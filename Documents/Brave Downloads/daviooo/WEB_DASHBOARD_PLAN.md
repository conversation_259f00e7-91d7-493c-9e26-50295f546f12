# 🌐 DAVIO WEB DASHBOARD - IMPLEMENTIERUNGSPLAN

## 📋 **PROJEKT ÜBERSICHT**

### **Ziel:**
Ein professionelles, real-time Web Dashboard für DAVIO Bot mit modernem Design und umfassenden Management-Features.

### **Technologie-Stack:**
- **Backend:** Node.js + Express.js + Socket.io
- **Frontend:** React.js + Material-UI + Chart.js
- **Database:** MongoDB (bereits vorhanden)
- **Authentication:** Discord OAuth2
- **Real-time:** WebSockets (Socket.io)

---

## 🏗️ **ARCHITEKTUR**

### **Ordnerstruktur:**
```
src/
├── dashboard/
│   ├── server/
│   │   ├── routes/
│   │   │   ├── auth.js
│   │   │   ├── api.js
│   │   │   ├── guilds.js
│   │   │   └── giveaways.js
│   │   ├── middleware/
│   │   │   ├── auth.js
│   │   │   └── rateLimit.js
│   │   ├── controllers/
│   │   │   ├── StatsController.js
│   │   │   ├── GiveawayController.js
│   │   │   └── ModerationController.js
│   │   └── app.js
│   ├── client/
│   │   ├── src/
│   │   │   ├── components/
│   │   │   ├── pages/
│   │   │   ├── hooks/
│   │   │   └── utils/
│   │   ├── public/
│   │   └── package.json
│   └── shared/
│       ├── types/
│       └── utils/
```

---

## 🚀 **PHASE 1: BACKEND SETUP**

### **1. Express Server Setup:**
```javascript
// src/dashboard/server/app.js
const express = require('express');
const cors = require('cors');
const session = require('express-session');
const MongoStore = require('connect-mongo');
const { Server } = require('socket.io');
const http = require('http');

class DashboardServer {
  constructor(bot) {
    this.bot = bot;
    this.app = express();
    this.server = http.createServer(this.app);
    this.io = new Server(this.server, {
      cors: {
        origin: process.env.FRONTEND_URL || "http://localhost:3000",
        credentials: true
      }
    });
    
    this.setupMiddleware();
    this.setupRoutes();
    this.setupSocketIO();
  }

  setupMiddleware() {
    this.app.use(cors({
      origin: process.env.FRONTEND_URL || "http://localhost:3000",
      credentials: true
    }));
    
    this.app.use(express.json());
    this.app.use(express.urlencoded({ extended: true }));
    
    this.app.use(session({
      secret: process.env.SESSION_SECRET,
      resave: false,
      saveUninitialized: false,
      store: MongoStore.create({
        mongoUrl: process.env.MONGODB_URI
      }),
      cookie: {
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
        secure: process.env.NODE_ENV === 'production'
      }
    }));
  }

  setupRoutes() {
    this.app.use('/auth', require('./routes/auth'));
    this.app.use('/api', require('./routes/api'));
    this.app.use('/api/guilds', require('./routes/guilds'));
    this.app.use('/api/giveaways', require('./routes/giveaways'));
  }

  start(port = 8080) {
    this.server.listen(port, () => {
      console.log(`🌐 Dashboard server running on port ${port}`);
    });
  }
}
```

### **2. Discord OAuth2 Authentication:**
```javascript
// src/dashboard/server/routes/auth.js
const express = require('express');
const passport = require('passport');
const DiscordStrategy = require('passport-discord').Strategy;

const router = express.Router();

passport.use(new DiscordStrategy({
  clientID: process.env.CLIENT_ID,
  clientSecret: process.env.CLIENT_SECRET,
  callbackURL: process.env.CALLBACK_URL,
  scope: ['identify', 'guilds']
}, (accessToken, refreshToken, profile, done) => {
  return done(null, profile);
}));

router.get('/discord', passport.authenticate('discord'));

router.get('/discord/callback', 
  passport.authenticate('discord', { failureRedirect: '/login' }),
  (req, res) => {
    res.redirect('/dashboard');
  }
);

router.get('/user', (req, res) => {
  if (req.user) {
    res.json(req.user);
  } else {
    res.status(401).json({ error: 'Not authenticated' });
  }
});

router.post('/logout', (req, res) => {
  req.logout();
  res.json({ success: true });
});

module.exports = router;
```

### **3. API Routes:**
```javascript
// src/dashboard/server/routes/api.js
const express = require('express');
const StatsController = require('../controllers/StatsController');
const { requireAuth } = require('../middleware/auth');

const router = express.Router();

// Server Statistics
router.get('/stats/:guildId', requireAuth, StatsController.getServerStats);
router.get('/stats/:guildId/commands', requireAuth, StatsController.getCommandStats);
router.get('/stats/:guildId/users', requireAuth, StatsController.getUserStats);

// Real-time data
router.get('/live/:guildId', requireAuth, (req, res) => {
  const { guildId } = req.params;
  const guild = req.app.locals.bot.guilds.cache.get(guildId);
  
  if (!guild) {
    return res.status(404).json({ error: 'Guild not found' });
  }

  res.json({
    memberCount: guild.memberCount,
    onlineCount: guild.members.cache.filter(m => m.presence?.status !== 'offline').size,
    channelCount: guild.channels.cache.size,
    roleCount: guild.roles.cache.size,
    botUptime: process.uptime()
  });
});

module.exports = router;
```

---

## 🎨 **PHASE 2: FRONTEND DEVELOPMENT**

### **1. React App Setup:**
```javascript
// src/dashboard/client/src/App.js
import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';

import Dashboard from './pages/Dashboard';
import Login from './pages/Login';
import GuildSelector from './pages/GuildSelector';
import { AuthProvider } from './contexts/AuthContext';
import { SocketProvider } from './contexts/SocketContext';

const theme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#5865F2', // Discord Blurple
    },
    secondary: {
      main: '#57F287', // Discord Green
    },
    background: {
      default: '#2C2F33',
      paper: '#36393F',
    },
  },
});

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AuthProvider>
        <SocketProvider>
          <Router>
            <Routes>
              <Route path="/login" element={<Login />} />
              <Route path="/guilds" element={<GuildSelector />} />
              <Route path="/dashboard/:guildId" element={<Dashboard />} />
              <Route path="/" element={<GuildSelector />} />
            </Routes>
          </Router>
        </SocketProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
```

### **2. Dashboard Components:**
```javascript
// src/dashboard/client/src/components/ServerStats.js
import React, { useState, useEffect } from 'react';
import { Card, CardContent, Typography, Grid, Box } from '@mui/material';
import { Line, Doughnut } from 'react-chartjs-2';
import { useSocket } from '../hooks/useSocket';

const ServerStats = ({ guildId }) => {
  const [stats, setStats] = useState(null);
  const socket = useSocket();

  useEffect(() => {
    // Fetch initial stats
    fetch(`/api/stats/${guildId}`)
      .then(res => res.json())
      .then(setStats);

    // Listen for real-time updates
    socket.on('statsUpdate', setStats);

    return () => socket.off('statsUpdate');
  }, [guildId, socket]);

  if (!stats) return <div>Loading...</div>;

  return (
    <Grid container spacing={3}>
      <Grid item xs={12} md={3}>
        <Card>
          <CardContent>
            <Typography variant="h6">Members</Typography>
            <Typography variant="h4">{stats.memberCount}</Typography>
            <Typography variant="body2" color="textSecondary">
              {stats.onlineCount} online
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      
      <Grid item xs={12} md={3}>
        <Card>
          <CardContent>
            <Typography variant="h6">Channels</Typography>
            <Typography variant="h4">{stats.channelCount}</Typography>
          </CardContent>
        </Card>
      </Grid>
      
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6">Command Usage (24h)</Typography>
            <Line data={stats.commandUsageChart} />
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
};

export default ServerStats;
```

### **3. Giveaway Management:**
```javascript
// src/dashboard/client/src/components/GiveawayManager.js
import React, { useState, useEffect } from 'react';
import {
  Card, CardContent, Typography, Button, Dialog,
  DialogTitle, DialogContent, TextField, Grid
} from '@mui/material';

const GiveawayManager = ({ guildId }) => {
  const [giveaways, setGiveaways] = useState([]);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [newGiveaway, setNewGiveaway] = useState({
    prize: '',
    duration: '',
    channel: '',
    winnerCount: 1
  });

  useEffect(() => {
    fetchGiveaways();
  }, [guildId]);

  const fetchGiveaways = async () => {
    const response = await fetch(`/api/giveaways/${guildId}`);
    const data = await response.json();
    setGiveaways(data);
  };

  const createGiveaway = async () => {
    const response = await fetch(`/api/giveaways/${guildId}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(newGiveaway)
    });

    if (response.ok) {
      setCreateDialogOpen(false);
      fetchGiveaways();
      setNewGiveaway({ prize: '', duration: '', channel: '', winnerCount: 1 });
    }
  };

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
        <Typography variant="h5">Giveaway Management</Typography>
        <Button 
          variant="contained" 
          onClick={() => setCreateDialogOpen(true)}
        >
          Create Giveaway
        </Button>
      </div>

      <Grid container spacing={2}>
        {giveaways.map(giveaway => (
          <Grid item xs={12} md={6} key={giveaway._id}>
            <Card>
              <CardContent>
                <Typography variant="h6">{giveaway.prize}</Typography>
                <Typography variant="body2">
                  Ends: {new Date(giveaway.endTime).toLocaleString()}
                </Typography>
                <Typography variant="body2">
                  Participants: {giveaway.participants.length}
                </Typography>
                <Button 
                  size="small" 
                  onClick={() => endGiveaway(giveaway._id)}
                  disabled={giveaway.ended}
                >
                  {giveaway.ended ? 'Ended' : 'End Now'}
                </Button>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Dialog open={createDialogOpen} onClose={() => setCreateDialogOpen(false)}>
        <DialogTitle>Create New Giveaway</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            margin="normal"
            label="Prize"
            value={newGiveaway.prize}
            onChange={(e) => setNewGiveaway({...newGiveaway, prize: e.target.value})}
          />
          <TextField
            fullWidth
            margin="normal"
            label="Duration (e.g., 1h, 30m, 1d)"
            value={newGiveaway.duration}
            onChange={(e) => setNewGiveaway({...newGiveaway, duration: e.target.value})}
          />
          <Button onClick={createGiveaway} variant="contained">
            Create
          </Button>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default GiveawayManager;
```

---

## 📊 **PHASE 3: REAL-TIME FEATURES**

### **Socket.io Integration:**
```javascript
// src/dashboard/server/socket.js
class SocketManager {
  constructor(io, bot) {
    this.io = io;
    this.bot = bot;
    this.setupEventListeners();
  }

  setupEventListeners() {
    this.io.on('connection', (socket) => {
      console.log('Dashboard client connected');

      socket.on('joinGuild', (guildId) => {
        socket.join(`guild_${guildId}`);
      });

      socket.on('leaveGuild', (guildId) => {
        socket.leave(`guild_${guildId}`);
      });
    });

    // Bot event listeners
    this.bot.on('guildMemberAdd', (member) => {
      this.io.to(`guild_${member.guild.id}`).emit('memberJoin', {
        user: member.user,
        joinedAt: member.joinedAt
      });
    });

    this.bot.on('messageCreate', (message) => {
      if (message.author.bot) return;
      
      this.io.to(`guild_${message.guild.id}`).emit('newMessage', {
        author: message.author.username,
        content: message.content,
        channel: message.channel.name,
        timestamp: message.createdAt
      });
    });
  }

  broadcastStats(guildId, stats) {
    this.io.to(`guild_${guildId}`).emit('statsUpdate', stats);
  }
}
```

---

## 🔒 **PHASE 4: SICHERHEIT & OPTIMIERUNG**

### **Security Middleware:**
```javascript
// src/dashboard/server/middleware/auth.js
const requireAuth = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({ error: 'Authentication required' });
  }
  next();
};

const requireGuildAccess = async (req, res, next) => {
  const { guildId } = req.params;
  const userGuilds = req.user.guilds || [];
  
  const hasAccess = userGuilds.some(guild => 
    guild.id === guildId && 
    (guild.permissions & 0x8) === 0x8 // Administrator permission
  );

  if (!hasAccess) {
    return res.status(403).json({ error: 'Access denied' });
  }
  
  next();
};

module.exports = { requireAuth, requireGuildAccess };
```

### **Rate Limiting:**
```javascript
// src/dashboard/server/middleware/rateLimit.js
const rateLimit = require('express-rate-limit');

const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP'
});

const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 5, // limit each IP to 5 auth requests per windowMs
  skipSuccessfulRequests: true
});

module.exports = { apiLimiter, authLimiter };
```

---

## 📱 **PHASE 5: MOBILE RESPONSIVENESS**

### **Responsive Design:**
```javascript
// src/dashboard/client/src/components/ResponsiveLayout.js
import React from 'react';
import { useMediaQuery, useTheme } from '@mui/material';
import { Drawer, AppBar, Toolbar, Typography } from '@mui/material';

const ResponsiveLayout = ({ children }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  return (
    <div style={{ display: 'flex' }}>
      {!isMobile && (
        <Drawer variant="permanent" sx={{ width: 240 }}>
          {/* Navigation */}
        </Drawer>
      )}
      
      <main style={{ flexGrow: 1 }}>
        <AppBar position="fixed">
          <Toolbar>
            <Typography variant="h6">DAVIO Dashboard</Typography>
          </Toolbar>
        </AppBar>
        
        <div style={{ marginTop: 64, padding: 16 }}>
          {children}
        </div>
      </main>
    </div>
  );
};

export default ResponsiveLayout;
```

---

## 🚀 **DEPLOYMENT PLAN**

### **Production Setup:**
```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY src/dashboard/server/package*.json ./server/
COPY src/dashboard/client/package*.json ./client/

# Install dependencies
RUN npm install
RUN cd server && npm install
RUN cd client && npm install

# Copy source code
COPY . .

# Build frontend
RUN cd client && npm run build

# Expose port
EXPOSE 8080

# Start server
CMD ["npm", "run", "start:dashboard"]
```

### **Environment Variables:**
```env
# .env.production
NODE_ENV=production
PORT=8080
MONGODB_URI=mongodb://localhost:27017/davio
SESSION_SECRET=your-secret-key
CLIENT_ID=your-discord-client-id
CLIENT_SECRET=your-discord-client-secret
CALLBACK_URL=https://yourdomain.com/auth/discord/callback
FRONTEND_URL=https://yourdomain.com
```

---

## 📈 **TIMELINE**

### **Woche 1-2: Backend Foundation**
- Express server setup
- Discord OAuth2 integration
- Basic API routes
- Database integration

### **Woche 3-4: Frontend Development**
- React app setup
- Authentication flow
- Basic dashboard components
- Server statistics display

### **Woche 5-6: Advanced Features**
- Real-time updates
- Giveaway management
- Moderation panel
- Command analytics

### **Woche 7-8: Polish & Deploy**
- Mobile responsiveness
- Security hardening
- Performance optimization
- Production deployment

**Das Dashboard wird DAVIO zum ultimativen Discord Bot machen! 🌐✨**
