require('dotenv').config();
const { Client, GatewayIntentBits, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
// Removed djs-slash-handler - using custom command loader
const Giveaway = require('./src/database/models/Giveaway');
const { deployCommands } = require('./src/deployCommands');

// 🎨 PROFESSIONAL SYSTEM IMPORTS
const logger = require('./src/utils/logger');
const errorHandler = require('./src/utils/errorHandler');
const performanceMonitor = require('./src/utils/performanceMonitor');
const databaseManager = require('./src/database/connection');
const InteractionHandler = require('./src/handlers/interactionHandler');

// 🎨 INITIALIZE PROFESSIONAL SYSTEMS
logger.header();
logger.startup('Initializing DAVIO Bot System v3.0...');

// Initialize database connection
logger.info('Connecting to database...');
databaseManager.connect().catch(error => {
  logger.error('Database connection failed', error.message);
  process.exit(1);
});

const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.MessageContent,
    GatewayIntentBits.GuildVoiceStates,
    GatewayIntentBits.GuildMembers,
    GatewayIntentBits.GuildPresences,
    GatewayIntentBits.GuildMessageReactions
  ]
});

logger.success('Discord Client initialized with enhanced intents');

// Initialize interaction handler
const interactionHandler = new InteractionHandler(client);
logger.success('Advanced interaction handler initialized');

// Setup error handling for Discord client
client.on('error', (error) => {
  errorHandler.handleDiscordError(error, { source: 'Discord Client' });
});

client.on('warn', (warning) => {
  logger.warning('Discord Client Warning', warning);
});

logger.info('Loading commands...');

// Load commands manually
const fs = require('fs');
const path = require('path');

client.commands = new Map();

function loadCommands(dir) {
  const commandFiles = fs.readdirSync(dir);

  for (const file of commandFiles) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      loadCommands(filePath);
    } else if (file.endsWith('.js') && file !== 'index.js') {
      try {
        const command = require(filePath);
        if (command.data && command.execute) {
          client.commands.set(command.data.name, command);
          logger.info(`Loaded command: ${command.data.name}`);
        }
      } catch (error) {
        logger.error(`Failed to load command ${file}:`, error.message);
      }
    }
  }
}

// Load commands from all categories
const commandsPath = path.join(__dirname, 'src', 'commands');
loadCommands(commandsPath);

logger.success(`Command handler loaded successfully (${client.commands.size} commands)`);
// Handle interactions
client.on('interactionCreate', async interaction => {
  if (!interaction.isChatInputCommand()) return;

  const command = client.commands.get(interaction.commandName);
  if (!command) return;

  try {
    await command.execute(interaction);
    logger.command(interaction.user.username, interaction.commandName, interaction.guild?.name);
  } catch (error) {
    logger.error(`Command error [${interaction.commandName}]:`, error.message);

    const errorEmbed = {
      title: '❌ Command Error',
      description: 'An error occurred while executing this command.',
      color: 0xff0000,
      timestamp: new Date().toISOString()
    };

    if (interaction.replied || interaction.deferred) {
      await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
    } else {
      await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
    }
  }
});

logger.startup('Connecting to Discord...');

client.login(process.env.TOKEN);

  // helper: ping a user in admin channel with beautiful console logs
  async function pingUser(userId, msg) {
    try {
      const user = await client.users.fetch(userId);
      const channel = client.channels.cache.get('1307942654067216465');
      if (user && channel) {
        await channel.send(`${user}, ${msg}`);

        // 🎨 BEAUTIFUL PING CONSOLE LOG
        await sendEmbed('📢⎮Admin Ping', `📢 Pinged user: ${user.tag}`, '#FFA500', [
          { name: 'User', value: user.tag, inline: true },
          { name: 'Message', value: msg.substring(0, 30) + '...', inline: true },
          { name: 'Time', value: new Date().toLocaleString(), inline: true }
        ]);
      }
    } catch(err) {
      // 🎨 BEAUTIFUL ERROR CONSOLE LOG
      await sendEmbed('🚨⎮Ping Error', `📢 Error pinging user: ${err.message}`, '#FF0000', [
        { name: 'User ID', value: userId, inline: true },
        { name: 'Error', value: err.name || 'Unknown', inline: true },
        { name: 'Time', value: new Date().toLocaleString(), inline: true }
      ]);
    }
  }

  let previousStatus;
  let startTime;

  client.once('ready', async () => {
    startTime = Date.now();

    // 🎉 BEAUTIFUL READY MESSAGE WITH CONSOLE LOGS
    const guildCount = client.guilds.cache.size;
    const userCount = client.guilds.cache.reduce((acc, guild) => acc + guild.memberCount, 0);

    // 🎨 BEAUTIFUL CONSOLE READY MESSAGE
    console.log('\n╔══════════════════════════════════════════════════════════════╗');
    console.log('║                    🎉 BOT READY & ONLINE                    ║');
    console.log('╠══════════════════════════════════════════════════════════════╣');
    console.log(`║ 🤖 Bot: ${client.user.tag.padEnd(47)} ║`);
    console.log(`║ 🏰 Servers: ${guildCount.toString().padEnd(44)} ║`);
    console.log(`║ 👥 Users: ${userCount.toString().padEnd(46)} ║`);
    console.log(`║ 🕐 Started: ${new Date().toLocaleString().padEnd(42)} ║`);
    console.log('╚══════════════════════════════════════════════════════════════╝\n');

    logger.ready(client.user.tag, guildCount, userCount);
    logger.success('Bot successfully connected to Discord');

    // Deploy commands with console logs
    logger.info('Deploying slash commands...');
    deployCommands();
    logger.success('Slash commands deployed');

    // Check for unfinished giveaways with beautiful console logs
    logger.info('Checking for ongoing giveaways...');
    try {
      const ongoing = await Giveaway.find({ ended: false });
      if (ongoing.length > 0) {
        logger.info(`Found ${ongoing.length} ongoing giveaways`);

        // 🎨 BEAUTIFUL GIVEAWAY CONSOLE LOG
        await sendEmbed('🎉⎮Giveaways Found', `📢 Found ${ongoing.length} ongoing giveaways to resume`, '#00FF00', [
          { name: 'Count', value: ongoing.length.toString(), inline: true },
          { name: 'Status', value: 'Resuming...', inline: true },
          { name: 'Time', value: new Date().toLocaleString(), inline: true }
        ]);

        ongoing.forEach(g => {
          const remaining = g.endsAt - Date.now();
          if (remaining > 0) setTimeout(() => endGiveaway(g, client), remaining);
          else endGiveaway(g, client);
        });
      } else {
        logger.info('No ongoing giveaways found');

        // 🎨 BEAUTIFUL NO GIVEAWAYS CONSOLE LOG
        await sendEmbed('✅⎮Giveaways Check', '📢 No ongoing giveaways found - All clear!', '#00FF00', [
          { name: 'Status', value: 'Clean', inline: true },
          { name: 'Time', value: new Date().toLocaleString(), inline: true }
        ]);
      }
    } catch (error) {
      // 🎨 BEAUTIFUL ERROR CONSOLE LOG
      await sendEmbed('🚨⎮Giveaway Error', `📢 Error checking giveaways: ${error.message}`, '#FF0000', [
        { name: 'Error', value: error.name || 'Unknown', inline: true },
        { name: 'Time', value: new Date().toLocaleString(), inline: true }
      ]);
    }

    if (previousStatus !== 'ready') {
      const total = guildCount;
      await pingUser('1182064854006251520', `🟢⎮Shard ${client.shard.ids[0]} is now back online! 📤`);
      await sendEmbed(
        `🟢⎮Shard ${client.shard.ids[0]} is now back online! 📤`,
        '_The bot has restarted and is now ready to use on your server! 🚀_',
        '#00FF00',
        [
          { name: 'Time', value: new Date().toLocaleString(), inline: true },
          { name: 'Total Servers', value: total.toString(), inline: true },
          { name: 'Shard ID', value: client.shard.ids[0].toString(), inline: true },
          { name: 'Version', value: '1.0.0', inline: true },
        ]
      );
    }
    previousStatus = 'ready';
  });

  client.on('shutdown', async () => {
    const uptime = Date.now() - startTime;
    await pingUser('1182064854006251520', `🔴⎮Shard ${client.shard.ids[0]} is now offline! 🔴`);
    await sendEmbed(
      `🔴⎮Shard ${client.shard.ids[0]} is now offline! 🔴`,
      '⚠⎮The bot has lost connection. We are working to bring it back online!⚠',
      '#FF0000',
      [
        { name: 'Time', value: new Date().toLocaleString(), inline: true },
        { name: 'Uptime', value: formatUptime(uptime), inline: true },
        { name: 'Reason', value: 'Manual shutdown', inline: true },
        { name: 'Shard ID', value: client.shard.ids[0].toString(), inline: true },
      ]
    );
    setTimeout(() => process.exit(0), 2000);
  });

  client.on('reconnecting', async () => {
    previousStatus = 'reconnecting';
    await sendEmbed(`🔄⎮Shard ${client.shard.ids[0]} is reconnecting! 🔄`, '🔂 The bot is reconnecting...', '#FFFF00');
  });

  client.on('resume', async () => {
    if (previousStatus !== 'ready') await sendEmbed(`🟢⎮Shard ${client.shard.ids[0]} is back online! 🟢`, '🚀 The bot has successfully reconnected!', '#00FF00');
    previousStatus = 'ready';
  });

  // 🎨 BEAUTIFUL CONSOLE LOGS - ACADEMIC EXCELLENCE
  async function sendEmbed(title, description, color, fields = []) {
    console.log(`\n╔══════════════════════════════════════════════════════════════╗`);
    console.log(`║ ${title.padEnd(60)} ║`);
    console.log(`╠══════════════════════════════════════════════════════════════╣`);
    console.log(`║ ${description.padEnd(60)} ║`);
    if (fields.length > 0) {
      console.log(`╠══════════════════════════════════════════════════════════════╣`);
      fields.forEach(field => {
        console.log(`║ ${field.name}: ${field.value.toString().padEnd(50)} ║`);
      });
    }
    console.log(`╚══════════════════════════════════════════════════════════════╝\n`);
  }

  client.on('error', async error => await sendEmbed('⚠⎮Error detected!', `📢 System message: ${error.message}`, '#FFA500', [{ name: 'Time', value: new Date().toLocaleString(), inline: true }]));

  // 🎨 BEAUTIFUL SHUTDOWN AND ERROR HANDLING
  client.on('shutdown', async () => {
    await sendEmbed('🔄⎮Bot Shutdown', '📢 System message: Bot is shutting down gracefully...', '#FF6B6B', [{ name: 'Time', value: new Date().toLocaleString(), inline: true }]);
    process.exit(0);
  });

  process.on('SIGINT', () => client.emit('shutdown'));
  process.on('SIGTERM', () => client.emit('shutdown'));
  process.on('uncaughtExceptionMonitor', (err, origin) => {
    sendEmbed('🚨⎮Exception Monitor', `📢 System message: ${err.message}`, '#FF0000', [
      { name: 'Origin', value: origin, inline: true },
      { name: 'Time', value: new Date().toLocaleString(), inline: true }
    ]);
  });
  process.on('uncaughtException', err => {
    sendEmbed('💥⎮Uncaught Exception', `📢 Critical error: ${err.message}`, '#FF0000', [
      { name: 'Stack', value: err.stack?.substring(0, 50) + '...', inline: false },
      { name: 'Time', value: new Date().toLocaleString(), inline: true }
    ]);
  });
  process.on('unhandledRejection', (reason, promise) => {
    sendEmbed('⚠️⎮Unhandled Rejection', `📢 Promise rejection: ${reason}`, '#FFA500', [
      { name: 'Promise', value: promise.toString().substring(0, 50) + '...', inline: false },
      { name: 'Time', value: new Date().toLocaleString(), inline: true }
    ]);
  });

  client.login(process.env.TOKEN);

async function endGiveaway(giveaway, client) {
  if (giveaway.ended) return;
  try {
    const updated = await Giveaway.findById(giveaway._id);
    if (!updated) return;
    updated.ended = true;
    const channel = await client.channels.fetch(updated.channelId);
    const message = await channel.messages.fetch(updated.messageId);

    let endEmbed = new EmbedBuilder().setColor(0x808080).setTitle('🎉 Giveaway Ended! 🎉');
    let disabledButtons;

    if (updated.participants.length < 2) {
      endEmbed.setDescription(`**Prize**: ${updated.prize}\n**Status**: Cancelled - Not enough participants`);
      disabledButtons = new ActionRowBuilder().addComponents(
        new ButtonBuilder().setCustomId('giveaway-ended').setLabel('Giveaway Ended').setEmoji('❌').setStyle(ButtonStyle.Secondary).setDisabled(true),
        new ButtonBuilder().setCustomId('giveaway-info').setLabel('Info').setEmoji('ℹ️').setStyle(ButtonStyle.Secondary).setDisabled(true)
      );
      await message.edit({ embeds: [endEmbed], components: [disabledButtons] });
      await message.reply('❌ Giveaway cancelled: Not enough participants.');

      // 🎨 BEAUTIFUL CONSOLE LOG FOR CANCELLED GIVEAWAY
      await sendEmbed('❌⎮Giveaway Cancelled', `📢 Prize: ${updated.prize} - Not enough participants`, '#FF6B6B', [
        { name: 'Participants', value: updated.participants.length.toString(), inline: true },
        { name: 'Required', value: '2', inline: true },
        { name: 'Time', value: new Date().toLocaleString(), inline: true }
      ]);
    } else {
      const winners = selectWinners(updated.participants, updated.winnerCount);
      updated.winnerIds = winners;
      await updated.save();
      endEmbed.setDescription(`**Prize**: ${updated.prize}\n**Winners**: ${winners.map(id => `<@${id}>`).join(', ')}`);
      disabledButtons = new ActionRowBuilder().addComponents(
        new ButtonBuilder().setCustomId('giveaway-ended').setLabel('Giveaway Ended').setEmoji('🎉').setStyle(ButtonStyle.Secondary).setDisabled(true),
        new ButtonBuilder().setCustomId('giveaway-info').setLabel('Info').setEmoji('ℹ️').setStyle(ButtonStyle.Secondary).setDisabled(true)
      );
      await message.edit({ embeds: [endEmbed], components: [disabledButtons] });
      const winMsg = await message.reply({ content: `🎉 Congratulations ${winners.map(id => `<@${id}>`).join(', ')}!`, allowedMentions: { users: winners } });
      await winMsg.pin().catch(() => {});

      // 🎨 BEAUTIFUL CONSOLE LOG FOR SUCCESSFUL GIVEAWAY
      await sendEmbed('🎉⎮Giveaway Completed', `📢 Prize: ${updated.prize} - Winners selected!`, '#00FF00', [
        { name: 'Winners', value: winners.length.toString(), inline: true },
        { name: 'Participants', value: updated.participants.length.toString(), inline: true },
        { name: 'Time', value: new Date().toLocaleString(), inline: true }
      ]);
    }
  } catch (err) {
    // 🎨 BEAUTIFUL ERROR LOG
    await sendEmbed('🚨⎮Giveaway Error', `📢 Error ending giveaway: ${err.message}`, '#FF0000', [
      { name: 'Error Type', value: err.name || 'Unknown', inline: true },
      { name: 'Time', value: new Date().toLocaleString(), inline: true }
    ]);
  }
}

function selectWinners(participants, count) {
  const uniq = [...new Set(participants)];
  const winners = [];
  while (winners.length < count && uniq.length) {
    const i = Math.floor(Math.random() * uniq.length);
    winners.push(uniq.splice(i, 1)[0]);
  }
  return winners;
}

// Remove the duplicate client.login() at the end of the file