"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommandHandler = void 0;
const CommandHandler_1 = __importDefault(require("./structures/CommandHandler"));
exports.CommandHandler = CommandHandler_1.default;
exports.default = CommandHandler_1.default;
