"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
class SubCommandGroup {
    constructor(name, description, subCommands) {
        this.name = name;
        this.description = description;
        this.subCommands = subCommands;
    }
    get json() {
        return {
            name: this.name,
            description: this.description,
            type: 2,
            options: [...this.subCommands.map((subCommand) => subCommand.json)],
        };
    }
}
exports.default = SubCommandGroup;
