"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
class SubCommand {
    constructor(name, data) {
        SubCommand.validate(name, data);
        this.name = name;
        this.description = data.description;
        this.options = data.options;
        this.callback = data.callback;
    }
    get json() {
        return {
            name: this.name,
            description: this.description,
            type: 1,
            options: this.options,
        };
    }
    static validate(name, data) {
        if (typeof data.description !== "string")
            throw new TypeError(`SubCommand description must be a string: ${name}`);
    }
}
exports.default = SubCommand;
