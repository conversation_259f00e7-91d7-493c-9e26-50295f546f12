"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SubCommandGroup = exports.SubCommand = exports.Command = void 0;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const Command_1 = __importDefault(require("./Command"));
exports.Command = Command_1.default;
const SubCommand_1 = __importDefault(require("./SubCommand"));
exports.SubCommand = SubCommand_1.default;
const SubCommandGroup_1 = __importDefault(require("./SubCommandGroup"));
exports.SubCommandGroup = SubCommandGroup_1.default;
class CommandHandler {
    constructor(options, load) {
        CommandHandler.validateOptions(options);
        this.options = options;
        this.commands = new Map();
        if (load)
            this.loadCommands();
    }
    /**
     * Load commands from specified path
     */
    loadCommands() {
        var _a;
        const categories = fs_1.default.readdirSync(path_1.default.join(process.cwd(), this.options.commandsDir));
        for (const category of categories) {
            if ((_a = this.options.disabledCategories) === null || _a === void 0 ? void 0 : _a.includes(category))
                continue;
            const categoryCommands = fs_1.default.readdirSync(path_1.default.join(process.cwd(), this.options.commandsDir, category));
            // iterate over commands in a category
            for (const categoryCommand of categoryCommands) {
                const stat = fs_1.default.lstatSync(path_1.default.join(process.cwd(), this.options.commandsDir, category, categoryCommand));
                // command without subcommands/subcommand-groups
                if (!stat.isDirectory()) {
                    let cmdObj = require(path_1.default.join(process.cwd(), this.options.commandsDir, category, categoryCommand));
                    if (cmdObj.default)
                        cmdObj = cmdObj.default;
                    if ((cmdObj === null || cmdObj === void 0 ? void 0 : cmdObj.enabled) === false)
                        continue;
                    const fileName = categoryCommand.split(".")[0];
                    const command = new Command_1.default(fileName, cmdObj, [], []);
                    this.commands.set(fileName, command);
                }
                // command with subcommands/subcommand-groups
                else {
                    let _defaultCmdData;
                    try {
                        _defaultCmdData = require(path_1.default.join(process.cwd(), this.options.commandsDir, category, categoryCommand, "index.js"));
                    }
                    catch (err) {
                        // default command metadata
                        _defaultCmdData = {
                            enabled: true,
                            description: `description for ${categoryCommand}`,
                            aliases: [],
                            ephemeral: false,
                        };
                    }
                    const subFolders = fs_1.default.readdirSync(path_1.default.join(process.cwd(), this.options.commandsDir, category, categoryCommand));
                    const subCommandsArray = [];
                    const subCommandGroupsArray = [];
                    // iterate over subcommands/subcommand-groups
                    for (const sub of subFolders) {
                        const stat = fs_1.default.lstatSync(path_1.default.join(process.cwd(), this.options.commandsDir, category, categoryCommand, sub));
                        // only subcommands
                        if (!stat.isDirectory() && sub != "index.js") {
                            const subCmdObj = require(path_1.default.join(process.cwd(), this.options.commandsDir, category, categoryCommand, sub));
                            if ((subCmdObj === null || subCmdObj === void 0 ? void 0 : subCmdObj.enabled) === false)
                                continue;
                            const fileName = sub.split(".")[0];
                            subCommandsArray.push(new SubCommand_1.default(fileName, subCmdObj));
                        }
                        // subcommand-groups
                        else if (stat.isDirectory()) {
                            const subcommandGroup = fs_1.default.readdirSync(path_1.default.join(process.cwd(), this.options.commandsDir, category, categoryCommand, sub));
                            const innerSubCommandsArray = [];
                            let _defaultCmdGroupData;
                            try {
                                _defaultCmdGroupData = require(path_1.default.join(process.cwd(), this.options.commandsDir, category, categoryCommand, sub, "index.js"));
                            }
                            catch (err) {
                                // default command metadata
                                _defaultCmdGroupData = {
                                    description: `description for ${sub}`,
                                };
                            }
                            for (const subCommand of subcommandGroup) {
                                const subCmdObj = require(path_1.default.join(process.cwd(), this.options.commandsDir, category, categoryCommand, sub, subCommand));
                                if ((subCmdObj === null || subCmdObj === void 0 ? void 0 : subCmdObj.enabled) === false)
                                    continue;
                                const fileName = subCommand.split(".")[0];
                                innerSubCommandsArray.push(new SubCommand_1.default(fileName, subCmdObj));
                            }
                            subCommandGroupsArray.push(new SubCommandGroup_1.default(sub, _defaultCmdGroupData.description, innerSubCommandsArray));
                        }
                    }
                    const cmdObj = new Command_1.default(categoryCommand, _defaultCmdData, subCommandsArray, subCommandGroupsArray);
                    this.commands.set(categoryCommand, cmdObj);
                }
            }
        }
    }
    getCommandsJSON() {
        const toRegister = [];
        for (const command of this.commands.values()) {
            toRegister.push(command.json);
        }
        return toRegister;
    }
    getCommandCallBack(interaction) {
        var _a, _b, _c;
        const commandName = interaction.commandName;
        const command = this.commands.get(commandName);
        if (!command)
            return console.log("Command not found");
        if (command.subCommands.length === 0 && command.subCommandGroups.length === 0)
            return command.callback;
        const subCommandGroup = interaction.options.getSubcommandGroup();
        const subCommand = interaction.options.getSubcommand();
        if (subCommandGroup) {
            return (_b = (_a = command.subCommandGroups.find((x) => x.name === subCommandGroup)) === null || _a === void 0 ? void 0 : _a.subCommands.find((x) => x.name === subCommand)) === null || _b === void 0 ? void 0 : _b.callback;
        }
        else {
            return (_c = command.subCommands.find((x) => x.name === subCommand)) === null || _c === void 0 ? void 0 : _c.callback;
        }
    }
    handleInteraction(interaction) {
        return __awaiter(this, void 0, void 0, function* () {
            const fn = this.getCommandCallBack(interaction);
            if (!fn)
                return console.log("Command not found");
            yield fn(interaction);
        });
    }
    static validateOptions(options) {
        if (!options)
            throw new Error("No options provided");
        if (!options.commandsDir)
            throw new Error("No path for commands provided");
        if (options.disabledCategories) {
            if (!Array.isArray(options.disabledCategories))
                throw new Error("disabledCategories must be an array");
            if (options.disabledCategories.find((x) => typeof x !== "string"))
                throw new Error("disabledCategories must be an array of strings");
        }
    }
}
exports.default = CommandHandler;
