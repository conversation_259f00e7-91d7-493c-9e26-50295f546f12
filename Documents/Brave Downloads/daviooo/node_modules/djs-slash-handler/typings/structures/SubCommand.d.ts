import { ChatInputCommandInteraction, ApplicationCommandOptionData } from "discord.js";
export declare type SubCommandData = {
    name: string;
    description: string;
    options: ApplicationCommandOptionData[];
    callback: (interaction: ChatInputCommandInteraction) => any;
};
export default class SubCommand {
    name: string;
    description: string;
    options: ApplicationCommandOptionData[];
    callback: (interaction: ChatInputCommandInteraction) => any;
    constructor(name: string, data: SubCommandData);
    get json(): {
        name: string;
        description: string;
        type: number;
        options: ApplicationCommandOptionData[];
    };
    static validate(name: string, data: SubCommandData): void;
}
