import SubCommand from "./SubCommand";
export default class SubCommandGroup {
    name: string;
    description: string;
    subCommands: SubCommand[];
    constructor(name: string, description: string, subCommands: SubCommand[]);
    get json(): {
        name: string;
        description: string;
        type: number;
        options: {
            name: string;
            description: string;
            type: number;
            options: import("discord.js").ApplicationCommandOptionData[];
        }[];
    };
}
