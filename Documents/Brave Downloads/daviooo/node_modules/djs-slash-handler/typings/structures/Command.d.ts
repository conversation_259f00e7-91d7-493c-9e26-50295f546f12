import { ApplicationCommandOptionData, ChatInputCommandInteraction } from "discord.js";
import SubCommand from "./SubCommand";
import SubCommandGroup from "./SubCommandGroup";
export declare type CommandData = {
    description: string;
    options: ApplicationCommandOptionData[];
    callback?: (interaction: ChatInputCommandInteraction) => any;
};
export default class Command {
    name: string;
    description: string;
    options: ApplicationCommandOptionData[];
    subCommands: SubCommand[];
    subCommandGroups: SubCommandGroup[];
    callback?: (interaction: ChatInputCommandInteraction) => any;
    constructor(name: string, data: CommandData, subCommands: SubCommand[], subCommandGroups: SubCommandGroup[]);
    get json(): {
        name: string;
        description: string;
        options: ApplicationCommandOptionData[] | ({
            name: string;
            description: string;
            type: number;
            options: ApplicationCommandOptionData[];
        } | {
            name: string;
            description: string;
            type: number;
            options: {
                name: string;
                description: string;
                type: number;
                options: ApplicationCommandOptionData[];
            }[];
        })[];
    };
    static validate(name: string, containsSubCommand: boolean, data: CommandData): void;
}
