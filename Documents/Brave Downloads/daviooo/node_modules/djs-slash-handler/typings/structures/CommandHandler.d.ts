import Command from "./Command";
import SubCommand from "./SubCommand";
import SubCommandGroup from "./SubCommandGroup";
import { ChatInputCommandInteraction } from "discord.js";
declare type CommandHandlerOptions = {
    commandsDir: string;
    disabledCategories?: string[];
};
declare class CommandHandler {
    private options;
    commands: Map<string, Command>;
    constructor(options: CommandHandlerOptions, load: boolean);
    /**
     * Load commands from specified path
     */
    loadCommands(): void;
    getCommandsJSON(): {
        name: string;
        description: string;
        options: import("discord.js").ApplicationCommandOptionData[] | ({
            name: string;
            description: string;
            type: number;
            options: import("discord.js").ApplicationCommandOptionData[];
        } | {
            name: string;
            description: string;
            type: number;
            options: {
                name: string;
                description: string;
                type: number;
                options: import("discord.js").ApplicationCommandOptionData[];
            }[];
        })[];
    }[];
    getCommandCallBack(interaction: ChatInputCommandInteraction): void | ((interaction: ChatInputCommandInteraction<import("discord.js").CacheType>) => any);
    handleInteraction(interaction: ChatInputCommandInteraction): Promise<void>;
    static validateOptions(options: CommandHandlerOptions): void;
}
export default CommandHandler;
export { Command, SubCommand, SubCommandGroup };
