{"name": "discord-aka<PERSON>", "version": "8.1.0", "description": "A highly customizable bot framework for Discord.js.", "main": "./src/index.js", "types": "./src/index.d.ts", "author": "1Computer", "license": "MIT", "keywords": ["discord", "discord-js", "discord.js", "framework", "bot", "client", "modular", "commands", "arguments"], "dependencies": {}, "devDependencies": {"@types/node": "^10.14.4", "discord.js-docgen": "github:discordjs/docgen", "eslint": "^5.16.0", "jsdoc": "^3.6.4", "tslint": "^5.15.0", "tslint-config-typings": "^0.3.1", "typescript": "^3.4.2"}, "scripts": {"test": "npm run lint", "lint": "eslint ./src && tslint ./src/index.d.ts"}, "repository": {"type": "git", "url": "https://github.com/discord-akairo/discord-akairo.git"}, "bugs": {"url": "https://github.com/discord-akairo/discord-akairo/issues"}, "homepage": "https://github.com/discord-akairo/discord-akairo"}