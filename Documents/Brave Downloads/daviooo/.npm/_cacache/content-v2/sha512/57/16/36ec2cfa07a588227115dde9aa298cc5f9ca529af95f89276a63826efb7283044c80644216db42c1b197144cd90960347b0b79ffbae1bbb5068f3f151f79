{"_id": "gauge", "_rev": "98-e748a044ceb40e6bf6d8dcf9e83619f1", "name": "gauge", "dist-tags": {"latest": "5.0.2"}, "versions": {"1.0.0": {"name": "gauge", "version": "1.0.0", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "gauge@1.0.0", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/gauge", "bugs": {"url": "https://github.com/iarna/gauge/issues"}, "dist": {"shasum": "5682aefa9ba0f7042f652cff50e4c7fc0e592aa4", "tarball": "https://registry.npmjs.org/gauge/-/gauge-1.0.0.tgz", "integrity": "sha512-CoXRwwrwtdg9sr2PUb8PLFw5uHvwmxS3RuMvTf7GbxNWUUhg+DGRSiiSidPmszCtIDzK902DDrEMmyAdC98mEA==", "signatures": [{"sig": "MEUCIGDWwhr2BmViZtHAOabJfoudzGsT+K/T/Ym9SdsEZFZbAiEAwDJ3KSMZA5OF4TknqWIg4R5VzgdoBJKUuXOymZ7hdYg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "progress-bar.js", "_from": ".", "_shasum": "5682aefa9ba0f7042f652cff50e4c7fc0e592aa4", "gitHead": "d0bb39c32c1e12ac80f789bc7ba5efd8049e1502", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "https://github.com/iarna/gauge", "type": "git"}, "_npmVersion": "2.1.11", "description": "A terminal based horizontal guage", "directories": {}, "_nodeVersion": "0.10.33", "dependencies": {"ansi": "^0.3.0", "has-unicode": "^1.0.0"}, "devDependencies": {"tap": "^0.4.13"}}, "1.0.1": {"name": "gauge", "version": "1.0.1", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "gauge@1.0.1", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/gauge", "bugs": {"url": "https://github.com/iarna/gauge/issues"}, "dist": {"shasum": "2f3849afd25e8f3318838bda11ec5501f32e8789", "tarball": "https://registry.npmjs.org/gauge/-/gauge-1.0.1.tgz", "integrity": "sha512-mZvqLtabyXcCtc2No6nEe/Pg7jXxF1gVlebbnESkpWdLUw3QflwEL00W1GBoGvPlBfWJ1PhxGe/oN0tFoM8DTw==", "signatures": [{"sig": "MEUCIQCv47CMmmE519nA542EZepbijhNY09b9OThcEMUBLMqawIgI/l6UW9LOJwVjuUD4gZ61XWFAiGY6dglD2l0LSSUTJI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "progress-bar.js", "_from": ".", "_shasum": "2f3849afd25e8f3318838bda11ec5501f32e8789", "gitHead": "d5d78e839430edd650cd8630da6432bf6e22dc13", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "https://github.com/iarna/gauge", "type": "git"}, "_npmVersion": "2.1.11", "description": "A terminal based horizontal guage", "directories": {}, "_nodeVersion": "0.10.33", "dependencies": {"ansi": "^0.3.0", "has-unicode": "^1.0.0"}, "devDependencies": {"tap": "^0.4.13"}}, "1.0.2": {"name": "gauge", "version": "1.0.2", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "gauge@1.0.2", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/gauge", "bugs": {"url": "https://github.com/iarna/gauge/issues"}, "dist": {"shasum": "53e25965dfaf1c85be3a2a0633306a24a67dc2f9", "tarball": "https://registry.npmjs.org/gauge/-/gauge-1.0.2.tgz", "integrity": "sha512-s78esK8iq5uapI23iZrZuak37O91rgchsp3fwetfgH32jzmaNKNmUn39J6CbOmL4NOVHynYrukvgcCXkILpo+g==", "signatures": [{"sig": "MEUCIQDzYaM20EV3lCTpTVIBjpANw+LhmGBxOjRInB5N201BTgIgR27oXgSgZQrHqioOBJ9U700reloysGbOo2s1ahP/oj8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "progress-bar.js", "_from": ".", "_shasum": "53e25965dfaf1c85be3a2a0633306a24a67dc2f9", "gitHead": "899cd353e5204203b722edda47ad4e55294a1ae8", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "https://github.com/iarna/gauge", "type": "git"}, "_npmVersion": "2.1.11", "description": "A terminal based horizontal guage", "directories": {}, "_nodeVersion": "0.10.33", "dependencies": {"ansi": "^0.3.0", "has-unicode": "^1.0.0"}, "devDependencies": {"tap": "^0.4.13"}}, "1.1.0": {"name": "gauge", "version": "1.1.0", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "gauge@1.1.0", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/gauge", "bugs": {"url": "https://github.com/iarna/gauge/issues"}, "dist": {"shasum": "4f1c13cb6232469f65de92357b34f8ff53c5ca41", "tarball": "https://registry.npmjs.org/gauge/-/gauge-1.1.0.tgz", "integrity": "sha512-LXfvUORjKITx2J6y3UT3J73AnCEyVrxLOOyQRVlhjVX3InocRju56IvMXtCffKdC+uf7L4olKnyZQIhIeJJvVQ==", "signatures": [{"sig": "MEUCIFJXDZG7q6YFAcEARw/MJAKsFTfgtr8KDdzxNYXamQKIAiEAsFoOJYTHWldelA9jOYnDNIjWHhfAN7vkfWyjn/nlgzU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "progress-bar.js", "_from": ".", "_shasum": "4f1c13cb6232469f65de92357b34f8ff53c5ca41", "gitHead": "2c58ed76115a3232e16824193b2d2866cfc90c90", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "https://github.com/iarna/gauge", "type": "git"}, "_npmVersion": "2.4.0", "description": "A terminal based horizontal guage", "directories": {}, "_nodeVersion": "0.10.33", "dependencies": {"ansi": "^0.3.0", "lodash.pad": "^3.0.0", "has-unicode": "^1.0.0", "lodash.padleft": "^3.0.0", "lodash.padright": "^3.0.0"}, "devDependencies": {"tap": "^0.4.13"}}, "1.2.0": {"name": "gauge", "version": "1.2.0", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "gauge@1.2.0", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/gauge", "bugs": {"url": "https://github.com/iarna/gauge/issues"}, "dist": {"shasum": "3094ab1285633f799814388fc8f2de67b4c012c5", "tarball": "https://registry.npmjs.org/gauge/-/gauge-1.2.0.tgz", "integrity": "sha512-U0HozVpMs5FAjnSqR+FNwwzs+cTaXC2YHro2Rbbx4yx1ZnKYnJFwMhYE/7TyY7n+45tSE3dE3NfSpClZKgbUcg==", "signatures": [{"sig": "MEUCIBHgfQ47vNp4XhWVb6rY/V46d251YZp0L62TB8AehvN8AiEAtenwY2F//jMHEgHzUFrBZM7R+LhvUNK67x1YrDKgl8s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "progress-bar.js", "_from": ".", "_shasum": "3094ab1285633f799814388fc8f2de67b4c012c5", "gitHead": "db15c35374816b3fc3b9e1e54866f31ed7f4a733", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "https://github.com/iarna/gauge", "type": "git"}, "_npmVersion": "2.6.0", "description": "A terminal based horizontal guage", "directories": {}, "_nodeVersion": "1.1.0", "dependencies": {"ansi": "^0.3.0", "lodash.pad": "^3.0.0", "has-unicode": "^1.0.0", "lodash.padleft": "^3.0.0", "lodash.padright": "^3.0.0"}, "devDependencies": {"tap": "^0.4.13"}}, "1.2.1": {"name": "gauge", "version": "1.2.1", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "gauge@1.2.1", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/gauge", "bugs": {"url": "https://github.com/iarna/gauge/issues"}, "dist": {"shasum": "923d55c9c6669ff9ce37528b50b40acadb15f63c", "tarball": "https://registry.npmjs.org/gauge/-/gauge-1.2.1.tgz", "integrity": "sha512-CdThAaLnXOUlX6wPkRwluUAW5hJcR6320aZ8X3LR0+KFUjN1BZrS3h50Y4ZnrUD+FO3A9JcB7sWSp5IZgJyZwQ==", "signatures": [{"sig": "MEYCIQDJgoAl5z6NjN72nYuatRs4qOQHQdEzTbTB2WEDEZc8zgIhAJshLkHnwU9wirghxQbec10TnbVqBvrI163lTtcWln2B", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "progress-bar.js", "_from": ".", "_shasum": "923d55c9c6669ff9ce37528b50b40acadb15f63c", "gitHead": "3034dd52a902e54c8132830c0482c3c53e9ad94c", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/iarna/gauge.git", "type": "git"}, "_npmVersion": "3.1.0", "description": "A terminal based horizontal guage", "directories": {}, "_nodeVersion": "0.10.38", "dependencies": {"ansi": "^0.3.0", "lodash.pad": "^3.0.0", "has-unicode": "^1.0.0", "lodash.padleft": "^3.0.0", "lodash.padright": "^3.0.0"}, "devDependencies": {"tap": "^0.4.13"}}, "1.2.2": {"name": "gauge", "version": "1.2.2", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "gauge@1.2.2", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/gauge", "bugs": {"url": "https://github.com/iarna/gauge/issues"}, "dist": {"shasum": "05b6730a19a8fcad3c340a142f0945222a3f815b", "tarball": "https://registry.npmjs.org/gauge/-/gauge-1.2.2.tgz", "integrity": "sha512-yDJPEWyYhk67nD9Lp+ngAZCtguAx4hw6U6ycDHmOyXQRAJOX57hwO45XorR7McAIdXCb7ObAEziW0uWskD77ow==", "signatures": [{"sig": "MEUCIFjrZr0ggQ60FzA+e+lX7z9SRFNEcPTOURY4kyRNayZjAiEAtKMoSCcUZF6G+Pj5DZ3bq5cFpxkNNZVuNFFHVSlXo/A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "progress-bar.js", "_from": ".", "_shasum": "05b6730a19a8fcad3c340a142f0945222a3f815b", "gitHead": "9f7eeeeed3b74a70f30b721d570435f6ffbc0168", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/iarna/gauge.git", "type": "git"}, "_npmVersion": "3.1.0", "description": "A terminal based horizontal guage", "directories": {}, "_nodeVersion": "0.10.38", "dependencies": {"ansi": "^0.3.0", "lodash.pad": "^3.0.0", "has-unicode": "^1.0.0", "lodash.padleft": "^3.0.0", "lodash.padright": "^3.0.0"}, "devDependencies": {"tap": "^0.4.13"}}, "1.2.3": {"name": "gauge", "version": "1.2.3", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "gauge@1.2.3", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/gauge", "bugs": {"url": "https://github.com/iarna/gauge/issues"}, "dist": {"shasum": "e6d1b860e3ac9a44719e4d7417778be803647a3c", "tarball": "https://registry.npmjs.org/gauge/-/gauge-1.2.3.tgz", "integrity": "sha512-26QWAJzAuJOEshk/5N3hGeTwEyA/jR8TYSVdj7OPAxj2KbCnOrxUKl61eACTQJd92y60B3FSJhTFNMYjIO09+Q==", "signatures": [{"sig": "MEUCIQDqUfEX3CrNDdrKORhaQbGNr+psDufuIK4wqOValPTz1wIgHuVuH1l3kbQcCvVBSGvkMkeXwTRGZiIT7htWikzlczQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "progress-bar.js", "_from": ".", "_shasum": "e6d1b860e3ac9a44719e4d7417778be803647a3c", "gitHead": "e71d97fc8c9c78ffff5e7724d3888ef3b94b2320", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/iarna/gauge.git", "type": "git"}, "_npmVersion": "3.5.3", "description": "A terminal based horizontal guage", "directories": {}, "_nodeVersion": "4.2.2", "dependencies": {"ansi": "^0.3.0", "lodash.pad": "^3.0.0", "has-unicode": "^2.0.0", "lodash.padleft": "^3.0.0", "lodash.padright": "^3.0.0"}, "devDependencies": {"tap": "^0.4.13"}}, "1.2.4": {"name": "gauge", "version": "1.2.4", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "gauge@1.2.4", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/gauge", "bugs": {"url": "https://github.com/iarna/gauge/issues"}, "dist": {"shasum": "b1d519758b3c77c7b45021d0ca4841548818bc41", "tarball": "https://registry.npmjs.org/gauge/-/gauge-1.2.4.tgz", "integrity": "sha512-wGYs3Y/OsuXLQ8X6kh2Q12SrlhlhbIX7mZj0EGkG2h/rNpRMnDmetDXSzbuH/kQa3kXTg8C0BcYqr6wBQcVW5g==", "signatures": [{"sig": "MEQCIBwWB8chH36uCDWd9FPRHmPE1pUGsl1B8ht9cN2kBMfsAiBIbdcdSvjZ3OUWej6KWSf8iyM1lpkd6t3cim4GArL3rA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "progress-bar.js", "_from": ".", "_shasum": "b1d519758b3c77c7b45021d0ca4841548818bc41", "gitHead": "a6af415c7e143fd8dd058c97f5f3ed3dbad872f3", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/iarna/gauge.git", "type": "git"}, "_npmVersion": "3.5.4", "description": "A terminal based horizontal guage", "directories": {}, "_nodeVersion": "4.2.2", "dependencies": {"ansi": "^0.3.0", "lodash.pad": "^3.0.0", "has-unicode": "^2.0.0", "lodash.padleft": "^3.0.0", "lodash.padright": "^3.0.0"}, "devDependencies": {"tap": "^0.4.13"}}, "1.2.5": {"name": "gauge", "version": "1.2.5", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "gauge@1.2.5", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/gauge", "bugs": {"url": "https://github.com/iarna/gauge/issues"}, "dist": {"shasum": "b80f107dd1f7d3c5a85f5aa74f9e0124caac9da7", "tarball": "https://registry.npmjs.org/gauge/-/gauge-1.2.5.tgz", "integrity": "sha512-MUWjEkitpzmoua6ye7oQ+NRbbh2oweXzPoyIviqkrt7XdMYAtTBo9cvYoAk7MDqTktVGoDAgMki2v1UCI0OjMQ==", "signatures": [{"sig": "MEYCIQDHaCwPS1ZcFpl9OkVgwIK6qS5E5yQRWDAdLLq65ZehJwIhAJIkG4paoo1YgMa2f/n44Y0iBh+HmO7N81Thpg9/LRqo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "progress-bar.js", "_from": ".", "_shasum": "b80f107dd1f7d3c5a85f5aa74f9e0124caac9da7", "gitHead": "bd0bb377d121e17d343bba156dd92fe6a8b21581", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/iarna/gauge.git", "type": "git"}, "_npmVersion": "3.6.0", "description": "A terminal based horizontal guage", "directories": {}, "_nodeVersion": "5.4.0", "dependencies": {"ansi": "^0.3.0", "lodash.pad": "^3.0.0", "has-unicode": "^2.0.0", "lodash.padleft": "^3.0.0", "lodash.padright": "^3.0.0"}, "devDependencies": {"tap": "^0.4.13"}}, "1.2.6": {"name": "gauge", "version": "1.2.6", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "gauge@1.2.6", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/gauge", "bugs": {"url": "https://github.com/iarna/gauge/issues"}, "dist": {"shasum": "4b6d178b3d9042ba9fc0ef2626dbb1f22a64c793", "tarball": "https://registry.npmjs.org/gauge/-/gauge-1.2.6.tgz", "integrity": "sha512-xoqMy9tUADtK2zIKuScFdYi2ur3NRaWkrtS98Q0zTjD8GuJ+TnLR/JPALywrxxaFrv260Fgop6eJCIbSPnsgUg==", "signatures": [{"sig": "MEUCIQCNyaDNYx4EPJ24/BTjevUEoRisiLDSJDnd06MJ88hVewIgfI+YRfbiXwfmzDKCXEfFfk9GNkGLYFTzFKRjGbex7nM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "progress-bar.js", "_from": ".", "_shasum": "4b6d178b3d9042ba9fc0ef2626dbb1f22a64c793", "gitHead": "a0d0b6c0914b25306df3e01896a477d353beed54", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/iarna/gauge.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "A terminal based horizontal guage", "directories": {}, "_nodeVersion": "4.2.2", "dependencies": {"ansi": "^0.3.0", "through2": "^2.0.1", "lodash.pad": "^4.1.0", "has-unicode": "^2.0.0", "lodash.padleft": "^3.0.0", "lodash.padright": "^3.0.0"}, "devDependencies": {"tap": "^5.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/gauge-1.2.6.tgz_1455833008276_0.26550534344278276", "host": "packages-5-east.internal.npmjs.com"}}, "1.2.7": {"name": "gauge", "version": "1.2.7", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "gauge@1.2.7", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/gauge", "bugs": {"url": "https://github.com/iarna/gauge/issues"}, "dist": {"shasum": "e9cec5483d3d4ee0ef44b60a7d99e4935e136d93", "tarball": "https://registry.npmjs.org/gauge/-/gauge-1.2.7.tgz", "integrity": "sha512-fVbU2wRE91yDvKUnrIaQlHKAWKY5e08PmztCrwuH5YVQ+Z/p3d0ny2T48o6uvAAXHIUnfaQdHkmxYbQft1eHVA==", "signatures": [{"sig": "MEYCIQCAYcQK03fLBJW/Ce3tODKo6bm5KIR0WheRecK5E6ImFQIhALlOfCG3cq1UgO/KZOpgnJ+uIwCNguiyGcQ3RQuUtt8p", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "progress-bar.js", "_from": ".", "_shasum": "e9cec5483d3d4ee0ef44b60a7d99e4935e136d93", "gitHead": "75a7d0a4ed67489ac992ed3d211bed60376ca7c1", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/iarna/gauge.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "A terminal based horizontal guage", "directories": {}, "_nodeVersion": "4.2.2", "dependencies": {"ansi": "^0.3.0", "lodash.pad": "^4.1.0", "has-unicode": "^2.0.0", "lodash.padend": "^4.1.0", "lodash.padstart": "^4.1.0"}, "devDependencies": {"tap": "^5.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/gauge-1.2.7.tgz_1455835409513_0.6293477965518832", "host": "packages-9-west.internal.npmjs.com"}}, "2.0.0": {"name": "gauge", "version": "2.0.0", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "gauge@2.0.0", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/gauge", "bugs": {"url": "https://github.com/iarna/gauge/issues"}, "dist": {"shasum": "d3408135d29e7e817f595443a1cdb435203ff8a5", "tarball": "https://registry.npmjs.org/gauge/-/gauge-2.0.0.tgz", "integrity": "sha512-h4N5YqHW/FDox90WQWUyk1Le1jaJFbjV5eso2oEkA29vBRsevddm83NPd/Ydd+qwdyE3XjK31n6dENb+W288yw==", "signatures": [{"sig": "MEQCIBI/iD+XEzgAB9o9yUyHTCOyKKnnHpL0r38tiBJO07SeAiBjYSxqOXCsXAzusJvzhwMGp4c6mBQqezWwBARkkXcCbQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "d3408135d29e7e817f595443a1cdb435203ff8a5", "gitHead": "cfdbfb65a544dcb10d41a5abed110f34df4d007d", "scripts": {"test": "standard && tap test/*.js --coverage"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/iarna/gauge.git", "type": "git"}, "_npmVersion": "3.7.3", "description": "A terminal based horizontal guage", "directories": {}, "_nodeVersion": "5.4.0", "dependencies": {"aproba": "^1.0.1", "has-color": "^0.1.7", "strip-ansi": "^3.0.0", "wide-align": "^1.1.0", "has-unicode": "^2.0.0", "string-width": "^1.0.1", "object-assign": "^4.0.1"}, "devDependencies": {"tap": "^5.6.0", "standard": "^6.0.7", "through2": "^2.0.0", "require-inject": "^1.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/gauge-2.0.0.tgz_1456017288812_0.2166549153625965", "host": "packages-9-west.internal.npmjs.com"}}, "2.1.0": {"name": "gauge", "version": "2.1.0", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "gauge@2.1.0", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/gauge", "bugs": {"url": "https://github.com/iarna/gauge/issues"}, "dist": {"shasum": "595604845c774b35f25b0a5a9fc8a96516d0b2da", "tarball": "https://registry.npmjs.org/gauge/-/gauge-2.1.0.tgz", "integrity": "sha512-dKOy7VvA+05DdX6RsSjtalihrwNPZg1QXCV0gqM9UqDrKWrILJpDJijCif74HlQHCzXdGhb+egRXI0KpHBwvwQ==", "signatures": [{"sig": "MEUCIBb5ZeWDNdVyOlBYmuMJW5FCkmbzJAi3TwkgwRjATA/6AiEA1DZJ1bgQbmRGdpE7mwv2ENzcYv0A3G60hCtc1neP3sU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "595604845c774b35f25b0a5a9fc8a96516d0b2da", "gitHead": "023575cdd26a268d9eaea10e9fc0a8d340350e70", "scripts": {"test": "standard && tap test/*.js --coverage"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/iarna/gauge.git", "type": "git"}, "_npmVersion": "3.8.0", "description": "A terminal based horizontal guage", "directories": {}, "_nodeVersion": "4.2.2", "dependencies": {"aproba": "^1.0.1", "has-color": "^0.1.7", "strip-ansi": "^3.0.0", "wide-align": "^1.1.0", "has-unicode": "^2.0.0", "signal-exit": "^2.1.2", "string-width": "^1.0.1", "object-assign": "^4.0.1"}, "devDependencies": {"tap": "^5.6.0", "standard": "^6.0.7", "through2": "^2.0.0", "require-inject": "^1.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/gauge-2.1.0.tgz_1456873991095_0.3077438606414944", "host": "packages-13-west.internal.npmjs.com"}}, "2.2.0": {"name": "gauge", "version": "2.2.0", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "gauge@2.2.0", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/gauge", "bugs": {"url": "https://github.com/iarna/gauge/issues"}, "dist": {"shasum": "c115548d7423f3bfa3247e153e8dccc767ec2696", "tarball": "https://registry.npmjs.org/gauge/-/gauge-2.2.0.tgz", "integrity": "sha512-KWtmjkzMqijtaE07TF2hw/1Nge7f8r4/Fb+vNnunfG75KGWkUx1UnY+nqcBOmPsiP9l8FKT0aPO1YqUoIioOgg==", "signatures": [{"sig": "MEQCIAVyR9GVHpL3xk4MExY+klCnT/vVLj6A3RFrez28TupvAiA13Ol9BiHNmIt1WaSg4m8pRWpE7iE/DUpvX3OE/VT+tQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "c115548d7423f3bfa3247e153e8dccc767ec2696", "gitHead": "38ee3b98f25d4020e0fba8acf9abde9c1f4919f5", "scripts": {"test": "standard && tap test/*.js --coverage"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/iarna/gauge.git", "type": "git"}, "_npmVersion": "3.8.1", "description": "A terminal based horizontal guage", "directories": {}, "_nodeVersion": "4.2.2", "dependencies": {"aproba": "^1.0.1", "has-color": "^0.1.7", "strip-ansi": "^3.0.0", "wide-align": "^1.1.0", "has-unicode": "^2.0.0", "signal-exit": "^2.1.2", "string-width": "^1.0.1", "object-assign": "^4.0.1"}, "devDependencies": {"tap": "^5.6.0", "standard": "^6.0.7", "through2": "^2.0.0", "require-inject": "^1.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/gauge-2.2.0.tgz_1458082244821_0.86755271977745", "host": "packages-12-west.internal.npmjs.com"}}, "2.2.1": {"name": "gauge", "version": "2.2.1", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "gauge@2.2.1", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/gauge", "bugs": {"url": "https://github.com/iarna/gauge/issues"}, "dist": {"shasum": "979d7c1bd5a948824f0ccbeaef6fa0f6607860dc", "tarball": "https://registry.npmjs.org/gauge/-/gauge-2.2.1.tgz", "integrity": "sha512-S1Oe6CwshyssG2CCN+0N/DxpaSUYkTbYtbgB5jY4JYIAid3ikCyjyJ+h16dbosrHAWhDqHKkfdnnvSX8zpfiCA==", "signatures": [{"sig": "MEQCIDvCWmsOwhIzuBeJmtd7GpogOQrno89TGq5nL8ILvC8YAiBVzBmu836GudOBccRCEH7JP/N4Mkl04qIwZ9+qBPs0eQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "979d7c1bd5a948824f0ccbeaef6fa0f6607860dc", "gitHead": "78b35070f9fbd08d1770f9aa09df648722c30fc6", "scripts": {"test": "standard && tap test/*.js --coverage"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/iarna/gauge.git", "type": "git"}, "_npmVersion": "3.8.1", "description": "A terminal based horizontal guage", "directories": {}, "_nodeVersion": "4.2.2", "dependencies": {"aproba": "^1.0.1", "has-color": "^0.1.7", "strip-ansi": "^3.0.0", "wide-align": "^1.1.0", "has-unicode": "^2.0.0", "signal-exit": "^2.1.2", "string-width": "^1.0.1", "object-assign": "^4.0.1"}, "devDependencies": {"tap": "^5.6.0", "standard": "^6.0.7", "through2": "^2.0.0", "require-inject": "^1.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/gauge-2.2.1.tgz_1458082409285_0.9220770343672484", "host": "packages-13-west.internal.npmjs.com"}}, "2.3.0": {"name": "gauge", "version": "2.3.0", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "gauge@2.3.0", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/gauge", "bugs": {"url": "https://github.com/iarna/gauge/issues"}, "dist": {"shasum": "69594aa32548cbb61b73d3bbdf70f99ccc576c1d", "tarball": "https://registry.npmjs.org/gauge/-/gauge-2.3.0.tgz", "integrity": "sha512-0z3ihT4LV5KzP+BsbfQ37FouLeobJXxssj6sSPu6fH4CsPzzMXMBLJ6KltOpGwX4oiu6rLgAIOi10PHNa6p1ow==", "signatures": [{"sig": "MEQCIEFZt7Vffhrp61wp/aqDsvqDD+v6T0iXuuto7wNKoOt/AiAHSyDa46FA8TtWjWRDdYmez3xp9MNbQTfwNzT8yWrnSg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "69594aa32548cbb61b73d3bbdf70f99ccc576c1d", "gitHead": "7537d40009f199fa78158befcb7cea4b31ba0b77", "scripts": {"test": "standard && tap test/*.js --coverage"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/iarna/gauge.git", "type": "git"}, "_npmVersion": "3.8.9", "description": "A terminal based horizontal guage", "directories": {}, "_nodeVersion": "4.4.0", "dependencies": {"aproba": "^1.0.1", "has-color": "^0.1.7", "strip-ansi": "^3.0.0", "wide-align": "^1.1.0", "has-unicode": "^2.0.0", "signal-exit": "^2.1.2", "string-width": "^1.0.1", "object-assign": "^4.0.1"}, "devDependencies": {"tap": "^5.6.0", "standard": "^6.0.7", "through2": "^2.0.0", "require-inject": "^1.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/gauge-2.3.0.tgz_1461894738473_0.10730170574970543", "host": "packages-16-east.internal.npmjs.com"}}, "2.3.1": {"name": "gauge", "version": "2.3.1", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "gauge@2.3.1", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/gauge", "bugs": {"url": "https://github.com/iarna/gauge/issues"}, "dist": {"shasum": "3c4b268e9ba469de2be91a35c68376c46a5b18b0", "tarball": "https://registry.npmjs.org/gauge/-/gauge-2.3.1.tgz", "integrity": "sha512-IdgFouFZhh598336Ww/ql4spa3AlHosvNTUdIkph+p9ZY8VCVZPSVwocw5JUD/k1hqKj1qmRoBiZLSNLclk5kg==", "signatures": [{"sig": "MEUCIQDvek6QkxDYaJoWM8DETlwrAM6vOPq2/FDhJVu1StSWBQIgFAaMiU0qnbSY5KvOVzH02P5oOWuWFYaG26LmdsO9CUo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "3c4b268e9ba469de2be91a35c68376c46a5b18b0", "gitHead": "dddd91adcdff3a742da2d037d5d428fc9e1550a2", "scripts": {"test": "standard && tap test/*.js --coverage"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/iarna/gauge.git", "type": "git"}, "_npmVersion": "3.8.9", "description": "A terminal based horizontal guage", "directories": {}, "_nodeVersion": "4.4.0", "dependencies": {"aproba": "^1.0.1", "has-color": "^0.1.7", "strip-ansi": "^3.0.0", "wide-align": "^1.1.0", "has-unicode": "^2.0.0", "signal-exit": "^2.1.2", "string-width": "^1.0.1", "object-assign": "^4.0.1"}, "devDependencies": {"tap": "^5.6.0", "standard": "^6.0.7", "through2": "^2.0.0", "require-inject": "^1.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/gauge-2.3.1.tgz_1461895383486_0.6393620013259351", "host": "packages-16-east.internal.npmjs.com"}}, "2.4.0": {"name": "gauge", "version": "2.4.0", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "gauge@2.4.0", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/gauge", "bugs": {"url": "https://github.com/iarna/gauge/issues"}, "dist": {"shasum": "7168bc137cde159bff7b1ba18c159f8aed71f3ef", "tarball": "https://registry.npmjs.org/gauge/-/gauge-2.4.0.tgz", "integrity": "sha512-W8QcQVTA8X4CXdYBVLAuQ0mHo2wIi+IVamDQWwC+cJqAo1O83sdaJYw92rnVUbeygb2RCI0zGdhSxutx6uIHsw==", "signatures": [{"sig": "MEYCIQCeZA3YCpVywjCngzNC9n/5SZgG7svrm5rLINU5iJKsoQIhAOH7oIdM6dH8J0bSR/KaghldpY0fNMUeNkwjemORnGg9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "7168bc137cde159bff7b1ba18c159f8aed71f3ef", "gitHead": "a41baad97a7a18ed466181b75a161bd8cc21ad03", "scripts": {"test": "standard && tap test/*.js --coverage"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/iarna/gauge.git", "type": "git"}, "_npmVersion": "3.9.0", "description": "A terminal based horizontal guage", "directories": {}, "_nodeVersion": "6.0.0", "dependencies": {"aproba": "^1.0.1", "has-color": "^0.1.7", "strip-ansi": "^3.0.0", "wide-align": "^1.1.0", "has-unicode": "^2.0.0", "signal-exit": "^2.1.2", "string-width": "^1.0.1", "object-assign": "^4.0.1"}, "devDependencies": {"tap": "^5.6.0", "standard": "^6.0.7", "through2": "^2.0.0", "require-inject": "^1.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/gauge-2.4.0.tgz_1462843434229_0.022514664102345705", "host": "packages-16-east.internal.npmjs.com"}}, "2.5.0": {"name": "gauge", "version": "2.5.0", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "gauge@2.5.0", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/gauge", "bugs": {"url": "https://github.com/iarna/gauge/issues"}, "dist": {"shasum": "15094f34e20bfce7b3e6ff439818e28027aa2440", "tarball": "https://registry.npmjs.org/gauge/-/gauge-2.5.0.tgz", "integrity": "sha512-fqD2SvWfqCymx65oFZZmgOTnkmAb4IGIb1tuBz7tgoQlyN8N26ClyjI5KfRB7t5ibGLytwmdLshrcGchT7jO2Q==", "signatures": [{"sig": "MEYCIQCrfrj1pzwb5oQc3FsvYENcwZPMcpaCGu14xT79kDAdJQIhANsoftAkPGlaCF5IW+S7MUGJNjKzQpqnxa6Xw0BiP8bn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["base-theme.js", "CHANGELOG.md", "console-strings.js", "error.js", "has-color.js", "index.js", "LICENSE", "package.json", "plumbing.js", "process.js", "progress-bar.js", "README.md", "render-template.js", "set-immediate.js", "set-interval.js", "spin.js", "template-item.js", "theme-set.js", "themes.js", "wide-truncate.js"], "_shasum": "15094f34e20bfce7b3e6ff439818e28027aa2440", "gitHead": "dfc7361d1790b2fc9541e59a00dba35819e7d5b8", "scripts": {"test": "standard && tap test/*.js --coverage"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/iarna/gauge.git", "type": "git"}, "_npmVersion": "3.9.2", "description": "A terminal based horizontal guage", "directories": {}, "_nodeVersion": "4.4.0", "dependencies": {"aproba": "^1.0.1", "has-color": "^0.1.7", "strip-ansi": "^3.0.0", "wide-align": "^1.1.0", "has-unicode": "^2.0.0", "signal-exit": "^2.1.2", "string-width": "^1.0.1", "object-assign": "^4.0.1"}, "devDependencies": {"tap": "^5.6.0", "standard": "^6.0.7", "through2": "^2.0.0", "require-inject": "^1.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/gauge-2.5.0.tgz_1465259770241_0.06730274180881679", "host": "packages-12-west.internal.npmjs.com"}}, "2.5.1": {"name": "gauge", "version": "2.5.1", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "gauge@2.5.1", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/gauge", "bugs": {"url": "https://github.com/iarna/gauge/issues"}, "dist": {"shasum": "ae35da37184411472a38e0fb7b1b3697c9323240", "tarball": "https://registry.npmjs.org/gauge/-/gauge-2.5.1.tgz", "integrity": "sha512-MhLnUbIRJlEZYNnX5hSXlv70qDPkGvVDE1v5RWExQKDH7xAkxPhnTd5W/05E9kFz0KV4VvyiDd4LY6njXtZdNw==", "signatures": [{"sig": "MEYCIQCKy8wKPqU8l0KRNUWxlKipJphXq6b4GgLhSZiLhUu/dwIhAOK7B+pyp+++Cik35+rylKfgXc+SyJ547SMmCfVThlSy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["base-theme.js", "CHANGELOG.md", "console-strings.js", "error.js", "has-color.js", "index.js", "LICENSE", "package.json", "plumbing.js", "process.js", "progress-bar.js", "README.md", "render-template.js", "set-immediate.js", "set-interval.js", "spin.js", "template-item.js", "theme-set.js", "themes.js", "wide-truncate.js"], "_shasum": "ae35da37184411472a38e0fb7b1b3697c9323240", "gitHead": "685b999af82e3ab04a2db56d11425cb9e41e730c", "scripts": {"test": "standard && tap test/*.js --coverage"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/iarna/gauge.git", "type": "git"}, "_npmVersion": "3.9.6", "description": "A terminal based horizontal guage", "directories": {}, "_nodeVersion": "6.0.0", "dependencies": {"aproba": "^1.0.3", "has-color": "^0.1.7", "strip-ansi": "^3.0.1", "wide-align": "^1.1.0", "has-unicode": "^2.0.0", "signal-exit": "^3.0.0", "string-width": "^1.0.1", "object-assign": "^4.1.0"}, "devDependencies": {"tap": "^5.7.2", "standard": "^7.1.2", "through2": "^2.0.0", "require-inject": "^1.4.0", "readable-stream": "^2.0.6"}, "_npmOperationalInternal": {"tmp": "tmp/gauge-2.5.1.tgz_1465858229208_0.20211401558481157", "host": "packages-12-west.internal.npmjs.com"}}, "2.5.2": {"name": "gauge", "version": "2.5.2", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "gauge@2.5.2", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/gauge", "bugs": {"url": "https://github.com/iarna/gauge/issues"}, "dist": {"shasum": "9c870371cdbbd6985cb1198e421e2c6790e1a383", "tarball": "https://registry.npmjs.org/gauge/-/gauge-2.5.2.tgz", "integrity": "sha512-I8Yr+7Yh7a9dobSqE2qtwVm4l1Nihhdx+A1teyrgcG2TH5uJS995NLb+UXBtPyLp2gJyGinacdUvrxmxlXV6AA==", "signatures": [{"sig": "MEYCIQChym/C5/GSJvfp2Glf1GzFLJdr2EnNU/NFI5WlTJVv2gIhAIWQBW3kCPUJtsX9NrEzpydLvOVh7+kJuJScFL1N4z8S", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["base-theme.js", "CHANGELOG.md", "error.js", "has-color.js", "index.js", "LICENSE", "package.json", "plumbing.js", "process.js", "progress-bar.js", "README.md", "render-template.js", "set-immediate.js", "set-interval.js", "spin.js", "template-item.js", "theme-set.js", "themes.js", "wide-truncate.js"], "_shasum": "9c870371cdbbd6985cb1198e421e2c6790e1a383", "gitHead": "334b8b22752e64b72cb6a10b44e06b95942b3aad", "scripts": {"test": "standard && tap test/*.js --coverage"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/iarna/gauge.git", "type": "git"}, "_npmVersion": "3.9.2", "description": "A terminal based horizontal guage", "directories": {}, "_nodeVersion": "4.4.0", "dependencies": {"aproba": "^1.0.3", "has-color": "^0.1.7", "strip-ansi": "^3.0.1", "wide-align": "^1.1.0", "has-unicode": "^2.0.0", "signal-exit": "^3.0.0", "string-width": "^1.0.1", "object-assign": "^4.1.0", "console-control-strings": "^1.0.0"}, "devDependencies": {"tap": "^5.7.2", "standard": "^7.1.2", "through2": "^2.0.0", "require-inject": "^1.4.0", "readable-stream": "^2.0.6"}, "_npmOperationalInternal": {"tmp": "tmp/gauge-2.5.2.tgz_1466026091552_0.03216121275909245", "host": "packages-16-east.internal.npmjs.com"}}, "2.5.3": {"name": "gauge", "version": "2.5.3", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "gauge@2.5.3", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/gauge", "bugs": {"url": "https://github.com/iarna/gauge/issues"}, "dist": {"shasum": "ce08f4d442a7f94e817835721af5e8d0fb3ae9e3", "tarball": "https://registry.npmjs.org/gauge/-/gauge-2.5.3.tgz", "integrity": "sha512-rZqM/ujiDIfrK8cArAVWQfQyIlEoFhATEtqqZGbAY8/wcHhnYul8Y17IyWm3Qw0asv2Bh9tAoIXwWawiTMSJhw==", "signatures": [{"sig": "MEQCIAo105/YT959BD0NIuayX9iS2hHj8+Z5Qx8jYD0Vr/qbAiB9XNymDP+iXVEhbCOfNgkNTY6YXiazuzn7aqUI8CB6fw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["base-theme.js", "CHANGELOG.md", "error.js", "has-color.js", "index.js", "LICENSE", "package.json", "plumbing.js", "process.js", "progress-bar.js", "README.md", "render-template.js", "set-immediate.js", "set-interval.js", "spin.js", "template-item.js", "theme-set.js", "themes.js", "wide-truncate.js"], "_shasum": "ce08f4d442a7f94e817835721af5e8d0fb3ae9e3", "gitHead": "819308b1a8cad4e65a6c1c3f7fe8925e6902e3ba", "scripts": {"test": "standard && tap test/*.js --coverage"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/iarna/gauge.git", "type": "git"}, "_npmVersion": "3.9.2", "description": "A terminal based horizontal guage", "directories": {}, "_nodeVersion": "4.4.0", "dependencies": {"aproba": "^1.0.3", "has-color": "^0.1.7", "strip-ansi": "^3.0.1", "wide-align": "^1.1.0", "has-unicode": "^2.0.0", "signal-exit": "^3.0.0", "string-width": "^1.0.1", "object-assign": "^4.1.0", "console-control-strings": "^1.0.0"}, "devDependencies": {"tap": "^5.7.2", "standard": "^7.1.2", "through2": "^2.0.0", "require-inject": "^1.4.0", "readable-stream": "^2.0.6"}, "_npmOperationalInternal": {"tmp": "tmp/gauge-2.5.3.tgz_1466050754940_0.47893059928901494", "host": "packages-16-east.internal.npmjs.com"}}, "2.6.0": {"name": "gauge", "version": "2.6.0", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "gauge@2.6.0", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/gauge", "bugs": {"url": "https://github.com/iarna/gauge/issues"}, "dist": {"shasum": "d35301ad18e96902b4751dcbbe40f4218b942a46", "tarball": "https://registry.npmjs.org/gauge/-/gauge-2.6.0.tgz", "integrity": "sha512-YFArj4ApymqXBC2KORLBtKc2CIc1r+ycx786XYEkEF4++1rBGhTD7QHOSWYYGCx50Op+WuboEk9xQa+wNmzuLA==", "signatures": [{"sig": "MEUCIF/FLSt4htNRoNvQwocm1sHBhBD+7I+HkmyWql6HqXV3AiEA2dbO7+9jTDExhMmotWpMSHK4tBs1NLeKKeYdgW+MXXc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["base-theme.js", "CHANGELOG.md", "error.js", "has-color.js", "index.js", "LICENSE", "package.json", "plumbing.js", "process.js", "progress-bar.js", "README.md", "render-template.js", "set-immediate.js", "set-interval.js", "spin.js", "template-item.js", "theme-set.js", "themes.js", "wide-truncate.js"], "_shasum": "d35301ad18e96902b4751dcbbe40f4218b942a46", "gitHead": "d51040a71c269432c16cc542143f403a831630e6", "scripts": {"test": "standard && tap test/*.js --coverage"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/iarna/gauge.git", "type": "git"}, "_npmVersion": "3.9.2", "description": "A terminal based horizontal guage", "directories": {}, "_nodeVersion": "4.4.0", "dependencies": {"aproba": "^1.0.3", "has-color": "^0.1.7", "strip-ansi": "^3.0.1", "wide-align": "^1.1.0", "has-unicode": "^2.0.0", "signal-exit": "^3.0.0", "string-width": "^1.0.1", "object-assign": "^4.1.0", "console-control-strings": "^1.0.0"}, "devDependencies": {"tap": "^5.7.2", "standard": "^7.1.2", "through2": "^2.0.0", "require-inject": "^1.4.0", "readable-stream": "^2.0.6"}, "_npmOperationalInternal": {"tmp": "tmp/gauge-2.6.0.tgz_1466067371972_0.20705468393862247", "host": "packages-12-west.internal.npmjs.com"}}, "2.7.0": {"name": "gauge", "version": "2.7.0", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "gauge@2.7.0", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/gauge", "bugs": {"url": "https://github.com/iarna/gauge/issues"}, "dist": {"shasum": "b304f53cc868ea235067ed4b2b0616dd191ee531", "tarball": "https://registry.npmjs.org/gauge/-/gauge-2.7.0.tgz", "integrity": "sha512-aJ3sGTG46M0eyItL+/PaumWAVqP1PLvDJ4pBKqJZb7rYp+IJO0ogh7NwmCDnE/2mVpEaj7+9rLU1jWghGnmkmw==", "signatures": [{"sig": "MEUCICyIewBUpKE8UgcyWvvooy25G4KHXOU/t8uFYcfSdQAiAiEA5Qf/CTsMb+fA8KE15I/QErMoG/DvXCvAfRoecCFYb9Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["base-theme.js", "CHANGELOG.md", "error.js", "has-color.js", "index.js", "LICENSE", "package.json", "plumbing.js", "process.js", "progress-bar.js", "README.md", "render-template.js", "set-immediate.js", "set-interval.js", "spin.js", "template-item.js", "theme-set.js", "themes.js", "wide-truncate.js"], "_shasum": "b304f53cc868ea235067ed4b2b0616dd191ee531", "gitHead": "582c8ef66aec3e8f5f0bca06cece42da6e871eb1", "scripts": {"test": "standard && tap test/*.js --coverage", "prepublish": "rm -f *~"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/iarna/gauge.git", "type": "git"}, "_npmVersion": "4.0.0", "description": "A terminal based horizontal guage", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"aproba": "^1.0.3", "has-color": "^0.1.7", "strip-ansi": "^3.0.1", "wide-align": "^1.1.0", "has-unicode": "^2.0.0", "signal-exit": "^3.0.0", "string-width": "^1.0.1", "object-assign": "^4.1.0", "console-control-strings": "^1.0.0"}, "devDependencies": {"tap": "^5.7.2", "standard": "^7.1.2", "through2": "^2.0.0", "require-inject": "^1.4.0", "readable-stream": "^2.0.6"}, "_npmOperationalInternal": {"tmp": "tmp/gauge-2.7.0.tgz_1478208903566_0.7889880251605064", "host": "packages-12-west.internal.npmjs.com"}}, "2.7.1": {"name": "gauge", "version": "2.7.1", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "gauge@2.7.1", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/gauge", "bugs": {"url": "https://github.com/iarna/gauge/issues"}, "dist": {"shasum": "388473894fe8be5e13ffcdb8b93e4ed0616428c7", "tarball": "https://registry.npmjs.org/gauge/-/gauge-2.7.1.tgz", "integrity": "sha512-rJSgVoHuaUeiKFmecpm+isTuWnvl/z2PfU+BhmmwfXIh19Pu7ne38LpzpWRXwA0f2LzBNAIjfTfusipJi2bV7Q==", "signatures": [{"sig": "MEYCIQDoA4S6N47PtaD+6DTrbP/4QxGaLv+/SOUej24kkzrw7wIhAL2kvoC+OQZB3X3xyny7q/wYqmJm8OVu+LWvYUAp/cou", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["base-theme.js", "CHANGELOG.md", "error.js", "has-color.js", "index.js", "LICENSE", "package.json", "plumbing.js", "process.js", "progress-bar.js", "README.md", "render-template.js", "set-immediate.js", "set-interval.js", "spin.js", "template-item.js", "theme-set.js", "themes.js", "wide-truncate.js"], "_shasum": "388473894fe8be5e13ffcdb8b93e4ed0616428c7", "gitHead": "d7ac37af0a44af2315656fb73f76f6bca03d084e", "scripts": {"test": "standard && tap test/*.js --coverage", "prepublish": "rm -f *~"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/iarna/gauge.git", "type": "git"}, "_npmVersion": "4.0.0", "description": "A terminal based horizontal guage", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"aproba": "^1.0.3", "has-color": "^0.1.7", "strip-ansi": "^3.0.1", "wide-align": "^1.1.0", "has-unicode": "^2.0.0", "signal-exit": "^3.0.0", "string-width": "^1.0.1", "object-assign": "^4.1.0", "console-control-strings": "^1.0.0"}, "devDependencies": {"tap": "^5.7.2", "standard": "^7.1.2", "through2": "^2.0.0", "require-inject": "^1.4.0", "readable-stream": "^2.0.6"}, "_npmOperationalInternal": {"tmp": "tmp/gauge-2.7.1.tgz_1478210591065_0.5937802786938846", "host": "packages-18-east.internal.npmjs.com"}}, "2.7.2": {"name": "gauge", "version": "2.7.2", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "gauge@2.7.2", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/gauge", "bugs": {"url": "https://github.com/iarna/gauge/issues"}, "dist": {"shasum": "15cecc31b02d05345a5d6b0e171cdb3ad2307774", "tarball": "https://registry.npmjs.org/gauge/-/gauge-2.7.2.tgz", "integrity": "sha512-GSdoJS+3THWtgr4Hl3DYsL2X8HgY3PG294anxOnSDOT7UWhmJ+e5Jc5qckX96SrKsoL116EdSbpr8gLEPwEd0Q==", "signatures": [{"sig": "MEUCIQCDGb0mECY8GrBqtE5QScSlcs1j8S23NyTOsjTv/KgVOQIgSLxsFi3iGKqXv4lWuoZjlBHI181dJWzhKRnvq3om9Jo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["base-theme.js", "CHANGELOG.md", "error.js", "has-color.js", "index.js", "LICENSE", "package.json", "plumbing.js", "process.js", "progress-bar.js", "README.md", "render-template.js", "set-immediate.js", "set-interval.js", "spin.js", "template-item.js", "theme-set.js", "themes.js", "wide-truncate.js"], "_shasum": "15cecc31b02d05345a5d6b0e171cdb3ad2307774", "gitHead": "6971e27a577d165cde360ebed86a59dfc18ac55b", "scripts": {"test": "standard && tap test/*.js --coverage", "prepublish": "rm -f *~"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/iarna/gauge.git", "type": "git"}, "_npmVersion": "4.0.5", "description": "A terminal based horizontal guage", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"aproba": "^1.0.3", "strip-ansi": "^3.0.1", "wide-align": "^1.1.0", "has-unicode": "^2.0.0", "signal-exit": "^3.0.0", "string-width": "^1.0.1", "object-assign": "^4.1.0", "supports-color": "^0.2.0", "console-control-strings": "^1.0.0"}, "devDependencies": {"tap": "^5.7.2", "standard": "^7.1.2", "through2": "^2.0.0", "require-inject": "^1.4.0", "readable-stream": "^2.0.6"}, "_npmOperationalInternal": {"tmp": "tmp/gauge-2.7.2.tgz_1480851024801_0.17901206784881651", "host": "packages-12-west.internal.npmjs.com"}}, "2.7.3": {"name": "gauge", "version": "2.7.3", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "gauge@2.7.3", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/gauge", "bugs": {"url": "https://github.com/iarna/gauge/issues"}, "dist": {"shasum": "1c23855f962f17b3ad3d0dc7443f304542edfe09", "tarball": "https://registry.npmjs.org/gauge/-/gauge-2.7.3.tgz", "integrity": "sha512-UnvQ6VA8+dfvcUvrG1AR5ZUSQssn56AhL04kXJzvjBRLvTAAQmCT3V+nCkoiwNHY3dWsOMRbQVeZR4/eL83ReQ==", "signatures": [{"sig": "MEUCIH89kI/ZaEmoGKGSuRSYuHebw9vFqE6CtN9yAeKqufGZAiEA+qAGResWdOcMaWtJr42KHzvlUjA8hbLNRX2wW3H1Cg0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["base-theme.js", "CHANGELOG.md", "error.js", "has-color.js", "index.js", "LICENSE", "package.json", "plumbing.js", "process.js", "progress-bar.js", "README.md", "render-template.js", "set-immediate.js", "set-interval.js", "spin.js", "template-item.js", "theme-set.js", "themes.js", "wide-truncate.js"], "_shasum": "1c23855f962f17b3ad3d0dc7443f304542edfe09", "gitHead": "2defcdb50c243c92d21f0fd39c4b06bd48bcfdf3", "scripts": {"test": "standard && tap test/*.js --coverage", "prepublish": "rm -f *~"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/iarna/gauge.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "A terminal based horizontal guage", "directories": {}, "_nodeVersion": "4.6.1", "dependencies": {"aproba": "^1.0.3", "strip-ansi": "^3.0.1", "wide-align": "^1.1.0", "has-unicode": "^2.0.0", "signal-exit": "^3.0.0", "string-width": "^1.0.1", "object-assign": "^4.1.0", "console-control-strings": "^1.0.0"}, "devDependencies": {"tap": "^5.7.2", "standard": "^7.1.2", "through2": "^2.0.0", "require-inject": "^1.4.0", "readable-stream": "^2.0.6"}, "_npmOperationalInternal": {"tmp": "tmp/gauge-2.7.3.tgz_1486515229637_0.61380137456581", "host": "packages-18-east.internal.npmjs.com"}}, "2.7.4": {"name": "gauge", "version": "2.7.4", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "gauge@2.7.4", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/gauge", "bugs": {"url": "https://github.com/iarna/gauge/issues"}, "dist": {"shasum": "2c03405c7538c39d7eb37b317022e325fb018bf7", "tarball": "https://registry.npmjs.org/gauge/-/gauge-2.7.4.tgz", "integrity": "sha512-14x4kjc6lkD3ltw589k0NrPD6cCNTD6CWoVUNpB85+DrtONoZn+Rug6xZU5RvSC4+TZPxA5AnBibQYAvZn41Hg==", "signatures": [{"sig": "MEUCIBQW6CXxqtINAfY7ocF5skijoK4FlKv6Oq6W6U9Z6BnJAiEAokOy0oFp/vgwi8moEmu+kPPg+1wCBKRx15ky8ubqhZc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["base-theme.js", "CHANGELOG.md", "error.js", "has-color.js", "index.js", "LICENSE", "package.json", "plumbing.js", "process.js", "progress-bar.js", "README.md", "render-template.js", "set-immediate.js", "set-interval.js", "spin.js", "template-item.js", "theme-set.js", "themes.js", "wide-truncate.js"], "gitHead": "1011abf6c2cb7ae89a3ee76fb447d3182d4e8d3a", "scripts": {"test": "standard && tap test/*.js --coverage", "prepublish": "rm -f *~"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/iarna/gauge.git", "type": "git"}, "_npmVersion": "4.5.0", "description": "A terminal based horizontal guage", "directories": {}, "_nodeVersion": "7.7.4", "dependencies": {"aproba": "^1.0.3", "strip-ansi": "^3.0.1", "wide-align": "^1.1.0", "has-unicode": "^2.0.0", "signal-exit": "^3.0.0", "string-width": "^1.0.1", "object-assign": "^4.1.0", "console-control-strings": "^1.0.0"}, "devDependencies": {"tap": "^5.7.2", "standard": "^7.1.2", "through2": "^2.0.0", "require-inject": "^1.4.0", "readable-stream": "^2.0.6"}, "_npmOperationalInternal": {"tmp": "tmp/gauge-2.7.4.tgz_1492815975490_0.4050216095056385", "host": "packages-18-east.internal.npmjs.com"}}, "3.0.0": {"name": "gauge", "version": "3.0.0", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "gauge@3.0.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/gauge", "bugs": {"url": "https://github.com/npm/gauge/issues"}, "dist": {"shasum": "2313d52e067a797356c8214907828ff1a097cde4", "tarball": "https://registry.npmjs.org/gauge/-/gauge-3.0.0.tgz", "fileCount": 19, "integrity": "sha512-VSxauaaCsLOTerAyzunAYGgK3iaWZvOL1BCvBvf/IhDWrczPAf1tUqn05DOCJOOe4k3vOdX6fHhJIvF2UtCMhw==", "signatures": [{"sig": "MEYCIQDC/sFSRg4cwFBtXaUNG57ChYU6xHhz2HPATC84hrmIFAIhAIDz7pt/P1MFwOknJgbryRnLPp7MFaN4OEOfQLaA3h+P", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48538, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeNKCMCRA9TVsSAnZWagAAGBgP/3ilCItGPSGs6D0mitcY\n0AUkX9gvVmE3IknqbkjkHTnFEveY7r/GAKeFurClPwjOq0Kj5fqbD3WcvqBU\nBGiwMwi9JXVbUg8R07vmGPWIq857QzxRE/XpCJdmUJXZk3lFEbJaxz2woyUm\nj9Js372DitUz8fcO3Dv7NGt3Dv1g1E4nM7F0ySiBVIcxtTeo//bE+dh0ELDu\nTQqfE4afsdxOru5EUj6vMPA1b94DTgNYlwmLUQ3Bnzla1C8WVLGcdeqIPzHX\nLE9Vp8jQPFG1zc/wxpSxcwgKPoRYjhfPLlcrUMzx7QVnlCRrWVITLuE7h7Uc\nRf2baHhXbiwj+1/f2kodPYqYNfsoN3zPJm3j/rr0DI3myR8TRSEY1bDtm2Iw\nvvr6GdBYFHCo7+iHoWt4zjSKPs+Nlnkq6bHtPA7jq9B0SxkKFGG13bvlWBNc\nII3Jod7ZJSBBegjJZTOQVJpjARbVVkcjry76twpvsi9EF4mJ4kYUa0RPh6Yl\nsJqEkjyQtGEdawbbssSNYnqIPz3vJ1kt/xMLdv+xcPrd1UQr/XjXiBZtu4C4\nnrevSZgjEJpqJtIGqD3yZ7cNInTMt6J43+wJlhWJTlNdG4bRY7Id8ab15I36\nUUbolh/by2+WCPt6Y2t3c/vdsaU+3jfYOz9v7SdE1E3Mws7R0bAKC5ooBwjs\nxCM6\r\n=hsMi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "e1839fb2fddb15b6e468ff5ec90086c3c59e084d", "scripts": {"test": "standard && tap test/*.js --coverage"}, "_npmUser": {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/iarna/gauge.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "A terminal based horizontal guage", "directories": {}, "_nodeVersion": "12.11.1", "dependencies": {"aproba": "^1.0.3 || ^2.0.0", "strip-ansi": "^3.0.1 || ^4.0.0", "wide-align": "^1.1.2", "has-unicode": "^2.0.1", "signal-exit": "^3.0.0", "string-width": "^1.0.1 || ^2.0.0", "color-support": "^1.1.2", "console-control-strings": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.0.1", "standard": "^11.0.1", "through2": "^2.0.0", "require-inject": "^1.4.0", "readable-stream": "^2.0.6"}, "_npmOperationalInternal": {"tmp": "tmp/gauge_3.0.0_1580507276052_0.6058092562342443", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "gauge", "version": "3.0.1", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "gauge@3.0.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "gimli01", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/gauge", "bugs": {"url": "https://github.com/npm/gauge/issues"}, "dist": {"shasum": "4bea07bcde3782f06dced8950e51307aa0f4a346", "tarball": "https://registry.npmjs.org/gauge/-/gauge-3.0.1.tgz", "fileCount": 19, "integrity": "sha512-6STz6KdQgxO4S/ko+AbjlFGGdGcknluoqU+79GOFCDqqyYj5OanQf9AjxwN0jCidtT+ziPMmPSt9E4hfQ0CwIQ==", "signatures": [{"sig": "MEYCIQDDlguJtGuktG4A9JueLH1bNv1qFIiB6S5K0YlNbRqDEwIhALGbCsZfV/k3TyHWpnoIQi6dmh3x8X4fAXbWSAg51r2k", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48569, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg/r5kCRA9TVsSAnZWagAAf1oP/iWxTsNebtCeaJ2rMgzd\npEkfQ3EndfR8LQvOuvNeezGalvsltYrZ9kT5iS0oLN25vXejdSv6DyeFzTWq\nviDOTAeJwl9+OADTtyYF2B+3w8bCbJNL3pPuobhe1/Aza6DDg0MpRLJAI53a\nlKTeaxJr+gbo+R0P452yFEbEUEu3vtnmy2LSoMcwRzEI0EZ8p4qGBUuINMCD\ny/WWOjlv3khaaIz+zz7D8/CDM0HqkT5aZhgdIdVI58kLVtAaUxytUTPE2VNH\ntPomlwDHVeavzGuTesKt+zBlbLLire1lKTebJnJ8lAFEkJASR7CuPsMgVeZQ\n+fRJNui9jSYxyCb4SG0RxeXmegJk93ivXvXMnG3EL+lq8KoOcZvEPWdELZtO\nZR+SVu697qhbpGU4FpSjwCIBFPPeVJdxMII1owwMDD6PgF4Csg3UWKwheO1d\nI3Y8m6sD4sQiRmaPh6tj4sO7RGhc6wVGNRXuHLGlAxqt0WnefuVq5+steAmQ\n1unnqlkJc/txSgTibevopNq3YPFxbZ9eKwRNRbs7owhYIteiYeZdF9T6jmZw\nnHjVo1kh3zyr9r/pKi4IDXvty53ioT4rqqsUZadT7dsRdPSPjTUA+KL54QGj\nkIhneVmKim34qf4DgZ2+SOOtBiX7qDztjPo9fmxnKm3qMZA6UQ0LxV5ZUIrU\nAIt5\r\n=bmTQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "bd3a1f2ecf80a57bb9f5ac963c4cd054cc960317", "scripts": {"test": "standard && tap test/*.js --coverage"}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/iarna/gauge.git", "type": "git"}, "_npmVersion": "7.20.1", "description": "A terminal based horizontal guage", "directories": {}, "_nodeVersion": "14.17.1", "dependencies": {"aproba": "^1.0.3 || ^2.0.0", "strip-ansi": "^3.0.1 || ^4.0.0", "wide-align": "^1.1.2", "has-unicode": "^2.0.1", "signal-exit": "^3.0.0", "string-width": "^1.0.1 || ^2.0.0", "color-support": "^1.1.2", "object-assign": "^4.1.1", "console-control-strings": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.0.1", "standard": "^11.0.1", "through2": "^2.0.0", "require-inject": "^1.4.0", "readable-stream": "^2.0.6"}, "_npmOperationalInternal": {"tmp": "tmp/gauge_3.0.1_1627307620180_0.1237829947703275", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "gauge", "version": "4.0.0", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "gauge@4.0.0", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/gauge", "bugs": {"url": "https://github.com/npm/gauge/issues"}, "tap": {"lines": 90, "branches": 79, "functions": 92, "statements": 89}, "dist": {"shasum": "afba07aa0374a93c6219603b1fb83eaa2264d8f8", "tarball": "https://registry.npmjs.org/gauge/-/gauge-4.0.0.tgz", "fileCount": 19, "integrity": "sha512-F8sU45yQpjQjxKkm1UOAhf0U/O0aFt//Fl7hsrNVto+patMHjs7dPI9mFOGUKbhrgKm0S3EjW3scMFuQmWSROw==", "signatures": [{"sig": "MEUCIQC6k0SjaUbqkCtCtHkzwP4JZEFpkswy6bElAp74vutU/QIgIms3ZCI2xDS/UDM7/o1IOcDHQusa6SbUGOF93gHVPxA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43884}, "main": "lib", "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}, "gitHead": "8ad1d598a6bf42e3435062e71fd5bc538e9fbe2b", "scripts": {"lint": "eslint '**/*.js'", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "npm-template-check", "posttest": "npm run lint", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/npm/gauge.git", "type": "git"}, "_npmVersion": "8.1.3", "description": "A terminal based horizontal gauge", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"aproba": "^1.0.3 || ^2.0.0", "ansi-regex": "^5.0.1", "strip-ansi": "^6.0.1", "wide-align": "^1.1.2", "has-unicode": "^2.0.1", "signal-exit": "^3.0.0", "string-width": "^4.2.3", "color-support": "^1.1.2", "console-control-strings": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.0.10", "readable-stream": "^3.6.0", "@npmcli/template-oss": "^2.3.0"}, "templateVersion": "2.3.0", "_npmOperationalInternal": {"tmp": "tmp/gauge_4.0.0_1636988740677_0.7528672038864028", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "gauge", "version": "3.0.2", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "gauge@3.0.2", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/gauge", "bugs": {"url": "https://github.com/npm/gauge/issues"}, "dist": {"shasum": "03bf4441c044383908bcfa0656ad91803259b395", "tarball": "https://registry.npmjs.org/gauge/-/gauge-3.0.2.tgz", "fileCount": 19, "integrity": "sha512-+5J6MS/5XksCuXq++uFRsnUd7Ovu1XenbeuIuNRJxYWjgQbPuFhT14lAvsWfqfAmnwluf1OwMjz39HjfLPci0Q==", "signatures": [{"sig": "MEUCIDWq68X8PwYFByw2HjVET740leh7i2Oz2OJ1pIYr1eYNAiEA2jD9Tnn4jCLZ+w3D8ereJ9iOk1dtTt/5lcYpkoExS4c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48549, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht5QiCRA9TVsSAnZWagAA9wYP/jAlv5tWeZ+bAzr/+uZQ\n1riLQuguIfgZaQ5XavqmK6Zl18aH74d5K75JGVJC/UIRatBlhtIi7QbhM3dR\nl5CLvWiUwPuA75yDqMqFcOcd13MDNLwIx7kpHx4JDNfRZPYO1H3DN8HikKpl\nYgflWbM0xMO3GG1fDUzWM/+driIuPfPUKTjgvW2k80JkvB4ryaDDV4JpIuPR\nZMr/pnchd+HJTGS498qoJmY2Mt4iX+rJAZ1PB76dHbBmp+NUFsX5ibdTSYmy\nFgsHQsnUt+Wz9//UtkTF9foOAOXnVLdI/s/W7id8KqHJK3wodTNGfvDLmPVc\n0z/ji7/NCMBDnNNa2381cmMO0fH5d38w+3MejHFj2OYHJfzpRFNPb00X6jLo\n9TztWNm4UmfEVA+SOrhagWLGOUybrJMPyrs8k+OCwritKAux7Z7veOo3dwQU\n7BHDZPEK8SZgtGlPuke+qxdj/OQYYnxph7tJVGa8z9HRkGgUTOiISkJfx3oy\na01NNPUyZTd2kN4NT1MAuPN51ZidankBvnCL3OL2FENsrxYhoXS6x9uNzhUV\nkAmRfDRUPKYPBWcWbHFwHVtXtc5i1R0v0ca2gzxbbM6/QgnbRAbqu8UneW/B\ntDASjXgDbYC+aqxsNFibiExMzwvO7mqguG2LyShoGT8GdAxBIQoZi/8HWyUb\nAxUf\r\n=nIiO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "a501d39bc26d4289241281fad16f9dd119669775", "scripts": {"test": "standard && tap test/*.js --coverage"}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/iarna/gauge.git", "type": "git"}, "_npmVersion": "8.2.0", "description": "A terminal based horizontal guage", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"aproba": "^1.0.3 || ^2.0.0", "strip-ansi": "^6.0.1", "wide-align": "^1.1.2", "has-unicode": "^2.0.1", "signal-exit": "^3.0.0", "string-width": "^4.2.3", "color-support": "^1.1.2", "object-assign": "^4.1.1", "console-control-strings": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.0.1", "standard": "^11.0.1", "through2": "^2.0.0", "require-inject": "^1.4.0", "readable-stream": "^2.0.6"}, "_npmOperationalInternal": {"tmp": "tmp/gauge_3.0.2_1639420962539_0.9282811768654349", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "gauge", "version": "4.0.1", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "gauge@4.0.1", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/gauge", "bugs": {"url": "https://github.com/npm/gauge/issues"}, "tap": {"lines": 90, "branches": 79, "functions": 92, "statements": 89}, "dist": {"shasum": "82984bc08c90357d60b0a46c03a296beb1affec4", "tarball": "https://registry.npmjs.org/gauge/-/gauge-4.0.1.tgz", "fileCount": 19, "integrity": "sha512-zJ4jePUHR8cceduZ53b6temRalyGpkC2Kc2r3ecNphmL+uWNoJ3YcOcUjpbG6WwoE/Ef6/+aEZz63neI2WIa1Q==", "signatures": [{"sig": "MEQCIFs8I00olaywhKw7wOAwUuNxg6WLqNkPFfzReCeslZ7jAiBCxE2UEDB76/0rHQvaRxuKcavx1qsnZ0u4QkiIeuVNrg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43947, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiC/NuCRA9TVsSAnZWagAAFSwP/RVTdhmeglSDNKgImXV6\nsnCLAYA+8srKOJ4IhxhviQa7aAol4apUcx0wVEb63YEkjvZXVqnHEwbBOzty\nK3B0ArUVqqte5gi5/5KbPnlW5tnKYNKEV4cZNjFXPOLwYVguE+RuajRGGLWs\nVooR4h7hL4yH8mjtENxoIrmX5s0fiWNLx8TjC4EQWiByX+oRn/uTA9gtgvZQ\n4zd5xLoJ/m0jheom+Cj+cSOzDISWYL9C5+uruWHAnkmp3lC584VUNlNJ/V48\nEiY+GqhfkTH9/SK9zxD2h0s4C0LSa1p4ISn871gzhGQcD4mZeu05/vrIm1M2\nzJq5069IAMfeIbC2lMETGu9v21x2J75PKHJfI+8JRzpG0VGcNDtNtsSUDPsb\npUQ6SwJWeKohsfjpFc8ANMhQpoQAS1kwvVQtYkeUrDOPZ9NlnyYTdmTCcJN+\nMkEz364D2dTayvtxkbPKIjTCHlO8Qp6bZNBbDTg+nkpCh1iEtSwG4gwMnbZw\nss0XLvhRgcLx6viICUmBnZLI+Jb2pmaATuAcy3SXL690k1zJiBCCkTz3ivzZ\nohhSxsJqtHmoDFPD4IEpMG5GBkv1xiImUUu79RQ1ACkFeE4f8VoIQvHJcwVA\nU62F2/Rbf6hSbGYX4chPxdN1LcSTBRnkfWNMwodj8p13cyYirSnRdsJKoJ7G\nCJB3\r\n=bjp6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib", "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}, "gitHead": "bd57beb4f7227cb5bfe921adf72b531840ae69ba", "scripts": {"lint": "eslint '**/*.js'", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "npm-template-check", "posttest": "npm run lint", "preversion": "npm test", "postversion": "npm publish", "template-copy": "npm-template-copy --force", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "nlf", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/npm/gauge.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "A terminal based horizontal gauge", "directories": {}, "templateOSS": {"version": "2.7.1"}, "_nodeVersion": "16.13.2", "dependencies": {"aproba": "^1.0.3 || ^2.0.0", "ansi-regex": "^5.0.1", "strip-ansi": "^6.0.1", "wide-align": "^1.1.2", "has-unicode": "^2.0.1", "signal-exit": "^3.0.0", "string-width": "^4.2.3", "color-support": "^1.1.2", "console-control-strings": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.0.10", "readable-stream": "^3.6.0", "@npmcli/template-oss": "^2.7.1"}, "_npmOperationalInternal": {"tmp": "tmp/gauge_4.0.1_1644950382437_0.869823947170203", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "gauge", "version": "4.0.2", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "gauge@4.0.2", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/gauge", "bugs": {"url": "https://github.com/npm/gauge/issues"}, "tap": {"lines": 90, "branches": 79, "functions": 92, "statements": 89}, "dist": {"shasum": "c3777652f542b6ef62797246e8c7caddecb32cc7", "tarball": "https://registry.npmjs.org/gauge/-/gauge-4.0.2.tgz", "fileCount": 19, "integrity": "sha512-aSPRm2CvA9R8QyU5eXMFPd+cYkyxLsXHd2l5/FOH2V/eml//M04G6KZOmTap07O1PvEwNcl2NndyLfK8g3QrKA==", "signatures": [{"sig": "MEUCIB4P2FaXBw1xdJYzYyONaBIu0ZXGlc41iFqhYSjBjZwBAiEAifuUbd3XBnN++vD8fsrKjcxPzsKtxs7zAeOwi50eMFQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43946, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFVy4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrXfQ/9EZYcKodqecy+kshRTWXgLNjgsXPYfrst6sk0IPH07KUCVyxa\r\nQHMP+xRHSRj2sRZApFlQQc7KHT0pnKc2Ayb7nbHbfevL0Aa92wJyg/0thq43\r\njUl4tB5Rg0OxHOUmxvhdlKb7DeAkJJYPFha9spUG3t5LctctBxnBNDfk3MCq\r\n5OinpMgMFUiFLCuY3OO584xTPmEWIbyhFKYXWMo+xCU86xoo8GpCYLQa0gNe\r\n8v7Oz5LAJC+2xPSP41DxOlV22bN3XKCCB8ib8NOzmtpH9InqCFheGCRBh/48\r\no7HaDydGH+Bor4yyJ428FKU37jnrcv92iN34rTU89Bpsxn4vyf3JprmcWURG\r\njQdULec0vH8fiY64Hpv9rDuHocwILsNTrvMa5n+ku2r3QI1CebZ7xmMLYsxp\r\nJpiNEgS4jin0Mr6reR9rTDgZ9Gb5Ops+LkCewUwgUHTchsfBopLsSnRZoluI\r\nII8Th1lKWviQMu9aUOExz7CmA50JjwjdRr1zoW6j1x0oHPC2ZiOzCmy3oMeR\r\nc/iOyQpT83e+8PMEC1yorppaTn9TM9iJM8Jx19DqmP8io5Zi9ctfuJu/qcHg\r\nh1VzUhYXcgTgAEMWUOVn3sNeTfVaaimh6BmolHaE4CtOFECgKo88qCpzldZZ\r\nMzDQwD9Q33RIuN1Cqb9hKWd5klf08GH5OMk=\r\n=s26P\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib", "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}, "gitHead": "b84f00572e4cd7aa154f87fa467da7e75d136f77", "scripts": {"lint": "eslint '**/*.js'", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "npm-template-check", "posttest": "npm run lint", "preversion": "npm test", "postversion": "npm publish", "template-copy": "npm-template-copy --force", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/npm/gauge.git", "type": "git"}, "_npmVersion": "8.5.1", "description": "A terminal based horizontal gauge", "directories": {}, "templateOSS": {"version": "2.7.1"}, "_nodeVersion": "16.14.0", "dependencies": {"aproba": "^1.0.3 || ^2.0.0", "ansi-regex": "^5.0.1", "strip-ansi": "^6.0.1", "wide-align": "^1.1.5", "has-unicode": "^2.0.1", "signal-exit": "^3.0.7", "string-width": "^4.2.3", "color-support": "^1.1.3", "console-control-strings": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "readable-stream": "^3.6.0", "@npmcli/template-oss": "^2.7.1"}, "_npmOperationalInternal": {"tmp": "tmp/gauge_4.0.2_1645567160373_0.1884965320494476", "host": "s3://npm-registry-packages"}}, "4.0.3": {"name": "gauge", "version": "4.0.3", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "gauge@4.0.3", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/gauge", "bugs": {"url": "https://github.com/npm/gauge/issues"}, "tap": {"lines": 90, "branches": 79, "functions": 92, "statements": 89}, "dist": {"shasum": "286cf105c1962c659f0963058fb05116c1b82d3f", "tarball": "https://registry.npmjs.org/gauge/-/gauge-4.0.3.tgz", "fileCount": 18, "integrity": "sha512-ICw1DhAwMtb22rYFwEHgJcx1JCwJGv3x6G0OQUq56Nge+H4Q8JEwr8iveS0XFlsUNSI67F5ffMGK25bK4Pmskw==", "signatures": [{"sig": "MEYCIQCjV/l82wmNyouwA6Qp8wawX2NT+WML9GFRC5AMWCAfhwIhAPexKoVTIl77l4eT+p3gB7OK/3lOsFiMena0TXa+0LXB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43007, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiKiwWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq3SA/+LxUIpUjNclkHEKdetgfOR6jg0uv5Q+bZ6F3cyUyjHiOircew\r\nCAK8GO+PAxNW3bdT8p/j2DZgoiYcBjQicdzxT1oHA7uL4q1jEpaVHMuyZAyJ\r\nIdQWnmFh7biOE6lJ52D4+r/Vz4DWcmNgn0eWix20M5ls8A9OaezKzQP4Dfno\r\n+tQyzLNqC7QNZzEevW+eZ8linOrOCpwpGKVapsyIK92Hn3ymHsUCIQ8QzJhN\r\nHQdd2aT+qgtGiD5e4GUjfRoh3zlfMin/sCKyjAHZY6pe+RPqh393EnL37gjg\r\npSmyDsjhmCmzqHMvYnVOWvCceVjBVl3d59BEbTMxC7xYagpfWOQhSEc4aKd0\r\nJrkDwwJDy+RjZzI0UkL34Urm0nYby2SQa+vfNpj+dLnp5UqIpLv5xdoNoiE7\r\nTPEpj4bk/GuMO2CaSDWz9z++fRUoOdf37GqbCvglOA2sGfm3mgysvQeiyZB8\r\ncoBd9pxScPhtYj6smS71DqqYth9MqCNKqhF/TCUNYvNNcHe9XqM1q72AULwE\r\npZTOWwayyrC1utYC73m3h9Oi77/jQMKOfhd9gD2BdS0HMLM83Xwc89GlcmPL\r\n8FSTenyyyn/VCFBr74/vRiP14HISzVBmNrQXjVa6yYpZYiTMP2OMR009CYTg\r\no/7RQZyXCrUV8hiIgxJG+WSFBE/o7M2MBG0=\r\n=T+kV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib", "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}, "gitHead": "f8368ef729722c59b7b1a8dbd723ea0ed3efa8a1", "scripts": {"lint": "eslint '**/*.js'", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "npm-template-check", "posttest": "npm run lint", "preversion": "npm test", "postversion": "npm publish", "template-copy": "npm-template-copy --force", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/npm/gauge.git", "type": "git"}, "_npmVersion": "8.5.3", "description": "A terminal based horizontal gauge", "directories": {}, "templateOSS": {"version": "2.9.2"}, "_nodeVersion": "16.14.0", "dependencies": {"aproba": "^1.0.3 || ^2.0.0", "strip-ansi": "^6.0.1", "wide-align": "^1.1.5", "has-unicode": "^2.0.1", "signal-exit": "^3.0.7", "string-width": "^4.2.3", "color-support": "^1.1.3", "console-control-strings": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "readable-stream": "^3.6.0", "@npmcli/template-oss": "^2.9.2"}, "_npmOperationalInternal": {"tmp": "tmp/gauge_4.0.3_1646930966167_0.2782764649009768", "host": "s3://npm-registry-packages"}}, "4.0.4": {"name": "gauge", "version": "4.0.4", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "gauge@4.0.4", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/gauge", "bugs": {"url": "https://github.com/npm/gauge/issues"}, "tap": {"lines": 90, "branches": 79, "functions": 92, "statements": 89}, "dist": {"shasum": "52ff0652f2bbf607a989793d53b751bef2328dce", "tarball": "https://registry.npmjs.org/gauge/-/gauge-4.0.4.tgz", "fileCount": 18, "integrity": "sha512-f9m+BEN5jkg6a0fZjleidjN51VE1X+mPFQ2DJ0uv1V39oCLCbsGe6yjbBnp7eK7z/+GAon99a3nHuqbuuthyPg==", "signatures": [{"sig": "MEUCIG+tCiwQOyzl/BHfVee/C2XNeJ+eHbpRC7+TDuPBnX52AiEAt2AC9sJUelRE0EaoJOvM0q7tCu1wY7KhKjMjOIM1Wgc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43163, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiQhydACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoJBQ//Svi5B6D0+TvvqYf+NYu7+gMdjoytCWNLH9k0IRDFYjYN+XkC\r\nuRqSj4FjcjX4md3upFmDjgUbL2IrEaPN6u7bYEtNUPuFhLASkcIzggclASdF\r\nylcGKYlw8w5VrjkDXt2OvniUDISvWWgkKg5yPKH3ZpN2k2Yo4sBPEuRk+jn7\r\nKEz/yICUrAKd7G+iXz5rlFxU0JxXG6OTYLYqK+0E7CbKi1Kej9zA4JXuoUBC\r\nNpGE9pYeiQiuvsN/phmpJVB9To93hAsvHCAMHovNyxv+gQ/Vfbh5ac+f7rRT\r\ntOfr+zfyMsn7NpatL5g4VbL8pksCJB5CYs1AfPT31TwBUrb4qUGgBDNMM0/a\r\nSaPm6NhIpTv+TfogGkigN0qrrOiYVx45w47ba7J16ZOvVkSZ8qwtREXuq0rP\r\njjTd5MtsjQw+2VjoTpFyybaOdDBH0+MTEcSQ5pqBHqYeX8QonLf8UIKsuhSq\r\nsSlehgMLVfZ4WTaaT4rIhhqL4A1glbiYX4XqG8FVbMDW0rNtcGF4agER5L+w\r\nh1FCDegPbuNhjo1u05sFpYlbyi1h20DXLhEK6+18lRPxDU8m+WfdrMla3wC5\r\nxunoPQjkVTUdlOYet1MxNEdUFaHVaglPLXbGUjEl3agRiPYccSNRujzKBKX2\r\naLqbs6pJXekim9wMjnoC3YRigdC5a+fPlvs=\r\n=mx1f\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib", "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "gitHead": "54452cfada7a690edb7e0fd4b6fabcef7ba1d567", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/npm/gauge.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "A terminal based horizontal gauge", "directories": {}, "templateOSS": {"version": "3.2.0", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "16.14.2", "dependencies": {"aproba": "^1.0.3 || ^2.0.0", "strip-ansi": "^6.0.1", "wide-align": "^1.1.5", "has-unicode": "^2.0.1", "signal-exit": "^3.0.7", "string-width": "^4.2.3", "color-support": "^1.1.3", "console-control-strings": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "readable-stream": "^3.6.0", "@npmcli/template-oss": "3.2.0", "@npmcli/eslint-config": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/gauge_4.0.4_1648499869680_0.7890415207504722", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "gauge", "version": "5.0.0", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "gauge@5.0.0", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/gauge", "bugs": {"url": "https://github.com/npm/gauge/issues"}, "tap": {"lines": 90, "nyc-arg": ["--exclude", "tap-snapshots/**"], "branches": 79, "functions": 92, "statements": 89}, "dist": {"shasum": "e270ca9d97dae84abf64e5277ef1ebddc7dd1e2f", "tarball": "https://registry.npmjs.org/gauge/-/gauge-5.0.0.tgz", "fileCount": 18, "integrity": "sha512-0s5T5eciEG7Q3ugkxAkFtaDhrrhXsCRivA5y8C9WMHWuI8UlMOJg7+Iwf7Mccii+Dfs3H5jHepU0joPVyQU0Lw==", "signatures": [{"sig": "MEQCICc4OmYVBTVxgn0aLQIBF2WpOSEp44rol8SpZ2FKOkX2AiATrIsx527Y8IaxlZRohNyGuZg7s9xcOyT90pHI7Bspsw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43112, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSPIaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrn2Q//Q96qWA2RTxtgipAra9E2dqn9TsUwDo9C4iQFhAjE7vSia2ZZ\r\nmBCaIPBTNbYDT1CWQmPnHMEJBZ0j1amwab/ZG4nRyWjSSmgnkI3cdp5JtmX8\r\n1zb9dZ39Vq1xt51rf1V/5XszLvUfm7qwZKnjROOyafX/KdoYexhQ1hFr0tDc\r\nzGe7UyMJZcsXiVAYpLEjZfy4SWGtWOjCYq5QvKP3fHcJC0kwlaGgr8+FKNe/\r\nADJ7X/yA49NGNV0Ffe6tHJ3AuorCQX85Tv0LNQWreY82neaF5LplF/fRV/It\r\nmOjKm9B1IH+ZIGLHbrwKhCXBV2Op8bkB01jj56GkVAKsfoU8FxwzI7LK/9dC\r\nbWYdPY9/mnXoG2oXMSm7Mqw/HF0wGYWeFh3fwJVu/oYeDCAtzG+ij8o/aEyR\r\nwE4wQklDxXYnKHZ2dBglPTk/MBYlXSNWWl+m/jUinTiH9BBs75qShPHj98pS\r\nJvM9UEJWf3XkbP2fv53Le5dR6inuTLpy+oKVFj+XF664ygZoccGhF8za763K\r\nNOHwNkaontYAV4lx4i1ebLMCzNwTwk6vJP/z6LtbonsXGD+cPsD6/Y3leUoa\r\nAQBV9dXG2y7Eev7rxGin8W4cqeXIbMdKObywhCBWoK+WMHquCXOcwbuy9iD0\r\n6c/3wOGC+nFrIXfof+IOp/3eJifR/gRxSgw=\r\n=g+G+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "gitHead": "843763ffb9f845052678a85c8fedf10d8dce965f", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/npm/gauge.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "A terminal based horizontal gauge", "directories": {}, "templateOSS": {"version": "4.5.1", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.10.0", "dependencies": {"aproba": "^1.0.3 || ^2.0.0", "strip-ansi": "^6.0.1", "wide-align": "^1.1.5", "has-unicode": "^2.0.1", "signal-exit": "^3.0.7", "string-width": "^4.2.3", "color-support": "^1.1.3", "console-control-strings": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "readable-stream": "^4.0.0", "@npmcli/template-oss": "4.5.1", "@npmcli/eslint-config": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/gauge_5.0.0_1665724954016_0.5108192013292334", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "gauge", "version": "5.0.1", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "gauge@5.0.1", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/gauge", "bugs": {"url": "https://github.com/npm/gauge/issues"}, "tap": {"lines": 90, "nyc-arg": ["--exclude", "tap-snapshots/**"], "branches": 79, "functions": 92, "statements": 89}, "dist": {"shasum": "1efc801b8ff076b86ef3e9a7a280a975df572112", "tarball": "https://registry.npmjs.org/gauge/-/gauge-5.0.1.tgz", "fileCount": 18, "integrity": "sha512-CmykPMJGuNan/3S4kZOpvvPYSNqSHANiWnh9XcMU2pSjtBfF0XzZ2p1bFAxTbnFxyBuPxQYHhzwaoOmUdqzvxQ==", "signatures": [{"sig": "MEUCIQCdBiVWmiA/bAqlWrtcDAJcLR7FDVXwa24sSM41Kyc13wIgeW563jXYOnnlyf+b2E99Lae3GZ83PhRg7fiMG9si0wo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/gauge@5.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 43144, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkSW4MACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp2oA//QURUdEj3vLNSNXcztbRJ+5FlJFz43VWNKJFsyrA3YLmLcbc4\r\nWnDtWcSGkyXJLOHNJFkYurhx27K8SEw5Fw0+KHAx0xST383ggWmf2+vUYZ8p\r\nOKXNgjkYjMx/LzdGN8EbKlDfBcYX0GFwS7vBceAPn71odk2kQwliBznqm3x/\r\nMzSm8pmU2KEzbiezjCsNqmunjZC3DMxywj9kBoRilUPvSTSZJ17YBH4tDqK9\r\nEVVlm/ZDXHYd7fQ8OcLNnDdB/997wE+WQNaClU3xNq7pULZwf/Gsy1731F+C\r\nCFs1509l9Zgo0qneyiPQ4ezdjr594eV7slc2WX82468ASZF77ua3lHLlbzX+\r\noLOFqdqNR2aitMy4EduFZlZv6RI7CI2DSJIorks7oGqFh+tGrC2rBn9Dub+g\r\nLPa38t3YaI8izs0eqkpz9enSQ6Pk9T4wfz+ukuPMYAnNxFScaQYDUtPOaBu/\r\n1riUKlAK/zMacmD+g8J9aaIqyhr53wGVbU0XBY/bFj1r9HDvbXReuh9oL2z4\r\n+UHpuNUxnIh4Mt6h88Mjfcy8wzbKjTXETTmgeBvbvQMeHlaq7lSt1Zey6nP/\r\nBgxEgDcESljFruzO6seFZA/fKVDLdRV9YnWZMFuiHcwNxlUxCfOZichLsL5f\r\njjYrB1wirMbDxliHuLJi8gITpsVF8dunRyE=\r\n=hdJK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "gitHead": "abd81c8a7e81a20c7b1e3c332a1599393181acde", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/npm/gauge.git", "type": "git"}, "_npmVersion": "9.6.5", "description": "A terminal based horizontal gauge", "directories": {}, "templateOSS": {"publish": "true", "version": "4.14.1", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.16.0", "dependencies": {"aproba": "^1.0.3 || ^2.0.0", "strip-ansi": "^6.0.1", "wide-align": "^1.1.5", "has-unicode": "^2.0.1", "signal-exit": "^4.0.1", "string-width": "^4.2.3", "color-support": "^1.1.3", "console-control-strings": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "readable-stream": "^4.0.0", "@npmcli/template-oss": "4.14.1", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/gauge_5.0.1_1682533900320_0.676143878294744", "host": "s3://npm-registry-packages"}}, "5.0.2": {"name": "gauge", "version": "5.0.2", "keywords": ["progressbar", "progress", "gauge"], "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "gauge@5.0.2", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/gauge", "bugs": {"url": "https://github.com/npm/gauge/issues"}, "tap": {"lines": 90, "nyc-arg": ["--exclude", "tap-snapshots/**"], "branches": 79, "functions": 92, "statements": 89}, "dist": {"shasum": "7ab44c11181da9766333f10db8cd1e4b17fd6c46", "tarball": "https://registry.npmjs.org/gauge/-/gauge-5.0.2.tgz", "fileCount": 18, "integrity": "sha512-pMaFftXPtiGIHCJHdcUUx9Rby/rFT/Kkt3fIIGCs+9PMDIljSyRiqraTlxNtBReJRDfUefpa263RQ3vnp5G/LQ==", "signatures": [{"sig": "MEUCIHAdAQlp6jBaLQobhULHE2+mhtObvmyzMcDmTESm7mPUAiEAqSnaxA0xzzB16ic9X639ZPAW+4bHqJsfQg1Xnm6Ve8E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/gauge@5.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 43162}, "main": "lib", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "gitHead": "f8092518a47ac6a96027ae3ad97d0251ffe7643b", "scripts": {"lint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/npm/gauge.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A terminal based horizontal gauge", "directories": {}, "templateOSS": {"publish": "true", "version": "4.22.0", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "22.1.0", "dependencies": {"aproba": "^1.0.3 || ^2.0.0", "strip-ansi": "^6.0.1", "wide-align": "^1.1.5", "has-unicode": "^2.0.1", "signal-exit": "^4.0.1", "string-width": "^4.2.3", "color-support": "^1.1.3", "console-control-strings": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "readable-stream": "^4.0.0", "@npmcli/template-oss": "4.22.0", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/gauge_5.0.2_1714785038720_0.32677979782964606", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2015-01-05T08:44:24.415Z", "modified": "2024-07-31T15:24:05.328Z", "1.0.0": "2015-01-05T08:44:24.415Z", "1.0.1": "2015-01-05T22:59:23.490Z", "1.0.2": "2015-01-07T17:06:20.846Z", "1.1.0": "2015-01-29T21:14:00.911Z", "1.2.0": "2015-02-28T23:00:19.506Z", "1.2.1": "2015-07-07T20:41:59.706Z", "1.2.2": "2015-07-08T02:10:06.936Z", "1.2.3": "2016-01-12T22:05:24.139Z", "1.2.4": "2016-01-13T01:16:59.137Z", "1.2.5": "2016-01-26T22:34:06.607Z", "1.2.6": "2016-02-18T22:03:32.492Z", "1.2.7": "2016-02-18T22:43:31.980Z", "2.0.0": "2016-02-21T01:14:51.530Z", "2.1.0": "2016-03-01T23:13:11.660Z", "2.2.0": "2016-03-15T22:50:45.443Z", "2.2.1": "2016-03-15T22:53:29.707Z", "2.3.0": "2016-04-29T01:52:20.907Z", "2.3.1": "2016-04-29T02:03:06.045Z", "2.4.0": "2016-05-10T01:23:55.461Z", "2.5.0": "2016-06-07T00:36:10.716Z", "2.5.1": "2016-06-13T22:50:29.778Z", "2.5.2": "2016-06-15T21:28:13.668Z", "2.5.3": "2016-06-16T04:19:17.086Z", "2.6.0": "2016-06-16T08:56:12.377Z", "2.7.0": "2016-11-03T21:35:03.796Z", "2.7.1": "2016-11-03T22:03:12.902Z", "2.7.2": "2016-12-04T11:30:25.029Z", "2.7.3": "2017-02-08T00:53:51.651Z", "2.7.4": "2017-04-21T23:06:17.925Z", "3.0.0": "2020-01-31T21:47:56.221Z", "3.0.1": "2021-07-26T13:53:40.356Z", "4.0.0": "2021-11-15T15:05:40.853Z", "3.0.2": "2021-12-13T18:42:42.713Z", "4.0.1": "2022-02-15T18:39:42.680Z", "4.0.2": "2022-02-22T21:59:20.536Z", "4.0.3": "2022-03-10T16:49:26.286Z", "4.0.4": "2022-03-28T20:37:49.810Z", "5.0.0": "2022-10-14T05:22:34.193Z", "5.0.1": "2023-04-26T18:31:40.449Z", "5.0.2": "2024-05-04T01:10:38.906Z"}, "bugs": {"url": "https://github.com/npm/gauge/issues"}, "author": {"name": "GitHub Inc."}, "license": "ISC", "homepage": "https://github.com/npm/gauge", "keywords": ["progressbar", "progress", "gauge"], "repository": {"url": "git+https://github.com/npm/gauge.git", "type": "git"}, "description": "A terminal based horizontal gauge", "maintainers": [{"email": "<EMAIL>", "name": "hashtagchris"}, {"email": "<EMAIL>", "name": "reggi"}, {"email": "<EMAIL>", "name": "npm-cli-ops"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "fritzy"}, {"email": "<EMAIL>", "name": "gar"}], "readme": "gauge\n=====\n\nA nearly stateless terminal based horizontal gauge / progress bar.\n\n```javascript\nvar Gauge = require(\"gauge\")\n\nvar gauge = new Gauge()\n\ngauge.show(\"working…\", 0)\nsetTimeout(() => { gauge.pulse(); gauge.show(\"working…\", 0.25) }, 500)\nsetTimeout(() => { gauge.pulse(); gauge.show(\"working…\", 0.50) }, 1000)\nsetTimeout(() => { gauge.pulse(); gauge.show(\"working…\", 0.75) }, 1500)\nsetTimeout(() => { gauge.pulse(); gauge.show(\"working…\", 0.99) }, 2000)\nsetTimeout(() => gauge.hide(), 2300)\n```\n\nSee also the [demos](docs/demo.js):\n\n![](./docs/gauge-demo.gif)\n\n\n### CHANGES FROM 1.x\n\nGauge 2.x is breaking release, please see the [changelog] for details on\nwhat's changed if you were previously a user of this module.\n\n[changelog]: CHANGELOG.md\n\n### THE GAUGE CLASS\n\nThis is the typical interface to the module– it provides a pretty\nfire-and-forget interface to displaying your status information.\n\n```\nvar Gauge = require(\"gauge\")\n\nvar gauge = new Gauge([stream], [options])\n```\n\n* **stream** – *(optional, default STDERR)* A stream that progress bar\n  updates are to be written to.  Gauge honors backpressure and will pause\n  most writing if it is indicated.\n* **options** – *(optional)* An option object.\n\nConstructs a new gauge. Gauges are drawn on a single line, and are not drawn\nif **stream** isn't a tty and a tty isn't explicitly provided.\n\nIf **stream** is a terminal or if you pass in **tty** to **options** then we\nwill detect terminal resizes and redraw to fit.  We do this by watching for\n`resize` events on the tty.  (To work around a bug in versions of Node prior\nto 2.5.0, we watch for them on stdout if the tty is stderr.) Resizes to\nlarger window sizes will be clean, but shrinking the window will always\nresult in some cruft.\n\n**IMPORTANT:** If you previously were passing in a non-tty stream but you still\nwant output (for example, a stream wrapped by the `ansi` module) then you\nneed to pass in the **tty** option below, as `gauge` needs access to\nthe underlying tty in order to do things like terminal resizes and terminal\nwidth detection.\n\nThe **options** object can have the following properties, all of which are\noptional:\n\n* **updateInterval**: How often gauge updates should be drawn, in milliseconds.\n* **fixedFramerate**: Defaults to false on node 0.8, true on everything\n  else.  When this is true a timer is created to trigger once every\n  `updateInterval` ms, when false, updates are printed as soon as they come\n  in but updates more often than `updateInterval` are ignored.  The reason\n  0.8 doesn't have this set to true is that it can't `unref` its timer and\n  so it would stop your program from exiting– if you want to use this\n  feature with 0.8 just make sure you call `gauge.disable()` before you\n  expect your program to exit.\n* **themes**: A themeset to use when selecting the theme to use. Defaults\n  to `gauge/themes`, see the [themes] documentation for details.\n* **theme**: Select a theme for use, it can be a:\n  * Theme object, in which case the **themes** is not used.\n  * The name of a theme, which will be looked up in the current *themes*\n    object.\n  * A configuration object with any of `hasUnicode`, `hasColor` or\n    `platform` keys, which if will be used to override our guesses when making\n    a default theme selection.\n\n  If no theme is selected then a default is picked using a combination of our\n  best guesses at your OS, color support and unicode support.\n* **template**: Describes what you want your gauge to look like.  The\n  default is what npm uses.  Detailed [documentation] is later in this\n  document.\n* **hideCursor**: Defaults to true.  If true, then the cursor will be hidden\n  while the gauge is displayed.\n* **tty**: The tty that you're ultimately writing to.  Defaults to the same\n  as **stream**.  This is used for detecting the width of the terminal and\n  resizes. The width used is `tty.columns - 1`. If no tty is available then\n  a width of `79` is assumed.\n* **enabled**: Defaults to true if `tty` is a TTY, false otherwise.  If true\n  the gauge starts enabled.  If disabled then all update commands are\n  ignored and no gauge will be printed until you call `.enable()`.\n* **Plumbing**: The class to use to actually generate the gauge for\n  printing.  This defaults to `require('gauge/plumbing')` and ordinarily you\n  shouldn't need to override this.\n* **cleanupOnExit**: Defaults to true. Ordinarily we register an exit\n  handler to make sure your cursor is turned back on and the progress bar\n  erased when your process exits, even if you Ctrl-C out or otherwise exit\n  unexpectedly. You can disable this and it won't register the exit handler.\n\n[has-unicode]: https://www.npmjs.com/package/has-unicode\n[themes]: #themes\n[documentation]: #templates\n\n#### `gauge.show(section | status, [completed])`\n\nThe first argument is either the section, the name of the current thing\ncontributing to progress, or an object with keys like **section**,\n**subsection** & **completed** (or any others you have types for in a custom\ntemplate).  If you don't want to update or set any of these you can pass\n`null` and it will be ignored.\n\nThe second argument is the percent completed as a value between 0 and 1.\nWithout it, completion is just not updated. You'll also note that completion\ncan be passed in as part of a status object as the first argument. If both\nit and the completed argument are passed in, the completed argument wins.\n\n#### `gauge.hide([cb])`\n\nRemoves the gauge from the terminal.  Optionally, callback `cb` after IO has\nhad an opportunity to happen (currently this just means after `setImmediate`\nhas called back.)\n\nIt turns out this is important when you're pausing the progress bar on one\nfilehandle and printing to another– otherwise (with a big enough print) node\ncan end up printing the \"end progress bar\" bits to the progress bar filehandle\nwhile other stuff is printing to another filehandle. These getting interleaved\ncan cause corruption in some terminals.\n\n#### `gauge.pulse([subsection])`\n\n* **subsection** – *(optional)* The specific thing that triggered this pulse\n\nSpins the spinner in the gauge to show output.  If **subsection** is\nincluded then it will be combined with the last name passed to `gauge.show`.\n\n#### `gauge.disable()`\n\nHides the gauge and ignores further calls to `show` or `pulse`.\n\n#### `gauge.enable()`\n\nShows the gauge and resumes updating when `show` or `pulse` is called.\n\n#### `gauge.isEnabled()`\n\nReturns true if the gauge is enabled.\n\n#### `gauge.setThemeset(themes)`\n\nChange the themeset to select a theme from. The same as the `themes` option\nused in the constructor. The theme will be reselected from this themeset.\n\n#### `gauge.setTheme(theme)`\n\nChange the active theme, will be displayed with the next show or pulse. This can be:\n\n* Theme object, in which case the **themes** is not used.\n* The name of a theme, which will be looked up in the current *themes*\n  object.\n* A configuration object with any of `hasUnicode`, `hasColor` or\n  `platform` keys, which if will be used to override our guesses when making\n  a default theme selection.\n\nIf no theme is selected then a default is picked using a combination of our\nbest guesses at your OS, color support and unicode support.\n\n#### `gauge.setTemplate(template)`\n\nChange the active template, will be displayed with the next show or pulse\n\n### Tracking Completion\n\nIf you have more than one thing going on that you want to track completion\nof, you may find the related [are-we-there-yet] helpful.  It's `change`\nevent can be wired up to the `show` method to get a more traditional\nprogress bar interface.\n\n[are-we-there-yet]: https://www.npmjs.com/package/are-we-there-yet\n\n### THEMES\n\n```\nvar themes = require('gauge/themes')\n\n// fetch the default color unicode theme for this platform\nvar ourTheme = themes({hasUnicode: true, hasColor: true})\n\n// fetch the default non-color unicode theme for osx\nvar ourTheme = themes({hasUnicode: true, hasColor: false, platform: 'darwin'})\n\n// create a new theme based on the color ascii theme for this platform\n// that brackets the progress bar with arrows\nvar ourTheme = themes.newTheme(themes({hasUnicode: false, hasColor: true}), {\n  preProgressbar: '→',\n  postProgressbar: '←'\n})\n```\n\nThe object returned by `gauge/themes` is an instance of the `ThemeSet` class.\n\n```\nvar ThemeSet = require('gauge/theme-set')\nvar themes = new ThemeSet()\n// or\nvar themes = require('gauge/themes')\nvar mythemes = themes.newThemeSet() // creates a new themeset based on the default themes\n```\n\n#### themes(opts)\n#### themes.getDefault(opts)\n\nTheme objects are a function that fetches the default theme based on\nplatform, unicode and color support.\n\nOptions is an object with the following properties:\n\n* **hasUnicode** - If true, fetch a unicode theme, if no unicode theme is\n  available then a non-unicode theme will be used.\n* **hasColor** - If true, fetch a color theme, if no color theme is\n  available a non-color theme will be used.\n* **platform** (optional) - Defaults to `process.platform`.  If no\n  platform match is available then `fallback` is used instead.\n\nIf no compatible theme can be found then an error will be thrown with a\n`code` of `EMISSINGTHEME`.\n\n#### themes.addTheme(themeName, themeObj)\n#### themes.addTheme(themeName, [parentTheme], newTheme)\n\nAdds a named theme to the themeset.  You can pass in either a theme object,\nas returned by `themes.newTheme` or the arguments you'd pass to\n`themes.newTheme`.\n\n#### themes.getThemeNames()\n\nReturn a list of all of the names of the themes in this themeset. Suitable\nfor use in `themes.getTheme(…)`.\n\n#### themes.getTheme(name)\n\nReturns the theme object from this theme set named `name`.\n\nIf `name` does not exist in this themeset an error will be thrown with\na `code` of `EMISSINGTHEME`.\n\n#### themes.setDefault([opts], themeName)\n\n`opts` is an object with the following properties.\n\n* **platform** - Defaults to `'fallback'`.  If your theme is platform\n  specific, specify that here with the platform from `process.platform`, eg,\n  `win32`, `darwin`, etc.\n* **hasUnicode** - Defaults to `false`. If your theme uses unicode you\n  should set this to true.\n* **hasColor** - Defaults to `false`.  If your theme uses color you should\n  set this to true.\n\n`themeName` is the name of the theme (as given to `addTheme`) to use for\nthis set of `opts`.\n\n#### themes.newTheme([parentTheme,] newTheme)\n\nCreate a new theme object based on `parentTheme`.  If no `parentTheme` is\nprovided then a minimal parentTheme that defines functions for rendering the\nactivity indicator (spinner) and progress bar will be defined. (This\nfallback parent is defined in `gauge/base-theme`.)\n\nnewTheme should be a bare object– we'll start by discussing the properties\ndefined by the default themes:\n\n* **preProgressbar** - displayed prior to the progress bar, if the progress\n  bar is displayed.\n* **postProgressbar** - displayed after the progress bar, if the progress bar\n  is displayed.\n* **progressBarTheme** - The subtheme passed through to the progress bar\n  renderer, it's an object with `complete` and `remaining` properties\n  that are the strings you want repeated for those sections of the progress\n  bar.\n* **activityIndicatorTheme** - The theme for the activity indicator (spinner),\n  this can either be a string, in which each character is a different step, or\n  an array of strings.\n* **preSubsection** - Displayed as a separator between the `section` and\n  `subsection` when the latter is printed.\n\nMore generally, themes can have any value that would be a valid value when rendering\ntemplates. The properties in the theme are used when their name matches a type in\nthe template. Their values can be:\n\n* **strings & numbers** - They'll be included as is\n* **function (values, theme, width)** - Should return what you want in your output.\n  *values* is an object with values provided via `gauge.show`,\n  *theme* is the theme specific to this item (see below) or this theme object,\n  and *width* is the number of characters wide your result should be.\n\nThere are a couple of special prefixes:\n\n* **pre** - Is shown prior to the property, if its displayed.\n* **post** - Is shown after the property, if its displayed.\n\nAnd one special suffix:\n\n* **Theme** - Its value is passed to a function-type item as the theme.\n\n#### themes.addToAllThemes(theme)\n\nThis *mixes-in* `theme` into all themes currently defined. It also adds it\nto the default parent theme for this themeset, so future themes added to\nthis themeset will get the values from `theme` by default.\n\n#### themes.newThemeSet()\n\nCopy the current themeset into a new one.  This allows you to easily inherit\none themeset from another.\n\n### TEMPLATES\n\nA template is an array of objects and strings that, after being evaluated,\nwill be turned into the gauge line.  The default template is:\n\n```javascript\n[\n    {type: 'progressbar', length: 20},\n    {type: 'activityIndicator', kerning: 1, length: 1},\n    {type: 'section', kerning: 1, default: ''},\n    {type: 'subsection', kerning: 1, default: ''}\n]\n```\n\nThe various template elements can either be **plain strings**, in which case they will\nbe be included verbatum in the output, or objects with the following properties:\n\n* *type* can be any of the following plus any keys you pass into `gauge.show` plus\n  any keys you have on a custom theme.\n  * `section` – What big thing you're working on now.\n  * `subsection` – What component of that thing is currently working.\n  * `activityIndicator` – Shows a spinner using the `activityIndicatorTheme`\n    from your active theme.\n  * `progressbar` – A progress bar representing your current `completed`\n    using the `progressbarTheme` from your active theme.\n* *kerning* – Number of spaces that must be between this item and other\n  items, if this item is displayed at all.\n* *maxLength* – The maximum length for this element. If its value is longer it\n  will be truncated.\n* *minLength* – The minimum length for this element. If its value is shorter it\n  will be padded according to the *align* value.\n* *align* – (Default: left) Possible values \"left\", \"right\" and \"center\". Works\n  as you'd expect from word processors.\n* *length* – Provides a single value for both *minLength* and *maxLength*. If both\n  *length* and *minLength or *maxLength* are specified then the latter take precedence.\n* *value* – A literal value to use for this template item.\n* *default* – A default value to use for this template item if a value\n  wasn't otherwise passed in.\n\n### PLUMBING\n\nThis is the super simple, assume nothing, do no magic internals used by gauge to\nimplement its ordinary interface.\n\n```\nvar Plumbing = require('gauge/plumbing')\nvar gauge = new Plumbing(theme, template, width)\n```\n\n* **theme**: The theme to use.\n* **template**: The template to use.\n* **width**: How wide your gauge should be\n\n#### `gauge.setTheme(theme)`\n\nChange the active theme.\n\n#### `gauge.setTemplate(template)`\n\nChange the active template.\n\n#### `gauge.setWidth(width)`\n\nChange the width to render at.\n\n#### `gauge.hide()`\n\nReturn the string necessary to hide the progress bar\n\n#### `gauge.hideCursor()`\n\nReturn a string to hide the cursor.\n\n#### `gauge.showCursor()`\n\nReturn a string to show the cursor.\n\n#### `gauge.show(status)`\n\nUsing `status` for values, render the provided template with the theme and return\na string that is suitable for printing to update the gauge.\n", "readmeFilename": "README.md", "users": {"jrop": true, "mrxf": true, "n1kk": true, "iarna": true, "buzuli": true, "janzal": true, "wenbing": true, "byoigres": true, "thechori": true, "boneskull": true, "monolithed": true, "django_wong": true, "scytalezero": true, "wangnan0610": true, "entropy-lion": true, "arcticicestudio": true}}