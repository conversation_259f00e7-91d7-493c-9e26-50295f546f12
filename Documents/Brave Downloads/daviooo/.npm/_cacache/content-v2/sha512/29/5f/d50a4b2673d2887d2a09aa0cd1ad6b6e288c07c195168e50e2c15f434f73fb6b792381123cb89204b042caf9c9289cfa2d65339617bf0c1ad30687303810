{"_id": "trim", "_rev": "20-4a830fefe8d530b5f16920317f413a17", "name": "trim", "description": "Trim string whitespace", "dist-tags": {"latest": "1.0.1"}, "versions": {"0.0.1": {"name": "trim", "version": "0.0.1", "description": "Trim string whitespace", "keywords": ["string", "trim"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}, "main": "index", "component": {"scripts": {"trim/index.js": "index.js"}}, "readme": "\n# trim\n\n  Trims string whitespace.\n\n## Installation\n\n```\n$ npm install trim\n$ component install component/trim\n```\n\n## API\n\n   - [trim(str)](#trimstr)\n   - [.left(str)](#leftstr)\n   - [.right(str)](#rightstr)\n<a name=\"\" />\n \n<a name=\"trimstr\" />\n### trim(str)\nshould trim leading / trailing whitespace.\n\n```js\ntrim('  foo bar  ').should.equal('foo bar');\ntrim('\\n\\n\\nfoo bar\\n\\r\\n\\n').should.equal('foo bar');\n```\n\n<a name=\"leftstr\" />\n### .left(str)\nshould trim leading whitespace.\n\n```js\ntrim.left('  foo bar  ').should.equal('foo bar  ');\n```\n\n<a name=\"rightstr\" />\n### .right(str)\nshould trim trailing whitespace.\n\n```js\ntrim.right('  foo bar  ').should.equal('  foo bar');\n```\n\n\n## License \n\n(The MIT License)\n\nCopyright (c) 2012 TJ Ho<PERSON>aychuk &lt;<EMAIL>&gt;\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.", "readmeFilename": "Readme.md", "_id": "trim@0.0.1", "dist": {"shasum": "5858547f6b290757ee95cccc666fb50084c460dd", "tarball": "https://registry.npmjs.org/trim/-/trim-0.0.1.tgz", "integrity": "sha512-YzQV+TZg4AxpKxaTHK3c3D+kRDCGVEE7LemdlQZoQXn0iennk10RsIoY6ikzAqJTc9Xjl9C1/waHom/J86ziAQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCb2D2z/tfxPlDiGHfHTwfFd5Jkuzb7fJcWy6MXccysfwIhAO5uHa0bPAw3p4BEbdO+pG5TjePkqWpLxYOjwHICYPLI"}]}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "deprecated": "Use String.prototype.trim() instead"}, "0.0.2": {"name": "trim", "version": "0.0.2", "description": "Trim string whitespace", "keywords": ["string", "trim"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}, "main": "index", "repository": {"type": "git", "url": "git+https://github.com/component/trim.git"}, "gitHead": "8bb80b47d8e05cfaa98b67be0fa8409666a893f7", "bugs": {"url": "https://github.com/component/trim/issues"}, "homepage": "https://github.com/component/trim#readme", "_id": "trim@0.0.2", "_nodeVersion": "15.0.1", "_npmVersion": "7.0.3", "dist": {"integrity": "sha512-kTIK/cS0xM3jxJ7toUHlFTxHgix/kmmBgOiqc0gUAoW+NjIRsMB3vkjgAth5XEghYFCQxOdF0p/PHrv1BqTHgA==", "shasum": "b41afc68d6b5fc1a1fceb47b2ac91da258a071d4", "tarball": "https://registry.npmjs.org/trim/-/trim-0.0.2.tgz", "fileCount": 6, "unpackedSize": 3091, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfo0F2CRA9TVsSAnZWagAAMGIP/1Qh/keb1a7jIj1U4uV2\nu73T25EQCJAO8/9ZnJntaV7AlHbMCvesAyIAqN6Vz+LgrEWj/0yILPcDrnQQ\nDOoNjZZoiUPRwnv0DGwwuMzw8CNNdBJoxhSCoW3KBvjEowxexWYmnXFBkAOW\nA5PeXazgJS5mbSlSkUavF/fBZBlgJje/R5dzC+jm7HCkS/7vufRlUM7rZFJ/\n8T089vEHqsUHSjCvy9PA1H/wHs2tmNp6hU7zRUCLCXtertQvVsNDoSOnU3y8\nsDZE1MwvoBi/cCqa07r/WSTE2KSDrElSgiM5aDOVo98S3e8g6VAYlvWJYfrf\nLJ5UoZujmaIqCkOJrA4Yf0e6ysMEzbhkqeMtMiLRbbaDzstR648xrPQ5PFqc\n4LqRY5Gnuwuosq0CyRI01APPxoKu3R6TNw9AjwaZ9zD7rKFibpsMXBsOEHAa\noVZ3SjgwA7YDpGA8iCsNmQoLKC3/f6L6TzzhgfnXuSOsKpB6gTnpjX4qckXb\nYiDTGEo2diHAFa9ecaGJJCnhKM13IkTT8F3zniEGCNRYG2y0ufPD2RKHsRNj\nS4AjC4YZMaAslULf2j1QfrqsxoiOw84kQKzFRgvi/RFNEUyQnUTHBNfrN3Ad\nESxmEcNZVdziMHCo6BlSVEirlDliIYEt6k4NXxsrF/Fdqfwoemv+JsUCv52K\npec3\r\n=2hGP\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD3TwJp0IGnlGiiaIprRJis6jzqn5SKWvwid8yiJXW77gIgJJFvvcRPTUGcvh2Wo/2SR+j9V81B7ORthyJnBj7K7+E="}]}, "_npmUser": {"name": "trott", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "trott", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/trim_0.0.2_1604534645187_0.00459234049118451"}, "_hasShrinkwrap": false, "deprecated": "Use String.prototype.trim() instead"}, "0.0.3": {"name": "trim", "version": "0.0.3", "description": "Trim string whitespace", "keywords": ["string", "trim"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}, "main": "index", "repository": {"type": "git", "url": "git+https://github.com/component/trim.git"}, "gitHead": "a0fe8266ef6ffa27f38fa9a0d01d63614f51e641", "bugs": {"url": "https://github.com/component/trim/issues"}, "homepage": "https://github.com/component/trim#readme", "_id": "trim@0.0.3", "_nodeVersion": "15.0.1", "_npmVersion": "7.0.3", "dist": {"integrity": "sha512-h82ywcYhHK7veeelXrCScdH7HkWfbIT1D/CgYO+nmDarz3SGNssVBMws6jU16Ga60AJCRAvPV6w6RLuNerQqjg==", "shasum": "05243a47a3a4113e6b49367880a9cca59697a20b", "tarball": "https://registry.npmjs.org/trim/-/trim-0.0.3.tgz", "fileCount": 6, "unpackedSize": 3160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfo0H4CRA9TVsSAnZWagAAxScP/1eU9zYiEaOJYqxNTTmU\nYshkiuzhLsarli1hf69PovKyPVomXpgikDhyLT88vqtwrhFPoxr+ZB0GEw5O\nzJMUwJBIW+v9Aqs3HHZTopLisD1X5bfKCmjEwFd3i0kfsZGPcoI39GXevZLX\nMFVuWPLTqYO++pjcKAloRjyGuzfowJBy/Xpt0pxdKEyrrQCN/0xX55HxOTDa\nXXmdpit4WoxEuaTaoBYorlTGLHTD8b1eAY7J0hiMhNeGs4bzUQrS364v3FAp\n8CilYzlgtX6p0jMXa4doKMorlNc/Q2rLJi2yNUl/NQTVT3+JOtsZ2Hy4n/Ix\ngUd3AOB0w2ydIJo8CUqlMZmrv2lR920LtkAR1GiOQjZJMcEQtMaGjb82GipS\nG3fW8h9Itd9qJm0dbuG+Azk2+tv71MmupxDQmZVMGWNESrdYyJLl2txZ3szu\nUuWxlrZwrAJyvXVaRsXG1E4aQ7YY5TnZ8M7AEAhal342k+M0inspwx2CKS/u\nOlvSZ5kVtJe/HS14AGJHs2ezaNXlHmHoSVz22/bTcFRWsZLOkS0jw15Ns9Pe\nHLI/v1d8JHCYJcxjHtIwnnE5LAlg+SLhSYQlBNqeUJb0CWsp3gCf6QRmsL5t\nyFfNQQCDWKpcVm+17ky0eBScboKYMEOA5FGc4kilNRkupUXhOH6kEJIPwRLG\nXvQo\r\n=IVE7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFc+F8L+MeIfOfLZkBCaJlhHY8tmpD0Lbyck7UUVuolZAiEA08AqhjPC7fr+1M8RfPokrj9v6RMCHZ7S4IZdOTK5tqs="}]}, "_npmUser": {"name": "trott", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "trott", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/trim_0.0.3_1604534776321_0.727485674694546"}, "_hasShrinkwrap": false, "deprecated": "Use String.prototype.trim() instead"}, "1.0.0": {"name": "trim", "version": "1.0.0", "description": "Trim string whitespace", "license": "MIT", "keywords": ["string", "trim"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}, "main": "index", "repository": {"type": "git", "url": "git+https://github.com/component/trim.git"}, "gitHead": "b0e7e19075f96b74b2e35a3e5e74bd68c4e4942a", "bugs": {"url": "https://github.com/component/trim/issues"}, "homepage": "https://github.com/component/trim#readme", "_id": "trim@1.0.0", "_nodeVersion": "15.2.0", "_npmVersion": "7.0.8", "dist": {"integrity": "sha512-UgtES1lYpE+f4WiGY5lyJlHchuGhTa/xMPH96g/B7gc+pEQPiL41s6ECm7Ky3hkhARG/u1SHGFcleJodAvQOKQ==", "shasum": "fd1f30b878bdd2d8435fa0f2cc9cbb55f518be7d", "tarball": "https://registry.npmjs.org/trim/-/trim-1.0.0.tgz", "fileCount": 6, "unpackedSize": 3180, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfrc43CRA9TVsSAnZWagAAwmwP/iddG683y6e+/lBjlnTs\nuJVR9op5T72San5M7XLKrA/2GU1btlC1DDa/1b95W1MyY+vHvYvUqdAPDToB\nFrhZUOFYphY8+eH6UoGaIIIFVIHW/GTHvjACelGXfEy0vioFoV6i/5xzRWry\nStrRTPKM/47pGIXgs3Lu8x38qaFEUvSRzQmgmFZGf9wXnZGQ6Kw5XAqf9P+z\ncht6Gvg72eX9y1yEM7hNjrZ3BEiqvIxO6StHRGe/IuC9WiUN3uk4IaJRJpdK\nCXCO01VVndMVEn45YEIfoLoh3KZRikvxGCoHSsVsKlOYn9TGhUU7dphB5x3H\n0bBxI+QyhVWaxFrcnbdGj1kcrglkcKchpH484vf4Td4SNBXrDVmKVW/57CRF\nv5yg1w3xeF9p6B24h3f1SDdx5YlyvLqoTemhrWts2t56qacE5tCLqJyuNrDb\ni7x7KExTnC3RQfmH0LHM6J48/DzRcuN1mqt+T4Lky7Fwju7KY1YOiJb8/UdY\nOcEqETSgubuMVsWeO/LNErpxoO0hmt2C91EOJ4J+e/9xcv7S07stTy1UDhf0\n5bESHmSYDwlTVscVi2OzKCFFGjXQ9WWEPLgfBLO3vY2LGQw94qahXOjCyzkv\n0H9UmTrs844PDhW79olz+uMND1XGvySIp86q3ZpIhStDUu4wH/SpeC/4wkgR\n46+J\r\n=wu7M\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICzq39ykI7jPY6SgDcIsA2g5HBaemC49nlB7XNAPL+uYAiBDAMWbM8Rd1/BXQFauf73DqcJ04sQsQUhTv2NQ/bcISg=="}]}, "_npmUser": {"name": "trott", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "trott", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/trim_1.0.0_1605226039124_0.7738112171566207"}, "_hasShrinkwrap": false, "deprecated": "Use String.prototype.trim() instead"}, "1.0.1": {"name": "trim", "version": "1.0.1", "description": "Trim string whitespace", "license": "MIT", "keywords": ["string", "trim"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}, "main": "index", "repository": {"type": "git", "url": "git+https://github.com/Trott/trim.git"}, "gitHead": "727a93b0c69980283e871769b0f01052e3d22af0", "bugs": {"url": "https://github.com/Trott/trim/issues"}, "homepage": "https://github.com/Trott/trim#readme", "_id": "trim@1.0.1", "_nodeVersion": "15.13.0", "_npmVersion": "7.7.6", "dist": {"integrity": "sha512-3JVP2YVqITUisXblCDq/Bi4P9457G/sdEamInkyvCsjbTcXLXIiG7XCb4kGMFWh6JGXesS3TKxOPtrncN/xe8w==", "shasum": "68e78f6178ccab9687a610752f4f5e5a7022ee8c", "tarball": "https://registry.npmjs.org/trim/-/trim-1.0.1.tgz", "fileCount": 6, "unpackedSize": 3346, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZjGkCRA9TVsSAnZWagAAmQcP/0EujKehHOhkDTDgqiBy\nLvoBH8XrGdtaXTvGd0+Wwbgh2+**********************************\n+Z6GuoqrhGP+q/z7Uct23A1YO3Mlg+yAErE3hQAa66Y3YfWjd8Q3qIf4sxyG\nzb3ZruZTAHhKNHSSbpSaQGWdLiL2irzAlWp+bE5+2Yx6J7hq3LdHqCbeV3bx\nkDetuB3V07LpeBCGlGcfgMND4n2SdA9zxmFFUNaHkhWJzE2L176j/zwzU0Ru\nBnKiy/YAlZPftcR5WvMIbxZ2pxkYGG6yxPwOs5ESGlBtG0Q15+6LX7sJ9QDu\nT59zb743ovT0tnymUkGJsMwnKWxlJwNTu6M3Dehexs3uzYPZxwFE5sxOM3JW\nTbz9KnBCT2X60XezElKD6tENccLmciTcUbQ3cQsitVXZGhcWBj5Lb9EDI27Q\nZPcj2emd3jJWaEHADhWlAL0+aFXRwAW/x09Q7Wnu2IukNYZJBhCJxSZYvfon\nZNoSOBFCqW6AIljf1B/9iRPFrPXTtkwzDe2AlKGOmSdJP6noDSk+FCbpbIf0\norjW0yIwekVxHHIMxDw8NVpqZJGVDu+UBSL+Csh0YtOjNo4l8taq4DZIcPwe\nu9CD0+emv59PKaQtQF++5j2wtbo9eKme+bsveAvhIHp5TgHvh9xzqMu/tU4c\nRku6\r\n=hSgw\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDjXJN6C3gtAFH1Ye8hE+nd97g/oB3u9KB4lDxFJDBA3AIhANbvuakU2tRT2bSdOMYNzys0/rK4yZM6or9xCOzr666c"}]}, "_npmUser": {"name": "trott", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "trott", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/trim_1.0.1_1617310115850_0.048714381881778346"}, "_hasShrinkwrap": false, "deprecated": "Use String.prototype.trim() instead"}}, "readme": "\n# trim\n\n  Trims string whitespace.\n\n## Installation\n\n```\n$ npm install trim\n$ component install component/trim\n```\n\n## API\n\n   - [trim(str)](#trimstr)\n   - [.left(str)](#leftstr)\n   - [.right(str)](#rightstr)\n<a name=\"\" />\n \n<a name=\"trimstr\" />\n### trim(str)\nshould trim leading / trailing whitespace.\n\n```js\ntrim('  foo bar  ').should.equal('foo bar');\ntrim('\\n\\n\\nfoo bar\\n\\r\\n\\n').should.equal('foo bar');\n```\n\n<a name=\"leftstr\" />\n### .left(str)\nshould trim leading whitespace.\n\n```js\ntrim.left('  foo bar  ').should.equal('foo bar  ');\n```\n\n<a name=\"rightstr\" />\n### .right(str)\nshould trim trailing whitespace.\n\n```js\ntrim.right('  foo bar  ').should.equal('  foo bar');\n```\n\n## Development\n\nRun tests with\n\n```js\nnpx mocha\n```\n\n\n## License \n\n(The MIT License)\n\nCopyright (c) 2012 TJ <PERSON> &lt;<EMAIL>&gt;\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "trott", "email": "<EMAIL>"}], "time": {"modified": "2023-02-25T18:02:14.784Z", "created": "2013-03-12T17:59:21.016Z", "0.0.1": "2013-03-12T17:59:22.296Z", "0.0.2": "2020-11-05T00:04:05.399Z", "0.0.3": "2020-11-05T00:06:16.543Z", "1.0.0": "2020-11-13T00:07:19.249Z", "1.0.1": "2021-04-01T20:48:35.970Z"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "users": {"nickl": true, "koulmomo": true, "acollins-ts": true, "werdyin": true, "mojaray2k": true, "shangsinian": true}, "homepage": "https://github.com/Trott/trim#readme", "keywords": ["string", "trim"], "repository": {"type": "git", "url": "git+https://github.com/Trott/trim.git"}, "bugs": {"url": "https://github.com/Trott/trim/issues"}, "readmeFilename": "Readme.md", "license": "MIT"}