{"_id": "npmlog", "_rev": "125-e66e2b31799f08a29e92023e423a53f2", "name": "npmlog", "dist-tags": {"latest": "7.0.1"}, "versions": {"0.0.1": {"name": "npmlog", "version": "0.0.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "npmlog@0.0.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "121bf400effe0f20c05521593f1344b4befd5e08", "tarball": "https://registry.npmjs.org/npmlog/-/npmlog-0.0.1.tgz", "integrity": "sha512-5121eAdx5zS7WmGHecb7qIo5Bj26HFXKRb7j/Nq7Ag+6osFCvH4mH1MnJMFGvao7K0mWb6vykFY4USHVpk/fYA==", "signatures": [{"sig": "MEQCIQD4tq6tHe91pYEwZBqpGnIcWNHZM+/EFryueUfr0JXItAIfMbcu67PPSjt1hGjeUIs645wcABASkMC368Fxt/8nCg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "log.js", "engines": {"node": "*"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git://github.com/isaacs/npmlog.git", "type": "git"}, "_npmVersion": "1.1.24", "description": "logger for npm", "directories": {}, "_nodeVersion": "v0.7.10-pre", "dependencies": {"ansi": "~0.1.2"}, "_defaultsLoaded": true, "devDependencies": {"tap": ""}, "_engineSupported": true, "optionalDependencies": {}}, "0.0.2": {"name": "npmlog", "version": "0.0.2", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "npmlog@0.0.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "f0cf4b2c519950c00e91ba8e2868b62bf86254f6", "tarball": "https://registry.npmjs.org/npmlog/-/npmlog-0.0.2.tgz", "integrity": "sha512-qkrCR75q6/HmU3SWLHb4cr5oAQa+xUp6Vh6/sOkO7MGbSaNoCS7iubg8XTwJe48PqzlCHslzyux+Vmq1hnfLAA==", "signatures": [{"sig": "MEYCIQCvLvkB6pFYF8R3PFutzYiWa8h9Oi/fb9K+jOYO6gHgPgIhAKiAiYS3MCXZnVaV04Vz83vR+XDc9WQzHNJu1CIVtyF7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "log.js", "engines": {"node": "*"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git://github.com/isaacs/npmlog.git", "type": "git"}, "_npmVersion": "1.1.24", "description": "logger for npm", "directories": {}, "_nodeVersion": "v0.7.10-pre", "dependencies": {"ansi": "~0.1.2"}, "_defaultsLoaded": true, "devDependencies": {"tap": ""}, "_engineSupported": true, "optionalDependencies": {}}, "0.0.3": {"name": "npmlog", "version": "0.0.3", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "npmlog@0.0.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/isaacs/npmlog/issues"}, "dist": {"shasum": "c424ad1531af402eef8da201fc3d63bdbd37dacb", "tarball": "https://registry.npmjs.org/npmlog/-/npmlog-0.0.3.tgz", "integrity": "sha512-knXDtgdfuorLFlyY1pUzqHdHZsH1qbNYxQm8jM+rvQUPRmPuW23BbX9OLQYY7FGbdc48z9LkDEgZDMOtNaH63A==", "signatures": [{"sig": "MEUCIHyUeY6p21bSHPobKS10GcIaZFBi6c3eep8/ax5pd2XaAiEAyQc8frNGrKVm0l5SuqYIMHbrE0xChrxNZ7bVm2WdQpk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "log.js", "_from": ".", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git://github.com/isaacs/npmlog.git", "type": "git"}, "_npmVersion": "1.2.32", "description": "logger for npm", "directories": {}, "dependencies": {"ansi": "~0.1.2"}, "devDependencies": {"tap": ""}}, "0.0.4": {"name": "npmlog", "version": "0.0.4", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "npmlog@0.0.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/isaacs/npmlog/issues"}, "dist": {"shasum": "a12a7418606b7e0183a2851d97a8729b9a0f3837", "tarball": "https://registry.npmjs.org/npmlog/-/npmlog-0.0.4.tgz", "integrity": "sha512-pG1bYtYXWshDw0XL/xZJw6SQlbQWVgREkq1K4cow9UXglL4byxrjcDsU63JtGGoWDIFQHlgSeP29VH0ts+c5cA==", "signatures": [{"sig": "MEQCIFZ0tsHWrdAWmI05p8jJBnQ4UYllezBr3lLJSbaq/7/YAiASJqx3wPlpXMDAmqxbuYzVGOXx7gnD7duNuf9Gtc0PLg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "log.js", "_from": ".", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git://github.com/isaacs/npmlog.git", "type": "git"}, "_npmVersion": "1.3.2", "description": "logger for npm", "directories": {}, "dependencies": {"ansi": "~0.1.2"}, "devDependencies": {"tap": ""}}, "0.0.5": {"name": "npmlog", "version": "0.0.5", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "npmlog@0.0.5", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/isaacs/npmlog/issues"}, "dist": {"shasum": "73c25116265cc9ed252bef4bb65e423a24836308", "tarball": "https://registry.npmjs.org/npmlog/-/npmlog-0.0.5.tgz", "integrity": "sha512-WueimYqEm2uVm1yZ5LQu0ig/etEKSJ95sjvI3+T6WKC/rCJVaU3OGGhczk4l/b8o5CegKMHvS3ICIMdoWb9LZw==", "signatures": [{"sig": "MEQCIE83WNfLLLNzS2EuD9p3Se6peph8GkQJQ62/jL5lZM/2AiAvECVGJD6HEmAyrSY5ZkOEYmNTZACSxe/tforcx9nyyQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "log.js", "_from": ".", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git://github.com/isaacs/npmlog.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "logger for npm", "directories": {}, "dependencies": {"ansi": "~0.2.1"}, "devDependencies": {"tap": ""}}, "0.0.6": {"name": "npmlog", "version": "0.0.6", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "npmlog@0.0.6", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/isaacs/npmlog/issues"}, "dist": {"shasum": "685043fe71aa1665d6e3b2acef180640caf40873", "tarball": "https://registry.npmjs.org/npmlog/-/npmlog-0.0.6.tgz", "integrity": "sha512-8omfjyaSWsT0lOJq08ccWEUipV49JbY0joZ3fUA9OvC4nHOmpBVSEdnzN7uKvZ1eBO9bYR9QVvqiBWOc4KbfFw==", "signatures": [{"sig": "MEUCIE1/nD1iMJcN2QGzaO6KHZlY3wUiVKovYNbwVgvvuc1EAiEAunsoB5+AYacnrIVI7Suic7VRFJLlh0iz9FmKQkzT4lc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "log.js", "_from": ".", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git://github.com/isaacs/npmlog.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "logger for npm", "directories": {}, "dependencies": {"ansi": "~0.2.1"}, "devDependencies": {"tap": ""}}, "0.1.0": {"name": "npmlog", "version": "0.1.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "npmlog@0.1.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/npmlog", "bugs": {"url": "https://github.com/isaacs/npmlog/issues"}, "dist": {"shasum": "c88336df9426979b86d848d35357f4e6a558bd90", "tarball": "https://registry.npmjs.org/npmlog/-/npmlog-0.1.0.tgz", "integrity": "sha512-HYCrcMjEZgmMhfzdC3+Xi3of8uzTTLzRSmgHW8pTvQ/47r362eSSUTwDNWmQRVQ4Y83auUo58ILccqSLeY/OBw==", "signatures": [{"sig": "MEYCIQDbWMTTkjmTNBRt6ZVWpjUXsMpRgleH6Hj4JN8JCUjsBQIhAKiM+Sq0sgG+PtnDDO80c7k2W4BY3wOr3XcJlUzFvNaU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "log.js", "_from": ".", "_shasum": "c88336df9426979b86d848d35357f4e6a558bd90", "gitHead": "e1bc90b9ce78900fa0b1c30bcaaa630de99763c8", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git://github.com/isaacs/npmlog.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "logger for npm", "directories": {}, "dependencies": {"ansi": "^0.3.0"}, "devDependencies": {"tap": ""}}, "0.1.1": {"name": "npmlog", "version": "0.1.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "npmlog@0.1.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/npmlog", "bugs": {"url": "https://github.com/isaacs/npmlog/issues"}, "dist": {"shasum": "8b9b9e4405d7ec48c31c2346965aadc7abaecaa5", "tarball": "https://registry.npmjs.org/npmlog/-/npmlog-0.1.1.tgz", "integrity": "sha512-gEpnmIBogjDV2xuCNl7ooAkDYKnjiLyRcKTXbkg4sO2JZ8MDzo1VTKioUk7In4eedJ0fGzyasJ/f6P0ZKG4Wyg==", "signatures": [{"sig": "MEUCICuuHj8TanZVTnm9jN/FFgZ8CxByjySolgp2uo5HKYLDAiEAhUO3O+gfiCVhClQcu9gdO4Fnv83o4XwF6xXA2iTPKsI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "log.js", "_from": ".", "_shasum": "8b9b9e4405d7ec48c31c2346965aadc7abaecaa5", "gitHead": "b58e360cd99db707d1191ce6125ae53d79f075a1", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git://github.com/isaacs/npmlog.git", "type": "git"}, "_npmVersion": "1.4.15", "description": "logger for npm", "directories": {}, "dependencies": {"ansi": "~0.3.0"}, "devDependencies": {"tap": ""}}, "1.0.0": {"name": "npmlog", "version": "1.0.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "npmlog@1.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/npmlog", "bugs": {"url": "https://github.com/isaacs/npmlog/issues"}, "dist": {"shasum": "ed2f290b60316887c39e0da9f09f8d13847cef0f", "tarball": "https://registry.npmjs.org/npmlog/-/npmlog-1.0.0.tgz", "integrity": "sha512-tLHonOJv0KRb/h2DSYCSwBpd4jJtf++R8moSi+WXNf4tEPc9CuTONGrWoCjMbjaDO+JMTeEMTkhjQgMkcGIPXA==", "signatures": [{"sig": "MEUCIQC+WlMhGLMYkeE4++CNif1FDE9v39tkVSHCEqWan9allgIgCsNtU5MrNNHYfj3kKzLWo7bhqcEyLm8evXORrRHZy1Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "log.js", "_from": ".", "_shasum": "ed2f290b60316887c39e0da9f09f8d13847cef0f", "gitHead": "09b2976531b85f39d2c509ebb46c04f6bec8f3a8", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git://github.com/isaacs/npmlog.git", "type": "git"}, "_npmVersion": "2.1.11", "description": "logger for npm", "directories": {}, "_nodeVersion": "0.10.33", "dependencies": {"ansi": "~0.3.0", "gauge": "~1.0.2", "are-we-there-yet": "~1.0.0"}, "devDependencies": {"tap": ""}}, "1.1.0": {"name": "npmlog", "version": "1.1.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "npmlog@1.1.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/npmlog", "bugs": {"url": "https://github.com/isaacs/npmlog/issues"}, "dist": {"shasum": "8744168148df1ce3f3387c0bc38154883b4af5f4", "tarball": "https://registry.npmjs.org/npmlog/-/npmlog-1.1.0.tgz", "integrity": "sha512-XtoxhFb6c4dwClIdQRxs9UPq5qtAaNJGX/uF9lITsO0TpZc74OrdMQgFKSS4TMRHH5DoAnI2vZQx2XW/kpsnjg==", "signatures": [{"sig": "MEUCIDrWSdvNfzI43eJEym0YlxEnUPy1vO6IR1AWxkRF2PgFAiEAlsoIszo+H/ch8JSqcpl9YcMgalx9G0u++KxvMXw/1Po=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "log.js", "_from": ".", "_shasum": "8744168148df1ce3f3387c0bc38154883b4af5f4", "gitHead": "d8e2bd3976cc052816ea3eaea2db45e257763d74", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git://github.com/isaacs/npmlog.git", "type": "git"}, "_npmVersion": "2.4.0", "description": "logger for npm", "directories": {}, "_nodeVersion": "0.10.33", "dependencies": {"ansi": "~0.3.0", "gauge": "~1.1.0", "are-we-there-yet": "~1.0.0"}, "devDependencies": {"tap": ""}}, "1.2.0": {"name": "npmlog", "version": "1.2.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "npmlog@1.2.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/npmlog", "bugs": {"url": "https://github.com/isaacs/npmlog/issues"}, "dist": {"shasum": "b512f18ae8696a0192ada78ba00c06dbbd91bafb", "tarball": "https://registry.npmjs.org/npmlog/-/npmlog-1.2.0.tgz", "integrity": "sha512-e7yPyzsH3InhdHei2+flGMm1hLsWh5N4c7oO2nD4DxegLE41yUGTdriewBEdiBlB1oGvlZ5Ol5j0kLz4jF9qig==", "signatures": [{"sig": "MEQCID8Ra93XpVeDNZBaZCw8hjfInWhx0fY/BSn8hVQmsGQCAiBACvkb6sySe7pf81PCj6/6s+ydiJYu+nsux0mx3NO2Uw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "log.js", "_from": ".", "_shasum": "b512f18ae8696a0192ada78ba00c06dbbd91bafb", "gitHead": "1fe2892a8b9dacb775d4fb365315865f421f4ca9", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git://github.com/isaacs/npmlog.git", "type": "git"}, "_npmVersion": "2.6.0", "description": "logger for npm", "directories": {}, "_nodeVersion": "1.1.0", "dependencies": {"ansi": "~0.3.0", "gauge": "~1.2.0", "are-we-there-yet": "~1.0.0"}, "devDependencies": {"tap": ""}}, "1.2.1": {"name": "npmlog", "version": "1.2.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "npmlog@1.2.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/npmlog#readme", "bugs": {"url": "https://github.com/isaacs/npmlog/issues"}, "dist": {"shasum": "28e7be619609b53f7ad1dd300a10d64d716268b6", "tarball": "https://registry.npmjs.org/npmlog/-/npmlog-1.2.1.tgz", "integrity": "sha512-1J5KqSRvESP6XbjPaXt2H6qDzgizLTM7x0y1cXIjP2PpvdCqyNC7TO3cPRKsuYlElbi/DwkzRRdG2zpmE0IktQ==", "signatures": [{"sig": "MEYCIQCujIaBAvDd/v6LpLiLBwqjBGBAdUu/ahyAGK0mz6kTqQIhAO+KFO7TTvKvAvmAYEOuZRUTd48/92oZJLQbaNycka3x", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "log.js", "_from": ".", "_shasum": "28e7be619609b53f7ad1dd300a10d64d716268b6", "gitHead": "4e1a73a567036064ded425a7d48c863d53550b4f", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git://github.com/isaacs/npmlog.git", "type": "git"}, "_npmVersion": "2.10.0", "description": "logger for npm", "directories": {}, "_nodeVersion": "2.0.1", "dependencies": {"ansi": "~0.3.0", "gauge": "~1.2.0", "are-we-there-yet": "~1.0.0"}, "devDependencies": {"tap": ""}}, "2.0.0": {"name": "npmlog", "version": "2.0.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "npmlog@2.0.0", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/npmlog#readme", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "dist": {"shasum": "4076c200a3dda51133e6f3cf052130105f78bbdf", "tarball": "https://registry.npmjs.org/npmlog/-/npmlog-2.0.0.tgz", "integrity": "sha512-dK+uobtc6MZiF2OegYLq5wavFTRLa1d/IVpJmDQxWcYoD51A9Y8STJ8sWIoCl0iOFtgy55ub6qQhOUFoIzGq7Q==", "signatures": [{"sig": "MEUCIQC9+d9I/OlsxJrBoBC9h2dllHs4mur0OMPVP5KwmkS94wIgSYhcA0hWmlyEC/ABnmUzEmnWZQ8V4+ZEIhXVv4dMCNA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "log.js", "_from": ".", "_shasum": "4076c200a3dda51133e6f3cf052130105f78bbdf", "gitHead": "6eaa3f8eec672bb7b56a4df9b55dbfff3b9c6a71", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "othiym23", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/npm/npmlog.git", "type": "git"}, "_npmVersion": "2.14.9", "description": "logger for npm", "directories": {}, "_nodeVersion": "4.2.1", "dependencies": {"ansi": "~0.3.0", "gauge": "~1.2.0", "are-we-there-yet": "~1.0.0"}, "devDependencies": {"tap": "~2.2.0"}}, "2.0.1": {"name": "npmlog", "version": "2.0.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "npmlog@2.0.1", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/npmlog#readme", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "dist": {"shasum": "c2e704d3eb50de738c903f7172001d60bf673fa0", "tarball": "https://registry.npmjs.org/npmlog/-/npmlog-2.0.1.tgz", "integrity": "sha512-xkPfzGFtodm2Zav19QQs34mTbtZqLTWQoe5+RgbHCkgU5UJUl+HXyuADj6I8KhsMoC+42iNhXjyq+GzZIPEsmg==", "signatures": [{"sig": "MEQCIBcYLu2kmczgqb2sr6HbmTyle31YERl3IiAtByaxAuv+AiA82PPbgn8WdMlv45thS5yngs8Q9fbIh3mVTwJxgumh9w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "log.js", "_from": ".", "_shasum": "c2e704d3eb50de738c903f7172001d60bf673fa0", "gitHead": "c4a1c3e1acd5a324398c3b2d3f9daffd3175b6ed", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/npm/npmlog.git", "type": "git"}, "_npmVersion": "3.5.4", "description": "logger for npm", "directories": {}, "_nodeVersion": "5.4.0", "dependencies": {"ansi": "~0.3.1", "gauge": "~1.2.4", "are-we-there-yet": "~1.0.5"}, "devDependencies": {"tap": "~5.1.1"}}, "2.0.2": {"name": "npmlog", "version": "2.0.2", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "npmlog@2.0.2", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/npmlog#readme", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "dist": {"shasum": "d0470238b9697b7c3c4d16bdea65a00b12a464ab", "tarball": "https://registry.npmjs.org/npmlog/-/npmlog-2.0.2.tgz", "integrity": "sha512-16UagPcQMyS4ZVwKCKM+mxdHMtrDcGFj9D8UyWzd8p0iKv4qZG3ZkXImTlv9+HYY/TvPhSWDlKS+cKGqEop1og==", "signatures": [{"sig": "MEUCIQDBZGVecFeZ82YU5LiAF/wh8UJKaH8iYEykX8xFUSKmmQIgZ8Z7wtbSgCo+ICbCg/6CWq01i7Gsj0p1CNXS7j2eduY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "log.js", "_from": ".", "_shasum": "d0470238b9697b7c3c4d16bdea65a00b12a464ab", "gitHead": "79dc582bf1ce4d2010454d89738a0a4dbd113be9", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/npm/npmlog.git", "type": "git"}, "_npmVersion": "3.6.0", "description": "logger for npm", "directories": {}, "_nodeVersion": "5.4.0", "dependencies": {"ansi": "~0.3.1", "gauge": "~1.2.5", "are-we-there-yet": "~1.0.6"}, "devDependencies": {"tap": "~5.1.2"}}, "2.0.3": {"name": "npmlog", "version": "2.0.3", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "npmlog@2.0.3", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/npmlog#readme", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "dist": {"shasum": "020f99351f0c02e399c674ba256e7c4d3b3dd298", "tarball": "https://registry.npmjs.org/npmlog/-/npmlog-2.0.3.tgz", "integrity": "sha512-fi2RmbJV0702nTZJQsGg8lVOU19VeIUZp+LPd7ce2XaZHe77TEcsKUlpBp6jGdARYVCMJPcHTaIYCU5LY7/tsQ==", "signatures": [{"sig": "MEQCIDmh/N5oAs4pMNLiQrCq1HuzDXgupY2CY7lUEkingXsbAiAjuScAGVFIYAH0LDfHrL6RtVWp+VSttiV2Pzc7kwG3hA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "log.js", "_from": ".", "_shasum": "020f99351f0c02e399c674ba256e7c4d3b3dd298", "gitHead": "9dfe26296118ceb5443e76f347f256c35e7ca999", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/npm/npmlog.git", "type": "git"}, "_npmVersion": "3.8.1", "description": "logger for npm", "directories": {}, "_nodeVersion": "4.2.2", "dependencies": {"ansi": "~0.3.1", "gauge": "~1.2.5", "are-we-there-yet": "~1.1.2"}, "devDependencies": {"tap": "~5.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/npmlog-2.0.3.tgz_1458089035965_0.5096880353521556", "host": "packages-13-west.internal.npmjs.com"}}, "2.0.4": {"name": "npmlog", "version": "2.0.4", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "npmlog@2.0.4", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/npmlog#readme", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "dist": {"shasum": "98b52530f2514ca90d09ec5b22c8846722375692", "tarball": "https://registry.npmjs.org/npmlog/-/npmlog-2.0.4.tgz", "integrity": "sha512-DaL6RTb8Qh4tMe2ttPT1qWccETy2Vi5/8p+htMpLBeXJTr2CAqnF5WQtSP2eFpvaNbhLZ5uilDb98mRm4Q+lZQ==", "signatures": [{"sig": "MEYCIQCwocWKMHrUMVzK8NJDzjZJnBPU8kMXBHuS2ey31StsKwIhAKbdGOu4ggTKLXHCZl7t2mOxpYVaCq6towqVP0xMoZC7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "log.js", "_from": ".", "files": ["log.js"], "_shasum": "98b52530f2514ca90d09ec5b22c8846722375692", "gitHead": "3732fd4ba1ca2d47c6102343e6c3fb7e66df7fe5", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "zkat", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/npm/npmlog.git", "type": "git"}, "_npmVersion": "3.9.1", "description": "logger for npm", "directories": {}, "_nodeVersion": "5.10.1", "dependencies": {"ansi": "~0.3.1", "gauge": "~1.2.5", "are-we-there-yet": "~1.1.2"}, "devDependencies": {"tap": "~5.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/npmlog-2.0.4.tgz_1463616637725_0.461703865788877", "host": "packages-12-west.internal.npmjs.com"}}, "3.0.0": {"name": "npmlog", "version": "3.0.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "npmlog@3.0.0", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/npmlog#readme", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "dist": {"shasum": "5b75a32bffcfd6ebf046487a302182224cd17c9b", "tarball": "https://registry.npmjs.org/npmlog/-/npmlog-3.0.0.tgz", "integrity": "sha512-dJfGNRmZvXgCOQKvQoQbIWEcDGJ+17fdpmqHZk6bvN68C5wKcreY+kAfsDJnNSdoen6X4vHWrHPtOA4wCVVMFQ==", "signatures": [{"sig": "MEUCIG8WlTAixO0yyRGL5UtNA2vTwW6TgObr0Ro+nJzPyZlRAiEA39o/DkvEriROORJ4QAQWsdPywJOBYAkKTIkxY6pCSC4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "log.js", "_from": ".", "files": ["log.js"], "_shasum": "5b75a32bffcfd6ebf046487a302182224cd17c9b", "gitHead": "87f698677996db477309c490c8a5b8371f65cc2b", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/npm/npmlog.git", "type": "git"}, "_npmVersion": "3.9.2", "description": "logger for npm", "directories": {}, "_nodeVersion": "4.4.0", "dependencies": {"ansi": "~0.3.1", "gauge": "~2.5.0", "set-blocking": "~2.0.0", "are-we-there-yet": "~1.1.2"}, "devDependencies": {"tap": "~5.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/npmlog-3.0.0.tgz_1465263036089_0.9853639076463878", "host": "packages-12-west.internal.npmjs.com"}}, "3.1.0": {"name": "npmlog", "version": "3.1.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "npmlog@3.1.0", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/npmlog#readme", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "dist": {"shasum": "52fdba095f72aa7c6fc1106b570ce5932d039bb7", "tarball": "https://registry.npmjs.org/npmlog/-/npmlog-3.1.0.tgz", "integrity": "sha512-gkDlSwxxuGXdlXFaMOe1+SLQJZ1Fm7L+nKjeUwsqEPFnVRvVDJTdIa8SOmTpPD62sZlbsfSqSDLdYnsw5+rpOQ==", "signatures": [{"sig": "MEUCIQCEM3licgoX8XpHwBbLhLVJ/SEGwNiJqH5qXJhrQCcJdgIgRJJT5ataLH+/sa8Th7iR2v/2lZujGEdOJLad9d/8/QA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "log.js", "_from": ".", "files": ["log.js"], "_shasum": "52fdba095f72aa7c6fc1106b570ce5932d039bb7", "gitHead": "accfd23d05a6bcd0b8ff05e7da840964482b7a3c", "scripts": {"test": "standard && tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/npm/npmlog.git", "type": "git"}, "_npmVersion": "3.9.2", "description": "logger for npm", "directories": {}, "_nodeVersion": "4.4.0", "dependencies": {"gauge": "~2.5.2", "set-blocking": "~2.0.0", "are-we-there-yet": "~1.1.2", "console-control-strings": "~1.1.0"}, "devDependencies": {"tap": "~5.7.0", "standard": "~7.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/npmlog-3.1.0.tgz_1466040332571_0.0009939647279679775", "host": "packages-16-east.internal.npmjs.com"}}, "3.1.1": {"name": "npmlog", "version": "3.1.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "npmlog@3.1.1", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/npmlog#readme", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "dist": {"shasum": "29477e7644cf46532782a7f2abe0405016ade755", "tarball": "https://registry.npmjs.org/npmlog/-/npmlog-3.1.1.tgz", "integrity": "sha512-891eC+Nx8LjOZXEBOQLJiudltKEtAWZ1jGWWmtAZb9/VZ07JYamKSNpMGV4Qz9F3YrqQTDVLjMoIlSTYHfuoQQ==", "signatures": [{"sig": "MEQCIHffz/hnlqO6AiFLDDyVrNfILZB1vKrz23S9ZuZXgQj1AiBysnA16kKGkIBjngHkQlh8VaZ8MIdqrBAaDUc2AiJUBQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "log.js", "_from": ".", "files": ["log.js"], "_shasum": "29477e7644cf46532782a7f2abe0405016ade755", "gitHead": "f66e503c982d9b5c6b6646e984108356dc8fd4a6", "scripts": {"test": "standard && tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/npm/npmlog.git", "type": "git"}, "_npmVersion": "3.9.2", "description": "logger for npm", "directories": {}, "_nodeVersion": "4.4.0", "dependencies": {"gauge": "~2.5.3", "set-blocking": "~2.0.0", "are-we-there-yet": "~1.1.2", "console-control-strings": "~1.1.0"}, "devDependencies": {"tap": "~5.7.0", "standard": "~7.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/npmlog-3.1.1.tgz_1466059950921_0.06024856795556843", "host": "packages-12-west.internal.npmjs.com"}}, "3.1.2": {"name": "npmlog", "version": "3.1.2", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "npmlog@3.1.2", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/npmlog#readme", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "dist": {"shasum": "2d46fa874337af9498a2f12bb43d8d0be4a36873", "tarball": "https://registry.npmjs.org/npmlog/-/npmlog-3.1.2.tgz", "integrity": "sha512-M9EjtMS6UQU5jVhfdCBmvPup0/i7QIoDy38ERfX/sLpzr5gida2uCtvf0pE586OB7+/NQx7KZvwwomGRyHQXwA==", "signatures": [{"sig": "MEUCIBEkkikHYtos761OmdiSRWZ1auCcYidsWdA/KrBym/Y1AiEA41NPwc+1EF16aYmK5Fm0Var5/lkeQWn4qzCtleHlZPM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "log.js", "_from": ".", "files": ["log.js"], "_shasum": "2d46fa874337af9498a2f12bb43d8d0be4a36873", "gitHead": "444e237743fa1339cb91bea2d3f16f710be56984", "scripts": {"test": "standard && tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/npm/npmlog.git", "type": "git"}, "_npmVersion": "3.9.6", "description": "logger for npm", "directories": {}, "_nodeVersion": "4.4.0", "dependencies": {"gauge": "~2.6.0", "set-blocking": "~2.0.0", "are-we-there-yet": "~1.1.2", "console-control-strings": "~1.1.0"}, "devDependencies": {"tap": "~5.7.0", "standard": "~7.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/npmlog-3.1.2.tgz_1466073052641_0.36111341998912394", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.0": {"name": "npmlog", "version": "4.0.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "npmlog@4.0.0", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/npmlog#readme", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "dist": {"shasum": "e094503961c70c1774eb76692080e8d578a9f88f", "tarball": "https://registry.npmjs.org/npmlog/-/npmlog-4.0.0.tgz", "integrity": "sha512-4HyeePH0eTvisKApQnKqi1e44wDZERAJvfavvdu2FRcHJQR0EthB+wY5h5IiIzK4dHGHlmHeGWO8LKyjFhl2aQ==", "signatures": [{"sig": "MEUCIDfxJsaPqP/uxAM5r0KUriuvKgWheu/511kIzrt/roF/AiEA4NyQ1dPBSVBjBwgsk0QRyiTVqDgteofeC4SL2vH+g/A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "log.js", "_from": ".", "files": ["log.js"], "_shasum": "e094503961c70c1774eb76692080e8d578a9f88f", "gitHead": "3ca8823fdfa66f54c72adde3fd2c4e0237e6302b", "scripts": {"test": "standard && tap test/*.js"}, "_npmUser": {"name": "zkat", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/npm/npmlog.git", "type": "git"}, "_npmVersion": "3.10.4", "description": "logger for npm", "directories": {}, "_nodeVersion": "5.10.1", "dependencies": {"gauge": "~2.6.0", "set-blocking": "~2.0.0", "are-we-there-yet": "~1.1.2", "console-control-strings": "~1.1.0"}, "devDependencies": {"tap": "~5.7.0", "standard": "~7.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/npmlog-4.0.0.tgz_1468888150556_0.3835553650278598", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.1": {"name": "npmlog", "version": "4.0.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "npmlog@4.0.1", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/npmlog#readme", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "dist": {"shasum": "d14f503b4cd79710375553004ba96e6662fbc0b8", "tarball": "https://registry.npmjs.org/npmlog/-/npmlog-4.0.1.tgz", "integrity": "sha512-TAleH95lxe4kHiUesHryWWrzHn53Mi2gk9Y+9/dX4ZlM59gnKJBVU4ifxZ9Iq4fJYMKY08MB9oaaPLUeVieJEw==", "signatures": [{"sig": "MEUCIQD5VQUAbVS1Dg6gdsMw4nqzFz3YJVIR5S57Lzw2brjqHgIgHH1mW2YaxWuNNsgQsYXYvcGyrOzzGEC9uIhFxYGN6Eg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "log.js", "_from": ".", "files": ["log.js"], "_shasum": "d14f503b4cd79710375553004ba96e6662fbc0b8", "gitHead": "c027c276f6f7e6c8d808767b0d611555e3ef5f61", "scripts": {"test": "standard && tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/npm/npmlog.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "logger for npm", "directories": {}, "_nodeVersion": "7.1.0", "dependencies": {"gauge": "~2.7.1", "set-blocking": "~2.0.0", "are-we-there-yet": "~1.1.2", "console-control-strings": "~1.1.0"}, "devDependencies": {"tap": "~5.7.0", "standard": "~7.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/npmlog-4.0.1.tgz_1479345245313_0.32757814647629857", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.2": {"name": "npmlog", "version": "4.0.2", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "npmlog@4.0.2", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/npmlog#readme", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "dist": {"shasum": "d03950e0e78ce1527ba26d2a7592e9348ac3e75f", "tarball": "https://registry.npmjs.org/npmlog/-/npmlog-4.0.2.tgz", "integrity": "sha512-EoRXwMcIkbPu0ufHuR6xqtN+oFW7HiULHVWENwMClAbZpE93wa0sZ8w1YmdEyRAQ0wakKFNXLVb2f441uHyJTA==", "signatures": [{"sig": "MEUCIEEXjKi0ekmWuVblOKSCRhWgPIrRZ4Qxe/D8NT5sBUcxAiEA2XLdgqRDcLCfWcH5nYlUKOWPjU3crXcampJuO49zGaQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "log.js", "_from": ".", "files": ["log.js"], "_shasum": "d03950e0e78ce1527ba26d2a7592e9348ac3e75f", "gitHead": "a3b7aed07790b674aa1fecfc81a61481abeaf882", "scripts": {"test": "standard && tap test/*.js"}, "_npmUser": {"name": "othiym23", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/npm/npmlog.git", "type": "git"}, "_npmVersion": "4.0.5", "description": "logger for npm", "directories": {}, "_nodeVersion": "7.2.0", "dependencies": {"gauge": "~2.7.1", "set-blocking": "~2.0.0", "are-we-there-yet": "~1.1.2", "console-control-strings": "~1.1.0"}, "devDependencies": {"tap": "~5.7.0", "standard": "~7.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/npmlog-4.0.2.tgz_1481572338217_0.2948465726803988", "host": "packages-12-west.internal.npmjs.com"}}, "4.1.0": {"name": "npmlog", "version": "4.1.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "npmlog@4.1.0", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/npmlog#readme", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "dist": {"shasum": "dc59bee85f64f00ed424efb2af0783df25d1c0b5", "tarball": "https://registry.npmjs.org/npmlog/-/npmlog-4.1.0.tgz", "integrity": "sha512-ocolIkZYZt8UveuiDS0yAkkIjid1o7lPG8cYm05yNYzBn8ykQtaiPMEGp8fY9tKdDgm8okpdKzkvu1y9hUYugA==", "signatures": [{"sig": "MEUCIQDRFAPAKZCTZiNIAJgGszPKXLyZ/5D5UZ5uxiUPlj1trQIgVOXBSm20tqd0KMYb+2GU9w2BVzZmLGgcrDE7Am2s1yg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "log.js", "files": ["log.js"], "gitHead": "46f88c4bcb35a39b22aa5b65d0958221dadb2bbe", "scripts": {"test": "standard && tap test/*.js"}, "_npmUser": {"name": "zkat", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/npm/npmlog.git", "type": "git"}, "_npmVersion": "5.0.0-beta.40", "description": "logger for npm", "directories": {}, "_nodeVersion": "7.9.0", "dependencies": {"gauge": "~2.7.3", "set-blocking": "~2.0.0", "are-we-there-yet": "~1.1.2", "console-control-strings": "~1.1.0"}, "devDependencies": {"tap": "~5.7.3", "standard": "~7.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/npmlog-4.1.0.tgz_1494018281527_0.3773633665405214", "host": "packages-18-east.internal.npmjs.com"}}, "4.1.1": {"name": "npmlog", "version": "4.1.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "npmlog@4.1.1", "maintainers": [{"name": "zkat", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/npmlog#readme", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "dist": {"shasum": "301349a832a751633ff63a42a230a6e7ecf02c6e", "tarball": "https://registry.npmjs.org/npmlog/-/npmlog-4.1.1.tgz", "integrity": "sha512-iiAM9qm23XbN/IaaX6zX5pXxFALOjsjq0K4YhrrtBMh6KxrezMv1oPMwfFgqIcgykw/uey7Thg4yWWOcFjeZxw==", "signatures": [{"sig": "MEYCIQCn7Yq0Yzz37jTM7JsSMC/S4reg2VJOklPayetZ4PT43QIhAMkMu5+pt3kTUcoy9ENAQTaKj8q/lpz2AEqlLWqXsXBp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "log.js", "files": ["log.js"], "gitHead": "a1093e72aebee4febc807a0a6fcece7e253c06c4", "scripts": {"test": "standard && tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/npm/npmlog.git", "type": "git"}, "_npmVersion": "5.0.4", "description": "logger for npm", "directories": {}, "_nodeVersion": "8.1.0", "dependencies": {"gauge": "~2.7.3", "set-blocking": "~2.0.0", "are-we-there-yet": "~1.1.2", "console-control-strings": "~1.1.0"}, "devDependencies": {"tap": "~5.7.3", "standard": "~7.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/npmlog-4.1.1.tgz_1498510598709_0.003092980245128274", "host": "s3://npm-registry-packages"}}, "4.1.2": {"name": "npmlog", "version": "4.1.2", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "npmlog@4.1.2", "maintainers": [{"name": "zkat", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/npmlog#readme", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "dist": {"shasum": "08a7f2a8bf734604779a9efa4ad5cc717abb954b", "tarball": "https://registry.npmjs.org/npmlog/-/npmlog-4.1.2.tgz", "integrity": "sha512-2uUqazuKlTaSI/dC8AzicUck7+IrEaOnN/e0jd3Xtt1KcGpwx30v50mL7oPyr/h9bL3E4aZccVwpwP+5W9Vjkg==", "signatures": [{"sig": "MEUCIGe39qazZkC65Fu6ZKj3cK4YG3iqQXFcns1mHz5FCSX0AiEA38RW4nb5sStSGAkG8/6+gWcL/lIAUgz35ZfmKa6aAfk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "log.js", "files": ["log.js"], "gitHead": "f7f9516d35b873c4e45b1aaeb78cff4e43b72c31", "scripts": {"test": "standard && tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/npm/npmlog.git", "type": "git"}, "_npmVersion": "5.0.4", "description": "logger for npm", "directories": {}, "_nodeVersion": "8.1.0", "dependencies": {"gauge": "~2.7.3", "set-blocking": "~2.0.0", "are-we-there-yet": "~1.1.2", "console-control-strings": "~1.1.0"}, "devDependencies": {"tap": "~5.7.3", "standard": "~7.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/npmlog-4.1.2.tgz_1498519394438_0.33615764300338924", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "npmlog", "version": "5.0.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "npmlog@5.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "gimli01", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/npmlog#readme", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "dist": {"shasum": "e6a41b556e9b34cb29ea132294676c07acb30efb", "tarball": "https://registry.npmjs.org/npmlog/-/npmlog-5.0.0.tgz", "fileCount": 4, "integrity": "sha512-ftpIiLjerL2tUg3dCqN8pOSoB90gqZlzv/gaZoxHaKjeLClrfJIEQ1Pdxi6qSzflz916Bljdy8dTWQ4J7hAFSQ==", "signatures": [{"sig": "MEQCIBUe0U9gvuc+5ZidX4IksXce8JsC8l64BeDxZ9CrH6JIAiBbVMY3pJsGLwApyjuga+GtEfqdqEENxhxlmPIPrWPinQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16330, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg+aWECRA9TVsSAnZWagAAVZsP/0fQw2VTJcAzf8yeKY9A\n+p8CGaJaoC1LS4CEFHgoY7FmGlANMcxAxYs2Jcta0BUq7l3qPCdBvddTz7VO\nNz5XX96ITxx2Oc6wArQo9qItPWAm/Tw3d6/8L5AgUdyUnmukMG7Ue3qvZxB3\naZQs+TU+Zrg2jTRJfR1+iMulFc+UTD1di4LGKwTFuejcBI4nB8nHV+kDh66W\nsz92KrS0OuCfkr9hXAuOZP2WEhN3AfToKEtFITOK+cBCl7LQjksr6pJJJjJ<PERSON>\ndGJWZpwGmKyN44NG8fWonSUfUP9LLrtFlxOUXBwGZCMZ7tYKoPQEnR7LkD9Q\nq0i+xvnCgSxk7zXMQv86KRj+63nNaIXEBFhJ2z/4JdWIFDrPPRp07B09nEpM\nTmMRSRtx7jg9QgBoib1i/KK7XCr+6VMzDKjMEfSrI3u8pQhSf4Ua4fmVLqBx\nvqUBlZRM50EEYA6+v9be37bETimMZBxBDqKH12HO/3JQcKnmOyOH4fojN4iS\n7ZU3mM8npoe3la4V3KPtHY+uwkpyzA0QjsfK4Jy9wvwnEkygZdFe14+h6EFf\nHr4ocNoGiXYml3aTj5LbPpbdmBlRe5w0jIUw/Z0h2ejS0Y9I+mSKufDyEFS4\n1w6KSCdrhuQic58UQP5LYEOh5eaMLg4GvkDLkm3UvS9C3dodKh/4mXDju8CP\nusjV\r\n=0DnC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "log.js", "gitHead": "15366fb7c90a0819dcf0fac1f95be7081a6f07a1", "scripts": {"lint": "npm run npmclilint -- \"*.*js\" \"test/**/*.*js\"", "test": "tap test/*.js --branches=95", "lintfix": "npm run lint -- --fix", "postsnap": "npm run lintfix --", "posttest": "npm run lint --", "npmclilint": "npmcli-lint"}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/npm/npmlog.git", "type": "git"}, "_npmVersion": "7.20.0", "description": "logger for npm", "directories": {}, "_nodeVersion": "14.17.1", "dependencies": {"gauge": "^3.0.0", "set-blocking": "^2.0.0", "are-we-there-yet": "^1.1.5", "console-control-strings": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.0.9", "@npmcli/lint": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/npmlog_5.0.0_1626973572734_0.5359424754133315", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "npmlog", "version": "5.0.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "npmlog@5.0.1", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/npmlog#readme", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "dist": {"shasum": "f06678e80e29419ad67ab964e0fa69959c1eb8b0", "tarball": "https://registry.npmjs.org/npmlog/-/npmlog-5.0.1.tgz", "fileCount": 4, "integrity": "sha512-AqZtDUWOMKs1G/8lwylVjrdYgqA4d9nu8hc+0gzRxlDb1I10+FHBGMXs6aiQHFdCUUlqH99MUMuLfzWDNDtfxw==", "signatures": [{"sig": "MEQCIFF3vx5WUbfrvYqyrnhZiRdWq1GkB+r4wv/r3gZC08pvAiAJxBGKoSrdSSYVYjQ+RbjD2aLjhOH9F0MNwM2Xj11quw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16566, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhMOClCRA9TVsSAnZWagAAprgP/2OUEddanB8XfqSxLJM8\ngJjYtpvDEDuDWkVcZc9BNbC1avrt+xW4MEFckcMsDpTY0sG7smUOwiX0PlFy\niq+BmuhSPEKNAqopIzI1mY2jELYFI0Egk+bjiMZBmhdeTaZI39KijLqZEbks\nTghBXCD0+tu31e3FglwXPOjjpn0klqJpcMyljh8FIte0M4weHfG7ArYl/2Hs\n4eLOyRspbui/JbrH4KMGBoUKv5GLZr5WYMVYX67Nt8tfhMulr7voB53YRiTB\n0lnypfetr7Xp6ty8BZrQdjVJFxRFy50LX1QbRoepDLKXbh3/jxAjQuBpXM7i\nvjwaE187oq3b/W5nJ0ALHD93joYr6+x65K2zJnsb6iHfFsnh6xvTYs+x2ZZi\nOLpzt1wP4vs4w5rrjTxIbbSPLZs/iC5FgIZxlmLgS5h1fsL3CayPQVB0WQtF\noFf8JO75d0Pr+v8r/wlBZZ4h5jf7SHIiMSVU0dRFmIBbTzYP58/fztTgMEVC\nwXsWIk0OfYZ5503g94ZfoLxont7CKg2uvKdjs7zcoAQSVtZwYrsGoJGOgSnT\nJG5XBN6o2iv48MD1T5kaSvpZsAPP3YFRPJ1AL/JcuZpYYZ9vLb9heydyiPrb\nM3FKaDyAj1WlSMS76AJf3JC+0HExZFKGBIUuPF7rys97RXKCWyLicBYYEwh5\nsWxP\r\n=43Za\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "log.js", "gitHead": "37ac908491ed4081c8b90d6bf11fc355f5ad097a", "scripts": {"lint": "npm run npmclilint -- \"*.*js\" \"test/**/*.*js\"", "test": "tap test/*.js --branches=95", "lintfix": "npm run lint -- --fix", "postsnap": "npm run lintfix --", "posttest": "npm run lint --", "npmclilint": "npmcli-lint"}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/npm/npmlog.git", "type": "git"}, "_npmVersion": "7.21.1", "description": "logger for npm", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"gauge": "^3.0.0", "set-blocking": "^2.0.0", "are-we-there-yet": "^2.0.0", "console-control-strings": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.0.9", "@npmcli/lint": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/npmlog_5.0.1_1630593188964_0.7168522351452797", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "npmlog", "version": "6.0.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "npmlog@6.0.0", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/npmlog#readme", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "tap": {"branches": 95}, "dist": {"shasum": "ba9ef39413c3d936ea91553db7be49c34ad0520c", "tarball": "https://registry.npmjs.org/npmlog/-/npmlog-6.0.0.tgz", "fileCount": 4, "integrity": "sha512-03ppFRGlsyUaQFbGC2C8QWJN/C/K7PsfyD9aQdhVKAQIH4sQBc8WASqFBP7O+Ut4d2oo5LoeoboB3cGdBZSp6Q==", "signatures": [{"sig": "MEUCIGeznLxSh0lfwhvJiEvtfUE4R+EgQYvu3O5G+KQQ9Ig6AiEAhR+TqhqVDBlfEEwrUZlANt01YMpft/hauALeIG/+5zM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16841, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhk+vWCRA9TVsSAnZWagAAMb8P/itFHGIET+RRqi5yXhAb\ndcGQa44rJM1TeJzKMX/1G49v4a9tkIJqzbNv6fI2baH3fefheJkbb6ILpI+j\nwCszA7B+1Xw/bM0bIRW4UvZZp+MZxTGzXhDkPsNWJ1v1zPeTTCSPxahqmYyv\n3ZhZcRgA79kkLBfXqd2EoZ5W0/oQ92sQ7MjMEMnnS+HY7o1G9FzG0PwfNi5A\nuOFcYJQcZck+PP38nqQofqH7mWkdAlGhgl9ahDCfXrtueCUsh+adYslzXJLs\nND6y/L1Y1z3pOldoOlph8IDvpf9sJSOmHNj3qDhhbSLMCbSl6bTYNBJ3GVK4\nmkMrt1t4uKVMMJ6jLeXRf5b4lV4ZGuipdDsFT65p//O2KDp8cVsfFRJTt+ZA\n4fiHSlaycFFQh6NJZRMSa1SO5C57plfHRq2Hof4Hf3W3MdAYz2eXmgvCrvFa\nz1658BIKUlZgCaVTCU+2L6BzmLin9EjV5Zv27ggg5FCStBcogjxSZgtU+rAb\nPhs2D7+2+2fe74kESFHgcofaWW1smUUyNP8ft85Lqw53yu3BSrTIfyY2D+MD\nBBsFV1CkRavFxR1PxvvVjK2X4H74KxX+rv14MFaA7WnTL3vwpNCXuU2KOGKg\nXjF+Tl4i1KRu4shjHtvOW0+GMYomMIOowbXmO2qyxNZQzVXDhF7SCcoZDuvx\naENi\r\n=gqV2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/log.js", "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}, "gitHead": "4c489761818a03913bf2f132564acc83b9bf400b", "scripts": {"lint": "eslint '**/*.js'", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "npm-template-check", "postsnap": "npm run lintfix --", "posttest": "npm run lint", "npmclilint": "npmcli-lint", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/npm/npmlog.git", "type": "git"}, "_npmVersion": "8.1.3", "description": "logger for npm", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"gauge": "^4.0.0", "set-blocking": "^2.0.0", "are-we-there-yet": "^2.0.0", "console-control-strings": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.0.9", "@npmcli/template-oss": "^2.3.0"}, "templateVersion": "2.3.0", "_npmOperationalInternal": {"tmp": "tmp/npmlog_6.0.0_1637084118152_0.858472544940055", "host": "s3://npm-registry-packages"}}, "6.0.1": {"name": "npmlog", "version": "6.0.1", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "npmlog@6.0.1", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/npmlog#readme", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "tap": {"branches": 95}, "dist": {"shasum": "06f1344a174c06e8de9c6c70834cfba2964bba17", "tarball": "https://registry.npmjs.org/npmlog/-/npmlog-6.0.1.tgz", "fileCount": 4, "integrity": "sha512-BTHDvY6nrRHuRfyjt1MAufLxYdVXZfd099H4+i1f0lPywNQyI4foeNXJRObB/uy+TYqUW0vAD9gbdSOXPst7Eg==", "signatures": [{"sig": "MEUCIQD5QGOocpxL3NpnhilFcsu3naFFlaXuxaxauSU2PyHmiAIgdLm8i7KKuUpvUzUqo+jzLYfQIJ5fjUN14y6vEZtbbX0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16929, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBA6NCRA9TVsSAnZWagAAmZcQAJzTdklvgx9WMBBmWExv\n72Syx/sgDcf17zOSnPGwKH0HvpwnGaqJ+E0cP2bm3sa/HXJTSkVkNYmjHrRq\n/n4uML/giatB30+MuMEP50ZI+TXh3vkpY1++Gp1LWi6NjL8JkcSZIAoaeK9x\niucAgKu0XhYgIJHv5NM2ijEOnZMvw2zaJWSerm6fjMn/F9xKrP5Y8T1lH5w3\nwywa9/1o9WZHoGGv6tRRiSZvz9gJ+ulU5WuiK0nUdZrYLVFqa6O1KU9QPVJ/\nc9nnNIb8w7h5DunwUVVzYREjgoZ/j0QmZDhRFWVZEu0Q/Gc/26HHqLvtQ6UQ\njDNK5DhePjjE2/9ADrS3Naq0yRC2bX8jAbPLw69xXkrJeDVxtXcudiULroY6\n5pe0bsCoa9R0gbryaHqouFfv4UNacQaa+A3Ag9FRyxB0hwdd4/WdpeGisCEx\neXDbb14QlYzecd7bMvY7Yo3rAWSVtV+B3e1Vmll6TNQ+KkGjpWtHd6BsDMVM\nAZA/K1GWR4gwueEWTypJeT3+YuHxq3WVgLd0njC0YB9cVteXyT3Xcqa2p1Pq\nBBrIDyhwzIelUZBrYv6H85o+elAU/imKAOgblEIiOmyNlbT1ECgDCb9feUXA\njp8JbTVphX2Ttmz3grlUDdir2UA8KRRF8WWCSViteveirQP51K6lFQqMZOO5\n6QY0\r\n=7ije\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/log.js", "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}, "gitHead": "3c31adc8fa6e64f8d8c4f735d992efa6faca50d0", "scripts": {"lint": "eslint '**/*.js'", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "npm-template-check", "postsnap": "npm run lintfix --", "posttest": "npm run lint", "npmclilint": "npmcli-lint", "preversion": "npm test", "postversion": "npm publish", "template-copy": "npm-template-copy --force", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/npm/npmlog.git", "type": "git"}, "_npmVersion": "8.4.1", "description": "logger for npm", "directories": {}, "templateOSS": {"version": "2.7.1"}, "_nodeVersion": "16.14.0", "dependencies": {"gauge": "^4.0.0", "set-blocking": "^2.0.0", "are-we-there-yet": "^3.0.0", "console-control-strings": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "@npmcli/template-oss": "^2.7.1"}, "_npmOperationalInternal": {"tmp": "tmp/npmlog_6.0.1_1644433036914_0.4705778977788231", "host": "s3://npm-registry-packages"}}, "6.0.2": {"name": "npmlog", "version": "6.0.2", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "npmlog@6.0.2", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/npmlog#readme", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "tap": {"branches": 95}, "dist": {"shasum": "c8166017a42f2dea92d6453168dd865186a70830", "tarball": "https://registry.npmjs.org/npmlog/-/npmlog-6.0.2.tgz", "fileCount": 4, "integrity": "sha512-/vBvz5Jfr9dT/aFWd0FIRf+T/Q2WBsLENygUaFUqstqsycmZAP/t5BvFJTK0viFmSUxiUKTUplWy5vt+rvKIxg==", "signatures": [{"sig": "MEUCIQDbRBWcBsZrbWZtrpo6HqKxbuGDydOeES1INxipVH8orwIgDa4GBjQ4g9IFb2SvBYz4UZ7iouodVRhReMrsx+ZOD6g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17096, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiYHkrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqiPw//Wuld0zPI2QP8SgFD3GREYJJ60FbCxVG2w/YQ913KZory0F6Z\r\nksoSsy3tAfxKjwETJ+1r1r3lQXs2Heq1pP9Kb79XRFee/tz5L/UbyBzOhyMZ\r\nL6jeL1TuPiAlDNWuhNEfN+ZRCOQz0tQx0JC7pAtAmzGVC3/8axWZ4vjjp/NU\r\n5KleW2WKCfkFC3IGP9by4hJ2sJcS+qkLX3WfuK+Xpm8MgSEoega20DFVJtj4\r\nAVn6zIC1rhs8QVHx9AM6rVbPr9e7h0GRscXgTreBImyhCT5Zy5L5hriF5kcW\r\n1BySY5QelWKSUmiTw4wV8FriC7OW7H/m4Egl4JYjmxB5Eq6Cdr7EGG4zJEJX\r\n1zJUtTjFnngkM+2TNEHGRjrNc0naIzAlmGkY8gh0CE81S6lUrgno6IfMi9tI\r\nB0Iq+RcCwSdOQHXVq4/nk9ukJ061iC3hgO60P3jWagXN8+VatArdYKrOdTcN\r\nxdAXglBraXjTqse1cVbwWxQdRPMSgbfpYvTudMnJ8vVYzgnwTRNi9IF2ZlBr\r\nEP+3OL0REMBzLsZtMDDE9IFZFF3LX6ySreQxlVztLU3vsjeTscv8v1cwGPKa\r\nSY7bqJh/bJH5wWzN0zensGols1HQl6OVpEbWpRtpglsEnsOwRsdhi4t20dUP\r\nFZx9whkvTN8LYxq0+0jXz/dXGoMcqSOxFf0=\r\n=lUyC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/log.js", "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "gitHead": "87e9ff0c40065bd1242a19d3f486ee750558c618", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "postsnap": "npm run lintfix --", "posttest": "npm run lint", "npmclilint": "npmcli-lint", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/npm/npmlog.git", "type": "git"}, "_npmVersion": "8.7.0", "description": "logger for npm", "directories": {}, "templateOSS": {"version": "3.4.1", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "16.14.2", "dependencies": {"gauge": "^4.0.3", "set-blocking": "^2.0.0", "are-we-there-yet": "^3.0.0", "console-control-strings": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "3.4.1", "@npmcli/eslint-config": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/npmlog_6.0.2_1650489642740_0.8093656921374428", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "npmlog", "version": "7.0.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "npmlog@7.0.0", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/npmlog#readme", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"], "branches": 95}, "dist": {"shasum": "09731bbb33019813704d6635b55f35a1b1784b34", "tarball": "https://registry.npmjs.org/npmlog/-/npmlog-7.0.0.tgz", "fileCount": 4, "integrity": "sha512-p+OVCIQx1Rehplt2DNgBERrKtE5Ej0/rqdcNz5PbohpKHDPprGAl142qQuozmzWUM9uNjvstEO+A92CwytJPcQ==", "signatures": [{"sig": "MEQCIB19wnJEW1lJ9QKQXFpOuamd2IhoJeUteKtr+NEiSENkAiB8DVoMGo+9IB+RetWy7to82AWEywY/WyKRzZPUiKfZTw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17045, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSaHdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrAmw/+OrV2bW+IQs9H9I1Iz4f+84g0jdYSU40Hn6l4SvxyrYvAZEUg\r\njaaNu3hoD6N6yhAhXQNJbVD6DjV6S6AZoNDcANP4/lc3r8Z315VcQWjbMQt/\r\njjCAuJfkcdvVEcn1Cv/wYj6zm+iEc5jsjontmSRvMT/xV4Z+eaqMkrvHDlhl\r\n+XsEyiAPzmczQldI3gHoCeHA6ov8PjAzsCxrrPZIia/NNxcwGP8sSNakFQ4U\r\nOjT+PmtBPDsZPjH7yIaVzZjT7aFpsdwtuvBpCFIaId3J+BiaawZ1trWRVo1z\r\ng+86X/cSjPYNxIKR/6b8s+3+45MKTvqsexK+OiUbxIXTYxIf0nmARdMQoF4k\r\nSC9+xkPnrayAnK8oo1vCtpiusBqsIuuG6oaR2l+HuTrsqnZ/q66AG/I9WY2z\r\nGewTv9Yxa+NeFme2ob0PaJIVhPE0aXmc3qfBMrbkWI+6rT3MQKTRACrjGBTV\r\n7U2PDyJv5jvuWVeLydvPA1UZT1Y74PsLBvfUtncHeqBJHvXZNAPvwKuPi0N1\r\n5kG2i7s09gy6kHrYe0nPqQNgC/a2fbgwflby6c7PV726SLwjXu5pQ9lmTtfR\r\n/p1zhOdPeVcBD2iWuJX2NJJ8HK7CrmWOvXF2NKBDEtivAO5U6fwuFkvHJEWt\r\n8BisrrA4r9g3fBYUJAb71bvNr0U94vBL3H8=\r\n=mcz1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/log.js", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "gitHead": "040c66367057ee96514b1297c1347c7468f2f3c5", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "postsnap": "npm run lintfix --", "posttest": "npm run lint", "npmclilint": "npmcli-lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/npm/npmlog.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "logger for npm", "directories": {}, "templateOSS": {"version": "4.5.1", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.10.0", "dependencies": {"gauge": "^5.0.0", "set-blocking": "^2.0.0", "are-we-there-yet": "^4.0.0", "console-control-strings": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "4.5.1", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/npmlog_7.0.0_1665769948842_0.4028305437232067", "host": "s3://npm-registry-packages"}}, "7.0.1": {"name": "npmlog", "version": "7.0.1", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "npmlog@7.0.1", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/npmlog#readme", "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"], "branches": 95}, "dist": {"shasum": "7372151a01ccb095c47d8bf1d0771a4ff1f53ac8", "tarball": "https://registry.npmjs.org/npmlog/-/npmlog-7.0.1.tgz", "fileCount": 4, "integrity": "sha512-uJ0YFk/mCQpLBt+bxN88AKd+gyqZvZDbtiNxk6Waqcj2aPRyfVx8ITawkyQynxUagInjdYT1+qj4NfA5KJJUxg==", "signatures": [{"sig": "MEUCIQCYUn00VadIpdhJ5gCnWDJXjdZQ81awth5qgaYsQrSM1QIgM9rvN8MjU/DOfpR7ETiCR5OlQ/YSRGQbtsA4lUSI570=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17023, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjT5JJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoDQQ//XnU3qP0DSlBFajvrOJALD/TVbvLxce3rm9YqAjV7SOxOFmZD\r\nqgtXNfYpdCcJ7bqn8OD9445M7ulQiUAmO+UAxSevtNTfC/yv49YiM2RSGOMB\r\nmxThnWDMqF4VpB8XuYJ+8LrDDWXas3Yxzklt4gMeAuzBJixy3l2yJ8CDUzJe\r\ngh+U9jCE6PFtiN7CNayRnnFKsyTk2FLGDR5+hxmZWihvp7kkh7Xa8pD9AgZZ\r\nhX53glzjWmfpf8JeBdTqLq0Ojdr7hcSekxlldvAVgxRYrrgUqMRVI5kzpwHE\r\nbjRs/Cuqvg4Yz11rH0p1+k0Gd9QWBJRERfgGrjcy8NBhHhHtMAipWLRuEetb\r\nKp0o9UByOWuwqxKt3F2fSBq5Ea90fu4jzNENfLJvDYDxLBdL8Rc/qlyJXcPH\r\nBSQSgdfrPPciiWt8YemQvc+tW0noLR7v3hUv210Z0egCBJC96ePSHcjxacLX\r\ntKSIGDVaePvX/3Unt5lgNlFR4fzYwBZDmk5ypI5bEU30LL23zyDW2et+m/sL\r\nmLIWDAkcy1Im3HrX32caqkqw6ENWMlU6y0iknp8WXmwC74VYureyNtBUWMvi\r\n7QYKAS/jtLgUdsEY2+kgM/p+d78azKkd2LZKbqZsfhIGJ0rQuizuXHrL1l3c\r\niIRXhAVjdBXvFWfeQDjHhK6tuki8O/YylwA=\r\n=axJk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/log.js", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "gitHead": "ae1f107953b0882ea2c44989e12d5ee08849663b", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "postsnap": "npm run lintfix --", "posttest": "npm run lint", "npmclilint": "npmcli-lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/npm/npmlog.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "logger for npm", "directories": {}, "templateOSS": {"version": "4.6.1", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.10.0", "dependencies": {"gauge": "^5.0.0", "set-blocking": "^2.0.0", "are-we-there-yet": "^4.0.0", "console-control-strings": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "4.6.1", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/npmlog_7.0.1_1666159176927_0.8280302570565217", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2012-06-06T09:08:16.978Z", "modified": "2024-07-31T15:24:39.832Z", "0.0.1": "2012-06-06T09:08:18.466Z", "0.0.2": "2012-06-06T21:10:11.014Z", "0.0.3": "2013-06-20T01:56:10.752Z", "0.0.4": "2013-07-11T06:16:28.666Z", "0.0.5": "2013-10-24T07:05:11.752Z", "0.0.6": "2013-10-24T07:15:02.897Z", "0.1.0": "2014-06-06T04:26:18.361Z", "0.1.1": "2014-06-13T23:51:04.789Z", "1.0.0": "2015-01-07T17:41:44.265Z", "1.1.0": "2015-02-01T18:37:29.804Z", "1.2.0": "2015-02-28T23:07:02.634Z", "1.2.1": "2015-05-19T01:40:29.670Z", "2.0.0": "2015-11-05T07:19:06.878Z", "2.0.1": "2016-01-21T18:58:56.587Z", "2.0.2": "2016-01-28T00:38:33.387Z", "2.0.3": "2016-03-16T00:43:56.419Z", "2.0.4": "2016-05-19T00:10:38.359Z", "3.0.0": "2016-06-07T01:30:36.683Z", "3.1.0": "2016-06-16T01:25:34.864Z", "3.1.1": "2016-06-16T06:52:31.337Z", "3.1.2": "2016-06-16T10:30:53.032Z", "4.0.0": "2016-07-19T00:29:10.793Z", "4.0.1": "2016-11-17T01:14:05.535Z", "4.0.2": "2016-12-12T19:52:18.463Z", "4.1.0": "2017-05-05T21:04:44.607Z", "4.1.1": "2017-06-26T20:56:38.797Z", "4.1.2": "2017-06-26T23:23:14.534Z", "5.0.0": "2021-07-22T17:06:12.924Z", "5.0.1": "2021-09-02T14:33:09.109Z", "6.0.0": "2021-11-16T17:35:18.641Z", "6.0.1": "2022-02-09T18:57:17.076Z", "6.0.2": "2022-04-20T21:20:42.967Z", "7.0.0": "2022-10-14T17:52:29.016Z", "7.0.1": "2022-10-19T05:59:37.106Z"}, "bugs": {"url": "https://github.com/npm/npmlog/issues"}, "author": {"name": "GitHub Inc."}, "license": "ISC", "homepage": "https://github.com/npm/npmlog#readme", "repository": {"url": "git+https://github.com/npm/npmlog.git", "type": "git"}, "description": "logger for npm", "maintainers": [{"email": "<EMAIL>", "name": "hashtagchris"}, {"email": "<EMAIL>", "name": "reggi"}, {"email": "<EMAIL>", "name": "npm-cli-ops"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "fritzy"}, {"email": "<EMAIL>", "name": "gar"}], "readme": "# npmlog\n\nThe logger util that npm uses.\n\nThis logger is very basic.  It does the logging for npm.  It supports\ncustom levels and colored output.\n\nBy default, logs are written to stderr.  If you want to send log messages\nto outputs other than streams, then you can change the `log.stream`\nmember, or you can just listen to the events that it emits, and do\nwhatever you want with them.\n\n# Installation\n\n```console\nnpm install npmlog --save\n```\n\n# Basic Usage\n\n```javascript\nvar log = require('npmlog')\n\n// additional stuff ---------------------------+\n// message ----------+                         |\n// prefix ----+      |                         |\n// level -+   |      |                         |\n//        v   v      v                         v\n    log.info('fyi', 'I have a kitty cat: %j', myKittyCat)\n```\n\n## log.level\n\n* {String}\n\nThe level to display logs at.  Any logs at or above this level will be\ndisplayed.  The special level `silent` will prevent anything from being\ndisplayed ever.\n\n## log.record\n\n* {Array}\n\nAn array of all the log messages that have been entered.\n\n## log.maxRecordSize\n\n* {Number}\n\nThe maximum number of records to keep.  If log.record gets bigger than\n10% over this value, then it is sliced down to 90% of this value.\n\nThe reason for the 10% window is so that it doesn't have to resize a\nlarge array on every log entry.\n\n## log.prefixStyle\n\n* {Object}\n\nA style object that specifies how prefixes are styled.  (See below)\n\n## log.headingStyle\n\n* {Object}\n\nA style object that specifies how the heading is styled.  (See below)\n\n## log.heading\n\n* {String} Default: \"\"\n\nIf set, a heading that is printed at the start of every line.\n\n## log.stream\n\n* {Stream} Default: `process.stderr`\n\nThe stream where output is written.\n\n## log.enableColor()\n\nForce colors to be used on all messages, regardless of the output\nstream.\n\n## log.disableColor()\n\nDisable colors on all messages.\n\n## log.enableProgress()\n\nEnable the display of log activity spinner and progress bar\n\n## log.disableProgress()\n\nDisable the display of a progress bar\n\n## log.enableUnicode()\n\nForce the unicode theme to be used for the progress bar.\n\n## log.disableUnicode()\n\nDisable the use of unicode in the progress bar.\n\n## log.setGaugeTemplate(template)\n\nSet a template for outputting the progress bar. See the [gauge documentation] for details.\n\n[gauge documentation]: https://npmjs.com/package/gauge\n\n## log.setGaugeThemeset(themes)\n\nSelect a themeset to pick themes from for the progress bar. See the [gauge documentation] for details.\n\n## log.pause()\n\nStop emitting messages to the stream, but do not drop them.\n\n## log.resume()\n\nEmit all buffered messages that were written while paused.\n\n## log.log(level, prefix, message, ...)\n\n* `level` {String} The level to emit the message at\n* `prefix` {String} A string prefix.  Set to \"\" to skip.\n* `message...` Arguments to `util.format`\n\nEmit a log message at the specified level.\n\n## log\\[level](prefix, message, ...)\n\nFor example,\n\n* log.silly(prefix, message, ...)\n* log.verbose(prefix, message, ...)\n* log.info(prefix, message, ...)\n* log.http(prefix, message, ...)\n* log.warn(prefix, message, ...)\n* log.error(prefix, message, ...)\n\nLike `log.log(level, prefix, message, ...)`.  In this way, each level is\ngiven a shorthand, so you can do `log.info(prefix, message)`.\n\n## log.addLevel(level, n, style, disp)\n\n* `level` {String} Level indicator\n* `n` {Number} The numeric level\n* `style` {Object} Object with fg, bg, inverse, etc.\n* `disp` {String} Optional replacement for `level` in the output.\n\nSets up a new level with a shorthand function and so forth.\n\nNote that if the number is `Infinity`, then setting the level to that\nwill cause all log messages to be suppressed.  If the number is\n`-Infinity`, then the only way to show it is to enable all log messages.\n\n## log.newItem(name, todo, weight)\n\n* `name` {String} Optional; progress item name.\n* `todo` {Number} Optional; total amount of work to be done. Default 0.\n* `weight` {Number} Optional; the weight of this item relative to others. Default 1.\n\nThis adds a new `are-we-there-yet` item tracker to the progress tracker. The\nobject returned has the `log[level]` methods but is otherwise an\n`are-we-there-yet` `Tracker` object.\n\n## log.newStream(name, todo, weight)\n\nThis adds a new `are-we-there-yet` stream tracker to the progress tracker. The\nobject returned has the `log[level]` methods but is otherwise an\n`are-we-there-yet` `TrackerStream` object.\n\n## log.newGroup(name, weight)\n\nThis adds a new `are-we-there-yet` tracker group to the progress tracker. The\nobject returned has the `log[level]` methods but is otherwise an\n`are-we-there-yet` `TrackerGroup` object.\n\n# Events\n\nEvents are all emitted with the message object.\n\n* `log` Emitted for all messages\n* `log.<level>` Emitted for all messages with the `<level>` level.\n* `<prefix>` Messages with prefixes also emit their prefix as an event.\n\n# Style Objects\n\nStyle objects can have the following fields:\n\n* `fg` {String} Color for the foreground text\n* `bg` {String} Color for the background\n* `bold`, `inverse`, `underline` {Boolean} Set the associated property\n* `bell` {Boolean} Make a noise (This is pretty annoying, probably.)\n\n# Message Objects\n\nEvery log event is emitted with a message object, and the `log.record`\nlist contains all of them that have been created.  They have the\nfollowing fields:\n\n* `id` {Number}\n* `level` {String}\n* `prefix` {String}\n* `message` {String} Result of `util.format()`\n* `messageRaw` {Array} Arguments to `util.format()`\n\n# Blocking TTYs\n\nWe use [`set-blocking`](https://npmjs.com/package/set-blocking) to set\nstderr and stdout blocking if they are tty's and have the setBlocking call.\nThis is a work around for an issue in early versions of Node.js 6.x, which\nmade stderr and stdout non-blocking on OSX. (They are always blocking\nWindows and were never blocking on Linux.) `npmlog` needs them to be blocking\nso that it can allow output to stdout and stderr to be interlaced.\n", "readmeFilename": "README.md", "users": {"dm07": true, "bojand": true, "daizch": true, "hobord": true, "lgomez": true, "testabc": true, "wenbing": true, "bobmhong": true, "brugnara": true, "merrickp": true, "moimikey": true, "anddoutoi": true, "evanyeung": true, "luizbills": true, "max_devjs": true, "papasavva": true, "wolfram77": true, "zekesonxx": true, "morogasper": true, "mysticatea": true, "ragingsmurf": true, "wangnan0610": true, "xinwangwang": true, "chinawolf_wyp": true, "littlepumpkin": true, "mattmcfarland": true, "schnittstabil": true}}