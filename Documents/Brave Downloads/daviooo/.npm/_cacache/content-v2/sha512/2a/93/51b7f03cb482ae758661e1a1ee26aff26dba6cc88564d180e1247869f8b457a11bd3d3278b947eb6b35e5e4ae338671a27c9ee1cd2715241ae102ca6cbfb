{"source": 1091753, "name": "safe-eval", "dependency": "safe-eval", "title": "safe-eval vulnerable to Sandbox Bypass due to improper input sanitization", "url": "https://github.com/advisories/GHSA-79xf-67r4-q2jj", "severity": "critical", "versions": ["0.0.0", "0.1.0", "0.2.0", "0.3.0", "0.4.0", "0.4.1"], "vulnerableVersions": ["0.0.0", "0.1.0", "0.2.0", "0.3.0", "0.4.0", "0.4.1"], "cwe": ["CWE-1321"], "cvss": {"score": 10, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:H"}, "range": "<=0.4.2", "id": "AC9b44tvavTm6ID4ZzANVZo6SipXZgH+C5KztYbeUB+nl4aeEtmDyzftCDa5UCSFdvlT0qvnudI/+9gf2EFpAQ=="}