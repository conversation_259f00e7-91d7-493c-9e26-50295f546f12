{"_id": "cross-fetch", "_rev": "82-d1604be54820b424e9a836f6f027d4ab", "name": "cross-fetch", "dist-tags": {"latest-v2.x": "2.2.6", "latest-v3.x": "3.2.0", "latest-v4.x": "4.1.0", "latest": "4.1.0"}, "versions": {"0.0.2": {"name": "cross-fetch", "version": "0.0.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@0.0.2", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "dist": {"shasum": "dabc9321c555100838236d230389087ef95ce5ce", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-0.0.2.tgz", "integrity": "sha512-tS5toTqD6ezTil0mXfWZL3H8EyCG/J2tGqyqsXKuYTCDjqJt8rEWWHvt5h2aD6zYjfprzLVp/9603xGFZHqQLw==", "signatures": [{"sig": "MEQCIQDHQdp/Jb+Fl1wdk0XTud6AmkcIjQRIBk6ZCKw00TFi2AIfHmEeq6LT9+dx4DJc4mLQaS7J1salS197H9IkSmqJcQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "fetch-node.js", "_from": ".", "_shasum": "dabc9321c555100838236d230389087ef95ce5ce", "browser": "fetch-bundler.js", "gitHead": "afd3ac00cc7cf6be6ee366a91cbb211cae04db03", "scripts": {"lint": "eslint .", "test": "npm run lint && npm run mocha", "mocha": "mocha", "prepush": "npm test"}, "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "2.14.2", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "_nodeVersion": "4.0.0", "dependencies": {"node-fetch": "1.7.3", "whatwg-fetch": "2.0.3"}, "devDependencies": {"chai": "4.1.2", "nock": "9.0.19", "husky": "^0.14.3", "mocha": "3.5.3", "eslint": "4.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch-0.0.2.tgz_1506192747535_0.38779060170054436", "host": "s3://npm-registry-packages"}}, "0.0.3": {"name": "cross-fetch", "version": "0.0.3", "keywords": ["fetch", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@0.0.3", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "dist": {"shasum": "7ae76de287bd0fa034d75128d39f1f377959f910", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-0.0.3.tgz", "integrity": "sha512-2nbnm6zZ0i3Vr/pwfMID9fmduyZGmq3D1SnDVq68508KppLMIaPxLK147RXsajiNHYPmd3zp6n6Bngbw0J7Uyw==", "signatures": [{"sig": "MEQCIGRzhCs/pecrknpdMTbNyIBcUTqzrL3M+PIVQpmNBdrTAiAlJ6rNKyg3X+MADaT/FbJMOdqsXuds8vvT+hYVrt66lg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "fetch-node.js", "_from": ".", "_shasum": "7ae76de287bd0fa034d75128d39f1f377959f910", "browser": "fetch-bundler.js", "gitHead": "2961545d03edccfcd29ca239433d08ad6188cfdd", "scripts": {"lint": "eslint .", "test": "npm run lint && npm run mocha", "mocha": "mocha", "prepush": "npm test"}, "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "_nodeVersion": "6.10.0", "dependencies": {"node-fetch": "1.7.3", "whatwg-fetch": "2.0.3", "fetch-ponyfill": "4.1.0"}, "devDependencies": {"chai": "4.1.2", "nock": "9.0.19", "husky": "^0.14.3", "mocha": "3.5.3", "eslint": "4.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch-0.0.3.tgz_1506474015384_0.7208899806719273", "host": "s3://npm-registry-packages"}}, "0.0.4": {"name": "cross-fetch", "version": "0.0.4", "keywords": ["fetch", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@0.0.4", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "dist": {"shasum": "3d1ae337ab53f9a097d90b0abde1118118e39447", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-0.0.4.tgz", "integrity": "sha512-ONoKkq+5kRq2wNfEj4gAF6b5C1083z6ZuBCvyVScTGRGQzp7ZgWbL8KADca3hU1YMdV3ejyK3NynJatFL2sTBA==", "signatures": [{"sig": "MEYCIQCMYTWTFZj3U7XBFU/sBzGsxP4H/6xHjBcDbxXogdA+FgIhAOdTb2DhJ6S2rebOhirsC+yROxO9C+flpQY28hMO/Ytt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "fetch-node.js", "_from": ".", "_shasum": "3d1ae337ab53f9a097d90b0abde1118118e39447", "browser": "fetch-bundler.js", "gitHead": "897edf3200b8bc7db056a55f076fade54e49953c", "scripts": {"lint": "eslint .", "test": "npm run lint && npm run mocha", "mocha": "mocha", "prepush": "npm test"}, "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "_nodeVersion": "6.10.0", "dependencies": {"node-fetch": "1.7.3", "whatwg-fetch": "2.0.3", "fetch-ponyfill": "4.1.0"}, "devDependencies": {"chai": "4.1.2", "nock": "9.0.19", "husky": "^0.14.3", "mocha": "3.5.3", "eslint": "4.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch-0.0.4.tgz_1506476097172_0.7924986633006483", "host": "s3://npm-registry-packages"}}, "0.0.5": {"name": "cross-fetch", "version": "0.0.5", "keywords": ["fetch", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@0.0.5", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "dist": {"shasum": "714a46e225e616021b98bb7080e2c52937ca0a35", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-0.0.5.tgz", "integrity": "sha512-HH39iOkx2UrRwbe4/bAoPlOhQt7ylowLrsqkLtfXLnANhhfdLT4q1+/E6ikXrgM28XikYpCTZjcfa2foXsURyw==", "signatures": [{"sig": "MEUCIQDKGtkZu2OOEonNMh41fH6cX93M2VtbgD+RPHoFtBxuNwIgUwuK2jMg9ERjJr9JAGrKPmXExCyJoiqSjw/1NZbXhj8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "fetch-node.js", "_from": ".", "_shasum": "714a46e225e616021b98bb7080e2c52937ca0a35", "browser": "fetch-bundler.js", "gitHead": "5b4c453dc10fc65c2bbbf2c4e25cdb4755096b43", "scripts": {"lint": "eslint .", "test": "npm run lint && npm run mocha", "mocha": "mocha", "prepush": "npm test"}, "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "_nodeVersion": "6.10.0", "dependencies": {"node-fetch": "1.7.3", "whatwg-fetch": "2.0.3", "fetch-ponyfill": "4.1.0"}, "devDependencies": {"chai": "4.1.2", "nock": "9.0.19", "husky": "^0.14.3", "mocha": "3.5.3", "eslint": "4.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch-0.0.5.tgz_1506489029969_0.725010289112106", "host": "s3://npm-registry-packages"}}, "0.0.6": {"name": "cross-fetch", "version": "0.0.6", "keywords": ["fetch", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@0.0.6", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "dist": {"shasum": "617b00c5c4a1ec09b5bb1faa0c86954abd1cc3a2", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-0.0.6.tgz", "integrity": "sha512-4fx1CkHlMik9pi6B7I0GyvresmfZF5/6n+Wy6qQ/i++cePE7tJ9WZJrKqUuiiidM0ArCsIehc0SaGCcyXaTYvQ==", "signatures": [{"sig": "MEQCIFJfUcPezbdIQDhfIhBw6lFH/sl36nsJW1DvY3h5Ih0uAiA2CM4CUUlpdPYbMkgg8DJ3uGQLLOk9JRjsKCVO1xR7YQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "fetch-node.js", "_from": ".", "_shasum": "617b00c5c4a1ec09b5bb1faa0c86954abd1cc3a2", "browser": "dist/fetch-browser.js", "gitHead": "abd1494aae46eaac6d123118286cee7d41b8d930", "scripts": {"lint": "eslint .", "test": "npm run lint && npm run mocha", "build": "npm run build:polyfill && npm run build:ponyfill", "mocha": "mocha", "serve": "http-server", "prepush": "npm test", "prepublish": "RU_ENV=production npm run build && npm test", "build:polyfill": "RU_TYPE=polyfill rollup -c", "build:ponyfill": "RU_TYPE=ponyfill rollup -c"}, "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "_nodeVersion": "6.10.0", "dependencies": {"node-fetch": "1.7.3", "whatwg-fetch": "2.0.3"}, "devDependencies": {"chai": "4.1.2", "nock": "9.0.19", "husky": "0.14.3", "mocha": "3.5.3", "eslint": "4.7.2", "rollup": "0.50.0", "http-server": "0.10.0", "rollup-plugin-uglify": "2.0.1", "rollup-plugin-commonjs": "8.2.1", "rollup-plugin-node-resolve": "3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch-0.0.6.tgz_1506807539537_0.46208966709673405", "host": "s3://npm-registry-packages"}}, "0.0.7": {"name": "cross-fetch", "version": "0.0.7", "keywords": ["fetch", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@0.0.7", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "dist": {"shasum": "f4274e89be0befc8f94f6dd0a701574d6614a126", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-0.0.7.tgz", "integrity": "sha512-qEsoLKnx2F2Ua6L50hI7WoqBovLA4SeAB5eoP3ySd+0tZCZVxiTzEup44oD89/5RUciguJte3thtZkNwN2/kvA==", "signatures": [{"sig": "MEUCIF3zkg3WGA4wgBfIOmarp1mo5EisdtQwmqiuxjvJtdqtAiEA5FKtnotu0XuXuiD0XJqfWrXY3tENnWKzRDccSBL2oXk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "fetch-node.js", "_from": ".", "files": ["dist", "polyfill", "fetch-node.js", "fetch-node-polyfill.js", "fetch-browser-polyfill.js"], "_shasum": "f4274e89be0befc8f94f6dd0a701574d6614a126", "browser": "dist/fetch-browser.js", "gitHead": "ced2fa75adde68a7d6713f1957b905117ac324f6", "scripts": {"lint": "eslint .", "test": "npm run lint && npm run mocha", "build": "npm run build:polyfill && npm run build:ponyfill", "mocha": "mocha", "serve": "http-server", "prepush": "npm test", "prepublish": "RU_ENV=production npm run build && npm test", "build:polyfill": "RU_TYPE=polyfill rollup -c", "build:ponyfill": "RU_TYPE=ponyfill rollup -c"}, "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "_nodeVersion": "6.10.0", "dependencies": {"node-fetch": "1.7.3", "whatwg-fetch": "2.0.3"}, "devDependencies": {"chai": "4.1.2", "nock": "9.0.19", "husky": "0.14.3", "mocha": "3.5.3", "eslint": "4.7.2", "rollup": "0.50.0", "http-server": "0.10.0", "rollup-plugin-uglify": "2.0.1", "rollup-plugin-commonjs": "8.2.1", "rollup-plugin-node-resolve": "3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch-0.0.7.tgz_1506809307916_0.2153799335937947", "host": "s3://npm-registry-packages"}}, "0.0.8": {"name": "cross-fetch", "version": "0.0.8", "keywords": ["fetch", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@0.0.8", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "dist": {"shasum": "01ed94dc407df2c00f1807fde700a7cfa48a205c", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-0.0.8.tgz", "integrity": "sha512-7D2Bf4H0CkqncAEi/Y2m5Hs2iLSH8K03mhkYSLchtddWd+LUOOxQ2huHUF2V75VkLWS70jqDWgZEnYurCg0s7A==", "signatures": [{"sig": "MEUCIB8biPC6U41VhwoSERxFFGDbM97BcWEnlune9W3SivVwAiEAlGAdqcr4yKnsKn9vwyR8BgnCnLLPOGsfEPOigQ9oxC0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "fetch-node.js", "_from": ".", "files": ["dist", "polyfill", "fetch-node.js", "fetch-node-polyfill.js", "fetch-browser-polyfill.js"], "_shasum": "01ed94dc407df2c00f1807fde700a7cfa48a205c", "browser": "dist/fetch-browser.js", "gitHead": "f87e42ddcce9241242d3d18f9997f9acbe3bb841", "scripts": {"lint": "eslint .", "test": "npm run lint && npm run mocha", "build": "rollup -c", "mocha": "mocha", "prepush": "npm test", "prepublish": "NODE_ENV=production npm run build && npm test"}, "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "_nodeVersion": "6.10.0", "dependencies": {"node-fetch": "1.7.3", "whatwg-fetch": "2.0.3"}, "devDependencies": {"chai": "4.1.2", "nock": "9.0.19", "husky": "0.14.3", "mocha": "3.5.3", "eslint": "4.7.2", "rollup": "0.50.0", "rollup-plugin-uglify": "2.0.1", "rollup-plugin-commonjs": "8.2.1", "rollup-plugin-node-resolve": "3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch-0.0.8.tgz_1506823336898_0.6666918101254851", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "cross-fetch", "version": "1.0.0", "keywords": ["fetch", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@1.0.0", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "dist": {"shasum": "ab989f2f05222790359a48da43edcbd0400b821a", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-1.0.0.tgz", "integrity": "sha512-KHn5u7Unt+YEXPzjua34/Re9/r8+WKBHd2KyQhFtUyFHL6KRgLb45TPbjwcIfhc7OScP3o9qVln9VQYuy7lS2Q==", "signatures": [{"sig": "MEQCIE70JmiJs5etN0Y5VSXv00NNhuVvmavi8+YWOrwmha7dAiBMjIkkMZm1XCVirJyHwyEk75nsa8+CTQd/uWb0Htd73Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/node.js", "_from": ".", "files": ["dist", "polyfill"], "_shasum": "ab989f2f05222790359a48da43edcbd0400b821a", "browser": "dist/browser.js", "gitHead": "bf8d6ade96fd7f8f84e707e49583500025e9edac", "scripts": {"lint": "eslint .", "test": "npm run -s mocha && npm run -s lint", "build": "rollup -c", "mocha": "mocha", "prepush": "npm test -s", "prebuild": "npm test -s", "prepublishOnly": "npm run -s build"}, "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "_nodeVersion": "6.11.5", "dependencies": {"node-fetch": "1.7.3", "whatwg-fetch": "2.0.3"}, "devDependencies": {"chai": "4.1.2", "nock": "9.0.27", "husky": "0.14.3", "mocha": "4.0.1", "eslint": "4.10.0", "rollup": "0.50.0", "rollup-plugin-copy": "0.2.3", "rollup-plugin-uglify": "2.0.1", "rollup-plugin-node-resolve": "3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch-1.0.0.tgz_1509285653224_0.11592822126112878", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "cross-fetch", "version": "1.1.0", "keywords": ["fetch", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@1.1.0", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "dist": {"shasum": "adcb4c4522411998408c3a1afeb928284c882175", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-1.1.0.tgz", "integrity": "sha512-/aiDZ33cIrQaUv3OjPDrKmNH6Pt3OJBq4iYmhJQtwq4zV259dkxkugPh0wDht/gKAFKgZjkdS8PmrJtUxTIMBQ==", "signatures": [{"sig": "MEYCIQC7BOtYDUY/fpcp0Ri3/eEQgiawfDlUDB9JqS/aRWXt9gIhALRcTox2prpc0yI+OdZb6UuzeB8z3z5BjPlJwSsDq7cM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/node.js", "_from": ".", "files": ["dist", "polyfill"], "_shasum": "adcb4c4522411998408c3a1afeb928284c882175", "browser": "dist/browser.js", "gitHead": "cd547b8ded74fefbda2514a0c36d99b5b2aee686", "scripts": {"lint": "eslint .", "test": "npm run -s mocha && npm run -s lint", "build": "rollup -c", "mocha": "mocha", "prepush": "npm test -s", "prebuild": "npm test -s", "prepublishOnly": "npm run -s build"}, "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "_nodeVersion": "6.11.5", "dependencies": {"node-fetch": "1.7.3", "whatwg-fetch": "2.0.3"}, "devDependencies": {"chai": "4.1.2", "nock": "9.0.27", "husky": "0.14.3", "mocha": "4.0.1", "eslint": "4.10.0", "rollup": "0.50.0", "rollup-plugin-copy": "0.2.3", "rollup-plugin-uglify": "2.0.1", "rollup-plugin-node-resolve": "3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch-1.1.0.tgz_1509629213121_0.06447058473713696", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "cross-fetch", "version": "1.1.1", "keywords": ["fetch", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@1.1.1", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "dist": {"shasum": "dede6865ae30f37eae62ac90ebb7bdac002b05a0", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-1.1.1.tgz", "integrity": "sha512-+VJE04+UfxxmBfcnmAu/lKor53RUCx/1ilOti4p+JgrnLQ4AZZIRoe2OEd76VaHyWQmQxqKnV+TaqjHC4r0HWw==", "signatures": [{"sig": "MEYCIQCvZ0Pta41gzqd/+3hBdJQIj+/u5EubviFPJ6twYywAvgIhAJEdBL3oXsnafS7ORsgE2PXJFIm9FaUt3gX7JuKR3eDa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/node.js", "files": ["dist", "polyfill"], "browser": "dist/browser.js", "gitHead": "64c6101348e5a56d912af724f177368c52669172", "scripts": {"lint": "eslint .", "test": "npm run -s mocha && npm run -s lint", "build": "rollup -c", "mocha": "mocha", "prepush": "npm test -s", "prebuild": "npm test -s", "prepublishOnly": "npm run -s build"}, "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "_nodeVersion": "8.9.0", "dependencies": {"node-fetch": "1.7.3", "whatwg-fetch": "2.0.3"}, "devDependencies": {"chai": "4.1.2", "nock": "9.0.27", "husky": "0.14.3", "mocha": "4.0.1", "eslint": "4.10.0", "rollup": "0.50.0", "rollup-plugin-copy": "0.2.3", "rollup-plugin-uglify": "2.0.1", "rollup-plugin-node-resolve": "3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch-1.1.1.tgz_1510280219504_0.6528741999063641", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "cross-fetch", "version": "2.0.0", "keywords": ["fetch", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@2.0.0", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "dist": {"shasum": "a17475449561e0f325146cea636a8619efb9b382", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-2.0.0.tgz", "fileCount": 10, "integrity": "sha512-gnx0GnDyW73iDq6DpqceL8i4GGn55PPKDzNwZkopJ3mKPcfJ0BUIXBsnYfJBVw+jFDB+hzIp2ELNRdqoxN6M3w==", "signatures": [{"sig": "MEUCIB2iFSi5xPWLlHKTE7baqH7pIq9yOOHns/o69o08O4deAiEAi82Mn++OTnXgoq5EmDS0ncXep6D9AMo00gEF+y4fv20=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65125}, "main": "dist/node-ponyfill.js", "files": ["dist", "polyfill"], "browser": "dist/browser-ponyfill.js", "gitHead": "df5badff8c6a9497bb5121b187e070ad854a2d9f", "scripts": {"lint": "eslint .", "test": "npm run -s test:node && npm run -s test:browser && npm run -s lint", "build": "rollup -c", "prepush": "npm test -s", "prebuild": "npm test -s", "test:node": "mocha --check-leaks test/node/index.js", "test:browser": "mocha-headless-chrome -f test/browser/index.html -a no-sandbox -a disable-setuid-sandbox", "release:major": "npm run -s build && npm version major && git push --no-verify && npm publish"}, "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "_nodeVersion": "8.9.0", "dependencies": {"node-fetch": "2.0.0", "whatwg-fetch": "2.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "4.1.2", "nock": "9.2.1", "husky": "0.14.3", "mocha": "5.0.1", "sinon": "4.4.2", "eslint": "4.18.1", "rollup": "0.56.3", "rollup-plugin-copy": "0.2.3", "rollup-plugin-uglify": "3.0.0", "mocha-headless-chrome": "1.8.2"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_2.0.0_1519854940741_0.23818672541391628", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "cross-fetch", "version": "2.1.0", "keywords": ["fetch", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@2.1.0", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "dist": {"shasum": "7d4ea7e10a4f3bb73d5c2f0a3791ec3752d39b50", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-2.1.0.tgz", "fileCount": 10, "integrity": "sha512-FTIt2WK44RiafWQ62xIvd+oBoVd392abh1lF872trLlA74JCR1s4oTHlixwoIKy44ehn8WbQ0Ds2P16sw7ZQxg==", "signatures": [{"sig": "MEYCIQCPcKcPZ1iVgyE0wIOexGd2Vu4g77TpAEX5K6suSV8B3QIhAPxVBFoex4WOl7X+aGevV3cUiUgGEZD9qVxvK3KVHZyw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65498}, "main": "dist/node-ponyfill.js", "files": ["dist", "polyfill"], "browser": "dist/browser-ponyfill.js", "gitHead": "cec780e8ff358d9d729f75722f27e1ec53b67ef8", "scripts": {"lint": "eslint .", "test": "run-p -s test:node test:headless lint", "build": "rollup -c", "deploy": "git push --no-verify && git push --tags --no-verify && npm publish", "release": "release-it", "test:ci": "npm test -s && npm run -s test:sauce", "precommit": "lint-staged", "test:node": "mocha --check-leaks test/node/index.js", "test:sauce": "./tasks/sauce", "test:browser": "opn test/browser/index.html", "test:headless": "mocha-headless-chrome -f test/browser/index.html -a no-sandbox -a disable-setuid-sandbox"}, "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "5.7.1", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "_nodeVersion": "8.9.0", "dependencies": {"node-fetch": "2.1.1", "whatwg-fetch": "2.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "4.1.2", "nock": "9.2.3", "husky": "0.14.3", "mocha": "5.0.4", "sinon": "4.4.2", "eslint": "4.18.2", "rollup": "0.56.5", "opn-cli": "3.1.0", "release-it": "7.2.1", "lint-staged": "7.0.0", "npm-run-all": "4.1.2", "rollup-plugin-copy": "0.2.3", "rollup-plugin-uglify": "3.0.0", "mocha-headless-chrome": "1.8.2"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_2.1.0_1520605913751_0.15682475878858582", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "cross-fetch", "version": "2.1.1", "keywords": ["fetch", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@2.1.1", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "nyc": {"temp-directory": "./.reports/.coverage"}, "dist": {"shasum": "c41b37af8e62ca1c6ad0bd519dcd0a16c75b6f1f", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-2.1.1.tgz", "fileCount": 10, "integrity": "sha512-3W94GTFVrSQWw/xHsLpX+z3ArhDKjoh7pJfl4+5sbch0V17ZfPjhZ+gnUdz56t7eoFXI9DhdJtaZTr7jmPL2EQ==", "signatures": [{"sig": "MEUCIHu18L9ndeRdoLNQFs3wWm/WEHbaVxpyya1nEvgbhumMAiEA6+wdimaQVuyYaY4oTfJzV3UbdD35faIbeDcbaypZAFU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67104, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa4hGNCRA9TVsSAnZWagAAnrYP/05ic3oGP6uaGSageInQ\nojNWJCCjEpxFwf2M/Z98Zm9fXgiL0dhpOjifKXL0GcZyUGueAALJIZxp9zp8\nNDDwhoEPGLPRR7H+wKYlGgPCC6UdaHpj0lfDplmZPrlsgP2xmLuRNkSf+BjH\nzvW2xNIMqyV08naz33xQMkdD5Wy7JR62ccZRwsDmT/DSKBevpQ5BW4b2RH4p\nxMFHmMzCTzcI7K3UmjIgCnfr1A6JjjyKNqc84efhjU5SdRgOAiSVnJzb+yfS\nmlVkCyQNXuFYUP0HAw44WYH1MWJ0yg0cfh/PccLPmQgYBWlraGhRcATAC77R\nzb44GjhYO2zshNMG9im4AfmHLYQknpQR8TOF8qW8X46KvojfMJvX2YNA9FEs\n7LVTiaDKUjDqfqGTQv7uO7s+xPMg+nBXbjni9WV2jpHBYDPErggEtUGjg+ar\nqr1rXfx/17vp7jWOSydkkRBZzC8jX40oyEzhHIeJeb6bzikHgI/q9j5sn8sR\nuKkBP6p/fY9qj1qiSPgc0Qkqw0icC8CLzrWGyx0JnH0jQJo32lSXEXfNnGxH\nlmcrkYQr5l72yg8e1Hu54pYK6wf/vXiTiMylnnwy1U0SWNLLgl4hW5w0K2Dc\npp1uj/94vVXrXMbBqLYfZpGsDw8r+TIo5ZZKlDKp95DFgNVIhnZfrNdme4XG\nzp3Z\r\n=5Dt6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/node-ponyfill.js", "files": ["dist", "polyfill"], "browser": "dist/browser-ponyfill.js", "gitHead": "6145587aca077db6c41e1a436e5d55a652f8570f", "scripts": {"lint": "eslint .", "test": "run-p -s test:node test:headless lint", "build": "rollup -c", "deploy": "git push --no-verify && git push --tags --no-verify && npm publish", "codecov": "nyc report --reporter=text-lcov > coverage.lcov && codecov", "release": "release-it", "test:ci": "run-s -s test codecov && npm run -s test:sauce", "precommit": "lint-staged", "test:node": "nyc mocha --check-leaks test/node/index.js", "test:sauce": "./tasks/sauce", "test:browser": "opn test/browser/index.html", "test:headless": "mocha-headless-chrome -f test/browser/index.html -a no-sandbox -a disable-setuid-sandbox"}, "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "5.8.0", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "_nodeVersion": "8.9.0", "dependencies": {"node-fetch": "2.1.2", "whatwg-fetch": "2.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "11.7.1", "ora": "2.0.0", "chai": "4.1.2", "nock": "9.2.5", "husky": "0.14.3", "mocha": "5.1.1", "sinon": "4.5.0", "eslint": "4.19.1", "rollup": "0.58.2", "codecov": "3.0.0", "opn-cli": "3.1.0", "release-it": "7.4.2", "lint-staged": "7.0.5", "npm-run-all": "4.1.2", "rollup-plugin-copy": "0.2.3", "rollup-plugin-uglify": "3.0.0", "mocha-headless-chrome": "2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_2.1.1_1524765068040_0.2708899222153549", "host": "s3://npm-registry-packages"}}, "2.2.0": {"name": "cross-fetch", "version": "2.2.0", "keywords": ["fetch", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@2.2.0", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "dist": {"shasum": "2bcbff94ee51319bcee6699c642c5dda2aa8b6c0", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-2.2.0.tgz", "fileCount": 11, "integrity": "sha512-P0tdN3ZcwhZQsqUiBnyH02mduL2sBIG1lESy+rUALVDXobpSxNzJhzx4cbzRcSsy3FcJ40Ogc9sjIYrrPs3BVg==", "signatures": [{"sig": "MEQCICA0uEg3qcnOm/qv+SA8TdSSGYrnfYbR6sKCmZM2/WI1AiBM2AqoUrLaUFY5VmNvsnymaH49zPuhRHS3mip/Q4EdWg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67637, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa9EVkCRA9TVsSAnZWagAAID0QAJI2oBVzeucyu7ZbD9UM\nuEwbyHbaObbwJBMRFrs5yXT4HESWXqD40bmc5P0j2Gcar4ryqbnAEA9vAj9M\nHHsOgM2SIYm/4Ci3i6NjpA6wzcrrSDMNnjVm/vzkmFNcPiQa2yj0fDVXLmBu\nAda007um2sy+9iR9f7vxFp6Q6P4j6vQsf4wjPJwulQor7HULh2YW15oyr/pn\nwKEKHvd36tfv6X2c+3uXZEVjP2SN4a7fKlDwVQWaDW4sJjyc9AJJbBPmpWkE\n0jB8udtJuK4Q5bNGM00nnof8RBYcxqG7Qvqqa7sRb2NzTboFMBBBkF5mR8Pi\n1iF5iNQK7otDUlvfpuEzHmNIJxVCniU/xuElHChBUd3Wqr1shircaP69/z1d\nfIwGbokqJxaREDtHOwKn5CMJA50lkeRRa3Odd7vdD/kejNNNa+cN6FQj9HAc\ni2Wkb9lujSNs4WSFITfZhwQ5T97CwvYqAevMtQmeZOc0qn60Z4bmu3XsAFDx\nMTzkK3xT8diuJE7wOOb/rConLOqfOKinLXY0N68uUR4Yd14MMeahsv8MAvAl\nYgK6SLa0nlRnzFgUC3Sj44SvokkD/2YMqJk1rXcOSf7Wc1+eWGZqLgBHXk/V\nTUwscpbhz55GRmR9nIuITem4uXneZodtq+TPiNbJFV33EvGUIm7N3h1ndDgc\nyHPR\r\n=GV+X\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/node-ponyfill.js", "files": ["dist", "polyfill", "index.d.ts"], "browser": "dist/browser-ponyfill.js", "gitHead": "4ca0fa35b55f40df5e399fa945e709c71c1862f4", "scripts": {"lint": "eslint .", "test": "npm run -s test:headless && npm run -s test:node && npm run -s lint", "build": "rollup -c", "sauce": "./tasks/sauce", "codecov": "nyc report --reporter=text-lcov > coverage.lcov && codecov", "security": "snyk test", "precommit": "lint-staged", "test:node": "nyc mocha --check-leaks test/node/index.js", "deploy:major": "npm version major && git push --follow-tags", "deploy:minor": "npm version minor && git push --follow-tags", "deploy:patch": "npm version patch && git push --follow-tags", "test:browser": "opn test/browser/index.html", "test:headless": "mocha-headless-chrome -f test/browser/index.html -a no-sandbox -a disable-setuid-sandbox"}, "typings": "index.d.ts", "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "5.8.0", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "_nodeVersion": "8.9.0", "dependencies": {"node-fetch": "2.1.2", "whatwg-fetch": "2.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "11.7.3", "ora": "2.1.0", "chai": "4.1.2", "nock": "9.2.5", "snyk": "1.80.0", "husky": "0.14.3", "mocha": "5.1.1", "sinon": "5.0.7", "eslint": "4.19.1", "rollup": "0.58.2", "codecov": "3.0.2", "opn-cli": "3.1.0", "lint-staged": "7.1.0", "rollup-plugin-copy": "0.2.3", "rollup-plugin-uglify": "3.0.0", "mocha-headless-chrome": "2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_2.2.0_1525957986955_0.5270250186513836", "host": "s3://npm-registry-packages"}}, "2.2.1": {"name": "cross-fetch", "version": "2.2.1", "keywords": ["fetch", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@2.2.1", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "dist": {"shasum": "96c859104d75defc967fb5db62474e75426b02b0", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-2.2.1.tgz", "fileCount": 11, "integrity": "sha512-eONhYwUm5YCMUbmmKPQ1Y01pKeXI9vP2ZafvDEN+vkES9EppbFrn3atl3bPlSLUv2ts78rKy32Ya1nrZSxqQSQ==", "signatures": [{"sig": "MEQCIAbYx5Qi6XsumHfUG93sk8jjEEFWuFQaPrJJxJCFerJLAiAUz3b7v7DLwqeOxdQn8Qexzl/cpg4TATcSJA/WW5xYuw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67831, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbErQmCRA9TVsSAnZWagAAicEQAIGifdninUU5vH1ZrX5D\n+EhJWR7Y/7Pq4PWVIPC9hXBGGzOgzZ0PDSe9HhpOC4RvKknzAdrToDlr85NI\nFrh6mXjSAHmBJ67KgoYb9Pf8Un89ngv+L3CoQxiajq/GtlieoCSiEK7oMxLq\nfsEIyslzN00SGYsWwnSV35PhnbgDr9rUFUwuyQdPwbVw03C68zyVGqmrUvgg\nzAs6U/ZmJuF2WCxhubTwzJPWmXohE2bVCl//0cTwl5/3tVLVgUwX4B1vNruG\nEnLWaJWsIV4/ylWr7YQeRZsycW1Trk7XhEUWCYVKIJvGUCHv01dqs+X6J3Bk\n+bIR0rqPghCM3jTz3t7bq6ymPwaIbqXE/FERVZ8f691Ze537drqyGOvb6ihj\nN9+34yEK7HMRJh1EGJjXhPhsyM3u13EgzCFg0RA2tt/CFbqxVsvDX6pwdpAE\nkU/hj0Z+37bZhSURQNvef33R2uabSoowl5wAO0mmv7SDKj3HF2TAu4/eZQYu\nkN/vsgopnjlYDW6k/1YgZWKbuMCxySTxKCc9QtK03+etmDdAUn5tWVhe4kNj\nPwY2FZRnEEcawaJ6wJqw2b+Z4laDadTkrQExpisWLCVRyAM1lHiiH/hDEVgE\nc/fdZYLX5+ML3yXpgGA7aApPfHZCzvqdBQixnKHuruAmxDNTn1S/XL6ikzwC\nPutj\r\n=SARk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/node-ponyfill.js", "_from": ".", "files": ["dist", "polyfill", "index.d.ts"], "_shasum": "96c859104d75defc967fb5db62474e75426b02b0", "browser": "dist/browser-ponyfill.js", "gitHead": "03694b63551c0011d40786aae1d011cae576845b", "scripts": {"lint": "eslint .", "test": "npm run -s test:headless && npm run -s test:node && npm run -s test:node:bundle && npm run -s lint", "build": "rollup -c", "sauce": "./tasks/sauce", "codecov": "nyc report --reporter=text-lcov > coverage.lcov && codecov", "security": "snyk test", "precommit": "npm run -s build && lint-staged", "test:node": "nyc mocha test/node/index.js", "deploy:major": "npm version major && git push --follow-tags", "deploy:minor": "npm version minor && git push --follow-tags", "deploy:patch": "npm version patch && git push --follow-tags", "test:browser": "opn test/browser/index.html", "test:headless": "mocha-headless-chrome -f test/browser/index.html -a no-sandbox -a disable-setuid-sandbox", "test:node:bundle": "nyc mocha test/webpack-node/bundle.js", "pretest:node:bundle": "webpack-cli --config test/webpack-node/webpack.config.js"}, "typings": "index.d.ts", "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "_nodeVersion": "6.14.2", "dependencies": {"node-fetch": "2.1.2", "whatwg-fetch": "2.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "12.0.1", "ora": "2.1.0", "chai": "4.1.2", "nock": "9.3.0", "snyk": "1.82.0", "husky": "0.14.3", "mocha": "5.2.0", "sinon": "5.0.10", "eslint": "4.19.1", "rollup": "0.59.4", "codecov": "3.0.2", "opn-cli": "3.1.0", "webpack": "4.10.2", "lint-staged": "7.1.3", "webpack-cli": "3.0.0", "rollup-plugin-copy": "0.2.3", "rollup-plugin-uglify": "4.0.0", "mocha-headless-chrome": "2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_2.2.1_1527952420281_0.25210631365224345", "host": "s3://npm-registry-packages"}}, "2.2.2": {"name": "cross-fetch", "version": "2.2.2", "keywords": ["fetch", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@2.2.2", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "dist": {"shasum": "a47ff4f7fc712daba8f6a695a11c948440d45723", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-2.2.2.tgz", "fileCount": 11, "integrity": "sha512-sMF1jhA9GQ6PlBhneCxtCToyK7vHEEbmk+3DgZPjJaqsHSgsb69ajLMSOSmW5OLTDhZ0w+Fe8u7PQofZhJwqzQ==", "signatures": [{"sig": "MEQCIHzbyN+zyiG2O7bSP1qzEjWm6Q9sGFz+8Fvx6523aoriAiAgxVes0EMC5DC6JIAxK+dpjHawucT1UaU1pJMHraiNeQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67800}, "main": "dist/node-ponyfill.js", "_from": ".", "files": ["dist", "polyfill", "index.d.ts"], "_shasum": "a47ff4f7fc712daba8f6a695a11c948440d45723", "browser": "dist/browser-ponyfill.js", "gitHead": "2cd84213a796eb10a605ae7723b6ee0499bbc6c3", "scripts": {"lint": "eslint .", "test": "npm run -s test:headless && npm run -s test:node && npm run -s test:node:bundle && npm run -s lint", "build": "rollup -c", "sauce": "./tasks/sauce", "codecov": "nyc report --reporter=text-lcov > coverage.lcov && codecov", "security": "snyk test", "precommit": "npm run -s build && lint-staged", "test:node": "nyc mocha test/node/index.js", "deploy:major": "npm version major && git push --follow-tags", "deploy:minor": "npm version minor && git push --follow-tags", "deploy:patch": "npm version patch && git push --follow-tags", "test:browser": "opn test/browser/index.html", "test:headless": "mocha-headless-chrome -f test/browser/index.html -a no-sandbox -a disable-setuid-sandbox", "test:node:bundle": "nyc mocha test/webpack-node/bundle.js", "pretest:node:bundle": "webpack-cli --config test/webpack-node/webpack.config.js"}, "typings": "index.d.ts", "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "_nodeVersion": "6.14.3", "dependencies": {"node-fetch": "2.1.2", "whatwg-fetch": "2.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "12.0.2", "ora": "2.1.0", "chai": "4.1.2", "nock": "9.3.3", "snyk": "1.83.0", "husky": "0.14.3", "mocha": "5.2.0", "sinon": "6.0.0", "eslint": "4.19.1", "rollup": "0.60.7", "codecov": "3.0.2", "opn-cli": "3.1.0", "webpack": "4.12.0", "lint-staged": "7.2.0", "webpack-cli": "3.0.7", "rollup-plugin-copy": "0.2.3", "rollup-plugin-uglify": "4.0.0", "mocha-headless-chrome": "2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_2.2.2_1530274588096_0.5366373550944468", "host": "s3://npm-registry-packages"}}, "2.2.3": {"name": "cross-fetch", "version": "2.2.3", "keywords": ["fetch", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@2.2.3", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "nyc": {"temp-dir": ".reports/.coverage"}, "dist": {"shasum": "e8a0b3c54598136e037f8650f8e823ccdfac198e", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-2.2.3.tgz", "fileCount": 11, "integrity": "sha512-PrWWNH3yL2NYIb/7WF/5vFG3DCQiXDOVf8k3ijatbrtnwNuhMWLC7YF7uqf53tbTFDzHIUD8oITw4Bxt8ST3Nw==", "signatures": [{"sig": "MEUCIQCme/bMgp5/j8VpLEquiAVlH94oYE7QfiO81iv7RfGqqQIgbwODPlNqs/iBBCV5CZPd8PBtVoY/MBpRa1Zu/XWiPSQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68085, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb1Ld7CRA9TVsSAnZWagAACEUP/1uhhj/NAsvgQ6Fu78gI\npyldOOhquI+pfKVqOgyAKw8b3jqEPeG28p8g3Ngmz2vrCz0QJStiQ4ZKSn83\nfXZNpVyFNDxCHI27JQW7zlPApNV2ssff1AhamRtYINNmB2o3chy810k37gmu\naOsl8QZHCBlPr8K4cek/8tmoYcTpuJcg90thwbT/SG75xh2FpWDsetwpiJq9\n568d4hgb4DOPWRrB7RKlNshHnPeZkavbPAwsWjcRXsNfmV9Wbqr1nE5u47ej\nw8xKdImmu5P4SZQViee0GZqJdHcEQUhxRv1/VPd6ycFaMFfd38T954czLxpw\nIrjctt3eAs+clPOWnDRePdT23gad/ldFp1yOW4SjCIvh1Skmriv7y5lm6vjK\nIYRlLpoWY/i/vQgOOTmSfK2/jBqKbzsLf9tWn2txt/+YqAx+P0K0QVTSk0Ub\nspjtfxVs42QEjwpvsez52bYha9PKlRYtSA+b+6Xlx4hOk0Qst3Zf1LAhswDr\nsnCeQ1iKYjBkH89pCBVq3XK0aN8R6kkM05nF0lIt+QrWBUVBBVbTUbO+3V/G\n5A3ubViHNaJT2tF75ElLR3+knMDiKSpywRV/5ODrhK4tBPLMjd98oJVNGD1j\nwXDwBLjRdtfE+kRs6+6+IAKIsMv3rM1pSQZxjKHR0p2vgWdOO/BeLA4NIwFw\n9QyS\r\n=CuVP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/node-ponyfill.js", "husky": {"hooks": {"pre-commit": "npm run -s build && lint-staged"}}, "browser": "dist/browser-ponyfill.js", "gitHead": "bb10f0177c0f5b3c1f0e8898cdb67284d1e4f243", "scripts": {"lint": "standard", "test": "npm run -s test:browser:headless && npm run -s test:node:plain && npm run -s test:node:bundle && npm run -s lint", "build": "rollup -c", "sauce": "./tasks/sauce", "codecov": "nyc report --reporter=text-lcov > coverage.lcov && codecov", "security": "snyk test", "deploy:major": "npm version major && git push --follow-tags", "deploy:minor": "npm version minor && git push --follow-tags", "deploy:patch": "npm version patch && git push --follow-tags", "test:node:plain": "nyc mocha test/node-plain/index.js", "test:node:bundle": "nyc mocha test/node-bundle/index.js", "test:browser:plain": "opn test/browser/index.html", "pretest:node:bundle": "webpack-cli --progress --config test/node-bundle/webpack.config.js", "test:browser:headless": "mocha-headless-chrome -f test/browser-headless/index.html -a no-sandbox -a disable-setuid-sandbox"}, "typings": "index.d.ts", "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "standard": {"env": ["mocha", "browser"], "ignore": ["/dist/", "test/node-bundle/index.js"], "globals": ["expect", "chai", "sinon"]}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "lint-staged": {"*.js": ["standard --fix", "git add"]}, "_nodeVersion": "8.12.0", "dependencies": {"node-fetch": "2.1.2", "whatwg-fetch": "2.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "13.1.0", "ora": "3.0.0", "chai": "4.2.0", "nock": "10.0.1", "snyk": "1.105.0", "husky": "1.1.2", "mocha": "5.2.0", "sinon": "7.0.0", "rollup": "0.66.6", "codecov": "3.1.0", "opn-cli": "3.1.0", "webpack": "4.23.0", "standard": "12.0.1", "lint-staged": "7.3.0", "webpack-cli": "3.1.2", "rollup-plugin-copy": "0.2.3", "rollup-plugin-uglify": "6.0.0", "mocha-headless-chrome": "2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_2.2.3_1540667259108_0.9624378613369793", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "cross-fetch", "version": "3.0.0", "keywords": ["fetch", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@3.0.0", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "nyc": {"temp-dir": ".reports/.coverage"}, "dist": {"shasum": "fb7fa6906f39c7233aa7ab8de1785e980532b899", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-3.0.0.tgz", "fileCount": 11, "integrity": "sha512-P8HyKlMwT1ed9LqEWlJu+zfcxfn0KI4Nl4nxyvu1a8sg4vgtHdwhElZOgSNzoar44zQMdliZcve4QG/04AUi9Q==", "signatures": [{"sig": "MEYCIQDcR5AwQJ7VdhkUxwnkOYUxXJPWWO05men7nWXCik5CgAIhAO4Q9D40aYz20qwCWOiavcO4Pn+eIfW1+bcpjWzl7nm3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71485, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcDHF7CRA9TVsSAnZWagAAe14P/jDnsRF+63CF139N6m9V\n+rpIzeUFEk04TTtvraobVywxZOcCttu+FgCJnl0mIopc5v6kC4ruAqBgTF8L\nFum02zaBD0TWeXCElJC7tJCMxJaBTFfjkH7BjZtN+7YR1D6FP1P0tyvpGNSz\nWUiuuxmurO1va2KGAuW8ErxjU4+bh8uua9aYjYoW2U5essLUjXclgN8Ix0SY\n9GXurktRi/9ZPgUdhUZHNqqQPUgAdRnyAjDirWw6bcii4fbH6L+geCrNrFEn\nsNs0AqMVm2Op3nfvLyL0f3YN/qQ9e0K+wBQik6GiMLqV5vTmbFbN+bBFJ0VP\n3BZlQevDk1k0ryIblT7VguA+0WLnxwDtjXXn+SnO6HjoPgFeO7IWGPJBEw4j\nRuUx63UsvqEz1yBiXO1t2CckrJtEAKD2Q8hyHPvowegiNiZIQ6A6h1fYhC/f\nCnBeNiT8+7ZxgcxZVJyAKbt6M9beR9RmsrjHLAJUoLMI/SulWRiGdHvqwWL7\nINve/LAcy/XUKniUGtArkdvZtA3GB8PlmFtNbGeImM7Q4U3qykDa9IzyHHKn\n909ZVuhXrP7q8wNID3BkXTWei9z7ObtDnEm+DIRhr2+oWgZAfw7gGAbvTZ26\n2Kg9pb2/G7aOUi6JfdHnPorAs9qJ492tZb8WJJJn8acz+QwsicLcaDWi3ckj\nYWF/\r\n=u4oI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/node-ponyfill.js", "husky": {"hooks": {"pre-commit": "make build && lint-staged"}}, "browser": "dist/browser-ponyfill.js", "gitHead": "2d7beded9bb57d8498fb4cb30e7d67a38af5820d", "typings": "index.d.ts", "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "standard": {"env": ["mocha", "browser"], "ignore": ["/dist/", "bundle.*.js"], "globals": ["expect", "assert", "chai", "sinon"]}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "lint-staged": {"*.js": ["standard --fix", "git add"]}, "_nodeVersion": "8.12.0", "dependencies": {"node-fetch": "2.3.0", "whatwg-fetch": "3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "13.1.0", "ora": "3.0.0", "chai": "4.2.0", "nock": "10.0.4", "husky": "1.2.0", "mocha": "5.2.0", "sinon": "7.1.1", "rollup": "0.67.4", "codecov": "3.1.0", "webpack": "4.27.1", "standard": "12.0.1", "lint-staged": "8.1.0", "webpack-cli": "3.1.2", "rollup-plugin-copy": "0.2.3", "rollup-plugin-uglify": "6.0.0", "mocha-headless-chrome": "2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_3.0.0_1544319354907_0.8281494384093122", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "cross-fetch", "version": "3.0.1", "keywords": ["fetch", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@3.0.1", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "nyc": {"temp-dir": ".reports/.coverage"}, "dist": {"shasum": "3f207bbd829a76e9aa2a953348bd1f2a3cf388a7", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-3.0.1.tgz", "fileCount": 13, "integrity": "sha512-qWtpgBAF8ioqBOddRD+pHhrdzm/UWOArkrlIU7c08DlNbOxo5GfUbSY2vr90ZypWf0raW+HNN1F38pi5CEOjiQ==", "signatures": [{"sig": "MEQCIBzEBGnh8mSo7LCWbsCFsHQpNIaM7tEzfAptSbaG5RA3AiB5vb8MmNHF4q87P9MWkZnJmJK3mc5ui0fc3Rjdz1I2Hw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72117, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcVfTGCRA9TVsSAnZWagAAqi8P/iTpDJCQcR9058WhHSF4\no/8RU7z8YN5OJLY9xxLPgOq6GW+UDmiTTF9gI6+HfDS+20804G4CDT5JQ2dC\nUajTRbaUxqbTph4jolRqjCfST7TY/6zPOhXGS1s46bUzw5BGdubLMpUOy7no\nja9M9quh+WapPcZmu8NCkCzHWEb0OMRLpDLOtJpx7D/8W5t7+xi3E+JKHrDF\nexnBIZkqS8Fx1kZWuxkGAuH2erg1ovnqAT/6RENKWRGRqvZvB2U7ap4D7Bz2\nDtzTOzjJheXDmUX/HUbahJunpFJaCEPFCJP3mcNvzb0Vxber9x1KPa1+fBeP\nxRemMtBQd+CsgVkwstBbFz9AUf2wjV5XCfmgxKLrGfPMd4bEIkJgHuuagemA\naH9B3iMgtU+ooXE0CTv/RfpzPh6Ti/SDpCn4v7OXm4NRW9q9Y1uPQLjVVCwB\n5CoSu7TYav5qpgxgtR4hiCTe6X1y8Fspt50T2J4LWS7uoSP6J1/2JXuuPT1S\nSVuFp4KHk5Tn4lPVFBeM4vo4dxTWMOEKD4YPmyIcqmmiw9XbwfDueTT40OEx\n8yrhkh0zTN/3V8ToHZkQo1zWYKd25QP8Sq4CYJM0bwIPHNtA5tsroyTbAoIo\n8mlQ9OAC7viIrH4YwKJlR37wvDY5yryDoolC/xOvdj+5d9Nyuel1ZCmOnSah\nWO5T\r\n=xy4x\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/node-ponyfill.js", "husky": {"hooks": {"pre-commit": "make build && lint-staged"}}, "browser": "dist/browser-ponyfill.js", "gitHead": "35dd931623ffa3fcec5eac72801c914b4c1a2ad4", "typings": "index.d.ts", "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "standard": {"env": ["mocha", "browser"], "ignore": ["/dist/", "bundle.*.js"], "globals": ["expect", "assert", "chai", "sinon"]}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "lint-staged": {"*.js": ["standard --fix", "git add"]}, "_nodeVersion": "8.12.0", "dependencies": {"node-fetch": "2.3.0", "whatwg-fetch": "3.0.0"}, "react-native": "dist/react-native-ponyfill.js", "_hasShrinkwrap": false, "devDependencies": {"nyc": "13.1.0", "ora": "3.0.0", "chai": "4.2.0", "nock": "10.0.6", "husky": "1.3.1", "mocha": "5.2.0", "sinon": "7.2.3", "rollup": "1.1.2", "codecov": "3.1.0", "webpack": "4.29.0", "standard": "12.0.1", "lint-staged": "8.1.1", "webpack-cli": "3.2.1", "rollup-plugin-copy": "0.2.3", "rollup-plugin-uglify": "6.0.2", "mocha-headless-chrome": "2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_3.0.1_1549137091721_0.095634416700348", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "cross-fetch", "version": "3.0.2", "keywords": ["fetch", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@3.0.2", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "nyc": {"temp-dir": ".reports/.coverage"}, "dist": {"shasum": "b7136491967031949c7f86b15903aef4fa3f1768", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-3.0.2.tgz", "fileCount": 13, "integrity": "sha512-a4Z0EJ5Nck6QtMy9ZqloLfpvu2uMV3sBfMCR+CgSBCZc6z5KR4bfEiD3dkepH8iZgJMXQpTqf8FjMmvu/GMFkg==", "signatures": [{"sig": "MEUCICXEw3FhdVbH2GIl3JyMgKeC0dwag+T/ON0NzgFsXJRRAiEAr7zj7HSXK7BnwJcdgaXERLkMc6AII/LIqKGffMHE8wk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72193, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcmvaJCRA9TVsSAnZWagAAJ8QQAIkVOSiNAsOStCeWNdP6\ncmTpYfIEqI9gjPdFCic5M0RtW1XqDhFgNAfraLcB9n6Dle0r0c0VJ2YRp5fY\nYoGU2JTnl0D4C7J7d28bQgkI2iDMHRRfSL1oahyV24BGETQuFVCaya5kAZOD\nlVGgzEaEyWkA3DS5Y+56MzcGXrsLmGxX/DJqyL+24D+Z2hKA1RxkSZK9eiRV\nyXTNEE5BDi3RUPZLBk8IGAsoAOhmmEndJ2/WnEhHarsht7ShwV7e/vp7GJO6\nK60DgmH+LdlGsuvtMd757ivdJdayJifNGn3o89U/+tvjmezBsKoIyl5/nivA\ne0aDNENqfSYwMcps5U1v0XlLCsPtztqMBP544l5HF5S4ZnVdyhbSez/PDw7P\nrhUxxniBbWr1Bxu0ok7noW/wNyYLyqY9iH5tNNwjnAR83i7yg5JI9b9ytE+q\nOmoPP4sqAA6HRST7o2Sm849la51rgFAQH+gCyOQ5MGAGVyycR3jYzxjlmIb4\n3aeUp4vyDwrSiL9s8pFK914pUBFH+bJumLgnFBQaLmqa/m427BNp7kVVW5KT\nlIxYj7CW/+EWjq7HfCbNPG734BLvgd4LUjHPB5tAY+9/xx+14CVKPttFI4Rz\nOmIXIfX0EV4tMwLeIvTqT8wfEs2VVCK1Z7UYPLO4ll696NmDdkzhLGgqFH7R\nAboV\r\n=SVvz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/node-ponyfill.js", "husky": {"hooks": {"pre-commit": "make build && lint-staged"}}, "browser": "dist/browser-ponyfill.js", "gitHead": "d009200978d1a44b56d34c61fd9656c7f485127d", "typings": "index.d.ts", "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "standard": {"env": ["mocha", "browser"], "ignore": ["/dist/", "bundle.*.js"], "globals": ["expect", "assert", "chai", "sinon"]}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "lint-staged": {"*.js": ["standard --fix", "git add"]}, "_nodeVersion": "8.15.1", "dependencies": {"node-fetch": "2.3.0", "whatwg-fetch": "3.0.0"}, "react-native": "dist/react-native-ponyfill.js", "_hasShrinkwrap": false, "devDependencies": {"nyc": "13.3.0", "ora": "3.2.0", "chai": "4.2.0", "nock": "10.0.6", "husky": "1.3.1", "mocha": "6.0.2", "sinon": "7.3.0", "rollup": "1.7.3", "codecov": "3.2.0", "webpack": "4.29.6", "standard": "12.0.1", "lint-staged": "8.1.5", "webpack-cli": "3.3.0", "rollup-plugin-copy": "0.2.3", "rollup-plugin-uglify": "6.0.2", "mocha-headless-chrome": "2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_3.0.2_1553659529040_0.6222327464986093", "host": "s3://npm-registry-packages"}}, "3.0.3": {"name": "cross-fetch", "version": "3.0.3", "keywords": ["fetch", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@3.0.3", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "nyc": {"temp-dir": ".reports/.coverage"}, "dist": {"shasum": "b988beab275939cb77ad4e362c873f7fa8a221a5", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-3.0.3.tgz", "fileCount": 13, "integrity": "sha512-mplYkc4CopHu+AdHK8wxjJcPskUxp5CvPTdtDij3MUgVNBa0xOb9CQqbbn1zO23qISM4WLxIBociyEVQL7WQAg==", "signatures": [{"sig": "MEQCIFz9SkBqtWowiB37aSEJwa7VK/5dyAqkYEwQk6JUBhm3AiAYCUihsEOstz9hXKzb61wEQWwCssKUdG5mpDK7JTAzIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72275, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc6KeQCRA9TVsSAnZWagAARMAP/2/qjFheM6QyGeCRJySL\nfh2AaX8worjgOrEv0ZgNPo3pYejxq9cF+8oJVAAB13BhfO6AkSaap/F0CffV\nFjk8bm3VtjjFSFWsJyJX/X3INBKGxjRQuwglWeY67BZ0lZy9VN/WTf2/yKGj\nbZXIP1koYbgJxC8Nps/OgBUmLGzalkkcugDLwpr6mGeeZjr1VZ4guaCYxYqx\ndjURJfEqikLQGS2A+j5UXq2Vqe7TII1mz+bIuP4pLaB7dTm6Uw02jGoogwvG\n2p8vIYbSVIeVESDS+vuYH5zc6tJajMtXgebfvp9jKYpPk+xACJAtkE/hpAVb\nDZQrNiC7YRdR63gT+ROkIo9EwTSiwmsROYZRDObEJlbn2ejho9/1AJ7RGsdR\n0ewYq3asT+AyUMNEnkyMq5PXYaG5YkdNURuN+/Cd9gAIrwjFZ5NP0UgRd/BO\nM/QotGIwYPJELPohxEY/qGyWX97byapXE5Q8JNvHUjW+Z5rxLm5pcQ8ExcPg\ngcrCP6K1Fm7Q80kj8fO8H0rDZ31MfahWb2NeHNrLs5H32OitPANhsbyeh7Eu\nS8QXgh2ng3y5Ib/OEO7EuqgI2JEp65M2M2ARiXDKRZyI5EiOWEPyD12gv1b2\nU8QFgMEzoisKxiTr/HKDE1SX1Q9cz+zEFaLMpE0KNPB7N533I46jZ6vNDSA9\nlsvk\r\n=eMcm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/node-ponyfill.js", "husky": {"hooks": {"pre-commit": "make build && make check-dependencies && lint-staged"}}, "browser": "dist/browser-ponyfill.js", "gitHead": "0148f82475d673234fc3eae65bba16cc35f3433f", "typings": "index.d.ts", "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "standard": {"env": ["mocha", "browser"], "ignore": ["/dist/", "bundle.*.js"], "globals": ["expect", "assert", "chai", "sinon"]}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "lint-staged": {"*.js": ["standard --fix", "git add"]}, "_nodeVersion": "8.15.1", "dependencies": {"node-fetch": "2.6.0", "whatwg-fetch": "3.0.0"}, "react-native": "dist/react-native-ponyfill.js", "_hasShrinkwrap": false, "devDependencies": {"nyc": "14.1.1", "ora": "3.4.0", "chai": "4.2.0", "nock": "10.0.6", "husky": "2.3.0", "mocha": "6.1.4", "sinon": "7.3.2", "rollup": "1.12.3", "semver": "6.1.0", "codecov": "3.5.0", "webpack": "4.32.2", "standard": "12.0.1", "lint-staged": "8.1.7", "webpack-cli": "3.3.2", "rollup-plugin-copy": "2.0.1", "rollup-plugin-uglify": "6.0.2", "mocha-headless-chrome": "2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_3.0.3_1558751120019_0.8755574936264237", "host": "s3://npm-registry-packages"}}, "3.0.4": {"name": "cross-fetch", "version": "3.0.4", "keywords": ["fetch", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@3.0.4", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "nyc": {"temp-dir": ".reports/.coverage"}, "dist": {"shasum": "7bef7020207e684a7638ef5f2f698e24d9eb283c", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-3.0.4.tgz", "fileCount": 13, "integrity": "sha512-MSHgpjQqgbT/94D4CyADeNoYh52zMkCX4pcJvPP5WqPsLFMKjr2TCMg381ox5qI0ii2dPwaLx/00477knXqXVw==", "signatures": [{"sig": "MEYCIQD9e88AQDGrFUHkBZseZdkD2tkKM/Wi4cLpwKEi+hK1XQIhAKSMZeg4mCmJ/B0fyYMGqwdRuWdie6gm1SG3m58GUPdR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72736, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc+xi/CRA9TVsSAnZWagAAeP8P/39k0TddvRL5Y0hLeMMB\nO6Yc2Hf+xKFaSe1HF8Dl1vZeqXJYRx1IK1Z06H4E4rCFtvnw6XBykXMzFFwd\n5jMx6YR1ubU94yEEk62emAa10IXFCwTrc40YlNANO+f3Kc0JGowLoOGCnwOk\nYfcKcYRViojQges6bqc6Lh8Ei0AmlACf3GTd1FkYeG2DeYMS8r/rLIS+Hut4\nyAMZpYBuKKKkIfBfX7JnpnwBqZSajGEML9XSSGOQC2DPLAPvaPv9/BNxspzV\n30fpysEcP9IHT1+dlAJJ8MhmqCBSd57abNqIfhzqjngUjGTEURh2SteE4ji+\nEfJ+t+V4m/ztKS1XcuSVs8pJ0NWopeqkhXMX7fnLfGu3p3ztq1hMR2VWjtvK\nzSYAmEM5Lq4ByZ2oIu8drkc3vbCinGkAKm73po/zZ6Rm0Bpqyf17s6zTjfcI\nw11FX9QOprPgKMtFjL1KnKMbR0X9OBFjBzFB4dFNCy2qcTuGmM+T/R4jPFbt\nG7PQtt/W/3Y6/6/kGzAPRi2X69EZTOSPzlUipZbZBW84kffca15QLoo1M54c\nx7eZCqqozfDXuhESQzvTxG1sNmAgvcZx1/gyuJYChcL2Mf+Uaaaj6OFGo9x+\nd80xIrMnQi8EeRWcr6PrKAXh9U/zk1WAHX3+hIbUmx5xTPk6gfrUFeFrIFPs\nVd+l\r\n=palQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/node-ponyfill.js", "husky": {"hooks": {"pre-commit": "make build && make check-dependencies && lint-staged"}}, "browser": "dist/browser-ponyfill.js", "gitHead": "33a2a86087760920035f75766ce26bdee26d356c", "typings": "index.d.ts", "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "standard": {"env": ["mocha", "browser"], "ignore": ["/dist/", "bundle.*.js"], "globals": ["expect", "assert", "chai", "sinon"]}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "lint-staged": {"*.js": ["standard --fix", "git add"]}, "_nodeVersion": "8.15.1", "dependencies": {"node-fetch": "2.6.0", "whatwg-fetch": "3.0.0"}, "react-native": "dist/react-native-ponyfill.js", "_hasShrinkwrap": false, "devDependencies": {"nyc": "14.1.1", "ora": "3.4.0", "chai": "4.2.0", "nock": "10.0.6", "husky": "2.3.0", "mocha": "6.1.4", "sinon": "7.3.2", "rollup": "1.12.3", "semver": "6.1.0", "codecov": "3.5.0", "webpack": "4.32.2", "standard": "12.0.1", "lint-staged": "8.1.7", "webpack-cli": "3.3.2", "rollup-plugin-copy": "2.0.1", "rollup-plugin-uglify": "6.0.2", "mocha-headless-chrome": "2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_3.0.4_1559959742907_0.8136850094593382", "host": "s3://npm-registry-packages"}}, "3.0.5": {"name": "cross-fetch", "version": "3.0.5", "keywords": ["fetch", "http", "url", "promise", "async", "await", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@3.0.5", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "nyc": {"temp-dir": ".reports/.coverage"}, "dist": {"shasum": "2739d2981892e7ab488a7ad03b92df2816e03f4c", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-3.0.5.tgz", "fileCount": 13, "integrity": "sha512-FFLcLtraisj5eteosnX1gf01qYDCOc4fDy0+euOt8Kn9YBY2NtXL/pCoYPavw24NIQkQqm5ZOLsGD5Zzj0gyew==", "signatures": [{"sig": "MEUCIElwCt5bTizhmq6LaiopkP/1nQSkBkbHDA0krVaS8U23AiEAsNd7xT2R7JbJ8MG9sli+tWrbBqtXteHIxGYmrxvvPJo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73067, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe5rDICRA9TVsSAnZWagAA9u4P/1NCP6w07mszsZL5TPKk\nXlkd1JD89elocqNGiys2moK1WBXklCvu6Fufw4WY9xChpAVdPgcU5g4on51Z\nkwhPcheGHgJj47E9h/cGZeF8FU3Mw2Y5Mb16UuxM7tYkX9xPUeRHHaPzidYL\nnrmqlUOvLg/uWZzZlXT+ANl8guqe5jyowhP0mP2NlzVi0bI5jv8D5K+/jzou\nYy2x0lQbG+qXAex1d2+3TosHSbz9ktQLXeVjSIdVc+8lZvda3Sxi9PKd2TkX\nA5Y06ezLv5l03HtJPZif3T103jlz3vymGSKnT0N+b8mCmAHkDcqgI7cQcgkF\nXeUKWf3RonEtsrGZIQEYVLAmeozEuyPOkYShfZRCZxYhjv95jCOoTwjDbkkG\nka7u8rrX+c57WYBRoA10wIo/V+CgNF1+zPik4qUfK9R+1Kzs8Ggrd9IXrc+g\nVa9GHVCRcPH0D0yybUX+k1UFJok9fYtB4Q8ZxIxRNgOHxTGNH4eqeaiFf9Mp\nQHUHbJqgBZI4QJ0V7zIS9EeKfq4+qBr3kSagrJkU1A3ZBv5b4u5K9B3d126I\n7CuhjZXyNPAyv9Bg2AXXsBu5DjsRQXnb7P/7nXzKWwChWCJy20QN4KfihuKF\nKYuTLtmwmvKw0/PPUA6faoREZwHkz/xmkYgLOV4tsGeIkLwUjG1/BhS4GJJN\nYdkT\r\n=+w4R\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/node-ponyfill.js", "husky": {"hooks": {"pre-commit": "make build && make check-dependencies && lint-staged"}}, "mocha": {"require": ["chai/register-expect", "chai/register-assert"], "check-leaks": true}, "browser": "dist/browser-ponyfill.js", "gitHead": "c1354d3b3fa3d1eb344ec0991e4f766f757074e0", "typings": "index.d.ts", "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "standard": {"env": ["mocha", "browser"], "ignore": ["/dist/", "bundle.js", "bundle.*.js"], "globals": ["expect", "assert", "chai", "sinon"]}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "lint-staged": {"*.js": ["standard --fix"]}, "_nodeVersion": "10.19.0", "dependencies": {"node-fetch": "2.6.0"}, "react-native": "dist/react-native-ponyfill.js", "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "ora": "4.0.4", "chai": "4.2.0", "nock": "12.0.3", "husky": "4.2.5", "mocha": "8.0.1", "sinon": "9.0.2", "rollup": "2.16.1", "semver": "7.3.2", "codecov": "3.7.0", "webpack": "4.43.0", "standard": "14.3.4", "lint-staged": "10.2.10", "webpack-cli": "3.3.11", "whatwg-fetch": "3.0.0", "rollup-plugin-copy": "3.3.0", "rollup-plugin-uglify": "6.0.4", "mocha-headless-chrome": "2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_3.0.5_1592176839984_0.6287885532169284", "host": "s3://npm-registry-packages"}}, "3.0.6": {"name": "cross-fetch", "version": "3.0.6", "keywords": ["fetch", "http", "url", "promise", "async", "await", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@3.0.6", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "nyc": {"temp-dir": ".reports/.coverage"}, "dist": {"shasum": "3a4040bc8941e653e0e9cf17f29ebcd177d3365c", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-3.0.6.tgz", "fileCount": 13, "integrity": "sha512-KBPUbqgFjzWlVcURG+Svp9TlhA5uliYtiNx/0r8nv0pdypeQCRJ9IaSIc3q/x3q8t3F75cHuwxVql1HFGHCNJQ==", "signatures": [{"sig": "MEUCIAxfbj6SoCqaAsvjAv0uoxPBjV9Qsr7XXbzSvxjiSe0fAiEAmGFDX6vM9w/kfyhjHX7WeOuU30xXwcrBxpHgwA3SkwU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73064, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfWt9eCRA9TVsSAnZWagAAT70P/16Evx2IjFyp1/Z4u06B\n66L/zIK84pMCgz2Vxr1fdFLLhHYFcT+jAVTCThP7CGswB2inGDFIbxtoS/tq\ns/MUYNN5ecoQVm5eoLE0ZsHeD/ubGuwMNkGfh1PZpmYFq96CZhAF/la0ZOG+\nyceCyglP90enzfx0dUNWgEC/0I4nFo4tKMU8/ebacImoJVd9FOdkMvilLLFe\ntfAVVccDoPuqgwmT+ipIdbcHF4IpDxQL/nGMVgGkldrc3C/E1CnuzR1lSnN6\nZJkPu0UGwOBNIKcu//R0o5BIj0xf5CD6U1eL53wJEaaF8216pL4Yp+xzerIc\nWEF3DHY0sIn2hI5cEIQcgdidjghsuVaw792HjTZWIcFZGWHGwHYbuFBna/SB\n2gisFrQ+z9IKBLSGI5bmFfUd59K+HLCtLWGkni2uduTpzJfCpA7fJIMkjrCM\n879zLYTFCtZvZ+GeveSoLktObotG8ScTsopdpNbs329pgwcQpnetiEIzPsY0\n2pAt/QS6HWQxc+uR1sRBWTr9gIciQSH481BD9rkhAn9265fmRA5aZspldjb7\nbsR1EUyIKOPc2gLdizDFjZlHAFMTpB6apzlWuqFavuie7Zi76OIqGJLV41RU\nw7aNmyhhDLBYPkTit0L6V7MexPIjVX6YXgjUm/VsxKQg8ol0ZcDryo1gupn2\nCdbc\r\n=j79F\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/node-ponyfill.js", "husky": {"hooks": {"pre-commit": "make build && make check-dependencies && lint-staged"}}, "mocha": {"require": ["chai/register-expect", "chai/register-assert"], "check-leaks": true}, "browser": "dist/browser-ponyfill.js", "gitHead": "14afdc109138529af3917fb5b340343eda32560c", "typings": "index.d.ts", "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "standard": {"env": ["mocha", "browser"], "ignore": ["/dist/", "bundle.js", "bundle.*.js"], "globals": ["expect", "assert", "chai", "sinon"]}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "lint-staged": {"*.js": ["standard --fix"]}, "_nodeVersion": "10.19.0", "dependencies": {"node-fetch": "2.6.1"}, "react-native": "dist/react-native-ponyfill.js", "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "ora": "5.1.0", "chai": "4.2.0", "nock": "13.0.4", "husky": "4.3.0", "mocha": "8.1.3", "sinon": "9.0.3", "rollup": "2.26.11", "semver": "7.3.2", "codecov": "3.7.2", "webpack": "4.44.1", "standard": "14.3.4", "lint-staged": "10.3.0", "webpack-cli": "3.3.12", "whatwg-fetch": "3.0.0", "rollup-plugin-copy": "3.3.0", "rollup-plugin-uglify": "6.0.4", "mocha-headless-chrome": "2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_3.0.6_1599790942254_0.21034185227407676", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "cross-fetch", "version": "3.1.0", "keywords": ["fetch", "http", "url", "promise", "async", "await", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@3.1.0", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "nyc": {"temp-dir": ".reports/.coverage"}, "dist": {"shasum": "6447c13cc8887fa2a66caef92888d7fdaab6e0d1", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-3.1.0.tgz", "fileCount": 14, "integrity": "sha512-a+yso9lSpXQI9DH+YjAu/m0dVfP8IVoZDPBLLFcvGpeq3KHNdikkekTOdkHiXEuTq4GBOeO0MfWkE40yzF1w7g==", "signatures": [{"sig": "MEQCIFDG4L80UXjyNqp25eniFuJJrlDfx+J2kiqiToER6ErwAiAsYTWOFJih7sHHLpoe+vZ9yLnghhgNzZyQZBHnwsiYKg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 95319, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgTEaXCRA9TVsSAnZWagAAyvcP/1x4yZOZSe1hHhuhOLnO\nXYyaQ6cG/JadcfS/Wbtoh05KhQcBR2eZdBAuD3bm7nQwgim4LKqK+mwVH4gt\nE3kS6f6GMoxmoag+SDct4X3XZPlZ3IT+jAccKiaPd6LDYhMaxWkLB8A2bZEm\nriYsB4gUZmNYyDDJfCXkGzdVbdJlbtNf87+unQMsKh5RfyasAmMS7iBggWIA\nDz/BYivxIrINmyQ6SUPAnli7++dA1TIlbwA4RLQHLSYMN/DDbIgM1U8JUO+Z\nSWZ8QjYDglHSD/eDPlp5ltMPU1bi1Zx9X42PrcinNK/z0zH9r8+vpGTxYjyL\nAOFxwv6cMBGzCSqgACcWXuf38WndJwxir1vlIwuZIuj9fMtF0LSKnQURZOR+\nRWMQs8IVPEH6SXEXO/agD/vgk4oaodBx4DllBPCnuf8TDB6D52bAZ8m7Qg1f\nwdH22AspGylC/po3qLl8SZmjxZDfd6rp81CK2kwMtLLDr0BVQX9IrHjX71hY\nYrOmbFJSFfjppp0vQ6NPdyXfOzskdoMLLuZjBy8R+EVA2IhD2kJ//uUf20OA\n3jrxegt94T9UJSTUk/gmiNo6t/4l4/IIXPJe/+Ml+aJVtpBcO9HcHIfdVTA6\nqLyv2Wg2i3s/GtDSO/dcG21F1CDoychJBJetIjkKIk1OIcv9NWs7tnFb9uhE\nKCiZ\r\n=9iOk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/node-ponyfill.js", "husky": {"hooks": {"pre-push": "make", "pre-commit": "make build && lint-staged"}}, "mocha": {"require": ["chai/register-expect", "chai/register-assert"], "check-leaks": true}, "browser": "dist/browser-ponyfill.js", "gitHead": "f42759f8f39a945011ec5fdce428c04db1b7aa40", "typings": "index.d.ts", "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "standard": {"env": ["mocha", "browser"], "ignore": ["/dist/", "bundle.js", "test.js", "test.*.js"], "globals": ["expect", "assert", "chai", "sinon"]}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "lint-staged": {"*.js": ["standard --fix"]}, "_nodeVersion": "10.24.0", "dependencies": {"node-fetch": "2.6.1"}, "react-native": "dist/react-native-ponyfill.js", "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "ora": "5.3.0", "chai": "4.3.4", "nock": "13.0.11", "husky": "4.3.8", "mocha": "8.3.2", "sinon": "9.2.4", "rollup": "2.41.2", "semver": "7.3.4", "codecov": "3.8.1", "express": "4.17.1", "webpack": "5.25.0", "standard": "16.0.3", "body-parser": "1.19.0", "lint-staged": "10.5.4", "serve-index": "1.9.1", "webpack-cli": "4.5.0", "whatwg-fetch": "3.0.0", "rollup-plugin-copy": "3.4.0", "rollup-plugin-terser": "7.0.2", "mocha-headless-chrome": "3.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_3.1.0_1615611542727_0.6252559625204814", "host": "s3://npm-registry-packages"}}, "3.1.1": {"name": "cross-fetch", "version": "3.1.1", "keywords": ["fetch", "http", "url", "promise", "async", "await", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@3.1.1", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "nyc": {"temp-dir": ".reports/.coverage"}, "dist": {"shasum": "a7ed5a9201d46223d805c5e9ecdc23ea600219eb", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-3.1.1.tgz", "fileCount": 14, "integrity": "sha512-eIF+IHQpRzoGd/0zPrwQmHwDC90mdvjk+hcbYhKoaRrEk4GEIDqdjs/MljmdPPoHTQudbmWS+f0hZsEpFaEvWw==", "signatures": [{"sig": "MEUCIQCvZRu0NxaSEkgNU1RHYJsymMcW1C2/XV9F88+tigq5ZAIgIcv7DScAOcMkA+suy4YeLHNIGN50/nLSzy/yqKSpCYU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 95370, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgU6k4CRA9TVsSAnZWagAAwU8QAIRcJR7SIXPjebmS37KT\nOloqr0fLeZPTMjy4dGWgDHwKgZYYMsXlXm4c8NAn/ccDsha8CDuNlgE1FVNE\nIEMvrKbDtvIWiMqsU0Tu9eUoPK6bZSl1iDn0h97s6DpCAp47q6hC0AP94sYF\n91tqM8ixeS03pPTmCfX/cPQoMHSLr9wz4UjtiwDMzu5F+pBmUWOhW+mRw0wH\nTwT5PGSvaDvX9QOil2cwZyiXky+XyMBXKEIX6MBvDHiZnEFph1C/etmGeG3d\nltXElpwuHWkXPDk+ab+9zSWCyUmVzJI28iWUgINWTCutLbobwjcewx6l0BgS\nf7vF00D8ProqXAkMxGRk3y9uGEmLkk1z5ubN9t3gCE5ZoWjsdKfobUDgig9d\nBuVIQAIdMXsoCVAt7+zWFZvEJADY92oVU42FDgjjOFFM9Me4bGndQbiyx+2y\nczcbldB0/CBNeSBVgAfCQ8X7UFcbjRst4SilKsvlP1JlIWh7fl912CzImVGM\nckoHZclzojI/wQVN+yAyZ0rzo1VSOibQWXhmDsqaiIsvjW5jNa0FFsoZoVhG\nGWDBMFStQvdD6gC0BcoMIy/cvl/dDD5O3dyHgghgmGwZUCiY99qX4wYYPgvS\nN+oWlJgdMKxLtGjEYhRaD6QGDVIcxECRS+2lVvMOaQ1ZpSWVOwmBmAa+lnVG\n9JG/\r\n=eO1K\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/node-ponyfill.js", "husky": {"hooks": {"pre-push": "make", "pre-commit": "make build && lint-staged"}}, "mocha": {"require": ["chai/register-expect", "chai/register-assert"], "check-leaks": true}, "browser": "dist/browser-ponyfill.js", "gitHead": "633fa8c865c925533f1497bf5f5d711b9b124f59", "typings": "index.d.ts", "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "standard": {"env": ["mocha", "browser"], "ignore": ["/dist/", "bundle.js", "test.js", "test.*.js"], "globals": ["expect", "assert", "chai", "sinon"]}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "lint-staged": {"*.js": ["standard --fix"]}, "_nodeVersion": "10.24.0", "dependencies": {"node-fetch": "2.6.1"}, "react-native": "dist/react-native-ponyfill.js", "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "ora": "5.3.0", "chai": "4.3.4", "nock": "13.0.11", "husky": "4.3.8", "mocha": "8.3.2", "sinon": "9.2.4", "rollup": "2.41.2", "semver": "7.3.4", "codecov": "3.8.1", "express": "4.17.1", "webpack": "5.25.0", "standard": "16.0.3", "typescript": "4.2.3", "body-parser": "1.19.0", "lint-staged": "10.5.4", "serve-index": "1.9.1", "webpack-cli": "4.5.0", "whatwg-fetch": "3.0.0", "rollup-plugin-copy": "3.4.0", "rollup-plugin-terser": "7.0.2", "mocha-headless-chrome": "3.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_3.1.1_1616095543823_0.6968361373457337", "host": "s3://npm-registry-packages"}}, "3.1.2": {"name": "cross-fetch", "version": "3.1.2", "keywords": ["fetch", "http", "url", "promise", "async", "await", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@3.1.2", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "nyc": {"temp-dir": ".reports/.coverage"}, "dist": {"shasum": "ee0c2f18844c4fde36150c2a4ddc068d20c1bc41", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-3.1.2.tgz", "fileCount": 15, "integrity": "sha512-+JhD65rDNqLbGmB3Gzs3HrEKC0aQnD+XA3SY6RjgkF88jV2q5cTc5+CwxlS3sdmLk98gpPt5CF9XRnPdlxZe6w==", "signatures": [{"sig": "MEQCIDjx3NdQ6aab+xQTUP2DjdDb7/6A6GVe5OQlkM+Z6o+UAiBr4hlk9/v37eF1kMD7ECJjoVbp9BYsaouAxp9QXpVkBg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 96553, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgVS5iCRA9TVsSAnZWagAAnmsQAIT5RkTUUJ++ozKUvGdd\nLDkrp8MQc8KG4JuyqPgJ6QDxJBaD0w8Q6FN5lC18xH9c0iWTi38gvgv97xNS\nZDBCz2coQofWjpycTW9DBcnDbe426c9zkWQeZBHeWesDFGfDqZfDKDbSsrzG\nbLlA9Vteun3gqJiymjqpOYGFL2VyrTsDfU8cIwb15B5Hmwf/PB3m2/JNEPK8\nvNXlWAux35WWKxRHIdLSc71Wt6Pulw6a3q8+HLX1oCqyMGniiDa5+s1L30bg\nqPpC/XnLY6X5yZgxKpcYR1pKfyD/0ctfFnGmqJRiIfm4ZKE3xcY0LX04PMlX\nWdmNqzlEzuKegPONQROs9i98ZzgbM9tobBcbx955AruROB2j7i1i4yIbjN+M\na2/Vna2xSRHWAUGQHtsu+pieso1Nmmw+LgT0Chi9nRCzqifKxZepaHliqPfM\nM7MoTg6GqSmWqe16RTq3RDTS3vdsr/Zs6byaKb5hlpr9xMtdhesDoQIoH3Qv\nDejxoXJWT1C3ffJAOo+lL/MZBHjWHKfHteALZ7eOION/ZQtN1UZn72t6ADkW\nzeZxzzKsWi5EuYpLoD2vpFjogq4t9NebMOJGQsC2U4y7dBWApNUwLq0wU24j\njT8cUg6K5P2hVz7KxYLZ2TwwjbYI2FuviymHiIKOC2PMRAS3teJLSMn3G29C\nzmad\r\n=Lcfx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/node-ponyfill.js", "husky": {"hooks": {"pre-push": "make", "commit-msg": "commitlint --edit $1", "pre-commit": "make build && lint-staged"}}, "mocha": {"require": ["chai/register-expect", "chai/register-assert"], "check-leaks": true}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "browser": "dist/browser-ponyfill.js", "gitHead": "cd5ffe34c31af99dd5afb1d19bf97628884de932", "typings": "index.d.ts", "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "standard": {"env": ["mocha", "browser"], "ignore": ["/dist/", "bundle.js", "test.js", "test.*.js"], "globals": ["expect", "assert", "chai", "sinon"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "lint-staged": {"*.js": ["standard --fix"]}, "_nodeVersion": "10.24.0", "dependencies": {"node-fetch": "2.6.1"}, "react-native": "dist/react-native-ponyfill.js", "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "ora": "5.3.0", "chai": "4.3.4", "nock": "13.0.11", "husky": "4.3.8", "mocha": "8.3.2", "sinon": "9.2.4", "rollup": "2.41.2", "semver": "7.3.4", "codecov": "3.8.1", "express": "4.17.1", "webpack": "5.25.0", "standard": "16.0.3", "typescript": "4.2.3", "body-parser": "1.19.0", "lint-staged": "10.5.4", "serve-index": "1.9.1", "webpack-cli": "4.5.0", "whatwg-fetch": "3.0.0", "@commitlint/cli": "12.0.1", "rollup-plugin-copy": "3.4.0", "rollup-plugin-terser": "7.0.2", "mocha-headless-chrome": "3.1.0", "cz-conventional-changelog": "3.3.0", "@commitlint/config-conventional": "12.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_3.1.2_1616195169962_0.5770086783967774", "host": "s3://npm-registry-packages"}}, "3.1.3-alpha.4": {"name": "cross-fetch", "version": "3.1.3-alpha.4", "keywords": ["fetch", "http", "url", "promise", "async", "await", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@3.1.3-alpha.4", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "nyc": {"temp-dir": ".reports/.coverage"}, "dist": {"shasum": "e7e1cbd02ffabdaee567c9d7d20239dcf8975d28", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-3.1.3-alpha.4.tgz", "fileCount": 14, "integrity": "sha512-7Kug4LY2vRbkcVcGTZivE9UIV9TzSdMGd3ERPmstTACfEp1aRVfNbLNSuxXbzXHRWD9IRX57n6UR1gMyfduyiw==", "signatures": [{"sig": "MEUCIE8wbyN3fVXnfCIypjzH2UZui42SEsol51IU4vhj7A82AiEAhHZIhq7rxfW1hP2MqsXZzEUlGXwg0FrOORRe14pUxm4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97299, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgX+NICRA9TVsSAnZWagAAAX0P+QHz0AIOIUXX6W9IP83G\nB/l+/9+NIyEEvYrdcBxF2auTFpCZ7fPk9QwpN3eFnK/JsymAKmCGFqFFgjpg\nWeUwD17Ng1DgrWCQ6Dc2kWr4poS8/w8PWUt9W53la+VWrw5ct/HG0n75WH+g\n99vo+3M88GJOllyhSG0Dsnzhqo+22GIiVI9MN2S7xwTWvrGF56lpewa+If8w\nlciZxgXgNxINDgOF76tZ2pVdmZr/p1pvOeYyUN4lOm/Z2JAitE8mszZp4GGr\nO+REIAw8KWQMqdrLqd6B44iSBTtFOrJfBu3rz3XJkbVIiWkZFljvkZlVZI6B\nQ6rdAA3CedGddh47pkWUKOMNWgkiEyFVX9HkCOzO1NUGknZxM3QnZ8vxquQ6\nOXD8SSZvFuQ53UkUgFchOTDFUtL0q70wddx1zsqYsVKSGqExcyd7CtSQy/qY\nwzlIhBaJaQqjC2K1D0eEVplKVcCS4lVUmbLhXoF/4PotWjcH0TSxoAsnl5AO\nADdYUZBWH+YCXs/BL82WTznoymK67uoMOiAf1gaFhXQS1WyRAPTDxZVTmFad\n38BhvjOvooA2TktvURXjodcRBHdJZ3eBzE3h2kTKbk6ujMR/664kur2Gk7GW\nNW6HQdKeN7XcBpGxnIs/23OIpRg20UDmRWkFj8Bq1Tjjn4ua3OlONWmKYIKL\n4g6P\r\n=idkW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/node-ponyfill.js", "mocha": {"require": ["chai/register-expect", "chai/register-assert"], "check-leaks": true}, "types": "index.d.ts", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "browser": "dist/browser-ponyfill.js", "gitHead": "c45b935fe82e6c8e36684fb531d9d2b6fcb85d1d", "scripts": {"prepare": "husky install"}, "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "standard": {"env": ["mocha", "browser"], "ignore": ["/dist/", "bundle.js", "test.js", "test.*.js", "api.spec.js", "*.ts"], "globals": ["expect", "assert", "chai", "sinon"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "lint-staged": {"*.js": ["make build", "standard --fix"]}, "_nodeVersion": "10.24.0", "dependencies": {"node-fetch": "2.6.1"}, "react-native": "dist/react-native-ponyfill.js", "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "ora": "5.4.0", "chai": "4.3.4", "nock": "13.0.11", "husky": "5.2.0", "mocha": "8.3.2", "sinon": "9.2.4", "rollup": "2.42.1", "semver": "7.3.4", "codecov": "3.8.1", "express": "4.17.1", "webpack": "5.27.1", "standard": "16.0.3", "typescript": "4.2.3", "@types/chai": "4.2.15", "@types/node": "14.14.36", "body-parser": "1.19.0", "lint-staged": "10.5.4", "serve-index": "1.9.1", "webpack-cli": "4.5.0", "@types/mocha": "8.2.2", "whatwg-fetch": "3.0.0", "@commitlint/cli": "12.0.1", "standard-version": "9.1.1", "rollup-plugin-copy": "3.4.0", "rollup-plugin-terser": "7.0.2", "mocha-headless-chrome": "3.1.0", "cz-conventional-changelog": "3.3.0", "@commitlint/config-conventional": "12.0.1"}, "standard-version": {"skip": {"changelog": true}}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_3.1.3-alpha.4_1616896840147_0.11285848525708886", "host": "s3://npm-registry-packages"}}, "3.1.3-alpha.5": {"name": "cross-fetch", "version": "3.1.3-alpha.5", "keywords": ["fetch", "http", "url", "promise", "async", "await", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@3.1.3-alpha.5", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "nyc": {"temp-dir": ".reports/.coverage"}, "dist": {"shasum": "a92a0b98e59391627a68b8bb21abd7b0f9c6317d", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-3.1.3-alpha.5.tgz", "fileCount": 14, "integrity": "sha512-NOupYTsRXTE+3m2m0MVqt4g/L42N+/dfMgvJVyc5nfPuMDpDMSw/L+l28ZI5mF3MeTe8DAHRj/ImHL8a9LDJbA==", "signatures": [{"sig": "MEQCIBAo/d26HtjPtaRYHSvZZK5lPzBiNWJJ36dIGm0FmQI6AiA9pkaqCyIO8+/TcMftSQpujLFNxcSROkz4h10jEPsp7g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97418, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgYP/RCRA9TVsSAnZWagAAV3MQAI2Duo5K6BlQtMeez3ri\nVvYp6smRpIU9zRABpBw2kRD+lQykeRi9mASAdbk4FgYN7N3st2vpoJmpu7/N\nMGvk5EKaXCg/Wr2WDC29iWuv3lxwJBHy5XTS2kp2kTlRZgnht6FGm3DEGh5z\n6dUdYKBHxAAA2VRb/sl7XXM1aiO1ByWMyJsVfTdGgN/AIkXYGRTWxUHwv/6o\nQQjF43PDNQPvgZxHQOHwqurLvTUpIZ4vo/7W9ZGM6LR8yg56cWxEP3DaFRp7\nrJr2ChoAnxydLIU1s0fMdQ+nBIs+tqrS8BjyZn22m1OwEOZz96JzRLnJ7NxP\n8kXC7KSiK9HhYOiCyufMf3QViiekl2am+tW281ewlIuZ+aMroJeOJgkGd0IE\n/wS7LZctl4ZXGiKttP+PTyyEP6HmnSUSGllTCGNw5vwFQgp5LJC0wqlzQ0Wq\nTtwV8MP/f3dNYn3fFIZmo9XWimO2WwhbrxCvT0wXU9KW2SvyAbLe8rrGUF+N\n5HF0cmS7B6hVKpkS4POMkm7S/2kJZaJoUaRu9ud6cf905SuhVYN5cmLE+OyK\nsKmPafESsJGHg4/KQshCuxAoe1XVJ0LWjSkEJ9++SW7dNYXqLQdz40U1R7hm\nM0Fv6kOdmtoqenkD+N92gMYpNRu2FnOUd63GVJyJwNwCnIQlpYwWsIRWJzRS\nWOdx\r\n=ZABv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/node-ponyfill.js", "mocha": {"require": ["chai/register-expect", "chai/register-assert"], "check-leaks": true}, "types": "index.d.ts", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "browser": "dist/browser-ponyfill.js", "gitHead": "d7210dc1965ab1368be8ed59944bb83b2ed8259b", "scripts": {"prepare": "husky install"}, "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "standard": {"env": ["mocha", "browser"], "ignore": ["/dist/", "bundle.js", "test.js", "test.*.js", "api.spec.js", "*.ts"], "globals": ["expect", "assert", "chai", "sinon"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "lint-staged": {"*.js": ["make build", "standard --fix"]}, "_nodeVersion": "10.24.0", "dependencies": {"node-fetch": "2.6.1"}, "react-native": "dist/react-native-ponyfill.js", "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "ora": "5.4.0", "chai": "4.3.4", "nock": "13.0.11", "husky": "5.2.0", "mocha": "8.3.2", "sinon": "9.2.4", "rollup": "2.42.1", "semver": "7.3.4", "codecov": "3.8.1", "express": "4.17.1", "webpack": "5.27.1", "standard": "16.0.3", "typescript": "4.2.3", "@types/chai": "4.2.15", "@types/node": "14.14.36", "body-parser": "1.19.0", "lint-staged": "10.5.4", "serve-index": "1.9.1", "webpack-cli": "4.5.0", "@types/mocha": "8.2.2", "whatwg-fetch": "3.0.0", "@commitlint/cli": "12.0.1", "standard-version": "9.1.1", "rollup-plugin-copy": "3.4.0", "rollup-plugin-terser": "7.0.2", "mocha-headless-chrome": "3.1.0", "cz-conventional-changelog": "3.3.0", "@commitlint/config-conventional": "12.0.1"}, "standard-version": {"skip": {"changelog": true}}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_3.1.3-alpha.5_1616969680544_0.001234745746089505", "host": "s3://npm-registry-packages"}}, "3.1.3-alpha.6": {"name": "cross-fetch", "version": "3.1.3-alpha.6", "keywords": ["fetch", "http", "url", "promise", "async", "await", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@3.1.3-alpha.6", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "nyc": {"temp-dir": ".reports/.coverage"}, "dist": {"shasum": "55c565bf00b2d3b0457754a7c76bab34c1466b7a", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-3.1.3-alpha.6.tgz", "fileCount": 14, "integrity": "sha512-0ite+zahD3S235L7PnAeBQ4/gR0g8XmMMzRIk5js4EhCKB2uJPgSen3xrxdQh9Qu1lWMyWBFmF1LKinBUSc4+w==", "signatures": [{"sig": "MEQCIHQAq2k96mIbz01gPqFhpdOZotOSyVtGZwp/LoDldPgsAiBkuaqIyARdWTp/hP7DvQLiGKsEX+4kppfwrTLtIeqZtw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97781, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgYQLzCRA9TVsSAnZWagAAtukP/2rrnSu/4p4gGLblUqh5\njsWF537NpH9K0AblX7T0wDXfxAY6Tp2/g7OeGhxFGVIHdXCc7+AL9gghdcZA\nS1mEVDEAH6kjzofUH6goFOHR6+rNdy8VURcxrDVTwiHiwyn6eWLVZ4MAS+87\nzNpJzVcqUsm6iwAYA729AfVTTR8hlrDPRiXkIkIbKBT2WPtsMJTYd6lnXZ15\njNSbd4dSJDMKAzYPRH5DrxCPI1Yhoq6joT8xJMb2Ew4sURgA4jGlQpDKCwv7\nr/TYL/44x1iRYStnwHZhMp5ZeY/mwKZJVSZlCWfj7KXt7hJ4W6C9PcQ6hT84\nwk690aVEqwn8jqM48kqMIxKQ2GeCTcuD5eGxGBDIQBG8lqouuuGzvebVZ5JU\nSjvYaEr0j8gI1h9yxUolMQIPF42/VCdxJ7SihmQkku2kNQM7d60PDiWPKfu5\nm0tv+Jk7k9imPZU1c64Xb2XNWOx8b+lqZuVEuVK64s0Yjc+yK2VoHy+OfH4C\ntD946DLCjuRKr0UpCmzOTOVHYc+IyLIszlKd0pphD7nFbcX0ZADOvTGJ0fRr\n2rgicAM52VpIWDBx6xdmqH2kVSBO0tkXQo5znJBSRP7r33TFHUh9UAktNw8Y\nXM+1EF+QGoWrz6K1V9JMOXo5UW10CH7ZZtOReOi/f0xwrM3S78ZudZ7ka7ul\nmgGx\r\n=nPK+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/node-ponyfill.js", "mocha": {"require": ["chai/register-expect", "chai/register-assert"], "check-leaks": true}, "types": "index.d.ts", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "browser": "dist/browser-ponyfill.js", "gitHead": "7c6a3f41670f6b8cc91652c2b2fa482b299a6443", "scripts": {"prepare": "husky install"}, "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "standard": {"env": ["mocha", "browser"], "ignore": ["/dist/", "bundle.js", "test.js", "test.*.js", "api.spec.js", "*.ts"], "globals": ["expect", "assert", "chai", "sinon"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "lint-staged": {"*.js": ["make build", "standard --fix"]}, "_nodeVersion": "10.24.0", "dependencies": {"node-fetch": "2.6.1"}, "react-native": "dist/react-native-ponyfill.js", "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "ora": "5.4.0", "chai": "4.3.4", "nock": "13.0.11", "husky": "5.2.0", "mocha": "8.3.2", "sinon": "9.2.4", "rollup": "2.42.1", "semver": "7.3.4", "codecov": "3.8.1", "express": "4.17.1", "webpack": "5.27.1", "standard": "16.0.3", "typescript": "4.2.3", "@types/chai": "4.2.15", "@types/node": "14.14.36", "body-parser": "1.19.0", "lint-staged": "10.5.4", "serve-index": "1.9.1", "webpack-cli": "4.5.0", "@types/mocha": "8.2.2", "whatwg-fetch": "3.0.0", "@commitlint/cli": "12.0.1", "standard-version": "9.1.1", "rollup-plugin-copy": "3.4.0", "rollup-plugin-terser": "7.0.2", "mocha-headless-chrome": "3.1.0", "cz-conventional-changelog": "3.3.0", "@commitlint/config-conventional": "12.0.1"}, "standard-version": {"skip": {"changelog": true}}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_3.1.3-alpha.6_1616970482675_0.9885932618484579", "host": "s3://npm-registry-packages"}}, "3.1.3": {"name": "cross-fetch", "version": "3.1.3", "keywords": ["fetch", "http", "url", "promise", "async", "await", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@3.1.3", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "nyc": {"temp-dir": ".reports/.coverage"}, "dist": {"shasum": "b8e7d5f19161c4a0ca916f707978848786043afb", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-3.1.3.tgz", "fileCount": 14, "integrity": "sha512-2i6v88DTqVBNODyjD9U6Ycn/uSZNvyHe25cIbo2fFnAACAsaLTJsd23miRWiR5NuiGXR9wpJ9d40/9WAhjDIrw==", "signatures": [{"sig": "MEYCIQDKaLO4FXo1suq4+875r3162oOiqCFU6KxbFI9ZoWIShwIhAJPgBQSGIUiPIJbsxv+ZwApYqZSuiBIxqX5R+Kg8rMN7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97774, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgY27PCRA9TVsSAnZWagAAWwQP/1qgu6rX5cXZf0r0Y7La\nqHAVLNoTciPObtAgOhTJ5GLBqXb7q0wTyECl6h5ofN1ChOXFvugbGv+9e9HP\nmVgkElvPS3Aq8FRBeH2dGnGBTbMrdo5UC8/Ib5fKIEsbw92k7OvFpudiTbbq\n+YUBdg/KiUf0J9Z2k+NEabugFi7ofxsIACHkWP9yyT9ilI+mzTwRBXqU+cx+\nzQD3mtd6PSTMh16DJR99nTZOqyorElGJifiWEtpj7+YSPSIX5zc07mJmtDBy\nnpQnQeNBtgQqgxFhhhBdbqIuLlz1P8lSkCclcTiRIZpEcZKiAJc/uxK/BOEx\nNNCeoBIvkrvMmHOprhc7UwpP4nl49o9tthPsQ/SOTjvGaOSHAVC5Mv0nXZch\nyy1Z+9Hw12whbRCiYM0Xv4PiuxQo6JWqqqNXTOCVGddI+NzlRVd3cTwyBINq\nS3f2U/cmBwfDIZXbvayGBeDvAaIu3OQ7CYIlHiys6WSvn+95r33msou0t85Z\nyVBN2whDHQMYvkRDAs5VYcyhdZqs08BKTxq+fNYPn7uFPTAgKHN8TroAt0zx\nB+SlNqR+BFfYugBJdzV4V1jVYzZWrdNGSzCycfyNhxQ7uJYMB4UdInjMF7wP\nnjz81KyH8RFxhXLz/Ngff2R1bFFVpxB1qQsC8a2+3JIrqAW1SobViu8Htcx3\nOEK/\r\n=wlUv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/node-ponyfill.js", "mocha": {"require": ["chai/register-expect", "chai/register-assert"], "check-leaks": true}, "types": "index.d.ts", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "browser": "dist/browser-ponyfill.js", "gitHead": "ddfe452c7c7569a0239f57cbff1e2e4ca3723800", "scripts": {"prepare": "husky install"}, "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "standard": {"env": ["mocha", "browser"], "ignore": ["/dist/", "bundle.js", "test.js", "test.*.js", "api.spec.js", "*.ts"], "globals": ["expect", "assert", "chai", "sinon"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "lint-staged": {"*.js": ["make build", "standard --fix"]}, "_nodeVersion": "10.24.0", "dependencies": {"node-fetch": "2.6.1"}, "react-native": "dist/react-native-ponyfill.js", "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "ora": "5.4.0", "chai": "4.3.4", "nock": "13.0.11", "husky": "6.0.0", "mocha": "8.3.2", "sinon": "10.0.0", "rollup": "2.44.0", "semver": "7.3.5", "codecov": "3.8.1", "express": "4.17.1", "webpack": "5.28.0", "standard": "16.0.3", "typescript": "4.2.3", "@types/chai": "4.2.15", "@types/node": "14.14.37", "body-parser": "1.19.0", "lint-staged": "10.5.4", "serve-index": "1.9.1", "webpack-cli": "4.6.0", "@types/mocha": "8.2.2", "whatwg-fetch": "3.0.0", "@commitlint/cli": "12.0.1", "standard-version": "9.1.1", "rollup-plugin-copy": "3.4.0", "rollup-plugin-terser": "7.0.2", "mocha-headless-chrome": "3.1.0", "cz-conventional-changelog": "3.3.0", "@commitlint/config-conventional": "12.0.1"}, "standard-version": {"skip": {"changelog": true}}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_3.1.3_1617129167221_0.1749836307624888", "host": "s3://npm-registry-packages"}}, "3.1.4-alpha.0": {"name": "cross-fetch", "version": "3.1.4-alpha.0", "keywords": ["fetch", "http", "url", "promise", "async", "await", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@3.1.4-alpha.0", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "nyc": {"temp-dir": ".reports/.coverage"}, "dist": {"shasum": "015649a305ac8edc43e525778f60751b3e159828", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-3.1.4-alpha.0.tgz", "fileCount": 13, "integrity": "sha512-7hcNtCGD9VnrB6BiSKyKsyz5UfJyC9MGeLyMC/FpF2rPCZB/htwGE84HaPnnRTXTsZ7xlzhqAjBHzk9C8KBMrA==", "signatures": [{"sig": "MEQCIBduRgnrVYtzu+QmlZus6Dnn53wwxl4VTdJxAFboMhZwAiALdPlhEiFUCPRF0M2j2GM6NmXbFsRs2rVNo9gnKhdUmw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73846, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZelSCRA9TVsSAnZWagAAArgQAIwoWoB6UZtoAmTjLqyU\nGtlR6M9HTwGnxsH+AiIbxMsCi01X0hTPIvO4vsSXSz/NUQ3pR8h6DUsNT0lN\n39ZdvEqpuYDQDr0uP9W0DS9IrFW5hK5vuFv5cvcqmehvKXNcE5tqXKySL7f3\niGO53JWXKB/FeBJuxowvfqhIHmaQJeKly4ibpvO9rL73g5R/7u7z08osQssg\nFqxRCImNYEBTMQDYYCXBPhVF7MX/VlU/x+Hv9NmHLSek1KxXEV2f2qhGVY5u\n8dO8qYILtGC/f5Rx4FZ4e7aX0QWIB50LPLH6ZxcQ9WwU0eGkgNcq0zeBMnw3\nDkwwTV2DFf5/9SIBvXRcVyVfwcBsGcDwo0YkxH7WVoN+Ibd9CWBWaDcH3I0A\n87pqfjWkaYXJsKIEiElEE5aeIwXcIGNpkstw+x9nhJPBLIZbS2bME4NQrVxz\nqdaC5n6w2y1Qsrc3NJt3DhOi8qRzCnxOPIxwtwfY/Vc1OT9Ynp64vrU/740r\n61YGuKsL+i1vq89pm166gNM+daR9lM0MHgkKx/1sDeUtRDd+bDceI70LkLlD\nuqfmJCK7jt7aMwzxQOM9aW8rfzNA+PALJ2Oe3gLTQZ7+0njbAX+tpnc2p85y\negqlWKeSX/sISnz0YokYMAhLqP5fiX0wZbStqjeTjr7iyY98WWZ9uJ25jmXX\nVCS4\r\n=8iNA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/node-ponyfill.js", "mocha": {"require": ["chai/register-expect", "chai/register-assert"], "check-leaks": true}, "types": "index.d.ts", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "browser": "dist/browser-ponyfill.js", "gitHead": "47bc898bc24b7759b20ce679c269830e5c94c471", "scripts": {"prepare": "husky install"}, "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "standard": {"env": ["mocha", "browser"], "ignore": ["/dist/", "bundle.js", "test.js", "test.*.js", "api.spec.js", "*.ts"], "globals": ["expect", "assert", "chai", "sinon"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "lint-staged": {"*.js": ["make build", "standard --fix"]}, "_nodeVersion": "10.24.0", "dependencies": {"node-fetch": "2.6.1"}, "react-native": "dist/react-native-ponyfill.js", "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "ora": "5.4.0", "chai": "4.3.4", "nock": "13.0.11", "husky": "6.0.0", "mocha": "8.3.2", "sinon": "10.0.0", "rollup": "2.44.0", "semver": "7.3.5", "codecov": "3.8.1", "express": "4.17.1", "webpack": "5.28.0", "standard": "16.0.3", "typescript": "4.2.3", "@types/chai": "4.2.15", "@types/node": "14.14.37", "body-parser": "1.19.0", "lint-staged": "10.5.4", "serve-index": "1.9.1", "webpack-cli": "4.6.0", "@types/mocha": "8.2.2", "whatwg-fetch": "3.0.0", "@commitlint/cli": "12.0.1", "standard-version": "9.1.1", "rollup-plugin-copy": "3.4.0", "rollup-plugin-terser": "7.0.2", "mocha-headless-chrome": "3.1.0", "cz-conventional-changelog": "3.3.0", "@commitlint/config-conventional": "12.0.1"}, "standard-version": {"skip": {"changelog": true}}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_3.1.4-alpha.0_1617291602091_0.023527907986356178", "host": "s3://npm-registry-packages"}}, "3.1.4": {"name": "cross-fetch", "version": "3.1.4", "keywords": ["fetch", "http", "url", "promise", "async", "await", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@3.1.4", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "nyc": {"temp-dir": ".reports/.coverage"}, "dist": {"shasum": "9723f3a3a247bf8b89039f3a380a9244e8fa2f39", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-3.1.4.tgz", "fileCount": 13, "integrity": "sha512-1eAtFWdIubi6T4XPy6ei9iUFoKpUkIF971QLN8lIvvvwueI65+Nw5haMNKUwfJxabqlIIDODJKGrQ66gxC0PbQ==", "signatures": [{"sig": "MEYCIQC0iI6+TRxQ5RV93J1nPhy/HmEa4bzKv2zqKAzUhwzkpwIhAPRL1Da1l8y/ckrUa2dRZGFgNiuhEFph98PqkgWB0RHY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73838, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2nSRCRA9TVsSAnZWagAAzIsP/3XYAt6mkDNosBnil6Kz\nE/E3BHN/2Z86wu57IMtbxh+csUWah0ey1n1lC1cJE4T7XaTZ5X/U2Gh7kbvv\n9l0LtLMRb0Lbz1H68M1f8UG0LA/em9cdf5pfmnXxW1+rKPn9WAPKAjhActu5\nSb4T1wAIEBR15L9g/WulaFhApzV098nfiNuforLW3Gb3klQB28+jdVJVPIw/\n6hDr6pyL5cdV+U+kUwvY7OAP+K5ltq19duOncZZzLdSpwvrlfloPsagH9QEu\nhN56viAjsVh30oS2jXAd4SBj/hBYSsvRqocSgPbc16PQNwCceLwXNn2JPKJu\n0dtFYNqsw8PyEFd4Xd3I/DqAZEJtS+Zg9fEOB7/g4Uc3dVH4RS2CXXJ+qhjc\n6lRAEooe1dbz5qdZ47eIZogB92VxqEH+mWLLiqISnCRZSduPIeE5PnU+yetb\nTVpfA2qt0nBAU/T7nyDMPKoZ6zl/lwm+UEZjIxJfgGu4Dchv0ZxLvspj6e+s\n6pWdu7wwFXTHMHepFTwA41+XWWn22tO0gSqN9ZYGYsFF7hc1cfMybWpy2EOL\nvWduyhzYITuG+hGuIAYPu5pBipj32raYEMkhwLwXL5KmZjDD94uEG4WNjiaF\nYt2gOiRbJY+WS4TVkHpRn82QmWb23RVWV+luDt6kRf0Wh7ruBkpG/CoLr7P3\ntqIn\r\n=wUSl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/node-ponyfill.js", "mocha": {"require": ["chai/register-expect", "chai/register-assert"], "check-leaks": true}, "types": "index.d.ts", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "browser": "dist/browser-ponyfill.js", "gitHead": "7e4b657fa43915672350bcc53413721cbc14bd36", "scripts": {"prepare": "husky install"}, "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "standard": {"env": ["mocha", "browser"], "ignore": ["/dist/", "bundle.js", "test.js", "test.*.js", "api.spec.js", "*.ts"], "globals": ["expect", "assert", "chai", "sinon"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "lint-staged": {"*.js": ["make build", "standard --fix"]}, "_nodeVersion": "10.24.0", "dependencies": {"node-fetch": "2.6.1"}, "react-native": "dist/react-native-ponyfill.js", "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "ora": "5.4.0", "chai": "4.3.4", "nock": "13.0.11", "husky": "6.0.0", "mocha": "8.3.2", "sinon": "10.0.0", "rollup": "2.44.0", "semver": "7.3.5", "codecov": "3.8.1", "express": "4.17.1", "webpack": "5.28.0", "standard": "16.0.3", "typescript": "4.2.3", "@types/chai": "4.2.15", "@types/node": "14.14.37", "body-parser": "1.19.0", "lint-staged": "10.5.4", "serve-index": "1.9.1", "webpack-cli": "4.6.0", "@types/mocha": "8.2.2", "whatwg-fetch": "3.0.0", "@commitlint/cli": "12.0.1", "standard-version": "9.1.1", "rollup-plugin-copy": "3.4.0", "rollup-plugin-terser": "7.0.2", "mocha-headless-chrome": "3.1.0", "cz-conventional-changelog": "3.3.0", "@commitlint/config-conventional": "12.0.1"}, "standard-version": {"skip": {"changelog": true}}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_3.1.4_1617405975429_0.04662795443691592", "host": "s3://npm-registry-packages"}}, "2.2.4": {"name": "cross-fetch", "version": "2.2.4", "keywords": ["fetch", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@2.2.4", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "nyc": {"temp-dir": ".reports/.coverage"}, "dist": {"shasum": "c3e04298a684f9a201245ca99079b66c0b0b175d", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-2.2.4.tgz", "fileCount": 11, "integrity": "sha512-mTkfZra7jooqlUdtGRqRMfgG4QJhrrZUvXael4eaq47tOzvgfWh2MKg81KpvSek5Myq7nZJBwxKSOFVT5Ah1EA==", "signatures": [{"sig": "MEUCIA/bTui6JdVY9IImsgCUXyPAIfYEPXcMWJ/fcZJCeji2AiEAjP0ar2EIpn9m7nMw6lbM/xsksquj87eSPy6qiBsbiY4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68086, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg/dF7CRA9TVsSAnZWagAAeikQAI+djCzbs7GBfG/okXlH\nfuJzwllnYip8BF4HKj8QgnO1qc5op3tOOpDoJ+1UHg28MjIMcjxY2uIeSpnz\nIur8wiV25G0iOoSWY1i3a/N8c/6Ou9M3hKHZJ8Ob/Qf6ZNoTEJK2/bpclaA1\nEj0/M+RFaFrL4kCvZGc5vV/YKn/IVgg4CyZ7/blDK7jRI8PV02qEUrTJVM9n\ntUtE8oUHWPW4lUA22cBZe3q4aogiPqzjNtDYSAtwJJEkAndandWUCbBa5mQ5\nDd1zKVggzyTWq2lTx+xMsEOnXX7/ZF0Y2JryuWs0Ry7BxoOjywEtasA/ZP2j\ng66Tum8MU/Un5J9Gfluqmjr9/gG9QvlPZXCG41r2O/SEupx4oRreEdBP9pT2\nbDRjd6RdcnDDuDDEDjXHpF3Ft2fZWpX5/xgEN4h2DRwxIrgaP7wIlUKKaIUU\nZSFoXfCUhkjQwb5aEo24aPNfLpct8eNzn9w9alzezgVGrs5ysKgIM3FQkDnu\n76CCYp+JvLyhzvs3QZWi97UBJJRG8SRvemaT4g09DmB3bwH4fpy7tF33RGYq\nRUvolcPLqe5XM5MWO6ffDIKvKRtUbw5PrtvvSb184VM0aAfCcz27/JPhoPWe\nJbAqiVH70EGVQL7TYP8s5+qZlKdoTR45oAvF/5WTFkDd+QGYlQWL205PoMaN\nwuVC\r\n=cH5w\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/node-ponyfill.js", "husky": {"hooks": {"pre-commit": "npm run -s build && lint-staged"}}, "browser": "dist/browser-ponyfill.js", "gitHead": "0820407fd41fd26cb9b9937ae55f96eaabde1a5c", "scripts": {"lint": "standard", "test": "npm run -s test:browser:headless && npm run -s test:node:plain && npm run -s test:node:bundle && npm run -s lint", "build": "rollup -c", "sauce": "./tasks/sauce", "codecov": "nyc report --reporter=text-lcov > coverage.lcov && codecov", "security": "snyk test", "deploy:major": "npm version major && git push --follow-tags", "deploy:minor": "npm version minor && git push --follow-tags", "deploy:patch": "npm version patch && git push --follow-tags", "test:node:plain": "nyc mocha test/node-plain/index.js", "test:node:bundle": "nyc mocha test/node-bundle/index.js", "test:browser:plain": "opn test/browser/index.html", "pretest:node:bundle": "webpack-cli --progress --config test/node-bundle/webpack.config.js", "test:browser:headless": "mocha-headless-chrome -f test/browser-headless/index.html -a no-sandbox -a disable-setuid-sandbox"}, "typings": "index.d.ts", "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "standard": {"env": ["mocha", "browser"], "ignore": ["/dist/", "test/node-bundle/index.js"], "globals": ["expect", "chai", "sinon"]}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "6.14.2", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "lint-staged": {"*.js": ["standard --fix", "git add"]}, "_nodeVersion": "8.12.0", "dependencies": {"node-fetch": "^2.6.1", "whatwg-fetch": "2.0.4"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "13.1.0", "ora": "3.0.0", "chai": "4.2.0", "nock": "10.0.1", "snyk": "1.105.0", "husky": "1.1.2", "mocha": "5.2.0", "sinon": "7.0.0", "rollup": "0.66.6", "codecov": "3.1.0", "opn-cli": "3.1.0", "webpack": "4.23.0", "standard": "12.0.1", "lint-staged": "7.3.0", "webpack-cli": "3.1.2", "rollup-plugin-copy": "0.2.3", "rollup-plugin-uglify": "6.0.0", "mocha-headless-chrome": "2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_2.2.4_1627246971364_0.5837339082600108", "host": "s3://npm-registry-packages"}}, "2.2.5": {"name": "cross-fetch", "version": "2.2.5", "keywords": ["fetch", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@2.2.5", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "nyc": {"temp-dir": ".reports/.coverage"}, "dist": {"shasum": "afaf5729f3b6c78d89c9296115c9f142541a5705", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-2.2.5.tgz", "fileCount": 11, "integrity": "sha512-xqYAhQb4NhCJSRym03dwxpP1bYXpK3y7UN83Bo2WFi3x1Zmzn0SL/6xGoPr+gpt4WmNrgCCX3HPysvOwFOW36w==", "signatures": [{"sig": "MEYCIQCUhjzofUN539/Fpn2y5Iyt+r3zNzr6nTCsK8VcBLtbpAIhAJqlEA3cVrWUKrnYTlSV9+dvTZLcAG201brjz4n0AoVb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68085, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg/dOBCRA9TVsSAnZWagAAEBEQAJue0o5vis5DEMD1tWZ6\nCJPH+svEFUdSHIk0lC+nLF0fkXdp948gBP1OzqNliWRRs1b2fou2eYvrMClv\nsg8wUw1TfEb/GmTLnXyInFioSRL/vNjOhmMP/LjNBy2/jspplbR8vcw1Mc9M\ngxAL7BTID8RrdoHWKEOaMv81FGUdd0GyV5pNCpgYZRN22xOs1oc6zWhPlig7\n0BFFw37aC2c9tLSxqemIEghp2JnLIUHK203Gbf0hIiTxtQicDNGqQqYbZvZ8\niRnhH90k6/wY1OfQq+0n/Z/qcXBEG2+60XjEoOvSVzZJBOVSeYSs1WanILh8\ntEaoZUufY7wJyomdlYlDBb8RMMPktZM9SQsvk6kxpGi9b9XvvrEOjMtuwODA\nVGP2ZII9vbR/ZKVaAGNwsh0IguMIgukJvLwz514XZAxWF9gocuxVyuGa8rqi\nHD0kM/XlaDj6jmJRFUuT2o2UMzthm5dLXuRqfDgx6VQK104lBbiMMwNeiNjS\na7ncLHpHJh6A6H5T+F66DDXFn74OZEzki9yv0aydMWYDZ3aaQjhZsMxlIh64\nBEaAOpDzoAiJLk7S2x5wKjAv6TS10p4pDktEAmdX7seZaw0tM58ZlVijCF8O\nHZ7R1NUYwyCZZ7CJaXlsGlsT+Q18KMhd+ZevThcCqD9n/2nLF4Ikcaw2cL1j\nQclA\r\n=GDYO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/node-ponyfill.js", "husky": {"hooks": {"pre-commit": "npm run -s build && lint-staged"}}, "browser": "dist/browser-ponyfill.js", "gitHead": "3abdc676d0b198e54d87c0df17b59668243826c4", "scripts": {"lint": "standard", "test": "npm run -s test:browser:headless && npm run -s test:node:plain && npm run -s test:node:bundle && npm run -s lint", "build": "rollup -c", "sauce": "./tasks/sauce", "codecov": "nyc report --reporter=text-lcov > coverage.lcov && codecov", "security": "snyk test", "deploy:major": "npm version major && git push --follow-tags", "deploy:minor": "npm version minor && git push --follow-tags", "deploy:patch": "npm version patch && git push --follow-tags", "test:node:plain": "nyc mocha test/node-plain/index.js", "test:node:bundle": "nyc mocha test/node-bundle/index.js", "test:browser:plain": "opn test/browser/index.html", "pretest:node:bundle": "webpack-cli --progress --config test/node-bundle/webpack.config.js", "test:browser:headless": "mocha-headless-chrome -f test/browser-headless/index.html -a no-sandbox -a disable-setuid-sandbox"}, "typings": "index.d.ts", "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "standard": {"env": ["mocha", "browser"], "ignore": ["/dist/", "test/node-bundle/index.js"], "globals": ["expect", "chai", "sinon"]}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "lint-staged": {"*.js": ["standard --fix", "git add"]}, "_nodeVersion": "13.11.0", "dependencies": {"node-fetch": "2.6.1", "whatwg-fetch": "2.0.4"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "13.1.0", "ora": "3.0.0", "chai": "4.2.0", "nock": "10.0.1", "snyk": "1.105.0", "husky": "1.1.2", "mocha": "5.2.0", "sinon": "7.0.0", "rollup": "0.66.6", "codecov": "3.1.0", "opn-cli": "3.1.0", "webpack": "4.23.0", "standard": "12.0.1", "lint-staged": "7.3.0", "webpack-cli": "3.1.2", "rollup-plugin-copy": "0.2.3", "rollup-plugin-uglify": "6.0.0", "mocha-headless-chrome": "2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_2.2.5_1627247489404_0.1789342107282943", "host": "s3://npm-registry-packages"}}, "3.2.0-alpha.0": {"name": "cross-fetch", "version": "3.2.0-alpha.0", "keywords": ["fetch", "http", "url", "promise", "async", "await", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@3.2.0-alpha.0", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "nyc": {"temp-dir": ".reports/.coverage"}, "dist": {"shasum": "214764cdad95255cb4f2e68fc03bd1874af993ce", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-3.2.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-id88emgIscP5mNnc3DJF5CMANWRHzAhQmlyLzPAQvvnFWGBQYAV3HBcGAP1S0VXEAax7vEhM0NbBsQMtgyie/g==", "signatures": [{"sig": "MEUCIEH4+4in/aIwULxe4xNwKvRXDse9vNgAOHoe+9zteHUXAiEAnVKenQCZaBgJPK0Es/1ugw97QtD+wTWccGbF3FL5EF0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10467}, "main": "dist/node-ponyfill.js", "mocha": {"require": ["chai/register-expect", "chai/register-assert"], "check-leaks": true}, "types": "index.d.ts", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "browser": "dist/browser-ponyfill.js", "gitHead": "94e834d13e03fc1eb05da74d648d9ecc032967b6", "scripts": {"prepare": "husky install"}, "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "standard": {"env": ["mocha", "browser"], "ignore": ["/dist/", "bundle.js", "test.js", "test.*.js", "api.spec.js", "*.ts"], "globals": ["expect", "assert", "chai"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "lint-staged": {"*.js": ["make build", "standard --fix"]}, "_nodeVersion": "12.14.1", "dependencies": {"node-fetch": "2.6.5"}, "react-native": "dist/react-native-ponyfill.js", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "15.1.0", "chai": "4.3.4", "nock": "13.1.3", "husky": "6.0.0", "mocha": "8.3.2", "yargs": "16.2.0", "rollup": "2.58.0", "semver": "7.3.5", "codecov": "3.8.3", "express": "4.17.1", "webpack": "5.58.2", "open-cli": "6.0.1", "standard": "16.0.4", "typescript": "4.4.4", "@types/chai": "4.2.22", "@types/node": "14.14.37", "body-parser": "1.19.0", "lint-staged": "10.5.4", "serve-index": "1.9.1", "webpack-cli": "4.9.0", "@types/mocha": "8.2.2", "whatwg-fetch": "3.6.2", "@commitlint/cli": "12.0.1", "standard-version": "9.3.1", "rollup-plugin-copy": "3.4.0", "rollup-plugin-terser": "7.0.2", "mocha-headless-chrome": "3.1.0", "cz-conventional-changelog": "3.3.0", "@commitlint/config-conventional": "12.0.1"}, "standard-version": {"skip": {"changelog": true}}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_3.2.0-alpha.0_1634672373250_0.8376992667578189", "host": "s3://npm-registry-packages"}}, "3.2.0-alpha.1": {"name": "cross-fetch", "version": "3.2.0-alpha.1", "keywords": ["fetch", "http", "url", "promise", "async", "await", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@3.2.0-alpha.1", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "nyc": {"temp-dir": ".reports/.coverage"}, "dist": {"shasum": "a3f2f76008c5f80c8c2ecd4252d4317946bce8ca", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-3.2.0-alpha.1.tgz", "fileCount": 13, "integrity": "sha512-B60cj3COstuxn6ZbNnnEfem6GKNvhejDE4QAq7dljhAWhimdIuCwfn0ZFvoU8C0aSt8O0u4H5iKQZEdBJMJP3Q==", "signatures": [{"sig": "MEQCIFTqXzj+/GfnmkVNu5L29HAgq7g7tlondop2+YfTN15AAiA6LWkpVFvzmXbGd/5GuSY+ikFY+fb2UeRn4K5tdxCS5Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87638}, "main": "dist/node-ponyfill.js", "mocha": {"require": ["chai/register-expect", "chai/register-assert"], "check-leaks": true}, "types": "index.d.ts", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "browser": "dist/browser-ponyfill.js", "gitHead": "7689a6615366d31b9e47c06f0d32e70593047e6d", "scripts": {"prepare": "husky install", "prepublish": "make build"}, "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "standard": {"env": ["mocha", "browser"], "ignore": ["/dist/", "bundle.js", "test.js", "test.*.js", "api.spec.js", "*.ts"], "globals": ["expect", "assert", "chai"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "7.21.0", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "lint-staged": {"*.js": ["make build", "standard --fix"]}, "_nodeVersion": "10.19.0", "dependencies": {"node-fetch": "2.6.5"}, "react-native": "dist/react-native-ponyfill.js", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "15.1.0", "chai": "4.3.4", "nock": "13.1.3", "husky": "6.0.0", "mocha": "8.3.2", "yargs": "16.2.0", "rollup": "2.58.0", "semver": "7.3.5", "codecov": "3.8.3", "express": "4.17.1", "webpack": "5.58.2", "open-cli": "6.0.1", "standard": "16.0.4", "typescript": "4.4.4", "@types/chai": "4.2.22", "@types/node": "14.14.37", "body-parser": "1.19.0", "lint-staged": "10.5.4", "serve-index": "1.9.1", "webpack-cli": "4.9.0", "@types/mocha": "8.2.2", "whatwg-fetch": "3.6.2", "@commitlint/cli": "12.0.1", "standard-version": "9.3.1", "rollup-plugin-copy": "3.4.0", "rollup-plugin-terser": "7.0.2", "mocha-headless-chrome": "3.1.0", "cz-conventional-changelog": "3.3.0", "@commitlint/config-conventional": "12.0.1"}, "standard-version": {"skip": {"changelog": true}}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_3.2.0-alpha.1_1634673813245_0.4943626162633472", "host": "s3://npm-registry-packages"}}, "3.2.0-alpha.2": {"name": "cross-fetch", "version": "3.2.0-alpha.2", "keywords": ["fetch", "http", "url", "promise", "async", "await", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@3.2.0-alpha.2", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "nyc": {"temp-dir": ".reports/.coverage"}, "dist": {"shasum": "51b70796f44b68cdc1e86fdc3220c1d17dbb86a8", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-3.2.0-alpha.2.tgz", "fileCount": 13, "integrity": "sha512-drGPjxxGKIsZN52uPUX929wCGLxd6lXkfhUlB8Zso+wWw5LXiJy+3iQQ1iqCB0O0SittVCWk1ZPD07wYvM19pQ==", "signatures": [{"sig": "MEQCICWmejr7vQ1Q/xxewaUAoW2InwDG8OdF52ShWBs9Qdj4AiAN7Wh+HL3GiiMwKYX7SvqIA6zApMqrlKBCvLIlXo2efg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87679}, "main": "dist/node-ponyfill.js", "mocha": {"require": ["chai/register-expect", "chai/register-assert"], "check-leaks": true}, "types": "index.d.ts", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "browser": "dist/browser-ponyfill.js", "gitHead": "dfd48c7670720bcf918bc94b4cacf861215831db", "scripts": {"prepare": "husky install", "prepublishOnly": "rimraf dist && make dist"}, "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "standard": {"env": ["mocha", "browser"], "ignore": ["/dist/", "bundle.js", "test.js", "test.*.js", "api.spec.js", "*.ts"], "globals": ["expect", "assert", "chai"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "7.21.0", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "lint-staged": {"*.js": ["make build", "standard --fix"]}, "_nodeVersion": "10.19.0", "dependencies": {"node-fetch": "2.6.5"}, "react-native": "dist/react-native-ponyfill.js", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "15.1.0", "chai": "4.3.4", "nock": "13.1.3", "husky": "6.0.0", "mocha": "8.3.2", "yargs": "16.2.0", "rimraf": "3.0.2", "rollup": "2.58.0", "semver": "7.3.5", "codecov": "3.8.3", "express": "4.17.1", "webpack": "5.58.2", "open-cli": "6.0.1", "standard": "16.0.4", "typescript": "4.4.4", "@types/chai": "4.2.22", "@types/node": "14.14.37", "body-parser": "1.19.0", "lint-staged": "10.5.4", "serve-index": "1.9.1", "webpack-cli": "4.9.0", "@types/mocha": "8.2.2", "whatwg-fetch": "3.6.2", "@commitlint/cli": "12.0.1", "standard-version": "9.3.1", "rollup-plugin-copy": "3.4.0", "rollup-plugin-terser": "7.0.2", "mocha-headless-chrome": "3.1.0", "cz-conventional-changelog": "3.3.0", "@commitlint/config-conventional": "12.0.1"}, "standard-version": {"skip": {"changelog": true}}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_3.2.0-alpha.2_1634674698142_0.010281058076072824", "host": "s3://npm-registry-packages"}}, "3.1.5": {"name": "cross-fetch", "version": "3.1.5", "keywords": ["fetch", "http", "url", "promise", "async", "await", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@3.1.5", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "nyc": {"temp-dir": ".reports/.coverage"}, "dist": {"shasum": "e1389f44d9e7ba767907f7af8454787952ab534f", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-3.1.5.tgz", "fileCount": 13, "integrity": "sha512-lvb1SBsI0Z7GDwmuid+mU3kWVBwTVUbe7S0H52yaaAdQOXq2YktTCZdlAcNKFzE6QtRz0snpw9bNiPeOIkkQvw==", "signatures": [{"sig": "MEUCICBsd4ans1ErY2ai2SUkXqQfGZFl2q+Dbw/7mmWZxRYdAiEAwDIrmf2pchC/n/eg6lhUazVCVo66pOkiyzDo/hZt7BU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73827, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6efvCRA9TVsSAnZWagAAe5EP/j1/nM109GubRSlo2rNn\nPva0GDxGk7GCyMe+G6MHlKv+74rnIRHDgcGrtDjRUigElO97K33XedremQtH\nGMEF74uZcMHBSk8D20z7K8c13sFOxYVpgWeiNBnRSobaqk16tZ/vN2K3ZRsw\nGk41t7cdvfLFKzXYKL7gOf9LNc+myxRtAw5KvA3VsiOI85ehWfXIpNd+Idzg\nKgtTUhanujvcORpKBFkpS9U0h9mUIYKhg/B3DugFOLrV2IHO8XEqI0zahJ7i\nLkS1+H1YNZmiVvs/8dA/SvmrK5Gu1C2zgcFRAuSVzwoBMfWDyvJnfbrXocBy\nLEABXxoKV5lm7NdOTTcVpnOsjC8J1SAU3778st7jOrX4CUYLcjYfktOdWDXj\n6ygDT9VpxNTl91tt7EHfCjJ/HsJUmp1/+z6avQW/I2HwoMZOb6hoe/he2gG+\nlzvLH0UEoenr6W43mH/GBT8bZkidHCmZMW56IBQgJ8ymUBuuOwxVj/2KEOJV\nm6aHiL7Dzy9xxWgEN9P3wrZRX8SPCfBzGAUR+sEYhcMWf/O01EtAoI94m31r\nQ1UgHCJwb5NsPL1hW+FWsRv9YN96hDLzinl82TJsCMW4umfmosnUK3egxNPs\nD8R9m9dkyJJIIbULKkN2B7DiE3IPADXADRzqr/VcO7d/soxlAHdGl9kY0mW9\nkwxl\r\n=HMK7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/node-ponyfill.js", "mocha": {"require": ["chai/register-expect", "chai/register-assert"], "check-leaks": true}, "types": "index.d.ts", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "browser": "dist/browser-ponyfill.js", "gitHead": "c6089dfafc1fd6253b4d204d37c0439eea631cd0", "scripts": {"prepare": "husky install"}, "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "standard": {"env": ["mocha", "browser"], "ignore": ["/dist/", "bundle.js", "test.js", "test.*.js", "api.spec.js", "*.ts"], "globals": ["expect", "assert", "chai"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "lint-staged": {"*.js": ["make build", "standard --fix"]}, "_nodeVersion": "16.13.0", "dependencies": {"node-fetch": "2.6.7"}, "react-native": "dist/react-native-ponyfill.js", "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "chai": "4.3.4", "nock": "13.1.3", "husky": "6.0.0", "mocha": "8.3.2", "yargs": "16.2.0", "rollup": "2.58.0", "semver": "7.3.5", "codecov": "3.8.3", "express": "4.17.1", "webpack": "5.58.2", "open-cli": "6.0.1", "standard": "16.0.4", "typescript": "4.4.4", "@types/chai": "4.2.22", "@types/node": "14.14.37", "body-parser": "1.19.0", "lint-staged": "10.5.4", "serve-index": "1.9.1", "webpack-cli": "4.9.0", "@types/mocha": "8.2.2", "whatwg-fetch": "3.0.0", "@commitlint/cli": "12.0.1", "standard-version": "9.3.1", "rollup-plugin-copy": "3.4.0", "rollup-plugin-terser": "7.0.2", "mocha-headless-chrome": "3.1.0", "cz-conventional-changelog": "3.3.0", "@commitlint/config-conventional": "12.0.1"}, "standard-version": {"skip": {"changelog": true}}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_3.1.5_1642719215429_0.13267096737394102", "host": "s3://npm-registry-packages"}}, "2.2.6": {"name": "cross-fetch", "version": "2.2.6", "keywords": ["fetch", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@2.2.6", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "nyc": {"temp-dir": ".reports/.coverage"}, "dist": {"shasum": "2ef0bb39a24ac034787965c457368a28730e220a", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-2.2.6.tgz", "fileCount": 11, "integrity": "sha512-9JZz+vXCmfKUZ68zAptS7k4Nu8e2qcibe7WVZYps7sAgk5R8GYTc+T1WR0v1rlP9HxgARmOX1UTIJZFytajpNA==", "signatures": [{"sig": "MEUCIQD661wFiexsjueK6VKcFv8vZB2TG9M8nFAYLtzOKa0F8QIgS5vWSiJ15q6A3mymplUOy5WBPQ8UgPeXCIkDBBk4M+U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68087, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiUyiyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqnbw//Q+MpLH/GF8sOFn7kY31DIddks21b+Idwv0SlRMHHgBJ2cz1Q\r\nciEzKJ1JKxodZVKiNGPhPg9lI1Y+X4j11Bza5sUbzpXuKaziiSNRl8Awhj2o\r\nyKh3bG6zdpqFwdfKy3JEJK7bEpMilrlMys+eSJB/tWxwsE/5Na2jxZnAVV/U\r\nF+x1OBdjgXbBWtD6/tmadVwP6WYHkeJ95uFJsVoVsHATKJmw53za22TxfNIV\r\niVH3mh+F73jbS7FFwPzNCKCKRA8BrpNYoM9+8NxNzqLMKkxgqVp05lxLRzl3\r\njHBwlI6+/f8xoXjW3YoX1dJ7z+bNETaKpnL7a4QFabwcIDwSH7SUD+rz0MxJ\r\nQ5OF5v75xlZcjP+zGobaJQjc2BOzPYa5az3yxNmb53i5Po0rGP1Pq/lVpPDz\r\ncsY1+u+bVaIyyPSb5xI6LfbJhpxmCtQtdodTdd0yENoUGupAlQtLKm1Cnhpz\r\nx5+XLxK4MhpmId6StkMZkzq4GZeQtjOapM2zL56ctDRjmQhOkCmeTFkQ/tR+\r\n/93OWUV9pG8rLabyhxPNH1VhzTiKpjgGPaWoUeEYrGNWFCDW/SoS84m6jkKu\r\n/qbbhIyp+uPYtkeag3SDvCQ6H5N99PMKguVpxQeXufRzcn1iBCmAlBab6SS8\r\nDiWwZ1RrzO/db2NuWOVtnyF8rHLX+/Fs1FI=\r\n=8/0X\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/node-ponyfill.js", "husky": {"hooks": {"pre-commit": "npm run -s build && lint-staged"}}, "browser": "dist/browser-ponyfill.js", "gitHead": "bfe5fe2d9c64fe96e69e21f4d71c8260e4dca563", "scripts": {"lint": "standard", "test": "npm run -s test:browser:headless && npm run -s test:node:plain && npm run -s test:node:bundle && npm run -s lint", "build": "rollup -c", "sauce": "./tasks/sauce", "codecov": "nyc report --reporter=text-lcov > coverage.lcov && codecov", "security": "snyk test", "deploy:major": "npm version major && git push --follow-tags", "deploy:minor": "npm version minor && git push --follow-tags", "deploy:patch": "npm version patch && git push --follow-tags", "test:node:plain": "nyc mocha test/node-plain/index.js", "test:node:bundle": "nyc mocha test/node-bundle/index.js", "test:browser:plain": "opn test/browser/index.html", "pretest:node:bundle": "webpack-cli --progress --config test/node-bundle/webpack.config.js", "test:browser:headless": "mocha-headless-chrome -f test/browser-headless/index.html -a no-sandbox -a disable-setuid-sandbox"}, "typings": "index.d.ts", "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "standard": {"env": ["mocha", "browser"], "ignore": ["/dist/", "test/node-bundle/index.js"], "globals": ["expect", "chai", "sinon"]}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "lint-staged": {"*.js": ["standard --fix", "git add"]}, "_nodeVersion": "10.19.0", "dependencies": {"node-fetch": "^2.6.7", "whatwg-fetch": "^2.0.4"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "13.1.0", "ora": "3.0.0", "chai": "4.2.0", "nock": "10.0.1", "snyk": "1.105.0", "husky": "1.1.2", "mocha": "5.2.0", "sinon": "7.0.0", "rollup": "0.66.6", "codecov": "3.1.0", "opn-cli": "3.1.0", "webpack": "4.23.0", "standard": "12.0.1", "lint-staged": "7.3.0", "webpack-cli": "3.1.2", "rollup-plugin-copy": "0.2.3", "rollup-plugin-uglify": "6.0.0", "mocha-headless-chrome": "2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_2.2.6_1649617074653_0.32483129577495196", "host": "s3://npm-registry-packages"}}, "3.1.6": {"name": "cross-fetch", "version": "3.1.6", "keywords": ["fetch", "http", "url", "promise", "async", "await", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@3.1.6", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "nyc": {"temp-dir": ".reports/.coverage"}, "dist": {"shasum": "bae05aa31a4da760969756318feeee6e70f15d6c", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-3.1.6.tgz", "fileCount": 14, "integrity": "sha512-riRvo06crlE8HiqOwIpQhxwdOk4fOeR7FVM/wXoxchFEqMNUjvbs3bfo4OTgMEMHzppd4DxFBDbyySj8Cv781g==", "signatures": [{"sig": "MEUCIGY5cs7dbOwlLaExibocCqSPrj1+OFgfMVFu2RbaMMf0AiEAgEIPVLpPH+f+l2D/OcZempSFRgJNs+A2sjW9obridag=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74797}, "main": "dist/node-ponyfill.js", "mocha": {"require": ["chai/register-expect", "chai/register-assert"], "check-leaks": true}, "types": "index.d.ts", "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "browser": "dist/browser-ponyfill.js", "gitHead": "a2220175e3e2a585487b68cc0893a64076deb562", "scripts": {"commit": "cz", "prepare": "husky install", "prepublishOnly": "rimraf dist && make build"}, "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "standard": {"env": ["mocha", "browser"], "ignore": ["/dist/", "bundle.js", "test.js", "test.*.js", "api.spec.js", "*.ts"], "globals": ["expect", "assert", "chai"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "lint-staged": {"*.js": ["standard --fix"]}, "_nodeVersion": "10.19.0", "dependencies": {"node-fetch": "^2.6.11"}, "react-native": "dist/react-native-ponyfill.js", "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "chai": "4.3.4", "nock": "13.1.3", "husky": "6.0.0", "mocha": "8.3.2", "yargs": "16.2.0", "rimraf": "3.0.2", "rollup": "2.58.0", "semver": "7.3.5", "codecov": "3.8.3", "express": "4.17.1", "webpack": "5.82.1", "open-cli": "6.0.1", "standard": "16.0.4", "commitizen": "4.2.4", "typescript": "4.4.4", "@types/chai": "4.2.22", "@types/node": "14.14.37", "body-parser": "1.19.0", "lint-staged": "10.5.4", "serve-index": "1.9.1", "webpack-cli": "4.9.0", "@types/mocha": "8.2.2", "whatwg-fetch": "3.0.0", "@commitlint/cli": "12.0.1", "standard-version": "9.3.1", "rollup-plugin-copy": "3.4.0", "rollup-plugin-terser": "7.0.2", "mocha-headless-chrome": "4.0.0", "cz-conventional-changelog": "3.3.0", "@commitlint/config-conventional": "12.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_3.1.6_1684065035551_0.4513957529400294", "host": "s3://npm-registry-packages"}}, "4.0.0-alpha.4": {"name": "cross-fetch", "version": "4.0.0-alpha.4", "keywords": ["fetch", "http", "url", "promise", "async", "await", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@4.0.0-alpha.4", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "nyc": {"temp-dir": ".reports/.coverage"}, "dist": {"shasum": "446e2358f9d19b72792749e87e6470a65468bfcf", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-4.0.0-alpha.4.tgz", "fileCount": 13, "integrity": "sha512-jC3zI0uNK75kZ5qOTP/wogDXFsYLukiDSXGqgk87AiY4hJqwjUZwM9+Toljq8imkX/boc/m2qmn7gEXTpJsdbw==", "signatures": [{"sig": "MEUCIHv+YliTNs7OvfrNe/D24Lqb8zO5GBZlwisWKUBzt2t1AiEAohK4OpRVMjmhpKisJ1gqFQXcIjFnzxhBV1LtsFbPZFA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87704}, "main": "dist/node-ponyfill.js", "mocha": {"require": ["chai/register-expect.js", "chai/register-assert.js"], "check-leaks": true}, "types": "index.d.ts", "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "browser": "dist/browser-ponyfill.js", "gitHead": "a6aed569dac4cab9335a57e41a369aa1658a2b2e", "scripts": {"commit": "cz", "prepare": "husky install", "prepublishOnly": "rimraf dist && make dist"}, "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "standard": {"env": ["browser", "mocha", "serviceworker"], "ignore": ["/dist/", "api.spec.js", "bundle.js", "test.js", "*.bundle.js", "*.ts"], "globals": ["expect", "assert", "chai", "<PERSON><PERSON>"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "lint-staged": {"*.js": ["standard --fix"]}, "_nodeVersion": "14.21.3", "dependencies": {"node-fetch": "^2.6.11"}, "react-native": "dist/react-native-ponyfill.js", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "15.1.0", "chai": "4.3.7", "nock": "13.3.0", "husky": "8.0.3", "mocha": "10.2.0", "yargs": "17.7.1", "rimraf": "5.0.0", "rollup": "3.20.7", "semver": "7.5.0", "codecov": "3.8.3", "express": "4.18.2", "webpack": "5.80.0", "standard": "17.0.0", "commitizen": "4.3.0", "typescript": "5.0.4", "@types/chai": "4.3.4", "@types/node": "18.15.13", "body-parser": "1.20.2", "lint-staged": "13.2.1", "serve-index": "1.9.1", "webpack-cli": "5.0.2", "@types/mocha": "10.0.1", "whatwg-fetch": "3.6.2", "@commitlint/cli": "17.6.1", "standard-version": "9.5.0", "rollup-plugin-copy": "3.4.0", "@rollup/plugin-terser": "0.4.1", "mocha-headless-chrome": "4.0.0", "cz-conventional-changelog": "3.3.0", "@commitlint/config-conventional": "17.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_4.0.0-alpha.4_1685724939240_0.4308973647140788", "host": "s3://npm-registry-packages"}}, "4.0.0-alpha.5": {"name": "cross-fetch", "version": "4.0.0-alpha.5", "keywords": ["fetch", "http", "url", "promise", "async", "await", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@4.0.0-alpha.5", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "nyc": {"temp-dir": ".reports/.coverage"}, "dist": {"shasum": "822c644bb7d997bfb762d3ff2ec91a7aa04a2896", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-4.0.0-alpha.5.tgz", "fileCount": 13, "integrity": "sha512-JyOWYhayN5jX+vxmZC0w15BMUEVVmeHCodh5vfJQIFDF9jZZNfdQUFzZkviNtXMh+HwODYFxLnCOb7ZiztAdrw==", "signatures": [{"sig": "MEQCIHEXDjMVrEVp4Ykqi4beMBolAa0olOD2KlGJY4wO5mYnAiAmhIEHMUaVa+Hj/QjAod09WIeHiwQdNQOz+5VKlb7fuA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87704}, "main": "dist/node-ponyfill.js", "mocha": {"require": ["chai/register-expect.js", "chai/register-assert.js"], "check-leaks": true}, "types": "index.d.ts", "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "browser": "dist/browser-ponyfill.js", "gitHead": "f971d4e91935472bb62e10a305d43d035cce13c9", "scripts": {"commit": "cz", "prepare": "husky install", "prepublishOnly": "rimraf dist && make dist"}, "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "standard": {"env": ["browser", "mocha", "serviceworker"], "ignore": ["/dist/", "api.spec.js", "bundle.js", "test.js", "*.bundle.js", "*.ts"], "globals": ["expect", "assert", "chai", "<PERSON><PERSON>"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "lint-staged": {"*.js": ["standard --fix"]}, "_nodeVersion": "14.21.3", "dependencies": {"node-fetch": "^2.6.11"}, "react-native": "dist/react-native-ponyfill.js", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "15.1.0", "chai": "4.3.7", "nock": "13.3.0", "husky": "8.0.3", "mocha": "10.2.0", "yargs": "17.7.1", "rimraf": "5.0.0", "rollup": "3.20.7", "semver": "7.5.0", "codecov": "3.8.3", "express": "4.18.2", "webpack": "5.80.0", "standard": "17.0.0", "commitizen": "4.3.0", "typescript": "5.0.4", "@types/chai": "4.3.4", "@types/node": "18.15.13", "body-parser": "1.20.2", "lint-staged": "13.2.1", "serve-index": "1.9.1", "webpack-cli": "5.0.2", "@types/mocha": "10.0.1", "whatwg-fetch": "3.6.2", "@commitlint/cli": "17.6.1", "standard-version": "9.5.0", "rollup-plugin-copy": "3.4.0", "@rollup/plugin-terser": "0.4.1", "mocha-headless-chrome": "4.0.0", "cz-conventional-changelog": "3.3.0", "@commitlint/config-conventional": "17.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_4.0.0-alpha.5_1685725645128_0.9208673415717121", "host": "s3://npm-registry-packages"}}, "3.1.7-test.0": {"name": "cross-fetch", "version": "3.1.7-test.0", "keywords": ["fetch", "http", "url", "promise", "async", "await", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@3.1.7-test.0", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "nyc": {"temp-dir": ".reports/.coverage"}, "dist": {"shasum": "f2aa1a3f8bbb6ba0816f7f504d56f444e8b90970", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-3.1.7-test.0.tgz", "fileCount": 14, "integrity": "sha512-ofByNhft/IcF/KZZsVKegiSPfDvdLvyIGXESt8buGZKJmbCYd9pBftJTWd1WuaZdgMO+VLhh1gzsqrrvmUAlOQ==", "signatures": [{"sig": "MEUCIQDU41uwmUnPjlnZogwsY/IfShfCGGo0Dhsje7xgK4HfBAIgZ1Q+uc5lsGyt6VZgWiC7t8cOcbFRLNSaXNvzqrNtQM8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74804}, "main": "dist/node-ponyfill.js", "mocha": {"require": ["chai/register-expect", "chai/register-assert"], "check-leaks": true}, "types": "index.d.ts", "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "browser": "dist/browser-ponyfill.js", "gitHead": "eaeca7f52ddbcd0c96b33cffbac28ce62fde4a7e", "scripts": {"commit": "cz", "prepare": "husky install", "prepublishOnly": "rimraf dist && make build"}, "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "standard": {"env": ["mocha", "browser"], "ignore": ["/dist/", "bundle.js", "test.js", "test.*.js", "api.spec.js", "*.ts"], "globals": ["expect", "assert", "chai"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "deprecated": "deprecated", "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "lint-staged": {"*.js": ["standard --fix"]}, "_nodeVersion": "10.24.1", "dependencies": {"node-fetch": "^2.6.11"}, "react-native": "dist/react-native-ponyfill.js", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "15.1.0", "chai": "4.3.4", "nock": "13.1.3", "husky": "6.0.0", "mocha": "8.3.2", "yargs": "16.2.0", "rimraf": "3.0.2", "rollup": "2.58.0", "semver": "7.3.5", "codecov": "3.8.3", "express": "4.17.1", "webpack": "5.82.1", "open-cli": "6.0.1", "standard": "16.0.4", "commitizen": "4.2.4", "typescript": "4.4.4", "@types/chai": "4.2.22", "@types/node": "14.14.37", "body-parser": "1.19.0", "lint-staged": "10.5.4", "serve-index": "1.9.1", "webpack-cli": "4.9.0", "@types/mocha": "8.2.2", "whatwg-fetch": "3.0.0", "@commitlint/cli": "12.0.1", "standard-version": "9.3.1", "rollup-plugin-copy": "3.4.0", "rollup-plugin-terser": "7.0.2", "mocha-headless-chrome": "4.0.0", "cz-conventional-changelog": "3.3.0", "@commitlint/config-conventional": "12.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_3.1.7-test.0_1686485950834_0.43162216671293296", "host": "s3://npm-registry-packages"}}, "3.1.7": {"name": "cross-fetch", "version": "3.1.7", "keywords": ["fetch", "http", "url", "promise", "async", "await", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@3.1.7", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "nyc": {"temp-dir": ".reports/.coverage"}, "dist": {"shasum": "5f5a1e97021f427166fed50f86d48fc70bcd916e", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-3.1.7.tgz", "fileCount": 14, "integrity": "sha512-Ff9FKeIMm0Rx1o8TEV87bTK5M232akt7uSAYrSTU/QA/W6Jj9P+fWn1mxGgl+dwDzpFoAY35OIS2SJXA8WEWKA==", "signatures": [{"sig": "MEUCIB+6vdwzu6a4t0Z6kGlY6BGmAdGDegnA3E+ENh9sXDZrAiEA9jufSQmuszHKSQ+nyFGEpZoQj2CgJzTeJdUhiqMbHwI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74894}, "main": "dist/node-ponyfill.js", "mocha": {"require": ["chai/register-expect", "chai/register-assert"], "check-leaks": true}, "types": "index.d.ts", "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "browser": "dist/browser-ponyfill.js", "gitHead": "cc2663b38c3518f885f88ebc70aa8cc097ab8385", "scripts": {"commit": "cz", "prepare": "husky install", "prepublishOnly": "rimraf dist && make build"}, "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "standard": {"env": ["mocha", "browser"], "ignore": ["/dist/", "bundle.js", "test.js", "test.*.js", "api.spec.js", "*.ts"], "globals": ["expect", "assert", "chai"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "lint-staged": {"*.js": ["standard --fix"]}, "_nodeVersion": "10.24.1", "dependencies": {"node-fetch": "2.6.12"}, "react-native": "dist/react-native-ponyfill.js", "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "chai": "4.3.4", "nock": "13.1.3", "husky": "6.0.0", "mocha": "8.3.2", "yargs": "16.2.0", "rimraf": "3.0.2", "rollup": "2.58.0", "semver": "7.3.5", "codecov": "3.8.3", "express": "4.17.1", "webpack": "5.82.1", "open-cli": "6.0.1", "standard": "16.0.4", "commitizen": "4.2.4", "typescript": "4.4.4", "@types/chai": "4.2.22", "@types/node": "14.14.37", "body-parser": "1.19.0", "lint-staged": "10.5.4", "serve-index": "1.9.1", "webpack-cli": "4.9.0", "@types/mocha": "8.2.2", "whatwg-fetch": "3.0.0", "@commitlint/cli": "12.0.1", "standard-version": "9.3.1", "rollup-plugin-copy": "3.4.0", "rollup-plugin-terser": "7.0.2", "mocha-headless-chrome": "4.0.0", "cz-conventional-changelog": "3.3.0", "@commitlint/config-conventional": "12.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_3.1.7_1688253254075_0.7734701861259885", "host": "s3://npm-registry-packages"}}, "4.0.0-alpha.10": {"name": "cross-fetch", "version": "4.0.0-alpha.10", "keywords": ["fetch", "http", "url", "promise", "async", "await", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@4.0.0-alpha.10", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "nyc": {"temp-dir": ".reports/.coverage"}, "dist": {"shasum": "0d45e1ae62b8338df9c434f29d25ade4b696207b", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-4.0.0-alpha.10.tgz", "fileCount": 13, "integrity": "sha512-5jRmHQqWCx4jZZZO/7+9bRum1FV950wNQ0O8N9kOsYtMnJyS9UNe+C0dq2+wngvMUtwcJnSBCcPP7m8ee/70fg==", "signatures": [{"sig": "MEYCIQDrB/Kr1+OLe2LTZefNB+GHS+OaJ1CtWiUco7uIOAZ2rAIhANwtW+jOLcLvXjUbQpChAwk6fWKvoyjQUYxWG3McH0wh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87704}, "main": "dist/node-ponyfill.js", "mocha": {"require": ["chai/register-expect.js", "chai/register-assert.js"], "check-leaks": true}, "types": "index.d.ts", "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "browser": "dist/browser-ponyfill.js", "gitHead": "68986384ba2353f262bd411ef4de9947ad950bb6", "scripts": {"commit": "cz", "prepare": "husky install", "prepublishOnly": "rimraf dist && make dist"}, "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "standard": {"env": ["browser", "mocha", "serviceworker"], "ignore": ["/dist/", "api.spec.js", "bundle.js", "test.js", "*.bundle.js", "*.ts"], "globals": ["expect", "assert", "chai", "<PERSON><PERSON>"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "lint-staged": {"*.js": ["standard --fix"]}, "_nodeVersion": "14.21.3", "dependencies": {"node-fetch": "2.6.12"}, "react-native": "dist/react-native-ponyfill.js", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "15.1.0", "chai": "4.3.7", "nock": "13.3.0", "husky": "8.0.3", "mocha": "10.2.0", "yargs": "17.7.1", "rimraf": "5.0.0", "rollup": "3.20.7", "semver": "7.5.0", "codecov": "3.8.3", "express": "4.18.2", "webpack": "5.80.0", "standard": "17.0.0", "commitizen": "4.3.0", "typescript": "5.0.4", "@types/chai": "4.3.4", "@types/node": "18.15.13", "body-parser": "1.20.2", "lint-staged": "13.2.1", "serve-index": "1.9.1", "webpack-cli": "5.0.2", "@types/mocha": "10.0.1", "whatwg-fetch": "3.6.2", "@commitlint/cli": "17.6.1", "standard-version": "9.5.0", "rollup-plugin-copy": "3.4.0", "@rollup/plugin-terser": "0.4.1", "mocha-headless-chrome": "4.0.0", "cz-conventional-changelog": "3.3.0", "@commitlint/config-conventional": "17.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_4.0.0-alpha.10_1688303374089_0.5785024053485841", "host": "s3://npm-registry-packages"}}, "3.1.8": {"name": "cross-fetch", "version": "3.1.8", "keywords": ["fetch", "http", "url", "promise", "async", "await", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@3.1.8", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "nyc": {"temp-dir": ".reports/.coverage"}, "dist": {"shasum": "0327eba65fd68a7d119f8fb2bf9334a1a7956f82", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-3.1.8.tgz", "fileCount": 14, "integrity": "sha512-cvA+JwZoU0Xq+h6WkMvAUqPEYy92Obet6UdKLfW60qn99ftItKjB5T+BkyWOFWe2pUyfQ+IJHmpOTznqk1M6Kg==", "signatures": [{"sig": "MEUCIQCjuVCWaF/0YRx6RiSrxsY5HrKnTt9jrS/a3VjPt4EagQIgGWWzgOcfegL4NFU1k13c2G8m0GjgfYygI5oqqe69gg8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75143}, "main": "dist/node-ponyfill.js", "mocha": {"require": ["chai/register-expect", "chai/register-assert"], "check-leaks": true}, "types": "index.d.ts", "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "browser": "dist/browser-ponyfill.js", "gitHead": "0922089bb632037e58564661c2ae9482bbbf4580", "scripts": {"commit": "cz", "prepare": "husky install", "prepublishOnly": "rimraf dist && make build"}, "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "standard": {"env": ["mocha", "browser"], "ignore": ["/dist/", "bundle.js", "test.js", "test.*.js", "api.spec.js", "*.ts"], "globals": ["expect", "assert", "chai"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "lint-staged": {"*.js": ["standard --fix"]}, "_nodeVersion": "10.24.1", "dependencies": {"node-fetch": "^2.6.12"}, "react-native": "dist/react-native-ponyfill.js", "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "chai": "4.3.4", "nock": "13.1.3", "husky": "6.0.0", "mocha": "8.3.2", "yargs": "16.2.0", "rimraf": "3.0.2", "rollup": "2.58.0", "semver": "7.3.5", "codecov": "3.8.3", "express": "4.17.1", "webpack": "5.82.1", "open-cli": "6.0.1", "standard": "16.0.4", "commitizen": "4.2.4", "typescript": "4.4.4", "@types/chai": "4.2.22", "@types/node": "14.14.37", "body-parser": "1.19.0", "lint-staged": "10.5.4", "serve-index": "1.9.1", "webpack-cli": "4.9.0", "@types/mocha": "8.2.2", "whatwg-fetch": "3.0.0", "@commitlint/cli": "12.0.1", "standard-version": "9.3.1", "rollup-plugin-copy": "3.4.0", "rollup-plugin-terser": "7.0.2", "mocha-headless-chrome": "4.0.0", "cz-conventional-changelog": "3.3.0", "@commitlint/config-conventional": "12.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_3.1.8_1688328309551_0.48752235224196117", "host": "s3://npm-registry-packages"}}, "4.0.0-alpha.13": {"name": "cross-fetch", "version": "4.0.0-alpha.13", "keywords": ["fetch", "http", "url", "promise", "async", "await", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@4.0.0-alpha.13", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "nyc": {"temp-dir": ".reports/.coverage"}, "dist": {"shasum": "6b89cc7b893248a079dac1f8cb3de1f6d7cbc2b8", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-4.0.0-alpha.13.tgz", "fileCount": 13, "integrity": "sha512-hWDJHIK8qyXOYb5Jz0/CrTvrD4lqCt/na1ZAswwBVJ0KqQVIsrgwHFlUZ2rYqAQQqgqQhfNweaWbYFsMzCYajg==", "signatures": [{"sig": "MEQCIGGSrF+1xWceUHRo7+t9tDLB/uJ1XiXsK50XYibJrpASAiApj6MCOTXlTc6GGVB5Of78kmxm37gtf71LbZOlO1qlfg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87704}, "main": "dist/node-ponyfill.js", "mocha": {"require": ["chai/register-expect.js", "chai/register-assert.js"], "check-leaks": true}, "types": "index.d.ts", "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "browser": "dist/browser-ponyfill.js", "gitHead": "c4c3663361c1491a9bc9d07427e76ccdb67f5864", "scripts": {"commit": "cz", "prepare": "husky install", "prepublishOnly": "rimraf dist && make dist"}, "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "standard": {"env": ["browser", "mocha", "serviceworker"], "ignore": ["/dist/", "api.spec.js", "bundle.js", "test.js", "*.bundle.js", "*.ts"], "globals": ["expect", "assert", "chai", "<PERSON><PERSON>"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "lint-staged": {"*.js": ["standard --fix"]}, "_nodeVersion": "14.21.3", "dependencies": {"node-fetch": "2.6.12"}, "react-native": "dist/react-native-ponyfill.js", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "15.1.0", "chai": "4.3.7", "nock": "13.3.0", "husky": "8.0.3", "mocha": "10.2.0", "yargs": "17.7.1", "rimraf": "5.0.0", "rollup": "3.20.7", "semver": "7.5.0", "codecov": "3.8.3", "express": "4.18.2", "webpack": "5.80.0", "standard": "17.0.0", "commitizen": "4.3.0", "typescript": "5.0.4", "@types/chai": "4.3.4", "@types/node": "18.15.13", "body-parser": "1.20.2", "lint-staged": "13.2.1", "serve-index": "1.9.1", "webpack-cli": "5.0.2", "@types/mocha": "10.0.1", "whatwg-fetch": "3.6.2", "@commitlint/cli": "17.6.1", "standard-version": "9.5.0", "rollup-plugin-copy": "3.4.0", "@rollup/plugin-terser": "0.4.1", "mocha-headless-chrome": "4.0.0", "cz-conventional-changelog": "3.3.0", "@commitlint/config-conventional": "17.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_4.0.0-alpha.13_1688332463013_0.028103315975801513", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "cross-fetch", "version": "4.0.0", "keywords": ["fetch", "http", "url", "promise", "async", "await", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@4.0.0", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "nyc": {"temp-dir": ".reports/.coverage"}, "dist": {"shasum": "f037aef1580bb3a1a35164ea2a848ba81b445983", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-4.0.0.tgz", "fileCount": 14, "integrity": "sha512-e4a5N8lVvuLgAWgnCrLr2PP0YyDOTHa9H/Rj54dirp61qXnNq46m82bRhNqIA5VccJtWBvPTFRV3TtvHUKPB1g==", "signatures": [{"sig": "MEQCIDfUv6nBv2QBck1WvpaA2YCVFz1i4jhJr0XUIgYlJj7NAiAOEOafEKtDeAX6zlIFmrYFpaaybOuPmsBGyjJZV/eIgg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88103}, "main": "dist/node-ponyfill.js", "mocha": {"require": ["chai/register-expect.js", "chai/register-assert.js"], "check-leaks": true}, "types": "index.d.ts", "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "browser": "dist/browser-ponyfill.js", "gitHead": "748a3126ec3d6d9e87d2d26a3eea49010102932d", "scripts": {"commit": "cz", "prepare": "husky install", "prepublishOnly": "rimraf dist && make dist"}, "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "standard": {"env": ["browser", "mocha", "serviceworker"], "ignore": ["/dist/", "api.spec.js", "bundle.js", "test.js", "*.bundle.js", "*.ts"], "globals": ["expect", "assert", "chai", "<PERSON><PERSON>"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "lint-staged": {"*.js": ["standard --fix"]}, "_nodeVersion": "14.21.3", "dependencies": {"node-fetch": "^2.6.12"}, "react-native": "dist/react-native-ponyfill.js", "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "chai": "4.3.7", "nock": "13.3.1", "husky": "8.0.3", "mocha": "10.2.0", "yargs": "17.7.2", "rimraf": "5.0.1", "rollup": "3.26.0", "semver": "7.5.3", "codecov": "3.8.3", "express": "4.18.2", "webpack": "5.88.1", "standard": "17.1.0", "commitizen": "4.3.0", "typescript": "5.1.6", "@types/chai": "4.3.5", "@types/node": "18.15.13", "body-parser": "1.20.2", "lint-staged": "13.2.3", "serve-index": "1.9.1", "webpack-cli": "5.1.4", "@types/mocha": "10.0.1", "whatwg-fetch": "3.6.2", "@commitlint/cli": "17.6.6", "standard-version": "9.5.0", "rollup-plugin-copy": "3.4.0", "@rollup/plugin-terser": "0.4.3", "mocha-headless-chrome": "4.0.0", "cz-conventional-changelog": "3.3.0", "@commitlint/config-conventional": "17.6.6"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_4.0.0_1688399665904_0.813050772753541", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "cross-fetch", "version": "4.1.0", "keywords": ["fetch", "http", "url", "promise", "async", "await", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cross-fetch@4.1.0", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "homepage": "https://github.com/lquixada/cross-fetch", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "nyc": {"temp-dir": ".reports/.coverage"}, "dist": {"shasum": "8f69355007ee182e47fa692ecbaa37a52e43c3d2", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-4.1.0.tgz", "fileCount": 13, "integrity": "sha512-uKm5PU+MHTootlWEY+mZ4vvXoCn4fLQxT9dSc1sXVMSFkINTJVN8cAQROpwcKm8bJ/c7rgZVIBWzH5T78sNZZw==", "signatures": [{"sig": "MEYCIQC+5p7oa45w/TxWVvORD0J4F079zqgM0GFEXDf2JEXpvgIhAO/dyRNKieGCrqpcwzQJkb5dW/zZjn/YlSVPxntgRQiX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93252}, "main": "dist/node-ponyfill.js", "mocha": {"require": ["chai/register-expect.js", "chai/register-assert.js"], "check-leaks": true}, "types": "index.d.ts", "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "browser": "dist/browser-ponyfill.js", "gitHead": "3415e1f5875711a7b6bed08eb8d9ddfdd79c97d2", "scripts": {"commit": "cz", "prepare": "husky install", "prepublishOnly": "rimraf dist && make dist"}, "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "standard": {"env": ["browser", "mocha", "serviceworker"], "ignore": ["/dist/", "api.spec.js", "bundle.js", "test.js", "*.bundle.js", "*.ts"], "globals": ["expect", "assert", "chai", "<PERSON><PERSON>"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "directories": {}, "lint-staged": {"*.js": ["standard --fix"]}, "_nodeVersion": "20.18.1", "dependencies": {"node-fetch": "^2.7.0"}, "react-native": "dist/react-native-ponyfill.js", "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "chai": "4.3.7", "nock": "13.3.1", "husky": "8.0.3", "mocha": "10.2.0", "yargs": "17.7.2", "rimraf": "5.0.1", "rollup": "3.26.0", "semver": "7.5.3", "codecov": "3.8.3", "express": "4.18.2", "webpack": "5.88.1", "standard": "17.1.0", "commitizen": "4.3.0", "typescript": "5.1.6", "@types/chai": "4.3.5", "@types/node": "18.15.13", "body-parser": "1.20.2", "lint-staged": "13.2.3", "serve-index": "1.9.1", "webpack-cli": "5.1.4", "@types/mocha": "10.0.1", "whatwg-fetch": "3.6.20", "@commitlint/cli": "17.6.6", "standard-version": "9.5.0", "rollup-plugin-copy": "3.4.0", "mocha-headless-chrome": "4.0.0", "rollup-plugin-esbuild": "6.1.1", "cz-conventional-changelog": "3.3.0", "rollup-plugin-esbuild-minify": "1.1.2", "@commitlint/config-conventional": "17.6.6"}, "_npmOperationalInternal": {"tmp": "tmp/cross-fetch_4.1.0_1734793386793_0.4164648153927486", "host": "s3://npm-registry-packages-npm-production"}}, "3.2.0": {"name": "cross-fetch", "version": "3.2.0", "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "homepage": "https://github.com/lquixada/cross-fetch", "main": "dist/node-ponyfill.js", "browser": "dist/browser-ponyfill.js", "react-native": "dist/react-native-ponyfill.js", "types": "index.d.ts", "scripts": {"commit": "cz", "prepare": "husky install", "prepublishOnly": "rimraf dist && make dist"}, "lint-staged": {"*.js": ["standard --fix"]}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "standard": {"env": ["mocha", "browser"], "globals": ["expect", "assert", "chai"], "ignore": ["/dist/", "bundle.js", "test.js", "test.*.js", "api.spec.js", "*.ts"]}, "mocha": {"require": ["chai/register-expect", "chai/register-assert"], "check-leaks": true}, "nyc": {"temp-dir": ".reports/.coverage"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"type": "git", "url": "git+https://github.com/lquixada/cross-fetch.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "dependencies": {"node-fetch": "^2.7.0"}, "devDependencies": {"@commitlint/cli": "12.0.1", "@commitlint/config-conventional": "12.0.1", "@types/chai": "4.2.22", "@types/mocha": "8.2.2", "@types/node": "14.14.37", "body-parser": "1.19.0", "chai": "4.3.4", "codecov": "3.8.3", "commitizen": "4.2.4", "cz-conventional-changelog": "3.3.0", "express": "4.17.1", "husky": "6.0.0", "lint-staged": "10.5.4", "mocha": "8.3.2", "mocha-headless-chrome": "4.0.0", "nock": "13.1.3", "nyc": "15.1.0", "open-cli": "6.0.1", "rimraf": "3.0.2", "rollup": "2.58.0", "rollup-plugin-copy": "3.4.0", "rollup-plugin-terser": "7.0.2", "semver": "7.3.5", "serve-index": "1.9.1", "standard": "16.0.4", "standard-version": "9.3.1", "typescript": "4.4.4", "webpack": "5.82.1", "webpack-cli": "4.9.0", "whatwg-fetch": "3.6.20", "yargs": "16.2.0"}, "keywords": ["fetch", "http", "url", "promise", "async", "await", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "gitHead": "c6f6f83baf5877abf9135dff08f3d17c064e1a08", "readmeFilename": "README.md", "_id": "cross-fetch@3.2.0", "_nodeVersion": "16.20.2", "_npmVersion": "8.19.4", "dist": {"integrity": "sha512-Q+xVJLoGOeIMXZmbUK4HYk+69cQH6LudR0Vu/pRm2YlU/hDV9CiS0gKUMaWY5f2NeUH9C1nV3bsTlCo0FsTV1Q==", "shasum": "34e9192f53bc757d6614304d9e5e6fb4edb782e3", "tarball": "https://registry.npmjs.org/cross-fetch/-/cross-fetch-3.2.0.tgz", "fileCount": 13, "unpackedSize": 93584, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEzYAMc4A1st+cdyWohi3XO4xvldPOx1eNZStiQhks0UAiEAulmeN/MeBsfkShml2Z2MYcgobV/HGCSaURH2VtTPROE="}]}, "_npmUser": {"name": "lquixada", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/cross-fetch_3.2.0_1734794678516_0.4233948952086224"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-09-23T14:11:24.198Z", "modified": "2024-12-21T15:24:38.868Z", "0.0.1": "2017-09-23T14:11:24.198Z", "0.0.2": "2017-09-23T18:52:27.652Z", "0.0.3": "2017-09-27T01:00:16.369Z", "0.0.4": "2017-09-27T01:34:58.203Z", "0.0.5": "2017-09-27T05:10:30.965Z", "0.0.6": "2017-09-30T21:38:59.617Z", "0.0.7": "2017-09-30T22:08:28.018Z", "0.0.8": "2017-10-01T02:02:16.957Z", "1.0.0": "2017-10-29T14:00:53.319Z", "1.1.0": "2017-11-02T13:26:53.246Z", "1.1.1": "2017-11-10T02:17:00.496Z", "2.0.0": "2018-02-28T21:55:40.807Z", "2.1.0": "2018-03-09T14:31:53.877Z", "2.1.1": "2018-04-26T17:51:08.278Z", "2.2.0": "2018-05-10T13:13:08.053Z", "2.2.1": "2018-06-02T15:13:40.401Z", "2.2.2": "2018-06-29T12:16:28.208Z", "2.2.3": "2018-10-27T19:07:39.283Z", "3.0.0": "2018-12-09T01:35:55.114Z", "3.0.1": "2019-02-02T19:51:31.856Z", "3.0.2": "2019-03-27T04:05:29.228Z", "3.0.3": "2019-05-25T02:25:20.205Z", "3.0.4": "2019-06-08T02:09:03.069Z", "3.0.5": "2020-06-14T23:20:40.117Z", "3.0.6": "2020-09-11T02:22:22.415Z", "3.1.0": "2021-03-13T04:59:02.917Z", "3.1.1": "2021-03-18T19:25:44.137Z", "3.1.2": "2021-03-19T23:06:10.148Z", "3.1.3-alpha.4": "2021-03-28T02:00:40.291Z", "3.1.3-alpha.5": "2021-03-28T22:14:40.682Z", "3.1.3-alpha.6": "2021-03-28T22:28:02.865Z", "3.1.3": "2021-03-30T18:32:47.359Z", "3.1.4-alpha.0": "2021-04-01T15:40:02.399Z", "3.1.4": "2021-04-02T23:26:15.575Z", "2.2.4": "2021-07-25T21:02:51.554Z", "2.2.5": "2021-07-25T21:11:29.560Z", "3.2.0-alpha.0": "2021-10-19T19:39:33.404Z", "3.2.0-alpha.1": "2021-10-19T20:03:33.384Z", "3.2.0-alpha.2": "2021-10-19T20:18:18.343Z", "3.1.5": "2022-01-20T22:53:35.742Z", "2.2.6": "2022-04-10T18:57:54.820Z", "3.1.6": "2023-05-14T11:50:35.739Z", "4.0.0-alpha.4": "2023-06-02T16:55:39.585Z", "4.0.0-alpha.5": "2023-06-02T17:07:25.272Z", "3.1.7-test.0": "2023-06-11T12:19:11.038Z", "3.1.7": "2023-07-01T23:14:14.281Z", "4.0.0-alpha.10": "2023-07-02T13:09:34.237Z", "3.1.8": "2023-07-02T20:05:09.752Z", "4.0.0-alpha.13": "2023-07-02T21:14:23.241Z", "4.0.0": "2023-07-03T15:54:26.105Z", "4.1.0": "2024-12-21T15:03:07.027Z", "3.2.0": "2024-12-21T15:24:38.692Z"}, "bugs": {"url": "https://github.com/lquixada/cross-fetch/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/lquixada/cross-fetch", "keywords": ["fetch", "http", "url", "promise", "async", "await", "isomorphic", "universal", "node", "react", "native", "browser", "ponyfill", "whatwg", "xhr", "ajax"], "repository": {"url": "git+https://github.com/lquixada/cross-fetch.git", "type": "git"}, "description": "Universal WHATWG Fetch API for Node, Browsers and React Native", "maintainers": [{"name": "lquixada", "email": "<EMAIL>"}], "readme": "", "readmeFilename": "README.md", "users": {"rajiff": true, "nmrshll": true, "sternelee": true, "tomgao365": true, "cfleschhut": true, "rajkumarit12": true, "parkerproject": true}}