{"_id": "internal-slot", "_rev": "9-82c70fc0346386dc523540438ac64214", "name": "internal-slot", "dist-tags": {"latest": "1.1.0"}, "versions": {"1.0.0": {"name": "internal-slot", "version": "1.0.0", "keywords": ["internal", "slot", "internal slot", "ecmascript", "es", "spec", "private", "data", "private data", "weakmap"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "internal-slot@1.0.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/internal-slot#readme", "bugs": {"url": "https://github.com/ljharb/internal-slot/issues"}, "dist": {"shasum": "ac737889aa55002ac24dff28beaa4efe83898b3c", "tarball": "https://registry.npmjs.org/internal-slot/-/internal-slot-1.0.0.tgz", "fileCount": 10, "integrity": "sha512-O9CC4W792JVqDyJcGzSNMPDWdEHhjMs6iWM/EuVwUc7qk0A75bWf47fC8JguMdJ9bcDXSav6wptwMlDPhKVhmQ==", "signatures": [{"sig": "MEUCIQCgchh5TyqbsifthRAUaZyZrUOqJGJNYvhcnyCdvb9Y1AIgeQBZzhbodmVJSrV+L5i/ZZ2oQqZlOjPBnOlVJXyJ8Bc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21974, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdrUloCRA9TVsSAnZWagAApKAP/R5loBAA5ComhHSFh0iO\ne8M8mAc2ERixb60XctBOfKkScvg0o9aXBBXHbkrNDUdJC5gd/c+jftyf01dB\n1+yDPewcaVQ/74lOh9eIyWrvGuArLS4iuR/g/aOC/SNuNMzLuSekKCQaw+qk\nBVB+ZgGZzVrwS777KlS8Irb2JXPLu3KemoeMmJJgc3jIPNNhDmUM+4q0RLHw\nf3XhMGxby5+VcsQsJ0gVE5UiF2K7azAvFwgZaqBpv4aij00wfWacyOUSRhvW\nOc9s9B+2W2yNjWtRB2Sr4h5KjgCYJRRX8etlohnMATvEWRm+Q+A9X80LzRi+\n/uddo5RMubXjaI1VWd89oO37m/NA1RuMjr7SyrtmI6dGWOhTxw53BT8/i/7e\n2DYEkIGZygDHZsECdbjPLcESc3GXwNOnXytefBGkofYB3XVE031Zrdp71POC\n4xAtHfsF5ycrCLBB0UOLRBk8xfnAdVx7u6F8dY2ktRINaDytAW67ZezgSvDE\nsny8tljcTd+g9qXL84iLeE7GAo9+KEBqN6HxDGuolPVSBAR91UPq/DXQJeRX\nHyc6EhiQFHcQZG766PDT5m8wjAO9PyPb7GAv69IosH4PF/MsAiaba0dyKLs+\nlN07ndNAFIOmFirzpb8awR9zVrUT1/3CgM1rhx4XEh1tOx9kGFYBX1bCkB4w\nmhzN\r\n=wLwB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "gitHead": "1d86b7bf3bb84a8772e9faf1b37bc4883e1bb29d", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "npx aud", "prepublish": "safe-publish-latest", "tests-only": "node test", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/internal-slot.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "ES spec-like internal slots", "directories": {}, "_nodeVersion": "12.12.0", "dependencies": {"has": "^1.0.3", "es-abstract": "^1.16.0"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"tape": "^4.11.0", "eslint": "^6.5.1", "foreach": "^2.0.5", "auto-changelog": "^1.16.1", "object-inspect": "^1.6.0", "safe-publish-latest": "^1.1.3", "@ljharb/eslint-config": "^14.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/internal-slot_1.0.0_1571637607499_0.768492863794743", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "internal-slot", "version": "1.0.1", "keywords": ["internal", "slot", "internal slot", "ecmascript", "es", "spec", "private", "data", "private data", "weakmap"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "internal-slot@1.0.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/internal-slot#readme", "bugs": {"url": "https://github.com/ljharb/internal-slot/issues"}, "dist": {"shasum": "84e940f560e6f1547a979dafbc59ffa9525d8226", "tarball": "https://registry.npmjs.org/internal-slot/-/internal-slot-1.0.1.tgz", "fileCount": 10, "integrity": "sha512-dhZgomoNHuaZI1htOZ9/pQO1V5/Nok5WsLkBPWsgOOiTldgi1TB8ho8OgQtSQTpGxaD1pMvYVks95twX17qgsw==", "signatures": [{"sig": "MEUCIQD6LGxVW2HfASl/Lyy/7KWUf+jvwMsOrmIdDe+/sLZbOgIgJBjp9PIj/YdulDkbQApbilMGU65rb/xmQmiRh0Sb4Sw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21800, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd5MG/CRA9TVsSAnZWagAAu5MP/0xu/xBEae1v4bcIXt0h\nq6OCYlcRkHlJW8YMb9AhTakPL5TgHn/iqTGGqUtiZ0acDx/XtyB3nM4R07nS\n/xLSR/B205yjamFw7HsLLpwk9PxBU5nM9ukqIBIRUsb5bqC2UTwmKeAJ1E4z\nga3TBMZUkTeNvAlabBuORgI0tV1Z+ZR8n64ZJc4rxVkQvTu2lM4xsUM0kZOW\ntu9lgisqTIktbJRN2dmVosbNWinUwgHZCLJw7sATHy/L3ACkpIouxbOnTBzx\n4+q0euqGTvwizxcYZ98We5S/40eGKi3ZVRhH1AiOPqEc3VG88DBoB0tr6LHp\nCb+zr+RJV8sMfwP04m+usbvsUw1rSN2ZDyo2+qNp5XV99Lb20UOWZo4n8ZEm\nutwrRFMhHMZfSdfwX0Y675Pr8d/uXdijF3OrkSy704BZsOLmcU9bm+TlzDoO\nUjJEi2bqr3o9DfWEUpjpbmz60Kkbre+e8F1/eSJbWDdn3zt8OTXim4KuKFb8\nSOjSNl7oPAPsA6YIQHBuf74p6+2hASapLQwcf7wRHPgu5WDlPOKbQdi4E0+k\nMfW7MAcU9x1gymrBzZR2PNsThEelUhmIX06q5fNl63P1pFSYM8AWogITbdhg\nMP8LkJsapQiNAKS+QgpN6TvrKvBjF51nOPzdkmSjaoVsybxhdd0DCoeu0JGH\noQQA\r\n=gcds\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "gitHead": "2df57fb55542977e3a61548b4b9cbb3cda5e40d7", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "npx aud", "prepublish": "safe-publish-latest", "tests-only": "node test", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/internal-slot.git", "type": "git"}, "_npmVersion": "6.13.1", "description": "ES spec-like internal slots", "directories": {}, "_nodeVersion": "13.2.0", "dependencies": {"has": "^1.0.3", "es-abstract": "^1.16.2", "side-channel": "^1.0.1"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"tape": "^4.11.0", "eslint": "^6.7.2", "foreach": "^2.0.5", "auto-changelog": "^1.16.2", "object-inspect": "^1.7.0", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^15.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/internal-slot_1.0.1_1575272895002_0.24797653513859852", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "internal-slot", "version": "1.0.2", "keywords": ["internal", "slot", "internal slot", "ecmascript", "es", "spec", "private", "data", "private data", "weakmap"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "internal-slot@1.0.2", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/internal-slot#readme", "bugs": {"url": "https://github.com/ljharb/internal-slot/issues"}, "dist": {"shasum": "9c2e9fb3cd8e5e4256c6f45fe310067fcfa378a3", "tarball": "https://registry.npmjs.org/internal-slot/-/internal-slot-1.0.2.tgz", "fileCount": 10, "integrity": "sha512-2cQNfwhAfJIkU4KZPkDI+Gj5yNNnbqi40W9Gge6dfnk4TocEVm00B3bdiL+JINrbGJil2TeHvM4rETGzk/f/0g==", "signatures": [{"sig": "MEYCIQCKzc3AVK/+JbyHZbSosubnfoKeEbjU1SVfknazCSggmAIhAMhVx3TrBvrxXQkEl/TiEbj3jBkw7oN/fTxtFLJrbFs9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22208, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd/TY2CRA9TVsSAnZWagAAhAIQAJ9UhllrPMyLmQ/os+SW\n8hZpD1KuYYjtZy30ty8620bStDP+t8jk8NKlJlRl6jeBxrFI/+qOupOAMz9U\nv+v3mD49Hy7i/Ri3TKH2D72N4soG8omJO7hS/YfDL5yk+TFgp087wtMcJcfg\nRyDvvja4pYMTbjMxo1KavwhkXcuYyR06wbH8p9Vl3Ns3WW5qNtueIO8+BLKW\nCAjude8EO3IWK5LQBoi7XnpSsE7jihN3y3L4O60ksGJiQof39JLZNcZgEMJw\ne3grlIpGe39phw1Yfm6mqqL+4MI+T2GUOUlQT3welXTUeJd+OzwOWjCA26TX\nlJRqBpN/re0Z50nvRFY9sHZYCOpE/QIz7fVVW527OYktLqeXLWYuloaGdhV3\nRQRWPtiwE+K/5ZRWeoOpxOSaAxnF/EsA/cO2BKkFx2Ve9BkQigsRm7j1J4ds\nD7pp71gSv6QILcEqXmvkPH7ZK1SWZChXVUuGTlS9U8g4r81tlpeqnxPwYGrk\nHU8gsH94LQklt8PCxsbe4y9l6JrwQXOdfWm0yXayLmd07BzeMsvi7uVWZmLQ\n2wvv/PpL40UIvemFBF9ufkHNE2t0nBrhWB1bmjJga9UUuknUZhTatR1/hcnT\nthJ9etEcrMLCD51FI4STVubGhm61WhV/cS4xDxCbvyC804DtTh7bOy+8Ho83\nPJM3\r\n=u7ko\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "gitHead": "f236165bc5fee44d37ff34fc22f0017e0617fbf4", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "npx aud", "prepublish": "safe-publish-latest", "tests-only": "node test", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/internal-slot.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "ES spec-like internal slots", "directories": {}, "_nodeVersion": "13.5.0", "dependencies": {"has": "^1.0.3", "es-abstract": "^1.17.0-next.1", "side-channel": "^1.0.2"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"tape": "^4.12.0", "eslint": "^6.7.2", "foreach": "^2.0.5", "auto-changelog": "^1.16.2", "object-inspect": "^1.7.0", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^15.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/internal-slot_1.0.2_1576875574402_0.059510546321444924", "host": "s3://npm-registry-packages"}}, "1.0.3": {"name": "internal-slot", "version": "1.0.3", "keywords": ["internal", "slot", "internal slot", "ecmascript", "es", "spec", "private", "data", "private data", "weakmap"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "internal-slot@1.0.3", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/internal-slot#readme", "bugs": {"url": "https://github.com/ljharb/internal-slot/issues"}, "dist": {"shasum": "7347e307deeea2faac2ac6205d4bc7d34967f59c", "tarball": "https://registry.npmjs.org/internal-slot/-/internal-slot-1.0.3.tgz", "fileCount": 11, "integrity": "sha512-O0DB1JC/sPyZl7cIo78n5dR7eUSwwpYPiXRhTzNxZVAMUuB8vlnRFyLxdrVToks6XPLVnFfbzaVd5WLjhgg+vA==", "signatures": [{"sig": "MEYCIQD1EmRX7MCTUCFuJ3z6d2M5hVJny0Wlxl1N1c4Y3LTUEAIhANG2XHcTlFsbAyhCqo2zYFgtMmBxmRwu32Yw4+jSK9vZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15457, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgEHg9CRA9TVsSAnZWagAAcsAP/3KzortAJkvCR5XsD/yH\nzG1wj4sNtnbVEfxr29Fg+rybwC1y8v3bFooUrRjYN42JyBjqFFWNbN3c3UQy\nzU8m9xk8l6qej+TRDtKS1JSwWn6m51m9u/DH5WjgvNL/EcpfANDaC14DE225\nOSYUgpHxe63XUYfFbccQMRIOedGzzqM2Z84irz1dZfmzAKkveIE10d9S1nau\ntpBLW38+n0dx9oTpEF90Fpuuv73tjESe0zKDtKCDKek7btumlCrSkFTqDQC+\nLUOSy+82Bnt8wpWYhuzaK9Is+mEmNpVNXNIxpFPx0WmtBXZo9rqPYKJV/FK8\nxeb9OOQf6qrLEfke6hTqmYMrFzl9Fxl0lsdQQ0cfn/oFJL7rjxYmhaLl0Gye\n1uVUUeOeBQI0f9CRCWsmyQbhSIrMABFgh7R8jVl/WVDCmFUp0mwtojbSGzBI\nx8RxsFRnZr19g9wHOkJ8c0z0gHQ0DHU6ApZpapGhmA4WzgsghBJxaSQqYB4k\npNhL+Cwq8VbBn2aCihP/YGUK+75eRAEJE8WbWLybCbWs14HAwvaiMXs1dbSb\nGZwQyAtD4oNql/jV8LZ0PfStHGJye07BsbwbaiCTZU0C3dzlz8dGf5LFFeYV\n7Or/3zINWdjMAc9AIS7RrCGjoiM8FE0UTeTAm5VU7jlCOnc7/vgOG6oQYKex\nE7XW\r\n=/dem\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "gitHead": "94c565fba1d59f1f5dc510c66e9d3554240c7b14", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "safe-publish-latest", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/internal-slot.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "ES spec-like internal slots", "directories": {}, "_nodeVersion": "14.15.4", "dependencies": {"has": "^1.0.3", "side-channel": "^1.0.4", "get-intrinsic": "^1.1.0"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^1.1.3", "nyc": "^10.3.2", "tape": "^5.1.1", "eslint": "^7.18.0", "foreach": "^2.0.5", "auto-changelog": "^2.2.1", "object-inspect": "^1.9.0", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/internal-slot_1.0.3_1611692093252_0.5701073709385671", "host": "s3://npm-registry-packages"}}, "1.0.4": {"name": "internal-slot", "version": "1.0.4", "keywords": ["internal", "slot", "internal slot", "ecmascript", "es", "spec", "private", "data", "private data", "weakmap"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "internal-slot@1.0.4", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/internal-slot#readme", "bugs": {"url": "https://github.com/ljharb/internal-slot/issues"}, "dist": {"shasum": "8551e7baf74a7a6ba5f749cfb16aa60722f0d6f3", "tarball": "https://registry.npmjs.org/internal-slot/-/internal-slot-1.0.4.tgz", "fileCount": 11, "integrity": "sha512-tA8URYccNzMo94s5MQZgH8NB/XTa6HsOo0MLfXTKKEnHVVdegzaQoFZ7Jp44bdvLvY2waT5dc+j5ICEswhi7UQ==", "signatures": [{"sig": "MEUCIQDuAzLnhyPCmiSKw+8S1ZiVJH48lDZFIh0hA/KBMlIUQgIgN/HdixHchCf4s/Mm90fiWUSSNT/nNLFddpi1z+6BpIM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18899, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmNT3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpQshAAkDmX3YTMbMWRQx46qUhn/r4cWl5h4L6bbQ9tv9/SwCsvOq2k\r\nRNrRYnxBdq15CZ0jfTPbU5MXV5DYSa1HLt00I0324xs3k67KPXrwyjV4dzod\r\ndrWTsuJEsCfhSqw3WREsAKmhCTMxAsCGfDmIspN+2ARrPWlWd/OP3no2fKuZ\r\ngvI4KYKbNAASC6gl2nFhaQXgbDwzjLWaoFOp94EnOtisJXJWBAr11tPdPrIO\r\nbd0Ve5yA/aQvVs6/bkVEop28oijtAFWFCBu10qiuvjCAos9JYSUInt6RtAcL\r\nRWrM3/BZ0QpI92Diyvqln0kf8FDtX2aZbHHsIUha4wn/2xWWLcQi+sf533kZ\r\n24VF1FGTK9nveFcpkNYq2jZE3CAJZB7lKZ34CQqRmC5BRDlotLxjtEGsv8j+\r\n03ke0vvKx7MlPEje2mX+NrG/M4qjPhKDS37VUySJMqTwQOgCOa0+om7582Q7\r\nNBtaNbT/oN447MuN14Pal+LXgljIFKIgwbBCkzKgLJo6IZiU/wsJrgENQGH+\r\nKxjyTltyMOsELhp1czUxzo8Jr3cPeXMYyl2A4xdqAm9ILl4XhosqSWk//kx3\r\nqA7y3lLjG4y5xQX1N7OmdRm+94h+0JnooKiKt440AGoWA1obomlcgnn4Th3E\r\ncBDJ2/VkGEZBxoZlGkbVUCarO/2qZja1Tng=\r\n=ohDS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "gitHead": "36eb820fff0dd696f966ab9ed597ddabdd401b3c", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/internal-slot.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "ES spec-like internal slots", "directories": {}, "_nodeVersion": "19.2.0", "dependencies": {"has": "^1.0.3", "side-channel": "^1.0.4", "get-intrinsic": "^1.1.3"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.1", "nyc": "^10.3.2", "tape": "^5.6.1", "eslint": "=8.8.0", "for-each": "^0.3.3", "npmignore": "^0.3.0", "auto-changelog": "^2.4.0", "object-inspect": "^1.12.2", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/internal-slot_1.0.4_1670960375764_0.8641480894436218", "host": "s3://npm-registry-packages"}}, "1.0.5": {"name": "internal-slot", "version": "1.0.5", "keywords": ["internal", "slot", "internal slot", "ecmascript", "es", "spec", "private", "data", "private data", "weakmap"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "internal-slot@1.0.5", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/internal-slot#readme", "bugs": {"url": "https://github.com/ljharb/internal-slot/issues"}, "dist": {"shasum": "f2a2ee21f668f8627a4667f309dc0f4fb6674986", "tarball": "https://registry.npmjs.org/internal-slot/-/internal-slot-1.0.5.tgz", "fileCount": 11, "integrity": "sha512-Y+R5hJrzs52QCG2laLn4udYVnxsfny9CpOhNhUvk/SSSVyF6T27FzRbF0sroPidSu3X8oEAkOn2K804mjpt6UQ==", "signatures": [{"sig": "MEQCIDGLAnyDYpdNFAYYpfYX4CN0/a93OKr2Cll9p4IwZgHqAiB2LgrwbpXvQHecqsKVeCsHLvCWk9uAeyJv5r5qgREJXw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19453, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5WSeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrT3g/5AFQow2cKTQiYIje5gGiCsyoFzk18NEwhelPGMw4opMN/86z6\r\n2fqgoA3PTnl3EUgC9VH0MWoQ3SN/SDgaMsvIRCao/hP4MNn1JnvbYtMBZ4au\r\nbCc8ffRoIBZFVv6R64BwGCegW2c3RI0/yo9uBI1/xc/P+At9dh4PLRxHWtjA\r\nGhXoHwm3tT7cpipQlpqGWx2EMzjlAZ+n8M6BrmAq5U0k/y0f9AwLVWk+1IKA\r\n7i1F5w23xhW/Sf/PFHiHygYy+EQpxYIz6805Rrz36NVsNZQqh5LQiP8PcCwT\r\nHNAfZrzTUhuHyl4JfJEy31qMJ/VUCzaE4BgDskc0jRTyFtjpMDUR/7OFzjqB\r\nlgav0L8BtEkZj7bMgICgeutFAUMMtyKQFyDZEKTXBemq20mze9tjbTuKWGRN\r\n33ZJwDMM6dux8rtr/t/fS2Gbb4Qb9lfwfP3ovoWfqN/hIe91uJ8sN5+m6o7E\r\nxgrOq1m2Pz8Qq3/uYnYUMs8wlLAeSKbBiR5zjC346zp8I3ZA4m67EudYM9GN\r\nc0hVIawhDcnleRz/UXj/kaoNpCdNtrckWiqtfXw4o/NNpJEFMp028IIoXZN0\r\n8aPH+alNh1eDTtglLNxP/A0ot+cWz/PIk66g5PAFBVRuqhfNPAYJ4ld7W7Eo\r\nLppwzw+cf6pJxz+Q0gHr0isQttZwoqkPU1Y=\r\n=lKm3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "gitHead": "88dca88014ff908f86c1ae292371a07e4ed06368", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/internal-slot.git", "type": "git"}, "_npmVersion": "9.4.0", "description": "ES spec-like internal slots", "directories": {}, "_nodeVersion": "19.6.0", "dependencies": {"has": "^1.0.3", "side-channel": "^1.0.4", "get-intrinsic": "^1.2.0"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.2", "nyc": "^10.3.2", "tape": "^5.6.3", "eslint": "=8.8.0", "for-each": "^0.3.3", "npmignore": "^0.3.0", "auto-changelog": "^2.4.0", "object-inspect": "^1.12.3", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/internal-slot_1.0.5_1675977886546_0.3985005074969261", "host": "s3://npm-registry-packages"}}, "1.0.6": {"name": "internal-slot", "version": "1.0.6", "keywords": ["internal", "slot", "internal slot", "ecmascript", "es", "spec", "private", "data", "private data", "weakmap"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "internal-slot@1.0.6", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/internal-slot#readme", "bugs": {"url": "https://github.com/ljharb/internal-slot/issues"}, "dist": {"shasum": "37e756098c4911c5e912b8edbf71ed3aa116f930", "tarball": "https://registry.npmjs.org/internal-slot/-/internal-slot-1.0.6.tgz", "fileCount": 10, "integrity": "sha512-Xj6dv+PsbtwyPpEflsejS+oIZxmMlV44zAhG479uYu89MsjcYOhCFnNyKrkJrihbsiasQyY0afoCl/9BLR65bg==", "signatures": [{"sig": "MEYCIQCR77z7mAhgvM6iBy/vG3PXrQK9RYiufR1YqulgooQTagIhAPIGyaNVxdKoran39/NxVH6NwopHmtY5L+9307BHnjRK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20150}, "main": "index.js", "engines": {"node": ">= 0.4"}, "gitHead": "e01fb0c564e5d6c932fcbd899c142465bd2b0927", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/internal-slot.git", "type": "git"}, "_npmVersion": "10.2.0", "description": "ES spec-like internal slots", "directories": {}, "_nodeVersion": "21.0.0", "dependencies": {"hasown": "^2.0.0", "side-channel": "^1.0.4", "get-intrinsic": "^1.2.2"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.3", "nyc": "^10.3.2", "tape": "^5.7.2", "eslint": "=8.8.0", "for-each": "^0.3.3", "npmignore": "^0.3.0", "auto-changelog": "^2.4.0", "object-inspect": "^1.13.1", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/internal-slot_1.0.6_1697866652478_0.7415527036095158", "host": "s3://npm-registry-packages"}}, "1.0.7": {"name": "internal-slot", "version": "1.0.7", "keywords": ["internal", "slot", "internal slot", "ecmascript", "es", "spec", "private", "data", "private data", "weakmap"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "internal-slot@1.0.7", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/internal-slot#readme", "bugs": {"url": "https://github.com/ljharb/internal-slot/issues"}, "dist": {"shasum": "c06dcca3ed874249881007b0a5523b172a190802", "tarball": "https://registry.npmjs.org/internal-slot/-/internal-slot-1.0.7.tgz", "fileCount": 10, "integrity": "sha512-NGnrKwXzSms2qUUih/ILZ5JBqNTSa1+ZmP6flaIp6KmSElgE9qdndzS3cqjrDovwFdmwsGsLdeFgB6suw+1e9g==", "signatures": [{"sig": "MEUCIQCv4h9JVF4W4cWUxuFxmxkuPrga9Neg4GvGtLA3DMwmEQIgAUyQaYL2uwnYnycx/vq/fCSyR8+1j2b9CTuvF2hiz5I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20537}, "main": "index.js", "engines": {"node": ">= 0.4"}, "gitHead": "293160b43164b435cd9c980eb3f003c49b88755f", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/internal-slot.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "ES spec-like internal slots", "directories": {}, "_nodeVersion": "21.6.0", "dependencies": {"hasown": "^2.0.0", "es-errors": "^1.3.0", "side-channel": "^1.0.4"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.4", "nyc": "^10.3.2", "tape": "^5.7.4", "eslint": "=8.8.0", "for-each": "^0.3.3", "npmignore": "^0.3.1", "auto-changelog": "^2.4.0", "object-inspect": "^1.13.1", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/internal-slot_1.0.7_1707196383156_0.15654529309440535", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "internal-slot", "version": "1.1.0", "description": "ES spec-like internal slots", "main": "index.js", "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "pretest": "npm run lint", "lint": "eslint --ext=js,mjs .", "postlint": "tsc && attw -P", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "npx npm@'>= 10.2' audit --production"}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/internal-slot.git"}, "keywords": ["internal", "slot", "internal slot", "ecmascript", "es", "spec", "private", "data", "private data", "weakmap"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/internal-slot/issues"}, "homepage": "https://github.com/ljharb/internal-slot#readme", "engines": {"node": ">= 0.4"}, "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/for-each": "^0.3.3", "@types/object-inspect": "^1.13.0", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "eslint": "=8.8.0", "for-each": "^0.3.3", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.3", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "dependencies": {"es-errors": "^1.3.0", "hasown": "^2.0.2", "side-channel": "^1.1.0"}, "auto-changelog": {"output": "CHANGELOG.md", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "publishConfig": {"ignore": [".github/workflows"]}, "_id": "internal-slot@1.1.0", "gitHead": "7e8e584fb6b3389ff6305fcbbb213622d75cf0d9", "types": "./index.d.ts", "_nodeVersion": "23.4.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-4gd7VpWNQNB4UKKCFFVcp1AVv+FMOgs9NKzjHKusc8jTMhd5eL1NqQqOpE0KzMds804/yHlglp3uxgluOqAPLw==", "shasum": "1eac91762947d2f7056bc838d93e13b2e9604961", "tarball": "https://registry.npmjs.org/internal-slot/-/internal-slot-1.1.0.tgz", "fileCount": 13, "unpackedSize": 23009, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD7LudoLEihjXPcPbXbEmECgaq3MoZr11MuwboIDrjneQIgBxASCWwFPsDPKLGio2GMSub/WC9J7PxBISTXvUilFcI="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/internal-slot_1.1.0_1734105749396_0.3045510233914843"}, "_hasShrinkwrap": false}}, "time": {"created": "2019-10-21T06:00:07.498Z", "modified": "2024-12-13T16:02:29.764Z", "1.0.0": "2019-10-21T06:00:07.641Z", "1.0.1": "2019-12-02T07:48:15.146Z", "1.0.2": "2019-12-20T20:59:34.523Z", "1.0.3": "2021-01-26T20:14:53.516Z", "1.0.4": "2022-12-13T19:39:35.926Z", "1.0.5": "2023-02-09T21:24:46.683Z", "1.0.6": "2023-10-21T05:37:32.744Z", "1.0.7": "2024-02-06T05:13:03.391Z", "1.1.0": "2024-12-13T16:02:29.590Z"}, "bugs": {"url": "https://github.com/ljharb/internal-slot/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/ljharb/internal-slot#readme", "keywords": ["internal", "slot", "internal slot", "ecmascript", "es", "spec", "private", "data", "private data", "weakmap"], "repository": {"type": "git", "url": "git+https://github.com/ljharb/internal-slot.git"}, "description": "ES spec-like internal slots", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "readme": "# internal-slot <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![dependency status][deps-svg]][deps-url]\n[![dev dependency status][dev-deps-svg]][dev-deps-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\nTruly private storage, akin to the JS spec’s concept of internal slots.\n\nUses a WeakMap when available; a Map when not; and a regular object in even older engines. Performance and garbage collection behavior will reflect the environment’s capabilities accordingly.\n\n## Example\n\n```js\nvar SLOT = require('internal-slot');\nvar assert = require('assert');\n\nvar o = {};\n\nassert.throws(function () { SLOT.assert(o, 'foo'); });\n\nassert.equal(SLOT.has(o, 'foo'), false);\nassert.equal(SLOT.get(o, 'foo'), undefined);\n\nSLOT.set(o, 'foo', 42);\n\nassert.equal(SLOT.has(o, 'foo'), true);\nassert.equal(SLOT.get(o, 'foo'), 42);\n\nassert.doesNotThrow(function () { SLOT.assert(o, 'foo'); });\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n## Security\n\nPlease email [@ljharb](https://github.com/ljharb) or see https://tidelift.com/security if you have a potential security vulnerability to report.\n\n[package-url]: https://npmjs.org/package/internal-slot\n[npm-version-svg]: https://versionbadg.es/ljharb/internal-slot.svg\n[deps-svg]: https://david-dm.org/ljharb/internal-slot.svg\n[deps-url]: https://david-dm.org/ljharb/internal-slot\n[dev-deps-svg]: https://david-dm.org/ljharb/internal-slot/dev-status.svg\n[dev-deps-url]: https://david-dm.org/ljharb/internal-slot#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/internal-slot.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/internal-slot.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/internal-slot.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=internal-slot\n[codecov-image]: https://codecov.io/gh/ljharb/internal-slot/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/ljharb/internal-slot/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/ljharb/internal-slot\n[actions-url]: https://github.com/ljharb/internal-slot/actions\n", "readmeFilename": "README.md"}