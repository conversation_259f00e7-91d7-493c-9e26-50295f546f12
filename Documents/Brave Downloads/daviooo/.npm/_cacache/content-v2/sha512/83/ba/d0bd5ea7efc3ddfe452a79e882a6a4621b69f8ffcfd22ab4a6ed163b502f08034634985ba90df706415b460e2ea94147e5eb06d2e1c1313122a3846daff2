{"_id": "cacheable-lookup", "_rev": "37-e39b75573d62e69239303fa464efd49b", "name": "cacheable-lookup", "dist-tags": {"latest": "7.0.0"}, "versions": {"0.1.0": {"name": "cacheable-lookup", "version": "0.1.0", "description": "A cacheable dns.lookup(…) that respects the TTL", "main": "index.js", "scripts": {"test": "xo && nyc ava"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/cacheable-lookup.git"}, "keywords": ["dns", "lookup", "cacheable", "ttl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/cacheable-lookup/issues"}, "homepage": "https://github.com/szmarczak/cacheable-lookup#readme", "devDependencies": {"ava": "^1.0.1", "benchmark": "^2.1.4", "coveralls": "^3.0.2", "microtime": "^2.1.8", "nyc": "^13.1.0", "proxyquire": "^2.1.0", "xo": "^0.23.0"}, "dependencies": {"keyv": "^3.1.0"}, "gitHead": "4eedc428c2894c4f3cd4166f36955364c2913be8", "_id": "cacheable-lookup@0.1.0", "_npmVersion": "6.5.0-next.0", "_nodeVersion": "11.6.0", "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "dist": {"integrity": "sha512-0aJ4Iv15HDQNxMBDmGpQYZeQSRmfbWdXFk5EsPxChQTLDlqABt/c5eJN8kOXevfxZGPfE1glKeaLId7wAQ0EMw==", "shasum": "d7d1696bbc3c31159586b512542ae25e636a1cd3", "tarball": "https://registry.npmjs.org/cacheable-lookup/-/cacheable-lookup-0.1.0.tgz", "fileCount": 4, "unpackedSize": 11075, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcQN0qCRA9TVsSAnZWagAAVaUQAIsk7paRW2EisBjs+/9F\ngqLms0LLKAw6ruqhgol8XNZrxbq7n2/HWoSIsB1cDj5wFRiY6b2IL8QafwHF\nF+IMwyOOhYgvztlTII086x3Fc1nHBwV7DoUe51xmQjzqN/Q005I0L6vOg0j0\nHF0vj7jInBe6KFYxpbbt+QIF7DMYAeku678yqXC2zEpbSo7gRlv4GSi6bV3t\njfl+YpIhDuiU7lxo8Ey7DyEk+rKeS+voFfAH3DqqVwcIXNb4SHobLkmcOlTo\n6LYlwWITwlAUgZ/ofudjeKN8lE8YNCpJYBfAJQUzBvcohq9CSDdRiXvBEWco\nkUjpiNLPuJIPvKVS04GXarFGdcKbQwqnq6hFs5FPZ1ptDqXKQBJu2rNKw2yy\nKP1K719aRYzj/hbSe6sOLglYKFuNeWDKPiPXctexLfMy6CcZzu3nWyjN9r4e\nxZaDkbTNPPNg3aZO9LLf3Owftk6BPFjJ6juPRgLbHHvLOY2v6cycrCGdvG10\n5jcYiEyf8Tpc5SUi5oNguT9XCvUOakDIt6YetVf9mDMM4x4T8OJE9nuYihsj\noin66s3y8ySK7GNksK9uTz75nZ3f8Yfw7dHXiWTdsOZqUncE/GXb5SDJkCM7\nX4o6jyOD6u7UmINuzr/UKW5Lu/pezcLD9F1jCTGQ4jXUtjoi/NuAotWhathz\nCe0B\r\n=DTzi\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD4gVow59KhHJELw33DUBxiAZuHDj9+0AEGrUyj0K3aQAIhAKpx0ORrxHvgyB+001DlzAzQNNQ5RbvuX6tc/bh0S8gQ"}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cacheable-lookup_0.1.0_1547754793780_0.23115665683265574"}, "_hasShrinkwrap": false}, "0.2.0": {"name": "cacheable-lookup", "version": "0.2.0", "description": "A cacheable dns.lookup(…) that respects the TTL", "main": "index.js", "scripts": {"test": "xo && nyc ava && tsd"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/cacheable-lookup.git"}, "keywords": ["dns", "lookup", "cacheable", "ttl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/cacheable-lookup/issues"}, "homepage": "https://github.com/szmarczak/cacheable-lookup#readme", "devDependencies": {"@types/keyv": "^3.1.0", "ava": "^1.0.1", "benchmark": "^2.1.4", "coveralls": "^3.0.2", "microtime": "^2.1.8", "nyc": "^13.1.0", "proxyquire": "^2.1.0", "tsd": "^0.7.1", "xo": "^0.23.0"}, "dependencies": {"keyv": "^3.1.0"}, "gitHead": "ebecd3e27b4885f3582376b51b8dcbd124c2c505", "_id": "cacheable-lookup@0.2.0", "_nodeVersion": "11.6.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-sut3+ZL6ULXAznSyEsypCE5fLE+DoqEfjd5qFa2rcRrzomGdIkb/bRuScIwxTnwfDCPLPoeWGDxah+ZvifQnhw==", "shasum": "dc2e48ea20fbc8a480dce47923eac88d124ef7ee", "tarball": "https://registry.npmjs.org/cacheable-lookup/-/cacheable-lookup-0.2.0.tgz", "fileCount": 5, "unpackedSize": 15217, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcolMxCRA9TVsSAnZWagAAPegP/AyZtJ9ydJAUE0x6pkFu\nLLjN+zjBlq/KBUftwxexjESIuCzNnYdVoguF6QmkLocAxRNh6HFT8i42hxwK\nDTuW6I+fBVHHYiOs/crWBFIW+ehjU5+novTgdL77o9XQrVJa3MyDN6l1amGQ\n2/HmFvgsXVlJ9+qAWTgFv0OggQxraYoc+54Y+CR9+vx4tXcWNnQbwmiRFfu8\nRYrDEyRg8kAQd+MKxyHVdD+J0EuOE0BBMGyw3xysFNzEjs+mAjlytqztE4no\nEXTALdxlcJ7N1SjFVakdPz7mzP4en8l/PMzL6XtoCDuuSP1sZ0CnwHXeKLEo\naS/c2RxJYukK8Wz5sUOmfrJygAWVN8voAmMcruG9rdyr1Q23TLPAa8UYu22E\ni9pVrlq2pXo8Sd3Nit/9kqRgearBlskn8Quf6V5wgPk4IMvxpzRxrOLNkUMH\ndCwLkV4iF9Q0yvMiYzEgi3BmO5j5+kl/HT3NYJB2l5CCO0ZeQmkGk4IWOauV\nQzU/nCeNsVm3BeU7iDP4XY+cQswIpG3Y7ssg/huuq49IpOKqV+SMDrWSR5od\niKkFb0HHc7d4ZwYNOcoPq5f9NcS9CmxMXdtnXT7lFyuSkZDH1oEF9AdJM+QI\nJIwxQIWKLKtLYfEB/zgFmnh7z3X1KER/qPznTQSA+nIRQNrzZEPAqq4ZTe3S\nesJe\r\n=ScU/\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCFCMGzXf9WK7S0uICD9Z4HWgHskQr9QGu5at355tVDmgIgR2IZR77ZMOmw8xlzyT7pVKuZkZPTcNK3/3MPnqEnJVQ="}]}, "maintainers": [{"email": "<EMAIL>", "name": "sindresor<PERSON>"}, {"email": "sz.marc<PERSON>@gmail.com", "name": "szmar<PERSON>ak"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cacheable-lookup_0.2.0_1554142000107_0.96803338430275"}, "_hasShrinkwrap": false}, "0.2.1": {"name": "cacheable-lookup", "version": "0.2.1", "description": "A cacheable dns.lookup(…) that respects the TTL", "main": "index.js", "scripts": {"test": "xo && nyc ava && tsd"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/cacheable-lookup.git"}, "keywords": ["dns", "lookup", "cacheable", "ttl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/cacheable-lookup/issues"}, "homepage": "https://github.com/szmarczak/cacheable-lookup#readme", "devDependencies": {"@types/keyv": "^3.1.0", "ava": "^1.0.1", "benchmark": "^2.1.4", "coveralls": "^3.0.2", "microtime": "^2.1.8", "nyc": "^13.1.0", "proxyquire": "^2.1.0", "tsd": "^0.7.1", "xo": "^0.23.0"}, "dependencies": {"keyv": "^3.1.0"}, "gitHead": "d97180944340be962e6cbfee2afe3e4994a89b82", "_id": "cacheable-lookup@0.2.1", "_nodeVersion": "11.6.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-BQ8MRjxJASEq2q+w0SusPU3B054gS278K8sj58QCLMZIso5qG05+MdCdmXxuyVlfvI8h4bPsNOavVUauVCGxrg==", "shasum": "f474ae2c686667d7ea08c43409ad31b2b31b26c2", "tarball": "https://registry.npmjs.org/cacheable-lookup/-/cacheable-lookup-0.2.1.tgz", "fileCount": 5, "unpackedSize": 15217, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcolVrCRA9TVsSAnZWagAAqD4QAJXItZW/AAATsxuXb0cY\nNixsZ5b+Ve0x2H+cX9mivrH5+6wgFcBJLhR+EagM9MTyfjl4dB2apf3NWj6v\nk09jI1bmInW2Ye+JqhRTWUh8Yblhdz40WIGk2233Gs08rLSONpmWO+RGIQil\nS3zssLF7jK2ok4hdI0ifDVEPNc6Sg4oBfQ2QRYzZLYIVF5d1KCzQ6I8VAEpO\nCP0PgCcYgcOw2cH0BqowNhLRBPUAZ4jBkWSUrPscQ1fwdlWzrtlIsvo0pgzG\n7hxZ+AsAYWjWoBLDG6bOgSq/I3Gr6j2rxmm4hoM3+lTqGVe10n3Knm9/wApC\nrIHgrj2w68Sc8NB5G2y3J7T0u05ldTqNFLxYhb05rcokYKKNqX8enkkxcvT4\nOi+zw4qi7s40wTgXxKYKrdO+3oxBSryo5PhLKTyVEb6gnZ3SIWsdPGN0jegO\nlc8XPprzI6fOa6E7+F1pt4dqPYOzWfb6883LuF4iDZZ1CkbbhaeM0rdsQUA5\n9Vv4c+zIH65Wbth7ucHQCPgUDt/vechQ8MXyNvKBCXk0aRgNDsu1yuAL7TqV\n52gqWilqXlvwypJ+HwLLi3h2Hx4wk2fYE8Yb2/EdVcjMk5cunSalt9EAmW6N\n0jfYdsAS6iEP4RPBqs3ZeaZb8/737x0BhMeiFBezXid/i95LQQt7nxQ4AJ6e\n5LWZ\r\n=1mbP\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDJ6Kwrck6sxVL0/rnfHj9Z5hzM0ji5kITMEYPzZDs9ZgIgf1RiOVtmTvFPNydZPuTeZPaw9K66IJGMrDdQGZSGAg0="}]}, "maintainers": [{"email": "<EMAIL>", "name": "sindresor<PERSON>"}, {"email": "sz.marc<PERSON>@gmail.com", "name": "szmar<PERSON>ak"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cacheable-lookup_0.2.1_1554142570242_0.06472356376724542"}, "_hasShrinkwrap": false}, "0.2.2": {"name": "cacheable-lookup", "version": "0.2.2", "description": "A cacheable dns.lookup(…) that respects the TTL", "main": "index.js", "scripts": {"test": "xo && nyc ava && tsd"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/cacheable-lookup.git"}, "keywords": ["dns", "lookup", "cacheable", "ttl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/cacheable-lookup/issues"}, "homepage": "https://github.com/szmarczak/cacheable-lookup#readme", "devDependencies": {"@types/keyv": "^3.1.0", "ava": "^3.0.0", "benchmark": "^2.1.4", "coveralls": "^3.0.2", "nyc": "^15.0.0", "proxyquire": "^2.1.0", "tsd": "^0.11.0", "xo": "^0.25.3"}, "dependencies": {"keyv": "^4.0.0"}, "gitHead": "e1e56e24ef33e160734ecc1f1da9a38bfff20cac", "_id": "cacheable-lookup@0.2.2", "_nodeVersion": "13.0.0", "_npmVersion": "6.12.0", "dist": {"integrity": "sha512-+XR+sYSw3gWyyL/4em004eP3RzisWDaGnBPbGpXl4GFSJSiliomxBU9AvStC6W9ivFuDbEcHwuWCEoSqBrK8Ww==", "shasum": "5db9a480af3c55494e0a8e57c3c5095c3d314dd3", "tarball": "https://registry.npmjs.org/cacheable-lookup/-/cacheable-lookup-0.2.2.tgz", "fileCount": 5, "unpackedSize": 15182, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeK8ZVCRA9TVsSAnZWagAAhX8P/22F6jREtnCTD8vsTgd2\nzL1C4/eK8c1Djus4n2CcJfCeuZrJMNhlaJWMIWkRewVJdGotU3dmkZIKSPAh\nLEHoUcTOFdAssqEpca+nMuwrZD/h6l5ae4+6t0ntLoF6IQSFzuvOXcHTWVr6\nWYJ5J8X2V5SqiKYHgPJu/3oR8Y5o+rGcm5FTYd7BJrbm270w08HeZAzyHw85\neD7vaJP4MkxSCBsf4cM7nVUp6qBvCxokXHPC/WFFvu1LcARL0COCo5LXyk+l\nYojNYin3/JBhGX50Xd6SUXSHA0oodzWEKjCYeEJ6QmiyMbCOvbB3yDp0EjOB\nypSO7hS2VkidBHPWfv2qaphR/BW+VLDV4+lnInybQwxdAJvMUQoyLaggTWhj\nQAdBwul6oABQW+gJ0wAoyHUq1f+2gT6kK9PcXDA52LY4PvCNRJchL8VpEnAF\nsSBI2cRdgyE/pWwa7v53r07b+evEf6hTM6ghbCTv9SuF6nIARakDTAmqVfVd\nYor+tpX8CUruZC4FgxliGP31g1Wsdh263ICi/WHwBmum6sDkMwinljHmwraQ\nkOEKmKVnZV0PyfoYdKE1gG+MjavxTeLNxhvJB8IDBS3uNU/JcANft5ahTu75\nQOA0BocxWnzBTsuO0AOjVFD/zQPZjaklnXT9810jHVBZLCcPuoKEXDAAhOdv\njjpC\r\n=iVhG\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCMoD/6eNos+6X8UgXCrHzAoG1oDIB35vs+mAYgd4GwNgIhAP2QWaw4Cn2HKAMLEfbR3HoCliMqafMudxq0N+bd3Se2"}]}, "maintainers": [{"email": "<EMAIL>", "name": "sindresor<PERSON>"}, {"email": "sz.marc<PERSON>@gmail.com", "name": "szmar<PERSON>ak"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cacheable-lookup_0.2.2_1579927124439_0.3022957951910936"}, "_hasShrinkwrap": false}, "1.0.0": {"name": "cacheable-lookup", "version": "1.0.0", "description": "A cacheable dns.lookup(…) that respects the TTL", "engines": {"node": ">=10"}, "main": "index.js", "scripts": {"test": "xo && nyc ava && tsd"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/cacheable-lookup.git"}, "keywords": ["dns", "lookup", "cacheable", "ttl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/cacheable-lookup/issues"}, "homepage": "https://github.com/szmarczak/cacheable-lookup#readme", "devDependencies": {"@types/keyv": "^3.1.1", "ava": "^3.1.0", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "nyc": "^15.0.0", "proxyquire": "^2.1.3", "tsd": "^0.11.0", "xo": "^0.25.3"}, "dependencies": {"keyv": "^4.0.0"}, "gitHead": "bd8c1bd495a617df397ad9ae23dd01a52d17322e", "_id": "cacheable-lookup@1.0.0", "_nodeVersion": "10.17.0", "_npmVersion": "6.11.3", "dist": {"integrity": "sha512-Te7MkBWBEPUdLjFWLoIu61osWKjrvBdBrSxEso6T9iGLTDPhcA2PI6J++lF/Hmqi5HtZmkKN3q/C7gwa+U/EUg==", "shasum": "ae877296b7b43b720e4a4554c47ec85e74d0932a", "tarball": "https://registry.npmjs.org/cacheable-lookup/-/cacheable-lookup-1.0.0.tgz", "fileCount": 5, "unpackedSize": 16941, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeMbSnCRA9TVsSAnZWagAAtrkP/0XNXBYMIagE1tz5JQTV\nJibzfqgmu7ltQJxnjuo2+mOTFhI99QqfnDvuxoBzXrOF4por+V2IsqJwN49n\nIDhDmMr1e4AppYODJKlIRUsLRBbwHVu4aAAyY0i7U3Y2TR5Gk4qmlC66OpcN\n5LoUNB5JDpeEO1K75eCDHdmKymAfHduzBy3L6r4Z6M71QWhSp234SUaKedgP\nMF9zM5U2SNtSigvcU17g8/axETNL/fFzJ44aL/Pq0paF0iGHVRO1jGvBdfK8\nIofLAmdhgc0NVNt9bC+7Z5e0N5htywLHaf9GSc4O1i8c0T6BM9xPV1F7v2Xq\n1nFYMkVMA/dTeyiGw7+ogP+Ftzf6HmB02EwL6ObHJrg5o4jGyR7SWVqUUm/a\nhB/QNVp0jeEvFZXD3qd0Cr5F7hjXmSHzL9bY31sRndkazMgmZhJnN26PO6Jh\nVMrWHyxEYInATb0F+bxvsbcIgUx6WwA36epvF0U064Gw/jFXyj8d4hIjuqTc\nHOdrDsNdCVa4CCEftv1/wy5fOuMUA8jsxvEW+iBqeogsIGvIY3mMs/JvvOyg\nHb8huD0iBfOhgDQ4bDp/3+whWDf+kG8jql7Sz+FiW946kweruTwORSYFxGN/\n7LNQkYOrVXcrgxC8k1huZKT/i7i8/BN0pQeNdS2IqDEQNay5G2JEz6ayjNbl\nU5P7\r\n=GVOR\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHs0qI1MZJWbj4JXkYrSOtNekCdyIHqBRupLmml6oygMAiAD1zYRmWENrjK0ntzwSJgzg1FOA5UfQfWvEPQFDBBaWA=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "sindresor<PERSON>"}, {"email": "sz.marc<PERSON>@gmail.com", "name": "szmar<PERSON>ak"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cacheable-lookup_1.0.0_1580315814782_0.06950894149502052"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "cacheable-lookup", "version": "2.0.0", "description": "A cacheable dns.lookup(…) that respects the TTL", "engines": {"node": ">=10"}, "main": "index.js", "scripts": {"test": "xo && nyc ava && tsd"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/cacheable-lookup.git"}, "keywords": ["dns", "lookup", "cacheable", "ttl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/cacheable-lookup/issues"}, "homepage": "https://github.com/szmarczak/cacheable-lookup#readme", "devDependencies": {"@types/keyv": "^3.1.1", "ava": "^3.1.0", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "nyc": "^15.0.0", "proxyquire": "^2.1.3", "tsd": "^0.11.0", "xo": "^0.25.3"}, "dependencies": {"keyv": "^4.0.0"}, "gitHead": "f9bac24cc93ce0674692cfe15a69a6c08d79e636", "_id": "cacheable-lookup@2.0.0", "_nodeVersion": "13.7.0", "_npmVersion": "6.13.6", "dist": {"integrity": "sha512-s2piO6LvA7xnL1AR03wuEdSx3BZT3tIJpZ56/lcJwzO/6DTJZlTs7X3lrvPxk6d1PlDe6PrVe2TjlUIZNFglAQ==", "shasum": "33b1e56f17507f5cf9bb46075112d65473fb7713", "tarball": "https://registry.npmjs.org/cacheable-lookup/-/cacheable-lookup-2.0.0.tgz", "fileCount": 5, "unpackedSize": 17201, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeNIWxCRA9TVsSAnZWagAA13oP/Rs/F3oKbH/NDf7NZOd7\njGVZgXJPUbKyzr1/2R2rhTD47vnWnO8EvJ0J2a1xeVo9H5tES3yGJLJEhuMr\ns5vt+HWfD2Hbq81srTf4BdNY3sJktPoRUc320eZGNZT8j/MbeAgLzbMjMGmB\nG25pyj4AwNwbc51MA+YpfcSHokZVQWheFtP6labP2oNSNkbfMZQD73HBFvyh\n7E+34imqgUDcO2IOw3EAyrX8RP8lQdBaMBNBM5EmigGuGLCBZJni1p3bPj2L\n9D1ZwvBXKCa0tA5Lr7/0FMUncWyUjlBrTmvWEZI9vU3IGhPHu4/AiBlAdszf\n72z8HII5+DxvY58LQW7kE1okB9EMtMa8hvt/cABzKezmMOhf+/0TDPdtMOwC\nhvcQtMdQP0JWSgu5di4o6fuegnyhwvFunWb5/I0p6vCZvB0WHYlp6eUqjkWi\nxKprs1TrVaWKPlK3+5NQ6PdVnoyQMH5pDNk39ie6Oyw6c/Xw37OO/oGzNAy9\nwa2EkD43755aFr5m6iR8LbNG6aMKiPm/e0ADLAVL+QvWxgn00SOlrlRyee/u\nZBvkyEKjYVKfHyMdAppkhR+BMKRVqjUHusxjJRP4J1EVne/5OKPkgCQdbY4w\nM8Ay5vOM6013b1/1bhUMI4hSBJGLJsARGELzpo74OIFBi/PmczHYO9V93I64\nLWfu\r\n=19Oc\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCgLHrKeDsKYMep5Ia12kljm4uDXj0ay+kzSt0adZy7ZwIgN1Nn4RRZAZFi8ouiAdJwSDcouykfcBnkkR5P41qkAo8="}]}, "maintainers": [{"email": "<EMAIL>", "name": "sindresor<PERSON>"}, {"email": "sz.marc<PERSON>@gmail.com", "name": "szmar<PERSON>ak"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cacheable-lookup_2.0.0_1580500400917_0.23777133432245812"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "cacheable-lookup", "version": "3.0.0", "description": "A cacheable dns.lookup(…) that respects the TTL", "engines": {"node": ">=10"}, "main": "index.js", "scripts": {"test": "xo && nyc ava && tsd"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/cacheable-lookup.git"}, "keywords": ["dns", "lookup", "cacheable", "ttl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/cacheable-lookup/issues"}, "homepage": "https://github.com/szmarczak/cacheable-lookup#readme", "devDependencies": {"@types/keyv": "^3.1.1", "ava": "^3.1.0", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "nyc": "^15.0.0", "proxyquire": "^2.1.3", "tsd": "^0.11.0", "xo": "^0.25.3"}, "gitHead": "3b30a244b999df3c7ab80fe16ce722dae4ea4a87", "_id": "cacheable-lookup@3.0.0", "_nodeVersion": "13.7.0", "_npmVersion": "6.13.6", "dist": {"integrity": "sha512-DgADvcggDGLxsJ3WqNKoHBCtrE/3YCnAggzCCzQDN90fMUkelBSJdd3g46u+2n+N/kDq9vo9TVBC6NcxxqaGLA==", "shasum": "e76177abbf13e44ecb913669ce398025f8987aff", "tarball": "https://registry.npmjs.org/cacheable-lookup/-/cacheable-lookup-3.0.0.tgz", "fileCount": 5, "unpackedSize": 18031, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeNZ+QCRA9TVsSAnZWagAA3UEP/2+pxOjxfo5EVy5v/NYo\nAs5YpQJnkNTOoFs6f3wjJJQq6WA/EwiwMJ5E0Fpzp/CFXNgbxDki5H7ts1zE\ncUBfhgmc4OhWIiOCPzsv7nFBFohuB4hq33rziO+wfxvJCxU8stOL4RB6+aBh\nyuJqEvjbgjc/Oc8eFQ0NZc8g+bPt4qFd0KlgqbzMLMhuYmPV2jIWW/V4CWB5\ntfL0jxvYG9UyL+4iwpiIk+aPJccfwByENoy5Oj7h19FYF4kwW/XfWfHiS4BV\nJOohnG7UM0oiGw7KgoWnmxgK4hpu38WFYxFd8wa87CjRIAf6C8zuy1oc1iAE\nV49qgd3e4bzk4PeQMBP5u0AiszDl46g6JWoYkDIxpVXz4Ca6XMXAk4R6zBaN\ndSWJqZ4Hef/FOSYNBYWHB07cg5YcuHF7NobSMGjBe7UvaF+UYYEiSVehgQex\nbArecwScfdcEq4LDKYJGYbJwSzTfy/tct1oKedAW400dT8ZBLWO6rLz+ldaW\nrR/ifoFjpfWbZIzh/VfXZdP1/EZb4Qhv6Xa+L5g5UhZd2yHjcTaMQtBJ4iCv\n73pe8rexvrxzuEhKon4Rfh8lK0gsEMpwwX3KNcgq0betta/cyPc9ADA03NGu\n92TiFBxsbAX5h7cYQiJ2Xo6dwN1PHHUJGf8zaLB/ltjeUgL4QlOENESq9ndm\n7i6c\r\n=lRvH\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEPN7NBLeAX48iDCjowPPcKdsKRXCirSpE+GBN2B/h3uAiEA8QSu8B8nTHbV/QsHWZOMrmZTbw3/LTFrt+A+xqbSXfo="}]}, "maintainers": [{"email": "<EMAIL>", "name": "sindresor<PERSON>"}, {"email": "sz.marc<PERSON>@gmail.com", "name": "szmar<PERSON>ak"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cacheable-lookup_3.0.0_1580572559991_0.5438489426454716"}, "_hasShrinkwrap": false}, "3.1.0": {"name": "cacheable-lookup", "version": "3.1.0", "description": "A cacheable dns.lookup(…) that respects the TTL", "engines": {"node": ">=10"}, "main": "index.js", "scripts": {"test": "xo && nyc ava && tsd"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/cacheable-lookup.git"}, "keywords": ["dns", "lookup", "cacheable", "ttl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/cacheable-lookup/issues"}, "homepage": "https://github.com/szmarczak/cacheable-lookup#readme", "devDependencies": {"@types/keyv": "^3.1.1", "ava": "^3.1.0", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "nyc": "^15.0.0", "proxyquire": "^2.1.3", "tsd": "^0.11.0", "xo": "^0.25.3"}, "gitHead": "469caf1ca22de60bf5c52699357d90ff6033b92a", "_id": "cacheable-lookup@3.1.0", "_nodeVersion": "13.7.0", "_npmVersion": "6.13.6", "dist": {"integrity": "sha512-UgBKxaLqV6Odb8SfwC3GaPQ8N2vKAs2o3pkzElmt7X7KkADnrR2P468uC/M2/yIMKaJ80fn96y8HWp+JmnEVHQ==", "shasum": "188474d3f0b26560cd51d56754efd57a76e0a039", "tarball": "https://registry.npmjs.org/cacheable-lookup/-/cacheable-lookup-3.1.0.tgz", "fileCount": 5, "unpackedSize": 18694, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeOZaqCRA9TVsSAnZWagAAjYAP+wVmKlS1lWi1wj8bF6xf\nujt46471/WZ8N7GV/v0Ia5fAB+dgYRiiwa3svHqMlssWC2PAhtHbsEhBqVzI\nkdus/mx1/ypag+EAFg1T3KJa8vZZ04NQVXCMsGC0YvTQ8bGoLPcpk/e5/G05\ncv4ZRWL6VwKIi4rDfkofNBridq7C3DZ/y05AY/P+rY3sLJ7FKNGVZ4kFfZup\noPZiwisSi5jYbKTAHtx0ItOSkUJ0H/BxTwQdnWqsxIRR0nRmiH77BR+AI+uR\nE/fEqsZiKyo1dor7I+uS1G/ruYxGmOW/QY5W0zm40QVUQnjc1g4SAp7quyh0\nAHz0MAFg56uX15t+XBCrJxzwwyg7W39edUJCVAxlLM3JTsETXvLr2XMqoNMq\n1cmI/nx8f5skIMjdAoXAPgN/D3WigN0B+zSWus4Oa87zEozezNVnVtZpE0o7\niUeEeWyrajAYvXPWQVG03MvCfLUC5ItxpqnFUYAKfd6ke49g9zfuWMRJ5Lrv\nDc9JOkaazTj+5LArwFpImSIjOfpUUSt18/sPOtZ2ZPXk67fF3CkT6dfSddrF\nD6jjAclKxj6iaQkYOGS2PzzlWlhOgRt7SUDQJ256KbJ+KFJ2BPGiJucyIx4O\nKdC5AUPjzz6qDobKsATMfJi6kPEVMZNtNMhuHOCzWU6SI4wVHZAzP09XG4G0\not08\r\n=cq6y\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEIkM6CediL6xnqZvlGYC8oGXcLd7VsxfO8OhL3km6ByAiEAnuH+sl1DrjC9rNt8rjlIaFeaZbU790ccv2AfRWXZVSA="}]}, "maintainers": [{"email": "<EMAIL>", "name": "sindresor<PERSON>"}, {"email": "sz.marc<PERSON>@gmail.com", "name": "szmar<PERSON>ak"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cacheable-lookup_3.1.0_1580832426459_0.3096301271742192"}, "_hasShrinkwrap": false}, "3.2.0": {"name": "cacheable-lookup", "version": "3.2.0", "description": "A cacheable dns.lookup(…) that respects the TTL", "engines": {"node": ">=10"}, "main": "index.js", "scripts": {"test": "xo && nyc ava && tsd"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/cacheable-lookup.git"}, "keywords": ["dns", "lookup", "cacheable", "ttl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/cacheable-lookup/issues"}, "homepage": "https://github.com/szmarczak/cacheable-lookup#readme", "devDependencies": {"@types/keyv": "^3.1.1", "ava": "^3.1.0", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "nyc": "^15.0.0", "proxyquire": "^2.1.3", "tsd": "^0.11.0", "xo": "^0.25.3"}, "gitHead": "b7b4ee09e5c3d862972a8b103ebc9f82f02170a4", "_id": "cacheable-lookup@3.2.0", "_nodeVersion": "13.7.0", "_npmVersion": "6.13.6", "dist": {"integrity": "sha512-sMG40M4CYqMVmpkBdW7OzWWm7q7gKhJ+vB/0HTOQHTo+wcGx6uevOBGYdrRS1Vp8PefzcTWfxYMiXidxnJbsvA==", "shasum": "77c3be64c90c2ce676ff04b467972b3c49408441", "tarball": "https://registry.npmjs.org/cacheable-lookup/-/cacheable-lookup-3.2.0.tgz", "fileCount": 5, "unpackedSize": 19525, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeX95ACRA9TVsSAnZWagAAYi0P/0osqeFL6glDTx08xo+3\ndT9/BCwjw10C69f2vwJgRIu6z2HmskvNVfrXuOC6vG/8+PhMtVh++Z+dkYJ8\ncS2qlthIvtz51vNLjT/RYpRJZYXpjm1AkRwN3/4gJAgrt6M71VoA03IM1A2p\nA1rwJSvD9xnu1sCZn48pU+qFPa6ELKCJZrOqypru1tMFgvnfB0iNpLPrdqIH\nWOnbQI3hmMY+O47wU0eh7Z3xjrW7hHmzo+vS8C4FQupDouJIRq2YTyPIs2L4\n9Imomk3cpqUjCEUq9v4MY5rj76qM5lVgYRaP7tYUpvBVBi6o2mURSvyXQvaN\n3LF8GVDTvo5ic2VST8a1gCzg6a1qTC61kqG9lXA4OiBzpRCTWGG76Kd52LOg\nfaW0rXR34hnf4bIX3gIKmNA2t4FwUZSnTAGsWuYWexmYL4fuEtPxHTtC8wQQ\nWjaXgQk1ktqlj6F5jGXoxD489ft8ILr7UXji8MSQcXGMEa9hks70kneMeNQz\nhvwudIch3R7GoqaU9L+s9Lmbmm3B9RDXcdGvhabBEpWvR4Sl9C6shrvJIEPS\nku+bQ7fexGODU6SpumuGWgNKiVzWeJTUFwKNCvALx0ibvgnC6bq1dxCgA5Zw\nYh1OiFdLF9Cx9eD7TJVGTTnkms9iWKkJVsbU1vPL4yC8S5ayHA6eaAZHMqxy\ne2oJ\r\n=NOO7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBgZyPm5Sd74YjsIrANwjYTPh5nL3zDpEMzuiQNH2jPUAiEA31fYWmetOfOYseiiZ71GJyBQkzIU/W7c874Z9r6kfms="}]}, "maintainers": [{"email": "<EMAIL>", "name": "sindresor<PERSON>"}, {"email": "sz.marc<PERSON>@gmail.com", "name": "szmar<PERSON>ak"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cacheable-lookup_3.2.0_1583341119846_0.05116109812516112"}, "_hasShrinkwrap": false}, "3.2.1": {"name": "cacheable-lookup", "version": "3.2.1", "description": "A cacheable dns.lookup(…) that respects the TTL", "engines": {"node": ">=10"}, "main": "index.js", "scripts": {"test": "xo && nyc ava && tsd"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/cacheable-lookup.git"}, "keywords": ["dns", "lookup", "cacheable", "ttl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/cacheable-lookup/issues"}, "homepage": "https://github.com/szmarczak/cacheable-lookup#readme", "devDependencies": {"@types/keyv": "^3.1.1", "ava": "^3.1.0", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "nyc": "^15.0.0", "proxyquire": "^2.1.3", "tsd": "^0.11.0", "xo": "^0.25.3"}, "gitHead": "bb1e839194dd996d22928fdfb9485c3ce266e429", "_id": "cacheable-lookup@3.2.1", "_nodeVersion": "13.7.0", "_npmVersion": "6.13.6", "dist": {"integrity": "sha512-DQiFHhlgG5/FHpD9KEim2OnpK4wtGnDodfRz2j+c3yyoxCJm16GNS+6R9KvBpfRuUgkc3U5rp2FIvYjrLYfovA==", "shasum": "608c9c5340f0d44cd9d981336933ace4431c4111", "tarball": "https://registry.npmjs.org/cacheable-lookup/-/cacheable-lookup-3.2.1.tgz", "fileCount": 6, "unpackedSize": 20898, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeX+IACRA9TVsSAnZWagAAW9gP/jP6kxUDaCw1YvNx6KtX\nW/oyC6UVIeg+DGWW2yLhymlli58ZOBLVjA9Va5gBMjA/7N5pBoj/V5Obl5XU\nWK5u5pKCA74AuB/EpeJiI7m3X5F8zIV8qDuAV4S+q9lDoQrWyXl65KEB/tdN\nMvQboBYEt60N1Cn0TgD4PdrxDczYq+1DcJTwBA2QcFv7wbjAcyncJpA1xPKe\n8BjRN+H+wcPUmVaHsA7e60Y2iMuS8qWvJkPCE8fi9eAiiFkmDkG+fBGbnJMo\nU9Ov55631BJGWqSmKyjKvqbbFHM7ZavsbRuw/7DbkRpzxrqFYE+cG/9rHATO\njxOhqkCV42AWBiSzhLBAm/i72F6rLo4BR6vN2eWBYPO97dUFvTU4Vq22d21z\nOZlBTv+JchtUgX+R8oDsr2xLrC5PSyWKmiApxvo1SOz0SZJWbN2zU3oX3Zca\n0prukZkkShDQ4dIsOI0AxeaPoXP9pAd0ZQTEWcSY4TM1uFYM8Gf9kw+KBHjH\nZJ5aLNRZ9Hh0CziKjSeffBqnT6rkBolxOvclGa3N41PbQ1NG4sfe9/SFOuFg\nxmfwZ6Uk2T/FGRHtJ1Ki48TNevAxj8KUM3EtjWk1H1NxcfrPMmpyPAb3T+rj\nyGq+Ec1MDZW4DsBh+Lc/ZERs2Xf+niCPlc17k0e1d+qdjeZH/AhlsEU3q3mx\nOXC3\r\n=hAKA\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDJK4KwzlBmDb66NzoeK0r0stszOsRzmxPfJvVUm56X2AIgUL12/zu/GSQV0Vp8+8c7cZJpec8b30dq6Sev3/LpV1c="}]}, "maintainers": [{"email": "<EMAIL>", "name": "sindresor<PERSON>"}, {"email": "sz.marc<PERSON>@gmail.com", "name": "szmar<PERSON>ak"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cacheable-lookup_3.2.1_1583342080096_0.5672910514113534"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "cacheable-lookup", "version": "4.0.0", "description": "A cacheable dns.lookup(…) that respects the TTL", "engines": {"node": ">=10"}, "main": "source/index.js", "types": "source/index.d.ts", "scripts": {"test": "xo && nyc ava"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/cacheable-lookup.git"}, "keywords": ["dns", "lookup", "cacheable", "ttl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/cacheable-lookup/issues"}, "homepage": "https://github.com/szmarczak/cacheable-lookup#readme", "devDependencies": {"@types/keyv": "^3.1.1", "ava": "^3.1.0", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "nyc": "^15.0.0", "proxyquire": "^2.1.3", "tsd": "^0.11.0", "xo": "^0.25.3"}, "gitHead": "75d7c77bd91dca426ecc0fea0808780fbaa0c1ac", "_id": "cacheable-lookup@4.0.0", "_nodeVersion": "13.11.0", "_npmVersion": "6.13.7", "dist": {"integrity": "sha512-lUHKgGNV4tGZJCAPufxlhYwTxmTKkn4a2CeX/YkuEGnZkqeKf9IYlVbDwFcIbQf4pWFccMnEsgoS3gTgj/pyPA==", "shasum": "cc07d1627fb9c3b4d024bf5d1fe8edbca8f1efdd", "tarball": "https://registry.npmjs.org/cacheable-lookup/-/cacheable-lookup-4.0.0.tgz", "fileCount": 7, "unpackedSize": 22452, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJebL7kCRA9TVsSAnZWagAArl4P/iiOKG74IxBK3hBNW8tU\nh8ApHxzaiVp2TnMYxY+m4x4G8gy9qN0awYxmi5oWsF7VG5A+olGuqCaGL9gW\nbvZXZqmm9rEensfsAlv5BFNjkbInl3aAER9dM8zLyJjkixnkpvAPkYga7g2h\noUjC2y5FaWdITBoJnC4hb8afDPVhbiAuIyPuL8UMzomLl9sEwtRVpMB91zs5\naNqHLFqWCZXLL53hItXs0r3o/mL/Ui69Y+GsRRxb7IQ73yh/3h7Y/Z9hcdRM\noV09LLq7YRzFK31hlO6nMJYxdDmR7XWDnRcd55/IJQBxey+sQdhAaqx+zigU\nbb2Uxoa/tz5wNaTgBRNVoL/r/a4/YwRqm2b2dBL9kXvTmpWEQx4aP2+CWGUY\nRNy35t/mbcsVMxHmXeMrBYjd7MTU5M80QosSpyd3zePs9kePPFV0OD4warVQ\nv6VlC4yJcos2tCQfXEXly9qm8xVGsICtkeScEhtIXlV/V2A2bZfeqt7Td2JD\naBGW15hBMe1aIq0ihY//naL4NGa7lDg3ZFYWTqoDvi/3dyL/hXXwxnebm0Su\nVJ7UdYp9+VQQqTwMKD04BKpGwVG0+7KQHbjYm3pke3DdHT4eoQxXEao0lqqZ\nCVIJxpWlBF4YNrZONEuxkqNOKIAzdrd7TpEmYqT7nxoMTymcE8LAYRN7WpED\njTut\r\n=BIj2\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGzCF2SgP1MuTSEQ/Pa3RgnKpJq9e7uoX+mXifzREdXRAiEA48rIohmRd1KAAmrpiuu/P4tNjnuyjf2sfsruJo9kIQM="}]}, "maintainers": [{"email": "<EMAIL>", "name": "sindresor<PERSON>"}, {"email": "sz.marc<PERSON>@gmail.com", "name": "szmar<PERSON>ak"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cacheable-lookup_4.0.0_1584185059932_0.03796973477926646"}, "_hasShrinkwrap": false}, "4.0.1": {"name": "cacheable-lookup", "version": "4.0.1", "description": "A cacheable dns.lookup(…) that respects the TTL", "engines": {"node": ">=10"}, "main": "source/index.js", "types": "index.d.ts", "scripts": {"test": "xo && nyc ava && tsd"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/cacheable-lookup.git"}, "keywords": ["dns", "lookup", "cacheable", "ttl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/cacheable-lookup/issues"}, "homepage": "https://github.com/szmarczak/cacheable-lookup#readme", "devDependencies": {"@types/keyv": "^3.1.1", "ava": "^3.1.0", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "nyc": "^15.0.0", "proxyquire": "^2.1.3", "tsd": "^0.11.0", "xo": "^0.25.3"}, "gitHead": "4e64e777d18abc1e6fa0417faa35f0ef0868a24a", "_id": "cacheable-lookup@4.0.1", "_nodeVersion": "13.11.0", "_npmVersion": "6.13.7", "dist": {"integrity": "sha512-OO2X3s5lcv0bi2koO1lQIOm7lottL6E/irDQWSGwD9+PEfJfI+pce6/MvICb4XqO7X0sN+Vm6oPfzL/hfO9qHg==", "shasum": "9f7e16a283b04dfbad4c312f16e7dcc96bb6ae6b", "tarball": "https://registry.npmjs.org/cacheable-lookup/-/cacheable-lookup-4.0.1.tgz", "fileCount": 6, "unpackedSize": 21283, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJebNyLCRA9TVsSAnZWagAA1qAP/1lqB0nPnkLfRspwk3Aa\nZ4SgBk4qRmEeZItFnrkUBIu5ht8BQJrnQlSAx5+AWvE4HM/1+jsfjMzlKRqf\nKgRONf6hJTP4cl3r89skeTyiTcJJTsyMUfD00KQIpujpnJ6Xfbtm7tuFFA46\nEY5sKiD5Tf0It91X+hN1fASz45FfYwdXPdaZLNv4KCez8r7eMMIiDmbTgv/m\nq8nCGgsNrZfjKPqpK2exEUl2+59dzNzCRcxN2PzzPHsRbfEP6P5dcHBuiBPF\nSit49pt7Cq4hiZNSxe108cFAEcq3xWUCVYQrc/YtLmeccle0ilA/MlhTwiEG\nUUnL1Ns1YKvmtDSzGXa33Paotj+lzb8YzfpL6UT/Xo/p0ZRjOlBQDO/ecTMz\nfX9ZiBelgWb9dHll7S5ilyl/S8yRPBGEV+CaCbqDAfGMBrQUM3FWLI2JuR48\nGWNRlysm04mkw43uI/KRJwzIuDvxgLvvoDY1ugal/PrYH6RUCru8xH7kkTvH\nyHcgmnLRI7SmBawzqVinaprt6IS3nvZmoqruZLliU9S3pvsfuaIisQZFvmhJ\n0Bs27V+bgQTknwbF2zsziba2xW9NGiuYOZpYC4n8Hu5Q4gnHadsxGL101HCb\nExizslc4lOZa63lga1LW0QTQJJG8yfZU/uZ1C8JuLND+0SBCHFkdsiQU8LkZ\nNSqH\r\n=4kpo\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFzFN9bAkh/Ot1zF0ve002WAObZjwhp2fV9s7QFADUpVAiADAzoA2fqLP43lhM1AD2hLMPbvB3uG3//w6HwXXljGgg=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "sindresor<PERSON>"}, {"email": "sz.marc<PERSON>@gmail.com", "name": "szmar<PERSON>ak"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cacheable-lookup_4.0.1_1584192651475_0.260260229072119"}, "_hasShrinkwrap": false}, "4.1.0": {"name": "cacheable-lookup", "version": "4.1.0", "description": "A cacheable dns.lookup(…) that respects the TTL", "engines": {"node": ">=10"}, "main": "source/index.js", "types": "index.d.ts", "scripts": {"test": "xo && nyc ava && tsd"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/cacheable-lookup.git"}, "keywords": ["dns", "lookup", "cacheable", "ttl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/cacheable-lookup/issues"}, "homepage": "https://github.com/szmarczak/cacheable-lookup#readme", "devDependencies": {"@types/keyv": "^3.1.1", "ava": "^3.1.0", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "keyv": "^4.0.0", "nyc": "^15.0.0", "proxyquire": "^2.1.3", "tsd": "^0.11.0", "xo": "^0.25.3"}, "gitHead": "55381fcd7b7a29dbb5c193629ed0a7fab8ffe522", "_id": "cacheable-lookup@4.1.0", "_nodeVersion": "13.8.0", "_npmVersion": "6.13.6", "dist": {"integrity": "sha512-MRcD7MDkq9HVHyOsEoZRU6W6Vw2EnGrHJ55go8x985pxvwFN45obXyUauAsAPFpA9z5Tzptvi6I3J/rjG2gVQA==", "shasum": "80398ebd570feba0c7a5bc6941638b9e13088540", "tarball": "https://registry.npmjs.org/cacheable-lookup/-/cacheable-lookup-4.1.0.tgz", "fileCount": 6, "unpackedSize": 22209, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJebT/6CRA9TVsSAnZWagAAu3IQAJxIkk4tQf/1aWbfMl4S\nqPVgHKnLC2nF0u+4wYG9mRO4DAr66vXMKtitTQihq0AFMwYYeNXDwzxBgXat\nG0+CegiIejbblZOfbMA41R1sUZVMUt4GZgFnyzsd8vGx/YVuxC4YuU2iVMEl\n/b/cz0K0QHfL6RBX0RB0w34LoOcO7MmOrF2VgM7MysUBmsRf96xOeRxDIlMK\nnplVN+5ZeXNEsDqA6Ay5JmtBprKdp1GFNqx5lXgXKYYk6eMfR2+RTimqNHkx\npgwMIigCMbZp92H5TT/xWHMvLNPDhfLJ5g3/tekhY2lYeHPFEhqpCUKN7Fvs\nWew6nzgvIil/UQGYeXtTZ6ijCxGUadswuCM7pqQ6n+vmzWmjaK1ep4ZEdKfO\neVV+4WlJPjUNSO8Oc5AkUaUsffCGcSw3S3zdxP7cm/pNpt+FdS4Cw9Gi3Fl9\nJI46mDzLnGgKrr1h72ZNQ8nGU7N9RQuHT0QEPcJnDKMTuM/upioz2+wJucFZ\nMalJhYUI9SMt82LBhwmhaW1+32yC3jJsQ3mENtwikMXer1mmYXlbdum2GCWj\n32QTwNczQ1iGsp6O3h/RlinSsQFJhJ7ksdVhMQttbQNmZBvWTsEF0uUfGgdJ\nPecEDz0aDWW/oZho+8rawxX1G+kgrvRrUXKsa3emHy0lj7EFtX7+nzrMxISq\ncGHC\r\n=kUkc\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAWtr4TqhXwUesgMdb3cPhpK+4ejuRTcAY+NuntYi3LmAiEAylGrFwu/01Dk+iIoiT6x6VXJ+8BoUw/a+Yb5o8GrrRQ="}]}, "maintainers": [{"email": "<EMAIL>", "name": "sindresor<PERSON>"}, {"email": "sz.marc<PERSON>@gmail.com", "name": "szmar<PERSON>ak"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cacheable-lookup_4.1.0_1584218106503_0.011769615797013744"}, "_hasShrinkwrap": false}, "4.1.1": {"name": "cacheable-lookup", "version": "4.1.1", "description": "A cacheable dns.lookup(…) that respects the TTL", "engines": {"node": ">=10"}, "main": "source/index.js", "types": "index.d.ts", "scripts": {"test": "xo && nyc ava && tsd"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/cacheable-lookup.git"}, "keywords": ["dns", "lookup", "cacheable", "ttl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/cacheable-lookup/issues"}, "homepage": "https://github.com/szmarczak/cacheable-lookup#readme", "devDependencies": {"@types/keyv": "^3.1.1", "ava": "^3.1.0", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "keyv": "^4.0.0", "nyc": "^15.0.0", "proxyquire": "^2.1.3", "tsd": "^0.11.0", "xo": "^0.25.3"}, "gitHead": "a292a835c9bf199eb04113590762f6dc9aeff4f7", "_id": "cacheable-lookup@4.1.1", "_nodeVersion": "13.8.0", "_npmVersion": "6.13.6", "dist": {"integrity": "sha512-2Grf8R8PpzhjCK11cfBezL2I1VmABWh1o/Wtn3ctdYzvAm4GfQkkMPxMSnbWxCisiV26JOuNdogid2FLjYxJ4w==", "shasum": "f5e879489ce2cc3b2eb2c6e438458e6cadd39bfc", "tarball": "https://registry.npmjs.org/cacheable-lookup/-/cacheable-lookup-4.1.1.tgz", "fileCount": 6, "unpackedSize": 22706, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJebpPyCRA9TVsSAnZWagAA9dwP/368pOKBI66urxiLyKNV\nRGpw8OrxJPl4TXd8Xljuo3XiNzEuesdlpS3flQogDRjUTTk0CP+bgL2tvsPS\ns5nHmQqc5/FJUXzUUOAhMsfnqOLcx0d8xrnDYtxMAjlvL76Fj+YoxPu97eWg\ndUqCjWLVrm52KDUIpwNwKbODDLsi6c8ntzqaMXf82KevdUIwmv8xjJr0NrGN\ncKlnhyKDoilXWKg2u0FRFBXrRkWkrTpXo1slv2aiTmmesCZJ5xphPhynK+6I\nKDVC4pDzE/QbywnwF/zuWf7oH0b2teqZwzX7ptpVWEiotBEzVWgVGduPWVa+\ndJAYAPjLJey4ybc0vN5Acc+49zhESf5S1rDSJ5+WruI6mvgWBmQaMDFMDhLl\nNLY3gl0MoVhiTbZzSLpnTvzwhg+w+WmtCgewbmOeJwOzX5WZ89NOh6+jRLkZ\n1c0hF6DzQRJWzIO4LdGiKz67+Hm2I2W0LldJMGeWwLLrOy5gbilMItO15VTJ\nOQ8+AaMCLAHxtFFrOSNVrSGs/vnec27NLyB2VBy2JVF/GqCStSIcho/mDBMO\nGwLXTlmGV/PPLoPMNbae5Y9p0iZpspBUCv3qufaSxc/9jWR245K+jz9Pabtf\nTfZeBmkDwzdzwfdDDJLFFywKtfC/L0+F1kPpO3uzBbk0BkWgjABwE+hckNPy\n1+Ml\r\n=n8GQ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCZUGFL1qPgVdXAmv4oGR62O4OUkedpi3FwS4XvxWKuaQIgGccB8HtsPTkJQQTzjoxZISIeuma0WN5CRoVYUo7gzR0="}]}, "maintainers": [{"email": "<EMAIL>", "name": "sindresor<PERSON>"}, {"email": "sz.marc<PERSON>@gmail.com", "name": "szmar<PERSON>ak"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cacheable-lookup_4.1.1_1584305137878_0.9799931414849918"}, "_hasShrinkwrap": false}, "4.1.2": {"name": "cacheable-lookup", "version": "4.1.2", "description": "A cacheable dns.lookup(…) that respects the TTL", "engines": {"node": ">=10"}, "main": "source/index.js", "types": "index.d.ts", "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text ava && tsd"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/cacheable-lookup.git"}, "keywords": ["dns", "lookup", "cacheable", "ttl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/cacheable-lookup/issues"}, "homepage": "https://github.com/szmarczak/cacheable-lookup#readme", "devDependencies": {"@types/keyv": "^3.1.1", "ava": "^3.1.0", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "keyv": "^4.0.0", "nyc": "^15.0.0", "proxyquire": "^2.1.3", "tsd": "^0.11.0", "xo": "^0.25.3"}, "gitHead": "44d2816731a94b7cad4d249d2b2f4d7f792c06eb", "_id": "cacheable-lookup@4.1.2", "_nodeVersion": "12.10.0", "_npmVersion": "6.10.3", "dist": {"integrity": "sha512-qwnOjdDFv4VNVjd5g+aKg7JuRS+q6bD8t9nk7l0tGTCPfLRX2jOmCTHFYc+leooxrtSKyOFzZz5MJQPUeYYkXA==", "shasum": "07c90b15910d4a5be6f3fa1a0ce73dfb9c192dc9", "tarball": "https://registry.npmjs.org/cacheable-lookup/-/cacheable-lookup-4.1.2.tgz", "fileCount": 6, "unpackedSize": 22720, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJedJ3ECRA9TVsSAnZWagAAVcMP/AhIS1EdAdHF8u6pfn2t\neQiaBgPPLl4vm/2J9D3jcejSBNjpbPTylea5X+QblTTgELgiwULQhoY4taEZ\npBW+Mx0adKFT4mPknm4/7wa4KylG3YbXJ/fwRMjP9aiIe+z97FY4LfC46R1f\nMLeKcQC4ERaTIkRf3IGFLtdBABRoLZvyNU3ssDxyOxKWm04ilisn0nD68Zk/\nz1D0n3wwHwxvBIoVKiUv4EuyA79e8N4o2sTHj7GAU6immWe8VRgYLcHl47O7\nClj8ZTQeUE1egjxeQnBJXeZZYJkkTBglQivxRmXzoA3KzycIuMg8/dtvpQRw\nkBP11egTYR4DkL51lRIAw2OWdDW4QAwdHWLJhUOA0Ebo3Au8JMFcIfsPGcza\n3cWqxJv3oO0fjbWvXhGwl42xx3rlPx3o3OomlR8MeoHRTvcFQj6xxYzodx6a\nn6u5Y0X/+fFwaCpMR396xKr/1G9mNI9uM0zkSnXZwtUUnNoqVeZya2frN0Uj\nDGLZdszUEeERsAzRVjLRVmiG2w75JEP14DwFsUvwKIn7QQwcVX9UqnRoyV5u\nZI5frV7kesUo33CKl1iAbmtDBHXi7w7/Qg3w94MPq/X2/PJIQW2MtU1+GEp+\nequLLmP4yWk8pGxF+2Ebhet5KMXcIsrjrCbAwoEkayiufEpmSYci09Xj+28y\nxxrw\r\n=NyXt\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEV9OJwkXyskT7+px9Vvsr/CoV30b7IPC4JHfz/BkSBoAiEA4r+D9mPmb8mMmyyrM2gq+Ar89NjhOtsSlpy9b7bMIiU="}]}, "maintainers": [{"email": "<EMAIL>", "name": "sindresor<PERSON>"}, {"email": "sz.marc<PERSON>@gmail.com", "name": "szmar<PERSON>ak"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cacheable-lookup_4.1.2_1584700868048_0.4442040801091358"}, "_hasShrinkwrap": false}, "3.2.2": {"name": "cacheable-lookup", "version": "3.2.2", "description": "A cacheable dns.lookup(…) that respects the TTL", "engines": {"node": ">=10"}, "main": "index.js", "scripts": {"test": "xo && nyc ava && tsd"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/cacheable-lookup.git"}, "keywords": ["dns", "lookup", "cacheable", "ttl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/cacheable-lookup/issues"}, "homepage": "https://github.com/szmarczak/cacheable-lookup#readme", "devDependencies": {"@types/keyv": "^3.1.1", "ava": "^3.1.0", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "nyc": "^15.0.0", "proxyquire": "^2.1.3", "tsd": "^0.11.0", "xo": "^0.25.3"}, "gitHead": "bc086da69ca0c774ee33672c2be72f65aa807c5b", "_id": "cacheable-lookup@3.2.2", "_nodeVersion": "12.10.0", "_npmVersion": "6.10.3", "dist": {"integrity": "sha512-cuQeb63dh/3WLwOqd0yUKLUyV0YB2OKmnH2FxTnRyQ6MK+kHXxrc1tuGLa+Rtzb1QU/syvzCVup3XG0ZZW51BA==", "shasum": "ab03bfb2def072182063a0d1115cedb51651a272", "tarball": "https://registry.npmjs.org/cacheable-lookup/-/cacheable-lookup-3.2.2.tgz", "fileCount": 6, "unpackedSize": 20867, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJedJ7/CRA9TVsSAnZWagAAgz4P/3/mv6XS+20BKSj05zns\nMel6jDD/T/DyFKCZphMgV4h6oiaFIx6PDLbVkbiKdvOUzv/hTdky4J2CBkpz\nHDS/mVTU8cSWcS7H93L7bqJgToQsEZKuVljyDAbt4Tf0l/iiEaj+eAWMSKf/\ne9dT3RLqEriD3GQq4n4umF0nkLaesfGIYO85RZwzQeNMHGMSL269DW6t3SCX\n5qqxCGjl0kaIGcpcxRl+6gOLBsZGicGmRukl1tHWf8zmzgkc/+VttWzjtAt8\n1LIDnnEH4BDJVbc4andY21vUqaXatT/QwlrYpjjNQp6JVyA87XHQVOATEICv\nQE/EAZ/7FJiiQsxgabSpKaGvYRN8b6gVs6HcsOzrwJ5oq4FjbOFhdubSJy7r\nS9m5NsjllMxw96hc6yySDmoTDACRHixBNColY3iQu5OWoV/FmG/GCazc5Fnh\nZ385e4isy+a5gJ4roAs+W0X9PZIFfZogldNYVdAqB2QKgYut6EhpC1l0y+ur\nCTYuATGPyk5lO7fvybp8QVnvPd9SJo3/bZHT2j3CcEdCyUL5dP2W58jM1LzO\n5BIC+8XeQNycnQVQ6cJnLWTzT0BjfF74icoT713GCbdZB7KX8fM1FLowZtoY\n4aC0++myQMLoEiHmrXPUYMGNQAD0Ci8EhjlK2JXBc+XFNEtPbFxtYsemnisc\ngtkq\r\n=ChYp\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC+njLZUuk+O1fUt9cIGfXQ9XSVYs3HH/RJgILkfZVCXAiEArZ64peqywOjJeyt5rxRBJxrttsToeVRKxHKjrEeJBCE="}]}, "maintainers": [{"email": "<EMAIL>", "name": "sindresor<PERSON>"}, {"email": "sz.marc<PERSON>@gmail.com", "name": "szmar<PERSON>ak"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cacheable-lookup_3.2.2_1584701183402_0.9022463835340191"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "cacheable-lookup", "version": "2.0.1", "description": "A cacheable dns.lookup(…) that respects the TTL", "engines": {"node": ">=10"}, "main": "index.js", "scripts": {"test": "xo && nyc ava && tsd"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/cacheable-lookup.git"}, "keywords": ["dns", "lookup", "cacheable", "ttl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/cacheable-lookup/issues"}, "homepage": "https://github.com/szmarczak/cacheable-lookup#readme", "devDependencies": {"ava": "^3.1.0", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "nyc": "^15.0.0", "proxyquire": "^2.1.3", "tsd": "^0.11.0", "xo": "^0.25.3"}, "dependencies": {"@types/keyv": "^3.1.1", "keyv": "^4.0.0"}, "gitHead": "4c1c3a0ce02137fa81e57ac90f2f0891f7766e42", "_id": "cacheable-lookup@2.0.1", "_nodeVersion": "12.10.0", "_npmVersion": "6.10.3", "dist": {"integrity": "sha512-EMMbsiOTcdngM/K6gV/OxF2x0t07+vMOWxZNSCRQMjO2MY2nhZQ6OYhOOpyQrbhqsgtvKGI7hcq6xjnA92USjg==", "shasum": "87be64a18b925234875e10a9bb1ebca4adce6b38", "tarball": "https://registry.npmjs.org/cacheable-lookup/-/cacheable-lookup-2.0.1.tgz", "fileCount": 5, "unpackedSize": 17154, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJedJ/8CRA9TVsSAnZWagAAzZgQAJ/NuMQGuSzjQzKam61U\n3/jsUh1y+/1PXKjvzfZhEfV8YlQX9tBJCOlFp3u/PJFgzPfG+5O85/fw99Ik\nxPNGPohSHlEXR0fyrXc0pfZbd8wcF4Wbx1UTUMoPh0y34doytmz5adh+9c4T\nViciClnAeu41iavw6sQkUE+l0gvWhY47LdBBSJG892gmNhzK8VrKVuVKfWnf\n7tH1mdocNgXVTuSsnGTvKqvfbS5LUZsnYGolgcCco5HkE034dHpRfzLvz6n/\nmgzNvKCASDsvLq7Hf2ZEXbBzPcaEuZAIZrg8E5PhxbsrcjFwvXpAikbu1gRN\nrW9HIN7j+CWZYDuyvKpj/78mNQKQVPfIWjjPhkLn/xokHBEbBcKlYYjX84W0\nDzXJcRPQ6YTXxuDg8QX7dx0VRPKxSB3z7L2O9RSZ9ggiI+/M/JtceMlMcgzz\nD7FLJWhyeyVEkI0BZXVEzGVPIWhm8m7usmmb60vUlfPP7Vawq+Iqmw2VZTco\nFf5ri8k+EffdTajsjZ6hTzEpjQqsE+vK5oZYFQgYyC8Eb3gqYNZiye3UlMtk\nxKPB84xauZO1F1IY2tY7jDlRfpUXA0lbaOot82izv0LA0lhlX6gkLerCYLHf\nQMs/C8Wn6sPIsNwxxnaCq0T18fcKL6y+a+1zAekaacdfUz2yBPPqe2VMuhCb\nG51N\r\n=Lesv\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDiIQAi/Euxg2xRgrDhbS+tq/KmbOv7RlIxqDYAI6PiMAIhAPVFGTeP2jTK3Vus0HS8hI+O253OudEJcV527jmyco3G"}]}, "maintainers": [{"email": "<EMAIL>", "name": "sindresor<PERSON>"}, {"email": "sz.marc<PERSON>@gmail.com", "name": "szmar<PERSON>ak"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cacheable-lookup_2.0.1_1584701435696_0.8034821789790332"}, "_hasShrinkwrap": false}, "4.2.0": {"name": "cacheable-lookup", "version": "4.2.0", "description": "A cacheable dns.lookup(…) that respects the TTL", "engines": {"node": ">=10"}, "main": "source/index.js", "types": "index.d.ts", "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text ava && tsd"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/cacheable-lookup.git"}, "keywords": ["dns", "lookup", "cacheable", "ttl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/cacheable-lookup/issues"}, "homepage": "https://github.com/szmarczak/cacheable-lookup#readme", "devDependencies": {"@types/keyv": "^3.1.1", "ava": "^3.7.1", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "keyv": "^4.0.0", "nyc": "^15.0.0", "proxyquire": "^2.1.3", "tsd": "^0.11.0", "xo": "^0.25.3"}, "gitHead": "49c193b15446baac2679751ea001b1915d474c78", "_id": "cacheable-lookup@4.2.0", "_nodeVersion": "13.13.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-57vpIhK+mpViWMgY65nly7pgofA9x8yebdVc+y4Vwu8S8Wunx/iru5ieIlD4QyaIAXBJIqI+ONsSU8guxr02iQ==", "shasum": "24a725d2a9cf6822c0cc6c0ea66d1fe16edd87ae", "tarball": "https://registry.npmjs.org/cacheable-lookup/-/cacheable-lookup-4.2.0.tgz", "fileCount": 6, "unpackedSize": 24515, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeptyfCRA9TVsSAnZWagAA78EQAJWyEqAqdcgG5Dl+UOdu\nrt+aeS9+tY9rB9/pb9NU4YcPWTDvauTlyQGbjP2lRZfecGKeFBvELgfcAJdm\nHsxq1ozH+thfxZEs5+rEQghlLTRxka79FqL4uKnQPZxrcwVK/V/cvCnMnosW\nYBkBBe08e2NCxTByo2QFSx6Xtm/zJUZ+GvpVp8pPw+wrKrI+RLs4i1ggs6SU\nd58BjwbT1jLSTo7KZPJmwYY8rurfCrUCsZAFQ8dTuydj7dJHEl7aBaHIjbwu\n9kFP8zkiZ2aRvmv+LK8zwkGgBu2OBZtxkRJrF5Rlz//mXccSTiF5sDJGPD6e\nno+fts8Rd8Nl/OrWiNSuE83OMguadB2aRNIOLVajfNjdTNtBeI2Ws66aaQUN\nFc5vpO+R5rbH4X8bOct3yGKDJse+87ilXW7ypkcBar8xJa8zoX7PH6idrHCd\nZCLCNKGR7CSWpm3vDSTGxpCk0CWcAZxqkw5sjrS+rMVveELxwiu8X2KfBDgD\nbq1oP6atfcrCVH9rd9q4oTgFUr4HhJmPJ3D9iqBfBXcWDGGB44N6Ee0rqasS\nbe5IPRL7wQKo4wsBOkqwPjT56WYjY6y56gv1Qpog1DOT/ufEU9PkGO7oovAK\n3EDBvcgziLd5iAFDevX4qTI151Rvh8MoirIjePjBubaTiea7MOdAhzMVGbFx\nbZsx\r\n=0OBt\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDVq7jhTI2mJpxMruRQiAZhDaGwAnf8+dod8F/hnqLeIAiEAvcKAvl7rdMFdGXOXgzlWNyDLedw2L6FLNvO9IZUgpxI="}]}, "maintainers": [{"email": "<EMAIL>", "name": "sindresor<PERSON>"}, {"email": "sz.marc<PERSON>@gmail.com", "name": "szmar<PERSON>ak"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cacheable-lookup_4.2.0_1587993758751_0.22260147104762384"}, "_hasShrinkwrap": false}, "4.2.1": {"name": "cacheable-lookup", "version": "4.2.1", "description": "A cacheable dns.lookup(…) that respects the TTL", "engines": {"node": ">=10"}, "main": "source/index.js", "types": "index.d.ts", "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text ava && tsd"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/cacheable-lookup.git"}, "keywords": ["dns", "lookup", "cacheable", "ttl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/cacheable-lookup/issues"}, "homepage": "https://github.com/szmarczak/cacheable-lookup#readme", "devDependencies": {"@types/keyv": "^3.1.1", "ava": "^3.7.1", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "keyv": "^4.0.0", "nyc": "^15.0.0", "proxyquire": "^2.1.3", "tsd": "^0.11.0", "xo": "^0.25.3"}, "gitHead": "68d8b77608f9304a3138302364de3b30aa806b07", "_id": "cacheable-lookup@4.2.1", "_nodeVersion": "13.13.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-DqtvWnUbBJgpOvgYctguN4jl36gjAZIQ/BjnSMsiK8/ubm1XIqi5qn40PRoyqaPAVZccXrwB+75Mhubcr5v+5A==", "shasum": "3449825d21b971e7971c4fd42b89522deb3a6d7f", "tarball": "https://registry.npmjs.org/cacheable-lookup/-/cacheable-lookup-4.2.1.tgz", "fileCount": 6, "unpackedSize": 25023, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJepwS5CRA9TVsSAnZWagAAA9QP/3soxPiBUPx0b6gtjhIJ\nuftFp5mMhYYG3R1V0cVcebafBNrO4amq7IRNBOfrTZtpQ0eezHcMWRhigfw7\nHNuPGi9L1z+ACQ1E3Ic53rI300x43d3TcLnD/ovvp9DKVzayw8qNix4XwxPO\nFgmlHvIeaI54v88y2SkceHR7u1QmMHgRW1zw51bpLH0V5bTZZ3fNA/a0nZxs\nOulY5epWVU2ylZ1Quj5/cZjyjlw51a2rV77WqlCj4F9rHC0WmuZgJuc3AmSO\naNLl1363bqVCCTrYA+T4wI1PP3k/cHWiy2mr2TOIZL9ubXSAf382QYpUs8dR\nzEiyEy08eXkkqmXXCEv+6RjMbbDCVsxZToHv+hA205omKU2tFugBG4onIp9o\n59m4vWsxhbsLH5mlW51jDAYCoFAHmgt3N14BywR21jZ2NMrF0xF3S0VYkFaH\nbK2o50wz2K8hTlUaqGOXmEDst/J/BMBEvvBNWjucExem5FPtLkWqPaCi5uOC\nydxP27EfkqCnDIsAPJ7CDad2eNp3jw2HjpQCkdBkbsyIuzlBbXYOb4Nj//Lm\no1zU7HExjHKVyPmN2hlNYpuhehfYSWYd15hbg5/z48gXnPbStiwJuacwEb+l\nSz5A7G22VDQJVH6QIGzfqz01qAQ/lU4E4cW0u8wMx6TVL9ZyZcKTBKnR/1un\ngRuH\r\n=vzqa\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD8ztqf7IGbLWTX4gJ62C7E6wKat00cknJKepw+rljRdQIgEZK5IN9t7/9aPoSRPE/08bZE0ZficmZDkWgLuSxw34k="}]}, "maintainers": [{"email": "<EMAIL>", "name": "sindresor<PERSON>"}, {"email": "sz.marc<PERSON>@gmail.com", "name": "szmar<PERSON>ak"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cacheable-lookup_4.2.1_1588004025242_0.22155291334598215"}, "_hasShrinkwrap": false}, "4.2.2": {"name": "cacheable-lookup", "version": "4.2.2", "description": "A cacheable dns.lookup(…) that respects the TTL", "engines": {"node": ">=10"}, "main": "source/index.js", "types": "index.d.ts", "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text ava && tsd"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/cacheable-lookup.git"}, "keywords": ["dns", "lookup", "cacheable", "ttl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/cacheable-lookup/issues"}, "homepage": "https://github.com/szmarczak/cacheable-lookup#readme", "devDependencies": {"@types/keyv": "^3.1.1", "ava": "^3.7.1", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "keyv": "^4.0.0", "nyc": "^15.0.0", "proxyquire": "^2.1.3", "tsd": "^0.11.0", "xo": "^0.25.3"}, "gitHead": "6ed7e37ccffe7cda06140d20d6b0262e97bf36eb", "_id": "cacheable-lookup@4.2.2", "_nodeVersion": "13.13.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-06EWjs5/UO+gl6RHW7UAajeMZ+5E+HvHLQtaKcpjJLE5S/3+pX28VClFXM+LCwFRcmODURMnO94bZ+lFy5YvRg==", "shasum": "7fee1d25d9902382a6b8966c164349977168ed4f", "tarball": "https://registry.npmjs.org/cacheable-lookup/-/cacheable-lookup-4.2.2.tgz", "fileCount": 6, "unpackedSize": 24635, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqDteCRA9TVsSAnZWagAAZ1wP/1i4VEXIaDVSCQ8Arlg4\nF0Hhh0ns44PMo1LQYJXfwrTY4fMjBghm/QTCewrU1SfsE06pqFpZGYB0Rk81\nnxEHk584Ofcr9wPsPWV+6ORC0PVG5W3SWbnWJAKhOTP82zVz8GQrjX/BmP+c\n9PGaQpKUTST8f1QDKbXew3nI5hagC/Yt/QsCOlBx0kYCjzrMYdzwE6VpIbie\n9tM7BQDD/uud55EQcbghaJtQKmPOLZIeoC8jC0OrlgF/i/QTJOQ9BOW/rnpa\nXdOh+bAu+EiCs5ONVKasMqQRCWBiNj2mYvQK5Mm6avb+YYVfZSVpkmv+msf1\nw3mYW9jT6sV2xq0gjCVriJcmIOSZHXMziw549zxrIiMTJglajLgyXgBYceyt\nzEdGWW47Mr9XYb+Gpq/l0yL5Dknve67pE/d1aQqr7YLJGfpiny2PR8W8YRsX\nczF7ctSNAm40BCWPdxk9av/hY7sn8Yzj6ov79Gzhp94uwaHg3EqoOq/qJphK\nEs22mCN8DiaAmWoA8OlSUp02CPA+OAxP0P4MgGG1zO7etF/j4mIi94xwDbmY\n7PozyphX5EwUm9AnLDVyuJuPSFbSlpN88WCQowuyLxlwROvIImXawNdTs8Aa\nwV+vvztmq0KU/YEbVEbtK8X945cNrWdbdlnczGlbZqswHfPtzzYpXsYYRGmU\nYvEG\r\n=U/TM\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDOuaMOV1k+mNLZyTgV+/Dwh/8nekp3t+JLuPARMu+w2AiEAtoGh7HWDm2wvmncPUVjJr7rs8mk3ELVDColwDBa35vI="}]}, "maintainers": [{"email": "<EMAIL>", "name": "sindresor<PERSON>"}, {"email": "sz.marc<PERSON>@gmail.com", "name": "szmar<PERSON>ak"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cacheable-lookup_4.2.2_1588083550011_0.9413656738650971"}, "_hasShrinkwrap": false}, "4.2.3": {"name": "cacheable-lookup", "version": "4.2.3", "description": "A cacheable dns.lookup(…) that respects the TTL", "engines": {"node": ">=10"}, "main": "source/index.js", "types": "index.d.ts", "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text ava && tsd"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/cacheable-lookup.git"}, "keywords": ["dns", "lookup", "cacheable", "ttl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/cacheable-lookup/issues"}, "homepage": "https://github.com/szmarczak/cacheable-lookup#readme", "devDependencies": {"@types/keyv": "^3.1.1", "ava": "^3.7.1", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "keyv": "^4.0.0", "nyc": "^15.0.0", "proxyquire": "^2.1.3", "tsd": "^0.11.0", "xo": "^0.25.3"}, "gitHead": "9973e76b936e58d1597f829c8f393061490817d2", "_id": "cacheable-lookup@4.2.3", "_nodeVersion": "12.10.0", "_npmVersion": "6.10.3", "dist": {"integrity": "sha512-NdruD8iCwUSqPeacly+fxbi71wjPuIbh9wsBhLdRmpXDFPgQtX+xWytveqDyFYQ1tDR5POAxH4jIkkoit3arXw==", "shasum": "8d8a6021addb6b0d21711ba80797cdc636761cc3", "tarball": "https://registry.npmjs.org/cacheable-lookup/-/cacheable-lookup-4.2.3.tgz", "fileCount": 6, "unpackedSize": 24765, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJer9tECRA9TVsSAnZWagAA/BEP/jO8vZ+KqzcLSFs+wSE9\n3q7QoWhSmDZUIEoFUS+pPOJu5e0ppchZbLu4zJ6iDl0hj680oilGk0HA1GIs\n8GEDz8yYsZJY9JdHpWVT0W87wbmkYHCCT8gAlxzvGkJZ0Gm9fdIhxuLoeEmQ\ndgXf9q2YjymFWwmdM7tlWHVQq0/CmQiLa13kRbVg1tFjQiJcJdb00I3WkLb+\ncSRbi6/P9Fs9atFEN+c4AxnBhmc4P8YQ8/2mvYeBT06LoBaXiiuaXmkgBKit\nlPk6BN4S43w2SRNChsjkSVh1fOOSF8nIshC+c7I6q+jwHSfMeIjPYprL9ala\n8weBGHBwhU9fGeDbm1ttBf1hz3IGKL1CoASKeu7w3WYOl685cG8N6CzqQ51c\nzpoFNpAFNcsJJ9d+gaz4LDECscqvor2vEU3Wp9tWPMUCSNsxQKYZjGaqylX6\n2NoZc9/hJ6AhhRGtU2bFd28ojW8UQGJ+n/2AYQg4agUjBniHYoiwQHvNKHKk\nO6yod+fnqQBKlV9kykpzPGh3hlmnHsQ7W4lrGCOAtvl/K6Jy4G5Oxxab/WLS\nAxDWyqjAvxEaQAF2spKWy9g3OqNU5NQg0n9SS8Ph4AH3yIDWiaIRR+rZ27hC\nblPT7QfIAJglnRUeyHZoPzI9yzp+2vLjMYpiR5jTpXJ/L7IA+IylOa1CXAg7\noCFJ\r\n=4o1P\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHyToEYw2pDbqHvRjXsxgdv7fihbKTTL8M0nP6DdkkFTAiEAoyO3y9G+AgwrguDdwUdAMq4c970FJ3YEz8TixtEondA="}]}, "maintainers": [{"email": "<EMAIL>", "name": "sindresor<PERSON>"}, {"email": "sz.marc<PERSON>@gmail.com", "name": "szmar<PERSON>ak"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cacheable-lookup_4.2.3_1588583235810_0.4958221990551972"}, "_hasShrinkwrap": false}, "4.3.0": {"name": "cacheable-lookup", "version": "4.3.0", "description": "A cacheable dns.lookup(…) that respects the TTL", "engines": {"node": ">=10"}, "main": "source/index.js", "types": "index.d.ts", "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text ava && tsd"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/cacheable-lookup.git"}, "keywords": ["dns", "lookup", "cacheable", "ttl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/cacheable-lookup/issues"}, "homepage": "https://github.com/szmarczak/cacheable-lookup#readme", "devDependencies": {"@types/keyv": "^3.1.1", "ava": "^3.7.1", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "keyv": "^4.0.0", "nyc": "^15.0.0", "proxyquire": "^2.1.3", "tsd": "^0.11.0", "xo": "^0.25.3"}, "gitHead": "20bc56c49e4dbbacc3237dfee1b3797e207e716d", "_id": "cacheable-lookup@4.3.0", "_nodeVersion": "10.20.1", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-PTUoCeIjj2awloqyVRUL33SjquU1Qv5xuDalYY8WAzd9NnUMUivZnGsOzVsMfg2YuMsWXaXkd/hjnsVoWc/3YA==", "shasum": "86ff1cb38f648cc6aba28feffe008f808b403550", "tarball": "https://registry.npmjs.org/cacheable-lookup/-/cacheable-lookup-4.3.0.tgz", "fileCount": 6, "unpackedSize": 25877, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJetS+XCRA9TVsSAnZWagAArFAP/1GE1t2gXMRzjyBdDPyA\nhW13hcF8jHosLioHpTsTlkqDFg1XlPa7533SCwD1PkWxO7YGf+XwzDw9R/fZ\n9JnKyi5vBTOK7u8sNR4NnHNklH3s0INGILdomEuv6h4ZXNQY72Ozz8hegTcz\nA4yZlXWWj8A61FcjfKQ5fNhgwdV5K7nO2zuOkBNlkUWiJM0BZEinndOzfLqY\nm9dNNtBevC3aQ/N2YrSrM8shINWYvB2cR187lt1T0hb1MxHtHySVLnNtuJO5\nI1X0opxtuCO+4J+X4Whg3KHDtvr6MYemK3vHVslXOMFpk35q98aEpfpMy7Tz\nosR2kHQLWmRlBFgaUaxj0LSonGhmPtACQKibPJUttDPpLmyN/UysAeV/PIS+\nXMEM1xs3Zeou5vpBvrN4r0Xqoxva00+HLdNSB4kZo9Kpp5ZcB0hBRvDgAeqt\niunMoLRMExjoP237VGA/VuRN7OUDhu5r0EwZ+uytc5OJtamMosk/y5y9zwzO\niWDgagaVFV5YSj1Nw/IZHXSYx0cYXN8TcxVuXC7/H6ofY0Pa0Ah5t9R39vF9\nSj606Nncb0YypnikUGufIN8m2aNvx4uQM8tXOqJHdne0RkWfX7JZEr8H/0dE\nNL0ASf0ioR9RaBmhfZx+2H2fXpNX+RKse/Ys89Y1tw04QDvd09oG5BD3nz1y\n6CS7\r\n=Iw1q\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCRzgpm6zT6JT1os1Tdx2sWWp5LIG83eHH9UOo0rJ1TiwIgBbdfrQgYmnDBo78QiwkR+1Tv6d3rJbO7BvsDdhT3Fog="}]}, "maintainers": [{"email": "<EMAIL>", "name": "sindresor<PERSON>"}, {"email": "sz.marc<PERSON>@gmail.com", "name": "szmar<PERSON>ak"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cacheable-lookup_4.3.0_1588932503310_0.4138340296997358"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "cacheable-lookup", "version": "5.0.0", "description": "A cacheable dns.lookup(…) that respects the TTL", "engines": {"node": ">=10"}, "main": "source/index.js", "types": "index.d.ts", "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text ava && tsd"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/cacheable-lookup.git"}, "keywords": ["dns", "lookup", "cacheable", "ttl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/cacheable-lookup/issues"}, "homepage": "https://github.com/szmarczak/cacheable-lookup#readme", "devDependencies": {"@types/keyv": "^3.1.1", "ava": "^3.8.2", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "keyv": "^4.0.0", "nyc": "^15.0.0", "proxyquire": "^2.1.3", "tsd": "^0.11.0", "quick-lru": "^5.1.0", "xo": "^0.25.3"}, "gitHead": "fb377ef17533221f4bc23059ac9d30b55eab4e85", "_id": "cacheable-lookup@5.0.0", "_nodeVersion": "14.2.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-2Yy1H4UOAkOJB6PK64k1jfg2xiaqM/2lbpJA2/9rOMfXMfpPJdNLqU1kUHCokqD0POl4VK/vBjXBCUDvOzefJg==", "shasum": "092b8d2e06e09ae49e2e3a2d4344318f33989571", "tarball": "https://registry.npmjs.org/cacheable-lookup/-/cacheable-lookup-5.0.0.tgz", "fileCount": 5, "unpackedSize": 24264, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJetuqhCRA9TVsSAnZWagAAA/wP/1QEV3xr3zjaKbRakpol\n3lj3p6JFBJvTTtamWJF+bs8FcJo3cXIyEXhQWgqu7Pb2bDGZcLMoE7Fz5TCM\ndrJClgoe2FP0dYR8nM+NWBWYXJiZnDF7fQC2ZE3Hikpt+E55VeqI7HIDe6RK\nDnpnlL6ff3s+FsoWSAoXDRhBZW2QXvK+z2OQ1X9e8p3Jdngo6dVTIWTam4kL\nLcclGXXtJLf97btiTu3nWvBBMysP0xBnR7JPbMRUPDIfPa9k4YBBYINl+zzZ\nW3Le9WTRrepoepgIGCE+1Uro1/dnOzadcaI+Z1JYJvJ1Ut5nCETBJfzq4FAh\n6XEEkFK7XQ87sWhhlxgay6bLvvzE4uP2qf4WdUSxYt6/HtJdoeN5pn8rH0ic\nlP1Ptn6l/KKRc4v9ZXPwme+dZxb4FTR6+C8gd5WFtfAHroSwcukeTdxZYtI7\nkStidkINPMmYO3bemXyCgI9vP5WY2mtCK3OOQVy17Ky6vPmwo0z0IUvQ8tSd\nX4BYH1gjGE2JxzjG+xhSXhTCsYpPcGkoL/xX3r2fmxRzQoetNXjySTVQeyWC\nL/wlWoJzaoA6/Q2pzHvZiYCJszKKDzGPg/LpYGWBlcy11lXGkJRDauck5rSE\nUEeW+m+OFnoi3MEK3lUyvaQszHMUdsnbNuINIbTz799YFWQgNYNBF7ROPvY5\n6/oq\r\n=YnKn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGM1JCizoxI2gHZPN3klopSTIT3uGOhpYSQeMc2l7FHFAiEAjKnzUCtcsgdEMK2XR3ZCNVbiO7n7l9spsW9AXBY8eO4="}]}, "maintainers": [{"email": "<EMAIL>", "name": "sindresor<PERSON>"}, {"email": "sz.marc<PERSON>@gmail.com", "name": "szmar<PERSON>ak"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cacheable-lookup_5.0.0_1589045921203_0.5331031062874094"}, "_hasShrinkwrap": false}, "5.0.1": {"name": "cacheable-lookup", "version": "5.0.1", "description": "A cacheable dns.lookup(…) that respects the TTL", "engines": {"node": ">=10"}, "main": "source/index.js", "types": "index.d.ts", "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text ava && tsd"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/cacheable-lookup.git"}, "keywords": ["dns", "lookup", "cacheable", "ttl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/cacheable-lookup/issues"}, "homepage": "https://github.com/szmarczak/cacheable-lookup#readme", "devDependencies": {"@types/keyv": "^3.1.1", "ava": "^3.8.2", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "keyv": "^4.0.0", "nyc": "^15.0.0", "proxyquire": "^2.1.3", "tsd": "^0.11.0", "quick-lru": "^5.1.0", "xo": "^0.25.3"}, "gitHead": "c231865e7ccedac0fb4d93bee97ccf00dcd270dd", "_id": "cacheable-lookup@5.0.1", "_nodeVersion": "14.2.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-1M+8zGmXAfpR/gYgnO1VWjPxXcGBgH4f+XHInZabKP14ncA6fxpB5RVcT3TLKg54vPBujt922VOlBh7LA5Gucg==", "shasum": "ed74fa2b7c25eda0f45f60ed9ecdef2c831e309a", "tarball": "https://registry.npmjs.org/cacheable-lookup/-/cacheable-lookup-5.0.1.tgz", "fileCount": 5, "unpackedSize": 24423, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJetvDyCRA9TVsSAnZWagAA7bQP+wafbDMBMChluvVOaUHO\nq6YoFw/2NgWQib2MaYs9WLbTYWxw1DX0Nu4pP7yOzrThns5RczHVFwIA/AHT\nRpUok69QhmN3Xm6wjH3oVZ8J26u1EpdD3COW1JERg8kH0B1koALf9cVwJrRH\n5nPOeh9dJRHKy19YLsZ7adR5AAX/u2G3P3akcJI1apkwz8iRGB3/i41X0u3u\nrqQ4HLije2b0h/Upi6h+dR/HAkDXVAuzOoNaM+Uw9EAQ/QiJF0fl1pGh4ney\n/wbqGKjEM6dYVwOuaGxK8eHzCv3PutnUzRAJ6v9MVgsa/EaDAJzvdQDBAeo7\nc+yYqkPjljVEKd9eNWzpVH8EoOxHfEj/gr+Mpzg7b807f2qUDRfYO5VqQwTW\nlvoQ3BjvqH/ChcuIEXforjtvy3p398fttYWu+qPImqV0cTtznv90i/5R867i\nwiFXZI4UcA7UDxPTRsXs6TMhj9KFgLrpbQGyPlT4gnYbFQjctfS0vf0o02mJ\nT7aIpTJqII5ZPofWT+cvRAuMCxverHkIjgG+J0xZhzRPJuqawF17zmQsEg7K\nAlbx6TgX4+N3vbcJjCxXhALEMCaM+PfjRjvg+VILs6ejggpo4XthfidKGTjB\nkGG8i49g5wq4G5+cVDJZVH37KIFg3G+Shx9Xg9znXgn4vYR3CjNPBCeOKwUR\nWtoW\r\n=0V6D\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDX7Rc5nWguCE4C/HbTKjwBC3grRIniFk6/brDPK2qX8gIhAICUoE24R30mg4MZo1A5isfUmAg36J82lqLsS2E4Rqvl"}]}, "maintainers": [{"email": "<EMAIL>", "name": "sindresor<PERSON>"}, {"email": "sz.marc<PERSON>@gmail.com", "name": "szmar<PERSON>ak"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cacheable-lookup_5.0.1_1589047538226_0.3055773516401603"}, "_hasShrinkwrap": false}, "5.0.2": {"name": "cacheable-lookup", "version": "5.0.2", "description": "A cacheable dns.lookup(…) that respects the TTL", "engines": {"node": ">=10"}, "main": "source/index.js", "types": "index.d.ts", "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text ava && tsd"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/cacheable-lookup.git"}, "keywords": ["dns", "lookup", "cacheable", "ttl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/cacheable-lookup/issues"}, "homepage": "https://github.com/szmarczak/cacheable-lookup#readme", "devDependencies": {"@types/keyv": "^3.1.1", "ava": "^3.8.2", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "keyv": "^4.0.0", "nyc": "^15.0.0", "proxyquire": "^2.1.3", "tsd": "^0.11.0", "quick-lru": "^5.1.0", "xo": "^0.25.3"}, "gitHead": "392e809c5706d5838f13be732bab69a7f3bc1155", "_id": "cacheable-lookup@5.0.2", "_nodeVersion": "14.2.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-HY6sveKeYAuop44Wj3eawYMx7lktf6zWg7pes/UvuKA9fFJaxRwCkdQJxV2WKhXNYx06dVy35R7lS7XDxjdPLg==", "shasum": "864ea350ab89b7c940e93896a1fb604de4b60927", "tarball": "https://registry.npmjs.org/cacheable-lookup/-/cacheable-lookup-5.0.2.tgz", "fileCount": 5, "unpackedSize": 24586, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeuGWXCRA9TVsSAnZWagAA33wP/jCs3P+vo3+0DPp9YYt5\n5bNPGmZlUoAoFdFzvr0smb9Dk2r1VtWJryLTNGDUWHanPL4xZaWxuGFOH/7k\n7VsM21OLDJrJ9EFLQegZ43hmWZcb9LpJXzLep8GQXUT9QbiMVvcTbsZbaoZl\nG31R8DvbRo9d7lXy5BGNYEwD4npE3YqwsCN0kDouuly4L5SLEmW7gN/19+aI\nBb/EUR9PL5MhEdt11Kj7LJXt8I1ywMKYGQlurW6fHt43G012akzM0jgFwSdc\nEcPvsUeXvp24QdjmaVyf4JnfnjCPCUAxn22iDBGtgNuls2bfBAGDd4oJSkrv\n0A7Rk9GggMDsTvgp2597NAdQYEOsJeKanuvi8XmYa2nIHhMFPZ4GNnCXwbwO\naYBwqcVbW4xN5FldFoccCziKz4fdPC5qq7z9+z+/WNfJae1UB1dnxvwur+rC\nfFMGhfFTcJrTXXCqO0VX+k8s4nKdwVYQA2+tLwPbKL/mkw+5jxdr5VzXAso9\nk3mhwDX55GPzkJ9VzwZFZjrYqD5SdlWV0Lm5E14A1Q798Z4K8OeDq8zV1VuA\nljGxI93OzBfFuqKixMiwxIpZ0ytNCAKIYO4v8eDqB7k1sntx/iZb8kLK0XzA\n2C6MoKHHL6HPUtb50qNlwzfglTE5n1cONAtQoxn3sBmmwWpb2TktxIEQWXpm\nh3Aw\r\n=kwq8\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCTB4CH5TDHXCgrpu63e2I77jEBl1GWEK065FuCogiQqQIgWFkg101qAoKZhwb6dd4Bxe1sehrLOSdHeQtfey+7oCA="}]}, "maintainers": [{"email": "<EMAIL>", "name": "sindresor<PERSON>"}, {"email": "sz.marc<PERSON>@gmail.com", "name": "szmar<PERSON>ak"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cacheable-lookup_5.0.2_1589142935232_0.727981832368094"}, "_hasShrinkwrap": false}, "5.0.3": {"name": "cacheable-lookup", "version": "5.0.3", "description": "A cacheable dns.lookup(…) that respects the TTL", "engines": {"node": ">=10"}, "main": "source/index.js", "types": "index.d.ts", "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text ava && tsd"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/cacheable-lookup.git"}, "keywords": ["dns", "lookup", "cacheable", "ttl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/cacheable-lookup/issues"}, "homepage": "https://github.com/szmarczak/cacheable-lookup#readme", "devDependencies": {"@types/keyv": "^3.1.1", "ava": "^3.8.2", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "keyv": "^4.0.0", "nyc": "^15.0.0", "proxyquire": "^2.1.3", "tsd": "^0.11.0", "quick-lru": "^5.1.0", "xo": "^0.25.3"}, "gitHead": "c46a5771d9d3a6a59537ecf6b7de786faab5eb98", "_id": "cacheable-lookup@5.0.3", "_nodeVersion": "14.2.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-W+JBqF9SWe18A72XFzN/V/CULFzPm7sBXzzR6ekkE+3tLG72wFZrBiBZhrZuDoYexop4PHJVdFAKb/Nj9+tm9w==", "shasum": "049fdc59dffdd4fc285e8f4f82936591bd59fec3", "tarball": "https://registry.npmjs.org/cacheable-lookup/-/cacheable-lookup-5.0.3.tgz", "fileCount": 5, "unpackedSize": 23976, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeuVqdCRA9TVsSAnZWagAAEb0P/RbtzjrM5+HEZGwDs2Ri\nNhgIOCNXgLwruoQiJrktPB4BjpOgOJddekrH/HwrHBnUGONC/e/6i+nHP1mH\nxjH9eVMS47m4dctYNoG3R6O5f4SY1XIyiMOYMAwR/p4iUyCNSVVvn7MxfeY4\n+7BwYjDkTkXe0LM3bfnBcTwLe/IL2G9Fy3vh7wPaQzeiN7M5xGXi5IncaEb6\nda6SyeqeGSU0kihFCTySEBiGMtgUrEmYaaWIh3oBNJaJvIIYHNSz6ENjg3+8\n5+AT8ls51a1hzxJzUp3SnXqGv/VthFYyakt4wULMT04W4i3znSUcwLBhVz7P\nj5uWkGjU3iVqTTQnPHP2x6H/2RRMN96zxoYDAij05q3ar4g0oLpoQirTYoL7\nc3O+OrGhHnIQK+5mJEbmqO4v53ewlmQRCh7E7NBR+uYHCihFusSigOPaDShu\nM/1ekS+xTlwfOXsDmNI54UZl6pSoW9f63TSACo431Ncd57ajnbtriZJLI9nG\nyEeASx7vFznToxxl92EK6HOdKcgelY9VtuPkKJtVfoj5oXw+OhqlrN+us2U8\nauvLJIrjSju4xAcrnoKDPrOckp+7nY9nyBKoc+coNdT81PMBmNQ81z1eg2zw\naPej3CKL03x+i2AM0k6L/99fj3smlhB9YcCcjB+qfAIt8fVyvO6T8wR7nJ/s\nvvkh\r\n=qMQk\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCQfx+OA56NeOCycaEbxUKYMj5HhFFQUzUjo5rcRkJ0UwIgAfQ01Vy4bBkmvzLOvOoLCcJ6YEmukW4FLGsAKemVH04="}]}, "maintainers": [{"email": "<EMAIL>", "name": "sindresor<PERSON>"}, {"email": "sz.marc<PERSON>@gmail.com", "name": "szmar<PERSON>ak"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cacheable-lookup_5.0.3_1589205653888_0.3739318147341064"}, "_hasShrinkwrap": false}, "5.0.4": {"name": "cacheable-lookup", "version": "5.0.4", "description": "A cacheable dns.lookup(…) that respects the TTL", "engines": {"node": ">=10.6.0"}, "main": "source/index.js", "types": "index.d.ts", "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text ava && tsd"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/cacheable-lookup.git"}, "keywords": ["dns", "lookup", "cacheable", "ttl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/cacheable-lookup/issues"}, "homepage": "https://github.com/szmarczak/cacheable-lookup#readme", "devDependencies": {"@types/keyv": "^3.1.1", "ava": "^3.8.2", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "keyv": "^4.0.0", "nyc": "^15.0.0", "proxyquire": "^2.1.3", "tsd": "^0.11.0", "quick-lru": "^5.1.0", "xo": "^0.25.3"}, "gitHead": "c257b0489b37385f8dc18aa5512a6e748effea2f", "_id": "cacheable-lookup@5.0.4", "_nodeVersion": "14.12.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-2/kNscPhpcxrOigMZzbiWF7dz8ilhb/nIHU3EyZiXWXpeq/au8qJ8VhdftMkty3n7Gj6HIGalQG8oiBNB3AJgA==", "shasum": "5a6b865b2c44357be3d5ebc2a467b032719a7005", "tarball": "https://registry.npmjs.org/cacheable-lookup/-/cacheable-lookup-5.0.4.tgz", "fileCount": 5, "unpackedSize": 23919, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfz8NHCRA9TVsSAnZWagAAQ8IP/2KkM7vBPG21oeCI3UI0\njA4Y1YJ2qAt5zEo29H1LjOxOxJ0sT5sXXdRnKVDTYAtCmPrUlq3TZkLpHOaK\ncHlaFr4+YWG1AmWGwPaoNHwSuPf5WeZlpqPiBBKZYdxJselhV57TQOFMNbK1\noKei03gocMA6J2iAzeu39I8eaET6xhlO+sMlsiOVNzmu9qJhMIhULfYIz41g\nndyZilwiAMG0B2mlsQVQBQiO3fl7VpqK5MvcyhpNIzKB7AsgwdldlHUTAUXv\np43osjrcnuIVRJZGggorWJwtq167AFJzS0k7o0jnCBtygolsBeC44yVk1WID\n6acLiGUQGDRv0bP5CM02Tqk4bXaKeDI4GfVHTZ2rYj9IH5MgJa5s++bFXSP7\nVSVGIq2hCXPFAbn1sedDQjdeH5h6W37xAUFh6azHTEIcemAm/t2lrHpRVFxC\nVMIv46+wbdfs67ICNrjs6Hc+VFmsoeNSo7hWecidQu73bDYY4I0Yd6dhWwDp\nbkPHhJji8Gf+5x6q7jHi0CLm96iBDZ6hvuoa222WGrhzmxD1aPgT0AcuQ2vk\n/ZLenCxjt1ZQPyFL7lYtD9QfZz+sQGYyRt8XWrBqg6XeXb9q1hG7g1xbTRr3\ns/QRphwPpMue8FNBbDj6s+XeXWH0NvILMSmXX5VmNVK1H/3n/eVeKbllfw5H\na0ra\r\n=uRj4\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBkaNzKYNgC6PyWbSsfzMDRQOLD1zYM5W1LbmDWInCilAiEA6UFju7no8AtcKEXaQSjP2t/GLUCNpd+b3uEgsOBBdTY="}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cacheable-lookup_5.0.4_1607451463016_0.7206932720950916"}, "_hasShrinkwrap": false}, "6.0.0": {"name": "cacheable-lookup", "version": "6.0.0", "description": "A cacheable dns.lookup(…) that respects the TTL", "engines": {"node": ">=10.6.0"}, "main": "source/index.js", "types": "index.d.ts", "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text ava && tsd"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/cacheable-lookup.git"}, "keywords": ["dns", "lookup", "cacheable", "ttl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/cacheable-lookup/issues"}, "homepage": "https://github.com/szmarczak/cacheable-lookup#readme", "devDependencies": {"@types/keyv": "^3.1.1", "ava": "^3.8.2", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "keyv": "^4.0.0", "nyc": "^15.0.0", "proxyquire": "^2.1.3", "tsd": "^0.11.0", "quick-lru": "^5.1.0", "xo": "^0.25.3"}, "gitHead": "5960e1d98c61f9edd6873eb03050a12122f24af4", "_id": "cacheable-lookup@6.0.0", "_nodeVersion": "15.7.0", "_npmVersion": "7.4.3", "dist": {"integrity": "sha512-5qeyMn8/BERrUPdIfcOLkdMrwltVbxIpgnYM61OLWOg3BuSSh9HrkUtTTRxYthQpBrocvYqD0tJ7vU0y6T7OWw==", "shasum": "6fd7e364a0929ee50af00843aaf6e31b9b9f200e", "tarball": "https://registry.npmjs.org/cacheable-lookup/-/cacheable-lookup-6.0.0.tgz", "fileCount": 5, "unpackedSize": 24020, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLRYbCRA9TVsSAnZWagAAI+UP/2HmmREuDR2F1Hbp1ZtI\nFr5FgkZXL4rVtr13dclSFkSckzmH5EPz9w6BPUc8tmIup1BUmTJsdJ3TS8Y6\nsWSpcEA2uB8JZOLzHAxmCeDkrDhCkd/4LzYWKToogRHwqDryTwHsMGi+II7l\nVqp0zcKSb1xVhKpoWux2s32hW6lqwtyMVDwjNSm1MIi4Ls2gKIAML33PT7of\no8LUpCnA/8f/g8tJj5YGwYKbfaoRTBB0QZvrgeJtOyWxa/fZACGPPV8usbIt\nBWfsx4sd+kX+dAPOm03dyjBzcMuujebmG5fdxfuzSnO/WQmmkOa3ZzG90439\nggRJgIdHgYPsYwVlIwonGpvGJN/R7TULvc6REw5cm8ODKLCsZO++URSRR0Oz\nqePlugKN/xiZ15C0m+Z0IezpbdniXEcL0uSBHZDpI0gb9SZfbv6Htdac6QHR\nTG6tLLOAQJIhVe61dbcxoJlMcpUQK9WuYvhx2jsmIz6/pVpuPi6dAMuBPlyT\nuDWUiGngASx4RtwfDwJ6KWYx0nqK43bKcPFRL4AWE8gR97j4sgmfcMyU1cft\nfECtEd9yml22s13PMzuuryndTIHmOMBOqXMGN7XGPeyeAMkBBiEhsOstk9o4\n2PZYfeMaCkKUilX//Nra5Jk6vco6/u/koAYbAFiFrEY9eRBFtp1xjlrujyQS\nVOId\r\n=v4Ul\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHlnjg5a4hNIas99W7SwVkn5PnSZzNj47nq82Z2KkclHAiEA/gJIjGnUESI4FYNV3rDG2x7GmnEoxgvCdLjKc+LKFN4="}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cacheable-lookup_6.0.0_1613567515231_0.9649089626127523"}, "_hasShrinkwrap": false}, "6.0.1": {"name": "cacheable-lookup", "version": "6.0.1", "description": "A cacheable dns.lookup(…) that respects the TTL", "engines": {"node": ">=10.6.0"}, "main": "source/index.js", "types": "index.d.ts", "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text ava && tsd"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/cacheable-lookup.git"}, "keywords": ["dns", "lookup", "cacheable", "ttl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/cacheable-lookup/issues"}, "homepage": "https://github.com/szmarczak/cacheable-lookup#readme", "devDependencies": {"@types/keyv": "^3.1.1", "ava": "^3.8.2", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "keyv": "^4.0.0", "nyc": "^15.0.0", "proxyquire": "^2.1.3", "tsd": "^0.11.0", "quick-lru": "^5.1.0", "xo": "^0.25.3"}, "gitHead": "06fd8fca0c7b9067c18d52e8e7d82fcec96b0744", "_id": "cacheable-lookup@6.0.1", "_nodeVersion": "16.6.2", "_npmVersion": "7.20.3", "dist": {"integrity": "sha512-vaccXt7hUfa5UzrtbdzHTWnL6V6ir39QtLuvGZys32j4HboAeiWVhrcdAm8ecTz1rLubxPhec2n22BBb5/dgVA==", "shasum": "f32ab50c3212302d9f49aa094c8a7593c162af7c", "tarball": "https://registry.npmjs.org/cacheable-lookup/-/cacheable-lookup-6.0.1.tgz", "fileCount": 5, "unpackedSize": 24048, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhIAvxCRA9TVsSAnZWagAAfGMP/REQ5MHKetMoCXm6UsDi\nW7PqfMir3U5zSVYu0ySI/OidB3WhFiaKXMEA3F6VEPkOLJFbzk2DK7J3eTwh\n1EaZCwu3mScwxiKdZL1qXFVvf0KDNfzaiyPjnDGswCCAT4VYnzIqmcfftCj/\nWHOWtHDC1lf+U3G5t5jIbmfMR001zTDgfvzenG5m/XNHSegQpwrD4jUYc+Po\nh5UhliEv6ZlTICcY53bg56FE6GJmy8+jarcijd0g2rv3EwJKyV+9BxE2+YuO\na3WoZxgnQh/JHoVzRN9ZrVTrPN7YqFS9MuepfdN4FW/zLiigY/PTuaHzyy4P\nEMYkKknmxQVPdFqA/emCTaxBANWYy3Cdzq83ivg3KKgf5kmB6d9VImbC4SHq\nIlRqAUTld3oZZ5RD5yFB+GREYA47yYlIkIeEnOR5nJDuJ52zLsscfAoSuaIK\nKjOvqLOgJ9tZHTD/nf1Nrfp2G3VOc9/NtO/O2mgcvoR3BO1KVnGTgQGw05Vp\nXMYyaTmRPvbbvLK/stxpkqJzWTjCv0uc8AsOzaTupplqhiJqYA1gkmf3oZtq\n7FKzaGbuRtemIu4OJqDp5wz9+5nl7a2euAJ+7TybGVgryO0skPFRIyfo33y0\nC7/jJYcOyS8kOYTNKSpeGEgMh0fAuUM1gzQ5xmdW1/yJehpS5ZzjFmq9y6oB\nvtc+\r\n=bMTb\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCmGIDQzsaomV92sW1dijr/h5UdJCwT+t3BlG1XZl0X8AIhAJFo7LXOXk3A2DhhNxdApg3Lf+cj0K/L2x1c38zXBAt5"}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cacheable-lookup_6.0.1_1629490161004_0.8804830957990255"}, "_hasShrinkwrap": false}, "6.0.2": {"name": "cacheable-lookup", "version": "6.0.2", "description": "A cacheable dns.lookup(…) that respects TTL", "engines": {"node": ">=10.6.0"}, "main": "source/index.js", "types": "index.d.ts", "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text ava && tsd"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/cacheable-lookup.git"}, "keywords": ["dns", "lookup", "cacheable", "ttl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/cacheable-lookup/issues"}, "homepage": "https://github.com/szmarczak/cacheable-lookup#readme", "devDependencies": {"@types/keyv": "^3.1.1", "ava": "^3.8.2", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "keyv": "^4.0.0", "nyc": "^15.0.0", "proxyquire": "^2.1.3", "tsd": "^0.11.0", "quick-lru": "^5.1.0", "xo": "^0.25.3"}, "gitHead": "8db73ed46a14d6fab5274316de36cb6c33991afb", "_id": "cacheable-lookup@6.0.2", "_nodeVersion": "16.10.0", "_npmVersion": "7.24.0", "dist": {"integrity": "sha512-9RJkUl1k/A1dFhaRfrEUdISvvou0WKx8LboMO0j1BpsqgAuolwZgwaEtn0dmFMk5HQxpFtHF1bHCnIQMywUpvw==", "shasum": "8df03d6239c91bb9f6394700d7ba4a100abbad67", "tarball": "https://registry.npmjs.org/cacheable-lookup/-/cacheable-lookup-6.0.2.tgz", "fileCount": 5, "unpackedSize": 24318, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD5YLKWRefn4Ps871vonKpqmOZfw0+F+xaP5iDyhCiWPQIgIkIoKiA2Jyc1Y33MKgFdxWGlMLMQPw/1jzDuEGDQUHc="}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cacheable-lookup_6.0.2_1633432812977_0.5894009507615305"}, "_hasShrinkwrap": false}, "6.0.3": {"name": "cacheable-lookup", "version": "6.0.3", "description": "A cacheable dns.lookup(…) that respects TTL", "engines": {"node": ">=10.6.0"}, "main": "source/index.js", "types": "index.d.ts", "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text ava && tsd"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/cacheable-lookup.git"}, "keywords": ["dns", "lookup", "cacheable", "ttl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/cacheable-lookup/issues"}, "homepage": "https://github.com/szmarczak/cacheable-lookup#readme", "devDependencies": {"@types/keyv": "^3.1.1", "ava": "^3.8.2", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "keyv": "^4.0.0", "nyc": "^15.0.0", "proxyquire": "^2.1.3", "tsd": "^0.11.0", "quick-lru": "^5.1.0", "xo": "^0.25.3"}, "gitHead": "4a22f87bc09ce8653ccd98859a469b0b248a5da2", "_id": "cacheable-lookup@6.0.3", "_nodeVersion": "16.10.0", "_npmVersion": "7.24.0", "dist": {"integrity": "sha512-xdwIK7MEC8NpRIt0dx2PL7pTRKaSmDb+zirzuM+cJTRWDfwfVu4XyASkODIU4XbjsyFHKo/tDOPSs64Z3yfFWg==", "shasum": "61d6171f6818fab230666b11f7cf3f5a48df7818", "tarball": "https://registry.npmjs.org/cacheable-lookup/-/cacheable-lookup-6.0.3.tgz", "fileCount": 5, "unpackedSize": 24406, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGIyvJ722hY0CqjxhjOdCDKW5L7V1fJ1Xhf8dWQ1hlU8AiEA0QqaZlVzNeZDB9HyVCIf+WyvEdNlZMkw8611TBRPEuA="}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cacheable-lookup_6.0.3_1634028513377_0.27913642657503046"}, "_hasShrinkwrap": false}, "6.0.4": {"name": "cacheable-lookup", "version": "6.0.4", "description": "A cacheable dns.lookup(…) that respects TTL", "engines": {"node": ">=10.6.0"}, "main": "source/index.js", "types": "index.d.ts", "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text ava && tsd"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/cacheable-lookup.git"}, "keywords": ["dns", "lookup", "cacheable", "ttl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/cacheable-lookup/issues"}, "homepage": "https://github.com/szmarczak/cacheable-lookup#readme", "devDependencies": {"@types/keyv": "^3.1.1", "ava": "^3.8.2", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "keyv": "^4.0.0", "nyc": "^15.0.0", "proxyquire": "^2.1.3", "tsd": "^0.11.0", "quick-lru": "^5.1.0", "xo": "^0.25.3"}, "gitHead": "45b18daf9f0c8406691fad81188e956641e2309d", "_id": "cacheable-lookup@6.0.4", "_nodeVersion": "16.10.0", "_npmVersion": "7.24.0", "dist": {"integrity": "sha512-mbcDEZCkv2CZF4G01kr8eBd/5agkt9oCqz75tJMSIsquvRZ2sL6Hi5zGVKi/0OSC9oO1GHfJ2AV0ZIOY9vye0A==", "shasum": "65c0e51721bb7f9f2cb513aed6da4a1b93ad7dc8", "tarball": "https://registry.npmjs.org/cacheable-lookup/-/cacheable-lookup-6.0.4.tgz", "fileCount": 5, "unpackedSize": 24496, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2lWaCRA9TVsSAnZWagAA/XcP/2iD/rlAgfRRYM+3VpUp\ndOPLuQYS7Hfh9g1+2OscU5qgqfhaCXhzG18UH5Q91Nqt7JgOPQulHMGLgQlW\nrCcJt1A8tmMA2wBTTzqIiWA667ej93EWVcA0BS81412vzwiDJ5jfNG4L6PRF\nRJ+VJ60DMlcTID8TsWAaFjIrQjr/ooiAAbL+iiJhi9acfxCwZR1GSCbaJoRG\nfJBUwP1peUG33ZHkwW4uZTb17uq90x5je7jgxHzdyT+z89fv2WuyoUdMmuxZ\nFf+oxa70OJd97ZneO0T46oKT+KutEYfFYJkB1MKonjqC4cnGl6qWF1n14I6U\n6aBPVBMgRO3hyQFp9P40P02n4pV7w92HZitVggptaEgKXEhMnNJ+N8JaFkDG\nrPKWAWUH2Gd1tkrLPwSxqCbdFTNktHbbs7wsZTJxYqeBeiOPXDgdpemwX7Qj\nF6eJDHyGXcPU91buwLC6tC4ZAa0v0mb+Ih6LnFtMqC0L+v0MDl9UYu53vy8l\n6TbZrs9VgpGFugcGihbQTi1zqjAGZ7lSn/Gtq2g8nozMygXG31ZsIFFBjfsj\nw2Qc3ztV5ekBBPb18gUwop6CCBkacmCuB1/XGhcImUL8h39LKNO4f8KXApaB\nssJsASnFXX2Wo6U8B80Q8h8W+CDC0tjF7O6XWHrM2XlsZcUyMJMpjwU/5N4/\nr+Bn\r\n=pia9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBTfFFMOUVYo4cw8guPrXIuWn7UPSxwVCKxxss7O2WuUAiEA56YRrInO70RejFKv6FkSopRz1k94syl5Ak3L1O2buOc="}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cacheable-lookup_6.0.4_1634548056734_0.45625603265953796"}, "_hasShrinkwrap": false}, "6.1.0": {"name": "cacheable-lookup", "version": "6.1.0", "description": "A cacheable dns.lookup(…) that respects TTL", "engines": {"node": ">=10.6.0"}, "main": "source/index.js", "types": "index.d.ts", "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text ava && tsd"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/cacheable-lookup.git"}, "keywords": ["dns", "lookup", "cacheable", "ttl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/cacheable-lookup/issues"}, "homepage": "https://github.com/szmarczak/cacheable-lookup#readme", "devDependencies": {"@types/keyv": "^3.1.1", "ava": "^3.8.2", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "keyv": "^4.0.0", "nyc": "^15.0.0", "proxyquire": "^2.1.3", "tsd": "^0.11.0", "quick-lru": "^5.1.0", "xo": "^0.25.3"}, "gitHead": "5b08a3610a5ec1328da583a6327111428ff85c42", "_id": "cacheable-lookup@6.1.0", "_nodeVersion": "16.15.0", "_npmVersion": "8.5.3", "dist": {"integrity": "sha512-KJ/Dmo1lDDhmW2XDPMo+9oiy/CeqosPguPCrgcVzKyZrL6pM1gU2GmPY/xo6OQPTUaA/c0kwHuywB4E6nmT9ww==", "shasum": "0330a543471c61faa4e9035db583aad753b36385", "tarball": "https://registry.npmjs.org/cacheable-lookup/-/cacheable-lookup-6.1.0.tgz", "fileCount": 5, "unpackedSize": 24879, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEKYH9LoKMnSRlf2ArrIvHkk3+E1rmRzcmIJqUBmHjn6AiEAuwG4bXqLOlVdDnHA35FVS0ykGGMBrFbW/bWQIgxhniU="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi7aE3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoT3g/+OeZza4u9eOE/o4s1ODvMKqOG5CyprA8RzKXasBrZpIfKEXI8\r\nWCvpLYrSonFJWTZ7v+4GddSzGkWcHirIeF8UVWAa7xlFzhRm2oxnj5KDcWY9\r\n6HgWmT2yKCHlDDz6JFz0NR2eyHElbUeZQ7u0TzxCnBZIT7UIOXGsGGj2gIPY\r\n5p4S4UhYfaEyWuIOvXA0PvtPnNzrH9MJkLf9i0XgfUrqTaAj7S9BOKd0v7/t\r\nyBxB3bnqi1116ilcGTFzD7eubWXnkBaWaU9W3/7qvESObzGcl6GB+VUGovTR\r\nWCQRVvfBkv6SSI9ZjhAUIz4VkZxagA3oFysI1SdbqJ1TE7Tl61zEXDm9KUaU\r\nS11pnIgEW9SegSqOYU85a2J+ZyNbTHPbTc6gLmCoshzn2tbtPVFS+U4YTK05\r\n58xvWGFwup3/7On9Juf1kYaFwwQ7DVAubT3WTfuRTqsbz5Mg09u3NPgg6dPN\r\nJBWV3UYYT8ot1skzzAfUn2YXz4PWdN5MozSCpxacI1O3vMKgejGZ/DzEgLuM\r\ngGBuiuucHkY401JzK7OdaRMlISau4D0NiA+w/ihQIZavqpHDjmwfYFO8DQvC\r\nSY2fMtHsCJutaMGFqPN8G/gs1kn4ybCvqdbZWQaak6QwWwJxVk5mf8+4wFjA\r\n50cl5qoUEC+/C86TBk82Y19wpiz8TdQFTHc=\r\n=1ZT6\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cacheable-lookup_6.1.0_1659740470792_0.8630484021261038"}, "_hasShrinkwrap": false}, "7.0.0": {"name": "cacheable-lookup", "version": "7.0.0", "description": "A cacheable dns.lookup(…) that respects TTL", "engines": {"node": ">=14.16"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./source/index.js"}, "scripts": {"//test": "xo && nyc --reporter=lcovonly --reporter=text ava && tsd", "test": "tsd"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/cacheable-lookup.git"}, "keywords": ["dns", "lookup", "cacheable", "ttl"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/cacheable-lookup/issues"}, "homepage": "https://github.com/szmarczak/cacheable-lookup#readme", "devDependencies": {"@types/keyv": "^3.1.1", "ava": "^4.3.3", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "keyv": "^4.0.0", "nyc": "^15.0.0", "quibble": "^0.6.14", "quick-lru": "^5.1.0", "tsd": "^0.11.0", "xo": "^0.25.3"}, "ava": {"nodeArguments": ["--loader=quibble"]}, "xo": {"rules": {"unicorn/import-index": "off", "import/extensions": "off", "import/no-useless-path-segments": "off"}}, "types": "./index.d.ts", "gitHead": "649ebeb16856e321ac7dc8026759cfffefcd1edb", "_id": "cacheable-lookup@7.0.0", "_nodeVersion": "14.19.3", "_npmVersion": "8.3.2", "dist": {"integrity": "sha512-+qJyx4xiKra8mZrcwhjMRMUhD5NR1R8esPkzIYxX96JiecFoxAXFuz/GpR3+ev4PE1WamHip78wV0vcmPQtp8w==", "shasum": "3476a8215d046e5a3202a9209dd13fec1f933a27", "tarball": "https://registry.npmjs.org/cacheable-lookup/-/cacheable-lookup-7.0.0.tgz", "fileCount": 5, "unpackedSize": 25193, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB1njydILh7eyaOjefdXH9zNQV9FKb9VrXmoV28RaXl/AiEAyQ8/aVRMa+tCo+EDMRQ1slFz2pzseOwSBFx8Yo/U9/A="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjMqISACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrP2Q//c2Ke+uitTb/WLRJPLGGOlQD5Ifep1QhZYfcntvYBzAS97al8\r\n8UdsY589NoahBbWZh3D6nsvqakPqY7V195bT95cz5U09eXx3PEpwDMl3ENlw\r\nb+KBs36bCyAtYkmKrrBJkz+2BUPuKjiy+DUcJUVmgc2nJ17nmkkeRYuTmAZg\r\nYo1TvUzNEAfVUubdqIgISbKH5tfOZvgIj3D1J6oJc3aM2MY8evpYHrEzBjSB\r\ntdXf7HJbqgWjVvUpDC08ZnYI92/YvlmZnHvHtRPDef8ieeY1A0k2p66Ul/pC\r\n8H17TIbuBBu9xH4sJosboCRYysFpJUGH2cP+SEtXkhMdT2m+JfLyHa4gvUU0\r\nbdS29HqgOLeb2749aEJHdjucJksny4I7rfBxPYGS+cqlCDSGmM0mYK8Sxx1e\r\nf9z7pZBrg4dMnNHOu7axarEZJhARtu5QknwgGhuxIQgMqziBYmcV8IihFuZJ\r\nv3AICbyVq2f1xGeQT1pkzqMNMoLmtexRg/QEHQGeQMzZdCzLTxgz/Q3STWst\r\nvHZGBvOCd7sEbCCMpS1fJuMP+OuunBtS4vUEMQed5U17P3Lk9XEkiFT6AoZF\r\n0sqZOV7kN/7QJ2tjdOjyAW7o2uXNhtSthRsegcWps7JZkY5x7oH9Je9ICOrw\r\nWI3hic2cL7uDnJnN3eR4bETVlVa637smEFc=\r\n=rATp\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cacheable-lookup_7.0.0_1664262674590_0.73013195329316"}, "_hasShrinkwrap": false}}, "time": {"created": "2019-01-17T19:53:13.779Z", "0.1.0": "2019-01-17T19:53:13.883Z", "modified": "2022-09-27T07:11:14.853Z", "0.2.0": "2019-04-01T18:06:40.358Z", "0.2.1": "2019-04-01T18:16:10.521Z", "0.2.2": "2020-01-25T04:38:44.570Z", "1.0.0": "2020-01-29T16:36:54.915Z", "2.0.0": "2020-01-31T19:53:21.058Z", "3.0.0": "2020-02-01T15:56:00.104Z", "3.1.0": "2020-02-04T16:07:06.608Z", "3.2.0": "2020-03-04T16:58:39.959Z", "3.2.1": "2020-03-04T17:14:40.258Z", "4.0.0": "2020-03-14T11:24:20.063Z", "4.0.1": "2020-03-14T13:30:51.611Z", "4.1.0": "2020-03-14T20:35:06.637Z", "4.1.1": "2020-03-15T20:45:38.027Z", "4.1.2": "2020-03-20T10:41:08.161Z", "3.2.2": "2020-03-20T10:46:23.511Z", "2.0.1": "2020-03-20T10:50:35.825Z", "4.2.0": "2020-04-27T13:22:38.918Z", "4.2.1": "2020-04-27T16:13:45.345Z", "4.2.2": "2020-04-28T14:19:10.117Z", "4.2.3": "2020-05-04T09:07:15.904Z", "4.3.0": "2020-05-08T10:08:23.413Z", "5.0.0": "2020-05-09T17:38:41.320Z", "5.0.1": "2020-05-09T18:05:38.379Z", "5.0.2": "2020-05-10T20:35:35.360Z", "5.0.3": "2020-05-11T14:01:01.484Z", "5.0.4": "2020-12-08T18:17:43.131Z", "6.0.0": "2021-02-17T13:11:55.517Z", "6.0.1": "2021-08-20T20:09:21.146Z", "6.0.2": "2021-10-05T11:20:13.100Z", "6.0.3": "2021-10-12T08:48:33.468Z", "6.0.4": "2021-10-18T09:07:37.053Z", "6.1.0": "2022-08-05T23:01:11.033Z", "7.0.0": "2022-09-27T07:11:14.775Z"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "description": "A cacheable dns.lookup(…) that respects TTL", "homepage": "https://github.com/szmarczak/cacheable-lookup#readme", "keywords": ["dns", "lookup", "cacheable", "ttl"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/cacheable-lookup.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "bugs": {"url": "https://github.com/szmarczak/cacheable-lookup/issues"}, "license": "MIT", "readme": "# cacheable-lookup\n\n> A cacheable [`dns.lookup(…)`](https://nodejs.org/api/dns.html#dns_dns_lookup_hostname_options_callback) that respects TTL :tada:\n\n[![Node CI](https://github.com/szmarczak/cacheable-lookup/workflows/Node%20CI/badge.svg)](https://github.com/szmarczak/cacheable-lookup/actions)\n[![codecov](https://codecov.io/gh/szmarczak/cacheable-lookup/branch/master/graph/badge.svg)](https://codecov.io/gh/szmarczak/cacheable-lookup)\n[![npm](https://img.shields.io/npm/dm/cacheable-lookup.svg)](https://www.npmjs.com/package/cacheable-lookup)\n[![install size](https://packagephobia.now.sh/badge?p=cacheable-lookup)](https://packagephobia.now.sh/result?p=cacheable-lookup)\n\nMaking lots of HTTP requests? You can save some time by caching DNS lookups :zap:\n\n## Usage\n\n### Using the `lookup` option\n\n```js\nimport http from 'node:http';\nimport CacheableLookup from 'cacheable-lookup';\n\nconst cacheable = new CacheableLookup();\n\nhttp.get('http://example.com', {lookup: cacheable.lookup}, response => {\n\t// Handle the response here\n});\n```\n\n### Attaching CacheableLookup to an Agent\n\n```js\nimport http from 'node:http';\nimport https from 'node:https';\nimport CacheableLookup from 'cacheable-lookup';\n\nconst cacheable = new CacheableLookup();\n\ncacheable.install(http.globalAgent);\ncacheable.install(https.globalAgent);\n\nhttp.get('http://example.com', response => {\n\t// Handle the response here\n});\n```\n\n## API\n\n### new CacheableLookup(options)\n\nReturns a new instance of `CacheableLookup`.\n\n#### options\n\nType: `object`\\\nDefault: `{}`\n\nOptions used to cache the DNS lookups.\n\n##### cache\n\nType: `Map` | [`Keyv`](https://github.com/lukechilds/keyv/)\\\nDefault: `new Map()`\n\nCustom cache instance. If `undefined`, it will create a new one.\n\n**Note**: If you decide to use Keyv instead of the native implementation, the performance will drop by 10x. Memory leaks may occur as it doesn't provide any way to remove all the deprecated values at once.\n\n**Tip**: [`QuickLRU`](https://github.com/sindresorhus/quick-lru) is fully compatible with the Map API, you can use it to limit the amount of cached entries. Example:\n\n```js\nimport http from 'node:http';\nimport CacheableLookup from 'cacheable-lookup';\nimport QuickLRU from 'quick-lru';\n\nconst cacheable = new CacheableLookup({\n\tcache: new QuickLRU({maxSize: 1000})\n});\n\nhttp.get('http://example.com', {lookup: cacheable.lookup}, response => {\n\t// Handle the response here\n});\n```\n\n##### options.maxTtl\n\nType: `number`\\\nDefault: `Infinity`\n\nThe maximum lifetime of the entries received from the specifed DNS server (TTL in seconds).\n\nIf set to `0`, it will make a new DNS query each time.\n\n**Pro Tip**: This shouldn't be lower than your DNS server response time in order to prevent bottlenecks. For example, if you use Cloudflare, this value should be greater than `0.01`.\n\n##### options.fallbackDuration\n\nType: `number`\\\nDefault: `3600` (1 hour)\n\nWhen the DNS server responds with `ENOTFOUND` or `ENODATA` and the OS reports that the entry is available, it will use `dns.lookup(...)` directly for the requested hostnames for the specified amount of time (in seconds).\n\n**Note**: You should avoid setting this to `0` unless the provided DNS servers' database is limited to few domains.\n\n##### options.errorTtl\n\nType: `number`\\\nDefault: `0.15`\n\nThe time how long it needs to remember queries that threw `ENOTFOUND` or `ENODATA` (TTL in seconds).\n\n**Note**: This option is independent, `options.maxTtl` does not affect this.\n\n**Pro Tip**: This shouldn't be lower than your DNS server response time in order to prevent bottlenecks. For example, if you use Cloudflare, this value should be greater than `0.01`.\n\n##### options.resolver\n\nType: `dns.Resolver | dns.promises.Resolver`\\\nDefault: [`new dns.promises.Resolver()`](https://nodejs.org/api/dns.html#dns_class_dns_resolver)\n\nAn instance of [DNS Resolver](https://nodejs.org/api/dns.html#dns_class_dns_resolver) used to make DNS queries.\n\n##### options.lookup\n\nType: `Function`\\\nDefault: [`dns.lookup`](https://nodejs.org/api/dns.html#dns_dns_lookup_hostname_options_callback)\n\nThe fallback function to use when the DNS server responds with `ENOTFOUND` or `ENODATA`.\n\nIf you don't query internal hostnames (such as `localhost`, `database.local` etc.), it is strongly recommended to set this to `false`.\n\n### Entry object\n\nType: `object`\n\n#### address\n\nType: `string`\n\nThe IP address (can be an IPv4 or IPv6 address).\n\n#### family\n\nType: `number`\n\nThe IP family (`4` or `6`).\n\n##### expires\n\nType: `number`\n\n**Note**: This is not present when falling back to `dns.lookup(...)`!\n\nThe timestamp (`Date.now() + ttl * 1000`) when the entry expires.\n\n#### ttl\n\n**Note**: This is not present when falling back to `dns.lookup(...)`!\n\nThe time in seconds for its lifetime.\n\n#### source\n\n**Note**: This is not present when falling back to `dns.lookup(...)`!\n\nWhether this entry was loaded from the cache or came from a query (`cache` or `query`)\n\n### Entry object (callback-style)\n\nWhen `options.all` is `false`, then `callback(error, address, family, expires, ttl)` is called.\\\nWhen `options.all` is `true`, then `callback(error, entries)` is called.\n\n### CacheableLookup instance\n\n#### servers\n\nType: `Array`\n\nThe DNS servers used to make queries. Can be overridden - doing so will clear the cache.\n\n#### [lookup(hostname, options, callback)](https://nodejs.org/api/dns.html#dns_dns_lookup_hostname_options_callback)\n\n#### lookupAsync(hostname, options)\n\nThe asynchronous version of `dns.lookup(…)`.\n\nReturns an [entry object](#entry-object).\\\nIf `options.all` is true, returns an array of entry objects.\n\n##### hostname\n\nType: `string`\n\n##### options\n\nType: `object`\n\nThe same as the [`dns.lookup(…)`](https://nodejs.org/api/dns.html#dns_dns_lookup_hostname_options_callback) options.\n\n#### query(hostname)\n\nAn asynchronous function which returns cached DNS lookup entries.\\\nThis is the base for `lookupAsync(hostname, options)` and `lookup(hostname, options, callback)`.\n\n**Note**: This function has no options.\n\nReturns an array of objects with `address`, `family`, `ttl` and `expires` properties.\n\n#### queryAndCache(hostname)\n\nAn asynchronous function which makes two DNS queries: A and AAAA. The result is cached.\\\nThis is used by `query(hostname)` if no entry in the database is present.\n\nReturns an array of objects with `address`, `family`, `ttl` and `expires` properties.\n\n#### updateInterfaceInfo()\n\nUpdates interface info. For example, you need to run this when you plug or unplug your WiFi driver.\n\n**Note:** Running `updateInterfaceInfo()` will trigger `clear()` only on network interface removal.\n\n#### clear(hostname?)\n\nClears the cache for the given hostname. If the hostname argument is not present, the entire cache will be emptied.\n\n## High performance\n\nPerformed on:\n- Query: `example.com`\n- CPU: i7-7700k\n- CPU governor: performance\n\n```\nCacheableLookup#lookupAsync                x 2,896,251 ops/sec ±1.07% (85 runs sampled)\nCacheableLookup#lookupAsync.all            x 2,842,664 ops/sec ±1.11% (88 runs sampled)\nCacheableLookup#lookupAsync.all.ADDRCONFIG x 2,598,283 ops/sec ±1.21% (88 runs sampled)\nCacheableLookup#lookup                     x 2,565,913 ops/sec ±1.56% (85 runs sampled)\nCacheableLookup#lookup.all                 x 2,609,039 ops/sec ±1.01% (86 runs sampled)\nCacheableLookup#lookup.all.ADDRCONFIG      x 2,416,242 ops/sec ±0.89% (85 runs sampled)\ndns#lookup                                 x 7,272     ops/sec ±0.36% (86 runs sampled)\ndns#lookup.all                             x 7,249     ops/sec ±0.40% (86 runs sampled)\ndns#lookup.all.ADDRCONFIG                  x 5,693     ops/sec ±0.28% (85 runs sampled)\nFastest is CacheableLookup#lookupAsync.all\n```\n\n## Related\n\n- [cacheable-request](https://github.com/lukechilds/cacheable-request) - Wrap native HTTP requests with RFC compliant cache support\n", "readmeFilename": "README.md"}