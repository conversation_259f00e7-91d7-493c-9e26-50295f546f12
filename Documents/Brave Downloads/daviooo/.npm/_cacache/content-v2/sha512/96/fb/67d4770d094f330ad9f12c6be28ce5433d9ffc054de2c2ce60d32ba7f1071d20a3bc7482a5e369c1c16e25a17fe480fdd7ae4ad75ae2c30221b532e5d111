{"name": "make-fetch-happen", "dist-tags": {"legacy": "5.0.2", "latest": "14.0.3"}, "versions": {"0.0.0": {"name": "make-fetch-happen", "version": "0.0.0", "dependencies": {"osenv": "^0.1.4", "cacache": "^6.2.0", "request": "^2.81.0", "bluebird": "^3.5.0", "lru-cache": "^4.0.2", "mississippi": "^1.2.0", "proxy-agent": "^2.0.0", "checksum-stream": "^1.0.2", "promise-inflight": "^1.0.1", "npm-registry-client": "^7.4.6", "realize-package-specifier": "^3.0.3"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.2.0", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "rimraf": "^2.5.4", "standard": "^9.0.1", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "dist": {"shasum": "a38ee3ef30189de758167d5d17f7d1f4e577cfc4", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-0.0.0.tgz", "integrity": "sha512-JJyWVwXtZKWG5YmEmDCnFogiNKsT9VTXIMiwgsLDJ5RPc0JAc9tbO8Ih3Gl9rXNYmKARQk7iIA4oFtVXTWZWVw==", "signatures": [{"sig": "MEUCIHdefKlGPKtD0YB1Rk9yRt8ixt3yoqxFGVrGWbMYXABPAiEA806e4GCq7XyZYVunZtkLXrDZCRroEj2Q/cxUkmD8zxU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.0": {"name": "make-fetch-happen", "version": "1.0.0", "dependencies": {"cacache": "^6.3.0", "bluebird": "^3.5.0", "lru-cache": "^4.0.2", "node-fetch": "^2.0.0-alpha.3", "mississippi": "^1.2.0", "proxy-agent": "^2.0.0", "safe-buffer": "^5.0.1", "promise-retry": "^1.1.1", "checksum-stream": "^1.0.2"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.2.0", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "rimraf": "^2.5.4", "standard": "^9.0.1", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "dist": {"shasum": "5a23cb257074aa98709f45858d795a0d549a8539", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-1.0.0.tgz", "integrity": "sha512-6wCXjEO86cGaZVldbn4Bsnfo4c/Iei2X7tTC+Uam6iFxRXvTvhEHSGcJ8o2m3MlmdVKKCYog1iNk88xZPKsVuA==", "signatures": [{"sig": "MEQCIEJcaiMawDRZQJG6vpGQ5RSa0DXerw1vtlmT36qioVflAiBHi+XLejZnKIm+3ujJmnGOBeqtdYBF6ArTC/iQM/NGmw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.1": {"name": "make-fetch-happen", "version": "1.0.1", "dependencies": {"cacache": "^6.3.0", "bluebird": "^3.5.0", "lru-cache": "^4.0.2", "node-fetch": "^2.0.0-alpha.3", "mississippi": "^1.2.0", "proxy-agent": "^2.0.0", "safe-buffer": "^5.0.1", "promise-retry": "^1.1.1", "checksum-stream": "^1.0.2"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.2.0", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "rimraf": "^2.5.4", "standard": "^9.0.1", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "dist": {"shasum": "d7410876acb1ec69d3222869374aa3bed84e2834", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-1.0.1.tgz", "integrity": "sha512-/J8K4lMbAb7Z6DnOWjeQzVRqXj3BVYgMfTUtvH42+AGNCrMEZG3DRy8owfeRvELOwcBenGcGLS8aP9NkZ3Zmrw==", "signatures": [{"sig": "MEUCIQCNmoC5F18/+E6I0bXiR7p6Jr6EOITZjvO7TwQyfQtrnQIgCgCtubpq4KDliLMrvVEyVJ/UEnS3GZchLtdel3BYlGk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.0": {"name": "make-fetch-happen", "version": "1.1.0", "dependencies": {"cacache": "^6.3.0", "bluebird": "^3.5.0", "lru-cache": "^4.0.2", "node-fetch": "^2.0.0-alpha.3", "mississippi": "^1.2.0", "proxy-agent": "^2.0.0", "safe-buffer": "^5.0.1", "promise-retry": "^1.1.1", "checksum-stream": "^1.0.2"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.2.0", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "rimraf": "^2.5.4", "standard": "^9.0.1", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "dist": {"shasum": "28991e65aa0a5dd447bdb9ac70f041868b36a0e9", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-1.1.0.tgz", "integrity": "sha512-NvOHQNfpb0Q38NbyD/h1bCsQ6eb2/Y25McZEptiSThXtjHA79/3w25qHOnMTdgYM9tDnUIs05233bdRIXDGvGA==", "signatures": [{"sig": "MEUCIQCk56wgMmxC/c2VgFun/1fsmuhJomGXQi4MK80mnyyJ2AIgVX6jSRE7RTEJ0hzhXzlJLjsdYQXs3RVhEQwdOy39MNA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.2.0": {"name": "make-fetch-happen", "version": "1.2.0", "dependencies": {"ssri": "^3.0.2", "cacache": "^7.0.1", "bluebird": "^3.5.0", "lru-cache": "^4.0.2", "node-fetch": "^2.0.0-alpha.3", "mississippi": "^1.2.0", "proxy-agent": "^2.0.0", "safe-buffer": "^5.0.1", "promise-retry": "^1.1.1", "checksum-stream": "^1.0.2"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.2.0", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "rimraf": "^2.5.4", "standard": "^9.0.1", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "dist": {"shasum": "529c8dbb96ce48f5a97aa645965b78078d358469", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-1.2.0.tgz", "integrity": "sha512-kuvFeTqErDnHWtLP+sOF8rUWetibhj2u5e6HeWGHwLqrWOIEEsCaNkI6uk0xCap+aSq+wgLJAUBvwvskQinD2w==", "signatures": [{"sig": "MEQCIAZj35k9A5SQ8Pe0aoohk8znnGG3zFLR+DBmvsWReojRAiARMage+72YasjbA1rK7iW7U/0TQ0Mb+vee+eiRYZDL6g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.2.1": {"name": "make-fetch-happen", "version": "1.2.1", "dependencies": {"ssri": "^4.0.0", "cacache": "^7.0.2", "bluebird": "^3.5.0", "lru-cache": "^4.0.2", "node-fetch": "^2.0.0-alpha.3", "mississippi": "^1.2.0", "proxy-agent": "^2.0.0", "safe-buffer": "^5.0.1", "promise-retry": "^1.1.1", "checksum-stream": "^1.0.2"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.2.0", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "rimraf": "^2.5.4", "standard": "^9.0.1", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "dist": {"shasum": "2e2fbf094d0ee1d238bebb37099366849bc587ef", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-1.2.1.tgz", "integrity": "sha512-II20RlBXhhEbNGyNBvCla7bubPHMIzeIQ3mf0Z3eh75D3s/0n8IVpsCSI3uNLySdS/jUUXS24kBhV9i7sr6Utw==", "signatures": [{"sig": "MEYCIQDWWjqNNftVwJskEp+PdD1oUueRYz2KcbpHKq7xhqz1rgIhAL3WiPWHw4ywY/9gPvEh7C60jdWM1aQ1sc+0BdaEO7Ms", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.3.0": {"name": "make-fetch-happen", "version": "1.3.0", "dependencies": {"ssri": "^4.0.0", "cacache": "^7.0.2", "bluebird": "^3.5.0", "lru-cache": "^4.0.2", "node-fetch": "^2.0.0-alpha.3", "mississippi": "^1.2.0", "proxy-agent": "^2.0.0", "safe-buffer": "^5.0.1", "promise-retry": "^1.1.1", "checksum-stream": "^1.0.2"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.2.0", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "rimraf": "^2.5.4", "standard": "^9.0.1", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "dist": {"shasum": "ccc5dc029dc71d4c8acfd17669c063ca4c7fdde8", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-1.3.0.tgz", "integrity": "sha512-AaPAS39zBuble326AcYdzpm6TqrcspFBh1cGkSqcQJ7bWiN5KbLcJxC9CiJZ28HbgFLsAkzuLfbInskmOUr/2A==", "signatures": [{"sig": "MEYCIQDrJPooMYMuK98LMdX6WMSG3RgTLfcbsgytdiLCAmYh/gIhAIikqOyk4ci1IPEGHnv7Lmz99sUV3ed1mMJDijyyz+tK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.3.1": {"name": "make-fetch-happen", "version": "1.3.1", "dependencies": {"ssri": "^4.0.0", "cacache": "^7.0.2", "bluebird": "^3.5.0", "lru-cache": "^4.0.2", "node-fetch": "^2.0.0-alpha.3", "mississippi": "^1.2.0", "proxy-agent": "^2.0.0", "safe-buffer": "^5.0.1", "promise-retry": "^1.1.1", "checksum-stream": "^1.0.2"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.2.0", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "rimraf": "^2.5.4", "standard": "^9.0.1", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "dist": {"shasum": "516a0ab922d9cb1dac0cf2e506652e171ee61377", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-1.3.1.tgz", "integrity": "sha512-IMqieCKKbJo8htMAznol8LTFkLdLCNqO4ANJU/CmUJeGXWwuU6kBvcGo24vf7/XDaD3+lF5qSlhJZr+1GuZncQ==", "signatures": [{"sig": "MEUCIH83H94uFODDd4nbFsE1fY36YcS/sP6weu8sMMUzceTCAiEA/1epbbAITYNd3YML55W9im+V2bKuIi34TN1jeqsXLEc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.4.0": {"name": "make-fetch-happen", "version": "1.4.0", "dependencies": {"ssri": "^4.0.0", "cacache": "^7.0.2", "bluebird": "^3.5.0", "lru-cache": "^4.0.2", "node-fetch": "^2.0.0-alpha.3", "mississippi": "^1.2.0", "proxy-agent": "^2.0.0", "safe-buffer": "^5.0.1", "promise-retry": "^1.1.1", "checksum-stream": "^1.0.2"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.2.0", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "rimraf": "^2.5.4", "standard": "^9.0.1", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "dist": {"shasum": "0535dad55ed6afb7e24562a070e7ae7e254e046a", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-1.4.0.tgz", "integrity": "sha512-Gw7MxY+4RA/+1qtV9r2/gsBWsLUifPi9356xs29Trm4zQBhFXtPSVOywfL/eFq4a2xoP7G9fZjYVGZj7TlIIYQ==", "signatures": [{"sig": "MEYCIQC2yzKg132phdZVv7yX5tN4kHx84fau8gWTJh6rUKnXyQIhALJIzpYrtSMikjT/v6RJ1QAPRMbdwOllVW4dPCziBQY/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.5.0": {"name": "make-fetch-happen", "version": "1.5.0", "dependencies": {"ssri": "^4.0.0", "cacache": "^7.0.2", "bluebird": "^3.5.0", "lru-cache": "^4.0.2", "node-fetch": "^2.0.0-alpha.3", "mississippi": "^1.2.0", "proxy-agent": "^2.0.0", "safe-buffer": "^5.0.1", "promise-retry": "^1.1.1", "checksum-stream": "^1.0.2"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.2.0", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "rimraf": "^2.5.4", "standard": "^9.0.1", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "dist": {"shasum": "fd5e5e93dc48ede0b16e440915c3ec402038e7d7", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-1.5.0.tgz", "integrity": "sha512-mgA1u3af1k9yGnPDhIyU4C68wimnhaFHLOVsyyuxVmKikjMN63vbQfIdE38l55NAZKn3EvQ+Eep10MAf+dH9AQ==", "signatures": [{"sig": "MEUCIQD0wFZouxYI7/BmEO/cDUKJcu0yFH0CdmPbT2kS/C5zgAIgcuJ4iepJGvHsl2w1fuWA0UZRs6l6HnXE48sDNYwPk/c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.5.1": {"name": "make-fetch-happen", "version": "1.5.1", "dependencies": {"ssri": "^4.0.0", "cacache": "^7.0.3", "bluebird": "^3.5.0", "lru-cache": "^4.0.2", "node-fetch": "^2.0.0-alpha.3", "mississippi": "^1.2.0", "proxy-agent": "^2.0.0", "safe-buffer": "^5.0.1", "promise-retry": "^1.1.1", "checksum-stream": "^1.0.2"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.2.0", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "rimraf": "^2.5.4", "standard": "^9.0.1", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "dist": {"shasum": "6fdf715edf89f456717614cdf9798332435fae6e", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-1.5.1.tgz", "integrity": "sha512-B<PERSON>mmHERF6PqkRaxDicHBz2TCH7896/1OLAl2zbwwuJVK0PwZ+Yq0OPt2l9YOvp6bwvmXwKxlGC0CVOABncwk8g==", "signatures": [{"sig": "MEQCIGMlsvCmC0P6yc8t3pqOZ6Pkwq3TU/7yhua2N4HZXOLuAiBE3ODQTRbqMfo95f+bIW0oXqikuMYgCeUNKwM4Rsu/OA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.6.0": {"name": "make-fetch-happen", "version": "1.6.0", "dependencies": {"ssri": "^4.0.0", "cacache": "^7.0.3", "bluebird": "^3.5.0", "lru-cache": "^4.0.2", "node-fetch": "^2.0.0-alpha.3", "mississippi": "^1.2.0", "proxy-agent": "^2.0.0", "safe-buffer": "^5.0.1", "promise-retry": "^1.1.1", "agentkeepalive": "^3.1.0", "checksum-stream": "^1.0.2"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.2.0", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "rimraf": "^2.5.4", "standard": "^9.0.1", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "dist": {"shasum": "0bce9b86715627ff3fdf07d004b88d93cfa7fb9c", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-1.6.0.tgz", "integrity": "sha512-LNGO1GM3XgpvBD/3OZSx6BR+jX4em3AfY/6F3QJYn08XyO1QGko1OurYC4HFQsj0JKklDYVdN+vEEPCfFfvW0Q==", "signatures": [{"sig": "MEUCIGpuK2RRyzWp5qfLIMKoqelR4Tc2Y4ipAeYRfF5PHLL2AiEA7c1K1CvRweRhQ8BVfdj4GVGthDLd9WL+4aZ5FeSoi14=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.7.0": {"name": "make-fetch-happen", "version": "1.7.0", "dependencies": {"ssri": "^4.0.0", "cacache": "^7.0.3", "bluebird": "^3.5.0", "lru-cache": "^4.0.2", "node-fetch": "^2.0.0-alpha.3", "mississippi": "^1.2.0", "proxy-agent": "^2.0.0", "safe-buffer": "^5.0.1", "promise-retry": "^1.1.1", "agentkeepalive": "^3.1.0", "checksum-stream": "^1.0.2"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.2.0", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "rimraf": "^2.5.4", "standard": "^9.0.1", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "dist": {"shasum": "7b443957c928583fc8013af8bcd7a8485d7ccd01", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-1.7.0.tgz", "integrity": "sha512-9LY8k0Y0GrCMrg/nfh4GmNFnh9J7eD8Czi+mqFSg5i2ZGtduMIh6q2/2UjoPHXoPU8NJR/hSPu1XF4772q1stw==", "signatures": [{"sig": "MEQCICvgHwQSU+k6ZPln4mGwLLvn/eAHbe3NxYkTbbasDY9vAiB/ndYeQ+duo72vxxMJ+2xd8BTV1S8+EpNvj/SiRK25xw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.0": {"name": "make-fetch-happen", "version": "2.0.0", "dependencies": {"ssri": "^4.0.0", "cacache": "^7.0.3", "bluebird": "^3.5.0", "lru-cache": "^4.0.2", "node-fetch": "file:node-fetch-pkg.tgz", "mississippi": "^1.2.0", "safe-buffer": "^5.0.1", "promise-retry": "^1.1.1", "agentkeepalive": "^3.1.0", "checksum-stream": "^1.0.2", "http-proxy-agent": "^1.0.0", "https-proxy-agent": "^1.0.0", "socks-proxy-agent": "^2.0.0"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.2.0", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "rimraf": "^2.5.4", "standard": "^9.0.1", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "dist": {"shasum": "56c938488d73d74f96732c5f74a6e9996034884d", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-2.0.0.tgz", "integrity": "sha512-mTB9guHhpQLnWUN2Q6XefBNqFtlr87X6Whh2zzMwsCF/NOBZ/1k7axd0muTOO4vU9TNTQedLlLnmg7Pjn1yH0w==", "signatures": [{"sig": "MEUCIQDQi2a1oQ8ROfLYTDQKKPqlp/EJ5h4v9ewYfB6KVWMSxQIgRc4PAdR2t/Sfb0isGOzCqYMt51erHmyUqOsXYUhPhLI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.1": {"name": "make-fetch-happen", "version": "2.0.1", "dependencies": {"ssri": "^4.0.0", "cacache": "^7.0.3", "bluebird": "^3.5.0", "lru-cache": "^4.0.2", "node-fetch": "file:node-fetch-pkg.tgz", "mississippi": "^1.2.0", "safe-buffer": "^5.0.1", "promise-retry": "^1.1.1", "agentkeepalive": "^3.1.0", "checksum-stream": "^1.0.2", "http-proxy-agent": "^1.0.0", "https-proxy-agent": "^1.0.0", "socks-proxy-agent": "^2.0.0"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.2.0", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "rimraf": "^2.5.4", "standard": "^9.0.1", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "dist": {"shasum": "4d1a138a1476e2ae6fde8f38052e292dd7b68dfa", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-2.0.1.tgz", "integrity": "sha512-0UUnIg2NEvn8gKFT6TQeDU7F5kkI3EHNCd2mHhDQzUbGBFxBfLSpGXSNtaPJOmHpJBDHjw832slLT5DWUmhTLA==", "signatures": [{"sig": "MEUCIQC7NtCWM3qfGtWInGAaWLnBdU9MQxFwaHt+KaGNbiGXYAIgd5ulTRL5Eh8VizkpfIAP/K8H1nCAsr/IClMeGM3K1B4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.2": {"name": "make-fetch-happen", "version": "2.0.2", "dependencies": {"ssri": "^4.0.0", "cacache": "^7.0.3", "bluebird": "^3.5.0", "lru-cache": "^4.0.2", "node-fetch": "file:node-fetch-pkg.tgz", "mississippi": "^1.2.0", "safe-buffer": "^5.0.1", "promise-retry": "^1.1.1", "agentkeepalive": "^3.1.0", "checksum-stream": "^1.0.2", "http-proxy-agent": "^1.0.0", "https-proxy-agent": "^1.0.0", "socks-proxy-agent": "^2.0.0"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.2.0", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "rimraf": "^2.5.4", "standard": "^9.0.1", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "bundleDependencies": ["node-fetch"], "dist": {"shasum": "3b98a18c65839156942f94afc270c29698fc2d18", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-2.0.2.tgz", "integrity": "sha512-xHp5nLoLUbMSglrSVAvGn2WylJAY3ND0UPGj28ny8VEGi/V1v9au5u0j0gfqpjxdFfFA2Vg/55JIQhPLYQ8HjA==", "signatures": [{"sig": "MEYCIQClCDyX43+bZXGRsD1vxXDSYgetNi5Aw9ZIv53ioXsGIgIhANFM+uyl1uCTMwP0l2wQtiUOvYfyaNjuG2MpFff4lJQY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.3": {"name": "make-fetch-happen", "version": "2.0.3", "dependencies": {"ssri": "^4.0.0", "cacache": "^7.0.3", "bluebird": "^3.5.0", "lru-cache": "^4.0.2", "node-fetch": "2.0.0-alpha.3", "mississippi": "^1.2.0", "safe-buffer": "^5.0.1", "promise-retry": "^1.1.1", "agentkeepalive": "^3.1.0", "checksum-stream": "^1.0.2", "http-proxy-agent": "^1.0.0", "https-proxy-agent": "^1.0.0", "socks-proxy-agent": "^2.0.0"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.2.0", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "rimraf": "^2.5.4", "standard": "^9.0.1", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "bundleDependencies": ["node-fetch"], "dist": {"shasum": "f9a09f9f631c2c7c83f71b914bb01450006b8a7a", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-2.0.3.tgz", "integrity": "sha512-mC39TRHnLKJs08aiNpAZfKwscByKfyesEDUzCd9jnWkLyvoZQsyZPGQNv0RBIxDkQlmX/n/Pghh9hX47HBVzHA==", "signatures": [{"sig": "MEUCIHxom5vZMEl0b2jx++2wmZlYd+o7FTtehX5lgyx0P9TGAiEAz//s8jCXoKFYfevCSwA+o08Qs47Mf8vyFk8gHx331mM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.4": {"name": "make-fetch-happen", "version": "2.0.4", "dependencies": {"ssri": "^4.0.0", "cacache": "^7.0.3", "bluebird": "^3.5.0", "lru-cache": "^4.0.2", "node-fetch": "2.0.0-alpha.3", "mississippi": "^1.2.0", "safe-buffer": "^5.0.1", "promise-retry": "^1.1.1", "agentkeepalive": "^3.1.0", "checksum-stream": "^1.0.2", "http-proxy-agent": "^1.0.0", "https-proxy-agent": "^1.0.0", "socks-proxy-agent": "^2.0.0"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.2.0", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "rimraf": "^2.5.4", "standard": "^9.0.1", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "bundleDependencies": ["node-fetch"], "dist": {"shasum": "d41655a1df360aac1b3133af918ad011184ada3a", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-2.0.4.tgz", "integrity": "sha512-cIS8KseDmuEszYjP1QQVmqP1irplJW8eJC+jcCqpchyKMuEewJjg0TeOVVS5KMuh46kJWhXlsccZMnuIb13cRA==", "signatures": [{"sig": "MEQCIDbBWXvL+qfURONJ8S/Ru8FREG88RIUkBmUZgn+vyb34AiAFWYU6KpkKjMqq6+LWbSO8Yfntbx1pfECiuuZGFTNGtg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.1.0": {"name": "make-fetch-happen", "version": "2.1.0", "dependencies": {"ssri": "^4.0.0", "cacache": "^7.0.3", "bluebird": "^3.5.0", "lru-cache": "^4.0.2", "node-fetch": "2.0.0-alpha.3", "mississippi": "^1.2.0", "safe-buffer": "^5.0.1", "promise-retry": "^1.1.1", "agentkeepalive": "^3.1.0", "checksum-stream": "^1.0.2", "http-proxy-agent": "^1.0.0", "https-proxy-agent": "^1.0.0", "socks-proxy-agent": "^2.0.0", "http-cache-semantics": "^3.7.3"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.2.0", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "rimraf": "^2.5.4", "standard": "^9.0.1", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "bundleDependencies": ["node-fetch"], "dist": {"shasum": "8881c5a2e5fbbfe30693449fe5d6676176609477", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-2.1.0.tgz", "integrity": "sha512-DElIIhCrYxrTq1jUaZ5ZkmQPL8mgHxUO2pmHKjT13HY+MF5H3WvhqRiumF3W0Kx4+rlnnX4HqIrAPQU+TxNY7g==", "signatures": [{"sig": "MEYCIQCYZLMpUJOZwM3qezXKzYGA/iWpjx/9JZyyCCmJepOHLgIhAKp/8zaXhig1W6my7HMYe6fzMtPDYVVvcGPtbMpjWNH8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.2.0": {"name": "make-fetch-happen", "version": "2.2.0", "dependencies": {"ssri": "^4.0.0", "cacache": "^7.0.3", "bluebird": "^3.5.0", "lru-cache": "^4.0.2", "node-fetch": "2.0.0-alpha.3", "mississippi": "^1.2.0", "safe-buffer": "^5.0.1", "promise-retry": "^1.1.1", "agentkeepalive": "^3.1.0", "checksum-stream": "^1.0.2", "http-proxy-agent": "^1.0.0", "https-proxy-agent": "^1.0.0", "socks-proxy-agent": "^2.0.0", "http-cache-semantics": "^3.7.3"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.2.0", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "rimraf": "^2.5.4", "standard": "^10.0.1", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "bundleDependencies": ["node-fetch"], "dist": {"shasum": "311cdf2aed6fdb7b2c04bb462458231be5e687c8", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-2.2.0.tgz", "integrity": "sha512-BI9kCsgk/sz449aH21cvgAi8ln9mjTQpjf+D32/ZKna7OM5oRVVqzRvD9aJp9pfBcPM+Zxa2ogcx6NLSAsIjoA==", "signatures": [{"sig": "MEYCIQClfc6dcpmzq2q94Dehflntdlz+TwYCrQmxU3Rh1P0h5AIhALHlkqiLTCORTnkuza6HdsYtVTn6aegSb3iGqTL0PUwj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.2.1": {"name": "make-fetch-happen", "version": "2.2.1", "dependencies": {"ssri": "^4.0.0", "cacache": "^7.0.3", "lru-cache": "^4.0.2", "node-fetch": "2.0.0-alpha.3", "mississippi": "^1.2.0", "promise-retry": "^1.1.1", "agentkeepalive": "^3.1.0", "http-proxy-agent": "^1.0.0", "https-proxy-agent": "^1.0.0", "socks-proxy-agent": "^2.0.0", "http-cache-semantics": "^3.7.3"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.2.0", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "rimraf": "^2.5.4", "bluebird": "^3.5.0", "standard": "^10.0.1", "safe-buffer": "^5.0.1", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "bundleDependencies": ["node-fetch"], "dist": {"shasum": "1812d51f20838bb6c760bcd13fc039620d74befa", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-2.2.1.tgz", "integrity": "sha512-3jINJOfe9mKjajHAGYVkAdxGnRh0lp85bD2iuV1YeftK69JB1ItLwUmGG2Pe5uAWWcTeaDe5zWke2NYzd2ymvg==", "signatures": [{"sig": "MEQCIGV60zteFM9rWumBlpX1grQKOGoWVnrDEYxjExvjKlEGAiAgLkzTXoC9HqvnWo9M1nORLoFtgE7WuWjuYMkzDokpbg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.2.2": {"name": "make-fetch-happen", "version": "2.2.2", "dependencies": {"ssri": "^4.0.0", "cacache": "^7.0.3", "lru-cache": "^4.0.2", "node-fetch": "2.0.0-alpha.3", "mississippi": "^1.2.0", "promise-retry": "^1.1.1", "agentkeepalive": "^3.1.0", "http-proxy-agent": "^1.0.0", "https-proxy-agent": "^1.0.0", "socks-proxy-agent": "^2.0.0", "http-cache-semantics": "^3.7.3"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.2.0", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "rimraf": "^2.5.4", "bluebird": "^3.5.0", "standard": "^10.0.1", "safe-buffer": "^5.0.1", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "bundleDependencies": ["node-fetch"], "dist": {"shasum": "e7c965ed2205742f520b084cf0d8364529e4d93e", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-2.2.2.tgz", "integrity": "sha512-uJVL5FVNeMRECtsrX5Uy14uCH/qbsxLQSh/ItueftUoSeK4TRyuiMEqgxp8XPDvtk6iBuNM2MIgqaUnUhHt+4A==", "signatures": [{"sig": "MEUCIQCtFYKfNeEW0/afRpWRoGrX7aYexOn07A4IefXOmJ4vGQIgN7IWO9ZbQKITVPB8cSSeKp+nv/M6Il9GI1tYE+rtrM8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.2.3": {"name": "make-fetch-happen", "version": "2.2.3", "dependencies": {"ssri": "^4.0.0", "cacache": "^7.0.3", "lru-cache": "^4.0.2", "node-fetch": "2.0.0-alpha.3", "mississippi": "^1.2.0", "promise-retry": "^1.1.1", "agentkeepalive": "^3.1.0", "http-proxy-agent": "^1.0.0", "https-proxy-agent": "^1.0.0", "socks-proxy-agent": "^2.0.0", "http-cache-semantics": "^3.7.3"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.2.0", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "rimraf": "^2.5.4", "bluebird": "^3.5.0", "standard": "^10.0.1", "safe-buffer": "^5.0.1", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "bundleDependencies": ["node-fetch"], "dist": {"shasum": "880e457dbf43fae23cf2ae2acbc19e056fe0cd3f", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-2.2.3.tgz", "integrity": "sha512-vuCTYaSAimeubpfXn70v7W2BtequSJXGQqvOaBGAsiF2o7wNS7FCHEKDV0BZAX2ZiEDcm8I+1vnC6S/AC4WdOg==", "signatures": [{"sig": "MEUCID2s/U0AKbcHSoXmmIXoK8IQ1/mTiFClXAewyhAqNBf/AiEA//gAPogU7mYVPUys0nPMtoSSPBhh7H/70LNqHtz5rRQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.2.4": {"name": "make-fetch-happen", "version": "2.2.4", "dependencies": {"ssri": "^4.1.2", "cacache": "^7.0.5", "lru-cache": "^4.0.2", "node-fetch": "2.0.0-alpha.3", "mississippi": "^1.2.0", "promise-retry": "^1.1.1", "agentkeepalive": "^3.1.0", "http-proxy-agent": "^1.0.0", "https-proxy-agent": "^1.0.0", "socks-proxy-agent": "^2.0.0", "http-cache-semantics": "^3.7.3"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.2.0", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "rimraf": "^2.5.4", "bluebird": "^3.5.0", "standard": "^10.0.1", "safe-buffer": "^5.0.1", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "bundleDependencies": ["node-fetch"], "dist": {"shasum": "3aeb3213e37887f40391426dbd0937ddf3de3dbf", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-2.2.4.tgz", "integrity": "sha512-ND8x3bYIYjLuA9CCPQTV+vwFNy8IqpB/Cwrl/JcEikoDF+gTbA4rOEe+TSZd/Px9mtbdOvQptBp0on4xew/h0g==", "signatures": [{"sig": "MEYCIQDkXsLJbTAO8RTZQKloLVQdvxCl/ySxszShnpcpJ9xvwAIhAJq4SHnu78nEryy1dDyq3eZMVTD39/kcaCMlIyDrx9WK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.2.5": {"name": "make-fetch-happen", "version": "2.2.5", "dependencies": {"ssri": "^4.1.2", "cacache": "^8.0.0", "lru-cache": "^4.0.2", "node-fetch": "2.0.0-alpha.3", "mississippi": "^1.2.0", "promise-retry": "^1.1.1", "agentkeepalive": "^3.1.0", "http-proxy-agent": "^1.0.0", "https-proxy-agent": "^1.0.0", "socks-proxy-agent": "^2.0.0", "http-cache-semantics": "^3.7.3"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.2.0", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "rimraf": "^2.5.4", "bluebird": "^3.5.0", "standard": "^10.0.1", "safe-buffer": "^5.0.1", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "bundleDependencies": ["node-fetch"], "dist": {"shasum": "36af286e02fcaceafd824f336dce79230f5621f7", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-2.2.5.tgz", "integrity": "sha512-5A3hIlAXaBbVGSld/E1GDUKexgk9C7BVHHsvxm0Lul8jFUJyZ1Nn9yK2btFwISUvrP1bkRGZwhzxyICn/BhKqQ==", "signatures": [{"sig": "MEQCIGYpKk3jhNqfbRlaaagVYrLZV9uVXMVZppsRqKQR94YzAiB8DouwesnXODBIjkrz4SDxKCP85oeUmC0aF48rLCIhiA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.2.6": {"name": "make-fetch-happen", "version": "2.2.6", "dependencies": {"ssri": "^4.1.2", "cacache": "^8.0.0", "lru-cache": "^4.0.2", "mississippi": "^1.2.0", "promise-retry": "^1.1.1", "agentkeepalive": "^3.1.0", "node-fetch-npm": "^1.0.0", "http-proxy-agent": "^1.0.0", "https-proxy-agent": "^1.0.0", "socks-proxy-agent": "^2.0.0", "http-cache-semantics": "^3.7.3"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.2.0", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "rimraf": "^2.5.4", "bluebird": "^3.5.0", "standard": "^10.0.1", "safe-buffer": "^5.0.1", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "dist": {"shasum": "4d0fdaebeb769607d1098c4e63d9e5e262e321d2", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-2.2.6.tgz", "integrity": "sha512-H2tdmzCxgjEWgVjlAIfXuCNBxUzX8s6OWlDEFcYLnlLPvOo98iGsxBS/GZ3zJ2YKSX1mixt0HH0b+pDvN2Ad3g==", "signatures": [{"sig": "MEYCIQDzo1VPRS3JKJ9rCl0KJWFEWKN+Cmasl7KsoPhbdeZ/egIhAIyu7qgJQef2NApJgFlHw0Oewb979lJtXWwercCXQHu7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.3.0": {"name": "make-fetch-happen", "version": "2.3.0", "dependencies": {"ssri": "^4.1.2", "cacache": "^8.0.0", "lru-cache": "^4.0.2", "mississippi": "^1.2.0", "promise-retry": "^1.1.1", "agentkeepalive": "^3.1.0", "node-fetch-npm": "^1.0.0", "http-proxy-agent": "^1.0.0", "https-proxy-agent": "^1.0.0", "socks-proxy-agent": "^2.0.0", "http-cache-semantics": "^3.7.3"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.2.0", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "rimraf": "^2.5.4", "bluebird": "^3.5.0", "standard": "^10.0.1", "safe-buffer": "^5.0.1", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "dist": {"shasum": "899586db6b4c882938e4f58cd1edc3ee480f4e83", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-2.3.0.tgz", "integrity": "sha512-zADOJkEqVADFrXlYLL2z8k/7xwwKZjQEWUIEwIBRx+4ikLhuH4KpXYPMjFo4qNU0pkvYZ7z+JGZhKSMbPMcdSQ==", "signatures": [{"sig": "MEUCIENOzAhGMWso41xsyssPasnVA2EOqmaiX3+UMlwvgm4hAiEAzXC+oF0EJJXMwm4GdskkYebjBUzXlslabNKb9M1h9Pg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.4.0": {"name": "make-fetch-happen", "version": "2.4.0", "dependencies": {"ssri": "^4.1.2", "cacache": "^9.0.0", "lru-cache": "^4.0.2", "mississippi": "^1.2.0", "promise-retry": "^1.1.1", "agentkeepalive": "^3.1.0", "node-fetch-npm": "^1.0.0", "http-proxy-agent": "^1.0.0", "https-proxy-agent": "^1.0.0", "socks-proxy-agent": "^2.0.0", "http-cache-semantics": "^3.7.3"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.2.0", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "rimraf": "^2.5.4", "bluebird": "^3.5.0", "standard": "^10.0.1", "safe-buffer": "^5.0.1", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "dist": {"shasum": "0dbf1127f20064bfcbe6993b91dd810027f11fda", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-2.4.0.tgz", "integrity": "sha512-uLPoFJlJEEx+Ih6Jqp7W7tGTDttHaKAMyqOuw+dBk87d/2BbSNmTS10a7/P3bGIaUCwID04JNLbi8qSJ2yUwnQ==", "signatures": [{"sig": "MEQCIDVhTNx5NR2/KKyb3KJNqjlZOsSsQKGFUU3ZfbghQpdiAiBJDa0oU6YY4MepoZbaWWyw/fFpHEEEHfxg6pk0MYwoZQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.4.1": {"name": "make-fetch-happen", "version": "2.4.1", "dependencies": {"ssri": "^4.1.2", "cacache": "^9.0.0", "lru-cache": "^4.0.2", "mississippi": "^1.2.0", "promise-retry": "^1.1.1", "agentkeepalive": "^3.1.0", "node-fetch-npm": "^1.0.0", "http-proxy-agent": "^1.0.0", "https-proxy-agent": "^1.0.0", "socks-proxy-agent": "^2.0.0", "http-cache-semantics": "^3.7.3"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.2.0", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "rimraf": "^2.5.4", "bluebird": "^3.5.0", "standard": "^10.0.1", "safe-buffer": "^5.0.1", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "dist": {"shasum": "657fc48b1400a17ae0b52fec2b75c340c0e7ac64", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-2.4.1.tgz", "integrity": "sha512-2E1HHdn/jjCo2gw8flGxTSE1YuIrvBq8y+MOUuLOSID9bO0q7n8f46keauEehyIrxoQCTG7Ou0R9iVeD03YIjg==", "signatures": [{"sig": "MEYCIQCBmYcgmd4T9yCH0y4TZKAy2n+XSX7HL1H8jVXbPT/jpgIhAKCZ6Xxgmwq3O8xJIT9Nj8N5cSqW+AfYEsaish59o66f", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.4.2": {"name": "make-fetch-happen", "version": "2.4.2", "dependencies": {"ssri": "^4.1.2", "cacache": "^9.0.0", "lru-cache": "^4.0.2", "mississippi": "^1.2.0", "promise-retry": "^1.1.1", "agentkeepalive": "^3.1.0", "node-fetch-npm": "^1.0.0", "http-proxy-agent": "^1.0.0", "https-proxy-agent": "^1.0.0", "socks-proxy-agent": "^2.0.0", "http-cache-semantics": "^3.7.3"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.2.0", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "rimraf": "^2.5.4", "bluebird": "^3.5.0", "standard": "^10.0.1", "safe-buffer": "^5.0.1", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "dist": {"shasum": "0b1f778e55f2faa6cfa464fd6c45645a2fe43ce4", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-2.4.2.tgz", "integrity": "sha512-sEf0VCgPv7ISmbzX/sZ+xynlY26WJuB5aTG0WNVuhz1Ed0MkB5BnOfb3vS7GZRHO5n9t0Cyrlw/zlDlQKxLWvA==", "signatures": [{"sig": "MEQCIC3WWCq5qsVAEQtP3GQpxa9avsm/ZfN5MVpEi45QVlGFAiBAB5lkhdUa1PJd0bdDrYnN5HigsqZ3TRxe246qbwa9VA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.4.3": {"name": "make-fetch-happen", "version": "2.4.3", "dependencies": {"ssri": "^4.1.2", "cacache": "^9.0.0", "lru-cache": "^4.0.2", "mississippi": "^1.2.0", "promise-retry": "^1.1.1", "agentkeepalive": "^3.1.0", "node-fetch-npm": "^2.0.0", "http-proxy-agent": "^1.0.0", "https-proxy-agent": "^1.0.0", "socks-proxy-agent": "^2.0.0", "http-cache-semantics": "^3.7.3"}, "devDependencies": {"nyc": "^10.3.2", "tap": "^10.2.0", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.1.0", "rimraf": "^2.5.4", "bluebird": "^3.5.0", "standard": "^10.0.1", "safe-buffer": "^5.0.1", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "dist": {"shasum": "a9e894f213cc4628fde0859a589a90da96f4e4d8", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-2.4.3.tgz", "integrity": "sha512-Vi+Y+uUnWki65KG6RjjW6J4o10XJivCyhvSh4TB/XC5gNtD9MbxlyOVkAFKTNCj1w1wcDw7HWaVPGj0CPfAyhw==", "signatures": [{"sig": "MEUCIC6dKpXHEsNFBD4kh5lPkDGEGLCbJeFh6cVi4LhFKsCeAiEA2RuFAzteBPqaHNtcH5qpGTjuhbdt+WGy03dgfr3sdU0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.4.4": {"name": "make-fetch-happen", "version": "2.4.4", "dependencies": {"ssri": "^4.1.2", "cacache": "^9.0.0", "lru-cache": "^4.0.2", "mississippi": "^1.2.0", "promise-retry": "^1.1.1", "agentkeepalive": "^3.1.0", "node-fetch-npm": "^2.0.0", "http-proxy-agent": "^1.0.0", "https-proxy-agent": "^1.0.0", "socks-proxy-agent": "^2.0.0", "http-cache-semantics": "^3.7.3"}, "devDependencies": {"nyc": "^10.3.2", "tap": "^10.2.0", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.1.0", "rimraf": "^2.5.4", "bluebird": "^3.5.0", "standard": "^10.0.1", "safe-buffer": "^5.0.1", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "dist": {"shasum": "a94589e10bdbfc80f5afd6ec6bd670ebcf3e6a17", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-2.4.4.tgz", "integrity": "sha512-thx4k+usAEic2pjJls9au1DmT/Vm8AacnaMWx4+k6yMEc+VuutwMP+dVML9v6e/mGB1qYH3UNYjBEJmBpZwwhw==", "signatures": [{"sig": "MEYCIQDxdijgv0D9zEK8wa+FOEcn5ZRiG+uyGKoZL1kXS2ZTHQIhAPhRmrHzbAk8Teh8LZeBB1u7CppNh7nIstZxjYJPcLZI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.4.5": {"name": "make-fetch-happen", "version": "2.4.5", "dependencies": {"ssri": "^4.1.2", "cacache": "^9.0.0", "lru-cache": "^4.0.2", "mississippi": "^1.2.0", "promise-retry": "^1.1.1", "agentkeepalive": "^3.1.0", "node-fetch-npm": "^2.0.0", "http-proxy-agent": "^1.0.0", "https-proxy-agent": "^1.0.0", "socks-proxy-agent": "^2.0.0", "http-cache-semantics": "^3.7.3"}, "devDependencies": {"nyc": "^10.3.2", "tap": "^10.2.0", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.1.0", "rimraf": "^2.5.4", "bluebird": "^3.5.0", "standard": "^10.0.1", "safe-buffer": "^5.0.1", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "dist": {"shasum": "ee68d9783aff7532833f05537673cd281e336885", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-2.4.5.tgz", "integrity": "sha512-/PS1MQRvxv3p10SwgyEZyE6ZeIX6b+yM+tZMoSe+0VztYMAecg88nNjIH/nCvuSX0v0qTnKO+G9PxKgciRqbwQ==", "signatures": [{"sig": "MEUCIQCFqInbi/0unzscI4+HnPpZxjMQqYFwF43wojux6zfvJQIgY5NMJJ7M8bUTXhfA72NKOfvQQG4JHzHKfifqMHhzq4o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.4.6": {"name": "make-fetch-happen", "version": "2.4.6", "dependencies": {"ssri": "^4.1.2", "cacache": "^9.0.0", "lru-cache": "^4.0.2", "mississippi": "^1.2.0", "promise-retry": "^1.1.1", "agentkeepalive": "^3.1.0", "node-fetch-npm": "^2.0.0", "http-proxy-agent": "^1.0.0", "https-proxy-agent": "^1.0.0", "socks-proxy-agent": "^2.0.0", "http-cache-semantics": "^3.7.3"}, "devDependencies": {"nyc": "^10.3.2", "tap": "^10.2.0", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.1.0", "rimraf": "^2.5.4", "bluebird": "^3.5.0", "standard": "^10.0.1", "safe-buffer": "^5.0.1", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "dist": {"shasum": "83b573e975b65f8ed7a79aabbc1ac8963abe6c98", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-2.4.6.tgz", "integrity": "sha512-KXn9dhQWlA/MpjcxK6WbkZ5L/N95EeWR5GAV55vNZnx9Ql+qqdmbOyhWuUi++5I6osOdWwDm96Sz/P54IlKjrg==", "signatures": [{"sig": "MEQCIDrkI40icRa/zvvupgxC+0vnTbMJR6BJnibgBRVS9JofAiAIEuupcb3SmUR5EQj4x3aMyvQZl5KWpviu0LBaDsxIuw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.4.7": {"name": "make-fetch-happen", "version": "2.4.7", "dependencies": {"ssri": "^4.1.3", "cacache": "^9.2.4", "lru-cache": "^4.0.2", "mississippi": "^1.2.0", "promise-retry": "^1.1.1", "agentkeepalive": "^3.1.0", "node-fetch-npm": "^2.0.1", "http-proxy-agent": "^1.0.0", "https-proxy-agent": "^1.0.0", "socks-proxy-agent": "^2.0.0", "http-cache-semantics": "^3.7.3"}, "devDependencies": {"nyc": "^10.3.2", "tap": "^10.2.0", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.1.0", "rimraf": "^2.5.4", "bluebird": "^3.5.0", "standard": "^10.0.1", "safe-buffer": "^5.0.1", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "dist": {"shasum": "7c320d0555d02c9e7e283e869fb7a0b7afc6927a", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-2.4.7.tgz", "integrity": "sha512-8vXuEMB7+XqmrgK1A5TDSLaLohnfjRv9R4hY1U9bbTEkrCqejFqO3qrfqeofxa+o2vrZYgqgks8luSwWyuy60w==", "signatures": [{"sig": "MEQCIBY8VN9i9Qx6A4Wh4HYnxwdWooA9fXCPUx65EKvTJmlsAiBqjjeM0rmWR+rOhNMpdwv+M+FFOAs8ZxVH88dLKCKbsQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.4.8": {"name": "make-fetch-happen", "version": "2.4.8", "dependencies": {"ssri": "^4.1.3", "cacache": "^9.2.4", "lru-cache": "^4.0.2", "mississippi": "^1.2.0", "promise-retry": "^1.1.1", "agentkeepalive": "^3.1.0", "node-fetch-npm": "^2.0.1", "http-proxy-agent": "^1.0.0", "https-proxy-agent": "^1.0.0", "socks-proxy-agent": "^2.0.0", "http-cache-semantics": "^3.7.3"}, "devDependencies": {"nyc": "^10.3.2", "tap": "^10.2.0", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.1.0", "rimraf": "^2.5.4", "bluebird": "^3.5.0", "standard": "^10.0.1", "safe-buffer": "^5.0.1", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "dist": {"shasum": "2f607531ea325cf634fc275fd37f2162ee7c9612", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-2.4.8.tgz", "integrity": "sha512-ByJP2FudbLXxanTFDhsER91i9u6g+nCx14VG3Ttj48A2/81n8VNJrpxtPZ85pABRhHl5zY6xCRJuMCRkc4fyIg==", "signatures": [{"sig": "MEUCIQDdlB7mlN2sPdfwULPFrCbXSpn54NQLsVdA76dd1MU4yQIgP5bYBfbIjOrdDnbCD6ugbGxVZCzbmpLo4K/uhFkrmUI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.4.9": {"name": "make-fetch-happen", "version": "2.4.9", "dependencies": {"ssri": "^4.1.3", "cacache": "^9.2.4", "lru-cache": "^4.0.2", "mississippi": "^1.2.0", "promise-retry": "^1.1.1", "agentkeepalive": "^3.1.0", "node-fetch-npm": "^2.0.1", "http-proxy-agent": "^1.0.0", "https-proxy-agent": "^1.0.0", "socks-proxy-agent": "^2.0.0", "http-cache-semantics": "^3.7.3"}, "devDependencies": {"nyc": "^10.3.2", "tap": "^10.2.0", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.1.0", "rimraf": "^2.5.4", "bluebird": "^3.5.0", "standard": "^10.0.1", "safe-buffer": "^5.0.1", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "dist": {"shasum": "245b799e35da3ec05a45e6ef31f9c34df7d1e0c1", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-2.4.9.tgz", "integrity": "sha512-/qh6T1E2gBD31bhutxeFehcHDwbBJJ7F+7w8bNAzPbacqfTwEpeo7W5SVQqciCSfNex51SjnEyw1XuK4zDn+Fw==", "signatures": [{"sig": "MEUCIECIrTGq52UJG6jpWCLIM8fBVqdUuHH4gVeled+PeR0HAiEArExN5N1zDOu+6mBcG3ilTr+xArFB7F3HblynHjBzMIo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.4.10": {"name": "make-fetch-happen", "version": "2.4.10", "dependencies": {"ssri": "^4.1.4", "cacache": "^9.2.6", "lru-cache": "^4.0.2", "mississippi": "^1.2.0", "promise-retry": "^1.1.1", "agentkeepalive": "^3.1.0", "node-fetch-npm": "^2.0.1", "http-proxy-agent": "^1.0.0", "https-proxy-agent": "^1.0.0", "socks-proxy-agent": "^2.1.0", "http-cache-semantics": "^3.7.3"}, "devDependencies": {"nyc": "^10.3.2", "tap": "^10.2.0", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.1.0", "rimraf": "^2.5.4", "bluebird": "^3.5.0", "standard": "^10.0.1", "safe-buffer": "^5.0.1", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "dist": {"shasum": "5e52244a4cd80be925f5c8118a38ad0c2ceb4a81", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-2.4.10.tgz", "integrity": "sha512-ZD9SRSC0TGlThOnnlPdDuUcKE74D8XeriPoNYEtWgBGAzKp4P7tIDWN6LvLvMSkd23HDbaUMsu9g3dO3NavZIg==", "signatures": [{"sig": "MEYCIQCJSyqvQ0Wu1bxqJKtDqA6n6imS280fPxjEkWHYz1ZahQIhAKzEBTCjI1+f7ye3IbCqrF6c0Akz7q/3xMJpc6DKsmEl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.4.11": {"name": "make-fetch-happen", "version": "2.4.11", "dependencies": {"ssri": "^4.1.5", "cacache": "^9.2.7", "lru-cache": "^4.0.2", "mississippi": "^1.2.0", "promise-retry": "^1.1.1", "agentkeepalive": "^3.1.0", "node-fetch-npm": "^2.0.1", "http-proxy-agent": "^1.0.0", "https-proxy-agent": "^1.0.0", "socks-proxy-agent": "^2.1.0", "http-cache-semantics": "^3.7.3"}, "devDependencies": {"nyc": "^11.0.2", "tap": "^10.3.3", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.1.0", "rimraf": "^2.5.4", "bluebird": "^3.5.0", "standard": "^10.0.1", "safe-buffer": "^5.1.0", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "dist": {"shasum": "0b29967da2dc995e88f778dd0c673855ec7613e3", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-2.4.11.tgz", "integrity": "sha512-3dbl9CVS9Y0hqVzcD5HBYnaNUGw3XWpmeTqGT6ACRrmKS7WKwSDfv5+Gd1K0X/Mt52hsvZwtOH8hrNIulYkhag==", "signatures": [{"sig": "MEQCIFwy4gRyTXgw8DYD61PTf0PZhOwOEGHLU5akaHU7KDYQAiBm5yFvajw5K5MgayGRB3nL1SoCeAZwQwql7o4XC3YMWA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.4.12": {"name": "make-fetch-happen", "version": "2.4.12", "dependencies": {"ssri": "^4.1.5", "cacache": "^9.2.7", "lru-cache": "^4.0.2", "mississippi": "^1.2.0", "promise-retry": "^1.1.1", "agentkeepalive": "^3.1.0", "node-fetch-npm": "^2.0.1", "http-proxy-agent": "^1.0.0", "https-proxy-agent": "^1.0.0", "socks-proxy-agent": "^2.1.0", "http-cache-semantics": "^3.7.3"}, "devDependencies": {"nyc": "^11.0.2", "tap": "^10.3.3", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.1.0", "rimraf": "^2.5.4", "bluebird": "^3.5.0", "standard": "^10.0.1", "safe-buffer": "^5.1.0", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.0.0"}, "dist": {"shasum": "5e16f97b3e1fc30017da82ba4b4a5529e773f399", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-2.4.12.tgz", "integrity": "sha512-7nqjivrk3WElOcoikc8tvRM3appfY2G6DO/rrRS86dxJE/DuO6QuPJqNkXmPf+DqVTeO+tmBqrtEjEqMWR2WSw==", "signatures": [{"sig": "MEQCIBBiJtMVDGwlYV/y26LZR1f7tgBrdfZcRhnokpNHVljQAiBSgBSpW+pWO28w1DVcOSREXX1JuIRGuBr/QG/bm2F42w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.4.13": {"name": "make-fetch-happen", "version": "2.4.13", "dependencies": {"ssri": "^4.1.6", "cacache": "^9.2.9", "lru-cache": "^4.1.1", "mississippi": "^1.2.0", "promise-retry": "^1.1.1", "agentkeepalive": "^3.3.0", "node-fetch-npm": "^2.0.1", "http-proxy-agent": "^2.0.0", "https-proxy-agent": "^2.0.0", "socks-proxy-agent": "^3.0.0", "http-cache-semantics": "^3.7.3"}, "devDependencies": {"nyc": "^11.0.3", "tap": "^10.7.0", "nock": "^9.0.6", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.1.2", "rimraf": "^2.5.4", "bluebird": "^3.5.0", "standard": "^10.0.1", "safe-buffer": "^5.1.1", "weallbehave": "^1.0.0", "weallcontribute": "^1.0.7", "standard-version": "^4.2.0"}, "dist": {"shasum": "3139ba2f4230a8384e7ba394534816c872ecbf4b", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-2.4.13.tgz", "integrity": "sha512-73CsTlMRSLdGr7VvOE8iYl/ejOSIxyfRYg7jZhepGGEqIlgdq6FLe2DEAI5bo813Jdg5fS/Ku62SRQ/UpT6NJA==", "signatures": [{"sig": "MEYCIQCMnjlLzyIM2uWxQJvbO7NjX+y1llfg0u6wfzxsfz9yowIhAO5pxcPKnT3FTpXrZGBu96EaIv6txYfpy8/cNlFTJdmk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.5.0": {"name": "make-fetch-happen", "version": "2.5.0", "dependencies": {"ssri": "^4.1.6", "cacache": "^9.2.9", "lru-cache": "^4.1.1", "mississippi": "^1.2.0", "promise-retry": "^1.1.1", "agentkeepalive": "^3.3.0", "node-fetch-npm": "^2.0.1", "http-proxy-agent": "^2.0.0", "https-proxy-agent": "^2.0.0", "socks-proxy-agent": "^3.0.0", "http-cache-semantics": "^3.7.3"}, "devDependencies": {"nyc": "^11.0.3", "tap": "^10.7.0", "nock": "^9.0.14", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.1.2", "rimraf": "^2.5.4", "bluebird": "^3.5.0", "standard": "^10.0.1", "safe-buffer": "^5.1.1", "weallbehave": "^1.0.0", "require-inject": "^1.4.2", "weallcontribute": "^1.0.7", "standard-version": "^4.2.0"}, "dist": {"shasum": "08c22d499f4f30111addba79fe87c98cf01b6bc8", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-2.5.0.tgz", "integrity": "sha512-JPD5R43T02wIkcxjcmZuR7D06nB20fMR8aC9VEyYsSBXvJa5hOR/QhCxKY+5SXhy3uU5OUY/r+A6r+fJ2mFndA==", "signatures": [{"sig": "MEUCIQCEVT2FsAb4X5S4nmFUR4dPoTtAc28cGHxN66enQaOdUQIgePnMb6eejjwQ0BLxxpfkN0/N8liV9/8vBcDQXPgZyxg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.6.0": {"name": "make-fetch-happen", "version": "2.6.0", "dependencies": {"ssri": "^5.0.0", "cacache": "^10.0.0", "lru-cache": "^4.1.1", "mississippi": "^1.2.0", "promise-retry": "^1.1.1", "agentkeepalive": "^3.3.0", "node-fetch-npm": "^2.0.2", "http-proxy-agent": "^2.0.0", "https-proxy-agent": "^2.1.0", "socks-proxy-agent": "^3.0.1", "http-cache-semantics": "^3.8.0"}, "devDependencies": {"nyc": "^11.0.3", "tap": "^10.7.0", "nock": "^9.0.14", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.1.2", "rimraf": "^2.5.4", "bluebird": "^3.5.0", "standard": "^10.0.1", "safe-buffer": "^5.1.1", "weallbehave": "^1.0.0", "require-inject": "^1.4.2", "weallcontribute": "^1.0.7", "standard-version": "^4.2.0"}, "dist": {"shasum": "8474aa52198f6b1ae4f3094c04e8370d35ea8a38", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-2.6.0.tgz", "integrity": "sha512-FFq0lNI0ax+n9IWzWpH8A4JdgYiAp2DDYIZ3rsaav8JDe8I+72CzK6PQW/oom15YDZpV5bYW/9INd6nIJ2ZfZw==", "signatures": [{"sig": "MEUCIQC0S4h8oBoBieytcDjwUWoVT1PbSTOhB1W1sslt/p7qugIgJN53mFjOh1dh7ivNCGVT8EOz9F96smGbXENJH1F68/o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.0.0": {"name": "make-fetch-happen", "version": "3.0.0", "dependencies": {"ssri": "^5.2.4", "cacache": "^10.0.4", "lru-cache": "^4.1.2", "mississippi": "^3.0.0", "promise-retry": "^1.1.1", "agentkeepalive": "^3.4.1", "node-fetch-npm": "^2.0.2", "http-proxy-agent": "^2.1.0", "https-proxy-agent": "^2.2.0", "socks-proxy-agent": "^3.0.1", "http-cache-semantics": "^3.8.1"}, "devDependencies": {"nyc": "^11.4.1", "tap": "^11.1.2", "nock": "^9.2.3", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.1.2", "rimraf": "^2.6.2", "bluebird": "^3.5.1", "standard": "^11.0.0", "safe-buffer": "^5.1.1", "weallbehave": "^1.0.0", "require-inject": "^1.4.2", "weallcontribute": "^1.0.7", "standard-version": "^4.3.0"}, "dist": {"shasum": "7b661d2372fc4710ab5cc8e1fa3c290eea69a961", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-3.0.0.tgz", "fileCount": 8, "integrity": "sha512-FmWY7gC0mL6Z4N86vE14+m719JKE4H0A+pyiOH18B025gF/C113pyfb4gHDDYP5cqnRMHOz06JGdmffC/SES+w==", "signatures": [{"sig": "MEUCIQDOcMWLZ2RdmsChgYyclfYYQa10mQS+myhURZQxpej39AIgfAXItHyj8GaKADWija8c+nur5Oz+DgGqh/fWNgeeFZ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64241}}, "4.0.0": {"name": "make-fetch-happen", "version": "4.0.0", "dependencies": {"ssri": "^5.3.0", "cacache": "^11.0.0", "lru-cache": "^4.1.2", "mississippi": "^3.0.0", "promise-retry": "^1.1.1", "agentkeepalive": "^3.4.1", "node-fetch-npm": "^2.0.2", "http-proxy-agent": "^2.1.0", "https-proxy-agent": "^2.2.1", "socks-proxy-agent": "^4.0.0", "http-cache-semantics": "^3.8.1"}, "devDependencies": {"tap": "^11.1.3", "nock": "^9.2.3", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.1.2", "rimraf": "^2.6.2", "bluebird": "^3.5.1", "standard": "^11.0.1", "safe-buffer": "^5.1.1", "weallbehave": "^1.0.0", "require-inject": "^1.4.2", "weallcontribute": "^1.0.7", "standard-version": "^4.3.0"}, "dist": {"shasum": "7a7d7cf5ec6d6255cbcd55abf3ea467e3e7e715b", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-4.0.0.tgz", "fileCount": 8, "integrity": "sha512-RdvnYWRAFgAi2CcxKQ5V8v1VDjxWkU33+kcAoO+t/h3wuHyR5jmbWb6PmUoRqCtBmjwxkZ9PcDJFEsvHBPW9MQ==", "signatures": [{"sig": "MEUCIEKaAFAQJVxxlzC/FFu7TFrYpS0tXwDyYtfhZvZlK8kmAiEA8ApvWUVoWfKPyf8TyWaV2v4D38htRPlxfOman379NIY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64505}}, "4.0.1": {"name": "make-fetch-happen", "version": "4.0.1", "dependencies": {"ssri": "^6.0.0", "cacache": "^11.0.1", "lru-cache": "^4.1.2", "mississippi": "^3.0.0", "promise-retry": "^1.1.1", "agentkeepalive": "^3.4.1", "node-fetch-npm": "^2.0.2", "http-proxy-agent": "^2.1.0", "https-proxy-agent": "^2.2.1", "socks-proxy-agent": "^4.0.0", "http-cache-semantics": "^3.8.1"}, "devDependencies": {"tap": "^11.1.3", "nock": "^9.2.3", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.1.2", "rimraf": "^2.6.2", "bluebird": "^3.5.1", "standard": "^11.0.1", "safe-buffer": "^5.1.1", "weallbehave": "^1.0.0", "require-inject": "^1.4.2", "weallcontribute": "^1.0.7", "standard-version": "^4.3.0"}, "dist": {"shasum": "141497cb878f243ba93136c83d8aba12c216c083", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-4.0.1.tgz", "fileCount": 8, "integrity": "sha512-7R5ivfy9ilRJ1EMKIOziwrns9fGeAD4bAha8EB7BIiBBLHm2KeTUGCrICFt2rbHfzheTLynv50GnNTK1zDTrcQ==", "signatures": [{"sig": "MEUCIQC0C9U8iyBsbT/DEkzqX/qTf+40PvW9H3WqqBPRaJ5QgwIgAkp58h6KQK2cBSq+WUQh01s2D7x+AXaBZyumDOvHVPs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64488}}, "4.0.2": {"name": "make-fetch-happen", "version": "4.0.2", "dependencies": {"ssri": "^6.0.0", "cacache": "^11.3.3", "lru-cache": "^5.1.1", "mississippi": "^3.0.0", "promise-retry": "^1.1.1", "agentkeepalive": "^3.4.1", "node-fetch-npm": "^2.0.2", "http-proxy-agent": "^2.1.0", "https-proxy-agent": "^2.2.1", "socks-proxy-agent": "^4.0.0", "http-cache-semantics": "^3.8.1"}, "devDependencies": {"tap": "^12.7.0", "nock": "^9.2.3", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.1.2", "rimraf": "^2.6.2", "bluebird": "^3.5.1", "standard": "^11.0.1", "safe-buffer": "^5.1.1", "weallbehave": "^1.0.0", "require-inject": "^1.4.2", "weallcontribute": "^1.0.7", "standard-version": "^4.3.0"}, "dist": {"shasum": "2d156b11696fb32bffbafe1ac1bc085dd6c78a79", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-4.0.2.tgz", "fileCount": 8, "integrity": "sha512-YMJrAjHSb/BordlsDEcVcPyTbiJKkzqMf48N8dAJZT9Zjctrkb6Yg4TY9Sq2AwSIQJFn5qBBKVTYt3vP5FMIHA==", "signatures": [{"sig": "MEYCIQCO8JVAWmjLc0q3H3NM2QL/snlpKFAyLwJaNEbtcepsOgIhAM0PWxnqf8tUxv8PYLrnkqx51XFUj7d2iBIKm5oeGZMS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdGqFhCRA9TVsSAnZWagAA6rUP/0PTeimdKH/T/6B0c2UH\nKdA/IcxR/LvByYXHuLOKZuZFnRCQMfhdMkVkiL3w46tJ5qXGfwoUVq0qELe3\njT3mTZto0qtiOQzv9i/YcfU+bggBzQQhRrxTLHiGewaxU2TOgHSWuGYP1cbC\nFtUpOoihfYV6Z/6wh1OidMTZ/7C1RdzShqPW7bmbxfCOs0r6WQCxjnxc2oua\nCrdHz5/VetVNRvwZJ0Ek4Fc2qzTr31NSG3cJRDtk/N2OxVKDuWSQOX39Ar85\n48/yYS98PUBJpDya0N++MrodA/R1OcBzoNdkXMEln9XmaJmIiRxQhXuBT2eR\nhLU9JO0nGJ0K9A0OIYahFVoq+tZ5erVpbiqoN882djtNLvW/mk+4kD0z1e75\nsUHXb3eDYDM64vty3J/eAsHTPMbKFGdBoAQjVhQrH/57GJeMIL0snAm9+Wy/\n2T288Hq5vWwlhaGzCemrPpZdyOEFbjbavsqKNJfgzYajRKDXEQD8FMiVxUnC\nKuyIqjmFY8K9Jcfr00DABt8lmMN/7PAx3FS1P/mhCFAXSOmcN6PBdNUt2aHM\nytsi6WUj6uyuFkj2W1yTbK+4t7hn80Ry8S8HLJiyztlZw0kKOOQg7W4bCJwi\nHbb/UhjD1ZWPEbom/wJujwZOz0ZdfHwe+gtS124LmM+I085+hHa7xcqjOgic\n7HX+\r\n=O9mQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "5.0.0": {"name": "make-fetch-happen", "version": "5.0.0", "dependencies": {"ssri": "^6.0.0", "cacache": "^12.0.0", "lru-cache": "^5.1.1", "mississippi": "^3.0.0", "promise-retry": "^1.1.1", "agentkeepalive": "^3.4.1", "node-fetch-npm": "^2.0.2", "http-proxy-agent": "^2.1.0", "https-proxy-agent": "^2.2.1", "socks-proxy-agent": "^4.0.0", "http-cache-semantics": "^3.8.1"}, "devDependencies": {"tap": "^12.7.0", "nock": "^9.2.3", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.1.2", "rimraf": "^2.6.2", "bluebird": "^3.5.1", "standard": "^11.0.1", "safe-buffer": "^5.1.1", "weallbehave": "^1.0.0", "require-inject": "^1.4.2", "weallcontribute": "^1.0.7", "standard-version": "^4.3.0"}, "dist": {"shasum": "a8e3fe41d3415dd656fe7b8e8172e1fb4458b38d", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-5.0.0.tgz", "fileCount": 8, "integrity": "sha512-nFr/vpL1Jc60etMVKeaLOqfGjMMb3tAHFVJWxHOFCFS04Zmd7kGlMxo0l1tzfhoQje0/UPnd0X8OeGUiXXnfPA==", "signatures": [{"sig": "MEUCIC1+0MKVDKhWyVAJ0jgXQdpt64SdMPPjqFHbFbUUAJhMAiEA5Pwg4mvXd9a5P0wehM3o5MADe5826LJcEE0HLjxWkJk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64845, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdLQ/DCRA9TVsSAnZWagAA8YoP/0axoyiRqnfZ928niuZL\nNWGK61ATZeGTQa97re7nxCs2GXTtSaetfKZWGnotn/Rz8CN2yfOpzD9Xy63e\n4KBGjM2Bv2cCkUuSexs/sMt7hLvxPp9WH4fEOeUH74eZBB49e1+uZ5w3A51T\nOIY2BLgYSb/VKw/cScUjHoNZxeMaJfIxf/pY8QACl6DBF5DcbO7/vWclvVrQ\nmy1jPEEy5mPCuwBacqxXvRnNQ4IyvbkJDtRKMZMlaExVEABXI1CciWxNr+FQ\nFtqtzhr1uuLF3WiYKbIPXb4hzn+Mw2u4mbhYGETjpgT22dVGVOp1S4uHBmQb\nq3xYthZVdgeV9NXcocUetNWRYcUMD5MS1fTJ60AUYQQuSMobcq0w5ERkpQhE\noTI/4dW4GJedervewcLDggtqYbUa8vQEpEfI5TIJ6thtdA4A8WVBMKNXma7x\n8K7/0L90usfJOOBk+DuLUEniVSfMfFoq/mw+qn9TLmGxY4kDoNxMTF8Bfr0y\nRYWVz/YST3jNYWDpzgBjHx/iWVtgAcFZXxyJ1C7SWfuh58E+2UW1gvj1C61v\ncU9MhXbWSoeyXsdwluemADT4mKP2TwFSPV1Vtc9QNFyMiSKnW7Vwn+eepUvk\n0YAGAq3+eRCriA7er8SkmjKJ97Jn2OM7kg2pDxLLDoLZ/MwbHIKug85+Nwaj\nt2Kf\r\n=uKuu\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.0.0": {"name": "make-fetch-happen", "version": "6.0.0", "dependencies": {"ssri": "^7.0.1", "cacache": "^13.0.1", "minipass": "^3.0.0", "lru-cache": "^5.1.1", "promise-retry": "^1.1.1", "agentkeepalive": "^3.4.1", "minipass-fetch": "^1.1.2", "minipass-flush": "^1.0.5", "http-proxy-agent": "^2.1.0", "minipass-collect": "^1.0.2", "https-proxy-agent": "^2.2.1", "minipass-pipeline": "^1.2.2", "socks-proxy-agent": "^4.0.0", "http-cache-semantics": "^3.8.1"}, "devDependencies": {"tap": "^14.6.9", "nock": "^9.2.3", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.1.2", "rimraf": "^2.6.2", "standard": "^11.0.1", "safe-buffer": "^5.1.1", "weallbehave": "^1.0.0", "require-inject": "^1.4.2", "weallcontribute": "^1.0.7", "standard-version": "^4.3.0"}, "dist": {"shasum": "34bca862432be387f65caa55cba0cdc27810741d", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-6.0.0.tgz", "fileCount": 8, "integrity": "sha512-0c4s/5ktozfRWPvii/Bang+an/YCPzkTRFSzEC6uBMTUp02ZskNGgNFm0rmmMLYlFN5OvW7HruLIDSS2fjQjOA==", "signatures": [{"sig": "MEQCIGagbRcWsJ5pPHzfszBudd3kyAJjHRfqGPuMWlEaLQGVAiAfBDoLzVxYYNSGttCQ3N+dDuv47MUTEkqoaVxeB4ME9A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66172, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdk4+gCRA9TVsSAnZWagAAFtkP/3g5htSzMB86VTMND9JX\nX2HZFHkXNEa0wRFryWsSVHWhj0OXc1iA9OkOgdGx5hJDaeBEDAW52HlVZyR9\nOu8O6/xccUAR1iZZg/IHAA9vawX/D0nh+LBs4sqmanmgdFkbqMROCLWpfUZN\nDYcFLsshN1MyYvOCOU8VpY2hNCb+grZTFz9rtllXSKsIthlfC1hssZf3HCyq\nvarMk4H3JPIbWKkTVW4aJ+w1eEpITIQnr8SZsipTmMrIB/oj4YYdyhREWlEP\nbtDhmd8neaHdr3N2heG3pTz68tm+77+Zf4F/mN4Z/tTWTPy0sgD8XdbOfPA4\nmglWwadDVrW67OVbgYvtRPgREl+efuCClR1xzVpa9GG7AFVPDqbwYZ29Ebrv\nhOaKXAydSqESAVkqJMAwb41DVkmtisI6Ax8U0E2zzvb5MZsYpu5uFjHS3B0X\nbGfDiCxOtZ3DJ/X8lVc2bHi3GZi7tIddg6K4KVbBy1/c+sOtbCQ2EV8JNe7O\npAbqCqxqwH9+18XpVFEYaLu8q2aXxrqobaCcBbI6ZzXlnIPghMGCseDF3lVj\nwO9ZRiSLd/Ccn/m9e+sUJID4n5POEvmEXXUMXXGv9V5EVq5wBobBz3/cBrwJ\n6gE0TcDSFMoTjyEJ7iQkSJMdbGKYkslW0Xbql77OIoIwr6f+kPmvBrXHpzI/\nOvvT\r\n=JmEI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8"}}, "6.0.1": {"name": "make-fetch-happen", "version": "6.0.1", "dependencies": {"ssri": "^7.0.1", "cacache": "^13.0.1", "minipass": "^3.0.0", "lru-cache": "^5.1.1", "promise-retry": "^1.1.1", "agentkeepalive": "^3.4.1", "minipass-fetch": "^1.1.2", "minipass-flush": "^1.0.5", "http-proxy-agent": "^2.1.0", "minipass-collect": "^1.0.2", "https-proxy-agent": "^2.2.3", "minipass-pipeline": "^1.2.2", "socks-proxy-agent": "^4.0.0", "http-cache-semantics": "^3.8.1"}, "devDependencies": {"tap": "^14.6.9", "nock": "^9.2.3", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.1.2", "rimraf": "^2.6.2", "standard": "^11.0.1", "safe-buffer": "^5.1.1", "weallbehave": "^1.0.0", "require-inject": "^1.4.2", "weallcontribute": "^1.0.7", "standard-version": "^7.0.0"}, "dist": {"shasum": "3992b9cc1a0564ac72eef3a9c27427487a985649", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-6.0.1.tgz", "fileCount": 8, "integrity": "sha512-PAHjh/ohpW1lvc6eWHirvEoyQgRGa9g1Ugrddf0udiRdaVI/R4TMOqzGMEeM5tBzW6p4J+7kAQIKLgAxvLuT/A==", "signatures": [{"sig": "MEYCIQDiX/Mveir2eiBT2/oRqtomD/Q//ddUXldZhP+6AKdJQAIhAM6oK3t0opccgabknbx+M8xkwmy8o35APXI593EegFtA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66263, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdr5mTCRA9TVsSAnZWagAAweEP/Ao9D/r5sTBFsS64xdJa\nv06XyY1FN50tS2DHCk5fD3eGJ8iTNLFRpB4H3TqB4u245OHEbGlcFZO2s8Wz\nErv0AhKlbCbyhTyw2/ZJaOdGJ/BG1FjZYSP74QGCvLQSSyJymq9ZmS+g9UmV\ncoq4/OBl7Wlub1sF658MAZcmzn4cX8Y2MnU/6D6+rSD0eJD/ZMc6kSYJ9hnt\nETh5BT42D8pER2qRisLzS2WrbsilUzFLwXLYFznlNv+3K21wFv/lNwoskgM9\nCNWh3cYKYYEt8+cIpnC9P5HER41A+27vQ6Ztk8ceoOBv1pzDN7aUVY14Snu1\nyx+bV8oOysqNQ+LWMGMp7/XKY9SIMsh1SC5VdEtcs13o7fHNR0/s3zysC4Nu\nWDRivjLz6G00cbHrGSW+sum4TRtKSOxf8Riy3hqefkXftGCyH0q4qm0E/yQk\naHfQpueEZ0fZVtvIlc588qopMMYs6DTgs6Y/+MtoGsxzw65EyCbSRS5TDlys\nNi9UOgvMB21YNLunts6x+YDNZyAYIKq5JIlmnnUnclZMe6tCoqBZ5DnOtMYs\nesjXBbMTKAEYqu9N6TR8iY8GCDbVBzMdVu+PA0BX7+EEvakDfLqD1v0V8PlT\ntZruOra7VQsygs5wnBgN+Ly3gCKMHPs7SP9CLxmsKUqX9WSsZG35NE6yKtwy\n+0xd\r\n=S/eZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8"}}, "5.0.1": {"name": "make-fetch-happen", "version": "5.0.1", "dependencies": {"ssri": "^6.0.0", "cacache": "^12.0.0", "lru-cache": "^5.1.1", "mississippi": "^3.0.0", "promise-retry": "^1.1.1", "agentkeepalive": "^3.4.1", "node-fetch-npm": "^2.0.2", "http-proxy-agent": "^2.1.0", "https-proxy-agent": "^2.2.3", "socks-proxy-agent": "^4.0.0", "http-cache-semantics": "^3.8.1"}, "devDependencies": {"tap": "^12.7.0", "nock": "^9.2.3", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.1.2", "rimraf": "^2.6.2", "bluebird": "^3.5.1", "standard": "^11.0.1", "safe-buffer": "^5.1.1", "weallbehave": "^1.0.0", "require-inject": "^1.4.2", "weallcontribute": "^1.0.7", "standard-version": "^4.3.0"}, "dist": {"shasum": "fac65400ab5f7a9c001862a3e9b0f417f0840175", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-5.0.1.tgz", "fileCount": 8, "integrity": "sha512-b4dfaMvUDR67zxUq1+GN7Ke9rH5WvGRmoHuMH7l+gmUCR2tCXFP6mpeJ9Dp+jB6z8mShRopSf1vLRBhRs8Cu5w==", "signatures": [{"sig": "MEUCIDEd4K1ArypbMl+CUT4HvjX5mSe0BXu+3uw8EdQhKtWXAiEAyo+dzsbISR4K0nTtF9Oe+Qa3aSTTVHQqOUBdzDalHgo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64973, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdr5otCRA9TVsSAnZWagAAyZ0P/RDJ1pMK49zImleqhHV7\nsvT9PUz6RM6GwHYTH1UdFmm4Lb8NeRzJks3fTUJ6X2tzFqWqscnHdA31S9kq\nUI373xG1gZtwYlEnacdxSLF5EXRsNVNWHn1JVLX8fRHUK9gmopWfwQSoc6uH\np/EVib50LLe1c5Sy9PIa7LAoTDhXm2cBlDEj+FpkqWSm7K3nvnBGNP2CQh3B\nLIndOM51RVEih321a72lTP0fwBZWiyd8kFR+tOGCNh2gS1J/lacSLoNXXw5m\n+y5V1FQ9AUiMPeKLCWlR6u1f1ehmG/NGX1zd6TFqFbx9Nr0J/hFW0+hvHBev\n9H2MhhItbZgP70ZtOrMcuL+wvCKKb/gmRfJpLsnvs12CGyMeCVnPpbNjRt7g\nnUZTDvFP9SSg1pEc1ELxBi50CBJT12h7DeZxhP/YUiUowadUvqDKG1aH4cky\nfmZ4cTwLvoeudUjAblxesxaK/4ZXA0V0H6e2ZMDywXdCdbnEBM9hAb1cHxSY\n2X1j0om33cR8rCGj3VDj8t8cPZ3TmXiUEphj1iCv3BuKlyAnLMSnxrel/3uG\nQmT+hO+WM83HbYTMr/+iNFKFL79UgKkztqAYrFZ3kFbbtW1G+hdLFHtISlpq\nTmxrgA5vomkqwVINCSPds3O3anfSfaNyoMN1bBKHh/tHnEDCWmgvDjGsrhqx\nro2d\r\n=auLZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "5.0.2": {"name": "make-fetch-happen", "version": "5.0.2", "dependencies": {"ssri": "^6.0.0", "cacache": "^12.0.0", "lru-cache": "^5.1.1", "mississippi": "^3.0.0", "promise-retry": "^1.1.1", "agentkeepalive": "^3.4.1", "node-fetch-npm": "^2.0.2", "http-proxy-agent": "^2.1.0", "https-proxy-agent": "^2.2.3", "socks-proxy-agent": "^4.0.0", "http-cache-semantics": "^3.8.1"}, "devDependencies": {"tap": "^12.7.0", "nock": "^9.2.3", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.1.2", "rimraf": "^2.6.2", "bluebird": "^3.5.1", "standard": "^11.0.1", "safe-buffer": "^5.1.1", "weallbehave": "^1.0.0", "require-inject": "^1.4.2", "weallcontribute": "^1.0.7", "standard-version": "^4.3.0"}, "dist": {"shasum": "aa8387104f2687edca01c8687ee45013d02d19bd", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-5.0.2.tgz", "fileCount": 8, "integrity": "sha512-07JHC0r1ykIoruKO8ifMXu+xEU8qOXDFETylktdug6vJDACnP+HKevOu3PXyNPzFyTSlz8vrBYlBO1JZRe8Cag==", "signatures": [{"sig": "MEYCIQD21xyU59ddzrOnVqZ9lYj3VAEO+8tLF6pfujyJjRPNrAIhAJatdHE0N0OvY7puZ0Zyr6JgeJHRwv82dI0hdcmBOE51", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65417, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdzWoyCRA9TVsSAnZWagAA2VYP/1YNm+JRRh/53Aei1PDc\nfh+pu/YF2V5p+1zSgGHxIKQ8vdZ/htTXbTY6Y96DBVkU9iU7DCuqd4o0DSXt\nGeguobt2dhSNyCiM4/7h9XxpIdh/JtEj3mbDyrWiwZt6JVyQAKWc+QK4iyeD\nwPpDORwT46atOBzEiQuMyoMnGyw/wKESwskzd19Zxaya+jQkP6q6fet6vFHl\nvWv9NElvfXucSZ3ae07UW7OYEkFDXzEf31h/e+PTYXOuLomGpBIQu6GpplWR\nr63qkuaY37BH/TtUHzp7hg+bVM0UFA+hllAP07gYG4EJ5iWazgI5ujOatkgW\np9xBret5FvKVrh1oN/ZpuaS1cmI0U84XH/orkoqifrmR3fxTDEpcKDvHQ/gf\nYyWtia+Dn+51SbgDfULdOzu4zaYb7/78eqCdNTSqLeXmZ/6sJwTqPGfjbg+2\nkY0AQtzU/fh5pt7EUgV2OVhtjkwCKJD0nJ51NeifwrOP1p+XjRR/RFaVgHWF\nL5RNqqEnJYHY9luDqR8HzPypO0l5t8V7rOyiDztDhxMBpTf2zdPzThqOLn8r\nTrH4NjlrfZ4MKosvKYOzdGKdwqVJLmkDjNxG2xQH+sWl6vnc+aceHmBiGF+J\nTzwB/VT4+Jzr77zmCWsNSClTO9yT1aDkn0oBFoMezjgF2WKL8NuXK7w7X5GX\nXONX\r\n=Purz\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.1.0": {"name": "make-fetch-happen", "version": "6.1.0", "dependencies": {"ssri": "^7.0.1", "cacache": "^13.0.1", "minipass": "^3.0.0", "lru-cache": "^5.1.1", "promise-retry": "^1.1.1", "agentkeepalive": "^3.4.1", "minipass-fetch": "^1.1.2", "minipass-flush": "^1.0.5", "http-proxy-agent": "^2.1.0", "minipass-collect": "^1.0.2", "https-proxy-agent": "^3.0.1", "minipass-pipeline": "^1.2.2", "socks-proxy-agent": "^4.0.0", "http-cache-semantics": "^3.8.1"}, "devDependencies": {"tap": "^14.6.9", "nock": "^9.2.3", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.1.2", "rimraf": "^2.6.2", "standard": "^11.0.1", "safe-buffer": "^5.1.1", "weallbehave": "^1.0.0", "require-inject": "^1.4.2", "weallcontribute": "^1.0.7", "standard-version": "^7.0.0"}, "dist": {"shasum": "7d3c647913136efd73e94bde899f85fcd3eda09e", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-6.1.0.tgz", "fileCount": 8, "integrity": "sha512-Q/RHcHofC+BnleSGDiO3SQQX2mCvXz639s+kJ7+loR4RPT487itVJ8RWIyBEfnqpnkaUFaWgNyV6CxT7eyDdEA==", "signatures": [{"sig": "MEUCIDh0l3b0phFNdkoJjXEHVrLAprLQvFgqJP+0avgKwcHUAiEAvqyIs6a6E6IouqSLwaYnPKpHkoCSfkRfM7qLvfQ3lMg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66514, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdzW83CRA9TVsSAnZWagAAG5YP/0ys9oGfEqyMdXylbS/q\nQkx+ePrDb6r8HqVPfODZESWr7m8fNq3JrsxCk1IkU7Ytg5BoJinfyJ9KPTBT\ncYbDy+SoUJxZYXvKfmHv2B90/JN6ZPdYztjYg624X3zqcG8xclu7KHJ7AirN\n/CxY2og+izf4Rn4Q4X0eux/Zb/UcAHwiysDS5vYNXyt3byv/DbPvjyIAgny7\nUZJPxT8jkVAHrooDwv4tUANBx6wofO74EGbxnK2OSEzM0f/Q0LRi9oFIWnXF\nkgS3+CSOAqBv+3WI2giR6tMh3rNo2IXVN3izz0v2ndbgbGp5LppwitVL5nVh\niDmcmSnztzr2fn/zY1qUERVEJa4PkR3lG444yKAw4rtjANe/J5hktTfJgYQ7\nIzG3gNqf6uldHEiTacHVMDDXe2OsMGMGKI+LJtRT93I6+BESgJxJojIHbIeB\nJtcf0KOXXheC62VcJNqDN3b1oHTP+pwbUz0nS9tCeiTJAs1vROkQqSfFrYkh\nFhHpHvWixrf6JoIiCuRnMq7WES1yPuvjw3PG8mcAo3KvgWVEWmQ7w5veMeJM\n1qFbN5WLGVLwpfSY+3c/K6d7kCh20Jf0joJ0LhwlZV6ndMSWBuJcUlTdbuEx\n5SFE1/Ff89IETNxlkdhqF38oyiaIKzKJqcPYZGV6h1tXpvEGbUo2NxEYF0BZ\n6RAu\r\n=b0v/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8"}}, "7.0.0": {"name": "make-fetch-happen", "version": "7.0.0", "dependencies": {"ssri": "^7.0.1", "cacache": "^13.0.1", "minipass": "^3.0.0", "lru-cache": "^5.1.1", "promise-retry": "^1.1.1", "agentkeepalive": "^4.1.0", "minipass-fetch": "^1.1.2", "minipass-flush": "^1.0.5", "http-proxy-agent": "^3.0.0", "minipass-collect": "^1.0.2", "https-proxy-agent": "^4.0.0", "minipass-pipeline": "^1.2.2", "socks-proxy-agent": "^4.0.0", "http-cache-semantics": "^4.0.3"}, "devDependencies": {"tap": "^14.10.2", "nock": "^11.7.0", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.1.2", "rimraf": "^2.7.1", "standard": "^14.3.1", "safe-buffer": "^5.2.0", "require-inject": "^1.4.2", "standard-version": "^7.0.1"}, "dist": {"shasum": "a264fd7c09f600f3aad69fed529523efd3fa1a63", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-7.0.0.tgz", "fileCount": 13, "integrity": "sha512-ZsYCpqbC6bXAakAXkkldzkDUtJP1ibosnyAEBx2x1lWphjzHFzAajm0FDWBY/CB5DATAKP97HYRnLDxXawNEMQ==", "signatures": [{"sig": "MEUCIQDoGyZay2yMHEuKWIeJrxgkw4R5o2e0NWDhth21f0yeRQIgJ2T5D4D4ELonaPSJCpd2S+TWtSn+v82i1eR6tQoqAA8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70771, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd+B9ECRA9TVsSAnZWagAAe6UP/i2ED2gPDkp9KIi7X8Ur\nXFp3DGVGbSOeJklAo2kAsOjFklDLf/M6Q4Y6wCEyrKDiyUbjRHkwRzcjqxd6\napFTReGLoumXMkkx1+DVXEQU9/K0y2hIkIj6Ks1vBmfACEcDtOq7xM7lrH5o\nsPcq2uewRKZIefGfsiSHzFR4UbhCP3Ex9tu68lgmid0lOoSGO+CFPIu6CdZq\nPrBGMYQOtPqPJ3/H8ay/3jNAkS28bpm/hra10XNFmj3O0dgTE0yZaMzHDNnc\n51CS7kwYzkNmom97kB27RdQuQiXOALVLFnbfVvZ60sAD9FVa2F6h5UAhfKs3\nJfirUdLT5cN7m+OMx/Abt/VJz3conqO4nUVQQNPk8u3R4RXo+oOouSwNe8/t\nqiMIOn3k6c8nps7YK46vSmSdDzQ8YS9hXb0bzNWdByscQ1/JYUAn9GZrgbwp\nLiCXRoJyJyEQHhbwIKA1RIKUEwam6fKCj5o7Y1WNCLveDkJO0o7UW4xorWLa\nTkWjGt/gLwwA+UhhoNhzb0cu3Y1yirrpzpPIttiWiE5ijmEwki/92C1ylfIR\nwz0PXSUIthjvG52k3it4udY2Nz/YkXUp1H6wC2oHaKLBDji52HLs+LUkg7zx\nZDDuQfCRG0fY13FSNUCJweFEDEDNcukYHjYqEg2xA2qeRpSt4fGUU4o+/Dv6\nPtpp\r\n=OpoF\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "7.1.0": {"name": "make-fetch-happen", "version": "7.1.0", "dependencies": {"ssri": "^7.0.1", "cacache": "^13.0.1", "minipass": "^3.0.0", "is-lambda": "^1.0.1", "lru-cache": "^5.1.1", "promise-retry": "^1.1.1", "agentkeepalive": "^4.1.0", "minipass-fetch": "^1.1.2", "minipass-flush": "^1.0.5", "http-proxy-agent": "^3.0.0", "minipass-collect": "^1.0.2", "https-proxy-agent": "^4.0.0", "minipass-pipeline": "^1.2.2", "socks-proxy-agent": "^4.0.0", "http-cache-semantics": "^4.0.3"}, "devDependencies": {"tap": "^14.10.2", "nock": "^11.7.0", "tacks": "^1.2.6", "mkdirp": "^0.5.1", "npmlog": "^4.1.2", "rimraf": "^2.7.1", "standard": "^14.3.1", "safe-buffer": "^5.2.0", "require-inject": "^1.4.2", "standard-version": "^7.0.1"}, "dist": {"shasum": "22038954801048ac598c72e0f0c239e42d97fa2e", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-7.1.0.tgz", "fileCount": 13, "integrity": "sha512-/5ICTcpd4ApIRn76pxcl4aQhrWxdDCnRDy3y+Tu7DbRsfqde6q8OYXUm7bYhH5dSey590AMT0RH9LDFq7v5KRA==", "signatures": [{"sig": "MEUCIQCA9i82aMv79d9Kxg+3udDPnr8lNf9E/zcnOaAtKz6JcQIgae5PFdsSGPFaHomwSVK6h3JuRyDzPqCIvNXurSPBhsE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71451, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd+CNSCRA9TVsSAnZWagAAFp8P/iwVKAsU4CFoF/QQiKlL\nHTLQtUmUc/rS5mEQVNowuwYcvEM/AY9Fw6/8qvbWfPCs13b5h4OXXuuqjRMp\nPlq6bewYKc5PmAswShoGVaFiioepqaGVqA8sywMopLTjiWlTrkfBewuR/V6h\n1J2d+PDcOXYgVXw5ai1+NhOMC6HyWaY1jC7dnNeAhKfKwCuppdOAQi2bQwZ4\n9qiLswO3PL0VQjP54A0kXo/DBzmEYdrkEEXqj5GU0Kv+O0Vm86+MGu3QPdNm\n3e9y88Ox+ZfXvG8GL9AQAiWiDQRqigPkF1F7x0ZaPtAe9a69p1sOFGk/tJAA\noztfwsbuwREhmrXa76fIUl/gqVuG/s3k2nQG7YfTOXJMjFxBeARln/9RHThZ\nvTGdzuarJdxYLAaU9koi9kaV7DSlpJkEHHqgt70QC/DKwxSMcRUULMh0Q5ex\nUqnXKxiB10LuRmhfyVwEXhqIVSJ7RfKtfMfNK7Dm7gSi8ClTubl/e4qtpJQb\n0tIyqRW8oq8cO0vE1b/5388IUC9NscRelSLh2VE9YGvqAy3ifc9dvQN3gqH/\nLtSPVihs182Q6GVx7Xq/g0ur0W30YQcz+/oiI4pLNNoqq1FsAxJ1j/o95vCy\nsuCR4q+SYC1nS33lvLDc8jKpCiZF1+SlYhcxlsIWqL1s4P/YQvY6IHVnavJP\n255s\r\n=y/u5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "7.1.1": {"name": "make-fetch-happen", "version": "7.1.1", "dependencies": {"ssri": "^7.0.1", "cacache": "^14.0.0", "minipass": "^3.0.0", "is-lambda": "^1.0.1", "lru-cache": "^5.1.1", "promise-retry": "^1.1.1", "agentkeepalive": "^4.1.0", "minipass-fetch": "^1.1.2", "minipass-flush": "^1.0.5", "http-proxy-agent": "^3.0.0", "minipass-collect": "^1.0.2", "https-proxy-agent": "^4.0.0", "minipass-pipeline": "^1.2.2", "socks-proxy-agent": "^4.0.0", "http-cache-semantics": "^4.0.3"}, "devDependencies": {"tap": "^14.10.2", "nock": "^11.7.0", "tacks": "^1.2.6", "mkdirp": "^1.0.3", "npmlog": "^4.1.2", "rimraf": "^2.7.1", "standard": "^14.3.1", "safe-buffer": "^5.2.0", "require-inject": "^1.4.2", "standard-version": "^7.0.1"}, "dist": {"shasum": "89ac8112eaa9d4361541deb591329e9238a531b1", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-7.1.1.tgz", "fileCount": 13, "integrity": "sha512-7fNjiOXNZhNGQzG5P15nU97aZQtzPU2GVgVd7pnqnl5gnpLzMAD8bAe5YG4iW2s0PTqaZy9xGv4Wfqe872kRNQ==", "signatures": [{"sig": "MEYCIQDBmcU7BcdQv6CUYEGtjt2mX/ENeHsEpRHrt/SeQGwIUwIhAKRdCi8krkwJd9P7EFntK0T101KjzNQ/1zFEVPwXKgDn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71570, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeL5VJCRA9TVsSAnZWagAAeAkP/ipQu2CMZCgk3++IakoT\n3C/lDXjIvZqEYiUvhZEyRY5ubAcLV/onYRhXnClEw/uPvUVNuNc0ou8X9Kin\nlhxlc2qidbGuGWEou0Cmah4cTL1zXor453tT9XEc9vEOreZnCsrFkVoKaFIJ\nQ8HnEPBG8o1BjCX3khhGOyHn4ctZsgnv8gPXVWdQ9owtDVJOyGhvuN0dJpGn\nscNp5N3xpxjPpqYlRRASV8055JrLneBf0ZXJ4FLNAJZrHOlLfBQMLx39g0BM\nV/iXcpOQ6q4zXdxGRXhmYGW+hio03yQQBr4ikeI9QpFpopN+0Zl3YTfsSizy\npFJYc+CZWmlMqdoRjtyNQIZod0+L+L6NAN9bgD3QP6NXHCpQB0a0FmptfuGT\nhrIQiuw1OTjrrLPpp61sLA7jWDeYwwEzM9dIfTaZiYslbMXayGwNZfut1MM9\nysCQD4CgBqSYMlfLm1Gm/F7qGBVTmST4FQW8/6ELcTxUhOjFFqlWjHo0Um/i\ncv8YyRQJsBDIsrTr0kc8fHVIBkdLLcGbjBDPMLE7ISs4TOC+Z0tbzek6UVjS\nezbZJxxUlp29H9+OOzK+i3nhz0LnJO1AqLXSQB8eQlF8bbdsSRSqpZz3jLvI\n/42ZuCWPs4UxtzWzWuwi7SdZFUNM1G6Jj4LD/W3tEG9psRO1oPetdkTB9vqO\nKOAp\r\n=oKzC\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "8.0.0": {"name": "make-fetch-happen", "version": "8.0.0", "dependencies": {"ssri": "^8.0.0", "cacache": "^15.0.0", "minipass": "^3.0.0", "is-lambda": "^1.0.1", "lru-cache": "^5.1.1", "promise-retry": "^1.1.1", "agentkeepalive": "^4.1.0", "minipass-fetch": "^1.1.2", "minipass-flush": "^1.0.5", "http-proxy-agent": "^3.0.0", "minipass-collect": "^1.0.2", "https-proxy-agent": "^4.0.0", "minipass-pipeline": "^1.2.2", "socks-proxy-agent": "^4.0.0", "http-cache-semantics": "^4.0.4"}, "devDependencies": {"tap": "^14.10.6", "nock": "^11.9.1", "tacks": "^1.2.6", "mkdirp": "^1.0.3", "npmlog": "^4.1.2", "rimraf": "^2.7.1", "standard": "^14.3.1", "safe-buffer": "^5.2.0", "require-inject": "^1.4.2", "standard-version": "^7.1.0"}, "dist": {"shasum": "38411249c509a5c6af954761f6e4d56d17378de8", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-8.0.0.tgz", "fileCount": 14, "integrity": "sha512-/ZHSXdEJpGS2Ob74AirRbylC5BpWKoO6C+QUJks/wi4JWFEUiVaR5QdZ9Uh2V6ukiMyVZi1KPyQHHlsZJp2O6Q==", "signatures": [{"sig": "MEYCIQCocpb1lwBJ8nQx3UZs0843hkxcpDwcu3NvEfr0GJzwvAIhALD8s65M/AoveH6ZUBVUl26+7T+rPWps32t3ZvQcauIC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72407, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeS0BiCRA9TVsSAnZWagAAHuAP/RAuB4it+IebapRLLFDg\nqQRjFOx1BxA/eHd/SzFf6C4uDNHlDxCkTkVmvUxqAbTaVljG75GXgdE47xS4\nTUES45BxSfhOFA5XZfWDiyYvXqg5h2ZlnbiTCgGoMVAgcVfP3yoKzvJzV73L\nWwq4pD/zBjOR5XKB2Q0et6r8AyihTa7g+FhCZ7YvNK7Jsp/17Mawv3ZoklLC\nYgLrx53X/g5dlnVIYqsJO3VK4YbEt7TaU1USDrxM5d0AJpw0Py0NqxFpN2nU\narLcwgv+fST9eR7KdirnkBJCm/fNsFzawvB8i2yfvrJlq9lULzh2d3cy9bek\nQUDpgN7s0C5qw2+YNJ/X8C4OglQ8NoFsm6ULKWMVOioqZlZ4lYVpF8FsOtfI\nZgCul+2OyoIvQTNJZDXpXjLWl3fec/xGfRQ8Cg3Px1JfUcLbu21auq3P1aJn\nBlMcfM9ciyLl2kjWeEvTf9P9V15sHeilRM6XZwKbWk1djYA0uGS2w9qyJf2o\nGeXgKn0sVi5JGwSSjL/IypFaJE2OXk4UefNDviug0mOvz6DczJDCwVANRadb\nCeME/lSmCg4Gsm6FuUIsvjjpiep5QNN+nUJCGA2/uYDQ7zEaNCxv5tfaM8Cz\n284yn8TvIHXXO+nqbAAd5JpdaIR4/0KADMyvlsuav1OEixPw+1pfXqIBHzsY\ndp8H\r\n=vzVk\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "8.0.1": {"name": "make-fetch-happen", "version": "8.0.1", "dependencies": {"ssri": "^8.0.0", "cacache": "^15.0.0", "minipass": "^3.0.0", "is-lambda": "^1.0.1", "lru-cache": "^5.1.1", "promise-retry": "^1.1.1", "agentkeepalive": "^4.1.0", "minipass-fetch": "^1.1.2", "minipass-flush": "^1.0.5", "http-proxy-agent": "^3.0.0", "minipass-collect": "^1.0.2", "https-proxy-agent": "^4.0.0", "minipass-pipeline": "^1.2.2", "socks-proxy-agent": "^4.0.0", "http-cache-semantics": "^4.0.4"}, "devDependencies": {"tap": "^14.10.6", "nock": "^11.9.1", "tacks": "^1.2.6", "mkdirp": "^1.0.3", "npmlog": "^4.1.2", "rimraf": "^2.7.1", "standard": "^14.3.1", "safe-buffer": "^5.2.0", "require-inject": "^1.4.2", "standard-version": "^7.1.0"}, "dist": {"shasum": "94e0c57c42617d28f3e3c6c64a8977f87887362b", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-8.0.1.tgz", "fileCount": 13, "integrity": "sha512-oiK8xz6+IxaPqmOCW+rmlH922RTZ+fi4TAULGRih8ryqIju0x6WriDR3smm7Z+8NZRxDIK/iDLM096F/gLfiWg==", "signatures": [{"sig": "MEUCIQDEb/9dL56O/caQuEMKW5xJM6pBY3449TgWfZyNS3ZXZQIgFFjeTO+J2PX07O7bxXiB52D60qpsBtLEo625W6Zc76E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72019, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeS0CcCRA9TVsSAnZWagAATzUP/AxQyp8zK+FGMgIb8pbz\nRZO14i5gtRjajuXj2rlElYwz7PRD8ly1Xg4RE7Y30CKcqBhN63oCnItutJOW\n/aGsfROEPqTJjwk+sBS9wde3icDaAbVccDXhuNr/v8duCaTRbtIH8Ya7CBQS\nyLY1/IkM776wZCuDnS86Luo1IFpwP+okYESweAi6kULKOsakJ7cCxTuidku8\n/iRRrESdzSa2E9w2aS6Jt8pATkzExJdQU2TCy9n9gfu/D5lAin5H8BDIOaTM\n/AUWi5GkS5R1a00a8rVv/zH509W/vQwYjXVRtQ+5PFxR9g9EZXB4KAUmZHPn\nRfi8FQWE5CNNiW+XAa+YdaUQQGUulftTmVB8vD7lOC1WZM2PnX5vvpCxcc4H\ny2Nx3rmUQPkDYEeJ0L/LKPFksn1RwXWK2u7s/y2MJBE3iQwmXUGuKBCLXFXg\nauNQ75lNt+ipD5yebMaG+wwC4koN4dyB3a922jpoo9CS2XSNXQrTG6Gz+Y9+\nZBYJlIyfz6/qsHI8ntZeQ6Wov1AmWFIP2gHQtWn8y4ebBBM/Qj+1h5RHOmwx\ndE+Mg3eIy2iCPnSUHQ1grKoIXBXQIYpiVSxliRenv/0zhKNiR78dmCMLUXT3\nu27WyKFm3MTZ7+sL1aUL5rfK7z6ZS3gb44Yfb8flylYKiDemTWIxOPGdURNb\nPrHF\r\n=OFUA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "8.0.2": {"name": "make-fetch-happen", "version": "8.0.2", "dependencies": {"ssri": "^8.0.0", "cacache": "^15.0.0", "minipass": "^3.0.0", "is-lambda": "^1.0.1", "lru-cache": "^5.1.1", "promise-retry": "^1.1.1", "agentkeepalive": "^4.1.0", "minipass-fetch": "^1.1.2", "minipass-flush": "^1.0.5", "http-proxy-agent": "^4.0.1", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.2", "socks-proxy-agent": "^5.0.0", "http-cache-semantics": "^4.0.4"}, "devDependencies": {"tap": "^14.10.6", "nock": "^11.9.1", "tacks": "^1.2.6", "mkdirp": "^1.0.3", "npmlog": "^4.1.2", "rimraf": "^2.7.1", "standard": "^14.3.1", "safe-buffer": "^5.2.0", "require-inject": "^1.4.2", "standard-version": "^7.1.0"}, "dist": {"shasum": "155ef26d7c6f3caf8146b0f5d0140a01e0fdc050", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-8.0.2.tgz", "fileCount": 13, "integrity": "sha512-jRqI9zjLyz8ufXfLSbEObJ6a8sv8geeKYEPFpI+b39JjYU14MZtCiJGazSWPZMjCm7161b4r57N/na5fBXpooQ==", "signatures": [{"sig": "MEQCIB+Z0uk1CxZEDDBKp5mwqUMs0/H280JaMBoWNNpAcJtLAiARnvaoHu37yThlReUeDe6YGeu65Wo6CI0OZS0uUVfovQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72019, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeU1alCRA9TVsSAnZWagAAycEP/0eWQbZcafl5NQ1MmIkq\nS47OUTlGA1hMiFtFnUSCpU899uNB38VOp4qpxlIIMXhBT+x/I5fREkrZDCyr\nu99fQTbXrmJaNj8MnRmhQG2eymP/5zdsj/99g4zAidgkwU2O620Cz4YXXTKe\nzSKUmsN+G4U6Bi6T8UN9VgxRkA910J2ySwK4eE+fFkw35TIAnmetnaSOB1Bi\nkNaagriHPt/ccRHpvNRFSn28MHnzpSnqZ1zooLIV42sfLknjjjLtfnbQVc+R\n/yY0zoOH29picL4YWjXOWUFnWe+WANsyL09+dwK36lA40TlMvfTdqr7rmL44\nr95gBGeOfeFn4/UQkO06FTwhcodZFWHZMnwpI+ZdeIcPLaM5cpbhSrhASQGP\nznMfJL0oXSm4bPzE49VmMhZDu7NOAlKsElUFRlgjVHCF1MYWjhAc/aQSMOUg\ndv9rYyMvMIngCT7YwfdkJmfwKGkhdn9Zz7okyHEG7QxbAIoFWT3QN0y+pqca\nZbmG+htaQg/ZL4/z4qwREdCiA0wnoiwa16Z86NASiZtmoQQXJGdor+8INVs1\nznk7Q5FqCRzOyCpNMHDRT6sNoOgNkp5rtI8D2GQqLMgAeRhPpWv822hkYA7e\nxm0h8Y8uYN6QNEbOt14dQJpemDUpXJgcYv/W43nkTIYpp3Uw0Su7Hh0r2fxd\nbt6D\r\n=+EkP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "8.0.3": {"name": "make-fetch-happen", "version": "8.0.3", "dependencies": {"ssri": "^8.0.0", "cacache": "^15.0.0", "minipass": "^3.0.0", "is-lambda": "^1.0.1", "lru-cache": "^5.1.1", "promise-retry": "^1.1.1", "agentkeepalive": "^4.1.0", "minipass-fetch": "^1.1.2", "minipass-flush": "^1.0.5", "http-proxy-agent": "^4.0.1", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.2", "socks-proxy-agent": "^5.0.0", "http-cache-semantics": "^4.0.4"}, "devDependencies": {"tap": "^14.10.6", "nock": "^11.9.1", "tacks": "^1.2.6", "mkdirp": "^1.0.3", "npmlog": "^4.1.2", "rimraf": "^2.7.1", "standard": "^14.3.1", "safe-buffer": "^5.2.0", "require-inject": "^1.4.2", "standard-version": "^7.1.0"}, "dist": {"shasum": "9a95b566b236b5f08d41f5770f563cd089faf955", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-8.0.3.tgz", "fileCount": 13, "integrity": "sha512-3ulUor6Xf8g+T0x4vvsV6mIYchThPR6sl2J3lSRF9I5ygI5/qScRnDToTLRMNXPlnB6cpO2cfQ+r088T7GYwxA==", "signatures": [{"sig": "MEYCIQCdZv54dPcMMZ/4I5iu+QPAhVH7lA8EvHdA2m4P3S7EOwIhALY7/xNx5fazanAw0nZog+yzk++GAatLJNJO8XuRkfZr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72475, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeXaJaCRA9TVsSAnZWagAAsM4QAIPs6OBasfyqu5Z8RhAj\ni5bKpDlNHq8ukoXE7dYY1OnkR31tZsnDPjaISMUCuOi243lLOuzoaMm2a52q\n4lkCtLQwtbXyHAK7173Jg4cokXs0IF2F/m8HPmj113kHP5ndeSDTsIbGVoCw\nqKFQchrHEZdvnt9d+O/mle8L7oSest7NE2pE1icqiM+O41Vi4CSRuCFB5anW\ne+UwvR0H2OM+JpiyYgWT3OcHac3ZCCPBr1mhpc6TF0g5JmeMxotlojF5zRqU\n51RxgXnr6wi8YrDw+/6G+VjljTAikGa9HO4PLIC64KpBRhGanenLgK4EOhIo\nixsttBfVrDja3rtG7fggw463qhXlydZVUNHwYf+85L92nwfLsTvoASxXFgma\ngCjFVxRLUKEUdXh1PWydYr+EwhfQlOUf8biNO/rb/wDehO8nE/MRR3plxR8/\nR7O0az8cnIvpn/Z6sMpig0gud+AnbUoiYW/So25+OcP/jkFJkHwoCgn30gRU\noON6+ofH6gVsjDY6xyq7m5RlJrqewon5HNSzvgNimyAsRoCZH5K16z5O1Six\nlXVofB7JxAQMXH2FpgEml0gdECE6oDQf1ZCZ75prszGK7yPVNkt+8A8/Jfmi\n9SxXEuCFNrI1Fn7QObsIPt+9Vda9oMNi4HwdsJoOzfewZbI3oPfO/Z4ZHh+l\n2B5f\r\n=4AYr\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "8.0.4": {"name": "make-fetch-happen", "version": "8.0.4", "dependencies": {"ssri": "^8.0.0", "cacache": "^15.0.0", "minipass": "^3.0.0", "is-lambda": "^1.0.1", "lru-cache": "^5.1.1", "promise-retry": "^1.1.1", "agentkeepalive": "^4.1.0", "minipass-fetch": "^1.1.2", "minipass-flush": "^1.0.5", "http-proxy-agent": "^4.0.1", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.2", "socks-proxy-agent": "^5.0.0", "http-cache-semantics": "^4.0.4"}, "devDependencies": {"tap": "^14.10.6", "nock": "^11.9.1", "tacks": "^1.2.6", "mkdirp": "^1.0.3", "npmlog": "^4.1.2", "rimraf": "^2.7.1", "standard": "^14.3.1", "safe-buffer": "^5.2.0", "require-inject": "^1.4.2", "standard-version": "^7.1.0"}, "dist": {"shasum": "d3451baf5b43d6230c4eea7009c5aa6b6bccf9d4", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-8.0.4.tgz", "fileCount": 13, "integrity": "sha512-hIFoqGq1db0QMiy/Atr/pI1Rs4rDV+ZdGSey2SQyF3KK3u1z4aj9mS5UdNnZkdQpA+H3pGn0J3KlEwsi2x4EqA==", "signatures": [{"sig": "MEQCIFYlqHmMUSk/KTFRSOxFZdiu6LZWPdS2VD1/xpbMBrlZAiAy3hLEg4l8ECVOBlFqkJn2XekoWmKcMCZX8manp0oOrA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73216, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeaZHNCRA9TVsSAnZWagAAxgUP/AyC2E/WVpXl8BdZxpyU\n/0z/p//N3w7S4NZ49kHIV/lLgN4jpZT68+z0naNBZmXDCyS14aqb7etdily3\n1s+ApU0mfdAztyEXu43GZovoab8/rZ8XDeAzhq1U7/X8HbCeZu4L1k9jPEGK\npFIfmwRumGl1kz3OyDcY6NVziR2wtNQ/SZVpuGQNQ9Cm2W5DzELipA9h81YZ\nWKwpWg3Id9T7/kZRG3BKv3he+DoYp7q++I9FZOiONER+zh5vgalthaxrdgCo\nCpUN4SA4f9VaQ+ENfYhQLRVPPXAus02ApdZEsffAb3XiNr4SrZb4nm/95035\nGcQtI7e9BuzxG8AxiOSkKrSb7r+AblTICJFZBf+hRQbyZMk0FS/lifvTfsnW\nnB+KaAYX360yYOQSvIZoJ/gxT8gzmA9w9kDdxzGjJfJLL9mZ71VSRy7dsquO\ne6bpBDA/Zcz3GK5+vEVz4900XW3jMhYKX0Sibk+f3Iq1gqqKrs502Et6C0Y5\nE9JQESogEQpj/JER5qGhbkXuK+u299ezSYC3V7cx7xRlUvA/qd/WtDbpIWaJ\ntA0gwiyQghmT11LsVgSOz7F2HexkE7xC+FZVLTvjQ989YiATasc9LrR/hM1a\nuCNYfck9seyxgJ0miTVv/F2XJlDzTJaW+EDm2FGD3tvLvRmgzUZ2oNJXUzEB\nz5kB\r\n=iuNx\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "8.0.5": {"name": "make-fetch-happen", "version": "8.0.5", "dependencies": {"ssri": "^8.0.0", "cacache": "^15.0.0", "minipass": "^3.0.0", "is-lambda": "^1.0.1", "lru-cache": "^5.1.1", "promise-retry": "^1.1.1", "agentkeepalive": "^4.1.0", "minipass-fetch": "^1.1.2", "minipass-flush": "^1.0.5", "http-proxy-agent": "^4.0.1", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.2", "socks-proxy-agent": "^5.0.0", "http-cache-semantics": "^4.0.4"}, "devDependencies": {"tap": "^14.10.6", "nock": "^11.9.1", "tacks": "^1.2.6", "mkdirp": "^1.0.3", "npmlog": "^4.1.2", "rimraf": "^2.7.1", "standard": "^14.3.1", "safe-buffer": "^5.2.0", "require-inject": "^1.4.2", "standard-version": "^7.1.0"}, "dist": {"shasum": "b59ca14f3cd91636258fd111b3b9e25b773f4801", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-8.0.5.tgz", "fileCount": 13, "integrity": "sha512-01z8ZJSvzECQ5XzeUbRY9dDh1h69LHU/dWaZdLwJPIJ4Mi4XjQp+muOsNP0pcqNvINkkl7/KjobEb83sR2Nshw==", "signatures": [{"sig": "MEUCIQC3Z/i60FKSK1B3QWjyyoMw7OXVpUS6zOng8u0Pq76awAIgYzS3qpVVeV1OtwhSD9Iv5CwIH18/C9bESY2CjCwE5k0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73266, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeq3cyCRA9TVsSAnZWagAAVYUP/24Zd5jOR3w1eMYhQVSc\nif5b38AwxEuLAL/a8u39wneG2NWVDH3kgDi43S9xEEa9e44jle0IATwWyJme\nCD4LQI1aydR63H2eSvPP13YJaSYtyfXzxlWEKfo/QZMhYlP9qnb22f5DEF7C\n8gRllyXNBo1GwoEo05ZQ+pOay/1cwXtDAyl5BzsKvweOe2Ip7xobJq0jGIJQ\nGHNNE7381bAxxvBYQA+iQUm3mxQNJJNaZZ3ZwNbHAHkMC/3PyVFdtAUzj6qf\nGYLAqJn56l4PpZWj7BWzZDzxavezaik7LmpMTb+LP4kDcgeLao2iJ93xsiMg\nF4Qoh2WXuCXsNbZxO8AqJO7J0+esm03zL28pRal8XyNz9jwX0imN9Cur7CFm\nNQLRsKo7pUbAFCx0pmNs8CSAkLXpbs70g0mdCrakpAoYphes9WyG6fKsXTFt\n5ShiLo6Tli36PbqihBWRWTZY7FO6ptKl/LRQkY7BuNlW+MTVv9UTXRDYlOZm\n92hQ7oX+C5LfIQ5f5NUf+dPbCzzbLNJsPC8nYcLsJ6OJpCDLO4Z9vPDj+CbH\nMtNVsoEBP+xknP2zCRoDsBQkW0U2g0TT/pYm5DB2vEd1Xt83aU50258fDXEj\nikbA9/V+dHZ8+KLyaR1vvuMP99Tdj2E04YI9AqJu79spAvRsbi1uAIQz9rLH\nee/W\r\n=50uE\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "8.0.6": {"name": "make-fetch-happen", "version": "8.0.6", "dependencies": {"ssri": "^8.0.0", "cacache": "^15.0.0", "minipass": "^3.0.0", "is-lambda": "^1.0.1", "lru-cache": "^5.1.1", "promise-retry": "^1.1.1", "agentkeepalive": "^4.1.0", "minipass-fetch": "^1.1.2", "minipass-flush": "^1.0.5", "http-proxy-agent": "^4.0.1", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.2", "socks-proxy-agent": "^5.0.0", "http-cache-semantics": "^4.0.4"}, "devDependencies": {"tap": "^14.10.6", "nock": "^11.9.1", "tacks": "^1.2.6", "mkdirp": "^1.0.3", "npmlog": "^4.1.2", "rimraf": "^2.7.1", "standard": "^14.3.1", "safe-buffer": "^5.2.0", "require-inject": "^1.4.2", "standard-version": "^7.1.0"}, "dist": {"shasum": "392726f46eba30cc61dc82dc015167e6e428ce80", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-8.0.6.tgz", "fileCount": 13, "integrity": "sha512-QJ4pB5VBY9H9e+3t/o+fPjsVUlPULpAllxuKertRo/7ii47TfxeEEnneM6NCmhyn4MQPTYL+M+RkiU9bR+hAfg==", "signatures": [{"sig": "MEQCIBvuQ3e0DVh4fRDAGOWTsmZgehHuSVX25qsbCnoyDUujAiA+oIEnzxMI7GEGyQvXxmNRxwdmNCB/DvKaTAsrTStmxg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75119, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesF2iCRA9TVsSAnZWagAACSIP/i82dNeH607Q6yAjCb7C\nOhhef7DF2qMkTPUrK7GJK24K+P7seI7E83/AaMQfySHswcxDrWss2TOu6HnB\n6UGw/kldLVHG3mv33GIXvH3z9yyNsHCy5ydQBKs/4V7sYnYpSYTi8eiPQYlb\nhQlvLYmNHkNeS8h/UCNjGK5FuX6fjWf8hKHnsfYDeJ8XQQUtLaDdUmeIaaWZ\nJX2R+aM9YBhJt0KqI+4yf4WFfuqFNuAtbbUNyAltT6hZsdpn9+TulkNRXRI0\nmajXqngjL+f+cZNoF2tBtPe8DgZKMmZFTANPuqWNkRL4w9tR2azZAe8LJzF3\nS5jFLJaFJfMB7XNJhVRXlnnspxP2fUQtVJTlqQAZ5Tx2N2c/MJ4ipi/2Okge\nqTIsPyEoLiMQBfZUqTFWMzxrM+gY+PS4I/cqtH0NdZ+PBCWD5WRvv1sw53a1\n4E4proLOJPQsLPlyfFYTjbbl3AmK2PuQgxBYZAqovEXd3XcndVLXf5QJ8t7T\nI8soG3y9TgxEfXF9LnUcvWyjyuceqerqTYrfygOIQjwl3Nev4ey2U2QTYYoz\nC+3YdiFv8K1P95Okeh+Ei9hfiyNEw4t1Gky6dgDdgxDE5LMChlpL2ZV+RewC\n7zyiuLsT5S5ARs74s5d0433U27XQIJaHq03QFJDAh4xtnUp6jA+fwO9wAPxX\n8XMK\r\n=F5q3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "8.0.7": {"name": "make-fetch-happen", "version": "8.0.7", "dependencies": {"ssri": "^8.0.0", "cacache": "^15.0.0", "minipass": "^3.1.3", "is-lambda": "^1.0.1", "lru-cache": "^5.1.1", "promise-retry": "^1.1.1", "agentkeepalive": "^4.1.0", "minipass-fetch": "^1.1.2", "minipass-flush": "^1.0.5", "http-proxy-agent": "^4.0.1", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.2", "socks-proxy-agent": "^5.0.0", "http-cache-semantics": "^4.0.4"}, "devDependencies": {"tap": "^14.10.6", "nock": "^11.9.1", "tacks": "^1.2.6", "mkdirp": "^1.0.3", "npmlog": "^4.1.2", "rimraf": "^2.7.1", "standard": "^14.3.1", "safe-buffer": "^5.2.0", "require-inject": "^1.4.2", "standard-version": "^7.1.0"}, "dist": {"shasum": "7f98e6e75784c541833d0ffe2f82c31418a87ac2", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-8.0.7.tgz", "fileCount": 13, "integrity": "sha512-rkDA4c1nMXVqLkfOaM5RK2dxkUndjLOCrPycTDZgbkFDzhmaCO3P1dmCW//yt1I/G1EcedJqMsSjWkV79Hh4hQ==", "signatures": [{"sig": "MEUCIQCc/dRwsw90aPJwd378Ql0F0VPqu1w7MGDK8JFPmaWCNAIgLN8ywS3IrGDhZUHuqayqZn6UhwbqzxB401wFux3oqks=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73266, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeu0yQCRA9TVsSAnZWagAA+EQP/18jJNNWT7dQZvXRmsfg\n3nsNd/zXTEGkoW8Ay7ccC+xBG7qzqRMwsWkqeoQ8oi9nyT2ICRPjYSmNMHC1\n3ojV9GKpxXmw4f6bZxURucAiLtTDY2hBl9zbpMWdVp6fJVqIO0AWB1iNo0yC\nBRQCsTYqO/bdOG6hIlRF4ks46be1cCtcnku9nEuONqxsAOcYzr5FmgGmMLpW\nK/8yMUlMJzhK6Z0GrAP0LhV9aRD57mQmkHYxlvjPMBq+mNbwhBoku80ZZFKR\nnbtWftOHfRdVSWfuaLZ0Uj5Zn/H6SvJW+c5oEy6j95ZP9muQNdxoPKc/0sZ5\nT92Z88cR9Fc3mY2OJjJUcctip5ahO71jI+SSFNfK757LkgI9v4BTxCfn6Mxd\nJNixNzkW9VTz/BnZu0OajZ1Q2PFDdjE3XBTFewURwzK4lwWKX4Ej76Mb8Az6\ntgcf0fdg5uEoldfqEj7NUEVgm3+xXTc5MbGboCo4cpmhXRC/mXxjzeCfCWjx\njDeTFyw3asnaoevwpGFwVKz6zJIv3IlWGnqC4iDoI6+8Lgdg73/s8Pu/pQKO\nL8/RtsOBwq+2iBwmyRwHyFtl565F28LNIylPShlZkEGQRQIk5AkqQPlkovIH\nX1aW378G3H62GvvTb8psF5HfShuKZ8OS4vp1LiUXrEXZOG/DuAh6YECwNQS/\ngXZK\r\n=6X/v\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "8.0.8": {"name": "make-fetch-happen", "version": "8.0.8", "dependencies": {"ssri": "^8.0.0", "cacache": "^15.0.0", "minipass": "^3.1.3", "is-lambda": "^1.0.1", "lru-cache": "^6.0.0", "promise-retry": "^1.1.1", "agentkeepalive": "^4.1.0", "minipass-fetch": "^1.1.2", "minipass-flush": "^1.0.5", "http-proxy-agent": "^4.0.1", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.2", "socks-proxy-agent": "^5.0.0", "http-cache-semantics": "^4.0.4"}, "devDependencies": {"tap": "^14.10.6", "nock": "^11.9.1", "tacks": "^1.2.6", "mkdirp": "^1.0.3", "npmlog": "^4.1.2", "rimraf": "^2.7.1", "standard": "^14.3.1", "safe-buffer": "^5.2.0", "require-inject": "^1.4.2", "standard-version": "^7.1.0"}, "dist": {"shasum": "12e8281b83db47324380b9967bb7d38756a4454c", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-8.0.8.tgz", "fileCount": 13, "integrity": "sha512-kILd4WtBFqylc65LRq9sTx/GG0r9yMoomjz9ZMGxzZKWXhnidDhROhnjtZEQDHSWbwQJ5ojI4XZk46DdHwslGg==", "signatures": [{"sig": "MEUCIBGV0ePoVMwvDzp2xkB9UJzjkPQtWnmJoDvpoYqVQVk9AiEA8JustxAcnN0/OSE6dvxJwqzfUF2mXadjKSkColP+aNU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73266, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfCQ8mCRA9TVsSAnZWagAAGPsP/At6B0hhCaTBK3VPAREp\nqZgH/QWdCWIXUaI6iRk9f5MTwBBkYIiZiP4BCv8J2/m4zxymk3X+nm6MSebX\ndEPBmGoLtilrckTS4Fi72k3XiQTCHmbhun0MkXD4tbYEwiDrZIVG/pTeyYQT\nTZj+c6D3RDXlSAGV1uNMRsIE+fmD2K19OdIlZRU8p7jwgtA5NIantg3+Hj11\n3ixlk6z7WC8v0yVzKRgUnzLnQL9HHP17XZZOpQz2DduDENttTlcYa8lxI81g\nvMyOzo0I6qy5FLRRYXTY3dUkzuoq4bRwOQCcHNRACdrYff8THLUAxkdRakrE\nJL1Q/HLQwO1OAiRGtLjlAXj9gYdxKF/Tqss6ugjnuamCauRRCxa1VSP4bszd\n7HqQ4TrDEfv8a3fiZafAC1Gps0Og6zcSS32/rTZvOuMoqikB2vK9GKaKWQx7\nNzq1dXTTjhV7pzInjljqYnaQqophROF9kTHKOv7KDDg7JBprBQ0sk14Kt/ar\nkkFuA6iz+Rc2Uxi/lTRASwcu9QOc5GlwvlSc8BPast1/CfGySNJMlGJBSIRY\nWAM5TKR+vsSnt6Vuc/B/CT3MjYhudzFARbmnuQAxAouq/F8T7Afzejg1x+Sd\nH3UCZX/DhF5IbKd3+rQ7NyGgpS7BBmv5kMMKXvpI8deYiH/veYCXYD7YMe/r\na7/N\r\n=wvIb\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "8.0.9": {"name": "make-fetch-happen", "version": "8.0.9", "dependencies": {"ssri": "^8.0.0", "cacache": "^15.0.0", "minipass": "^3.1.3", "is-lambda": "^1.0.1", "lru-cache": "^6.0.0", "promise-retry": "^1.1.1", "agentkeepalive": "^4.1.0", "minipass-fetch": "^1.3.0", "minipass-flush": "^1.0.5", "http-proxy-agent": "^4.0.1", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.2", "socks-proxy-agent": "^5.0.0", "http-cache-semantics": "^4.0.4"}, "devDependencies": {"tap": "^14.10.6", "nock": "^11.9.1", "tacks": "^1.2.6", "mkdirp": "^1.0.3", "npmlog": "^4.1.2", "rimraf": "^2.7.1", "standard": "^14.3.1", "safe-buffer": "^5.2.0", "require-inject": "^1.4.2", "standard-version": "^7.1.0"}, "dist": {"shasum": "2179178be1593cacd04fa7a420b19ac6415f9380", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-8.0.9.tgz", "fileCount": 13, "integrity": "sha512-uHa4gv/NIdm9cUvfOhYb57nxrCY08iyMRXru0jbpaH57Q3NCge/ypY7fOvgCr8tPyucKrGbVndKhjXE0IX0VfQ==", "signatures": [{"sig": "MEYCIQDpm5hJSmyM1VNsJziM6UqYJn96zQyUWvWXiumG4cTafAIhAJmTfNFLT7jz1PsoepGZrpZvqz8vSbE22IEAskGVGxH+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73266, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfF28WCRA9TVsSAnZWagAAst8P/A2bl3O66w2v1KVoDTtG\n/8ECmLfda4pNZtXxhI+wxsxekhIR1HKUnCYgsrQgZ4T40j15sd2McDfCYnhY\nTIlB0NgZZQNbJtGKP0E2XsOutJHBaBURpvUezKy2QWwcR8+W4bvL3hob6dvV\nymbkmwRADl8UYuev2CNHQs9ON1M/RC0GkVUlljJgTlrTRJAI8oLQZFbNGh/c\nGChyt+CEubl1mioN3M//kOH0Hs41LUae5kBpReoeGxjk3y3ETskZiOdXVcVi\nqu4iKvgBVMDur3LsAdaclg4h+ymS0RJHIq69c6RWeONPWiEnP7sUFrYxy1gV\nB6YWN24jwKJWbY7oSFx1dfjxrX9gRTINCUPYAhEQicFbHiEIebER3cG39NCH\n5m21vaq9aZOEbWc0e645ixEdX9mKlaCyxBlMcAiquIA/vCyshbZwpQfWO7ai\nq/qnj9VhjoY0FgSsa8xT16CvtUxqsuSN7g1GfZyfx6wy3JQFpjFpLHphMCTN\ni3f+qEGbZge58UbL+DoqfXh1jObYD3rROuaP/GnMgGj4w+L9MieRRyRpeACk\nK7ktfBqPjPnfi9UtZ+/7mQdn1DtJoeCSYwb6XNF+mxRib/YDv0rprxlOwl1D\nH8qAkcOvtn0XrvO8+JKJVE8XIVBWDUw8eXYnV+4KwSAsapMwkV8zaCzb8ZPS\niMjz\r\n=tGqc\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "8.0.10": {"name": "make-fetch-happen", "version": "8.0.10", "dependencies": {"ssri": "^8.0.0", "cacache": "^15.0.0", "minipass": "^3.1.3", "is-lambda": "^1.0.1", "lru-cache": "^6.0.0", "promise-retry": "^1.1.1", "agentkeepalive": "^4.1.0", "minipass-fetch": "^1.3.0", "minipass-flush": "^1.0.5", "http-proxy-agent": "^4.0.1", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.2", "socks-proxy-agent": "^5.0.0", "http-cache-semantics": "^4.0.4"}, "devDependencies": {"tap": "^14.10.6", "nock": "^11.9.1", "tacks": "^1.2.6", "mkdirp": "^1.0.3", "npmlog": "^4.1.2", "rimraf": "^2.7.1", "standard": "^14.3.1", "safe-buffer": "^5.2.0", "require-inject": "^1.4.2", "standard-version": "^7.1.0"}, "dist": {"shasum": "f37c5d93d14290488ca6a2ae917a380bd7d24f16", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-8.0.10.tgz", "fileCount": 13, "integrity": "sha512-jPLPKQjBmDLK5r1BdyDyNKBytmkv2AsDWm2CxHJh+fqhSmC9Pmb7RQxwOq8xQig9+AWIS49+51k4f6vDQ3VnrQ==", "signatures": [{"sig": "MEUCIFUpz82J9Qs3NwSkiFkiGhfCvkxR0qNiu+Nezop7yaEWAiEA7fF4OVvEzL/UhtRdbrHGjFCY5SDxzvIE3YllM73vpMo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73369, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJff2TuCRA9TVsSAnZWagAACXgP/2JZx1tVlvkh0zRx6dxW\nOmpczjS1JsynlgMM3df3GbhsXEXCpYWVZ1wuXaxTUYKMlSTZNKeTUHGc56AQ\nXEO8BRafjbX8qUMLSbyAusGANTusYK1QzeGiyGUkffEogzTVgCR+0nMJH3VX\nJI9KAF0jwphOucJyR7W6sZQWKtPgOPLe+lPeEy+z8bqKvSXbpnKJbqtSHyUl\ndfwpb1tDe8+fY5x7gzFFwLNhhunRKIrbjQNhf5PKCVc3g7TK9r06qcwrAscc\nprSngZX4+oTwhRyeVT4psBEDYFpxRRokSCb9x99hwbTWhkXCJPl6NgXC+PhK\nBodng1ejTNvKyWr8gCJApXD07nIqmRGoTccRNYTdVg4l6Sv7+T6BSK8BdQbg\nHCxxPHefYrfjq8EUjRXThimKC5vVTHjOz3TIkE2n1FHRuynhhVL5kyCR7lis\nql7NymyD4E9ICdCuJGgG24KOs5xS2AhG9LthYa5ardFk8wMXgHRdC6kKUKR1\nYYSp42hy/BuqTpo5m88fLSkjIHGkH9EuuL79HYnqDpHcbXf3j5uxS//OGZok\nbfCFu8m0Bg/NJzbMGw7QHVwGk5GUIP2mVPTXyaFP/h0UyZUKYetuloWLI+dV\ngV5lc5tuqhgi4e4tLp8vCeUCRGp0/hleo8cTGu9lEn5SHcF5bUUA1cIK+j8E\nKY9A\r\n=KO1Z\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "8.0.11": {"name": "make-fetch-happen", "version": "8.0.11", "dependencies": {"ssri": "^8.0.0", "eslint": "^7.14.0", "cacache": "^15.0.5", "minipass": "^3.1.3", "is-lambda": "^1.0.1", "lru-cache": "^6.0.0", "promise-retry": "^1.1.1", "agentkeepalive": "^4.1.3", "minipass-fetch": "^1.3.2", "minipass-flush": "^1.0.5", "http-proxy-agent": "^4.0.1", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.4", "socks-proxy-agent": "^5.0.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.1", "http-cache-semantics": "^4.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^5.0.0"}, "devDependencies": {"tap": "^14.11.0", "nock": "^11.9.1", "mkdirp": "^1.0.4", "npmlog": "^4.1.2", "rimraf": "^2.7.1", "safe-buffer": "^5.2.1", "require-inject": "^1.4.2", "standard-version": "^7.1.0"}, "dist": {"shasum": "717cc40a7238a566e47beba00358c2f0b40c5730", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-8.0.11.tgz", "fileCount": 13, "integrity": "sha512-Zl0oWwxchCBnljUYJPcbe3uI/y2Hi9clqGN9YJI5lS3O2XuXcE4SySuR3LAav0I+YK9djLvC1SH0rpDilBN+jw==", "signatures": [{"sig": "MEYCIQDowOIeVcOAkOaj9EAmBD5gPQWn7sbtb4UIZkBBnTG5KwIhANpX1v+4wECxDuA9AN0FQzUXCFcVzut5eHaOuZe44nTH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73994, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf0A9wCRA9TVsSAnZWagAApKwP/3mVffzxTSzAF5yuuCjv\n8J9kXzxJsegKj4l1GkLtmZubLYEDwlQYHpVDymhO8sqcZo/4Fa8Q1FlSKzm8\nNwz1RzN7oqRu1hFQ+odjS53fZ7lXeAgkVSgPXRmZrr/X7H1pwT/PTfcWm0ek\nKuCOB/44d4EAaMuAFknlydsTeNnqJ8F2TAhicXCsIXbhu85VWBVxQB7PxXJF\nspZF+5hvOEnZD6XF5d+OyOnSNzhT+Wm8TWYaBNdraK0zd8p8s4Z5zpc+Dd6s\nhs1iqv15FMkt1WhOc3KvZBnXgQqjnFcx1kG9XFdlUhq/G3CvyLXaXM5dvCDA\nCw2pH5kjg0YqppNQKI+6b1ZxXhBL99oVrBUwIXSiuziMPoDXkLP0qujxwiiF\nfUf+2b/3KU8INcYrKyTRGQJlLpUzOCmMi9sWE8xCPYyYyk5LbmD05Uh+JUPI\nL6kaJvuSS03NuRmdU3ToaQSp8nqH5+6eKGVCkj8Lmv0FNj9ZPcyWydiSt4IJ\ntlJn7XRoGtzj62TklejlSXd8Vj8P/n1mY4kctWGShcgkKaGGF3Woz0SheKSI\nXwlmJq0iy4zKlt8Y04gw8+TKzni6awjm8EpnEaavJ8GdRuDcuS/F5II0YDL3\nVrTn3EQkhuKDW76sI/3TdzDPe1tNzs5xX6S1WsaZ3niWC8yC8JtQkgPTQ8HQ\n1T7D\r\n=i4hj\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "8.0.12": {"name": "make-fetch-happen", "version": "8.0.12", "dependencies": {"ssri": "^8.0.0", "cacache": "^15.0.5", "minipass": "^3.1.3", "is-lambda": "^1.0.1", "lru-cache": "^6.0.0", "promise-retry": "^1.1.1", "agentkeepalive": "^4.1.3", "minipass-fetch": "^1.3.2", "minipass-flush": "^1.0.5", "http-proxy-agent": "^4.0.1", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.4", "socks-proxy-agent": "^5.0.0", "http-cache-semantics": "^4.1.0"}, "devDependencies": {"tap": "^14.11.0", "nock": "^11.9.1", "eslint": "^7.14.0", "mkdirp": "^1.0.4", "npmlog": "^4.1.2", "rimraf": "^2.7.1", "safe-buffer": "^5.2.1", "require-inject": "^1.4.2", "standard-version": "^7.1.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^5.0.0"}, "dist": {"shasum": "e281aa9182d3f2ac657d25a834cd887394eacd9e", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-8.0.12.tgz", "fileCount": 13, "integrity": "sha512-cBD7yM72ltWEV+xlLlbimnh5qHwr+thAb/cZLiaZhicVVPVN63BikBvL5OR68+8+z2fvBOgck628vGJ2ulgF6g==", "signatures": [{"sig": "MEQCIFT4CI/cbqpIkybFgdbhn+fMgUF/CWik1AzPcqKmkLgzAiA370Mo8xaPX+BTBbgQ6BE2q3gvbUyYmA5M6e33IO6yNQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73994, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf0BaOCRA9TVsSAnZWagAAAw8QAKRouln40XQLVAa8dQad\nM46yHw4Ucvl1vcL1FKPqlUrW+APWHc4bxWI9vznKrYKuI5TR8Siqkc90KK9Z\nxP7bwP0vx4Cpa7XeX9CUHP7SGK91I8SY9dFE0nU16zdOmrHssMLOjxzSKMxi\n6Yd6ngyDBREvszf6sQGjwadwrHgraQuZtjYsN0rRJ/oJmuw7guYGmKBbRzLC\nxrE7SCqXedkiqAJkpgwZSNWgmP9mYiWeGcXtCeLPcHsfH0Acxa00SwKy5f3n\nksLW809Hx9tA4ITzLfFCzxNqU2IZK9W28wD7pbw6eM9g8d3Ye8YNzVu4CCc4\nnOrYl/rI6cNDhjiFITEyHVn9X4RzWWkhF/duu7Pt/jX+kQRxyIibtegs1ZGz\n+ObMBN2vRMaZhdvXi2zEhRwmoG3C35H880Zk6EzfMU5kjBr/gDCqmTY1H0cW\n4WrRYi0fXKbTYSKUcHWKwHpjx6CImuu4AAd9FVXPWq+2sKQGECgL60jZxsLR\nMye5Vw14zzcxlnJZKPn+MUph9xnIw0Cu6ycngOSePdVDM6IGKQ+oGiBlJ/gO\nnU5P4ue4ENWizADUattHWhPXTu87ybhwe+P2+GC5oFcxmYDbgA8a2iU07U+L\nitxhEN5MVWbpPizRtMVCN/MwvIPoxP/zDi3Vyi0xHk8iVvOs0EzcGIYk8TAK\n7cV4\r\n=Zgja\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "8.0.13": {"name": "make-fetch-happen", "version": "8.0.13", "dependencies": {"ssri": "^8.0.0", "cacache": "^15.0.5", "minipass": "^3.1.3", "is-lambda": "^1.0.1", "lru-cache": "^6.0.0", "promise-retry": "^1.1.1", "agentkeepalive": "^4.1.3", "minipass-fetch": "^1.3.2", "minipass-flush": "^1.0.5", "http-proxy-agent": "^4.0.1", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.4", "socks-proxy-agent": "^5.0.0", "http-cache-semantics": "^4.1.0"}, "devDependencies": {"tap": "^14.11.0", "nock": "^11.9.1", "eslint": "^7.14.0", "mkdirp": "^1.0.4", "npmlog": "^4.1.2", "rimraf": "^2.7.1", "safe-buffer": "^5.2.1", "require-inject": "^1.4.2", "standard-version": "^7.1.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^5.0.0"}, "dist": {"shasum": "3692e1fdf027343c782e53bfe1f941fe85db9462", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-8.0.13.tgz", "fileCount": 13, "integrity": "sha512-rQ5NijwwdU8tIaBrpTtSVrNCcAJfyDRcKBC76vOQlyJX588/88+TE+UpjWl4BgG7gCkp29wER7xcRqkeg+x64Q==", "signatures": [{"sig": "MEYCIQC/ZmTk+3xnhwVEpD7YNwXFU8EQgsL/VwbzW2QEGjsSsgIhAPe4T2vhMusLmfINLqv3uXWebZAF2XTkoGvHHuO9lJtG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74091, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf/f9rCRA9TVsSAnZWagAARWUP/0XVyax/nIchg1thioMb\nIBfzOsXzA6mFiV2nP53Db3rg7CTmyiBpbveB6aaZ9/wQjjkFTLEadYxM+FMC\nKTG3bjazfOZ0nEXbX+DXlKAPej7hEYRMBIpZWR80pgZU/unnFWq/zxJVhMHT\n0rh0aKjwGCs0Bo17QfJpSQBiB9FZG508xRch8HIApbNvwr/jXGGpoWMd2waT\nqZvJ8jDl4bk0Pqjpkfd91iclo7/KYC9LCeesqtuAC2pEwG/DapAky6lLoUPB\nal0BFasJM2Qc5GSvg32QS8qEpHjZ9VFkubUw7UHvHxGpLf3HJeoKw2ZdNtrq\n+lES8Zn53uOMmAfpCC4Zeessi8hYWV5UVC1Orag4r3RHrCvMxx8QFoOo6QOL\nl2tb51+7fpFV/yUK6BUNwhpMysQBNlSZdwZsxYoh4SRH7rUrNU8uBGjqWdm1\nT4u2oSchq4Ca1yX8Ix4hoYCTfkE6Vu85Qz6WiHwdwiVKBjPHf48HVDuhx2Eu\nY0yyVD+eJwKaDnZ6sH4t5O23rVwNB9dFDhXBEPUQYhTLWLUOYnTy06SaKh2l\nttiD5VMeI0uzS85M2Gt4YS3XR/LGtYEhF70zBPRx84m2FTAOxylvLV8eh7Ih\nIISXni94lcL3WMNI0AaNXdhYiMUZCITiYtL1L3qs9EYzJfbQYUrUO2RD/S/N\nHXzy\r\n=6wdn\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "8.0.14": {"name": "make-fetch-happen", "version": "8.0.14", "dependencies": {"ssri": "^8.0.0", "cacache": "^15.0.5", "minipass": "^3.1.3", "is-lambda": "^1.0.1", "lru-cache": "^6.0.0", "promise-retry": "^2.0.1", "agentkeepalive": "^4.1.3", "minipass-fetch": "^1.3.2", "minipass-flush": "^1.0.5", "http-proxy-agent": "^4.0.1", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.4", "socks-proxy-agent": "^5.0.0", "http-cache-semantics": "^4.1.0"}, "devDependencies": {"tap": "^14.11.0", "nock": "^11.9.1", "eslint": "^7.14.0", "mkdirp": "^1.0.4", "npmlog": "^4.1.2", "rimraf": "^2.7.1", "safe-buffer": "^5.2.1", "require-inject": "^1.4.2", "standard-version": "^7.1.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^5.0.0"}, "dist": {"shasum": "aaba73ae0ab5586ad8eaa68bd83332669393e222", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-8.0.14.tgz", "fileCount": 13, "integrity": "sha512-EsS89h6l4vbfJEtBZnENTOFk8mCRpY5ru36Xe5bcX1KYIli2mkSHqoFsp5O1wMDvTJJzxe/4THpCTtygjeeGWQ==", "signatures": [{"sig": "MEUCIFDaHsn9R0C78NRSAzYWdprKnHOFOm8miqNtibndtZv2AiEAnW101le1+Ne0T0oqYs2r4lP+1rWYgTIwjiq8cEp7t0c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74091, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgJbEzCRA9TVsSAnZWagAAvMwP/3HLXGXK/tpQ8dtfkab7\nNoT5cYiV+hdO04jxsMrWU8Uq4UrOJssO2/Yz4PrZvsg7lMnkiC9qSSwNv/RI\n5Qbh42s9qJuuJndBExpWSc7wGDXVBnoM0dTFrpmYXbV8AX8cNw6pHlriz+72\n09pMVtR4gzsWrO9HThGzGZZ6+cK39oN10gWyoSBrSbLNJ/NHW3Xc0Xj+5XLg\nBP+KAgWKO/fQ1lZdekV/lhYc2RzLM+fXmhgdSebNpia3Gv3Y9cvsz0lI1ALv\n3YMmym6Ut3GNMku9iGJy5vZpMOnKm2/rqR+RnVfzRQMoPkaD4J1csFJUPkFx\nW9vjzazkudubCMEuODvAp5AN4ArBUC81aEoJkDJMvzZb+lrwy4IrfGmHREKY\nG2syakNYuRn+k57d17NIfhSJBcM5GBonttQ2aXexVMtwJqO0F10rpWAnOPAN\na2pfuEL8kRIPgN0nsoSyzI+Dnr6TPfUpTmSH5OmSga8c6lxkuUk/rsrNvXgj\nKhaj9LSgfC42DI1eVj1NnS6fN4CkbIUk9ADCHjpqzV4z1D/Kp6pubh4Mg/T+\nl4CzgGHKkL0Yzb7q2S/A0/EuONFiiRAYkTkqnfIBHAI5bm/WPcUtNHUrZXu5\nbTKPEbusjMdAI3mMshAWt5QfHkRZDvFuG8NdhY/GH8F3/5WBEu6NIjk+/wVZ\nVJcV\r\n=Z9dP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "9.0.0": {"name": "make-fetch-happen", "version": "9.0.0", "dependencies": {"ssri": "^8.0.0", "cacache": "^15.2.0", "minipass": "^3.1.3", "is-lambda": "^1.0.1", "lru-cache": "^6.0.0", "negotiator": "^0.6.2", "promise-retry": "^2.0.1", "agentkeepalive": "^4.1.3", "minipass-fetch": "^1.3.2", "minipass-flush": "^1.0.5", "http-proxy-agent": "^4.0.1", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.4", "socks-proxy-agent": "^5.0.0", "http-cache-semantics": "^4.1.0"}, "devDependencies": {"tap": "^15.0.9", "nock": "^13.0.11", "eslint": "^7.26.0", "mkdirp": "^1.0.4", "npmlog": "^4.1.2", "rimraf": "^3.0.2", "safe-buffer": "^5.2.1", "require-inject": "^1.4.2", "standard-version": "^9.3.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.23.2", "eslint-plugin-promise": "^5.1.0", "eslint-plugin-standard": "^5.0.0"}, "dist": {"shasum": "d75512c94226d9d73451e8594552e43be1c86747", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-9.0.0.tgz", "fileCount": 13, "integrity": "sha512-FhvHrUyAUDzXDrejC6wqrPAx5YW5jCfpR//qSZpRPn/8FJ/75Y2o9YhdqCIsf+YSOwzdygeryexCJZv+2YgNGg==", "signatures": [{"sig": "MEQCIAQh7c8ZO/fHZHxjdhvQSE2mBmHSbUQ/0fg1RCuyRCJ5AiAOK2MipgZ5NQWn2CdajxM76tppQnjgdlsTC+tSuRxOGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55790, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgtolJCRA9TVsSAnZWagAAajUP/3Ji8zkN5IG4eWAtx2II\nCaTGQhSeM4gEtF3zW2WiQPo/IB6+Y0fIQ4ikmPGfL89WnZ7UDUXvKIqgAjkc\nQ38actVWWIsjejWsAsLQAXELWNNAWRaMMqo8doMFpUhILl8qhljABSsIPxFe\nBs0cSViDTX4YcXPf39IW+1BEq48vk3jOVIVipRCvrE0FYWBSXJcke6Lzgxny\n1+77lFVLGvFZ4e0wEJ21MQbY2E2Vo6jM42EYCiwHkwPpEg1q+6Keql/hV+Xk\nknFvkxU4h1cDBlPXUJs0jKbt7MKSZWXtmuGvwZTuvIVUFBf28t1dsunLkOrv\nalKH18XBW2NbDV2A/ux8pmZerd14anh49hkuAr9QiEZ32SP6LEBsD+vPZaSD\nI/FAuSIA9GKRr6m59OsZaPFQWG1yo60AW8UuogxIIYXsj6WHOBKtYiPHUnyR\nglYFVfqHK/Z8/uSDd4SSowrCJAxS+eub/c/PmlrzMqBPMpMRySNjIoWKFU/I\nAdd0p26bs5b+15CmS2EaHupnDuQc8ZRro/mG6li6FBoU6RUMUCau/Yn5Rtrv\nWHCl3/stHPULzYD2mLiVzHsOdDH/XYaz2TUGkYjfliSaq9lHVD0kL+2vKisW\nlHTray79Tvvwm4NpOCtQ/YdqeOME+PE8nHItATbAQ9dj1KyeE1hnRbI0ycvR\nJwkC\r\n=DhFl\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "9.0.1": {"name": "make-fetch-happen", "version": "9.0.1", "dependencies": {"ssri": "^8.0.0", "cacache": "^15.2.0", "minipass": "^3.1.3", "is-lambda": "^1.0.1", "lru-cache": "^6.0.0", "negotiator": "^0.6.2", "promise-retry": "^2.0.1", "agentkeepalive": "^4.1.3", "minipass-fetch": "^1.3.2", "minipass-flush": "^1.0.5", "http-proxy-agent": "^4.0.1", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.4", "socks-proxy-agent": "^5.0.0", "http-cache-semantics": "^4.1.0"}, "devDependencies": {"tap": "^15.0.9", "nock": "^13.0.11", "eslint": "^7.26.0", "mkdirp": "^1.0.4", "npmlog": "^4.1.2", "rimraf": "^3.0.2", "safe-buffer": "^5.2.1", "require-inject": "^1.4.2", "standard-version": "^9.3.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.23.2", "eslint-plugin-promise": "^5.1.0", "eslint-plugin-standard": "^5.0.0"}, "dist": {"shasum": "77d0e8b8ed7d387be7f137b76621fd904e4e10df", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-9.0.1.tgz", "fileCount": 13, "integrity": "sha512-c2IxuRxsPKpW9ftCUnsbbAD3rBZNGsuRNwexAbWI8Eh9jlEVPrxZYK5ffgYRAVTQBegqrqR3DlWrsvvLhi4xQA==", "signatures": [{"sig": "MEYCIQDFTOC7w0gMHta6PJYoA4MgDFmUOc6x3yrEaPSclKOGwgIhAObGjxuhX7XJaHyGTDhAHlnLsrCGdjCaYsRtjEZg4E8Q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55616, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgtr/TCRA9TVsSAnZWagAAeA0P/Rj1umkQH7vdeDAvPM3Z\nspnwjBaGQXtiGZP62J7RnfDsxGWJRFJZGJqpgF/WC74dd2qRIizvhn02tObz\nnmt7JuQCXWGrSto844VE+HWQ3+X1kT+1bcDY2PlxXf462IwSVUMqYsnPCTbY\nVhgBHX9Gl/uR296LBYSi8gWj9UdWvI+PoWi12wKtRDeflCjesK9BT3dDfqDQ\nToOYAF9IIk2C7IqbZrzHWaGZ4uH9NIyIdeF0VdeDAxjLYTwzwU+qK2UIPbOq\nGLbc7vOda1mnxYZrn5A00fELrY8TR2xYHV23MhdxlQ0k68xWNNQKqLpD104J\nZrCJ7t8Ezap8Y6YcFHsyeQJlW/sSVijqffPYo3D23VAVV95Z20bikGt7rISU\ndKjq8/gN0RlNqlJGQQgbs9FpMlvdJUl4MkHpwDsi72MSVcgv/kemzYOj4NkR\nO+q14ReRUvS8AccBHDrUynLj/RdG+l8dKkrMMrz3BiiAWtUN84XsMXEm12PN\nN3bi8Iqcshb1655HbLsLwKhT5EsWQ+rQqET3PKWPowMS9XkSCy9TB0VLtop2\nFg5rErfM3vFVxM8Tn7CqRWEt8CXLDktLNQ44FMuDa6gDP+iXe8eViWXtH/go\nhant3Dhs3MEKsS6sPbnfgEBdBISVbquRWpGO/Y17uLs7H70lkBhAg4rGBfuQ\npVlo\r\n=Bl0g\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "9.0.2": {"name": "make-fetch-happen", "version": "9.0.2", "dependencies": {"ssri": "^8.0.0", "cacache": "^15.2.0", "minipass": "^3.1.3", "is-lambda": "^1.0.1", "lru-cache": "^6.0.0", "negotiator": "^0.6.2", "promise-retry": "^2.0.1", "agentkeepalive": "^4.1.3", "minipass-fetch": "^1.3.2", "minipass-flush": "^1.0.5", "http-proxy-agent": "^4.0.1", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.4", "socks-proxy-agent": "^5.0.0", "http-cache-semantics": "^4.1.0"}, "devDependencies": {"tap": "^15.0.9", "nock": "^13.0.11", "eslint": "^7.26.0", "mkdirp": "^1.0.4", "npmlog": "^4.1.2", "rimraf": "^3.0.2", "safe-buffer": "^5.2.1", "require-inject": "^1.4.2", "standard-version": "^9.3.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.23.2", "eslint-plugin-promise": "^5.1.0", "eslint-plugin-standard": "^5.0.0"}, "dist": {"shasum": "aa8c0e4a5e3a5f2be86c54d3abed44fe5a32ad5d", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-9.0.2.tgz", "fileCount": 13, "integrity": "sha512-UkAWAuXPXSSlVviTjH2We20mtj1NnZW2Qq/oTY2dyMbRQ5CR3Xed3akCDMnM7j6axrMY80lhgM7loNE132PfAw==", "signatures": [{"sig": "MEUCIQDHKtSGkyK9rvAe3qVrmKRl7i7/h/u7pEfgmEZ6qaOwYgIgTGik7vJ7qYndBt+L/qAWNzuCOmmT5AZEg2IAhkJNbc8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56169, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJguSauCRA9TVsSAnZWagAAN44P/RrNAmOMQ5jgySs1wnmx\nld9yJh8ZifgmGmWVTJ8viEFLeYJJdw5H4ROXI5rkX51ILds/JMFRKFMvi1aA\nsHGUuLzHWghGG3L/jIR/TnXJmKV2aZaU9LlH32rGdajOIyUfdZx523X5ypcW\nOY15kuB7NV96kmWlafYwzT63ZdE4gnT2cwgd9f//BhGsaCbAXPbmBK9bz7Dk\nnH6zdoZ0SXE1QYhVDTYISHnId20k+KowwG3KmCrP5d8c7VphgXQkOj+OWs39\nuezPX82R0wnZdVt4m3uvgRDjfUIR25m9DL2UARHFdtqlHIzDbORABsmOlBki\nFGYa/QJfRxUhfSsYpgGbJGy+P92MdGylAllekFAphhsE2zIefFOjARrU8UOd\npvvf775OJUO0ictShx5v+69siFOOAuhn8ex8Cc4nvpZRKLuxd9POcOcBr7Uv\nykxbMHkqbRc0/IrlyhZdRgXmnePDolkM6fGJ9JM2LGghkDFM8uPpHLdJvjEj\nsPFe2N/jfSz2ToJmGZx7V9eTfOWyNQ0hBQHgk1AxNUUGfFsPb8ps+BsTcrC3\nJFehSV1Lz6b/imqYiVKu5ARUZUlU5NHdljrU5JM48Ynj/XnmzbsR0oZURNDv\nxiHYk7viM1PsqoRBpeRh2zm5noiYCarqjiKXmqc3wd+eEpUx7kw6MPiQ8TJH\ndjzo\r\n=n/Ei\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "9.0.3": {"name": "make-fetch-happen", "version": "9.0.3", "dependencies": {"ssri": "^8.0.0", "cacache": "^15.2.0", "minipass": "^3.1.3", "is-lambda": "^1.0.1", "lru-cache": "^6.0.0", "negotiator": "^0.6.2", "promise-retry": "^2.0.1", "agentkeepalive": "^4.1.3", "minipass-fetch": "^1.3.2", "minipass-flush": "^1.0.5", "http-proxy-agent": "^4.0.1", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.4", "socks-proxy-agent": "^5.0.0", "http-cache-semantics": "^4.1.0"}, "devDependencies": {"tap": "^15.0.9", "nock": "^13.0.11", "eslint": "^7.26.0", "mkdirp": "^1.0.4", "npmlog": "^4.1.2", "rimraf": "^3.0.2", "safe-buffer": "^5.2.1", "require-inject": "^1.4.2", "standard-version": "^9.3.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.23.2", "eslint-plugin-promise": "^5.1.0", "eslint-plugin-standard": "^5.0.0"}, "dist": {"shasum": "57bbfb5b859807cd28005ca85aa6a72568675e24", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-9.0.3.tgz", "fileCount": 13, "integrity": "sha512-uZ/9Cf2vKqsSWZyXhZ9wHHyckBrkntgbnqV68Bfe8zZenlf7D6yuGMXvHZQ+jSnzPkjosuNP1HGasj1J4h8OlQ==", "signatures": [{"sig": "MEYCIQDRjXlwm41gO9sgj2ePh8NWJhsbOMFABgkiKFMBMST7qwIhAPot5bCLMVOn5axCKfPWNPXREs3zeBTvgDXJ7HNeoTg6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56396, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgynq2CRA9TVsSAnZWagAATsMP/3WsInsboGkjZd1O8URo\n6lbq5sQtfQjzyoMkIUwxlAnZySKVSOGV21kvZf/FoQGoQUfLKKdX1gtgtGe4\niGKk7+yYfXzQzpWLwe4fFkMDmn38PYlwb7doqjT8QqFcr0ANDC2GuQhs+kqr\n6lB3nS9TWFN4EaE8Jvz0ExWB4bqBNyAAUyap1RXkoD5iO/F8033ZcC32hYEA\nJVNotdGiZrWk+2nh/o6kBVx7Id5YFBpgR8rGN9gQI7doCA6CY0pIJUGtve0V\nG8B5TMA51AaZq0pK9OEGMCIXUCz1gKOTLEdTCv/rxZBdVZqQa6eAdwdtykpa\nPzxPQ0wIL8+H29ApGDXBeEWwGcenyB0GbbOA8ce9zhk8qX1ZfquStWiC+aSK\no+1lFD4P9k7MZ74GmRf3zxXUpcN6PD963PeU7ccTfmHsolx0y8xWAkyg1wDp\nYM+wjxFz90xCGxXYONZ7R+ry6/SVEKsdFySUVYmbzR8pftoHzhVqtzkTNeC1\nAauPdMxd8SlvoB+rv5WYBZbjMxCzIseL7dEZ9SQn07Yxr5X4qp1OLzOxHLmy\n9LlWN2W7TZi2wzadnt5TWvE34lYRjw6ETrUMdAmJtzNL3rs4+uNm4XIZzy5V\nzqqOgoE8/5J2CaQmp0kHSbHGIpo4+b3zS7mHWQrpGsOvY/p5ET6zUHML+vL9\niUWq\r\n=p2Ca\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "9.0.4": {"name": "make-fetch-happen", "version": "9.0.4", "dependencies": {"ssri": "^8.0.0", "cacache": "^15.2.0", "minipass": "^3.1.3", "is-lambda": "^1.0.1", "lru-cache": "^6.0.0", "negotiator": "^0.6.2", "promise-retry": "^2.0.1", "agentkeepalive": "^4.1.3", "minipass-fetch": "^1.3.2", "minipass-flush": "^1.0.5", "http-proxy-agent": "^4.0.1", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.4", "socks-proxy-agent": "^5.0.0", "http-cache-semantics": "^4.1.0"}, "devDependencies": {"tap": "^15.0.9", "nock": "^13.0.11", "eslint": "^7.26.0", "mkdirp": "^1.0.4", "npmlog": "^4.1.2", "rimraf": "^3.0.2", "safe-buffer": "^5.2.1", "require-inject": "^1.4.2", "standard-version": "^9.3.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.23.2", "eslint-plugin-promise": "^5.1.0", "eslint-plugin-standard": "^5.0.0"}, "dist": {"shasum": "ceaa100e60e0ef9e8d1ede94614bb2ba83c8bb24", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-9.0.4.tgz", "fileCount": 13, "integrity": "sha512-sQWNKMYqSmbAGXqJg2jZ+PmHh5JAybvwu0xM8mZR/bsTjGiTASj3ldXJV7KFHy1k/IJIBkjxQFoWIVsv9+PQMg==", "signatures": [{"sig": "MEUCICEuyCGpibHaJxLA+QFlKcacXHyPA+MOYtMxhwJxSNsNAiEA5OvKvImNfo8AcdZVekQI8ye3bHG84cgiQVKk93ikvEo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57606, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg8HYDCRA9TVsSAnZWagAAy5AP/1T5xpFaBfF0qiwYKOC6\nipQAjumCT4/Tw5/arE3ZrWlrBG0OrRte1xHL76nfbHUtx05lF4Zq7OWFuRJ5\n6ItEIZGyUyYZq+zKR+LN2kQHSdN4Zs88HQVHzlOzJxVfY+5gjP4X2fl1POWU\nNKHOStJ2GddQ9NeM8t5Nqko3QY8L+Ew6dnclr4TERuj0AWfIMTkXxtoX8yJj\n1OflXzeUKD4osrudCIkCXeF1+k2+NWlUfpIlLALAxiz7NRf6JUO1MKShEudq\nIypgAEl0vosFYSvJQjGcsZ36veoOHIBG36sp94WYT3o1dGHwredOUIWZTnLo\n7sRbBmDUjSrC/jg33PdZ29w0bB5V4uVbKX4gRxpQH0Av5ekp1dJDxQSo7iJE\n5lx5d+rpqDXMh45oLYtq8qjnheLago0T8h8trKODLGpurKsGod3Y1f7CTCxk\nnFXeXo9vp7BNiG0lo4Y8q93XgDztHZsLDPdi9LLO3m+53UfQrSTvyfSOB3ii\nSdAUCfS3ocZifvj+UwvAvMfFAMcknuWMwj4FIEhwUeklRutgrsQH9BAuZhfL\n+t4Utgt3wymogF/d0IOFFH/k19gSc3z/Ayhlw0GAMt6xXFXHH+6CJuHLmF2J\ndyMbKZvRg4ezsGtztsNVzosXqnYXx0DhzqZ1q0k4aG+IQUd9PKCINkC5KFDT\nWolG\r\n=2CCv\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "9.0.5": {"name": "make-fetch-happen", "version": "9.0.5", "dependencies": {"ssri": "^8.0.0", "cacache": "^15.2.0", "minipass": "^3.1.3", "is-lambda": "^1.0.1", "lru-cache": "^6.0.0", "negotiator": "^0.6.2", "promise-retry": "^2.0.1", "agentkeepalive": "^4.1.3", "minipass-fetch": "^1.3.2", "minipass-flush": "^1.0.5", "http-proxy-agent": "^4.0.1", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.4", "socks-proxy-agent": "^6.0.0", "http-cache-semantics": "^4.1.0"}, "devDependencies": {"tap": "^15.0.9", "nock": "^13.0.11", "eslint": "^7.26.0", "mkdirp": "^1.0.4", "npmlog": "^5.0.0", "rimraf": "^3.0.2", "safe-buffer": "^5.2.1", "require-inject": "^1.4.2", "standard-version": "^9.3.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.23.2", "eslint-plugin-promise": "^5.1.0", "eslint-plugin-standard": "^5.0.0"}, "dist": {"shasum": "e7819afd9c8605f1452df4c1c6dc5c502ca18459", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-9.0.5.tgz", "fileCount": 13, "integrity": "sha512-XN0i/VqHsql30Oq7179spk6vu3IuaPL1jaivNYhBrJtK7tkOuJwMK2IlROiOnJ40b9SvmOo2G86FZyI6LD2EsQ==", "signatures": [{"sig": "MEQCIBCcF7rJPQpxg35lRsMM1Qx4asJXLy5gWLwpYljX38WwAiA0IM2rnSxkvMyoGjh5BJgdDeYlWbX5TXRptzC59uq3VA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57606, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhHnq+CRA9TVsSAnZWagAAdWkP/RJY2o4BwvEEln2cCoev\nRRpy1it6VLwzsiLprMQ1R3u9kNDPzNInj8jCQkdpfIxgeWqL+tRP54B3vtx/\n1Noh2NYEZWicOW830xAkBdEuSNgHUsdMCUFOJPRdwUJ3OLYt552d0g+pkFRd\nxp3B7bU66ThTQ3yldL/l8hzVSXmU1u4et83tFBTAksDucxJ2VZoCAOY/vwCa\nJeAy2Nt32bSegC2Hz/xIIva5PkOi7aTyn21cZ2Y0rr0NQGTyXUOBN9pp+kxW\nQNHA8jR7qsV/DiBs5J88GR0NPjfqve3UgYVp/vAUbqFgDvRcdoNpfYnNPTtP\nwjxbVQ23s+ELReh5b672d7oaz18nMIBQTb4PbHZM0cG0bGmwme7A1N15Fh/v\naS3A2WmYIP17jUHIRc4muzkekYIG6tfC+evL79bhMFjFzK2yvnyk8NVc/9oj\nvHeRzUu0S4M/WHfTSq0yTkKqaMc7dCSNBREtFbpvQ72qGUMDTjGYHIAPfkfw\nXDWy3xICbXTJ1AyWa/vwba4ZW/z4JRJqbEzYEgCYcIZ32wMagD6pnXWzq1f3\n0fLTQkFjN+VoRfOUYyG//4VINIvx03neeeREoUM7MCJ6ma7WF6zv+JUlJX5z\ne0+jljJSl/1CAvF7LLfhQp8Af0Q8fD7ziSw3NdHNpnI397dC7cSDtodZcknh\nD9TZ\r\n=bPVg\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "9.1.0": {"name": "make-fetch-happen", "version": "9.1.0", "dependencies": {"ssri": "^8.0.0", "cacache": "^15.2.0", "minipass": "^3.1.3", "is-lambda": "^1.0.1", "lru-cache": "^6.0.0", "negotiator": "^0.6.2", "promise-retry": "^2.0.1", "agentkeepalive": "^4.1.3", "minipass-fetch": "^1.3.2", "minipass-flush": "^1.0.5", "http-proxy-agent": "^4.0.1", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.4", "socks-proxy-agent": "^6.0.0", "http-cache-semantics": "^4.1.0"}, "devDependencies": {"tap": "^15.0.9", "nock": "^13.0.11", "eslint": "^7.26.0", "mkdirp": "^1.0.4", "npmlog": "^5.0.0", "rimraf": "^3.0.2", "safe-buffer": "^5.2.1", "require-inject": "^1.4.2", "standard-version": "^9.3.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.23.2", "eslint-plugin-promise": "^5.1.0", "eslint-plugin-standard": "^5.0.0"}, "dist": {"shasum": "53085a09e7971433e6765f7971bf63f4e05cb968", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-9.1.0.tgz", "fileCount": 13, "integrity": "sha512-+zopwDy7DNknmwPQplem5lAZX/eCOzSvSNNcSKm5eVwTkOBzoktEfXsa9L23J/GIRhxRsaxzkPEhrJEpE2F4Gg==", "signatures": [{"sig": "MEUCIQCnNyu+HWPeAR4SsD667Khuv8a7CebZz5M1iAL1hiIe1QIgbnoiz0CRK9mBhMXLAFyYg1YEqQUcymF+bWuc0TiyAxU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57581, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhJRZHCRA9TVsSAnZWagAAkg0P/iBCjj2NyEpfZcCXvTm0\nFc4s3tCgo+pUw9N32Z4Aeiq9qiwXcrx+VOdibzNp7xkXNzBbtxBiXuPPCvL5\nlpJUxX8SYFe3jZnSl1dgNmSeQhKgDiHYmZPSwAYygYftVa4AHhkjDkgwhfSo\n+FOAtCv0sBNJk8chTQ9ijVST6T7ATjUL4k4AO+wQhFTaEJXBJ+kp8xfryb4M\nCYMOqnjTYsQudzJzSgMJ3bhzZmVrqpSznZ6Qb9sVxPasOV4xPHmE+p6vlMcF\nkw/iJ6FoXXBDXtuBwNbwNPZS68vuwRyF8UYiLVvh+KoFXUSH6yfSAWoCv1zj\nS1xUvQg+rZLhn5J1uoae2m9WoCJJiHWTuMsZXT0Sf7HlYtHzWZ8O2YZqoyBk\nOfDC2cb+2G/MDJmpTZgeyIAhMvLVkeUeKvQDZNOZyHYZ3FQSB0k7nOYRMvcG\nsOEjF9CyIDgfsdP4eWZGn9/CQOi9IUZCrDJ7YGhvyYK4ili35xwmoR7Dn5nQ\n14s+cDXQjUaXt5mqcT7uwtG0bO2/o2C37SkmogzP5bbtUMCIHRrXImi/l1Bk\nP592ZaaaeIDKz+4LsHd12j71lGipYrhfC5y24aOUcm0lDGHYuqUi8DNKhQAM\n3tChxZYKIhCu1yLW30D96NH3W+eDcBGn/yUW6T/AsaCVJwuarFYfleGM3xzr\nPfMx\r\n=+bh6\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "10.0.0": {"name": "make-fetch-happen", "version": "10.0.0", "dependencies": {"ssri": "^8.0.0", "cacache": "^15.2.0", "minipass": "^3.1.3", "is-lambda": "^1.0.1", "lru-cache": "^6.0.0", "negotiator": "^0.6.3", "promise-retry": "^2.0.1", "agentkeepalive": "^4.1.3", "minipass-fetch": "^1.3.2", "minipass-flush": "^1.0.5", "http-proxy-agent": "^5.0.0", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.4", "socks-proxy-agent": "^6.0.0", "http-cache-semantics": "^4.1.0"}, "devDependencies": {"tap": "^15.0.9", "nock": "^13.0.11", "eslint": "^8.7.0", "mkdirp": "^1.0.4", "npmlog": "^6.0.0", "rimraf": "^3.0.2", "safe-buffer": "^5.2.1", "require-inject": "^1.4.2", "standard-version": "^9.3.0", "@npmcli/template-oss": "^2.5.1"}, "dist": {"shasum": "a2dc77ec1ebf082927f4dc6eaa70227f72e5a250", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-10.0.0.tgz", "fileCount": 13, "integrity": "sha512-CREcDkbKZZ64g5MN1FT+u58mDHX9FQFFtFyio5HonX44BdQdytqPZBXUz+6ibi2w/6ncji59f2phyXGSMGpgzA==", "signatures": [{"sig": "MEUCIEJbHs6cs5HWT4csV/5bqMiSdpbQb686ACVnzEtNgFsqAiEAttJzFsl98yzOf/6WpzkBaxuUJYC6AxhXRkhQJ3rz/5c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57705, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8EKPCRA9TVsSAnZWagAAeEwP/i3MnIUa5PzREoPRyiD+\npssrY/SeU6dAeKS9iYjsasY8irP3gtJGY/Me5zX5HjjIQsR1f4/+lGwspvNf\nLgwrWca7wt1w0NCj3ByQmGkyTnZdTF5bXBKSld5JBAPkyJkjFsRHj/XtJ3h7\nVlt01S4IYaxTJGCKzNSroM3+ccvh5Z7gADuQ+nciqNBsPKiO9VzleV/SAqVv\nnBMrnEFx6ULMlA4UpJHRRstkdDbvA0JRnMn76wVxwMkl1GtjCzF57ZoWiYEh\n24k/aCTDGUDRsE9ajE891AyQxo11pFPY1PkEEJ6or4YrJHMkwxRDrJT9CbXL\nRQ5MRKK/jNZkZNeb8hU9zz0vyV34XWN92TFj1rSxjxWR9st72/7P+lY5TlTi\niUl8gF+Lz9WBb0JskgybCzT2anHoi8v4PvxyE2YLDCrVlTnFtfluTirPHMbs\n88Cv8XdE4w3JzVb8VwNWXQFa3d9WYnoTN8GDPXkX2qKPJ+jATmKBF/AMGqSs\nmmHH8lUPAZjhFlT/fLlBiXAHfNzoBFFi0uF6als7MmNmefQo7vhc155CS3sE\nV1TuNbQTxtE43COA9Pyz7zu1Kb3EpbQ7/XehfTn+JUIEpPS1LXzdg8XucR3R\np8cDp5lQZV9iy2o795mkF6Ba5AGELBz6k1lPS11uLKYH7FpNCR3WKoQp+eMx\nhndT\r\n=s4Ty\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "10.0.1": {"name": "make-fetch-happen", "version": "10.0.1", "dependencies": {"ssri": "^8.0.1", "cacache": "^15.3.0", "minipass": "^3.1.6", "is-lambda": "^1.0.1", "lru-cache": "^7.3.0", "negotiator": "^0.6.3", "promise-retry": "^2.0.1", "agentkeepalive": "^4.2.0", "minipass-fetch": "^1.4.1", "minipass-flush": "^1.0.5", "http-proxy-agent": "^5.0.0", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.4", "socks-proxy-agent": "^6.1.1", "http-cache-semantics": "^4.1.0"}, "devDependencies": {"tap": "^15.1.6", "nock": "^13.2.4", "eslint": "^8.8.0", "mkdirp": "^1.0.4", "npmlog": "^6.0.0", "rimraf": "^3.0.2", "safe-buffer": "^5.2.1", "standard-version": "^9.3.2", "@npmcli/template-oss": "^2.7.1"}, "dist": {"shasum": "fb374080b454ae0591c55342c19813943de9370a", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-10.0.1.tgz", "fileCount": 13, "integrity": "sha512-xJVRzemKMb9r2gZ5DJfkvbeSGbBKOtwI4G8hJ1Ak/2jIFJFveyQxN3d2OhXlAk7rLzAL/O+Ge8u+nb6/Zrrd9w==", "signatures": [{"sig": "MEQCIGxGS4wUwVaSnyjxaHbRNXmwEIf7U8J1exjD6vcTLvjsAiA2z1ArZQgZduecwJjHznIlFi/lXYcAd5z/B1PHGK8xjg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57734, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiA9NhCRA9TVsSAnZWagAA/pIP/39y08wBAPys+0vSbI2t\n205sidQX6Fc2tgGyb2kfIU/2p1Q+yT4VvnVL6WpwmwjPA9yDZdIWSZ+07o8P\nZ2E8k8o3gX/x2odGzAWs9Pr1k1LNtGDxJ6WOwq8thZnyR/NqaVKEng0B/eA5\nx4qtZvk39apCYVUxek0t1qFZ5YjMKjix3FVcycq/F7sPhWP3qbNwOvBJ3sEv\nSfm7Fr4yo/QQVZXwGZR+bCEG0u8NIvUDTq+HWA6NMuRyVVkPIFNY59dZdpla\n8nSJVQ1iHUQ/j1b1DyCAKjJIBfUqMRr4Lm2+aWdNOG2+H9SLQEhxnr/D44OM\nxOngbKk7YSWnqt5jRbtcw4gExuloLGLD+YDgHAEATYHkUhUPxJux+snToZh5\n6WzAMr5r9QMAl8Lt9zyDRC5vC/ldmV4rhvw+n/yDGfiY4luP3Bzx4OduHx+b\nW5wAn34IcRs7cA2guuorKh78USsQdBYKWGhZ4ckIOrg/Lqbn05csr8LvnBG/\n06NURoP3hpOviqNm/INmgj2s/qlW9+/EkgLYMQbEhTgiJjqlK9q2qfEDechk\n8CSEq/16A2uAZNZoqb/Hml4xbMn8Yr/7hgKNUz/8DRkDTH5SunffWKObBxHV\nN61X1hBVHaBYZ+ZN/kBP2rMbepeqyv2er+Uw8ChPgv7lA4xR7090ryBTYAwP\nKiZn\r\n=nCot\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "10.0.2": {"name": "make-fetch-happen", "version": "10.0.2", "dependencies": {"ssri": "^8.0.1", "cacache": "^15.3.0", "minipass": "^3.1.6", "is-lambda": "^1.0.1", "lru-cache": "^7.3.1", "negotiator": "^0.6.3", "promise-retry": "^2.0.1", "agentkeepalive": "^4.2.0", "minipass-fetch": "^1.4.1", "minipass-flush": "^1.0.5", "http-proxy-agent": "^5.0.0", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.4", "socks-proxy-agent": "^6.1.1", "http-cache-semantics": "^4.1.0"}, "devDependencies": {"tap": "^15.1.6", "nock": "^13.2.4", "eslint": "^8.8.0", "mkdirp": "^1.0.4", "npmlog": "^6.0.1", "rimraf": "^3.0.2", "safe-buffer": "^5.2.1", "standard-version": "^9.3.2", "@npmcli/template-oss": "^2.7.1"}, "dist": {"shasum": "0afb38d2f951b17ebc482b0b16c8d77f39dfe389", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-10.0.2.tgz", "fileCount": 13, "integrity": "sha512-JSFLK53NJP22FL/eAGOyKsWbc2G3v+toPMD7Dq9PJKQCvK0i3t8hGkKxe+3YZzwYa+c0kxRHu7uxH3fvO+rsaA==", "signatures": [{"sig": "MEYCIQDyMZ+rSPJ48p2tZC/3nuWLrrtmuXTvR1vO4ke4S2F/UwIhAKkVLT4+mfWdvZA0V4TVraEwutzuIQsQzXsCqDUVz5Wr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57734, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBSYxCRA9TVsSAnZWagAA6w4P/2wmr6o3+BKHz+MHPM2c\ntFRlhjFfnNyBz2v91bgN4L5XuYuldwaPIiNn2/zXJLL02SfO+ZZKFZEpnyQL\ntYuONpomYUQOFDxwfOaRlcDirC20B/tnqvzdodtEkFBPtTU4FL8ps7k4N6Y0\nPDZ4qjMe6n7pO8lq5Jgwmma6MB+JX/hZ0qSY4aeaeMXUYI8pmCuicfRKq2gT\n1I82JMqcn+UEqCsjI5FHpHGC9zty2da1IZ3vqCClOt5MFtGmJdLBhuh4U/uZ\njC0xGsi3sVefuCwVS5y1rt45+V2ZuHGOH3UwmezQUOv4q8LOpFHASBE4XBSc\nz/0Lfoextu+McTnpTYDEDDh1nvggfuusLo7ErNL9fTRLVG9oaEVyI4MlrsuY\ntoUXEUl55MYGtr8zC8Y86zb66Ox/6jgYvUUozHQLqm5V+qfkOc9aaVrce4Sk\nbr1AlzkRL5vlx9WynJ/N4GZ2HIJWmOOuyT3efLXyVE54zFAOm3WDSvWDOOw6\n6tfex/0czUXy6CuR/eNIV9zVwOzKj3Zc0lYJVRkdKNlWLjrxnUlvkUoDzIaO\n7jXQOwGGuVs60tA8Y5+KJLPz67bdDar5I03/DTT0257LP27fx870pVdHyKv/\nM5UmD0GUATpaT9rxyrBs11d9+To83xxOmwXC+dn9Vl9TAzBL8/4kG+/HCo4v\n0zY6\r\n=gCQx\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "10.0.3": {"name": "make-fetch-happen", "version": "10.0.3", "dependencies": {"ssri": "^8.0.1", "cacache": "^15.3.0", "minipass": "^3.1.6", "is-lambda": "^1.0.1", "lru-cache": "^7.3.1", "negotiator": "^0.6.3", "promise-retry": "^2.0.1", "agentkeepalive": "^4.2.0", "minipass-fetch": "^1.4.1", "minipass-flush": "^1.0.5", "http-proxy-agent": "^5.0.0", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.4", "socks-proxy-agent": "^6.1.1", "http-cache-semantics": "^4.1.0"}, "devDependencies": {"tap": "^15.1.6", "nock": "^13.2.4", "eslint": "^8.9.0", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "safe-buffer": "^5.2.1", "standard-version": "^9.3.2", "@npmcli/template-oss": "^2.7.1"}, "dist": {"shasum": "94bbe675cf62a811dbab59668052388a078beaf2", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-10.0.3.tgz", "fileCount": 13, "integrity": "sha512-<PERSON>zarPHynPpHjhF5in/YapnO44rSZeYX5VCMfdXa99+gLwpbfFLh20CWa6dP/taV9Net9PWJwXNKtp/4ZTCQnag==", "signatures": [{"sig": "MEQCIAlIVDxE8QPdViPZcb7DRwY/NB44n0NBCnHU4wKMquOVAiBwti1Ib8bH/dMH7Yh1DtIdilxn91pE9+y6SYhC8V140w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57770, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiC9uBCRA9TVsSAnZWagAAg1AP/ivg0nrt4VWqdmLiQCXT\nijYWYHgeIp/cShdv51jW5tbxWnV5f29IPdBVtxxFwC3wWPuSnaeqNhiJ9ZhP\nCJiUKNDZughHjekTu/injjkji2Ytg0fUdF4aHGC5Kdh2P065HNkyEHSTy9BN\nT7TT+YsY/t/7MI5SwADlTklV2dzCFjy0yx/4Pc/Z8ygUeABd1aWMJijxt3Wt\ndJiIlsMIZcpRatTOJdQaMA2jgEhjLJQyu7Mn9GTH5i4QCF3NVBR0TK+0lONa\nn+osdNwPVRuG6U5t60B/AmeZkhcOSn7IPMQUu3or/tiRb8cc3cjsRjjyPsR8\nhMgJ5KJXvuxsGDxWUMAb3jJ4YAGm+lQeQF0MqlVhU+wXF7mmc39MlNT2cXWF\nKfiKfbrbf1rSDY0pW+2ghqGY8qcu3OBu9PTZy/xHgW8SCakfPwLeKjjIjwtZ\nEWG0tYYt5e2ORvZVbkiily4JQ7hJqhDFBTEN2TUz7reSwA+WwjgIhu1M+qSZ\nSOjoeD0AHuG6NCwRTm1pHQtaugetihLNXgwvSsFXWaGnb/IU/YBQX8s0AxUc\nulCQ9e5s/osKELKq0q9msfgh2EfJ+MgSLW4wnpONLt9hb2OSFmTEnrTKPcne\nTw4DmQ2/m3D1+6dqdocEdRRpxlSJZToR8dLXO7Cj0jFuRrCMiRxY/RYn07ZG\nnxnj\r\n=QL++\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "10.0.4": {"name": "make-fetch-happen", "version": "10.0.4", "dependencies": {"ssri": "^8.0.1", "cacache": "^15.3.0", "minipass": "^3.1.6", "is-lambda": "^1.0.1", "lru-cache": "^7.4.0", "negotiator": "^0.6.3", "promise-retry": "^2.0.1", "agentkeepalive": "^4.2.1", "minipass-fetch": "^2.0.1", "minipass-flush": "^1.0.5", "http-proxy-agent": "^5.0.0", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.4", "socks-proxy-agent": "^6.1.1", "http-cache-semantics": "^4.1.0"}, "devDependencies": {"tap": "^15.1.6", "nock": "^13.2.4", "eslint": "^8.10.0", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "safe-buffer": "^5.2.1", "standard-version": "^9.3.2", "@npmcli/template-oss": "^2.8.1"}, "dist": {"shasum": "309823c7a2b4c947465274220e169112c977b94f", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-10.0.4.tgz", "fileCount": 13, "integrity": "sha512-CiReW6usy3UXby5N46XjWfLPFPq1glugCszh18I0NYJCwr129ZAx9j3Dlv+cRsK0q3VjlVysEzhdtdw2+NhdYA==", "signatures": [{"sig": "MEUCICu8q5cH6QvYf3WL6js6RaDKSb8M/MrGAH6l2PDapmBWAiEA7z7OLmBIiESdDA8p6km86GWcmVfC7gKhpEn0nk/nZMg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57771, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiH6G9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoTDQ/7BDVpAN+3DH3GJ+g2towm0t/ehqh9tbVvt7TuMjvdQy06lEk+\r\nb5V4TLiJ6YTKyLEdj2V9Fc6o52HhvJDwq1xPFIxBB2Oxik4/f+pW7KFoQdvl\r\n1SUexMvH4n80vIqstuaj286pgxHIOgbsUXa4fTOjkFv5PGP2dOB06897uLrd\r\nhXCr1iEqBtrmf/X5kthj/CtVvqWcNGv6LwocI4UWxVBLiodUvbwIVBnnb+0k\r\no3ANbMHWG/HMInZ9GqSje94/fk48aFVIjcXWQiNreVXieNItdemBoJk7Cjwm\r\na2SG3KIRk+xvQWQrn4Ti9sTcX0CgLAOPUWsEbuALahtCzACWMZKN0wLvEwQs\r\nYdwf4WtPgiM5c8q7QYPfJY57DNRfCeXimIjeTopgGZkpYRMmyfijUG6bDtHL\r\nq8Xzep25qiGsyu9uzNBXacbktETs8VzsAREV1U6ZK047+mPcWVvfhk9+rroa\r\nWFv0H7qHiJWutt7mdFFPojVmDgzXVyJJqubr9ssLi5aa0dmnY8aro4lNT+rC\r\nBjkStjFGp+RcnC8+Go/7OWj0ivpw4kVxNC4WdTwS1FAY2z12K9TjFgLYNJLO\r\neekWG8C4GyMzl5vx9KC1DLUXqmSbw/o2XiDgbKJG6fFNvvoENdbFimvnLRxr\r\naoUTlI279phTuS7UgD1PctQgUmBW/jQzNvs=\r\n=96wq\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "10.0.5": {"name": "make-fetch-happen", "version": "10.0.5", "dependencies": {"ssri": "^8.0.1", "cacache": "^15.3.0", "minipass": "^3.1.6", "is-lambda": "^1.0.1", "lru-cache": "^7.4.1", "negotiator": "^0.6.3", "promise-retry": "^2.0.1", "agentkeepalive": "^4.2.1", "minipass-fetch": "^2.0.2", "minipass-flush": "^1.0.5", "http-proxy-agent": "^5.0.0", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.4", "socks-proxy-agent": "^6.1.1", "http-cache-semantics": "^4.1.0"}, "devDependencies": {"tap": "^15.1.6", "nock": "^13.2.4", "eslint": "^8.10.0", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "safe-buffer": "^5.2.1", "standard-version": "^9.3.2", "@npmcli/template-oss": "^2.9.2"}, "dist": {"shasum": "006e0c5579224832c732c35b7bcc43c8602da775", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-10.0.5.tgz", "fileCount": 13, "integrity": "sha512-0JQ0daMRDFEv14DelmcFlprdhSDNG7WEgInTjBeWYWZ78W0jfDqygZdPLhcrQ4s/G8skNhBrS4fiF6xA+YlFjQ==", "signatures": [{"sig": "MEUCIQDDnPAUkVloiQuzwti63bsDi9peMdjCgZp4YtlPhjUF4gIgXU2JYeDm6XIqMHygoqAPlOl0qXAKty90Pabv9m+loow=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57806, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiJ7u4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoT7g/+NvpermfTcNJJtIU7lbm5odI/FfALYpbFiW5q2Gnmd3IbhL6f\r\njenWIAfL3QVZVmRGYdn5KwBEGuNwl+khA9YRfbWp8FNOV3UGSarrw72pLa+H\r\nP7p6LvvQCw0+9QKBuQpunZPIDQp024Y6a4X/nD7zh5gmRso5LBdZPSUXPukj\r\njLgpYq46TbtOydYNuP+JSWHGJ/G3HEbhxJNW3TSD0a5BH1kfKkNo4MZXr1XK\r\n3U/dRBkX7DODpXmgz6j+SnMI2A/0647+yVaLVwocZxGaNFY1csjSeCsb79sU\r\nLijiL+MQ9glrrMuLK2bhgVMksCb/jXcsbaB3TrCEjQcWUWq76BK1uoCLZAoj\r\nsBeOP71VyEgiixJUeu6plxUKUc7SGaeOb0KB3XDMc46uIXIU7IRIbQne6JPR\r\nBVF2xsXxEZyeItcx6CG9KhI2uL/h3WCAxAAAGsqFBbcLeLzZ5OUwbJupbJXc\r\nrQj6/RpBJtAYADfiLxY0ATNRVmkTJ/BiLcO5NYQuJkhrCJlddt3JP428o64/\r\nnkSvpea92YMPRRfs+9DrrJtBIbwKH5/AeVjrdsILpuVBkSEQtPF2l2UOlLG/\r\nd4uWQtmNvveHFweGyJ75U5mTpybEQw1npOGrWUp3e8ynjCbFO/SAaiDrlztl\r\nFQ1sg7mFbRy/ULPdF+0uUzvnkvOJ7rk8aCM=\r\n=coFk\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "10.0.6": {"name": "make-fetch-happen", "version": "10.0.6", "dependencies": {"ssri": "^8.0.1", "cacache": "^16.0.0", "minipass": "^3.1.6", "is-lambda": "^1.0.1", "lru-cache": "^7.5.1", "negotiator": "^0.6.3", "promise-retry": "^2.0.1", "agentkeepalive": "^4.2.1", "minipass-fetch": "^2.0.3", "minipass-flush": "^1.0.5", "http-proxy-agent": "^5.0.0", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.4", "socks-proxy-agent": "^6.1.1", "http-cache-semantics": "^4.1.0"}, "devDependencies": {"tap": "^15.1.6", "nock": "^13.2.4", "eslint": "^8.11.0", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "safe-buffer": "^5.2.1", "standard-version": "^9.3.2", "@npmcli/template-oss": "^2.9.2"}, "dist": {"shasum": "671269de09cc51208413460898efb7b36adf5534", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-10.0.6.tgz", "fileCount": 13, "integrity": "sha512-4Gfh6lV3TLXmj7qz79hBFuvVqjYSMW6v2+sxtdX4LFQU0rK3V/txRjE0DoZb7X0IF3t9f8NO3CxPSWlvdckhVA==", "signatures": [{"sig": "MEQCIHdg3HdD8UBdQUrLB+kQkMmFsuNSS7ZE27ddk8IXqV+WAiB5Ldz5NrHSM5sweZ/TwLKh3pLVxecEKFlI2pfk6XIv1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57806, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiL6rJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqW7xAAm7J9mt8nPSXdAMgSWSIv1oaaeeMKT1FrDKY5EE7sl5B4jzXz\r\nVWhBezRfjWvldM+dQn5tRayqUou7ZvqZVv8cF+ehDHmG/XIz6JFqI7A8oMY6\r\nDn5zlomuPckxHQITmlbE336VHAffyrL5dj6V8trhoBpz4UKrjpzKNtHv0abg\r\nAQ6LPDAJVJprRZiOcgMinpQqc/2aF/c1vRWUqIBHPK/fpLmn8O+HNl4HKd9D\r\nfMMyrfn9U3M8YZw29pOzotm+LHg2qdaStanxM+z/A38fU8D8O3zhrstqZVii\r\nIBcJ3C8LW5wMw0RLP6ok4rAll6Dwc4YWkE0PwguIBGojPOuzOffeYyUWymIA\r\nZQxsErb3FUGe0Q0I4Bark7aLIAxJYHF8hInLwy/eMNs6dX4sw92miccB2ElM\r\nwgemsF564uAqs8kIfJHIm0ZAb4AmSs4euEq25VeKs4gHHEiDbC4Bogwvltfk\r\n0pV/mwEsFJvSzqtiZgOPcb5S5zUDeuZFcXF2kBUSQeVilPztzpPbthr91rhk\r\nhWB2xE9EPk7kSFarwt6yES5jewGliEYoNOnox2idS2CAZa09XVxD0OFj9KhK\r\n2yutdCiozcisUdi4a92NaKjaB0hnz901dSeQz5sOuiB+I+CRaKxdZofPkWGT\r\nLclEHxmGvBQyL5IyZExlY7RP/4f6h/8a14I=\r\n=pU1A\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "10.1.0": {"name": "make-fetch-happen", "version": "10.1.0", "dependencies": {"ssri": "^8.0.1", "cacache": "^16.0.2", "minipass": "^3.1.6", "is-lambda": "^1.0.1", "lru-cache": "^7.7.1", "negotiator": "^0.6.3", "promise-retry": "^2.0.1", "agentkeepalive": "^4.2.1", "minipass-fetch": "^2.0.3", "minipass-flush": "^1.0.5", "http-proxy-agent": "^5.0.0", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.4", "socks-proxy-agent": "^6.1.1", "http-cache-semantics": "^4.1.0"}, "devDependencies": {"tap": "^16.0.0", "nock": "^13.2.4", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "safe-buffer": "^5.2.1", "standard-version": "^9.3.2", "@npmcli/template-oss": "3.1.2", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "7232ef7002a635c04d4deac325df144f21871408", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-10.1.0.tgz", "fileCount": 14, "integrity": "sha512-HeP4QlkadP/Op+hE+Une1070kcyN85FshQObku3/rmzRh4zDcKXA19d2L3AQR6UoaX3uZmhSOpTLH15b1vOFvQ==", "signatures": [{"sig": "MEQCIAkHlyFbimvfWIEidVPHOQzYarJXR+Pun+ajI/wW9TizAiAhH1RkBNxISKtsGrJgkKX4LmNDFDWB/0g2eQUQAo2Eww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60264, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiPNE4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrImA/+OasI0wsBhQKXZbsXGnIZwL4z23S/Wv8RI2x9nSaRoJDUgChx\r\noZmC3/J/oQfeuZhVBInY1mHF4n0ONpLKmxmrBQub4M0aTdRcKT3Xz41i3GON\r\nfCNBZJ5tzmRXQzOkQWwfALrNq6Be9GdZIeKXUBn43Dc4xuzc4gYxqcf8fth+\r\nb8dbYGq0KCllQMZRsARd+KR+emH9nYYCCa7dQ/rsBGEVuL3CIZ2+jdq4CA96\r\nwG9AUfzq4ham/XM5iladBdUJ7DWod7Ng2B9tisG9On6FMpjeoVoetLzDE41y\r\nu91+2yNLF2ozINJFHvpvzt8X/yPWGXioiIM24X+hzM+qg2TXh81QZlavDoKU\r\n/fMJ162w3/YJybJoCyrFNdfW/WOFuxIFsiePOe49/+UKVP646ctQwPUnOZsA\r\nZzGmk/3DzjWka4ZDgmWtdGSG7HkYVjZ9ue4I4dL/3m/aKF0CzkZ6LOsHzJM5\r\nhQ2IWixfoLfrj3i+zJxjVvKg4L63+3fkIQuXShfSAQCUosXesgXV8lJPYxVi\r\nFgAb/wznrSiOhkCHigq8h1FRs6l22D34BHd50NRdIKfbPy45egAFpy7mVffP\r\n0l++wA5dREwTrq5C0D69bnInhSMMja4gqRTetDvADgX/yQ5wDQJKePsYLbkR\r\nL801u9bOiekYIrIIUTIboPZS1Oz74aaOqbo=\r\n=wIa8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "10.1.1": {"name": "make-fetch-happen", "version": "10.1.1", "dependencies": {"ssri": "^8.0.1", "cacache": "^16.0.2", "minipass": "^3.1.6", "is-lambda": "^1.0.1", "lru-cache": "^7.7.1", "negotiator": "^0.6.3", "promise-retry": "^2.0.1", "agentkeepalive": "^4.2.1", "minipass-fetch": "^2.0.3", "minipass-flush": "^1.0.5", "http-proxy-agent": "^5.0.0", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.4", "socks-proxy-agent": "^6.1.1", "http-cache-semantics": "^4.1.0"}, "devDependencies": {"tap": "^16.0.0", "nock": "^13.2.4", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "safe-buffer": "^5.2.1", "standard-version": "^9.3.2", "@npmcli/template-oss": "3.2.0", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "d93e9241e580ebe0b2b7294b2d1b79ab4e5adfa4", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-10.1.1.tgz", "fileCount": 14, "integrity": "sha512-3/mCljDQNjmrP7kl0vhS5WVlV+TvSKoZaFhdiYV7MOijEnrhrjaVnqbp/EY/7S+fhUB2KpH7j8c1iRsIOs+kjw==", "signatures": [{"sig": "MEQCIQDwjBJjPTe/YV1Y7syhtPHYD4qMebvhCTPIdNiN1NQBoQIfCIUhNRAGSQvIvRqlY1heu8JLYOOjIHSTlkN5v6KYuQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59352, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiQzbEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpn2Q/+Kjxitc+aM4RU9h/wzvi0zm8OcZ7YfqJKrBJl/kw99V8cmwMA\r\nwSVZG8OUs2Zvsg2LL5zWapdJY6kQbmQhwU4FV5Tyvdtp0z9l9hF0dFA2soWD\r\no+HJC4O0CB2Ifcl6fxdMrZUS9l3XflehJ7D4TI+V79DtWKFP+SzvFNEGEyaD\r\na/daFNI5adrfoFEXjatsUy4USOQyQIbkWakdsjne3DWyObzpr5rM9AlAx1UW\r\nuB84xsVvt0BNhcSOgtg+02fUaJYpfNp7U5JyYZB1QaTUiuOMQTfuf4D4AtRl\r\naShbTEFCzvgTGwJ0UJBLlq3DZGjwlw6uwmKChPtIhp18agrn9mt9ZaBrd72y\r\nGWPBdyuPbxfUUhKg2tpkX3YeeCU8KMDp8azsyLLrjSCfeiSPkiL2TInmBFst\r\nK2W1Vsj8NHZgry2d2rXkKHTlIv+A3gb6QnUCsXBROlNMFKnP6aBvtAxcgG60\r\ncKJ48S9+dMs/ppwSMuuGxwdHIYhtpm+AHk8PEGx6gb7+JQs9BlQZyeTPiQT7\r\n2OknTEyR9s3xIblYsj9cLSzOOtScw4p1BTzgkVWKNZgxxsvtoJsFpY1zdCoC\r\nzls2DCkHQWZBmTPtd5DrzPdpY8jAxgfFayOKQjWeBNbqw2K5dW4Gz1i1AuSq\r\nrUKvh6SvbmbJpkB7buNBZTLhwD42plpgREc=\r\n=ZRZF\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "10.1.2": {"name": "make-fetch-happen", "version": "10.1.2", "dependencies": {"ssri": "^9.0.0", "cacache": "^16.0.2", "minipass": "^3.1.6", "is-lambda": "^1.0.1", "lru-cache": "^7.7.1", "negotiator": "^0.6.3", "promise-retry": "^2.0.1", "agentkeepalive": "^4.2.1", "minipass-fetch": "^2.0.3", "minipass-flush": "^1.0.5", "http-proxy-agent": "^5.0.0", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.4", "socks-proxy-agent": "^6.1.1", "http-cache-semantics": "^4.1.0"}, "devDependencies": {"tap": "^16.0.0", "nock": "^13.2.4", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "safe-buffer": "^5.2.1", "standard-version": "^9.3.2", "@npmcli/template-oss": "3.2.2", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "acffef43f86250602b932eecc0ad3acc992ae233", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-10.1.2.tgz", "fileCount": 14, "integrity": "sha512-GWMGiZsKVeJACQGJ1P3Z+iNec7pLsU6YW1q11eaPn3RR8nRXHppFWfP7Eu0//55JK3hSjrAQRl8sDa5uXpq1Ew==", "signatures": [{"sig": "MEYCIQCMOdlukktsR7ZOkVLybrvHasYNjpGQwlKhDeB47s2uQAIhAL8GVl4wucoGK+ARXsGKkUGRdFPxLazYU0vX/oOrYuuZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59352, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTHTqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq7+g//X/4FVtzm/S9Hs9j07Ap+8K3OIIFanTy+xIj8vIPquKVEq7lg\r\nccIieTiGl1y55l5SHKCD52RZeFS9DqhjpW8sxj8HM9uaWXAgy5lg7qbiwi+c\r\nOcVzwUghidAVV5MB3AAhhXGlLw27BitcklTTHZzParXdlt/cJG1LLbhgoPgx\r\nj/RmkdXQoLEP6fXxtLOVSfsUgv9NBaC75Hk2MpTQiXaWnE6DP7Cj1dVvlHQm\r\nZXzRrib55pOH6kv5JbBL0wbJRD1tKB2qhjZz3GLfOFMqosOCqxY7Nyl86UGZ\r\nLd/egYCppUB11qn9d1bPhM5J+s8bZJavdV156xt6NWhmQkHIa5p/03KF3E6x\r\nxwvkOse/VeoM3HV+y2Aw00Wwwuaw6omcvit78E0H+xSyJfWt1sqWwhW+JkgU\r\nKHsa6UGbH1ucQkas9DqZa2HgxXjVt9AMX25qbqf0q1sFDN0G0Z3dp9QKm+tz\r\ns3MCX1WeJJzZxdpmuDIikSXX79DqalG2ca8+5rAOgRA3O7M3JH8t39pH2osn\r\n8NkQVX1WsOremnb5nTqvE9Me81w5LNrnWatsfW0WYQ6za8fOMp14XdCdoeaG\r\nNg2TYUEjh2A4ZJ5Rkn1UIYPPQ2mvmi1iZDdy9iNspKRtVksAeXQtjVLZcSV1\r\nAS4QTzj85iw/MKPgrNIbY+qaSJzWMEJFYyw=\r\n=aMl0\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "10.1.3": {"name": "make-fetch-happen", "version": "10.1.3", "dependencies": {"ssri": "^9.0.0", "cacache": "^16.0.2", "minipass": "^3.1.6", "is-lambda": "^1.0.1", "lru-cache": "^7.7.1", "negotiator": "^0.6.3", "promise-retry": "^2.0.1", "agentkeepalive": "^4.2.1", "minipass-fetch": "^2.0.3", "minipass-flush": "^1.0.5", "http-proxy-agent": "^5.0.0", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.4", "socks-proxy-agent": "^6.1.1", "http-cache-semantics": "^4.1.0"}, "devDependencies": {"tap": "^16.0.0", "nock": "^13.2.4", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "safe-buffer": "^5.2.1", "standard-version": "^9.3.2", "@npmcli/template-oss": "3.4.3", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "d7ecd4a22563b2c05b74735eda46569da26a46f6", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-10.1.3.tgz", "fileCount": 14, "integrity": "sha512-s/UjmGjUHn9m52cctFhN2ITObbT+axoUhgeir8xGrOlPbKDyJsdhQzb8PGncPQQ28uduHybFJ6Iumy2OZnreXw==", "signatures": [{"sig": "MEQCIAizkisYWulE/3rfXjL7rhSakLpHDhn2XzD3mX9tkadMAiBDZ5y7Y35NiXlw3kPP4KgHYBQL6KAFVN0B48mv6IH7Gw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59470, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJieSHtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmryIA//R57hDah9Uujbmeo/6U1aTveZaLm4rSKb530o6AnkFwX01rhj\r\n0cGzpQW18RROiyWxZ2NJLXuPchTHTwq6AhjvRU/xm7PDVS8UMe0UPPuzKkp+\r\nwCVFihWKO0GAHMYh0iou3Vpv7JQsUfDnEoqjXknrLiQ2rj7SKjPWwb+CCENm\r\nLAOWlYQjzmfC9K1HmfruclWKWnloJD4tCTytahjK41n4ak9aCrDGwYy1ERf+\r\ndAV/n8BThnlvjK6fSrMBYPO9thEPlXeRpy4dRVNQKi+cDKEjW3/vHpG/x4cX\r\n5X0rEsYefNjc2X0xL1421BMtFHQEvCNvV6oG2hBMGx6QRUlftLXc31usZomO\r\nqgHjZ3zR6YlVT31wJMx4JBqUa3aOJBAgO9umDLwlmCjyMoJqJr/xEH5RUwUf\r\ntY2bFPZMxc0fqA6tnhIo5H25HuYa0cT0kEgbiGzOdnr0NuQmalYbOu5jeL3R\r\nw/t9uLuQcaWx8tW75omXk5zjKtD8kDyLAcYOpG52xQ3pWhAUlOadmrVBlUlS\r\ntgLy7Zjt5NzNvR0vtgnbojQB3RcLHqklJM2JNhkNi6skMaoHc/40+stCkqSM\r\n2xB3W1JzKgaCZkVcHb7/rvpq+8y+xDL2OzeLNMsXb9a9cFSx4n1i/c7puRmd\r\noAGWv1FgrNjFzSIrWVIfFcWnRXZF05Ixk/Y=\r\n=TTMQ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "10.1.4": {"name": "make-fetch-happen", "version": "10.1.4", "dependencies": {"ssri": "^9.0.0", "cacache": "^16.1.0", "minipass": "^3.1.6", "is-lambda": "^1.0.1", "lru-cache": "^7.7.1", "negotiator": "^0.6.3", "promise-retry": "^2.0.1", "agentkeepalive": "^4.2.1", "minipass-fetch": "^2.0.3", "minipass-flush": "^1.0.5", "http-proxy-agent": "^5.0.0", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.4", "socks-proxy-agent": "^6.1.1", "http-cache-semantics": "^4.1.0"}, "devDependencies": {"tap": "^16.0.0", "nock": "^13.2.4", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "safe-buffer": "^5.2.1", "standard-version": "^9.3.2", "@npmcli/template-oss": "3.4.3", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "b68a64367d2c402f24edc48308ff193d11fc2618", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-10.1.4.tgz", "fileCount": 14, "integrity": "sha512-hU1w68PqfH7FdMgjbiziJoACY0edlbIZ0CyKnpcEruVdCjsUrN+qoenOCIayNqVBK7toSWwbDxvQlrhH0gjRdg==", "signatures": [{"sig": "MEQCICQT1E/1koADP7j5eArIeub8Y3+IcuNxKFwPHLbbXia+AiAW2XfuxvLEJlNHE7qcHQ0PqPdaHDJAeFfQGr6XNf8+Jg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57824, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihSgwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrgFQ//fgSE811+iFK8f2rLSjinAcNv5oYw3sCODe4b/AvDS6LqhAxF\r\nmd5NNczId+vTDIIF+sOjz69bKuS+FRxLIGZ0roIpNSL/EbAUXUyHUX7bBLmd\r\nBGdEQcbEG52NUUHV9uLHC+babYTbd34p6DxI1UlOKCPPf5wQwnq7IYX/ApC5\r\n6TH0HTLJTWTZrJhYSQqa/jGZ022VClljRUnUEshqqsvGAOzNb1Myuhw5OigV\r\ndTuKl9duZKgBGc9pJjq/PuKDiN0fXtf7bpiG+4VUV65P8rANxUJX7tijD5Y1\r\nv1f+bDokaS7rftt8F2LWaftetfa6byIcgoVSJMpE3ECss04DXPAsvhUu8/nl\r\nbdKb4kR5E8RtI+DPpchClwXBZtHwPHjuTo0roLN/jyfZIRfx/A6GinMsBstO\r\n61NYuM17mPWdlY5HUMEG/hRBO/yC9j4jyF1ZuUBrmMx/K/hUdhs6ADR6H8p7\r\nuKW2RZbQdcoePGJbDLrmzbDJq/tMRROlKPx8K6nytvmrqfYeO7wU3fqqKmCI\r\nj7N8hizEb8Wv2PXbVVFYETfCBrnI75m1dsDa94mRBXGF2BEBflDS/spBNpSx\r\nL4eKfdnbTScyF6tiqSAgpgUgLlU0/5fSp0js9LDp6G3DgyukPNVNIaF/YIdl\r\nxt/iv+2bv5c/ssq0RqJ0q00BOvXzkiQ9h4M=\r\n=EGlX\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "10.1.5": {"name": "make-fetch-happen", "version": "10.1.5", "dependencies": {"ssri": "^9.0.0", "cacache": "^16.1.0", "minipass": "^3.1.6", "is-lambda": "^1.0.1", "lru-cache": "^7.7.1", "negotiator": "^0.6.3", "promise-retry": "^2.0.1", "agentkeepalive": "^4.2.1", "minipass-fetch": "^2.0.3", "minipass-flush": "^1.0.5", "http-proxy-agent": "^5.0.0", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.4", "socks-proxy-agent": "^6.1.1", "http-cache-semantics": "^4.1.0"}, "devDependencies": {"tap": "^16.0.0", "nock": "^13.2.4", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "safe-buffer": "^5.2.1", "standard-version": "^9.3.2", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "d975c0a4373de41ea05236d8182f56333511c268", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-10.1.5.tgz", "fileCount": 15, "integrity": "sha512-mucOj2H0Jn/ax7H9K9T1bf0p1nn/mBFa551Os7ed9xRfLEx20aZhZeLslmRYfAaAqXZUGipcs+m5KOKvOH0XKA==", "signatures": [{"sig": "MEUCIEXxB15GIXSKxgDxuB31W+9PZlTYA/BJR5eX2qd7FiLmAiEAgrcp1RMaKQilFCc9B8cLn3cdKTma+99o+mkj7E48jBI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59048, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihoVnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrZcg/7BbrWTZZPkv9TYtCweTmsd5OGtSx+5AwpuZnwpNM6MZbvdqOB\r\nK/kHi/m7zRijRDyFCR2DXkWPMeE62R4KF1IlA39+PQibXs03PC0ugVEuaW3n\r\nmqVMMKPVy6I/2l0yllykHz+ORqQGGC1y/qCLOHD9lVeo6gFHKZPV63sYppth\r\nhOSJ7jssx8cYmKfzwwL2AbwUECEhX/mfCDkTu1B2u/li2kj6sjcb3t0C9aMz\r\nyLHozTygb40AH5gT2lidNQA/toQ1GJcNVGTqm49eBJslxqmy6h0rAGUtggi1\r\ntGqFI/X1OTRW4RKkSSaME3JTUNOdcF0I9HlJ4FXiNrLJ9xo7hSW7oNrb5hlf\r\nrx2yQEV7PA/JWmppCffXxDV0WPminCvo6mDc7Of8M4ch/qmdlFj/QD1x/Xn9\r\nzltmYMMYQFFMiQc9qwkHzX7i5HrzDh7QpLn+LCCqIWDgUsTVbNUHEsBNNXSr\r\nzF6lxxG5YDYwbcftw5KOlrFoQA70nsMEVyvYCZalROLugnNrdqA9qM+9t0KY\r\nUqsNyRKxl/7WK+f1r4kk4ayl85Ewxb5wxMQ/dlt1nkCPHgFvh2BNr9MsUrMK\r\ndG5TUgWBWnn1FqMj8Q5Svjzj75D0ZkwKlsuAC+jXH/0rgA6Kn5TtwIcEAzuV\r\nY2ya9VSAwh7zRfqLqzrRh95bQ2ZZUTGEsfw=\r\n=erpn\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "10.1.6": {"name": "make-fetch-happen", "version": "10.1.6", "dependencies": {"ssri": "^9.0.0", "cacache": "^16.1.0", "minipass": "^3.1.6", "is-lambda": "^1.0.1", "lru-cache": "^7.7.1", "negotiator": "^0.6.3", "promise-retry": "^2.0.1", "agentkeepalive": "^4.2.1", "minipass-fetch": "^2.0.3", "minipass-flush": "^1.0.5", "http-proxy-agent": "^5.0.0", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.4", "socks-proxy-agent": "^6.1.1", "http-cache-semantics": "^4.1.0"}, "devDependencies": {"tap": "^16.0.0", "nock": "^13.2.4", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "safe-buffer": "^5.2.1", "standard-version": "^9.3.2", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "22b3ac3b077a7cfa80525af12e637e349f21d26e", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-10.1.6.tgz", "fileCount": 15, "integrity": "sha512-/iKDlRQF0fkxyB/w/duW2yRYrGwBcbJjC37ijgi0CmOZ32bzMc86BCSSAHWvuyRFCB408iBPziTSzazBSrKo3w==", "signatures": [{"sig": "MEUCIFAKu2ycsuNl4Agu8+TRwmAEgqjRhBhE+zXQMgfadxvVAiEAwCqc3ROB489rLFuUHyYYCV11lnxGlkUVyQTOwDFDDuQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59154, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJikScBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmolow/5AROmZTnvTxJGlAqXIHllShAq390tMGxT60zfirn1BWUgIXx2\r\n+O7VfKGyc9/6BrL4N0MBahEj6b0z7lGC9Dx+xTbfYLwPvxy3Y7XiThUZuxQe\r\nC4Ll8dEqy9cvCbhw+m5HXv6/JhBFUsAy7d2earo7CvxO7SRW+o6cA6VFC5ws\r\nV6PSyfWaJr2yjfPi5srqJNX/PFagG4Y7D1XyntmEirqxkMYKUT7BVI0rEZ2s\r\nkBeCUkwEFIa1ljXGJkh3lojXLGx/MUkx+xaoOwF9OPJ18R1LFPkZrwxVbbwQ\r\n/Ce7FXdzn3pHzGIcIHtHSlIYtaGGpKdUXGTr0gXDJI/hS+r4fnhDDk4EKM+B\r\nRpBzBQbWZZNfnrav+/B4VXpCSeqZVKqYNikuUxlsCnH0Hs6wWJc3FY3w4Ajf\r\nSDTz0CVzRUflA9k7WmwQYrN4Sy8YO6uIxptgUa2Ek0f0aPQegZnrUe7YS2tR\r\nmXHiGCIRx6+6ATx4BCxfpPTKi4DfBbVVoxdCu9B2YRxFd2nefxbsdJgk8UOm\r\nd6fkicUtzNP3FuelnquAOMgV1E4DVrf2Gt1q8lIUtsoKPRyeH/4Cl7XoPiEh\r\n0beCFS/e6hCjCE6IP6fSHjb432CTKHsye9YMD8JIuOReq01rcubnKdO7JUPM\r\nL36LFdWGpdAExsUWT36nltay1XM0pfA7a5Y=\r\n=xuOH\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "10.1.7": {"name": "make-fetch-happen", "version": "10.1.7", "dependencies": {"ssri": "^9.0.0", "cacache": "^16.1.0", "minipass": "^3.1.6", "is-lambda": "^1.0.1", "lru-cache": "^7.7.1", "negotiator": "^0.6.3", "promise-retry": "^2.0.1", "agentkeepalive": "^4.2.1", "minipass-fetch": "^2.0.3", "minipass-flush": "^1.0.5", "http-proxy-agent": "^5.0.0", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.4", "socks-proxy-agent": "^7.0.0", "http-cache-semantics": "^4.1.0"}, "devDependencies": {"tap": "^16.0.0", "nock": "^13.2.4", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "safe-buffer": "^5.2.1", "standard-version": "^9.3.2", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "b1402cb3c9fad92b380ff3a863cdae5414a42f76", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-10.1.7.tgz", "fileCount": 15, "integrity": "sha512-J/2xa2+7zlIUKqfyXDCXFpH3ypxO4k3rgkZHPSZkyUYcBT/hM80M3oyKLM/9dVriZFiGeGGS2Ei+0v2zfhqj3Q==", "signatures": [{"sig": "MEUCIQCIWHDu1FxaEVayQf883qVF4hFZBdMbOpCTW727EgunNgIgYJOq/lYLMOUL5XaMElzrY9mgfyRBF+rOx6M31BxXIZ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59256, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJimPAfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqEGhAAjhDFN9CigCU3qER4A836uKLkyY2nqkzaMlcXp8T+7NINVBhr\r\nt+PkAyewF2HW1ee4aZm61OahXHFpdwNBmfJPatZP/a0wwP5kfvGSgLj3xlr6\r\nrHSLqd/apiDe/1XRRHrMQOD9O8axrMlzuFrAh7dfs09Df7LnEZx8lFPzE1He\r\nOh683xzV8qwnQB6rwc7ZvkO0H8ad61J3dyXzs8CuvbTW2DIJ7zEvFiy748F3\r\nq3mT2wkBI0qdDZa4dg2GlemsGB6HVk2PPzNpGHEzWObj3rtXObWYP4oBL8sZ\r\n1HC6JRaXqtoD0pzfk4XXjqJy26tsUiXSWgCQggxK/wcJOi5yyOY9PRFjOjOv\r\nspTnkUig7g5dETdoY1+HZrYpJ5zdEnIOJ2T1YKZaT4CHg28sVqyGkjX83LwN\r\ntXWqMpsZHbdEEa9mtzF48TSueL6TJ+LmnxjPF5EXvGI9JeTTUgI1E7liLQB6\r\nGkyycEtYzGilHcIhQfPGDepCCvzIw6HcAXDLiVCrCeR9eRFNR2CeWML7m1TK\r\nlk6uhhnGW0xYJk8PapNGRLxjAwl5MicU2/KzxyzO5CiYbvxtHW9lvKCAjKnb\r\n/cddi0TyMTAB+5ZLgdx0eI1Wt0YCn6ll8CtHk2SjePIF99YwS9sZzL7PMSmv\r\nUPw3ClGOitpRAEjOFlJvfD9EOZZdU/DMsXU=\r\n=hdJa\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "10.1.8": {"name": "make-fetch-happen", "version": "10.1.8", "dependencies": {"ssri": "^9.0.0", "cacache": "^16.1.0", "minipass": "^3.1.6", "is-lambda": "^1.0.1", "lru-cache": "^7.7.1", "negotiator": "^0.6.3", "promise-retry": "^2.0.1", "agentkeepalive": "^4.2.1", "minipass-fetch": "^2.0.3", "minipass-flush": "^1.0.5", "http-proxy-agent": "^5.0.0", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.4", "socks-proxy-agent": "^7.0.0", "http-cache-semantics": "^4.1.0"}, "devDependencies": {"tap": "^16.0.0", "nock": "^13.2.4", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "safe-buffer": "^5.2.1", "standard-version": "^9.3.2", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "3b6e93dd8d8fdb76c0d7bf32e617f37c3108435a", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-10.1.8.tgz", "fileCount": 15, "integrity": "sha512-0ASJbG12Au6+N5I84W+8FhGS6iM8MyzvZady+zaQAu+6IOaESFzCLLD0AR1sAFF3Jufi8bxm586ABN6hWd3k7g==", "signatures": [{"sig": "MEUCIQCavw5aQFGiZ0422VRLPP3bDqEbSbPk/wJfTwSF2n1k1gIgFrPc/G+XJ18I6n93PRW88EvBVcuPtFa6ZihKBMPbVt8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59260, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisIBtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqbKhAAnj0+R1r9WjtHvHV0QSSoSAW69LU6wBXkcYlaLNz1M9zbUOsS\r\nXWLis1D2tWhGjc6Q79qKZq6hlHnFE/RAgiAIVn/TJznfKLBvT2y9mwczpcbt\r\nexLHiWOLBH/MckGo6BCMOB1RrLsikZutnbmGcEsqfMKUB8Os4EI3wMsim7F3\r\nUnEMXjD8E2T3YgrIMTL4E40syaR6fv+l/yFH1xYYFmXd05rRH/SLvBFxmi5V\r\nXhkcQsiCrOUKd/fVzkyj6vOngoLSl+IYMfs5QUkjOVei6yi0pC1ACwt4gb3L\r\n8kfMXtIm9Lzo6yHmjV/N0k+UqwqizakZK+1DH7ynMw50iYqL9U6ZUTudFyA2\r\nvKwkWC9LNvKxtYpGQtGHc3EF/3TiYhET+CnRnYQehZYjv46bHmC2XMMLG6XI\r\nQnGP4dBW+vPIYU6GcoQl8X0brUyZkJl8Vxi7muJKE6bb0W2wdysmS97+FdTr\r\nRxcpoLLCjIwFhZJ1uyU2u1L/vfHuhFdirY1XX9Llw+4I3UDRJCeZcXtp7yBA\r\neTnhvSPJbhG4MmeCgvN6KfmuUwKZmdq6RuMJ0RXiUZe9NUDe1qySwtfXjK6p\r\nH6jVhbTQaG9lrAQYRY4lGfW2mO49PjgRD4l0+0USKen1fkwymdddAibqqeKh\r\n6HD9JHI/gcf9db9iahmRAS14IhiLol4fSQQ=\r\n=dFFh\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "10.2.0": {"name": "make-fetch-happen", "version": "10.2.0", "dependencies": {"ssri": "^9.0.0", "cacache": "^16.1.0", "minipass": "^3.1.6", "is-lambda": "^1.0.1", "lru-cache": "^7.7.1", "negotiator": "^0.6.3", "promise-retry": "^2.0.1", "agentkeepalive": "^4.2.1", "minipass-fetch": "^2.0.3", "minipass-flush": "^1.0.5", "http-proxy-agent": "^5.0.0", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.4", "socks-proxy-agent": "^7.0.0", "http-cache-semantics": "^4.1.0"}, "devDependencies": {"tap": "^16.0.0", "nock": "^13.2.4", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "safe-buffer": "^5.2.1", "standard-version": "^9.3.2", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "0bde3914f2f82750b5d48c6d2294d2c74f985e5b", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-10.2.0.tgz", "fileCount": 15, "integrity": "sha512-OnEfCLofQVJ5zgKwGk55GaqosqKjaR6khQlJY3dBAA+hM25Bc5CmX5rKUfVut+rYA3uidA7zb7AvcglU87rPRg==", "signatures": [{"sig": "MEUCIQD1S1IJ5IxbhfRrb3wMou47mFqM4l+pInexxlHjWkwEZQIgLvpcZpAH1mIgPMATlszUMmcHksPKR6bpPt0rT37H0Zw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59270, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1vlyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpz1g//RkooBCSM0pIW864b2mVKXwu+oXauFZ440qM25X77ZoZFuRs8\r\nJS4MSMVxgMhX9sPhouoUSxbQUtNosBvzNn8r4cRLreGBx222VJBzLqhkgJ4d\r\nu2cnBc5iWGrendgppqJzgOKyOxorz9Q5eoMKan/gKMuZxbKuy9hItcSAHxcL\r\nfiw/kAxy/Z68Om0lRG/4bltcJRBoBOTToaMVTT968OZ2RyOihux3rtg9vMwi\r\npyoJ5K2P4ZrAgusXmvfu9RpVce/uRccFe8s67AjPMoRJvE0c0odCb7DIaq+h\r\ndVh5A2XSNLg+Qm1RSz2QVlGbwLteJok4rFPdfdwQuy+8CXTSoGYeFN6dh9Ff\r\neXk2JhZBlZ349ZAnPMRD6ZCmUPfqB31QTGOhT34vOmdjjcgzaLBcK8ja2ohK\r\nDLi2wErzmEvjZoGobPVlzUz4XO9hLyVMOhcKYc13PHYa4RCHXogPz7FXOQCi\r\nXgquPpgi26ec6eT9lomNf5n2w4Rp3Rk5g4Awvynov7St42GrgemiFz03W83t\r\nbuM8s9bK6pgjPW9UKCV6Hmym6q8qgZFVa25IZa5+lv6zoMi3GPcOhXiKBmG7\r\nM9vRBR3TEsoyWQU6UgnzSe3BlGXb4iBdcdVg4bxyGcydoh+ei28oFnStySWg\r\n7ipYqHM0ATqsZ/Gjg3596YXTxJAOozvIGj8=\r\n=jsIk\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "10.2.1": {"name": "make-fetch-happen", "version": "10.2.1", "dependencies": {"ssri": "^9.0.0", "cacache": "^16.1.0", "minipass": "^3.1.6", "is-lambda": "^1.0.1", "lru-cache": "^7.7.1", "negotiator": "^0.6.3", "promise-retry": "^2.0.1", "agentkeepalive": "^4.2.1", "minipass-fetch": "^2.0.3", "minipass-flush": "^1.0.5", "http-proxy-agent": "^5.0.0", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.4", "socks-proxy-agent": "^7.0.0", "http-cache-semantics": "^4.1.0"}, "devDependencies": {"tap": "^16.0.0", "nock": "^13.2.4", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "safe-buffer": "^5.2.1", "standard-version": "^9.3.2", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "f5e3835c5e9817b617f2770870d9492d28678164", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-10.2.1.tgz", "fileCount": 15, "integrity": "sha512-NgOPbRiaQM10DYXvN3/hhGVI2M5MtITFryzBGxHM5p4wnFxsVCbxkrBrDsk+EZ5OB4jEOT7AjDxtdF+KVEFT7w==", "signatures": [{"sig": "MEQCIH837ugsDH/JQQj0ZOPe5fLa4qd8S5M25jJBh5cokmM9AiB5lFM6wwfG3HhHL1+nzPbJGlEKMAVKwHJvLw6lUoYcOA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59330, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi+q6cACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpfNw//cYxxa9yu80zII2b2GehxJl+FZw3FxkHweKVahS5qS0ur8X58\r\n5U+SPP/ez7GItLM/THOjVP4pSNNGZnihoeNU1/epyVDxwXYIbmdXHegOGXoq\r\nMF3v4dqqktqmJwD6IqCuIh6fGWBi8qVRZTtRXW8eCqr6zaXAKLUEfHhhDQ5I\r\niBRWOHzyz3eIhwv8JnZzaL0OH54BEe4qPezsfB3sXfTnKSLrCxQmjViLT6Ed\r\nWMR6hYOP68A6dbvbO/khKk1eJbAuoTi0U2c7q/mvpKkprecNYgodNVKBKivY\r\nQa+aSbtaqyi2Fa4LGNvlIT4XfMW90fOPtRADKKGSNAJR4SjUVQrr5qd1H7Ba\r\nnj3SvwZf2iW1DPThMmuV2VEbNzcgiTjkjq+Gq9AU424UpmLYK5seGTmZvV/p\r\nRZp6BQBe6dmiQIyJAWEs+Z5NUUpDe6TRCwcTkytOR4cyyDkFiRoYS+AK8GJ2\r\nRtHJjKte5P/r0eX/ynDgwcGTk6pkF2MENOzDtqMm8dJOJnltTz8ycNiJDsWN\r\n19l75LWMIfjBhKnz2CW0GxQkxrVr8HkClzpHEyRX0UANG6Jq5sDg/O/HrDXt\r\nYAy62ZjYwgWa69byd5UF4xpeElr3GsTp0PVm53uF02Y3omQTEM9l0LoeDGRh\r\ns1OFCSEbQSFWy/K2T48W74l6z9LSIWoyhqw=\r\n=lxB5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "11.0.0": {"name": "make-fetch-happen", "version": "11.0.0", "dependencies": {"ssri": "^9.0.0", "cacache": "^17.0.0", "minipass": "^3.1.6", "is-lambda": "^1.0.1", "lru-cache": "^7.7.1", "negotiator": "^0.6.3", "promise-retry": "^2.0.1", "agentkeepalive": "^4.2.1", "minipass-fetch": "^2.0.3", "minipass-flush": "^1.0.5", "http-proxy-agent": "^5.0.0", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.4", "socks-proxy-agent": "^7.0.0", "http-cache-semantics": "^4.1.0"}, "devDependencies": {"tap": "^16.0.0", "nock": "^13.2.4", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "safe-buffer": "^5.2.1", "standard-version": "^9.3.2", "@npmcli/template-oss": "4.5.1", "@npmcli/eslint-config": "^3.1.0"}, "dist": {"shasum": "192e29e3be8290db9cc49cebc8b8bd1af9228384", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-11.0.0.tgz", "fileCount": 15, "integrity": "sha512-oVk/+BQuW+fUB/RjF8jUtgsVI9wMzcHyahdea6gcshVLWQygW4OlxqEibCEh9XamMVbrXVOAH9dCZrzdLQ1lwg==", "signatures": [{"sig": "MEUCIBxM41XY0ZwZjCxvqPbv/s0NOSecXni2qYuMbAgJqvKoAiEAkWruqFr85YNr43FqO+kkbI3Makhs8yE8NzEBS/r/NAg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59347, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSHGHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqUQw//f2EYiRjTiHaCzPihqTXxzekGQKMEo+IyOEc/f1OiJYOr8G8G\r\nMg9L9elh0JMXMO6Ti8R/JrsJK34aMLoK66c8u/N7zo8e1IEufXNG66Uvi5ds\r\nYreYv+8jNXEf4mM35dL4dPG7CCwKBBU7XsC9SGAwqrwm/wnvoNr4TCoi+Ndd\r\np+ivgxAPJAZRoC+6zRGpkiwxaG+iZLGJKh7LPT7MIgbfNoLghHmipY/3NS+I\r\n5mNuaM71wsKYeXjUqzOq3GeuFM4VHNL4rB2qYR9qnOqG3feCR8ffq7gssA9H\r\nSEjloCe7BajLO/g62CbAgXzq9lT3grIQ94EkcYOwNi0ePDH8RMta3OdjF+hx\r\nhaCKH/zwZubZCiOFVw7rj8RCmkkfxtNA875DHxzCXP8hpNSzianQlTK0i83v\r\n8zJvTcvRVlOYQtYs7EPW5bUn2aJgxhsGZFQNQ4O29tUFFzhcYWh2jWjeEQ8g\r\nhcg/5nBGr62Z5Ec+8Vvzzymj31j7d3XYFcLJXfbOtZUeiSnHwpfh1W0ytN0p\r\nJaso13NE+iNzSEmveUi+YSj711dPvbteYQ5y/gZRovsn2S0RkdHpW9jG8pO3\r\nI0/4NhV6FF09m1qF7ksCmqp+7XwwBg0218AfgOPVtvyrDR6LU3YOl5VrUhJX\r\nmZxsSHcAMg7L99Kotdiz/Rtpz4hO5k37hGQ=\r\n=juPb\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "11.0.1": {"name": "make-fetch-happen", "version": "11.0.1", "dependencies": {"ssri": "^10.0.0", "cacache": "^17.0.0", "minipass": "^3.1.6", "is-lambda": "^1.0.1", "lru-cache": "^7.7.1", "negotiator": "^0.6.3", "promise-retry": "^2.0.1", "agentkeepalive": "^4.2.1", "minipass-fetch": "^3.0.0", "minipass-flush": "^1.0.5", "http-proxy-agent": "^5.0.0", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.4", "socks-proxy-agent": "^7.0.0", "http-cache-semantics": "^4.1.0"}, "devDependencies": {"tap": "^16.0.0", "nock": "^13.2.4", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "safe-buffer": "^5.2.1", "standard-version": "^9.3.2", "@npmcli/template-oss": "4.5.1", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "b3c51663d018d9e11d57fdd4393a4c5a1a7d56eb", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-11.0.1.tgz", "fileCount": 15, "integrity": "sha512-clv3IblugXn2CDUmqFhNzii3rjKa46u5wNeivc+QlLXkGI5FjLX3rGboo+y2kwf1pd8W0iDiC384cemeDtw9kw==", "signatures": [{"sig": "MEQCIGTJydBbLYc0gOFuwQ0sckGEXtirQ/AGTBY7qk92edF1AiAZj53dC05Ac46cqgYK6YBq4joS3upsgDXLCJlQ7FC9Vg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59348, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTa6AACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoWSg/+OgzBNEDnRP6EKAxDjCG7UGF1pcZxhUWgSJYbNfwp+BGS+939\r\nUM6+f1au68YK7OU1m7OHbRwi3EExK7FhpnWvAN7wq9EsQqNC2zezu5S0pqux\r\nEaV/ZnjmwjNWztXVp+I/pBhVZ1yF2Kgzh01UwUwfeYi7qriQiQOLyuP86nKs\r\nQCNBm5ala2TmIKGYIhR+WqBTdRLYzb790XuIFllxNilRK0bspB255wbzGa7t\r\nQ03127NMwr0dwwZv85cegytMAo3DI98vwl13giFo+vagEy4UIg71p/cj8TQg\r\n8tn7dM8i9yMkd8YRoxv/qTGwDZJ22R0K1W50IfbiZFINX6mk8iK3CZCESi1O\r\n7MmkIKa8Ydf8ZxAIQECpYMDZ/BXUjOQBZcDSUXwwOOEgmS6Q/9pX7CPB57Zf\r\n8tBzrCL3zoqCEoVKdUtgx95TOn7SK8zSjzeo9l4S/ai6QlXr0s+ZwEf9qmv5\r\nrqrq2AdeBZ1/3N74K81C+36de8yu8OYexxHW7PTgOoLYAaY9Oec00G595ae8\r\nR2+dqz2urBt65+lsKWY5PE4cwAye+cHpDPUP+cZSCJQTed0ufwy2YVlTf4Rl\r\nV4CotgJxyOJNS4lZrZZKspl3kyWWDdcJpWk5VLQrVtzgYsPH46ZtfAT4gV3B\r\nqHkyCF2wklIDT2HjtP/vYMMINuEqjw+bvTI=\r\n=GOEz\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "11.0.2": {"name": "make-fetch-happen", "version": "11.0.2", "dependencies": {"ssri": "^10.0.0", "cacache": "^17.0.0", "minipass": "^4.0.0", "is-lambda": "^1.0.1", "lru-cache": "^7.7.1", "negotiator": "^0.6.3", "promise-retry": "^2.0.1", "agentkeepalive": "^4.2.1", "minipass-fetch": "^3.0.0", "minipass-flush": "^1.0.5", "http-proxy-agent": "^5.0.0", "minipass-collect": "^1.0.2", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.4", "socks-proxy-agent": "^7.0.0", "http-cache-semantics": "^4.1.0"}, "devDependencies": {"tap": "^16.0.0", "nock": "^13.2.4", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "safe-buffer": "^5.2.1", "standard-version": "^9.3.2", "@npmcli/template-oss": "4.10.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "a880370fb2452d528a5ca40b2d6308999773ab17", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-11.0.2.tgz", "fileCount": 15, "integrity": "sha512-5n/Pq41w/uZghpdlXAY5kIM85RgJThtTH/NYBRAZ9VUOBWV90USaQjwGrw76fZP3Lj5hl/VZjpVvOaRBMoL/2w==", "signatures": [{"sig": "MEQCIFSD/TgrZgFfsGccJKvU0hI9wbVPw9wBoNR0GyTcx/TvAiBlYQXBDuyiHkRDVylGyw02Asur0gXbILLw4UQnvocwVQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59350, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkPhnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqgdA//egw7n0ooH8jDYuqPnTTnu12NRCmGI7DygPVE+7u/dMBQROiv\r\nYx8pmG/F1Nh9wR1IngHgoCplrLj8j0ymSJtnanSxEnbtaJcTwZGxb4DcM+hV\r\nknhwuMg+Qjc5h5OYzj604P8QxXErn3iXpc8UbO2sagT0KTvBO4TozA+goURE\r\nUmbuazId7f0rnVu2/JeyC5fF/3wZa6sAIjegXS5kCuR4J124Pg2XGkUatm1l\r\nvtKNwLDmBPDM/60Z7xZBiHlsWEoXGPlllu5vr4K8Wtv3lBFzXx/dsOJoYQa8\r\nS9Epye8QJgAZq1Nyt7fHISEKTea0JXt/A2lg5HsMSHnvZUHaaV8Dvm77nVDw\r\n6yIEre/MnzEamAW/yrCBatj3jsM1XOrjtsbvCd6AIVOk2LhzCpqY0jEGX+Y9\r\nByygcSp2Md7SIo8sFFeKliQDdvn1RQRnWihlCazXP55tLomk3oFnLb3JoPT4\r\nQ6jBVbbpHQuFj/G3NNynExksCEF9/0IZrF+1iuZaOFmi6fenNgtPEYIj2ghv\r\nSj5LCFPS+TpcDQE2Qd2Ep/Hq6OWz+9ZqdgOyhx0RUrxwQgSYmo0FJd4hmLrt\r\nIh+hyj9ahAgVw8t1hzaIXij2+PQFuiy2o4SBf0hlyu8P5NvIkCELIxV6ILXu\r\n7QAB9IrbY6brDnZR83FXGpyCUJ9LwQYY+xs=\r\n=Epsn\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "11.0.3": {"name": "make-fetch-happen", "version": "11.0.3", "dependencies": {"ssri": "^10.0.0", "cacache": "^17.0.0", "minipass": "^4.0.0", "is-lambda": "^1.0.1", "lru-cache": "^7.7.1", "negotiator": "^0.6.3", "promise-retry": "^2.0.1", "agentkeepalive": "^4.2.1", "minipass-fetch": "^3.0.0", "minipass-flush": "^1.0.5", "http-proxy-agent": "^5.0.0", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.4", "socks-proxy-agent": "^7.0.0", "http-cache-semantics": "^4.1.1"}, "devDependencies": {"tap": "^16.0.0", "nock": "^13.2.4", "safe-buffer": "^5.2.1", "standard-version": "^9.3.2", "@npmcli/template-oss": "4.11.3", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "ed83dd3685b97f75607156d2721848f6eca561b9", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-11.0.3.tgz", "fileCount": 15, "integrity": "sha512-oPLh5m10lRNNZDjJ2kP8UpboUx2uFXVaVweVe/lWut4iHWcQEmfqSVJt2ihZsFI8HbpwyyocaXbCAWf0g1ukIA==", "signatures": [{"sig": "MEUCIFzfVIlK6Glr+LL6fh2IDbmpcLS1p5jJwhAG7j+GejL5AiEA4RYx/ux4srXy/Kw/opMqqN2QhLiMrr+ykh+e8GGhzGs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59268, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj2+bpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpz4g/9HH8TJ7XwIBcl2wclViXpHDU0MDwAyba4qQ8jS5oQULaN121R\r\nWq7VBxUP8l4gs6qFEnt+aJat2EOqJ2xLRylUpreW5K1AdiJ+VqM2KjQuUm4w\r\nS/O7vSsNN3j1m+n8njJJl+IRW57S9EBOJRR1by2zVQ9X03zxL61iSh++WP9O\r\n6slWaNUh/sAY0WQK0LgSNu6JRWL0QNhbf4nC1B5NWFu6SkTTZCvtJpdEo/5A\r\ntqtzDCyx0teDLCq/+E03QZK1qJNAiBEO+GoEp39yAlXauqo3Unriff0tJ2vC\r\nmLL0ROpmEinALy3Ksp1EhW7/VABwuLj8IGQP3Uj5ZVMa8nVe8eyVTQRerBDx\r\n3KVqk8wqMBJqHRApBOv3niFFb7cvHsKAd3RqLf8h5NZ3E/0s6ng0BZQHPwSG\r\nsn3hv/wE02YkWN50sYEGknYugJkG2pciM+gcAq1Tvq36sio/9GpGrvKvCSer\r\nGLkRiaUXEJ3pxL6GEtQ/K189CGolAAc/JnXeeNKfQ0eo24t9QqhBm3nOAgdz\r\nylcgnV1sLbh1kT4suzbOCSCeMW7gfyudPzBM8fP1gjQbznhQOHNthzjCVbBA\r\n3CmdhogCvG0dsJ7DsfWWVIe5CJs2FZHfD3V1+IrhOg3Bxi8AzqYpkokxaoEu\r\njqcNPpdoL4DT+bzbTvbYUdxZ68mVRK6mt+0=\r\n=MfiR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "11.1.0": {"name": "make-fetch-happen", "version": "11.1.0", "dependencies": {"ssri": "^10.0.0", "cacache": "^17.0.0", "minipass": "^4.0.0", "is-lambda": "^1.0.1", "lru-cache": "^7.7.1", "negotiator": "^0.6.3", "promise-retry": "^2.0.1", "agentkeepalive": "^4.2.1", "minipass-fetch": "^3.0.0", "minipass-flush": "^1.0.5", "http-proxy-agent": "^5.0.0", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.4", "socks-proxy-agent": "^7.0.0", "http-cache-semantics": "^4.1.1"}, "devDependencies": {"tap": "^16.0.0", "nock": "^13.2.4", "safe-buffer": "^5.2.1", "standard-version": "^9.3.2", "@npmcli/template-oss": "4.13.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "f26b05e89317e960b75fd5e080e40d40f8d7b2a5", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-11.1.0.tgz", "fileCount": 15, "integrity": "sha512-7ChuOzCb1LzdQZrTy0ky6RsCoMYeM+Fh4cY0+4zsJVhNcH5Q3OJojLY1mGkD0xAhWB29lskECVb6ZopofwjldA==", "signatures": [{"sig": "MEYCIQDsGMqeTZm9cwOb37Kl56aYiHJGI6ttCxG0tWF4bjktbQIhAIkJuz2+o63HXoQrp7RK9StcnFsNkNcSpp4EPlOggsQs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61193, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkOHkuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrZPA//W1MGdQOqtmPjOb2/TntzGpmySEIlay2VHEvTXvfxFv98bm7e\r\nU6z5X6ydrUlI3bC224eC2dMQXyHVCU3aRjOfgH91U6hTA/HZ/iZlTy96AlUd\r\nBNKVtX8moUVV/DMYZ2g6zaonF/KmgMTyDJ8LKkhApKXiFi4IdvJCzortjHSf\r\nB8EE/beDd9byNKYQTKk0ptmOWoiH6atMFWIQsP3Nan08Hu4bZoOhg9PkbVOk\r\n0IIxkjzT+NayNWuukmE5j99MClnCYm4/vu6TS/ZipByKU+voC9qEV9/fAp7V\r\n/kjChTuN44XH9oqs1VpD90UtDTlAiLK91NEzrEOWQSpL5w1WIIg8DKEAjF3m\r\nnZOV5Yinc++IvFpSRccqmHhup0sSM4k9hwJMXx9aYEX4dPvltRtniHC2ajqd\r\n4gHA2aHOJJRzEdZpOiEFVERvbqpofx1nTOE0CBxH4mqZtu7FajgLesHArAPV\r\npO/mLQBGg84iMzJQpZK1nhBzqvi5yg6m6qlht+Yyc+oLx+IGr/XFlWq3XEIn\r\nZVszfKo+0xudp+ItfeCnbue73W0ODSm51H2JQp1j3Rb14YZ0rrOgae5gYh1f\r\nYpYnG13L+Vdf/nVi15iUPzeKpTOBJHiYP9uY5Eqs09ZIxH284GgUN42J5znL\r\nuln8TixQrLa5tsk3NrPMRDqf2hdyo3Bmr+A=\r\n=KK1c\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "11.1.1": {"name": "make-fetch-happen", "version": "11.1.1", "dependencies": {"ssri": "^10.0.0", "cacache": "^17.0.0", "minipass": "^5.0.0", "is-lambda": "^1.0.1", "lru-cache": "^7.7.1", "negotiator": "^0.6.3", "promise-retry": "^2.0.1", "agentkeepalive": "^4.2.1", "minipass-fetch": "^3.0.0", "minipass-flush": "^1.0.5", "http-proxy-agent": "^5.0.0", "https-proxy-agent": "^5.0.0", "minipass-pipeline": "^1.2.4", "socks-proxy-agent": "^7.0.0", "http-cache-semantics": "^4.1.1"}, "devDependencies": {"tap": "^16.0.0", "nock": "^13.2.4", "safe-buffer": "^5.2.1", "standard-version": "^9.3.2", "@npmcli/template-oss": "4.14.1", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "85ceb98079584a9523d4bf71d32996e7e208549f", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-11.1.1.tgz", "fileCount": 15, "integrity": "sha512-rLWS7GCSTcEujjVBs2YqG7Y4643u8ucvCJeSRqiLYhesrDuzeuFIk37xREzAsfQaqzl8b9rNCE4m6J8tvX4Q8w==", "signatures": [{"sig": "MEUCIQCHNNciYGJOXNR/IKr/Dmh7JQjrwC2ff3XyVFdL5hQxaAIgKmHDnacOTGYT4VgoSAFiGvxS57RZGepvzmrTvEh5TiM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/make-fetch-happen@11.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 61201, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkStYuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqS5g/+Lrg/Z0LfFE5Lpz6nUKPZ5Cx6XVCKHl/iHhfhWi6wBfX6xMc/\r\nGkLNO+9/fZhEpC/IZT/nhAKSlFV88CsP+R3uLa0aEhPYWgP93lFEJQyO7T/P\r\nIww4XZRURvc7Y9l4oIi9dlEBdrG9psjVxSS+WfDGoGhkXfmbAKDpoMDPJz2I\r\n0yiDC95H45Oga13erBsmi3nHliL+YlslqyLku65rd5pE2YmwrW6r6OrnO7eM\r\nrjyF90JDW6zquA+BHz/d/CXd4DdcnyNg6Cbiq7FjSylQZVBJetFDyzIvQKzS\r\nHBI96pT4zvuhvZ00FueGOCsjMOOM2k7m1bKeJOoxVYfkk8ULD05j5vsVDpaT\r\n1gpGu7SGBA+c3UWhOqtAQRl/7Mey18YzBu/CNBd4Y9jaDDGqo4UoAPP/jJD/\r\ne9GoC2jw9Mznss7a5MQM2CqnIr0ORJFz/cO0AN/+XPm4PLj/2JVlZ/wPkwp+\r\n4N5JKY2HlSIiUHQMMw7NuDKhSx+j+Bk5e5Y0YRYNCSwQhMwF6IBvZ2gwuvYq\r\nIe84SomQVjA3jct23wLctwlnO6a9aEXvn7UKjqeKc+GBcgMFSFbS+QWnZf3k\r\nLqkN5llkjQfJFEVbgXq3PA4xo+HX9+/xOv5qhjUsIraAjOP2bxr5B/ybTg2Z\r\nIKlhPgE2MJLEIKPdBdq06V8ZXUlYTppHKo0=\r\n=agH2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "12.0.0": {"name": "make-fetch-happen", "version": "12.0.0", "dependencies": {"ssri": "^10.0.0", "cacache": "^17.0.0", "minipass": "^7.0.2", "is-lambda": "^1.0.1", "negotiator": "^0.6.3", "@npmcli/agent": "^1.1.0", "promise-retry": "^2.0.1", "minipass-fetch": "^3.0.0", "minipass-flush": "^1.0.5", "minipass-pipeline": "^1.2.4", "http-cache-semantics": "^4.1.1"}, "devDependencies": {"tap": "^16.0.0", "nock": "^13.2.4", "safe-buffer": "^5.2.1", "standard-version": "^9.3.2", "@npmcli/template-oss": "4.18.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "788e783444ac988a8145481cab3621bfa7d9d9ea", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-12.0.0.tgz", "fileCount": 13, "integrity": "sha512-xpuA2kA8Z66uGQjaSXd7rffqJOv60iYpP8X0TsZl3uwXlqxUVmHETImjM71JOPA694TlcX37GhlaCsl6z6fNVg==", "signatures": [{"sig": "MEUCIQDAzEOXbs6BEXaxWM3X0gi34NjiWkRcjeHpbq2i0GyELQIgRN0OuCWaoQDbYy78XOKYoY6RdHrfe8JfQS7sefYCv08=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/make-fetch-happen@12.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 52486}, "engines": {"node": "^16.13.0 || >=18.0.0"}}, "13.0.0": {"name": "make-fetch-happen", "version": "13.0.0", "dependencies": {"ssri": "^10.0.0", "cacache": "^18.0.0", "minipass": "^7.0.2", "is-lambda": "^1.0.1", "negotiator": "^0.6.3", "@npmcli/agent": "^2.0.0", "promise-retry": "^2.0.1", "minipass-fetch": "^3.0.0", "minipass-flush": "^1.0.5", "minipass-pipeline": "^1.2.4", "http-cache-semantics": "^4.1.1"}, "devDependencies": {"tap": "^16.0.0", "nock": "^13.2.4", "safe-buffer": "^5.2.1", "standard-version": "^9.3.2", "@npmcli/template-oss": "4.18.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "705d6f6cbd7faecb8eac2432f551e49475bfedf0", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-13.0.0.tgz", "fileCount": 13, "integrity": "sha512-7ThobcL8brtGo9CavByQrQi+23aIfgYU++wg4B87AIS8Rb2ZBt/MEaDqzA00Xwv/jUjAjYkLHjVolYuTLKda2A==", "signatures": [{"sig": "MEQCIEhlkCHnHIH+Fjbwu7HbI/a+dFLNU6NqIGxQEB2ZGGreAiAk3UfoSEX0F942cXR5kYe7ngBqzSZ30EhjVZ9hbwLSkA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/make-fetch-happen@13.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 52486}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "13.0.1": {"name": "make-fetch-happen", "version": "13.0.1", "dependencies": {"ssri": "^10.0.0", "cacache": "^18.0.0", "minipass": "^7.0.2", "proc-log": "^4.2.0", "is-lambda": "^1.0.1", "negotiator": "^0.6.3", "@npmcli/agent": "^2.0.0", "promise-retry": "^2.0.1", "minipass-fetch": "^3.0.0", "minipass-flush": "^1.0.5", "minipass-pipeline": "^1.2.4", "http-cache-semantics": "^4.1.1"}, "devDependencies": {"tap": "^16.0.0", "nock": "^13.2.4", "safe-buffer": "^5.2.1", "standard-version": "^9.3.2", "@npmcli/template-oss": "4.21.4", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "273ba2f78f45e1f3a6dca91cede87d9fa4821e36", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-13.0.1.tgz", "fileCount": 13, "integrity": "sha512-cKTUFc/rbKUd/9meOvgrpJ2WrNzymt6jfRDdwg5UCnVzv9dTpEj9JS5m3wtziXVCjluIXyL8pcaukYqezIzZQA==", "signatures": [{"sig": "MEQCIA7a7b2hrtmuR9wWgjUVT2P4QyG21Hl7HvygsnEsFXPmAiBMMHCNEuyCw6YnMFh0lhvBpgQ/f2jefYql9X40r/Wh+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/make-fetch-happen@13.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 52787}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "14.0.0": {"name": "make-fetch-happen", "version": "14.0.0", "dependencies": {"ssri": "^12.0.0", "cacache": "^18.0.0", "minipass": "^7.0.2", "proc-log": "^5.0.0", "negotiator": "^0.6.3", "@npmcli/agent": "^3.0.0", "promise-retry": "^2.0.1", "minipass-fetch": "^4.0.0", "minipass-flush": "^1.0.5", "minipass-pipeline": "^1.2.4", "http-cache-semantics": "^4.1.1"}, "devDependencies": {"tap": "^16.0.0", "nock": "^13.2.4", "safe-buffer": "^5.2.1", "standard-version": "^9.3.2", "@npmcli/template-oss": "4.23.3", "@npmcli/eslint-config": "^5.0.0"}, "dist": {"shasum": "f8e163711991ea6994e65120cb6f9a969907cf25", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-14.0.0.tgz", "fileCount": 13, "integrity": "sha512-/0z4kjybWroAwWuQJJgkA5yUKw/WGepJhjPdj6YW8+pn7lWs75zA9khVgvID/OMYAwRjq2MsLexKxttjIcfD4w==", "signatures": [{"sig": "MEQCIHpj62wpfxywsxgIA+kMEwDMF4hOKcTtgeZVdOi+pWKDAiAtQLGkA/g60XnzQzUFuyqin/Q8mUi4dWaaGzcCtggY7g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/make-fetch-happen@14.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 52774}, "engines": {"node": "^18.17.0 || >=20.5.0"}}, "14.0.1": {"name": "make-fetch-happen", "version": "14.0.1", "dependencies": {"ssri": "^12.0.0", "cacache": "^19.0.1", "minipass": "^7.0.2", "proc-log": "^5.0.0", "negotiator": "^0.6.3", "@npmcli/agent": "^3.0.0", "promise-retry": "^2.0.1", "minipass-fetch": "^4.0.0", "minipass-flush": "^1.0.5", "minipass-pipeline": "^1.2.4", "http-cache-semantics": "^4.1.1"}, "devDependencies": {"tap": "^16.0.0", "nock": "^13.2.4", "safe-buffer": "^5.2.1", "standard-version": "^9.3.2", "@npmcli/template-oss": "4.23.3", "@npmcli/eslint-config": "^5.0.0"}, "dist": {"shasum": "5b2d4e1f6d89d2cc2d4bb97995e250ed9dadba1b", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-14.0.1.tgz", "fileCount": 13, "integrity": "sha512-Z1ndm71UQdcK362F5Wg4IFRBZq4MGeCz+uor5iPROkSjEWEoc1Zn7OSKPvmg01S9XOI8mr+GlRr+W4ABz4ZgdA==", "signatures": [{"sig": "MEUCIQCgI803msaBJ9iLfxb39RP/3jzddKTSjeD3PQeLd991aQIgT15hjzpG+oIYNfJ0EXZ5ajNdxAPz66xFFhm9D622Y3c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/make-fetch-happen@14.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 52774}, "engines": {"node": "^18.17.0 || >=20.5.0"}}, "14.0.2": {"name": "make-fetch-happen", "version": "14.0.2", "dependencies": {"ssri": "^12.0.0", "cacache": "^19.0.1", "minipass": "^7.0.2", "proc-log": "^5.0.0", "negotiator": "^0.6.3", "@npmcli/agent": "^3.0.0", "promise-retry": "^2.0.1", "minipass-fetch": "^4.0.0", "minipass-flush": "^1.0.5", "minipass-pipeline": "^1.2.4", "http-cache-semantics": "^4.1.1"}, "devDependencies": {"tap": "^16.0.0", "nock": "^13.2.4", "safe-buffer": "^5.2.1", "standard-version": "^9.3.2", "@npmcli/template-oss": "4.23.4", "@npmcli/eslint-config": "^5.0.0"}, "dist": {"shasum": "c7c79fa7e6f97856cb883fe96f01e02639ed425f", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-14.0.2.tgz", "fileCount": 13, "integrity": "sha512-ByhSXJdWoBKRHKaqfmULjm4RwlL3EN9bZogHPDIuT9GHJIqHgh8FYkxpcCMsvaNTwl2VzFFYgzrGMlGsOEtGPQ==", "signatures": [{"sig": "MEYCIQD7nA3LTYKmTaj7YzfecVpktol3M+ko75K8l3I9LlRG1QIhANPEs/fz3QhMyhI9tVx0ML8SOMMoJ2NZKbnrepSAfsTD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/make-fetch-happen@14.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 52999}, "engines": {"node": "^18.17.0 || >=20.5.0"}}, "14.0.3": {"name": "make-fetch-happen", "version": "14.0.3", "dependencies": {"ssri": "^12.0.0", "cacache": "^19.0.1", "minipass": "^7.0.2", "proc-log": "^5.0.0", "negotiator": "^1.0.0", "@npmcli/agent": "^3.0.0", "promise-retry": "^2.0.1", "minipass-fetch": "^4.0.0", "minipass-flush": "^1.0.5", "minipass-pipeline": "^1.2.4", "http-cache-semantics": "^4.1.1"}, "devDependencies": {"tap": "^16.0.0", "nock": "^13.2.4", "safe-buffer": "^5.2.1", "standard-version": "^9.3.2", "@npmcli/template-oss": "4.23.4", "@npmcli/eslint-config": "^5.0.0"}, "dist": {"shasum": "d74c3ecb0028f08ab604011e0bc6baed483fcdcd", "tarball": "https://registry.npmjs.org/make-fetch-happen/-/make-fetch-happen-14.0.3.tgz", "fileCount": 13, "integrity": "sha512-QMjGbFTP0blj97EeidG5hk/QhKQ3T4ICckQGLgz38QF7Vgbk6e6FTARN8KhKxyBbWn8R0HU+bnw8aSoFPD4qtQ==", "signatures": [{"sig": "MEUCIQDp26zMrE6d6rpcSl6dDSLmAmn9A/TUzbxJ5/6OHAkWVgIgB3TWGJDx7aFn7VWSMPuZaYMtSH3Zl2mjDIRcBwpVN3M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/make-fetch-happen@14.0.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 53154}, "engines": {"node": "^18.17.0 || >=20.5.0"}}}, "modified": "2025-05-14T20:03:51.982Z"}