{"_id": "undici-types", "_rev": "68-4506b11f80203746ce3a6720d7e8d558", "name": "undici-types", "dist-tags": {"test": "5.24.0-test.6", "latest": "7.2.3"}, "versions": {"0.0.1": {"name": "undici-types", "version": "0.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "undici-types@0.0.1", "maintainers": [{"name": "ethan_arrowood", "email": "<EMAIL>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "2dde5a7111c75a79446548d943fe4fd64adb6c99", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-0.0.1.tgz", "fileCount": 36, "integrity": "sha512-coq2VfeV80wvuLUDdbpcg4vBJQiWbPSSoJk2ra6a6BvehnZaqwh3M9CubAJRfCMujX4uM8JTVPXTYaCihJuOSw==", "signatures": [{"sig": "MEYCIQCXiMT8UhahBI/MeKUYpli//nUE0ip0Xam2p7CZWrFljwIhANmuJCbHZiKzJYiduLW9eyaAQXEJRdYHz/K0PLzXqlx1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89683}, "types": "index.d.ts", "gitHead": "4d7c319d67a73f7284216ac38e81be4f94f70cde", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "16.20.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_0.0.1_1695136175361_0.8015107674328446", "host": "s3://npm-registry-packages"}}, "5.24.0-test.2": {"name": "undici-types", "version": "5.24.0-test.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "undici-types@5.24.0-test.2", "maintainers": [{"name": "ethan_arrowood", "email": "<EMAIL>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "1aee4918d6405d486eddfafb402230ea8993a35a", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-5.24.0-test.2.tgz", "fileCount": 36, "integrity": "sha512-zJ7TI5zCiq/xCmtpsJMOTbzFzH6gkftm/5VKP3V4UHvjPlIxTgnajPGFJ0N2l4oOY+nKFL5LPJgom4Uw2ZX5pw==", "signatures": [{"sig": "MEYCIQDOjKU5u8s9X2kwIikeDGcE30bjwvTI+pXcBD3d5DAa/gIhAJhixME1q97PyfuKrbkegiF7BYQ1c1tjEjwa/fGp8Da0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89690}, "types": "index.d.ts", "gitHead": "9c3e7d7ef367ebde507a3111a4bef99d8571cd46", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "16.20.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/undici-types_5.24.0-test.2_1695143978031_0.09775288326284604", "host": "s3://npm-registry-packages"}}, "5.24.0-test.5": {"name": "undici-types", "version": "5.24.0-test.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "undici-types@5.24.0-test.5", "maintainers": [{"name": "ethan_arrowood", "email": "<EMAIL>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "ce780c35343772a64b6aad4ddccc35d1fcbaa440", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-5.24.0-test.5.tgz", "fileCount": 1, "integrity": "sha512-xKqn8NTd9tEzIOgDhiyXaP3G82+pKKABLc6XtvgsReYZVMZ8OnP0DQRNpmgWfU1gDrTQjlQWKONZljgRwq7P3A==", "signatures": [{"sig": "MEUCIQDQooopw0iOlnCB7+nHCWIKSo1XbtgfPWxotaVP77EsDwIgdnc6o8OpEjzEJWA3ruquabF7RjsRJxIAeLATvpRJt7I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 475}, "types": "index.d.ts", "gitHead": "2ac9c5cc5bc4117bd288d82983fe3ff3a735cf03", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "16.20.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_5.24.0-test.5_1695145293875_0.1539863504520682", "host": "s3://npm-registry-packages"}}, "5.24.0-test.6": {"name": "undici-types", "version": "5.24.0-test.6", "license": "MIT", "_id": "undici-types@5.24.0-test.6", "maintainers": [{"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "480b4f87c6a68b5a445a5f90db9f389d9c56c2f9", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-5.24.0-test.6.tgz", "fileCount": 34, "integrity": "sha512-M9MV3OjD6YuaF4kLb1uR+66dlE/U3nvv4oDU3ia2YIusjsxoVmjP6Ra0+u7hQmXpXMeNPDzt3SKSk8pag4XMkw==", "signatures": [{"sig": "MEQCIF1HnWWYKM99wLoVk36IRbsa8cIcur/gndQ+r3P7ywJIAiBubNjSvbCjGi9gD8UH/nHS6g5y/IDE6B7ebOnfWkl0bQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72231}, "types": "index.d.ts", "gitHead": "271f1d9b7c4b03c223a22bf2ea1b6f2a0cdf4cb6", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "16.20.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_5.24.0-test.6_1695242965290_0.7724505524131717", "host": "s3://npm-registry-packages"}}, "5.25.1": {"name": "undici-types", "version": "5.25.1", "license": "MIT", "_id": "undici-types@5.25.1", "maintainers": [{"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "4d783e20541212a2af9659f476c31d920626ac0e", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-5.25.1.tgz", "fileCount": 35, "integrity": "sha512-k98JkedWuLf5oOAKuzRH+hZviKnEavAW5FL5Ls3Vys19rSittLWcxGiHAkAVoM29t588sLs0ZwK1BqUUIZLLAw==", "signatures": [{"sig": "MEQCIHcmjQkXsbvQW2XwRnkvFEBJCHrbN0S0xA9vnayi5Eh9AiAGCHyIZkeVd5zxWJ6inKSHEQ2qHIIvZ3UMy0C/hhPbvA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72679}, "types": "index.d.ts", "gitHead": "c86279c9bcf62fe28d124b124b91eb364d478a25", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "16.20.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_5.25.1_1695243829363_0.3590322704548976", "host": "s3://npm-registry-packages"}}, "5.25.2": {"name": "undici-types", "version": "5.25.2", "license": "MIT", "_id": "undici-types@5.25.2", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "5e959fa2abf90a0a5caa02a73b63ce40c84d3256", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-5.25.2.tgz", "fileCount": 35, "integrity": "sha512-Q88IZXwVbathaQqOdzAvpGRdRtXbu4pzgcxTMmp/u7EbbLWmoLmGv0uQybm82A1weU5/gSYeIn4mC6ztlgsLfg==", "signatures": [{"sig": "MEYCIQDhxqdLnH9N5yf8i62j5udGNeO8kf6iUaxhRlaPKUgCsgIhAOnWTfjytEdBe4h7eDLPzCdsvILdYZkVIJzhTExCk+3b", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72680}, "types": "index.d.ts", "gitHead": "4013c4b8932e73728809e4106d5c9d9d40648031", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "16.20.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_5.25.2_1695673013262_0.2355764246047658", "host": "s3://npm-registry-packages"}}, "5.25.3": {"name": "undici-types", "version": "5.25.3", "license": "MIT", "_id": "undici-types@5.25.3", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "e044115914c85f0bcbb229f346ab739f064998c3", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-5.25.3.tgz", "fileCount": 35, "integrity": "sha512-Ga1jfYwRn7+cP9v8auvEXN1rX3sWqlayd4HP7OKk4mZWylEmu3KzXDUGrQUN6Ol7qo1gPvB2e5gX6udnyEPgdA==", "signatures": [{"sig": "MEUCIHOX6VrKn1H0TB+ZDD60CSBWnjAKxxjn/xeSXr3wmprkAiEAkhMpcEM9HaDkMbrY6n2vEQaYVCIWcBzpCpT5Uv7dVHc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72679}, "types": "index.d.ts", "gitHead": "96f1425ca7ab7b6f0578fe0ff9badbc16ff11ff6", "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_5.25.3_1696171998049_0.2455461148389746", "host": "s3://npm-registry-packages"}}, "5.26.0": {"name": "undici-types", "version": "5.26.0", "license": "MIT", "_id": "undici-types@5.26.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "b51f55526c5147ff733109f7bce1b9ddc47a6880", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-5.26.0.tgz", "fileCount": 35, "integrity": "sha512-NuS953vC338lTCrOAAyqkPOhbJTnkoZqbZ1h9fNVEr8asP1HJEJW0DkqofDk0bYFNMOQ+1/Gqx03uR8446bBnQ==", "signatures": [{"sig": "MEUCIQCpkBQGBcDcpfYnNultbVa+JKN2JGpmF5L3eNrM+jsadQIgZAEx9hMJ99wyeG1biuyiA1FvkZJmYPUmRHMHarFt07c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72748}, "types": "index.d.ts", "gitHead": "89e0c00b37f28e0ba6930273a8b45510fd851019", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "16.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_5.26.0_1697037692429_0.3458628500807821", "host": "s3://npm-registry-packages"}}, "5.26.1": {"name": "undici-types", "version": "5.26.1", "license": "MIT", "_id": "undici-types@5.26.1", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "f744acb3be2b31b1fe9463ee9d49431145cbcf0d", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-5.26.1.tgz", "fileCount": 35, "integrity": "sha512-ztSmnRYqYiYwie4TcCR3tyaNl1tEEYWDkcxEeYmBCmdXCWJ7qaD6Uc6ZzPOLHVfBWh/bl0zqstQ6oC3+4mF5zA==", "signatures": [{"sig": "MEUCIQChERezsEViJgMGEUfpmJVOocNe4AiCIQIVtG8N6s+pXwIgcWW1pqo+uIelqOwsS8uUTH1tEYtz9NW43NKF49wXEb4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72748}, "types": "index.d.ts", "gitHead": "c8c80b1115d668664d8cf3acec7535b0258c3079", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "16.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_5.26.1_1697048879293_0.2991261502806115", "host": "s3://npm-registry-packages"}}, "5.26.2": {"name": "undici-types", "version": "5.26.2", "license": "MIT", "_id": "undici-types@5.26.2", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "f8fad3fc072c885d9fa82fc19353e93592b769ef", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-5.26.2.tgz", "fileCount": 35, "integrity": "sha512-hwP+iAUYI/aE56tnK5w0DsKnsnVreg4Va8Q62BWCXjRGATmJr7+bQsVLqqqJMHTcyMMo7X/JX0qiYg+Qq1bPMg==", "signatures": [{"sig": "MEUCIGwUjGoZTOu5U2oWzQJmKCX1bJdfKhCkd70tvnFAksICAiEAqKGJYDef1I9DH3GBvHDR+dfxduBFv/xpQBrgyA6Dca8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72748}, "types": "index.d.ts", "gitHead": "12a62187d45f332cf39dd405f7c52b759cf40cdd", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "16.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_5.26.2_1697050792650_0.16197507825093083", "host": "s3://npm-registry-packages"}}, "5.26.3": {"name": "undici-types", "version": "5.26.3", "license": "MIT", "_id": "undici-types@5.26.3", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "8022b823f663300dce32cf850e247173c376b004", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-5.26.3.tgz", "fileCount": 35, "integrity": "sha512-sD45goX3K5aoPnrxgiHFK1XFuU6UpckJfoRoZ2mFrLJs1tdqX0yFtuOgHU24Y6kbPy4hM5nENcrtNyI+GZVOOQ==", "signatures": [{"sig": "MEUCICoHEDcBZlszG8JoeWLfkkgKNKjVjiexzsbRYWclP46CAiEAtFstxIMZtKwFHtuYHQzmwZbHl6EZ5vwCa1iHcOv+ZaA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72748}, "types": "index.d.ts", "gitHead": "227b9bedf233f741b86dda4ae9d1c7ad69f5d75c", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "16.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_5.26.3_1697051647031_0.22010038149479616", "host": "s3://npm-registry-packages"}}, "5.26.4": {"name": "undici-types", "version": "5.26.4", "license": "MIT", "_id": "undici-types@5.26.4", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "0b81fe7bbae26c8082ec180f6dd06154ee94d0ec", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-5.26.4.tgz", "fileCount": 35, "integrity": "sha512-nSNuWyiXbGptNQjUVhJGKA0KufCryCa6yjc2IAfg4HRblpUNgF5xhqEXNO3+F8cVYFiJsIgurydTuKphzszEmA==", "signatures": [{"sig": "MEYCIQCoC382cJ9FPHHd8MkC/2hIEWmQfchj/4iT8XpJzhFjFgIhAINp+U3sjeXp7yVG2EnyzKVbla7hsX1gRqd0adQ8ZdpB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72748}, "types": "index.d.ts", "gitHead": "dea70e27e4d14952eb7b96da021eb44d24d1159e", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "16.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_5.26.4_1697705245698_0.5186810367698944", "host": "s3://npm-registry-packages"}}, "5.26.5": {"name": "undici-types", "version": "5.26.5", "license": "MIT", "_id": "undici-types@5.26.5", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "bcd539893d00b56e964fd2657a4866b221a65617", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-5.26.5.tgz", "fileCount": 35, "integrity": "sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==", "signatures": [{"sig": "MEUCIQD/EnrCkJJ7oe1TmYqBRI4n2tewrdux8HM6ufRIs2fk2QIgHMUCkefGQiUVfEJ2YpbQKlLRqYnOVdUF+ghWEE2T3Fs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73053}, "types": "index.d.ts", "gitHead": "9197790ae0d015b40b75fd0c5cdb7420704b5272", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "16.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_5.26.5_1698046044515_0.657448767936649", "host": "s3://npm-registry-packages"}}, "5.27.0": {"name": "undici-types", "version": "5.27.0", "license": "MIT", "_id": "undici-types@5.27.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "7c43adb723d24eaf8bcac9009242e3fc7adbf5b7", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-5.27.0.tgz", "fileCount": 35, "integrity": "sha512-vBblTWiSquVYNHW8Xgnho3vhWyi1ARrAqaTXIdwpsp4CVn2wXRh/AVjBzpAo0f09Rqg98ihf+AIXGb2jGsILtg==", "signatures": [{"sig": "MEUCIFgbry0KX0ozH/T2qU6bRlorXlrYoAO+Wht9f5vK2uEZAiEAyJzHgQd2gWWv9GjHtWTzyIO4fXe1kRTncH7JyLQ14Eg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73053}, "types": "index.d.ts", "gitHead": "41c253d0c23fd1cf63b8033d8ab61c2cf13e8c6e", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "16.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_5.27.0_1698320954558_0.8574250233454885", "host": "s3://npm-registry-packages"}}, "5.27.1": {"name": "undici-types", "version": "5.27.1", "license": "MIT", "_id": "undici-types@5.27.1", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "7e6fd541f9138b01d0934c44f06c5a77ca0d4ddf", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-5.27.1.tgz", "fileCount": 35, "integrity": "sha512-DKi9rtM6gl9JVWRUVeywgWRRYW7jKr29Q8GNlia9jD6P9FDi09z6NJyM6W2D1C4ISHc0OrUJCOTZqPPoi7TzTg==", "signatures": [{"sig": "MEUCIQDmuQcV0e6OpG/Gi642D8ibnF/cRMehXXM5HEbxb/yg6QIgGwtzCAJea30IF82RsVOSPsMfLSG2MPXkKeWmBKFWQlk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73053}, "types": "index.d.ts", "gitHead": "2aedba485b539335b7ade6977615f9f94173eab2", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "16.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_5.27.1_1699030492099_0.5038985376436489", "host": "s3://npm-registry-packages"}}, "5.27.2": {"name": "undici-types", "version": "5.27.2", "license": "MIT", "_id": "undici-types@5.27.2", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "a57c2eb2cc5f98147fec92d3980ccf118ea13a0b", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-5.27.2.tgz", "fileCount": 35, "integrity": "sha512-jq2b5ZpcQl5WYBYs7F1Mnit4B1VZtT61eeHF+fr+M0By0DLua/45PNizT2gqzcZlOU1RUVhpj5MGLiTF20he5Q==", "signatures": [{"sig": "MEQCIBmFHCasIy2BYYL2as/AWAiT3bS7591xMSOtisV8XYH9AiBzUiXntD8kapqVdOejAJ6/4yq3BHlbZ1fom4KeISc/IA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73053}, "types": "index.d.ts", "gitHead": "1541173d7a728eaf88bcd87263cef2ea0d993e74", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "16.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_5.27.2_1699043837939_0.8269660928690659", "host": "s3://npm-registry-packages"}}, "5.28.0": {"name": "undici-types", "version": "5.28.0", "license": "MIT", "_id": "undici-types@5.28.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "59a3a52cb57c1c41a487c858ea7e8203248c611e", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-5.28.0.tgz", "fileCount": 37, "integrity": "sha512-boHJxuxqgtSQnp22xKSLe6iNYMk8GaCMfKALCgUsvtSC0Gj9Gf2Vaz75otJGnN8Is4ij1CHztHkwzpko/CzCJA==", "signatures": [{"sig": "MEQCIF3YhwwgkvgRxV9gy+kqrLMZBGgq+PN7+76/zRLvNAulAiBRVfaYboM03W2hCPmuUy3/ohsthzXu1XRtcGNzHcrtqw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77288}, "types": "index.d.ts", "gitHead": "66029d1b317c0cfe38543553055cc86c658d7635", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "16.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_5.28.0_1700816462731_0.9014521332174086", "host": "s3://npm-registry-packages"}}, "5.28.1": {"name": "undici-types", "version": "5.28.1", "license": "MIT", "_id": "undici-types@5.28.1", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "262b059e8b071bb747d99426d53a513261262565", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-5.28.1.tgz", "fileCount": 37, "integrity": "sha512-1C+CctX9yXPVSUFpkGPNRnBcrLxhhJtDqjJZGpuevDaoH8xXql4SXlJRqqb9ZSEah/YuGfpi0oAvY78jD3kMFg==", "signatures": [{"sig": "MEUCIQDrarkI07sx+gSNc05LG07ibi02Hq2DUC6vGyVVzr98jAIgUGQWeIGnTersO+m9pt0TOeMR9F7mvYca9IL9oZqTjPk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77288}, "types": "index.d.ts", "gitHead": "286bb4463b05e01e809737214e8eb1c161b78240", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "16.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_5.28.1_1701078568827_0.2538512334195526", "host": "s3://npm-registry-packages"}}, "5.28.2": {"name": "undici-types", "version": "5.28.2", "license": "MIT", "_id": "undici-types@5.28.2", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "6d86278547d81c2cad72425cf275e8dfbaa8bc42", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-5.28.2.tgz", "fileCount": 37, "integrity": "sha512-W71OLwDqzIO0d3k07qg1xc7d4cX8SsSwuCO4bQ4V7ITwduXXie/lcImofabP5VV+NvuvSe8ovKvHVJcizVc1JA==", "signatures": [{"sig": "MEQCIEXKvMM3RzH2zGClJFK7ettJ8fqrERhnG+ZEBaw/0IfmAiBvM2Lyo597FTIcTSOyAEew9VNQlCucEqnbIMamrCu6AQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77295}, "types": "index.d.ts", "gitHead": "9a14e5f32a118fa93e769cc15ae8de9de552f2e4", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "16.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_5.28.2_1701358945884_0.0016210716759952337", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "undici-types", "version": "6.0.0", "license": "MIT", "_id": "undici-types@6.0.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "705e359362533096ec410cfeb2b8ce1385146669", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.0.0.tgz", "fileCount": 37, "integrity": "sha512-I7ax/e3PkfMlo4ohi+jUEbjCKXHbTsHTc18EVhbzD+FtOeIW+CoHIPVu0JRWdJnD7czzE960J25NfaVv9VTOGw==", "signatures": [{"sig": "MEYCIQDrDeUurXAuzAqBBhP798TSYgxqDCTthNYr6Rs/pM3SGgIhAL0h5z7x8WLwF5hBesbtJ7f6H1AkxX6uSTqysU1NN7C3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77294}, "types": "index.d.ts", "gitHead": "e218fc61eda46da8784e0cedcaa88cd7e84dee99", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "16.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.0.0_1701766084084_0.24827363271812963", "host": "s3://npm-registry-packages"}}, "6.0.1": {"name": "undici-types", "version": "6.0.1", "license": "MIT", "_id": "undici-types@6.0.1", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "62e2af9fcd3ce359634175658de39df8d0f37197", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.0.1.tgz", "fileCount": 37, "integrity": "sha512-i9dNdkCziyqGpFxhatR9LITcInbFWh+ExlWkrZQpZHje8FfCcJKgps0IbmMd7D1o8c8syG4pIOV+aKIoC9JEyA==", "signatures": [{"sig": "MEUCIFuj/YcEeF0zmxmhGWhoyccWn6ey9owoi3IhoZcczX+lAiEA2OHTiqIDPz4CyY2jhoNRmhUUdArN860ok+s68iV+ujo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77294}, "types": "index.d.ts", "gitHead": "0c3c6f8474857497ad1d8ca3d2687a66589079d3", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "16.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.0.1_1701850976312_0.3786176052242136", "host": "s3://npm-registry-packages"}}, "6.1.0": {"name": "undici-types", "version": "6.1.0", "license": "MIT", "_id": "undici-types@6.1.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "af3ead3fefd6a003f8b9c4a68e76231b2ec02d78", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.1.0.tgz", "fileCount": 38, "integrity": "sha512-yN8XCXMkvhKbL/gXK0DVtMPRbriSdYKF+piDAbZnX9jPT+itcqRMGiOA/KtTkpVUfLgqWnD4vUpOVHE5BVhyag==", "signatures": [{"sig": "MEUCIQD316yVBCHsFaaXf49Fy9BoEkwC48N3ARBQS7GcgAGEfgIgMiokeHtZbcJdUg9UTuFDhie2d0Gbnr6IXsBIRmR23ww=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78533}, "types": "index.d.ts", "gitHead": "250b89af0ae27b93aaacbb885e852636e2c78ce6", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "16.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.1.0_1703080912101_0.8490038691267285", "host": "s3://npm-registry-packages"}}, "6.2.0": {"name": "undici-types", "version": "6.2.0", "license": "MIT", "_id": "undici-types@6.2.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "c138e2d254d12bdcb9b0285083c4f4be3d80fcc0", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.2.0.tgz", "fileCount": 38, "integrity": "sha512-IHRfqL4BhOW3lK3jpLx6Ul3BqFSeB4ykHIvamF5zUQ3GRxvcQU8uS1tXEFaRlPemCHz2tvMz4GMF1jEND51vDA==", "signatures": [{"sig": "MEUCIQCBA8smCQxrehklMhsRtdNXnkkxmlnfREzASw0Fh93lQwIgEu9Eg3O8jR96sJorKOIG0xC/LFu3zg3HVOt0n13iQ9c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78533}, "types": "index.d.ts", "gitHead": "0c4c4504852c71dac1a6eb8dfae0f2411b6f2fc6", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "16.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.2.0_1703086445823_0.9246380697761818", "host": "s3://npm-registry-packages"}}, "6.2.1": {"name": "undici-types", "version": "6.2.1", "license": "MIT", "_id": "undici-types@6.2.1", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "b67078581ef640f7420f113cf45ddb3ca4076dc3", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.2.1.tgz", "fileCount": 38, "integrity": "sha512-iD2yLpYLPK+BZwQbl5X+iDZblu3Nsc4lJgqJwxvIWhrT4IC8iNgmhhfmAm0JIeYP5mlXXlUBcpgRy212MoEjuQ==", "signatures": [{"sig": "MEYCIQDN0Q51lkN6/iNfMdrTQ1K1baC6EijZQ+ur/I8uXomUQgIhAKezzzXmiXap8p6tZFl+JUj7nvs/mknntKXIcfIEAxdb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78533}, "types": "index.d.ts", "gitHead": "f51f917061aec737edfe635e52db5bccc6fc0ac6", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "16.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.2.1_1703237841473_0.26133404526965287", "host": "s3://npm-registry-packages"}}, "6.3.0": {"name": "undici-types", "version": "6.3.0", "license": "MIT", "_id": "undici-types@6.3.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "5c2d24bf9f05af1345806111d0925fa2296149a2", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.3.0.tgz", "fileCount": 38, "integrity": "sha512-LwelOasW+A7zT4t0EmBtD2x1UGNgQvLgrT/Sqkjf45uY2d2jv7Y9vZkXNnU2hJFIYrQrSSzIrHVTlANOOy/qkg==", "signatures": [{"sig": "MEUCIHnZIS9xzC2KFK8M234BxmPAKIKkcTrTQuu/11ltsxIaAiEAkUyMhTGCcEI1eW74kHWJvNCKGNgdA0OOIqmP3A856ms=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79517}, "types": "index.d.ts", "gitHead": "887d1cb2df84abdf7c57fb74342d3a51db681652", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "16.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.3.0_1704725847188_0.10800977637101972", "host": "s3://npm-registry-packages"}}, "6.4.0": {"name": "undici-types", "version": "6.4.0", "license": "MIT", "_id": "undici-types@6.4.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "59bd2eaa6928683466202f7f0df3ffbc76aa6a1a", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.4.0.tgz", "fileCount": 38, "integrity": "sha512-hF4VKFUTypcLYImmZ13xVhjDaFhRlNULLUCOpm2ADKjuoqWpBFL1oXJXe2+hPq7aBv4v/FagI+MEDUNb8H9vHA==", "signatures": [{"sig": "MEYCIQDHeIW7eeOjRyTuklvyZ6I7DgNHFFVTetojga///tmQAwIhAN6LdBCx+6iRHrEqMcLpv6j5UKpeG+nw2Pav5EwFQUhG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79719}, "types": "index.d.ts", "gitHead": "9b8ee28b1080<PERSON>bba211b84b6d89682d6fcb2df4", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "16.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.4.0_1705676504316_0.7430173575621501", "host": "s3://npm-registry-packages"}}, "6.5.0": {"name": "undici-types", "version": "6.5.0", "license": "MIT", "_id": "undici-types@6.5.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "c78812d4845076b90a08871d403d8c83a07d6ae3", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.5.0.tgz", "fileCount": 39, "integrity": "sha512-j+GtfW/Ce3WnWiiL9mWfvg5kM//OaW0YsSoE+xe8Z/72cmpHK0uC1kSx3sHl1b0fTzOSYpnLYTsG1ZEieSiJqw==", "signatures": [{"sig": "MEYCIQCT3IJ7ENivV08nKVyrj5RWqYsTUCfYFWDZZdFBkQ+JFwIhAJisoc4pl+rV2xSLm/KjvdM1e6sYK19iSLaWTf+ipkEh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81791}, "types": "index.d.ts", "gitHead": "519b9e13543a594bcfa4d1954bf639c10cf3e824", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "16.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.5.0_1706281889107_0.2551530033099956", "host": "s3://npm-registry-packages"}}, "6.6.0": {"name": "undici-types", "version": "6.6.0", "license": "MIT", "_id": "undici-types@6.6.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "b13ec38d9fb7aeaf73f1902be956f695ac7fe997", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.6.0.tgz", "fileCount": 39, "integrity": "sha512-WqF3FrzJ4oJR8/EIPVY5LtxAkskhsSrdUdAcxEt9sHje/jUkrVI33ye4op0TJ6UhNSdrLOqMQk2/ppn5+oCFVg==", "signatures": [{"sig": "MEYCIQCcjsoiLxn9VDrvAZD5CR7jAFnrRDUVYh80z2AS7td1FQIhAItsCMOJUd867scwEDFJWNnB9GxRAtjm9vo6DtickpL1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81791}, "types": "index.d.ts", "gitHead": "fa2d2d29a46412f8fb1f1a1ecf07b73e0db66a32", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "16.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.6.0_1706780199006_0.7002501392334055", "host": "s3://npm-registry-packages"}}, "6.6.1": {"name": "undici-types", "version": "6.6.1", "license": "MIT", "_id": "undici-types@6.6.1", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "4283acfc9362c6ba0be2d1980fc0a55bcd909d3e", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.6.1.tgz", "fileCount": 39, "integrity": "sha512-I9w6ZtO5ygMnENrKhJ5BRQqnJUDmrqGUl3vl4EwZkncFafkqvLVvYr/i+KUZhzBKGUwQyPUgZW3LBB2299HSFg==", "signatures": [{"sig": "MEQCICiQZ0sZrilEPikH3HVExKbnlLH1P+ldMLmbOJmPLmy8AiAEqanr6zN9meFxqgsGN4xkIFKTZ/D9R6F6lYrdrFh4JQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81791}, "types": "index.d.ts", "gitHead": "d36b19eeaf89b0c02e309bb3bb780c1977b21feb", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "16.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.6.1_1707132762340_0.5433042956772609", "host": "s3://npm-registry-packages"}}, "5.28.3": {"name": "undici-types", "version": "5.28.3", "license": "MIT", "_id": "undici-types@5.28.3", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "698aa430db5e60dbf3799385ad8e15085751fcc0", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-5.28.3.tgz", "fileCount": 37, "integrity": "sha512-VJD0un4i6M1/lFOJPhacHdq6FadtlkdhKBed2W6yBqmrAr/W58oqENaOIX031stDVFwz9AemOLkIj/2AXAMLCg==", "signatures": [{"sig": "MEUCIQDf743GL4C+o9IBLO7zpOVXTeOeMo7Eb1PeAZz0ApccAAIgVVrJPe39Kvz8GtyDH6K98eEksFv0fP4rpVjhrslfdZ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77295}, "types": "index.d.ts", "gitHead": "e71cb4c88faae5670a129fde5552266afc2dbc39", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "16.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_5.28.3_1707133249317_0.6737349324979853", "host": "s3://npm-registry-packages"}}, "6.6.2": {"name": "undici-types", "version": "6.6.2", "license": "MIT", "_id": "undici-types@6.6.2", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "48c65d30bfcae492c3c89b1d147fed9d43a16b79", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.6.2.tgz", "fileCount": 39, "integrity": "sha512-acoBcoBobgsg3YUEO/Oht8JJCuFYpzWLFKbqEbcEZcXdkQrTzkF/yWj9JoLaFDa6ArI31dFEmNZkCjQZ7mlf7w==", "signatures": [{"sig": "MEYCIQCpgPZEHvuz+lJEe/2dBcqpuVGfwI58GSG9q5g4j/DXnwIhAN+ONadmXQTF++1l1yg+C4nHdhRkCxYozxTir6HfNZiz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81791}, "types": "index.d.ts", "gitHead": "e48df9620edf1428bd457f481d47fa2c77f75322", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "16.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.6.2_1707315251999_0.2887340812558947", "host": "s3://npm-registry-packages"}}, "6.7.0": {"name": "undici-types", "version": "6.7.0", "license": "MIT", "_id": "undici-types@6.7.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "1b7025585e712a07ddec7f2f642fbe8ffc5dbc28", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.7.0.tgz", "fileCount": 40, "integrity": "sha512-dZuEp06dJ32FQ/0HdqsA/ci0LbMDsTo8h0w7bOlThZXfY3tqNC6Vg68koejjl9dr6Kcw3Q6PdWjaPTMYzipO4g==", "signatures": [{"sig": "MEYCIQDqSkEPV9bf+gJvgzhn2xiJnVUb8i68F8MhKf1qrl2rOQIhAOIk0DJO0BwHNI/eRWyoleppexPoFs1QgP6gPKsjRYM1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82403}, "types": "index.d.ts", "gitHead": "2316bae1b790517b9fbc8d066582410604ab733b", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "20.11.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.7.0_1709485757816_0.5943616282969899", "host": "s3://npm-registry-packages"}}, "6.7.1": {"name": "undici-types", "version": "6.7.1", "license": "MIT", "_id": "undici-types@6.7.1", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "1be08ee81dd361f9563289af202b7730e247153a", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.7.1.tgz", "fileCount": 40, "integrity": "sha512-7r+rI8EcAjs3Th8JYhJUW4K6cNne8aPoQ7f44Y8zZIkLEcn7UkKvbQpFgBu/fbn2YY7k44pn1e7PnTY/vzBnVQ==", "signatures": [{"sig": "MEUCIQCGddnlgJLE/KteEAm/jA5QMaudnT1SvSamYR6pmOTOQQIgDgH9B/69A1TcV4/fTHgsA8fulu/Gg+O/UuiNNjpb69M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82403}, "types": "index.d.ts", "gitHead": "219da8b7b3fea7e38a7644b8bc35fe6fec97d66e", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "20.11.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.7.1_1709887775297_0.5427621491349508", "host": "s3://npm-registry-packages"}}, "6.8.0": {"name": "undici-types", "version": "6.8.0", "license": "MIT", "_id": "undici-types@6.8.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "812c7ded1c9ef92eb12e774e7fd52309177eb725", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.8.0.tgz", "fileCount": 40, "integrity": "sha512-5Nx4nBLLJBqhMezWmoEP+xpKmSGuOYagWgaxImc38hre6h6CxoMRRxkPbHKBsz+MZcSUaxgDNGKRGixBmSDoOw==", "signatures": [{"sig": "MEQCIB4S2lIhQaqe1KeRG1ah9xCL6v8555ziY0eZHHgfMpI4AiAjxZTiDhSmBK1US5YhFGtbroXycyWUEAhP1g1WWhAXIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82403}, "types": "index.d.ts", "gitHead": "f84ec8087e11a26ee3553a0c601f6a73373edae6", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "20.11.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.8.0_1710319533155_0.9043564445184484", "host": "s3://npm-registry-packages"}}, "6.9.0": {"name": "undici-types", "version": "6.9.0", "license": "MIT", "_id": "undici-types@6.9.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "345650ac91d7faa758f64c47946220880c72a8eb", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.9.0.tgz", "fileCount": 40, "integrity": "sha512-SwFgFJtloS7+CcTkjRdpwjCA0RWtZXZH26DIcy+cn6+7AfciCBi2oWYImlaBMvgq5b5DbhFe37ebaUphbmi9QA==", "signatures": [{"sig": "MEYCIQC1qgC11I3ruPune9VCX0nuDpW8Rq1T3aTlvgOYtwg53QIhALOMr4gI1Cfq4Tjc/Ns30B5bl1zlUAwxlq3lsNcIAH8a", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82410}, "types": "index.d.ts", "gitHead": "****************************************", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "20.11.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.9.0_1710436424086_0.6002660075998916", "host": "s3://npm-registry-packages"}}, "6.10.0": {"name": "undici-types", "version": "6.10.0", "license": "MIT", "_id": "undici-types@6.10.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "fced15f25a3bf23bb30838567fa981115e28e097", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.10.0.tgz", "fileCount": 40, "integrity": "sha512-hnbshH4eaJunfBee1JWEt+7//qGu/lAoArnPZ9YmRxB4wxE4yq+SWRstWEKM5aC1/vRLByzFXa/ULyYEv2obIA==", "signatures": [{"sig": "MEUCIGlLUMf6st7Qxa8KH9aLD7tU8KZsA2enu8OjAX4I/Gv4AiEA6A9F9QK88eKjohqg0r67YHe6VorSNqXD6Bj3HRfepbU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82753}, "types": "index.d.ts", "gitHead": "e434060efc659e30865d711eafb71a6b01915533", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "20.11.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.10.0_1711019141128_0.09692671248680784", "host": "s3://npm-registry-packages"}}, "6.10.2": {"name": "undici-types", "version": "6.10.2", "license": "MIT", "_id": "undici-types@6.10.2", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "b7857f613e0db97b9409917dfcb7d9c234160a28", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.10.2.tgz", "fileCount": 40, "integrity": "sha512-6/AUdsIOYVNllcOhIStipEzDyx/u8C5LdKq2gnJXtlMc42WodPl+qJBydPzlzyyo919+DNjRRwgsJUKsoiF7EQ==", "signatures": [{"sig": "MEQCIDkCPJtht3NE2yiZYJ2cgRW7ANrR4sUrAXBdNJ17UfapAiA+RKsAWlrcnXveKD/zj66YxJD37xHgPa4xVk1Qcpr7Uw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82773}, "types": "index.d.ts", "gitHead": "7485cd9b4cf9a86cb76b1597df527eba15755bfc", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "20.11.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.10.2_1711532174047_0.6121523778960483", "host": "s3://npm-registry-packages"}}, "6.10.1": {"name": "undici-types", "version": "6.10.1", "license": "MIT", "_id": "undici-types@6.10.1", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "5f9c1fcbf249f7c6f835896fec17e9510b143c50", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.10.1.tgz", "fileCount": 40, "integrity": "sha512-A9gdHGE8EFZnd4IdrfizVgmnTvR8iWZUE2dqPIpAYEO4yZKv3Fed0Xhs8y17JKtQfthDMVwJBv8dOqFe52t3sw==", "signatures": [{"sig": "MEQCIGwNloeL6tDvZ9PVcx41g+d+RHuiZMTXSoqPGY2+9RuWAiBG+GnKEZqezvkEcih6C6l8Y1vuceaMFsJMQX8ciniWLA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82753}, "types": "index.d.ts", "gitHead": "dd3918fee4f90e02fb93ff1bc04e707144041938", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "20.11.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.10.1_1711533000411_0.8780367552404467", "host": "s3://npm-registry-packages"}}, "6.11.0": {"name": "undici-types", "version": "6.11.0", "license": "MIT", "_id": "undici-types@6.11.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "70d843cec4074858c8ecf733da07e55c3d88cc26", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.11.0.tgz", "fileCount": 40, "integrity": "sha512-poAjeKpLaKuV41mZfeiUuc7mO/kFAixPpvadHZ6U2Xy8HnL1xz3unkXEwULbeukqt9bjOucc6FxG8E+ATnzJww==", "signatures": [{"sig": "MEUCIQDBR8IYfBCWQamGB+pVz+FNQM3tRvs7DgKPEfeSAR8pcQIgJXOyEBVT22ktwxsQ8KCrXeGfNNzdTbrht3YjXqyIeXg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82718}, "types": "index.d.ts", "gitHead": "ee5f892f3955eaca37730ed30349153ba203e9cd", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "20.11.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.11.0_1712075253700_0.30114187538157866", "host": "s3://npm-registry-packages"}}, "5.28.4": {"name": "undici-types", "version": "5.28.4", "license": "MIT", "_id": "undici-types@5.28.4", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "501669b1af1f288a9cbc2e273811965c9178306d", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-5.28.4.tgz", "fileCount": 37, "integrity": "sha512-3OeMF5Lyowe8VW0skf5qaIE7Or3yS9LS7fvMUI0gg4YxpIBVg0L8BxCmROw2CcYhSkpR68Epz7CGc8MPj94Uww==", "signatures": [{"sig": "MEUCIQCrOu1jzD6guNF3QjG8IXbT+xFg/NbiWl7I2xNuBBqN+wIgdPlmyAMTgDIBEgyEQoEoPGmmi4SzhZcNEQPhlc4CIy0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77295}, "types": "index.d.ts", "gitHead": "fb983069071f52e0a7ea0e71078459c765aae172", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "16.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_5.28.4_1712075984861_0.13614113613342949", "host": "s3://npm-registry-packages"}}, "6.11.1": {"name": "undici-types", "version": "6.11.1", "license": "MIT", "_id": "undici-types@6.11.1", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "432ea6e8efd54a48569705a699e62d8f4981b197", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.11.1.tgz", "fileCount": 40, "integrity": "sha512-mIDEX2ek50x0OlRgxryxsenE5XaQD4on5U2inY7RApK3SOJpofyw7uW2AyfMKkhAxXIceo2DeWGVGwyvng1GNQ==", "signatures": [{"sig": "MEUCIGZ5BAc1f4hyJqG9maPkYAtOTY+OgPwVnnwJSQXpL/mvAiEA7EEAV2omKwJkfIwzLqkvz1pDGG2DcwcBKphzSd3oV2I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82718}, "types": "index.d.ts", "gitHead": "6df3c738d03dc4014a26640316bf699950d62024", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "20.12.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.11.1_1712076394919_0.2860254040968042", "host": "s3://npm-registry-packages"}}, "6.12.0": {"name": "undici-types", "version": "6.12.0", "license": "MIT", "_id": "undici-types@6.12.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "ee8205806a68ecacba267b266f8597eba2b0bca7", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.12.0.tgz", "fileCount": 40, "integrity": "sha512-spYS3Hq2I215rLthdNw/oP0RnRzz24MDQOkcb4azgmIM/bdx+B2u/xZjbWCFjqJHx/qFv2k3sRYg6+snBjDNqg==", "signatures": [{"sig": "MEYCIQDL4yxqyfNTWxws4sYDotQjvvn117dKqFsv/GNZpAJkBQIhAKpL97TZaGHrAQEGJMoK4cead80PpLUPcv6Bzax49Ufb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82708}, "types": "index.d.ts", "gitHead": "7751d9bcd5bbba45b60c90183aeab450b60c0831", "_npmUser": {"name": "ethan_arrowood", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "20.12.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.12.0_1712569641470_0.16119400575140386", "host": "s3://npm-registry-packages"}}, "6.13.0": {"name": "undici-types", "version": "6.13.0", "license": "MIT", "_id": "undici-types@6.13.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "e3e79220ab8c81ed1496b5812471afd7cf075ea5", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.13.0.tgz", "fileCount": 40, "integrity": "sha512-xtFJHudx8S2DSoujjMd1WeWvn7KKWFRESZTMeL1RptAYERu29D6jphMjjY+vn96jvN3kVPDNxU/E13VTaXj6jg==", "signatures": [{"sig": "MEUCIQCbDfAdZqPbxwx+yg1Zjz8UwQXO8pFRtsw1+srTq9l1WwIgCRSwJMdhlkciKNpBQSq/LFpP0Ma/90+u+1D0v8HuA/o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/undici-types@6.13.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 82708}, "types": "index.d.ts", "gitHead": "65f768c72762b38e3d35a8a4934c0830c41b0f6c", "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "10.5.2", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "20.12.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.13.0_1712911369533_0.0882651436465316", "host": "s3://npm-registry-packages"}}, "6.14.0": {"name": "undici-types", "version": "6.14.0", "license": "MIT", "_id": "undici-types@6.14.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "b756dbb8c5b0489d415cef022535e12f043ff6ff", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.14.0.tgz", "fileCount": 41, "integrity": "sha512-q9cSMXCmf1wgG4+8jEy9dhuinxP5yS2/n3+bcXO4Q4RwRYy9CdP/HRfGCpI0MHykuPGeBH/40C1PA7dIIBRzbQ==", "signatures": [{"sig": "MEQCIFpqqdjbzeKMosOoEiJLcMneBgf17jaH9NKBrx/03Wc7AiAmm1lmNSBcBmx82FV9+t3i5Gwgqk+x4kygPAfdSHOcPQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/undici-types@6.14.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 83534}, "types": "index.d.ts", "gitHead": "9458ffd4f1e5116cdcf8b421a3171d3aded41d46", "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "10.5.2", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "20.12.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.14.0_1713799104331_0.1941079054296324", "host": "s3://npm-registry-packages"}}, "6.14.1": {"name": "undici-types", "version": "6.14.1", "license": "MIT", "_id": "undici-types@6.14.1", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "37d176df66898d2e028ff39e09134d05c796215a", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.14.1.tgz", "fileCount": 41, "integrity": "sha512-aYaPmOBKwekCVadPU9b0HW6AvXai3nHKmPWBDwjun36RLRoBgEOAfkfAH7KArVqi7o5/slRhaYkFmt0wuJyDmQ==", "signatures": [{"sig": "MEYCIQDJ0AM93oWdfdzTJtClyT5nMHDOd2IC0GSiheeAKqlLxQIhALCXYIBh9tzUQC+O0eibZOb+JWMJcMHdG0a4hUBVofrM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/undici-types@6.14.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 83534}, "types": "index.d.ts", "gitHead": "1c440555a0c25d6a2ee2b0bdf8c5afcd9636332f", "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "10.5.2", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "20.12.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.14.1_1713868627264_0.051378526285156045", "host": "s3://npm-registry-packages"}}, "6.15.0": {"name": "undici-types", "version": "6.15.0", "license": "MIT", "_id": "undici-types@6.15.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "04b86307eaf366a043727bb3eb9d21e9839cd3a6", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.15.0.tgz", "fileCount": 41, "integrity": "sha512-zJlHmwXg6e41SGrTdd+1wOl00iGjscWSrhYkeANV7GPnDAZ2Z9kGVCgTVkpzcazNebHxN6x3zdSiKCE0JrWhsw==", "signatures": [{"sig": "MEUCIEZ7wRDZA6BiJaMwqD3U+BRQTNy6uwTTOvGLcotEBUOdAiEAuBbDxNLIQO41X+Fka/93P2bG8ARPLy9dams3J5gEx4s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/undici-types@6.15.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 83611}, "types": "index.d.ts", "gitHead": "41dc36c845ce1386d0700bba099879ef355648d1", "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "10.6.0", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "20.12.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.15.0_1714392477667_0.9310034000116048", "host": "s3://npm-registry-packages"}}, "6.16.0": {"name": "undici-types", "version": "6.16.0", "license": "MIT", "_id": "undici-types@6.16.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "b3bbe917fe378b8b8cd8e15a8d72aa68ac09c20d", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.16.0.tgz", "fileCount": 41, "integrity": "sha512-y3dW5MONx2FFOGdPfIE3XzcC5j5Dz52vrpbGyP8wIx3BLjc7L/TUZSi/SNWJrwJubmShcyIuhetWsqxoMmHg2w==", "signatures": [{"sig": "MEQCIFZzJt/Ew3EbAJkDew4PYX4K4jZd4ki5sY7HbEigM7hNAiBXSOLaW4vhAe4RNLMF2chjWsfhNAB0Yb2RXS3ijhJeRQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/undici-types@6.16.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 83174}, "types": "index.d.ts", "gitHead": "f8978bdf1e5583e1dcda2c00cdc116a4aaf9ae56", "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "20.12.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.16.0_1715097709141_0.6355387836130402", "host": "s3://npm-registry-packages"}}, "6.16.1": {"name": "undici-types", "version": "6.16.1", "license": "MIT", "_id": "undici-types@6.16.1", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "0748822389b184b33c4cf56414bd499aaddcdd21", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.16.1.tgz", "fileCount": 41, "integrity": "sha512-iOd+ZQKljEITFeCuOOTXnZUpVggAivBEQPZGdGWsij0QjgZgGEzn2awf8TBhAer9IREcCtFYT3JpG6p+aJU/oA==", "signatures": [{"sig": "MEUCIQCOaB8iWjWO9MzI3Nj+nWQdJE8eNfztWEwn3w/Vpvn5nQIgJq8uXL+Qz3pJZSFD2hYlAES/4z0iTx7w4MITb41k1eY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/undici-types@6.16.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 83174}, "types": "index.d.ts", "gitHead": "a613753c03b78ea11dbf884aaafc067895a580e1", "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "20.12.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.16.1_1715334329404_0.16024547314800364", "host": "s3://npm-registry-packages"}}, "6.17.0": {"name": "undici-types", "version": "6.17.0", "license": "MIT", "_id": "undici-types@6.17.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "4f689d0331786e6347b50fe70b6b0c99f2fea6e9", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.17.0.tgz", "fileCount": 41, "integrity": "sha512-RDqsYjfPRx+zrn6nd3C7V3wJ9TVWh3/EkdaMNTEt1GjNTYyiHnEa3ZYfaWDHVb4H0H1XfwA+lytficpjwvR0IA==", "signatures": [{"sig": "MEYCIQDQe92VCz9dJvXOhg56yalztg39TVkhsCYCPAwmLiexhwIhALpY83x5TZFaKPKrN9RLGT3q1R+OMX5/RV4dikacAzLo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/undici-types@6.17.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 83855}, "types": "index.d.ts", "gitHead": "0ca9c1e1faa8cac5ed9310dacbe2e9b5cfd4f6b1", "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "10.8.0", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "20.13.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.17.0_1715926047382_0.21801117623153154", "host": "s3://npm-registry-packages"}}, "6.18.0": {"name": "undici-types", "version": "6.18.0", "license": "MIT", "_id": "undici-types@6.18.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "8391168d56adf3f3ec1db55b463ba117e3c403d0", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.18.0.tgz", "fileCount": 41, "integrity": "sha512-w2f2PnG0PfXFkk+liTJUyfE+laxp1o7RFgDC+wQszkUqOwwAcUWp1vxa3YHV8bkx6cA/lhedJT4x9q+rv7Q3zw==", "signatures": [{"sig": "MEQCIBB5OiN+vz2uMiU3rytFzMGXB4MGhGIpUY0FXeB2ConkAiAT16WvgeWzTyySBKzf5sksuLqONvFeeq54yghC0Ft1CQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/undici-types@6.18.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 83855}, "types": "index.d.ts", "gitHead": "284827820c1bb6d2e98d11df2890b5970bc961c5", "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "10.8.0", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "20.13.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.18.0_1716210006049_0.39580614230868627", "host": "s3://npm-registry-packages"}}, "6.18.1": {"name": "undici-types", "version": "6.18.1", "license": "MIT", "_id": "undici-types@6.18.1", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "56bcf28d879a741ffde7f105b1e3ac1a149a34f3", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.18.1.tgz", "fileCount": 41, "integrity": "sha512-yay8kaNGEGjUrGtW1ubEVqeWDHFpEdB7Pw/v+n7b5clmBkTTZJJpAEYtRtaCJ5wTDpTLR/Dn9nPgjGBRaIhvFg==", "signatures": [{"sig": "MEQCIFRskOL+d6X8pZ5mjBJyChVbTeBVPQuQSlqhFINsZ4e3AiAAz4AEN39gSW6qkqwIwkApWxB9ozCUaO5WIQHJaqApyg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/undici-types@6.18.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 83855}, "types": "index.d.ts", "gitHead": "eed423a66960d61da56f6185d9b6624e32cd4ff9", "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "10.8.0", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "20.13.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.18.1_1716374362714_0.6274150468862567", "host": "s3://npm-registry-packages"}}, "6.18.2": {"name": "undici-types", "version": "6.18.2", "license": "MIT", "_id": "undici-types@6.18.2", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "8b678cf939d4fc9ec56be3c68ed69c619dee28b0", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.18.2.tgz", "fileCount": 41, "integrity": "sha512-5ruQbENj95yDYJNS3TvcaxPMshV7aizdv/hWYjGIKoANWKjhWNBsr2YEuYZKodQulB1b8l7ILOuDQep3afowQQ==", "signatures": [{"sig": "MEUCIQDKzBD9aD8+2BtWwEUlRSCuVHpJAMA5QXlglDmDGxvNFQIgNu4zFBpBJdp0gTUYIyKYwBRbLQaGWSzZ2WfkOJQLvKw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/undici-types@6.18.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 83855}, "types": "index.d.ts", "gitHead": "665f24738041757789fab95cce40cb0345cf2c0f", "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "10.8.0", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "20.13.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.18.2_1716989603359_0.799056337820301", "host": "s3://npm-registry-packages"}}, "6.19.1": {"name": "undici-types", "version": "6.19.1", "license": "MIT", "_id": "undici-types@6.19.1", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "22f0238c60c3e2dd6b5a01ad5f5f2b0a39041b75", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.19.1.tgz", "fileCount": 41, "integrity": "sha512-rML31stL5uvaUvjF2YpHO5lX+jrTZvnj+t8m+dFTeamn2Fbz8j6FOtVF2fK3FCvs1/C1SPhc4s6BYwGWaZJShg==", "signatures": [{"sig": "MEUCIQDzPeAYv/4HY6LDK5hcaQ92mz+JT+FydiKA8ABoGoSenAIgNrm5kCtg/n6MBajkaD2effZXOSLWQM5kwCGayb7DkiU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/undici-types@6.19.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 84461}, "types": "index.d.ts", "gitHead": "f6b9b442b02d8ab81b06ea0473e1c22bc8ddc254", "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "20.14.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.19.1_1718651030195_0.17631820201200576", "host": "s3://npm-registry-packages"}}, "6.19.2": {"name": "undici-types", "version": "6.19.2", "license": "MIT", "_id": "undici-types@6.19.2", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "215f35d42793966563f3bacb1f768cbc396e0a2c", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.19.2.tgz", "fileCount": 41, "integrity": "sha512-jvI+p8VJnrOIQ8AU4PyB1ytW68EyrLnI2xvCO3e5umm3nmhNjpugMD1wo+X994PyvUT9dWS/k3vO1wwRIh730Q==", "signatures": [{"sig": "MEUCIQC7sG1UcxWj+rEFvsQzaj6iChaHtMzLQ0J4W102NH53KAIgWeNLbO8jVKSWiZHKwxwqAfhk0Fh1tCXtPlaXDo9GD4Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/undici-types@6.19.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 84225}, "types": "index.d.ts", "gitHead": "532b7b28827afe52ed7b43cbb210ac8425516c28", "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "20.14.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.19.2_1718695475163_0.8632617514371559", "host": "s3://npm-registry-packages"}}, "6.19.3": {"name": "undici-types", "version": "6.19.3", "license": "MIT", "_id": "undici-types@6.19.3", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "e8ce67668c7ec8cd464bb0a7b0ac303b3bd8ae4e", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.19.3.tgz", "fileCount": 41, "integrity": "sha512-MsVItLOJ2R26sie5QTIrd4RKaMAb6auG4xgZoHn1LZ9aiuBC8flYigxRaX5zaaiU69bIFKMLgiPb9XJQ1+nyrQ==", "signatures": [{"sig": "MEUCIQDYvOjB5m4R1dPoOsBHoT5tP63J0YS7an3fjPwcPu512AIgYbkzwRV37E/oMjIXrniE1DVOUhfRCBA/kmSczJ9qcKA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84225}, "types": "index.d.ts", "gitHead": "99102ccf646f08708d49187ddc835a016cbfd6f9", "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "20.15.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.19.3_1721636115020_0.6298918447454691", "host": "s3://npm-registry-packages"}}, "6.19.4": {"name": "undici-types", "version": "6.19.4", "license": "MIT", "_id": "undici-types@6.19.4", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "78650a42cd883b0e6e819407603daffbe3cffbfe", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.19.4.tgz", "fileCount": 41, "integrity": "sha512-BiKTnNSkjSEYzxd0X3KQ/ZNoA8/aFlS598kds3PuZ55csLy3fFqGap0aP84Ekb/VWYG5um4MgilNs3kAx4LHMg==", "signatures": [{"sig": "MEYCIQDlqEGysNPnnbNHAQoqlZbIHiugnW9SWHMzDGUm4UOe+wIhAPldhad54s/3KjNkuOMoaLyFCIyIo289tJpZzfjz7Cin", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84225}, "types": "index.d.ts", "gitHead": "62241c3600513cf0e8eac11cf16ed9dca98a80ac", "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "20.15.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.19.4_1721656674066_0.12618376476764714", "host": "s3://npm-registry-packages"}}, "6.19.5": {"name": "undici-types", "version": "6.19.5", "license": "MIT", "_id": "undici-types@6.19.5", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "25d861b7995ae90dbb215c0bbbf47e6db85c3216", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.19.5.tgz", "fileCount": 41, "integrity": "sha512-VQUzGd+K73uDi/pTqzDBbxZneciOuMRjF0r/Lep2zr/GOnU+cUvfgRu4T5k4TWJfpGdSK5nrzVDoQVoEIAFbmg==", "signatures": [{"sig": "MEYCIQDtZ5qvlQJKu1VfFufxr0KaTyDUsXbL1JR30nEpFbLH1AIhAM43GyrE8EhYVoYWYONMLPHInFCoIbBIvEmdf/Rjh+nb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84225}, "types": "index.d.ts", "gitHead": "8499c4bbdc9dcff3a5ce36fac41b4e9497814a16", "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "20.16.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.19.5_1722422008170_0.42782679471642093", "host": "s3://npm-registry-packages"}}, "6.19.6": {"name": "undici-types", "version": "6.19.6", "license": "MIT", "_id": "undici-types@6.19.6", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "e218c3df0987f4c0e0008ca00d6b6472d9b89b36", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.19.6.tgz", "fileCount": 41, "integrity": "sha512-e/vggGopEfTKSvj4ihnOLTsqhrKRN3LeO6qSN/GxohhuRv8qH9bNQ4B8W7e/vFL+0XTnmHPB4/kegunZGA4Org==", "signatures": [{"sig": "MEQCICHyDnxTdw+UKitVb16fH0Nv7k82eaOSKFQmOAMqMYKGAiAkyEHv/V6Ba/s/Oac76BkD8f7f5NCFWKDhKnhW9t+HYA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84225}, "types": "index.d.ts", "gitHead": "b9bf7adfddeb9cd93a9433a0a1d38e39afa5dfb5", "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "20.16.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.19.6_1723192692181_0.0674023072724046", "host": "s3://npm-registry-packages"}}, "6.19.8": {"name": "undici-types", "version": "6.19.8", "license": "MIT", "_id": "undici-types@6.19.8", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "35111c9d1437ab83a7cdc0abae2f26d88eda0a02", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.19.8.tgz", "fileCount": 41, "integrity": "sha512-ve2KP6f/JnbPBFyobGHuerC9g1FYGn/F8n1LWTwNxCEzd6IfqTwUQcNXgEtmmQ6DlRrC1hrSrBnCZPokRrDHjw==", "signatures": [{"sig": "MEUCIGHm3RU6nmPjpFrE7lus7cMT9nBeVHOhTWjzflE57x08AiEAs6/KJsXAnqL1KXWZBAkJ5Gf4NdDUqZcjr/OL+clXX3I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84225}, "types": "index.d.ts", "gitHead": "3d3ce0695c8c3f9a8f3c8af90dd42d0569d3f0bb", "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "20.16.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.19.8_1724087971683_0.46442472430222814", "host": "s3://npm-registry-packages"}}, "6.20.0": {"name": "undici-types", "version": "6.20.0", "license": "MIT", "_id": "undici-types@6.20.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "8171bf22c1f588d1554d55bf204bc624af388433", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.20.0.tgz", "fileCount": 41, "integrity": "sha512-Ny6QZ2Nju20vw1SRHe3d9jVu6gJ+4e3+MMpqu7pqE5HT6WsTSlce++GQmK5UXS8mzV8DSYHrQH+Xrf2jVcuKNg==", "signatures": [{"sig": "MEQCIHd/Gtj3oAJIlw8kP2csQy8I6DCLSNszG0rZ0aZwjJ+IAiApQh6swzDA7ZoWbjy6WHSgtIk60eoq06r3v/5Qa6VvBg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83349}, "types": "index.d.ts", "gitHead": "24b940329af4ad7b72fad89824a3d0cee924d23f", "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "20.17.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.20.0_1728460238702_0.33444145625174193", "host": "s3://npm-registry-packages"}}, "6.21.0": {"name": "undici-types", "version": "6.21.0", "license": "MIT", "_id": "undici-types@6.21.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "691d00af3909be93a7faa13be61b3a5b50ef12cb", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-6.21.0.tgz", "fileCount": 41, "integrity": "sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ==", "signatures": [{"sig": "MEQCIDiOEbu6RWRkOnmP+JKSWceSpDutDhrGGmI90/OmHLfqAiAsuiLiSZuw9Jy+qpfzOaxJzR9d5d3mNqEkBnAlUs0Q0g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83680}, "types": "index.d.ts", "gitHead": "61ec3531a64ffeec953a990c11735ff09455de4e", "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "20.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_6.21.0_1731505028949_0.964438528822493", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "undici-types", "version": "7.0.0", "license": "MIT", "_id": "undici-types@7.0.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "7a130f803f7411a28ee0704fda82cf2f82db26f1", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-7.0.0.tgz", "fileCount": 41, "integrity": "sha512-KGeRdWTIBFYWOPvnssHfsmYsWSdcLWOcdvchHZ5tqdhGWuKg3wBXc5o2eTe/KORc3Yinrrddt8+zYBdSr46Pkw==", "signatures": [{"sig": "MEUCIQDycVbsHU6rGniR6RoAb3yZ3aVs1TB6Afphb1FzCcyZTAIgEZA++WVVZ7gj+9KFd3FikPecu86KZf+vYwpJPwugW8s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 92455}, "types": "index.d.ts", "gitHead": "1cfe0949053aac6267f11b919cee9315a27f1fd6", "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "22.11.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_7.0.0_1732791034966_0.3482088854550782", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "undici-types", "version": "7.1.0", "license": "MIT", "_id": "undici-types@7.1.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "4561b2df582a6c8cf38d37964f13837aadd7f048", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-7.1.0.tgz", "fileCount": 41, "integrity": "sha512-IdycojzhJm9g7sblozLpNkyPG2dJ7bsrpfc4hq2QKrnwbDK+ebiGL3UFSwq6B3IzB3R0m3u1b4in3hrZtMnm8g==", "signatures": [{"sig": "MEYCIQCn03QcbmTBDV/UPviWWxM1MSarMAg4lGAn9Molo6kRKQIhANbZ7PZqNZIPMrgO8OuHF6T4cjrruaRDZFn5C8kBnkwO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/undici-types@7.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 92455}, "types": "index.d.ts", "gitHead": "95f7d6c691893deaef1a00827fbb27931bc4f8e4", "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "10.9.1", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_7.1.0_1733239418590_0.2935067886167557", "host": "s3://npm-registry-packages"}}, "7.1.1": {"name": "undici-types", "version": "7.1.1", "license": "MIT", "_id": "undici-types@7.1.1", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "9902007137afaa06bb568c404c6122fb84bce8d9", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-7.1.1.tgz", "fileCount": 41, "integrity": "sha512-mHbN00TC1eXAlNt8V3fdsPi5clP7ictht+L7SzrLueTV1IFDEGdLJMWLphrlq6ba09UoYZiO9VpLrOYQ6do1LQ==", "signatures": [{"sig": "MEYCIQC7cAIevCJFUXMnW1n6aJ2FhYb+kBgIEjsxdJOhS3SQ+AIhAOL/LuGS4Wmusxrh1IYZFiy7wM+21HhgGaggfIp/AM/T", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/undici-types@7.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 92540}, "types": "index.d.ts", "gitHead": "e9a336971c25564a833fcb927af00579d2cce6e9", "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_7.1.1_1734369012163_0.21299362106737774", "host": "s3://npm-registry-packages-npm-production"}}, "7.2.0": {"name": "undici-types", "version": "7.2.0", "license": "MIT", "_id": "undici-types@7.2.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "6651a38db40a8e9103c94b66e885044d0ffb0d83", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-7.2.0.tgz", "fileCount": 41, "integrity": "sha512-/3xD7OTyHquyh9bEz8puhS6ajaOqTYv88RBrHHpK0LAIWRwqKuCK8W1w5sJaOXXsr9/B5AYoJ/cYImiO0C4sQQ==", "signatures": [{"sig": "MEUCIAbK73vQFFpAw7pO4mHnoj/yqrXgvvVrUGrX0RngU3DpAiEAgs9GHZB5wjlq2JqvWlc4QQkkOFEFn3wyPFymc632lTo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 92540}, "types": "index.d.ts", "gitHead": "29760e70972f58a1bccfb02a5566803227198aff", "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "11.0.0", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_7.2.0_1734545456693_0.4275908946020519", "host": "s3://npm-registry-packages-npm-production"}}, "7.2.1": {"name": "undici-types", "version": "7.2.1", "license": "MIT", "_id": "undici-types@7.2.1", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "****************************************", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-7.2.1.tgz", "fileCount": 41, "integrity": "sha512-JzyfEFtoTUqsKrSPsm4U75Z3a8wt1Gx3BHN+auORziFSkk7Nqa4eFKKsWiY12NMdaDiUUuvJF4Gf9F/AeWWHYQ==", "signatures": [{"sig": "MEQCIB4zFbMFpesXoPnCP8IYxn5OEcNg8V2f3hFx05sumgJYAiBei4O4uy6IdS7eyWA32qgFunSBjRPIgTSqXyl1UdL9fA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/undici-types@7.2.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 92971}, "types": "index.d.ts", "gitHead": "251da10da915b70bf576777ca0734bba8eb38f96", "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "11.0.0", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_7.2.1_1736435164647_0.6932382601327816", "host": "s3://npm-registry-packages-npm-production"}}, "7.2.2": {"name": "undici-types", "version": "7.2.2", "license": "MIT", "_id": "undici-types@7.2.2", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/dnlup", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ethan-arrowood", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/KhafraDev", "name": "<PERSON>"}, {"url": "https://github.com/ronag", "name": "<PERSON>"}, {"url": "https://github.com/szmarczak", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/delvedor", "name": "<PERSON>"}], "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "dist": {"shasum": "5541311027da6fbfe18107de5983522de85a629a", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-7.2.2.tgz", "fileCount": 41, "integrity": "sha512-VerMUVcyhof24hWfYwVryNfaHl8FebNDGV9ktzVbI1t0K2+sUhKtlOPq0CGnPhl3D9SlUB9DlTHYCjP1jECMeA==", "signatures": [{"sig": "MEQCIAb1GTvDQGUHF3TerK+4nhAgzWhS9lnqbgQYSG4siFbvAiBNLEwzhHEj5Ud3flf1tjDVVaj9dMPWjowg0F5GoEgS8w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/undici-types@7.2.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 92971}, "types": "index.d.ts", "gitHead": "bd98a6303e45d5e0d44192a93731b1defdb415f3", "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/undici.git", "type": "git"}, "_npmVersion": "11.0.0", "description": "A stand-alone types package for Undici", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/undici-types_7.2.2_1736962158363_0.11555432421847156", "host": "s3://npm-registry-packages-npm-production"}}, "7.2.3": {"name": "undici-types", "version": "7.2.3", "description": "A stand-alone types package for Undici", "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "repository": {"type": "git", "url": "git+https://github.com/nodejs/undici.git"}, "license": "MIT", "types": "index.d.ts", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/dnlup"}, {"name": "<PERSON>", "url": "https://github.com/ethan-arrowood"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/KhafraDev"}, {"name": "<PERSON>", "url": "https://github.com/ronag"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/szmarczak"}, {"name": "<PERSON>", "url": "https://github.com/delvedor"}], "_id": "undici-types@7.2.3", "gitHead": "83537aa55028bfeef5dd9504abfde63264c2df5d", "_nodeVersion": "20.18.1", "_npmVersion": "11.0.0", "dist": {"integrity": "sha512-ExIyOiwqQGQkOzlNlYUZVwW5k0qKim9ygUDHEkpHX/KsXpsKKFIOTSojlr+f0R5dGhtD10rL3Yp8L3nVoYTnJw==", "shasum": "727873c8fcb4da9290c4f9a38c1d93243df67a48", "tarball": "https://registry.npmjs.org/undici-types/-/undici-types-7.2.3.tgz", "fileCount": 41, "unpackedSize": 92971, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/undici-types@7.2.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCIR2eJoRXRpFVw1E+s1IRYNj7G87gI+VKHIoQXSXOOWwIgcT2ZbwfSWFa4j/YJnCbWn3eXMxvR3jLzYQiRlco/Tzc="}]}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/undici-types_7.2.3_1737018970001_0.1752162594272637"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-09-19T15:09:35.360Z", "modified": "2025-01-16T09:16:10.583Z", "0.0.1": "2023-09-19T15:09:35.622Z", "5.24.0-test.2": "2023-09-19T17:19:38.262Z", "5.24.0-test.5": "2023-09-19T17:41:34.002Z", "5.24.0-test.6": "2023-09-20T20:49:25.511Z", "5.25.1": "2023-09-20T21:03:49.603Z", "5.25.2": "2023-09-25T20:16:53.499Z", "5.25.3": "2023-10-01T14:53:18.228Z", "5.26.0": "2023-10-11T15:21:32.601Z", "5.26.1": "2023-10-11T18:27:59.617Z", "5.26.2": "2023-10-11T18:59:52.872Z", "5.26.3": "2023-10-11T19:14:07.297Z", "5.26.4": "2023-10-19T08:47:25.917Z", "5.26.5": "2023-10-23T07:27:24.774Z", "5.27.0": "2023-10-26T11:49:15.569Z", "5.27.1": "2023-11-03T16:54:52.309Z", "5.27.2": "2023-11-03T20:37:18.275Z", "5.28.0": "2023-11-24T09:01:02.889Z", "5.28.1": "2023-11-27T09:49:28.995Z", "5.28.2": "2023-11-30T15:42:26.134Z", "6.0.0": "2023-12-05T08:48:04.311Z", "6.0.1": "2023-12-06T08:22:56.497Z", "6.1.0": "2023-12-20T14:01:52.231Z", "6.2.0": "2023-12-20T15:34:06.004Z", "6.2.1": "2023-12-22T09:37:21.622Z", "6.3.0": "2024-01-08T14:57:27.332Z", "6.4.0": "2024-01-19T15:01:44.494Z", "6.5.0": "2024-01-26T15:11:29.270Z", "6.6.0": "2024-02-01T09:36:39.188Z", "6.6.1": "2024-02-05T11:32:42.518Z", "5.28.3": "2024-02-05T11:40:49.458Z", "6.6.2": "2024-02-07T14:14:12.171Z", "6.7.0": "2024-03-03T17:09:18.008Z", "6.7.1": "2024-03-08T08:49:35.476Z", "6.8.0": "2024-03-13T08:45:33.330Z", "6.9.0": "2024-03-14T17:13:44.251Z", "6.10.0": "2024-03-21T11:05:41.367Z", "6.10.2": "2024-03-27T09:36:14.196Z", "6.10.1": "2024-03-27T09:50:00.588Z", "6.11.0": "2024-04-02T16:27:33.919Z", "5.28.4": "2024-04-02T16:39:45.034Z", "6.11.1": "2024-04-02T16:46:35.085Z", "6.12.0": "2024-04-08T09:47:21.640Z", "6.13.0": "2024-04-12T08:42:49.701Z", "6.14.0": "2024-04-22T15:18:24.469Z", "6.14.1": "2024-04-23T10:37:07.450Z", "6.15.0": "2024-04-29T12:07:57.862Z", "6.16.0": "2024-05-07T16:01:49.370Z", "6.16.1": "2024-05-10T09:45:29.551Z", "6.17.0": "2024-05-17T06:07:27.542Z", "6.18.0": "2024-05-20T13:00:06.220Z", "6.18.1": "2024-05-22T10:39:22.863Z", "6.18.2": "2024-05-29T13:33:23.515Z", "6.19.1": "2024-06-17T19:03:50.356Z", "6.19.2": "2024-06-18T07:24:35.327Z", "6.19.3": "2024-07-22T08:15:15.171Z", "6.19.4": "2024-07-22T13:57:54.252Z", "6.19.5": "2024-07-31T10:33:28.336Z", "6.19.6": "2024-08-09T08:38:12.331Z", "6.19.8": "2024-08-19T17:19:31.849Z", "6.20.0": "2024-10-09T07:50:38.926Z", "6.21.0": "2024-11-13T13:37:09.135Z", "7.0.0": "2024-11-28T10:50:35.140Z", "7.1.0": "2024-12-03T15:23:38.812Z", "7.1.1": "2024-12-16T17:10:12.372Z", "7.2.0": "2024-12-18T18:10:56.926Z", "7.2.1": "2025-01-09T15:06:04.828Z", "7.2.2": "2025-01-15T17:29:18.625Z", "7.2.3": "2025-01-16T09:16:10.204Z"}, "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "license": "MIT", "homepage": "https://undici.nodejs.org", "repository": {"type": "git", "url": "git+https://github.com/nodejs/undici.git"}, "description": "A stand-alone types package for Undici", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/dnlup"}, {"name": "<PERSON>", "url": "https://github.com/ethan-arrowood"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/KhafraDev"}, {"name": "<PERSON>", "url": "https://github.com/ronag"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/szmarczak"}, {"name": "<PERSON>", "url": "https://github.com/delvedor"}], "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "ethan_arrowood", "email": "<EMAIL>"}], "readme": "# undici-types\n\nThis package is a dual-publish of the [undici](https://www.npmjs.com/package/undici) library types. The `undici` package **still contains types**. This package is for users who _only_ need undici types (such as for `@types/node`). It is published alongside every release of `undici`, so you can always use the same version.\n\n- [GitHub nodejs/undici](https://github.com/nodejs/undici)\n- [Undici Documentation](https://undici.nodejs.org/#/)\n", "readmeFilename": "README.md"}