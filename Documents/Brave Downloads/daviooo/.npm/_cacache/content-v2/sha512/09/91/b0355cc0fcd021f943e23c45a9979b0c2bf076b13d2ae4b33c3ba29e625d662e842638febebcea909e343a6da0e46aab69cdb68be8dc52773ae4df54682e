{"_id": "ansi-styles", "_rev": "91-3114b91484e19ef97a5f086dc94ae824", "name": "ansi-styles", "description": "ANSI escape codes for styling strings in the terminal", "dist-tags": {"latest": "6.2.1"}, "versions": {"0.1.0": {"name": "ansi-styles", "version": "0.1.0", "description": "ANSI escape codes for colorizing strings in the terminal", "keywords": ["ansi", "styles", "color", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256"], "homepage": "https://github.com/sindresorhus/ansi-styles", "bugs": {"url": "https://github.com/sindresorhus/ansi-styles/issues"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "main": "ansi-styles.js", "repository": {"type": "git", "url": "git://github.com/sindresorhus/ansi-styles.git"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "~1.12.0"}, "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "MIT"}], "files": ["ansi-styles.js"], "_id": "ansi-styles@0.1.0", "dist": {"shasum": "af63b736c8b14c5dc94af0f3818da822527c10c8", "tarball": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-0.1.0.tgz", "integrity": "sha512-S9N78mUU/qZFid8TNkgVFh796qnswzr+ho+GYuf3/3afqUTKlGKe/qN+2j7udlrbqjsrZGkrj+UCEvrRFeStfg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDRYgVTGvHrIq7Og4UBJYQvYKR3eDSnjcRMd19ES3jM5AIhAMb68zN8K9psZcQu+4Of1EI5OuSWkVyg9NjCjob0B2Ev"}]}, "_from": ".", "_npmVersion": "1.2.32", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.1.1": {"name": "ansi-styles", "version": "0.1.1", "description": "ANSI escape codes for colorizing strings in the terminal", "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "homepage": "https://github.com/sindresorhus/ansi-styles", "bugs": {"url": "https://github.com/sindresorhus/ansi-styles/issues"}, "license": "MIT", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "files": ["ansi-styles.js"], "main": "ansi-styles", "repository": {"type": "git", "url": "git://github.com/sindresorhus/ansi-styles.git"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "~1.12.0"}, "engines": {"node": ">=0.8.0"}, "_id": "ansi-styles@0.1.1", "dist": {"shasum": "8f1618d24da7072a436f66b79c65385d7c1fe9cf", "tarball": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-0.1.1.tgz", "integrity": "sha512-vMKHQSsNruuO1fgtMI/I9Gc7jP3tXnv2QurI7L6QXZN3BmHsEhtd7TVy7qDzUpuRKaUxtpUlBrek5ZhhkBbLhQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC0aJgfyP96hNCjr5FEK4lBaTSkBo0O9QC7WOwAmz9amAiAWWN0XcTm4Wwpf9Pq/d1yOiNOHmpeNCCyodoI+e5HTJQ=="}]}, "_from": ".", "_npmVersion": "1.2.32", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.1.2": {"name": "ansi-styles", "version": "0.1.2", "description": "ANSI escape codes for colorizing strings in the terminal", "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "homepage": "https://github.com/sindresorhus/ansi-styles", "bugs": {"url": "https://github.com/sindresorhus/ansi-styles/issues"}, "license": "MIT", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "files": ["ansi-styles.js"], "main": "ansi-styles", "repository": {"type": "git", "url": "git://github.com/sindresorhus/ansi-styles.git"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "~1.12.0"}, "engines": {"node": ">=0.8.0"}, "_id": "ansi-styles@0.1.2", "dist": {"shasum": "5bab27c2e0bbe944ee42057cf23adee970abc7c6", "tarball": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-0.1.2.tgz", "integrity": "sha512-67aDATkGbpDcNIRjTtfPOGbQUepitKL7Kesl7SS72wV1FLvYEpLYXYFGcWHlhvB18uIoHZAHxmjfsckNMVckNg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAYttKopZ01JcjHhAIuitbV9MIUT+7TadkfGDghx50pyAiEA6Ike4TLijzKRDfP/zk+OZt3LdOHZJ5V8s/nZ9mTdNwU="}]}, "_from": ".", "_npmVersion": "1.2.32", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.2.0": {"name": "ansi-styles", "version": "0.2.0", "description": "ANSI escape codes for colorizing strings in the terminal", "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "homepage": "https://github.com/sindresorhus/ansi-styles", "bugs": {"url": "https://github.com/sindresorhus/ansi-styles/issues"}, "license": "MIT", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "files": ["ansi-styles.js"], "main": "ansi-styles", "repository": {"type": "git", "url": "git://github.com/sindresorhus/ansi-styles.git"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "~1.12.0"}, "engines": {"node": ">=0.8.0"}, "_id": "ansi-styles@0.2.0", "dist": {"shasum": "359ab4b15dcd64ba6d74734b72c36360a9af2c19", "tarball": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-0.2.0.tgz", "integrity": "sha512-YyQBeLj0juxUC9uUXRpQ1ZAzPT1dnsn5vVeJLHYFq4Ct1p0rymUSyvckKCXCH9I0bh3jWDIETA5nXIaZVKlDyA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDmNRO4MgROcKCKyr+cHP2poBC9LE+EWT2G0ttJPsuINAiEA0ZZl7LBgb+Gl/Swo9oTRJ0bLUVCCbEIc/jt0jTAa84o="}]}, "_from": ".", "_npmVersion": "1.2.32", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.0.0": {"name": "ansi-styles", "version": "1.0.0", "description": "ANSI escape codes for colorizing strings in the terminal", "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "homepage": "https://github.com/sindresorhus/ansi-styles", "bugs": {"url": "https://github.com/sindresorhus/ansi-styles/issues"}, "license": "MIT", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "files": ["ansi-styles.js"], "main": "ansi-styles", "repository": {"type": "git", "url": "git://github.com/sindresorhus/ansi-styles.git"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "~1.12.0"}, "engines": {"node": ">=0.8.0"}, "_id": "ansi-styles@1.0.0", "dist": {"shasum": "cb102df1c56f5123eab8b67cd7b98027a0279178", "tarball": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-1.0.0.tgz", "integrity": "sha512-3iF4FIKdxaVYT3JqQuY3Wat/T2t7TRbbQ94Fu50ZUCbLy4TFbTzr90NOHQodQkNqmeEGCw8WbeP78WNi6SKYUA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCawl5mLAJ0kVnzqZ+AxQtfHi4mOr1xRkHULZxOLezqyAIgVpf3/aQ57vPJGxSxP7Kkc/xgSuKA9y6ZEDR2QaLsVCk="}]}, "_from": ".", "_npmVersion": "1.3.15", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.1.0": {"name": "ansi-styles", "version": "1.1.0", "description": "ANSI escape codes for styling strings in the terminal", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/sindresorhus/ansi-styles"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "devDependencies": {"mocha": "*"}, "bugs": {"url": "https://github.com/sindresorhus/ansi-styles/issues"}, "homepage": "https://github.com/sindresorhus/ansi-styles", "_id": "ansi-styles@1.1.0", "_shasum": "eaecbf66cd706882760b2f4691582b8f55d7a7de", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "eaecbf66cd706882760b2f4691582b8f55d7a7de", "tarball": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-1.1.0.tgz", "integrity": "sha512-f2PKUkN5QngiSemowa6Mrk9MPCdtFiOSmibjZ+j1qhLGHHYsqZwmBMRF3IRMVXo8sybDqx2fJl2d/8OphBoWkA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEPU864OQp7DgSU9AXNpOHK4yAAWvI99UMCEgxmBElSDAiBvIO9ty4uAsAha+OtYj48TQmtWVMraNRhn1o/CWmCYPA=="}]}, "directories": {}}, "2.0.0": {"name": "ansi-styles", "version": "2.0.0", "description": "ANSI escape codes for styling strings in the terminal", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/ansi-styles"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "devDependencies": {"mocha": "*"}, "gitHead": "8c71708c951ab5ba824487c67053fb8f1eb8b6ea", "bugs": {"url": "https://github.com/sindresorhus/ansi-styles/issues"}, "homepage": "https://github.com/sindresorhus/ansi-styles", "_id": "ansi-styles@2.0.0", "_shasum": "432b26162fea1b63c878896abc8cc5548f25063e", "_from": ".", "_npmVersion": "2.1.5", "_nodeVersion": "0.10.32", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "dist": {"shasum": "432b26162fea1b63c878896abc8cc5548f25063e", "tarball": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-2.0.0.tgz", "integrity": "sha512-0kjBHdIQSa1iuh2rs8Md1GQNHAKrefcRSp2W5OKQU1oBZgCSqQ5aG4o+r69irBlhIPwA8wUaPdN/FWZVIHW7rA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAQocR+KZqHu8RDVGjRE37tTHS3T72Ek/ZIq0DGr6mHAAiEAiUrPvGxFbeLDC2FzpTnoNLj4TS/j6wVExRbIrvmhnfA="}]}, "directories": {}}, "2.0.1": {"name": "ansi-styles", "version": "2.0.1", "description": "ANSI escape codes for styling strings in the terminal", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/ansi-styles"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "devDependencies": {"mocha": "*"}, "gitHead": "da6541334e1681cb803f891fab8abf4313cc4bc1", "bugs": {"url": "https://github.com/sindresorhus/ansi-styles/issues"}, "homepage": "https://github.com/sindresorhus/ansi-styles", "_id": "ansi-styles@2.0.1", "_shasum": "b033f57f93e2d28adeb8bc11138fa13da0fd20a3", "_from": ".", "_npmVersion": "2.1.16", "_nodeVersion": "0.10.35", "_npmUser": {"name": "jbnicolai", "email": "<EMAIL>"}, "dist": {"shasum": "b033f57f93e2d28adeb8bc11138fa13da0fd20a3", "tarball": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-2.0.1.tgz", "integrity": "sha512-0zjsXMhnTibRx8YrLgLKb5NvWEcHN/OZEe1NzR8VVrEM6xr7/NyLsoMVelAhaoJhOtpuexaeRGD8MF8Z64+9LQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQChftvoE6wbxVGq0kMFSRJ57gEJivQWQot37JjthUMDTwIgQ2dCpoKN2KaHU199TIT0TwKY8iq5E+2DMhc8BCMLETc="}]}, "directories": {}}, "2.1.0": {"name": "ansi-styles", "version": "2.1.0", "description": "ANSI escape codes for styling strings in the terminal", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/chalk/ansi-styles"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "devDependencies": {"mocha": "*"}, "gitHead": "18421cbe4a2d93359ec2599a894f704be126d066", "bugs": {"url": "https://github.com/chalk/ansi-styles/issues"}, "homepage": "https://github.com/chalk/ansi-styles", "_id": "ansi-styles@2.1.0", "_shasum": "990f747146927b559a932bf92959163d60c0d0e2", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "jbnicolai", "email": "<EMAIL>"}, "dist": {"shasum": "990f747146927b559a932bf92959163d60c0d0e2", "tarball": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-2.1.0.tgz", "integrity": "sha512-qGfeDJjjwuLKKzFsdbYhnbn4Hqxkel6cSzdCP5IYmT38eIzVxGCzcOceJNkMG0sD5QMFz8SauI5Y+5lwcgHIgA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDnpdD9hdGQmkMQsctjPHrYOBsZbJlYQEQlBynDrjzttwIhAL9c10O5YhEm+f298qUg4MbjskTJqZZo3V/etfLTmKqe"}]}, "directories": {}}, "2.2.1": {"name": "ansi-styles", "version": "2.2.1", "description": "ANSI escape codes for styling strings in the terminal", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-styles.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "devDependencies": {"mocha": "*"}, "gitHead": "95c59b23be760108b6530ca1c89477c21b258032", "bugs": {"url": "https://github.com/chalk/ansi-styles/issues"}, "homepage": "https://github.com/chalk/ansi-styles#readme", "_id": "ansi-styles@2.2.1", "_shasum": "b432dd3358b634cf75e1e4664368240533c1ddbe", "_from": ".", "_npmVersion": "3.8.3", "_nodeVersion": "4.3.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "b432dd3358b634cf75e1e4664368240533c1ddbe", "tarball": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-2.2.1.tgz", "integrity": "sha512-kmCevFghRiWM7HB5zTPULl4r9bVFSWjz62MhqizDGUrq2NWuNMQyuv4tHHoKJHs69M/MF64lEcHdYIocrdWQYA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHh2prCFZwahuHtzuSo2pE6Elj1AIQNKWAWw/ytmkefPAiB7JVjhCZ4tsZHwfaY1RsB9VMESxoHT5bB3pJnXphgr1w=="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ansi-styles-2.2.1.tgz_1459197317833_0.9694824463222176"}, "directories": {}}, "3.0.0": {"name": "ansi-styles", "version": "3.0.0", "description": "ANSI escape codes for styling strings in the terminal", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-styles.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"color-convert": "^1.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}, "xo": {"esnext": true}, "gitHead": "a80f4c8d155dc6d2c90c0a790ce2dc2e60e6defb", "bugs": {"url": "https://github.com/chalk/ansi-styles/issues"}, "homepage": "https://github.com/chalk/ansi-styles#readme", "_id": "ansi-styles@3.0.0", "_shasum": "5404e93a544c4fec7f048262977bebfe3155e0c1", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "7.2.0", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "dist": {"shasum": "5404e93a544c4fec7f048262977bebfe3155e0c1", "tarball": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.0.0.tgz", "integrity": "sha512-caosO5GROQ6HZCO0PCuqFmKb0g2ow+7fvz60N3/A3ggyeDnFvLjW8mLR84eK/6hsh206bSm3s3j34bLKJPcnLw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD7venaNuAJCBsAVEI6opbc2mQTvo4N7tj851qo7NSfjQIgPAoAAOgf5TSENXsBNXu/EWbcWH/RTVxSfjjEkU0EdMo="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ansi-styles-3.0.0.tgz_1484644594587_0.7388329922687262"}, "directories": {}}, "3.1.0": {"name": "ansi-styles", "version": "3.1.0", "description": "ANSI escape codes for styling strings in the terminal", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-styles.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "maintainers": [{"name": "dthree", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"color-convert": "^1.0.0"}, "devDependencies": {"ava": "*", "babel-polyfill": "^6.23.0", "xo": "*"}, "ava": {"require": "babel-polyfill"}, "gitHead": "a8f7d0f353eca6d67bad277b6437860fbc0d007e", "bugs": {"url": "https://github.com/chalk/ansi-styles/issues"}, "homepage": "https://github.com/chalk/ansi-styles#readme", "_id": "ansi-styles@3.1.0", "_shasum": "09c202d5c917ec23188caa5c9cb9179cd9547750", "_from": ".", "_npmVersion": "4.6.1", "_nodeVersion": "8.0.0", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "dist": {"shasum": "09c202d5c917ec23188caa5c9cb9179cd9547750", "tarball": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.1.0.tgz", "integrity": "sha512-1jXmDD7l38qjk7yqmpFMLvs94InTmzcupUKdP2N4YjuDy5gNRJc1J5zb3Q/ur6FKxep0GW+2vK2qL82GEAwwCQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC3/FMTqAa8GlPUV9SltNHfSTEI+sFS7rBs7CktE8w4mgIgVq1rpKXapuJpfjcI71B0iVM0UfA9vP8CAx73YK33Cdc="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-styles-3.1.0.tgz_1497307320106_0.3296520886942744"}, "directories": {}}, "3.2.0": {"name": "ansi-styles", "version": "3.2.0", "description": "ANSI escape codes for styling strings in the terminal", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-styles.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"color-convert": "^1.9.0"}, "devDependencies": {"ava": "*", "babel-polyfill": "^6.23.0", "xo": "*"}, "ava": {"require": "babel-polyfill"}, "gitHead": "3340c4d536078a51fd7b5049e939c43c5ab05db3", "bugs": {"url": "https://github.com/chalk/ansi-styles/issues"}, "homepage": "https://github.com/chalk/ansi-styles#readme", "_id": "ansi-styles@3.2.0", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-NnSOmMEYtVR2JVMIGTzynRkkaxtiq1xnFBcdQD/DnNCYPoEPsVJhM98BDyaoNOQIi7p4okdi3E27eN7GQbsUug==", "shasum": "c159b8d5be0f9e5a6f346dab94f16ce022161b88", "tarball": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEuVVC7x7QVA3wcmy6N5R/FvCl85Kz2GXjgJ8iw0P54HAiAK+/bvLwH/Q6HoglnG5DWDc+sFxxWUBVFyGfw5f+jilw=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "qix"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-styles-3.2.0.tgz_1500809147099_0.7141686324030161"}, "directories": {}}, "3.2.1": {"name": "ansi-styles", "version": "3.2.1", "description": "ANSI escape codes for styling strings in the terminal", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-styles.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava", "screenshot": "svg-term --command='node screenshot' --out=screenshot.svg --padding=3 --width=55 --height=3 --at=1000 --no-cursor"}, "files": ["index.js"], "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"color-convert": "^1.9.0"}, "devDependencies": {"ava": "*", "babel-polyfill": "^6.23.0", "svg-term-cli": "^2.1.1", "xo": "*"}, "ava": {"require": "babel-polyfill"}, "gitHead": "de7527a86c1cf49906b0eb32a0de1402d849ccc2", "bugs": {"url": "https://github.com/chalk/ansi-styles/issues"}, "homepage": "https://github.com/chalk/ansi-styles#readme", "_id": "ansi-styles@3.2.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.4", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==", "shasum": "41fbb20243e50b12be0f04b8dedbf07520ce841d", "tarball": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz", "fileCount": 4, "unpackedSize": 9371, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCkgSQy8Rtz7ooK6YW4Ptv5DKKpVxfmHmDkkzGYJL60BgIhAKXrz0azsciFr39Mrox8mFpX1zJdBJqXh/L7EHzl6Hp9"}]}, "maintainers": [{"email": "<EMAIL>", "name": "qix"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-styles_3.2.1_1519983600652_0.7656433427334486"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "ansi-styles", "version": "4.0.0", "description": "ANSI escape codes for styling strings in the terminal", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-styles.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava", "screenshot": "svg-term --command='node screenshot' --out=screenshot.svg --padding=3 --width=55 --height=3 --at=1000 --no-cursor"}, "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"color-convert": "^2.0.0"}, "devDependencies": {"ava": "^1.4.1", "svg-term-cli": "^2.1.1", "xo": "^0.24.0"}, "gitHead": "3df6a798ff5a86fed4e93d638a0d8525056e684d", "bugs": {"url": "https://github.com/chalk/ansi-styles/issues"}, "homepage": "https://github.com/chalk/ansi-styles#readme", "_id": "ansi-styles@4.0.0", "_nodeVersion": "8.16.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-8zjUtFJ3db/QoPXuuEMloS2AUf79/yeyttJ7Abr3hteopJu9HK8vsgGviGUMq+zyA6cZZO6gAyZoMTF6TgaEjA==", "shasum": "f6b84e8fc97ea7add7a53b7530ef28f3fde0e048", "tarball": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.0.0.tgz", "fileCount": 4, "unpackedSize": 9741, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc8NNCCRA9TVsSAnZWagAAAiQP/2a4KgMz9t4UZBR03CNL\n635U+8tvDW8ihbpL5EGXWjsK4E9WWqjAkTTcP2J/eekN7qCR3MGek0Y1UFJp\nTD3M2wHb3UQrYwBTxSJ61mLR5QpvdXl3q6z1KZ1cZ364D0jWElWaYYiXhNjI\ntm8r5r6ygEbEqAv0FU6U+Dckw/k0++a6KavCa0NBqNXfqrjVNAndtjIJkARg\nwo8hmb2LiqGGqcgFRiqmESNro5+YLa/ictECH8e8RYWWFRe2UiyH8KQPQXnE\nip4ugr/ZbRxrYnbj962/Ctd1mALqeVW/mCSTP1nx70HIuBwfr9P/SjIdLbIG\nFb59gQRMlTgc4d7wmuUVyanhwuVLpZc/yTNfqbfQgDbJ9bZDoO5uKTVWzM+S\n8REx3CfRfAaJpaKjCJNrxhxZQcWdGzhMxhRhzeF3/lCvWkpv/+KyiSu/N32p\nU15+PuyJomZT8R6Kx/VJ/+dzUXCDiPsMgrTKz9+KpWOc6dRVExRKYnRgVCH2\n5Cmav2IVl4GOgM6nxeA4drZP4vR/jQdbjw4qtVbUuKXON1B1auRORDzLwub9\nZmVLXTtECJedZxWVHo0YMm8s5mnUzl+nGWPl/LMt1/MyRtxtCkh20TvV3jBo\n4s5FeioDeIKjw6nUCIraSCNQ0k73rxy/IsirJES4L3j8I8mcUrhuhn9BQmen\nXLdl\r\n=wntf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHDVmsP+iLomR9PHgNp87F1OgJgTzLkN5I6G0mi+CG+TAiBWwyB6i8uUBqTlO+DcrvRagk5SgylEoiHbWV689eETaA=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "qix"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-styles_4.0.0_1559286594221_0.9141286342597881"}, "_hasShrinkwrap": false}, "4.1.0": {"name": "ansi-styles", "version": "4.1.0", "description": "ANSI escape codes for styling strings in the terminal", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-styles.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava", "screenshot": "svg-term --command='node screenshot' --out=screenshot.svg --padding=3 --width=55 --height=3 --at=1000 --no-cursor"}, "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"color-convert": "^2.0.1"}, "devDependencies": {"ava": "^2.3.0", "svg-term-cli": "^2.1.1", "xo": "^0.24.0"}, "gitHead": "18d3055ac5dab4c210534ce525d6493430d557d9", "bugs": {"url": "https://github.com/chalk/ansi-styles/issues"}, "homepage": "https://github.com/chalk/ansi-styles#readme", "_id": "ansi-styles@4.1.0", "_nodeVersion": "10.16.0", "_npmVersion": "6.10.3", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Qts4KCLKG+waHc9C4m07weIY8qyeixoS0h6RnbsNVD6Fw+pEZGW3vTyObL3WXpE09Mq4Oi7/lBEyLmOiLtlYWQ==", "shasum": "d3ba8047b818293eaaa7978321dd61bff9842cfc", "tarball": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.1.0.tgz", "fileCount": 4, "unpackedSize": 10214, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXdVUCRA9TVsSAnZWagAAmDcP/A8BEtzFCQLnvUOFV+fH\nZXSwAW536SLJN5oq+1jToSQkG1lJA161YVCaxr0iaDxL8SbXIn9ZQ89+VBcZ\nH6hcIEku94U1++/omuF6Qh16v8DApAJZwhs4HCsrZlBkvVNT8UpPf3Rwc8tz\nwPoXaWFlm4hihJwPT8M2nuVyAnCDUQGaZBotXoYOknvl30RanjO9E9nTMtDF\nyJ/KjbETVK+5u6JESVq0KCIc3f60+lUPRzXCUYsV95+23J80/4z0fIjwgf1O\noZhHrfvp3wrrKqpaOZxXJPiySPsJ8nGQsFmgka/DWOxdKxymL25DrmhNKMNd\nFBYbRJBeDHWmISrFqpcFaBd9yf23EH9lDaU62NoJV1DPQfpZq3u/n5Cdq3Ic\nNY6sgSL2XLf6JwKU7NfZzSnmuHSc4/ln8EcmLAJewBVUPaIqFJrRxqNfoY2K\nDoIQzWOy379/LqIs1AOqw6V/jSCAttI0NZZvwUNdNAQA7qyKCN+DyW2/wC0N\n6VRfkQGAS3RJqIeiSeKd+i2joUFMrRbJz8mXB+grqKY3cqMratg8S+cpcfHv\nPeGITPGIgcmTVcP92MC2ytpVmyL0PNbzT/scBEPUYdvhyuFumuHeUK/ul1ms\naG2NMpXfU8f9w/VHzqk7dCzwHtPrz61sAxpCmS3+DUBpKz9Ip7iZZrXnbb/F\nkgmT\r\n=w4D6\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDPSCzwwFc12TT7uJHAYs4+MbNcIZjZgHEq2cmXWtiR8gIga1KkiCj+QInHbGViQlOO4TeoI9592Kr8m5TmOtOkxlw="}]}, "maintainers": [{"email": "<EMAIL>", "name": "qix"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-styles_4.1.0_1566430547434_0.3073668474671494"}, "_hasShrinkwrap": false}, "4.2.0": {"name": "ansi-styles", "version": "4.2.0", "description": "ANSI escape codes for styling strings in the terminal", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-styles.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd", "screenshot": "svg-term --command='node screenshot' --out=screenshot.svg --padding=3 --width=55 --height=3 --at=1000 --no-cursor"}, "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"@types/color-name": "^1.1.1", "color-convert": "^2.0.1"}, "devDependencies": {"@types/color-convert": "^1.9.0", "ava": "^2.3.0", "svg-term-cli": "^2.1.1", "tsd": "^0.10.0", "xo": "^0.25.3"}, "gitHead": "6d1245dee68379d261d358cb46f8f7669218b04f", "bugs": {"url": "https://github.com/chalk/ansi-styles/issues"}, "homepage": "https://github.com/chalk/ansi-styles#readme", "_id": "ansi-styles@4.2.0", "_nodeVersion": "12.13.0", "_npmVersion": "6.12.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-7kFQgnEaMdRtwf6uSfUnVr9gSGC7faurn+J/Mv90/W+iTtN0405/nLdopfMWwchyxhbGYl6TC4Sccn9TUkGAgg==", "shasum": "5681f0dcf7ae5880a7841d8831c4724ed9cc0172", "tarball": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.2.0.tgz", "fileCount": 5, "unpackedSize": 14675, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdypVlCRA9TVsSAnZWagAAngEP+wZkQrJHApsbLoyRRtDt\nF4MXBartHNDHjhH/k1n/+GhMoPVsRWvgS0BOXmfTCeR7FXDzbdWVpdmdOTJO\n6cJ/XHV95wrVsJYCbXZrSpm4if5J8VH70k14tAvxgYdkCPXySyIqrctzZYcK\nTas/e4IAMGfOVZ+LsmPsriuEJQwuc+cttgNcb7PII5Mb8mMSETRvRlF7RysB\nkXjiCtcGhLCazcSIyUiqtTVFL2C855h4q1vrRFXV86H+tI8DMdiKl92xgWDg\n7MajHwSXQF0mduwrdOpZhQVQXYcFCqvxzFNr9Y7izX6m6LqoiMKrbxZ08hGV\nJvRPJzOaMzTwiuWLA2rdsxePkCpA76qQWtxHwfVabYVdfIoCGAVMVejPt9A2\nsoVV48YcfTu9Q3AQArJ8tcZiGRgybXRS7mTbazGUnjn/VKfiK/npVcN8xzLH\njDjDgmqfokSp5JonfPYGFQQLWe7HyCC5zm/6lv8H1/kyfuBa8g78e6ZW1CBx\nBpQ7WHNxqhhbMdPsuufUp67ojMOz3G4JXpb88ewfxovSJVFf2+JPIkKeeCDJ\n1U7SvvU3hc7dkUWhMWxsjKiDnGyE0rEcfD1gVGlVVSGWhhE3hGI3Sur4VzTp\ni7Tty9qpnEIgROqXhnFT44FFWKQDDaK1t1K3HQFx6QhEfVq484Uhvjk9JNoG\nEreL\r\n=cINu\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDWPGPfokXrboheH0p6/o4aKx/R7ilwx4emHpfzRX2aegIgBkXGH6Wq2Yvx4nQHi2K6meOO+ZCZvtusQ70mSWTdAUg="}]}, "maintainers": [{"email": "<EMAIL>", "name": "qix"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-styles_4.2.0_1573557604896_0.04368984078174476"}, "_hasShrinkwrap": false}, "4.2.1": {"name": "ansi-styles", "version": "4.2.1", "description": "ANSI escape codes for styling strings in the terminal", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-styles.git"}, "funding": "https://github.com/chalk/ansi-styles?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd", "screenshot": "svg-term --command='node screenshot' --out=screenshot.svg --padding=3 --width=55 --height=3 --at=1000 --no-cursor"}, "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"@types/color-name": "^1.1.1", "color-convert": "^2.0.1"}, "devDependencies": {"@types/color-convert": "^1.9.0", "ava": "^2.3.0", "svg-term-cli": "^2.1.1", "tsd": "^0.11.0", "xo": "^0.25.3"}, "gitHead": "74d421cf32342ac6ec7b507bd903a9e1105f74d7", "bugs": {"url": "https://github.com/chalk/ansi-styles/issues"}, "homepage": "https://github.com/chalk/ansi-styles#readme", "_id": "ansi-styles@4.2.1", "_nodeVersion": "10.17.0", "_npmVersion": "6.13.4", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-9VGjrMsG1vePxcSweQsN20KY/c4zN0h9fLjqAbwbPfahM3t+NL+M9HC8xeXG2I8pX5NoamTGNuomEUFI7fcUjA==", "shasum": "90ae75c424d008d2624c5bf29ead3177ebfcf359", "tarball": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.2.1.tgz", "fileCount": 5, "unpackedSize": 14729, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeDOG3CRA9TVsSAnZWagAAszAP/2sW6WnLJ1UrghYkSdNT\nxbvlislTDp3antpvGjow1CkHkptklTWjZBMtp8zfrhAZat52Q3wKRrYUpOyK\nNw1eihJGgsSWwIcEX6jS0WBFMOBIvM9i5VRJ79Iwm5F9oVJ0z4aPQkQ+eUJb\nVYjjPQp/LukjvyzwjsZXxHFVuJ3u9k5EoX2q9QGOII7R7jE+M8pSunbuJcWK\nYDnbavJl7WeVd1+9QT5SuyUt59HO0WEZ9eoCkVO2ooLluyFbXZvi6ZDnURgN\n2Fjipox7lMUitDM0/UngYPmDNeGbtfi8zyYtCFRbyGUKQSgzXtYsD7+58Xtu\nH/3vzKbQvABRaRhX6laYiCSe7pV+pOi2rpnPoUAPjdhSkXZF4yLL0DLprrag\nCVBv02fVCagNGmKN9c1JQif7ACCPUEfz+jAClklsUpTY+OLfuYbaYPsJ0WKB\nuE1qZh8Sr9dgqBuK9ASot6BLJcf31Qv0SPmBszFAmdLmt7ozk4AxBZ4dkxpN\n4b59DDkW/yOyVWPxT7B5ZadYUaItTO+ucPp1WYuN/3j71nHEWf2Pcm0JqJTY\nBQy5KNr10nAeTsmNC7+xBllPeHeXM2mJ3GoT6UBQDfY8g0xvvmjU5jgDoQ4m\nUYVmjE06AsQXxXHxBSJzmSabLjQLxQy7E5GP70L8rfUjRSTq/JjpMMgYDfYe\nWZ1u\r\n=wfZo\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGeqlsZwnvqwtiKne8fqCS9cl7q3VmiJBUSXTNf4l1N2AiBpOVQvZCoLNQZIN0dSyLzBvVlPuwoItSiuk5lRbelyTg=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "qix"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-styles_4.2.1_1577902519016_0.16811694192100912"}, "_hasShrinkwrap": false}, "4.3.0": {"name": "ansi-styles", "version": "4.3.0", "description": "ANSI escape codes for styling strings in the terminal", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-styles.git"}, "funding": "https://github.com/chalk/ansi-styles?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd", "screenshot": "svg-term --command='node screenshot' --out=screenshot.svg --padding=3 --width=55 --height=3 --at=1000 --no-cursor"}, "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"color-convert": "^2.0.1"}, "devDependencies": {"@types/color-convert": "^1.9.0", "ava": "^2.3.0", "svg-term-cli": "^2.1.1", "tsd": "^0.11.0", "xo": "^0.25.3"}, "gitHead": "2d02d5bd070f733179007f0904eb5fb41090395a", "bugs": {"url": "https://github.com/chalk/ansi-styles/issues"}, "homepage": "https://github.com/chalk/ansi-styles#readme", "_id": "ansi-styles@4.3.0", "_nodeVersion": "14.11.0", "_npmVersion": "6.14.8", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==", "shasum": "edd803628ae71c04c85ae7a0906edad34b648937", "tarball": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz", "fileCount": 5, "unpackedSize": 16978, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfeiACCRA9TVsSAnZWagAA5ccP/0DWoQsxVaTeYJuAyJlY\nPaf8cCbWwUQ6ZqFPreR4yYM+mpnSLnSlo8zqctIMofG63LIic4Co358CXP8v\nv9kvZYChYKZHcW7mxzIBUklWHRlrfDfVJSHhFiyAFHAu2X0hw70CZaZEXSGK\n0BiutHMjskT2GCSkheHBtj9g1OICvGFAP3wx1RasvYH7qhXBVFFCC/3Ci/c3\ne58izr3ivXcnOwqyC1ZlFNmL0rsgt91JtMY/7dcm/TouDpwrlO2BVGgJbD0L\nAu+jvjQnBjbjQlxskuS8mu69VvovP3lauf6YP0oeRdX2/UqY8J65/eDc1MN3\nBBcD8S98VglpGM/RGJmpUlCaoWfoz7yN9YyJk6mV/+wOSS2KRhmaiPXws9xY\nXAVdgFrK2cp1qR9RhOhmNng02WCZ3i7oh/1Pvp1DWCgDtASz5YOlT6ikvyHG\nCvqs3m7vkkhrnaEK68soPmbG2Q3O92wwINk7tX4WjH8TyqBaDbu8Z6t9NOwU\noW/v+A1gxWlrtl5eqOaMxo7XYxiF9YZzWWfzk1t6bl1rPICtYVRx2fn/3qqR\n0VRmR3DulMxsyx7ZwW1C210MbuuiRrDMIJZsg/kvuaLWOmh1US+rHa1YrD6g\n8pmirVpcxxBp1Pj44ZjM7W4X6MaboTHHNiE6dtJdJdZrO66hQnB4Bu25w9uX\n10be\r\n=uXWi\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCiWmk7MXmf0ZVnUHbCbR2mb9eLfmcN5XoDJMyjv/VX/QIgKOsdyMm5gXdutN1mR1lRxBc/muUtoCWbdoL3I/e7TB4="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-styles_4.3.0_1601839105748_0.2212872458771633"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "ansi-styles", "version": "5.0.0", "description": "ANSI escape codes for styling strings in the terminal", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-styles.git"}, "funding": "https://github.com/chalk/ansi-styles?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava && tsd", "screenshot": "svg-term --command='node screenshot' --out=screenshot.svg --padding=3 --width=55 --height=3 --at=1000 --no-cursor"}, "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "devDependencies": {"@types/color-convert": "^1.9.0", "ava": "^2.3.0", "svg-term-cli": "^2.1.1", "tsd": "^0.11.0", "xo": "^0.25.3"}, "gitHead": "94ab248a83af375cf2179adce343df89daa25da0", "bugs": {"url": "https://github.com/chalk/ansi-styles/issues"}, "homepage": "https://github.com/chalk/ansi-styles#readme", "_id": "ansi-styles@5.0.0", "_nodeVersion": "10.22.1", "_npmVersion": "6.14.9", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-6564t0m0fuQMnockqBv7wJxo9T5C2V9JpYXyNScfRDPVLusOQQhkpMGrFC17QbiolraQ1sMXX+Y5nJpjqozL4g==", "shasum": "675dbbb5ca1908fa90abe4e5b1c2e9b1f4080d99", "tarball": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-5.0.0.tgz", "fileCount": 5, "unpackedSize": 13185, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfxbuRCRA9TVsSAnZWagAAxW4P/AhubyPwX7EZahWbqG8a\nry4Gr1H6qUSbzzLOxTLDTsxxjeKkodlzCic9zzN9qoy10mNw2bZwoOPDdMdB\n0pbSv2KDqf4BQ0bUFVcEYh9FWvAkdBi6W2rOD5qUmfl1KewnvkFt/nLrRl5K\nzZcQI26UTBEl45TwYnmU1PAMCLElGwzDPwKjT19IA9/8Zh54uipAgiB9ImQS\nUypHk2+lVOYFr4cseI6Qtx4YixKUDre+1BCGXzbBnnjyy37Ht2SUU4S/AdYT\nnuWZKU7HjKi311HuxIReLrqaZTCcPkxWgBUdbiazFGH3HQIJ5LQcMZbVi6N4\nBQU00K37u4/d/+yu0ESiKujDbR/3pgddGls9r1X6azcDuqXFUDQR2DGvc4n6\nx2HO3TIire41wxVrTwL4D7ZpSsw0fhHDqu26ppCbOAcj5NFuRidJx/NaHuKF\nZqX6Kl3VAB5bA51z7WM1FkiSewqhfM2m0MQ5kRAzCw5SSJA8s9qoDLTJ8GMW\nBu/jRoPCz6TzFfOeDx7mPfl7T0HadjfC2KtptMPwxm65sQ9W55n/BhX0w75m\ndsmd53mpT0gf5a8rRDayNsewf69eIlSDvvNt5EqUOw4RPL1xGODxwCJia/2Q\nN8JzLDFiFsvmJEVUszEuIqJCnBl4FlawWFehOvxSU1QWqzASzCNQLIQq6Ogk\naI1G\r\n=sA1I\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA5mlOoFGX2ZzUw5XhEDdB+9D6d1BJLuk/E66YhwiT1EAiAgDEaQJ3WglYhB/sU8VyYsKP3wDJZFbWvf7QvdkdRLhQ=="}]}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-styles_5.0.0_1606794128478_0.35759914215476774"}, "_hasShrinkwrap": false}, "5.1.0": {"name": "ansi-styles", "version": "5.1.0", "description": "ANSI escape codes for styling strings in the terminal", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-styles.git"}, "funding": "https://github.com/chalk/ansi-styles?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava && tsd", "screenshot": "svg-term --command='node screenshot' --out=screenshot.svg --padding=3 --width=55 --height=3 --at=1000 --no-cursor"}, "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "devDependencies": {"ava": "^2.4.0", "svg-term-cli": "^2.1.1", "tsd": "^0.14.0", "xo": "^0.37.1"}, "gitHead": "b890cf7edccf56215e5948d1cc385fa4e85a8969", "bugs": {"url": "https://github.com/chalk/ansi-styles/issues"}, "homepage": "https://github.com/chalk/ansi-styles#readme", "_id": "ansi-styles@5.1.0", "_nodeVersion": "12.20.1", "_npmVersion": "6.14.10", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-osxifZo3ar56+e8tdYreU6p8FZGciBHo5O0JoDAxMUqZuyNUb+yHEwYtJZ+Z32R459jEgtwVf1u8D7qYwU0l6w==", "shasum": "a436bcc51d81f4fab5080b2ba94242e377c41573", "tarball": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-5.1.0.tgz", "fileCount": 5, "unpackedSize": 13405, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgCuEfCRA9TVsSAnZWagAARQYP/j3jbwiknKp90fSTFkFL\nAhkP41N8y9cFUXbF4UTDj8oZ096xclr+Qg56EGV864kcp6x/IR5qTxDyLlUe\nhRzQ3ZtGECTr+XXXpULYg1c3TqJr3e7TaHy6onLsvNkgjLViKNvE2BxOxpnc\nWsQRbWF5ZcV4sURIzF7fQj6GArZsCbW8OaDdVKlBcNwNlysaeDXKEiY3cfLK\nPc3qhnXpjwP+br3OasE/YehDtj4OrXgKs5+0xx9S3nnUXfbsejG12a7oC2yZ\nEJNonvx+cbXYVHcL7WEhKp93PDreZLK3LsyDGVQusQcE78q/45sKxeeBsWtz\n9Th/s7TqRL/U8FubEotZl49zA4KPB6JAReFF9iUWxmx08f+pD1nae24T67dv\nVoGYC3c9UOWlgkccRebgGBzjqAnix3F5kz5zi9lLYrT3X3iSUfrjYN5ut/tc\nworOV2DpzmMFWolRpi2AslId4QxoAPM3VMsPfnINlPFDQkiWDBEkw8Ku/gad\np4j9FlTj/EUACT0SWADejY5ZWOI9t2Oj/8xgR6/bCXuL2G8i+9GAuE/BGFPc\njps+07LWI5tZQHrw2wSC8PzrfXJUx6Dh5U/5yG3xHRt9BecdkvyYy9htFTws\n3gIlp26SNcjb0n67RWmo+VXaQ6lhKfx0Mhowvqhl9sbyq3ocB/dm5divBmyr\nCAp4\r\n=chND\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDidn/Opgn11OR6lqhNpD+/+bEocVmfd2JOmaTrL2MUoAIhAItZXfInKrm9Im8dt+G5r6PeQvu9aa292/TBdtjrPWc9"}]}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-styles_5.1.0_1611325727047_0.2908627011479632"}, "_hasShrinkwrap": false}, "5.2.0": {"name": "ansi-styles", "version": "5.2.0", "description": "ANSI escape codes for styling strings in the terminal", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-styles.git"}, "funding": "https://github.com/chalk/ansi-styles?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava && tsd", "screenshot": "svg-term --command='node screenshot' --out=screenshot.svg --padding=3 --width=55 --height=3 --at=1000 --no-cursor"}, "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "devDependencies": {"ava": "^2.4.0", "svg-term-cli": "^2.1.1", "tsd": "^0.14.0", "xo": "^0.37.1"}, "gitHead": "4d30b90085fb5a971d7cb0f4dca0920ff4dbc955", "bugs": {"url": "https://github.com/chalk/ansi-styles/issues"}, "homepage": "https://github.com/chalk/ansi-styles#readme", "_id": "ansi-styles@5.2.0", "_nodeVersion": "12.20.1", "_npmVersion": "6.14.10", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Cxwpt2SfTzTtXcfOlzGEee8O+c+MmUgGrNiBcXnuWxuFJHe6a5Hz7qwhwe5OgaSYI0IJvkLqWX1ASG+cJOkEiA==", "shasum": "07449690ad45777d1924ac2abb2fc8895dba836b", "tarball": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-5.2.0.tgz", "fileCount": 5, "unpackedSize": 13596, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZCGXCRA9TVsSAnZWagAAIY4QAJ2NcJU7iCjOGPSivrMO\nHSYzm0/3P/LFJMFFv30TB0yX91UxgfFBRV+01o0ksBdK+wzXxYgNF7dbSqGq\n2ulEdYkdIjzrH+E+yfOZ26iArGFlvYimSBDMNq3HS4V6rKU4ZIVXbCd/J2wc\nFCq4wmsyWSp05Ea74BP/iczmC1v2OGNfGM5qrqkpfbJ6/OtvL9dyfhbtBl90\nnhwmCN0OlCvRxkZllnnDyM1nUAx+1E3U270e0R+qUayC34+DWcjVsg13Bu9M\niHkN8pOrV5TCvBGSGyDFG1IvCzrV36r7eGttAs6lRXgW5dzUclmYjnTFbgm0\n9CdgJ0ovDvrbqfvAWyr4BIGuqzEKRtIwxnZlbpvx/1b6vT7+qKMaZW6R3ETf\nmI7ppshymh8Kefs4g5EPrNTp9S4qHPwigEjD1dEy+Rikl8ptmrhkwnk3KQ8c\nwRycjc+a3a+8fF1OW58yr2BOpZ+gcYyonbi/4OWjO0k0NvrfE0+RRrf2hO+o\n6ubsA5ttHeiDvEVxDNC6DJEVOKxd/QH6afDyT4Fuc9wy61wI2ukm36Vi/RS0\n82xqllSSkMvP9RvIvqLMtcZLV8z4zRauBMEnV69WHRNEpifL4QMqatfWHS6U\n14V3Njnql9MeFcZtUtcVd7OKkxaER54rlDBWMGkQkIBhOTA+IChBooqdl2M2\nMhG8\r\n=OR+b\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAND+m0Vl7hjZGBFTXuRx5O/TtwhyoDZvIQUhTS4uy1AAiBLOZwjRapi08CEyjcH210r2J7cetSsgdiuHGG1bY1XYw=="}]}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-styles_5.2.0_1617174934592_0.8102256934313619"}, "_hasShrinkwrap": false}, "6.0.0": {"name": "ansi-styles", "version": "6.0.0", "description": "ANSI escape codes for styling strings in the terminal", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-styles.git"}, "funding": "https://github.com/chalk/ansi-styles?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd", "screenshot": "svg-term --command='node screenshot' --out=screenshot.svg --padding=3 --width=55 --height=3 --at=1000 --no-cursor"}, "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "devDependencies": {"ava": "^3.15.0", "svg-term-cli": "^2.1.1", "tsd": "^0.14.0", "xo": "^0.38.2"}, "gitHead": "b566b5ba72df29dc2896db5b4f9bcd1e40789eb2", "bugs": {"url": "https://github.com/chalk/ansi-styles/issues"}, "homepage": "https://github.com/chalk/ansi-styles#readme", "_id": "ansi-styles@6.0.0", "_nodeVersion": "14.16.1", "_npmVersion": "6.14.10", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-4nUCKPhvbhgqc4a1S0w446veDod0i6krCOMF/bPBbBq9eP11BKztPS4Mp5yxUXhr1PFEe6mYcUSAv1jfoMUuOg==", "shasum": "2dc5d239631857256fcc501cf052135f290e25b3", "tarball": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-6.0.0.tgz", "fileCount": 5, "unpackedSize": 13494, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgeSS7CRA9TVsSAnZWagAAuJMP/0tEVLWMX5qZbn8X7a4Q\nvKtHQLB8UzaNAqHF3xbdBOGUIIgQub3g+hInvAEZQVjWKBzYvq1MNiveNKWR\nsHMHAIVFQQznBsEFNKV0EflrYFgSwoXfkTVgJqBfnQKHNvelsRUpHDpPvGCu\nfSC5NN+bJCIOzZZAjqeOBntApG0RmCiinyLyJsa6X99tAaMssvWmYSgfztjQ\njwstpLdWTqu0/5qW5ibcj8Oiy4f6ywRKxLju3WB4QaABb75kstIIpRWCD3vw\naUFD+L6/jJPsJJRajpSYWiF6HYQ4rignq6yVdLeaNm8rOySQRh39Qd/3qCkQ\nvsjwuOZ5zuy5d+Ug71qqjhu8P70mhswf/cbhQigyRtCM0zgkgaCkN9BOC49D\nuT4wuzfCDuejezqg2H/ToGPwbqUtfm5p7Dmnd0CnSp0ypyvy4ph/8WFnZag9\nC+KCJCGgSSX7dvb1P9+9ayd8gY6BTE5Mdrcny9S8nQlM/XKuWJhxRI0lwGRW\nVdZdlIfcF8MyqKjl2V/EcsR1aCASMTFUm2Eaj51VGWSOCPqHwb3/epGcIc4F\ncY5y+/hBAAX80HA8md5pndff9RN2AO2x1U56I3mLx6ouKtTfTvalJYW6pjSY\n4ZcfQGyYFxbe0Ua+fYhuVgnQ2DZy5IlhyBJ8D5T0PlOogB81w1Rp9+hj1V+U\n1LxA\r\n=E3cP\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFY7x9WkaANBcYZGRYsDJOqWlY74Sa2YzsmucDlQRgh9AiBOe/B4LIsgUCeWV4FGK7bumheht1Y6J3U9z21PkvTzwQ=="}]}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-styles_6.0.0_1618551995229_0.7726601134349003"}, "_hasShrinkwrap": false}, "6.1.0": {"name": "ansi-styles", "version": "6.1.0", "description": "ANSI escape codes for styling strings in the terminal", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-styles.git"}, "funding": "https://github.com/chalk/ansi-styles?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd", "screenshot": "svg-term --command='node screenshot' --out=screenshot.svg --padding=3 --width=55 --height=3 --at=1000 --no-cursor"}, "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "devDependencies": {"ava": "^3.15.0", "svg-term-cli": "^2.1.1", "tsd": "^0.14.0", "xo": "^0.38.2"}, "gitHead": "cd0b0cb59337bfd7d3669b2d0fcde7ff661a83a6", "bugs": {"url": "https://github.com/chalk/ansi-styles/issues"}, "homepage": "https://github.com/chalk/ansi-styles#readme", "_id": "ansi-styles@6.1.0", "_nodeVersion": "12.22.1", "_npmVersion": "6.14.10", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-VbqNsoz55SYGczauuup0MFUyXNQviSpFTj1RQtFzmQLk18qbVSpTFFGMT293rmDaQuKCT6InmbuEyUne4mTuxQ==", "shasum": "87313c102b8118abd57371afab34618bf7350ed3", "tarball": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-6.1.0.tgz", "fileCount": 5, "unpackedSize": 15535, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgf+daCRA9TVsSAnZWagAAQOIP/Anx6U+vOfkg1KDr5iZJ\nmkSxedAzNH3576oI9roh7wbQSQ/fQGN/jaY0npudzvsa/NLfWuCQCt00dtQ/\nAyKY5NLtfb5VejLgIF8AF1CtJOHA88TY+aRoA+hL+eUA1PkOY9Y+1lV9U1QO\nJaUVlLQr/FIzY21pSYo6+V0lLmPGgMIs/ULdjpr2X5dZ+7Ff2DzrsQtQV2k9\nAc0G2XQRyp/NTDHCHnfOSnK9zKuFhjY2sjbNn0Gp9r3T7hTcopKqPz6FhIKY\nAUo9rqswIvAYXDEAXCoGWzJoiRsDldxTkf+mb9RV9e2jAUsGAfvJLXntizp3\n7tvMEYINoHPzs4CvRqcYonYeY7FREhTWaaNTxQzy/Ae1mARAmlIl0vXuZifP\nfglSd6wrqcz4euaOc3YRkW9eT+9q4cK7JCjyssc4vdHBMn2Yd5+A4hTn51AB\n5I9QJ8Xc0nWy712korCpcR9cPXrwve50pD2AxEinp+hyQzpPzzKyfDQHwjpe\nVbTu8/aAhDK1s19qFmUY4VtUqS1Uu8NjXpaWOlpdD4BI+dh43tsmSz61RWfJ\nhDIC39htWlyhlak1mCBQIFlGTZPr7GkEeNbZLW8MCpnfgXp7Lcv3Konzm/C+\nrsQw6XBGf4O9Tp25Q0EFyl8uaNb3wOXgSCpUydBe2DFtJWpTHGIX8L9nuVUQ\n5e9Z\r\n=m2Tr\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCA/tHV/Ykpiby73wpS1NtBNs2fJbTeFyJQgDAmuDFbsQIgHtpoqyHPZTFeQ4S3cP52dKS24bMMNntxC5FqTsPsP1c="}]}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-styles_6.1.0_1618995033909_0.3859516923978603"}, "_hasShrinkwrap": false}, "6.1.1": {"name": "ansi-styles", "version": "6.1.1", "description": "ANSI escape codes for styling strings in the terminal", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-styles.git"}, "funding": "https://github.com/chalk/ansi-styles?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd", "screenshot": "svg-term --command='node screenshot' --out=screenshot.svg --padding=3 --width=55 --height=3 --at=1000 --no-cursor"}, "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "devDependencies": {"ava": "^3.15.0", "svg-term-cli": "^2.1.1", "tsd": "^0.19.0", "xo": "^0.47.0"}, "types": "./index.d.ts", "gitHead": "6e5de6e2118b02fe117f9982a6527ddbf37dbae1", "bugs": {"url": "https://github.com/chalk/ansi-styles/issues"}, "homepage": "https://github.com/chalk/ansi-styles#readme", "_id": "ansi-styles@6.1.1", "_nodeVersion": "14.19.3", "_npmVersion": "8.3.2", "dist": {"integrity": "sha512-qDOv24WjnYuL+wbwHdlsYZFy+cgPtrYw0Tn7GLORicQp9BkQLzrgI3Pm4VyR9ERZ41YTn7KlMPuL1n05WdZvmg==", "shasum": "63cd61c72283a71cb30bd881dbb60adada74bc70", "tarball": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-6.1.1.tgz", "fileCount": 5, "unpackedSize": 15679, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD09qQhAc0ynQplZ0eozAZT7eFOJrSC4KpSlRJfWoJ3vAIgTgVVVjkWaVMo4FKfWQ9ywU86nV4CThFZBiIYtGbE+uA="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjHC8KACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqE1Q/+P2JqeZ/VegQv8ecvoDLphpJcevCLjD05Ma/se0iEVPvs6Q/O\r\nYj5IZuuSQzDdZiW9zpQOHYYG9o5kFJdogKnxWydGHs25ML6eVp8C/UWcUi+i\r\ngwYAiJX99tT/ZxZHlDQLVq+02O0u6PXyoVDL3reEjmPWVyrJkAiw6yKSOmy7\r\nnXSkFOM8STz9A12cG1mWcxkxNQXtWdDLcQ+VgZogJodt/wWkWdHUgd/eOXEL\r\nAwF7qskCYWfBgulRaKEFn4mHSouLB92xXtIfw+FAJbr4ikBBVD44S6XOmzJK\r\n/sVtfx8sVqeHS9yqh8suo5iGW7PhAuGmCoi59ZlqGX8omBirE2hV9fdvzN6q\r\nxRStRENRddNz6w1OvBRm2vm2A0jsuSFnRmQbUMlR06r9ao8xqYWanIFiVS6A\r\nMjszV3VKqqa4nWQUQQFE+41kiGkQCQwZwqJOzJ68i477uhTeJECw+jQ2pFcO\r\nbCit09pX0bj2VxbWQJLruP+3/xhmr873fHE4YVRaJw4lBUMqxNmjO4wSoQgz\r\nIxmqE7QEBK2e0HFs5m7F+myWDSnfhKA2//Q0q9oA/0+/il+MZ651Ui+l8xL0\r\nmeXXQYoMrvhtjuEhKvrNClz/xmVtzZ6q78HwCYe2e5R1bSHn+eeY3TLJz2AJ\r\nSxjob3PRLqDK+Tpxo5utkm3K0VbKhsibzWU=\r\n=H32H\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-styles_6.1.1_1662791434728_0.5451961566128818"}, "_hasShrinkwrap": false}, "6.2.0": {"name": "ansi-styles", "version": "6.2.0", "description": "ANSI escape codes for styling strings in the terminal", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-styles.git"}, "funding": "https://github.com/chalk/ansi-styles?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd", "screenshot": "svg-term --command='node screenshot' --out=screenshot.svg --padding=3 --width=55 --height=3 --at=1000 --no-cursor"}, "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "devDependencies": {"ava": "^3.15.0", "svg-term-cli": "^2.1.1", "tsd": "^0.19.0", "xo": "^0.47.0"}, "types": "./index.d.ts", "gitHead": "1e1b27d9aee505d930cdeec236ca5b75bce334b0", "bugs": {"url": "https://github.com/chalk/ansi-styles/issues"}, "homepage": "https://github.com/chalk/ansi-styles#readme", "_id": "ansi-styles@6.2.0", "_nodeVersion": "16.15.0", "_npmVersion": "8.3.2", "dist": {"integrity": "sha512-3MWBO/XxbkDtc/qpECaUwDM0DQ++ujBjdjs0ElZvChUoPv/P7GOnl3x+R2RF2My5UJHEW5R87q556MiR8U3PLw==", "shasum": "24b0517e8156e12520f389a8ddf938f337f03143", "tarball": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-6.2.0.tgz", "fileCount": 5, "unpackedSize": 17512, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFjoRsK356fzCvNPXkmv39rc5r+CEythKQrQxZbRD7SyAiA8xnnnyrMSMYCd8Ir6FzcTqfnsOEelJd/tU6IVdv6W2Q=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRS8NACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmphig/+K5gufRzRWjkQj27laiMRkJb6McuOn3m/IptsxjVy1Sq0Yg7R\r\nrN8Kj8a5pfud2sLvn50oQru8vfm9Gca90FvI7QykIxESZ5kZLiSJ2PTWUwpt\r\nIuY0sWrEno3HTLrBa7OTnQDCXX5uHJC6UdkxRchsIA558gX2cn7VvPJ9pjCL\r\ngSDyGp61ooZHjoYfLq7tf6fApwvxTbTtyvP+aqwQtgdzbM8XgQEK36HgG0Th\r\nUdcXXWiJzBX6qwBv4Qymcuzqks70yk0RFI3b6i2O5jUcIZ88nB1mnh14To6Y\r\nVjzWPEhuK3cOb9XXBw4TyF3gVjr+xhZTTj2ooZOyoOLSSifT0tW7mWeMY2HR\r\nAPVj9mGJZbtMTExo0TMFfL6REKyJ9GHmKoKBH9vYVSt/Ha8U8NIfEU9KNrpM\r\n2D0W6geO67mRQbcIViAWX6LqGyMYs+U3zv9poFmu28IRawcVSDrcEYoRMd+W\r\nFKxqmDeDSp6W/DVUHAviy+LRJQc8qf7Q/ckN8bX2nYRQWsBJo72VjZfLEuAi\r\ngB785G+v95us7GdrcRZOiDTRs3J31IPUnEVgcZAD/8gLdCxtFZs9Yrx/uev4\r\nXHLRw/XjSxpTJXNcHGrxzRT6F9g4skHRYox0eWyX6F+JrNuEmAgFLod5tyY7\r\nfVNRpt7hGDhuzHoNBJoy5KmksmI8oCtxH7w=\r\n=1iC4\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-styles_6.2.0_1665478413588_0.681402661250216"}, "_hasShrinkwrap": false}, "6.2.1": {"name": "ansi-styles", "version": "6.2.1", "description": "ANSI escape codes for styling strings in the terminal", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-styles.git"}, "funding": "https://github.com/chalk/ansi-styles?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd", "screenshot": "svg-term --command='node screenshot' --out=screenshot.svg --padding=3 --width=55 --height=3 --at=1000 --no-cursor"}, "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "devDependencies": {"ava": "^3.15.0", "svg-term-cli": "^2.1.1", "tsd": "^0.19.0", "xo": "^0.47.0"}, "types": "./index.d.ts", "gitHead": "c325165daa029dad038c12118d2bf20aa4cf3a77", "bugs": {"url": "https://github.com/chalk/ansi-styles/issues"}, "homepage": "https://github.com/chalk/ansi-styles#readme", "_id": "ansi-styles@6.2.1", "_nodeVersion": "16.16.0", "_npmVersion": "8.3.2", "dist": {"integrity": "sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==", "shasum": "0e62320cf99c21afff3b3012192546aacbfb05c5", "tarball": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-6.2.1.tgz", "fileCount": 5, "unpackedSize": 17512, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFG8Ismuz7QceJds84ad7aPzrkac5X2fz77O03EWGd2NAiEAjEl3Glc9eJKUdpAivBYekFwYpFD11EcZpMgchlpobk8="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRux+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr5ig//VjdkOryvuCnDcYpJhihW65JHJVw6Ppc/93gfYkqRDvVEkENx\r\nMTCPrrxwTB59Ksos12hrIy+Wk7/w5HGxiA/p/hoxSmf44eePej4L8VUDgkEx\r\nlrYW4B9i/pNIvcZcX27FoOALXq2/PiZPAWne+n61Tzg9MP2mQvMpc6UGRr0/\r\nMJ7XyuS3n5T7Sw6IknuH4Ts1Q+rboQWXo3/gYdnLUubh979f9nBs7PMpRxPo\r\ngIAcbrer2MfXEkaHwYu7GV2xiaCk9DZNPFM4GCucaV8bm1AP/QnLlu8/ATzv\r\nFpprkNnvwb4cMTzbTf8Q0hgURPpwtUIxci7ROegqRKC5F9k4OCsy8NgfFgkR\r\nrUpT3bzhe4DnUVCQS58hE5WweFqmmcJBTa4XGyaOtov0cyARkaVt/f6MIeup\r\nvFAXEphN0m+OHOLt7DQan3Mi0OMRig+CsQxK93EQz2QGC4rlMjzEC2x0UFC6\r\ntJOGB0vAwi+0vCSuHg/rxTqV+5QuJ916+4ASaTvhgtAP5+Y6hDTBgRFBPzae\r\nEj2gUVX4Jn9jdFp17PmLjdFj3DZjRfiE6yFTi06Egn63eS29wFxXI0rTfCwt\r\ndyGJkB/Vzdw/b7eLixIsOPS5ibTxrfkfzCyjZOJxOVPbUOBbGEG9WtPoZpDJ\r\noLxKumt3S+KsvopOSRGnjzHnk7Y9bYF5iew=\r\n=3p57\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-styles_6.2.1_1665592446400_0.005396651822920928"}, "_hasShrinkwrap": false}}, "readme": "# ansi-styles\n\n> [ANSI escape codes](https://en.wikipedia.org/wiki/ANSI_escape_code#Colors_and_Styles) for styling strings in the terminal\n\nYou probably want the higher-level [chalk](https://github.com/chalk/chalk) module for styling your strings.\n\n![](screenshot.png)\n\n## Install\n\n```sh\nnpm install ansi-styles\n```\n\n## Usage\n\n```js\nimport styles from 'ansi-styles';\n\nconsole.log(`${styles.green.open}Hello world!${styles.green.close}`);\n\n\n// Color conversion between 256/truecolor\n// NOTE: When converting from truecolor to 256 colors, the original color\n//       may be degraded to fit the new color palette. This means terminals\n//       that do not support 16 million colors will best-match the\n//       original color.\nconsole.log(`${styles.color.ansi(styles.rgbToAnsi(199, 20, 250))}Hello World${styles.color.close}`)\nconsole.log(`${styles.color.ansi256(styles.rgbToAnsi256(199, 20, 250))}Hello World${styles.color.close}`)\nconsole.log(`${styles.color.ansi16m(...styles.hexToRgb('#abcdef'))}Hello World${styles.color.close}`)\n```\n\n## API\n\n### `open` and `close`\n\nEach style has an `open` and `close` property.\n\n### `modifierNames`, `foregroundColorNames`, `backgroundColorNames`, and `colorNames`\n\nAll supported style strings are exposed as an array of strings for convenience. `colorNames` is the combination of `foregroundColorNames` and `backgroundColorNames`.\n\nThis can be useful if you need to validate input:\n\n```js\nimport {modifierNames, foregroundColorNames} from 'ansi-styles';\n\nconsole.log(modifierNames.includes('bold'));\n//=> true\n\nconsole.log(foregroundColorNames.includes('pink'));\n//=> false\n```\n\n## Styles\n\n### Modifiers\n\n- `reset`\n- `bold`\n- `dim`\n- `italic` *(Not widely supported)*\n- `underline`\n- `overline` *Supported on VTE-based terminals, the GNOME terminal, mintty, and Git Bash.*\n- `inverse`\n- `hidden`\n- `strikethrough` *(Not widely supported)*\n\n### Colors\n\n- `black`\n- `red`\n- `green`\n- `yellow`\n- `blue`\n- `magenta`\n- `cyan`\n- `white`\n- `blackBright` (alias: `gray`, `grey`)\n- `redBright`\n- `greenBright`\n- `yellowBright`\n- `blueBright`\n- `magentaBright`\n- `cyanBright`\n- `whiteBright`\n\n### Background colors\n\n- `bgBlack`\n- `bgRed`\n- `bgGreen`\n- `bgYellow`\n- `bgBlue`\n- `bgMagenta`\n- `bgCyan`\n- `bgWhite`\n- `bgBlackBright` (alias: `bgGray`, `bgGrey`)\n- `bgRedBright`\n- `bgGreenBright`\n- `bgYellowBright`\n- `bgBlueBright`\n- `bgMagentaBright`\n- `bgCyanBright`\n- `bgWhiteBright`\n\n## Advanced usage\n\nBy default, you get a map of styles, but the styles are also available as groups. They are non-enumerable so they don't show up unless you access them explicitly. This makes it easier to expose only a subset in a higher-level module.\n\n- `styles.modifier`\n- `styles.color`\n- `styles.bgColor`\n\n###### Example\n\n```js\nimport styles from 'ansi-styles';\n\nconsole.log(styles.color.green.open);\n```\n\nRaw escape codes (i.e. without the CSI escape prefix `\\u001B[` and render mode postfix `m`) are available under `styles.codes`, which returns a `Map` with the open codes as keys and close codes as values.\n\n###### Example\n\n```js\nimport styles from 'ansi-styles';\n\nconsole.log(styles.codes.get(36));\n//=> 39\n```\n\n## 16 / 256 / 16 million (TrueColor) support\n\n`ansi-styles` allows converting between various color formats and ANSI escapes, with support for 16, 256 and [16 million colors](https://gist.github.com/XVilka/8346728).\n\nThe following color spaces are supported:\n\n- `rgb`\n- `hex`\n- `ansi256`\n- `ansi`\n\nTo use these, call the associated conversion function with the intended output, for example:\n\n```js\nimport styles from 'ansi-styles';\n\nstyles.color.ansi(styles.rgbToAnsi(100, 200, 15)); // RGB to 16 color ansi foreground code\nstyles.bgColor.ansi(styles.hexToAnsi('#C0FFEE')); // HEX to 16 color ansi foreground code\n\nstyles.color.ansi256(styles.rgbToAnsi256(100, 200, 15)); // RGB to 256 color ansi foreground code\nstyles.bgColor.ansi256(styles.hexToAnsi256('#C0FFEE')); // HEX to 256 color ansi foreground code\n\nstyles.color.ansi16m(100, 200, 15); // RGB to 16 million color foreground code\nstyles.bgColor.ansi16m(...styles.hexToRgb('#C0FFEE')); // Hex (RGB) to 16 million color foreground code\n```\n\n## Related\n\n- [ansi-escapes](https://github.com/sindresorhus/ansi-escapes) - ANSI escape codes for manipulating the terminal\n\n## Maintainers\n\n- [Sindre Sorhus](https://github.com/sindresorhus)\n- [Josh Junon](https://github.com/qix-)\n\n## For enterprise\n\nAvailable as part of the Tidelift Subscription.\n\nThe maintainers of `ansi-styles` and thousands of other packages are working with Tidelift to deliver commercial support and maintenance for the open source dependencies you use to build your applications. Save time, reduce risk, and improve code health, while paying the maintainers of the exact dependencies you use. [Learn more.](https://tidelift.com/subscription/pkg/npm-ansi-styles?utm_source=npm-ansi-styles&utm_medium=referral&utm_campaign=enterprise&utm_term=repo)\n", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "time": {"modified": "2023-05-10T18:27:53.474Z", "created": "2013-07-31T23:00:29.145Z", "0.1.0": "2013-07-31T23:00:32.552Z", "0.1.1": "2013-08-03T00:31:59.821Z", "0.1.2": "2013-08-03T01:38:52.766Z", "0.2.0": "2013-08-03T16:40:58.340Z", "1.0.0": "2013-12-08T00:00:09.315Z", "1.1.0": "2014-06-03T23:35:17.884Z", "2.0.0": "2014-11-23T11:52:58.607Z", "2.0.1": "2015-02-22T09:21:36.085Z", "2.1.0": "2015-07-01T13:26:31.005Z", "2.2.0": "2016-02-21T12:27:55.984Z", "2.2.1": "2016-03-28T20:35:18.267Z", "3.0.0": "2017-01-17T09:16:34.807Z", "3.1.0": "2017-06-12T22:42:00.220Z", "3.2.0": "2017-07-23T11:25:48.038Z", "3.2.1": "2018-03-02T09:40:00.702Z", "4.0.0": "2019-05-31T07:09:54.329Z", "4.1.0": "2019-08-21T23:35:47.551Z", "4.2.0": "2019-11-12T11:20:05.018Z", "4.2.1": "2020-01-01T18:15:19.134Z", "4.3.0": "2020-10-04T19:18:25.986Z", "5.0.0": "2020-12-01T03:42:08.629Z", "5.1.0": "2021-01-22T14:28:47.169Z", "5.2.0": "2021-03-31T07:15:34.766Z", "6.0.0": "2021-04-16T05:46:35.357Z", "6.1.0": "2021-04-21T08:50:34.094Z", "6.1.1": "2022-09-10T06:30:34.873Z", "6.2.0": "2022-10-11T08:53:33.797Z", "6.2.1": "2022-10-12T16:34:06.647Z"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-styles.git"}, "users": {"passy": true, "tunnckocore": true, "gliviu": true, "operandom": true, "scottfreecode": true, "michalskuza": true, "usex": true, "houzhanfeng": true, "danielheene": true, "willwolffmyren": true, "denmasgeo": true, "flumpus-dev": true}, "homepage": "https://github.com/chalk/ansi-styles#readme", "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "bugs": {"url": "https://github.com/chalk/ansi-styles/issues"}, "license": "MIT", "readmeFilename": "readme.md"}