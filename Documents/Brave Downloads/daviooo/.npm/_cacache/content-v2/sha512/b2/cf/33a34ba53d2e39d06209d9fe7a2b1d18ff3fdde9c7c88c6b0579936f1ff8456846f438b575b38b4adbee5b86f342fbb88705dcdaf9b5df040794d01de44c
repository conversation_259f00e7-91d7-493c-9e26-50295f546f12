{"_id": "process", "_rev": "57-5d6379bb649778392722be9c35e1384d", "name": "process", "description": "process information for node.js and browsers", "dist-tags": {"latest": "0.11.10"}, "versions": {"0.4.9": {"author": {"name": "AJ <PERSON>", "email": "<EMAIL>", "url": "http://coolaj86.info"}, "name": "process", "description": "aliases `window` as `global` and adds `process`", "keywords": ["ender", "global", "process"], "version": "0.4.9", "repository": {"type": "git", "url": "git://github.com/coolaj86/nodejs-libs-4-browser.git"}, "main": "./process.js", "directories": {"lib": "."}, "engines": {"node": ">= 0.2.0", "ender": ">= 0.5.0"}, "dependencies": {}, "devDependencies": {}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/process/0.4.9/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "process@0.4.9", "_engineSupported": true, "_npmVersion": "1.0.15", "_nodeVersion": "v0.4.8", "_defaultsLoaded": true, "dist": {"shasum": "42adda3c6c577ea0c9763fb52698f5702b40c056", "tarball": "https://registry.npmjs.org/process/-/process-0.4.9.tgz", "integrity": "sha512-EMCQT2eba5zMs/LqRi4Tr/IGvfnn7f2N3brGimtOMnb1lDn8uBG4t1KjyRSYxCj0Xdc+ojew3Fh+z0E3PP0cLQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCGddSzqih+k17DD3tmUHDr2G4TG5skCWWGbsOeooR3OQIhAP3I8QBgNMgwquHG+T/N5dF+7I0xeym8Ub6TePCYaffK"}]}, "scripts": {}}, "0.5.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "process", "description": "process information for node.js and browsers", "keywords": ["process"], "version": "0.5.0", "repository": {"type": "git", "url": "git://github.com/shtylman/node-process.git"}, "browser": "./browser.js", "main": "./index.js", "engines": {"node": ">= 0.6.0"}, "_id": "process@0.5.0", "dist": {"shasum": "f82e05372efa5035715da6622d22f0a06b6f053e", "tarball": "https://registry.npmjs.org/process/-/process-0.5.0.tgz", "integrity": "sha512-keggeARIyaZK2e/EzBc3wiYR1dJuHRnTzBC+Y13Z2xEuKfTehFbWwm+FihyNL4eucO1kktW3elFCKgw4W7v/gg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQChrw7jFsfdNGjoIfaZnUP5t2lWbq7FvQQXZQ3j2kQVMQIhAIom6bTslGfSZPLAuBqNyAJCZssoJASARJO9VUSDdP8a"}]}, "_npmVersion": "1.1.70", "_npmUser": {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "coolaj86", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.5.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "process", "description": "process information for node.js and browsers", "keywords": ["process"], "version": "0.5.1", "repository": {"type": "git", "url": "git://github.com/shtylman/node-process.git"}, "browser": "./browser.js", "browserify": "./browser.js", "main": "./index.js", "engines": {"node": ">= 0.6.0"}, "_id": "process@0.5.1", "dist": {"shasum": "f9af36059dfe99556bd43fd220aabb617c1ef4a3", "tarball": "https://registry.npmjs.org/process/-/process-0.5.1.tgz", "integrity": "sha512-7YX6p/0IJ1OWazNET3o1u7tsLHDOD4obyScX5Ukcbl5aFeABEG2mye0dVh1uEOZvZ+YifYYZ9//KIr8eixId3A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF+AcgZY52PM8JgLtKZIgi/fqkMLIHWZXVdiu12kINMMAiEApkzCUW9CjJGvWTRI4tOAYH82P42CvuPo4/GiHNu/23o="}]}, "_from": ".", "_npmVersion": "1.2.3", "_npmUser": {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "coolaj86", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.5.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "process", "description": "process information for node.js and browsers", "keywords": ["process"], "version": "0.5.2", "repository": {"type": "git", "url": "git://github.com/shtylman/node-process.git"}, "browser": "./browser.js", "main": "./index.js", "engines": {"node": ">= 0.6.0"}, "bugs": {"url": "https://github.com/shtylman/node-process/issues"}, "homepage": "https://github.com/shtylman/node-process", "_id": "process@0.5.2", "dist": {"shasum": "1638d8a8e34c2f440a91db95ab9aeb677fc185cf", "tarball": "https://registry.npmjs.org/process/-/process-0.5.2.tgz", "integrity": "sha512-oNpcutj+nYX2FjdEW7PGltWhXulAnFlM0My/k48L90hARCOJtvBbQXc/6itV2jDvU5xAAtonP+r6wmQgCcbAUA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCujBrgMqnTqXl5qOYnwv4ClaI8V3P15fGf1GeXNU1gfgIgLqJD0dANu/hSXr3fpNXuHmyBqTlZ+mEqPJgJwobauHY="}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.6.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "process", "description": "process information for node.js and browsers", "keywords": ["process"], "version": "0.6.0", "repository": {"type": "git", "url": "git://github.com/shtylman/node-process.git"}, "browser": "./browser.js", "main": "./index.js", "engines": {"node": ">= 0.6.0"}, "bugs": {"url": "https://github.com/shtylman/node-process/issues"}, "homepage": "https://github.com/shtylman/node-process", "_id": "process@0.6.0", "dist": {"shasum": "7dd9be80ffaaedd4cb628f1827f1cbab6dc0918f", "tarball": "https://registry.npmjs.org/process/-/process-0.6.0.tgz", "integrity": "sha512-wKdRDRIisD/dfTBK678QFFEwi1oI5Q8U4JCu4lJSRZn7QlTilsXXlE/JytTY5xA8bAIADkwXaU8Vt6zE8ClzVw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDNc06MAOsyDMSMtmytCkVFEJmhYgZ0VB13gjRNXLgdrAIgeYaWC/Uog7xbriDyc95Gw2+f5eiJBjbbUEg2BO/Pzgk="}]}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.7.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "process", "description": "process information for node.js and browsers", "keywords": ["process"], "version": "0.7.0", "repository": {"type": "git", "url": "git://github.com/shtylman/node-process.git"}, "browser": "./browser.js", "main": "./index.js", "engines": {"node": ">= 0.6.0"}, "bugs": {"url": "https://github.com/shtylman/node-process/issues"}, "homepage": "https://github.com/shtylman/node-process", "_id": "process@0.7.0", "dist": {"shasum": "c52208161a34adf3812344ae85d3e6150469389d", "tarball": "https://registry.npmjs.org/process/-/process-0.7.0.tgz", "integrity": "sha512-zJYE4ZXy79hFghxwR6iYQfa6u6hU/790qdv0QKnU5RhUYYDmX0XwPGwGUARR4JGZcIiidlh3q+rjqUNEDlg7nw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDCMsnsnUrl96H9Kr3iJtWa0enkPzS1Xybnr/E6UKIDegIgTRdBasVropNNwzZh3D2JM8eFr6lFVmLguqSQsAa5F9k="}]}, "_from": ".", "_npmVersion": "1.4.6", "_npmUser": {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "coolaj86", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.8.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "process", "description": "process information for node.js and browsers", "keywords": ["process"], "version": "0.8.0", "repository": {"type": "git", "url": "git://github.com/shtylman/node-process.git"}, "browser": "./browser.js", "main": "./index.js", "engines": {"node": ">= 0.6.0"}, "gitHead": "67e233fe6cd92869dfdf3ed305e969c4bc803ef9", "bugs": {"url": "https://github.com/shtylman/node-process/issues"}, "homepage": "https://github.com/shtylman/node-process", "_id": "process@0.8.0", "scripts": {}, "_shasum": "7bbaf7187fe6ded3fd5be0cb6103fba9cacb9798", "_from": ".", "_npmVersion": "1.4.23", "_npmUser": {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "coolaj86", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "7bbaf7187fe6ded3fd5be0cb6103fba9cacb9798", "tarball": "https://registry.npmjs.org/process/-/process-0.8.0.tgz", "integrity": "sha512-g7Lv2/7sZaS5oNElebRqsd40W/k0QCTmq8WeNZ/w3PVTqRsz0giER3sMsFQXobi+/Gmuy/qbnksrh76o21DEDA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBmwrNTgOgmmCvJox4Tjx7omnsE8qhTwNbyoN0IVJxezAiBj/q84cslqU+xW/wYsSvreZmesvgubtjswTwFPwZQJTA=="}]}, "directories": {}}, "0.9.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "process", "description": "process information for node.js and browsers", "keywords": ["process"], "version": "0.9.0", "repository": {"type": "git", "url": "git://github.com/shtylman/node-process.git"}, "browser": "./browser.js", "main": "./index.js", "engines": {"node": ">= 0.6.0"}, "gitHead": "79c897b5bf244125266353310fb30ca2863f8602", "bugs": {"url": "https://github.com/shtylman/node-process/issues"}, "homepage": "https://github.com/shtylman/node-process", "_id": "process@0.9.0", "scripts": {}, "_shasum": "47d6a6acf51b879428c3269608992c7c5a790be4", "_from": ".", "_npmVersion": "1.4.23", "_npmUser": {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "coolaj86", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "47d6a6acf51b879428c3269608992c7c5a790be4", "tarball": "https://registry.npmjs.org/process/-/process-0.9.0.tgz", "integrity": "sha512-aw6IEbtBfEepPhZlz2s3ZP94bjri8JE8sStWcB03iNdtBbGkOaHWj2yftfFJhACB0c33IrMFJlkcSzhJLWDdow==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD51POjXNYCCmC6t48o4NOxXm1vzshfaoU1efT+m9FdLwIhALDm24mGMQ1tBkRDI92VKjG3aQtZXwTcjAY/jjfmqEX7"}]}, "directories": {}}, "0.10.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "process", "description": "process information for node.js and browsers", "keywords": ["process"], "version": "0.10.0", "repository": {"type": "git", "url": "git://github.com/shtylman/node-process.git"}, "browser": "./browser.js", "main": "./index.js", "engines": {"node": ">= 0.6.0"}, "gitHead": "181030e3bd47dbc3fc1914b5635ff6242e8e2e5b", "bugs": {"url": "https://github.com/shtylman/node-process/issues"}, "homepage": "https://github.com/shtylman/node-process", "_id": "process@0.10.0", "scripts": {}, "_shasum": "99b375aaab5c0d3bbb59f774edc69df574da8dd4", "_from": ".", "_npmVersion": "2.1.9", "_nodeVersion": "0.10.33", "_npmUser": {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "coolaj86", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "99b375aaab5c0d3bbb59f774edc69df574da8dd4", "tarball": "https://registry.npmjs.org/process/-/process-0.10.0.tgz", "integrity": "sha512-ZXMCPON5RS0v/GNcj3qClErdtvph9174f/eHTiW0JuFYfjXowEn5UxAveX09cl2iz3pYPxw5T6RbxAksN5TFxg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDKnyETCTmxHxZjYXo+y6OkVqNHTa2gysrqGlmCrwJkvAiEAjFhMMmUK0voHiVslSM6ZFrXr6uDLeCvRJ+l3jrC5xVw="}]}, "directories": {}}, "0.10.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "process", "description": "process information for node.js and browsers", "keywords": ["process"], "version": "0.10.1", "repository": {"type": "git", "url": "git://github.com/shtylman/node-process.git"}, "browser": "./browser.js", "main": "./index.js", "engines": {"node": ">= 0.6.0"}, "gitHead": "b723f82028b12e94abfe732e9f432a3fb2b5cece", "bugs": {"url": "https://github.com/shtylman/node-process/issues"}, "homepage": "https://github.com/shtylman/node-process", "_id": "process@0.10.1", "scripts": {}, "_shasum": "842457cc51cfed72dc775afeeafb8c6034372725", "_from": ".", "_npmVersion": "2.6.1", "_nodeVersion": "0.10.35", "_npmUser": {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "coolaj86", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "842457cc51cfed72dc775afeeafb8c6034372725", "tarball": "https://registry.npmjs.org/process/-/process-0.10.1.tgz", "integrity": "sha512-dyIett8dgGIZ/TXKUzeYExt7WA6ldDzys9vTDU/cCA9L17Ypme+KzS+NjQCjpn9xsvi/shbMC+yP/BcFMBz0NA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDRCqOBX081OB95BAcY0qknv81ak1F+PifWo9kzsH9UugIgb4AMbbgLtQL1bw1SE9NE4mPnUBu9fQ4JShF0f67cExQ="}]}, "directories": {}}, "0.11.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "process", "description": "process information for node.js and browsers", "keywords": ["process"], "scripts": {"test": "mocha test.js"}, "version": "0.11.0", "repository": {"type": "git", "url": "git://github.com/shtylman/node-process.git"}, "browser": "./browser.js", "main": "./index.js", "engines": {"node": ">= 0.6.0"}, "devDependencies": {"mocha": "2.2.1"}, "gitHead": "8ed5a16c8b9ecb1cf8e32727ce36ed69ee4cee44", "bugs": {"url": "https://github.com/shtylman/node-process/issues"}, "homepage": "https://github.com/shtylman/node-process", "_id": "process@0.11.0", "_shasum": "3eaaece62d56719a25bfa4198ca74c27b25960f3", "_from": ".", "_npmVersion": "2.7.5", "_nodeVersion": "0.10.35", "_npmUser": {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "coolaj86", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "3eaaece62d56719a25bfa4198ca74c27b25960f3", "tarball": "https://registry.npmjs.org/process/-/process-0.11.0.tgz", "integrity": "sha512-yxmHTM9xoW1MjZhnDOQhYJq1Y3tMB8G52XmQyV6GwZC2d+io4rA5lPBdYW5xmWlzuodrrAuL3alwzDA5YtjKbA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIATDNsVyFWk6OJip7TqTFh9+1xTl6xNBXIcFuc7pOxQCAiB237bNighs5Ww3ajiLUR3+qU7C2oL09DaL9Fmw6P3afw=="}]}, "directories": {}}, "0.11.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "process", "description": "process information for node.js and browsers", "keywords": ["process"], "scripts": {"test": "mocha test.js"}, "version": "0.11.1", "repository": {"type": "git", "url": "git://github.com/shtylman/node-process.git"}, "browser": "./browser.js", "main": "./index.js", "engines": {"node": ">= 0.6.0"}, "devDependencies": {"mocha": "2.2.1"}, "gitHead": "2d0290765eef8f977298a8313460c85d3964d84b", "bugs": {"url": "https://github.com/shtylman/node-process/issues"}, "homepage": "https://github.com/shtylman/node-process#readme", "_id": "process@0.11.1", "_shasum": "ee3becb4335edf3c8b14ccbf7e17a1125a4431ae", "_from": ".", "_npmVersion": "2.9.0", "_nodeVersion": "0.10.35", "_npmUser": {"name": "defunctzombie", "email": "<EMAIL>"}, "dist": {"shasum": "ee3becb4335edf3c8b14ccbf7e17a1125a4431ae", "tarball": "https://registry.npmjs.org/process/-/process-0.11.1.tgz", "integrity": "sha512-THKIWRYs/ywetZ9VghVzYxKwQbJqtNgGp4ybb82OvjotbzFvOY33GJKkoHFEAlrlIoStCxr8VgKlMVmd16XIew==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFuD79aO+E3hnWIafo+K98zRtKP1T5MF5NBqrPPJa0faAiEAs2cH1ipI70yI5mHjQTE/n750+ParlUhg8aigU84S7gY="}]}, "maintainers": [{"name": "coolaj86", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "directories": {}}, "0.11.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "process", "description": "process information for node.js and browsers", "keywords": ["process"], "scripts": {"test": "mocha test.js"}, "version": "0.11.2", "repository": {"type": "git", "url": "git://github.com/shtylman/node-process.git"}, "license": "MIT", "browser": "./browser.js", "main": "./index.js", "engines": {"node": ">= 0.6.0"}, "devDependencies": {"mocha": "2.2.1"}, "gitHead": "4b996b1e3281aa38d46294f33b9772771f53272d", "bugs": {"url": "https://github.com/shtylman/node-process/issues"}, "homepage": "https://github.com/shtylman/node-process#readme", "_id": "process@0.11.2", "_shasum": "8a58d1d12c573f3f890da9848a4fe8e16ca977b2", "_from": ".", "_npmVersion": "2.11.1", "_nodeVersion": "0.10.38", "_npmUser": {"name": "defunctzombie", "email": "<EMAIL>"}, "dist": {"shasum": "8a58d1d12c573f3f890da9848a4fe8e16ca977b2", "tarball": "https://registry.npmjs.org/process/-/process-0.11.2.tgz", "integrity": "sha512-GvtEeUY1yNkdFNDcWO1W+KvvGQLz497wdlmLtcw6tJueZsSZOHByGlxWZFOLJlS1tMigHCEByRWmUFa0c3QWUg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDgr5O3IXumkF4t332s+QM+3HEw08RuatAUmdQT/uAyigIhAISxE/LaH4BGd4kTA6nDmFo7t5Ek/NJY5CbwSeFs+swG"}]}, "maintainers": [{"name": "coolaj86", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "directories": {}}, "0.11.3": {"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "process", "description": "process information for node.js and browsers", "keywords": ["process"], "scripts": {"test": "mocha test.js"}, "version": "0.11.3", "repository": {"type": "git", "url": "git://github.com/shtylman/node-process.git"}, "license": "MIT", "browser": "./browser.js", "main": "./index.js", "engines": {"node": ">= 0.6.0"}, "devDependencies": {"mocha": "2.2.1"}, "gitHead": "09adde8cb8bd9c61097c5742cd870d4c4708a223", "bugs": {"url": "https://github.com/shtylman/node-process/issues"}, "homepage": "https://github.com/shtylman/node-process#readme", "_id": "process@0.11.3", "_shasum": "d7d8fb7b3db3c0bfa9658b0dd9a4fa11e691ef3a", "_from": ".", "_npmVersion": "3.8.8", "_nodeVersion": "5.5.0", "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "dist": {"shasum": "d7d8fb7b3db3c0bfa9658b0dd9a4fa11e691ef3a", "tarball": "https://registry.npmjs.org/process/-/process-0.11.3.tgz", "integrity": "sha512-mSi+e5IWFFpzLpzHKddzCjTBl5gRsdP2/EA7n/pYsjGzsjRyPfKiRiavb3faHC1CYgiR0hrLF+xr76GwXuUlhQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDW8zIXXks6AHLun1pk7vl5mXP/9R/I8u8N4mMAd2cO9QIhANi0F6K9qipExV3KZIiDx9sGDtDFVJA7e/xs1phUIh0b"}]}, "maintainers": [{"name": "coolaj86", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/process-0.11.3.tgz_1462649988786_0.6088946990203112"}, "directories": {}}, "0.11.4": {"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "process", "description": "process information for node.js and browsers", "keywords": ["process"], "scripts": {"test": "mocha test.js"}, "version": "0.11.4", "repository": {"type": "git", "url": "git://github.com/shtylman/node-process.git"}, "license": "MIT", "browser": "./browser.js", "main": "./index.js", "engines": {"node": ">= 0.6.0"}, "devDependencies": {"mocha": "2.2.1"}, "gitHead": "5de5d427da885c6c618d3b3a009f8c4470215e30", "bugs": {"url": "https://github.com/shtylman/node-process/issues"}, "_id": "process@0.11.4", "_shasum": "a6e6d49f0833d36571c0b9492c0f4b90bac96cd3", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "5.11.0", "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "dist": {"shasum": "a6e6d49f0833d36571c0b9492c0f4b90bac96cd3", "tarball": "https://registry.npmjs.org/process/-/process-0.11.4.tgz", "integrity": "sha512-Kvfc91oL3n+wX6n3J0tq5F+nDIyyOIGyGVsEqO3e2TlG328r7erlVt4ABf4HtAYdl1Hbsk4bsGJNeC261aLM5Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDz1+oru9R/JM+dAJGM8Xcvd/XjOgqe8PdyytreGIBWtQIgGoPWTjwEytbfln1GvxRh0UTb4bzGRFyS7D3ihnB9dVY="}]}, "maintainers": [{"name": "coolaj86", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/process-0.11.4.tgz_1465568417025_0.5683431040961295"}, "directories": {}}, "0.11.5": {"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "process", "description": "process information for node.js and browsers", "keywords": ["process"], "scripts": {"test": "mocha test.js"}, "version": "0.11.5", "repository": {"type": "git", "url": "git://github.com/shtylman/node-process.git"}, "license": "MIT", "browser": "./browser.js", "main": "./index.js", "engines": {"node": ">= 0.6.0"}, "devDependencies": {"mocha": "2.2.1"}, "gitHead": "dbaeeb81fe68d00c0a5201f22dca56009b948202", "bugs": {"url": "https://github.com/shtylman/node-process/issues"}, "_id": "process@0.11.5", "_shasum": "de49788f60e706f333adeea57a187ea9cc4c8495", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "5.11.0", "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "dist": {"shasum": "de49788f60e706f333adeea57a187ea9cc4c8495", "tarball": "https://registry.npmjs.org/process/-/process-0.11.5.tgz", "integrity": "sha512-hn2RKl04oJers9vtfffoQjabPWdLjruLXDwFiNwj0ggjruaLUeEWRR6gKiBR7ejqoONOGNvBPxDx1MrD+G7mDQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAP4zX1y2Vm+SsCa2+SHhkNaGMms6gPWOr6aEfguoJYpAiEA1ziTq9DDyoLSWw6w3QDYUepZFc0u9BiSw6ulWt6ftR0="}]}, "maintainers": [{"name": "coolaj86", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/process-0.11.5.tgz_1465844241330_0.7085902339313179"}, "directories": {}}, "0.11.6": {"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "process", "description": "process information for node.js and browsers", "keywords": ["process"], "scripts": {"test": "mocha test.js"}, "version": "0.11.6", "repository": {"type": "git", "url": "git://github.com/shtylman/node-process.git"}, "license": "MIT", "browser": "./browser.js", "main": "./index.js", "engines": {"node": ">= 0.6.0"}, "devDependencies": {"mocha": "2.2.1"}, "gitHead": "331de4d3b8cab1bb41ffea954e7971aa3f96d1f1", "bugs": {"url": "https://github.com/shtylman/node-process/issues"}, "_id": "process@0.11.6", "_shasum": "f234a1fbfddd0a64f8347e1ab52184453d7347af", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "5.12.0", "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "dist": {"shasum": "f234a1fbfddd0a64f8347e1ab52184453d7347af", "tarball": "https://registry.npmjs.org/process/-/process-0.11.6.tgz", "integrity": "sha512-Dj+TvVat2xIHKM9WzhPa4y2sm6tCbcmDK0eiirf8olwwWW4r6eRftXFYio3zV5JfHdQJeV3W8iOgxWpm2b/3fA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCdh6UXFKhsYcHqGwQBmNiCtlYhwQKveoFJzvEvE1T5iwIhAPCTi5yACbcHdG/9nLlUeusf26czXXZu5Msi6CQYUVFr"}]}, "maintainers": [{"name": "coolaj86", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/process-0.11.6.tgz_1469540140351_0.07442293269559741"}, "directories": {}}, "0.11.7": {"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "process", "description": "process information for node.js and browsers", "keywords": ["process"], "scripts": {"test": "mocha test.js"}, "version": "0.11.7", "repository": {"type": "git", "url": "git://github.com/shtylman/node-process.git"}, "license": "MIT", "browser": "./browser.js", "main": "./index.js", "engines": {"node": ">= 0.6.0"}, "devDependencies": {"mocha": "2.2.1"}, "gitHead": "5997b18064fab7821cfce36a5d2c531771709de7", "bugs": {"url": "https://github.com/shtylman/node-process/issues"}, "_id": "process@0.11.7", "_shasum": "9f19d4803c1091c7f87e853f812a419f22b1fa45", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "5.12.0", "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "dist": {"shasum": "9f19d4803c1091c7f87e853f812a419f22b1fa45", "tarball": "https://registry.npmjs.org/process/-/process-0.11.7.tgz", "integrity": "sha512-yeDcCmC8n8/Bpk1uG/5WBUgGjgu01JggJSIQuLXVxY3iilHqUXeLM8jNn9vDivetaF0V+y+0n26SsGChhSGKew==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCA4HUX1qWNOYLHwZHlbYjhb1kGcny8UCnWV8s7Vl96qQIhAIubCk4oargMiUjNi7JOjIxz9ADZdJ6i/bpXNYPI6VOu"}]}, "maintainers": [{"name": "coolaj86", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/process-0.11.7.tgz_1470082128030_0.5076596953440458"}, "directories": {}}, "0.11.8": {"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "process", "description": "process information for node.js and browsers", "keywords": ["process"], "scripts": {"test": "mocha test.js", "browser": "zuul --no-coverage --ui mocha-bdd --local 8080 -- test.js"}, "version": "0.11.8", "repository": {"type": "git", "url": "git://github.com/shtylman/node-process.git"}, "license": "MIT", "browser": "./browser.js", "main": "./index.js", "engines": {"node": ">= 0.6.0"}, "devDependencies": {"mocha": "2.2.1", "zuul": "^3.10.3"}, "gitHead": "1c4943a088579d57cc82876e6ed3972fd2595e84", "bugs": {"url": "https://github.com/shtylman/node-process/issues"}, "_id": "process@0.11.8", "_shasum": "58ff3d04ae0641be72c5a6e60ce8f7822d64eb3c", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "5.12.0", "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "dist": {"shasum": "58ff3d04ae0641be72c5a6e60ce8f7822d64eb3c", "tarball": "https://registry.npmjs.org/process/-/process-0.11.8.tgz", "integrity": "sha512-u7iwDOZXtjs4HAGG3T8dUKXRmc9NUm3ISDp5NyRfKz4zpek3Oju3e5iBphz5X5Tc70tidKOCK0ylxoBH6n/sFQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAQ+85K7q6VxPSfwhUyrPJAaioRfIDBGMaXeWdowo/vFAiEAww3W6tbT29gVwM+SAB/lzc1u8v7SK5U7kdkh1CFlgW0="}]}, "maintainers": [{"name": "coolaj86", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/process-0.11.8.tgz_1470663916796_0.6756675334181637"}, "directories": {}}, "0.11.9": {"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "process", "description": "process information for node.js and browsers", "keywords": ["process"], "scripts": {"test": "mocha test.js", "browser": "zuul --no-coverage --ui mocha-bdd --local 8080 -- test.js"}, "version": "0.11.9", "repository": {"type": "git", "url": "git://github.com/shtylman/node-process.git"}, "license": "MIT", "browser": "./browser.js", "main": "./index.js", "engines": {"node": ">= 0.6.0"}, "devDependencies": {"mocha": "2.2.1", "zuul": "^3.10.3"}, "gitHead": "7d8c3702a8bbc43fa55f4bab74b150aef37001dd", "bugs": {"url": "https://github.com/shtylman/node-process/issues"}, "_id": "process@0.11.9", "_shasum": "7bd5ad21aa6253e7da8682264f1e11d11c0318c1", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "5.12.0", "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "dist": {"shasum": "7bd5ad21aa6253e7da8682264f1e11d11c0318c1", "tarball": "https://registry.npmjs.org/process/-/process-0.11.9.tgz", "integrity": "sha512-rBG9LeETmhkfw0lr4dB5y3p5NHQadv2gyr+yPgQ8OebVn9LS4CDL7JW6NCdyeE8lzGkQ+x+lyytd+nC26A1DtQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDekw/LCiZnu6LEWhYOh56TBdeMbzgaK72scpsMViGWKgIhAJu+TkZkKHsYtrWsUBefZ2l7veCXs7g9fYbHaySVq/GU"}]}, "maintainers": [{"name": "coolaj86", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/process-0.11.9.tgz_1472587751718_0.8843140550889075"}, "directories": {}}, "0.11.10": {"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "process", "description": "process information for node.js and browsers", "keywords": ["process"], "scripts": {"test": "mocha test.js", "browser": "zuul --no-coverage --ui mocha-bdd --local 8080 -- test.js"}, "version": "0.11.10", "repository": {"type": "git", "url": "git://github.com/shtylman/node-process.git"}, "license": "MIT", "browser": "./browser.js", "main": "./index.js", "engines": {"node": ">= 0.6.0"}, "devDependencies": {"mocha": "2.2.1", "zuul": "^3.10.3"}, "gitHead": "557aa46f283caccce603dbb9c376b3d81067c510", "bugs": {"url": "https://github.com/shtylman/node-process/issues"}, "homepage": "https://github.com/shtylman/node-process#readme", "_id": "process@0.11.10", "_shasum": "7332300e840161bda3e69a1d1d91a7d4bc16f182", "_from": ".", "_npmVersion": "4.0.5", "_nodeVersion": "6.10.0", "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "dist": {"shasum": "7332300e840161bda3e69a1d1d91a7d4bc16f182", "tarball": "https://registry.npmjs.org/process/-/process-0.11.10.tgz", "integrity": "sha512-cdGef/drWFoydD1JsMzuFf8100nZl+GT+yacc2bEced5f9Rjk4z+WtFUTBu9PhOi9j/jfmBPu0mMEY4wIdAF8A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAkUiaCU6Ri9Wc3QVyTsJOHRUiudmz3nMsAmOi9ILw8MAiEAg2/+q/uTfnViZCy0ChjRIOeJwDeH8e0xIlZg+MT57Qk="}]}, "maintainers": [{"name": "coolaj86", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/process-0.11.10.tgz_1493210065468_0.9640797527972609"}, "directories": {}}}, "maintainers": [{"name": "coolaj86", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "time": {"modified": "2023-07-22T20:56:21.581Z", "created": "2011-06-30T01:07:03.273Z", "0.4.9": "2011-06-30T01:07:03.645Z", "0.5.0": "2013-01-21T18:28:53.789Z", "0.5.1": "2013-02-16T23:33:34.663Z", "0.5.2": "2014-01-02T15:45:50.078Z", "0.6.0": "2014-02-01T15:17:00.901Z", "0.7.0": "2014-04-29T19:57:50.021Z", "0.8.0": "2014-09-13T18:06:49.355Z", "0.9.0": "2014-10-11T05:55:44.245Z", "0.10.0": "2014-12-10T19:09:32.584Z", "0.10.1": "2015-03-05T16:50:05.662Z", "0.11.0": "2015-04-24T19:01:16.544Z", "0.11.1": "2015-05-21T21:26:30.512Z", "0.11.2": "2015-09-09T00:51:40.285Z", "0.11.3": "2016-05-07T19:39:49.834Z", "0.11.4": "2016-06-10T14:20:19.867Z", "0.11.5": "2016-06-13T18:57:24.351Z", "0.11.6": "2016-07-26T13:35:42.977Z", "0.11.7": "2016-08-01T20:08:48.819Z", "0.11.8": "2016-08-08T13:45:19.072Z", "0.11.9": "2016-08-30T20:09:13.289Z", "0.11.10": "2017-04-26T12:34:27.448Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/shtylman/node-process.git"}, "users": {"326060588": true, "m42am": true, "luk": true, "simplyianm": true, "monsterkodi": true, "dac2205": true, "blitzprog": true, "coalesce": true, "ahme-t": true, "shanewholloway": true, "mojaray2k": true, "dzhou777": true, "langri-sha": true, "chinawolf_wyp": true, "advence-liz": true, "kkho595": true, "subinvarghesein": true, "tedyhy": true, "flumpus-dev": true}, "readme": "# process\n\n```require('process');``` just like any other module.\n\nWorks in node.js and browsers via the browser.js shim provided with the module.\n\n## browser implementation\n\nThe goal of this module is not to be a full-fledged alternative to the builtin process module. This module mostly exists to provide the nextTick functionality and little more. We keep this module lean because it will often be included by default by tools like browserify when it detects a module has used the `process` global.\n\nIt also exposes a \"browser\" member (i.e. `process.browser`) which is `true` in this implementation but `undefined` in node. This can be used in isomorphic code that adjusts it's behavior depending on which environment it's running in. \n\nIf you are looking to provide other process methods, I suggest you monkey patch them onto the process global in your app. A list of user created patches is below.\n\n* [hrtime](https://github.com/kumavis/browser-process-hrtime)\n* [stdout](https://github.com/kumavis/browser-stdout)\n\n## package manager notes\n\nIf you are writing a bundler to package modules for client side use, make sure you use the ```browser``` field hint in package.json.\n\nSee https://gist.github.com/4339901 for details.\n\nThe [browserify](https://github.com/substack/node-browserify) module will properly handle this field when bundling your files.\n\n\n", "readmeFilename": "README.md", "keywords": ["process"], "bugs": {"url": "https://github.com/shtylman/node-process/issues"}, "license": "MIT", "homepage": "https://github.com/shtylman/node-process#readme"}