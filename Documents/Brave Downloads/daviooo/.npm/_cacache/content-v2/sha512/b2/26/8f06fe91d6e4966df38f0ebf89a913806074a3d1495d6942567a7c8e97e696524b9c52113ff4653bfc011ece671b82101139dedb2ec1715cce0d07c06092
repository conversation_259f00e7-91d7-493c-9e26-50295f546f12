{"_id": "resolve-alpn", "_rev": "7-390c0bed5cf90180878dab303fe0871d", "name": "resolve-alpn", "dist-tags": {"latest": "1.2.1"}, "versions": {"1.0.0": {"name": "resolve-alpn", "version": "1.0.0", "description": "Detects the ALPN protocol", "main": "index.js", "scripts": {"test": "xo && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/resolve-alpn.git"}, "keywords": ["alpn", "http2"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/resolve-alpn/issues"}, "homepage": "https://github.com/szmarczak/resolve-alpn#readme", "devDependencies": {"ava": "^1.0.1", "coveralls": "^3.0.2", "nyc": "^13.1.0", "pem": "^1.13.2", "xo": "^0.23.0"}, "gitHead": "64c69e4917328cdcbdef85d677409c4fe3c835d9", "_id": "resolve-alpn@1.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "11.1.0", "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "dist": {"integrity": "sha512-rTuiIEqFmGxne4IovivKSDzld2lWW9QCjqv80SYjPgf+gS35eaCAjaP54CCwGAwBtnCsvNLYtqxe1Nw+i6JEmA==", "shasum": "745ad60b3d6aff4b4a48e01b8c0bdc70959e0e8c", "tarball": "https://registry.npmjs.org/resolve-alpn/-/resolve-alpn-1.0.0.tgz", "fileCount": 7, "unpackedSize": 5345, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcH6bECRA9TVsSAnZWagAA2SYP/2Ufx6ratYri5VJzXhZH\n1hOoCby9C0bmsFVBkNStqeKsdOHdJ3I6Nse82oJW2i5UFhhh8ikzQZq9VL3q\n8cUafC91RHJv5hmdJ460NnAOhvlCX+bnlnfm/DcpxJCufZfQwMnuI9Yp2M+I\nP2HDCs9BL0vntylq6V1lHuhkk9yJSjxi/jiNFw7UZamaodfGEnwJsSHz9hNp\nEREx7yQsVuVfxpSugLSwLA2MsAeg6RP+KP4NYejKOCtohV/+fnaEstwGeu+l\nlpnHCcPwbTGlt3HD5ceLq/FfnpdwFnS9TfBrhqf5zGe+Yd5nSmPSO35sKX/H\nrkOEK1qMHp864hGHh/cYChSr58moi5veUTBR1PvPSvDV3U1GPsp29OMMWHPv\niL/2tzpJW1PNDIF8i2DEmhqFSOlvo2e48klXbuVk/hH6d0eWAevCAqwlE1MV\nz1yqNi0auXg0vbNyI+ir4PiZJwoa2dCktD2LaKo8qX9RCGPyIk8BH+wGkhIu\nirBZ8YvygVcVuqRn4trQNuZa2sHMqtfQtohaAbDE1MvFXNjFUhk0S6wIQL0N\nl9S7aUZfr6Ac0xllRWFcXvMxBBfI2yokCM8FWTxGk97+/UBQ5kmchbrux/Gr\nAYydWzl83Cr54Hypkga/xVyktkPIuSU5vm2/HYPx7gSCYrCjpfRDlpHhWlZu\nbq5T\r\n=M/td\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEE6WjK/ozMWuiwTx23VByU2Tf0m8CsBSEWShrZeajnwAiEAkAEN2sX4I6UiwaLefPMdh5dodUX+I6MoCmg+jFvHtb0="}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/resolve-alpn_1.0.0_1545578180000_0.5579683285245283"}, "_hasShrinkwrap": false}, "1.0.1": {"name": "resolve-alpn", "version": "1.0.1", "description": "Detects the ALPN protocol", "main": "index.js", "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text --reporter=html ava"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/resolve-alpn.git"}, "keywords": ["alpn", "tls", "socket", "http2"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/resolve-alpn/issues"}, "homepage": "https://github.com/szmarczak/resolve-alpn#readme", "devDependencies": {"ava": "^3.15.0", "nyc": "^15.1.0", "pem": "1.14.3", "xo": "^0.38.2"}, "gitHead": "7df04693f0dad4cd6e2f810cb34ea9a8e1a94bc5", "_id": "resolve-alpn@1.0.1", "_nodeVersion": "15.12.0", "_npmVersion": "7.6.3", "dist": {"integrity": "sha512-LqxH4ojsUlx7HV8Cw00qVE6X7eQYMlIg2tayGBRVzBTBp3q2TQhpsaxGteNID+6/R0+SxdLw5N540nqGHxpanw==", "shasum": "77b1d41c16dd506af90358037d1b2e1ccba98049", "tarball": "https://registry.npmjs.org/resolve-alpn/-/resolve-alpn-1.0.1.tgz", "fileCount": 7, "unpackedSize": 6745, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgc0agCRA9TVsSAnZWagAAdbwQAIdosSoWP6Be+/2IngVW\nKfznxaP5jB4ZTTbvZoisMKKoGCwZ5+J8dl9HM+vEyN9wT12G9nmintn/jhBa\nManhzCIMXk3/+v3Jr7ak3grOxmTt57+A0IQODgQGdjvDri9WVvC+B5Vmr4Ix\nfUjthcpfy2s8Gu84vnLyALrY78MW1mxOs2SvD0OlUQlFrbjxR7ElOYjQsJqx\nHDrmwQu/m4vdRVPNMiWv/8pioWaaDcglJ/GFEswtr0ruYSOMazM3TxWGKKYC\nQnS0ApCgVTMvq121v/rZuPcDpfmzphSUdYc1VioWwHqj/BvR4B1a5+amHQM3\n3tQsx4PlrPk1R58N0FC0D3ZQwv6amXPzJqTOZFQgnfcZJIwwJ4LTK2QpwKMT\nhdSZ2nyAykaTs6gfpkQYxR9eIQfh4t4g6Y4hPWdLFFmdw1Ad8Bfb7E1j0gDf\nUnvhqF63yxzEpvV7XjYE9GCA7HrrcPg/qLrA9v6+soONLPEC5EGmQ6RfC99N\nytVKkG0WmTLBeWAmQxP3QKOsq+1Jw802jEie0MwkdPIgm9K+5q3HfKKGxtfw\n3S0h91gdgD8OvfRwOxYwMtIUQPehRWG3kZwhGXuC0BsuITytUGZKihsE9TN8\n8/iPLqVt9YEnJWDampuQOuvTgDVoRfeQQ2sPyIasAfZvAYChSPWeTQ97WG/i\nhrHG\r\n=JpDS\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE01lZnIh0CpuEzO7XDSnRy13J9Svdoq1HrBDVUk71WEAiEA6jfsr4wuaHpvJnhrfxbqcoyPSRxQiRv2llHg+ZZnVbA="}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/resolve-alpn_1.0.1_1618167456049_0.6222278121713505"}, "_hasShrinkwrap": false}, "1.1.0": {"name": "resolve-alpn", "version": "1.1.0", "description": "Detects the ALPN protocol", "main": "index.js", "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text --reporter=html ava"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/resolve-alpn.git"}, "keywords": ["alpn", "tls", "socket", "http2"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/resolve-alpn/issues"}, "homepage": "https://github.com/szmarczak/resolve-alpn#readme", "devDependencies": {"ava": "^3.15.0", "nyc": "^15.1.0", "pem": "1.14.3", "xo": "^0.38.2"}, "gitHead": "fe0b71e244e23dfb043a7cb73dbf06b77910994a", "_id": "resolve-alpn@1.1.0", "_nodeVersion": "15.12.0", "_npmVersion": "7.6.3", "dist": {"integrity": "sha512-tjfdWcy8i4tPVzJn8lUij23JvzCuX8terfbfXq7JnCyZz9FCkiDbMSk2XQJnXA790uCqJM/X4YDS4wug7Q1zIg==", "shasum": "82cc1f4d367cbd484a06065a9f53fb1811a60aec", "tarball": "https://registry.npmjs.org/resolve-alpn/-/resolve-alpn-1.1.0.tgz", "fileCount": 7, "unpackedSize": 6965, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgc1KLCRA9TVsSAnZWagAAbkcP/2novPHSvAfOEcmLZXx8\nr+5zvNRjrASt3gVj0xpSSUmw+sikqnn14C0UdN5o28j5OMbWdy6YpI+Ix1mk\n4rQ2DKB6PQvSQ0uZBrd/bWSavhblAQADC+vmXW8NpxNyB9a7mBC97110jbp/\n3N6z5AQn9/JWIm7OXh/xJG6EME3YRXpmo8ov+auqXoiyn6X/l+uUg4qZQfNT\n/MlSqJpb0glCLT6wZsMcG/aa4tJYgkZ0MRSV23QEUHZxLFdhMLhFmxff/oM/\nIlEctgeIb3ueHFfkkk6dc09vuVVkkfcJZ4WakQtaBlgRR3lSsuBDDvJW5rh/\naCIssSX9ijJ2UliEtVbAbC1XAj7rONX9LoaOgS9SJeC4j2NC92iGxb2Whz9r\nljjb876A2Rf+E4VBZOwgbqsoDAb7aFYmp/dwLzIrutcMlzHkTwiBid/JsrvK\nnBsTpfzzqtZ7wCguhxgb6rPcnpVSSJQYo8NcuCWK3juHToap/fALc28xMQ8C\nV8kXIj0ZVDdrId3f2ND67p1MXeOHsPeeQBIQEmlhvzDVLrotmo7p/qgos2e+\nc2CNk5NY0MlAZ7V2g6fQJejJLOw72omOKYPdHknvEGk1yfKPX5CD3D9PyHOH\n8wTLx9w9iUKqHjwVQLi6wJcjMUvyb4D34bYz6E+OwM1cEjA+/WUr/I1ZgmTh\nzcI6\r\n=FBKN\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGFNIOaATM/MmI5FBc5aJmg8i+GowxThdUf/nQhQ/5sNAiEA6ZJr5xKEr8wMa3Sok1P2jj4UmTINzvT08oE3yMVBFBY="}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/resolve-alpn_1.1.0_1618170507098_0.8120324421676954"}, "_hasShrinkwrap": false}, "1.1.1": {"name": "resolve-alpn", "version": "1.1.1", "description": "Detects the ALPN protocol", "main": "index.js", "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text --reporter=html ava"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/resolve-alpn.git"}, "keywords": ["alpn", "tls", "socket", "http2"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/resolve-alpn/issues"}, "homepage": "https://github.com/szmarczak/resolve-alpn#readme", "devDependencies": {"ava": "^3.15.0", "nyc": "^15.1.0", "pem": "1.14.3", "xo": "^0.38.2"}, "gitHead": "fe0b71e244e23dfb043a7cb73dbf06b77910994a", "_id": "resolve-alpn@1.1.1", "_nodeVersion": "15.12.0", "_npmVersion": "7.6.3", "dist": {"integrity": "sha512-0KbFjFPR2bnJhNx1t8Ad6RqVc8+QPJC4y561FYyC/Q/6OzB3fhUzB5PEgitYhPK6aifwR5gXBSnDMllaDWixGQ==", "shasum": "4a006a7d533c81a5dd04681612090fde227cd6e1", "tarball": "https://registry.npmjs.org/resolve-alpn/-/resolve-alpn-1.1.1.tgz", "fileCount": 7, "unpackedSize": 7032, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgc1S9CRA9TVsSAnZWagAADhYP/Rk3/1vY6nx99OdJP91p\nXhl6Nw8u/+FSGSjtTVeOxBY/+GX16jGv5w8nRlexSKj8lIqfubakrUV5jmwd\nSeL8ihH+3nO2E/g4WRs8jqe99MLasGl6ALYPtsPXvDgS82DsC8bjr/YE3TQX\nyhbb3gZAai8GVWQ9lY0uRBnXI0c3oeaUNXWr9lKyhUOKVymztsys2q9+T/aX\nvbBbTIHTskSKWnuJiRRzVbOmHr/8Q2inlJv0mbwW2H1uuhbYXfV8KASI0QU0\nV5HlYc69dHf0UthbIk44c2VmvUPR86mQ4jlkpdukyqWV9v9pqgQtb9HPjNMA\nV643GtmLSM7zzJf3UO6IblpEOLoAF98Vvsm+FJumIeZwGwTgFhJrbnGoYBZi\nl6HfbBYLN3TC32Xou3Xh5rQieauJzrrchsxwN33xmThxF+9GEz2ubIqfFLey\nDdiTd1NP/6t1uDNVDNJy5Y2ICvFBahiQ6aZTGosGoA6leHzYKTkWk+p5dyml\nhfqp0RG/6pf5TF1SYEtdaryYEc/op7cOhMUuf+Luqgx4HgUdh6gSByExlitw\ndE2WEFtkPHGqNX2X9D0QVnFP1ANewUkZ6G70+MWk+1C8wDVGf7fGolqlJt0Q\nQQqWN0VbHyAoXexo9UeIwk8VjjjdyeDcLHJ8gBNEBj2DMZoR3z/wKkDzvKwX\n/FCi\r\n=c7wi\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHWpPhVWMi6dMnrNRdyz6q+Cv32uBuDZremE1SanVyRpAiEAhOSClD11AeLKwmOnM6KsvOyXni/UCMNDR4DaMyILE2w="}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/resolve-alpn_1.1.1_1618171068542_0.963122534139432"}, "_hasShrinkwrap": false}, "1.1.2": {"name": "resolve-alpn", "version": "1.1.2", "description": "Detects the ALPN protocol", "main": "index.js", "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text --reporter=html ava"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/resolve-alpn.git"}, "keywords": ["alpn", "tls", "socket", "http2"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/resolve-alpn/issues"}, "homepage": "https://github.com/szmarczak/resolve-alpn#readme", "devDependencies": {"ava": "^3.15.0", "nyc": "^15.1.0", "pem": "1.14.3", "xo": "^0.38.2"}, "gitHead": "43a065f747bc32728af4e2529d1ee5a61a29c63e", "_id": "resolve-alpn@1.1.2", "_nodeVersion": "15.10.0", "_npmVersion": "7.5.3", "dist": {"integrity": "sha512-8OyfzhAtA32LVUsJSke3auIyINcwdh5l3cvYKdKO0nvsYSKuiLfTM5i78PJswFPT8y6cPW+L1v6/hE95chcpDA==", "shasum": "30b60cfbb0c0b8dc897940fe13fe255afcdd4d28", "tarball": "https://registry.npmjs.org/resolve-alpn/-/resolve-alpn-1.1.2.tgz", "fileCount": 4, "unpackedSize": 4059, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgeHJnCRA9TVsSAnZWagAAyo0P/jMJL2LfNQsM4cBGBU/9\ndHz6mgZKY2hmRq1yoYTWQslfw451xtxCp6hMHgIBhS890Y/0sF0i9sP/Pc3e\nyvNqzB9HoJR8JyXN29aJBOW6ZyWNs78jqZ3l5AX8lUa1/ePHdfKpm/jqGK41\n+8to7WHDtPAZsgzAqEf/rUyBXF72Jclenn50cmeBSCa0wq3hccv+/E4JV6jr\nekp6iSeqj9x0/OuU9a9vs0miDQLHc1TYSz8S6+QE7mguhciEbtoMJs/XJG9y\niKix/hdtvljwFOOtxdUUI78EXPNK53QLy5WgeaHTD8h7ODie0l+UMv8IXVx/\nLd27Ikn2HDG+fhdZpfH2TEC51ceE7xUf4WpUlGXAiQIcNVbZZxvW9zow02pg\nbZrfuEystNU/39jXTiINAQ5llcV43MBeeI43Jx8QzMfn00u8Jw2799+md9Mz\nuBwCDlohpqLz203DGX4TAS1KfzSqvAUX4ot4tnxqyRuDgffz3CbgY/28fz3K\ncEdgsVR0JV3R48vUnHophCF/ELLTJB8UTiSpQCUmi3pLRQE7jvvZLLL0An06\njK1V0jx/9kB0JjkGDVspTCAf7fDaMRg50di9tp72cIp681pNmhNQyREGkfW4\npz5EVGBgFrzUbhqbRWcwxyTqCM7NrI/VaowB5w8ouX9b7HSmtPVWbqKMzYgB\nb1n9\r\n=DjEN\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIClq8rjOMB0zLUF+JLm4rrya/OhCqd3ggV77GumXmnpqAiEA3Xe9DbGTuvajLlqw/c4OMGbFwDCvPpeHAGVzf3Am0Zc="}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/resolve-alpn_1.1.2_1618506342776_0.3621364652688188"}, "_hasShrinkwrap": false}, "1.2.0": {"name": "resolve-alpn", "version": "1.2.0", "description": "Detects the ALPN protocol", "main": "index.js", "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text --reporter=html ava"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/resolve-alpn.git"}, "keywords": ["alpn", "tls", "socket", "http2"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/resolve-alpn/issues"}, "homepage": "https://github.com/szmarczak/resolve-alpn#readme", "devDependencies": {"ava": "^3.15.0", "nyc": "^15.1.0", "pem": "1.14.3", "xo": "^0.38.2"}, "gitHead": "5fb9af9ac11c7fab34f980e531d98d3b08f4800c", "_id": "resolve-alpn@1.2.0", "_nodeVersion": "16.5.0", "_npmVersion": "7.19.1", "dist": {"integrity": "sha512-e4FNQs+9cINYMO5NMFc6kOUCdohjqFPSgMuwuZAOUWqrfWsen+Yjy5qZFkV5K7VO7tFSLKcUL97olkED7sCBHA==", "shasum": "058bb0888d1cd4d12474e9a4b6eb17bdd5addc44", "tarball": "https://registry.npmjs.org/resolve-alpn/-/resolve-alpn-1.2.0.tgz", "fileCount": 4, "unpackedSize": 4599, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg+ENnCRA9TVsSAnZWagAAbnYP/1Sc3oHHaezUYS6ZoEuj\nqGcErF5qd4W7B12fxk70ftdCgQdJstmyUayuEJAqeKe74Zbos5Bql+PDkToG\nMcSQ0pBXVCeZg+5v8OSXaa31cKyv9XO15ia6HC2GvCjDx0oU7v/nXOVYudUG\nt4AG54UkQ/LeRpcBtPDzcD/07nBcbdzHhsO+J9/aKcju9MjRAOtJVW6RaH8B\n8hDzbe7Cg0nyB5pdYfIBcWWl75AZmZ9zqNPae7OR9QJIg4ByUDOt3kIiypFA\n7c3YPBai2SqN1CT+Uq9vlyJNQ2xeBKLw55j8sHkqtr8QNsRnY8PRhyKHhHmw\nu+f28XaWgKG4MjMg12vOUYqYZBgS6+gaAqZ8MUZlsP0hhui1Ub1d+sJNVU3o\nDYz6lL0Sfew/8y0KdUeHvqy1AyXyoeOgSrc8WgukBPDxY69psmWSt/ViWGe+\nfB2raqmqRXxgeWNDXhgHgSNaa1FDxC3DwIoaXKUSmwsIkSjGAvX5YJLkIZ8o\n70fT+LDIHUIj1kv3M149aI4T7yAMCrpJSQxLmVK+mr1VcvwO6J/4Q7ohqw4t\no07CAURFa8fU+TmpAR2aEy76d7ng841n9dKCXK8PjRPAkRSqw7uhjmrAlIId\nSdhoA20I8en+ZR10f0hC7EMPG3iAffSJlF787NoJ0SDrY44w2Cw6fOL9iT/+\nqPWn\r\n=7Kqw\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDWutz84ulrmEGZkDZ74vLCtD+RxiOLJhUJggLVY7sx3AiAAvRfJk38ORZwRkVL0RCntA1XTAiabNJqSz7O2nxWvMw=="}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/resolve-alpn_1.2.0_1626882919273_0.021926971397304262"}, "_hasShrinkwrap": false}, "1.2.1": {"name": "resolve-alpn", "version": "1.2.1", "description": "Detects the ALPN protocol", "main": "index.js", "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text --reporter=html ava"}, "repository": {"type": "git", "url": "git+https://github.com/szmarczak/resolve-alpn.git"}, "keywords": ["alpn", "tls", "socket", "http2"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/resolve-alpn/issues"}, "homepage": "https://github.com/szmarczak/resolve-alpn#readme", "devDependencies": {"ava": "^3.15.0", "nyc": "^15.1.0", "pem": "1.14.3", "xo": "^0.38.2"}, "gitHead": "778781a0521c713ca7605fc3ba31fb8e4be37d6b", "_id": "resolve-alpn@1.2.1", "_nodeVersion": "16.8.0", "_npmVersion": "7.21.0", "dist": {"integrity": "sha512-0a1F4l73/ZFZOakJnQ3FvkJ2+gSTQWz/r2KE5OdDY0TxPm5h4GkqkWWfM47T7HsbnOtcJVEF4epCVy6u7Q3K+g==", "shasum": "b7adbdac3546aaaec20b45e7d8265927072726f9", "tarball": "https://registry.npmjs.org/resolve-alpn/-/resolve-alpn-1.2.1.tgz", "fileCount": 4, "unpackedSize": 4645, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhLJL0CRA9TVsSAnZWagAAX64P/R0rS42z848N99J7ttJ4\n35icwgihGUd5sUDhR/j0+k5x3cOm58y0XN0xm9nbQF7rN/0z+x/Zb5L8sokp\n4S5UzGYn1IiBqSUDkjAO0WzlvFucL6pfPApJb2fICU5z09Fj0UGaqhzsygRu\nmld81QSap7GCdGycJAQ3uFyA7ODe0o9CA0wXpi7V5Ph4rBRJ/X0sFlBdoEjc\nPEUzeT4qvpXNti0claoRKDMZWHDJaeAPWJAbGnTukgjbG9XRxcas2CH5a1sR\nknsTj6He49Ls8Cg5j2SGjJDz/ekJDVlcMrXNVbmB002QGi0A9+5+P4oJRBfO\n2ZBdLUrsEDYBDqEUDSqHS5k9h+eJvfVDTw9+Wbbmi13UWK+Rxutyr5UnT2ON\nHjY+EWj05egNNIOw1J2pr6/Z9drCWCpyY1+FT5OmCTYvgHBSLWOyiHNPAp2O\nf5GNybOTonKv4tw+KspFoUSeqVGyACRJvr5rwCBL3459oqvWoNyPEGYr2H7r\nDfgxC3y/u99nxzQx/v7h0Y07YK6ptW5kgfbC4s1onp56fx7v6FQYcewUEOTo\ny4aBFItyqwVMdBviAOuOgFmhQGF9uWJYZSWiz0WOELKft35gAsXG4gRW9awp\ngemmuwuOu7Q7F3hArkkK6ZmtlLcgZwoLMcAgv6PQp+lVdhR4ArDZ1Gl8Chxi\nXWic\r\n=4z4p\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDNSH7t3Kxpo66GwY5Byo21LxWli3D9iHduXALg6TOU7AIgDE/gufNlN7VmD2N+tvOubjy+OQf81qNa4/VMEZ54Kms="}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/resolve-alpn_1.2.1_1630311156553_0.3874047205312978"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-12-23T15:16:19.999Z", "1.0.0": "2018-12-23T15:16:20.138Z", "modified": "2022-05-15T21:07:49.469Z", "1.0.1": "2021-04-11T18:57:36.181Z", "1.1.0": "2021-04-11T19:48:27.215Z", "1.1.1": "2021-04-11T19:57:48.924Z", "1.1.2": "2021-04-15T17:05:42.944Z", "1.2.0": "2021-07-21T15:55:19.534Z", "1.2.1": "2021-08-30T08:12:36.687Z"}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "description": "Detects the ALPN protocol", "homepage": "https://github.com/szmarczak/resolve-alpn#readme", "keywords": ["alpn", "tls", "socket", "http2"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/resolve-alpn.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "bugs": {"url": "https://github.com/szmarczak/resolve-alpn/issues"}, "license": "MIT", "readme": "# `resolve-alpn`\n\n[![Node CI](https://github.com/szmarczak/resolve-alpn/workflows/Node%20CI/badge.svg)](https://github.com/szmarczak/resolve-alpn/actions)\n[![codecov](https://codecov.io/gh/szmarczak/resolve-alpn/branch/master/graph/badge.svg)](https://codecov.io/gh/szmarczak/resolve-alpn)\n\n## API\n\n### resolveALPN(options, connect = tls.connect)\n\nReturns an object with an `alpnProtocol` property. The `socket` property may be also present.\n\n```js\nconst result = await resolveALPN({\n\thost: 'nghttp2.org',\n\tport: 443,\n\tALPNProtocols: ['h2', 'http/1.1'],\n\tservername: 'nghttp2.org'\n});\n\nconsole.log(result); // {alpnProtocol: 'h2'}\n```\n\n**Note:** While the `servername` option is not required in this case, many other servers do. It's best practice to set it anyway.\n\n**Note:** If the socket times out, the promise will resolve and `result.timeout` will be set to `true`.\n\n#### options\n\nSame as [TLS options](https://nodejs.org/api/tls.html#tls_tls_connect_options_callback).\n\n##### options.resolveSocket\n\nBy default, the socket gets destroyed and the promise resolves.<br>\nIf you set this to true, it will return the socket in a `socket` property.\n\n```js\nconst result = await resolveALPN({\n\thost: 'nghttp2.org',\n\tport: 443,\n\tALPNProtocols: ['h2', 'http/1.1'],\n\tservername: 'nghttp2.org',\n\tresolveSocket: true\n});\n\nconsole.log(result); // {alpnProtocol: 'h2', socket: tls.TLSSocket}\n\n// Remember to destroy the socket if you don't use it!\nresult.socket.destroy();\n```\n\n#### connect\n\nType: `Function<TLSSocket> | AsyncFunction<TLSSocket>`\\\nDefault: [`tls.connect`](https://nodejs.org/dist/latest-v16.x/docs/api/tls.html#tls_tls_connect_options_callback)\n\n**Note:** No matter which function is used (synchronous or asynchronous), it **must** accept a `callback` function as a second argument. The `callback` function gets executed when the socket has successfully connected.\n\n## License\n\nMIT\n", "readmeFilename": "README.md"}