{"_id": "ncp", "_rev": "155-866d26162afcf5b143f77df1d15f74e3", "name": "ncp", "description": "Asynchronous recursive file copy utility.", "dist-tags": {"latest": "2.0.0"}, "versions": {"0.0.0": {"name": "ncp", "preferGlobal": "true", "version": "0.0.0", "author": {"name": "AvianFlu", "email": "<EMAIL>"}, "description": "Asynchronous recursive file copy utility.", "bin": {"ncp": "./bin/ncp"}, "repository": {"type": "git", "url": "git://github.com/AvianFlu/ncp.git"}, "keywords": ["cli", "copy"], "license": "MIT", "engine": {"node": ">=0.4"}, "_npmJsonOpts": {"file": "/home/<USER>/.npm/ncp/0.0.0/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "ncp@0.0.0", "dependencies": {}, "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.25", "_nodeVersion": "v0.4.9", "_defaultsLoaded": true, "dist": {"shasum": "49315e9bd1a801dbdeedbf81a424bafcda193e9f", "tarball": "https://registry.npmjs.org/ncp/-/ncp-0.0.0.tgz", "integrity": "sha512-64r1yZRTT0nDSosQykWaqLqMeiFiWlx2pyLxwFUNEanQBH6RmW0d1ti7tac3cLZKiIXVzwJMClX676Hf36Ly6A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDFJZINRQ9VpHu2aswyRfqt5oE1gwkTBARUa1a59AxnRQIgWt6wp3tFW/rlJLv7o/Na5O9CYKWZUGQJwv7+Kd53VS0="}]}, "scripts": {}, "maintainers": [{"name": "avianflu", "email": "<EMAIL>"}], "directories": {}}, "0.0.1": {"name": "ncp", "preferGlobal": "true", "version": "0.0.1", "author": {"name": "AvianFlu", "email": "<EMAIL>"}, "description": "Asynchronous recursive file copy utility.", "bin": {"ncp": "./bin/ncp"}, "main": "./lib/ncp.js", "repository": {"type": "git", "url": "git://github.com/AvianFlu/ncp.git"}, "keywords": ["cli", "copy"], "license": "MIT", "engine": {"node": ">=0.4"}, "_npmJsonOpts": {"file": "/home/<USER>/.npm/ncp/0.0.1/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "ncp@0.0.1", "dependencies": {}, "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.25", "_nodeVersion": "v0.4.9", "_defaultsLoaded": true, "dist": {"shasum": "8a4d6ba28390c6a6ecf6536d553cfff300d4dcab", "tarball": "https://registry.npmjs.org/ncp/-/ncp-0.0.1.tgz", "integrity": "sha512-2plp6zNBR1a3mNcSYLtXLnZBIX7v+W5KIpZVjV3K8q3QSeYnz2nMpTFPdWC7EY4uW9iyC+ueF/LE67VghCY/CQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDzECIP9/q8+aCu4wX3nGX7DFFgUzXZEVauRHp68UvgfQIhALolYnGc/LOuJijY1valXxahmCCkFEM+HW5ap6ogB6/f"}]}, "scripts": {}, "maintainers": [{"name": "avianflu", "email": "<EMAIL>"}], "directories": {}}, "0.0.2": {"name": "ncp", "version": "0.0.2", "author": {"name": "AvianFlu", "email": "<EMAIL>"}, "description": "Asynchronous recursive file copy utility.", "bin": {"ncp": "./bin/ncp"}, "main": "./lib/ncp.js", "repository": {"type": "git", "url": "git://github.com/AvianFlu/ncp.git"}, "keywords": ["cli", "copy"], "license": "MIT", "engine": {"node": ">=0.4"}, "_npmJsonOpts": {"file": "/home/<USER>/.npm/ncp/0.0.2/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "ncp@0.0.2", "dependencies": {}, "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.25", "_nodeVersion": "v0.4.9", "_defaultsLoaded": true, "dist": {"shasum": "07b8daad5f5b51e14cf23b09a8d79b92b71a373c", "tarball": "https://registry.npmjs.org/ncp/-/ncp-0.0.2.tgz", "integrity": "sha512-rxlhurCxHmIiP30jNBWkm9lRqDQj1pKq+l4oSbvE66X6EdfDuljyVjz4MzX4yyzWS1DhJUVCVL3vQWCpTSiM+w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD98K6dvYq9nIMW7kCQAJerGdgxGygxOP++GcPsEr1RKgIgUm6AV4lE1VvmpKPQm9NbDcVSrJy6T6bSliE4efmabbA="}]}, "scripts": {}, "maintainers": [{"name": "avianflu", "email": "<EMAIL>"}], "directories": {}}, "0.1.0": {"name": "ncp", "version": "0.1.0", "author": {"name": "AvianFlu", "email": "<EMAIL>"}, "description": "Asynchronous recursive file copy utility.", "bin": {"ncp": "./bin/ncp"}, "main": "./lib/ncp.js", "repository": {"type": "git", "url": "git://github.com/AvianFlu/ncp.git"}, "keywords": ["cli", "copy"], "license": "MIT", "engine": {"node": ">=0.4"}, "_npmJsonOpts": {"file": "/home/<USER>/.npm/ncp/0.1.0/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "ncp@0.1.0", "dependencies": {}, "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.25", "_nodeVersion": "v0.4.9", "_defaultsLoaded": true, "dist": {"shasum": "059f51dcbda43f525d6f2de53369ca8bb88cb651", "tarball": "https://registry.npmjs.org/ncp/-/ncp-0.1.0.tgz", "integrity": "sha512-B8L+luYHF9415qBiuwKf923cdt/kRhkv2chazllYqpIK8x8iofTFwsMHvh1tgR7JZDWFhY7rvG+3XmYI0wCplg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGXaeswGAl8a9fLY7p4/xexH0pA10DFJD5OSvDbACXM0AiEAssBFQtu2ufuambl70TaoGbuC7wQDQqKDcZFovDKcIjA="}]}, "scripts": {}, "maintainers": [{"name": "avianflu", "email": "<EMAIL>"}], "directories": {}}, "0.1.1": {"name": "ncp", "version": "0.1.1", "author": {"name": "AvianFlu", "email": "<EMAIL>"}, "description": "Asynchronous recursive file copy utility.", "bin": {"ncp": "./bin/ncp"}, "main": "./lib/ncp.js", "repository": {"type": "git", "url": "git://github.com/AvianFlu/ncp.git"}, "keywords": ["cli", "copy"], "license": "MIT", "engine": {"node": ">=0.4"}, "_npmJsonOpts": {"file": "/home/<USER>/.npm/ncp/0.1.1/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "ncp@0.1.1", "dependencies": {}, "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.27", "_nodeVersion": "v0.4.9", "_defaultsLoaded": true, "dist": {"shasum": "a94e6677cd1ae774813fb0ef5b63bf2e32df3c3d", "tarball": "https://registry.npmjs.org/ncp/-/ncp-0.1.1.tgz", "integrity": "sha512-WtITvHvggso0N2kPQXrYqUoxJHqcSCw0Lna1scxm/LMXueAAyevRQ/5/PXje35ZLE7Q0GnTFLN+zSXa+Jg8TdQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICXOZjl/MHHseTv7LqWhm/1WC2wKJQHSm55IchJRyLu7AiBIY4qzih3bn8Oq43z4eOV8uRwEmzc2ulJFRcWUuP0qwA=="}]}, "maintainers": [{"name": "avianflu", "email": "<EMAIL>"}], "directories": {}}, "0.1.2": {"name": "ncp", "version": "0.1.2", "author": {"name": "AvianFlu", "email": "<EMAIL>"}, "description": "Asynchronous recursive file copy utility.", "bin": {"ncp": "./bin/ncp"}, "main": "./lib/ncp.js", "repository": {"type": "git", "url": "git://github.com/AvianFlu/ncp.git"}, "keywords": ["cli", "copy"], "license": "MIT", "engine": {"node": ">=0.4"}, "_npmJsonOpts": {"file": "/home/<USER>/.npm/ncp/0.1.2/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "ncp@0.1.2", "dependencies": {}, "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.27", "_nodeVersion": "v0.4.9", "_defaultsLoaded": true, "dist": {"shasum": "5f6bb78f4e74951ce7b9fd392f9d8a158f2557fc", "tarball": "https://registry.npmjs.org/ncp/-/ncp-0.1.2.tgz", "integrity": "sha512-uGyHyD78mSq16ONh+ZACziuTygO4w9slTgF6T0lsPv+kXofPtuhlejmQ7wTUZQ/IGVNJrGd54Oj3EPsHnlcCBA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDXCXvtOgm8q6HZeMQ5gAa1nCDG3vqIughWNudO3CendQIgWg3PMOvveSyIatOwVwgIQ3vEThSDe4SOvrU9T/KnMiE="}]}, "maintainers": [{"name": "avianflu", "email": "<EMAIL>"}], "directories": {}}, "0.2.0": {"name": "ncp", "version": "0.2.0", "author": {"name": "AvianFlu", "email": "<EMAIL>"}, "description": "Asynchronous recursive file copy utility.", "bin": {"ncp": "./bin/ncp"}, "main": "./lib/ncp.js", "repository": {"type": "git", "url": "git://github.com/AvianFlu/ncp.git"}, "keywords": ["cli", "copy"], "license": "MIT", "engine": {"node": ">=0.4"}, "_npmJsonOpts": {"file": "/home/<USER>/.npm/ncp/0.2.0/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "ncp@0.2.0", "dependencies": {}, "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.30", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"shasum": "35be4efc1c62dab442598ec5c7d4d420f210a436", "tarball": "https://registry.npmjs.org/ncp/-/ncp-0.2.0.tgz", "integrity": "sha512-kEx1Qrdh4aJcq+XmMpcaT2eUmtkJI9h8nxV3wepw1ysd+tbonwxcn9n027DiQ98tpCsWxW0H3m/GT480WUcPyw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDMG6wbAE60fNmrnPZeaJz2y2yHFTS71IHrqeqzlSb4fgIhALepABl34LP5tFzEKUCvZOXpBZ0MRZo/b8k0u6U6tqdM"}]}, "maintainers": [{"name": "avianflu", "email": "<EMAIL>"}], "directories": {}}, "0.2.1": {"name": "ncp", "version": "0.2.1", "author": {"name": "AvianFlu", "email": "<EMAIL>"}, "description": "Asynchronous recursive file copy utility.", "bin": {"ncp": "./bin/ncp"}, "main": "./lib/ncp.js", "repository": {"type": "git", "url": "git://github.com/AvianFlu/ncp.git"}, "keywords": ["cli", "copy"], "license": "MIT", "engine": {"node": ">=0.4"}, "_npmUser": {"name": "avianflu", "email": "<EMAIL>"}, "_id": "ncp@0.2.1", "dependencies": {}, "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.103", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"shasum": "16eb90193d629f01ff89358f34c36244420c4c5a", "tarball": "https://registry.npmjs.org/ncp/-/ncp-0.2.1.tgz", "integrity": "sha512-ZW1HxFt7bw6IVgQ08HapvaFFTdmovctjB/codmzvXTSZFRsCbGD9jZysJ5J5bCT2FcG27L9Z4BtD2+KWZSaN8Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGaHdd/ie7DumpEdl5DI0oDd9JB648u9Z6ITjKom+pHPAiAOv50l0CVlLy4zi9kKRRZZrdtF7NIsa6jP8vcb9wNTWA=="}]}, "maintainers": [{"name": "avianflu", "email": "<EMAIL>"}], "directories": {}}, "0.2.2": {"name": "ncp", "version": "0.2.2", "author": {"name": "AvianFlu", "email": "<EMAIL>"}, "description": "Asynchronous recursive file copy utility.", "bin": {"ncp": "./bin/ncp"}, "main": "./lib/ncp.js", "repository": {"type": "git", "url": "git://github.com/AvianFlu/ncp.git"}, "keywords": ["cli", "copy"], "license": "MIT", "engine": {"node": ">=0.4"}, "_npmUser": {"name": "avianflu", "email": "<EMAIL>"}, "_id": "ncp@0.2.2", "dependencies": {}, "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.105", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"shasum": "74b7bfc9f021837dd63b87096e8d0e3006b444c8", "tarball": "https://registry.npmjs.org/ncp/-/ncp-0.2.2.tgz", "integrity": "sha512-OYSkoD3MYg8n61/n8SEs/abrGGYOpHbWPvQykuEKn3NpXTsjruyYqhm2cu1NhST6Fchn1Iw5gMzbTd+R86dcLQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGbHsmUuzSVMwHNFKDsG+nCqjjiVK7EYr3EE/zlSnhO6AiBK1RXVApyaBo30RAPlSHXDo3QKe2uOwMpriopDIuD7wA=="}]}, "maintainers": [{"name": "avianflu", "email": "<EMAIL>"}], "directories": {}}, "0.2.3": {"name": "ncp", "version": "0.2.3", "author": {"name": "AvianFlu", "email": "<EMAIL>"}, "description": "Asynchronous recursive file copy utility.", "bin": {"ncp": "./bin/ncp"}, "main": "./lib/ncp.js", "repository": {"type": "git", "url": "git://github.com/AvianFlu/ncp.git"}, "keywords": ["cli", "copy"], "license": "MIT", "engine": {"node": ">=0.4"}, "_npmUser": {"name": "avianflu", "email": "<EMAIL>"}, "_id": "ncp@0.2.3", "dependencies": {}, "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.105", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"shasum": "3f9083927ea36fa7f3efe8502fefe5a886dc994e", "tarball": "https://registry.npmjs.org/ncp/-/ncp-0.2.3.tgz", "integrity": "sha512-dAwXRyeClWgzLE+POf3RRtVuzTrkDqHyVTucy19utwNOteppBBr32u967Mgs1A/Wq0UYRsszBDRREpkmr+qh6g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAU1NtTMFe0Pz5BxrNnR3MnLEqIhW+7FWaeiJ3M6qqNDAiBkVBUojaj27PYDxI3BhchXYoeZBGpJ6JxV+Qdm8aJvTQ=="}]}, "maintainers": [{"name": "avianflu", "email": "<EMAIL>"}], "directories": {}}, "0.2.4": {"name": "ncp", "version": "0.2.4", "author": {"name": "AvianFlu", "email": "<EMAIL>"}, "description": "Asynchronous recursive file copy utility.", "bin": {"ncp": "./bin/ncp"}, "devDependencies": {"vows": "0.6.x", "rimraf": "1.0.x", "read-dir-files": "0.0.x"}, "main": "./lib/ncp.js", "repository": {"type": "git", "url": "git://github.com/AvianFlu/ncp.git"}, "keywords": ["cli", "copy"], "license": "MIT", "engine": {"node": ">=0.4"}, "scripts": {"test": "vows --isolate --spec"}, "_npmUser": {"name": "avianflu", "email": "<EMAIL>"}, "_id": "ncp@0.2.4", "dependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.1.0-3", "_nodeVersion": "v0.6.10", "_defaultsLoaded": true, "dist": {"shasum": "595f66ae9da84b5e55dc6ddfb77d6052277c3db8", "tarball": "https://registry.npmjs.org/ncp/-/ncp-0.2.4.tgz", "integrity": "sha512-8y1MrokbLrOKqtK8MlJCPmo8fLPWpcmmws6ZtJ7YGl3UZAP4Fm70evkYJnbdOjKAmD6HB9PModf7xfbSFZHG4w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCCZkg3ZcOBmVTHanRf+9AGNYqF3LmxQG/xCP9Zkdj2zwIhALOi1aECMX0FRCYePWSHEFso9MSzZGR/sagHG6endIn0"}]}, "maintainers": [{"name": "avianflu", "email": "<EMAIL>"}], "directories": {}}, "0.2.5": {"name": "ncp", "version": "0.2.5", "author": {"name": "AvianFlu", "email": "<EMAIL>"}, "description": "Asynchronous recursive file copy utility.", "bin": {"ncp": "./bin/ncp"}, "devDependencies": {"vows": "0.6.x", "rimraf": "1.0.x", "read-dir-files": "0.0.x"}, "main": "./lib/ncp.js", "repository": {"type": "git", "url": "git://github.com/AvianFlu/ncp.git"}, "keywords": ["cli", "copy"], "license": "MIT", "engine": {"node": ">=0.4"}, "scripts": {"test": "vows --isolate --spec"}, "_npmUser": {"name": "avianflu", "email": "<EMAIL>"}, "_id": "ncp@0.2.5", "dependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.1.1", "_nodeVersion": "v0.6.10", "_defaultsLoaded": true, "dist": {"shasum": "f00f7a0247e33642896d55e42f27162541696308", "tarball": "https://registry.npmjs.org/ncp/-/ncp-0.2.5.tgz", "integrity": "sha512-ORAYfZpagYnxKZASZx6oc3qVe+RckW6cEY7Tc/dYooyXheoCTBL0uK0aKKwsgMPs1Sokj5k8ThLRRlpGbgQA1g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCW25NGncwNyzTxaGAyh2bW7dsgXmVEQyC11twbpBnIOwIgDM8rJUIgnoGmB39cdo5Zv3blsnWjGYP9RbYKN7o5c9s="}]}, "maintainers": [{"name": "avianflu", "email": "<EMAIL>"}], "directories": {}}, "0.2.6": {"name": "ncp", "version": "0.2.6", "author": {"name": "AvianFlu", "email": "<EMAIL>"}, "description": "Asynchronous recursive file copy utility.", "bin": {"ncp": "./bin/ncp"}, "devDependencies": {"vows": "0.6.x", "rimraf": "1.0.x", "read-dir-files": "0.0.x"}, "main": "./lib/ncp.js", "repository": {"type": "git", "url": "git://github.com/AvianFlu/ncp.git"}, "keywords": ["cli", "copy"], "license": "MIT", "engine": {"node": ">=0.4"}, "scripts": {"test": "vows --isolate --spec"}, "_npmUser": {"name": "avianflu", "email": "<EMAIL>"}, "_id": "ncp@0.2.6", "dependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.1.4", "_nodeVersion": "v0.6.11", "_defaultsLoaded": true, "dist": {"shasum": "87f5a7587e35454ea5f977a2178e6ebeab6eaf01", "tarball": "https://registry.npmjs.org/ncp/-/ncp-0.2.6.tgz", "integrity": "sha512-GI2nk8/v56u4U/rK4dMyoq6a2Gii8BjXeSpEy8ynY9LL57EUJc7+EBlEdvFKjhVNaCKew/tGA0p6khvZd/nk2A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBflqsl3N/MyXdaelg5nRl/Zai1gi8RJnSb2upWa+PuGAiEAqGDvcDHMRc6XHK+zpbB/nuyZqhSMHwR7KEXPzmiInQk="}]}, "maintainers": [{"name": "avianflu", "email": "<EMAIL>"}], "directories": {}}, "0.3.0": {"name": "ncp", "version": "0.3.0", "author": {"name": "AvianFlu", "email": "<EMAIL>"}, "description": "Asynchronous recursive file copy utility.", "bin": {"ncp": "./bin/ncp"}, "devDependencies": {"vows": "0.6.x", "rimraf": "1.0.x", "read-dir-files": "0.0.x"}, "main": "./lib/ncp.js", "repository": {"type": "git", "url": "https://github.com/AvianFlu/ncp.git"}, "keywords": ["cli", "copy"], "license": "MIT", "engine": {"node": ">=0.4"}, "scripts": {"test": "vows --isolate --spec"}, "_id": "ncp@0.3.0", "dist": {"shasum": "5d9e39b4570801abe02b0c9fd15b59b161ce88fb", "tarball": "https://registry.npmjs.org/ncp/-/ncp-0.3.0.tgz", "integrity": "sha512-pzfukl09UK8b9U/B/mmY/MgLvIyTmcBtkSoMjGOqgMQRkBPmPh8R7qDTKlIcgJfMLlG6jrum3UybA3E/rdGELw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHZ7DSiKIintLjd9ZoPFTpVI4qMWDuxWuVIi9MZft6YRAiAHQFh4gwZyq5QXCGrvrqQlCjG53eSdybP7HFFspoDVMw=="}]}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "avianflu", "email": "<EMAIL>"}, "maintainers": [{"name": "avianflu", "email": "<EMAIL>"}], "directories": {}}, "0.4.0": {"name": "ncp", "version": "0.4.0", "author": {"name": "AvianFlu", "email": "<EMAIL>"}, "description": "Asynchronous recursive file copy utility.", "bin": {"ncp": "./bin/ncp"}, "devDependencies": {"vows": "0.6.x", "rimraf": "1.0.x", "read-dir-files": "0.0.x"}, "main": "./lib/ncp.js", "repository": {"type": "git", "url": "https://github.com/AvianFlu/ncp.git"}, "keywords": ["cli", "copy"], "license": "MIT", "engine": {"node": ">=0.4"}, "scripts": {"test": "vows --isolate --spec"}, "_id": "ncp@0.4.0", "dist": {"shasum": "a202c36a9d6898d819c8980d23ef6c95bdef14e1", "tarball": "https://registry.npmjs.org/ncp/-/ncp-0.4.0.tgz", "integrity": "sha512-Xnn1Ldgso720PflKEjBwkkZc8CRpADwC4NNPLTRg7XibxPxMoEmFCfRjO9QAMbXe4yc1EaiZmDLBfn/IXmiFIg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICrxd02gBuKy0Et4y8kgBrOK2SR3aXLjaQ9qTFyg+qWMAiAs0n6v0Hgo1nVBLa+mXNgYU7voiMQTWwcPO49/SZfG8A=="}]}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "avianflu", "email": "<EMAIL>"}, "maintainers": [{"name": "avianflu", "email": "<EMAIL>"}], "directories": {}}, "0.2.7": {"name": "ncp", "version": "0.2.7", "author": {"name": "AvianFlu", "email": "<EMAIL>"}, "description": "Asynchronous recursive file copy utility.", "bin": {"ncp": "./bin/ncp"}, "devDependencies": {"vows": "0.6.x", "rimraf": "1.0.x", "read-dir-files": "0.0.x"}, "main": "./lib/ncp.js", "repository": {"type": "git", "url": "https://github.com/AvianFlu/ncp.git"}, "keywords": ["cli", "copy"], "license": "MIT", "engine": {"node": ">=0.4"}, "scripts": {"test": "vows --isolate --spec"}, "_id": "ncp@0.2.7", "dist": {"shasum": "46fac2b7dda2560a4cb7e628677bd5f64eac5be1", "tarball": "https://registry.npmjs.org/ncp/-/ncp-0.2.7.tgz", "integrity": "sha512-wPUepcV37u3Mw+ktjrUbl3azxwAkcD9RrVLQGlpSapWcEQM5jL0g8zwKo6ukOjVQAAEjqpRdLeojOalqqySpCg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICpxrKLns+p6+AE9Y/bWaWAS1pDYKgtOusGrQM3hHxGVAiEAyBx002E3HWDFHLGYcBXPsOZehPmW4/Dbywx7OWoAem4="}]}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "avianflu", "email": "<EMAIL>"}, "maintainers": [{"name": "avianflu", "email": "<EMAIL>"}], "directories": {}}, "0.4.1": {"name": "ncp", "version": "0.4.1", "author": {"name": "AvianFlu", "email": "<EMAIL>"}, "description": "Asynchronous recursive file copy utility.", "bin": {"ncp": "./bin/ncp"}, "devDependencies": {"vows": "0.6.x", "rimraf": "1.0.x", "read-dir-files": "0.0.x"}, "main": "./lib/ncp.js", "repository": {"type": "git", "url": "https://github.com/AvianFlu/ncp.git"}, "keywords": ["cli", "copy"], "license": "MIT", "engine": {"node": ">=0.4"}, "scripts": {"test": "vows --isolate --spec"}, "_id": "ncp@0.4.1", "dist": {"shasum": "f20112f496505847386e29f582e0525071f3f2c5", "tarball": "https://registry.npmjs.org/ncp/-/ncp-0.4.1.tgz", "integrity": "sha512-rrU1V8XJQJBeWrT27viUoOsXKkD2FrrqGYbSYcOCN/gsM3p1wwdKs94GRGC8vBHLFqSR9HEWsQjXr9G4+p2I+g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCHXORfC85RNDE5W2/a8uZT4WmQe/5sV4mLkQ8YCf7SxgIhAIP5ioPEw4khnuPZwD97MS3v1/iGF/no4IErCmfnB5dO"}]}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "avianflu", "email": "<EMAIL>"}, "maintainers": [{"name": "avianflu", "email": "<EMAIL>"}], "directories": {}}, "0.4.2": {"name": "ncp", "version": "0.4.2", "author": {"name": "AvianFlu", "email": "<EMAIL>"}, "description": "Asynchronous recursive file copy utility.", "bin": {"ncp": "./bin/ncp"}, "devDependencies": {"vows": "0.6.x", "rimraf": "1.0.x", "read-dir-files": "0.0.x"}, "main": "./lib/ncp.js", "repository": {"type": "git", "url": "https://github.com/AvianFlu/ncp.git"}, "keywords": ["cli", "copy"], "license": "MIT", "engine": {"node": ">=0.4"}, "scripts": {"test": "vows --isolate --spec"}, "_id": "ncp@0.4.2", "dist": {"shasum": "abcc6cbd3ec2ed2a729ff6e7c1fa8f01784a8574", "tarball": "https://registry.npmjs.org/ncp/-/ncp-0.4.2.tgz", "integrity": "sha512-PfGU8jYWdRl4FqJfCy0IzbkGyFHntfWygZg46nFk/dJD/XRrk2cj0SsKSX9n5u5gE0E0YfEpKWrEkfjnlZSTXA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC4kk0tmLpVwi5HpHRctO80GI2s3Wa/X+g5nyDLue3jvAiEAprLoYwbQWdUBSfcEs0CY1iuBp+RU9f7gbC8tJRT9pk0="}]}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "avianflu", "email": "<EMAIL>"}, "maintainers": [{"name": "avianflu", "email": "<EMAIL>"}], "directories": {}}, "0.5.0": {"name": "ncp", "version": "0.5.0", "author": {"name": "AvianFlu", "email": "<EMAIL>"}, "description": "Asynchronous recursive file copy utility.", "bin": {"ncp": "./bin/ncp"}, "devDependencies": {"mocha": "1.15.x", "rimraf": "1.0.x", "read-dir-files": "0.0.x"}, "main": "./lib/ncp.js", "repository": {"type": "git", "url": "https://github.com/AvianFlu/ncp.git"}, "keywords": ["cli", "copy"], "license": "MIT", "engine": {"node": ">=0.6"}, "scripts": {"test": "mocha -R spec"}, "bugs": {"url": "https://github.com/AvianFlu/ncp/issues"}, "homepage": "https://github.com/AvianFlu/ncp", "_id": "ncp@0.5.0", "dist": {"shasum": "2a9ca894d519a84e0d446087385f8706548263dd", "tarball": "https://registry.npmjs.org/ncp/-/ncp-0.5.0.tgz", "integrity": "sha512-H+8Wp3KnKizlNKGGudDV6TknvFiEQlrvNFpULf8NHWs8tIqqRZUsvtzEWzD9KiQj00EwQ6JQ57Y21l5q5W5EIQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHmAl2xpKws2oUJQiMXMc0bvcirdZEmNpJPO3Kwk3bn1AiARUENTdS9wlkKiIn9Av123zwcfXwKkBBgkV15zGlBd4g=="}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "avianflu", "email": "<EMAIL>"}, "maintainers": [{"name": "avianflu", "email": "<EMAIL>"}], "directories": {}}, "0.5.1": {"name": "ncp", "version": "0.5.1", "author": {"name": "AvianFlu", "email": "<EMAIL>"}, "description": "Asynchronous recursive file copy utility.", "bin": {"ncp": "./bin/ncp"}, "devDependencies": {"mocha": "1.15.x", "rimraf": "1.0.x", "read-dir-files": "0.0.x"}, "main": "./lib/ncp.js", "repository": {"type": "git", "url": "https://github.com/AvianFlu/ncp.git"}, "keywords": ["cli", "copy"], "license": "MIT", "engine": {"node": ">=0.6"}, "scripts": {"test": "mocha -R spec"}, "bugs": {"url": "https://github.com/AvianFlu/ncp/issues"}, "homepage": "https://github.com/AvianFlu/ncp", "_id": "ncp@0.5.1", "dist": {"shasum": "743985316e3db459281b587169e845735a05439f", "tarball": "https://registry.npmjs.org/ncp/-/ncp-0.5.1.tgz", "integrity": "sha512-l+pJxuLlzwp11Dy72MJgCPNwIbXdv6imaACLiEMb2TIDyr54qz+nAZeD5qDlJefveaJ+R9Ug6KuozCxRpQXO0Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB40XUdhjatWDuoHtLJhaUcXMabj2VWhFkDyhrVlggzvAiAQbp9khZhL/okDJ1D2X20JSlfGW5F403ElJOUMKjZt7w=="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "avianflu", "email": "<EMAIL>"}, "maintainers": [{"name": "avianflu", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.6.0": {"name": "ncp", "version": "0.6.0", "author": {"name": "AvianFlu", "email": "<EMAIL>"}, "description": "Asynchronous recursive file copy utility.", "bin": {"ncp": "./bin/ncp"}, "devDependencies": {"mocha": "1.15.x", "rimraf": "1.0.x", "read-dir-files": "0.0.x"}, "main": "./lib/ncp.js", "repository": {"type": "git", "url": "https://github.com/AvianFlu/ncp.git"}, "keywords": ["cli", "copy"], "license": "MIT", "engine": {"node": ">=0.6"}, "scripts": {"test": "mocha -R spec"}, "bugs": {"url": "https://github.com/AvianFlu/ncp/issues"}, "homepage": "https://github.com/AvianFlu/ncp", "_id": "ncp@0.6.0", "dist": {"shasum": "df8ce021e262be21b52feb3d3e5cfaab12491f0d", "tarball": "https://registry.npmjs.org/ncp/-/ncp-0.6.0.tgz", "integrity": "sha512-z5F/duoqgCu095ADYgO0rNh1z7Yc1AA6VQk9tUOEwd8sPeCwTMPRST+f0UiDBLAEQ3joAgdKqSCRs09sgvU1xA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCEfoObMzQRoEUsn+OvWg3fNSQKds4f38yJdf9SXAqUYQIhAK/GSngER2A2HWWKU4e+ygckjf6DLuO653292l0IETCa"}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "avianflu", "email": "<EMAIL>"}, "maintainers": [{"name": "avianflu", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.0.0": {"name": "ncp", "version": "1.0.0", "author": {"name": "AvianFlu", "email": "<EMAIL>"}, "description": "Asynchronous recursive file copy utility.", "bin": {"ncp": "./bin/ncp"}, "devDependencies": {"mocha": "1.15.x", "rimraf": "1.0.x", "read-dir-files": "0.0.x"}, "main": "./lib/ncp.js", "repository": {"type": "git", "url": "https://github.com/AvianFlu/ncp.git"}, "keywords": ["cli", "copy"], "license": "MIT", "engine": {"node": ">=0.8"}, "scripts": {"test": "mocha -R spec"}, "gitHead": "81b3d3b325cb5f484b37795d691dc2f8eee5c1e7", "bugs": {"url": "https://github.com/AvianFlu/ncp/issues"}, "homepage": "https://github.com/AvianFlu/ncp", "_id": "ncp@1.0.0", "_shasum": "0b8fcdd0e93c533f291cba79744a2db3bf0e6f8e", "_from": ".", "_npmVersion": "2.0.2", "_nodeVersion": "0.10.32", "_npmUser": {"name": "avianflu", "email": "<EMAIL>"}, "maintainers": [{"name": "avianflu", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "0b8fcdd0e93c533f291cba79744a2db3bf0e6f8e", "tarball": "https://registry.npmjs.org/ncp/-/ncp-1.0.0.tgz", "integrity": "sha512-pBCNI6pHlJmYfZitJAb8sSKoaOAbmOMYZFaGUTyRr9BkhdkHA4tjUinn4olMA/yH3HJCDYv12UHW3PLWHaRXyg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQClfkGFHjOR7yjSlsQRMISy7fbQtVnpF5wM4JD49NEhUgIgL0vd23u6p6Y6wLX00uWnwZAM2Zy148OdLx4Kc/IiDZs="}]}, "directories": {}}, "1.0.1": {"name": "ncp", "version": "1.0.1", "author": {"name": "AvianFlu", "email": "<EMAIL>"}, "description": "Asynchronous recursive file copy utility.", "bin": {"ncp": "./bin/ncp"}, "devDependencies": {"mocha": "1.15.x", "rimraf": "1.0.x", "read-dir-files": "0.0.x"}, "main": "./lib/ncp.js", "repository": {"type": "git", "url": "https://github.com/AvianFlu/ncp.git"}, "keywords": ["cli", "copy"], "license": "MIT", "engine": {"node": ">=0.8"}, "scripts": {"test": "mocha -R spec"}, "gitHead": "e9e6fae157e94e6c55b8edae70779bf12b0d2476", "bugs": {"url": "https://github.com/AvianFlu/ncp/issues"}, "homepage": "https://github.com/AvianFlu/ncp", "_id": "ncp@1.0.1", "_shasum": "d15367e5cb87432ba117d2bf80fdf45aecfb4246", "_from": ".", "_npmVersion": "2.1.2", "_nodeVersion": "0.10.32", "_npmUser": {"name": "avianflu", "email": "<EMAIL>"}, "maintainers": [{"name": "avianflu", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "d15367e5cb87432ba117d2bf80fdf45aecfb4246", "tarball": "https://registry.npmjs.org/ncp/-/ncp-1.0.1.tgz", "integrity": "sha512-akBX7I5X9KQDDWmYYgQlLbVbjkveTje2mioZjhLLrVt09akSZcoqXWE5LEn1E2fu8T7th1PZYGfewQsTkTLTmQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHzsPkgOqtQO4xfEDknh1N6jKxsEDW5AVgmLvUY4UKtZAiBb2553YNdpJ7Ilaq8aGcnGLVIwOmUabIms+pMkdG/p8w=="}]}, "directories": {}}, "2.0.0": {"name": "ncp", "version": "2.0.0", "author": {"name": "AvianFlu", "email": "<EMAIL>"}, "description": "Asynchronous recursive file copy utility.", "bin": {"ncp": "./bin/ncp"}, "devDependencies": {"mocha": "1.15.x", "rimraf": "1.0.x", "read-dir-files": "0.0.x"}, "main": "./lib/ncp.js", "repository": {"type": "git", "url": "https://github.com/AvianFlu/ncp.git"}, "keywords": ["cli", "copy"], "license": "MIT", "engine": {"node": ">=0.10"}, "scripts": {"test": "mocha -R spec"}, "gitHead": "93c7c6c719e2a4944dc09a16178b09aef428cdf0", "bugs": {"url": "https://github.com/AvianFlu/ncp/issues"}, "homepage": "https://github.com/AvianFlu/ncp", "_id": "ncp@2.0.0", "_shasum": "195a21d6c46e361d2fb1281ba38b91e9df7bdbb3", "_from": ".", "_npmVersion": "2.6.0", "_nodeVersion": "0.13.0-pre", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "avianflu", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "195a21d6c46e361d2fb1281ba38b91e9df7bdbb3", "tarball": "https://registry.npmjs.org/ncp/-/ncp-2.0.0.tgz", "integrity": "sha512-zIdGUrPRFTUELUvr3Gmc7KZ2Sw/h1PiVM0Af/oHB6zgnV1ikqSfRk+TOufi79aHYCW3NiOXmr1BP5nWbzojLaA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEfhgRpDVRmG9wtZ998gn8NsBG6uebP2jDUEyVZxuTi6AiA1bIcNlWtK8lVHnfCBZD+AchhiT6EQgCuuafzxrJUfVg=="}]}, "directories": {}}}, "maintainers": [{"name": "avianflu", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2022-06-20T22:42:18.438Z", "created": "2011-08-25T00:42:56.649Z", "0.0.0": "2011-08-25T00:42:58.122Z", "0.0.1": "2011-08-25T01:16:44.642Z", "0.0.2": "2011-08-25T08:55:36.028Z", "0.1.0": "2011-08-26T06:29:36.199Z", "0.1.1": "2011-08-27T05:54:31.461Z", "0.1.2": "2011-08-27T21:41:40.327Z", "0.2.0": "2011-09-18T03:45:39.737Z", "0.2.1": "2011-11-04T07:40:57.523Z", "0.2.2": "2011-11-28T17:40:44.763Z", "0.2.3": "2011-12-05T18:06:54.822Z", "0.2.4": "2012-02-11T22:43:20.005Z", "0.2.5": "2012-02-27T16:36:43.254Z", "0.2.6": "2012-03-11T20:09:19.561Z", "0.3.0": "2013-02-05T13:38:43.020Z", "0.4.0": "2013-02-13T17:39:40.689Z", "0.4.1": "2013-03-12T16:52:08.349Z", "0.2.7": "2013-03-12T16:51:22.908Z", "0.4.2": "2013-03-12T17:03:20.086Z", "0.5.0": "2013-12-19T17:00:31.273Z", "0.5.1": "2014-04-18T15:10:11.827Z", "0.6.0": "2014-07-08T20:01:55.343Z", "1.0.0": "2014-09-24T14:38:00.986Z", "1.0.1": "2014-10-02T06:03:54.123Z", "2.0.0": "2015-02-22T20:55:26.456Z"}, "author": {"name": "AvianFlu", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/AvianFlu/ncp.git"}, "users": {"avianflu": true, "fgribreau": true, "hij1nx": true, "werle": true, "jamesmgreene": true, "pid": true, "timur.shemsedinov": true, "h2non": true, "jacoborus": true, "kahboom": true, "xgenvn": true, "maschs": true, "hiwanz": true, "fill": true, "johnnychq": true, "alxe.master": true, "ajduke": true, "pythondave": true, "yashprit": true, "tunderdomb": true, "n0n1": true, "markuswaltre": true, "mutoo": true, "jason0518": true, "detj": true, "davequick": true, "joeyespo": true, "viz": true, "axo": true, "algonzo": true, "evanyeung": true, "panlw": true, "foto": true, "piecioshka": true, "moimikey": true, "animabear": true, "zoxon": true, "adamlu": true, "majgis": true, "tongjieme": true, "teaera": true, "nguru": true, "program247365": true, "wangnan0610": true, "programmer.severson": true, "usingthesystem": true, "pfuri": true, "amd940": true, "monjer": true, "shanewholloway": true, "zhongyuan": true, "olonam": true, "itonyyo": true, "alexchao": true, "skymap": true, "dheerajvs": true, "pstoeckle": true, "xueboren": true, "dankle": true, "shaddyhm": true, "pddivine": true, "santhoshbabu": true, "wujr5": true, "hugojosefson": true, "dm7": true, "drewigg": true, "tedyhy": true, "xtx1130": true, "allen_lyu": true, "diegorbaquero": true, "debashish": true, "nuwaio": true, "ungurys": true}, "readme": "# ncp - Asynchronous recursive file & directory copying\n\n[![Build Status](https://secure.travis-ci.org/AvianFlu/ncp.png)](http://travis-ci.org/AvianFlu/ncp)\n\nThink `cp -r`, but pure node, and asynchronous.  `ncp` can be used both as a CLI tool and programmatically.\n\n## Command Line usage\n\nUsage is simple: `ncp [source] [dest] [--limit=concurrency limit]\n[--filter=filter] --stopOnErr`\n\nThe 'filter' is a Regular Expression - matched files will be copied.\n\nThe 'concurrency limit' is an integer that represents how many pending file system requests `ncp` has at a time.\n\n'stoponerr' is a boolean flag that will tell `ncp` to stop immediately if any\nerrors arise, rather than attempting to continue while logging errors. The default behavior is to complete as many copies as possible, logging errors along the way.\n\nIf there are no errors, `ncp` will output `done.` when complete.  If there are errors, the error messages will be logged to `stdout` and to `./ncp-debug.log`, and the copy operation will attempt to continue.\n\n## Programmatic usage\n\nProgrammatic usage of `ncp` is just as simple.  The only argument to the completion callback is a possible error.  \n\n```javascript\nvar ncp = require('ncp').ncp;\n\nncp.limit = 16;\n\nncp(source, destination, function (err) {\n if (err) {\n   return console.error(err);\n }\n console.log('done!');\n});\n```\n\nYou can also call ncp like `ncp(source, destination, options, callback)`. \n`options` should be a dictionary. Currently, such options are available:\n\n  * `options.filter` - a `RegExp` instance, against which each file name is\n  tested to determine whether to copy it or not, or a function taking single\n  parameter: copied file name, returning `true` or `false`, determining\n  whether to copy file or not.\n\n  * `options.transform` - a function: `function (read, write) { read.pipe(write) }`\n  used to apply streaming transforms while copying.\n\n  * `options.clobber` - boolean=true. if set to false, `ncp` will not overwrite \n  destination files that already exist.\n\n  * `options.dereference` - boolean=false. If set to true, `ncp` will follow symbolic\n  links. For example, a symlink in the source tree pointing to a regular file\n  will become a regular file in the destination tree. Broken symlinks will result in\n  errors.\n\n  * `options.stopOnErr` - boolean=false.  If set to true, `ncp` will behave like `cp -r`,\n  and stop on the first error it encounters. By default, `ncp` continues copying, logging all\n  errors and returning an array.\n\n  * `options.errs` - stream. If `options.stopOnErr` is `false`, a stream can be provided, and errors will be written to this stream.\n\nPlease open an issue if any bugs arise.  As always, I accept (working) pull requests, and refunds are available at `/dev/null`.\n", "homepage": "https://github.com/AvianFlu/ncp", "keywords": ["cli", "copy"], "bugs": {"url": "https://github.com/AvianFlu/ncp/issues"}, "license": "MIT", "readmeFilename": "README.md"}