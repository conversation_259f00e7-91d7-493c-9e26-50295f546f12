{"_id": "available-typed-arrays", "_rev": "8-f67894477d36200c59bdb4aebc1bea85", "name": "available-typed-arrays", "dist-tags": {"latest": "1.0.7"}, "versions": {"1.0.0": {"name": "available-typed-arrays", "version": "1.0.0", "description": "Returns an array of Typed Array names that are available in the current environment", "main": "index.js", "scripts": {"prepublish": "safe-publish-latest", "prelint": "evalmd README.md", "lint": "eslint .", "pretest": "npm run lint", "tests-only": "node test", "test": "npm run tests-only", "posttest": "npx aud", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/available-typed-arrays.git"}, "keywords": ["typed", "arrays", "Float32Array", "Float64Array", "Int8Array", "Int16Array", "Int32Array", "Uint8Array", "Uint8ClampedArray", "Uint16Array", "Uint32Array", "BigInt64Array", "BigUint64Array"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/available-typed-arrays/issues"}, "homepage": "https://github.com/inspect-js/available-typed-arrays#readme", "engines": {"node": ">= 0.4"}, "devDependencies": {"@ljharb/eslint-config": "^15.1.0", "array.prototype.every": "^1.1.0", "auto-changelog": "^1.16.2", "eslint": "^6.8.0", "evalmd": "^0.0.19", "isarray": "^2.0.5", "safe-publish-latest": "^1.1.4", "tape": "^5.0.0-next.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "dependencies": {"array-filter": "^1.0.0"}, "gitHead": "013987241993b7447915e4129506d7dd917570e0", "_id": "available-typed-arrays@1.0.0", "_nodeVersion": "13.7.0", "_npmVersion": "6.13.6", "dist": {"integrity": "sha512-IGFg4IjmQpIPx/j6+VJhUcjhYJOtDG/mvnJK2nlVgtLZhgryVCZ65TuQg7wyqYyE0QK3fqj3/GYVYPy4xnBhEQ==", "shasum": "2d3e2d7b0846523a47b631d6c3ab25fc59541c54", "tarball": "https://registry.npmjs.org/available-typed-arrays/-/available-typed-arrays-1.0.0.tgz", "fileCount": 8, "unpackedSize": 7466, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeKrkYCRA9TVsSAnZWagAAnOIP/0lBTKM14nXRhncroZyZ\nONY06u48TO0jWheBy8G31myNFc788D9Ss0NmkyAR/XaKgn5sPBFjvt7/kEvg\nsdQVhFygJsOffy7KTQJeL8SfwNZ5WaDtjM+wtlnQTYOl5S5xbaehig28GAcL\ner7+1MjuKFeSYpGPzlnYWR5pEGOc3fCcJ9WJy2TuR7DE7qTW9pwWZoksukkH\nf4ETHlq+d+vZRJrmeTTxAJCKvjiBSYKDOad8rx+oZ/TPZk7sLbPY6SSv95pJ\ndAHdR35dRfy5jVPzdiJ3b24PFXRb/tG+u8b57x3rXQclHQfXNQTtJwzSYw3L\n9OY3gjd1dwlDyBc3sfL4c20H/PW9N7ScIDSHq153xVjeaHZLfxB01NCswt3X\nCz/zvwOKEKhcS974wH23xgIhFjWF3wLN2nmRugIRGCxnGssa20lNzel3uePN\nEzR3svGss8H/kx1tHtmu48NONi9TgW44UuzGuGY8vNQ1VWaYTR0Ys9XZxgCU\n9ejB9ASxxg/XQ7GqpVebi8b3wv4QNL6S6vvy3o63hdFDYFm6eNT299jPzNGs\nAxcnCskP/NJLpCoDAQJpKDA80hKqAyu9g83z55rjBO8FSNOxTmNX6GcviKYr\nNDgNDEjA58q9F6o6igUMIb9zU7VZ/fBFMhEV/SDLO6lJwl7nGyjMYG1r55cn\nodAX\r\n=pnHo\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDOlByXleboOepBLAFUpTSr9aTi+TPyJr05xrEqBphZRQIhAJHwSIe+uzkoFuNwpTl5V7ZvU90Rdv4U6UbZI2evRBZN"}]}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/available-typed-arrays_1.0.0_1579858199841_0.27181267351846605"}, "_hasShrinkwrap": false}, "1.0.1": {"name": "available-typed-arrays", "version": "1.0.1", "description": "Returns an array of Typed Array names that are available in the current environment", "main": "index.js", "type": "commonjs", "exports": {".": [{"require": "./index.js", "default": "./index.js"}, "./index.js"], "./package": "./package.json", "./package.json": "./package.json"}, "scripts": {"prepublish": "safe-publish-latest", "prelint": "evalmd README.md", "lint": "eslint .", "pretest": "npm run lint", "tests-only": "node test", "test": "npm run tests-only", "posttest": "npx aud", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/available-typed-arrays.git"}, "keywords": ["typed", "arrays", "Float32Array", "Float64Array", "Int8Array", "Int16Array", "Int32Array", "Uint8Array", "Uint8ClampedArray", "Uint16Array", "Uint32Array", "BigInt64Array", "BigUint64Array"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/available-typed-arrays/issues"}, "homepage": "https://github.com/inspect-js/available-typed-arrays#readme", "engines": {"node": ">= 0.4"}, "devDependencies": {"@ljharb/eslint-config": "^15.1.0", "array.prototype.every": "^1.1.0", "auto-changelog": "^1.16.2", "eslint": "^6.8.0", "evalmd": "^0.0.19", "isarray": "^2.0.5", "safe-publish-latest": "^1.1.4", "tape": "^5.0.0-next.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "dependencies": {"array-filter": "^1.0.0"}, "gitHead": "28e0709cb723c4373cbeca3d3e690331c3b824a9", "_id": "available-typed-arrays@1.0.1", "_nodeVersion": "13.7.0", "_npmVersion": "6.13.6", "dist": {"integrity": "sha512-Zo/GKz+tytvGfpbqSckhEm/moLI08UhK62Nus5uJjJHqwUFdTv9Q9NQAgntlvD83uPcEixZNzhm7dMtfCzrr0Q==", "shasum": "0065ccef4a3f4894db02e1f6a72bcfb8f3f1b98d", "tarball": "https://registry.npmjs.org/available-typed-arrays/-/available-typed-arrays-1.0.1.tgz", "fileCount": 8, "unpackedSize": 7928, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeKyI4CRA9TVsSAnZWagAA+ekQAJVQbszBC4KG8CFw7Rl3\nFcsqqEjN/8WRhmWsYmP14YRFIZroPhj1crU46kyE5z/nfiofwdW+R1OCHQZc\nmr6N5wJ9tv5M5RIwh9XDg+mp5oGDRKJs1ilKTrsCYVcFrRE/SOR3MwTg2JFh\n8/GzlZmLWo2IBUdUmlGOJMkNC9Cp7bNpG1DewrLtYVIUGpP1a5+/+HcMIDOX\nXCDOmwQKylB26EtOILftVR7b/f33OfgwX5aGwN2Za8Dx2can8PDZA9+qz3io\ng3AhgUPyPKVFsPVssW9GJNZcfzKdv/awA9t85rC/yKZBaMt92oNjkMCRjOeg\nq94BRkAdkq/s5xyRAwDN+7ya4jGl0MjsxU3qfdidjBmYZIGVYKuqhqjGOT5u\nHifIyHBFGAe5R6GFCSR+FdllPyy7LIOpbAhvC1Sb7DLJY8W9/hMv0i8kVDam\nD2AVPkZmSrlk/16NUlUrX0iuA7ltI37+iSxXXwW81nMW38No73mbnI1KdXim\ngL1ycq0J+C3X654VSTCmEvL1XH6O2fNhimRXsI4FECcNWAUZ2LNALGRI8Vo2\nJELoziRoxXVoq6Dy3ZzzW9IuWND0bdN4JP7oYgEtbzFB79J/QJRiuC2Iny2H\nLJnm5EMCTratRXPdTDF3VFOyjK39BdFKKFLodvqbhgYyt2F6kG/SFDkgnlZt\nHdXE\r\n=yKFz\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDBfd0yuKvI257p7eKNzz3TBy7h6yrb4y/HVvVpwU4uUAiEAwGSWvhn2CIPmBX9P7+gYTYCXy6vYqzf1mGv0riHDKts="}]}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/available-typed-arrays_1.0.1_1579885111828_0.31777765501901256"}, "_hasShrinkwrap": false}, "1.0.2": {"name": "available-typed-arrays", "version": "1.0.2", "description": "Returns an array of Typed Array names that are available in the current environment", "main": "index.js", "type": "commonjs", "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package": "./package.json", "./package.json": "./package.json"}, "scripts": {"prepublish": "safe-publish-latest", "prelint": "evalmd README.md", "lint": "eslint .", "pretest": "npm run lint", "tests-only": "node test", "test": "npm run tests-only", "posttest": "npx aud", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/available-typed-arrays.git"}, "keywords": ["typed", "arrays", "Float32Array", "Float64Array", "Int8Array", "Int16Array", "Int32Array", "Uint8Array", "Uint8ClampedArray", "Uint16Array", "Uint32Array", "BigInt64Array", "BigUint64Array"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/available-typed-arrays/issues"}, "homepage": "https://github.com/inspect-js/available-typed-arrays#readme", "engines": {"node": ">= 0.4"}, "devDependencies": {"@ljharb/eslint-config": "^16.0.0", "array.prototype.every": "^1.1.0", "auto-changelog": "^1.16.2", "eslint": "^6.8.0", "evalmd": "^0.0.19", "isarray": "^2.0.5", "safe-publish-latest": "^1.1.4", "tape": "^5.0.0-next.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "dependencies": {"array-filter": "^1.0.0"}, "gitHead": "7f44c74097f86c7a8a6f469fda4b9ebbb68c5f96", "_id": "available-typed-arrays@1.0.2", "_nodeVersion": "13.7.0", "_npmVersion": "6.13.6", "dist": {"integrity": "sha512-XWX3OX8Onv97LMk/ftVyBibpGwY5a8SmuxZPzeOxqmuEqUCOM9ZE+uIaD1VNJ5QnvU2UQusvmKbuM1FR8QWGfQ==", "shasum": "6b098ca9d8039079ee3f77f7b783c4480ba513f5", "tarball": "https://registry.npmjs.org/available-typed-arrays/-/available-typed-arrays-1.0.2.tgz", "fileCount": 9, "unpackedSize": 8777, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeLUhsCRA9TVsSAnZWagAAecQP/jujq3ghnWn5bVhTZ3VX\nA7pfH41Xs8pBREIeWXzBS0+3AF95iCqFxLwwU9l2pfxMpfJOcu7Y6TThw09s\nPlx2clYe6kZxOWQ4cODW0gxbK8hLJOdl4/wh/KSSt7MzRGDXHYJwav0k+Hgn\nuw2eSntyJ1OckTvxsoQHUqUH6bt0YY8bt/MzrQ0qy4a6YhYka2GHbGsSPpL+\nwmKZZLtXuHGoGnc9+AhyOii39TyGUmYoE630AZqLly1ktpJvbvSa+X4ecPZH\nPPLACwTGsSTMtUig7XuPPtUUGY+Blz7HOTrhR6FEW2Fn3xtXJcU5lWjf1Co1\nSNJuJ0ThLdvtER4hAAqFjqGTioTbSoiTAx3e2ORPOED1cdHZD+2TgZLXXpLw\nuCdXjdT2Bx6rSloViEGF4UXXjS9nsoh3B5KLANkb9zBkxsE8m4Cy4PjtJ1AN\nFrweUpMOnPwpPRdqZVcV5jBkMG3vEQqnXx63jHwcCsLPWGWU1OrFHDXxQ/xb\nAdOSDEv+7+idDvpRpadiqWi/oX9pEMi+zpnQMotjOQqBxTIi2r4vZhF3D6+r\nQ6tggCIPPhtjBWm2nCgI4tCYIJg6YbuWHyTSh4oaVeRmO6gTpgPHBO4MOOMt\nygPc/ipAd66Pw1qMg4xAhw8Mmi8FoRl5jVYYE1mscLSlQfp3bIphCSu1amUg\nWdte\r\n=xsdp\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEC/gyvDrSEHCFVOxqiNMxW0+L72i2SXX8RS6PzueuRmAiBAcy9NG5D6KJvazgu9YxmrOKV6qXzsUSfy4J3wzUg6CQ=="}]}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/available-typed-arrays_1.0.2_1580025964459_0.20148085947412264"}, "_hasShrinkwrap": false}, "1.0.3": {"name": "available-typed-arrays", "version": "1.0.3", "description": "Returns an array of Typed Array names that are available in the current environment", "main": "index.js", "type": "commonjs", "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package": "./package.json", "./package.json": "./package.json"}, "scripts": {"prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "prelint": "evalmd README.md", "lint": "eslint --ext=js,mjs .", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test:harmony": "nyc node --harmony --es-staging test", "test": "npm run tests-only && npm run test:harmony", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/available-typed-arrays.git"}, "keywords": ["typed", "arrays", "Float32Array", "Float64Array", "Int8Array", "Int16Array", "Int32Array", "Uint8Array", "Uint8ClampedArray", "Uint16Array", "Uint32Array", "BigInt64Array", "BigUint64Array"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/available-typed-arrays/issues"}, "homepage": "https://github.com/inspect-js/available-typed-arrays#readme", "engines": {"node": ">= 0.4"}, "devDependencies": {"@ljharb/eslint-config": "^17.6.0", "array.prototype.every": "^1.1.2", "aud": "^1.1.5", "auto-changelog": "^2.2.1", "eslint": "^7.26.0", "evalmd": "^0.0.19", "isarray": "^2.0.5", "nyc": "^10.3.2", "safe-publish-latest": "^1.1.4", "tape": "^5.2.2"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "dependencies": {"array.prototype.filter": "^1.0.0"}, "gitHead": "7f09d376aa70c60626fe1c97beb4d6af97f7eff0", "_id": "available-typed-arrays@1.0.3", "_nodeVersion": "16.1.0", "_npmVersion": "7.11.2", "dist": {"integrity": "sha512-CuPhFULixV/d89POo1UG4GqGbR7dmrefY2ZdmsYakeR4gOSJXoF7tfeaiqMHGOMrlTiJoeEs87fpLsBYmE2BMw==", "shasum": "fb7d02445bfedefad79fad1fe47931163a227198", "tarball": "https://registry.npmjs.org/available-typed-arrays/-/available-typed-arrays-1.0.3.tgz", "fileCount": 9, "unpackedSize": 12001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgpSn5CRA9TVsSAnZWagAA+iIP/jhvfWfe3E50DOCk9tiz\nBJk31wXD+eapfDzdyPuBeyUFx62Sqe9FPs/Vu/uipFMN9Yb575FHvD6zfggb\n3/N0fiLHkQlkm2rblts/gnKs02UyeJISx5XOUnWAjEqeLG4re6RHaDNjDFZB\nsehW4vvyBCM7aMmDv5Gvt7Kpon1G5Dxguh8x02AysK5rpHXNAHDr2G5G7WIv\niO2mam45BarnnbLVKVk+ydcioaImmbiWzCJuEvp28gqo9G9V/TCYd1swow4C\nFv0+OvFsSQTbovynqFE9KGsFHTqIrsbuWBkfjYZFJtbR2gZsca9cYQ+Nh35K\nm1fxqTG8uXbIm2nzYcB8H94ckP0mLDZbOEvAJL42g0/SjlNdTvodTH3ahVPD\nts1CTaFnjjnaX7zRd44Izd1NM8d3OBPplMt2WNT62UKbazTsvX7mtYptMIZE\niVvVoAyS/DWVRo5LQYXygk36gmSmzirjgXb92HUnuW6ZBJCjmA3lAInXSOWU\nsyrUX0k/tZdcE7IsYGBcl7ahsxPQrPCU8gyW7n+shpDjehPlyIDgtAgpTvr9\nFrrRBj5GhSEzy75vpzs1N9oM5V0TRRL0oeypEMnqaVTC3hcCx4zldWVzkAKo\nX00gcqpT3ECXadc7E3Kvoa+hyRgSkCr4cHDexr2O5inmPgGmyx0cSrmwC/nI\nPp7b\r\n=SFTk\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDkvH2vFDYrUK8YQLayDMnXb8dJCvGTakr41Dn3t/A0wwIgV/wWYRcauCrKYW7Ok+rHiu2OOolDVmEjfe0eV3yotUI="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/available-typed-arrays_1.0.3_1621436920941_0.23538947922503084"}, "_hasShrinkwrap": false}, "1.0.4": {"name": "available-typed-arrays", "version": "1.0.4", "description": "Returns an array of Typed Array names that are available in the current environment", "main": "index.js", "type": "commonjs", "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package": "./package.json", "./package.json": "./package.json"}, "sideEffects": false, "scripts": {"prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "prelint": "evalmd README.md", "lint": "eslint --ext=js,mjs .", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test:harmony": "nyc node --harmony --es-staging test", "test": "npm run tests-only && npm run test:harmony", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/available-typed-arrays.git"}, "keywords": ["typed", "arrays", "Float32Array", "Float64Array", "Int8Array", "Int16Array", "Int32Array", "Uint8Array", "Uint8ClampedArray", "Uint16Array", "Uint32Array", "BigInt64Array", "BigUint64Array"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/available-typed-arrays/issues"}, "homepage": "https://github.com/inspect-js/available-typed-arrays#readme", "engines": {"node": ">= 0.4"}, "devDependencies": {"@ljharb/eslint-config": "^17.6.0", "array.prototype.every": "^1.1.2", "aud": "^1.1.5", "auto-changelog": "^2.3.0", "eslint": "^7.27.0", "evalmd": "^0.0.19", "isarray": "^2.0.5", "nyc": "^10.3.2", "safe-publish-latest": "^1.1.4", "tape": "^5.2.2"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "gitHead": "9401fb568f20ca35203007830b11657563a7e983", "_id": "available-typed-arrays@1.0.4", "_nodeVersion": "16.1.0", "_npmVersion": "7.11.2", "dist": {"integrity": "sha512-SA5mXJWrId1TaQjfxUYghbqQ/hYioKmLJvPJyDuYRtXXenFNMjj4hSSt1Cf1xsuXSXrtxrVC5Ot4eU6cOtBDdA==", "shasum": "9e0ae84ecff20caae6a94a1c3bc39b955649b7a9", "tarball": "https://registry.npmjs.org/available-typed-arrays/-/available-typed-arrays-1.0.4.tgz", "fileCount": 10, "unpackedSize": 13607, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrSEtCRA9TVsSAnZWagAAEv8P/0xEyvhVL09hfdVr2Gzw\nQraqHsiW0z8hEqrNc+inrD7XF/ktNSEt3yUvx//h7rJ8ToYCWjumuzMw70o5\novwCKLE4GfMlxWv+q7ACMUMY42i5+GwfXQig8zgX16vUItOyoP060EQ38K8v\nCa9cDM47vDOn0SE/uRm5NMNN6ceAMXM6H1B1hDIvSlBdbCDPyLi+TJfIpdLF\nZmVrkNglc2B3Igc3e+DRJloHQySq6+iSogkvrH0HZJ8I288/T4MdVl5qWIYt\ngrJCh/fr16GP4Jci4tiw8xK+piBwB74eQDtVggHb9iJfkRYN/9Fk+dZ9omji\nEOyKrzpZGIw/7Bdc9Qbo0uWMA9HZYqhUnSuoqVNZJ1Z6u6Q1TIq0wEXnLdzK\nEl19dBsQYvG/4CH25cmxpdrOVEnU0osHJGfbHYTLdn5CohTaEW9GdWw2+ow8\nXjmySse3Hrc2gDIhCKoKWt75mAOOBd/Ta7iT0Q6RH22KJfqzGz+UT5blg4Tp\n2+5Hs7eIyoDqhdYYPT4Wx9nx6PHKkxoxKXe+6mrjOAEVT+2gcUD5VY1gvkt8\nFvQtaaszeJgwuvsDbNqhd+wW5YCHQD/PI+bdeLYRRewH1HdPxF8zZOvsutYE\nxYxuEAxQE1EDDuVFHsHchFtBjPaxBeWygUbD3FqvSF1zW/N8D2OEIJkspEyb\n8LPW\r\n=PsM2\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGEx4CYiSreZsr13AJbqzIbOgozmISKox9gG89JDi4qBAiEApYg3Trne9BjE+Bb1FS2yXyKSZ0939QCzGKQwkGand38="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/available-typed-arrays_1.0.4_1621958956714_0.2935773881456296"}, "_hasShrinkwrap": false}, "1.0.5": {"name": "available-typed-arrays", "version": "1.0.5", "description": "Returns an array of Typed Array names that are available in the current environment", "main": "index.js", "type": "commonjs", "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package": "./package.json", "./package.json": "./package.json"}, "sideEffects": false, "scripts": {"prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "prelint": "evalmd README.md", "lint": "eslint --ext=js,mjs .", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test:harmony": "nyc node --harmony --es-staging test", "test": "npm run tests-only && npm run test:harmony", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/available-typed-arrays.git"}, "keywords": ["typed", "arrays", "Float32Array", "Float64Array", "Int8Array", "Int16Array", "Int32Array", "Uint8Array", "Uint8ClampedArray", "Uint16Array", "Uint32Array", "BigInt64Array", "BigUint64Array"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/available-typed-arrays/issues"}, "homepage": "https://github.com/inspect-js/available-typed-arrays#readme", "engines": {"node": ">= 0.4"}, "devDependencies": {"@ljharb/eslint-config": "^18.0.0", "array.prototype.every": "^1.1.2", "aud": "^1.1.5", "auto-changelog": "^2.3.0", "eslint": "^7.32.0", "evalmd": "^0.0.19", "isarray": "^2.0.5", "nyc": "^10.3.2", "safe-publish-latest": "^1.1.4", "tape": "^5.3.1"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "gitHead": "d0f3cb979af4322acaa149e08ae58fd4faed3715", "_id": "available-typed-arrays@1.0.5", "_nodeVersion": "16.8.0", "_npmVersion": "7.21.0", "dist": {"integrity": "sha512-DMD0KiN46eipeziST1LPP/STfDU0sufISXmjSgvVsoU2tqxctQeASejWcfNtxYKqETM1UxQ8sp2OrSBWpHY6sw==", "shasum": "92f95616501069d07d10edb2fc37d3e1c65123b7", "tarball": "https://registry.npmjs.org/available-typed-arrays/-/available-typed-arrays-1.0.5.tgz", "fileCount": 10, "unpackedSize": 14129, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhLZLDCRA9TVsSAnZWagAAs20QAJlzqxlx75Lm4YUkQcx8\noZDyewBMuO2hLh7SwqPXf15FNw4lh1CytmB9EjaCMekc7VdedwKVN3Nd612N\nsPRujz7mSHQknwyS8OHGpt7cdAIb+fj9gZpFRGRErdSYeaRvhyo1VvDkkO+9\nyFO3pcOA7K5jyOtrkA4Xqi1VcGdla5D1Oo6dvRotmV3dgouJL38HAr/JaM5y\nN7rzFRznFlevcVEoeEIabmSpN/LGQ1sCpXiWTQpnvUDaGQ3SN7dCS01URrY0\nezI2o32xoLSbf2QoYtAXvWIQs3Nxcll1wFkajRHHV7nHLgr0X0U/pxyGZv4V\ncPonNwiSISRiu6aQ1QlgyJscHt3Ze0J6wlPdoBZBwoZTGNNWwuqF62yaoZ4n\ngBYWJAJSnTKc4Sl+52q5U3LfUmzZ0zgUnLzNLctd5uu5rkqtRaZlVFdJC3k+\nXgynICIXXWFkS7ib+E85fokVgN7bJYO2JLl1cp/cctlooRhYTsKtbmZfIVyl\ncACSF/eQ6xzbEoacjKjmzo1oLfKluk0YASzOChOJRIRM6zYWdyzlaaJHPCUm\nSKNa4W43RfM+yu8EhfDxayoWTLrgMDI+g4uq7vNDGifPd1d1Auu/O0tcwCXO\nPLqsdN/tCHCGAvFqKTzEUD5EKsmxm02lIG0NXXC/hcS+HC7/zf1FjBMQh8s9\njuZK\r\n=3dYh\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAFCWG/QMTZN+JpDsi1HaGTT5BaghDyXnOxOfJWzpqk+AiEAnl2+LrlKLHRd8fHBz33TrOMsppTRFe2OBkVb/4M3TzQ="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/available-typed-arrays_1.0.5_1630376643554_0.2131087752403893"}, "_hasShrinkwrap": false}, "1.0.6": {"name": "available-typed-arrays", "version": "1.0.6", "description": "Returns an array of Typed Array names that are available in the current environment", "main": "index.js", "type": "commonjs", "exports": {".": "./index.js", "./package.json": "./package.json"}, "types": "./index.d.ts", "sideEffects": false, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "prelint": "evalmd README.md", "lint": "eslint --ext=js,mjs .", "postlint": "tsc -p .", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test:harmony": "nyc node --harmony --es-staging test", "test": "npm run tests-only && npm run test:harmony", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/available-typed-arrays.git"}, "keywords": ["typed", "arrays", "Float32Array", "Float64Array", "Int8Array", "Int16Array", "Int32Array", "Uint8Array", "Uint8ClampedArray", "Uint16Array", "Uint32Array", "BigInt64Array", "BigUint64Array"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/available-typed-arrays/issues"}, "homepage": "https://github.com/inspect-js/available-typed-arrays#readme", "engines": {"node": ">= 0.4"}, "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "@types/array.prototype.every": "^1.1.1", "@types/isarray": "^2.0.2", "@types/tape": "^5.6.4", "array.prototype.every": "^1.1.5", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "isarray": "^2.0.5", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.7.4", "typescript": "^5.4.0-dev.20240131"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "_id": "available-typed-arrays@1.0.6", "gitHead": "a18bbe6d9513b44ebf9ca17170794dae1d53d443", "_nodeVersion": "21.6.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-j1QzY8iPNPG4o4xmO3ptzpRxTciqD3MgEHtifP/YnJpIo58Xu+ne4BejlbkuaLfXn/nz6HFiw29bLpj2PNMdGg==", "shasum": "ac812d8ce5a6b976d738e1c45f08d0b00bc7d725", "tarball": "https://registry.npmjs.org/available-typed-arrays/-/available-typed-arrays-1.0.6.tgz", "fileCount": 11, "unpackedSize": 20365, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCUcP7eBf6u1xi1CAJfcAVgQ+XrniZwSwlZ8bn7I4J6YAIgfMxE6gHC/5gEyPgg1PpCHY7LMwqFacHTln8AKcjp2mc="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/available-typed-arrays_1.0.6_1706766379004_0.910742744450938"}, "_hasShrinkwrap": false}, "1.0.7": {"name": "available-typed-arrays", "version": "1.0.7", "description": "Returns an array of Typed Array names that are available in the current environment", "main": "index.js", "type": "commonjs", "exports": {".": "./index.js", "./package.json": "./package.json"}, "types": "./index.d.ts", "sideEffects": false, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "prelint": "evalmd README.md", "lint": "eslint --ext=js,mjs .", "postlint": "tsc -p .", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test:harmony": "nyc node --harmony --es-staging test", "test": "npm run tests-only && npm run test:harmony", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/available-typed-arrays.git"}, "keywords": ["typed", "arrays", "Float32Array", "Float64Array", "Int8Array", "Int16Array", "Int32Array", "Uint8Array", "Uint8ClampedArray", "Uint16Array", "Uint32Array", "BigInt64Array", "BigUint64Array"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/available-typed-arrays/issues"}, "homepage": "https://github.com/inspect-js/available-typed-arrays#readme", "engines": {"node": ">= 0.4"}, "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "@types/array.prototype.every": "^1.1.1", "@types/isarray": "^2.0.2", "@types/tape": "^5.6.4", "array.prototype.every": "^1.1.5", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "isarray": "^2.0.5", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.7.4", "typescript": "^5.4.0-dev.20240131"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "dependencies": {"possible-typed-array-names": "^1.0.0"}, "_id": "available-typed-arrays@1.0.7", "gitHead": "d72cd6154ce39482ca83bed2200ff3b56c76e8d8", "_nodeVersion": "21.6.2", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-wvUjBtSGN7+7SjNpq/9M2Tg350UZD3q62IFZLbRAR1bSMlCo1ZaeW+BJ+D090e4hIIZLBcTDWe4Mh4jvUDajzQ==", "shasum": "a5cc375d6a03c2efc87a553f3e0b1522def14846", "tarball": "https://registry.npmjs.org/available-typed-arrays/-/available-typed-arrays-1.0.7.tgz", "fileCount": 11, "unpackedSize": 20380, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDoqwDwclim1uEhw17jNNg0gv39Ju8MtQAWOYTPwxkldgIgEPaVtQkMYFsWMoFxWmB82FvaBTuWcvHgiPI5Wj6aAj0="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/available-typed-arrays_1.0.7_1708386870564_0.5677413133448181"}, "_hasShrinkwrap": false}}, "time": {"created": "2020-01-24T09:29:59.840Z", "1.0.0": "2020-01-24T09:30:00.014Z", "modified": "2024-02-19T23:54:31.157Z", "1.0.1": "2020-01-24T16:58:31.926Z", "1.0.2": "2020-01-26T08:06:04.613Z", "1.0.3": "2021-05-19T15:08:41.114Z", "1.0.4": "2021-05-25T16:09:16.855Z", "1.0.5": "2021-08-31T02:24:03.721Z", "1.0.6": "2024-02-01T05:46:19.196Z", "1.0.7": "2024-02-19T23:54:30.736Z"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "description": "Returns an array of Typed Array names that are available in the current environment", "homepage": "https://github.com/inspect-js/available-typed-arrays#readme", "keywords": ["typed", "arrays", "Float32Array", "Float64Array", "Int8Array", "Int16Array", "Int32Array", "Uint8Array", "Uint8ClampedArray", "Uint16Array", "Uint32Array", "BigInt64Array", "BigUint64Array"], "repository": {"type": "git", "url": "git+https://github.com/inspect-js/available-typed-arrays.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/inspect-js/available-typed-arrays/issues"}, "license": "MIT", "readme": "# available-typed-arrays <sup>[![Version Badge][2]][1]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![dependency status][5]][6]\n[![dev dependency status][7]][8]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][11]][1]\n\nReturns an array of Typed Array names that are available in the current environment.\n\n## Example\n\n```js\nvar availableTypedArrays = require('available-typed-arrays');\nvar assert = require('assert');\n\nassert.deepStrictEqual(\n\tavailableTypedArrays().sort(),\n\t[\n\t\t'Int8Array',\n\t\t'Uint8Array',\n\t\t'Uint8ClampedArray',\n\t\t'Int16Array',\n\t\t'Uint16Array',\n\t\t'Int32Array',\n\t\t'Uint32Array',\n\t\t'Float32Array',\n\t\t'Float64Array',\n\t\t'BigInt64Array',\n\t\t'BigUint64Array'\n\t].sort()\n);\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n[1]: https://npmjs.org/package/available-typed-arrays\n[2]: https://versionbadg.es/inspect-js/available-typed-arrays.svg\n[5]: https://david-dm.org/inspect-js/available-typed-arrays.svg\n[6]: https://david-dm.org/inspect-js/available-typed-arrays\n[7]: https://david-dm.org/inspect-js/available-typed-arrays/dev-status.svg\n[8]: https://david-dm.org/inspect-js/available-typed-arrays#info=devDependencies\n[11]: https://nodei.co/npm/available-typed-arrays.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/available-typed-arrays.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/available-typed-arrays.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=available-typed-arrays\n[codecov-image]: https://codecov.io/gh/inspect-js/available-typed-arrays/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/inspect-js/available-typed-arrays/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/inspect-js/available-typed-arrays\n[actions-url]: https://github.com/inspect-js/available-typed-arrays/actions\n", "readmeFilename": "README.md"}