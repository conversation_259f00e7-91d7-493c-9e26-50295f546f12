{"_id": "typed-function", "_rev": "47-73dd228ecb0acd6424cbeb8bc058ee03", "name": "typed-function", "description": "Type checking for JavaScript functions", "dist-tags": {"latest": "4.2.1"}, "versions": {"0.3.1": {"name": "typed-function", "version": "0.3.1", "description": "Type checking for JavaScript functions", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/josde<PERSON>"}, "contributors": [], "homepage": "https://github.com/josde<PERSON>/typed-function", "repository": {"type": "git", "url": "https://github.com/josde<PERSON>/typed-function.git"}, "keywords": ["typed", "function", "arguments", "compose", "types"], "dependencies": {}, "devDependencies": {"istanbul": "^0.3.2", "mocha": "^1.21.5", "uglify-js": "^2.4.15"}, "main": "./typed-function", "scripts": {"minify": "uglifyjs typed-function.js -o typed-function.min.js -c -m", "test": "mocha test --recursive", "coverage": "istanbul cover _mocha -- test --recursive; echo \"\nCoverage report is available at ./coverage/lcov-report/index.html\""}, "engines": {"node": ">= 0.10"}, "gitHead": "8aec44f74304c088f2d78ca11bc1a395d780c571", "bugs": {"url": "https://github.com/josde<PERSON>/typed-function/issues"}, "_id": "typed-function@0.3.1", "_shasum": "da41ad7dd18992df692c714bc375f974dddb0b4e", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "da41ad7dd18992df692c714bc375f974dddb0b4e", "tarball": "https://registry.npmjs.org/typed-function/-/typed-function-0.3.1.tgz", "integrity": "sha512-qAZz2shXPyw+NzrwrYtjzphId4ijx46AVcn9IQDTnkxqNHcGwEfTdSzKfAAeD06MiBziOsyQ7lEezJ1D8fDZNQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCl5hdFhbjKk0BOO0EQD6JmPEv8HhYi8U7hRdJ+17zVAgIhAMj70HRu6O2qtMy8RE+7r6pBuj+jf12TDu+CyjfDghEi"}]}, "directories": {}}, "0.4.0": {"name": "typed-function", "version": "0.4.0", "description": "Type checking for JavaScript functions", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/josde<PERSON>"}, "contributors": [], "homepage": "https://github.com/josde<PERSON>/typed-function", "repository": {"type": "git", "url": "https://github.com/josde<PERSON>/typed-function.git"}, "keywords": ["typed", "function", "arguments", "compose", "types"], "dependencies": {}, "devDependencies": {"istanbul": "^0.3.2", "mocha": "^1.21.5", "uglify-js": "^2.4.15"}, "main": "./typed-function", "scripts": {"minify": "uglifyjs typed-function.js -o typed-function.min.js -c -m", "test": "mocha test --recursive", "coverage": "istanbul cover _mocha -- test --recursive; echo \"\nCoverage report is available at ./coverage/lcov-report/index.html\""}, "engines": {"node": ">= 0.10"}, "gitHead": "42a25b89f8a0a3fe77d57f877d01d01617d77fe0", "bugs": {"url": "https://github.com/josde<PERSON>/typed-function/issues"}, "_id": "typed-function@0.4.0", "_shasum": "71023088bdc744f1df751274aa4eeae299275295", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "71023088bdc744f1df751274aa4eeae299275295", "tarball": "https://registry.npmjs.org/typed-function/-/typed-function-0.4.0.tgz", "integrity": "sha512-bXMX9FKFHfMqf15scYPdSikAwoZsyMXafOgLtzfUrqsvZCDsDbq4Zfk9iU3kPEGnKNa59Fh/3iHIIuY0gkH3Jg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDvJrJ4sVjhOvgUtc3hbBu8ZpX0hm14GppA3XFobuUYuwIhAJbnZpYbFk25aisu30IchBrPz3YJONcGDQEyMD+1DKIU"}]}, "directories": {}}, "0.5.0": {"name": "typed-function", "version": "0.5.0", "description": "Type checking for JavaScript functions", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/josde<PERSON>"}, "contributors": [], "homepage": "https://github.com/josde<PERSON>/typed-function", "repository": {"type": "git", "url": "https://github.com/josde<PERSON>/typed-function.git"}, "keywords": ["typed", "function", "arguments", "compose", "types"], "dependencies": {}, "devDependencies": {"istanbul": "^0.3.2", "mocha": "^1.21.5", "uglify-js": "^2.4.15"}, "main": "./typed-function", "scripts": {"minify": "uglifyjs typed-function.js -o typed-function.min.js -c -m", "test": "mocha test --recursive", "coverage": "istanbul cover _mocha -- test --recursive; echo \"\nCoverage report is available at ./coverage/lcov-report/index.html\""}, "engines": {"node": ">= 0.10"}, "gitHead": "40206710938700ae713c281d9486e6343484bdf6", "bugs": {"url": "https://github.com/josde<PERSON>/typed-function/issues"}, "_id": "typed-function@0.5.0", "_shasum": "acc72a53d882861f1ea7ba3fbec88018ff28a8a4", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "acc72a53d882861f1ea7ba3fbec88018ff28a8a4", "tarball": "https://registry.npmjs.org/typed-function/-/typed-function-0.5.0.tgz", "integrity": "sha512-SyVHpfKGcwoe5iXIMJ39Jzc4w+IP4p1rlWxWakhnbXCap3tCqyRZ0b6bRkSU0QZ0zvLhOtiiaUnxNRvwq/AgpQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEQR5xa/Du6KosoRT3yaBTtKV7ov+5H7EePzXbE2yJk+AiEApdR140Xlqse4lzm9QhIbGkWFrR9F2ZXgU182BnnWtBA="}]}, "directories": {}}, "0.6.0": {"name": "typed-function", "version": "0.6.0", "description": "Type checking for JavaScript functions", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/josde<PERSON>"}, "contributors": [], "homepage": "https://github.com/josde<PERSON>/typed-function", "repository": {"type": "git", "url": "https://github.com/josde<PERSON>/typed-function.git"}, "keywords": ["typed", "function", "arguments", "compose", "types"], "dependencies": {}, "devDependencies": {"istanbul": "^0.3.2", "mocha": "^1.21.5", "uglify-js": "^2.4.15"}, "main": "./typed-function", "scripts": {"minify": "uglifyjs typed-function.js -o typed-function.min.js -c -m", "test": "mocha test --recursive", "coverage": "istanbul cover _mocha -- test --recursive; echo \"\nCoverage report is available at ./coverage/lcov-report/index.html\""}, "engines": {"node": ">= 0.10"}, "gitHead": "d612cae3a9b7bcaf5b3857ddf1a9190fa3ad8c98", "bugs": {"url": "https://github.com/josde<PERSON>/typed-function/issues"}, "_id": "typed-function@0.6.0", "_shasum": "a9f3ecae7d38f3656b6777fa95d16cb9d216d007", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "a9f3ecae7d38f3656b6777fa95d16cb9d216d007", "tarball": "https://registry.npmjs.org/typed-function/-/typed-function-0.6.0.tgz", "integrity": "sha512-RtbiTl0VhslOgDUGHo6MU28AcX0/c7lOEzeUR8yBL2cGYU3qyN+GzBvss930JU+CHUBd+zzy0Oh8mBN494xL0Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC+D7komPQSFmlFmE6TAj/askG2NW6FyulTD86q+KBOOwIgatrkzp3KWLanlJUAL9jkDYK3k3KBQCGmejfx0orVsLg="}]}, "directories": {}}, "0.6.1": {"name": "typed-function", "version": "0.6.1", "description": "Type checking for JavaScript functions", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/josde<PERSON>"}, "contributors": [], "homepage": "https://github.com/josde<PERSON>/typed-function", "repository": {"type": "git", "url": "https://github.com/josde<PERSON>/typed-function.git"}, "keywords": ["typed", "function", "arguments", "compose", "types"], "dependencies": {}, "devDependencies": {"istanbul": "^0.3.2", "mocha": "^1.21.5", "uglify-js": "^2.4.15"}, "main": "./typed-function", "scripts": {"minify": "uglifyjs typed-function.js -o typed-function.min.js -c -m", "test": "mocha test --recursive", "coverage": "istanbul cover _mocha -- test --recursive; echo \"\nCoverage report is available at ./coverage/lcov-report/index.html\""}, "engines": {"node": ">= 0.10"}, "gitHead": "b9374007a16b705dc185b034c6a0d187b3cab5e4", "bugs": {"url": "https://github.com/josde<PERSON>/typed-function/issues"}, "_id": "typed-function@0.6.1", "_shasum": "e32b2d2b6c74b1695b8a42074ae9972ae1f77021", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e32b2d2b6c74b1695b8a42074ae9972ae1f77021", "tarball": "https://registry.npmjs.org/typed-function/-/typed-function-0.6.1.tgz", "integrity": "sha512-2pmogeYd9rj++z9gPMm18opm83mvUui6cSr4UtHUlXvFKGBZxXNPIgzFDEwNZ21isTKOV5kyhn4F04hhnBZEiA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDpfda7QdbDzwI2qt7ftZyQuJ/1llyylr0Pnr7syz0NlQIhANhpc2R6ESzu+5fEjnNpHlc6e+9UsO/wG6ghUWEtfS/U"}]}, "directories": {}}, "0.6.2": {"name": "typed-function", "version": "0.6.2", "description": "Type checking for JavaScript functions", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/josde<PERSON>"}, "contributors": [], "homepage": "https://github.com/josde<PERSON>/typed-function", "repository": {"type": "git", "url": "https://github.com/josde<PERSON>/typed-function.git"}, "keywords": ["typed", "function", "arguments", "compose", "types"], "dependencies": {}, "devDependencies": {"istanbul": "^0.3.2", "mocha": "^1.21.5", "uglify-js": "^2.4.15"}, "main": "./typed-function", "scripts": {"minify": "uglifyjs typed-function.js -o typed-function.min.js -c -m", "test": "mocha test --recursive", "coverage": "istanbul cover _mocha -- test --recursive; echo \"\nCoverage report is available at ./coverage/lcov-report/index.html\""}, "engines": {"node": ">= 0.10"}, "gitHead": "4a34d4ee46a9f7348d31f88824ca73d9fd17b137", "bugs": {"url": "https://github.com/josde<PERSON>/typed-function/issues"}, "_id": "typed-function@0.6.2", "_shasum": "48178b4008db9ccf095fc373777694eb091c7015", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "48178b4008db9ccf095fc373777694eb091c7015", "tarball": "https://registry.npmjs.org/typed-function/-/typed-function-0.6.2.tgz", "integrity": "sha512-Km5jpVfOqjLfJEywP9V4NZr9RasESMypvKejqw+4lQP+9r3xZUQbnzrVi96MK63/Bod5gfWqqD7f4WsJ+MrF5w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDvtcnyNBwUQTEHwP4wvi+eGJDgAIAZu5OrTPsvuza0agIgZlI0QFQhxpu2T7sBzEM2IUXBq4rMvhwZ9ha+TN0DENE="}]}, "directories": {}}, "0.6.3": {"name": "typed-function", "version": "0.6.3", "description": "Type checking for JavaScript functions", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/josde<PERSON>"}, "contributors": [], "homepage": "https://github.com/josde<PERSON>/typed-function", "repository": {"type": "git", "url": "https://github.com/josde<PERSON>/typed-function.git"}, "keywords": ["typed", "function", "arguments", "compose", "types"], "dependencies": {}, "devDependencies": {"istanbul": "^0.3.2", "mocha": "^1.21.5", "uglify-js": "^2.4.15"}, "main": "./typed-function", "scripts": {"minify": "uglifyjs typed-function.js -o typed-function.min.js -c -m", "test": "mocha test --recursive", "coverage": "istanbul cover _mocha -- test --recursive; echo \"\nCoverage report is available at ./coverage/lcov-report/index.html\""}, "engines": {"node": ">= 0.10"}, "gitHead": "987c5c620a692ccd069c51973a988a3434f5e374", "bugs": {"url": "https://github.com/josde<PERSON>/typed-function/issues"}, "_id": "typed-function@0.6.3", "_shasum": "28219f72ffacd669b6574b1573ea3b13cc84cec2", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "28219f72ffacd669b6574b1573ea3b13cc84cec2", "tarball": "https://registry.npmjs.org/typed-function/-/typed-function-0.6.3.tgz", "integrity": "sha512-Hl6LW8ubq5ZWDQLXWHHLHxjUATK0b4uKU7fI+T/PvbhpCIUxOHNgRX/sBVq0U9oqVc1Cxz0/Q04ZedRAmLEYVQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCkWKaEtvC3vjEJeyUb4fU0BOKGnlzQzTmttj27ydX1uwIgdY+tUbtrxicR9xCDWVKeH/A8J1yJxivdvIuVLMilJ/A="}]}, "directories": {}}, "0.7.0": {"name": "typed-function", "version": "0.7.0", "description": "Type checking for JavaScript functions", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/josde<PERSON>"}, "contributors": [], "homepage": "https://github.com/josde<PERSON>/typed-function", "repository": {"type": "git", "url": "https://github.com/josde<PERSON>/typed-function.git"}, "keywords": ["typed", "function", "arguments", "compose", "types"], "dependencies": {}, "devDependencies": {"istanbul": "^0.3.2", "mocha": "^1.21.5", "uglify-js": "^2.4.15"}, "main": "./typed-function", "scripts": {"minify": "uglifyjs typed-function.js -o typed-function.min.js -c -m", "test": "mocha test --recursive", "coverage": "istanbul cover _mocha -- test --recursive; echo \"\nCoverage report is available at ./coverage/lcov-report/index.html\""}, "engines": {"node": ">= 0.10"}, "gitHead": "ad35ec3e706c0a3d9e0a59a0cb0a6ddb63da46f9", "bugs": {"url": "https://github.com/josde<PERSON>/typed-function/issues"}, "_id": "typed-function@0.7.0", "_shasum": "7b5aa72ce31fdb4ba4dbfa5236674c8f0b71e453", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "7b5aa72ce31fdb4ba4dbfa5236674c8f0b71e453", "tarball": "https://registry.npmjs.org/typed-function/-/typed-function-0.7.0.tgz", "integrity": "sha512-JJXi+FbzwBcGC4VpU65Zag8rf5ATCLeEj1x494Tu+EQCH7yZXcNX4tN3ybGGHw4pPll5VdK0jajYUFEqQiY8tg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC3eeDh6wLNM0lWk26jGUinRQkPDKzdxRZ562atV+9RuAIgPaLJHhCbVXClzis/zbWBA1cNNQ8pQ/KBj/r97evm6V0="}]}, "directories": {}}, "0.8.0": {"name": "typed-function", "version": "0.8.0", "description": "Type checking for JavaScript functions", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/josde<PERSON>"}, "contributors": [], "homepage": "https://github.com/josde<PERSON>/typed-function", "repository": {"type": "git", "url": "https://github.com/josde<PERSON>/typed-function.git"}, "keywords": ["typed", "function", "arguments", "compose", "types"], "dependencies": {}, "devDependencies": {"istanbul": "^0.3.2", "mocha": "^1.21.5", "uglify-js": "^2.4.15"}, "main": "./typed-function", "scripts": {"minify": "uglifyjs typed-function.js -o typed-function.min.js -c -m", "test": "mocha test --recursive", "coverage": "istanbul cover _mocha -- test --recursive; echo \"\nCoverage report is available at ./coverage/lcov-report/index.html\""}, "engines": {"node": ">= 0.10"}, "gitHead": "63eab526a8f829d4555573a34c2481d69c627da6", "bugs": {"url": "https://github.com/josde<PERSON>/typed-function/issues"}, "_id": "typed-function@0.8.0", "_shasum": "5340364c0428a4a35c75e24887e0ee25726e68ba", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "5340364c0428a4a35c75e24887e0ee25726e68ba", "tarball": "https://registry.npmjs.org/typed-function/-/typed-function-0.8.0.tgz", "integrity": "sha512-ZdFtjUAacjfl9c26ptw3ceQg8bbFBZ8Xn+h1vZ54ZpmzqKJyLvTYy9LXG1znQ+582N+qiQmsZrExtuyNnTDQoQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGp5I6PtAgL5t9HXB1xnp5iDqTSl04XvKRAhYC+uO2NQAiEA1d2Iu2848BoqB62pA6XeRUxQ4aRcIFtm4nEbUkXqeaI="}]}, "directories": {}}, "0.8.1": {"name": "typed-function", "version": "0.8.1", "description": "Type checking for JavaScript functions", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/josde<PERSON>"}, "contributors": [], "homepage": "https://github.com/josde<PERSON>/typed-function", "repository": {"type": "git", "url": "https://github.com/josde<PERSON>/typed-function.git"}, "keywords": ["typed", "function", "arguments", "compose", "types"], "dependencies": {}, "devDependencies": {"istanbul": "^0.3.2", "mocha": "^1.21.5", "uglify-js": "^2.4.15"}, "main": "./typed-function", "scripts": {"minify": "uglifyjs typed-function.js -o typed-function.min.js -c -m", "test": "mocha test --recursive", "coverage": "istanbul cover _mocha -- test --recursive; echo \"\nCoverage report is available at ./coverage/lcov-report/index.html\""}, "engines": {"node": ">= 0.10"}, "gitHead": "dfbe21ecbcb23918c273daabfeb82c22356a2279", "bugs": {"url": "https://github.com/josde<PERSON>/typed-function/issues"}, "_id": "typed-function@0.8.1", "_shasum": "55a6f43465f76c3b44bc5bbdb4d6954825baa948", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "55a6f43465f76c3b44bc5bbdb4d6954825baa948", "tarball": "https://registry.npmjs.org/typed-function/-/typed-function-0.8.1.tgz", "integrity": "sha512-J6qG/tuPMOWwoZE5sfbeZpPOh0vSuht9gG+lTqoTM+2Ck8gYK7CiKtn1+4s0WVsZ7jCMkDpJbqON7Av3pEBmaQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFOcAM8T5AqXg2ZU+o5XBWvhz/QfpZViGOVo+pWXvsj+AiBJEWafsAPaujzU1+XY1p5PzoOTtaWddMKJ8WTorDO/iw=="}]}, "directories": {}}, "0.8.2": {"name": "typed-function", "version": "0.8.2", "description": "Type checking for JavaScript functions", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/josde<PERSON>"}, "contributors": [], "homepage": "https://github.com/josde<PERSON>/typed-function", "repository": {"type": "git", "url": "https://github.com/josde<PERSON>/typed-function.git"}, "keywords": ["typed", "function", "arguments", "compose", "types"], "dependencies": {}, "devDependencies": {"istanbul": "^0.3.2", "mocha": "^1.21.5", "uglify-js": "^2.4.15"}, "main": "./typed-function", "scripts": {"minify": "uglifyjs typed-function.js -o typed-function.min.js -c -m", "test": "mocha test --recursive", "coverage": "istanbul cover _mocha -- test --recursive; echo \"\nCoverage report is available at ./coverage/lcov-report/index.html\""}, "engines": {"node": ">= 0.10"}, "gitHead": "8aa0a3ef59aa79ad09160905f51ae48ea2cc765b", "bugs": {"url": "https://github.com/josde<PERSON>/typed-function/issues"}, "_id": "typed-function@0.8.2", "_shasum": "eaa0642c1bb00b0aa0eb1d824646006669ff9aab", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "eaa0642c1bb00b0aa0eb1d824646006669ff9aab", "tarball": "https://registry.npmjs.org/typed-function/-/typed-function-0.8.2.tgz", "integrity": "sha512-1dpk4tscq4NWwrKubydNobSwpQwL0yHyK9koPd5/FZlEipcXoWR2NKXT8axZndINMjXsFmBjCFf+uPZlvij5cg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDxtHCIus2/3qCZlM+2+nPj1HJPt41rLVbD5apyo4QNsQIhAOY6nAUj1cMi01tkk/qMuSgzEJIELlr5qcySPPmGZLZa"}]}, "directories": {}}, "0.8.3": {"name": "typed-function", "version": "0.8.3", "description": "Type checking for JavaScript functions", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/josde<PERSON>"}, "contributors": [], "homepage": "https://github.com/josde<PERSON>/typed-function", "repository": {"type": "git", "url": "https://github.com/josde<PERSON>/typed-function.git"}, "keywords": ["typed", "function", "arguments", "compose", "types"], "dependencies": {}, "devDependencies": {"istanbul": "^0.3.2", "mocha": "^1.21.5", "uglify-js": "^2.4.15"}, "main": "./typed-function", "scripts": {"minify": "uglifyjs typed-function.js -o typed-function.min.js -c -m", "test": "mocha test --recursive", "coverage": "istanbul cover _mocha -- test --recursive; echo \"\nCoverage report is available at ./coverage/lcov-report/index.html\""}, "engines": {"node": ">= 0.10"}, "gitHead": "3e8d283d65368bfa9adb60187d8cc80192d9b2b9", "bugs": {"url": "https://github.com/josde<PERSON>/typed-function/issues"}, "_id": "typed-function@0.8.3", "_shasum": "ee0d5aa926fdce4a5bd5442de5715483e50c86ec", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "ee0d5aa926fdce4a5bd5442de5715483e50c86ec", "tarball": "https://registry.npmjs.org/typed-function/-/typed-function-0.8.3.tgz", "integrity": "sha512-rNTtTIMXdRRlRUg6G4eQcesaY8dUtIhuYGJzw5PHhsGxVwZcV6sAa+UM12K8Dv5Ct6vhM2KHy+ruyFcZaFjcYA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDF5qH5dpfC95f6x96H8N1DDCZdN62TUCWaQQ3i6gErYwIhALKLpdrFFMeb0Jmcdat1eYYg4bX3FrX+plWljOG1qWez"}]}, "directories": {}}, "0.9.0": {"name": "typed-function", "version": "0.9.0", "description": "Type checking for JavaScript functions", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/josde<PERSON>"}, "contributors": [], "homepage": "https://github.com/josde<PERSON>/typed-function", "repository": {"type": "git", "url": "https://github.com/josde<PERSON>/typed-function.git"}, "keywords": ["typed", "function", "arguments", "compose", "types"], "dependencies": {}, "devDependencies": {"istanbul": "^0.3.2", "mocha": "^1.21.5", "uglify-js": "^2.4.15"}, "main": "./typed-function", "scripts": {"minify": "uglifyjs typed-function.js -o typed-function.min.js -c -m", "test": "mocha test --recursive", "coverage": "istanbul cover _mocha -- test --recursive; echo \"\nCoverage report is available at ./coverage/lcov-report/index.html\""}, "engines": {"node": ">= 0.10"}, "gitHead": "bae9e2145246723fbdcada9b583a20b68be1285f", "bugs": {"url": "https://github.com/josde<PERSON>/typed-function/issues"}, "_id": "typed-function@0.9.0", "_shasum": "54b09d2bf65c09fb4ae55e0ad1cd2e36ab37c233", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "54b09d2bf65c09fb4ae55e0ad1cd2e36ab37c233", "tarball": "https://registry.npmjs.org/typed-function/-/typed-function-0.9.0.tgz", "integrity": "sha512-264ZMpHbRbEEy5WlxTsTBWKV5c/F5nmQ2ROZguxk4jNqwiphb4WAKf6B3MSQv+2tvisyHaLB908vDZor97SKFA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBU3ald9UQd6h5+dGUsxHPqUMw/cKQmyKksMRFDjETePAiA45t7EbTuzURzW1k5MvcKD08o2XsKsdG1Zb1btp6yl7g=="}]}, "directories": {}}, "0.10.0": {"name": "typed-function", "version": "0.10.0", "description": "Type checking for JavaScript functions", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/josde<PERSON>"}, "contributors": [], "homepage": "https://github.com/josde<PERSON>/typed-function", "repository": {"type": "git", "url": "https://github.com/josde<PERSON>/typed-function.git"}, "keywords": ["typed", "function", "arguments", "compose", "types"], "dependencies": {}, "devDependencies": {"istanbul": "^0.3.2", "mocha": "^1.21.5", "uglify-js": "^2.4.15"}, "main": "./typed-function", "scripts": {"minify": "uglifyjs typed-function.js -o typed-function.min.js -c -m", "test": "mocha test --recursive", "coverage": "istanbul cover _mocha -- test --recursive; echo \"\nCoverage report is available at ./coverage/lcov-report/index.html\""}, "engines": {"node": ">= 0.10"}, "gitHead": "9ddfc7ce79deed4fc7da1be7371c71e041d928c2", "bugs": {"url": "https://github.com/josde<PERSON>/typed-function/issues"}, "_id": "typed-function@0.10.0", "_shasum": "4c78cc3246c25042d1ac0161ad8c3a9b04eb366c", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "4c78cc3246c25042d1ac0161ad8c3a9b04eb366c", "tarball": "https://registry.npmjs.org/typed-function/-/typed-function-0.10.0.tgz", "integrity": "sha512-Dbk9T9npwFqhzvAIKdNKLLNBDD/gLGhzHTg0xOX78VVQd3HuS9aJUZ57Eh5im/TVsQ3YTju6NPIUtBDlaNs/yQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBRas9gQHaxKvZB3k9LMUaaNGClHjJuRJjQnUk9BqUgtAiBDhDbRpKztkDUu1M3jHME/IPF4EpAfST1J5NQxiAGsrg=="}]}, "directories": {}}, "0.10.1": {"name": "typed-function", "version": "0.10.1", "description": "Type checking for JavaScript functions", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/josde<PERSON>"}, "contributors": [], "homepage": "https://github.com/josde<PERSON>/typed-function", "repository": {"type": "git", "url": "git+https://github.com/josdejong/typed-function.git"}, "keywords": ["typed", "function", "arguments", "compose", "types"], "dependencies": {}, "devDependencies": {"istanbul": "^0.3.2", "mocha": "^1.21.5", "uglify-js": "^2.4.15"}, "main": "./typed-function", "scripts": {"minify": "uglifyjs typed-function.js -o typed-function.min.js -c -m", "test": "mocha test --recursive", "coverage": "istanbul cover _mocha -- test --recursive; echo \"\nCoverage report is available at ./coverage/lcov-report/index.html\""}, "engines": {"node": ">= 0.10"}, "gitHead": "1ed80ade1949873f74f86e13c102d151a5a46570", "bugs": {"url": "https://github.com/josde<PERSON>/typed-function/issues"}, "_id": "typed-function@0.10.1", "_shasum": "e1b8f63e4b2170c5c1ba57969bf4ee9f9fc49736", "_from": ".", "_npmVersion": "2.11.3", "_nodeVersion": "0.12.7", "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e1b8f63e4b2170c5c1ba57969bf4ee9f9fc49736", "tarball": "https://registry.npmjs.org/typed-function/-/typed-function-0.10.1.tgz", "integrity": "sha512-Xn6zceI5dDDcL+YQuY/hlXt685uBk9CY+0nJJKr7AT6kzYeK3Lk0lJoL1+SZteATRsA0RgE8AguHyZJ1oBr4kg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFaH0HqGpT82u+B8gzOYvRrFoVBUVJHjVjc5kxfIThNnAiEAv8ozX0UWhNNBAoPDuWhJEYqdJAMvFOvo1sCmdU6yzqI="}]}, "directories": {}}, "0.10.2": {"name": "typed-function", "version": "0.10.2", "description": "Type checking for JavaScript functions", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/josde<PERSON>"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/luke-gumbley"}], "homepage": "https://github.com/josde<PERSON>/typed-function", "repository": {"type": "git", "url": "git+https://github.com/josdejong/typed-function.git"}, "keywords": ["typed", "function", "arguments", "compose", "types"], "dependencies": {}, "devDependencies": {"istanbul": "^0.3.2", "mocha": "^1.21.5", "uglify-js": "^2.4.15"}, "main": "./typed-function", "scripts": {"minify": "uglifyjs typed-function.js -o typed-function.min.js -c -m", "test": "mocha test --recursive", "coverage": "istanbul cover _mocha -- test --recursive; echo \"\nCoverage report is available at ./coverage/lcov-report/index.html\""}, "engines": {"node": ">= 0.10"}, "gitHead": "95228ac2490638eef54b02a919eeb0f4e14522ce", "bugs": {"url": "https://github.com/josde<PERSON>/typed-function/issues"}, "_id": "typed-function@0.10.2", "_shasum": "dde037ce61540ee8e79c5bc9c5ebcd305365fd7d", "_from": ".", "_npmVersion": "2.14.4", "_nodeVersion": "4.1.1", "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "dde037ce61540ee8e79c5bc9c5ebcd305365fd7d", "tarball": "https://registry.npmjs.org/typed-function/-/typed-function-0.10.2.tgz", "integrity": "sha512-s+t0D3299nGx+8eaT4nIQGfafU6sZO2FnC/ixEm0iKhGyWlH5q15fRB2pZiNwioPIEkPK33NOwpOx2a3C3bb7Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFPJ8la37Qq082QFZgXWoEG2+JDn5zGT5v6wBF0njqCTAiEAoXQRyJQSsj4wUbBqxqqvGXxPienFDKqXKyKyYy5fYeg="}]}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.10.3": {"name": "typed-function", "version": "0.10.3", "description": "Type checking for JavaScript functions", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/josde<PERSON>"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/luke-gumbley"}], "homepage": "https://github.com/josde<PERSON>/typed-function", "repository": {"type": "git", "url": "git+https://github.com/josdejong/typed-function.git"}, "keywords": ["typed", "function", "arguments", "compose", "types"], "dependencies": {}, "devDependencies": {"istanbul": "^0.3.2", "mocha": "^1.21.5", "uglify-js": "^2.4.15"}, "main": "./typed-function", "scripts": {"minify": "uglifyjs typed-function.js -o typed-function.min.js -c -m", "test": "mocha test --recursive", "coverage": "istanbul cover _mocha -- test --recursive; echo \"\nCoverage report is available at ./coverage/lcov-report/index.html\""}, "engines": {"node": ">= 0.10"}, "gitHead": "213b3b3cbf58ad69f9d12c21765dfb8e24a27345", "bugs": {"url": "https://github.com/josde<PERSON>/typed-function/issues"}, "_id": "typed-function@0.10.3", "_shasum": "29029ad784ed28bb00165aca7b008c202bcf4105", "_from": ".", "_npmVersion": "2.14.4", "_nodeVersion": "4.1.1", "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "29029ad784ed28bb00165aca7b008c202bcf4105", "tarball": "https://registry.npmjs.org/typed-function/-/typed-function-0.10.3.tgz", "integrity": "sha512-I5jb1Z0KxQ01FZBEvd4qayIahOA1qhXW+FT3QQb0xJ9b09Y6QhxF89hUWOBwDsUpp7lJKmw8/GIjVoY+gQemLw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDSeWzSvVjiN9D1hTIcqsZXPfMmSkE/XPJPHNUUksfd7QIhALx5nR5vnd9dPencZ2BMJjJg+ZliweZoekosJVWpXPej"}]}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.10.4": {"name": "typed-function", "version": "0.10.4", "description": "Type checking for JavaScript functions", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/josde<PERSON>"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/luke-gumbley"}], "homepage": "https://github.com/josde<PERSON>/typed-function", "repository": {"type": "git", "url": "git+https://github.com/josdejong/typed-function.git"}, "keywords": ["typed", "function", "arguments", "compose", "types"], "dependencies": {}, "devDependencies": {"istanbul": "^0.3.2", "mocha": "^1.21.5", "uglify-js": "^2.4.15"}, "main": "./typed-function", "scripts": {"minify": "uglifyjs typed-function.js -o typed-function.min.js -c -m", "test": "mocha test --recursive", "coverage": "istanbul cover _mocha -- test --recursive; echo \"\nCoverage report is available at ./coverage/lcov-report/index.html\""}, "engines": {"node": ">= 0.10"}, "gitHead": "74db3c59f189200bb7607307c1956cdc01a93038", "bugs": {"url": "https://github.com/josde<PERSON>/typed-function/issues"}, "_id": "typed-function@0.10.4", "_shasum": "1a015498d38eaf9c402fcce1022a0bd87b7418b2", "_from": ".", "_npmVersion": "3.8.3", "_nodeVersion": "5.10.1", "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "1a015498d38eaf9c402fcce1022a0bd87b7418b2", "tarball": "https://registry.npmjs.org/typed-function/-/typed-function-0.10.4.tgz", "integrity": "sha512-82uL9oVWgW7Qlbr0ugPmNLGSNsjExn3iCB6QUew0UFMPdvAyrbmwhWQQBvhKTEL8Ia4prY3FOmC+gTuyfQ+79w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDuTHAcQG7bHQsO5Ttz64XD3xoc5i4rJ1JrGF3xT+gV8wIhAOAFeDuxymaudHgn14nx1la55Oc6JMKcFh5ly6zvPdpm"}]}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/typed-function-0.10.4.tgz_1460195338871_0.10340209794230759"}, "directories": {}}, "0.10.5": {"name": "typed-function", "version": "0.10.5", "description": "Type checking for JavaScript functions", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/josde<PERSON>"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/luke-gumbley"}], "homepage": "https://github.com/josde<PERSON>/typed-function", "repository": {"type": "git", "url": "git+https://github.com/josdejong/typed-function.git"}, "keywords": ["typed", "function", "arguments", "compose", "types"], "dependencies": {}, "devDependencies": {"istanbul": "0.4.5", "mocha": "3.1.2", "uglify-js": "2.4.15"}, "main": "./typed-function", "scripts": {"minify": "uglifyjs typed-function.js -o typed-function.min.js -c -m", "test": "mocha test --recursive", "coverage": "istanbul cover _mocha -- test --recursive; echo \"\nCoverage report is available at ./coverage/lcov-report/index.html\""}, "engines": {"node": ">= 0.10"}, "gitHead": "9a8cac3cb8ae89fd0a9b3ae2dfde27cd1aa5a60a", "bugs": {"url": "https://github.com/josde<PERSON>/typed-function/issues"}, "_id": "typed-function@0.10.5", "_shasum": "2e0f18abd065219fab694a446a65c6d1981832c0", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "7.1.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "2e0f18abd065219fab694a446a65c6d1981832c0", "tarball": "https://registry.npmjs.org/typed-function/-/typed-function-0.10.5.tgz", "integrity": "sha512-5BI8zKF/aZhsXJJKcRvcOuDCzBVY4R08Ok8ilRWHpQ5Np+LwzWeGzaIUNUw++d19Vvv8iqzvaN9/h5WDdY8cmg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCKAbjP6OEIuBhXPGNs30r4TlBg5zQjBJq2I/NQFmA9wQIhAJVZF9FpxNNMP0Qxswp8D9FARxR7B1WActlHX08HoDZf"}]}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/typed-function-0.10.5.tgz_1479468564987_0.0415192183572799"}, "directories": {}}, "0.10.6": {"name": "typed-function", "version": "0.10.6", "description": "Type checking for JavaScript functions", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/josde<PERSON>"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/luke-gumbley"}], "homepage": "https://github.com/josde<PERSON>/typed-function", "repository": {"type": "git", "url": "git+https://github.com/josdejong/typed-function.git"}, "keywords": ["typed", "function", "arguments", "compose", "types"], "dependencies": {}, "devDependencies": {"istanbul": "0.4.5", "mocha": "3.1.2", "uglify-js": "2.4.15"}, "main": "./typed-function", "scripts": {"minify": "uglifyjs typed-function.js -o typed-function.min.js -c -m", "test": "mocha test --recursive", "coverage": "istanbul cover _mocha -- test --recursive; echo \"\nCoverage report is available at ./coverage/lcov-report/index.html\""}, "engines": {"node": ">= 0.10"}, "gitHead": "3dab82ee255af173e295ddfb70e2646723d7917a", "bugs": {"url": "https://github.com/josde<PERSON>/typed-function/issues"}, "_id": "typed-function@0.10.6", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-PYtsDjxyW3vq7Itn2RMz0cn6CrbybIY6XC2i9c1q1o/H94QW8B1Pf3wSsbBDOCMpN1i5jDRrlDsLXFaqXBpfHQ==", "shasum": "314aa0ea72bd586de5920095559683e20b01688b", "tarball": "https://registry.npmjs.org/typed-function/-/typed-function-0.10.6.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG47itrVoFcrdOyA2Lt7kq6HdUF3tZ+pBRBtCDoH8BSGAiBAuxAAxkTlNNPznehIKAMuycKkjEtZREhKNwRb32nl7A=="}]}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typed-function-0.10.6.tgz_1511032634036_0.07693882496096194"}, "directories": {}}, "0.10.7": {"name": "typed-function", "version": "0.10.7", "description": "Type checking for JavaScript functions", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/josde<PERSON>"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/luke-gumbley"}], "homepage": "https://github.com/josde<PERSON>/typed-function", "repository": {"type": "git", "url": "git+https://github.com/josdejong/typed-function.git"}, "keywords": ["typed", "function", "arguments", "compose", "types"], "dependencies": {}, "devDependencies": {"istanbul": "0.4.5", "mocha": "3.1.2", "uglify-js": "2.4.15"}, "main": "./typed-function", "scripts": {"minify": "uglifyjs typed-function.js -o typed-function.min.js -c -m", "test": "mocha test --recursive", "coverage": "istanbul cover _mocha -- test --recursive; echo \"\nCoverage report is available at ./coverage/lcov-report/index.html\""}, "engines": {"node": ">= 0.10"}, "gitHead": "7e605c941ea5ce28896ea0afe4fb00e15eadd4e0", "bugs": {"url": "https://github.com/josde<PERSON>/typed-function/issues"}, "_id": "typed-function@0.10.7", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.4", "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-3mlZ5AwRMbLvUKkc8a1TI4RUJUS2H27pmD5q0lHRObgsoWzhDAX01yg82kwSP1FUw922/4Y9ZliIEh0qJZcz+g==", "shasum": "f702af7d77a64b61abf86799ff2d74266ebc4477", "tarball": "https://registry.npmjs.org/typed-function/-/typed-function-0.10.7.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHgPSiAJZjrs/ZKvAStjo/S1X7/JAkdEaWrlOIzIFCMYAiBEUUgAfryomGXJ18ApuMm6QpgLwRKmka3jmAFF/Pc6rg=="}]}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typed-function-0.10.7.tgz_1516796709305_0.42829554900527"}, "directories": {}}, "1.0.0": {"name": "typed-function", "version": "1.0.0", "description": "Type checking for JavaScript functions", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/josde<PERSON>"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/luke-gumbley"}], "homepage": "https://github.com/josde<PERSON>/typed-function", "repository": {"type": "git", "url": "git+https://github.com/josdejong/typed-function.git"}, "keywords": ["typed", "function", "arguments", "compose", "types"], "dependencies": {}, "devDependencies": {"benchmark": "2.1.4", "istanbul": "0.4.5", "mocha": "5.0.1", "pad-right": "0.2.2", "uglify-js": "3.3.11"}, "main": "./typed-function", "scripts": {"build": "uglifyjs typed-function.js -o typed-function.min.js -c -m", "test": "mocha test --recursive", "coverage": "istanbul cover _mocha -- test --recursive; echo \"\nCoverage report is available at ./coverage/lcov-report/index.html\"", "prepublishOnly": "npm test && npm run build"}, "engines": {"node": ">= 6"}, "gitHead": "c6b249ca1b04b79a0db315a9744acaf8896a5574", "bugs": {"url": "https://github.com/josde<PERSON>/typed-function/issues"}, "_id": "typed-function@1.0.0", "_npmVersion": "5.6.0", "_nodeVersion": "9.5.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-bHRCYBG3pgkXoaiJiVrA4rNHjUCcEEMFdKzQM/wezbUiP0DsWUTBissC6wm0M1zNe719D7zVwgAhIOA/gRQv/g==", "shasum": "5dd931ebb0d0f0cc47759454a4bac3f20c1c87ee", "tarball": "https://registry.npmjs.org/typed-function/-/typed-function-1.0.0.tgz", "fileCount": 31, "unpackedSize": 137312, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEAK1YPydCGJKmBasghdfb5l6kAv5B7aOdyayPi2QvOAAiEA7pJHPUJWN5ARs5ZIEZTJmFBenpeMr+MP0cUGWolN4HU="}]}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typed-function_1.0.0_1519156238136_0.05959754666349992"}, "_hasShrinkwrap": false}, "1.0.1": {"name": "typed-function", "version": "1.0.1", "description": "Type checking for JavaScript functions", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/josde<PERSON>"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/luke-gumbley"}], "homepage": "https://github.com/josde<PERSON>/typed-function", "repository": {"type": "git", "url": "git+https://github.com/josdejong/typed-function.git"}, "keywords": ["typed", "function", "arguments", "compose", "types"], "dependencies": {}, "devDependencies": {"benchmark": "2.1.4", "istanbul": "0.4.5", "mocha": "5.0.1", "pad-right": "0.2.2", "uglify-js": "3.3.11", "brace-expansion": "1.1.11"}, "comment": "brace-expansion is installed because an old insecure version is used by one of the dev depencencies (under istanbul)", "main": "./typed-function", "scripts": {"build": "uglifyjs typed-function.js -o typed-function.min.js -c -m", "test": "mocha test --recursive", "coverage": "istanbul cover _mocha -- test --recursive; echo \"\nCoverage report is available at ./coverage/lcov-report/index.html\"", "prepublishOnly": "npm test && npm run build"}, "engines": {"node": ">= 6"}, "gitHead": "5634e548308da41a4cf9acfb353df900f0284b47", "bugs": {"url": "https://github.com/josde<PERSON>/typed-function/issues"}, "_id": "typed-function@1.0.1", "_npmVersion": "5.6.0", "_nodeVersion": "9.5.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Ie5d+HS39FU+sKj5nzcSV9pucMOtHsomaZPaxX9CWnxeqcdBkGl0cGKx1xd5v+b1czUd1iVa/RMZbsN8wnfGPg==", "shasum": "2fc13787268764cb09633fe57be5bd089180668a", "tarball": "https://registry.npmjs.org/typed-function/-/typed-function-1.0.1.tgz", "fileCount": 31, "unpackedSize": 137539, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDr1ZpB92guia9lopebp4QJ1QJay8B/qbMxlZ1eE539YgIgBOoYjrPRUMYNjrE0eJb6GnBjQhKYIWXlA5zkWacjhbk="}]}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typed-function_1.0.1_1519208122132_0.3169087716870247"}, "_hasShrinkwrap": false}, "1.0.2": {"name": "typed-function", "version": "1.0.2", "description": "Type checking for JavaScript functions", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/josde<PERSON>"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/luke-gumbley"}], "homepage": "https://github.com/josde<PERSON>/typed-function", "repository": {"type": "git", "url": "git+https://github.com/josdejong/typed-function.git"}, "keywords": ["typed", "function", "arguments", "compose", "types"], "dependencies": {}, "devDependencies": {"benchmark": "2.1.4", "istanbul": "0.4.5", "mocha": "5.0.1", "pad-right": "0.2.2", "uglify-js": "3.3.11", "brace-expansion": "1.1.11"}, "comment": "brace-expansion is installed because an old insecure version is used by one of the dev depencencies (under istanbul)", "main": "./typed-function", "scripts": {"build": "uglifyjs typed-function.js -o typed-function.min.js -c -m", "test": "mocha test --recursive", "coverage": "istanbul cover _mocha -- test --recursive; echo \"\nCoverage report is available at ./coverage/lcov-report/index.html\"", "prepublishOnly": "npm test && npm run build"}, "engines": {"node": ">= 6"}, "gitHead": "8933f9b42f85d1e575e401549ebe7803b7cf652b", "bugs": {"url": "https://github.com/josde<PERSON>/typed-function/issues"}, "_id": "typed-function@1.0.2", "_npmVersion": "5.7.1", "_nodeVersion": "9.8.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-TG898+mFCYMxgpFlIGTRMialtWh7nqIEcUQvRkaFZfWjOBSmIKI5DnMcoRSdelxilxBS915g9yKAH7ttBtlplA==", "shasum": "cb9110708fef26dedeb9b449348ecb4371c08c4a", "tarball": "https://registry.npmjs.org/typed-function/-/typed-function-1.0.2.tgz", "fileCount": 31, "unpackedSize": 138171, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCuNL3gtZmi8Ahqr3PknAZXIHvADnlcHx9X88R+fhypRwIgbhQtwJRvaa9qpV9ypTlMnLAb3plSYGHdpnbM5kzS/7c="}]}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typed-function_1.0.2_1521314179864_0.5814894604966818"}, "_hasShrinkwrap": false}, "1.0.3": {"name": "typed-function", "version": "1.0.3", "description": "Type checking for JavaScript functions", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/josde<PERSON>"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/luke-gumbley"}], "homepage": "https://github.com/josde<PERSON>/typed-function", "repository": {"type": "git", "url": "git+https://github.com/josdejong/typed-function.git"}, "keywords": ["typed", "function", "arguments", "compose", "types"], "dependencies": {}, "devDependencies": {"benchmark": "2.1.4", "istanbul": "0.4.5", "mocha": "5.0.1", "pad-right": "0.2.2", "uglify-js": "3.3.11", "brace-expansion": "1.1.11"}, "comment": "brace-expansion is installed because an old insecure version is used by one of the dev depencencies (under istanbul)", "main": "./typed-function", "scripts": {"build": "uglifyjs typed-function.js -o typed-function.min.js -c -m", "test": "mocha test --recursive", "coverage": "istanbul cover _mocha -- test --recursive; echo \"\nCoverage report is available at ./coverage/lcov-report/index.html\"", "prepublishOnly": "npm test && npm run build"}, "engines": {"node": ">= 6"}, "gitHead": "b347b507ad7c16eed19134b5c7fa3a2372306d29", "bugs": {"url": "https://github.com/josde<PERSON>/typed-function/issues"}, "_id": "typed-function@1.0.3", "_npmVersion": "5.7.1", "_nodeVersion": "9.8.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-sVC/1pm70oELDFMdYtFXMFqyawenLoaDiAXA3QvOAwKF/WvFNTSJN23cY2lFNL8iP0kh3T0PPKewrboO8XUVGQ==", "shasum": "57026246214b664a5af45ef0a06679f4453bd090", "tarball": "https://registry.npmjs.org/typed-function/-/typed-function-1.0.3.tgz", "fileCount": 31, "unpackedSize": 138894, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIESOy/HBgmaXAzEfpRRKzB7Xe/Ej0aURGL0FindRu4f3AiAMp1xNAXlfUOFtage9YiLVf6sQtpyOh1cvFZLHSfFjLg=="}]}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typed-function_1.0.3_1521314962570_0.21930363743369985"}, "_hasShrinkwrap": false}, "1.0.4": {"name": "typed-function", "version": "1.0.4", "description": "Type checking for JavaScript functions", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/josde<PERSON>"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/luke-gumbley"}], "homepage": "https://github.com/josde<PERSON>/typed-function", "repository": {"type": "git", "url": "git+https://github.com/josdejong/typed-function.git"}, "keywords": ["typed", "function", "arguments", "compose", "types"], "dependencies": {}, "devDependencies": {"benchmark": "2.1.4", "istanbul": "0.4.5", "mocha": "5.2.0", "pad-right": "0.2.2", "uglify-js": "3.4.3", "brace-expansion": "1.1.11"}, "comment": "brace-expansion is installed because an old insecure version is used by one of the dev depencencies (under istanbul)", "main": "./typed-function", "scripts": {"build": "uglifyjs typed-function.js -o typed-function.min.js -c -m", "test": "mocha test --recursive", "coverage": "istanbul cover _mocha -- test --recursive; echo \"\nCoverage report is available at ./coverage/lcov-report/index.html\"", "prepublishOnly": "npm test && npm run build"}, "engines": {"node": ">= 6"}, "gitHead": "ea5b1e6ee3b55c77d1925a8a036b17d394b0b1ba", "bugs": {"url": "https://github.com/josde<PERSON>/typed-function/issues"}, "_id": "typed-function@1.0.4", "_npmVersion": "6.1.0", "_nodeVersion": "10.5.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-G85BDe07dC/JgOaxfnJ0RomuDKuon797UEgF50G0zMmZ7tay/fbJxusuFJz5s0ZUrvx2z7uaDvZD5KavqJbKKw==", "shasum": "6b25993b8587b0a5c3356585d6ef99aa63f6d317", "tarball": "https://registry.npmjs.org/typed-function/-/typed-function-1.0.4.tgz", "fileCount": 31, "unpackedSize": 140677, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbPJ+0CRA9TVsSAnZWagAAdpcP/RWX4q7UH9miS69gWYxv\nIxyZQs2qdIpjS52ZfLc+HYSDZOc+sMQLSPRqHUmcQLP0w+WGKijJM1nfglF3\nmivbzYSBGgyYDOEIOW4rEsjA4zMYE2gqEc3iiJQVHZTG2hX1hY7IKJjXTQme\n5kauRzDq1ni0+gXPu55QuOROhWgjBjS28IGZiHBuQV6dvqNi13Slneqmzgsb\nJweqdSjAhh5Fd3V/FDsLMsvBqC48l4aYcyzgRWIdmzoqIcUuzHgvbXhJ5nBP\nFlG4YFIhzg19Gp/GTkLykhHE6HgqrxphXWAt6eHsXp+06Xqn9mRYZniqs9lW\nCCaN6ynwfz0SZ7ElV1NUvqrFZ60Nz6avU2VQyFvv2a3mIkuZM3bZkJWcwqG4\n2TwnuCkKg1YyJ/2PUfpPESlM0dKGnMxmM0HOfWkagrIrpmWVazZbtdKP96S5\nUbX8ZcZiIAKfuq93GhpklEhZR9oJKyQO9BF2xRxN1Y3sU2Lmj4J92+LA4uPZ\n510FCXmfnVJsttULHbwB43sIcIINAdVNaJtDW7aTpd1wTbNyOug6sxwpEgzg\nKuMmK+/ZP2D9+zIr9FBVEBr25aXcgRt67DvA9UQzeMqySRMTxduw32bqhQTH\n3mSA6gEPDYXI303bHPupEwVqUnlQEjQfecqB7VgQ0Gr6vQeiXYEsP+Im3aeP\n3SUX\r\n=8OWX\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCYHLhNMiAwRaLtqa8oq9zUZWEFMqwZxtA5DjlrTCncTQIhAN0a5o3VuwdPqbVB/8IAiD56J6GC62cOc3HnNydmbdYE"}]}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typed-function_1.0.4_1530699700228_0.7686929397850222"}, "_hasShrinkwrap": false}, "1.1.0": {"name": "typed-function", "version": "1.1.0", "description": "Type checking for JavaScript functions", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/josde<PERSON>"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/luke-gumbley"}], "homepage": "https://github.com/josde<PERSON>/typed-function", "repository": {"type": "git", "url": "git+https://github.com/josdejong/typed-function.git"}, "keywords": ["typed", "function", "arguments", "compose", "types"], "dependencies": {}, "devDependencies": {"benchmark": "2.1.4", "istanbul": "0.4.5", "mocha": "5.2.0", "pad-right": "0.2.2", "uglify-js": "3.4.3", "brace-expansion": "1.1.11"}, "comment": "brace-expansion is installed because an old insecure version is used by one of the dev depencencies (under istanbul)", "main": "./typed-function", "scripts": {"build": "uglifyjs typed-function.js -o typed-function.min.js -c -m", "test": "mocha test --recursive", "coverage": "istanbul cover _mocha -- test --recursive; echo \"\nCoverage report is available at ./coverage/lcov-report/index.html\"", "prepublishOnly": "npm test && npm run build"}, "engines": {"node": ">= 6"}, "gitHead": "aff577b45f4df8bf641cfc128b9e386d31b90809", "bugs": {"url": "https://github.com/josde<PERSON>/typed-function/issues"}, "_id": "typed-function@1.1.0", "_npmVersion": "6.1.0", "_nodeVersion": "10.7.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-TuQzwiT4DDg19beHam3E66oRXhyqlyfgjHB/5fcvsRXbfmWPJfto9B4a0TBdTrQAPGlGmXh/k7iUI+WsObgORA==", "shasum": "ea149706e0fb42aca1791c053a6d94ccd6c4fdcb", "tarball": "https://registry.npmjs.org/typed-function/-/typed-function-1.1.0.tgz", "fileCount": 32, "unpackedSize": 143605, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbXME/CRA9TVsSAnZWagAAzoQP/3wgVsIE8O0FXYXkpCoN\nbNmwZ8NS/mYjgmxDhV4f/W3dTlqPToQYfGstl2QylwpjIbS255wxzFthNsD5\n8EopRLhj2Cyww0+DRENpiA3FV3ezQBNLOK5sDEN/1PSFA8IjqACepM4gUxhh\nMp8KwY/H33Jwqv3e1CY3a2EN0UibuCyzRRt8fx0bbe6qjlSJA70kTqTD8cPH\no2tRVWcDQPdK98bSU5DkZ18sQQ7HrRUlI4UdvQNlVAT2hfxpwPZfE7+urjLq\n7Y0Y4tu6g5qBuYdLxR2XS8V8izvIpchcg1a9LXg7L4u3rNgk0JTwtAdqW/c3\nsyAXfCaEH0WvMKeytYgzglezyVXcMDN0QatFia1W/GeZ+2Uap5GzgBhBMCCU\n1orkwpQ/fsakqftlJYRdvuuqFUoCagtof+4TzYizrtjS9TBuiChXiKVy88Yd\ng2sckLjm4az0PvBoSihiRkLs4r26UPUm8z9Bowtl/J0SoPg+TotgU1cMZjEM\nxz4CY5hD00sY2zOc/leeRdBdfHfJUoUc2xhHizZwn17wQP7pxpN5O1R5fRKu\nF/NqUvqWH710XNV1jR9EcHxLle/PPhTc/L6g/eURYD1DK5mz7D6sIa06zjZC\nLMQUEAD6ndZInisAfjj5SaMAEkzctFMG0K15qpp9iqxCEtN1492uMxBA7Ttw\nmaFt\r\n=0cOg\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCiW4TfOG1nVSVXCSnDwL9TdtwYyViqeVNboM3T6P56nAIgbaGekIZseKEUqR3DMKTXYhDJT8NiOu+mMrmXK2o6TZE="}]}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typed-function_1.1.0_1532805439394_0.5868819983995099"}, "_hasShrinkwrap": false}, "1.1.1": {"name": "typed-function", "version": "1.1.1", "description": "Type checking for JavaScript functions", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/josde<PERSON>"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/luke-gumbley"}], "homepage": "https://github.com/josde<PERSON>/typed-function", "repository": {"type": "git", "url": "git+https://github.com/josdejong/typed-function.git"}, "keywords": ["typed", "function", "arguments", "compose", "types"], "dependencies": {}, "devDependencies": {"benchmark": "2.1.4", "istanbul": "0.4.5", "mocha": "5.2.0", "pad-right": "0.2.2", "uglify-js": "3.4.3", "brace-expansion": "1.1.11"}, "comment": "brace-expansion is installed because an old insecure version is used by one of the dev depencencies (under istanbul)", "main": "./typed-function", "scripts": {"build": "uglifyjs typed-function.js -o typed-function.min.js -c -m", "test": "mocha test --recursive", "coverage": "istanbul cover _mocha -- test --recursive; echo \"\nCoverage report is available at ./coverage/lcov-report/index.html\"", "prepublishOnly": "npm test && npm run build"}, "engines": {"node": ">= 6"}, "gitHead": "675c8f26a6b47f56f779ddd05e066caed8b65ded", "bugs": {"url": "https://github.com/josde<PERSON>/typed-function/issues"}, "_id": "typed-function@1.1.1", "_nodeVersion": "12.8.1", "_npmVersion": "6.10.3", "dist": {"integrity": "sha512-RbN7MaTQBZLJYzDENHPA0nUmWT0Ex80KHItprrgbTPufYhIlTePvCXZxyQK7wgn19FW5bnuaBIKcBb5mRWjB1Q==", "shasum": "a1316187ec3628c9e219b91ca96918660a10138e", "tarball": "https://registry.npmjs.org/typed-function/-/typed-function-1.1.1.tgz", "fileCount": 32, "unpackedSize": 144612, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXqRZCRA9TVsSAnZWagAAcNsP/3v1cZMDQaXTmvSgOIuf\nPnRpvwIKaG7mFcpBBmVIUvu3s5OnAZKUoBC5Hvt8rn5PEYJz80AEBeGB8OkH\nVZLQnjelso1yUzPfcIbbq0B41jyqoFrZtXTGvfgUOTpVQpJsIDPxCMzqGC+i\nJ6nMhMA3r4s/q52N5n6im22kBVxpj7SpqpGZUdrDcjv0OGseAKLXfSEdQLP8\nBVI1iEpq0sNiKWQqLabc7X06BJ706I8WaOzgAtvhCmzRu8jjrlmtDvPQAcOG\nLbNyNzXH+0yGdg4CLozUKJy7PkfjonNqjaC4ZMmZzJKSnxMqy3h0sIYNTqfN\nYbYZK5AZUxcgf9eRacJhf7RzocCkqhFLShHPpU/Y3pvgL3dOXSELdqbWeEJH\n47GUrZXgJeIC5MFZWzkodgrzNwfOQgzQL6Y2Z+CWcsJtlYLMhRTgDhzjz799\nihp0iwHf3yLVj7hw+11Aky95PiQyXEm8e9KZBQ/0q7RG3SDkcdjhMfr/FdEd\n1hRKX0PvjFfn5bgeH+JxnXphKB1VLt17qrlzzj3QunVdtNX/vDsvBVou6P7t\n0EtL7ZsK7r/kCqcSlYiULTlDL/TgnOu9F++dIm5Q9JB9HjWwJmNdliOPPmXq\ne9PixazQrOeYdPEjmTlecSb9SWeDbRd3ZbFT8rKFdU84knejtKAFX3VZ7kvb\nDm92\r\n=xKpV\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAyacJZeuXYJh+/FEkTwbOskYqAE2ssRFWmYR3yH/7LJAiEAqFa7khpzEnEcEsxBhlVXGhpR9qfs65nO7bhIOLfFOGw="}]}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typed-function_1.1.1_1566483544306_0.49571876445301766"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "typed-function", "version": "2.0.0", "description": "Type checking for JavaScript functions", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/josde<PERSON>"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/luke-gumbley"}], "homepage": "https://github.com/josde<PERSON>/typed-function", "repository": {"type": "git", "url": "git+https://github.com/josdejong/typed-function.git"}, "keywords": ["typed", "function", "arguments", "compose", "types"], "dependencies": {}, "devDependencies": {"benchmark": "2.1.4", "istanbul": "0.4.5", "mocha": "8.0.1", "pad-right": "0.2.2", "uglify-js": "3.10.0", "brace-expansion": "1.1.11"}, "comment": "brace-expansion is installed because an old insecure version is used by one of the dev depencencies (under istanbul)", "main": "typed-function.js", "scripts": {"build": "uglifyjs typed-function.js -o typed-function.min.js -c -m", "test": "mocha test --recursive", "coverage": "istanbul cover _mocha -- test --recursive; echo \"\nCoverage report is available at ./coverage/lcov-report/index.html\"", "prepublishOnly": "npm test && npm run build"}, "engines": {"node": ">= 8"}, "gitHead": "b2b4e4cb46d0f7f285a2be6c085db15912f95254", "bugs": {"url": "https://github.com/josde<PERSON>/typed-function/issues"}, "_id": "typed-function@2.0.0", "_nodeVersion": "14.3.0", "_npmVersion": "6.14.5", "dist": {"integrity": "sha512-Hhy1Iwo/e4AtLZNK10ewVVcP2UEs408DS35ubP825w/YgSBK1KVLwALvvIG4yX75QJrxjCpcWkzkVRB0BwwYlA==", "shasum": "15ab3825845138a8b1113bd89e60cd6a435739e8", "tarball": "https://registry.npmjs.org/typed-function/-/typed-function-2.0.0.tgz", "fileCount": 33, "unpackedSize": 146551, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe/s5vCRA9TVsSAnZWagAAeNIP/iM3oHB2Z8ah+QTgWEht\nRkOZx5qrcQKveVFhBGGFMZLlqlFR1TIwFmeJeyRe9p8d9pODYn0GnrYqTWOZ\nqgR52vWoyt/0mO1duHpu+7a3Evw/EwqVmmko5+bz4ZmVdEPGBksfRc+5smE5\nI8o4L8XzZ6HBK4ISuJ9vXUFAOiTsSFsmzJSlCRPvxhNDDLkyQLVEjmdRM2tu\nOtrEkBXjG5xNP73x8t7jaGQVPgwZcjtQB6otWopjMEKhINMKHZGLaUwNPHpj\nhBUMEfJiZke0Jj1rFUdWMQ42oazp81ZerHEMkBvuGDhHHeTTY+2xLAFZPita\nziVzDq+P1ngcNmotroQZYNEpnXSU/LUcm+TsSjfDDyp0YCb4CeabbIE5i7qr\n+mbuPhAs6hpbh2XU4HGoJthirwuJYwUBlgW6YI4eVXLe9lxtPf+jAMqUkngs\nlvsecp1Vxl+MXOUyJZW9sD1F/WLp74D7//aj60EyyQQC8IRmapo1Be+vOPO5\nK7kdk2eI6/Mfrhz3pc4DoFdF8dZbKVxuN1mhuRlXEREXNQNIpZPCpFJ1dTBG\nepuJV2zcuMCdZf9N0mW6Wwwnw6ds40RpKxV69iIw3F1KrkDCKPesV3aRtrZp\n+oRth6xXJ6O5x9b8gIGsICCz1bKkbu21KltwTNaE0KIqJRpiTSXMqNIyxBt/\nETcj\r\n=qejp\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBpch0mGpIa4ZvI9kyiCRaGUXJmBlb0+KgvyhTkIuvQJAiEAx68zxRxwL1DoeYHoCZgUalg+a0k+sMBLbzvHUqPmLwU="}]}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typed-function_2.0.0_1593757295387_0.8381953343698381"}, "_hasShrinkwrap": false}, "2.1.0": {"name": "typed-function", "version": "2.1.0", "description": "Type checking for JavaScript functions", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/josde<PERSON>"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/luke-gumbley"}], "homepage": "https://github.com/josde<PERSON>/typed-function", "repository": {"type": "git", "url": "git+https://github.com/josdejong/typed-function.git"}, "keywords": ["typed", "function", "arguments", "compose", "types"], "dependencies": {}, "devDependencies": {"benchmark": "2.1.4", "brace-expansion": "2.0.1", "istanbul": "0.4.5", "mocha": "8.3.2", "pad-right": "0.2.2", "uglify-js": "3.13.3"}, "comment": "brace-expansion is installed because an old insecure version is used by one of the dev depencencies (under istanbul)", "main": "typed-function.js", "scripts": {"build": "uglifyjs typed-function.js -o typed-function.min.js -c -m", "test": "mocha test --recursive", "coverage": "istanbul cover _mocha -- test --recursive; echo \"\nCoverage report is available at ./coverage/lcov-report/index.html\"", "prepublishOnly": "npm test && npm run build"}, "engines": {"node": ">= 10"}, "gitHead": "25591d8f5cacd40e3becb920a84b6e415e4744d1", "bugs": {"url": "https://github.com/josde<PERSON>/typed-function/issues"}, "_id": "typed-function@2.1.0", "_nodeVersion": "16.13.1", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-bctQIOqx2iVbWGDGPWwIm18QScpu2XRmkC19D8rQGFsjKSgteq/o1hTZvIG/wuDq8fanpBDrLkLq+aEN/6y5XQ==", "shasum": "ded6f8a442ba8749ff3fe75bc41419c8d46ccc3f", "tarball": "https://registry.npmjs.org/typed-function/-/typed-function-2.1.0.tgz", "fileCount": 35, "unpackedSize": 153318, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiKx0ZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpqTw//UBvHgzKFByAidTZeFnGQ/iYWAQUMRs3CdDSPcswdyyX12WcD\r\nmKqGjHm5hKJYQaQX4nwbBz+FrYflU61rrI3rtRsKby6ClniKYng8ultaAJn7\r\ny+KQ54UHzkW3r1f1hJKNDjeH5dN5IMq9C4+MaM6oDdlzwWMneGf0VKx9XoDV\r\nsuBkBRoOhP2vVsHz/1ZtkG65MTxolFQrqPp3KYahuN0KPJz2M7BXaFUVdUBD\r\nSTZhIJQz3A/zAUGiGU7R4/kEo+RY+1OAplBL4petygbEXBMOQ6xmN9K5fzCv\r\nvB1W+iaHo5X2DfZOIPN1ryuUniVp2RTK3kE4xqRdVPz/YZ6m8bFSQni/H8r/\r\nQTjTpeQkhHMctNBBdPljsKNMBXBtm/L04ltJY8jIAIv0aS/O0Kk7qBQ8yOB7\r\nci+dx6MGtjjyvlYjX48ce4pRUbvfymTcXPesKbHVv2y4MyYDG1sEHWBIbmBy\r\nZYZe1O1YeFNQyPX3j/HU/8Sfj2kiklo9m69FISNT6oow7pePSjKVb6tuAnO0\r\n903Dq1Ih8znEdsyhx8BY5V9XHJMASzTPOEs7tBwAWNHApiEN46yq07mYJVH4\r\nFYx1Q3LWXp0URe8l0wMp5d31KBfBHsHkqCWbmz/iH3LCpeNThzgeibkn3g2F\r\ncRdD57aJ4RS0NruOOaHpV4MwGIgmNIcStDY=\r\n=Ra96\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDWugp97kd26WMzbSZQA7hohqX0jmUSHonEdYMSGTQGkwIgFX0TvZ1J0QrIec2eIuSoPLqIw5lrlSWreoISwMcHSx0="}]}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typed-function_2.1.0_1646992664958_0.9739749267728033"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "typed-function", "version": "3.0.0", "description": "Type checking for JavaScript functions", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/josde<PERSON>"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/luke-gumbley"}], "homepage": "https://github.com/josde<PERSON>/typed-function", "repository": {"type": "git", "url": "git+https://github.com/josdejong/typed-function.git"}, "keywords": ["typed", "function", "arguments", "compose", "types"], "dependencies": {}, "devDependencies": {"benchmark": "2.1.4", "brace-expansion": "2.0.1", "nyc": "15.1.0", "mocha": "10.0.0", "pad-right": "0.2.2", "uglify-js": "3.15.5"}, "comment": "brace-expansion is installed because an old insecure version is used by one of the dev depencencies (under istanbul)", "main": "typed-function.js", "scripts": {"build": "uglifyjs typed-function.js -o typed-function.min.js -c -m", "test": "mocha test --recursive", "coverage": "nyc _mocha -- test --recursive; echo \"\nCoverage report is available at ./coverage/lcov-report/index.html\"", "prepublishOnly": "npm test && npm run build"}, "engines": {"node": ">= 14"}, "gitHead": "7308eb848f5f1abbdcbdddd7497363b25fb86ff0", "bugs": {"url": "https://github.com/josde<PERSON>/typed-function/issues"}, "_id": "typed-function@3.0.0", "_nodeVersion": "16.13.1", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-mKJKkt2xYxJUuMD7jyfgUxfn5KCsCxkEKBVjep5yYellJJ5aEDO2QUAmIGdvcZmfQnIrplkzELIaG+5b1475qg==", "shasum": "42f75ffdd7dd63bf5dcc950847138f2bb65f1ad3", "tarball": "https://registry.npmjs.org/typed-function/-/typed-function-3.0.0.tgz", "fileCount": 37, "unpackedSize": 212763, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDAi+kM9UUyDuMeqDNj3f0J1DABsYjKzPNcbG3/BYoLqgIhAPiXWU9+9z84jMsfk6JgfJETGTPmyOS3KehaxgSBz/Yf"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifMtvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmplxA/+KgIkO7EA44MGBYeyR0Ikan+3puRQF2NUKQ2ovBnLTK2gff/p\r\njcZulS+s6ThSsKwVvJRT8i10IyTl5pVu7GM6aT1oEkbtMgzzVrBKxTByXBkt\r\n3ERRoBce3Jq9xHYuEAfG1bp6PUqVlUL8RyvvFbSkmlXbo9T+6Unyg5fMDz43\r\nssl/0VW+h5qRownDZ54Lg4TGE8j1Fdt8tRswj/OtK21rOajF7STW9Oo5go6S\r\nG0WAdL61fEWFmsH8+N6zKA+WhhK9RU+GRGqVRWCKKG8Gnj9HMj8EMVZNjnlk\r\nepEBpUypWRV3eE3Rh41BHofjp0hkdO84qW0c7qTaxyUZUEjPUW1Gb1q/Sn3x\r\nHgifczA7qmB31h3p0B9C6YvN8eGAX5XH8p5WJuTDuYs9vLsjOY2TlL0PW3pu\r\nzX1sUIBsHAb/C+O357mq0lHWowiIapLjx2EtJlr0lmpWPB20MGKu6hnFNmr6\r\nnIFc66LgtdTEH29GnRKGXt/tKUB1GbDShMdL48opN60CyBLMnOS54hhiTne4\r\nLfss5oviJATToOD2i9XmtLKTLaGQjdAn5IDCqUawXX3yMuFWFtyIrwADUrrB\r\nT1pJIB59FeiOydlC+CE41EXkAcGJuUgRq65zIPMa3YLf782Fvzj3uwUzyElk\r\nCilMOdsDM8ptYw88d2DOfv+GzGJ9xrlxymA=\r\n=D+Vj\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typed-function_3.0.0_1652345711288_0.7863844535897273"}, "_hasShrinkwrap": false}, "3.0.1": {"name": "typed-function", "version": "3.0.1", "description": "Type checking for JavaScript functions", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/josde<PERSON>"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/luke-gumbley"}], "homepage": "https://github.com/josde<PERSON>/typed-function", "repository": {"type": "git", "url": "git+https://github.com/josdejong/typed-function.git"}, "keywords": ["typed", "function", "arguments", "compose", "types"], "dependencies": {}, "devDependencies": {"benchmark": "2.1.4", "brace-expansion": "2.0.1", "nyc": "15.1.0", "mocha": "10.0.0", "pad-right": "0.2.2", "uglify-js": "3.16.3"}, "comment": "brace-expansion is installed because an old insecure version is used by one of the dev depencencies (under istanbul)", "main": "typed-function.js", "scripts": {"build": "uglifyjs typed-function.js -o typed-function.min.js -c -m", "test": "mocha test --recursive", "coverage": "nyc _mocha -- test --recursive; echo \"\nCoverage report is available at ./coverage/lcov-report/index.html\"", "prepublishOnly": "npm test && npm run build"}, "engines": {"node": ">= 14"}, "gitHead": "0bbf181c333e3aed288bcf7607189ab5a1490d4b", "bugs": {"url": "https://github.com/josde<PERSON>/typed-function/issues"}, "_id": "typed-function@3.0.1", "_nodeVersion": "16.13.2", "_npmVersion": "8.16.0", "dist": {"integrity": "sha512-bHs+Nfx5fu7BFWxxwqOv+toxYmMYzyqU+/3wdVk/z2Yz5l9kPJtTkk8sz3dqmWtz7bLIsAboIIrs4nhBBKWnXQ==", "shasum": "337ebc833be1e326148c309daa865dc639500be6", "tarball": "https://registry.npmjs.org/typed-function/-/typed-function-3.0.1.tgz", "fileCount": 37, "unpackedSize": 213932, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC49+N1mKpdcsckE6Gv/z3qVoLNXxf7qSd9KveTJj8xfQIhAIZb+qfnalGva9n6fDQIISF4T2zRTNYZV8ZVe4ad3xcw"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi+6nWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrBew/+NiE55t56xb6o5ZM77rKUbRpkWctE9rRhaIB3L/DgwfXGlR9Y\r\n4BZVy5mLKK65EWzRX5Xc80JRXG4/ZevzGn6xeS0GEyontDLzOZEeMNqkOBB0\r\nN1cocaLt+XPdnJs6KcAqa32xT7ETMUVsMKB0AHY7F+2mhLUqsMNH7+mrNFLe\r\nnOBErSuOuqIp8BMibbkCHTNmdopJ4ZsNPS2ngUMXarwIeBEGuSj3WqikRSrq\r\n5EWeZI4WVi+oPvnmXloQIh2fDWqrOH4HxNOps6yt8LS190Sm2xF9FmOXhn2k\r\nT4zZELupQLwEy+u49V8f/1SQgMnQrtoB7U8Cbfc7Z9p7OI4c/EGZ3+LsuvJX\r\ngSmEwZW6za0fae8VP0fMe5TrpgFS6XcPlvC01MGRQ+hyQAXpvdteZksRrUrd\r\nfMf0z84sLYfXGxsS/zJH+DwEzrzwUJRHnbiMHjesbKF+SOWOirABecxOzXM8\r\nOo1PJIpr5sdcHuLuiRd4p8C3c0yXVZ7//t3JDSjo/A1rImZ+MXfth06M0rL0\r\nfMNA1Qo2jo7oDItyxb1usKL+v3yBq0WrZG7IemTsgyT+Ka/XsI+turMSZw1c\r\nt2eRrMb1NzKxowtcLKgX7d53Fro8RbFejgWQJEl1iKz+d0kHl0MseyFs43F4\r\nVUns8S0qzzFi1nIsxRKfoUKeZYjA+OWE5rQ=\r\n=YaIU\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typed-function_3.0.1_1660660182276_0.7985659104146805"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "typed-function", "version": "4.0.0", "description": "Type checking for JavaScript functions", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/josde<PERSON>"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/gwhitney"}, {"name": "<PERSON>", "url": "https://github.com/luke-gumbley"}], "homepage": "https://github.com/josde<PERSON>/typed-function", "repository": {"type": "git", "url": "git+https://github.com/josdejong/typed-function.git"}, "keywords": ["typed", "function", "arguments", "compose", "types"], "type": "module", "main": "lib/esm/typed-function.js", "scripts": {"test": "mocha test --recursive", "test:lib": "mocha test test-lib --recursive", "build": "babel src --out-dir lib/esm --source-maps --config-file ./babel.config.json", "build-and-test": "npm run lint && npm run build && npm run test:lib", "lint": "eslint --cache src/**/*.mjs test/**/*.mjs test-lib/**/*.mjs", "format": "npm run lint -- --fix", "coverage": "nyc _mocha -- test --recursive; echo \"\nCoverage report is available at ./coverage/lcov-report/index.html\"", "prepublishOnly": "npm run build-and-test"}, "engines": {"node": ">= 14"}, "devDependencies": {"@babel/cli": "7.18.10", "@babel/preset-env": "7.18.10", "benchmark": "2.1.4", "eslint": "8.22.0", "eslint-config-standard": "17.0.0", "eslint-plugin-import": "2.26.0", "eslint-plugin-n": "15.2.5", "eslint-plugin-promise": "6.0.0", "mocha": "10.0.0", "nyc": "15.1.0", "pad-right": "0.2.2"}, "gitHead": "2018b4dc1f9df910725c7ff7cd51b81da4c187f8", "bugs": {"url": "https://github.com/josde<PERSON>/typed-function/issues"}, "_id": "typed-function@4.0.0", "_nodeVersion": "16.13.2", "_npmVersion": "8.16.0", "dist": {"integrity": "sha512-cAfT9cmOlojXvjir2uMXQpjHpLAHzBRBeZn9CEhaHQJHMZCOG1yN8Mfdp3+VYpHUEoPk4NeJA2Rn/YMiuGUjaA==", "shasum": "998aafdcd1159c9b9dda5c4e743a9408ca296d69", "tarball": "https://registry.npmjs.org/typed-function/-/typed-function-4.0.0.tgz", "fileCount": 5, "unpackedSize": 210861, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFNkj18Gy6mCAMk/vY/ui0JemwPNZqeB5R+zbO4eVLlOAiAO9BQdPNb83WZ3L3/X2wKA2Hf3g+XSSbaGrZ1sTaSHJQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjA34EACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoB9g/+K20MdZidxj8dzh2MbuDfdD2LbwYY+7GnpH2vSJb6yvOA5yyw\r\nPj5v8bJ+9Hu/5u8EamFhEU+v+LPzQul3lJVsGZmcRNb+tADuF6n0sAAreLdK\r\nlaklxGTqSOifipkogjPUVxayhCbgcnRV5Zq3YyKrOrS8m01k1Vdeo6CPm4bU\r\nRtAFEAtr399A1TZa9e67UAsoH/YPGPwY9Fo+/2dzvxsT4mn1I07jW9BSKtqN\r\nnGz6dbgEKfceLce6KViGI9lAWEjf80KoS/rBEGKK6mBnvniusL461+i7zkiN\r\nvqwqjMLzyvYTWlOu8Vw8ghoOJIjFxoYS9xCXb0J9UP55UAXFHhIM2OpbHBH+\r\nMUWPxIfJy4WoAbS6TNYpO6x8Ppa+TPAh65C/fgLBsnp5luvszeW4EWVB98fT\r\nwx4KeVBRXj32H42mh+pmqRNXXUU4V5GtWtJXyMbRvWrVjz02QuDVx3gcvdv4\r\nYla5kLANcIqJNcMc9oCYo7xxo+VttC1WjeBtRkfXT+mXWliVnfPVzT8TzRvt\r\nXDEBi/i4fisKEO5mcdBRzQl/olJqJDmJ3V+rkQNFRf625kRIsV9zMKoJp/82\r\namslucjRWVB379E2761lSqEWsfAYRHmDweesahUc0F0O8lZM/i3hBX8wWLrQ\r\nm55hAGaXiUoXX+leG7JglrZbs2spO0i0Ggo=\r\n=6XA3\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typed-function_4.0.0_1661173252333_0.9540888333724442"}, "_hasShrinkwrap": false}, "4.1.0": {"name": "typed-function", "version": "4.1.0", "description": "Type checking for JavaScript functions", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/josde<PERSON>"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/gwhitney"}, {"name": "<PERSON>", "url": "https://github.com/luke-gumbley"}], "homepage": "https://github.com/josde<PERSON>/typed-function", "repository": {"type": "git", "url": "git+https://github.com/josdejong/typed-function.git"}, "keywords": ["typed", "function", "arguments", "compose", "types"], "type": "module", "main": "lib/umd/typed-function.js", "module": "lib/esm/typed-function.mjs", "browser": "lib/umd/typed-function.js", "scripts": {"test": "mocha test --recursive", "test:lib": "mocha test test-lib --recursive", "build": "npm-run-all build:**", "build:clean": "del-cli lib", "build:esm": "babel src --out-dir lib/esm --out-file-extension .mjs --source-maps --config-file ./babel.config.json", "build:umd": "rollup lib/esm/typed-function.mjs --format umd --name 'typed' --sourcemap --output.file lib/umd/typed-function.js && cpy tools/cjs/package.json lib/umd --flat", "build-and-test": "npm run lint && npm run build && npm run test:lib", "lint": "eslint --cache src/**/*.mjs test/**/*.mjs test-lib/**/*.mjs", "format": "npm run lint -- --fix", "coverage": "nyc _mocha -- test --recursive; echo \"\nCoverage report is available at ./coverage/lcov-report/index.html\"", "prepublishOnly": "npm run build-and-test"}, "engines": {"node": ">= 14"}, "devDependencies": {"@babel/cli": "7.18.10", "@babel/preset-env": "7.18.10", "benchmark": "2.1.4", "cpy-cli": "4.2.0", "del-cli": "5.0.0", "eslint": "8.22.0", "eslint-config-standard": "17.0.0", "eslint-plugin-import": "2.26.0", "eslint-plugin-n": "15.2.5", "eslint-plugin-promise": "6.0.0", "mocha": "10.0.0", "npm-run-all": "4.1.5", "nyc": "15.1.0", "pad-right": "0.2.2", "rollup": "2.78.1"}, "gitHead": "ef6cc5afd3b38d73f74b670f5e8edb3e71771be3", "bugs": {"url": "https://github.com/josde<PERSON>/typed-function/issues"}, "_id": "typed-function@4.1.0", "_nodeVersion": "16.13.2", "_npmVersion": "8.16.0", "dist": {"integrity": "sha512-DGwUl6cioBW5gw2L+6SMupGwH/kZOqivy17E4nsh1JI9fKF87orMmlQx3KISQPmg3sfnOUGlwVkroosvgddrlg==", "shasum": "da4bdd8a6d19a89e22732f75e4a410860aaf9712", "tarball": "https://registry.npmjs.org/typed-function/-/typed-function-4.1.0.tgz", "fileCount": 8, "unpackedSize": 415743, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFhJ79uVk6dqK3OJERqDAvfc8STHCp9gwCYHaqHyJYpnAiBv8n//F0BfG/yPVVJ/rkPxzmYYRT2wDxFeDVqDmaykPQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjBPdrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmosxA//cyf5UsbNlQzqxUhYPBGiEaAccesrH3TelfyFY5C0EmmIkAOG\r\n69+1SCXylD6bRKxhmt+llH7qlVsh6eFhZalD9TzDc4XaBmC/UU/aUpGz4XCl\r\nb6c00+NYQ3zmFIlo52uaDSDUULRJxOSI/gxqLtZPVtXDVcnhIHdI+Dv+tAmZ\r\nN7cCo3SS51JCS/sFqmCswJd1ig1hy8bTtlmJhHqmBTKa98zBlyDcgMqsM4LT\r\nOhGlO8srNBadwDxIgTp95I4iTJKTdL+JKrx/nIWbP4Rm+4YQLBgT/j+Pxulj\r\nkOlkdn6eJWfi1iM5qpf2frhXRZsT0sDw6AzTOMIigEZyRlTtY3BqjJ3tDnsw\r\nW83eGjxMc5deXH5SkeRT15wyfqs/qIcA5rrQNchNR9nG9G0tt+kACqkJcULq\r\nqs2U1o6sOw2SXKNCeEuHLNb9mB0ppvZbN9/qfzZc/bNHix2X8/geFXMpOoVB\r\n2Z1N4d7Gf3PnXZ+esmU+KTWbYJzHBeLi+uDJihXvPGVL0Ux0C6C9J+ipfWe8\r\nxXli/0ulafbvM+66sig/LjkB9KRwloSZS5fHd/TbPJ1hB6S9DlcCazNPMJE/\r\noS60qJuWmH3ZOFbTIQGuqYTcovPCm/+oj1P1YtDv+KsEflKpjGNNJfDpj2oY\r\nrI3evIrC0RPwV5S617nI5wHGesKhLag1oWU=\r\n=KFNn\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typed-function_4.1.0_1661269867583_0.5216297840607735"}, "_hasShrinkwrap": false}, "4.1.1": {"name": "typed-function", "version": "4.1.1", "description": "Type checking for JavaScript functions", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/josde<PERSON>"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/gwhitney"}, {"name": "<PERSON>", "url": "https://github.com/luke-gumbley"}], "homepage": "https://github.com/josde<PERSON>/typed-function", "repository": {"type": "git", "url": "git+https://github.com/josdejong/typed-function.git"}, "keywords": ["typed", "function", "arguments", "compose", "types"], "type": "module", "main": "lib/umd/typed-function.js", "module": "lib/esm/typed-function.mjs", "browser": "lib/umd/typed-function.js", "scripts": {"test": "mocha test --recursive", "test:lib": "mocha test test-lib --recursive", "build": "npm-run-all build:**", "build:clean": "del-cli lib", "build:esm": "babel src --out-dir lib/esm --out-file-extension .mjs --source-maps --config-file ./babel.config.json", "build:umd": "rollup lib/esm/typed-function.mjs --format umd --name 'typed' --sourcemap --output.file lib/umd/typed-function.js && cpy tools/cjs/package.json lib/umd --flat", "build-and-test": "npm run lint && npm run build && npm run test:lib", "lint": "eslint --cache src/**/*.mjs test/**/*.mjs test-lib/**/*.mjs", "format": "npm run lint -- --fix", "coverage": "c8 --reporter=lcov --reporter=text-summary mocha test --recursive && echo \"\nCoverage report is available at ./coverage/lcov-report/index.html\"", "prepublishOnly": "npm run build-and-test"}, "engines": {"node": ">= 14"}, "devDependencies": {"@babel/cli": "7.22.15", "@babel/preset-env": "7.22.15", "benchmark": "2.1.4", "c8": "8.0.1", "cpy-cli": "5.0.0", "del-cli": "5.1.0", "eslint": "8.49.0", "eslint-config-standard": "17.1.0", "eslint-plugin-import": "2.28.1", "eslint-plugin-n": "16.1.0", "eslint-plugin-promise": "6.1.1", "mocha": "10.2.0", "npm-run-all": "4.1.5", "pad-right": "0.2.2", "rollup": "3.29.1"}, "license": "MIT", "_id": "typed-function@4.1.1", "gitHead": "7cec654e781f69d09dea0b0e176c69b8494509ee", "bugs": {"url": "https://github.com/josde<PERSON>/typed-function/issues"}, "_nodeVersion": "20.5.0", "_npmVersion": "9.8.0", "dist": {"integrity": "sha512-Pq1DVubcvibmm8bYcMowjVnnMwPVMeh0DIdA8ad8NZY2sJgapANJmiigSUwlt+EgXxpfIv8MWrQXTIzkfYZLYQ==", "shasum": "38ce3cae31f4f513bcb263563fdad27b2afa73e8", "tarball": "https://registry.npmjs.org/typed-function/-/typed-function-4.1.1.tgz", "fileCount": 8, "unpackedSize": 388684, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCtlRSRXs/8oQpdWo4H//jbNhYh25mciU9ZC644VeHGtQIhANwMr/bfYzTgaSZ02UOEPEB8sfweUPIqAZtNGudoDn/g"}]}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typed-function_4.1.1_1694607831290_0.45001645742288265"}, "_hasShrinkwrap": false}, "4.2.0": {"name": "typed-function", "version": "4.2.0", "description": "Type checking for JavaScript functions", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/josde<PERSON>"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/gwhitney"}, {"name": "<PERSON>", "url": "https://github.com/luke-gumbley"}], "homepage": "https://github.com/josde<PERSON>/typed-function", "repository": {"type": "git", "url": "git+https://github.com/josdejong/typed-function.git"}, "keywords": ["typed", "function", "arguments", "compose", "types"], "type": "module", "main": "lib/umd/typed-function.js", "module": "lib/esm/typed-function.mjs", "browser": "lib/umd/typed-function.js", "scripts": {"test": "mocha test --recursive", "test:lib": "mocha test test-lib --recursive", "build": "npm-run-all build:**", "build:clean": "del-cli lib", "build:esm": "babel src --out-dir lib/esm --out-file-extension .mjs --source-maps --config-file ./babel.config.json", "build:umd": "rollup lib/esm/typed-function.mjs --format umd --name 'typed' --sourcemap --output.file lib/umd/typed-function.js && cpy tools/cjs/package.json lib/umd --flat", "build-and-test": "npm run lint && npm run build && npm run test:lib", "lint": "eslint --cache src/**/*.mjs test/**/*.mjs test-lib/**/*.mjs", "format": "npm run lint -- --fix", "coverage": "c8 --reporter=lcov --reporter=text-summary mocha test --recursive && echo \"\nCoverage report is available at ./coverage/lcov-report/index.html\"", "prepublishOnly": "npm run build-and-test"}, "engines": {"node": ">= 18"}, "devDependencies": {"@babel/cli": "7.24.6", "@babel/preset-env": "7.24.6", "benchmark": "2.1.4", "c8": "9.1.0", "cpy-cli": "5.0.0", "del-cli": "5.1.0", "eslint": "8.56.0", "eslint-config-standard": "17.1.0", "eslint-plugin-import": "2.29.1", "eslint-plugin-n": "16.6.2", "eslint-plugin-promise": "6.2.0", "mocha": "10.4.0", "npm-run-all": "4.1.5", "pad-right": "0.2.2", "rollup": "4.18.0"}, "license": "MIT", "_id": "typed-function@4.2.0", "gitHead": "479eb2ea89a594a4429ddd39b83ba5993c02c292", "bugs": {"url": "https://github.com/josde<PERSON>/typed-function/issues"}, "_nodeVersion": "20.12.1", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-LGKFj+G+wF8/FbF1sNZc0qksudW1xjJ7OSem5ArP3Q85tG3uSiLe5LSxwn3MBva8WqKIiLe+Y4vkEQhpdE4gKg==", "shasum": "a02188466bcb80a976bb6019307de6ca07648ed0", "tarball": "https://registry.npmjs.org/typed-function/-/typed-function-4.2.0.tgz", "fileCount": 8, "unpackedSize": 388300, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDsfhGrFtgN+PL1EOYY6Q9H1YlBc6hvEn6+E9IHNy+jbgIgFc+4671B7uissUvY/0lLWxjuOsIM+aQICoIKSXLUjVs="}]}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typed-function_4.2.0_1717574736329_0.6547237985584198"}, "_hasShrinkwrap": false}, "4.2.1": {"name": "typed-function", "version": "4.2.1", "description": "Type checking for JavaScript functions", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/josde<PERSON>"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/gwhitney"}, {"name": "<PERSON>", "url": "https://github.com/luke-gumbley"}], "homepage": "https://github.com/josde<PERSON>/typed-function", "repository": {"type": "git", "url": "git+https://github.com/josdejong/typed-function.git"}, "keywords": ["typed", "function", "arguments", "compose", "types"], "type": "module", "main": "lib/umd/typed-function.js", "module": "lib/esm/typed-function.mjs", "browser": "lib/umd/typed-function.js", "scripts": {"test": "mocha test --recursive", "test:lib": "mocha test test-lib --recursive", "build": "npm-run-all build:**", "build:clean": "del-cli lib", "build:esm": "babel src --out-dir lib/esm --out-file-extension .mjs --source-maps --config-file ./babel.config.json", "build:umd": "rollup lib/esm/typed-function.mjs --format umd --name 'typed' --sourcemap --output.file lib/umd/typed-function.js && cpy tools/cjs/package.json lib/umd --flat", "build-and-test": "npm run lint && npm run build && npm run test:lib", "lint": "eslint --cache src/**/*.mjs test/**/*.mjs test-lib/**/*.mjs", "format": "npm run lint -- --fix", "coverage": "c8 --reporter=lcov --reporter=text-summary mocha test --recursive && echo \"\nCoverage report is available at ./coverage/lcov-report/index.html\"", "prepublishOnly": "npm run build-and-test"}, "engines": {"node": ">= 18"}, "devDependencies": {"@babel/cli": "7.24.6", "@babel/preset-env": "7.24.6", "benchmark": "2.1.4", "c8": "9.1.0", "cpy-cli": "5.0.0", "del-cli": "5.1.0", "eslint": "8.56.0", "eslint-config-standard": "17.1.0", "eslint-plugin-import": "2.29.1", "eslint-plugin-n": "16.6.2", "eslint-plugin-promise": "6.2.0", "mocha": "10.4.0", "npm-run-all": "4.1.5", "pad-right": "0.2.2", "rollup": "4.18.0"}, "license": "MIT", "_id": "typed-function@4.2.1", "gitHead": "fc6529fc75899aa2b55c7216ad3bf6464257e9cb", "bugs": {"url": "https://github.com/josde<PERSON>/typed-function/issues"}, "_nodeVersion": "20.12.1", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-EGjWssW7Tsk4DGfE+5yluuljS1OGYWiI1J6e8puZz9nTMM51Oug8CD5Zo4gWMsOhq5BI+1bF+rWTm4Vbj3ivRA==", "shasum": "19aa51847aa2dea9ef5e7fb7641c060179a74426", "tarball": "https://registry.npmjs.org/typed-function/-/typed-function-4.2.1.tgz", "fileCount": 8, "unpackedSize": 388790, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDn8cPgxuq/ZEDEed4rqMLG7S9js/T3EneSer4IMyR0NwIhAPkaWQMC4ZVpc/wmyvwyoyfzd9FaNySxJVLKK+AdSn++"}]}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typed-function_4.2.1_1717577865875_0.8120418427061287"}, "_hasShrinkwrap": false}}, "readme": "# typed-function\n\n[![Version](https://img.shields.io/npm/v/typed-function.svg)](https://www.npmjs.com/package/typed-function)\n[![Downloads](https://img.shields.io/npm/dm/typed-function.svg)](https://www.npmjs.com/package/typed-function)\n[![Build Status](https://github.com/josdejong/typed-function/workflows/Node.js%20CI/badge.svg)](https://github.com/josdejong/typed-function/actions)\n\nMove type checking logic and type conversions outside of your function in a\nflexible, organized way. Automatically throw informative errors in case of\nwrong input arguments.\n\n\n## Features\n\ntyped-function has the following features:\n\n- Runtime type-checking of input arguments.\n- Automatic type conversion of arguments.\n- Compose typed functions with multiple signatures.\n- Supports union types, any type, and variable arguments.\n- Detailed error messaging.\n\nSupported environments: node.js, Chrome, Firefox, Safari, Opera, IE11+.\n\n\n## Why?\n\nIn JavaScript, functions can be called with any number and any type of arguments.\nWhen writing a function, the easiest way is to just assume that the function\nwill be called with the correct input. This leaves the function's behavior on\ninvalid input undefined. The function may throw some error, or worse,\nit may silently fail or return wrong results. Typical errors are\n*TypeError: undefined is not a function* or *TypeError: Cannot call method\n'request' of undefined*. These error messages are not very helpful. It can be\nhard to debug them, as they can be the result of a series of nested function\ncalls manipulating and propagating invalid or incomplete data.\n\nOften, JavaScript developers add some basic type checking where it is important,\nusing checks like `typeof fn === 'function'`, `date instanceof Date`, and\n`Array.isArray(arr)`. For functions supporting multiple signatures,\nthe type checking logic can grow quite a bit, and distract from the actual\nlogic of the function.\n\nFor functions dealing with a considerable amount of type checking and conversion\nlogic, or functions facing a public API, it can be very useful to use the\n`typed-function` module to handle the type-checking logic. This way:\n\n-   Users of the function get useful and consistent error messages when using\n    the function wrongly.\n-   The function cannot silently fail or silently give wrong results due to\n    invalid input.\n-   Correct type of input is assured inside the function. The function's code\n    becomes easier to understand as it only contains the actual function logic.\n    Lower level utility functions called by the type-checked function can\n    possibly be kept simpler as they don't need to do additional type checking.\n\nIt's important however not to *overuse* type checking:\n\n-   Locking down the type of input that a function accepts can unnecessarily\n    limit its flexibility. Keep functions as flexible and forgiving as possible,\n    follow the\n    [robustness principle](http://en.wikipedia.org/wiki/Robustness_principle)\n    here: \"be liberal in what you accept and conservative in what you send\"\n    (Postel's law).\n-   There is no need to apply type checking to *all* functions. It may be\n    enough to apply type checking to one tier of public facing functions.\n-   There is a performance penalty involved for all type checking, so applying\n    it everywhere can unnecessarily worsen the performance.\n\n\n## Load\n\nInstall via npm:\n\n    npm install typed-function\n\n\n## Usage\n\nHere are some usage examples. More examples are available in the\n[/examples](/examples) folder.\n\n```js\nimport typed from 'typed-function'\n\n// create a typed function\nvar fn1 = typed({\n  'number, string': function (a, b) {\n    return 'a is a number, b is a string';\n  }\n});\n\n// create a typed function with multiple types per argument (type union)\nvar fn2 = typed({\n  'string, number | boolean': function (a, b) {\n    return 'a is a string, b is a number or a boolean';\n  }\n});\n\n// create a typed function with any type argument\nvar fn3 = typed({\n  'string, any': function (a, b) {\n    return 'a is a string, b can be anything';\n  }\n});\n\n// create a typed function with multiple signatures\nvar fn4 = typed({\n  'number': function (a) {\n    return 'a is a number';\n  },\n  'number, boolean': function (a, b) {\n    return 'a is a number, b is a boolean';\n  },\n  'number, number': function (a, b) {\n    return 'a is a number, b is a number';\n  }\n});\n\n// create a typed function from a plain function with signature\nfunction fnPlain (a, b) {\n  return 'a is a number, b is a string';\n}\n\nfnPlain.signature = 'number, string';\nvar fn5 = typed(fnPlain);\n\n// use the functions\nconsole.log(fn1(2, 'foo'));      // outputs 'a is a number, b is a string'\nconsole.log(fn4(2));             // outputs 'a is a number'\n\n// calling the function with a non-supported type signature will throw an error\ntry {\n  fn2('hello', 'world');\n} catch (err) {\n  console.log(err.toString());\n  // outputs:  TypeError: Unexpected type of argument.\n  //           Expected: number or boolean, actual: string, index: 1.\n}\n```\n\n\n## Types\n\ntyped-function has the following built-in types:\n\n- `null`\n- `boolean`\n- `number`\n- `string`\n- `Function`\n- `Array`\n- `Date`\n- `RegExp`\n- `Object`\n\nThe following type expressions are supported:\n\n- Multiple arguments: `string, number, Function`\n- Union types: `number | string`\n- Variable arguments: `...number`\n- Any type: `any`\n\n### Dispatch\n\nWhen a typed function is called, an implementation with a matching signature\nis called, where conversions may be applied to actual arguments in order to\nfind a match.\n\nAmong all matching signatures, the one to execute is chosen by the following\npreferences, in order of priority:\n\n* one that does not have an `...any` parameter\n* one with the fewest `any` parameters\n* one that does not use conversions to match a rest parameter\n* one with the fewest conversions needed to match overall\n* one with no rest parameter\n* If there's a rest parameter, the one with the most non-rest parameters\n* The one with the largest number of preferred parameters\n* The one with the earliest preferred parameter\n\nWhen this process gets to the point of comparing individual parameters,\nthe preference between parameters is determined by the following, in\npriority order:\n\n* All specific types are preferred to the 'any' type\n* All directly matching types are preferred to conversions\n* Types earlier in the list of known types are preferred\n* Among conversions, ones earlier in the list are preferred\n\nIf none of these aspects produces a preference, then in those contexts in\nwhich Array.sort is stable, the order implementations were listed when\nthe typed-function was created breaks the tie. Otherwise the dispatch may\nselect any of the \"tied\" implementations.\n\n## API\n\n### Construction\n\n```\ntyped([name: string], ...Object.<string, function>|function)\n```\nA typed function can be constructed from an optional name and any number of\n(additional) arguments that supply the implementations for various\nsignatures. Each of these further arguments must be one of the following:\n\n-   An object with one or multiple signatures, i.e. a plain object\n    with string keys, each of which names a signature, and functions as\n    the values of those keys.\n\n-   A previously constructed typed function, in which case all of its\n    signatures and corresponding implementations are merged into the new\n    typed function.\n\n-   A plain function with a `signature` property whose value is a string\n    giving that function's signature.\n\nThe name, if specified, must be the first argument. If not specified, the new\ntyped-function's name is inherited from the arguments it is composed from,\nas long as any that have names agree with one another.\n\nIf the same signature is specified by the collection of arguments more than\nonce with different implementations, an error will be thrown.\n\n#### Properties and methods of a typed function `fn`\n\n-   `fn.name : string`\n\n    The name of the typed function, if one was assigned at creation; otherwise,\n    the value of this property is the empty string.\n\n-   `fn.signatures : Object.<string, function>`\n\n    The value of this property is a plain object. Its keys are the string\n    signatures on which this typed function `fn` is directly defined\n    (without conversions). The value for each key is the function `fn`\n    will call when its arguments match that signature. This property may\n    differ from the similar object used to create the typed function,\n    in that the originally provided signatures are parsed into a canonical,\n    more usable form: union types are split into their constituents where\n    possible, whitespace in the signature strings is removed, etc.\n\n-   `fn.toString() : string`\n\n    Returns human-readable code showing exactly what the function does.\n    Mostly for debugging purposes.\n\n### Methods of the typed package\n\n-   `typed.convert(value: *, type: string) : *`\n\n    Convert a value to another type. Only applicable when conversions have\n    been added with `typed.addConversion()` and/or `typed.addConversions()`\n    (see below in the method list).\n    Example:\n    \n    ```js\n    typed.addConversion({\n      from: 'number',\n      to: 'string',\n      convert: function (x) {\n        return +x;\n      }\n    });\n    \n    var str = typed.convert(2.3, 'string'); // '2.3' \n    ```\n\n-   `typed.create() : function`\n\n    Create a new, isolated instance of typed-function. Example:\n\n    ```js\n    import typed from 'typed-function.mjs';  // default instance\n    const typed2 = typed.create();           // a second instance\n    ```\n\n    This would allow you, for example, to have two different type hierarchies\n    for different purposes.\n\n-   `typed.resolve(fn: typed-function, argList: Array<any>): signature-object`\n\n    Find the specific signature and implementation that the typed function\n    `fn` will call if invoked on the argument list `argList`. Returns null if\n    there is no matching signature. The returned signature object has\n    properties `params`, `test`, `fn`, and `implementation`. The difference\n    between the last two properties is that `fn` is the original function\n    supplied at typed-function creation time, whereas `implementation` is\n    ready to be called on this specific argList, in that it will first\n    perform any necessary conversions and gather arguments up into \"rest\"\n    parameters as needed.\n\n    Thus, in the case that arguments `a0`,`a1`,`a2` (say) do match one of\n    the signatures of this typed function `fn`, then `fn(a0, a1, a2)`\n    (in a context in which `this` will be, say, `t`) does exactly the same\n    thing as\n\n    `typed.resolve(fn, [a0,a1,a2]).implementation.apply(t, [a0,a1,a2])`.\n\n    But `resolve` is useful if you want to interpose any other operation\n    (such as bookkeeping or additional custom error checking) between\n    signature selection and execution dispatch.\n\n-   `typed.findSignature(fn: typed-function, signature: string | Array, options: object) : signature-object`\n\n    Find the signature object (as returned by `typed.resolve` above), but\n    based on the specification of a signature (given either as a\n    comma-separated string of parameter types, or an Array of strings giving\n    the parameter types), rather than based on an example argument list.\n\n    The optional third argument, is a plain object giving options controlling\n    the search. Currently, the only implemented option is `exact`, which if\n    true (defaults to false), limits the search to exact type matches,\n    i.e. signatures for which no conversion functions need to be called in\n    order to apply the function.\n\n    Throws an error if the signature is not found.\n\n-   `typed.find(fn: typed-function, signature: string | Array, options: object) : function`\n\n    Convenience method that returns just the implementation from the\n    signature object produced by `typed.findSignature(fn, signature, options)`.\n    \n    For example:\n    \n    ```js\n    var fn = typed(...);\n    var f = typed.find(fn, ['number', 'string']);\n    var f = typed.find(fn, 'number, string', 'exact');\n    ```\n\n-   `typed.referTo(...string, callback: (resolvedFunctions: ...function) => function)`\n\n    Within the definition of a typed-function, resolve references to one or\n    multiple signatures of the typed-function itself. This looks like:\n\n    ```\n    typed.referTo(signature1, signature2, ..., function callback(fn1, fn2, ...) {\n      // ... use the resolved signatures fn1, fn2, ...\n    });\n    ```\n\n    Example usage:\n\n    ```js\n    const fn = typed({\n      'number': function (value) {\n        return 'Input was a number: ' + value;\n      },\n      'boolean': function (value) {\n        return 'Input was a boolean: ' + value;\n      },\n      'string': typed.referTo('number', 'boolean', (fnNumber, fnBoolean) => {\n        return function fnString(value) {\n          // here we use the signatures of the typed-function directly:\n          if (value === 'true') {\n            return fnBoolean(true);\n          }\n          if (value === 'false') {\n            return fnBoolean(false);\n          }\n          return fnNumber(parseFloat(value));\n        }\n      })\n    });\n    ```\n\n    See also `typed.referToSelf(callback)`.\n\n-   `typed.referToSelf(callback: (self) => function)`\n\n    Refer to the typed-function itself. This can be used for recursive calls.\n    Calls to self will incur the overhead of fully re-dispatching the\n    typed-function. If the signature that needs to be invoked is already known,\n    you can use `typed.referTo(...)` instead for better performance.\n\n    > In `typed-function@2` it was possible to use `this(...)` to reference the typed-function itself. In `typed-function@v3`, such usage is replaced with the `typed.referTo(...)` and `typed.referToSelf(...)` methods. Typed-functions are unbound in `typed-function@v3` and can be bound to another context if needed.\n\n-   `typed.isTypedFunction(entity: any): boolean`\n\n    Return true if the given entity appears to be a typed function\n    (created by any instance of typed-function), and false otherwise. It\n    tests for the presence of a particular property on the entity,\n    and so could be deceived by another object with the same property, although\n    the property is chosen so that's unlikely to happen unintentionally.\n\n-   `typed.addType(type: {name: string, test: function, [, beforeObjectTest=true]): void`\n\n    Add a new type. A type object contains a name and a test function.\n    The order of the types determines in which order function arguments are \n    type-checked, so for performance it's important to put the most used types \n    first. Also, if one type is contained in another, it should likely precede\n    it in the type order so that it won't be masked in type testing.\n    \n    Example:\n    \n    ```js\n    function Person(...) {\n      ...\n    }\n    \n    Person.prototype.isPerson = true;\n\n    typed.addType({\n      name: 'Person',\n      test: function (x) {\n        return x && x.isPerson === true;\n      }\n    });\n    ```\n\n    By default, the new type will be inserted before the `Object` test\n    because the `Object` test also matches arrays and classes and hence\n    `typed-function` would never reach the new type. When `beforeObjectTest`\n    is `false`, the new type will be added at the end of all tests.\n\n-   `typed.addTypes(types: TypeDef[] [, before = 'any']): void`\n\n    Adds an list of new types. Each entry of the `types` array is an object\n    like the `type` argument to `typed.addType`. The optional `before` argument\n    is similar to `typed.addType` as well, except it should be the name of an\n    arbitrary type that has already been added (rather than just a boolean flag)\n\n-   `typed.clear(): void`\n\n    Removes all types and conversions from the typed instance. Note that any\n    typed-functions created before a call to `clear` will still operate, but\n    they may prouce unintelligible messages in case of type mismatch errors.\n\n-   `typed.addConversion(conversion: {from: string, to: string, convert: function}, options?: { override: boolean }) : void`\n\n    Add a new conversion.\n    \n    ```js\n    typed.addConversion({\n      from: 'boolean',\n      to: 'number',\n      convert: function (x) {\n        return +x;\n    });\n    ```\n\n    Note that any typed functions created before this conversion is added will\n    not have their arguments undergo this new conversion automatically, so it is\n    best to add all of your desired automatic conversions before defining any\n    typed functions.\n\n-   `typed.addConversions(conversions: ConversionDef[], options?: { override: boolean }): void`\n\n    Convenience method that adds a list of conversions. Each element in the\n    `conversions` array should be an object like the `conversion` argument of\n    `typed.addConversion`.\n\n-   `typed.removeConversion(conversion: ConversionDef): void`\n\n    Removes a single existing conversion. An error is thrown if there is no\n    conversion from and to the given types with a strictly equal convert\n    function as supplied in this call.\n\n-   `typed.clearConversions(): void`\n\n    Removes all conversions from the typed instance (leaving the types alone).\n\n-   `typed.createError(name: string, args: Array.<any>, signatures: Array.<Signature>): TypeError`\n\n    Generates a custom error object reporting the problem with calling\n    the typed function of the given `name` with the given `signatures` on the\n    actual arguments `args`. Note the error object has an extra property `data`\n    giving the details of the problem. This method is primarily useful in\n    writing your own handler for a type mismatch (see the `typed.onMismatch`\n    property below), in case you have tried to recover but end up deciding\n    you want to throw the error that the default handler would have.\n\n### Properties\n\n-   `typed.onMismatch: function`\n\n    The handler called when a typed-function call fails to match with any\n    of its signatures. The handler is called with three arguments: the name\n    of the typed function being called, the actual argument list, and an array\n    of the signatures for the typed function being called. (Each signature is\n    an object with property 'signature' giving the actual signature and\\\n    property 'fn' giving the raw function for that signature.) The default\n    value of `onMismatch` is `typed.throwMismatchError`.\n\n    This can be useful if you have a collection of functions and have common\n    behavior for any invalid call. For example, you might just want to log\n    the problem and continue:\n\n    ```\n    const myErrorLog = [];\n    typed.onMismatch = (name, args, signatures) => {\n      myErrorLog.push(`Invalid call of ${name} with ${args.length} arguments.`);\n      return null;\n    };\n    typed.sqrt(9); // assuming definition as above, will return 3\n    typed.sqrt([]); // no error will be thrown; will return null.\n    console.log(`There have been ${myErrorLog.length} invalid calls.`)\n    ```\n\n    Note that there is only one `onMismatch` handler at a time; assigning a\n    new value discards the previous handler. To restore the default behavior,\n    just assign `typed.onMismatch = typed.throwMismatchError`.\n\n    Finally note that this handler fires whenever _any_ typed function call\n    does not match any of its signatures. You can in effect define such a\n    \"handler\" for a _single_ typed function by simply specifying an\n    implementation for the `...` signature:\n\n    ```\n    const lenOrNothing = typed({\n      string: s => s.length,\n      '...': () => 0\n    });\n    console.log(lenOrNothing('Hello, world!')) // Output: 13\n    console.log(lenOrNothing(57, 'varieties')) // Output: 0\n    ```\n\n-   `typed.warnAgainstDeprecatedThis: boolean`\n\n    Since `typed-function` v3, self-referencing a typed function using\n    `this(...)` or `this.signatures` has been deprecated and replaced with\n    the functions `typed.referTo` and `typed.referToSelf`. By default, all\n    function bodies will be scanned against this deprecated usage pattern and\n    an error will be thrown when encountered. To disable this validation step,\n    change this option to `false`.\n\n### Recursion\n\nThe `this` keyword can be used to self-reference the typed-function:\n\n```js\nvar sqrt = typed({\n  'number': function (value) {\n    return Math.sqrt(value);\n  },\n  'string': function (value) {\n    // on the following line we self reference the typed-function using \"this\"\n    return this(parseInt(value, 10));\n  }\n});\n\n// use the typed function\nconsole.log(sqrt('9')); // output: 3\n```\n\n\n## Roadmap\n\n### Version 4\n\n- Extend function signatures:\n  - Optional arguments like `'[number], array'` or like `number=, array`\n  - Nullable arguments like `'?Object'`\n- Allow conversions to fail (for example string to number is not always\n  possible). Call this `fallible` or `optional`?\n\n### Version 5\n\n- Extend function signatures:\n  - Constants like `'\"linear\" | \"cubic\"'`, `'0..10'`, etc.\n  - Object definitions like `'{name: string, age: number}'`\n  - Object definitions like `'Object.<string, Person>'`\n  - Array definitions like `'Array.<Person>'`\n- Improve performance of both generating a typed function as well as\n  the performance and memory footprint of a typed function.\n\n\n## Test\n\nTo test the library, run:\n\n    npm test\n\n\n## Code style and linting\n\nThe library is using the [standardjs](https://standardjs.com/) coding style.\n\nTo test the code style, run:\n\n    npm run lint\n\nTo automatically fix most of the styling issues, run:\n\n    npm run format\n\n\n## Publish\n\n1. Describe the changes in `HISTORY.md`\n2. Increase the version number in `package.json`\n3. Test and build:\n    ```\n    npm install\n    npm run build-and-test\n    ```\n4. Verify whether the generated output works correctly by opening\n   `./test/browserEsmBuild.html` in your browser. \n5. Commit the changes\n6. Merge `develop` into `master`, and push `master`\n7. Create a git tag, and push this\n8. publish the library:\n    ```\n    npm publish\n    ```\n", "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2024-06-05T08:57:46.186Z", "created": "2014-11-05T13:44:09.393Z", "0.3.1": "2014-11-05T13:44:09.393Z", "0.4.0": "2014-12-17T15:53:06.904Z", "0.5.0": "2015-01-07T13:31:17.293Z", "0.6.0": "2015-01-16T08:17:30.189Z", "0.6.1": "2015-02-07T12:27:29.158Z", "0.6.2": "2015-02-26T20:48:10.688Z", "0.6.3": "2015-03-08T13:59:13.959Z", "0.7.0": "2015-04-17T18:53:05.750Z", "0.8.0": "2015-05-09T12:47:04.068Z", "0.8.1": "2015-05-09T14:32:39.315Z", "0.8.2": "2015-05-09T19:05:10.022Z", "0.8.3": "2015-05-16T09:14:46.653Z", "0.9.0": "2015-05-17T18:39:24.965Z", "0.10.0": "2015-07-26T17:37:24.127Z", "0.10.1": "2015-07-27T20:24:00.836Z", "0.10.2": "2015-10-04T11:35:29.830Z", "0.10.3": "2015-10-07T19:33:12.671Z", "0.10.4": "2016-04-09T09:49:01.335Z", "0.10.5": "2016-11-18T11:29:26.881Z", "0.10.6": "2017-11-18T19:17:15.048Z", "0.10.7": "2018-01-24T12:25:09.379Z", "1.0.0": "2018-02-20T19:50:38.275Z", "1.0.1": "2018-02-21T10:15:22.286Z", "1.0.2": "2018-03-17T19:16:19.972Z", "1.0.3": "2018-03-17T19:29:22.652Z", "1.0.4": "2018-07-04T10:21:40.948Z", "1.1.0": "2018-07-28T19:17:19.445Z", "1.1.1": "2019-08-22T14:19:04.539Z", "2.0.0": "2020-07-03T06:21:35.613Z", "2.1.0": "2022-03-11T09:57:45.149Z", "3.0.0": "2022-05-12T08:55:11.463Z", "3.0.1": "2022-08-16T14:29:42.438Z", "4.0.0": "2022-08-22T13:00:52.581Z", "4.1.0": "2022-08-23T15:51:07.850Z", "4.1.1": "2023-09-13T12:23:51.467Z", "4.2.0": "2024-06-05T08:05:36.532Z", "4.2.1": "2024-06-05T08:57:46.021Z"}, "homepage": "https://github.com/josde<PERSON>/typed-function", "keywords": ["typed", "function", "arguments", "compose", "types"], "repository": {"type": "git", "url": "git+https://github.com/josdejong/typed-function.git"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/gwhitney"}, {"name": "<PERSON>", "url": "https://github.com/luke-gumbley"}], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/josde<PERSON>"}, "bugs": {"url": "https://github.com/josde<PERSON>/typed-function/issues"}, "readmeFilename": "README.md", "license": "MIT"}