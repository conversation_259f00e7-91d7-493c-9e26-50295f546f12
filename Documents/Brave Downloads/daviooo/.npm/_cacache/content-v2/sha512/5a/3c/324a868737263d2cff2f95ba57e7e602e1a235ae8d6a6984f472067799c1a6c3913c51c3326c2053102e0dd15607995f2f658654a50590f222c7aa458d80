{"_id": "event-target-shim", "_rev": "36-51eaf576021592ff33984ee7d7f2c4d7", "name": "event-target-shim", "description": "An implementation of WHATWG EventTarget interface.", "dist-tags": {"latest": "6.0.2"}, "versions": {"0.0.1": {"name": "event-target-shim", "version": "0.0.1", "description": "A polyfill for W3C EventTarget Constructor.", "main": "lib/index.js", "directories": {"test": "test"}, "scripts": {"build": "babel src --out-dir lib --source-maps-inline", "test": "karma start karma.conf.js --single-run", "testing": "karma start karma.conf.js --auto-watch"}, "repository": {"type": "git", "url": "https://github.com/mysticatea/event-target-shim.git"}, "keywords": ["w3c", "eventtarget", "event", "events", "shim", "polyfill"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/event-target-shim/issues"}, "homepage": "https://github.com/mysticatea/event-target-shim", "devDependencies": {"babel": "^4.4.3", "babelify": "^5.0.3", "chai": "^2.0.0", "chai-spies": "^0.5.1", "karma": "^0.12.31", "karma-browserify": "^3.0.2", "karma-chrome-launcher": "^0.1.7", "karma-firefox-launcher": "^0.1.4", "karma-ie-launcher": "^0.1.5", "karma-mocha": "^0.1.10", "mocha": "^2.1.0"}, "_id": "event-target-shim@0.0.1", "dist": {"shasum": "5bc7644d86c0f627eec81b8eefad633b664c2ddc", "tarball": "https://registry.npmjs.org/event-target-shim/-/event-target-shim-0.0.1.tgz", "integrity": "sha512-qqf005ftq5R6NBC22cqLuE0QEK+rVgG5amH7669UJ+sDHubG7sWq5FxEp/y+VROGPimuMt3EBedYX6UmYzMEWA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDlBh60MwLq8MAC15hEhkjcYsVO1W+mzPccZtx0eBAzNgIhAJSqV8EQebTH4xCgS4k/gxqxHrF7KwKBtmEWwFF4VqP2"}]}, "_from": ".", "_npmVersion": "1.4.6", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}]}, "0.1.0": {"name": "event-target-shim", "version": "0.1.0", "description": "A polyfill for W3C EventTarget Constructor.", "main": "lib/EventTarget.js", "directories": {"test": "test"}, "scripts": {"build": "babel src --out-dir lib --source-maps-inline", "test": "karma start karma.conf.js --single-run", "testing": "karma start karma.conf.js --auto-watch"}, "repository": {"type": "git", "url": "https://github.com/mysticatea/event-target-shim.git"}, "keywords": ["w3c", "eventtarget", "event", "events", "shim", "polyfill"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/event-target-shim/issues"}, "homepage": "https://github.com/mysticatea/event-target-shim", "devDependencies": {"babel": "^4.4.3", "babelify": "^5.0.3", "chai": "^2.0.0", "chai-spies": "^0.5.1", "karma": "^0.12.31", "karma-browserify": "^3.0.2", "karma-chrome-launcher": "^0.1.7", "karma-firefox-launcher": "^0.1.4", "karma-ie-launcher": "^0.1.5", "karma-mocha": "^0.1.10", "mocha": "^2.1.0"}, "_id": "event-target-shim@0.1.0", "dist": {"shasum": "1cb78228c282646d3c5b37f8c4b00ba7619a6feb", "tarball": "https://registry.npmjs.org/event-target-shim/-/event-target-shim-0.1.0.tgz", "integrity": "sha512-wYCmH4/d/k9fDcA19r/8/rCrULuD+EZsrKA6qLZTW6QTBE/D68Bj5apdUkzpRfZitK4oPX9mtlVSsr1oF1+0Tg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDCEF/5sN79tYPwcqEMjEhV6lL4Rj5vnw4aYUwNjBKOnwIhALyzRGPFW+abkaWIZVEdHcNRe06wCxAiOtOiEGMEeATW"}]}, "_from": ".", "_npmVersion": "1.4.6", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}]}, "0.1.1": {"name": "event-target-shim", "version": "0.1.1", "description": "A polyfill for W3C EventTarget Constructor.", "main": "lib/EventTarget.js", "directories": {"test": "test"}, "scripts": {"build": "babel src --out-dir lib --source-maps-inline", "test": "karma start karma.conf.js --single-run", "testing": "karma start karma.conf.js --auto-watch"}, "repository": {"type": "git", "url": "https://github.com/mysticatea/event-target-shim.git"}, "keywords": ["w3c", "eventtarget", "event", "events", "shim", "polyfill"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/event-target-shim/issues"}, "homepage": "https://github.com/mysticatea/event-target-shim", "devDependencies": {"babel": "^4.4.3", "babelify": "^5.0.3", "chai": "^2.0.0", "chai-spies": "^0.5.1", "karma": "^0.12.31", "karma-browserify": "^3.0.2", "karma-chrome-launcher": "^0.1.7", "karma-firefox-launcher": "^0.1.4", "karma-ie-launcher": "^0.1.5", "karma-mocha": "^0.1.10", "mocha": "^2.1.0"}, "_id": "event-target-shim@0.1.1", "dist": {"shasum": "f267520c93392d8475608decd1e70a7dc85928f1", "tarball": "https://registry.npmjs.org/event-target-shim/-/event-target-shim-0.1.1.tgz", "integrity": "sha512-Cnp32f71BhMjzmXpyG2JMllCX9mrs67BKoujJRIRO7R6qbBohv9k5ArNVTZRlwHDEuYHWrZVUxRuh4P5ZepZYQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCGNOv/iLG76YXKJW8ygWAujJLFxytzxjNnSRQdI7FErwIgbO56Jaag4b+Qc2z3v5emvytCoGehJR7ERr0PHpq0tbQ="}]}, "_from": ".", "_npmVersion": "1.4.6", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}]}, "1.0.0": {"name": "event-target-shim", "version": "1.0.0", "description": "A polyfill for W3C EventTarget Constructor.", "main": "lib/EventTarget.js", "files": ["lib"], "scripts": {"build": "babel src --out-dir lib", "test": "npm-run-all test:lint build test:karma", "test:lint": "eslint src test", "test:karma": "karma start karma.conf.js --single-run", "testing": "npm-run-all --parallel testing:build testing:karma", "testing:build": "npm run build -- --watch", "testing:karma": "karma start karma.conf.js --auto-watch"}, "devDependencies": {"babel": "^4.7.16", "babelify": "^5.0.4", "eslint": "^0.17.1", "espowerify": "^0.10.0", "karma": "^0.12.31", "karma-browserify": "^4.0.0", "karma-chrome-launcher": "^0.1.7", "karma-firefox-launcher": "^0.1.4", "karma-ie-launcher": "^0.1.5", "karma-mocha": "^0.1.10", "mocha": "^2.2.1", "npm-run-all": "^1.0.2", "power-assert": "^0.10.2", "spy": "^0.1.3"}, "repository": {"type": "git", "url": "https://github.com/mysticatea/event-target-shim.git"}, "keywords": ["w3c", "eventtarget", "event", "events", "shim", "polyfill"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/event-target-shim/issues"}, "homepage": "https://github.com/mysticatea/event-target-shim", "gitHead": "58700481bd92e6bd0dbd46193444f48cfb783a49", "_id": "event-target-shim@1.0.0", "_shasum": "308dc372d1cb3c3acb02165aba3a2c75ca9e7b3b", "_from": ".", "_npmVersion": "2.7.1", "_nodeVersion": "0.12.1", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}], "dist": {"shasum": "308dc372d1cb3c3acb02165aba3a2c75ca9e7b3b", "tarball": "https://registry.npmjs.org/event-target-shim/-/event-target-shim-1.0.0.tgz", "integrity": "sha512-AhZP+7zUrfAVgv/FYWtIAJYr5fM8a41BNjdMRrGfvRYRzSMX6GOQ+e/CB3lWTO2nnv7nxCTV4MiV46xMyUwygw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDdmsCBfJ52qiE6v2KvCZ6M10NiKHW4WZob9ReokwgKTAIhAIRMonZ1pYWeIzgGoheOHye7tESQFUKi9NugrPPSWBPN"}]}, "directories": {}}, "1.0.1": {"name": "event-target-shim", "version": "1.0.1", "description": "A polyfill for W3C EventTarget Constructor.", "main": "lib/EventTarget.js", "files": ["dist", "lib"], "scripts": {"clean": "<PERSON><PERSON><PERSON> lib dist", "lint": "eslint src", "build": "npm-run-all clean lint build:lib build:dist build:dist-min", "build:lib": "babel src --out-dir lib", "build:dist": "mkdirp dist && browserify lib/EventTarget.js --standalone event-target-shim > dist/event-target-shim.js", "build:dist-min": "uglifyjs dist/event-target-shim.js --compress --mangle > dist/event-target-shim.min.js", "test": "npm-run-all clean lint build:lib test:karma", "test:karma": "karma start karma.conf.js --single-run", "testing": "npm run clean && npm-run-all --parallel testing:build testing:karma", "testing:build": "npm run build:lib -- --watch", "testing:karma": "karma start karma.conf.js --auto-watch"}, "devDependencies": {"babel": "^4.7.16", "babelify": "^5.0.4", "browserify": "^9.0.3", "eslint": "^0.17.1", "espowerify": "^0.10.0", "karma": "^0.12.31", "karma-browserify": "^4.0.0", "karma-chrome-launcher": "^0.1.7", "karma-firefox-launcher": "^0.1.4", "karma-ie-launcher": "^0.1.5", "karma-mocha": "^0.1.10", "mkdirp": "^0.5.0", "mocha": "^2.2.1", "npm-run-all": "^1.0.2", "power-assert": "^0.10.2", "rimraf": "^2.3.2", "spy": "^0.1.3", "uglify-js": "^2.4.17"}, "repository": {"type": "git", "url": "https://github.com/mysticatea/event-target-shim.git"}, "keywords": ["w3c", "eventtarget", "event", "events", "shim", "polyfill"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/event-target-shim/issues"}, "homepage": "https://github.com/mysticatea/event-target-shim", "gitHead": "ed4cc6787ca30534661d655ae793d2a17e730978", "_id": "event-target-shim@1.0.1", "_shasum": "1c1d462e0a561111949054120cc0b309c23882b0", "_from": ".", "_npmVersion": "2.7.1", "_nodeVersion": "0.12.1", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}], "dist": {"shasum": "1c1d462e0a561111949054120cc0b309c23882b0", "tarball": "https://registry.npmjs.org/event-target-shim/-/event-target-shim-1.0.1.tgz", "integrity": "sha512-xEnFCVMzqXCIP2l8aliPI09aDZzlwMaOI+61ZrcHetUvBapS0DpHTVjj2HqhYZjIer1JMgqpHRvCyYTn2TYhAg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDsdk302RRIjjZJvKFBX9vF7XH0fhp4lNkYg1xHomyvOQIhAM9PRB/jDKI3MOdc+M1IlU9xMCbAiAK+ErkRjPS3A/9M"}]}, "directories": {}}, "1.0.2": {"name": "event-target-shim", "version": "1.0.2", "description": "A polyfill for W3C EventTarget Constructor.", "main": "lib/EventTarget.js", "files": ["dist", "lib"], "scripts": {"clean": "<PERSON><PERSON><PERSON> lib dist", "lint": "eslint src", "build": "npm-run-all clean lint build:lib build:dist build:dist-min", "build:lib": "babel src --out-dir lib", "build:dist": "mkdirp dist && browserify lib/EventTarget.js --standalone event-target-shim > dist/event-target-shim.js", "build:dist-min": "uglifyjs dist/event-target-shim.js --compress --mangle > dist/event-target-shim.min.js", "test": "npm-run-all clean lint build:lib test:karma", "test:karma": "karma start karma.conf.js --single-run", "testing": "npm-run-all clean --parallel testing:build testing:karma", "testing:build": "npm run build:lib -- --watch", "testing:karma": "karma start karma.conf.js --auto-watch --reporters growl,progress"}, "devDependencies": {"babel": "^4.7.16", "babelify": "^5.0.4", "browserify": "^9.0.3", "eslint": "^0.18.0", "espowerify": "^0.10.0", "karma": "^0.12.31", "karma-browserify": "^4.1.0", "karma-chrome-launcher": "^0.1.7", "karma-firefox-launcher": "^0.1.4", "karma-growl-reporter": "^0.1.1", "karma-ie-launcher": "^0.1.5", "karma-mocha": "^0.1.10", "mkdirp": "^0.5.0", "mocha": "^2.2.1", "npm-run-all": "^1.1.1", "power-assert": "^0.10.2", "rimraf": "^2.3.2", "spy": "^0.1.3", "uglify-js": "^2.4.19"}, "repository": {"type": "git", "url": "https://github.com/mysticatea/event-target-shim.git"}, "keywords": ["w3c", "eventtarget", "event", "events", "shim", "polyfill"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/event-target-shim/issues"}, "homepage": "https://github.com/mysticatea/event-target-shim", "gitHead": "16d375f49d5a4d9bc5b44260341253e40293afb8", "_id": "event-target-shim@1.0.2", "_shasum": "51385ba9442262f695fd51139fb3a5a9964203bd", "_from": ".", "_npmVersion": "2.7.1", "_nodeVersion": "0.12.1", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}], "dist": {"shasum": "51385ba9442262f695fd51139fb3a5a9964203bd", "tarball": "https://registry.npmjs.org/event-target-shim/-/event-target-shim-1.0.2.tgz", "integrity": "sha512-tiq3Tc38cbXdCIsY/l+zWhGM8PoUPKuDd1nwvSWu6JURxRMqSaqVKWKxdu4yEW5qPfjDtxrm7dw/IcNZYBQ71Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGl3DILe0XcFFUMwv1/uLaJq32HxrT3kuizG6j4ikxWVAiBGwJ+7OSYfoBTeo5oJsXZ9XDBWU31tC2nq1D8MXnczuw=="}]}, "directories": {}}, "1.0.3": {"name": "event-target-shim", "version": "1.0.3", "description": "A polyfill for W3C EventTarget Constructor.", "main": "lib/EventTarget.js", "files": ["dist", "lib"], "scripts": {"clean": "<PERSON><PERSON><PERSON> lib dist", "lint": "eslint src", "build": "npm-run-all clean lint build:lib build:dist build:dist-min", "build:lib": "babel src --out-dir lib", "build:dist": "mkdirp dist && browserify lib/EventTarget.js --standalone event-target-shim > dist/event-target-shim.js", "build:dist-min": "uglifyjs dist/event-target-shim.js --compress --mangle > dist/event-target-shim.min.js", "test": "npm-run-all clean lint build:lib test:karma", "test:karma": "karma start karma.conf.js --single-run", "testing": "npm-run-all clean --parallel testing:build testing:karma", "testing:build": "npm run build:lib -- --watch", "testing:karma": "karma start karma.conf.js --auto-watch --reporters growl,progress"}, "devDependencies": {"babel": "^5.0.8", "babelify": "^6.0.1", "browserify": "^9.0.7", "eslint": "^0.18.0", "espowerify": "^0.10.0", "karma": "^0.12.31", "karma-browserify": "^4.1.2", "karma-chrome-launcher": "^0.1.7", "karma-firefox-launcher": "^0.1.4", "karma-growl-reporter": "^0.1.1", "karma-ie-launcher": "^0.1.5", "karma-mocha": "^0.1.10", "mkdirp": "^0.5.0", "mocha": "^2.2.1", "npm-run-all": "^1.1.2", "power-assert": "^0.10.2", "rimraf": "^2.3.2", "spy": "^0.1.3", "uglify-js": "^2.4.19"}, "repository": {"type": "git", "url": "https://github.com/mysticatea/event-target-shim.git"}, "keywords": ["w3c", "eventtarget", "event", "events", "shim", "polyfill"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/event-target-shim/issues"}, "homepage": "https://github.com/mysticatea/event-target-shim", "gitHead": "b4ed2318429fe19593a5b2415c435962d25b864e", "_id": "event-target-shim@1.0.3", "_shasum": "9192aa2f9ab311c663d033645d4b839213d9ebb6", "_from": ".", "_npmVersion": "2.7.1", "_nodeVersion": "0.12.1", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}], "dist": {"shasum": "9192aa2f9ab311c663d033645d4b839213d9ebb6", "tarball": "https://registry.npmjs.org/event-target-shim/-/event-target-shim-1.0.3.tgz", "integrity": "sha512-yxcVnkpDSuuoQCrRu+pB5eOkYY4nrx3emnPBqGdvJLpZ1hrOK7mBmb7aKL35yUDFdeGWvz+7vgbvnTUVKDH7jQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDhU7fMrysz41v541FinPEX0kru8CpT1aJcymuz/Nd55gIgFI3VkraVjPT9dgtRyLfNWRnvbCsg/PhTqBniNxDGu3w="}]}, "directories": {}}, "1.0.4": {"name": "event-target-shim", "version": "1.0.4", "description": "A polyfill for W3C EventTarget Constructor.", "main": "lib/EventTarget.js", "files": ["dist", "lib"], "scripts": {"clean": "<PERSON><PERSON><PERSON> lib dist", "lint": "eslint src", "build": "npm-run-all clean lint build:lib build:dist build:dist-min", "build:lib": "babel src --out-dir lib", "build:dist": "mkdirp dist && browserify lib/EventTarget.js --standalone event-target-shim > dist/event-target-shim.js", "build:dist-min": "uglifyjs dist/event-target-shim.js --compress --mangle > dist/event-target-shim.min.js", "test": "npm-run-all clean lint build:lib test:karma", "test:karma": "karma start karma.conf.js --single-run", "testing": "npm-run-all clean --parallel testing:build testing:karma", "testing:build": "npm run build:lib -- --watch", "testing:karma": "karma start karma.conf.js --auto-watch --reporters growl,progress"}, "devDependencies": {"babel": "^5.1.10", "babel-core": "^5.1.10", "babel-plugin-espower": "^0.1.0", "babelify": "^6.0.2", "browserify": "^9.0.8", "eslint": "^0.19.0", "karma": "^0.12.31", "karma-browserify": "^4.1.2", "karma-chrome-launcher": "^0.1.8", "karma-firefox-launcher": "^0.1.4", "karma-growl-reporter": "^0.1.1", "karma-ie-launcher": "^0.1.5", "karma-mocha": "^0.1.10", "mkdirp": "^0.5.0", "mocha": "^2.2.4", "npm-run-all": "^1.2.3", "power-assert": "^0.11.0", "rimraf": "^2.3.2", "spy": "^0.1.3", "uglify-js": "^2.4.20"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/event-target-shim.git"}, "keywords": ["w3c", "eventtarget", "event", "events", "shim", "polyfill"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/event-target-shim/issues"}, "homepage": "https://github.com/mysticatea/event-target-shim", "gitHead": "78565e8422f4c1da92258a2eab5feb064d057e8e", "_id": "event-target-shim@1.0.4", "_shasum": "7860059994cff259ccc682f593336468bd440517", "_from": ".", "_npmVersion": "2.8.3", "_nodeVersion": "0.12.2", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}], "dist": {"shasum": "7860059994cff259ccc682f593336468bd440517", "tarball": "https://registry.npmjs.org/event-target-shim/-/event-target-shim-1.0.4.tgz", "integrity": "sha512-NOnsBpGogbIuS1b+kJbz9lZZt6l4VJyXfNjqAvxtB7ZrQfxjKxN2/arvjnu2kn2E3cgXL3owG8wVQHVmLTv7hQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC3EoL9/XUiCmTieJcuZtqLIR7Xmb7NLhV2hEukEuZVHwIhAMmukhXGWDNXE3BSx1cQfZ/TSWn2L/HA+9m5ZUO6R6Iq"}]}, "directories": {}}, "1.0.5": {"name": "event-target-shim", "version": "1.0.5", "description": "A polyfill for W3C EventTarget Constructor.", "main": "lib/EventTarget.js", "files": ["dist", "lib"], "scripts": {"clean": "<PERSON><PERSON><PERSON> lib dist", "lint": "eslint src", "build": "npm-run-all clean lint build:*", "build:lib": "babel src --out-dir lib", "build:dist": "mkdirp dist && browserify lib/EventTarget.js --standalone event-target-shim > dist/event-target-shim.js", "build:dist-min": "uglifyjs dist/event-target-shim.js --compress --mangle > dist/event-target-shim.min.js", "test": "npm run lint && karma start karma.conf.js --single-run", "testing": "karma start karma.conf.js --auto-watch --reporters growl,progress"}, "devDependencies": {"babel": "^5.4.7", "babel-plugin-espower": "^1.0.0", "babelify": "^6.1.2", "browserify": "^10.2.1", "eslint": "^0.21.2", "karma": "^0.12.33", "karma-browserify": "^4.2.1", "karma-chrome-launcher": "^0.1.12", "karma-firefox-launcher": "^0.1.6", "karma-growl-reporter": "^0.1.1", "karma-ie-launcher": "^0.1.5", "karma-mocha": "^0.1.10", "mkdirp": "^0.5.1", "mocha": "^2.2.5", "npm-run-all": "^1.2.5", "power-assert": "^0.11.0", "rimraf": "^2.3.4", "spy": "^0.1.3", "uglify-js": "^2.4.23"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/event-target-shim.git"}, "keywords": ["w3c", "eventtarget", "event", "events", "shim", "polyfill"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/event-target-shim/issues"}, "homepage": "https://github.com/mysticatea/event-target-shim", "gitHead": "151feae20b23fbcaaf938a696b7c0b3f29c051e5", "_id": "event-target-shim@1.0.5", "_shasum": "854de637f9faacc4f86cf65fd21c35c2a9f4ce8a", "_from": ".", "_npmVersion": "2.8.4", "_nodeVersion": "0.12.2", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}], "dist": {"shasum": "854de637f9faacc4f86cf65fd21c35c2a9f4ce8a", "tarball": "https://registry.npmjs.org/event-target-shim/-/event-target-shim-1.0.5.tgz", "integrity": "sha512-qlLYzBDd6aXpXx40ogcPVGQmS5nB19ncxjwhfWrBt8ehw4x0ZmS7mL3j3yfYdYTAhaQqdWjk8zg2ot9pzvsLkQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHac1no66zTICdRsr8+/+Dc68TXoW463dhPmkwt3yP2+AiAex4bItov0fHDkLvPMrJ3dvjXni3lB46i3yA8e2JryMA=="}]}, "directories": {}}, "1.0.6": {"name": "event-target-shim", "version": "1.0.6", "description": "An implementation of W3C EventTarget interface.", "main": "lib/event-target.js", "files": ["dist", "lib"], "scripts": {"preversion": "npm run build", "postversion": "git push && git push --tags", "clean": "rimraf coverage dist", "lint": "eslint lib test", "build": "npm-run-all clean lint test build:*", "build:dist": "mkdirp dist && browserify lib/event-target.js --standalone event-target-shim > dist/event-target-shim.js", "build:dist-min": "uglifyjs dist/event-target-shim.js --compress --mangle > dist/event-target-shim.min.js", "test": "npm-run-all clean lint && karma start karma.conf.js --single-run", "watch": "karma start karma.conf.js --watch", "travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- test/**/*.js --require scripts/power-assert", "coveralls": "cat ./coverage/lcov.info | coveralls"}, "devDependencies": {"browserify": "^12.0.1", "browserify-istanbul": "^0.2.1", "coveralls": "^2.11.4", "eslint": "^1.10.2", "eslint-config-mysticatea": "^1.9.0", "eslint-plugin-mysticatea": "^1.0.3", "eslint-plugin-node": "^0.2.0", "espower-loader": "^1.0.0", "espowerify": "^1.0.0", "istanbul": "^0.4.1", "karma": "^0.13.15", "karma-browserify": "^4.2.1", "karma-chrome-launcher": "^0.2.1", "karma-coverage": "^0.5.3", "karma-firefox-launcher": "^0.1.6", "karma-growl-reporter": "^0.1.1", "karma-ie-launcher": "^0.2.0", "karma-mocha": "^0.2.1", "mkdirp": "^0.5.1", "mocha": "^2.2.5", "npm-run-all": "^1.2.5", "power-assert": "^1.2.0", "rimraf": "^2.3.4", "spy": "^0.1.3", "uglify-js": "^2.4.23"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/event-target-shim.git"}, "keywords": ["w3c", "eventtarget", "event", "events", "shim"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/event-target-shim/issues"}, "homepage": "https://github.com/mysticatea/event-target-shim", "gitHead": "5f9df4a85975b6e8306ef6830f8429495437cb94", "_id": "event-target-shim@1.0.6", "_shasum": "f1e6789742a7e5bfe62581e43905324ca81e6101", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "4.2.2", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}], "dist": {"shasum": "f1e6789742a7e5bfe62581e43905324ca81e6101", "tarball": "https://registry.npmjs.org/event-target-shim/-/event-target-shim-1.0.6.tgz", "integrity": "sha512-0bpI9p9P3a00R93w9NAcK0A02l20kUZJbI+k5NUQzOJilqU59M1VWZehpkQTPo3CJ/v/KFsk5EiaSDre8thhow==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCsN49qzq2fRtGKrqZjt6oo1OIxzj/VL6MKPmFb0MEGswIgcI3MkQ2xWwYeouLCIklM3RTUyNwMRuN+Bh7sHjjhpf8="}]}, "directories": {}}, "1.0.7": {"name": "event-target-shim", "version": "1.0.7", "description": "An implementation of W3C EventTarget interface.", "main": "lib/event-target.js", "files": ["dist", "lib"], "scripts": {"preversion": "npm run build", "postversion": "git push && git push --tags", "clean": "rimraf coverage dist", "lint": "eslint lib test", "build": "npm-run-all clean lint test build:*", "build:dist": "mkdirp dist && browserify lib/event-target.js --standalone event-target-shim > dist/event-target-shim.js", "build:dist-min": "uglifyjs dist/event-target-shim.js --compress --mangle > dist/event-target-shim.min.js", "test": "npm-run-all clean lint && karma start karma.conf.js --single-run", "watch": "karma start karma.conf.js --watch", "travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- test/**/*.js --require scripts/power-assert", "coveralls": "cat ./coverage/lcov.info | coveralls"}, "devDependencies": {"browserify": "^12.0.1", "browserify-istanbul": "^0.2.1", "coveralls": "^2.11.4", "eslint": "^1.10.2", "eslint-config-mysticatea": "^1.9.0", "eslint-plugin-mysticatea": "^1.0.3", "eslint-plugin-node": "^0.2.0", "espower-loader": "^1.0.0", "espowerify": "^1.0.0", "istanbul": "^0.4.1", "karma": "^0.13.15", "karma-browserify": "^4.2.1", "karma-chrome-launcher": "^0.2.1", "karma-coverage": "^0.5.3", "karma-firefox-launcher": "^0.1.6", "karma-growl-reporter": "^0.1.1", "karma-ie-launcher": "^0.2.0", "karma-mocha": "^0.2.1", "mkdirp": "^0.5.1", "mocha": "^2.2.5", "npm-run-all": "^1.2.5", "power-assert": "^1.2.0", "rimraf": "^2.3.4", "spy": "^0.1.3", "uglify-js": "^2.6.1"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/event-target-shim.git"}, "keywords": ["w3c", "eventtarget", "event", "events", "shim"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/event-target-shim/issues"}, "homepage": "https://github.com/mysticatea/event-target-shim", "gitHead": "0ac70f4221783aba9ed4bad78e8b5fd5e1e4f5ef", "_id": "event-target-shim@1.0.7", "_shasum": "ce11318120865f240a967801b400e95ca2e77cb7", "_from": ".", "_npmVersion": "3.5.0", "_nodeVersion": "4.2.2", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}], "dist": {"shasum": "ce11318120865f240a967801b400e95ca2e77cb7", "tarball": "https://registry.npmjs.org/event-target-shim/-/event-target-shim-1.0.7.tgz", "integrity": "sha512-qI1nz9Wd9LoKqnQA9sIDcPUEYNfk6Q5SGwjsLXgQF/61R90/7YfasGevb1WU6mAdsB5lpkurGUkYtB4R0iimMQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIELRxLVjMhxPStQ2t7hntjDCZINdU/qnjG+SbIfD1qJsAiEAvEAsg1lqGHmpE8rFhW82p3Z1w2/1+V4E/U2EctZF/L4="}]}, "directories": {}}, "1.1.0": {"name": "event-target-shim", "version": "1.1.0", "description": "An implementation of W3C EventTarget interface.", "main": "lib/event-target.js", "files": ["dist", "lib"], "scripts": {"preversion": "npm run build", "postversion": "git push && git push --tags", "clean": "rimraf coverage dist", "lint": "eslint lib test", "build": "npm-run-all clean lint test build:*", "build:dist": "mkdirp dist && browserify lib/event-target.js --standalone event-target-shim > dist/event-target-shim.js", "build:dist-min": "uglifyjs dist/event-target-shim.js --compress --mangle > dist/event-target-shim.min.js", "test": "npm-run-all clean lint && karma start karma.conf.js --single-run", "watch": "karma start karma.conf.js --watch", "travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- test/**/*.js --require scripts/power-assert", "coveralls": "cat ./coverage/lcov.info | coveralls"}, "devDependencies": {"browserify": "^12.0.1", "browserify-istanbul": "^0.2.1", "coveralls": "^2.11.4", "eslint": "^1.10.2", "eslint-config-mysticatea": "^1.9.0", "eslint-plugin-mysticatea": "^1.0.3", "eslint-plugin-node": "^0.2.0", "espower-loader": "^1.0.0", "espowerify": "^1.0.0", "istanbul": "^0.4.1", "karma": "^0.13.15", "karma-browserify": "^4.2.1", "karma-chrome-launcher": "^0.2.1", "karma-coverage": "^0.5.3", "karma-firefox-launcher": "^0.1.6", "karma-growl-reporter": "^0.1.1", "karma-ie-launcher": "^0.2.0", "karma-mocha": "^0.2.1", "mkdirp": "^0.5.1", "mocha": "^2.2.5", "npm-run-all": "^1.2.5", "power-assert": "^1.2.0", "rimraf": "^2.3.4", "spy": "^0.1.3", "uglify-js": "^2.6.1"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/event-target-shim.git"}, "keywords": ["w3c", "eventtarget", "event", "events", "shim"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/event-target-shim/issues"}, "homepage": "https://github.com/mysticatea/event-target-shim", "gitHead": "a5e1b870034a9c1d3e4a01c3e4b51d617e952daf", "_id": "event-target-shim@1.1.0", "_shasum": "e2d099db54f4709d83adb62ff64c31d53ccfa0ef", "_from": ".", "_npmVersion": "3.5.0", "_nodeVersion": "4.2.3", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}], "dist": {"shasum": "e2d099db54f4709d83adb62ff64c31d53ccfa0ef", "tarball": "https://registry.npmjs.org/event-target-shim/-/event-target-shim-1.1.0.tgz", "integrity": "sha512-cGWhM6g7RmZ3OfBLfNZoqwTgK9as9Z3h/92yoigwGKdNVM4Yg8r4l4toPbMx1rxPE0oB2weOvGYF7FwNzWg06A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC5WoStb8gDgvTxUENWQv0/oqIXILvvHNw7r9Qo7G1xaAiEAwX2Uu5Q1zubzq5336QOCZdNvwhcrLk4YUBtFIGCkCMQ="}]}, "directories": {}}, "1.1.1": {"name": "event-target-shim", "version": "1.1.1", "description": "An implementation of W3C EventTarget interface.", "main": "lib/event-target.js", "files": ["dist", "lib"], "scripts": {"preversion": "npm run build", "postversion": "git push && git push --tags", "clean": "rimraf coverage dist", "lint": "eslint lib test", "build": "npm-run-all clean lint test build:*", "build:dist": "mkdirp dist && browserify lib/event-target.js --standalone event-target-shim > dist/event-target-shim.js", "build:dist-min": "uglifyjs dist/event-target-shim.js --compress --mangle > dist/event-target-shim.min.js", "test": "npm-run-all clean lint && karma start karma.conf.js --single-run", "watch": "karma start karma.conf.js --watch", "travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- test/**/*.js --require scripts/power-assert", "coveralls": "cat ./coverage/lcov.info | coveralls"}, "devDependencies": {"browserify": "^12.0.1", "browserify-istanbul": "^0.2.1", "coveralls": "^2.11.4", "eslint": "^1.10.2", "eslint-config-mysticatea": "^1.9.0", "eslint-plugin-mysticatea": "^1.0.3", "eslint-plugin-node": "^0.2.0", "espower-loader": "^1.0.0", "espowerify": "^1.0.0", "istanbul": "^0.4.1", "karma": "^0.13.15", "karma-browserify": "^4.2.1", "karma-chrome-launcher": "^0.2.1", "karma-coverage": "^0.5.3", "karma-firefox-launcher": "^0.1.6", "karma-growl-reporter": "^0.1.1", "karma-ie-launcher": "^0.2.0", "karma-mocha": "^0.2.1", "mkdirp": "^0.5.1", "mocha": "^2.2.5", "npm-run-all": "^1.2.5", "power-assert": "^1.2.0", "rimraf": "^2.3.4", "spy": "^0.1.3", "uglify-js": "^2.6.1"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/event-target-shim.git"}, "keywords": ["w3c", "eventtarget", "event", "events", "shim"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/event-target-shim/issues"}, "homepage": "https://github.com/mysticatea/event-target-shim", "gitHead": "a42f0a2b99fcea18856cbc64ab3b5f0aceaecb7b", "_id": "event-target-shim@1.1.1", "_shasum": "a86e5ee6bdaa16054475da797ccddf0c55698491", "_from": ".", "_npmVersion": "3.5.0", "_nodeVersion": "4.2.3", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}], "dist": {"shasum": "a86e5ee6bdaa16054475da797ccddf0c55698491", "tarball": "https://registry.npmjs.org/event-target-shim/-/event-target-shim-1.1.1.tgz", "integrity": "sha512-9hnrQp9HNLexUaxXvgV83/DNrZET6Yjr5wFZowmv2sfbxYrpGT4YB4pmgvoJ6NmUUr/CDQbC1l99v9EaX3mO5w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDQz3BXlwwyBKPKJfULb9VdXt4dqyUh12BtNNZsN2qoawIgUqP9levvCT4q99M1eShcf3ialFuDK194owGKG2dMu08="}]}, "directories": {}}, "2.0.0": {"name": "event-target-shim", "version": "2.0.0", "description": "An implementation of W3C EventTarget interface.", "main": "lib/event-target.js", "files": ["dist", "lib"], "scripts": {"preversion": "npm test", "version": "npm run build && git add dist/*", "postversion": "git push && git push --tags", "clean": "rimraf coverage", "lint": "if-node-version \">=4\" eslint lib test", "build": "rimraf dist && run-s build:*", "build:dist": "mkdirp dist && browserify lib/event-target.js --standalone event-target-shim > dist/event-target-shim.js", "build:dist-min": "uglifyjs dist/event-target-shim.js --compress --mangle > dist/event-target-shim.min.js", "test": "run-s clean lint && karma start karma.conf.js --single-run", "watch": "karma start karma.conf.js --watch", "travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- test/**/*.js --require scripts/power-assert", "codecov": "codecov"}, "devDependencies": {"browserify": "^13.1.1", "browserify-istanbul": "^2.0.0", "codecov": "^1.0.1", "eslint": "^3.11.0", "eslint-config-mysticatea": "^7.0.1", "espower-loader": "^1.0.0", "espowerify": "^1.0.0", "if-node-version": "^1.1.1", "istanbul": "^0.4.1", "karma": "^1.3.0", "karma-browserify": "^5.1.0", "karma-chrome-launcher": "^2.0.0", "karma-coverage": "^1.1.1", "karma-firefox-launcher": "^1.0.0", "karma-growl-reporter": "^1.0.0", "karma-ie-launcher": "^1.0.0", "karma-mocha": "^1.3.0", "mkdirp": "^0.5.1", "mocha": "^3.2.0", "npm-run-all": "^3.1.1", "power-assert": "^1.2.0", "rimraf": "^2.3.4", "spy": "^0.1.3", "uglify-js": "^2.6.1", "watchify": "^3.7.0"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/event-target-shim.git"}, "keywords": ["w3c", "eventtarget", "event", "events", "shim"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/event-target-shim/issues"}, "homepage": "https://github.com/mysticatea/event-target-shim", "gitHead": "3a8e1435a65230f4ba3fa50402609865c49928a7", "_id": "event-target-shim@2.0.0", "_shasum": "a6c07fb273495a3789e105b49fcc3cb0a308fab4", "_from": ".", "_npmVersion": "3.8.9", "_nodeVersion": "0.10.48", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"shasum": "a6c07fb273495a3789e105b49fcc3cb0a308fab4", "tarball": "https://registry.npmjs.org/event-target-shim/-/event-target-shim-2.0.0.tgz", "integrity": "sha512-ncZITgPe4AeqabDvYAitkMFFHHOe49T9cuaUNfYg/bAsWSAifgOmC4I0WqCt4iMCZVovchn2UlPRMOSxYdeXPw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD4xpf9WLECja+4ST/kUDGuthCFlAQmtfSvV/H5MdfSIAIgeZ3tuVggxlo5y0PJcsU7QKH9ksQSkXZCu4cDRacqFGQ="}]}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/event-target-shim-2.0.0.tgz_1480179031483_0.25760268373414874"}, "directories": {}}, "3.0.0": {"name": "event-target-shim", "version": "3.0.0", "description": "An implementation of W3C EventTarget interface.", "main": "dist/event-target-shim", "files": ["dist"], "engines": {"code": ">=4"}, "scripts": {"preversion": "npm test", "version": "npm run build && git add dist/*", "postversion": "git push && git push --tags", "clean": "rimraf .nyc_output coverage", "coverage": "nyc report --reporter lcov && opener coverage/lcov-report/index.html", "lint": "eslint src test scripts", "build": "run-p build:*", "build:cjs": "node scripts/build-cjs.js", "build:mjs": "node scripts/build-mjs.js", "build:umd": "node scripts/build-umd.js", "pretest": "npm run lint", "test": "run-s test:*", "test:mocha": "nyc --require ./scripts/babel-register.js mocha test/index.mjs", "test:karma": "karma start scripts/karma.conf.js --single-run", "watch": "run-p watch:*", "watch:mocha": "mocha test/index.mjs --compilers mjs:babel-register --watch --growl", "watch:karma": "karma start scripts/karma.conf.js --watch", "codecov": "codecov"}, "devDependencies": {"babel-core": "^6.26.0", "babel-preset-env": "^1.6.0", "babel-register": "^6.26.0", "chai": "^4.1.2", "codecov": "^2.3.0", "eslint": "^4.7.2", "eslint-config-mysticatea": "^12.0.0", "karma": "^1.7.1", "karma-chrome-launcher": "^2.2.0", "karma-coverage": "^1.1.1", "karma-firefox-launcher": "^1.0.0", "karma-growl-reporter": "^1.0.0", "karma-ie-launcher": "^1.0.0", "karma-mocha": "^1.3.0", "karma-rollup-preprocessor": "^5.0.1", "mocha": "^3.5.3", "npm-run-all": "^4.1.1", "nyc": "^11.2.1", "opener": "^1.4.3", "rimraf": "^2.6.2", "rollup": "^0.50.0", "rollup-plugin-babel": "^3.0.2", "rollup-plugin-babel-minify": "^3.1.2", "rollup-plugin-commonjs": "^8.2.1", "rollup-plugin-json": "^2.3.0", "rollup-plugin-node-resolve": "^3.0.0", "rollup-watch": "^4.3.1", "spy": "^1.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/event-target-shim.git"}, "keywords": ["w3c", "eventtarget", "event", "events", "shim"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/event-target-shim/issues"}, "homepage": "https://github.com/mysticatea/event-target-shim", "gitHead": "03e27ed664d780475d58bd3e0461143ed1e9d300", "_id": "event-target-shim@3.0.0", "_npmVersion": "5.4.2", "_nodeVersion": "8.6.0", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-UCwmb8zwbAxZN9wM/B5S4DQC1wECq+Jhvev8TQuRYQoR+hGN2BY6uJkePusZSG9tFUrZTQBbgMpPYGqrybwxNg==", "shasum": "d2faa31db7d97188463b2cbde5e09601acae4672", "tarball": "https://registry.npmjs.org/event-target-shim/-/event-target-shim-3.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDGYpsjYpzMzTjXQZJVtLkcLVHaWImmR6dxwNdUODs5CAIhAJ8qANnHXts+AOaOP7Fnfrwmczr8G72FZjs1atqYsbg0"}]}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/event-target-shim-3.0.0.tgz_1506917580720_0.6984660590533167"}, "directories": {}}, "3.0.1": {"name": "event-target-shim", "version": "3.0.1", "description": "An implementation of WHATWG EventTarget interface.", "main": "dist/event-target-shim", "files": ["dist"], "engines": {"code": ">=4"}, "scripts": {"preversion": "npm test", "version": "npm run build && git add dist/*", "postversion": "git push && git push --tags", "clean": "rimraf .nyc_output coverage", "coverage": "nyc report --reporter lcov && opener coverage/lcov-report/index.html", "lint": "eslint src test scripts", "build": "run-p build:*", "build:cjs": "node scripts/build-cjs.js", "build:mjs": "node scripts/build-mjs.js", "build:umd": "node scripts/build-umd.js", "pretest": "npm run lint", "test": "run-s test:*", "test:mocha": "nyc --require ./scripts/babel-register.js mocha test/index.mjs", "test:karma": "karma start scripts/karma.conf.js --single-run", "watch": "run-p watch:*", "watch:mocha": "mocha test/index.mjs --compilers mjs:babel-register --watch --growl", "watch:karma": "karma start scripts/karma.conf.js --watch", "codecov": "codecov"}, "devDependencies": {"babel-core": "^6.26.0", "babel-preset-env": "^1.6.0", "babel-register": "^6.26.0", "chai": "^4.1.2", "codecov": "^3.0.0", "eslint": "^4.7.2", "eslint-config-mysticatea": "^13.0.0", "karma": "^2.0.0", "karma-chrome-launcher": "^2.2.0", "karma-coverage": "^1.1.1", "karma-firefox-launcher": "^1.0.0", "karma-growl-reporter": "^1.0.0", "karma-ie-launcher": "^1.0.0", "karma-mocha": "^1.3.0", "karma-rollup-preprocessor": "^5.0.1", "mocha": "^5.0.0", "npm-run-all": "^4.1.1", "nyc": "^11.2.1", "opener": "^1.4.3", "rimraf": "^2.6.2", "rollup": "^0.55.0", "rollup-plugin-babel": "^3.0.2", "rollup-plugin-babel-minify": "^3.1.2", "rollup-plugin-commonjs": "^8.2.1", "rollup-plugin-json": "^2.3.0", "rollup-plugin-node-resolve": "^3.0.0", "rollup-watch": "^4.3.1", "spy": "^1.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/event-target-shim.git"}, "keywords": ["w3c", "whatwg", "eventtarget", "event", "events", "shim"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/event-target-shim/issues"}, "homepage": "https://github.com/mysticatea/event-target-shim", "gitHead": "a1e78938ef82a75690baed2045a5f51ed9544253", "_id": "event-target-shim@3.0.1", "_npmVersion": "5.6.0", "_nodeVersion": "9.2.0", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-mgaF73LRFedJ9I4KGhd+0ZmWgMishY0FDgayG/LgxOHVFWC1lRi251uc6rX0Ml9F79KHyRU1lXuRzi/a/N+SnA==", "shasum": "a4a62f0795e5b65363e86c6780413224d1eea688", "tarball": "https://registry.npmjs.org/event-target-shim/-/event-target-shim-3.0.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC/NXQLGDqvaJroY/K2mlGM8vIM2DAvs75I7vC5p/z0tAIhANFEEyLNUXKOX5hBCkG3NaFyxE2JR7MdQKedQ43XC81n"}]}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/event-target-shim-3.0.1.tgz_1516708754681_0.2569048146251589"}, "directories": {}}, "3.0.2": {"name": "event-target-shim", "version": "3.0.2", "description": "An implementation of WHATWG EventTarget interface.", "main": "dist/event-target-shim", "files": ["dist"], "engines": {"node": ">=4"}, "scripts": {"preversion": "npm test", "version": "npm run build && git add dist/*", "postversion": "git push && git push --tags", "clean": "rimraf .nyc_output coverage", "coverage": "nyc report --reporter lcov && opener coverage/lcov-report/index.html", "lint": "eslint src test scripts --ext .js,.mjs", "build": "rollup -c scripts/rollup.config.js", "pretest": "npm run lint", "test": "run-s test:*", "test:mocha": "nyc --require ./scripts/babel-register.js mocha test/index.mjs", "test:karma": "karma start scripts/karma.conf.js --single-run", "watch": "run-p watch:*", "watch:mocha": "mocha test/index.mjs --compilers mjs:babel-register --watch --growl", "watch:karma": "karma start scripts/karma.conf.js --watch", "codecov": "codecov"}, "devDependencies": {"babel-core": "^6.26.3", "babel-preset-env": "^1.7.0", "babel-register": "^6.26.0", "chai": "^4.1.2", "codecov": "^3.0.2", "eslint": "^4.7.2", "eslint-config-mysticatea": "^13.0.0", "karma": "^2.0.4", "karma-chrome-launcher": "^2.2.0", "karma-coverage": "^1.1.2", "karma-firefox-launcher": "^1.0.0", "karma-growl-reporter": "^1.0.0", "karma-ie-launcher": "^1.0.0", "karma-mocha": "^1.3.0", "karma-rollup-preprocessor": "^6.0.0", "mocha": "^5.2.0", "npm-run-all": "^4.1.3", "nyc": "^12.0.2", "opener": "^1.4.3", "rimraf": "^2.6.2", "rollup": "^0.61.2", "rollup-plugin-babel": "^3.0.5", "rollup-plugin-babel-minify": "^5.0.0", "rollup-plugin-commonjs": "^9.1.3", "rollup-plugin-json": "^3.0.0", "rollup-plugin-node-resolve": "^3.3.0", "rollup-watch": "^4.3.1", "spy": "^1.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/event-target-shim.git"}, "keywords": ["w3c", "whatwg", "eventtarget", "event", "events", "shim"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/event-target-shim/issues"}, "homepage": "https://github.com/mysticatea/event-target-shim", "gitHead": "a33b40bfde7c9509a39f7fd05c2fc399c7c52c6a", "_id": "event-target-shim@3.0.2", "_npmVersion": "6.1.0", "_nodeVersion": "8.11.2", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-HK5GhnEAkm7fLy249GtF7DIuYmjLm85Ft6ssj7DhVl8Tx/z9+v0W6aiIVUdT4AXWGYy5Fc+s6gqBI49Bf0LejQ==", "shasum": "af25bb55a97c670bbeba985c82647fb64d892153", "tarball": "https://registry.npmjs.org/event-target-shim/-/event-target-shim-3.0.2.tgz", "fileCount": 9, "unpackedSize": 160030, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbMgfACRA9TVsSAnZWagAAjhYQAJPAcbh7CzhnxZ1A5zAJ\nF35lSQk30iFxgnbHmc477RzA4ao0p0jp/Vvluu93p8QLEI5cHFVTfWG1Ew5Q\nAz6pWRbk2J1KdqDT1srQoVPWyer8mcATFHNvMdiDWIY+4M6ivnlQpuv5S8zh\nQ1G4r1v3JVHGk1ahxeT6tdUrMm9KWA+ARj/g8O2VAkrfdaWVn4i02z2yDg3K\nXJJHy4UO/vUkOOUE8IQj3X33hglXd2C0SK5myFRNowaaICfiL27TrWb0E//Z\n1w3hLwYJzoyv/siJIxNg+B6zuijJCAmwXs+FA3Yu9aNm38KH7xChMKL6/fMo\nDjvjLYexxJEcbJiQ6DV0vOtgdKx1zGp+ZcApFK5+P77FD0S9N5ynXOdmjI1r\nDKfEna9zWp/TfjCLI6YZRgepyGjfQukjfNIqUfgTvUrwvsuM5MRHal1VktM1\nlTlV6mbHfA+lU+seBdCk6zZfvAmivm6ImwSSEhlMRKj0cg016+gu7R6xsq4k\nQVIGP38d5Xnwa2NSq2RpJ2g5dEWiGT84ISU+E6U9b0k38mhA7JZEA/kPUQ0z\nbWecDI3Kmdwiv8XIxYMb8u3dv7th75qOVbTdc/sEMf8dRKuNyFb0Gu2YTdWl\ndzSw/Ja8V1OcWNslNwfZ8ayTYawnzuDU8AVMoGiVwTaym4nCtoaKTnQsmZGI\nSV4Q\r\n=UXTf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC6t5DEbUjj4sB83XYLSePgAeuILZR1Uu8lVa+F+MEvcAiEA0/pelzptT6xN9CbNGZrr/UauAZYewqDy3VV+yeMbwLk="}]}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/event-target-shim_3.0.2_1530005440907_0.027979869051472095"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "event-target-shim", "version": "4.0.0", "description": "An implementation of WHATWG EventTarget interface.", "main": "dist/event-target-shim", "types": "index.d.ts", "engines": {"node": ">=6"}, "scripts": {"preversion": "npm test", "version": "npm run build && git add dist/*", "postversion": "git push && git push --tags", "clean": "rimraf .nyc_output coverage", "coverage": "nyc report --reporter lcov && opener coverage/lcov-report/index.html", "lint": "eslint src test scripts --ext .js,.mjs", "build": "rollup -c scripts/rollup.config.js", "pretest": "npm run lint", "test": "run-s test:*", "test:mocha": "nyc --require ./scripts/babel-register.js mocha test/index.mjs", "test:karma": "karma start scripts/karma.conf.js --single-run", "test:types": "tsc", "watch": "run-p watch:*", "watch:mocha": "mocha test/index.mjs --require ./scripts/babel-register.js --watch --watch-extensions js,mjs --growl", "watch:karma": "karma start scripts/karma.conf.js --watch", "codecov": "codecov"}, "devDependencies": {"@babel/core": "^7.1.6", "@babel/preset-env": "^7.1.6", "@babel/register": "^7.0.0", "@mysticatea/eslint-plugin": "^7.0.0", "@mysticatea/spy": "^0.1.2", "assert": "^1.4.1", "codecov": "^3.1.0", "eslint": "^5.9.0", "karma": "^3.1.1", "karma-chrome-launcher": "^2.2.0", "karma-coverage": "^1.1.2", "karma-firefox-launcher": "^1.0.0", "karma-growl-reporter": "^1.0.0", "karma-ie-launcher": "^1.0.0", "karma-mocha": "^1.3.0", "karma-rollup-preprocessor": "^6.1.1", "mocha": "^5.2.0", "npm-run-all": "^4.1.5", "nyc": "^13.1.0", "opener": "^1.5.1", "rimraf": "^2.6.2", "rollup": "^0.66.6", "rollup-plugin-babel": "^4.0.3", "rollup-plugin-babel-minify": "^6.2.0", "rollup-plugin-commonjs": "^9.2.0", "rollup-plugin-json": "^3.1.0", "rollup-plugin-node-resolve": "^3.4.0", "rollup-watch": "^4.3.1", "typescript": "~3.2.1"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/event-target-shim.git"}, "keywords": ["w3c", "whatwg", "eventtarget", "event", "events", "shim"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/event-target-shim/issues"}, "homepage": "https://github.com/mysticatea/event-target-shim", "gitHead": "a6c10d08fb526fc0425427c42776ab680d189053", "_id": "event-target-shim@4.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "11.1.0", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-wySKsjlbyfyg8fqmK/1hDI4vnhkPbZhFL6FfD5jJ+glfmxzXKnSakxAQB4ifsK0N4ps7JDsBd+0X1lKCfi4UVg==", "shasum": "9286cfbcd3f8e2d6c8400a0a98a381863c8b8467", "tarball": "https://registry.npmjs.org/event-target-shim/-/event-target-shim-4.0.0.tgz", "fileCount": 10, "unpackedSize": 181815, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcAo+jCRA9TVsSAnZWagAAtsAP/3fXQPKCgDGkKZULyTi9\ncajsylskyDR4lEkLAPPe/3zjY2oR5nobiU9ip4h3gyGwJwFEOIAjvk5qU6b+\nN6nI8KAR3lWj14qR/pqS5vLguHHgs4wUowEEQAcnD4Qa2LVtPYmXu7VtWnRV\n67Sk3lZ42NNbSJJcwmoMHQxl9ffEBAmqonIE7Hovm0sIpf/k03mZzjYTszGc\nRytBBGWf3plY5doBSXY4hXmS57l+BfFiCwy8AIufWbJ/ByCzftPPgM5yV7rJ\nAL2bTjCutr5hkbxInDQdbomBjB45edDPkIenbLEpGtwRXoSMI/JSzEt1tWrO\n4PEKxp54dIxmamkEtXukaCGvht4tu3UggRl8DXjMsuW59gp2PKhhtmLew+JX\nDy8kUN5SxuzSE2c/YqTcKnesG54CWHmqWeMWDFfJVHSz9zHlDVC3WCEDHZ4x\nNDgHrWx4rHB4OAVvriqVq+y1WQcqcb0AcopWhCCwoiI5VabLUuWsEpOSIjwG\n/VnHkHb0RSd8b9gWQzJ3SRpp/yiqSb7F8z9cZFkjhfmDGsPxaKdk1Zgvz0hC\nFCoCrHzJ6CW9xKNJzIX89KXwFppTPBeWpu7RYGP6myQ3V8rXnrSQX7FDHVHs\nt20ExZRpDZXJ6TcZgXuz1EkP95CdwqV9ouUeoudmiR3ZINlq5N3G3N0gZg8q\nysUk\r\n=Oq4R\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC7L+vb6wDAsOldP6joy8fNbF8RQsxs5ezAsWBoBkl98AiEAgyxTYQ9bmaI9KxnPl7B3MtqAec8N9tprAZldh4iyOIE="}]}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/event-target-shim_4.0.0_1543671715108_0.1154688913399855"}, "_hasShrinkwrap": false}, "4.0.1": {"name": "event-target-shim", "version": "4.0.1", "description": "An implementation of WHATWG EventTarget interface.", "main": "dist/event-target-shim", "types": "index.d.ts", "engines": {"node": ">=6"}, "scripts": {"preversion": "npm test", "version": "npm run build && git add dist/*", "postversion": "git push && git push --tags", "clean": "rimraf .nyc_output coverage", "coverage": "nyc report --reporter lcov && opener coverage/lcov-report/index.html", "lint": "eslint src test scripts --ext .js,.mjs", "build": "rollup -c scripts/rollup.config.js", "pretest": "npm run lint", "test": "run-s test:*", "test:mocha": "nyc --require ./scripts/babel-register.js mocha test/index.mjs", "test:karma": "karma start scripts/karma.conf.js --single-run", "test:types": "tsc", "watch": "run-p watch:*", "watch:mocha": "mocha test/index.mjs --require ./scripts/babel-register.js --watch --watch-extensions js,mjs --growl", "watch:karma": "karma start scripts/karma.conf.js --watch", "codecov": "codecov"}, "devDependencies": {"@babel/core": "^7.1.6", "@babel/preset-env": "^7.1.6", "@babel/register": "^7.0.0", "@mysticatea/eslint-plugin": "^7.0.0", "@mysticatea/spy": "^0.1.2", "assert": "^1.4.1", "codecov": "^3.1.0", "eslint": "^5.9.0", "karma": "^3.1.1", "karma-chrome-launcher": "^2.2.0", "karma-coverage": "^1.1.2", "karma-firefox-launcher": "^1.0.0", "karma-growl-reporter": "^1.0.0", "karma-ie-launcher": "^1.0.0", "karma-mocha": "^1.3.0", "karma-rollup-preprocessor": "^6.1.1", "mocha": "^5.2.0", "npm-run-all": "^4.1.5", "nyc": "^13.1.0", "opener": "^1.5.1", "rimraf": "^2.6.2", "rollup": "^0.66.6", "rollup-plugin-babel": "^4.0.3", "rollup-plugin-babel-minify": "^6.2.0", "rollup-plugin-commonjs": "^9.2.0", "rollup-plugin-json": "^3.1.0", "rollup-plugin-node-resolve": "^3.4.0", "rollup-watch": "^4.3.1", "typescript": "~3.2.1"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/event-target-shim.git"}, "keywords": ["w3c", "whatwg", "eventtarget", "event", "events", "shim"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/event-target-shim/issues"}, "homepage": "https://github.com/mysticatea/event-target-shim", "gitHead": "287ab36f866199c44a27486f073fb958d218eefa", "_id": "event-target-shim@4.0.1", "_npmVersion": "6.4.1", "_nodeVersion": "11.1.0", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-DLqpHgqoxYPQ5YVFHDAUk3DlLQWQPNnQPLRIJCuelQk7Dk+fWWaGS5sCs/75N2m1SuwPEw2JSZU0TOGYN2dsag==", "shasum": "1d922e76bdc720f2ad624b4e092bd0d726cc2c36", "tarball": "https://registry.npmjs.org/event-target-shim/-/event-target-shim-4.0.1.tgz", "fileCount": 10, "unpackedSize": 181891, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcAp3OCRA9TVsSAnZWagAAzQoP/R76J2QnN6q17ohXVOkI\nSsYrCCeESl1OBpINWsB0a8rxXhJfrKhfZtZ5go0jxPw9A5D645FVeBktQX4g\nQc4PMnnI7JXvwU1+ji26wWCi7ZNDuAKzi0wRwr5QPd453yJvIEK7AFVrKbak\n8LJMGhNyRtscf3Js/Lb/UkAy7D1MEuNQs37vTrHGCGvMNvxxZqCiChth2fxE\n3HSNte6kMVvmRPf27+pc3CUDT3i1rIrxmvQJLfp+1/3KWm0RmJqFeu2HxKLp\n88dQJnVF46K2zBY/sj9bA5s+k19Lw6/kx0V/NDTKHtc3Eui9EswdybMiCuPC\n55C40NtyJCT5XxEQsAdzy+iwqpBJ9IbR6mPArlTPTDucHArBW20giR2/38Bg\nRuISku9MFTFFpLFOLSZxpS4QqTn+YqShQcCL3x/GLJpxE2TeicSicYsdUCLk\nKda329Wm1dJWajWKHv/GkTWHvBwYXS/Z7ja+NXVDogNNx7R5Jr/GUXE072rK\nyE64EsQz0C63KS3XW1XDw56GGoRVM0iqW2QirTdF1zN3u66+Z5VGhDHPubsu\nP8lRy4digIlRV/i6BrwOkjtdAjG7yVANo3ujnUWC7Jo+n6kM6xd+vYmaTXaA\nCyyXdeBLRBX6JrvyRgfe3aw5+Lz5FwMZuMoytlIK4cQFFhoZjDJfkatlQSxG\nmr2s\r\n=Zi3j\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCgDUh2PT4B0Ht0eju5S0THIHkiIFjoZIAiKFsnVieFYgIgDDB8kbpXngsfJfMJsWIg6faIpCVOogdMyu+XGaLDWuk="}]}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/event-target-shim_4.0.1_1543675342048_0.44988206476608106"}, "_hasShrinkwrap": false}, "4.0.2": {"name": "event-target-shim", "version": "4.0.2", "description": "An implementation of WHATWG EventTarget interface.", "main": "dist/event-target-shim", "types": "index.d.ts", "engines": {"node": ">=6"}, "scripts": {"preversion": "npm test", "version": "npm run build && git add dist/*", "postversion": "git push && git push --tags", "clean": "rimraf .nyc_output coverage", "coverage": "nyc report --reporter lcov && opener coverage/lcov-report/index.html", "lint": "eslint src test scripts --ext .js,.mjs", "build": "rollup -c scripts/rollup.config.js", "pretest": "npm run lint", "test": "run-s test:*", "test:mocha": "nyc --require ./scripts/babel-register.js mocha test/index.mjs", "test:karma": "karma start scripts/karma.conf.js --single-run", "test:types": "tsc", "watch": "run-p watch:*", "watch:mocha": "mocha test/index.mjs --require ./scripts/babel-register.js --watch --watch-extensions js,mjs --growl", "watch:karma": "karma start scripts/karma.conf.js --watch", "codecov": "codecov"}, "devDependencies": {"@babel/core": "^7.1.6", "@babel/preset-env": "^7.1.6", "@babel/register": "^7.0.0", "@mysticatea/eslint-plugin": "^7.0.0", "@mysticatea/spy": "^0.1.2", "assert": "^1.4.1", "codecov": "^3.1.0", "eslint": "^5.9.0", "karma": "^3.1.1", "karma-chrome-launcher": "^2.2.0", "karma-coverage": "^1.1.2", "karma-firefox-launcher": "^1.0.0", "karma-growl-reporter": "^1.0.0", "karma-ie-launcher": "^1.0.0", "karma-mocha": "^1.3.0", "karma-rollup-preprocessor": "^6.1.1", "mocha": "^5.2.0", "npm-run-all": "^4.1.5", "nyc": "^13.1.0", "opener": "^1.5.1", "rimraf": "^2.6.2", "rollup": "^0.66.6", "rollup-plugin-babel": "^4.0.3", "rollup-plugin-babel-minify": "^6.2.0", "rollup-plugin-commonjs": "^9.2.0", "rollup-plugin-json": "^3.1.0", "rollup-plugin-node-resolve": "^3.4.0", "rollup-watch": "^4.3.1", "typescript": "~3.2.1"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/event-target-shim.git"}, "keywords": ["w3c", "whatwg", "eventtarget", "event", "events", "shim"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/event-target-shim/issues"}, "homepage": "https://github.com/mysticatea/event-target-shim", "gitHead": "8f3eaaeb921dc2f43edea1f5e596a91bbfbe717e", "_id": "event-target-shim@4.0.2", "_npmVersion": "6.4.1", "_nodeVersion": "11.1.0", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-D3ajgqNJgpqJA06ZqfsEELXEW+Dul1KA+QzySRT9oAocBXv5NX3BLM/Rh0apuHsDm5AcG3d/O8zqgHngpsVn1w==", "shasum": "ad5818028702c4e85b5c2398ddb0bdbc537b9e4b", "tarball": "https://registry.npmjs.org/event-target-shim/-/event-target-shim-4.0.2.tgz", "fileCount": 10, "unpackedSize": 181902, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBmZUCRA9TVsSAnZWagAAOXgP/3Z9bOXTMdgSUGp7rXw/\nbA7WbjMSZ5zY6t1dDhXZZ6L09AMI3iHdN5fzoUmiCaRKF9ea3GiMDOv1ZyAZ\n5scMyZnobI3+OHMNiFeOa6xyHZEQK2YT40cOrqjUiwVDBjxk9jwFOomEH+LK\ngZkVYDIBTkXzi0XoEuvzKM7dsjO2rAo0FEDAzMXes/13fb0pl7a1BvkhceoV\nPciyvf22ip2yVkZIupLhuYaUYiCKMPiC5By/A26w6Qqm1faZSB0DWz1XZa89\nX2siVwGLSz+sLWi7DuhDau3Whpz73XS7Kf6HQtHssFwsPP574VAefsBmIId3\ny2z7yDlFFFVLsUHi/1uSFkxG1X7rg452C881oMmWMmRId36cb9jcFSCYT+1o\nlraxNLSjAZQ5+nHTXsNGvhFLR+yWq3xI6FsXTPHe3TNHcWm6MPHiCsmHs5Kv\n5BYADmLeCfOWwY8J58l6imTGq2pWHmzzwanM9g30Ty7t5yC5U6cQKIX5aClR\nv2P2TBg/MfOxjm64f+0gyVOxMAestX/CGBDtKDHQdJTMOZg6M7Rv+d/Y1SiX\nVXH7t2gomj5PVESdy3AToX21dLoKEKNMGh/qgKL+SM7n+lw+WInNk1CaLk92\naRphpZDqn7+O2b5+DbEoX7ndGfVBp/4AynyfWXu1r9U4tkICGSbtzETO6Qfy\nELd0\r\n=Sj/B\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDNU+lQM9mdcYcBazqxjp/LhBWk1/yVh28Nascqyxlt/QIgagshmGTI5f0Iovqpg+StSCYBgvSqpVhnuvxWxHvPnd4="}]}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/event-target-shim_4.0.2_1543923283862_0.812382212577629"}, "_hasShrinkwrap": false}, "4.0.3": {"name": "event-target-shim", "version": "4.0.3", "description": "An implementation of WHATWG EventTarget interface.", "main": "dist/event-target-shim", "types": "index.d.ts", "engines": {"node": ">=6"}, "scripts": {"preversion": "npm test", "version": "npm run build && git add dist/*", "postversion": "git push && git push --tags", "clean": "rimraf .nyc_output coverage", "coverage": "nyc report --reporter lcov && opener coverage/lcov-report/index.html", "lint": "eslint src test scripts --ext .js,.mjs", "build": "rollup -c scripts/rollup.config.js", "pretest": "npm run lint", "test": "run-s test:*", "test:mocha": "nyc --require ./scripts/babel-register mocha test/index.mjs", "test:karma": "karma start scripts/karma.conf.js --single-run", "test:types": "tsc", "watch": "run-p watch:*", "watch:mocha": "mocha test/index.mjs --require ./scripts/babel-register --watch --watch-extensions js,mjs --growl", "watch:karma": "karma start scripts/karma.conf.js --watch", "codecov": "codecov"}, "devDependencies": {"@babel/core": "^7.1.6", "@babel/plugin-transform-modules-commonjs": "^7.2.0", "@babel/preset-env": "^7.1.6", "@babel/register": "^7.0.0", "@mysticatea/eslint-plugin": "^7.0.0", "@mysticatea/spy": "^0.1.2", "assert": "^1.4.1", "codecov": "^3.1.0", "eslint": "^5.9.0", "karma": "^3.1.1", "karma-chrome-launcher": "^2.2.0", "karma-coverage": "^1.1.2", "karma-firefox-launcher": "^1.0.0", "karma-growl-reporter": "^1.0.0", "karma-ie-launcher": "^1.0.0", "karma-mocha": "^1.3.0", "karma-rollup-preprocessor": "^6.1.1", "mocha": "^5.2.0", "npm-run-all": "^4.1.5", "nyc": "^13.1.0", "opener": "^1.5.1", "rimraf": "^2.6.2", "rollup": "^0.66.6", "rollup-plugin-babel": "^4.0.3", "rollup-plugin-babel-minify": "^6.2.0", "rollup-plugin-commonjs": "^9.2.0", "rollup-plugin-json": "^3.1.0", "rollup-plugin-node-resolve": "^3.4.0", "rollup-watch": "^4.3.1", "typescript": "~3.2.1"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/event-target-shim.git"}, "keywords": ["w3c", "whatwg", "eventtarget", "event", "events", "shim"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/event-target-shim/issues"}, "homepage": "https://github.com/mysticatea/event-target-shim", "gitHead": "f7f80bbab8455c2337e7ad8a1f8b3621411232ad", "_id": "event-target-shim@4.0.3", "_npmVersion": "6.4.1", "_nodeVersion": "6.15.1", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-YXVJDPGzU0yhSzrXGoEFAByEaLnZXSlJDHXkq4Hp6WrlnV66tADiZugYf1utTaCP/dsYRcLndAhecbq9mnbbqg==", "shasum": "758cd632cd4241f5a73ceec64f93f0d7b0ae77d6", "tarball": "https://registry.npmjs.org/event-target-shim/-/event-target-shim-4.0.3.tgz", "fileCount": 10, "unpackedSize": 181924, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBzZaCRA9TVsSAnZWagAAsgYP/R3E5s4Nrj9XUabu75Fl\nEYdx379hEAC357Q5Ehn8Iy97ppz/MGHrPokgNr31n+xB6dSyk7/Q56fQiLc2\nDwDpP9Dq+o3beL5mg5cCI7rGkerxIPYwjSmzZf4UhglNegkqR3LfixonMTa5\nZwUgHUwlNxd7ufnXgGhBxWRnqEJAf7dHPiFMihDhp7pkM3/69km1884HRoWu\nAYxjgDPuCTgR1eyzWD+XTe3D8sWYq9d4cjOW3ANrrPA+Fs0GeggzYCxABTJT\nm9nhwMGntjofP+NdlSczDT7nA+PrTz+Yzso0ZdxUQ02sJG0+4jwb/tjMDm8s\nUcRiH8gcb1WdhaOcBFgcV+xkfRJmNXT8MF6M/x2b08qczX20QVFkTvnmz3Wv\nXZonL4D51oFR5PouQbPJG+KXDGLq4wBT3wDeRgwMq6k2sfQKmnDa8MQ1Imw8\n2Fm1nf09MWqwSWLfHy/f6T30GFExXaL+WdRJbghr9HuOGVYvJk386NpIIxDJ\n6OUi6tUuRMN3y9fkgX9ohoy2u2Y8EAUf5Fo8j6f4p60bFCLMI5FQtHvcFsyY\nHpPBwo/I1f3Fin1qkLmA2k9RiooObW3lBn2Waz4ZzMVBwh2a3jXeHDI4iZpU\ndseT/olF1gY3NZJe/LR2hqrP+4wvM9C/EStO9mekTPfLkMKfQBFZ4EPW0b0O\nBOj+\r\n=T1On\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBTepCoaWU2g/g6l5o/NI40UJQdDNoiD1iIh4akAMED2AiAXYyGyKPpS3lF2Ss5U6zp3H7+W+qdUxjcFlTwpkJStUg=="}]}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/event-target-shim_4.0.3_1543976537516_0.8478395699257628"}, "_hasShrinkwrap": false}, "5.0.0-beta.0": {"name": "event-target-shim", "version": "5.0.0-beta.0", "description": "An implementation of WHATWG EventTarget interface.", "main": "dist/event-target-shim", "types": "index.d.ts", "engines": {"node": ">=6"}, "scripts": {"preversion": "npm test", "version": "npm run build && git add dist/*", "postversion": "git push && git push --tags", "clean": "rimraf .nyc_output coverage", "coverage": "nyc report --reporter lcov && opener coverage/lcov-report/index.html", "lint": "eslint src test scripts --ext .js,.mjs", "build": "rollup -c scripts/rollup.config.js", "pretest": "npm run lint", "test": "run-s test:*", "test:mocha": "nyc --require ./scripts/babel-register mocha test/*.mjs", "test:karma": "karma start scripts/karma.conf.js --single-run", "watch": "run-p watch:*", "watch:mocha": "mocha test/*.mjs --require ./scripts/babel-register --watch --watch-extensions js,mjs --growl", "watch:karma": "karma start scripts/karma.conf.js --watch", "codecov": "codecov"}, "devDependencies": {"@babel/core": "^7.1.6", "@babel/plugin-transform-modules-commonjs": "^7.2.0", "@babel/preset-env": "^7.1.6", "@babel/register": "^7.0.0", "@mysticatea/eslint-plugin": "^7.0.0", "@mysticatea/spy": "^0.1.2", "assert": "^1.4.1", "codecov": "^3.1.0", "eslint": "^5.9.0", "karma": "^3.1.1", "karma-chrome-launcher": "^2.2.0", "karma-coverage": "^1.1.2", "karma-firefox-launcher": "^1.0.0", "karma-growl-reporter": "^1.0.0", "karma-ie-launcher": "^1.0.0", "karma-mocha": "^1.3.0", "karma-rollup-preprocessor": "^6.1.1", "mocha": "^5.2.0", "npm-run-all": "^4.1.5", "nyc": "^13.1.0", "opener": "^1.5.1", "rimraf": "^2.6.2", "rollup": "^0.66.6", "rollup-plugin-babel": "^4.0.3", "rollup-plugin-babel-minify": "^6.2.0", "rollup-plugin-commonjs": "^9.2.0", "rollup-plugin-json": "^3.1.0", "rollup-plugin-node-resolve": "^3.4.0", "rollup-watch": "^4.3.1", "typescript": "~3.2.1"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/event-target-shim.git"}, "keywords": ["w3c", "whatwg", "eventtarget", "event", "events", "shim"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/event-target-shim/issues"}, "homepage": "https://github.com/mysticatea/event-target-shim", "readme": "# event-target-shim\n\n[![npm version](https://img.shields.io/npm/v/event-target-shim.svg)](https://www.npmjs.com/package/event-target-shim)\n[![Downloads/month](https://img.shields.io/npm/dm/event-target-shim.svg)](http://www.npmtrends.com/event-target-shim)\n[![Build Status](https://travis-ci.org/mysticatea/event-target-shim.svg?branch=master)](https://travis-ci.org/mysticatea/event-target-shim)\n[![Coverage Status](https://codecov.io/gh/mysticatea/event-target-shim/branch/master/graph/badge.svg)](https://codecov.io/gh/mysticatea/event-target-shim)\n[![Dependency Status](https://david-dm.org/mysticatea/event-target-shim.svg)](https://david-dm.org/mysticatea/event-target-shim)\n\nAn implementation of [WHATWG EventTarget interface](https://dom.spec.whatwg.org/#interface-eventtarget), plus few extensions.\n\n- This provides `EventTarget` constructor that can inherit for your custom object.\n- This provides an utility that defines properties of attribute listeners (e.g. `obj.onclick`).\n\n```js\nimport {EventTarget, defineEventAttribute} from \"event-target-shim\"\n\nclass Foo extends EventTarget {\n    // ...\n}\n\n// Define `foo.onhello` property.\ndefineEventAttribute(Foo.prototype, \"hello\")\n\n// Use\nconst foo = new Foo()\nfoo.addEventListener(\"hello\", e => console.log(\"hello\", e))\nfoo.onhello = e => console.log(\"onhello:\", e)\nfoo.dispatchEvent(new CustomEvent(\"hello\"))\n```\n\n## 💿 Installation\n\nUse [npm](https://www.npmjs.com/) to install then use a bundler.\n\n```\nnpm install event-target-shim\n```\n\nOr download from [`dist` directory](./dist).\n\n- [dist/event-target-shim.mjs](dist/event-target-shim.mjs) ... ES modules version.\n- [dist/event-target-shim.js](dist/event-target-shim.js) ... Common JS version.\n- [dist/event-target-shim.umd.js](dist/event-target-shim.umd.js) ... UMD (Universal Module Definition) version. This is transpiled by [Babel](https://babeljs.io/) for IE 11.\n\n## 📖 Usage\n\n```js\nimport {EventTarget, defineEventAttribute} from \"event-target-shim\"\n// or\nconst {EventTarget, defineEventAttribute} = require(\"event-target-shim\")\n\n// or UMD version defines a global variable:\nconst {EventTarget, defineEventAttribute} = window.EventTargetShim\n```\n\n### EventTarget\n\n> https://dom.spec.whatwg.org/#interface-eventtarget\n\n#### eventTarget.addEventListener(type, callback, options)\n\nRegister an event listener.\n\n- `type` is a string. This is the event name to register.\n- `callback` is a function. This is the event listener to register.\n- `options` is a boolean or an object `{ capture?: boolean, passive?: boolean, once?: boolean }`. If this is a boolean, it's same meaning as `{ capture: options }`.\n    - `capture` is the flag to register the event listener for capture phase.\n    - `passive` is the flag to ignore `event.preventDefault()` method in the event listener.\n    - `once` is the flag to remove the event listener automatically after the first call.\n\n#### eventTarget.removeEventListener(type, callback, options)\n\nUnregister an event listener.\n\n- `type` is a string. This is the event name to unregister.\n- `callback` is a function. This is the event listener to unregister.\n- `options` is a boolean or an object `{ capture?: boolean }`. If this is a boolean, it's same meaning as `{ capture: options }`.\n    - `capture` is the flag to register the event listener for capture phase.\n\n#### eventTarget.dispatchEvent(event)\n\nDispatch an event.\n\n- `event` is a [Event](https://dom.spec.whatwg.org/#event) object or an object `{ type: string, [key: string]: any }`. The latter is non-standard but useful. In both cases, listeners receive the event as implementing [Event](https://dom.spec.whatwg.org/#event) interface.\n\n### defineEventAttribute(proto, type)\n\nDefine an event attribute (e.g. `onclick`) to `proto`. This is non-standard.\n\n- `proto` is an object (assuming it's a prototype object). This function defines a getter/setter pair for the event attribute.\n- `type` is a string. This is the event name to define.\n\nFor example:\n\n```js\nclass AbortSignal extends EventTarget {\n    constructor() {\n        this.aborted = false\n    }\n}\n// Define `onabort` property.\ndefineEventAttribute(AbortSignal.prototype, \"abort\")\n```\n\n### EventTarget(types)\n\nDefine a custom `EventTarget` class with event attributes. This is non-standard.\n\n- `types` is a string or an array of strings. This is the event name to define.\n\nFor example:\n\n```js\n// This has `onabort` property.\nclass AbortSignal extends EventTarget(\"abort\") {\n    constructor() {\n        this.aborted = false\n    }\n}\n```\n\n## 📚 Examples\n\n### ES2015 and later\n\n> https://jsfiddle.net/636vea92/\n\n```js\nconst {EventTarget, defineEventAttribute} = EventTargetShim\n\n// Define a derived class.\nclass Foo extends EventTarget {\n    // ...\n}\n\n// Define `foo.onhello` property.\ndefineEventAttribute(Foo.prototype, \"hello\")\n\n// Register event listeners.\nconst foo = new Foo()\nfoo.addEventListener(\"hello\", (e) => {\n    console.log(\"hello\", e)\n})\nfoo.onhello = (e) => {\n    console.log(\"onhello\", e)\n}\n\n// Dispatching events\nfoo.dispatchEvent(new CustomEvent(\"hello\", { detail: \"detail\" }))\n```\n\n### Typescript\n\n```ts\nimport { EventTarget, defineEventAttribute } from \"event-target-shim\";\n\n// Define events\ntype FooEvents = {\n    hello: CustomEvent\n}\ntype FooEventAttributes = {\n    onhello: CustomEvent\n}\n\n// Define a derived class.\nclass Foo extends EventTarget<FooEvents, FooEventAttributes> {\n    // ...\n}\n// Define `foo.onhello` property's implementation.\ndefineEventAttribute(Foo.prototype, \"hello\")\n\n// Register event listeners.\nconst foo = new Foo()\nfoo.addEventListener(\"hello\", (e) => {\n    console.log(\"hello\", e.detail)\n})\nfoo.onhello = (e) => {\n    console.log(\"onhello\", e.detail)\n}\n\n// Dispatching events\nfoo.dispatchEvent(new CustomEvent(\"hello\", { detail: \"detail\" }))\n```\n\nUnfortunately, both `FooEvents` and `FooEventAttributes` are needed because TypeScript doesn't allow the mutation of string literal types. If TypeScript allowed us to compute `\"onhello\"` from `\"hello\"` in types, `FooEventAttributes` will be optional.\n\nThis `EventTarget` type is compatible with `EventTarget` interface of `lib.dom.d.ts`.\n\n#### To disallow unknown events\n\nBy default, methods such as `addEventListener` accept unknown events. You can disallow unknown events by the third type parameter `\"strict\"`.\n\n```ts\ntype FooEvents = {\n    hello: CustomEvent\n}\nclass Foo extends EventTarget<FooEvents, {}, \"strict\"> {\n    // ...\n}\n\n// OK because `hello` is defined in FooEvents.\nfoo.addEventListener(\"hello\", (e) => {\n})\n// Error because `unknown` is not defined in FooEvents.\nfoo.addEventListener(\"unknown\", (e) => {\n})\n```\n\nHowever, if you use `\"strict\"` parameter, it loses compatibility with `EventTarget` interface of `lib.dom.d.ts`.\n\n#### To infer the type of `dispatchEvent()` method\n\nTypeScript cannot infer the event type of `dispatchEvent()` method properly from the argument in most cases. You can improve this behavior with the following steps:\n\n1. Use the third type parameter `\"strict\"`. This prevents inferring to `dispatchEvent<string>()`.\n2. Make the `type` property of event definitions stricter.\n\n```ts\ntype FooEvents = {\n    hello: CustomEvent & { type: \"hello\" }\n    hey: Event & { type: \"hey\" }\n}\nclass Foo extends EventTarget<FooEvents, {}, \"strict\"> {\n    // ...\n}\n\n// Error because `detail` property is lacking.\nfoo.dispatchEvent({ type: \"hello\" })\n```\n\n### ES5\n\n> https://jsfiddle.net/522zc9de/\n\n```js\n// Define a derived class.\nfunction Foo() {\n    EventTarget.call(this)\n}\nFoo.prototype = Object.create(EventTarget.prototype, {\n    constructor: { value: Foo, configurable: true, writable: true }\n    // ...\n})\n\n// Define `foo.onhello` property.\ndefineEventAttribute(Foo.prototype, \"hello\")\n\n// Register event listeners.\nvar foo = new Foo()\nfoo.addEventListener(\"hello\", function(e) {\n    console.log(\"hello\", e)\n})\nfoo.onhello = function(e) {\n    console.log(\"onhello\", e)\n}\n\n// Dispatching events\nfunction isSupportEventConstrucor() { // IE does not support.\n    try {\n        new CusomEvent(\"hello\")\n        return true\n    } catch (_err) {\n        return false\n    }\n}\nif (isSupportEventConstrucor()) {\n    foo.dispatchEvent(new CustomEvent(\"hello\", { detail: \"detail\" }))\n} else {\n    var e = document.createEvent(\"CustomEvent\")\n    e.initCustomEvent(\"hello\", false, false, \"detail\")\n    foo.dispatchEvent(e)\n}\n```\n\n## 📰 Changelog\n\n- See [GitHub releases](https://github.com/mysticatea/event-target-shim/releases).\n\n## 🍻 Contributing\n\nContributing is welcome ❤️\n\nPlease use GitHub issues/PRs.\n\n### Development tools\n\n- `npm install` installs dependencies for development.\n- `npm test` runs tests and measures code coverage.\n- `npm run clean` removes temporary files of tests.\n- `npm run coverage` opens code coverage of the previous test with your default browser.\n- `npm run lint` runs ESLint.\n- `npm run build` generates `dist` codes.\n- `npm run watch` runs tests on each file change.\n", "readmeFilename": "README.md", "gitHead": "0b85ee9d3fa48e4ca1a0abdc3696d7bbd419d73c", "_id": "event-target-shim@5.0.0-beta.0", "_npmVersion": "6.5.0", "_nodeVersion": "11.1.0", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-0pQ5ZGKLtUbUAEN6fPTHkZVi9HXKOut0RbUJDdf5/mYGfmEjFiJybnjMh73gtQghbfbacbS/nk9EMo/QtDzjKw==", "shasum": "00f535458f32b46c990cfd03c0c33ace2006b58d", "tarball": "https://registry.npmjs.org/event-target-shim/-/event-target-shim-5.0.0-beta.0.tgz", "fileCount": 10, "unpackedSize": 189344, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcQch3CRA9TVsSAnZWagAAmTcP/AzoBFdFC8fIVQfc003b\n5DzMUspkAxxTzkWj+8E/tFIugl50cm11ZFDm0whawMYCr28Vkq+Jex0ujI4h\n4OycFwNQtX6TbdNMZrK8R11+i1rMGE1mSj9yyM6ehcbmeg8QEfH2OPNMIJFm\n0yFOtEbsX36eiju2/OdfEziJ4PHA0UAc9mj0q7p96eaA9qkzh7Cp5SnRnal5\nQ5cKktCGuqHBTzJ3qPm7wprp/j3qfnQT/clUg6//dazuOK3cKNplBPjDYz+/\nJrb2ptqk/W5Yi3SDyAc0tqZQQLEGqu5N87LZwl8fnL4L4wH2X/awrbWBQYO+\n4Mv9mNqhfuh7S49pGOPVxTLd5eyYKi9RRopqI1yI3EvLL02LvrkHmEpOplid\ncVmcHUooV4mWoi1r/kv6u2H9CjAscob8dQ3/67PmeptYUabXfwulmiN7MZwn\nxmIt9uGD8QL8/mg8h7ClqSJgEmyt/jGAkSIeJOhItQtw/xlXxVU/0fNJYAhW\nj/peqMCi8FkqbctrgtOwjNd8Ydz4cKSC/E1ip8GXo1LiDLbYUL1liU5rtJvk\nihsQdEy2E2WOhM9NhFoBQSW01DJaqRSCXHZOXkdEJKy1QtLHC8k1VWBNZJWI\nHnXlf/gViJmhtrFCk0Fy9JoETfTkXbBSkF9w1A2Y7qVVJg/c+zxOJXqJuRDP\nh1w0\r\n=Dj/L\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBBN8n7J1cK41nkw0aJIC+t3gxRM4GaK54OKmui/9bZsAiAzKR7xYErzcl7do9HPavPWqhePWyCuNXJ4Ily5T/QdVQ=="}]}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/event-target-shim_5.0.0-beta.0_1547815030429_0.7861757433505683"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "event-target-shim", "version": "5.0.0", "description": "An implementation of WHATWG EventTarget interface.", "main": "dist/event-target-shim", "types": "index.d.ts", "engines": {"node": ">=6"}, "scripts": {"preversion": "npm test", "version": "npm run build && git add dist/*", "postversion": "git push && git push --tags", "clean": "rimraf .nyc_output coverage", "coverage": "nyc report --reporter lcov && opener coverage/lcov-report/index.html", "lint": "eslint src test scripts --ext .js,.mjs", "build": "rollup -c scripts/rollup.config.js", "pretest": "npm run lint", "test": "run-s test:*", "test:mocha": "nyc --require ./scripts/babel-register mocha test/*.mjs", "test:karma": "karma start scripts/karma.conf.js --single-run", "watch": "run-p watch:*", "watch:mocha": "mocha test/*.mjs --require ./scripts/babel-register --watch --watch-extensions js,mjs --growl", "watch:karma": "karma start scripts/karma.conf.js --watch", "codecov": "codecov"}, "devDependencies": {"@babel/core": "^7.2.2", "@babel/plugin-transform-modules-commonjs": "^7.2.0", "@babel/preset-env": "^7.2.3", "@babel/register": "^7.0.0", "@mysticatea/eslint-plugin": "^8.0.1", "@mysticatea/spy": "^0.1.2", "assert": "^1.4.1", "codecov": "^3.1.0", "eslint": "^5.12.1", "karma": "^3.1.4", "karma-chrome-launcher": "^2.2.0", "karma-coverage": "^1.1.2", "karma-firefox-launcher": "^1.0.0", "karma-growl-reporter": "^1.0.0", "karma-ie-launcher": "^1.0.0", "karma-mocha": "^1.3.0", "karma-rollup-preprocessor": "^7.0.0-rc.2", "mocha": "^5.2.0", "npm-run-all": "^4.1.5", "nyc": "^13.1.0", "opener": "^1.5.1", "rimraf": "^2.6.3", "rollup": "^1.1.1", "rollup-plugin-babel": "^4.3.2", "rollup-plugin-babel-minify": "^7.0.0", "rollup-plugin-commonjs": "^9.2.0", "rollup-plugin-json": "^3.1.0", "rollup-plugin-node-resolve": "^4.0.0", "rollup-watch": "^4.3.1", "typescript": "^3.2.4"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/event-target-shim.git"}, "keywords": ["w3c", "whatwg", "eventtarget", "event", "events", "shim"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/event-target-shim/issues"}, "homepage": "https://github.com/mysticatea/event-target-shim", "gitHead": "a3774a59a5b71ef7a45f313bb1ec271b4bea4acd", "_id": "event-target-shim@5.0.0", "_nodeVersion": "11.1.0", "_npmVersion": "6.6.0", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-vu4tlY5xqMEGj/rzuDHxfvm9Kk2562O5h58i8xwnkMkv/yqmBqBcDJt/vGBrOBbCKuVc5eV3ghYxAX9YUhyi0w==", "shasum": "8fabe06975219af64c36ceed4771cf1ad1c3455c", "tarball": "https://registry.npmjs.org/event-target-shim/-/event-target-shim-5.0.0.tgz", "fileCount": 10, "unpackedSize": 188856, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcRXLwCRA9TVsSAnZWagAA/W8P/iNxFrfqMi0kcBeeJhJT\n6L+3Z5xmwGs1GTkscVrD6LEVArPjMiy+I9DXBzJk9RceDcInj9fToBdYLU2r\niuabRDxdz9T8S8SXY1eaVHhed9CylAFrOKL44I8/8ouQlr+YtvARSdVyrJmN\nEcHgrJenagH/XlwcEjH30mJhZGdCH2xdWSX9iKljWp2XyrC7aZyNFafH9cSt\nD2ECST3rtdbB+W1EGcEtVjLDIkqJExzOjBYS11T4dltmARfEsNb5yECrIuAo\naF53Ywn/o70f4feVI8N9GoOkTUCGWssN/HapNOGEmj2dcChG/0z1WTHleF0o\nYAYVNaD1RKxD6hrNAAoc+RDkaNfi5j5M1SzMc0aMzsGAI8+ewOeD5hlGAek3\nPSQ+10eV3SXQkL6aIdRuZ6FAH6uqxpXDVlT3Yk1FFBJa5xdudRHoMAOC9MK0\n/yGcXuWX4Rw+Ubn21B8GT7QqInTO80KdLnKDE0zKiKbhNo8sCQa4IJHJCFSa\n8B6NqRWzbgby+20UX2pVUVGRsYJflUWDNUpA6duJwMAwKJ099GBBG2kt+U7F\nLZagw85CLyGHAJqAroezDLsNof2CkODokdrj2ixmnMxFTGWO7ZGa1yxgwFQF\nJSA6bfeYPAr6Y10a5590ohni1htuuwQWlDU+4On5i9thJ6wPYO5jipPw+dH7\nHNhK\r\n=0kiE\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAOf6XrDKmiXdlX+wMuC1ct28liVVSpVDQrOMDqJ6ZjEAiEAwgb8lh3npiDHt/GNPhZ5c1HrcDgcLFJAE1X27Dw4TN8="}]}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/event-target-shim_5.0.0_1548055280171_0.7655104485164022"}, "_hasShrinkwrap": false}, "5.0.1": {"name": "event-target-shim", "version": "5.0.1", "description": "An implementation of WHATWG EventTarget interface.", "main": "dist/event-target-shim", "types": "index.d.ts", "engines": {"node": ">=6"}, "scripts": {"preversion": "npm test", "version": "npm run build && git add dist/*", "postversion": "git push && git push --tags", "clean": "rimraf .nyc_output coverage", "coverage": "nyc report --reporter lcov && opener coverage/lcov-report/index.html", "lint": "eslint src test scripts --ext .js,.mjs", "build": "rollup -c scripts/rollup.config.js", "pretest": "npm run lint", "test": "run-s test:*", "test:mocha": "nyc --require ./scripts/babel-register mocha test/*.mjs", "test:karma": "karma start scripts/karma.conf.js --single-run", "watch": "run-p watch:*", "watch:mocha": "mocha test/*.mjs --require ./scripts/babel-register --watch --watch-extensions js,mjs --growl", "watch:karma": "karma start scripts/karma.conf.js --watch", "codecov": "codecov"}, "devDependencies": {"@babel/core": "^7.2.2", "@babel/plugin-transform-modules-commonjs": "^7.2.0", "@babel/preset-env": "^7.2.3", "@babel/register": "^7.0.0", "@mysticatea/eslint-plugin": "^8.0.1", "@mysticatea/spy": "^0.1.2", "assert": "^1.4.1", "codecov": "^3.1.0", "eslint": "^5.12.1", "karma": "^3.1.4", "karma-chrome-launcher": "^2.2.0", "karma-coverage": "^1.1.2", "karma-firefox-launcher": "^1.0.0", "karma-growl-reporter": "^1.0.0", "karma-ie-launcher": "^1.0.0", "karma-mocha": "^1.3.0", "karma-rollup-preprocessor": "^7.0.0-rc.2", "mocha": "^5.2.0", "npm-run-all": "^4.1.5", "nyc": "^13.1.0", "opener": "^1.5.1", "rimraf": "^2.6.3", "rollup": "^1.1.1", "rollup-plugin-babel": "^4.3.2", "rollup-plugin-babel-minify": "^7.0.0", "rollup-plugin-commonjs": "^9.2.0", "rollup-plugin-json": "^3.1.0", "rollup-plugin-node-resolve": "^4.0.0", "rollup-watch": "^4.3.1", "type-tester": "^1.0.0", "typescript": "^3.2.4"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/event-target-shim.git"}, "keywords": ["w3c", "whatwg", "eventtarget", "event", "events", "shim"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/event-target-shim/issues"}, "homepage": "https://github.com/mysticatea/event-target-shim", "gitHead": "410bad2ce333e7eeaafbc6acae0679aa685281c9", "_id": "event-target-shim@5.0.1", "_nodeVersion": "11.9.0", "_npmVersion": "6.7.0", "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ==", "shasum": "5d4d3ebdf9583d63a5333ce2deb7480ab2b05789", "tarball": "https://registry.npmjs.org/event-target-shim/-/event-target-shim-5.0.1.tgz", "fileCount": 10, "unpackedSize": 188883, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJca5uUCRA9TVsSAnZWagAAvwMP/jVzsnBIjv2ii7M2eLcF\nwCHbW/MyrwfrYtz0Jtu8Bz3YQZnvVTCCPJyNk21Nq78lZEbTjG3jY1bDITz2\nMIZKdz+DY1FKmpNMsfK4dDmKKJ9NoWu2TFEvLYHWzoj3NfWx+M+DtsamAf4r\nZeEz1Jr2LIHVIgXWdrAwKaEoCReJx7FgY4QC2ACcix8ktodzs0HsCSrpFNho\nG5gFu3jMtgoLed6uJA+hTN5wta3w3VXNHPOOjGLng3XjYg7PpYXcz83FSrS9\n6S7S+e0ZoR0kxmp9NmFrldjx4tbPt7WDtL5joalamZBimVl5VI3WNnoaQ/d7\nYuXL0lC0kFwgfsKZwSUjAWwSRrhdKGOB9rRnHL9pBrWCtp6ldUBi9DV1jLJw\nrKYX3GUUpPUOnvUjfddfz6H0yO9iD9mYjrJwVaiPegX7yVytloI4NpEUixra\ndHYK2XodQnvuoIe64DdCZO2Y5bYC8gn+K/favmS5DoOe4uuzUWN2h7v1cPHR\nyBtROs/s0jBE00eDuI+eeMdBBubSPj14bL2JEW9bHvvL1j+tObfSKG7WQe4p\nItTHECTjgKm8g/fFEe19V8JUPv/S1djYFWM34VRXuw96fJgxr8Y1bQktiUVs\n1pFjlq8KwDk7SLKEp0s13mCWwgAFbCkUqiRUbWaoHP1HWhcl6W8V/t20cJVa\nipx/\r\n=rwCY\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE+Bq3W5+3IXCbMStAf6zH0uV5j/05vW/qr5jSzDt09AAiEA28H/4T1B3Dqj+tOPjbNqsby1nLnB6BMnfYHZM8sAeUw="}]}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/event-target-shim_5.0.1_1550556051907_0.4435255590872793"}, "_hasShrinkwrap": false}, "6.0.0": {"name": "event-target-shim", "version": "6.0.0", "description": "An implementation of WHATWG EventTarget interface.", "main": "index.js", "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./es5": {"import": "./es5.mjs", "require": "./es5.js"}, "./umd": "./umd.js", "./package.json": "./package.json"}, "engines": {"node": ">=10.13.0"}, "scripts": {"build": "run-s \"build:{clean,rollup,dts,meta}\"", "build:clean": "rimraf \"dist/*\"", "build:rollup": "rollup --config scripts/rollup.config.js", "build:dts": "dts-bundle-generator --project tsconfig/dts.json --out-file dist/index.d.ts src/index.ts && dts-bundle-generator --project tsconfig/dts.json --out-file dist/es5.d.ts src/index.ts", "build:meta": "cpx \"{LICENSE,package.json,README.md}\" dist/", "preversion": "npm test", "version": "npm run build", "postversion": "release", "test": "run-s \"test:{clean,tsc,lint,format,mocha}\"", "test:clean": "rimraf \"coverage/*\"", "test:tsc": "tsc -p tsconfig/build.json --noEmit", "test:lint": "eslint .", "test:format": "prettier --check .", "test:mocha": "ts-node scripts/test", "watch:mocha": "mocha --require ts-node/register/transpile-only --extensions ts --watch-files src,test --watch \"test/*.ts\""}, "dependencies": {"@types/rimraf": "^3.0.0", "domexception": "^2.0.1"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-transform-runtime": "^7.12.10", "@babel/preset-env": "^7.12.11", "@mysticatea/eslint-plugin": "^13.0.0", "@mysticatea/spy": "^0.1.2", "@mysticatea/tools": "^0.1.1", "@rollup/plugin-babel": "^5.2.2", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^11.0.1", "@rollup/plugin-typescript": "^8.1.0", "@types/domexception": "^2.0.1", "@types/istanbul-lib-coverage": "^2.0.3", "@types/istanbul-lib-report": "^3.0.0", "@types/istanbul-lib-source-maps": "^4.0.1", "@types/istanbul-reports": "^3.0.0", "@types/mocha": "^8.2.0", "assert": "^2.0.0", "babel-loader": "^8.2.2", "babel-plugin-istanbul": "^6.0.0", "buffer": "^6.0.3", "chalk": "^4.1.0", "codecov": "^3.8.1", "cpx": "^1.5.0", "dts-bundle-generator": "^5.5.0", "eslint": "^7.15.0", "istanbul-lib-coverage": "^3.0.0", "istanbul-lib-report": "^3.0.0", "istanbul-lib-source-maps": "^4.0.0", "istanbul-reports": "^3.0.2", "mocha": "^7.2.0", "npm-run-all": "^4.1.5", "path-browserify": "^1.0.1", "playwright": "^1.7.0", "prettier": "~2.2.1", "process": "^0.11.10", "rimraf": "^3.0.2", "rollup": "^2.35.1", "rollup-plugin-terser": "^7.0.2", "rollup-watch": "^4.3.1", "stream-browserify": "^3.0.0", "ts-loader": "^8.0.12", "ts-node": "^9.1.1", "tslib": "^2.0.3", "typescript": "~4.1.3", "url": "^0.11.0", "util": "^0.12.3", "webpack": "^5.11.0"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/event-target-shim.git"}, "keywords": ["w3c", "whatwg", "eventtarget", "event", "events", "shim"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/event-target-shim/issues"}, "homepage": "https://github.com/mysticatea/event-target-shim", "funding": "https://github.com/sponsors/mysticatea", "sideEffects": false, "unpkg": "umd.js", "readme": "# event-target-shim\n\n[![npm version](https://img.shields.io/npm/v/event-target-shim.svg)](https://www.npmjs.com/package/event-target-shim)\n[![Downloads/month](https://img.shields.io/npm/dm/event-target-shim.svg)](http://www.npmtrends.com/event-target-shim)\n[![Build Status](https://github.com/mysticatea/event-target-shim/workflows/CI/badge.svg)](https://github.com/mysticatea/event-target-shim/actions)\n[![Coverage Status](https://codecov.io/gh/mysticatea/event-target-shim/branch/master/graph/badge.svg)](https://codecov.io/gh/mysticatea/event-target-shim)\n[![Dependency Status](https://david-dm.org/mysticatea/event-target-shim.svg)](https://david-dm.org/mysticatea/event-target-shim)\n\nAn implementation of [WHATWG `EventTarget` interface](https://dom.spec.whatwg.org/#interface-eventtarget) and [WHATWG `Event` interface](https://dom.spec.whatwg.org/#interface-event). This implementation supports constructor, `passive`, `once`, and `signal`.\n\nThis implementation is designed ...\n\n- Working fine on both browsers and Node.js.\n- TypeScript friendly.\n\n## 💿 Installation\n\nUse [npm](https://www.npmjs.com/) or a compatible tool.\n\n```\nnpm install event-target-shim\n```\n\n## 📖 Getting started\n\n```js\nimport { EventTarget, Event } from \"event-target-shim\";\n\n// constructor (was added to the standard on 8 Jul 2017)\nconst myNode = new EventTarget();\n\n// passive flag (was added to the standard on 6 Jan 2016)\nmyNode.addEventListener(\n  \"hello\",\n  (e) => {\n    e.preventDefault(); // ignored and print warning on console.\n  },\n  { passive: true }\n);\n\n// once flag (was added to the standard on 15 Apr 2016)\nmyNode.addEventListener(\"hello\", listener, { once: true });\nmyNode.dispatchEvent(new Event(\"hello\")); // remove the listener after call.\n\n// signal (was added to the standard on 4 Dec 2020)\nconst ac = new AbortController();\nmyNode.addEventListener(\"hello\", listener, { signal: ac.signal });\nac.abort(); // remove the listener.\n```\n\n- For browsers, use a bundler such as [Webpack](https://webpack.js.org/) to bundle.\n  - If you want to support IE11, use `import {} from \"event-target-shim/es5\"` instead. It's a transpiled code by babel. It depends on `@baebl/runtime` package.\n- The `AbortController` class was added to the standard on 14 Jul 2017. If you want the shim of that, use [abort-controller](https://www.npmjs.com/package/abort-controller) package.\n\n## 📚 API Reference\n\nSee [docs/reference.md](docs/reference.md).\n\n## 💥 Migrating to v6\n\nSee [docs/migrating-to-v6.md](docs/migrating-to-v6.md).\n\n## 📰 Changelog\n\nSee [GitHub releases](https://github.com/mysticatea/event-target-shim/releases).\n\n## 🍻 Contributing\n\nContributing is welcome ❤️\n\nPlease use GitHub issues/PRs.\n\n### Development tools\n\n- `npm install` installs dependencies for development.\n- `npm test` runs tests and measures code coverage.\n- `npm run watch:mocha` runs tests on each file change.\n", "readmeFilename": "README.md", "_id": "event-target-shim@6.0.0", "_nodeVersion": "15.2.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-/5kZzzIjSdCo09kObw0T5rP5FyqMiUqIMj0utoRKzr7Qe7t39HCg5ieXfJO2pwTvbrf/0Y0hWeaol26pLkmT/g==", "shasum": "dfadf7a372025d69353e32d2360ac47f8f0d4906", "tarball": "https://registry.npmjs.org/event-target-shim/-/event-target-shim-6.0.0.tgz", "fileCount": 15, "unpackedSize": 369561, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9WGtCRA9TVsSAnZWagAA9/AP/1ncMHK6fE16Q2ZuW5SF\nrLvoR2Ds3SLhp9iqloF97AlFLB0Kx1PUsZ+iK9YrsgetPKMwvspNO8BNZedX\nOKYuquz2TwCppjwQkaBCjmAP/S8ScoFGuclWWl1c8plCK6a5qUIzyXWuapMP\n3OOFGNqXmp73GDfBCsYdEP21gpNeTKJUOeVMmxSlqs6E4azA0oTpLX5f8IIm\nUF52CEHTJIn83m5+pWt5nua5dGjUKngiwhPeolDg0kDWCJ8b8MJaMKUL0XJQ\nFRMpaK4QGsozwR4Pej53hRhp7p1mm/BtQQCEHNXUoI9srGXswDIuh5A+h5QL\nPbsJ0KQ1dJsD4CFp340s7n8C023PO9kHC4HAcYrM3Iwie6WMdel/PRiP+qws\nNxEbu2QBSMDIwOH76JOyXfk6KXo4V2aE3wfNKENgqlC0Fh/MPk4NOKpjVa2J\nNgEG1R+2YrswWX9BJIkPRdX5ztrqB/vUMdMSzQ0wEQR5IoTSVD87QgKJBZv7\nHN8jblawc0ic83cw0HUmL3g3Y/BWKWyAPGDVNQPtL/g3nZ9f5pyURutkln6V\nvByggXfQWBtqx3b6rKK8iCOcf7SiK5VwTha+kdMAHejfN6GLiJ2RziOCrxAP\nHIfUUeuibYxTIJRCE5FreJPdXLnlYeUHAwY5vrFdB1Kk0VJsaAGB2aNurtcT\n75Kd\r\n=esPU\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDTr5vp7snlXwdUwIiDwGsPQXw8VcFWud+uXet1x12z0QIhAPhto7u2WvyndOq+AhOpM9SjUYuIHTrIA0mRuYrx3fEz"}]}, "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/event-target-shim_6.0.0_1609916844648_0.03513029896207409"}, "_hasShrinkwrap": false}, "6.0.1": {"name": "event-target-shim", "version": "6.0.1", "description": "An implementation of WHATWG EventTarget interface.", "main": "index.js", "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./es5": {"import": "./es5.mjs", "require": "./es5.js"}, "./umd": "./umd.js", "./package.json": "./package.json"}, "engines": {"node": ">=10.13.0"}, "scripts": {"build": "run-s \"build:{clean,rollup,dts,meta}\"", "build:clean": "rimraf \"dist/*\"", "build:rollup": "rollup --config scripts/rollup.config.js", "build:dts": "dts-bundle-generator --project tsconfig/dts.json --out-file dist/index.d.ts src/index.ts && dts-bundle-generator --project tsconfig/dts.json --out-file dist/es5.d.ts src/index.ts", "build:meta": "cpx \"{LICENSE,package.json,README.md}\" dist/", "preversion": "npm test", "version": "npm run build", "postversion": "release", "test": "run-s \"test:{clean,tsc,lint,format,mocha}\"", "test:clean": "rimraf \"coverage/*\"", "test:tsc": "tsc -p tsconfig/build.json --noEmit", "test:lint": "eslint .", "test:format": "prettier --check .", "test:mocha": "ts-node scripts/test", "watch:mocha": "mocha --require ts-node/register/transpile-only --extensions ts --watch-files src,test --watch \"test/*.ts\""}, "dependencies": {}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-transform-runtime": "^7.12.10", "@babel/preset-env": "^7.12.11", "@mysticatea/eslint-plugin": "^13.0.0", "@mysticatea/spy": "^0.1.2", "@mysticatea/tools": "^0.1.1", "@rollup/plugin-babel": "^5.2.2", "@rollup/plugin-typescript": "^8.1.0", "@types/istanbul-lib-coverage": "^2.0.3", "@types/istanbul-lib-report": "^3.0.0", "@types/istanbul-lib-source-maps": "^4.0.1", "@types/istanbul-reports": "^3.0.0", "@types/mocha": "^8.2.0", "@types/rimraf": "^3.0.0", "assert": "^2.0.0", "babel-loader": "^8.2.2", "babel-plugin-istanbul": "^6.0.0", "buffer": "^6.0.3", "chalk": "^4.1.0", "codecov": "^3.8.1", "cpx": "^1.5.0", "dts-bundle-generator": "^5.5.0", "eslint": "^7.15.0", "istanbul-lib-coverage": "^3.0.0", "istanbul-lib-report": "^3.0.0", "istanbul-lib-source-maps": "^4.0.0", "istanbul-reports": "^3.0.2", "mocha": "^7.2.0", "npm-run-all": "^4.1.5", "path-browserify": "^1.0.1", "playwright": "^1.7.0", "prettier": "~2.2.1", "process": "^0.11.10", "rimraf": "^3.0.2", "rollup": "^2.35.1", "rollup-plugin-terser": "^7.0.2", "rollup-watch": "^4.3.1", "stream-browserify": "^3.0.0", "ts-loader": "^8.0.12", "ts-node": "^9.1.1", "tslib": "^2.0.3", "typescript": "~4.1.3", "url": "^0.11.0", "util": "^0.12.3", "webpack": "^5.11.0"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/event-target-shim.git"}, "keywords": ["w3c", "whatwg", "eventtarget", "event", "events", "shim"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/event-target-shim/issues"}, "homepage": "https://github.com/mysticatea/event-target-shim", "funding": "https://github.com/sponsors/mysticatea", "sideEffects": false, "unpkg": "umd.js", "readme": "# event-target-shim\n\n[![npm version](https://img.shields.io/npm/v/event-target-shim.svg)](https://www.npmjs.com/package/event-target-shim)\n[![Downloads/month](https://img.shields.io/npm/dm/event-target-shim.svg)](http://www.npmtrends.com/event-target-shim)\n[![Build Status](https://github.com/mysticatea/event-target-shim/workflows/CI/badge.svg)](https://github.com/mysticatea/event-target-shim/actions)\n[![Coverage Status](https://codecov.io/gh/mysticatea/event-target-shim/branch/master/graph/badge.svg)](https://codecov.io/gh/mysticatea/event-target-shim)\n[![Dependency Status](https://david-dm.org/mysticatea/event-target-shim.svg)](https://david-dm.org/mysticatea/event-target-shim)\n\nAn implementation of [WHATWG `EventTarget` interface](https://dom.spec.whatwg.org/#interface-eventtarget) and [WHATWG `Event` interface](https://dom.spec.whatwg.org/#interface-event). This implementation supports constructor, `passive`, `once`, and `signal`.\n\nThis implementation is designed ...\n\n- Working fine on both browsers and Node.js.\n- TypeScript friendly.\n\n## 💿 Installation\n\nUse [npm](https://www.npmjs.com/) or a compatible tool.\n\n```\nnpm install event-target-shim\n```\n\n## 📖 Getting started\n\n```js\nimport { EventTarget, Event } from \"event-target-shim\";\n\n// constructor (was added to the standard on 8 Jul 2017)\nconst myNode = new EventTarget();\n\n// passive flag (was added to the standard on 6 Jan 2016)\nmyNode.addEventListener(\n  \"hello\",\n  (e) => {\n    e.preventDefault(); // ignored and print warning on console.\n  },\n  { passive: true }\n);\n\n// once flag (was added to the standard on 15 Apr 2016)\nmyNode.addEventListener(\"hello\", listener, { once: true });\nmyNode.dispatchEvent(new Event(\"hello\")); // remove the listener after call.\n\n// signal (was added to the standard on 4 Dec 2020)\nconst ac = new AbortController();\nmyNode.addEventListener(\"hello\", listener, { signal: ac.signal });\nac.abort(); // remove the listener.\n```\n\n- For browsers, use a bundler such as [Webpack](https://webpack.js.org/) to bundle.\n  - If you want to support IE11, use `import {} from \"event-target-shim/es5\"` instead. It's a transpiled code by babel. It depends on `@baebl/runtime` (`^7.12.0`) package.\n- The `AbortController` class was added to the standard on 14 Jul 2017. If you want the shim of that, use [abort-controller](https://www.npmjs.com/package/abort-controller) package.\n\n### Runnable Examples\n\n- [Basic Example](https://jsbin.com/dapuwomamo/edit?html,console)\n- [Basic Example (IE11)](https://jsbin.com/fumuhepike/edit?html,console)\n\n## 📚 API Reference\n\nSee [docs/reference.md](docs/reference.md).\n\n## 💥 Migrating to v6\n\nSee [docs/migrating-to-v6.md](docs/migrating-to-v6.md).\n\n## 📰 Changelog\n\nSee [GitHub releases](https://github.com/mysticatea/event-target-shim/releases).\n\n## 🍻 Contributing\n\nContributing is welcome ❤️\n\nPlease use GitHub issues/PRs.\n\n### Development tools\n\n- `npm install` installs dependencies for development.\n- `npm test` runs tests and measures code coverage.\n- `npm run watch:mocha` runs tests on each file change.\n", "readmeFilename": "README.md", "gitHead": "41c25b27495ac79e5183784e8cc39282046b58d9", "_id": "event-target-shim@6.0.1", "_nodeVersion": "15.2.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-M2uk7FC/FANvg2PFEiAuQNpdLDWK0kq9mac94HOnAfa37N3yM7PvcNlRrQWYx6qmXq/lc0e22HkEWKYsr6NWYg==", "shasum": "548f1f2a25b165d92594335a2ec71dfe4cae52d6", "tarball": "https://registry.npmjs.org/event-target-shim/-/event-target-shim-6.0.1.tgz", "fileCount": 3, "unpackedSize": 7584, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9sC1CRA9TVsSAnZWagAADrAP/3H1a29Z5hKgArA2b6/J\nxdjy+zxmqXb8cqZcutKoOWdislrfm1UIGR5wehkHSlu3zpY2ndt8zLPXWq0a\ngWxCnaeThiARMoISxCLKA6g/sPdoIfjsr1usHq0owJ5ayI6kjiLp63YjPUkm\nKnND3VcffeXWVp3FYsySqWiRfnvrrYtPyjzoxWw0dqT+bGp4Q3N0MgwBL2GE\nXIRBTvcf9UiMCKgYCYjd/7uhYSTUKbImOhpp9WU3YV+0q4gd0LALOeLcQhwy\ncFfLVJFmF5E+lfiId67T5N9DZ9k/2C6MfjoGq/oUS6vIakLUIEB/AwKXpi5+\nf2SttC0Y32dTd3N6VbLsWEYb9ZUBaFtoz7xB4W8ok4GdF15NPUiim2+kJ9q4\nNX9aUNv32t6g1+FIgHy3ceI71Mf6NsIKWMcEfbII79Swrn5Dorfg0oUk36Fh\nuvzRTA9BFLp44fOZq7rc28tvNvRQzq+cn1vTPWG9Yiz/xku7kPVE0vrQWWpa\nHmB+wdZ5lUUrfk7+4ZPdv59mh1RXG83+PkiiUmH12/9Sq2Sp+WUEMT9wCx02\n8cGdfUaR294gTInehw3bW8+z7BE9w8JSn0FXBEm7EtcLJkb9m3ikA1bnTbcd\n6pLen9tCBf2cEVpwK/CwXI0R9xCJiKCEfFQbck1rmr1lW98DAKtqYUF0Y/6V\nwEv8\r\n=oVaj\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGlGtFoyIW8dLz5NCOpOIig2J1b2KNIMGwqvCHBwPmtPAiA5LEc82XIBdf6JY8BEHUSLvJzB6CvFe2uDCaA4rKde4w=="}]}, "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/event-target-shim_6.0.1_1610006708676_0.41435068543167874"}, "_hasShrinkwrap": false}, "6.0.2": {"name": "event-target-shim", "version": "6.0.2", "description": "An implementation of WHATWG EventTarget interface.", "main": "index.js", "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./es5": {"import": "./es5.mjs", "require": "./es5.js"}, "./umd": "./umd.js", "./package.json": "./package.json"}, "engines": {"node": ">=10.13.0"}, "scripts": {"build": "run-s \"build:{clean,rollup,dts,meta}\"", "build:clean": "rimraf \"dist/*\"", "build:rollup": "rollup --config scripts/rollup.config.js", "build:dts": "dts-bundle-generator --project tsconfig/dts.json --out-file dist/index.d.ts src/index.ts && dts-bundle-generator --project tsconfig/dts.json --out-file dist/es5.d.ts src/index.ts", "build:meta": "cpx \"{LICENSE,package.json,README.md}\" dist/", "preversion": "npm test", "version": "npm run build", "postversion": "release", "test": "run-s \"test:{clean,tsc,lint,format,mocha}\"", "test:clean": "rimraf \"coverage/*\"", "test:tsc": "tsc -p tsconfig/build.json --noEmit", "test:lint": "eslint .", "test:format": "prettier --check .", "test:mocha": "ts-node scripts/test", "watch:mocha": "mocha --require ts-node/register/transpile-only --extensions ts --watch-files src,test --watch \"test/*.ts\""}, "dependencies": {}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-transform-runtime": "^7.12.10", "@babel/preset-env": "^7.12.11", "@mysticatea/eslint-plugin": "^13.0.0", "@mysticatea/spy": "^0.1.2", "@mysticatea/tools": "^0.1.1", "@rollup/plugin-babel": "^5.2.2", "@rollup/plugin-typescript": "^8.1.0", "@types/istanbul-lib-coverage": "^2.0.3", "@types/istanbul-lib-report": "^3.0.0", "@types/istanbul-lib-source-maps": "^4.0.1", "@types/istanbul-reports": "^3.0.0", "@types/mocha": "^8.2.0", "@types/rimraf": "^3.0.0", "assert": "^2.0.0", "babel-loader": "^8.2.2", "babel-plugin-istanbul": "^6.0.0", "buffer": "^6.0.3", "chalk": "^4.1.0", "codecov": "^3.8.1", "cpx": "^1.5.0", "dts-bundle-generator": "^5.5.0", "eslint": "^7.15.0", "istanbul-lib-coverage": "^3.0.0", "istanbul-lib-report": "^3.0.0", "istanbul-lib-source-maps": "^4.0.0", "istanbul-reports": "^3.0.2", "mocha": "^7.2.0", "npm-run-all": "^4.1.5", "path-browserify": "^1.0.1", "playwright": "^1.7.0", "prettier": "~2.2.1", "process": "^0.11.10", "rimraf": "^3.0.2", "rollup": "^2.35.1", "rollup-plugin-terser": "^7.0.2", "rollup-watch": "^4.3.1", "stream-browserify": "^3.0.0", "ts-loader": "^8.0.12", "ts-node": "^9.1.1", "tslib": "^2.0.3", "typescript": "~4.1.3", "url": "^0.11.0", "util": "^0.12.3", "webpack": "^5.11.0"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/event-target-shim.git"}, "keywords": ["w3c", "whatwg", "eventtarget", "event", "events", "shim"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/event-target-shim/issues"}, "homepage": "https://github.com/mysticatea/event-target-shim", "funding": "https://github.com/sponsors/mysticatea", "sideEffects": false, "unpkg": "umd.js", "readme": "# event-target-shim\n\n[![npm version](https://img.shields.io/npm/v/event-target-shim.svg)](https://www.npmjs.com/package/event-target-shim)\n[![Downloads/month](https://img.shields.io/npm/dm/event-target-shim.svg)](http://www.npmtrends.com/event-target-shim)\n[![Build Status](https://github.com/mysticatea/event-target-shim/workflows/CI/badge.svg)](https://github.com/mysticatea/event-target-shim/actions)\n[![Coverage Status](https://codecov.io/gh/mysticatea/event-target-shim/branch/master/graph/badge.svg)](https://codecov.io/gh/mysticatea/event-target-shim)\n[![Dependency Status](https://david-dm.org/mysticatea/event-target-shim.svg)](https://david-dm.org/mysticatea/event-target-shim)\n\nAn implementation of [WHATWG `EventTarget` interface](https://dom.spec.whatwg.org/#interface-eventtarget) and [WHATWG `Event` interface](https://dom.spec.whatwg.org/#interface-event). This implementation supports constructor, `passive`, `once`, and `signal`.\n\nThis implementation is designed ...\n\n- Working fine on both browsers and Node.js.\n- TypeScript friendly.\n\n## 💿 Installation\n\nUse [npm](https://www.npmjs.com/) or a compatible tool.\n\n```\nnpm install event-target-shim\n```\n\n## 📖 Getting started\n\n```js\nimport { EventTarget, Event } from \"event-target-shim\";\n\n// constructor (was added to the standard on 8 Jul 2017)\nconst myNode = new EventTarget();\n\n// passive flag (was added to the standard on 6 Jan 2016)\nmyNode.addEventListener(\n  \"hello\",\n  (e) => {\n    e.preventDefault(); // ignored and print warning on console.\n  },\n  { passive: true }\n);\n\n// once flag (was added to the standard on 15 Apr 2016)\nmyNode.addEventListener(\"hello\", listener, { once: true });\nmyNode.dispatchEvent(new Event(\"hello\")); // remove the listener after call.\n\n// signal (was added to the standard on 4 Dec 2020)\nconst ac = new AbortController();\nmyNode.addEventListener(\"hello\", listener, { signal: ac.signal });\nac.abort(); // remove the listener.\n```\n\n- For browsers, use a bundler such as [Webpack](https://webpack.js.org/) to bundle.\n  - If you want to support IE11, use `import {} from \"event-target-shim/es5\"` instead. It's a transpiled code by babel. It depends on `@baebl/runtime` (`^7.12.0`) package.\n- The `AbortController` class was added to the standard on 14 Jul 2017. If you want the shim of that, use [abort-controller](https://www.npmjs.com/package/abort-controller) package.\n\n## 📚 API Reference\n\nSee [docs/reference.md](docs/reference.md).\n\n## 💥 Migrating to v6\n\nSee [docs/migrating-to-v6.md](docs/migrating-to-v6.md).\n\n## 📰 Changelog\n\nSee [GitHub releases](https://github.com/mysticatea/event-target-shim/releases).\n\n## 🍻 Contributing\n\nContributing is welcome ❤️\n\nPlease use GitHub issues/PRs.\n\n### Development tools\n\n- `npm install` installs dependencies for development.\n- `npm test` runs tests and measures code coverage.\n- `npm run watch:mocha` runs tests on each file change.\n", "readmeFilename": "README.md", "_id": "event-target-shim@6.0.2", "_nodeVersion": "15.2.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-8q3LsZjRezbFZ2PN+uP+Q7pnHUMmAOziU2vA2OwoFaKIXxlxl38IylhSSgUorWu/rf4er67w0ikBqjBFk/pomA==", "shasum": "ea5348c3618ee8b62ff1d344f01908ee2b8a2b71", "tarball": "https://registry.npmjs.org/event-target-shim/-/event-target-shim-6.0.2.tgz", "fileCount": 12, "unpackedSize": 390440, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9sIhCRA9TVsSAnZWagAAumMQAJ4iL3VmIyJnmpb81QAW\ne8AMkntVnaCtE7WetZvrdw8bS/yrl4cx9jFwvCxfNRmq9PpEW7mXG5YLWP4J\nkLfn7i0KOx7dpWjroE2u2OMlvj0flU09fYEnsFPxek7pLYAHJquAMIUuaT/f\niBmxP6CCHHyXwXlZ2pAnIko7Hk62Ua8pZDjiRxMEQv6AFPk0AvtlY+6Jn6xr\npKdtIicyVpcnwWCt9wghVQFfXx/YyH14ivMpcKiOHz17keA0mUjVrQq4FlMe\nRPUKUPWIQuFGWukiF05dIQXSYpMvFy2hky2azxxCxBWijANK4QdBmex2+moe\nT8H8ljZcaJpsJW4m+VIrIZzarL0LzrIu0VIImRcM3wXnvDJmpDlZNNp9l8GQ\nCCKVcoiBMzrmndrG2i3b1k8aHYfUVAhZfTh8oN4g8OFpsRhekd/IWDZMihkm\nOak+BIUhH1YMcvdPs8OODtRmon1DRbjqzqf2QXi8Iq9PQxGEMW6fxzxbhyEr\nB0NgKtLasfEHY1HS1Q4t24pE//oNVsdvoDSqiWpS8D4SrseAV+7+eNCWPWQS\nebdjNA9P44IQXUXP0AKWSO2o4IOcHf5ooT5HMqFmARHx2W+BdZjm42ho08d0\nMXv2rKD0Nrlt7w3kA+bGUtxoegUQeTGvNpmldlkRMn5JOKDIk03wvc61AhKU\nyo80\r\n=soFT\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHPJDcE/kIj5yiwrfiqF/Esym571BO2G8Z7n0CszwTuWAiB3Wu/zZDxs3dlmO1uuXNUWH1QdUEM3NFbe3o/M+F8YLQ=="}]}, "_npmUser": {"name": "mysticatea", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/event-target-shim_6.0.2_1610007073080_0.043426754362076325"}, "_hasShrinkwrap": false}}, "readme": "", "maintainers": [{"name": "mysticatea", "email": "<EMAIL>"}], "time": {"modified": "2022-06-17T21:29:41.407Z", "created": "2015-02-22T03:01:19.979Z", "0.0.1": "2015-02-22T03:01:19.979Z", "0.1.0": "2015-02-26T05:48:48.933Z", "0.1.1": "2015-02-28T14:41:36.746Z", "1.0.0": "2015-03-25T14:42:36.969Z", "1.0.1": "2015-03-27T11:51:08.927Z", "1.0.2": "2015-03-30T22:12:59.851Z", "1.0.3": "2015-04-04T22:29:52.369Z", "1.0.4": "2015-04-19T01:35:45.734Z", "1.0.5": "2015-05-27T14:24:04.822Z", "1.0.6": "2015-11-30T04:34:12.355Z", "1.0.7": "2015-11-30T11:04:47.550Z", "1.1.0": "2015-12-08T06:11:53.906Z", "1.1.1": "2015-12-17T12:03:15.914Z", "2.0.0": "2016-11-26T16:50:33.401Z", "3.0.0": "2017-10-02T04:13:00.805Z", "3.0.1": "2018-01-23T11:59:14.806Z", "3.0.2": "2018-06-26T09:30:40.958Z", "4.0.0": "2018-12-01T13:41:55.236Z", "4.0.1": "2018-12-01T14:42:22.213Z", "4.0.2": "2018-12-04T11:34:44.104Z", "4.0.3": "2018-12-05T02:22:17.690Z", "5.0.0-beta.0": "2019-01-18T12:37:10.557Z", "5.0.0": "2019-01-21T07:21:20.280Z", "5.0.1": "2019-02-19T06:00:52.075Z", "6.0.0": "2021-01-06T07:07:24.847Z", "6.0.1": "2021-01-07T08:05:08.800Z", "6.0.2": "2021-01-07T08:11:13.294Z"}, "homepage": "https://github.com/mysticatea/event-target-shim", "keywords": ["w3c", "whatwg", "eventtarget", "event", "events", "shim"], "repository": {"type": "git", "url": "git+https://github.com/mysticatea/event-target-shim.git"}, "author": {"name": "<PERSON><PERSON>"}, "bugs": {"url": "https://github.com/mysticatea/event-target-shim/issues"}, "license": "MIT", "readmeFilename": "", "users": {"j.su": true}}