{"_id": "which-typed-array", "_rev": "28-156d66d7d23c75ca50e96d5ff7430ee2", "name": "which-typed-array", "dist-tags": {"latest": "1.1.18"}, "versions": {"1.0.0": {"name": "which-typed-array", "version": "1.0.0", "keywords": ["array", "TypedArray", "typed array", "which", "typed", "Int8Array", "Uint8Array", "Uint8ClampedArray", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array", "ES6", "toStringTag", "Symbol.toStringTag", "@@toStringTag"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "which-typed-array@1.0.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/which-typed-array#readme", "bugs": {"url": "https://github.com/ljharb/which-typed-array/issues"}, "dist": {"shasum": "a1a0d4c513ff028e046682096504220ef01d87e3", "tarball": "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.0.0.tgz", "integrity": "sha512-WEvvXQ6Afq6VwLA1lhYxaLjnV3nEzkAzgBwp8Sn68foRbywlhN6alaLqXcAYP1DDP/ouuK1mjceYxSjNOR3poQ==", "signatures": [{"sig": "MEUCIQDMgzq7v7PQZeqIIDXrZmdlMEbg4WSMQwkOwbYcsdOu3QIgKpAFq/lly582pM3IqnQlSw/qlFK2TMbQD5jX4K9+5wk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "a1a0d4c513ff028e046682096504220ef01d87e3", "engines": {"node": ">= 0.4"}, "gitHead": "4794cb1899d6b3a84770b486a4323b9d6c3fe716", "scripts": {"jscs": "jscs *.js", "lint": "npm run jscs && npm run eslint", "test": "npm run lint && npm run tests-only && npm run security", "eslint": "eslint *.js", "coverage": "covert test.js", "security": "nsp package", "tests-only": "node --es-staging test.js", "coverage-quiet": "covert test.js --quiet"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/which-typed-array.git", "type": "git"}, "_npmVersion": "2.14.4", "description": "Which kind of Typed Array is this JavaScript value? Works cross-realm, without `instanceof`, and despite Symbol.toStringTag.", "directories": {}, "_nodeVersion": "4.1.2", "dependencies": {"function-bind": "^1.0.2", "is-typed-array": "^1.0.2"}, "devDependencies": {"nsp": "^1.1.0", "jscs": "^2.2.1", "tape": "^4.2.1", "covert": "^1.1.0", "eslint": "^1.6.0", "semver": "^5.0.3", "foreach": "^2.0.5", "replace": "^0.3.0", "is-callable": "^1.1.0", "make-arrow-function": "^1.1.0", "@ljharb/eslint-config": "^1.3.0", "make-generator-function": "^1.1.0"}}, "1.0.1": {"name": "which-typed-array", "version": "1.0.1", "keywords": ["array", "TypedArray", "typed array", "which", "typed", "Int8Array", "Uint8Array", "Uint8ClampedArray", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array", "ES6", "toStringTag", "Symbol.toStringTag", "@@toStringTag"], "author": {"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "which-typed-array@1.0.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/which-typed-array#readme", "bugs": {"url": "https://github.com/ljharb/which-typed-array/issues"}, "dist": {"shasum": "80c3ec320e843a113060cabc1000620d822f73ac", "tarball": "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.0.1.tgz", "integrity": "sha512-cUtYy477AmyoxmZPbI4Qyrq0s6RueGOMpscmfslwdw4+qPQSH6NipGjfRnyxFgfXd0b2JXDvY9F8iPgScFfBkA==", "signatures": [{"sig": "MEUCIQDAx2v9WVbMxNcb94vVSO2MdW+Svt9ffSRGjLtbKPa22wIgScdbrsui/buwYdBr9TuCSbBv+ReI0w0ktzDJpJGtY/o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "80c3ec320e843a113060cabc1000620d822f73ac", "engines": {"node": ">= 0.4"}, "gitHead": "7b8052a28777524283c591d8c49a3d241928523e", "scripts": {"jscs": "jscs *.js", "lint": "npm run --silent jscs && npm run --silent eslint", "test": "npm run --silent tests-only", "eslint": "eslint *.js", "pretest": "npm run --silent lint", "coverage": "covert test.js", "posttest": "npm run --silent security", "security": "nsp check", "tests-only": "node --es-staging test.js", "coverage-quiet": "covert test.js --quiet"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/which-typed-array.git", "type": "git"}, "_npmVersion": "3.7.3", "description": "Which kind of Typed Array is this JavaScript value? Works cross-realm, without `instanceof`, and despite Symbol.toStringTag.", "directories": {}, "_nodeVersion": "5.9.0", "dependencies": {"function-bind": "^1.1.0", "is-typed-array": "^1.0.4"}, "devDependencies": {"nsp": "^2.2.1", "jscs": "^2.11.0", "tape": "^4.5.1", "covert": "^1.1.0", "eslint": "^2.4.0", "semver": "^5.1.0", "foreach": "^2.0.5", "replace": "^0.3.0", "is-callable": "^1.1.3", "make-arrow-function": "^1.1.0", "@ljharb/eslint-config": "^2.1.1", "make-generator-function": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/which-typed-array-1.0.1.tgz_1458435199973_0.3849983694963157", "host": "packages-12-west.internal.npmjs.com"}}, "1.1.0": {"name": "which-typed-array", "version": "1.1.0", "keywords": ["array", "TypedArray", "typed array", "which", "typed", "Int8Array", "Uint8Array", "Uint8ClampedArray", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array", "ES6", "toStringTag", "Symbol.toStringTag", "@@toStringTag"], "author": {"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "which-typed-array@1.1.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/which-typed-array#readme", "bugs": {"url": "https://github.com/ljharb/which-typed-array/issues"}, "dist": {"shasum": "98c9efafc484d049f9398b38139e0ea2eeebcea5", "tarball": "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.0.tgz", "fileCount": 10, "integrity": "sha512-k7Xs1zymsr1oTmXdv1EStHlyBwIUWTl+OKh2WZlSOGXUjllS4Ujcv3+v6oYu6Pvd4/wRSF7JH/gusCcRWFukNA==", "signatures": [{"sig": "MEYCIQCqWbH98qSMAnNkErIR0iz+oON21pCvEMwoURUtmbMHNQIhAK3LzpG+EakjupstT5i2jmDTXyExsVnhZa6B0I3W0JOI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24584, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcaFAqCRA9TVsSAnZWagAAv3kP/3SsHWUuSPQVxwxfi+hi\njjILx8QCzjDTG6yEmnOEk/eW0Z0YyCoc64FMP1PPVqL3caYhHKPI8XeGhW2d\ntlS+1JApbu27kx9JaP44njGSr8r06iT1guPAsu8ihTh9QIWHIxJ36LhhCTWl\nH/bOxnCVZqSFu+kH6s4cV6ysCwr2/1LqoVFzh3Nw2U/eVvPwB2jJNK6TCOgo\nn5nfgZl2kw2cPygU80W5JeZ+av7oPuok/bcgO0G5RFAQGjBOvgBLe0JUb09n\nOg3/r3xDPfdGz16ySF4fHNM2rw0tjNP0r0HHMAh7VUsWgm2zBPvjVzB4JmFu\nAmDZXJ42q3tqaEorlYr4yEJUyvOmDNzSdbJltcaRUlzX628iX6as1XDpKhCI\nmTh/XEYXhdH8hXIVslv4RnuTMB5eMFzg7KrWLUBVQ7RuYR4+QnqPOiOeXuf1\n8IadmFt1YlfN42dFgUjiIPFl550hrzRnTv+LDQ5tzTkXrE3/P2eZsgWyDA3/\nLBl0q4vS/0VwUmwIHvt1m+xSqR5HQ2rUYrdfff+GTU4xJyioXqQ2UKZlAo/6\n9EOsruCVL5LhVjFb1VC8/A3+Dq5cr8Quiolgszq1IXCxo8ikcgTjizQSXU8B\nAXoZ8oBcfMaDa6k2OBYs27SN/opOJ+yD+PaGOgnBOK70pLLIyx1h0gcHR0jN\nMKTo\r\n=PCLx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "gitHead": "a9b57efcf2eff3e3dbb8caaa0cfec7fb869f424b", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "audit": "npm audit", "pretest": "npm run --silent lint", "coverage": "covert test.js", "posttest": "npm run audit", "preaudit": "npm install --package-lock --package-lock-only", "postaudit": "rm package-lock.json", "tests-only": "node --es-staging test.js", "coverage-quiet": "covert test.js --quiet"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/which-typed-array.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "Which kind of Typed Array is this JavaScript value? Works cross-realm, without `instanceof`, and despite Symbol.toStringTag.", "directories": {}, "_nodeVersion": "11.10.0", "dependencies": {"function-bind": "^1.1.1", "is-typed-array": "^1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.10.1", "covert": "^1.1.1", "eslint": "^5.14.0", "semver": "^5.6.0", "foreach": "^2.0.5", "replace": "^1.0.1", "is-callable": "^1.1.4", "make-arrow-function": "^1.1.0", "@ljharb/eslint-config": "^13.1.1", "make-generator-function": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/which-typed-array_1.1.0_1550340137858_0.08314622131208194", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "which-typed-array", "version": "1.1.1", "keywords": ["array", "TypedArray", "typed array", "which", "typed", "Int8Array", "Uint8Array", "Uint8ClampedArray", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array", "ES6", "toStringTag", "Symbol.toStringTag", "@@toStringTag"], "author": {"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "which-typed-array@1.1.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/which-typed-array#readme", "bugs": {"url": "https://github.com/inspect-js/which-typed-array/issues"}, "dist": {"shasum": "bff075fd975faedad9ed9355ee0d15452d068794", "tarball": "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.1.tgz", "fileCount": 10, "integrity": "sha512-IWlkoJZ4Zdfi4YBn2wnYB8AVox+4A2+Kmr4kHFAraffHYrQZFiTOjgjk9et8e6RSPZOV1QjZOC51PVCK9SkR/A==", "signatures": [{"sig": "MEUCIQCmRZfd03KKdTL+gyYqEJAi4pnVjrONVAyMdUcOTWptBQIga8qUojQB7WR/eCPTQ/HW8/XaLhOxKAQyN/tWVXN7U1U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23511, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeK1dLCRA9TVsSAnZWagAAfSwP+gJcReHByDZvhrb8QPQU\n9Dsdz1jyORox0gSa2kyUfH9TG+Op/ysuQj6rGg37r0ovbv/kZzAo1+OxiwhV\n4AXLPUB/QvSBlHx0UFBPt8gjpSS0i6NQ0ZOAzLC4qtYRNHXaNREUfu1iZrn1\nb2BIwpcqB7itC7EeuRJZK88SacIZw5c3bULMtyZg3UdWQwtt0EKhuk6Z5Ujf\nmNwrJMYmn0y16WPKHGiq7bjVooIMmdh04uVgCR69msQN+AnCBcPbrMj8kdRD\ndvthPC1qLM3ZAfJh0MQGIstFt92zJ3AVuJNsnRWPyR5z18QUKBLunj7xljtW\nXfujAu1kHpP4UccFx/zRrcxRpZFjEHDuUU523eFRvjmhvLGNGEUpvbZTYW0O\nRjUfi8MXELb2gZAHhroDebGaf+p1r6d3529piLOJWmMD/U6EqFCA6jNtFyNT\nMxmAUyegMngQYPUcLS8L8f5WlFs7nFtYQSJT1UrupTiL8upvd00tXzT8Ny12\n+beDycjhWO/1E17BN+OgXu3O4rxZVSOUidGqxUQOppcZOdj+cDyhfj5SpKg6\nm/WqEdTjmyZhKlhFCBeGZHwAsiRz4qWc5/i/sAD0NShiPDHWaJ04dRCzxQje\n0gj0kOilIjuvsobdZP+f3Q+XkIba48/rVpZ+hKrMJP/Coar+JzEI200f7/6g\nL2XG\r\n=4Hu4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "b375f4b189827beeadb355b96fba9aad017a5df5", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "pretest": "npm run --silent lint", "version": "auto-changelog && git add CHANGELOG.md", "coverage": "covert test/index.js", "posttest": "npx aud", "prepublish": "safe-publish-latest", "tests-only": "node --es-staging test", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/inspect-js/which-typed-array.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "Which kind of Typed Array is this JavaScript value? Works cross-realm, without `instanceof`, and despite Symbol.toStringTag.", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"es-abstract": "^1.17.4", "has-symbols": "^1.0.1", "function-bind": "^1.1.1", "is-typed-array": "^1.1.3", "available-typed-arrays": "^1.0.1"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"tape": "^5.0.0-next.4", "covert": "^1.1.1", "eslint": "^6.8.0", "foreach": "^2.0.5", "is-callable": "^1.1.5", "auto-changelog": "^1.16.2", "make-arrow-function": "^1.1.0", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^15.1.0", "make-generator-function": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/which-typed-array_1.1.1_1579898698938_0.6189814703216332", "host": "s3://npm-registry-packages"}}, "1.1.2": {"name": "which-typed-array", "version": "1.1.2", "keywords": ["array", "TypedArray", "typed array", "which", "typed", "Int8Array", "Uint8Array", "Uint8ClampedArray", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array", "ES6", "toStringTag", "Symbol.toStringTag", "@@toStringTag"], "author": {"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "which-typed-array@1.1.2", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/which-typed-array#readme", "bugs": {"url": "https://github.com/inspect-js/which-typed-array/issues"}, "dist": {"shasum": "e5f98e56bda93e3dac196b01d47c1156679c00b2", "tarball": "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.2.tgz", "fileCount": 11, "integrity": "sha512-KT6okrd1tE6JdZAy3o2VhMoYPh3+J6EMZLyrxBQsZflI1QCZIxMrIYLkosd8Twf+YfknVIHmYQPgJt238p8dnQ==", "signatures": [{"sig": "MEQCICVNYXqxVsEmVgJS9HGiTi8LUiyFWh7R/kkmBk8dAPq5AiBrX5r5n6B/NHWyhkGNm1R1IhXSEcZB4YLC9Sh3FiLBAA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25274, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJejVt/CRA9TVsSAnZWagAAQCYP/RujrpjMtzAg0g/eFLew\n25+jxt7aDe0/s6wBeSJ0POLPac1RT7L/ixgVLHw3nxq4Axk2aRIubQe1uN4r\nN+ASWycP/yvpwh1CYQkUpPm3+jjUPsd7z18Id6lvifIFUf++aJ117WbraQrS\nwMUqkaNXaN0FN85o8fmh/jpB6NTScAPhxU9VoPd80ql5ip7NAxsxPQ83/S4A\nXjogW9lNS6f6V6NvE9+WuX1/hYDNTk6WShwARC7cr34Y7fiVGvO8S9K6mI+o\nDz2OxbTWmwVC1KVzPacRNLne8j5FUcU8/OVr/Eu3NswcQsOdFwpy4pWVMxHb\n1olamJD0MmNcyVIiqP6ByZttK4QyP45rX2SUvdde9XWB0TCJd0MOIxrz+aqV\nZqzQ3kphQFzry8Ez0GRovv6lQVOClNJ+eKkhdTY8fI5FwUgu/Gcz6r4kWVY/\n20qJG0Ymogc1s081Oz3KqscNh9EQZPkbK5IKmF6weazOtU+0ygDtj4IbfSXO\nkzWoR3RQwtFQAEbl+XBMyxxYqTVzHaboyWSqj2phlmgoS7hG8nju0vH8wfLg\nNmvzHW2o2ctCnGpNz0kpoxjIrLse6EX/UjPNiCUrO5TAXAdmilwFUrEIILLX\nQwCoSWoywU6+Ctrxt0pqQHxlXmCwDGyZsGr/H32njNnr5SKn8TwR5mb2io9l\n2D1r\r\n=IdX/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "3b299771c6de392c2ea7a0a275865a5cf0f5ff50", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "pretest": "npm run --silent lint", "version": "auto-changelog && git add CHANGELOG.md", "coverage": "covert test/index.js", "posttest": "npx aud --production", "prepublish": "safe-publish-latest", "tests-only": "node --es-staging test", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/inspect-js/which-typed-array.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Which kind of Typed Array is this JavaScript value? Works cross-realm, without `instanceof`, and despite Symbol.toStringTag.", "directories": {}, "_nodeVersion": "13.12.0", "dependencies": {"foreach": "^2.0.5", "es-abstract": "^1.17.5", "has-symbols": "^1.0.1", "function-bind": "^1.1.1", "is-typed-array": "^1.1.3", "available-typed-arrays": "^1.0.2"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"tape": "^5.0.0-next.5", "covert": "^1.1.1", "eslint": "^6.8.0", "is-callable": "^1.1.5", "auto-changelog": "^1.16.4", "make-arrow-function": "^1.2.0", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^16.0.0", "make-generator-function": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/which-typed-array_1.1.2_1586322302964_0.41407769495463453", "host": "s3://npm-registry-packages"}}, "1.1.3": {"name": "which-typed-array", "version": "1.1.3", "keywords": ["array", "TypedArray", "typed array", "which", "typed", "Int8Array", "Uint8Array", "Uint8ClampedArray", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array", "ES6", "toStringTag", "Symbol.toStringTag", "@@toStringTag"], "author": {"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "which-typed-array@1.1.3", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/which-typed-array#readme", "bugs": {"url": "https://github.com/inspect-js/which-typed-array/issues"}, "dist": {"shasum": "d0d04b8192a6e98d1f768b7b4a82edf3a2b905c4", "tarball": "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.3.tgz", "fileCount": 20, "integrity": "sha512-Jj0y6bBs37yegsn4321P5qOZnXxB0bJynOven/MFMpE9Up/+Ituy93ZHWbyVPs9ft5bnHQb+8+hSkaJVRS83cw==", "signatures": [{"sig": "MEUCIGBqCALgBGj82lnFYbWHruXgP3mvqyR9wybXSvE9EBvIAiEAuUw1ZRvB1uKoRpsr/Ejp1r+RPGppR6h5E7/BIczta0c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46713, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfy/Z/CRA9TVsSAnZWagAAaTIP/19eWVH229qY0iwHznTH\nHIN5tqfAsKpwaAlPZxEy6BXP8avzQwGrX2sV2B/IFt0ExoGCkfudT3PSCom8\nXTZSFtFx//Y79cGL/Ap5GqTY1TGv7UaYhAxkb8ddS2/ia4Q07zaiSgoVpuep\n+4Y5pJkkjDWA5mqKGOWlRYabIPE/O+5HvTuXdAMo9Ell5GfJpB6SEHfZC8mQ\nzN+OCG6ya0gdP5bScuYyPEGTFcKiWl65FiwhFPN2EU1keN4vLuGO+mOwIxyS\njBaV435NDEcj6mvhfB6DRk9tmbGc0CK5JoQZVlql2iX7Z/dg4+shgcI/TNxQ\nxmhdJLw6ollw5vh5umxDXD9/oUdtk/4aeQGvdS8l+s35h0P4SM+dLH4EaE+7\n71YDisSpn1gIGVL1bndq6UcNYGgo8M6mGjaGjBWb5a11N5wtnSKoi7pAWyO9\nXO3b+M4eQ/xfgb8c2jQEdTme9YgcK6JSKxjl1B8bjNATHOJo99sak2DcV4mE\nxE3e33EDXdcrlGgSqEOhRf5ad9Z5Xx4FOogSm0KnNPaQnnfHUTZW+Wltl+c6\nbAasU7nLuaxxzUhew+2nVNgPrRZX473ySe4RXMAQ8uONENN6va3tnCzY6ki7\n1nDDmhceNVwpqCPWbyb2TOZEPi8UQyeAdegRczja9iaK5bMLAd5NDB84Y1/X\npD1+\r\n=MPZQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "c40eaf50bc7e2389670f9491f37e58b64c1ab32b", "scripts": {"lint": "eslint .", "test": "npm run tests-only && npm run test:harmony", "pretest": "npm run --silent lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "npx aud --production", "prepublish": "safe-publish-latest", "tests-only": "nyc tape test", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "test:harmony": "nyc node --harmony --es-staging test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/inspect-js/which-typed-array.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Which kind of Typed Array is this JavaScript value? Works cross-realm, without `instanceof`, and despite Symbol.toStringTag.", "directories": {}, "_nodeVersion": "14.15.1", "dependencies": {"foreach": "^2.0.5", "call-bind": "^1.0.0", "es-abstract": "^1.18.0-next.1", "has-symbols": "^1.0.1", "function-bind": "^1.1.1", "is-typed-array": "^1.1.3", "available-typed-arrays": "^1.0.2"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^1.1.3", "nyc": "^10.3.2", "tape": "^5.0.1", "eslint": "^7.15.0", "is-callable": "^1.2.2", "auto-changelog": "^2.2.1", "make-arrow-function": "^1.2.0", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.3.0", "make-generator-function": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/which-typed-array_1.1.3_1607202431240_0.1497394773296954", "host": "s3://npm-registry-packages"}}, "1.1.4": {"name": "which-typed-array", "version": "1.1.4", "keywords": ["array", "TypedArray", "typed array", "which", "typed", "Int8Array", "Uint8Array", "Uint8ClampedArray", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array", "ES6", "toStringTag", "Symbol.toStringTag", "@@toStringTag"], "author": {"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "which-typed-array@1.1.4", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/which-typed-array#readme", "bugs": {"url": "https://github.com/inspect-js/which-typed-array/issues"}, "dist": {"shasum": "8fcb7d3ee5adf2d771066fba7cf37e32fe8711ff", "tarball": "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.4.tgz", "fileCount": 11, "integrity": "sha512-49E0SpUe90cjpoc7BOJwyPHRqSAd12c10Qm2amdEZrJPCY2NDxaW01zHITrem+rnETY3dwrbH3UUrUwagfCYDA==", "signatures": [{"sig": "MEUCIQDHYxNj2v5N3/LueFmcJO5PRLa3gvH3DJR6fVPmB5Tt+gIgWgoLjjxCXqZ129aj17OujMVh9kqNuj3M9HuDMD26vX4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26749, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfy/cbCRA9TVsSAnZWagAAM/kP/jJhXGzxr77qM8kO8UI8\n9In/ipBDXADj8tqTrXYylizNqLmPudTbVv0/B4TltW8WCUFGEbRHn6KLphrn\n895BvuFi+phmmWsg1bEFw/lESXEdy8fw1hOxe4/u07Y4gyWjxPXMLf6IJ8NT\n3awZUx3xTCuuE2FCEOZ4yaZpeEb3RcG00Zhc+RW3VQ/1r8wO+fGsC/71KpB+\n07pLRDK9ob/ZtMHW1wW+OJGl3om+SKKpRI9alkhO6rY2D2X0tSluqIJ96qwe\nzn8GQD68rCqq0T5r6xZ81RyjlDniBGDnz+jJHOnqzjYDMJP5xC+kN8LcVW7d\nRuck197Hdm0hnOD5B5JEznKBcA3mDvf6NQeNsGhOXDeb+qyVmMzkxgf9FMUZ\nfv5e+a+PZDwERlf6TTXHeUfnDzyRNZ77ILRjQmqgsm9jQIwY6Yq0wOdTHmOf\nhd19E3QpLA2IpI97TejshcIxkHR7mD+5M6vdPJXwNEqP3+hhAwjxf53YJUY6\nyuMywL3TnmgXG5THNBEvF9wLI9bQ487G2hXvw2b/3dLAplguazcukVwowN/s\nhfV7F0zz2F5/eiut8H+nH6Ts1LPy/B0qxzDCBI6y3eOIdLIZvhV3YjtB8rNX\n83eeBrnBS934g56ESzrQGgN06dG11yxukkprJCs/JmDlZ2kyeWeTOwICzkZC\ny85G\r\n=XZU9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "76f6f982f575640c8d46083ab9da037857877676", "scripts": {"lint": "eslint .", "test": "npm run tests-only && npm run test:harmony", "pretest": "npm run --silent lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "npx aud --production", "prepublish": "safe-publish-latest", "tests-only": "nyc tape test", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "test:harmony": "nyc node --harmony --es-staging test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/inspect-js/which-typed-array.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Which kind of Typed Array is this JavaScript value? Works cross-realm, without `instanceof`, and despite Symbol.toStringTag.", "directories": {}, "_nodeVersion": "14.15.1", "dependencies": {"foreach": "^2.0.5", "call-bind": "^1.0.0", "es-abstract": "^1.18.0-next.1", "has-symbols": "^1.0.1", "function-bind": "^1.1.1", "is-typed-array": "^1.1.3", "available-typed-arrays": "^1.0.2"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^1.1.3", "nyc": "^10.3.2", "tape": "^5.0.1", "eslint": "^7.15.0", "is-callable": "^1.2.2", "auto-changelog": "^2.2.1", "make-arrow-function": "^1.2.0", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.3.0", "make-generator-function": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/which-typed-array_1.1.4_1607202587398_0.9204380955615525", "host": "s3://npm-registry-packages"}}, "1.1.5": {"name": "which-typed-array", "version": "1.1.5", "keywords": ["array", "TypedArray", "typed array", "which", "typed", "Int8Array", "Uint8Array", "Uint8ClampedArray", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array", "ES6", "toStringTag", "Symbol.toStringTag", "@@toStringTag"], "author": {"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "which-typed-array@1.1.5", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/which-typed-array#readme", "bugs": {"url": "https://github.com/inspect-js/which-typed-array/issues"}, "dist": {"shasum": "c41c369c37484b4f6a4304fb415f3ad8191069be", "tarball": "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.5.tgz", "fileCount": 11, "integrity": "sha512-ib2f4KSZPjFfV1g+Up/whdhp9yWhsf1BSoLrPdkAJwvLRl0EYg9CvT6kmPPn6nft0OT/NgmWA/KdUcYZadopeQ==", "signatures": [{"sig": "MEUCIQCclarocZ7X1o3vHryqwefAzF9F0EN824BSnvrmkT13EAIgY2ZT9irSc3ixkXCNFhIb/HHI0QD05zfRrhPnptUI6l0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27752, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhDE1YCRA9TVsSAnZWagAA0mwP/jL076GIra4w9WG4oMC2\n54HDYl4LgjyDmee5pMbxPxC9scD1kwraf/1TA0ZAQpwKjLR/d9ujFnbhRF2/\nIF7DHlwzfDGL5c/1XGfZE/OP5NPgfWaq17mvf/aTATjkX7tTuQ7Ix96BbBZc\n+XCUwlEvAJUeusXYa6TMTCGFDUXLKyoDd1eem6YcaPXwCbJXbNHAmcFhIVD1\n2D5GmnlozCb5q2eCr2XhZhBQH+18UTS28NL1iPQIortoY1ieaTwPg8nDS0qR\numn52OxPOrgV58FsL/4vfiJyrSQti/AhUKIanOE1xKkOiOyTGrZozQ7ZiwpC\ntUM+Y4wLR3SClCCczq4mZLfRmDRK1N43+/v0nExRFFayqJloR9jVklWheN5A\nAUCTe0QfPxAhYgZ93RcvdL6h9ba7qJFcazRtMOfk81cgUlzp00XhBbv9uQI9\nmX7XWpAt5e0WiSwFV/KIl9kXK2SXOn0NVyhozDSwN24cDfKvUSzwpyKJcaqI\n2DGwNUmCiNaTa6+Mylp1oKr2ZJad3thul1lMPU+Y0qTH36nYu+QqnB8ZgpCF\nxvfl/JdHTP9X1V6sEFLhhOOU+WC7GXEvObpegWdMezsw5YUwa/JMizPSYZst\nXqBjhotlaZOW7g28nHJVAsUQ1j+ZLTelxiHbmFDtdXqYCMCzRjEUw3bUoMzS\nzFZm\r\n=QrIi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "3c50a2f31dc43db2666f8070d6eeceffa706cd65", "scripts": {"lint": "eslint .", "test": "npm run tests-only && npm run test:harmony", "pretest": "npm run --silent lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "npx aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape test", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "test:harmony": "nyc node --harmony --es-staging test", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/inspect-js/which-typed-array.git", "type": "git"}, "_npmVersion": "7.19.1", "description": "Which kind of Typed Array is this JavaScript value? Works cross-realm, without `instanceof`, and despite Symbol.toStringTag.", "directories": {}, "_nodeVersion": "16.6.0", "dependencies": {"foreach": "^2.0.5", "call-bind": "^1.0.2", "es-abstract": "^1.18.5", "is-typed-array": "^1.1.5", "has-tostringtag": "^1.0.0", "available-typed-arrays": "^1.0.4"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^1.1.5", "nyc": "^10.3.2", "tape": "^5.3.0", "eslint": "^7.32.0", "in-publish": "^2.0.1", "is-callable": "^1.2.3", "auto-changelog": "^2.3.0", "make-arrow-function": "^1.2.0", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.6.0", "make-generator-function": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/which-typed-array_1.1.5_1628196184232_0.03236099768248302", "host": "s3://npm-registry-packages"}}, "1.1.6": {"name": "which-typed-array", "version": "1.1.6", "keywords": ["array", "TypedArray", "typed array", "which", "typed", "Int8Array", "Uint8Array", "Uint8ClampedArray", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array", "ES6", "toStringTag", "Symbol.toStringTag", "@@toStringTag"], "author": {"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "which-typed-array@1.1.6", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/which-typed-array#readme", "bugs": {"url": "https://github.com/inspect-js/which-typed-array/issues"}, "dist": {"shasum": "f3713d801da0720a7f26f50c596980a9f5c8b383", "tarball": "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.6.tgz", "fileCount": 11, "integrity": "sha512-DdY984dGD5sQ7Tf+x1CkXzdg85b9uEel6nr4UkFg1LoE9OXv3uRuZhe5CoWdawhGACeFpEZXH8fFLQnDhbpm/Q==", "signatures": [{"sig": "MEYCIQCxF6nybPRPqY6zTyDsKjuZsizGwP2Jck9SqXnMU43xYAIhAOly4M6DG+Bvs2CEQ9EH10VgjwG5PYzcYewz7EiFGS0/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28282, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhDidDCRA9TVsSAnZWagAARiMP/A5XV7HnGOrNVsM0L9dC\nk4PqqIXVmPHIYFfn6gZop/+uz/CGjxdcxTBRu4r0g0xeYC8OKmp4u4ZoTsmM\nn4p3zLqRZshEa0wuJjt2/ju65j40Kqcnh0rBISJFbeMhVgStH3RFl/nUX+bD\nHvWLIv/+/hxiU3BRZ7vkfuFJCGVPCv2UbJzI+O8wfSe1sK/fF3emDTuPSgBi\nBjBaY1j8f/5S2s9Gc5RZY6KrbJ6bOhmnQaks5icXPt8w5zX0+mSI6iEgNdpn\ncmnBLr2gyPfERCh8G0FdzxzSwKML7ErQAu6rRW4Wy8cZBSmVc4s/Bz90Qhqa\nMxXi8WpJroL7dtnCVtsJzgE7tA3SGjZsmr8+HRwYj9aoGMEx7iNltPKierDh\nXxezmapo8lk9wHgSwflSWLhd/NSHptTXtcZDnZ2cDNWEzruEE8jJjF6MBQ7D\nrTgqrHLqEbUR0+IBkWoCTsuPrlj8bBSho6R8MOyMIyAOkdSB2B67NhVge5Ty\nLrCa2WrgkTKTWR7/vPOx+UtoybKReC9iFf+9bTYGlvpNE4ESaIQKscjhE0S6\nsHw9FdwYr0Es5jTHuukIZuu4msf6c1X4vJIgNZTDhz27SEZKDJ0r2w6J+81s\nSL0/SlzrKfVOTR8lcjXoCt+rJZqhZ+aI8W3OGl0II47DK8B4/rl0SEt1hPmy\nM6ht\r\n=y2NC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "9207a054d3adaba88601d069f07ff8f6371b405b", "scripts": {"lint": "eslint .", "test": "npm run tests-only && npm run test:harmony", "pretest": "npm run --silent lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "npx aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape test", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "test:harmony": "nyc node --harmony --es-staging test", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/inspect-js/which-typed-array.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Which kind of Typed Array is this JavaScript value? Works cross-realm, without `instanceof`, and despite Symbol.toStringTag.", "directories": {}, "_nodeVersion": "16.6.1", "dependencies": {"foreach": "^2.0.5", "call-bind": "^1.0.2", "es-abstract": "^1.18.5", "is-typed-array": "^1.1.6", "has-tostringtag": "^1.0.0", "available-typed-arrays": "^1.0.4"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^1.1.5", "nyc": "^10.3.2", "tape": "^5.3.1", "eslint": "^7.32.0", "in-publish": "^2.0.1", "is-callable": "^1.2.4", "auto-changelog": "^2.3.0", "make-arrow-function": "^1.2.0", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.6.0", "make-generator-function": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/which-typed-array_1.1.6_1628317506704_0.7258707654926067", "host": "s3://npm-registry-packages"}}, "1.1.7": {"name": "which-typed-array", "version": "1.1.7", "keywords": ["array", "TypedArray", "typed array", "which", "typed", "Int8Array", "Uint8Array", "Uint8ClampedArray", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array", "ES6", "toStringTag", "Symbol.toStringTag", "@@toStringTag"], "author": {"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "which-typed-array@1.1.7", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/which-typed-array#readme", "bugs": {"url": "https://github.com/inspect-js/which-typed-array/issues"}, "dist": {"shasum": "2761799b9a22d4b8660b3c1b40abaa7739691793", "tarball": "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.7.tgz", "fileCount": 11, "integrity": "sha512-vjxaB4nfDqwKI0ws7wZpxIlde1XrLX5uB0ZjpfshgmapJMD7jJWhZI+yToJTqaFByF0eNBcYxbjmCzoRP7CfEw==", "signatures": [{"sig": "MEUCIQCCB4qolc1dJIbp+h6J58QFsLQmSkGeq6YF8RkxD5FMHgIgSdixrTxJ2l92a64GRg7SU8nCkqUBZCYlxnSsPFyArX8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29168, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhLZZrCRA9TVsSAnZWagAAV5wQAKHPbO+L71iEhUf8DNzZ\nm6BkwD9XJzCbTj0mnnoRr7jQ+/gDMEQK909ukxIZ+AlBIz2xl3cDWb4FFSYv\nEN+meO1cSSLW2UrwffM2gnBJmpMugqAdwM67FBOW1g8wPB4mz9J9cIjeCaH+\nLwswi4FPP3H3VeIqEiXDGnvb5bIFAjBctfUkaX1RmXvA4s+rtc4eMbUpLOR5\nuXu0y2SvbEJ0mUgPEJNabj1Z6sXixm9yaGiW3jNazVQhBpF6f1+U5mM5nlct\naKSZP5mA6TppKAi0nvNHdbzUHFKnFROFWNkYPaM/5AxYNyvHuAnhDu9RQtvL\n2eJ4r5E929f0JR/I/ZERySoSjompDbBwpPWsv0dsNY+QTMTuZVX0YfI1IqOb\nR6XVY0DumaBkHKCYV1kk0PqQq7hu1rq6BwgDwTYObhMV90Yf/EBA97JnOPbK\n6shdAm+j4cgMJGeFqRs1X3wShGKBnR266BlfOhMCPHYcLOJ2kmp/0jnaiFxM\nIVSG8kwRAsWVJO0NMtq/1ocNFMPCCQ4HsJZc6a2UjbkqhncTAPiP8f0gc+Qz\nhlH+6cN2ahxatVRlKKQUPgq/HZLZSS0gP+yYlhIjumMTPvcfxlCGyskDUd0P\nEhSDhAFzbHoZxxADGhmW5+u0Z4VTvtyFrbLPGPFfrNnV5qd+HaUJuysJP7kn\nUjAR\r\n=U9Xg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "951bb1ab9e930ba5657e358f6cef9266b547e4a6", "scripts": {"lint": "eslint .", "test": "npm run tests-only && npm run test:harmony", "pretest": "npm run --silent lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "npx aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape test", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "test:harmony": "nyc node --harmony --es-staging test", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/inspect-js/which-typed-array.git", "type": "git"}, "_npmVersion": "7.21.0", "description": "Which kind of Typed Array is this JavaScript value? Works cross-realm, without `instanceof`, and despite Symbol.toStringTag.", "directories": {}, "_nodeVersion": "16.8.0", "dependencies": {"foreach": "^2.0.5", "call-bind": "^1.0.2", "es-abstract": "^1.18.5", "is-typed-array": "^1.1.7", "has-tostringtag": "^1.0.0", "available-typed-arrays": "^1.0.5"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^1.1.5", "nyc": "^10.3.2", "tape": "^5.3.1", "eslint": "^7.32.0", "in-publish": "^2.0.1", "is-callable": "^1.2.4", "auto-changelog": "^2.3.0", "make-arrow-function": "^1.2.0", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^18.0.0", "make-generator-function": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/which-typed-array_1.1.7_1630377579016_0.08204462360783737", "host": "s3://npm-registry-packages"}}, "1.1.8": {"name": "which-typed-array", "version": "1.1.8", "keywords": ["array", "TypedArray", "typed array", "which", "typed", "Int8Array", "Uint8Array", "Uint8ClampedArray", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array", "ES6", "toStringTag", "Symbol.toStringTag", "@@toStringTag"], "author": {"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "which-typed-array@1.1.8", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/which-typed-array#readme", "bugs": {"url": "https://github.com/inspect-js/which-typed-array/issues"}, "dist": {"shasum": "0cfd53401a6f334d90ed1125754a42ed663eb01f", "tarball": "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.8.tgz", "fileCount": 10, "integrity": "sha512-Jn4e5PItbcAHyLoRDwvPj1ypu27DJbtdYXUa5zsinrUx77Uvfb0cXwwnGMTn7cjUfhhqgVQnVJCwF+7cgU7tpw==", "signatures": [{"sig": "MEYCIQDe5Pe6zYuCjZwVDoOsEaL4qmDtP8t0DZ6nm4vDaY/1VAIhAKZGLY1m10TpCJhZ5IKAkbv+fY8u0SLn4f+T93hhTp08", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31345, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJif9QIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmonABAAnvVpeVWgbGv6MR2t42yxHmn5uBlUD0RkpIg/71CzHtr43jx2\r\nCfIbKbS+XfCUq1vq6uYKpJkfoNCIqCyV0bKSmF6LEq8Vdlx3keTnoH2RyLII\r\nLjqcpSKVvqrgwcWnfsgEMMKzh4tJRnTWA7N2DrBrkp9nO3JB7rnVKAn+2qt9\r\n6Flry7ogxCe1EoGHEuwDGiL8C3SmLFuJZoudJ10+FDasja3xJTsYYPiDy8fu\r\nRsvRfO4we0gV/Sx5mGgQkL4vzw11Jpb8uUf/O/+Zisu+hBtPBqEy6oe+axfM\r\nQAZr5VnU+ptq4qJXkWlNneslLYbOGazPJsIlDyn1HcE0k4obCErizlTBKNVT\r\nq3weOjSp62NmJrBTW3TdfKVp7mBYI7IufYgSWCx6z7HTk8GhV5E8L6ioZBPt\r\nUjTKyiETGkbkhb0JrRdc2lZnjKFxZ0qMatHlaMeWUeIeDVV29qudqRG9rjXq\r\naAU5nR9uG26yJrQ1jyuAwkosjLYCcJBMfYgo9yn+4onLw7RPpSaBMtkRdMEf\r\ny0VFbRU71qnMiOIwBY6uJ3WUeRL8nWkLFMfue9k9EvCQYZwLLd614SlkDG1D\r\neMfn5tLf2UpGLBDlteyNPzgvAG7r6uA84ny5tbSWoBWZYgiQHI3ly00M5rDh\r\n71npJL2CKwoGahlxLgk/mFh8vzGPdP+oCKo=\r\n=5JQU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "2c6bb26014a4cb352f7e81705b0d34ff6391ff95", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only && npm run test:harmony", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run --silent lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape test", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "test:harmony": "nyc node --harmony --es-staging test", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/inspect-js/which-typed-array.git", "type": "git"}, "_npmVersion": "8.8.0", "description": "Which kind of Typed Array is this JavaScript value? Works cross-realm, without `instanceof`, and despite Symbol.toStringTag.", "directories": {}, "_nodeVersion": "18.1.0", "dependencies": {"for-each": "^0.3.3", "call-bind": "^1.0.2", "es-abstract": "^1.20.0", "is-typed-array": "^1.1.9", "has-tostringtag": "^1.0.0", "available-typed-arrays": "^1.0.5"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.0", "nyc": "^10.3.2", "tape": "^5.5.3", "eslint": "=8.8.0", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "is-callable": "^1.2.4", "auto-changelog": "^2.4.0", "make-arrow-function": "^1.2.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.0.0", "make-generator-function": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/which-typed-array_1.1.8_1652544519869_0.****************", "host": "s3://npm-registry-packages"}}, "1.1.9": {"name": "which-typed-array", "version": "1.1.9", "keywords": ["array", "TypedArray", "typed array", "which", "typed", "Int8Array", "Uint8Array", "Uint8ClampedArray", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array", "ES6", "toStringTag", "Symbol.toStringTag", "@@toStringTag"], "author": {"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "which-typed-array@1.1.9", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/which-typed-array#readme", "bugs": {"url": "https://github.com/inspect-js/which-typed-array/issues"}, "dist": {"shasum": "307cf898025848cf995e795e8423c7f337efbde6", "tarball": "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.9.tgz", "fileCount": 10, "integrity": "sha512-w9c4xkx6mPidwp7180ckYWfMmvxpjlZuIudNtDf4N/tTAUB8VJbX25qZoAsrtGuYNnGw3pa0AXgbGKRB8/EceA==", "signatures": [{"sig": "MEUCIEGUaMk1VeVcJl9naefNWUWA/7UcJekXWiV9t3C+R+U1AiEAgLII00sbIndC/0snftwNzvXBW1S1o/vh4t2i9JKpLw8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32040, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjYpaeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrpqRAApJHsYKNBC/4wFcSYs+2rsk9ls/J+1Yo2c6A4hS3Af1vZNrFr\r\n5JBVimvp5SEFr0v0/N64osCzzrw+nbgE4Y47Psh1+LURSiaw+zqD9ItMYBfZ\r\nAifOJ1LPwl90SlDGKQxm6LLyinuhP489dzSwiG6bKcpCG2R6Bsh+TnqyX0MB\r\n57TOVKlSRosNaDjR4S6eUBHfEKk6gYi6v06Dgq4eenU8P3psmztQWFbVXeHz\r\njarQ6CP8BJMENFaO/6v/P64JtPiJPIjjS1liX9ISfN4XjQnFO5D3rEA7CCPh\r\n9tfTlHz58H/pt8RLUYmjQLf4GdoAZEvkddy0Xmvhi4S/7WM/le+b2MApIiDC\r\n9FEZNOogFNuR92sIieMC+HvNUhdEpAIxFZPCkBhOE7IivU+FbQyOJDNU/o01\r\nCRWTkGivJLg3eUiVwoWdyQG6U0sXF9AkWNsA2008/oGYY65rbbZAcdI2fLeF\r\noOWM5siJwFhCKGk+Xurwto4qxZ0ltaZ374WQaCoG71TTMf8RdhV3GFXnP4rS\r\nY6c8zRWUwLDajDQtHQ8Beg0wNQwqRgfEE9NiNMAX87r4YwuBcmo2pHQnbyZJ\r\ny19ZQVxuQsrpI/pmjV4Y6czC2gv2Z0s7+F1tqJlg1p5m/o/UeFv1OgstexnZ\r\nUX+T4imiydMfqIsFWj9HjXm9aqv/ZGA4oUQ=\r\n=dDA2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "c7f7c5510fce82dbf8deb77d091b22d0f20a4695", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only && npm run test:harmony", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run --silent lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape test", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "test:harmony": "nyc node --harmony --es-staging test", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/inspect-js/which-typed-array.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Which kind of Typed Array is this JavaScript value? Works cross-realm, without `instanceof`, and despite Symbol.toStringTag.", "directories": {}, "sideEffects": false, "_nodeVersion": "19.0.0", "dependencies": {"gopd": "^1.0.1", "for-each": "^0.3.3", "call-bind": "^1.0.2", "is-typed-array": "^1.1.10", "has-tostringtag": "^1.0.0", "available-typed-arrays": "^1.0.5"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.1", "nyc": "^10.3.2", "tape": "^5.6.1", "eslint": "=8.8.0", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "is-callable": "^1.2.7", "auto-changelog": "^2.4.0", "make-arrow-function": "^1.2.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.0.0", "make-generator-function": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/which-typed-array_1.1.9_1667405470800_0.8456955482181028", "host": "s3://npm-registry-packages"}}, "1.1.10": {"name": "which-typed-array", "version": "1.1.10", "keywords": ["array", "TypedArray", "typed array", "which", "typed", "Int8Array", "Uint8Array", "Uint8ClampedArray", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array", "ES6", "toStringTag", "Symbol.toStringTag", "@@toStringTag"], "author": {"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "which-typed-array@1.1.10", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/which-typed-array#readme", "bugs": {"url": "https://github.com/inspect-js/which-typed-array/issues"}, "dist": {"shasum": "74baa2789991905c2076abb317103b866c64e69e", "tarball": "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.10.tgz", "fileCount": 10, "integrity": "sha512-uxoA5vLUfRPdjCuJ1h5LlYdmTLbYfums398v3WLkM+i/Wltl2/XyZpQWKbN++ck5L64SR/grOHqtXCUKmlZPNA==", "signatures": [{"sig": "MEYCIQDIm8+boCRm48Zw+/SJuUi5+XiImBuwRcMctPrgfYcQTwIhAJyswaC1szkfAnbvF1AJnPOOcYFseWTtlU9zrdgLz8Xd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32690}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "80322d4346ac892da5758c46c175aa7c26508493", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only && npm run test:harmony", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run --silent lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape test", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "test:harmony": "nyc node --harmony --es-staging test", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/inspect-js/which-typed-array.git", "type": "git"}, "_npmVersion": "9.7.2", "description": "Which kind of Typed Array is this JavaScript value? Works cross-realm, without `instanceof`, and despite Symbol.toStringTag.", "directories": {}, "sideEffects": false, "_nodeVersion": "20.4.0", "dependencies": {"gopd": "^1.0.1", "for-each": "^0.3.3", "call-bind": "^1.0.2", "is-typed-array": "^1.1.10", "has-tostringtag": "^1.0.0", "available-typed-arrays": "^1.0.5"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.3", "nyc": "^10.3.2", "tape": "^5.6.4", "eslint": "=8.8.0", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "is-callable": "^1.2.7", "auto-changelog": "^2.4.0", "make-arrow-function": "^1.2.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.1.0", "make-generator-function": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/which-typed-array_1.1.10_1689010001632_0.****************", "host": "s3://npm-registry-packages"}}, "1.1.11": {"name": "which-typed-array", "version": "1.1.11", "keywords": ["array", "TypedArray", "typed array", "which", "typed", "Int8Array", "Uint8Array", "Uint8ClampedArray", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array", "ES6", "toStringTag", "Symbol.toStringTag", "@@toStringTag"], "author": {"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "which-typed-array@1.1.11", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/which-typed-array#readme", "bugs": {"url": "https://github.com/inspect-js/which-typed-array/issues"}, "dist": {"shasum": "99d691f23c72aab6768680805a271b69761ed61a", "tarball": "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.11.tgz", "fileCount": 10, "integrity": "sha512-qe9UWWpkeG5yzZ0tNYxDmd7vo58HDBc39mZ0xWWpolAGADdFOzkfamWLDxkOWcvHQKVmdTyQdLD4NOfjLWTKew==", "signatures": [{"sig": "MEUCIQClTwp/pZ7SRL3MzUT7vAaf/FojL9ClexDeDPATDZwY3wIgVXEiXJxspm6t6q5msBZvUuEa9t8bVKtxjp/PbhEos30=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33757}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "efb97902a28cf1703883bee41dd35591df6ce6d1", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only && npm run test:harmony", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run --silent lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape test", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "test:harmony": "nyc node --harmony --es-staging test", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/inspect-js/which-typed-array.git", "type": "git"}, "_npmVersion": "9.7.2", "description": "Which kind of Typed Array is this JavaScript value? Works cross-realm, without `instanceof`, and despite Symbol.toStringTag.", "directories": {}, "sideEffects": false, "_nodeVersion": "20.4.0", "dependencies": {"gopd": "^1.0.1", "for-each": "^0.3.3", "call-bind": "^1.0.2", "has-tostringtag": "^1.0.0", "available-typed-arrays": "^1.0.5"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.3", "nyc": "^10.3.2", "tape": "^5.6.5", "eslint": "=8.8.0", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "is-callable": "^1.2.7", "auto-changelog": "^2.4.0", "make-arrow-function": "^1.2.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.1.0", "make-generator-function": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/which-typed-array_1.1.11_1689648792031_0.7706620800739166", "host": "s3://npm-registry-packages"}}, "1.1.12": {"name": "which-typed-array", "version": "1.1.12", "keywords": ["array", "TypedArray", "typed array", "which", "typed", "Int8Array", "Uint8Array", "Uint8ClampedArray", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array", "ES6", "toStringTag", "Symbol.toStringTag", "@@toStringTag"], "author": {"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "which-typed-array@1.1.12", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/which-typed-array#readme", "bugs": {"url": "https://github.com/inspect-js/which-typed-array/issues"}, "dist": {"shasum": "cc8dfcc0ed769af33879012ea549b0af69d33cd3", "tarball": "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.12.tgz", "fileCount": 10, "integrity": "sha512-H287jkk4q9wRAQTStQoqm+zGjoMqOxBKAdwnENVwav5/ngW76883g6EwLH9GUPPD7m7yj60xP7wtzQhh6//04w==", "signatures": [{"sig": "MEUCIE/yYWDJNUqY8D7aohPYy+Dfuk2CTVxhCW4O7ZaYFAkjAiEA65tMAttR4dqj8AJrzQuITyI0SDbOF5nLTCqdVCZ4XR8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34343}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "c166569211e2aaea4682d460f19c1e2765eeec17", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only && npm run test:harmony", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run --silent lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape test", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "test:harmony": "nyc node --harmony --es-staging test", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/inspect-js/which-typed-array.git", "type": "git"}, "_npmVersion": "10.2.0", "description": "Which kind of Typed Array is this JavaScript value? Works cross-realm, without `instanceof`, and despite Symbol.toStringTag.", "directories": {}, "sideEffects": false, "_nodeVersion": "21.0.0", "dependencies": {"gopd": "^1.0.1", "for-each": "^0.3.3", "call-bind": "^1.0.4", "has-tostringtag": "^1.0.0", "available-typed-arrays": "^1.0.5"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.3", "nyc": "^10.3.2", "tape": "^5.7.1", "eslint": "=8.8.0", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "is-callable": "^1.2.7", "auto-changelog": "^2.4.0", "make-arrow-function": "^1.2.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.1.0", "make-generator-function": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/which-typed-array_1.1.12_1697758879769_0.24867722797087533", "host": "s3://npm-registry-packages"}}, "1.1.13": {"name": "which-typed-array", "version": "1.1.13", "keywords": ["array", "TypedArray", "typed array", "which", "typed", "Int8Array", "Uint8Array", "Uint8ClampedArray", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array", "ES6", "toStringTag", "Symbol.toStringTag", "@@toStringTag"], "author": {"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "which-typed-array@1.1.13", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/which-typed-array#readme", "bugs": {"url": "https://github.com/inspect-js/which-typed-array/issues"}, "dist": {"shasum": "870cd5be06ddb616f504e7b039c4c24898184d36", "tarball": "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.13.tgz", "fileCount": 10, "integrity": "sha512-P5Nra0qjSncduVPEAr7xhoF5guty49ArDTwzJ/yNuPIbZppyRxFQsRCWrocxIY+CnMVG+qfbU2FmDKyvSGClow==", "signatures": [{"sig": "MEQCIANktBt+A/BwZtsAnmoCzATnFtoTUwmqWTqybOeI/wjUAiBqZ9+fK1DTmmUQJ8UN9Xx6kEy60xZOI4/+0Ik0bSg5WQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34673}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "e79078168f2f587423811e28dc50819dd9ede7e2", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only && npm run test:harmony", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run --silent lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape test", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "test:harmony": "nyc node --harmony --es-staging test", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/inspect-js/which-typed-array.git", "type": "git"}, "_npmVersion": "10.2.0", "description": "Which kind of Typed Array is this JavaScript value? Works cross-realm, without `instanceof`, and despite Symbol.toStringTag.", "directories": {}, "sideEffects": false, "_nodeVersion": "21.0.0", "dependencies": {"gopd": "^1.0.1", "for-each": "^0.3.3", "call-bind": "^1.0.4", "has-tostringtag": "^1.0.0", "available-typed-arrays": "^1.0.5"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.3", "nyc": "^10.3.2", "tape": "^5.7.1", "eslint": "=8.8.0", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "is-callable": "^1.2.7", "auto-changelog": "^2.4.0", "make-arrow-function": "^1.2.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.1.0", "make-generator-function": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/which-typed-array_1.1.13_1697760632298_0.3675376584735963", "host": "s3://npm-registry-packages"}}, "1.1.14": {"name": "which-typed-array", "version": "1.1.14", "keywords": ["array", "TypedArray", "typed array", "which", "typed", "Int8Array", "Uint8Array", "Uint8ClampedArray", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array", "ES6", "toStringTag", "Symbol.toStringTag", "@@toStringTag"], "author": {"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "which-typed-array@1.1.14", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/which-typed-array#readme", "bugs": {"url": "https://github.com/inspect-js/which-typed-array/issues"}, "dist": {"shasum": "1f78a111aee1e131ca66164d8bdc3ab062c95a06", "tarball": "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.14.tgz", "fileCount": 12, "integrity": "sha512-VnXFiIW8yNn9kIHN88xvZ4yOWchftKDsRJ8fEPacX/wl1lOvBrhsJ/OeJCXq7B0AaijRuqgzSKalJoPk+D8MPg==", "signatures": [{"sig": "MEUCIGxDt2mKLYxRFnrHxfWl4jVoLMgN2jdXnRRIJRhcFNWJAiEA6xc/h0t7mBE/nO21VYqxRYGFscmoyx02lDj2neLCYZI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41681}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "1fdc86b21df4c3b32f583ea5975577638fbb1bbf", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only && npm run test:harmony", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run --silent lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "tsc -p .", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape test", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "test:harmony": "nyc node --harmony --es-staging test", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/inspect-js/which-typed-array.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Which kind of Typed Array is this JavaScript value? Works cross-realm, without `instanceof`, and despite Symbol.toStringTag.", "directories": {}, "sideEffects": false, "_nodeVersion": "21.6.0", "dependencies": {"gopd": "^1.0.1", "for-each": "^0.3.3", "call-bind": "^1.0.5", "has-tostringtag": "^1.0.1", "available-typed-arrays": "^1.0.6"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.4", "nyc": "^10.3.2", "tape": "^5.7.4", "eslint": "=8.8.0", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "^5.4.0-dev.20240131", "@types/gopd": "^1.0.3", "@types/node": "^20.11.14", "@types/tape": "^5.6.4", "is-callable": "^1.2.7", "auto-changelog": "^2.4.0", "@types/for-each": "^0.3.3", "@types/call-bind": "^1.0.5", "@types/is-callable": "^1.1.2", "make-arrow-function": "^1.2.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.1.0", "make-generator-function": "^2.0.0", "@types/make-arrow-function": "^1.2.2", "@types/make-generator-function": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/which-typed-array_1.1.14_1706814215863_0.890140298733832", "host": "s3://npm-registry-packages"}}, "1.1.15": {"name": "which-typed-array", "version": "1.1.15", "keywords": ["array", "TypedArray", "typed array", "which", "typed", "Int8Array", "Uint8Array", "Uint8ClampedArray", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array", "ES6", "toStringTag", "Symbol.toStringTag", "@@toStringTag"], "author": {"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "which-typed-array@1.1.15", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/which-typed-array#readme", "bugs": {"url": "https://github.com/inspect-js/which-typed-array/issues"}, "dist": {"shasum": "264859e9b11a649b388bfaaf4f767df1f779b38d", "tarball": "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.15.tgz", "fileCount": 12, "integrity": "sha512-oV0jmFtUky6CXfkqehVvBP/LSWJ2sy4vWMioiENyJLePrBO/yKyV9OyJySfAKosh+RYkIl5zJCNZ8/4JncrpdA==", "signatures": [{"sig": "MEYCIQCkbe22mDTgDEFQiPJ9Y92G6eKGiJEwKfIxM+bsa1qr0gIhAMlfwyP8Y4oSktVN8KemM83S0g1iy/X8Tn8IVN15OiaN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40709}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "bf446c8e6a748e1a6f5847fffa5350f6c51190b5", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only && npm run test:harmony", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run --silent lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "tsc -p . && attw -P", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape test", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "test:harmony": "nyc node --harmony --es-staging test", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/inspect-js/which-typed-array.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Which kind of Typed Array is this JavaScript value? Works cross-realm, without `instanceof`, and despite Symbol.toStringTag.", "directories": {}, "sideEffects": false, "_nodeVersion": "21.7.0", "dependencies": {"gopd": "^1.0.1", "for-each": "^0.3.3", "call-bind": "^1.0.7", "has-tostringtag": "^1.0.2", "available-typed-arrays": "^1.0.7"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.4", "nyc": "^10.3.2", "tape": "^5.7.5", "eslint": "=8.8.0", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "next", "@types/gopd": "^1.0.3", "@types/node": "^20.11.25", "@types/tape": "^5.6.4", "is-callable": "^1.2.7", "auto-changelog": "^2.4.0", "@types/for-each": "^0.3.3", "@ljharb/tsconfig": "^0.2.0", "@types/call-bind": "^1.0.5", "@types/is-callable": "^1.1.2", "make-arrow-function": "^1.2.0", "safe-publish-latest": "^2.0.0", "@arethetypeswrong/cli": "^0.15.1", "@ljharb/eslint-config": "^21.1.0", "make-generator-function": "^2.0.0", "@types/make-arrow-function": "^1.2.2", "@types/make-generator-function": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/which-typed-array_1.1.15_1710130626936_0.1343374469365075", "host": "s3://npm-registry-packages"}}, "1.1.16": {"name": "which-typed-array", "version": "1.1.16", "keywords": ["array", "TypedArray", "typed array", "which", "typed", "Int8Array", "Uint8Array", "Uint8ClampedArray", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array", "ES6", "toStringTag", "Symbol.toStringTag", "@@toStringTag"], "author": {"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "which-typed-array@1.1.16", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/which-typed-array#readme", "bugs": {"url": "https://github.com/inspect-js/which-typed-array/issues"}, "dist": {"shasum": "db4db429c4706feca2f01677a144278e4a8c216b", "tarball": "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.16.tgz", "fileCount": 12, "integrity": "sha512-g+N+GAWiRj66DngFwHvISJd+ITsyphZvD1vChfVg6cEdnzy53GzB3oy0fUNlvhz7H7+MiqhYr26qxQShCpKTTQ==", "signatures": [{"sig": "MEQCIAyKD/EU+8nVwaAgwv71sdNyaU7aDj2YG7Phex/0V6RHAiAdqarUSM9PGGwlgVDgmYflZnJTjaKjh2fH1++FdFx6Gg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41895}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "a8048ead3db7bc967ed9220a1f322640f603b6c4", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only && npm run test:harmony", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run --silent lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "tsc -p . && attw -P", "posttest": "npx npm@'>=10.2' audit --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape test", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "test:harmony": "nyc node --harmony --es-staging test", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/inspect-js/which-typed-array.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Which kind of Typed Array is this JavaScript value? Works cross-realm, without `instanceof`, and despite Symbol.toStringTag.", "directories": {}, "sideEffects": false, "_nodeVersion": "23.3.0", "dependencies": {"gopd": "^1.0.1", "for-each": "^0.3.3", "call-bind": "^1.0.7", "has-tostringtag": "^1.0.2", "available-typed-arrays": "^1.0.7"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"nyc": "^10.3.2", "tape": "^5.9.0", "eslint": "=8.8.0", "encoding": "^0.1.13", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "next", "@types/gopd": "^1.0.3", "@types/tape": "^5.6.5", "is-callable": "^1.2.7", "auto-changelog": "^2.5.0", "@types/for-each": "^0.3.3", "@ljharb/tsconfig": "^0.2.0", "@types/call-bind": "^1.0.5", "@types/is-callable": "^1.1.2", "make-arrow-function": "^1.2.0", "safe-publish-latest": "^2.0.0", "@arethetypeswrong/cli": "^0.17.0", "@ljharb/eslint-config": "^21.1.1", "make-generator-function": "^2.0.0", "@types/make-arrow-function": "^1.2.2", "@types/make-generator-function": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/which-typed-array_1.1.16_1732743138457_0.9353616286575286", "host": "s3://npm-registry-packages"}}, "1.1.17": {"name": "which-typed-array", "version": "1.1.17", "keywords": ["array", "TypedArray", "typed array", "which", "typed", "Int8Array", "Uint8Array", "Uint8ClampedArray", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array", "ES6", "toStringTag", "Symbol.toStringTag", "@@toStringTag"], "author": {"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "which-typed-array@1.1.17", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "contributors": [{"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/which-typed-array#readme", "bugs": {"url": "https://github.com/inspect-js/which-typed-array/issues"}, "dist": {"shasum": "b181869df579e1be9ebacb88922fe4353b1289da", "tarball": "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.17.tgz", "fileCount": 12, "integrity": "sha512-i2prb5irfKvNFV84NNLOZaNiMx20sm/AG2u59hU+JsjraeD5xs9LgQa+VzU95e2Tn0YMc/4drYPgPV3QvRAPPA==", "signatures": [{"sig": "MEUCIQDaWtuLesQncZj3kZ00tftUijpppqaMLHgVOpm+w/mY4gIgcSGcIgT19BVImur63+IwYIW4xnsLV4kkNe7Avd5sFrw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42510}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "dfa5140c98877229d53005e7024c5ae35d2a880e", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only && npm run test:harmony", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run --silent lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "tsc -p . && attw -P", "posttest": "npx npm@'>=10.2' audit --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape test", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "test:harmony": "nyc node --harmony --es-staging test", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/inspect-js/which-typed-array.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Which kind of Typed Array is this JavaScript value? Works cross-realm, without `instanceof`, and despite Symbol.toStringTag.", "directories": {}, "sideEffects": false, "_nodeVersion": "23.4.0", "dependencies": {"gopd": "^1.2.0", "for-each": "^0.3.3", "call-bind": "^1.0.8", "call-bound": "^1.0.3", "has-tostringtag": "^1.0.2", "available-typed-arrays": "^1.0.7"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"nyc": "^10.3.2", "tape": "^5.9.0", "eslint": "=8.8.0", "encoding": "^0.1.13", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "next", "@types/gopd": "^1.0.3", "@types/tape": "^5.7.0", "is-callable": "^1.2.7", "auto-changelog": "^2.5.0", "@types/for-each": "^0.3.3", "@ljharb/tsconfig": "^0.2.2", "@types/call-bind": "^1.0.5", "@types/is-callable": "^1.1.2", "make-arrow-function": "^1.2.0", "safe-publish-latest": "^2.0.0", "@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "make-generator-function": "^2.0.0", "@types/make-arrow-function": "^1.2.2", "@types/make-generator-function": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/which-typed-array_1.1.17_1734539260096_0.08525257824784549", "host": "s3://npm-registry-packages-npm-production"}}, "1.1.18": {"name": "which-typed-array", "version": "1.1.18", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "description": "Which kind of Typed Array is this JavaScript value? Works cross-realm, without `instanceof`, and despite Symbol.toStringTag.", "license": "MIT", "main": "index.js", "types": "./index.d.ts", "sideEffects": false, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run --silent lint", "test": "npm run tests-only && npm run test:harmony", "tests-only": "nyc tape test", "test:harmony": "nyc node --harmony --es-staging test", "posttest": "npx npm@'>=10.2' audit --production", "lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && attw -P", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/inspect-js/which-typed-array.git"}, "keywords": ["array", "TypedArray", "typed array", "which", "typed", "Int8Array", "Uint8Array", "Uint8ClampedArray", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array", "ES6", "toStringTag", "Symbol.toStringTag", "@@toStringTag"], "dependencies": {"available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "call-bound": "^1.0.3", "for-each": "^0.3.3", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2"}, "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/call-bind": "^1.0.5", "@types/for-each": "^0.3.3", "@types/gopd": "^1.0.3", "@types/is-callable": "^1.1.2", "@types/make-arrow-function": "^1.2.2", "@types/make-generator-function": "^2.0.3", "@types/tape": "^5.8.0", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "eslint": "=8.8.0", "in-publish": "^2.0.1", "is-callable": "^1.2.7", "make-arrow-function": "^1.2.0", "make-generator-function": "^2.0.0", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "_id": "which-typed-array@1.1.18", "gitHead": "c603d9bcc1c40ecea3d59005646e242b67f1df85", "bugs": {"url": "https://github.com/inspect-js/which-typed-array/issues"}, "homepage": "https://github.com/inspect-js/which-typed-array#readme", "_nodeVersion": "23.4.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-qEcY+KJYlWyLH9vNbsr6/5j59AXk5ni5aakf8ldzBvGde6Iz4sxZGkJyWSAueTG7QhOvNRYb1lDdFmL5Td0QKA==", "shasum": "df2389ebf3fbb246a71390e90730a9edb6ce17ad", "tarball": "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.18.tgz", "fileCount": 12, "unpackedSize": 43024, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCYy7tOOiTxXelL8H4Kd1arytQenjZrr0DrC18K06QdWQIhAMFmZ4Iwi1R8E5J19jgELMOL8nWKx5joLUcOvtQnzVbX"}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/which-typed-array_1.1.18_1734543752433_0.4908214494114316"}, "_hasShrinkwrap": false}}, "time": {"created": "2015-10-05T22:40:31.831Z", "modified": "2024-12-18T17:42:32.822Z", "1.0.0": "2015-10-05T22:40:31.831Z", "1.0.1": "2016-03-20T00:53:20.441Z", "1.1.0": "2019-02-16T18:02:18.017Z", "1.1.1": "2020-01-24T20:44:59.057Z", "1.1.2": "2020-04-08T05:05:03.232Z", "1.1.3": "2020-12-05T21:07:11.381Z", "1.1.4": "2020-12-05T21:09:47.597Z", "1.1.5": "2021-08-05T20:43:04.368Z", "1.1.6": "2021-08-07T06:25:07.308Z", "1.1.7": "2021-08-31T02:39:39.202Z", "1.1.8": "2022-05-14T16:08:40.026Z", "1.1.9": "2022-11-02T16:11:10.952Z", "1.1.10": "2023-07-10T17:26:41.813Z", "1.1.11": "2023-07-18T02:53:12.209Z", "1.1.12": "2023-10-19T23:41:19.976Z", "1.1.13": "2023-10-20T00:10:32.538Z", "1.1.14": "2024-02-01T19:03:36.072Z", "1.1.15": "2024-03-11T04:17:07.148Z", "1.1.16": "2024-11-27T21:32:18.653Z", "1.1.17": "2024-12-18T16:27:40.395Z", "1.1.18": "2024-12-18T17:42:32.594Z"}, "bugs": {"url": "https://github.com/inspect-js/which-typed-array/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "license": "MIT", "homepage": "https://github.com/inspect-js/which-typed-array#readme", "keywords": ["array", "TypedArray", "typed array", "which", "typed", "Int8Array", "Uint8Array", "Uint8ClampedArray", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array", "ES6", "toStringTag", "Symbol.toStringTag", "@@toStringTag"], "repository": {"type": "git", "url": "git://github.com/inspect-js/which-typed-array.git"}, "description": "Which kind of Typed Array is this JavaScript value? Works cross-realm, without `instanceof`, and despite Symbol.toStringTag.", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "readme": "# which-typed-array <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![dependency status][deps-svg]][deps-url]\n[![dev dependency status][dev-deps-svg]][dev-deps-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\nWhich kind of Typed Array is this JavaScript value? Works cross-realm, without `instanceof`, and despite Symbol.toStringTag.\n\n## Example\n\n```js\nvar whichTypedArray = require('which-typed-array');\nvar assert = require('assert');\n\nassert.equal(false, whichTypedArray(undefined));\nassert.equal(false, whichTypedArray(null));\nassert.equal(false, whichTypedArray(false));\nassert.equal(false, whichTypedArray(true));\nassert.equal(false, whichTypedArray([]));\nassert.equal(false, whichTypedArray({}));\nassert.equal(false, whichTypedArray(/a/g));\nassert.equal(false, whichTypedArray(new RegExp('a', 'g')));\nassert.equal(false, whichTypedArray(new Date()));\nassert.equal(false, whichTypedArray(42));\nassert.equal(false, whichTypedArray(NaN));\nassert.equal(false, whichTypedArray(Infinity));\nassert.equal(false, whichTypedArray(new Number(42)));\nassert.equal(false, whichTypedArray('foo'));\nassert.equal(false, whichTypedArray(Object('foo')));\nassert.equal(false, whichTypedArray(function () {}));\nassert.equal(false, whichTypedArray(function* () {}));\nassert.equal(false, whichTypedArray(x => x * x));\nassert.equal(false, whichTypedArray([]));\n\nassert.equal('Int8Array', whichTypedArray(new Int8Array()));\nassert.equal('Uint8Array', whichTypedArray(new Uint8Array()));\nassert.equal('Uint8ClampedArray', whichTypedArray(new Uint8ClampedArray()));\nassert.equal('Int16Array', whichTypedArray(new Int16Array()));\nassert.equal('Uint16Array', whichTypedArray(new Uint16Array()));\nassert.equal('Int32Array', whichTypedArray(new Int32Array()));\nassert.equal('Uint32Array', whichTypedArray(new Uint32Array()));\nassert.equal('Float32Array', whichTypedArray(new Float32Array()));\nassert.equal('Float64Array', whichTypedArray(new Float64Array()));\nassert.equal('BigInt64Array', whichTypedArray(new BigInt64Array()));\nassert.equal('BigUint64Array', whichTypedArray(new BigUint64Array()));\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n[package-url]: https://npmjs.org/package/which-typed-array\n[npm-version-svg]: https://versionbadg.es/inspect-js/which-typed-array.svg\n[deps-svg]: https://david-dm.org/inspect-js/which-typed-array.svg\n[deps-url]: https://david-dm.org/inspect-js/which-typed-array\n[dev-deps-svg]: https://david-dm.org/inspect-js/which-typed-array/dev-status.svg\n[dev-deps-url]: https://david-dm.org/inspect-js/which-typed-array#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/which-typed-array.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/which-typed-array.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/which-typed-array.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=which-typed-array\n[codecov-image]: https://codecov.io/gh/inspect-js/which-typed-array/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/inspect-js/which-typed-array/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/inspect-js/which-typed-array\n[actions-url]: https://github.com/inspect-js/which-typed-array/actions\n", "readmeFilename": "README.md"}