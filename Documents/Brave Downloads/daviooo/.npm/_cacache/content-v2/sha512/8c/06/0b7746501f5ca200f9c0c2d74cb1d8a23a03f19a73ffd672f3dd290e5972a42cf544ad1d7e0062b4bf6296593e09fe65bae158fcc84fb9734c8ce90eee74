{"_id": "i", "_rev": "44-8d401c711106880a98213ba9c78690db", "name": "i", "description": "custom inflections for nodejs", "dist-tags": {"latest": "0.3.7"}, "versions": {"0.2.0": {"name": "i", "version": "0.2.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "pksunkara.github.com"}, "description": "custom inflections for nodejs", "main": "./lib/inflect", "repository": {"type": "git", "url": "git://github.com/pksunkara/inflect.git"}, "keywords": ["singular", "plural", "camelize", "underscore", "dasherize", "demodulize", "ordinalize", "uncountable", "pluralize", "singularize", "titleize", "tableize", "classify", "foreign_key"], "homepage": "http://pksunkara.github.com/inflect", "scripts": {"test": "vows --spec $(find test -name '*-test.js')"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "dependencies": {}, "devDependencies": {"vows": "~0.6.1"}, "engines": {"node": ">=0.4"}, "bugs": {"url": "https://github.com/pksunkara/inflect/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/pksunkara/inflect/raw/master/LICENSE"}], "_npmUser": {"name": "pksunkara", "email": "<EMAIL>"}, "_id": "i@0.2.0", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.0-2", "_nodeVersion": "v0.6.0", "_defaultsLoaded": true, "dist": {"shasum": "70dd5c41ba1b188c9066c57376fedb153fc9ee50", "tarball": "https://registry.npmjs.org/i/-/i-0.2.0.tgz", "integrity": "sha512-dVmNpjT/v3oDSQZJmyR5oSLPclCBeCJGuamrucpRyrp7i9YBZP4jdreEAEkOC569NbdIx81ebZ0IaNiZaj7rVg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBHfjLxt7r+CaLzNTLVwbJESjN6PILzhTuA8+B38ohHuAiEA6QYDXH/5bJgaRhnDSn5TY0/m9l4bTDICLYQ+X1I3tuw="}]}, "maintainers": [{"name": "pksunkara", "email": "<EMAIL>"}], "directories": {}}, "0.3.0": {"name": "i", "version": "0.3.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "pksunkara.github.com"}, "description": "custom inflections for nodejs", "main": "./lib/inflect", "repository": {"type": "git", "url": "git://github.com/pksunkara/inflect.git"}, "keywords": ["singular", "plural", "camelize", "underscore", "dasherize", "demodulize", "ordinalize", "uncountable", "pluralize", "singularize", "titleize", "tableize", "classify", "foreign_key"], "homepage": "http://pksunkara.github.com/inflect", "scripts": {"test": "./node_modules/.bin/vows --spec $(find test -name '*-test.js')"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "dependencies": {}, "devDependencies": {"vows": "~0.6.1"}, "engines": {"node": ">=0.4"}, "bugs": {"url": "https://github.com/pksunkara/inflect/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/pksunkara/inflect/raw/master/LICENSE"}], "_npmUser": {"name": "pksunkara", "email": "<EMAIL>"}, "_id": "i@0.3.0", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.12", "_nodeVersion": "v0.6.14", "_defaultsLoaded": true, "dist": {"shasum": "1b49d1b29a738964381a988d083e82e85facbde1", "tarball": "https://registry.npmjs.org/i/-/i-0.3.0.tgz", "integrity": "sha512-E782tRXYr/aBjhfkfk82gLMQQVjOrV+5Oy0JQYJRcePiB+n6FovrpkGVlDL1rK9pEtIEu/Ki6qWBGStOlSM5xA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICeS2Gi+31DLPSF9TxgM2HJUplwxDG4pCI55cu0uQ9hCAiBqOfFG1a4yiSBM41cQFACdr+n2QbotlkrFNDiWTeT46g=="}]}, "maintainers": [{"name": "pksunkara", "email": "<EMAIL>"}], "directories": {}}, "0.3.1": {"name": "i", "version": "0.3.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "pksunkara.github.com"}, "description": "custom inflections for nodejs", "main": "./lib/inflect", "repository": {"type": "git", "url": "git://github.com/pksunkara/inflect.git"}, "keywords": ["singular", "plural", "camelize", "underscore", "dasherize", "demodulize", "ordinalize", "uncountable", "pluralize", "singularize", "titleize", "tableize", "classify", "foreign_key"], "homepage": "http://pksunkara.github.com/inflect", "scripts": {"test": "./node_modules/.bin/vows --spec $(find test -name '*-test.js')"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "dependencies": {}, "devDependencies": {"vows": "~0.6.1"}, "engines": {"node": ">=0.4"}, "bugs": {"url": "https://github.com/pksunkara/inflect/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/pksunkara/inflect/raw/master/LICENSE"}], "_id": "i@0.3.1", "dist": {"shasum": "6ecf7847caed3168b17b86ccc390d7b805be0ee4", "tarball": "https://registry.npmjs.org/i/-/i-0.3.1.tgz", "integrity": "sha512-rxOhIz/9zh4k44zyMHKz5W+286eVrkE6Ia23wybQ3r0utfLRHvLMWWO/Xhat+rM3aqrRZMLjcrnBl+guPhoyYw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICkNhZtjnjL201pAFxrQxVc49Asw3vD63ZRLlzUJe/1KAiEAldZCA9a9CRG8hp+3Xkg3KfJ5tZOOlky8Ap1RxEe27rQ="}]}, "_npmVersion": "1.1.54", "_npmUser": {"name": "pksunkara", "email": "<EMAIL>"}, "maintainers": [{"name": "pksunkara", "email": "<EMAIL>"}], "directories": {}}, "0.3.3": {"name": "i", "version": "0.3.3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "pksunkara.github.com"}, "description": "custom inflections for nodejs", "main": "./lib/inflect", "repository": {"type": "git", "url": "git://github.com/pksunkara/inflect.git"}, "keywords": ["singular", "plural", "camelize", "underscore", "dasherize", "demodulize", "ordinalize", "uncountable", "pluralize", "singularize", "titleize", "tableize", "classify", "foreign_key"], "homepage": "http://pksunkara.github.com/inflect", "scripts": {"test": "vows --spec $(find test -name '*-test.js')"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "dependencies": {}, "devDependencies": {"vows": "~0.6.1"}, "engines": {"node": ">=0.4"}, "bugs": {"url": "https://github.com/pksunkara/inflect/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/pksunkara/inflect/raw/master/LICENSE"}], "gitHead": "f6495e42873fe5b3ca80b060bc274662c3ed610b", "_id": "i@0.3.3", "_shasum": "0ff9a5eb743504aa8ac26a84f84c641287ff24cd", "_from": ".", "_npmVersion": "2.0.0", "_npmUser": {"name": "pksunkara", "email": "<EMAIL>"}, "maintainers": [{"name": "pksunkara", "email": "<EMAIL>"}], "dist": {"shasum": "0ff9a5eb743504aa8ac26a84f84c641287ff24cd", "tarball": "https://registry.npmjs.org/i/-/i-0.3.3.tgz", "integrity": "sha512-K6WZ4VnF9cB7hYawriC3cF0iVa74DIio5FQ0yQb6AkbiLWOBfgYX1U2wsjU7HW5S/DDZ3nPOf4nsczxRX7CEug==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGlIo9/f/Q7Vq4SXymYfuZhE8HjNCr0O81iArGpoTW03AiA17pMNE3aesJOzfIcSqHLu4z3MLR1x3l6HB9vrsK3qdw=="}]}, "directories": {}}, "0.3.4": {"name": "i", "version": "0.3.4", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "pksunkara.github.com"}, "description": "custom inflections for nodejs", "main": "./lib/inflect", "repository": {"type": "git", "url": "git://github.com/pksunkara/inflect.git"}, "keywords": ["singular", "plural", "camelize", "underscore", "dasherize", "demodulize", "ordinalize", "uncountable", "pluralize", "singularize", "titleize", "tableize", "classify", "foreign_key"], "homepage": "http://pksunkara.github.com/inflect", "scripts": {"test": "vows --spec $(find test -name '*-test.js')"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "dependencies": {}, "devDependencies": {"vows": "0.7.0"}, "engines": {"node": ">=0.4"}, "bugs": {"url": "https://github.com/pksunkara/inflect/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/pksunkara/inflect/raw/master/LICENSE"}], "gitHead": "355009895db1bd8dcceaa687b53fe085fc873b88", "_id": "i@0.3.4", "_shasum": "e1918d417b363a544e0e4a9b83c36dce1f85c91d", "_from": ".", "_npmVersion": "2.14.9", "_nodeVersion": "0.12.9", "_npmUser": {"name": "pksunkara", "email": "<EMAIL>"}, "maintainers": [{"name": "pksunkara", "email": "<EMAIL>"}], "dist": {"shasum": "e1918d417b363a544e0e4a9b83c36dce1f85c91d", "tarball": "https://registry.npmjs.org/i/-/i-0.3.4.tgz", "integrity": "sha512-KDaYtwpldOf0yqMEpybHh/0u7CVUpsJNzePcRypOi3icVlJm3mVMR58WLbK8fqBYUuS/Ank71PhNeb9FgeqEmg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDdsuAMIX/wtP/PCbX7iMSTU/8qE7eMgeSEQeq+ZaT9iQIhALdvOSHBdEgv4uPGe1PuXDedpAmxD/nvdECvonc9GLbR"}]}, "directories": {}}, "0.3.5": {"name": "i", "version": "0.3.5", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "pksunkara.github.com"}, "description": "custom inflections for nodejs", "main": "./lib/inflect", "repository": {"type": "git", "url": "git://github.com/pksunkara/inflect.git"}, "keywords": ["singular", "plural", "camelize", "underscore", "dasherize", "demodulize", "ordinalize", "uncountable", "pluralize", "singularize", "titleize", "tableize", "classify", "foreign_key"], "homepage": "http://pksunkara.github.com/inflect", "scripts": {"test": "vows --spec $(find test -name '*-test.js')"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "dependencies": {}, "devDependencies": {"vows": "0.7.0"}, "engines": {"node": ">=0.4"}, "bugs": {"url": "https://github.com/pksunkara/inflect/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/pksunkara/inflect/raw/master/LICENSE"}], "gitHead": "bbcadd57a182007ab158acea511515b815385d4d", "_id": "i@0.3.5", "_shasum": "1d2b854158ec8169113c6cb7f6b6801e99e211d5", "_from": ".", "_npmVersion": "2.14.9", "_nodeVersion": "0.12.9", "_npmUser": {"name": "pksunkara", "email": "<EMAIL>"}, "dist": {"shasum": "1d2b854158ec8169113c6cb7f6b6801e99e211d5", "tarball": "https://registry.npmjs.org/i/-/i-0.3.5.tgz", "integrity": "sha512-XFmJQOpUegDZ9oXorK3AjimFzK//YLoDtGzwJ4xmop/4bRDtEWcbAwlEwDiJrwv5kPhbUVWXObnmmvkCFtjw3Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCFEE1MHYltgj1zymQAIwz0u5Mq3jzeOEBS9yDXiayKtwIhAJs9aqhcWh64q7zEaTV0LsLHyPiTo8adE/fLlsmRgn4W"}]}, "maintainers": [{"name": "pksunkara", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/i-0.3.5.tgz_1462355680372_0.1744775390252471"}, "directories": {}}, "0.3.6": {"name": "i", "version": "0.3.6", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "pksunkara.github.com"}, "description": "custom inflections for nodejs", "main": "./lib/inflect", "repository": {"type": "git", "url": "git://github.com/pksunkara/inflect.git"}, "keywords": ["singular", "plural", "camelize", "underscore", "dasherize", "demodulize", "ordinalize", "uncountable", "pluralize", "singularize", "titleize", "tableize", "classify", "foreign_key"], "homepage": "http://pksunkara.github.com/inflect", "scripts": {"test": "vows --spec $(find test -name '*-test.js')"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "dependencies": {}, "devDependencies": {"vows": "0.7.0"}, "engines": {"node": ">=0.4"}, "bugs": {"url": "https://github.com/pksunkara/inflect/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/pksunkara/inflect/raw/master/LICENSE"}], "gitHead": "6bf6ca463906704775e0e91c86e365eba7f336c0", "_id": "i@0.3.6", "_shasum": "d96c92732076f072711b6b10fd7d4f65ad8ee23d", "_from": ".", "_npmVersion": "4.6.1", "_nodeVersion": "8.1.4", "_npmUser": {"name": "pksunkara", "email": "<EMAIL>"}, "dist": {"shasum": "d96c92732076f072711b6b10fd7d4f65ad8ee23d", "tarball": "https://registry.npmjs.org/i/-/i-0.3.6.tgz", "integrity": "sha512-L3HtskNuuoYuBblACAG00UjaVSUb/09eLpkWEWcJ7n+/SmC1XikEckdXYpWYiVU+oFbvqp9GyGGwtzm21mDUOQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDkOyk1Otp2ZAkLqlR63OiQojk1Ev864W3h8iG7+TCnsgIhAOCxuWcbpG80/S+G/7jSJRQTFgn/eBmtUWYY9bM1Xt3N"}]}, "maintainers": [{"name": "pksunkara", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/i-0.3.6.tgz_1507045364530_0.33664347720332444"}, "directories": {}}, "0.3.7": {"name": "i", "version": "0.3.7", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "pksunkara.github.com"}, "description": "custom inflections for nodejs", "main": "./lib/inflect", "repository": {"type": "git", "url": "git://github.com/pksunkara/inflect.git"}, "keywords": ["singular", "plural", "camelize", "underscore", "dasherize", "demodulize", "ordinalize", "uncountable", "pluralize", "singularize", "titleize", "tableize", "classify", "foreign_key"], "homepage": "http://pksunkara.github.com/inflect", "scripts": {"test": "vows --spec $(find test -name '*-test.js')"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "dependencies": {}, "devDependencies": {"vows": "^0.8.2"}, "engines": {"node": ">=0.4"}, "bugs": {"url": "https://github.com/pksunkara/inflect/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/pksunkara/inflect/raw/master/LICENSE"}], "gitHead": "71961bd70c2f516e03a1a73538f783ada3aab2c0", "_id": "i@0.3.7", "_nodeVersion": "12.22.6", "_npmVersion": "7.21.0", "dist": {"integrity": "sha512-FYz4wlXgkQwIPqhzC5TdNMLSE5+GS1IIDJZY/1ZiEPCT2S3COUVZeT5OW4BmW4r5LHLQuOosSwsvnroG9GR59Q==", "shasum": "2a7437a923d59c14b17243dc63a549af24d85799", "tarball": "https://registry.npmjs.org/i/-/i-0.3.7.tgz", "fileCount": 16, "unpackedSize": 56146, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2t3vCRA9TVsSAnZWagAA01wP/RDhWtC+TBwtArYXcA1k\nmB7VIXo11fHN2Hv1V5zlzq8vUfJJd/fmXzpUcp9l21TNGOM5EJloQ8wUyaoz\nJYcnk9xFIRtlZrnMofG9XaNlrXHdU0ZHi04waRW8+ImN0XKlYdHAvbYSzFyf\nRgMzeHm9zbNIFlVDFB+zKZyusi+oaVMqjqbn12xI3VYtYkLZNCNClxEUtQxi\nuavxCqbr8EBHRu3HYzCOYS+ciPKvt4I9KDrLjEewWAvNSe/TXmuoSS5ACaHi\nTaVer50c4/Rp3eas3txXlAAX8WVPtmNSzzUA5HHVenEktjUsB1y3QrjZHo65\nv7rPMKIx5piUvqr1/4MBmyoLQEVdEjiKVw+xUkinadvhcy+7lPJFwrbr35zY\nV1OxYt9FwM6Cn7YQBkHDag/g/BzHppYj3MubgaAK1brCUT01vzvnSH0aiXKb\nhsbhNVCGbAlzz/+rrxyNztwpbyq+dU34K0ihc4WvTuMilFres+jhO1cQKK/T\nC+LeSAfv4PTSFXAx8w17oEOx57uLuS6piAV8d1LbHVvx4FcwdTp9R33zDxbf\nrT4V8/xYjWPuG3fCnOBShIYOXTTegzxv45/jvIBC8Y+UkDVfvWEQexE/77RD\nXTCZXbjqFGiKmG7kJLfr55u+Yz9ywtrcQS2SDBCEaGHcMT7XGn8+a3u/+aa5\nS9yA\r\n=sfRx\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB5808NOIKabgt+cApIYyFOZH1/znXWHQE2jRWpWQcBLAiBt2wVJgAjNtdOyTqzWt0cweSR/vd0axMr+or152soMOQ=="}]}, "_npmUser": {"name": "pksunkara", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pksunkara", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/i_0.3.7_1632221432344_0.7391110368311664"}, "_hasShrinkwrap": false}}, "readme": "# inflect\n\ncustomizable inflections for nodejs\n\n**NOTE: 0.3.2 was accidentally unpublished from the server and npm doesn't allow me to publish it back. Please upgrade to 0.3.3**\n\n## Installation\n\n```bash\nnpm install i\n```\n\n## Usage\n\nRequire the module before using\n\n```js\nvar inflect = require('i')();\n```\n\nAll the below api functions can be called directly on a string\n\n```js\ninflect.titleize('messages to store') // === 'Messages To Store'\n'messages to store'.titleize // === 'Messages To Store'\n```\n\nonly if `true` is passed while initiating\n\n```js\nvar inflect = require('i')(true);\n```\n\n### Pluralize\n\n```js\ninflect.pluralize('person'); // === 'people'\ninflect.pluralize('octopus'); // === 'octopi'\ninflect.pluralize('Hat'); // === 'Hats'\n```\n\n### Singularize\n\n```js\ninflect.singularize('people'); // === 'person'\ninflect.singularize('octopi'); // === 'octopus'\ninflect.singularize('Hats'); // === 'Hat'\n```\n\n### Camelize\n\n```js\ninflect.camelize('message_properties'); // === 'MessageProperties'\ninflect.camelize('message_properties', false); // === 'messageProperties'\n```\n\n### Underscore\n\n```js\ninflect.underscore('MessageProperties'); // === 'message_properties'\ninflect.underscore('messageProperties'); // === 'message_properties'\n```\n\n### Humanize\n\n```js\ninflect.humanize('message_id'); // === 'Message'\n```\n\n### Dasherize\n\n```js\ninflect.dasherize('message_properties'); // === 'message-properties'\ninflect.dasherize('Message Properties'); // === 'Message Properties'\n```\n\n### Titleize\n\n```js\ninflect.titleize('message_properties'); // === 'Message Properties'\ninflect.titleize('message properties to keep'); // === 'Message Properties to Keep'\n```\n\n### Demodulize\n\n```js\ninflect.demodulize('Message.Bus.Properties'); // === 'Properties'\n```\n\n### Tableize\n\n```js\ninflect.tableize('MessageBusProperty'); // === 'message_bus_properties'\n```\n\n### Classify\n\n```js\ninflect.classify('message_bus_properties'); // === 'MessageBusProperty'\n```\n\n### Foreign key\n\n```js\ninflect.foreign_key('MessageBusProperty'); // === 'message_bus_property_id'\ninflect.foreign_key('MessageBusProperty', false); // === 'message_bus_propertyid'\n```\n\n### Ordinalize\n\n```js\ninflect.ordinalize( '1' ); // === '1st'\n```\n\n## Custom rules for inflection\n\n### Custom plural\n\nWe can use regexp in any of these custom rules\n\n```js\ninflect.inflections.plural('person', 'guys');\ninflect.pluralize('person'); // === 'guys'\ninflect.singularize('guys'); // === 'guy'\n```\n\n### Custom singular\n\n```js\ninflect.inflections.singular('guys', 'person')\ninflect.singularize('guys'); // === 'person'\ninflect.pluralize('person'); // === 'people'\n```\n\n### Custom irregular\n\n```js\ninflect.inflections.irregular('person', 'guys')\ninflect.pluralize('person'); // === 'guys'\ninflect.singularize('guys'); // === 'person'\n```\n\n### Custom human\n\n```js\ninflect.inflections.human(/^(.*)_cnt$/i, '$1_count');\ninflect.humanize('jargon_cnt'); // === 'Jargon count'\n```\n\n### Custom uncountable\n\n```js\ninflect.inflections.uncountable('oil')\ninflect.pluralize('oil'); // === 'oil'\ninflect.singularize('oil'); // === 'oil'\n```\n\n## Contributors\nHere is a list of [Contributors](http://github.com/pksunkara/inflect/contributors)\n\n### TODO\n\n- More obscure test cases\n\n__I accept pull requests and guarantee a reply back within a day__\n\n## License\nMIT/X11\n\n## Bug Reports\nReport [here](http://github.com/pksunkara/inflect/issues). __Guaranteed reply within a day__.\n\n## Contact\nPavan Kumar Sunkara (<EMAIL>)\n\nFollow me on [github](https://github.com/users/follow?target=pksunkara), [twitter](http://twitter.com/pksunkara)\n", "maintainers": [{"name": "pksunkara", "email": "<EMAIL>"}], "time": {"modified": "2024-04-07T09:52:46.734Z", "created": "2012-02-22T14:20:23.060Z", "0.2.0": "2012-02-22T14:20:25.535Z", "0.3.0": "2012-06-01T04:59:19.189Z", "0.3.1": "2012-09-18T10:49:47.315Z", "0.3.2": "2013-08-14T07:44:33.829Z", "0.3.3": "2015-04-02T18:40:55.722Z", "0.3.4": "2016-01-14T20:05:31.241Z", "0.3.5": "2016-05-04T09:54:41.664Z", "0.3.6": "2017-10-03T15:42:45.519Z", "0.3.7": "2021-09-21T10:50:32.501Z"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "pksunkara.github.com"}, "repository": {"type": "git", "url": "git://github.com/pksunkara/inflect.git"}, "users": {"kastor": true, "hollingberry": true, "ninozhang": true, "itonyyo": true, "luissquall": true, "hoanganh25991": true, "gurunate": true, "derflatulator": true, "kkk123321": true, "cheapsteak": true, "alfeo92": true, "idanlaav": true, "siriuscore": true, "853ness": true}, "homepage": "http://pksunkara.github.com/inflect", "keywords": ["singular", "plural", "camelize", "underscore", "dasherize", "demodulize", "ordinalize", "uncountable", "pluralize", "singularize", "titleize", "tableize", "classify", "foreign_key"], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/pksunkara/inflect/issues"}, "readmeFilename": "README.md"}