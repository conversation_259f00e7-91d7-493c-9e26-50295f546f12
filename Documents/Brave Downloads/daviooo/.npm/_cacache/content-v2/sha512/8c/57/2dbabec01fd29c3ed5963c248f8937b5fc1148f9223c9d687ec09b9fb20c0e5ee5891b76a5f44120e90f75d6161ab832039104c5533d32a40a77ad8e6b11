{"_id": "clone-response", "_rev": "15-79e96aeb3c405edec4309464a4a619b7", "name": "clone-response", "description": "Clone a Node.js HTTP response stream", "dist-tags": {"latest": "2.0.0"}, "versions": {"0.0.0": {"name": "clone-response", "version": "0.0.0", "description": "Clone a Node.js response object", "main": "src/index.js", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "xo": {"extends": "xo-lukechilds"}, "repository": {"type": "git", "url": "git+https://github.com/lukechilds/clone-response.git"}, "keywords": ["async", "asynchronous", "non-blocking", "base64", "encode", "decode"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://lukechilds.co.uk"}, "license": "MIT", "bugs": {"url": "https://github.com/lukechilds/clone-response/issues"}, "homepage": "https://github.com/lukechilds/clone-response", "dependencies": {}, "devDependencies": {"ava": "^0.19.1", "coveralls": "^2.13.1", "eslint-config-xo-lukechilds": "^1.0.0", "nyc": "^10.3.2", "xo": "^0.19.0"}, "gitHead": "eb11503122152fc224e75593d56eecf1e9e0bba6", "_id": "clone-response@0.0.0", "_npmVersion": "5.0.0", "_nodeVersion": "8.0.0", "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Zze/x9w0nRaaCi2+hgU5GkmzT4D0izCj89YGIGiteCVDFqs5nXdw6gIvZ3XgKDTEnZTVqR0XfyucdH+rL7sz2A==", "shasum": "14c14a912cc34fba56c3ddb996ac3e70aec17b0f", "tarball": "https://registry.npmjs.org/clone-response/-/clone-response-0.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHfsp6EMtXEpFLvWo4AU+xKoumVo40ZYNHcwNgtPznsYAiEAumWhxtW+B0vmF2/iQM8YK75X04t5xXdrl9koHNwjStE="}]}, "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/clone-response-0.0.0.tgz_1496313212628_0.3775720295961946"}, "directories": {}}, "0.1.0": {"name": "clone-response", "version": "0.1.0", "description": "Clone a Node.js response object", "main": "src/index.js", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "xo": {"extends": "xo-lukechilds"}, "repository": {"type": "git", "url": "git+https://github.com/lukechilds/clone-response.git"}, "keywords": ["async", "asynchronous", "non-blocking", "base64", "encode", "decode"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://lukechilds.co.uk"}, "license": "MIT", "bugs": {"url": "https://github.com/lukechilds/clone-response/issues"}, "homepage": "https://github.com/lukechilds/clone-response", "dependencies": {}, "devDependencies": {"ava": "^0.19.1", "coveralls": "^2.13.1", "create-test-server": "^0.1.0", "eslint-config-xo-lukechilds": "^1.0.0", "get-stream": "^3.0.0", "nyc": "^10.3.2", "rfpify": "^1.0.0", "xo": "^0.19.0"}, "gitHead": "cb68480b467474ee01fe521d016a557a68c440a3", "_id": "clone-response@0.1.0", "_npmVersion": "5.0.0", "_nodeVersion": "8.0.0", "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-ts+zUMLrGd9f3iO9LUk+qEcKJSiP4tqO+1Oli6RSlx5Ha4b2cI5QScz9SAzjaslBicOMQOz76L28zdikidqXvA==", "shasum": "ee9ba731a99c70668b006eaebf3f1503df40836a", "tarball": "https://registry.npmjs.org/clone-response/-/clone-response-0.1.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDVh4ExzYnqNyDdfBTuYWyahlS7SS5LLINerjsUFrnD3AIgdmWS+9X10xRzKCMFCQMfP3Vydi8isaSUo3fSLr768KM="}]}, "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/clone-response-0.1.0.tgz_1496390670787_0.1935164975002408"}, "directories": {}}, "0.1.1": {"name": "clone-response", "version": "0.1.1", "description": "Clone a Node.js response object", "main": "src/index.js", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "xo": {"extends": "xo-lukechilds"}, "repository": {"type": "git", "url": "git+https://github.com/lukechilds/clone-response.git"}, "keywords": ["async", "asynchronous", "non-blocking", "base64", "encode", "decode"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://lukechilds.co.uk"}, "license": "MIT", "bugs": {"url": "https://github.com/lukechilds/clone-response/issues"}, "homepage": "https://github.com/lukechilds/clone-response", "dependencies": {}, "devDependencies": {"ava": "^0.19.1", "coveralls": "^2.13.1", "create-test-server": "^0.1.0", "eslint-config-xo-lukechilds": "^1.0.0", "get-stream": "^3.0.0", "nyc": "^10.3.2", "rfpify": "^1.0.0", "xo": "^0.19.0"}, "gitHead": "86f4f00e61905a3921b0a4d43cccda8871b7dea4", "_id": "clone-response@0.1.1", "_npmVersion": "5.0.0", "_nodeVersion": "8.0.0", "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-LfqdnW4bZ3A2jr8tL3GJANzfJ//mPCdNzPSIudkvtRuf1T4xd6vE9aj2KKr0px/7o+ldEiLPpq/sSAkYL6B8MQ==", "shasum": "6dc95e0ee70f7262585a8743587a327847b21519", "tarball": "https://registry.npmjs.org/clone-response/-/clone-response-0.1.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCXQjj9SQQJBzF3bb9YyVDKJo4bNqcozsSI6d3Lj7xKwgIhAIstGqxhgR7ldtlL4VYCne1zLhnOLXPHuApuGEJIe0ZO"}]}, "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/clone-response-0.1.1.tgz_1496390993550_0.6186737371608615"}, "directories": {}}, "0.2.0": {"name": "clone-response", "version": "0.2.0", "description": "Clone a Node.js response object", "main": "src/index.js", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "xo": {"extends": "xo-lukechilds"}, "repository": {"type": "git", "url": "git+https://github.com/lukechilds/clone-response.git"}, "keywords": ["async", "asynchronous", "non-blocking", "base64", "encode", "decode"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://lukechilds.co.uk"}, "license": "MIT", "bugs": {"url": "https://github.com/lukechilds/clone-response/issues"}, "homepage": "https://github.com/lukechilds/clone-response", "dependencies": {}, "devDependencies": {"ava": "^0.19.1", "coveralls": "^2.13.1", "create-test-server": "^0.1.0", "eslint-config-xo-lukechilds": "^1.0.0", "get-stream": "^3.0.0", "nyc": "^10.3.2", "rfpify": "^1.0.0", "xo": "^0.19.0"}, "gitHead": "1e4c18b99aafee599260bfeb2eaa82ec36c13fc1", "_id": "clone-response@0.2.0", "_npmVersion": "5.0.0", "_nodeVersion": "8.0.0", "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Qi6+0zKEcRkehT6A5TFQ2rOfL7Q5qp9xHARvMiyXiNdLFdm6o2zbSqjEtnyTu/hvFRPJaLhFZNAiKXmtz+wW5w==", "shasum": "5cff071a359afc43c0199ba673356a948482cadb", "tarball": "https://registry.npmjs.org/clone-response/-/clone-response-0.2.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDohB05YcoNsla6mFaNNOmRVbwhs4gZpyZ4lX+GnN89AAiBbrMD8msHL8dQElgGvFSGSRv3iQ2S9zTRjszbZXv8EJw=="}]}, "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/clone-response-0.2.0.tgz_1496392152797_0.6868132923264056"}, "directories": {}}, "0.2.1": {"name": "clone-response", "version": "0.2.1", "description": "Clone a Node.js response object", "main": "src/index.js", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "xo": {"extends": "xo-lukechilds"}, "repository": {"type": "git", "url": "git+https://github.com/lukechilds/clone-response.git"}, "keywords": ["async", "asynchronous", "non-blocking", "base64", "encode", "decode"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://lukechilds.co.uk"}, "license": "MIT", "bugs": {"url": "https://github.com/lukechilds/clone-response/issues"}, "homepage": "https://github.com/lukechilds/clone-response", "dependencies": {"mimic-response": "^1.0.0"}, "devDependencies": {"ava": "^0.19.1", "coveralls": "^2.13.1", "create-test-server": "^0.1.0", "eslint-config-xo-lukechilds": "^1.0.0", "get-stream": "^3.0.0", "nyc": "^10.3.2", "pify": "^3.0.0", "xo": "^0.19.0"}, "gitHead": "9b1128d5758d4669d4f1b4db6ef6a7cb5495ed65", "_id": "clone-response@0.2.1", "_npmVersion": "5.0.0", "_nodeVersion": "8.0.0", "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-CRslSnR3z9MQ/8MNShNRQBwTzI7SI1GWWRQEc8+/dgbefXUoD7/S3pZQhF1YeIS3237owLg5IFkuo52X4nBAEA==", "shasum": "b7b2fa85a083f5b4b467af31ee6a766ad99d9342", "tarball": "https://registry.npmjs.org/clone-response/-/clone-response-0.2.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC+cIV6/Y3bX1YrLVJZZPBW94lfpt+VWihbrwhqkS/m+wIhANPGW4n6NrSVDZD5+GLNsmGG7wNQQJ09gd5hR2sF/YQA"}]}, "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/clone-response-0.2.1.tgz_1496460501457_0.03791434480808675"}, "directories": {}}, "0.2.2": {"name": "clone-response", "version": "0.2.2", "description": "Clone a Node.js response object", "main": "src/index.js", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "xo": {"extends": "xo-lukechilds"}, "repository": {"type": "git", "url": "git+https://github.com/lukechilds/clone-response.git"}, "keywords": ["clone", "duplicate", "copy", "response"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://lukechilds.co.uk"}, "license": "MIT", "bugs": {"url": "https://github.com/lukechilds/clone-response/issues"}, "homepage": "https://github.com/lukechilds/clone-response", "dependencies": {"mimic-response": "^1.0.0"}, "devDependencies": {"ava": "^0.19.1", "coveralls": "^2.13.1", "create-test-server": "^0.1.0", "eslint-config-xo-lukechilds": "^1.0.0", "get-stream": "^3.0.0", "nyc": "^10.3.2", "pify": "^3.0.0", "xo": "^0.19.0"}, "gitHead": "b942a1f08548c5d82ff6770ffa39a6a266fc223e", "_id": "clone-response@0.2.2", "_npmVersion": "5.0.0", "_nodeVersion": "8.0.0", "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-fZt1B3Fj5A13LBBKcGQhmxEz1pQyG8SVMvWdC+4WEIpnZrvRsqDZDkiqmaT00HASlGDUxMxyGye/gH2LO2zWMA==", "shasum": "d8f8c277f7a4f22653585130bd834c2266c77c6e", "tarball": "https://registry.npmjs.org/clone-response/-/clone-response-0.2.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD4pGrtvIqwOBC/eLRbpyAcru5L0UCSwG8wLfTIVO5FTgIgVaM9T5/pKdkTOzRaWBHbQBw4x5wzSSatqI1hefpXlwM="}]}, "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/clone-response-0.2.2.tgz_1496468080100_0.45025671273469925"}, "directories": {}}, "1.0.0": {"name": "clone-response", "version": "1.0.0", "description": "Clone a Node.js HTTP response stream", "main": "src/index.js", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "xo": {"extends": "xo-lukechilds"}, "repository": {"type": "git", "url": "git+https://github.com/lukechilds/clone-response.git"}, "keywords": ["clone", "duplicate", "copy", "response", "HTTP", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://lukechilds.co.uk"}, "license": "MIT", "bugs": {"url": "https://github.com/lukechilds/clone-response/issues"}, "homepage": "https://github.com/lukechilds/clone-response", "dependencies": {"mimic-response": "^1.0.0"}, "devDependencies": {"ava": "^0.19.1", "coveralls": "^2.13.1", "create-test-server": "^1.1.1", "eslint-config-xo-lukechilds": "^1.0.0", "get-stream": "^3.0.0", "nyc": "^10.3.2", "pify": "^3.0.0", "xo": "^0.19.0"}, "gitHead": "45bad5bda2ebcb5c510638273ba53147d61acd8f", "_id": "clone-response@1.0.0", "_npmVersion": "5.0.3", "_nodeVersion": "8.0.0", "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-dIA1J7HryFHdlfo7GJ+2wypKWKYOvLEU7oNLOZPoBc4qIM0tdJ0B0aZv6LWNd48ehlSN6ZsByGdXqwIX/jwuuA==", "shasum": "098632e140f04cdca509c6926c7ad3a8c44f36a6", "tarball": "https://registry.npmjs.org/clone-response/-/clone-response-1.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDPdEMAHGelZu1w87cQmG2goZh68GNZoXTmzCPUeFPPSAiBBrrXP4QRSjSejToYsq/QCj5o8Oa/4knmS4RkwJTF46w=="}]}, "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/clone-response-1.0.0.tgz_1497615089253_0.2357099496293813"}, "directories": {}}, "1.0.1": {"name": "clone-response", "version": "1.0.1", "description": "Clone a Node.js HTTP response stream", "main": "src/index.js", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "xo": {"extends": "xo-lukechilds"}, "repository": {"type": "git", "url": "git+https://github.com/lukechilds/clone-response.git"}, "keywords": ["clone", "duplicate", "copy", "response", "HTTP", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://lukechilds.co.uk"}, "license": "MIT", "bugs": {"url": "https://github.com/lukechilds/clone-response/issues"}, "homepage": "https://github.com/lukechilds/clone-response", "dependencies": {"mimic-response": "^1.0.0"}, "devDependencies": {"ava": "^0.20.0", "coveralls": "^2.13.1", "create-test-server": "^2.0.1", "eslint-config-xo-lukechilds": "^1.0.0", "get-stream": "^3.0.0", "nyc": "^11.0.2", "pify": "^3.0.0", "xo": "^0.19.0"}, "gitHead": "b90422a12709acb5d15e0b462c3623d06fb62923", "_id": "clone-response@1.0.1", "_shasum": "35ecdb6235f5a064979a8ef263325cc75da6d40e", "_from": ".", "_npmVersion": "4.6.1", "_nodeVersion": "8.1.2", "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "dist": {"shasum": "35ecdb6235f5a064979a8ef263325cc75da6d40e", "tarball": "https://registry.npmjs.org/clone-response/-/clone-response-1.0.1.tgz", "integrity": "sha512-FGsUFRp9Okp7UPNJ9qmzQJd6enRct2vt2VfxkxsY4UtEEaClN2M1146orz+gAfcRHTvxx2jY117lXjzM2l2CyA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEK6z0csJ7/UKOUizt8fKIRSCYE7Cxxzpazy7WeVJAdyAiBJxxrLMUUPSHFUt7B+uruteqFnDsLRG6KAo5R02Y8NVA=="}]}, "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/clone-response-1.0.1.tgz_1498827743228_0.20690521295182407"}, "directories": {}}, "1.0.2": {"name": "clone-response", "version": "1.0.2", "description": "Clone a Node.js HTTP response stream", "main": "src/index.js", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "xo": {"extends": "xo-lukechilds"}, "repository": {"type": "git", "url": "git+https://github.com/lukechilds/clone-response.git"}, "keywords": ["clone", "duplicate", "copy", "response", "HTTP", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://lukechilds.co.uk"}, "license": "MIT", "bugs": {"url": "https://github.com/lukechilds/clone-response/issues"}, "homepage": "https://github.com/lukechilds/clone-response", "dependencies": {"mimic-response": "^1.0.0"}, "devDependencies": {"ava": "^0.22.0", "coveralls": "^2.13.1", "create-test-server": "^2.0.1", "eslint-config-xo-lukechilds": "^1.0.0", "get-stream": "^3.0.0", "nyc": "^11.0.2", "pify": "^3.0.0", "xo": "^0.19.0"}, "gitHead": "857094916c5393a7ce78838a4e447fa944263cca", "_id": "clone-response@1.0.2", "_shasum": "d1dc973920314df67fbeb94223b4ee350239e96b", "_from": ".", "_npmVersion": "4.6.1", "_nodeVersion": "8.4.0", "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "dist": {"shasum": "d1dc973920314df67fbeb94223b4ee350239e96b", "tarball": "https://registry.npmjs.org/clone-response/-/clone-response-1.0.2.tgz", "integrity": "sha512-yjLXh88P599UOyPTFX0POsd7WxnbsVsGohcwzHOLspIhhpalPw1BcqED8NblyZLKcGrL8dTgMlcaZxV2jAD41Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCchRqgDa9tyXJT8TvspzdVcGRSb0/s2r+GDdQSmtWv/wIgVsY7yH7RKYk8Bzp7UdKN80RpyyRmRVJdJhQK0E077ZE="}]}, "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/clone-response-1.0.2.tgz_1502988274674_0.8035625964403152"}, "directories": {}}, "1.0.3": {"name": "clone-response", "version": "1.0.3", "description": "Clone a Node.js HTTP response stream", "main": "src/index.js", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "funding": "https://github.com/sponsors/sindresorhus", "xo": {"extends": "xo-lukechilds"}, "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/clone-response.git"}, "keywords": ["clone", "duplicate", "copy", "response", "HTTP", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://lukechilds.co.uk"}, "license": "MIT", "dependencies": {"mimic-response": "^1.0.0"}, "devDependencies": {"ava": "^0.22.0", "coveralls": "^2.13.1", "create-test-server": "^2.0.1", "eslint-config-xo-lukechilds": "^1.0.0", "get-stream": "^3.0.0", "nyc": "^11.0.2", "pify": "^3.0.0", "xo": "^0.19.0"}, "gitHead": "b113fa538f5b9c5520e1adfdfa8d73afe437c700", "bugs": {"url": "https://github.com/sindresorhus/clone-response/issues"}, "homepage": "https://github.com/sindresorhus/clone-response#readme", "_id": "clone-response@1.0.3", "_nodeVersion": "14.19.3", "_npmVersion": "8.3.2", "dist": {"integrity": "sha512-ROoL94jJH2dUVML2Y/5PEDNaSHgeOdSDicUyS7izcF63G6sTc/FTjLub4b8Il9S8S0beOfYt0TaA5qvFK+w0wA==", "shasum": "af2032aa47816399cf5f0a1d0db902f517abb8c3", "tarball": "https://registry.npmjs.org/clone-response/-/clone-response-1.0.3.tgz", "fileCount": 4, "unpackedSize": 4526, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDetbaU/AEVbGF09LarTHOFxGeEy63A1bQUoLmjqjBS6gIhAKyHGSopE02tGcuNO+TckqSg3fNqz8IgzMSk/Rod2gcz"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1CQdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpn6BAAmBVohVLL/R054d9YXqstR763hBGCKDd6CB52GWySEp4hCRjT\r\n2yf6GCVKwSZBtj0AuwpzMpT5yoUCZOGZYK+NBFGA5hizGTR/V/XrOS5/NiXu\r\n+OdyQDNA2rJFUNPJgc7ujvur6IDKvRhnL/dNWZGcGURN5gkpJVTxcJ8h3muu\r\nBsf5X8cS5X0CNIjFz8zZNMnreGiRus9iEc/LfCfJjVt7Fmy1pwzoNrjnGhzf\r\nZgQ5mBm8Lub6eUQ3fUkMB+2VOWwwmYZc/ezI9I0e1vnxKbpZK8xGuYaZ97JW\r\n7l9+BEVQDIXQ5K65subdUk4JjSzEsEYfBTQRSDcNIYdrnLRHDZ+aaipmuWJd\r\nL/F3J5vroww/+a1JShHgK0WXplpoa6HtQu71rBAPyl6w07ghilb9VihiPeUW\r\n1XvUHq2SFgVAQ9qE+hcX8lI3+8ElNDt6QOMiMqmcuwzuC6CLbKwifEUobgx9\r\nPBILOCQaSrAjTtTFAgeK8Qt7D3QdPOMBOMqJ2FtIZhHDWkrA53hKcqYEQYf7\r\nRQ6gHCEvnSQRPsgw2wWKrpIUHWegGe/20tFPKh29WcahAa4AwXbVUkGmosP+\r\nvfXhkn4J2BEXymAGrIx/il3MbB3rY+Fi0mkXL3nWQAykKjTnv9MspyT51pFw\r\n8KVqtwiGKJZG6h9tYq2hEF8IUey1ald0lrA=\r\n=siN8\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/clone-response_1.0.3_1658070045760_0.7587340926053368"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "clone-response", "version": "2.0.0", "description": "Clone a Node.js HTTP response stream", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/clone-response.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://lukechilds.co.uk"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=14.16"}, "scripts": {"test": "xo && ava"}, "keywords": ["clone", "response", "duplicate", "copy", "http", "stream"], "dependencies": {"mimic-response": "^4.0.0"}, "devDependencies": {"ava": "^4.3.1", "create-test-server": "^3.0.1", "get-stream": "^6.0.1", "pify": "^6.0.0", "xo": "^0.50.0"}, "gitHead": "3c2392df0d6a7811ef30e4d31c981db3080d9d56", "bugs": {"url": "https://github.com/sindresorhus/clone-response/issues"}, "homepage": "https://github.com/sindresorhus/clone-response#readme", "_id": "clone-response@2.0.0", "_nodeVersion": "14.19.3", "_npmVersion": "8.3.2", "dist": {"integrity": "sha512-2jbaEcptYA32VbtJw/ucElFsFNVPmF+fkXWsH+GgjWX7fZWGosmqXxzAf+5nIeqAzg2HKpHu0zFXGhaRWkQdNw==", "shasum": "6978655600cb6f0bb99ca5f10e7fb0c19ecc56bc", "tarball": "https://registry.npmjs.org/clone-response/-/clone-response-2.0.0.tgz", "fileCount": 4, "unpackedSize": 3869, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDKNXJa6LqWC6cvvBZlcUuSS8TQgn5zWkC4Fzn4Q3Z3dQIgM1rfWNrslofqPy5aD/XdnO69lxHo/Xkwkd5X9HEJSHU="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1CcNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqmfQ/+Pcf2cGqITLv+8csXhr0AWO3axOkzjgtxCVzNpWNAVI6/g0DC\r\ncbUhvMml1nO+tJUZGYobZAftweeGNBNe5VdLogemSGL7QoUskPpm9S/u5Hod\r\ne1H6nUwdyZkb11STqt5lRhpWgd7IIiGzsu/3tMMiE3+XqPug9fVcEGxL1dBJ\r\nPbDGQUHQlWorL8xe1HIHqm24UNWiSWU5U4+fTtFbVTKUbbQ+PsjEh7If+xZI\r\nd/s4+j+YhOepiXtEKQ1iVoPy0+llybmnIzQnuNJlu/kZHgVkdao2l4NgsLps\r\nVgeN0DpIUoo3iVPU9QczEYTuxa0POokp/X4lLxGmw5GkOZ2xBNhRWLkmYVYR\r\nrXetVW4TuaI/bBRtW3xgaETxOC28uEKmEXBLtHQQ1oZ1VuUECCsjWGtd+Wk8\r\nE381C6o8C4sZYPrVjRy+9EGZHT6ezlFnh7ZOh0yFS0YgJujDezjNYc7YsU30\r\nl/Cu6SEwThicB5QemTTJqjFZRP2z33nuU0eV3c16Y2D9IkI9KLX/9wphjZy9\r\n+ag4uZ6CvstO1HVhEH2glvOGmHOks24Owp1tGKpigccILTLz9PtkM0BxVDc+\r\nXpU4Ij70UFWdm1suV7Ck9dnL17wuZobhE5X4w4/J9tgEf1kVIbRqGil0HQnB\r\njffI3P/CYqz0t7SBwXGwJ1a8ZYHvQRfH1UA=\r\n=FCHf\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/clone-response_2.0.0_1658070796968_0.5743072080081504"}, "_hasShrinkwrap": false}}, "readme": "# clone-response\n\n> Clone a Node.js HTTP response stream\n\nReturns a new stream and copies over all properties and methods from the original response giving you a complete duplicate.\n\nThis is useful in situations where you need to consume the response stream but also want to pass an unconsumed stream somewhere else to be consumed later.\n\n## Install\n\n```sh\nnpm install clone-response\n```\n\n## Usage\n\n```js\nimport http from 'node:http';\nimport cloneResponse from 'clone-response';\n\nhttp.get('http://example.com', response => {\n\tconst clonedResponse = cloneResponse(response);\n\tresponse.pipe(process.stdout);\n\n\tsetImmediate(() => {\n\t\t// The response stream has already been consumed by the time this executes,\n\t\t// however the cloned response stream is still available.\n\t\tdoSomethingWithResponse(clonedResponse);\n\t});\n});\n```\n\nPlease bear in mind that the process of cloning a stream consumes it. However, you can consume a stream multiple times in the same tick, therefore allowing you to create multiple clones. For example:\n\n```js\nconst clone1 = cloneResponse(response);\nconst clone2 = cloneResponse(response);\n// The response can still be consumed in this tick but cannot be consumed if passed\n// into any async callbacks. clone1 and clone2 can be passed around and be\n// consumed in the future.\n```\n\n## API\n\n### cloneResponse(response)\n\nReturns a clone of the passed in response stream.\n\n#### response\n\nType: `Stream`\n\nA [Node.js HTTP response stream](https://nodejs.org/api/http.html#http_class_http_incomingmessage) to clone.\n", "maintainers": [{"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "time": {"modified": "2022-10-07T10:29:10.943Z", "created": "2017-06-01T10:33:32.704Z", "0.0.0": "2017-06-01T10:33:32.704Z", "0.1.0": "2017-06-02T08:04:30.901Z", "0.1.1": "2017-06-02T08:09:53.625Z", "0.2.0": "2017-06-02T08:29:13.105Z", "0.2.1": "2017-06-03T03:28:21.554Z", "0.2.2": "2017-06-03T05:34:40.172Z", "1.0.0": "2017-06-16T12:11:29.393Z", "1.0.1": "2017-06-30T13:02:24.398Z", "1.0.2": "2017-08-17T16:44:35.584Z", "1.0.3": "2022-07-17T15:00:45.903Z", "2.0.0": "2022-07-17T15:13:17.135Z"}, "homepage": "https://github.com/sindresorhus/clone-response#readme", "keywords": ["clone", "response", "duplicate", "copy", "http", "stream"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/clone-response.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://lukechilds.co.uk"}, "bugs": {"url": "https://github.com/sindresorhus/clone-response/issues"}, "license": "MIT", "readmeFilename": "readme.md"}