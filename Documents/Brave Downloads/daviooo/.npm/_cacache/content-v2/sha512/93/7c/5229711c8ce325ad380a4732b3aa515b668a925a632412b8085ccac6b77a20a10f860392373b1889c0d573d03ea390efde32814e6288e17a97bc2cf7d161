{"_id": "safe-eval", "_rev": "15-06afbb902709fde9448ebab4ddb99741", "name": "safe-eval", "description": "Safer version of eval()", "dist-tags": {"latest": "0.4.1"}, "versions": {"0.0.0": {"name": "safe-eval", "version": "0.0.0", "description": "Safer version of eval()", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/hacksparrow/safe-eval.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/hacksparrow/safe-eval/issues"}, "homepage": "https://github.com/hacksparrow/safe-eval", "gitHead": "0259d4011e749ef0411df6f7bba036cc0f5315ab", "_id": "safe-eval@0.0.0", "_shasum": "da21df7016001377cb78e6850ae55a840c7f227f", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "hacks<PERSON>row", "email": "<EMAIL>"}, "maintainers": [{"name": "hacks<PERSON>row", "email": "<EMAIL>"}], "dist": {"shasum": "da21df7016001377cb78e6850ae55a840c7f227f", "tarball": "https://registry.npmjs.org/safe-eval/-/safe-eval-0.0.0.tgz", "integrity": "sha512-lnPgUDH0db0YW0ZZyv/Vcpjbli66PpBAsknNNdzylMAK3efCmhA1ddnotjtta1NuQtU8g7ktCkTmbQPNET3KFw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC8qjn74kf1fZIM2WzOmlOXx4mYfvUoOJyceX7xNw0ftQIgaXcanxzkM+EXdNQdF5j/3JFYNe6sk3Z/9aYJh+US4+Y="}]}, "directories": {}}, "0.1.0": {"name": "safe-eval", "version": "0.1.0", "description": "Safer version of eval()", "main": "index.js", "scripts": {"pretest": "node_modules/standard/bin/cmd.js", "test": "node_modules/mocha/bin/mocha test"}, "repository": {"type": "git", "url": "https://github.com/hacksparrow/safe-eval.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/hacksparrow/safe-eval/issues"}, "homepage": "https://github.com/hacksparrow/safe-eval", "devDependencies": {"mocha": "^2.2.5", "standard": "^5.1.1"}, "gitHead": "818d419de15772cef7616e86ee9d6df7f2e5e25e", "_id": "safe-eval@0.1.0", "_shasum": "b478fbd58fbcd2cbc00fb1361ac8273bfae428de", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "hacks<PERSON>row", "email": "<EMAIL>"}, "maintainers": [{"name": "hacks<PERSON>row", "email": "<EMAIL>"}], "dist": {"shasum": "b478fbd58fbcd2cbc00fb1361ac8273bfae428de", "tarball": "https://registry.npmjs.org/safe-eval/-/safe-eval-0.1.0.tgz", "integrity": "sha512-q+QKGvdXqUtD7DKfoz8Npyj0Sje6oRpvNj18PdpoIkQ4Mr5uQn9QDP7TMmV/HUQBQSB6o9TABgwPJjNfCNUr/w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHFVwG+6+DTDFt9zOOePeyVAEx4V22cxpOIP8yg+A09JAiEArM8GzdlGsFebNIKJPHg8tkt64xuR0YAAkl0Q8rC8Ca8="}]}, "directories": {}}, "0.2.0": {"name": "safe-eval", "version": "0.2.0", "description": "Safer version of eval()", "main": "index.js", "scripts": {"pretest": "node_modules/standard/bin/cmd.js", "test": "node_modules/mocha/bin/mocha test"}, "repository": {"type": "git", "url": "https://github.com/hacksparrow/safe-eval.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/hacksparrow/safe-eval/issues"}, "homepage": "https://github.com/hacksparrow/safe-eval", "devDependencies": {"mocha": "^2.2.5", "standard": "^5.1.1"}, "gitHead": "2d6c28243f719af5c6a7a7ee3db76b496fea48e9", "_id": "safe-eval@0.2.0", "_shasum": "f9f383035244e72db3f0057f3ed380b569e0ab95", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "hacks<PERSON>row", "email": "<EMAIL>"}, "maintainers": [{"name": "hacks<PERSON>row", "email": "<EMAIL>"}], "dist": {"shasum": "f9f383035244e72db3f0057f3ed380b569e0ab95", "tarball": "https://registry.npmjs.org/safe-eval/-/safe-eval-0.2.0.tgz", "integrity": "sha512-X4WJr7HmpQEcYlp6TuGCarJngX7ttGQRwpBLeiaz4ZlcWWThFAQrj4ilmVN3uv+v0SqEwUkHaZHpEgYIEu5g5g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF3N3PUsZB+DMEXxZoGpBJdzLckRadasquKauziGig18AiBgTbpXNGZd1hnBV5gnX9wbMy2PJqDqBOvd9uIpo0ho7Q=="}]}, "directories": {}}, "0.3.0": {"name": "safe-eval", "version": "0.3.0", "description": "Safer version of eval()", "main": "index.js", "scripts": {"pretest": "node_modules/standard/bin/cmd.js", "test": "node_modules/mocha/bin/mocha test"}, "repository": {"type": "git", "url": "git+https://github.com/hacksparrow/safe-eval.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/hacksparrow/safe-eval/issues"}, "homepage": "https://github.com/hacksparrow/safe-eval", "devDependencies": {"mocha": "^2.2.5", "standard": "^5.1.1"}, "gitHead": "ebfcbad1adac1ec2fcc9fa9ee7100a1b412ec97d", "_id": "safe-eval@0.3.0", "_shasum": "06ce111eebd9c185abaff008ec0fcffc5c5be00c", "_from": ".", "_npmVersion": "3.8.3", "_nodeVersion": "5.10.1", "_npmUser": {"name": "hacks<PERSON>row", "email": "<EMAIL>"}, "dist": {"shasum": "06ce111eebd9c185abaff008ec0fcffc5c5be00c", "tarball": "https://registry.npmjs.org/safe-eval/-/safe-eval-0.3.0.tgz", "integrity": "sha512-uPIAjU2zpyv2QJCZ1zaWZKnPv/5jgkaitE7WHomV4Mxu6kgHY1ruIQ1oTikEta/Sux3E8pZAozzJRsAUu3iDZA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDucgxPo54MXSGCswd+srVZ09XFEt7BsDlCe0QCuC0KQwIhAJ7hQtjytek5Bpol3NgWztg5PLv7W4XIbBnUFNeLlwEp"}]}, "maintainers": [{"name": "hacks<PERSON>row", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/safe-eval-0.3.0.tgz_1464033307414_0.6982610889244825"}, "directories": {}}, "0.4.0": {"name": "safe-eval", "version": "0.4.0", "description": "Safer version of eval()", "main": "index.js", "scripts": {"pretest": "node_modules/standard/bin/cmd.js", "test": "node_modules/mocha/bin/mocha test"}, "repository": {"type": "git", "url": "git+https://github.com/hacksparrow/safe-eval.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/hacksparrow/safe-eval/issues"}, "homepage": "https://github.com/hacksparrow/safe-eval", "devDependencies": {"mocha": "^2.2.5", "standard": "^5.1.1"}, "gitHead": "0fdfb822bde5eccbc3dffc934147cee2d749f7c2", "_id": "safe-eval@0.4.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hacks<PERSON>row", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Y3oRAm7NQPPXWuN8Mos9CgxxgwtjoP7ArJt4m2zcS7VmRl3EnXeWhiyGjZrSXIEDCE2rUBzjJ4B73Bjbzc7D4Q==", "shasum": "17eefd3e8f89d83ef8c7faa139fca5f1d9e3b4b0", "tarball": "https://registry.npmjs.org/safe-eval/-/safe-eval-0.4.0.tgz", "fileCount": 5, "unpackedSize": 7352, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbWfRZCRA9TVsSAnZWagAANNcP/2GZIcYz4hKtI8cHtGeC\n6z+KTXIJiOwMrTGSj2i+Xk9KNUiP5BQoo9fFWS6N48pJK3lhv2W4h/rjTwpv\n1xsw5ZiEDj1zhb7O5GnkyJtEiWcqIB2sEAPepfByYUSYtPiJcNijrTGDfN3+\nV+2jwios7ZfyLRGGB24ABPmzo2nnq1GaoZiY2Tu1jgghUoNR8CPuKBOSpLaB\nkjuA/TKLWBWWL+cyqTOmKkkwpRrJCXgFzSMgBP1ecLAagtahqMZSoTmueM/J\nwQ+J/3+oqiInGeFnt4KmHKDUISXbzUEAOReIF4g7vOWM2QX2SWCyOBOaBEnp\nxD8pziQClH5B/4S9lqQjAHaAfudmkNIIz9yXx1APsqNsVmBIntte2N+egTP/\ndy2atnweCGxU2l3JbBphT7eeyNyML5nsJQuZQUQlY2m2tWywf7L7+S7vulKb\nhK1iStOxdA9WK7Aqbaw4o62BBvbWhvo3XW+D/MWBmvYqQo/JlrbfMO29Us2X\nH9IlBImiG2hEugBZ91ySv/eSsufp51uqq52UwwZqSMRgEgMlzL7fQBMEx2Sd\nVgz6Lq4vbuCBHI+Xa5rsy6ro0joNXaWZOjo8Va94U/XriM6nrVpUQKHYbpwK\nOTLOkU/K8D+CHMbxtd261fda4lepxklG8g0ZXsyYkzUnTBqxVTR4j/dBqd8b\nCHhi\r\n=dN7E\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHAhtCCSH9rOH89K0JFBt6HG0k8gN5N74L50cB+JzQhEAiAIOr9PUgsAKyG8khwmGj/J5f6zgiMKc/srAwnKE31waA=="}]}, "maintainers": [{"name": "hacks<PERSON>row", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/safe-eval_0.4.0_1532621913724_0.21009435560174983"}, "_hasShrinkwrap": false}, "0.4.1": {"name": "safe-eval", "version": "0.4.1", "description": "Safer version of eval()", "main": "index.js", "scripts": {"pretest": "node_modules/standard/bin/cmd.js", "test": "node_modules/mocha/bin/mocha test"}, "repository": {"type": "git", "url": "git+https://github.com/hacksparrow/safe-eval.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/hacksparrow/safe-eval/issues"}, "homepage": "https://github.com/hacksparrow/safe-eval", "devDependencies": {"mocha": "^5.2.0", "standard": "^11.0.1"}, "gitHead": "e6c5dba9a8b3e151b012d5ed50e3a781592911ac", "_id": "safe-eval@0.4.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hacks<PERSON>row", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-wmiu4RSYVZ690RP1+cv/LxfPK1dIlEN35aW7iv4SMYdqDrHbkll4+NJcHmKm7PbCuI1df1otOcPwgcc2iFR85g==", "shasum": "e54ba5a1bbdec795d488f6c8765e0c2a78b4cdc0", "tarball": "https://registry.npmjs.org/safe-eval/-/safe-eval-0.4.1.tgz", "fileCount": 5, "unpackedSize": 7389, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbWstxCRA9TVsSAnZWagAA6RoQAKOWY/8qbXBmqtId0UX3\n7TaOo94nfeVyTRqssoRDVkO8lQby0/z/U8RmrQuAfMxGpxlTnm7GwA0Bt65r\nVW9leCwDYrGY17NsmMuzE7phVMrgNpB5shfSoLkS7MpGq/SXbvhKiW6/Bx6x\n1nBtjOGoee9Pkmr6xbcK6HYw8yBI0mj79lLVjS3Ezfvt2Dhi32qruOQW2eo8\nnj2GaK1kJWOBGlTw7upa4HPhJfi+QiT4Ao7MXIS287yVPB54nyc7EFOpasPu\nLPjuMUq7oVQOqKrcyXF+qhP2gSDR9oD/deXs1IMzrvQP2vD65uJN8sn9Fpsh\nkrrXam86bB7RrvafLR4p8IRfOBzff2GtoBY+UkHQcfFVaWyobhDzZB3Z0XJV\nmj8wwLucaJrgnlAZv7W+j+ZjOZ3A5LLVktQC7VsMICL07MoVNHCiiSOSSzST\nSmaikmy6i+gDLV1J+KQKtvKgIXsQsRtFKk9YwJztBmCeUmOGcydT9zH6LvZO\nE/cVIvPGfw1wcx4dbBkdsEshG5oreeP0yfyYF3Ks46/9SFEfZdnD5MHZ7pdE\ndy8xgpFMKie0uX+ipzUXSkltNuaSFGl91HiF+HMlMs8vxr/ymmFIhNwR0/iW\ns0mysq/eUMMJ602PRICgOO/uU+BzAqa4j0inCMX11o7cp2RuIAzu383TkvIc\njGLn\r\n=xT64\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD5WouTZXIP68k8p9YBHv7Th5MU5aSXRW/KrnoN9ODG6wIgSFqtNCJcvU1xOgny1mJpCYYLrQNM/1bqThcS8OTlIVY="}]}, "maintainers": [{"name": "hacks<PERSON>row", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/safe-eval_0.4.1_1532676977082_0.8575079585491823"}, "_hasShrinkwrap": false}}, "readme": "# safe-eval [![npm version](https://badge.fury.io/js/safe-eval.svg)](https://badge.fury.io/js/safe-eval) [![Build Status](https://travis-ci.org/hacksparrow/safe-eval.svg?branch=master)](https://travis-ci.org/hacksparrow/safe-eval)\n\n**NOTE**\n\n`safe-eval` `0.3.0` and below are affected by a sandbox breakout vulnerability - [NSP 337](https://nodesecurity.io/advisories/337), [CVE-2017-16088](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2017-16088).\n\nVersion `0.4.0` fixes this vulnerability. It is highly recommended to upgrade to the latest version if you are using `safe-eval` for executing code not generated by yourself. Thanks @kauegimenes for the patch.\n\n## What is this?\n\n`safe-eval` lets you execute JavaScript code without having to use the much discouraged and feared upon `eval()`. `safe-eval` has access to all the standard APIs of the [V8 JavaScript Engine](https://code.google.com/p/v8/). By default, it does not have access to the Node.js API, but can be given access using a conext object. It is implemented using [node's vm module](https://nodejs.org/api/vm.html).\n\nCurrently, it works only with Node.js, and the JavaScript code must be an expression (something which evaluates to a value).\n\n## Installation\n\n```sh\nnpm install safe-eval --save\n```\n\n## Usage\n\n```js\nvar safeEval = require('safe-eval')\n```\n\n**safeEval(code, [context], [options])**\n\n`code` is the JavaScript code you want to execute.\n\n`context` is an object of methods and properties, these methods and properties are interpreted as global objects in `code`. Be careful about the objects you are passing to the context API, because they can completely defeat the purpose of `safe-eval`.\n\n`options` is the [options object](https://nodejs.org/api/vm.html) for the vm executing the code.\n\n### Examples\n\n```js\n// string concatenation\nvar code = '\"app\" + \"le\"'\nvar evaluated = safeEval(code) // \"apple\"\n```\n\n```js\n// math\nvar code = 'Math.floor(22/7)'\nvar evaluated = safeEval(code) // 3.142857142857143\n```\n\n```js\n// JSON\nvar code = '{name: \"Borat\", hobbies: [\"disco dance\", \"sunbathing\"]}'\nvar evaluated = safeEval(code) // {name: \"Borat\", hobbies: [\"disco dance\", \"sunbathing\"]}\n```\n\n```js\n// function expression\nvar code = '(function square(b) { return b * b; })(5)'\nvar evaluated = safeEval(code) // 25\n```\n\n```js\n// no access to Node.js objects\nvar code = 'process'\nsafeEval(code) // THROWS!\n```\n\n```js\n// your own context API - access to Node's process object and a custom function\nvar code = '{pid: process.pid, apple: a()}'\nvar context = {\n  process: process,\n  a: function () { return 'APPLE' }\n}\nvar evaluated = safeEval(code, context) // { pid: 16987, apple: 'APPLE' }\n```\n\n```js\n// pass an options object to the vm\nvar code = 'process'\nsafeEval(code, {}, { filename: 'myfile.js'}) // myfile.js can be seen in the stacktrace\n```\n\n## License (MIT)\n\nCopyright (c) 2016 Hage Yaapa\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n\n", "maintainers": [{"name": "hacks<PERSON>row", "email": "<EMAIL>"}], "time": {"modified": "2022-06-26T15:19:13.543Z", "created": "2015-08-30T19:23:49.558Z", "0.0.0": "2015-08-30T19:23:49.558Z", "0.1.0": "2015-08-30T20:31:18.496Z", "0.2.0": "2015-09-01T07:52:19.737Z", "0.3.0": "2016-05-23T19:55:07.903Z", "0.4.0": "2018-07-26T16:18:33.826Z", "0.4.1": "2018-07-27T07:36:17.187Z"}, "homepage": "https://github.com/hacksparrow/safe-eval", "repository": {"type": "git", "url": "git+https://github.com/hacksparrow/safe-eval.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/hacksparrow/safe-eval/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"afelicioni": true, "abhisekp": true, "th3brink": true, "ry0id": true}}