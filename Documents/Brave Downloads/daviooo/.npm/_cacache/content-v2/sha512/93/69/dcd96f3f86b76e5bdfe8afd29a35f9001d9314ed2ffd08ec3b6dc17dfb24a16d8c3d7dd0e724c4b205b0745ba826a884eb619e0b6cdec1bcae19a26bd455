{"name": "@npmcli/arborist", "dist-tags": {"backport": "6.5.1", "prerelease": "9.0.0-pre.1", "latest": "9.1.1"}, "versions": {"0.0.0-pre.0": {"name": "@npmcli/arborist", "version": "0.0.0-pre.0", "dependencies": {"pacote": "^10.3.0", "semver": "^6.1.2", "treeverse": "^1.0.1", "npm-package-arg": "^6.1.0", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.0.0", "read-package-json-fast": "^1.1.0", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.5", "mkdirp": "^0.5.1", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1"}, "dist": {"shasum": "c88004d95062e4a8b527eba20f2c8b54f6f1a3ff", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.0-pre.0.tgz", "fileCount": 22, "integrity": "sha512-MJkkUm9gQQAPXUjt31E62m412xSLkIHSUhqTGYv8cHskFmhlJ3z2c5nsgkuB306YppD8Igbgy2BY1FIHyxRUGQ==", "signatures": [{"sig": "MEUCIQDC/bYfRbWBMkxd4pTsz3EleNFvRW8W7RoA8c0CopQIQAIgSOeZEbaR/cuPRsCU+/m9U1y4naJevBP7XDhkim/kYj0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 148523, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeDAL6CRA9TVsSAnZWagAAhBYP/iSp0GqasL6ZkG/DrTEK\nf77VSdzM7hxmqFew2V6x1dtxpm3LoI1wO7/rn4wG16Nqe3Iv5IVy03NnEBvh\n1dnfpBGW7dudo6wXLVI2vl9DHGnQgjdq8S37rwVdfrOVxk6hJ+bQTSexTHuD\nMwQaHtjBO1sL3BDI+gg/o8bXXy2kdS3VmdYtpdw3ndhmVQM95bq2wIHadV18\ndZb2nwD9ZDM/DCzKvJrMAVLFwZDRtuBhOvnSjRbgU3Uf5xzOFVZTaXU4TdGs\nDWwiFOoMpUaxP7G0jt7eThobCpilX4TVwMtrGgPzWzAjbrAlw6oE8GhTPxPV\nvVBKvDB0hARzDy8TCJwyrMQVFUj+kfdQjmg7/FKGZmT64Yrrol304raGXDTg\nXhReQ9fbDMHXXoW+Y/iuhgLjOcRbtqxylXZQ3fCu2kkA7ruIOUWfDLc4Ptov\nuePNARBvxkOooexol/Zt18Zb3uUBjcqTZ1ApCaVIy3JvTViRMbR7PBP5VgZE\nmDaV+LlzPcDdTX+LvPsPEFJhKhtGTxp3E27chpCIgAUaWTQVLgY3tp3LmuWT\nutIxNlTEgk67ngATe7y7JSYz9SDAgYKHpkml56CWZ1lH3Qq4ZzbsqXV2BF5s\nUbM9bZGhhFiNXSnVC6owgw26nnNmDUgj/PyZ+PoyixRZg0Tv95rvsvyYkAda\nqcxI\r\n=BGyY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.0-pre.1": {"name": "@npmcli/arborist", "version": "0.0.0-pre.1", "dependencies": {"pacote": "^10.3.0", "semver": "^6.1.2", "treeverse": "^1.0.1", "npm-package-arg": "^6.1.0", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.0.0", "read-package-json-fast": "^1.1.0", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.5", "mkdirp": "^0.5.1", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1"}, "dist": {"shasum": "f30e35243ccfea4be066bd76fc721e78b2cf9f55", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.0-pre.1.tgz", "fileCount": 22, "integrity": "sha512-VZDAewInDhuhAabTlg6zFBDMsPgfKt1n3EeJ6MRKz6iNWTboYp2pJWZIb0T6U25ffPcVvR8nqVdH+56bsRqidA==", "signatures": [{"sig": "MEYCIQD9i1t7dE8k91ojYaGGvUnAT3t8zitFxRR9oiMqTuCECAIhAMpkOu8AF+H7M322Wbz0pD7xq+PkrMptYeQ1mxys4g7e", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 148591, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeDAPGCRA9TVsSAnZWagAAC/QP/2fTwFrdZVyWwsSUTZtn\n5jrjrRCd8aSIxuvwlUD4wstwLEbFf8zIVsIpsTTbbccYRGRJwLP1Y9PLLG4A\n/NoCcXjpFzRjPPDUW9LrgCCtDNHQjTBPU9a1yQn2SQ5HA6zPD9+/IHfosZYo\njZBNNVHAMyfTr7AydJ+mj0E8vR/E2g4y5vhvvHIR00ASgRWRs+sU7L0++Sfo\nbf4JJE9VaKdxZOI7KE2PmsDgYrIcDbaELaYkUPJ9DPKUY4Ie78DINENMX/lP\nJDMGkN06GlGHyW8yM7v4gWNSVxMR8qDUH7uczkMedg0G/rl6cuThLdvhNC93\n9wsRBnX+b9DczLB+RA8lT0ILpGuJRah+SrSDYFewOLK96zh3r18IFBLQ7p86\nXT7rml9+m79eQgSLcu159t8ORAZCG7FyUVilCaKwgVSwwDDuG6s39prLyhL/\nm9pdS8cmhIZt+ftizoExDP/szgOMfX+vOUF62LvlSZLP4NogHbM7WL+1M+GI\ngJGqGoiwygdTZtMskqZPFo0LIH/v53yOBAtWlIwoFf5elGyNKdHeB/e88PXa\n9VE/k4lRQoPMBUbRkXWl5af9qt2g5tQtMBHh96twK3mppABP2/5hRi8qWPd9\nrjkdryYfSu2pT2kqKrdontinOM42mzEdZVURnQdm9XfjTXmutXx1iWRSnNmV\nvxpT\r\n=MeQs\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.0-pre.2": {"name": "@npmcli/arborist", "version": "0.0.0-pre.2", "dependencies": {"pacote": "^10.3.0", "semver": "^6.1.2", "treeverse": "^1.0.1", "npm-package-arg": "^6.1.0", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.0.0", "read-package-json-fast": "^1.1.0", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.5", "mkdirp": "^0.5.1", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4"}, "dist": {"shasum": "bff8c4d1c52548463f557145f2855449acdb522a", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.0-pre.2.tgz", "fileCount": 26, "integrity": "sha512-GBDy48h1QJeFN7tUWiXdds3JaJ9HWdiPG8L/eMIzcjTTsofP10gy7L+J3YTRzCTFk9105sYqCok4ZC+nvwZzXw==", "signatures": [{"sig": "MEUCIFq8GU6nRZofYZayCg67mBtBctW1vUj9Y90Bphr6u6RfAiEAlFjgVqxpFsjAr+v5nhQ1QtV6UjK8dxSmrSFoO3o9JY4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 156806, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeDaNNCRA9TVsSAnZWagAAuJYP+wdWGzR6LilJHUng2gki\nhRlWSGr+cpx5vfPw/MlVQF1Liz88fQmmtcCatYO6NjWXdE3W01Azoa5vXAye\nCGduS3D4hru2vIhrMLb1LnTJbuR2ve/oKK7akLp6/TsNi6fthfz2eeKW8QHP\nX19nPUEZxhkGRxvNYuBP2dGHHsnRhrjsXiqruLMdoJNfbRY+p+MnFuqqBe9I\nR0A5WDhSskqRciXLqZ9mgh2YBUENnGLNk9RE3Xv4FzShFBUY+0qAubaCt2aa\nKO+QOWBCHTX4YSZVDSFFHvkluJMHn4P+34gspeyEWDqFEdlcXvUuglW1dYm4\nJ62U6XpvP2fog01bkROnYnCLIvctJ7S9kdG/1Hdj1lFC+cVOY265KbfnNFAY\niwPp5CZUOAc7L4MnDsYybqOAWS14rkVJnF7LFry57r+cQoUvEDR2Q0guPvm7\ncDfiOUZy9Isldd/l6B2i4iy4ioJWRPLd6b6WWThXJPP7pM2OKezJHwSfupzI\nbR7ch93F80WGNZAaTVzxIqpYERFRDHEawJoHW2DYKaqPd4sfR/ent/yixvdh\nES+FkPm4j2X2sUE2nsAro//phRlCNI1ebYdWAmZ5oMrsJP6whH/C0avc75jY\nLmmRMrWVmJi4m3qXO8O0jqD+6hyFv5aPPNWGHdqeN+YPoBKBec89xNUJA3Iy\nCCkA\r\n=5/RX\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.0-pre.3": {"name": "@npmcli/arborist", "version": "0.0.0-pre.3", "dependencies": {"pacote": "^10.3.1", "semver": "^6.1.2", "bin-links": "github:npm/bin-links#v2", "treeverse": "^1.0.1", "npm-package-arg": "^8.0.0", "@npmcli/run-script": "^1.2.1", "mkdirp-infer-owner": "^1.0.2", "npm-install-checks": "github:npm/npm-install-checks#v4", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.0.0", "read-package-json-fast": "^1.1.0", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.5", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "83c9fb2f40a846ca3531d1875bb385513db75e28", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.0-pre.3.tgz", "fileCount": 33, "integrity": "sha512-zqZXpt2PnJFTn/rdRQwC9elAgfOcPo1TL8P75TTOyLTDlhZvYkRY0O/Pwr4FGrBP+KK6wDr/DXKJiyCaykWxvQ==", "signatures": [{"sig": "MEUCIQDFkMHL3ROpYwSgZLJ5/zecFYM0f0hE8OPpwn+Rc3uTkQIgFuwjAowvgBRqc/3APGIJIB+p9u/DAVX8gNlKzDXAuJc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 271317, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeOLKmCRA9TVsSAnZWagAAWGcQAIktMHafSLyTJ3JQJIEy\nqevBP/oqLvsTT+5VGKPQtIu3kjH+kd8Vp9kdGBpChJ+CRYzYnb/6WqlROmQD\nl3cNsw2bvQSgiv5A5PvdgG28Y/ddM27BX36xxc/vPsjVlxwR3wrL811gY8mY\nPMlcCyFbEhLRIjG6abwU9mD6RtkUdrSI6BzLntaROWUkbUVcZN/yrPhdVE75\n+OyeUyyO4rslmpzoA9ORIYyYqhZzBX86j2fuYC7uVywsv59swRhy3LNuWNiV\n7RPEnYghYaW/tBMrvELIQEQNNT+wDs27Aa/mx0+9AMKsx73vVHiH2gwx9g4s\nTNcbIhpk9iax7yNeMxtAkoVU/tEI2kBjFGWKvFhZvPXR2fgrIgbDtBkotla2\n7y0H5ulqAfjqLJ/hDSICuvgMGYzW74cc0JP9f5SX+M7YA6v9oAPr179TYMNq\noaXWP3PJdRDHxTmOEfwFWDxgAxzAItyzutAKkJmpMVmOn5A3F2WUyitfSxte\n+MwFw9IuBReYna3XhGof9YlmqyV20j0ubDiulGKgiSuTOK230N8O76w2HDdZ\njEkkBJ8sU9mHjoF5TK56Jwd8p755pQQV6woceRmv662n/K1b6okPbAMsFBhd\naV2UTpK9ntzg1SQzzkfIhp93+glor1SsI6lFx1sIgL/2ndzpdyJOaarhZXjz\nTC3D\r\n=amU/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.0-pre.4": {"name": "@npmcli/arborist", "version": "0.0.0-pre.4", "dependencies": {"pacote": "^10.3.1", "semver": "^7.1.2", "bin-links": "github:npm/bin-links#v2", "treeverse": "^1.0.1", "npm-package-arg": "^8.0.0", "@npmcli/run-script": "^1.2.1", "mkdirp-infer-owner": "^1.0.2", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.0.0", "read-package-json-fast": "^1.1.0", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.6", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "4b4bae2eaa8e145c1a18c7ef3e750acf3938d93c", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.0-pre.4.tgz", "fileCount": 32, "integrity": "sha512-Q+C5ygJwpWr0mn8fFetAhJGx0SiC5L7eP1Dcp1LTspEphE5XXePODOHOdm/nOaDdyO2K3GeocutfGGZnCh6oRw==", "signatures": [{"sig": "MEUCIQCMRTaMk9obXcLsXuRcGHlz7IQ9Iyhw89on62Co42hpuwIgB9DFTSh/5xfYiXWeOiMsvvVrubBbZq8S6logJggqhfE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 195640, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJePN/JCRA9TVsSAnZWagAAvhwP/A1t7YDjqZjKw1dibnpL\na8kQTv6VKJ/TpxJFNnyNnJLw3eTqbjPjVeEPL9VPz8UUXI74FLZr0mnyGRh0\naoUGJRiNKuHwuhHPVF0xONhhYxriiMK67QGIoSilFiNNBetwgbUR5tPW5gSk\nVt+yy1oHuyF773RoqoJFoolWCIEQ/qPN9zKaq70GiKpqOBOQPYKkYJhZhQ1s\nVWeCQxqIko/mymoGC786LHXs/kmm4CmT/c2SCMEVdYuBOQDmAnse8Xa14dlY\nIKdkNcPk5Tor+3c/TonI5i1e3mLqgn10/uy0lqcBlOsGrL1STjpoXDfLGoQN\n7ebgVi6qzNJm98t0wpNTMzNTH2tpXUvr35x6wr5EdsMqw2wcvg4clA5t7eJ7\n1LSN6G1/ttFwEy0tSl4hm4+Ks+gNdtDobBSi4SVpTfthwO16iqSQZZdTu4bB\nLI4vDNGxpezPJv7H6MopBqw1dx6W8Ld4qV3PDLhYHHX/ZJY2aKe3mL0gw22r\nY4ZfJw3qSf0LGIfuVmv2se547XkofOQUFc2BOaQ/r+m32MW0DoRAFpOXmdqN\nMmwA8nT/L926A8kzPdBMZYiBv9pm7Y0/Zi9Upf17jtZdisLDR/i5Fc+Ej+sG\n6dc/+GPrGNpPlnoOiEm0otERe5Sb89aM1LRm/24GXldFWX6TOq/b/QuB8TuJ\nOgd/\r\n=FBtu\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.0-pre.5": {"name": "@npmcli/arborist", "version": "0.0.0-pre.5", "dependencies": {"pacote": "^10.3.1", "semver": "^7.1.2", "bin-links": "github:npm/bin-links#v2", "treeverse": "^1.0.1", "npm-package-arg": "^8.0.0", "@npmcli/run-script": "^1.2.1", "mkdirp-infer-owner": "^1.0.2", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.0.0", "read-package-json-fast": "^1.1.0", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.6", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "41c2ffb38e298566649d30c9f21055f2e0b4d0fd", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.0-pre.5.tgz", "fileCount": 32, "integrity": "sha512-wEw6qlX3YLtJH/H6W4E77S2u+0xVVs8mlGYWAAqbvp0VnHMGTszMV0Ygb9Q3VOJkZ36eu2awVGWRQTwQhueAJw==", "signatures": [{"sig": "MEYCIQD94/+Et4adFF80zNU/ZeKSTZqWmehukvrdcZl/A5WVfAIhAIRc5MZMh3TKBmpBorwhrKaPLIZQbaW/46/D1AvhR3z7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 229419, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJePhIaCRA9TVsSAnZWagAA9wYP/RLUjTcEO7k58w2NuSgG\nMc1wq/Ka8nzdQg3Q9blrNBoHwJReaQ1PyieTpqY9t/MzRSHrz2/vcR7izjt2\nviiQVGRWuXEswojxeKJUPQIjs4d6CTfRBCej02WzZL3hRzagQ7//m0/w2vHP\nOsLE5zmkz6hqdyQQYwrecBvnsDg0H/5VZm2ZBP2QnKR33DszioKl1WIAyFzg\n9gbmehIbaOKFyr0vc26GXTcGAsd6ifiTK1O4k9Qm/ZwpnJlkX9Lx+3SUHiKm\n4NpKWoq8kGoCu7+P+PcQ3GVgS2F9c0PHMKTJOtUAf9DGR63gIlOAWfRqWQLY\nMoIy3q7UyvdV1FG6wJOsBtvEzDK+7AqDfTMr7sZXedi/67+eAHSOzzOHrbQD\nWd6fsyEECBBIokxodX7sv+gn9yt4kBAyODD2Xo5wETQIoAsa+yFPJL2SwjCi\nr1JVNBS0CJgf34PQ3wkPicQ4PlBMm9QirFoTYtbTftyOiMmlSd2gZYPtHYuT\nnQWotiF4EiCF51lzhTlzJoe3gu5AMIdd7v671mWCHsrcFmf1ma7ZVtVP9eUq\nb5LXMhOlYl1Qehicb6XKXGGQ52H/dvk4A5E9tr8BmrnK3BeqxKXxZxjY/lof\nVcnloFsclGSu8DBaV8MptPSqgaUurumoP2zf4zqQ/6FfQbLJZ5IG3fc20YuZ\nH5dh\r\n=e28V\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.0-pre.6": {"name": "@npmcli/arborist", "version": "0.0.0-pre.6", "dependencies": {"pacote": "^10.3.1", "semver": "^7.1.2", "bin-links": "github:npm/bin-links#v2", "treeverse": "^1.0.1", "npm-package-arg": "^8.0.0", "@npmcli/run-script": "^1.2.1", "mkdirp-infer-owner": "^1.0.2", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.0.0", "read-package-json-fast": "^1.1.0", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.6", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "3fd6862b4f6ec3f3b9a8c3d4b517e51d29e6959c", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.0-pre.6.tgz", "fileCount": 31, "integrity": "sha512-lS3QFA/7aRMqNsVcfHyg8lJRWem1pb1gxAHaNH1efgJl8rJM7r93HCtawsRPoMtCsFsVRihlKfDw6wG6iGkmMg==", "signatures": [{"sig": "MEUCIGyhAn4QZh8jjTOkIGSsWUDXb68KwIuWgt0Q9h9Oc6cPAiEAyvGxVkRGxu4364GwrfN6BhnA7FkuibtRXfsTop3LovU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 188639, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeQevyCRA9TVsSAnZWagAAz2sP/jYsPoZNSjUBQs+86+44\nnDzsuciZS7nIXFm7gmMn9bYIsTWWjbp0UKNhKGp+ODbp4fCZk3lG442FHVlv\ng0a+6stZUiMAqpzz1k11CpxOLvYdCghtJmM7uyjappi1dajMX/iPw040MyPh\nn22s4g2YyZS4VdRqfUgHSGGotAQ+orcWd4mjDrSDeNzHDOnhGA9twyqD7leI\nn9tTJCghpUY36k8Nly2R+q8yZLVfQc8YEF4NGwYxMRpkhv+y7vz7ZMhOJ4ep\n4koz57yKtmldZcfZSbfka28RQHzVxFciojZ/r2p/s/T8CpXjQ7t3TZAUXZ+I\nPv8lLJjnxfKkShTk2yi1Xx6w3ARqt5tiSPk9oxNQHQ5Voz6XNiOQ5x9VLKKS\nMhcbdOnaV0NKtpP26EVtBDAbcdTnnTEMnDeFj3O4fsX/lERVTWAucqjTxmHf\nT4uz7m1phT8LcH6dMhIqvcz5xWLC5wilafO5mnilO22J9xuXfVfolFfmH6C/\nMg1v2rYgSXurwsbZ8xjM6btyTT6hqzJt/5TV5OQjZgexMWk2WtiX8YlY6LJq\nWPLPA0GhU0//yp3GXlilSeg3WGhyegL97vhhAWf0y3VrR8KyAIv9bo1vFrvw\nAGRoyCQuKXYQ4Jb4J1zEFzH46HZFrCidcbZuJV/EBSyeluNoAiYWBvVhQoNl\n2R4x\r\n=YE8G\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.0-pre.7": {"name": "@npmcli/arborist", "version": "0.0.0-pre.7", "dependencies": {"pacote": "^11.0.0", "semver": "^7.1.2", "bin-links": "github:npm/bin-links#v2", "treeverse": "^1.0.1", "npm-package-arg": "^8.0.0", "@npmcli/run-script": "^1.2.1", "mkdirp-infer-owner": "^1.0.2", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.0.0", "read-package-json-fast": "^1.1.0", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.6", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "77e31962e33a4a7a9f604e772f2e2b2dd7ec8c84", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.0-pre.7.tgz", "fileCount": 31, "integrity": "sha512-xUtElnU5cn7TapgQ4viCCXPz1L7VXlQKhbwDKvvCHVyftRLIGd5osvNkdH08SPC8cIXNrVf7LKINZs2kpMkPBA==", "signatures": [{"sig": "MEUCICSU0sEoaw1DtwZotRwhQqiMzYa7GxQU6sjtKMQx8mBIAiEAxB9lRx8yb90u8o6Lpfs+dmOU+n8Y3i8GbM7DvgG+VGo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 188639, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeS0pkCRA9TVsSAnZWagAAe6UP/2TeldvqBYLsk7MQiTeZ\nVqkYiIK/otmnPDmD66aeWuCk7OwtSRuCAmlQfJZokIXGJJP5ZSsjJvvw2tPu\n/LhMFhM3M4TQgGPjGuE4FJLkwQnTh8G1MgTW0oJ7PS385XgaYU60Q+A0l35I\n9ZRlI7ub+ylkQ1FnIPsmwYwbZUYQHS0PGVFpNg9QlIDix8K3ws3M583Lt0AJ\n6cgh/lq/h0hruhdZc7wRL/NRT9q7VDIxdK2QgXlgQMcbdPZj8X1ft9K+tSYw\nvXSVMPmM8rhZU32ayPy/cNw2VRa7MCiH1SkqbUtQlo8IAihog1ZERxuk6zGk\nxi4NSI8f5wcU3bB5MJyD3AbIsJPHFPfnzX4u0QSi9B+j7Or4LOWrZ44pQvFO\n4qboucF/4Ew24dUbaBP6PxavLpsirKEWFCaTwQTT2FAky8XHvF/Guf+qwVvH\n113evEBGa22bxK7/0gycRYZT4H3vYok6anlbcDsKTBiHANYs3b520h7QQCqY\nMkOZPVdguv5ger+RzTHW75QUwej6ziWTfKCr3mB7HTgW2FWaDv1GMoNKbasG\nQUJwiv9c/RoXl+2uhyMN9ertHasm02UIRG88avlrzVC5JtVKvuRiHjFAYWkl\nysEVemqOY8t9cD46g5qC27xPILlSrLh/1utlC2dvqMSHwQUrMe9M7IP2hjo8\nHqfq\r\n=AFLI\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.0-pre.8": {"name": "@npmcli/arborist", "version": "0.0.0-pre.8", "dependencies": {"pacote": "^11.0.0", "semver": "^7.1.2", "bin-links": "github:npm/bin-links#v2", "treeverse": "^1.0.1", "npm-package-arg": "^8.0.0", "@npmcli/run-script": "^1.2.1", "mkdirp-infer-owner": "^1.0.2", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.0.0", "read-package-json-fast": "^1.1.0", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.6", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "182b67fe973a3918ea8a13bcf4fe3d485b9f1be4", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.0-pre.8.tgz", "fileCount": 31, "integrity": "sha512-l1qCEvdxX3NR69ifgVPX3bG7GbvhsrEv63G7nKvRNMYedQzBtQx8H3ECukVblk00KbqVoJh/3hfzMPUJVRP17g==", "signatures": [{"sig": "MEQCIH3C9stxT7pMW5Zybbnck9hspATycuv9xPbt+tZQvvm9AiASBlE9dEcppp0t9zYfytSqCKyFGBDlRLYoSnwxvionZQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 189694, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeTH08CRA9TVsSAnZWagAAygsQAKEA4l/BjHjmXFV3mIkt\nhR9hKJv9qz28O7W5p7uiLbQRoQPXOlpuoNkCZ4DNTcNb131InG5tCy4ZXa5E\nRWyM5QtaUTaEx3WEh8TMzepDlcFPai9Sr5QoCEE4LEwFqY30vVbQq9W6m9xE\noyIAN07KasYAf+Suu01gH6X8sG2Yx+7FgTZ+10PLBIeHQY/60dExqMY7GcOj\nPYgLzUapVsyjWP/**************************/1L+pQf6LDgBNHs9XAt\nIZA+1nUWpCDy8HBF7zj3nNVjEuWW0XrjGbOwiLr1sDR6bjO8jZrpt7JpUzFQ\nYlHcD0bI1MB/ofj+3Oy0fBL93iWstUfJQPmX/4JHNMX8cswG/AdFHmqwJGK6\nVGUAU+ZJpmzbpNG53jaIGkOEch6lGAiNfzMFTt9t4KXZ5/CKTCNF6ZqAeADM\no+39ksdzAVqe1XULOCiTyMJkqHpY5nUs8d12wwKJ6kFupfKc2Gxiw4eudpm3\njRlAioB9/Ic3I67RP5SBysGXeMxoDCQZ7rdsr8Qw/DHUcOgfSdde/d476xv1\nrNX0BEcnXdYpaxKJWD6Ov4cY4libMvXlv705dqmVqel07+nhNW3I0+LgWqkY\n3UTBMeAGZn6Tc5hoI7pif8W0FnpJ+bx/VxJ1E9IWSRuA24Cq7EkHQBnHYA1P\ncTlo\r\n=XR46\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.0-pre.9": {"name": "@npmcli/arborist", "version": "0.0.0-pre.9", "dependencies": {"pacote": "^11.0.0", "semver": "^7.1.2", "bin-links": "^2.0.0", "treeverse": "^1.0.1", "npm-package-arg": "^8.0.0", "@npmcli/run-script": "^1.2.1", "mkdirp-infer-owner": "^1.0.2", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.0.0", "read-package-json-fast": "^1.1.0", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.6", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "5c9bf32bbe12bf4d2b93bcb6eb7bf3b60318d032", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.0-pre.9.tgz", "fileCount": 31, "integrity": "sha512-P9iWMYo5ePQoOiiSf5xu+IvahV/FQ7Vtw/bmGGQG3BWk9d+tEWmReBsRGp/ENxGIA+PV+SY12qHVO1YsBGNPNg==", "signatures": [{"sig": "MEQCIHLwFPGkSd8ayJwgRRyxD5LV6oFy5XgQqlhPWXw7/31eAiAQh/ip2r1nDOSxrgjvODzeNV4KGyGjxyG6m73F7YvZXw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 189677, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeTdYuCRA9TVsSAnZWagAA8McP/14+Fv0nf4b5m8FWWkkg\n3bUQMcSg49e5hvEYnDVEDD6DBdSNVGflbrxEyV8cwS2NuvLVGt/JelmlaDlX\ne85xoQNFfU8hqeg3WP0kxGYo6QDDd/XMNXYFaHPX6fGlBaC0Rqgt0mLu0Aq4\nlD/c93rP4/Q2ggBZuE+r6P47oT5ce70kOLVvTZM2hqkI+6PDECK13yAa5JMP\nvyWVyq2Uvptgg/APnkOAxGwKktVFMC1VNH7OrCykWgTaHkdKDROCPlI6KzG7\nNvYeiHTp7ThOcJS00yNtcs9aAg/d6F7PiWdO/bfCq3Ln2RmR/0lMi9jNU58T\nx1c12wapkSbUsqF7jbxlyTo324fZMBLOJDr9+zT17ufEw/+xmyxp4Dw8Me88\n0IRncCPsllykOZPGwLTPzle5qUYpbCF6rKIEDP63Z/ImJJ7V+2hFr0MfzZpS\nv5wAw+cQjivVH5LvYs8dQcVdKDx5zV7+J8seyCbDuS0GUSVGD8Uc+xQz7qtE\ncM7W/x0oxMALitWSJ84SbToFIpI4oCQvg8q1UEji3FH4Jftt5guBXq56UWDs\nutt+JDlTnuhRqlEqTjWuSrjTNru2aVdjJrVG6AFY1f4zcmqhCqtzqOZ2zS3H\nPxADJ5syUsZfEhjkT90sGZSpKlKHl9RfyIPOEx8fF+Ump960Ug/GmE4Lk5Y0\n8L0e\r\n=vcFT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.0-pre.10": {"name": "@npmcli/arborist", "version": "0.0.0-pre.10", "dependencies": {"pacote": "^11.0.0", "semver": "^7.1.2", "bin-links": "^2.1.2", "treeverse": "^1.0.1", "npm-package-arg": "^8.0.0", "@npmcli/run-script": "^1.2.1", "mkdirp-infer-owner": "^1.0.2", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.0.0", "read-package-json-fast": "^1.1.0", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.6", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "d0269a815cf54227ff23056516aa06c52eac8edb", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.0-pre.10.tgz", "fileCount": 31, "integrity": "sha512-pshtlDoQ7A55BR47fD+e7Xl7KRJKyFLPGKHYZjtAaxquVId/Y4qgOb4uMV+WjCD0WTlwU70IOFrhlzG36uR16Q==", "signatures": [{"sig": "MEQCIFGL7fvCfNZU/xLWd8od71TzpEgtizVL3C8FuoY3MKlLAiBGrO9tIpd+BGd3Yr+SYY/nb+ZQha3kz+qhQR/4YhXC3g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 193138, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeUbdWCRA9TVsSAnZWagAAqQMP+QA0VdKBbymNmt1nkkf7\nYqy2z2wM3mDS0L+NsnAt0q9YVtKP89XksYbnIgSgMKiZVTfsoI5RCpa5aIs0\n+8wMO3ARO4XJUBwH3WrjdEwBcmidUhcPWBslxWsRrne6H/Fc5p10CMwJJcNk\nkFNjDwxY5V1o3xxJuR1hWcrC3RvgLpdAMu4LsWzykc7mPslvYRi/eV34N+iq\nKR7+4L1vuRnVXCp0zVHFOUiISkWesG8EyAsl0P1lOskQ4jC1WpQOXji0V91K\n2jPShSkAWLGq9cm839k+O2nJZ39p0/rd3JI2kQZ+yzsgCCG9c1ZvLOBTUuh1\nhNBlxlcNFMysnChzdGaQn4e8L4fwKRv8AlchFqTzz/tNHGFrAhJntx6FLUsa\nyn1wvEkrUx+nczUWeuQxgiCGF6Nmoq5DexyItkbImX8q43NHKTp3cFdq/m5I\nHiFcAVGwO4+Kj1kGLgxaMmtUZnSe2BXnOdIp8kERHwnIT7efxhuAgiuMPTgG\nJ1iy4VJVdFnl3hIgIkqNLDZ4oQEDhS8z3pJttl7TMCHIMFKoo2G0ybnP9dmA\ncd8A0mkYeLJm3QDQKrLgcRQalbFeFkKRWCSOq8YyugoIpnssBB+Ih9uFwbiw\nR0+g8z2yTOTpWF5mY7FJguqNtQYUcnYtazPAYVQT7B5hwT258JmAFm2Oa/TE\nn5l8\r\n=k2nZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.0-pre.11": {"name": "@npmcli/arborist", "version": "0.0.0-pre.11", "dependencies": {"pacote": "^11.1.0", "semver": "^7.1.2", "bin-links": "^2.1.2", "treeverse": "^1.0.1", "npm-package-arg": "^8.0.0", "@npmcli/run-script": "^1.2.1", "mkdirp-infer-owner": "^1.0.2", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.0.0", "read-package-json-fast": "^1.1.0", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.6", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "116d6f8862d72774c63e303be883cdb459ad072a", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.0-pre.11.tgz", "fileCount": 31, "integrity": "sha512-d75SHZi+fybiPH3juQ9VhZEexhVsom3vGl0pN0cbLHAHd/N5tSqgIKJ/YXjN96G24pMLI9wfpkFAIrnPkfv/6w==", "signatures": [{"sig": "MEUCIQC5yYb1HZ9lQ8Iy9/+3gXaV9xeK1BqQlX6zObz0QCjHqwIgPUx+vSqp+oiHzpOYnZBwPkZ5P6MAIhh856sCBH/ndOY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 193839, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeVf94CRA9TVsSAnZWagAAuTgP/igMaI2HoIVn+ZIb2l9P\n6+52bS4I30RvNjoUP6r1poZ2i++vJtUgVKq3Y/ub/Lf8C7GsMpCl7YFBwOdl\n0Y4//TFxeLFsa6awk861unZb+GUUFnBVhyepN4n4x6yFYDMcOo+br4S4SGi2\nad7Lesvu6bJtzGUQzRrXsq0b1gO3cAeZcTHzKXeUwXdAIjnKZzmYib4nj7i2\n4akEjOZKfTYBWk2xObV3rDGAySqpA8fMQpmGupqfzgFk9OAA1YlM5+OQVloQ\nCzqDQBNkIfavQ1WxEUWxFq+O3xMhgXDSP/BbtuXCgSkazDtKOnJhE0LjkHbm\nzx7l29DhHvJCzRcB0xDs1KK0iHq1lKXHq8XuduhG0xsnnijlc14Qm+4A3OTZ\nv2afZK8mHoj0YsQUgdNTZJxUvpAvCteWAwBRvv4CdS89k7+HKswf8KpGqMNj\nvCYy5FBbAUJa5RQeX+bAr2sY3WOVrHZ1TGV0zkGZ/dfXMQO5kRnAjDnuId5i\n8qSAF1W8St3mXk8gtMau0LsUgk1OXpKXgTYkXDWYpZNQ8BAXorKxsVlSmXva\nsuIQLOt9Vr+bWVqozxD8zAOVY9PLad70UwbDehUW0n7E9ZWl6FVEjYW5pYx4\n2x3+uGaRnnb2qE5n9CN2I9CJzxkqOGlpGB33iZzix3M2iPaLeOPjCJfiZrLe\ndTqu\r\n=biLw\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.0-pre.12": {"name": "@npmcli/arborist", "version": "0.0.0-pre.12", "dependencies": {"pacote": "^11.1.0", "semver": "^7.1.2", "bin-links": "^2.1.2", "treeverse": "^1.0.1", "npm-package-arg": "^8.0.0", "@npmcli/run-script": "^1.2.1", "mkdirp-infer-owner": "^1.0.2", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.0.0", "read-package-json-fast": "^1.1.0", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.6", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "47d42e41e853460367df4c033f8659364825abcb", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.0-pre.12.tgz", "fileCount": 31, "integrity": "sha512-lAUNVd6bLG28clWO4Oi/pRZnVBcPpwgt0DuscRPwnjjs5lZW92zrWqTf9Kc3uEQPi4yFJTcUPQrsmJiM+VMNOQ==", "signatures": [{"sig": "MEUCIGUbELevKTRATIZ5aJIXcvn6mrxxL5ItLlp3Vy3IR2sEAiEA9jlukkChvwJdUsjDIO7OYiESDNqkIUWgm8HqMypMM0o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 194086, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJecXb7CRA9TVsSAnZWagAAnWMQAJnw5faQAA9VJXbe8hO1\nU33lGdHIndX/FgTrX77V1rVlJiAn2eZcvb3No/0OkgefCsBqwJmT6nVOWKWN\nB1oItg47CwAm6CmVLTL3nb4DyjPEY0gEqMMlMBr0hRLS1uZT2i5RiRZkqhC7\nPNrBxhCerR6ZN56jWmG+nRe+unJcaFEqAUB4/wPjnR6YLqbtvCbRC9qlQjxz\nFXEP3iI+iJkVgiC/XV1zPz7IK3QjLV4B7cklosbi3s7JkUwQzbU7lqUvSIeN\nb7X++QoDOmO81HgAwIF3y/RTMSzzAEDQv/9IR5EhS2+cgQ3HK3stUTR/q3Er\n4HS7472iOdmRyxhduG+qT0ag6RZGdbPwYFdlfjGlBE3ydfB0PMio+xo6/M+7\nTGU1KQUqLl9Ur9JM/M3YTcJeOixAcqEdpZnFnAIj8p/fvaZNLOWWMimyI+Sp\nUqcQXzDogpFleQdf7RhBPAYp9oa+khHg/VwB/wqzof5/H4LRvfK15pHTzDyr\n7u9BqGkgkoBYDkJtUVQQcdqf32jj1hGNvqErWjBSL5GIzIzywVapV+eglHPw\n+7Z8BRPLeqNCLvfEJULQqUwN9Dxtx3SN9wbtBeZ8R6rcjEutqUwYysWJYR/Z\nL1PyVK7dNWqp2+N7r3szhZumZ5+EOXQtV46gEsGI5TXVgLR0mDX7VmqdSPKH\nnGdi\r\n=ecSH\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.0-pre.13": {"name": "@npmcli/arborist", "version": "0.0.0-pre.13", "dependencies": {"pacote": "^11.1.0", "semver": "^7.1.2", "bin-links": "^2.1.2", "treeverse": "^1.0.1", "npm-package-arg": "^8.0.0", "@npmcli/run-script": "^1.2.1", "mkdirp-infer-owner": "^1.0.2", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.0.0", "read-package-json-fast": "^1.1.0", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.6", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "5e3d1a5811ab781a02d482b5c18c1e4909e9ea9a", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.0-pre.13.tgz", "fileCount": 31, "integrity": "sha512-sJjOIIP718HqWJuTU4VPIxj5E2RV88C0SmQF3DwtSuYggcuocFTdo/quDdhZGeRzFxxpqPl7+XvZsBfWC1Qzyg==", "signatures": [{"sig": "MEYCIQDFf20ks0/U0n+5jjnJULnmLe0OgbPPMvqQCROlpRz0PgIhANsi2DG59ieIdPeLEzzXKr4+w3KaTBM6IsHqxIhMFcVD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 194597, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJedD9ZCRA9TVsSAnZWagAAW0QP+gKsA1X8e5adRPGz1U+U\njyqjUqEAx/0RtqWrxmJw/l+A3dOhJMqQAT59Kfu0ZdcBliUd0af1a6c9q2yX\nZXbbOsmHMudXtzPNwwxs78cCpS/oRetAPjEId0Bpk49VH+0IEBnPOXppykzP\nvSdczyk5xirpR5iLnkG5r1MSQJ8m714GdT4i5AXnpxOT3IzTwbeqWLd3z/gC\ntu14aSwlHxITjfhUkd8kP9rKKLgm/aMzyCg+Qtv19Kxudd4Q29U+tAyj1eaX\npJEfD4+wYZkiOoYUC399bhsLvhZwBp5wPsVZp0F3LWe4RYs4d34HULOfUyCD\nuaQtuTWno4oiHaMapM8l13QRP0MabkvcMu0vZEAHqI4VdT+zWgENlljsyjb4\neF4gu5T8AGOumgZeTqD7u8T0C61iNOBAKuBIXH+F+VNji+kTScl6KY+zP/1y\n3F17LvmLyQJecEzJ07TZ9XHiv4+SQYNVkvDhi+gzI9YUveh06PspTN8oHi6N\nHLLis6AB9Jh07xMaWM519ECuK459py5r4eyOLAsepKBTOTuk6tYPZpqVvsZQ\nTRBiBp5ekNBesxqYzc+3waEEhJG848aoSJ/u/HmlKdDGeohHn0r3MJxV+/tj\nxzSQlAUCixIcToZ+oCRF0kLobPFeEnDeuxQqmT+zLDeeYjG4N7WPqE9XOWYw\n30Nm\r\n=7wPx\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.0-pre.14": {"name": "@npmcli/arborist", "version": "0.0.0-pre.14", "dependencies": {"pacote": "^11.1.0", "semver": "^7.1.2", "bin-links": "^2.1.2", "treeverse": "^1.0.1", "npm-package-arg": "^8.0.0", "@npmcli/run-script": "^1.2.1", "mkdirp-infer-owner": "^1.0.2", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.0.0", "read-package-json-fast": "^1.1.0", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.6", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "d0eea6f148ea474f9901e686a8a8064678056a91", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.0-pre.14.tgz", "fileCount": 30, "integrity": "sha512-xQSj+3Jy8KikehSezXZnDwRv9SxFgtnbv4Box7aSm98Z4wTyrOmZv0zmy6YvYFj4e/9BWD55e4DpuJjgV7bazQ==", "signatures": [{"sig": "MEQCIDFq9vAPWa/Syo1g5bq8jO1Zg1eEcANhVNTzftzoWcd7AiBNq0h0fYliUKv7v/tAlGI2sE2TgqLL2+iMqt8EtrKF9g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196669, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJegkFACRA9TVsSAnZWagAA9TUP/31p/Z6p1JBFI5+lCjWL\nH4S7hEhAj8H0zuugxA874gqEboV0zlKLpuxMjYhaBhKNLIBeYE3aJYYxnxms\nP9HCkf86GwQwvaKMJy2nNzofpWeF+YNEcPzE2yNlNxzbRvAO5ID4kgx85f1c\n2isShq4xxWutL8P2Wwyt4+saDyuPnu+tTP/FenRVirOOawTGUFBnSPCMpqgZ\nTDQNIw8i+x71Q/6Zh5X4xegHUXVov4+NM2hAJ98VQZOv5PcEEa6JAdMLs1v4\nnjP68tWYg7/HeuMZ8oj3FYJQTHrP0OLcKFktaTQM/N2+GB8jT4ysXBTfZ5nh\n0nu6u57vHAWXAuz/zK4bz5z+STC0CWDxwbvLcjARPuvaNxwARlImZKm2MdGa\nQtZolsUCa1NAtT7TzW72cO1E5egGHTayOpUGhh4arvIluoqVLWoc4q2r72RF\nqxOUJsUwWwuhprxGB+fBoAzY4xmQ8QSDeZz5JbRwB+e6q5pYdFjokTDQuDzE\nBlYl2UB3e0rM1FSu0O/vcXKMR9XvHJprrIJ3ZWA8iFvQQFRxp7PUu/ifg2yo\noQQB7ICUHLutugREUXiUYPfD491n/Sqe1qnmar67vbtTNb9qKx7RiBNYuSNO\nuSzLC0atO6a2dXEQavJiuy9m1CpsC25BPZvx4STxlFaU/35McV8eNEAHxjPw\nooqs\r\n=k3jZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.0-pre.15": {"name": "@npmcli/arborist", "version": "0.0.0-pre.15", "dependencies": {"pacote": "^11.1.6", "semver": "^7.1.2", "bin-links": "^2.1.2", "treeverse": "^1.0.1", "npm-package-arg": "^8.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.3.1", "mkdirp-infer-owner": "^1.0.2", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.0.0", "read-package-json-fast": "^1.1.0", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "29431b111efb65898ab37b98227bc6fb90f089b2", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.0-pre.15.tgz", "fileCount": 33, "integrity": "sha512-5oEDwNTM07NzL0DhoBQ48EH6YdioR8BjRUgYxYxShQqVU2dPlf2v6NzaXH9Im5ZhwsSz9YkYZW6mZz9o/JMetw==", "signatures": [{"sig": "MEUCIDg/9n1RGJj85Pm4k9+i9+/XFnFAUap7gEK5mIlUfLGaAiEA4H1ZFuxuZWai0GyM+X45cNQ35f1PJ8o0JqCZqGFhJKo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 220949, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJep3qxCRA9TVsSAnZWagAAmgcP/i3XqubSDuJTRC2iQ4A2\nLJ8oDTXT+jBpwxY5LQP4NUZMP4IHImPoaT2ZO/Y8TLzC4IYJUncmMthx3y/2\nIoAkrlU+hINOnrQsJX56K/fB4eb56dQdbyTIjHGEV5xUgcm35YzTyq4zlp/t\nKMIGu+/R9+opZVqcgm4fvhHM3Oo9ueWZeh/JsWNDjpHFWsHE5DEB3aFJazhm\nFlwi1m5uzh/Oqo4eFXatlC8qkpA1hipHIq3hNLbCovtJonhYc460PaKkDQXE\n+3iBKGyuJrre7St6YrPG04NrphZ5CXb01B3Izdxwkwtw0lWLGCgciFTv4qwd\nQrGBbqcDlHwZAbhXZ3FdJmMorowQsDzPMZ+XH6Kq9IOu/NHkEgKW2ONX8vWN\nMnhYJ8vt9cKRD/vsb+f1orrNsIkbF1cVoJ1YSi4bPVFTXpgz5cBtrVCpm/I6\nA9S1ebnsuvA9iRK9zY1yXERfSXJAYWlWeLgjlxlqx973y1D9R0ZtnI6dGUv9\nCIagvMveed0ycoNgJnkSpUMIkhnxcPkmS4MJEZmQeevv5rkpIEqHs5b9FQIV\nFztA4I5ivHcO1/ThqFy1ymLkURbvZKccENIXT/FU5+K4+Of4gqqDOyazbqA8\nWRI3hzPO+jtTdgJNUlvrJS4BW5XfXOFrjEJtvlSaH7+iqnRWeICzwlvnpx5W\nlXQD\r\n=z5m9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.0-pre.16": {"name": "@npmcli/arborist", "version": "0.0.0-pre.16", "dependencies": {"pacote": "^11.1.6", "semver": "^7.1.2", "bin-links": "^2.1.2", "treeverse": "^1.0.1", "npm-package-arg": "^8.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.3.1", "mkdirp-infer-owner": "^1.0.2", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.0.0", "@npmcli/map-workspaces": "0.0.0-pre.1", "read-package-json-fast": "^1.1.0", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "9cb183cc7b9eea4e8ff9e6d7afc0602d67574c67", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.0-pre.16.tgz", "fileCount": 33, "integrity": "sha512-entJfCUj5/ZKz+hP9dPArucWskMb2ST2vA69MvnGWSW+7KWV04CBj9HkNe85IML7iX/k5hlwspSQlpFDHdWAdg==", "signatures": [{"sig": "MEUCIDymZOU4rkYl7Wke0lNDizSUXUyyZ4SD2juKx5GoPOoIAiEAwfHLRbk2a5ISqiYl7r11vZGtPx09q/PZa9MviVtEjcg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 222875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerKz8CRA9TVsSAnZWagAAw/EP/iekpdJe47OdV6E8Y2+n\nFUiixNmgR0rocoH0nOFD0oQUFK/D8So+uBS+BUFe+CDdjvZQ0t73KZH8icQT\ndMnDRYS9jHR8kQfOIrK6B2MxhRzC844ny893GMsDa2TZrcWZqBTl2sPkDMYT\ntQyrYxKRRrJ46aR8BKSgmrqxsd2BSaoe+n/Qt1LxHZnsg4xkklr63lFXXUmG\n29OM/a55kzqUpjOYed0U3xvnlolZXROkcNRQAgYgFrbNml4xNeRzLO8ak6Ix\nrytBrO9d1YiA706Knbl6J9SwWseqReZr0Zmop+nr4PGT+xLD70rARzR77enG\nYXP/Xx0fFc0LdYR0p2oBue0Lf43YphRPyzOhYTfIh7YFTnjPiBSc04p60OwC\ncwrjMeMdgBzTzdbLsqaDnf41/ECI7+aah7AK/+rBKeh2Ccc2JlzCoJ7WoOkS\nl3BgeLcosDmTNKGi/Wl6UbqjTwAFoRH87B/fCcITbNJzuZmn9QTTZp5tzFWI\nrGqErr60bwcjubSAiKAfVlgPf1rfOgQdT0L58Jlp8S9I7U1HbKkfRPVXbkNW\nvgu7Wd3Mk2Boh3RIEDMspreMyN8nXqoSBIOjW699OgBpsB7eUIyxWzpwHkJw\n+4GuRnDRUUEMwSeLXNq7dkqhkzQ9cArjx3PvFt6Fw4lhDMJG7hnBu0zGrGPH\nmc8A\r\n=gCN5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.0-pre.17": {"name": "@npmcli/arborist", "version": "0.0.0-pre.17", "dependencies": {"pacote": "^11.1.6", "semver": "^7.1.2", "bin-links": "^2.1.2", "treeverse": "^1.0.1", "npm-package-arg": "^8.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.3.1", "mkdirp-infer-owner": "^1.0.2", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.0.0", "@npmcli/map-workspaces": "0.0.0-pre.1", "read-package-json-fast": "^1.1.0", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "a12593231f6d65ccfd1a0eaa6e76d7fc24e86ebe", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.0-pre.17.tgz", "fileCount": 33, "integrity": "sha512-kg0Qp+vzpyuQl5AeKaSEI6GVlQTlrcr9YMMvifDjJLGNALyo53/7T+myVHlr6eYb59YFo5nm8ePb+VOdadJBkg==", "signatures": [{"sig": "MEYCIQCul0hyGpaWQdENhfBSwCnWtSQcArD0T10yWo7Z99/yVgIhALuGifhZuH+BFE6OOgpx3+bcS1xRmNTAFu1tqCkkr7FA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 222875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerK1vCRA9TVsSAnZWagAAUu0P/1uPKxdiLCxALbKd8fNj\noNTC9LkxZNuaoS0FD2mnxA4Z94QktgHvqJYwWTSI2qwjm77XOwHGDu9UN7rr\nuKCjvnOK47+IAdxYIkSn9kyYo9nhiUfnA/oceZ49XJde7/2nASsXWbkJU/ox\ngTh/B8JGPE3bGk3i5E6/rs4FD8Sg2mrFNhEyt2pPEh603u7iWRPA8Z7XMcc1\nqEkaAv09x2NNTXW3dIHuuiFOYS74u7AyEMubeSpzjHdxnwYOuPoZrfyK/ZvZ\n5/hSt1hXsF6R7/HpV/gc+ja67BJp007hXRa9ft7ZQbkOyTrpVlicc+XneGey\nulzGVI5/j/M3oERbhBEIjZ73KCq8yQdRft+aXniTcYd15QFePZXNe0ssn4Nv\nvD6OTzMmEBzvda8Ez/c0139mnrI7tEJSxGXSyFr1Xdu1uem2Vw3brsIheBPH\nmi7gQfu1GsIaxSC4y68BjZ+uEIpVB8i2tmka3mTcOTYLQLW0JpL3I4K3j3co\nX9gc/mqvroJtWlem7n6Pj+YV7WghjgqQhp6xydehOPP12mrK0yjfXJkolJdS\n5JMQEgMzv7U3cna+CmYHTBlje/Qff70hIHngBEMDKUgWGnb1Zg6e+gi/t7fN\nxKikUfaLh4PnSXC4IdgXxiaCbKuFJtHhociBH4KEAW06kT3kH8XI9j1q1zMt\n5FkM\r\n=Dv3m\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.0-pre.18": {"name": "@npmcli/arborist", "version": "0.0.0-pre.18", "dependencies": {"pacote": "^11.1.9", "semver": "^7.1.2", "bin-links": "^2.1.2", "treeverse": "^1.0.1", "npm-package-arg": "^8.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.3.1", "mkdirp-infer-owner": "^1.0.2", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.0.0", "@npmcli/map-workspaces": "0.0.0-pre.1", "read-package-json-fast": "^1.1.0", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "b8ed85ff908d0af2f5e371869dd70aeb30b4f637", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.0-pre.18.tgz", "fileCount": 33, "integrity": "sha512-3o+2qNI4K/Ucr/IjPbaPQwwZQuSp7gnKmdSmt3yW2oDcxq9/FIulg3BDJ/crbsT/gkSu1iK16U61m8q2yWPc+A==", "signatures": [{"sig": "MEQCIBFM5aWPgYA9w2YCtcTIXk4la/YQa7H/PDFocMszExfoAiBE80xxIA0U0XAsBBs7sS/s3JmIufHriTovuYoIgEeFZw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 224083, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesJjACRA9TVsSAnZWagAAo3QQAIZ7fxzbVaWlqoMrsrZF\nasiLkmczeWTGbPwZXybTg5DiALkbbtSVXT2b1ZUupVU/XRcQ/AzB+oE12i5x\nD7lQfdTG9gKIOXiNvRiTeHxhBnbtemGQx42Muxp3wF7GlbI2E/0VJAdIVTxW\nqF3H86h1HsQm9xyV/IH/npOUEOekj1UGi88CCe/9cctFEtCSViUWRN0SZjRp\nyBMLeqrHWc4f881+pD0ieSY2mHfQBAPvComj7s/52BESYJNq7+y7DlZlxQzM\nu06vnNkXiNpHc3udMQXfmaFcrP9hhIXGL26OHPeoIHDETY54ezP6VknCWiEF\nWeeDobA/6DJMRaCvejL2rNBcG8or0l3OpI4cWn0oZVBm0Mag1jmLkkdH57jy\nZLq7YDEp8YWtNZmIX2yG2hq+oTgQCrArLcJ3ZiFx8UOBqqxG5a31qV22+SJ4\n/mKfIQrMmvRcfugQPKMU29Z0/Bq/OTZ/ep3QCStXj4/JULpv1+9rqnDCD8o/\nfzojGlj3EWX70D1QMDTu1GAcg34z5y992Ko9cXjvgjK2BgwoDjKOf2nKS0mN\nUV39Lb/k3zgaNFBQC2DwCzs8ZJR+0OlSHdOwpLQbGe/NiPBpApZzqUzGitkv\nenwyLELa2EFErpCyAXe4UeVwLcakqxwAg7A+g6O5gX9uaxhXJbILoVN/wLlD\n0i8y\r\n=XGh/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.0-pre.19": {"name": "@npmcli/arborist", "version": "0.0.0-pre.19", "dependencies": {"pacote": "^11.1.9", "semver": "^7.1.2", "bin-links": "^2.1.2", "treeverse": "^1.0.1", "npm-package-arg": "^8.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.3.1", "mkdirp-infer-owner": "^1.0.2", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.0.0", "@npmcli/map-workspaces": "0.0.0-pre.1", "read-package-json-fast": "^1.1.0", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "bb33d5bc383226c7b379832dcbf32d9004d946ac", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.0-pre.19.tgz", "fileCount": 33, "integrity": "sha512-ddHXJyMNlrmrgvVuuca4qAXO9sfpZyMaDnePnjdezkG2snVWbQMUxV6S4zRS3aUtiQOL8E6GPROGRjBRkMDYvA==", "signatures": [{"sig": "MEQCIGOajXXbcxAGtYfKtPWpgVPqBV4A8ZVQduw6Eq245in/AiAA4kIwwRqdO0S3Juwe8oqGXCR8x3lGKDi9TqkbP4WWyA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 224989, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesQmfCRA9TVsSAnZWagAAHtgP/jXPjifguvt+MYG2bBSK\nDd/JD4DOyT7qvLV5Q759y2HlhH08qPUtAINkdboP2/k4ByYWRh17jB3rrW3s\nuTkHfGj5ooHEGAKvzUMzQtUnNqXVuJ1r0QDOk3MosPvqY2K77e8vTbSnpOhp\nwrusogTdM6byeRiR552YRfRtesF66opU8b56J/BKdPCzdB6Pe9nJbJwc7tmC\nuU2sERKxzMa0RN11WGnXtXPklzdBzIPoguXl515NgDunP8hX5YzMaz0rcZUg\nYayvQlJXKqKPyiGL6FIqeK8KpnwS2TEAROvbY3h5gRl5qFY1nDKLl135NkuC\nlbww0l0GKmC3bI44K+gRxSi58BW0D74sy/YsjXDEmHik6HboK4qA/70CdqYp\nKNy8r0uwq2gd82X8/iYTc0soB02Lj4Uqzg6BHkloQ2STH1NoWCawUaaUWkgD\ncxpIyuWjsWly5buyB2B4JB7dE8m+kJHuxGEcmJJJRYzze+pB+QFtVaZnxnVT\n6cbQxVgsMZWzMbPWvp8EdRmo16TxTnk8xRSIt4BRAurJE274e73O3L/JZmU3\nv7+ooFeihPM+rncMGaRv8WH2Wrtdob5Wk8U/rKJSojYiSUsrBmwDMV5NTaRI\nmTXmGIGrEhXWfqt1ieIQBPLB02Vv6bhWCRADhWK81TVPxVGnh5x5tVnYqczU\nwlbD\r\n=h761\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.0-pre.20": {"name": "@npmcli/arborist", "version": "0.0.0-pre.20", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.1.2", "treeverse": "^1.0.1", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.3.1", "mkdirp-infer-owner": "^1.0.2", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "0.0.0-pre.1", "read-package-json-fast": "^1.1.0", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "1083553530a57587e6aa06d3375c13a63b7be534", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.0-pre.20.tgz", "fileCount": 37, "integrity": "sha512-ILYXVLsWmqdC9UJY238NOXkNnfM9/3dhQKwAApPRDIgmciC/rUb7KIg5R3sbDA/LmurXGZ3wgtzgn6VokJfqZQ==", "signatures": [{"sig": "MEQCIBp5jochDS+RquUXMigFr3hEAJJk5GcyP1Aq+UG5NJCzAiBZ6OqC01sjhUNH2i1j1TmX68dJu7VxQ85BxEHxEh5ZXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 244742, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe1s5oCRA9TVsSAnZWagAAP8AP/ROmZ5tJ2qcy3Sq7SdoI\nRWodcCRLEskD1qiFnSbOUfYFL4KUQ7/VnqtQaumTNdlTym3l/wuFOVA1p10k\nREbUZ/rDUd2zJ2Si0JJvh0KBZtnGfqPCgpjDryJO5SzVTVt7Xdgb3oemaxDO\nsUAehAV1i9loP4MtAQ6yPwd9SqJB3VtHQB1k7Ehgi89V2Qvi42YwaNWo54FK\n0QEbK7qk7G8jYgWrIrhOcdb9ebGOsbhirKATbjwUV2vnNI/uq8HH43+pblEd\n8j0pw68VTdKTeg+osO2e9DQ4+wGRhe3V/5wudPF98isCTgNx4zx7Fd3rNajC\nkycpbpaUSaJwEcqSF4e094ygsugj36EoQlx+z1CNV4mNKPhALCwEbhCv+Ief\nBzY4QgkUDRJyRxnlaFugANQiHJuyZOy0cpGFJDJ2GWohTNzfOX4OSDc+ugnk\n8vKDFZnjuMEYdjanadWCvdPLyPtZHc2djj489K6CkXPMJqalFfcgWSkUiVoX\n0tXh5Hr0K1FDGHlD+Lyz2TP55oKxqZs1t3ZDJnM5wrr9DMMqEPoVFn4yiGxq\nKTPrFxl7D8tbyydv3N66S9RnQnqjGInlNX4I/FN2//hCZqIp5T05dt+XGqav\n5K1DC/6uItBkLdbO7AABBb0bON71rYlOftRRvbWlJcRJgp+81ho36tLd0gDp\n5bnr\r\n=TEZw\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.0-pre.21": {"name": "@npmcli/arborist", "version": "0.0.0-pre.21", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.1.2", "treeverse": "^1.0.1", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.3.1", "mkdirp-infer-owner": "^1.0.2", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "0.0.0-pre.1", "read-package-json-fast": "^1.1.0", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "1b078c9dfa3339af9a13b0c52c056be9e13484c9", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.0-pre.21.tgz", "fileCount": 37, "integrity": "sha512-WQ/t8dmeo5ea8+8WRTojLetS6rBbyC5i+GAFyH91YGe+T2X3OLhHV0xkif9TEjoGE0Nb2HGYXXk8NBq6hcwmKA==", "signatures": [{"sig": "MEUCIEREQXjLfx0g2C80Dzc3r8uuLAD0z9gtUrkVdeAp/TsMAiEA3ejsRz0Wnz8a3sEepInUobGJWVmAvmn5SbnHz5yJgEs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 250104, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe2tCgCRA9TVsSAnZWagAAi9oP/jCYprgi89l/axZ6YC/U\neLqzbxxG/1EddhMKkYJtO25mgEz/hb/3DHEJtp7z2HMIHCyvZ9OcPOa0I0Jw\nDAehTpRe2qYjgEvVAtRZQEgF5kZRS4+arwXMrNquZ4cK9ufAkdLpMGhYbgAL\nCM5EhrtQqpPiShusBr6O6HHox30EyXyT3SjWNXX7rXVDQsnXRQ3TvSBXjsCg\nsMx5YhuBUxbp4LheNuNaDR/FMRfDPTkbCpsLTds0vhGDl0wRQgGURv9s+WBv\nF4ole58wyf87BlHDaZjvmGXSC5gm94nFDtRViW92C6xfHUmFDImM7DPLsQ8A\nyVqXVaZYKLpMuOSbzsZX+gOXeAlAKqSnR5U5SFUyzAAHHZASi5FryO4cvakL\n4z2/Cjq5FRZLouc/XQ/JRl/bqIn9bHtQgGdD0qCN54V4N7o5qahkfZuEZr+j\n9FY8dfGhwc1asIGPtKTtuXuesVqzi2RrS1hRlapZh/gpkIzEJZZUX4aCJz9x\n1375wmaOOKiwhBKwchJ7GwNtRM1MyznoMRL+Dt6nRBM2HLRmVDiuwS4gcIam\nxVOxAaStJIzyvgzWPTIQTivixcDh0VkH8V7OkR0vpwxjti9nC/S/pJIUaZlU\nd9yQ9MtPGPAtwEGnZWRJdI149OZkgVj7CpSrdwQC6H37GLtc8P8o37e57gnr\n2onm\r\n=Ger+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.0": {"name": "@npmcli/arborist", "version": "0.0.0", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.1.2", "treeverse": "^1.0.1", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.3.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "0.0.0-pre.1", "read-package-json-fast": "^1.1.0", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "b53dfcaf8da96c0b7c8a8f9a92c3f15b63b37cbd", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.0.tgz", "fileCount": 37, "integrity": "sha512-rO<PERSON>oof4b4mtNSYIEjJJI+WK+l40T9RyMMvjWwR2KNbHaqqDkJ5NuzT8uzir3yMCIYoHWJRiRImc8dlXqVVA3w==", "signatures": [{"sig": "MEQCIBts9SvDDHe5lYOhaEQwsQ1UZ2899JYgdGzB/Ea65jSaAiBvjmZTYI9TV81LQXSwDFbInlAkWZZY5V3eisFY58tuFA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 250097, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfCQrkCRA9TVsSAnZWagAApNgP/2d0P02baCDySHkYU1xo\n4tH+cxV2V3QUlpiR+/C5nx2YTIFRJwjvYtnrDc1yNpmAjDQdzGyXCuy3VLdo\nYizTux7qVT0isNs1tExH75pIMqXB6ojR/slzPstcrfTpk1TacQHiRJAPvobA\nvOKBRiui4T+7HoQrVBKcFxtPoyrBrTh1cpnOcMZpDWg7Twb/aHVjIR/PaH2n\nHBDm1NQvwOfjsxh4WSfkXA2ESponvX81+OCmGYNaOQeCzd0hJfDcTvJEQ5aR\ngj2QBGf7WLUKpir3qq4Udi51RdnCz/pvoasvgj1iLBmwWjlqAt8kPYnpd95K\n1qtVXgoAJVUklld8Ce4qrOk1zFEHeBs9ro6+G++6Nh8uxXLwI/DSibjKpqoI\npvgxk8LNlQ4zqGXPVLBlNUzBJ8Vu8u9VkvgKAwKDXyyZo1/wXPDAMv4eSN3u\nOt3X1wEyTfDsixIBXmwQ30wDJMlEN5K3Id+0tPl6JIHdlN+WqYrJ+ey8D/OQ\nN0rGJuUsZZrpwE5U2MBbRKDcJeArphcM5cg/2OBHxFA0EgKv5bgMzB6xlJnz\nh25E792cctU2fS3uEny/aFFxLgdXTLNiLeWU3jyTfVeanX7h4R1HYYeGOPbY\nzP9G1IsOnEMfTWg0JTwNmbVB90viZt01213OleEleA6Ffm3ZFerqxLfOhe5F\nwvdz\r\n=6PGH\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.1": {"name": "@npmcli/arborist", "version": "0.0.1", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.1.2", "treeverse": "^1.0.1", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.3.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "0.0.0-pre.1", "read-package-json-fast": "^1.1.0", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "6b05fa7dd4207fbf5f592c66e3e06a429f5ccffd", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.1.tgz", "fileCount": 37, "integrity": "sha512-i/ir1MAzc4dHLMwWCoyszanE2wMMes36iYxf8BeoTFnO+NSwNrznwPD/yNcXO3JaH89h/nzvM9SMrmrOCPOYmQ==", "signatures": [{"sig": "MEQCIGIlanXx/bhSYOHwWrz0nKedzjqsTawp9k4DGxBDbaWBAiB8d3AExcJQ6oUF10yddsp+dM4GjLWKdJFe5FrJkjbUKw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 250848, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfDjbmCRA9TVsSAnZWagAAZVgQAJMjMRA1Y6/6v6j90I4h\nw+cOAioyBgoqhfCJ96Mv+ahSwZmZ2MD3ScIk5mgDXDvjkC/VWA/pdbKbDlaO\nRN3/HJT8bBglmXZsnLSgAMomhF4KR4D2xzFwOFBr1KO9VnbY3s3EdoG9O8YH\nqISaZ3aZKy/QmIoQIK1I6ArWj//ir3vPMxzdNfAZin/V9YrfDMkqUJQUDuFG\nFs67VGXTwfveomnF/h4FpZHnMfP+k49jCqaaoQ0LyoAENKUIBEwIMdeVdSsl\nashPIRop2H08LRmiSG0RYXnalROrSIEwJGBy6GHPGq330YzHPtzs3/qSdVsV\nP/MXDfHL6QDJHTmgdb620/R61sPd8g0rsdINh5zhLq06ZrOfZw7RL4SIJYCY\nbf2E86nISMNPo2vsIyBc6nYhbOMZ42syc4n8YjkH0o3N4kkTWduuD6Seb6GH\nRe7oC/sBPzFqlQw37ckGSPc/oxvFhl92e2cx6+zQxEVxkz1+yZcSAO6lD1r7\nSeFkbq1Lns0KlRZSufg075PeFVY9893Jj38PcVzBqD37E2UlXGOTYRf8lrCL\nlX0EAQtfuzaxm5Mf71E05iKeP5YuApCBrHIBehIQ450FhQbnmGAmov58j0zF\nCztfxL7+xMcnzUWBCh7tXXJqY1CpwZgY6mK1picSbXHdfqQvVLmPzIU9j5ou\ncH5h\r\n=3YlD\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.2": {"name": "@npmcli/arborist", "version": "0.0.2", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.1.2", "treeverse": "^1.0.1", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.3.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "0.0.0-pre.1", "read-package-json-fast": "^1.1.0", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "b50f05875bbaafab8a8416de26911ad275bca62a", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.2.tgz", "fileCount": 37, "integrity": "sha512-iFDEbI44qtyt+Qa1ZBLO/a83Qpf7/Y/ohGn8HJg0RWroK1Q83HQIb1RkcRJR2kpTfCVjgPpfnC7llZirGp06Kw==", "signatures": [{"sig": "MEUCIH5BmkR/nyOAYquKXbtCpYyWt5H2Z9UeCvNpD7XlSbenAiEAy7Y8x5JuwCDF28LdYKLDk1M2hbXhCAY3tHvzA6u3fxs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 250903, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfEKE9CRA9TVsSAnZWagAAyMsP/21y1184X1ONV8sy5BIu\n4GETW6mBw4ZKUhGcKuxX0dI89BkMDFKqMxDnwiutT6UiPuhHRI40M7uXm10z\nUYrHtso9s3TkSUAJpP+Jp/K29rH1MsKdS9yHHtHJCBxJHBeEj+BH1Dseebqb\neyrYt8k/Bc3B7ftVgqyCBDvRlCQMEHM2wN8GIy81ehc69WLqtEq5iuHucRWD\n4z4142jrDqUqFQxBwV/z5vxWmY+*********************************\nqVh4Im6JbCSRMs/0i+dpWKBn5PBWvFzVUv0r0D9A/VrK1iJ6YydfzXi8QzdW\nxycxLcqp20+XOZ2JZ9+05gVMahl6K0BuZAxBWe6IhBTcb7YQ47Nifq4S1E8D\n9INRYvz1wnGnLCSemB3nvXzfSzFRQoeg+6RHGlWE+taK5KLS/Y/a5g30IJOp\nWKKJHI6lqdnRQHWo6qMsLqSg1L1fEUymQo+wl+kPwI3DwSYdO0ntv1acqbhM\nf+/p3rj20WdaaQCmXOyUH5rgKiI7IZswagWCOU6gs2sj2UbvxTmyZKf37nZE\nRtfN8Uy8/rX9lRFPfCVsCg6YWFFc3H83Y4DVoXC8DRjRg58qeB3XXPyMFkFG\nMzFW+UPQUHNrxxB7W6QxY+SN7VKCNFAVqao+gI6YX0he21FAfD0Oo+GKoIOx\nhc9N\r\n=VidT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.3": {"name": "@npmcli/arborist", "version": "0.0.3", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.1.2", "treeverse": "^1.0.1", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.3.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "0.0.0-pre.1", "read-package-json-fast": "^1.1.0", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "4c199a2a0be69fa53cd91861cbf58bf3f04e81c6", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.3.tgz", "fileCount": 37, "integrity": "sha512-w9YiLAU1FeI8Nw4NfZYVBZ3ESyvXGsB9B7cJ1PzW7SPvhD52Ezbc2c8AFvHV7gmelB2ZNzkAwy45RVOOgTfjhg==", "signatures": [{"sig": "MEYCIQCNmkUcAfK/bYTKHzBRbAAzf6LR89QfCfufIaQy83oTbQIhANUVtLo7yuDP5cHwmB9UeXpKUol6sl4uUXXw0UaZf3Rc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 251005, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfEez0CRA9TVsSAnZWagAAFo8P/0nM/ZaAlql6QnnwjQiv\nsnQfXqroLWMVFJ3SNKoqOZzHYp77kyBbfq0cwA9bc+W488E1EXnZyb4hfXDz\nTPOjDDWJjtF1FxYXkJSzPBreaNJzrIsLLZR5KNjqqDu/gQxpTwv35c0tWlt4\n61UvJieYTiGyWrTy5/6P7G+vbk9xtXLyvGaMLlPBrGZ252rujQ7jO6WrArvm\nfbV6HZYYUGDB7qJ9+nfyX9Eyj2n6vKfXdr8r7rHvdowV+aTW52PA+8NT5YL8\ntJOTuBLR2ElvpltsPF/E6Aguhq7KzKbPmuH1CuLN6ACkEk0PZell7430C5pt\n+8y8Whq42EDyLJwzrz15IYiCtRcvD7JY+ztLxHEl2I9w7k/YNLtgv+dJq/6U\nuj6EfuZoJfemw4fdbjYM4sPKZVxztExghP5b4Je/piedD/ybY4yx1UmDY3dQ\nZzwDGWR5C0oWee5aiHFQLfulBXHxKuJXvwwfrBz2I4v7YA8Trk6IzU/OEvsg\neRbstORZYg+sTSqUGhwTfLbdRXoFy3Me/YyXjlJNaF8QQrBI6NZvJVPduJrW\nnk4iGJKfIlRVaJTVnQPGBgIL+58GkJV0W/CcO/1PC/S8MfJDNFlERtz+fTXQ\n5Pso7/XdpT0YORXlG1cJ45N2ANZc96anDtv7ymoahQOKOUmLzacpOuHfVSC7\n5M6W\r\n=c/hB\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.4": {"name": "@npmcli/arborist", "version": "0.0.4", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.1.2", "treeverse": "^1.0.1", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.3.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "0.0.0-pre.1", "read-package-json-fast": "^1.1.0", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "fa03605939106f9c574f4bb92ca25735590ec1e6", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.4.tgz", "fileCount": 37, "integrity": "sha512-rqgirIme4N8jtCdu+n6EyauZDNEis1AXeVhSiOQM/OrVaoqWCDjIUnAjItZ8AJfianN2s/OB3Gln7E1d0lUm8A==", "signatures": [{"sig": "MEQCICimtCP+IrYIfZAcUrJ2XqARhviYhfS20BUwW2dRxgJxAiAFPIbkJK0IixdSTTfirsQsTCKuLs0baPF135IXpnsN1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 251780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfGyYHCRA9TVsSAnZWagAASmMQAIqeLVowxQphcOo6bmyT\nE7ibyI2/vSLIONUBn3nn1OcnJXFDmW4aWR/Sl2LwioJ+UW09IpGfXWEeplkb\n2WAhxQvD6Tu30kGDsQBumoVMQCwSSnWiMym0KeMnjMc4VLumthUwVyvy49Vl\nKN9AfrgWVCOmCw+YEhuaUM11tz6UlVulYvEzjD8xFxWCKn2P1Ps6Cqz/Skzx\nqWpM7YrVpmYbkgLN1NyMIGYi7F1PGzsHL+vnBo2LRF2eO0rU/48omgJ+Cdsc\n6bjztISkXo6deNmvF+p5jky6xGAGSISkeqU3r6RFIBel0L8mit5RBDewEVu4\n9NpgJnFYiygJfpRBesBpJJ7+gX5ipPvct0o1hX3uNLm0hjeP3s8ubu6ruRqL\nBqpcgLAkRVhwgjLPEqajczzzqqSnzXPoxMQWBqvPxdlNNjx5xa9Q0qKwWFHg\nHZDRuPnApmPdxqppITBy8G1KCn27n8pY+oCcgGscTCjacXUztTYFtKZh6DpJ\nKvSDzGhrmomdSyTGilA7HpfiF8dLEGPPfgp4A8pjvHdKWS1SKFvmrw49TKnb\nCGAV0EZUeTc/mfGpPDwWES0wnvhattayjKbio0VJVt2tzJ0C9vTqcwjyrPtb\nVU7v/UEmnoiaQh6ESnkdkPhrhwpHuLdTLAqKIo+FljVLJ6vhYh9smnYAH5HQ\nVCUy\r\n=vJjG\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.5": {"name": "@npmcli/arborist", "version": "0.0.5", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.1.2", "treeverse": "^1.0.1", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.3.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "0.0.0-pre.1", "read-package-json-fast": "^1.1.0", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "ed5786186ab25b0dea2c29af86ac0061ddc9232c", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.5.tgz", "fileCount": 37, "integrity": "sha512-7Khg3ORXxDCnp7e/ocjYESUbZMe1aUm0Oc8HbovuvgCigSUT6WHfNh68RyM3nHHmwjz3l/Xepw6XyPjo9da5oA==", "signatures": [{"sig": "MEUCID12E4/Rcg66yFdO5PKVuUrlEtO1fmFqDByqlUQeIDCSAiEArpaZYviieR9jujlH3Z79qfEfrOLGVfVBv7rWCM5IACc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 252120, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfGzT7CRA9TVsSAnZWagAAY4cP/3oy5pmwunXIAihXKVgi\nAKkZIPGsa7HoZRt1cUN3qMtBlSh+v2D0R1dGu+TjQk9qSiZcIcXIadNI4Fja\n4Mk92J73zoQgFgGMZtjgY+DXHb7/1SL8W/VclogHalwrqAO7swvmbwX+ZCcg\nph1zw+U7VS71SJV0OFXoEB1AIep2FPPZF6KqxQYTYsHu9LaSMOBjxPriDJnO\nnhPd/BFKj4MsPi+LdKWblCzka+Jyvepp0SRNytz1tJbK+FoUAZ/BpJiUSxOv\nESIn5hulsxayBkUBBIFV77gMwXvR6UPiyPKD3x5LmbbitkGYsP2dCoH+iiWo\njY7Tsz7k62ACROAOkjikmQw+sWIfJRgz2Xa/tS7RuAbiigbKUq1OzzrzoyYR\nYDZHIAlAr9TIsi3JqY6PdVjYfAaz/uISLro7BDekmobhdw/cy0XmQSOUfDPe\nh/J8cSeUe+/8kVjFy3tilF8TLCtfysa3BlNJ9v5Th8FD9aRw8lsuUfF7sWxr\nKa4FqdumC9WSfc6BgANZW4hldVLZ4Seg804S2vhrEpvI5aa07XqF3K+/h6lC\nV4cm3ioV+26JkvD8WTGFuF9cViV9O3WwFVI81JXkyQ2OWG4emOkK+auOYpCy\nNxUycZF0FtWwz+LMPFhLW6HCCEisDF5ldB0cAsUSGMKYfuajiVq8i6FJSbW6\nH4DS\r\n=FLgi\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.6": {"name": "@npmcli/arborist", "version": "0.0.6", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.1.2", "treeverse": "^1.0.1", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.3.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "0.0.0-pre.1", "read-package-json-fast": "^1.1.0", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "2d153dfdbbed53280693cb3595df47949c831857", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.6.tgz", "fileCount": 37, "integrity": "sha512-owWPScECmYbC6S8hs4N9djqtH10YnwHAwWGVqE9mRncYs4dNS8XIu+Mk9N/XZozpkIiZ+DAm8o5n9x8Y4kFEqg==", "signatures": [{"sig": "MEYCIQCJlPiRRXvHGmaapScqSA0XUQK14aTkFtrpLPrq0prluAIhAJUl8L1t8Cmfu6VdK4mK5peHkhPnJ8bXS/Q2CJIANiH9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 252531, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfG4KqCRA9TVsSAnZWagAATpYP/2F+FqiXGPzO0Ng+QoaA\nE2a6H1ALWH2z1ihk1nNOQ4yYrC71dD9xeh2lFbvpmiBxPvA4nJPoS7if0veN\nLjuXnteOCCguuFTC1XDegMH78BLG+fyFiCxHVBVoQ77wwvZlh7NdTK+qXEH8\nMU2yzH6slrkO2mLdx/O740SxOkByjTM/cu4kMCKWHXngUSpIcaugERSWIqbT\nHfGvr5sBGXQ7nXkfkDlMCoCdBbd2Z+THOgJNqb33THX7quCPJ2Mb8kMnfsi5\nU7yb81nMwqd3ZQB6Q1ovhVRhv10QCYZZBbNywaNhwVF5GjfMWgVBb3hdm8jj\n7ok5WHHYh56/s+iRssAgPdFkuC+s4Y+rjgSXQYvnQENcbGuSOzBztKWE5Xou\nUzpBfVECBEf8L4saz+yt6cyY3Ul5njXf1lCWIGLLp4teaImdNQoRmXAoxheX\nrrmeyqSsFvwuc8/IKHR03hcRQqWt5dgsy0rFS9V3JcFFKnuZJKsqmWVUXa6x\n/xikEdY9MdrSc3Is7x7R6sC9YeEfH5ssY99phoUfTWLX5P/Tk5vYYM2874Hm\nOpJWtBxfsvTSashKkDf6bqOmnOy1VUWcu9iDy/bSChAESqVEECtXtgih3DUc\nFyC2M50RbPgt8URsE6gr5xvOlE31IzQR3289kXxrHe+XThrqoh37QSKF2qIH\n6A+u\r\n=bW8g\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.7": {"name": "@npmcli/arborist", "version": "0.0.7", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.1.2", "treeverse": "^1.0.1", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.3.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "0.0.0-pre.1", "read-package-json-fast": "^1.1.0", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "7254e9368ac996fcbb1de9edd840e632536f2427", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.7.tgz", "fileCount": 37, "integrity": "sha512-W2CfAYE/GtsqetL1CBj6/n6hhDw3L4EOEaZG3MZ/hLC1NsOuk0W2914BWIR7GP5SPL5IMWimH36+PZIWauzK9Q==", "signatures": [{"sig": "MEQCIAtKtxF6MpHUC+BWI2FUkABk6geOnUdZ8Esf3Jcc2pU9AiBlW0hhHCkLL6AHTN2DCBCVvpfVTR9V2umecuhamTiBGA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 252902, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfHJCqCRA9TVsSAnZWagAAkZgP/iblkjiKYLfAM9t3EPXX\nYdgV+TkAVO6xBFppp99TQI9R9wt3QgpPQf5++KEhLfw6DpzpV2b1U8B78kQl\nRCgHLiYn0zWDHMRxPSXs1QpyDBtNFfN0pDrGR4i38wtZmHA+tNhXyjfo8OE7\n7tcvAvCd4DNVfIq+BVKfIPRsddvPGS6+cjUyr40ZwJppEdupKU0IwYd+JGf5\nGGOrLyqyq3CubKq2e4ujjWCUTU4DkOFw/ghELZ4PGEEMFR8PO589lBXL+C6l\np8V/zz4HBFgWGYAdjTSqj8jL72PIaZ/+RRfyKCb7fhcKkvr7wBu6IzeEBQXB\nOahKwOlhnA+azJg8X0z7qsZCiwDZZ+DFwGcHt+5lPCuSlo5M6WLutjREVIjc\nN7LvAcsomYhA+TB+TwNxAV0AmEq7JcI2wd1LM3gg88K7Tzc3zlkmg4Sxy1jb\nGiyH6/l/0+2iR9WsiEUmOMxc0NtJb4wRgDjVaWN6aNQ0UgwfmW8LZ9LKbuNL\n9wzrMlTMyNeqRpXhcep28z52eMNOh2IHujGLUVCk8lwbpgA33TH0G9B/NX1i\ncpmGBs6HnAA/pGLxY0mRU/baOmIAC7IlNl2FCgQeIku7thd31UiOeFjClr2A\ncmrCtxAKc3xcr6d0/dSy83pl9WJ5CfPaYdMg2inCg7xzbRYFOPGuB8z95rQA\n7SNq\r\n=iokg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.8": {"name": "@npmcli/arborist", "version": "0.0.8", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.1.2", "treeverse": "^1.0.1", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.3.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "0.0.0-pre.1", "read-package-json-fast": "^1.1.0", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "292251e4e33ab720419f135968337f7455cd2b21", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.8.tgz", "fileCount": 37, "integrity": "sha512-MK8HpdMnFPXpj55NytXsgVo4MSd28l7FnzBcNBX2se+00njeydEuHAZfewSJFzFWHyPTqX3Cp+nKs1Z2Y5Kwmg==", "signatures": [{"sig": "MEUCIQCq0y/zAJh4gPnaS9PyLjxMzl0xQhJ2sUWLWpLQftH/FQIgHJWSR7linBdcUq4xSpvyNklYYr6Lgho7RKB5sPRI7RU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 253160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfHwdXCRA9TVsSAnZWagAAw7UP/336wkSY2jQwtoyxwojp\n4cwwXhcOXy4kT6oSqhXQcEY/2Sem0e8oHBa/0wH5tXQVWWnoR9nJQVflMZ4q\n8yoaMLd4I8YoIse58PCU71X6oUZYfBEzFRjN9BPIVN/MvjLAKa9fF5IBbwC9\n8Vn88IuMLu4x9MikS+OnHCeju6ct8iEiAj3KZqJj74d1FQajHDWgJWvbLihe\nWnXH5ua0375s5r13bsaVCPZ79zI4LI8Ad8hm07872NOQJFtQJrO8ryE9ACa2\ny9lXjEplsM5G7dZwskDL8FCvRYOMaCWWsQyTmMKmAaPdC0aYSzJKVx1aVxop\nxvXc9I35oUOjk8CTG+fN308aJBCN/RrmoafhhX91Z7onJjnOX0Kv3RK2YVHb\nxClEv06U4DSe3quGCFCO97Ol0saLDjin+aUErHYCIz7CMgEA6ERugP4hcr/i\n6Crgd8FZSptPPuwT36uA1j5XFntJFjdjb2uVJA7Yb5eFUhP2E3E2hjPZQuwY\nflhWz+EgRIATb6qB8A0kdW/HMVS1nGur2eRz3WxUxHjwRLtPUNl5YfznvvSH\nC6aYDQeZJgZDZxbVemLxBUavDlAvK6iocGGHGTFDUuKBK6Xl8sQJPSpyIlro\nmjuIaWG6hHS1suZM6Q0qLFgvMcuwM4PDY+sG2qw5s/9+CXsc2urY+ZAMMg9O\nijc2\r\n=OBGQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.9": {"name": "@npmcli/arborist", "version": "0.0.9", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.1.2", "treeverse": "^1.0.1", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.3.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "0.0.0-pre.1", "read-package-json-fast": "^1.1.0", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "bdc759703109ceb9dbade1c42c36d2a20339cf96", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.9.tgz", "fileCount": 37, "integrity": "sha512-rRqVn7HHPORzvI8Wwsc1EOYZjC9SFLuIlOawlELC/UPOC7gACOGzYKyWBzSjNcRKPsE/ebcUP+8JD/8wiFD9rw==", "signatures": [{"sig": "MEUCIBeTrVZ2ulcKclj/NkxMtm6AEVIXVj/LRFgEr3zYXyrZAiEA45Rcf8QgRGHKn0hKnAE+E9dOVWE9f2dniVOAt1WNRqk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 253163, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfH0E8CRA9TVsSAnZWagAAkvkP/0vVbU6l4g935hWHNkSz\necZN9hyd46lTGUAuAJzrxblpschqrant6u5FMxpYniGMvIRuTx5idU7MAFEe\nQHb2wt5o5gICToN0yOrCyQuOK7YEKsw4xxs2t1BX8E1m6tbNgDMFifoX5j1x\nxqArTpBLK3M++oQdzw3AXcUbU01M9FalObTotPREBcrD+g1wE/4+9QsiQzLe\nEw1GNs9ebsZ2Kbc+TqfGWHfiJpHUKS7MwycaYXPf6TOfGStv9Y4HFSNEetdZ\nhfW+nT32FJepHegeQbAe3gsLypggHWYDMXu0C5Pj7UtICPJfgwxKTpfFd1gr\nDofd4Ry4EBwnSAzilgCT+FV41IZz5TZwCPTfqNYlo8HfjZktSqD3hyukx2uU\nFLFCw+C5wbaBAy2fd7FZq6BPjtNh+p4sHST3bkTRPeW5i73Voi43Vqnk/qCe\nhXa9nkRreEb11f82SOna6sGBSH6v4NAU7NSRat+O6Td5Zqllu+t4ujmwhMmw\nSQYtaEyrvJxK1TptNFJ8O++pvjjL24rvf2v7U42fD6a8VuSyK6YECcOKelZo\nqOYfV7ZmmKDcCvE9MyUatkZTb7Ah+hmVwn0ipoZINbWnQP0XDCk4HPvFgSMe\neuO2KKwYW2kRNpUsz3PhXa6YEUOuGCJlpRQNEneJXyRPTD9ntIAqx+GK6GIH\nPmOc\r\n=G/RF\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.10": {"name": "@npmcli/arborist", "version": "0.0.10", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.1.2", "treeverse": "^1.0.1", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.3.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "0.0.0-pre.1", "read-package-json-fast": "^1.1.0", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "8f9e46de59ce8e0eb7f3fd00f2f476b259a712dd", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.10.tgz", "fileCount": 37, "integrity": "sha512-MWHybKXLkgc1Ds4aZjwmIL6G3ItK0wvKMG8oLt5XHrsE5sII0Vb83+8xT4hRGg+H17K/JfFSLF8UPilPidLy5w==", "signatures": [{"sig": "MEUCIFzRr7yGYydiTpZTZtVbRlV/aseaoGogfIBOwxcJyRt1AiEAg6Fzw7VQar0yzL7xj47+jkzLlkQ1/y8NCUrCWbIFqms=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 254588, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIbV4CRA9TVsSAnZWagAAi78P/jXnNFaUc7OWUgh6IKYW\nU0f+1QDxjvA5DY6VDo16+XWwVngAShQpTGQ9eDFlPQzA9CxIQgyMx4HVmL9S\nhK4R/6Htli/9micONZRuR3FZZRywFGaZqUXAApkH7M3FlEWGVMmK3PZ0g2dH\nRq6AT35Yyg4oD8j4i6vrdLnpwDEN2PSPgxBRo7Y458i1DLp2ee7wWy4wdUE+\n/C63dvvivWlW8vECNgHVzJuxAFDvQtiMEDSfDyT8LwKQMQ9LErEz+2/M+ewu\nDfKUDeDTwzumNeLi10r7BV3xUin4fqY7Yf3VnIodI16PO0Kh1DEtmZODEKhP\nMgaLtYAXR58iN7T4Qs809c1IYWLzB6mAWXGU3aEYRJOl6OjO9lseFPbPU8c9\nAFqC5Ws9V28g5ReL3sU2YGawDgCk+ciclqfad2cS1ngwLJEUnWhGDlOD4Y6Z\nIqhwiRnZFcJHxB3HtH3JK8Xt6mT6H3jQLs50nh/tHbcblQfxI9HTTSrGS1B2\nA3L2uG7aCRDaCCCWSjrq83ddZ7h2mktK7/CBYHEvsKITkJyuigTswbWlvYXA\ni3KIFlZFiTJgtpHAcxQ4alInD2BTbtRaakyrn8PeTQDiC1QtdaiUHv63xfW4\neIjK5amCjaD76t8F0Qso5ImnKLN7LgDImfwypcHL6TWi/4j8lU/wjwLSnnv3\nsVYG\r\n=A7ga\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.11": {"name": "@npmcli/arborist", "version": "0.0.11", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.1.2", "treeverse": "^1.0.1", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.3.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "0.0.0-pre.1", "read-package-json-fast": "^1.1.0", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "c828ea4e559b3a5c664c305e55250cfe5ff7b34b", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.11.tgz", "fileCount": 37, "integrity": "sha512-7F8TnKzNNMMZoPLkJ0QizOCp6BeBZMhPy8qzV6wOvIewQlyKhsc/Lte4dxVlHjGmh2supcjwqg2PN8hQb/I/8w==", "signatures": [{"sig": "MEQCICby83hn0UdXiJiy6o0kVSFdr4afpSFhz3J5l1Fm1/B1AiA7Pdn0Xi4LQT2btjPl2k9qW/A5hY/WUnl/8NpKWa4yWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 256261, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfJyLZCRA9TVsSAnZWagAAy9UQAJpSuVWFlcM+5y/jKwoR\nIuCifyfFl00AD3YHAQwSLBOfIGzQJW7ErjNsNnocuptD1Ygh/IOj00kkI6g2\nyVi7eQ8UtbW/5WOEHmRbGSgNtd/baci0nnJIZqSjB7TMUfUdYpg9wjr77aO/\nvdWd4lHP1OQ6pvef7LWWM7XwnxQ3GusTtY8Fiob9NpasVcjUkI4u0/MuMivO\nEUzUFnGsE+iZ3+YCc+AOmjORLODzoZ5P77cpodBU3fnAiw3LzlUHoN/zsWq9\n/Wt3f8jXK0xKlAl8+xybH/Ee+0P2y2ue0TxUf/1BdEOAEdFixqt4Uj5TQn/Y\nrbRDgYhM4KI4MHO2UZ6mrtJRSCsAyxyGetC9PTNlfePx/7ycpXOEVSqxagYq\nSHRiV6rA2vQ2IMziJRSm1BowVhTrgpLFmIlB5KDjDWYNwOrMDLTT09F+uV5y\nBnQDdcmF0hetUdHrUWGSb65b7OzRv4pPRVLBfK5WTOi0KGA9aPByLxSRhgrp\nzbVPTgGgsbqFtefTTvtOAkNioIk2bcxhcIL/hrrBRuHAI7c+mNQcmtU9yniR\nuNwyzVPzhabWZssl2DmXxswmTLpSjR4rJh8Ny16131F1HuQyZPPtTIaoNobY\nEv7PZFf727w3lGKO7m0i6VMHm3yx7c0Qy/LgBu+4JS+Z5rQh8MiI13qWjehy\nfwdL\r\n=Yyox\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.12": {"name": "@npmcli/arborist", "version": "0.0.12", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.1.2", "treeverse": "^1.0.1", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.3.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "0.0.0-pre.1", "read-package-json-fast": "^1.1.0", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "4884cd27f0490ade6bc6752ae6d9ad99f7c80ed7", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.12.tgz", "fileCount": 37, "integrity": "sha512-RGsgb7h1NKWwfzj5j6CVysLsazCk/oswyMEc/GkHFffNnkM8SS3KHpoi1o4gsyNywbj5cZ0ujiO+a6b2dS7Htw==", "signatures": [{"sig": "MEYCIQDvqStCOw1vILna2ZMophTGneQjzBQSQsjQP+6icZsOwAIhAKf5OuopLQqgpE2pBJogaZSt28jcpr5eemyRewMVjfEH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 258147, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfKLLACRA9TVsSAnZWagAAhKYP/1sdWZj2fYuRQPUobY5a\n3ZllVekUSebvVZV3JJUZ06b+0Tlv8So5vKfQ2SSfbuf5/yWj30zublMQ8LG7\nO7eKxGf9hk5lB2EWzY2Oqtzc98nZ6UqrRiSvNXfoLfGXNISWJs7qJgapnCgA\n7bxxT2+LgzI1pjstgLdjqlDvLIUzCTVKDcaIFrMeX7X2eGoatknBYbm0n8Kg\n+t5ZG8JyzeepQr9A7UddyykMhL5IRX/KErCBCFR2vatQi0w9wIpPcfwO6q3u\nmJ3xy+ByW+0ssMDP7xPtgpdzhrnQmJ+Ukojflin6Kp2IxrQFhSa5y3v3pGa7\nwo4yeDxZKNwQ1doSi8Q9vgi60xCqd/froaJQmL2zp+BAvwPqFyUIg6rNFCHb\nCzF2SplrQMSbIkJbf25AvMl4cfkGOgpOzEH3UFs5ERLTONLPqbuwurtXgX2X\n+ATlQ0Z/kd5A9uEEoYoEyWqPNf/7noh/1a4ppV7NgfKbAOWbGVMynQCQZCuo\nkpWaf/xVml70o5Djq9GcJtBZN1Spo9zzPNcuRJUxW8lJwpo4zDt+5sRSjaLO\np0Oby4cYbw+S+SQZmf67dWYZNBeoVqlOvt3e4gcqBZ255NMRtZLPZLyr4fwl\ncF0l+np0SixIM667Q1pwMpHlSstcz0Mi4J7LA1hLDYXW+c9yC9MmRPHERPHT\nktV7\r\n=O/g/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.13": {"name": "@npmcli/arborist", "version": "0.0.13", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.1.2", "treeverse": "^1.0.1", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.3.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "0.0.0-pre.1", "read-package-json-fast": "^1.1.0", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "a545836ce5699a43fd59c87b0c92c628a43aa1d9", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.13.tgz", "fileCount": 37, "integrity": "sha512-cSZbtqTKZOw4khk+6Xz<PERSON><PERSON><PERSON>ov+l02IRbcZCB0l50JuNkv8R3+4E2ctLnKMHpYsCRYNSP1cvIUOU+dhMrLuW7zg==", "signatures": [{"sig": "MEUCIQDwxE96RTMKl3Vfp2qOZHNi2iH/rxXkdzs6knyXMA8NawIgE5mnRuBJv9jj/QJlovqNfEpNXwt9u1WaT5AsZItbmWM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 258512, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfKaW3CRA9TVsSAnZWagAA8akP/2QagZRuXV7M4HlTtvma\nRK5dm7xbqpfl8Eyk28hwBtRWP0DW8QtS81sIvtxO+3d6LaJzApIM9hN6syss\np4+YYvxe+sMypYpqsgLPnvb1x5ToM5nzT1ky2AJZ6qQ8cOox0sY+EnIQH144\njKZYbJmvo2iiopaBS0ZolvGkzJKX0hrGxnpR+TC6jDSn9ogibT1tyYvQdVdL\noE/JixsWr5xxvf9jOgl3VjTBVPRGsXg7eqU24bZQ1jzXGpvJAYtF+o+zVMZm\nWuLYg9dLjGDIG41r1sNZ/UFQSvq6E3zZvb7hqrBwlvEZLe7uzN15NxaAaRa/\na19zwJXC5To+ec1s1GnHjIxjhste4snuC3duF79s7JznwG13SvdwToxFAvn2\nT7oCZNuwgksz4FNu1eZ735NcmGcPtJ6MM93lkcNh2bubeKPk2MbSpFYIJpH2\ni9TcJOaK5J6UrWu2QBfNfpwCKxqLks9UI0hymL7o0O8ullF9IOZHupBDOkvI\nXhn4p/F1gFdV0+dIrPyTANogRgQp2JocAObi3V+tO/k6k5Sl3Xsv21+G0QH1\nPRo6wY1+9w0OR5dMIyWVRoLruStlEnT1JUkpk5XiSOxFf4aaO6Wb/+PgIjk+\nQnAMokrUgQNU/w+N6qGxVqlGevqwgCiUU1HAoiT0NLiypdN5AyG4NGKadNyE\nukCX\r\n=6QEz\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.14": {"name": "@npmcli/arborist", "version": "0.0.14", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.1.2", "treeverse": "^1.0.1", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.3.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "0.0.0-pre.1", "read-package-json-fast": "^1.1.0", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "e646c74f454b2b9beef9c23612969c6a56b24309", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.14.tgz", "fileCount": 37, "integrity": "sha512-UWLPw8ahdYNJGrstg9yYOIsMsDhjUSRckys9ofElNASlOgjBKuX2gvbebyCuUKxje9uxwdq+OTp9DJ8iRR6pgg==", "signatures": [{"sig": "MEUCIQCC37K9KMgPmQVXUYfcvoY6meLL/GJKUg51LVq3dlqP2QIgJfp0NWxcOIQMdUOvhswG79EKkMmYp0Qwm9gg8tJr7Ps=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 258757, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfKvomCRA9TVsSAnZWagAAMvQP/1ZyYU7xLXQ8t8rMjfuV\ny8XjtVc8l95D+AuCr7A6FH636wbBEK5/G08Opqa7YiXVRelSjeVPfG3Bc25D\nPbb/lgRKRyMoBF8MBDXKpBDXM53OHFI2VbJ5dKX4l7hT4sQZKEXPKwAVub8M\n02vH3opbF8+6wvzraSbHfE6tATMwsjLfbvTlrEW2imJ+gCJj1rb4UGhovqEL\nLbI9mqIfcAkw2gl8gf54C1PzSMzgZwJDaCfASlwfhH+x53QelsGtgrlhL6vT\nA310qmunzz1YkLRu5WdCsGERL1qNQxRI78fBolIo8+pjgLE/YZXyGqp1z2bC\nE75OzyyDy5oDG4qwZ/MZDGL/77NqahC9p2arwVbcel23Pnh362SNhnoRWaHy\n4JZunsqXBrXu/WObCJgvwCQon4Gemdor+DK5TRblkzW1Z2XcFnffGJfESpol\n8VsuecrcwyKJ0yV2RsZJZtoIB4iRhEKmcu8XzmiOzdglPY2A5F7/An9CKniZ\nh1gaw+iDkPuXrQv08Ux8k5LzNwNz6/fTZvNrfTg9cflIxR5IzZOxnDsR/VCH\nq9doZwxYL5Nda/YqSpQ+VShojuv1vSqFjExrTfCoZXxsbuOdv74uDanS71ct\nL8rzvK6X1ZGP/owOHUKE7YVeQkJyC+xRg6oIK0n4Hu+kg41hmB9YH+OvFwmk\nVKx3\r\n=m197\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.15": {"name": "@npmcli/arborist", "version": "0.0.15", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.1.2", "treeverse": "^1.0.1", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.3.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "0.0.0-pre.1", "read-package-json-fast": "^1.1.0", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "154f37878a3ac22364c1ca3134a5d899c85888d2", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.15.tgz", "fileCount": 37, "integrity": "sha512-z/vBh3WXNQTz2DBcNYlA9aA5Jbh7Q3C6WQ0iBJS1FjshrtvlkTp3vaRQS/koSRVl1P2Q6f5sCAJLraU44Q5CEA==", "signatures": [{"sig": "MEYCIQCGsssyGcgjXc5INAGdSja3o2WH7oa8BPldV1wGskiQDwIhAKJF1FacubSDgAdWXJwp2Zj5oF9vYpMMys0uciZWozyP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 258907, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfMJzgCRA9TVsSAnZWagAAuPMP/Rxdehmn7X3tr+wFb+9l\nvDbZfLC2xjC4WfX0Akhbya+JDL+pJdtIY4wQYJRELvzyE8hMvu+hVJAM1LMc\nNkKVx30IidfuVD55uNtwcVH+bdTfdkMfxeb+deHYgYruz2zYp2+OKxyCJpDX\n6Y611iIvC9f/oIQhbVWPOlm9cJ/kkVXXtE13eIPnRbZN3kKzidUDy7f1q96A\nQewYHzIZcAvzLy9kgVxOY/12n11yYGTQCm0mqnRvT+ItJpJzXF7qHFZ7Y5iV\ny/6UvG8yyiC21S44Y3/1G6kTgJxPVTHA3els6oEgLK1EXQJPg9ZeOfBYe8Co\nwSlAG6mlXKETxHUck89rAO//6uwjsYG8nppBBtX1prpovWPiSPZxDJ4SV2Dx\nKF4L9CLOWWTuQ79PuxebFPHU7iL1+/MbEVvTpkexqTbhYABauy4RpIS7nfYj\nL6ymGlwwbrCGMyQVghysWdslZuhEZh3DUTw+pQrEm5b0rabW0mxXHTI+REkB\nD7hvjID5j1vyP+RLgM18qEAzwLbYDrnGdwenExmgmqmG8mF1bWTAvAlc2DRb\nltgYxPbgQTLz9H08mO63DEyyEqqN//Ziv5Zy/SWcdJDU33tCcx79wZpGoGYX\ntkKIlHhgoKcW/rkTk8Usx5qT+Y63hrlqQrfpjz4/gKYXigfVUxGsE0cjk4I3\ngmSH\r\n=KKex\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.16": {"name": "@npmcli/arborist", "version": "0.0.16", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.1.2", "treeverse": "^1.0.1", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.3.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "0.0.0-pre.1", "read-package-json-fast": "^1.1.0", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "5f2d6c283bacd7887c6da9bef5b819642ddaa104", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.16.tgz", "fileCount": 37, "integrity": "sha512-Bou0VolPtTjQq63wIFYHoyub09HEz2u4aPdF1gUHJu7ZjOf+PcMmxdX9L/ULe3E11/JvRK6Tp6x/lnY92uG8LA==", "signatures": [{"sig": "MEUCIQCoeu+QDGOb6m6S/+S4qIPLV7xcAGNsiKbxZ9pbC2jhLAIgUHQth3Gu1GSQbjmAHtA4LEkGuXxjoDjS8j0BtHLs5lw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 260718, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfMb1bCRA9TVsSAnZWagAAmiwQAJdqYTYF70dRcpD3Gua+\nmtWafQht/4ms6jGDhRTteLeTDLPIfmkquv2wj5DvwRom6INeL3eEkOytOlEn\ntcscrKfUIBmZyApDdJFpoJSqDuE+AoNl0jN27yFss/97kGGRwy4tiZW3KdHv\nJhzc6UCiGTstP/K7OrMLZYvvTXVxacK80OM9kPuPobKuG/tIm7Z4BDpLJZQJ\nEHQn8LvB8aEiCjV78XJReH3UaohL04pC5LpLEHkt6bSXlvRk4AUBcFAojfft\nogS2I7kekv9G/9ntIjzst9/f8vk85O3IsyFSiEoBx/T37Gc1PAsAvdXNoqbw\ncJrOmGzHRRh41lasDbWBsbJvBhsSsrIAotBxytE924rSsh7XCrF0nItYKj0x\n+OZtFYQgPttcR/1fL+15xDFLRSB32qp3zwKjugvEyseZpdufQB4HPqDB+V3o\n0q4ZjAOyzKhnrOpKKWwJQhkDHolVn8HZ+3QeZvqaJ3hMxCr+kwZ5oyRqmWU+\npxhETuPEahjOE2dIRpPDjxldWz978O7f1CyQZrgI8uz21GoHW2lqf9b5AO0i\nrCXlrenlEkX/3E1RJ0UtOppg5KBeWjOIusHcmIsGr3wEojbm5snt4/k4FaPo\nLaju00hmQyythq1usGaafH3MGOa6WcIkRGEpp/rc7GXT6Pw5UMfNw1n+smF5\n2WdR\r\n=rjJx\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.17": {"name": "@npmcli/arborist", "version": "0.0.17", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.1.2", "treeverse": "^1.0.1", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.3.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "0.0.0-pre.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "fa90f6a4b07fe2016b7ddf1b6f2233d5a0646a79", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.17.tgz", "fileCount": 37, "integrity": "sha512-vudmTpFbhlmOfRMBx0aBJtjY48Ny2tTJaf0/b1RYBvITpVHAA3+lhX1kNVawv5KXjoPgE3mf5HKNhTQrKqLA/w==", "signatures": [{"sig": "MEUCIAjkCscI3mnCbTVApID98vxSqRxkzyAnV72NoxSsw7IxAiEAtQWibGc44rsiX5tvRvQpvAa4i1VZR2H0T43x+06Es0k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 263838, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfPvqmCRA9TVsSAnZWagAAt6IQAJA+s83m5yMdQ0VjUlv/\n4iGmGPGZ0GOFL0naNv4nNdEQSqicThxJiww1QNvbLbzCdCdyzXqZwWpM0OOV\n6K9PrnqNrTitQH2ID3XvjPEJNurSQ9E3Sn0aJPrn/I9HOAmrMABcMgMpyx1P\nFaq/YWqD2/7Y5I+8PxgUH4bMCXkz+CLRexex10g7A4wHFe8tLRUu7H0a+Kv1\n9OVv7GjzsA0KAnAFIgb7O3AE7Zx1df0zGat8tVKYhFkOCP1EUpRvKo4/nh17\nWUbOc4RS8NL1MfiflAFFk+Rysf6VjbXZei1Of6mc4tFtWYiWhyNtpLly6cXZ\nJk7eXF1FaBZU/mDwS5gkxrmY4hyNLiCKsERCUhuUhZLM8zyd5b/fHux34jQK\nHdoDxYjOYNeN+ApGFIMshFHrjceV8D/6I8hvfnIty4LY132DqYZC8OWeMgZ5\n21yutUc9IjpgDSuUJ5ZPDqdzfeeG6i+wzJLZ/hYqWntDGbqI2oq0PvyaAw3e\nmFyqchy0f7z5wlVRf8ULAlAIHNi6F9SK3/0ISzd4oW3UcmW7+BpEgjGtjlEN\ntzbY2YgjZj+N7+nDMXaOW4rAq4suHfQYCDGM7IABtMA4/NwPEuD6ZcRZzzZ7\nIyXwqetkvVo173MYEOnI/q4mpL4jFJ5/+f7OPDeGuVbtvXQkFYKuOKtJeKBM\nxAHj\r\n=NA7l\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.18": {"name": "@npmcli/arborist", "version": "0.0.18", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.1.2", "treeverse": "^1.0.1", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.3.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "0.0.0-pre.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "576ca1f31adf831f216801102da6b0b858d74bbe", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.18.tgz", "fileCount": 37, "integrity": "sha512-pBsw8zemeZekUol1P89Z2qktriGbYnoZ+TfZAyIMtGY6yRadVzg8mYtxxSym/xHgfZ6QvEuwY85oScotajx/7A==", "signatures": [{"sig": "MEUCIFLd1ronrThpjBlAZfyL8r8KCDsglrcJjw/X0aQg76jMAiEAmnTPiygj2tIwk5rkSu63pX8sFYPscSG9TLWWZMdZYPE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 263833, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfTmVbCRA9TVsSAnZWagAAwy0P/RfBjLVXS4JqUyGwafDF\nvh3/UuOy7aKDmYWV7YRMY6oqzEOT16KeNi7fNlSluMgFkuQSRH69E+Ubjqgc\nbyxu5sgIZABmCCWU1AOS/hBGNkqwHkswXsS3ZT5D/bDcheh48wVHKDgF7O2R\ntiruGmTRt3VE1PNaqvuFrAyYpIY9bHD+XgQqAx24SJh6zKBsRS4ZYXWIVhor\nPXKE9DpOh0YqGC3o3nWhfeM6fIk/ibsW7JmzQC4bFMRqjCmHHLwM9wxeMYvq\nBN6I3Ad4sKvP/RVyGxh53DzOrTI7GEKlLBbSsIjIPfbGcHC5ByYk11QFzio0\nyDcIsyP1K6MWT34i3uQsL5DoubuH/kCf/WFPzQ3wXpfjeWUFOSt9qCcrneuD\nH7+d84mxbubzzi7rkNpGUm/ccCpkL2TwfgBtVIHwXyc+PSum5Qo1JxxhaShH\nBSw3TcZw7eOdV7lqpxtsjIFxNEP4jGJ80QycSiZJbni87YBDOdB/OjIF9uMQ\nh4YTmJXu35lukVWZ/Deov/f5g9qsitl+zK1K47sGFpTZSzwqB7knEl+BtRal\n1u91n5myUP04IwNhzvgXq25YsibLvBbepClNNAQDACnb8Tj3N+S2H+GiLKCH\nAhqVaVXss4Fkk07OzyYh0KEvmKfMGF5ifh5sZed0tlPHzLGqFQQewsJhH7UK\nLmP/\r\n=AJJM\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.19": {"name": "@npmcli/arborist", "version": "0.0.19", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.1.2", "treeverse": "^1.0.1", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.3.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "0.0.0-pre.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "76a5db66bcc6ceb05fe59e59e8733b3e68cd29be", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.19.tgz", "fileCount": 37, "integrity": "sha512-Y8JY1FnE3HoCZDBTXN1TIqMoGnywV9l0L21cBZQPx+MEAJoF87gfhg56RAwEICUD8wca6X1v4/A2fqPPMGzscw==", "signatures": [{"sig": "MEQCIBZlRECPddFgnBpJB4sCN0YRGMjZ4cycXa/1OAgzDONwAiBMAa0VKfhHCJJyuUSeYfgPIXePxBeWyrjvBZ1kgp3Mgg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 263834, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfTpHvCRA9TVsSAnZWagAAHaoP+QA98W0K5Lc+2ACORwmU\niGr/V4XWdpeku4668Y2HBfCEq/w/K1IzkJKZ375erFwy+jA4x7XpFPf9Xg5s\n9oFvc/hAJqy0H4L9qa2rALyvB5Ine/49zQFvFpv9NASUU/ob0IVL2qfVykj2\nkRdjQpLc4e64K18WYE9tdItvNTMQ0l/hdNcSX75cS0f/TAZl+kqpAcn53Swb\nhIg1woxqdvmXlY/+p5SIRJFdiOnfn56mT4QM6DWwNe+dmNyo5p6nZ/fWoQ7f\nO+fk3H+4NkllS/n+hLhoj9vdmpTnmGo0vi2JL8fSqKIIDmKgwaXKVE2+i1Us\n7Ac2psmqz35r++TA8m8qEEm6QCCXy/155GscPh5/62fBSO9pSgI1Vi2OrjCg\nfL+q83FA+Wq4EU91BR/NRYsLp8am16H0uacUHfF/wGGH7R+8ZUreDpX/1Y1N\ngTWLcRmzQlbBYGPs/7jpP9AWkgTVswFZc5w3/3StLgSnNbXt9KBzuUca4NJW\nPD5FGmvntC3+CwHxKoSvVCsili1ZZakESOQ0sabyTbon0vXWz6UtQ1RriEEu\ntedrSl2o8wb0IyiRn9WG4coD10ynyUgQ5oPRXOFksNQsaLzzQDne+l35+L1o\nBDdElGUue9kRu+VmVj1owgIfE8DBRURoThykxJGSIW0Eyf9+Z0ZNfU7To89X\n/Tn3\r\n=7vnX\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.20": {"name": "@npmcli/arborist", "version": "0.0.20", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.1.2", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.3.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "0.0.0-pre.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "11f31abb89d15431fe5c68cadfb55f48ce219de1", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.20.tgz", "fileCount": 38, "integrity": "sha512-MklbWx9KFkMgi6gdzyzNMyWOX+f+OEwebyRkOSxTyYFfgLtqU+SlmrlhQ0mgF4jODZ70YkR5lfAvF0T5V57XVw==", "signatures": [{"sig": "MEQCIFIPwB8urzB1FOcPW9l22N4ofPeIR7NF6/Zj6s+634IeAiBmFSJDAmvIP517W6Er3kJ3jA2BYl+6eYyaZN9OIfFfmA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274397, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfUYAbCRA9TVsSAnZWagAAZkkP+wTT9tDK1jEr5nbGaqhF\nqzXqOSSjZhjzZlotyyWh1Rzfn5MGXY0UR0Caez+iojD9lbXDA8/peNNITgwc\nJulQA+Wq0cKMMn7VRrbrzR7ukwuV2w5d7oDIXiHbMuliBLkCJ4Mvmdfr7CJp\nsv3DBp2aLLXDkSuYFdswYKbURspzRulbdHYRiD/0iQXe2BXWfh2d+wV7EH6h\nxg+AicCpmxHlEFo1Hv7ze/062BN2aDujDcKLqrk658ANx8Yt4x3vsQ7rcXlC\nDQG04dL8D17UpffDZCVPwpltMO6EbXoTDV8D0ePbn/q4ii1gKkbzYN6BdaZO\nKyDiMCvbjeBhfxA1dfX70Oj29Vgwuv9trtz1+K+edlFnDfq/CVhaGrX5GxbS\nSzw2us9FNG+8F1kSt+tsLUvT6j840sznfcIliaYsWmsX9GiLv8gand0dxqQq\n1MwSiULw5qV2L02zFH/cEsSz2eJOiJ6H+2OfcDYOWafaMSGaf3pcaPsHFDBR\n4ia/XNUg2MVXIvsjGv/asamiz5Zcpe4JED8/6zbTJs8qnhNkWyHWnT+/nPPV\nJJ7BFBHdZ5u5hBa1dfPBqwx51yXREiE37pL7x54cMdjjCmz0EP2L3c9pE80d\nujXPlHjcnHvwt5XqaOGwvjBOBE6Whdz3JY6NU9Frr/MZ1qv4oodY12Ze3c4e\nUlNL\r\n=bSfl\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.21": {"name": "@npmcli/arborist", "version": "0.0.21", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.1.2", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.3.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "0.0.0-pre.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "5f1a99791d16efe39c6e9df80f8a88f2a2496b26", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.21.tgz", "fileCount": 38, "integrity": "sha512-5yZ9xL4yDqqmjaJmql5HQ99lA/zMPZJL9DrmABiHAp22U5hR08e0E4DYBRlnLy/mdjjiopJ+aAm0hIgcD56vdg==", "signatures": [{"sig": "MEUCIQDG59grkSlV2c5fj47Om1cN7c4tekBDsQPaOHmNqxB+XwIgKHcowBiUPFnMi/KLjMpgNLykX/ODmst2y7MANdQd2+c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275180, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfUof+CRA9TVsSAnZWagAAfZoP/1gyPGNg26wHjCR5F2RS\nloBsMu4Ilz7dZh/XvDBiHCTXcuxR/sR1E0yTxNERhCt2fkxqOuVYQ9Qqzxql\nCSoMDRo/ZwT+DbeuE+vtYuI0yNwD4+nqgUMBN05wnbPeJ30ohk6sh3yOHNFM\nhv+V9SuFWBF8+n3GYZG9dKAkXmB2pyPIkzHOu+WkhOQ9xWi7Owfb0IUR4BHm\nz/NhNRPo3kIQPTvANgXUghlnNphXhi5A0AbURkUU9/MWdy+XBn3JspG5kCvY\n2CFqeLMqlZZB/d1UQxgRW9zJYJMBgt/QNJkMGRxKNHjdZ/JtMAiF1irdlfeA\nL2bBiaM0wH6XK0EYZh97HnsZ6Y1soONSXdH3t5zioUOP9CuwfkvYz3ntpWFk\nAQsyee3bTeJ9kSH47gLDpOurVPs/vkHC5SAfh8bzt3FyBUnvQohUTzYNdc/U\n7Uas0r+Qjge3GBsxFRO6+Kn7hemeoGzkVJyQlT+ZUrciSHAUUcee0NPLWaN/\nM8jPBNfDIu6G/FNWWqW2vzlPsEAR80Clt/eoWbmENmxSJ6toNYmmiASuhC4r\n+3zSQbXVPTVC58EAOU4ioRfqu7ney97OkTesbFdMx8leDmG1CBVwHeH6EIMS\nNWeuHM09/unl8KqlNEaKr7CSyxZ/XqiDsaejvBnrPF8a4P3dH0EkIU2tkxpr\neSpO\r\n=qM4d\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.22": {"name": "@npmcli/arborist", "version": "0.0.22", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.1.2", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.3.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "0.0.0-pre.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "dabd49300da1fca1e59f65cd6febad2054ab4985", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.22.tgz", "fileCount": 38, "integrity": "sha512-LvdATSS03d7VqFY7tMrpfFyscFndRxpF0xIpYh8/A/N3b6V6SLDS0OtWguHFrJg2hHbfzBxDXHF9Ak69Czl0YA==", "signatures": [{"sig": "MEUCIDE7DkGIpO9GW3nS0kIEiKBjttfdqnUUO6J+wgwft8AKAiEAo5cphIdubvqV28PwnDRlFllA4GLrSLj9hnRVNXKosso=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275294, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfWpGVCRA9TVsSAnZWagAAg/8P/iyA9y/nWraw0+ilhLJm\nFlWHzfP7u7Jcts+rteJTNUWe05Lh4WNlDYoE4Rngwyx4nRwZrutvfco5RZuU\nCZWOe2Z2tmGK0W+6dsEcYJOH+w/vc9HFgho/3vAH9vVrtMhH/DBtrq0FGY1J\nSh1cIslJ0R7uVnCisolNbIyhEq1u/3h9oAQiwGVtK2p5pnC4k0CetK00d0rH\nqFsKDTPFQRv3RpZ2jzSsycXequ+VfeCkIq7Y+CZ50Z99gvNafCncWGLQHGOH\n6pWBpbSNKob1YqbCL2rKdQ18MjOeHNaEzE3tadteIV0shkR/nwAwhwfKPS/G\nGq6twRhOeFoPlStLubuhs2vQqSo5ILCVzXkGmQ0qvnPpO84y4pB/7ihYA9Tx\nmmIjjkV1NgiYUwwQ0atGfLVa2478keSaY9lEsdtoZU2btLR34XQHW5NFukHF\n27C/9IYx2lF1LHMeU3i7OTZWliKyz3u+D3mX+e9Gg5DPpHTSDZZGm3uJbDE6\nJKLOZYLKjbzDpARbRYcWTfxTcTbM9xgIWPB1FHvpwDYkO3STm6fw4i4P7/uz\nAYiuwfulIqY8zLSruSVzMVAksAGbeOovizwHjSkbok5ln1YXyF7MDWjxrPgW\naD7O7MFFVLUfRZJKUwGRx5T5S7faN0r/8vJ4A7ALpJdPYJ9hAytdsXRUHzUS\nF1zD\r\n=31Ip\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.23": {"name": "@npmcli/arborist", "version": "0.0.23", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.1.2", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.3.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "0.0.0-pre.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "9d1bc99e79f465127e8e87c6c1e97126e8f6117c", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.23.tgz", "fileCount": 38, "integrity": "sha512-GP3QFAr5TWYzoG7JkfClc4XCwFGUk+R/pJpErpHzyiwf8TI9Ib1Psxjmdb+lVGYaX6QlO0iJeuvL5tvmOI4LlQ==", "signatures": [{"sig": "MEUCIE2x5FHlN8O+QUSxEif2gkD34B2FKxL9A27E4s7WCOmWAiEAhpLfHg9WGCtq6pgkPbDhr64miKQaQ4jNCB2qb+5rgYU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275607, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfWpztCRA9TVsSAnZWagAAyi0P/0E5LOaojS4fJNFS4nSp\n27RMgiJ4c0En/KIAjoo+KwlrzCz0C18uG2MhO22Z8M9+PbyWbASgtYaWSU5+\nsDn+zAMnd90JptzcZLk/HRPPCa/RR93kiNcBCaOm+1ldC9utTlqIHQNi0aEw\nneYMgZr65A28syySXjxwu5aSOIbzpgN2sVwlPfawViM1HnDzEWLAuV7PzoEH\nzRysxj7GAudQnAWvZPdD3S30NosfxUBEkZO2Gpo9dC3Ko63GZpjkatc5O7R8\nq102ZCa0vsCyzPM2NA5C5NWwVsOcywXz/BFvpYRzZiKaWqfwY6ilQsEivz9A\nNUEYjNbGNagFy91gGsivKiDdFjaVuPH/E4p7HttCl2AUIDf9xgjRKKBRm5Ho\nVUmbZLBpp7gc0hGi7fzcDeka1f4+x+Gb4KMK4O7KoVZR/gCsDxq1GHG7GGeB\na3ndNdBx+V2UlxOWKK3vMMkQQt6xTqpr4RPL3JBR2v4JqK8XKbHAxVe6tohN\nXeCYsiyyLbejtHZOJYrhQ9KSlPB2r/iKiWn0X6cTznYy518vtKw4qHOAusFk\nRLFexdS+nmsHCbFEm0LdJ1tlDMcaHDv366vlS+SBj0BWAmGDKrZJ8MnumcxR\nvmz/azgyRW0ptNAmA4RL92m9mmVIXMZ8fPn57pnV7FwCYe3PGLJ7pPd+fPhU\nUJ3H\r\n=NaKq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.24": {"name": "@npmcli/arborist", "version": "0.0.24", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.1.2", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.3.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "0.0.0-pre.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "f03da575a8aca20fe56d1c3d9545cb717d0d92af", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.24.tgz", "fileCount": 38, "integrity": "sha512-8/g7GjKKaMwC31xlDyiKwlwrsYFQmqUFfYDEyD49+7WxocKtI/TLwP2daoIm3lDq7Rgu/OPIJiD2+Aex18sMoA==", "signatures": [{"sig": "MEYCIQDeOA8wC1Z+ahr+fr5aR7xiyOjnIffbAQX9fu7cUR+9+wIhAL77CDbETbumvfGg98O5I5ojtIiVHQoJvmFqAKNn9NaX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 277146, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfYWEGCRA9TVsSAnZWagAAX74P/At4WK3HbnqraKkOkqer\nKQa0jfWyf+kVlqfGJk8JW/oOESlqCBRnyTZmtehikyHef+L7xtjfrdCHbTPJ\nOfD+yAFBE4SraFhSzg0FF5jwKW/jY14vSeojC/Flk0veWMGKT8c32jXSl3lv\npp3+Mn3CS6rCr3MknWYYIXPrZUcE5BoqDlEjC7PLAee3kaF7LjkHBws5DADW\nmjeZTehmXtPdT6wP3fvPb9S+L4JS1GsPEWtSBoNf29IXCxa9dxfBZRty/QOL\nqCxTnn/donqUN3A1CTHLF3VFWiaDK2q4d4a0yzZQenx4PuPmhXiULnFnsZi6\nQ/4l5JPMYg2RLG+hWRy3YlzzwhhfcMVhcvJbEDquBvCqFabizG7WjBdfCIJj\nG1gWy+ytpytZxJiW6apTpBormGMFgC4y88V466ydbSiuiXlipjmB29EVZ/mW\n0uop+G0cPkKkXHk3stU6TbiNhLrlqyBmgkgmyys18prU5QI9v6lP/p4sUlpC\n49otw8HQ4X/MX5ZBIGxiSrvDXI+tHRTyhNBez2FkmwlCjtzBC86Zjr2Y3ZKD\nUPYOIbfTUKKSbnmdQZPyaBRXw7kfuaL3/KsMyJ8d9VLFkZu4kdLZXP8ipVmT\nNy8Ld9Rv8V3DCEpalwsDxNxlmg4SvaGaSDxE1h8FMJpgAF+rWFEhR1R/wgAa\ntygm\r\n=yzOt\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.25": {"name": "@npmcli/arborist", "version": "0.0.25", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.1.2", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.3.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "0.0.0-pre.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.0.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "8ca90ede0983a291a2ae3d93e644490aa8679fe3", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.25.tgz", "fileCount": 38, "integrity": "sha512-16IohKyuzjFvauOG8bbare78gK4W3gy6BTCT42QrJ/kUE0Rrk0hotshOaaBsbnsfy4VkRTy41t5hexAdYhLk5Q==", "signatures": [{"sig": "MEUCIGgQrPvZH72cJS6e8xSbmvq2PNIsH/6OmLea3F9IUqd4AiEA1XiLBWFBF9e/8VXNFcSag11cS5mzjGjt2AX3F4/+7uI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 272795, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfYiPSCRA9TVsSAnZWagAAji4P/0gd7pBHXCcqGg19SNWR\nhREfNNWjf7b2SMtHQg3e3YLm0zedELGNINMnDAJVYf0Cf2kkcAvJiIQrG3WG\nnX/Qa0qij6nBCP5xwVNQqbMX12ix5EUvCjZeWeYDB9AEPcepTa1fuaa/okpU\nkaQZdsORTqXmMGX1Y4u+8I3NxrlTU8MQArZr3xmmwXyEs9gKuyoj5s7DFkNY\n+2lzDQcQYpSRCekAufZhs7jQDzI7LSiBA4T4/3+fxF3oJ/VL5WuU/KlL5cGo\nN0MnYwt+ypjN3W2rO6SNeIhCcmNbGsoJEsOfcp4mBlMxqDXtNQNTg2oqxbqE\nugz8JqwPDRUFRL1nxr4RnustP7djrJtuj5v5mdJQ3kEJmQS/9+JAA6D5ARl6\nV2CNeEE0V4f/pRJTPbRmgtR7hf6rRrrF2cEKm13XXk8Jn5Y5gyktAPlH8PtN\n3au5wnUalfJyA6yl7veigwyWTvrGSomRW+s22oPH8X7Po12T3+R8nq4vA8Zf\nTj07E8zy7KlUfypoD5Ux44ypjoUkbzeZIp/bBVxOzeNOXqOaAf4AILY07C6z\n7jcXALXEBWfa1rg4Df3gJugH2bdutudsx9Foty69eJXBjpiTpHoNGy4E3zC8\nFLf0eMlOC3krgmrsTuL7GuZKtdHb3rSfIh5uA8vrm3ETg3/Wk2S3l6RAqEII\n07xh\r\n=BANT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.26": {"name": "@npmcli/arborist", "version": "0.0.26", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.1.4", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.3.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "0.0.0-pre.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.0.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "3b4d4dbbff203cba824fc8295843fd545ac394c2", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.26.tgz", "fileCount": 38, "integrity": "sha512-qRXYOmxnp/qdhaZpkSKPFF+bShEmalNCFTDk+EogEBU+tNWQ/pkrOmiX8mx6oVOyQms53kytUOZSJtk9FH/r7g==", "signatures": [{"sig": "MEUCIAgzBuww1ZFO9pVyvgkx7FaQUVF8t5z88hFzJyVgJDJjAiEAyLQl2e0/pHMHIIzTJCz5JJvm/QMz6u8O7c48E3c3kYU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 273555, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfY+CnCRA9TVsSAnZWagAAfxkP/A1HztzWIRuEl9RByIDa\nwtYTNGqRZh27fiLOe4lRxoTbcJ9k6HtDcdecJyBUAJBSDwflB5YHTsObgAJW\nROfddrcq1DhncZ66jAYeqmj9zsTSUU0qRc/mdv3pgtyZjK0MYPB55U2rNudw\nSS9S3AV1JADJ1/IQa7IZkgm3oYF56t/WWsCtIPqBa1mRu+sVZ0MSxINVTN14\nyW5C9Sju6hreun3kbGMng6hC41cqI3WZ5XsryeHeP5MFEvEErd9jqoFEmt0G\nyfUIg1ZWEaY4/K0x3PcE9Vxozd0JvmRDtgIYBY0gEYrb2et9dgCSQqd5aWaO\n1QiYOpP5jlwj9XfFTNDbUkarwdJy52Tw5lrT7tvYjDxM8RCL/3CfX6JfgmNk\nSGnXpdqTCij+Qsr/2QNd0dIYchugmP+ZyiIIOYX/n3i1LS7xbq0Yg5AZuJLw\ndks5ByD551F4P4/HynaWCciHMCURXZyZ/4dhkgmjOGCxEyBDnyUeG6/0kjTL\nOmXlXPsAUmUQz9JNeRnDHGbMYpvVdQEcftYQivt9CBhkUQ4ocR4m5jTGLHNp\n08NuzfP3ZpaZlEKatNqPoT/sloFq5zCePT+0Dl1JdtoGxNV4T7dNm2nEihT4\n/o/YUjSdPHhcLRVyZpr/jEp06xO+SYpLplWyRkyoz1R9UaLpshjpW91f3qES\nUOnN\r\n=Nsmp\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.27": {"name": "@npmcli/arborist", "version": "0.0.27", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.1.4", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.3.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "0.0.0-pre.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.0.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "fb2150c4b3238c8504b8b1d65a6f9c38e34af837", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.27.tgz", "fileCount": 38, "integrity": "sha512-7+uXkEV7d+oY7A3gpQGNx6ufYoihC99R4I3BtWOeC1x3PzgGe1VMiKyirYLkiG2q2sYswa00NBmlG3R3LqVAAg==", "signatures": [{"sig": "MEUCIDiU+mVCtViMz/CENKhMt3z718/4O4iaw4dNfyktCSkoAiEA42yi8NOIfVdbafEVTd6M1J1x35goXEEOpAgiIP/DhCQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274468, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfaiM4CRA9TVsSAnZWagAA/dMP/iO9Y/lRRntXF/ce/ty7\n/7zQt8ulSqo1xkuWRThU9fbhWNKz5CGT1zMMd/Ks4fTqnwbi8gq6PjEw4pFS\nqpN9rZsw/EuO+2WULKjnvDzGtBQiXmZFGlyUJElEq5CQfpGPnnrka0hXvJ7N\nLkbzfzNHjFtruZv5/uVSeEeykYxtOVqSpTvvNXXVkVxhB2YlSmOc0vJ/SIyg\nLkA0zxaSZHbAowvSHbLCRVb2xHB/vPMJu6RKITF8CLhS8gigg/3ObFOyweqa\nX+sXkpxBz+7y/F/v4CNdWBRz6Iuv0EZslUITC4CQYN0gTE3D7cdj/t8BOOcV\nXrWuGYCzuT7KU1JK1Dg+M8nETt6vlxbfTeMUTL1jTkLx/eHeGO97D1vjTcXo\nMeFsXT2exbxOxsONwnMboGkQg47U90h0ZMIhVLok2rjRyKNOB062MGomlWbR\nBCfEN8sQeVkpQWb9XE9Nn6uhP8Qxn1nhD743OM84JqevEz1I2Vz6h2yI3vUe\nEyCbuhCCela0JbKlej3yj3JWHhV7MdT2rmPjvp/RoI3Vjhne2fQ13/gX/OvA\nBMjcYQAxRRBXzyvMcSLe7xg3ex8bkfNq7rJ7EmAw3tIxqoVKF1MFLf/yPkiY\nHwDTB5yJ96IyHZi0fSu9Vn/DWuQt3KknyNuH844s8FTpcavpi0KYALykcS4t\n43hF\r\n=zb3o\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.28": {"name": "@npmcli/arborist", "version": "0.0.28", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.1.4", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.3.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "0.0.0-pre.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.0.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "eslint": "^7.9.0", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "fd66f3bb2474d5ae268fd9582a9e2690164ef266", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.28.tgz", "fileCount": 38, "integrity": "sha512-E0yscLZ8yOPbbFnODRIRMsG+Lz7ffP/FWu4tq2mz0Omk3DQN+pllBbDHiOIjekrfJsLFBpmvGTltH71bKAMeCw==", "signatures": [{"sig": "MEUCIQDbLlSNfK2dhCChQhbN24nCZSLodPfg9cfEFFNiFsKWbQIgTVdfvqNu8cvYr/3fzF94pkZev7qpmDPaEKhBemGnigQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274193, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfc24GCRA9TVsSAnZWagAAUzgP/iTbJlvl33rq6jWlo6l3\nRyRW3QRlBFuX4reV+n7jl6BZtCgnw4imYzk4s/7rL2WIVDToUtyydZRoMYlZ\nQ6061sPvK5nOlkKK+UqT9cxeo2JHtNZDgZK7LV4nrV06XRpKdDwgvrigwF5s\nAsVCfPlnBsSItL65iDPxcRk69T4u2yO+fKp8vhcCEuKG7qtkaruoHZhu7F0U\n5DQqGsNoMAAV3Z0Ss8aW54cAqRlkx7KnWOKZ0gy7gdtf773S5BOFeDpvlHRi\nkqGii/Za2h1lmhe6+SIHZcs1oEzAsUTM2Qxo6GjOz2nYe8zjKhQC0xCsN2Nd\nRn3tOuHzJo7foadB4f4yyXBlevxrK8dZK2fctphV5aYk+PT1ba0mS39ePFQq\nLGqsESCJS2FFvM1C10CU4cFVm47CwW0KYRcmUvEzO8sOovruYWqeMFpaTkjB\npxic8zVDdvXi3tP345WF/tjK6eDkfE+YFpexO8JDrofnyL9inr/EAaWpfWGT\nl+M8HNX5n0g9X4LzDe5iVuC5XA+SrpLxofk9VgTnIttqVBUIKESIPJvWAwpq\nkzEC99jZBywy93ZLFxJjOYbCBc9Gt65qnTQB5q06/2Aq18oTNkpx+jf8Pxnx\nqpsdHmQCx+FYx1/gYWub7CWc0OWNE/oo4l8iwQRgUZ7PNjFhRdo0/F0B3rdW\nm2gQ\r\n=uOEJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.29": {"name": "@npmcli/arborist", "version": "0.0.29", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "@npmcli/node-gyp": "^1.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.3.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.0.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "eslint": "^7.9.0", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "701eb2266224bb8b9dbcaf171ee094749f8d27e3", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.29.tgz", "fileCount": 38, "integrity": "sha512-QbLzHlfJoExnrLpZYAdf14pYQfk+KpMkpQabjeusrJabrNsfHtfY+rr5HIcG4AAmb0VaiubcUdadqzpBVXgkcQ==", "signatures": [{"sig": "MEQCIEtZk7FkNtA5oB/0bmV1R3jJexkNeYruPhxpQUXSj6XKAiB2AZn4dqkpZNcT6jJtCeZePUbP+luZJdm8cUaBpqx39w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275649, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfdRodCRA9TVsSAnZWagAAjFkP/3+um5OSBXDiRl+8cq9S\n/o+TzKCG6/L6ghUHWZZWOv98azCbHgiZkM3xuLPPp/bMyuXK02CEnh7pzzy2\ngPQa2JqJXsD3t6xCL9muoO3a1yAwY4GF9cFZ9Oa/W0fiXQsg00Kx2jlAV2B+\nOg6IyGpB2EX37HVV3enXlk5EbhKjdN045R87np0G58G21ewf8bo+5DFIBOjD\n0n6R3EJIMJezrvrH7g4EeQ/Ny2okKfrygDPKO59XxNca3vLnBJSAplM7FT9H\n+Z3FV4pTTj4kCLUr0wHgl/OifWGMVp5Sl3AHZ0uDFAnS4lU31agwzqNPwfJB\nUohPi8pOqmvCGjoRGxnuUTqsicSDf7ByPtKUV/7Km7Kjucby8IiHhU3sqL6X\nhsvJXdaEe8UaG0CCOL6ej9HVJXmDfE5txOEIF1MgUgo9iydmQ5T/5dJkZpel\nZcxFUawZglYiQXHdQO+7QD/bye+qXxFsh1KpYW9J+C0okjWTugGfx9gTfRxu\nPitCvB3XGBRQ/giZPQdCF4amdrJn02ETCYMIZrHFWTxMUghvbXNPcsGkfVj3\nfxSx8TRf/arkKCxsVk7Jcho4lBKzKuuk+DnPI+DyhxEP/xs6DAahmUHx9TuO\n/QnVvyeMzOvsGCZRK/OHm+EUGEC/Oxz6RdwOMOE1AZlXjFXUMIPTMEFUJlki\nndN1\r\n=HxXj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.30": {"name": "@npmcli/arborist", "version": "0.0.30", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "@npmcli/node-gyp": "^1.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.7.0", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.0.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "eslint": "^7.9.0", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "f34f8fb5b12ad4a128420fc83b90b273bf641183", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.30.tgz", "fileCount": 38, "integrity": "sha512-L2xeTh6ca0RU/J57YZb/etKDsaRdUl+O7NrObIhgg5KaqF+IxOc6+QcDznnmN6swdZcxR0wX7o/VJqEyTv657w==", "signatures": [{"sig": "MEYCIQDn5shamWofAZ1lkjBlI/eUUcUwadEFVJVWSZL1LLJQHgIhAOBK3H9JF7+WynDMbn516RDtEKGoqnK4EkGwVEjzze/5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275649, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfdRt5CRA9TVsSAnZWagAAViIP/3tBg79dVa/fVhgrt/k6\npdbUux42OKUAd2XQhhJaQgJdx5DyvgcyPlxs5Uujt996o+GliuIjQRr68uYK\nKQUZJeYBCeTEePJFcDUd8qN5espQvcjhiQqBo3HtKHm41GU/wv1nTrgYLnYq\newQowmu/+V3eqAjrmn9b1Yhjn3vCYTCe3nnVdknizIbYo+jkTeYGch///Dn0\nOj7EfKVaqojJthNDZOmCVhJTx/5dRwCiUs86SLDiH3gfn+jMcD+XGklWsc2b\nm7PpB4iRAfm1Jr8mDK8dLvdPKV+xc2PcoulncAWWKVsy5hhr7LSlck8YRSyf\n+8/0v7JDSeKyKRdmtxsl3q1O8MpV6CYJts8ve8UPkM9ULZ4sPus4BkBuDuaD\nsUpmOOYKIsdMOLQsV9uVCfIHnHKWm1sNPmVs7eZvVqV9YZgwJlC+orx5PMVw\nCG3u3dXqJJmVb+5UiQ1AoCqXcaHak7nOs2RP+ZEXxsLgaRlAKYo8yCdYPCJ7\nfoRGwEkRHXKiSVEmsdZpJ+ixtMC47rdf/DtVVwr2EDUJNEY7yXysheHv1aCd\noJXNI2kwIcKiRgpzt3v4dPdE6EKd8ugUARl2qpRm/N3Lbq5T91Lulz0mFnoJ\nJL+nh8VUv638pQiZk7aCgfrvEDptPpV53PgDk1bkkCS+ZYQkozvQQdO83PQ+\nqUHa\r\n=XbwF\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.31": {"name": "@npmcli/arborist", "version": "0.0.31", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "@npmcli/node-gyp": "^1.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.7.0", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.0.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "eslint": "^7.9.0", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "708d1808ffe52b85ef041bb8aa33b7eee9f19f06", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.31.tgz", "fileCount": 38, "integrity": "sha512-BO5mCagGFv2szVhdAoZj2RcyjgOFxf1B+KpBTJvJYnYWwj/v4dDpzh5BixNrvDRcrCVZUq+kOOL/l53d5MHPjA==", "signatures": [{"sig": "MEUCIEoJzBNsd7rOliZU8RU/gxaZrbFT/RjHgaFhMKvD+30YAiEAm08b5GE/kmujpB+a5whqYMAYOi4Mqn0EAxvK0HvM/Lw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfd28RCRA9TVsSAnZWagAAWwQP/i/5OrW6siwSZB+b/tvU\nQDqhyMEV38wXH4kSnem+8ti9FuesE4BqTzT96bL8/XV32/It6Xef/ZCcir0D\nXPGUZXpwYy/rnVI6qSPRX6nTkOQVbqNCGd+DRIJn0vA8l5kq8/TZebhf+pcu\nJ6loyuum2Ui3jvpunGKA76XhoQO8iZ2fqXYn1OQH6aP1BQovM6G5qyPc8mPI\n6yZRrHxOiS5Q30OSZ4Ncc40sKDVdu3zxf13Q9CG00yBw9SB2dLdFZE8LPYAZ\nvdWMSjwgSqbd2S2P1R6DC6D/x2akJlzF0VkQ2MQ2G8lQ/FprPxgErCoCpueG\nCOXPIxJ5u7wbOaeGZuJvSyflvgKMnROMMS15Io7UvXdVeiz3pD+wKps6KXky\nkkWtI97DIkRtG5vzgzXbsdbQWk9YxnIEzcNCLVQRKN4GQ7fZDiU8eHA3n/Ki\noF71tKPNQOFfgGBSoRI0NStij4JiAN/7rN1EzcroiksITRStIrcpBJhRlKNc\ngLo2bZ8f2rVON7lx3aUEEl5exhns6MMVukIiH/EJ+rxxSreqU+KToRC8AAzf\n1EQRQjrNVdFvVTCKT3FcR19O2hHd3DWuyTGil7lF3O8IuOBf8iUNfTyZ8qYx\n2gb4qOvaGem4tZpp/C86WYqnBQnUfltReD6xuxG+95iUL6XR25G/Dbh6LLQ7\ng4qK\r\n=a8z1\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.32": {"name": "@npmcli/arborist", "version": "0.0.32", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "@npmcli/node-gyp": "^1.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.7.0", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.0.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "eslint": "^7.9.0", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "502006820674e8f75084197d81acbf1e83746761", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.32.tgz", "fileCount": 39, "integrity": "sha512-FZgrZVFaBqSPRsfr7DYAdrqFBT6ShxXpkMUBBIFZ6mm7Bgt3h5exM050s9ROD2DBOsxXJGOJj8BfdAuU0ZABXg==", "signatures": [{"sig": "MEYCIQDbZYwnIGCs0TkkfGty0s7n5y5i5i2YX1SPSEJKPaI2bAIhAPY+NsMikmzOBT2JnIvu3UNMq78hc0wsfzbdTOu9GpLP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275792, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJffLpPCRA9TVsSAnZWagAAYlQP/i5OdFkIlcB5jiie+REi\nbx3TduOlhkGuEaYiJM2OBMyhcfrH4p3hrlWeoFOFRWZQXNbxlmpJjrIqI+3R\nDoBWQYPxgrbsacFQQLURs/tCf1Av/NcqgBMhMyEkX9Wumcy1uJfOSWjqCKnU\noJaoqOIfc8GvZQ9ytR5J1mAGIDSXALn+MtlQMJOZxen/anAe8f35wJXBfR6q\nhWZkO8RP3E32ZWWZ5UuH0KEyH52DH+vtf/jGwliT/jZ9Ild34D9gJVlVePRv\nwtuAtToEIhMFNLdElcY//J/9kzIS0FBBUWmGjapkIjGertQflhRWFw8OYD2R\nCZL0fUEFgZ4ryRmr8zMY6b+1BnYCbb9f9YHNoK523vgSBzi5/3NVRysj3HZX\nI6J7imvTEVwoUGoLF5SKJc0xFqIcUjCiLUYLvS3agFELI8XMqvnUe2KdMVCF\nBG444rZNmsK0m8tpF3EkYoi3yvAw0tjuPPqCLJwWtCLPFWkp+rbYpfmGIyCC\nkalaFC3My4BnNryMW2ZhDes2I8eaY6uIwcYNksFGPSgUpMOLFcqrowYGZPWK\njJ7NfwxkcbZuEI78AFhziftWsSl46p5s9BltQKcszU2CxKxG9SWRSH0yjquz\nzIyp/U048Tgx17TOi+0RyqH/n1THq5IcPM3MClZ2f5AAdvTqQwUEEyLgsrE4\n3W82\r\n=zTDo\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.33": {"name": "@npmcli/arborist", "version": "0.0.33", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "@npmcli/node-gyp": "^1.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.7.0", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.0.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "eslint": "^7.9.0", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "c633f3073c8827d1d2f5f5e3ef2ba3c46b8ff248", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-0.0.33.tgz", "fileCount": 39, "integrity": "sha512-yDCdhNz8+N3pli1GsKw+7qcVL9q5C+V1Lk42JlTxw6cNpgcUwKlDj3e/nDkO+B1vJQRkLMSt9IQKWZtUjZHePQ==", "signatures": [{"sig": "MEYCIQDic34gUTZiMt8YyRSCGMeq+Cmb020GRnc3PHoQWJKfSwIhAIvV/pGFArc61EhxpZbz2l5wWdnWHQT+sTt7xr88Krgg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275882, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfgKA8CRA9TVsSAnZWagAA7GAQAIkL53/VvgeDbwFy4ASv\np/TmLFjMojZgaE14T8xVh9l+vwJsmKt9lsri9PdHCpuQzhvGyUe/fX60JPXG\nVFxS3BBkVO2zY8kVHBowYMFK1YHSwTnyHBSS+ApggekcFCnTe6Y1TCFwJwhu\nU1VH5noQKAiRNS2xlQ+spEOSM66gnrt4Dp/Pfzpv2tl/5194OGQ7DcDAh13o\nb8g6V6Grsl+aR8U3vkgAFcKTuK4Smd2Rkef+1IFU5Dly44N/COSqa3b/W/FQ\nvA/kbM8ZZiX8uzi+6bM7/rt/cEbhB2XfT6GEepfSPtYBH28cRC4nJ22GMNLq\nbtUBIxAhL2u5YRmH82LORI6dmVupjtwBYEx6HwTq0R6x5HPU3IR4ZYldguys\nyz542e4f1h+mQDJKvFDK4lnclqjioboTmj3gjAQoGZmE21bJL89OnxYBx6Ji\nx8c8pU5DGV3Gvrz6m7Xz3eLcCRpOlu33C8vXLQgEbqTw+KgeL4X5GgKKP2Hy\n54GucMg14ALw9PyBA3EAfRVzHSAGYwVIcqXI0zbHjrULq8DeQVBlvF8oniTb\nVjrzfnLTSdJhj+wnjOo3crb9m8SSr4qqCnMmwUOGGKBkVWnnSeWb+CgE6wYW\nQVOOvWQYspN4LGVptE45ntCqRe8p4W6bUcKp/MZY0QlKyi2jSC7fMY9LVy4M\nucAp\r\n=QbLk\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "@npmcli/arborist", "version": "1.0.0", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "@npmcli/node-gyp": "^1.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.7.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.0.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "eslint": "^7.9.0", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "52c729fe91fdf6d8e867f1a1e173c8a001f6918f", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-1.0.0.tgz", "fileCount": 40, "integrity": "sha512-SZgRAltGY/+hpiHP1a/DtJM8rND8An02yQGgp6wV2Cmp9lcQ/gWTT+dTqW1F3Hbw1oP6voHePdTzv4f77cj7uQ==", "signatures": [{"sig": "MEQCIFJhnkLumud8ma17H1d8GNNfh9KinZ1fSCMgvS59WsBrAiBB7V7dQPpxSawGx83jqtbKHsvsV2Q8hGNkEwJt2Hdf4A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 283177, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfhPkUCRA9TVsSAnZWagAAgbwQAJrhUQYfJBA0pSbxBnsC\nih6gKE0778X/Za5OrSO6odDgpG7SEjEqhjksPSmvaLjonnSeRRFEKjF4I8W+\nXWdSDlyhrPgoTmD7mpS5ZjZHzm+VAwcRd+UkbSvCGHmWnAM0WTfprWBDNP20\n7M5dMlNCQy6waTgUXRLASR5vxBYQqfafrSKD0QiIZa0RtCI4YHpRlltHoXY7\nexy52Ew2uBIPK+IFXM7Cfmq+cMaozsmCDg5guMIrW1lRvDpxk2vqRKR9QGdB\n2SUXzQMhC9u5E+yaVaURzPndVglsGHy7Qv2n3DryRy40GIw60h8ZjUNODBI9\n4g1QP4kY4v4VUxj51TNWJ4sHSNiKgJ086XiFd8smg0VnxtCHylJehvPX6iMG\n8zedrhN2vdbLygcVr1C/KAQnvLHk/lPH+z+jh9bN+96wnRqeUOZlIZOE7rzW\nnxS+wvbWI38igJu3+J3lCwabHfOg5O36WgHSpO3E6bsRJo7MMN7rBHH7N6ma\n2jwbm+lltiOjdQlHAel+u7VAPqmomNaCmfLkNbM/QDTc8a3CmqJ2kOOxnOAo\nYuTBJFfVLdnerY+rC9hIIowXirKrGNnqGwCcHt7eKvF+UbZ9eRaOmQ3RWb8J\nC4CF47T4K6gnrKKefX6DjV/EZbHnNpuv5YpRehe1vW/3s3K4kXkNKnQMxlZW\nH3nR\r\n=24Ao\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1": {"name": "@npmcli/arborist", "version": "1.0.1", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "@npmcli/node-gyp": "^1.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.7.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.0.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "eslint": "^7.9.0", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "736535813043b804a140b0b4424de8a5547ee0a6", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-1.0.1.tgz", "fileCount": 40, "integrity": "sha512-c4<PERSON>YinMoXLhLNxNJk84JMNQizqhFvxCW9skM3ipNJl2/gVT34/DB7fdwskrpX8s96AP95aZPsqLb5l/cIgUgoQ==", "signatures": [{"sig": "MEQCIB3zBLaJD91O95hhLFT15uZHuZKA5+rSAdTEUyLES4RWAiAB5whk/lqFDtHLLKww4CInS5hV9QMHK0ExqXctyBNglg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 283173, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfhUAZCRA9TVsSAnZWagAA0CkP/jeqZHVjBD/lqiiQMgNl\nygHR00tuGGJs81YE3PNX+/0FzSsUXaiMdz49CSgoyopZZRb5UQnF/VnxhuTN\nLE1Rp3ZuinnClNYdFDGjEVVivKEWKnK+iNv+39gm2tRu3J93AX2X+RX9+71N\nyoclfynnu9VDRVLB2esecTw1NQTTplGwc0ae7l++iot8lUsfgDJrcLctCD03\nKCOHhirst+11u7SjfuojDkwBQNnGeCsk+hiJM36q7eeJc7MnkapoUCGEZlMu\nw0cBdzZliPR1VS996GR2pEQEabQiitS1Jo7HUdGdkdfog7Ez+kmQdGF98xaP\nOJTpX969w5AtEeD9PK8iy+Iubc4KfHqnbT5bqmTrq8IfKQ3FhqrQHud6gDvm\npbXYreL/94P63b5zBxCcFRI5xMtCuTIwbqtCQcxbvF88X/22Ffj2z+uHcym6\n+y+MxVhqJv1katUermDYOLBRWxb1SgGsFJ3wyEyLNhBDV+2znxEUF4X9IgXd\nz+wu7CGKuIXfBo7s9HvjqSrOTSy96tszeZaRLAhVBrj7iENDbkjWKn5DbJ5w\n6U94PKd4ShjQUwehIM4oPJFzBlIBBHbEdaErnyZD1hb1Ro0cUT17XEPX9G3R\nkbk7gNbuLxFsQYC5Jg1Nu6gd58Iwuyw4qlX/tF1D23CRCrxWF4pokxrU34Ze\nsVYG\r\n=gqH5\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2": {"name": "@npmcli/arborist", "version": "1.0.2", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "@npmcli/node-gyp": "^1.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.7.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.0.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "eslint": "^7.9.0", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "1d099a944dddc777ebd4974014b54257546cf6d6", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-1.0.2.tgz", "fileCount": 40, "integrity": "sha512-YBlU3HcRI7Q1dVQa4ljf/ifKxbAZELe+e66NP/vNFrv6pv4oVrOOkBrMCC/0tareDKKpxtHoGS5oVFwCF8ZzGg==", "signatures": [{"sig": "MEQCIHQWMzbpJ1zLFM43dX9T7qKbQiCeY8nB8AeC2CF6hT0MAiAIp+dpe7bAj3kAP7ro7O8OYHwNUaCtAFXnhEqY2Dl5/w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 282878, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiNC7CRA9TVsSAnZWagAA6A8P/31bLAsm1KQDGmafvUw3\nNRJr/o9FXIqiQHf9haDF+RoUH6NXhTPyeN4UU+pZzdsnr0O/EX7QysuwUqBT\nuXp6y5Fi7r4Fio6xHwWjlNWAsLvR1dJD3dl6e3xVeZKQGyesJ5yDN7jSeC9V\naW1BvOrPiaoI9bMiu9+eMqH+4HrlHSyKSo8s67+ggF32bhQuyDshvxiyxLRY\nN51tqlojIxwUUcFw5sNxvGLFIGsC9olgmRqECxall2Wv020sBLL35uB7g9HG\nor2pS8/qKMef2j/GjnfkIzfB91/wlaGaXShe7+cTsSvueQNkOHmzNQeSevFg\n5cTQztOe0H9289rJc5pp7u9ZEx281+K4m/YkpIJN0z/7oNhnN5IaFxgifksL\nqE6s7TSH4Nk6+nzyLgyQaXxjSYj7TJqk2dxrKmZ5KwABJVVucbJuYHmqypgg\nSGX7A5EljepiRZTyCE0hOQHN4fnl7fsbqe13KapYX4HnxZZVBv7zT2NPXiEG\nFJ5jv3YYDpPTxv49rY2n1BvAeInoJk2sapd64A4iFE+p9jv6J4tDb/eiGlyO\n8AoR3Slmq99+ljM6lxcChVfsVcytSEg7gjJ/B2RvLX5aru/DdBlt1Dy4gFVO\nbcHejzIM+8vRGZoGDPGoGsIwV4C026sJYFrx4+Q/jc5zaiQKUKfIQArS6BIv\nWGJp\r\n=OLNz\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.3": {"name": "@npmcli/arborist", "version": "1.0.3", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "@npmcli/node-gyp": "^1.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.7.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.0.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "eslint": "^7.9.0", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "bb4eb8be109c65cf12e2483681c9a25c6f8b0350", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-1.0.3.tgz", "fileCount": 40, "integrity": "sha512-eRyDsmcWISxK/hz3RyqyEJAAMG7qF76c0ckaw3nRgU+1L05kaA30COKsrXlyB/G0gV6uaiVvPgZJknm70hMaYg==", "signatures": [{"sig": "MEQCIFdZRcn9z1Ev4Xi1UewAb6Qm62h1oNGh8arwVelCT+QeAiBhYsBdCzZ61rHCR5tr3epJhVSKZZBBxL8RGPKDss7IbA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 284509, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfifLwCRA9TVsSAnZWagAAe80P/29vxcWr4KplqyNccUqe\n7Wm6OVbAni9kVCc91YwvfLvyN0ElAz3Oq0jyIci6kP7jou5IZ9vyotDUfOR8\n+omJ2qJYvjp7uXmqVkJ7BhJo+ksjSGa5V7LT3OWmmeaP0uhWJu1DiMsQnfcm\nALY3wgsIUdQ0AS5PrcLE2sznsW9/ttAtjs7UJc2WOeVgf2KAwyPMbtpijy/T\nZea3YPqusCK1OJdAYMpJPRhBcZQcEIkrfX8TIxP9hUgGiOQAhMKenIFe7KF3\n/8zKJBWjQwg9raDwLEErjuoDuBl5/OQcsmHieiawUP4GHB9rwsV8krDN0eKH\nGCNfUW7XkXd7pHNmQfrrCzj/TpaG5qGUE97HTnF6KcE5VIP5Ly7IcT642BS4\nxgYku/YWnBUjTnmPtuNsZtwLzjKbhj4SmosVcwoLZ7WBc4UGC6MmWYty+cx+\n4GIfU2FsKVeOrac2DYaA41lCZR5uKbNuzDU1IyC8aTO38VwHf78qQ+xdXDdv\nEQ31ZigrJyzMjjCCX5Ow+SuQxl+hujMPQ0ijIds9mnbgalEuirpsiMQjAuzf\nfpOdJUPzoTbgCTsPVWW2tVbuFpx0T8HVZYvvpDyd7nm8qDZZZMEIYCaTgGQP\nXYmXYFbcQx6zbuTHiqUfqLaNowpyxzJ5glyqM40rEIKu9hFsuP01YHI66HqI\n9qHx\r\n=yEB8\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.4": {"name": "@npmcli/arborist", "version": "1.0.4", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "@npmcli/node-gyp": "^1.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.7.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.0.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "eslint": "^7.9.0", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "0eed6d6f37ac76248cf69ecd6b1767b33bb5da3a", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-1.0.4.tgz", "fileCount": 40, "integrity": "sha512-1P0+eYHxSQ6PHWm7hQGA0l8xgw4isbrG0eT9I6J9zY32GgUb/ehmMSbgWuXK3/ADWTdldBLnSBICR2oWX4YW7A==", "signatures": [{"sig": "MEQCICEVLK2bS8H0WVAw7iSNQ0ek7XaDj3xG0Vbrcwhl/60fAiA8A46SHLmAelzQ9CfbaHEriWczt6WO6XbjXjhX1l2Dfg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 285713, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfkxdiCRA9TVsSAnZWagAAYzkP/2ND5rPj7ODRQ8IuxngN\nMM7Q2xHcLvreqASLcCsELutostiytYZtlLQfqN+irfbRe70aoQHBYqGcZq4Z\nm2BMpbBZ3j0YXBNQjU4MQvKoWi2zc89YM6X8Te3T0QvjRbIH8WnPXso5UeqC\nBTz6bhNP52w1CSp9fAdOgzE7iptz8iIkKmYaQVkhl/vqr046JQRrvb3Yz4AQ\nWgnMy7Wk+t7Jsm030abTcn+93AaYEFxenhg6RaZa4MrFYvnwitBQQfJjdC5n\nhAYnSJHvUqVLRgrbPka/tsGq3hTthJ8qubwLtRBOQZR63UE8BkudggfR0TNz\nVZ7YvGLP6D2AK4F2nYPxlD/5Rin9h3mh5Aqi/xgeLO0UADcufYVovOw8y608\nnhpHX7DUva7EjlhPAPKnYhnsEV/bR9DVAwS7JWPFpXxRypPp8Dlt2Mnx6BBW\nrp/ejJu+zEyeEdXsFGaQPgDKjOOFp3Oqn4cGkeemSnv/e04f3xoWk6giv56n\nCbQtgfpfZaf2mngotPt0rmf5AyDxLX/M2EuaiyJoJpUBD+eDdp9uWHuX6F9/\n9pmgEFAIOWM1KjHi1d2LhyMrxpqq16JpIuluybN53apmKqu9dIUozG8YIur8\nXjiDWCwg8fFtXw3Gqw//qOj6jKmG0hLWB5Y26AO3OjO8hRSAXpOwXlUQkUz0\nTTH5\r\n=m8Bw\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.5": {"name": "@npmcli/arborist", "version": "1.0.5", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "@npmcli/node-gyp": "^1.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.7.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.0.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "eslint": "^7.9.0", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "0efb58de087e3cedb05a074598d653db99d6b801", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-1.0.5.tgz", "fileCount": 41, "integrity": "sha512-VK3VqSxnk5muyMB5zIIg12EsmV0COBKpBE8wQ/s85O02nh4N1suUxuf9nbgFonsi+D8uN4DO6qtdQ4FtN6i6+A==", "signatures": [{"sig": "MEUCIQC4qQRjHGdW5L7ZPxD3lffCXM/m+52clKtgoXjuPbBOmQIgGpwuAEtqV2E3Y1nuecv0Ye0OG3PWCjeccIvNX0Ypzzo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 286835, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfmGikCRA9TVsSAnZWagAA51IP/Rdbl8Urc0BkRSCGg7D2\nnhTN7HP+PTPA1equ7nY4ehZnAj+hfbHx6nWZPVb883dpJ1eyfmazzKM69ndB\nq0Gl2/57FiyFaVSvt5ehtYn/dXdiIeXbphE6Li5rrPDKg5KnQhiA2e0iHQEI\njlmTKtvGZd1mDdNNGenU4Bbw6Z1yHXD3/59mQl9G+5PGBoGfadoPBOEC6d+V\nNNDdx0j5PBhw7aolxfthrwKu3xHx687bCUxQZAkN8iH6D+OWdxyMyvLAWkJD\nZ+TVDbENTxInjkxNLKZ2GTuvDGFdPU2LQthYhM6HtntmmJ3sYbEMqr/NGse7\nigjVxQPaRXWEXDGsiM5xTSeu++nyR5vHUm28wWLvoKm00LXbVC5kQhLPpQ0j\nRfy3yEfdTQ/qoOXZla/xSZi1BAtGCzy9LSeZ7I42TLS1ojlytQdtmmWf49iO\n1eOkMocCwq9T+OoQsmv9A+SNxvfT7GiksQ/ARUdrMkLNwWn/0JhnxcORaWwM\nCgNHcpdXmPF9ugfqHWnepkuGk0M9hZPh6ukyuuTeE1qtDIJTKZ6Y7nLSmxWI\nP5HK/WawDw4D67HKj6NLh0MsR8pRTu+Cwq0+aOwxhInLRpEYKZCtzMzAos/j\n5JpRJ/eq7LNbAVokOsZUDOgz1NdvBHfaEv/IMYRlcR4O+A0ZlthtwnEXn4/I\nQoWn\r\n=/vim\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.6": {"name": "@npmcli/arborist", "version": "1.0.6", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "@npmcli/node-gyp": "^1.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.7.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.0.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "eslint": "^7.9.0", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "cb9b43744a09c22d159d16567a706c4da508c182", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-1.0.6.tgz", "fileCount": 41, "integrity": "sha512-Hj5QxT1/BIU5HMb3sSOCV81trhc6sqT1FaCdXPM+YjZPLpQsO3UFKWAGuDknDcNyx1mVT0IA1nlmrjhGGXDDtQ==", "signatures": [{"sig": "MEUCIQC3Wg0b1jjXlsN53r+DH/lkWZtCsqqw0kFqROR13zaTgwIgdDBOLONhz4c9vtFeDA5E+vbIJ+WG4PTdJmtCdK27Rh8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 287038, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfnElaCRA9TVsSAnZWagAApYIQAJcvEeyQDYx2vQdnQ+t1\n/9USndd/gtGITh+liOv/ExBrxtL0r8GhrC2YT+OHubIBt/S/2IXJXw6zDO+w\nAntly/eIOYKcJt91t8evWRZ0vAjJaS8b9HV5uZbOlXWY+lFAqvM8NNm86RO/\nJc4E4CCG6Ai3XCk+tRy5OmH4TBmkZLDEY180Wil+0eQmzagltjWw+cHm1xQT\nRO1+UJpNccgInLU0iJQsHoRb2X6D4xAymtPOpjy3xpYCDmTpm2Vb3W30P/U9\nQSH/C0Vq6bf1qX+SdkRu1Mfyyp1BfkwFrnaGNK+IsNY1tmOwx7wJ5dNF5lBA\ncGAO586ry5zM+Hi3QWdfK1pu7H+bAMouitiMLReAXVa347xyW/Gh9OxVX3fr\n8Ne30DgQVtkOSqmsYpEf/lRHlY+FVWwpPL3aL8Sq8pY0vshM4LBT86o9leXk\n0Gq4+C+OU25PUZgHjKLahKUwwJtbWCLnlWPCX/FrJVEtgL9SEvSc9RBEEjW8\nj5dDs7MM8Szxvn2FjDKn7oZnVqemyMpWAjMbIXpLcdOSwmJo70Q7BkIyI7n+\nvdIbzqsp2Ins/EZnuu73ZvJp2PvIPTHUkIdUJkM0xdOnDPleg5Fws6yZY9jn\nEZ23I3aJTVd2MY2jS3Y0fGFmZeIY6qY86eS98sRodd0KfuawE9mAt04O+3L8\nc4SO\r\n=L39W\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.7": {"name": "@npmcli/arborist", "version": "1.0.7", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "@npmcli/node-gyp": "^1.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.7.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.0.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "eslint": "^7.9.0", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "b44ba5b4c5280321a079fc2b282ea86835787eaf", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-1.0.7.tgz", "fileCount": 41, "integrity": "sha512-v6d7RRtoL/RxV44IQCYVo+ofiPsu2jD2F2ZXN1TZTEAfzGZunA1AzHcn6c59l+M4oJZWUcDYSUxZddAk1zOZWA==", "signatures": [{"sig": "MEQCIC0Tqbyw5ffCIO6WDwjRykN+NmTtcCD9fbGqI2EkmGbfAiAY7tht8+vyVLG+gM/3HMqpYQUfR3Zi/vcyGQdXqemXGg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 287994, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfobXzCRA9TVsSAnZWagAAFa8P/jY6Mz7sxWU1zWmQqoZH\nmvLi98BmDueWnUZ/wPWYWkFiXQTFsooezuYKGjBwfYHRE2wyVjVmufnoDulV\nxOVff/TruMvCKNoPtYehvDCRsnZfjqANmCMQpjVpnEZA0QvHe0Nye0RZwsai\nehNPEz+bAQMkK8CJtAsiyHE0JM8sWNV7MORY0RTRJt/xf3e4TZFksLtuBaQG\nygaZ+EsJgnZVwOFPJAvFDwhY9B3uCi4QOwoAeoiy77equOPhJl+75xB6zVAx\nuRRH6+qJMoCnJ//FRTy/eURYxHQaq4kU8ob68/9JOj9k0bDEN5o1XQs+DdaO\ngPQdT9Xwj/qRt07uxFVIULh6jaAd3qEe/ok7IAwWFnZ98TIsinpu42R4sSIX\nEeaFy/2TMQ/CVb4yTWx7SOAKTChaiTq/+m/bDfAg2wF4k8Z32NE0eTtjvqwD\nHfSr7oERYTDp1JAyNL5ST+JNNSKBNDDrF2ySv2wLhBrvI8EbzuiwnxAZ7NFx\n9M4Mefl4N5HpqzPeKDsq535oq0RqqsRMw06F2m38Eb6fro8gDtRoQeKxFJuy\nBX5C9tH+vbFSdlsR7dKCljketFRFu5PYWUTshWxtY56fRqX/qoAMH0oQwEH5\nNG59YOyrTlsTqVzhtEWIrO0DuHrqZyoSP0uHc4yo5/fE9jgZzxS3RY0RRMfM\nOGig\r\n=d1pu\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.8": {"name": "@npmcli/arborist", "version": "1.0.8", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "@npmcli/node-gyp": "^1.0.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.7.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.0.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "eslint": "^7.9.0", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "e11aa09630b3d2c87bb670aee06757fae325cc4d", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-1.0.8.tgz", "fileCount": 41, "integrity": "sha512-LAopKkhAnBr6/cbIpH9VeNS4yLWMFf6AFKC7x3XHvNFDMy8cmvEjWtjcpV8ACjPPm4UbjIzQ/n4qrxaA0ZyBDA==", "signatures": [{"sig": "MEUCICepHdJPi4IBZpSkIeHvIEGlm9d33xz72lDSoIFt5ZHqAiEAp5MkumTHJigu4xiq0ZsJfNqisrXMZWuWIM+WyNtA4s8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 288241, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfocz3CRA9TVsSAnZWagAALPEP/jUuRSAoCWPYUUZELHx7\nUeCcG53g/MeVrKlZk8tlsIKitxlwd8zQW3/HmEljWBeSN2C7bYkXsr6pVzvs\nC4fExlYP9Xr1sCxTRB/trwcwwWMgxbRD2mcf2oXVMHyKc3cpSgf0Y2eNTt2/\nXLUFHzF9K6VJF2NKexoLlyiLT8p+eWi4gdIpewQ9DH8D6YMHpjsvNoDlBuuW\n+5Iii4VYrG6hsboXxKlP8xAeVu5IKYieygAKqlDLCP/vQ0iZT/a8Z40lAuWw\nM/7hujQqT58KaUOEq+vzADkthknracHQ6EPQ79WWFvVDlbr78k3oXDpEfvky\n1bsrBl2ccrVtUjcLja1kAbzMh1pxW6ZXTtD8uUGSNqm+Q+D2iQBN0/aR3NxO\n1w6R1GJ9tVtftjXbWu0wGd/1qYqA481zlvDdjjX6mLbO6WXo3yThn85qLrz5\nR32eaK+ttvWFCO+NBHLmlkdBXH5QdIOoYHxNw40p6FEeJBLZxaYBmlHvn/Ob\nH7Xvfa1JGWyAFzTd6u0C9xAkbBmDFOnMR20TCn9TzkuEhjXEDihBqMs2xmb3\nez2VavoIOor+XiPOLiokksgJkGR57VzPdFzoLf1onl6H+gd+s/D5s+lCUltK\n+NMv6iRlk+RaLO69ryO3pSF7vKvDm+p5nhC+cgGbBNE63pUNV1vXz4/lp3W4\n38Da\r\n=ad7o\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.9": {"name": "@npmcli/arborist", "version": "1.0.9", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "@npmcli/node-gyp": "^1.0.0", "@npmcli/move-file": "^1.0.1", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.7.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.0.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "eslint": "^7.9.0", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "04e1a88e0ed0a07d975b826363ee18a35da877ea", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-1.0.9.tgz", "fileCount": 41, "integrity": "sha512-bvaPKdlT4F0U2JT0eV1fprv9dWdjFxY+O7WbXMvABvyO81kUPTJTBe1HhsGTIqXTrt2cOqXtLJI31Xs7iugptg==", "signatures": [{"sig": "MEUCIQC3htC5JR6/x9zAKAoiNFU2yfnspf2ZuJxUMJOM69bGvAIgG8GHx0giTRTRvOixT3/f7wYdWo/o11d73P3iFpiIF7A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 288090, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfpZ5MCRA9TVsSAnZWagAANeYP/1FAabc51l82rix7jUYB\nPJJeWN8pf+QBWjr1bpySO651xtXIBwSa4oE/8QrC0zctS2B7KoBV0rQ471GW\nsocMlNX/v4nJaTIRSRyh4O/612N2lWiPhPROn0M472i+Koj4iradS9F+JcOH\nXn2xhgMoYw1iIYeVB/lHhm38ThbMpe9mi2jHlMgn5mPonW31NHxQvYcQs7ay\nRrOfTCzR9p6v4i3QvmQQHSi8Q7JDuTXPYBlByt25Yw5nX5epCLwGKH5MzJGY\nF6JPlPHr2p6zuUwOC5TDI3xEnLJYfSojUHqU53wVHkerrU8zI6IU/imWOgdT\no8Pwv+axD3qxAqmxGdZuFG8Wm+9OJFDhk+b72gtrzeiyACBFYiUK3grj7ZiE\ndMeN2nmTm3k+I5ZuhOmmGUnK5+LKOWOHWXxiccYAOhAtjfQomY9YuQxpF4aq\n9vlDpOYycrKmiaCF+2+xi6uUQth+qqA6nP+MPEH8/pl3rM3C9+lTUjAmboUs\nvg9aT2qLEd1a5ulqtK9MnxpwKtr7+7IdBilixPMFkDVrdokwzOszuKQTtkp9\n3wte/V6GDdOvkmtRTfCfBcyoCr3OJCZWc3kijjVZV7Mi5ipU9PTyaeXMoYAx\nQlo+HG7w65jXwrGU4d3xJQULki64QjuE3sasdB874qJLDKqYiXK/1Ffkv91R\n8Ngk\r\n=2b1x\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.10": {"name": "@npmcli/arborist", "version": "1.0.10", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "@npmcli/node-gyp": "^1.0.0", "@npmcli/move-file": "^1.0.1", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.7.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.0.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "eslint": "^7.9.0", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "2ea43fecc21bb4e5be1026a6a90b03077a347ee7", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-1.0.10.tgz", "fileCount": 41, "integrity": "sha512-k5HwMlztD7clml2PJJLMS01QWUolzw6MXOyibhipmTHtKjsh5d2WtQNvPMxNYWLyACpJu9xIfK2OGaJpuNwbjA==", "signatures": [{"sig": "MEYCIQDZlvhV2CKH1CyCYmfBDYijF3zpk0AhW4OaG3/6Tmx+BgIhALDBkhcqJ6pkI/HzGt6fQDq7lruBB1iBbMG5+bSHkFEd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 289773, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfquaECRA9TVsSAnZWagAAQgEQAIlzBZFavkRUMgtqVSxf\nubBpUw5O/cMBmldtJZ07IAA9HiaBgrW6to4DuTrhWPiIAdnDM3YkZVNCYNxM\n/VtRuln77pCbKbZYeVTr8sS6O4I6diLnvmmAbbZ+cmn/OAzK9wi7I1RUOCp2\nTM1n9cuWqkQ7QbX2vr6p8jQLN8MAgUu9D9yhWkSNUxqnEt4ytu6m2W9NBIqF\nXq+vzp5/GQ5Lwv618kza7I0QBr/g3w+CeJH5lI0PS3lHwQ7gbSK/Yj/Eu2Eq\n2wRWeeR+B9rpfIAtx5o+JW402ATLX/uia8M56UWA8gm4BFU2ElHdS+cAdc72\n40+GLkt7x3hTMWbJvsUJRdRi+fEnemQZip42fc+R8WXsNxUZtCQ49FHaRb/X\nI9ZsuAj2YxsjQST3jJ8U4Ex4UfpSHaTYXFRXZ9E2vHHJKSy5coob6A7JHVQq\nL6nWk5/2fzuF78sswAqztjlyHnqMg6ImKGI5SLqb7rhuRDyDand5EWkb410+\n625smtDx2PhmeYlb+TCmxs+PwHrk4YwAz/5xAEHZmaAoK8C3nVCIkFAfqsek\nBdlVK3mxJOlpnyQ9T010vpxZNB8kFft1mTbWncdjr2yDNY4A6DETmf/PBD32\nEgASi7O7FKB3jUEj4lqNprfh4zF8tLcEoiGVUNd3su3uIn5iWAMfTOBtRAio\n1snQ\r\n=+7Wn\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.11": {"name": "@npmcli/arborist", "version": "1.0.11", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "@npmcli/node-gyp": "^1.0.0", "@npmcli/move-file": "^1.0.1", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.7.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.0.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.10.7", "eslint": "^7.9.0", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "9bcabee7f8fd8418087cb0e3fb7d0a95564ae44d", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-1.0.11.tgz", "fileCount": 41, "integrity": "sha512-GKd8033vyh0ov5ktnrQaQS7yF7HS0FU4MnRa9DkITpFyQxcnAeJsR7gE/WBZG6/zrbnj+XeoW/IZ8I+WwA3O7w==", "signatures": [{"sig": "MEYCIQCZqHX1mewI2attxRo9uNnaBHfSXl/oCGdZKYD+GwvrSAIhAIqmPm68gtyylHPY5RBScoB/OkNEoqqb22t7Esxqyb1u", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 292138, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfrti6CRA9TVsSAnZWagAAOx8P/iSqkzd1P68FBs77aIaV\n3qLfeAlRpcHSCIgemBr2QNRS73beehV39R8y29CoL5YF/G0DA0YF09ufDmZq\nmkaoDbJIuZzrkZUpVdGKwKn7JJxxyrd3oW8Y/zkU59JZBjUNgXo2WGhAV7II\n10PvrXCAcZB/yRb+H218FgxU/VDRbtlOKgmcyiekRjKjD+du1AfT7+Myv7JT\nnE5kSUT+pjTbc09OGeQU3lUN54Yp4OhhsE7GOY79hvo/sKS+qdlP7Hj5cew0\nLcml4306hLhJ5MherWyuxiEcPbPulkc/VpJxuwgS9hVxLycLx/v44DBu5b+R\n39Tz8t280j1i8H4L/SFINWMJ7TKrUkQ+giDWfCtBJUvfybub1dJ4rMD9OXEc\nYSavRLyDmTEGbj12rNHR+LqZpFXAc58cOwrFZ3e8YGp/w54hGSDQa8U2zKGZ\nfPkNQzge846GJiYnPaNsgSTGND5IDsLawAHG+QGTC/H0oQOOljPHN+4rNaQF\n9YOmLap9DC9h7t/K8xk4JwYZABWbtOU+uALjDCgy/MO/HUsTP2fxqiy3X7uX\n3+X2nyKM7/mTpArZWNOobHzi16bOfBHm+iJdux4wT9bhsFBd+euXJfsIIZyi\nSFVYzfVMH/5IAgEhJNfhGUtdu4AcPXja3EFWlg2KpSFRUJOd7d2MKXqjU4Ki\na0iA\r\n=uZWE\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.12": {"name": "@npmcli/arborist", "version": "1.0.12", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "@npmcli/node-gyp": "^1.0.0", "@npmcli/move-file": "^1.0.1", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.0", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.0.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.11.0", "eslint": "^7.9.0", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "b8b5e6cbf8b65740a1f53e929f53df521f26017a", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-1.0.12.tgz", "fileCount": 41, "integrity": "sha512-w/Wmy5qBphI1QiuPt7ff3+SL1/PzVkEjuBQj3zAhNxvsXyQMHtK4YKyFm1cUbO9tcEOKhN0RfHuax0LCmDlMzQ==", "signatures": [{"sig": "MEQCIBT9NKpb5uatv4oUrhJzxvuWrIEGs3FwwAWRav0tjNtjAiAyOERde3df9y/V2994mcQMk9v7f2rQlcnaJkzmKgHasg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 292231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfuAqUCRA9TVsSAnZWagAAIe4P/1JrMpk58NhBeBi+/hTq\n3ZrSJqxkZd4CnrUwm8WLI+eoJarNRWr79iHYcFCQIXWGIuk9gwWjMPZU2ZmN\napmR//pY6o4F717AWg3y0SK++uAKace8aG4JNOH6i8DiaGsnTGfaTnEdV+1b\nzr82H5JE/q/nZpriL+HKbO6fnqJYENnbPlxa7Bmt6vhzsZm9iDfM8NiHgxVE\n9RePRKJc7ENb+TdB0/e8fNoybsKjPJyATdw89brX+IFN3lSJdlDPuJg6IwEn\nQMAVQCng2A8cle0E0HlNTvEoz/ouly1qIXfi8ukxqj5/QxLYbhTXMDoR9dMk\n9t1H9U6+aGgrccY3I4wjgpsVT9iXeCheH14kxs8eGakUk0jQcXO6nw4av69r\nSmVmAoRqjsy819KTwj6fGw8GHWqGn5yoYxSuP+59ur8Ls1g/KzmB6YphHmCg\n2PzCkVuIkDfxBFLOkoYvxwdlewr/JwcNVrvKXmvT0s7XwUwEja98/nlAbw0O\nvb9d9C0oWdQ4ar/4z9C8hL+INOJtrGh34QtGNn1uDuKP03hXizdRwWDuy6Yh\nBJnvmFki2YzOFXjuLiKs+PPXNkysSr092JTZwZghFAndjv+o3dKE4XdqWIj7\nl7F5sW05UVnlhVlkOND37AlMLOBCpJEHSWF27rkKVUB9lq0ADfie+j0Am/sR\nWPnJ\r\n=zuIJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.13": {"name": "@npmcli/arborist", "version": "1.0.13", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "@npmcli/node-gyp": "^1.0.0", "@npmcli/move-file": "^1.0.1", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.0", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.0.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.11.0", "eslint": "^7.9.0", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "f3d2b761213b8a476e84acb58808a0044ae441e7", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-1.0.13.tgz", "fileCount": 42, "integrity": "sha512-AtNV/iuDFiwdPf2RqnTbq3cd3dRL6H5Vv3Ve+78SShfXF0dOFJrDsaVOFbmAqDKvemBny0cI8GBu+ncjkAffBw==", "signatures": [{"sig": "MEUCIQCzW4b8tf2tqS0E/JGUc1/OhTO3HbdF7H54/4qkxog5kwIgT0G1YBNkqgm83K1WTK1TUoOxZKvfF0W42lujXDFkjGo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 293317, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfwSMoCRA9TVsSAnZWagAA44cP/jb1NMM1ME+3Dzqfj1Ue\nJVUq6m1joSE0glPpsPB2YBDpK8QVMdv2ndytl/HzFDpyv1shZmQ5VRxKW+nt\nkIknbD2Wfvrz1DFx1pq9UjtHMgqSgUg06AlXCY6H8KPSosXwQ4rXskkwjDql\nb6HJ/gPBlGbjpyErD8xtwaXMHmgkxLIDox1XGmOQEjdYfG3kGhCBlIMlq/2m\nYC/ZuwrkykwPYg5AkMI9Ar/XtJlYSJabVw0AMR3ofImQB2XBpXVZwF6INYpI\n2Oa6l+1zjcKmgv08Ab56VT+T33KR/c/f/MIXdtsdonzQbpUqBd74/+mZwKxi\nQd0d2oxCJ35fqWXQKrY7Jkqt8Ty2RoHy2F/9J0bm5O77H0oJpG7lUx6VdQPe\n+VS+ClnrDiDfuoZtUlvGWGgfuiJb5j3xJX2sS7ytb5koMSbrvag/yoyJjQk1\nvMtMZYP0ySSa8iF7aretK3tFqRXg4ysvvPaJsQ+HZibRWp42ctsnBQWtVooo\nv0mxiTvN6vLxYLHPD1nLUknOltOWTHJjlrr7YhKbO0kWGTXsaq8lsyqvXhan\nM7hbBeOES8zm6hM9P6mRDz47ZkVddYNUvasug4gXBrt32tkA0nNVAUbjL5yx\nJfluyVjK6kpR4LZ8TOaEzmq7oQIGffFlranDMAjmQlFw9E4rdv1H3JJCOC1X\ngQle\r\n=PuaL\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.14": {"name": "@npmcli/arborist", "version": "1.0.14", "dependencies": {"pacote": "^11.1.10", "semver": "^7.1.2", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.0.0", "@npmcli/node-gyp": "^1.0.0", "@npmcli/move-file": "^1.0.1", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.0", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.0.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.11.0", "eslint": "^7.9.0", "tcompare": "^3.0.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "d813feb114edf36cf7ac9c9febd289413e6f042d", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-1.0.14.tgz", "fileCount": 42, "integrity": "sha512-caa8hv5rW9VpQKk6tyNRvSaVDySVjo9GkI7Wj/wcsFyxPm3tYrEsFyTjSnJH8HCIfEGVQNjqqKXaXLFVp7UBag==", "signatures": [{"sig": "MEUCIAvNpR3G0j7WOPUuVMhE0ZdM8PnDNcsoeFD8Iwz9YWIMAiEAn8cicDC2Sf9MFQydqTv6S5XYsAh9Af1sig1nApNI11M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 293384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfxsLaCRA9TVsSAnZWagAANvUP/3X37RhFQGx2ySg9Zb49\nrbsY7DHsttrmzPQ4ypldYvGSSLdXMkOb5wSEo3Cwra9hxzm9HfM0nk/I9Umr\ntZ3UmQBthlyd6Ku5n2Zvx5M3kQeJVqdJyK2tMqeEyr3VeI/CENBpMfQtpuJI\nP9oILoqyifGvNH2NbKuIWtBLngVRNQ5ci0D/VUKjUIe/EvYp+n88gKlsPY9b\nzn2QAbHPF3xGCx6uip6YZ0aySYBQu0ydyVKED4I2lF322yJdjV3hPjjPKycf\nc7Tvx6dW2x1RR5BHaPwlJNsnQ3LatQ4VITqOOi6orgmoTn7Gm1ynqD5JkyLy\nfx5WaKz3xpwXzLaQ6k44p33G2bi5FAtVIEnzhgi4BIH0z55HD3AEg9qLS6p5\nfRTZ768uTzY+/deJZ4VDR1zOnj75oXjUduRwEPgHkeaBt82Cwgzwgcbs+5gM\nmDEFlWOePoiZyffRfTvvLAdd35Lb2WydNnEIaKREYvGgDqVxikX8uoBMk98h\nk8+xyyHXwtdSkZ5x+PaMz/p60/zLF7T9n8Ug31dUXTkQcWlZdwtFYdCqkoCx\n7G+d52U5Xtz3WrPIo2esXen5KdLOnrgtEXyu1v9ln5NTCAlttaJ3hdEtFqHW\nX9ykhta9aUSKUqlJxUXEfm8lBACEhT+ecjIzwIObPpozTZ0avvsL/qwspsHp\n6TqR\r\n=vJwk\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0": {"name": "@npmcli/arborist", "version": "2.0.0", "dependencies": {"pacote": "^11.1.13", "semver": "^7.3.4", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.0", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.0.1", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.0.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.11.0", "chalk": "^4.1.0", "eslint": "^7.9.0", "tcompare": "^3.0.4", "benchmark": "^2.1.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "bdf845b648218edd0fe1836287b7d60e926b3dd9", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.0.0.tgz", "fileCount": 44, "integrity": "sha512-0h32rv2ZU6j5NR6V3WWx+8u10rwoF2SH1uTRzuNeTxfkHWbiuMd4xeUqJDMYzzlVAi9Jdk9L3pgtiDyXZP+8Lw==", "signatures": [{"sig": "MEUCIDe689caaiZKpdKcJd1sEmlZYhkSD3PdsgwjbVNgAOwHAiEA9eLMOXXB0CjlURlyLQ6WS0c+c+2CsmlHNaUc3Xwh8ac=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 301747, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf0AlkCRA9TVsSAnZWagAAdDYQAI4sWxvCnQYkqxdEVfg1\nxsAkDLZZ7QwReEc4Uaf+yifWgJxd55daZgBHFDB4rc8CtonRkQ1WjjOgy2FV\nh85eWJoWjzZtZZLZx5vb0TX3gJ+VwvgtQoz4Fl2k5bOEQUB9YSYeEPQDHiPw\n9pqhdkAblJk70KwOLQoa8CG6RbvWFfeIlFPuQvZS4wFy4hEefAZkW07J+9D5\nyv3bpqOFhnPbUsUIjSNW5w2cXQzGC6LxQuCSwJZ10qa1zr7+ofhvYsr7Pa3i\nkyALxZe0SCehZDyn4n+2vH5bTe3cR9OA8gjR2K9nvJDqObtMQZNkUJnln52J\ncXZPS3w6ZNLVkg1UHPdWLKgrHWibrqIxajh7QZnsB8sgppyc6oQqgvr4kqL1\nK0aXnSshGqsJ6RfUBPR49DUGdtFRuUQtti2+3Zx3gNn3rCU2qkq9OPxLiBa9\n51cJjJC8+nDNGUBJklETmUEhC0SuZcStCTxywZpEQXmGqtt8v0nlNQbSLcdu\n7I1pxNljtN0yjeHV4cJbFRcAJ+VQCvyOFEXSHimAfytuE8zEj2SrFxD4um0Z\n9f2MtO2X1smHid2jkz+KoKpvRT41ADlk1zyMNe+/kYKBrl6K4X8AitivYAji\nzn3G1zf/vMaCU/pSgODrntB5Zte+rtYEPvTRmjKz7bNw3rRl0XegqgrgL+L1\nNCgl\r\n=kuJs\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.1": {"name": "@npmcli/arborist", "version": "2.0.1", "dependencies": {"pacote": "^11.1.13", "semver": "^7.3.4", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.0", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.0.1", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.0.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.11.0", "chalk": "^4.1.0", "eslint": "^7.9.0", "tcompare": "^3.0.4", "benchmark": "^2.1.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "0fb6892cf4803f6d78c9b0e976f231feb3366f20", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.0.1.tgz", "fileCount": 44, "integrity": "sha512-xUH2aggJhPTrKu94sAQxwyPLr/LHFLYTEzd+tQ/Ubgrs6H1UQSdQKrq/U+mlx74ypxw7Ee/blPJBed8SnMqXpA==", "signatures": [{"sig": "MEQCIH8qUvlBKyJQpddqvhJfMSJcIvGByBPyiLnwlwPiEsK2AiBcKwldQOo8DIiwhnQSNp/vNlW1iCQ/kkyvW5E34k7/BA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 301763, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf08yKCRA9TVsSAnZWagAA1toQAJDGy6Xk0mrppki79FML\n+JHvuDGbvVnvCAk8jRei/fXZgxTnTN1PsIIQ/DgBNQkfn7lmbLfkmKxXI3eZ\n9QvF/WuqNkfqDoyvB1kDY75A9MHjlrnlM5GPSlU5r5LHeyZqSnSA/Y8TFifk\nyHZnuKVsMcUVhtMXf86b7hPWTyI4Hw3kPhxf14+K8Oj9KtYMlpbgJS9YfyMV\nyNrOIm1mUgi7FHljSI2gcGm9OT7nD94fMj1DVS3VKt0V9krvtqbUXr6Sk5KT\n8bisyd5LGKofe8fNzqWdERyQVbe+1HBw1IrhMnIJWHkjOG7ZpYhCOZ/JuFso\nmmoL08VBCDAC+1yh7OeqnQaAEkSNBCc6MB8PM5T/6P1GxxVk1L19IUj8iwzq\nxVPZG7nA0aU4xRPNTyUlJLTlVCeJ9tkEcgiXD3PM6TLJ5ZXn9DVVU/v8xmmK\n5vmzPlD/tMfG79oRXeSqeESO/0ofc0LrUvLLwRheajv/BjbcO6QyWvYPJhuC\n5rGuJb0AwMJAWAhNOjZ5TEcSvdXYJHhF85XdkPLk4hmY2/NHMfwA3OnsjF2B\n0upRi+2XblptRRYw1whkHyYgmCr1HwGCNAyaSiVk13LlJ5eGmTWFUHAdAnNg\noFbDmwscJNEqGj+6LMDC8DeKyTFkaHLtqPpNFSLYIkwKkAywpTNWPvZGcUHr\nT/ed\r\n=AH6F\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.2": {"name": "@npmcli/arborist", "version": "2.0.2", "dependencies": {"pacote": "^11.1.13", "semver": "^7.3.4", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.0", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.0.1", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.0.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.11.0", "chalk": "^4.1.0", "eslint": "^7.9.0", "tcompare": "^3.0.4", "benchmark": "^2.1.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "4b119e842f59fb150be81c36d01807270ba81a75", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.0.2.tgz", "fileCount": 44, "integrity": "sha512-QMMUSeGW6u9/T8zH0zCGSRtOqCMmv8LnRNjZFX+zv4u1dauIx5iJ4i8e7EJbvXkKEZyGjK8sJ45NIoF+umMgIQ==", "signatures": [{"sig": "MEUCIARz1LPYiRnaYbgwqdndKckT4cRA/AVPM5puGqKq5u5yAiEA8ojJ75wfy9xkDL/ZeOBTSouCdbCbpUY4Xdy3Ckp4qKA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 302071, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2Qp0CRA9TVsSAnZWagAAkIYQAJ6fW56DPh6eu3bltp5j\nhI4fCpj7S4kgkeXHY3zTSGFvlJierWmKlFvZXpx4fMUkMPZC0Bf8CDdJq6+9\nV85TYMZokMwFZcvqHuUlP6sOv9BIAGOOLtSCle7bhM6tw3bXTynCVmaOyfsz\nTdFB/2euPL1GEaDD2bAhqCy50zCjlIcltFGzKww7L9gEpxt9Xb/Ijp3wCKX/\n8MwAdZwvrsLkuncKGLsmwmlErqPZyD7sGocoXhY3L4pFUmjoL+yDgXnFZExK\nRJIGesr8fwhmAmkZWLJOlOB3zhyXtvmIskSBvaaUGkHN3h/d8a+79L1xR9Ze\nCTfUj1WYzO0CfAbQxDn+A/Ro+B+78pnizPPgRMIIfOkIoM97/pptSMjkPS1+\nIrsJ2XgWN1bGKNG7GAcyb8AXiM7GKR/0tPiVHV1POwBlxjFnKWTquxHxZ/1m\noZ7mChnf56M+8iWCchbGLYalx2eZ6pVyee0a5VBcMPme1Z4fpd/XTwGznFWJ\nTHWa9Y0HPsbyfsycgrnmuTZk80DbRX0L/7vVcVCBrDfS8TvWPtrO+uod2mg9\n/iO8JKal6cYtkql6LB7tfxDFIVUcgVyjVy520X3YNhvclPZC50ZyhNTIgpGl\nIpq7TctrzVJ725Qn3H7rzPCXwInx1/R9BJEhwrb4z40gf24Z5M5q1wMFmN3J\nB/Ng\r\n=meHv\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.3": {"name": "@npmcli/arborist", "version": "2.0.3", "dependencies": {"tar": "^6.1.0", "pacote": "^11.1.14", "semver": "^7.3.4", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.0", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.0.1", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.0.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.11.0", "chalk": "^4.1.0", "eslint": "^7.9.0", "tcompare": "^3.0.4", "benchmark": "^2.1.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "d11f85c6609f542588bb946d0223b57c9a968650", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.0.3.tgz", "fileCount": 44, "integrity": "sha512-iqahzDZaqdUyAHLG1SIG9jrbkLtT5xNbKX1ppAnx7mKx1u+BXYjkxi5ohewLAfyERH6IpODPAiRVc8c3kxA5jQ==", "signatures": [{"sig": "MEUCIHXWO/fAOcVE+csmwPrWSyjqgqlUUyvFpEpT/eA52v/lAiEAkgLz/Cm9wxwq2BLYW1LmHIU3V/ldIwTmx8E7B1VHdAQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 302834, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf92moCRA9TVsSAnZWagAA+KAP/jb3m0BDIKFymGgsj1lN\nmtxJnOzBNOq3d5Zue0XxN9bHYSxGAnYjYiX13g4cC2n2/MJ/f7wpvefIXwzD\nY86ATbGBFjYre48qnOes6PFJLvboHtnosYWGc1pMEX1aSdkj8iEaOuybhKTM\nrjXj17CgDe7QD6k4gGxyyBUlrAcDME8HLMZVUTd+gd5BJ6izOEcaZonavbo+\nXuWZ3c1rvr3bfVtY4ZhWmlB+IaS5QaDq2t/pY1Hh5zBlIBIY8JEG8obAOCep\nEM+Eg6lg6CKc/1q3bjSxV6dXHHb1BNwDNXpwyUmvgP9zH8YT9VD4eVgkHrNJ\nAytUnY3GGbPwcm7XP4px4uA3wP6Kd/RpZVsA/yL5Ra34arpWifwrSayBz5M4\nLCYLJwQKp/sCz+xycH2keIypXMS1i3FPvHsedZ6svQ4SojsTp0ruEcy+7xGi\nBkfLtu0olDHSTdhj+Af0RXkFq7iFiEpBMX66wf7B25PNrHG28gNWmf4TuU93\n+9EyNookZMs9PtQhixvsnXF56iL4CaZBfdaQntEGdw5C9thiZ9Y9xwOS36NN\n3j9Qfc0a21qWM6VmEBdh0NCaeONq5IC9oj4eQuwTElmbmlN1aIRXEyC7trlc\n+WwMBFnvmxXqW0NGgaQsZKQxlNdhZbJnQLzY0MsPl19jJXCAhdY2UBToruOG\n2r1O\r\n=z5qv\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.4": {"name": "@npmcli/arborist", "version": "2.0.4", "dependencies": {"tar": "^6.1.0", "pacote": "^11.2.1", "semver": "^7.3.4", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.0", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.0.1", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^9.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.0.1", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.11.0", "chalk": "^4.1.0", "eslint": "^7.9.0", "tcompare": "^3.0.4", "benchmark": "^2.1.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "f47d84d3f99811fc6c34e9438afdbc07d6083923", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.0.4.tgz", "fileCount": 44, "integrity": "sha512-nKS+QUYH40XkfeF7B2uftimPe67SqB52VODLBSTuI/iCI+zz7nIX+c97JXKip69CZg/CNNBqV5hiAdUTbVWhfA==", "signatures": [{"sig": "MEUCIC0mYzvTwx8r+I/izQEuHq5VEPe5bjvSIT8oM1pcNGxiAiEAnlxn9IFiMpVfUSMvqSuDDNC6GMecy/WkdAVEoCx4voM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 306619, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgAKE5CRA9TVsSAnZWagAA/sYQAIZkEu9pw2AAHrOgdBdI\nLK+fkV2pfbwpEoLAFach4k0KSnBSypDW5nQJg/Y5OEMQ6H7YculwhYz/Ti7J\nbbrNexWePWhqdKCWBMAP48H5VnGnkHF4RwdYplgp2q2RENq1szsaSQWgFxYc\nwSc4671yK1PsVQRg46mqHNprTK5vpX/mVp4YWRAj7EqQnYd47A78IX2+Zxat\nc46vA/DCChom7kPUeg/Uya334lDROZwf9I4WFLvjJQvJRFckpkelERimLglF\nuiw4NDD24nEsjNrcaMhMp4wvANHI36Wj5VpZIrfp0pJqw2ifmY9UrSLUTkJk\nk4r7PxApX+hsuInrPfuwLyhdbnnnlVuTfgxA1N5fdyTwi4nsV4cJHNZwZmQS\nQ4OlKdGSaTtW4L7KeDAr9F/gD4rFBcp/5rp0luZjVKBsKARgPO9Q4NpGRa4A\ng+3ZBJEEyTy8K6tIqC18xruGFOA1v0QArEZH5ysq8vFHyFesGlbheu+bWz3E\nFf4YnOCM0oQQR2g/GW+/KRQ+/o1ZQ0wZX/k9GIZkloMZkK/P09zHhFjYDYiu\nFNXmbheu1GNgZTQNhlI92WusjYu1kBtsEH0h5LA75PX2RUuGVA2XH4EPTivV\nV1N4paCXeO0GEQ95wsnNu0i3cSImjvW64Ev/svzrBpjDKks3wt0/hecAnvte\nMwez\r\n=J4mz\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.5": {"name": "@npmcli/arborist", "version": "2.0.5", "dependencies": {"tar": "^6.1.0", "pacote": "^11.2.1", "semver": "^7.3.4", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.0", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^9.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.0.1", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.11.0", "chalk": "^4.1.0", "eslint": "^7.9.0", "tcompare": "^3.0.4", "benchmark": "^2.1.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "d4c4e0d3050084ac741af124410d9831f2b3da86", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.0.5.tgz", "fileCount": 44, "integrity": "sha512-hUXn8XRChDG2Af4NpfPQpMiVbb0/IfhONdX1f1bcxjPXXKV54DMshU25tItcnKIeT5iKF1fqebQg8F3xHb5pCw==", "signatures": [{"sig": "MEQCIGOvYbfCC/XzY+KJidpaNXJV0LYPzkQeY/6jV/RcYGqBAiB+81q5dEgPMBvDedJlvoqpXWXlZvsjcmPelo6tzITEdg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 306619, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgALkUCRA9TVsSAnZWagAAldkP/jPH8vMb32x71XUjmGY1\ncaYU6DtPC0EXtdcLRcQiKaEyYJhLQMIoWsbxA7YOcf7X/pkNYF3Ba5XCz/86\n71nrKsB3twp3qQLSGiz3wUgVYUAjILQWRvx1da+l7dyoQ6IVHWJk/6Bjh/0r\n5q7EJcNB2ubBSLtNgMgUZ85dJdmFTk/9SDIKClP8Hz9zD2PDWXOiTno4n1z5\n3LA8Ho83pEZf/I8sSAMkPfeWL5i0oDwcE0Jiw+dS/6/TyfO+6cZxNVPE7q9v\nPpoFTeFXWKBKXEaS/Hi3naOtb2jYqXz/LHWEWkVVzaRg+iyCbjpDO/xX1kt6\n5k7q40rTVM27Z8AjJSBlpL8cpcy8xRatrG3ZEV+eTlMlazLzijWhkZZrXeYU\nKtoXmJenZz5FCO9ulBfy6S9LygHvZ517Ky4uXuusFxCtZa6HOYx04mQwxD32\nXwj6OGLMoVHgg6hfnLjiTtdlGhMGu25YIfDegsErMQV4t6aZrzsQa0giTyT2\n4IGsY/A++eYfmDC9QCsgiswwmRS0UegpGWBjyJkaAxsE1aGAOg8AJJfA33Tp\n0OLQGP6vRC3xlkafNJ/0XqAVV03a1TA3Ub3yUNn27rzW4KokQhlvF+E6ESZ7\n679fGeKPtss2EcGql0VXcKHzyhzQU7qGr8HSvVlsQzmFt1i8CfdoTW5IFF6R\ngRMM\r\n=yzFy\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.6": {"name": "@npmcli/arborist", "version": "2.0.6", "dependencies": {"tar": "^6.1.0", "pacote": "^11.2.3", "semver": "^7.3.4", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.0", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^9.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.0.1", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.11.0", "chalk": "^4.1.0", "eslint": "^7.9.0", "tcompare": "^3.0.4", "benchmark": "^2.1.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "b1036209455f2048b791915a8e050a4390f99202", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.0.6.tgz", "fileCount": 44, "integrity": "sha512-3VF6rr3TlGABVZHksblQCcG+aXvsND+pdkUc7vKsKyvY5DB1b6QxXUHwJTPTZz7hKvFM5GQPewp8OxMUdMDMRQ==", "signatures": [{"sig": "MEUCIClpyGhDtNuIbghV0SIlPRoBQI+4SG+IFz+7iftb+TbbAiEAyMz3C/c5wo/Ksd4ooxSi11eMJtLHzQwBuCihvrXc+yQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 306914, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgCNKHCRA9TVsSAnZWagAA/egP/j5/5LuBusvYFxC43+D8\nIVkCYEQ2e1n6o84h80muJksCG857rY9G9zrXeYZzv0rCm+b+titPHR1liN8l\no2h5DEY7bTdfvDyuIHw/RoFKtVa4Imn3CwMtJXX7uhdPbZnkbLav+XeAnog/\nLY2Tx5nb+Vt0/nXl076Onu6katvCTYFl6UlfJZ9YAshi2JYXQfvpbgOl7V6L\ny7K6NNUEcJGAF/1J/6IhXC7Z+sO+7Pns/7HKCjJgv4cRE+wEhR9ZdLkGz1Ch\nbHMWOAbOvQlplyEcVMjVWGeqpNEu8/ZxY4UCQ6CIG6QS/Ys1DZXwmFcxK/KQ\nx//uQGM1+6w6rebs0GFnwqfzqxUIV0R5dBfvy3XkEBu3x5XNo+1CvghUEjVm\nh05fHEdNDvdv7eege0HojvnhuRqDf3tvaFy7dIV6ebNwukMmZHkOl4x27G9/\nmo3fKaDIZo2X6uuNwic8l4vhWtuOh+lOz/AiTxFITTFhRu1nvy251zXTIYxK\nMRboGHcWzRyI5s8wHAB5cXlpBs6nyPDaQKR66zqMnGuUEcdlZm3/hO5VIE+k\nUjljAWrxQUzsv2gAEtbM9loPFPFIJvEPNYfK2bnxha6Zcg3DQC2Q7/VU/jdw\nPscd2foXK5SCmxiD04F7rpCSysO9LxyxLRRy9Jn1IR/1gUGHz0zjDP4dVvjY\nhL9T\r\n=EOGT\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.1.0": {"name": "@npmcli/arborist", "version": "2.1.0", "dependencies": {"tar": "^6.1.0", "pacote": "^11.2.3", "semver": "^7.3.4", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.0", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^9.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.0.1", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.11.0", "chalk": "^4.1.0", "eslint": "^7.9.0", "tcompare": "^3.0.4", "benchmark": "^2.1.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "fa83fc952018b82fa8e1af4bfdab621b53389fc7", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.1.0.tgz", "fileCount": 47, "integrity": "sha512-ltBA6olA04/Gt1KJ2YTE5V0Bxi2U4to7psst6JFlRHBfqxE6LiHKbqqiIRXB5qmW0c+26LOR9ocH+NxKjddX8w==", "signatures": [{"sig": "MEQCIAKHd78rxYZVl5ZtkH3R+lVJqXT6XlyT4L50uo9Bkyw+AiBN7v7LsKTpBhK9tc72je5F92tfh6Tj1Q3nrrZoOdI5eg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 315630, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgEypFCRA9TVsSAnZWagAAGecP+gL1OavvjuqLllPWklxJ\n5wZGn7NfwsYGokbDrJWu1K7OBmwYRBv4O7FzuXFcNG2SmnIx2L9h3S+2pjn3\nRh9TD13oIZdBjDJo3QH0jNyfKT2z6znc9C7l4n8LMN/hpCRCC0sL8ozSPncV\n7Rm+EUmnxflmyIVSq+ALC4eE/5oobBSgPs8WmeOBFTBlz2XMNkHSdmpTWSn8\n+78QKl4/ptvK3U4f3gcYK1dT6B1vyOeTYKDKPs2cqKgEX3w313BxEs/tnqCz\nJrrTyH6usuuGOiKfPIp3vLKOGYAhFGsnR8PVLEv68rQfn90qk/YntrPOdOk6\nI0SVRAv07mqmSYwbCLaOy23e9Eg9TkHq5P6gahdDVNctBJGHCUXVrF0jnk1c\nvWfBeAinp+AKFlOQdMnYylUbOgf609Gw+bZhLUtnUCbk1tKAKQc3bGZeOqaO\niQZYyIEGE54LUrsUbckBlQD07eC2Eb8TPCKLh/Tnuo13UySuEV+dPTxLi19s\nKP5Qc+DSYNVqdR/j83StMf/lr54gHC9MZegVDzcIOiauLayoLuZyz2S7u/Vs\nrN5gMbAZCPL0iE55vR0j8mXXYgwgz4axnOR0YHNDA1Nt8NoHgHeVF7R/0rgd\n6a7dFmlzV5wiavC5Dv1GCcpZUuokyv/UGmzsDyzptZx8KWU6eDoFnQAcoqfy\nyt2Y\r\n=4lOO\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.1.1": {"name": "@npmcli/arborist", "version": "2.1.1", "dependencies": {"tar": "^6.1.0", "pacote": "^11.2.4", "semver": "^7.3.4", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.0", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^9.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.0.1", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.11.0", "chalk": "^4.1.0", "eslint": "^7.9.0", "tcompare": "^3.0.4", "benchmark": "^2.1.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "dist": {"shasum": "e0ae0ea657662b8b21406528e41545f2d4386fcb", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.1.1.tgz", "fileCount": 47, "integrity": "sha512-zt+dabNvSuhQMlmJL4H0YV4mGujylxgxeXPWSSjMjMoZI3laniHUB+oGOhJi/k68FVoZ/o/Aevi4rWDClfm5ZQ==", "signatures": [{"sig": "MEYCIQCafUZ7FxlKOhazRiB/TPVhYcdok3oyoV5nV+YsRsSpgAIhAO2ntL7BEofc9Owo0ri5QqO7vFZdomftNn2d/L+txDB9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 315998, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGFnNCRA9TVsSAnZWagAACuMP/3keJOoMoAD2yK6qpECA\nwVYF4uDBxMLxWLn+0uV05IayF+zHOdyhYyrCeh3kqiyxucUmxdpC5uznO3vM\nisuQ+3W/ccCy1HpDODrZq7567T0ur/IDY/5tI7vArmSebBbV88KZKsTOs6tP\nROu17+csym911RcsfzdGxP4h4iNIc4AWISbGLQxcy+wZl/c+Dln9OZj3LtFG\n81JQdo3wYXoV5CBdd7gItIAfLMxEJnvsFu0AkLcfJC+7XpxLTytDI8lIQI+2\n2AM7HzVTUDkPBa2/5vluQDW5F2ZmQH9Pnql6ClvuzUFqOGPPJ+riMugq2BMH\nMnUikFIpHy0+0cusrq3i7U5AG+o5x8jniJenU0Az8A4m5q7nLFr6y2rnoxqv\nAEYpyRyaKz5D+Xyzdys+FRX4kG1ndvbwwTsK/Vk/45TVMkQ32/F7HgOeZkeC\nXofS9FTEkjh/Ug/pPdQaWF13ZBVoZ0PqOKmATBBBO4p5vprPB6m6PhS/uu6i\nMDnakFVjsM4TZUUIU7x26CShwN/iOLy2rsj4oIRe0IpgJE2PGm2mveVcQUDk\nH55QvpA8gC6N3N1TLgbACVdvId0j1eBnxuXGhwOgFnA1F6birNepMt1Bl1pB\nf7E5nUfPnV7a+PfFfcqzI2KWH7GbaCpYAf+KB1GgmRUlKaydIrsMzb7Ydb0q\nstKD\r\n=CbIK\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.2.0": {"name": "@npmcli/arborist", "version": "2.2.0", "dependencies": {"tar": "^6.1.0", "pacote": "^11.2.5", "semver": "^7.3.4", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.0", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.1", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^9.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.1", "read-package-json-fast": "^1.2.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.0.1", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.5"}, "devDependencies": {"tap": "^14.11.0", "chalk": "^4.1.0", "eslint": "^7.9.0", "tcompare": "^3.0.4", "benchmark": "^2.1.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "4cd64abd0d6993382631c4064a8bef2c6c680232", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.2.0.tgz", "fileCount": 60, "integrity": "sha512-bnQccUyKUz6Id7GgMnQiTA4E4U6LK5FolkWtVahk29JXiJYXWrRDItnjvcBbzjGAG9mAEK3LxsO3oWDvGVjw0A==", "signatures": [{"sig": "MEUCIQCjg7qLbUNIJuqIT/iJEouosMhMbEZxoHT7iEXdB2xKhAIgD/yYiOAEzP9wEQxSu6KT2mz1xZHrSuzSewbsTcf/XQI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 330464, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgHH9zCRA9TVsSAnZWagAA6FQP/01ZMC9VmO4EcYpxpRJd\nRo2fVSYc0QbJPcGT9ZVnoev8AJzy5epyr9WkKGpOjEmcLE/wFLWLIByMs1VE\n3Gk6Tg2v8yRZni3aBJC9+BUWxI049f4D9f1gGriWpaEVe/4I1x4LVvnN8idR\nKzak6g7S8s2p+peIuU4tAQDzo16Bvp2af1baVRAugi9iYRm0CJrU7xjFWE59\nekFfDmKmJbqD8ngpnPoa8N0m036662luDUIB0LnwpCAW4JLUzFNs3g6V1Al4\nvNaljkjIaBkseq6kTKRzBVkO/zdiLEwz3f8Yc+n1mJJmD6b8X2vQgcXR+amn\nh4imkoEZNAnyphOmBOLgky5D5fKD9ibIxNB5Snn3LOUG4jtzGwaJz9zCmlfU\nravEFTFmbl3DlKFNUnVASD6nTIHL7WQUmVwENCAxChmuqmZsFl8QSPVOibK6\nQ06X9mQmGC9TZpTZYlP5wzRdwFHyVcd9WtJ/2XyJrBCudwrwXWLkpq+0Mt9F\nSB0YpCzkzVllk3FfVkbTXg29M79KxHW/o2KXmar3+zcAB5OYCRYy9Ku3FL9R\n6gZ9W1HFiTDSsgt33tCRzdiIYzWpmppC1eprjezsdDBndRmHiO/P9Z2VUV1H\n7TDfpLmocLuNQ55VvXK2OpOdIRXXS1k31vSVTKQJIFEJ4L5B7koRvvrxtX6m\no7h3\r\n=fGsW\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.2.1": {"name": "@npmcli/arborist", "version": "2.2.1", "dependencies": {"tar": "^6.1.0", "pacote": "^11.2.6", "semver": "^7.3.4", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.0", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^9.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.2", "read-package-json-fast": "^2.0.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.0.1", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.6"}, "devDependencies": {"tap": "^14.11.0", "chalk": "^4.1.0", "eslint": "^7.9.0", "tcompare": "^3.0.4", "benchmark": "^2.1.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "5fbf3b697f3315be124e3477c17793ad9a68828a", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.2.1.tgz", "fileCount": 60, "integrity": "sha512-J76nY+TYhxNLFAnWy1HqfjszC6dHy5zxHHFt1LJ2pgBDcb00ipNAbTX0qtyv6FPTF67hPErmPKePaKtFr5KvEA==", "signatures": [{"sig": "MEUCIQDdEOqDQKal4m9dBUCpZVqNIiL3vcuRP1hM6N57Iok8BgIgRsgx4ZFY7kpEBZ3gILGrKVpuLau8TmarmfH75YdhuW0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 330532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgHXsHCRA9TVsSAnZWagAA0HoP/3dMmobGsZMLy8hVZy/w\nillpCjve4hAh0X/FP/yawG726YYX0Y4t577jRdvupNnhTspWmR8t2e9pI6tf\nuIObehvjGGq6Gft33ycrfIHHz4dvkXp0shWnD/d9992rtVwgL3dLL0RR09NM\nJ9odsIFAxsP8JgJpd1e7JvJZK/E6LuJny/qGniMdoj973ToOJiaVwtgnvR/O\nuGm/Njq7j1UeSdA6YUbYkpapNFNfBlaRaPnZP6ktwJ1mvxAEXX4TdzFFdSU9\n71rciV+326kDBCiU+2X/+gwtuONxBcZeu8zXmus3sKPrq7I3KPcY0IPcsYRi\nG7UguRDOa8QbNlLRcLy5AOPzG9l+fWsjGMLsk8DkApwB24PkHRgVBQmlGZMw\nvI5t6h/4N7m4XiaYgJeA5fM4reK9c3/KW+B5JV1uBYlnJ1aHS4gqvDk+kxU4\nHPbnlcGhwrEE7e9hXk5MorEmefxIGh0rzuouhbskGNHA6li5bd69dMTqLfG9\n/g3ofdhaewrP9Llnw/t9bZtwrdfaDjhgW7B0WR4SYbW/X7inlsGoAXeOVcBv\ngdVgx3uCbSjYZXv1ojnHPF/lKrFqvfnrXYpMVdwjaS3nJXi4KR51ahUs78aW\n5xUbw8t9kqDYlv3da2isJZ96JqMkaQTmAVdepPEPle5AM+vIFFjxLwbZHFDx\nnJc0\r\n=W3vZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.2.2": {"name": "@npmcli/arborist", "version": "2.2.2", "dependencies": {"tar": "^6.1.0", "pacote": "^11.2.6", "semver": "^7.3.4", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.0", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^9.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.2", "read-package-json-fast": "^2.0.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.0.1", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.6"}, "devDependencies": {"tap": "^14.11.0", "chalk": "^4.1.0", "eslint": "^7.9.0", "tcompare": "^3.0.4", "benchmark": "^2.1.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "e059847cf0d65579a5b3fdf0eae9d39d458e2593", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.2.2.tgz", "fileCount": 60, "integrity": "sha512-X6sl303t4UQUD42JKqgicOG1kEUoncu1x8IH4s3YUq/m3ALIMFAsorJ8DNa8RDVbjOvJ+aB9X9Aif/pB1xQLog==", "signatures": [{"sig": "MEYCIQDjBJ29413lHIpMuOxaqH/8Gwj10eGHDck3VadYNSD6UwIhAM0cqgnnAA9jskTFfZWCY5m2WLnP0wLTxE2OST4z0i+/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 331233, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgJrOYCRA9TVsSAnZWagAACMgP/jjWWwIrXQU9NQ4V1+L8\nYSAbTeJrgFuv1hdHG3AAz+x09+NwmzfqmLYxwNHsvO2ClKkiTR6Wv4W5Dp1W\nFQw0pW6Y7HZ1SlGRBjWj7bGvk33QBclMx3vBKt4CyWwF3K877jQ5Yf5F8Q8G\nAa+ESr58AOR9pjWtkkI7s1otANBAlm1z02BKQFGf+syqyw0Cl8QN9Lnl1sBe\nCwUTZboDXQvzYZHwCWipkTJQucQ7O7q/5vtffTz7LUqPEWMT2XKs7khZUfUJ\n8yVAzTKtNCJJ1o9nd1Y9+eqgM9SWEdgUevq9Oo8G87CBVC4ogTmelLixGi7Z\n87RxiUj8VbyaODfY4z+rnH10ZDQHQb47ml3RVVh8quV5MtZu7NyRubg+YR0J\nla/iYDtDlUzXg0SGKVbNJeoRVoLc7dHU/q2X28EAtD2mCs51G4ww9FkOHEQ0\nhQ2fP1hsFBqRT8SZvb6ZiOO8Pf7oCOQGrBujQx7HYs1BEdeZJV2JeWP9s6SZ\nwF0FOF/Ld6qhd3R1zMaCBBfFM7Fwn4rtxWj5zXOH573RjC/CaXXGYE1fJfRD\nOYBvHsm0sU/e4Fn2FmRMw7unrtst0KkzaJLqDOWctRayDVtDKZzfkgT77i+X\nx0VptTZReEA3J9sqx6Oosxu4WYp5kX03ygeRaXO2Bb/wpDLwqsgw35Ma7G0W\nB2dr\r\n=GSMp\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.2.3": {"name": "@npmcli/arborist", "version": "2.2.3", "dependencies": {"tar": "^6.1.0", "pacote": "^11.2.6", "semver": "^7.3.4", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.0", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^9.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.2", "read-package-json-fast": "^2.0.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.6"}, "devDependencies": {"tap": "^14.11.0", "chalk": "^4.1.0", "eslint": "^7.9.0", "tcompare": "^3.0.4", "benchmark": "^2.1.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "fb5777be5bf143a466f2a62ceb13c85ce40b3bea", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.2.3.tgz", "fileCount": 60, "integrity": "sha512-K7yWh9uQZ87o8ktcsr+5ummcshP6Jsif05T4F7j1jA8WNCN6CP6I/1ePEsi1fTjCwKF/TAYn2gLX719LbW7gCA==", "signatures": [{"sig": "MEUCIQD6P5u75Guv/xH4OdI/q5YUXuyUvdr+HnnzspgAPkFV4QIgfbs7NxnC0iqTvRAydBWGImznS7xEsEOUhujavhwFyMI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 333438, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLvOACRA9TVsSAnZWagAAa3YP/3otMvJ0Oda170NhWwjg\nJUtrfjBTDvD83fvE2X4TH/b3TlmGuc/bnvJTNEH3gZbvqWh/hmxltUbh2w9a\nYfo9H10lS8QIJH6ccyb8Nf1PtEJL58SWGGYrtbhUSgwb3CHqXs7yHqH0QRMz\narDodPAITcbfmx3ejlWkqEg8tZIzwk5sGNOU+3xe5s6leadTo6EXu1Xktqux\nFpOPZnWoiKtn2Lng7vq/qifQ4xh0bKjGqwJL0rc8bGEVoadRwJ8e4I8imDf1\nMv6HxgG52HuYImiPsCks53Ynd2z/RlmFRJYgzZCtg0LrbK+OLqd5kj/0Lx8g\nT5l6Ar1o6yV/VQ2OPZXtpKRE1t2IVdQHDQ45qbeaeuOoyRL6Abj+LwxCMcoG\nY3z8Q7P2ttQi5Y64xY/SKM5/bYrCORY1L3dTXl4yX6ouZhMWgyaCZBIp3w9Q\nipkh/9EDntZ6jXzYI/mHu0Bom/NWRmYrDx1ToG9Ku1nnO8USQLzFjoaqbirr\nCxWu/lguX76027DwT4JPrcslT9fplvzXtl5M96gMz6pvmCP0Aify7HUjHIdJ\nqzk9MKTNB1qdUWY5C0IbEKVwYJ1+qgU1USGPm2EzCknixhBv9Nt+ShbixtDq\nz/ItOVWeavsQUnIUvrD1TdcoNGzsPq2zrvZVwB1kNxgITnV7wayzySQ+bwsW\nBi7j\r\n=2obP\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.2.4": {"name": "@npmcli/arborist", "version": "2.2.4", "dependencies": {"tar": "^6.1.0", "pacote": "^11.2.6", "semver": "^7.3.4", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.0", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^9.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.2", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.6"}, "devDependencies": {"tap": "^14.11.0", "chalk": "^4.1.0", "eslint": "^7.9.0", "tcompare": "^3.0.4", "benchmark": "^2.1.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "f2f5974b35164dedc944f3e3ee15361ebaf8d3d7", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.2.4.tgz", "fileCount": 60, "integrity": "sha512-JajrhRKOgHe/s3kC5OKc6j5hKv2C6dDLMXx0lDP3jU70rUyHV0w0bqUTYeJAcu1y+eyA4szj6/PUcIQUsQmCVQ==", "signatures": [{"sig": "MEUCIQCHgXJN0wqo4GPUCTNWsO2q/BCD8w9S7l//7+6pmJdodwIgXzwXEeUOmZ7DnGGDsuEQjRAR58kJEjKrTzMwjQ1MHhc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 333893, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNBNrCRA9TVsSAnZWagAAoKsP/jrpqqP17uDZaFdhhEat\nL/gdp7fM7+mfOWrOuFVv6TKKLMy+04JbTi5sxbRQRJ87Opgo/cyL9cTl/yWE\nFOK1O2OhU80Ps9Hceb8zldRNl5CrOvIQtA+7WSnDksx5i0oDSM2yyVjZGpqn\nVFHMHuSHTXtQkg7lxOHlB35+4ASY394ZbigLGaMn0T9DrMG3FgujF3Wb0eba\n9NTulZQRW5bwOS65DbxV7tQ0D9IkopGQj1SRNK43tUP5MIp9PYgA+f06QqSg\n3/8fQv0YNrrpfDBgNwX2xaaOMRDeqpxry+NgptMIKIPVqA2PeueF7anEEk6N\nra2CE/L8L/RJzM2IAzOe0AE8zmcEvW7rXkBhjUSpvsn5TsPwJR2iW2Z+TWFa\nunjPh/GE0LwiP/i98AGw6SJNiKO+XjKw2PZyNu093RKqOaImekglJ/viV1fM\nQsntzpd3AxSSmPPADsBuPHRFFLn0tcUNhmuag6DSqn6GaRzSoeIquWN6PUGv\nlEdihKfYgB0U3AN2SPNvDCebfjyIvHkF+LR3xx9yAKU2NkD3YAzD0HKWsPwu\nZPjdhIwUZeiLkcq79tCe/44J/A7C8BO8bPVOaKjvy2WPR/6okBxAZJ0Oitir\nyu8WUSLmKO3DFvq0skrTy5gsKa/pIbjdWDcyeJNcmJXRk9GPICxoXJ50nRxd\nqz5Q\r\n=Ns3T\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.2.5": {"name": "@npmcli/arborist", "version": "2.2.5", "dependencies": {"tar": "^6.1.0", "pacote": "^11.2.6", "semver": "^7.3.4", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.0", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^9.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.2", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^14.11.0", "chalk": "^4.1.0", "eslint": "^7.9.0", "tcompare": "^3.0.4", "benchmark": "^2.1.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "e1bacf20d168a74112a9d7fab56b34cb8099161b", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.2.5.tgz", "fileCount": 60, "integrity": "sha512-nLnhRZsUa1kPryyI0N6hLGX6lsQTFDqBJRTNHmZNmjgzP7ZBKiqz8y6ItsouT2CpWhvmoIpnstLyoglIQyo0YQ==", "signatures": [{"sig": "MEUCIQDeNt2vrhD1Ofop8OGgcaROzKkE1OwMtdHNFGyM0xHNYgIgXEtQGiR3sFnLBgXRGY/40ebJVQVCAbEf3CLiHE+kLSI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 334747, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgN9uHCRA9TVsSAnZWagAA9xAP/0cSGPzPdLeT8U/+yUqn\niPvTL9OqN+d7FGJEXsoGCyVwrdDDu1sQU9SrEObXBucrGbKSHDDnwMSCQacH\nOlFWA3n9vRZBq2PwTsDuUg0ZIsdD1DYRnvQM25DAU/FjJqC5twQdNedRnbYe\nsU38Qs+0I59SParY+/Pd9iaElvW45xbfDoStxyebwT57PlYvH2Bkos01j/Lr\nB92tgQ8eAW6Jlgc+qkpNNqW4h2Mon/ww6I48tpO4nUTRJ9Ss9DPApHZgQZMK\nSTb6+d7qjeK66k3grHUnmc+zP/TPqy3f5J3OhHvGgR0l3gimgQ/z/QYcwPMk\nYI5RH4GdlYuihOPFM8I0hIpjwwngbbirw+cMuh0iaLktYXdN4fW0opJDQw5C\njw4uS/ijlZV1sVFHPw+kPsx9U2DCQZeYFZcmh5+xh9eY3dFdmOvivT7Ph7we\n15CpPJXeg0Hz2byaXnxyBZxJUab5CDRmnjaIXx8b8uL1JqiKJ5+OQylOJOCi\nugC9YPg5SqGDYOsMA/YaMeiLRldilyxaC9zASIQ/Qy++Ns6a9Di5DSKwtVk8\naZ8OacgFLTWYVXm0Ro3XPWsywTYlua1rhc83F6Uh3Fey2/8E50TcUetSvfl7\nwZhQ6myW0vs9mKLzq7HaPAjoYnFTOQaT1rNVe4qENNwBL6d8Ls7wpcZbsLMn\ncZRO\r\n=qycM\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.2.6": {"name": "@npmcli/arborist", "version": "2.2.6", "dependencies": {"tar": "^6.1.0", "pacote": "^11.2.6", "semver": "^7.3.4", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.0", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^9.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.2", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^14.11.0", "chalk": "^4.1.0", "eslint": "^7.9.0", "tcompare": "^3.0.4", "benchmark": "^2.1.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "3cf7185953da413da92d6f05ab302d97be624687", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.2.6.tgz", "fileCount": 60, "integrity": "sha512-dLWPjMeUTrlIJG+f4j3cOMZ24vJ1GQmZm7QixXeOnx3XCo7reWqzjDZfQvPE21FPUuz25EGoRw+MSHzc7OlxAA==", "signatures": [{"sig": "MEUCIBnHWhNkiUZrnXUXPfMlNnZSt8rGFOT4NVPTMiuQbszkAiEA/zySU28WhS1lyluljcl1IULxonGEXDyxZk8zGnBIenk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 334971, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgPX/iCRA9TVsSAnZWagAAKvEQAJ/YqQX7R2wqUXXRsTte\nl241L3fcmV6KBkYV1jcxgg3KlfPaCcs4I9McCNAiJIMolyZxipHLwzcDeH0s\nrfr5sYnWKU0clGzuqYw89BB098vxcMxra8Whehhwe0NrPO4iblFXRT9hj6JI\n1fYPWRDNvX3vKzWuRvuVk8XEPRfxbHiR5aZZ/s2tAX1XUaw/LsIAUtQBCMNx\n5V+2mRe2RYqmMppU/I8Sl2AIzNY93Sol7TSynLw/riyU+N1E188vm3xfaJYv\noy7LCjzn0TsI8uPNW5G9JgRAVvEBB4+AV5SGKbCY7QguCgUpgRiRhJN7t4xI\nHV8AH6wQtjPR7Hj6lAbc5/IIQi5Ul0HHdp3tFPLj73nhnOyZ3z6RgLnq6EWA\nQDewrX9yxlo7sC1e424apLCO0Q+6h+dM24QHkquwcm21N+QRPbxpOuuBChUG\nPeCWaSwH1XG99xgM/SUm2H7zmVwA7t3KFr/5gGy+xV0sNuzxYrf1hXmzGuKv\nsat7SeD+teyZpylmIIkLA7hlxCvwOKFasQri2r5gxtx/epaGqRVCGKP8OiaT\nnxY3YOv/5sn9+bo57ecvqqP65YGX9IDWxoYn1qEU6svJar8qbV661RBq6V5N\nLoIMfa2Qi7msuTxfz8kXr7cHIN6j33eZw7/9Bj3MiHS3fDmgH523n90NY4il\n/Ipo\r\n=3d8F\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.2.7": {"name": "@npmcli/arborist", "version": "2.2.7", "dependencies": {"tar": "^6.1.0", "pacote": "^11.2.6", "semver": "^7.3.4", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.0", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^9.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.2", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^14.11.0", "chalk": "^4.1.0", "eslint": "^7.9.0", "tcompare": "^3.0.4", "benchmark": "^2.1.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "05feb1da148da7d5aeda84c25b7282ef87e2830c", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.2.7.tgz", "fileCount": 60, "integrity": "sha512-NulX/tVu45PIXO4DSNpVQkLrN94OjzAUxgLQ7Vsdb1macSmklJwQF0+4jBgC2riMpdWJP+IiFsxg1k9T9RoRRg==", "signatures": [{"sig": "MEYCIQDRf9+1klgM2oJMmEnDM3R7GQ2go3WMQVDbe0yFXekmLAIhALfwTPsfOmBGDbc3jXDJTO/3Y+gCK8+BPqt20krfy9gb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 336077, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgR7poCRA9TVsSAnZWagAANKIP/1iB37Uu6Q+k+iL2fWwI\nFOvcnISBgArVWr+vF1M44Y2Tx8iP8JzyRBRpXI9RIkBdZOEZVLhffOw5Lq2+\n10iGP6QNUJ+EKsGy+g6sEuQeN2uhfBYPar5rINrxWijIOqyq+taWeSk53i0F\nix3RsZ4OZSDIueN35v6R7Op7cQAOa3P5cFqmEPuzUC6gfLOvexW+hhpNsv/k\nWOx0OUQyFHXJVZAwU0Xr0k3OWKFITYwjKDw8we/N1fBNXaa9Qno9e1DP+Djr\nLCoQd7/7HAS49X2/sbfI1x6AEeddrF59x/5o0qybf7mTTCJiqz9dwdMezhFb\npmhzs7rpCUJ4nwkdL8R4UdCY2DNAWfu7wMJVUylBhmE5/iyXnN9juvWPEV8W\nzvx/4CQUUwnBUw5io1gVUUzDB25dmW78x5ZyRm3FLZM9LFGOFjR4RSFzMtGj\nvgt46e7EBEEfxAF7UlP1VxYdcp7ivEWJMu//amMPuB80SZcB85tzYKDiI10Q\ncu6NUUm+fyopWCE9KeH2SHp4tjOCF2HQMxa91WDGerSFdhgO8fSkq3NoHyJL\n00eVWyQqREIqaLN7c7cHo7XZVjnFWZtyRFeOAqW6nTZ3Pv9Bk7OkVWeic7YY\nF7D8ZKN3EbPJVe4HwvJLuf5z4wbzf8yYg8L7oxDXblOOPqPu7X8A7I/+sjRF\n8tb9\r\n=i6mG\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.2.8": {"name": "@npmcli/arborist", "version": "2.2.8", "dependencies": {"tar": "^6.1.0", "pacote": "^11.2.6", "semver": "^7.3.4", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.0", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^9.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.2", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^14.11.0", "chalk": "^4.1.0", "eslint": "^7.9.0", "tcompare": "^3.0.4", "benchmark": "^2.1.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "914252e23ebc25316d7b6565a8050f00d739c461", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.2.8.tgz", "fileCount": 60, "integrity": "sha512-Wct6W0oXYqc0SU3ad2zr3xIZ0+mOcBRO/hO4JpuYalKIwha+X6es8pj7iexZKLU7ichBSdkEqo+3dqeJg1+qVQ==", "signatures": [{"sig": "MEUCIHaugnE05tAX+XQqkUsif0w/uOUbrfTTzBRuk70pWplXAiEA17mOTOEhVTC5WyEz2A/CMbp2vVwBt5IwqVFaPOXJELc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 337285, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgSnkxCRA9TVsSAnZWagAAVQ0P/jyaM1SUrfoR9QQyAeJf\n7hkvVbNLLoEuow4FMENGf0e0sd0Kqa9ZZxr/eb9ndvUUhx9XPAzHwOqMz54T\nTc7q0q4fnyqLNK3lm0efPXVlGiuKVbcqIxn2sF8X3WIfF0VjrHTPmslO+Ni6\nMV8iXi/amhRQaOlRUcqIKh74uDY+D+in1rA/om8+BW28mhmZknFwXrgd7G15\nSiLRY67Inwb5CiPI3oM3atbKoraPlrsHE+9sACrU6y4HY/O0kV4v7zrqfSlw\nBHs5oVTaX4hHkanvcA3vGkg7TEdZJBA+sWoTZz+kexH9XSag4l0mbF7F+i54\nZ+YScAykwk3tK64rTj9lHj/afCW3/0h67tcI2miPTeaM7j/32AmRLk4bPMQ9\nXn2knVu012GoZkW+7JL0HLHkHoR0CECA8c3q/zxjaHOgkUQdklAbVV1zFpf7\n+EIJ+7SPuwzW8TK9ZKce1+QMFK+qoPIpAPfeVlArUBYAE1Hel325qKOzyb8s\n4uyEWBcL2NMu412V9HEPwsUIxmTjNni8moDnTDRNOboQdVk17GLPIlDqlDEd\nwhOI9siLzuWzTjissxNID3Cpm1u6UP4IMx/orIXntU6l6Bq5m0Y/M1RMWisT\nq/zwzplziarHOWYn7rh4NdrdQ3JxPbn2TaeGQOgEu/Tn6bYVsOaqmVl9HJYd\nLRng\r\n=Wz1M\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.2.9": {"name": "@npmcli/arborist", "version": "2.2.9", "dependencies": {"tar": "^6.1.0", "pacote": "^11.2.6", "semver": "^7.3.5", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.0", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^9.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.1", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.2", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^14.11.0", "chalk": "^4.1.0", "eslint": "^7.9.0", "tcompare": "^3.0.4", "benchmark": "^2.1.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "4e8b1d48c348994a5d35fa67b4e5a6f3f8c7018b", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.2.9.tgz", "fileCount": 60, "integrity": "sha512-ddC/CCAEHh28XYtgSAOudchdphNXcgErdYxwsEiykc2YbRA9Z+4XjI0BdBdXvv22DvkpO7zotUSxlVTcJmdURw==", "signatures": [{"sig": "MEUCIA6mB8diyeVV/M3/yGOmH3XjluxqqxCVMW3Kg2ftGP00AiEA9bg7oeP5jEXaMzIjC0QoiauI99B+PjVR+5ufEjsMxKY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 337353, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWhLgCRA9TVsSAnZWagAABTAQAIT7N5mAHkT6MhyoS1Jx\nSISYfpbS1e1c9NDSnSD9UDXDxBX30NWAmrXjlF1FHEJ0+0AT1B4N8lftZwHG\nXoTKxaCRI3aZy6FwNkOpQBgKd9WQQH3ud5F6PJovkporoZ7en3QjwLdMvfSa\nB/CzSAiLddu44pgLlV4VQKCmkgv/gsLshgK1E5irPtrBmJm9HFVDG+9nZ/tC\ns8hNuORTaiWtCebAa7bcQwJF0BARVrxsRrcCsyVCE8VcMFXvlLclj/of7cMK\naeNonDHky1UNUzELIHxJTEVp/7/lDYobMrHYPrO6auiVuiFiVD9OV8OsOPJN\npcYnEmixIC3CKtZLNrJtzhxjzruJkY5b+kec08svEm56gK20V9vTm/ekwrj9\nJmVYJEplBBwVhwXRUkKt3wBW5MY3StWf7inUi403ZyOFQfvNnbs1K2XAAkhC\n9fSm5Ret4NZpP5ObZEKUVCuK4+fKZ9LgtF5cYB9ViUnIMWWWlTLnmva7raiH\n/Ja94bABR9QXubdmnCjKxN1rC6gAzPg+6a4htKe0GALzS2ZU9ZDoGrp2MaCL\nuyAozbHVgDozcVpT+wZOSpjE9rnjzd136Ef/8m71vQzcMyokudsBdEv58d9T\n7TDXGWT8cqSSrVgK98YBo8S6O/jJd8CJqSIctQe8a8Az/4KRIIPScDjBWIAu\nBGqS\r\n=fk1H\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.3.0": {"name": "@npmcli/arborist", "version": "2.3.0", "dependencies": {"tar": "^6.1.0", "pacote": "^11.2.6", "semver": "^7.3.5", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.0", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^9.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.2", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.2", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^14.11.0", "chalk": "^4.1.0", "eslint": "^7.9.0", "tcompare": "^3.0.4", "benchmark": "^2.1.4", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "0d3273f85691711b10a85f82dffd235d755a3f57", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.3.0.tgz", "fileCount": 60, "integrity": "sha512-4z8x8jImp/Clwol4sgmR6qdntLQZDxNFabBSbyr9EB11cyWHyqhRvBKip/1sBTcQAScIwuFT64MOu/HI4a5Nkw==", "signatures": [{"sig": "MEUCIQCOscSksI/pcA+IcqbNiay+IYKtQpGL/rTCNDgQnM5YSAIgOIFP+WM+qZkZO2K0JaIAsYNMnsaakOCoHQeLa+96znA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 348630, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZhCSCRA9TVsSAnZWagAAvXAP/0Yun2BNkjrIQ/BVxXAE\nlJr3g5l3bLl0VPwxE7tE7RgQZ0xGddqwR3AiIgvOrlyitzv4dnDw72MVBEHC\nYFIfN06QXiSV+WDKhPSU+G+XaK2mLGQTxs6IihDWiNcKvlQssokuD3ecsVrR\nbM/f+NtRhXK9M0CJhS66kzKTkn5r5qVPrMVo8j2P6fbov+xMSPNuiFBZ7rA8\naNprrJXemSor7MEKgix6vid5WMfsCmfTKr36qv036gTnILbRPk32SFUWdoor\nUOBUdcC0AQWSEmK4dX7FnatupJbDSJoKKhUOGhi6gTkRsimGV6xZ8+PomzZK\nqErAtOBM6wlqIXGrYM5MknWqJxsbfmlklzzglA7wxbHZeyV+pCLDv0gnKWK9\nQN4vm/53fVvQr3NnzzxYiRLRQmnL91JzLq41TNrtrjf1/H/dKvOo8+Q67U57\nu+/8NgQtvPNny159D4y6zgYU0ki1KBCoQ0QKVbmiimDH3xVDUjTu8CpWaEUT\nl9wfPNyJzkDhFajO3zUUx1Xz+6D7n5qsKV1hblipeY2RjZLYP+SgO7xWYbIb\n9cTye9MFhaDY95rNtSfS0zBZq8XIWoMyVim2si00YP1aaI2B7XSpx5Vw1wTQ\nGJEhgUrVG3Dg3BEJJGkOkOm+Q1nc4Zi9pfbz6KYIE9LnQSH7hGpcdAaYnlOV\nzJWj\r\n=cEj6\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.4.0": {"name": "@npmcli/arborist", "version": "2.4.0", "dependencies": {"tar": "^6.1.0", "pacote": "^11.2.6", "semver": "^7.3.5", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.0", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^10.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.2", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.2", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.0.4", "chalk": "^4.1.0", "eslint": "^7.9.0", "tcompare": "^3.0.4", "benchmark": "^2.1.4", "mutate-fs": "^2.1.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "34e7d7ead0140268ff7f9418b305993e8f2ce0d7", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.4.0.tgz", "fileCount": 60, "integrity": "sha512-rCoRrUSmXdBDBBgL/O0oehIR53ey99Pds8dId7gztARZmx6/NBoeiUOu9RnvXSe15XZLc3JSz9sHPcbQ9NQ53Q==", "signatures": [{"sig": "MEUCIQD9dSgQAtmHHrDWIuuTVmmnjYgVki2Olk8Ih/U88MUokgIgQVg4U7BQijhR+YaH2bcMGTdZhFEPOtP3/Qk5VQnirUo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 351005, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJggcOXCRA9TVsSAnZWagAAUesP/0fs3AKJPe4seSudA0ln\nn6I0oeV+oAZV0TZijJi2sOy38edNUulXwGjZBEjklFnsyopWYnwugMrKTKit\n1qw+3xYv6gNUY/sG5CoVaPdDEGRUPbeVOyx5u9OznJMZpO6qG9ynfDBgBB44\n+cnoyHj7RLJIHQnO3/3fDGWKperRQHawlS7O9Txob8c7RXp+m3vRSXtbi9cL\nYOKt2Kc0bd39ln3g6c5euQm61qfvQ4I0gP8rE73k8vGt6Q+qa7jjDKHarpBA\n5aRYD6NqlGVi50Y+RwERqpmRdEB7w6XF58s4wRjEhs3l2lFE2M0VNYfh52JX\nFy7c+b74jwfEj8o9BqsFlpadnP3fXGPXtMz7XgO9Joji4oL8TQVuW45PMPTV\nwzEg5I33z94q86stR/QAd9yWH172XNGN+4PV/4ODlg4WiLW8/AmRac1zMKfA\nzsQCjSdWPkxeum0ZDaoHj/wyE904luQ0yL0GuVi2ytTDaisKxZo7hJ4QGaCQ\njlTpB6idgyYK3PN/GDOLkdBUTSYZmR/OwxvHSPlymZERnTyFqg62P2z/JySL\n1d1fzz62Bv7hlWJZ9XmEG8ruKegSgVcuzhVeu/yubomU/bx/245uZnlJDlPh\nCVYqZK8Ov+PE+LL8VLATT4vqB2q1e1H8QC3636tE7ppZoi9kTBNPI9Yg9FLT\n3HJ8\r\n=FbdH\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.4.1": {"name": "@npmcli/arborist", "version": "2.4.1", "dependencies": {"tar": "^6.1.0", "pacote": "^11.2.6", "semver": "^7.3.5", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.0", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^10.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.2", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.2", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.0.4", "chalk": "^4.1.0", "eslint": "^7.9.0", "tcompare": "^3.0.4", "benchmark": "^2.1.4", "mutate-fs": "^2.1.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "e98c77f7e17924876b89907237516eb65d46005f", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.4.1.tgz", "fileCount": 60, "integrity": "sha512-LivXfK+LjtvzFjnwK6E41Pkw1C8+MYrgdXinzqpDc8MDYp7gMT0nvGvnpQd47OV2GhLRyBkbUSEcLk6P1d1s0g==", "signatures": [{"sig": "MEUCIQChiDPqPA218EfqMmH/3kONzzmwm5wUCfA8e7Rik2+ZhQIgQvSqWBykrLvWjXwRUkBzltduCv3/ymBC9nA13OW7jPU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 357549, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgiwoNCRA9TVsSAnZWagAA6VUQAIIP23jT7VHf0gogBOUw\nEr+83lVfJw3tn/UjA75/maowkkGkwAZlGtp6ZiuBAUMAoh+bf6JJmMVF7WWp\n+wTqYxoU7gmxuT5hTQ4YEztq2QbENWt/n8WqCDrSyLLghC6wM3RyVzX3kyvk\nIkoNEr6O7OL2biBDEXXwuEa8pabVPCsTblSSm7Vb35a9MKOehqQdVntb9JeJ\n2BoxVM0Wtzqp1YlX//oki8K9vuns4+ea5GJQifxoQVaY8Otvj5Ps/g68hkhx\nAGJQIDC5u9XD7ndwdRKdw1V+C+BGq6JtX4TKbhiN+3gROzDI8LZyM6TJODes\ngN42J38fUz3U+hSqT/J2Km8WYXx3FlZlsAK1LbkDZf+1dMXxsxzwAHdojYMf\nwLGHVGktYsfJNTXfclaDDA8cMqVqzsi8Oww9oSrVW+bxBtbJjMkpad4C2pw4\nnV/JPguoUh448CnSQKh2ZEs0sKcNWC+4rKBruet7I49M8hL078U6ooCU/ZOv\nvJL2Lbeg01vPQMfKTusnN+oFJmMCxVpaBALT7/TxORKiR+QkygcP7TSkTsKs\nkFziSPdWSYrt9MdRReRc9LTRfrpS2FmeVtJmjnilljyFb1JYm500awiw8f+y\ndmQaiadi+wstchznIdTcHTvHtDMIMwXbcjUjCoTP90+5FNi8EhDQ6uC15vPL\nebXT\r\n=k4Zw\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.4.2": {"name": "@npmcli/arborist", "version": "2.4.2", "dependencies": {"tar": "^6.1.0", "pacote": "^11.2.6", "semver": "^7.3.5", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.0", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^10.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.2", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.2", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.0.4", "chalk": "^4.1.0", "eslint": "^7.9.0", "tcompare": "^3.0.4", "benchmark": "^2.1.4", "mutate-fs": "^2.1.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "7c22eb0d7b66f31e250e1927047d0bd497dbdf8a", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.4.2.tgz", "fileCount": 59, "integrity": "sha512-QrsMrRWzO1D2EmPQheyPz1yRnnmln6vPe4SujV4cRF0v9qIAQbD8M0dMH6K3y+w/2X3t7vg5lx20LHXsbcu7lw==", "signatures": [{"sig": "MEQCIHareQYKcFuINb0l8/A7S/ILF1JxZt+V6PVizsbW4Z0oAiBwPilcdP6uZTCkxBaZaKTrjS8h/Ecc/fSTzc0NYcsJ6A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 360002, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJglCKrCRA9TVsSAnZWagAA5v8P/1u9vE015eVNjLln65VT\nLhIOpdyySv6mddxOWk4XCjAEZK7mJRCNlosVNWp0TYalgFu/CpwWfN+oFXN7\nU1g9rZNKnWWpb4Rkguo9l+I4VKWnkkEiRu7t1v6h/FbPeDtQZGCF8hAgXy3O\nd84zA4GNLFbAli+C0hAMIR/xjk/e5vfhIrlOyQmn1naemgi16m2ZwmYOxoA+\n4MnJBA4vr2Yed9+mNdC0/Va9QRgdUV/qsZ0lr2lr7PcT1y3dqRdohZpQ7SX6\ndYc28EyekXXjO4QdnXgl6flFDoQsmoF1sRBuw8QONvvxSezF5uBLFQt1mTUf\n9KuoKjpv024GbAfolUvQFiNYZ4HEBppJ7l2Q3/0ubsFADQu21v+Yc/iGu6h+\njDO0ZGKa5ZI964tnWs40LfRuaSwNQzR4INOVnuG5VCEuqlczTGa9aI+31xiI\nwlYa5G42pRePqjtr0gzrD3dcb054Ax7ZetFvAv2x/zSmHf1/Ny6w6i1XByGu\nl9l4jU4ScW0Apn45l98jbGtb2dI01hJBoSO4Sl6eLg/G5Uge4t6nsz1RHkf4\nzjoPDt9UYuS5cmzi7S6y42Ii5cvq5biy9+o5Xni4fze6KyS7VTmLZWIeIqrU\nehhi2hVykXLiZOaLIk8rzU4kG7U/Q1rFn+NOcSrATZL/weqI/ODRATYpCEek\nGF8Y\r\n=H61H\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.4.3": {"name": "@npmcli/arborist", "version": "2.4.3", "dependencies": {"tar": "^6.1.0", "pacote": "^11.2.6", "semver": "^7.3.5", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.0", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^10.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.2", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.0.9", "chalk": "^4.1.0", "eslint": "^7.9.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "ef821121d19edb5ba3e75cdbf0003a9ba6e4b201", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.4.3.tgz", "fileCount": 58, "integrity": "sha512-8BJKsRXfJ+Ie9QxIJjQbjGysWRQF5rTR32jKlZYYR+O//GcoYkBdlklytQg1OBSECojAAbZY8glMEmW/HhElBA==", "signatures": [{"sig": "MEUCIQC5Go2ACARfxSrNe2U/b+FzPXgTC9YIboibESQ94JbCPAIgEK9QTabsBw9O5OMIatcCXLCLbraVqZdzAIfH5LqmhjU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 359696, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgmWBjCRA9TVsSAnZWagAA/k4P/RCgtxtZzhk8wynPjsNV\nX0XdZ9mD819knGpZIH+VgsJql3kaGZzRFc6yts3mr2/FU9pVYufp25VSgZM7\nxTlBDu725Mm+IPmK20OJVtE6QeUJc3Xmm9pQHQAthWTSL8Ip6autUmdnFfBw\nswW34nizgeLSW9UD+Yijotlya7NzzunMwWmrY3X76DHHCsgXvy8vzy/PThdD\nf4wVKIo3+y/TF+JgZKW1XNJnE1nHL6d4M7z/8XiFAwtkdpvW8AaixPbvbIi0\n8hlT72BvOO3ubrqI4cMo3aEjm/jEByNFwzR1yPvU4oZfW22jx0Udw0ODklQi\n71uE48Anfzca6zS+cQzxSOtwq0GWbtSFKnp108GbfVTYogNieSgFqIWHYg2U\nc9B+zBqWI8zb9PoKk+gXJWSB6hrkOYt5tcuSUMpFnetu3migfsWsZXqSp88V\nu167CqDJWJbdwyBdJmiOYa3y1zZ4d2dXtEZTlw022QrBI6bdbeEKaa3VODaw\n4QxaY84ni66STR1aS2OOFLLuf2u6cP6ADwJsR7KDADXPemvRsCZlWRpSLeCl\nCLydE0yug90n8H5nNYAPjFifZt01qDWN9bgSBsgcC9gNbyvdlK910+egs54n\nXfoojJxngk/FQeUyoV5vWac7j0b1EVvNA4nqlZxwB5Gr5wOT2y5h9kBVst+j\nAUVH\r\n=qHh7\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.4.4": {"name": "@npmcli/arborist", "version": "2.4.4", "dependencies": {"tar": "^6.1.0", "pacote": "^11.2.6", "semver": "^7.3.5", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.0", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^10.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.2", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.0.9", "chalk": "^4.1.0", "eslint": "^7.9.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "8a5885b9d8076264aaf5547eb8c1faa95fc8e4c1", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.4.4.tgz", "fileCount": 58, "integrity": "sha512-mqZvcPCWT6gSSYxs08aKvXmECXh9fP85q1pUIY/jDkaQ58QTRy6F7XrUQr7F77jXpYfpYKPUi6RhpuSpOXCITA==", "signatures": [{"sig": "MEYCIQCpsywNBERNS10OpfuW1zvTrYQy9q6t3fUgkb2H+t47NAIhAO1rS2y50p4Q2D5PYlHBCkbjvtbI1Yzas+aXvfWi5V16", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 359779, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgmWdWCRA9TVsSAnZWagAAVe8P/2jrKaZDdPGp6HHahIPX\nTZeVMmAjx2xA1XQ/YJX64tzz6lPMpf+nbRCAvpji6RPEHdnK6o7a3/9W/lLt\neM91K4pRtgOPt7fBac1BPmp6EKXr+u5KJBuGAbDZRxqirdG6yW1CWZc4JnSd\nlUHDisxynaur0sgGYPKejth1NwYrBrO6BJlvmblSaDm2Dt09M9b1jMvtzycv\n2bQ7vVPqbj9eLq33FUciHJ7tUqNV8r4U4do3Gz3qcxWuIAFgzni+4v75B91H\nnvNJ+PiAcUD0y0tSjPJbd7kd5llLIZfFCnaLMjeFBnnEhOoNPQ+LBvinrov3\nMIM7PyaJM/CWAX4m/Cv0OMpjOGh7QZ5Zk5UUCAcB2HdPCLEpbcm7D5LIO4S7\nQ6yBK32olq5Cedw/Btp8ROHPXeCOlgvZe1I1dcq0tGIhq56hq+rD0mbaPZRx\nNm+6kVaD98MdrtBQ+q4IA3+9t+CpoFAWw6B9tZPQgfnwUtil+DRI/LA0KGTY\ni0k2P4381VV5VwGNKcRXMt+0iR7JcMVvM7tgeFpjDscs+mUEvyJndV3BcGE1\nCo50IRl10s+k3pnMBkXWh5FwSpdhw73GTOIQmQsfTuyS1l/oc2gDw4S6IQiw\nYIJwWDRyOnNroGTa9hjbVguJcgGFVa9i02OwKNh2kbq4VY0SmIXu5yLXysmd\n19X9\r\n=P4i4\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.5.0": {"name": "@npmcli/arborist", "version": "2.5.0", "dependencies": {"tar": "^6.1.0", "pacote": "^11.2.6", "semver": "^7.3.5", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.0", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^10.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.2", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.0.9", "chalk": "^4.1.0", "eslint": "^7.9.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "3a2bf74e47b4cbe20e0983291f291a2ec2c3d06f", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.5.0.tgz", "fileCount": 59, "integrity": "sha512-YPSkV/8vofpbAJyeu52J12YnC5VTkYIcfcNkRoSW6qjfQG+QybgbJtCbcdx+M0YxfdzDKS6iDTjpNMoETZ8HOA==", "signatures": [{"sig": "MEUCIQCGwOERkn+Zu+Y8TDeDIqYyF8AsolKr4eRyl2OmKGLwaQIgWHhICH7T+z9yzAt0oUHB71ZBYYYnJAOOaDDytohdnvU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 361288, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgnUy8CRA9TVsSAnZWagAAhBoP/2Ap7SbF+g2f19eCJc6G\nxbU+rB52xesWz5/CBP5vGb5BCTj4PTXP2O/PgUkSQALldnzm7i1tpYxTCZs0\nRwDbJLYafYjGRf1PjgMlc8ZedK+IPnm2b9N5iX0voXV7i5RmhXGfyUF99K6L\nZM0Um/hxL3e4rPt4kHSOupLmVZm1QWyCUY2CEUAhxedT7ztACrX9g+5YwMOq\njIoarvGeVqZkewz7mn/i8FzpUJJfuCd5YCMxhxOMYPgChnOAzqAhMLSQhP2I\nVLRa7iWg44+l6u5q6MzHPFqEmeLdgA500KKvul19bCa3IEizMvBtHZLVi2a8\ndv2mPvlHMdHtrAbJqDagMHoZyg/T2S3uDiTQWc6M8noz9NRCOLSgVwhpYlvb\nzlpb0a5mB35j+SUeQOCo7nR2M3Yxg/4pUSV84rHF1VRRPDEAiTCXB5SE+RM4\nYfkD8tF63nbrgtExkhwmzMoPKjhgLwXUl6VIC1Vk11aAfMe7r4OCy/MNvFkE\n98L+30jL9G1BejpvQuyOverVqDZH4jK8deDO+DcZmDx4D9t2NXtIClFLa3yh\ncm+piW/0cWmWzOyGZWPvmJGU9tLKtcwYzA2EjuxXQOpdJQmp927+De4YaVbf\nPWUuXFxDl2iJpxGSNBFfDwdHajD4Tdlu5MYZ8Od/5RjSG0kTLp51ofPmO4a/\nCAlC\r\n=VFo6\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.6.0": {"name": "@npmcli/arborist", "version": "2.6.0", "dependencies": {"tar": "^6.1.0", "pacote": "^11.2.6", "semver": "^7.3.5", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.0", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^10.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.2", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.0.9", "chalk": "^4.1.0", "eslint": "^7.9.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "8006d1a60435199db6ee72d4c0772869bb75557a", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.6.0.tgz", "fileCount": 60, "integrity": "sha512-6njRVuPMgGRvQUmsXwGdp1ItZtJuSdt5ouoQe4AeFTTZoMufKWLeXFDOlWj7qbMAzqw+guNEAZwBiwm04J7T2g==", "signatures": [{"sig": "MEUCIBmVYeIqfO8go3pDhaD9CeQ+NB2zl2NkNr2MBabdCiROAiEA/KN6dRALON/+mLkvXRCULEk8DbstIR6k/WJ6MdRuPuM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 365232, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgpXz3CRA9TVsSAnZWagAALOAP/1tqL0bZ/cHLlsulocS8\nvp5oVQPFCvW7tPNjRRFP31P12WrBNZnnFY0RUB11e2CeENMNCZIBUXYwpWUe\ndi3rtGdwHMCzXgt7IiMyg/ksq3qC6QKuz4CVYUYdbLHdHnDVN5y2YVRgL4AY\nM3pQfWaWtXJWdZu2EJtC6q1t0s9xH9ygRz7r47NnEE1Z/cMZzxobfQpWBSSa\nqEi8P4DBIdHW2aaejTRl8FpWNYtV7B5PJRJfcoS9EgQnUwYTBa04nXdmG9st\n1sMIS3/OVDfqfXP4xAZVR5lSBjQNx61xNNPio0OeHesvcHB7gj6posYBFTnR\nw75QxShsYlxiRe2HgVljrs0VqpESfat8Bdf5WOAmG0f/vWPnaZ7Fp0WFWAVQ\nm6wokiejhGVOdoG7S89EY67rbfjO6lJMEqBTOk3d86M+j36hy2QeE+3mea72\nvGNRDrpIYrDd3MEPZE9FaOrdreGLp4X8VmMP1J8cKEsvYWkk6vqXjkV3UxLA\nS0gKGx2Dn9ffKhdL3/B+nb0+YlWIr/KQlvXv6/fMGJmsK1hMd0CaebaTby4j\nwNbkK+0HBWxIU+EOlKijfK5iEJ+LyeVdW3BI4tEG+TGDBkt8DWZgZOdBpRRI\ngf7etYxx8176xATIdj5P8YcagFjFUhKyAj54cWeMb6gEKI/UViErtpwECQT6\ntM16\r\n=klvV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "2.6.1": {"name": "@npmcli/arborist", "version": "2.6.1", "dependencies": {"tar": "^6.1.0", "pacote": "^11.2.6", "semver": "^7.3.5", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.0", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^10.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.2", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.0.9", "chalk": "^4.1.0", "eslint": "^7.9.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "bdbd1311cc857583ffc85f4d3f24a50683303dda", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.6.1.tgz", "fileCount": 60, "integrity": "sha512-OOlntFIOAo7RplEQaYXlA5U5NXE+EwZtnTCsit4Wtme5+llGiea6GBytuV8dOzdPMPlNx3fQQjBUE9E8k76yjQ==", "signatures": [{"sig": "MEYCIQDjLzcx4YyVm3LAUHer+QPNbukZ+cZ4CTTJrbtwAbNnngIhANhKlEk2DhILbvZgYuwNfIJHrtGsyQb2b0JTdwfYiJA9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 366224, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgsTjsCRA9TVsSAnZWagAAWnkQAJqb4I8OUgZpaJFwS2bo\n2zQ23U5uie/pT/I+/jJAjP0FgQgb1Nj0fYjKbZicHthXvaRBqdhrpVOkRvo5\n8kZCNvu53yziYR7UU+kWN8I8v5BsDWpp4cTHV9PLhgfl9wrh5EK0CJcGaySn\nhC63lTqHikLH6nWVMgSPc7xYG/6Eq8C2fQp4qfGJRaRAP3A0M40nq9xtCOOo\nhRkg4Lv6W3njhWrIOxeM+EkaWp4TEvvalOsAhSADCGI85fxZfIBjeABUb6U3\nmh3WmOFCVJxw4S9OkyIUUcuW4GYQ7cipkyqdLiX3cedmEZ5UV/l++pSxNroI\njcvKpU4dYHTTWMVgQRshtSImuZ+mvRz8sWg4ROJhb8+/6pDudOYrCyXyKHBj\nYPCY1Ag2zbWpqSB1AJEpD1MbW0/wPIVyKajTuzq9pFIj2SASsJ72x8F0wyEg\nAFSDrwMe7ODMzZ1ypHQkSmO6eSSZTH4h7zvZgbRbHSBHmKAWeMiSFAmDRvZD\nY1cpSyKhGplB2Zw494suvamY2SoV/q25jKTmXLJyzfG9gtOYjOYDtaKytV+K\nlgneKPjDfSATadwGBRkVDCWvR177GLhpyFY4jG3zk2d2BPi0TdTixkd54glB\nUnSyQ+FaL0wYkTB/lsKWq0SjAtq5kR1ddyCfhcLC1caD7LyDWj9rEPbgFS/w\n2qLc\r\n=DsRW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "2.6.2": {"name": "@npmcli/arborist", "version": "2.6.2", "dependencies": {"tar": "^6.1.0", "pacote": "^11.2.6", "semver": "^7.3.5", "cacache": "^15.0.3", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.0", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^11.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.2", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.0.9", "chalk": "^4.1.0", "eslint": "^7.9.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "74ec5741afa6b6bf62603443793e33fc7a4f1245", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.6.2.tgz", "fileCount": 60, "integrity": "sha512-CAo0HSziRdlpGUUheERmOrADnKHfBYpLAl/HmWGwGCtWKB3BCxfgb0rJ7MsFg38wy7YF3+fDs7R9dMVCH89K/A==", "signatures": [{"sig": "MEUCIA+IP2IWMRmca9eFtomPc7rgLe1jH7pPMcAF6t7K1yDqAiEAtHvkYpXUiRCJIYXB0SNQHLpQU3/hQ0180vb4v6ow6ic=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 366224, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgt8BVCRA9TVsSAnZWagAA55MP/1ttYPi8G4OHPvGI7oO3\nDo+1/Z0K/AGsgXPs7yQKyacqdlTT+FOKsf8I2peeyYzov5UcecRUMUE9SY4G\nqrJSgblTTCxtiQLHsj96GDlJgqeCk1oofM/674VOQB6o9UCyjfHfCd3DPD9z\nhdNZdjZQX5xrjCSHRdSbaVLtbClfQHGp9/cIWztlHbLZtXxQFfN7ucFVu4Xi\nCqSzF3aW7u0Ivikhi0RaAIJh+XWctf5h3KWuStHaTnjeGDs2hmxACqoGkXQr\n5j0vnBTr+lGOTf1ctek8i4PEliZ5C0dojIVUemvsoaqS2IphYyczruKRl+RF\nyYeQQTziIuwVkV2nO45wRMk/aCUFzGTDVegkLLyKLuOs36Hkwn8SOEAP5jr0\n+7bW49AYv5lduH1Uqc0tbpNk7cA1bDgutNLkTZQySnb8uiS8Pp6hFqnBG+UX\noopSdA7bAxtYq/y277MmMRqsjpjwx/iJ2NkuM36nWteRPQwWBsahaFcsm5eZ\ndRKd21Gn67gDepNaVYSYVEmPPgprqvmEtdq5iRNEdYVvS5hv/KvoV+LJhiKm\nJ19crHDvYnvN+xEwHW1gteg/NMe1vlaAacwvusJz56SwsAuy/UutIbNP9lhe\nUfr2pEncE686v9OIq+d3AZkI48TxL2Hoa81EiolvVbOuVTdeIclTE8i0Y9ye\niK/P\r\n=885h\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "2.6.3": {"name": "@npmcli/arborist", "version": "2.6.3", "dependencies": {"tar": "^6.1.0", "pacote": "^11.2.6", "semver": "^7.3.5", "cacache": "^15.0.3", "proc-log": "^1.0.0", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.0", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^11.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^1.1.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.2", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.0.9", "chalk": "^4.1.0", "eslint": "^7.9.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "da75b0c6a6c73c24e2b848df71a45a298d9c6932", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.6.3.tgz", "fileCount": 59, "integrity": "sha512-R8U2dZ8+jeE7go+qNU4Mt6aiXyBu3mM75iRIugNCA4P0OWlsLOpuDPPhsaRcOVbtXheOGZXrqe36qP1g+M68KQ==", "signatures": [{"sig": "MEUCIG4wKwP6Wl86guW0wqBmc0mLku24zwlvCg1xp/D/i8ehAiEA+VvT/CXHxdQ2g+ArUvJu8N2OxFe34rjdysPcq6NKZ0I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 365914, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgyihxCRA9TVsSAnZWagAAsqUP/RL5j/1bBwWBPljmjuhy\ny9BrCAfg6yb+1MahqFB6BrHDHbL+YBX+6qfmw6SjeFsLoszRJCHxdX5K/UUy\nBSKZXL4NBRtodjLlVDbZQd7gFjAG4MUFA6w/DmRXwrXlNCcGGVJn6yIS4V12\n6/lIPjjAZp7DjQhmg3ssYu5tssgO68hhkzbND8o1+JoD/onDQoO3uT/MLoi4\nyw/UGNz+2s/El35ja7a2QSPKxeewtFIHwVLlTw8ol7l9qrNa2EZCQOhCFSMR\nA+PE0FJbfJLyCIbPPnqkSmCooHaAwgUZVzX90pxBzY0EbUQmYNLC2aV+Lv3m\n4/cqMtZG9eOD7e9TsTzbboSn0GSI8NB20+Vaav9xLh8ArNkRWnn952zTBSC8\n82Zs+rPCGPiE9j00ehy2z01rQoj00yWb7bJYmmM+h5jKZHeqc0KhjRGp9avv\n4lZR7SVWSxN3fA3OzsRPN0c9Wxz94/Td5hi2DYqgtLExhrZ6uXnyr/oP55yK\nR4LzNzICI7coyCJoUh4BfELm7EnVJuXs0FCZuck3A4ME1bF2mnzn5SyBuPpC\nu29LIELIu0CvR52MZQ/hYQi9t9TOzbkxp7WbjIwmxwNpcZXEONdi/qvSAwRK\n2uAbnyqry63vGvkstN3dBUDFpe/IDby4b93/GDys9xMpJDWZwbwaw9ZlKt9a\nFHSq\r\n=n8gg\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "2.6.4": {"name": "@npmcli/arborist", "version": "2.6.4", "dependencies": {"tar": "^6.1.0", "pacote": "^11.2.6", "semver": "^7.3.5", "cacache": "^15.0.3", "proc-log": "^1.0.0", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.0", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^11.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^1.1.1", "@npmcli/package-json": "^1.0.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.2", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.0.9", "chalk": "^4.1.0", "eslint": "^7.9.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "bc413ae61dd57e23b8775a77c1f3199eea60b223", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.6.4.tgz", "fileCount": 58, "integrity": "sha512-A/pDQ/VZpdxaqsQS5XOWrhrPuC+ER7HLq+4ZkEmnO2yo/USFCWEsiUPYKhfY+sWXK3pgKjN7B7CEFmAnSoAt3g==", "signatures": [{"sig": "MEUCIDiKMfdEA6Gme6u5dVIiazisrV0lPYz1bip0BGYwLWneAiEAwJHaNeiYvfMRzChJbCtnDcwIzQcN2ZqEk29iZv/rfIo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 363513, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg1PNlCRA9TVsSAnZWagAAmDEQAJW/kgKEAa4BZJ2QwDNZ\nLUomd8Xb2Z40i6RctkmC8zLebBp4fzFLbgy2LQCbyqGJjWKon93/v/NpHH2/\nQheunZHMyf0jw7ykCOzMw1vtWezZjUBLPnfL9eB+8a5dsvyPv8JgNt/QJCUM\nqZLzI9/uS67eDfTUt+MCuP9sTxpbyAP245Nwzb0bpqZf58SrBBD7gxUQxbbh\nJI8L02/lEiBryfXRwHMVm7O0zaHtDl2UtrdYA26U6sk+AreRQF4harIi1xZZ\nELIkSrkWkvQvuXbmc26tggm8lZJUigk58pfcWsCoJEl2OMrsqR7GTbLfP0Ti\ndUXH5S+bctWB6+eeuMImEaNJJFZteOz2VeUyl+RB/K6i/KS6z523M7NDV0Hg\nxRqZBz6Chc1XHlGzhLP8T7389n72VmDtDNeKTng7KWph1xkYOLj8vE4N8BdN\nUp5AjxjJ4vmSTrSGs8XMzkLvsQ3Ndyx2M3YMIVRAN+Tjb5gwRE1mmEXNO7pl\nJVwfLKI0KRNH+m6UdxAC/nlorC3IF2wOhIl2s973QIrfaGGtDrUS774VXRfZ\nMju8nGBfjjl3jNi/wmFxZEEnG1Lmg6sNv00AB3eP6L2du5W6s2WXhF8Ef2M+\n4hqXRY3g/OZXBWBGx4kGg0C5jmhW58OXafzY2Qge/dk7WC297XBEdWh206/7\nPcC8\r\n=JmtP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "2.7.0": {"name": "@npmcli/arborist", "version": "2.7.0", "dependencies": {"tar": "^6.1.0", "pacote": "^11.2.6", "semver": "^7.3.5", "cacache": "^15.0.3", "proc-log": "^1.0.0", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.0", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^11.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^1.1.1", "@npmcli/package-json": "^1.0.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.2", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.0.9", "chalk": "^4.1.0", "eslint": "^7.9.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "71e9eefcebbcd442f1e97775f53d1d0858fd8059", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.7.0.tgz", "fileCount": 58, "integrity": "sha512-wWYXW3aGdLygc5b1MGEMliVZ1fEQb8zAtz7PuIwb0gHoc1u9X3RItpIomvA4zCQsVWrlFYnQHc87aAvlD08Ekg==", "signatures": [{"sig": "MEQCIEsB5/Csypob0onTKvq19+U2Hd+m9HJuldyITL14150lAiBFJjkk9hzI2ZhXPU5voSngq8Q9g2fniJfaKO2dZyTMuw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 363895, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg7gzPCRA9TVsSAnZWagAAkgMQAJ1AffP6JY0Nd5GI0MvV\naWJXN32SufzyyLSLZC7Y7muO4rO/WUo+n0vZUnxPBdbv0BQamBB82Ta32Ysx\nLxrK9bWS6R8DxjJzWXA0pPpCqZLuNdG13rwAUIP1TwHLD5sb+CM+VRc7EObf\n/5ZFRfICXatqnHmRP/ktIX7m2Bxj4o092Xc/xGngz49gV7NdozIwOqR1rQwj\n4qb4WI1rqETZdAvgfV75m/xCkezsR+4dyzKM60GsSQCpOfvIrx33S/qlraWt\n80bl6OBpZ7UfOz15Viiuimkh8E17KOYaxpro93nLBDMUdlBpdSgZY9sIlRjY\na97jJ60uTF5IrDcujlX9DPCLtMs9fj2M+wl+l5lcMldOe5aovJs1JQtuQqsl\nj+Y2oUfOdSdTi8e4DAKYGWulChXee5LXQ+M5fS8krEhA61V3f/XnXh/b6wir\ndx9Eu2lcYM5hB09+u/nop/N8maYWEGtEmxZxqtQsmD33ws92Tn7mEQYW9dc3\nc8wBqAnl2xh4rt2aO/Fr6hpnl8JAxddAilVsmoDf0gaXfppdktb+Cb7bc34L\n7nXD+mCR1oO5bhwyge8TRE62FnVfM+eFsmvT7w+MgGArA06fimVtXQZhiec9\nGFsx2kuZtWPgOWjUJlz4o8W6ITjJozPHdI1cD8MAgcb4sSZLebLLOvcE9mHE\nlW8z\r\n=Dw6j\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "2.7.1": {"name": "@npmcli/arborist", "version": "2.7.1", "dependencies": {"tar": "^6.1.0", "ssri": "^8.0.1", "mkdirp": "^1.0.4", "pacote": "^11.2.6", "rimraf": "^3.0.2", "semver": "^7.3.5", "cacache": "^15.0.3", "proc-log": "^1.0.0", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.0", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^11.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^1.1.1", "@npmcli/package-json": "^1.0.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.2", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.0.9", "chalk": "^4.1.0", "eslint": "^7.9.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "dc7b8a75d7469c26559675adbccae26cfcbe2d01", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.7.1.tgz", "fileCount": 58, "integrity": "sha512-EGDHJs6dna/52BrStr/6aaRcMLrYxGbSjT4V3JzvoTBY9/w5i2+1KNepmsG80CAsGADdo6nuNnFwb7sDRm8ZAw==", "signatures": [{"sig": "MEQCIGYufZasLDilw98H9zvrD2/UwuO4JyPLGP39Yd8aXlEnAiAnrZcrK4y8luOIbr5tPoMC3MWU/KsD5RkZPHPq8xyePw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 363965, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg8HtWCRA9TVsSAnZWagAAbbAQAIwoeMctga1Gd83ZxQQW\n8QmG5vmFHFxOShD6kN+ue1O2xz1/la1tdStaYLEdJ1YsmrcUWb3UepuV7IXE\nXPHQZXm5HROF2hEtTvmSS2CFHcZo4wtYO9gsZEXE6fA84mRxyidpLNZvVb2p\nqhzFrEC/38FQVM47KJqUflCfFouODunm7XSN2zlynDorEFZy7DF1dnEYoxQW\nG2G/0ILpfrQLJ9PEhP+qqy2GwOskMS1YDSakkirGgrzmGvvYaEGEnU+KuzN3\n/8KHR6yxZJCXdWecsASqvzn2Nwf93q91Qg0Nmq3Z9oXO2hpo41bIAVgIOhLi\noP88nvl7DtEmcNISGUNH2y+mQs0IJVfsp1Qz7+XNuS3Jar3gdAzG3J9IVeFA\nwszY1cTOhlsF9P6z+CdtLlNkPQNMlEbROMFLe+/TJ+Cdh5zctj7b3amFqbi+\ncededDlsWJ9OnGoknfhs1KoD/3XMJfJeAwoAqy0GP0nLlO4NdfM33o8jaRYg\nA5Z+U8LnhT2tdnwcj+oPxvBQs77i9nfAExjVZg0ZJ6vemXQOWt9qYvZKmEd1\n3PvRhsroXuiuwhCsMF2MW4gQtO/PWz9XgY5IFFpmC2RsWztC3RZ5km9b81kd\nQBn0Z8dt7grElGLwuWjDWMLA6hFtXHpB5HmO8Is4ZXOxtUTMUTZRE08P3IIz\n97v5\r\n=KJ0O\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "2.8.0": {"name": "@npmcli/arborist", "version": "2.8.0", "dependencies": {"tar": "^6.1.0", "ssri": "^8.0.1", "mkdirp": "^1.0.4", "pacote": "^11.3.5", "rimraf": "^3.0.2", "semver": "^7.3.5", "cacache": "^15.0.3", "proc-log": "^1.0.0", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.5", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^11.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^1.1.1", "@npmcli/package-json": "^1.0.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.2", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.0.9", "chalk": "^4.1.0", "eslint": "^7.9.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "ff078287eba44595383eb58ad8aa8540bc8aae9e", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.8.0.tgz", "fileCount": 61, "integrity": "sha512-R9rTyak1rGdmVTyiU14dgBb+qMllY3B6I8hp7FB4xXsU9dJDrYZJR8I+191CMo5Y1941jTDCtNcXXW9TldPEFQ==", "signatures": [{"sig": "MEUCIC7fLtDVS+mmR1g8s0WlGU5tgW04iRctcAkQRcahEISZAiEA9W6ZUKooWOWjhpt1653LNacUfb3rrwY1fBCJ+pVefl0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 374850, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhAD5oCRA9TVsSAnZWagAAvt0P/RAhi2gWVoO41Xeu4cWf\n62SCr2MxPXTQksTuC8ehWQpQAC/8kdCt1SBlDnlGdcQwNYkXOzjykHpfzJNY\n9oz6fOPkzEN8pvCwBsP0lUcGc92pf4ryNl+89JiGlRRE+7euslMPGCfyTgSo\nSBwa4NDZ47TDLOccsvffhy9tLls08gD+hHAO/eFlJw+nmPCzdFm+++iFnOXn\nxjKW7wowT9H4JfgzTV7VtaJNpdZWHSq0LH+t3SsJkg2By2IQvv/TU4m+FEzK\n+h0wH8ypwWkvCTkaMnMVUPOhewxcPnt1/a3ZpzbAB/oCdlV5D9dSMw6UFinR\n/oBdhrnLe5fIMYN1b7+fVQfFMDaMU/EV/lcb+UJHD4JI+Ld//RKWZtco9/GC\naY7Qkktc5quuZ0+hAorEr4aIIdmqBDsob28dbxP0hQkUwvhHYP1TVa6e/meh\nxAPIc23OAe51lejKxOrAsYg1B0hDJardu4j9gbUtxla3Edx7KbGZOO3R4weU\n9aLOcsSU8n7Eu+lnw68pLEmqwb45tV7eZMjfmBmAgwt+jsaJw1xNgk0yzd9z\ngFoom6ucnCKMPr+CJhQQ66LI/thWP2jl7KOc4Idb2bssSoqgBAcW7i3iOPFC\nXl5KiYZux0H6jyXVdNq3LTJYGq9xMJ9N0ue6F5NAmLQiYa40ScFbrwXxdgzy\nqHeI\r\n=qx9m\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "2.8.1": {"name": "@npmcli/arborist", "version": "2.8.1", "dependencies": {"tar": "^6.1.0", "ssri": "^8.0.1", "mkdirp": "^1.0.4", "pacote": "^11.3.5", "rimraf": "^3.0.2", "semver": "^7.3.5", "cacache": "^15.0.3", "proc-log": "^1.0.0", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.5", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^11.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^1.1.1", "@npmcli/package-json": "^1.0.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.2", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.0.9", "chalk": "^4.1.0", "eslint": "^7.9.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "ee7e9128e48aba735d45d2184542dfbefd4346ff", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.8.1.tgz", "fileCount": 62, "integrity": "sha512-kbBWllN4CcdeN032Rw6b+TIsyoxWcv4YNN5gzkMCe8cCu0llwlq5P7uAD2oyL24QdmGlrlg/Yp0L1JF+HD8g9Q==", "signatures": [{"sig": "MEYCIQDUQPEj7HVhXGYDjnF7sDB7uIsgci0MJqxmGjbBIitzkAIhAKbHX+h0L7CtXswIfwix+/Bic8HXM32ElLptENcY8zSt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 376408, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhFXK2CRA9TVsSAnZWagAAmcoP/0vv8RSb7O3Clz5oUP/Q\nmfvw0nsilivjzRgeDBK+Q618DDR7/CGJAU0lgdEBqKb31M9O2kzNx6ctqgNx\nVQvilb6CFOLnztww8QglRj6XtUMcXFtPTu2vzGlJQfZG4jvMMZlUGS5vLHfr\nmkoDXylsUitbJok1Z331EbgTReWVTf4lfhyLtA9T8TCScLypJuseYMuAAE7a\nNd+yixk5wSH99vAkAsucRAIy86KqXfMaflSCDxf1HTvfP+A0NLS6LkyDHRl8\ne8tBazcZL6UuHD1GqHoRJxxtV1uhH8w5+a/G7sn7msdwJDcDUHFUpv20wnup\neE+TuE/LTyq/D1WeKuFc95W3Iu5LILmw/50mBYf26cGmzniT6NPyqDvdMddM\nTWZu0ojHkxxZiS/763Zew3ap7MK1kwZjH5WFr/hux29B96cdQq4oZYY/lbgQ\nvSlWmD78wIiV0DgStJdZ37lEAC0UWIV7tZ5pcMv3krNVBr8CNtwFXjK04SXH\n4QtdYQHti9hpSYj+3FpMvKExrN3kLhndtdqhkI46nZhxSZH6vGZUzUPaTY2K\nnbaaeEei0iF9pwnkr5R6O5OWBW6hURwAYdpgUrlEqDUtwOSAdeK4gVj1S1EP\npUOL2nFB4CAEHSeVkTyCjmjSZGkZiyzlxw+4gkKo8n8IYMgPKlZWv/9AIrxV\nRL+d\r\n=mEEu\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "2.8.2": {"name": "@npmcli/arborist", "version": "2.8.2", "dependencies": {"ssri": "^8.0.1", "mkdirp": "^1.0.4", "pacote": "^11.3.5", "rimraf": "^3.0.2", "semver": "^7.3.5", "cacache": "^15.0.3", "proc-log": "^1.0.0", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.5", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^11.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^1.1.1", "@npmcli/package-json": "^1.0.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.2", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.0.9", "chalk": "^4.1.0", "eslint": "^7.9.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "643f8c8a26ffbaa579983972f67a60cb6217e86a", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.8.2.tgz", "fileCount": 63, "integrity": "sha512-6E1XJ0YXBaI9J+25gcTF110MGNx3jv6npr4Rz1U0UAqkuVV7bbDznVJvNqi6F0p8vgrE+Smf9jDTn1DR+7uBjQ==", "signatures": [{"sig": "MEUCICzVvtHkkTfPjwK9ZUbt9YdmuNAq3cOKFNJdscq533ROAiEA4iqGDwFJyjdBd8y1FzB0pRfIp5BatfgM4GvQZV1fiOg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 381922, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhHcUZCRA9TVsSAnZWagAARqIP/2krHoA1raPy0OQS5Ry4\n7IKft0umafmFK7pUxfGAxV5pc5uK1yxoOl/V4c2hPQwFXePKKLQwUU9C/wOl\nfanwXoJpY0+jEAq6wwZvrChAThb80OApBxdwncFRIu730UeUtS/K1Dnz7KEZ\nlc0XVARfkN3XyihfLbGoVM6tSBDLWooRoM3cOxH55iicY4NBgiqiDyha/cKk\n52od4Bf7vrxwystwHRslSpFy4B6BArsr+RLHiwLRqUWdkoK5W1AEF0zoo2+X\nsmf3K7sVbBMOWMm/1QBqWiB5AMk4CE/2x8KK0OGENV5MXV1pkNc89i4FyQTL\nhoB0hhpzM0E42BXFJl4vag99oJi+vXvo5MPzuvxikabGmwgfYS9NX2P/76uC\n8zr8Gv9i5YVRpa6O/0bjc/uA31brAnhseNepQgkZu6RM29rjNxrYT73lG24o\na6WMSZsxHHsoW9eDqLUhxjk9E4yDEq9B/xwSIAF8yQopiMwCbdMKTbRZBozE\ndUkVajAPlrSU/z1A3VgaN8JmyriLUw9K5tjbFSld8LON0d85EUxvhkQJaumo\nIgeS7/im8SU86T0drKjF92jU3VMdtbx90BAPoP7rIAOU2lRc94iQMZ5NIDuL\n3zXClYSxXHb7Uf7GZ0v2BuNk/wINn13nURTWhw2UTY65pIRCWkpgfpminBkw\nbBbb\r\n=b+Wd\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "2.8.3": {"name": "@npmcli/arborist", "version": "2.8.3", "dependencies": {"ssri": "^8.0.1", "mkdirp": "^1.0.4", "pacote": "^11.3.5", "rimraf": "^3.0.2", "semver": "^7.3.5", "cacache": "^15.0.3", "proc-log": "^1.0.0", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.5", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^11.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^1.1.1", "@npmcli/package-json": "^1.0.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.2", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.0.9", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/lint": "^1.0.2", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "5569e7d2038f6893abc81f9c879f497b506e6980", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.8.3.tgz", "fileCount": 63, "integrity": "sha512-miFcxbZjmQqeFTeRSLLh+lc/gxIKDO5L4PVCp+dp+kmcwJmYsEJmF7YvHR2yi3jF+fxgvLf3CCFzboPIXAuabg==", "signatures": [{"sig": "MEUCIARJc0lddXuq4e4savDdYcIBxemCUg8VOzKDoFPRZXImAiEA86SCajWZrXbpdA+BCkwskXctdEVuGcbDQYG0YPWVbfY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 388300, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhMO5JCRA9TVsSAnZWagAATvAP/R/1OlSx9NCLnkxpne84\nj81dV+PRRPDOAKQdRdL36l82tuFIm7FMyKhy1TT1gVdeFzhTP2hdr5Az+2Zt\nJHpYC5a074DFUbFw6HLpwUtk96YjK8A3VV5Oyj9aESZJxjP/KCoRKTZ8PKvk\nEeIWoKClYdx9L+CM6D8Okx/yqnvCKpruw7hzREHrRw3aporjMZRTesCrXN4a\nqE4pwhKqC+a2s3lGzNXP2x0GKyuk7Fqv0M4zcYN63CD1/KnDSxWbUlNsZOBs\n39J593piN3rqSQEJHRCWZqcp73U9ARSq675fXGrCUWSbK9CQ5v/WnsizByVi\nj7VmCFjhPkbAx/FiMpJHOiU6ic1UaTYDYVchA3tCPjyp0XdyOTMhFjjzkPVA\nC7sMGzEQi6TOEn7Ggyw+N5RFrU/6wfdURWVnrJxvh8uFhAxtquUwu7NQ5Wf7\ns7eF6X0oKVbdvMw4qGQFrJc5wROMwB4rFGj/vQLdTfOQ8G7JBi1RqFqPDyf+\nZcFQzJbEq2QpVnTwlmkbchU7jrLEgXH3YTcbDDOg7iYwKU8WF/uXxlZK0/nR\n+9I/tlxIizDgvJiLpB+jYGOVFWcJXIx/8keAmw59Qjj6cL+CBAgI2TkweaRm\nnhJURJ7yurdWfdeG6CeM+Z/dREfE1Bcnnl7OM4apMEtpXhNyP5m+mqvuEAr/\ns8ce\r\n=X8zd\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "2.8.4": {"name": "@npmcli/arborist", "version": "2.8.4", "dependencies": {"ssri": "^8.0.1", "mkdirp": "^1.0.4", "pacote": "^11.3.5", "rimraf": "^3.0.2", "semver": "^7.3.5", "cacache": "^15.0.3", "proc-log": "^1.0.0", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.5", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^11.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^1.1.1", "@npmcli/package-json": "^1.0.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.2", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.0.9", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/lint": "^1.0.2", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "7fd23305de7536142587102bd24322ffe733a6b2", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.8.4.tgz", "fileCount": 63, "integrity": "sha512-rE9L3eBpM4D5hT3IyN8oWxym4pTOOZPj9HgUsFwCSgXinqUWFRXw0SSKvLvFTC5FFRu02QWS/DTsPG7mZTDvJw==", "signatures": [{"sig": "MEUCIGWy5OxkbcSWOx2E7tNQBiSC/Zb4yQ0aYLDKJ3ClIFXJAiEAh8eS2in7ZTUJ/qRn1M07+nyCWXUwwctTxTH3Y3T2Odk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 390315}, "engines": {"node": ">= 10"}}, "2.8.5": {"name": "@npmcli/arborist", "version": "2.8.5", "dependencies": {"ssri": "^8.0.1", "mkdirp": "^1.0.4", "pacote": "^11.3.5", "rimraf": "^3.0.2", "semver": "^7.3.5", "cacache": "^15.0.3", "proc-log": "^1.0.0", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.5", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^11.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^1.1.1", "@npmcli/package-json": "^1.0.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.2", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.1.0", "@isaacs/string-locale-compare": "^1.0.1", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.0.9", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/lint": "^1.0.2", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "425b2326c290668b106ecaafbc2d13feb805d7e3", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.8.5.tgz", "fileCount": 63, "integrity": "sha512-dtwuFeRQ1SiXc1FswS40r42JfDnTT8PMwVnVAxw3YCXiIM85qlI1iyAICGzN+Ty6W3+p4Avp7He26v+i5+pUWg==", "signatures": [{"sig": "MEYCIQC8Xdak3IQiAsvII5KdVhSMzka/C3G+HssAbiEGW8ALXwIhALh4bN8xcaQg2JgctUoN7KMx9tMzwbfwsivjHe4bMI73", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 390807}, "engines": {"node": ">= 10"}}, "2.9.0": {"name": "@npmcli/arborist", "version": "2.9.0", "dependencies": {"ssri": "^8.0.1", "mkdirp": "^1.0.4", "pacote": "^11.3.5", "rimraf": "^3.0.2", "semver": "^7.3.5", "cacache": "^15.0.3", "proc-log": "^1.0.0", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.5", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^11.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^1.1.1", "@npmcli/package-json": "^1.0.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.2", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.1.0", "@isaacs/string-locale-compare": "^1.0.1", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.0.9", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/lint": "^1.0.2", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "b9940c0a795740c47a38245bbb90612b6b8453f5", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.9.0.tgz", "fileCount": 63, "integrity": "sha512-21DTow2xC0GlkowlE4zOu99UY21nSymW14fHZmB0yeAqhagmttJPmCUZXU+ngJmJ/Dwe5YP9QJUTgEVRLqnwcg==", "signatures": [{"sig": "MEQCIHAmF/xFDQ+CZg9wBoKTBjD9p3MHaqju3VHomrynAt11AiA0fv7QLQ8pZHUsOOuLrV8niwlXU1kurgAWRGWuvrCa0g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 391020}, "engines": {"node": ">= 10"}}, "2.10.0": {"name": "@npmcli/arborist", "version": "2.10.0", "dependencies": {"ssri": "^8.0.1", "mkdirp": "^1.0.4", "pacote": "^11.3.5", "rimraf": "^3.0.2", "semver": "^7.3.5", "cacache": "^15.0.3", "proc-log": "^1.0.0", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.5", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^1.8.2", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^11.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^1.1.1", "@npmcli/package-json": "^1.0.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.2", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^1.1.0", "@isaacs/string-locale-compare": "^1.0.1", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.0.9", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/lint": "^1.0.2", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "424c2d73a7ae59c960b0cc7f74fed043e4316c2c", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-2.10.0.tgz", "fileCount": 63, "integrity": "sha512-CLnD+zXG9oijEEzViimz8fbOoFVb7hoypiaf7p6giJhvYtrxLAyY3cZAMPIFQvsG731+02eMDp3LqVBNo7BaZA==", "signatures": [{"sig": "MEYCIQC7RKRrusb9KA3Z33fJm//ZR7pocGBR4mNFjuUi1V/BcgIhAPvR25jd9YCrHDVopSPDfEL4i8rUNrDhJMvUhYyPBnZW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 392820}, "engines": {"node": ">= 10"}}, "3.0.0": {"name": "@npmcli/arborist", "version": "3.0.0", "dependencies": {"ssri": "^8.0.1", "mkdirp": "^1.0.4", "pacote": "^12.0.0", "rimraf": "^3.0.2", "semver": "^7.3.5", "cacache": "^15.0.3", "proc-log": "^1.0.0", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.5", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^2.0.0", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^11.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^1.1.1", "@npmcli/package-json": "^1.0.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.2", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^2.0.0", "@isaacs/string-locale-compare": "^1.0.1", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.0.9", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/lint": "^1.0.2", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "fcd2416dc153aefa1e3ca5436eacbf4de5f09662", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-3.0.0.tgz", "fileCount": 62, "integrity": "sha512-zAmy3LwjQ81HKYA8Z4Uao8Re+ydiad2sDKI+PKe2loqDXnFolm69LIGmHp8+7BPWX1CAJCs1/XRNTLdXmuMZZw==", "signatures": [{"sig": "MEUCIBHOTq1jurZtRG5P5w90rCj1Trl4n3vm3cYlGswHwcEaAiEA6mpmxHmeoua+4aGZLPjo0DVYzhnVAz0GwxDTCPdqfGk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 391566}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "4.0.0": {"name": "@npmcli/arborist", "version": "4.0.0", "dependencies": {"ssri": "^8.0.1", "mkdirp": "^1.0.4", "pacote": "^12.0.0", "rimraf": "^3.0.2", "semver": "^7.3.5", "cacache": "^15.0.3", "proc-log": "^1.0.0", "bin-links": "^2.2.1", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.5", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^2.0.0", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^11.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^1.1.1", "@npmcli/package-json": "^1.0.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^1.0.2", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^2.0.0", "@isaacs/string-locale-compare": "^1.0.1", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.0.9", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/lint": "^1.0.2", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "cdee5800c4d9a5351ede8544008cf6690e066c46", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-4.0.0.tgz", "fileCount": 63, "integrity": "sha512-UtgFRDJGgnFNONW9hjyq0ft6fQPK8nLuYyFFXlhaEKVl7+/rhtUG0t7TWwu8CBXn7KjxhbHZgIlZBKIE+v1C6g==", "signatures": [{"sig": "MEYCIQCuWUYlUyR5MLhpuYcb+I4LR6tv+qu2t7peJS3Ol46S0gIhAMi+ZLKOL2g1dH/yRw3oInhubViy/8SpwjJy6qPMKN7N", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 396507}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "4.0.1": {"name": "@npmcli/arborist", "version": "4.0.1", "dependencies": {"ssri": "^8.0.1", "mkdirp": "^1.0.4", "pacote": "^12.0.0", "rimraf": "^3.0.2", "semver": "^7.3.5", "cacache": "^15.0.3", "proc-log": "^1.0.0", "bin-links": "^2.3.0", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.5", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^2.0.0", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^11.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^1.1.1", "@npmcli/package-json": "^1.0.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^2.0.0", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^2.0.0", "@isaacs/string-locale-compare": "^1.0.1", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.0.9", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/lint": "^1.0.2", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "18230aca9d58acf920d61351ea42cda4d0364a91", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-4.0.1.tgz", "fileCount": 62, "integrity": "sha512-EhHFbvwNbkVl2T0FYUyxt00pxLCuqMSloikOOpjGXGSHLZSkItQGxDM3ly4liKGEBuU1qJBRH3VlJJKCz0c6vQ==", "signatures": [{"sig": "MEUCICBNuzXYY8ApnP9RLUM/94rm6NiYSVXZFTXP87wOVgBjAiEAvieMT6WP9o6qt5Ju6Ggzbn4z2swoG2KIKGmsEa7pUzA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 395212}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "4.0.2": {"name": "@npmcli/arborist", "version": "4.0.2", "dependencies": {"ssri": "^8.0.1", "mkdirp": "^1.0.4", "pacote": "^12.0.0", "rimraf": "^3.0.2", "semver": "^7.3.5", "cacache": "^15.0.3", "proc-log": "^1.0.0", "bin-links": "^2.3.0", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.5", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^2.0.0", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^11.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^1.1.1", "@npmcli/package-json": "^1.0.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^2.0.0", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^2.0.0", "@isaacs/string-locale-compare": "^1.0.1", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.0.9", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/lint": "^1.0.2", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "3769dc1d3d48e7df804833bd7382051241abab2c", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-4.0.2.tgz", "fileCount": 62, "integrity": "sha512-tmuUNr66acGh8oOo6rKLNOaleeUDSymxTBQJFzDpRET8kG1nzLwIRMpV+CZkzmQ0tbCQ1NMyDvBeyu+kaJ+Dtw==", "signatures": [{"sig": "MEUCIQCjpLcZfT0CR6hZt56Jte7Cp1ysnwbpnAnmZnvWr5C7+QIgY4hMuAKK6m9oAUEtj3g+YwIpBUDxHijubnm+e8Irb/M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 395328}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "4.0.3": {"name": "@npmcli/arborist", "version": "4.0.3", "dependencies": {"ssri": "^8.0.1", "mkdirp": "^1.0.4", "pacote": "^12.0.0", "rimraf": "^3.0.2", "semver": "^7.3.5", "cacache": "^15.0.3", "proc-log": "^1.0.0", "bin-links": "^2.3.0", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.5", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^2.0.0", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^11.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^1.1.1", "@npmcli/package-json": "^1.0.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^2.0.0", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^2.0.0", "@isaacs/string-locale-compare": "^1.0.1", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.0.9", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/lint": "^1.0.2", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "5e1632192f970c3a4e43c4699ad875089418bed0", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-4.0.3.tgz", "fileCount": 62, "integrity": "sha512-gFz/dNJtpv2bYXlupcUpEaWlFDRUNmvVnQNbE6dY4ild6beZ2SkG4R5/CM4GZZwj9HD2TyfGjO350Ja+xlLzuA==", "signatures": [{"sig": "MEQCICHbXgb/6tibK4N8dhF+eAm2tkfe1dveoUFSddxjdamcAiBDNc0IqMLxAr3J8FwyDYB82wnU1AK+oeQ0HZKS13b5QA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 395561}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "4.0.4": {"name": "@npmcli/arborist", "version": "4.0.4", "dependencies": {"ssri": "^8.0.1", "mkdirp": "^1.0.4", "pacote": "^12.0.0", "rimraf": "^3.0.2", "semver": "^7.3.5", "cacache": "^15.0.3", "proc-log": "^1.0.0", "bin-links": "^2.3.0", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.5", "@npmcli/node-gyp": "^1.0.1", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^2.0.0", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^11.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^1.1.1", "@npmcli/package-json": "^1.0.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^2.0.0", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^2.0.0", "@isaacs/string-locale-compare": "^1.0.1", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.0.9", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/lint": "^1.0.2", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "a532a7cc430ccbd87c0595a8828f9614f29d2dac", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-4.0.4.tgz", "fileCount": 62, "integrity": "sha512-5hRkiHF9zu62z6a7CJqhVG5CFUVnbYqvrrcxxEmhxFgyH2ovICyULOrj7nF4VBlfzp7OPu/rveV2ts9iYrn74g==", "signatures": [{"sig": "MEUCIF0czoBJsC3kxPaC3G+w5lItPaOKJYYMrA9UXBuNPsWqAiEAomL8u5kzzRYFt6N/s1pQJSgoLfuyMkuAzf5ifBSOHeA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 395904}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "4.0.5": {"name": "@npmcli/arborist", "version": "4.0.5", "dependencies": {"ssri": "^8.0.1", "mkdirp": "^1.0.4", "pacote": "^12.0.2", "rimraf": "^3.0.2", "semver": "^7.3.5", "cacache": "^15.0.3", "proc-log": "^1.0.0", "bin-links": "^2.3.0", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.5", "@npmcli/node-gyp": "^1.0.3", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^2.0.0", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^11.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^1.1.1", "@npmcli/package-json": "^1.0.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^2.0.0", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^2.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.1.2", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "^2.3.0", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "7cdcfed28cad03803f5d75caf2b0f3051165ee70", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-4.0.5.tgz", "fileCount": 62, "integrity": "sha512-WR2cqxzjsvmHJ9sKCdqBYG/qeiAXB9ev1iq1W2Rry7LxeJ7eDtTr4mOWe/TBvp6xFzevGecQc2YEWwExTuLZLg==", "signatures": [{"sig": "MEUCIQD4AJeLVZHEcrkBn/h/E/bQrFP0eupbxKo9btCsKaelQgIgDk7zPk2kUpBd8m/3bAwA2sk+koMm8X7ZuWV5zRcxb8E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 396418}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "4.1.0": {"name": "@npmcli/arborist", "version": "4.1.0", "dependencies": {"ssri": "^8.0.1", "mkdirp": "^1.0.4", "pacote": "^12.0.2", "rimraf": "^3.0.2", "semver": "^7.3.5", "cacache": "^15.0.3", "proc-log": "^1.0.0", "bin-links": "^2.3.0", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.5", "@npmcli/node-gyp": "^1.0.3", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^2.0.0", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^11.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^1.1.1", "@npmcli/package-json": "^1.0.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^2.0.0", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^2.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.1.2", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "^2.3.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "436464664bcfb3b180963383c410ff9180351c59", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-4.1.0.tgz", "fileCount": 63, "integrity": "sha512-bkaOqCuTUtpVOe1vaAP7TUihu64wIbnSDpsbqBJUsGFTLYXbjKwi6xj8Zx5cfHkM3nqyeEEbPYlGkt0TXjKrUg==", "signatures": [{"sig": "MEYCIQCmHfkGjprjE+e50sTKF7EEbhdopZirjtC4FLB7tBTZuwIhANmo3LxiMyHQcqXdtk7varCYN4lGOSAgGQgt2WUdD0Js", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 402363, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqUvPCRA9TVsSAnZWagAAGooQAJ2RMkWqNLtypMmcagbm\n3OWgMinSQvoCpEGtJVa5HsYIxnrb8jr5zomKBa8bDW+uGBAWFgr3xG2onwTC\nE9+sGniM//oCVJuSzqkC/TuFlWsXdGr0GVYJaDh+SWsFbum8yXqPmle2+01Z\n8FRgTqs76RyMNztCVD8w6+kP0VpDp9OKe01DGY/F4syIWF8DBVeSCEpZxnrI\nXAqHK/1rjz9Trx8oEMa2U2ZLE5Sah4ROVHf8RYWrCT3fhli+HEt2zeSavV8/\n7dkUBI44SuRRbmI0hq7Sbb9wjZi3pfeafoMogbwu1NgDkzIbtx4/4Z7jHgDj\ng1czBBVBiQgdIXnEyfNSHzMSDvmccsuBQ2iZpCRFThswKihDWc/JgqJGr+4w\n1udb169h/pBA+eTBygDWxi1QKdsDf4WHUzr63Yz7Ouw0GaZYGg9YWpH3ktod\nNS5LoXngiGrm2fj/lv5RJ2Dw1RIM0IbYYMtMa5+xNLdVuj9OHWVcrSsTWEHD\nMV/JludKYJCgni+e4i0pNSPJrkZ7ghQVvVDTAANTJX5cv4xzps5c9KIWMf3g\nBogxd+fF3O5D+CN0bA8LrnwsiooFQWsgL0KgE894oEyTziOqz0cs1FL6UdGP\nsHTUZkhg5uvHovgc38sNJTT824Ns/d5VcG105+5apoSJIrz/lkSIsOpg6QsD\n7wi/\r\n=cO3d\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "4.1.1": {"name": "@npmcli/arborist", "version": "4.1.1", "dependencies": {"ssri": "^8.0.1", "mkdirp": "^1.0.4", "pacote": "^12.0.2", "rimraf": "^3.0.2", "semver": "^7.3.5", "cacache": "^15.0.3", "proc-log": "^1.0.0", "bin-links": "^2.3.0", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.5", "@npmcli/node-gyp": "^1.0.3", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^2.0.0", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^11.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^2.0.1", "@npmcli/package-json": "^1.0.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^2.0.0", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^2.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.1.2", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "^2.3.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "a36202f85b0b8d47f5fc0e056e9836282bc5a38c", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-4.1.1.tgz", "fileCount": 63, "integrity": "sha512-sASzHngGWt8l6ic1VP0Qf3+ral/RL8L+MculTp2w8NYjjkDiurByOT39KiYmLwpeJ2GQoDR/rdhEwnII8wZQ9g==", "signatures": [{"sig": "MEUCIQCXamwuFOeCitmOVD3AU8nGn0xrwaDM6uodeahTzGU0zAIgUNC+KrUDLpzhPwpphyco72HL2SN7ZXZ3GOAageGbl6E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 402363, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhsSJ5CRA9TVsSAnZWagAAd5UP/1bmcltZ6gr5vqDiqCJT\nf32lmycTd/TgjTeH1JIcEt+Cd7oi7JvXM+2P+t38mC5oAdgGVBgVIobRBxEh\ncIrUBrK1UkH1EQ371lsRw8o1ZOJ/wASE7lip/g2NukCNMG+SBK1JbdfqXvlZ\n9AQfJKq75cfAmO5I1XpNyDFL6f0fAntFCZsy+ltz2MBYNQHmdz9FTm52qnTa\npYHoK/O+g6JTQLf88SzCeGbM41rLDjzpzXLF8SCqoyg80Xm3HJVPY4uY23pj\nvoNEZb12WvSLJAERuko37SL+MpaxK0djk/iKN0DOmjjK+LjiJEjsl/jD5lbw\nbTwNEaV+QTirPrmrM+qj4ELl+aDltkKsNTnG6Wea3qaFXlngDeyDyosJtewo\nlNk6OLVuyu7Vx752uVvcb1fW/wcoAzrOWtD26U8SlOPXVr26CzPhL8dBadyO\nLXcN5LBNu/6KykgjE7s8x4MCgaV85aIpeSF7yYZy3b5z+tD21E/f3iXfVl/+\nP5NTpdD4NnIx5mUi0NDb9WItxTse2H/VedEGCIVQN0ZJ5lA+fUPb0yBHXz0T\nbkbvDzkAuvesqBmIJWruMrwmDAgGo5PiioliMz3PO+CdpxeWHVI+fMo90ofG\naQyKLLVHmuhkKIre/W3/ZlagAtJsZ1xYRtL3c4L3H08QEDZUwei9Bv+U1qRU\nx1dy\r\n=QUGB\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "4.1.2": {"name": "@npmcli/arborist", "version": "4.1.2", "dependencies": {"ssri": "^8.0.1", "mkdirp": "^1.0.4", "pacote": "^12.0.2", "rimraf": "^3.0.2", "semver": "^7.3.5", "cacache": "^15.0.3", "proc-log": "^1.0.0", "bin-links": "^2.3.0", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.5", "@npmcli/node-gyp": "^1.0.3", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^2.0.0", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^11.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^2.0.1", "@npmcli/package-json": "^1.0.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^2.0.0", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^2.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.1.2", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "^2.3.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "85c9143662423afb00a3fad69fba9e97d3251812", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-4.1.2.tgz", "fileCount": 63, "integrity": "sha512-L7iFMkos0EcMFaBagIB5GBrQS6WTywNjp5yGUZbLCHCzxETfqr1emdWYP/4xMbapYeGYPryNtfzszZjmjVFsFg==", "signatures": [{"sig": "MEUCID+RG8XwR+Wm7GpH/H1a5k18uDx1P5rUdO5C8dFpkZbOAiEA6DBrDiMTBqsyLXQewTGL42w9TzwNIiIEhchwib4IIUo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 402363, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh1eRcCRA9TVsSAnZWagAAM8sQAIGEfKP5YIigzq5SjbXV\nA5qsjdQHO6B4Qu46QWBLLYpoT878/PH88TAuklvKv0eedDocf3HkDc4ZcALu\nERqFDlWorKKN8FfeAeodWvzKXOuDAFlDk+w76xq0PvSfvRDK7FLV9Cuwu+Q2\n33vjDTZ/4RATPQmo6RxRBD9ZKf4yolR0hbq/eTQRQ2YLx1DAtpeOVO38tc2r\nXMybFTExEOuRiYVOnW7Uf800ZdRCG9WlxGZpSPRkALOxpbJcWXxlmcIKJPFm\nxTwmNBY06TZuhtDg20m+si9Apg3pzKnjXOJDVZnHuNr0Ygw9l+FoTkg1Rw56\nqxFG0S0L+xgzjPSvGam36LAPmhO4CJ+0mL9WCf04PB7LHDqhTPbhJOOFKn0j\nsgUMzAixQ+N9zh8PjXJ8oLGZuIDy59lkrga1Icxgpt3vz4Q/zpo37J7JhVe8\nDYXAfTOfOHL/aaqi+2+dY5kLNEWnldCzHsWDuE95sqyiVE0dAmSHHMrDJX0e\nYcDn6TWWZtDaC+5FKHnE0dYVaACA5Vnjfi7j7KqzaMuSQKiKYpSYzivYRouQ\n9tZncxH0lsA7Um/e75NTpfvHjX6tcJoHZPYxx4SWKrGJyginjzvH61USMNpf\nhRdDjsJS7z4It7w2dDyXkv/GboQ47QuuUPbEf8e9OtZ9zAqjOxaLNHhB2Fcy\nTZ9f\r\n=/Cll\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "4.2.0": {"name": "@npmcli/arborist", "version": "4.2.0", "dependencies": {"ssri": "^8.0.1", "mkdirp": "^1.0.4", "pacote": "^12.0.2", "rimraf": "^3.0.2", "semver": "^7.3.5", "cacache": "^15.0.3", "proc-log": "^1.0.0", "bin-links": "^2.3.0", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.5", "@npmcli/node-gyp": "^1.0.3", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^2.0.0", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^11.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^2.0.1", "@npmcli/package-json": "^1.0.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^2.0.0", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^2.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.1.2", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "^2.3.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "722b114376645ed3c78e1cef62969eb7993df2a0", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-4.2.0.tgz", "fileCount": 63, "integrity": "sha512-uQmPnwuhNHkN8IgCwda6wXklUf3BUfuuIUFuJMT224frUS5u2AuEAeCr2fiRVsz7AHcW3iSDai2j3WhVFlfbRQ==", "signatures": [{"sig": "MEYCIQCkcy46cIPRwz/7KwvQH24bIQYaeW+DdqKNgaRowPbtdgIhAMfw9cxrq4xbhUJ2Zsav6JapVcOK6QD+mqnNdOLcwU3/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 402569, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh10ApCRA9TVsSAnZWagAAkGIP/0WTl/cLqywwy4kvYYkP\n594csy5OOFxxCAhAhrEe5YlK1nMdxjvCchXHzmBrQ+Rqb8h9QWGGTsiSPTuj\nMaedQ2secxyQ3PQgHSdF0/R76UmL0FokX+YNzI+5NLVKqVVgRuil5SHIaAuL\naaUUF1q9Z2JekEKUQ+g4iZ+s/gFJaCY/QU+T0PCcr5qzB5Lfbh8N82ldmuxh\nWhu0ZD9ucnIBQNrvOR4s3az7Brp0vCWhLe/ffBUZBKN5I+ikNWRj3/YNUHTT\nHG7cWhh8c1O3nn2znE/CEzc6HSUJIGAb2UaaUP4yrZ0Vjqowu2Hc85440FY4\ns8yk/P6CEzFc4mbWOZbzz2j4wlmnCI1OZR84AQb6pMFBX+E/6BkrFs1cB4c0\n0/DswgbAyZaRRtJ3KrcRWyNRsqsrbNxbeTIDw2Jy15A19ykVObGG+Bi+Y9Tt\nUEOZ8lzSuE6o1PJctdySFJmWwJh1jnrQ1O4bL7YU3YtuvQx5bs4PuS0DaAlU\nxuXtFwdBk0rpf7LyzLjME1v7S5SXJZZGzwF8jczaa41y4iADwIAZJyI+DLkZ\ncpuy1uw2tCBLERfxsMMwJ5MpQkkXyCCd6VYs+1hPV/mycEQkPT1OrcliI83B\nVcLs5B7BA2A/Qvqv6uT/Or7fftNp1Q4YJNuoV0SWiP6f3+lyZq3cStHgL02m\nJ/bc\r\n=1qyp\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "4.2.1": {"name": "@npmcli/arborist", "version": "4.2.1", "dependencies": {"ssri": "^8.0.1", "mkdirp": "^1.0.4", "pacote": "^12.0.2", "rimraf": "^3.0.2", "semver": "^7.3.5", "cacache": "^15.0.3", "proc-log": "^1.0.0", "bin-links": "^3.0.0", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.5", "@npmcli/node-gyp": "^1.0.3", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^2.0.0", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^11.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^2.0.1", "@npmcli/package-json": "^1.0.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^2.0.0", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^2.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.1.2", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "^2.4.2", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "b0bbeb2036460b7a85adca42e2b2226fc349aa0d", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-4.2.1.tgz", "fileCount": 63, "integrity": "sha512-vAIfPwW6alsyjfv7wzGPMJv6Dun8xs9VugdqfwkWEyRajzOLjeifxeDqlJYQ8G8FTU38grIWjkoUmd9cAnJnPg==", "signatures": [{"sig": "MEUCIEMTTjZ9Q5bFsBnfQvFgLNHoZhcK+r40/ATEH3PSgmARAiEAz6wbG31zfnl/QkSUOcK1RGMTzyvSaQHbh6kdcqngHT0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 405424, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6dKcCRA9TVsSAnZWagAAx0MQAInUlUlbIcSu1tJYgTSG\naPsWBXomtXCgYwHvHXMjTTEQJTxK718SkG/xiYmK+3vzeVtbZyMySBMldzeD\nuw8lGxaGeeuE+xRYJYw+fRKxpzd1iOszsCZW2q+oCKlG3MqSw6e0kSDuI5dB\nniRXtlNwjqX+Fl91sbJ4FzrGqyDXoIhpoxeK87oeGPmDm0sSj5Fw48zEeXNH\nP9Q1jdIimKMUOBetNcFP76Zg+3h0dgFa/PNzSX5HtFXrmIAonXjJN7ZwK+tV\n0De7EL5OLOQgiHjido+gZpIV65IQlch5S15mmAD/ceg7CBLUxuGr0pIxycvi\nVh5hyh3DJp14X1KSbwUu2HIod338kRCH8ldUCq0FmwPJjgyPvzD/EWjkhSBX\n4qkkZi2eLkST2g4PuafaZ2t5zr8W4dOueoz4zgd3mwdjksIBVJ0mb80aUkzZ\noAYQTQ9ztXRJnmboqWlNarT0UAKtlTSFxxsegEaXP7wk72YNuhkRuEcxnk9N\nM7a/cf68xomclxV1oGYlAalEh121OO7H9MxDN+POpDiE17W2CzSdaFC/gsDf\nxKSKvk7NbYp39Y4Nm7AsNH39qeAo7SKV4K3gKO0x5xHnbacwsxJGybXza5lg\nRmjZhTsqiXiZWVhh5HEDMVLYadXRnWjv04CrFB8lISI68gX9YWZYmff4JCG1\nfl5U\r\n=u2Hg\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "4.3.0": {"name": "@npmcli/arborist", "version": "4.3.0", "dependencies": {"ssri": "^8.0.1", "mkdirp": "^1.0.4", "pacote": "^12.0.2", "rimraf": "^3.0.2", "semver": "^7.3.5", "cacache": "^15.0.3", "proc-log": "^1.0.0", "bin-links": "^3.0.0", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.5", "@npmcli/node-gyp": "^1.0.3", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^2.0.0", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^12.0.1", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^2.0.1", "@npmcli/package-json": "^1.0.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^2.0.0", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^2.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.1.2", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "^2.4.2", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "aaad5d3143eedad49a147d4dabdad8d059c06569", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-4.3.0.tgz", "fileCount": 63, "integrity": "sha512-d1aDSql/JrCRRc3g6R6bXk94Gx9ggf25qaMTEc6KCEdIghr2oL+zkr/hQMWcSuBPPynx4yNA9EcZ3uFosvMp+Q==", "signatures": [{"sig": "MEQCIDUcHbRG8lRP6ZfPe0MoAU1lhGTaseVo+zrh41XPHYHdAiAQ9MsROsVf98Dlj7kUtyqEhOlQCopxiEVpjiCJJBPBvg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 407021, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8tNWCRA9TVsSAnZWagAAnYEP/iGpTWt0csWl22qMIv+C\nlDr8VOwr+Z8h9PnDGkVdNsY85wUHcakor0mJi4ufJS1kL/XnMWMTX41PiekS\nYp7qciuFXxuuLbW1Hj7v3JSPTeuifT2BkCtiQALZjU6bM9buVoVHZHUVpedy\nXKovmBltBfw3sdZzuS+uatn4enSeLSmc5Trc2hbgnkMsQEZ6by+es5Jf5IB5\nkV3arpfWfVKr9YStdGmBLhD8t4YBVop+UsDnIgeSwZlSG8M5biXfdBwfmM32\naa5PvSNaS582T+wsWcVnka9MZANQd1EoOEpkdFdUe9TxoEucQm2UV4616+lU\nLuj7F/74oQNJly/H9wapIMN09Mt54dvDs+3R6eAsF56P82e1TkekbGq3HcvD\n3KpkPBl4+MAQX3GeYkyj4+kE6DoAlywEfRjsaFl257F7DWk781TmYtgvm7WF\nMDsjqKrtocJgEuCFIjbh5TpD1pWwCcrDs+IOYDY9WK+94J5o6bKElxmZ/3CX\nmjQsbebUhn8xdN45Qn3TH1P1xTD2JSoP8bpbOHPHTherhkoL/WIflIGsuBkW\nq7u4sVvWQJcfd0bIxdswtVjpjo/eXFlK6U+/kwNTq3buNGMSi8cvfWH0fupS\nV5ULM8ZgxDB6sN1ZTh5gSCHwN0t/LZx98yNYlL5hPh49XwkDXFBSwbrbtHlK\nIyPC\r\n=Lyaw\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "4.3.1": {"name": "@npmcli/arborist", "version": "4.3.1", "dependencies": {"ssri": "^8.0.1", "mkdirp": "^1.0.4", "pacote": "^12.0.2", "rimraf": "^3.0.2", "semver": "^7.3.5", "cacache": "^15.0.3", "proc-log": "^1.0.0", "bin-links": "^3.0.0", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^8.1.5", "@npmcli/node-gyp": "^1.0.3", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^6.1.0", "@npmcli/run-script": "^2.0.0", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^12.0.1", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^2.0.1", "@npmcli/package-json": "^1.0.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^2.0.0", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^2.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.1.2", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "^2.4.2", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "a08cddce3339882f688c1dea1651f6971e781c44", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-4.3.1.tgz", "fileCount": 63, "integrity": "sha512-yMRgZVDpwWjplorzt9SFSaakWx6QIK248Nw4ZFgkrAy/GvJaFRaSZzE6nD7JBK5r8g/+PTxFq5Wj/sfciE7x+A==", "signatures": [{"sig": "MEUCIQCfdFNqozPFEnHTGXf8dXQEgtdBh1HB/RQKevHT8NDzvQIgIxzJtuJPny5IJeq6Q+v2DYLsxUyaR5i5P/po2JmzqMo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 407675, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAVjlCRA9TVsSAnZWagAAIe8P/j3Th9gy49g4IaECXV8x\n1UE8kDE0W8yXoCSQAoSmBPfZsBfA5SwIPOjKXUY3i1lEtQfwMTex9bh7G7qO\n3ngNzGqPbp85GHwGYUWE9RV/l0T4RckrmB34Xye1Mc2p3I/b1D82e3sELwnN\n+PyOdgVTTgpDwC0Vp3t24Z/RvODQsW5hGcOZBUDrMOp96e/9PCUQd8MU+A65\n+9SJsM8DDMVYOSPuFUhODs+fbCc6vYQvTj69pEpN+bma0YQBrOC7sDBaCmUW\nXAN9H0OoT9Rnztrh2MPU25x31RTe/jpL6ee+ZeLepFdh8cUbU7VkUVygCik6\nQWTJ7iYIa3DctF+/BSLA6qXVgZ8X9nr5PGUjJ6jmtBGJ60mX9jCVzp886nkd\nC3Lu6rxiOuCdzzLV4J5FUWNU+K/FBW3A5txKcPTinciSe1WvvIMJ7PGYBsnh\n8/ydrI0Za3EIjzVuiPgE0tWOggpxBmK+FmwIvW6wTGwO8qZgb9+m+LpFmpU/\niBrRIFyatYY6o3WVkANlW/ERIdu/aw/n73WCpOiz0oCXn7+NQ4Y2+YvGDCOV\ndDl2IPBGhjHXvcoCUhQohcuLPw+vR1gPZ//MySXDkE9yRJ/6FtK60TDzRXdJ\nLQrI5QbdxSrIdyPQ4yPBwkr4yMz4gMeUH+2zYjmwBugpXoiOPCVjGULiJOe+\neLU4\r\n=jhy+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "5.0.0": {"name": "@npmcli/arborist", "version": "5.0.0", "dependencies": {"nopt": "^5.0.0", "ssri": "^8.0.1", "mkdirp": "^1.0.4", "npmlog": "^6.0.1", "pacote": "^13.0.2", "rimraf": "^3.0.2", "semver": "^7.3.5", "cacache": "^15.0.3", "proc-log": "^2.0.0", "bin-links": "^3.0.0", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^9.0.0", "@npmcli/node-gyp": "^1.0.3", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^7.0.0", "@npmcli/run-script": "^3.0.0", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^13.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^2.0.1", "@npmcli/package-json": "^1.0.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^2.0.0", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^3.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.1.2", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "^2.4.2", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "f5186180eb89fd436e75e0ad48c2724f707594b5", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-5.0.0.tgz", "fileCount": 63, "integrity": "sha512-Z0YSj0baksaueiybAZe9NuVKiAO+k2J6ICkHyWx/V4c9ICwNAv+/y2DKdmcVyeUF0pzIcTVf7MRoyvbbPDURww==", "signatures": [{"sig": "MEUCIDYQh47S1xYYYrTkZPCBLHYNn63yd2b0e1N/OVnMApe7AiEAlWiCUszStRYQ/91jaI/vGMxyc4/vdpP1JO3Ltjmp3dc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 410376, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF97VACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoVTQ//ebO6jut0t2fybF4JEKxfpZfkyNH/IWuBQCEsV7eYDbm6DeLu\r\nr7dNPZ2bYRUlJvslg0Qxo0pcYJRI7pvyDWN35+ukp5SB5D3GIjqurv2aWlFd\r\ngAXoli1iFov/SLC/De4BS/yC5T9X0OzXH8LBWwitGxUdveP4OaLxX5WyForZ\r\nm/aQV+MlaaetKxWMhVoWGTkVPkoL9kMIC8VdRxZJWhlXkZhkV+rqn/TDunlU\r\n41VvGRtAQtx9/i1RWVaTRLwjErXHkkWyxb8pHScT0rHEpM6y8Jfy/a8Gk0yg\r\nayy2CSop6ZrIZ3JWCf89PDj8EX0XtAr/hELVIW4fMcQkEnW6JqZvcavRPgar\r\nWhOnDtO9A99u0kT+JzHddqlnJ4dGAcgxDPTlTnVarj1Q7/HKMEix4K8Zxi/A\r\nVLqlmpZo3aUEeRuuRQRNa539PehswRGt3dGwCfJYZsMkQT0QHpxAvj8xCvCf\r\nisX50cyHeacNBPJvk/MUgIs1eMhZhrUk/YW2Oea+sx+sgptLyjo/0ifbnv+8\r\n69soLhaiPDo8wfm01WxGeuGZJS/K5cHCtrKMkLlEnuH4LLBJI8kCk9+Fyue2\r\nNaj6LZGXZFKHq0FVyMUgplR4QYcOXDXLom4aF2kAp7Bdeat31FHGTkvEfhQ4\r\n9o5MmQcsdKeSIVZuVmcVDAqw7FQzdMEEH9M=\r\n=kC2K\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "5.0.1": {"name": "@npmcli/arborist", "version": "5.0.1", "dependencies": {"nopt": "^5.0.0", "ssri": "^8.0.1", "mkdirp": "^1.0.4", "npmlog": "^6.0.1", "pacote": "^13.0.2", "rimraf": "^3.0.2", "semver": "^7.3.5", "cacache": "^15.0.3", "proc-log": "^2.0.0", "bin-links": "^3.0.0", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^9.0.0", "@npmcli/node-gyp": "^1.0.3", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^7.0.0", "@npmcli/run-script": "^3.0.0", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^13.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^2.0.1", "@npmcli/package-json": "^1.0.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^2.0.0", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^3.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.1.2", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "^2.4.2", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "ec7dc8be74fa30392373e2ef73ef42e04e92ac48", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-5.0.1.tgz", "fileCount": 63, "integrity": "sha512-EY8oyKLihQJNDkqcSi+weL7naraSWNoTKY7rTGzd4roX4kdl1cMSLw1gZnCZzQd/JvT74M9mmvN45YqXAOp5Yg==", "signatures": [{"sig": "MEYCIQDejWJB7a9oEN0KCbD7QF3ZlJkKxQlO2649XWqAANdqqgIhANPViUAcwgG8doiq9KE8YcviYhv66CQ0yLVmBDahwf/2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 410461, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiJ8l0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqs0g//T8hpub3VG0MYXYMYp570Fv6oDKDQtXqvlIIPsMhiRuB+1vYu\r\n/XjTrDuinCFkUtGwtxqyh86o+y1+IaaY8lY9Hbakvptdj5MvRtb38vSW93FI\r\n8dy2XiTx0gnxrHUPSzfbQHmu/9Myb1meWMYTqv9G4OXOxOLQJGqd7DDZZbt1\r\ndI4l1FH69M9SUCW7igrtAMygbGW8VBZQh6IWVdB+iS11YtnRN/ubCZG6Pioc\r\nBuIjuowWP17LJXojoAj/rRgNal1cDiywVBB8SvjlNjqb1MboZb2kltmL8GzT\r\nAf0tjF+sNtqphoeepL04/O9BoaLKxf/IVsXc9Vj9i3+QYiZcHz14F0matIFz\r\ngFetu3pJNBy40m8LgE0ZFVX9f14+QN0nH+A3NYIrL/iK+eok9+JXrX+vsq1J\r\nC/zCWMAxnnxEZp/211tiXRLhlGP4kNrRlEbfLlcM/++L1kecXoQwvlFgy13w\r\n0eS/XEftgNPmGsMYHWZUqO7mwKZosziM+pYoWUaRxDITHt16lqI7gzOl+y34\r\nkjAf8TgcJm5gqz0mye+FcdjD8nMGIofSsz+Z71/SPlQKJJfi4E1mm1xy9ayj\r\nG4GByuBAcNyHuLus2QEGiAdLEMQojfbFHcQipBhk2uRPuxA+tS6gwXki1xYI\r\nrEgETz0Ml676VAuoRScAJYEvnr82R83Nym0=\r\n=Bzfs\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "5.0.2": {"name": "@npmcli/arborist", "version": "5.0.2", "dependencies": {"nopt": "^5.0.0", "ssri": "^8.0.1", "mkdirp": "^1.0.4", "npmlog": "^6.0.1", "pacote": "^13.0.2", "rimraf": "^3.0.2", "semver": "^7.3.5", "cacache": "^15.0.3", "proc-log": "^2.0.0", "bin-links": "^3.0.0", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^9.0.0", "@npmcli/node-gyp": "^1.0.3", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^7.0.0", "@npmcli/run-script": "^3.0.0", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^13.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^2.0.1", "@npmcli/package-json": "^1.0.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^2.0.0", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^3.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.1.2", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "^2.4.2", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "6713c1be8f34668bf6032d7ff5789726156dbc99", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-5.0.2.tgz", "fileCount": 63, "integrity": "sha512-QNfOdTCzglgiycfU1HRMWz02R6vP4DEwActG1YG9kyt8qO7EfQdvvVA7U/JTIgD5LrNDm2rQXkx5sGD5MsbiZw==", "signatures": [{"sig": "MEYCIQCsFM8xKwpVvVfmgKtRrPvqSqwJfq10xOnftPKqfTDdBwIhAK6BaW9XvpY2gdvfozZ96225DV/VUnoScdUTCIh/s2LR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 410467, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiKj87ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpT7w//bIJbUTN0eEXaFsQjlHDSrJREvz9Cde5S4wfDKblXJ3UB7BUg\r\n1CwN5Vd46fmj40Ef7/n1xInZLWQHNuKwGsLU5Vyos17qC5OA0UiBdkSTm7Aj\r\n76oo646uICbRW3pOoAlgBlw32fUgb3ys5YzvioWukiiRgGJ/ej1+e6bydv+K\r\nzkAPaaGt0B+204bIH/UJ9UJwFBSKehyxnoDv9V8vSjEQqgQwu1krIqC1o5z+\r\neWiwWpRKn6RcXRwxj79NP4w1181ovui8IvED5yxUPNgBlrtqYvVW2WxCzjns\r\nQ77MN0ehsKfm1DZKvZvq/aQ5txDPv1rdZVno4pbxvxXSa8OrOzYAZQBBw1sL\r\nUOhpe04RPWdHQ0ELR/lKqr2i6mH0yibe/OwodU94uLG4x+lb8adOz8nKeK74\r\n7OMsJgePs8SrB+HwdRaMgvKVbgEicijqYr+0Fx8Yia86a/3O/V2WvD9X/yWd\r\nz3US7Ea/lU8eqjFhzJIb4XRGqGdUjhSe7HDEWIcTUCp/4ae8FzPxNmmTiVlv\r\ncFnNtlck5eSgVAJ87cUk5SDZzovRX/y9omkY4Kkbp5+2WbN6rdq/OjW1PXw6\r\nUslG6ZI0YMuXCLOgG+X9YrCWWgUYGSyOqkp186AnLVhl3DSueHwvCPU/t6DF\r\nkd2ZBCS9v2P6WH8/22MBveE/85J61UsR04I=\r\n=5zou\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "5.0.3": {"name": "@npmcli/arborist", "version": "5.0.3", "dependencies": {"nopt": "^5.0.0", "ssri": "^8.0.1", "mkdirp": "^1.0.4", "npmlog": "^6.0.1", "pacote": "^13.0.5", "rimraf": "^3.0.2", "semver": "^7.3.5", "cacache": "^16.0.0", "proc-log": "^2.0.0", "bin-links": "^3.0.0", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^9.0.0", "@npmcli/node-gyp": "^1.0.3", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^7.0.0", "@npmcli/run-script": "^3.0.0", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^13.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^2.0.1", "@npmcli/package-json": "^1.0.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^2.0.0", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^3.0.1", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.1.2", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "^2.4.2", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "3ba8e0a357eaf6e1c56d44b7379214079e57a05b", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-5.0.3.tgz", "fileCount": 63, "integrity": "sha512-dlLhJypoPftnvlu0l21yb1PXiD7kofZ6NFIAatuAI31xHPnfo0QQjV59ocwbm+gyqgE+m25/erutzTj5LdJ+kQ==", "signatures": [{"sig": "MEQCIBF1zIdKZYB9BTHa/F7Cb5SdH8GwAZOuI0x6mkRWyWN2AiBNBjKFCEKrLM62WLw3VcQMAOZ8wZkZ905CMRxLmsJWlg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 410879, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiM425ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpB8hAAjaam7CLnxmPfnA+aKphq7HkCJnGxSogsnghyq5R/QAkVnTmk\r\n2d0ZJ89EnLxdsiw4blBK/DQaSPC7+yIO36LqKKu23OPmYHAu92Y2dGVgC4bz\r\nbLLbWor8ov+8+afW/plIWFGkJQw/ptOLb1Y+YTw5WCejjD74dvOF6pdVDPNG\r\n246ceEaXtXqJm9I/pJr66qbo6r98PfAOjx3qW8RSH+nlH4tZ2+5yscerPzA8\r\nmHVi+Zu88VJBf2TSqGaSa+lKauCGPdyb7nZjvc94K5XovmQjB5nQzx89PVcZ\r\nj+a4AAl60QDqWiyrBdwY/g/Q92s5ccUfw+sLBuKnVEA6CCwsBBgs61j/EIGt\r\n6a/jFGI9DfvroLc6RFkO35nDiiPtgOUt6JJKOL+H+HxBukJO+53o5sDvYNJA\r\nlJ+IhWuPgu8nD3KSzTi6T6cJ1HdGH9GBR4y4/1ggVkiAHCAEYfbe8ChUA7jT\r\nxhQ3C1sowLHpwdAGUyWWa8fT8ZtLEh1Jzna5yOrXDsCLMEJnihEd8O0kB9es\r\nnglAlvVrDLZIXhuB19R27A2zCCc4jzXiayb8euLK2NZ1mB5D6ciC/JuNK6dl\r\nBoLNh9JrvSYTSBfk5/qeMiFBro1Dsb9SHd+TABJhdiK23I9tvTLvfQZxZUpo\r\ncxe0W3Qp5cPSst4Wn4uOf4DJ/m+JjIN7sr4=\r\n=LpbE\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "5.0.4": {"name": "@npmcli/arborist", "version": "5.0.4", "dependencies": {"nopt": "^5.0.0", "ssri": "^8.0.1", "mkdirp": "^1.0.4", "npmlog": "^6.0.1", "pacote": "^13.0.5", "rimraf": "^3.0.2", "semver": "^7.3.5", "cacache": "^16.0.0", "proc-log": "^2.0.0", "bin-links": "^3.0.0", "treeverse": "^1.0.4", "walk-up-path": "^1.0.0", "npm-package-arg": "^9.0.0", "@npmcli/node-gyp": "^1.0.3", "@npmcli/move-file": "^1.1.0", "npm-pick-manifest": "^7.0.0", "@npmcli/run-script": "^3.0.0", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^4.0.0", "npm-registry-fetch": "^13.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^2.0.1", "@npmcli/package-json": "^1.0.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^2.0.0", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^3.0.1", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^15.1.2", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "3.2.2", "@npmcli/eslint-config": "^3.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "b10099088e74c376f4b0df96ea8316085e8e689b", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-5.0.4.tgz", "fileCount": 63, "integrity": "sha512-w3N7gvCLBS1uwOmGEYAlE81bGzLc+mSbfMkWVT++y4G7w4fhjLMmVCJlerOLIf8u5Xsf5Js1tT6UClhGz5p19Q==", "signatures": [{"sig": "MEUCIF+eEb75/XGIGaewNFG5gFd7mO3igJYVjK3uoeISavQZAiEA87YliKVOo8APSGX7MMWNTDaT4DboiEsa+j4pwim9uxE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 411996, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiRiCLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoQcw/9FY9coApdZiPaA/iyt6B72avTzH5+vmU9FhvamtpJZWmvD7sG\r\nHm6w3iJmTGR7p68AhtKVnarSaTzwTXN/TNC/YtfUtr9kE2B19GyNiOBOso7m\r\n2JhPjHnoIVJ7uslr3w3sd/w4HzrMCQMPV2iYJSKnSriGrOVjKUDfKY2Ikguj\r\n7DY7/2T58zPIFwcjurZ13Ro2XQ8s8BQy75SyH4BgHn14CnfCp8/GEJo0NxIT\r\nBLHzBILWPUrBUSgopDDfPfOFqzNP6FacZbhM1Wrv9zUveL97op7vjSKts9+B\r\nck0ZfjcWa5i3Ad4Nd1sfhcPjcYI9TnKmYJpe4cLCaqyKEzWWv5fpcgsv2Ki9\r\nffEouCSFwmal5T8VL6TqR4LTveDnWfZ6bsLuXwNccasIppvVOaUpfxMmn0uM\r\ngwIolKD12z98I7aIy7VF6WfOaF7lcFGbWo8TOoURppa+td1EILjtQTSdFx4y\r\nSyAgztxFBmWY2N9lirEYBsMhZdDMvpd+1LYZEMtnNSdxdzpwbYxrFDONGXh0\r\n7jiYe7NroPAMkjW7YCIlFJlKiIPMS5PhyFjerIXrXpTjeXClFRgGXlJ5l/CX\r\nXaSrvYu6tbdmdOeDmgmXnKpPVVZXo8N2YSdZ/X/+nr98xUMdr4z7wZBcCNL8\r\nsIZsuocy1XHJTdivgGELM9hbtTYnh0wxNdI=\r\n=RKKV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "5.0.5": {"name": "@npmcli/arborist", "version": "5.0.5", "dependencies": {"nopt": "^5.0.0", "ssri": "^9.0.0", "mkdirp": "^1.0.4", "npmlog": "^6.0.1", "pacote": "^13.0.5", "rimraf": "^3.0.2", "semver": "^7.3.5", "cacache": "^16.0.0", "proc-log": "^2.0.0", "bin-links": "^3.0.0", "treeverse": "^2.0.0", "walk-up-path": "^1.0.0", "npm-package-arg": "^9.0.0", "@npmcli/node-gyp": "^2.0.0", "@npmcli/move-file": "^2.0.0", "npm-pick-manifest": "^7.0.0", "@npmcli/run-script": "^3.0.0", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^5.0.0", "npm-registry-fetch": "^13.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^2.0.1", "@npmcli/package-json": "^2.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^2.0.0", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^3.0.1", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^16.0.1", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "3.3.2", "@npmcli/eslint-config": "^3.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "e38b63cabc496840bb329182fe6bee18ad9f3ee2", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-5.0.5.tgz", "fileCount": 63, "integrity": "sha512-96UwbW4ASOMg/ltORxx36njDiIy5HYdWR5HOvroYeVhoSOLYKwlixALowNK7ZBt49MOaJVCfLMyu9on71gQC/g==", "signatures": [{"sig": "MEYCIQD7IFTUTW5BfqkFuQc9+BOqqJYxm25kJuhEC0ZupNZK0gIhAJRsxcBXQ2fHjc2DTxsh4OJGS1UUkqnk+dXsvR8gYCmM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 412366, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiT0/uACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoK8hAAgD+jcPipCdqHsTA1U2/TWg99ScCCe7VRnfiUcCDVrs2PMA9x\r\nt69lNPP9VxVyJFVXl+/N7oiwmjZBfpwN2Es/wQVLIFnEXQ2JGJXI053aui7q\r\nfO+bjFQHxFMwuWT9rkCVFn2CLbCfyKEkFi/F1QSz2LQT7V/ruE/GUlS+JxKx\r\nT38+qVKs6R6t95DH/AOFEEHZDW21Sv3h25gLPoPKFUxCMECmiE+NQfv0vz7Q\r\nF0oLwb5QVVS3FtfzOFoJBYwslYMXj9J2Wzv8fS45+tLUr2JAWqkHh6KH54xM\r\n/KM/x2CM9rmh4+1zW2eXfCioxn9aqa8BRaoQRFs0U8VqakdurD0hhbBWuNp0\r\n7oaebHp2HoOKg0CD+iQ95Qj49Ko32WYcj9z7myThQAlmu9JhXvzu6i/gXjok\r\n7IR/Wd0N2y/DtZkRNgMHQO2l6OWN9cvE1lcc+RKM8S84VPy71P9q1qp8VChj\r\nmEbekcEHxAPbtccfdcZtjwlSReBLHTOYZ9YI2EzgNhP0Yzur0yt+W5VOvltT\r\neNLUe39pFcULfM68/iP7Dlfncd0v7j8hf7kQpWsj3wYgKLDwZpFMV92a5Y3E\r\nFqPqdgQJeoFJM7wJpn9Nh5wZbi6bPjxI081nAwqAGvfGPZFQEMqNNg8w7BFv\r\nT41um3QXTCcaXglfUTIUJG4NvKyZOz8pntQ=\r\n=uIT6\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "5.0.6": {"name": "@npmcli/arborist", "version": "5.0.6", "dependencies": {"nopt": "^5.0.0", "ssri": "^9.0.0", "mkdirp": "^1.0.4", "npmlog": "^6.0.1", "pacote": "^13.0.5", "rimraf": "^3.0.2", "semver": "^7.3.5", "cacache": "^16.0.0", "proc-log": "^2.0.0", "bin-links": "^3.0.0", "treeverse": "^2.0.0", "walk-up-path": "^1.0.0", "npm-package-arg": "^9.0.0", "@npmcli/node-gyp": "^2.0.0", "@npmcli/move-file": "^2.0.0", "npm-pick-manifest": "^7.0.0", "@npmcli/run-script": "^3.0.0", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^5.0.0", "npm-registry-fetch": "^13.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^2.0.1", "@npmcli/package-json": "^2.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^2.0.0", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^3.0.1", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^16.0.1", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "3.3.2", "@npmcli/eslint-config": "^3.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "131e74f4723a5076e826fe25586b97e6b16f2b68", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-5.0.6.tgz", "fileCount": 63, "integrity": "sha512-byBsiOgwEHb0zeEEYTkyyRiHIkvD/EoNpfUy71mrrMcBMWkaaVJoJHvJHu46MVPgz8WUdqJMwl8HhrHauY/TPg==", "signatures": [{"sig": "MEUCIHk4ay17FqZj2puQM6PBw0HR9gwVmRj31z9ipmgDLIz7AiEAots6Ok3zmQJ9OtZ5T2nBQAzr1y5BIeuhd159DNCzEKw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 412695, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiV01AACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoV/w/5AUHPR2zXJkwayywbO/fisu+PrvlckNkvQN6FiiJ8S5RYiaBl\r\nef98Wj6J7US0ZluHJY8qqewu6orku90MRlemZ9BXkKYjr8J041XFN2g1Oeo1\r\nntVemyVhbPYb7ck7phCz4j1fNyHaCsKlHaXUwpQEPqJyTz6M5VcbvTzTV1Gf\r\n9SFVoIRsJxw5FMnxE3YIOylmSdcoo1aZVqxDiVjpDCAXK1BFRlP3w/ULBJhJ\r\noHRaAfYMkIVTQYSYlkYmdEQSWL8e7P3ak6Kl5adSWJljeL9uqbNv1HZrFqtg\r\nka95EL2AOBWbU578tbn9FQFgl/WyO8FtJoLb4G/is0c8hJzY2fQ2HMxhUgLq\r\nUnHFHFmEP8hewHH3J8yWbrWN5tLWaLBW0/AnZd6tQhCb3VhjTcAXUeti6ygT\r\ne3vvDNTKaVPZ41a7SLhCtDxuGkkfnLuzp4Rdm6QzQVjexzLXY65VWbU08h0a\r\n7TRawxf3usu7gMTovqlbfX0pbAGiyPJExqEyVJiabZkXt5Nmzb3W+elKW84x\r\n5fzy22iEPVjAT7nBIVdCjAIQrbvPm60W5tMaWgxhfqfGVSV92c1i4/5U+cP2\r\nHizgz/swA+ebGf5MMlg4S5M0KSHIjgPx1YJnRcCTZSmtJVMsejCwIzrXg1Es\r\nCvqhJYh1JfCZyq9rXbyFIXV34/gfpjjNOKo=\r\n=x/U/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "5.1.0": {"name": "@npmcli/arborist", "version": "5.1.0", "dependencies": {"nopt": "^5.0.0", "ssri": "^9.0.0", "mkdirp": "^1.0.4", "npmlog": "^6.0.1", "pacote": "^13.0.5", "rimraf": "^3.0.2", "semver": "^7.3.5", "cacache": "^16.0.0", "proc-log": "^2.0.0", "bin-links": "^3.0.0", "treeverse": "^2.0.0", "walk-up-path": "^1.0.0", "npm-package-arg": "^9.0.0", "@npmcli/node-gyp": "^2.0.0", "@npmcli/move-file": "^2.0.0", "npm-pick-manifest": "^7.0.0", "@npmcli/run-script": "^3.0.0", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^5.0.0", "npm-registry-fetch": "^13.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^2.0.1", "@npmcli/package-json": "^2.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^2.0.0", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^3.0.1", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^16.0.1", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "3.3.2", "@npmcli/eslint-config": "^3.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "cccf66a23ca96b68da8660b07bef7cb5f4b7bebe", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-5.1.0.tgz", "fileCount": 63, "integrity": "sha512-MMutHkOmGyLJTBGXeeZ7khANmjlJtuqHScDATXaxChHMLF6MhAlw2Cca4TXlbeJuq0X1s7wm6ODa/vnBS3DVAw==", "signatures": [{"sig": "MEUCIBjssGBvVSQD1B8E9/T4TduZjMyhaBB6lAFA4+0zLOk3AiEArq8zlv2gP3LvwbfOht8+4+97XSFOkWjDt4gO8nN0pFE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 413752, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiYHmNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoeTA/+MNWCAbmP6frm91iR4StUU4d+JZ2m/agx3J4bip4p+5+8ycOM\r\ngHPQznK15dm5R7RetVZEs4z/2iRIBWhKonhkZ7oeaBPQ9RKj9emADM8/qStL\r\n/7i9d5+01+YpYjUpjDnqm6chKgVVf7XgDIiwSRTvWOy2uWCY7INq81jS7CEm\r\nAYnZWcKVuiGkMccLTBqqpKPz9ToyGuxQAOyUIN446vahXaFe45Pzw8gfGP9X\r\nb4apwOGRJT47UsSe2MvucYLLc1TCSef7WQNPWmr3R9SN9uP2E+rhRxz0B7qL\r\nI+qb2KBiG4lv4FRl5yJv9hPjSrAR9id4oFi++rmLkxY8s+M1rtEk4hyX5mFe\r\nf+Sy393wPtmrUt9brMJOeJkXqiJ4aNCOHPYvmDlZMCTXq0i45brg+eo/Cj8h\r\nBqmgRfbXdB4VneZv5FL9QB+heRjAee2vxBvb+7JL8eCWor9ILXqEzc0sQ/fc\r\nWS/wGhZMBccHLw3s85ZB+aXdA+fBC4H+rNu9fBIOwsXpJqrJg7hJrw3Xk5U+\r\n3gpIR0p56lfvqNZ/Wj6GoNPLKz6LIMikCMsofLCg7i4/Xskex6Zrj7pWuykq\r\nyLNkeRfFQie4ewWI+LKkHr6vBrVIGL2o5gz1GaiwAC4Z+5sAQU3WO7jDzp9q\r\nkYSo2FU4WEq5fuPSCrZSkl21hbz2aiTnYog=\r\n=g9Bt\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "5.1.1": {"name": "@npmcli/arborist", "version": "5.1.1", "dependencies": {"nopt": "^5.0.0", "ssri": "^9.0.0", "mkdirp": "^1.0.4", "npmlog": "^6.0.2", "pacote": "^13.0.5", "rimraf": "^3.0.2", "semver": "^7.3.7", "cacache": "^16.0.6", "proc-log": "^2.0.0", "bin-links": "^3.0.0", "treeverse": "^2.0.0", "walk-up-path": "^1.0.0", "npm-package-arg": "^9.0.0", "@npmcli/node-gyp": "^2.0.0", "@npmcli/move-file": "^2.0.0", "npm-pick-manifest": "^7.0.0", "@npmcli/run-script": "^3.0.0", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^5.0.0", "npm-registry-fetch": "^13.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^2.0.1", "@npmcli/package-json": "^2.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^2.0.3", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^3.0.1", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^16.0.1", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "3.4.2", "@npmcli/eslint-config": "^3.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "668dbf9ebf48ec0bbf19a5b4b19013e8c51f35b0", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-5.1.1.tgz", "fileCount": 63, "integrity": "sha512-qAlwjo95HuqYoOBJgg2dSZRQniHR+GxLdcIoEBQ1f79GFd1TFdIyqHtr77HBGsnGVqTpvBGD0A2vFbEJ9CwWlg==", "signatures": [{"sig": "MEQCICqMx/tATKNJF+Fi7gVtDaWoENMwt1gA0yfxMo+EF4lNAiBrftmfQhYhcCTgYixLItOz8/yg6abhksdoOkU6Fj88WQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 413752, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaFn7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqHFg/9GUH8WyycpxtiQCjiBeck8M7h1RwslrXLa24nnznaVluDO378\r\nUtC1R0RfEvQJVNmjZ0tdq2fnOwICoHzGeap62Ew+yCvxSCPF7KfJGHQ+wgt6\r\nB6uL/U6ouzks9vi4Dlgh+LipWS0nLgkJpCvZM0wE/sHd5u6xNeI0Rti49v0W\r\n3sGFV1JTNhGWdMiWXDJDAMYSsPqzNkPRYoh8Fo7uDQuvM/TJKO1OyylodS1+\r\n/zgj4MOWplYcz/rT22xUXpCacxUVkVoiMRRrfMdFYC307qOMU9l8rQBfXBRR\r\nSOpvKERRsnu5H9XSXRPVWUP8reqPMlKg8N7BXlXxrxFI3h7seU27DAhH1xVq\r\nbLx4J32v/Fc5xgWPnj419sCrKLN4AKzzacVeNiAmiKYs6XnBMiCYrxk/7mn0\r\nv/Zn8HRN2o5iOXZQdtcc3EUTwx1/JK+T21q2tjCVQBz97rifbkhmh8Mei30E\r\nUqirlx8NXmLk4MhFxwVzyDOwgzR9tlKuaw4SVdRiqaNQcSBCraCVuf4/fDix\r\nK0s00i2qWwa+DkzE9336cBQFAoktmrh8HFkfGDHKIxIYZ9BI7G1hazaSOTwA\r\ncrFkXd9V6Wcy6X9GKdUPwTdGyUN4dl0/WO4ECtufVraEwU1y6EyVfscx506Q\r\nB3+/pgGNigVbEbvfBJvK8KF3ur9flQ2XFxU=\r\n=db+T\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "5.2.0": {"name": "@npmcli/arborist", "version": "5.2.0", "dependencies": {"nopt": "^5.0.0", "ssri": "^9.0.0", "mkdirp": "^1.0.4", "npmlog": "^6.0.2", "pacote": "^13.0.5", "rimraf": "^3.0.2", "semver": "^7.3.7", "cacache": "^16.0.6", "proc-log": "^2.0.0", "bin-links": "^3.0.0", "treeverse": "^2.0.0", "walk-up-path": "^1.0.0", "npm-package-arg": "^9.0.0", "@npmcli/node-gyp": "^2.0.0", "@npmcli/move-file": "^2.0.0", "npm-pick-manifest": "^7.0.0", "@npmcli/run-script": "^3.0.0", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^5.0.0", "npm-registry-fetch": "^13.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^2.0.1", "@npmcli/package-json": "^2.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^2.0.3", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^3.0.1", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^16.0.1", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "3.4.2", "@npmcli/eslint-config": "^3.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "ee40dfe1f81ae1524819ee39c8f3e7022b0d6269", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-5.2.0.tgz", "fileCount": 64, "integrity": "sha512-zWV7scFGL0SmpvfQyIWnMFbU/0YgtMNyvJiJwR98kyjUSntJGWFFR0O600d5W+TrDcTg0GyDbY+HdzGEg+GXLg==", "signatures": [{"sig": "MEUCIQClFKr9XFE7qbNSYPxRNaLqvkdgyMs0YxqBGZo+VHnlmgIgL2Q57Ax3ii4UV8ju6vkRgaKLK5knpr4O1ejhonkoFFQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 415610, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJie8TbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqVwg/7BS1RzRm+f1FP913jWYADFu7RaTvyNcMLq7zmkf1tPxeC7S6b\r\nHNNDULPOgavAWXG8QrP/fZXGTJJS/ptCMPcnIiYHGcJNdODDTfuMIMkJZdql\r\nQsUkcEwAqKhqh6+eR2FibWLbdniUydmqc1yqRc50VneLOvwAHmFKPwv6i7xE\r\nTyG+nMap4zm5y73tWOU6haZRo8iSk42WJBHgMJv4HVa/j2+Su2dNDorBHo7S\r\nzp+O+Ec5CgrDEKPBb6q8uuyVptxe5mtguT3wKn1XWhbbekEjjGe8FWocXNhc\r\nv9D02g+xPMhIDReiHrgOupPpkwByZHHABA/CvYgueOmif6ijKVYSYM1fNi6G\r\nMUB5QxoU3fTLQSc/E38LmOFyc84NRV8l2YbqTK+zwIm5aD+xuKbiF+Eyat7s\r\nDofjSb8MDUitzP6vkP6qNG6oj0mASiIgwq5VwY3a9kdBCVPowe1OErpGcEhj\r\ncOVNL2Gznppz1qtkaifS7mIibx+fZlSRrC6tqfzLW/fRbWZb7iApq3j1rw60\r\ndN1Ak1KXdLvwNbPTcN448PQpa5m7n8jouCfOpBSYvzobHRT+tCFNFOslWiZn\r\nd3jXkJfqIRFEWEhG37Bnc4A0H1uXlpeX8GFbMUR1yEkZOkFUbvJrUPyYsgkR\r\nyzSRo6ZeQwjuhTvyIlGEhRx+4vzaROcOhKM=\r\n=bOSh\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "5.2.1": {"name": "@npmcli/arborist", "version": "5.2.1", "dependencies": {"nopt": "^5.0.0", "ssri": "^9.0.0", "mkdirp": "^1.0.4", "npmlog": "^6.0.2", "pacote": "^13.0.5", "rimraf": "^3.0.2", "semver": "^7.3.7", "cacache": "^16.0.6", "proc-log": "^2.0.0", "bin-links": "^3.0.0", "treeverse": "^2.0.0", "walk-up-path": "^1.0.0", "npm-package-arg": "^9.0.0", "@npmcli/node-gyp": "^2.0.0", "@npmcli/move-file": "^2.0.0", "npm-pick-manifest": "^7.0.0", "@npmcli/run-script": "^3.0.0", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^5.0.0", "npm-registry-fetch": "^13.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^2.0.1", "@npmcli/package-json": "^2.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^2.0.3", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^3.0.1", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^16.0.1", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "4f38187cb694946f551a825df17e6efd565b8946", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-5.2.1.tgz", "fileCount": 64, "integrity": "sha512-<PERSON><PERSON><PERSON>THov3lU7PtCGHABzrPqQOUiBdiYzZ5dLv3D0RD5I9KbmhTLcZI/rv3ddZY0K9vpDE/R+R48b+cU/dUkL0Tw==", "signatures": [{"sig": "MEUCIQDl/cji+KB72RF2mbsuVZHLmIldx470odAvYkg9ppSzCAIgDFzwLgPzFSuqnmCFjYffBj13649X3JygyXun0qsyUnA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 415856, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJil9qwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqwNg//Typ3iO0riF79sjeMq/oVppAxYOK7LhUdeTZxpLUQxYyH6nUe\r\nZmPdtMLEH7f91l0XTqLumzOi2lNuLPTsZOqZ0xsoYcHypaQXJZaVeWU5YdHS\r\nZX+TjTlzTryMDYzgVy/ljg78/+RPGvCtE2OA+FQdtoREOJPotRQYonOeOPHi\r\nW3TZerh+ZVu0IGrvQ4PyGc9fWw9orHTlhUstnvSJYI3j9ZjAUdlAfZbt5a0n\r\nZKHgIoncs7uZkYsktFzaGXTAO9Pjkk4OSwlyv6dBhAwZGQwY8Jp/IvudNKUP\r\nVjIz0u0h+jT74anH7YO9dI+e+wJMOSyxH4gWqCO2aCMcnkIIFfqVqwA3slet\r\nfAcSpl/V8F0hnEiKBipkK5mC8g4cJBiybQnp+6GXBtPU4+1GLsQgT/weNI7o\r\nU6LmspfjGhmCgtuK1rokH+62b0CSmPOU127CsMwTl56SEvUzSXBoGsUcNgnd\r\nV8i/nyTvSKh7nf2FfyKeVl3NtxXR/c+6iSV4kNfcwMoZCWGiYrkFIGNplc4t\r\nlUZ4YnuxfV/yMkyU6g3KB5iUyxeIstx8KBycni3JVXVeujm6fR24XvO6swWa\r\nYpxB8qdrPOjnxfdi8djlRk7KjNmjR6Ktevhtz0kc5jJHkwYrfmtyeThFj0Fm\r\ngF99ocu5AqRkv1Id0qyTnARtHQn445YmckE=\r\n=YlIQ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "5.2.2": {"name": "@npmcli/arborist", "version": "5.2.2", "dependencies": {"nopt": "^5.0.0", "ssri": "^9.0.0", "mkdirp": "^1.0.4", "npmlog": "^6.0.2", "pacote": "^13.6.1", "rimraf": "^3.0.2", "semver": "^7.3.7", "cacache": "^16.0.6", "proc-log": "^2.0.0", "bin-links": "^3.0.0", "treeverse": "^2.0.0", "walk-up-path": "^1.0.0", "npm-package-arg": "^9.0.0", "@npmcli/node-gyp": "^2.0.0", "@npmcli/move-file": "^2.0.0", "npm-pick-manifest": "^7.0.0", "@npmcli/run-script": "^4.1.0", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^5.0.0", "npm-registry-fetch": "^13.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^2.0.1", "@npmcli/package-json": "^2.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^2.0.3", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^3.0.1", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^16.0.1", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "12dbbd0b47ca78309d15933d8cfc9344275e5a81", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-5.2.2.tgz", "fileCount": 64, "integrity": "sha512-6WXFhQzqcvgcziyrX0BEBQjYef4lixfJv2q9I19yFHjcq1+IO0UdAVH0SkQtuSt4yKOoh1Koqto9w09h0uzDsA==", "signatures": [{"sig": "MEUCIBa0unka/aG7TgJZPChkYzoSbMecgUENgM8U3pCKb1mHAiEAr/caSMcVBz5GhQm2WlZ9/PhB6wBOtmbdTNPeTpRnnwU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 415857, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJis4n6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoBtQ//Wx/rhQAhF4IHnC5AxIMiwD79q4jGlrpMtEH3Pph9VdVWSxbK\r\nWEOI/dnRuREYOMpSSTnM+KMVvKqaR83oHuwHhB+wI/Wt1viIi7SFAR3KLsvq\r\nD4NCQGuMbICshRdvAOgi46kVW+275ZqWpwOno7/5YAkkc+V1wnrGB9VSdu93\r\nOgLCw3ClJOdDkIvPffUKwJn2DvHcdZvwgCP6svuwCVJmHKLtQ+LztLLNWA2G\r\n+olveC/bgWGhRmaaUEukoJ04fLwoZXyGllqlM89NWya6AZsiFT/3WCj75zAe\r\nKIvQy2gPIlE7D+HKqMDlBhawiJcq37TGNMI/3IBmJGptCOTQQHYuIMSCNoRQ\r\nU5HHV6WtRwm2wuhYEz+At0XolPxo/pYURzq5qXq/NpeMmNnbI28ZtuoIqzrP\r\nW8Mn8mq+ZqTmmVJy7Zh2k9W/Ox6dIFG9r2VWQP0fJHbUlowoC1S4Z7WOduMb\r\nxHKjYTKv9hTKGDEHpMd+WRqoL5HgGyzp881jWBgn7YwO856cBjHMtGokfiW7\r\nKeNa4dbChLoFwXtOm7ocfJHADx85fkbOzqZeEn6Nl7od3R2KInXDRUxukNKg\r\nzz+RRVvLH774PbEYA71CwoJvA14fGOJJHisqze9L3+kI489+8NcCMftDiNDi\r\nD+QtNT/WG+b60IXTLVb2ELEdin1NrktrxKE=\r\n=YJfd\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "5.2.3": {"name": "@npmcli/arborist", "version": "5.2.3", "dependencies": {"nopt": "^5.0.0", "ssri": "^9.0.0", "mkdirp": "^1.0.4", "npmlog": "^6.0.2", "pacote": "^13.6.1", "rimraf": "^3.0.2", "semver": "^7.3.7", "cacache": "^16.0.6", "proc-log": "^2.0.0", "bin-links": "^3.0.0", "treeverse": "^2.0.0", "walk-up-path": "^1.0.0", "npm-package-arg": "^9.0.0", "@npmcli/node-gyp": "^2.0.0", "@npmcli/move-file": "^2.0.0", "npm-pick-manifest": "^7.0.0", "@npmcli/run-script": "^4.1.3", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^5.0.0", "npm-registry-fetch": "^13.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^2.0.1", "@npmcli/package-json": "^2.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^2.0.3", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^3.0.1", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^16.0.1", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "3cc5d7b4bcf9783c41b6beec908d3de9592b4952", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-5.2.3.tgz", "fileCount": 64, "integrity": "sha512-2ywCfbN3ibONJ2t9Ke0CXIHa20yJH6/e3Kta3ZNabnFjm+5Amr+rw5qL52mVQBKxEB+pfSiZDsnqwyKyyj0JTQ==", "signatures": [{"sig": "MEUCIBKeIysXbfxdikSHxeCirFT9hQI2WVNsEUHR5nVTBTU+AiEAiHlbAES5IJD6Nq6rSyl7/eDosvoLtvob/T1gTY0oBDE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 415857, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJitMb9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq7eBAAiI+AhlhhUWiUyburAItbq6biNJ4TWkjlNYKlL3VqpnAakiRA\r\nSOcwXt9bJJHWqDu+yl2D8C9dKX+O6eZ0He3oX0qc14riBV8YdTkdZ9lRHDOQ\r\nav9rjkeaUWIrBhXNzjVAFLEL9tMYKtzAIXWkp5BzUH++pNhJVzPn0cCPE152\r\nNkj84w9KHquApQmyUZ1xE7+AmW31/Q+QEquwWRcbQ6atZGVxGsGuoHVzzZnR\r\nuwMKccsK5kOGRQJLIW6BDoYWZ9K+TY9fkSFTKhKGnHwqdDSStgCxrXYFvAF1\r\nK0EImbfVfZYqA8qhPMHldubUg8LgNN8YdhbjcAn6JnPseDNUQmAgAmFk/hIx\r\nIQuaG2iSsjj3Vn6qdjTjyIF0cr84yP77f3vaOgvwLyc9oAAc9WT7W6+JCVZt\r\nuCLe7lOevQUO2RNe8jTABnNFPS+2O1JJMgkQt5Zgi6luphz/2RhtSu17xgkl\r\nOJbAKVgfqijpQyeoGT6G6oh2uYkPf8YIS17naTpjYG61DHGznoEfPjeOqmgJ\r\nt4IulW9UE8vltn5AO1xGtaLm87VjVFNk6FyNdB1G2U7lxBbZ4rf4kxr4d68K\r\nmmHAfVYSor2PhfiYK45udtS/sndyHl7wWKWqPAKuBCGc+Hq1Gb0bX3EAMNAU\r\noSXGgk7f6jl5RmPEaXF31OLL6sMdwak1mEo=\r\n=R6J+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "5.3.0": {"name": "@npmcli/arborist", "version": "5.3.0", "dependencies": {"nopt": "^5.0.0", "ssri": "^9.0.0", "mkdirp": "^1.0.4", "npmlog": "^6.0.2", "pacote": "^13.6.1", "rimraf": "^3.0.2", "semver": "^7.3.7", "cacache": "^16.0.6", "proc-log": "^2.0.0", "bin-links": "^3.0.0", "treeverse": "^2.0.0", "walk-up-path": "^1.0.0", "npm-package-arg": "^9.0.0", "@npmcli/node-gyp": "^2.0.0", "@npmcli/move-file": "^2.0.0", "npm-pick-manifest": "^7.0.0", "@npmcli/run-script": "^4.1.3", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^5.0.0", "npm-registry-fetch": "^13.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^2.0.1", "@npmcli/package-json": "^2.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^2.0.3", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^3.0.1", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^16.0.1", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "321d9424677bfc08569e98a5ac445ee781f32053", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-5.3.0.tgz", "fileCount": 64, "integrity": "sha512-+rZ9zgL1lnbl8Xbb1NQdMjveOMwj4lIYfcDtyJHHi5x4X8jtR6m8SXooJMZy5vmFVZ8w7A2Bnd/oX9eTuU8w5A==", "signatures": [{"sig": "MEYCIQDGixg7Kmjh9rm2BbbUjyHou4NBhzwwXu8o1ii7is/6owIhAJqziSkz3op3CFJJKXczyyMC/Dxzh/H7SiZ0zUYXbAV6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 416959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJizZIxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq9TA//TtxswVtiSevlhE8XGl0NGoGwRDgqL2DVyNhwIrHcalzQt8eP\r\n71PHqO3LRqtS3XvscQIoT1/Qbnfc9ZDxeewnxTYrywX52FS+/SbqtT7j38qp\r\nHO2CfAAaxlqD37eGz6fcluY4tYyCO31DY3rynDbNM42/bUf9PSFDJAtUwlqb\r\neQ0adJB4Zr5j5Rwla678wvWqEC7wxA4HKTwW5joDuVQW3b8SY5fFA5Ty7Z2M\r\nOi9Qk/Y+DnnPiU2qnmqAHuwAZKO+jfufpN3gpPFpaj++trTPvklpoXf3wv49\r\nEKfURA+gRWPEojMXNpJ3Ln3u0hK+ltIrjUYsmahJQ9MlM3zu7+kF3vK5o/Dn\r\nSiTwt8hpK2GABakmk/oouaTulqdWOnIhz2+9zWJt6hOvhTnOf95HAIqx1Jd0\r\n15zjPHBvNzDG92uEZ3CAa7TRVeauoAxmAj0ST8yyvgBjs/kkQiQOuUwF2FeF\r\n0UQC7aAC10rdqR+t78DABjJA9Bl/Afyb/FYCtLAU3q5paxdcZh9lz5Rx5TqX\r\n9bc+/0Htio6Drj+NuFbLfp7s2Trcr8ZoNS410UH4Czzo5166BEYvlqxCJyj9\r\nql5uyjVy5uh7PMLZy9kIwZiu9K13uztLBeSYTMaB2cclNnzJe9ZKrPbMhWKH\r\n2U037qoptJuFoZDojr0FK+Do4w7xCNEV4ig=\r\n=OxcV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "5.3.1": {"name": "@npmcli/arborist", "version": "5.3.1", "dependencies": {"nopt": "^5.0.0", "ssri": "^9.0.0", "mkdirp": "^1.0.4", "npmlog": "^6.0.2", "pacote": "^13.6.1", "rimraf": "^3.0.2", "semver": "^7.3.7", "cacache": "^16.0.6", "proc-log": "^2.0.0", "bin-links": "^3.0.0", "treeverse": "^2.0.0", "walk-up-path": "^1.0.0", "npm-package-arg": "^9.0.0", "@npmcli/node-gyp": "^2.0.0", "@npmcli/move-file": "^2.0.0", "npm-pick-manifest": "^7.0.0", "@npmcli/run-script": "^4.1.3", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^5.0.0", "npm-registry-fetch": "^13.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^2.0.1", "@npmcli/package-json": "^2.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^2.0.3", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^3.0.1", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^16.0.1", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "6ce26ccd909dd74a5b37eb32f032ab2655a95709", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-5.3.1.tgz", "fileCount": 64, "integrity": "sha512-qTxXudG7HI46sHpuWCos/uchJONsRnz+Pf/jkMsu7nKA3BdtgI+pFRatozuliHrIR9uOxyTQlAm6cQojKmyy1A==", "signatures": [{"sig": "MEUCIE8IglWaKucp/5SfwXozQkOGlQNsGHjePn4anXIpSS8rAiEA7mtGIlQGoovFN3OjVJZQYfKzPLXUJI2kTvInIQAtXAo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 417232, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi4bIxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoyqRAAlBI6FA7sMAfdBtEqAD3lBc5d54JgxN5dDfMiJ1qd1yF0NXbA\r\n3wbymplQD/TR28e7OQO6uABmr3fDpRPSv4bkRyCrGQq3Gl7l7c4dZV+oFpn4\r\nP1eaEgEwYxe/BhYUOxnIQIQV7qb06asvLJX+y62mg8EkCnStW4oNhz/Q0EtI\r\noWwXaIC3EvG7YyZw/tVMWSPDUl+cXIE6RNJUXeePFmaiYAYk5F5sFpzI5eY0\r\n8hOGf72uRKQIkX1qgMSmhgAJwvw4fX9n9GARqbrJsKgXXZQFreLbvvh2lP/w\r\n8/lEF4Da55T3kWLCtB+/JUzNAVftVWhb23T+3UTmkLjDhCqGutyJQMgCYB8r\r\n0LmvV8SLfpaKOssuybTU1n0Y9AQHxmlTG2cvAqEt//eWJtdbbFj0tldfW5VH\r\nr4IVNr44PlO0laFeBO6Ai65qahboGp3+X2r8EMUWq+TxTCvSTttw3OmJOvBL\r\nbR/fxhM1peBEORn2ff01TekiySI6Gz0pPw2+v2jFIVpEice/ynK++Kgfk4e3\r\nR5URlyqMVWeGvFuH812mVc/tSAxV5cV5lpjpFm2UC5hGzZDScraLDdCp3yAF\r\nKwa0giNNURAyqCaWlCJukY5V4TbGgzZyaios17a3UuYynKifQjrY0KvZsbXm\r\nUaF43yQpwJvWPFS+aULTSemW5WFjpywRx2Q=\r\n=lCAM\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "5.4.0": {"name": "@npmcli/arborist", "version": "5.4.0", "dependencies": {"nopt": "^5.0.0", "ssri": "^9.0.0", "mkdirp": "^1.0.4", "npmlog": "^6.0.2", "pacote": "^13.6.1", "rimraf": "^3.0.2", "semver": "^7.3.7", "cacache": "^16.0.6", "proc-log": "^2.0.0", "bin-links": "^3.0.0", "minimatch": "^5.1.0", "treeverse": "^2.0.0", "walk-up-path": "^1.0.0", "@npmcli/query": "^1.1.1", "npm-package-arg": "^9.0.0", "@npmcli/node-gyp": "^2.0.0", "@npmcli/move-file": "^2.0.0", "npm-pick-manifest": "^7.0.0", "@npmcli/run-script": "^4.1.3", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^5.0.0", "npm-registry-fetch": "^13.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^2.0.1", "@npmcli/package-json": "^2.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^2.0.3", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^3.0.1", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^16.0.1", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "beef01e0b47c682b74cae4c9266f5720bf1cdf0a", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-5.4.0.tgz", "fileCount": 65, "integrity": "sha512-gWDDQjoRndukgV9DLDXLqgzY2sIbUJ0D7JNgNlLuMFbei4Gm7EWYulpOyIjYxdYXM9b0L3sAniOOlOVMkMNMXA==", "signatures": [{"sig": "MEYCIQCpdSi1kqpNnl9z2WQEOPp54gBMIN0J5ji8xv4htBf7oAIhANcBUaWWcpnFSyCZ1aW4QjyNJu5YZEjjYZD/pHSjlCRL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 435584, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi6phTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpmkA//WzkPfuOzwqn+DKppC868qvvQp/zloSHEY5JB00r1gKdK1UiN\r\n96uqhJsMv9WHeaKR7CP+LKX6RL+fE908d1Ovs4JG1MoEPgYHg3htzyTbX03P\r\n8V5UuJ56jqIJnUQ6+PMPnxPZihfwHlDbfvszB7IWTTPtqBc59d0OwvYgyJK0\r\njZ18SbTOAKoyx9vnn/TJXfb8CBo/+x7G+Gejr+N6pZpxw6EK8m9/hua3+AW1\r\nvcMBR/vBdDXyAvE3fkuY9j5yaHAazLGWlsTdQ/pCF3VxIvUSbl1m+8ydkB96\r\n+S94xSKLxk0K8FwaunCdWPIwC3S2ZHrkInHChcGRcYw+Eazw2mf6kIu67P3H\r\nKBS+/XSBS9rptP+lFxdw1QD5CitTv4ZrHkKklNEMvpT517hI5nAARh8gK9Dg\r\nlnhnLWBN0mFJFBnclZ10YTs+MJ9YsgP2zFvFeEwidXePoG1POUbiFVjqKGN3\r\nXph1sUL+7C0U9eaB4hvEiLlR8CTXZmpXwYdtPZnKpA1UoEsspHOwo9E7a6nQ\r\nyqoqx8IpgVaIHozwTGLj90viQMKvb0ZiPvOkrUoRjv/FELv6NCd1Mq4PhW3s\r\np3H7+o+3lU4PjDr5aTJIu6BpFN7EQlr8/lvc+oz/l+7K4PlnR9WLJp/+BcA3\r\n2isob72nMFzVYsW+MBjoglelQERp6hav/wI=\r\n=lGRW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "5.5.0": {"name": "@npmcli/arborist", "version": "5.5.0", "dependencies": {"nopt": "^6.0.0", "ssri": "^9.0.0", "mkdirp": "^1.0.4", "npmlog": "^6.0.2", "pacote": "^13.6.1", "rimraf": "^3.0.2", "semver": "^7.3.7", "cacache": "^16.0.6", "proc-log": "^2.0.0", "bin-links": "^3.0.0", "minimatch": "^5.1.0", "treeverse": "^2.0.0", "walk-up-path": "^1.0.0", "@npmcli/query": "^1.1.1", "npm-package-arg": "^9.0.0", "@npmcli/node-gyp": "^2.0.0", "@npmcli/move-file": "^2.0.0", "npm-pick-manifest": "^7.0.0", "@npmcli/run-script": "^4.1.3", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^5.0.0", "npm-registry-fetch": "^13.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^2.0.1", "@npmcli/package-json": "^2.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^2.0.3", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^3.0.1", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^16.0.1", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "e40f5379a900ef80c4a97c4e4ca0e9796b702861", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-5.5.0.tgz", "fileCount": 65, "integrity": "sha512-+Qg3h+J0o4NQMpiC4JgllnrvwBJ1utSBWOTa2xsWqbbVAcPTdEwPV3V0BWEhhrx5zt3hRfIwv9TWmYBvyxd4aw==", "signatures": [{"sig": "MEUCIQC4L5+WMthg30H1vew+1P2XxwZrO8lEu8fyhOAIoN1pWwIgP6HF1esLUpVtxmEiKlBHRZK/ISLIAVKpb7lVR60B4Dg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 435705, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi8+2zACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq2dg/9FzuhGY3olScJ9+0L5ELiwjbkPqdmpFuJLroaSnTJ1O6Hy/oD\r\nObXZa+5vuxp/t8hv5bSPRMz1o/h+FoHQ2NEhnXvYsCV1bI220eoPkNHE07al\r\nNFEuLDwSa+TGja6IUptMK8smV/UbF+mMAT/tZ/MpwSOqmsDEK4lN3Ar+FLrF\r\nczx3Urd7XYsvqqlMvDP1/hFNHVVo1iABLDPYbBuV9mXyEklg13CJIrRo/Pum\r\nvvWgZXkp26IF9z+VGSsjl5RLjmFLFOOrgdSlD9Rq6knd34i+NGpgKiD6MnBm\r\nIHecd1HAnQm9HHK6OGObh1hFjhkrWpDwqzAO4YpXGn5MCJnh6QwZTGBlOSMI\r\nBW2NHNN24L47EhWjyBihUpWJ403xtWEAAswOM78pA7AvHlcJr/F27mwS6oZZ\r\nPD96OS+Zcy5HtCGUSDgTL58jgBHiLwB974ZbZjhFX9VjcdpjjFLswR3aUGjy\r\nTvAJ9/9feQxFmjLAsojCMAES1nGoJAdA9ePmxcvFctEXQe4cNTRRbjEc/QlV\r\n3BpWaCGRxbgMhKJ/Laos7imHLxxUlRXeBYt534qd4kwtiqdJ9TjesrUnqYyr\r\neEIoyue3dNQS2C+jaReLb9J/KEa6PifgbZlYs+QHAfq+V8lVAl0zYvBy7tvh\r\nWqeh3VpPjDSukQkL6B+ytPHhuaxWBdGANPw=\r\n=tzhZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "5.6.0": {"name": "@npmcli/arborist", "version": "5.6.0", "dependencies": {"nopt": "^6.0.0", "ssri": "^9.0.0", "mkdirp": "^1.0.4", "npmlog": "^6.0.2", "pacote": "^13.6.1", "rimraf": "^3.0.2", "semver": "^7.3.7", "cacache": "^16.0.6", "proc-log": "^2.0.0", "bin-links": "^3.0.0", "minimatch": "^5.1.0", "treeverse": "^2.0.0", "walk-up-path": "^1.0.0", "@npmcli/query": "^1.1.1", "npm-package-arg": "^9.0.0", "@npmcli/node-gyp": "^2.0.0", "@npmcli/move-file": "^2.0.0", "npm-pick-manifest": "^7.0.0", "@npmcli/run-script": "^4.1.3", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^5.0.0", "npm-registry-fetch": "^13.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^2.0.1", "@npmcli/package-json": "^2.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^2.0.3", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^3.0.1", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^16.0.1", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.0.1", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "ace635279d0d548df164ac83464237fd5f0175ad", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-5.6.0.tgz", "fileCount": 65, "integrity": "sha512-gM2AxWCaXTZRZnKOHT6uIUHTkvRf+UPU2iC/3nC1Bj21zemnoKyJh2NvcG69UCcfs+r1jpx6hZ0dL9s2yPssJQ==", "signatures": [{"sig": "MEYCIQDkcYIS9exyz82wVFBuveSu8bZY9UoxbqGBguUahg1EBwIhAO0x0kJxhJ0qw+4tZglWhlb3jzHSUBJoPgMENMH5bI1R", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 435913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi/EAMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrVSBAAo3ZV31AbmU1MueayJ7NFFBCnJjKKI+EpXLrI2BgWp8Xedl1O\r\nkn5XT6thFWnbGk5TGGedg29sgaYFx5OcptbObw/ojygpgT/yuNBmw8setAy5\r\nGNvHC/A21V7by83T9iXuqgN6fiuQWfD/Q2x4xHDK6+E9MI1caQylaewAsTdh\r\nfGnVT0Bpu3HQzJOzCXWD0mpspauFQQWvPruzqhOZPpQ7XAtGkwSEdJXGV2wu\r\n1/ky5qAHmCSs8h0gKDJh9hTlP/taXPRuVykIgvR35djqmCcNz3IL+3Jssl8H\r\n/OB8Tpcz9jnM1ettLpVwuCwT88ZXMghbWppnxxygjRS8P8NfozeBAfHFNLQ8\r\nTgEOFnAt5FDPQiXjkggqf6R+xeBOMiUsbvy9/Cm/54/dl+JP6SkxeqUmoKW3\r\nzmxIZRGwD3WBFBjflzOMoBIUFo1oZ2hBSoMsBlSiVk0yAkGHU+eUMZr3Qm47\r\nI/AL8C8ew1btgNTJASciRgCTNhLk7gFxogW2XdZIpQyrQ4MSitYFCymgACFA\r\nmClLtLFO95CV4cZP290P1kU42GqnDUWPSWaaKVyVhtWBMNuAed5eWt4YPoQq\r\ni9fcfWaJGSTmwGP9V1doMrVou7kxH2frsfkdCsVnyBvbyn8oWXBIKDxwWgSK\r\n+ceOj3VdqPERQPlrpUfki/+gtRAIvpNjVu4=\r\n=4Ysy\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "5.6.1": {"name": "@npmcli/arborist", "version": "5.6.1", "dependencies": {"nopt": "^6.0.0", "ssri": "^9.0.0", "mkdirp": "^1.0.4", "npmlog": "^6.0.2", "pacote": "^13.6.1", "rimraf": "^3.0.2", "semver": "^7.3.7", "cacache": "^16.1.3", "proc-log": "^2.0.0", "bin-links": "^3.0.3", "minimatch": "^5.1.0", "treeverse": "^2.0.0", "walk-up-path": "^1.0.0", "@npmcli/query": "^1.2.0", "npm-package-arg": "^9.0.0", "@npmcli/node-gyp": "^2.0.0", "@npmcli/move-file": "^2.0.0", "npm-pick-manifest": "^7.0.2", "@npmcli/run-script": "^4.1.3", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^5.0.0", "npm-registry-fetch": "^13.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^2.0.1", "@npmcli/package-json": "^2.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^2.0.3", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^3.0.1", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^16.0.1", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "3.8.0", "@npmcli/eslint-config": "^3.1.0", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "8371fe6e38f2d9ebc57a77be9ac0a8276564aa07", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-5.6.1.tgz", "fileCount": 65, "integrity": "sha512-bFEihRTSzIpJY+EJjyUUiTHkuZfFyn6ROlPzyVVDsHmysN8JRZ0LdgA/cwNuTGndb1ddsUxhSENhLp5pJHhX3Q==", "signatures": [{"sig": "MEUCIQD8gQ19Ck9xKCsBIOQOVnV6TmRwNsgbjLR/S3e+a++vDgIgK/NHmPydhitc/qX4+BQbkYdeZzbPy3a3JUaVbWl48T4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 435499, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjD9bqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo2FQ//Q/29NJE75GPDuegfZ/UvmBZm1dQV/w3Xnl1AfJH1/OJPIr9o\r\nPvqPfVrKQiOs7oSuNUYgQDc99zoZRkJKBJhx+vf0xwddxfjysyrgR/JTkBYA\r\nqE4+aUrOtEY3bhZq9L9dN367yhqambBKTEDSnr82tuIF2ayUfTQ2LnbEhs/r\r\niCPTx4cqR049maOE/xLa0cJRJasnvo1icLXWoBm1t1vtFAqrz+zTtYoxUoqM\r\nRJaKikSOzJxnU3MaaGajDIqw8JD7cq0FCmvGe+zwFBmIQLzOC/f2QGzro9YQ\r\nc441+z92HI4NAQx+eWt1q1Y5Lx60lZG+inTQQVdE3yu+0OZ7ddaYcAMXduDq\r\nn4ctr7C2jRbd3nk8/Cv6i0t/HI7MA2a89Wc6V57mivRv9fLrK0UvkSfbjvtd\r\neb0YYMNhSOmFCGAU9Ey0GOb3bzBIIbFwpoyIdPYpvxP9g8dsIz3ePAJDOcKA\r\nOWf4E9q4U6WCn29OKOwkuMVs1VwpTn90IHrjEqjd9bj8kDNnZW5Rw1Malf6K\r\nBU/aniPGJHbz7HYinpY88nwdJSf8fMvwO2NDhp3lkdXjovfBSDCjkVucH24P\r\n69WXWK9Pq27jP0MUELyfh6QV5B2w6hLUPK9RO4LZ0epZw1lpC7NwKpw2nR73\r\nfHxWrzZcRSTFz7uv5p/bZc5Phuc4ZG2Q67I=\r\n=RNXA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "6.0.0-pre.0": {"name": "@npmcli/arborist", "version": "6.0.0-pre.0", "dependencies": {"nopt": "^6.0.0", "ssri": "^9.0.0", "mkdirp": "^1.0.4", "npmlog": "^6.0.2", "pacote": "^13.6.1", "rimraf": "^3.0.2", "semver": "^7.3.7", "cacache": "^16.1.3", "proc-log": "^2.0.0", "bin-links": "^3.0.3", "minimatch": "^5.1.0", "treeverse": "^2.0.0", "walk-up-path": "^1.0.0", "@npmcli/query": "^1.2.0", "npm-package-arg": "^9.0.0", "@npmcli/node-gyp": "^2.0.0", "@npmcli/move-file": "^2.0.0", "npm-pick-manifest": "^7.0.2", "@npmcli/run-script": "^4.1.3", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^5.0.0", "npm-registry-fetch": "^13.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^2.0.1", "@npmcli/package-json": "^2.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^2.0.3", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^3.0.1", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^16.0.1", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "4.0.0", "@npmcli/eslint-config": "^3.1.0", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "022f9aba17a71a8d3b4e15c12bb2709b2bac27d3", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-6.0.0-pre.0.tgz", "fileCount": 65, "integrity": "sha512-z7y2A1ir5xSA51vfm8KPjWyEst8ndjaCQb1xuHWn5Kl4dHf/PM5xzLC3adH4Ecvo+C7wSoSweP6+ISE8NjlwuQ==", "signatures": [{"sig": "MEUCIQD/iILvppyYmOvnItxE8a+Nckcux+J2NNsjxIGYyAuahwIgMJSHKQO1QSip2FRzeIfjm3vc27UKYcOYk8OO/hq7Hsw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 437149, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjH0+JACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqNQg/9Hni/UVE3f6xTiHWDtR0LjX8pbHtToTYLEfmJ1o4TTJP0fo9w\r\nRKeZXB7zlSEuY6eiOk6/rs50ajdnOm5m3cyrUAEot5lOMF+ktMUGmkLLrRcu\r\nNv52zZVB9I0TIaG/bzQkQP8W/Q3VFZZyQJ3Chamks78E8HO8xYKGduthFAh5\r\n3Z7Z6+fbPRx17wfguFFBV3k8bfBlRIYnxu9yh57iY2j7dpCMxpRKPWj9QyBh\r\nzg2kVhrjgF8UoLjoTq+ZV5mZAM/WeoaIc2jwY4bPOVHFKpME1Rvd8fNSbAYS\r\nKhQ7fnnHbUm+lYRWBAsyqJdtEXbXHpL2pLgM5C8kojL9/w56xJPtGXdp0D+4\r\nOlcwXOi5JACb8LkkvgKgonv+hoi7VghfDOnzxXXm3quHMe6+C5BmW12dSJ0i\r\nW3iFY/vpfxaiBPkgvcdl0ITlJ5YCHexruCxzR5Uf6w8mCK7/1jZEoEimI2AG\r\nkjT+Gm10KlyvwZrdALXdRGGhVb+A/qf27mI6fW66CfTy1lt2UBvH05odqkyo\r\nga87RxfhzRhziDICG+Iy/FFvyjvuSuaGbBRUm8uqPNoXSmiZorHjQDfGfmu9\r\nvv/lllHgdqxvRTBPX9z0yzM6MxLox5uzPj1z5gu2hsNcC0/nRhBgQ8qIMK1j\r\nDfbovmb6rLdEHpiQ2chdyMs2aH8a8QPWXQ0=\r\n=VMvV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "5.6.2": {"name": "@npmcli/arborist", "version": "5.6.2", "dependencies": {"nopt": "^6.0.0", "ssri": "^9.0.0", "mkdirp": "^1.0.4", "npmlog": "^6.0.2", "pacote": "^13.6.1", "rimraf": "^3.0.2", "semver": "^7.3.7", "cacache": "^16.1.3", "proc-log": "^2.0.0", "bin-links": "^3.0.3", "minimatch": "^5.1.0", "treeverse": "^2.0.0", "walk-up-path": "^1.0.0", "@npmcli/query": "^1.2.0", "npm-package-arg": "^9.0.0", "@npmcli/node-gyp": "^2.0.0", "@npmcli/move-file": "^2.0.0", "npm-pick-manifest": "^7.0.2", "@npmcli/run-script": "^4.1.3", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^5.0.0", "npm-registry-fetch": "^13.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^2.0.1", "@npmcli/package-json": "^2.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^2.0.3", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^3.0.1", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^16.0.1", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "4.1.1", "@npmcli/eslint-config": "^3.1.0", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "552b554f34777e5dcc8e68ad86cdaeebc0788790", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-5.6.2.tgz", "fileCount": 65, "integrity": "sha512-Lyj2g+foWKzrwW2bT/RGO982VR9vb5tlvfD88n4PwWJRrDttQbJoIdcQzN9b+NIBhI1/8iEhC5b8far9U0fQxA==", "signatures": [{"sig": "MEQCICGhzpfcq8zt0YuX3GUCMmuHL26zsBqTlIAomCPMenpBAiB3qEj8GIIq0ygnT4kUtCNsuJuqq9HHwM4FpqtJPBtWNw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 435450, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIQSQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrADA//cA0XQ3+g75wlKZ+Bzls2YnZ+XVSGD6VYouqQQwlPsIgxAvAo\r\nQuG5PFA7QA2JxLWP6ozoVc71xMFXMJGo8U9Er9lwL6nlyjnxsAD+oJPKr0Rw\r\nz0DoAKb3PRWYWxkleqVtsdbLCcY3UH2PCDf0PDNBhglQgCo+CZ0VIkAmWGa7\r\ne5LFyJ1pjscbAmVwce/hDkpYiilEgEDT0j8Z7uvtC/PRhwLSCiOdBIIWAsun\r\noi4yy3mmcdH619XnQ5OB771S8gt3eg758gbhWCfKi35um8sDIh6J/dqnszcu\r\n29vCmkkk7WQE48ftnRzAXOy2/TfL2B9+ow0vZEwV7tOWGMs2N8FmYcwaGDZn\r\nVnpkV2uVBjvTaQ60Q/9s9Xp47teZKpCG21plCJZWrqMsz4PjhX4YYWfFWsWl\r\nPsiKnCZqR6coZve1MC9DNW953F5qsP1Twmi9EnNnxebVcHaTewkGze1gXUrU\r\noMYUDVilGUr2G0ceD8ab927LrelVYmTU6DYqcj/Pw2CVhW2dOH9WHvOZtxco\r\nUw2paphQgopy3ECDnkVjpRzfZg/OcNS2J1x+CPLHZILIQXt/9COnRjzYfxrP\r\nQc+ICnL6wlYvHrMs4Fqg9mf0zpMwMg6QjNDGyWNovSqLEsB0sQmsTSsNSmXn\r\nx3mZHef6XZuhqVVYqLBSRqoX9Jzv7AZsvqo=\r\n=0OTs\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "6.0.0-pre.1": {"name": "@npmcli/arborist", "version": "6.0.0-pre.1", "dependencies": {"nopt": "^6.0.0", "ssri": "^9.0.0", "mkdirp": "^1.0.4", "npmlog": "^6.0.2", "pacote": "^13.6.1", "rimraf": "^3.0.2", "semver": "^7.3.7", "cacache": "^16.1.3", "proc-log": "^2.0.0", "bin-links": "^3.0.3", "minimatch": "^5.1.0", "treeverse": "^2.0.0", "walk-up-path": "^1.0.0", "@npmcli/query": "^1.2.0", "npm-package-arg": "^9.0.0", "@npmcli/node-gyp": "^2.0.0", "@npmcli/move-file": "^2.0.0", "npm-pick-manifest": "^7.0.2", "@npmcli/run-script": "^4.1.3", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^5.0.0", "npm-registry-fetch": "^13.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^2.0.1", "@npmcli/package-json": "^2.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^2.0.3", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^3.0.1", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^16.0.1", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "4.1.2", "@npmcli/eslint-config": "^3.1.0", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "55c2974051ea88d2b02b99e0cb33b5d81b4c8458", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-6.0.0-pre.1.tgz", "fileCount": 65, "integrity": "sha512-UbHOZqb05c67TvDPL4fmetSar/qhfq/1q8tFMVRkrGVFQ/6dtsy+cf/UbXq2uehFlgXfKuONj/kH+SMnijYeDg==", "signatures": [{"sig": "MEYCIQDI/xsaSxLZyrT3IYV4ajLLDugGY2jTf0reqmdfxOjXbQIhAJwCi9GGuCVFy/fqNHw6eQkQ2SsToqyombzkq9otGYJf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 437219, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjImV1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqBWA/+Pwjf7/omCbq8Mj/roawM9HjPryKSUctFSJwJvUbd1fIjmSbL\r\nPvryogvysxuU9YBROSfHd+de0ehz39GGk4wOAoLz3RVuGwSuUe1wwgSddXqD\r\niEqcShHt7wMSkrQN5Jzso4jbJc4wUfKgIQLGym4xFg1SCweaUSbaqCJBUbY3\r\nt0hmmnNSXM6JUmuYZFHjJp42eOXT3VWLZbw2jHJ3rbGBaV91rMGbFOwUVTo6\r\nmUVv/V/yKr85AXiSF+SVHwyh4vu7+w77Gu6vFrqN/2dyQtS8moKXpFiSetSB\r\nwr94Jdns40bs/roXYD5NHEj5EF89vD35zS7LGyapl6YSISjBs8672dL5abCU\r\nETbG4rS4BQ+5Xr3ZCuFPdggL84cIRnrP6Xp8EwYvnfCuvU70wks51GV41S8k\r\nZDKIVpVbFJumv1gk1OPbi5hz/BM6aifM9nP4C9VMhhATRs4wBl+L3506aIqv\r\nepK0qaKHbFQdACg26h4z1QfX6X8++B65lsllv1OplGbeJUf/CE9jjlcslb4F\r\nK9AOouxjtt/9yBGoHG1q3o7WBYTSwPrqwcuvzCZrHK4igs/w4DxopRr4j6xM\r\nTWaLZzL9CC30odAcVRaY6v6LkCHzxwYvoJeqerQByz0ji5TA2ypVpitJdo1Z\r\nBYSOx1Jdf03x/61P1f83m/hi5ZSGQscJ11s=\r\n=83CZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.0.0-pre.2": {"name": "@npmcli/arborist", "version": "6.0.0-pre.2", "dependencies": {"nopt": "^6.0.0", "ssri": "^9.0.0", "mkdirp": "^1.0.4", "npmlog": "^6.0.2", "pacote": "^13.6.1", "rimraf": "^3.0.2", "semver": "^7.3.7", "cacache": "^16.1.3", "proc-log": "^2.0.0", "bin-links": "^3.0.3", "minimatch": "^5.1.0", "treeverse": "^2.0.0", "walk-up-path": "^1.0.0", "@npmcli/query": "^2.0.0", "npm-package-arg": "^9.0.0", "@npmcli/node-gyp": "^2.0.0", "@npmcli/move-file": "^2.0.0", "npm-pick-manifest": "^7.0.2", "@npmcli/run-script": "^4.1.3", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^5.0.0", "npm-registry-fetch": "^13.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^2.0.1", "@npmcli/package-json": "^2.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^2.0.3", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^3.0.1", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^16.0.1", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "4.4.1", "@npmcli/eslint-config": "^3.1.0", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "924d970274c98a1e0446e299915f46f13c42d4a7", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-6.0.0-pre.2.tgz", "fileCount": 65, "integrity": "sha512-7C60KA5FRQg3+whPwJeEmNpV5UdSJ9kJR9W8SQ65tl54dGo77JiLfFXbhvDPpLIqBW+nmSrEAck+oG4Nm5Ij8w==", "signatures": [{"sig": "MEYCIQCIFsHa/1iiSJODUFas1ioX2ltGBgekVfHZ69MkcbPiNgIhANsvKYxHZSn3dTNHlcpnADqVFQfmVKl4fUdKPORQO0Yn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 443747, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjLUduACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpjig//a53kflsM58SlxWpgUlVVe+/KwdIAwFyFqzZB7rz7wKwQABEO\r\n8Z0q8QvK9mIt6H8TZPQPf5wwvbvs0JIA6/plPFpgBKJfAzor7GQQaYku20OD\r\nuxTLSz4tWE19Cr0PJLIFsKw4qnbX5NwOezlUZVYg0yKBKTuIp4dWz3TuhPNz\r\nY3VgiPhkDWmK3bj5HjSTjIdWsjPtq64lx7w7eGNBvvO7+D4UT/OGjo+hBkxw\r\nKVpD6ZbaOB5O9kFBRfIfojuh1jly3QULQJ80WMCoCsjc8FminXPTvswby4Yu\r\nq+p5jAiBD/ZVwMCbTALiiN4yRdavPeIR9uZ7hLYwMHnlm5/h2vEQU7hXCm0r\r\nF9UqC7mYymHX5mQNUvoGyb+H0Vo0+298ykSubnizfKXcDV/SEOP171HU6fQK\r\nQbXaF333iOBdOAHCeQN9S6UiJJWI01DVsmsTercHmL9VC8g8ehTGKxwa7nck\r\ndOJKoOWJ8XBKZOzuym3JZu0V7klU+KzOQZfj+ii8HenXt3icV5R+3mDbrM3y\r\nTudkgZFqXSZCz68F4NnbG0P+zEyJxIoF4WGx414JfvZWLljpgDetDeskrmIF\r\nDhgl8ijSh0q+n8JubYQfMOAYj8LTyL++Bg8AtQZaEmR/4q1QEhzx6rE3xOGl\r\njfMMoFC7HLhj4wlNNJMyIyQllIF6OuzBvDA=\r\n=xCqH\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.0.0-pre.3": {"name": "@npmcli/arborist", "version": "6.0.0-pre.3", "dependencies": {"nopt": "^6.0.0", "ssri": "^9.0.0", "mkdirp": "^1.0.4", "npmlog": "^6.0.2", "pacote": "^14.0.0-pre.3", "rimraf": "^3.0.2", "semver": "^7.3.7", "cacache": "^16.1.3", "proc-log": "^2.0.0", "bin-links": "^3.0.3", "minimatch": "^5.1.0", "treeverse": "^2.0.0", "walk-up-path": "^1.0.0", "@npmcli/query": "^2.0.0", "npm-package-arg": "^9.0.0", "@npmcli/node-gyp": "^2.0.0", "@npmcli/move-file": "^2.0.0", "npm-pick-manifest": "^7.0.2", "@npmcli/run-script": "^4.1.3", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^5.0.0", "npm-registry-fetch": "^13.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^2.0.1", "@npmcli/package-json": "^2.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^2.0.3", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^4.0.0-pre.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^16.0.1", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "4.4.1", "@npmcli/eslint-config": "^3.1.0", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "5bc53707c41e5c9600ba3da274f073a172dd9702", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-6.0.0-pre.3.tgz", "fileCount": 65, "integrity": "sha512-sDM+s1bsHpZqqW7B1yuN11isIDjHs9mC8ogzOSVFs2bJIEvT7pIvag5R4aDtai3U5nr3pFsDz6zM8oK+TiyyjQ==", "signatures": [{"sig": "MEYCIQDIiRc52zX0ogz4wfgO31gT5bHGv+W2iE9aLMH1OY6s/QIhAOt3/Nq4OZ2XkKs12vxhwDrxPILAsGhXjdY00CPfBUbI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 443815, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNlefACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq0gQ/+Iup9q3nuxOO+4EQtf+95h0pO18vBLGj0brE9fChq0jMA7a/O\r\nP+5wIKTqUu9MAlwSpts6cdqwfKSxfpklI1sXy3fIgYa6CQfJOmA0Cn6OzsQo\r\n9NOtXdYG29FkG0hdLfLDHTQl00Q3okbFTW5/KE40nSvR9pvLyuClyOMNe8hi\r\nFfZ4L+wEtNzlZO+ZtYr3UOAs97Qq4EfOXvnp6acdmkDHX72Sq+zG9YprLvAo\r\n+YMJR5YZ4zgBiE8U2PFlNBVs+YGf5zrWtZx7uvmmKPeerBnpCKoPJ1lJjLoF\r\nBocYZHs/LdpSYjtUiNhI5a1FRcRah86Gl8aRZkiPUYD36Gwq45NA474b+Cx8\r\nKXgaGQDfillRmtTPCRmyj+QCerl64B2Sru38nZbT0Nr18SHuRrarnzhBXCH+\r\n8Jxf4RpU6Cxtp0k4dM9l9gh3RxA3dtdeAxUY4qwlV6epaXci7y9TUmZj4qxd\r\nH3Gdx4fObkit+Km7hKsLsmPsNCc6+8lreGsKV/ZXtj2HmWV3pD7PNTpeurFU\r\nQJlbEcc7zSZ7FxxWOhBSs2wpPViEuQtzr2aEuMoS+xToXrF0XrpW2Y/6r91b\r\nrNKij005wbGt7YoEEqvcYNtkpbk4XaIGC3IMiNGoQ730/HFlL1Ft5cL+bqKJ\r\np5FvVn+6PYbvY1wPyad/Md+GjN7IxAA3AJw=\r\n=Hos5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.0.0-pre.4": {"name": "@npmcli/arborist", "version": "6.0.0-pre.4", "dependencies": {"nopt": "^6.0.0", "ssri": "^9.0.0", "mkdirp": "^1.0.4", "npmlog": "^6.0.2", "pacote": "^14.0.0", "rimraf": "^3.0.2", "semver": "^7.3.7", "cacache": "^16.1.3", "proc-log": "^2.0.0", "bin-links": "^3.0.3", "minimatch": "^5.1.0", "treeverse": "^2.0.0", "walk-up-path": "^1.0.0", "@npmcli/query": "^2.0.0", "npm-package-arg": "^9.0.0", "@npmcli/node-gyp": "^2.0.0", "@npmcli/move-file": "^2.0.0", "npm-pick-manifest": "^7.0.2", "@npmcli/run-script": "^4.1.3", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^5.0.0", "npm-registry-fetch": "^13.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^2.0.1", "@npmcli/package-json": "^2.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^2.0.3", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^4.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^16.0.1", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "4.5.0", "@npmcli/eslint-config": "^3.1.0", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "40bbe0eedc408c897f27828e0c236055fa012512", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-6.0.0-pre.4.tgz", "fileCount": 65, "integrity": "sha512-w1tHdMfo+B5U2+FUQBX/mXbQ62Jwh2xIvHjSLNnmlQJ/QUfwC/a87ne9U/2vQ8wSfrrzhDDa1NdFD6JoXfvTSQ==", "signatures": [{"sig": "MEUCIQCKeboM5iuZIFInrLDGrb9J0Lsyi7LElurrv+uBt33bTwIgNrMzsMS6SToTrqyD2kHNZJo8Dba7lSZmKlr5hhNEYLQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 444424, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjPeWDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq0CA//SiIh4gQh6aX0/doKodFF4V6VF11syFj6+z3RuVyAq7RrCJ9v\r\nS0SnAUJMQ7Zxvdw9jinF5iRPgfLfRcBZJhVnIGvSp1buSFAO8PITE4JLaH7B\r\nl9J94UrFbNlcMfUPRVasXzZxuSZU9Z1JRdB85kyF506WoEU4jbzRvrcqVNeB\r\nJbSHGtOBmMfXuDbNqPHzs/Yv6DxqOPLaqAUxYrqB3Muj4tAaEXfvV77LgjnJ\r\nCxmjzNoo9NMV9GObOU/1EAVV+9fCNw/NHSzaJkH8OeobkEGxE8UqhV95yEPt\r\n2fKWtf7KkUQkTd7T/k1ZGMPs/GTFUsbBKkKLQt0gA5KxPMSzAxgEDskDwkS+\r\n4Il98kVSm47VrCJd13foFTnHgK8Op8sAP5fKXWjDx3J/ZWJ0zXEWSyCKeSjT\r\n/9WdQV/zHhQZVK9AfHGT9YTFRmzEHnmM0Tkqp9ZWbAK6WePHFtAYl6gM96wu\r\nBDCoMhoSnX1dySChRVmeTEF0hpkIJL6EZ1cwIY5UuqG1DNYDuf7Z8L+l9RZT\r\n4upC02kmBJpIDUr6yGm0BaKKEC5TMh1K6DAwa9m+RmZcVZR8DLx4g0bc0/ym\r\nl0wAR+cK6sYxTKtQcjJu+eVtuFTRhE6sD3e4O758FLMEsXgx60n9mF34vx/e\r\npz2K+WnlGS3V49FuUWLcVAv2AO9x4JyT49Q=\r\n=kdHR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.0.0-pre.5": {"name": "@npmcli/arborist", "version": "6.0.0-pre.5", "dependencies": {"nopt": "^6.0.0", "ssri": "^10.0.0", "npmlog": "^7.0.1", "pacote": "^15.0.2", "semver": "^7.3.7", "cacache": "^17.0.1", "proc-log": "^3.0.0", "bin-links": "^4.0.1", "minimatch": "^5.1.0", "treeverse": "^3.0.0", "walk-up-path": "^1.0.0", "@npmcli/query": "^3.0.0", "npm-package-arg": "^10.0.0", "@npmcli/node-gyp": "^3.0.0", "@npmcli/move-file": "^3.0.0", "npm-pick-manifest": "^8.0.1", "@npmcli/run-script": "^5.0.0", "npm-install-checks": "^6.0.0", "npm-registry-fetch": "^14.0.2", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^3.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.0", "read-package-json-fast": "^3.0.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^5.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.0", "@npmcli/installed-package-contents": "^2.0.0"}, "devDependencies": {"tap": "^16.0.1", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "4.6.2", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "f08dce26db8afe268dbfced1a7c9efbb4dbd58d3", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-6.0.0-pre.5.tgz", "fileCount": 65, "integrity": "sha512-Xe8yVh60itAMe9YYaZtfZGxeR/YZ3rhiUQgQ5R15MRYwD9ktPLaNM0cwJ6RAB6RMFYOzagDcQ25GGelCDPhgiw==", "signatures": [{"sig": "MEQCIBFlHH827nIS1B5u/6sgg8s5o41MPDj5wAjZj2NLlvd5AiBPGdO1GSOG66Um2xVd/ees91l+pje9gHpm9UTaiPpxvA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 443293, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjUF8LACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrR6A/9F81j3kygvRmaoPSMNM4qV3wJHbJY0YX4Vfrp+JuLA/g+TfAn\r\n2Lv9B1WeKzdNvhGH+KUj5yhdjTijIrFDhaSyC9j+jfNcDpRr1wQd5bqLnEFT\r\nZohMpXFY63SElv8V7xGJRWAY7dKZ6xpW+6033nsWEmvj7mWwMcJNQSycX5yg\r\nL5FfbT7qFihGaoxGfyzvR/g5Id7fLcjGki+QjrBUZK+oAGqgVpUSn5c39SuS\r\nNhLiIclwxhLTKuvBiylWQIbAtls0rAP1d9jb0nj4te0jmb8i2Xkea5JJCAPH\r\nUKRYwEeMkQSykIZ7a9RABe8BtOF1JHN3PFTOUJH9QMheIOuHjNPvXq1daB02\r\nohUs88Tka8OAdluSKPFyP2KsvBrRiBndYNEvyXmpNx9COt3JDD8wPsA5rnoY\r\nPoeZApv/tZ2jSMke2CjtN3aBqejMc0ZCAqlfsh28PSdU8BuXwm66BMcRUunT\r\n9pasUtDPh7m/7Tmy/ruseWvnZeh/STh4CNYERoPkA8bN59rCvXkfQLGttMG6\r\nbkONdXm/8xLoZheDLVo1RFPg/6iEqKA7SNJb7w3uwT9Si0X+gb77fA2NKP/n\r\n8Xk86SfgUg7D3NKdAvybHRNYoV/8rFPpTm2Yaqcm3BVPrKqFZA/pt/uCiY/n\r\nFX11jiAmz+bQAUvE1vvk1BbxXXjsV7lUJJU=\r\n=S/EE\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.0.0": {"name": "@npmcli/arborist", "version": "6.0.0", "dependencies": {"nopt": "^6.0.0", "ssri": "^10.0.0", "npmlog": "^7.0.1", "pacote": "^15.0.2", "semver": "^7.3.7", "cacache": "^17.0.1", "proc-log": "^3.0.0", "bin-links": "^4.0.1", "minimatch": "^5.1.0", "treeverse": "^3.0.0", "walk-up-path": "^1.0.0", "@npmcli/query": "^3.0.0", "npm-package-arg": "^10.0.0", "@npmcli/node-gyp": "^3.0.0", "@npmcli/move-file": "^3.0.0", "npm-pick-manifest": "^8.0.1", "@npmcli/run-script": "^5.0.0", "npm-install-checks": "^6.0.0", "npm-registry-fetch": "^14.0.2", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^3.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.0", "read-package-json-fast": "^3.0.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^5.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.0", "@npmcli/installed-package-contents": "^2.0.0"}, "devDependencies": {"tap": "^16.0.1", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "4.6.2", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "57171cc06803a39f5983c628eb70b55441e31afc", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-6.0.0.tgz", "fileCount": 65, "integrity": "sha512-OqMJfAOjcUBm3fjJlUaeDHNAw/0YNrF6ErNFNq5y7wOBdsNSpJ65FQPp4e4yVF5kcLemhjkTZOQPrbWaFDkJNA==", "signatures": [{"sig": "MEQCIH0QIPsGg+i3pwkOO7SOO7nE+9/jhSjrB09vWv8EEcjvAiA5zLENdXNYwFq67STvHzpYjbPrXMIqg/NvpzpW37rCEQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 443287, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjUG78ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmooQg/+OwBN4UKiZ7VzRwUyLaln0i+AZTQwOQrJXnWkCHPbIyBpQjXT\r\n+8riSfXJkk54s7R0I9e6t1IBdhx6PpMaq9MiD3S5lK0rPK78dhAH5SokE5mP\r\niHGx0TKPEnicMhvyYG6PwzsyUSNBPjNkCykWAsBvVtIKHZDHU35oO75VJQnc\r\nfurUotEMVBMnaqMfnF44J/ufQ6/neOKVQ/CZwErkiwExOPBblUJDuxpMd8o4\r\nGWyGPcXf/LF1SvDOUamDaBSIiarwcL2Lfo7FUQglBPsVSUnXaMRg59COpx9k\r\nua4ukw0zMcBEF1gJR13b3qpdutBYnolvYi/mGy0E2Q2R6xqqDO6YLxBAwYxp\r\nCQUgv96bt57p9Qu/FgTW0KvTHM8l+B81vCDRctAWLmPE/Lg64ZGs6sPtq4pC\r\nWjlcXxEnJmTxqbrUXesJIQg2A3YQCYVUEYVk+NSmbLHGpcwu8XXyfYi33Gsj\r\nqdqcC8EwPlToV728jgvwxc3+rJHtSaUFg3A/CW5i0FVhaRWrzYPu0SrnXJns\r\nR8G5UfPqrKzeGQJ9HN5H0kwk+PqOMzvQQMjW/qdlDAc7jOu0Gs/Y5mxQYcUq\r\njRVd2yoFsrW+g0rVsAFjnXac+P5oBMijquw9XJ6GIVPxGvyxrpMH8fdE6P1S\r\necNbSMy8B/ETKfsph0jmepJUHlv2jiFFZjQ=\r\n=bTGX\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.1.0": {"name": "@npmcli/arborist", "version": "6.1.0", "dependencies": {"nopt": "^6.0.0", "ssri": "^10.0.0", "npmlog": "^7.0.1", "pacote": "^15.0.2", "semver": "^7.3.7", "cacache": "^17.0.1", "proc-log": "^3.0.0", "bin-links": "^4.0.1", "minimatch": "^5.1.0", "treeverse": "^3.0.0", "walk-up-path": "^1.0.0", "@npmcli/query": "^3.0.0", "npm-package-arg": "^10.0.0", "@npmcli/node-gyp": "^3.0.0", "@npmcli/move-file": "^3.0.0", "npm-pick-manifest": "^8.0.1", "@npmcli/run-script": "^5.0.0", "npm-install-checks": "^6.0.0", "npm-registry-fetch": "^14.0.2", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^3.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.0", "read-package-json-fast": "^3.0.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^5.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.0", "@npmcli/installed-package-contents": "^2.0.0"}, "devDependencies": {"tap": "^16.0.1", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "4.6.2", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "fafbd405fabb1b0b3d17461ac3373ec15f0f7030", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-6.1.0.tgz", "fileCount": 65, "integrity": "sha512-lesxjLt/3RmkdxWUC/BzqfWh0YXk+APagi+C/wsIQx1mim/ovRvP893k5njkLQCjmNSZHTLEqOohdsUxfa6Jag==", "signatures": [{"sig": "MEUCIQDAq++DBAVTdQoaMpNwjDTW6pRBvAMfYwNl3rDKblOQDgIgHSIqKrXY5BFAY722LxaBJiXZPSzoLqpRPjcQUwoN2uI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 443832, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjWalvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpx6g//f07PEpzD9kVVSC1rpEesIJBkp4AyzDWny5QGtNoFds86nl3k\r\nohvCqVk4qUDMeXhRpRpKZWOfmSMn5KDuAY1A+ipx1RzC6yAhvpQ5dfZMHZge\r\naDmAodGsYbdCy0pm3wqc3RFeaXr7I0KhVPrvNRqaKTFFJozDvlmWeJ0SfwzB\r\nbwbcjbWiXrhLwRFpaudLhN2jrL0gXLOEhqqdHAoruyqg8W/mEjFhzjQHN+5s\r\nXMEfONyPe+wtsxr0Rt+oMq47aNrzpUsboTxAGbchxMFK//NNQnuPJU0xkc/J\r\n320yLxSCA2OBNP9CEM+C9VPG+fdtYlxgoyMrbI1PLpvClPi/FDjg1cf8GLSB\r\ndAHmkcqXV0XCzt7nIBRY7Y93Gb6haJmpqw44nQw8xVaKPhx/RcuzBOND+cnp\r\nvapkYDW5TGXb+H9jeM42EACYxi3amTKWRR1ChoClgazx6LV5XwevrTApmOjl\r\nKRrOriRA8k4N0cbtSKoV9wri87JB99T+1sBa1kQvDnOHSs98B2U8N2YitfD8\r\na+v4WkSQE9XECwLRvujGYg/Frt86PYCy8UJnCUoLkwqFu9GgSjeWnNNBHo7U\r\neApmNvh48o4bl9YcfDvPjEQ4y9/BEpWFNSCU3qtBJptMzyUlWUYtW7VAiDtN\r\n81Ks9FCkm+TNJ7gZTLT4US75p+2neEBOJ4w=\r\n=w8eO\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.1.1": {"name": "@npmcli/arborist", "version": "6.1.1", "dependencies": {"nopt": "^7.0.0", "ssri": "^10.0.0", "npmlog": "^7.0.1", "pacote": "^15.0.2", "semver": "^7.3.7", "cacache": "^17.0.1", "proc-log": "^3.0.0", "bin-links": "^4.0.1", "minimatch": "^5.1.0", "treeverse": "^3.0.0", "walk-up-path": "^1.0.0", "@npmcli/query": "^3.0.0", "hosted-git-info": "^6.1.1", "npm-package-arg": "^10.0.0", "@npmcli/node-gyp": "^3.0.0", "@npmcli/move-file": "^3.0.0", "npm-pick-manifest": "^8.0.1", "@npmcli/run-script": "^6.0.0", "npm-install-checks": "^6.0.0", "npm-registry-fetch": "^14.0.2", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^3.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.0", "read-package-json-fast": "^3.0.1", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^5.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.0", "@npmcli/installed-package-contents": "^2.0.0"}, "devDependencies": {"tap": "^16.0.1", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "4.8.0", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "8b0895eb2067d105ed36f2071d5c83650e78541a", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-6.1.1.tgz", "fileCount": 65, "integrity": "sha512-dT7+4qICKQcBX5xWAw8O9+pzR4ejwUNRAWJWb98inY6fqGSwvhp1HAlvM/ZXTrN/BySbT0/TELShC01+icyxew==", "signatures": [{"sig": "MEQCID/JVVeBwqY0Nzfh9DxE9fGwoUbuSGtdGH1ndrrpvHSvAiAXL4YunBNeeki6pjYxc7f6DuW2NoPmjPo1c96g4z48Iw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 444146, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjYrWTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqluQ//QF10lN67150n0Fo66K8LjprRb43vSLLlhL8MAcO1qWLTlsIy\r\nofLVirHYlC4t3eO+Y5/Dxv7VlBMA0dSWd9CsVZGJL/r01E6gcF433fsVQkaV\r\nfrHSNP4FOplMvi0ydozrP7NjZhui8Lu0WljFBJ+0XMFpUldySig9LpmJ8ORv\r\nXZl56Xtb6NqnRGtCtUaVLAWrjXXgyLIQVK7LVdKC/KnOzwZlAIQicw8W7Efw\r\nTfVS2pBaicOcPj8GJbaWdsq0iJ+MveWLOp77uZwC2vknanQ94ofIGhSFjGrU\r\n9Y+mupaFQJwChHGARciCWI2NvtILJtlKjPlI6BKdPW5mvBoNYkjuqNV4kRiW\r\ne3uE+VhMS98S0k5j6RlEytaCw8dfHefTzm1kzkK3uZ1GLrE2AzN/4n1DgT14\r\nEKMv4SNAQncq3iW6sM2k3lSTZeLaavsaxOxt/4GV0vVd4ykG5hiOYVh5C05R\r\nStmucBxgtA55VVl+SKfPRBv0MzrBP6worj+g7k8mik9zo7Ourr3SmMDvII21\r\ntO8aXP76YULf/CVKT4C1kdnA65ICcm8+/MevUMdmkI4OAe1cubwoZSqpNmD7\r\nln/dHPsQozMJJ2RFZD81145PhKXU99/DqdljJXkhtbM8TFfKLzeS4QZ6GD1/\r\n3E49Sz022xgaCmwsoWOFSijK3kVgADLVKXQ=\r\n=eB3w\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "5.6.3": {"name": "@npmcli/arborist", "version": "5.6.3", "dependencies": {"nopt": "^6.0.0", "ssri": "^9.0.0", "mkdirp": "^1.0.4", "npmlog": "^6.0.2", "pacote": "^13.6.1", "rimraf": "^3.0.2", "semver": "^7.3.7", "cacache": "^16.1.3", "proc-log": "^2.0.0", "bin-links": "^3.0.3", "minimatch": "^5.1.0", "treeverse": "^2.0.0", "walk-up-path": "^1.0.0", "@npmcli/query": "^1.2.0", "hosted-git-info": "^5.2.1", "npm-package-arg": "^9.0.0", "@npmcli/node-gyp": "^2.0.0", "@npmcli/move-file": "^2.0.0", "npm-pick-manifest": "^7.0.2", "@npmcli/run-script": "^4.1.3", "mkdirp-infer-owner": "^2.0.0", "npm-install-checks": "^5.0.0", "npm-registry-fetch": "^13.0.0", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^2.0.1", "@npmcli/package-json": "^2.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^2.0.3", "read-package-json-fast": "^2.0.2", "readdir-scoped-modules": "^1.1.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^3.0.1", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^2.3.1", "@npmcli/installed-package-contents": "^1.0.7"}, "devDependencies": {"tap": "^16.0.1", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "4.8.0", "@npmcli/eslint-config": "^3.1.0", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "40810080272e097b4a7a4f56108f4a31638a9874", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-5.6.3.tgz", "fileCount": 65, "integrity": "sha512-/7hbqEM6YuRjwTcQXkK1+xKslEblY5kFQe0tZ7jKyMlIR6x4iOmhLErIkBBGtTKvYxRKdpcxnFXjCobg3UqmsA==", "signatures": [{"sig": "MEUCIQDPD3Jiy7P2uiLR5E1Vs/NchEVdJwiD6l2pCAz1pLecRwIgI6zlwaDVtQCoL4Zgf0eymaYvfAYW+IyhydzsORFcDbE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 435960, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjZC9nACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr4lhAAljIlf9gcsQfRggZBWbQSl1lIaFaQWXLy7L7dZPBQ5f3b4//U\r\nu1MRiHABBe/Uw4i276hkSv4AuL7kO1fOpQHt5yQYDSAFwLELn6ZOqX6Lq2Qp\r\n3ycgAuz37vIxT/UNBvngEabrgJOdWG2O6bxnHIXrqE/QuaYs5IF3eIWL5mKE\r\np0Ia0jHONx6UbJYmzv8+YKoPc77c5tardoyptU3iFj/ayPPnixBBNyp5kvHu\r\ne4nKRHk9xN4/mpUynIdlbcB9sPl1bovj3JsIgbrGB4K7XZjlzGRlXrZgD02n\r\nAdR3sz5wjKMrfd+Gf2149MK2IgIXNmCIOCmpyROu8XOTmJlBBnvMIsrLybJx\r\n39Uqe6FQIf5OPXlsKMdyF/i6pQ+G8gF6ak4X8DeWfG2/UaV8Hqfz3sDlNMce\r\ncMdRKkYxTEfI9tWyhSzt2g4OScIfTIXLDObeZwS5GM7/D0KzvVgdoi/UetPA\r\nwzKTvdmYy0T09JYCK2cdhn0eGfjrISQmuqcx4G/4HTbL2w/F1MT/6L6vdlnr\r\nxL2IFnZfrw90EtPdmCkKg9VKTYRHpBt6wsMts/uFFWStu1VJMNtt8X2GXvn6\r\nxkuFQFuQMFPTUusg7cpmUARyqLhWxw+h7q27kI/PXM8fWhRh9pTj5od7cbzr\r\nRrILqmpFgZcpIK25iP8PlBYbJfyeoNF5COE=\r\n=E1QR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "6.1.2": {"name": "@npmcli/arborist", "version": "6.1.2", "dependencies": {"nopt": "^7.0.0", "ssri": "^10.0.0", "npmlog": "^7.0.1", "pacote": "^15.0.2", "semver": "^7.3.7", "cacache": "^17.0.2", "proc-log": "^3.0.0", "bin-links": "^4.0.1", "minimatch": "^5.1.0", "treeverse": "^3.0.0", "@npmcli/fs": "^3.1.0", "walk-up-path": "^1.0.0", "@npmcli/query": "^3.0.0", "hosted-git-info": "^6.1.1", "npm-package-arg": "^10.0.0", "@npmcli/node-gyp": "^3.0.0", "npm-pick-manifest": "^8.0.1", "@npmcli/run-script": "^6.0.0", "npm-install-checks": "^6.0.0", "npm-registry-fetch": "^14.0.2", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^3.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.0", "read-package-json-fast": "^3.0.1", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^5.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.0", "@npmcli/installed-package-contents": "^2.0.0"}, "devDependencies": {"tap": "^16.0.1", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "4.9.0", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "7a7e5b5a5c6c39702dd86875dc38649f2ac7dce1", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-6.1.2.tgz", "fileCount": 65, "integrity": "sha512-aZF7LGOhX+/CFZkAUpqADQVZqgz75S9Gvc2Is2G/RJAfAvWlG2GSJo3w6bMo1SwzA//zYmRhpSAQjScRg5Zmeg==", "signatures": [{"sig": "MEYCIQCbTsTTjQSWuRajVfHgRpjnGHgr/pVMl3CxIqlaTByaxAIhANQ3s83b4bPrXtI8fu8gyEF8YBlDcp3lStm4CMTdbniC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 444179, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjbBxQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmorRg/+LuLHYIUAIc8j2MNKgcUYVsdmhQ1a/JV6D/w0hSuz1BokxUxR\r\nw/vI0b3d+cJKh4uYKkpgkwLSPF/469mQxQPHDCqul2Vb459sd087b8CVMoeN\r\njvO7LZJDNWqKxoZN2PypEJFjW+jJkiq3vKncmsXwN6JIMv6a3PG8UePwDeOs\r\nod4ODnbuOiZcY0q9Zvkff7nBvOYZczdJSDG9sgfwMUMS89DFI4eOm5yC1LIa\r\niKW2T7KtHnu79PKr2vhMCDHXQnPA6p1DnSfhRgp8Rj3J/6DYdKIcKIy8PBv2\r\n4ep3/46Oc0vMGN2WUL5/Dgfp4NC/4MhsQ/+Hbnjcl4JZEZiMEYEtSohZXPDM\r\nxP1xcnUPAk7P9QKgxdC7sjIFy3osko2TcYqhbOFadoAItooA693K8ZKonTtw\r\nRprGbcPQUqFXZGgzkKQUoKFVR/hxkfmivAW3qzQoG7DnWcX2WF1qqgFqUfhd\r\nkQU7sHmicBuD6PztoTEXRB8zXt4gKYVbSZCGbhfoc/noyXgZHVk+KJPzR778\r\nLkwBqsq6tn9cRlHqFFlZutzbRXCjkiwu/xLtZ2R/C78/5311maTwkEl6zGiH\r\n9dyRknD1ViK6ajhFa6KCNBmX9r5Xj0vxfx1MG5LQPJnwXqlAnm4fU2pZDqnl\r\n4A4/2fTbBmNjgf8Ia2fnvGE6jC3JrrugU3I=\r\n=PUKr\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.1.3": {"name": "@npmcli/arborist", "version": "6.1.3", "dependencies": {"nopt": "^7.0.0", "ssri": "^10.0.0", "npmlog": "^7.0.1", "pacote": "^15.0.2", "semver": "^7.3.7", "cacache": "^17.0.2", "proc-log": "^3.0.0", "bin-links": "^4.0.1", "minimatch": "^5.1.0", "treeverse": "^3.0.0", "@npmcli/fs": "^3.1.0", "walk-up-path": "^1.0.0", "@npmcli/query": "^3.0.0", "hosted-git-info": "^6.1.1", "npm-package-arg": "^10.0.0", "@npmcli/node-gyp": "^3.0.0", "npm-pick-manifest": "^8.0.1", "@npmcli/run-script": "^6.0.0", "npm-install-checks": "^6.0.0", "npm-registry-fetch": "^14.0.2", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^3.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.0", "read-package-json-fast": "^3.0.1", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^5.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.0", "@npmcli/installed-package-contents": "^2.0.0"}, "devDependencies": {"tap": "^16.0.1", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "4.10.0", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "526df463d66ee01cfcae1d214abdbf8e73065fd4", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-6.1.3.tgz", "fileCount": 65, "integrity": "sha512-oPYO8WO21aB9ojhREzCbzdNnR+SNuloOtxqQ0Q4Mj8tZuUPdTS5SuatSIpPGKpdtpLi5642hr2sirrikqj33Vg==", "signatures": [{"sig": "MEQCIQDU4olo3aSsqpoiUEZG+5EZuV8j26Y8wVcHYHrtcxil3gIfOjSIUUKE4azLpojwX+kKT1/9RCZYtiP8v4fbotNGgg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 444160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjdVBNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoHEw/8DMe+jubtOiEUlnM4Vi0oNyS4deD8X1HGy3y1rnxwQNSnfSCg\r\ndxw6YLuOyleqDy4sdBQVX631JZRhfAkWPdRjHD9ATcK4OhWxBz6c+mXdYu4J\r\nJ6PUGbJlRrsPgbSmk2zm4K30ZA1l4TM4kpkh2RUJ+/LIORInIpb51EEtpdgS\r\nsmRwSIkYHBH+FegPwbSMammX2ut5BoxUGsANSoTl0+5tu3zUMkTFh/Yhh8Ac\r\nzeM/wZkioySfNXlaIUtqxl5Ix9QyiYlQ8gt570TGsUiwK0Q5RQ4EwIUuQhAY\r\ntAxq8WDN7JtgOwCU8lgBBNIv/eTG28z4p4KgtRJSy/6JvBtxasdMGVeMNy7y\r\n9uG8xqWNOh/9k0dWo1KBzH0kaBVxGD+OdAuwm5T8VtgTvbKix+4+4c8AE/aJ\r\nPIWWTSpwV8FfDM2dAsOcDFdni+tDha9GDzxay2vfedVNnY/h7EdjnO4BZbuK\r\nRHmQNWgPw817txQboNV8PBX/+l0Y7Ll96pFRXGkgFqjCSB5o3jd2VCvo6PrQ\r\nu/3dz9x+V2NKgZINPtgCHGgYoE2+/TBqKZ9aSnXbHjjsNvCXHtV09qwhsi8w\r\nXz+3r+kw877tyJSarqg01H+sAbU7chczUxFHwrM4hy11bwYccim9hpYVl8zN\r\nIfFNEti5O72B3TGh3B4CyZ/L77Jif4+BRmM=\r\n=d4Jg\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.1.4": {"name": "@npmcli/arborist", "version": "6.1.4", "dependencies": {"nopt": "^7.0.0", "ssri": "^10.0.0", "npmlog": "^7.0.1", "pacote": "^15.0.2", "semver": "^7.3.7", "cacache": "^17.0.2", "proc-log": "^3.0.0", "bin-links": "^4.0.1", "minimatch": "^5.1.0", "treeverse": "^3.0.0", "@npmcli/fs": "^3.1.0", "walk-up-path": "^1.0.0", "@npmcli/query": "^3.0.0", "hosted-git-info": "^6.1.1", "npm-package-arg": "^10.0.0", "@npmcli/node-gyp": "^3.0.0", "npm-pick-manifest": "^8.0.1", "@npmcli/run-script": "^6.0.0", "npm-install-checks": "^6.0.0", "npm-registry-fetch": "^14.0.2", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^3.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.0", "read-package-json-fast": "^3.0.1", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^5.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.0", "@npmcli/installed-package-contents": "^2.0.0"}, "devDependencies": {"tap": "^16.0.1", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "4.10.0", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "22babc5199793aae33508deecd9a816edde4aa63", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-6.1.4.tgz", "fileCount": 65, "integrity": "sha512-lEYtEydnF+N5kkGa6wdjH80js//DcCT4lovuaXMRSPthhXv8sqYAzRGljboF3p+MlcoTOQVS7wzfhUbbUf57nA==", "signatures": [{"sig": "MEUCIDBVSdY9yPeIO0nMue1tVPh3WvZtd6/vBO81J3gLVvlDAiEAk3LauIRvOR33RXyjS1MW61EoYHq7cXiwvE0thbwZHnA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 443978, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjh+lhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqO1BAAo3lgl6KsYFfjv4mPmnWszkgI+2zdjC6nkXtqzpbzY66zOwBO\r\nuRWzgrR7PWYWt46rksFwyuVdoQ/dD0aIuolIKAx7GHEX0WLqAYdzod9vJga8\r\n1JGvzjBdUVbbnYHHgx79V1Vte+Nru6CLQDynTekKNpQdc7ZtfXldG7EAmTUR\r\nDFAkpt0D+IEG8XgLE29H1LkuSz7uOTOfUZ34TxD81tDI1gWz1wUewCse3umr\r\ntuXznAzKKp5mDGKT6X9NwVH1vllUy0fcCN7OVaQbwrWcJLmpm4ZPf+U7Ptb9\r\nxvWkw4hBbAD+/Y+pQ8Izi7K3mMVC87tk6SJSqWV2v3dq+eQV+Y5+zeNMvz15\r\nch5TlYDfYhiqzKvn3UnkTfqfWSBOiXXxdM9X7ukOZeAQfr9jW6aFVVlTVZTl\r\nOkd2yBqYFhkqKxyVUbFPNvunSbQpoJTPyBb1NnUkz4d27g200EEqroE0iimq\r\nN3kUBcNmXMkTgrl0jJXqW8OJ0SL9369W+WQZhbXGc9LRjeokLj5AIc+RCyNe\r\nTYoscxiBCHgtBVfb8wewzzRZDiqCSunZ0F8SaR140Ldv5lSSWAO4genxgc9g\r\n/n1x4yZd9EKMQOC29Y6zuqeNb/BFHCTIJyqs+SRTgdMwSGxhlCzKO+u9W01x\r\ngAOF+hIeLODgXE4g08EH/8VLaQ9cYDkPd90=\r\n=lPwl\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.1.5": {"name": "@npmcli/arborist", "version": "6.1.5", "dependencies": {"nopt": "^7.0.0", "ssri": "^10.0.1", "npmlog": "^7.0.1", "pacote": "^15.0.7", "semver": "^7.3.7", "cacache": "^17.0.3", "proc-log": "^3.0.0", "bin-links": "^4.0.1", "minimatch": "^5.1.1", "treeverse": "^3.0.0", "@npmcli/fs": "^3.1.0", "walk-up-path": "^1.0.0", "@npmcli/query": "^3.0.0", "hosted-git-info": "^6.1.1", "npm-package-arg": "^10.1.0", "@npmcli/node-gyp": "^3.0.0", "npm-pick-manifest": "^8.0.1", "@npmcli/run-script": "^6.0.0", "npm-install-checks": "^6.0.0", "npm-registry-fetch": "^14.0.3", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^3.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.0", "read-package-json-fast": "^3.0.1", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^5.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.0", "@npmcli/installed-package-contents": "^2.0.0"}, "devDependencies": {"tap": "^16.0.1", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "4.11.0", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^2.1.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "ff816f94574c69115f1ae708af4b101c7518296f", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-6.1.5.tgz", "fileCount": 65, "integrity": "sha512-bdnvRL1Js6uz+CDf0NdG5dIbuwYzHwzTV4/WS16JCDdfstjsYSa8J69bYHf9osUwza9zzg2HI/C5SSmeMN+yFg==", "signatures": [{"sig": "MEUCIQCG3+5MWQQ6VdyMDyNp72epbuXcUWyXmpCtpiuAaoDu1QIgX0QJLmoo7KfsZSHlBl1Tq6sZ0DhdozTVwOqr0Hozyrk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 444318, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkR2JACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp4rw//UaX/fJ/MHr5ewUqiXs3+/G72s36T4uWV51UnKXTpD6Uo4uc9\r\nfg2HMfwcdUJL63QWcJ0PtZzu6tkgodbpqwIhgqnDJy4bV6nkN4lT5BmmzTmO\r\n3u8xiIHC2wLecLTe1IaqQ6R/OpCHETk8DwH3wxkFGhC+a8l/OocIVh4JWm1t\r\nBThslYPyj82a93JMVLy/EFS4HynxYRzEi1JzbyfYpm2f/yyHAlG2pzxP1oRb\r\nYDLVV6/utUBJdg8PzkDNbiPcZRxoIvLMafkTEck0Th9w5Xdp4V2pefg5kJAB\r\n4bc6w+YzTIsj6nKu/jkA7KChZMs1/14Mvb+5+v3RBuU4mzDpgJN+FY9dTTbO\r\naqo/b1cfkfwzJ4q6UTeN1ctnXSFOp5iz+h6/Nljw4X/IoMl+txYSXmlSOBS7\r\nvDTEXdzpzQyRuVlOY8CVrL7uD/6nU6YU2kE10Xsv2cVuPSzQYJY2/4faDVFm\r\n1AF+M1aUraL3f/ckJvkGCjfdNu6QxVvFqcEEnQYx+Q4gLtKAFTwJ/GHMz/6p\r\nKSs1BQYx6ohbhOcpu2bNP6Ynqqijyj83LWy+OWqG+Ak8QCvdPEcSG3WrXeqS\r\ndWkU9Bm4YOanRFjytnCOmEB4TKweAGDXI9QHRQF9hCt2AEQq9AD5qbBTpOnd\r\n2yeY+mYna/9pqJ/lr873xGBDxWWIqGI8/a4=\r\n=vqWg\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.1.6": {"name": "@npmcli/arborist", "version": "6.1.6", "dependencies": {"nopt": "^7.0.0", "ssri": "^10.0.1", "npmlog": "^7.0.1", "pacote": "^15.0.7", "semver": "^7.3.7", "cacache": "^17.0.3", "proc-log": "^3.0.0", "bin-links": "^4.0.1", "minimatch": "^5.1.1", "treeverse": "^3.0.0", "@npmcli/fs": "^3.1.0", "walk-up-path": "^1.0.0", "@npmcli/query": "^3.0.0", "hosted-git-info": "^6.1.1", "npm-package-arg": "^10.1.0", "@npmcli/node-gyp": "^3.0.0", "npm-pick-manifest": "^8.0.1", "@npmcli/run-script": "^6.0.0", "npm-install-checks": "^6.0.0", "npm-registry-fetch": "^14.0.3", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^3.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.0", "read-package-json-fast": "^3.0.1", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^5.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.0", "@npmcli/installed-package-contents": "^2.0.0"}, "devDependencies": {"tap": "^16.3.2", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "@npmcli/template-oss": "4.11.0", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^3.0.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "4a92a8fe2e6a00f916581a1d127043eefb6405e6", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-6.1.6.tgz", "fileCount": 65, "integrity": "sha512-AvHsZV5ZPW/GvPfzgZFqXbiELx3Gh9dLL/nIHZdU2LnlqavReBfniMgy4tqxY7xJ71TB9/Ctkqfb7gIdku7Ybw==", "signatures": [{"sig": "MEYCIQDJCYxq9tCigEdMcVuBICv7OwDOwaa9DDnDNTqGnQIQdQIhAM3qNBUUZYB0/AwBgAeWhl5UaFJZ77h9KA+vlj37bPnV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 444819, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjwG32ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq3Ww//cY4VAdrs4kA9/ay8FtSlU1PNXKUG8MMkHsExTXSQ2eVhm2MO\r\n8eaG+eccaPusprx637kaSk82A2FtbARQiT0rrqaBz7Tk0HndZhV4xHIBPnUZ\r\nI7wY1KxpvW9/efJHBz3MYiSj2Wtp4fWBF/R/oWRiLkEA3KfEsRt3MzkfoHfJ\r\n09HGwpp3P09QKuwo8LS+ChvcOyCS6K9bq3nPsmGcQUf4LlwIWKTdfMuZ/SsO\r\nScGydoJRWZn2O/yHwPjoFwAo00nPsscdGaZuUBlrHUN4Ao1ivif32UPbrhOZ\r\napdEX5sMgoKX4D6I7HD2LTc5gU6daihiTGuSyPOHIGqrVuazk0dO+XJLbDUn\r\n+FWtBW+cdfYjRMK1uFaEQyk6Sr470WeSVujw0oXLxCHle0KtiyqcjGbImBZR\r\nyfklPCU+CKQumSIF7j9dXlRRXFf78A+QuIEQ8e2/IgKVriWhjSupohflAbfo\r\nWYdPn6kQsvVZytiyU595mf2JbcFAe/AdrQrAWP/jMANUXNxlnZxhh01LtNL1\r\n7rOCy4QGzz68C3eWQ2t2GAgbLW/hYLe0KK473IYNRjC8ZyTydX5TF7onJiuY\r\nc8erOuO/eJBmLbcnBwEsccJsJd13FQ/lJIL5MIP9Zc6tMdR3m4jiYajdphTE\r\nYPiB0TBgeTK/NQOy0b/okYe64NRskgphMuI=\r\n=7nSJ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.2.0": {"name": "@npmcli/arborist", "version": "6.2.0", "dependencies": {"nopt": "^7.0.0", "ssri": "^10.0.1", "npmlog": "^7.0.1", "pacote": "^15.0.7", "semver": "^7.3.7", "cacache": "^17.0.3", "proc-log": "^3.0.0", "bin-links": "^4.0.1", "minimatch": "^5.1.1", "treeverse": "^3.0.0", "@npmcli/fs": "^3.1.0", "walk-up-path": "^1.0.0", "@npmcli/query": "^3.0.0", "hosted-git-info": "^6.1.1", "npm-package-arg": "^10.1.0", "@npmcli/node-gyp": "^3.0.0", "npm-pick-manifest": "^8.0.1", "@npmcli/run-script": "^6.0.0", "npm-install-checks": "^6.0.0", "npm-registry-fetch": "^14.0.3", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^3.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.0", "read-package-json-fast": "^3.0.1", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^5.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.0", "@npmcli/installed-package-contents": "^2.0.0"}, "devDependencies": {"tap": "^16.3.2", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "tar-stream": "^3.0.0", "@npmcli/template-oss": "4.11.1", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^3.0.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "37698594170d3f3d6848b9a8f714abdadf7a4d37", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-6.2.0.tgz", "fileCount": 66, "integrity": "sha512-hFo2SMBI1BeGQ9K9gbTXwr0Cnf4Ua5mrtMvv534T8Xk5jjET9IJKgdMPt8kxAI7nyfBGhkLFQM/p5bcWXuBa9A==", "signatures": [{"sig": "MEYCIQCyz2HRe92esSwWUI03fQs6ZwANvB+OOMOZs9i7AVcd5wIhANldWmkGuTUAMSEdGxx7aGCCL7cSLsN9r7lTzdAwhZKU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 461760, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0Z5kACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo6BQ//dylL5CTJie2/YLlitJs/ASFpfT0bo5khnxNPiXo/gHvEuk6u\r\nDIFP6tOUcIgxq3iWoeP3cvsFe/oZwLVKlMw12HnwotYRW+103TigyZmVxav0\r\nOT9+KqeRzyp4SZU456ZeIR1vjLm/rpSeWumj/xOJzrx1usQsbkgSbdcn8Ag8\r\nLtvWbrgK0hxQwLfDVfv8eIIh4sgYUoMz98SxpJy7oCDl1ARHYqfYsZuWHbsU\r\nxWdAPgYOQuEw5Ca4fu1H+R2Kfma2Uc4L8+3Ya9rdzWt3fhTiDOic1bkX5pMs\r\nQkTdl5MpwkHk/ewmlzVvCgDcWZrzXiPgkUUjiq2gxRmqS0l/Tsk7o4JWBA04\r\nHqqlFI3JIJkemklRJOZgS3uqrptMnd+SmXECZySJXVPGWYZJcQ3BSqiPXIru\r\nnftyTVzKPTz4eMjYsjuA1BksWfffMmCB6GafNZ8BhuzALcp+AHwgE7MEt4yD\r\n6rZQP6T/pZfKME561fJp8lc3PQAsDFFcM4l1hGc1RzRx4P2yVWHWh4GIUtAl\r\nJqw/fwQociXVUAg5fz2zMz0KKGA/aODCS/QKVhCPJmBpv8ySeFWi+dYhWwNq\r\nlASZGI3+oJtdHuS9sQAQKeq6KDKzo2V9ziC1lMXEU31huSyQn+N0QlIqBywb\r\n8J1sT/NqrWj7D0qzYD3HuwenWoshftuqeiw=\r\n=Slhl\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.2.1": {"name": "@npmcli/arborist", "version": "6.2.1", "dependencies": {"nopt": "^7.0.0", "ssri": "^10.0.1", "npmlog": "^7.0.1", "pacote": "^15.0.7", "semver": "^7.3.7", "cacache": "^17.0.3", "proc-log": "^3.0.0", "bin-links": "^4.0.1", "minimatch": "^5.1.1", "treeverse": "^3.0.0", "@npmcli/fs": "^3.1.0", "walk-up-path": "^1.0.0", "@npmcli/query": "^3.0.0", "hosted-git-info": "^6.1.1", "npm-package-arg": "^10.1.0", "@npmcli/node-gyp": "^3.0.0", "npm-pick-manifest": "^8.0.1", "@npmcli/run-script": "^6.0.0", "npm-install-checks": "^6.0.0", "npm-registry-fetch": "^14.0.3", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^3.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.0", "read-package-json-fast": "^3.0.1", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^1.0.1", "@npmcli/metavuln-calculator": "^5.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.0", "@npmcli/installed-package-contents": "^2.0.0"}, "devDependencies": {"tap": "^16.3.2", "nock": "^13.2.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "tar-stream": "^3.0.0", "@npmcli/template-oss": "4.11.1", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^3.0.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "caaa29eff7fb369653ccf1ddb1bca1b279c7debb", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-6.2.1.tgz", "fileCount": 66, "integrity": "sha512-5bpihPqBgAMdOoM7pCeziVVP5NHmAqnjTUq9v8qrzrFcesEbBRpeiD0h3M4tw16NtQ6f024sI6EvWASUrxTHHw==", "signatures": [{"sig": "MEUCIEt7S55iUBC1nZwfX+Pj8rB6snpM+OgJETFCRrgMRPCnAiEAqcyLtbhUv/uDgT0CyTXvul65VE8sXQ4ABdxvGXGTXZY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 461668, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj2zlHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqvag/8CnwJFi5ud6G/4vT2yWKpOjVRkUsgd4+FLEHS+wTxJ9ZI2AEH\r\nkya9rqozxL03PHZg5LOwJmM+lHigou5Kyyo1CxQ7XWI/pUF45S0iqbkUEJ6n\r\nyD4rDaH+eV5A5PEdXKg9Rx+L5rkhuuHn40Vvqq9L0GcgOWnSruHj+e3XJ5rM\r\n/RUL5SkOChQw/zAJU0xlr6WVZmaem6fqxTNxEDK40NN6hqclxFVDS01ojHQJ\r\nZLHEZmEHJV7nR/kUIfddk6qeE5VilkjBZFbaFWl3ujdKBJkWCedZyeAh23Qm\r\nAScHr6cygvnWR0gw5GbbbkKNDbvLzuYneOYsuo7PKoaQczMM2Uln7f7QIk/B\r\nuq91usL9Sf8e+OiopIpSiLmHihLLj5Iy6a7X6MlpCDQPVx07mT3N0/DmK4iK\r\nrXNukfVhYIuM+HSHHuzW+Kp8Lt1G+bMkha5eFFYIHDjZWefx/olPs0PlvSC/\r\nenOQrXWafhXILxJuTmSBuIGibt8B2qzdDTtyHH/U892+rlrnRGDmPkf6AK7Z\r\n+uFuAr4+TX2GYgQzB3ldQaTb4msrQecHLbNeoY0Pjy8em38iZ1Hvi43T6dks\r\n0L6OI8WpQBbTG2c4in7YIeMcVmhYB/7TDtkZeU3q1qeAUCM9ocrJEAqqSvBF\r\nB0r2DTPTw9J7HAE/Ml44jNrORUis9p5Vd7Q=\r\n=199n\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.2.2": {"name": "@npmcli/arborist", "version": "6.2.2", "dependencies": {"nopt": "^7.0.0", "ssri": "^10.0.1", "npmlog": "^7.0.1", "pacote": "^15.0.8", "semver": "^7.3.7", "cacache": "^17.0.4", "proc-log": "^3.0.0", "bin-links": "^4.0.1", "minimatch": "^6.1.6", "treeverse": "^3.0.0", "@npmcli/fs": "^3.1.0", "walk-up-path": "^1.0.0", "@npmcli/query": "^3.0.0", "hosted-git-info": "^6.1.1", "npm-package-arg": "^10.1.0", "@npmcli/node-gyp": "^3.0.0", "npm-pick-manifest": "^8.0.1", "@npmcli/run-script": "^6.0.0", "npm-install-checks": "^6.0.0", "npm-registry-fetch": "^14.0.3", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^3.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^2.0.0", "@npmcli/metavuln-calculator": "^5.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.0", "@npmcli/installed-package-contents": "^2.0.0"}, "devDependencies": {"tap": "^16.3.4", "nock": "^13.3.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "tar-stream": "^3.0.0", "@npmcli/template-oss": "4.11.4", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^3.0.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "3ea668e7d368376a8477bd7bc3389bf634603b5b", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-6.2.2.tgz", "fileCount": 66, "integrity": "sha512-cjK9CuA1cEovr4E1ljdgYU1UBdKJb4KvvULAEYOM1/qU0Que2X4DKyjIDBEy8MSWRY6PyaQ8oLGhwYIh8gUEEA==", "signatures": [{"sig": "MEYCIQCJm8dfnPgwiHWXAI4Li/qgNs1ohsqzgkYCsSUtCKBzDQIhAIogdMRt4NK+CBzGFjCrF+RrhffHeV2edhBD/149CdeB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 461545, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4rmTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrItA/+IQsZIH+kog7x5vK5MJHmOo9aUlIAjMSCpxHm8TVIL7Nxy2Gh\r\nKBzIinIgW9e7rPxVKP1DiX+a5varD3iHfRC1S3Dt1J7FhIwuMZ9mQi+WZfGz\r\nZWTXmCqgDgxT4Q8v04oHnUiUCT5eTH561u5qA1k7ZNfzP42yzD6hMkYtDTCM\r\nXearj/W10LkMhLqQOdDZdYe02rZrDyXjaRO+OKu7ZMpczGvx8JXeC0p27HvA\r\n6wU412rUvm8bBMBXCG4Loxzx0Rc5tZnJcNPNA1SM/l+IP3W6L+mIjPwMvRLX\r\nUAkeYm6IYT/ApcFpLBmBla5xTTBjWDYaDj5NJY65uvyge5NkMJEPyZJSI+Vu\r\nbAVNQmHVJGBGN78ft23PsxFtzK0a5f6EgiL/oNzGQLrzjA5X7dmBJ75gc3D5\r\nZ1Fxvkw1w2YtN7OycDag3xGJT8z9w2dQbixbnaX6psn+efzAlMORBm/SXWqV\r\ndVrbCFO5Gzjvg0iv2lttFR3H3g6Dd+pHn0a+feyaoB7X7/R4hqWp9If7waFf\r\nHLjhxCfq0fkGWb6w8Z+vV6NfxiMPJnBJgsY86+gHQzULdwZbQCdumhqcuVlN\r\nLHXT+5RRPtYMqA01LMCryQwQzvEfin1Hntw8uVXcOs99JF67citmClDOTHTK\r\nGcLmebEmqLX/LuFmbnfjnu+7sg5FjUp9ptA=\r\n=OoQb\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.2.3": {"name": "@npmcli/arborist", "version": "6.2.3", "dependencies": {"nopt": "^7.0.0", "ssri": "^10.0.1", "npmlog": "^7.0.1", "pacote": "^15.0.8", "semver": "^7.3.7", "cacache": "^17.0.4", "proc-log": "^3.0.0", "bin-links": "^4.0.1", "minimatch": "^6.1.6", "treeverse": "^3.0.0", "@npmcli/fs": "^3.1.0", "walk-up-path": "^1.0.0", "@npmcli/query": "^3.0.0", "hosted-git-info": "^6.1.1", "npm-package-arg": "^10.1.0", "@npmcli/node-gyp": "^3.0.0", "npm-pick-manifest": "^8.0.1", "@npmcli/run-script": "^6.0.0", "npm-install-checks": "^6.0.0", "npm-registry-fetch": "^14.0.3", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^3.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^2.0.0", "@npmcli/metavuln-calculator": "^5.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.0", "@npmcli/installed-package-contents": "^2.0.0"}, "devDependencies": {"tap": "^16.3.4", "nock": "^13.3.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "tar-stream": "^3.0.0", "@npmcli/template-oss": "4.11.4", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^3.0.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "31f8aed2588341864d3811151d929c01308f8e71", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-6.2.3.tgz", "fileCount": 66, "integrity": "sha512-lpGOC2ilSJXcc2zfW9QtukcCTcMbl3fVI0z4wvFB2AFIl0C+Q6Wv7ccrpdrQa8rvJ1ZVuc6qkX7HVTyKlzGqKA==", "signatures": [{"sig": "MEUCIQDIdWxaVq0KDYP6FfrXx6bHyE349Ib4jLsbutBFMpV9wgIgPJiI4Zxckx5aMKbhxIARZjADd978KhqyDIwEJvJqQHg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 461704, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9mWiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmocSQ//UVh444FCEtP6qTYkjbUMBmsb9d6CLaIFlMt/Lyxg6TYNyO0T\r\nNVbVHOz9AEmTCDHPpwKfP3JWHM2hZy5A027Er94sU7MUE0yW2WvRaEL8p+BI\r\nBMfGxO5EAx+ZvmcbiwAB3pNP54aATI8am/c4W4gccqWHOFyBuGWipurEHM8r\r\nDWUqP+wpXZdPedF3CEuUp3YZ9Ag0l5lFxk3wb8uHOmfktTKB9p1jkDgf6T9K\r\n/esty0GUhYEJtNV5P1FBfvlJBFx5kgPn1kRDQDuTvhpnfx/J/T5JnAcUZ0Mi\r\nXVzupLNa9CfpXlpOA0l2GAL6JC4dsTipk/Kj3edGDUHGTCKsQLBKjZd1tlS5\r\nL027lMtcsbY2rfdvyF8d8Z0Sfv4igdvur6W932Y5sFqOOBPVnFnVtWIUE7H2\r\n7dnnMvUOo2j7hT9SGG5MuU5R57Fis2wBfM2N86BeZnRwtFH9JWXz0Y5qvo5L\r\nbybQ2IDlfp0zmWIBiS/wjesx2Img44A5TRkgMO4bDoXhgMMGrzl+XseXLLlv\r\nqAEbf1QjYePPkPTrjRKfs+JYwMUzxmBTJMLJAevUmVEparUqBIKOqvRswUCp\r\nd1DF8ntS2tmiDlFlh9D5A4YJn0P4Lt+N0qAZXnmA7+kr9WF0IdSTTIp0N67v\r\nUB2/KXJe2orJiyhnw99j3GjwMH4Zr1DEO94=\r\n=UsaW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.2.4": {"name": "@npmcli/arborist", "version": "6.2.4", "dependencies": {"nopt": "^7.0.0", "ssri": "^10.0.1", "npmlog": "^7.0.1", "pacote": "^15.0.8", "semver": "^7.3.7", "cacache": "^17.0.4", "proc-log": "^3.0.0", "bin-links": "^4.0.1", "minimatch": "^6.1.6", "treeverse": "^3.0.0", "@npmcli/fs": "^3.1.0", "walk-up-path": "^1.0.0", "@npmcli/query": "^3.0.0", "hosted-git-info": "^6.1.1", "npm-package-arg": "^10.1.0", "@npmcli/node-gyp": "^3.0.0", "npm-pick-manifest": "^8.0.1", "@npmcli/run-script": "^6.0.0", "npm-install-checks": "^6.0.0", "npm-registry-fetch": "^14.0.3", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^3.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^2.0.0", "@npmcli/metavuln-calculator": "^5.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.0", "@npmcli/installed-package-contents": "^2.0.2"}, "devDependencies": {"tap": "^16.3.4", "nock": "^13.3.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "tar-stream": "^3.0.0", "@npmcli/template-oss": "4.11.4", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^3.0.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "a9c567a33012a7df5af86dfd1a3308686b056d92", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-6.2.4.tgz", "fileCount": 66, "integrity": "sha512-8nwyvv/WyGupcD9CRm3SOyFVgysHehMbkFgNXwz6db8Dd4hCkG8bptgPL5dveJzRUiA2AGKaGhX5CQsXaCyI/g==", "signatures": [{"sig": "MEQCIBPntdOvacEe5HvgMhOxJfGrPs7VfjYQfY4B5DPiiQKIAiAqffFSACaWf9tSHIdF4BRLx8q9tkYOFcuedAMuO+OrNw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 461709, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkAGkEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmphqA/8DkH/ZzO2kN/AhvOzugvM57I+AH6Lf7duJAB6MWkrnzwMzgYr\r\nyKe1SpFpPJeg4SCXylyTaQOKvd32ApugF9d8uVJmaAPETY3WW8Xu3rhBO8GA\r\nIGMigxdtV2H78Gvz8FoK0tvyGHHB0Y8mDBsPcvo6qO2LRD9/28fttrgk4A9r\r\n0RAwoNQbhv8hCaP3pIM0Zv7AI5DwlZcmm1REPd/6Dbjo6v8xr0tkWvu/S9cp\r\nEswQo/iJzDT+8GFKAy7ioDtZTl9uE/10rdk8onojLrP7gc96jA+EFdYyL8t4\r\n6BtrXtwt0Lh8hgI1lEnggVFOCuUS8s2Kx4H39tnD10yOnZu+RZ+E3cAfNh5K\r\ngyXmqXJXwnbKJX/GOBuyAAbRo7+Hhk5N2EQO0Ue5U/w+t4wAyfVMSw4is3fz\r\nUljnpxGfDltRV+1TIn+ybwQD6Qkf+Ndl420ElrZAqS0OwGVnhjWiVAznyv+c\r\n/bXbsELVemsDkLrbJ6w2T+l89j8kkPmBjGbhMpStOXmHdaRcZH7t4j72e3/i\r\n8LT8AKYp9TAOZt+485MtLFfYJLITmFcurxFKuk5UdPmQRoL0D9FGjsfJt0Nu\r\nxEnhv0/lWz3IPs3n4TFqsIv8AttpxOwx2gGQInp08F+Laf2CZJL+mRsD4Y4i\r\nJ2KtkoCE/mXCJr7i5mO3VuCJ8wu96Ud6bR4=\r\n=d4Yk\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.2.5": {"name": "@npmcli/arborist", "version": "6.2.5", "dependencies": {"nopt": "^7.0.0", "ssri": "^10.0.1", "npmlog": "^7.0.1", "pacote": "^15.0.8", "semver": "^7.3.7", "cacache": "^17.0.4", "proc-log": "^3.0.0", "bin-links": "^4.0.1", "minimatch": "^6.1.6", "treeverse": "^3.0.0", "@npmcli/fs": "^3.1.0", "walk-up-path": "^1.0.0", "@npmcli/query": "^3.0.0", "hosted-git-info": "^6.1.1", "npm-package-arg": "^10.1.0", "@npmcli/node-gyp": "^3.0.0", "npm-pick-manifest": "^8.0.1", "@npmcli/run-script": "^6.0.0", "npm-install-checks": "^6.0.0", "npm-registry-fetch": "^14.0.3", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^3.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^2.0.0", "@npmcli/metavuln-calculator": "^5.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.0", "@npmcli/installed-package-contents": "^2.0.2"}, "devDependencies": {"tap": "^16.3.4", "nock": "^13.3.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "tar-stream": "^3.0.0", "@npmcli/template-oss": "4.12.0", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^3.0.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "b35c6ec42feeb7b44d6a6fcd2ea323e35b256d80", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-6.2.5.tgz", "fileCount": 66, "integrity": "sha512-+GPm+9WrDnl9q+LvuMB2W+roVinHTGDdYWOtYzRfpAnuiqaATFbH14skpXjlJ7LvyUcyd1oJhuGq6XXJLGFNng==", "signatures": [{"sig": "MEYCIQCWV8v4JTUUGkTUSNPyl+ugHSxEqwaVW13YUMfs0Ge66QIhALb40SkIGd/6cBMaLCoourl5+ZCNIV0C+PV4/NNHRsCG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 461949, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkCN4NACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqCtQ/5AKPXDiNi+JphkmFi0RQYwZFA86EUV16eV3com1J4Kxcc8mPy\r\nXUVyhx9xF4j6DbuUirisLHOA8l9wmbved7VDGm8hGPM9xssB+M6uk8taE5wq\r\nRLE6E/IYrRhIFhEM/vyJkqJfHdV7HzHiCJUsgnQ+0ogXANRsWd+BxhCHlYta\r\nYnC/pC/A7lX26PfsG7RGP5PBOylHu9v1etZhLOjf/oHcK7iS1MjndsQ/K44a\r\n5mEt8nUkErcCI/wIkUFIjxo1WF/WKHJql8t6Vb0Q8CBw/ndWaR51CftFBJuI\r\nXZtno/FHEkdz3CS679shG15dQhQmyRa6IZcPCykuzr748JNk7HxXnFOMHuMw\r\nXtdsh1MWt0BG2cD1XEd9pO7hlFECYp9Hh4IQgBBxNO5wxj2wSumyrukEELeW\r\nX2fbhEXwtQcXaDkXkFlRUCyrK+D89EfZzPjPLyoDhWkjxksjhjMPbSJurDN3\r\nHbWVaCVpRWHi6XjA7Px+7ETaLPedi+9bWaz+AZ8TAsGqlk52/BYGIOqNSg9J\r\nLPOtV6vwtbE+g3lXh4jyYN7P9j9+8iHK0BaTiwfD0N/u4xjDcdJ0izKwGPwT\r\n0EyCNM3wnPO81cphF5abR82C6ITO34sysMcC0HratJfeew8nzlTrD7zwQPDk\r\nAFzy0kUXJnQtV5/BhLTiteFbqe8iO9UvkSU=\r\n=eZKl\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.2.6": {"name": "@npmcli/arborist", "version": "6.2.6", "dependencies": {"nopt": "^7.0.0", "ssri": "^10.0.1", "npmlog": "^7.0.1", "pacote": "^15.0.8", "semver": "^7.3.7", "cacache": "^17.0.4", "proc-log": "^3.0.0", "bin-links": "^4.0.1", "minimatch": "^7.4.2", "treeverse": "^3.0.0", "@npmcli/fs": "^3.1.0", "walk-up-path": "^1.0.0", "@npmcli/query": "^3.0.0", "hosted-git-info": "^6.1.1", "npm-package-arg": "^10.1.0", "@npmcli/node-gyp": "^3.0.0", "npm-pick-manifest": "^8.0.1", "@npmcli/run-script": "^6.0.0", "npm-install-checks": "^6.0.0", "npm-registry-fetch": "^14.0.3", "promise-call-limit": "^1.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^3.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^2.0.0", "@npmcli/metavuln-calculator": "^5.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.0", "@npmcli/installed-package-contents": "^2.0.2"}, "devDependencies": {"tap": "^16.3.4", "nock": "^13.3.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "tar-stream": "^3.0.0", "@npmcli/template-oss": "4.12.1", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^3.0.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "3b5d4cc9517c26252d3dd01bbd940ced93aec1bd", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-6.2.6.tgz", "fileCount": 66, "integrity": "sha512-fkKRY8Y8EHs9tydcuZMn6G1JhZfcoWDXgJzaRiZRJf+WFA1MqOS5IWL2wMjGUtF2V/vMVWWgBT1O4TEKpJK9PQ==", "signatures": [{"sig": "MEUCIHlxIjlIlpiwKk3x/lX9HEpUHNo2w1jCHMH8JPfyVr/KAiEAwU11NBUWCpdK4dBFanGqWOBOIc7E7qV3PaQ9AskG32M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 461949}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.2.7": {"name": "@npmcli/arborist", "version": "6.2.7", "dependencies": {"nopt": "^7.0.0", "ssri": "^10.0.1", "npmlog": "^7.0.1", "pacote": "^15.0.8", "semver": "^7.3.7", "cacache": "^17.0.4", "proc-log": "^3.0.0", "bin-links": "^4.0.1", "minimatch": "^7.4.2", "treeverse": "^3.0.0", "@npmcli/fs": "^3.1.0", "walk-up-path": "^1.0.0", "@npmcli/query": "^3.0.0", "hosted-git-info": "^6.1.1", "npm-package-arg": "^10.1.0", "@npmcli/node-gyp": "^3.0.0", "npm-pick-manifest": "^8.0.1", "@npmcli/run-script": "^6.0.0", "npm-install-checks": "^6.0.0", "npm-registry-fetch": "^14.0.3", "promise-call-limit": "^1.0.2", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^3.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^2.0.0", "@npmcli/metavuln-calculator": "^5.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.0", "@npmcli/installed-package-contents": "^2.0.2"}, "devDependencies": {"tap": "^16.3.4", "nock": "^13.3.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "tar-stream": "^3.0.0", "@npmcli/template-oss": "4.12.1", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^3.0.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "434b66fb90c598b25d4d0ba116b124bf504d6e65", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-6.2.7.tgz", "fileCount": 66, "integrity": "sha512-7Gp1lZEweW1GzoMOo0Z10zAm4SKZsVYUWtFSCrahXmV5H3Mp4Pgwg1nIgkXj4LkjqT+fLsXmjK6rQBauDH64Jg==", "signatures": [{"sig": "MEQCIAYmelmhWeECJfeHrCR/YIWRmTxSVuHOdXd7BU8lgRizAiA0XY2vpKAFhubqkGmKbeS8F/JgllufsrapIG7PtXiZjg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 461949, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLdHLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmopURAApC2vR1FsHguil9vOsxg2r0zcWLrix4wKU05CUG9HfauG4N5r\r\nugpylNh3sglZ3EMarZGjR/ujv+62d3Z8jo6RS2l32DFg/AHbv3oPB+8wYnBQ\r\ng1nXyWHMiXoxH8J7tcgyTABN+0NGbyvBZl81A13cZEyTU6WciwVZmsU/nWZ3\r\nTB7NF+bG8PzvrLdmEuj0b/wQjF2uGhLnhUXPxkXXO7VGX5v6yczNOLOupWdh\r\n2+GhTUzLKrAnFzxXp0sP8mFgvuKiUNGI0UUJFMPeUa4geWRZIVYee392nNOT\r\nsVvL/EzfcrghRU0cdbPpfIAsdoUd2vwDtsr86uCe4V0AutAW8mT1bZUeUVH8\r\nBIecUUcWY0FaMGXifPWarYe1WAz90V4nMmDNAczDDZIPMmIY3N/HgojAnF8A\r\n1X13Zo480qN+zwVReSKWfqNkGXzzrHID6XoCMZmiA8gmuUw94BaJIc3eTwSH\r\nme0LfzTAc0CB8NwllLO9iE2YLeKIFFA+WUFowgIu3lBwFlyALtD3R3062W0y\r\nzhXA2eF/b35W1I03RCl+114VZ4Bot9B3LLamfkianRc99u93Uy5lSxE4a56i\r\nr9n1cdRkYNbd1ryeku/I/vAOyss6Ze8q4CaLnKMttlDc0B+AkoiZdmDsOvaq\r\nVpvLuBiiN4FpMGX3ljRB/UYOwC9yPm7X3GQ=\r\n=gGMO\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.2.8": {"name": "@npmcli/arborist", "version": "6.2.8", "dependencies": {"nopt": "^7.0.0", "ssri": "^10.0.1", "npmlog": "^7.0.1", "pacote": "^15.0.8", "semver": "^7.3.7", "cacache": "^17.0.4", "proc-log": "^3.0.0", "bin-links": "^4.0.1", "minimatch": "^7.4.2", "treeverse": "^3.0.0", "@npmcli/fs": "^3.1.0", "walk-up-path": "^3.0.1", "@npmcli/query": "^3.0.0", "hosted-git-info": "^6.1.1", "npm-package-arg": "^10.1.0", "@npmcli/node-gyp": "^3.0.0", "npm-pick-manifest": "^8.0.1", "@npmcli/run-script": "^6.0.0", "npm-install-checks": "^6.0.0", "npm-registry-fetch": "^14.0.3", "promise-call-limit": "^1.0.2", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^3.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^2.0.0", "@npmcli/metavuln-calculator": "^5.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.0", "@npmcli/installed-package-contents": "^2.0.2"}, "devDependencies": {"tap": "^16.3.4", "nock": "^13.3.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "tar-stream": "^3.0.0", "@npmcli/template-oss": "4.12.1", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^3.0.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "bfc53ab964b157fd220c3c138b88e958c209be8b", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-6.2.8.tgz", "fileCount": 66, "integrity": "sha512-z8qQIbuZBk476/S5rATAS4zwWb7QHzYbtH0468K9c8gfxcH6VeZF5a6MqUiEcdg41OWwiBHqq0I65jdH2Alidw==", "signatures": [{"sig": "MEUCIQCKHpwA1EJg6iSdosSEHq7uOkAPCS2H1XhHQZWlLLCeSAIgNqqB2tn/NBh9x7VvajUY5Dyas+sLJnbkSDX6x9NsyJc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 461959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkQGJyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrgyQ/+LYzcuGM01s4z04P/in60Qmor5Ko/53g6DecS3ExoTv1zXplE\r\n1A/Mud3dXj9NIwE/tDXHv34MI8LhkT3Z5Dh6Hq8g+TCUNICdeaxyiSV3AND0\r\nJPXW6iyB2R97rQh8W5XyS300Jh4g85dvZy9Iyo/vuw4MGhkvYco1Ahq/kefh\r\nR+9kV58W7k/4DR2nPE7jhRVz2h/LRdyTRcIqCkWOxFQqj56MScmXfubf42KE\r\n+2V+GzKNznOFNZfwYLcRRh+OIDIQt+/eOMGMarhIMRvgxtjzQ/1Bg9V75gKj\r\nltAb3A7UuMWI3t4RHipMzcyhvULqz/ijPRs01tyIqbmUvSnJy0EiXkEBMw/Y\r\n/vlJIvm4t4JLxE7/4Bm9V7DvYqk47xTx8gr8QvANjuxq/pbyCu8GqGq33fN/\r\nrT8gzi+aiyq1QTIb4Jhx18e/9iLCiR1JOhZomQ/ZJZSHLhcwnIb9F1USApXB\r\nNtMp1Fna8ViAIvJ5Z2brdhR8bNsVx3wtmpcgdAP7FEh46r70ZN0R2bV2RzvJ\r\nTaiQNC0zpP+JUDXyuX+0hSRuwJzqkNxYhVEm8u/ktldbTv+2E8DsU3kuyiaw\r\nkSFWWOAL36kj7GkEHjvBeWsMLkqV81ME+V9wkNnsJxvpC9VxDu/cEJEJugh8\r\n7uMu4u0CDX9bSxCfztuMRVXCHxLYWsIsWXY=\r\n=a6C+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.2.9": {"name": "@npmcli/arborist", "version": "6.2.9", "dependencies": {"nopt": "^7.0.0", "ssri": "^10.0.1", "npmlog": "^7.0.1", "pacote": "^15.0.8", "semver": "^7.3.7", "cacache": "^17.0.4", "proc-log": "^3.0.0", "bin-links": "^4.0.1", "minimatch": "^9.0.0", "treeverse": "^3.0.0", "@npmcli/fs": "^3.1.0", "walk-up-path": "^3.0.1", "@npmcli/query": "^3.0.0", "hosted-git-info": "^6.1.1", "npm-package-arg": "^10.1.0", "@npmcli/node-gyp": "^3.0.0", "npm-pick-manifest": "^8.0.1", "@npmcli/run-script": "^6.0.0", "npm-install-checks": "^6.0.0", "npm-registry-fetch": "^14.0.3", "promise-call-limit": "^1.0.2", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^3.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^2.0.0", "@npmcli/metavuln-calculator": "^5.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.0", "@npmcli/installed-package-contents": "^2.0.2"}, "devDependencies": {"tap": "^16.3.4", "nock": "^13.3.0", "chalk": "^4.1.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "tar-stream": "^3.0.0", "@npmcli/template-oss": "4.14.1", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^3.0.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "9ac892e5444206bd9bb3cb1ff18232322ac0fe54", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-6.2.9.tgz", "fileCount": 66, "integrity": "sha512-uC16dg+aykdctgIYPUKNa3pNGIDYh1egzk5vOf34d24cw5mwMsVArFiVdcRieKHmOGEv595se3x0fvbwNrBk8g==", "signatures": [{"sig": "MEUCIQCeylsuWoHbbf4vn2dwXwyTPAk+nX+dPHsrmQk3G0eoOwIgOPM+6NQKf+A6oiVwEFT2GMfz0/zhgPhZzMHcB/nI6tA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 458611, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkUr2IACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmphLhAAj+rdYcoNZzT+JQWcRkPM91m/CDEneNMMMU7sWtHWjcvy7kxl\r\n5qdKYS1z2/AFytoN2mdlD7XWjrnVQCNfziLE8vMfzxSql8sbe6a9BF7X/QDE\r\nRgRC6XiC3KcS0Fvu6pQm72SLPmdaBCZlYKLel18miussxxJG9g/7T4P08lZQ\r\nPXma8BDQvy/0+Fukv02Ef859QQo6kYTo2Jh76o93EwH36EQMIp1LCkZ8WRxw\r\n+uwjeNzAOWAchQwZpLGAaSU3YxEVKRauIvJln0++9NBxlRQ1zgL3/dH0mG49\r\nuFxcG/VXsyY5jdOXNgQEgVBIWKkEHUpCuYT74xvdhXJ+aGCVuKgIlmcM2SVv\r\nL0CKbb1WlrEUwzlGj1pGTpGgCxfzSVrGX6SIahwF/MlSmre395+cHm016skL\r\nI5jDsr46bvgFL62q2vGyvtiXL53JDO1hx4sf2GSb3JyN5PThrlFNrZRT+tNR\r\n1OH4vgsHPLWJgFRu6x5yTPoet4N4Q6aoKE3PeGYmGDTP7puEGjdE5pUFLxmV\r\nJJlj/UUkJPXhs6nkYMuI5Fmex5kHP8U4epbHD0aPvEYWLqrL8qwXjH/XOGl5\r\nipedSB6P/JYxsqLjy11jixk5TOQDcVzCpH1yMWNk3a2qMEJHwI3X//XOm/3I\r\nPDEr36hqmu8LakGtFrxzaTNbkDA0JNBv5pg=\r\n=YwkG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.2.10": {"name": "@npmcli/arborist", "version": "6.2.10", "dependencies": {"nopt": "^7.0.0", "ssri": "^10.0.1", "npmlog": "^7.0.1", "pacote": "^15.0.8", "semver": "^7.3.7", "cacache": "^17.0.4", "proc-log": "^3.0.0", "bin-links": "^4.0.1", "minimatch": "^9.0.0", "treeverse": "^3.0.0", "@npmcli/fs": "^3.1.0", "walk-up-path": "^3.0.1", "@npmcli/query": "^3.0.0", "hosted-git-info": "^6.1.1", "npm-package-arg": "^10.1.0", "@npmcli/node-gyp": "^3.0.0", "npm-pick-manifest": "^8.0.1", "@npmcli/run-script": "^6.0.0", "npm-install-checks": "^6.0.0", "npm-registry-fetch": "^14.0.3", "promise-call-limit": "^1.0.2", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^3.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^2.0.0", "@npmcli/metavuln-calculator": "^5.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.0", "@npmcli/installed-package-contents": "^2.0.2"}, "devDependencies": {"tap": "^16.3.4", "nock": "^13.3.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "tar-stream": "^3.0.0", "@npmcli/template-oss": "4.14.1", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^3.0.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "d12dbc772b5f2894e7eb680fde2b03ada81331ed", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-6.2.10.tgz", "fileCount": 66, "integrity": "sha512-YpGd6RgYZ4JzIZCP6d+PfH81tD0XynOE7HyKUZPLss/YQZXR5pO6hyXWNYb1fcQw4yJrh9ed9umhGbxPhcjBRA==", "signatures": [{"sig": "MEUCIQC/kuaTZ3Pn/lWIp81RSPqqSxzqYc1BnwQw8g3Pqfj1PwIgboisZR8NYtFfIOXTZ2dQ5kop8rpXT8ovZeAr11NjUww=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 458825}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.3.0": {"name": "@npmcli/arborist", "version": "6.3.0", "dependencies": {"nopt": "^7.0.0", "ssri": "^10.0.1", "npmlog": "^7.0.1", "pacote": "^15.0.8", "semver": "^7.3.7", "cacache": "^17.0.4", "proc-log": "^3.0.0", "bin-links": "^4.0.1", "minimatch": "^9.0.0", "treeverse": "^3.0.0", "@npmcli/fs": "^3.1.0", "walk-up-path": "^3.0.1", "@npmcli/query": "^3.0.0", "hosted-git-info": "^6.1.1", "npm-package-arg": "^10.1.0", "@npmcli/node-gyp": "^3.0.0", "npm-pick-manifest": "^8.0.1", "@npmcli/run-script": "^6.0.0", "npm-install-checks": "^6.0.0", "npm-registry-fetch": "^14.0.3", "promise-call-limit": "^1.0.2", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^4.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^2.0.0", "@npmcli/metavuln-calculator": "^5.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.0", "@npmcli/installed-package-contents": "^2.0.2"}, "devDependencies": {"tap": "^16.3.4", "nock": "^13.3.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "tar-stream": "^3.0.0", "@npmcli/template-oss": "4.14.1", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^3.0.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "df37c79f7b82a2de8986fb9061b40efb4d188a38", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-6.3.0.tgz", "fileCount": 66, "integrity": "sha512-XrS14qBDhK95RdGhjTSx8AgeZPNah949qp3b0v3GUFOugtPc9Z85rpWid57mONS8gHbuGIHjFzuA+5hSM7BuBA==", "signatures": [{"sig": "MEUCIDT8nBaq+RMM64Ds5Z7LsF+vR20jA4mG5gyOrWh/qOYBAiEA6dlYNF5J3vX33iff2EU3XACdlx7MAG4QoiCKuugY1bQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 458791}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "7.0.0-pre.0": {"name": "@npmcli/arborist", "version": "7.0.0-pre.0", "dependencies": {"nopt": "^7.0.0", "ssri": "^10.0.5", "npmlog": "^7.0.1", "pacote": "^17.0.4", "semver": "^7.3.7", "cacache": "^18.0.0", "proc-log": "^3.0.0", "bin-links": "^4.0.1", "minimatch": "^9.0.0", "treeverse": "^3.0.0", "@npmcli/fs": "^3.1.0", "walk-up-path": "^3.0.1", "@npmcli/query": "^3.0.0", "hosted-git-info": "^7.0.0", "npm-package-arg": "^11.0.0", "@npmcli/node-gyp": "^3.0.0", "npm-pick-manifest": "^9.0.0", "@npmcli/run-script": "^7.0.1", "npm-install-checks": "^6.2.0", "npm-registry-fetch": "^16.0.0", "promise-call-limit": "^1.0.2", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^5.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^2.0.0", "@npmcli/metavuln-calculator": "^7.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.0", "@npmcli/installed-package-contents": "^2.0.2"}, "devDependencies": {"tap": "^16.3.8", "nock": "^13.3.3", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "tar-stream": "^3.0.0", "@npmcli/template-oss": "4.18.0", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^3.0.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "cbb1921d978bfb4fac1841f86449e8a345d636b0", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-7.0.0-pre.0.tgz", "fileCount": 66, "integrity": "sha512-Cat4zurA0g8WWnMmLGEqBETQXkUhs+FsCZC4QEvk/RwnmP6CFRa+KN+C2vda4vaxP5W9v/qQgoGz0fvNYCBZoQ==", "signatures": [{"sig": "MEQCIE5DptR+32rjVKr837X+a0T8wrSaD2tG8zaCiX+Wi+t+AiB7Q2Czsvgzh+zfvhFdse/FZBuR1bLlFoAMJN2jiI7gbg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 458856}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "7.0.0": {"name": "@npmcli/arborist", "version": "7.0.0", "dependencies": {"nopt": "^7.0.0", "ssri": "^10.0.5", "npmlog": "^7.0.1", "pacote": "^17.0.4", "semver": "^7.3.7", "cacache": "^18.0.0", "proc-log": "^3.0.0", "bin-links": "^4.0.1", "minimatch": "^9.0.0", "treeverse": "^3.0.0", "@npmcli/fs": "^3.1.0", "walk-up-path": "^3.0.1", "@npmcli/query": "^3.0.0", "hosted-git-info": "^7.0.0", "npm-package-arg": "^11.0.0", "@npmcli/node-gyp": "^3.0.0", "npm-pick-manifest": "^9.0.0", "@npmcli/run-script": "^7.0.1", "npm-install-checks": "^6.2.0", "npm-registry-fetch": "^16.0.0", "promise-call-limit": "^1.0.2", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^5.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^2.0.0", "@npmcli/metavuln-calculator": "^7.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.0", "@npmcli/installed-package-contents": "^2.0.2"}, "devDependencies": {"tap": "^16.3.8", "nock": "^13.3.3", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "tar-stream": "^3.0.0", "@npmcli/template-oss": "4.18.0", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^3.0.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "02cd6968f975fc264d38320e71f99fd5cacff1d7", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-7.0.0.tgz", "fileCount": 66, "integrity": "sha512-8eBcxisujfNnU149HwevTgwRVCILk8bljZ3F3fLSXzJqYL0CW5an6r7m5h9apdZRbKl13yxeOquQ5EtrjLIpnQ==", "signatures": [{"sig": "MEYCIQDu+Rh6k7V8mMmGXUGFX3/LLZ3umkjZBwDBwpvjdIRQegIhAPnPXaOf4O9qGnRFEhNyBA49VnIofL55k491RlNsSt8b", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 458850}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "7.1.0": {"name": "@npmcli/arborist", "version": "7.1.0", "dependencies": {"nopt": "^7.0.0", "ssri": "^10.0.5", "npmlog": "^7.0.1", "pacote": "^17.0.4", "semver": "^7.3.7", "cacache": "^18.0.0", "proc-log": "^3.0.0", "bin-links": "^4.0.1", "minimatch": "^9.0.0", "treeverse": "^3.0.0", "@npmcli/fs": "^3.1.0", "walk-up-path": "^3.0.1", "@npmcli/query": "^3.0.0", "hosted-git-info": "^7.0.0", "npm-package-arg": "^11.0.0", "@npmcli/node-gyp": "^3.0.0", "npm-pick-manifest": "^9.0.0", "@npmcli/run-script": "^7.0.1", "npm-install-checks": "^6.2.0", "npm-registry-fetch": "^16.0.0", "promise-call-limit": "^1.0.2", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^5.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^2.0.0", "@npmcli/metavuln-calculator": "^7.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.0", "@npmcli/installed-package-contents": "^2.0.2"}, "devDependencies": {"tap": "^16.3.8", "nock": "^13.3.3", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "tar-stream": "^3.0.0", "@npmcli/template-oss": "4.18.0", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^3.0.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "f240e2437201cf796d50311fb3bd39746e30baeb", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-7.1.0.tgz", "fileCount": 66, "integrity": "sha512-QwVVXZ1kt9VREBuZ6Q9y6jyrT/GRME+/xl3X6/xFUSdd7SGGG+fEbIdWBc25tqqUEuPZfuCEPxJIUngEpXMogw==", "signatures": [{"sig": "MEQCIEx40o9ufOcmTtJ49dJqRJKV6SzfAOKvpxJqrgoERzv0AiAQD+gSLRSxDV1nsMcF5A2F/KMeFQXxpRYTKXrrEqw9Xw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 458872}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "6.4.0": {"name": "@npmcli/arborist", "version": "6.4.0", "dependencies": {"nopt": "^7.0.0", "ssri": "^10.0.1", "npmlog": "^7.0.1", "pacote": "^15.0.8", "semver": "^7.3.7", "cacache": "^17.0.4", "proc-log": "^3.0.0", "bin-links": "^4.0.1", "minimatch": "^9.0.0", "treeverse": "^3.0.0", "@npmcli/fs": "^3.1.0", "walk-up-path": "^3.0.1", "@npmcli/query": "^3.0.0", "hosted-git-info": "^6.1.1", "npm-package-arg": "^10.1.0", "@npmcli/node-gyp": "^3.0.0", "npm-pick-manifest": "^8.0.1", "@npmcli/run-script": "^6.0.0", "npm-install-checks": "^6.2.0", "npm-registry-fetch": "^14.0.3", "promise-call-limit": "^1.0.2", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^4.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^2.0.0", "@npmcli/metavuln-calculator": "^5.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.0", "@npmcli/installed-package-contents": "^2.0.2"}, "devDependencies": {"tap": "^16.3.4", "nock": "^13.3.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "tar-stream": "^3.0.0", "@npmcli/template-oss": "4.19.0", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^3.0.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "0d57f9ad6b908794ada8dfd15ec3bf355a491274", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-6.4.0.tgz", "fileCount": 66, "integrity": "sha512-1HGjcL5l4aFCyx7IhXChYbgOUigVTUhKgfxkxle3Xo94azPLJGEXo11B7MFCSzr0bpT9hoCpP/6QbqgwfIbsdw==", "signatures": [{"sig": "MEUCIALutEGVRAZ0DbTcL5DeBhgF2R++e9A8zkfiiyMWxtRnAiEAw3nW4cMFOfhu9V6X4Czi8WWU0LurST832UdZHH7FdPE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 459237}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "7.2.0": {"name": "@npmcli/arborist", "version": "7.2.0", "dependencies": {"nopt": "^7.0.0", "ssri": "^10.0.5", "npmlog": "^7.0.1", "pacote": "^17.0.4", "semver": "^7.3.7", "cacache": "^18.0.0", "proc-log": "^3.0.0", "bin-links": "^4.0.1", "minimatch": "^9.0.0", "treeverse": "^3.0.0", "@npmcli/fs": "^3.1.0", "walk-up-path": "^3.0.1", "@npmcli/query": "^3.0.1", "hosted-git-info": "^7.0.1", "npm-package-arg": "^11.0.1", "@npmcli/node-gyp": "^3.0.0", "npm-pick-manifest": "^9.0.0", "@npmcli/run-script": "^7.0.1", "npm-install-checks": "^6.2.0", "npm-registry-fetch": "^16.0.0", "promise-call-limit": "^1.0.2", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^5.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^2.0.0", "@npmcli/metavuln-calculator": "^7.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.0", "@npmcli/installed-package-contents": "^2.0.2"}, "devDependencies": {"tap": "^16.3.8", "nock": "^13.3.3", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "tar-stream": "^3.0.0", "@npmcli/template-oss": "4.19.0", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^3.0.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "78768fd3d221190700dd61b75fb4237fbc89df3e", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-7.2.0.tgz", "fileCount": 66, "integrity": "sha512-J6<PERSON>Can+5nV6F94E0+9z//OnZADcqHw6HGDO0ynX+Ayd6GEopK0odq99V+UQjb8P1zexTmCWGvxp4jU5OM6NTtg==", "signatures": [{"sig": "MEUCIQD6+aguihCtk7O3ELG/quDw2eK5gcTVCTuSnN6fCN+rXQIgJJbnvdzWserir76P1fuL30gQi3PuxFDyyySFZi/uXPs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 459283}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "6.5.0": {"name": "@npmcli/arborist", "version": "6.5.0", "dependencies": {"nopt": "^7.0.0", "ssri": "^10.0.1", "npmlog": "^7.0.1", "pacote": "^15.0.8", "semver": "^7.3.7", "cacache": "^17.0.4", "proc-log": "^3.0.0", "bin-links": "^4.0.1", "minimatch": "^9.0.0", "treeverse": "^3.0.0", "@npmcli/fs": "^3.1.0", "walk-up-path": "^3.0.1", "@npmcli/query": "^3.0.0", "hosted-git-info": "^6.1.1", "npm-package-arg": "^10.1.0", "@npmcli/node-gyp": "^3.0.0", "npm-pick-manifest": "^8.0.1", "@npmcli/run-script": "^6.0.0", "npm-install-checks": "^6.2.0", "npm-registry-fetch": "^14.0.3", "promise-call-limit": "^1.0.2", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^4.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^2.0.0", "@npmcli/metavuln-calculator": "^5.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.0", "@npmcli/installed-package-contents": "^2.0.2"}, "devDependencies": {"tap": "^16.3.4", "nock": "^13.3.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "tar-stream": "^3.0.0", "@npmcli/template-oss": "4.19.0", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^3.0.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "ee24ecc56e4c387d78c3bce66918b386df6bd560", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-6.5.0.tgz", "fileCount": 66, "integrity": "sha512-Ir14P+DyH4COJ9fVbmxVy+9GmyU3e/DnlBtijVN7B3Ri53Y9QmAqi1S9IifG0PTGsfa2U4zhAF8e6I/0VXfWjg==", "signatures": [{"sig": "MEUCIDX6S0Hm9os5APvQiyf3KgbEQ8jOkCPRzHb9BjxXiHa1AiEAtcdC4rh40GJB3BHgSDtQOA1I4uOka1L7A2YYIORFyf4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 459237}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "7.2.1": {"name": "@npmcli/arborist", "version": "7.2.1", "dependencies": {"nopt": "^7.0.0", "ssri": "^10.0.5", "npmlog": "^7.0.1", "pacote": "^17.0.4", "semver": "^7.3.7", "cacache": "^18.0.0", "proc-log": "^3.0.0", "bin-links": "^4.0.1", "minimatch": "^9.0.0", "treeverse": "^3.0.0", "@npmcli/fs": "^3.1.0", "walk-up-path": "^3.0.1", "@npmcli/query": "^3.0.1", "hosted-git-info": "^7.0.1", "npm-package-arg": "^11.0.1", "@npmcli/node-gyp": "^3.0.0", "npm-pick-manifest": "^9.0.0", "@npmcli/run-script": "^7.0.2", "npm-install-checks": "^6.2.0", "npm-registry-fetch": "^16.0.0", "promise-call-limit": "^1.0.2", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^5.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^2.0.0", "@npmcli/metavuln-calculator": "^7.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.0", "@npmcli/installed-package-contents": "^2.0.2"}, "devDependencies": {"tap": "^16.3.8", "nock": "^13.3.3", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "tar-stream": "^3.0.0", "@npmcli/template-oss": "4.19.0", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^3.0.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "fcd7254a613b92a3174a57c896c249e30142bff1", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-7.2.1.tgz", "fileCount": 66, "integrity": "sha512-o1QIAX56FC8HEPF+Hf4V4/hck9j0a3UiLnMX4aDHPbtU4Po1tUOUSmc2GAx947VWT+acrdMYTDkqUt2CaSXt7A==", "signatures": [{"sig": "MEUCIDrI+PGsGdMAKtLq3Y2ubc9qNBU0XMl7PQkWwcxjCAChAiEA/04ilHOi4Gmai361Vm8Ec2iU8YMcQZ0n9Fi2JKb/7Ys=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 459283}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "7.2.2": {"name": "@npmcli/arborist", "version": "7.2.2", "dependencies": {"nopt": "^7.0.0", "ssri": "^10.0.5", "npmlog": "^7.0.1", "pacote": "^17.0.4", "semver": "^7.3.7", "cacache": "^18.0.0", "proc-log": "^3.0.0", "bin-links": "^4.0.1", "minimatch": "^9.0.0", "treeverse": "^3.0.0", "@npmcli/fs": "^3.1.0", "walk-up-path": "^3.0.1", "@npmcli/query": "^3.0.1", "hosted-git-info": "^7.0.1", "npm-package-arg": "^11.0.1", "@npmcli/node-gyp": "^3.0.0", "npm-pick-manifest": "^9.0.0", "@npmcli/run-script": "^7.0.2", "npm-install-checks": "^6.2.0", "npm-registry-fetch": "^16.0.0", "promise-call-limit": "^1.0.2", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^5.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^2.0.0", "@npmcli/metavuln-calculator": "^7.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.0", "@npmcli/installed-package-contents": "^2.0.2"}, "devDependencies": {"tap": "^16.3.8", "nock": "^13.3.3", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "tar-stream": "^3.0.0", "@npmcli/template-oss": "4.21.3", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^3.0.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "b1de598b4b4aaf3e05460c0b94565d1966b5f5ed", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-7.2.2.tgz", "fileCount": 66, "integrity": "sha512-dIIzyhy1zS2dYPS8bdM/8qA8W2evQE9KENBxVOhFthm/2RKqf2ninRWQc8xfc5f1gsiTxTP20Y9flIfziHfSKA==", "signatures": [{"sig": "MEUCIHTzxFdbrP4X7H6BGwcz2tdzAql5JveQcJ1oAj++MyXJAiEAkRqtyhy0pCZ+rzxbjwPapFw/e7iKW38xaMck729jJj0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 459202}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "7.3.0": {"name": "@npmcli/arborist", "version": "7.3.0", "dependencies": {"nopt": "^7.0.0", "ssri": "^10.0.5", "npmlog": "^7.0.1", "pacote": "^17.0.4", "semver": "^7.3.7", "cacache": "^18.0.0", "proc-log": "^3.0.0", "bin-links": "^4.0.1", "minimatch": "^9.0.0", "treeverse": "^3.0.0", "@npmcli/fs": "^3.1.0", "walk-up-path": "^3.0.1", "@npmcli/query": "^3.0.1", "hosted-git-info": "^7.0.1", "npm-package-arg": "^11.0.1", "@npmcli/node-gyp": "^3.0.0", "npm-pick-manifest": "^9.0.0", "@npmcli/run-script": "^7.0.2", "npm-install-checks": "^6.2.0", "npm-registry-fetch": "^16.0.0", "promise-call-limit": "^1.0.2", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^5.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^2.0.0", "@npmcli/metavuln-calculator": "^7.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.0", "@npmcli/installed-package-contents": "^2.0.2"}, "devDependencies": {"tap": "^16.3.8", "nock": "^13.3.3", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "tar-stream": "^3.0.0", "@npmcli/template-oss": "4.21.3", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^3.0.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "ca716707053daf63bc320a917bfe317fca7b4dc6", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-7.3.0.tgz", "fileCount": 66, "integrity": "sha512-Z<PERSON>lIE9L14fEYiL4KqgqRHmo8fRKiTSOFU3hVS1mNm0zJE7hu4FHmof+OFsA7fAAXfkNDJrDByvD0o7Le0ISHMw==", "signatures": [{"sig": "MEQCIBs/CegJC/tCghTxDkdaxw9YbEBzdXc8oetgjtKl34vrAiAJ8S/b4iUB9HG3BB8IKZ0Exe7PLeZb5VmGkuGT9w9TXQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 459214}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "7.3.1": {"name": "@npmcli/arborist", "version": "7.3.1", "dependencies": {"nopt": "^7.0.0", "ssri": "^10.0.5", "npmlog": "^7.0.1", "pacote": "^17.0.4", "semver": "^7.3.7", "cacache": "^18.0.0", "proc-log": "^3.0.0", "bin-links": "^4.0.1", "minimatch": "^9.0.0", "treeverse": "^3.0.0", "@npmcli/fs": "^3.1.0", "walk-up-path": "^3.0.1", "@npmcli/query": "^3.0.1", "hosted-git-info": "^7.0.1", "npm-package-arg": "^11.0.1", "@npmcli/node-gyp": "^3.0.0", "npm-pick-manifest": "^9.0.0", "@npmcli/run-script": "^7.0.2", "npm-install-checks": "^6.2.0", "npm-registry-fetch": "^16.0.0", "promise-call-limit": "^3.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^5.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^2.0.0", "@npmcli/metavuln-calculator": "^7.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.0", "@npmcli/installed-package-contents": "^2.0.2"}, "devDependencies": {"tap": "^16.3.8", "nock": "^13.3.3", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "tar-stream": "^3.0.0", "@npmcli/template-oss": "4.21.3", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^3.0.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "7f3f9bed8981584b040483a2f4b126fabcd06674", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-7.3.1.tgz", "fileCount": 66, "integrity": "sha512-qjMywu8clYczZE2SlLZWVOujAyiJEHHSEzapIXpuMURRH/tfY0KPKvGPyjvV041QsGN3tsWeaTUHcOi59wscSw==", "signatures": [{"sig": "MEYCIQCmDouFC7zwG4mv4Nm/2Oz/bTSwkZYPXHJp/K/NGnNgPgIhAI6z5QjJFS9NNtdF05bncqqA3+2Y0o5/VWJgXSN7U0d4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 460149}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "6.5.1": {"name": "@npmcli/arborist", "version": "6.5.1", "dependencies": {"nopt": "^7.0.0", "ssri": "^10.0.1", "npmlog": "^7.0.1", "pacote": "^15.0.8", "semver": "^7.3.7", "cacache": "^17.0.4", "proc-log": "^3.0.0", "bin-links": "^4.0.1", "minimatch": "^9.0.0", "treeverse": "^3.0.0", "@npmcli/fs": "^3.1.0", "walk-up-path": "^3.0.1", "@npmcli/query": "^3.1.0", "hosted-git-info": "^6.1.1", "npm-package-arg": "^10.1.0", "@npmcli/node-gyp": "^3.0.0", "npm-pick-manifest": "^8.0.1", "@npmcli/run-script": "^6.0.0", "npm-install-checks": "^6.2.0", "npm-registry-fetch": "^14.0.3", "promise-call-limit": "^1.0.2", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^4.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^2.0.0", "@npmcli/metavuln-calculator": "^5.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.0", "@npmcli/installed-package-contents": "^2.0.2"}, "devDependencies": {"tap": "^16.3.4", "nock": "^13.3.0", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "tar-stream": "^3.0.0", "@npmcli/template-oss": "4.21.3", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^3.0.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "b378a2e162e9b868d06f8f2c7e87e828de7e63ba", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-6.5.1.tgz", "fileCount": 66, "integrity": "sha512-cdV8pGurLK0CifZRilMJbm2CZ3H4Snk8PAqOngj5qmgFLjEllMLvScSZ3XKfd+CK8fo/hrPHO9zazy9OYdvmUg==", "signatures": [{"sig": "MEYCIQCZx61TBivjTVYjCl4EDuE4UpDlZ9ZT+5hfuNtbqW+LzwIhAI3hdCgUcucACfr3CCfIueNSMR3LuRaaDcK32LPbtvDt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 459258}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "7.4.0": {"name": "@npmcli/arborist", "version": "7.4.0", "dependencies": {"nopt": "^7.0.0", "ssri": "^10.0.5", "npmlog": "^7.0.1", "pacote": "^17.0.4", "semver": "^7.3.7", "cacache": "^18.0.0", "proc-log": "^3.0.0", "bin-links": "^4.0.1", "minimatch": "^9.0.0", "treeverse": "^3.0.0", "@npmcli/fs": "^3.1.0", "walk-up-path": "^3.0.1", "@npmcli/query": "^3.1.0", "hosted-git-info": "^7.0.1", "npm-package-arg": "^11.0.1", "@npmcli/node-gyp": "^3.0.0", "npm-pick-manifest": "^9.0.0", "@npmcli/run-script": "^7.0.2", "npm-install-checks": "^6.2.0", "npm-registry-fetch": "^16.0.0", "promise-call-limit": "^3.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^5.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^2.0.0", "@npmcli/metavuln-calculator": "^7.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.0", "@npmcli/installed-package-contents": "^2.0.2"}, "devDependencies": {"tap": "^16.3.8", "nock": "^13.3.3", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "tar-stream": "^3.0.0", "@npmcli/template-oss": "4.21.3", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^3.0.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "6be8e6562945cdf87097f8f8c50d72c37b9eb832", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-7.4.0.tgz", "fileCount": 61, "integrity": "sha512-VFsUaTrV8NR+0E2I+xhp6pPC5eAbMmSMSMZbS57aogLc6du6HWBPATFOaiNWwp1QTFVeP4aLhYixQM9hHfaAsA==", "signatures": [{"sig": "MEYCIQD7DVxbgBxmgUdTxZjnopD9FRH4CNlv+l5RCy5c920xtAIhAOlwNOCioVcbIH5ph4MamQ9aaNk3Xg/Gn8s5B7hRznf9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 459640}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "7.4.1": {"name": "@npmcli/arborist", "version": "7.4.1", "dependencies": {"nopt": "^7.0.0", "ssri": "^10.0.5", "npmlog": "^7.0.1", "pacote": "^17.0.4", "semver": "^7.3.7", "cacache": "^18.0.0", "proc-log": "^3.0.0", "bin-links": "^4.0.1", "minimatch": "^9.0.4", "treeverse": "^3.0.0", "@npmcli/fs": "^3.1.0", "walk-up-path": "^3.0.1", "@npmcli/query": "^3.1.0", "hosted-git-info": "^7.0.1", "npm-package-arg": "^11.0.1", "@npmcli/node-gyp": "^3.0.0", "npm-pick-manifest": "^9.0.0", "@npmcli/run-script": "^7.0.2", "npm-install-checks": "^6.2.0", "npm-registry-fetch": "^16.2.0", "promise-call-limit": "^3.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^5.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^2.0.0", "@npmcli/metavuln-calculator": "^7.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.0", "@npmcli/installed-package-contents": "^2.0.2"}, "devDependencies": {"tap": "^16.3.8", "nock": "^13.3.3", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "tar-stream": "^3.0.0", "@npmcli/template-oss": "4.21.3", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^3.0.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "1562c347e41e60939b4a2139cd3a16842de1e92e", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-7.4.1.tgz", "fileCount": 61, "integrity": "sha512-5/+bbRol8fvhyebwjqzmwjToAvTOR+k/hzAGFcGlV8k94H1PISNVyOGeSQl6MOMuxPo3BMMYVa39NZp2eNyzUQ==", "signatures": [{"sig": "MEUCIFR0Y9ohBXCgsexPAn2xNeqAu4xpeaSXHCHE/EWtohywAiEA2tnh75uUEokQSd4cSJpY9g/phmrrQBrsfLeEmWF3qwc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 459780}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "7.4.2": {"name": "@npmcli/arborist", "version": "7.4.2", "dependencies": {"nopt": "^7.0.0", "ssri": "^10.0.5", "npmlog": "^7.0.1", "pacote": "^17.0.4", "semver": "^7.3.7", "cacache": "^18.0.0", "proc-log": "^3.0.0", "bin-links": "^4.0.1", "minimatch": "^9.0.4", "treeverse": "^3.0.0", "@npmcli/fs": "^3.1.0", "walk-up-path": "^3.0.1", "@npmcli/query": "^3.1.0", "@npmcli/redact": "^1.1.0", "hosted-git-info": "^7.0.1", "npm-package-arg": "^11.0.1", "@npmcli/node-gyp": "^3.0.0", "npm-pick-manifest": "^9.0.0", "@npmcli/run-script": "^7.0.2", "npm-install-checks": "^6.2.0", "npm-registry-fetch": "^16.2.0", "promise-call-limit": "^3.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^5.0.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^2.0.0", "@npmcli/metavuln-calculator": "^7.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.0", "@npmcli/installed-package-contents": "^2.0.2"}, "devDependencies": {"tap": "^16.3.8", "nock": "^13.3.3", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "tar-stream": "^3.0.0", "@npmcli/template-oss": "4.21.3", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^3.0.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "deb6eb3d88ab6913f0efeb4ebca64151d091d331", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-7.4.2.tgz", "fileCount": 61, "integrity": "sha512-13flK0DTIhG7VEmPtoKFoi+88hIjuZxuZAvnHUTthIFql1Kc51VlsMRpbJPIcDEZHaHkILwFjHRXtCUYdFWvAQ==", "signatures": [{"sig": "MEYCIQDKvyCEoKHN85DpnN6bqCoHf9jQYr5YK81nGjj59a93AAIhAO9NzUNaz+Ksk5chrHv+xlpOQfC7M3+zdCE1m9e2jDeb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 459796}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "7.5.0": {"name": "@npmcli/arborist", "version": "7.5.0", "dependencies": {"nopt": "^7.0.0", "ssri": "^10.0.5", "pacote": "^18.0.1", "proggy": "^2.0.0", "semver": "^7.3.7", "cacache": "^18.0.0", "proc-log": "^4.2.0", "bin-links": "^4.0.1", "minimatch": "^9.0.4", "treeverse": "^3.0.0", "@npmcli/fs": "^3.1.0", "walk-up-path": "^3.0.1", "@npmcli/query": "^3.1.0", "@npmcli/redact": "^1.1.0", "hosted-git-info": "^7.0.1", "npm-package-arg": "^11.0.2", "@npmcli/node-gyp": "^3.0.0", "npm-pick-manifest": "^9.0.0", "@npmcli/run-script": "^8.0.0", "npm-install-checks": "^6.2.0", "npm-registry-fetch": "^16.2.1", "promise-call-limit": "^3.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^5.1.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^2.0.0", "@npmcli/metavuln-calculator": "^7.1.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.0", "@npmcli/installed-package-contents": "^2.1.0"}, "devDependencies": {"tap": "^16.3.8", "nock": "^13.3.3", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "tar-stream": "^3.0.0", "@npmcli/template-oss": "4.21.3", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^3.0.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "31cb45f593fac3b76e8ae7f24ac9e956431e8687", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-7.5.0.tgz", "fileCount": 61, "integrity": "sha512-Uu1hkXEVjz85gJfYqa0d2upTihR+Nw18ozkIuKb5oZXb8+wpCtuRUxP2mV20GYX7ZoWZym6QgC0jxUDLdHaTVQ==", "signatures": [{"sig": "MEYCIQCA0p65khapYcaB++65mzI9L3e7sZtXe/6A/0ajJkf9yQIhAJ6pomuYMk6v5EGOGfkwCqanEh4g8DN7is1q67NKrtT6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 458545}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "7.5.1": {"name": "@npmcli/arborist", "version": "7.5.1", "dependencies": {"nopt": "^7.0.0", "ssri": "^10.0.5", "pacote": "^18.0.1", "proggy": "^2.0.0", "semver": "^7.3.7", "cacache": "^18.0.0", "proc-log": "^4.2.0", "bin-links": "^4.0.1", "minimatch": "^9.0.4", "treeverse": "^3.0.0", "@npmcli/fs": "^3.1.0", "walk-up-path": "^3.0.1", "@npmcli/query": "^3.1.0", "@npmcli/redact": "^2.0.0", "hosted-git-info": "^7.0.1", "npm-package-arg": "^11.0.2", "@npmcli/node-gyp": "^3.0.0", "npm-pick-manifest": "^9.0.0", "@npmcli/run-script": "^8.1.0", "npm-install-checks": "^6.2.0", "npm-registry-fetch": "^17.0.0", "promise-call-limit": "^3.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^5.1.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^2.0.0", "@npmcli/metavuln-calculator": "^7.1.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.0", "@npmcli/installed-package-contents": "^2.1.0"}, "devDependencies": {"tap": "^16.3.8", "nock": "^13.3.3", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "tar-stream": "^3.0.0", "@npmcli/template-oss": "4.21.3", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^3.0.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "7075f8d0e34e92dee1292b0f055bc31725a54b56", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-7.5.1.tgz", "fileCount": 61, "integrity": "sha512-rjGX1tzn9HVQHv5lIP2wANvJmG5+/aFiVFoTBSzneOaSuBUJOnFRha2DE+cIRRekuGllmYff2/XcXnOWrZOJ/w==", "signatures": [{"sig": "MEUCIB+Fg+MdBlvKF6jq1bb3HbPdcsayDPqXQ5ia5OGR2iGfAiEAlKGw49YrrcoTIoJ35AIzhwgSW/9YI457fAdjHi7uFDg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 457505}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "7.5.2": {"name": "@npmcli/arborist", "version": "7.5.2", "dependencies": {"nopt": "^7.2.1", "ssri": "^10.0.6", "pacote": "^18.0.6", "proggy": "^2.0.0", "semver": "^7.3.7", "cacache": "^18.0.3", "proc-log": "^4.2.0", "bin-links": "^4.0.4", "lru-cache": "^10.2.2", "minimatch": "^9.0.4", "treeverse": "^3.0.0", "@npmcli/fs": "^3.1.1", "walk-up-path": "^3.0.1", "@npmcli/query": "^3.1.0", "@npmcli/redact": "^2.0.0", "hosted-git-info": "^7.0.2", "npm-package-arg": "^11.0.2", "@npmcli/node-gyp": "^3.0.0", "npm-pick-manifest": "^9.0.1", "@npmcli/run-script": "^8.1.0", "npm-install-checks": "^6.2.0", "npm-registry-fetch": "^17.0.1", "promise-call-limit": "^3.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^5.1.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^2.0.0", "@npmcli/metavuln-calculator": "^7.1.1", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.2", "@npmcli/installed-package-contents": "^2.1.0"}, "devDependencies": {"tap": "^16.3.8", "nock": "^13.3.3", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "tar-stream": "^3.0.0", "@npmcli/template-oss": "4.22.0", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^3.0.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "0a1b86d9dce852391ce86314c0d4f2172723bb27", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-7.5.2.tgz", "fileCount": 62, "integrity": "sha512-V0zqhdnK9Av3qSIbhYs2O+7HAJPSGhqBkNP6624iSVke2J2JKY306V5Czwul+tc2Xnq6SDEKe8v+frLeKJ4aeA==", "signatures": [{"sig": "MEQCIDSxQn6Ht7to5AkTw+onC7lTdvzrZWBW/RNoMtV8DzYGAiBCbJ8lSevIhcGoomTK9mQTGqW4fkLIk9RY4xIupdnJtw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 459734}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "7.5.3": {"name": "@npmcli/arborist", "version": "7.5.3", "dependencies": {"nopt": "^7.2.1", "ssri": "^10.0.6", "pacote": "^18.0.6", "proggy": "^2.0.0", "semver": "^7.3.7", "cacache": "^18.0.3", "proc-log": "^4.2.0", "bin-links": "^4.0.4", "lru-cache": "^10.2.2", "minimatch": "^9.0.4", "treeverse": "^3.0.0", "@npmcli/fs": "^3.1.1", "walk-up-path": "^3.0.1", "@npmcli/query": "^3.1.0", "@npmcli/redact": "^2.0.0", "hosted-git-info": "^7.0.2", "npm-package-arg": "^11.0.2", "@npmcli/node-gyp": "^3.0.0", "npm-pick-manifest": "^9.0.1", "@npmcli/run-script": "^8.1.0", "npm-install-checks": "^6.2.0", "npm-registry-fetch": "^17.0.1", "promise-call-limit": "^3.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^5.1.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^2.0.0", "@npmcli/metavuln-calculator": "^7.1.1", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.2", "@npmcli/installed-package-contents": "^2.1.0"}, "devDependencies": {"tap": "^16.3.8", "nock": "^13.3.3", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "tar-stream": "^3.0.0", "@npmcli/template-oss": "4.22.0", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^3.0.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "88c51b124a1ec48d358897778af6ab5b0e05694d", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-7.5.3.tgz", "fileCount": 62, "integrity": "sha512-7gbMdDNSYUzi0j2mpb6FoXRg3BxXWplMQZH1MZlvNjSdWFObaUz2Ssvo0Nlh2xmWks1OPo+gpsE6qxpT/5M7lQ==", "signatures": [{"sig": "MEUCIQCUw3sFMSETC6GZf8bUI43uRGWej5UNKbxuyqWBeNaY9AIgac/T8En+xov25RgGgPcjNHpGy8qYvDyrgb/gJJnqErI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 459988}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "7.5.4": {"name": "@npmcli/arborist", "version": "7.5.4", "dependencies": {"nopt": "^7.2.1", "ssri": "^10.0.6", "pacote": "^18.0.6", "proggy": "^2.0.0", "semver": "^7.3.7", "cacache": "^18.0.3", "proc-log": "^4.2.0", "bin-links": "^4.0.4", "lru-cache": "^10.2.2", "minimatch": "^9.0.4", "treeverse": "^3.0.0", "@npmcli/fs": "^3.1.1", "walk-up-path": "^3.0.1", "@npmcli/query": "^3.1.0", "@npmcli/redact": "^2.0.0", "hosted-git-info": "^7.0.2", "npm-package-arg": "^11.0.2", "@npmcli/node-gyp": "^3.0.0", "npm-pick-manifest": "^9.0.1", "@npmcli/run-script": "^8.1.0", "npm-install-checks": "^6.2.0", "npm-registry-fetch": "^17.0.1", "promise-call-limit": "^3.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^3.0.0", "@npmcli/package-json": "^5.1.0", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^2.0.0", "@npmcli/metavuln-calculator": "^7.1.1", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^3.0.2", "@npmcli/installed-package-contents": "^2.1.0"}, "devDependencies": {"tap": "^16.3.8", "nock": "^13.3.3", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "tar-stream": "^3.0.0", "@npmcli/template-oss": "4.22.0", "@npmcli/eslint-config": "^4.0.0", "minify-registry-metadata": "^3.0.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "3dd9e531d6464ef6715e964c188e0880c471ac9b", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-7.5.4.tgz", "fileCount": 62, "integrity": "sha512-nWtIc6QwwoUORCRNzKx4ypHqCk3drI+5aeYdMTQQiRCcn4lOOgfQh7WyZobGYTxXPSq1VwV53lkpN/BRlRk08g==", "signatures": [{"sig": "MEUCIQCU1CicP3+nLa/SqFllDly2Yfmy3z1fGit9P2FZh8yqxgIgah1cKFk/qQW1PAoRkQqLpdK/CEolt3Oz6BjaYzL9v0w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 460480}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "8.0.0": {"name": "@npmcli/arborist", "version": "8.0.0", "dependencies": {"nopt": "^8.0.0", "ssri": "^12.0.0", "pacote": "^19.0.0", "proggy": "^3.0.0", "semver": "^7.3.7", "cacache": "^19.0.1", "proc-log": "^5.0.0", "bin-links": "^5.0.0", "lru-cache": "^10.2.2", "minimatch": "^9.0.4", "treeverse": "^3.0.0", "@npmcli/fs": "^4.0.0", "walk-up-path": "^3.0.1", "@npmcli/query": "^4.0.0", "@npmcli/redact": "^3.0.0", "hosted-git-info": "^8.0.0", "npm-package-arg": "^12.0.0", "@npmcli/node-gyp": "^4.0.0", "npm-pick-manifest": "^10.0.0", "@npmcli/run-script": "^9.0.1", "npm-install-checks": "^7.1.0", "npm-registry-fetch": "^18.0.1", "promise-call-limit": "^3.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^4.0.0", "@npmcli/package-json": "^6.0.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^4.0.1", "read-package-json-fast": "^4.0.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^3.0.0", "@npmcli/metavuln-calculator": "^8.0.0", "@isaacs/string-locale-compare": "^1.1.0", "json-parse-even-better-errors": "^4.0.0", "@npmcli/installed-package-contents": "^3.0.0"}, "devDependencies": {"tap": "^16.3.8", "nock": "^13.3.3", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "tar-stream": "^3.0.0", "@npmcli/template-oss": "4.23.3", "@npmcli/eslint-config": "^5.0.1", "minify-registry-metadata": "^4.0.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "681af823ac8ca067404dee57e0f91a3d27d6ef0a", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-8.0.0.tgz", "fileCount": 62, "integrity": "sha512-APDXxtXGSftyXibl0dZ3CuZYmmVnkiN3+gkqwXshY4GKC2rof2+Lg0sGuj6H1p2YfBAKd7PRwuMVhu6Pf/nQ/A==", "signatures": [{"sig": "MEUCIQDv5/+wUD0ZqnGkTEz+943MILLpvBP/5nWLqNMIbouh4AIgFkc5i8IQQ0qSF5hXr03+yuoD5NUTNlYgalcG/ACF1CQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 460671}, "engines": {"node": "^18.17.0 || >=20.5.0"}}, "9.0.0-pre.0": {"name": "@npmcli/arborist", "version": "9.0.0-pre.0", "dependencies": {"nopt": "^8.0.0", "ssri": "^12.0.0", "pacote": "^20.0.0", "proggy": "^3.0.0", "semver": "^7.3.7", "cacache": "^19.0.1", "proc-log": "^5.0.0", "bin-links": "^5.0.0", "lru-cache": "^10.2.2", "minimatch": "^9.0.4", "treeverse": "^3.0.0", "@npmcli/fs": "^4.0.0", "walk-up-path": "^3.0.1", "@npmcli/query": "^4.0.0", "@npmcli/redact": "^3.0.0", "hosted-git-info": "^8.0.0", "npm-package-arg": "^12.0.0", "@npmcli/node-gyp": "^4.0.0", "npm-pick-manifest": "^10.0.0", "@npmcli/run-script": "^9.0.1", "npm-install-checks": "^7.1.0", "npm-registry-fetch": "^18.0.1", "promise-call-limit": "^3.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^4.0.0", "@npmcli/package-json": "^6.0.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^4.0.1", "read-package-json-fast": "^4.0.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^3.0.0", "@npmcli/metavuln-calculator": "^8.0.0", "@isaacs/string-locale-compare": "^1.1.0", "@npmcli/installed-package-contents": "^3.0.0"}, "devDependencies": {"tap": "^16.3.8", "nock": "^13.3.3", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "tar-stream": "^3.0.0", "@npmcli/template-oss": "4.23.5", "@npmcli/eslint-config": "^5.0.1", "@npmcli/mock-registry": "^1.0.0", "minify-registry-metadata": "^4.0.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "42c22f4da971325712d75ad8fcfc06a6df04275a", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-9.0.0-pre.0.tgz", "fileCount": 62, "integrity": "sha512-jV2gHYQaLUBnqHJD/6LhN4OklT5fVLsXTPsj1hLtwDRapIFX2/8PD+aJBcsm/ZU+0Gn/QTQ3g7kHzJywfGm5ZQ==", "signatures": [{"sig": "MEQCICtK/jVpTav33OZ2UKMFWo1JEHmkecZWJcgRZRD6itfEAiAJkGSHqZFlavGIEVfmBPcUg0QispfNad+gBWKR6D3uLA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 458594}, "engines": {"node": "^20.17.0 || >=22.9.0"}}, "9.0.0-pre.1": {"name": "@npmcli/arborist", "version": "9.0.0-pre.1", "dependencies": {"nopt": "^8.0.0", "ssri": "^12.0.0", "pacote": "^21.0.0", "proggy": "^3.0.0", "semver": "^7.3.7", "cacache": "^19.0.1", "proc-log": "^5.0.0", "bin-links": "^5.0.0", "lru-cache": "^10.2.2", "minimatch": "^9.0.4", "treeverse": "^3.0.0", "@npmcli/fs": "^4.0.0", "walk-up-path": "^4.0.0", "@npmcli/query": "^4.0.0", "@npmcli/redact": "^3.0.0", "hosted-git-info": "^8.0.0", "npm-package-arg": "^12.0.0", "@npmcli/node-gyp": "^4.0.0", "npm-pick-manifest": "^10.0.0", "@npmcli/run-script": "^9.0.1", "npm-install-checks": "^7.1.0", "npm-registry-fetch": "^18.0.1", "promise-call-limit": "^3.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^4.0.0", "@npmcli/package-json": "^6.0.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^4.0.1", "read-package-json-fast": "^4.0.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^3.0.0", "@npmcli/metavuln-calculator": "^9.0.0", "@isaacs/string-locale-compare": "^1.1.0", "@npmcli/installed-package-contents": "^3.0.0"}, "devDependencies": {"tap": "^16.3.8", "nock": "^13.3.3", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "tar-stream": "^3.0.0", "@npmcli/template-oss": "4.23.5", "@npmcli/eslint-config": "^5.0.1", "@npmcli/mock-registry": "^1.0.0", "minify-registry-metadata": "^4.0.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "a6beb6230a9ba29c809aa8ca28822f3da4c1a6a6", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-9.0.0-pre.1.tgz", "fileCount": 62, "integrity": "sha512-mMJzRxkynLRFklKehXk46YKmBnlkk5IIn1ehWldj0HRGYENauUFQeT96P8atTwIK4OZbqCUDMYWNrRIr5W6joQ==", "signatures": [{"sig": "MEUCIG0Qoknf5Hjt/yRtsEHKUT8omXaX1M1Eeo87dYk9p/T+AiEA8DWG4DMkxo/QVXC8krH9s+7EO5eC+oGUUI3IfbPdtEk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 458606}, "engines": {"node": "^20.17.0 || >=22.9.0"}}, "9.0.0": {"name": "@npmcli/arborist", "version": "9.0.0", "dependencies": {"nopt": "^8.0.0", "ssri": "^12.0.0", "pacote": "^21.0.0", "proggy": "^3.0.0", "semver": "^7.3.7", "cacache": "^19.0.1", "proc-log": "^5.0.0", "bin-links": "^5.0.0", "lru-cache": "^10.2.2", "minimatch": "^9.0.4", "treeverse": "^3.0.0", "@npmcli/fs": "^4.0.0", "walk-up-path": "^4.0.0", "@npmcli/query": "^4.0.0", "@npmcli/redact": "^3.0.0", "hosted-git-info": "^8.0.0", "npm-package-arg": "^12.0.0", "@npmcli/node-gyp": "^4.0.0", "npm-pick-manifest": "^10.0.0", "@npmcli/run-script": "^9.0.1", "npm-install-checks": "^7.1.0", "npm-registry-fetch": "^18.0.1", "promise-call-limit": "^3.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^4.0.0", "@npmcli/package-json": "^6.0.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^4.0.1", "read-package-json-fast": "^4.0.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^3.0.0", "@npmcli/metavuln-calculator": "^9.0.0", "@isaacs/string-locale-compare": "^1.1.0", "@npmcli/installed-package-contents": "^3.0.0"}, "devDependencies": {"tap": "^16.3.8", "nock": "^13.3.3", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "tar-stream": "^3.0.0", "@npmcli/template-oss": "4.23.6", "@npmcli/eslint-config": "^5.0.1", "@npmcli/mock-registry": "^1.0.0", "minify-registry-metadata": "^4.0.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "cad6961fa58362c558ec82910fd2bab3c477c802", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-9.0.0.tgz", "fileCount": 62, "integrity": "sha512-ZFsI/VJ7wJ2rTksLNJ9xqr75Ste/wiKvW+7w12ZGbcT67xWii97yS+aDlh3edNhqlqoXvdzYG4hTNui81VxJCA==", "signatures": [{"sig": "MEYCIQC4Hd7mnQixKDi22hlYIW5Gm5rCkS+TyPz8bVEQcWsXlwIhAMmw3/Oukdt519csj8OwRhIt1sZydT9Iczr1fjluw/pf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 458600}, "engines": {"node": "^20.17.0 || >=22.9.0"}}, "9.0.1": {"name": "@npmcli/arborist", "version": "9.0.1", "dependencies": {"nopt": "^8.0.0", "ssri": "^12.0.0", "pacote": "^21.0.0", "proggy": "^3.0.0", "semver": "^7.3.7", "cacache": "^19.0.1", "proc-log": "^5.0.0", "bin-links": "^5.0.0", "lru-cache": "^10.2.2", "minimatch": "^9.0.4", "treeverse": "^3.0.0", "@npmcli/fs": "^4.0.0", "walk-up-path": "^4.0.0", "@npmcli/query": "^4.0.0", "@npmcli/redact": "^3.0.0", "hosted-git-info": "^8.0.0", "npm-package-arg": "^12.0.0", "@npmcli/node-gyp": "^4.0.0", "npm-pick-manifest": "^10.0.0", "@npmcli/run-script": "^9.0.1", "npm-install-checks": "^7.1.0", "npm-registry-fetch": "^18.0.1", "promise-call-limit": "^3.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^4.0.0", "@npmcli/package-json": "^6.0.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^4.0.1", "read-package-json-fast": "^4.0.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^3.0.0", "@npmcli/metavuln-calculator": "^9.0.0", "@isaacs/string-locale-compare": "^1.1.0", "@npmcli/installed-package-contents": "^3.0.0"}, "devDependencies": {"tap": "^16.3.8", "nock": "^13.3.3", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "tar-stream": "^3.0.0", "@npmcli/template-oss": "4.23.6", "@npmcli/eslint-config": "^5.0.1", "@npmcli/mock-registry": "^1.0.0", "minify-registry-metadata": "^4.0.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "5574221af060d6192e1d73f4c3af5f9e3f7b502e", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-9.0.1.tgz", "fileCount": 62, "integrity": "sha512-m00iV8hgbmli0IMf4Os+UmEq5JRTgqOHR+x5h07O7mO/60q5hLaYwZUMamJ73wlMG68c3WB8ZloOxON/knF5vg==", "signatures": [{"sig": "MEUCIQDrbC1h4udHn8J1Kf0Faq2xJQrF4NLWDn1u/1Fq+e0FTQIgUxdSqrsWElGgpO2tT6JiI1cqjarSrAWn9G2WP4xok+4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 467047}, "engines": {"node": "^20.17.0 || >=22.9.0"}}, "9.0.2": {"name": "@npmcli/arborist", "version": "9.0.2", "dependencies": {"nopt": "^8.0.0", "ssri": "^12.0.0", "pacote": "^21.0.0", "proggy": "^3.0.0", "semver": "^7.3.7", "cacache": "^19.0.1", "proc-log": "^5.0.0", "bin-links": "^5.0.0", "lru-cache": "^10.2.2", "minimatch": "^9.0.4", "treeverse": "^3.0.0", "@npmcli/fs": "^4.0.0", "walk-up-path": "^4.0.0", "@npmcli/query": "^4.0.0", "@npmcli/redact": "^3.0.0", "hosted-git-info": "^8.0.0", "npm-package-arg": "^12.0.0", "@npmcli/node-gyp": "^4.0.0", "npm-pick-manifest": "^10.0.0", "@npmcli/run-script": "^9.0.1", "npm-install-checks": "^7.1.0", "npm-registry-fetch": "^18.0.1", "promise-call-limit": "^3.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^4.0.0", "@npmcli/package-json": "^6.0.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^4.0.1", "read-package-json-fast": "^4.0.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^3.0.0", "@npmcli/metavuln-calculator": "^9.0.0", "@isaacs/string-locale-compare": "^1.1.0", "@npmcli/installed-package-contents": "^3.0.0"}, "devDependencies": {"tap": "^16.3.8", "nock": "^13.3.3", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "tar-stream": "^3.0.0", "@npmcli/template-oss": "4.23.6", "@npmcli/eslint-config": "^5.0.1", "@npmcli/mock-registry": "^1.0.0", "minify-registry-metadata": "^4.0.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "416fbc22d256144235397dbc3899fee60eab6e9f", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-9.0.2.tgz", "fileCount": 62, "integrity": "sha512-9z5FgIYd62LxcuCF2BAXnsEo059pGoPv/1E3XkrKBlB9kOQnJ6WSsyOjuGIcZfLAXseamyAif2J7yAVkWNdWzA==", "signatures": [{"sig": "MEUCIBZSGst2e7Qq6DvwTjTiIJZgjwm4OXpcSIYiZoUbU+crAiEAmpwGzJutFnHLiTbczfZaJqxOdcDxxvnA+09PP6GURM8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 469864}, "engines": {"node": "^20.17.0 || >=22.9.0"}}, "9.1.0": {"name": "@npmcli/arborist", "version": "9.1.0", "dependencies": {"nopt": "^8.0.0", "ssri": "^12.0.0", "pacote": "^21.0.0", "proggy": "^3.0.0", "semver": "^7.3.7", "cacache": "^19.0.1", "proc-log": "^5.0.0", "bin-links": "^5.0.0", "lru-cache": "^10.2.2", "minimatch": "^9.0.4", "treeverse": "^3.0.0", "@npmcli/fs": "^4.0.0", "walk-up-path": "^4.0.0", "@npmcli/query": "^4.0.0", "@npmcli/redact": "^3.0.0", "hosted-git-info": "^8.0.0", "npm-package-arg": "^12.0.0", "@npmcli/node-gyp": "^4.0.0", "npm-pick-manifest": "^10.0.0", "@npmcli/run-script": "^9.0.1", "npm-install-checks": "^7.1.0", "npm-registry-fetch": "^18.0.1", "promise-call-limit": "^3.0.1", "json-stringify-nice": "^1.1.4", "parse-conflict-json": "^4.0.0", "@npmcli/package-json": "^6.0.1", "common-ancestor-path": "^1.0.1", "@npmcli/map-workspaces": "^4.0.1", "read-package-json-fast": "^4.0.0", "promise-all-reject-late": "^1.0.0", "@npmcli/name-from-folder": "^3.0.0", "@npmcli/metavuln-calculator": "^9.0.0", "@isaacs/string-locale-compare": "^1.1.0", "@npmcli/installed-package-contents": "^3.0.0"}, "devDependencies": {"tap": "^16.3.8", "nock": "^13.3.3", "tcompare": "^5.0.6", "benchmark": "^2.1.4", "tar-stream": "^3.0.0", "@npmcli/template-oss": "4.23.6", "@npmcli/eslint-config": "^5.0.1", "@npmcli/mock-registry": "^1.0.0", "minify-registry-metadata": "^4.0.0"}, "bin": {"arborist": "bin/index.js"}, "dist": {"shasum": "7517e44b7c6c361fd01cf6c2d6ec0af3d7007e56", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-9.1.0.tgz", "fileCount": 62, "integrity": "sha512-PoOjBc3stYoaI2ehGC0hKQLoa18UYuSxxNZMm86f1y/mjokOvLrshbes7Fne2fk/4V1wR4s2BRdHpYHbp+PJcg==", "signatures": [{"sig": "MEUCIFUcu6gS66+cYQU2u0ogbY4sXqg5C6Ui+Dt4XpRHWewpAiEA7oVXZF4iGmV6dj/e8KkUoF4MAb17z09IAvrHtwoOCC8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 470546}, "engines": {"node": "^20.17.0 || >=22.9.0"}}, "9.1.1": {"name": "@npmcli/arborist", "version": "9.1.1", "dependencies": {"@isaacs/string-locale-compare": "^1.1.0", "@npmcli/fs": "^4.0.0", "@npmcli/installed-package-contents": "^3.0.0", "@npmcli/map-workspaces": "^4.0.1", "@npmcli/metavuln-calculator": "^9.0.0", "@npmcli/name-from-folder": "^3.0.0", "@npmcli/node-gyp": "^4.0.0", "@npmcli/package-json": "^6.0.1", "@npmcli/query": "^4.0.0", "@npmcli/redact": "^3.0.0", "@npmcli/run-script": "^9.0.1", "bin-links": "^5.0.0", "cacache": "^19.0.1", "common-ancestor-path": "^1.0.1", "hosted-git-info": "^8.0.0", "json-stringify-nice": "^1.1.4", "lru-cache": "^10.2.2", "minimatch": "^9.0.4", "nopt": "^8.0.0", "npm-install-checks": "^7.1.0", "npm-package-arg": "^12.0.0", "npm-pick-manifest": "^10.0.0", "npm-registry-fetch": "^18.0.1", "pacote": "^21.0.0", "parse-conflict-json": "^4.0.0", "proc-log": "^5.0.0", "proggy": "^3.0.0", "promise-all-reject-late": "^1.0.0", "promise-call-limit": "^3.0.1", "read-package-json-fast": "^4.0.0", "semver": "^7.3.7", "ssri": "^12.0.0", "treeverse": "^3.0.0", "walk-up-path": "^4.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^5.0.1", "@npmcli/mock-registry": "^1.0.0", "@npmcli/template-oss": "4.23.6", "benchmark": "^2.1.4", "minify-registry-metadata": "^4.0.0", "nock": "^13.3.3", "tap": "^16.3.8", "tar-stream": "^3.0.0", "tcompare": "^5.0.6"}, "bin": {"arborist": "bin/index.js"}, "dist": {"integrity": "sha512-dtANj0Y757hrIDBfylk6neUzMi2yOX0+jK/YjwKrjSMOzis/o8APRfo6VCKL9hhodAeBW72xD65aN9gPzwQz8Q==", "shasum": "fff2675de66ce8e9ed21d3a8cb9fe53148bba0a2", "tarball": "https://registry.npmjs.org/@npmcli/arborist/-/arborist-9.1.1.tgz", "fileCount": 62, "unpackedSize": 470364, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDTfOgq6vbR1mlGTy0C/iP8RS9b4S/6NYgPCcpBVbkFsQIhAObZCDFV6an0eIq+ehEj6IqvjOwgFvMDSNgqiWM73U0v"}]}, "engines": {"node": "^20.17.0 || >=22.9.0"}}}, "modified": "2025-05-21T22:25:21.170Z"}