{"_id": "data-uri-to-buffer", "_rev": "29-c5a183076097a5c40a95b5e4ff187602", "name": "data-uri-to-buffer", "description": "Create an ArrayBuffer instance from a Data URI string", "dist-tags": {"latest": "6.0.2"}, "versions": {"0.0.1": {"name": "data-uri-to-buffer", "version": "0.0.1", "description": "Generate a Buffer instance from a Data URI string", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-data-uri-to-buffer.git"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-data-uri-to-buffer/issues"}, "homepage": "https://github.com/TooTallNate/node-data-uri-to-buffer", "devDependencies": {"mocha": "~1.16.2"}, "_id": "data-uri-to-buffer@0.0.1", "dist": {"shasum": "b4b10d29d88025ea71f0a815bc006613ca259469", "tarball": "https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-0.0.1.tgz", "integrity": "sha512-QPhQBQxTNXHesz2vmBsmfebX3UKWpGm8WMAkhZtglEVPLdfyaQ5126GrswcjLuhnui9Pt6Ix6ZzIOQDhMmcwsA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCKHtRmTt/WpParQuGFeGox4latVMBPmTogkePZNJF2OAIgLtQPnHLTLj8uv2+7V1naW/ftNwuqcq7cmd7OhW+7AM4="}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}]}, "0.0.2": {"name": "data-uri-to-buffer", "version": "0.0.2", "description": "Generate a Buffer instance from a Data URI string", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-data-uri-to-buffer.git"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-data-uri-to-buffer/issues"}, "homepage": "https://github.com/TooTallNate/node-data-uri-to-buffer", "devDependencies": {"mocha": "~1.16.2"}, "_id": "data-uri-to-buffer@0.0.2", "dist": {"shasum": "63c213f89191f8ee0e728d8cc0d257935e1bac90", "tarball": "https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-0.0.2.tgz", "integrity": "sha512-df5kSO158tRSyLxnoVfUXMARPwA1j3PYOr3JucmcnDXJ6Ro1AyD0T1SIT14uKcq6g9g2KivrwTQB9AVJSTpUkg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQChaoFiV8Nz3FSfjtYLUuLnU6qDl8EnkuHiArLsqdee+wIhALBfkLuwZ2aq/z6BwSKD7h9tiR0Tq088xUV5JN96Ih+K"}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}]}, "0.0.3": {"name": "data-uri-to-buffer", "version": "0.0.3", "description": "Generate a Buffer instance from a Data URI string", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-data-uri-to-buffer.git"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-data-uri-to-buffer/issues"}, "homepage": "https://github.com/TooTallNate/node-data-uri-to-buffer", "devDependencies": {"mocha": "~1.16.2"}, "_id": "data-uri-to-buffer@0.0.3", "dist": {"shasum": "18ae979a6a0ca994b0625853916d2662bbae0b1a", "tarball": "https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-0.0.3.tgz", "integrity": "sha512-Cp+jOa8QJef5nXS5hU7M1DWzXPEIoVR3kbV0dQuVGwROZg8bGf1DcCnkmajBTnvghTtSNMUdRrPjgaT6ZQucbw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDbYWsSfclCqWzEynY1o3q1cQdQSpeysR0IsXqFsWTpTgIgKOmw+yNAMrbMC991+f2GuBZ/wQYPa+KquAIDXfLFjOc="}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}]}, "0.0.4": {"name": "data-uri-to-buffer", "version": "0.0.4", "description": "Generate a Buffer instance from a Data URI string", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-data-uri-to-buffer.git"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert", "rfc2397", "2397"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-data-uri-to-buffer/issues"}, "homepage": "https://github.com/TooTallNate/node-data-uri-to-buffer", "devDependencies": {"mocha": "2"}, "gitHead": "387e133e2b016f8a62eb7df0d150455775f97ef8", "_id": "data-uri-to-buffer@0.0.4", "_shasum": "46e13ab9da8e309745c8d01ce547213ebdb2fe3f", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "dist": {"shasum": "46e13ab9da8e309745c8d01ce547213ebdb2fe3f", "tarball": "https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-0.0.4.tgz", "integrity": "sha512-nntmCbCupHk2zFSWe64pTt0LJ2U6Bt3K1MWgwXiEAj9IEaowSXbGLYN7m8xCb4hbpQl8QSCRBkKT9tFRUMkU7A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEc+pyQ4GRqIUg8EBwO12JAREbYaQndBp/ItZagjvA/vAiAQN/1XkxSezN2k02h0iYlypFwnWlacd5ul0des5CREQg=="}]}}, "1.0.0": {"name": "data-uri-to-buffer", "version": "1.0.0", "description": "Generate a Buffer instance from a Data URI string", "main": "index.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-data-uri-to-buffer.git"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert", "rfc2397", "2397"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-data-uri-to-buffer/issues"}, "homepage": "https://github.com/TooTallNate/node-data-uri-to-buffer", "devDependencies": {"mocha": "^3.4.2"}, "gitHead": "d02b63c39c8492fef3f22f064c4245dbdfc2f74a", "_id": "data-uri-to-buffer@1.0.0", "_npmVersion": "5.0.0", "_nodeVersion": "8.0.0", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-P2HdAgBLrJhnutGJkHLXqsfFOyVPWx3DTzr4WBVgv3UQVOcZmxwENbN1Zgt7QrBOGOPXoQe7/LSQMV2GMRNaWg==", "shasum": "af963806bc2bb6253f75442f13dfbee68a2a5897", "tarball": "https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-1.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCuo/mJgMp+2H8rrzppm0kvKrkmlvlU3Arr3Ow3/wYw+QIgbFVIPZCT3++bneChXZGsmcCAD/hKJPw2YMtxH4NXEUk="}]}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/data-uri-to-buffer-1.0.0.tgz_1497054908119_0.13749182294122875"}, "directories": {}}, "1.1.0": {"name": "data-uri-to-buffer", "version": "1.1.0", "description": "Generate a Buffer instance from a Data URI string", "main": "index.js", "types": "index.d.ts", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-data-uri-to-buffer.git"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert", "rfc2397", "2397"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-data-uri-to-buffer/issues"}, "homepage": "https://github.com/TooTallNate/node-data-uri-to-buffer", "dependencies": {"@types/node": "^8.0.7"}, "devDependencies": {"mocha": "^3.4.2"}, "gitHead": "08e292d692ae5e6afeecda322346f1e6d61b3cde", "_id": "data-uri-to-buffer@1.1.0", "_npmVersion": "5.0.3", "_nodeVersion": "8.1.2", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-svaf3LJmZcURiC4637DpcWjdmO/PxCqDsSk9KGWBFsw7tlhHUSDPHGDiXFIhOnnqt2qfS22k2b2Mj/oZFxcfdA==", "shasum": "1895c5ed83cce455e382ce8d4b7301a6f4fc2915", "tarball": "https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-1.1.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFqDLhfSEo/bCDe7+/WoA35auXwwh/2nizlROetkrgcHAiEAxX1PO1JY+VIC2GO8CJ9v8lYo8FImo3PTqY8mAX/dFq8="}]}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/data-uri-to-buffer-1.1.0.tgz_1500341341802_0.7833561815787107"}, "directories": {}}, "1.2.0": {"name": "data-uri-to-buffer", "version": "1.2.0", "description": "Generate a Buffer instance from a Data URI string", "main": "index.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-data-uri-to-buffer.git"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert", "rfc2397", "2397"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-data-uri-to-buffer/issues"}, "homepage": "https://github.com/TooTallNate/node-data-uri-to-buffer", "devDependencies": {"mocha": "^3.4.2"}, "gitHead": "9b6aadea00c5f08d0a13246a4e3655bcd9bf86a0", "_id": "data-uri-to-buffer@1.2.0", "_npmVersion": "5.0.3", "_nodeVersion": "8.1.2", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-vKQ9DTQPN1FLYiiEEOQ6IBGFqvjCa5rSK3cWMy/Nespm5d/x3dGFT9UBZnkLxCwua/IXBi2TYnwTEpsOvhC4UQ==", "shasum": "77163ea9c20d8641b4707e8f18abdf9a78f34835", "tarball": "https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-1.2.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIQD9Uq7Id0jEZdZphrikuSKdYjE/uKP02aYeElMmW6lz8wIfGWWiWIIAQDcwWxOxpM1cqxx4L3tCTpM4Yr6KmAkEBw=="}]}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/data-uri-to-buffer-1.2.0.tgz_1500427517195_0.7155292946845293"}, "directories": {}}, "2.0.0": {"name": "data-uri-to-buffer", "version": "2.0.0", "description": "Generate a Buffer instance from a Data URI string", "main": "index.js", "types": "index.d.ts", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-data-uri-to-buffer.git"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert", "rfc2397", "2397"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-data-uri-to-buffer/issues"}, "homepage": "https://github.com/TooTallNate/node-data-uri-to-buffer", "dependencies": {"@types/node": "^8.0.7"}, "devDependencies": {"mocha": "^3.4.2"}, "gitHead": "ef77360329db07c3867dc5fcccfc43576f17fdf4", "_id": "data-uri-to-buffer@2.0.0", "_npmVersion": "5.0.3", "_nodeVersion": "8.1.2", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-YbKCNLPPP4inc0E5If4OaalBc7gpaM2MRv77Pv2VThVComLKfbGYtJcdDCViDyp1Wd4SebhHLz94vp91zbK6bw==", "shasum": "0ba23671727349828c32cfafddea411908d13d23", "tarball": "https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-2.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDQeZpcPFw40q/I5Z4rNEGYjAc4MfR3zQjzFNA0+aN+JAiEArJJ9YY6Dbay/mtEBO8Ku9ZGD/ltQvDmGc8+gl6dQhM8="}]}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/data-uri-to-buffer-2.0.0.tgz_1500427596539_0.10986626683734357"}, "directories": {}}, "2.0.1": {"name": "data-uri-to-buffer", "version": "2.0.1", "description": "Generate a Buffer instance from a Data URI string", "main": "index.js", "types": "index.d.ts", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-data-uri-to-buffer.git"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert", "rfc2397", "2397"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-data-uri-to-buffer/issues"}, "homepage": "https://github.com/TooTallNate/node-data-uri-to-buffer", "dependencies": {"@types/node": "^8.0.7"}, "devDependencies": {"mocha": "^3.4.2"}, "gitHead": "84d6d828d8eaca38813c8ba7f11607fa558b7b2f", "_id": "data-uri-to-buffer@2.0.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.13.0", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-OkVVLrerfAKZlW2ZZ3Ve2y65jgiWqBKsTfUIAFbn8nVbPcCZg6l6gikKlEYv0kXcmzqGm6mFq/Jf2vriuEkv8A==", "shasum": "ca8f56fe38b1fd329473e9d1b4a9afcd8ce1c045", "tarball": "https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-2.0.1.tgz", "fileCount": 7, "unpackedSize": 13318, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpk2bCRA9TVsSAnZWagAA0gYP/ifvuWGpnTvukeCbzuAz\nUEx3+usgqDqgXQBRyBt/IKOGvf+QzB4FZdNSZrwx7TVFC6l6wOSrEK9MnYUf\nsLtgkazKK79h2KA/4dM3rL0qKAVEw0460hM1wx3Nsmd2of6MbcUf3wyMicbp\nsbr6sVLAMhb//hDrIlhK8F3ic/cKwSvr2bywZ5TBpOW7qOvj8g2n1dachCGN\nWTogiJe9kixB+MNt+l162b9jr/tYGNepWosFK8BuI7RleWcBVsMlsntogJlp\ngHml5TA0UzRQkSrFQXLgct1tLh3VwYcLrlt5fEBTVVLLBwGp2qOpywqFf8Qg\nXQ+YZu1FnBKvQbrfypJWW5iMjLzgaEp47RzywHUhm+We74UzZ0407ilzJLGR\nTdXCw9n/muWGCEPUvG+ZAiOD7aTowUrSdvCks2/Y9gp6pXD4ERY+8ujyiMdr\nQxO3mrPs0pVTZ+aUaAy5LYeSskoxBvS1r6QZ/YL+6Pw4L+QeRNPhVZyNzmS3\nq2/hBYQf/ighzrpojzMT8Smp3If1Vp4vrFahLpVpo502SHWLcblxsKBg3Lgn\nyPy2EajwxtbSJ0+7tYUsfes2JuOAgQTrw2JPfW9WBT1TMCH7wz+5ITsP1gmP\nNwlkxnN6Oj0xDdk69jnQaJmipm2Ay8mYTArPAhxwDGIq8hF/rVsh5O84kuLj\nIfYE\r\n=aVCV\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCdTte8tGge5lq+ha1xwlQdL1kmsO9R0a2OwGQhNCNp9gIhAO80bfYflSUyfbAhFGxvgxEulB5fILRiposJZIZT0CWT"}]}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/data-uri-to-buffer_2.0.1_1554402714500_0.7907742068800752"}, "_hasShrinkwrap": false}, "2.0.2": {"name": "data-uri-to-buffer", "version": "2.0.2", "description": "Generate a Buffer instance from a Data URI string", "main": "index.js", "types": "index.d.ts", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-data-uri-to-buffer.git"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert", "rfc2397", "2397"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-data-uri-to-buffer/issues"}, "homepage": "https://github.com/TooTallNate/node-data-uri-to-buffer", "devDependencies": {"@types/node": "^8.0.7", "mocha": "^3.4.2"}, "gitHead": "49ca2a1b68b0080b6e037712a73cb423679a5ec6", "_id": "data-uri-to-buffer@2.0.2", "_nodeVersion": "10.16.3", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-ND9qDTLc6diwj+Xe5cdAgVTbLVdXbtxTJRXRhli8Mowuaan+0EJOtdqJ0QCHNSSPyoXGx9HX2/VMnKeC34AChA==", "shasum": "d296973d5a4897a5dbe31716d118211921f04770", "tarball": "https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-2.0.2.tgz", "fileCount": 7, "unpackedSize": 13346, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdjSkuCRA9TVsSAnZWagAANxkP/iKIxylFIJEGlZ6AufPN\n9P1Ml1Yz5fRGXS1s0HKtl4eRGfLh0h2bBAGJ5r4P+bKvGaTw010N/3wWHTea\n0ka39AedyeWmIxD0fuHX6lV5yeqwY0rXDcPAR2/DVHG6v7cfxWNbZ7MA3TFo\n6dFJe4bPI3s6Wu7y+S1uuh475oHhZiz1HfuF7rKirkw7biuvVpwaEImcqup6\nL7auRcWEepWooGSOu5Wz1oYEk0umNy1MbH/CuvN6mCgDIoeAmktmgfb7h3LP\n4NHTxpT9Jzv0gla/Tjxbpx881DnN/izR4oVzOncHwTUnjxtpKpNJTDTyLyeJ\n6Gv6Re9duAktMgwsUBGoifhajcBKrEps09PeRRmB1DEaTvzXMwUZhhHA+4Z7\nVkoEmvY/epcY2uvqpsW5x77SCS4fEaOHRPywhBHVk/VarAVuxN5n9Z3P+DTA\nQ18Kun1ut6xg3Au/3mtvB580fbFHadG65aEc54wow4QJphb//IasgyfjSgxK\nPSNG9+bYQEdTHVIwivJv6Pox4CCBR7D34e8QwJpFr8bEqNhjWX7iSpDIAB00\nIWDUtzaH/c9rjsv1ZMjdyiSm4hKP1KGB454l4oqSCsjy7zycX2T+4xgb2lvW\nNDjmiISj8kUmtmMxAWILP1k7ErcrA51TPzzrcH1aiv14JY2T4O2lrM6ucA+y\n4JYZ\r\n=Gs8W\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCluu1MjSY0tma7d/BLSbdYwk76U1MlEzyxyrC04Kc/mwIgMiaJsxDgP0lCD84sBZWYV0HSu0oHJpi+4qemK+j2u6s="}]}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/data-uri-to-buffer_2.0.2_1569532206206_0.4679690854249954"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "data-uri-to-buffer", "version": "3.0.0", "description": "Generate a Buffer instance from a Data URI string", "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "scripts": {"build": "tsc", "test": "mocha --reporter spec dist/test/*.js", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-data-uri-to-buffer.git"}, "engines": {"node": ">= 6"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert", "rfc2397", "2397"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-data-uri-to-buffer/issues"}, "homepage": "https://github.com/TooTallNate/node-data-uri-to-buffer", "devDependencies": {"@types/buffer-from": "^1.1.0", "@types/es6-promisify": "^5.0.0", "@types/mocha": "^5.2.7", "@types/node": "^10.5.3", "@typescript-eslint/eslint-plugin": "1.6.0", "@typescript-eslint/parser": "1.1.0", "eslint": "5.16.0", "eslint-config-airbnb": "17.1.0", "eslint-config-prettier": "4.1.0", "eslint-import-resolver-typescript": "1.1.1", "eslint-plugin-import": "2.16.0", "eslint-plugin-jsx-a11y": "6.2.1", "eslint-plugin-react": "7.12.4", "mocha": "^6.2.0", "typescript": "^3.5.3"}, "dependencies": {"buffer-from": "^1.1.1"}, "gitHead": "e673786bdc3709a7337e1296fce0370a2ea6fed6", "_id": "data-uri-to-buffer@3.0.0", "_nodeVersion": "10.16.3", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-MJ6mFTZ+nPQO+39ua/ltwNePXrfdF3Ww0wP1Od7EePySXN1cP9XNqRQOG3FxTfipp8jx898LUCgBCEP11Qw/ZQ==", "shasum": "8a3088a5efd3f53c3682343313c6895d498eb8d7", "tarball": "https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-3.0.0.tgz", "fileCount": 8, "unpackedSize": 22224, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdjoRUCRA9TVsSAnZWagAAIwAP/1v+/K6v71jWEY+bpIgL\nh4XDxCpwcAZMv4GAPrDyhW86owZEvE/ue05dygTr/Iw5ZnvWuVOieOqBV2Ma\n1wqUuVRKLPcYAPiKnHtD3TZVIRd6Mv/Ps26VY/mWaIlQuBRMy5CeHx0bEW4U\nNuE+YbJ6dOvvNwkHRGHBaYWFp9H730zVLuY3zOsbcPh2zLsLL7OoqcbJLgrt\nd92xIANtypMpLDyIstqp6J8dbSefhVCQVkufAmD/4djZf2yoEoEtGLa9C9Gg\nB/rHaHEvycksirRtmNjOKwZuaF+oB22Qf7rFm1drEVJT2m7YcZ0WNxpE9esN\nY3/CZO0Cyk6widQNvWoBNAxnjn7A6YY5JYynlE5/wqM4im+M+Fo2Q5t84Tio\ntwAZRnnmWHDiEgNN+rwkb14kb/iZqt+GLU68kIsrfgrFleBgEwDBwtggudki\nE/xoINw/mJBfN/v0j8unRMZyt5dFmsEjdbeOC4BAVSOPoUQg302a+iCK2QYn\n3GbkWhDUerOaOVFPuBSo3Z/TcN5KtWAPX/YyxGK0Ipqk7ZlntXepjwzdcXoG\n/ZLIYDVYZWMAxKzk81y4OVKm0MR/BS4fQAHqeNWQbAnGD/1p3aLYyAzYCgoy\n2Ae2Dhm5f2OEKjbxbhjkzBn3OEdDiYBmTyc2oE08I4ToMnJNMBxgVo5bnIvZ\nIGED\r\n=0iwq\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAyZC2ZUNghWyDu7o5FTqP2cbHmNyLe5m9llB/VOFa17AiEAp7fdgyTDmJ92X92VkRJ70U7PDy3U+Yw+SjdfWbUHK2s="}]}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/data-uri-to-buffer_3.0.0_1569621075826_0.47339569396666925"}, "_hasShrinkwrap": false}, "3.0.1": {"name": "data-uri-to-buffer", "version": "3.0.1", "description": "Generate a Buffer instance from a Data URI string", "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "scripts": {"build": "tsc", "test": "mocha --reporter spec dist/test/*.js", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-data-uri-to-buffer.git"}, "engines": {"node": ">= 6"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert", "rfc2397", "2397"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-data-uri-to-buffer/issues"}, "homepage": "https://github.com/TooTallNate/node-data-uri-to-buffer", "devDependencies": {"@types/es6-promisify": "^5.0.0", "@types/mocha": "^5.2.7", "@types/node": "^10.5.3", "@typescript-eslint/eslint-plugin": "1.6.0", "@typescript-eslint/parser": "1.1.0", "eslint": "5.16.0", "eslint-config-airbnb": "17.1.0", "eslint-config-prettier": "4.1.0", "eslint-import-resolver-typescript": "1.1.1", "eslint-plugin-import": "2.16.0", "eslint-plugin-jsx-a11y": "6.2.1", "eslint-plugin-react": "7.12.4", "mocha": "^6.2.0", "typescript": "^3.5.3"}, "gitHead": "e39ad289e71726621466ba2cc7154a8eef452c23", "_id": "data-uri-to-buffer@3.0.1", "_nodeVersion": "12.16.2", "_npmVersion": "6.14.5", "dist": {"integrity": "sha512-WboRycPNsVw3B3TL559F7kuBUM4d8CgMEvk6xEJlOp7OBPjt6G7z8WMWlD2rOFZLk6OYfFIUGsCOWzcQH9K2og==", "shasum": "594b8973938c5bc2c33046535785341abc4f3636", "tarball": "https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-3.0.1.tgz", "fileCount": 5, "unpackedSize": 8403, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe0a4iCRA9TVsSAnZWagAAmLMP/iFe6FQCzJsnZUR9OV0E\ndjQqRNjZ5bcvWxCtjGX51447SVX+2VffgQrU5YhvpOldRRo/FTAWWAydjek3\nzx1WGTEbWU1ozelnr6jRDw2PYKL/Ucr/ZRv5IMmoA40pN/8GjOxvI5OFNoCv\n19SOoqNGt750OSzixDEREBtpZiJzwQ6dODQX6gnj6C58qxsD3LlYFDkYNB2y\nPi6yz89Y6WmvwZK4b4shDUpkkLlypzThJ/3dz/P2QlqMqiy3gqR6ePl1w11M\nENz4aj24TMeymeugJpem008vwVffqFrY0kC1uZJN6c9D7bYPRvp1sbodGf2K\nUc19UHOlf8Q3b0C+aMD3xrxtGUBBZOv5ntuwYzQZKRklFXPeS7KuBR/2cguu\nftY/Qc4dPxq2zjuzCEZGQCnx0pdVHCCu7y1/G5all5TAHOrVkhuDAQOzIycD\nInW6Z2Zp+9GSkj03dpv4eQi5jTLrqz4l8/CfJu0YC3oNv40pAztWPA+ggNR+\niV2LUv9eFcebhFwE9xJagFhx0tIu5gif1fCQARPTbrYhBMssXBJFdn6qXVhR\nmjJvBgHd3D6xYRFBnN7UJOGiQDxb59fKorGkHELrXU0LjBuPgN4wl3OEdDDW\n8Du1Baksu2MtVpYocNfFrZp89tuxioGG5UoZIfW0X4RdLNQzAoesW3+sEwCQ\n2UTp\r\n=EmgN\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCgfDFnQtnUEZckq8LbClI9lLwIeyvBljm6OkiSvFDecgIgX1mY9MMTFj0qTRkih5dYBDwFkeRK799CgQXjWK4Wq/o="}]}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/data-uri-to-buffer_3.0.1_1590799905558_0.6459117411174389"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "data-uri-to-buffer", "version": "4.0.0", "description": "Generate a Buffer instance from a Data URI string", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "test": "jest", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-data-uri-to-buffer.git"}, "engines": {"node": ">= 12"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert", "rfc2397", "2397"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-data-uri-to-buffer/issues"}, "homepage": "https://github.com/TooTallNate/node-data-uri-to-buffer", "devDependencies": {"@types/es6-promisify": "^5.0.0", "@types/mocha": "^9.0.0", "@types/node": "^10.5.3", "jest": "^27.2.2", "ts-jest": "^27.0.5", "typescript": "^4.4.3"}, "jest": {"preset": "ts-jest", "globals": {"ts-jest": {"diagnostics": false, "isolatedModules": true}}, "verbose": false, "testEnvironment": "node", "testMatch": ["<rootDir>/test/**/*.test.ts"]}, "gitHead": "099460306e59ee2cbd9117e2b430e808ad19f5c0", "_id": "data-uri-to-buffer@4.0.0", "_nodeVersion": "12.22.6", "_npmVersion": "7.24.1", "dist": {"integrity": "sha512-Vr3mLBA8qWmcuschSLAOogKgQ/Jwxulv3RNE4FXnYWRGujzrRWQI4m12fQqRkwX06C0KanhLr4hK+GydchZsaA==", "shasum": "b5db46aea50f6176428ac05b73be39a57701a64b", "tarball": "https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-4.0.0.tgz", "fileCount": 12, "unpackedSize": 26622, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2nsvCRA9TVsSAnZWagAAEd0QAIAzsqr1kzx5nbU8Kvi1\naNsqZbjjZVH24bwHcP9TWnyeNJiUC4dlnTRq0IElEWBUrU9FqSclAeVX7WM6\ns1KQJxLt7JgGcksc5USgckAWf6E2O7xFcRrVH6fNBvGW0GFTmQFDCc3yE+oS\nFTJtVwUSGYRcHG7DHsYTDDvJqUem2hoz1P/LLuwTizji1QIZAOZBpNYeo5/j\n/dU/FFGp1vSUMHORbgOR/hm9Ck8aWEx44z3DX+NFYubNdxEnhwgpZICcr2ql\niYrR1HeENjsKOWVBrF3DdiC+r+hCTSRlcNAzOF3x9COoqEfG8fp9Nz8U7lWY\najYK5AWe0C1CpjhSqbk23lQbYfL9tN8ootDJszmJzjs62YdrIR+T+TDmgsC6\notvJlgLJnQULBs91OWmx0I5vJWI5eeEzA2zhn121ZlvsTkTb9nkC/isAW8k0\n0k2/CP5W9vGC2no/iGE6+qfsGBMPlCm6c9OuOU3vWbj3DA5lSp2+Fx7LZHXj\nkJB6lxQmkDsiFgzUDZFtqfJ102mm2SukcOTq0uEh49IsFYC0xSPbMQa3Ls+a\n8YScQLKdy/ZLm3rIJN2w4fJw6TcN7haNTnREfsx8MLRS7cHt4YeJKUj7x6lm\ntmf8Pj56pB04KsCi1+EPN933rEEm60PKLudF3F3A5WvXenqZZQ2yGimf4l2l\nOwHH\r\n=7c9v\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFNMvE6i9iZGHi5EaFkKRrUsPpI4WtMvoQYQMmiAgbEjAiBPERt61ldHdwa5XUX4rnRokYytneFjK5bY33o5aDKkJw=="}]}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/data-uri-to-buffer_4.0.0_1632737498941_0.6380803188362318"}, "_hasShrinkwrap": false}, "4.0.1": {"name": "data-uri-to-buffer", "version": "4.0.1", "description": "Generate a Buffer instance from a Data URI string", "type": "module", "exports": "./dist/index.js", "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsc", "test": "jest", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-data-uri-to-buffer.git"}, "engines": {"node": ">= 12"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert", "rfc2397", "2397"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-data-uri-to-buffer/issues"}, "homepage": "https://github.com/TooTallNate/node-data-uri-to-buffer", "devDependencies": {"@types/jest": "^27.0.2", "@types/node": "^12.20.36", "jest": "^27.3.1", "ts-jest": "^27.0.7", "typescript": "^4.4.4"}, "jest": {"preset": "ts-jest", "globals": {"ts-jest": {"diagnostics": false, "isolatedModules": true}}, "verbose": false, "testEnvironment": "node", "testMatch": ["<rootDir>/test/**/*.test.ts"]}, "gitHead": "85cd8c854aefbf1bb636789d80364cfac8ea1583", "_id": "data-uri-to-buffer@4.0.1", "_nodeVersion": "16.18.1", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-0R9ikRb668HB7QDxT1vkpuUBtqc53YyAwMwGeUFKRojY/NWKvdZ+9UYtRfGmhqNbRkTSVpMbmyhXipFFv2cb/A==", "shasum": "d8feb2b2881e6a4f58c2e08acfd0e2834e26222e", "tarball": "https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-4.0.1.tgz", "fileCount": 6, "unpackedSize": 10049, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB8aF0pXlnwJZiGMoMGmXhPR+2h2Tkzq9N7In8oH37z6AiEAlBfobbDNJtTxMBDTaJ2ox0kSO3yvaNzS+StuDuythUA="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjwKoQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqPZxAAhSSUvsUlqSLH3I5/sCG2JgnzGNJ4Nu53GptVmWBh473olUtW\r\n15C1IahGUqMn/tu/lMgpmoMS7i9PNx6lkKABQyGQMoTIT5qlZ+4y6eGbS0eK\r\nEiofd9FZDbP3K9mUNpBk8oudK2Onp7L8z6c4K9pFEbHBIzNeUp3Q8dMY5LK4\r\nu5Ruq2U52AQrk8h2BSVkXjhGdJW9saEtbx17OIO1G3CcIxK4KVwjrg/59nV9\r\neY60Ba2Z+vxmByrycZJFtXiwTmj+lACTY6WQKN5LKoL9Jmfwtv7ouAwwWlkD\r\n0btf8CyGtgU20705j/OjVnKdp7yJyrzRNsRAMXX4iAGCA0sFn+ddwYTCb8sd\r\n16idSDog1GR4WLhwsfOrdouJy4qTi4vBYj//wa+jfF8kkaDwVv6i0wQ3kx6L\r\n3COFNtpKd6Jdw8kwCeBKfT5H0Bp/5Ws4iWQ/ObxV5cfhMM46k1uIz/qMeYXH\r\nG1PV3+Im4sTKsyhh07+raP5wvPSrEQqqdVdkIXKIlwLvW2CRex7K8NUn9dkn\r\n845cYU26vNXBSrboL6Hkr+uOMoZCn+nrpErbLk69/A+qmkRIxSQ2uvkZGY2M\r\n9d5R8WbQ+EqqiUdAlhQuX+ol0kWtAxB69pDNrHOqhnaWBLxEbM/o3Y6JXpNM\r\n7P5aJmNwCf4A2HJWAk7keW3m6BhKU4Sbl0E=\r\n=PPVo\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/data-uri-to-buffer_4.0.1_1673570832777_0.48692261036527995"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "data-uri-to-buffer", "version": "5.0.0", "description": "Generate a Buffer instance from a Data URI string", "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail", "lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/data-uri-to-buffer"}, "engines": {"node": ">= 14"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert", "rfc2397", "2397"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "devDependencies": {"@types/jest": "^27.0.2", "@types/node": "^14.18.43", "jest": "^29.5.0", "ts-jest": "^29.1.0", "tsconfig": "workspace:*", "typescript": "^5.0.4"}, "gitHead": "4d75e6cc974b2614a575e6f7a3d3c79741727dbc", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_id": "data-uri-to-buffer@5.0.0", "_nodeVersion": "16.20.0", "_npmVersion": "8.19.4", "dist": {"integrity": "sha512-RqYkPrQOBOGLS/OYpNwPuoyYvjKFmCePfA8SOrbCd1ohhtGNT/GSheWZzNTJlPvUWQYI9N4bcM0wbsp08AJyWw==", "shasum": "7b4e06b6292a279401c8868c047329f5d42c54ad", "tarball": "https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-5.0.0.tgz", "fileCount": 7, "unpackedSize": 10163, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDKt4vn5uG2MGQzK26j3FJPBSZSl6kJ2mhNrpyuWzJWagIhAMInB+sxcgQwZbSCwQMH09X8SzWXwEtG8/0RNYJPV+01"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkVBaPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrUCg//QYUFmz2mPuHhnTS2GmBol90DkqMsd4L4TaqnvwjnUOg6+IBi\r\nBSRXkTFKEObucIibTbvHnRtCHFmE950iTF7gSj7IHMaDMlQe1brCbzrfxUqL\r\njeDQfiOF1yM1k2xMoRwXZYTyXLZGj4Ony94iZrh1IbbXymUCh2pBbdFN4Us6\r\n/oEPWuXRe7VtN+TaGm56M2rugsaE3VSRskssmqC+DpNxkIPmKcTzi2sGs8aU\r\nk2k5vsr8mrKmavBIkcbfhqCbXU+ps/ShE0iN6oy/xDPboz8ehrfWXhgWaIJ6\r\nOl8c4Ta/ykE7qUmM8Pb3f9Q5mqEf6HUlIUydolSN+abdZT67biMQagnnCwNJ\r\nrRdt7duwH4lTedk0LmiM7n/cd+KPzaFd0krggPlXp6V7Js4Eijr4kkkQ+gF3\r\nOMZ7J6IwE+6UBPdod03bpDa3I86ffY9qnm0jy4Zg7medKwSHfhqA4eGMbqDZ\r\nVN884tFmgZHP0XhDpcvltC2Uh1ODDAdlhe7FFMTSLPavKz6fEv9/Q9QYz1Wm\r\ngJUnU+urPVf7zm54MSgruQWAioiKzt7CqHZlyUxGTI7fQUViwo/WtD1wg1+X\r\nOOM3qGxr3qeUlJQaQz9i9hNVj0HBr+CSccghmBewg6z9w/xZ/etHWz1bUAne\r\nS2cAHoBv3l+pXQxL3BkI7v8kT0dqf+kKbBo=\r\n=rlgJ\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/data-uri-to-buffer_5.0.0_1683232399483_0.4102046430354689"}, "_hasShrinkwrap": false}, "5.0.1": {"name": "data-uri-to-buffer", "version": "5.0.1", "description": "Generate a Buffer instance from a Data URI string", "main": "./dist/index.js", "types": "./dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/data-uri-to-buffer"}, "engines": {"node": ">= 14"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert", "rfc2397", "2397"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "devDependencies": {"@types/jest": "^27.0.2", "@types/node": "^14.18.45", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "tsconfig": "0.0.0"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail", "lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_id": "data-uri-to-buffer@5.0.1", "_integrity": "sha512-a9l6T1qqDogvvnw0nKlfZzqsyikEBZBClF39V3TFoKhDtGBqHu2HkuomJc02j5zft8zrUaXEuoicLeW54RkzPg==", "_resolved": "/tmp/dace3c93ff0dd76faa9aa2a8cba1fb78/data-uri-to-buffer-5.0.1.tgz", "_from": "file:data-uri-to-buffer-5.0.1.tgz", "_nodeVersion": "20.1.0", "_npmVersion": "9.6.4", "dist": {"integrity": "sha512-a9l6T1qqDogvvnw0nKlfZzqsyikEBZBClF39V3TFoKhDtGBqHu2HkuomJc02j5zft8zrUaXEuoicLeW54RkzPg==", "shasum": "db89a9e279c2ffe74f50637a59a32fb23b3e4d7c", "tarball": "https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-5.0.1.tgz", "fileCount": 7, "unpackedSize": 10156, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICJ+KvG2S3LWHkzgprmfPeRzEaLmzb1NFkQx86DgymoTAiEAjH12cTptj1o4ie5cFs3LRb8ZGkZwgUiMDNBvIjH1BM4="}]}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/data-uri-to-buffer_5.0.1_1683324250263_0.6802099700141306"}, "_hasShrinkwrap": false}, "6.0.0": {"name": "data-uri-to-buffer", "version": "6.0.0", "description": "Create an ArrayBuffer instance from a Data URI string", "main": "./dist/index.js", "types": "./dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/data-uri-to-buffer"}, "engines": {"node": ">= 14"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert", "rfc2397", "2397"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "devDependencies": {"@types/jest": "^27.0.2", "@types/node": "^14.18.45", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "tsconfig": "0.0.0"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail", "lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_id": "data-uri-to-buffer@6.0.0", "_integrity": "sha512-pMT6awii4zyQu/fUdCVM798DjDxiIamF8h9WUrmRC87TCnxDyxyHoVmOH0EbGAmbCrC3aw+PfPTXK6UTSQc26g==", "_resolved": "/tmp/fde915e75b6737256e48822001ac96da/data-uri-to-buffer-6.0.0.tgz", "_from": "file:data-uri-to-buffer-6.0.0.tgz", "_nodeVersion": "20.8.0", "_npmVersion": "10.1.0", "dist": {"integrity": "sha512-pMT6awii4zyQu/fUdCVM798DjDxiIamF8h9WUrmRC87TCnxDyxyHoVmOH0EbGAmbCrC3aw+PfPTXK6UTSQc26g==", "shasum": "80d4c58a7a9e3145dd623cfd138b5b4fec9cbc38", "tarball": "https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-6.0.0.tgz", "fileCount": 8, "unpackedSize": 14349, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH2iOk2diMyLCeEqRWfrBKkP4IYQbLWvTnCrbKFl/rjDAiAJjxe0ivVwX0PNHWRsIPJkgTOukU5Z7iT1klQ7tTS7Pg=="}]}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/data-uri-to-buffer_6.0.0_1696084191053_0.5843435140881559"}, "_hasShrinkwrap": false}, "6.0.1": {"name": "data-uri-to-buffer", "version": "6.0.1", "description": "Create an ArrayBuffer instance from a Data URI string", "main": "./dist/index.js", "types": "./dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/data-uri-to-buffer"}, "engines": {"node": ">= 14"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert", "rfc2397", "2397"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "devDependencies": {"@types/jest": "^27.0.2", "@types/node": "^14.18.45", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "tsconfig": "0.0.0"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail", "lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_id": "data-uri-to-buffer@6.0.1", "_integrity": "sha512-MZd3VlchQkp8rdend6vrx7MmVDJzSNTBvghvKjirLkD+WTChA3KUf0jkE68Q4UyctNqI11zZO9/x2Yx+ub5Cvg==", "_resolved": "/tmp/30694727d39c5c4314bc18d1dd4d19de/data-uri-to-buffer-6.0.1.tgz", "_from": "file:data-uri-to-buffer-6.0.1.tgz", "_nodeVersion": "20.8.0", "_npmVersion": "10.1.0", "dist": {"integrity": "sha512-MZd3VlchQkp8rdend6vrx7MmVDJzSNTBvghvKjirLkD+WTChA3KUf0jkE68Q4UyctNqI11zZO9/x2Yx+ub5Cvg==", "shasum": "540bd4c8753a25ee129035aebdedf63b078703c7", "tarball": "https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-6.0.1.tgz", "fileCount": 8, "unpackedSize": 14317, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCcBtG+obRfoXg5RGkUXcVjgtQ+ih1HxvjMgUsYJdXusQIgOXCiFINv/1sw6/AQB5Wrx9LBgQxjEQhsUKOFfAcHmOw="}]}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/data-uri-to-buffer_6.0.1_1696085397616_0.6014027081142856"}, "_hasShrinkwrap": false}, "6.0.2": {"name": "data-uri-to-buffer", "version": "6.0.2", "description": "Create an ArrayBuffer instance from a Data URI string", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {"node": "./dist/node.js", "default": "./dist/index.js"}, "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/data-uri-to-buffer"}, "engines": {"node": ">= 14"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert", "rfc2397", "2397"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "devDependencies": {"@types/jest": "^27.0.2", "@types/node": "^14.18.45", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "tsconfig": "0.0.0"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail", "lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_id": "data-uri-to-buffer@6.0.2", "_integrity": "sha512-7hvf7/GW8e86rW0ptuwS3OcBGDjIi6SZva7hCyWC0yYry2cOPmLIjXAUHI6DK2HsnwJd9ifmt57i8eV2n4YNpw==", "_resolved": "/tmp/fb3865a21fe72e0dc250a7262903a94e/data-uri-to-buffer-6.0.2.tgz", "_from": "file:data-uri-to-buffer-6.0.2.tgz", "_nodeVersion": "20.11.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-7hvf7/GW8e86rW0ptuwS3OcBGDjIi6SZva7hCyWC0yYry2cOPmLIjXAUHI6DK2HsnwJd9ifmt57i8eV2n4YNpw==", "shasum": "8a58bb67384b261a38ef18bea1810cb01badd28b", "tarball": "https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-6.0.2.tgz", "fileCount": 16, "unpackedSize": 16799, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCRRrwO+BS1tCn0U6jL1RKkjkALy0vPnMA5m9jKvkOIEgIhALe4GtXXnoFrnEKrvzC/29aAmCi97lfoV050TkdlB6Dt"}]}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/data-uri-to-buffer_6.0.2_1707762257392_0.9218302669200353"}, "_hasShrinkwrap": false}}, "readme": "data-uri-to-buffer\n==================\n### Create an ArrayBuffer instance from a [Data URI][rfc] string\n\nThis module accepts a [\"data\" URI][rfc] String of data, and returns\nan `ArrayBuffer` instance with the decoded data.\n\nThis module is intended to work on a large variety of JavaScript\nruntimes, including Node.js and web browsers.\n\nExample\n-------\n\n```typescript\nimport { dataUriToBuffer } from 'data-uri-to-buffer';\n\n// plain-text data is supported\nlet uri = 'data:,Hello%2C%20World!';\nlet parsed = dataUriToBuffer(uri);\nconsole.log(new TextDecoder().decode(parsed.buffer));\n// 'Hello, World!'\n\n// base64-encoded data is supported\nuri = 'data:text/plain;base64,SGVsbG8sIFdvcmxkIQ%3D%3D';\nparsed = dataUriToBuffer(uri);\nconsole.log(new TextDecoder().decode(parsed.buffer));\n// 'Hello, World!'\n```\n\n\nAPI\n---\n\n```typescript\nexport interface ParsedDataURI {\n\ttype: string;\n\ttypeFull: string;\n\tcharset: string;\n\tbuffer: ArrayBuffer;\n}\n```\n\n### dataUriToBuffer(uri: string | URL) → ParsedDataURI\n\nThe `type` property gets set to the main type portion of\nthe \"mediatype\" portion of the \"data\" URI, or defaults to `\"text/plain\"` if not\nspecified.\n\nThe `typeFull` property gets set to the entire\n\"mediatype\" portion of the \"data\" URI (including all parameters), or defaults\nto `\"text/plain;charset=US-ASCII\"` if not specified.\n\nThe `charset` property gets set to the Charset portion of\nthe \"mediatype\" portion of the \"data\" URI, or defaults to `\"US-ASCII\"` if the\nentire type is not specified, or defaults to `\"\"` otherwise.\n\n*Note*: If only the main type is specified but not the charset, e.g.\n`\"data:text/plain,abc\"`, the charset is set to the empty string. The spec only\ndefaults to US-ASCII as charset if the entire type is not specified.\n\n[rfc]: http://tools.ietf.org/html/rfc2397\n", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "time": {"modified": "2024-02-12T18:24:17.713Z", "created": "2014-01-03T00:53:10.915Z", "0.0.1": "2014-01-03T00:53:10.915Z", "0.0.2": "2014-01-09T00:19:25.592Z", "0.0.3": "2014-01-09T01:12:26.219Z", "0.0.4": "2015-06-29T20:04:15.846Z", "1.0.0": "2017-06-10T00:35:08.216Z", "1.1.0": "2017-07-18T01:29:01.904Z", "1.2.0": "2017-07-19T01:25:17.266Z", "2.0.0": "2017-07-19T01:26:36.607Z", "2.0.1": "2019-04-04T18:31:54.601Z", "2.0.2": "2019-09-26T21:10:06.304Z", "3.0.0": "2019-09-27T21:51:15.948Z", "3.0.1": "2020-05-30T00:51:45.700Z", "4.0.0": "2021-09-27T10:11:39.112Z", "4.0.1": "2023-01-13T00:47:12.963Z", "5.0.0": "2023-05-04T20:33:19.663Z", "5.0.1": "2023-05-05T22:04:10.448Z", "6.0.0": "2023-09-30T14:29:51.307Z", "6.0.1": "2023-09-30T14:49:57.761Z", "6.0.2": "2024-02-12T18:24:17.563Z"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert", "rfc2397", "2397"], "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/data-uri-to-buffer"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"nomemires": true, "hecto932": true, "vonthar": true, "adius": true, "yusef.ho.tw": true}}