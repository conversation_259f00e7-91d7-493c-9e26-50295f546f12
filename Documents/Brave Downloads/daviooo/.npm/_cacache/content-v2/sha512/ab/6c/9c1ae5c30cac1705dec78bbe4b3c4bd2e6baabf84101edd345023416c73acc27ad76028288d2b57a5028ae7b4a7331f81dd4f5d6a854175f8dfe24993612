{"_id": "isexe", "_rev": "12-f57d97ae4c5ceb17debef253e547232c", "name": "isexe", "description": "Minimal module to check if a file is executable.", "dist-tags": {"latest": "3.1.1"}, "versions": {"1.0.0": {"name": "isexe", "version": "1.0.0", "description": "Minimal module to check if a file is executable.", "main": "index.js", "directories": {"test": "test"}, "devDependencies": {"mkdirp": "^0.5.1", "rimraf": "^2.5.0", "tap": "^5.0.1"}, "scripts": {"test": "tap test/*.js --cov"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "d1c8749b748c5ed8bbd16cccd2ef19236b9e673b", "_id": "isexe@1.0.0", "_shasum": "9aa37a1d11d27b523bec4f8791b72af1ead44ee3", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "4.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "9aa37a1d11d27b523bec4f8791b72af1ead44ee3", "tarball": "https://registry.npmjs.org/isexe/-/isexe-1.0.0.tgz", "integrity": "sha512-xOmCsItMYmB/ti6mbBXTZ86HwBXqZB4TjCkACLNZeaCUjIvTNEd/QaFi/zOQEs31CLSqHZHIUOo7zicmLmKCOw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEgR9IgHQ0Jb8uoR1vAKbCAMVvr8/Y2JEnVpKWL39gUEAiEAjpVes+ziil3oWvN+f7YcebFDhjJbL+ScfLgJW+vNP70="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}]}, "1.0.1": {"name": "isexe", "version": "1.0.1", "description": "Minimal module to check if a file is executable.", "main": "index.js", "directories": {"test": "test"}, "devDependencies": {"mkdirp": "^0.5.1", "rimraf": "^2.5.0", "tap": "^5.0.1"}, "scripts": {"test": "tap test/*.js --cov"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/isaacs/isexe.git"}, "keywords": [], "bugs": {"url": "https://github.com/isaacs/isexe/issues"}, "homepage": "https://github.com/isaacs/isexe#readme", "gitHead": "111ec9c0a0ec7ed53073c1893db9745ed8d41758", "_id": "isexe@1.0.1", "_shasum": "5db010ed38a649d12d5faf9884b3474002e66a65", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "4.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "5db010ed38a649d12d5faf9884b3474002e66a65", "tarball": "https://registry.npmjs.org/isexe/-/isexe-1.0.1.tgz", "integrity": "sha512-MUtoZRyOOMsCXxIrBrVvjIGxGIyfjOfuHoG79OwI9U4M8zH/XTwngUrbjr+bfMxgg6mh3w+9RdqldIxLPnFLbg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEDKVIukLdpz5WS+azHw+9CvPtl1aR0mFcxY2EQeZCscAiEA3wMJTV82O6t65P9rbbvPTDgf7OjxsFCu3X/nTspjvSo="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}]}, "1.1.0": {"name": "isexe", "version": "1.1.0", "description": "Minimal module to check if a file is executable.", "main": "index.js", "directories": {"test": "test"}, "devDependencies": {"mkdirp": "^0.5.1", "rimraf": "^2.5.0", "tap": "^5.1.2"}, "scripts": {"test": "tap test/*.js --branches=100 --statements=100 --functions=100 --lines=100"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/isaacs/isexe.git"}, "keywords": [], "bugs": {"url": "https://github.com/isaacs/isexe/issues"}, "homepage": "https://github.com/isaacs/isexe#readme", "gitHead": "5cadd61b64338cdd49249720c40fff60deedd051", "_id": "isexe@1.1.0", "_shasum": "3cdbafc1a3b16b11290ce4e9da58c781dc368931", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "4.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "3cdbafc1a3b16b11290ce4e9da58c781dc368931", "tarball": "https://registry.npmjs.org/isexe/-/isexe-1.1.0.tgz", "integrity": "sha512-CpfhYyGLBdgJjV+b8p0FR4tILXnL70ZVeoV2oC56CMjDVRHl5R/qznlunU3ZKu+3xFyIZLbrSGSklkAFVPtK1A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDBAADDCGd13VkvVU2y0IWWfKFBACykTQz505D16C21sAiBNtwsw3MfGBEH+1nlzhzsHvW+Xz+k8Dnv8kKZVYViFTA=="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}]}, "1.1.1": {"name": "isexe", "version": "1.1.1", "description": "Minimal module to check if a file is executable.", "main": "index.js", "directories": {"test": "test"}, "devDependencies": {"mkdirp": "^0.5.1", "rimraf": "^2.5.0", "tap": "^5.1.2"}, "scripts": {"test": "tap test/*.js --branches=100 --statements=100 --functions=100 --lines=100"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/isaacs/isexe.git"}, "keywords": [], "bugs": {"url": "https://github.com/isaacs/isexe/issues"}, "homepage": "https://github.com/isaacs/isexe#readme", "gitHead": "af83031caed58654ad9d20b98eb710d383618ad7", "_id": "isexe@1.1.1", "_shasum": "f0d4793ed2fb5c46bfdeab760bbb965f4485a66c", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "4.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "f0d4793ed2fb5c46bfdeab760bbb965f4485a66c", "tarball": "https://registry.npmjs.org/isexe/-/isexe-1.1.1.tgz", "integrity": "sha512-k1YjpQ5+RsFWJ5zY6wKt3ozCKse+HrkOZNg5BwkbB3xrlbKw42V0xZUUwUqGFNvUwW9oKIO4/ejxo83p4MWSgw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE7gPPLpzYbXhErit3+XZ4N3zG4n5lNTe1GlR2E5PDGWAiEA8A0v0wJut6Eulwb4mex41aV3ZWj95TBTdJ18iQGgMgo="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}]}, "1.1.2": {"name": "isexe", "version": "1.1.2", "description": "Minimal module to check if a file is executable.", "main": "index.js", "directories": {"test": "test"}, "devDependencies": {"mkdirp": "^0.5.1", "rimraf": "^2.5.0", "tap": "^5.1.2"}, "scripts": {"test": "tap test/*.js --branches=100 --statements=100 --functions=100 --lines=100"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/isaacs/isexe.git"}, "keywords": [], "bugs": {"url": "https://github.com/isaacs/isexe/issues"}, "homepage": "https://github.com/isaacs/isexe#readme", "gitHead": "1882eed72c2ba152f4dd1336d857b0755ae306d9", "_id": "isexe@1.1.2", "_shasum": "36f3e22e60750920f5e7241a476a8c6a42275ad0", "_from": ".", "_npmVersion": "3.7.0", "_nodeVersion": "4.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "36f3e22e60750920f5e7241a476a8c6a42275ad0", "tarball": "https://registry.npmjs.org/isexe/-/isexe-1.1.2.tgz", "integrity": "sha512-d2eJzK691yZwPHcv1LbeAOa91yMJ9QmfTgSO1oXB65ezVhXQsxBac2vEB4bMVms9cGzaA99n6V2viHMq82VLDw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDhCj1z4E0D+nl8qM/SEor6k0BAzkS0pTlvGLbrBOLdpAiEA9Bjuyz1bsL4emg307DiPu6Leppxpz/OmrYA+P1ykZiw="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-9-west.internal.npmjs.com", "tmp": "tmp/isexe-1.1.2.tgz_1454992795963_0.7608721863944083"}}, "2.0.0": {"name": "isexe", "version": "2.0.0", "description": "Minimal module to check if a file is executable.", "main": "index.js", "directories": {"test": "test"}, "devDependencies": {"mkdirp": "^0.5.1", "rimraf": "^2.5.0", "tap": "^10.3.0"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/isaacs/isexe.git"}, "keywords": [], "bugs": {"url": "https://github.com/isaacs/isexe/issues"}, "homepage": "https://github.com/isaacs/isexe#readme", "gitHead": "10f8be491aab2e158c7e20df64a7f90ab5b5475c", "_id": "isexe@2.0.0", "_shasum": "e8fbf374dc556ff8947a10dcb0572d633f2cfa10", "_from": ".", "_npmVersion": "4.4.2", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "e8fbf374dc556ff8947a10dcb0572d633f2cfa10", "tarball": "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz", "integrity": "sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIADIHL0vHAyBDSHK5BeCzNQb3ooCTHoBU+bp0my0d0pwAiAEzxUmLzKn2xsUOTDZngxVYgQ+2BELnj8z+ZNAJWENzA=="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/isexe-2.0.0.tgz_1490230396126_0.8949183595832437"}}, "3.0.0": {"name": "isexe", "version": "3.0.0", "description": "Minimal module to check if a file is executable.", "main": "./dist/cjs/index.js", "module": "./dist/mjs/index.js", "types": "./dist/cjs/index.js", "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}, "./posix": {"import": {"types": "./dist/mjs/posix.d.ts", "default": "./dist/mjs/posix.js"}, "require": {"types": "./dist/cjs/posix.d.ts", "default": "./dist/cjs/posix.js"}}, "./win32": {"import": {"types": "./dist/mjs/win32.d.ts", "default": "./dist/mjs/win32.js"}, "require": {"types": "./dist/cjs/win32.d.ts", "default": "./dist/cjs/win32.js"}}, "./package.json": "./package.json"}, "devDependencies": {"@types/node": "^20.4.5", "@types/tap": "^15.0.8", "c8": "^8.0.1", "mkdirp": "^0.5.1", "prettier": "^2.8.8", "rimraf": "^2.5.0", "sync-content": "^1.0.2", "tap": "^16.3.8", "ts-node": "^10.9.1", "typedoc": "^0.24.8", "typescript": "^5.1.6"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "prepare": "tsc -p tsconfig/cjs.json && tsc -p tsconfig/esm.json && bash ./scripts/fixup.sh", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "c8 tap", "snap": "c8 tap", "format": "prettier --write . --loglevel warn --ignore-path ../../.prettierignore --cache", "typedoc": "typedoc --tsconfig tsconfig/esm.json ./src/*.ts"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"coverage": false, "node-arg": ["--enable-source-maps", "--no-warnings", "--loader", "ts-node/esm"], "ts": false}, "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/isexe.git"}, "engines": {"node": ">=16"}, "_id": "isexe@3.0.0", "gitHead": "d298cc33c3255ed0877c5c539eb5d1698d318bb9", "bugs": {"url": "https://github.com/isaacs/isexe/issues"}, "homepage": "https://github.com/isaacs/isexe#readme", "_nodeVersion": "18.16.0", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-IqRolFFz467y4realjJZCpAG6L6Ke9jyYHtthZV5A1vMiDy2RLCUrGrAHBbQpEvM17FlMjXtdliLIxyzEQwJnA==", "shasum": "a09b3e90e1f6a1a53a2b7098e3bdfb668e8b6811", "tarball": "https://registry.npmjs.org/isexe/-/isexe-3.0.0.tgz", "fileCount": 37, "unpackedSize": 230954, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICrt/joZESUKxH0GSgYuqW5aJxXpn2Z7OMtLE4eIwzA4AiADRZNFw4yjBIl3E5OfblKxO4C+EVHUcjr4vIpTXYPj7w=="}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/isexe_3.0.0_1690698068795_0.1110751491420936"}, "_hasShrinkwrap": false}, "3.1.0": {"name": "isexe", "version": "3.1.0", "description": "Minimal module to check if a file is executable.", "main": "./dist/cjs/index.js", "module": "./dist/mjs/index.js", "types": "./dist/cjs/index.js", "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}, "./posix": {"import": {"types": "./dist/mjs/posix.d.ts", "default": "./dist/mjs/posix.js"}, "require": {"types": "./dist/cjs/posix.d.ts", "default": "./dist/cjs/posix.js"}}, "./win32": {"import": {"types": "./dist/mjs/win32.d.ts", "default": "./dist/mjs/win32.js"}, "require": {"types": "./dist/cjs/win32.d.ts", "default": "./dist/cjs/win32.js"}}, "./package.json": "./package.json"}, "devDependencies": {"@types/node": "^20.4.5", "@types/tap": "^15.0.8", "c8": "^8.0.1", "mkdirp": "^0.5.1", "prettier": "^2.8.8", "rimraf": "^2.5.0", "sync-content": "^1.0.2", "tap": "^16.3.8", "ts-node": "^10.9.1", "typedoc": "^0.24.8", "typescript": "^5.1.6"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "prepare": "tsc -p tsconfig/cjs.json && tsc -p tsconfig/esm.json && bash ./scripts/fixup.sh", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "c8 tap", "snap": "c8 tap", "format": "prettier --write . --loglevel warn --ignore-path ../../.prettierignore --cache", "typedoc": "typedoc --tsconfig tsconfig/esm.json ./src/*.ts"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"coverage": false, "node-arg": ["--enable-source-maps", "--no-warnings", "--loader", "ts-node/esm"], "ts": false}, "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/isexe.git"}, "engines": {"node": ">=16"}, "_id": "isexe@3.1.0", "gitHead": "c23efcc601ceb87f1df108d47e9dd9f09e6d0673", "bugs": {"url": "https://github.com/isaacs/isexe/issues"}, "homepage": "https://github.com/isaacs/isexe#readme", "_nodeVersion": "18.16.0", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-RTzKlT4435HNIbKWZtifloLcA36uSBc/AsC3pl3nLZaRUZfF0gQyMbvXNAT262O0RB1Vd6rA8oXlb+iKJM7E/g==", "shasum": "a980b5c36f349a306453ba35f05248e54f75cc60", "tarball": "https://registry.npmjs.org/isexe/-/isexe-3.1.0.tgz", "fileCount": 37, "unpackedSize": 232443, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCK1oAuj6nromNeb1SzgG7fIRo9kzcX9MbXG7xDEGnpjwIhANLXinB1hyIF0L7jBN3tzMT9puYvnurnLOY63yGTo4N4"}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/isexe_3.1.0_1690750737400_0.025795616714981318"}, "_hasShrinkwrap": false}, "3.1.1": {"name": "isexe", "version": "3.1.1", "description": "Minimal module to check if a file is executable.", "main": "./dist/cjs/index.js", "module": "./dist/mjs/index.js", "types": "./dist/cjs/index.js", "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}, "./posix": {"import": {"types": "./dist/mjs/posix.d.ts", "default": "./dist/mjs/posix.js"}, "require": {"types": "./dist/cjs/posix.d.ts", "default": "./dist/cjs/posix.js"}}, "./win32": {"import": {"types": "./dist/mjs/win32.d.ts", "default": "./dist/mjs/win32.js"}, "require": {"types": "./dist/cjs/win32.d.ts", "default": "./dist/cjs/win32.js"}}, "./package.json": "./package.json"}, "devDependencies": {"@types/node": "^20.4.5", "@types/tap": "^15.0.8", "c8": "^8.0.1", "mkdirp": "^0.5.1", "prettier": "^2.8.8", "rimraf": "^2.5.0", "sync-content": "^1.0.2", "tap": "^16.3.8", "ts-node": "^10.9.1", "typedoc": "^0.24.8", "typescript": "^5.1.6"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "prepare": "tsc -p tsconfig/cjs.json && tsc -p tsconfig/esm.json && bash ./scripts/fixup.sh", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "c8 tap", "snap": "c8 tap", "format": "prettier --write . --loglevel warn --ignore-path ../../.prettierignore --cache", "typedoc": "typedoc --tsconfig tsconfig/esm.json ./src/*.ts"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"coverage": false, "node-arg": ["--enable-source-maps", "--no-warnings", "--loader", "ts-node/esm"], "ts": false}, "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/isexe.git"}, "engines": {"node": ">=16"}, "_id": "isexe@3.1.1", "gitHead": "8e5d06b2f0e6d7cfe83d19eb0a9c572d2c598232", "bugs": {"url": "https://github.com/isaacs/isexe/issues"}, "homepage": "https://github.com/isaacs/isexe#readme", "_nodeVersion": "18.16.0", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-LpB/54B+/2J5hqQ7imZHfdU31OlgQqx7ZicVlkm9kzg9/w8GKLEcFfJl/t7DCEDueOyBAD6zCCwTO6Fzs0NoEQ==", "shasum": "4a407e2bd78ddfb14bea0c27c6f7072dde775f0d", "tarball": "https://registry.npmjs.org/isexe/-/isexe-3.1.1.tgz", "fileCount": 37, "unpackedSize": 42976, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBt7dbhTPX0ky58uojKSFYst9buz67jou2hy++gCFoGJAiEA6bCzyCRapCKkPSJwo7lcebsUmoRa25od3DcKmMtaygM="}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/isexe_3.1.1_1691000862689_0.6700451299416117"}, "_hasShrinkwrap": false}}, "readme": "# isexe\n\nMinimal module to check if a file is executable, and a normal file.\n\nUses `fs.stat` and tests against the `PATHEXT` environment variable on\nWindows.\n\n## USAGE\n\n```js\nimport { isexe, sync } from 'isexe'\n// or require() works too\n// const { isexe } = require('isexe')\nisexe('some-file-name').then(isExe => {\n  if (isExe) {\n    console.error('this thing can be run')\n  } else {\n    console.error('cannot be run')\n  }\n}, (err) => {\n  console.error('probably file doesnt exist or something')\n})\n\n// same thing but synchronous, throws errors\nisExe = sync('some-file-name')\n\n// treat errors as just \"not executable\"\nconst isExe = await isexe('maybe-missing-file', { ignoreErrors: true })\nconst isExe = sync('maybe-missing-file', { ignoreErrors: true })\n```\n\n## API\n\n### `isexe(path, [options]) => Promise<boolean>`\n\nCheck if the path is executable.\n\nWill raise whatever errors may be raised by `fs.stat`, unless\n`options.ignoreErrors` is set to true.\n\n### `sync(path, [options]) => boolean`\n\nSame as `isexe` but returns the value and throws any errors raised.\n\n## Platform Specific Implementations\n\nIf for some reason you want to use the implementation for a\nspecific platform, you can do that.\n\n```js\nimport { win32, posix } from 'isexe'\nwin32.isexe(...)\nwin32.sync(...)\n// etc\n\n// or:\nimport { isexe, sync } from 'isexe/posix'\n```\n\nThe default exported implementation will be chosen based on\n`process.platform`.\n\n### Options\n\n```ts\nimport type IsexeOptions from 'isexe'\n```\n\n* `ignoreErrors` Treat all errors as \"no, this is not\n  executable\", but don't raise them.\n* `uid` Number to use as the user id on posix\n* `gid` Number to use as the group id on posix\n* `pathExt` List of path extensions to use instead of `PATHEXT`\n  environment variable on Windows.\n", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "time": {"modified": "2023-08-02T18:27:42.955Z", "created": "2016-01-17T05:25:47.501Z", "1.0.0": "2016-01-17T05:25:47.501Z", "1.0.1": "2016-01-20T01:35:20.406Z", "1.1.0": "2016-01-26T02:03:38.497Z", "1.1.1": "2016-01-26T02:05:55.873Z", "1.1.2": "2016-02-09T04:39:57.031Z", "2.0.0": "2017-03-23T00:53:16.356Z", "3.0.0": "2023-07-30T06:21:09.014Z", "3.1.0": "2023-07-30T20:58:57.607Z", "3.1.1": "2023-08-02T18:27:42.848Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "readmeFilename": "README.md", "homepage": "https://github.com/isaacs/isexe#readme", "repository": {"type": "git", "url": "git+https://github.com/isaacs/isexe.git"}, "bugs": {"url": "https://github.com/isaacs/isexe/issues"}, "users": {"flumpus-dev": true}}