{"_id": "cacheable-request", "_rev": "70-4b44011fae4b06370927481404c4b3bd", "name": "cacheable-request", "dist-tags": {"beta": "7.0.3", "latest": "13.0.3"}, "versions": {"0.0.0": {"name": "cacheable-request", "version": "0.0.0", "keywords": ["async", "asynchronous", "non-blocking", "base64", "encode", "decode"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@0.0.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/cacheable-request", "bugs": {"url": "https://github.com/lukechilds/cacheable-request/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "f4f1805e973bee28ed9d51891c34f1330276e8d6", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-0.0.0.tgz", "integrity": "sha512-wQQZ1mqV5oGz+4Wp68EIND0F9Iarzj+u43BuZpRaYaM8Z8dvh9sKmdpddZAECbSFuC7xzsLX/USVw9PFiHSEiQ==", "signatures": [{"sig": "MEUCIQC6HzBmy89eQUaXPzQhhnri1ndRnidXLB3TWHyJxOdY/wIgXmNmB1AeEgq3m2Kohcm6qEX/HOtXJJd/wBsdQdt+3VM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "gitHead": "aede9335680bfc35ee56e26eeb37af42a44a73a7", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/cacheable-request.git", "type": "git"}, "_npmVersion": "5.0.0", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "8.0.0", "dependencies": {}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.19.1", "nyc": "^10.3.2", "coveralls": "^2.13.1", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request-0.0.0.tgz_1496407010001_0.7954089867416769", "host": "s3://npm-registry-packages"}}, "0.1.0": {"name": "cacheable-request", "version": "0.1.0", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@0.1.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/cacheable-request", "bugs": {"url": "https://github.com/lukechilds/cacheable-request/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "d63d59e819f02f23c744f0e116af49f7d340b177", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-0.1.0.tgz", "integrity": "sha512-JPqaaVPmkswSoSwJ7+QC0OChdmbP3AD59PkdNmisW098jtz0N8hQJrON7Ruc+Pk1NKESn75i8g/LU5qi8tE/lw==", "signatures": [{"sig": "MEUCIBjo3w+dr2roIulqIJgmALUYI4Eqx9RJ4YePDYT5je99AiEAgrBft7QkSYyXx9z1from33nwkiV6gZzVATqCdNpP77A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "gitHead": "afa2b9eae682df60f28e398f5bf91ddd64b336d1", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/cacheable-request.git", "type": "git"}, "_npmVersion": "5.0.0", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "8.0.0", "dependencies": {"get-stream": "^3.0.0", "responselike": "^0.1.0", "normalize-url": "^1.9.1", "url-parse-lax": "^1.0.0", "lowercase-keys": "^1.0.0", "http-cache-semantics": "^3.7.3"}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.19.1", "nyc": "^10.3.2", "coveralls": "^2.13.1", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request-0.1.0.tgz_1497017206986_0.45462921005673707", "host": "s3://npm-registry-packages"}}, "0.2.0": {"name": "cacheable-request", "version": "0.2.0", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@0.2.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/cacheable-request", "bugs": {"url": "https://github.com/lukechilds/cacheable-request/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "2a5d28aea595e9877175f368516b112fd18f1cca", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-0.2.0.tgz", "integrity": "sha512-da3a+Os26/qymCzK5z6Hon26oJL56UfyPYyq0BR7V6DjJJ19iOoZlEWOy99t0SOG/42URjYorQEa+s83HAoTNA==", "signatures": [{"sig": "MEQCIEVqYyACMQYELuYy3ESYlCYUlab5FQyXKtYF9tlOKJG3AiAPkxT1tz/wGQmL698/vKpo5i89nYoxb9rW3ozmMnNApw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "gitHead": "8b0bd76a9f4fa3c3b07323e052202d0a7a7d0db2", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/cacheable-request.git", "type": "git"}, "_npmVersion": "5.0.0", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "8.0.0", "dependencies": {"get-stream": "^3.0.0", "responselike": "^0.1.0", "normalize-url": "^1.9.1", "url-parse-lax": "^1.0.0", "clone-response": "^0.2.2", "lowercase-keys": "^1.0.0", "http-cache-semantics": "^3.7.3"}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.19.1", "nyc": "^10.3.2", "coveralls": "^2.13.1", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request-0.2.0.tgz_1497067124166_0.8747847008053213", "host": "s3://npm-registry-packages"}}, "0.3.0": {"name": "cacheable-request", "version": "0.3.0", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@0.3.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/cacheable-request", "bugs": {"url": "https://github.com/lukechilds/cacheable-request/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "26f62a65a5d9550c0af2a526bb6d97f995e12e4f", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-0.3.0.tgz", "integrity": "sha512-Y+MYzXZPIv/Ovj2cLWx8lgh/Qeapk0zaVhA7VdqFxZVJi/VUU9Bz4UOi/Tbq1RzuBnjOacO4RRkZ3RmRtvVWNw==", "signatures": [{"sig": "MEUCIQCOc7F0RE6oL6Qcc33uQOIv3bUX610Uw4tldF9/k/PxLgIgb1cOhZFg5E2d3acogVSC8NKo7CQ5VOIGJO0Hx9Epz3U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "gitHead": "02e5b2702b69ee0c55724692c2a1924819f6dfd8", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/cacheable-request.git", "type": "git"}, "_npmVersion": "5.0.0", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "8.0.0", "dependencies": {"get-stream": "^3.0.0", "responselike": "^0.1.0", "normalize-url": "^1.9.1", "url-parse-lax": "^1.0.0", "clone-response": "^0.2.2", "lowercase-keys": "^1.0.0", "http-cache-semantics": "^3.7.3"}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.19.1", "nyc": "^10.3.2", "coveralls": "^2.13.1", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request-0.3.0.tgz_1497079276774_0.04189830622635782", "host": "s3://npm-registry-packages"}}, "0.4.0": {"name": "cacheable-request", "version": "0.4.0", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@0.4.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/cacheable-request", "bugs": {"url": "https://github.com/lukechilds/cacheable-request/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "071a42a18ca8a45c28408ce15231209809fe0ced", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-0.4.0.tgz", "integrity": "sha512-7l73UsuJH2G7FWW/HMH+dPBMI4TNVPOGsKIoIFrT/6/SXVJQ4irLYTpeJOoi6uwx/jpTeBVWhhezZM7ZnsSomQ==", "signatures": [{"sig": "MEUCIQCCRyD5gRIxmsiCkzwXTX2Kbm7glwZn7bXBJN039aBP0gIgHiUZwryn0WiS87vpVFf8i66CGN8/dW/Mgxm0gga/7dI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "071a42a18ca8a45c28408ce15231209809fe0ced", "gitHead": "93eb8379ca2e9eb4d7475eb7bac38906ee79db7b", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/cacheable-request.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {"keyv": "^1.0.4", "get-stream": "^3.0.0", "responselike": "^1.0.0", "normalize-url": "^1.9.1", "clone-response": "^1.0.1", "lowercase-keys": "^1.0.0", "http-cache-semantics": "^3.7.3"}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.21.0", "nyc": "^11.0.2", "delay": "^2.0.0", "coveralls": "^2.13.1", "get-stream": "^3.0.0", "create-test-server": "^2.0.0", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request-0.4.0.tgz_1502496737940_0.10047852876596153", "host": "s3://npm-registry-packages"}}, "0.5.0": {"name": "cacheable-request", "version": "0.5.0", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@0.5.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/cacheable-request", "bugs": {"url": "https://github.com/lukechilds/cacheable-request/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "b08b6feaf2dbb0ed95897d3d035ee379cfe24581", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-0.5.0.tgz", "integrity": "sha512-x2w/LuW0wX6d1+Dw+rs3E8WEY2lShOGYXertcWzmKjqypZYv6pVzJ0jhxMjtOMYtv49ZVN7WyyqD8HJ3pg8q+w==", "signatures": [{"sig": "MEUCICcN4z8WroVUtZSkoBdYOfAm4Wd10INFceBjRCaUI93dAiEAm6oG16jBivJBdoHuEWIAi4ZGks+Ff0SsKjw9O6mcH4Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "b08b6feaf2dbb0ed95897d3d035ee379cfe24581", "gitHead": "058843d1b00e336be1ce61434feaa888dd6e5ea8", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/cacheable-request.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {"keyv": "^1.0.4", "get-stream": "^3.0.0", "responselike": "^1.0.0", "normalize-url": "^1.9.1", "clone-response": "^1.0.1", "lowercase-keys": "^1.0.0", "http-cache-semantics": "^3.7.3"}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.21.0", "nyc": "^11.0.2", "pify": "^3.0.0", "this": "^1.0.2", "delay": "^2.0.0", "sqlite3": "^3.1.9", "coveralls": "^2.13.1", "get-stream": "^3.0.0", "keyv-sqlite": "^1.2.4", "create-test-server": "^2.0.0", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request-0.5.0.tgz_1502625752366_0.030264430213719606", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "cacheable-request", "version": "1.0.0", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@1.0.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/cacheable-request", "bugs": {"url": "https://github.com/lukechilds/cacheable-request/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "46b82562631895eda02913ab0cdc0910c0266a48", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-1.0.0.tgz", "integrity": "sha512-EWLq0/xldWxxCPgP7/zwTjUgeHqxHcmzemnV341HGXjlNSO3R+q/lVkHBNfGqE4q5Xp6PONrisuydKJUs1mY9w==", "signatures": [{"sig": "MEQCICutJKJhshF4ibi5NXfxRjCqnuhEYqZosmfi0hk376+HAiBX5X5ewJOiZ1TAHT/vwM7+DwgPmPvNmDncCWv+kW8Gug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "46b82562631895eda02913ab0cdc0910c0266a48", "gitHead": "4ffe384c90bd04deb8c365ab177a8d582a6c0b52", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/cacheable-request.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {"keyv": "^2.0.1", "get-stream": "^3.0.0", "responselike": "^1.0.0", "normalize-url": "^1.9.1", "clone-response": "^1.0.1", "lowercase-keys": "^1.0.0", "http-cache-semantics": "^3.7.3"}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.21.0", "nyc": "^11.0.2", "pify": "^3.0.0", "this": "^1.0.2", "delay": "^2.0.0", "sqlite3": "^3.1.9", "coveralls": "^2.13.1", "get-stream": "^3.0.0", "@keyv/sqlite": "^1.2.6", "create-test-server": "^2.0.0", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request-1.0.0.tgz_1502631103552_0.8947162763215601", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "cacheable-request", "version": "1.0.1", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@1.0.1", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/cacheable-request", "bugs": {"url": "https://github.com/lukechilds/cacheable-request/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "846ed1388acda5037bdd36cd0f3e9180bda5f79b", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-1.0.1.tgz", "integrity": "sha512-5jeWcH0TCJto1L+rnhcvQoYWQwIrpK8ZezXJTvxD4lQrG0NCY+dqvJK3XrSIybzwZQLlg+BcX0ZOSWxKv5ba+A==", "signatures": [{"sig": "MEYCIQDcqgnaZ6jn/5ajq/TxmINMv/KjFiQrM35aRO9CSpJFyQIhAMPRimgSVvZ8RhIopWI2P6logJQYAJkz/tCIv8U1ObES", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "846ed1388acda5037bdd36cd0f3e9180bda5f79b", "gitHead": "7f38564fb96741998607e3724629a5cabf694fba", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/cacheable-request.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {"keyv": "^2.0.1", "get-stream": "^3.0.0", "responselike": "^1.0.0", "normalize-url": "^1.9.1", "clone-response": "^1.0.1", "lowercase-keys": "^1.0.0", "http-cache-semantics": "^3.7.3"}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.21.0", "nyc": "^11.0.2", "pify": "^3.0.0", "this": "^1.0.2", "delay": "^2.0.0", "sqlite3": "^3.1.9", "coveralls": "^2.13.1", "get-stream": "^3.0.0", "@keyv/sqlite": "^1.2.6", "create-test-server": "^2.0.0", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request-1.0.1.tgz_1502632786565_0.8828422036021948", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "cacheable-request", "version": "1.0.2", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@1.0.2", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/cacheable-request", "bugs": {"url": "https://github.com/lukechilds/cacheable-request/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "9a69c7c5a7dcb3bda8e399f55bc2bd723b435fab", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-1.0.2.tgz", "integrity": "sha512-zn6gQJh3U9ro/hto9MoTdulZ0YKoEXVs9OAD3floWXDp3fbJ45z9dcZpHVQ+AFu2CrpcfhI+ria4ZTT7wAi1Fw==", "signatures": [{"sig": "MEQCIBcqBp8uLK4u+2sYiKqqijWQN/Y9nwNHhLrWd0IsurI7AiAO08IOj4DUGeZ3+TnCQH7MFI3C2VNvIMlzv8e4mBDxQw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "9a69c7c5a7dcb3bda8e399f55bc2bd723b435fab", "gitHead": "00c8b6a23524df455df275d5fa005249ae9f8425", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/cacheable-request.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {"keyv": "^2.0.1", "get-stream": "^3.0.0", "responselike": "^1.0.0", "normalize-url": "^1.9.1", "clone-response": "^1.0.1", "lowercase-keys": "^1.0.0", "http-cache-semantics": "^3.7.3"}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.21.0", "nyc": "^11.0.2", "pify": "^3.0.0", "this": "^1.0.2", "delay": "^2.0.0", "sqlite3": "^3.1.9", "coveralls": "^2.13.1", "get-stream": "^3.0.0", "@keyv/sqlite": "^1.2.6", "create-test-server": "^2.0.0", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request-1.0.2.tgz_1502640426366_0.9370210326742381", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "cacheable-request", "version": "1.1.0", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@1.1.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/cacheable-request", "bugs": {"url": "https://github.com/lukechilds/cacheable-request/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "fb516d9893add37cc25cdcd44f02cc84a33d4a59", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-1.1.0.tgz", "integrity": "sha512-eID+Ybkpdfal0sl/yy/xJ7jGFGvIf0/Um8t6c955/22NE5GEQwq9W/3QZUe6Av9QPFMpcmuKYIRHu6IvGJaoaA==", "signatures": [{"sig": "MEYCIQDD3HfKdaw7F01oLlwiGpdVBrsFHx56Ptk/a0aA26kLOwIhAJosUCpGZZjK+c0zl0lJ4tKWP3A1mv7tiJbzO2a3arsu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "fb516d9893add37cc25cdcd44f02cc84a33d4a59", "gitHead": "c7df98ffe39636b3729473dd720e715fce1c5192", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/cacheable-request.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {"keyv": "^2.0.1", "get-stream": "^3.0.0", "responselike": "^1.0.0", "normalize-url": "^1.9.1", "clone-response": "^1.0.1", "lowercase-keys": "^1.0.0", "http-cache-semantics": "^3.7.3"}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.21.0", "nyc": "^11.0.2", "pify": "^3.0.0", "this": "^1.0.2", "delay": "^2.0.0", "sqlite3": "^3.1.9", "coveralls": "^2.13.1", "get-stream": "^3.0.0", "@keyv/sqlite": "^1.2.6", "create-test-server": "^2.0.0", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request-1.1.0.tgz_1502643010619_0.6955999496858567", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "cacheable-request", "version": "1.1.1", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@1.1.1", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/cacheable-request", "bugs": {"url": "https://github.com/lukechilds/cacheable-request/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "8e63e273cc2dd46aaa46f080f430f1bb74cf19fd", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-1.1.1.tgz", "integrity": "sha512-dxoPiHzQ2i79QMW/LEdkMQqULXE0WDCHGaNvKVJgzEvht/b5YCCENvXPStLD8gklffBeJJpYcnnhviKIBP3P5w==", "signatures": [{"sig": "MEYCIQD4Zl8zMBlIXJH0FHP+LmY0TCbhNH/J4qsZZVxDB8XVXAIhALX4l7JZ7RkhOlHXeEkLF6AyVnL8QR0wvx8hHtPqr+tx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "8e63e273cc2dd46aaa46f080f430f1bb74cf19fd", "gitHead": "7d2eed54d36b37e25d5621e2f83262588eda14a8", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/cacheable-request.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {"keyv": "^2.0.1", "get-stream": "^3.0.0", "responselike": "^1.0.0", "normalize-url": "^1.9.1", "clone-response": "^1.0.1", "lowercase-keys": "^1.0.0", "http-cache-semantics": "^3.7.3"}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.21.0", "nyc": "^11.0.2", "pify": "^3.0.0", "this": "^1.0.2", "delay": "^2.0.0", "sqlite3": "^3.1.9", "coveralls": "^2.13.1", "get-stream": "^3.0.0", "@keyv/sqlite": "^1.2.6", "create-test-server": "^2.0.0", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request-1.1.1.tgz_1502647738160_0.3317284758668393", "host": "s3://npm-registry-packages"}}, "1.1.2": {"name": "cacheable-request", "version": "1.1.2", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@1.1.2", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/cacheable-request", "bugs": {"url": "https://github.com/lukechilds/cacheable-request/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "36ec8a0e0dc21cc44e0ccb6c669190f19e63fe3a", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-1.1.2.tgz", "integrity": "sha512-pOY+K6dpuCIQ3XvtUQqREwwn8Jdf0dL+bVqWHa2HopzSZX0X6SlJyZ/Lhtqt1PoaFRIODPy1jeABjdN9jPTqfg==", "signatures": [{"sig": "MEUCIB9n1XkAJBVe7M4TFfKUP6PxX8r0t1W14Jr3E43O9TS4AiEA9k4gXDlHlPb3E1iaEQmWFPwPxZpHtE8a/2NKDjusS+4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "36ec8a0e0dc21cc44e0ccb6c669190f19e63fe3a", "gitHead": "103d8f087ddfa382b3c81150a38afccc6ae0f6a1", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/cacheable-request.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"keyv": "^2.0.1", "get-stream": "^3.0.0", "responselike": "^1.0.0", "normalize-url": "^1.9.1", "clone-response": "^1.0.1", "lowercase-keys": "^1.0.0", "http-cache-semantics": "^3.7.3"}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.22.0", "nyc": "^11.0.2", "pify": "^3.0.0", "this": "^1.0.2", "delay": "^2.0.0", "sqlite3": "^3.1.9", "coveralls": "^2.13.1", "get-stream": "^3.0.0", "@keyv/sqlite": "^1.2.6", "create-test-server": "^2.0.0", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request-1.1.2.tgz_1502987901793_0.16526940814219415", "host": "s3://npm-registry-packages"}}, "1.1.3": {"name": "cacheable-request", "version": "1.1.3", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@1.1.3", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/cacheable-request", "bugs": {"url": "https://github.com/lukechilds/cacheable-request/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "0a45a87518445efc55be646081eca078cf481ab5", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-1.1.3.tgz", "integrity": "sha512-A3hMKQxvJBHkSZh/X8oxHYQgoW4QkmEVUz1dj8vAdbzeeeXqAx0C8RUCPJQ0iD+p6PVAkEHWATmm4bPNkEC/9A==", "signatures": [{"sig": "MEQCIEIgQZXBxOD00uLWSxBWNzdoyKiTGKahlzMbhrs8BX8LAiAnc9IarucDGkIjywjiHyeLa0aogH/DE6VrkiWjP7aV3A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "0a45a87518445efc55be646081eca078cf481ab5", "gitHead": "74b976f25ea73e636de92549930c4d12dee47601", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/cacheable-request.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"keyv": "^2.0.1", "get-stream": "^3.0.0", "responselike": "^1.0.0", "normalize-url": "^1.9.1", "clone-response": "^1.0.1", "lowercase-keys": "^1.0.0", "http-cache-semantics": "^3.7.3"}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.22.0", "nyc": "^11.0.2", "pify": "^3.0.0", "this": "^1.0.2", "delay": "^2.0.0", "sqlite3": "^3.1.9", "coveralls": "^2.13.1", "get-stream": "^3.0.0", "@keyv/sqlite": "^1.2.6", "create-test-server": "^2.0.0", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request-1.1.3.tgz_1502988122999_0.5417015382554382", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "cacheable-request", "version": "2.0.0", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@2.0.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/cacheable-request", "bugs": {"url": "https://github.com/lukechilds/cacheable-request/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "23d7001efe4de5b3add360d66220e054f00f40f8", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-2.0.0.tgz", "integrity": "sha512-08//JkAupjZ2U45jwt4ldXu0WSGU9+BKynNImDyaH6g7TkU3mMrbN4h+s5p5lwafLW0cVQ5+lz4nJwB9iMqEmg==", "signatures": [{"sig": "MEUCIQDo73CFFexZclv1NTY13NJWWIf+1OPDFlXJy0wgpqlU3wIge9C0Uo8KKhtDg0gV/QknZfnr4vw5sD+f3QgtaatXfWM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "gitHead": "4751ce0f5bf30bb7f0c0f14b88f3f7a108a744ff", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/cacheable-request.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "8.5.0", "dependencies": {"keyv": "3.0.0  ", "get-stream": "3.0.0", "responselike": "1.0.0", "normalize-url": "1.9.1", "clone-response": "1.0.1", "lowercase-keys": "1.0.0", "http-cache-semantics": "3.7.3"}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.22.0", "nyc": "^11.0.2", "pify": "^3.0.0", "this": "^1.0.2", "delay": "^2.0.0", "sqlite3": "^3.1.9", "coveralls": "^3.0.0", "get-stream": "^3.0.0", "@keyv/sqlite": "^1.2.6", "create-test-server": "^2.0.0", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request-2.0.0.tgz_1507039125295_0.9671146804466844", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "cacheable-request", "version": "2.0.1", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@2.0.1", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/cacheable-request", "bugs": {"url": "https://github.com/lukechilds/cacheable-request/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "b531cbce0c0bba4773837f69b31db09d3c6fc53a", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-2.0.1.tgz", "integrity": "sha512-hovRLYMty6MSJukmtIDFDTxuIZQHmniHvIfOG8xImXN+E/3ZTEEkToVXrQQWNQpJd2BmBQVwSbknDMCvuirTqQ==", "signatures": [{"sig": "MEQCIAHcffOcby+z1nC2+r2ELgAfEP1RQTHHZAOFVCSRQwnNAiADNzoRWqf5XBIR075j9sI2jP/5YndVCobxJRHuZErE7w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "gitHead": "b9e9a745f9c8a80bd13d6c23884e230ed2ec49e0", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/cacheable-request.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "8.6.0", "dependencies": {"keyv": "3.0.0  ", "get-stream": "3.0.0", "responselike": "1.0.0", "normalize-url": "1.9.1", "clone-response": "1.0.1", "lowercase-keys": "1.0.0", "http-cache-semantics": "3.8.0"}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.22.0", "nyc": "^11.0.2", "pify": "^3.0.0", "this": "^1.0.2", "delay": "^2.0.0", "sqlite3": "^3.1.9", "coveralls": "^3.0.0", "get-stream": "^3.0.0", "@keyv/sqlite": "^1.2.6", "create-test-server": "^2.0.0", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request-2.0.1.tgz_1507825564918_0.560035809641704", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "cacheable-request", "version": "2.1.0", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@2.1.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/cacheable-request", "bugs": {"url": "https://github.com/lukechilds/cacheable-request/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "3861c968678c0e86fd6450da07123f372cfac9d7", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-2.1.0.tgz", "integrity": "sha512-VZ1GM0UEHjrLEPAfsR+VRyYLzmLKJj4SH03EQlEqgUJSlJsmtbENTucNyTNWptZlca6Ykdt19v5iOluVuCwc1Q==", "signatures": [{"sig": "MEUCICCexqW9j8EOXSdGEhPad3Wutg+zzAtx9uGo/zoKZltDAiEA2mIuFg11oKSltIORAQPiBW1S+bU+dmX6uKlmKOQVRuA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "3861c968678c0e86fd6450da07123f372cfac9d7", "gitHead": "3d48da6b412879ee8fe956f3bbd55b224b526899", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/cacheable-request.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "8.6.0", "dependencies": {"keyv": "3.0.0  ", "get-stream": "3.0.0", "responselike": "1.0.0", "normalize-url": "1.9.1", "clone-response": "1.0.1", "lowercase-keys": "1.0.0", "http-cache-semantics": "3.8.0"}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.23.0", "nyc": "^11.0.2", "pify": "^3.0.0", "this": "^1.0.2", "delay": "^2.0.0", "sqlite3": "^3.1.9", "coveralls": "^3.0.0", "get-stream": "^3.0.0", "@keyv/sqlite": "^1.2.6", "create-test-server": "^2.0.0", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request-2.1.0.tgz_1510486924789_0.5684960193466395", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "cacheable-request", "version": "2.1.1", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@2.1.1", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/cacheable-request", "bugs": {"url": "https://github.com/lukechilds/cacheable-request/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "aa205ee287eae520950fec3dc71c5f41ee5550dd", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-2.1.1.tgz", "integrity": "sha512-EzPvhbyKHHq+T4ZyTJhju7p+CTE+sm4oSHTGdL2Q1GWXc7oghfk3WY/kqAg26Ubr9FwShaMFdstA7m/q73k9DA==", "signatures": [{"sig": "MEUCIALIqU25awxwac6FsbzheGkNdz2KtmI8rYGMCZC8XCTqAiEA4/c1FvGvWG+sSU2GvDiC8c1lzFlIbcRNhaYxmMIP2n4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "aa205ee287eae520950fec3dc71c5f41ee5550dd", "gitHead": "0a865d605bc2a25bf3e84a5bc69c695b90bcc630", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/cacheable-request.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "8.6.0", "dependencies": {"keyv": "3.0.0  ", "get-stream": "3.0.0", "responselike": "1.0.0", "normalize-url": "1.9.1", "clone-response": "1.0.1", "lowercase-keys": "1.0.0", "http-cache-semantics": "3.8.0"}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.23.0", "nyc": "^11.0.2", "pify": "^3.0.0", "this": "^1.0.2", "delay": "^2.0.0", "sqlite3": "^3.1.9", "coveralls": "^3.0.0", "get-stream": "^3.0.0", "@keyv/sqlite": "^1.2.6", "create-test-server": "^2.0.0", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request-2.1.1.tgz_1510824909378_0.31228654691949487", "host": "s3://npm-registry-packages"}}, "2.1.2": {"name": "cacheable-request", "version": "2.1.2", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@2.1.2", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/cacheable-request", "bugs": {"url": "https://github.com/lukechilds/cacheable-request/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "133288c62db51f45d63eb0c4bca121f03d251c71", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-2.1.2.tgz", "integrity": "sha512-IRF4Z2gdspQt+Bv2XBhTAfYEvU+ilt4G77MD3pEJ4ZqZqY+qIdgtMGgQJzP7KrHPGg3dh1g6qMOrUfW0gU1D9w==", "signatures": [{"sig": "MEQCIEUwc+YbbXRPUAy9wr4NZFs5R3Epo1o3e+evFUnyXlSAAiBsAP6iRAhpk29VxqwcURerevvPLaXAjn/w69u4vd2g2g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "133288c62db51f45d63eb0c4bca121f03d251c71", "gitHead": "6682d9695b03a8d268a63160ba5b974f8c5d1888", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/cacheable-request.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "9.2.0", "dependencies": {"keyv": "3.0.0  ", "get-stream": "3.0.0", "responselike": "1.0.0", "normalize-url": "2.0.0", "clone-response": "1.0.1", "lowercase-keys": "1.0.0", "http-cache-semantics": "3.8.1"}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.24.0", "nyc": "^11.0.2", "pify": "^3.0.0", "this": "^1.0.2", "delay": "^2.0.0", "sqlite3": "^3.1.9", "coveralls": "^3.0.0", "get-stream": "^3.0.0", "@keyv/sqlite": "^1.2.6", "create-test-server": "^2.0.0", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request-2.1.2.tgz_1512133736098_0.04721390572376549", "host": "s3://npm-registry-packages"}}, "2.1.3": {"name": "cacheable-request", "version": "2.1.3", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@2.1.3", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/cacheable-request", "bugs": {"url": "https://github.com/lukechilds/cacheable-request/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "b935607dd2ab2812898befb224f66aa86c533dbb", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-2.1.3.tgz", "integrity": "sha512-4uLxl5eJhr+tgsuRcvyGmS/ty/MJZ9A57ZTT8vp8nfuUzp8FSwjo1bu19nZfPVXPo0wyYbq+ChrwA/OGMt8sqg==", "signatures": [{"sig": "MEUCIQCvHszvhqmmutaHXOVLmN3O3x05KutfZjNqFXABMCZnsAIgWD/VpM80YF/6ERcQB47hrtRpfP3plY5YGPFlp52GSK0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "b935607dd2ab2812898befb224f66aa86c533dbb", "gitHead": "4b3e33b8f1d79bd9be8cc9af8105e8f4b5729736", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/cacheable-request.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "9.2.0", "dependencies": {"keyv": "3.0.0", "get-stream": "3.0.0", "responselike": "1.0.2", "normalize-url": "2.0.0", "clone-response": "1.0.2", "lowercase-keys": "1.0.0", "http-cache-semantics": "3.8.1"}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.24.0", "nyc": "^11.0.2", "pify": "^3.0.0", "this": "^1.0.2", "delay": "^2.0.0", "sqlite3": "^3.1.9", "coveralls": "^3.0.0", "@keyv/sqlite": "^1.2.6", "create-test-server": "^2.0.0", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request-2.1.3.tgz_1512215347112_0.4187504805158824", "host": "s3://npm-registry-packages"}}, "2.1.4": {"name": "cacheable-request", "version": "2.1.4", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@2.1.4", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/cacheable-request", "bugs": {"url": "https://github.com/lukechilds/cacheable-request/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "0d808801b6342ad33c91df9d0b44dc09b91e5c3d", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-2.1.4.tgz", "integrity": "sha512-vag0O2LKZ/najSoUwDbVlnlCFvhBE/7mGTY2B5FgCBDcRD+oVV1HYTOwM6JZfMg/hIcM6IwnTZ1uQQL5/X3xIQ==", "signatures": [{"sig": "MEQCIA+0J1DIPw6N7FBVhb2zTPSo+Iaj19zydXZRwk0xTI9uAiBjONENczHyExI/AkG2aX/RjZ79xfTves/avrmttcMh9A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "0d808801b6342ad33c91df9d0b44dc09b91e5c3d", "gitHead": "3f2d8e2f64af835a82721c51898bd6f08883c4de", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/cacheable-request.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "5.12.0", "dependencies": {"keyv": "3.0.0", "get-stream": "3.0.0", "responselike": "1.0.2", "normalize-url": "2.0.1", "clone-response": "1.0.2", "lowercase-keys": "1.0.0", "http-cache-semantics": "3.8.1"}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.24.0", "nyc": "^11.0.2", "pify": "^3.0.0", "this": "^1.0.2", "delay": "^2.0.0", "sqlite3": "^3.1.9", "coveralls": "^3.0.0", "@keyv/sqlite": "^1.2.6", "create-test-server": "^2.0.0", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request-2.1.4.tgz_1514318995331_0.5610265193972737", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "cacheable-request", "version": "3.0.0", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@3.0.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/cacheable-request#readme", "bugs": {"url": "https://github.com/lukechilds/cacheable-request/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "c83b8a76126da0c7472cc14d0be47fc0db34faa1", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-3.0.0.tgz", "fileCount": 4, "integrity": "sha512-aOitOP6oU2AdPVfOKWfRFyzdxlO1Ot+kL/NXtWZ3fPiw5XeBUPhp+OCIiF/eN8ZWh+o0U50N0/gdWZw2as914Q==", "signatures": [{"sig": "MEYCIQCuE/daUhRJeGQN6xnV3FpCOhXqbYoVVSvevQjwnQomkgIhANidz0hFAhM9paxLWA9xcPvwr+jMztaApkITMiLjv2r9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14069, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbPstuCRA9TVsSAnZWagAAUTAP/jQYZbSCUkJWtm5J/eSR\nLozf9JDTjACgdcJzFGgT/F0PEQ43AsEsI4gpUjv3bQFUwXPPMfZqzMTHElBF\nSvJa8y25R+nX5HIVZAlfyvgldlUB/BQqBqFo5O5R9FtwPZq6E7Hvi/TjIvAH\nc/u8I0wd/JoKt8GjjC6//aIHA8d/VL+KUiz6JlVRQtZ43LYqBmRt3OgdlvjR\na1q/jNQ9iA8QzJmN3KU032JQJ1U5VOAMi2MGyHWLyLHvFmmqyxjoCBqlwYUx\n1WCAGFWr43oNuRdm2wbjZ80q9oHiQLBUkMOYduqoSjcRUI7JoTn2+m8WAs7G\ner4nkSMXtFLlgv6qFY6cuo3n1UXXpoKGarnXY8EueAXP2PnC/ovpcvHCWzWF\nfXWaToPqx5IGaGZ4sA+1dYugJAurI+thIFtSfdJdhGD2dPEDeCGdWPMZqwjh\n2xQCpQFIFB3bI9sd0LXuGeyjEc+S2Hh79xChH93mElFwQIsHT/UtGg+QVg0Q\nTxS6AX3CSCoZ/awwF5cAl/sVLpWiN+5drpO4UXxS+2SiXJwITbPWAiu5Of+u\ndlX23pzEm08c3/c8hkH61WnJ2UirlnXAiLjN7HdLZn/McjYEYywbshBJdTUq\ndEWcl515J/yvj7VPCmlgLptiYF2zOs4vC+evdipXFKLpmadCV2ezF7RBptqZ\nyejE\r\n=N6wb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "files": ["src"], "engines": {"node": ">=8"}, "gitHead": "4b7f86a3ce4b88ca0fd9df312a95876a5ebfade6", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/cacheable-request.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"keyv": "^3.0.0", "get-stream": "^3.0.0", "responselike": "^1.0.2", "normalize-url": "^3.1.0", "clone-response": "^1.0.2", "lowercase-keys": "^1.0.1", "http-cache-semantics": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^12.0.2", "pify": "^3.0.0", "this": "^1.0.2", "delay": "^3.0.0", "sqlite3": "^4.0.1", "coveralls": "^3.0.0", "@keyv/sqlite": "^1.2.6", "create-test-server": "2.1.1", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_3.0.0_1530841966610_0.006173654088653002", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "cacheable-request", "version": "4.0.0", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@4.0.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/cacheable-request#readme", "bugs": {"url": "https://github.com/lukechilds/cacheable-request/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "3c90168895f7fb5c4645b97ec60d818bc1764ffe", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-4.0.0.tgz", "fileCount": 4, "integrity": "sha512-aMoyzOd+NA3kMwTr4DtNwDfz6mOldFci7IvBu1NBu+0+4TRnaPM3p8jcT9ocRiGG25Vmb2sO+3GS9/ve3z7sXQ==", "signatures": [{"sig": "MEUCIQDeFzF96d5eFOdDjUtpAP59X269Xd//M8vRWGh1MxUfxwIgau6wftXjL99fTderniL/KAOdRaf2MfveHAIDxPBF0kI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15234, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUbtNCRA9TVsSAnZWagAA7loP/jhHB+/rR5YXhQsGqrSJ\np4tI0m1DPrNDaQ0rODTSAnvbNTC3EbhZhmg9OsXsEIYIbG5qHwu664EucHgc\nq/QUWXLB4gES8rK4yZBW1tHH3IKXCEujCV2KzXgqUqF1QCfPwBjEj3W4mP7a\n/r6rEQiiJY38yzQW20ywiZlGH0husQ1QU/ODIDrlTKOyxj5hY+ptjsf+/mdo\nAeVkqVZ1wUC0jEWVtsBIvRfIwZk/jWAT6SvCD5xqx2Ty7D8mHD2u3QoaRV0B\nWOPfPT8hK6dNS0Ts+rEktkcxzncbJAudPsZCHjBeEyvgVzrZPRgd8fcAAKl6\nvJZ0xTVEtTOupj+xuR/kusF1JiyCzqDODjrzV5R6C186NlJuJ8Rm8Uh9IzNF\nUw78rawIetEMdx+eL+JVOcAmeIXSamCb5ib6BhTRUSzzZLyVjfMCsL+uIyEj\nMabD+LxpvIKCma6hCbc7vq6A4g85cVSoYl3SJxZTHmTZIyqL7mV4yyCNHMvj\nzWnEpVpmGrfUUsG0DVxK6Z9A3vWFCZkc/y/WZ0jiXajUpmo5bLjDolp1uBno\nSvTiQ9l9ExX+ND5zh9VH8pkDmrhIDKlruwG1RJ+thCDUV9/zDvGT80QO2n80\nk0Fh6JXio29ZGr6qyKcOpF40WyF6GGFqUsjWm8OqzvxJcHK0QoL6x8HWXQun\nJw8+\r\n=w+7q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "files": ["src"], "engines": {"node": ">=8"}, "gitHead": "5e16303663935d8797ba3070f1d3a0d8d2cd36c0", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/cacheable-request.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"keyv": "^3.0.0", "get-stream": "^3.0.0", "responselike": "^1.0.2", "normalize-url": "^3.1.0", "clone-response": "^1.0.2", "lowercase-keys": "^1.0.1", "http-cache-semantics": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^12.0.2", "pify": "^3.0.0", "this": "^1.0.2", "delay": "^3.0.0", "sqlite3": "^4.0.1", "coveralls": "^3.0.0", "@keyv/sqlite": "^1.2.6", "create-test-server": "2.1.1", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_4.0.0_1532083021230_0.7315888443717646", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "cacheable-request", "version": "4.0.1", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@4.0.1", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/cacheable-request#readme", "bugs": {"url": "https://github.com/lukechilds/cacheable-request/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "c93bbb87d7c67c62738f2e336e4ff8e78d9533fa", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-4.0.1.tgz", "fileCount": 4, "integrity": "sha512-sJJ9ivV+1DytIdAgQG1CsRjAESGD5WnjTr4BsQ3X4xZKp3O9i6Es7MWeQ1pOTuE0bTLVEf86YFjSGpr53wz7nw==", "signatures": [{"sig": "MEYCIQDwwBNe+9och80YsHFBPYFX6D8OBA9Zbjb6oAsNTbP3RAIhAOEuJw8zN/1/TZnsdSZBXVcMQO92E8ssxPoGVbUGgpWu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15309, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbVKZqCRA9TVsSAnZWagAAMnQQAKCcrCCbyGdn9pjZnql4\n3phQSIESybk1d7ULr7eL/hEVITOWkmKaQZBI7jE6yGWqCwPnD9puhjJDA5uw\n0OdhAZaFJ2ST10AuKcsSOWvk02h4cyzPyjThgzz1X5Gbw91nJbK2uSFeu0xx\nsXWM0eYDyvATgMltZ6AfVbPi/3nnBxs529vEojcDa0ulrBQo+qHqsaq1knj7\nVUAxSVOjv3qlVOiyEg69kOBZtmXsgXPdjBhZnuIXOQh5F48oSBzA82Cn7kLN\n/X14PGGEAX+s+QtTqqUy8yTfsHvRNOOBqIZmiZI/dotaavS+uVOS014m7Rqj\ngvSbZ5yNB+WT8eWuwoboFcVw7tiX2SkShhfB+wYwyoryCU80IllaRFshwm71\na3s00KaUq+FXH/3KB5vrp3hNv1rw94QivR9SF4IcaOhNWRBf4WkZ02Vi+yzP\n7XordJ7/l8mVzewwT5fm0C08D6FnQ7QQQnuZLH0lxEOdhPsmgERGbO1I3Lny\nAWVXpF3YBz+0eUtuZWgWC8wGIiH/XM+Vkxwxucwd7rmqOAO5YJiXatae1rRs\nIzVhGo/C26Kbn5K4qC2Jw8dY6buTR3v60frRj06LJVjvuUc2HGQAfpwMgSRZ\n3zx3sLrrcRkjPNnBxtEr1MJTnIeXsxeBlgJOYp5aRv3r83guOmE/JcA7UPVz\nVf57\r\n=TCyR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "files": ["src"], "engines": {"node": ">=8"}, "gitHead": "25253bc039b293375d38324dff92b5020a613d15", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/cacheable-request.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"keyv": "^3.0.0", "get-stream": "^3.0.0", "responselike": "^1.0.2", "normalize-url": "^3.1.0", "clone-response": "^1.0.2", "lowercase-keys": "^1.0.1", "http-cache-semantics": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^12.0.2", "pify": "^3.0.0", "this": "^1.0.2", "delay": "^3.0.0", "sqlite3": "^4.0.1", "coveralls": "^3.0.0", "@keyv/sqlite": "^1.2.6", "create-test-server": "2.1.1", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_4.0.1_1532274282703_0.20938672501885258", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "cacheable-request", "version": "5.0.0", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@5.0.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/cacheable-request#readme", "bugs": {"url": "https://github.com/lukechilds/cacheable-request/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "7ce347741c48d85c76bc41b78f6bf13e2907056d", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-5.0.0.tgz", "fileCount": 4, "integrity": "sha512-gU53XCfodl+GZ37ecX/uLobGE+WvgE2QE3VfhX7sPf04Kc35tNPip3IfCKfqJ2e04HLuHRxFCWgGWttv8OQbfw==", "signatures": [{"sig": "MEUCIQDtMnDCiJDcnTTZw9QGadGWZwlx0uqL0ZVAIj3DsD7rYAIgPBqs3xKBmf3xnymOSLWS/hVTKZbpo2H1OKrl4Fnqhqk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15617, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbj+33CRA9TVsSAnZWagAAEdwP/iGfckwHu/2jyemyi30J\nKCM1IH3Eca9OCYrPv6bwp8eoX3QcBVeFnp2p/Cm7lja6zxaDL0axmUzHBS4I\nfWBk/K98lp+bCl4VCnoua0SemZtuHH5TZLQvrUfVqu9xNNE/psJnl/MC/IUz\nkrhq07OE5rRsyHZl5Rm+Dx/Zw69s1U8P2fwp2dyakpJ6CZQKuI4MBZwwHUek\n688m6Ydj8t0Aj5H3f7D52zVECO201esuGpg09lpODpgGCL8amDdg2So/immh\nozVMc+1hYOx2PoDVkK4kX9ClTNCh+gaSl4HOl19yY5yWoZLcD+/r/QJzPd6X\n5QPSV5YgMpZ4afGlxEIDfEqXEAuJmuexQRN/ViaXyVlh5a70jPOv9DUGjnr4\nAlIloEqwr3sflmnJ++fZA20iWNEq1G7Op2GdS4Fgq2N9sEmGb0drjwTE8Lsi\nM/pGig//qaSNvzgRVvHJgWZlJ/c5RPlngwof18MribuLHf/UdyoElxaw2Gfb\nI2VpJNXgG/OivfMhIoEJ4TXYK1bUdZBL28zb6n6o2RXwDRlg7+kb33SOYcvT\nYUOJvWdh3Gv2cDuTkbvZJ1MxIDz9vay05fEDTKBNgoaVkqXa3PbNcVV6UI2/\n6iws1ROce7gtc2RpbQ6GBGoPUfD/xfGXUgTMa7px3Dr/6SCKmTO6bBuAszRO\nmEye\r\n=e/R6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "engines": {"node": ">=8"}, "gitHead": "c23ffedd5914b4ff929ea2b55e49d4b29c4d18bc", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/cacheable-request.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "10.9.0", "dependencies": {"keyv": "^3.0.0", "get-stream": "^4.0.0", "responselike": "^1.0.2", "normalize-url": "^3.1.0", "clone-response": "^1.0.2", "lowercase-keys": "^1.0.1", "http-cache-semantics": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.22.0", "ava": "^0.25.0", "nyc": "^13.0.1", "pify": "^4.0.0", "this": "^1.0.2", "delay": "^4.0.0", "sqlite3": "^4.0.2", "coveralls": "^3.0.0", "@keyv/sqlite": "^2.0.0", "create-test-server": "2.1.1", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_5.0.0_1536159222562_0.515616338733607", "host": "s3://npm-registry-packages"}}, "5.1.0": {"name": "cacheable-request", "version": "5.1.0", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@5.1.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/cacheable-request#readme", "bugs": {"url": "https://github.com/lukechilds/cacheable-request/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "ce0958e977bdb4a5b718464049793b8d4bf7d75d", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-5.1.0.tgz", "fileCount": 4, "integrity": "sha512-UCdjX4N/QjymZGpKY7hW4VJsxsVJM+drIiCxPa9aTvFQN5sL2+kJCYyeys8f2W0dJ0sU6Et54Ovl0sAmCpHHsA==", "signatures": [{"sig": "MEUCID4dlaN/rLlnZtCd3JKVmZzg9weUZT6xs80gIs8wsS1CAiEA6RBTMuIoyf9E6tkh0pEs72BzqHH3+K7mEJSzGyNiPBI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15839, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbx0oQCRA9TVsSAnZWagAAiwkP+weZQyiBHxPprdx9J1kr\n8jHpJaujMWtYMfPVz6edikNxDqJnGQRZYDq8gLrQtNe0wwAJYbcSt4KhQcWI\nf3ATnFFTyKHP5VGQ0eJ7NVF5kj/9am/sVzPc27CbZ15j+SS0DJiJvq6cKSji\np1MiantJQtDrDAiaGnEVJsSLNryalRUuvfaju2wqH52ChVqD33VtnjAzg2O8\niYUZvw1R+jIXmy00SGcM2a0a/uMWtjnpcs/zKeYKilKrUZV8pD0uqkl9NT4j\nOgBf3IAaRrfJu5a9hGXzmD6e849h+ftaonkNpwmjnb/aj2E8iELHiPucSpdX\nsGY1CMMv78EUk139BlycXNopWj4dIRaJdaRk1UtgG8aXP2BkB218hF2VRj6w\ncn7TMZY3oChRgai68bYRW48IrKUl8Btv7Gi101zZKG9mjbwgitZUvoxZzp3n\nyo2RzNwpD7911/63cSOrq8lobluAhwwYvvnR0nhZ7kE2sOFVUE1tOZJfehUz\nlKa9TCyoA84RPrRWISVkKiMK0IGUo+tUCIQTOevZGjqUE/14hpqEJck5t66v\nNfW6J5s4KERRju9ACXuYjo9B9z/8csRgyuPYbWXgIBJFUPsqpLveMoUNAR9m\nX+oZCQs2gT0h+zVqyj1j0+qc6DH7XwVBcQ35eduNMrdQJ2/CGtj+JNRUUnuj\n0RU2\r\n=7OX3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "engines": {"node": ">=8"}, "gitHead": "a1d01f4cf8d4233e70deff036abd388c5649a486", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/cacheable-request.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "8.12.0", "dependencies": {"keyv": "^3.0.0", "get-stream": "^4.0.0", "responselike": "^1.0.2", "normalize-url": "^3.1.0", "clone-response": "^1.0.2", "lowercase-keys": "^1.0.1", "http-cache-semantics": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.22.0", "ava": "^0.25.0", "nyc": "^13.0.1", "pify": "^4.0.0", "this": "^1.0.2", "delay": "^4.0.0", "sqlite3": "^4.0.2", "coveralls": "^3.0.0", "@keyv/sqlite": "^2.0.0", "create-test-server": "2.1.1", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_5.1.0_1539787279399_0.8749830873969406", "host": "s3://npm-registry-packages"}}, "5.2.0": {"name": "cacheable-request", "version": "5.2.0", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@5.2.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/cacheable-request#readme", "bugs": {"url": "https://github.com/lukechilds/cacheable-request/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "00c87097835af4caf92a97390660ecadce51187d", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-5.2.0.tgz", "fileCount": 4, "integrity": "sha512-h1n0vjpFaByTvU6PiyTKk2kx4OnuV1aVUynCUd/FiKl4icpPSceowk3rHczwFEBuZvz+E1EU4KExR0MCPeQfaQ==", "signatures": [{"sig": "MEYCIQDUNjdBqNppX/hg4CERneGWx/8KViPVZyfQk8YRdRff3QIhANXXmUFA+hbBEPaHAe7X5pHjo4Nn9kcWthun6qJj2OBl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15977, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb6opfCRA9TVsSAnZWagAAVX0P/0AadRqIKOf7Vy7B8sz+\nT3u0w11AE5SXnnMhy1bygZ/W/5ea/jYFrlBQ8t9u+a4It+OQQJSZz9+QF1Hi\nAzzFV4B2Dps6Ev+eYhV4AX/MQlcMXx195DUjLYl7C0dmj1i2tbTdTiIYBbho\n4kkt1Oyq+BzrnUAmjVi07CW9Eij7x9zE3JhGUEJtqwLcO+s9y/O4nvLjL5lY\nfLCl1Qa8wXD1mxSDYKE5sFHfZjlg9b4j2Bt4i6hoYkMJKB0iOKMoy8EIoZED\nluQtsz4YJ7X2w58/aVYnAlcLidb/ml2ECnPdFEfHCpBlofhZfl0g8LlH+JW5\noxaUCY7iWeXAsBYp82MJDL9mqBO9zaoKW10chqesCC8cUCn3BlioSHI7DE9Z\n8lb8CTGiEMD1Hi37NsnNfXstZ/FoPKpCY7iysvQUdhP774E1KYmS9gmYGlfW\nBgvk/D3zKyhKHCX5P7Rj0VuVjt15azgxpnlz4zLJ/lS3LlChsUgK6teTdxVN\n4NLBgwCBlZaWwiEK57STwOKVjVRqKDKjQExApMqpFI0BtM20Bv0J6flVsqzM\nBzAsTtbFFTyh+zI25gwjiIogVIfDe2dpsjVtYgQj54dT00Os2K5LIE0tVZRa\n5CSKZP0bwrBPd/5ILMKhr2dWdoTbHpCHyKolWmuJyaKKNIDLozxRGmUNT2cy\nihyy\r\n=zjoA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "engines": {"node": ">=8"}, "gitHead": "919af89701308c3e23daaab7147669c15b0948da", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/cacheable-request.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "8.12.0", "dependencies": {"keyv": "^3.0.0", "get-stream": "^4.0.0", "responselike": "^1.0.2", "normalize-url": "^3.1.0", "clone-response": "^1.0.2", "lowercase-keys": "^1.0.1", "http-cache-semantics": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.22.0", "ava": "^0.25.0", "nyc": "^13.0.1", "pify": "^4.0.0", "this": "^1.0.2", "delay": "^4.0.0", "sqlite3": "^4.0.2", "coveralls": "^3.0.0", "@keyv/sqlite": "^2.0.0", "create-test-server": "2.1.1", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_5.2.0_1542097502462_0.37772493732477685", "host": "s3://npm-registry-packages"}}, "5.2.1": {"name": "cacheable-request", "version": "5.2.1", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@5.2.1", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/cacheable-request#readme", "bugs": {"url": "https://github.com/lukechilds/cacheable-request/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "41814b0460b68b9baf74f57f5a6046224d55d71e", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-5.2.1.tgz", "fileCount": 4, "integrity": "sha512-+dLut9zvvuIM/MrtdHBVSh/QYJ9+uCKYoqww9cOYrndQH4O4rD/qH0IAwFhD5WJKfmWE6WgCOrLQPd/H5YJRVQ==", "signatures": [{"sig": "MEYCIQDSU3U0UeivjyPW4fLqlbGsxLk+XeG0zBOAkWKjjiB9rAIhALYsaryrlzGT1Cxdmj64kVzqbgJpAWgtWkY/CW2Na8UP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16523, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcHctUCRA9TVsSAnZWagAA4Y8QAIk5MqiKOU/oaQw4qazY\nV5BqSrNF1fIKJSeNBExwelQoV2ZQ7vuP/Wz3bOKL+5DSA8sVn+bB8Eiu6Rue\nPEqVtS3trXvF+oEkQTBGjfAa7h4Ckfgx9b8x3yp1QITzF9yhNIxI/i9odYls\nlJZp9CodXQxXwtDzYO3fAvRXizvwti6y3D7RGdwS6sHEbzzeF1ykAF3hs2B8\ndx79KoPhsZGiDxAIoh75zupfSD/Dn88Nt+J75v9dKTtY1jxaTTm5qobNikFZ\nHNqjnWGYrxtqv7gZ4IbMeGclAhLYtDwSe/+nsnSI4V0xyXVzM7/WZ3UewB0K\nzvz/I/q+FSSBy92ctsoLlMqMd10tbF1AmEue62rL3rTlKwq5uFxh5RUhEeQ5\n0bG6hqd88HnA5zQ+FfTxLN5pgsYoQom4CnnPYvbOtI8lHctCtTjsH3wf+AMv\n7q22ql6P7xs4a8iMHYD86P39XDWarz/2efJaQacWYZHyqmKNF2w+Aeua4OJ4\nv3i4CUueTKKIEGmFkBg5LFJucDOkfHt97ukXqoNN7DUrGYZL256dzD17jVEK\nZt2+9K47P9r4f8mnvmB5UU7fBau1Z5KqKiW7tmnTxesFMxL9iTKMmi3zSqFQ\nEWOHbQ4rvOdisqcg9BS3l0wlTq6tG5aEh23N39mXYd5VHdlgZXoqC0VVM2Y4\nKwmA\r\n=6bIN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "engines": {"node": ">=8"}, "gitHead": "06bd6b3bf83b82de767b6f15ac154d05fb5e2356", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/cacheable-request.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "8.12.0", "dependencies": {"keyv": "^3.0.0", "get-stream": "^4.0.0", "responselike": "^1.0.2", "normalize-url": "^3.1.0", "clone-response": "^1.0.2", "lowercase-keys": "^1.0.1", "http-cache-semantics": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.22.0", "ava": "^0.25.0", "nyc": "^13.0.1", "pify": "^4.0.0", "this": "^1.0.2", "delay": "^4.0.0", "sqlite3": "^4.0.2", "coveralls": "^3.0.0", "@keyv/sqlite": "^2.0.0", "create-test-server": "2.1.1", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_5.2.1_1545456467765_0.8719920249351871", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "cacheable-request", "version": "6.0.0", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@6.0.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/cacheable-request#readme", "bugs": {"url": "https://github.com/lukechilds/cacheable-request/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "4a1727414e02ac4af82560c4da1b61daa3fa2b63", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-6.0.0.tgz", "fileCount": 4, "integrity": "sha512-2N7AmszH/WPPpl5Z3XMw1HAP+8d+xugnKQAeKvxFZ/04dbT/CAznqwbl+7eSr3HkwdepNwtb2yx3CAMQWvG01Q==", "signatures": [{"sig": "MEQCIH7seruf3iezkG4H6hF0RPxJaOk3xdOMhQRsTNjidsOAAiB/LvXDujhfxHOtQFPep2bJFgwRm4XElC1F6pV5Mk7zAA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16525, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcLw1HCRA9TVsSAnZWagAA/eYP/iZd3/dcO8tPXlALoQ0M\nGdNAZBpRHZQIzgRmY+q4Ehv969PeeZCyzqc3MlTOhobXvPcQP45AnzpskYG4\n1gVxSJewP1rGFFd0XJcd9IlrREkt2uxqT97INl/sNJLPDy0x84cemPfcLDsl\naYhTFDXFRkKQ4LC7OfqNBlQUhRqTrG71DbGC8wu7DG2RcqMWOPciYf4yxIU0\nEqEx2ZtOcGhPNRPsp+1OsyjndunsGfUi7xnuTiXuVjEc2o1/jVgFYVHzpy92\nVyPXjHHngW3GqpKz9Wtj4qKVB2MLk43qzgOeYW+lBSUoBd1C5p56hvuCmMK+\nP2Pohwuqz7ouFW5AV73eVXLt9XD65Yqheeydh52skB811Gz+9LViQwdn+nwL\nj1XF8D6RcgGFLh4B5H5DNAro5R1fOjp8OOidnaVHv7zxhhBQw3U6sA4DlwTE\nJiRqhT7M77MoYit6g1CpVWSnwEBb1mQ/oWGY3JLwfoO8LqBTamnEg4UIP5Qm\nFbhhoJ4TFyguyny2BfpK+eNtGwmDZFbi35wU8P+5VKyIT/XudQyUD+N3ZR6i\ni9wi/VfwXHhC7XA2eH/LuQp56gyRyOYxbLE4c4iJ2reXt0bgshZDhzRpzkR3\nkhZjpDgBKV7aUPT72IGceusJmzSI9NI9uG6QK5JEMwZh/AKuqk074TM7lmp6\nueKC\r\n=9djW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "engines": {"node": ">=8"}, "gitHead": "ea92da1fb1e44e795fbe51d39f86630c2015e5fe", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/cacheable-request.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "8.12.0", "dependencies": {"keyv": "^3.0.0", "get-stream": "^4.0.0", "responselike": "^1.0.2", "normalize-url": "^3.1.0", "clone-response": "^1.0.2", "lowercase-keys": "^1.0.1", "http-cache-semantics": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.22.0", "ava": "^0.25.0", "nyc": "^13.0.1", "pify": "^4.0.0", "this": "^1.0.2", "delay": "^4.0.0", "sqlite3": "^4.0.2", "coveralls": "^3.0.0", "@keyv/sqlite": "^2.0.0", "create-test-server": "2.1.1", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_6.0.0_1546587463235_0.8245971191548354", "host": "s3://npm-registry-packages"}}, "6.1.0": {"name": "cacheable-request", "version": "6.1.0", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@6.1.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/cacheable-request#readme", "bugs": {"url": "https://github.com/lukechilds/cacheable-request/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "20ffb8bd162ba4be11e9567d823db651052ca912", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-6.1.0.tgz", "fileCount": 4, "integrity": "sha512-<PERSON>j3cAGPCqOZX7Rz64Uny2GYAZNliQSqfbePrgAQ1wKAihYmCUnraBtJtKcGR4xz7wF+LoJC+ssFZvv5BgF9Igg==", "signatures": [{"sig": "MEYCIQCT2bB90uN6Gu6pFIlJHps9NRzAAJBLySzmyD8veQnbDQIhAMrXSCkSv0Tl+wA8ToCwwujitHKIFgSjQrBLws1I82gT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16750, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc+gELCRA9TVsSAnZWagAAg4UP/icEa1kgzzzsS3WW4kLV\nxLC66G4F22SX1oGRPoVGDKT7/rn/cGBiiLyGvlQPMoTd/chYCAKIHSIiSoWU\nPkuPcZuFA2A+stiQ9TPPnZYyUxkFqB+9UwZBqqHXFZ+zoS5pdoUA2U6zhM5Z\nagiv8tV5TmzbyCq7AaWIl1xGA3MnwgWp7Yt2xpUW2KRYLfQDiCMB13O/5W9b\nfjz0qBqY0ebxtEiN4gS29QPNRo9SNtrGVB5fdQtiWd43abIvy3g+YytlVTRh\nfYAazeo0itA4txKIRYxz0eomNyaOJlER5qdNS667zuDyCDWl5+RQMf15AMeE\nvSHp7nMjzq0e6bjAo7M2aD/f2Be40s5Mo+JffZ0ZxPUa2A6oa9NypKnjtwOV\nVRqVadXuz2z2XVD+5kprL1ibZW8dtEL6pr8zaZ1bLqxnUvzVypi6BWdVKolp\nJM093Y2y6iUo3RpawHWVIlXGgOzXzZFwmSDjgG05h6MH4CeuGhMSdMRkQUjd\n2Egc+xaN+bzlNVBzcyIx8twvAETU7UjIF+tUdusVU0AGbSfiLIB6riZlr2QB\nZaUQmLGSxMWHcHDOFiJXIWv6FRFhlNpQa3Ft1mSv0TQxa6ZNE3VKEnV2O0Ca\nODS+lRX5xlQf+frQ14pmSW/gbj5qtYbn0SWN1oEgnpsXja0Fv0MZ1fBbLUpv\n1A3/\r\n=zWoo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "engines": {"node": ">=8"}, "gitHead": "d50e6af350b6f52814c71ce3908e704a3d5f428e", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/cacheable-request.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "12.0.0", "dependencies": {"keyv": "^3.0.0", "get-stream": "^5.1.0", "responselike": "^1.0.2", "normalize-url": "^4.1.0", "clone-response": "^1.0.2", "lowercase-keys": "^2.0.0", "http-cache-semantics": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.23.0", "ava": "^1.1.0", "nyc": "^14.1.1", "pify": "^4.0.0", "this": "^1.0.2", "delay": "^4.0.0", "sqlite3": "^4.0.2", "coveralls": "^3.0.0", "@keyv/sqlite": "^2.0.0", "create-test-server": "3.0.0", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_6.1.0_1559888139040_0.21564101704262906", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "cacheable-request", "version": "7.0.0", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@7.0.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/cacheable-request#readme", "bugs": {"url": "https://github.com/lukechilds/cacheable-request/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "12421aa084e943ec81eac8c93e56af90c624788a", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-7.0.0.tgz", "fileCount": 4, "integrity": "sha512-UVG4gMn3WjnAeFBBx7RFoprgOANIAkMwN5Dta6ONmfSwrCxfm0Ip7g0mIBxIRJZX9aDsoID0Ry3dU5Pr0csKKA==", "signatures": [{"sig": "MEYCIQCDc0uMStO2wq96qJhW1mETIMwzHQZIi6ouQXhnB20MfwIhAP8x9Te+WWGdB1xjJaAblX+TKciD3BJVBsVLrw4mve2L", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16750, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdnnoSCRA9TVsSAnZWagAA62YP/1GHLWNOoqYw68C4i+mK\nfXummdUJINDVQJoChC+LjDGhg2cViZnlneqkQiiw7+gye4dQzZSdeEQiM40a\nvSh9/zTH6vUXZFck4mcBTCGUT2VBA0zaCBL7bNg5ocssHGL0ZymVyRbLaoOq\nTwaK1CYvHzZw78hPXlTH6CnziJY1o4JYptXUoF67FB1pdkPkRQFk4ShWe/Ma\nJ0EZ7DsdBITAVp1E2d/oEJy3Jg8Q/i4ROnhQsZuRd0vgoc73FTyD/ifK+ved\nPXovFUk5squxwFVlHrn3UGER9q8v0tqtO6YYTaEgDqT99rDxc6ovhWQ/dPVn\n+OeeW9vlw+xhxKgcLBAtxY/YnjOTXX9fjxHOnYgLhMd4bXleD2Api122xcdM\niv1VL7ZoYuF98Zhq8Ng/ofrQVy9rkjKVluYoPEk3QjUyA+M9E7uO9Ap+0CVb\nkL2FkRNqfziaXaeNk39JemwZmZ5qDyXDnko1XvDl3XLj1hAF38ksCC4qVQqm\nLOrGEflzw4SkKUx4WuPnkib/boHugnGXx7ErRuYRf9YCeoHuPdPkp76lbQuc\nmtnFR8cLrKIVEZuWscd1GqrVgYmWVfaOlR9PEKpEYYE4HYGV27k2WErTdJzx\nu58QzRFydt6Qd4q5le//9SEYpJHh5Bo8FjRNZ2eeFTObO/Lo0d/CtiVtNxuJ\n0CTJ\r\n=PuEi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "engines": {"node": ">=8"}, "gitHead": "130fcbe79b9b7427b1d8016756be85aa3d52a303", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/cacheable-request.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "12.11.1", "dependencies": {"keyv": "^3.0.0", "get-stream": "^5.1.0", "responselike": "^2.0.0", "normalize-url": "^4.1.0", "clone-response": "^1.0.2", "lowercase-keys": "^2.0.0", "http-cache-semantics": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.23.0", "ava": "^1.1.0", "nyc": "^14.1.1", "pify": "^4.0.0", "this": "^1.0.2", "delay": "^4.0.0", "sqlite3": "^4.0.2", "coveralls": "^3.0.0", "@keyv/sqlite": "^2.0.0", "create-test-server": "3.0.0", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_7.0.0_1570667026222_0.23845783578515323", "host": "s3://npm-registry-packages"}}, "7.0.1": {"name": "cacheable-request", "version": "7.0.1", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@7.0.1", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/cacheable-request#readme", "bugs": {"url": "https://github.com/lukechilds/cacheable-request/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "062031c2856232782ed694a257fa35da93942a58", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-7.0.1.tgz", "fileCount": 4, "integrity": "sha512-lt0mJ6YAnsrBErpTMWeu5kl/tg9xMAWjavYTN6VQXM1A/teBITuNcccXsCxF0tDQQJf9DfAaX5O4e0zp0KlfZw==", "signatures": [{"sig": "MEUCIBe4JKQoCYGiCeeXElb05Qy277RAbg6eCqcYE6LNWqO4AiEAg2g4/u73R/yKehL9vSE2on5bq1ckZ3rrubKrWQhZN1w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16750, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJt/RCRA9TVsSAnZWagAA64UP/3Kn8Q4fuH4c74KU13Gf\nqscYlQCryUoxJJr1gADawf/iMQD1dNyafITP4TkHXxdEpyD9jyZYK+3B22S3\ncI9VYWtW0vRgbwEvXN1nXCGi04CCTQvbBiQ2GaMRWnzBZkA3vv1k48sPr/tl\ncbF888Ct3hVHDcKWTDCLFNSlkH95vnyxCBDzLYAwVQ4DdOAJOvWOZ2CbKTNd\n64U4PKAnOqoVEiSJmNEUSxjipemj3FKvYfBfuIHwCY8L3+BucVys+RKN0H3S\n5+4aMzfTB+Z0u7DiJxqCpe+NZA1VxqKPex4bW10GLkYCdrgif5HeSnIbyUko\nA6t1iOP8jAXE2AGOCkidC5XtQSNRwVGe1Rhop8JKtXD6xN//CxxrCsD9UdkR\nhVYojiAG2qjUZASjOug1ugB3k/N6brVsr0mfWJD4xME+10TS9Aq2drXmj5TF\nbVUpp/f+5H9DphgNpTK625aRpctUil4OCfY1XgcuF37bm58v2ek7QMSF0OLW\n/ZI9b9WX7WGY76UHXKwYxAP6rFV8igpuxmfbDNldCw97RmpRT/VTZ48ohjVc\nChT3irUZEjLvZ118SZdtaGjtCTefEgBwa4bMdh+K9Bz1dH3RkA08e/krBP/x\n9QeFUas5hyJMLMDDIrspTpqMagSwYLJevVlX6BdwQdv5CRPCQhTiVGooEOy2\ngRp9\r\n=KzGv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "engines": {"node": ">=8"}, "gitHead": "0656a46a3573a4de09f592f1d334433d223e68b7", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/cacheable-request.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "12.11.1", "dependencies": {"keyv": "^4.0.0", "get-stream": "^5.1.0", "responselike": "^2.0.0", "normalize-url": "^4.1.0", "clone-response": "^1.0.2", "lowercase-keys": "^2.0.0", "http-cache-semantics": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.23.0", "ava": "^1.1.0", "nyc": "^14.1.1", "pify": "^4.0.0", "this": "^1.0.2", "delay": "^4.0.0", "sqlite3": "^4.0.2", "coveralls": "^3.0.0", "@keyv/sqlite": "^2.0.0", "create-test-server": "3.0.0", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_7.0.1_1579605969134_0.1316842713674622", "host": "s3://npm-registry-packages"}}, "7.0.2": {"name": "cacheable-request", "version": "7.0.2", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@7.0.2", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "homepage": "https://github.com/lukechilds/cacheable-request#readme", "bugs": {"url": "https://github.com/lukechilds/cacheable-request/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "ea0d0b889364a25854757301ca12b2da77f91d27", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-7.0.2.tgz", "fileCount": 4, "integrity": "sha512-pouW8/FmiPQbuGpkXQ9BAPv/Mo5xDGANgSNXzTzJ8DrKGuXOssM4wIQRjfanNRh3Yu5cfYPvcorqbhg2KIJtew==", "signatures": [{"sig": "MEQCIG+hIW1Uq6H1S4WCldUkdDAzrQHR7/Vxtcx5mY2D29HvAiAKTSpMcwEQ5KUT3FkTCvXR2wGJLZ+wBuOVYbOXf4xmIg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16750, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwKM4CRA9TVsSAnZWagAAqpIQAIENvdixKOBqzvoupIkw\nRc+ua4F4JSJtR6lsNC9rDqb7CTjwiMTmwoEsuUzpEVYqqaSa03+TLKMapnhP\nYmghGdoyN0nOBxCe94mvT54i625tkqHn8z7O2691pYgyz0o5mASoYMBSgMMh\ns8mNmhokEV42HF7snuL4Zi9Te/VeTxtCUBO1EERFxIot6Ep8J6Uj65BYuiiI\noTBHLmS/jnzYpYfMOgHnHwFT0Pi75lwMDh0qNg3MISzICXpHeFySrKGoV6V4\nvcwVEUM6ugaHirrXCDhW9LXOmp6bKkI8VdwtlLlyKgH2BWPNj5Jrx2RTVefV\nXpHYZIHi+WKFynD/QYsrQXJU+V6BUjID9+YI0fxvdYGSt0IAKcCVeUpL2NpK\n/QT+OcnXi/No6i0fYFRP1KA3WvAhy1DfCU8tm0D4td4HgGQV3QAoZ08wB/DH\nHL/zuMBJ97g6J1ELWYsAEGWxDT2RQlPm70QJzTzHsW2MabyJQGUL6VLD95z+\nyP5zJLEfQejA7xOCw/lIDb+xvJ9ItLomMAvb3+YuQ6uh1lq2bm0DktSRcPSn\n8ObnGskTeQwQYmPJWMxwFvmlj2aCTNFYTvdi6zIta2MkfE7NP1t4D/uxvMnG\n66R4BQ0zOZsW14a8doaoUVLjmeyac1Oc2+Kash72V6xVvzFKXDADsg9PblLu\nJv+m\r\n=qg+M\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "engines": {"node": ">=8"}, "gitHead": "9e1c51c9325d16b62e51abad22c15aa53ac37337", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/lukechilds/cacheable-request.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "14.12.0", "dependencies": {"keyv": "^4.0.0", "get-stream": "^5.1.0", "responselike": "^2.0.0", "normalize-url": "^6.0.1", "clone-response": "^1.0.2", "lowercase-keys": "^2.0.0", "http-cache-semantics": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.23.0", "ava": "^1.1.0", "nyc": "^14.1.1", "pify": "^4.0.0", "this": "^1.0.2", "delay": "^4.0.0", "sqlite3": "^4.0.2", "coveralls": "^3.0.0", "@keyv/sqlite": "^2.0.0", "create-test-server": "3.0.0", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_7.0.2_1623237432390_0.32290954279577866", "host": "s3://npm-registry-packages"}}, "8.0.0": {"name": "cacheable-request", "version": "8.0.0", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@8.0.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "homepage": "https://github.com/jaredwray/cacheable-request#readme", "bugs": {"url": "https://github.com/jaredwray/cacheable-request/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "0f4547709a24ed7c3758ed0b7d636964d08179bb", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-8.0.0.tgz", "fileCount": 4, "integrity": "sha512-zsDLn+iW59i07duvmN2vPsYfuVcH78aDRsMvHawEV0o3jb5uWVwwyl4sGlk28YbcrLF7SHkxNhelY+lSpFG3SA==", "signatures": [{"sig": "MEUCIEJVrYk+VsUoJsghdxkFCr/WZiwpPtZuISLbcGQwKwTvAiEAr1Oc9x1rVDz/oj8Off7fACK7txxTm1N5XS0QkY4+Wgw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17963, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipNroACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrhNw/+Nv9zC8+HwsMEwvju4W0419JL1SwzTu+KBrzzAF84fySQuIY7\r\nEM+YsZA+UumXrESlxJqCGNg987Wz034PFrdogcF5FIfINPXR/oDj7BhR3j7/\r\nxq/7NOkAbz3P2cHoVp2Im+k+sUE4ibhywkySF6EEMCAKtQr5/vOQCgc22p9l\r\nKJqds/vZBmE+cGeNgoqcfW9HiIfZ7+pH0aIeGSwiIJQ+4xoGvoQPB9rqLfXB\r\n58J4CY9QKfoVc8Veq073HAE+A3rnKqjYhuuMvX748k1vt2ZE6tF4xOBWjriE\r\nYlKuMfgi0go8EvbCmiApyEUiCjdnWsG1n6j7tp4wS7bfDW6QA3aXIrrnWHeL\r\nmQuE0d3nILY0AtOJZp+cSGAz/h2k+9HoO+ZqouXx0Za3zdz6DhgAofZGdu5P\r\nNn6Dbu9fNpcUWVf4G6Tg+x2hbQGlYbKYwOaBy+7Vf5aGX/6GwY7zSmguTdn8\r\nNTNPgapRgPNbYexQyYLTmR215Nt+CNpmvNURh57AG4f6ZTdLuT5h5cQRMCAI\r\nf7wDcOwq/FCm4WMRNFORvsyHmVr/GHEFCdhzPqRqn5k/xSqhkpEbuLqvYuAv\r\nd9H0cBmWClHQ/Y7qLpR0/kRBpPzrBHid4wGPWwb7r2ltwUtxA2dHidS2ak21\r\n0Ba3M6hq1P2yoO4QSsmbdJTJyWYbD4n2NtM=\r\n=Qf1t\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "type": "module", "engines": {"node": ">=14"}, "gitHead": "7b358d7512716e27695b0e3317f38c2a102526dc", "scripts": {"test": "xo && nyc ava", "clean": "rm -rf node_modules && rm -rf .nyc_output && rm -rf coverage.lcov && rm -rf ./test/testdb.sqlite", "coverage": "nyc report --reporter=text-lcov > coverage.lcov"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/cacheable-request.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "16.13.1", "dependencies": {"keyv": "^4.3.0", "get-stream": "^6.0.1", "responselike": "^2.0.0", "normalize-url": "^7.0.3", "clone-response": "^1.0.2", "http-cache-semantics": "^4.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.50.0", "ava": "^4.3.0", "nyc": "^15.1.0", "pify": "^6.0.0", "this": "^1.1.0", "delay": "^5.0.0", "sqlite3": "^5.0.8", "@babel/core": "^7.18.2", "@keyv/sqlite": "^3.5.2", "@babel/register": "^7.17.7", "create-test-server": "3.0.1", "eslint-config-xo-lukechilds": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_8.0.0_1654971112099_0.18578767235409122", "host": "s3://npm-registry-packages"}}, "8.0.1": {"name": "cacheable-request", "version": "8.0.1", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@8.0.1", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "homepage": "https://github.com/jaredwray/cacheable-request#readme", "bugs": {"url": "https://github.com/jaredwray/cacheable-request/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "8f970af8094e16350a184a070f6b48d31e69d564", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-8.0.1.tgz", "fileCount": 4, "integrity": "sha512-jMxkKx4EaDyQx9xI1JPI2Sciubphg7BbDGniTZ03DKHY7HPiRiSnOJZqM64hsNOi5qy7TD9sIHbXIEQtk1TG7A==", "signatures": [{"sig": "MEQCIGI9i6lCpN6aOsrQO7JiwYxlYs8+svO1bKSyYTbWI1i1AiA5k7s3fL1ta+NO/t+xIeG+uzsKYw6lPqCuMBiq7BTjqg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17933, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipOF5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqfng//YPbyooPVJ4oywfNlq2ltzPWUFV5yCeiQir2qPQXEddgOSPZ7\r\nDs8HzAC5a6WjulyyHCkC094kiRcp/XjiRk5UzmKKhN/h36vNDpbvEps5xqJp\r\nh4a4S4zPx1xtALmOkRbvn2ooBN6v+CSxEZV5vxwaBG4DpZBO3hy05gSP/9aV\r\nL2bNOYm6/x6IXJZAkKy5CfAuptJBh7nz0n+UkhsfmnVY8ZyCQe+lnjxbCLyy\r\n6BfjeJ6crr340fwchrCCdQhi37XCF9Erim65mLpdc1JGGEbNGRSnJkn9H2Q1\r\nKn/wvcRnHEOKJMBYetx4Xqr6tYM8rUnQpvNZ1++TVFaZenc0GZydCF5uCgPN\r\nFrquisoXZb96FRei7yw2HGYWrSne1XTWPdJkKkesPSt0qmkdRUHgBkoP7JwQ\r\nKhwIWz0QwgMhtYR9BVSukMKrT622AKB8Ko+aa6rkCTIkIOopIZlvh/kwbNhg\r\nrWoc94Z4viBAbYaQjINUwn+OLPBm1cpo+0ZJcFHwFUtIDAGNXHdmkUELSkzS\r\nS0HS65yJAnAd+YEW7Ks4v/bnOc2zu8rT1ZpeWX1v08370kKQj0sPRH0LtHNu\r\nKw6tVdSOCMpV0J2P8fwqauCBzfwZtsCRHU0xBYJvjYmhHTGJYRbX65avdbzp\r\nFw+bp93/EtKaMfGR2THFxG/iM9HCbwz9sUs=\r\n=qBjE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "type": "module", "engines": {"node": ">=14"}, "gitHead": "4192f5e6ed49c0a8d72e1d7611ca0aa290a6a33a", "scripts": {"test": "xo && nyc ava", "clean": "rm -rf node_modules && rm -rf .nyc_output && rm -rf coverage.lcov && rm -rf ./test/testdb.sqlite", "coverage": "nyc report --reporter=text-lcov > coverage.lcov"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/cacheable-request.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "16.13.1", "dependencies": {"keyv": "^4.3.0", "get-stream": "^6.0.1", "responselike": "^2.0.0", "normalize-url": "^7.0.3", "clone-response": "^1.0.2", "http-cache-semantics": "^4.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.50.0", "ava": "^4.3.0", "nyc": "^15.1.0", "pify": "^6.0.0", "delay": "^5.0.0", "sqlite3": "^5.0.8", "@babel/core": "^7.18.2", "@keyv/sqlite": "^3.5.2", "@babel/register": "^7.17.7", "create-test-server": "3.0.1", "eslint-config-xo-lukechilds": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_8.0.1_1654972793044_0.39662002920724104", "host": "s3://npm-registry-packages"}}, "8.3.0": {"name": "cacheable-request", "version": "8.3.0", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@8.3.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "homepage": "https://github.com/jaredwray/cacheable-request#readme", "bugs": {"url": "https://github.com/jaredwray/cacheable-request/issues"}, "xo": {"rules": {"new-cap": 0, "@typescript-eslint/ban-types": 0, "@typescript-eslint/await-thenable": 0, "@typescript-eslint/no-unsafe-call": 0, "@typescript-eslint/no-unsafe-return": 0, "@typescript-eslint/naming-convention": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-floating-promises": 0, "@typescript-eslint/no-unsafe-assignment": 0, "@typescript-eslint/object-curly-spacing": 0, "@typescript-eslint/restrict-plus-operands": 0, "@typescript-eslint/restrict-template-expressions": 0}, "extends": ["plugin:jest/recommended"], "plugins": ["jest"]}, "dist": {"shasum": "96cc2927bd08aabbdeb4fe9d5dc1f95cb321920a", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-8.3.0.tgz", "fileCount": 3, "integrity": "sha512-dgWLxVs1lla5PgD17bD+sllNoRhi+h4ytelILlhUV8dgcUsu/5HcrAy7/F9Q9NiWI4CIFFjmPX53Ls6AVSyAwg==", "signatures": [{"sig": "MEQCICm4lqYtPCWxu/4hQNMxgYlJj94qazpN3USBKTrFuiS6AiATE6ctjn32RsxzsbHjnKzP9PlLqEPq322xrwc+3kAaIw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11253, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiucx5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrvnA/9HES8CyyXoA/IDSUkFYYxZ9XaLhHHQUM5/C+I+XGgS223Cvse\r\n51abw3+PNoxEb3D7AJxhcs4l2ptkpNkeoiBN9jBWbNIXXogkwbeay9GY2EAW\r\nXcpWx8vG6Shxps/D4Lbe0sMEtIOhPYnktBkR4ZLnXl/DiKcIA3YzF6Kt17xU\r\nlj4fGSs6aZJ1+HKCwzBkYurtYbx/EpnPK2urg+whI56hmDoWWFyKdsjtnAl6\r\n/MfVYBVWG9jWa1EZVC+MSNDanWXno/sIQ/oQBoJBfgm00+LCepRotjhHgE+k\r\nIeo5P29GmVUWqopfF69vy4sQ1gLltU4Hv1OySHM1FjEyLRO27N0Iuj7YhDc+\r\nU41spEadb8YQsJCsBQaDcJWr2SEL38zQi+R1MX5ktY6UeBVdsvj2pCmPNbjH\r\ne7ArqEhJ0WCD8biHkvOM7h68DYnu//Wp2kYHOr/TM/EyRAjixRkjPzuK3+k4\r\nyq+P6ljksPRJ4cl6/eSnUrxnBeWZPa+3famVARJ7lWaK0Tsx2vIZoK/0Lgqb\r\n0PxUrMHsvMsUiHiZbZ3VXtU9VmST36VI1bye5CNFM/2YhOpYaq0RrDWS78ii\r\n7uRXSv4lYMwnCjiotsW0rf80MrLDpP/HZHSLf0Tq1ZqxWu/6UclB5YpLwONB\r\nEu+j43u3hW8D2o51SKhHVcpO1yD0wiY+3tE=\r\n=z++l\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"globals": {"ts-jest": {"tsconfig": "./tsconfig.json"}}, "testMatch": ["**/test/*.test.(ts)"], "transform": {"^.+\\.(ts|tsx)$": "ts-jest"}, "testEnvironment": "node", "moduleFileExtensions": ["ts", "js"]}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "engines": {"node": ">=14.16"}, "gitHead": "f736d45d171b22c4e0b378cbaaf3f44717003795", "scripts": {"test": "xo && jest --coverage", "build": "tsc --project tsconfig.build.json", "clean": "rm -rf node_modules && rm -rf ./coverage && rm -rf ./package-lock.json && rm -rf ./test/testdb.sqlite && rm -rf ./dist"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/cacheable-request.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "16.13.1", "dependencies": {"keyv": "^4.3.2", "get-stream": "^6.0.1", "responselike": "^2.0.0", "normalize-url": "^6.0.3", "mimic-response": "^3.1.0", "http-cache-semantics": "^4.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.50.0", "jest": "^28.1.1", "pify": "^5.0.0", "delay": "^5.0.0", "sqlite3": "^5.0.8", "ts-jest": "^28.0.5", "ts-node": "^10.8.1", "typescript": "^4.7.4", "@babel/core": "^7.18.5", "@types/jest": "^28.1.3", "@types/node": "^18.0.0", "@types/pify": "^5.0.1", "@keyv/sqlite": "^3.5.2", "@types/delay": "^3.1.0", "@types/sqlite3": "^3.1.8", "@babel/preset-env": "^7.18.2", "@types/get-stream": "^3.0.2", "create-test-server": "3.0.1", "eslint-plugin-jest": "^26.5.3", "@types/responselike": "^1.0.0", "@types/create-test-server": "^3.0.1", "@types/http-cache-semantics": "^4.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_8.3.0_1656343673264_0.659839846961271", "host": "s3://npm-registry-packages"}}, "8.3.1": {"name": "cacheable-request", "version": "8.3.1", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@8.3.1", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "homepage": "https://github.com/jaredwray/cacheable-request#readme", "bugs": {"url": "https://github.com/jaredwray/cacheable-request/issues"}, "xo": {"rules": {"new-cap": 0, "@typescript-eslint/ban-types": 0, "@typescript-eslint/await-thenable": 0, "@typescript-eslint/no-unsafe-call": 0, "@typescript-eslint/no-unsafe-return": 0, "@typescript-eslint/naming-convention": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-floating-promises": 0, "@typescript-eslint/no-unsafe-assignment": 0, "@typescript-eslint/object-curly-spacing": 0, "@typescript-eslint/restrict-plus-operands": 0, "@typescript-eslint/restrict-template-expressions": 0}, "extends": ["plugin:jest/recommended"], "plugins": ["jest"]}, "dist": {"shasum": "c7abb281fbcb299d106086d282d21066544d1f67", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-8.3.1.tgz", "fileCount": 7, "integrity": "sha512-9BmhtKGqrX+sT4BARNR0YTFI84BLcuoJpYReaTglhB1VP7UFQN9g3awXpC9frAqFL3Wnv8RYxwz6XQvtgML6IQ==", "signatures": [{"sig": "MEUCICayNpeckY9rm0l6gHI13l0zrPj47Brug/E64iVSsSyVAiEAo+GtG3h/scxD07hs5qTLGKGNkm/dF/j1SnjMw+BeWpI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32815, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiuczTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmreAA/+JI1htq1b3w2t6cByDRBZMCmWE0X3K8ftK08nf2C5lIo1a4j9\r\n3Uzoa/kmbz3TOFtMRdsY0Na/f59YI3d/AoHkt6eyAeL3RyWOTmsWquplU5Ht\r\n9bMI0IcyZYOmb13aYbPZzMcpEKx734rZVdJe3LFottqwb50VWzhrLRTGkYLO\r\nvzJ5Bt1q99gXRci0yaSuI5GKKq0NmtkbcXkcYPNzS5iyMokZ8qY3dIJbbYZ3\r\nUpd6tPOwYy63R2hrXVGTW5UbMaJCs4NS5PE30tkZD+8P1wrlQ81mEucQ+m0P\r\n7CzrUd2v8hZzQkakZPvuthAgjCSMGyZaGeqxQ06SI0NB3YupubeBfpgVRMgB\r\nTDh/1zT10r2aXbgpeUl0ViN+OO3GkxXwwzF3colxQ7qwoM4mqs/s+lxQ/O7P\r\nK4XVT+0GDM4z8/nPKZf0oSIMw0maGVYBCKf4qldGs/YQ+JeVbNg0T6VZlObo\r\nHNB+6ye3h3owsOdZbBHMQAfTfqKr9bVg7ksFBBJ0rEoJy8x5RSk4GevHCWYT\r\nEWM1x3IAW9tYZeOPLqaDRzuuVPDLIlUVTfaVfBEVYNS3SPNTkZghIsQ7LZPu\r\nz1cPahaCM76el65AzX81HLsW2e2a91HIZG/cRLDLhMSzQpFDIqjOjM9YdiQZ\r\n3IE14N6IPT25o9RXOPSOmj/pZaiQHvlC5js=\r\n=gFcv\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"globals": {"ts-jest": {"tsconfig": "./tsconfig.json"}}, "testMatch": ["**/test/*.test.(ts)"], "transform": {"^.+\\.(ts|tsx)$": "ts-jest"}, "testEnvironment": "node", "moduleFileExtensions": ["ts", "js"]}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "engines": {"node": ">=14.16"}, "gitHead": "f736d45d171b22c4e0b378cbaaf3f44717003795", "scripts": {"test": "xo && jest --coverage", "build": "tsc --project tsconfig.build.json", "clean": "rm -rf node_modules && rm -rf ./coverage && rm -rf ./package-lock.json && rm -rf ./test/testdb.sqlite && rm -rf ./dist"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/cacheable-request.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "16.13.1", "dependencies": {"keyv": "^4.3.2", "get-stream": "^6.0.1", "responselike": "^2.0.0", "normalize-url": "^6.0.3", "mimic-response": "^3.1.0", "http-cache-semantics": "^4.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.50.0", "jest": "^28.1.1", "pify": "^5.0.0", "delay": "^5.0.0", "sqlite3": "^5.0.8", "ts-jest": "^28.0.5", "ts-node": "^10.8.1", "typescript": "^4.7.4", "@babel/core": "^7.18.5", "@types/jest": "^28.1.3", "@types/node": "^18.0.0", "@types/pify": "^5.0.1", "@keyv/sqlite": "^3.5.2", "@types/delay": "^3.1.0", "@types/sqlite3": "^3.1.8", "@babel/preset-env": "^7.18.2", "@types/get-stream": "^3.0.2", "create-test-server": "3.0.1", "eslint-plugin-jest": "^26.5.3", "@types/responselike": "^1.0.0", "@types/create-test-server": "^3.0.1", "@types/http-cache-semantics": "^4.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_8.3.1_1656343762988_0.7949596146434801", "host": "s3://npm-registry-packages"}}, "9.0.0": {"name": "cacheable-request", "version": "9.0.0", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@9.0.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "homepage": "https://github.com/jaredwray/cacheable-request#readme", "bugs": {"url": "https://github.com/jaredwray/cacheable-request/issues"}, "xo": {"rules": {"new-cap": 0, "@typescript-eslint/ban-types": 0, "@typescript-eslint/no-unsafe-call": 0, "@typescript-eslint/no-unsafe-return": 0, "@typescript-eslint/no-unsafe-assignment": 0, "@typescript-eslint/restrict-plus-operands": 0, "@typescript-eslint/restrict-template-expressions": 0}, "extends": ["plugin:jest/recommended"], "plugins": ["jest"]}, "dist": {"shasum": "7d0a16a09b4fd9f4f3af45423f7e5984381aed49", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-9.0.0.tgz", "fileCount": 7, "integrity": "sha512-qKN33DJz0hwa8KS/Ed2iwUZU/UTJTlwUP88nJW2pcIAKrxpTM+RSpGG+tQGOL5ii2Sh8/w4eiUKZjPZHdouwYg==", "signatures": [{"sig": "MEUCIQDswCX7bT8b5skTpcxOTME4To8Q+IEDQmDug6+V/QBwtgIgFps82YDBxsVCOYMflk1w/nDC49aXE1rpg1sdtlh8iKU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35240, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi98e9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqt4Q//WH/G7WtwdA8VLIgJAYL942Q3QsyjLYrjV5aH6kj6qiSTKHXX\r\nfjcaLwYhYb6r98Xsf9/PNn9tq1QrKu8+cYUnolM/jU+kiNslLqyGeBJiztTn\r\n5FkB7BIyxsEUp0o+XfnlbUFoWLQ/PdKHCBWbP+2hN3CYlE/h4q5o06Cd5K9I\r\nZPtr7qn9FeYsVhrWIhH89mhnjhdvBhCnYx57xO3y1Yg4nLDtgoi8lSCTVu7f\r\ngCuC/nBWzKBqM0C/dDeZxMgW1a1pe+vlBH9WDhwPnDJRSNm9EUrVi817k5wy\r\n7Jo+Csl0+6PS6UMhePjzH2SNMDT1tF66b1v6DEz1sqZAtG/qi7FGOhk0Phhj\r\nAfqjm9+xHbqG9/2PUNwn/ndaHSO+DNxcf195WgbZu/Z7+uINU/NGTMPpUp9F\r\ngQl1PYOAmWeTzZnLplyw3zYxuQaMWp2/PVL7pk+Cq7nsPeoMr/ZJkxX+u1A8\r\nj8kzDManRI9jwASWzONRS0llSOgFQ/Cm9JDz1HLJmFG/zs6cGsUpJ6EvinGZ\r\nwXvHZJ464p7lRKtc3NVzNqAiznmXh+DkGHM15Ncp1sVCrHCfKGux5Iye1Kgw\r\nz2SHOUdoh0boUrkAofUu1SWNEpg+mNN4G6uvGoVW+byfZuMMy3qcWmDYunAK\r\ntn9ML/Z6hhCPQwWoLfl3yTlwysVca7octzA=\r\n=jrvj\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"globals": {"ts-jest": {"useESM": true, "tsconfig": "./tsconfig.build.json"}}, "resolver": "ts-jest-resolver", "testMatch": ["**/test/*.test.(ts)"], "transform": {"^.+\\.(ts|tsx)$": "ts-jest"}, "testEnvironment": "node", "moduleFileExtensions": ["ts", "js"], "extensionsToTreatAsEsm": [".ts"]}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=14.16"}, "exports": "./dist/index.js", "gitHead": "364d7265c214c74007471151bb48bb21954e741d", "scripts": {"test": "xo && NODE_OPTIONS=--experimental-vm-modules jest --coverage ", "build": "tsc --project tsconfig.build.json", "clean": "rm -rf node_modules && rm -rf ./coverage && rm -rf ./package-lock.json && rm -rf ./test/testdb.sqlite && rm -rf ./dist"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/cacheable-request.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "16.13.1", "dependencies": {"keyv": "^4.3.3", "get-stream": "^6.0.1", "responselike": "^3.0.0", "normalize-url": "^7.0.3", "mimic-response": "^4.0.0", "http-cache-semantics": "^4.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.51.0", "jest": "^28.1.3", "pify": "^6.0.0", "delay": "^5.0.0", "sqlite3": "^5.0.11", "ts-jest": "^28.0.7", "ts-node": "^10.9.1", "typescript": "^4.7.4", "@types/jest": "^28.1.6", "@types/node": "^18.7.3", "@types/pify": "^5.0.1", "@keyv/sqlite": "^3.5.3", "@types/delay": "^3.1.0", "@types/sqlite3": "^3.1.8", "ts-jest-resolver": "^2.0.0", "@types/get-stream": "^3.0.2", "create-test-server": "3.0.1", "eslint-plugin-jest": "^26.8.2", "@types/responselike": "^1.0.0", "@types/create-test-server": "^3.0.1", "@types/http-cache-semantics": "^4.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_9.0.0_1660405692995_0.9324529572173805", "host": "s3://npm-registry-packages"}}, "10.0.0": {"name": "cacheable-request", "version": "10.0.0", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@10.0.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "homepage": "https://github.com/jaredwray/cacheable-request#readme", "bugs": {"url": "https://github.com/jaredwray/cacheable-request/issues"}, "xo": {"rules": {"new-cap": 0, "@typescript-eslint/ban-types": 0, "@typescript-eslint/no-namespace": 0, "@typescript-eslint/no-unsafe-call": 0, "@typescript-eslint/no-unsafe-return": 0, "@typescript-eslint/no-unsafe-assignment": 0, "@typescript-eslint/no-implicit-any-catch": 0, "@typescript-eslint/restrict-plus-operands": 0, "@typescript-eslint/triple-slash-reference": 0, "@typescript-eslint/restrict-template-expressions": 0}, "extends": ["plugin:jest/recommended"], "plugins": ["jest"]}, "dist": {"shasum": "ccf8be30fed4b7b47618f6ce5aa9839be5ec87d3", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-10.0.0.tgz", "fileCount": 3, "integrity": "sha512-C1KfsV7dZwFwhlXXSeqDZ5UuI4FY7zf4gv8SlbHfr6jmhZSdXyKy/Ku2dVbGZ8YLNlhJqgsgAOBLImdkthWTPA==", "signatures": [{"sig": "MEUCIQCSeHjrLyHE0bsYXdt8/vQlEH+AcT/Qh2BJL79vePUVqgIgSAmXgFO5JQ67NVgLSfCCVC0cTJMc+X9UPgyPnpEXwNo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12992, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjHMU5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmotpRAAnXiFgofOVa3HM/ouKQt3qYQsVUyPgQpj543504u9N+wOPkUQ\r\nYD9plnOAWoR+VuxSf0m2KkQ4H57k2KSMaPwZTRzEilpjbdCTo2sVbYemOipW\r\nch3V5KhL096ADvKOxVJ0ROF3bOWWhFB9YBagGT6oLuggyrB7PCWH02DEU9DZ\r\nLw/UOUdQAjobv15hjqJte+738obrrceUFFDN9ZeZRkhXfXUow9Eooi2CyPQ6\r\nno+LIxXxzEIGD/tzPHh2V4CfwTq2+PSVyVR2gzjnF3gLMe7U4GxbzPYGDRCn\r\nSrnhoiL1242KSCKnuOy0AnqhGpPHU53g4LlrSoPSVnt4XXlskCMOrVuuvhmi\r\nlqsk3AUa0us/6QNKr2lYYCZX422Ac/l0XbR2gajAn4ceXxCKJhhX29L6yTMs\r\n7N+Vuk1X+ngUIqrPbfUp3p+45djyTjTX1q0E9RUHofvbGTd5UvB9r4F35GiG\r\nBnjQVz7BHcHTn0Wq5BUEMZDdi8ojca43V2MaWysQgR4rCQU9ngOibboscmUk\r\n62ohZKuP/L1P7/Y3MLZoglly9fnwwQDxLAiowbKiaXX5BHtP+WLJ2k0Ku2UX\r\nodkFSgVYxCj7Sgx9TEM3vfAHlf3TNZh0+Ward6KwU+8AkOwc0USUcUwtOOwH\r\nAailVuTojpmNJP4tDjtw4c9RRuogrPWWA2Q=\r\n=aMy4\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"globals": {"ts-jest": {"useESM": true, "tsconfig": "./tsconfig.build.json"}}, "resolver": "ts-jest-resolver", "testMatch": ["**/test/*.test.(ts|js)"], "transform": {"^.+\\.(ts|tsx)$": "ts-jest"}, "testEnvironment": "node", "moduleFileExtensions": ["ts", "js"], "extensionsToTreatAsEsm": [".ts"]}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=14.16"}, "exports": "./dist/index.js", "gitHead": "72b94f81054d002ed976a98a74c5cc4ed63a4b42", "scripts": {"test": "xo && NODE_OPTIONS=--experimental-vm-modules jest --coverage ", "build": "tsc --project tsconfig.build.json", "clean": "rm -rf node_modules && rm -rf ./coverage && rm -rf ./package-lock.json && rm -rf ./test/testdb.sqlite && rm -rf ./dist"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/cacheable-request.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "16.13.1", "dependencies": {"keyv": "^4.4.1", "get-stream": "^6.0.1", "responselike": "^3.0.0", "normalize-url": "^7.0.3", "mimic-response": "^4.0.0", "http-cache-semantics": "^4.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.51.0", "jest": "^28.1.3", "delay": "^5.0.0", "sqlite3": "^5.0.11", "ts-jest": "^28.0.8", "ts-node": "^10.9.1", "typescript": "^4.7.4", "@types/jest": "^28.1.7", "@types/node": "^18.7.9", "@keyv/sqlite": "^3.5.3", "@types/delay": "^3.1.0", "@types/sqlite3": "^3.1.8", "ts-jest-resolver": "^2.0.0", "@types/get-stream": "^3.0.2", "create-test-server": "3.0.1", "eslint-plugin-jest": "^26.8.7", "@types/responselike": "^1.0.0", "@types/create-test-server": "^3.0.1", "@types/http-cache-semantics": "^4.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_10.0.0_1662829881453_0.47070694884899633", "host": "s3://npm-registry-packages"}}, "10.0.1": {"name": "cacheable-request", "version": "10.0.1", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@10.0.1", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "homepage": "https://github.com/jaredwray/cacheable-request#readme", "bugs": {"url": "https://github.com/jaredwray/cacheable-request/issues"}, "xo": {"rules": {"new-cap": 0, "@typescript-eslint/ban-types": 0, "@typescript-eslint/no-namespace": 0, "@typescript-eslint/no-unsafe-call": 0, "@typescript-eslint/no-unsafe-return": 0, "@typescript-eslint/no-unsafe-assignment": 0, "@typescript-eslint/no-implicit-any-catch": 0, "@typescript-eslint/restrict-plus-operands": 0, "@typescript-eslint/triple-slash-reference": 0, "@typescript-eslint/restrict-template-expressions": 0}, "extends": ["plugin:jest/recommended"], "plugins": ["jest"]}, "dist": {"shasum": "f3b3d7e6a62013312ba07baca2bf3149b634359f", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-10.0.1.tgz", "fileCount": 11, "integrity": "sha512-ctZqjZ3XsKNaIBqc61w/zx/43AvaqJi8gUh9PMMy3pbszgZHQfkmGYpabTiI0F3fKn3839QX8KvhfOTsrnrjUw==", "signatures": [{"sig": "MEUCIQCN5qXOr7QfPsaawbkDSxT/yG+4fKXmZuIDjzNKDwHf/QIgISh92a6bq7DxL1z8KEOtevnfKwnupJePq1EDfJqlTUw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51272, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjHj7eACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmopeA/+JemuJMC/n6eDDbNd0jmXreC4CDkHexmyZxgHxZ2lBaXPcRPG\r\ns2KdgLmGGMD/J214YQDETRXCsVxIkmTa8Kn1cOvDuIii9FuqJebvk5P1440+\r\nJ69SWbY2NGZiNWQ6H9A5Erw/0KpaAVPlAcuPX6U4nwcFVOCjeeFc6uJw4yOD\r\noXKg6Ig/C+M4oOCRy/5Er/Q6lqxFm77jtHDKuvwdBmOOLaoC9CFhbca740Br\r\nwD31IMSAs6uUht5IAM5t5DMpjFf38gNoBOqXlol50OHIvBuy170AN1A23L+y\r\n0u0Ply5BBTPr51v4RoP+bPLuQ0snvWtyQ2OEfelsPqJITA8jusda9wBy96TA\r\ni35uYtxAzjnGpceOrdsV2yT9t3FBIICCz6jnLq1r/VYr7/n5+BgTIErZQHZB\r\nK6sUbsyifnRLw4U8OXmyflmhAPhz8Uh/Nutpg5oNMi2uD3F7WZKZH2qmh9Iy\r\nPSIDM4lKUckMyRjD9Oqw2O3/3S2iTXYtRXMgjEe0EdoZI3bspJgkTq8kuDTD\r\ndLw1f1iDR2ZUkKVWB9P8TRyWvL2Dord7D75fOkgdGrBIwgG45mdGpQCQ/Hwr\r\nafV98NQyyXDId4xIv6BIrpQpYSDJlFV5FDuqMbX/yRtxFHWo7tkHfroAiQ3w\r\ngXImmVg7CkGUIbGZScl1FU0minqg33otZK0=\r\n=hqYW\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"resolver": "ts-jest-resolver", "testMatch": ["**/test/*.test.(ts|js)"], "transform": {"^.+\\.(ts|tsx)$": ["ts-jest", {"useESM": true, "tsconfig": "./tsconfig.build.json"}]}, "testEnvironment": "node", "moduleFileExtensions": ["ts", "js"], "extensionsToTreatAsEsm": [".ts"]}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=14.16"}, "exports": "./dist/index.js", "gitHead": "2fc4028037b214c09ca18a2bebcfe8f3412fa5a6", "scripts": {"test": "xo && NODE_OPTIONS=--experimental-vm-modules jest --coverage ", "build": "tsc --project tsconfig.build.json", "clean": "rm -rf node_modules && rm -rf ./coverage && rm -rf ./package-lock.json && rm -rf ./test/testdb.sqlite && rm -rf ./dist", "prepare": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/cacheable-request.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "16.13.1", "dependencies": {"keyv": "^4.5.0", "get-stream": "^6.0.1", "responselike": "^3.0.0", "normalize-url": "^7.1.0", "mimic-response": "^4.0.0", "http-cache-semantics": "^4.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.51.0", "jest": "^29.0.3", "delay": "^5.0.0", "sqlite3": "^5.0.11", "ts-jest": "^29.0.0", "ts-node": "^10.9.1", "typescript": "^4.8.3", "@types/jest": "^29.0.0", "@types/node": "^18.7.16", "@keyv/sqlite": "^3.5.3", "@types/delay": "^3.1.0", "@types/sqlite3": "^3.1.8", "ts-jest-resolver": "^2.0.0", "@types/get-stream": "^3.0.2", "create-test-server": "3.0.1", "eslint-plugin-jest": "^27.0.4", "@types/responselike": "^1.0.0", "@types/create-test-server": "^3.0.1", "@types/http-cache-semantics": "^4.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_10.0.1_1662926558691_0.2800814829954319", "host": "s3://npm-registry-packages"}}, "10.0.2": {"name": "cacheable-request", "version": "10.0.2", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@10.0.2", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "homepage": "https://github.com/jaredwray/cacheable-request#readme", "bugs": {"url": "https://github.com/jaredwray/cacheable-request/issues"}, "xo": {"rules": {"new-cap": 0, "@typescript-eslint/ban-types": 0, "@typescript-eslint/no-namespace": 0, "@typescript-eslint/no-unsafe-call": 0, "@typescript-eslint/no-unsafe-return": 0, "@typescript-eslint/no-unsafe-assignment": 0, "@typescript-eslint/no-implicit-any-catch": 0, "@typescript-eslint/restrict-plus-operands": 0, "@typescript-eslint/triple-slash-reference": 0, "@typescript-eslint/restrict-template-expressions": 0}, "extends": ["plugin:jest/recommended"], "plugins": ["jest"]}, "dist": {"shasum": "5a9b061fb5e69f3959d39f93431fc8f4f2c70579", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-10.0.2.tgz", "fileCount": 11, "integrity": "sha512-As40MJURq5OPGFHi3H61AZK6jqn3DfkviXUn33O0teaZogOhsA2SpJdT+7UeoVwFkowJn8XBsYegKxd0Xqdf9w==", "signatures": [{"sig": "MEUCIBWHbytOAsaOMo3xt5iBxqauQJihAUHH7XCIoaizt4tcAiEAoNr8U9EJsxWP33QAqS0x7SYO4JLy+ax00Fn6GYxY5FY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51004, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIeHDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrDJw//fLBKKrLQn/eGoh4rygMXJA5v4vfI5MLE+Uqoqe7YcSrd8mlw\r\nMFle//0SZ02d8YxQvhvmhLy3xDX1KsXKzjaSO+DjBBBP9GMdckntV1cAvHN4\r\n4lsPRYabKvpcdWrfp0jjb7j31s1WmRl6UsF/K/ZaSBklGnIa41pZJxlIyDDq\r\nzW8Clt5hIonccWiAzhpOBwFcn+5Hp5x7p4udW95F7umLtdgQViqlpvPZl/Sq\r\n2ofmEJw1vJUETaVUmI/8L0uuQzwA59mcuVkS9OGsCx2j/q0e5NL4/0IezPIo\r\nT0W8OsqjMlFVmW3XPW/vi00OsIvpTKYKg6IranAuTg9Cw0FYp3cSnS+jphnf\r\n2YueHL6kl9t85Aqfj9ajKWtEvcLY/kXuHPQ/OGcjmYfHwoGBQ0FayW3T3JRg\r\nh78PV4s+4NA/q1bJFdET0HlNZidIZ4g9Y8G1+wnaIUrLgNCEMWJxF3VmBmDi\r\nvLknPJCZdOQ6agx8jnQQxAZAGopVNX7nENoXo47/0vDQ5XPLWPqPxxzVs4Bm\r\nvBOYZUKS88ObG34yiQJgBrMCpQT9mquwDkGs2mO057CjTNB03lSvVOFpqMA1\r\nJvPcEiebW1aQJic4J47B0TmvSeh+7ANm8bXsCZ6T3BgcYkrTi79sSXHE0y8c\r\njVMzro74D0uByR9fkKBxWiToDMZIagYXPpU=\r\n=DNvz\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"resolver": "ts-jest-resolver", "testMatch": ["**/test/*.test.(ts|js)"], "transform": {"^.+\\.(ts|tsx)$": ["ts-jest", {"useESM": true, "tsconfig": "./tsconfig.build.json"}]}, "testEnvironment": "node", "moduleFileExtensions": ["ts", "js"], "extensionsToTreatAsEsm": [".ts"]}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=14.16"}, "exports": "./dist/index.js", "gitHead": "7cdb818060590ebf9589ff0459007235f124dd87", "scripts": {"test": "xo && NODE_OPTIONS=--experimental-vm-modules jest --coverage ", "build": "tsc --project tsconfig.build.json", "clean": "rm -rf node_modules && rm -rf ./coverage && rm -rf ./package-lock.json && rm -rf ./test/testdb.sqlite && rm -rf ./dist", "prepare": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/cacheable-request.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "16.13.1", "dependencies": {"keyv": "^4.5.0", "get-stream": "^6.0.1", "responselike": "^3.0.0", "normalize-url": "^7.1.0", "mimic-response": "^4.0.0", "http-cache-semantics": "^4.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.51.0", "jest": "^29.0.3", "delay": "^5.0.0", "sqlite3": "^5.0.11", "ts-jest": "^29.0.0", "ts-node": "^10.9.1", "typescript": "^4.8.3", "@types/jest": "^29.0.0", "@types/node": "^18.7.16", "@keyv/sqlite": "^3.5.3", "@types/delay": "^3.1.0", "@types/sqlite3": "^3.1.8", "ts-jest-resolver": "^2.0.0", "@types/get-stream": "^3.0.2", "create-test-server": "3.0.1", "eslint-plugin-jest": "^27.0.4", "@types/responselike": "^1.0.0", "@types/create-test-server": "^3.0.1", "@types/http-cache-semantics": "^4.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_10.0.2_1663164866928_0.910008174248718", "host": "s3://npm-registry-packages"}}, "10.1.2": {"name": "cacheable-request", "version": "10.1.2", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@10.1.2", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "homepage": "https://github.com/jaredwray/cacheable-request#readme", "bugs": {"url": "https://github.com/jaredwray/cacheable-request/issues"}, "xo": {"rules": {"new-cap": 0, "@typescript-eslint/ban-types": 0, "@typescript-eslint/no-namespace": 0, "@typescript-eslint/no-unsafe-call": 0, "@typescript-eslint/no-unsafe-return": 0, "@typescript-eslint/no-unsafe-assignment": 0, "@typescript-eslint/no-implicit-any-catch": 0, "@typescript-eslint/restrict-plus-operands": 0, "@typescript-eslint/triple-slash-reference": 0, "@typescript-eslint/restrict-template-expressions": 0}, "extends": ["plugin:jest/recommended"], "plugins": ["jest"]}, "dist": {"shasum": "93f77290eebfdff7f4d813ba7d0fa2502c180eec", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-10.1.2.tgz", "fileCount": 11, "integrity": "sha512-N7F4os5ZI+8mWHSbeJmxn+qimf5uK3WU53FD1b298XLGtOLPpSA/1xAchfP4NJlDwqgaviZ0SQfxTQD0K6lr9w==", "signatures": [{"sig": "MEQCICtUHPKerhEiKy6jwHcuD1dMOEi9mQ2O8hDf782hPz7YAiAzFV6Ir4HX6v/cblmHwq19yTu596YaW9UMLeVZqU4ALg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50903, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjJG3HACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqICw/+JjN3UPgPpyeCs/CFGqbSVm9yJL+JqTfY39EWvTX3Z55PIVu4\r\nnoFvp8JNl92NzgvfOsmVfrdWFAOiZEtBm0bxd9ZRsw++th28lg36iBn05cvs\r\nKGH3fEjWxctg9JII4P6YvmhbS10BGRlHuafH9w2K7VtKoOpMypQ7pHMPIH8y\r\nX7aDeBuKiP/uqcQ1nGrL44PlusX7WrRpFHKxq/oVorPVjFm2p+s2UzB7f/Lt\r\nIPfxjOdoUjFYPMmD1qZCEFVOVGbNXWAyEUXAeM38FVGTbiclY1AV7n8G/Crz\r\ng+SadDEFTeRAsT1IATvNCq1lp6lxmwdCWzxXNQJRjwRBEH7iCCe/Mql868/U\r\n0E0jEuznvDAAAZQf6Zxmy3I9VV0WlIeQ/Gs67+B5TViMf9NqyciAYuvyi2au\r\nM6vQ73S19jChFbezD3mo4n2Z+vI6Lio5oJycFGeFo2xQsLMgCi5sElOAb1Y5\r\nMM+lcQjj1P0POWyGFqznF/J5c8BHCeU1+NE6HA4ri4ztZ+pmW+B47qTWHGmg\r\nbPJ83AR06svCSZ1Kt1nJjkwwjE99NYJgQZLeHB1iG7lUjtut78kfGJ50tnEX\r\nbOKBkO4eJ/r4aX6lF3mpr7h2lxji4MknRfTAOogiw6+w1nrm4gTuj9ofijik\r\n3tkAvc+qkkbBcSLGkfEQ/FwMsAEIE9L8wsc=\r\n=KIUi\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"resolver": "ts-jest-resolver", "testMatch": ["**/test/*.test.(ts|js)"], "transform": {"^.+\\.(ts|tsx)$": ["ts-jest", {"useESM": true, "tsconfig": "./tsconfig.build.json"}]}, "testEnvironment": "node", "moduleFileExtensions": ["ts", "js"], "extensionsToTreatAsEsm": [".ts"]}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=14.16"}, "exports": "./dist/index.js", "gitHead": "f2d7bdc934fda98cf72ca872b5bd72abd5825f84", "scripts": {"test": "xo && NODE_OPTIONS=--experimental-vm-modules jest --coverage ", "build": "tsc --project tsconfig.build.json", "clean": "rm -rf node_modules && rm -rf ./coverage && rm -rf ./package-lock.json && rm -rf ./test/testdb.sqlite && rm -rf ./dist", "prepare": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/cacheable-request.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "16.13.1", "dependencies": {"keyv": "^4.5.0", "get-stream": "^6.0.1", "responselike": "^3.0.0", "normalize-url": "^7.1.0", "mimic-response": "^4.0.0", "http-cache-semantics": "^4.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.51.0", "jest": "^29.0.3", "delay": "^5.0.0", "sqlite3": "^5.0.11", "ts-jest": "^29.0.0", "ts-node": "^10.9.1", "typescript": "^4.8.3", "@types/jest": "^29.0.0", "@types/node": "^18.7.16", "@keyv/sqlite": "^3.5.3", "@types/delay": "^3.1.0", "@types/sqlite3": "^3.1.8", "ts-jest-resolver": "^2.0.0", "@types/get-stream": "^3.0.2", "create-test-server": "3.0.1", "eslint-plugin-jest": "^27.0.4", "@types/responselike": "^1.0.0", "@types/create-test-server": "^3.0.1", "@types/http-cache-semantics": "^4.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_10.1.2_1663331783024_0.9805023937338819", "host": "s3://npm-registry-packages"}}, "10.2.0": {"name": "cacheable-request", "version": "10.2.0", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@10.2.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "homepage": "https://github.com/jaredwray/cacheable-request#readme", "bugs": {"url": "https://github.com/jaredwray/cacheable-request/issues"}, "xo": {"rules": {"new-cap": 0, "@typescript-eslint/ban-types": 0, "@typescript-eslint/no-namespace": 0, "@typescript-eslint/no-unsafe-call": 0, "@typescript-eslint/no-unsafe-return": 0, "@typescript-eslint/no-unsafe-assignment": 0, "@typescript-eslint/no-implicit-any-catch": 0, "@typescript-eslint/restrict-plus-operands": 0, "@typescript-eslint/triple-slash-reference": 0, "@typescript-eslint/consistent-type-imports": 0, "@typescript-eslint/consistent-type-definitions": 0, "@typescript-eslint/restrict-template-expressions": 0}, "extends": ["plugin:jest/recommended"], "plugins": ["jest"]}, "dist": {"shasum": "8d8fdd5964ce6543742d9b6134a1e63be4ee7fd8", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-10.2.0.tgz", "fileCount": 11, "integrity": "sha512-PLOMmzyOU3L5HECdZC0PTNawPdP/taOhCNrYUmZchrw7gwERMctfx2wZcndb63w5W06PTHC1Poq89J9chMZPLw==", "signatures": [{"sig": "MEUCIQCeRdqJEqYImLvWUE3nq1BD6qjH98IWR6ULwPk6OaV1yAIgKuRkcmIU1af+rBHXRYpFQKNMZzjUpU3PFs1Mm/yfcRc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51539, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjLRaSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq/ng//bWdy4sksW0s9hhAEwwIG/mHXDrJjwELaRRC5MAR+H4mt3fFG\r\n2jUfMD3oh5dbFW6d8L3Z76t2tk/Sd78nWeWlRPeRBJGAdH3+bszsT3lUGI9F\r\nJ46bDSul8wxLC3TF3cN3Fm66yM+ybPiMuf1M5PudfO4IIldlp+6BmkJAp2d1\r\nj/w4nirRJdno1ZoQVnKbtqlhOwXrGhvUCzbXtpN2dzAaMj7i3h1Jao79xyG4\r\nR70p9K1lbJacLyMn08aioVxdPbbXzzohNuNo39UF0gvmwHdH88JpX8EgWAAg\r\nUhect8J5bF3Yr0OfEU+Yk5j6GDAUVdbLoed9p0Mf1Fd1rrxhn5418ZVIP98t\r\nEoqcwq5HL4yzeoHUEFUb5jjf95kMuJt+2xtdk0dN7Ttkd8MZhRYouDbBgD7W\r\n1OM+u/+oKdaTVPku3Ao1M/qBpXtRR1GWOVUanJILDosyxnkkblGkaFUsL57t\r\nSTYX1NCtAm9tu2CAe4CdffZQOQL12gYFbiG+Ty3ITAzKvxPP6BonjGIKNkhY\r\nvAHrdMWy71k2wJHWH7+5FmP5u4g9gDEITgMTHf7rw2AjY+Do4Z4GA4l8YHv9\r\nFLfuqSwdG1dxGRWHYgxUlrIDpEVCL9/CvjyRRtmyiv2YlMe0k6c+MP9ZcL/u\r\nMmnD48xFRKFlt+ic70eRcvX8LsziGW8+0l8=\r\n=xoBT\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"resolver": "ts-jest-resolver", "testMatch": ["**/test/*.test.(ts|js)"], "transform": {"^.+\\.(ts|tsx)$": ["ts-jest", {"useESM": true, "tsconfig": "./tsconfig.build.json"}]}, "testEnvironment": "node", "moduleFileExtensions": ["ts", "js"], "extensionsToTreatAsEsm": [".ts"]}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=14.16"}, "exports": "./dist/index.js", "gitHead": "7cdd0fa85fd5728e7641db94e58be8be8cea7021", "scripts": {"test": "xo && NODE_OPTIONS=--experimental-vm-modules jest --coverage ", "build": "tsc --project tsconfig.build.json", "clean": "rm -rf node_modules && rm -rf ./coverage && rm -rf ./package-lock.json && rm -rf ./test/testdb.sqlite && rm -rf ./dist", "prepare": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/cacheable-request.git", "type": "git"}, "_npmVersion": "8.19.1", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "18.9.0", "dependencies": {"keyv": "^4.5.0", "get-stream": "^6.0.1", "responselike": "^3.0.0", "normalize-url": "^7.1.0", "mimic-response": "^4.0.0", "http-cache-semantics": "^4.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.52.3", "jest": "^29.0.3", "delay": "^5.0.0", "sqlite3": "^5.1.1", "ts-jest": "^29.0.1", "ts-node": "^10.9.1", "typescript": "^4.8.3", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@keyv/sqlite": "^3.6.1", "@types/delay": "^3.1.0", "@types/sqlite3": "^3.1.8", "ts-jest-resolver": "^2.0.0", "@types/get-stream": "^3.0.2", "create-test-server": "3.0.1", "eslint-plugin-jest": "^27.0.4", "@types/responselike": "^1.0.0", "@types/create-test-server": "^3.0.1", "@types/http-cache-semantics": "^4.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_10.2.0_1663899282341_0.9594523332433038", "host": "s3://npm-registry-packages"}}, "10.2.1": {"name": "cacheable-request", "version": "10.2.1", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@10.2.1", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "homepage": "https://github.com/jaredwray/cacheable-request#readme", "bugs": {"url": "https://github.com/jaredwray/cacheable-request/issues"}, "xo": {"rules": {"new-cap": 0, "@typescript-eslint/ban-types": 0, "@typescript-eslint/no-namespace": 0, "@typescript-eslint/no-unsafe-call": 0, "@typescript-eslint/no-unsafe-return": 0, "@typescript-eslint/no-unsafe-assignment": 0, "@typescript-eslint/no-implicit-any-catch": 0, "@typescript-eslint/restrict-plus-operands": 0, "@typescript-eslint/triple-slash-reference": 0, "@typescript-eslint/consistent-type-imports": 0, "@typescript-eslint/consistent-type-definitions": 0, "@typescript-eslint/restrict-template-expressions": 0}, "extends": ["plugin:jest/recommended"], "plugins": ["jest"]}, "dist": {"shasum": "cbc7480bf057fb7bd5bc7520f7e5a43d9c865626", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-10.2.1.tgz", "fileCount": 11, "integrity": "sha512-3tLJyBjGuXw1s5gpKFSG3iS4kaKT4id04dZi98wzHQp/8cqZNweBnrF9J+rrlvrf4M53OdtDGNctNHFias8BEA==", "signatures": [{"sig": "MEYCIQD5kptLs45PQtjLUEj0ZuOcQwGrTML0zkRs+hqIE9Dc1wIhAO8+p7qRJzpvalN0Bl9xqOGJz/vIFln070qEkrt9Sfjm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52067, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjMbe+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqWdA//fv2TO1QlEUqX6Dt8sl+xIZTgmO0cnNvD/Yw80/n96dDk7skK\r\nYc6+bjw2VjbHStRBjhXvJzzTaITAutgtBjipHQwX1prY+P3R2G3uVW4gKVZ4\r\npdySULGN6TvZeIw60FXU9zjfJ1PQ5qlMTMmoiL3dduFGHhtsP25GDD4euvWF\r\ni6N7rX2h8rYsMj6SG/3XVLTX3fPxlfFBJprPEakBAW7ypDpEphxgcdQEp7b2\r\nxIasdZutVgRN5u2IrULMIH8kibF2RrtS20vkV3RNBnzm7mEF8EexiRq+GAwu\r\n2T1P1/luorq0n4XgolRasKvpSId+f/tuWtxBfZWJz6hFlmJnNOfZpDPlM1DW\r\n9qh8oa9OOMqMgY6v5bXC8P1mHVWRngyKbJSgSSOyCChpkFFmi/KZrd9ho/T6\r\nDp/jJ9AHVfHIzdVk/wEo+p0lK9cxGJF6AeZyMM6f7+ajoyRbVs+mluuvusEq\r\nNVcicB+yXM+3siNqw0+zxGqkVOdmWVf5IK+v9wlmUAqSn2At3Dl9tAFBLCO1\r\nEpQLn0Fvxbz/7MVg0GwDZdQ82YVc6KioVMfpoZ+nTvTStlYWnMc74S5ZjPC/\r\nQCkmTINjZHN69JkgB4f+UtVkmmv/hvjcmeggJehFu47PDCx1Z2xml4iWJTkz\r\nnweCk5XofQ2JCVIE1aGaJozlQebQprEbk+A=\r\n=0fgC\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"resolver": "ts-jest-resolver", "testMatch": ["**/test/*.test.(ts|js)"], "transform": {"^.+\\.(ts|tsx)$": ["ts-jest", {"useESM": true, "tsconfig": "./tsconfig.build.json"}]}, "testEnvironment": "node", "moduleFileExtensions": ["ts", "js"], "extensionsToTreatAsEsm": [".ts"]}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=14.16"}, "exports": "./dist/index.js", "gitHead": "bbf674a4a69985dff06e68a5744fdea3c51ade2f", "scripts": {"test": "xo && NODE_OPTIONS=--experimental-vm-modules jest --coverage ", "build": "tsc --project tsconfig.build.json", "clean": "rm -rf node_modules && rm -rf ./coverage && rm -rf ./package-lock.json && rm -rf ./test/testdb.sqlite && rm -rf ./dist", "prepare": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/cacheable-request.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "16.13.1", "dependencies": {"keyv": "^4.5.0", "get-stream": "^6.0.1", "responselike": "^3.0.0", "normalize-url": "^7.1.0", "mimic-response": "^4.0.0", "http-cache-semantics": "^4.1.0", "@types/http-cache-semantics": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.52.3", "jest": "^29.0.3", "delay": "^5.0.0", "sqlite3": "^5.1.1", "ts-jest": "^29.0.1", "ts-node": "^10.9.1", "typescript": "^4.8.3", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@keyv/sqlite": "^3.6.1", "@types/delay": "^3.1.0", "@types/sqlite3": "^3.1.8", "ts-jest-resolver": "^2.0.0", "@types/get-stream": "^3.0.2", "create-test-server": "3.0.1", "eslint-plugin-jest": "^27.0.4", "@types/responselike": "^1.0.0", "@types/create-test-server": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_10.2.1_1664202686400_0.21732031104218752", "host": "s3://npm-registry-packages"}}, "10.2.2": {"name": "cacheable-request", "version": "10.2.2", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@10.2.2", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "homepage": "https://github.com/jaredwray/cacheable-request#readme", "bugs": {"url": "https://github.com/jaredwray/cacheable-request/issues"}, "xo": {"rules": {"new-cap": 0, "@typescript-eslint/ban-types": 0, "@typescript-eslint/no-namespace": 0, "@typescript-eslint/no-unsafe-call": 0, "@typescript-eslint/no-unsafe-return": 0, "@typescript-eslint/no-unsafe-assignment": 0, "@typescript-eslint/no-implicit-any-catch": 0, "@typescript-eslint/restrict-plus-operands": 0, "@typescript-eslint/triple-slash-reference": 0, "@typescript-eslint/consistent-type-imports": 0, "@typescript-eslint/consistent-type-definitions": 0, "@typescript-eslint/restrict-template-expressions": 0}, "extends": ["plugin:jest/recommended"], "plugins": ["jest"]}, "dist": {"shasum": "07c3d5afcaa2de2e9f66959bacb3ff78da3735fd", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-10.2.2.tgz", "fileCount": 11, "integrity": "sha512-KxjQZM3UIo7/J6W4sLpwFvu1GB3Whv8NtZ8ZrUL284eiQjiXeeqWTdhixNrp/NLZ/JNuFBo6BD4ZaO8ZJ5BN8Q==", "signatures": [{"sig": "MEUCIQD4V0bkjRUEs99/0GjeAD8WUMbUPGjcaOWwS18ikqQtIwIgJTBpwjhWPQZQ9i4WRnuiI7AhraJN9/QpGmfF+rhU6LA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52067, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjUDe/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp2Pw/+Ogcv6p/NtSTU/dkB3Q8X6dKuk9iLuj1KPA4+293dUKPrIvkl\r\n0X/x5xjgh0SKdkj3F/JBJdufKO/o3BEKHvxVX/zuKWp2Tm3170RFELEy3TkN\r\nbxR+Oz28GAoQHcBId1TEihO9BIakutbi+7lbRXJHI5zKkofPGmfypG3hTeRF\r\ndNL6zROZ9I79BL68jciikB3SklmensOlpciSqXD95cNhSFnfb25FW+AyC4Y/\r\nao2vCo4oTgcXWRnH2ka9lHT/F6nfvvIRX758ywm4GDvPXq/By9xtJ/j0znuW\r\nyDboeqG/QfNinBbrYLltcgA618MjmGaxwCBBC8EuWjG5shQDsVvyMQfB2QHW\r\nPBYm6BycL7SmoeRNp46diddmUsyJCSPYeHkReySx8IiAoeVFWdSHNncHSqG4\r\nf+BWUPrQQvUVBn8rK8pWJJ07MrSEd2C6tXkKsLwlbfgDeYo2YrvH4QnwouBv\r\nm07vfVdBreL875h/84GYVg62hyA/h74CBmDexO/eHLPxemPI0tDex96bNggd\r\nVy/XLw2XlgORVUiJieLpM4Oi/op9ffRLo5tJeXYevzLQDgbfxlzcHFFHzmkC\r\n6ReMY+fOJcAGLwv/zEccv1yBRJlxeUoy4NBIBUkfmzkheyn8SbIY8FhDE+hP\r\nGI7cg6TWfMLxq97SdkCab7V+YiIdDmUPoU8=\r\n=bMH4\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"resolver": "ts-jest-resolver", "testMatch": ["**/test/*.test.(ts|js)"], "transform": {"^.+\\.(ts|tsx)$": ["ts-jest", {"useESM": true, "tsconfig": "./tsconfig.build.json"}]}, "testEnvironment": "node", "moduleFileExtensions": ["ts", "js"], "extensionsToTreatAsEsm": [".ts"]}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=14.16"}, "exports": "./dist/index.js", "gitHead": "2191f985d751643cc93039b392bfd9d8b535b2ed", "scripts": {"test": "xo && NODE_OPTIONS=--experimental-vm-modules jest --coverage ", "build": "tsc --project tsconfig.build.json", "clean": "rm -rf node_modules && rm -rf ./coverage && rm -rf ./package-lock.json && rm -rf ./test/testdb.sqlite && rm -rf ./dist", "prepare": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/cacheable-request.git", "type": "git"}, "_npmVersion": "8.19.1", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "18.9.0", "dependencies": {"keyv": "^4.5.0", "get-stream": "^6.0.1", "responselike": "^3.0.0", "normalize-url": "^7.2.0", "mimic-response": "^4.0.0", "http-cache-semantics": "^4.1.0", "@types/http-cache-semantics": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.52.4", "jest": "^29.2.1", "delay": "^5.0.0", "sqlite3": "^5.1.2", "ts-jest": "^29.0.3", "ts-node": "^10.9.1", "typescript": "^4.8.4", "@types/jest": "^29.2.0", "@types/node": "^18.11.2", "@keyv/sqlite": "^3.6.2", "@types/delay": "^3.1.0", "@types/sqlite3": "^3.1.8", "ts-jest-resolver": "^2.0.0", "@types/get-stream": "^3.0.2", "create-test-server": "3.0.1", "eslint-plugin-jest": "^27.1.3", "@types/responselike": "^1.0.0", "@types/create-test-server": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_10.2.2_1666201534914_0.34496924442912547", "host": "s3://npm-registry-packages"}}, "10.2.3": {"name": "cacheable-request", "version": "10.2.3", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@10.2.3", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "homepage": "https://github.com/jaredwray/cacheable-request#readme", "bugs": {"url": "https://github.com/jaredwray/cacheable-request/issues"}, "xo": {"rules": {"new-cap": 0, "n/prefer-global/url": 0, "@typescript-eslint/ban-types": 0, "@typescript-eslint/no-namespace": 0, "@typescript-eslint/no-unsafe-call": 0, "@typescript-eslint/no-unsafe-return": 0, "@typescript-eslint/no-unsafe-assignment": 0, "@typescript-eslint/no-implicit-any-catch": 0, "@typescript-eslint/restrict-plus-operands": 0, "@typescript-eslint/triple-slash-reference": 0, "@typescript-eslint/consistent-type-imports": 0, "@typescript-eslint/consistent-type-definitions": 0, "@typescript-eslint/restrict-template-expressions": 0}, "extends": ["plugin:jest/recommended"], "plugins": ["jest"]}, "dist": {"shasum": "25277efe121308ab722c28b4164e51382b4adeb1", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-10.2.3.tgz", "fileCount": 11, "integrity": "sha512-6BehRBOs7iurNjAYN9iPazTwFDaMQavJO8W1MEm3s2pH8q/tkPTtLDRUZaweWK87WFGf2Y5wLAlaCJlR5kOz3w==", "signatures": [{"sig": "MEUCICCvWQovXfd6Ji7id1USoolKNc40dRFuWQWiwOq06ZDaAiEA0XikSjCSffH4u981nhsWSiAeEmH3TfLyHeZ3bB0kg80=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52366, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjfQfjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmobAA/9GrHfoMqGCM1KL/IK+TAhirmtVgk9OZmLP+qyUZsCA2w3Up1d\r\nJ4+jOM+wx4LiMDFwagS2MuAKeMgXv+DitYSYL+rvpwlMeY2lGJSyFoQSjpEZ\r\n2eemvZke0F3yMzhVmSIr1K/LcZJ0wqM0yp0n8evVtKYTvowflJNa8rj626tG\r\npuwwEVUjHNhJs2o4RS3z+d10FzkhZS9fSTzuE6QnzmhVGzvMG6rgh3bpr39o\r\nfGo3fGO/EjVJI7C1+/l/xEYSi0cN6yMWB8dsnueVrdFGNatMysomKIZEW3Yx\r\nNz1qXkGPaxJhxHdpxDkpELzmvFCwE39mRf6cDF3DgX7aOg7JsW1026NV7fuG\r\npwmDBxqG2ZbxdJOFdMvaJZ+gtrRvk8sZKLniv/VdrPABNCDahjWVvYG2KJIE\r\nOaLpU8bUTmzaQJiQ08iaoCyhhrpj9Bcn2gpEUdMbW+xhhZcjWP3EaNC19qL4\r\ncHV43mWvwtqHB/curAt9TPhy/1S1+UYmPi1klIBWdT5wR4pQTQQH9sajnzBh\r\n+wp0ulc+AdaaBzkzLvKXrsRQ6fLOdCNyNWDgZLY4DLYBdl7Kd56oIfWQ2Z36\r\nV8kOGXngOqVldbFfX2nLdEfZsR3ENST6EfmX9UaSDM9RsFugvv0xjNkq2b1S\r\nRKzK7C0tZ/6x2g+rvsmU2XSssAXZZBmmZdU=\r\n=GwF7\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"resolver": "ts-jest-resolver", "testMatch": ["**/test/*.test.(ts|js)"], "transform": {"^.+\\.(ts|tsx)$": ["ts-jest", {"useESM": true, "tsconfig": "./tsconfig.build.json"}]}, "testEnvironment": "node", "moduleFileExtensions": ["ts", "js"], "extensionsToTreatAsEsm": [".ts"]}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=14.16"}, "exports": "./dist/index.js", "gitHead": "b4b7bc8c3c0fd95eca2f894c8c8b3f6c5eb4dfb9", "scripts": {"test": "xo && NODE_OPTIONS=--experimental-vm-modules jest --coverage ", "build": "tsc --project tsconfig.build.json", "clean": "rm -rf node_modules && rm -rf ./coverage && rm -rf ./package-lock.json && rm -rf ./test/testdb.sqlite && rm -rf ./dist", "prepare": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/cacheable-request.git", "type": "git"}, "_npmVersion": "8.19.1", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "18.9.0", "dependencies": {"keyv": "^4.5.2", "get-stream": "^6.0.1", "responselike": "^3.0.0", "normalize-url": "^8.0.0", "mimic-response": "^4.0.0", "http-cache-semantics": "^4.1.0", "@types/http-cache-semantics": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.53.1", "jest": "^29.3.1", "delay": "^5.0.0", "sqlite3": "^5.1.2", "ts-jest": "^29.0.3", "ts-node": "^10.9.1", "typescript": "^4.9.3", "@types/jest": "^29.2.3", "@types/node": "^18.11.9", "@keyv/sqlite": "^3.6.2", "@types/delay": "^3.1.0", "@types/sqlite3": "^3.1.8", "ts-jest-resolver": "^2.0.0", "@types/get-stream": "^3.0.2", "create-test-server": "3.0.1", "eslint-plugin-jest": "^27.1.5", "@types/responselike": "^1.0.0", "@types/create-test-server": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_10.2.3_1669138403242_0.11873102510895794", "host": "s3://npm-registry-packages"}}, "10.2.4": {"name": "cacheable-request", "version": "10.2.4", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@10.2.4", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "homepage": "https://github.com/jaredwray/cacheable-request#readme", "bugs": {"url": "https://github.com/jaredwray/cacheable-request/issues"}, "xo": {"rules": {"new-cap": 0, "n/prefer-global/url": 0, "@typescript-eslint/ban-types": 0, "@typescript-eslint/no-namespace": 0, "@typescript-eslint/no-unsafe-call": 0, "unicorn/no-abusive-eslint-disable": 0, "@typescript-eslint/no-unsafe-return": 0, "@typescript-eslint/no-unsafe-assignment": 0, "@typescript-eslint/no-implicit-any-catch": 0, "@typescript-eslint/restrict-plus-operands": 0, "@typescript-eslint/triple-slash-reference": 0, "@typescript-eslint/consistent-type-imports": 0, "@typescript-eslint/consistent-type-definitions": 0, "@typescript-eslint/restrict-template-expressions": 0}, "extends": ["plugin:jest/recommended"], "plugins": ["jest"]}, "dist": {"shasum": "9b9700f9f71b281d5f0e99b514fc9a94e3fbb589", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-10.2.4.tgz", "fileCount": 11, "integrity": "sha512-IWIea8ei1Ht4dBqvlvh7Gs7EYlMyBhlJybLDUB9sadEqHqftmdNieMLIR5ia3vs8gbjj9t8hXLBpUVg3vcQNbg==", "signatures": [{"sig": "MEQCIHh02cviw4/FzIpBKTLx5KOXSj7Eqt33ZZJYMcGhi29FAiBU9ee9tt5+qsXTHdVohaInt+aMUxVTm09njbvXdYgA1A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52428, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjpQAKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrXZA//au60u2gIEd6ljczJ6iDkkjjg8e4MQxdr5mBGfbkQWY7+loQ4\r\noB3i3Ct2TfdF/nSE0pIM60EqohTiNRssfwjEyrUecFfdURGftcEwPY5+oCyu\r\nvyT4qMIgLrr8s3K9P1pber47hcHzb5AX/1yLSkfRtdcnJBL12K67ZiMLiZ3I\r\nC054r6g+LcqOa/wLzLybqmq1wykT6TK1IiH6vs8MTNF/LOBwcNkqQKksKenW\r\nSoX97TXDwtzyCFkaXGSg8Gd0K3jYd6w7yqnpy+nVmdvcUaUPC20m29872dd9\r\nhEiEfeh+tk0JPkyS6j1oEWBygMSFLS8Q/7XXrh9NCuxBGecEDwUhYyEqo8zD\r\n1nkYNxa57uK68NOfxzwdkbuDkbq51bFyW+DGLsavDli9pnhpJFdAyzFJaExL\r\ng1eWng0BAuHQ4bdyPAIda+p6quZcEqn/t0aw0JVQ4Qk4buaRLmsdj9JJT+l3\r\nHEj/PDIxXkwTKKaeuK8O2YKaaJ9K+dxcAgrlg5lJ/hp+riLhvcLbkx7O3ZhM\r\nQmLu2ZXAV0weI7AIsA/dk7DVwf91cMdQFkxA0jmcfKdXn++bi5dP4EKshiYw\r\nUmWv9EW6HvCriylLSKMSWZcIfu3m4maDrds6aaT6wG/akOiFACjGfOIweyAV\r\n9/xn3moBnkn/E5aODPzXp0SQlmXU1BcSCTU=\r\n=Nq0+\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"resolver": "ts-jest-resolver", "testMatch": ["**/test/*.test.(ts|js)"], "transform": {"^.+\\.(ts|tsx)$": ["ts-jest", {"useESM": true, "tsconfig": "./tsconfig.build.json"}]}, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.{ts,js}"], "moduleFileExtensions": ["ts", "js"], "extensionsToTreatAsEsm": [".ts"]}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=14.16"}, "exports": "./dist/index.js", "gitHead": "5e057cfe0b129ecf244ea197634afec386d2a8e8", "scripts": {"test": "xo && NODE_OPTIONS=--experimental-vm-modules jest --coverage ", "build": "tsc --project tsconfig.build.json", "clean": "rm -rf node_modules && rm -rf ./coverage && rm -rf ./package-lock.json && rm -rf ./test/testdb.sqlite && rm -rf ./dist", "prepare": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/cacheable-request.git", "type": "git"}, "_npmVersion": "8.19.1", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "18.9.0", "dependencies": {"keyv": "^4.5.2", "get-stream": "^6.0.1", "responselike": "^3.0.0", "normalize-url": "^8.0.0", "mimic-response": "^4.0.0", "http-cache-semantics": "^4.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.53.1", "jest": "^29.3.1", "pify": "^6.1.0", "delay": "^5.0.0", "express": "^4.18.2", "sqlite3": "^5.1.4", "ts-jest": "^29.0.3", "ts-node": "^10.9.1", "typescript": "^4.9.4", "@types/jest": "^29.2.4", "@types/node": "^18.11.17", "body-parser": "^1.20.1", "@keyv/sqlite": "^3.6.4", "@types/delay": "^3.1.0", "@types/sqlite3": "^3.1.8", "ts-jest-resolver": "^2.0.0", "@types/get-stream": "^3.0.2", "eslint-plugin-jest": "^27.1.7", "@types/responselike": "^1.0.0", "@types/http-cache-semantics": "^4.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_10.2.4_1671757833753_0.4662600497955658", "host": "s3://npm-registry-packages"}}, "10.2.5": {"name": "cacheable-request", "version": "10.2.5", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@10.2.5", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "homepage": "https://github.com/jaredwray/cacheable-request#readme", "bugs": {"url": "https://github.com/jaredwray/cacheable-request/issues"}, "xo": {"rules": {"new-cap": 0, "n/prefer-global/url": 0, "@typescript-eslint/ban-types": 0, "@typescript-eslint/no-namespace": 0, "@typescript-eslint/no-unsafe-call": 0, "unicorn/no-abusive-eslint-disable": 0, "@typescript-eslint/no-unsafe-return": 0, "@typescript-eslint/no-unsafe-assignment": 0, "@typescript-eslint/no-implicit-any-catch": 0, "@typescript-eslint/restrict-plus-operands": 0, "@typescript-eslint/triple-slash-reference": 0, "@typescript-eslint/consistent-type-imports": 0, "@typescript-eslint/consistent-type-definitions": 0, "@typescript-eslint/restrict-template-expressions": 0}, "extends": ["plugin:jest/recommended"], "plugins": ["jest"]}, "dist": {"shasum": "7bf5fbdb4f51dc2537fb5f02a3f8a5aefd2d0f36", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-10.2.5.tgz", "fileCount": 11, "integrity": "sha512-5RwYYCfzjNPsyJxb/QpaM0bfzx+kw5/YpDhZPm9oMIDntHFQ9YXeyV47ZvzlTE0XrrrbyO2UITJH4GF9eRLdXQ==", "signatures": [{"sig": "MEUCIQCJtrp0lYkkLYziL/YGWm3/R3iKNF3XGPbt9UAJkKlk0gIgfZMhBYPzYtbSnEDhbTpea6UZkVz0wBWnmB+z79NpDFg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52718, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjt0v1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqQRQ/8Dx/Wz1EsZr+frCarUVUWtEP3HdBuHywYGJ/e0WScGk1xA0FS\r\nbp+a9eAsMrmIozo/jOMJaqni4O70hhDfRruQHxwKI/jWAtSdBzAAGpT4s4vr\r\nP7MgcAIWH+70rBkwF5nUlmikzphJYuU4SR/3Ku+T5vtlMTfAHdbnf79gRmkN\r\nhqq89MoPo2Orns1z9VR3FG4w3KgsphTyBKq4oh7toNIQOKTMQudLV8ygIRe1\r\n5Tu5KUzQzmAa1P4iLJ+RtGXcqhSELRFzsJtG9iqDII+wpmKYZvQVgxPDN8DS\r\nNbsGSbQKvpxxihcYe8iyV5mteH7Ap1/1eVx+wqYTKsO3t46NmetgXiyfFD+y\r\nCg4YlNSVMNwbHa+BwOv3oTImN94NOV+ahD7WLnS8h4cxbC1XP4mEV0LNocCD\r\n3mOxNymO2gBbfg2LHf8rmF2PscvollbwtIJweDsOzmBa3V0TZvTgdEGONKoy\r\nqVvhtcPuR7k2Ra4xTmhIXQOkcG3j/phNcwjefP+zV/ioYes2A0AfPOnrQVmj\r\nX68JRkFJrfilMPI4gIaaihPIyNXpZtlIsrzBT480lpTsDD14Al90wUgnBhPl\r\ndnCh+hxNaDowEMb+BsgLm6zhn5tJfbQ4Ni0gKz4s0Dma7N47cMtcAtHN067J\r\nSeA7TXrmeqZNPP7tR9T46iUu+8SSaBp57Js=\r\n=TyiP\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"resolver": "ts-jest-resolver", "testMatch": ["**/test/*.test.(ts|js)"], "transform": {"^.+\\.(ts|tsx)$": ["ts-jest", {"useESM": true, "tsconfig": "./tsconfig.build.json"}]}, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.{ts,js}"], "moduleFileExtensions": ["ts", "js"], "extensionsToTreatAsEsm": [".ts"]}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=14.16"}, "exports": "./dist/index.js", "gitHead": "d198d93942a3a4c5060bf02956033bce6536efaf", "scripts": {"test": "xo && NODE_OPTIONS=--experimental-vm-modules jest --coverage ", "build": "tsc --project tsconfig.build.json", "clean": "rm -rf node_modules && rm -rf ./coverage && rm -rf ./package-lock.json && rm -rf ./test/testdb.sqlite && rm -rf ./dist", "prepare": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/cacheable-request.git", "type": "git"}, "_npmVersion": "8.19.1", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "18.9.0", "dependencies": {"keyv": "^4.5.2", "get-stream": "^6.0.1", "responselike": "^3.0.0", "normalize-url": "^8.0.0", "mimic-response": "^4.0.0", "http-cache-semantics": "^4.1.0", "@types/http-cache-semantics": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.53.1", "jest": "^29.3.1", "pify": "^6.1.0", "delay": "^5.0.0", "express": "^4.18.2", "sqlite3": "^5.1.4", "ts-jest": "^29.0.3", "ts-node": "^10.9.1", "typescript": "^4.9.4", "@types/jest": "^29.2.4", "@types/node": "^18.11.17", "body-parser": "^1.20.1", "@keyv/sqlite": "^3.6.4", "@types/delay": "^3.1.0", "@types/sqlite3": "^3.1.8", "ts-jest-resolver": "^2.0.0", "@types/get-stream": "^3.0.2", "eslint-plugin-jest": "^27.1.7", "@types/responselike": "^1.0.0"}, "dependenciesComments": {"@types/http-cache-semantics": "It needs to be in the dependencies list and not devDependencies because otherwise projects that use this one will be getting `Could not find a declaration file for module 'http-cache-semantics'` error when running `tsc`, see https://github.com/jaredwray/cacheable-request/issues/194 for details"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_10.2.5_1672956916946_0.9555858927133083", "host": "s3://npm-registry-packages"}}, "10.2.6": {"name": "cacheable-request", "version": "10.2.6", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@10.2.6", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "homepage": "https://github.com/jaredwray/cacheable-request#readme", "bugs": {"url": "https://github.com/jaredwray/cacheable-request/issues"}, "xo": {"rules": {"new-cap": 0, "n/prefer-global/url": 0, "@typescript-eslint/ban-types": 0, "@typescript-eslint/no-namespace": 0, "@typescript-eslint/no-unsafe-call": 0, "unicorn/no-abusive-eslint-disable": 0, "@typescript-eslint/no-unsafe-return": 0, "@typescript-eslint/no-unsafe-assignment": 0, "@typescript-eslint/no-implicit-any-catch": 0, "@typescript-eslint/restrict-plus-operands": 0, "@typescript-eslint/triple-slash-reference": 0, "@typescript-eslint/consistent-type-imports": 0, "@typescript-eslint/consistent-type-definitions": 0, "@typescript-eslint/restrict-template-expressions": 0}, "extends": ["plugin:jest/recommended"], "plugins": ["jest"]}, "dist": {"shasum": "68f252932f448bdf49ccd03d1daf5506912df7ba", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-10.2.6.tgz", "fileCount": 11, "integrity": "sha512-fhVLoXIFHvTizxQkAVohKPToSzdwzjrhL5SsjHT0umeSCxWeqJOS0oPqHg+yO1FPFST3VE5rxaqUvseyH9JHtg==", "signatures": [{"sig": "MEUCIQCi2ir7IPeP9lxqFhS3m35W5Ph5IiLB0vXPEbSWhD+mcQIgcQJN7NDIpoZ7C2z+P6eQSI8p02JnTG3br7dfuwutF3I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52911, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj2+qBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmploQ/9GfMT5h+Q1b4dsOqJQxCsPQFLGzUVK0wRRTxVbrV8y1ZzjEIB\r\nmBT/TRzb6rXu0iJeBaIu/kY0tsDJheu5equSrkPZekun0+kfX+4WckC8uGhT\r\np8GASojl+YOq7CCTGICQayeRks9G2YmHWT6E4HWJNwV7eP2BdV7+VoIRWsAn\r\n3MBAcj8LHF9JLXuU0MlYlLUD7RV9+Ysz8bqJrjNmes0IUBaJDFWMB30MhbpP\r\nZPmK2AnNyPTWYrHvzFWe1myIJynIvH8gAQxO6hXnA2QvHXun4acn8CDqS7ir\r\naVArBHD98rLuO3Wt+CEVLutscHeouWd/k/uXY3NOLmHpricPIK1Gu/3QBM4L\r\nGsDpHMSttSsWcdXe8nCu8VCuU1piYzOCbyZTsgFugPmhdwwYjCyS8nQkZZo9\r\nZcCLVlz0ey9VBsYkHthBATImYLw39/2P+5hpw7/hDba+i/pgJiySImJddER7\r\nUfE7GE4gDd3leCDJFmxHNcaiiu/ahU71W29P+ARXg+RUNItIZOSay14Uzf5/\r\nyqRmFfvEROkKd/sZLmnmJwz9ckO+vtABRZB+y0e9pbizLAsSBujh4Gh3uS//\r\nplgBmsJbiO7CyBaXP0IDb8oP9G2NzHcMhYNc9facEdMb+1HK5xNyD2H7z7rb\r\nUiFn1CBlyAfysfXAcW41/dgZBI31MYhiiww=\r\n=x6Y3\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"resolver": "ts-jest-resolver", "testMatch": ["**/test/*.test.(ts|js)"], "transform": {"^.+\\.(ts|tsx)$": ["ts-jest", {"useESM": true, "tsconfig": "./tsconfig.build.json"}]}, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.{ts,js}"], "moduleFileExtensions": ["ts", "js"], "extensionsToTreatAsEsm": [".ts"]}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=14.16"}, "exports": "./dist/index.js", "gitHead": "c4815689be40854a3d748a3927959354c09b0ebb", "scripts": {"test": "xo && NODE_OPTIONS=--experimental-vm-modules jest --coverage ", "build": "tsc --project tsconfig.build.json", "clean": "rm -rf node_modules && rm -rf ./coverage && rm -rf ./package-lock.json && rm -rf ./test/testdb.sqlite && rm -rf ./dist", "prepare": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/cacheable-request.git", "type": "git"}, "_npmVersion": "8.19.1", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "18.9.0", "dependencies": {"keyv": "^4.5.2", "get-stream": "^6.0.1", "responselike": "^3.0.0", "normalize-url": "^8.0.0", "mimic-response": "^4.0.0", "http-cache-semantics": "^4.1.0", "@types/http-cache-semantics": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.53.1", "jest": "^29.3.1", "pify": "^6.1.0", "delay": "^5.0.0", "express": "^4.18.2", "sqlite3": "^5.1.4", "ts-jest": "^29.0.5", "ts-node": "^10.9.1", "typescript": "^4.9.4", "@types/jest": "^29.2.6", "@types/node": "^18.11.18", "body-parser": "^1.20.1", "@keyv/sqlite": "^3.6.4", "@types/delay": "^3.1.0", "@types/sqlite3": "^3.1.8", "ts-jest-resolver": "^2.0.0", "@types/get-stream": "^3.0.2", "eslint-plugin-jest": "^27.2.1", "@types/responselike": "^1.0.0"}, "dependenciesComments": {"@types/http-cache-semantics": "It needs to be in the dependencies list and not devDependencies because otherwise projects that use this one will be getting `Could not find a declaration file for module 'http-cache-semantics'` error when running `tsc`, see https://github.com/jaredwray/cacheable-request/issues/194 for details"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_10.2.6_1675356801001_0.7696610040536145", "host": "s3://npm-registry-packages"}}, "10.2.7": {"name": "cacheable-request", "version": "10.2.7", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@10.2.7", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "homepage": "https://github.com/jaredwray/cacheable-request#readme", "bugs": {"url": "https://github.com/jaredwray/cacheable-request/issues"}, "xo": {"rules": {"new-cap": 0, "n/prefer-global/url": 0, "@typescript-eslint/ban-types": 0, "@typescript-eslint/no-namespace": 0, "@typescript-eslint/no-unsafe-call": 0, "unicorn/no-abusive-eslint-disable": 0, "@typescript-eslint/no-unsafe-return": 0, "@typescript-eslint/no-unsafe-assignment": 0, "@typescript-eslint/no-implicit-any-catch": 0, "@typescript-eslint/restrict-plus-operands": 0, "@typescript-eslint/triple-slash-reference": 0, "@typescript-eslint/consistent-type-imports": 0, "@typescript-eslint/consistent-type-definitions": 0, "@typescript-eslint/restrict-template-expressions": 0}, "extends": ["plugin:jest/recommended"], "plugins": ["jest"]}, "dist": {"shasum": "8bb8da66338f321b3cbbc34a71ac231178171bcc", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-10.2.7.tgz", "fileCount": 11, "integrity": "sha512-I4SA6mKgDxcxVbSt/UmIkb9Ny8qSkg6ReBHtAAXnZHk7KOSx5g3DTiAOaYzcHCs6oOdHn+bip9T48E6tMvK9hw==", "signatures": [{"sig": "MEQCICvxyg34GP2xv3OaebTmPwRecJ7OF97I3RT/iRgy/7v4AiAdxqXAGW2kD4W8ft2a6FqH5iicpKmgmoqcjZCIA69saQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52911, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4UKyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr7lw/+Jqp+Tga8HX+ZC7wU590VsxuX3FTglM+2SxGzMngugHyMKtsY\r\nUBHI9P8VJZPCWzNPSXBlqGlpyHOHIs6AD5q2hhGxZHK9DBxEi4HHv2pYGgne\r\nxcbytpxfScvP7yEKjOuVb2xHwLHOEAiRCBZ5o9NbIoX7HtzrBfUQ1DwZs2xP\r\noSNVtTkHvNpG2JRltuoD36GKHf/7gntyufLVO2XuecdnLfE4Q5wIb50zTAhI\r\ns/TQXPYhi+F5oBXlhqTLBb/UcHU0mPChEQdYy0Iq8gYxmDGoQy3R0zclgfjX\r\nMPRppMev2K4oUGR5L9lrIH1Hq07Mx4b1WZQYATZAC5OL8An2Gql2ccvE2GXY\r\n+GN8ehHN/tvvZZK9kI0HsNJqlPP2gJVkKYs/vD72k3BoH6M/MYNRrKUArOIQ\r\nyBHVx4uLTcsO6qax9m+4/ifVvKCfCZUcDhzh3TDn3nDaQL1IkQwqQxxPrEZt\r\nzWwzBixd0wWV0kxzPx5bexNHJTUsUlK/swgcWaUYc0cwDIy8olNvQ4Wzy3Lo\r\nE+FyFtEzb6ZvjYhaVOIohGJKsnFMogkmd1eoZrw/Di9ouEycTxtx1RS0mJpg\r\nvtljHN9E6xbvIKrPC8dIbn61n9cL7SKP77UpWtx7R8rTOkv6zU5K3tzpFo/K\r\nKeoaT4qn/MaLe0rngTRj2rzlR4UCSBfERz0=\r\n=dsIZ\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"resolver": "ts-jest-resolver", "testMatch": ["**/test/*.test.(ts|js)"], "transform": {"^.+\\.(ts|tsx)$": ["ts-jest", {"useESM": true, "tsconfig": "./tsconfig.build.json"}]}, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.{ts,js}"], "moduleFileExtensions": ["ts", "js"], "extensionsToTreatAsEsm": [".ts"]}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=14.16"}, "exports": "./dist/index.js", "gitHead": "8fdaf8b4838dc34e5898aafe47253db47968aedd", "scripts": {"test": "xo && NODE_OPTIONS=--experimental-vm-modules jest --coverage ", "build": "tsc --project tsconfig.build.json", "clean": "rm -rf node_modules && rm -rf ./coverage && rm -rf ./package-lock.json && rm -rf ./test/testdb.sqlite && rm -rf ./dist", "prepare": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/cacheable-request.git", "type": "git"}, "_npmVersion": "8.19.1", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "18.9.0", "dependencies": {"keyv": "^4.5.2", "get-stream": "^6.0.1", "responselike": "^3.0.0", "normalize-url": "^8.0.0", "mimic-response": "^4.0.0", "http-cache-semantics": "^4.1.1", "@types/http-cache-semantics": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.53.1", "jest": "^29.3.1", "pify": "^6.1.0", "delay": "^5.0.0", "express": "^4.18.2", "sqlite3": "^5.1.4", "ts-jest": "^29.0.5", "ts-node": "^10.9.1", "typescript": "^4.9.4", "@types/jest": "^29.2.6", "@types/node": "^18.11.18", "body-parser": "^1.20.1", "@keyv/sqlite": "^3.6.4", "@types/delay": "^3.1.0", "@types/sqlite3": "^3.1.8", "ts-jest-resolver": "^2.0.0", "@types/get-stream": "^3.0.2", "eslint-plugin-jest": "^27.2.1", "@types/responselike": "^1.0.0"}, "dependenciesComments": {"@types/http-cache-semantics": "It needs to be in the dependencies list and not devDependencies because otherwise projects that use this one will be getting `Could not find a declaration file for module 'http-cache-semantics'` error when running `tsc`, see https://github.com/jaredwray/cacheable-request/issues/194 for details"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_10.2.7_1675707058335_0.5039335292812048", "host": "s3://npm-registry-packages"}}, "10.2.8": {"name": "cacheable-request", "version": "10.2.8", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@10.2.8", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "homepage": "https://github.com/jaredwray/cacheable-request#readme", "bugs": {"url": "https://github.com/jaredwray/cacheable-request/issues"}, "xo": {"rules": {"new-cap": 0, "n/prefer-global/url": 0, "@typescript-eslint/ban-types": 0, "@typescript-eslint/no-namespace": 0, "@typescript-eslint/no-unsafe-call": 0, "unicorn/no-abusive-eslint-disable": 0, "@typescript-eslint/no-unsafe-return": 0, "@typescript-eslint/no-unsafe-assignment": 0, "@typescript-eslint/no-implicit-any-catch": 0, "@typescript-eslint/restrict-plus-operands": 0, "@typescript-eslint/triple-slash-reference": 0, "@typescript-eslint/consistent-type-imports": 0, "@typescript-eslint/consistent-type-definitions": 0, "@typescript-eslint/restrict-template-expressions": 0}, "extends": ["plugin:jest/recommended"], "plugins": ["jest"]}, "dist": {"shasum": "899ae6c0c8c7127f263b2005ecaac07c95124079", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-10.2.8.tgz", "fileCount": 11, "integrity": "sha512-IDVO5MJ4LItE6HKFQTqT2ocAQsisOoCTUDu1ddCmnhyiwFQjXNPp4081Xj23N4tO+AFEFNzGuNEf/c8Gwwt15A==", "signatures": [{"sig": "MEQCIFNMW9pdUPituYRKZTV2qLI6guCD829VAs1kUBcVwTyjAiAys746hVVLhIEacibxnrWTfLLVV4bDr65dQEAabQCRKw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52910, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj973PACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqNUhAAlPK4ZQtV8VEYHjK4IXiveiIRV/7CcxTZbAVs/tZSbbKhqD1z\r\nwLuQApzaJIZKkz1WTXrdh79HmE9olVPQCqSmygpaJOBOIyLw/JKSz9LThAGZ\r\nsovp1HGbqbVMi0jHKFR62G8VYhiat18THMlK8EfhzJ5/6vKnrNSFuZflcMfF\r\nDFR+JNhF5pBktbPTbliimdSA5vFDxGUYjyFRUm1jSSpmniuda4ilkc8NlVjp\r\nOK0FNwwJC6hX0K386SPEEhvtzQJ2TCr68bzoq6xr11wXOpP65wq1vMSpP6Hq\r\niWOSk0rMGTrmFTqhitVEeGn1aNlz8Ryng+yK6P0IdZKyPcEApFa8GTy7pdQQ\r\nWSqn2zhDq7VtVyWZROpGdDDlrIzX6fbQHsXleUnSoWtixoHzmlDUERnSfkvg\r\nHg5k43b6f+wWwBnWOYEx9pf7B7dk24J2q6dgVm2cHtfvyetzqkGchlFgp+YK\r\n/sus6T6gXLB1+nDBkpGzahqzfTBfcbrtazYqv0hzfAQ5Hsthx2e/kdNL7YhD\r\np0pjyN5jRW4HFAPkC+5Wh3A+rfRSXWAcLlz121j/bM6gYZCYrfJpEY4cpeAV\r\nmgJgqWeWtegv9vZ0uRQBPCy6tHUM1A5SInHp8JAgsiccdLTnI3ML2kDFeIYb\r\nGTJl9IpWnxyqh8YD0mI/FV1lUkUz6iU/ysk=\r\n=L6MO\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"resolver": "ts-jest-resolver", "testMatch": ["**/test/*.test.(ts|js)"], "transform": {"^.+\\.(ts|tsx)$": ["ts-jest", {"useESM": true, "tsconfig": "./tsconfig.build.json"}]}, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.{ts,js}"], "moduleFileExtensions": ["ts", "js"], "extensionsToTreatAsEsm": [".ts"]}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=14.16"}, "exports": "./dist/index.js", "gitHead": "d7d1011cb140d958535f391390465b6dcdbe1aa0", "scripts": {"test": "xo && NODE_OPTIONS=--experimental-vm-modules jest --coverage ", "build": "tsc --project tsconfig.build.json", "clean": "rm -rf node_modules && rm -rf ./coverage && rm -rf ./package-lock.json && rm -rf ./test/testdb.sqlite && rm -rf ./dist", "prepare": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/cacheable-request.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "18.11.0", "dependencies": {"keyv": "^4.5.2", "get-stream": "^6.0.1", "responselike": "^3.0.0", "normalize-url": "^8.0.0", "mimic-response": "^4.0.0", "http-cache-semantics": "^4.1.1", "@types/http-cache-semantics": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.53.1", "jest": "^29.4.3", "pify": "^6.1.0", "delay": "^5.0.0", "express": "^4.18.2", "sqlite3": "^5.1.4", "ts-jest": "^29.0.5", "ts-node": "^10.9.1", "typescript": "^4.9.5", "@types/jest": "^29.4.0", "@types/node": "^18.14.1", "body-parser": "^1.20.2", "@keyv/sqlite": "^3.6.4", "@types/delay": "^3.1.0", "@types/sqlite3": "^3.1.8", "ts-jest-resolver": "^2.0.0", "@types/get-stream": "^3.0.2", "eslint-plugin-jest": "^27.2.1", "@types/responselike": "^1.0.0"}, "dependenciesComments": {"@types/http-cache-semantics": "It needs to be in the dependencies list and not devDependencies because otherwise projects that use this one will be getting `Could not find a declaration file for module 'http-cache-semantics'` error when running `tsc`, see https://github.com/jaredwray/cacheable-request/issues/194 for details"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_10.2.8_1677180367014_0.21076079493241995", "host": "s3://npm-registry-packages"}}, "10.2.9": {"name": "cacheable-request", "version": "10.2.9", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@10.2.9", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "homepage": "https://github.com/jaredwray/cacheable-request#readme", "bugs": {"url": "https://github.com/jaredwray/cacheable-request/issues"}, "xo": {"rules": {"new-cap": 0, "n/prefer-global/url": 0, "@typescript-eslint/ban-types": 0, "@typescript-eslint/no-namespace": 0, "@typescript-eslint/no-unsafe-call": 0, "unicorn/no-abusive-eslint-disable": 0, "@typescript-eslint/no-unsafe-return": 0, "@typescript-eslint/no-unsafe-assignment": 0, "@typescript-eslint/no-implicit-any-catch": 0, "@typescript-eslint/restrict-plus-operands": 0, "@typescript-eslint/triple-slash-reference": 0, "@typescript-eslint/consistent-type-imports": 0, "@typescript-eslint/consistent-type-definitions": 0, "@typescript-eslint/restrict-template-expressions": 0}, "extends": ["plugin:jest/recommended"], "plugins": ["jest"]}, "dist": {"shasum": "6375833d2b99921d8870df9fdc26cb703c56f356", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-10.2.9.tgz", "fileCount": 11, "integrity": "sha512-CaAMr53AS1Tb9evO1BIWFnZjSr8A4pbXofpsNVWPMDZZj3ZQKHwsQG9BrTqQ4x5ZYJXz1T2b8LLtTZODxSpzbg==", "signatures": [{"sig": "MEQCIDDK2uvg3DZZ4PcWM+Dm0GNUqzC1zjleuTNCmL5wD2/6AiAP2+cXH7vs8V833ZPWWUVpFpgAnI+Zk6BDYXXVRspwZQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52911, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkH6pJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq9pA//elfPUP2mzqIMEDTkzORdTxS1LBgKPYizziaLF9K9eXM9vgMS\r\nontW1QupnZqZqGnMo3rgA3N/XqWo8zVmKgv+021u42EZNxgRDmhFLOZGm99P\r\n82p2RvbV86e7IUlV+f5s4xYVuc/xLzBy3ZKLnRNUbOk3fV7wVt5UOxegIsJJ\r\nVeVGSnC1oCFas0qObvQTCVguEB0ZTFfQ18xeucqnlpovLzXmv9QsYAl6gL+H\r\n0DRclrC0Dt3z54Ui6WkNIcFSpILnjtg7CV8NBa3UkAaFe32PZQs5RogjTCnQ\r\n5B48Ip9shCRt6Nqb7DyTp9OanoMUiPc21cGqKbyA4Igal/Qw3emcfP/kitYh\r\nm/TDKsW5VVklHALB0LNulErQAFoMVF8gc08TFqgP+yZlGRqlJo2xfbLCfM5g\r\nxKRFhSf5Jr0mezzg+g20IWp05RpjfPkBByJGU/guB5ko9PsuvMbURifdQyPc\r\nRSVZ1Gb7l+7dkhyYjbXKNGxCOxM5dyDiIsBBIGxnSTyL+O5LdobTaWVoyF6P\r\nghfipL7snFWD/csjp3aQD/vTZW3OIa8VEVLjLSZGJt28yhg20NVi3IOByqEr\r\nk/7q9kaM/nbnlzEeGLjamXnCspDF1WjL4BoGh2xB9U0BU4trsbRWp+BFGBiz\r\n46ut23R+A2sdyG/Zv81l3r2VT/+DVvNNG/I=\r\n=6vLa\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"resolver": "ts-jest-resolver", "testMatch": ["**/test/*.test.(ts|js)"], "transform": {"^.+\\.(ts|tsx)$": ["ts-jest", {"useESM": true, "tsconfig": "./tsconfig.build.json"}]}, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.{ts,js}"], "moduleFileExtensions": ["ts", "js"], "extensionsToTreatAsEsm": [".ts"]}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=14.16"}, "exports": "./dist/index.js", "gitHead": "1e4ae90dc94f1609f0ece162c68c785093343d56", "scripts": {"test": "xo && NODE_OPTIONS=--experimental-vm-modules jest --coverage ", "build": "tsc --project tsconfig.build.json", "clean": "rm -rf node_modules && rm -rf ./coverage && rm -rf ./package-lock.json && rm -rf ./test/testdb.sqlite && rm -rf ./dist", "prepare": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/cacheable-request.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "18.11.0", "dependencies": {"keyv": "^4.5.2", "get-stream": "^6.0.1", "responselike": "^3.0.0", "normalize-url": "^8.0.0", "mimic-response": "^4.0.0", "http-cache-semantics": "^4.1.1", "@types/http-cache-semantics": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.53.1", "jest": "^29.5.0", "pify": "^6.1.0", "delay": "^5.0.0", "express": "^4.18.2", "sqlite3": "^5.1.6", "ts-jest": "^29.0.5", "ts-node": "^10.9.1", "typescript": "^5.0.2", "@types/jest": "^29.5.0", "@types/node": "^18.15.10", "body-parser": "^1.20.2", "@keyv/sqlite": "^3.6.5", "@types/delay": "^3.1.0", "@types/sqlite3": "^3.1.8", "ts-jest-resolver": "^2.0.1", "@types/get-stream": "^3.0.2", "eslint-plugin-jest": "^27.2.1", "@types/responselike": "^1.0.0"}, "dependenciesComments": {"@types/http-cache-semantics": "It needs to be in the dependencies list and not devDependencies because otherwise projects that use this one will be getting `Could not find a declaration file for module 'http-cache-semantics'` error when running `tsc`, see https://github.com/jaredwray/cacheable-request/issues/194 for details"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_10.2.9_1679796809068_0.8211888084887531", "host": "s3://npm-registry-packages"}}, "10.2.10": {"name": "cacheable-request", "version": "10.2.10", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@10.2.10", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "homepage": "https://github.com/jaredwray/cacheable-request#readme", "bugs": {"url": "https://github.com/jaredwray/cacheable-request/issues"}, "xo": {"rules": {"new-cap": 0, "n/prefer-global/url": 0, "@typescript-eslint/ban-types": 0, "@typescript-eslint/no-namespace": 0, "@typescript-eslint/no-unsafe-call": 0, "unicorn/no-abusive-eslint-disable": 0, "@typescript-eslint/no-unsafe-return": 0, "@typescript-eslint/no-unsafe-argument": 0, "@typescript-eslint/no-unsafe-assignment": 0, "@typescript-eslint/no-implicit-any-catch": 0, "@typescript-eslint/restrict-plus-operands": 0, "@typescript-eslint/triple-slash-reference": 0, "@typescript-eslint/consistent-type-imports": 0, "@typescript-eslint/prefer-nullish-coalescing": 0, "@typescript-eslint/consistent-type-definitions": 0, "@typescript-eslint/restrict-template-expressions": 0}, "extends": ["plugin:jest/recommended"], "plugins": ["jest"]}, "dist": {"shasum": "1785984a9a4ddec8dd01792232cca474be49a8af", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-10.2.10.tgz", "fileCount": 11, "integrity": "sha512-v6WB+Epm/qO4Hdlio/sfUn69r5Shgh39SsE9DSd4bIezP0mblOlObI+I0kUEM7J0JFc+I7pSeMeYaOYtX1N/VQ==", "signatures": [{"sig": "MEQCIHibQYFoDAETQ04hyJ+GRUNahEx6g1dGKuIOI+JryPwWAiBfhUANgxLK7rKVSgAcVZkpR5whFd7xcKGU2iMEglUJyA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52998, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkR/W5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmokfhAAkT5Ugo+Lj0f61jqwHz5iaFaZPw44fqBykQD6W3+88e1RWRJk\r\npfEGLheIJbVQsO7ve3wlfY3H9ieE/2WncmcyaZALSg1eagSP1SOMsg5zO+Wo\r\nwy2iY0HDsfT/V5i8gUxxpYoUz3/0guQaxj2cNqQPljGmPeOjOKzd69JHY1SB\r\nFdvIGiYJZ67HIJkM7hnV9ZhMubHD+6eN1fKKw8OHovlqC4I/UTsQRXBPUzvO\r\n6Q9PkLuTyTpJI2Dvbuy+OnVCUOyDOwWUvA3sHDpHXa7o4wt6mbfhUf2PLB1v\r\nMoC9z7/4W/cVzxxR9CSSNwm8j1NyJT/5dDbv9Z3bxou/qlFurlBWz7NOwIFe\r\n0qNu92eMxO2LvEBYjWi3f2aruUblwB2s0+oj+CIcNp9BwKbKcVfo0RtjS6pC\r\nawgPZWBNlT0HQhvmqrpCBZkMIMITqOiwsAXY0KexJEQ1K+og3hYw7S8UyPyo\r\n3xciytrx8FiJ6BWBW/mkmCIeBudrleVTAH8skmeO8I7/IAyjpJDiqVhMp8mm\r\nlfWLZLK4mo3fm0NBosPUptz6sPG4Xwy6RyOK3uiSlGIK5CcNEzPKHHQDRLny\r\nshRvyH/OM8CAjyHg2j8VVydBISXRM9Eqs02E2xV689HbqVJAKyPOd9ucZ0nU\r\nIywgFxcaHKsMZR/60M4fxp+4IeQ+HWSpFE0=\r\n=SlRU\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"resolver": "ts-jest-resolver", "testMatch": ["**/test/*.test.(ts|js)"], "transform": {"^.+\\.(ts|tsx)$": ["ts-jest", {"useESM": true, "tsconfig": "./tsconfig.build.json"}]}, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.{ts,js}"], "moduleFileExtensions": ["ts", "js"], "extensionsToTreatAsEsm": [".ts"]}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=14.16"}, "exports": "./dist/index.js", "gitHead": "7c8f7fdc83108ccb7bb3f206264e40a26315fc2b", "scripts": {"test": "xo && NODE_OPTIONS=--experimental-vm-modules jest --coverage ", "build": "tsc --project tsconfig.build.json", "clean": "rm -rf node_modules && rm -rf ./coverage && rm -rf ./package-lock.json && rm -rf ./test/testdb.sqlite && rm -rf ./dist", "prepare": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/cacheable-request.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "18.9.0", "dependencies": {"keyv": "^4.5.2", "get-stream": "^6.0.1", "responselike": "^3.0.0", "normalize-url": "^8.0.0", "mimic-response": "^4.0.0", "http-cache-semantics": "^4.1.1", "@types/http-cache-semantics": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.54.1", "jest": "^29.5.0", "pify": "^6.1.0", "delay": "^5.0.0", "express": "^4.18.2", "sqlite3": "^5.1.6", "ts-jest": "^29.1.0", "ts-node": "^10.9.1", "typescript": "^5.0.4", "@types/jest": "^29.5.1", "@types/node": "^18.16.0", "body-parser": "^1.20.2", "@keyv/sqlite": "^3.6.5", "@types/delay": "^3.1.0", "@types/sqlite3": "^3.1.8", "ts-jest-resolver": "^2.0.1", "@types/get-stream": "^3.0.2", "eslint-plugin-jest": "^27.2.1", "@types/responselike": "^1.0.0"}, "dependenciesComments": {"@types/http-cache-semantics": "It needs to be in the dependencies list and not devDependencies because otherwise projects that use this one will be getting `Could not find a declaration file for module 'http-cache-semantics'` error when running `tsc`, see https://github.com/jaredwray/cacheable-request/issues/194 for details"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_10.2.10_1682437560911_0.708036206753873", "host": "s3://npm-registry-packages"}}, "7.0.3": {"name": "cacheable-request", "version": "7.0.3", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@7.0.3", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "homepage": "https://github.com/jaredwray/cacheable-request#readme", "bugs": {"url": "https://github.com/jaredwray/cacheable-request/issues"}, "xo": {"rules": {"new-cap": 0, "n/prefer-global/url": 0, "@typescript-eslint/ban-types": 0, "@typescript-eslint/await-thenable": 0, "@typescript-eslint/no-unsafe-call": 0, "@typescript-eslint/no-unsafe-return": 0, "@typescript-eslint/naming-convention": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-unsafe-argument": 0, "@typescript-eslint/no-floating-promises": 0, "@typescript-eslint/no-unsafe-assignment": 0, "@typescript-eslint/object-curly-spacing": 0, "@typescript-eslint/restrict-plus-operands": 0, "@typescript-eslint/restrict-template-expressions": 0}, "extends": ["plugin:jest/recommended"], "plugins": ["jest"]}, "dist": {"shasum": "a02c49f32bd3251716e8965fbddaab4294ee8a3d", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-7.0.3.tgz", "fileCount": 11, "integrity": "sha512-zf+A5pbRvok+dr02YT1HqXRa/8Icl48/2Ng0kxgOqZ7U2MXvcPWxduw8DSAtOR+OOM+hfZofxf6E0lDbvQ9lWQ==", "signatures": [{"sig": "MEUCIQDmm61E+CeeCVes62AX0LcN84zB9sRr6xAm2ia+YQhyJwIgDF+d8uWWJ3s3xG+GKMMqnsDxZ8GVO4KBhLI51hpyJIo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44550}, "jest": {"globals": {"ts-jest": {"tsconfig": "./tsconfig.json"}}, "testMatch": ["**/test/*.test.(ts)"], "transform": {"^.+\\.(ts|tsx)$": "ts-jest"}, "testEnvironment": "node", "moduleFileExtensions": ["ts", "js"]}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "engines": {"node": ">=14.16"}, "gitHead": "733a3fb59f746fba65f2a475a62dbd92d6fa509f", "scripts": {"test": "xo --fix && jest --coverage", "build": "tsc --project tsconfig.build.json", "clean": "rm -rf node_modules && rm -rf ./coverage && rm -rf ./package-lock.json && rm -rf ./test/testdb.sqlite && rm -rf ./dist"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/cacheable-request.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "18.9.0", "dependencies": {"keyv": "^4.5.2", "get-stream": "^6.0.1", "responselike": "^2.0.0", "normalize-url": "^6.0.3", "mimic-response": "^3.1.0", "http-cache-semantics": "^4.1.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"xo": "^0.54.2", "jest": "^29.5.0", "pify": "^5.0.0", "delay": "^5.0.0", "sqlite3": "^5.1.6", "ts-jest": "^29.1.0", "ts-node": "^10.9.1", "typescript": "^4.7.4", "@babel/core": "^7.21.8", "@types/jest": "^29.5.1", "@types/node": "^18.0.0", "@types/pify": "^5.0.1", "@keyv/sqlite": "^3.6.5", "@types/delay": "^3.1.0", "@types/sqlite3": "^3.1.8", "@babel/preset-env": "^7.21.5", "@types/get-stream": "^3.0.2", "create-test-server": "3.0.1", "eslint-plugin-jest": "^27.2.1", "@types/responselike": "^1.0.0", "@types/create-test-server": "^3.0.1", "@types/http-cache-semantics": "^4.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_7.0.3_1686165085198_0.519093474788958", "host": "s3://npm-registry-packages"}}, "7.0.4": {"name": "cacheable-request", "version": "7.0.4", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@7.0.4", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "homepage": "https://github.com/lukechilds/cacheable-request#readme", "bugs": {"url": "https://github.com/lukechilds/cacheable-request/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "7a33ebf08613178b403635be7b899d3e69bbe817", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-7.0.4.tgz", "fileCount": 4, "integrity": "sha512-v+p6ongsrp0yTGbJXjgxPow2+DL93DASP4kXCDKb8/bwRtt9OEF3whggkkDkGNzgcWy2XaF4a8nZglC7uElscg==", "signatures": [{"sig": "MEUCIB198xX4hbMSrFXdUzyfCKcSrrgxad9hL/P6n3V8vipYAiEAnLySH7W4q2CWXyUDf9HIG4ltzVE/308PUDWwEN3/fjA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16750}, "main": "src/index.js", "engines": {"node": ">=8"}, "gitHead": "83a55c364f44a9f3bffecdcf052545632d722047", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/cacheable-request.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "12.22.12", "dependencies": {"keyv": "^4.0.0", "get-stream": "^5.1.0", "responselike": "^2.0.0", "normalize-url": "^6.0.1", "clone-response": "^1.0.2", "lowercase-keys": "^2.0.0", "http-cache-semantics": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.23.0", "ava": "^1.1.0", "nyc": "^14.1.1", "pify": "^4.0.0", "this": "^1.0.2", "delay": "^4.0.0", "sqlite3": "^4.0.2", "coveralls": "^3.0.0", "@keyv/sqlite": "^2.0.0", "create-test-server": "3.0.0", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_7.0.4_1686181616441_0.46042546470415324", "host": "s3://npm-registry-packages"}}, "10.2.11": {"name": "cacheable-request", "version": "10.2.11", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@10.2.11", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "homepage": "https://github.com/jaredwray/cacheable-request#readme", "bugs": {"url": "https://github.com/jaredwray/cacheable-request/issues"}, "xo": {"rules": {"new-cap": 0, "n/prefer-global/url": 0, "@typescript-eslint/ban-types": 0, "@typescript-eslint/no-namespace": 0, "@typescript-eslint/no-unsafe-call": 0, "unicorn/no-abusive-eslint-disable": 0, "@typescript-eslint/no-unsafe-return": 0, "@typescript-eslint/no-unsafe-argument": 0, "@typescript-eslint/no-unsafe-assignment": 0, "@typescript-eslint/no-implicit-any-catch": 0, "@typescript-eslint/restrict-plus-operands": 0, "@typescript-eslint/triple-slash-reference": 0, "@typescript-eslint/consistent-type-imports": 0, "@typescript-eslint/prefer-nullish-coalescing": 0, "@typescript-eslint/consistent-type-definitions": 0, "@typescript-eslint/restrict-template-expressions": 0}, "extends": ["plugin:jest/recommended"], "plugins": ["jest"]}, "dist": {"shasum": "c2d13a8cef0e4ad8022df761dfac9bb244cf897b", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-10.2.11.tgz", "fileCount": 11, "integrity": "sha512-kn0t0oJnlFo1Nzl/AYQzS/oByMtmaqLasFUa7MUMsiTrIHy8TxSkx2KzWCybE3Nuz1F4sJRGnLAfUGsPe47viQ==", "signatures": [{"sig": "MEUCIQDZ2O1N15yG+Rd3tO5CJeZf2VcswDZUZ7lQs9wNeyiAegIgHJvyU5NApTHkq3iOit5iZVJK/V/l1Lm9opRMM5w5WHA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53014}, "jest": {"resolver": "ts-jest-resolver", "testMatch": ["**/test/*.test.(ts|js)"], "transform": {"^.+\\.(ts|tsx)$": ["ts-jest", {"useESM": true, "tsconfig": "./tsconfig.build.json"}]}, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.{ts,js}"], "moduleFileExtensions": ["ts", "js"], "extensionsToTreatAsEsm": [".ts"]}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=14.16"}, "exports": "./dist/index.js", "gitHead": "92b2e1558c21e36729e1a7817478eb510621764b", "scripts": {"test": "xo && NODE_OPTIONS=--experimental-vm-modules jest --coverage ", "build": "tsc --project tsconfig.build.json", "clean": "rm -rf node_modules && rm -rf ./coverage && rm -rf ./package-lock.json && rm -rf ./test/testdb.sqlite && rm -rf ./dist", "prepare": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/cacheable-request.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "18.11.0", "dependencies": {"keyv": "^4.5.2", "get-stream": "^7.0.0", "responselike": "^3.0.0", "normalize-url": "^8.0.0", "mimic-response": "^4.0.0", "http-cache-semantics": "^4.1.1", "@types/http-cache-semantics": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.54.2", "jest": "^29.5.0", "pify": "^6.1.0", "delay": "^6.0.0", "express": "^4.18.2", "sqlite3": "^5.1.6", "ts-jest": "^29.1.0", "ts-node": "^10.9.1", "typescript": "^5.1.3", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "body-parser": "^1.20.2", "@keyv/sqlite": "^3.6.5", "@types/delay": "^3.1.0", "@types/sqlite3": "^3.1.8", "ts-jest-resolver": "^2.0.1", "@types/get-stream": "^3.0.2", "eslint-plugin-jest": "^27.2.2", "@types/responselike": "^1.0.0"}, "dependenciesComments": {"@types/http-cache-semantics": "It needs to be in the dependencies list and not devDependencies because otherwise projects that use this one will be getting `Could not find a declaration file for module 'http-cache-semantics'` error when running `tsc`, see https://github.com/jaredwray/cacheable-request/issues/194 for details"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_10.2.11_1687282605331_0.6813462207663854", "host": "s3://npm-registry-packages"}}, "10.2.12": {"name": "cacheable-request", "version": "10.2.12", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@10.2.12", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "homepage": "https://github.com/jaredwray/cacheable-request#readme", "bugs": {"url": "https://github.com/jaredwray/cacheable-request/issues"}, "xo": {"rules": {"new-cap": 0, "n/prefer-global/url": 0, "@typescript-eslint/ban-types": 0, "@typescript-eslint/no-namespace": 0, "@typescript-eslint/no-unsafe-call": 0, "unicorn/no-abusive-eslint-disable": 0, "@typescript-eslint/no-unsafe-return": 0, "@typescript-eslint/no-unsafe-argument": 0, "@typescript-eslint/no-unsafe-assignment": 0, "@typescript-eslint/no-implicit-any-catch": 0, "@typescript-eslint/restrict-plus-operands": 0, "@typescript-eslint/triple-slash-reference": 0, "@typescript-eslint/consistent-type-imports": 0, "@typescript-eslint/prefer-nullish-coalescing": 0, "@typescript-eslint/consistent-type-definitions": 0, "@typescript-eslint/restrict-template-expressions": 0}, "extends": ["plugin:jest/recommended"], "plugins": ["jest"]}, "dist": {"shasum": "05b97a3199d1ee65c360eb45c5af6191faa3ab6b", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-10.2.12.tgz", "fileCount": 11, "integrity": "sha512-qtWGB5kn2OLjx47pYUkWicyOpK1vy9XZhq8yRTXOy+KAmjjESSRLx6SiExnnaGGUP1NM6/vmygMu0fGylNh9tw==", "signatures": [{"sig": "MEQCIGmkkXSmi9GUR6i2DcL7/8vT8gzl68mZhjLcJuh02wXuAiAUJMIlwHI6e+r68DDdK2SNx4DkUVj/XjkqxyVekRU1PA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52997}, "jest": {"resolver": "ts-jest-resolver", "testMatch": ["**/test/*.test.(ts|js)"], "transform": {"^.+\\.(ts|tsx)$": ["ts-jest", {"useESM": true, "tsconfig": "./tsconfig.build.json"}]}, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.{ts,js}"], "moduleFileExtensions": ["ts", "js"], "extensionsToTreatAsEsm": [".ts"]}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=14.16"}, "exports": "./dist/index.js", "gitHead": "4896ac3fb7f4e8d66694fc2335243853359dfbfa", "scripts": {"test": "xo && NODE_OPTIONS=--experimental-vm-modules jest --coverage ", "build": "tsc --project tsconfig.build.json", "clean": "rm -rf node_modules && rm -rf ./coverage && rm -rf ./package-lock.json && rm -rf ./test/testdb.sqlite && rm -rf ./dist", "prepare": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/cacheable-request.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "18.11.0", "dependencies": {"keyv": "^4.5.2", "get-stream": "^6.0.1", "responselike": "^3.0.0", "normalize-url": "^8.0.0", "mimic-response": "^4.0.0", "http-cache-semantics": "^4.1.1", "@types/http-cache-semantics": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.54.2", "jest": "^29.5.0", "pify": "^6.1.0", "delay": "^6.0.0", "express": "^4.18.2", "sqlite3": "^5.1.6", "ts-jest": "^29.1.0", "ts-node": "^10.9.1", "typescript": "^5.1.3", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "body-parser": "^1.20.2", "@keyv/sqlite": "^3.6.5", "@types/delay": "^3.1.0", "@types/sqlite3": "^3.1.8", "ts-jest-resolver": "^2.0.1", "@types/get-stream": "^3.0.2", "eslint-plugin-jest": "^27.2.2", "@types/responselike": "^1.0.0"}, "dependenciesComments": {"@types/http-cache-semantics": "It needs to be in the dependencies list and not devDependencies because otherwise projects that use this one will be getting `Could not find a declaration file for module 'http-cache-semantics'` error when running `tsc`, see https://github.com/jaredwray/cacheable-request/issues/194 for details"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_10.2.12_1687440672922_0.39758532631899857", "host": "s3://npm-registry-packages"}}, "10.2.13": {"name": "cacheable-request", "version": "10.2.13", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@10.2.13", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "xo": {"rules": {"new-cap": 0, "n/prefer-global/url": 0, "unicorn/prefer-event-target": 0, "@typescript-eslint/ban-types": 0, "@typescript-eslint/no-namespace": 0, "@typescript-eslint/no-unsafe-call": 0, "unicorn/no-abusive-eslint-disable": 0, "@typescript-eslint/no-unsafe-return": 0, "@typescript-eslint/no-unsafe-argument": 0, "@typescript-eslint/no-unsafe-assignment": 0, "@typescript-eslint/no-implicit-any-catch": 0, "@typescript-eslint/restrict-plus-operands": 0, "@typescript-eslint/triple-slash-reference": 0, "@typescript-eslint/consistent-type-imports": 0, "@typescript-eslint/prefer-nullish-coalescing": 0, "@typescript-eslint/consistent-type-definitions": 0, "@typescript-eslint/restrict-template-expressions": 0}, "extends": ["plugin:jest/recommended"], "plugins": ["jest"]}, "dist": {"shasum": "b7012bb4a2acdb18cb54d2dff751d766b3500842", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-10.2.13.tgz", "fileCount": 12, "integrity": "sha512-3SD4rrMu1msNGEtNSt8Od6enwdo//U9s4ykmXfA2TD58kcLkCobtCDiby7kNyj7a/Q7lz/mAesAFI54rTdnvBA==", "signatures": [{"sig": "MEUCIQCv30vl3cKoAIEYnH1UbXGMOXjKn//L0C1e16A4KCpkLAIgO01TK5nF8gVJ2Wvg5T3u4QTy6NZf5Q3J/qvq2m/7Icc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69074}, "jest": {"resolver": "ts-jest-resolver", "testMatch": ["**/test/*.test.(ts|js)"], "transform": {"^.+\\.(ts|tsx)$": ["ts-jest", {"useESM": true, "tsconfig": "./tsconfig.build.json"}]}, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.{ts,js}"], "moduleFileExtensions": ["ts", "js"], "extensionsToTreatAsEsm": [".ts"]}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=14.16"}, "exports": "./dist/index.js", "scripts": {"test": "xo && NODE_OPTIONS=--experimental-vm-modules jest --coverage ", "build": "tsc --project tsconfig.build.json", "clean": "rm -rf node_modules && rm -rf ./coverage && rm -rf ./test/testdb.sqlite && rm -rf ./dist", "prepare": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jaredwray/cacheable.git", "type": "git"}, "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "dependencies": {"keyv": "^4.5.3", "get-stream": "^6.0.1", "responselike": "^3.0.0", "normalize-url": "^8.0.0", "mimic-response": "^4.0.0", "http-cache-semantics": "^4.1.1", "@types/http-cache-semantics": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.55.0", "jest": "^29.6.1", "pify": "^6.1.0", "delay": "^6.0.0", "eslint": "^8.45.0", "express": "^4.18.2", "sqlite3": "^5.1.6", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.1.6", "@types/jest": "^29.5.3", "@types/node": "^20.4.5", "body-parser": "^1.20.2", "@keyv/sqlite": "^3.6.5", "@types/sqlite3": "^3.1.8", "ts-jest-resolver": "^2.0.1", "eslint-plugin-jest": "^27.2.3", "@types/responselike": "^1.0.0"}, "dependenciesComments": {"@types/http-cache-semantics": "It needs to be in the dependencies list and not devDependencies because otherwise projects that use this one will be getting `Could not find a declaration file for module 'http-cache-semantics'` error when running `tsc`, see https://github.com/jaredwray/cacheable-request/issues/194 for details"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_10.2.13_1690347270440_0.8989823917562003", "host": "s3://npm-registry-packages"}}, "10.2.14": {"name": "cacheable-request", "version": "10.2.14", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@10.2.14", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "xo": {"rules": {"new-cap": 0, "n/no-deprecated-api": 0, "n/prefer-global/url": 0, "unicorn/prefer-event-target": 0, "@typescript-eslint/ban-types": 0, "@typescript-eslint/no-namespace": 0, "@typescript-eslint/no-unsafe-call": 0, "unicorn/no-abusive-eslint-disable": 0, "@typescript-eslint/no-unsafe-return": 0, "@typescript-eslint/no-unsafe-argument": 0, "@typescript-eslint/no-unsafe-assignment": 0, "@typescript-eslint/no-implicit-any-catch": 0, "@typescript-eslint/restrict-plus-operands": 0, "@typescript-eslint/triple-slash-reference": 0, "@typescript-eslint/consistent-type-imports": 0, "@typescript-eslint/prefer-nullish-coalescing": 0, "@typescript-eslint/consistent-type-definitions": 0, "@typescript-eslint/restrict-template-expressions": 0}, "extends": ["plugin:jest/recommended"], "plugins": ["jest"]}, "dist": {"shasum": "eb915b665fda41b79652782df3f553449c406b9d", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-10.2.14.tgz", "fileCount": 12, "integrity": "sha512-zkDT5WAF4hSSoUgyfg5tFIxz8XQK+25W/TLVojJTMKBaxevLBBtLxgqguAuVQB8PVW79FVjHcU+GJ9tVbDZ9mQ==", "signatures": [{"sig": "MEYCIQDXsrpuC5mYzJXwyo9LjbPaAyYsiAPwPzEs6Ii3mbHoyQIhAOh9UZerCmK2EPLBZfdjt2XdGV1mwFWwXZpMiKlQk8HJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69104}, "jest": {"resolver": "ts-jest-resolver", "testMatch": ["**/test/*.test.(ts|js)"], "transform": {"^.+\\.(ts|tsx)$": ["ts-jest", {"useESM": true, "tsconfig": "./tsconfig.build.json"}]}, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.{ts,js}"], "moduleFileExtensions": ["ts", "js"], "extensionsToTreatAsEsm": [".ts"]}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=14.16"}, "exports": "./dist/index.js", "scripts": {"test": "xo && NODE_OPTIONS=--experimental-vm-modules jest --coverage ", "build": "tsc --project tsconfig.build.json", "clean": "rm -rf node_modules && rm -rf ./coverage && rm -rf ./test/testdb.sqlite && rm -rf ./dist", "prepare": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jaredwray/cacheable.git", "type": "git"}, "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "dependencies": {"keyv": "^4.5.3", "get-stream": "^6.0.1", "responselike": "^3.0.0", "normalize-url": "^8.0.0", "mimic-response": "^4.0.0", "http-cache-semantics": "^4.1.1", "@types/http-cache-semantics": "^4.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.56.0", "jest": "^29.7.0", "pify": "^6.1.0", "delay": "^6.0.0", "eslint": "^8.50.0", "express": "^4.18.2", "sqlite3": "^5.1.6", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.2.2", "@types/jest": "^29.5.5", "@types/node": "^20.8.2", "body-parser": "^1.20.2", "@keyv/sqlite": "^3.6.6", "@types/sqlite3": "^3.1.9", "ts-jest-resolver": "^2.0.1", "eslint-plugin-jest": "^27.4.2", "@types/responselike": "^1.0.1"}, "dependenciesComments": {"@types/http-cache-semantics": "It needs to be in the dependencies list and not devDependencies because otherwise projects that use this one will be getting `Could not find a declaration file for module 'http-cache-semantics'` error when running `tsc`, see https://github.com/jaredwray/cacheable-request/issues/194 for details"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_10.2.14_1696528047835_0.6217791700526816", "host": "s3://npm-registry-packages"}}, "11.0.0": {"name": "cacheable-request", "version": "11.0.0", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@11.0.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "xo": {"rules": {"new-cap": 0, "n/no-deprecated-api": 0, "n/prefer-global/url": 0, "unicorn/prefer-event-target": 0, "@typescript-eslint/ban-types": 0, "@typescript-eslint/no-namespace": 0, "@typescript-eslint/no-unsafe-call": 0, "unicorn/no-abusive-eslint-disable": 0, "@typescript-eslint/no-unsafe-return": 0, "@typescript-eslint/no-unsafe-argument": 0, "@typescript-eslint/no-unsafe-assignment": 0, "@typescript-eslint/no-implicit-any-catch": 0, "@typescript-eslint/restrict-plus-operands": 0, "@typescript-eslint/triple-slash-reference": 0, "@typescript-eslint/consistent-type-imports": 0, "@typescript-eslint/prefer-nullish-coalescing": 0, "@typescript-eslint/consistent-type-definitions": 0, "@typescript-eslint/no-unnecessary-type-assertion": 0, "@typescript-eslint/restrict-template-expressions": 0}, "extends": ["plugin:jest/recommended"], "plugins": ["jest"]}, "dist": {"shasum": "2f52224cd583476aeb3a5bbb5969b0d71245d9be", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-11.0.0.tgz", "fileCount": 13, "integrity": "sha512-xV9wxF1WgWTHunnE0k+nq6jl/a68DvfjPwM+pR6F89lyGBCPTf0vmdl7ydpCA1CNjnnb7q1r2gJW8pfDPkmnlg==", "signatures": [{"sig": "MEQCIHjhaCvWiLi4Y51sOb4XDtgId0Co7yqhOAYuiSgCSYoCAiBVfbm9XbP4DuWDhKQhHn+L64tqphmCIeNoYB/idrFbfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70724}, "jest": {"resolver": "ts-jest-resolver", "testMatch": ["**/test/*.test.(ts|js)"], "transform": {"^.+\\.(ts|tsx)$": ["ts-jest", {"useESM": true, "tsconfig": "./tsconfig.build.json"}]}, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.{ts,js}"], "moduleFileExtensions": ["ts", "js"], "extensionsToTreatAsEsm": [".ts"]}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=14.16"}, "exports": "./dist/index.js", "scripts": {"test": "xo && NODE_OPTIONS=--experimental-vm-modules jest --coverage ", "build": "tsc --project tsconfig.build.json", "clean": "rm -rf node_modules && rm -rf ./coverage && rm -rf ./test/testdb.sqlite && rm -rf ./dist", "prepare": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jaredwray/cacheable.git", "type": "git"}, "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "licenseText": "MIT License\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"keyv": "^4.5.4", "get-stream": "^8.0.1", "responselike": "^3.0.0", "normalize-url": "^8.0.0", "mimic-response": "^4.0.0", "http-cache-semantics": "^4.1.1", "@types/http-cache-semantics": "^4.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.56.0", "jest": "^29.7.0", "pify": "^6.1.0", "delay": "^6.0.0", "eslint": "^8.56.0", "express": "^4.18.2", "sqlite3": "^5.1.7", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "^5.3.3", "@types/jest": "^29.5.11", "@types/node": "^20.11.5", "body-parser": "^1.20.2", "@keyv/sqlite": "^3.6.6", "@types/sqlite3": "^3.1.11", "ts-jest-resolver": "^2.0.1", "eslint-plugin-jest": "^27.6.3", "@types/responselike": "^1.0.3"}, "dependenciesComments": {"@types/http-cache-semantics": "It needs to be in the dependencies list and not devDependencies because otherwise projects that use this one will be getting `Could not find a declaration file for module 'http-cache-semantics'` error when running `tsc`, see https://github.com/jaredwray/cacheable-request/issues/194 for details"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_11.0.0_1705693954657_0.44549974797600456", "host": "s3://npm-registry-packages"}}, "12.0.0": {"name": "cacheable-request", "version": "12.0.0", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@12.0.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "xo": {"rules": {"new-cap": 0, "n/no-deprecated-api": 0, "n/prefer-global/url": 0, "unicorn/prefer-event-target": 0, "@typescript-eslint/ban-types": 0, "@typescript-eslint/no-namespace": 0, "@typescript-eslint/no-unsafe-call": 0, "unicorn/no-abusive-eslint-disable": 0, "@typescript-eslint/no-unsafe-return": 0, "@typescript-eslint/no-unsafe-argument": 0, "@typescript-eslint/no-unsafe-assignment": 0, "@typescript-eslint/no-implicit-any-catch": 0, "@typescript-eslint/restrict-plus-operands": 0, "@typescript-eslint/triple-slash-reference": 0, "@typescript-eslint/consistent-type-imports": 0, "@typescript-eslint/prefer-nullish-coalescing": 0, "@typescript-eslint/consistent-type-definitions": 0, "@typescript-eslint/no-unnecessary-type-assertion": 0, "@typescript-eslint/restrict-template-expressions": 0}, "extends": ["plugin:jest/recommended"], "plugins": ["jest"]}, "dist": {"shasum": "5f54693c0708ced6ffadd9c1a74b5cb68b3c8a65", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-12.0.0.tgz", "fileCount": 13, "integrity": "sha512-UtNTDctKsb0brJBsCarSCDUMy+OTXy/FuvTpuPOpI8GscRIRRAZc8zBxWZ8qtymmtQq6yVtRVxMTQm01RZKjrg==", "signatures": [{"sig": "MEYCIQCyB+PE/y0/pvViO+RC1JjH5k1pq5KU8GomkzzMyGvjIQIhAOwKXIBAuaAklu3yGVylB8jhqmHbkpVCLTrRq0jPB+Y9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71253}, "jest": {"resolver": "ts-jest-resolver", "testMatch": ["**/test/*.test.(ts|js)"], "transform": {"^.+\\.(ts|tsx)$": ["ts-jest", {"useESM": true, "tsconfig": "./tsconfig.build.json"}]}, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.{ts,js}"], "moduleFileExtensions": ["ts", "js"], "extensionsToTreatAsEsm": [".ts"]}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=18"}, "exports": "./dist/index.js", "scripts": {"test": "xo --fix && NODE_OPTIONS=--experimental-vm-modules jest --coverage ", "build": "tsc --project tsconfig.build.json", "clean": "rm -rf node_modules && rm -rf ./coverage && rm -rf ./test/testdb.sqlite && rm -rf ./dist", "prepare": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jaredwray/cacheable.git", "type": "git"}, "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "licenseText": "MIT License\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"keyv": "^4.5.4", "get-stream": "^8.0.1", "responselike": "^3.0.0", "normalize-url": "^8.0.0", "mimic-response": "^4.0.0", "http-cache-semantics": "^4.1.1", "@types/http-cache-semantics": "^4.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.57.0", "jest": "^29.7.0", "pify": "^6.1.0", "delay": "^6.0.0", "eslint": "^8.56.0", "express": "^4.18.2", "sqlite3": "^5.1.7", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": "^5.3.3", "@types/jest": "^29.5.12", "@types/node": "^20.11.19", "body-parser": "^1.20.2", "@keyv/sqlite": "^3.6.7", "@types/sqlite3": "^3.1.11", "ts-jest-resolver": "^2.0.1", "eslint-plugin-jest": "^27.9.0", "@types/responselike": "^1.0.3"}, "dependenciesComments": {"@types/http-cache-semantics": "It needs to be in the dependencies list and not devDependencies because otherwise projects that use this one will be getting `Could not find a declaration file for module 'http-cache-semantics'` error when running `tsc`, see https://github.com/jaredwray/cacheable-request/issues/194 for details"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_12.0.0_1708545577731_0.47028004629425135", "host": "s3://npm-registry-packages"}}, "12.0.1": {"name": "cacheable-request", "version": "12.0.1", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@12.0.1", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "xo": {"rules": {"new-cap": 0, "n/no-deprecated-api": 0, "n/prefer-global/url": 0, "unicorn/prefer-event-target": 0, "@typescript-eslint/ban-types": 0, "@typescript-eslint/no-namespace": 0, "@typescript-eslint/no-unsafe-call": 0, "unicorn/no-abusive-eslint-disable": 0, "@typescript-eslint/no-unsafe-return": 0, "@typescript-eslint/no-unsafe-argument": 0, "@typescript-eslint/no-unsafe-assignment": 0, "@typescript-eslint/no-implicit-any-catch": 0, "@typescript-eslint/restrict-plus-operands": 0, "@typescript-eslint/triple-slash-reference": 0, "@typescript-eslint/consistent-type-imports": 0, "@typescript-eslint/prefer-nullish-coalescing": 0, "@typescript-eslint/consistent-type-definitions": 0, "@typescript-eslint/no-unnecessary-type-assertion": 0, "@typescript-eslint/restrict-template-expressions": 0}, "extends": ["plugin:jest/recommended"], "plugins": ["jest"]}, "dist": {"shasum": "e6f473b5b76c02e72a0ec2cd44c7cfb7c751d7c5", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-12.0.1.tgz", "fileCount": 13, "integrity": "sha512-Yo9wGIQUaAfIbk+qY0X4cDQgCosecfBe3V9NSyeY4qPC2SAkbCS4Xj79VP8WOzitpJUZKc/wsRCYF5ariDIwkg==", "signatures": [{"sig": "MEUCICsa7kvIIN3aBOmz3Ah8QUvgE1Bh5C+Gf0mntc3co1jJAiEA+vgWqoMNSqJIaiECb73cshAyp/wepVR+oMbq0TdiGVY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71253}, "jest": {"resolver": "ts-jest-resolver", "testMatch": ["**/test/*.test.(ts|js)"], "transform": {"^.+\\.(ts|tsx)$": ["ts-jest", {"useESM": true, "tsconfig": "./tsconfig.build.json"}]}, "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.{ts,js}"], "moduleFileExtensions": ["ts", "js"], "extensionsToTreatAsEsm": [".ts"]}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=18"}, "exports": "./dist/index.js", "scripts": {"test": "xo --fix && NODE_OPTIONS=--experimental-vm-modules jest --coverage ", "build": "tsc --project tsconfig.build.json", "clean": "rm -rf node_modules && rm -rf ./coverage && rm -rf ./test/testdb.sqlite && rm -rf ./dist", "prepare": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jaredwray/cacheable.git", "type": "git"}, "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "licenseText": "MIT License\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"keyv": "^4.5.4", "get-stream": "^9.0.1", "responselike": "^3.0.0", "normalize-url": "^8.0.1", "mimic-response": "^4.0.0", "http-cache-semantics": "^4.1.1", "@types/http-cache-semantics": "^4.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.58.0", "jest": "^29.7.0", "pify": "^6.1.0", "delay": "^6.0.0", "eslint": "^8.57.0", "express": "^4.19.1", "sqlite3": "^5.1.7", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": "^5.4.3", "@types/jest": "^29.5.12", "@types/node": "^20.11.30", "body-parser": "^1.20.2", "@keyv/sqlite": "^3.6.7", "@types/sqlite3": "^3.1.11", "ts-jest-resolver": "^2.0.1", "eslint-plugin-jest": "^27.9.0", "@types/responselike": "^1.0.3"}, "dependenciesComments": {"@types/http-cache-semantics": "It needs to be in the dependencies list and not devDependencies because otherwise projects that use this one will be getting `Could not find a declaration file for module 'http-cache-semantics'` error when running `tsc`, see https://github.com/jaredwray/cacheable-request/issues/194 for details"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_12.0.1_1711079938435_0.44055876240102565", "host": "s3://npm-registry-packages"}}, "13.0.0": {"name": "cacheable-request", "version": "13.0.0", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@13.0.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "homepage": "https://github.com/jaredwray/cacheable#readme", "bugs": {"url": "https://github.com/jaredwray/cacheable/issues"}, "xo": {"rules": {"new-cap": 0, "n/no-deprecated-api": 0, "n/prefer-global/url": 0, "unicorn/prefer-event-target": 0, "@typescript-eslint/ban-types": 0, "promise/prefer-await-to-then": 0, "@typescript-eslint/no-namespace": 0, "@typescript-eslint/no-unsafe-call": 0, "unicorn/no-abusive-eslint-disable": 0, "@typescript-eslint/no-unsafe-return": 0, "@typescript-eslint/no-unsafe-argument": 0, "@typescript-eslint/no-unsafe-assignment": 0, "@typescript-eslint/no-implicit-any-catch": 0, "@typescript-eslint/restrict-plus-operands": 0, "@typescript-eslint/triple-slash-reference": 0, "@typescript-eslint/consistent-type-imports": 0, "@typescript-eslint/prefer-nullish-coalescing": 0, "@typescript-eslint/consistent-type-definitions": 0, "@typescript-eslint/no-unnecessary-type-assertion": 0, "@typescript-eslint/restrict-template-expressions": 0}}, "dist": {"shasum": "6cd4e8347a5053f994b8add116c678ae20b203dc", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-13.0.0.tgz", "fileCount": 11, "integrity": "sha512-aVaPz1icOFHrjT1trABao7njKMbu5NAOrTbh5Ljq48ReQL+NYTkH0TfZhfHmfEN0B0DWFkgytGuAXeoOgRikLA==", "signatures": [{"sig": "MEUCIBaPL5vqqqXolyq56LEAJqrG2Wprzb8OhYpJYj1ILeKSAiEAgNFC3IrqC72aPntsbPXlCZ1h9p6AkeW7Ow8/yKYE3YQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71561}, "type": "module", "_from": "file:cacheable-request-13.0.0.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">=18"}, "exports": "./dist/index.js", "scripts": {"test": "xo --fix && vitest run --coverage", "build": "tsc --project tsconfig.build.json", "clean": "rimraf node_modules ./coverage ./test/testdb.sqlite ./dist", "test:ci": "xo && vitest run"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/h1/n3vxgc0n1sn5_9pxftc4p6l80000gn/T/2f80116f7b921533060372280838b90d/cacheable-request-13.0.0.tgz", "_integrity": "sha512-aVaPz1icOFHrjT1trABao7njKMbu5NAOrTbh5Ljq48ReQL+NYTkH0TfZhfHmfEN0B0DWFkgytGuAXeoOgRikLA==", "repository": {"url": "git+https://github.com/jaredwray/cacheable.git", "type": "git", "directory": "packages/cacheable-request"}, "_npmVersion": "10.8.3", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"keyv": "^5.1.2", "get-stream": "^9.0.1", "responselike": "^3.0.0", "normalize-url": "^8.0.1", "mimic-response": "^4.0.0", "http-cache-semantics": "^4.1.1", "@types/http-cache-semantics": "^4.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.59.3", "pify": "^6.1.0", "delay": "^6.0.0", "rimraf": "^6.0.1", "vitest": "^2.1.3", "express": "^4.21.1", "sqlite3": "^5.1.7", "typescript": "^5.6.3", "@types/node": "^22.7.7", "body-parser": "^1.20.3", "@keyv/sqlite": "^4.0.1", "@types/sqlite3": "^3.1.11", "@types/responselike": "^1.0.3", "@vitest/coverage-v8": "^2.1.3"}, "dependenciesComments": {"@types/http-cache-semantics": "It needs to be in the dependencies list and not devDependencies because otherwise projects that use this one will be getting `Could not find a declaration file for module 'http-cache-semantics'` error when running `tsc`, see https://github.com/jaredwray/cacheable-request/issues/194 for details"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_13.0.0_1729543996662_0.2634340147796663", "host": "s3://npm-registry-packages"}}, "13.0.1": {"name": "cacheable-request", "version": "13.0.1", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@13.0.1", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "homepage": "https://github.com/jaredwray/cacheable#readme", "bugs": {"url": "https://github.com/jaredwray/cacheable/issues"}, "xo": {"rules": {"new-cap": 0, "n/no-deprecated-api": 0, "n/prefer-global/url": 0, "unicorn/prefer-event-target": 0, "@typescript-eslint/ban-types": 0, "promise/prefer-await-to-then": 0, "@typescript-eslint/no-namespace": 0, "@typescript-eslint/no-unsafe-call": 0, "unicorn/no-abusive-eslint-disable": 0, "@typescript-eslint/no-unsafe-return": 0, "@typescript-eslint/no-unsafe-argument": 0, "@typescript-eslint/no-unsafe-assignment": 0, "@typescript-eslint/no-implicit-any-catch": 0, "@typescript-eslint/restrict-plus-operands": 0, "@typescript-eslint/triple-slash-reference": 0, "@typescript-eslint/consistent-type-imports": 0, "@typescript-eslint/prefer-nullish-coalescing": 0, "@typescript-eslint/consistent-type-definitions": 0, "@typescript-eslint/no-unnecessary-type-assertion": 0, "@typescript-eslint/restrict-template-expressions": 0}}, "dist": {"shasum": "74a7d3d084168717e260b6629e0a340cecb882bf", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-13.0.1.tgz", "fileCount": 11, "integrity": "sha512-lgTZV3g8xVgjOgeCOJh5mqtCDfLXoAwX80JdQ8Pb7DBNMS/+JpPK2xQWXMazOtU+33FDqH6x/fdNCTheOmbMyw==", "signatures": [{"sig": "MEQCIF99VaeKPuVpHIxozyZQcaQlKkCqQL3ngCizWu7iVIieAiBqBhbEKpvz/REUKX37ml2hiwWAVZ5FcuxQUdvz2SvhbQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71561}, "type": "module", "_from": "file:cacheable-request-13.0.1.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">=18"}, "exports": "./dist/index.js", "scripts": {"test": "xo --fix && vitest run --coverage", "build": "tsc --project tsconfig.build.json", "clean": "rimraf node_modules ./coverage ./test/testdb.sqlite ./dist", "test:ci": "xo && vitest run"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/h1/n3vxgc0n1sn5_9pxftc4p6l80000gn/T/39689a57bcced21794500e908d7fd8de/cacheable-request-13.0.1.tgz", "_integrity": "sha512-lgTZV3g8xVgjOgeCOJh5mqtCDfLXoAwX80JdQ8Pb7DBNMS/+JpPK2xQWXMazOtU+33FDqH6x/fdNCTheOmbMyw==", "repository": {"url": "git+https://github.com/jaredwray/cacheable.git", "type": "git", "directory": "packages/cacheable-request"}, "_npmVersion": "10.8.3", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"keyv": "^5.1.2", "get-stream": "^9.0.1", "responselike": "^3.0.0", "normalize-url": "^8.0.1", "mimic-response": "^4.0.0", "http-cache-semantics": "^4.1.1", "@types/http-cache-semantics": "^4.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.59.3", "pify": "^6.1.0", "delay": "^6.0.0", "rimraf": "^6.0.1", "vitest": "^2.1.3", "express": "^4.21.1", "sqlite3": "^5.1.7", "typescript": "^5.6.3", "@types/node": "^22.7.7", "body-parser": "^1.20.3", "@keyv/sqlite": "^4.0.1", "@types/sqlite3": "^3.1.11", "@types/responselike": "^1.0.3", "@vitest/coverage-v8": "^2.1.3"}, "dependenciesComments": {"@types/http-cache-semantics": "It needs to be in the dependencies list and not devDependencies because otherwise projects that use this one will be getting `Could not find a declaration file for module 'http-cache-semantics'` error when running `tsc`, see https://github.com/jaredwray/cacheable-request/issues/194 for details"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_13.0.1_1729544057174_0.8696524143448867", "host": "s3://npm-registry-packages"}}, "13.0.2": {"name": "cacheable-request", "version": "13.0.2", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cacheable-request@13.0.2", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "homepage": "https://github.com/jaredwray/cacheable#readme", "bugs": {"url": "https://github.com/jaredwray/cacheable/issues"}, "xo": {"rules": {"new-cap": 0, "n/no-deprecated-api": 0, "n/prefer-global/url": 0, "unicorn/prefer-event-target": 0, "@typescript-eslint/ban-types": 0, "promise/prefer-await-to-then": 0, "@typescript-eslint/no-namespace": 0, "@typescript-eslint/no-unsafe-call": 0, "unicorn/no-abusive-eslint-disable": 0, "@typescript-eslint/no-unsafe-return": 0, "@typescript-eslint/no-unsafe-argument": 0, "@typescript-eslint/no-unsafe-assignment": 0, "@typescript-eslint/no-implicit-any-catch": 0, "@typescript-eslint/restrict-plus-operands": 0, "@typescript-eslint/triple-slash-reference": 0, "@typescript-eslint/consistent-type-imports": 0, "@typescript-eslint/prefer-nullish-coalescing": 0, "@typescript-eslint/consistent-type-definitions": 0, "@typescript-eslint/no-unnecessary-type-assertion": 0, "@typescript-eslint/restrict-template-expressions": 0}}, "dist": {"shasum": "27b87d763fa205897088896cedac4b1ab813c391", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-13.0.2.tgz", "fileCount": 11, "integrity": "sha512-46IqOe2A0P6DJsmNeQ39fXAUCXk4tGIPYJINYYFFHYxgP0w5QeqAgZFxc/ZxQG+BrtTwtABAnfLLj6FbbBzj1Q==", "signatures": [{"sig": "MEYCIQCLaNkRxm3YJEl9DJo2dI6ZNWP36gxwEU9wB/I15AAyQQIhAKFsiJSXA4votPSmjvPdfakEKcQao3CkYJUwb6kw4H30", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71561}, "type": "module", "_from": "file:cacheable-request-13.0.2.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">=18"}, "exports": "./dist/index.js", "scripts": {"test": "xo --fix && vitest run --coverage", "build": "tsc --project tsconfig.build.json", "clean": "rimraf node_modules ./coverage ./test/testdb.sqlite ./dist", "test:ci": "xo && vitest run"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/gs/5m4m2s857ts7l7nbvv0b57r80000gn/T/ea667c2d430554130b2ef0e1e79815be/cacheable-request-13.0.2.tgz", "_integrity": "sha512-46IqOe2A0P6DJsmNeQ39fXAUCXk4tGIPYJINYYFFHYxgP0w5QeqAgZFxc/ZxQG+BrtTwtABAnfLLj6FbbBzj1Q==", "repository": {"url": "git+https://github.com/jaredwray/cacheable.git", "type": "git", "directory": "packages/cacheable-request"}, "_npmVersion": "10.9.0", "description": "Wrap native HTTP requests with RFC compliant cache support", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"keyv": "^5.2.1", "get-stream": "^9.0.1", "responselike": "^3.0.0", "normalize-url": "^8.0.1", "mimic-response": "^4.0.0", "http-cache-semantics": "^4.1.1", "@types/http-cache-semantics": "^4.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.59.3", "pify": "^6.1.0", "delay": "^6.0.0", "rimraf": "^6.0.1", "vitest": "^2.1.3", "express": "^4.21.1", "sqlite3": "^5.1.7", "typescript": "^5.6.3", "@types/node": "^22.7.7", "body-parser": "^1.20.3", "@keyv/sqlite": "^4.0.1", "@types/sqlite3": "^3.1.11", "@types/responselike": "^1.0.3", "@vitest/coverage-v8": "^2.1.3"}, "dependenciesComments": {"@types/http-cache-semantics": "It needs to be in the dependencies list and not devDependencies because otherwise projects that use this one will be getting `Could not find a declaration file for module 'http-cache-semantics'` error when running `tsc`, see https://github.com/jaredwray/cacheable-request/issues/194 for details"}, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_13.0.2_1731275814804_0.29051726656677834", "host": "s3://npm-registry-packages"}}, "13.0.3": {"name": "cacheable-request", "version": "13.0.3", "description": "Wrap native HTTP requests with RFC compliant cache support", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jaredwray/cacheable.git", "directory": "packages/cacheable-request"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jaredwray.com"}, "type": "module", "exports": "./dist/index.js", "types": "./dist/index.d.ts", "engines": {"node": ">=18"}, "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "dependenciesComments": {"@types/http-cache-semantics": "It needs to be in the dependencies list and not devDependencies because otherwise projects that use this one will be getting `Could not find a declaration file for module 'http-cache-semantics'` error when running `tsc`, see https://github.com/jaredwray/cacheable-request/issues/194 for details"}, "dependencies": {"@types/http-cache-semantics": "^4.0.4", "get-stream": "^9.0.1", "http-cache-semantics": "^4.1.1", "keyv": "^5.2.3", "mimic-response": "^4.0.0", "normalize-url": "^8.0.1", "responselike": "^3.0.0"}, "devDependencies": {"@keyv/sqlite": "^4.0.1", "@types/node": "^22.10.2", "@types/responselike": "^1.0.3", "@types/sqlite3": "^3.1.11", "@vitest/coverage-v8": "^2.1.8", "body-parser": "^1.20.3", "delay": "^6.0.0", "express": "^4.21.2", "pify": "^6.1.0", "rimraf": "^6.0.1", "sqlite3": "^5.1.7", "typescript": "^5.7.2", "vitest": "^2.1.8", "xo": "^0.60.0"}, "xo": {"rules": {"@typescript-eslint/triple-slash-reference": 0, "@typescript-eslint/no-namespace": 0, "@typescript-eslint/no-unsafe-assignment": 0, "@typescript-eslint/no-unsafe-call": 0, "@typescript-eslint/ban-types": 0, "@typescript-eslint/restrict-template-expressions": 0, "@typescript-eslint/no-unsafe-return": 0, "@typescript-eslint/no-unsafe-argument": 0, "new-cap": 0, "unicorn/no-abusive-eslint-disable": 0, "@typescript-eslint/restrict-plus-operands": 0, "@typescript-eslint/no-implicit-any-catch": 0, "@typescript-eslint/consistent-type-imports": 0, "@typescript-eslint/consistent-type-definitions": 0, "@typescript-eslint/prefer-nullish-coalescing": 0, "n/prefer-global/url": 0, "n/no-deprecated-api": 0, "unicorn/prefer-event-target": 0, "@typescript-eslint/no-unnecessary-type-assertion": 0, "promise/prefer-await-to-then": 0}}, "scripts": {"test": "xo --fix && vitest run --coverage", "test:ci": "xo && vitest run", "prepublish": "pnpm run build", "build": "tsc --project tsconfig.build.json", "clean": "rimraf node_modules ./coverage ./test/testdb.sqlite ./dist"}, "_id": "cacheable-request@13.0.3", "bugs": {"url": "https://github.com/jaredwray/cacheable/issues"}, "homepage": "https://github.com/jaredwray/cacheable#readme", "_integrity": "sha512-3gjHiEihbf6eBO0Gxb1pOGxIrtO0tQPKuZ09GF2nlTJvTqIW2jtS1plt/YugbghrmMJEXOCagCgQoE92BAOscg==", "_resolved": "/private/var/folders/q4/x95kq1ln6cd7rrnct9cby32r0000gn/T/6bee315f4c19ed62d21210fd8720461b/cacheable-request-13.0.3.tgz", "_from": "file:cacheable-request-13.0.3.tgz", "_nodeVersion": "22.12.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-3gjHiEihbf6eBO0Gxb1pOGxIrtO0tQPKuZ09GF2nlTJvTqIW2jtS1plt/YugbghrmMJEXOCagCgQoE92BAOscg==", "shasum": "15bf504dfc0d8f17d7e03804482c1ef215896990", "tarball": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-13.0.3.tgz", "fileCount": 11, "unpackedSize": 71598, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIApxyVRXv1l5Wl/OR0El4XyCSq3xF1kqJ7VJeVDkeAHOAiEAismwDf2hRC4X/M2AyJqWNIPyyBZh0wdrSGOJnYd4DQo="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/cacheable-request_13.0.3_1735258130948_0.04457698831472556"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-06-02T12:36:50.988Z", "modified": "2024-12-27T00:08:51.297Z", "0.0.0": "2017-06-02T12:36:50.988Z", "0.1.0": "2017-06-09T14:06:47.087Z", "0.2.0": "2017-06-10T03:58:44.270Z", "0.3.0": "2017-06-10T07:21:16.883Z", "0.4.0": "2017-08-12T00:12:18.953Z", "0.5.0": "2017-08-13T12:02:33.299Z", "1.0.0": "2017-08-13T13:31:44.524Z", "1.0.1": "2017-08-13T13:59:47.583Z", "1.0.2": "2017-08-13T16:07:07.348Z", "1.1.0": "2017-08-13T16:50:11.531Z", "1.1.1": "2017-08-13T18:08:59.153Z", "1.1.2": "2017-08-17T16:38:22.801Z", "1.1.3": "2017-08-17T16:42:04.020Z", "2.0.0": "2017-10-03T13:58:46.286Z", "2.0.1": "2017-10-12T16:26:06.084Z", "2.1.0": "2017-11-12T11:42:04.930Z", "2.1.1": "2017-11-16T09:35:09.462Z", "2.1.2": "2017-12-01T13:08:56.165Z", "2.1.3": "2017-12-02T11:49:07.190Z", "2.1.4": "2017-12-26T20:09:56.307Z", "3.0.0": "2018-07-06T01:52:46.696Z", "4.0.0": "2018-07-20T10:37:01.341Z", "4.0.1": "2018-07-22T15:44:42.793Z", "5.0.0": "2018-09-05T14:53:42.689Z", "5.1.0": "2018-10-17T14:41:19.627Z", "5.2.0": "2018-11-13T08:25:02.628Z", "5.2.1": "2018-12-22T05:27:47.866Z", "6.0.0": "2019-01-04T07:37:43.357Z", "6.1.0": "2019-06-07T06:15:39.202Z", "7.0.0": "2019-10-10T00:23:46.330Z", "7.0.1": "2020-01-21T11:26:09.303Z", "7.0.2": "2021-06-09T11:17:12.520Z", "8.0.0": "2022-06-11T18:11:52.258Z", "8.0.1": "2022-06-11T18:39:53.257Z", "8.3.0": "2022-06-27T15:27:53.476Z", "8.3.1": "2022-06-27T15:29:23.195Z", "9.0.0": "2022-08-13T15:48:13.190Z", "10.0.0": "2022-09-10T17:11:21.624Z", "10.0.1": "2022-09-11T20:02:38.847Z", "10.0.2": "2022-09-14T14:14:27.128Z", "10.1.2": "2022-09-16T12:36:23.166Z", "10.2.0": "2022-09-23T02:14:42.569Z", "10.2.1": "2022-09-26T14:31:26.616Z", "10.2.2": "2022-10-19T17:45:35.120Z", "10.2.3": "2022-11-22T17:33:23.387Z", "10.2.4": "2022-12-23T01:10:34.071Z", "10.2.5": "2023-01-05T22:15:17.163Z", "10.2.6": "2023-02-02T16:53:21.132Z", "10.2.7": "2023-02-06T18:10:58.551Z", "10.2.8": "2023-02-23T19:26:07.214Z", "10.2.9": "2023-03-26T02:13:29.279Z", "10.2.10": "2023-04-25T15:46:01.127Z", "7.0.3": "2023-06-07T19:11:25.437Z", "7.0.4": "2023-06-07T23:46:56.697Z", "10.2.11": "2023-06-20T17:36:45.591Z", "10.2.12": "2023-06-22T13:31:13.142Z", "10.2.13": "2023-07-26T04:54:30.674Z", "10.2.14": "2023-10-05T17:47:28.079Z", "11.0.0": "2024-01-19T19:52:34.840Z", "12.0.0": "2024-02-21T19:59:37.903Z", "12.0.1": "2024-03-22T03:58:58.679Z", "13.0.0": "2024-10-21T20:53:16.978Z", "13.0.1": "2024-10-21T20:54:17.406Z", "13.0.2": "2024-11-10T21:56:55.040Z", "13.0.3": "2024-12-27T00:08:51.146Z"}, "bugs": {"url": "https://github.com/jaredwray/cacheable/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jaredwray.com"}, "license": "MIT", "homepage": "https://github.com/jaredwray/cacheable#readme", "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "repository": {"type": "git", "url": "git+https://github.com/jaredwray/cacheable.git", "directory": "packages/cacheable-request"}, "description": "Wrap native HTTP requests with RFC compliant cache support", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "readme": "[<img align=\"center\" src=\"https://cacheable.org/symbol.svg\" alt=\"Cacheable\" />](https://github.com/jaredwray/cacheable)\n\n# cacheable-request\n\n> Wrap native HTTP requests with RFC compliant cache support\n\n[![codecov](https://codecov.io/gh/jaredwray/cacheable/graph/badge.svg?token=lWZ9OBQ7GM)](https://codecov.io/gh/jaredwray/cacheable)\n[![tests](https://github.com/jaredwray/cacheable/actions/workflows/tests.yml/badge.svg)](https://github.com/jaredwray/cacheable/actions/workflows/tests.yml)\n[![npm](https://img.shields.io/npm/dm/cacheable-request.svg)](https://www.npmjs.com/package/cacheable-request)\n[![npm](https://img.shields.io/npm/v/cacheable-request.svg)](https://www.npmjs.com/package/cacheable-request)\n[![license](https://img.shields.io/github/license/jaredwray/cacheable)](https://github.com/jaredwray/cacheable/blob/main/LICENSE)\n\n[RFC 7234](http://httpwg.org/specs/rfc7234.html) compliant HTTP caching for native Node.js HTTP/HTTPS requests. Caching works out of the box in memory or is easily pluggable with a wide range of storage adapters.\n\n**Note:** This is a low level wrapper around the core HTTP modules, it's not a high level request library.\n\n# Table of Contents\n* [Latest Changes](#latest-changes)\n* [Features](#features)\n* [Install and Usage](#install-and-usage)\n* [Storage Adapters](#storage-adapters)\n* [API](#api)\n* [Using Hooks](#using-hooks)\n* [Contributing](#contributing)\n* [Ask a Question](#ask-a-question)\n* [License](#license) (MIT)\n\n# Latest Changes\n\n## Breaking Changes with v13.0.0\n[Keyv](https://keyv.org) has been updated to version 5. With this update, you can no longer pass in a connection string directly to the `CacheableRequest` constructor. Instead, you should pass in a Keyv or Keyv storage adapter instance.\n\n## Breaking Changes with v10.0.0\nThis release contains breaking changes. This is the new way to use this package.\n\n### Usage Before v10\n```js\nimport http from 'http';\nimport CacheableRequest from 'cacheable-request';\n\n// Then instead of\nconst req = http.request('http://example.com', cb);\nreq.end();\n\n// You can do\nconst cacheableRequest = new CacheableRequest(http.request);\nconst cacheReq = cacheableRequest('http://example.com', cb);\ncacheReq.on('request', req => req.end());\n// Future requests to 'example.com' will be returned from cache if still valid\n\n// You pass in any other http.request API compatible method to be wrapped with cache support:\nconst cacheableRequest = new CacheableRequest(https.request);\nconst cacheableRequest = new CacheableRequest(electron.net);\n```\n\n### Usage After v10.1.0\n```js\n\nimport CacheableRequest from 'cacheable-request';\n\n// Now You can do\nconst cacheableRequest = new CacheableRequest(http.request).request();\nconst cacheReq = cacheableRequest('http://example.com', cb);\ncacheReq.on('request', req => req.end());\n// Future requests to 'example.com' will be returned from cache if still valid\n\n// You pass in any other http.request API compatible method to be wrapped with cache support:\nconst cacheableRequest = new CacheableRequest(https.request).request();\nconst cacheableRequest = new CacheableRequest(electron.net).request();\n```\n\nThe biggest change is that when you do a `new` CacheableRequest you now want to call `request` method will give you the instance to use. \n\n```diff\n- const cacheableRequest = new CacheableRequest(http.request);\n+ const cacheableRequest = new CacheableRequest(http.request).request();\n```\n\n### ESM Support in version 9 and higher. \n\nWe are now using pure esm support in our package. If you need to use commonjs you can use v8 or lower. To learn more about using ESM please read this from `sindresorhus`: https://gist.github.com/sindresorhus/a39789f98801d908bbc7ff3ecc99d99c\n\n## Features\n\n- Only stores cacheable responses as defined by RFC 7234\n- Fresh cache entries are served directly from cache\n- Stale cache entries are revalidated with `If-None-Match`/`If-Modified-Since` headers\n- 304 responses from revalidation requests use cached body\n- Updates `Age` header on cached responses\n- Can completely bypass cache on a per request basis\n- In memory cache by default\n- Official support for Redis, Memcache, Etcd, MongoDB, SQLite, PostgreSQL and MySQL storage adapters\n- Easily plug in your own or third-party storage adapters\n- If DB connection fails, cache is automatically bypassed ([disabled by default](#optsautomaticfailover))\n- Adds cache support to any existing HTTP code with minimal changes\n- Uses [http-cache-semantics](https://github.com/pornel/http-cache-semantics) internally for HTTP RFC 7234 compliance\n\n## Install and Usage\n\n```shell\nnpm install cacheable-request\n```\n\n```js\nimport http from 'http';\nimport CacheableRequest from 'cacheable-request';\n\n// Then instead of\nconst req = http.request('http://example.com', cb);\nreq.end();\n\n// You can do\nconst cacheableRequest = new CacheableRequest(http.request).createCacheableRequest();\nconst cacheReq = cacheableRequest('http://example.com', cb);\ncacheReq.on('request', req => req.end());\n// Future requests to 'example.com' will be returned from cache if still valid\n\n// You pass in any other http.request API compatible method to be wrapped with cache support:\nconst cacheableRequest = new CacheableRequest(https.request).createCacheableRequest();\nconst cacheableRequest = new CacheableRequest(electron.net).createCacheableRequest();\n```\n\n## Storage Adapters\n\n`cacheable-request` uses [Keyv](https://github.com/jaredwray/keyv) to support a wide range of storage adapters.\n\nFor example, to use Redis as a cache backend, you just need to install the official Redis Keyv storage adapter:\n\n```\nnpm install @keyv/redis\n```\n\nAnd then you can pass `CacheableRequest` your connection string:\n\n```js\nimport KeyvRedis from '@keyv/redis';\nimport CacheableRequest from 'cacheable-request';\n\nconst keyvRedis = new KeyvRedis('redis://localhost:6379');\nconst cacheableRequest = new CacheableRequest(http.request, KeyvRedis).createCacheableRequest();\n```\n\n[View all official Keyv storage adapters.](https://github.com/jaredwray/keyv#official-storage-adapters)\n\nKeyv also supports anything that follows the Map API so it's easy to write your own storage adapter or use a third-party solution.\n\ne.g The following are all valid storage adapters\n\n```js\nconst storageAdapter = new Map();\n// or\nconst storageAdapter = require('./my-storage-adapter');\n// or\nconst QuickLRU = require('quick-lru');\nconst storageAdapter = new QuickLRU({ maxSize: 1000 });\n\nconst cacheableRequest = new CacheableRequest(http.request, storageAdapter).createCacheableRequest();\n```\n\nView the [Keyv docs](https://github.com/jaredwray/keyv) for more information on how to use storage adapters.\n\n## API\n\n### new cacheableRequest(request, [storageAdapter])\n\nReturns the provided request function wrapped with cache support.\n\n#### request\n\nType: `function`\n\nRequest function to wrap with cache support. Should be [`http.request`](https://nodejs.org/api/http.html#http_http_request_options_callback) or a similar API compatible request function.\n\n#### storageAdapter\n\nType: `Keyv storage adapter`<br />\nDefault: `new Map()`\n\nA [Keyv](https://github.com/jaredwray/keyv) storage adapter instance, or connection string if using with an official Keyv storage adapter.\n\n### Instance\n\n#### cacheableRequest(opts, [cb])\n\nReturns an event emitter.\n\n##### opts\n\nType: `object`, `string`\n\n- Any of the default request functions options.\n- Any [`http-cache-semantics`](https://github.com/kornelski/http-cache-semantics#constructor-options) options.\n- Any of the following:\n\n###### opts.cache\n\nType: `boolean`<br />\nDefault: `true`\n\nIf the cache should be used. Setting this to false will completely bypass the cache for the current request.\n\n###### opts.strictTtl\n\nType: `boolean`<br />\nDefault: `false`\n\nIf set to `true` once a cached resource has expired it is deleted and will have to be re-requested.\n\nIf set to `false` (default), after a cached resource's TTL expires it is kept in the cache and will be revalidated on the next request with `If-None-Match`/`If-Modified-Since` headers.\n\n###### opts.maxTtl\n\nType: `number`<br />\nDefault: `undefined`\n\nLimits TTL. The `number` represents milliseconds.\n\n###### opts.automaticFailover\n\nType: `boolean`<br />\nDefault: `false`\n\nWhen set to `true`, if the DB connection fails we will automatically fallback to a network request. DB errors will still be emitted to notify you of the problem even though the request callback may succeed.\n\n###### opts.forceRefresh\n\nType: `boolean`<br />\nDefault: `false`\n\nForces refreshing the cache. If the response could be retrieved from the cache, it will perform a new request and override the cache instead.\n\n##### cb\n\nType: `function`\n\nThe callback function which will receive the response as an argument.\n\nThe response can be either a [Node.js HTTP response stream](https://nodejs.org/api/http.html#http_class_http_incomingmessage) or a [responselike object](https://github.com/lukechilds/responselike). The response will also have a `fromCache` property set with a boolean value.\n\n##### .on('request', request)\n\n`request` event to get the request object of the request.\n\n**Note:** This event will only fire if an HTTP request is actually made, not when a response is retrieved from cache. However, you should always handle the `request` event to end the request and handle any potential request errors.\n\n##### .on('response', response)\n\n`response` event to get the response object from the HTTP request or cache.\n\n##### .on('error', error)\n\n`error` event emitted in case of an error with the cache.\n\nErrors emitted here will be an instance of `CacheableRequest.RequestError` or `CacheableRequest.CacheError`. You will only ever receive a `RequestError` if the request function throws (normally caused by invalid user input). Normal request errors should be handled inside the `request` event.\n\nTo properly handle all error scenarios you should use the following pattern:\n\n```js\ncacheableRequest('example.com', cb)\n  .on('error', err => {\n    if (err instanceof CacheableRequest.CacheError) {\n      handleCacheError(err); // Cache error\n    } else if (err instanceof CacheableRequest.RequestError) {\n      handleRequestError(err); // Request function thrown\n    }\n  })\n  .on('request', req => {\n    req.on('error', handleRequestError); // Request error emitted\n    req.end();\n  });\n```\n**Note:** Database connection errors are emitted here, however `cacheable-request` will attempt to re-request the resource and bypass the cache on a connection error. Therefore a database connection error doesn't necessarily mean the request won't be fulfilled.\n\n\n## Using Hooks\nHooks have been implemented since version `v9` and are very useful to modify response before saving it in cache. You can use hooks to modify response before saving it in cache.\n\n### Add Hooks\n\nThe hook will pre compute response right before saving it in cache. You can include many hooks and it will run in order you add hook on response object.\n\n```js\nimport http from 'http';\nimport CacheableRequest from 'cacheable-request';\n\nconst cacheableRequest = new CacheableRequest(request, cache).request();\n\n// adding a hook to decompress response\ncacheableRequest.addHook('onResponse', async (value: CacheValue, response: any) => {\n  const buffer = await pm(gunzip)(value.body);\n  value.body = buffer.toString();\n  return value;\n});\n```\n\nhere is also an example of how to add in the remote address\n\n```js\nimport CacheableRequest, {CacheValue} from 'cacheable-request';\n\nconst cacheableRequest = new CacheableRequest(request, cache).request();\ncacheableRequest.addHook('onResponse', (value: CacheValue, response: any) => {\n  if (response.connection) {\n    value.remoteAddress = response.connection.remoteAddress;\n  }\n\n  return value;\n});\n```\n\n### Remove Hooks\n\nYou can also remove hook by using below\n\n```js\nCacheableRequest.removeHook('onResponse');\n```\n\n## How to Contribute\n\nCacheable-Request is an open source package and community driven that is maintained regularly. In addition we have a couple of other guidelines for review:\n\n* [CODE_OF_CONDUCT.md](CODE_OF_CONDUCT.md) - Our code of conduct\n* [CONTRIBUTING.md](CONTRIBUTING.md) - How to contribute to this project\n* [SECURITY.md](SECURITY.md) - Security guidelines and supported versions \n\n## Post an Issue\n\nTo post an issue, navigate to the \"Issues\" tab in the main repository, and then select \"New Issue.\" Enter a clear title describing the issue, as well as a description containing additional relevant information. Also select the label that best describes your issue type. For a bug report, for example, create an issue with the label \"bug.\" In the description field, Be sure to include replication steps, as well as any relevant error messages.\n\nIf you're reporting a security violation, be sure to check out the project's [security policy](SECURITY.md).\n\nPlease also refer to our [Code of Conduct](CODE_OF_CONDUCT.md) for more information on how to report issues.\n\n## Ask a Question\n\nTo ask a question, create an issue with the label \"question.\" In the issue description, include the related code and any context that can help us answer your question.\n\n## License and Copyright\n\n[MIT © Luke Childs 2017-2021 and Jared Wray 2022+](./LICENSE)\n", "readmeFilename": "README.md", "users": {"shanewholloway": true}}