{"_id": "eyes", "_rev": "50-e4c013b99f2a8bd3f96e8ba0feba2ea0", "name": "eyes", "description": "a customizable value inspector", "dist-tags": {"latest": "0.1.8"}, "versions": {"0.1.1": {"name": "eyes", "description": "a customizable value inspector", "url": "http://github.com/cloudhead/eyes.js", "keywords": ["inspector", "debug", "inspect", "print"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "licenses": ["MIT"], "dependencies": {}, "main": "./lib/eyes", "version": "0.1.1", "directories": {"lib": "./lib", "test": "./test"}, "engines": {"node": "> 0.1.90"}, "_id": "eyes@0.1.1", "_nodeSupported": true, "_npmVersion": "0.2.7-2", "_nodeVersion": "v0.3.1-pre", "dist": {"tarball": "https://registry.npmjs.org/eyes/-/eyes-0.1.1.tgz", "shasum": "de4510396717cb573c74eb9bf8ddee49802555f8", "integrity": "sha512-m8sln3Iob8T4vJKf6IhTlW7fOmyU46JnB2GX4N1L7Iwfxfg8cal+eWetnh+7ulmIVZnceYm2RyeRvcQbOHPVDQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIErf3T+gvU5ycL9dYGHqKL6VAm/nb2WG1zLVUQUEOEclAiEAzUAfpytIRNNvZo1XHHTOS7z84rU9BplwEHVckPyBL/E="}]}}, "0.1.2": {"name": "eyes", "description": "a customizable value inspector", "url": "http://github.com/cloudhead/eyes.js", "keywords": ["inspector", "debug", "inspect", "print"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "licenses": ["MIT"], "dependencies": {}, "main": "./lib/eyes", "version": "0.1.2", "directories": {"lib": "./lib", "test": "./test"}, "engines": {"node": "> 0.1.90"}, "_id": "eyes@0.1.2", "_nodeSupported": true, "_npmVersion": "0.2.7-2", "_nodeVersion": "v0.3.1-pre", "dist": {"tarball": "https://registry.npmjs.org/eyes/-/eyes-0.1.2.tgz", "shasum": "fb44ee367b3923f459194eaa720670f929c5d4be", "integrity": "sha512-9pxAiIR22z3UdJDsc60zSUkvbiw9iRAxZrJ8Hybs1id9u7vGfNLWPEBhxkH65fax+kLjy04XDqAIZgcR9ZOuTw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICnrFfJ/+LkQHesGyaTp9iTtKUTTSKLhwbQPgW+iZLy6AiEA6TPW+Omji3MNTM/ZJcOH2HcmdJrLzLkm75B92ABjH1I="}]}}, "0.1.3": {"name": "eyes", "description": "a customizable value inspector", "url": "http://github.com/cloudhead/eyes.js", "keywords": ["inspector", "debug", "inspect", "print"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "licenses": ["MIT"], "dependencies": {}, "main": "./lib/eyes", "version": "0.1.3", "directories": {"lib": "./lib", "test": "./test"}, "engines": {"node": "> 0.1.90"}, "_id": "eyes@0.1.3", "_nodeSupported": true, "_npmVersion": "0.2.7-2", "_nodeVersion": "v0.3.1-pre", "dist": {"tarball": "https://registry.npmjs.org/eyes/-/eyes-0.1.3.tgz", "shasum": "0636928c9b27091250fdbe57b5ffcdb7593dc831", "integrity": "sha512-zDi/9KZYxmhU8avOxO717D0ydwkH983c+yXqOvoxrYllfOnI/qDy3+Dvr/h9tIcZqNFu1A1QRGMXcOgsMH7eUA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGxcXKeVgi7mM+sQgQUekFwd7LzBKH+VNzhQ/T3/d3oxAiEA3BJi0EgCV10cD/IsriytwelQmweWFlemcsgffYrtDlw="}]}}, "0.1.4": {"name": "eyes", "description": "a customizable value inspector", "url": "http://github.com/cloudhead/eyes.js", "keywords": ["inspector", "debug", "inspect", "print"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "licenses": ["MIT"], "dependencies": {}, "main": "./lib/eyes", "version": "0.1.4", "directories": {"lib": "./lib", "test": "./test"}, "engines": {"node": "> 0.1.90"}, "_id": "eyes@0.1.4", "_nodeSupported": true, "_npmVersion": "0.2.7-2", "_nodeVersion": "v0.3.1-pre", "dist": {"tarball": "https://registry.npmjs.org/eyes/-/eyes-0.1.4.tgz", "shasum": "2ca60e31d6a474f48e0e477b3b9cde4171ceb512", "integrity": "sha512-clBcHv59P8H5dax2C6XiAs3CTmIsemqqgRjUdv7MEbRTIefVxYDfCrm+RH9U4EjfnNLffNTA88YANpPZ20A4hg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC8hImXGYpew7wudP5r996u24jlxvaoTZHQltmMti2VQAiB4JMuaWvL0f9Dub/WcSHZA0kfj6Eo3nx+0W6LaEzbLFg=="}]}}, "0.1.5": {"name": "eyes", "description": "a customizable value inspector", "url": "http://github.com/cloudhead/eyes.js", "keywords": ["inspector", "debug", "inspect", "print"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "licenses": ["MIT"], "dependencies": {}, "main": "./lib/eyes", "version": "0.1.5", "directories": {"lib": "./lib", "test": "./test"}, "engines": {"node": "> 0.1.90"}, "_id": "eyes@0.1.5", "_nodeSupported": true, "_npmVersion": "0.2.7-2", "_nodeVersion": "v0.3.1-pre", "dist": {"tarball": "https://registry.npmjs.org/eyes/-/eyes-0.1.5.tgz", "shasum": "de73c55d577c215ebdbd9609c99f6f8123f47fb8", "integrity": "sha512-1/58ZI+nkMtwhRTfOOzrivAGYT2O31R9s64xiBbOH858nukVhImvFK0F9uSD1pijGnwIi4L3uGTHWuwLZ/Zdvw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDN+Yq6R1xgfSZ3z822PxYqyRcweCsxgRCWAio0RbgmEgIgR82o2R6gZN+e3FEFkoA0XvwYib9oBTrf8acv4ACcAjY="}]}}, "0.1.6": {"name": "eyes", "description": "a customizable value inspector", "url": "http://github.com/cloudhead/eyes.js", "keywords": ["inspector", "debug", "inspect", "print"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [], "licenses": ["MIT"], "dependencies": {}, "main": "./lib/eyes", "version": "0.1.6", "directories": {"lib": "./lib", "test": "./test"}, "engines": {"node": "> 0.1.90"}, "_id": "eyes@0.1.6", "_nodeSupported": true, "_npmVersion": "0.2.7-2", "_nodeVersion": "v0.3.1-pre", "dist": {"tarball": "https://registry.npmjs.org/eyes/-/eyes-0.1.6.tgz", "shasum": "c93c22e7e75d9cef96cfb0b0d80a41bbb92ae61c", "integrity": "sha512-n50aNYZPJucRPMUxEO6KEGc0EAiYf0XGsNHbDlOkH0kArVdqbDbSaD7oLqdwrq8mX2VzAc4rI6jF7lhgIpl1qA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHR4rDs1J4Uud8vqv+f54wFNGpvMrW4I2L8pCiev311tAiEAjbhz2pUUaskkfsLcG9GpMQrjX3B3mzCcE17A9f47LE0="}]}}, "0.1.7": {"name": "eyes", "description": "a customizable value inspector", "url": "http://github.com/cloudhead/eyes.js", "keywords": ["inspector", "debug", "inspect", "print"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "licenses": ["MIT"], "dependencies": {}, "main": "./lib/eyes", "version": "0.1.7", "scripts": {"test": "node test/*-test.js"}, "directories": {"lib": "./lib", "test": "./test"}, "engines": {"node": "> 0.1.90"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "_id": "eyes@0.1.7", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"shasum": "e9605b91d254e7375a68ee93e2a5937956b058fb", "tarball": "https://registry.npmjs.org/eyes/-/eyes-0.1.7.tgz", "integrity": "sha512-6dCujwLK4V2/JrVpXJz8zrZD1mCcLo7Hbj9eCawiN6Lm6AB42BfDTPXEcI9LJsCetE5BX5IMbRA5R6/iZGyvkA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGog/R84SuNGsesIM2bpI3XMeTOUDwlmHuiB0OUmT9atAiEApz/Op++ovVbMU2sTtWnTCo5gPlnsUWehrtl9j3s7YG0="}]}, "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}]}, "0.1.8": {"name": "eyes", "description": "a customizable value inspector", "url": "http://github.com/cloudhead/eyes.js", "keywords": ["inspector", "debug", "inspect", "print"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "licenses": ["MIT"], "main": "./lib/eyes", "version": "0.1.8", "scripts": {"test": "node test/*-test.js"}, "directories": {"lib": "./lib", "test": "./test"}, "engines": {"node": "> 0.1.90"}, "_id": "eyes@0.1.8", "dist": {"shasum": "62cf120234c683785d902348a800ef3e0cc20bc0", "tarball": "https://registry.npmjs.org/eyes/-/eyes-0.1.8.tgz", "integrity": "sha512-GipyPsXO1anza0AOZdy69Im7hGFCNB7Y/NGjDlZGJ3GJJLtwNSb2vrzYrTYJRrRloVx7pl+bhUaTB8yiccPvFQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHnBLAvycWcBLPp3svFEc8ITwKsNeXsCp8dX2GJAZipNAiB28Dp2SfWYpH76+fzm0f48qEvZ8hQnb2OtBSKFB+8aeQ=="}]}, "_npmVersion": "1.1.53", "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}]}}, "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "time": {"modified": "2023-07-10T23:19:08.929Z", "created": "2011-12-09T00:10:20.093Z", "0.1.1": "2011-12-09T00:10:20.093Z", "0.1.2": "2011-12-09T00:10:20.093Z", "0.1.3": "2011-12-09T00:10:20.093Z", "0.1.4": "2011-12-09T00:10:20.093Z", "0.1.5": "2011-12-09T00:10:20.093Z", "0.1.6": "2011-12-09T00:10:20.093Z", "0.1.7": "2011-12-09T00:10:32.764Z", "0.1.8": "2012-08-24T18:26:03.778Z"}, "users": {"chilts": true, "dodo": true, "tellnes": true, "fgribreau": true, "pid": true, "strathausen": true, "eins78": true, "themiddleman": true, "tunnckocore": true, "yianisn": true, "amirmehmood": true, "anaphase": true, "kontrax": true, "mccoyjordan": true, "antixrist": true, "emyann": true, "amaynut": true, "mrsarm": true, "dm7": true, "lwdgit": true, "flumpus-dev": true}, "readme": "eyes\n====\n\na customizable value inspector for Node.js\n\nsynopsis\n--------\n\nI was tired of looking at cluttered output in the console -- something needed to be done,\n`sys.inspect()` didn't display regexps correctly, and was too verbose, and I had an hour or two to spare. \nSo I decided to have some fun. _eyes_ were born.\n\n![eyes-ss](http://dl.dropbox.com/u/251849/eyes-js-ss.gif)\n\n_example of the output of a user-customized eyes.js inspector_\n\n*eyes* also deals with circular objects in an intelligent way, and can pretty-print object literals.\n\nusage\n-----\n\n    var inspect = require('eyes').inspector({styles: {all: 'magenta'}});\n\n    inspect(something); // inspect with the settings passed to `inspector`\n\nor\n\n    var eyes = require('eyes');\n\n    eyes.inspect(something); // inspect with the default settings\n\nyou can pass a _label_ to `inspect()`, to keep track of your inspections:\n\n    eyes.inspect(something, \"a random value\");\n\nIf you want to return the output of eyes without printing it, you can set it up this way:\n\n    var inspect = require('eyes').inspector({ stream: null });\n\n    sys.puts(inspect({ something: 42 }));\n\ncustomization\n-------------\n\nThese are the default styles and settings used by _eyes_.\n\n    styles: {                 // Styles applied to stdout\n        all:     'cyan',      // Overall style applied to everything\n        label:   'underline', // Inspection labels, like 'array' in `array: [1, 2, 3]`\n        other:   'inverted',  // Objects which don't have a literal representation, such as functions\n        key:     'bold',      // The keys in object literals, like 'a' in `{a: 1}`\n        special: 'grey',      // null, undefined...\n        string:  'green',\n        number:  'magenta',\n        bool:    'blue',      // true false\n        regexp:  'green',     // /\\d+/\n    },\n    \n    pretty: true,             // Indent object literals\n    hideFunctions: false,     // Don't output functions at all\n    stream: process.stdout,   // Stream to write to, or null\n    maxLength: 2048           // Truncate output if longer\n\nYou can overwrite them with your own, by passing a similar object to `inspector()` or `inspect()`.\n\n    var inspect = require('eyes').inspector({\n        styles: {\n            all: 'magenta',\n            special: 'bold'\n        },\n        maxLength: 512\n    });\n\n", "readmeFilename": ""}