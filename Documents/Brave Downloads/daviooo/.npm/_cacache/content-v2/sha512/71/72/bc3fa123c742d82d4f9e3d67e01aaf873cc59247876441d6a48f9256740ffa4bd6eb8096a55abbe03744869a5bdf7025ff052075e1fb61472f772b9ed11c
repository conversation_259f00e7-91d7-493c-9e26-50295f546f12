{"_id": "stack-trace", "_rev": "53-013735c4128ff3495578f3901832c58f", "name": "stack-trace", "description": "Get v8 stack traces as an array of CallSite objects.", "dist-tags": {"latest": "1.0.0-pre2"}, "versions": {"0.0.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "stack-trace", "description": "Get v8 stack traces as an array of CallSite objects.", "version": "0.0.1", "homepage": "https://github.com/felixge/node-stack-trace", "repository": {"type": "git", "url": "git://github.com/felixge/node-stack-trace.git"}, "main": "./lib/stack-trace", "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/stack-trace/0.0.1/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "stack-trace@0.0.1", "_engineSupported": true, "_npmVersion": "1.0.14", "_nodeVersion": "v0.4.9-pre", "_defaultsLoaded": true, "dist": {"shasum": "6b4c583666e4996a3616008b67ef9f5247101271", "tarball": "https://registry.npmjs.org/stack-trace/-/stack-trace-0.0.1.tgz", "integrity": "sha512-kbtm51VmeqPJDUMJKDAOfmDGKuC1OaLGpKlmXHUj7b0JyNpdxBblyRV9m/6Tdn+Xwh/t5z+5R72HaCvEZrDAYg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEMCIAhhp+ZBBAZ95WKtO/XH6YidOwH4IF9391ElRVZbCAdpAh9r8jC9S6WP2pxGUkc12kI8DIB2gyRX8pZ211VYx/Le"}]}, "scripts": {}, "directories": {}}, "0.0.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "stack-trace", "description": "Get v8 stack traces as an array of CallSite objects.", "version": "0.0.2", "homepage": "https://github.com/felixge/node-stack-trace", "repository": {"type": "git", "url": "git://github.com/felixge/node-stack-trace.git"}, "main": "./lib/stack-trace", "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {"far": "0.0.3"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/stack-trace/0.0.2/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "stack-trace@0.0.2", "_engineSupported": true, "_npmVersion": "1.0.14", "_nodeVersion": "v0.4.10-pre", "_defaultsLoaded": true, "dist": {"shasum": "17df29a3e16f2b1f424b71d4a1bdcf5a18aed2e5", "tarball": "https://registry.npmjs.org/stack-trace/-/stack-trace-0.0.2.tgz", "integrity": "sha512-eFVH6Y3IL+xCuho3wuVO713S48X9sRvuS+4JBacutByCFZRJbcncEB/uVyf4x3/1CHBqguK9/pjWuzSu7/eWqw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDU7nZ5H6Piq3jq7n7G4Wcn8LJ03/e5U2sPTONc6FD9QgIgIVOY6TlOzipV/0RQOZaZOZCsPPxG2p7lMcwyOijgLUY="}]}, "scripts": {}, "directories": {}}, "0.0.3": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "stack-trace", "description": "Get v8 stack traces as an array of CallSite objects.", "version": "0.0.3", "homepage": "https://github.com/felixge/node-stack-trace", "repository": {"type": "git", "url": "git://github.com/felixge/node-stack-trace.git"}, "main": "./lib/stack-trace", "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {"far": "0.0.3"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/stack-trace/0.0.3/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "stack-trace@0.0.3", "_engineSupported": true, "_npmVersion": "1.0.14", "_nodeVersion": "v0.4.10-pre", "_defaultsLoaded": true, "dist": {"shasum": "b10dc24b9e86a242cc69fbabf0f60d6284b20e12", "tarball": "https://registry.npmjs.org/stack-trace/-/stack-trace-0.0.3.tgz", "integrity": "sha512-jGAvmPO8g42LUlYK05UDBK7F2kKxd8N/vNIaLx9H5TJshnWjUOm/AcutnUuE+L9qOsc6Ai8rmYnfyjnGqJ2Dvg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDizgNI3EQr5tIB5g5jRsyiJnYHAYIVkpRPocH5cpSeagIgakcq3VvXORFA7J9+1fOQ2RK/Uxdo11UgOD2WlRvTKis="}]}, "scripts": {}, "directories": {}}, "0.0.4": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "stack-trace", "description": "Get v8 stack traces as an array of CallSite objects.", "version": "0.0.4", "homepage": "https://github.com/felixge/node-stack-trace", "repository": {"type": "git", "url": "git://github.com/felixge/node-stack-trace.git"}, "main": "./lib/stack-trace", "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {"far": "0.0.3"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/stack-trace/0.0.4/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "stack-trace@0.0.4", "_engineSupported": true, "_npmVersion": "1.0.15", "_nodeVersion": "v0.4.10-pre", "_defaultsLoaded": true, "dist": {"shasum": "f278a4dd79608f5ceb80f4fd7064842934a40f4a", "tarball": "https://registry.npmjs.org/stack-trace/-/stack-trace-0.0.4.tgz", "integrity": "sha512-km9C+IQfV67kZYlB4ISpOJSjMgpSS9YAFgJCW1TI+0cF6u7QCYQyYLph0dhWU9pj/imYRVbPj1ph6VdaaToPZg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDqK5w+RensF2zkZTDnph1MOISp9g5GOzfhQQ+E/7FgyQIhAMaDn/hMCbp3AHYbMykw0g2dm1cy6VQ8Ytti4+VqJrxn"}]}, "scripts": {}, "directories": {}}, "0.0.5": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "stack-trace", "description": "Get v8 stack traces as an array of CallSite objects.", "version": "0.0.5", "homepage": "https://github.com/felixge/node-stack-trace", "repository": {"type": "git", "url": "git://github.com/felixge/node-stack-trace.git"}, "main": "./lib/stack-trace", "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {"far": "0.0.3", "long-stack-traces": "0.1.2"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/stack-trace/0.0.5/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "stack-trace@0.0.5", "_engineSupported": true, "_npmVersion": "1.0.18", "_nodeVersion": "v0.4.10-pre", "_defaultsLoaded": true, "dist": {"shasum": "996a48767d9fd68834012dec500abaefcd49ac3c", "tarball": "https://registry.npmjs.org/stack-trace/-/stack-trace-0.0.5.tgz", "integrity": "sha512-EcDbbWgI2r6/K5bCBvxueUnM2fbAy/1Wgl8dTxaciOiSDdey2CjYGxj4wx6I8Qm1Uqw5F8YopYAP58HAkUEDhA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC4QqDjpO/hroMwzLNaQajEUMUSOQzhCZ5oVsqlSDuvLwIhAO2aOzkls0jhVVc8ZJioXGsmaR+pMrZzyan9nj2LxUCi"}]}, "scripts": {}, "directories": {}}, "0.0.6": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "stack-trace", "description": "Get v8 stack traces as an array of CallSite objects.", "version": "0.0.6", "homepage": "https://github.com/felixge/node-stack-trace", "repository": {"type": "git", "url": "git://github.com/felixge/node-stack-trace.git"}, "main": "./lib/stack-trace", "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {"far": "0.0.3", "long-stack-traces": "0.1.2"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/stack-trace/0.0.6/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "stack-trace@0.0.6", "_engineSupported": true, "_npmVersion": "1.0.22", "_nodeVersion": "v0.4.10-pre", "_defaultsLoaded": true, "dist": {"shasum": "1e719bd6a2629ff09c189e17a9ef902a94fc5db0", "tarball": "https://registry.npmjs.org/stack-trace/-/stack-trace-0.0.6.tgz", "integrity": "sha512-5/6uZt7RYjjAl8z2j1mXWAewz+I4Hk2/L/3n6NRLIQ31+uQ7nMd9O6G69QCdrrufHv0QGRRHl/jwUEGTqhelTA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCDo25DGxtolg7TwLi6+W4uq1/Doz5uHXH/eShKHHn+wAIgHnLrDCGr0BfFVFyrEw1fRuCbaS65gujgvDPRhFVNmaY="}]}, "scripts": {}, "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.0.7": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "stack-trace", "description": "Get v8 stack traces as an array of CallSite objects.", "version": "0.0.7", "homepage": "https://github.com/felixge/node-stack-trace", "repository": {"type": "git", "url": "git://github.com/felixge/node-stack-trace.git"}, "main": "./lib/stack-trace", "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {"far": "0.0.3", "long-stack-traces": "0.1.2"}, "bugs": {"url": "https://github.com/felixge/node-stack-trace/issues"}, "_id": "stack-trace@0.0.7", "dist": {"shasum": "c72e089744fc3659f508cdce3621af5634ec0fff", "tarball": "https://registry.npmjs.org/stack-trace/-/stack-trace-0.0.7.tgz", "integrity": "sha512-L4KzPxLdea3qWVPVrddj+omHqDCv/xoA59XCi7/hIkS9qGQ3WOH2uVzOnkITreWw9+VevjJna0U3Jk7KJT8Gfw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDwnLyBb4Sg2dbNfxEq7A9MW0M0Q7XQn575RHX0mQCs/QIhAJKiJsu+xfGH/RJRymZmpq2GsFfa7W0LhzKNxa5oOOuV"}]}, "_from": ".", "_npmVersion": "1.2.25", "_npmUser": {"name": "tim-smart", "email": "<EMAIL>"}, "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "tim-smart", "email": "<EMAIL>"}], "directories": {}}, "0.0.8": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "stack-trace", "description": "Get v8 stack traces as an array of CallSite objects.", "version": "0.0.8", "homepage": "https://github.com/felixge/node-stack-trace", "repository": {"type": "git", "url": "git://github.com/felixge/node-stack-trace.git"}, "main": "./lib/stack-trace", "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {"far": "0.0.3", "long-stack-traces": "0.1.2"}, "bugs": {"url": "https://github.com/felixge/node-stack-trace/issues"}, "_id": "stack-trace@0.0.8", "dist": {"shasum": "8a9c30543ed9697f77739c67b053d8b030d86f3e", "tarball": "https://registry.npmjs.org/stack-trace/-/stack-trace-0.0.8.tgz", "integrity": "sha512-SsZBaT8LqZ9xZRutPLI+z1xRcuy1y3TWiZ0hldN9LSLKH9y1z1MJQZhj6pF2lmIjLBDcErZ5EYiGXvlgJVZqgQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCaZKLC6aQOz4LlI6sB+ynqxIS2HWni0gfujNRh7t0KegIhAIuABhS5JsO79C3TQfgS64LUKUwc0aMbnXXI2qEp/d65"}]}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.0.9": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "stack-trace", "description": "Get v8 stack traces as an array of CallSite objects.", "version": "0.0.9", "homepage": "https://github.com/felixge/node-stack-trace", "repository": {"type": "git", "url": "git://github.com/felixge/node-stack-trace.git"}, "main": "./lib/stack-trace", "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {"far": "0.0.3", "long-stack-traces": "0.1.2"}, "bugs": {"url": "https://github.com/felixge/node-stack-trace/issues"}, "_id": "stack-trace@0.0.9", "dist": {"shasum": "a8f6eaeca90674c333e7c43953f275b451510695", "tarball": "https://registry.npmjs.org/stack-trace/-/stack-trace-0.0.9.tgz", "integrity": "sha512-vjUc6sfgtgY0dxCdnc40mK6Oftjo9+2K8H/NG81TMhgL392FtiPA9tn9RLyTxXmTLPJPjF3VyzFp6bsWFLisMQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCFpBB+9zHA0LdQIWLibfTmdk5ZK8xT7vFSMS7h39gx3AIgC4P6+y31H3dGQ2zmI4e1rELU0e1Kjm85J69LsGjcLe8="}]}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "tim-smart", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.0.10": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "stack-trace", "description": "Get v8 stack traces as an array of CallSite objects.", "version": "0.0.10", "homepage": "https://github.com/felixge/node-stack-trace", "repository": {"type": "git", "url": "git://github.com/felixge/node-stack-trace.git"}, "main": "./lib/stack-trace", "engines": {"node": "*"}, "license": "MIT", "dependencies": {}, "devDependencies": {"far": "0.0.3", "long-stack-traces": "0.1.2"}, "gitHead": "9a11c5294e37e8c1e8ca0f402711eb100bc7be5e", "bugs": {"url": "https://github.com/felixge/node-stack-trace/issues"}, "_id": "stack-trace@0.0.10", "scripts": {}, "_shasum": "547c70b347e8d32b4e108ea1a2a159e5fdde19c0", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.10.0", "_npmUser": {"name": "felix<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "gaj<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tim-smart", "email": "<EMAIL>"}], "dist": {"shasum": "547c70b347e8d32b4e108ea1a2a159e5fdde19c0", "tarball": "https://registry.npmjs.org/stack-trace/-/stack-trace-0.0.10.tgz", "integrity": "sha512-KGzahc7puUKkzyMt+IqAep+TVNbKP+k2Lmwhub39m1AsTSkaDutx56aDCo+HLDzf/D26BIHTJWNiTG1KAJiQCg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCevr7g/4ORxxPF2gHnoBV4hM31rXvyum1agCkzPOcGJwIgI3ZoG8VxK9wDYyM5c0SD2fA/JCgf/H8976k+ygER4Dc="}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/stack-trace-0.0.10.tgz_1494918433070_0.05463112820871174"}, "directories": {}}, "1.0.0-pre1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "stack-trace", "description": "Get v8 stack traces as an array of CallSite objects.", "version": "1.0.0-pre1", "homepage": "https://github.com/felixge/node-stack-trace", "repository": {"type": "git", "url": "git://github.com/felixge/node-stack-trace.git"}, "type": "module", "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "jest": {"testEnvironment": "node", "transform": {"^.+\\.js$": "babel-jest"}}, "scripts": {"test": "jest", "release": "git push && git push --tags && npm publish"}, "engines": {"node": "16"}, "license": "MIT", "devDependencies": {"@babel/preset-env": "^7.14.1", "babel-jest": "^26.6.3", "jest": "^26.6.3", "long-stack-traces": "0.1.2"}, "gitHead": "f71287470390c51b0eba771e25aa429c6aaf5861", "bugs": {"url": "https://github.com/felixge/node-stack-trace/issues"}, "_id": "stack-trace@1.0.0-pre1", "_nodeVersion": "16.0.0", "_npmVersion": "7.10.0", "dist": {"integrity": "sha512-biM7OwS3J2hcou7tfozHcsqhJZxX5pqMMqe/Zr6stw9uVn8Gh7ct3eFR9Gb66BBi/ToSeOgk4FsjKgZVrDIyew==", "shasum": "3eb3c7a86ceaaa67f8b1cf2eaab4a58ad68348d0", "tarball": "https://registry.npmjs.org/stack-trace/-/stack-trace-1.0.0-pre1.tgz", "fileCount": 9, "unpackedSize": 18641, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJglSOkCRA9TVsSAnZWagAAFakP/3DWNqU7HmKP3CCD35cN\nEtCK9uC16tvbOOBTnQjL0UQC1QKqNhoJ6wRWLX2GvqiQWmmZhGi873ADhDIa\n4NZlG3y6FsSBFDEjQSzMxrjhAAJg1l9iOcVrXw7XXIxV5WN3q69zU+yIQ3TC\n+4cS5x56st55imYubvkc4ibbUhK97Uny/9SSJshq9NFlyPXInHHl8kmFMT2l\nans2mBpAP+i/1kG9o/1QU1ZHRU2sEMfmQOAGNonhdJIf3YCovYMu8kPXX70P\nesio0Ep3NE3KP2UWTQPF+zgKMPGAmD8cXOC3XHDKLDVFIfLiTc6JxYd14z6l\n4sICNk8KCEQw2Nw6Uzsoiuo0GY8WSRj1nQHH401Vox+zwuppqydjJhIi7Cch\n5I+8YdVIE+fVoIkVow8xuLDr0RK8CyI6A7eVL1NZuZ8sympK2vrWyILDQFVz\npcR3Zp5+tn+LbfTHPniPFiQGpDHrvfQJaOavEXwJQhCxf77p39W7hb9v2DaQ\nlQJJSnPiN1ehDb7QyJzRFM8bp3dW5hewc/yew5PMLd6dHw/T+NOvEmZlHol6\nTh1vCwX/dP8TIVXfuoIHyOHKAeeK0ce18iDsSn64pV5n7z67+wDk5pJztjFG\n9/xN3nhiW3kcSJiIYZy7IkcOJDPTcfd6+NEzwlGMmrc1iPkbbrANKlry+dmY\nRDCE\r\n=fVjU\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA1NEe3IwQdapoD2Jrj7CQ4b0QUeBleY69QS6NWwjZTUAiBtzsDG+h/qpTIjZXHw4I4x5us7C/4ifmBH+xNELzqHEQ=="}]}, "_npmUser": {"name": "niemyjski", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tim-smart", "email": "<EMAIL>"}, {"name": "gaj<PERSON>", "email": "<EMAIL>"}, {"name": "niemyjski", "email": "<EMAIL>"}, {"name": "kjg", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/stack-trace_1.0.0-pre1_1620386723889_0.1812475461328933"}, "_hasShrinkwrap": false}, "1.0.0-pre2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "stack-trace", "description": "Get v8 stack traces as an array of CallSite objects.", "version": "1.0.0-pre2", "homepage": "https://github.com/felixge/node-stack-trace", "repository": {"type": "git", "url": "git://github.com/felixge/node-stack-trace.git"}, "type": "module", "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "jest": {"testEnvironment": "node", "transform": {"^.+\\.js$": "babel-jest"}}, "scripts": {"test": "jest", "release": "git push && git push --tags && npm publish"}, "engines": {"node": ">=16"}, "license": "MIT", "devDependencies": {"@babel/preset-env": "^7.14.1", "babel-jest": "^26.6.3", "jest": "^26.6.3", "long-stack-traces": "0.1.2"}, "gitHead": "ba06dcdb50d465cd440d84a563836e293b360427", "bugs": {"url": "https://github.com/felixge/node-stack-trace/issues"}, "_id": "stack-trace@1.0.0-pre2", "_nodeVersion": "19.6.0", "_npmVersion": "9.4.2", "dist": {"integrity": "sha512-2ztBJRek8IVofG9DBJqdy2N5kulaacX30Nz7xmkYF6ale9WBVmIy6mFBchvGX7Vx/MyjBhx+Rcxqrj+dbOnQ6A==", "shasum": "46a83a79f1b287807e9aaafc6a5dd8bcde626f9c", "tarball": "https://registry.npmjs.org/stack-trace/-/stack-trace-1.0.0-pre2.tgz", "fileCount": 7, "unpackedSize": 18455, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE/EE1Liz9V2srZJcH4tZpT12sXxiSRxmgUQZZnMXx6EAiEA7a65hlBH2potYUDFGCWBCP+AOtyhwty+Ll1crUkLf9Y="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5QbrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrRWA/9FQW2A97gb9BDxlj/bKt5UdtBwIKWvqUjlqMFAGwoMKyOuJab\r\n+gUm3cOdG934wkRK8pt9a1wp4mMqif8C7YfTwu7+T9twipxskV3pDnC2wymd\r\nZNTX7i3BbJ85EQ7LsSx58CEOzjnc6AfIsJEsiVBzU9ObOdbcaHX8P2iR1ynq\r\n5RNSa5k8T96/SPU2vIpCOs2V8ZO9MmUSQWe2BG22HTsqDCoUQ5n0wEBCLedf\r\n/C1/RCCEHUPvi74ahBxto63OdGrG1AlnK/Lyvl+txUV/NUKqGf9bq/uGRRR7\r\ne7UNdgFY1ZxOJaKTT4j+NAY2ZwdlU47RD7RosCc98ANbcPMJ6lBEb6QLMHAC\r\nIlGeM548A7KLmp95zT5z/LaT2Ham7DKZcAOf97QMBhtXfpwzvd9TwfA+NPJE\r\n+tiM1xulzjHhY6DUtZiWK6pYtUHvlJttKEN+Q1a0LRTQubhwCOQiKTAItw+8\r\nLOPHey3zS/pCSWZDvYdRH0FRztOnd4duSiaqyTtMIvd2qHIWEZB9Rglhmvxl\r\nxslvPm3X2ywIp7JOarJTRt1NXjQnwZQLtVVbodk73YW0D9XpxztoPoVbou7N\r\nIBN52NDsUhNHGimg8jLVXVgrJ1peWu5mbcU0Q8vX+MaW6AN8CPtXN/swweBZ\r\nBgCW6zQnG5YNnXkz5EiI+OCcw8CGBvidH6w=\r\n=6ZM2\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "niemyjski", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tim-smart", "email": "<EMAIL>"}, {"name": "gaj<PERSON>", "email": "<EMAIL>"}, {"name": "niemyjski", "email": "<EMAIL>"}, {"name": "kjg", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/stack-trace_1.0.0-pre2_1675953899496_0.982217209243438"}, "_hasShrinkwrap": false}}, "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tim-smart", "email": "<EMAIL>"}, {"name": "gaj<PERSON>", "email": "<EMAIL>"}, {"name": "niemyjski", "email": "<EMAIL>"}, {"name": "kjg", "email": "<EMAIL>"}], "time": {"modified": "2023-07-12T04:07:46.546Z", "created": "2011-06-25T16:09:15.530Z", "0.0.1": "2011-06-25T16:09:16.216Z", "0.0.2": "2011-07-13T08:16:56.532Z", "0.0.3": "2011-07-13T21:31:27.910Z", "0.0.4": "2011-07-17T08:38:37.454Z", "0.0.5": "2011-07-20T16:10:40.557Z", "0.0.6": "2011-08-01T16:58:53.033Z", "0.0.7": "2013-07-17T20:55:05.848Z", "0.0.8": "2014-02-01T15:11:47.010Z", "0.0.9": "2014-02-20T10:40:57.111Z", "0.0.10": "2017-05-16T07:07:13.840Z", "1.0.0-pre1": "2021-05-07T11:25:24.006Z", "1.0.0-pre2": "2023-02-09T14:44:59.653Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "repository": {"type": "git", "url": "git://github.com/felixge/node-stack-trace.git"}, "readme": "# stack-trace\r\n\r\n[![Build Status](https://travis-ci.org/felixge/node-stack-trace.svg?branch=master)](https://travis-ci.org/felixge/node-stack-trace)\r\n\r\nGet v8 stack traces as an array of CallSite objects.\r\n\r\n## Install\r\n\r\n``` bash\r\nnpm install stack-trace\r\n```\r\n\r\n## Usage\r\n\r\nThe stack-trace module makes it easy for you to capture the current stack:\r\n\r\n```javascript\r\nimport { get } from 'stack-trace';\r\nconst trace = get();\r\n\r\nexpect(trace[0].getFileName()).toBe(__filename);\r\n```\r\n\r\nHowever, sometimes you have already popped the stack you are interested in,\r\nand all you have left is an `Error` object. This module can help:\r\n\r\n```javascript\r\nimport { parse } from 'stack-trace';\r\nconst err = new Error('something went wrong');\r\nconst trace = parse(err);\r\n\r\nexpect(trace[0].getFileName()).toBe(__filename);\r\n```\r\n\r\nPlease note that parsing the `Error#stack` property is not perfect, only\r\ncertain properties can be retrieved with it as noted in the API docs below.\r\n\r\n## Long stack traces\r\n\r\nstack-trace works great with [long-stack-traces][], when parsing an `err.stack`\r\nthat has crossed the event loop boundary, a `CallSite` object returning\r\n`'----------------------------------------'` for `getFileName()` is created.\r\nAll other methods of the event loop boundary call site return `null`.\r\n\r\n[long-stack-traces]: https://github.com/tlrobinson/long-stack-traces\r\n\r\n## API\r\n\r\n### stackTrace.get([belowFn])\r\n\r\nReturns an array of `CallSite` objects, where element `0` is the current call\r\nsite.\r\n\r\nWhen passing a function on the current stack as the `belowFn` parameter, the\r\nreturned array will only include `CallSite` objects below this function.\r\n\r\n### stackTrace.parse(err)\r\n\r\nParses the `err.stack` property of an `Error` object into an array compatible\r\nwith those returned by `stackTrace.get()`. However, only the following methods\r\nare implemented on the returned `CallSite` objects.\r\n\r\n* getTypeName\r\n* getFunctionName\r\n* getMethodName\r\n* getFileName\r\n* getLineNumber\r\n* getColumnNumber\r\n* isNative\r\n\r\nNote: Except `getFunctionName()`, all of the above methods return exactly the\r\nsame values as you would get from `stackTrace.get()`. `getFunctionName()`\r\nis sometimes a little different, but still useful.\r\n\r\n### CallSite\r\n\r\nThe official v8 CallSite object API can be found [here][https://github.com/v8/v8/wiki/Stack-Trace-API#customizing-stack-traces]. A quick\r\nexcerpt:\r\n\r\n> A CallSite object defines the following methods:\r\n>\r\n> * **getThis**: returns the value of this\r\n> * **getTypeName**: returns the type of this as a string. This is the name of the function stored in the constructor field of this, if available, otherwise the object's [[Class]] internal property.\r\n> * **getFunction**: returns the current function\r\n> * **getFunctionName**: returns the name of the current function, typically its name property. If a name property is not available an attempt will be made to try to infer a name from the function's context.\r\n> * **getMethodName**: returns the name of the property of this or one of its prototypes that holds the current function\r\n> * **getFileName**: if this function was defined in a script returns the name of the script\r\n> * **getLineNumber**: if this function was defined in a script returns the current line number\r\n> * **getColumnNumber**: if this function was defined in a script returns the current column number\r\n> * **getEvalOrigin**: if this function was created using a call to eval returns a CallSite object representing the location where eval was called\r\n> * **isToplevel**: is this a toplevel invocation, that is, is this the global object?\r\n> * **isEval**: does this call take place in code defined by a call to eval?\r\n> * **isNative**: is this call in native V8 code?\r\n> * **isConstructor**: is this a constructor call?\r\n\r\n[v8stackapi]: https://v8.dev/docs/stack-trace-api\r\n\r\n## License\r\n\r\nstack-trace is licensed under the MIT license.\r\n", "readmeFilename": "Readme.md", "homepage": "https://github.com/felixge/node-stack-trace", "bugs": {"url": "https://github.com/felixge/node-stack-trace/issues"}, "users": {"davepoon": true, "ralucas": true, "eush77": true, "russt": true, "monolithed": true, "bpatel": true, "fedor": true, "bsnote": true, "xgheaven": true, "zombinary": true, "yokubee": true, "nigel0913": true, "kirilv": true, "linuxenko": true, "nmccready": true, "masonwan": true, "jondotsoy": true, "shanewholloway": true, "meeh": true, "xinwangwang": true, "isayme": true, "flumpus-dev": true}, "license": "MIT"}