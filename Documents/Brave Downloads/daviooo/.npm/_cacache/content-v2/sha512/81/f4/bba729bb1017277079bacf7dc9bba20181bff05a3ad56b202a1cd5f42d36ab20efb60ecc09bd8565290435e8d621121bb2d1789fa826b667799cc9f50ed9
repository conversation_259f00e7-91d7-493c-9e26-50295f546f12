{"_id": "inflight", "_rev": "54-93e9536ff91a13da8b35764ab06ad8c5", "name": "inflight", "description": "Add callbacks to requests in flight to avoid async duplication", "dist-tags": {"latest": "1.0.6"}, "versions": {"1.0.0": {"name": "inflight", "version": "1.0.0", "description": "Add callbacks to requests in flight to avoid async duplication", "main": "inflight.js", "dependencies": {"once": "^1.3.0"}, "devDependencies": {"tap": "^0.4.10"}, "scripts": {"test": "tap test.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/inflight"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/inflight/issues"}, "homepage": "https://github.com/isaacs/inflight", "license": "ISC", "_id": "inflight@1.0.0", "_shasum": "002f6df5129a7f4bf3e1b471bec5334804a2dd99", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "002f6df5129a7f4bf3e1b471bec5334804a2dd99", "tarball": "https://registry.npmjs.org/inflight/-/inflight-1.0.0.tgz", "integrity": "sha512-IquTv6O0kQa09r/X61OrgkA0JVapoBcggufuyqlshPdh/KAtr8ybVxQdkMGp1Wj4upEYlNIhEoji6Rd4BPFs0g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDD9XXIHGNkWE4pTDjoSjttlmtE/S0eFft+l1lclHQSQQIgAgb1AoAYeyGxs/m1lxkbfnFidtgqFub0Dzvlu/wLAEU="}]}, "directories": {}, "deprecated": "This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful."}, "1.0.1": {"name": "inflight", "version": "1.0.1", "description": "Add callbacks to requests in flight to avoid async duplication", "main": "inflight.js", "dependencies": {"once": "^1.3.0"}, "devDependencies": {"tap": "^0.4.10"}, "scripts": {"test": "tap test.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/inflight"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/inflight/issues"}, "homepage": "https://github.com/isaacs/inflight", "license": "ISC", "_id": "inflight@1.0.1", "_shasum": "01f6911821535243c790ac0f998f54e9023ffb6f", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "01f6911821535243c790ac0f998f54e9023ffb6f", "tarball": "https://registry.npmjs.org/inflight/-/inflight-1.0.1.tgz", "integrity": "sha512-Y6qkFIUPPR+z9LXqnwjDS+okztmmNNsGeJ3JZX6E24bZ02P1oqv8faJbafHLjOPJsTZ0UgdtjOT8SZbRkLkIlw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDPAfFYHKrWnkJiiXN6fuCIawSaP/F4mxL87mKEDehPegIhAJbTPM3WTrkJumaWAc6fdYI7f0QYduj/t6opD/yIWRk5"}]}, "directories": {}, "deprecated": "This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful."}, "1.0.2": {"name": "inflight", "version": "1.0.2", "description": "Add callbacks to requests in flight to avoid async duplication", "main": "inflight.js", "dependencies": {"once": "^1.3.0", "wrappy": "1"}, "devDependencies": {"tap": "^0.4.10"}, "scripts": {"test": "tap test.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/inflight"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/inflight/issues"}, "homepage": "https://github.com/isaacs/inflight", "license": "ISC", "gitHead": "f0c2b45a3924e4438d4c97d43acca1341be9354b", "_id": "inflight@1.0.2", "_shasum": "6d1d147db188760100f13e670ca0dfbd2b63d169", "_from": ".", "_npmVersion": "2.0.0", "_nodeVersion": "0.10.31", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "6d1d147db188760100f13e670ca0dfbd2b63d169", "tarball": "https://registry.npmjs.org/inflight/-/inflight-1.0.2.tgz", "integrity": "sha512-0cVKpb/7RAXWN77AVxc5czd7thGIUWMH+tlxPkVrh/0BWgbV68TjgMiGUHEBR3qvsF+dBf350u5gXbcK40r79g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCoQHkJ5dIaEQIQybaT624U5ivRIuRxBv0w57OhWul9fgIhAK30byjTacO74YbgQ3nE2icpfW8bIqNwgJ9KwT4CzE07"}]}, "directories": {}, "deprecated": "This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful."}, "1.0.3": {"name": "inflight", "version": "1.0.3", "description": "Add callbacks to requests in flight to avoid async duplication", "main": "inflight.js", "dependencies": {"once": "^1.3.0", "wrappy": "1"}, "devDependencies": {"tap": "^0.4.10"}, "scripts": {"test": "tap test.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/inflight"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/inflight/issues"}, "homepage": "https://github.com/isaacs/inflight", "license": "ISC", "gitHead": "a10cb02457ed415c9019d185902ec3db85b03984", "_id": "inflight@1.0.3", "_shasum": "70374be8ef3316248f37fa81276b6b329b95ff49", "_from": ".", "_npmVersion": "2.1.2", "_nodeVersion": "0.10.32", "_npmUser": {"name": "othiym23", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "iarna", "email": "<EMAIL>"}], "dist": {"shasum": "70374be8ef3316248f37fa81276b6b329b95ff49", "tarball": "https://registry.npmjs.org/inflight/-/inflight-1.0.3.tgz", "integrity": "sha512-GMTbCij8VGwxokaQyO2DF++9IHPbIEqEN2hErcvJgs/tDRSNkF8QJoDVkkQxzLe3O+m+D2kbUw3ot+R4H/ekWw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBiwr/bQGYbdbk/e7plwJFlzefDsfhYvxRaampXWwaXvAiAenyXshn9kiFF45OsNEFOGfRssJ0EvuBQ55ilgJR+Jfg=="}]}, "directories": {}, "deprecated": "This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful."}, "1.0.4": {"name": "inflight", "version": "1.0.4", "description": "Add callbacks to requests in flight to avoid async duplication", "main": "inflight.js", "dependencies": {"once": "^1.3.0", "wrappy": "1"}, "devDependencies": {"tap": "^0.4.10"}, "scripts": {"test": "tap test.js"}, "repository": {"type": "git", "url": "git://github.com/isaacs/inflight"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/inflight/issues"}, "homepage": "https://github.com/isaacs/inflight", "license": "ISC", "gitHead": "c7b5531d572a867064d4a1da9e013e8910b7d1ba", "_id": "inflight@1.0.4", "_shasum": "6cbb4521ebd51ce0ec0a936bfd7657ef7e9b172a", "_from": ".", "_npmVersion": "2.1.3", "_nodeVersion": "0.10.32", "_npmUser": {"name": "othiym23", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "iarna", "email": "<EMAIL>"}], "dist": {"shasum": "6cbb4521ebd51ce0ec0a936bfd7657ef7e9b172a", "tarball": "https://registry.npmjs.org/inflight/-/inflight-1.0.4.tgz", "integrity": "sha512-yMOGXregdgA+mer4gLyGWfj/gx0vzqmXtIY3YTxaQg4OZdk4GsrgiFCf/G96kVQt/aJDJyQPDeb/NtQRZp13xg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEe2iIHxEEhV5HTN5yKwa+tSN2zYXqeiT88W2IHctauMAiEAjtJOr5RRXFy5oiI+X3HG44otjH6ltYeBxJ9h1I+DE50="}]}, "directories": {}, "deprecated": "This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful."}, "1.0.5": {"name": "inflight", "version": "1.0.5", "description": "Add callbacks to requests in flight to avoid async duplication", "main": "inflight.js", "files": ["inflight.js"], "dependencies": {"once": "^1.3.0", "wrappy": "1"}, "devDependencies": {"tap": "^1.2.0"}, "scripts": {"test": "tap test.js"}, "repository": {"type": "git", "url": "git+https://github.com/npm/inflight.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/inflight/issues"}, "homepage": "https://github.com/isaacs/inflight", "license": "ISC", "gitHead": "559e37b4f6327fca797fe8d7fe8ed6d9cae08821", "_id": "inflight@1.0.5", "_shasum": "db3204cd5a9de2e6cd890b85c6e2f66bcf4f620a", "_from": ".", "_npmVersion": "3.9.1", "_nodeVersion": "5.10.1", "_npmUser": {"name": "zkat", "email": "<EMAIL>"}, "dist": {"shasum": "db3204cd5a9de2e6cd890b85c6e2f66bcf4f620a", "tarball": "https://registry.npmjs.org/inflight/-/inflight-1.0.5.tgz", "integrity": "sha512-ZOr/ZZjWvVTQML+yBDtsvuVlC9zkWqmaZVZTWP7XdfMTmoO3qjIP26vjfDKDJ6zA9ZffGlnm6Ry5t965o+WUgA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD20TiDL6jN6/AQQqM70KVWlPn9Wij0jPTOpoScNOpfNgIgMhxPzqjK2N9SmQkfYweGc7upRJOMmplNLXj47w0HCPg="}]}, "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/inflight-1.0.5.tgz_1463529611443_0.00041943578980863094"}, "directories": {}, "deprecated": "This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful."}, "1.0.6": {"name": "inflight", "version": "1.0.6", "description": "Add callbacks to requests in flight to avoid async duplication", "main": "inflight.js", "files": ["inflight.js"], "dependencies": {"once": "^1.3.0", "wrappy": "1"}, "devDependencies": {"tap": "^7.1.2"}, "scripts": {"test": "tap test.js --100"}, "repository": {"type": "git", "url": "git+https://github.com/npm/inflight.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/inflight/issues"}, "homepage": "https://github.com/isaacs/inflight", "license": "ISC", "gitHead": "a547881738c8f57b27795e584071d67cf6ac1a57", "_id": "inflight@1.0.6", "_shasum": "49bd6331d7d02d0c09bc910a1075ba8165b56df9", "_from": ".", "_npmVersion": "3.10.7", "_nodeVersion": "6.5.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "49bd6331d7d02d0c09bc910a1075ba8165b56df9", "tarball": "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz", "integrity": "sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHeTY8VavURUkUCnD17oIqsNuSlwTwP/yQOChuwV87WDAiEAhw/ZF24OrzMti41jlv8EM7Cq/KEVSteKCUh9MJ5Oo7s="}]}, "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/inflight-1.0.6.tgz_1476330807696_0.10388551792129874"}, "directories": {}, "deprecated": "This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful."}}, "readme": "# inflight\n\nAdd callbacks to requests in flight to avoid async duplication\n\n## USAGE\n\n```javascript\nvar inflight = require('inflight')\n\n// some request that does some stuff\nfunction req(key, callback) {\n  // key is any random string.  like a url or filename or whatever.\n  //\n  // will return either a falsey value, indicating that the\n  // request for this key is already in flight, or a new callback\n  // which when called will call all callbacks passed to inflightk\n  // with the same key\n  callback = inflight(key, callback)\n\n  // If we got a falsey value back, then there's already a req going\n  if (!callback) return\n\n  // this is where you'd fetch the url or whatever\n  // callback is also once()-ified, so it can safely be assigned\n  // to multiple events etc.  First call wins.\n  setTimeout(function() {\n    callback(null, key)\n  }, 100)\n}\n\n// only assigns a single setTimeout\n// when it dings, all cbs get called\nreq('foo', cb1)\nreq('foo', cb2)\nreq('foo', cb3)\nreq('foo', cb4)\n```\n", "maintainers": [{"email": "<EMAIL>", "name": "isaacs"}], "time": {"modified": "2024-05-23T03:14:32.244Z", "created": "2014-05-05T03:04:32.021Z", "1.0.0": "2014-05-05T03:04:32.021Z", "1.0.1": "2014-05-05T20:14:07.678Z", "1.0.2": "2014-09-18T23:07:30.791Z", "1.0.3": "2014-10-01T23:59:18.702Z", "1.0.4": "2014-10-03T06:50:17.203Z", "1.0.5": "2016-05-18T00:00:12.031Z", "1.0.6": "2016-10-13T03:53:29.524Z"}, "homepage": "https://github.com/isaacs/inflight", "repository": {"type": "git", "url": "git+https://github.com/npm/inflight.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/inflight/issues"}, "license": "ISC", "readmeFilename": "README.md", "users": {"program247365": true, "wenbing": true, "mojaray2k": true, "sbruchmann": true, "jasquier": true, "flumpus-dev": true}}