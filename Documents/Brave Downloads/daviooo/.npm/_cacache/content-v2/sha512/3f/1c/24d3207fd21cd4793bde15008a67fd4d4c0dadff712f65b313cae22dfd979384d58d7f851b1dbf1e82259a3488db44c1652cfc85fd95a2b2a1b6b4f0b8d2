{"_id": "readdirp", "_rev": "117-f15a93381ac49863d8348e4783fa89d8", "name": "readdirp", "dist-tags": {"latest": "4.1.1"}, "versions": {"0.1.0": {"name": "readdirp", "version": "0.1.0", "author": {"url": "thorstenlorenz.wordpress.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@0.1.0", "maintainers": [{"name": "thlorenz", "email": "<EMAIL>"}], "homepage": "https://github.com/thlorenz/readdirp", "dist": {"shasum": "62446d51e27ab2066d5a1831c7f9907846727b6a", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-0.1.0.tgz", "integrity": "sha512-IOlv+B+KMPWo5PlKEAlenB5UYkGw71+Owl8YCMmi7Kggngk3XNSZL5GEJ+qvjCwvj+EgevQ9H6oSk57DGNoK1A==", "signatures": [{"sig": "MEUCICH7kYZmBNZ8uEDxwJrVgbnjbjE8Oer6VWEcwAUWP95lAiEA9R8RlYF26CAD7Wl6SFhAW2Z3zRxbj91/KmTKV25T9u0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readdirp.js", "engines": {"node": ">=0.6"}, "scripts": {"test": "mocha"}, "repository": {"url": "git://github.com/thlorenz/readdirp.git", "type": "git"}, "description": "Recursive versions of fs module functions.", "directories": {}, "dependencies": {"minimatch": ">=0.2.4"}, "devDependencies": {"mocha": ">=1.1.0", "should": ">=0.6.3"}, "optionalDependencies": {}}, "0.1.1": {"name": "readdirp", "version": "0.1.1", "keywords": ["recursive", "fs", "readdir", "filesystem", "find", "filter"], "author": {"url": "thorstenlorenz.wordpress.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@0.1.1", "maintainers": [{"name": "thlorenz", "email": "<EMAIL>"}], "homepage": "https://github.com/thlorenz/readdirp", "dist": {"shasum": "9d2f892b8605b5aac44cd01da0606ac52745f1b5", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-0.1.1.tgz", "integrity": "sha512-qEbml2NfuFchx6ulOYWE7ry7k1iamtfy3NZBS0uxZqxcdmXQKcfc1HH5T8Ioq7TtnsObyNVcO/p7nN2JLk/q5g==", "signatures": [{"sig": "MEMCH3c0yk17+UlttpF0XVvSj39h5K1uFMCEd0YMf/xJ9K8CIHoQRxQL08GS1v45tAfUpHHD5J37sHLWSbTLHjNKAN1r", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readdirp.js", "engines": {"node": ">=0.6"}, "scripts": {"test": "mocha"}, "repository": {"url": "git://github.com/thlorenz/readdirp.git", "type": "git"}, "description": "Recursive version of fs.readdir.", "directories": {}, "dependencies": {"minimatch": ">=0.2.4"}, "devDependencies": {"mocha": ">=1.1.0", "should": ">=0.6.3"}, "optionalDependencies": {}}, "0.1.2": {"name": "readdirp", "version": "0.1.2", "keywords": ["recursive", "fs", "readdir", "filesystem", "find", "filter"], "author": {"url": "thorstenlorenz.wordpress.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@0.1.2", "maintainers": [{"name": "thlorenz", "email": "<EMAIL>"}], "homepage": "https://github.com/thlorenz/readdirp", "dist": {"shasum": "f7245556bbae9a8c6d0ac31c51d6fa81cfc09d36", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-0.1.2.tgz", "integrity": "sha512-XQUVP0gLO+Mown+w0M/Pjv/rV4G/2btWEL0QjTOz2oDrPXssd/ck3HtMKTfM67lq9j8H/ijlLMRDfTuBNEXEfA==", "signatures": [{"sig": "MEQCIHE8BC3fq/xc6SJyEZu6DHR+zE7UWe9iU2kFRGwHssV9AiAvQWa0GMCYKaI2BeGNytotKwQrNiY2Yej+pEAegqJwbQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readdirp.js", "engines": {"node": ">=0.6"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "thlorenz", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/thlorenz/readdirp.git", "type": "git"}, "_npmVersion": "1.1.59", "description": "Recursive version of fs.readdir.", "directories": {}, "dependencies": {"minimatch": ">=0.2.4"}, "devDependencies": {"mocha": ">=1.1.0", "should": ">=0.6.3"}, "optionalDependencies": {}}, "0.1.3": {"name": "readdirp", "version": "0.1.3", "keywords": ["recursive", "fs", "readdir", "filesystem", "find", "filter"], "author": {"url": "thorstenlorenz.wordpress.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@0.1.3", "maintainers": [{"name": "thlorenz", "email": "<EMAIL>"}], "homepage": "https://github.com/thlorenz/readdirp", "dist": {"shasum": "c852a0a090f72a1c026ec092e993165f11d2a613", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-0.1.3.tgz", "integrity": "sha512-zS+MAvJAKxzNlUqTXHYVgu6oqeXvFGK25XtlmlvjaGMcFlE0R0u0pgnBXenVTXViM5DVkmYFlkjnPHmkqkjgfw==", "signatures": [{"sig": "MEUCIQDr3sBYabAuQXOR5s97/hKRvManQU5QP2dnK09aRbBtpAIgRTWeKp/bw6j67Fe4YT3NZcfsJN3w8HREb4RL0c3sWeE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readdirp.js", "engines": {"node": ">=0.4"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "thlorenz", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/thlorenz/readdirp.git", "type": "git"}, "_npmVersion": "1.1.59", "description": "Recursive version of fs.readdir.", "directories": {}, "dependencies": {"minimatch": ">=0.2.4"}, "devDependencies": {"mocha": ">=1.1.0", "should": ">=0.6.3"}, "optionalDependencies": {}}, "0.1.4": {"name": "readdirp", "version": "0.1.4", "keywords": ["recursive", "fs", "readdir", "filesystem", "find", "filter"], "author": {"url": "thorstenlorenz.wordpress.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@0.1.4", "maintainers": [{"name": "thlorenz", "email": "<EMAIL>"}], "homepage": "https://github.com/thlorenz/readdirp", "dist": {"shasum": "0f7f6095bb2bce3968f7f9ed03da168ecd359933", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-0.1.4.tgz", "integrity": "sha512-Fxgb9AYjUINt7qMXWpeh0CHWMx1J8wHBGePNlT01d5X49624ogjFunCBKa0lHHLioQ4MKN4pUG2UyJ4MI0onkg==", "signatures": [{"sig": "MEYCIQDdmR7zakYMIUspgHL1wfFAYz3PUTWFqWIhRiI7jV29vwIhALIwjFQUoQ1yBsDcVrpsArmfnQr3qBwFI5kduggmOhol", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readdirp.js", "engines": {"node": ">=0.4"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "thlorenz", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/thlorenz/readdirp.git", "type": "git"}, "_npmVersion": "1.1.59", "description": "Recursive version of fs.readdir.", "directories": {}, "dependencies": {"minimatch": ">=0.2.4"}, "devDependencies": {"mocha": ">=1.1.0", "should": ">=0.6.3"}, "optionalDependencies": {}}, "0.2.0": {"name": "readdirp", "version": "0.2.0", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "author": {"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@0.2.0", "maintainers": [{"name": "thlorenz", "email": "<EMAIL>"}], "homepage": "https://github.com/thlorenz/readdirp", "dist": {"shasum": "ba20bb1517e7c9268989e2692fde1d8d2712614d", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-0.2.0.tgz", "integrity": "sha512-udHI4w8vXfqpgEKNHMgUCI0KsnSyZOu89aTtmbnMJhRb85qNuHLLgppJGLTRC6YiVB98mcJOGJgXcfQMM/cmxw==", "signatures": [{"sig": "MEUCIQCVVAcQacfDDVIE/RPATMB1imeQYM3ko7jsYLkkMwKgEAIgdpdCvPRJIgl/5mGYLQFit9lIAUXtvXIUsMSQtTggIfI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readdirp.js", "engines": {"node": ">=0.4"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "thlorenz", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/thlorenz/readdirp.git", "type": "git"}, "_npmVersion": "1.1.59", "description": "Recursive version of fs.readdir with streaming api.", "directories": {}, "dependencies": {"minimatch": ">=0.2.4"}, "devDependencies": {"tap": "~0.3.1", "through": "~1.1.0", "minimatch": "~0.2.7"}, "optionalDependencies": {}}, "0.2.1": {"name": "readdirp", "version": "0.2.1", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "author": {"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@0.2.1", "maintainers": [{"name": "thlorenz", "email": "<EMAIL>"}], "homepage": "https://github.com/thlorenz/readdirp", "dist": {"shasum": "679497bbd6b1f8cdf94ae5bf29c803c3c1b582a9", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-0.2.1.tgz", "integrity": "sha512-D643zKxHNB10M9ORzlET+12Uzatk3yBs77bw9+xGGLL1uqvaJAMFx0W7x0wiwC2YgSNVbMIoNn/r1je6OnlvWg==", "signatures": [{"sig": "MEYCIQCk6/Dn7p7f5LbOAdOLrijd+htf2Vnmp5J2zmlT8dO/qwIhAKp8MrHQiifG6/5+oDWWsNfs0PtOtZhp8glLIKGzCdU1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readdirp.js", "engines": {"node": ">=0.4"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "thlorenz", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/thlorenz/readdirp.git", "type": "git"}, "_npmVersion": "1.1.59", "description": "Recursive version of fs.readdir with streaming api.", "directories": {}, "dependencies": {"minimatch": ">=0.2.4"}, "devDependencies": {"tap": "~0.3.1", "through": "~1.1.0", "minimatch": "~0.2.7"}, "optionalDependencies": {}}, "0.2.2": {"name": "readdirp", "version": "0.2.2", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "author": {"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@0.2.2", "maintainers": [{"name": "thlorenz", "email": "<EMAIL>"}], "homepage": "https://github.com/thlorenz/readdirp", "dist": {"shasum": "2578a30daada8c4aae9042a3911eb0a301b8bf07", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-0.2.2.tgz", "integrity": "sha512-P0i6V6OJoTLQ9YP6CG3yxv9vaFQ1ntos1mNcG7UFEBtAtjSDprbZP/61N6L3weNAeC33Je9Ax8R8gguyVHNSFg==", "signatures": [{"sig": "MEQCIHHFWInAW9rYSHI43gZdhQqEOMaqEInxo8GLv+T066goAiBiGjCrdYdrXbU7+oHDmC6xvakLeCjf5PoIZR9ss2VSWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readdirp.js", "engines": {"node": ">=0.4"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "thlorenz", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/thlorenz/readdirp.git", "type": "git"}, "_npmVersion": "1.1.66", "description": "Recursive version of fs.readdir with streaming api.", "directories": {}, "dependencies": {"minimatch": ">=0.2.4"}, "devDependencies": {"tap": "~0.3.1", "through": "~1.1.0", "minimatch": "~0.2.7"}, "optionalDependencies": {}}, "0.2.3": {"name": "readdirp", "version": "0.2.3", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "author": {"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@0.2.3", "maintainers": [{"name": "thlorenz", "email": "<EMAIL>"}], "homepage": "https://github.com/thlorenz/readdirp", "dist": {"shasum": "51c51b33bdd05a5968d508aaae984136ae2c4cad", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-0.2.3.tgz", "integrity": "sha512-kMMWr7Uk5TXlBkYRZtMqktArKltNhJ3U8IPaML5RcWxMbkDwgR1ahHc75S8suN7BdWZucg276W/KeZ1cP/soTA==", "signatures": [{"sig": "MEQCIB/fYzEJ55bW6Ng6OHbPMLaS5Qz1x9QWDuU++kX+rx50AiBRgjT4xUrnxRffTehEMVKd5DugOAGI+Mfs29hV6TAjGQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readdirp.js", "engines": {"node": ">=0.4"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "thlorenz", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/thlorenz/readdirp.git", "type": "git"}, "_npmVersion": "1.1.69", "description": "Recursive version of fs.readdir with streaming api.", "directories": {}, "dependencies": {"minimatch": ">=0.2.4"}, "devDependencies": {"tap": "~0.3.1", "through": "~1.1.0", "minimatch": "~0.2.7"}, "optionalDependencies": {}}, "0.2.4": {"name": "readdirp", "version": "0.2.4", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "author": {"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@0.2.4", "maintainers": [{"name": "thlorenz", "email": "<EMAIL>"}], "homepage": "https://github.com/thlorenz/readdirp", "dist": {"shasum": "469a896cce3fa70b856fbbd10e3475760060008e", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-0.2.4.tgz", "integrity": "sha512-YWkRlc18TRd+9rIMqz/vibjAIa7Pl0KzCqR3fwEdIXIfnS3iEwLEk7SMpgbfGKLo5ZyBSLxlAsMgKS6yy1N0Lg==", "signatures": [{"sig": "MEUCIQDs3y228kCLqvlrxE2BpmmOXtb4bnA0Zske6SmjtuMNewIgdSsmXw6JZ3oERQ1H5RoInlbNbmAzx13dsax97MBXkp8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readdirp.js", "engines": {"node": ">=0.4"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "thlorenz", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/thlorenz/readdirp.git", "type": "git"}, "_npmVersion": "1.1.69", "description": "Recursive version of fs.readdir with streaming api.", "directories": {}, "dependencies": {"minimatch": ">=0.2.4"}, "devDependencies": {"tap": "~0.3.1", "through": "~1.1.0", "minimatch": "~0.2.7"}, "optionalDependencies": {}}, "0.2.5": {"name": "readdirp", "version": "0.2.5", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "author": {"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@0.2.5", "maintainers": [{"name": "thlorenz", "email": "<EMAIL>"}], "homepage": "https://github.com/thlorenz/readdirp", "dist": {"shasum": "c4c276e52977ae25db5191fe51d008550f15d9bb", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-0.2.5.tgz", "integrity": "sha512-j3/RLxRiCZSei/4UaYaOAqHa+rQ8ZL9vpolGO9E7mLXiVTb7Fu99eTG74ZmaB/4gCGgy7Veq+U6vw8y7sKJiTw==", "signatures": [{"sig": "MEQCIDbZiauhlmkRxtR1cJQuywjAc5PcLbffDsBV//sUTQNHAiBRXAnkZrIEwRS97JyV6KBYh8GhQecWW5L+J/QL+G5ZJQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readdirp.js", "_from": ".", "engines": {"node": ">=0.4"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "thlorenz", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/thlorenz/readdirp.git", "type": "git"}, "_npmVersion": "1.2.14", "description": "Recursive version of fs.readdir with streaming api.", "directories": {}, "dependencies": {"minimatch": ">=0.2.4"}, "devDependencies": {"tap": "~0.3.1", "through": "~1.1.0", "minimatch": "~0.2.7"}, "optionalDependencies": {}}, "0.3.0": {"name": "readdirp", "version": "0.3.0", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "author": {"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@0.3.0", "maintainers": [{"name": "thlorenz", "email": "<EMAIL>"}], "homepage": "https://github.com/thlorenz/readdirp", "dist": {"shasum": "daee0f17dadef1904f41884288045ee01b889e23", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-0.3.0.tgz", "integrity": "sha512-YyU/tkbG2sT38c+d7xKHIP894wB8Z6UtMoJkqxqYtFmxxAFYm6ClSxjmgt0EIrwgmvK85DCUeFmlo4lsRfagQw==", "signatures": [{"sig": "MEYCIQDkTh8pNFUitR13qwiHNE0Wh4eP48YzYzFvrKC5sZbVLQIhAJwT2KvIUYl6q4+P4ckuxWVKs3l1NoDkelH5D39+Rlno", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readdirp.js", "_from": ".", "engines": {"node": ">=0.4"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "thlorenz", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/thlorenz/readdirp.git", "type": "git"}, "_npmVersion": "1.2.14", "description": "Recursive version of fs.readdir with streaming api.", "directories": {}, "dependencies": {"minimatch": ">=0.2.4", "graceful-fs": "~1.2.2"}, "devDependencies": {"tap": "~0.3.1", "through": "~1.1.0", "minimatch": "~0.2.7"}, "optionalDependencies": {}}, "0.3.1": {"name": "readdirp", "version": "0.3.1", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "author": {"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@0.3.1", "maintainers": [{"name": "thlorenz", "email": "<EMAIL>"}], "homepage": "https://github.com/thlorenz/readdirp", "bugs": {"url": "https://github.com/thlorenz/readdirp/issues"}, "dist": {"shasum": "6a77e1dc33f20ca8e010ab981ca2319f882964ad", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-0.3.1.tgz", "integrity": "sha512-0EqVz84OgUo+znxZP6g4TRjIkBvce1mQWr7fMAGe7+GolgSxijixLCZnj+BQkr27VYxNGW5iBuAXEg+EsCKH3w==", "signatures": [{"sig": "MEQCIHjNNJchIb1MJXDiUlk2hqMz5ZqVjhVtf+Q56NtzIBpVAiByiwwPzYbl/lAL2LJa1Hr5n1rmzEJXW/PgEvv+GKBKsg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readdirp.js", "_from": ".", "engines": {"node": ">=0.8"}, "scripts": {"test": "if [ -e $TRAVIS ]; then npm run test-all; else npm run test-main; fi", "test-0.8": "nave use 0.8 npm run test-main", "test-all": "npm run test-main && npm run test-0.8 && npm run test-0.10", "test-0.10": "nave use 0.10 npm run test-main", "test-main": "tap test/*.js"}, "_npmUser": {"name": "thlorenz", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/thlorenz/readdirp.git", "type": "git"}, "_npmVersion": "1.3.2", "description": "Recursive version of fs.readdir with streaming api.", "directories": {}, "dependencies": {"minimatch": "~0.2.12", "graceful-fs": "~2.0.0"}, "devDependencies": {"tap": "~0.4.3", "through": "~2.3.4"}, "optionalDependencies": {}}, "0.3.2": {"name": "readdirp", "version": "0.3.2", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "author": {"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@0.3.2", "maintainers": [{"name": "thlorenz", "email": "<EMAIL>"}], "homepage": "https://github.com/thlorenz/readdirp", "bugs": {"url": "https://github.com/thlorenz/readdirp/issues"}, "dist": {"shasum": "f6b4d142f2089d67aba0106f19e7b2d0da748be9", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-0.3.2.tgz", "integrity": "sha512-6X2q2bXBH8N+nc6KpYBrOatBoN6yYA9T/KluLvJVO6DC1jGE1LR8iLjN3iYTAfQ0A1vHUDi690qKpHutuiBAAg==", "signatures": [{"sig": "MEQCIAbz1Fh028gTfE8J0kJg93c/FpcIgYVJgf3Iid6OcP0zAiBjK0a6hcH99hXQfDAREBrxRL1F406Az0EmMnhyvgPeAw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readdirp.js", "_from": ".", "engines": {"node": ">=0.8"}, "scripts": {"test": "if [ -e $TRAVIS ]; then npm run test-all; else npm run test-main; fi", "test-0.8": "nave use 0.8 npm run test-main", "test-all": "npm run test-main && npm run test-0.8 && npm run test-0.10", "test-0.10": "nave use 0.10 npm run test-main", "test-main": "tap test/*.js"}, "_npmUser": {"name": "thlorenz", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/thlorenz/readdirp.git", "type": "git"}, "_npmVersion": "1.3.17", "description": "Recursive version of fs.readdir with streaming api.", "directories": {}, "dependencies": {"minimatch": "~0.2.12", "graceful-fs": "~2.0.0"}, "devDependencies": {"tap": "~0.4.3", "through": "~2.3.4"}, "optionalDependencies": {}}, "0.3.3": {"name": "readdirp", "version": "0.3.3", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "author": {"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@0.3.3", "maintainers": [{"name": "thlorenz", "email": "<EMAIL>"}], "homepage": "https://github.com/thlorenz/readdirp", "bugs": {"url": "https://github.com/thlorenz/readdirp/issues"}, "dist": {"shasum": "552105525a105739a6198bfa98bcbce64b3d3818", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-0.3.3.tgz", "integrity": "sha512-FY18iOcywiypt0hLNh5H5CRnU9A+ps5pUnxV3u/LKPEcu2iUdteEJhokYKlPu7kmqZlU2gnrzS8Ltwy3hW16dQ==", "signatures": [{"sig": "MEUCIQDhrBQT8GJvBxZsvHWFiSc9OgVs0179bHOyruGZ+v4qNQIgFgxCKpgmroVyJ3aWANwy9SUOUSYRe9dUXUcKYi7HRXM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readdirp.js", "_from": ".", "engines": {"node": ">=0.8"}, "scripts": {"test": "if [ -e $TRAVIS ]; then npm run test-all; else npm run test-main; fi", "test-0.8": "nave use 0.8 npm run test-main", "test-all": "npm run test-main && npm run test-0.8 && npm run test-0.10", "test-0.10": "nave use 0.10 npm run test-main", "test-main": "tap test/*.js"}, "_npmUser": {"name": "thlorenz", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/thlorenz/readdirp.git", "type": "git"}, "_npmVersion": "1.3.8", "description": "Recursive version of fs.readdir with streaming api.", "directories": {}, "dependencies": {"minimatch": "~0.2.12", "graceful-fs": "~2.0.0"}, "devDependencies": {"tap": "~0.4.3", "through": "~2.3.4"}, "optionalDependencies": {}}, "0.4.0": {"name": "readdirp", "version": "0.4.0", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "author": {"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@0.4.0", "maintainers": [{"name": "thlorenz", "email": "<EMAIL>"}], "homepage": "https://github.com/thlorenz/readdirp", "bugs": {"url": "https://github.com/thlorenz/readdirp/issues"}, "dist": {"shasum": "ec0036fa0eb33c71cad70d9ca6082e52e2168725", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-0.4.0.tgz", "integrity": "sha512-iyYL8mkS77LE6DqmoBZXYePhbwBXRqhPPd4nSvv7KkYwDB90pc71aBk41eJEFq+CPEpy6s1Zni8Sdkpauaywbg==", "signatures": [{"sig": "MEUCIQCmS/DbaNNoW7rHEd+NDjx9tt2JSsz9PULuZcIOegl8ewIgR6wzz8kIqicmodFE166HjYoYoeFAS+YXSiPJfG8riqk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readdirp.js", "_from": ".", "engines": {"node": ">=0.8"}, "scripts": {"test": "if [ -e $TRAVIS ]; then npm run test-all; else npm run test-main; fi", "test-0.8": "nave use 0.8 npm run test-main", "test-all": "npm run test-main && npm run test-0.8 && npm run test-0.10", "test-0.10": "nave use 0.10 npm run test-main", "test-main": "tap test/*.js"}, "_npmUser": {"name": "thlorenz", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/thlorenz/readdirp.git", "type": "git"}, "_npmVersion": "1.4.6", "description": "Recursive version of fs.readdir with streaming api.", "directories": {}, "dependencies": {"minimatch": "~0.2.12", "graceful-fs": "~2.0.0"}, "devDependencies": {"tap": "~0.4.3", "through": "~2.3.4"}, "optionalDependencies": {}}, "1.0.0": {"name": "readdirp", "version": "1.0.0", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "author": {"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@1.0.0", "maintainers": [{"name": "thlorenz", "email": "<EMAIL>"}], "homepage": "https://github.com/thlorenz/readdirp", "bugs": {"url": "https://github.com/thlorenz/readdirp/issues"}, "dist": {"shasum": "b8ce62a269bc4dc68134f86cc964f027e47e1771", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-1.0.0.tgz", "integrity": "sha512-LEJpbMOGkqXPGzyVDKbPDecMGtDtxVW2w8iIuTayUqz7R4+kVybWgzuus8u1+sVANgjoWcu3jTmt/e6SkOtZeA==", "signatures": [{"sig": "MEYCIQCGjZJnYqFqnhUTfxtGU2/8s/PJpoa+5x+q6jcGvaDSdwIhALTW68zJmphvaXW08X0oh+W1KXTi/ouDSZufc5SJaAV1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readdirp.js", "_from": ".", "engines": {"node": ">=0.6"}, "scripts": {"test": "if [ -e $TRAVIS ]; then npm run test-all; else npm run test-main; fi", "test-0.8": "nave use 0.8 npm run test-main", "test-all": "npm run test-main && npm run test-0.8 && npm run test-0.10", "test-0.10": "nave use 0.10 npm run test-main", "test-main": "tap test/*.js"}, "_npmUser": {"name": "thlorenz", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/thlorenz/readdirp.git", "type": "git"}, "_npmVersion": "1.4.6", "description": "Recursive version of fs.readdir with streaming api.", "directories": {}, "dependencies": {"minimatch": "~0.2.12", "graceful-fs": "~2.0.0", "readable-stream": "~1.0.26-2"}, "devDependencies": {"tap": "~0.4.8", "through2": "~0.4.1"}, "optionalDependencies": {}}, "1.0.1": {"name": "readdirp", "version": "1.0.1", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "author": {"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@1.0.1", "maintainers": [{"name": "thlorenz", "email": "<EMAIL>"}], "homepage": "https://github.com/thlorenz/readdirp", "bugs": {"url": "https://github.com/thlorenz/readdirp/issues"}, "dist": {"shasum": "16967d390300346a67ffb30a3867bb4b6173934a", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-1.0.1.tgz", "integrity": "sha512-JxCNmsvrUNs+rNg3k3j0daqZlQIsKU4+ktKagvyNn2Z74hz/67Yew9zLSt/TPPQyEDTjEYHLKLyonVf+IHyAvg==", "signatures": [{"sig": "MEQCIE0AD7xh5LtIPZIcusCCqWJ2lVlnoJ6PmyTpf6tiTp7eAiBXpMYsM16zxGN5ALTFssK60cWv1S+rS7plCyPP2I8Hzg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readdirp.js", "_from": ".", "engines": {"node": ">=0.6"}, "scripts": {"test": "if [ -e $TRAVIS ]; then npm run test-all; else npm run test-main; fi", "test-0.8": "nave use 0.8 npm run test-main", "test-all": "npm run test-main && npm run test-0.8 && npm run test-0.10", "test-0.10": "nave use 0.10 npm run test-main", "test-main": "(cd test && set -e; for t in ./*.js; do node $t; done)"}, "_npmUser": {"name": "thlorenz", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/thlorenz/readdirp.git", "type": "git"}, "_npmVersion": "1.4.6", "description": "Recursive version of fs.readdir with streaming api.", "directories": {}, "dependencies": {"minimatch": "~0.2.12", "graceful-fs": "~2.0.0", "readable-stream": "~1.0.26-2"}, "devDependencies": {"tap": "~0.4.8", "through2": "~0.4.1"}, "optionalDependencies": {}}, "1.1.0": {"name": "readdirp", "version": "1.1.0", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "author": {"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@1.1.0", "maintainers": [{"name": "thlorenz", "email": "<EMAIL>"}], "homepage": "https://github.com/thlorenz/readdirp", "bugs": {"url": "https://github.com/thlorenz/readdirp/issues"}, "dist": {"shasum": "6506f9d5d8bb2edc19c855a60bb92feca5fae39c", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-1.1.0.tgz", "integrity": "sha512-aYbLJ+sPWx5YFUAvPJXX1fGhsKGk80vqSrvJDZ4nvH/dvRbc4roqkBRlal/ct7tUNzTM1h6JcA5UgUoHj2UlaQ==", "signatures": [{"sig": "MEQCIDKeru+Dj/flsclmMs8jsGkqrlAJTndUSeuNPS9kgqk5AiAQ/8TdypTqiAep+VfHHB+wSj8JxCe4uPio+35iVWpQ+w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readdirp.js", "_from": ".", "engines": {"node": ">=0.6"}, "scripts": {"test": "if [ -e $TRAVIS ]; then npm run test-all; else npm run test-main; fi", "test-0.8": "nave use 0.8 npm run test-main", "test-all": "npm run test-main && npm run test-0.8 && npm run test-0.10", "test-0.10": "nave use 0.10 npm run test-main", "test-main": "(cd test && set -e; for t in ./*.js; do node $t; done)"}, "_npmUser": {"name": "thlorenz", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/thlorenz/readdirp.git", "type": "git"}, "_npmVersion": "1.4.6", "description": "Recursive version of fs.readdir with streaming api.", "directories": {}, "dependencies": {"minimatch": "~0.2.12", "graceful-fs": "~2.0.0", "readable-stream": "~1.0.26-2"}, "devDependencies": {"tap": "~0.4.8", "through2": "~0.4.1"}, "optionalDependencies": {}}, "1.2.0": {"name": "readdirp", "version": "1.2.0", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "author": {"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@1.2.0", "maintainers": [{"name": "thlorenz", "email": "<EMAIL>"}], "homepage": "https://github.com/thlorenz/readdirp", "bugs": {"url": "https://github.com/thlorenz/readdirp/issues"}, "dist": {"shasum": "7ece25c8fc0ccae4461fe28e8a8b30b4d518cdfa", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-1.2.0.tgz", "integrity": "sha512-/Kkzwz/fq06M+FjuvhaGjj1q3kGe2NBJqq6LlOubCWYg4thc8WOIHMFCE0H1Ubfrfrx707R39WQ3U9mjuhVPvw==", "signatures": [{"sig": "MEUCIQCqLmD3Qlg+gwPkYW8VkSVnqmO8hcCmD51RiWVkuqAEmwIgLnZJRmr7t42FU66vBy9pELlfdJciPRIkh2wKmoVwivk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readdirp.js", "_from": ".", "_shasum": "7ece25c8fc0ccae4461fe28e8a8b30b4d518cdfa", "engines": {"node": ">=0.6"}, "gitHead": "d35a7381bf56db9e3f7b1fb69067ad907f71176e", "scripts": {"test": "if [ -e $TRAVIS ]; then npm run test-all; else npm run test-main; fi", "test-0.8": "nave use 0.8 npm run test-main", "test-all": "npm run test-main && npm run test-0.8 && npm run test-0.10", "test-0.10": "nave use 0.10 npm run test-main", "test-main": "(cd test && set -e; for t in ./*.js; do node $t; done)"}, "_npmUser": {"name": "thlorenz", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/thlorenz/readdirp.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "Recursive version of fs.readdir with streaming api.", "directories": {}, "dependencies": {"minimatch": "~0.2.12", "graceful-fs": "~2.0.0", "readable-stream": "~1.0.26-2"}, "devDependencies": {"tap": "~0.4.8", "through2": "~0.4.1"}, "optionalDependencies": {}}, "1.3.0": {"name": "readdirp", "version": "1.3.0", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "author": {"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@1.3.0", "maintainers": [{"name": "thlorenz", "email": "<EMAIL>"}], "homepage": "https://github.com/thlorenz/readdirp", "bugs": {"url": "https://github.com/thlorenz/readdirp/issues"}, "dist": {"shasum": "eaf1a9b463be9a8190fc9ae163aa1ac934aa340b", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-1.3.0.tgz", "integrity": "sha512-H1BGeo9VW8nmdwGo64SKRQgNNZwEuqtVUHijOoTDYIpqJGNKU65JaRXL3iqa/8tmVJ9jfoKY+soTznq0cOruTw==", "signatures": [{"sig": "MEYCIQCwdJNcE8skcmZ0PZ4DQC+7ZBMyOyJAhK34v/KPTbkVwQIhAJmAFQIV+JRsYtHFFtHnLwrYwfd4cRdPV81LdK/iuH/z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readdirp.js", "_from": ".", "_shasum": "eaf1a9b463be9a8190fc9ae163aa1ac934aa340b", "engines": {"node": ">=0.6"}, "gitHead": "82caf226eeafec8669c604f71e46e5e83c48cc86", "scripts": {"test": "if [ -e $TRAVIS ]; then npm run test-all; else npm run test-main; fi", "test-0.8": "nave use 0.8 npm run test-main", "test-all": "npm run test-main && npm run test-0.8 && npm run test-0.10", "test-0.10": "nave use 0.10 npm run test-main", "test-main": "(cd test && set -e; for t in ./*.js; do node $t; done)"}, "_npmUser": {"name": "thlorenz", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/thlorenz/readdirp.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "Recursive version of fs.readdir with streaming api.", "directories": {}, "dependencies": {"minimatch": "~0.2.12", "graceful-fs": "~2.0.0", "readable-stream": "~1.0.26-2"}, "devDependencies": {"tap": "~0.4.8", "through2": "~0.4.1"}, "optionalDependencies": {}}, "1.4.0": {"name": "readdirp", "version": "1.4.0", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "author": {"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@1.4.0", "maintainers": [{"name": "thlorenz", "email": "<EMAIL>"}], "homepage": "https://github.com/thlorenz/readdirp", "bugs": {"url": "https://github.com/thlorenz/readdirp/issues"}, "dist": {"shasum": "c5de6fcb3dec80523c1c70113f1a190d8af82c89", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-1.4.0.tgz", "integrity": "sha512-QOYlYH11MJ1RrkxHOBZDSvXOgRy/gKj6xQIvShAEf2KAaHn03BetRpakZdC6GWUEOpsGf1XXalXeIXgEblZbLw==", "signatures": [{"sig": "MEYCIQDajOhJLyBDytIudteqU3W6mKvfLBUSN4JzeB8FenSHGwIhAOdyrU2FLfAotBhoDuL49PXhRGfLa1teG7ySBW7flwph", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readdirp.js", "_from": ".", "_shasum": "c5de6fcb3dec80523c1c70113f1a190d8af82c89", "engines": {"node": ">=0.6"}, "gitHead": "409a3d9f52c746d8c737d449387d6fadbdd00604", "scripts": {"test": "if [ -e $TRAVIS ]; then npm run test-all; else npm run test-main; fi", "test-0.8": "nave use 0.8 npm run test-main", "test-2.4": "nave use 2.4 npm run test-main", "test-all": "npm run test-main && npm run test-0.8 && npm run test-0.10 && npm run test-0.12 && npm run test-2.4", "test-0.10": "nave use 0.10 npm run test-main", "test-0.12": "nave use 0.12 npm run test-main", "test-main": "(cd test && set -e; for t in ./*.js; do node $t; done)"}, "_npmUser": {"name": "thlorenz", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/thlorenz/readdirp.git", "type": "git"}, "_npmVersion": "2.11.3", "description": "Recursive version of fs.readdir with streaming api.", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"minimatch": "~0.2.12", "graceful-fs": "~4.1.2", "readable-stream": "~1.0.26-2"}, "devDependencies": {"tap": "~0.4.8", "nave": "~0.5.1", "through2": "~0.4.1"}, "optionalDependencies": {}}, "2.0.0": {"name": "readdirp", "version": "2.0.0", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "author": {"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@2.0.0", "maintainers": [{"name": "thlorenz", "email": "<EMAIL>"}], "homepage": "https://github.com/thlorenz/readdirp", "bugs": {"url": "https://github.com/thlorenz/readdirp/issues"}, "dist": {"shasum": "cc09ba5d12d8feb864bc75f6e2ebc137060cbd82", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-2.0.0.tgz", "integrity": "sha512-4mfs1i2diwg7SgZjBHxU+mAXg07dFxmIFbzFX8R3/KO/iGD2mW4lvOer9nH5emlwvXUn473enXJoFZYi5W8+mA==", "signatures": [{"sig": "MEUCIQCEvW3QCdYW++QoPw78oyJjAuF03ajFaNSITk8tPPvIxAIgHTMMNGIxW9u/tZc5eCQxtj12ztU+7NqxYGjQE2aMAAY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readdirp.js", "_from": ".", "_shasum": "cc09ba5d12d8feb864bc75f6e2ebc137060cbd82", "engines": {"node": ">=0.6"}, "gitHead": "480af1e35d413ebb36e427808dcaa65d47cdc490", "scripts": {"test": "if [ -e $TRAVIS ]; then npm run test-all; else npm run test-main; fi", "test-0.8": "nave use 0.8 npm run test-main", "test-2.4": "nave use 2.4 npm run test-main", "test-all": "npm run test-main && npm run test-0.8 && npm run test-0.10 && npm run test-0.12 && npm run test-2.4", "test-0.10": "nave use 0.10 npm run test-main", "test-0.12": "nave use 0.12 npm run test-main", "test-main": "(cd test && set -e; for t in ./*.js; do node $t; done)"}, "_npmUser": {"name": "thlorenz", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/thlorenz/readdirp.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "Recursive version of fs.readdir with streaming api.", "directories": {}, "dependencies": {"minimatch": "^2.0.10", "graceful-fs": "^4.1.2", "readable-stream": "^2.0.2"}, "devDependencies": {"tap": "^1.3.2", "nave": "^0.5.1", "through2": "^2.0.0"}}, "2.0.1": {"name": "readdirp", "version": "2.0.1", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "author": {"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@2.0.1", "maintainers": [{"name": "thlorenz", "email": "<EMAIL>"}], "homepage": "https://github.com/thlorenz/readdirp", "bugs": {"url": "https://github.com/thlorenz/readdirp/issues"}, "dist": {"shasum": "672aa0c5013e7942996bbf3a392bf69aef89d5a5", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-2.0.1.tgz", "integrity": "sha512-4ccmLnjcaQg1sBtWsolBgCbtjCv26taibhIWtFXQB0QRwWP8kK1HKMMJdBO5mz5EiTN/0ObXHsNfFGfGt8uPgg==", "signatures": [{"sig": "MEUCIQC2suXgAnZabd5h8SBMn8g6gT/69bYoo6wBIEKXNyLgsgIgcI0rkUmOrrLMr+fj68Z6HwKrC493tQmghnlS2HQ9E5w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readdirp.js", "_from": ".", "_shasum": "672aa0c5013e7942996bbf3a392bf69aef89d5a5", "engines": {"node": ">=0.6"}, "gitHead": "e741f5e5fe4b0df1e9017733dd8ada9b329a9b58", "scripts": {"test": "if [ -e $TRAVIS ]; then npm run test-all; else npm run test-main; fi", "test-0.8": "nave use 0.8 npm run test-main", "test-2.4": "nave use 2.4 npm run test-main", "test-all": "npm run test-main && npm run test-0.8 && npm run test-0.10 && npm run test-0.12 && npm run test-2.4", "test-0.10": "nave use 0.10 npm run test-main", "test-0.12": "nave use 0.12 npm run test-main", "test-main": "(cd test && set -e; for t in ./*.js; do node $t; done)"}, "_npmUser": {"name": "thlorenz", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/thlorenz/readdirp.git", "type": "git"}, "_npmVersion": "2.15.6", "description": "Recursive version of fs.readdir with streaming api.", "directories": {}, "_nodeVersion": "4.4.5", "dependencies": {"minimatch": "^3.0.2", "graceful-fs": "^4.1.2", "readable-stream": "^2.0.2"}, "devDependencies": {"tap": "^1.3.2", "nave": "^0.5.1", "through2": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/readdirp-2.0.1.tgz_1466617803466_0.7497695861384273", "host": "packages-16-east.internal.npmjs.com"}}, "2.1.0": {"name": "readdirp", "version": "2.1.0", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "author": {"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@2.1.0", "maintainers": [{"name": "thlorenz", "email": "<EMAIL>"}], "homepage": "https://github.com/thlorenz/readdirp", "bugs": {"url": "https://github.com/thlorenz/readdirp/issues"}, "dist": {"shasum": "4ed0ad060df3073300c48440373f72d1cc642d78", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-2.1.0.tgz", "integrity": "sha512-LgQ8mdp6hbxJUZz27qxVl7gmFM/0DfHRO52c5RUbKAgMvr81tour7YYWW1JYNmrXyD/o0Myy9/DC3fUYkqnyzg==", "signatures": [{"sig": "MEUCIQDYggKgYuXCpPonY03Lmk+AmlEZQumuSECPWMIIQiOZFAIgA7iqFJyRrNudb6bm1iCa+YsvkT5vzuvyG9LaofEO7eA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readdirp.js", "_from": ".", "_shasum": "4ed0ad060df3073300c48440373f72d1cc642d78", "engines": {"node": ">=0.6"}, "gitHead": "5a3751f86a1c2bbbb8e3a42685d4191992631e6c", "scripts": {"test": "if [ -e $TRAVIS ]; then npm run test-all; else npm run test-main; fi", "test-4": "nave use 4.4 npm run test-main", "test-6": "nave use 6.2 npm run test-main", "test-all": "npm run test-main && npm run test-0.10 && npm run test-0.12 && npm run test-4 && npm run test-6", "test-0.10": "nave use 0.10 npm run test-main", "test-0.12": "nave use 0.12 npm run test-main", "test-main": "(cd test && set -e; for t in ./*.js; do node $t; done)"}, "_npmUser": {"name": "thlorenz", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/thlorenz/readdirp.git", "type": "git"}, "_npmVersion": "2.15.6", "description": "Recursive version of fs.readdir with streaming api.", "directories": {}, "_nodeVersion": "4.4.6", "dependencies": {"minimatch": "^3.0.2", "graceful-fs": "^4.1.2", "readable-stream": "^2.0.2", "set-immediate-shim": "^1.0.1"}, "devDependencies": {"tap": "1.3.2", "nave": "^0.5.1", "through2": "^2.0.0", "proxyquire": "^1.7.9"}, "_npmOperationalInternal": {"tmp": "tmp/readdirp-2.1.0.tgz_1467053820730_0.8782131769694388", "host": "packages-16-east.internal.npmjs.com"}}, "2.2.0": {"name": "readdirp", "version": "2.2.0", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "author": {"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@2.2.0", "maintainers": [{"name": "thlorenz", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/readdirp", "bugs": {"url": "https://github.com/paulmillr/readdirp/issues"}, "dist": {"shasum": "cf040e9cb125fc921e6e9771647496edde3666fd", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-2.2.0.tgz", "fileCount": 5, "integrity": "sha512-iJ1qI5H8fDSgViLIHepwTeV0D9ISMvGQlmKlU8WnNVzqb3KrvGZG/sCz7LxNKvvMU4JpcfejU9uxGn30zTVlXg==", "signatures": [{"sig": "MEUCIQDPw2ITrJnPzhKy2cIFekCBTAsLsCEd73OYXilvj1IoOQIgCOevyLGV7mx+3yqzrABMw7E8rHQ+xpeyWIi5lxYnAbU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21309, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbmonlCRA9TVsSAnZWagAAQcEP/2lfS7fqucDQRlGg66J7\nVQ4CEEqMkcdWuE+ouI/8/DQekycQzJVr+tqq+ocWeV+hnONscbZsnUZ/F+mN\nWAebXI0L4XanlMnTzaVC+AwwFBAWJADiQAXOnMlQ1wccJeud0ak9SFR/0cGD\nOS2+Xpp6XMs42YhNKCBBeSeJy4A6wLYotfmtK15+8zKov0QWq8+99mEmTIX2\nzPSj8uTSF6+mMXbICsdlicD8aNrMva8QSnCfIvRcRgYoxe6h+1EAnfk9v16v\nw1x6f/yxPRjPK9MkhQ7tXUQ1kcdF42j7eXB45x9FrYBDK57GT4W96DT4RGf2\nrfl64uthDVqsuAQ46mF4eYLFrNKc3hMHkYyiE1l3KOZJy/ELp4suStBru8Ax\n3uT89m3UnIuOEDfPb46iFXRonAE/RiLCTPRXrQsv2EjgdOkUPhC58AH5t/q8\n8erecHAJnOrXE6SELf/YseyG5u/QEbbMU4NyvHYgFy4f/Z9Kll8lAKAZvu3i\nNYP3OH4Oa77aa2nwCj6Aug6R7Blj/W5P1su8CJNeRwOVazuzJYn5zN9JSZs1\ncuJt/uHpER3StmdKq8fCMMJiEFCbilXsIoZUJWOg8arTNXO7CoiOV76WGUPk\nzADhhPrXxPyGecY+R1utoTLo7fks89URQTVVvp/B8W/PjUzwqUW81PA5JUsW\nDyGr\r\n=M6Ra\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "readdirp.js", "engines": {"node": ">=0.6"}, "gitHead": "a00f3058c6eda4396c051eed754a234995635c45", "scripts": {"test": "npm run test-main", "test-4": "nave use 4.4 npm run test-main", "test-6": "nave use 6.2 npm run test-main", "test-all": "npm run test-main && npm run test-0.10 && npm run test-0.12 && npm run test-4 && npm run test-6", "test-0.10": "nave use 0.10 npm run test-main", "test-0.12": "nave use 0.12 npm run test-main", "test-main": "(cd test && set -e; for t in ./*.js; do node $t; done)"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/paulmillr/readdirp.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Recursive version of fs.readdir with streaming api.", "directories": {}, "_nodeVersion": "10.9.0", "dependencies": {"micromatch": "^3.1.10", "graceful-fs": "^4.1.2", "readable-stream": "^2.0.2", "set-immediate-shim": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "1.3.2", "nave": "^0.5.1", "through2": "^2.0.0", "proxyquire": "^1.7.9"}, "_npmOperationalInternal": {"tmp": "tmp/readdirp_2.2.0_1536854500346_0.4274468514378924", "host": "s3://npm-registry-packages"}}, "2.2.1": {"name": "readdirp", "version": "2.2.1", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "author": {"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@2.2.1", "maintainers": [{"name": "thlorenz", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/readdirp", "bugs": {"url": "https://github.com/paulmillr/readdirp/issues"}, "dist": {"shasum": "0e87622a3325aa33e892285caf8b4e846529a525", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-2.2.1.tgz", "fileCount": 5, "integrity": "sha512-1JU/8q+VgFZyxwrJ+SVIOsh+KywWGpds3NTqikiKpDMZWScmAYyKIgqkO+ARvNWJfXeXR1zxz7aHF4u4CyH6vQ==", "signatures": [{"sig": "MEUCIQDNICfI1G6RPtsAu2OuKyIECFL+9ftutE69EPuDhxP6DgIgdudbv103Bzh1Aq8L7yUKLHwJQCTA1NagagWwgH2TdAU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21217, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbmpoWCRA9TVsSAnZWagAAfksP/0wK6B4d4pn98vFuBWFV\nUhHDrg547zhR9TGGiKuu0FR6PfWSOjoTFsRJjBv+wWrvkicROn+YyV2DSTPY\nSGL980bRRIXeU7SfuX4CDiTBt0kioR8rEaw34MgKur0EMzwcdzgRx0lQ1dhs\nX1eKZ6nDOBFPBKI16Fle9aUIZNsBSIqo0hPcU+rmudEo4NJE2vkGFe3Fq2Ph\n0IRuWtMRS3zCfqL0baJ+yNHQ0iiQRDimCqPl6/5CNznPq7yTwXspVYgMU85N\n3cR2sciTdoYHtGDw1DojL/b3W++kg1P5qBW6pU8dtx9PoiVNCbYDw0GrUcKf\n0GbZ7LNzj4+MS6Z4AiFwWYJZRrcRriDT5MxIvrA8bEXweua2mGmx7IZe4YSQ\nf5B/DsNKoYeL8OuNBscxHsnRH9WSprZtlk4sZjfukV2UMVkgWzE04UMufZ46\nVlpPw3hCA6Cd5k6Q2jJ5ID0jDJKbCxmiWNCiraKqOwHL/aho61Un0wbAEMl7\nVC6brtN/IH40NY9dUHGFjbIX6hYKW/oadEZ1glS+iraspBWK1DaqFZIrOZ1I\nQ/wVur333b3JVZvmeM4BOIMmmyeG6Wqfz45SMMLwLGs54sN7U6Ji8CyGqk2F\nWukXq+ijjaDcquNNoNlz8DiIEYzs6EO1wARaeuLsFtAsWfU9EkjVvI4YYmUU\nPG9C\r\n=pWI8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "readdirp.js", "engines": {"node": ">=0.10"}, "gitHead": "d0f58fd435d7918706128df1742b69bf5f81dac8", "scripts": {"test": "npm run test-main", "test-4": "nave use 4.4 npm run test-main", "test-6": "nave use 6.2 npm run test-main", "test-all": "npm run test-main && npm run test-0.10 && npm run test-0.12 && npm run test-4 && npm run test-6", "test-0.10": "nave use 0.10 npm run test-main", "test-0.12": "nave use 0.12 npm run test-main", "test-main": "(cd test && set -e; for t in ./*.js; do node $t; done)"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/paulmillr/readdirp.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Recursive version of fs.readdir with streaming api.", "directories": {}, "_nodeVersion": "10.9.0", "dependencies": {"micromatch": "^3.1.10", "graceful-fs": "^4.1.11", "readable-stream": "^2.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "1.3.2", "nave": "^0.5.1", "through2": "^2.0.0", "proxyquire": "^1.7.9"}, "_npmOperationalInternal": {"tmp": "tmp/readdirp_2.2.1_1536858645522_0.5020330379451936", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "readdirp", "version": "3.0.0", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "author": {"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@3.0.0", "maintainers": [{"name": "thlorenz", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/readdirp", "bugs": {"url": "https://github.com/paulmillr/readdirp/issues"}, "dist": {"shasum": "d528ababce0382e06e2ab2db6405dc131d2efed2", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-3.0.0.tgz", "fileCount": 5, "integrity": "sha512-XJUBnlLkO37C7eCO9fdYd1HGRnnEq87gGyoQxpvPKLCLc9usnkAjkjvXCcLzrwsgTtjnPd1r1zA7ylUZdFZQow==", "signatures": [{"sig": "MEQCIH8+yHufOPJvN0w3trpslwhl44MHNAVX2zvstcy2bW6zAiB6DlK/axRTZ5H+W8KRoyLNik4+l6TtjFwbaWTTr946VQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17562, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJct2cuCRA9TVsSAnZWagAAo5sP/1nt1Xa7iCmHIwbYiPhm\n3fp2tlYjvb6r33TE7yLTvCMwBna28c8sZoInuXKgSXDbPfTF+CvAZSPxuqUz\n+ZFw4bJG2wazQyvEoypac2hI7Is1qs2tbKE/vUFkGglW0tyNNN2r+Gn7rlnI\nPnaKugIevcssycdOF1p4dJpu64lr5O6Xa89Vjc4nSFT1ckpgZur/ee/vanaT\n3I6aFtvyi4w+GIDHw4uXj6HyiZ+NmvmltzuvOuJm3pJNo1GbLOeAdvXvTsCb\ni/mIXsRvFWnVSl77YcbcxVbhVc+TtYI6ed5eieBijT+fODVOKHFcuaOPiV5f\nJKIs1JWg1OeZLKFMVOeQRdbks0QLcy6FY/jMsbzDrdCg4p4MTacmAETLF0Lz\nL4es+Je63QLXtO0q4mGPymMcli4Qo9jaJ5THRs2stZU6wzcN05C4ysASAZrR\nbrIzXK2R+Y7DX/roLohXKg3eSEigbNxOwtQ7M7N7OzMSslmbFKbikgZSNh4p\nVjmMtZqqaExtupzVmSix/f/iSV2MS/0R1owkDRVfb7TlatX107hU3vy2zmQc\nWKIRDuaBra1SIsv/XjX5Avo/TLlHZPMci5hVKweESsNZFbAaxIDvUL2yOqoS\nd1AjW4BTl6tY4vSqch54CYWqL1O+R8HpR1JDyIJNmMp1gnp21R+5gGY90xor\nF+kq\r\n=HPa8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8"}, "gitHead": "bbfca381c4d153f8ca0e704f82e4f53af147512d", "scripts": {"test": "nyc mocha"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/paulmillr/readdirp.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Recursive version of fs.readdir with streaming api.", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"picomatch": "^2.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.0.0", "chai": "^4.2.0", "mocha": "^6.1.3", "rimraf": "^2.6.3", "@types/chai": "^4.1.7", "@types/node": "^11.13.4", "@types/mocha": "^5.2.6"}, "_npmOperationalInternal": {"tmp": "tmp/readdirp_3.0.0_1555523373222_0.8265975406967714", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "readdirp", "version": "3.0.1", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "author": {"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@3.0.1", "maintainers": [{"name": "thlorenz", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/readdirp", "bugs": {"url": "https://github.com/paulmillr/readdirp/issues"}, "dist": {"shasum": "14a8875883c5575c235579624a1e177cb0b1ec58", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-3.0.1.tgz", "fileCount": 5, "integrity": "sha512-emMp13NEwWQQX1yeDgrzDNCSY7NHV6k9HTW0OhyQqOAzYacbqQhnmWiCYjxNPcqMTQ9k77oXQJp28jkytm3+jg==", "signatures": [{"sig": "MEQCIE8S1+Cgs7ma1RukX+EJCMwIBIA163uICtcf7wHhvzTEAiBmi6yhAz8Aw8r3FHMH76Zrha1YbBstFDWnw+4NAyji5w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17868, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcwNuhCRA9TVsSAnZWagAAKegP/ihNYeX/mRdh8xc/YrQ7\n+WbYOYNCfL5VfzUlMhm+ptYdZ1gCsiOxY8z0ROZfCIH1S6wAXcL2ds3u3XMc\nUAjAXBwCDV2IOZkumIXaQ7fF+bzf1gcttxEFEpWJfMMENiopqPMKf4dz7Lq2\npFeE0Ucj3hW6mAqbzKYfea9iQ36Dxa8shga2P8yNkH/K5eGkzIMbVIWF4nbB\nBxa33/vkdAn/pzBP0AoX0rQiOkpvtozBgZc9NwUui+q6pG9RPDbG7ynxc2Jo\nIx8Hd+fcHYwbBY9rZPgrq9D1Gfqw3eX4zCfwNqWiQXHTSas+3JmZJ+LB/Zex\nggSDrkxd9Bicn55puukzJQwU0mi7W701gMvB6PN4zFKtRRRkDO+dcEfdEfnS\nGGZkAeQC+3ejB63FBUY3iW9Vj2M4TXGKb77VUL0fW2JiJE6PPE3HCJuTVwJi\n1BtEdFE4BPpNt2C0vTay1saO4M9L2sec+pe4MQSoYbxVM3/dMcO7b1KB+myP\nVDsmx7W7IbSx1vtP+d3D48lGnPP4cfQl3eu68c/t2TK3G0dQ57JRyzumJxNG\nKW7hRmnmIyJcwfw/hVj3jERpoqeOv9n2oQEgkLM+P7HArU1OF0LD6DU9AhQr\n6ALo66qSjWk426vSyjBf49YKIW5TAqia7mqnRviKP4qxz9xuNSirDJ0/Tjbx\nqeUH\r\n=8tbN\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8"}, "gitHead": "095b56e99a829757109473c183197ec6b6def3df", "scripts": {"test": "nyc mocha"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/paulmillr/readdirp.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Recursive version of fs.readdir with streaming api.", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"picomatch": "^2.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.0.0", "chai": "^4.2.0", "mocha": "^6.1.3", "rimraf": "^2.6.3", "@types/chai": "^4.1.7", "@types/node": "^11.13.4", "chai-subset": "^1.6.0", "@types/mocha": "^5.2.6"}, "_npmOperationalInternal": {"tmp": "tmp/readdirp_3.0.1_1556143008864_0.34749273061483343", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "readdirp", "version": "3.0.2", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "author": {"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@3.0.2", "maintainers": [{"name": "thlorenz", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/readdirp", "bugs": {"url": "https://github.com/paulmillr/readdirp/issues"}, "dist": {"shasum": "cba63348e9e42fc1bd334b1d2ef895b6a043cbd6", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-3.0.2.tgz", "fileCount": 5, "integrity": "sha512-LbyJYv48eywrhOlScq16H/VkCiGKGPC2TpOdZCJ7QXnYEjn3NN/Oblh8QEU3vqfSRBB7OGvh5x45NKiVeNujIQ==", "signatures": [{"sig": "MEUCIAMAWMait6MnOfdv7hCN7OtCXsFJ/Ye+6W9tI+eLtwk7AiEAvRhMw7BVP7PWuwrfaQG4zIgtA77CblTk0tPq1txJabk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18124, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc9ESsCRA9TVsSAnZWagAAVkgP/0WHdx9d7yhkdVsCn+y3\nlLFRVjr8SH0T9Nh68kqbmdFuuDjgD/SdXBOjoUIhO1ZIos4iXBy12Lxw3naq\nP5z8txkxl0dUERLhsL0zW2U+eh+aVqF69JIILp+U5OmyF+QenOIaGb+PwRum\nmOkIspBOkhFo6L+BJIrv0N/egqOinRTE6CDtPyRhMXJV3lfxQUfUFORC2RX5\n6nSgJKgD/nvvvSReyapFrN4GkFcn7Mi1AaOyqrK2oRDbVVD73sS/Be5+oojE\nlEhBLOvGi3RPAjWqrF+NbDtfw9Xe1ZnxG1R7Rcp/EGMzR5PJyGvJd+hBbTjh\nGGE7hAp6YDPTlDTBt4AkVcPlJX3vKnypNJ3BcfFBOUT9x8+lzBTOEZWvJmZJ\nnUod9WiSK46wjlz3mmG+eWgg+oCy5TPyrsXPEiPiorQEi58iz4A4tvNl3isp\n6aiERWoQAeUFAnliSAVftLDRhE+lE2hbBsGxI9SYnbApGEVeYatRh6Dqf2Z2\nRMeyWp6T7yYdWaKGIQW6y0LJuhNuqm4GeKcxBxD76EH3aporaGyq5Ft/BpSX\nnKS1oAiEeUvXzfmJOCrtX1JMR34A8TJ3XpB0SaHIixH9dOhuSXx3dA3A9koQ\np53C4+tnCB6bNaA1GKrL449YGM8dVpSCIXiiKtkBcdNyoKClPf0wr3fkuq3L\nwtAh\r\n=HwHv\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8"}, "gitHead": "fc867f038f07656b4f277ec37bab8c67216a81b2", "scripts": {"test": "nyc mocha"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/paulmillr/readdirp.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Recursive version of fs.readdir with streaming api.", "directories": {}, "_nodeVersion": "12.1.0", "dependencies": {"picomatch": "^2.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.0.0", "chai": "^4.2.0", "mocha": "^6.1.3", "rimraf": "^2.6.3", "@types/chai": "^4.1.7", "@types/node": "^11.13.4", "chai-subset": "^1.6.0", "@types/mocha": "^5.2.6"}, "_npmOperationalInternal": {"tmp": "tmp/readdirp_3.0.2_1559512236047_0.5975619123617291", "host": "s3://npm-registry-packages"}}, "3.0.3": {"name": "readdirp", "version": "3.0.3", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "author": {"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@3.0.3", "maintainers": [{"name": "thlorenz", "email": "<EMAIL>"}], "contributors": [{"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://paulmillr.com", "name": "<PERSON>"}], "homepage": "https://github.com/paulmillr/readdirp", "bugs": {"url": "https://github.com/paulmillr/readdirp/issues"}, "dist": {"shasum": "6300e1ca8e3ec6fcf064b7cd09e6e4b948d42e27", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-3.0.3.tgz", "fileCount": 5, "integrity": "sha512-ucnUgJo2W/AC6s+dT0RS0rQOQi6PmniKgSqkF1Ung2UAkAqEUcqRVTzi+eWvTMbxicWDKHkbHJvvk98y1+de/w==", "signatures": [{"sig": "MEQCIFumupHntRmdjUmflXJisAKR8JDbMBh0GAcOFj2no1QGAiAYi/+qdw8zhhl0ugApGeCSsN69SR7HMMkF0nMe6Dfe7Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18526, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdH+F6CRA9TVsSAnZWagAAYBQQAJ51F9RNkb022O/XHhFo\natkA1FAld5JrYLNSBtD1/zZJaiOTrv4HIn1RTigwIYMUY6lSiCYARUzh8+rR\naDAmJlmOBEeEYSTFgSMpcexPZ3TOzrRkWaJbP6+lggSeNAQHKxmwN3llrhhy\nFhpboE/GCjLDlBeMvXZD4XjxH5nbY4ITnpAmb4sYt10LZsTxmzvtBbDI6/d0\nwOkAxlbcvoh0PyZe3+VdvwBn9l5bddzrTvhJxnqM0n34BS5pGU663NdtKXVA\nncPFGjUxenuw85qM8gGw5i8axeKS/ynIwo2Fe0zrQVVjg+HGRhWYkil8/lDu\noFyDbl/ET55ZcbMiWNpOeJ+wEQhUW8s6CBaHt2EXFe79pgS8REmO3/Fn/Pdy\nfA68NY8/xeKAWv6zXH8JR/H0fIkGIFSwN4NVp1AIzCDf2P2rHZuAR3CFD9Xl\nAuQaOVGzgq90EVQlEy4YhpqnY9c2H/E1rq7yfLJDkMROT5Rxmey5rn5gqzky\n+cBM8fF6hmpraXWDRg0f/V9AVru7qVaA62WjfbJr03S/Zyr3Maj5ca93okHK\nUlqeqIoS9JHdmTl1W4BBcM+jckQ0GytvuLGSXvJyNUnvYo2ofS+oBLtIyBpo\nuxVEQPIv2iiyzSyf4AjpvkxoSCwrkjursN3t5z9aPUpS9a8AS17CB3tKDCvp\nseiH\r\n=M0XO\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8"}, "gitHead": "a09d34bfc04c98efccc2e560c2537e667a679982", "scripts": {"test": "nyc mocha"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/paulmillr/readdirp.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Recursive version of fs.readdir with streaming api.", "directories": {}, "_nodeVersion": "12.5.0", "dependencies": {"picomatch": "^2.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2", "mocha": "^6.1.3", "rimraf": "^2.6.3", "@types/chai": "^4.1", "@types/node": "^12", "chai-subset": "^1.6", "@types/mocha": "^5.2"}, "_npmOperationalInternal": {"tmp": "tmp/readdirp_3.0.3_1562370426080_0.40728261735390325", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "readdirp", "version": "3.1.0", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "author": {"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@3.1.0", "maintainers": [{"name": "thlorenz", "email": "<EMAIL>"}], "contributors": [{"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://paulmillr.com", "name": "<PERSON>"}], "homepage": "https://github.com/paulmillr/readdirp", "bugs": {"url": "https://github.com/paulmillr/readdirp/issues"}, "dist": {"shasum": "c033ba515a2c77c0e81ffadecda6eb36ac19df7a", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-3.1.0.tgz", "fileCount": 5, "integrity": "sha512-iIj0CKV8k1WgcKZrP310W9Sme9qZyL/pldfGGnX2LoxMMXDYn1smRyjAIZibHXjCD0sTYx6Oqw7H2vWrODGlLw==", "signatures": [{"sig": "MEYCIQDXfG2hDfgPFtuzBIDXwkNhfY4u2cAzM6vNiG/ddJKyogIhAOGv+LtX1V/sYIj89s5fJqKX4pNxK5oRhuStGwCU6oqU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18526, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdITOLCRA9TVsSAnZWagAAo4QP/0lzKBFQEHrlsgKFQkKh\nfMZugIm7Ya7NjFW+q+iXE8GCU7lcFTxiT/sDPp7hDQZDvxd0Y4V3jequOAFe\nZEC1KB1zHz3Cgs07DsApZUXS7Iacxt9K72q5Q0w7CMrknZ+LvCCLKvJcTN7M\nbmqB9+ynzNAytvekTQ1PL5fsl1hwMXEPLvPcDXN54MKsCrx0LQmu4MrfKtM3\nxYqK0ko9xvVXe5dxd02ScfFxE7lNvbUZHfU4wvnZUCIlHuwx691zuYaUL8k0\n7ciUIByksmlu3Te39VhHWlP7bRofAsKGtIwPZnK6AgZjZPHTfRAuCvEgCajA\nsXEXlC01psd48xUY1Hp1QhDOnnTqMYT7XgwWUjcI8UO5uLwLO4xZZ/pqJGjS\nPx+TahQX2CHoE6Y6w4HdQRvMZF+VEAhCCMukl1JTvgZS2v6UrlgDYcftcwBu\ntCyxopu3AikMzGxhZ4ZAIycw6vfh2PSP39PicqwrQhBJtn6+f0O3jxT/e0oy\neMIp5am7oIs28+PmazhrmeWU8lZi6zVzC3gPtdVmnIz8xDrZ1I+Nzjaa9lH8\n9L+I5N1RvE50l/630C8mR3WFKgVWw3+H/He6Lw2f7PYl5kx1XeAATIm0Tcwu\ntG5LZd5CR2P25WYcnp0vC2ZEEg2eTnJE5F5NyC7/odTS2hBVKJo2NPWyB9UY\nf9L4\r\n=EYTO\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8"}, "gitHead": "a09d34bfc04c98efccc2e560c2537e667a679982", "scripts": {"test": "nyc mocha"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/paulmillr/readdirp.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Recursive version of fs.readdir with streaming api.", "directories": {}, "_nodeVersion": "12.5.0", "dependencies": {"picomatch": "^2.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2", "mocha": "^6.1.3", "rimraf": "^2.6.3", "@types/chai": "^4.1", "@types/node": "^12", "chai-subset": "^1.6", "@types/mocha": "^5.2"}, "_npmOperationalInternal": {"tmp": "tmp/readdirp_3.1.0_1562456970909_0.16153477731141663", "host": "s3://npm-registry-packages"}}, "3.1.1": {"name": "readdirp", "version": "3.1.1", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "author": {"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@3.1.1", "maintainers": [{"name": "thlorenz", "email": "<EMAIL>"}], "contributors": [{"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://paulmillr.com", "name": "<PERSON>"}], "homepage": "https://github.com/paulmillr/readdirp", "bugs": {"url": "https://github.com/paulmillr/readdirp/issues"}, "dist": {"shasum": "b158123ac343c8b0f31d65680269cc0fc1025db1", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-3.1.1.tgz", "fileCount": 5, "integrity": "sha512-XXdSXZrQuvqoETj50+JAitxz1UPdt5dupjT6T5nVB+WvjMv2XKYj+s7hPeAVCXvmJrL36O4YYyWlIC3an2ePiQ==", "signatures": [{"sig": "MEUCIQDix3+Vt2x6v5Tycgwhd/zhIysTuE5PUbDU8+LVEMa+CQIgKiTjImva+TzhOxjiMUyjrErY2WR3Jc6emVTc2XH98J8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18658, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdITRqCRA9TVsSAnZWagAAM7QP/0vewo7MZXR++1K3MnT+\nXDfNaU+YVyhGgZ2a19wzXc+IqmlGKU110FjRq0diJm25VCq/D8WftsiAvxlB\nJ3qmajhi2MbGL05EjIWp95fDw82Zwz1Nfcp8m6NjnWwmbTThkLLyooiTilvR\n8necJQv0eedTKE0Pf+XfNhjtk/eZP/tIdKkLdp9IdD35C9DRXpKqZB67d4jO\nFl9SoWIIJnpcfNAruSJk1bN8WzS0ijl/QZTb1oGHnNSmE+Hv1AAlA/CUwkM8\nyEwTPPyfmXNEl3zpfWM4QGZ3UH1xctk725YhIDnQwZCVsy5qbS5DTEYn1i4t\nMOSNHYm+O9FUArnxBHui4Z0Nnh+oDlcBeUwfXnqu/kJbO5M+54WYbf6kGHu7\no+4QVpdi/3zB/yunYyXwl59u/GV94u1sDeubVN+2a9biv8emCeHvx/2odfka\nzbj7/aMjW5ogmIOnzG3krK+BHeM5/jfE5OThcO6+OkQe67nY+tDd68C31ueA\nPokJcPOG7pIkcS8v9D+LNXik/sA+g1gi/3XH7YdA0a9YULm/50JvwlKHcZul\nV7gx5QuaoGRMQxTPvxvnLejJLllDUW2WoZVcNbuejhG9ZwhHJPv2EsUT4JTA\nx6Kt1p3qXP9rt6HJbRe+FcaSvjX0lxRGdImLdOpthKH02lQrz778QzbEc6N6\n4J0T\r\n=ZVFc\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8"}, "gitHead": "afabb47cedb77281b00210abd6b963a49b213481", "scripts": {"test": "nyc mocha"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/paulmillr/readdirp.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Recursive version of fs.readdir with streaming api.", "directories": {}, "_nodeVersion": "12.5.0", "dependencies": {"picomatch": "^2.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2", "mocha": "^6.1.3", "rimraf": "^2.6.3", "@types/chai": "^4.1", "@types/node": "^12", "chai-subset": "^1.6", "@types/mocha": "^5.2"}, "_npmOperationalInternal": {"tmp": "tmp/readdirp_3.1.1_1562457194295_0.6611801131738466", "host": "s3://npm-registry-packages"}}, "3.1.2": {"name": "readdirp", "version": "3.1.2", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "author": {"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@3.1.2", "maintainers": [{"name": "thlorenz", "email": "<EMAIL>"}], "contributors": [{"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://paulmillr.com", "name": "<PERSON>"}], "homepage": "https://github.com/paulmillr/readdirp", "bugs": {"url": "https://github.com/paulmillr/readdirp/issues"}, "dist": {"shasum": "fa85d2d14d4289920e4671dead96431add2ee78a", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-3.1.2.tgz", "fileCount": 5, "integrity": "sha512-8rhl0xs2cxfVsqzreYCvs8EwBfn/DhVdqtoLmw19uI3SC5avYX9teCurlErfpPXGmYtMHReGaP2RsLnFvz/lnw==", "signatures": [{"sig": "MEQCIDbuVChfqnbcBoiIR1ag4OeJuTikgqDkxKoRf9xjb5ObAiByEUuZe2+UUNU3KMnCAt+Y7h6QH8QtUOfyTJ2bcgQMPw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18936, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVBS/CRA9TVsSAnZWagAA3coP/AxLq4OtPKo2w2gTecNZ\n+PmPF6snX52WJyTBHr99UQxVW5ZnCY11m25fs+o0hwp0at6s7SmFdVCCsMti\nv+5Ns8XMTFotHjOB868Zl5Lwdi2XD0WIqxV7tzCAk0bk6LMLx2KsJXQVNEja\np19ukoojJLT6ETQuZwIeg9K2fCu6ho4Hil0Iq8CHsxUwejXhfcrTDq0CNC13\nZUJHT4xvFQKFHio8OB37BjAfKYvnLQeo8tAYR3pfU1Z12tvQQ0Vw2E64lY07\nt8aNePxvi8Fno4C+EDm5Dy/QSkjQOJdOpJjz3OP8eWgZPXX1EA7PPfp0jpvE\nQRox/tWpaB7iwL+2WsgueYkAGydNFXfFCsq7dPXjtNOdQ2L3n6V/zJRRdyFE\nlGiquXVMWEkPfqyfI6/QmORO3OfbcyoUsOGtYX8v+Be9X1Nml9fiK1PYIYXu\nXy5Io4qA6s07aLmYcbinHZDaLEmdvc2RW6pELiED4hNu0X95l17juB63gYGv\nDf3XvxLV/kFkmCdCLZetFUt3U6vnzX3+h161jj7oDtmCl2a9fKXomrfd4rV3\nGZXzBVDBsbYs9XyBFe0riBdeT3elFttIjF89F0dOIfKG3kU6VYe43/bizNI8\n7x4F5H67VuIasFApMib5yBuOYTmka2L46u6098n74SW7ml30Is2nipJ3Y2+n\nw14P\r\n=jBwp\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8"}, "gitHead": "f3c0eaf1986f20a69d16524c64929bf7334f5acf", "scripts": {"test": "nyc mocha"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/paulmillr/readdirp.git", "type": "git"}, "_npmVersion": "6.10.3", "description": "Recursive version of fs.readdir with streaming api.", "directories": {}, "_nodeVersion": "12.8.0", "dependencies": {"picomatch": "^2.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2", "mocha": "^6.1.3", "rimraf": "^2.6.3", "@types/chai": "^4.1", "@types/node": "^12", "chai-subset": "^1.6", "@types/mocha": "^5.2"}, "_npmOperationalInternal": {"tmp": "tmp/readdirp_3.1.2_1565791423022_0.90671887452009", "host": "s3://npm-registry-packages"}}, "3.1.3": {"name": "readdirp", "version": "3.1.3", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "author": {"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@3.1.3", "maintainers": [{"name": "thlorenz", "email": "<EMAIL>"}], "contributors": [{"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://paulmillr.com", "name": "<PERSON>"}], "homepage": "https://github.com/paulmillr/readdirp", "bugs": {"url": "https://github.com/paulmillr/readdirp/issues"}, "dist": {"shasum": "d6e011ed5b9240a92f08651eeb40f7942ceb6cc1", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-3.1.3.tgz", "fileCount": 5, "integrity": "sha512-ZOsfTGkjO2kqeR5Mzr5RYDbTGYneSkdNKX2fOX2P5jF7vMrd/GNnIAUtDldeHHumHUCQ3V05YfWUdxMPAsRu9Q==", "signatures": [{"sig": "MEQCIGgAlolvCKAOCjukX5RQNGlJxWAjXYQATf+or+JTjIcbAiBqsu+ON+PmbMwIVwGW0UMtiuyGodmWK6ViG6OzxfU4pg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19010, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdkroLCRA9TVsSAnZWagAAquUP/0hQ+/OqqENhiwzMbKmz\nrFf6svv8qngzG5SOyQaCRaQJFsAWkdjYFLxzMFC48j5vUacwEExjZ5DlJUkW\nXOTzXEmKL0pL7H97+65xOTph+9R2Ev8PpQg7TvCS8vzeauN7O0P9zbLUUXGf\nxatc3+TuAa6qq/ib9q8O0jPg97slaCdJgbXWBQPDqZjt+qEIKC92A7E+AgJl\nq9Qsuuz5lcPV/r6giF2W25eS4i/j+cKcjnDMYPwHxKVmAArS1/WYp6yt7JEG\nlr2uOXkxGcvlU7nAckm6SqyZ0I4mSnq039AYo2tC6sZ1JKib36ui+eg3OaRr\nSMpPIMQFTSheEA2eQqsu78Hph9cIjLxOIZ7IMTyykjJfCIXkAWinWbMs3W4R\n2FzohLzI7DXlVvyseRfI8fKIjomvrYgHjK/In7egOYYomaGyEEQVIxqITV3C\n6pu6Gitcv2HPegvPSKCLiMmdjby8OjogAT29C59yvB7noci/kQ79YO+6K+7F\nKWGBAbwQFtwZDTnpxrmAE5yOTnZ9GXtiQUbIXqzLuJKemltVATBOAYrNeudV\nn2eWkkRS6E1JhH0AD+VEKJEAoDEYSSIh9Qma1jC4CVuXcjeSkVQO+sPzaT2h\np2YAWd/nT7fGDVpTgOrZBam77iWBI6kEFpHO/yQbzBwV2mVP0QzkHY3mfjrw\nS2k3\r\n=lGM3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8"}, "gitHead": "7a597f1fdfc764ccc61776b27302b5d897a362ae", "scripts": {"test": "nyc mocha && dtslint"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/paulmillr/readdirp.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Recursive version of fs.readdir with streaming api.", "directories": {}, "_nodeVersion": "12.10.0", "dependencies": {"picomatch": "^2.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2", "mocha": "~6.1.3", "rimraf": "^2.6.3", "dtslint": "^0.9.8", "@types/chai": "^4.1", "@types/node": "^12", "chai-subset": "^1.6", "@types/mocha": "^5.2"}, "_npmOperationalInternal": {"tmp": "tmp/readdirp_3.1.3_1569896970822_0.677183777137714", "host": "s3://npm-registry-packages"}}, "3.2.0": {"name": "readdirp", "version": "3.2.0", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "author": {"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@3.2.0", "maintainers": [{"name": "thlorenz", "email": "<EMAIL>"}], "contributors": [{"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://paulmillr.com", "name": "<PERSON>"}], "homepage": "https://github.com/paulmillr/readdirp", "bugs": {"url": "https://github.com/paulmillr/readdirp/issues"}, "dist": {"shasum": "c30c33352b12c96dfb4b895421a49fd5a9593839", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-3.2.0.tgz", "fileCount": 5, "integrity": "sha512-crk4Qu3pmXwgxdSgGhgA/eXiJAPQiX4GMOZZMXnqKxHX7TaoL+3gQVo/WeuAiogr07DpnfjIMpXXa+PAIvwPGQ==", "signatures": [{"sig": "MEYCIQC0f1hFI2zsKuvwMSFobumQ2/g+8ZJNpDI3XwXEQnoawgIhAKYi2c7cjmCbKeRDxG1rY9fqoLSpLcworV4d7DY1Xr5L", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18844, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdo7ulCRA9TVsSAnZWagAAeZYP/0xjXDuMmT+ANQyuUn+j\nK3sUR1/5mmQhRQbF8RF9NWSPftb665Jtwd6f+sx8DFlGEsx288+VZ6gwEkiE\nC5jIyi19CF33MOAfF6QJFNoMlnmaA8+4upLqCE1DlyrYp/p22QzvEnAEzQ9t\nQ68+y/k6bdTxBLSWiRhNt1fScK28hiEYRN/bUxSwiDABpYdYxEhCnl4S/9/C\nUrPoQL1KzypR0VqfEnODPmXgW43VtGn3Ry0O/ACA6aW9GjNSu5BG2uefbMpg\n8xqFdSUbwvMnjCcHB88+HrPkY/Xth2ALUiiPVfDQ7WiWcNJ3W+pmHWq3NX+Q\nJvlnjHiahWxb/iiYdR1V81WRxU3LUeRopRCb5Ux13YAWomlnwvLA3XyhpETl\nOGrLTx3nSE6rYQpRDjQvBZQUnnySYPCmKgntxP7tiguVI74pLc8QTP38MucJ\nhTkdvK/griwUwv+/qNIGaQ9gsas1z5bbO0igUB7iVgokRq6MtFtok8pap4Hd\naue/NIOeOoREhTyx4IH3FmgRWFpAGfo7ckRnd912vVSZRzgbZWPzhBdBYPdl\n/C+o+61W0DmF/keGaJY2S54u8itySqn94AQQG0kkxVeCMxQ8jt45OeNDq62T\nVZdr+MOVtfyZ3tP6wTuSHITZjcbK3X3/ZkrIy43AGzuvt8n6XPIvVhjfG8GQ\n3ohn\r\n=BUQ1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8"}, "gitHead": "cdfb3df2192e0721be258b28c19b9be1d43fcdd0", "scripts": {"test": "nyc mocha && dtslint"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/paulmillr/readdirp.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Recursive version of fs.readdir with streaming api.", "directories": {}, "_nodeVersion": "12.12.0", "dependencies": {"picomatch": "^2.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2", "mocha": "~6.1.3", "rimraf": "^2.6.3", "dtslint": "^0.9.8", "@types/chai": "^4.1", "@types/node": "^12", "chai-subset": "^1.6", "@types/mocha": "^5.2"}, "_npmOperationalInternal": {"tmp": "tmp/readdirp_3.2.0_1571011492751_0.39603288486516264", "host": "s3://npm-registry-packages"}}, "3.3.0": {"name": "readdirp", "version": "3.3.0", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "author": {"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@3.3.0", "maintainers": [{"name": "thlorenz", "email": "<EMAIL>"}], "contributors": [{"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://paulmillr.com", "name": "<PERSON>"}], "homepage": "https://github.com/paulmillr/readdirp", "bugs": {"url": "https://github.com/paulmillr/readdirp/issues"}, "nyc": {"reporter": ["html", "text"]}, "dist": {"shasum": "984458d13a1e42e2e9f5841b129e162f369aff17", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-3.3.0.tgz", "fileCount": 5, "integrity": "sha512-zz0pAkSPOXXm1viEwygWIPSPkcBYjW1xU5j/JBh5t9bGCJwa6f9+BJa6VaB2g+b55yVrmXzqkyLf4xaWYM0IkQ==", "signatures": [{"sig": "MEQCIApdIXf7nM60b1/ogfqByVIOuilU741C6rdKlJ7oIzolAiB3jOIZvzDEEntULnaw8o9O/Rl7FPfJMDMfE7m09qCjNg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19047, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd6jPnCRA9TVsSAnZWagAA1sYP/R1EmbDs2cTv7FR4DEy1\nbPQGtaKwDHT8kaNEj8L1SuyEs8KxzfoQbOejh2gURoMSLXz3QTIOhT/3rEP8\n/FUrvPaK9NSFUBvbjQzXOwiz1gfsdPhpYlhnhbM5Fl8fyUqpexpwGV0CXBVv\n6aw0vDGb6AccI9rLE6ClK3Kx3+kRXtQJSTMxD5HgoofXV5ZQEtamoWK/XSyN\nwyeKelyVYK+ADvNn7T7kbilKrZ6j3LSGx107/N8liQvxhR1AsN9nzFZWvXsl\nH+UKCkT66YTPUFnr3BkpsEt4BaHn61J1KiGIMxfwTbV636WMFAqBeVCNnG+Q\nNAMQZFS72z60Ck4KqqBdJWSNFq3twyt2750fmPJDx8cm16yuVYNGFlUVDi51\nqbDHw8bO00T25/QzUpKNAi1I9UT+jVat1YB8PNQpKdQN5yLgdPHYoNZ0l751\nneRghmEPogyrJZpD3qIHj83Rhl/lahNuPiDp3onuhow3SPFgALG2ogWaKFX4\nUr4EC/bQd5YyNnH3SOvy3mwK6LFS0NQDoDW5LYHNsvpS/9oVz5Gc2jDeV7M5\nTwj2ndLUTOQgJrZT57M/XDFAKzKVytcIFYnv7TZVBoUlK93wvHCC6DTtIQ/4\npkkl+19PzJrF6uY8xtvupapocUWS8c2GxvrcOvs+FCrpg2a9dOue3od0STuL\nz5Ts\r\n=A6up\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=8.10.0"}, "gitHead": "b9376eb2aad7e7f4dc3352ff8d139ba5e4877519", "scripts": {"nyc": "nyc", "lint": "eslint --report-unused-disable-directives --ignore-path .gitignore .", "test": "npm run lint && nyc npm run mocha", "mocha": "mocha --exit", "dtslint": "dtslint"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/paulmillr/readdirp.git", "type": "git"}, "_npmVersion": "6.13.1", "description": "Recursive version of fs.readdir with streaming API.", "directories": {}, "_nodeVersion": "12.13.1", "dependencies": {"picomatch": "^2.0.7"}, "eslintConfig": {"env": {"es6": true, "node": true}, "root": true, "rules": {"semi": "error", "radix": "error", "no-var": "error", "quotes": ["error", "single"], "strict": "error", "no-empty": ["error", {"allowEmptyCatch": true}], "no-lonely-if": "error", "prefer-const": ["error", {"ignoreReadBeforeAssign": true}], "prefer-spread": "error", "no-else-return": ["error", {"allowElseIf": false}], "prefer-template": "error", "object-shorthand": "error", "prefer-destructuring": ["error", {"array": false, "object": true}], "array-callback-return": "error", "prefer-arrow-callback": ["error", {"allowNamedFunctions": true}]}, "extends": "eslint:recommended", "parserOptions": {"sourceType": "script", "ecmaVersion": 9}}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2", "mocha": "^6.2.2", "eslint": "^6.6.0", "rimraf": "^3.0.0", "dtslint": "^2.0.0", "@types/node": "^12", "chai-subset": "^1.6"}, "_npmOperationalInternal": {"tmp": "tmp/readdirp_3.3.0_1575629798808_0.6718017800899874", "host": "s3://npm-registry-packages"}}, "3.4.0": {"name": "readdirp", "version": "3.4.0", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "author": {"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@3.4.0", "maintainers": [{"name": "thlorenz", "email": "<EMAIL>"}], "contributors": [{"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://paulmillr.com", "name": "<PERSON>"}], "homepage": "https://github.com/paulmillr/readdirp", "bugs": {"url": "https://github.com/paulmillr/readdirp/issues"}, "nyc": {"reporter": ["html", "text"]}, "dist": {"shasum": "9fdccdf9e9155805449221ac645e8303ab5b9ada", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-3.4.0.tgz", "fileCount": 5, "integrity": "sha512-0xe001vZBnJEK+uKcj8qOhyAKPzIT+gStxWr3LCB0DwcXR5NZJ3IaC+yGnHCYzB/S7ov3m3EEbZI2zeNvX+hGQ==", "signatures": [{"sig": "MEUCIQDurecjRlyNEtgy0MGyQ1RlsW0tlu6bvB8H8pfoW0DHUwIgIgiZ/JhKVY9TwtAFbCaQlUM2AgYbwk0tMcABUEyV9Bc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19629, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJec0KVCRA9TVsSAnZWagAALVkQAJ+3qhvu4xMlqtBj+hWj\n4tQbLIy98JPnVOUCzF09Xd3B4DHHarcbFkMIf20k9jQNtmmWmeQC5ehKXwLA\n7DiUP4fZnIsDK0MHw40LUx4NrQKe+K8s+h1Os7wcmspnAEF5mwKppcI7NHDW\nU0Gvs7txyqG5Rni+BB5EZgyibbUYBrbtxRQdmURp30cbFyXhX6GG9BqOACUp\n9dYsCXmE/uS41lDnbnIsQTpYOqS1jqyzfqoyMmMrY9nvLM/r9jkXJVXzSw61\nklzG7CzAE4m/cIUgh4PQlDqCKMYmvxCss4kHOkMbHhVXuGnme6HQWT2r61dz\nKo9KqePIcF/aZnXMSuZxibsTyBO2kAiZwAXhslX+alGJ9JUGZFKSl3tSKnph\njtoy/TMY8Uy8egAfzrNVuhsi/aH3mKJpcUIkU+XCvw9QytGPu7XO9k50x5eM\nr7ODEjfjn+ND9IdBL+cNzwJJUFnQU/RcYBUdPiqr7n1uFwz0yJBOVb92eR6Y\nvGU0Fy6GqkY52eHouvOz5t+TShqKk76TJ+B5YCcu96FdTm7gPQPgz0N07kG4\nIO/s5XXSuZ1ah4yFKEX/9bnCewzi5FB8EveL7h6NF46hyBUmGTiYeo8uCiO3\ns1T+mOTTJysOeL/pCbHeKwRkXLeLXZSnRE2iwinVkTENStU2CDpMk471m0Ot\nnuTk\r\n=z2Qn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=8.10.0"}, "gitHead": "d9c72137579b439fe0f8bb8d668f6b6846c75c45", "scripts": {"nyc": "nyc", "lint": "eslint --report-unused-disable-directives --ignore-path .gitignore .", "test": "npm run lint && nyc npm run mocha", "mocha": "mocha --exit", "dtslint": "dtslint"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/paulmillr/readdirp.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "Recursive version of fs.readdir with streaming API.", "directories": {}, "_nodeVersion": "13.11.0", "dependencies": {"picomatch": "^2.2.1"}, "eslintConfig": {"env": {"es6": true, "node": true}, "root": true, "rules": {"semi": "error", "radix": "error", "no-var": "error", "quotes": ["error", "single"], "strict": "error", "no-empty": ["error", {"allowEmptyCatch": true}], "no-lonely-if": "error", "prefer-const": ["error", {"ignoreReadBeforeAssign": true}], "prefer-spread": "error", "no-else-return": ["error", {"allowElseIf": false}], "prefer-template": "error", "object-shorthand": "error", "prefer-destructuring": ["error", {"array": false, "object": true}], "array-callback-return": "error", "prefer-arrow-callback": ["error", {"allowNamedFunctions": true}]}, "extends": "eslint:recommended", "parserOptions": {"sourceType": "script", "ecmaVersion": 9}}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.2", "mocha": "^7.1.1", "eslint": "^6.6.0", "rimraf": "^3.0.0", "dtslint": "^3.3.0", "@types/node": "^13", "chai-subset": "^1.6"}, "_npmOperationalInternal": {"tmp": "tmp/readdirp_3.4.0_1584611988760_0.3048259818011554", "host": "s3://npm-registry-packages"}}, "3.5.0": {"name": "readdirp", "version": "3.5.0", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "author": {"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@3.5.0", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "thlorenz", "email": "<EMAIL>"}], "contributors": [{"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://paulmillr.com", "name": "<PERSON>"}], "homepage": "https://github.com/paulmillr/readdirp", "bugs": {"url": "https://github.com/paulmillr/readdirp/issues"}, "nyc": {"reporter": ["html", "text"]}, "dist": {"shasum": "9ba74c019b15d365278d2e91bb8c48d7b4d42c9e", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-3.5.0.tgz", "fileCount": 5, "integrity": "sha512-cMhu7c/8rdhkHXWsY+osBhfSy0JikwpHK/5+imo+LpeasTF8ouErHrlYkwT0++njiyuDvc7OFY5T3ukvZ8qmFQ==", "signatures": [{"sig": "MEUCIQCV6ACDyNjInrf9FDk07zDzd+qoFtj1vmYQfOyIcVXQKgIgZU9M93wfiSXM0PiAr0sLiUPIgr6yej9/bR4c61x1iiI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20118, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfhX7WCRA9TVsSAnZWagAAj14P/39OW6eSQNZcF65ocDKQ\nsbcDmKYBabrh8A9UGks3EQimH2VhukrNfXaSYeCB8lb4WIIev7CTpC5s4hAE\n3sCKD5aJ5zY/LUcj66+tnU8qpdR8PnwgAW3hQgKTp1sin/jk5Oh8PIFTdY5j\nro0v4HWqgu81khLwBoT20EVi2Y6ijRa5CGLR1uiBDjVDywgtM64kV0ItF9iZ\ntOtQjvap42PiXp7uD8Hxy/Dbjsrr8jxb0IuPV31XQX0Nlj+RlEKfyrEtXYzO\n5F5FwERSj+SgjcVTgC3F9WNN5WIWrtbFvnRWMxbBVfRrCqz2cYsbHAlqOI+7\nI6OJGGnP1e0bmDTd6g1ORyWH25E5C5rv2aTajEY9fWDrAFKUsAzdLShtYFfF\nNqF2/xKHXkBSXfgq7MY01BF6DnxtW3TsUCiunLuD7AUsZJf+5h3Lg87unkTs\nbYelOYYqs1sk8o7kEWbloSXEm/O/Ao7X3sNc4c/QD2pWI3asVBUFJYOjAS9n\njzzBOUh91zoMHJVp8p5u7fMA5KHccHKi5aF3RsATgTTNYmbUxHJTraVwjOtr\nHwdTkwUZBBz0TIM+3k0l4W574MOrisePGt9oF+tWkREM98jsRMQYi3uzq2/W\nzxuezMOlC3A7cfrBICNOT0o8ZKnRJ4qsE4AGURVrzT9KMwU0Zq7lDSsYlCYy\n7Tqz\r\n=qceD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=8.10.0"}, "gitHead": "887e9f38a9d7bf2a619541179d8b78950fe49b44", "scripts": {"nyc": "nyc", "lint": "eslint --report-unused-disable-directives --ignore-path .gitignore .", "test": "npm run lint && nyc npm run mocha", "mocha": "mocha --exit", "dtslint": "dtslint"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/paulmillr/readdirp.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Recursive version of fs.readdir with streaming API.", "directories": {}, "_nodeVersion": "14.13.1", "dependencies": {"picomatch": "^2.2.1"}, "eslintConfig": {"env": {"es6": true, "node": true}, "root": true, "rules": {"semi": "error", "radix": "error", "no-var": "error", "quotes": ["error", "single"], "strict": "error", "no-empty": ["error", {"allowEmptyCatch": true}], "no-lonely-if": "error", "prefer-const": ["error", {"ignoreReadBeforeAssign": true}], "prefer-spread": "error", "no-else-return": ["error", {"allowElseIf": false}], "prefer-template": "error", "object-shorthand": "error", "prefer-destructuring": ["error", {"array": false, "object": true}], "array-callback-return": "error", "prefer-arrow-callback": ["error", {"allowNamedFunctions": true}]}, "extends": "eslint:recommended", "parserOptions": {"sourceType": "script", "ecmaVersion": 9}}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.2", "mocha": "^7.1.1", "eslint": "^7.0.0", "rimraf": "^3.0.0", "dtslint": "^3.3.0", "typescript": "^4.0.3", "@types/node": "^14", "chai-subset": "^1.6"}, "_npmOperationalInternal": {"tmp": "tmp/readdirp_3.5.0_1602584277620_0.7800107918402821", "host": "s3://npm-registry-packages"}}, "3.6.0": {"name": "readdirp", "version": "3.6.0", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "author": {"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@3.6.0", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "thlorenz", "email": "<EMAIL>"}], "contributors": [{"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://paulmillr.com", "name": "<PERSON>"}], "homepage": "https://github.com/paulmillr/readdirp", "bugs": {"url": "https://github.com/paulmillr/readdirp/issues"}, "nyc": {"reporter": ["html", "text"]}, "dist": {"shasum": "74a370bd857116e245b29cc97340cd431a02a6c7", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz", "fileCount": 5, "integrity": "sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==", "signatures": [{"sig": "MEUCIAMyqngGcA4dviNNK2hVJNVzbcjS7/I4hmoHq/P2CT5wAiEAr7LAs/ppzAHVkTZMBekYzabKfZANfLt4GaVwiCYfAvw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20472, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgTePnCRA9TVsSAnZWagAAAOMP/3OJB0jCHM9D7hKnWj3o\nrxyOUiiCf5rKrligrEh/BrvH78FvQH+CmP/npTXmuik7IgyWUjgTB1vnoRtG\n4ZKAzNFtJINn7nwS/7VrfeZ1EFsbb1trB1w/iCASDo7uQGU11JULh/eBCjON\ndwH3faladGaoT+sFp7JYBcHSqfRBvCAFhl+eFnDnd/8KQa8PMRpm1dtJNk7J\n2tLwA19yy/3vDp1uhFyH7kmcpPBHl3vvN+zdg9LvyTicXyid6YdxPDMLCJS4\nHZr7btHymUr9hBWrlMuJS+su+6GWBgZ9h3DWayNdymzX3zEnNVI2QFarIOQN\ntxRlAyvd7U5uuo1pIsrAzYyOZ0m14Qj3BD0ee1uVL7OnpijmWwrG+M06o3bE\niqza6VgqX4B+5X0VTJ8cGJgcBD3zYMCGu23a4MJClcb6kOXKMgXQfQ4U0YHz\nouLr5yWgaPvTacQQy0koL4Jakf5CS37BvMUduBMhfcKxUSRxkq7nk52uHSH6\nCqsUmbroKL9RUe1ME+H2EOcHRInGP/HLCeWR5to5GpyI4SM3yUEtK8uFi2jp\n4bdh5xqu2WmpQuMuHwyUqufKgRZpQVvKzkSSjQuxzlAALcNByFoH2mfsu17w\nXZX8Ona42sApzoO/J9J7HDDZcWM92XqqcfFlYrhNzjDcihpyYuGht6h4n5nI\nOz/f\r\n=FJ+9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=8.10.0"}, "gitHead": "c1482f9b3b74ecf24adc2733da538ce076ddfbeb", "scripts": {"nyc": "nyc", "lint": "eslint --report-unused-disable-directives --ignore-path .gitignore .", "test": "npm run lint && nyc npm run mocha", "mocha": "mocha --exit", "dtslint": "dtslint"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/paulmillr/readdirp.git", "type": "git"}, "_npmVersion": "7.5.0", "description": "Recursive version of fs.readdir with streaming API.", "directories": {}, "_nodeVersion": "15.8.0", "dependencies": {"picomatch": "^2.2.1"}, "eslintConfig": {"env": {"es6": true, "node": true}, "root": true, "rules": {"semi": "error", "radix": "error", "no-var": "error", "quotes": ["error", "single"], "strict": "error", "no-empty": ["error", {"allowEmptyCatch": true}], "no-lonely-if": "error", "prefer-const": ["error", {"ignoreReadBeforeAssign": true}], "prefer-spread": "error", "no-else-return": ["error", {"allowElseIf": false}], "prefer-template": "error", "object-shorthand": "error", "prefer-destructuring": ["error", {"array": false, "object": true}], "array-callback-return": "error", "prefer-arrow-callback": ["error", {"allowNamedFunctions": true}]}, "extends": "eslint:recommended", "parserOptions": {"sourceType": "script", "ecmaVersion": 9}}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.2", "mocha": "^7.1.1", "eslint": "^7.0.0", "rimraf": "^3.0.0", "dtslint": "^3.3.0", "typescript": "^4.0.3", "@types/node": "^14", "chai-subset": "^1.6"}, "_npmOperationalInternal": {"tmp": "tmp/readdirp_3.6.0_1615717350937_0.9483157819570771", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "readdirp", "version": "4.0.0", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "author": {"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@4.0.0", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "thlorenz", "email": "<EMAIL>"}], "contributors": [{"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://paulmillr.com", "name": "<PERSON>"}], "homepage": "https://github.com/paulmillr/readdirp", "bugs": {"url": "https://github.com/paulmillr/readdirp/issues"}, "nyc": {"reporter": ["html", "text"]}, "dist": {"shasum": "aa900a6deedb59274f3c616021a89129a00a1196", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-4.0.0.tgz", "fileCount": 11, "integrity": "sha512-vhk1KSlF6K9P+pvrsz1g5oKkSgczqWhhQHGZLLLrPs0TnnNQTF3twqgTRWwaBcjTGmwwebnVolVpXwLKxBE3Kg==", "signatures": [{"sig": "MEUCIQDteJ8pxhfXL4BJL9YPkEH07tQ2wuKygKpCET4GLJrzLQIgOUEiqDmZ3tX11mgWcQQupuRyuvl4HScMV8VAtCRWyVA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/readdirp@4.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 52773}, "main": "./index.js", "types": "./index.d.ts", "module": "./esm/index.js", "engines": {"node": ">= 14.16.0"}, "exports": {".": {"import": "./esm/index.js", "require": "./index.js"}}, "funding": {"url": "https://paulmillr.com/funding/", "type": "individual"}, "gitHead": "61109135e9e9cd1bcab5a3f5a81a130127a90b4d", "scripts": {"nyc": "nyc", "lint": "prettier --check index.ts", "test": "nyc npm run mocha", "build": "tsc && tsc -p tsconfig.esm.json", "mocha": "mocha --exit", "format": "prettier --write index.ts"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/paulmillr/readdirp.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Recursive version of fs.readdir with streaming API.", "directories": {}, "sideEffects": false, "_nodeVersion": "20.16.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.0.1", "chai": "4.3.4", "mocha": "10.7.3", "rimraf": "6.0.1", "prettier": "3.1.1", "typescript": "5.5.2", "@types/node": "20.14.8", "chai-subset": "1.6.0", "@paulmillr/jsbt": "0.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/readdirp_4.0.0_1724615490679_0.041146977507308335", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "readdirp", "version": "4.0.1", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "author": {"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@4.0.1", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "thlorenz", "email": "<EMAIL>"}], "contributors": [{"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://paulmillr.com", "name": "<PERSON>"}], "homepage": "https://github.com/paulmillr/readdirp", "bugs": {"url": "https://github.com/paulmillr/readdirp/issues"}, "nyc": {"reporter": ["html", "text"]}, "dist": {"shasum": "b2fe35f8dca63183cd3b86883ecc8f720ea96ae6", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-4.0.1.tgz", "fileCount": 12, "integrity": "sha512-GkMg9uOTpIWWKbSsgwb5fA4EavTR+SG/PMPoAY8hkhHfEEY0/vqljY+XHqtDf2cr2IJtoNRDbrrEpZUiZCkYRw==", "signatures": [{"sig": "MEUCIAnN/rxw9dI06fNCSIRkLOUS3ub2PXOvlBSaeBIjtRIeAiEAk1bfD0dDZplywuxMEDeW9fpxvM6WT6gTBwH4GffkxEo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/readdirp@4.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 52816}, "main": "./index.js", "types": "./index.d.ts", "module": "./esm/index.js", "engines": {"node": ">= 14.16.0"}, "exports": {".": {"import": "./esm/index.js", "require": "./index.js"}}, "funding": {"url": "https://paulmillr.com/funding/", "type": "individual"}, "gitHead": "0e4358920a5a5a4a405f549e7f8fc8641acc1555", "scripts": {"nyc": "nyc", "lint": "prettier --check index.ts", "test": "nyc npm run mocha", "build": "tsc && tsc -p tsconfig.esm.json", "mocha": "mocha --exit", "format": "prettier --write index.ts"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/paulmillr/readdirp.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Recursive version of fs.readdir with streaming API.", "directories": {}, "sideEffects": false, "_nodeVersion": "20.16.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.0.1", "chai": "4.3.4", "mocha": "10.7.3", "rimraf": "6.0.1", "prettier": "3.1.1", "typescript": "5.5.2", "@types/node": "20.14.8", "chai-subset": "1.6.0", "@paulmillr/jsbt": "0.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/readdirp_4.0.1_1724615856967_0.038697290198742795", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "readdirp", "version": "4.0.2", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "author": {"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@4.0.2", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "thlorenz", "email": "<EMAIL>"}], "contributors": [{"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://paulmillr.com", "name": "<PERSON>"}], "homepage": "https://github.com/paulmillr/readdirp", "bugs": {"url": "https://github.com/paulmillr/readdirp/issues"}, "nyc": {"reporter": ["html", "text"]}, "dist": {"shasum": "388fccb8b75665da3abffe2d8f8ed59fe74c230a", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-4.0.2.tgz", "fileCount": 8, "integrity": "sha512-yDMz9g+VaZkqBYS/ozoBJwaBhTbZo3UNYQHNRw1D3UFQB8oHB4uS/tAODO+ZLjGWmUbKnIlOWO+aaIiAxrUWHA==", "signatures": [{"sig": "MEUCIAvq5GJPHpgS+otEhGDL7E1gh6OqfXGJH3boDg7KG5bGAiEAxCb90QplQU9xdL7IJSygVC1VFRHE/5xFVsP9kKLezQg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/readdirp@4.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 32182}, "main": "./index.js", "types": "./index.d.ts", "module": "./esm/index.js", "engines": {"node": ">= 14.16.0"}, "exports": {".": {"import": "./esm/index.js", "require": "./index.js"}}, "funding": {"url": "https://paulmillr.com/funding/", "type": "individual"}, "gitHead": "367f35f961436e6fd9df18f2c0066ced03b11f8a", "scripts": {"nyc": "nyc", "lint": "prettier --check index.ts", "test": "nyc npm run mocha", "build": "tsc && tsc -p tsconfig.esm.json", "mocha": "mocha --exit", "format": "prettier --write index.ts"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/paulmillr/readdirp.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "Recursive version of fs.readdir with streaming API.", "directories": {}, "sideEffects": false, "_nodeVersion": "20.17.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.0.1", "chai": "4.3.4", "mocha": "10.7.3", "rimraf": "6.0.1", "prettier": "3.1.1", "typescript": "5.5.2", "@types/node": "20.14.8", "chai-subset": "1.6.0", "@paulmillr/jsbt": "0.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/readdirp_4.0.2_1727965945803_0.2810957524453961", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "readdirp", "version": "4.1.0", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "author": {"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readdirp@4.1.0", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "thlorenz", "email": "<EMAIL>"}], "contributors": [{"url": "thlorenz.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://paulmillr.com", "name": "<PERSON>"}], "homepage": "https://github.com/paulmillr/readdirp", "bugs": {"url": "https://github.com/paulmillr/readdirp/issues"}, "dist": {"shasum": "84f8c468aebc665a83fc423c332894f35e50db49", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-4.1.0.tgz", "fileCount": 8, "integrity": "sha512-4+hHiVsxlm4OVSNFpAIrOGyGeG9kNLGcLMqvSGL5Rj2NOYBDQiQ6lJRViwAZ80i8SNbY8kCpdjgJy5PNALARew==", "signatures": [{"sig": "MEQCIBNjKIz8PYtETLPe4uO8HgvNBWwnwgzIrhh9KOVn1wNOAiAfiYaIK//a/CBupD6denEKRtrBucI7MTlHAqagJBl1rA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/readdirp@4.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 36253}, "main": "./index.js", "type": "module", "types": "./index.d.ts", "module": "./esm/index.js", "engines": {"node": ">= 14.18.0"}, "exports": {".": {"import": "./esm/index.js", "require": "./index.js"}}, "funding": {"url": "https://paulmillr.com/funding/", "type": "individual"}, "gitHead": "3600b8765d6bdc3c20eb4052fdd58930868619bb", "scripts": {"lint": "prettier --check index.ts index.test.js", "test": "node index.test.js", "build": "tsc && tsc -p tsconfig.esm.json", "format": "prettier --write index.ts index.test.js", "test:coverage": "c8 node index.test.js"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/paulmillr/readdirp.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Recursive version of fs.readdir with small RAM & CPU footprint.", "directories": {}, "sideEffects": false, "_nodeVersion": "22.12.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "10.1.3", "chai": "4.3.4", "prettier": "3.1.1", "typescript": "5.5.2", "@types/node": "20.14.8", "chai-subset": "1.6.0", "micro-should": "0.4.0", "@paulmillr/jsbt": "0.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/readdirp_4.1.0_1736613032222_0.31533607529239305", "host": "s3://npm-registry-packages-npm-production"}}, "4.1.1": {"name": "readdirp", "description": "Recursive version of fs.readdir with small RAM & CPU footprint.", "version": "4.1.1", "homepage": "https://github.com/paulmillr/readdirp", "repository": {"type": "git", "url": "git://github.com/paulmillr/readdirp.git"}, "license": "MIT", "bugs": {"url": "https://github.com/paulmillr/readdirp/issues"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "thlorenz.com"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "thlorenz.com"}, {"name": "<PERSON>", "url": "https://paulmillr.com"}], "engines": {"node": ">= 14.18.0"}, "main": "./index.js", "module": "./esm/index.js", "types": "./index.d.ts", "exports": {".": {"import": "./esm/index.js", "require": "./index.js"}}, "sideEffects": false, "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "scripts": {"build": "tsc && tsc -p tsconfig.esm.json", "lint": "prettier --check index.ts test/index.test.js", "format": "prettier --write index.ts test/index.test.js", "test": "node test/index.test.js", "test:coverage": "c8 node test/index.test.js"}, "devDependencies": {"@paulmillr/jsbt": "0.2.1", "@types/node": "20.14.8", "c8": "10.1.3", "chai": "4.3.4", "chai-subset": "1.6.0", "micro-should": "0.4.0", "prettier": "3.1.1", "typescript": "5.5.2"}, "funding": {"type": "individual", "url": "https://paulmillr.com/funding/"}, "_id": "readdirp@4.1.1", "gitHead": "3dbe87b67c3f7fe7908e8164a29df2b5580f6e53", "_nodeVersion": "22.12.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-h80JrZu/MHUZCyHu5ciuoI0+WxsCxzxJTILn6Fs8rxSnFPh+UVHYfeIxK1nVGugMqkfC4vJcBOYbkfkwYK0+gw==", "shasum": "bd115327129672dc47f87408f05df9bd9ca3ef55", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-4.1.1.tgz", "fileCount": 8, "unpackedSize": 36253, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/readdirp@4.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBm4aKY80a6GiV1tSaFs4tVwGkN0ycxy9tGglski8hAfAiAm02Euo2CfXdBkYSkvkFz3NalIyRPm9UvKQEhyLb+C7Q=="}]}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "thlorenz", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/readdirp_4.1.1_1736617895445_0.412630955945823"}, "_hasShrinkwrap": false}}, "time": {"created": "2012-07-18T11:44:10.802Z", "modified": "2025-01-11T17:51:36.028Z", "0.1.0": "2012-07-18T11:44:11.200Z", "0.1.1": "2012-07-18T12:08:10.372Z", "0.1.2": "2012-09-13T02:55:04.808Z", "0.1.3": "2012-09-13T03:01:41.172Z", "0.1.4": "2012-09-16T00:10:43.993Z", "0.2.0": "2012-10-07T22:23:32.951Z", "0.2.1": "2012-10-09T01:28:45.122Z", "0.2.2": "2012-12-31T20:16:55.863Z", "0.2.3": "2013-01-19T05:25:00.867Z", "0.2.4": "2013-03-19T02:27:47.074Z", "0.2.5": "2013-06-22T01:42:42.820Z", "0.3.0": "2013-06-22T14:36:39.116Z", "0.3.1": "2013-07-17T12:33:55.195Z", "0.3.2": "2013-12-15T22:54:33.167Z", "0.3.3": "2014-01-13T15:35:44.575Z", "0.4.0": "2014-03-29T22:51:01.775Z", "1.0.0": "2014-03-30T03:01:35.038Z", "1.0.1": "2014-03-30T03:37:40.737Z", "1.1.0": "2014-06-11T15:16:06.903Z", "1.2.0": "2014-12-01T21:09:56.324Z", "1.3.0": "2014-12-12T21:22:09.908Z", "1.4.0": "2015-07-21T18:40:39.811Z", "2.0.0": "2015-08-25T20:09:10.272Z", "2.0.1": "2016-06-22T17:50:05.562Z", "2.1.0": "2016-06-27T18:57:01.706Z", "2.2.0": "2018-09-13T16:01:40.496Z", "2.2.1": "2018-09-13T17:10:45.627Z", "3.0.0": "2019-04-17T17:49:33.406Z", "3.0.1": "2019-04-24T21:56:48.947Z", "3.0.2": "2019-06-02T21:50:36.246Z", "3.0.3": "2019-07-05T23:47:06.235Z", "3.1.0": "2019-07-06T23:49:31.019Z", "3.1.1": "2019-07-06T23:53:14.423Z", "3.1.2": "2019-08-14T14:03:43.139Z", "3.1.3": "2019-10-01T02:29:30.949Z", "3.2.0": "2019-10-14T00:04:52.884Z", "3.3.0": "2019-12-06T10:56:38.944Z", "3.4.0": "2020-03-19T09:59:49.025Z", "3.5.0": "2020-10-13T10:17:57.720Z", "3.6.0": "2021-03-14T10:22:31.129Z", "4.0.0": "2024-08-25T19:51:30.850Z", "4.0.1": "2024-08-25T19:57:37.090Z", "4.0.2": "2024-10-03T14:32:25.960Z", "4.1.0": "2025-01-11T16:30:32.431Z", "4.1.1": "2025-01-11T17:51:35.641Z"}, "bugs": {"url": "https://github.com/paulmillr/readdirp/issues"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "thlorenz.com"}, "license": "MIT", "homepage": "https://github.com/paulmillr/readdirp", "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter"], "repository": {"type": "git", "url": "git://github.com/paulmillr/readdirp.git"}, "description": "Recursive version of fs.readdir with small RAM & CPU footprint.", "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "thlorenz.com"}, {"name": "<PERSON>", "url": "https://paulmillr.com"}], "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "thlorenz", "email": "<EMAIL>"}], "readme": "# readdirp [![Weekly downloads](https://img.shields.io/npm/dw/readdirp.svg)](https://github.com/paulmillr/readdirp)\n\nRecursive version of fs.readdir. Exposes a **stream API** (with small RAM & CPU footprint) and a **promise API**.\n\n```sh\nnpm install readdirp\njsr add jsr:@paulmillr/readdirp\n```\n\n```javascript\n// Use streams to achieve small RAM & CPU footprint.\n// 1) Streams example with for-await.\nimport readdirp from 'readdirp';\nfor await (const entry of readdirp('.')) {\n  const {path} = entry;\n  console.log(`${JSON.stringify({path})}`);\n}\n\n// 2) Streams example, non for-await.\n// Print out all JS files along with their size within the current folder & subfolders.\nimport readdirp from 'readdirp';\nreaddirp('.', {alwaysStat: true, fileFilter: (f) => f.basename.endsWith('.js')})\n  .on('data', (entry) => {\n    const {path, stats: {size}} = entry;\n    console.log(`${JSON.stringify({path, size})}`);\n  })\n  // Optionally call stream.destroy() in `warn()` in order to abort and cause 'close' to be emitted\n  .on('warn', error => console.error('non-fatal error', error))\n  .on('error', error => console.error('fatal error', error))\n  .on('end', () => console.log('done'));\n\n// 3) Promise example. More RAM and CPU than streams / for-await.\nimport { readdirpPromise } from 'readdirp';\nconst files = await readdirpPromise('.');\nconsole.log(files.map(file => file.path));\n\n// Other options.\nimport readdirp from 'readdirp';\nreaddirp('test', {\n  fileFilter: (f) => f.basename.endsWith('.js'),\n  directoryFilter: (d) => d.basename !== '.git',\n  // directoryFilter: (di) => di.basename.length === 9\n  type: 'files_directories',\n  depth: 1\n});\n```\n\n## API\n\n`const stream = readdirp(root[, options])` — **Stream API**\n\n- Reads given root recursively and returns a `stream` of [entry infos](#entryinfo)\n- Optionally can be used like `for await (const entry of stream)` with node.js 10+ (`asyncIterator`).\n- `on('data', (entry) => {})` [entry info](#entryinfo) for every file / dir.\n- `on('warn', (error) => {})` non-fatal `Error` that prevents a file / dir from being processed. Example: inaccessible to the user.\n- `on('error', (error) => {})` fatal `Error` which also ends the stream. Example: illegal options where passed.\n- `on('end')` — we are done. Called when all entries were found and no more will be emitted.\n- `on('close')` — stream is destroyed via `stream.destroy()`.\n  Could be useful if you want to manually abort even on a non fatal error.\n  At that point the stream is no longer `readable` and no more entries, warning or errors are emitted\n- To learn more about streams, consult the very detailed [nodejs streams documentation](https://nodejs.org/api/stream.html)\n  or the [stream-handbook](https://github.com/substack/stream-handbook)\n\n`const entries = await readdirp.promise(root[, options])` — **Promise API**. Returns a list of [entry infos](#entryinfo).\n\nFirst argument is awalys `root`, path in which to start reading and recursing into subdirectories.\n\n### options\n\n- `fileFilter`: filter to include or exclude files\n    - **Function**: a function that takes an entry info as a parameter and returns true to include or false to exclude the entry\n- `directoryFilter`: filter to include/exclude directories found and to recurse into. Directories that do not pass a filter will not be recursed into.\n- `depth: 5`: depth at which to stop recursing even if more subdirectories are found\n- `type: 'files'`: determines if data events on the stream should be emitted for `'files'` (default), `'directories'`, `'files_directories'`, or `'all'`. Setting to `'all'` will also include entries for other types of file descriptors like character devices, unix sockets and named pipes.\n- `alwaysStat: false`: always return `stats` property for every file. Default is `false`, readdirp will return `Dirent` entries. Setting it to `true` can double readdir execution time - use it only when you need file `size`, `mtime` etc. Cannot be enabled on node <10.10.0.\n- `lstat: false`: include symlink entries in the stream along with files. When `true`, `fs.lstat` would be used instead of `fs.stat`\n\n### `EntryInfo`\n\nHas the following properties:\n\n- `path: 'assets/javascripts/react.js'`: path to the file/directory (relative to given root)\n- `fullPath: '/Users/<USER>/projects/app/assets/javascripts/react.js'`: full path to the file/directory found\n- `basename: 'react.js'`: name of the file/directory\n- `dirent: fs.Dirent`: built-in [dir entry object](https://nodejs.org/api/fs.html#fs_class_fs_dirent) - only with `alwaysStat: false`\n- `stats: fs.Stats`: built in [stat object](https://nodejs.org/api/fs.html#fs_class_fs_stats) - only with `alwaysStat: true`\n\n## Changelog\n\n- 4.0 (Aug 25, 2024) rewritten in typescript, producing hybrid common.js / esm module.\n    - Remove glob support and all dependencies\n    - Make sure you're using `let {readdirp} = require('readdirp')` in common.js\n- 3.5 (Oct 13, 2020) disallows recursive directory-based symlinks.\n  Before, it could have entered infinite loop.\n- 3.4 (Mar 19, 2020) adds support for directory-based symlinks.\n- 3.3 (Dec 6, 2019) stabilizes RAM consumption and enables perf management with `highWaterMark` option. Fixes race conditions related to `for-await` looping.\n- 3.2 (Oct 14, 2019) improves performance by 250% and makes streams implementation more idiomatic.\n- 3.1 (Jul 7, 2019) brings `bigint` support to `stat` output on Windows. This is backwards-incompatible for some cases. Be careful. It you use it incorrectly, you'll see \"TypeError: Cannot mix BigInt and other types, use explicit conversions\".\n- 3.0 brings huge performance improvements and stream backpressure support.\n- Upgrading 2.x to 3.x:\n    - Signature changed from `readdirp(options)` to `readdirp(root, options)`\n    - Replaced callback API with promise API.\n    - Renamed `entryType` option to `type`\n    - Renamed `entryType: 'both'` to `'files_directories'`\n    - `EntryInfo`\n        - Renamed `stat` to `stats`\n            - Emitted only when `alwaysStat: true`\n            - `dirent` is emitted instead of `stats` by default with `alwaysStat: false`\n        - Renamed `name` to `basename`\n        - Removed `parentDir` and `fullParentDir` properties\n- Supported node.js versions:\n    - 4.x: node 14+\n    - 3.x: node 8+\n    - 2.x: node 0.6+\n\n## License\n\nCopyright (c) 2012-2019 Thorsten Lorenz, Paul Miller (<https://paulmillr.com>)\n\nMIT License, see [LICENSE](LICENSE) file.\n", "readmeFilename": "README.md", "users": {"hr.": true, "pid": true, "akiva": true, "fozzy": true, "jruif": true, "lgh06": true, "three": true, "eshinn": true, "hughsk": true, "madvas": true, "marsup": true, "mrzmmr": true, "nuwaio": true, "alectic": true, "asaupup": true, "thelmos": true, "esundahl": true, "zuojiang": true, "youstrive": true, "sbruchmann": true, "silverwind": true, "balazserdos": true, "tunnckocore": true, "wkronmiller": true, "jahnestacado": true, "johnnyscript": true}}