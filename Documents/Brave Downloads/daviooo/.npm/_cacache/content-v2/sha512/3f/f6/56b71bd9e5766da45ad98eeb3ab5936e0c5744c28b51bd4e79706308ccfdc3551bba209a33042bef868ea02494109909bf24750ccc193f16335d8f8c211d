{"_id": "is-weakmap", "_rev": "12-a6368b1743dc4d5fc2468954fae717c3", "name": "is-weakmap", "description": "Is this value a JS WeakMap? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "dist-tags": {"latest": "2.0.2"}, "versions": {"1.0.0": {"name": "is-weakmap", "version": "1.0.0", "description": "Easily check if a givin object is an ES6 WeakMap", "keywords": ["weakmap", "es6", "esnext", "harmony", "typeof", "type", "object"], "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "files": ["index.js"], "repository": {"type": "git", "url": "https://github.com/arthurvr/is-weakmap"}, "scripts": {"test": "node test/test.js"}, "devDependencies": {"ava": "^0.0.4"}, "engines": {"node": ">=0.12.0"}, "gitHead": "9ca06fb61ad97b1253e6f1f0defbe10ddfbbd861", "bugs": {"url": "https://github.com/arthurvr/is-weakmap/issues"}, "homepage": "https://github.com/arthurvr/is-weakmap", "_id": "is-weakmap@1.0.0", "_shasum": "831dc5858bb405318e78278a2f9f4a56e77cdc82", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "1.2.0", "_npmUser": {"name": "art<PERSON><PERSON>r", "email": "<EMAIL>"}, "maintainers": [{"name": "art<PERSON><PERSON>r", "email": "<EMAIL>"}], "dist": {"shasum": "831dc5858bb405318e78278a2f9f4a56e77cdc82", "tarball": "https://registry.npmjs.org/is-weakmap/-/is-weakmap-1.0.0.tgz", "integrity": "sha512-m84udKOJKCy9hcVVtV3BtaR34JQbGbSrkW2JvKfwfjUCNVT600UkMe2831jTY70DEkvVnYbxFF/PNr/mSGizEQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCJ7X9j2nziG4N8Dar0rvQX+zKgShfbDnBSG4Z74SJxMgIhAPUqdt289KUavvsdpEuHCUUckR8mKMYevVoA6hct6LxE"}]}, "directories": {}}, "1.0.1": {"name": "is-weakmap", "version": "1.0.1", "description": "Easily check if a givin object is an ES6 WeakMap", "keywords": ["weakmap", "es6", "esnext", "harmony", "typeof", "type", "object"], "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "files": ["index.js"], "repository": {"type": "git", "url": "git+https://github.com/arthurvr/is-weakmap.git"}, "scripts": {"test": "xo && node test.js"}, "devDependencies": {"ava": "0.0.4", "xo": "*"}, "engines": {"node": ">=0.12.0"}, "xo": {"envs": ["node"]}, "gitHead": "398b5ca28d33c3e712ccfe0492ed66d9ec104d89", "bugs": {"url": "https://github.com/arthurvr/is-weakmap/issues"}, "homepage": "https://github.com/arthurvr/is-weakmap#readme", "_id": "is-weakmap@1.0.1", "_shasum": "ba2b14ee17c918a3bcd69869b66409816f1190e1", "_from": ".", "_npmVersion": "2.13.0", "_nodeVersion": "2.4.0", "_npmUser": {"name": "art<PERSON><PERSON>r", "email": "<EMAIL>"}, "dist": {"shasum": "ba2b14ee17c918a3bcd69869b66409816f1190e1", "tarball": "https://registry.npmjs.org/is-weakmap/-/is-weakmap-1.0.1.tgz", "integrity": "sha512-WOhR6aIz4hUVOLC1SQPf5MJUAykkqbHSHJulO1f2u4ljvn7WSde8c4Q0bV7k7cubyxEqYBPjGwzokHYOZvXUdQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC/gRI6FR+F+MFOM7lvPf5TbK698Q4ykRsMDPUMaZId6wIge796seIqiy1ol2ONNoAr5nyNKEmtYbK3sFM1Zid0a6k="}]}, "maintainers": [{"name": "art<PERSON><PERSON>r", "email": "<EMAIL>"}], "directories": {}}, "2.0.0": {"name": "is-weakmap", "version": "2.0.0", "description": "Is this value a JS WeakMap? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "main": "index.js", "scripts": {"version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublish": "safe-publish-latest", "pretest": "npm run lint", "lint": "eslint .", "tests-only": "node test", "posttests-only": "node -e \"require('es5-shim'); require('es6-shim'); require('./test');\"", "test": "npm run tests-only", "posttest": "npx aud"}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/is-weakmap.git"}, "keywords": ["map", "weakmap", "set", "weakset", "collection", "is", "robust"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/is-weakmap/issues"}, "homepage": "https://github.com/inspect-js/is-weakmap#readme", "devDependencies": {"@ljharb/eslint-config": "^15.0.1", "auto-changelog": "^1.16.2", "es5-shim": "^4.5.13", "es6-shim": "^0.35.5", "eslint": "^6.6.0", "for-each": "^0.3.3", "object-inspect": "^1.7.0", "safe-publish-latest": "^1.1.4", "tape": "^4.11.0"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "gitHead": "031ced9808ead4947a7d81ae13efa6511d3f2487", "_id": "is-weakmap@2.0.0", "_nodeVersion": "13.1.0", "_npmVersion": "6.12.1", "dist": {"integrity": "sha512-vZKPGTqgcrNWsav9/lvXA4DvZtxqzdkSm624p85UvZV47pk1cvw+JQ5YrZfQ1KqsogYxnIztAtu4WBdPNz/n/w==", "shasum": "88bd0b8ec3a5724477637d58ed96cb00552593fd", "tarball": "https://registry.npmjs.org/is-weakmap/-/is-weakmap-2.0.0.tgz", "fileCount": 9, "unpackedSize": 9046, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdyxTJCRA9TVsSAnZWagAAdzUP/1JAHo3sKhvF0tI0hZhv\nCdcj7FLdopzAvVglOTw01TsamKQ4gMFb2LZV2u7EJIh9OcPI6osPhddcGNqU\nDh/wDAPx/iNFPCIwrz0XM945VXpA1iHeYNqU9pqLPMLCbEGgG6+tyWwxixZF\n7X0plr8VfG0kVpmFlr8F1/WfsJirRbvEHeUhFs2aJnR8WGp/kyUNByN3+Zkl\n/KKaHu3wvSEv/YlJUPV3ATyHmGvYVHwabJ+92I7QifAZAcEBwFGLq7bIeUBi\nVF+8YK2K046HO4JUlOr2GYkJWfIABQmgPTCjGYBX86zCVaMViWEWXjWTmsCV\nc7c3EQuWGNVDle6YAtx3/kZE264rHZdFrJN7MzX1FdpEzJ+Yw6UALdHD750S\nSq62yQKk2PwRMPTnCjTHoBEAbeFo4i+wHuZguqMnX8z6ZkALSim0UlvP0bcz\nxX8Ks+BBbCaZWWG3XqoUlICsDUy0/srx366KrmxmlfNCU5E1X5/MGJuuXouf\nDOpuu4fuIHDkNkFEHBiuv1J/9eqrFwoXzb9Y3xdS+4R01UwUGbId0j65NrYG\n/uTZ34UeWQzUeBIWkM3M2i2gFd2T24RMTIAZ+625T7B0yuX1z03h0iBUX+iZ\nvREB2kxnRpTjpel9cOcWocYH7csx3tfjzRRMvs/WWBPk+MIIBQRxijAfaNYf\njkXN\r\n=yKRC\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC5gl9uhEZXj9dV28VHhjY2wOXimdWvxh8onKiEzv90ZgIhAOnyfdiFf68Pzt7WbUTVmKLv5/S+kyFFnps3Lm+egcNE"}]}, "maintainers": [{"email": "<EMAIL>", "name": "lj<PERSON>b"}], "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-weakmap_2.0.0_1573590216572_0.7775777273311031"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "is-weakmap", "version": "2.0.1", "description": "Is this value a JS WeakMap? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "main": "index.js", "scripts": {"version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublish": "safe-publish-latest", "pretest": "npm run lint", "lint": "eslint .", "tests-only": "node test", "posttests-only": "node -e \"require('es5-shim'); require('es6-shim'); require('./test');\"", "test": "npm run tests-only", "posttest": "npx aud"}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/is-weakmap.git"}, "keywords": ["map", "weakmap", "set", "weakset", "collection", "is", "robust"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/is-weakmap/issues"}, "homepage": "https://github.com/inspect-js/is-weakmap#readme", "devDependencies": {"@ljharb/eslint-config": "^15.0.2", "auto-changelog": "^1.16.2", "es5-shim": "^4.5.13", "es6-shim": "^0.35.5", "eslint": "^6.7.2", "for-each": "^0.3.3", "object-inspect": "^1.7.0", "safe-publish-latest": "^1.1.4", "tape": "^4.12.0"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "gitHead": "2ea2585475935605fb66a77304ad0083870a6b8c", "_id": "is-weakmap@2.0.1", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.1", "dist": {"integrity": "sha512-NSBR4kH5oVj1Uwvv970ruUkCV7O1mzgVFO4/rev2cLRda9Tm9HrL70ZPut4rOHgY0FNrUu9BCbXA2sdQ+x0chA==", "shasum": "5008b59bdc43b698201d18f62b37b2ca243e8cf2", "tarball": "https://registry.npmjs.org/is-weakmap/-/is-weakmap-2.0.1.tgz", "fileCount": 10, "unpackedSize": 10071, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd+a8qCRA9TVsSAnZWagAAVT8P/1Nt+VL4hvSIhkspc4j/\nKzsgydu26VhtdCrae0Tbk6Tw7MayhMhZjNR8NHrF8TmCQdYKudllExBhnLTZ\ndsr3mdy3+YuERPfxt8r7xOy3q0hJjtHp9t94ymYew2+Z+Kudc0AiAXBli2vO\nzgGGQbHjkzcfsXnSMbZ4f4Vz6RZ8eaQjFdv7w4htyndadyOWpiq+CkZi2X9u\nFWI/dR66OMWbvQLTTtvY9L/NfMjORRPTZQOr4VLa2vcHQgVlC0aNeQKxThVa\nVaKSTdypHGUJKN18bQ1RKwOoK0nMX3CfbEbG9OWJ4oRNtYN8tpxUJnv6ChF+\nZ8rtNLUjSI1rPcLmNOecpJi70Q50NWZ4QPuMzljZ/9JSmJ0FJVL9rGpmSl9R\nLtVM7tUDuSgRG/74/C3jhIe4fyEx5NnFmSzsVng8BEaZsN/9dzwP3MXtB9tZ\nR+lS88S9FT9iEJox+GDWdmz8a1VYz1yLzz7gx5mtgzsva9QnxDOQeu0XBg/X\nUQ49+Lo5ax7dpLUN5G4QvhJWH06nXPXmO9+HXmcW3SYdeQyKYXsavwdmeeiT\nH4KUqxG8wdz/s6bf2ULAHxAnbpXicDJpaDZOjbae7Jmp1V7TYpAdQ+jXvspz\n4fWG9rw1eGEonBmr91JS9sw8DpsPfyufghAzlC8YtBNrULkBHcdU4h5yl4Rd\nbSHi\r\n=g2EB\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCLFK9x23SKsA3X+DNHNk2EFH/ekmeiiLb34opg5i/k6gIhAMYzleyr79SqZPd8Msy2PR2SLYK5ERMgMl8vghUCF9u8"}]}, "maintainers": [{"email": "<EMAIL>", "name": "lj<PERSON>b"}], "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-weakmap_2.0.1_1576644393747_0.5346311529639225"}, "_hasShrinkwrap": false}, "2.0.2": {"name": "is-weakmap", "version": "2.0.2", "description": "Is this value a JS WeakMap? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "main": "index.js", "sideEffects": true, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run lint", "lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && attw -P", "tests-only": "nyc tape 'test/**/*.js'", "tests:shims": "nyc tape --require=es5-shim --require=es5-shim 'test/**/*.js'", "tests:corejs": "nyc tape --require=core-js 'test/**/*.js'", "test": "npm run tests-only && npm run tests:shims && npm run tests:corejs", "posttest": "aud --production"}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/is-weakmap.git"}, "keywords": ["map", "weakmap", "set", "weakset", "collection", "is", "robust"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/is-weakmap/issues"}, "homepage": "https://github.com/inspect-js/is-weakmap#readme", "devDependencies": {"@arethetypeswrong/cli": "^0.15.0", "@ljharb/eslint-config": "^21.1.0", "@types/for-each": "^0.3.3", "@types/object-inspect": "^1.8.4", "@types/tape": "^5.6.4", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "core-js": "^2.6.12", "es5-shim": "^4.6.7", "es6-shim": "^0.35.8", "eslint": "=8.8.0", "for-each": "^0.3.3", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.1", "safe-publish-latest": "^2.0.0", "tape": "^5.7.5", "typescript": "next"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "engines": {"node": ">= 0.4"}, "_id": "is-weakmap@2.0.2", "gitHead": "7ccef90eb7e20417c70a0fb80ac6fa9dbf7c0dd1", "types": "./index.d.ts", "_nodeVersion": "21.7.0", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-K5pXYOm9wqY1RgjpL3YTkF39tni1XajUIkawTLUo9EZEVUFga5gSQJF8nNS7ZwJQ02y+1YCNYcMh+HIf1ZqE+w==", "shasum": "bf72615d649dfe5f699079c54b83e47d1ae19cfd", "tarball": "https://registry.npmjs.org/is-weakmap/-/is-weakmap-2.0.2.tgz", "fileCount": 12, "unpackedSize": 20571, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC/2rNsALOAQTviCpJ05jszBUHgyt1FalEQ39mN9cmwTgIgc8Q7bY12VAf3DJQOpVVgmaw/Pmw7L0WRXtmaqjHrqho="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-weakmap_2.0.2_1709929111880_0.933051433597637"}, "_hasShrinkwrap": false}}, "readme": "# is-weakmap <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\nIs this value a JS WeakMap? This module works cross-realm/iframe, and despite ES6 @@toStringTag.\n\n## Example\n\n```js\nvar isWeakMap = require('is-weakmap');\nassert(!isWeakMap(function () {}));\nassert(!isWeakMap(null));\nassert(!isWeakMap(function* () { yield 42; return Infinity; });\nassert(!isWeakMap(Symbol('foo')));\nassert(!isWeakMap(1n));\nassert(!isWeakMap(Object(1n)));\n\nassert(!isWeakMap(new Set()));\nassert(!isWeakMap(new WeakSet()));\nassert(!isWeakMap(new Map()));\n\nassert(isWeakMap(new WeakMap()));\n\nclass MyWeakMap extends WeakMap {}\nassert(isWeakMap(new MyWeakMap()));\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n[package-url]: https://npmjs.org/package/is-weakmap\n[npm-version-svg]: https://versionbadg.es/inspect-js/is-weakmap.svg\n[deps-svg]: https://david-dm.org/inspect-js/is-weakmap.svg\n[deps-url]: https://david-dm.org/inspect-js/is-weakmap\n[dev-deps-svg]: https://david-dm.org/inspect-js/is-weakmap/dev-status.svg\n[dev-deps-url]: https://david-dm.org/inspect-js/is-weakmap#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/is-weakmap.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/is-weakmap.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/is-weakmap.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=is-weakmap\n[codecov-image]: https://codecov.io/gh/inspect-js/is-weakmap/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/inspect-js/is-weakmap/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/inspect-js/is-weakmap\n[actions-url]: https://github.com/inspect-js/is-weakmap/actions\n", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "time": {"modified": "2024-03-08T20:18:32.469Z", "created": "2015-02-18T12:32:27.653Z", "1.0.0": "2015-02-18T12:32:27.653Z", "1.0.1": "2015-08-31T20:09:06.050Z", "2.0.0": "2019-11-12T20:23:36.681Z", "2.0.1": "2019-12-18T04:46:33.984Z", "2.0.2": "2024-03-08T20:18:32.058Z"}, "homepage": "https://github.com/inspect-js/is-weakmap#readme", "keywords": ["map", "weakmap", "set", "weakset", "collection", "is", "robust"], "repository": {"type": "git", "url": "git+https://github.com/inspect-js/is-weakmap.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/inspect-js/is-weakmap/issues"}, "license": "MIT", "readmeFilename": "README.md"}