{"_id": "@types/responselike", "_rev": "345-0514f6f195acf8d8505f31e01d4da43d", "name": "@types/responselike", "dist-tags": {"ts2.0": "1.0.0", "ts2.1": "1.0.0", "ts2.2": "1.0.0", "ts2.3": "1.0.0", "ts2.4": "1.0.0", "ts2.5": "1.0.0", "ts2.6": "1.0.0", "ts2.7": "1.0.0", "ts2.8": "1.0.0", "ts2.9": "1.0.0", "ts3.0": "1.0.0", "ts3.1": "1.0.0", "ts3.2": "1.0.0", "ts3.3": "1.0.0", "ts3.4": "1.0.0", "ts3.5": "1.0.0", "ts3.6": "1.0.0", "ts3.7": "1.0.0", "ts3.8": "1.0.0", "ts3.9": "1.0.0", "ts4.0": "1.0.0", "ts4.1": "1.0.0", "ts4.2": "1.0.0", "ts4.3": "1.0.0", "ts4.4": "1.0.0", "ts5.7": "1.0.3", "ts5.6": "1.0.3", "latest": "1.0.3", "ts4.5": "1.0.3", "ts4.6": "1.0.3", "ts4.7": "1.0.3", "ts4.8": "1.0.3", "ts4.9": "1.0.3", "ts5.0": "1.0.3", "ts5.1": "1.0.3", "ts5.2": "1.0.3", "ts5.3": "1.0.3", "ts5.4": "1.0.3", "ts5.5": "1.0.3", "ts5.8": "1.0.3"}, "versions": {"1.0.0": {"name": "@types/responselike", "version": "1.0.0", "license": "MIT", "_id": "@types/responselike@1.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "251f4fe7d154d2bad125abe1b429b23afd262e29", "tarball": "https://registry.npmjs.org/@types/responselike/-/responselike-1.0.0.tgz", "fileCount": 4, "integrity": "sha512-85Y2BjiufFzaMIlvJDvTTB8Fxl2xfLo4HgmHzVBz08w4wDePCTjYw66PdrolO0kzli3yam/YCgRufyo1DdQVTA==", "signatures": [{"sig": "MEQCIAFuK0xa28dUYGitJepQrBte3YF4v8RkBe64UAe1wwbVAiBzRNZwkj3gG0UwRwSkaKXqrIyv1GPpV75fMAUFv3mdfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3618, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcUfK4CRA9TVsSAnZWagAAnVIP/ichQfYe1T1a5DnMt4M6\nI0en7CfISwNMSpFPqQahHwBj4zRFyjIbpR9aW/1j+gKSHt5d6pfrUWgxWwm3\n4mzFsRnMt+kxW+yI9CV5hu8+rmcPGkVgbEep/2n3X07f4kQRoEqaDqu4ppOT\nXusuksbWpXJ0Tl8KUt9BDX5vWeZ8pwVFzQMZED/4cbPnvNvVcjkR+iuEHgM1\nwQe8Rt6uG/c2L2lqFnATdKf1fckeiXkKMPdAH3rFfa9oHpdNknCoCSICiiYL\njse0VYQaHXych1iYmpG8HY49FL6vWR2n1+u3Y/tQKC3NmuwqncCiUOkhGiQf\njEGKqL9izPZ7YHcK+K2u9V58bEDwz6TVgw8smda4WaAvQAFc53ebWNsw/UU9\nfj8ADuZ78D1hHPaThTtu0k+rSdmvnoFG37y1/TRhvgQj9gD5gi1aII9uHJz4\nGXruAJ0XQwdpmaQkq7ovqIdA2GQtupGNstrZlqkgpx3300Kqz/w8+lIgB1rc\nSbdPy620irpftJHmM3UOTiO19wQ4y86bZ8gpsgVNh0oGrvlE/FtuIYTZWX29\nYUWRZjdhp0CnqqT43dn9Pu8tx+APROGQT+2tFORQorr+hbreImX3HqrapfNl\nYBqMGDguCBHthGoFf0qD0oUqfH15SRPQjBnt+1P++xQ0p+Oz5i1vtYvLZ9ge\nwUUy\r\n=006i\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for responselike", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/responselike_1.0.0_1548874424163_0.15382502147942811", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "38ee8db1511cdb4a9133ff67b8bc16901de733aa4dc1efffdb5064b7daaa3f21"}, "3.0.0": {"name": "@types/responselike", "version": "3.0.0", "license": "MIT", "_id": "@types/responselike@3.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "5ecc1fc88552e5ac03de648a7796f9e125d002dc", "tarball": "https://registry.npmjs.org/@types/responselike/-/responselike-3.0.0.tgz", "fileCount": 4, "integrity": "sha512-zfgGLWx5IQOTJgQPD4UfGEhapTKUPC1ra/QCG02y3GUJWrhX05bBf/EfTh3aFj2DKi7cLo+cipXLNclD27tQXQ==", "signatures": [{"sig": "MEUCIQC7t7BuUEMXDhYv8abFqPoKdHBkbWkxsKsrSupnV4QiIQIgEU53OKJ46IBbO0KVbPcuuzN+Vca38/vU5WyE96DX62w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1776, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFIXQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpckxAAiYCBiCtCg67zm7FnMJgW/Nb54/Dg9xWW2rXoMZeDlNlKHRAb\r\nN4S7elsRPy+GK+NnSIqMgmXW3I+7Tia/BOjKI83xdhKnigLiMswfLjpZsa04\r\nvZEyLjhqd/P1yemWBaYIpT2VhvQC9jBPNDqVkqRtAX18pgTDx7gUhQ8mCbal\r\nJIrsDUdBiT9epcs1zTnuWko5/eSKgiMx4o0WC4zwwJSMdP7+K2cnwhMIO5wy\r\nhXAEMxOp8xBWdXBUh9Tz5EoAxZOKqO2MRVSnapPJ+cdWzxpX6Tg6ltMFsVRq\r\n6QsnpDXEzE2LmajPfP7oE88ibcERvwT7SCfUbnoCzAv0KiWH2ZvNLRcgLk3Y\r\nsN3zIkovG1aHc+ZZNUfooT6a6CDOxGn1Ni76AiUMqdw1tXYTUzRN00mTgit3\r\nBbpUs31nWi/UAAc3FdW/MEEsCNb5yOk0J6wbT1krBvn0ek9VHDm0rD9vCPUv\r\nTQh4AWMlGwpGxy/X58S3hPGV9BRML059ikSsbpRGVButYd6tUksTJ7qUut6e\r\nu5ReyqC4UXzI8K8yo3fVsCSojDgYCuw+736zMz45cSn6yvj07VgcEy1bIQrF\r\nUePtGJhuJ/la1gExelKDlk1UrtD1cfMQsMhSXKhz2RNl4pkVMmjGl9HHRPjf\r\nCX0L/p1hY/IMhYAuXy9M6+Ukhi6DL9FDvXs=\r\n=5Ax0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "deprecated": "This is a stub types definition. responselike provides its own type definitions, so you do not need this installed.", "description": "Stub TypeScript definitions entry for responselike, which provides its own types definitions", "directories": {}, "dependencies": {"responselike": "*"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/responselike_3.0.0_1662289360049_0.4448736802876745", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "@types/responselike", "version": "1.0.1", "license": "MIT", "_id": "@types/responselike@1.0.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/responselike", "dist": {"shasum": "1dd57e54509b3b95c7958e52709567077019d65d", "tarball": "https://registry.npmjs.org/@types/responselike/-/responselike-1.0.1.tgz", "fileCount": 5, "integrity": "sha512-TiGnitEDxj2X0j+98Eqk5lv/Cij8oHd32bU4D/Yw6AOq7vvTk0gSD2GPj0G/HkvhMoVsdlhYF4yqqlyPBTM6Sg==", "signatures": [{"sig": "MEQCID6M+VJm5sPVw2hVNYH4gnB39sb5s/escr0ytAapn1lkAiBVurdqT3ZMtsUMs6TmkDuoWqNQWsjSZsozADOm71ivFg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5106}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/responselike"}, "description": "TypeScript definitions for responselike", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/responselike_1.0.1_1695806320143_0.9107615305630679", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "f455a4d977ac656d0981fba1b87415666d812e96862b4cc39bbf86790744b4f5"}, "1.0.2": {"name": "@types/responselike", "version": "1.0.2", "license": "MIT", "_id": "@types/responselike@1.0.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/responselike", "dist": {"shasum": "8de1b0477fd7c12df77e50832fa51701a8414bd6", "tarball": "https://registry.npmjs.org/@types/responselike/-/responselike-1.0.2.tgz", "fileCount": 5, "integrity": "sha512-/4YQT5Kp6HxUDb4yhRkm0bJ7TbjvTddqX7PZ5hz6qV3pxSo72f/6YPRo+Mu2DU307tm9IioO69l7uAwn5XNcFA==", "signatures": [{"sig": "MEQCIHhbPeGXUMoZ1F6WoBFthdX2a2EVIDp+CiqqgLMf2ObAAiAcfS33+vHY6KBW7lap64NqihE83rFtCg38X67Yd8K/oQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4604}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/responselike"}, "description": "TypeScript definitions for responselike", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/responselike_1.0.2_1697637070071_0.5720069899977713", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "61426d6c2f235a8229a94f44232f46dd304b0ef6d828822e37c281453c4a54fb"}, "1.0.3": {"name": "@types/responselike", "version": "1.0.3", "license": "MIT", "_id": "@types/responselike@1.0.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/responselike", "dist": {"shasum": "cc29706f0a397cfe6df89debfe4bf5cea159db50", "tarball": "https://registry.npmjs.org/@types/responselike/-/responselike-1.0.3.tgz", "fileCount": 5, "integrity": "sha512-H/+L+UkTV33uf49PH5pCAUBVPNj2nDBXTN+qS1dOwyyg24l3CcicicCA7ca+HMvJBZcFgl5r8e+RR6elsb4Lyw==", "signatures": [{"sig": "MEUCIEurCBU5l03Shg46kiO19Z6q8spPHzrjuinZE34BCk5XAiEAvPmS+ZCRKwKpG5qx3i+A04uzT2hBKwRN4myi21rVSQk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4604}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/responselike"}, "description": "TypeScript definitions for responselike", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/responselike_1.0.3_1699371027593_0.4692311165447023", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ea1d581578b0ef6027b7cd6aa25990bb9ee8723d002d0617acf0aa4d3324aa49"}}, "time": {"created": "2019-01-30T18:53:43.978Z", "modified": "2024-11-11T09:44:03.530Z", "1.0.0": "2019-01-30T18:53:44.305Z", "3.0.0": "2022-09-04T11:02:40.236Z", "1.0.1": "2023-09-27T09:18:40.336Z", "1.0.2": "2023-10-18T13:51:10.222Z", "1.0.3": "2023-11-07T15:30:27.822Z"}, "license": "MIT", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/responselike", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/responselike"}, "description": "TypeScript definitions for responselike", "contributors": [{"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "maintainers": [{"name": "types", "email": "<EMAIL>"}], "readme": "[object Object]", "readmeFilename": ""}