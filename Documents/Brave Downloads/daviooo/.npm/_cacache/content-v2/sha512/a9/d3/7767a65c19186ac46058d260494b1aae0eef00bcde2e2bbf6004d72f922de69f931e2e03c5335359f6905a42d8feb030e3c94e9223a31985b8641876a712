{"_id": "which-boxed-primitive", "_rev": "9-********************************", "name": "which-boxed-primitive", "dist-tags": {"latest": "1.1.1"}, "versions": {"1.0.0": {"name": "which-boxed-primitive", "version": "1.0.0", "keywords": ["boxed", "primitive", "object", "ecmascript", "javascript", "which"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "which-boxed-primitive@1.0.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/which-boxed-primitive#readme", "bugs": {"url": "https://github.com/ljharb/which-boxed-primitive/issues"}, "dist": {"shasum": "349fe7d6f1a00522fb4594054aba03aebf110545", "tarball": "https://registry.npmjs.org/which-boxed-primitive/-/which-boxed-primitive-1.0.0.tgz", "fileCount": 10, "integrity": "sha512-+JYt0r8cchF4UgWKdJhDeSu1onPoPCh46TDYuT+TlQY5eunXm2ICXOejc4y4ZPcJGP/eclPvgTelskDE68AtIQ==", "signatures": [{"sig": "MEYCIQCq+E4YP7BpQhNbKL8C+/7X5cw+kv/cmsSNC3dX/MJyIwIhAM4tFxuXVJtqFZTuLVMun4V9VTcMhcT5/gN7hWZxj5mT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdT0UnCRA9TVsSAnZWagAAxMcP/1hqPP7ccLgytBCt6xJU\nsSiBfUAdkOCX/Fxrp6E0XjNPiO05/jq5CYJOyeiQd2lPeXIS65hAxQDXnhDd\nfbj+aOOopuGW0tgjoSbgow0ZeRQ2ktmBMZKTGMPDx4ZhoiIM0b/vOgF/+IMy\noidjDTndTg3QdQE8lsObeyCxBzBqpu2uDaVUIDjR6scVFw5399vp/Yi3jH6/\n9UYKgyFEGPkV1lmxZx3WAFTu1vZe1FbqV4JxLBYiib8dw7aTMlNealMwjok8\n169lZbChqEpDmytir/I9WEOKCnXQIF35hNxZIniShy4gvuzPHiUGZaKHjxnX\nxRzFpKAF4WdKvbIBK3bv7VzCNEacKoooRYshPuKD1JCRHF2AkLCKOh5ptW5b\n40I2FbvwnS91XrzPG/OcIkoIQvF1gcxW6FwXe5dDy0X6YOAmbLtKWRIm3n8O\nOvJxboq+tRbB+bWJ3YgJZuJ70bw1KEIvS2nrbMUP+LbSFZBqvPV6Q8zZ0mAB\nn4n06eYoXpULHqTgs50f7EdOEANt7w1CaeLYFhOwj7r7c96HunNeekYV9/7q\nOSEqnoF2KpLsOfiRj4X7NfPKg875kUSaySuZTxxJ6cz7TGTSb1pRZfhlDKrr\nqA+1ihhZHH8QikU5MFK8YMATUnslN1fexD2vg1e8ULLqmNVnfz+HglKsI1cj\nYh1e\r\n=mLvO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "2df75e08d068449d1a27304f234f0f72f20ff208", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "pretest": "npm run lint", "posttest": "npx aud", "prepublish": "safe-publish-latest", "preversion": "auto-changelog", "tests-only": "node test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/which-boxed-primitive.git", "type": "git"}, "_npmVersion": "6.10.2", "description": "Which kind of boxed JS primitive is this?", "directories": {}, "_nodeVersion": "12.8.0", "dependencies": {"is-bigint": "^1.0.0", "is-string": "^1.0.4", "is-symbol": "^1.0.2", "is-number-object": "^1.0.3", "is-boolean-object": "^1.0.0"}, "_hasShrinkwrap": false, "auto-changelog": {"template": "<PERSON><PERSON><PERSON><PERSON>", "commitLimit": false, "backfillLimit": false}, "devDependencies": {"tape": "^4.11.0", "eslint": "^6.1.0", "has-symbols": "^1.0.0", "auto-changelog": "^1.14.1", "object-inspect": "^1.6.0", "safe-publish-latest": "^1.1.2", "@ljharb/eslint-config": "^14.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/which-boxed-primitive_1.0.0_1565476134905_0.12349053249965203", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "which-boxed-primitive", "version": "1.0.1", "keywords": ["boxed", "primitive", "object", "ecmascript", "javascript", "which"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "which-boxed-primitive@1.0.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/which-boxed-primitive#readme", "bugs": {"url": "https://github.com/ljharb/which-boxed-primitive/issues"}, "dist": {"shasum": "cbe8f838ebe91ba2471bb69e9edbda67ab5a5ec1", "tarball": "https://registry.npmjs.org/which-boxed-primitive/-/which-boxed-primitive-1.0.1.tgz", "fileCount": 10, "integrity": "sha512-7BT4TwISdDGBgaemWU0N0OU7FeAEJ9Oo2P1PHRm/FCWoEi2VLWC9b6xvxAA3C/NMpxg3HXVgi0sMmGbNUbNepQ==", "signatures": [{"sig": "MEQCIGkJlGU2VR4aNdp44IqHg07euwbTcUOonlhp9gax92DFAiBTDMrfNI1uBl6qD+68yG1kafiUIpXCdjqkLEvBwTqIBg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20081, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdT3l0CRA9TVsSAnZWagAA0l8P+wb0JDCRE9tHEptvt6y0\n/xXQZ7w9GC+dDGSJEa8erYiiEfS2Kh0+iJkCeJNPEV3+Lt+65J1h+qDO3If9\nn+dM579IPv8pAJO0d/wmS1CJs/EXhBa8LFw8NIMbDgfdV7XWYteIn4jpRia/\nY0A/RPXcU0ATJde1tNk9xaM2ORnIDUFbUGN7IOICFVYN4kxmyYgoHEQFs82P\nHpbfOfjEeZCkQvYblLWbR3NL19reweL3T6jHcHkcolYUnw/DIyvOScUka6BI\nzNAdlty279P9KtWZW+BSAc5nCtFOn3Q1CfFjHQPOLSQo4O5fOREFFTot2cFw\nNmGHa19lOfboCLZxJAdgv5Zp4jmipV+M3bjvYZgwP6DyTWagLFNg0w9Ms6WT\n2Okso2pFi00GCk85ggfREuR7q0W4GksfwJwxLIzok0X+PeuVdHQtB6Swg7ja\ntnDd2VMnm7X5peg/aDf9C0LQwlyWWIBVqnDVVrrP8eycxSsPk7Atvpf8tmUb\nQ+ksxDnz0aTSS2pVKBFLc/Or5+A9AYu/E7dE+74XTZSFkjA15m+freDKjIv2\nh/57BMh9OyL6Nqn1OPMFD8SZSgVWvOJGNhTDkpzDFgRIv3dkOcmkh/WKcY7k\n7Ve3ydTMeUCj0tX3f22qe19UqRc7cLMlK6e0t/TSsCP4pwnRYpBaEezV9/kf\nsUEt\r\n=5qSJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "2415eca06d0399e6aac2ee3bfa0924b2482eb27e", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "pretest": "npm run lint", "posttest": "npx aud", "prepublish": "not-in-publish || safe-publish-latest", "preversion": "auto-changelog", "tests-only": "node test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/which-boxed-primitive.git", "type": "git"}, "_npmVersion": "6.10.2", "description": "Which kind of boxed JS primitive is this?", "directories": {}, "_nodeVersion": "12.8.0", "dependencies": {"is-bigint": "^1.0.0", "is-string": "^1.0.4", "is-symbol": "^1.0.2", "is-number-object": "^1.0.3", "is-boolean-object": "^1.0.0"}, "_hasShrinkwrap": false, "auto-changelog": {"template": "<PERSON><PERSON><PERSON><PERSON>", "commitLimit": false, "backfillLimit": false}, "devDependencies": {"tape": "^4.11.0", "eslint": "^6.1.0", "in-publish": "^2.0.0", "has-symbols": "^1.0.0", "auto-changelog": "^1.14.1", "object-inspect": "^1.6.0", "safe-publish-latest": "^1.1.2", "@ljharb/eslint-config": "^14.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/which-boxed-primitive_1.0.1_1565489523536_0.07728945349327221", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "which-boxed-primitive", "version": "1.0.2", "keywords": ["boxed", "primitive", "object", "ecmascript", "javascript", "which"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "which-boxed-primitive@1.0.2", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/which-boxed-primitive#readme", "bugs": {"url": "https://github.com/inspect-js/which-boxed-primitive/issues"}, "dist": {"shasum": "13757bc89b209b049fe5d86430e21cf40a89a8e6", "tarball": "https://registry.npmjs.org/which-boxed-primitive/-/which-boxed-primitive-1.0.2.tgz", "fileCount": 11, "integrity": "sha512-bwZdv0AKLpplFY2KZRX6TvyuN7ojjr7lwkg6ml0roIy9YeuSr7JS372qlNW18UQYzgYK9ziGcerWqZOmEn9VNg==", "signatures": [{"sig": "MEUCIF1QLYhzxyoMRToFNdaQBZ345eG7FERlYVP7ErofnwslAiEAywvmq9IaFOSES0Nxek1WpBDmiRTU5VBn/ZbY+2obYAM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15028, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf1+kBCRA9TVsSAnZWagAAU/sQAIMpUTPZJMQfWRHo+A8p\n2K2LRXeB0qWSV6ccNtt+B8ngLoyqJ9mVgK2laGIM3xd6ajxwvE5SBY/trsUn\n9kLz7mrN5YRa/OPOTCK+GVupFZLvHJGelemPNWMZCh8OKVLrwbqzBSKTDPKQ\nHC8A8kQLYu+EbiJzrq3nOMdmnBShpY0uE6yzTmkSWNBLyUiCva3w8lirOk11\nTZoNBgLM6mtxy5xkMTHKigVR4UXiuefyKbUNaE3hYpvOqUStw7gK27fnTru9\n8iUwotv/6XGKCXkw/vZ0eGMlJygJkOWVAAMP7m1d8HkobzezEGpKRkVRsIkf\nxIdQYnQVKuTC0ASQhJLonBgBdLW+J55v5I/SJVrdVZxHIF3JEcFwYbh84b2F\n8Fnaw2FPz0ZqZc9259MMl6yJ7/nEE5ufCJRqTHNGK0z4pUIYpyWBE8pG9eEH\nC7uv6bQbVeEMqz2/fUoFBxKQ9K3zXN2b1pFyQG5C4ABfhnrH/QD0Yvnl+VR7\nG/g4P67HrPLZ1twzhuPU5bIV9/okO4yGeMvhJaMZCUniPgsJqzpBlQk0e6ch\nqDGCuJKfQvH6En1rlfTrW78IqHSWnWJLq+H8Z5sK3o5roDHlffIJ4CfmJbnS\n8klUq8goaXHuXFqI7kMwMkMIhzGgNxmXe546LowCVm0t5I527gUW4FqLmssJ\nIDDo\r\n=saW+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "e2572398ae2be0d87527bc1726c1393a982a2f37", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || safe-publish-latest", "preversion": "auto-changelog", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspect-js/which-boxed-primitive.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Which kind of boxed JS primitive is this?", "directories": {}, "_nodeVersion": "14.15.1", "dependencies": {"is-bigint": "^1.0.1", "is-string": "^1.0.5", "is-symbol": "^1.0.3", "is-number-object": "^1.0.4", "is-boolean-object": "^1.1.0"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^1.1.3", "nyc": "^10.3.2", "tape": "^5.0.1", "eslint": "^7.15.0", "in-publish": "^2.0.1", "has-symbols": "^1.0.1", "auto-changelog": "^2.2.1", "object-inspect": "^1.9.0", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/which-boxed-primitive_1.0.2_1607985408730_0.3073854540194627", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "which-boxed-primitive", "version": "1.1.0", "keywords": ["boxed", "primitive", "object", "ecmascript", "javascript", "which"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "which-boxed-primitive@1.1.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/which-boxed-primitive#readme", "bugs": {"url": "https://github.com/inspect-js/which-boxed-primitive/issues"}, "dist": {"shasum": "2d850d6c4ac37b95441a67890e19f3fda8b6c6d9", "tarball": "https://registry.npmjs.org/which-boxed-primitive/-/which-boxed-primitive-1.1.0.tgz", "fileCount": 12, "integrity": "sha512-Ei7Miu/AXe2JJ4iNF5j/UphAgRoma4trE6PtisM09bPygb3egMH3YLW/befsWb1A1AxvNSFidOFTB18XtnIIng==", "signatures": [{"sig": "MEUCIBbg2Nj9FqMUzyST37gV0yOksbZQGwkO0OU2FAtiVhg6AiEAzE7PeFnTHBMEeXFAOsUOcVru4jFA5cmof37mcA7C0Sg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18774}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "7310c5f86e59ff4a7b94ed3b53815a98785f178d", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "tsc -p . && attw -P", "posttest": "npx npm@'>=10.2' audit --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspect-js/which-boxed-primitive.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Which kind of boxed JS primitive is this?", "directories": {}, "_nodeVersion": "23.3.0", "dependencies": {"is-bigint": "^1.1.0", "is-string": "^1.1.0", "is-symbol": "^1.1.0", "is-number-object": "^1.1.0", "is-boolean-object": "^1.2.0"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"nyc": "^10.3.2", "tape": "^5.9.0", "eslint": "=8.8.0", "encoding": "^0.1.13", "for-each": "^0.3.3", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "next", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "object-inspect": "^1.13.3", "@types/for-each": "^0.3.3", "@ljharb/tsconfig": "^0.2.0", "es-value-fixtures": "^1.5.0", "safe-publish-latest": "^2.0.0", "@arethetypeswrong/cli": "^0.17.0", "@ljharb/eslint-config": "^21.1.1", "@types/object-inspect": "^1.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/which-boxed-primitive_1.1.0_1733171088990_0.316680455359889", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "which-boxed-primitive", "version": "1.1.1", "description": "Which kind of boxed JS primitive is this?", "main": "index.js", "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && attw -P", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "npx npm@'>=10.2' audit --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/which-boxed-primitive.git"}, "keywords": ["boxed", "primitive", "object", "ecmascript", "javascript", "which"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/which-boxed-primitive/issues"}, "homepage": "https://github.com/inspect-js/which-boxed-primitive#readme", "dependencies": {"is-bigint": "^1.1.0", "is-boolean-object": "^1.2.1", "is-number-object": "^1.1.1", "is-string": "^1.1.1", "is-symbol": "^1.1.1"}, "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/for-each": "^0.3.3", "@types/object-inspect": "^1.13.0", "@types/tape": "^5.7.0", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "es-value-fixtures": "^1.5.0", "eslint": "=8.8.0", "for-each": "^0.3.3", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.3", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "engines": {"node": ">= 0.4"}, "_id": "which-boxed-primitive@1.1.1", "gitHead": "3cb1edfed48c1a749cb497fa05b8b3971f798613", "types": "./index.d.ts", "_nodeVersion": "23.4.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-TbX3mj8n0odCBFVlY8AxkqcHASw3L60jIuF8jFP78az3C2YhmGvqbHBpAjTRH2/xqYunrJ9g1jSyjCjpoWzIAA==", "shasum": "d76ec27df7fa165f18d5808374a5fe23c29b176e", "tarball": "https://registry.npmjs.org/which-boxed-primitive/-/which-boxed-primitive-1.1.1.tgz", "fileCount": 12, "unpackedSize": 19278, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCD8z8vxzr6NXh/p7RcLFsbzSZJjdErMJEiMjqAI+8E4gIgfmIIkGdObc5GxTDB+TUlfzQa0FYxOkd+8fVd0/7Mjzw="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/which-boxed-primitive_1.1.1_1734328123835_0.07296879180699811"}, "_hasShrinkwrap": false}}, "time": {"created": "2019-08-10T22:28:54.905Z", "modified": "2024-12-16T05:48:44.232Z", "1.0.0": "2019-08-10T22:28:55.043Z", "1.0.1": "2019-08-11T02:12:03.677Z", "1.0.2": "2020-12-14T22:36:48.931Z", "1.1.0": "2024-12-02T20:24:49.156Z", "1.1.1": "2024-12-16T05:48:44.026Z"}, "bugs": {"url": "https://github.com/inspect-js/which-boxed-primitive/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/inspect-js/which-boxed-primitive#readme", "keywords": ["boxed", "primitive", "object", "ecmascript", "javascript", "which"], "repository": {"type": "git", "url": "git+https://github.com/inspect-js/which-boxed-primitive.git"}, "description": "Which kind of boxed JS primitive is this?", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "readme": "# which-boxed-primitive <sup>[![Version Badge][2]][1]</sup>\n\n[![dependency status][5]][6]\n[![dev dependency status][7]][8]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][11]][1]\n\nWhich kind of boxed JS primitive is this? This module works cross-realm/iframe, does not depend on `instanceof` or mutable properties, and works despite ES6 Symbol.toStringTag.\n\n## Example\n\n```js\nvar whichBoxedPrimitive = require('which-boxed-primitive');\nvar assert = require('assert');\n\n// unboxed primitives return `null`\n// boxed primitives return the builtin constructor name\n\nassert.equal(whichBoxedPrimitive(undefined), null);\nassert.equal(whichBoxedPrimitive(null), null);\n\nassert.equal(whichBoxedPrimitive(false), null);\nassert.equal(whichBoxedPrimitive(true), null);\nassert.equal(whichBoxedPrimitive(new Boolean(false)), 'Boolean');\nassert.equal(whichBoxedPrimitive(new Boolean(true)), 'Boolean');\n\nassert.equal(whichBoxedPrimitive(42), null);\nassert.equal(whichBoxedPrimitive(NaN), null);\nassert.equal(whichBoxedPrimitive(Infinity), null);\nassert.equal(whichBoxedPrimitive(new Number(42)), 'Number');\nassert.equal(whichBoxedPrimitive(new Number(NaN)), 'Number');\nassert.equal(whichBoxedPrimitive(new Number(Infinity)), 'Number');\n\nassert.equal(whichBoxedPrimitive(''), null);\nassert.equal(whichBoxedPrimitive('foo'), null);\nassert.equal(whichBoxedPrimitive(new String('')), 'String');\nassert.equal(whichBoxedPrimitive(new String('foo')), 'String');\n\nassert.equal(whichBoxedPrimitive(Symbol()), null);\nassert.equal(whichBoxedPrimitive(Object(Symbol()), 'Symbol');\n\nassert.equal(whichBoxedPrimitive(42n), null);\nassert.equal(whichBoxedPrimitive(Object(42n), 'BigInt');\n\n// non-boxed-primitive objects return `undefined`\nassert.equal(whichBoxedPrimitive([]), undefined);\nassert.equal(whichBoxedPrimitive({}), undefined);\nassert.equal(whichBoxedPrimitive(/a/g), undefined);\nassert.equal(whichBoxedPrimitive(new RegExp('a', 'g')), undefined);\nassert.equal(whichBoxedPrimitive(new Date()), undefined);\nassert.equal(whichBoxedPrimitive(function () {}), undefined);\nassert.equal(whichBoxedPrimitive(function* () {}), undefined);\nassert.equal(whichBoxedPrimitive(x => x * x), undefined);\nassert.equal(whichBoxedPrimitive([]), undefined);\n\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n[1]: https://npmjs.org/package/which-boxed-primitive\n[2]: https://versionbadg.es/inspect-js/which-boxed-primitive.svg\n[5]: https://david-dm.org/inspect-js/which-boxed-primitive.svg\n[6]: https://david-dm.org/inspect-js/which-boxed-primitive\n[7]: https://david-dm.org/inspect-js/which-boxed-primitive/dev-status.svg\n[8]: https://david-dm.org/inspect-js/which-boxed-primitive#info=devDependencies\n[11]: https://nodei.co/npm/which-boxed-primitive.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/which-boxed-primitive.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/which-boxed-primitive.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=which-boxed-primitive\n", "readmeFilename": "README.md"}