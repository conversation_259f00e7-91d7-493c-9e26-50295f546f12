{"_id": "xdg-basedir", "_rev": "21-39008887b7f3a85a0dff628757144963", "name": "xdg-basedir", "description": "Get XDG Base Directory paths", "dist-tags": {"latest": "5.1.0"}, "versions": {"1.0.0": {"name": "xdg-basedir", "version": "1.0.0", "description": "Get XDG Base Directory paths", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/xdg-basedir"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js"}, "files": ["index.js"], "keywords": ["xdg", "base", "directory", "dir", "basedir", "path", "data", "config", "cache"], "dependencies": {"user-home": "^1.0.0"}, "devDependencies": {"ava": "0.0.4", "require-uncached": "^1.0.2"}, "gitHead": "8116a2fd05718de88b740861764739dc1879916f", "bugs": {"url": "https://github.com/sindresorhus/xdg-basedir/issues"}, "homepage": "https://github.com/sindresorhus/xdg-basedir", "_id": "xdg-basedir@1.0.0", "_shasum": "95ea42a738d98a9470fd5a21d08556dbd61dc74a", "_from": ".", "_npmVersion": "2.1.2", "_nodeVersion": "0.10.32", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "95ea42a738d98a9470fd5a21d08556dbd61dc74a", "tarball": "https://registry.npmjs.org/xdg-basedir/-/xdg-basedir-1.0.0.tgz", "integrity": "sha512-ks+cmmnR41KkU9c/MWLMEdgk4TLx0pi5lo370FsDMX12su1nQt3Xt901przP6ZSI8Lk7Hkz7XdBlbV4MLPONbA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCFM1OnAHwT+PEgaxqCrdJvCwbaok/bsBHK9sD8NDgO/gIhAOUvP6fG49wfGEQ5Uf2fAK2aWQ41c09Veg44T488l5RN"}]}, "directories": {}}, "1.0.1": {"name": "xdg-basedir", "version": "1.0.1", "description": "Get XDG Base Directory paths", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/xdg-basedir"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js"}, "files": ["index.js"], "keywords": ["xdg", "base", "directory", "dir", "basedir", "path", "data", "config", "cache"], "dependencies": {"user-home": "^1.0.0"}, "devDependencies": {"ava": "0.0.4", "require-uncached": "^1.0.2"}, "gitHead": "2eaf176e5d0c9c1bd64e6fc8a60a3a8e3dba4e79", "bugs": {"url": "https://github.com/sindresorhus/xdg-basedir/issues"}, "homepage": "https://github.com/sindresorhus/xdg-basedir", "_id": "xdg-basedir@1.0.1", "_shasum": "14ff8f63a4fdbcb05d5b6eea22b36f3033b9f04e", "_from": ".", "_npmVersion": "2.1.16", "_nodeVersion": "0.10.32", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "14ff8f63a4fdbcb05d5b6eea22b36f3033b9f04e", "tarball": "https://registry.npmjs.org/xdg-basedir/-/xdg-basedir-1.0.1.tgz", "integrity": "sha512-ugGW++yvGoxr4IrSoxsieH2b/NlZbXsBaL85Off3z487yS9eiiRjrfdkBw1iBvzv/SK0XjjYy+KBix5PIseOtQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDBB2UFeOOjctj9/RH/VwXAuptIeplW23patg14f5JXOAIgENArnc2d3B5suHO9Kx5UsD/lJok8azEU9ABEuhZc0aA="}]}, "directories": {}}, "2.0.0": {"name": "xdg-basedir", "version": "2.0.0", "description": "Get XDG Base Directory paths", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/xdg-basedir"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js"}, "files": ["index.js"], "keywords": ["xdg", "base", "directory", "dir", "basedir", "path", "data", "config", "cache", "linux", "unix", "spec"], "dependencies": {"os-homedir": "^1.0.0"}, "devDependencies": {"ava": "0.0.4", "require-uncached": "^1.0.2"}, "gitHead": "0ad9e461ca531fcc1532c66932df386739aeffb2", "bugs": {"url": "https://github.com/sindresorhus/xdg-basedir/issues"}, "homepage": "https://github.com/sindresorhus/xdg-basedir", "_id": "xdg-basedir@2.0.0", "_shasum": "edbc903cc385fc04523d966a335504b5504d1bd2", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "edbc903cc385fc04523d966a335504b5504d1bd2", "tarball": "https://registry.npmjs.org/xdg-basedir/-/xdg-basedir-2.0.0.tgz", "integrity": "sha512-NF1pPn594TaRSUO/HARoB4jK8I+rWgcpVlpQCK6/6o5PHyLUt2CSiDrpUZbQ6rROck+W2EwF8mBJcTs+W98J9w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC4fgI47ypl9/LwYVnuV+yWxQWCWglF9+/Vu2SdsRyMgAIhANJJgKu7+JEkNzvpbEANq5MRPL0xgXxj1deb8cwTLvY9"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "3.0.0": {"name": "xdg-basedir", "version": "3.0.0", "description": "Get XDG Base Directory paths", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/xdg-basedir.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["xdg", "base", "directory", "dir", "basedir", "path", "data", "config", "cache", "linux", "unix", "spec"], "devDependencies": {"ava": "*", "require-uncached": "^1.0.2", "xo": "*"}, "gitHead": "d441833e9dad9694b04495905ab8fe484e3b8b99", "bugs": {"url": "https://github.com/sindresorhus/xdg-basedir/issues"}, "homepage": "https://github.com/sindresorhus/xdg-basedir#readme", "_id": "xdg-basedir@3.0.0", "_shasum": "496b2cc109eca8dbacfe2dc72b603c17c5870ad4", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.6.2", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "496b2cc109eca8dbacfe2dc72b603c17c5870ad4", "tarball": "https://registry.npmjs.org/xdg-basedir/-/xdg-basedir-3.0.0.tgz", "integrity": "sha512-1Dly4xqlulvPD3fZUQJLY+FUIeqN3N2MM3uqe4rCJftAvOjFa3jFGfctOgluGx4ahPbUCsZkmJILiP0Vi4T6lQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE4rnC9OR8Q7nec0NGUuFXGS8JgtPps2AASTZaLnH354AiEA5oyDGBmMw4Axj/FRZXt3S5h0TOA1mdMNh/OHf6NNTrQ="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/xdg-basedir-3.0.0.tgz_1486978344594_0.5195651547983289"}, "directories": {}}, "4.0.0": {"name": "xdg-basedir", "version": "4.0.0", "description": "Get XDG Base Directory paths", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/xdg-basedir.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["xdg", "base", "directory", "basedir", "path", "data", "config", "cache", "linux", "unix", "spec"], "devDependencies": {"ava": "^1.4.1", "import-fresh": "^3.0.0", "tsd": "^0.7.2", "xo": "^0.24.0"}, "gitHead": "bc65ded91e797cbdedbe3e5e1a2882372e3ba983", "bugs": {"url": "https://github.com/sindresorhus/xdg-basedir/issues"}, "homepage": "https://github.com/sindresorhus/xdg-basedir#readme", "_id": "xdg-basedir@4.0.0", "_nodeVersion": "10.15.3", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-PSNhEJDejZYV7h50BohL09Er9VaIefr2LMAf3OEmpCkjOi34eYyQYAXUTjEQtZJTKcF0E2UKTh+osDLsgNim9Q==", "shasum": "4bc8d9984403696225ef83a1573cbbcb4e79db13", "tarball": "https://registry.npmjs.org/xdg-basedir/-/xdg-basedir-4.0.0.tgz", "fileCount": 5, "unpackedSize": 5693, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcx8qMCRA9TVsSAnZWagAAXYcQAJBYvVO5Ga+5eLBnXIoN\n9S7SxuKKQlMPGsyAzVVwNiKCqN4rb0EZ8MRiVvZbqdccaqTP/Zk0X3SxXyCZ\nRxNpYAA+uIDNkcf0NbzF49svPw6493dJp7qdCPcVzfzSegjLBaFKadglDEg9\nL7d1zQ+muR7/FWstWr8nUhezpOKn9Di6Rw5ngmUDQ/uqWppqk6vqpcz2yPKW\nVmUvRyYsUIoyTF9Xb4YOlhtmI/VI8n7iT4/0/1Uy1+P81+JN8ROJP1bF2Pca\npKuuQ7XoZpd5AJSWeJwpAjLHzHkc73YjQ+i7BN+wkQXBoI3zkMXWY4OgKV/i\nm7cp+hF3/+1AHSd7gQ+2w8wYrjpg+s7a3PQxGE+Yz6V0lO/jZ7w+7qj5m5Aa\nDTmWig9SbCoMjW7/0JLQfEAtOvUw1qidrj59/rJlQRJ+16/kzpC8qz4k+0J8\nNQR8CjYi/E+WyllqrIIDp7W3aOrlq5YEjzpEGAWuLaZr2QQsMCQm4pTIC+v1\nImgr+GFPoZxpl8JuU9iWbp2fv16ESMhRgUsb5iR6TN1KVittjLHrn36cban1\nm0R/5colnO/1Oh3bFtoekXJ1AeDpNUCIHM/3k4qPm+j5wT2DfMXieykN846x\nBT2Stjbf64PkuCcrT10fHnQd8Sv7g0RL/EP7HBOFBHJbFqMxRAwTP/7Tw761\nUAZJ\r\n=zFLM\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDgORhdOC2OKHuXd/16wLVGjuVlc8RCnNFODlgmJqiEZAiAVdG/Bt7QFQFgR3Qll4jpGQWOglCe8RCkdEqIuwy5qPQ=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xdg-basedir_4.0.0_1556597386863_0.809265814738712"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "xdg-basedir", "version": "5.0.0", "description": "Get XDG Base Directory paths", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/xdg-basedir.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["xdg", "base", "directory", "basedir", "path", "data", "config", "cache", "linux", "unix", "spec"], "devDependencies": {"ava": "^1.4.1", "import-fresh": "^3.0.0", "tsd": "^0.7.2", "xo": "^0.24.0"}, "ava": {"serial": true}, "gitHead": "47109e5e93484fe9e07c4ed19a9458baf8b9c845", "bugs": {"url": "https://github.com/sindresorhus/xdg-basedir/issues"}, "homepage": "https://github.com/sindresorhus/xdg-basedir#readme", "_id": "xdg-basedir@5.0.0", "_nodeVersion": "12.20.1", "_npmVersion": "6.14.10", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-LfppoQHWbQjyZvOYvWs1IlcdG9ZYXtxkTcXmIdLgHAFJzExQm4TmHsujT/jSL+dE0EOQ6LNesHyB+Tr7D/5byQ==", "shasum": "435b5b342501e23b8b876d5567acf650b187d935", "tarball": "https://registry.npmjs.org/xdg-basedir/-/xdg-basedir-5.0.0.tgz", "fileCount": 5, "unpackedSize": 6359, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgasDUCRA9TVsSAnZWagAAy34P/1guhc4TjVsZUqw+elfA\nSTctTJwpBlxgIg0QZ3NgYNlgbFuucpQcyUxWxyoXLi1JKfTIESZEUHZXGZjV\ndklbVecwvu/UpnGzmpUlMfpFT2VyOL4Ln8lYaZHPIuoFI44yi99HY79sRbQ4\n4Hzdxhd9sAEyNmqdrOZmtB8jNYEXIrmop0dX2jDiEGmSN8KbSz6uMi9p/cOe\neeYpmjAN0Jj3Z3Z0M4BvFqJ7+rMyj3sFCTE7wm2bDQguqlPHpUpd3Ud4fJjq\n4srcg3+WUafGkEbyt87IdRRjek3YhEst3NmWuLlrhPmCv1TEo6gHjhUOvGZD\n0Cri1PVO9sIC/65oBPPqtlOoQgFt4zJqCy1XfyowDDXdgZLq05zb77Umw24m\nw36OzoxmUqrl/tTCAHhur4+ucvBA7DtTqLdEr3+ctm+Fvi13/esoLpB90WFq\nRBFr41TQKOXYGoxsmn6cfFcgzXvOHvd0cm4KM0Tqpkn4YV2wqc+Xpg1oO5Fj\nbkhYI7dbmix8duRnVtleLT5s4Ugg5v8goo/H1XXoeU9lEQl3mj81aynmxKK/\n162t7CoOEpKckY5a84HOjg5Dmh0OrqEuxu2j7o/K0i/qmeUYa/YSYXJVp9tK\neVQFJAqZLII4wX7vpa0t3Siw+DZLnmq/OJhSAN8ZjUtyqeeQt9XBYa/uCsrv\niv9D\r\n=onXP\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCMkNEKHc1YIgrPRBDg9HVJV9uU7rZbj7PjEuCzTdJscwIgYtLk1SFFPa04Bat3cnGdvHPvkYP5/B6vXJji+W26Q94="}]}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xdg-basedir_5.0.0_1617608915772_0.6350504480753574"}, "_hasShrinkwrap": false}, "5.0.1": {"name": "xdg-basedir", "version": "5.0.1", "description": "Get XDG Base Directory paths", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/xdg-basedir.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["xdg", "base", "directory", "basedir", "path", "data", "config", "cache", "linux", "unix", "spec"], "devDependencies": {"ava": "^1.4.1", "import-fresh": "^3.0.0", "tsd": "^0.7.2", "xo": "^0.24.0"}, "ava": {"serial": true}, "gitHead": "3fc64a8f2476dd22b5a70a0f5978076e05c9dc49", "bugs": {"url": "https://github.com/sindresorhus/xdg-basedir/issues"}, "homepage": "https://github.com/sindresorhus/xdg-basedir#readme", "_id": "xdg-basedir@5.0.1", "_nodeVersion": "12.20.1", "_npmVersion": "6.14.10", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-YgJsKtT0Jerlp42eqk9illJhG2ScfLSYEvp7qzYDdVCjHN625BkiK+defqXyv5gYjtif7rOIYi65vKA0JC3qaQ==", "shasum": "b8900938336874b2efacaf8a2d5ad3d65fe0ca7d", "tarball": "https://registry.npmjs.org/xdg-basedir/-/xdg-basedir-5.0.1.tgz", "fileCount": 5, "unpackedSize": 6423, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgasqNCRA9TVsSAnZWagAAJzMP/jaFgByAvC/8fE4sTNw7\n3T7bLWAiygl2YnvN6r/fjR7eUNhPX1Axi0DhaZLTALWN/dliiJc/az9KwbJN\nhyJiX3GDhsQFJV3x89mXyjp7asoB8H+KE5B+DOlvFhD10z6O9atbMS4RQiRg\nIzFRAUlhEpwqwx8N+5X6ZjDJP/XF7NHvUnQ3inN0Y6KzVSvZP5uRlgwyMhfr\n5gq0QVHID+sg9enZx7Ia5fu+5MCiU06LgfA5O9l72w9q88b+Fu0XDFjAGNYh\n2aqiBwvFjS22LPZ6mptvMJ8gD+cpGVIxGAsRA2yf7SWyxeFETNJvLZEcUG5P\n+mFGnsUCkX4vBv1WEx/GlrkoJXwHlXGP7p+A2EEunC7nxwwqhMakcsamTBIT\nN+hHhZWxJZEsKc9SnKq8SVt0cVK4prntfQMHWXPjQTiPWg5coaoN+brJVtnC\ngvMxfRxLTB+oSZ+l6EbSp/HRb0I3bYa5kPts7XR8IOQs+ZL67FTkUeMMtn/s\nzBcRLdT10vP9Q/Zf5YcJPPz/rMYoDwpm0f3j9G7F/SXll9XoHeq9ZVa22hGR\nfKvvz62JILmmXaYxStxU4I0c6F5669DLMaRPliYgLoVD9tkh8Y1+j1VjuUY7\nTca9E8CtNcXRISAI2lZQspKsCwD0J2X74UJnYwvts0gvuLEOtyRJcMlWEPni\nzusF\r\n=uDNR\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDgH0jSu5BRdo5WBWVuk2nw9VVilJNC9QwKcrCIOhaWrgIhALcfz/yLql3X6aH1n0i6AUOc9Ed8MQPAQLfuoC8XXy/x"}]}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xdg-basedir_5.0.1_1617611404993_0.024172589241229447"}, "_hasShrinkwrap": false}, "5.1.0": {"name": "xdg-basedir", "version": "5.1.0", "description": "Get XDG Base Directory paths", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/xdg-basedir.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["xdg", "base", "directory", "basedir", "path", "data", "config", "cache", "linux", "unix", "spec"], "devDependencies": {"ava": "^1.4.1", "import-fresh": "^3.0.0", "tsd": "^0.7.2", "xo": "^0.24.0"}, "ava": {"serial": true}, "gitHead": "8cceade858e4da18cb971bf1844f086e9e213563", "bugs": {"url": "https://github.com/sindresorhus/xdg-basedir/issues"}, "homepage": "https://github.com/sindresorhus/xdg-basedir#readme", "_id": "xdg-basedir@5.1.0", "_nodeVersion": "12.22.1", "_npmVersion": "7.10.0", "dist": {"integrity": "sha512-GCPAHLvrIH13+c0SuacwvRYj2SxJXQ4kaVTT5xgL3kPrz56XxkF21IGhjSE1+W0aw7gpBWRGXLCPnPby6lSpmQ==", "shasum": "1efba19425e73be1bc6f2a6ceb52a3d2c884c0c9", "tarball": "https://registry.npmjs.org/xdg-basedir/-/xdg-basedir-5.1.0.tgz", "fileCount": 5, "unpackedSize": 6813, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhDCA8CRA9TVsSAnZWagAAky0QAIAb9deiPNxlvCwDuc00\ncEjnHyDqEzggtJVzTePWc8iLg12qeVWTlsrrRLJrIFx5p5J+eqkCl2Qa63/1\neUV6HfmWtmUphgZW0Yalk4vAOCT3NV771saKIdT8ztOtqCPsiq0K1OxCvPF3\nf7oasWNfiDzVgRZvUOFWXrFJEGBHc/FAlk9PakK0H/HcWzORzKo+hzmjiv0f\nAbwqU9hbpCJdB1j3Hp7Y7hXDzY7+kf6vr7X/g3YGup8g/NzuRrAKv7TUZbnA\nUNj5BVkpb9496xlh/Mtu0SmXVX8Iiyh48GBcfMhu+JMMbvX8gbWkVqSwXtgE\nOGzbLWN6s8t1yr0udQ/XU9pDL2bQMbrL5AOy7emD0b0AdwJIE0z5kIqLXTEW\nm7RSywbugxKXxwIC8xvPd1THXs+/uuEbeoB3CYfgcFSaHU76lrEq+CG1s826\ne/OwT/7Q/hbR8M3sTfTXxk5m/IZ9Wb93TMwU9o5aPDek+uuwpy74tbNN2SYi\noQLHG+xYA38PicH4e59TZLE1/7EBLkzyqXoVCjoIeWVxhhn16r3w9HVrp99J\nK2nZoX5eZfN0D8R/sGyTIPJp7BzhlAITbBUyMQxFBA5/Z1qTR7bx5ccLa0R5\nOY8fobVLa/jwHvxyaiq2i4+BS/20Cf+mNO64EO+Lg/pkoVXJJ3C3dKaiIulD\n57Gj\r\n=ATJh\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCusYcAGerP1RicK4cxVie7tvpeYAlxTlZsRfOpfowFXwIhAOMUzufAlW3TJ78vGqa3GJb7iJkntFdY34Dlr6/w1/jJ"}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/xdg-basedir_5.1.0_1628184636227_0.6668901574190473"}, "_hasShrinkwrap": false}}, "readme": "# xdg-basedir\n\n> Get [XDG Base Directory](https://specifications.freedesktop.org/basedir-spec/basedir-spec-latest.html) paths\n\nThis package is meant for Linux. You should not use XDG on macOS or Windows. Instead, you should follow their platform conventions. You can use [`env-paths`](https://github.com/sindresorhus/env-paths) for that.\n\n## Install\n\n```\n$ npm install xdg-basedir\n```\n\n## Usage\n\n```js\nimport {xdgData, xdgConfig, xdgDataDirectories} from 'xdg-basedir';\n\nconsole.log(xdgData);\n//=> '/home/<USER>/.local/share'\n\nconsole.log(xdgConfig);\n//=> '/home/<USER>/.config'\n\nconsole.log(xdgDataDirectories);\n//=> ['/home/<USER>/.local/share', '/usr/local/share/', '/usr/share/']\n```\n\n## API\n\nThe exports `xdgData`, `xdgConfig`, `xdgCache`, `xdgRuntime` will return `undefined` in the uncommon case that both the XDG environment variable is not set and the users home directory can't be found. You need to handle this case. A common solution is to [fall back to a temporary directory](https://github.com/yeoman/configstore/blob/b82690fc401318ad18dcd7d151a0003a4898a314/index.js#L15).\n\n### xdgData\n\nDirectory for user-specific data files.\n\n### xdgConfig\n\nDirectory for user-specific configuration files.\n\n### xdgState\n\nDirectory for user-specific state files.\n\n### xdgCache\n\nDirectory for user-specific non-essential data files.\n\n### xdgRuntime\n\nDirectory for user-specific non-essential runtime files and other file objects (such as sockets, named pipes, etc).\n\n### xdgDataDirectories\n\nPreference-ordered array of base directories to search for data files in addition to `xdgData`.\n\n### xdgConfigDirectories\n\nPreference-ordered array of base directories to search for configuration files in addition to `xdgConfig`.\n\n---\n\n<div align=\"center\">\n\t<b>\n\t\t<a href=\"https://tidelift.com/subscription/pkg/npm-xdg-basedir?utm_source=npm-xdg-basedir&utm_medium=referral&utm_campaign=readme\">Get professional support for this package with a Tidelift subscription</a>\n\t</b>\n\t<br>\n\t<sub>\n\t\tTidelift helps make open source sustainable for maintainers while giving companies<br>assurances about security, maintenance, and licensing for their dependencies.\n\t</sub>\n</div>\n", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-03-04T17:06:23.880Z", "created": "2014-10-06T12:42:04.417Z", "1.0.0": "2014-10-06T12:42:04.417Z", "1.0.1": "2015-01-13T17:57:21.801Z", "2.0.0": "2015-06-13T12:47:10.440Z", "3.0.0": "2017-02-13T09:32:24.818Z", "4.0.0": "2019-04-30T04:09:47.550Z", "5.0.0": "2021-04-05T07:48:35.908Z", "5.0.1": "2021-04-05T08:30:05.149Z", "5.1.0": "2021-08-05T17:30:36.455Z"}, "homepage": "https://github.com/sindresorhus/xdg-basedir#readme", "keywords": ["xdg", "base", "directory", "basedir", "path", "data", "config", "cache", "linux", "unix", "spec"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/xdg-basedir.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/xdg-basedir/issues"}, "license": "MIT", "readmeFilename": "readme.md", "users": {"julien-f": true, "itonyyo": true, "chocolateboy": true, "akiva": true, "jessaustin": true, "flumpus-dev": true}}