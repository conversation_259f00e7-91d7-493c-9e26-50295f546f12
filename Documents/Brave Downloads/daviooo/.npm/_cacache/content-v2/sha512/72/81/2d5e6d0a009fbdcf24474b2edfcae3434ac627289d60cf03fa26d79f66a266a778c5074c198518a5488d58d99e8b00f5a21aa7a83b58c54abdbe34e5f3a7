{"name": "@npmcli/package-json", "dist-tags": {"latest": "6.2.0"}, "versions": {"1.0.0": {"name": "@npmcli/package-json", "version": "1.0.0", "dependencies": {"json-parse-even-better-errors": "^2.3.1"}, "devDependencies": {"tap": "^15.0.9", "@npmcli/lint": "^1.0.1"}, "dist": {"shasum": "74a63b9a472a5a493d8441803769fa4dff9119d5", "tarball": "https://registry.npmjs.org/@npmcli/package-json/-/package-json-1.0.0.tgz", "fileCount": 7, "integrity": "sha512-fGcgENGZDivJeH44gSm3DXOrvxQyRmLh2e6yfOyqI2KblSOo+i+m04MTLhjfZ4eqzMUB9HVBuVSlgs8idlcEug==", "signatures": [{"sig": "MEQCIHrIbj23QTu+spqPkGZeBRPgJlGXMq18TPAr/a6ORxm0AiAmFo7iueA+MuN3jWAncnY4R5YKJ3GTSpOFgmn3Ls2J0g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11077, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg0nEaCRA9TVsSAnZWagAAJwAP/0mS2gYS84XUHa/1i+TF\nQite4hFnB8RikjOu6mCjfcjMEMyiufCSunfjqLzvNsPwqgRBhr3uI0hzMtN0\n0/Y6pMsPH8GOJ270VFwwTIpyvFD95DdYKIY4bxpcM061y1cF5GlDr8hcEQ7i\n0RCsWG5kpcRe9Z0zY0fPCL/yMLIzR0UNLaTyk96J1tuWOWC88j0pSCR0VuK1\nJrjN2LQq730WWL788TjLA6/s6vx5sPiuC2GWW1fO32axCrxeVgO3jTyzLSiF\nfKAXUe2wnYKY2ml9964f+8jlnarj4bc2Y/oV0QFEkOXX5whh0liLRRuzoUJX\n+tHl2oGIZQmdIZGshG48FZgj6WpDq9XrByGfRTRx3uqZSL3+WGk4QTcChdzE\nkUcQs4Y/eXOhupf5vpp8xbxM7GxVsbjL4UIZywgLmZlGd40FOTwbge3HlI7+\n6ZDO/Gs0VAzM0wBL00hDzgrRcfrRiN29p3wIeQR24G00/LeHd/24jXZVkTwG\nCIpC6qYFE7RSaTKFCx6vogTxmGSxmwQUp4WRokwmqz2Cnba/6ruXVMWPh82S\no+ezzEN81owfy+QTk2CfdaLNr5AZ+fg0eGrRa+HgSaVHhvSrJ0exZEJGGIbY\n39+hvXd4lPJOdb1o0ebhBQFCW6rnLaMfZWaa/C/sb43z0EAqgXIsCpcBdBSN\nrsqj\r\n=fI4K\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1": {"name": "@npmcli/package-json", "version": "1.0.1", "dependencies": {"json-parse-even-better-errors": "^2.3.1"}, "devDependencies": {"tap": "^15.0.9", "@npmcli/lint": "^1.0.1"}, "dist": {"shasum": "1ed42f00febe5293c3502fd0ef785647355f6e89", "tarball": "https://registry.npmjs.org/@npmcli/package-json/-/package-json-1.0.1.tgz", "fileCount": 7, "integrity": "sha512-y6jnu76E9C23osz8gEMBayZmaZ69vFOIk8vR1FJL/wbEJ54+9aVG9rLTjQKSXfgYZEr50nw1txBBFfBZZe+bYg==", "signatures": [{"sig": "MEUCIQDKI4yoSe45bc3tknTAhggxfGLHq7U+gkVgH4HOLqSRegIgb0nkTCsvaAPLsCxAeiDYbBz9C4oOwGN8Z7tOtxznVdM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11887, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg02CmCRA9TVsSAnZWagAAou0P/RkMDFmRLeu723CHaapz\nM1cmfwYje9/PqRFG7mIPXX87v920P739PaNwHdmgIGgVwBqX1Hymre1ahxEs\ncTymj6MEKIa8UksseUWTVzViTFlTWLdLpypOKB2Fqn4Hkoc4xtVySMmkieiv\nAAM6Z/bpTsg9/DBwch1jQPE5sCTwzinnvhqdSP08neMbsK6QvwX5mAR4pXWV\n0gHwIJu6N5Dv8qUet3PJ3nwIKUugidsPWGPLJvrKaYZy8KcIGdRqbOB+MczN\nWYCVMb5R/qV7mY6chasatYoLKaxwl/nQ41neLLHV+Dlp6iyarFpVnUOpP7Rv\n3CmoFeKoxg7416w+wV9QWr4fGskNd5OgCDvZPy4ai4FJ0ZhWdx5StbsTsxwk\nPi/RFOA673K192i+ikwdyV9wkmcdzskK3dlTxJUtD1a4UE+AYKfqZBEGf7eN\nE8W9ePEsZDEG9hbpeMklVPpvmMAyBjJG9tk1b719fi3HiUCiv9Eq0nQy4rOt\nSd+ukTr8fijdFdhY45NGOr7EmQkvVWQijILc6jPYrH+FjXZnwhwdZEfGqQ5I\nnyD8NAmopJ3fE55I46iZ7EPMdhuXI79L0aremQnqQA+eHCYLB1w37rjrSb2/\ny/nGrwdsKo8e+86w99+h2sys39qB49eCzkki8XXnsJJruOWwpAccO02hWZLD\nO/UZ\r\n=4jfo\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0": {"name": "@npmcli/package-json", "version": "2.0.0", "dependencies": {"json-parse-even-better-errors": "^2.3.1"}, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "3.2.2", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "3bbcf4677e21055adbe673d9f08c9f9cde942e4a", "tarball": "https://registry.npmjs.org/@npmcli/package-json/-/package-json-2.0.0.tgz", "fileCount": 7, "integrity": "sha512-42jnZ6yl16GzjWSH7vtrmWyJDGVa/LXPdpN2rcUWolFjc9ON2N3uz0qdBbQACfmhuJZ2lbKYtmK5qx68ZPLHMA==", "signatures": [{"sig": "MEYCIQCw5KTDzKMbHJCNHTPV89qp6P4eWQH0OEFcYxvvnE7/OgIhAJpojHSwEhQMJrvAZkwzSzl41KcK93fRytPF7AGIvctJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTEvfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoMIA/9FEgPoXvAndIZWrjnE6GHMZIdfA2M6FCTqqjf/t1bVaqJy/kp\r\nWLi9BTXjQvAG8Imt+n0tv5dDP7ghzE01eLy/astuJ9B3rBKo5K0cPtMClvR4\r\nyuY9w5hURawQzqBx15D7whbOPEPDryigfnl0CobyU3fXue4sdfnZ2x0GbSiq\r\n1l0Maa7SnmUadlBZo4sClqXBgWVlS/uszQJXt13LBMLz1XPrhPpm5goKDjsh\r\nyBxTbfF3enRrw6a7xagvm6ZjHy1hapaA6e7MVnAqOWFnzNmMgOikC6chVhhr\r\nfVGGBob52rXhQix4EvEx5Uji4b4K3vKwMphVJALCmWPZw/lnRRS7VOFG1YUX\r\ni4hTdy9qk/zLuMHp34lHLuPtvrz3r/3ud4pHBmCK1Gle8WjYLR7LIk1KkL5l\r\n1CJmLUg7+K6CGm5Qq2Eg1j01RVCQxs8iW7Ws7JN9Zq5sjjaxsBBnNnycn6c5\r\ntfb57Qoc6PzB+pqL4bBD305LczS2CDd6cbrJHnByWfGydyG75Qm4mKo3nA7t\r\n/5GGFv58H9zDbsmA74ySQBWflDH/UmVZMNB68dALvvfwwNkeyVjf8+6cG1WL\r\nlNMQPheUr1xKpMZGoTdVdwYaGznIUU9qYRr/Qly44t4hpUQFama3VibC7bF7\r\nKzGZe/chaerhsCVjtV50LoP7g6UcSckHBSo=\r\n=h7kD\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "3.0.0": {"name": "@npmcli/package-json", "version": "3.0.0", "dependencies": {"json-parse-even-better-errors": "^3.0.0"}, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "4.5.1", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "c9219a197e1be8dbf43c4ef8767a72277c0533b6", "tarball": "https://registry.npmjs.org/@npmcli/package-json/-/package-json-3.0.0.tgz", "fileCount": 7, "integrity": "sha512-NnuPuM97xfiCpbTEJYtEuKz6CFbpUHtaT0+5via5pQeI25omvQDFbp1GcGJ/c4zvL/WX0qbde6YiLgfZbWFgvg==", "signatures": [{"sig": "MEQCIC1sb+XKMFZmBzZ0MHUV4lXKXdWFn0Xq4BfPpOkokvN3AiAYIbQg+OjL+2RebxpBftuQ9FUbS8z78wIBrwY0d1GUTA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12378, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSMLaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqT+Q/+MwVDLJUllgF49sWZjkG0+ixuHBNK3tuwn5kJMh2oBz7lDksQ\r\ni2ijX+LZ+8dsnAQpbg5l13wvp2NS1/LQ9yPbXguM0yR+maGgCA3r9nlr6tB9\r\nA2vNpiIOiyhaw8m2LBS/5EPtQQjj5JzbSLH8JS7ZZBkn36Gf/JTAMSiRTqrC\r\naiBqBqZtTosBzRbmWJuaFsrELkA92m12l8dQ4+myyYS9l8XyAsD4qftGlU/4\r\nONQrJu4l3vOyUNPUn6xUonUbu1GGjtubCMh3FbC9gdr0BQDTAS8KVGwiW94y\r\nakLGWkApEnjDJlTK3IfuQ8ygmY+rAnx1qwi3UFgYiLTmyqWJBL/EeCFq0pqY\r\nEj9880ZZ0182OKn8Zji9PTswq8bI6bZhXMZsrajFZ7fPfI0ipaa5+387b9Up\r\nwGD7ZsNArVAYlqpxIBuUYiSV5AlVsKYlAKQm7Fi5WluppG7TLa9jGJgw8/KU\r\nU+2wqkF3jYQbsf/L/vjVxvfsq8J8W/lhX0uzpPdsf6aLxOauI5VhLtWvP0R9\r\nMRI4I7nSIlHrrKZ2d+q4LBZLiT9mXONHlnvfUMvaSBZ2x0pq1WHZ2nxYpYOm\r\niMcwZMr34yWLY1Shy8/j67CIjyiR2mV3kgd70dW/HSbpUTtIHA50jrzC/0rn\r\nD3mXUqkKZI/11E4Y/F+TD2kZvRxVzcb7VVg=\r\n=Nw0p\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "3.1.0": {"name": "@npmcli/package-json", "version": "3.1.0", "dependencies": {"glob": "^10.2.2", "normalize-package-data": "^5.0.0", "npm-normalize-package-bin": "^3.0.1", "json-parse-even-better-errors": "^3.0.0"}, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "4.15.1", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "d9eb34083be4275520f3844d17fc74926d47cae1", "tarball": "https://registry.npmjs.org/@npmcli/package-json/-/package-json-3.1.0.tgz", "fileCount": 8, "integrity": "sha512-qNPy6Yf9ruFST99xcrl5EWAvrb7qFrwgVbwdzcTJlIgxbArKOq5e/bgZ6rTL1X9hDgAdPbvL8RWx/OTLSB0ToA==", "signatures": [{"sig": "MEYCIQCbxWIEMu/6a7+ze+k0SLv6PgeUfBmkm+UqgMR7m1Ew9QIhAIX+ZaFvHIDgetUD2blik4cJh82cCS/1fEjMaHa+VdIk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2fpackage-json@3.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 23843}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "3.1.1": {"name": "@npmcli/package-json", "version": "3.1.1", "dependencies": {"glob": "^10.2.2", "proc-log": "^3.0.0", "@npmcli/git": "^4.1.0", "normalize-package-data": "^5.0.0", "npm-normalize-package-bin": "^3.0.1", "json-parse-even-better-errors": "^3.0.0"}, "devDependencies": {"tap": "^16.0.1", "read-package-json": "^6.0.4", "@npmcli/template-oss": "4.15.1", "@npmcli/eslint-config": "^4.0.0", "read-package-json-fast": "^3.0.2"}, "dist": {"shasum": "5628332aac90fa1b4d6f98e03988c5958b35e0c5", "tarball": "https://registry.npmjs.org/@npmcli/package-json/-/package-json-3.1.1.tgz", "fileCount": 8, "integrity": "sha512-+UW0UWOYFKCkvszLoTwrYGrjNrT8tI5Ckeb/h+Z1y1fsNJEctl7HmerA5j2FgmoqFaLI2gsA1X9KgMFqx/bRmA==", "signatures": [{"sig": "MEYCIQC4XEZS0rHwaNCblwG9FYTkGNR3NhWM1EQhsyOkXfqc5AIhAP19n2/YYGdPIE1+78gx+6wVnLYMtAiHQnepC+8Ux9IB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2fpackage-json@3.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 24452}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "4.0.0": {"name": "@npmcli/package-json", "version": "4.0.0", "dependencies": {"glob": "^10.2.2", "proc-log": "^3.0.0", "@npmcli/git": "^4.1.0", "normalize-package-data": "^5.0.0", "npm-normalize-package-bin": "^3.0.1", "json-parse-even-better-errors": "^3.0.0"}, "devDependencies": {"tap": "^16.0.1", "read-package-json": "^6.0.4", "@npmcli/template-oss": "4.15.1", "@npmcli/eslint-config": "^4.0.0", "read-package-json-fast": "^3.0.2"}, "dist": {"shasum": "a63ae5084eb031e7cdadd3d40b511445f1fe8631", "tarball": "https://registry.npmjs.org/@npmcli/package-json/-/package-json-4.0.0.tgz", "fileCount": 8, "integrity": "sha512-ZeXtZBQ/xjSUmrZj9R1Y2gsQRfkdhP5H31SCieJbAd8bHbn4YRglOoajcEZTJTM9m9BuEE7KiDcMPEoD/OgJkw==", "signatures": [{"sig": "MEUCIQDZppxsxO7xVn01c9cSvwIhZN7JO+NSqTzST6eTFBK4wQIgHZQWLT/uiW9ceQ510g30afeNbroTv9D7GnkEc0b+Vww=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2fpackage-json@4.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 30817}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "4.0.1": {"name": "@npmcli/package-json", "version": "4.0.1", "dependencies": {"glob": "^10.2.2", "semver": "^7.5.3", "proc-log": "^3.0.0", "@npmcli/git": "^4.1.0", "hosted-git-info": "^6.1.1", "normalize-package-data": "^5.0.0", "json-parse-even-better-errors": "^3.0.0"}, "devDependencies": {"tap": "^16.0.1", "read-package-json": "^6.0.4", "@npmcli/template-oss": "4.17.0", "@npmcli/eslint-config": "^4.0.0", "read-package-json-fast": "^3.0.2"}, "dist": {"shasum": "1a07bf0e086b640500791f6bf245ff43cc27fa37", "tarball": "https://registry.npmjs.org/@npmcli/package-json/-/package-json-4.0.1.tgz", "fileCount": 8, "integrity": "sha512-lRCEGdHZomFsURroh522YvA/2cVb9oPIJrjHanCJZkiasz1BzcnLr3tBJhlV7S86MBJBuAQ33is2D60YitZL2Q==", "signatures": [{"sig": "MEUCIAJY0MFfNPabqCr7p3MqMRf//g8Bq0C43OjOAUdao/9HAiEAurd7PKVBi5cE+6dCGt8yQaj32e2y3a89Dgb6OS2Jv9Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2fpackage-json@4.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 36924}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "5.0.0": {"name": "@npmcli/package-json", "version": "5.0.0", "dependencies": {"glob": "^10.2.2", "semver": "^7.5.3", "proc-log": "^3.0.0", "@npmcli/git": "^5.0.0", "hosted-git-info": "^7.0.0", "normalize-package-data": "^6.0.0", "json-parse-even-better-errors": "^3.0.0"}, "devDependencies": {"tap": "^16.0.1", "read-package-json": "^6.0.4", "@npmcli/template-oss": "4.18.0", "@npmcli/eslint-config": "^4.0.0", "read-package-json-fast": "^3.0.2"}, "dist": {"shasum": "77d0f8b17096763ccbd8af03b7117ba6e34d6e91", "tarball": "https://registry.npmjs.org/@npmcli/package-json/-/package-json-5.0.0.tgz", "fileCount": 8, "integrity": "sha512-OI2zdYBLhQ7kpNPaJxiflofYIpkNLi+lnGdzqUOfRmCF3r2l1nadcjtCYMJKv/Utm/ZtlffaUuTiAktPHbc17g==", "signatures": [{"sig": "MEUCIH3t0sBVDvLDvdkdwgUOP+eFZL9SCzrpY04uAfgKLcekAiEAw3GVJyi8MDVrMUHvNaeVC4uy3GVxW0zXn0QcJhM4LeE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2fpackage-json@5.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 36999}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "5.0.1": {"name": "@npmcli/package-json", "version": "5.0.1", "dependencies": {"glob": "^10.2.2", "semver": "^7.5.3", "proc-log": "^3.0.0", "@npmcli/git": "^5.0.0", "hosted-git-info": "^7.0.0", "normalize-package-data": "^6.0.0", "json-parse-even-better-errors": "^3.0.0"}, "devDependencies": {"tap": "^16.0.1", "read-package-json": "^7.0.0", "@npmcli/template-oss": "4.21.3", "@npmcli/eslint-config": "^4.0.0", "read-package-json-fast": "^3.0.2"}, "dist": {"shasum": "c26ca69e89d4dd22d71ceee1db7949e40654a862", "tarball": "https://registry.npmjs.org/@npmcli/package-json/-/package-json-5.0.1.tgz", "fileCount": 8, "integrity": "sha512-WdwGsRP/do+94IXEgfD/oGGVn0VDS+wYM8MoXU5tJ+02Ke8ePSobMwnfcCHAfcvU/pFwZxyZYWaJdOBsqXRAbA==", "signatures": [{"sig": "MEUCIQDsxw+GHhpMEMT8Xa1ByuvhlQsHEdahXyyvm3Y9b4coGQIgE2Ok/VZxrS8WyUVObxygqSGv8bcVMapxocWrftf4eY8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2fpackage-json@5.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 37012}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "5.0.2": {"name": "@npmcli/package-json", "version": "5.0.2", "dependencies": {"glob": "^10.2.2", "semver": "^7.5.3", "proc-log": "^3.0.0", "@npmcli/git": "^5.0.0", "hosted-git-info": "^7.0.0", "normalize-package-data": "^6.0.0", "json-parse-even-better-errors": "^3.0.0"}, "devDependencies": {"tap": "^16.0.1", "read-package-json": "^7.0.0", "@npmcli/template-oss": "4.21.3", "@npmcli/eslint-config": "^4.0.0", "read-package-json-fast": "^3.0.2"}, "dist": {"shasum": "9bf4a04d669d1743b358338eba5a19504e89b057", "tarball": "https://registry.npmjs.org/@npmcli/package-json/-/package-json-5.0.2.tgz", "fileCount": 8, "integrity": "sha512-LmW+tueGSK+FCM3OpcKtwKKo3igpefh6HHiw23sGd8OdJ8l0GrfGfVdGOFVtJRMaXVnvI1RUdEPlB9VUln5Wbw==", "signatures": [{"sig": "MEUCIQCqVQw9xwb+FPPUby6DOEBjVl6hZFJ3mq7hVx8SQoDEigIgbQwqpVt4XVMCz2sCKU04bFAfxCxhrJ9abMkGBMFBbbY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2fpackage-json@5.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 37352}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "5.0.3": {"name": "@npmcli/package-json", "version": "5.0.3", "dependencies": {"glob": "^10.2.2", "semver": "^7.5.3", "proc-log": "^4.0.0", "@npmcli/git": "^5.0.0", "hosted-git-info": "^7.0.0", "normalize-package-data": "^6.0.0", "json-parse-even-better-errors": "^3.0.0"}, "devDependencies": {"tap": "^16.0.1", "read-package-json": "^7.0.0", "@npmcli/template-oss": "4.21.3", "@npmcli/eslint-config": "^4.0.0", "read-package-json-fast": "^3.0.2"}, "dist": {"shasum": "d8a922fcd5abe27a8b0ed619beddfef0f44e614e", "tarball": "https://registry.npmjs.org/@npmcli/package-json/-/package-json-5.0.3.tgz", "fileCount": 8, "integrity": "sha512-cgsjCvld2wMqkUqvY+SZI+1ZJ7umGBYc9IAKfqJRKJCcs7hCQYxScUgdsyrRINk3VmdCYf9TXiLBHQ6ECTxhtg==", "signatures": [{"sig": "MEUCIQD/EywnPutTosHZKiRp8K+FlFvfwD2JDcTSC1FBMO7W+AIgcL1kjmMDSHm3QAUC8DvBDaHu6V0C6io8RdmwK2JUrrc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2fpackage-json@5.0.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 37356}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "5.1.0": {"name": "@npmcli/package-json", "version": "5.1.0", "dependencies": {"glob": "^10.2.2", "semver": "^7.5.3", "proc-log": "^4.0.0", "@npmcli/git": "^5.0.0", "hosted-git-info": "^7.0.0", "normalize-package-data": "^6.0.0", "json-parse-even-better-errors": "^3.0.0"}, "devDependencies": {"tap": "^16.0.1", "read-package-json": "^7.0.0", "@npmcli/template-oss": "4.21.4", "@npmcli/eslint-config": "^4.0.0", "read-package-json-fast": "^3.0.2"}, "dist": {"shasum": "10d117b5fb175acc14c70901a151c52deffc843e", "tarball": "https://registry.npmjs.org/@npmcli/package-json/-/package-json-5.1.0.tgz", "fileCount": 8, "integrity": "sha512-1aL4TuVrLS9sf8quCLerU3H9J4vtCtgu8VauYozrmEyU57i/EdKleCnsQ7vpnABIH6c9mnTxcH5sFkO3BlV8wQ==", "signatures": [{"sig": "MEYCIQDTbdLrx9gHmxAdETEjStoMKP8FCuDMnT1uz+3mAuQzLgIhAMUHvgi87/VXDJzVcuwQAM8RVQHfOsKeTFSzJU3cljj/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2fpackage-json@5.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 37452}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "5.1.1": {"name": "@npmcli/package-json", "version": "5.1.1", "dependencies": {"glob": "^10.2.2", "semver": "^7.5.3", "proc-log": "^4.0.0", "@npmcli/git": "^5.0.0", "hosted-git-info": "^7.0.0", "normalize-package-data": "^6.0.0", "json-parse-even-better-errors": "^3.0.0"}, "devDependencies": {"tap": "^16.0.1", "read-package-json": "^7.0.0", "@npmcli/template-oss": "4.22.0", "@npmcli/eslint-config": "^4.0.0", "read-package-json-fast": "^3.0.2"}, "dist": {"shasum": "ec7339438ae16fcb2216f1c34a0dad707e62d55b", "tarball": "https://registry.npmjs.org/@npmcli/package-json/-/package-json-5.1.1.tgz", "fileCount": 8, "integrity": "sha512-uTq5j/UqUzbOaOxVy+osfOhpqOiLfUZ0Ut33UbcyyAPJbZcJsf4Mrsyb8r58FoIFlofw0iOFsuCA/oDK14VDJQ==", "signatures": [{"sig": "MEYCIQCfTIxBkam7ag6X79n7XZZ9dzK6ndAIq96O3DZFBYw+iQIhANjqaGKVluArNSROaPf9wFEwsPpFPDrHO/XfuNBA3qTL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2fpackage-json@5.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 38243}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "5.2.0": {"name": "@npmcli/package-json", "version": "5.2.0", "dependencies": {"glob": "^10.2.2", "semver": "^7.5.3", "proc-log": "^4.0.0", "@npmcli/git": "^5.0.0", "hosted-git-info": "^7.0.0", "normalize-package-data": "^6.0.0", "json-parse-even-better-errors": "^3.0.0"}, "devDependencies": {"tap": "^16.0.1", "read-package-json": "^7.0.0", "@npmcli/template-oss": "4.22.0", "@npmcli/eslint-config": "^4.0.0", "read-package-json-fast": "^3.0.2"}, "dist": {"shasum": "a1429d3111c10044c7efbfb0fce9f2c501f4cfad", "tarball": "https://registry.npmjs.org/@npmcli/package-json/-/package-json-5.2.0.tgz", "fileCount": 9, "integrity": "sha512-qe/kiqqkW0AGtvBjL8TJKZk/eBBSpnJkUWvHdQ9jM2lKHXRYYJuyNpJPlJw3c8QjC2ow6NZYiLExhUaeJelbxQ==", "signatures": [{"sig": "MEQCIHxaoFXF0lW1JiKUIrrXa49BnQb1t4pKkWLMMTlpVQD5AiANe3yohHuuKK8vvdnQDGlbOcFxQi0Soc9JgfsymQzSHw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2fpackage-json@5.2.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 39533}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "5.2.1": {"name": "@npmcli/package-json", "version": "5.2.1", "dependencies": {"glob": "^10.2.2", "semver": "^7.5.3", "proc-log": "^4.0.0", "@npmcli/git": "^5.0.0", "hosted-git-info": "^7.0.0", "normalize-package-data": "^6.0.0", "json-parse-even-better-errors": "^3.0.0"}, "devDependencies": {"tap": "^16.0.1", "read-package-json": "^7.0.0", "@npmcli/template-oss": "4.23.3", "@npmcli/eslint-config": "^4.0.0", "read-package-json-fast": "^3.0.2"}, "dist": {"shasum": "df69477b1023b81ff8503f2b9db4db4faea567ed", "tarball": "https://registry.npmjs.org/@npmcli/package-json/-/package-json-5.2.1.tgz", "fileCount": 9, "integrity": "sha512-f7zYC6kQautXHvNbLEWgD/uGu1+xCn9izgqBfgItWSx22U0ZDekxN08A1vM8cTxj/cRVe0Q94Ode+tdoYmIOOQ==", "signatures": [{"sig": "MEUCIQDhfonDHkZWslwamWks0VMTAp2H/jLyg5UJGuf40hSDMAIgffOc4v3Yl8+Mw2Ts5ddD5SmRxzeGAytMzLHJstqVKak=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2fpackage-json@5.2.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 39522}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "6.0.0": {"name": "@npmcli/package-json", "version": "6.0.0", "dependencies": {"glob": "^10.2.2", "semver": "^7.5.3", "proc-log": "^5.0.0", "@npmcli/git": "^5.0.0", "hosted-git-info": "^8.0.0", "normalize-package-data": "^7.0.0", "json-parse-even-better-errors": "^4.0.0"}, "devDependencies": {"tap": "^16.0.1", "read-package-json": "^7.0.0", "@npmcli/template-oss": "4.23.3", "@npmcli/eslint-config": "^5.0.0", "read-package-json-fast": "^4.0.0"}, "dist": {"shasum": "a6fe597a3783cfbf48d67beedb66de46f252f172", "tarball": "https://registry.npmjs.org/@npmcli/package-json/-/package-json-6.0.0.tgz", "fileCount": 9, "integrity": "sha512-6+SgO3DjUi0hTCP5ih+Z8xYq6ZIpUwp7A3CNDr5zGotXndqk97GBnvp3BeN0xDM/aGcHYWatJpHwse9xD8F9WA==", "signatures": [{"sig": "MEYCIQD/D3cW0p6uTBb8OwTwgOG0Qys0EeXUNp58MGrVQibNyQIhAOInWRxwFDRcwGEoNQ35el7SUMINBjDIvL3uZ59DNRIa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2fpackage-json@6.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 39522}, "engines": {"node": "^18.17.0 || >=20.5.0"}}, "6.0.1": {"name": "@npmcli/package-json", "version": "6.0.1", "dependencies": {"glob": "^10.2.2", "semver": "^7.5.3", "proc-log": "^5.0.0", "@npmcli/git": "^6.0.0", "hosted-git-info": "^8.0.0", "normalize-package-data": "^7.0.0", "json-parse-even-better-errors": "^4.0.0"}, "devDependencies": {"tap": "^16.0.1", "read-package-json": "^7.0.0", "@npmcli/template-oss": "4.23.3", "@npmcli/eslint-config": "^5.0.0", "read-package-json-fast": "^4.0.0"}, "dist": {"shasum": "550a8eb3e0ae9ad8577cb7a3f2d677a04a3bcee9", "tarball": "https://registry.npmjs.org/@npmcli/package-json/-/package-json-6.0.1.tgz", "fileCount": 9, "integrity": "sha512-YW6PZ99sc1Q4DINEY2td5z9Z3rwbbsx7CyCnOc7UXUUdePXh5gPi1UeaoQVmKQMVbIU7aOwX2l1OG5ZfjgGi5g==", "signatures": [{"sig": "MEYCIQCLn3O06zwyGif2S8wZChMho41T29TNpo8G4odZDxkstQIhAJsz7ferCLmZgr72e05xStnfLM9YAbzSdgPQCUQ0eIRZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2fpackage-json@6.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 39522}, "engines": {"node": "^18.17.0 || >=20.5.0"}}, "6.1.0": {"name": "@npmcli/package-json", "version": "6.1.0", "dependencies": {"glob": "^10.2.2", "semver": "^7.5.3", "proc-log": "^5.0.0", "@npmcli/git": "^6.0.0", "hosted-git-info": "^8.0.0", "normalize-package-data": "^7.0.0", "json-parse-even-better-errors": "^4.0.0"}, "devDependencies": {"tap": "^16.0.1", "read-package-json": "^7.0.0", "@npmcli/template-oss": "4.23.5", "@npmcli/eslint-config": "^5.0.0", "read-package-json-fast": "^4.0.0"}, "dist": {"shasum": "34f0875da178b04df1a7746c02bdc26479819afb", "tarball": "https://registry.npmjs.org/@npmcli/package-json/-/package-json-6.1.0.tgz", "fileCount": 10, "integrity": "sha512-t6G+6ZInT4X+tqj2i+wlLIeCKnKOTuz9/VFYDtj+TGTur5q7sp/OYrQA19LdBbWfXDOi0Y4jtedV6xtB8zQ9ug==", "signatures": [{"sig": "MEQCIEZo8hRVpVaqrkHgic0ig/UNWlh1xWyEGTUDE9PUWpdtAiBXv1gVK1S2KqD18lCGuAC41IWGCprnhEeyfQnDxfdNJQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2fpackage-json@6.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 43232}, "engines": {"node": "^18.17.0 || >=20.5.0"}}, "6.1.1": {"name": "@npmcli/package-json", "version": "6.1.1", "dependencies": {"glob": "^10.2.2", "semver": "^7.5.3", "proc-log": "^5.0.0", "@npmcli/git": "^6.0.0", "hosted-git-info": "^8.0.0", "validate-npm-package-license": "^3.0.4", "json-parse-even-better-errors": "^4.0.0"}, "devDependencies": {"tap": "^16.0.1", "read-package-json": "^7.0.0", "@npmcli/template-oss": "4.23.6", "@npmcli/eslint-config": "^5.1.0", "read-package-json-fast": "^4.0.0"}, "dist": {"shasum": "78ff92d138fdcb85f31cab907455d5db96d017cb", "tarball": "https://registry.npmjs.org/@npmcli/package-json/-/package-json-6.1.1.tgz", "fileCount": 11, "integrity": "sha512-d5qimadRAUCO4A/Txw71VM7UrRZzV+NPclxz/dc+M6B2oYwjWTjqh8HA/sGQgs9VZuJ6I/P7XIAlJvgrl27ZOw==", "signatures": [{"sig": "MEUCIQDBFpNZZ9mjR09eTY0g4LCxErVDuEM40O7tqiBHT8hrQwIgGF5LQVaMPQdLN/oX+C26KqNWNV6l0oUKy5qLRldinN8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2fpackage-json@6.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 50779}, "engines": {"node": "^18.17.0 || >=20.5.0"}}, "6.2.0": {"name": "@npmcli/package-json", "version": "6.2.0", "dependencies": {"@npmcli/git": "^6.0.0", "glob": "^10.2.2", "hosted-git-info": "^8.0.0", "json-parse-even-better-errors": "^4.0.0", "proc-log": "^5.0.0", "semver": "^7.5.3", "validate-npm-package-license": "^3.0.4"}, "devDependencies": {"@npmcli/eslint-config": "^5.1.0", "@npmcli/template-oss": "4.23.6", "read-package-json": "^7.0.0", "read-package-json-fast": "^4.0.0", "tap": "^16.0.1"}, "dist": {"integrity": "sha512-rCNLSB/JzNvot0SEyXqWZ7tX2B5dD2a1br2Dp0vSYVo5jh8Z0EZ7lS9TsZ1UtziddB1UfNUaMCc538/HztnJGA==", "shasum": "7c7e61e466eefdf729cb87a34c3adc15d76e2f97", "tarball": "https://registry.npmjs.org/@npmcli/package-json/-/package-json-6.2.0.tgz", "fileCount": 11, "unpackedSize": 51137, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2fpackage-json@6.2.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIAiPGwPnZErZxUh7rlKtsi/KoxBHZhOs6ioRIEx30XIfAiBOTJTy4tzmf0KdBU/vBhA8nPkpbaSNq75BRF/ijE1bcQ=="}]}, "engines": {"node": "^18.17.0 || >=20.5.0"}}}, "modified": "2025-05-21T16:28:33.780Z"}