{"_id": "@szmarczak/http-timer", "_rev": "20-b3d4a78f1bdeecfc7f8bf594a91cf307", "name": "@szmarczak/http-timer", "dist-tags": {"latest": "5.0.1"}, "versions": {"1.0.0": {"name": "@szmarczak/http-timer", "version": "1.0.0", "description": "Timings for HTTP requests", "main": "source", "engines": {"node": ">=5.10.0"}, "scripts": {"test": "xo && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "files": ["source"], "keywords": ["http", "https", "timer", "timings"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http-timer.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http-timer/issues"}, "homepage": "https://github.com/szmarczak/http-timer#readme", "xo": {"rules": {"unicorn/filename-case": "camelCase"}}, "devDependencies": {"ava": "^0.25.0", "coveralls": "^3.0.2", "p-event": "^2.1.0", "nyc": "^12.0.2", "xo": "^0.22.0"}, "_id": "@szmarczak/http-timer@1.0.0", "_npmVersion": "6.2.0", "_nodeVersion": "10.9.0", "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "dist": {"integrity": "sha512-LyA3j0cIXq96VyPTizIkX17noPaoO/scloz1T8f9cKTLSg2zlTg0pw0x7nnyv9r7sKyJROC4eI1P/3IFNL33Pg==", "shasum": "e3f139c7d0de1ea3048d1fc0a4cafcb9f9497c54", "tarball": "https://registry.npmjs.org/@szmarczak/http-timer/-/http-timer-1.0.0.tgz", "fileCount": 4, "unpackedSize": 4563, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgF7CCRA9TVsSAnZWagAArOQP/3WUnUZARUR27XVrGdDC\nmg1qQ3XWb+wcLQStMknjk7Nlc11p/VfBC82tdjP4tVfiSZOgmocWpmr2J6Ep\n5dQGIExCjZaxcSfT9QY4DsX3w1t66dLQ3i78GittswHknNyRbFWrvCgFKAAz\nYkKupFdbRdc5nG8AjofWmo/zGbzKfmvC6L81PpKP2EKEPylDjXcTLqUxszKP\naYZLQ9JqB5IbWHfpoO+/kFuHx2/RUv/zjxXoXPJ7qovevATpNlAiz0Ige7+d\n+dhwMyATOgdLUKQ9/Pl9iiT7VF7UkjK/7cdYgHkuvyLv260QIoiJwnfXOvEV\nDR/1OqK2gGaj/edzFv0uPTqavg5vUKECnzCPt0OgEjaYDyIETR3c1mjkLqsS\nP8LryPbGEZCIuApPks3dE9VGxWvV2TnwmEVgh4fylBNfZNBOlED4Be2L0wC2\n2u4m3Zm1dLnsuYdduVgfsckSu/pJD4CjWf/Z7OH0yzLTSvlqBkemgN6aIZwZ\n9O/KKraXQcGuWlWqxeVdZSLSPp/Wt8VP174eS5u9RDo4/0p4uqGUuQJbzyJW\n6V5ml1R5I0vHGBkbIKQB/tefgNxFqinHS7UhikmKEHiUiNBlg0X6290J4o+0\nD5p4uupv5S4i0EemVu5uwKKN0SGLlBUlbxFfjUZd91QJjoNHr18qLmezQg4V\n8WtE\r\n=Bdm2\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCj4rHhQcq35G87OBy94q6U5GypAoGriEvNj0XZSnqmZAIgDDOPgvolHnwLGb8vxpO5jxd2h6mO7N2EFsPXUsSVbus="}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-timer_1.0.0_1535139521640_0.2151615588016218"}, "_hasShrinkwrap": false}, "1.0.1": {"name": "@szmarczak/http-timer", "version": "1.0.1", "description": "Timings for HTTP requests", "main": "source", "engines": {"node": ">=5.10.0"}, "scripts": {"test": "xo && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "files": ["source"], "keywords": ["http", "https", "timer", "timings"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http-timer.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http-timer/issues"}, "homepage": "https://github.com/szmarczak/http-timer#readme", "xo": {"rules": {"unicorn/filename-case": "camelCase"}}, "devDependencies": {"ava": "^0.25.0", "coveralls": "^3.0.2", "p-event": "^2.1.0", "nyc": "^12.0.2", "xo": "^0.22.0"}, "gitHead": "38984f458f2bf714a20f18a447aab4970ac8b441", "_id": "@szmarczak/http-timer@1.0.1", "_npmVersion": "6.2.0", "_nodeVersion": "10.9.0", "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "dist": {"integrity": "sha512-PMCFjst2r4lrprb2ZzO54GA0qBKZL6GvM2din4SfjoZYIMr2uj1ayKLSripsjaBZg/6Jq0KhnkwdY+goOmdtNg==", "shasum": "c25791a0d26d4590b5320bcbd9526cc4244d2e8f", "tarball": "https://registry.npmjs.org/@szmarczak/http-timer/-/http-timer-1.0.1.tgz", "fileCount": 4, "unpackedSize": 4472, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgGJcCRA9TVsSAnZWagAADy0QAI6Eg1O5XbXl69f9COJo\n5SihZNBmm/6CGzOMdc05npm7WPI+i1N76JJy/KCYpxN934CQhWu3kBS1S+ni\n2bxuRe9RbFRcLekVxLj712He1ZaafqBhUya5AC7TY9MUaNKWDTE7e2rxMyNV\nk5HiVxVzqJODylH0Bn+Dd5ZWwBHQA8hicv5dNhDGZUjDq07bgBcejhWZ2UuT\nVErYApIu8aSFfppxKSbnzimzISRcRb4TQEoIB3bHvpBqiYpp1lw8RxPFNIRo\nR+8mATJEY7YwLcg67JPYbc4gyXBalfNkTr58FV7oWMydKndMuvC85eG1XmUm\nup2rP9qSnnwdMpK84r5ySlanmzlcSke8RfSqyMIxKm/N6wi4kctssFqVaR4+\ntLTYhmtourHWy1X+ScBIzNZLd3wDXGkfLIBrnxenoJ1NUCSWuin0xp3JL2iG\nuxNhvx/qg2H1kuxxywmV8ocBalsx74d+9/iJ19PfkJqDRvc5ecThTX9/onbD\npwrkZ4fNHClFoSz3hcxl1KuVTbCfpuIgFjarbtVLK1F1fMM9pVPH19ljQETy\nw8i2R2N47DVEyJcY278NUek8kubC0B69KrH9xL+L+9SXKdd8QMUra+JBZr5+\nCQfZ3YYo7wBbyH1LX99+5mvAAlFLMxXAttPjTw2XlRTV0BxItED4b+iViYVP\nHqAK\r\n=NhhH\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAJbsUNvuN7Ra5xLasAp9WvHrT2oBjsgGqiAU7rWZyLtAiEAhOgjlHd0R2/0Khx5LaEpUMH8MZsA++xzSDSvUf5T+N4="}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-timer_1.0.1_1535140443786_0.7093421137350722"}, "_hasShrinkwrap": false}, "1.0.2": {"name": "@szmarczak/http-timer", "version": "1.0.2", "description": "Timings for HTTP requests", "main": "source", "engines": {"node": ">=5.10.0"}, "scripts": {"test": "xo && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "files": ["source"], "keywords": ["http", "https", "timer", "timings"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http-timer.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http-timer/issues"}, "homepage": "https://github.com/szmarczak/http-timer#readme", "xo": {"rules": {"unicorn/filename-case": "camelCase"}}, "devDependencies": {"ava": "^0.25.0", "coveralls": "^3.0.2", "p-event": "^2.1.0", "nyc": "^12.0.2", "xo": "^0.22.0"}, "dependencies": {"defer-to-connect": "^1.0.1"}, "gitHead": "dd3581642540c11caec989cac98d1aa840090a1f", "_id": "@szmarczak/http-timer@1.0.2", "_npmVersion": "6.2.0", "_nodeVersion": "10.9.0", "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "dist": {"integrity": "sha512-1Xv0I90OOdq13xfHzHPeNuinR2TpThDIKynTor12CL5EtA0sGuWzvmpIGnm1hyhAhosGUTboROq6bhqIDO1zRQ==", "shasum": "59873d465aca1ac04a29dafa0966c21112d4cfae", "tarball": "https://registry.npmjs.org/@szmarczak/http-timer/-/http-timer-1.0.2.tgz", "fileCount": 4, "unpackedSize": 4438, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgQ9xCRA9TVsSAnZWagAAt+MP/075euEwtHzGN4RTfZWi\ncm7z5sgfaYWZx5AyYmtVueYM6Pf5VW0lJSEGFmEyFIaiT7vs7nuy2PVq+/iD\n09tbEVLv7hFpvyxtVS2heyAjr7zr+stya1rigxH0oiWHdkTxqcpByFWdKfY2\npuFpt7enRqdlWvFn+pLSlADDYjvVsQ4nDHRlWE44VISvIiK1wKhZdOxvmh64\ns4Tpv7D7U039prq4DCtWIqbBAEPWVjv8rSD6ILUSNgMNob1yw5V2qUzSt28/\ntqgBY4tVBB1qa/Vio4xT07kBBOlpdmIfN9X5ppiDl9kizcSL9iWiYVVMi9d9\nrN2F+fIkcRPFPjtbcoCDvYyehXzqBcxVTRhaq+AjHvDi4BKCoTTdtpI4kxmg\nFfPzTnwnaI5ZojJfSp9HFhOVxFpL+Wcm2vma3L309Eu9/Vkz9hBxhRXAH53X\nQBHJpQxaqD0gcM9Vfbe6lpFGjkwQaDnxtfUv69DL/DILjR96UTE/71Fip/th\n+jotjFdWkFlDlK9vyyvdVfIhYJdFctr4AL6r/rrdA+ri9aiXXlIf44NaopMP\nCSDiHqlUK/hBJO6Xig0vD4MYlGfGZMS6GFahCn5dR9iBwlm7NoA/t7YQnqsl\nLb6nQF5VGix+htORRVWn+HgRhCPkJ3/4nN0DksVBF9DBrIL25B92yC/cAxC/\nw7WY\r\n=6wTr\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFDY7ee0mk36pfA+X+65tbpHLxQFNzy5dA1vJ2dLTHyCAiEA+bBsG14LzkoZppwsyp4fSAYcy+p2RZke1CSur0nOoxM="}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-timer_1.0.2_1535184752882_0.053243571634998155"}, "_hasShrinkwrap": false}, "1.1.0": {"name": "@szmarczak/http-timer", "version": "1.1.0", "description": "Timings for HTTP requests", "main": "source", "engines": {"node": ">=6"}, "scripts": {"test": "xo && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["http", "https", "timer", "timings"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http-timer.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http-timer/issues"}, "homepage": "https://github.com/szmarczak/http-timer#readme", "xo": {"rules": {"unicorn/filename-case": "camelCase"}}, "devDependencies": {"ava": "^0.25.0", "coveralls": "^3.0.2", "p-event": "^2.1.0", "nyc": "^12.0.2", "xo": "^0.22.0"}, "dependencies": {"defer-to-connect": "^1.0.1"}, "gitHead": "06b516bb0a076dbae385fee7e8e393c968541c3d", "_id": "@szmarczak/http-timer@1.1.0", "_npmVersion": "6.4.0", "_nodeVersion": "10.9.0", "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "dist": {"integrity": "sha512-ovIGig9pFGX8wKwSg4MZw5ocsDmEcjcUEbVqr6syeBaFANIUoNy1GkZWuPz+VNAeD6AzffZxtuJA23I0PUj9iw==", "shasum": "9d57e87a07bbf36bc69f295063265eefec7e5045", "tarball": "https://registry.npmjs.org/@szmarczak/http-timer/-/http-timer-1.1.0.tgz", "fileCount": 4, "unpackedSize": 5805, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbiR2BCRA9TVsSAnZWagAAKeUP/jLebBeKlepARXvV/UOr\ndKDlwuwM6VQSLk5wKBZpWIhOt3WY9zxBSluMA/NxG6tI7WKp4ip+wS07TT5J\n8PhXkPH/wZi5iN0my9wMnDI/9glezqE0Wb2exN4CoHdxGscpQbyCTY5SP+Px\njYcBqGQwWKH+4uEwv3Zb0R62LSSdCHlJRc8YgORx1Xg5q4oErn9aX7HIUwgS\naSHC9GkSAu6KfSgyqAGN/lPz3wOCQVcN5j5C8yCvz+uNZZe37d+h91Y21WUM\nwgRSmtL6yzdSwdNZOewOR0by8tiDHMNFdTGkFsi2Bwc7e/PwT1P34YIELApf\naBubStYJy/tW2RG6aTkLXTsyIrrBpIb4EwkraWgB2YIV5ZKFnFpybMCd6mel\nUgIu0r0Q2mbQODKCondgCUVJUR3velYH28CCYDwH7XXw/+JOPSeFa/8PLunq\nCvdkaOYGoS4YUj/2YmsraW3zCjGmGa41hKCzXwU11se9mTHSitXbG+xjNFgF\nh5Zl49mNY1nBMICr5rk7gA6fRtcBFXOrRUCM0dCPu+gcGehvaAnBA+69iZ32\nEkz5Bh47NUwuMmfaYmNJK2l/eUfs0lWl1eZ+A/hQ5Ac96WzpQC+YB8KGFXmL\nS/WpCTGaYmGJwCOChka9YYE+gAZ7lSwsktmkvp8o1mGgNubWVIfFVfzac/yH\nJLpd\r\n=iPyv\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAGTVaUg62SMSoIcpyO1eMNZV1L24ZEu0QyZcTErQZv4AiBUZ6tvLVqsmvn3R6rl3j/OpmU7ARVXkquC55SQfSeUHA=="}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-timer_1.1.0_1535712640480_0.6381735004576952"}, "_hasShrinkwrap": false}, "1.1.1": {"name": "@szmarczak/http-timer", "version": "1.1.1", "description": "Timings for HTTP requests", "main": "source", "engines": {"node": ">=6"}, "scripts": {"test": "xo && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["http", "https", "timer", "timings"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http-timer.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http-timer/issues"}, "homepage": "https://github.com/szmarczak/http-timer#readme", "xo": {"rules": {"unicorn/filename-case": "camelCase"}}, "devDependencies": {"ava": "^0.25.0", "coveralls": "^3.0.2", "p-event": "^2.1.0", "nyc": "^12.0.2", "xo": "^0.22.0"}, "dependencies": {"defer-to-connect": "^1.0.1"}, "gitHead": "5b18e89af7cbd32195274d0375547234fac27832", "_id": "@szmarczak/http-timer@1.1.1", "_npmVersion": "6.4.0", "_nodeVersion": "10.9.0", "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "dist": {"integrity": "sha512-WljfOGkmSJe8SUkl+4TPvN2ec0dpUGVyfTBQLoXJUiILs+wBSc4Kvp2N3aAWE4VwwDSLGdmD3/bufS5BgZpVSQ==", "shasum": "6402258dfe467532b26649ef076b4d11f74fb612", "tarball": "https://registry.npmjs.org/@szmarczak/http-timer/-/http-timer-1.1.1.tgz", "fileCount": 4, "unpackedSize": 6076, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbiXZQCRA9TVsSAnZWagAA6MYP/R3uiABdxsOVFtxPBmZG\nutM19UpgKOsYhbbgmCtoVrIQacWyZBqrGdYm29Ckstdp6asHSZ1Z/j7kv3+G\nmyVNk1iJRZ9gj9koVYcHYvDXM4fv0balGKB7lhjWuHSSh7umUFKG2KnHURKN\n79zuZSJyO1/gyNJ9gdqoc6P1vN8TAUtX9rnZkoPRzlx9jxTgGiBDTCE62IMK\nufVUEMv+rO1brN59pqCt4Y4QIjInJq2skYAmTmDiPPbIy81iem0KWarUW/s3\n3yrQEsD9vAFkh8jjXEfs4BfaSGlajtSh8453S/r72ocksN/3AH9f1oA16S6U\nZ/DGdaxTdpQ0G6ZYVUCGo9kphv/FRo+fWryf9ZX1lsbfnLx+IEcSrUpTB6Nj\nvSR0pv073bwRG25WkRQtyhZw1Iwbx+1450Ns3xJLJsK3uInlTxAJv24FYZww\njDSZ+4xiT2aSrYLqKzQh/1zMh4fUzdt0Oe5isXrdnQ4LDtCwwGtuqyPouDFh\nllA70iKTtgQIbgpQz8vqqIIFEg+ZE5dawE91IA/fPjMd27ySJbdQmvleDwrw\n7ELnfg8wU46epszq+uzsAmZvJ5SDD9dGrxCqaH1EkM8zlyimaV7qanvseTAT\nTiW50fcbVdtu+Ks3R7uF+DVq2/DkNjLUd+Nt92RYOyeTIJjVu5p027oQcqDc\nVZkP\r\n=lT8z\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCQzMEkBRoGuobGPEkSXGDK4v5Z+goV0TwxiwelNl4RuwIhAJwyYaUWHWEvPijQkvPoFuiVls2GHSv5q8AimsCuBXvA"}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-timer_1.1.1_1535735375848_0.1109885778767421"}, "_hasShrinkwrap": false}, "1.1.2": {"name": "@szmarczak/http-timer", "version": "1.1.2", "description": "Timings for HTTP requests", "main": "source", "engines": {"node": ">=6"}, "scripts": {"test": "xo && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["http", "https", "timer", "timings"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http-timer.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http-timer/issues"}, "homepage": "https://github.com/szmarczak/http-timer#readme", "xo": {"rules": {"unicorn/filename-case": "camelCase"}}, "devDependencies": {"ava": "^0.25.0", "coveralls": "^3.0.2", "p-event": "^2.1.0", "nyc": "^12.0.2", "xo": "^0.22.0"}, "dependencies": {"defer-to-connect": "^1.0.1"}, "gitHead": "010f7732043933ec6309045f01473af3495591c4", "_id": "@szmarczak/http-timer@1.1.2", "_npmVersion": "6.5.0-next.0", "_nodeVersion": "11.6.0", "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "dist": {"integrity": "sha512-XIB2XbzHTN6ieIjfIMV9hlVcfPU26s2vafYWQcZHWXHOxiaRZYEDKEwdl129Zyg50+foYV2jCgtrqSA6qNuNSA==", "shasum": "b1665e2c461a2cd92f4c1bbf50d5454de0d4b421", "tarball": "https://registry.npmjs.org/@szmarczak/http-timer/-/http-timer-1.1.2.tgz", "fileCount": 4, "unpackedSize": 6296, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcL4whCRA9TVsSAnZWagAA5pgP/3DWndgx0zab0GQZ0r6z\nxahulxeCKn6PCn+v4S214bPpTO7rRnOiotJT46gx7aKx0G8rx5Vnaf3Isx/s\nJ3rdoO3qLw8ISl9CnRlqRHnr1xd7k+IKir5tAHIJTA0eFEZpaizaH56j/xDS\nLcTsmXRlGkDp8Xp7fRTN1dXseCdtDcOM7+4f3+18oviB3vW65Z1UFa1SrvJ4\nJOmgsn/Q8d9yIlmL6TpXpiHoymOx35Aqu/vMFnXMnRJQ1zHJd9XmS1EBfBl5\n4VWrB//a/c9IrYmt7z+xIms173qmVEA3TwRVjkaMfs/9baafKMdyI4ztUtsW\nnwf6aoYBMbf/06VPiU3L5rKIht9WesfBrk01vZDr3M9+Yjy294PAvvTSTJEw\nj4Yyp55HJ6NzLYO6MbS4k0+TNK4vexYI3ocRosaEZyqpye6gAr0Xo6mvmWDD\nI7q4oyaIicAiidgLkgrd46URKddKwvx9PoycyRxZS9MxjUte6X0XK2ODjYqI\n8BSgwMIhk87fSPTL7UeFQpD9vFm15YVhV+gyiPVFy43LtuevAVJsnZQ9EV2S\nV5M7TwSmiI1Ue0FH3sQIeeQtDu2pM0ewCQmNTHDfq/Fj63FAxWEFqPZgWxTF\nOtnFgQNTZX9YXQMpvgsvJWm8a59vo17ene7vXqeXNPHHLWcJaF5VhkugBk6L\n8Uy/\r\n=WwTF\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEvIG1rl0K2QJ36+bXlciUEvX/cUYgfP2hBw7mP9PK6EAiBaaIVviS1T/5GsUiEmHCWgImTZ4rgsAuvB8tdNFxSumg=="}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-timer_1.1.2_1546619937020_0.7812035537643258"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "@szmarczak/http-timer", "version": "2.0.0", "description": "Timings for HTTP requests", "main": "dist", "engines": {"node": ">=8"}, "scripts": {"test": "xo && nyc ava", "build": "del-cli dist && tsc", "prepublishOnly": "npm run build", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["http", "https", "timer", "timings"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http-timer.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http-timer/issues"}, "homepage": "https://github.com/szmarczak/http-timer#readme", "dependencies": {"defer-to-connect": "^1.0.2"}, "devDependencies": {"@sindresorhus/tsconfig": "^0.3.0", "@types/node": "^11.13.6", "@typescript-eslint/eslint-plugin": "^1.6.0", "ava": "^1.4.1", "coveralls": "^3.0.3", "del-cli": "^1.1.0", "eslint-config-xo-typescript": "^0.9.0", "nyc": "^14.0.0", "p-event": "^4.1.0", "ts-node": "^8.1.0", "typescript": "^3.4.4", "xo": "^0.24.0"}, "types": "dist", "xo": {"extends": "xo-typescript", "extensions": ["ts"], "rules": {"ava/no-ignored-test-files": "off"}}, "nyc": {"extension": [".ts"]}, "ava": {"babel": false, "compileEnhancements": false, "extensions": ["ts"], "require": ["ts-node/register"]}, "gitHead": "421d049829f706a52a92c287e404007a74ee47eb", "_id": "@szmarczak/http-timer@2.0.0", "_nodeVersion": "11.13.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-HpfsZs19JAfp0oEP5DnZU1tdQrEAjdQXKE5uV+pK/rBrOk8oKB9TPKthSr+SM3LrF3EhH8FROfgy9ggDNNcDFA==", "shasum": "445091dd074dfd9360b4e26ef9b860c4500027f3", "tarball": "https://registry.npmjs.org/@szmarczak/http-timer/-/http-timer-2.0.0.tgz", "fileCount": 6, "unpackedSize": 11284, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcu2+0CRA9TVsSAnZWagAA414P+gP256njW44wWr1a2t7M\nnApY74ayprjXFFOleSp05Tf4KunxzP9OcfVo3Hnd82RLOq0qzLuQmx+i84ng\nEGKyEEChiyVMIdSVXVGYnm/LahBr5Cg9yuGYb27gnvx+ns7d3c5uJMR1TV+t\n/oWpxaJq41uy6g3/Ye4qqQ0D4a4ffL3rOlIDJffrs77c1bH/jdu+JeSSxG2H\nGE38WNAxfCfjIk2DZ7ywv/DHOEmj6kf+Xf/QXtKJ9iiIReccKL2ScnvM0QCL\nE+27ssfZNdPJVLicXyfnJ+OzXELfSRqXgMVYQqaMEUHFs0aPQSCtcFXUVcqx\n2IPr0zWkbnmy0uHU4KSoTDVg2hE/dD2fxH6xo8uMXocutYB9eemCSSaZWNun\nUJrGf8dQxxqAiVifaoVmDD70lXxpwmX/WGifOTJk9AwnQ3KKGtWYT64Arxwl\nWxkepJiAgltRG1LpUVi7ypJ4+K1t/TioRt3V0goJnmbNjZPy8BA8V+h2JU0L\nZfXYfpiQTe2L0AAclQGv/hwudbI1bupRFCEF/ML3Ktvc7H0NqfTS0yeNaR9e\nHKfG5CuG1ry6J9ElvTDCasHnP6oYnSb9qGDvoAi2lBEPJEVJKy7gc1rHD1oz\n8HAf9qRTI3U1CJFU4G7DWWxeRh7PwTHwShnhdMzKF1cT17rsnAgJU4Dz4Wh7\nzPAw\r\n=DcJ/\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCJ3lwNlCHePYNgezt8guXaKKDedLfWiHPhCzpQX7suIAIhAP4iqCxGFLZX6U/FFlYaNHJMuvzqH+k+iwoi3cKZFUga"}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-timer_2.0.0_1555787699708_0.5242984846847887"}, "_hasShrinkwrap": false}, "2.1.0": {"name": "@szmarczak/http-timer", "version": "2.1.0", "description": "Timings for HTTP requests", "main": "dist", "engines": {"node": ">=8"}, "scripts": {"test": "xo && nyc ava", "build": "del-cli dist && tsc", "prepublishOnly": "npm run build", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["http", "https", "timer", "timings"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http-timer.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http-timer/issues"}, "homepage": "https://github.com/szmarczak/http-timer#readme", "dependencies": {"defer-to-connect": "^1.0.2"}, "devDependencies": {"@sindresorhus/tsconfig": "^0.3.0", "@types/node": "^11.13.6", "@typescript-eslint/eslint-plugin": "^1.11.0", "@typescript-eslint/parser": "^1.11.0", "ava": "^2.1.0", "coveralls": "^3.0.3", "del-cli": "^1.1.0", "eslint-config-xo-typescript": "^0.15.0", "nyc": "^14.0.0", "p-event": "^4.1.0", "ts-node": "^8.1.0", "typescript": "^3.4.4", "xo": "^0.24.0"}, "types": "dist", "xo": {"extends": "xo-typescript", "extensions": ["ts"], "rules": {"ava/no-ignored-test-files": "off"}}, "nyc": {"extension": [".ts"]}, "ava": {"babel": false, "compileEnhancements": false, "extensions": ["ts"], "require": ["ts-node/register"]}, "gitHead": "f1b7bc949f355c8f98163babbe3e95bb575528a0", "_id": "@szmarczak/http-timer@2.1.0", "_nodeVersion": "12.10.0", "_npmVersion": "6.11.3", "dist": {"integrity": "sha512-9kQylMSSjAwabpdXjpaiO1hOwf6gtV/OldZN8wiZn0HAUacMSQNe+k/tkeehGAylsvPV9vDviJmaiBa/GAqF1g==", "shasum": "cb5775bd35560bf6d4fe476d29fcc29a99749dec", "tarball": "https://registry.npmjs.org/@szmarczak/http-timer/-/http-timer-2.1.0.tgz", "fileCount": 6, "unpackedSize": 10908, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdvFVfCRA9TVsSAnZWagAAZuMQAJZssn42bHojvu7QVA8z\nZpge14f6stpOeWkdmIpxmqanjMQMZy293+lw1Zlo8wY/N0r8bBmaca7II7L+\naAN8Bm3UOMTZktMJlAEUamnEzSjiT6h4qpHjmyhrQ8bvDSjWhn5MQrSo/Yy/\nwpAYBNPN9iwCft2FDQbuqp9J3k82dWF5q1USMj+4i/8cpBZfhz8f/UmmM/kL\noolBODZOA7RsglWH+7zp1W6tbnQLIoXO9rwPj7gXFMsqyV6hA7eBD6O/6OlL\njy32WRHA+WzU1zpILgX9KWadoFZe7CiH1dz9FtUkhgaG+11crSrFGYNTQ10C\n2xH0UADKLJ4lYVsnWBmrRMTAKYuPfUFn3WvWw5zX0SI24IsrQetuVJSy0Eue\n6Xe0UTz+QOZMx7ZeB/lC247MryjgKnrMM1uohZvNd22NoqDse8Uk2XLLFQoY\nNIsb93ERRiA8xIaCks51rY31NRPgGiUWyaXYoSTBLzd/oIG7i301ULZHkTf8\nqgZwamAruTeZrDGeRDCn57VmSXto5kiqKdVN71BwyAWiok+aNrKbDZmcHADK\nUXi/Vjnzis6dSAIaoEt1w3A6lufwzIoXT2dVyR/TnYcrZPw/BX3oLhVvLOUI\nhAqykoGXEyiGS2gaMJPqZ5sJI1Vik97R1ghA8ZLf8C3mY1iv/1/t2Y7bYxXs\nxI+m\r\n=nZn+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCiBUCwlAiqWKbEKt1/ku+wimWYTwqHs4yzY5ogTwGHUgIhAKmzjK0iq9s1wEmvjxccQMontOJp2n7Fg/+fJbIQiaGa"}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-timer_2.1.0_1572623711163_0.5227505964213726"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "@szmarczak/http-timer", "version": "3.0.0", "description": "Timings for HTTP requests", "main": "dist", "engines": {"node": ">=10"}, "scripts": {"test": "xo && nyc ava", "build": "del-cli dist && tsc", "prepublishOnly": "npm run build", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["http", "https", "timer", "timings"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http-timer.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http-timer/issues"}, "homepage": "https://github.com/szmarczak/http-timer#readme", "dependencies": {"defer-to-connect": "^1.1.0"}, "devDependencies": {"@sindresorhus/tsconfig": "^0.3.0", "@types/node": "^11.13.6", "@typescript-eslint/eslint-plugin": "^1.11.0", "@typescript-eslint/parser": "^1.11.0", "ava": "^2.1.0", "coveralls": "^3.0.3", "del-cli": "^1.1.0", "eslint-config-xo-typescript": "^0.15.0", "nyc": "^14.0.0", "p-event": "^4.1.0", "ts-node": "^8.1.0", "typescript": "^3.4.4", "xo": "^0.24.0"}, "types": "dist", "xo": {"extends": "xo-typescript", "extensions": ["ts"], "rules": {"ava/no-ignored-test-files": "off"}}, "nyc": {"extension": [".ts"]}, "ava": {"babel": false, "compileEnhancements": false, "extensions": ["ts"], "require": ["ts-node/register"]}, "gitHead": "9582de3388283ac1aa5d8affa51aa1178f1bf563", "_id": "@szmarczak/http-timer@3.0.0", "_nodeVersion": "12.10.0", "_npmVersion": "6.11.3", "dist": {"integrity": "sha512-6RcMc4PnnbgOzgmtyKsZuOP4XTLOQAy0sNI9LfbjJt80jw+/7nMu54tke/ysxwQN+AsGsy488vS0UIaR++lDFQ==", "shasum": "d774300886281b88c40a1fcbffb4abdbc61585d8", "tarball": "https://registry.npmjs.org/@szmarczak/http-timer/-/http-timer-3.0.0.tgz", "fileCount": 6, "unpackedSize": 12117, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdvbI7CRA9TVsSAnZWagAAatgP/2LV6v1IVJy8GdCPbhGB\nedLSm4Owh+F39hKOWtve9b+mwrWF7E5Q2lm7cfku5ADzKyU7cvl5y/JKufOz\nlL+7sRhPGk2+a/tLkZkTy8cwh9I9H2p3Y6BZxYzfk0qaeeYeCVbp/WaBC8b0\nqJefN+PKy8BDGBw+rQ6hVDwL0eQUoOD0RYClP97kHyIqumWHyKThNIlDUShZ\nFLCtituMAQOM/FV6y5atxvsXCf94FCZk4DppVvRieqSc9dQYu4luaZMDLEBi\nqqq6h2V6eiNkfJHAkLOqDbzMt3P1grMA/TlMZu7OK/gKqaFGoxplSbw5jQyP\n/EHsXKwDmmn6Z3MEq1PLJn8e5uieVpQh86ZSwKvk5ga+ncP/iSpFVBV5WLPk\n+kpd7lUKicNO0S1B2yZNH5irlOUXPd+icIZ71/htMsFAVfAIVPdZj8EWCnvt\n+/bdEM1FY/FZSQOh+nCx15+B6aq4QxGIckhCCv4p0E3XyyByx1sQobHpgV9q\n3hNyKW7A0qm2IkrSLEKI9r15yw6QPYclJ11lEaAb+opHzgmjzRgG2Qt0WUVd\nexwpMOTFu+jmNc2rib4fwcwweC7Xy6Ofj3QGCim1FUkMakzh6eV9tWe0BO3i\nprLy0EQmR2A5K7Ohnopz+zEJVPeXx1fWN/syA6z/O4cHHyuKXU/0yulXw5P9\nya6H\r\n=rdOK\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGMB5jNjXkkzfnpjBKlAErwgWhzk7H6KsZP38lUqyXD2AiEA4bPiFdvNlYaAMt8Ezzp2fJ+u8FvtcCKYnrinl1qzbiU="}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-timer_3.0.0_1572713019096_0.8477653922275252"}, "_hasShrinkwrap": false}, "3.1.0": {"name": "@szmarczak/http-timer", "version": "3.1.0", "description": "Timings for HTTP requests", "main": "dist", "engines": {"node": ">=10"}, "scripts": {"test": "xo && nyc ava", "build": "del-cli dist && tsc", "prepublishOnly": "npm run build", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["http", "https", "timer", "timings"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http-timer.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http-timer/issues"}, "homepage": "https://github.com/szmarczak/http-timer#readme", "dependencies": {"defer-to-connect": "^1.1.0"}, "devDependencies": {"@sindresorhus/tsconfig": "^0.3.0", "@types/node": "^11.13.6", "@typescript-eslint/eslint-plugin": "^1.11.0", "@typescript-eslint/parser": "^1.11.0", "ava": "^2.1.0", "coveralls": "^3.0.3", "del-cli": "^1.1.0", "eslint-config-xo-typescript": "^0.15.0", "nyc": "^14.0.0", "p-event": "^4.1.0", "ts-node": "^8.1.0", "typescript": "^3.4.4", "xo": "^0.24.0"}, "types": "dist", "xo": {"extends": "xo-typescript", "extensions": ["ts"], "rules": {"ava/no-ignored-test-files": "off"}}, "nyc": {"extension": [".ts"]}, "ava": {"babel": false, "compileEnhancements": false, "extensions": ["ts"], "require": ["ts-node/register"]}, "gitHead": "89f1a9badb72c3a07a5aaa44fc560e71e3384826", "_id": "@szmarczak/http-timer@3.1.0", "_nodeVersion": "12.10.0", "_npmVersion": "6.11.3", "dist": {"integrity": "sha512-tLQ8OCB5hIzQlv/YefIhCkw6TaIo/bepVvkEV9cPTLZZ8WDCXELe4Elf4ikH5/KDErH1M0VEltFV3naBmq20dQ==", "shasum": "b1b207a2b3bfdf362642623fc2ecbee5ce22f534", "tarball": "https://registry.npmjs.org/@szmarczak/http-timer/-/http-timer-3.1.0.tgz", "fileCount": 6, "unpackedSize": 12876, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdzwoBCRA9TVsSAnZWagAAVEsQAJzZc7rh7lF/xxBF/nUj\ngtRxwVQ4+PTkzt8rlzcaC7038+F/n/5GVCM/ndLKJbmJ7lYI4qFUMNAQk2X4\ndhfymSZQi2zQvIxooQuR6p3ZlPY7Q8HA4Kh6ghBejwmVnJNdJ+W+gvLPszms\n+s9JfWKDoF8QOXnjPhJ6ab9rWwpqkMl93EUNcVKyXzIEnnQhb5rE1gxQRT5L\nVEs922/AkYqBASqNr7RK0tCQWt29cmY3z6CfDWN0+CjerOjqEsNauKoKuNsE\negq6WoxQDeEQYmEw5q7A4Olrrsim40N713JiqNpB8Qb3JjY+i+7qEiuWiY4h\n5BzA8x8REbuB/9hHr8t5pw05tQh9927ymR5tjAZzfvelYdmwzUhxtlyV4+aY\nwGDcgmsQwtgeFxQBP6AKlGQnvlysFxlHAzZOx2383+ljq9H0k2lolBSc+4n0\nIvfZs1zLI2ZCLtQDNbWY6QPWypn3zm+dNfmK6iNtN9SlqqEJXTMCNz6rtwAz\nI5AUXMOxXQOtlWuJcTV58BDHBZzpA/GRxo9nnqOc4iWQzunDzlf2NPZwRJyh\nygtEqF35dNK2zYylFaBnYm6Pwi7C8M9TqO8MmIiA4HCFfyEtX4LyqsK0R21C\nRdf7Pglej0SCfzjgTNhKWIyEfhLByNIbISxzGHUS2smUuGcgTqMpiHOcXAUu\nSakt\r\n=shz6\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDpW92CI6+Ogr3BNrzYIA90gtzUbo28iEonrj/AhvcxdwIhAPhc3Me3zkSjOsz/ujwQTMNdQqPbHfZjZbKZAWYg6q9y"}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-timer_3.1.0_1573849600755_0.6241626495217634"}, "_hasShrinkwrap": false}, "3.1.1": {"name": "@szmarczak/http-timer", "version": "3.1.1", "description": "Timings for HTTP requests", "main": "dist", "engines": {"node": ">=10"}, "scripts": {"test": "xo && nyc ava", "build": "del-cli dist && tsc", "prepublishOnly": "npm run build", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["http", "https", "timer", "timings"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http-timer.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http-timer/issues"}, "homepage": "https://github.com/szmarczak/http-timer#readme", "dependencies": {"defer-to-connect": "^1.1.1"}, "devDependencies": {"@sindresorhus/tsconfig": "^0.3.0", "@types/node": "^11.13.6", "@typescript-eslint/eslint-plugin": "^1.11.0", "@typescript-eslint/parser": "^1.11.0", "ava": "^2.1.0", "coveralls": "^3.0.3", "del-cli": "^1.1.0", "eslint-config-xo-typescript": "^0.15.0", "nyc": "^14.0.0", "p-event": "^4.1.0", "ts-node": "^8.1.0", "typescript": "^3.4.4", "xo": "^0.24.0"}, "types": "dist", "xo": {"extends": "xo-typescript", "extensions": ["ts"], "rules": {"ava/no-ignored-test-files": "off"}}, "nyc": {"extension": [".ts"]}, "ava": {"babel": false, "compileEnhancements": false, "extensions": ["ts"], "require": ["ts-node/register"]}, "gitHead": "56584824a3a9542a7de0c2581fd5b08484567107", "_id": "@szmarczak/http-timer@3.1.1", "_nodeVersion": "13.0.1", "_npmVersion": "6.12.0", "dist": {"integrity": "sha512-F7vS53bV9NXT+mmYFeSBr2nXaOI1h6qxdlLDVP+4CPG/c60MMStT7aaqYD2TSNWob1DA3GH9ikFY0UW31bUsWA==", "shasum": "8b876acd14a4f36ad274468910ee858241e356ad", "tarball": "https://registry.npmjs.org/@szmarczak/http-timer/-/http-timer-3.1.1.tgz", "fileCount": 6, "unpackedSize": 12876, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd57VACRA9TVsSAnZWagAAbeoP/172vxetsjkw6Z9n5Jhw\no0mm0Adb04KYe3yd7jWNYCtFk34sHuJzc8/HS2/4eeWCeEpiC0KU1J/3/4sE\njmceNT+3cRMMfnVNo+amgVzHA2vzyN9YS+u9WC6VM1/qSUltgh/qZlT1oai0\nfNIO+KoV2A5HyrdzRVPWoGRiPr5Wsogya3mXYgwDVjt4wBeuoByNzccQHmgj\nRW+fJfdhx67f5sZMr5GPhSKQgbhAVWtej5ug+L3EG2YOQwkwj59clSUe2avw\n3AnfvJhx9RioTkZHeuzIMw05FGh7m11fIK8YCNoQQ/W2kE2v16eAuUce2XVp\nhxl7+KPWB7eNr08ZoQ03WA3AG+shfVYEso3hZrF18pcVH91G+s4AtNG4smr8\n+uHeSFg4lUvQTX1d0IRWZ+sN9aBL6FDQTRBqHZHjm1bvEP4Ii/mmJ3TBI3w6\nYFN4ESDRL5XDTYJwuiab2nvFCYI1N68BkkjVUTtmp/x1IU5IR+EHSYoz0ZOm\nzNb3Kg1UVQmzKtcplI1i2AeEwyl8+13sOb8qJU2KX8vdR0Rwh2VE8cH1DBvw\nr6eP9ApATCHsWS9kfQMDLc6G0UEPfoG68EqokqA0GRPkeX0ZFkf3mKT3ANAn\nKrk/QMAeO+Anb2JfueustnKtUhSLJN2ZJF4TEwaieTgxX97WaAX2BRFfDF+t\ns5sA\r\n=Pm5l\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCHwPvbmDJh7WlW1NKlYl8WSNu8LJRAjbFFaz+c/T0N6gIgDOHKMJwGyNCohupQhtDbo4+JtyROWQGPbgNRCHbZHEQ="}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-timer_3.1.1_1575466304037_0.9578035734468986"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "@szmarczak/http-timer", "version": "4.0.0", "description": "Timings for HTTP requests", "main": "dist", "engines": {"node": ">=10"}, "scripts": {"test": "xo && nyc ava", "build": "del-cli dist && tsc", "prepublishOnly": "npm run build", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["http", "https", "timer", "timings"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http-timer.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http-timer/issues"}, "homepage": "https://github.com/szmarczak/http-timer#readme", "dependencies": {"defer-to-connect": "^1.1.1"}, "devDependencies": {"@sindresorhus/tsconfig": "^0.3.0", "@types/node": "^11.13.6", "@typescript-eslint/eslint-plugin": "^1.11.0", "@typescript-eslint/parser": "^1.11.0", "ava": "^2.1.0", "coveralls": "^3.0.3", "del-cli": "^1.1.0", "eslint-config-xo-typescript": "^0.15.0", "nyc": "^14.0.0", "p-event": "^4.1.0", "ts-node": "^8.1.0", "typescript": "^3.4.4", "xo": "^0.24.0"}, "types": "dist", "xo": {"extends": "xo-typescript", "extensions": ["ts"], "rules": {"ava/no-ignored-test-files": "off"}}, "nyc": {"extension": [".ts"]}, "ava": {"babel": false, "compileEnhancements": false, "extensions": ["ts"], "require": ["ts-node/register"]}, "gitHead": "1c3ad72dec966435cdb6a4714a8a201231ddc8c6", "_id": "@szmarczak/http-timer@4.0.0", "_nodeVersion": "13.5.0", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-3yoXv8OtGr/r3R5gaWWNQ3VUoQ5G3Gmo8DXX95V14ZVvE2b7Pj6Ide9uIDON8ym4D/ItyfL9ejohYUPqOyvRXw==", "shasum": "309789ccb7842ff1e41848cf43da587f78068836", "tarball": "https://registry.npmjs.org/@szmarczak/http-timer/-/http-timer-4.0.0.tgz", "fileCount": 6, "unpackedSize": 12944, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeBjmPCRA9TVsSAnZWagAAXUMP/iGTYU5FhhU+NBCXxlcJ\nqH404vI4UZz25Df9Sp0+2ZFNn/ydOYXOUZ2VjTruhOzuxrjTaVgouIenw456\nSsysuGirIZeCmIx8PnoEgHNIq6CbUp7mniDs+KuwKH82z9FEGUtrLJIq3MZM\nevxjzRcy8gdJh7FifPIzuC97Vj2bcHnAwLLpW6eZKhNBnEt0qrnPmam0tYrz\nGC7pTK+xyPjGyr+CtxQzUadDYTUH1BhRCHUx0olRf2lpUYayxegqwmzLHiM2\nJxcEPjYpqhe50bo5cVEjJlTjPM7ApdlY6iLgDw0GCB1BDssLRkvSGzVde7Rn\njIb9rq0FsnZQ2D+OMIrbj30REBdeLMC0pOLiL6t/tdbgvyf78YhHdwxB4hpG\nZBM2jHdKYTyPkW7JTGWVfJjCztYrFTd/xbiNokYGpgo1zNP/eHjC2HuaVddd\nPEhXQhYUvIVN576bJZh7ANxkEU/No8lSYW3E/aMALVNh1h+v/juqAqcj6jeT\nc5P6nyy+h2XAvP2fkMClLu/Kfeef6zVmwa+4L202QkAxVlggC5HTdCM05+av\npVgRAZ5+pAADgBsEI+DFYxWP0AmbKG+CA8WD/C8cNh7LKxtnX21kjXDN5Xgf\nI9qmTMxewargkjcDiPmF1MogS4Fs9CWlIcN8ZKqXXeklZSziTcZxOftTOk53\n8sNA\r\n=hllJ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCzx2i1Ky1mBsGY7VS049nE+3N5eFa168MFe1mXc0T0NgIgRZUfARsd96nqwdJ6F+DfeaQb0eLoknIOIXnEONPo7oU="}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-timer_4.0.0_1577466255156_0.04292146812442388"}, "_hasShrinkwrap": false}, "4.0.1": {"name": "@szmarczak/http-timer", "version": "4.0.1", "description": "Timings for HTTP requests", "main": "dist/source", "engines": {"node": ">=10"}, "scripts": {"test": "xo && tsc --noEmit && nyc ava", "build": "del-cli dist && tsc", "prepare": "npm run build", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["http", "https", "timer", "timings"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http-timer.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http-timer/issues"}, "homepage": "https://github.com/szmarczak/http-timer#readme", "dependencies": {"defer-to-connect": "^2.0.0"}, "devDependencies": {"@ava/typescript": "^1.1.0", "@sindresorhus/tsconfig": "^0.7.0", "@types/node": "^13.5.1", "@typescript-eslint/eslint-plugin": "^2.18.0", "@typescript-eslint/parser": "^2.18.0", "ava": "^3.2.0", "coveralls": "^3.0.9", "del-cli": "^3.0.0", "eslint-config-xo-typescript": "^0.24.1", "nyc": "^15.0.0", "p-event": "^4.1.0", "typescript": "^3.7.5", "xo": "^0.25.3"}, "types": "dist/source", "xo": {"extends": "xo-typescript", "extensions": ["ts"]}, "nyc": {"extension": [".ts"]}, "ava": {"typescript": {"rewritePaths": {"tests/": "dist/tests/"}}}, "gitHead": "57d0e83b0a432c5756e9c428640fce800130a81d", "_id": "@szmarczak/http-timer@4.0.1", "_nodeVersion": "13.7.0", "_npmVersion": "6.13.6", "dist": {"integrity": "sha512-Q4/Ej0p3fTHY1paYvJDi9ROkoNHTjii3fKdrLDOlfMkYowoC5Jn+HgAzVTXxH0XG/dFV6yWU1xaNMC1bxLBWfQ==", "shasum": "3dab3f5a85874795237b39ce8003cffce061fa1d", "tarball": "https://registry.npmjs.org/@szmarczak/http-timer/-/http-timer-4.0.1.tgz", "fileCount": 5, "unpackedSize": 9655, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeOEpOCRA9TVsSAnZWagAADikP/3SeTUoq2bHgm9S235ip\nWbs6c31g+hOnQB/jiWLrX2BRGZIehvdwH39fYk9wJ/rAcKxdbgWaHarchfMB\nOc5R/swXB3FF0Az04KYViJHjUH28VBVjRnpE7CzSa3YMpGVKDvB0GxIfG4N2\n+5Ra5lYqrwqQWjRyGh/onIESMHzyM0xHppQ79TjmhXAt4ThecvTjN/ss9RQJ\nLzdnlyXzC97uiFQms54JPW2xJ/R12tUQAzxuHR1ou2QkMObXiURacA+63IG/\nvy2ZdK7uSGO6Ioclx7EoT6cTKCpQe8SoF4KRgEDNxHtSU6uBy3iLI95QZgNh\n8EoDz1wIwSZPwdtiePY5lFRdUVASWyHStsdIEE2qwCVtFLIZz5IWhQPwCBCs\nWqJ0dQVDbj/v7t8LVdSygLTp6EwEBHzBkx294Be515e5Hl5I+EzdxZv9KGER\nbgKgL5S+DtmDSFQkIo1GniGd3AXyqQVNiseSN5GBJQ7JYOXO2M/YwfVKl5Ch\nbb1ZZBR0cF1d166fwVTjIaCwz0lbJozHOlWyuY30GEFM5DxDkL9gUX322Ml6\nnZjU8DUWkDKV/YX8RMS7nS0vJ2XL8nyaICGpPhwny9Bhp4othTSuYLThVmNx\nV3xbBpNAp3t/wHAGC36VwSO0xrIRC97utXTHiZLPFDzjzlRImfST4F/r6x2X\nNJC9\r\n=ZQ5X\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCHTHXfuUOQlHO22XvzV3pa3aY9yJVQORN8VcXGbQKH3gIhAMOT9Ows9fqdMUK6TxvK7N8jx1pW/bKp1A8WQtQevzAG"}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-timer_4.0.1_1580747341778_0.8542214302814568"}, "_hasShrinkwrap": false}, "4.0.2": {"name": "@szmarczak/http-timer", "version": "4.0.2", "description": "Timings for HTTP requests", "main": "dist/source", "engines": {"node": ">=10"}, "scripts": {"test": "xo && tsc --noEmit && nyc ava", "build": "del-cli dist && tsc", "prepare": "npm run build", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["http", "https", "timer", "timings"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http-timer.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http-timer/issues"}, "homepage": "https://github.com/szmarczak/http-timer#readme", "dependencies": {"defer-to-connect": "^2.0.0"}, "devDependencies": {"@ava/typescript": "^1.1.0", "@sindresorhus/tsconfig": "^0.7.0", "@types/node": "^13.5.1", "@typescript-eslint/eslint-plugin": "^2.18.0", "@typescript-eslint/parser": "^2.18.0", "ava": "^3.2.0", "coveralls": "^3.0.9", "del-cli": "^3.0.0", "eslint-config-xo-typescript": "^0.24.1", "nyc": "^15.0.0", "p-event": "^4.1.0", "typescript": "^3.7.5", "xo": "^0.25.3"}, "types": "dist/source", "xo": {"extends": "xo-typescript", "extensions": ["ts"]}, "nyc": {"extension": [".ts"]}, "ava": {"typescript": {"rewritePaths": {"tests/": "dist/tests/"}}}, "gitHead": "c43fb21183c1f7e74f15f545a8344eda5978e082", "_id": "@szmarczak/http-timer@4.0.2", "_nodeVersion": "13.7.0", "_npmVersion": "6.13.6", "dist": {"integrity": "sha512-Qo4BTFIKQJoX3ED9U/RYZSkwq1CmBY7phf8gMD+Dxv9QHfFiN+y2Ig88eH/Y2S7mKEUTHsEI/R0VZuecsru99g==", "shasum": "2aab809ee1648f3659cb6e0da9a5b7bf5161d149", "tarball": "https://registry.npmjs.org/@szmarczak/http-timer/-/http-timer-4.0.2.tgz", "fileCount": 5, "unpackedSize": 9655, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeOEtSCRA9TVsSAnZWagAAVUUP/06LMIobLIZCHvRtsKKY\nRFljcQ3xTEqVe/ZGGj7IVuXdMVyPZ+OpKfG4nl8SzbrzsLs0ghbnCSxswN/N\nD7+T08DpedlCqFacdAuCc0Hja7wCyTO+mV56a3FE2LN6FF6I2UFfEebudwHK\nPmComxYAXKUMbwAssztXhuafbU6m5g23fLyo5jb0DluEM5cpF5XXcXuY/vOL\nRE+Vr0YPkihF79mX5Ig62Z32vdN6xpqVoO8Dn+Qe0D9f9CJd6zOP1AuXdueT\nH4woaeXfNVRR6DrRJPVnznDlNVpbWoCo4yCC4qVFXNrc87yvttsZc7GHFD0S\n/YB5tJdupI50Ccj4ev1BOYhHrmD46eGV/j7ll51oMj+6uXmsf6NUeK0DAs7/\n/zYvnjZZIOpbahBZvzpJp8RwTYJMkvifzvonRLX+QCmQvuAazGnkLY0a6Q1I\nEdY6FKi6fPhMADf5xwwsuupu2yMg4FhhRzFtigKMOFOe7DJyhnXpBCaNyanC\n72J1ATBWplTgFlp+Dakrxb+MOi4Ttp+6vZXm5EehyTtIblo5aXNf7tmMWDGr\nTL0VOfMzijwnjiPt2BY5IkAQXw/JgzqhyBjPc9VVa6aGXbua94vUF+4FiGt4\nKM/BnZohSp6PQCUMX7P7tLlscsm/q7tlmCc/cFYWs2LFkfx9UkeV5Y+WzonQ\n+0rV\r\n=tgYY\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICuccft/1iov4Hu4gPt+5gMl653yJ4rUxQVcw+4txbhsAiEAoY78OUcksa1Lc2QMizASHoxMcJO+5GY6DFzfvLp9+6U="}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-timer_4.0.2_1580747602480_0.40754113877996234"}, "_hasShrinkwrap": false}, "4.0.3": {"name": "@szmarczak/http-timer", "version": "4.0.3", "description": "Timings for HTTP requests", "main": "dist/source", "engines": {"node": ">=10"}, "scripts": {"test": "xo && tsc --noEmit && nyc ava", "build": "del-cli dist && tsc", "prepare": "npm run build", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["http", "https", "timer", "timings"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http-timer.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http-timer/issues"}, "homepage": "https://github.com/szmarczak/http-timer#readme", "dependencies": {"defer-to-connect": "^2.0.0"}, "devDependencies": {"@ava/typescript": "^1.1.0", "@sindresorhus/tsconfig": "^0.7.0", "@types/node": "^13.5.1", "@typescript-eslint/eslint-plugin": "^2.18.0", "@typescript-eslint/parser": "^2.18.0", "ava": "^3.2.0", "coveralls": "^3.0.9", "del-cli": "^3.0.0", "eslint-config-xo-typescript": "^0.24.1", "nyc": "^15.0.0", "p-event": "^4.1.0", "typescript": "^3.7.5", "xo": "^0.25.3"}, "types": "dist/source", "xo": {"extends": "xo-typescript", "extensions": ["ts"]}, "nyc": {"extension": [".ts"]}, "ava": {"typescript": {"rewritePaths": {"tests/": "dist/tests/"}}}, "gitHead": "65de996d48e5e2b83508efd6491685c931d9a465", "_id": "@szmarczak/http-timer@4.0.3", "_nodeVersion": "10.18.1", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-VSKX5i4dZX5ZO0MhTUAYlC5in9ltTdn/syTB8Z+dS0bSIaeaNWWkwoSHrbDm9rjjjIdYlXqB3RArOj9x+COTzQ==", "shasum": "e77cf501575deab9c99d307fa4dbff1432567d09", "tarball": "https://registry.npmjs.org/@szmarczak/http-timer/-/http-timer-4.0.3.tgz", "fileCount": 5, "unpackedSize": 9998, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeOFFpCRA9TVsSAnZWagAATScP/3Tqb+xaCygKf53bxoHf\nlSWaaocuUs3I113bngxRlLwpXSl02i9lny63vzvMqPI/GYKkDIAw+RqeQ5ax\npJbHei0yG63eKdPPN+niWmdtV4XeW50HPTN/p4Kj1PZUIcEGg3OOZWg5s3cP\n/2CxC/jDNiS6PLDhvnx5g+hxc3uXy/zbudOkNSlxcVmuNB7qQYkStcVDNv8p\nfdYGrJywfL9Lwpe1hkJj7iXLs7cTn9jrLMPPPGkueBOnVo+yd2PQzO3rLxw+\n4u0aVYxEpzowmDXxoqT24WJNRw+whGsPsYCLYtdyG1m3OubGjc6StwTkI4DH\n2ZYIteBlr6yi9vE/Gk7lYAVAJakjiAfoNqpuUDZHPCi5qmstSLkSLENzNr5E\nt9vHz1mfzH/kgzR3g07+jLkrBnh6EAZ13WPBiHOb2cakZ/tPY91rEBPg7C0O\nm+QRNLKMHQkNhitn2vUnRasouycKLYBDdO/bXqAeKmimjdhTiEALBzGnyEGO\njnI9FpNey4J+vzyQv9L5hc2TazgQK0KT847GbxqBpiBmkhBcW4f7MRyGoSCt\ndwrPnf9A3aIInDqho8S1dRRD53KLaJWkwL7EaykA/MkJ1BetjpEjajesxkiB\n4CojGiy8KT0RjTREbci6nrg6jM8lJCkxa9EAS1HD5LZ9Cbosm5UQ4YyV+1te\n7sCE\r\n=XHU8\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD9uJa1zrugnX/N3GqZdf+7p2takLKR8JGMxbO3CdMu9AIgK+hXr8JtmfAG4SCTmp9jgnjK0+12y5EWLRDw2wqC2YQ="}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-timer_4.0.3_1580749161208_0.6604497917763361"}, "_hasShrinkwrap": false}, "4.0.4": {"name": "@szmarczak/http-timer", "version": "4.0.4", "description": "Timings for HTTP requests", "main": "dist/source", "engines": {"node": ">=10"}, "scripts": {"test": "xo && tsc --noEmit && nyc ava", "build": "del-cli dist && tsc", "prepare": "npm run build", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["http", "https", "timer", "timings"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http-timer.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http-timer/issues"}, "homepage": "https://github.com/szmarczak/http-timer#readme", "dependencies": {"defer-to-connect": "^2.0.0"}, "devDependencies": {"@ava/typescript": "^1.1.0", "@sindresorhus/tsconfig": "^0.7.0", "@types/node": "^13.5.1", "@typescript-eslint/eslint-plugin": "^2.18.0", "@typescript-eslint/parser": "^2.18.0", "ava": "^3.2.0", "coveralls": "^3.0.9", "del-cli": "^3.0.0", "eslint-config-xo-typescript": "^0.24.1", "nyc": "^15.0.0", "p-event": "^4.1.0", "typescript": "^3.7.5", "xo": "^0.25.3"}, "types": "dist/source", "xo": {"extends": "xo-typescript", "extensions": ["ts"]}, "nyc": {"extension": [".ts"]}, "ava": {"typescript": {"rewritePaths": {"tests/": "dist/tests/"}}}, "gitHead": "d27f8dacc9a6210454d720509805241761a74c37", "_id": "@szmarczak/http-timer@4.0.4", "_nodeVersion": "12.14.1", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-P3F6Dgeshq/sw52NTBuunTiYcd7FdgR6mhWgpmfrA9dHIS8Fq3Vl7ZikiEzYlC5zKkvKgrvDEZf7svfccSUnpw==", "shasum": "d493cdf163b23cde731c15755e87bff7f1d33ffd", "tarball": "https://registry.npmjs.org/@szmarczak/http-timer/-/http-timer-4.0.4.tgz", "fileCount": 5, "unpackedSize": 10128, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeOHPuCRA9TVsSAnZWagAAZtAP/19Zie+CRDPX026PuDCt\nzbHMo8zdpYcmUVhIetIuX5dIaoxnTJZ5CLdOlE+VviMoykmQUPZrdLoYYESc\nv1UGNJwZN9f3ZbGFdJXz7M+MDjs25dq1kTYsAHBgRiFBAUN3O8rOpkxtHyot\nD10D/iiKeBwIFI7wZYP0PIFwvTEtT/hwl+DJb2l8UlIcl9JaR6LdL1XV0F+N\neizFk7Hd60lXDm1++CPT+Z/JVvNA1wjScV61eZaz7D/a4JKZAjYvssRDJ11g\nNNOevHiC0qTKk1vHZXzK3MkEiFYnYdotJ05VO2k6zEORUaGmu3WwcPhzU0OT\nYi/qYFlgkNPj1GK7IekesHtMC5TBmAoPbUldZChxXycPH2D0e+dRyyA5cGOl\nNEnKV9GX3pzdQvjcD1J5IPIK5TzP6OUNX54FJlATa9uGtEoaVZVLEcleqHBz\nkKMMAGn9QzpuOVwOauIJx56ZF5b4TmZW8iIMvJErWnQTCjGw/2i2rBJh0Daf\n9V4HfVIGL5nYAET3hzLvV9Rlc7wZRcgqJRFG9Qjn1d74b/bdsmpzUZtazxSk\nxO8MdCEfPyzATwIOtaoWoy8BS/fRs/ZrJbpmT+wwPTEZ3DioPv1r5Gg/FzgT\n8zUDx+Z9edqIlwW/m0F90OyXyQpXs8zOp6reKwB17UStdVarkS0z/wwPtTBZ\nTFZm\r\n=gBfD\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDaEzE88DkvuTy0HcWmO8JbxJtYMv/NaBg4h0g67qoWiwIhAM2h+Ue/YUWHNidrvmKmTDkrmU29Cd8Fb7RHaG+2QZF7"}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-timer_4.0.4_1580757998314_0.21997616793335495"}, "_hasShrinkwrap": false}, "4.0.5": {"name": "@szmarczak/http-timer", "version": "4.0.5", "description": "Timings for HTTP requests", "main": "dist/source", "engines": {"node": ">=10"}, "scripts": {"test": "xo && tsc --noEmit && nyc ava", "build": "del-cli dist && tsc", "prepare": "npm run build", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["http", "https", "timer", "timings"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http-timer.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http-timer/issues"}, "homepage": "https://github.com/szmarczak/http-timer#readme", "dependencies": {"defer-to-connect": "^2.0.0"}, "devDependencies": {"@ava/typescript": "^1.1.0", "@sindresorhus/tsconfig": "^0.7.0", "@types/node": "^13.5.1", "@typescript-eslint/eslint-plugin": "^2.18.0", "@typescript-eslint/parser": "^2.18.0", "ava": "^3.2.0", "coveralls": "^3.0.9", "del-cli": "^3.0.0", "eslint-config-xo-typescript": "^0.24.1", "nyc": "^15.0.0", "p-event": "^4.1.0", "typescript": "^3.7.5", "xo": "^0.25.3"}, "types": "dist/source", "xo": {"extends": "xo-typescript", "extensions": ["ts"]}, "nyc": {"extension": [".ts"], "exclude": ["**/tests/**"]}, "ava": {"typescript": {"rewritePaths": {"tests/": "dist/tests/"}}}, "gitHead": "c4783261a2926379eed6c4f58f8f18359c2f42c2", "_id": "@szmarczak/http-timer@4.0.5", "_nodeVersion": "13.7.0", "_npmVersion": "6.13.6", "dist": {"integrity": "sha512-PyRA9sm1Yayuj5OIoJ1hGt2YISX45w9WcFbh6ddT0Z/0yaFxOtGLInr4jUfU1EAFVs0Yfyfev4RNwBlUaHdlDQ==", "shasum": "bfbd50211e9dfa51ba07da58a14cdfd333205152", "tarball": "https://registry.npmjs.org/@szmarczak/http-timer/-/http-timer-4.0.5.tgz", "fileCount": 5, "unpackedSize": 10320, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeOHaeCRA9TVsSAnZWagAAzzkQAJgrmhJwY8Nr8IAd80us\nWJ6xQuiOfEyfVNP/wYQ2Z6YyOGAFYSMRSYpeVuDghGjZB07yWh4b9ll1Vxza\nyNysJeY/atZSTui1N4wJXPJJlGGKBQskWYi15OLzYhEL13GNLmV3NU7fkRhZ\ngZ62r2k+YAowFV5sr9foJFcHGgpd0qFoju2XzR5IQquArwTw797oQWvtaoiK\nvlfy3TJrUn82mwYh/d10+iBwmEt7BhmVHZ+KVLIUIAI9i0MD97A7PUz/RFLQ\nHt8buid5uG/QfQ2MCP6c0viwFxuXVf68UdIYJeKbqxo/GW8yreJ+b1nhoyaU\nQ1bIjCMbC06AlnyNfTaT8X8khMNX7N1tPowB1jcxaGJaRKdR6x+0+kCwIf7l\n4q45Xyd+ivZzdO0cXdm6gYN7lXHR+5fcqM9q1DPT4sUW6wOakWIkgw+adwD0\nktIWrbZiaT1ukwyvq8KjoIBggExaNqSnJXg+zwlpDW52/lj5Tycx18MyLFgp\nSpvuig5wjU7KBqUekiF2jmtz+koQ7wZshu+XP/Zlh2iFjoKWyf+IxDjKpZbi\nH22SXdJ/KuXTyG4oMMzAx4dTzRpVhJCu76gaIl6e/DszVf7j+qZHQw7NtjI6\n5xYEIhwxfApdlFyuW7GJ6iQZyoAM6gW5IyQw7Kc4t/ygidMC6/TzuTsJytII\n2MG3\r\n=M6m2\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCnlJ0q49VO3uJya2i3zdjBrcCmn1u7UAMcrufEEnXNpQIhAMfG9Va5aignZo/numR1uajoKudnP7OaYme3ho94xvZF"}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-timer_4.0.5_1580758686149_0.14731738815375062"}, "_hasShrinkwrap": false}, "4.0.6": {"name": "@szmarczak/http-timer", "version": "4.0.6", "description": "Timings for HTTP requests", "main": "dist/source", "engines": {"node": ">=10"}, "scripts": {"test": "xo && tsc --noEmit && nyc ava", "build": "del-cli dist && tsc", "prepare": "npm run build", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["http", "https", "timer", "timings"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http-timer.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http-timer/issues"}, "homepage": "https://github.com/szmarczak/http-timer#readme", "dependencies": {"defer-to-connect": "^2.0.0"}, "devDependencies": {"@ava/typescript": "^2.0.0", "@sindresorhus/tsconfig": "^1.0.2", "@types/node": "^16.3.1", "ava": "^3.15.0", "coveralls": "^3.1.1", "del-cli": "^3.0.1", "http2-wrapper": "^2.0.7", "nyc": "^15.1.0", "p-event": "^4.2.0", "typescript": "^4.3.5", "xo": "^0.39.1"}, "types": "dist/source", "nyc": {"extension": [".ts"], "exclude": ["**/tests/**"]}, "xo": {"rules": {"@typescript-eslint/no-non-null-assertion": "off"}}, "ava": {"typescript": {"compile": false, "rewritePaths": {"tests/": "dist/tests/"}}}, "gitHead": "a44ca1a3ebe5440211b0142f796760bb17aeee30", "_id": "@szmarczak/http-timer@4.0.6", "_nodeVersion": "16.4.2", "_npmVersion": "7.18.1", "dist": {"integrity": "sha512-4BAffykYOgO+5nzBWYwE3W90sBgLJoUPRWWcL8wlyiM8IB8ipJz3UMJ9KXQd1RKQXpKp8Tutn80HZtWsu2u76w==", "shasum": "b4a914bb62e7c272d4e5989fe4440f812ab1d807", "tarball": "https://registry.npmjs.org/@szmarczak/http-timer/-/http-timer-4.0.6.tgz", "fileCount": 5, "unpackedSize": 10824, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg7LaRCRA9TVsSAnZWagAA/68P/0i0xtCfTxdI2P5Ksi1j\n5tAKy07b72lmvXtuKvjYfCwxwiV/d5U2mxY2HGMJnpKzw3KA5lXLTeoMN8rY\nmGlicZ0lmKX0Zh527XzckO8gBL4NyuJxNSdRE8IdD8KErQNEZcYC5hcOq8Tz\nGTIuRd7DCgud6QSawbs9qVnnbmcgvGIwhwxvIZVA7cKErthxl91H3CHlOylj\nDRMvDOV11wAo8k2IjwBz38tPxahkjjgFdZ/qWTd+SyjorKYg64aMfUjrnlfo\nbY9WYL8EDcrkhNTUjsng2OvMVoR7b43OOeHA1a9cR262qX0JZD1TIE4hUsdb\nIPXeQcI+cCY055m6T5z4as5bSxjFqXdKTLRrzAr2sZQhgXBEZAueFXyREjB9\ns+J8ymmHLhEMj+SdoLeHAydVpl1S7u3q4PblFsleQ92N/xxIgM9BpvqOD1K7\n6vg0kQERtRIg5utWlwFp5h08/OiWT8ZDhqAeUL8uvCqWaHIOo0pzKWRhtKyb\nHAhL4seMRuUbxkNlEqTR5ENrTzz3Wi2QELWXTfSvmdkp+mexCaBGEtNDVy0x\nw5v7fKdC+cMaNQ8U4lmuwT8kUvsPQ+KJNXYOC1j5t+YZQ64jaraa5/h2FQ0P\n3pK2Jdm1mA5me7YQrFnS9fRX0hf89hPDS/4EflFgxoqEEIYQ/1avqqlCPgXf\nCKvj\r\n=XCGj\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDHpUaDOx2Dr6fIAj6xIHOvgR1oZN0xPDh5xuzzvCG5qwIhALuXa1mcq9auTUC1tVH/57NT1SA+Wts4ySHyLg70Heyq"}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-timer_4.0.6_1626125968621_0.6405166985466364"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "@szmarczak/http-timer", "version": "5.0.0", "description": "Timings for HTTP requests", "type": "module", "exports": "./dist/source/index.js", "engines": {"node": ">=14.16"}, "scripts": {"test": "xo && ava", "build": "del-cli dist && tsc", "prepare": "npm run build", "coveralls": "exit 0 && nyc report --reporter=text-lcov | coveralls"}, "keywords": ["http", "https", "http2", "timer", "timings", "performance", "measure"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http-timer.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http-timer/issues"}, "homepage": "https://github.com/szmarczak/http-timer#readme", "dependencies": {"defer-to-connect": "^2.0.1"}, "devDependencies": {"@ava/typescript": "^2.0.0", "@sindresorhus/tsconfig": "^1.0.2", "@types/node": "^16.7.0", "ava": "^3.15.0", "coveralls": "^3.1.1", "del-cli": "^4.0.1", "http2-wrapper": "^2.1.4", "nyc": "^15.1.0", "p-event": "^4.2.0", "ts-node": "^10.2.1", "typescript": "^4.3.5", "xo": "^0.44.0"}, "types": "dist/source", "nyc": {"extension": [".ts"], "exclude": ["**/tests/**"]}, "xo": {"rules": {"@typescript-eslint/no-non-null-assertion": "off", "@typescript-eslint/no-unsafe-assignment": "off", "@typescript-eslint/no-unsafe-member-access": "off", "@typescript-eslint/no-unsafe-call": "off"}}, "ava": {"files": ["tests/*"], "timeout": "1m", "nonSemVerExperiments": {"nextGenConfig": true, "configurableModuleFormat": true}, "extensions": {"ts": "module"}, "nodeArguments": ["--loader=ts-node/esm"]}, "gitHead": "b8dabf9bbd64c71ed702064bb78123a70e7c069c", "_id": "@szmarczak/http-timer@5.0.0", "_nodeVersion": "16.6.2", "_npmVersion": "7.20.3", "dist": {"integrity": "sha512-3T7qFw0Ta2JPfSGAL6y3XnS28vjfIGRNwIQQ+Uj63tycqDxyvG4MUzwmvv1fFr8sYx3BvHcTW/zCCeRHHbc9iA==", "shasum": "e972e084bb9ec7b180eb27e507b469e98edeebec", "tarball": "https://registry.npmjs.org/@szmarczak/http-timer/-/http-timer-5.0.0.tgz", "fileCount": 5, "unpackedSize": 10174, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhIDbkCRA9TVsSAnZWagAAEWYP/Ri/BjSMPjGGmC00qC0i\nuMa6ibznZbUYxRKXSLeSxJaAPUkWh7RhGVetoNydBpsjbP4vVZXb1Z5+zNQA\nRYOsRu2WBNs/daeWPupwvJPopocRADo5mxvCZgfyFr9KnMczWxXMGpteTSua\nnIDQ/64AG1M+1yQF5SeuyOKRAFY6PnxumuqncdCoExQunRZ6LaBSOHpR/9bm\nQIZlb5wAffaSwb+yWd3esrGonyeekcTmxVXYtZX/Czsiyk8i9CeJ+cg0ar+Z\nJYgiNNUDQR0bFeOP2IeN1ikRgEzRwVQSghjKBYm7EPYQ7HN1e8cM5p3gFylS\n46krx0eQ3TQJEQy+8Dipu1PJafRf79sg5Zw4lDaMau8RJ3t+OEWZV1JRqGKE\nqUMD9L6rQtkKuqbHQBe/m2DHx/UV/VBQB+rXzWLsiOa6gVYMGaa+a76zDYLC\n7wfIwO9V6Q3UQXIilhAO+uz3W5LOzn61t57quWlaH/V/ITucQztEanW+0Bei\nKnwjjh1rXkUJtBFUuZlJhR4B8QLd6lOv3qTjYoiIw/uC2rqtEbvyr4jMlSom\nfqylcVaFQj9AqLM35pgVn76LbDvdYAfMcitOJaQr0NYDP+iun6Dj6UeZX55v\nB+nAvaByv23bn8i50bTpCDftgGWzlsxCel8y3hnkzwu0622xfyTZd9gg3rvX\nV1tr\r\n=ecTC\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICoLXuj8mKfDwE2Lwv1xQLL3y+YnW1C4bmZB97zz3VPcAiAEsSrdkJVYRotHNivTykIMIBs0CaFOGjeT45/eT7VPmw=="}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-timer_5.0.0_1629501156451_0.2868118044635479"}, "_hasShrinkwrap": false}, "5.0.1": {"name": "@szmarczak/http-timer", "version": "5.0.1", "description": "Timings for HTTP requests", "type": "module", "exports": "./dist/source/index.js", "engines": {"node": ">=14.16"}, "scripts": {"test": "xo && ava", "build": "del-cli dist && tsc", "prepare": "npm run build", "coveralls": "exit 0 && nyc report --reporter=text-lcov | coveralls"}, "keywords": ["http", "https", "http2", "timer", "timings", "performance", "measure"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http-timer.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http-timer/issues"}, "homepage": "https://github.com/szmarczak/http-timer#readme", "dependencies": {"defer-to-connect": "^2.0.1"}, "devDependencies": {"@ava/typescript": "^2.0.0", "@sindresorhus/tsconfig": "^1.0.2", "@types/node": "^16.7.0", "ava": "^3.15.0", "coveralls": "^3.1.1", "del-cli": "^4.0.1", "http2-wrapper": "^2.1.4", "nyc": "^15.1.0", "p-event": "^4.2.0", "ts-node": "^10.2.1", "typescript": "^4.3.5", "xo": "^0.44.0"}, "types": "dist/source", "nyc": {"extension": [".ts"], "exclude": ["**/tests/**"]}, "xo": {"rules": {"@typescript-eslint/no-non-null-assertion": "off", "@typescript-eslint/no-unsafe-assignment": "off", "@typescript-eslint/no-unsafe-member-access": "off", "@typescript-eslint/no-unsafe-call": "off", "unicorn/prefer-node-protocol": "off"}}, "ava": {"files": ["tests/*"], "timeout": "1m", "nonSemVerExperiments": {"nextGenConfig": true, "configurableModuleFormat": true}, "extensions": {"ts": "module"}, "nodeArguments": ["--loader=ts-node/esm"]}, "gitHead": "b8dabf9bbd64c71ed702064bb78123a70e7c069c", "_id": "@szmarczak/http-timer@5.0.1", "_nodeVersion": "16.6.2", "_npmVersion": "7.20.3", "dist": {"integrity": "sha512-+PmQX0PiAYPMeVYe237LJAYvOMYW1j2rH5YROyS3b4CTVJum34HfRvKvAzozHAQG0TnHNdUfY9nCeUyRAs//cw==", "shasum": "c7c1bf1141cdd4751b0399c8fc7b8b664cd5be3a", "tarball": "https://registry.npmjs.org/@szmarczak/http-timer/-/http-timer-5.0.1.tgz", "fileCount": 5, "unpackedSize": 10201, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhIDoTCRA9TVsSAnZWagAAU/cP/0QBOlcsqItX4c6nHGYU\nze3dEQQ8h8Ba7gACmLCaG3v7bPiOI6juOpTQW//XbD/DeS4/cS13a3c79nmm\n93C51pTqv/p/oD415wprzQgvz21df8JjzbecMFJUsubLtRLfgBrmxVfKM9gZ\nLs+RLQgErGkOu6mQr06bm1xLgcA0YZehXh/eyy+Y6i8xutAhWLwySecn7sQd\ndD82IamzYUtyFiSV3N8IslOu7GPzlP5GD7VPGL0Wi5MD/jXqMZ8Px8FVOGvQ\nAU0QGECmdpy74qjxjH2mLNJTzi7KDHOc9PJLRg3srY3zbb+OBp7tVbsEX4xx\nk4z6G8I8MkwtFsTpnO4BOIQQ/gatG5gO89/RlSj3dvuSvQn1cy5Us95ysCQw\n17ikf1k/hH1fYFHuYJ5FeKiILXbz93jFw04Q5WIFi0hqVpE86StfPBtM2+TV\nWSP7BSluURnrIfvsr3w8Sz5bhMRRjPRT5HO6Sic+jOIdjvKqBwiznfWxoYug\nOCbCsmOydsdLMX2HuVCE+ZK17boFumQ5SUkZxDhfweqsxD0AAAUM7nTvdqVV\n7VfBKR8Uu7w1Kt2xb1YWrk55j2ymyHbCfZSQuRHnUt8N/tNgOfOmkXOQs3pR\nM+YBPVcBBuBg4nnmCXI6RIqzMcrj6C3Ul8SeFXGnyjYrXUdrZOkS4S/nqMLM\n7y2w\r\n=yOTC\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDFDGBWP+OMcGhqusm8h0a3macaIZYYYX6LhEBaT3PjtAiEA4zKtJCOig0UzeZrw4q2RshtM6Gh65tuiJE/V/5lpAwU="}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-timer_5.0.1_1629501971094_0.9473692551089752"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-08-24T19:38:41.369Z", "1.0.0": "2018-08-24T19:38:41.692Z", "modified": "2022-04-07T03:49:45.986Z", "1.0.1": "2018-08-24T19:54:03.960Z", "1.0.2": "2018-08-25T08:12:32.976Z", "1.1.0": "2018-08-31T10:50:40.690Z", "1.1.1": "2018-08-31T17:09:36.011Z", "1.1.2": "2019-01-04T16:38:57.153Z", "2.0.0": "2019-04-20T19:14:59.810Z", "2.1.0": "2019-11-01T15:55:11.309Z", "3.0.0": "2019-11-02T16:43:39.307Z", "3.1.0": "2019-11-15T20:26:40.864Z", "3.1.1": "2019-12-04T13:31:44.250Z", "4.0.0": "2019-12-27T17:04:15.256Z", "4.0.1": "2020-02-03T16:29:01.907Z", "4.0.2": "2020-02-03T16:33:22.559Z", "4.0.3": "2020-02-03T16:59:21.303Z", "4.0.4": "2020-02-03T19:26:38.472Z", "4.0.5": "2020-02-03T19:38:06.355Z", "4.0.6": "2021-07-12T21:39:28.744Z", "5.0.0": "2021-08-20T23:12:36.611Z", "5.0.1": "2021-08-20T23:26:11.251Z"}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "description": "Timings for HTTP requests", "homepage": "https://github.com/szmarczak/http-timer#readme", "keywords": ["http", "https", "http2", "timer", "timings", "performance", "measure"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http-timer.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "bugs": {"url": "https://github.com/szmarczak/http-timer/issues"}, "license": "MIT", "readme": "# http-timer\n> Timings for HTTP requests\n\n[![Build Status](https://travis-ci.org/szmarczak/http-timer.svg?branch=master)](https://travis-ci.org/szmarczak/http-timer)\n[![Coverage Status](https://coveralls.io/repos/github/szmarczak/http-timer/badge.svg?branch=master)](https://coveralls.io/github/szmarczak/http-timer?branch=master)\n[![install size](https://packagephobia.now.sh/badge?p=@szmarczak/http-timer)](https://packagephobia.now.sh/result?p=@szmarczak/http-timer)\n\nInspired by the [`request` package](https://github.com/request/request).\n\n## Installation\n\nNPM:\n\n> `npm install @szmarczak/http-timer`\n\nYarn:\n\n> `yarn add @szmarczak/http-timer`\n\n## Usage\n**Note:**\n> - The measured events resemble Node.js events, not the kernel ones.\n> - Sending a chunk greater than [`highWaterMark`](https://nodejs.org/api/stream.html#stream_new_stream_writable_options) will result in invalid `upload` and `response` timings. You can avoid this by splitting the payload into smaller chunks.\n\n```js\nimport https from 'https';\nimport timer from '@szmarczak/http-timer';\n\nconst request = https.get('https://httpbin.org/anything');\ntimer(request);\n\nrequest.once('response', response => {\n\tresponse.resume();\n\tresponse.once('end', () => {\n\t\tconsole.log(response.timings); // You can use `request.timings` as well\n\t});\n});\n\n// {\n//   start: 1572712180361,\n//   socket: 1572712180362,\n//   lookup: 1572712180415,\n//   connect: 1572712180571,\n//   upload: 1572712180884,\n//   response: 1572712181037,\n//   end: 1572712181039,\n//   error: undefined,\n//   abort: undefined,\n//   phases: {\n//     wait: 1,\n//     dns: 53,\n//     tcp: 156,\n//     request: 313,\n//     firstByte: 153,\n//     download: 2,\n//     total: 678\n//   }\n// }\n```\n\n## API\n\n### timer(request)\n\nReturns: `Object`\n\n**Note**: The time is a `number` representing the milliseconds elapsed since the UNIX epoch.\n\n- `start` - Time when the request started.\n- `socket` - Time when a socket was assigned to the request.\n- `lookup` - Time when the DNS lookup finished.\n- `connect` - Time when the socket successfully connected.\n- `secureConnect` - Time when the socket securely connected.\n- `upload` - Time when the request finished uploading.\n- `response` - Time when the request fired `response` event.\n- `end` - Time when the response fired `end` event.\n- `error` - Time when the request fired `error` event.\n- `abort` - Time when the request fired `abort` event.\n- `phases`\n\t- `wait` - `timings.socket - timings.start`\n\t- `dns` - `timings.lookup - timings.socket`\n\t- `tcp` - `timings.connect - timings.lookup`\n\t- `tls` - `timings.secureConnect - timings.connect`\n\t- `request` - `timings.upload - (timings.secureConnect || timings.connect)`\n\t- `firstByte` - `timings.response - timings.upload`\n\t- `download` - `timings.end - timings.response`\n\t- `total` - `(timings.end || timings.error || timings.abort) - timings.start`\n\nIf something has not been measured yet, it will be `undefined`.\n\n## License\n\nMIT\n", "readmeFilename": "README.md"}