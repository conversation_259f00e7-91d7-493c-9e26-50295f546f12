{"_id": "cholera", "_rev": "3-b09047d581225e6b0405db4ad90abcc6", "time": {"8.8.8": "2021-03-12T11:35:23.406Z", "created": "2021-03-12T15:53:47.630Z", "0.0.1-security": "2021-03-12T15:53:47.779Z", "modified": "2022-04-12T06:13:12.324Z"}, "name": "cholera", "dist-tags": {"latest": "0.0.1-security"}, "versions": {"0.0.1-security": {"name": "cholera", "version": "0.0.1-security", "description": "security holding package", "repository": {"type": "git", "url": "git+https://github.com/npm/security-holder.git"}, "bugs": {"url": "https://github.com/npm/security-holder/issues"}, "homepage": "https://github.com/npm/security-holder#readme", "_id": "cholera@0.0.1-security", "_nodeVersion": "12.20.1", "_npmVersion": "6.14.10", "dist": {"integrity": "sha512-imX67+FAcxSx0dK1yZMJXl+t0YWGHJP7FjhI7nTegrzpLe82AuvJvtQ6EL92LYTPsY6I6U148IYRSxHFnv4OqA==", "shasum": "a7a194c015675198080a2d2c22587415bebb5a71", "tarball": "https://registry.npmjs.org/cholera/-/cholera-0.0.1-security.tgz", "fileCount": 2, "unpackedSize": 426, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgS46LCRA9TVsSAnZWagAAAqgQAJRS3H+48RhioOQSntUm\nHSmEH6cI9OXiG1meg0xXWhXQfv8cXnPrJyvmKMvsesj8/8+dCBYZ1Z7hp0ep\nXZwyBl+MozCg2Y/hgqmN8Z/m9VJjszlJ2W6MgAEO6jhPoGLhsdJZw3Gz8Wbs\nBsJvMSv0h5htyOdUBRZqc6ECF793KoHEp1zHwmrr18jtTYnWVz/C6kngDS8p\ntet+usgiJ5IDtnOVlx1nMo2FgYcVWzw4M+IHZzB2IPW5gN5W29hXXXCL+9Qm\nlOW4qf+TdPElCvufxEmdX/6Lk8a9+k/qwDe75IeseTWoNlRQ06QekrW77tjL\nflu3Yd6o9Z8A1X84+3UuaB1B18mIRWjA7TjL/U7mP0ik0rbeL4ojtmh0glc0\nTu6klnmdJgPenHV/kVIRZLFdedQw1f77W+z/yxjq51vdkJJK4ZtXBgkulFba\n5pmiVGbO1QQfB2uTKMgMFHNgQKywWTQODNMIAGONQPoFc0c7dDJ5aDuZo2g3\niJqPkM2jKpMsAzKot8UjYT0cWpgFkanL+g9hwX6bvGJpXvgUvYEnhd6TG/UZ\nZoUxelCBniWT84jUw4lmN3L4xGRx/CvGmS/vHRSznJlNzQe/8sCy93WT4gvF\nvM1wdXX1nK1JCuNDhONsBUf9Ees63LwzUQrfrY0glaZ4WcWC4SihfCwztu0h\nXQe3\r\n=tNJ2\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAxx/fK3T7Xqn/epyuFEF08DsSsawd/4SAEjzNnqCbeFAiB2Bsl4JeGdyNSpRYjskSK2r5gmaROHfyqyJoWbyT7PDg=="}]}, "_npmUser": {"name": "npm", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cholera_0.0.1-security_1615564427631_0.6886247832971586"}, "_hasShrinkwrap": false}}, "maintainers": [{"name": "npm", "email": "<EMAIL>"}], "description": "security holding package", "homepage": "https://github.com/npm/security-holder#readme", "repository": {"type": "git", "url": "git+https://github.com/npm/security-holder.git"}, "bugs": {"url": "https://github.com/npm/security-holder/issues"}, "readme": "# Security holding package\n\nThis package contained malicious code and was removed from the registry by the npm security team. A placeholder was published to ensure users are not affected in the future.\n\nPlease refer to www.npmjs.com/advisories?search={package-name} for more information.\n", "readmeFilename": "README.md"}