{"name": "trim", "dist-tags": {"latest": "1.0.1"}, "versions": {"0.0.1": {"name": "trim", "version": "0.0.1", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "5858547f6b290757ee95cccc666fb50084c460dd", "tarball": "https://registry.npmjs.org/trim/-/trim-0.0.1.tgz", "integrity": "sha512-YzQV+TZg4AxpKxaTHK3c3D+kRDCGVEE7LemdlQZoQXn0iennk10RsIoY6ikzAqJTc9Xjl9C1/waHom/J86ziAQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCb2D2z/tfxPlDiGHfHTwfFd5Jkuzb7fJcWy6MXccysfwIhAO5uHa0bPAw3p4BEbdO+pG5TjePkqWpLxYOjwHICYPLI"}]}, "deprecated": "Use String.prototype.trim() instead"}, "0.0.2": {"name": "trim", "version": "0.0.2", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"integrity": "sha512-kTIK/cS0xM3jxJ7toUHlFTxHgix/kmmBgOiqc0gUAoW+NjIRsMB3vkjgAth5XEghYFCQxOdF0p/PHrv1BqTHgA==", "shasum": "b41afc68d6b5fc1a1fceb47b2ac91da258a071d4", "tarball": "https://registry.npmjs.org/trim/-/trim-0.0.2.tgz", "fileCount": 6, "unpackedSize": 3091, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfo0F2CRA9TVsSAnZWagAAMGIP/1Qh/keb1a7jIj1U4uV2\nu73T25EQCJAO8/9ZnJntaV7AlHbMCvesAyIAqN6Vz+LgrEWj/0yILPcDrnQQ\nDOoNjZZoiUPRwnv0DGwwuMzw8CNNdBJoxhSCoW3KBvjEowxexWYmnXFBkAOW\nA5PeXazgJS5mbSlSkUavF/fBZBlgJje/R5dzC+jm7HCkS/7vufRlUM7rZFJ/\n8T089vEHqsUHSjCvy9PA1H/wHs2tmNp6hU7zRUCLCXtertQvVsNDoSOnU3y8\nsDZE1MwvoBi/cCqa07r/WSTE2KSDrElSgiM5aDOVo98S3e8g6VAYlvWJYfrf\nLJ5UoZujmaIqCkOJrA4Yf0e6ysMEzbhkqeMtMiLRbbaDzstR648xrPQ5PFqc\n4LqRY5Gnuwuosq0CyRI01APPxoKu3R6TNw9AjwaZ9zD7rKFibpsMXBsOEHAa\noVZ3SjgwA7YDpGA8iCsNmQoLKC3/f6L6TzzhgfnXuSOsKpB6gTnpjX4qckXb\nYiDTGEo2diHAFa9ecaGJJCnhKM13IkTT8F3zniEGCNRYG2y0ufPD2RKHsRNj\nS4AjC4YZMaAslULf2j1QfrqsxoiOw84kQKzFRgvi/RFNEUyQnUTHBNfrN3Ad\nESxmEcNZVdziMHCo6BlSVEirlDliIYEt6k4NXxsrF/Fdqfwoemv+JsUCv52K\npec3\r\n=2hGP\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD3TwJp0IGnlGiiaIprRJis6jzqn5SKWvwid8yiJXW77gIgJJFvvcRPTUGcvh2Wo/2SR+j9V81B7ORthyJnBj7K7+E="}]}, "deprecated": "Use String.prototype.trim() instead"}, "0.0.3": {"name": "trim", "version": "0.0.3", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"integrity": "sha512-h82ywcYhHK7veeelXrCScdH7HkWfbIT1D/CgYO+nmDarz3SGNssVBMws6jU16Ga60AJCRAvPV6w6RLuNerQqjg==", "shasum": "05243a47a3a4113e6b49367880a9cca59697a20b", "tarball": "https://registry.npmjs.org/trim/-/trim-0.0.3.tgz", "fileCount": 6, "unpackedSize": 3160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfo0H4CRA9TVsSAnZWagAAxScP/1eU9zYiEaOJYqxNTTmU\nYshkiuzhLsarli1hf69PovKyPVomXpgikDhyLT88vqtwrhFPoxr+ZB0GEw5O\nzJMUwJBIW+v9Aqs3HHZTopLisD1X5bfKCmjEwFd3i0kfsZGPcoI39GXevZLX\nMFVuWPLTqYO++pjcKAloRjyGuzfowJBy/Xpt0pxdKEyrrQCN/0xX55HxOTDa\nXXmdpit4WoxEuaTaoBYorlTGLHTD8b1eAY7J0hiMhNeGs4bzUQrS364v3FAp\n8CilYzlgtX6p0jMXa4doKMorlNc/Q2rLJi2yNUl/NQTVT3+JOtsZ2Hy4n/Ix\ngUd3AOB0w2ydIJo8CUqlMZmrv2lR920LtkAR1GiOQjZJMcEQtMaGjb82GipS\nG3fW8h9Itd9qJm0dbuG+Azk2+tv71MmupxDQmZVMGWNESrdYyJLl2txZ3szu\nUuWxlrZwrAJyvXVaRsXG1E4aQ7YY5TnZ8M7AEAhal342k+M0inspwx2CKS/u\nOlvSZ5kVtJe/HS14AGJHs2ezaNXlHmHoSVz22/bTcFRWsZLOkS0jw15Ns9Pe\nHLI/v1d8JHCYJcxjHtIwnnE5LAlg+SLhSYQlBNqeUJb0CWsp3gCf6QRmsL5t\nyFfNQQCDWKpcVm+17ky0eBScboKYMEOA5FGc4kilNRkupUXhOH6kEJIPwRLG\nXvQo\r\n=IVE7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFc+F8L+MeIfOfLZkBCaJlhHY8tmpD0Lbyck7UUVuolZAiEA08AqhjPC7fr+1M8RfPokrj9v6RMCHZ7S4IZdOTK5tqs="}]}, "deprecated": "Use String.prototype.trim() instead"}, "1.0.0": {"name": "trim", "version": "1.0.0", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"integrity": "sha512-UgtES1lYpE+f4WiGY5lyJlHchuGhTa/xMPH96g/B7gc+pEQPiL41s6ECm7Ky3hkhARG/u1SHGFcleJodAvQOKQ==", "shasum": "fd1f30b878bdd2d8435fa0f2cc9cbb55f518be7d", "tarball": "https://registry.npmjs.org/trim/-/trim-1.0.0.tgz", "fileCount": 6, "unpackedSize": 3180, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfrc43CRA9TVsSAnZWagAAwmwP/iddG683y6e+/lBjlnTs\nuJVR9op5T72San5M7XLKrA/2GU1btlC1DDa/1b95W1MyY+vHvYvUqdAPDToB\nFrhZUOFYphY8+eH6UoGaIIIFVIHW/GTHvjACelGXfEy0vioFoV6i/5xzRWry\nStrRTPKM/47pGIXgs3Lu8x38qaFEUvSRzQmgmFZGf9wXnZGQ6Kw5XAqf9P+z\ncht6Gvg72eX9y1yEM7hNjrZ3BEiqvIxO6StHRGe/IuC9WiUN3uk4IaJRJpdK\nCXCO01VVndMVEn45YEIfoLoh3KZRikvxGCoHSsVsKlOYn9TGhUU7dphB5x3H\n0bBxI+QyhVWaxFrcnbdGj1kcrglkcKchpH484vf4Td4SNBXrDVmKVW/57CRF\nv5yg1w3xeF9p6B24h3f1SDdx5YlyvLqoTemhrWts2t56qacE5tCLqJyuNrDb\ni7x7KExTnC3RQfmH0LHM6J48/DzRcuN1mqt+T4Lky7Fwju7KY1YOiJb8/UdY\nOcEqETSgubuMVsWeO/LNErpxoO0hmt2C91EOJ4J+e/9xcv7S07stTy1UDhf0\n5bESHmSYDwlTVscVi2OzKCFFGjXQ9WWEPLgfBLO3vY2LGQw94qahXOjCyzkv\n0H9UmTrs844PDhW79olz+uMND1XGvySIp86q3ZpIhStDUu4wH/SpeC/4wkgR\n46+J\r\n=wu7M\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICzq39ykI7jPY6SgDcIsA2g5HBaemC49nlB7XNAPL+uYAiBDAMWbM8Rd1/BXQFauf73DqcJ04sQsQUhTv2NQ/bcISg=="}]}, "deprecated": "Use String.prototype.trim() instead"}, "1.0.1": {"name": "trim", "version": "1.0.1", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"integrity": "sha512-3JVP2YVqITUisXblCDq/Bi4P9457G/sdEamInkyvCsjbTcXLXIiG7XCb4kGMFWh6JGXesS3TKxOPtrncN/xe8w==", "shasum": "68e78f6178ccab9687a610752f4f5e5a7022ee8c", "tarball": "https://registry.npmjs.org/trim/-/trim-1.0.1.tgz", "fileCount": 6, "unpackedSize": 3346, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZjGkCRA9TVsSAnZWagAAmQcP/0EujKehHOhkDTDgqiBy\nLvoBH8XrGdtaXTvGd0+Wwbgh2+**********************************\n+Z6GuoqrhGP+q/z7Uct23A1YO3Mlg+yAErE3hQAa66Y3YfWjd8Q3qIf4sxyG\nzb3ZruZTAHhKNHSSbpSaQGWdLiL2irzAlWp+bE5+2Yx6J7hq3LdHqCbeV3bx\nkDetuB3V07LpeBCGlGcfgMND4n2SdA9zxmFFUNaHkhWJzE2L176j/zwzU0Ru\nBnKiy/YAlZPftcR5WvMIbxZ2pxkYGG6yxPwOs5ESGlBtG0Q15+6LX7sJ9QDu\nT59zb743ovT0tnymUkGJsMwnKWxlJwNTu6M3Dehexs3uzYPZxwFE5sxOM3JW\nTbz9KnBCT2X60XezElKD6tENccLmciTcUbQ3cQsitVXZGhcWBj5Lb9EDI27Q\nZPcj2emd3jJWaEHADhWlAL0+aFXRwAW/x09Q7Wnu2IukNYZJBhCJxSZYvfon\nZNoSOBFCqW6AIljf1B/9iRPFrPXTtkwzDe2AlKGOmSdJP6noDSk+FCbpbIf0\norjW0yIwekVxHHIMxDw8NVpqZJGVDu+UBSL+Csh0YtOjNo4l8taq4DZIcPwe\nu9CD0+emv59PKaQtQF++5j2wtbo9eKme+bsveAvhIHp5TgHvh9xzqMu/tU4c\nRku6\r\n=hSgw\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDjXJN6C3gtAFH1Ye8hE+nd97g/oB3u9KB4lDxFJDBA3AIhANbvuakU2tRT2bSdOMYNzys0/rK4yZM6or9xCOzr666c"}]}, "deprecated": "Use String.prototype.trim() instead"}}, "modified": "2023-02-25T18:02:14.784Z"}