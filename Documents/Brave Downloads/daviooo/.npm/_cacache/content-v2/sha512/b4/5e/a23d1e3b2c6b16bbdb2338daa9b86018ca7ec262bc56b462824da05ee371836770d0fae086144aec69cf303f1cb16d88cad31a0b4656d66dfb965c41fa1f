{"_id": "is-weakset", "_rev": "14-85d2ccc6bbfc1abfe913d01a13a5ea3c", "name": "is-weakset", "dist-tags": {"latest": "2.0.4"}, "versions": {"1.0.0": {"name": "is-weakset", "version": "1.0.0", "keywords": ["set", "es6", "harmony", "typeof", "type check", "weakset"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-weakset@1.0.0", "maintainers": [{"name": "art<PERSON><PERSON>r", "email": "<EMAIL>"}], "homepage": "https://github.com/arthurvr/is-weakset", "bugs": {"url": "https://github.com/arthurvr/is-weakset/issues"}, "dist": {"shasum": "3fe684c08ec698c6492e588925ceff8665fd9fa1", "tarball": "https://registry.npmjs.org/is-weakset/-/is-weakset-1.0.0.tgz", "integrity": "sha512-Nv4iOqp4k+QEEeencgxGiPormPH7WS8t6gEvMN8CD/DWbWeAlQ96rnSk8+V9k3PssolJOlfYWyY7U6pSEK2L0g==", "signatures": [{"sig": "MEYCIQD1WZikzOjl9TKGhleY9aK2ER2440IUO8gj+eTqIuPc0QIhANWxqJobzTVoJiFXCPIOzgrn1vI3wc/NI0H6Tnp5cFUh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "3fe684c08ec698c6492e588925ceff8665fd9fa1", "engines": {"node": ">=0.12.0"}, "gitHead": "c71bd3a75b29d600fb276b2a76d47667e5c3b159", "scripts": {"test": "node test/test.js"}, "_npmUser": {"name": "art<PERSON><PERSON>r", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/arthurvr/is-weakset", "type": "git"}, "_npmVersion": "2.5.1", "description": "Easily check if a givin object is an ES6 WeakSet", "directories": {}, "_nodeVersion": "1.2.0", "devDependencies": {"ava": "^0.0.4"}}, "1.0.1": {"name": "is-weakset", "version": "1.0.1", "keywords": ["set", "es6", "harmony", "typeof", "type check", "weakset"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-weakset@1.0.1", "maintainers": [{"name": "art<PERSON><PERSON>r", "email": "<EMAIL>"}], "homepage": "https://github.com/arthurvr/is-weakset#readme", "bugs": {"url": "https://github.com/arthurvr/is-weakset/issues"}, "dist": {"shasum": "79bf1d4656abd2404f3736c1aaae62d93a73bfdc", "tarball": "https://registry.npmjs.org/is-weakset/-/is-weakset-1.0.1.tgz", "integrity": "sha512-IAQ1mL6SDq5P2HjsT0wy4CU5/3ywc7gJB5qNoHIgekhx0Wt2QSQBSt++JDTzDuhYQurKYTSstK6zwDDUB+e2EQ==", "signatures": [{"sig": "MEQCIBIuWkC7lYW09AM2byemzIbxIOS2udnnRtEYR2wJJOcQAiBQoPG/CUEN8cyhf7afXOyk8E3LhIpH7bkeAGFko7rLrQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "79bf1d4656abd2404f3736c1aaae62d93a73bfdc", "engines": {"node": ">=0.12.0"}, "gitHead": "2c0879d098cab47799a32feef0dd92c07027fb1c", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "art<PERSON><PERSON>r", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arthurvr/is-weakset.git", "type": "git"}, "_npmVersion": "2.9.0", "description": "Easily check if a givin object is an ES6 WeakSet", "directories": {}, "_nodeVersion": "2.0.1", "devDependencies": {"ava": "^0.0.4"}}, "2.0.0": {"name": "is-weakset", "version": "2.0.0", "keywords": ["map", "weakmap", "set", "weakset", "collection", "is", "robust"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-weakset@2.0.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-weakset#readme", "bugs": {"url": "https://github.com/inspect-js/is-weakset/issues"}, "dist": {"shasum": "119577bcf75528e7d5b4c7ebaa4fdee9e2d3e5d7", "tarball": "https://registry.npmjs.org/is-weakset/-/is-weakset-2.0.0.tgz", "fileCount": 9, "integrity": "sha512-bDiKqyPK2GeWiZNsm1zHf0adJJ2I0gNzwjBxu4iajQPgJH3UZiXTGUPMNmbQ3TIBYPs+tVNpiDSF/eF5U4EF7A==", "signatures": [{"sig": "MEUCIENokAs7CHbXr2qJVEyGDDa4Gu39jQhCWOmUMlK6ahXhAiEAie8meNO8y28918eS5AS5Nlnn2ih0ZvqnOTVMUNSsCGE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9046, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdyxTLCRA9TVsSAnZWagAAJf4QAI3VaoCCy2QwHr7Z4iVV\nUCPQRxh+2kqEMmz6h4nari6d7XiSiQJVZ3YpXrkopNWCCCMfpPlk9UZZybfY\nQ9HHqAn6j04z6VwVH02ozN58G2GEYxuNIYCBCNdsQsQf0JMuZmrjKas0nYVn\nsZf4wWDdTHjjvZ/wEzmGN0lXjPI67nb/y2UD5bZ4hDzp4212CZTLrNJ5/tGK\n3fsboJsvjALHAHEXzE46x33x2dQgSD1IDFfRsiyWNgg7tHSGftR961GGDeld\n3cHznz9A/ttbftJ7wal/BJSuLCxpLYCPw629mzTA2uY8Hcb4xtEsO79AfURn\nY3LBvHW0UaO29Wt+C+V6SIMT70FhxapRHnqyyu6S0RRiFxYSsZrWnYs0e3MC\nQal1M++wi4GXG3vNhnDsTIiUdGj+6EnFYHkiFx8BEVnlSPrPGRtTYWsTXCiM\nKCVjj3cOr35BwvkOPcSJBZlQEnSS6ziWhCkJLagCTBTtOAYPwjdmPMzW1eXY\n+sSOGMCQTutCJj7bDLalxc2WH+JsxyIbmMFBlxs9uS5ruY1gO1nLjz1SiE+d\nLkivCIgWyjDkxQ8NKmQZxSFo3Je3Y+UAUjDVccn7+gGEdcstZXu0ikJ4a9RJ\ndafzhAjG/CVyF5n/oC3LdLIDgFUuhMguCJQNgeCJ4OOvFTkyXkRxOwVs6TOG\nVlK+\r\n=erdt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "15d0d42f37269bdbf25f34af0b4cedcc8990bcfc", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "npx aud", "prepublish": "safe-publish-latest", "tests-only": "node test", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "posttests-only": "node -e \"require('es5-shim'); require('es6-shim'); require('./test');\""}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspect-js/is-weakset.git", "type": "git"}, "_npmVersion": "6.12.1", "description": "Is this value a JS WeakSet? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "directories": {}, "_nodeVersion": "13.1.0", "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"tape": "^4.11.0", "eslint": "^6.6.0", "es5-shim": "^4.5.13", "es6-shim": "^0.35.5", "for-each": "^0.3.3", "auto-changelog": "^1.16.2", "object-inspect": "^1.7.0", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^15.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/is-weakset_2.0.0_1573590219293_0.4392444827645412", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "is-weakset", "version": "2.0.1", "keywords": ["map", "weakmap", "set", "weakset", "collection", "is", "robust"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-weakset@2.0.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-weakset#readme", "bugs": {"url": "https://github.com/inspect-js/is-weakset/issues"}, "dist": {"shasum": "e9a0af88dbd751589f5e50d80f4c98b780884f83", "tarball": "https://registry.npmjs.org/is-weakset/-/is-weakset-2.0.1.tgz", "fileCount": 10, "integrity": "sha512-pi4vhbhVHGLxohUw7PhGsueT4vRGFoXhP7+RGN0jKIv9+8PWYCQTqtADngrxOm2g46hoH0+g8uZZBzMrvVGDmw==", "signatures": [{"sig": "MEUCIQDBG4GICygcVytGqRggAB7SvJo8zFlIlCaTOlezvpLgpQIgGSRefF/vLnIKXIRyOR1Sf7HRAqLvJNFBY2cF3lwGqEk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10198, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd+buCCRA9TVsSAnZWagAAEeYP/26JMSixad8Zy4/l8vlL\n8DbfUJS3UYx5HomnjUK3KWvffwvMjXG6+iCBDa4jswoMQd3FfmmlNCueVhCI\n9VysbEq2R9BAuQ8UhPO/4F7zAIGsZCab3Z+UTkct4fvGmKSrktIy1Fkg8uB9\neie/+2ujrvyZXmExlsIsyLH1RmDa3YBZrBtvry8J4d0CufJFI9zjFHSD30ZK\nVNZExlryoydxhs+KxCW2P9KIG1KHDq96xVKdcp86XDN+1RUwKyIfu4YwOPTU\neAZJcic0u9mnywf9+1NxYvCmR3QyEHPM8+Qh+v3KBOEmIVep69pnYdDj/Uia\nBE1Ot4SeXlyideZWMbJLKr5y8De4h0Bd6EMqG1gjG6o7xbNLbs05zWY4eVMu\ncFTFdZwQapT7tAFaBk8JdcZqkcFuwRuuHB3PokbgSFcJc4tBm0XNvDfZ2eto\nYjyUshkGtk9JiAVCvzJYJ+nm16owf0lNrqzAEugic8A81+IF6TSV45Ntui76\nom17vtofbcem/9ubvcQp9FgVi9uSERYmbP5b6cnCni7nGJl93m+AiYFNsd9/\nU07nRTrlC0lLIoJS/3PHOZsPPtqBgLMxwgjRquGvf7sMJukf02XoXBeKJrot\n9yM6nnTQu8aLC9kWFl+zr1q/s/cQCJsJlQWdhXSeS1TRcYf9RweHxF5MvJIB\n6T6w\r\n=SuTg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "ed2a5c1c3424911887a2ef8dea42bea43c7f90ed", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "npx aud", "prepublish": "safe-publish-latest", "tests-only": "node test", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "posttests-only": "node -e \"require('es5-shim'); require('es6-shim'); require('./test');\""}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspect-js/is-weakset.git", "type": "git"}, "_npmVersion": "6.13.1", "description": "Is this value a JS WeakSet? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "directories": {}, "_nodeVersion": "13.3.0", "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"tape": "^4.12.0", "eslint": "^6.7.2", "es5-shim": "^4.5.13", "es6-shim": "^0.35.5", "for-each": "^0.3.3", "auto-changelog": "^1.16.2", "object-inspect": "^1.7.0", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^15.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/is-weakset_2.0.1_1576647554177_0.3211947336392891", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "is-weakset", "version": "2.0.2", "keywords": ["map", "weakmap", "set", "weakset", "collection", "is", "robust"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-weakset@2.0.2", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-weakset#readme", "bugs": {"url": "https://github.com/inspect-js/is-weakset/issues"}, "dist": {"shasum": "4569d67a747a1ce5a994dfd4ef6dcea76e7c0a1d", "tarball": "https://registry.npmjs.org/is-weakset/-/is-weakset-2.0.2.tgz", "fileCount": 12, "integrity": "sha512-t2yVvttHkQktwnNNmBQ98AhENLdPUTDTE21uPqAQ0ARwQfGeQKRVS0NNurH7bTf7RrvcVn1OOge45CnBeHCSmg==", "signatures": [{"sig": "MEYCIQDHJEYzlI9VQ6W2xGFCX5a98KG+5dE0RzryzUUlg+V7+QIhAIz7h7AKo3T2ZAbNHDe4sPBnU69AuO10SnaNk2XyZrKE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15050, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhtq81CRA9TVsSAnZWagAA3jcP/024qHHRmh/bwyL5+NTs\nsfct9DJWrhBACCcHfVa6KllgK+qfQvg16eTbrREcqHLSzWMAvdvOKSIijTUW\nnCh36tubQjTj0LmOVBsmrJ+5hjToewEsqQsZlEWOL85mkFiYXLKKxLjsrTUm\nH1fkiHeD53retd6awFHyvlQgPu7VGE5YNAQJCP9uKjXQVLa2WEF26uPvO7yB\n9Lu0mE62ppKebepLs+fn7NOmziA7NTsM3s/47/EGQfmlZbcnGr3/oCWfxsFQ\ndZ2jkZ9LiJXcK6eB5EFE0gSYbyqhKYipEwy9TUXAx1kgzKk52+O5IXb6lE4h\nyCNvrvPId6EcneKReXDha/I58VU2opmAFhkkH6ZPhjBfDFZRkY/m6j81wHx7\n7MCGobGwveoSmWw6nhEqbDHTq5x3H06Ib4xB+U9r6JFRFPOtPT3mjJ+9mRCv\nGKB3ZGht07kf0XzvgNqHV4/kELaSrt+LzBIDpyCGGUdPTJtwimfGd1tAbY8w\ngmon9e9hvDMtbALPcglJPoSk5L/NYasmUj4SYoTQLT6FsxOX3egdOjO7sorg\nGAbmkiHH8XCuhzbQbgfSSoJgpKex4mU0g0OdomgmrkTNt0+1NQeLyfLQwude\nrTvbid5sTmJL3X+vVlmop85vTG5b8/KJ4ok1WKEGPjv4zkBKrSC/KLaAY5Mf\n0x9f\r\n=v/mw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "68a69d548ade4e1d7112a11dfb79d1be7323f75a", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only && npm run tests:shims && npm run tests:corejs", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "tests:shims": "nyc tape --require=es5-shim --require=es6-shim 'test/**/*.js'", "tests:corejs": "nyc tape --require=core-js 'test/**/*.js'", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspect-js/is-weakset.git", "type": "git"}, "_npmVersion": "8.1.4", "description": "Is this value a JS WeakSet? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "directories": {}, "_nodeVersion": "17.2.0", "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.1.1"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^1.1.5", "nyc": "^10.3.2", "tape": "^5.3.2", "eslint": "^8.4.1", "core-js": "^2.6.12", "es5-shim": "^4.6.2", "es6-shim": "^0.35.6", "for-each": "^0.3.3", "auto-changelog": "^2.3.0", "object-inspect": "^1.11.1", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^20.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-weakset_2.0.2_1639362356859_0.8806669371414737", "host": "s3://npm-registry-packages"}}, "2.0.3": {"name": "is-weakset", "version": "2.0.3", "keywords": ["map", "weakmap", "set", "weakset", "collection", "is", "robust"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-weakset@2.0.3", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-weakset#readme", "bugs": {"url": "https://github.com/inspect-js/is-weakset/issues"}, "dist": {"shasum": "e801519df8c0c43e12ff2834eead84ec9e624007", "tarball": "https://registry.npmjs.org/is-weakset/-/is-weakset-2.0.3.tgz", "fileCount": 13, "integrity": "sha512-LvIm3/KWzS9oRFHugab7d+M/GcBXuXX5xZkzPmN+NxihdQlZUQ4dWuSV1xR/sq6upL1TJEDrfBgRepHFdBtSNQ==", "signatures": [{"sig": "MEUCIAEngrBxyWp+c+Rqyae5AN330SfmmNZPv5hQYY5ZJslwAiEA313ZQzfix6sJ01shugDQanKArJFjx9e9b7EIPoS70mc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21242}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "10b18409d54d25909a23943b6c0461c5477201da", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only && npm run tests:shims && npm run tests:corejs", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "tsc -p . && attw -P", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "tests:shims": "nyc tape --require=es5-shim --require=es6-shim 'test/**/*.js'", "tests:corejs": "nyc tape --require=core-js 'test/**/*.js'", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspect-js/is-weakset.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Is this value a JS WeakSet? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "directories": {}, "sideEffects": false, "_nodeVersion": "21.7.0", "dependencies": {"call-bind": "^1.0.7", "get-intrinsic": "^1.2.4"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.4", "nyc": "^10.3.2", "tape": "^5.7.5", "eslint": "=8.8.0", "core-js": "^2.6.12", "es5-shim": "^4.6.7", "es6-shim": "^0.35.8", "for-each": "^0.3.3", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "next", "@types/tape": "^5.6.4", "auto-changelog": "^2.4.0", "object-inspect": "^1.13.1", "@types/for-each": "^0.3.3", "@types/call-bind": "^1.0.5", "safe-publish-latest": "^2.0.0", "@types/get-intrinsic": "^1.2.2", "@arethetypeswrong/cli": "^0.15.0", "@ljharb/eslint-config": "^21.1.0", "@types/object-inspect": "^1.8.4"}, "_npmOperationalInternal": {"tmp": "tmp/is-weakset_2.0.3_1709929121930_0.7669151720556282", "host": "s3://npm-registry-packages"}}, "2.0.4": {"name": "is-weakset", "version": "2.0.4", "description": "Is this value a JS WeakSet? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "main": "index.js", "sideEffects": false, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run lint", "lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && attw -P", "tests-only": "nyc tape 'test/**/*.js'", "tests:shims": "nyc tape --require=es5-shim --require=es6-shim 'test/**/*.js'", "tests:corejs": "nyc tape --require=core-js 'test/**/*.js'", "test": "npm run tests-only && npm run tests:shims && npm run tests:corejs", "posttest": "npx npm@'>=10.2' audit --production"}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/is-weakset.git"}, "keywords": ["map", "weakmap", "set", "weakset", "collection", "is", "robust"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/is-weakset/issues"}, "homepage": "https://github.com/inspect-js/is-weakset#readme", "dependencies": {"call-bound": "^1.0.3", "get-intrinsic": "^1.2.6"}, "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/for-each": "^0.3.3", "@types/get-intrinsic": "^1.2.3", "@types/object-inspect": "^1.13.0", "@types/tape": "^5.7.0", "auto-changelog": "^2.5.0", "core-js": "^2.6.12", "encoding": "^0.1.13", "es5-shim": "^4.6.7", "es6-shim": "^0.35.8", "eslint": "=8.8.0", "for-each": "^0.3.3", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.3", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "engines": {"node": ">= 0.4"}, "_id": "is-weakset@2.0.4", "gitHead": "3cf98e3fd0236a8457861db81cc7c5b8d1f4106d", "types": "./index.d.ts", "_nodeVersion": "23.4.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-mfcwb6IzQyOKTs84CQMrOwW4gQcaTOAWJ0zzJCl2WSPDrWk/OzDaImWFH3djXhb24g4eudZfLRozAvPGw4d9hQ==", "shasum": "c9f5deb0bc1906c6d6f1027f284ddf459249daca", "tarball": "https://registry.npmjs.org/is-weakset/-/is-weakset-2.0.4.tgz", "fileCount": 13, "unpackedSize": 19794, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQChEZSRi//6pzgqpcfQ1n1vt6BRrBr8E93gL/GW4u6BXwIhALpjgM+zdkRWZR1yvazgCLutmuYq58i3a2lJCD8WoF0Z"}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/is-weakset_2.0.4_1734413863196_0.2574897336775783"}, "_hasShrinkwrap": false}}, "time": {"created": "2015-02-18T12:14:43.897Z", "modified": "2024-12-17T05:37:43.571Z", "1.0.0": "2015-02-18T12:14:43.897Z", "1.0.1": "2015-06-03T17:04:46.271Z", "2.0.0": "2019-11-12T20:23:39.397Z", "2.0.1": "2019-12-18T05:39:14.311Z", "2.0.2": "2021-12-13T02:25:57.003Z", "2.0.3": "2024-03-08T20:18:42.103Z", "2.0.4": "2024-12-17T05:37:43.401Z"}, "bugs": {"url": "https://github.com/inspect-js/is-weakset/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/inspect-js/is-weakset#readme", "keywords": ["map", "weakmap", "set", "weakset", "collection", "is", "robust"], "repository": {"type": "git", "url": "git+https://github.com/inspect-js/is-weakset.git"}, "description": "Is this value a JS WeakSet? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "readme": "# is-weakset <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\nIs this value a JS WeakSet? This module works cross-realm/iframe, and despite ES6 @@toStringTag.\n\n## Example\n\n```js\nvar isWeakSet = require('is-weakset');\nassert(!isWeakSet(function () {}));\nassert(!isWeakSet(null));\nassert(!isWeakSet(function* () { yield 42; return Infinity; });\nassert(!isWeakSet(Symbol('foo')));\nassert(!isWeakSet(1n));\nassert(!isWeakSet(Object(1n)));\n\nassert(!isWeakSet(new Set()));\nassert(!isWeakSet(new WeakMap()));\nassert(!isWeakSet(new Map()));\n\nassert(isWeakSet(new WeakSet()));\n\nclass MyWeakSet extends WeakSet {}\nassert(isWeakSet(new MyWeakSet()));\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n[package-url]: https://npmjs.org/package/is-weakset\n[npm-version-svg]: https://versionbadg.es/inspect-js/is-weakset.svg\n[deps-svg]: https://david-dm.org/inspect-js/is-weakset.svg\n[deps-url]: https://david-dm.org/inspect-js/is-weakset\n[dev-deps-svg]: https://david-dm.org/inspect-js/is-weakset/dev-status.svg\n[dev-deps-url]: https://david-dm.org/inspect-js/is-weakset#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/is-weakset.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/is-weakset.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/is-weakset.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=is-weakset\n[codecov-image]: https://codecov.io/gh/inspect-js/is-weakset/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/inspect-js/is-weakset/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/inspect-js/is-weakset\n[actions-url]: https://github.com/inspect-js/is-weakset/actions\n", "readmeFilename": "README.md"}