{"_id": "for-each", "_rev": "20-1dee361db9fdeb4cb67f86932cc60582", "name": "for-each", "dist-tags": {"latest": "0.3.4"}, "versions": {"0.1.0": {"name": "for-each", "version": "0.1.0", "keywords": [], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "for-each@0.1.0", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}], "homepage": "https://github.com/Raynos/for-each", "bugs": {"url": "https://github.com/Raynos/for-each/issues", "email": "<EMAIL>"}, "dist": {"shasum": "e64d083dfe1ecb48d030b7db2e9eb33f7b02fb45", "tarball": "https://registry.npmjs.org/for-each/-/for-each-0.1.0.tgz", "integrity": "sha512-M9LSDg91XzEJrx4J1tOUeoEIh7IQVhnsrM+Ckw4SglSd90HOQQGbjUg/fFbAnod+t9PP6M5FsEjjbcfcxgVtDA==", "signatures": [{"sig": "MEUCIHjOWtjHhzZJQw/zt1nGN3dXIMVl+qeFXhShG1Di3dYFAiEAhfdBtg+r06rGp3Te42bogyIG3ze2AUpRXojfSS5cCFI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "scripts": {"test": "tap --stderr --tap ./test"}, "_npmUser": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/Raynos/for-each/raw/master/LICENSE", "type": "MIT"}], "repository": {"url": "git://github.com/Raynos/for-each.git", "type": "git"}, "_npmVersion": "1.1.49", "description": "A better for<PERSON>ach", "directories": {}, "dependencies": {}, "devDependencies": {"tap": "~0.3.1"}}, "0.3.1": {"name": "for-each", "version": "0.3.1", "keywords": [], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "for-each@0.3.1", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"url": "https://github.com/ljharb", "name": "<PERSON>"}], "homepage": "https://github.com/Raynos/for-each", "bugs": {"url": "https://github.com/Raynos/for-each/issues", "email": "<EMAIL>"}, "dist": {"shasum": "d24a631395e844c226261e3b30206ef956e1b53f", "tarball": "https://registry.npmjs.org/for-each/-/for-each-0.3.1.tgz", "integrity": "sha512-TyQvgCJxSy+ovg9Lel3YMtRqs0AgBsZti/qg/m8ayXx38qGqugpW+ATQUUXWaOw5L/feXiGXh1r0UCasNEDtIQ==", "signatures": [{"sig": "MEUCIQDqFxNPb9NWgUcWz+h1yAYHmzcynHwo0xDdyOcel9bq6AIgEVlmwt+GG4OtjWfZWL+3vJdb1wShozR2+RERvCDktSo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "scripts": {"test": "node test/test.js"}, "_npmUser": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/Raynos/for-each/raw/master/LICENSE", "type": "MIT"}], "testling": {"files": "test/test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/Raynos/for-each.git", "type": "git"}, "_npmVersion": "1.3.17", "description": "A better for<PERSON>ach", "directories": {}, "dependencies": {}, "readmeFilename": "README.md", "devDependencies": {"tape": "~1.1.0"}}, "0.3.2": {"name": "for-each", "version": "0.3.2", "keywords": [], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "for-each@0.3.2", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"url": "https://github.com/ljharb", "name": "<PERSON>"}], "homepage": "https://github.com/Raynos/for-each", "bugs": {"url": "https://github.com/Raynos/for-each/issues", "email": "<EMAIL>"}, "dist": {"shasum": "2c40450b9348e97f281322593ba96704b9abd4d4", "tarball": "https://registry.npmjs.org/for-each/-/for-each-0.3.2.tgz", "integrity": "sha512-EZ<PERSON>ij37gJU2yEqysbZ2EcCrEtROFxG+qT1uTVfMnwnHsz9Z1yqkuSmZaYzyLY6P1VzlVPzP2C5HfUSC0CyxcMw==", "signatures": [{"sig": "MEUCIFaRH+GWVlC6L+GF+9UqFDeMi2oVpCqDVYgS7MUO+P+mAiEAnEEJRGKWQD1x0bxfPMeV9Fws9KCSmwwcUXw6SAjIUc4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "scripts": {"test": "node test/test.js"}, "_npmUser": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/Raynos/for-each/raw/master/LICENSE", "type": "MIT"}], "testling": {"files": "test/test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/Raynos/for-each.git", "type": "git"}, "_npmVersion": "1.3.17", "description": "A better for<PERSON>ach", "directories": {}, "dependencies": {"is-function": "~1.0.0"}, "readmeFilename": "README.md", "devDependencies": {"tape": "~1.1.0"}}, "0.3.3": {"name": "for-each", "version": "0.3.3", "keywords": [], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "for-each@0.3.3", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"url": "https://github.com/ljharb", "name": "<PERSON>"}], "homepage": "https://github.com/Raynos/for-each", "bugs": {"url": "https://github.com/Raynos/for-each/issues", "email": "<EMAIL>"}, "dist": {"shasum": "69b447e88a0a5d32c3e7084f3f1710034b21376e", "tarball": "https://registry.npmjs.org/for-each/-/for-each-0.3.3.tgz", "fileCount": 9, "integrity": "sha512-jqYfLp7mo9vIyQf8ykW2v7A+2N4QjeCeI5+Dz9XraiO1ign81wjiH7Fb9vSOWvQfNtmSa4H2RoQTrrXivdUZmw==", "signatures": [{"sig": "MEQCIFmnfur02t2zyB61hgHEHbo+1LdxjTcIzl/R0Egijv7QAiBWFgficrLKnGk4caPrKkhSqwAiI49Z92RMNCOUAuqpug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13033, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbEiuRCRA9TVsSAnZWagAA2aUP/1cRKERbqJF6aH9E1IPq\n9fyzb8YbJ9eBdsCzc/N34Z9tIXUye7RdvteMIDcXcBxKlXn68Uq6xrGD0DLi\nIOA+uGyVXsw8hUDB1EnAhKurjUOTLs3ygmBqQGQPVeExOzqyxpGqXI3rF5EW\ngzgKCPFGa8ZOcEXys4Im09SHI0tsW4Ns/lEwqf7SH22wcuZybBoI6J04Tuh3\nVpUJdqSVWsBBlOcdoHj9pyHqjD6JHE65JRiLxhWsuuVDzgWnbaTSfJrBpRNA\n94Jku+KgteDorHHF3+cbDZe+L4C0o6AenEscRzMStqjfLL6wyjMKowMR4YY+\nyrffiZ3GLyl7NpJQhEKHxGeypIrJludOS4u9RWhG1GO1fZgqOxAbGCve9Mo/\nUEn79sq/LvUybjH9eSOuEcs/DApEtmMObb0cOidES1VfOLbQCaOKqQ4KawX8\nZmVLCm2aI3giQKOcthvJZxFvCNH8lwQbrmHZdyLh1BIvqs8/YXgsFI4hwKKG\nPdaNdYBZgEgqdAXwTtNXYLBL60KqK182sCchEUxmVAQelkS+2aArJHgOb1hz\nVKhybdSs/Cc55/Bd7KqOAyrZGRFxMUoGP350/X1A+37+Rk2up+38lX3VWrBl\nSaLbwtkeJZE/Vm1ojAbXOTwx6UVEvuqCgSXNFfuh3cYSXRNj8R3kbIS9+B9q\nc9g2\r\n=rUoa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "gitHead": "7adaf8162a12d55f6ea3fb0bf5e999035dfd303a", "scripts": {"lint": "eslint *.js test/*.js", "test": "npm run tests-only", "pretest": "npm run lint", "posttest": "npm run security", "security": "nsp check", "tests-only": "node test/test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/Raynos/for-each/raw/master/LICENSE", "type": "MIT"}], "testling": {"files": "test/test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/Raynos/for-each.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "A better for<PERSON>ach", "directories": {}, "_nodeVersion": "10.3.0", "dependencies": {"is-callable": "^1.1.3"}, "_hasShrinkwrap": false, "devDependencies": {"nsp": "^3.2.1", "tape": "^4.9.0", "eslint": "^4.19.1", "@ljharb/eslint-config": "^12.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/for-each_0.3.3_1527917456605_0.15636549848965142", "host": "s3://npm-registry-packages"}}, "0.3.4": {"name": "for-each", "version": "0.3.4", "description": "A better for<PERSON>ach", "keywords": [], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/Raynos/for-each.git"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "main": "index", "homepage": "https://github.com/Raynos/for-each", "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ljharb"}], "bugs": {"url": "https://github.com/Raynos/for-each/issues", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/*.js'", "posttest": "npx npm@\">= 10.2\" audit --production", "lint": "eslint --ext=js,mjs .", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "dependencies": {"is-callable": "^1.2.7"}, "devDependencies": {"@ljharb/eslint-config": "^21.1.1", "auto-changelog": "^2.5.0", "eslint": "=8.8.0", "npmignore": "^0.3.1", "nyc": "^10.3.2", "tape": "^5.9.0"}, "testling": {"files": "test/test.js"}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "_id": "for-each@0.3.4", "gitHead": "71fd9c1ae8422b94d492ba4014630b167e3e9b7c", "_nodeVersion": "23.6.1", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-kKaIINnFpzW6ffJNDjjyjrk21BkDx38c0xa/klsT8VzLCaMEefv4ZTacrcVR4DmgTeBra++jMDAfS/tS799YDw==", "shasum": "814517ffc303d1399b2564d8165318e735d0341c", "tarball": "https://registry.npmjs.org/for-each/-/for-each-0.3.4.tgz", "fileCount": 11, "unpackedSize": 21284, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIF87i2aKwrOYaVMkoXscVpNCdsfpIdqeQJl7r3Z2vMPBAiB5gQfCP00Zvjf47RYj/3j33IzNi1ucRrTDIE7pWdwkDQ=="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/for-each_0.3.4_1737738876071_0.9435316664496038"}, "_hasShrinkwrap": false}}, "time": {"created": "2012-09-29T06:32:20.980Z", "modified": "2025-01-24T17:14:36.435Z", "0.1.0": "2012-09-29T06:32:22.249Z", "0.3.1": "2014-01-07T03:25:23.023Z", "0.3.2": "2014-01-07T21:27:50.214Z", "0.3.3": "2018-06-02T05:30:56.680Z", "0.3.4": "2025-01-24T17:14:36.252Z"}, "bugs": {"url": "https://github.com/Raynos/for-each/issues", "email": "<EMAIL>"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/Raynos/for-each", "keywords": [], "repository": {"type": "git", "url": "git+https://github.com/Raynos/for-each.git"}, "description": "A better for<PERSON>ach", "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ljharb"}], "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "readme": "# for-each [![build status][1]][2]\n\n[![browser support][3]][4]\n\nA better forEach.\n\n## Example\n\nLike `Array.prototype.forEach` but works on objects.\n\n```js\nvar forEach = require(\"for-each\")\n\nforEach({ key: \"value\" }, function (value, key, object) {\n    /* code */\n})\n```\n\nAs a bonus, it's also a perfectly function shim/polyfill for arrays too!\n\n```js\nvar forEach = require(\"for-each\")\n\nforEach([1, 2, 3], function (value, index, array) {\n    /* code */\n})\n```\n\n## Installation\n\n`npm install for-each`\n\n## MIT Licenced\n\n  [1]: https://secure.travis-ci.org/Raynos/for-each.png\n  [2]: http://travis-ci.org/Raynos/for-each\n  [3]: https://ci.testling.com/Raynos/for-each.png\n  [4]: https://ci.testling.com/Raynos/for-each\n\n", "readmeFilename": "README.md", "users": {"ljharb": true, "mwyatt": true, "nichoth": true, "mojaray2k": true, "flumpus-dev": true, "jamescostian": true, "shekharreddy": true}}