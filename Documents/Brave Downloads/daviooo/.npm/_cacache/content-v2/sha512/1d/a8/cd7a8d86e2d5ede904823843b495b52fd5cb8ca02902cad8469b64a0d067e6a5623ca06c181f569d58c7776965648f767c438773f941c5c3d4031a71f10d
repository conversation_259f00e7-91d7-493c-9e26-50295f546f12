{"_id": "audit", "_rev": "14-df51d05c44e5bf9e990c04a791d9f30b", "name": "audit", "description": "Generate performance statistics for async or sync functions", "dist-tags": {"latest": "0.0.6"}, "versions": {"0.0.1": {"name": "audit", "version": "0.0.1", "description": "Generate performance statistics for async or sync functions", "keywords": ["bench", "audit", "performance"], "repository": {"type": "git", "url": "git://github.com/Weltschmerz/Audit.git"}, "author": {"name": "Weltschmerz", "email": "<EMAIL>"}, "main": "lib/audit.js", "engines": {"node": ">= 0.5.0"}, "readme": "", "_id": "audit@0.0.1", "dist": {"shasum": "d1c192dd0adeb8faa52991a25a21a9be60d130e2", "tarball": "https://registry.npmjs.org/audit/-/audit-0.0.1.tgz", "integrity": "sha512-GPdA17ixaiWcl7ThVRPaullQK0z3SEBQOJGWmKP2GDwWRTg/u14tL71kz5HlsyAzmQC9WZ3yidkXqzpjskjtyg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEdWbupQCN92/pjaGkpxfOcgzp4Rys/mJOcvcXcHgIQvAiEA5gSeBA59INoZ/5alEDYN55AiRZri+8OFnYi8m0I0XTo="}]}, "maintainers": [{"name": "Weltschmerz", "email": "<EMAIL>"}]}, "0.0.2": {"name": "audit", "version": "0.0.2", "description": "Generate performance statistics for async or sync functions", "keywords": ["bench", "audit", "performance"], "repository": {"type": "git", "url": "git://github.com/Weltschmerz/Audit.git"}, "author": {"name": "Weltschmerz", "email": "<EMAIL>"}, "dependencies": {"vows": "0.6.3", "should": "1.0.0"}, "main": "lib/audit.js", "scripts": "make test", "engines": {"node": ">= 0.5.0"}, "readme": "\n# Audit\n\n`npm install audit`\n\n`npm test audit`\n\nAudit is a benchmarking tool for asynchronous or synchronous functions. It generates the following statistics:\n\n* elapsed time\n* iterations\n* operations per second\n* mean\n* median\n* mode\n* max\n* min\n\n\n## Basic usage\n\n```js\nvar audit = require('audit')({\n    iterations:100\n})\n\naudit.on('complete', function(stats) {\n    console.log(stats)\n})\n\naudit.on('auditing', function(name) {\n    console.log('Auditing', name, '...')\n})\n\naudit.on('auditcomplete', function(stats) {\n    console.log('Completed', stats.name)\n})\n\naudit.async(function(next) {\n    return next()\n})\n\naudit.sync(function() {\n    return 1\n})\n\naudit.run()\n```\n\n## Stats\n\nThe stats object contains the following properties:\n\n* `name`\n* `elapsed`\n* `iterations`\n* `opsPerSecond`\n* `mode`\n* `median`\n* `mean`\n* `max`\n* `min`\n\nSample output:\n\n```\nBenching musicmetadata...\nmusicmetadata 23.12673450508788\nBenching child-ffmpeg...\nchild-ffmpeg 7.58150113722517\n{\n    \"musicmetadata\": {\n        \"name\": \"musicmetadata\",\n        \"elapsed\": \"4324ms\",\n        \"iterations\": 100,\n        \"opsPerSecond\": 23.12673450508788,\n        \"mode\": [ \"19ms\", \"1occ\" ],\n        \"median\": \"45ms\",\n        \"mean\": \"43.676767676767675ms\",\n        \"max\": [ \"61ms\", \"7ind\" ],\n        \"min\": [ \"19ms\", \"0ind\" ]\n},\n    \"child-ffmpeg\": {\n        \"name\": \"child-ffmpeg\",\n        \"elapsed\": \"13190ms\",\n        \"iterations\": 100,\n        \"opsPerSecond\": 7.58150113722517,\n        \"mode\": [ \"128ms\", \"1occ\" ],\n        \"median\": \"131ms\",\n        \"mean\": \"133.23232323232324ms\",\n        \"max\": [ \"221ms\", \"0ind\" ],\n        \"min\": [ \"128ms\", \"19ind\" ]\n    }\n}\n\n```\n\n## Options\n\nPass options to the audit constructor function in an object. Options and their default values follow.\n\n* `iterations` **Number** *1000*\n\nNumber of times to execute the provided functions\n\n* `pause` **Number** *100*\n\nNumber of milliseconds to pause between audits.\n", "_id": "audit@0.0.2", "dist": {"shasum": "31dc6b91878caccb22d11ad78d7e6d02a4b757d9", "tarball": "https://registry.npmjs.org/audit/-/audit-0.0.2.tgz", "integrity": "sha512-CcwhIpbWQzAurooJwVPyvUhHHI2wN3Uwitn+TUifMQXWjCcO6/YNX+DfMrQKHj5Wi2MqAtEZKjeqZr4EoE2Xfg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCslQX8AFeacznhDdbWm/Rs9/j2pSMslpnj4Vq2VJvV7wIhAKfKudAOLN9QsAj8j/CzOdbL4lrI3frwxSS1chmMj4jq"}]}, "maintainers": [{"name": "Weltschmerz", "email": "<EMAIL>"}]}, "0.0.3": {"name": "audit", "version": "0.0.3", "description": "Generate performance statistics for async or sync functions", "keywords": ["bench", "audit", "performance"], "repository": {"type": "git", "url": "git://github.com/Weltschmerz/Audit.git"}, "author": {"name": "Weltschmerz", "email": "<EMAIL>"}, "dependencies": {"vows": "0.6.3", "should": "1.0.0"}, "main": "lib/audit.js", "scripts": "make test", "engines": {"node": ">= 0.5.0"}, "readme": "\n# Audit\n\n`npm install audit`\n\n`npm test audit`\n\nAudit is a benchmarking tool for asynchronous or synchronous functions. It generates the following statistics:\n\n* elapsed time\n* iterations\n* operations per second\n* mean\n* median\n* mode\n* max\n* min\n\n\n## Basic usage\n\n```js\nvar audit = require('audit')({\n    iterations:100\n})\n\naudit.on('complete', function(stats) {\n    console.log(stats)\n})\n\naudit.on('auditing', function(name) {\n    console.log('Auditing', name, '...')\n})\n\naudit.on('auditcomplete', function(stats) {\n    console.log('Completed', stats.name)\n})\n\naudit.async(function(next) {\n    return next()\n})\n\naudit.sync(function() {\n    return 1\n})\n\naudit.run()\n```\n\n## Stats\n\nThe stats object contains the following properties:\n\n* `name`\n* `elapsed`\n* `iterations`\n* `opsPerSecond`\n* `mode`\n* `median`\n* `mean`\n* `max`\n* `min`\n\nSample output:\n\n```\nBenching musicmetadata...\nmusicmetadata 23.12673450508788\nBenching child-ffmpeg...\nchild-ffmpeg 7.58150113722517\n{\n    \"musicmetadata\": {\n        \"name\": \"musicmetadata\",\n        \"elapsed\": \"4324ms\",\n        \"iterations\": 100,\n        \"opsPerSecond\": 23.12673450508788,\n        \"mode\": [ \"19ms\", \"1occ\" ],\n        \"median\": \"45ms\",\n        \"mean\": \"43.676767676767675ms\",\n        \"max\": [ \"61ms\", \"7ind\" ],\n        \"min\": [ \"19ms\", \"0ind\" ]\n},\n    \"child-ffmpeg\": {\n        \"name\": \"child-ffmpeg\",\n        \"elapsed\": \"13190ms\",\n        \"iterations\": 100,\n        \"opsPerSecond\": 7.58150113722517,\n        \"mode\": [ \"128ms\", \"1occ\" ],\n        \"median\": \"131ms\",\n        \"mean\": \"133.23232323232324ms\",\n        \"max\": [ \"221ms\", \"0ind\" ],\n        \"min\": [ \"128ms\", \"19ind\" ]\n    }\n}\n\n```\n\n## Options\n\nPass options to the audit constructor function in an object. Options and their default values follow.\n\n* `iterations` **Number** *1000*\n\nNumber of times to execute the provided functions\n\n* `pause` **Number** *100*\n\nNumber of milliseconds to pause between audits.\n", "_id": "audit@0.0.3", "dist": {"shasum": "e8252ebc93f2660f41628ea22ca7970bef8c2335", "tarball": "https://registry.npmjs.org/audit/-/audit-0.0.3.tgz", "integrity": "sha512-U8x+Ho4E16wcDMnFttK74R8DMP19U5IYe7iK5s4gNnz5RvJ4bcW+XgZzdu8/SXNLgHqF9Wr/WM5tD5lR7Dv/Ag==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDRXCIiT3CUdrmsTkNwJOh6OiCflg/DUf9+4CvoHATt+AIhAPbOMYNHQEEWglz4BcQN+n7eHKZxfJa0zQnggp+I68/Z"}]}, "maintainers": [{"name": "Weltschmerz", "email": "<EMAIL>"}]}, "0.0.4": {"name": "audit", "version": "0.0.4", "description": "Generate performance statistics for async or sync functions", "keywords": ["bench", "audit", "performance"], "repository": {"type": "git", "url": "git://github.com/Weltschmerz/Audit.git"}, "author": {"name": "Weltschmerz", "email": "<EMAIL>"}, "dependencies": {"vows": "0.6.3", "should": "1.0.0"}, "main": "lib/audit.js", "scripts": {"test": "make test"}, "engines": {"node": ">= 0.5.0"}, "readme": "\n# Audit\n\n`npm install audit`\n\n`npm test audit`\n\nAudit is a benchmarking tool for asynchronous or synchronous functions. It generates the following statistics:\n\n* elapsed time\n* iterations\n* operations per second\n* mean\n* median\n* mode\n* max\n* min\n\n\n## Basic usage\n\n```js\nvar audit = require('audit')({\n    iterations:100\n})\n\naudit.on('complete', function(stats) {\n    console.log(stats)\n})\n\naudit.on('auditing', function(name) {\n    console.log('Auditing', name, '...')\n})\n\naudit.on('auditcomplete', function(stats) {\n    console.log('Completed', stats.name)\n})\n\naudit.async(function(next) {\n    return next()\n})\n\naudit.sync(function() {\n    return 1\n})\n\naudit.run()\n```\n\n## Stats\n\nThe stats object contains the following properties:\n\n* `name`\n* `elapsed`\n* `iterations`\n* `opsPerSecond`\n* `mode`\n* `median`\n* `mean`\n* `max`\n* `min`\n\nSample output:\n\n```\nBenching musicmetadata...\nmusicmetadata 23.12673450508788\nBenching child-ffmpeg...\nchild-ffmpeg 7.58150113722517\n{\n    \"musicmetadata\": {\n        \"name\": \"musicmetadata\",\n        \"elapsed\": \"4324ms\",\n        \"iterations\": 100,\n        \"opsPerSecond\": 23.12673450508788,\n        \"mode\": [ \"19ms\", \"1occ\" ],\n        \"median\": \"45ms\",\n        \"mean\": \"43.676767676767675ms\",\n        \"max\": [ \"61ms\", \"7ind\" ],\n        \"min\": [ \"19ms\", \"0ind\" ]\n},\n    \"child-ffmpeg\": {\n        \"name\": \"child-ffmpeg\",\n        \"elapsed\": \"13190ms\",\n        \"iterations\": 100,\n        \"opsPerSecond\": 7.58150113722517,\n        \"mode\": [ \"128ms\", \"1occ\" ],\n        \"median\": \"131ms\",\n        \"mean\": \"133.23232323232324ms\",\n        \"max\": [ \"221ms\", \"0ind\" ],\n        \"min\": [ \"128ms\", \"19ind\" ]\n    }\n}\n\n```\n\n## Options\n\nPass options to the audit constructor function in an object. Options and their default values follow.\n\n* `iterations` **Number** *1000*\n\nNumber of times to execute the provided functions\n\n* `pause` **Number** *100*\n\nNumber of milliseconds to pause between audits.\n", "_id": "audit@0.0.4", "dist": {"shasum": "eb777068c9eaee39b4e7e9036496d0f9160e92a9", "tarball": "https://registry.npmjs.org/audit/-/audit-0.0.4.tgz", "integrity": "sha512-nV6bf549SXiV97svJ9bD8xVuyrnOKy3nSt1Tl5pW3M/+ndUR4u37eTUnJYqTZxM1zb4NAZ4pzeeV9mH22d/e0A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFGo0acqYPjKDgfNtiD1rSvMMTfdxykOCCADMpUeIKexAiEA5wEiSaG3xZkgflGUDjSJ9rBNg/nyzU/nfW92Ev0hGRs="}]}, "maintainers": [{"name": "Weltschmerz", "email": "<EMAIL>"}]}, "0.0.6": {"name": "audit", "version": "0.0.6", "description": "Generate performance statistics for async or sync functions", "keywords": ["bench", "audit", "performance"], "repository": {"type": "git", "url": "git://github.com/Weltschmerz/Audit.git"}, "author": {"name": "Weltschmerz", "email": "<EMAIL>"}, "devDependencies": {"vows": "0.6.3", "should": "1.0.0"}, "main": "lib/audit.js", "scripts": {"test": "make test"}, "engines": {"node": ">= 0.5.0"}, "readme": "\n# Audit\n\n`npm install audit`\n\nAudit is a benchmarking tool for asynchronous or synchronous functions. It generates the following statistics:\n\n* elapsed time\n* iterations\n* operations per second\n* mean\n* median\n* mode\n* max\n* min\n\n\n## Basic usage\n\n```js\nvar Audit = require('audit')\nvar audit = new Audit({\n    iterations:100\n})\n\naudit.on('complete', function(stats) {\n    console.log(stats)\n})\n\naudit.on('auditing', function(name) {\n    console.log('Auditing', name, '...')\n})\n\naudit.on('auditcomplete', function(stats) {\n    console.log('Completed', stats.name)\n})\n\naudit.async(function(next) {\n    return next()\n})\n\naudit.sync(function() {\n    return 1\n})\n\naudit.run()\n```\n\n## Chaining\n\n```js\nvar Audit = require('audit')\nvar audit = new Audit()\n\naudit.on('complete', console.log)\n\naudit\n.async('mytestname', function(next) {\n    return next()\n})\n.async('mytestname2', function(next) {\n    return next()\n})\n.async('mytestname3', function(next) {\n    return next()\n})\n.run()\n\n```\n\n## Stats\n\nThe stats object contains the following properties:\n\n* `name`\n* `elapsed`\n* `iterations`\n* `opsPerSecond`\n* `mode`\n* `median`\n* `mean`\n* `max`\n* `min`\n\nSample output:\n\n```\nBenching musicmetadata...\nmusicmetadata 23.12673450508788\nBenching child-ffmpeg...\nchild-ffmpeg 7.58150113722517\n{\n    \"musicmetadata\": {\n        \"name\": \"musicmetadata\",\n        \"elapsed\": \"4324ms\",\n        \"iterations\": 100,\n        \"opsPerSecond\": 23.12673450508788,\n        \"mode\": [ \"19ms\", \"1occ\" ],\n        \"median\": \"45ms\",\n        \"mean\": \"43.676767676767675ms\",\n        \"max\": [ \"61ms\", \"7ind\" ],\n        \"min\": [ \"19ms\", \"0ind\" ]\n},\n    \"child-ffmpeg\": {\n        \"name\": \"child-ffmpeg\",\n        \"elapsed\": \"13190ms\",\n        \"iterations\": 100,\n        \"opsPerSecond\": 7.58150113722517,\n        \"mode\": [ \"128ms\", \"1occ\" ],\n        \"median\": \"131ms\",\n        \"mean\": \"133.23232323232324ms\",\n        \"max\": [ \"221ms\", \"0ind\" ],\n        \"min\": [ \"128ms\", \"19ind\" ]\n    }\n}\n\n```\n\n## Options\n\nPass options to the audit constructor function in an object. Options and their default values follow.\n\n* `iterations` **Number** *1000*\n\nNumber of times to execute the provided functions\n\n* `pause` **Number** *100*\n\nNumber of milliseconds to pause between audits.\n", "_id": "audit@0.0.6", "dist": {"shasum": "fef17dd84af1dcd2a5633fd46d3b1adcb084bf3b", "tarball": "https://registry.npmjs.org/audit/-/audit-0.0.6.tgz", "integrity": "sha512-xgv3Y3RIYE00N2/xk10VLlwFd1kjc7FRaX1vC8+CsOfDRe53a06vOSkp91BOSNijZfddYum47a1Fvju/2+JPcw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCIMf36c/Gm/NF7HlYrczgLxCyUBRS71+oLwpg/rMkz7wIhALIGa741j60X+EM3ZMQITjhEhyz5J/oxfCK1msC3X9ss"}]}, "maintainers": [{"name": "Weltschmerz", "email": "<EMAIL>"}]}}, "readme": "", "maintainers": [{"name": "Weltschmerz", "email": "<EMAIL>"}], "time": {"modified": "2022-06-13T03:44:07.023Z", "created": "2012-07-19T00:29:14.198Z", "0.0.1": "2012-07-19T00:29:14.493Z", "0.0.2": "2012-07-21T19:50:25.699Z", "0.0.3": "2012-07-21T19:54:50.648Z", "0.0.4": "2012-07-21T19:56:50.640Z", "0.0.6": "2012-07-23T18:56:21.840Z"}, "author": {"name": "Weltschmerz", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/Weltschmerz/Audit.git"}, "users": {"fgribreau": true}}