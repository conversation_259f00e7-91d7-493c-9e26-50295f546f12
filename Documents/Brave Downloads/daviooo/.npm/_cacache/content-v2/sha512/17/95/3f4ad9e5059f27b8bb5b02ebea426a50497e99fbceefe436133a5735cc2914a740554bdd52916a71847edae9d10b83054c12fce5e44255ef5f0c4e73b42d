{"_id": "@kurkle/color", "_rev": "17-10c4ec8a1b1e0814efd015d5059d2c59", "name": "@kurkle/color", "dist-tags": {"latest": "0.3.4"}, "versions": {"0.1.0": {"name": "@kurkle/color", "version": "0.1.0", "keywords": ["color", "colour", "css", "hsl", "hex", "rgb", "rgba", "hwb", "hsv", "cmyk"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@kurkle/color@0.1.0", "maintainers": [{"name": "kurkle", "email": "<EMAIL>"}], "homepage": "https://github.com/kurkle/color#readme", "bugs": {"url": "https://github.com/kurkle/color/issues"}, "dist": {"shasum": "b60d7bc39a433e777052a08410499ede4f01b55a", "tarball": "https://registry.npmjs.org/@kurkle/color/-/color-0.1.0.tgz", "fileCount": 7, "integrity": "sha512-IsjPEE68JSSVZVbrhSdnFT1nwKRhWnkmF8Q9RqU7jU04NCUWfXX+MA+kE0fTLWru/hDalSX4uAFC4kNMLi136w==", "signatures": [{"sig": "MEUCICbCRabt6mpbqO8QclJHNZsraxPDaSb76KTlSijc+SZyAiEAwme7bk5SBixZPu2kzI9PrA6zkKr0SOYTKK1vPZf21Bc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49061, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeOx6xCRA9TVsSAnZWagAAXbUQAJLRInqnBAolJqHl2tP3\njJk2STjhE9SogAuEBJ4vyQs16GH13Eon9DtL2CTofX7DxhoxEV9SuZHM3r9K\nVzB62oUQIBAxT+NJQeAu+/oY8mjfzNt2u4pZXp+ZUddN6sJ54aCyxZq4TM+L\nmJi4+F2UNq98Z4Hqg/JykeVT9A3iIR1//JGvlYflgLvP4H6r4gPC9cKJItI6\n/dld7lTIPRwMpX4kpUTzM4T1mAELHbbNuR1KNuPlG93RmQjF9SNks+CX20aX\nG2YdIUe1xecQT/X4GZsG3v3LoF8gY8wHqaY4c26LfU7g8x34YGUHBm3zAvwA\nw8XTnEHEBgIGuKx4tHyaPxXLoqqoddMo1e4QM3h+HOUwHp2yxV5M+T1/eDes\nNZwaA2hFYafo4Kni61s9hb5tLfkbRxx4zfiffTmdcYvEjqGYI0Y8/nRkAG/3\n/gVJY767O0E347SfHx8jHXQDcfZj1HjceOKOtqViLUzL0Q9bDcwaSnNK5ePv\nBTwZsSijG+zYnU4MGSnujSWuAuhz6INDbOLqmCYUFAqrjcpe+vcdrpxhDUIr\nbcxhsuTqaYpGVUxwYtFSF0mcdXg8sB0UaKrk/LbcN8q3aaPhLceje19L35Ld\n6W7r+t2qJALaGv3I1WI26LaPSWqk1ye28TperMV7jK49g+XkZ2+od9cucCug\njW+Q\r\n=rkdG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/color.js", "module": "dist/color.esm.js", "gitHead": "fc803fc19e5ce6ab23f73eac8b292165a49f1752", "scripts": {"test": "node test/index.js"}, "_npmUser": {"name": "kurkle", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kurkle/color.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "css color parsing, manupulation and conversion", "directories": {}, "_nodeVersion": "12.13.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"fs": "0.0.2", "gulp": "^4.0.2", "util": "^0.12.1", "assert": "^2.0.0", "eslint": "^6.1.0", "rollup": "^1.19.4", "benchmark": "^2.1.4", "chroma-js": "^2.0.4", "color-name": "^1.1.4", "perf_hooks": "0.0.1", "tinycolor2": "^1.4.1", "color-names": "^2.0.0", "color-parse": "^1.3.8", "gulp-eslint": "^6.0.0", "babel-eslint": "^10.0.2", "color-parser": "^0.1.0", "color-string": "^1.5.3", "chartjs-color": "^2.4.1", "child_process": "^1.0.2", "csscolorparser": "^1.0.3", "eslint-plugin-react": "^7.14.3", "chartjs-color-string": "^0.6.0", "eslint-plugin-import": "^2.18.2", "rollup-plugin-terser": "^5.1.1", "eslint-config-chartjs": "^0.1.0", "eslint-config-defaults": "^9.0.0", "rollup-plugin-commonjs": "^10.0.2", "rollup-plugin-istanbul": "^2.0.1", "rollup-plugin-node-resolve": "^5.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/color_0.1.0_1580932785066_0.987886983181804", "host": "s3://npm-registry-packages"}}, "0.1.1": {"name": "@kurkle/color", "version": "0.1.1", "keywords": ["color", "colour", "css", "hsl", "hex", "rgb", "rgba", "hwb", "hsv", "cmyk"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@kurkle/color@0.1.1", "maintainers": [{"name": "kurkle", "email": "<EMAIL>"}], "homepage": "https://github.com/kurkle/color#readme", "bugs": {"url": "https://github.com/kurkle/color/issues"}, "dist": {"shasum": "2e5b3bf8ab4be2a534ffd7fc76791c3148a41cdd", "tarball": "https://registry.npmjs.org/@kurkle/color/-/color-0.1.1.tgz", "fileCount": 7, "integrity": "sha512-rpgJFEVJQNwCLSPiBbSSzR3ujRFXuwTRJEzYEto5/L5hsvEzoEHq0M72A6GJC5tWO/CF+tlS5Ii9hUb+Geiu1A==", "signatures": [{"sig": "MEUCIF2d5xTISFxzqON2/o1Tt5MJV8zE/6kx4c9BHnO5RyYYAiEAsbZBOyqT9UpMqeB3szgSaCIYqjHJTrnr6uSTgCpZN2A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49605, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeO0EtCRA9TVsSAnZWagAAPZoP+wVRkDI5Nv9XvgHcZ4yy\nwNPDvny0fB0GcA79wdWthpQzCp71KWaAfJm0+Cwtl6xG5aVSz47pcdIKahFE\n0ybKNV2d3wBp35uZQV0EHBsQQkgmuDIY35WUxO2boL8FMXilY7vQraXRgOTJ\nT2mQxfBen6PNWZN4cFmmZQFF15wiQbbjXaup4UnM3tnOyEER7brOIOSDbTck\nrIEWtV85xin/e441gfkSj3BD0DSVtooMw5F7qqrzZKJXQ0E81Tdrway/uBaD\nS6MXuI7HTzxMN3ymbwKBs8iaf4yjQrP5jCkQIdVYbnghFzdto25VADNzmmyQ\nxUHdy4nzXo+fvFLO1E3l3n4LUFMiwMKakUg/BX382tSo2qgR1V8ksZrOQso+\nzhxE1qn/xk1R58h9Cepqtj77xUw4XbBcRxcy4AGMjSRJ2IFNSgRPw0Vs6Pvz\n9f1gMKiPBwKsF5NKRFYM7sTpUm3xU5WczA2+NkmJcbtZFIfSp6+LqA5YFxQV\nY3V026XP6wRJxBCOXJxjZnHG5AXDAvC6X36/eniuSjuTy1xqzbfUj70KR4b3\n7nNhvoOFw3rd6DdBDW9/JJ1lUNwSPe++8wQu5gffVQ9LfpH29dKNTZY1DkRj\nAR5L7IMkz+XaxmoppXvuvkeVBoKsM0oKBgYhdIZ1/lcHs1/WhT9NTFEABO2l\nyKx7\r\n=ZHJg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/color.js", "module": "dist/color.esm.js", "gitHead": "d2d66a037f083c0fcbc3b6a9d6e5db79ec20be28", "scripts": {"test": "node test/index.js"}, "_npmUser": {"name": "kurkle", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kurkle/color.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "css color parsing, manupulation and conversion", "directories": {}, "_nodeVersion": "12.13.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"fs": "0.0.2", "gulp": "^4.0.2", "util": "^0.12.1", "assert": "^2.0.0", "eslint": "^6.1.0", "rollup": "^1.19.4", "benchmark": "^2.1.4", "chroma-js": "^2.0.4", "color-name": "^1.1.4", "perf_hooks": "0.0.1", "tinycolor2": "^1.4.1", "color-names": "^2.0.0", "color-parse": "^1.3.8", "gulp-eslint": "^6.0.0", "babel-eslint": "^10.0.2", "color-parser": "^0.1.0", "color-string": "^1.5.3", "chartjs-color": "^2.4.1", "child_process": "^1.0.2", "csscolorparser": "^1.0.3", "eslint-plugin-react": "^7.14.3", "chartjs-color-string": "^0.6.0", "eslint-plugin-import": "^2.18.2", "rollup-plugin-terser": "^5.1.1", "eslint-config-chartjs": "^0.1.0", "eslint-config-defaults": "^9.0.0", "rollup-plugin-commonjs": "^10.0.2", "rollup-plugin-istanbul": "^2.0.1", "rollup-plugin-node-resolve": "^5.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/color_0.1.1_1580941612641_0.5894413092542974", "host": "s3://npm-registry-packages"}}, "0.1.2": {"name": "@kurkle/color", "version": "0.1.2", "keywords": ["color", "colour", "css", "hsl", "hex", "rgb", "rgba", "hwb", "hsv", "cmyk"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@kurkle/color@0.1.2", "maintainers": [{"name": "kurkle", "email": "<EMAIL>"}], "homepage": "https://github.com/kurkle/color#readme", "bugs": {"url": "https://github.com/kurkle/color/issues"}, "dist": {"shasum": "5a70cee86980142a36328db9b3478eed3595e175", "tarball": "https://registry.npmjs.org/@kurkle/color/-/color-0.1.2.tgz", "fileCount": 3, "integrity": "sha512-bDwmhgT9rNXewdhn/rbdi0N0HpgQRZfyFrdXqqNDfSp0aX0tZars6XLgNWZayEz951Jmu8dWS9nQUpUs4mNa/g==", "signatures": [{"sig": "MEUCIBdOaR5r7y6bQnAMclk7PiJJkNLGqJ8QwGJn3mj3x/8WAiEAgWHIflU0rD+n8a6yRqR/roAfD9v2/ymz5ZKzID1c5aA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4512, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeQGY/CRA9TVsSAnZWagAAPQUP/Awqe9h31YW/NaGG5qo7\nULowCiU7j3pIqfs9syz5x2QxGwdQhBs4zBmFp3Y3RVyJr7tPLwObK1yo5BlS\nP1jKG6YSNZFzS+yBN6+IFyccApOzDlR+1EED03o5l8qyh1vKQEDFIgotzTkR\nOdn4qyyaKtx469KPofOyg4Bn5RPoxQqP/H0Z4ngOquqKhUYQpbtKPJ/xbrIr\nogGWpfGqSf8B+Pv6HrK7AyyUIweQn5dT3lt4SiKRg5JMoIpYHcoEUhtSnWQm\nXoOVIe12liB5VQjgaGX6IGe8/6e/jond/gMjYWnx636Fk0dHB4eHVsYJlkRj\nEMwO4wF+zICQx2rbaGwo8XAY3IouNpvG/cOalh1rhFsCRZHywc2y5FDbvqBb\n37fvM6CMiDr0iIgJu8kv5QoVs6b+utIWIZd5LbF+c+IemnUv/ezSIdydnsQE\nTQbC69yFhMxLYDhq3LKCgIoV70e786K84+RLIlNp+RvQTL3oWSRtgQUVg3D6\nTmIjw24JU9ZjUzY2ecyVnmU+6HGuR61fxtqkXCsRsgzPdAnlqnHtEoFLUfr8\nlYZlKXJ4kqp7nsRZPSa5jrB7jSniSJ9DCkkQoEfz6agBzVZOuV85CIcq8u2n\nN+7cN1xHNJ2LPiPCY6pQsfGKkWuwoOf+HMNBVqrUjQ/Yh/ZfxikDVk+gh3Qt\nUsc1\r\n=15aG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/color.js", "module": "dist/color.esm.js", "gitHead": "62efdf8e2ebc0a3c1541eb24014b78c9336279b3", "scripts": {"test": "node test/index.js"}, "_npmUser": {"name": "kurkle", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kurkle/color.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "css color parsing, manupulation and conversion", "directories": {}, "_nodeVersion": "12.15.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"fs": "0.0.2", "gulp": "^4.0.2", "util": "^0.12.1", "assert": "^2.0.0", "eslint": "^6.1.0", "rollup": "^1.19.4", "typedoc": "^0.16.9", "benchmark": "^2.1.4", "chroma-js": "^2.0.4", "color-name": "^1.1.4", "perf_hooks": "0.0.1", "tinycolor2": "^1.4.1", "color-names": "^2.0.0", "color-parse": "^1.3.8", "gulp-eslint": "^6.0.0", "babel-eslint": "^10.0.2", "color-parser": "^0.1.0", "color-string": "^1.5.3", "chartjs-color": "^2.4.1", "child_process": "^1.0.2", "csscolorparser": "^1.0.3", "eslint-plugin-react": "^7.14.3", "chartjs-color-string": "^0.6.0", "eslint-plugin-import": "^2.18.2", "rollup-plugin-terser": "^5.1.1", "eslint-config-chartjs": "^0.1.0", "eslint-config-defaults": "^9.0.0", "rollup-plugin-commonjs": "^10.0.2", "rollup-plugin-istanbul": "^2.0.1", "rollup-plugin-visualizer": "^3.3.1", "rollup-plugin-node-resolve": "^5.2.0", "typedoc-plugin-external-module-name": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/color_0.1.2_1581278782956_0.15078315773645534", "host": "s3://npm-registry-packages"}}, "0.1.3": {"name": "@kurkle/color", "version": "0.1.3", "keywords": ["color", "colour", "css", "hsl", "hex", "rgb", "rgba", "hwb", "hsv", "cmyk"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@kurkle/color@0.1.3", "maintainers": [{"name": "kurkle", "email": "<EMAIL>"}], "homepage": "https://github.com/kurkle/color#readme", "bugs": {"url": "https://github.com/kurkle/color/issues"}, "dist": {"shasum": "244313280bec08743e2ba7237a43850352541c14", "tarball": "https://registry.npmjs.org/@kurkle/color/-/color-0.1.3.tgz", "fileCount": 7, "integrity": "sha512-AEYY1u0xlvb7gyT1Dfe4cg6Brfk7ymmyFJkx2KUIIf/u+lvOPhvUqSwNYMEtBCrEO6KW2Iqtdy174HNa9R51Sg==", "signatures": [{"sig": "MEUCIBbGBhKPFQYGCBudJ6xksRWwXrkGyHElGp8FJeS9RoCxAiEAnMsVKXrh1CV+i648VdcDQXgf+Q4J6c8cWVIH9J7F4i4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53966, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeQGwRCRA9TVsSAnZWagAAQfwP/j5XsHbVP7bA8D5BBRTE\nWmNVjwHsX9VDmG7hSeViEonI5hmBTUFI1yDX2aN0OyzBDCgpoBpLVlFR70Tr\n2WZBRfeoL+poBIZx9sfUGWK7Qht0Sacf5NdxScnSxj4dXGrv5Vc01vzppP/l\nLkNd/g1JsUCoi+kOtJ4c5SbXDJZyxSxkACIk8FvwoVSb/BBaGIelvPoYrNcp\nrbj3PyKl/gvntKvYJbELxIobszDc6jrtfWI5+YXQGYtuLoRYU+E+n0hTcEor\nE4aohHx2vbtXM8w2DKIS3JRphuPksQ761h24YypEA3Tx1gpaLUXSVmUDJ3lG\npkG+EZpQzOxdnLbTY9swC8duLE/faYGmKEQr1xmyYdV6Bb9OfuyxvoOP5zng\nbFam7cfvixB8snhiDpK1ah7orScq8D9Wml6f4+SOvI3hGfzqQRIOucp+3VEb\nu6sanwbyyW+Kj43HB/m7NT3DfKYnZEEwwMzSUvE+6ZgB6Hq0Js62gmOvTSiu\nBLTIghmOW81iEPvjP2ucTEWT78dESPzalQJLvpMpNsAU4Ov5dJZISSznlgIU\nuEGMxKR0h/SH03HOmLKk+J8NXCzlp8LJpuDEJ5gz/NcXpJKBN0LqviqyW2Rq\ngATslnVCSmxwleDlIKTnlJbKEN1B1A5FqbBz8BkuGIZxItQRnDu1zGI0U4wI\nbmNY\r\n=+3CC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/color.js", "module": "dist/color.esm.js", "gitHead": "7b2da6333fae194862cb6194bb52088f56a21c1e", "scripts": {"test": "node test/index.js"}, "_npmUser": {"name": "kurkle", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kurkle/color.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "css color parsing, manupulation and conversion", "directories": {}, "_nodeVersion": "12.13.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"fs": "0.0.2", "gulp": "^4.0.2", "util": "^0.12.1", "assert": "^2.0.0", "eslint": "^6.1.0", "rollup": "^1.19.4", "typedoc": "^0.16.9", "benchmark": "^2.1.4", "chroma-js": "^2.0.4", "color-name": "^1.1.4", "perf_hooks": "0.0.1", "tinycolor2": "^1.4.1", "color-names": "^2.0.0", "color-parse": "^1.3.8", "gulp-eslint": "^6.0.0", "babel-eslint": "^10.0.2", "color-parser": "^0.1.0", "color-string": "^1.5.3", "chartjs-color": "^2.4.1", "child_process": "^1.0.2", "csscolorparser": "^1.0.3", "eslint-plugin-react": "^7.14.3", "chartjs-color-string": "^0.6.0", "eslint-plugin-import": "^2.18.2", "rollup-plugin-terser": "^5.1.1", "eslint-config-chartjs": "^0.1.0", "eslint-config-defaults": "^9.0.0", "rollup-plugin-commonjs": "^10.0.2", "rollup-plugin-istanbul": "^2.0.1", "rollup-plugin-visualizer": "^3.3.1", "rollup-plugin-node-resolve": "^5.2.0", "typedoc-plugin-external-module-name": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/color_0.1.3_1581280273022_0.30525147990362234", "host": "s3://npm-registry-packages"}}, "0.1.4": {"name": "@kurkle/color", "version": "0.1.4", "keywords": ["color", "colour", "css", "hsl", "hex", "rgb", "rgba", "hwb", "hsv", "cmyk"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@kurkle/color@0.1.4", "maintainers": [{"name": "kurkle", "email": "<EMAIL>"}], "homepage": "https://github.com/kurkle/color#readme", "bugs": {"url": "https://github.com/kurkle/color/issues"}, "dist": {"shasum": "35ab6e08cb5d8bc6885671d1277a7348eb748f65", "tarball": "https://registry.npmjs.org/@kurkle/color/-/color-0.1.4.tgz", "fileCount": 7, "integrity": "sha512-fEK/DTxWEKLqdQLM2OlH6hev30CiCE3r9Cx5tOYWEP8IpcH6dJUnawBemJ0HYwiUrG1BDByODuGR/RJVW7VDTw==", "signatures": [{"sig": "MEYCIQCJ+Bv+GVVMvzBsXu5mS7QLWVKvW/YrBlmzpx4XJLiWrQIhAPX5jxXRj9Nnwj5IYZK1YY3J1goD7PyACP3KS7zx84Er", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41895, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeUAMNCRA9TVsSAnZWagAAiAQQAILI8l4LbyPAaz+7w4M/\nciillI/rkkPJhQzu609iASJgNo1LFgfFQrtm0uqa6bsGXZsFnypM+0jRjtW6\nnVWMHcgs4LvpflMIaTKj6PfantKb4srto4PJrekzPURkCIr+XS34rXvLAlI2\nmK6YDf7dm6U5BsZKjNdS3RRtiUyezqTzhkeng8lC5I+XnfSc4jNgzmN54mYn\nf7HuwNg7IKq4dtVZCFqdr0qJ/vv7qDuUHaFnldrGWmKmV9Y7HJ2KgpJG+RzN\nvONTnqkgDjwyqiK+Gv9nVh6Gih6imwnkix05gaeUqDDB1iN7m/mCljNk2uId\nCFHjOHfrWmABO9e2mhmpiWXJqWC4IayO8V1wU8px/UtsGVbMwCML+0PZhfDi\nVoZSvawWFtX/i9R+0u64p2mi9tDKZalcfC7+IPFjmyvJxxdKZuaslNTDQPkH\n79nRsIl8K1RGFHmSQjCAVOOKSTtdTbFPrm6gsjHB036cTCgWReQeBPMkS3cN\njfNW+Tw7mdxtoboWVyD3hWjeDqv3nVlx/btPjGIYvjyylrTx6cXb9iCi3ePG\ndKHN5dstoL0KVpdB4yn4PlcqZOtopF5pZ2URZY/r86MMtCZ6unI7hAua24xm\nGqh5hX0Uz92QlONbD+fDLuCSIX/tOFFhtBLyOrhCqbPpiGcg8IywYc+RD2U4\nk8II\r\n=8rHq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/color.js", "types": "dist/color.d.ts", "module": "dist/color.esm.js", "gitHead": "6875a586573bb758b07c56f07be3b99de20c5c06", "scripts": {"test": "node test/index.js"}, "_npmUser": {"name": "kurkle", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kurkle/color.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "css color parsing, manupulation and conversion", "directories": {}, "_nodeVersion": "12.13.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"fs": "0.0.2", "gulp": "^4.0.2", "util": "^0.12.1", "assert": "^2.0.0", "eslint": "^6.1.0", "rollup": "^1.31.1", "typedoc": "^0.16.10", "benchmark": "^2.1.4", "chroma-js": "^2.0.4", "color-name": "^1.1.4", "perf_hooks": "0.0.1", "tinycolor2": "^1.4.1", "typescript": "^3.8.2", "color-names": "^2.0.0", "color-parse": "^1.3.8", "gulp-eslint": "^6.0.0", "babel-eslint": "^10.0.2", "color-parser": "^0.1.0", "color-string": "^1.5.3", "chartjs-color": "^2.4.1", "child_process": "^1.0.2", "csscolorparser": "^1.0.3", "eslint-plugin-react": "^7.14.3", "chartjs-color-string": "^0.6.0", "eslint-plugin-import": "^2.18.2", "rollup-plugin-terser": "^5.1.1", "eslint-config-chartjs": "^0.1.0", "rollup-plugin-cleanup": "^3.1.1", "eslint-config-defaults": "^9.0.0", "rollup-plugin-commonjs": "^10.0.2", "rollup-plugin-istanbul": "^2.0.1", "rollup-plugin-visualizer": "^3.3.1", "@rollup/plugin-typescript": "^3.0.0", "rollup-plugin-node-resolve": "^5.2.0", "typedoc-plugin-external-module-name": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/color_0.1.4_1582301965427_0.37630453369106753", "host": "s3://npm-registry-packages"}}, "0.1.5": {"name": "@kurkle/color", "version": "0.1.5", "keywords": ["color", "colour", "css", "hsl", "hex", "rgb", "rgba", "hwb", "hsv", "cmyk"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@kurkle/color@0.1.5", "maintainers": [{"name": "kurkle", "email": "<EMAIL>"}], "homepage": "https://github.com/kurkle/color#readme", "bugs": {"url": "https://github.com/kurkle/color/issues"}, "dist": {"shasum": "f0776fccbfd4d10b9d5cdeb7ffd7e63d5628b4f3", "tarball": "https://registry.npmjs.org/@kurkle/color/-/color-0.1.5.tgz", "fileCount": 8, "integrity": "sha512-6burT8xSb5vnma+UO+D/H8K2vGMhnnvLC2ZRfYTDNJPdeKZPkMa94Ebw3Zr53P4m8DWc4TUXBSp/GgNS0YDxMw==", "signatures": [{"sig": "MEUCIQC/mBti3InMcdb0xdQBCUU71KYKIVZDYaVlUjZ1c+Oq9gIgejrI2bPNzUCvMPNCBHeNOOGjQumQxn7rDj0EfyP2LfE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47133, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeUAaFCRA9TVsSAnZWagAA8XkP/ApwtxPpmmPcuQdDCTV+\nfwUPTzWq9nacuIDEbHW84qzSZW1nh9J5IBaSfnDXKxxqPvA7sEOds4/c999p\ns4cjZWLfx4DBg3QPgZdq4oNamoePPxPuB2TyUh0wqvQMLYNRaRlSLKMjMa0b\n4GM625tT3OMSUlIWhmX6ODFHwWs9Tcn1nAa0/3Ef8ErhoS77L9kC7c4Fqb2B\nKavgLEdaHPjevuQ7QKM1Ao+FqV69B6kJxCVN4aoFSoG+t0HOcmgsA1ZhXDTb\niTqtuCIOtEYJclxSeq+Fbf1Crta720osptEIEJ0ItTgO3AoMeIn+yDNm8kvf\ni3oC+Lqw7Yf6FdXqCE30kTWqtgQ6euAnQOyh5iFJIIKfYR0UULT9z8YGhr+g\n4KAtQW6wPgd39V10Eo+HcEJY7jAr90cZm9EMvteOxvAico0LmSglPoYSpKKj\nHl2ZY3c3BSsgY9epK1x+E8w9WrcsDj/Tkot6WPy0QK0GFPhuMXRIF5M4Bis3\nZCtyXb2SMS7Ey38p/EMV90MtJIOetRvhry79VW3+Y9aCbel0maCUO2txMGC6\nML9lLYkIB8DC/gq/sgqvs4DKUVU1FqxXcwL/XqmhJH0NBGcQYP6ILV6d3KBB\nb1Rc10aypvC/fy5r17B1PVOtr7nnyJ+0sQaPFFeXtoyhRNBAVoX8DTsQhlOw\nxjhu\r\n=DEsj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/color.js", "types": "dist/color.d.ts", "module": "dist/color.esm.js", "gitHead": "67cb8a82dd504bc8072e180c77c47b415659586b", "scripts": {"test": "node test/index.js"}, "_npmUser": {"name": "kurkle", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kurkle/color.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "css color parsing, manupulation and conversion", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"fs": "0.0.2", "gulp": "^4.0.2", "util": "^0.12.1", "assert": "^2.0.0", "eslint": "^6.1.0", "rollup": "^1.31.1", "typedoc": "^0.16.10", "benchmark": "^2.1.4", "chroma-js": "^2.0.4", "color-name": "^1.1.4", "perf_hooks": "0.0.1", "tinycolor2": "^1.4.1", "typescript": "^3.8.2", "color-names": "^2.0.0", "color-parse": "^1.3.8", "gulp-eslint": "^6.0.0", "babel-eslint": "^10.0.2", "color-parser": "^0.1.0", "color-string": "^1.5.3", "chartjs-color": "^2.4.1", "child_process": "^1.0.2", "csscolorparser": "^1.0.3", "eslint-plugin-react": "^7.14.3", "chartjs-color-string": "^0.6.0", "eslint-plugin-import": "^2.18.2", "rollup-plugin-terser": "^5.1.1", "eslint-config-chartjs": "^0.1.0", "rollup-plugin-cleanup": "^3.1.1", "eslint-config-defaults": "^9.0.0", "rollup-plugin-commonjs": "^10.0.2", "rollup-plugin-istanbul": "^2.0.1", "rollup-plugin-visualizer": "^3.3.1", "@rollup/plugin-typescript": "^3.0.0", "rollup-plugin-node-resolve": "^5.2.0", "typedoc-plugin-external-module-name": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/color_0.1.5_1582302852792_0.9463193687226084", "host": "s3://npm-registry-packages"}}, "0.1.6": {"name": "@kurkle/color", "version": "0.1.6", "keywords": ["color", "colour", "css", "hsl", "hex", "rgb", "rgba", "hwb", "hsv", "cmyk"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@kurkle/color@0.1.6", "maintainers": [{"name": "kurkle", "email": "<EMAIL>"}], "homepage": "https://github.com/kurkle/color#readme", "bugs": {"url": "https://github.com/kurkle/color/issues"}, "dist": {"shasum": "75f7c4d4652579b31f02db4135242c1533ad8634", "tarball": "https://registry.npmjs.org/@kurkle/color/-/color-0.1.6.tgz", "fileCount": 8, "integrity": "sha512-jOb40/8Ih98jEQq4gsBw6Wo35hD/XDDfbM/DaikRdEwNMdJAs/bipZugzUDeegTqHMlFFuA4oN28PVINMedm4A==", "signatures": [{"sig": "MEUCIQCmHNG2SkQyHwajPrqQ0vCiqOtm/FG3t9VKOMdoIz/SAQIgJhlQ3GmRHBZFq9ymVRC0J3hnN+VlcG5Cbk/5+MSbrMY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeUA2vCRA9TVsSAnZWagAAYsQP/1iOeUVa0jq7L3E9CYBp\nOHglejBU/A4tUx47weNfGoAQ6+/CCy560GPlLCGe0MlPye9pdgqsxwRNRm/Y\nft7J8v2BlYjcYDihn8VCaJ7obxEqWzUrtUdw4OaXVSzpeAiP9I0VFHrf4mMY\nq8uUjD1efOTsN22cRO28PN/6A4F32dOaDc/+hfJsIbdcVRwPY2xxjmwyaCnJ\nsetBr1iN+fN3Q+QSXAdePNf0k9M0caMjMwkrzzzldzMtdjWSSySXEcsqqYl2\n153A0Gqwj8qRbORkaOXMEvz6KIGbt9PBbPAK5DvStcMRObbi8AROLu7DnscI\nakUeO/XihSykTi+PLmV/Ke77CV2L3yG0Fj2U5BtL7qitiPMKOIHcKShlySgh\n2Unq9OQw0pytkugDXqLwnj8Y8HVsDKBW3KlwKvuhUiKfMb4GMHn+Ayt4uzcK\nYebnC1hnGY5RjBRVLgRHg29LM08SB34jaRo5NSuzzla2Oz31bomUaWcB4r7c\nMowxJOzkFCppvNQyBqnYgk9KntZskJuENHuWcymkYFuUvir5UDtQFyHM5/qJ\nANjbrw6RE05ze6eeymdvYgzy8QzyW8j/LzNO6zAIditqDgcluPkJUEoj9k0x\nG8BNl1PpUSkrMhNwUTaNBVIEpM8eKwRzj2ERLvBdIx0ocX6bHW28b23bhd1V\n9ClM\r\n=R2xQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/color.js", "types": "dist/color.d.ts", "module": "dist/color.esm.js", "gitHead": "5bc913d721689ad02409252135dd934b45eb5000", "scripts": {"test": "node test/index.js"}, "_npmUser": {"name": "kurkle", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kurkle/color.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "css color parsing, manupulation and conversion", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"fs": "0.0.2", "gulp": "^4.0.2", "util": "^0.12.1", "assert": "^2.0.0", "eslint": "^6.1.0", "rollup": "^1.31.1", "typedoc": "^0.16.10", "benchmark": "^2.1.4", "chroma-js": "^2.0.4", "color-name": "^1.1.4", "perf_hooks": "0.0.1", "tinycolor2": "^1.4.1", "typescript": "^3.8.2", "color-names": "^2.0.0", "color-parse": "^1.3.8", "gulp-eslint": "^6.0.0", "babel-eslint": "^10.0.2", "color-parser": "^0.1.0", "color-string": "^1.5.3", "chartjs-color": "^2.4.1", "child_process": "^1.0.2", "csscolorparser": "^1.0.3", "eslint-plugin-react": "^7.14.3", "chartjs-color-string": "^0.6.0", "eslint-plugin-import": "^2.18.2", "rollup-plugin-terser": "^5.1.1", "eslint-config-chartjs": "^0.1.0", "rollup-plugin-cleanup": "^3.1.1", "eslint-config-defaults": "^9.0.0", "rollup-plugin-commonjs": "^10.0.2", "rollup-plugin-istanbul": "^2.0.1", "rollup-plugin-visualizer": "^3.3.1", "@rollup/plugin-typescript": "^3.0.0", "rollup-plugin-node-resolve": "^5.2.0", "typedoc-plugin-external-module-name": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/color_0.1.6_1582304687109_0.8702188336528558", "host": "s3://npm-registry-packages"}}, "0.1.7": {"name": "@kurkle/color", "version": "0.1.7", "keywords": ["color", "colour", "css", "hsl", "hex", "rgb", "rgba", "hwb", "hsv", "cmyk"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@kurkle/color@0.1.7", "maintainers": [{"name": "kurkle", "email": "<EMAIL>"}], "homepage": "https://github.com/kurkle/color#readme", "bugs": {"url": "https://github.com/kurkle/color/issues"}, "dist": {"shasum": "d8b5a558ea91556790c053d31c7eae30087bd220", "tarball": "https://registry.npmjs.org/@kurkle/color/-/color-0.1.7.tgz", "fileCount": 8, "integrity": "sha512-6Dc4aR0xzQyWnxur+HOxt4rD/an6f1yuc7o/vpdMFuggQvg/CAIBmXrhPfQNI2pFJqNiSBoBSu5aXyYqOgNmaw==", "signatures": [{"sig": "MEUCIQCE8oWqBC4ulpUwSLyQJK506fG9DSzXJlJ8dykRKRsnEgIgOrzRykwuDszlXx5gGnF95M6RPzzbFijTgpwUelnkyCs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47708, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeUiiTCRA9TVsSAnZWagAAK/cP/Ai34S3biV9qGt+fytHr\nnaCZjMDh817gU4P9TXHaayXSkwkBdrBOODePWgyjtv7Vsa4TnFF9FKXGx+zT\nJBG58OJpQPdk7y7ExNqIUHmfkHEAOvS6RCchScpoqinHNzzrqgRcHpeK95pM\nvB8Cwbefdnw2lJy2wqOG3eNX2sxGu4W3iUC01LB/debQhdbZ0N+XirI0dVAd\nYZevN1i661NpHH+41k2hmHtSqX40+CsVlwOtMTxQ2d5THyrkoZAc6E4+ItNZ\nFQDgFC20KFX28ftZu7I7tvGUa2iqH/O8WknKnOZ8Ri29btenfTs4A8WNrBMV\nTQmNMwHfu3kEmLWb666BlGhrpi33VlvNM5y56mlJtYM7SX62idjxmMWjb7yb\nJtb1mUS8+3d/owk9SOtF4Z0241p48nTXMVvJtai2MjKLuIBR9r+gOqc0nySE\nukB8d34U61pdn4R4W/fs/PFKdOkv6vcn970V5Aa1viZYYC0pvcvWJzMfgYoX\nI5vmWvTK7WajMzNPbh8DoPtnL40goDmeGoAdAZFciL7LgKVpr0s0RfZjlgfl\naCosxmZzCBP+bKFR/nATX6MDXxckiYoi/LZKEDqPRetq4e6LcERvRggv6cjU\nJBbZm4+XtooHteW3SyQE9joSFjtry2oSstJDHs1sCq+xGbZgCGsWYwErsQwH\nia0n\r\n=okst\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/color.js", "types": "dist/color.d.ts", "module": "dist/color.esm.js", "gitHead": "f63d853c05ed51f7f258b28bc7b0593b8784dbd8", "scripts": {"test": "node test/index.js"}, "_npmUser": {"name": "kurkle", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kurkle/color.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "css color parsing, manupulation and conversion", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"fs": "0.0.2", "gulp": "^4.0.2", "util": "^0.12.1", "assert": "^2.0.0", "eslint": "^6.1.0", "rollup": "^1.31.1", "typedoc": "^0.16.10", "benchmark": "^2.1.4", "chroma-js": "^2.0.4", "color-name": "^1.1.4", "perf_hooks": "0.0.1", "tinycolor2": "^1.4.1", "typescript": "^3.8.2", "color-names": "^2.0.0", "color-parse": "^1.3.8", "gulp-eslint": "^6.0.0", "babel-eslint": "^10.0.2", "color-parser": "^0.1.0", "color-string": "^1.5.3", "chartjs-color": "^2.4.1", "child_process": "^1.0.2", "csscolorparser": "^1.0.3", "eslint-plugin-react": "^7.14.3", "chartjs-color-string": "^0.6.0", "eslint-plugin-import": "^2.18.2", "rollup-plugin-terser": "^5.1.1", "eslint-config-chartjs": "^0.1.0", "rollup-plugin-cleanup": "^3.1.1", "eslint-config-defaults": "^9.0.0", "rollup-plugin-commonjs": "^10.0.2", "rollup-plugin-istanbul": "^2.0.1", "rollup-plugin-visualizer": "^3.3.1", "@rollup/plugin-typescript": "^3.0.0", "rollup-plugin-node-resolve": "^5.2.0", "typedoc-plugin-external-module-name": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/color_0.1.7_1582442643157_0.05436020724189583", "host": "s3://npm-registry-packages"}}, "0.1.8": {"name": "@kurkle/color", "version": "0.1.8", "keywords": ["color", "colour", "css", "hsl", "hex", "rgb", "rgba", "hwb", "hsv", "cmyk"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@kurkle/color@0.1.8", "maintainers": [{"name": "kurkle", "email": "<EMAIL>"}], "homepage": "https://github.com/kurkle/color#readme", "bugs": {"url": "https://github.com/kurkle/color/issues"}, "dist": {"shasum": "56738b9b33f1533e1f92bcf16607bf7b6138bd62", "tarball": "https://registry.npmjs.org/@kurkle/color/-/color-0.1.8.tgz", "fileCount": 7, "integrity": "sha512-uRsX6+hzyFvfaxgfWiChESHmPkwVArFSkBwUUOqfBylQjUY5iBTb6KB/p6iKH0aWkkfLLc+v6mGFgGGkvlQBGg==", "signatures": [{"sig": "MEUCIQChC5Je/FDSMozylKuvpf2nO4qywWT6u1S9rCQdvJ80HwIgE4OOsmIh6M7SE3v/NTviUMUxlt0TJjSMHx+uoa6H72A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39901, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe2VxkCRA9TVsSAnZWagAAiGAP+wSVm/2jRhaqN5RnpSOG\npfz0n1umAhfWw+dwldSz1wq5y7UQMQVLvxb4xsyg2shCgbNNLMEJ15FO/dyW\nFpPe+8s0gySIaJYdSCVrJd0JoxAyv0u6y3SRhP56CV4YYKHbtOgUj/o80de0\n2wmEVO2Ih4yqK+5Y+mFG7GbJt19Fpve5a7xGFzod4G7jIQxGvU+z8IMWoCTS\n8eyVE8j62eK+DXhrphVYhOoW9c3L7kSku9bzf/FuqXsK7zxqzfD54g+f/hht\nh2FwtXylB8phgbf+cUHsKJfi1wb7LYIwf0bm+vlJFqMpeXEp2fADJZymhgKj\n0NuApRbIqrbeuC2vnupXQQi60zKTBxhC+a4TY0gc2PWJIBQ91o5Al3Fve+Y+\n7OqG0oXguczEJ9hf/aJheGOE3Et0HxekpqE4iENRO6WtkPxj5jg1VRhD3D4g\nqbJKjqxLdzPYh5YHnO6RipEiFcgggSceHUv27JM3yVfl2+Tvf8Xf8zLh2H+H\nrMBpaSKQ2KmKiJKKW2hVwNTU9kwKJSm0zepTkQWWuztqKyzOeFFtLZ8yTmQK\n2aTRKXELhbNHFFZcaPQD2XiFnKMds02PM7yZe4NG/nOL/V3EZPPQIlwMP8HI\ndlTQlYkAoIwpYUrfDau6oSsjdrZ0/Ngc7RbIOL7tgMQ/y7PRzczZLwRtOyg3\nTJvH\r\n=jI/a\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/color.js", "types": "dist/color.d.ts", "module": "dist/color.esm.js", "gitHead": "143587139f6f578347c551fa2e43cff9bb876833", "scripts": {"lint": "eslint src/*.js test/*.js util/*.js", "test": "node test/index.js", "build": "node util/copy_dist.js && rollup -c"}, "_npmUser": {"name": "kurkle", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kurkle/color.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "css color parsing, manupulation and conversion", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"fs": "0.0.1-security", "util": "^0.12.3", "assert": "^2.0.0", "eslint": "^7.1.0", "rollup": "^2.13.1", "typedoc": "^0.17.7", "benchmark": "^2.1.4", "chroma-js": "^2.0.4", "color-name": "^1.1.4", "perf_hooks": "0.0.1", "tinycolor2": "^1.4.1", "typescript": "^3.9.5", "color-names": "^2.0.0", "color-parse": "^1.3.8", "babel-eslint": "^10.1.0", "color-parser": "^0.1.0", "color-string": "^1.5.3", "chartjs-color": "^2.4.1", "child_process": "^1.0.2", "csscolorparser": "^1.0.3", "eslint-plugin-react": "^7.20.0", "chartjs-color-string": "^0.6.0", "eslint-plugin-import": "^2.20.2", "rollup-plugin-terser": "^6.1.0", "eslint-config-chartjs": "^0.2.0", "rollup-plugin-cleanup": "^3.1.1", "eslint-config-defaults": "^9.0.0", "rollup-plugin-istanbul": "^2.0.1", "rollup-plugin-visualizer": "^4.0.4", "typedoc-plugin-external-module-name": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/color_0.1.8_1591303265056_0.430101686236928", "host": "s3://npm-registry-packages"}}, "0.1.9": {"name": "@kurkle/color", "version": "0.1.9", "keywords": ["color", "colour", "css", "hsl", "hex", "rgb", "rgba", "hwb", "hsv", "cmyk"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@kurkle/color@0.1.9", "maintainers": [{"name": "kurkle", "email": "<EMAIL>"}], "homepage": "https://github.com/kurkle/color#readme", "bugs": {"url": "https://github.com/kurkle/color/issues"}, "dist": {"shasum": "949ea1494581b15576fca50d8486e5816fccfd27", "tarball": "https://registry.npmjs.org/@kurkle/color/-/color-0.1.9.tgz", "fileCount": 7, "integrity": "sha512-K3Aul4Ct6O48yWw0/az5rqk2K76oNXXX3Su32Xkh4SfMFvPt0QEkq0Q6+3icE5S3U2c88WAuq3Vh1Iaz4aUH+w==", "signatures": [{"sig": "MEQCIBfUB/OwM/HolFXSUamhxmy7MvYRTVkPl3XW10KOe7bFAiAmKDyX5nuu1VKSNCfWt4aRGr8M5gADSk7D6pqyi7KFew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40018, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe3w3QCRA9TVsSAnZWagAAFcMQAI8VcJZeruxaIHxLKz0I\n0yAck0qtq04Z5qm7hwWtjgy8v1jAtOA87Nkj2C0dlOVPB/sVcsuPy9LfVUT8\n9efAZEdn+p0MrX9NZ6QJPBe6n1aBu36sRO2N5D6CNyFNF6Z4KdpO3akfAXZN\n9MNGlJi8P4NZn/qzZx9y6d2K0ToksqF/AjjUdQE44RyGRtKM/qwYJ3rOfGOr\nFuqQ8V0stgK/qTQtOayHASaAeOrL91wKIKDajO8yez9GlHJnbkgoL0Ii1kVY\nLwKn/73u6rS3FdauMGrcTNX3bl5IUd32MHNPCz26fb8xSS/Q8PzzcaToUu+g\nWsawGIC92TlV393OniqUn8neauiG9h27gplUdUCFKMIBh0DfbN6zX+SEYoWC\nv6C74dKxy+kXkd+owJCwX8smCVm/09qisTEEVdGXjDKdnr7sg+idw8sScLmc\nFX8gBksI2gBWz9676FknW+WNjq6TkbSZBM6nQY6g89dnv+cSqKl0YXl+fr04\nNG+cmsXv7MQmyFxnaPjT8fzrZJLNVGU8EshG4hhBmOe83Kd1C0xybgJ8Or6/\nGGUo4nv9GEGEFyA2xBusbZ/3dhJ+XZ+rdcu/ygxskymFbOEHeNjeMq2mnGMQ\nD0WlzzN/ZJdCwrMJjd+qxZXXAHZwWEDNiPUe+K5fGEgctVMce9tyWlv7MSRl\n2dVn\r\n=wugq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/color.js", "types": "dist/color.d.ts", "module": "dist/color.esm.js", "gitHead": "556e92b87ae3015cb551875a96a14ce55ca8c876", "scripts": {"lint": "eslint src/*.js test/*.js util/*.js", "test": "node test/index.js", "build": "node util/copy_dist.js && rollup -c"}, "_npmUser": {"name": "kurkle", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kurkle/color.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "css color parsing, manupulation and conversion", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"fs": "0.0.1-security", "util": "^0.12.3", "assert": "^2.0.0", "eslint": "^7.2.0", "rollup": "^2.15.0", "typedoc": "^0.17.7", "benchmark": "^2.1.4", "chroma-js": "^2.0.4", "color-name": "^1.1.4", "perf_hooks": "0.0.1", "tinycolor2": "^1.4.1", "typescript": "^3.9.5", "color-names": "^2.0.0", "color-parse": "^1.3.8", "color-parser": "^0.1.0", "color-string": "^1.5.3", "chartjs-color": "^2.4.1", "child_process": "^1.0.2", "csscolorparser": "^1.0.3", "eslint-plugin-react": "^7.20.0", "chartjs-color-string": "^0.6.0", "eslint-plugin-import": "^2.21.1", "rollup-plugin-terser": "^6.1.0", "eslint-config-chartjs": "^0.2.0", "rollup-plugin-cleanup": "^3.1.1", "eslint-config-defaults": "^9.0.0", "rollup-plugin-istanbul": "^2.0.1", "rollup-plugin-visualizer": "^4.0.4", "typedoc-plugin-external-module-name": "^4.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/color_0.1.9_1591676368071_0.45797182505328426", "host": "s3://npm-registry-packages"}}, "0.2.0": {"name": "@kurkle/color", "version": "0.2.0", "keywords": ["color", "colour", "css", "hsl", "hex", "rgb", "rgba", "hwb", "hsv", "cmyk"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@kurkle/color@0.2.0", "maintainers": [{"name": "kurkle", "email": "<EMAIL>"}], "homepage": "https://github.com/kurkle/color#readme", "bugs": {"url": "https://github.com/kurkle/color/issues"}, "dist": {"shasum": "3163d007f4d1e165211e429f8e913d587239894f", "tarball": "https://registry.npmjs.org/@kurkle/color/-/color-0.2.0.tgz", "fileCount": 7, "integrity": "sha512-UzMZQEQKw7FtBY5eFV7Vt1zM6/Ag5pDsVOC+JoMUtxarqCwfyrU/dyEAVQn+TkP8j5uPCljfq0YDKIuG5FpliQ==", "signatures": [{"sig": "MEYCIQDOQo9YHAB6To8tf1Th5b0+H0rEnol+IHP/5Kqu5hjZqQIhAKGXwnTxxhXv0X9VW9pP6nLla5WWb2Ji/ePHF+tqxJ0J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43031, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiiqEhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpuYQ/+KMr6nquyegf1dEphs0MY6vLsGtkrcTTgm9klFgVw1af0C5Ml\r\nL1XxsvPf/s50RuRZubTbvJExnuyQ0LjujM9bOu0V7SHqSU1hMY/7DLcM3VK1\r\nlitQPB8bNCW4RWgq52eC5+btq9cH1mnu4CTPP82cAuCqGJejkQkKa6GXluV+\r\n5LY7j1+7PO25dU6LY7/AMvmVbpIQVD6iTZEdwS9FCY8RqLZUpu1y+qbuKndR\r\naVDwdeDKu3dtX0A0eKFEyy8x7hEYyderjY7c246XGUd94HfVptbkulPaKFkD\r\nZ9MkPrUr1714BxTkn74N1OtebBN9ib0YCqVPkinNlwnHlkGiFeb3hWBAHrMd\r\nsnzTO2ZGQX8iWPmp3UjzoRzsvkNu8VxmHVRkpy1ZtqZgOr2A54an2P3a3RDJ\r\nOfFlVmyoYkZ8yDYx6yUS4nbAE9bmS+zfuQypN4VIKPmJbFZTlQceKJ7v/hrP\r\nLRqcRQ1isghLyPpfx7zbqQMaUjR7JXa3Pa0Pu2bDqZJNBwQO1QSuUvOceiri\r\nOrOJM0b908liqMMw8RVaKGlQO2sFiZfgZkWMf+xY4tjXjihOowhbz3XuiJdf\r\nNC3hN1eP3sr7SUbcTjaWRQmZd53JU/5LMi5sYZ3SSHFGuUSM01K7oa9gZKTt\r\nT65FUnN1gzz/0WMZqBNz6k9GkgaCAl+zbM4=\r\n=sMEH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/color.js", "types": "dist/color.d.ts", "module": "dist/color.esm.js", "gitHead": "40364900872aad08a2d63ae3aa50d4698dd8e901", "scripts": {"lint": "eslint src/*.js test/*.js util/*.js", "test": "node test/index.js", "build": "node util/copy_dist.js && rollup -c"}, "_npmUser": {"name": "kurkle", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kurkle/color.git", "type": "git"}, "_npmVersion": "6.14.17", "description": "css color parsing, manupulation and conversion", "directories": {}, "_nodeVersion": "14.19.2", "_hasShrinkwrap": false, "devDependencies": {"fs": "0.0.1-security", "util": "^0.12.3", "assert": "^2.0.0", "eslint": "^8.12.0", "rollup": "^2.41.2", "typedoc": "^0.22.13", "benchmark": "^2.1.4", "chroma-js": "^2.1.1", "color-name": "^1.1.4", "perf_hooks": "0.0.1", "tinycolor2": "^1.4.2", "typescript": "^4.6.3", "color-names": "^2.0.0", "color-parse": "^1.4.2", "color-parser": "^0.1.0", "color-string": "^1.5.5", "chartjs-color": "^2.4.1", "child_process": "^1.0.2", "csscolorparser": "^1.0.3", "eslint-plugin-react": "^7.22.0", "chartjs-color-string": "^0.6.0", "eslint-plugin-import": "^2.22.1", "rollup-plugin-terser": "^7.0.2", "eslint-config-chartjs": "^0.3.0", "rollup-plugin-cleanup": "^3.2.1", "eslint-config-defaults": "^9.0.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-istanbul": "^3.0.0", "rollup-plugin-visualizer": "^5.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/color_0.2.0_1653252385339_0.2796175757066599", "host": "s3://npm-registry-packages"}}, "0.2.1": {"name": "@kurkle/color", "version": "0.2.1", "keywords": ["color", "colour", "css", "hsl", "hex", "rgb", "rgba", "hwb", "hsv", "cmyk"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@kurkle/color@0.2.1", "maintainers": [{"name": "kurkle", "email": "<EMAIL>"}], "homepage": "https://github.com/kurkle/color#readme", "bugs": {"url": "https://github.com/kurkle/color/issues"}, "dist": {"shasum": "19ddeb9a06f27d0e62452adda17683f8426a7232", "tarball": "https://registry.npmjs.org/@kurkle/color/-/color-0.2.1.tgz", "fileCount": 7, "integrity": "sha512-zkMiuMMMQ92x16uBC2nkqTYDynCY9vhAxF0TH1vSMWKD0hnTGxnYtV1v+NEuVYuwYeA40jD4iuyz0ApcDJQstw==", "signatures": [{"sig": "MEYCIQDndpFAMWiDrxR2uFZKB/5gODRAGWkWaIMYpihoRSBUCgIhAJn5dSqqUwJ4kTA7Z64BlaYxq06nFY3iekj8mo/TItah", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43409, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJii57cACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo44A//Sjzt15dSUSO/oexlywXVPRxEYvVLBHIzPOQY1aeWteuDkWf/\r\nLIcimTo1pdPbN/fivH+uqboUR1PbA4nWN3n+XLHFTqWiqGgcEDB5UdzaKOR9\r\nZXkBiar25lfk96n691oO+KQ4RbeHe0ApCSx0jygSR2BDNA20p83Xibwxc7mq\r\nyBpn+BcZrNhV9kv6JHS3gk0NP+QrsvmSa3gj9jHRzvLbOOTyzbawmgwTHSsQ\r\n8bEkCf/DmT1ddK2gpZ7WNt/9TBaD99e/fS6oMfQQjIioLt8RfvKWqMky1BY3\r\nc5pHARMmktA4GCH8cD6AuQgJYVTXy+GOr888G3HrF8lLy6wdb6L9XTJUiFdM\r\n4MeI3E9HYsRr94zzYcWkwEouwizZqoE8Jkws1uxdqq3rQkAXKwtzshZcChEX\r\nni0twqdlyAx6zZ6W7KvYg+JwoYULslv6gjIK+PVw1tgYt+EimN2MZn2kKn8b\r\ncTghWq6cmy3Phv3lHDheePISrnrvJ3hdood1xU/78f9RzWwUTXzKDuasBDBD\r\nMqM4k/ATuKULUS06PbgD74EdIeADU4lTNsVuTLmAqK4CjHbeTMBqylcTPdjf\r\nFtAIiHVoRZSuhj9U0usDGfG88KLl4Rn760Zhw57TY7/HZYVhto5TEYNLfpuO\r\nS77whCIyvZ2xjwNV31o4+GePyqn2CsvQHPU=\r\n=QmQP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/color.js", "types": "dist/color.d.ts", "module": "dist/color.esm.js", "gitHead": "7f441c91cb3a6e578680e63e448a6acca2e68f56", "scripts": {"lint": "eslint src/*.js test/*.js util/*.js", "test": "node test/index.js", "build": "node util/copy_dist.js && rollup -c"}, "_npmUser": {"name": "kurkle", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kurkle/color.git", "type": "git"}, "_npmVersion": "6.14.17", "description": "css color parsing, manupulation and conversion", "directories": {}, "_nodeVersion": "14.19.2", "_hasShrinkwrap": false, "devDependencies": {"fs": "0.0.1-security", "util": "^0.12.3", "assert": "^2.0.0", "eslint": "^8.12.0", "rollup": "^2.41.2", "typedoc": "^0.22.13", "benchmark": "^2.1.4", "chroma-js": "^2.1.1", "color-name": "^1.1.4", "perf_hooks": "0.0.1", "tinycolor2": "^1.4.2", "typescript": "^4.6.3", "color-names": "^2.0.0", "color-parse": "^1.4.2", "color-parser": "^0.1.0", "color-string": "^1.5.5", "chartjs-color": "^2.4.1", "child_process": "^1.0.2", "csscolorparser": "^1.0.3", "eslint-plugin-react": "^7.22.0", "chartjs-color-string": "^0.6.0", "eslint-plugin-import": "^2.22.1", "rollup-plugin-terser": "^7.0.2", "eslint-config-chartjs": "^0.3.0", "rollup-plugin-cleanup": "^3.2.1", "eslint-config-defaults": "^9.0.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-istanbul": "^3.0.0", "rollup-plugin-visualizer": "^5.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/color_0.2.1_1653317340342_0.4349809828918778", "host": "s3://npm-registry-packages"}}, "0.3.0": {"name": "@kurkle/color", "version": "0.3.0", "keywords": ["color", "colour", "css", "hsl", "hex", "rgb", "rgba", "hwb", "hsv", "cmyk"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@kurkle/color@0.3.0", "maintainers": [{"name": "kurkle", "email": "<EMAIL>"}], "homepage": "https://github.com/kurkle/color#readme", "bugs": {"url": "https://github.com/kurkle/color/issues"}, "dist": {"shasum": "3004051708ca3add1efd6bfb619b85f2d7ef2b7c", "tarball": "https://registry.npmjs.org/@kurkle/color/-/color-0.3.0.tgz", "fileCount": 7, "integrity": "sha512-Luw5mZn6nHxbI111tiS/Ub21FcDe8eel2c5nnlRllWEH3k3QprlarJ4XJ+yHibqsJNXN8DrTrovciwbJ1euZ0w==", "signatures": [{"sig": "MEUCIQCDmiDnziFU0CMsuMoI+OAanPDZLfoCsFjEY/bMDXXnVwIgbDQhXUpm/7BzYqe4+w8CnHWxPLJL4rLw7V/3zHSGRCM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43554, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjg6xdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrW6xAAjI8rrMfBiRri/80X6Pqff4zP4eR9LS1oBhxomN2g/V+kKzAr\r\nbIlmJ4xCLLIZHLTbWIFnsfFrEhO9NkPDh9NRuTeT5MZ66olodCbEyz1EVYLB\r\nDblUYX6T4kidK/jJcA8LZZBBGMsu1OqbZHDoaW1RdI59Jqa2WctIsKPTBQrw\r\nQ/poKjckapt60E8P4WU2nZPHCpJ/KWZfHFsL1SHt+8WaZaFvMtUkZvCz39Tc\r\nd2I1Z3rdmXT10aSthw8jnKkhdYBFTsv/A/D2OFVkxZfnEWNwX+Xn9gX4l4hH\r\nWr/DaO9z3N4u+Y+rmABUnYN6HQudNcccifLMYmsLNC9jrjcDdraOHR9TkupE\r\nBnn0t6Xm8T6B3+5TF2kwuhZtw43b2ez47FsOODzC8DZswK4XHOQNAqKT/5aW\r\niZ9MeQ3b0A9sj6t4baQdM4CNGZb9dIPIOB97qJH7dcaBbq6VIvFzeOAG2id2\r\nOtpF2fvAsC/hh4ucjm9ahZeW2oITow0OgdQAg8sQhp/v0FREA8/9zNL+sLo+\r\nrHtixb7sjEIQ3WRcZxa/U4Yps1fPdrJLH2yKqbM4/inobgzk/3RPeabt5n1B\r\n0nGlct5z1k8D00l//JZVX8SK0eOtX7y863oHGE1qvD5/ts7xVxhrl6CtcpDY\r\n7bU0RvIOxmZvPAavc/oyCO5L+B5nHGMvzQI=\r\n=qHSW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/color.cjs", "type": "module", "types": "dist/color.d.ts", "module": "dist/color.esm.js", "exports": {"types": "./dist/color.d.ts", "import": "./dist/color.esm.js", "require": "./dist/color.cjs"}, "gitHead": "5e025324bae1213c84a46e92b02a48d7bb8212b0", "scripts": {"lint": "eslint src/*.js test/*.js util/*.js", "test": "node test/index.js", "build": "node util/copy_dist.js && rollup -c"}, "_npmUser": {"name": "kurkle", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kurkle/color.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "css color parsing, manupulation and conversion", "directories": {}, "_nodeVersion": "16.18.1", "_hasShrinkwrap": false, "devDependencies": {"fs": "0.0.1-security", "util": "^0.12.3", "assert": "^2.0.0", "eslint": "^8.12.0", "rollup": "^3.5.0", "typedoc": "^0.23.21", "benchmark": "^2.1.4", "chroma-js": "^2.1.1", "color-name": "^2.0.0", "perf_hooks": "0.0.1", "tinycolor2": "^1.4.2", "typescript": "^4.9.3", "color-names": "^2.0.0", "color-parse": "^1.4.2", "color-parser": "^0.1.0", "color-string": "^1.5.5", "chartjs-color": "^2.4.1", "child_process": "^1.0.2", "csscolorparser": "^1.0.3", "eslint-plugin-react": "^7.22.0", "chartjs-color-string": "^0.6.0", "eslint-plugin-import": "^2.22.1", "@rollup/plugin-terser": "^0.1.0", "eslint-config-chartjs": "^0.3.0", "rollup-plugin-cleanup": "^3.2.1", "eslint-config-defaults": "^9.0.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-istanbul": "^4.0.0", "rollup-plugin-visualizer": "^5.8.3"}, "_npmOperationalInternal": {"tmp": "tmp/color_0.3.0_1669573725106_0.5053154611320378", "host": "s3://npm-registry-packages"}}, "0.3.1": {"name": "@kurkle/color", "version": "0.3.1", "keywords": ["color", "colour", "css", "hsl", "hex", "rgb", "rgba", "hwb", "hsv", "cmyk"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@kurkle/color@0.3.1", "maintainers": [{"name": "kurkle", "email": "<EMAIL>"}], "homepage": "https://github.com/kurkle/color#readme", "bugs": {"url": "https://github.com/kurkle/color/issues"}, "dist": {"shasum": "ef72bc8022ccf77cdd2715097f062ee591ec145c", "tarball": "https://registry.npmjs.org/@kurkle/color/-/color-0.3.1.tgz", "fileCount": 8, "integrity": "sha512-hW0GwZj06z/ZFUW2Espl7toVDjghJN+EKqyXzPSV8NV89d5BYp5rRMBJoc+aUN0x5OXDMeRQHazejr2Xmqj2tw==", "signatures": [{"sig": "MEUCIAkQVt0SCXvVRUhd8fVt7znQuLrMKIfEnnbrX2umODuCAiEArhlcLgU9pGdmN2rm/axvtg/PUVZDIKUsP6Y4CcDhd0Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77903, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjlQQFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrSgxAAiHISOuvherLKD56aoj/+T592ZLPtqOO5HMy40cAt+SmnGwNi\r\niQ/4Kloy5tTapRwBT44YYkUHikj5H42YDBLKdMzieaiuCwp1wUmnuhmmdGqE\r\ni6A14XssS+zYxw3IDo1UqMOGaR1cnTUNZGFY4BleL6amZlL1oqBWkP2QzY/O\r\neFEy3T+Cug54MVUJtO/kVilhKH2vPDSPo4YpN2heRLLh13pK3eBUZ87BBBlx\r\nYgo84yi6uC9OAYdIJ0dDaJ4sgcQLhwKZ6A6s+adSTWVMapJG418txkjnespv\r\nvxq0j/VXGJnb0ZsI19Y4lJ/AdntTrjS0UCne27qsgut104mUC7tZj54Fun7Q\r\n3jLYLxYMLCS6xzufT/+ANcWnb3xEtgw7aFZHXJ2DbNsoCgxPl3pgxFlLqSmz\r\nUCmbvUYXZO2s1FwCFDDtcOEpH7iAyOSr1zG1J+Z7fDi4eWQy1btNE2Vw1b4b\r\nYx2+zVqo/G3Rvkc7w3rll1yzCXu5tqNCkidofFsOLOBMeJo7rzETrwwJxLa3\r\nMIrmFTvdv5G/ztC/H/SmMCcpN8f4wIjPoTrEEa7pYGc9FeNHUOjCpy1NgNqi\r\naqEhKZa8PTtXOoPd1a1trAYANYKKSIYIZEXvVYxpLykCtfBBDYkuEzb5Ce7C\r\n8AsjC9uyOub4mmCt4VCDE09ZDDVfOG8toMA=\r\n=sPho\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/color.cjs", "type": "module", "types": "dist/color.d.ts", "module": "dist/color.esm.js", "exports": {"types": "./dist/color.d.ts", "import": "./dist/color.esm.js", "require": "./dist/color.cjs"}, "gitHead": "e59468f3fdd4ee8e81d1396b31cb9ce136787c33", "scripts": {"lint": "eslint src/*.js test/*.js util/*.js", "test": "node test/index.js", "build": "node util/copy_dist.js && rollup -c"}, "_npmUser": {"name": "kurkle", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kurkle/color.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "css color parsing, manupulation and conversion", "directories": {}, "sideEffects": false, "_nodeVersion": "16.18.1", "_hasShrinkwrap": false, "devDependencies": {"fs": "0.0.1-security", "util": "^0.12.3", "assert": "^2.0.0", "eslint": "^8.12.0", "rollup": "^3.5.0", "typedoc": "^0.23.21", "benchmark": "^2.1.4", "chroma-js": "^2.1.1", "color-name": "^2.0.0", "perf_hooks": "0.0.1", "tinycolor2": "^1.4.2", "typescript": "^4.9.3", "color-names": "^2.0.0", "color-parse": "^1.4.2", "color-parser": "^0.1.0", "color-string": "^1.5.5", "chartjs-color": "^2.4.1", "child_process": "^1.0.2", "csscolorparser": "^1.0.3", "eslint-plugin-react": "^7.22.0", "chartjs-color-string": "^0.6.0", "eslint-plugin-import": "^2.22.1", "@rollup/plugin-terser": "^0.1.0", "eslint-config-chartjs": "^0.3.0", "rollup-plugin-cleanup": "^3.2.1", "eslint-config-defaults": "^9.0.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-istanbul": "^4.0.0", "rollup-plugin-visualizer": "^5.8.3"}, "_npmOperationalInternal": {"tmp": "tmp/color_0.3.1_1670710277729_0.9784268204422815", "host": "s3://npm-registry-packages"}}, "0.3.2": {"name": "@kurkle/color", "version": "0.3.2", "keywords": ["color", "colour", "css", "hsl", "hex", "rgb", "rgba", "hwb", "hsv", "cmyk"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@kurkle/color@0.3.2", "maintainers": [{"name": "kurkle", "email": "<EMAIL>"}], "homepage": "https://github.com/kurkle/color#readme", "bugs": {"url": "https://github.com/kurkle/color/issues"}, "dist": {"shasum": "5acd38242e8bde4f9986e7913c8fdf49d3aa199f", "tarball": "https://registry.npmjs.org/@kurkle/color/-/color-0.3.2.tgz", "fileCount": 8, "integrity": "sha512-fuscdXJ9G1qb7W8VdHi+IwRqij3lBkosAm4ydQtEmbY58OzHXqQhvlxqEkoz0yssNVn38bcpRWgA9PP+OGoisw==", "signatures": [{"sig": "MEUCIQDBcktnegX8YKIt0m2SylOsMuV/YmxxUO/xJsIZhCIvnwIgZkFYToIWMuJXtvkwY4cUb4R0oRBErIRHugLh8lBBCWI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79214, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjvZepACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpuOw//RK42KYL/6c4QovOPEIzSLYylsDaV7hC7tFCvrzrgw/0Npjs8\r\nrt3lQ2Jl+MaK+YzOpjsjlgo5+eZBqjSXPFHsBPcSipypq87zYssL5bdfBzFc\r\ng7PBlKieRnxLAyxno4ftDYqgY4Y26tbqrKcBXYoHsJPL64CR5eKMl9nrf7ON\r\niE7xgXlaERWAD5ue879FD/wgoVEPWh/kMsFAuCuZgG7Az83mEJ6i8HTuf9+b\r\nhVVK3acbOQ2cDxbFHUbGt27O33Rm2FkG8Xj6AZH8xAwgrlU6oGrWY+tvOY5W\r\ns7+lZRdNyKQu05Af2N0pmmOYQ5XsQX6Vh8MKQJVNrEOnGjZyewy6dwckOBzN\r\nIE195BVAtwvs8ZSeLPQ4Hn2z43lEk98BDWP6xqQlchpIfqaHGF6oPmqANBwx\r\nR84F0BvhBKwof1/5fGI0PHEPKHNd7BH5rQSNST/yxxi/h9nvKfJYpwIbAS9D\r\nNBb4/JpaHWIBoOZ+VYHClWuVfWlhT+bG7yDJBJeIZ8dlTrirIg+6x+Ir415T\r\nG+cvZBFV+UMisc/0djRRZaTe2O4xisOCHeFxa09CL7eJ826gvFvNpvGFx9/4\r\nzi2TjtJK6bfXcCG9dG1ymuy/BDLopX0hh/XfBWaCzs0/yUcEYYJfmmpkTzBF\r\nRZ7JMkxcUpwhWkb1/4E0gFOMBKojrJrm5SE=\r\n=3MGk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/color.cjs", "type": "module", "types": "dist/color.d.ts", "module": "dist/color.esm.js", "exports": {"types": "./dist/color.d.ts", "import": "./dist/color.esm.js", "require": "./dist/color.cjs"}, "gitHead": "eb0d1db26a35704553c93782bad8c3ac2d971597", "scripts": {"lint": "eslint src/*.js test/*.js util/*.js", "test": "node test/index.js", "build": "node util/copy_dist.js && rollup -c"}, "_npmUser": {"name": "kurkle", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kurkle/color.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "css color parsing, manupulation and conversion", "directories": {}, "sideEffects": false, "_nodeVersion": "16.18.1", "_hasShrinkwrap": false, "devDependencies": {"fs": "0.0.1-security", "util": "^0.12.3", "assert": "^2.0.0", "eslint": "^8.12.0", "rollup": "^3.5.0", "typedoc": "^0.23.21", "benchmark": "^2.1.4", "chroma-js": "^2.1.1", "color-name": "^2.0.0", "perf_hooks": "0.0.1", "tinycolor2": "^1.4.2", "typescript": "^4.9.3", "color-names": "^2.0.0", "color-parse": "^1.4.2", "color-parser": "^0.1.0", "color-string": "^1.5.5", "chartjs-color": "^2.4.1", "child_process": "^1.0.2", "csscolorparser": "^1.0.3", "eslint-plugin-react": "^7.22.0", "chartjs-color-string": "^0.6.0", "eslint-plugin-import": "^2.22.1", "@rollup/plugin-terser": "^0.1.0", "eslint-config-chartjs": "^0.3.0", "rollup-plugin-cleanup": "^3.2.1", "eslint-config-defaults": "^9.0.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-istanbul": "^4.0.0", "rollup-plugin-visualizer": "^5.8.3"}, "_npmOperationalInternal": {"tmp": "tmp/color_0.3.2_1673369513471_0.7506685651229636", "host": "s3://npm-registry-packages"}}, "0.3.3": {"name": "@kurkle/color", "version": "0.3.3", "keywords": ["color", "colour", "css", "hsl", "hex", "rgb", "rgba", "hwb", "hsv", "cmyk"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "@kurkle/color@0.3.3", "maintainers": [{"name": "kurkle", "email": "<EMAIL>"}], "homepage": "https://github.com/kurkle/color#readme", "bugs": {"url": "https://github.com/kurkle/color/issues"}, "dist": {"shasum": "c3bebe3093e896f19612b5adac018de29952ec8a", "tarball": "https://registry.npmjs.org/@kurkle/color/-/color-0.3.3.tgz", "fileCount": 8, "integrity": "sha512-6ytplrHo8wTnrKrhdnuo1fRZoIJ2nnSwkxi6z2c38tSqAuqgV8E0zZH7JhXi+LZS9DiBgswFNDIhOzS9mg1Nyw==", "signatures": [{"sig": "MEUCIEQEYL336cRBnJJTmVYPJ+HFtvEyMTIJ9kZFg9B7eAU7AiEAt1cxLAr/bNjAoWwqCGuP7g748fZW39ZHdHHyekgPQSI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78960}, "main": "dist/color.cjs", "type": "module", "types": "dist/color.d.ts", "module": "dist/color.esm.js", "exports": {"types": "./dist/color.d.ts", "import": "./dist/color.esm.js", "require": "./dist/color.cjs"}, "gitHead": "a262189aa955fa0fc9bd7df3b38caf37a61c870c", "scripts": {"lint": "eslint src/*.js test/*.js util/*.js", "test": "node test/index.js", "build": "node util/copy_dist.js && rollup -c"}, "_npmUser": {"name": "kurkle", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/kurkle/color.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "css color parsing, manupulation and conversion", "directories": {}, "sideEffects": false, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "devDependencies": {"fs": "0.0.1-security", "util": "^0.12.3", "assert": "^2.0.0", "eslint": "^9.15.0", "rollup": "^4.25.0", "typedoc": "^0.26.7", "benchmark": "^2.1.4", "chroma-js": "^3.1.1", "color-name": "^2.0.0", "perf_hooks": "0.0.1", "tinycolor2": "^1.4.2", "typescript": "^5.6.2", "color-names": "^2.0.0", "color-parse": "^2.0.2", "color-parser": "^0.1.0", "color-string": "^1.5.5", "chartjs-color": "^2.4.1", "child_process": "^1.0.2", "csscolorparser": "^1.0.3", "eslint-plugin-react": "^7.22.0", "chartjs-color-string": "^0.6.0", "eslint-plugin-import": "^2.22.1", "@rollup/plugin-terser": "^0.4.0", "eslint-config-chartjs": "^0.3.0", "rollup-plugin-cleanup": "^3.2.1", "eslint-config-defaults": "^9.0.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-istanbul": "^5.0.0", "rollup-plugin-visualizer": "^5.8.3"}, "_npmOperationalInternal": {"tmp": "tmp/color_0.3.3_1732041167369_0.7952637739593456", "host": "s3://npm-registry-packages"}}, "0.3.4": {"name": "@kurkle/color", "type": "module", "version": "0.3.4", "description": "css color parsing, manupulation and conversion", "sideEffects": false, "main": "dist/color.cjs", "module": "dist/color.esm.js", "types": "dist/color.d.ts", "exports": {"types": "./dist/color.d.ts", "import": "./dist/color.esm.js", "require": "./dist/color.cjs"}, "scripts": {"build": "node util/copy_dist.js && rollup -c", "lint": "eslint src/*.js test/*.js util/*.js", "test": "node test/index.js"}, "repository": {"type": "git", "url": "git+https://github.com/kurkle/color.git"}, "keywords": ["color", "colour", "css", "hsl", "hex", "rgb", "rgba", "hwb", "hsv", "cmyk"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/kurkle/color/issues"}, "homepage": "https://github.com/kurkle/color#readme", "devDependencies": {"@rollup/plugin-terser": "^0.4.0", "assert": "^2.0.0", "benchmark": "^2.1.4", "chartjs-color": "^2.4.1", "chartjs-color-string": "^0.6.0", "child_process": "^1.0.2", "chroma-js": "^3.1.1", "color-name": "^2.0.0", "color-names": "^2.0.0", "color-parse": "^2.0.2", "color-parser": "^0.1.0", "color-string": "^1.5.5", "csscolorparser": "^1.0.3", "eslint": "^9.15.0", "eslint-config-chartjs": "^0.3.0", "eslint-config-defaults": "^9.0.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-react": "^7.22.0", "fs": "0.0.1-security", "perf_hooks": "0.0.1", "rollup": "^4.25.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-cleanup": "^3.2.1", "rollup-plugin-istanbul": "^5.0.0", "rollup-plugin-visualizer": "^5.8.3", "tinycolor2": "^1.4.2", "typedoc": "^0.26.7", "typescript": "^5.6.2", "util": "^0.12.3"}, "_id": "@kurkle/color@0.3.4", "gitHead": "a0c1acd578abdff7918fcd4365a9651639872412", "_nodeVersion": "18.20.5", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-M5UknZPHRu3DEDWoipU6sE8PdkZ6Z/S+v4dD+Ke8IaNlpdSQah50lz1KtcFBa2vsdOnwbbnxJwVM4wty6udA5w==", "shasum": "4d4ff677e1609214fc71c580125ddddd86abcabf", "tarball": "https://registry.npmjs.org/@kurkle/color/-/color-0.3.4.tgz", "fileCount": 8, "unpackedSize": 78965, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD8+FO4gMdmmcQMa42DXRlkyUh9OTKqRqs1l5lJ8FnLOAIhAMrLmfhOmg+j2bjSkO267jRULJy6f5wsuGhOuLVHSJHN"}]}, "_npmUser": {"name": "kurkle", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "kurkle", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/color_0.3.4_1732041849931_0.3788274428974501"}, "_hasShrinkwrap": false}}, "time": {"created": "2020-02-05T19:59:45.029Z", "modified": "2024-11-19T18:44:10.285Z", "0.1.0": "2020-02-05T19:59:45.167Z", "0.1.1": "2020-02-05T22:26:52.891Z", "0.1.2": "2020-02-09T20:06:23.099Z", "0.1.3": "2020-02-09T20:31:13.167Z", "0.1.4": "2020-02-21T16:19:25.522Z", "0.1.5": "2020-02-21T16:34:12.896Z", "0.1.6": "2020-02-21T17:04:47.247Z", "0.1.7": "2020-02-23T07:24:03.263Z", "0.1.8": "2020-06-04T20:41:05.225Z", "0.1.9": "2020-06-09T04:19:28.262Z", "0.2.0": "2022-05-22T20:46:25.511Z", "0.2.1": "2022-05-23T14:49:00.597Z", "0.3.0": "2022-11-27T18:28:45.289Z", "0.3.1": "2022-12-10T22:11:17.886Z", "0.3.2": "2023-01-10T16:51:53.673Z", "0.3.3": "2024-11-19T18:32:47.571Z", "0.3.4": "2024-11-19T18:44:10.106Z"}, "bugs": {"url": "https://github.com/kurkle/color/issues"}, "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "homepage": "https://github.com/kurkle/color#readme", "keywords": ["color", "colour", "css", "hsl", "hex", "rgb", "rgba", "hwb", "hsv", "cmyk"], "repository": {"type": "git", "url": "git+https://github.com/kurkle/color.git"}, "description": "css color parsing, manupulation and conversion", "maintainers": [{"name": "kurkle", "email": "<EMAIL>"}], "readme": "# @kurkle/color\n\n[![npm](https://img.shields.io/npm/v/@kurkle/color?style=plastic)](https://www.npmjs.com/package/@kurkle/color) [![release](https://img.shields.io/github/release/kurkle/color.svg?style=plastic)](https://github.com/kurkle/color/releases/latest) [![npm bundle size](https://img.shields.io/bundlephobia/minzip/@kurkle/color?style=plastic)](https://www.npmjs.com/package/@kurkle/color) [![GitHub Workflow Status](https://img.shields.io/github/actions/workflow/status/kurkle/color/ci.yml?style=plastic)](https://github.com/kurkle/color) [![GitHub](https://img.shields.io/github/license/kurkle/color?style=plastic)](https://github.com/kurkle/color/blob/main/LICENSE.md)\n\n## Overview\n\nFast and small CSS color parsing and manipulation library.\n\n## Parsing\n\nSupported formats:\n\n- named\n\n```text\nblue\ntransparent\n```\n\n- hex\n\n```text\n#aaa\n#bbba\n#1A2b3c\n#f1f2f388\n```\n\n- rgb(a)\n\n```text\nrgb(255, 255, 255)\nrgb(255, 0, 0, 0.5)\nrgb(50%, 50%, 50%, 50%)\nrgb(0 0 100% / 80%)\nrgba(200, 20, 233, 0.2)\nrgba(200, 20, 233, 2e-1)\n```\n\n- hsl(a)\n\n```text\nhsl(240deg, 100%, 50.5%)\nhsl(0deg 100% 50%)\nhsla(12, 10%, 50%, .3)\nhsla(-1.2, 10.2%, 50.9%, 0.4)\n```\n\n- hwb\n\n```text\nhwb(240, 100%, 50.5%)\nhwb(244, 100%, 100%, 0.6)\n```\n\n- hsv\n\n```text\nhsv(240, 100%, 50.5%)\nhsv(244, 100%, 100%, 0.6)\n```\n\n## Docs\n\n[typedocs](https://kurkle.github.io/color/)\n\n**note** The docs are for the ESM module. UMD module only exports the [default export](https://kurkle.github.io/color/modules.html#default)\n\n## Benchmarks\n\n[benchmarks](https://kurkle.github.io/color/dev/bench/)\n\n## Size visualization\n\n[color.min.js](https://kurkle.github.io/color/stats.html)\n\n## License\n\n`@kurkle/color` is available under the [MIT license](https://github.com/kurkle/color/blob/main/LICENSE.md).\n", "readmeFilename": "README.md"}