{"_id": "is-keyword-js", "_rev": "15-f4063aa1d3b71d3aaf166433250daaae", "name": "is-keyword-js", "description": "A simple way to check if a string is a keyword or reserved keyword in JavaScript", "dist-tags": {"latest": "1.0.3"}, "versions": {"1.0.0": {"name": "is-keyword-js", "version": "1.0.0", "description": "A simple way to check if a string is a keyword or reserved keyword in JavaScript", "main": "index.js", "scripts": {"test": "nodejs test.js"}, "files": ["index.js"], "homepage": "https://github.com/CrissDev/is-keyword-js", "repository": {"type": "git", "url": "https://github.com/CrissDev/is-keyword-js"}, "keywords": ["javascript", "keyword", "reserved"], "engines": {"node": ">=0.10.0"}, "author": {"name": "crissdev", "email": "<EMAIL>", "url": "http://crissdev.com/"}, "license": "MIT", "bugs": {"url": "https://github.com/CrissDev/is-keyword-js/issues"}, "devDependencies": {"ava": "0.0.4"}, "_id": "is-keyword-js@1.0.0", "dist": {"shasum": "c45c2f0cfa69e11b090d379bde7da39a86d89b15", "tarball": "https://registry.npmjs.org/is-keyword-js/-/is-keyword-js-1.0.0.tgz", "integrity": "sha512-hZnKvF/7PbHO57zhnVbPWUVh54kIHCQ2Wb9iONEaDReF2WvdZWjusnjc/oWzbJQW3LiE8I3im4gbYzBpk1/d1w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA7jhWIZ0Bf+B5PFCyPkFT+vI475Sp7rKH1U0NxYmtpAAiAO6HQfyxQRt9RA0oK/DbWqe9cOkD3ajrayeNZAUAIoeQ=="}]}, "_from": ".", "_npmVersion": "1.3.10", "_npmUser": {"name": "crissdev", "email": "<EMAIL>"}, "maintainers": [{"name": "crissdev", "email": "<EMAIL>"}]}, "1.0.1": {"name": "is-keyword-js", "version": "1.0.1", "description": "A simple way to check if a string is a keyword or reserved keyword in JavaScript", "main": "index.js", "scripts": {"test": "node test.js"}, "files": ["index.js"], "homepage": "https://github.com/CrissDev/is-keyword-js", "repository": {"type": "git", "url": "https://github.com/CrissDev/is-keyword-js"}, "keywords": ["javascript", "keyword", "reserved"], "engines": {"node": ">=0.10.0"}, "author": {"name": "crissdev", "email": "<EMAIL>", "url": "http://crissdev.com/"}, "license": "MIT", "bugs": {"url": "https://github.com/CrissDev/is-keyword-js/issues"}, "devDependencies": {"ava": "0.0.4"}, "_id": "is-keyword-js@1.0.1", "dist": {"shasum": "feaed78b60f6bce7df8745703ead597b653c5c48", "tarball": "https://registry.npmjs.org/is-keyword-js/-/is-keyword-js-1.0.1.tgz", "integrity": "sha512-/7ssb1u2Ak8k8ySaX6aEgZNYhwJp1o6LFB3qLVEJPphbBiwV0Ov9Mka1h46g/p1LN2jg/NUPgMX+pN1TOx/Dbg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFVgfgJRrsLdHB0TkkPgpfoc/Bsdy831Y348ZEpqRFW3AiEA1rZXHAQx4wMtTo5C33d6pkJ+FhOU9wSUumYbzpaY7U0="}]}, "_from": ".", "_npmVersion": "1.3.10", "_npmUser": {"name": "crissdev", "email": "<EMAIL>"}, "maintainers": [{"name": "crissdev", "email": "<EMAIL>"}]}, "1.0.2": {"name": "is-keyword-js", "version": "1.0.2", "description": "A simple way to check if a string is a keyword or reserved keyword in JavaScript", "main": "index.js", "scripts": {"test": "node test.js"}, "files": ["index.js"], "homepage": "https://github.com/crissdev/is-keyword-js", "repository": {"type": "git", "url": "https://github.com/crissdev/is-keyword-js"}, "keywords": ["javascript", "keyword", "reserved"], "engines": {"node": ">=0.10.0"}, "author": {"name": "crissdev", "email": "<EMAIL>", "url": "http://crissdev.com"}, "license": "MIT", "bugs": {"url": "https://github.com/crissdev/is-keyword-js/issues"}, "devDependencies": {"ava": "0.0.4"}, "gitHead": "9cf95131d4e1f843b72329790d352503c5a40e07", "_id": "is-keyword-js@1.0.2", "_shasum": "41ab729dc406a66516d06882cbc54857c099b909", "_from": ".", "_npmVersion": "2.1.2", "_nodeVersion": "0.10.32", "_npmUser": {"name": "crissdev", "email": "<EMAIL>"}, "maintainers": [{"name": "crissdev", "email": "<EMAIL>"}], "dist": {"shasum": "41ab729dc406a66516d06882cbc54857c099b909", "tarball": "https://registry.npmjs.org/is-keyword-js/-/is-keyword-js-1.0.2.tgz", "integrity": "sha512-gF+ucMJAtgh8vjqQB/hbmnh2SirPI71lgn9yO2s2VOySuh1JfbDtwa7O6JNJn7OHxy1V3mDj6UMkiHeVH9eaXQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCOhEcFtMmuTRMLEU8hF383cEsWw+SZT1g2r4k2cQ/5HQIgWXsulOPzEbrYlv2bqwTvJUZBIZQRFI0DV7iYfw7XagM="}]}}, "1.0.3": {"name": "is-keyword-js", "version": "1.0.3", "description": "A simple way to check if a string is a keyword or reserved keyword in JavaScript", "main": "index.js", "scripts": {"test": "node test.js"}, "files": ["index.js"], "homepage": "https://github.com/crissdev/is-keyword-js", "repository": {"type": "git", "url": "https://github.com/crissdev/is-keyword-js"}, "keywords": ["javascript", "keyword", "reserved"], "engines": {"node": ">=0.10.0"}, "author": {"name": "crissdev", "email": "<EMAIL>", "url": "http://crissdev.com"}, "license": "MIT", "bugs": {"url": "https://github.com/crissdev/is-keyword-js/issues"}, "devDependencies": {"ava": "0.0.4"}, "gitHead": "280bd0e1ae6d45b9fa5f7507060c1f34ba725cc8", "_id": "is-keyword-js@1.0.3", "_shasum": "ac30dcf35b671f4b27b17f5cb57235126021132d", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "crissdev", "email": "<EMAIL>"}, "maintainers": [{"name": "crissdev", "email": "<EMAIL>"}], "dist": {"shasum": "ac30dcf35b671f4b27b17f5cb57235126021132d", "tarball": "https://registry.npmjs.org/is-keyword-js/-/is-keyword-js-1.0.3.tgz", "integrity": "sha512-EW8wNCNvomPa/jsH1g0DmLfPakkRCRTcTML1v1fZMLiVCvQ/1YB+tKsRzShBiWQhqrYCi5a+WsepA4Z8TA9iaA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFc77w8vCCoMMVemV8oqDGjIXsAtQ6kT0UHInUDrcCT/AiEAyu/qwCT22dwFuPEQcVr3mWFGDdnVgZ5s1MXxOV0urYY="}]}}}, "readme": "# is-keyword-js\n\n[![Build Status](https://travis-ci.org/crissdev/is-keyword-js.png?branch=master)](https://travis-ci.org/crissdev/is-keyword-js)\n\n> Check if a given string is a Javascript keyword or reserved for future keywords.\n\n\n## Install\n\n```sh\n$ npm install --save is-keyword-js\n```\n\n\n## Usage\n\n```js\nvar isKeyword = require('is-keyword-js');\nisKeyword('this'); // returns true\n```\n\n\n## LICENSE\n\nMIT © [<PERSON><PERSON><PERSON>](http://crissdev.com)\n", "maintainers": [{"name": "crissdev", "email": "<EMAIL>"}], "time": {"modified": "2022-06-19T02:44:33.107Z", "created": "2014-09-18T14:50:49.840Z", "1.0.0": "2014-09-18T14:50:52.056Z", "1.0.1": "2014-09-18T14:59:05.418Z", "1.0.2": "2014-10-04T14:17:13.089Z", "1.0.3": "2014-12-21T00:59:11.166Z"}, "readmeFilename": "README.md", "homepage": "https://github.com/crissdev/is-keyword-js", "keywords": ["javascript", "keyword", "reserved"], "repository": {"type": "git", "url": "https://github.com/crissdev/is-keyword-js"}, "author": {"name": "crissdev", "email": "<EMAIL>", "url": "http://crissdev.com"}, "bugs": {"url": "https://github.com/crissdev/is-keyword-js/issues"}, "license": "MIT", "users": {"qingleili": true}}