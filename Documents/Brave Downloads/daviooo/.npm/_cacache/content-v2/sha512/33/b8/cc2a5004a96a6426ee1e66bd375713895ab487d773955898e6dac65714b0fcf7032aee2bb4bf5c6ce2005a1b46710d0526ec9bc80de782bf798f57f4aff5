{"name": "@npmcli/run-script", "dist-tags": {"latest": "9.1.0"}, "versions": {"1.0.0": {"name": "@npmcli/run-script", "version": "1.0.0", "dependencies": {"read-package-json-fast": "^1.1.3"}, "devDependencies": {"tap": "^14.10.6", "minipass": "^3.1.1", "require-inject": "^1.4.4"}, "dist": {"shasum": "6c8de7f42fd8b208f9434173455d63370271aa6f", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-1.0.0.tgz", "fileCount": 10, "integrity": "sha512-m2WRAiDcow+uuq2aAya2Yb3JfBDspnAacJL71eO41/kQSfBtO05lUcYs8NgburoErY3AiCXuxNO2y4AqWRaObw==", "signatures": [{"sig": "MEUCIQDLyANald3VLQmw19lhlo93TeEUQy4ZTh97RKTWEXQzBQIgJbGEr1R8GpxaaYfKhbP6U5JccxEbdttqptXBcIZvNn4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11589, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeKM6ICRA9TVsSAnZWagAAg8kP/2f4ixPzkixcaTkg4VSt\nc7O9YtCctCP8ucLBIDvuKYdJlZ3drQkRCSQ6ivScvDg+sDM/Jdm/0RR71PX3\nK3X0hZxYlsWjw/1jHWjHgWtKjsJEdMd9P+OhVGzRo+s/sk0r6wBeKBv4pY9g\nqOySRvp7EyGt4f5PVCc56Tvk0gkiK/5Mae1KjPxp5rSCjbeonw8EX6RfuVIH\nrNEXLQIwTJyq3LGoMMb0mk9Q/5dIdkDk6fqm5QDZLLAyX0mOjLwbQJkf5u2v\nBQmBiKqlHjXyQufs1avlUnPkrsmk+uaGuUKiqadVEI/5MVmqMQqQ9oA4N3d0\n6WIiUjC5ypVaAETyAUH4CmGNOYcfP+nMiTINaS9ipG//CWyGj+lcBSQ3xeFz\noenyAPMhpakmEPyXUxy0k+urnSmRisjpLgUC6ZRdqM8IRXR6dLHKAIYPR9hZ\n5vKx8/a6BOi6I3GvsE4mBD/z65YZ8XD0pPNvXB7x8O8riYWK01P4G4fS9wFy\nHsfHRkiyHFp32zaKur7pe2O3+tcOnoWX/bnXvSjCWAMrbipLRWGrlEoHCPyq\nkmoD8sXhPvfdidzKQe5Cdbz5DoNGcDxriPdGhh4zdOUzjFP9+w9DxZZ0IGKT\nRgywWK9eHtGj3iROd+eTNAlGj1t4Qx8DRcYb0hxzgBkOFmhWxf8TteHXZNJP\nHZU9\r\n=bw4l\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1": {"name": "@npmcli/run-script", "version": "1.0.1", "dependencies": {"read-package-json-fast": "^1.1.3"}, "devDependencies": {"tap": "^14.10.6", "minipass": "^3.1.1", "require-inject": "^1.4.4"}, "dist": {"shasum": "8ec8c712b0219bb62b92a7776e9652a2846e0074", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-1.0.1.tgz", "fileCount": 10, "integrity": "sha512-+/nu7aadES7vE6usy2FMlPFzQM7JSt+Y/KmZ3NBbmOuNuks7VtmGl29QEHwyLUttLks2j8HEwNDDZb5l5h4lHg==", "signatures": [{"sig": "MEQCICYILzvx0M2alBhwv8o5NUmHDuhRttu1LHmIHJRkr0HEAiB5080V0WoghNfJ1hAol8rA+EBZUihoEWQ/XcC3s2Whnw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11717, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeKM+PCRA9TVsSAnZWagAAsckP/RRAYmKkApiiRP1RYUL/\nrduZACgVU2gV03g7u+2ERxhyhjLQfvczKBEi2H245h8xsrqxI90J7w/k6vMo\nHVP8SXkA9ND7Wz8RvL4bfa8TVDQ5filY037exx0UrD24PvkG4m903QVedweX\n/6wmuxE7sHjqIS4LkwKjNUaDpNhCHzXNRk5dsvrZzBXfP3VySiUFunqql5My\nAedveTDIc9J3glPGqhhaWU6qClzZECANy6qYkOvQ2XiacpY7Wz2Sn+Qn9a4j\nybrGI12j8tp8M+DpA9K668wdQAmiN0I/LcBCIxzKXasd0S+KcBXzs6ZoC6aS\noraRcvLBzy8E1a5XcEQwjmMcIxGnh6+gK2CQE9CYFYVkrUNiZ0JlhLUhYI0E\nfEWU885J/RU/V7KCtspTkfHNDmlkKN36d5Dyp6J74RrBojnwxiT9uOqsDvZk\nGF/yZWazG5h3q/cX9sCygjwgKpjnjNIkt0prHvdCtCoF2f6QUX5G1/hTzKAO\nlGcf3x0MbL8lUoXpCQJmc8L0N7r36lgf89bVSDuG+pS00zjP6oXZyi+f67lQ\nAUo83OV/PBvJwZsZ9lIF2TmphsrtNKcH95+AA7O5i4V4V/47n3R6YITuv4b/\n+0GJpkO6ZOyc417DMrn40u1GidgUsHah80L9TFmZm2p4lybbQ9MTnRwZW+qI\ni9Dk\r\n=/zXR\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.0": {"name": "@npmcli/run-script", "version": "1.1.0", "dependencies": {"infer-owner": "^1.0.4", "read-package-json-fast": "^1.1.3"}, "devDependencies": {"tap": "^14.10.6", "minipass": "^3.1.1", "require-inject": "^1.4.4"}, "dist": {"shasum": "0637c34a0b339244090a5b9ab69174d6639674f7", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-1.1.0.tgz", "fileCount": 10, "integrity": "sha512-bnrzTood5MKf9k/2YijWKpHYk1Hw9Rv1bFJd+oCOtRVmLIw+srfI/PrrE5Ae3Ds8BIoTzP9oZRcbNEY51ZP24w==", "signatures": [{"sig": "MEQCICKE59BpBVRsCxMAmSRN3i2Ej536SYH2GLdOYMBh97uvAiBJwhLn8iUBTILGRGMHwAh3mI+bP40Ong2XE/AOx728zg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12189, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeK4MwCRA9TVsSAnZWagAABasP/1MHgiY2B4yEm2qOg54p\nVgWQ7XjQMAodSa2E4lHFObfrlJkXifU6ZnXmnprL4fha6OshIQtKd8vmWEGk\naDsRMJ1qH6Ax8ITzwuWZ4QXY25oIcbDMX9dFxQ4wCgYFMM3ab8q34aBpry4X\nwzxKRKOmrF/18YeL3oHEALSRwMkBfy1uLjpvluZk1h9yvWsffEVHS3I7RqRZ\n5VSd9sVFQiQNcs6NshEBP0Ahz6yeWFtTdbaRIVynHp6fsCVulDtmbdQmobG3\ncxkxKaJzBa7gISbVq63ijvq+gzXduEe6M1WTImXwqetbOzJyHDad8OsLiQKP\nLg4hfpGAK2fiZUcde/zE6CqF2K4X3p9mpDWouYUmJXSCYYjA4FYF5371Wa0l\njiG1bKXeMJpsbUM+VbT4fLAiHzu2+RkVfIfq4cUGTIygkAdBdK4PL4X7HEVG\ngSL7y2O6lq94el34ivGvgkPCI8y/ig9t88X+umXMab6TMRGp7cP3viGpweJ8\nPCTtXDyWUBYflPUxsbD23xRetGib2Ja9SbO8acasS1uLh9iG1nxyHgpN0smH\nrrlG6TaVrcdXgVLj3uZYNRSKBczaQU4gqAqzw+MkCI2bUaISDa/N7KHbzkfu\nIKLl8jUTDCMXT7U5lqnhin9BG7K4ketzOoU5lS/zXYF+IYpZ90eWb0PIYxLd\nCqA5\r\n=ryhp\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.1": {"name": "@npmcli/run-script", "version": "1.1.1", "dependencies": {"node-gyp": "^6.1.0", "infer-owner": "^1.0.4", "read-package-json-fast": "^1.1.3"}, "devDependencies": {"tap": "^14.10.6", "minipass": "^3.1.1", "require-inject": "^1.4.4"}, "dist": {"shasum": "343dce9010cc574de7fe1de0b315e5f7d84b4153", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-1.1.1.tgz", "fileCount": 10, "integrity": "sha512-mk5WtUVrSyAH2b7TLCujZprRidjh+w/Xls0onKhSTn0muHqi/X2HEa+OJhusk8F7MxR1F3zX4au8UrPXBo/VHQ==", "signatures": [{"sig": "MEUCIQDsEw8tsi8xcw3DEOBKjJnkmM7yGUcvPsnA/7K/6uL0XgIgPIhbpfc0p3WgdjQG+WFIIgK5xb++R52H3qU/ab4rVjk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12380, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeL7RkCRA9TVsSAnZWagAAPtUP+wR2ENIrARqSus9Oke5X\ntTZmqwRNZWXwt/63qvv0bQlJ6ZOzT+IBYPxTXK2vsF+r1ROlkdyII3RScIDt\nIroE73GqIJmYpQgmSbiQF9nSW7SErVMOhh9SRT3L1wHyYiHo8WLbSvDiXDxI\nYG8rQG23RZFjdzP90HycqMjMeFnGKT3mnteNDj2mYh5HTtQ6QZSM91WbIUpk\nqN4QfkJEn+NynphP3nNbQQfZGa/UyxaYMs5CI5HPNbmGjF8l9K9uXKEzELMd\nsmAERhu8ZsxU1o4VARJLiQnOBXY6sfZxcVjV+rnd4cm1vi2Tbdd9RjTUahbR\ntPuP3WWMyY8Fj6EBPEvDxIhnBP3yhpQ0coUumA2yicgzJnYk9Jv937wGcIeQ\nHV6P9xZ0lS2OHfztJ9pLSFvhoy9L56IoRs5OVI4/lVXG1jvKnyVf3NZV7xMd\nFuXgd+MamnhQMJUo96EKan9iHOukBsQ7uHIcTC1l+M5ySo7/7eCFMmBHtnz8\nWIaMa0mY1PvvchK2qwPws39VJ3JKCo6flnWyMM6p+WNdyEhZV/oo8sShgsFL\nNb/YQjYCzpc1k5rwTgBOQfYqmPUUyBOpbPflMJ4Lyv5qUxuUX4dCtZmDOGM1\nvLZ4b79VGBoTSaM6PmUOL0rPlHHwm8rzfaBBUi7qGoHRVAsJTkRNKpgKlImt\ng1N0\r\n=mGG1\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.2": {"name": "@npmcli/run-script", "version": "1.1.2", "dependencies": {"node-gyp": "^6.1.0", "infer-owner": "^1.0.4", "read-package-json-fast": "^1.1.3"}, "devDependencies": {"tap": "^14.10.6", "minipass": "^3.1.1", "require-inject": "^1.4.4"}, "dist": {"shasum": "045230d008acb0f0fee2f65a546f02f1c5f31cbe", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-1.1.2.tgz", "fileCount": 12, "integrity": "sha512-J9AXydt1Dj3RJtjYjHHPSK9pbLcYppfckc+Nm4sTs80qgd2+CrYkdm8xGugWeMAGteVSG4MM9LpFlvkEqZ8n1A==", "signatures": [{"sig": "MEYCIQCRQttxk0v0SA+qDirA55bcCGFKSfSgYnTC3230cxq/2QIhAPaPNYIJjHbb664bNb4Im3sXGydXL2yzs/oSjZtXG711", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12489, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeL7SnCRA9TVsSAnZWagAALEsQAJXawJsfvX6TWndwlDm/\nFsQHKsJqPXtjnrmJmdp0pqG6oXS9FFlv3ayVyLOi5BcMj1b13bSmC2Vn9R1G\nnjrduf9/75R3R94o/bmsldlULvZzWcIra8DGRKB6pPLQwrRgtNEkpmS0YPu9\nOXhDkGEcNcJqlR7WEwARF6uvXJ6iE7gaTZLbrlZIHuTmB1KotxrcT9qTIrMm\n2c/SpMWSlQr6JHIroN5IYBYps6nwuF+7rTYB26wJCOjP0e31qUtLLhW12MLt\nVv5iDHp75f66rUfLMXIl21BB00qeph0Kf8smzuSjjw/fwtAvjehMsedlXaCf\nQv+ebmjVeHyyUgGJ0yZqENX/bkNoQy1+/U0x8+cOGRHs0M5HkaMo7Xkedf/G\nfSmhSzxxyyTde2sAXwsDALhnD1s4v++z6M8VOIeRq1as1F37ENtSjUSpU58k\nHkSRPX4WBLQN7nTD7nG08Khl5fzBmUIkCnFAa1UlNV1Uaz+uo0ZVgdeySKrb\no18Xstn6BZnzzuZoZe7CsY+XLRY7K2CtR2WbvslaKIgijrxMldufN+V9g1TX\np2RqpYmS1NMbViObBFFuwM6I2Niss0fdkBZ2kx1a41I73ur35gQfFQLMpiJX\nSH21U8GJ7lH+BnoqdDif5JXdq2+OTkHr5W+rPJFUcppgIbGV8i7iT9qOCHDF\nxFt+\r\n=x2YR\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.0": {"name": "@npmcli/run-script", "version": "1.2.0", "dependencies": {"node-gyp": "^6.1.0", "infer-owner": "^1.0.4", "read-package-json-fast": "^1.1.3"}, "devDependencies": {"tap": "^14.10.6", "minipass": "^3.1.1", "require-inject": "^1.4.4"}, "dist": {"shasum": "f5cc76215c258d9f2111d9b84f654ae1f38538f6", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-1.2.0.tgz", "fileCount": 12, "integrity": "sha512-U5XFaBruyUJgN7W+OIem4mKUfMDo/hLghdoiR+OKp4v4MiWnFlT17m53QFgj8TPpX2SmFw4tA9FZ+XJ7v9g28w==", "signatures": [{"sig": "MEUCIAe29Uf/sOobtCkkJzDpRvgVoy/xzsWHS2Y6RhnzHBYlAiEArbS4urWkXp5vwmH7A/Cw/FRcEZQxO+D/Y09/YIExT+I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13075, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeMH9ICRA9TVsSAnZWagAA3r4P/RX/Emdm327tdoohE8AU\nPMkKpW41uK0K1dSPDJxAxUm91PPYES6SM33q44T77wO9vQf8z6OsKb3kwcDU\nPL6Ogq9uTP3SWjuZBqxCl94P7hXA7frHM6Ru6DNLahYnn69AySN2XYx3pPnY\nley3Tcu/Ml/9Cxq9ErESUhER/UGSZvjXwWSqJ29TEc4cTIqtEY7J6hFkhSTR\nqAh6zfLuxMgO3yItCgZTFJ+EsbIRCc7qDs2t8Xa0Qp6MV9uOnpjLitDbUxnV\nRhCzMGkqkkDX05TNBaqrErzQlYEwHvsKZzfmtpQ0DGv09ahSihePLjb07rs1\nRv/O56fLzPT0QLmiqLNgxt/VOK6z/RH8lzHwgYsIfSBUCjHw28UviJAwdk5l\nMJI7q7pOGjegTyz/tEZ9lNhMVWb85bq8W4K2Fdhvrh/nvhyVWD0EvFEVoAqU\nEdgFAfAYZUJ7o2dy4OfJd5nJRth+WQjH/5BKtECedDkZTnAMSwd+YDPd3WcU\nwWJLim4hY2YILxmLWN/XgFbTC5t7LyGWR9XQ7kz7ebZ0pxdLAGs6kYbtf/El\nIvcmP98BN/wQfGFuHSj5V996ZDVsriLJYP4F8jyoYhswRj+PcO+q/z10XfBE\nvL+8joCtO7sQK/AzblH4SWZanBscTJOcbARHrlJRCZLGMHjGG/SiNSMuMUej\nu+Oy\r\n=QDkn\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.1": {"name": "@npmcli/run-script", "version": "1.2.1", "dependencies": {"node-gyp": "^6.1.0", "infer-owner": "^1.0.4", "read-package-json-fast": "^1.1.3"}, "devDependencies": {"tap": "^14.10.6", "minipass": "^3.1.1", "require-inject": "^1.4.4"}, "dist": {"shasum": "b547dc814caa71bd37dc975c998bdb05a69ebc1f", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-1.2.1.tgz", "fileCount": 12, "integrity": "sha512-pkOaDtiOM5tLCgCoGOtPpB9Op3axQEWnfVJ+4LfL8UYI1io6K7xlp4bdrcu2rBGCRN8vzyHimCiK9uWBiuGemw==", "signatures": [{"sig": "MEUCID2RUQsggbxcLtTpriLmwOjrVO5rLUZKpONg2hY8fF12AiEAjw07ZA8zgGVAsi05wy5svSs4NFaSVNxNt5Y3CgGvzZ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13077, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeMIAECRA9TVsSAnZWagAAeRcP/0PviGJJkxd23TjsTANz\nQcRdVPHf4aRZ+AbUKTEEaRrMuz+3zkvIFudaV+TJUTbvNvaKCSmd2rL4nVTB\nVMj2YxCQwcxy17cs4rTmUEobE7++4Kv1fAKkQsyWLPfxMlHefXWNoWilD16R\nrP4skeeOvEQfsiFT5I0i/OiDiovHEulJ5YmraXeD3wjw7Zx9JXFefNhcgDkO\nUo3SScYJ2hHiUd+lfHX8C3sHwBT3EpWfOFrEYWh9BOZwZQvvSkiVQwFx2RmK\nj9lvkdanVspBxRJellrktsIA8WRaAfBDHV3oJX/rtYfp3TZauL0mK1eJnmRy\nkGbbJY7YDDYcGJooa1oCtld5wbNujg6djY/iylMrnFkH4TNT0Kz9PRz5mWMT\n8hrNLfxFpzA2LwCc9gtwDVffNAShLNFFxuiNDrlG1uvoi+zZTJC9tlT7FKmb\nGFphM2kiKnkP+gVQf/thjyhHVRSnWwE9Y9pVKU9o2b3rdeHbSXJ3p51wVy6/\nzsXz2jCbBQHTn+Ss+uXa8TLzhydbopIV0UQrbDOI4vtz/DD/gGG2rgRmB6NK\n4SmJ/RKF414vDH1NSE4+yAgKYeVJOyCoBZaaC/XiC3htD/c0qIv7/sNeKjhF\nttzUi1YBo/13Zp4mtCMsHgTQau9tLc9jv0y3JiABgMGrho0O0P9l//Jh8+1q\nt9JF\r\n=+mKu\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.0": {"name": "@npmcli/run-script", "version": "1.3.0", "dependencies": {"node-gyp": "^6.1.0", "infer-owner": "^1.0.4", "@npmcli/promise-spawn": "^1.0.0", "read-package-json-fast": "^1.1.3"}, "devDependencies": {"tap": "^14.10.6", "minipass": "^3.1.1", "require-inject": "^1.4.4"}, "dist": {"shasum": "08d85c4549ead75edbf09c6954bbc50980e71d97", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-1.3.0.tgz", "fileCount": 11, "integrity": "sha512-fI2iRHMkwLDc9CFtEC77yWVaPxh65PnzyernwFdAp019WVjkSoUKXodNTleGRLNtSy7lyZqaBymlI/rteMMwIg==", "signatures": [{"sig": "MEUCIQC0qFlybZvvlmecWyw5tpCPM98lqpERbNK7NnxeZrlffgIgOkdwrgn46jxxTAo52KAJkJoY5aUwwJhXHvklKW52Tkw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11795, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeZqLyCRA9TVsSAnZWagAA+fQP/iZal6wwwLgY6fhwQSfV\nEp3v07TiZq2bhzY3IJKh28IiZ5to3eexprNNf0gfCstTis0uz7/2C2pmaiCv\nNktluZXvEi1SqtWFClKvcXokmuIMNZ5SJ8X+7pC8lKkNbQi0079jd/8BU2f7\n9Q2UZb2611Iqj6v4mgHKYCs4Lje4MN/j98obcIZxnjoljamNDgR5oZpI03Ma\nrXCKS4eD/aSLVhqp8/WeLT4hjzGkV5jimdjn9saaSKYThDwv89MMt5UDGgo0\nZ8jO5vMFW9MF7Hhr7Vadu5SMHhSy0mhahRpVw41RpH6exAZTMremMQW+WCb2\n++6MH2G5stHKVJtYMQXXa9WXenamoO5qX0hNAbP+V7Ybq0ugv3jFxJq9JRWk\nz48iKUSAYhJlXU6dDnPlSP9qcZ/FCKqsxrk3clx54DB0PGT2Nt+XpoprNsB4\nKHaKP2N0bJGn0g1H+Ech/sDtYu/drK5Vo+R1e5E6hjOSP3nKEoiKmNC17KVX\n45A0My7SFRSjrDIMpUcHPI054GuuczwlDRpJ5kJhz+OAoG822ZTx3ROlCarx\nrHq3qmmjb7GX0JJYXivsWeItHY05r24ge6fJBilbWBBfjRdPX4DO+uBYuD70\nT6gttM5YEP7j/6D+AZmpB4kQl6aISBSFidEYNzVmxnHJHwyWIynIqRZZzxEy\nvXY+\r\n=5vDV\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.1": {"name": "@npmcli/run-script", "version": "1.3.1", "dependencies": {"node-gyp": "^6.1.0", "infer-owner": "^1.0.4", "@npmcli/promise-spawn": "^1.2.0", "read-package-json-fast": "^1.1.3"}, "devDependencies": {"tap": "^14.10.6", "minipass": "^3.1.1", "require-inject": "^1.4.4"}, "dist": {"shasum": "dc8934646634338b99898614ce083dabcd14edf3", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-1.3.1.tgz", "fileCount": 11, "integrity": "sha512-9Ea57XJjNLtBFRAaiKqqdoqRrL2QkM0vvCbMjPecljhog5IHupStPtZULbl0CoGN00N3lhLWJ4PaIEC0MGjqJw==", "signatures": [{"sig": "MEUCIQCGRkaddp4QAT9CJOTUb+WTTM7xYsXQsjPGrjUET4SiCAIgU98sFrZJCgUz8KwpLKwERiGvksBYF9ZAvsFwU9aakH8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJei7BiCRA9TVsSAnZWagAAFuUQAJTsg/i2vAhicRjDtFCg\nSyRcNBlPO51zxyieg8fD93dTZtvO7wDl1wLl6D1PGvtGSa3SsNUBrkDxx4GU\nAtIvVDTHbeJfwi3poaEomWNX+OqyLE6ZJZX/uhC2m2qyOSVAuG77vSNlF6I1\nh5iTMWJNMHx14ekL6hlZIk/+oddSJAn4yLv7f0ayhB95VSXnro3+fSIFMGym\nd9MfAybcRdRMe0z+J9Z7NrcUomNQxloWXj+wAwGYngszZVzUX0lJX6sU7gPs\nDASE5T2gJSzoCqD6jPcX0XOVpUVV21KqowWIjnnYFJ+Crm5k+LRB1B00yPmz\ngs1ME5b+LBajm2OSzqe3c4ISZxc+AEGuqNo7R/9SP43J/e6OIMONjkMNr+r3\nVX9w7y/A9pJozDws6PB3RbgcRUAPEwza0/wCKa8YCb5Avop2B2rtrtk3Z4+Q\ns7kEIGdwPWy4JuvvvreHxcO9ci+nrs8nJNGIuB6xOgyzQW/jh8QoTqUDU6kH\nRcz1eJpfMhtrot1vhQ4PS3IM1enKkUg6tha1AfGT89gC+dGHrCP8+swnLAGt\nKXIaYLaLmjn+5Pv0xnWb2CoglW1yYBw8b6EZgCDFVbVag1lVhzwHJ0V32ntc\nRKRAjECI/Oh97MxkAW/2kCFKDPFGDkyB0CYuOq7cBoHC1BoTcJAoTzEHxmmv\nqlrW\r\n=Bkli\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.4.0": {"name": "@npmcli/run-script", "version": "1.4.0", "dependencies": {"node-gyp": "^6.1.0", "infer-owner": "^1.0.4", "@npmcli/promise-spawn": "^1.2.0", "read-package-json-fast": "^1.1.3"}, "devDependencies": {"tap": "^14.10.6", "minipass": "^3.1.1", "require-inject": "^1.4.4"}, "dist": {"shasum": "44c4b5581136f232aa6627166e55db1ec77ba3c9", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-1.4.0.tgz", "fileCount": 11, "integrity": "sha512-evlD0Ur2ILGyTP7FfMYi90x80bto9+nEbGjoWzdF+gmIX3HuA1nW0Ghj91JFaTJAHiXnDEEduZS24oAve/aeOA==", "signatures": [{"sig": "MEYCIQC0HvEU5egfsSGwHV/mSzObw6wpc4Frmq5zeqxLnYcSZQIhANia8doHfIz+jGf1tkXr+6uNNbuS7/sqXgAiATZygeUK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12640, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfI0R+CRA9TVsSAnZWagAAP7UQAInzWK9zws+aKpDidc0c\n+E9UwX4PpM1xcyegGONppCen7FWJ9g1MkwegmS6g2vKdMWTeVCHey8f+Netf\nq33NKZk/gDL1SUebEdFRk3BjGg1mQKtvLA92nKw+ihTMEAZ4aruVr67HNSXN\nnGQHPPu9sXZuO9CAWKwBmU5TCnaT3HuroVIyvSQgJsZ+LRvBrugZvu1jCLQq\ntBS6Sd38mpROiL3VnSRSqW3qjk7N4pQpVjPmK+87ahXj4aw5xf+Vdgl7/hz7\nKci6D4GsLJJ5bhQeXFG74yvmavedn4hUJ1iNV9WrRd8JW9J1hb4t7zH06nxF\ndhexPBhih47XHO+LW3jpsnSdGnsRs7jXv4/+d7HYwZ6FVD4uHyg1drdT5+IP\nvfGUOaMqiDgMas+LTPaL/phsKV39p4/ViVkCUAbdX48+e6BMUJGJuZAdoPiz\ncZlN36qBU58ytDrEec7MK9B1JrIoSxH+3LV84NupjIrCf2IuKUvqs6do4BlG\nO1WZWbNWC+97Z3Y1qRrUsr2yqL1/JjtbfnD3v+vXKE0pgbtVFtWmsl8Ig0qz\nz8RCYqF3EPJ2V5usOpSxvOvtiMvIWVm0mmWxRZR1LGN2ci299QK01vGWo0FA\nFNdtoHew+iWikOZr2QmKkYX/T6Cqc2t+K/cbcZgSWPc2mofhPXAVJ7MwYcaj\nUldh\r\n=u5Am\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.0": {"name": "@npmcli/run-script", "version": "1.5.0", "dependencies": {"node-gyp": "^6.1.0", "infer-owner": "^1.0.4", "@npmcli/promise-spawn": "^1.2.0", "read-package-json-fast": "^1.1.3"}, "devDependencies": {"tap": "^14.10.6", "minipass": "^3.1.1", "require-inject": "^1.4.4"}, "dist": {"shasum": "9ae63392a51adddfd7ce5956e530925bfd5487c6", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-1.5.0.tgz", "fileCount": 12, "integrity": "sha512-z7AzLmsMtVntMRJt35M5VAjb/jH6yH37Q8Ku011JVR7rEoy+p2a6/NkwqChCRZORlJaS9rwjXmZKM6UmwXLkqA==", "signatures": [{"sig": "MEUCIQDnhzRM51O2eKkWW/fEhr71gc6zcDODR1Oxk7NpV6hv4gIgQ8hDjZEjbMjjMGrTHO04e2Olu6YCWLLobsJT53Qe754=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13562, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfMdyuCRA9TVsSAnZWagAAwrgP/0M1K4pqh7c5F5rs253i\nNvr68M9LsfFQFivlckKRNs5WaLiV49sIoX8skHu1ewnSzquJF96oa8rleTw0\nwNRvy4Uu+qNVKoI3FhPyCtFV69bOLBry5gJSYxC3oqr/TSXmx7MAw7iMOdsn\nLipaxXzss06bsjzG7Sh6Sq3dMSFJnEwnCI8dvwGsAwTTbArg0lh1igIu4gBd\nI9ApEl9QFKDxKPwaOwYLHEHXMvOBvRD2m/m6Rizpl80tyM2FqytHF2NOFila\nmoXtX/IjRqR/UdpRkPckPFXkDzTPnZilkRi6tOgp/D8bADp5wnI071wR0o7J\nNhEaNbnZeUgNjrSLDqg5YxAiAegasLyOk8fGI9+RBP+1IJxVGOsOpuOdsIeN\n8x++zkEx+dFDiJYwcFJAf6buY4BcixQtCth0fqf4uQi0GtiI4JDOUIYNa+Y9\nnojX34Xyq+NezO6CWVykEF2DGrxjlsaoa9PxriU23MtsBqmouwPPiPo/zqjl\n87aed0zt40o1DUP2uPUKV8C0qbk0LS6H+YSJkrvrMkF2E9Ooynh0QiW9YZXC\noCzzCCH7RDazAYe9z1RIwsQkTMvwQOBdVdAwpsr6lP+cVV9lmJYLXluQ9yEy\nCfvcGcGNLMHfJOtfkIRfw+s3WuO9ZAFKsJt1eDLIrOU4NH/u6cz23fDT1Dvg\nqU1p\r\n=RSYr\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.6.0": {"name": "@npmcli/run-script", "version": "1.6.0", "dependencies": {"node-gyp": "^7.1.0", "infer-owner": "^1.0.4", "@npmcli/promise-spawn": "^1.2.0", "read-package-json-fast": "^1.1.3"}, "devDependencies": {"tap": "^14.10.6", "minipass": "^3.1.1", "require-inject": "^1.4.4"}, "dist": {"shasum": "926f77320ada970f1baec0387d02ec4d63878c3d", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-1.6.0.tgz", "fileCount": 12, "integrity": "sha512-2BHum2b8MBXkiWGR6c1JOQ+2eAnE8kygwtT8F5bRam7ErDJqxxcHZSksIU6/qxqnA3WuiuyvxNdnptIJr8YQ8Q==", "signatures": [{"sig": "MEYCIQC1W8GV4WFMdvxdMBTM2LnohNV5A6NkI7WQDB90heLdswIhALRARuiYmmKXbUmm1rQ1Jv/8YRSpbx7dhDjeCBdtL/EZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13562, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfaiRpCRA9TVsSAnZWagAAV6IQAJNAM0KlWhELvx+iEHMw\nifZ2Hod8aKngmkuyIT8J9CXp40mYU7uBFUePGVueu03KpRACBxIbYEZrFF3s\nJhhLrb6D33qmsBi1bu81Uxvxfm+9zV+RaFEXGn1YH2JRbt3xJiWjAbSckp7G\nHKZO1xULdgK9Dg9tFOk2N8ewsuhz/R48Ua4AZzRD9+Yih3XLvwhImBm/K8HS\n/4ik8cqscZv9yuXgfAn2TXB056nv/b9K1Hupbu0LEm/gh0RE76D7mCuFv+/b\nx3uUsa3MzZamppfmGDIDParS0Uwj4Mqzw4n7zA+q363bQvdb1pEQbNwh1eDJ\nWpzR4Eq84t9iHEztL/GT5zc2u3IGtzmuZY/wDjHPrBRbOm7vvdm0OHxdZtPG\n7aRQQD/vd11UoomFe7Q408ejhzVV4SAHwk62ns9PeJdoDyado6qbMFLKzW3I\nR2qOSaNypd8u3qojmv3DUjNitx8p9S3DvewSm9HGUPw8mkYA4MIoMtH1vpf1\ncRmzdgkA5tuKlLSzgFqLZkziqRNXzKzuwnDsmi5j5eImlyLpILnZWs476QV5\nwBJslqmYTbMSDHrroPk38CF+1CcLPSn4h/dc8mm+QxT8A4UAHtDabq+N4hfo\n/F3sWC4mHsPq4DW3DwO77huLKrUL9tlNGcpiHotJeaW/l/vPnEkaGtG3E45N\nUILU\r\n=fg38\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.0": {"name": "@npmcli/run-script", "version": "1.7.0", "dependencies": {"node-gyp": "^7.1.0", "infer-owner": "^1.0.4", "@npmcli/node-gyp": "^1.0.0", "@npmcli/promise-spawn": "^1.2.0", "read-package-json-fast": "^1.1.3"}, "devDependencies": {"tap": "^14.10.6", "minipass": "^3.1.1", "require-inject": "^1.4.4"}, "dist": {"shasum": "bfc92317321a0266943c006706a2307afe72dc20", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-1.7.0.tgz", "fileCount": 12, "integrity": "sha512-GaWLYT88H6NzOVGyXeCigijJ+eo2sdBfI67VqgkBDcR/5vElpXQH3crdfLYySPuOMZQSQXh0EsW+gC5LciFsMQ==", "signatures": [{"sig": "MEYCIQC5/eXNWTXs5CZSq8GgpF3xNz1WwjSSlKeefKHwQTWsGwIhAPGxKfTiF5NFBz+Tqn9mEsPVwuaq30Kph88Lo0Zg1vpl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13965, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfdQrOCRA9TVsSAnZWagAApbEP/Ayj2n5pIBnyEXJSDx2p\n3xrhpERetIgdjM0+EV/PL1E/hPsQYkPlwPZybOWUSYlj1bmkHAfVAUnWzBLL\naN+I9ujpZQ0dZ7GNkVeZRHRFZgNyVxrQSKjun08HVigPdOmpWkeikonlVSBL\n82aMs2+s40X5LrOvILG0H/7srJcJNOoDc9NVx16hQ/fSPnrknc+SiFW3NcMi\nOEdqp7/oZX9zVS186PjMx42RnxwvWnnkOD6jjLxnt8qjbwOixIQt4a5vSzez\ntHWli6CutQrYcGbu/K5wbtCH+e9nuk5eofMevJ2nCyPS2Hpw8nn6u1Le9IT9\nXP1QImFSoOWqTkTGEd5rT3xqVidlXgXS7OyRUkC/bwQgdlJwnZ8VN5h/mijW\n/gs3gduU29XfzJfAr1aTSF45UOysIiVMn+Xm6j9KvqRhJV8PbLOVcj/Fk+9d\nICQTcw00Ief0XgWrYL8VQZJDzlHs+q/aER5y9XSD8hG06lwYjMmc67IqXYTE\neIVrJyHOblLE76MTMy3EAg6sHqzL0WzbhzFsNOaaYotfa9joVT9hY29lRjE3\nutZbsMsxyjbr3Gjbj1drKEVRnM1WPerXVhb9ZW09ozm8mBqneSba10q00+mo\nYAxZtR1o4SM1xs1t/QofZg1KnpqiRsteVx57SX802LpElpLsWyQkSfLOaM4+\nR/H6\r\n=56XJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.1": {"name": "@npmcli/run-script", "version": "1.7.1", "dependencies": {"node-gyp": "^7.1.0", "infer-owner": "^1.0.4", "@npmcli/node-gyp": "^1.0.0", "@npmcli/promise-spawn": "^1.2.0", "read-package-json-fast": "^1.1.3"}, "devDependencies": {"tap": "^14.10.6", "minipass": "^3.1.1", "require-inject": "^1.4.4"}, "dist": {"shasum": "846bfd37416e572b69e359b1e141ff70dc115cd1", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-1.7.1.tgz", "fileCount": 12, "integrity": "sha512-16GKM0Zyw1cSfQPbBN7cwrLMneM0QhAYYiamae5w8802VKK7HT7Gd5AkJHVljsL/BYLxhTV3I5FxAjwJa7S+RA==", "signatures": [{"sig": "MEUCIGDi4XUod3FHo6brZh5LD6e4A3oet7ypCPgf7UmLBFU3AiEA9dm7bnMEzyL36GxpcZtR4b3P1JqafZJEWJ6mUyZwPjs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13977, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfd3IZCRA9TVsSAnZWagAA0MIP/jbgoscAE/ZKvnTSuVYG\nC/BFzcPemh3gQtvV/GR74j9+kDnshMrXYhnAROznZi8AtPVHK/AWSrou4gJl\nLEDYz6WmvO4s1qVtpuzYi2xScbXVc8pZWZiswsKGCISUDOqJaWoAw/5j5k5U\nkgEAMMRD3SrV6oQ/l8Sr4p2ARi55PCVqzaaL3R/hOflbs8MlvPB5aFmohvMP\nAm4SVu4TDcRz0+hulnGM65x3X0AKMZbZ67dmuDOV2RUSnrjGxP3U3qzhFB9I\nEaUs2xN9UqTkk0VB/2j4wTJZEKqwQZC54giqYnp+PRMBiQFL0dH6H9OtHDqG\noNAZUmMVJAEf1cvrxXuYG46fwOc2Pn2asSVdzUt4lk4fzJRnJCDASYlscYfs\nnG1ZpjzzjL3VIT3HiOC4xAQuGE4Pbc8RLE7gAY+7/EUCl/TzAUUC1rhRfOLQ\nopuiPnwqPQJw6VAPCY7h3ac0f693FDp7FWBbZB7hErUE9KU1eF7qDIwXEQPR\nARNryf7YgvRvtbFY7/P94OS/Sc7KtHLpSeFNBIukHtfdNobgw3cD8OwnxxUA\n3PZr1Ko0VD0iahQr166pXOWMqrsmhi2nWnAGpWHWVO0Z0vJknfA8zLSY9D1a\n1jA5Q0ODZuf+6CdtKwiyXg5lNJa7AE2erpG4h3rv1w5u8gRD5EYGrlHEEx+g\nL0NK\r\n=wVXi\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.2": {"name": "@npmcli/run-script", "version": "1.7.2", "dependencies": {"node-gyp": "^7.1.0", "infer-owner": "^1.0.4", "@npmcli/node-gyp": "^1.0.0", "@npmcli/promise-spawn": "^1.2.0", "read-package-json-fast": "^1.1.3"}, "devDependencies": {"tap": "^14.10.6", "eslint": "^7.10.0", "minipass": "^3.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1"}, "dist": {"shasum": "b5ff8c8033e713ffaee961699da1d5b67d7b51f1", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-1.7.2.tgz", "fileCount": 12, "integrity": "sha512-EZO9uXrZrfzdIJsNi/WwrP2jt1P0lbFSxOq15ljgYn1/rr4UyQXUKBZRURioFVbUb7Z1BJDEKswnWrtRybZPzw==", "signatures": [{"sig": "MEQCIHjPy6cGR0J5+Osyk3lMxhkszhSKEUDMnuj3aDJkeHtOAiBgMuZq+SgBYUIQQ059lqvwS+QKXTtverry+JyWBUlwJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14302, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfd7tkCRA9TVsSAnZWagAAzgMP/j2g47rtzGVfIQDW/PK8\nbj8eDdYERKIPX3J0pAcU5pOsVYkF82aO86r4gBKhjOOCeYuV1ARa14ux3os7\nD8dmpPSSYsv6Og1QX1MZ6F9gzAqTDixHaeq6w35iDoSjNBlh0pl28BZD/Wvc\nUKU5yay3ANdJ3QvraBx+zfkpPn5gL1IaDRypGw0ifsd14CdVaMR09tUEoHKq\n3srpOVxRwKhlqUpdHlNy8CJVoNB8Ss4PkgxIo0ivfb6A5WuYh1WTS1k0fD9u\n2eoQsfG2P5u93U336vktSTOZCm5c9yCMLJ60HhEggazDavNPb4ciHFnbzjxI\nUF+SGwHUXYn9+ekQLltxfTKeiwYB5nJxoyfNWX0tZpRT/c9ZVEzAGGhhgRlu\nJ/8l3VBKM6oPS75Kx5tCGpBqAZNeB3w12QRYTxTmrxSTzwdYPeJMHQFhUw0M\n36/VpymN2TGiExS/+3S8E/sCF0Z60EZWZqbg78JueSO1721+0RAkebzXOAAn\nOPHgEsOVCNV3lZLsbVzeDZvYarNyEtdxxYBO4nDJtH4gUaoUj1xVexF+Ls7d\noxZWmynXoQ2lbd4x1yy8OcUrQUsM1FBSte6vrjnhNRVQnK4anyn0sm7AZCFH\nHiVQ0TJCY3KXr43Whq5bs0qdD3lDO80RyrOIYw3HCRSpAl9z9N5HzFRoQu4v\nOubI\r\n=LfJ7\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.3": {"name": "@npmcli/run-script", "version": "1.7.3", "dependencies": {"node-gyp": "^7.1.0", "infer-owner": "^1.0.4", "@npmcli/node-gyp": "^1.0.0", "@npmcli/promise-spawn": "^1.3.0", "read-package-json-fast": "^1.1.3"}, "devDependencies": {"tap": "^14.10.6", "eslint": "^7.10.0", "minipass": "^3.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1"}, "dist": {"shasum": "f56d951082f4f5dbf08283e2993488c9ccd5f62e", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-1.7.3.tgz", "fileCount": 13, "integrity": "sha512-kijsbIhpdai+DjZJ8NiHpAl/bQdJf0+hv2qy4pES7QuEXdfulB04HCMbgmmpwAulE88aOXjAj1zo1h8LVMKcWg==", "signatures": [{"sig": "MEUCIQD+zVFtrWAjJWs7Ke1Zm7XYPdb/tSx2SSlpwm2m2ztwqgIgEiz4etbzkHUZD+La4D45X1QQDfdlTbHUeFNlVx1ShjA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15286, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfmGRaCRA9TVsSAnZWagAAJVsP/RPSTENptB3yJASMRGa0\nsBv9bHXWMRuvaDcqr/DULMjqmzxy9aZwGT/jBw5SMProGemrXhBrNwoo6Hrh\ne/De8831Ibh/IXT5ci52F2DG03O5x8lXLIzftKGtc8fTTo7xazASIKK2M3M1\nkN9eZ/pTmMM0q2wczopPzduOd9LEJ2n4hI9lBWO8QZKSg0aYKTzGatIWtDhM\nS8UFzxBU3XHShV3d+rofp3zD0AyjRjw892iAu6h4notGEyff7MmnwOwUOOvV\n/fTjGqnL0vgWp5EuAcq5xMpKRKCYHM6YBDrPLAJRMHR4lE5kqZ9uSlENO+1E\nOgDD6IiqRDPQ3zbWN/pnvHcVvxKbmqCi09sFXb65VbOp34s/hIUKBZj2Qrq7\njlxg7l6hYTThP5QlWFJWKstV2NMQyIXbIIIm9uDufRDdq8/PyA/87Eds/xnT\npRa1HULOLSIYUAAEKcrrelmXy1ibDGmcxNzSnnh2nbf+JJqa7BXZLeBSDcHK\nh7nW2B1D2sXesjMsl9LRIeoFqLB0Idzmp0H2CEKUjnvjY3b5PuA+kvgucEn7\nUHm2IQcNUm4W0dWhpXCBDmUy1HJExZckFiMfjuUGgWaeHwKyFoqI9zCJo6zt\nBmj7F4Jljhx0TUAcAZnvTgnUd2QHqk8oT4wx8tJGz39ANH4OI9Sr0g/Nr19H\np2Oh\r\n=VXA6\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.4": {"name": "@npmcli/run-script", "version": "1.7.4", "dependencies": {"node-gyp": "^7.1.0", "infer-owner": "^1.0.4", "@npmcli/node-gyp": "^1.0.0", "@npmcli/promise-spawn": "^1.3.0", "read-package-json-fast": "^1.1.3"}, "devDependencies": {"tap": "^14.10.6", "eslint": "^7.10.0", "minipass": "^3.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1"}, "dist": {"shasum": "b2896035a7d1f591f5142097da9aedfcc3272efb", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-1.7.4.tgz", "fileCount": 14, "integrity": "sha512-GAeOB6P4/aYyvEbROVHXuSGPgy4wwQN/fGfXP993o9btyuCAOaz0T2cr5Y1y4nSeaBPfhk5W/E/AfFKDt6yG7w==", "signatures": [{"sig": "MEYCIQDqqO4w96B0KmRbofsiuLVtbuSG8IAldLmlZf4ob31CdAIhAIrREfBNk2B0oE3Ao7d18OPSOZ1XiFmlPyttZDU7SbcO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15660, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfmaR/CRA9TVsSAnZWagAAwt4QAJWyDTxbkLmtXeceLUjx\nBHYv/mRB+gwrXAgcCPD6m2QUcMwFoScE6A1PwW7rafbP6viRHgR50vGyhyzx\nunxdnGPWdfGLyYtQISbrT6j56F327iOvbYAEVgS76DIJ+UeIt5m5iOE8mhz9\nvLssVPiII0GqO+UhMb9bNG+F4Edtiyb8abk9I+U5Och3kXCGsI9gilitV8m2\n69+N9nbobYwEQxDnq2w0/65M1mic+ONfk9IYhBF8SV7IoXHGNvAKzdWtNlsE\n1eZd/hBb9tDX9xQz2bgtQSqe9sa6Vpto0EZhToBW46pperRQGQGdbEnb84A7\nqCDaBhvLhwGP8gIXd+8V1p6M/G/T05AAh2QQ6sbSGidhDs1ebtPPFai+7g+7\nrICjO9jNBaeZbmnJND/t+Qc144Dr5rJMlunFr0/4h2NwtTC4jGbhE4v2jRk5\nQYaRFDysDUHg0ZdR740rBg7J7odFbNJ8PCnEsMYFUV0d2nsMe39kH1XEIL53\nmPBYPrjJC1qjcohETn7sj8wWVvQ/8l2Ns49HQmcil6cH80m5MwmYL/rAvGTQ\nI/RaZQSAavgkIOWkYpuvfIpQKtEP05Z8F9tTUQaZng2uQO4cPemYqMpZpqhV\nGAW2Nn6wgfjompisLsxQXiZJmdDLegcsGVpMM+29KZ7xlaTlj3iVO6aqr+Bt\n7c5C\r\n=n9iy\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.5": {"name": "@npmcli/run-script", "version": "1.7.5", "dependencies": {"node-gyp": "^7.1.0", "infer-owner": "^1.0.4", "@npmcli/node-gyp": "^1.0.0", "@npmcli/promise-spawn": "^1.3.0", "read-package-json-fast": "^1.1.3"}, "devDependencies": {"tap": "^14.10.6", "eslint": "^7.10.0", "minipass": "^3.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1"}, "dist": {"shasum": "b9b286d88feb059d581ca08329f67a5ae08d9485", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-1.7.5.tgz", "fileCount": 14, "integrity": "sha512-G8taZCc0HExiLadB3Nv0/h1TWjQ9HhkqW/waaH9MNqMX26rPRyNORst1LlNXOO8QnxQF4tNdJfc/Z3TuEtiyww==", "signatures": [{"sig": "MEQCIGoHgko76gxuprr+2ReeiFcHyATb3JU3nLJFbtOWxa2VAiB0biNnPbbSwQbiHg3l7ACDP5J9R5nbo4E/Az++or4Wbg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16320, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfpZvoCRA9TVsSAnZWagAAObgP/1VzKAkfEa4HcBwdk3Qc\nKkFvwxL2dbz124enbahcFec+PFz+PpSFnCO0Fw17Md621TARnCctGOBh7bUP\nk4VkokpmG+LyR+WhZcjOWZD/bo6qnPmF90JcmJHeYE8Pof7Oz3tvO+BMzudW\nhaMwhnUSuFVGtAFtY9Si8iEmVIUtspdcK//5LQlqYhJHRBBQ4/xxpauRzrOU\nL7dz098hS1w+P9dSqUyjZ5cwmFZsul/A1sQrmWh0WzPSYH1dZMdx7yqD34j1\nbjK+mtqjpgUlr+njmzxseY6DumAFp5u/1xPsb2tcsx0WSNmlLHNqCyIrudYg\nvAOD0XUSUAAfVKmN5GHEgFq3VPeZ50vFu1nxXu6RcFDsTgPz4sLzVOH50amD\nFB/eZYexVCNompfEHwPRb99XqLT1GdCR1qRWH2UXrzZSfabaCMEET/AL5I+y\n3pcvYq/TFa5FfqbOoJu5yCT/vIAOMJEx80LZeK7pFwFu1d7mmGB+BQgp1goH\n19jiZYPvB6esN7aH8MeuoibzpMIUOGYbH9B13sdK6Lv1L2cJ1WG0Y7dZZFJw\nbUagrtQfRRJtxSvynqoG58pMTkRr0JLYBWeNc13JNrZr62ETxlz3m0J5QtHr\nG3VXYpVxNe43RBO5iclldIb3HqaLdGnp2m/RsB95COG6Gr6YbO1Rs2DmGi3T\nQ7Mn\r\n=wEve\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.8.0": {"name": "@npmcli/run-script", "version": "1.8.0", "dependencies": {"puka": "^1.0.1", "node-gyp": "^7.1.0", "infer-owner": "^1.0.4", "@npmcli/node-gyp": "^1.0.0", "@npmcli/promise-spawn": "^1.3.0", "read-package-json-fast": "^1.1.3"}, "devDependencies": {"tap": "^14.10.6", "eslint": "^7.10.0", "minipass": "^3.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1"}, "dist": {"shasum": "5cebd6373a4b051e5bf8473eb70c327fa48ebfe5", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-1.8.0.tgz", "fileCount": 14, "integrity": "sha512-ljPLRbQM5byhqacWl9kIjt/yPMee0heaTskaMBFaFvYzOXNJ64h27xV96Sr+LnjJpqR0qJejG36QzJkXILvghQ==", "signatures": [{"sig": "MEYCIQDAx+kWaaC/QxzFTErRVUyMdKA95aivBgyufrvdgN04UwIhAM9LUGhh8OUv0IsyM9gsBhm34WOBpEjWhsN41bVGvC/1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17005, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJftCn5CRA9TVsSAnZWagAAr88P/jVc128ys3QxyF/tqtYJ\neTPlWFkZr8GT2Ze9tJnVWSPUErqc8xwgyjVzzJr88DZG5rqGKLPJE8HnqgLv\njAGzkwtzNpYarAahsDpv628ZjwiRZdULgoJ8f3mngLh87KJIHCr/oEGVOk+Y\nwXhINqIoVfsVyv1KdMj6NMn6YGcgLRaXWlSPB/3tq/h6biOa18vPgcBuTxxA\ne5GVCfAZC2Q1fnuVcdpvqpSQDrpDQVoauebzdSqz41pcYiAGmisn1rcKrK8G\nfpgvcGRmQo5uG9X4GAcBuBmgMUctzmtqgqBls9Es6kkIExvX9AWFxiUCUz2D\nqs/9pu0WrQXX8lMp3bSBBzjE0nFRI8dX6rSaKTiSi6ElEvrtNZufHksduDu6\nUqAYJL9o/r8xpAs1NSv0Nw55mQptJkyFb1aITyTlmGnNVk9i32EvgAgCEa9+\nuWhliQjoOC+rgBXZ2YWUaTWUE3kqEvlphi1PpAuNWq8gc1oCadL3fvibSa8D\nFVuVekvbrqGzNZr2ESRNndzjx7pMM/86hZCyDMx5tNd8TZWkWXSf3/jZ865T\nllYZDCicBlr3VnZvNL3i0va/Ecjq8o5NSFVYMz17whV0nhcPKr/VKkhlV6+y\n8XTn1EdEnRtOClh7S9Dc6cp+MN5BR8QiCVOJBk5h6+OxRhCmrS33T39UdnrA\nUzn/\r\n=rgUf\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.8.1": {"name": "@npmcli/run-script", "version": "1.8.1", "dependencies": {"puka": "^1.0.1", "node-gyp": "^7.1.0", "infer-owner": "^1.0.4", "@npmcli/node-gyp": "^1.0.0", "@npmcli/promise-spawn": "^1.3.0", "read-package-json-fast": "^1.1.3"}, "devDependencies": {"tap": "^14.10.6", "eslint": "^7.10.0", "minipass": "^3.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1"}, "dist": {"shasum": "729c5ac7293f250b654504d263952703af6da39c", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-1.8.1.tgz", "fileCount": 14, "integrity": "sha512-G8c86g9cQHyRINosIcpovzv0BkXQc3urhL1ORf3KTe4TS4UBsg2O4Z2feca/W3pfzdHEJzc83ETBW4aKbb3SaA==", "signatures": [{"sig": "MEUCIQC+x0Br17QIRsaQfQwgu45SJ4jspBqrd5N/1K4GriVslgIgbKMmLkIfbK6abHhzVv4jHmyen7ev7Rw/DgDHSu8zGSQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17118, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfvB1VCRA9TVsSAnZWagAA3rAP/0bsS8Ogntgbwn0ltIwl\nJfhMcS/h/lRQ0QFo0sAeeqZzsdwnn6Lw/xY/hRkiuBjrTiUIOallZogAS9ZE\nJ9jZOFGXuLzdhf6SpGBRjjkgzEVUooWnXuCyfw3YtS8gDc5V1MVYicJeUpkm\nvlRCwfr3LKj6jrVwG6dv7Hwj0SiGuP+F34FjoTsaNdwtr/dTjnwstYkUDiEh\nkBOC022NKDXDAX2emjLjohTL+KRMi0+XKiez9Pd6hs+VetW8u8oYRFeCwlLv\ncT9T8BC75vrSbrty1tLxgpZp7PmYWh1kfJ5r6RGkmYiXrrq0Xly9TkDLtxpl\n3gKOthtoWI0FgYTaVD/vfEmPTiFjBsofPTNRux8HMDaF7PUOfadZIkoj0/KK\npNzHitWi8PEdL3pvNspSYNlStgS1IUqNZTSRRJjviVrXbiQyzpSpv04H60aU\n6pXsR24WwlciM0z3meHhnGK4ciMbitncas/00vgJmojK2MdPsuTawY5tQ7Th\n0w6+7fjtu2eV0Sqlttt2lfKVlcu68riwoKdeDvrzJVF1NOzGcsott3PAzWjF\nQqtKCReJGR0ct0Hdx0tYT7jIniwfp3Ujb8o3i6XAE5mDuEueMKgFSooSbTzC\n83Zq6gUvH82SSADiYIvei+muryBgImIJ58TuVlvvp+agO/a3yhN1AVK8vU1E\nY2kH\r\n=Rg+a\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.8.2": {"name": "@npmcli/run-script", "version": "1.8.2", "dependencies": {"puka": "^1.0.1", "node-gyp": "^7.1.0", "infer-owner": "^1.0.4", "@npmcli/node-gyp": "^1.0.1", "@npmcli/promise-spawn": "^1.3.2", "read-package-json-fast": "^2.0.1"}, "devDependencies": {"tap": "^14.11.0", "eslint": "^7.19.0", "minipass": "^3.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^5.0.0"}, "dist": {"shasum": "528627e24da72a94e3156fb78056bca995a828a0", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-1.8.2.tgz", "fileCount": 14, "integrity": "sha512-iwKq152Q62zG2rz/zRqT/OLDKcF1nBGTGmFdHRkTV8JRte6bUt18vPG4vOr/uoECecrIuJe1SSyvuUF32yt5BA==", "signatures": [{"sig": "MEUCICAHRTqV5Go9Dp5X18Crn6ywQc1ibxOtBVh1S2OCc4NYAiEAytjVvg/Y6Ldq65H4Wkv+sZeDMLqRpr9xQiz/kmbGq+c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17118, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgHWkBCRA9TVsSAnZWagAAJF0P/1xDnowWo2B2EYN8/6s2\nf/02wzGKyk6gIVKcw/Pxj8qmq8C1uudoXVACehfAddrepFfrHGNzEvYS38MA\nAQPW5JzGnW3yWFeheBJTY+zJWcNmxrJhSJAYvNylS/DBnTb4UQPddGM0swRD\ntWlGNyVurh9UiWPt8uhQ7td6dGIdY9gGtprAgdJWql5o2jydKduUH5jQOa6h\n//lc64HEHvmODYk/xD+GucPW39NUO3cAED31O4gXIpD5NiqRG/rLvzhGkAxh\nraH7Rsemqa27eUv+Ae0GgcnkB2ZfrcS4dzqthqP0BZ2fpsufwirMPLl2QPOv\nKXav4wPQft+Njf0BhNPMJnf5VmB8UZbd/WkW5FVEBbfmQZ8lJXYbtKdc6Qt4\nYGZmEhuCflDGG9375KA63hOFzFgB9VSQnTnfgRH04PjDvfbwPWlD5+ShFt6X\nfv0udE9jNDBzFMYBxzYQ2rewFwE0bVWDX7JlzSoKoND5pNjYYD66bZG6IK1q\no6Bu0PpMz9J+CX+AIa/CkhDykVTGMzWtWzfMWtEa15QyQRi2tack1v1k85lG\n57hDzo0D0kGCKw/MrL+i/bQ7zmPGhEJDRdUfnSpg2Vj5tCCRcyoWIQONYmLr\n7PtKqhC3V4kRo+XyS3fOyd8QIVaJP8H1C8n7n839vdmIew1l3l91oKll2o1p\nbgV3\r\n=eIXS\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.8.3": {"name": "@npmcli/run-script", "version": "1.8.3", "dependencies": {"puka": "^1.0.1", "node-gyp": "^7.1.0", "infer-owner": "^1.0.4", "@npmcli/node-gyp": "^1.0.2", "@npmcli/promise-spawn": "^1.3.2", "read-package-json-fast": "^2.0.1"}, "devDependencies": {"tap": "^14.11.0", "eslint": "^7.19.0", "minipass": "^3.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^5.0.0"}, "dist": {"shasum": "07f440ed492400bb1114369bc37315eeaaae2bb3", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-1.8.3.tgz", "fileCount": 14, "integrity": "sha512-ELPGWAVU/xyU+A+H3pEPj0QOvYwLTX71RArXcClFzeiyJ/b/McsZ+d0QxpznvfFtZzxGN/gz/1cvlqICR4/suQ==", "signatures": [{"sig": "MEUCIQCS57k/id9ajuiHV/5xUEgGeM5YnK36ETR5LDRkl/IwHwIgUlu0lXlV0Amo9bdDZ4ZqqGyfVEndqmrd6SOtHh1T+nM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17198, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgJAbMCRA9TVsSAnZWagAAkRkP/R1YRrTqtg1ulWgtagxI\nrDLZ9wP3fIuyfMjFnUmBvKgB6gZtALo6RP9ijp2gA5REfK8IpKk0XhNlvffg\nY9kVx+y6GLLvB1Cnz/fpT6mAq2rLiv+FGRnAMIk5KMtQTjfzlSlcq9K2Dakq\nH/CDqD1OvnGJT3nFpKtLvUUvvAfXnZVIuHQlDkxGdKJjGi4hq3ASMKxtbO/5\nLuFmxoySlUAFok0d/3Kio0vlRtmO9ANNeC1RDyH5oLP1s9ZB9xN3HZna0P5R\n4iysEBK+LlUSz/X8hSVzFYOS2uSkkxLRRAlF1AdpwhRkNTld1+qlWpfNXPtl\nrZ56pjjF+sa+YI08qv7UzD8vnaVLu8/00+HG2NkVbiZIgZrytkoSO4LOyt/z\n7VfUdne/yH3ittilv2719MIHmLPYmFLhgsoHQf5IVOUwQSzvISzB0dMfqXCw\nIapU+r+6E1wq71eCwcyCIZJYCJIkAaUhhTtGsaIfiTh/+l2m8CvbWjBojeI4\n/3xHrCL7orx3vjL/08o0ZjniGjg3Jx06R9P5jpi5hYk51VemCORC8gzadTP+\nyLYWaDewHjwk3xFR/6Ma0U+cT5hMieq1VD9D8cqNz+jYm9naM9ApdPxXOgAx\nlcvph370hYK9esbUPfVr2+J0VIKQ3ewwfXtzECSfpJKDDCr9MNPpLjRIijOp\nlyMa\r\n=Z35V\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.8.4": {"name": "@npmcli/run-script", "version": "1.8.4", "dependencies": {"node-gyp": "^7.1.0", "infer-owner": "^1.0.4", "@npmcli/node-gyp": "^1.0.2", "@npmcli/promise-spawn": "^1.3.2", "read-package-json-fast": "^2.0.1"}, "devDependencies": {"tap": "^14.11.0", "eslint": "^7.19.0", "minipass": "^3.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^5.0.0"}, "dist": {"shasum": "03ced92503a6fe948cbc0975ce39210bc5e824d6", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-1.8.4.tgz", "fileCount": 14, "integrity": "sha512-Yd9HXTtF1JGDXZw0+SOn+mWLYS0e7bHBHVC/2C8yqs4wUrs/k8rwBSinD7rfk+3WG/MFGRZKxjyoD34Pch2E/A==", "signatures": [{"sig": "MEUCIQC4hcwgru7/STc660OlC+BBru9UMFSa3G0RgCihE/hX2gIgfzhDveOvmvWHqomUkwvf4C+6Nwo0hW5ZpatUjHGQFvQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16549, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgR66BCRA9TVsSAnZWagAAwhYP/1+heSq/hIw4s5VOXFK4\n+CHnn+nnw8lMfy8FIGuTPUz967QzQF9sIpk940WoLXOXki/cnL3CrUfcYP9u\nWG6Ok5+8a9au9cDvYuwJR90jit1lee1NVaJ+VRjc9pyC+d4K+hY5BshUwJpl\n4pYJCIkgu8K9jkZlsUYLgd+IvJGdwFbIHb4OuS4fBvxNyhbfqow+hhO3z90v\nwsl1W0SUtoSMh4x/PbbNe0Y4A8u/ppIxi7Yl5sWKkHkfNynx3cpmracP5iVg\nzqp9x5BdGPpkfcfIrLkHktHQxRKd0ula1i8OKoQDlnPuF4FfiNHaPbQZOfLJ\n2XWNfoqAqaN8zjzdg62nAsTxexXofhilP+rW7yEDtlnraNiDp9Z0raGVAvH9\noXdaDsDXYlLXup+oF6+H859FVF2XwBuPtZg6573s4j0sce06cNg2N+t+4Jot\nSzBlHAx6vIVUGVDjclySgg41Xc/5QIanoAPECDqvNKc/iTPoOX8KSoBebSq+\nflx5X5SAhK7VDhmtR47JZ7fY5hWD7/czIGQVOQjkMQUPMHlUldFRKJhLo4kx\nAwQja9busP0yBSd6wM+0joIztm0JDvQT+ZJ2m+mEzynLFqCGmtTp41TUrg7y\nXaM8+QSKNe71T+lE4vskIVY+D43lONw2rKvyCT4Fx/cjJwkEVWxkLyIY4OR/\n/KNv\r\n=F3wL\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.8.5": {"name": "@npmcli/run-script", "version": "1.8.5", "dependencies": {"node-gyp": "^7.1.0", "infer-owner": "^1.0.4", "@npmcli/node-gyp": "^1.0.2", "@npmcli/promise-spawn": "^1.3.2", "read-package-json-fast": "^2.0.1"}, "devDependencies": {"tap": "^15.0.4", "eslint": "^7.19.0", "minipass": "^3.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^5.0.0"}, "dist": {"shasum": "f250a0c5e1a08a792d775a315d0ff42fc3a51e1d", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-1.8.5.tgz", "fileCount": 14, "integrity": "sha512-NQspusBCpTjNwNRFMtz2C5MxoxyzlbuJ4YEhxAKrIonTiirKDtatsZictx9RgamQIx6+QuHMNmPl0wQdoESs9A==", "signatures": [{"sig": "MEUCIEf7D7NGV9Fgc3e6VGLuen1tcWVisqDucELPf2tYgFriAiEA9d5E+6Pu89kVHw0lEUBT25U/p0Tiu1tT5FDxRmrO+yA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16548, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJggXsiCRA9TVsSAnZWagAAQEkP/1Ag+FY790G6i8YPjwXa\n7wYT9YKlS1ba74kF6Dv+wPRiik5I3JwXYmZfkwSaECisyPvTgnzAVjlMX0gB\n1Wsg4e8olYmgFsO8mOrM8jJC31slG+AYZcOGWT52lh73LpsDOjHwIgTmrYWe\nXipI5QVxqVm08G19L0g30N3hYmi8pe5aimcPcFgQsiljUx3fjdXLBT8XvCfp\nN/cjSY4NWTxlc7bQHdRh6P58nz95XYjAlgC9VpiUZ3YkbtWwjIyAgiFfbpIL\n5WSIzNDO8S1ZiLwHbgXMsnnU7Sqv8fPs33Hy9ihB6M9jVCm1UaEMAiUdSUJw\nxuX4WdmLcc0EOTtEZcy/yaR6U4wWBlUMyFxpBXxRrrLa3vdY4FR9WU68auMM\n873gsjFNBCfGfGQ0GgegDpO22NWZ/lLMG18fVx501lDCJRo0clEof/KTwLgK\nKobtrrZzoEu/vK3o7McxJdHgkMJU3TbBTWYTjJB3X45SCJaFr+Bf+4GeQ9U/\nTzyEOUF15xxEwY+Le33YdXfWQQICBcBytD1NoyYuRxqrpjOZME1QjxzImCMo\nL5u+16WcvLEA9o7ytBZUndbrp7hGgEc5IQZEJz1Utg3/BgbOU6kHzVAzhnIl\n17u53PjI1LuXYRPv9yggyLnd6OXFwzLqt7C/HyNsq7FeaQ/zybKreM1gTwOj\nz2Qp\r\n=yTGN\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.8.6": {"name": "@npmcli/run-script", "version": "1.8.6", "dependencies": {"node-gyp": "^7.1.0", "@npmcli/node-gyp": "^1.0.2", "@npmcli/promise-spawn": "^1.3.2", "read-package-json-fast": "^2.0.1"}, "devDependencies": {"tap": "^15.0.4", "eslint": "^7.19.0", "minipass": "^3.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^5.0.0"}, "dist": {"shasum": "18314802a6660b0d4baa4c3afe7f1ad39d8c28b7", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-1.8.6.tgz", "fileCount": 14, "integrity": "sha512-e42bVZnC6VluBZBAFEr3YrdqSspG3bgilyg4nSLBJ7TRGNCzxHa92XAHxQBLYg0BmgwO4b2mf3h/l5EkEWRn3g==", "signatures": [{"sig": "MEQCIGO9fVnS+UpALVPZxoBKsmwAh1vWb6+tPAYpZgYeBFRTAiAY9TN7bRGv8tkYZ2F/ivfEuYFDWTwngWrUOowHydrtBg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16519, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhGtBgCRA9TVsSAnZWagAAxrIQAJ8/BwFPufx4Vz15m7qW\nQOrvW2/IO9ctIKuOnWssXSMZkiaX7nr6sm7a3T8fFoe0Sq1Z40QPZpBdHETk\nvStqmhAWwo73m43lukptce0op74383NUKba+eAgEGnYqr0+s+puVBfNFPm3H\nY5oak4an45Na7rG76GBxh1MZbXApiksGlTZjf9u33nR8ReD4cHLxGruw1iN1\nypHRtRjE1OsnBnYYO5Nsp5izbB8bGJ6Q5gIP02TD9KmpyjWFTOwaatSFQz1S\nw4VNeOmYg5+rI5TlpALruFbTiZZ+NWdaiCUgr1TTuaZolmQw1Kz5rDGQpZrz\nAY9kV+9gDbkQHKnxC0U3aCm/AVYhKPUXFgl/O2fMHkeS6zfNWYlTUNttZj0Y\nXlshBsFUqiNh8Xxh4+OGM88l0BBiZAvJRkare/DZSqTV3j7vW4a6zcHOREaj\nX0W/gponh6H6+TdE1CDzPQWO6SvG8Tyawo4NCPNm7rSAdimgWnJZJS8WsQsS\ntFIibCzfpq5ukst0r6lBKl/LwOaA6yeXFgnjnnby2TR82IYX8A8K2VksSGMw\nzOckwX2wQkDREOvSysGFwNGj1S93IvCsg1AWCNKZjltrWNwyOWuDThagjyVM\n57eUxFSro7W4JFBwcPQ11mWwdyUjzlIXIWmTZdzp9cwUyGIDf5qwcnU3htn4\nHDmJ\r\n=P+id\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0": {"name": "@npmcli/run-script", "version": "2.0.0", "dependencies": {"node-gyp": "^8.2.0", "@npmcli/node-gyp": "^1.0.2", "@npmcli/promise-spawn": "^1.3.2", "read-package-json-fast": "^2.0.1"}, "devDependencies": {"tap": "^15.0.4", "eslint": "^7.19.0", "minipass": "^3.1.1", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^5.0.0"}, "dist": {"shasum": "9949c0cab415b17aaac279646db4f027d6f1e743", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-2.0.0.tgz", "fileCount": 14, "integrity": "sha512-fSan/Pu11xS/TdaTpTB0MRn9guwGU8dye+x56mEVgBEd/QsybBbYcAL0phPXi8SGWFEChkQd6M9qL4y6VOpFig==", "signatures": [{"sig": "MEUCIQDKBqNW1/ytagDaRChh9n14r1j3YhYhqbXSctk6JNeH/QIgbRWoQXXDcvHO1Ymf3VCFwf3DZvaxvfIPn9bZKUR8Hw4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16519}}, "3.0.0": {"name": "@npmcli/run-script", "version": "3.0.0", "dependencies": {"node-gyp": "^8.4.1", "@npmcli/node-gyp": "^1.0.3", "@npmcli/promise-spawn": "^1.3.2", "read-package-json-fast": "^2.0.3"}, "devDependencies": {"tap": "^15.0.4", "minipass": "^3.1.6", "require-inject": "^1.4.4", "@npmcli/template-oss": "^2.7.1"}, "dist": {"shasum": "c37752414fe06cffbc1279b340da8098d3f9f31a", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-3.0.0.tgz", "fileCount": 14, "integrity": "sha512-jIdmUepw+kVP2WEE/+XrBIvrSF5miDutdGBrQ8May6uHVAvpAb0m3NRHcJ0lKWbQ1BxsRFsmTrjkdY99qTTVIw==", "signatures": [{"sig": "MEQCIHi65a9IyCeMWGodru4hGAWZyL9MTT38ccG6mxZwhO3oAiBL7Pkj/Kp8AHcf9xEgYuJTxw99fpZhfbAmGqQvErevsQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16691, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFYEiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpqWg//dLxPXrufcwcWa5GdcxzEhWXXkssw+A7g4Coo1faOha4w2l8V\r\nK82YLcod6OnWX351zIQcir7eEEd1XpQffxGOAOyfDJPpevaBhG6ztgjcCfzR\r\n5dA7Z3+716Jw5HKB78OToww2Gbrf4Pyu+8fZWamLEsJbqxhd0Lxw558MvLg4\r\nGH0wKy1R++MTP0aYEnuLgDDU6MoxT0FvQCv3rO5thUbC/IVj4KKpKxvA+7yY\r\nk+1UyUQ0d0j0KiNrduGjt6G3Gs3DomUHz6CwMRSi2EUbtygdyXTQ2OVL+/hJ\r\nyBAwcG41UrBtvuHNSkqxtmhOHFTyduWhd7vqN5qyXuOHc6unacbTEIGH9GGM\r\norI2Foljf7NDHaTXM4lxM7fii/2sBMdSiMjezFZfq1cNEQ+hP5IYRBvDRE44\r\nTMM+Lp7kijOwmcJT+INhV4COi06I6KwggYzS6Vse9yGWB6NakE+XM3HdpBly\r\nWNcW2G+oOqhAleTEJSCMnIZeZ48HSD56k/s1TMBGua/EfL19rzvWWvb0B6l0\r\nwb+yN4glOP2yKT3dr30HzgUwqmfycOi1M5RFdp5/fVC56rY3BhvDbWi2pgoS\r\nN6MzkV6poWopBn5Gw+7ci3zG4MTCj4cjle/qLhDul5ADOyH4w2v6E4nMbV1X\r\nEYF+Svr1ZAo9/Qsb/3rlRgmT3XEzBCH57Sc=\r\n=Whcz\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "3.0.1": {"name": "@npmcli/run-script", "version": "3.0.1", "dependencies": {"node-gyp": "^9.0.0", "@npmcli/node-gyp": "^1.0.3", "@npmcli/promise-spawn": "^1.3.2", "read-package-json-fast": "^2.0.3"}, "devDependencies": {"tap": "^15.1.6", "minipass": "^3.1.6", "require-inject": "^1.4.4", "@npmcli/template-oss": "^2.9.1"}, "dist": {"shasum": "9d10b46586300074cc9e53ef320130a69567e1ce", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-3.0.1.tgz", "fileCount": 14, "integrity": "sha512-o2fkld5hYwu9sKYzoXTpqEocMnDLaigobaPzLaGB63k/ExmLBTaB+KpfKlpcIePPnuP8RFR+0GDI4KopJCM6Xg==", "signatures": [{"sig": "MEUCICNPaXK/BZgTITjH3iJANNAmZJ0pvApFn9DKTSrcJELMAiEA4K+YoN9YJf/Juj2+lzqt6mNVMq9gguW9hn6qs+XehY8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16691, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiH7uNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpCuQ/8DmFv/a+Oz2mi6kxU1XKUdiYYpS+ahGRFUcuX7yXVTwMl2bvB\r\nM4Q7fP5CGwSvm8Nkit5n+J/oI1fGn2Pv5Fu5zIXv0F985y1WwD3Meko7OQUl\r\nE8fx7YI+an7flpMbdFBJup8ziGS8yhqc78OHazRcfSm1ilEdum+pFMhgCLeI\r\n8UCx4Kkh5BFWW3pnNdxt36QEmhLA0pKJ6jiWGqmwtyQz3M5pfabcPQ/m6/oX\r\n6oIsGhZJaQMbznME2m0yBHUK7iQ4wyNJ9Grc4zEzuBQOEODcwBBBY8Zdwpm5\r\n+FxgbP3FmSwx1FgukkfjtVKqatm+rDCId98sxFCNehKdzhxgHObNXRQBL9t7\r\n7kdUj51as7U6XRjN7A3/F/W2ZGQ1urlOtDuUIcFgWnI+aY+9VBbD79e+Bq88\r\n5bZVO9mY9TYdJjAh7ciSTBNBGshBEHTaL05KSSOiLVYeOB5twY+Kz7RAkSYe\r\nHytVrDrrP1y1mODP1A/lxQL8Omnz2GmsGJunLYrB7t3PYafz+SkqYRk7BbKH\r\nyn6/3dThHaJyZtMprpNEUwGN7pynRTyd/Zxpg7sZnE0vM4lcxHRSef2S9YJE\r\nzW3oHY9GKrX+TxopfWY2TXnfDPrbtI48oKHZkhgdQXUzXH3PCeSSYev0ysFI\r\n6wzoMZzvPU/6QMX0pZlykqk76vW75i92YyQ=\r\n=Im41\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "3.0.2": {"name": "@npmcli/run-script", "version": "3.0.2", "dependencies": {"node-gyp": "^9.0.0", "@npmcli/node-gyp": "^2.0.0", "@npmcli/promise-spawn": "^3.0.0", "read-package-json-fast": "^2.0.3"}, "devDependencies": {"tap": "^16.0.1", "minipass": "^3.1.6", "require-inject": "^1.4.4", "@npmcli/template-oss": "3.2.2", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "3e9116d831f4539bf292d18b015977a6118997ee", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-3.0.2.tgz", "fileCount": 14, "integrity": "sha512-vdjD/PMBl+OX9j9C9irx5sCCIKfp2PWkpPNH9zxvlJAfSZ3Qp5aU412v+O3PFJl3R1PFNwuyChCqHg4ma6ci2Q==", "signatures": [{"sig": "MEUCIH8MdDcszP6kKTdlRsCg3UFTaopg+wUKAXOL0HaU5Yn0AiEAnIe+3MBwvyG5lO3lqmnKf1y/xxP1sIvgmPBfRKRDy5c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16898, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTJ+xACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpEZw//S+oYeb1/aBY6h02XMOOndB+fNEhmH66ZrGl0AGI2SUp749p6\r\ncR3oad2HT7goHjas+hGP26qjWAl9iOC69FzegIfyrwN0YcFf866ICN5jDwyQ\r\nh69xdwdeKxmKAyqHes5SMhc0Nj3TRDIDBFkTTGfxJdnDaCxpCtvVjqDid6XV\r\n1Klzv+L++7rXS/tZ/l0RFj5Uq4LHkzCDI9iqNH1+f9szmLP6TFKwUkdv/7I0\r\ncP6GNxJjz7VlFHPW+tCG2yaB0PQEF+8XrwZlAKPmLPV5xREWhVBIgBrPvQDk\r\ncqxbFyBUj+dteeepfW3fyo/ujtLK2nQmOoP7bEZQzkIXlONWQA52JnPyB+kA\r\nRxNG+r0fpJAh/Z/jVTcon9QwedpGCszBHlZ0PgXDT/5gSlAkFCZrU8vymiks\r\n50zxc01EPqJTy13WKdqNOU6o8UeECN6G6bxbwFP537T5F8Br04z9+f3g3BZP\r\nAjRoRCVwDv9ii19tjUTphfa6f2+3XxHYpKrriY/QE4hJri8kulR2K64CXhDu\r\nPv9LpzCp4eWLlgk2u7Jfwu9fLFZsk5RDDZZj/jF2FHG/fU3FtiGPXUykj6gQ\r\n9O9sT5qY1s2Zc64vefGPUpurD1fPJryTUmb6MijSXuvf5ZjYAZ96ZYlreqhr\r\nx6uv9FX8D1yoklqwOegPURZaZIuEZk+95M8=\r\n=2SsP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "3.0.3": {"name": "@npmcli/run-script", "version": "3.0.3", "dependencies": {"node-gyp": "^8.4.1", "@npmcli/node-gyp": "^2.0.0", "@npmcli/promise-spawn": "^3.0.0", "read-package-json-fast": "^2.0.3"}, "devDependencies": {"tap": "^16.0.1", "minipass": "^3.1.6", "require-inject": "^1.4.4", "@npmcli/template-oss": "3.4.3", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "66afa6e0c4c3484056195f295fa6c1d1a45ddf58", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-3.0.3.tgz", "fileCount": 14, "integrity": "sha512-ZXL6qgC5NjwfZJ2nET+ZSLEz/PJgJ/5CU90C2S66dZY4Jw73DasS4ZCXuy/KHWYP0imjJ4VtA+Gebb5BxxKp9Q==", "signatures": [{"sig": "MEYCIQCq+puGc5ky4N6A99oan84pwkZzjz2dqtBeQR+wqJN/AgIhAJPgwOodGowruzAE2z7vOcVPwcqGD2Qmd7IS0nHpmNDH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16898, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijoJzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrWqg//dbyArzkGmFsC4vGwpmXYoR/Tgy8nKFJZBw7e8v4YkiZkg0Cn\r\nq+IiHfFWHQFBg5LrkKWbnc0fUkiW6mMsq/l3s0S230G0JRjH1fYrDmw+CdCS\r\nITU5RTDgWFeKC8BMFCykq4nQGcErXITs1n3AK/22Ho7bibpvg3FnnkGw6i7J\r\nsc5NRBfzNV6LP7c8d7cc8Wza9ICAin0NGSfMtySlCnTsqAI8mk2WmFC2pQ8K\r\nXEQiOF15EfVy77kQHjUwKtBlsUrXo71fBZzzPpD0qUVWNElhtKDYX/f2xHNY\r\n6DkvOG6F+YXVtE+Ul8Mxig7V0sJl39+I4i7BD/YxnsfuPpHc6ibB9A3lAuhi\r\nzItRbE2zsgtxBsi03szCMvf2NelM36PH3C+jQ1uSRu3n07P8DFx9i14JXHwJ\r\nvm1jP08DOQU+y3+XJSFVdxqU5dPi0SXzSlo9zL1P/waNzPDLqRSThBfhm7Ik\r\nM75Ovpej2rVcyHqsZ+LySCZY/4UD1lAraxLgJbjsiBwsWMpx4aM8OWwXiyHl\r\nG3K8eS8dmZiKoTQMjW0cBT10xE7IglZyCnu0jl5asIQPEwb8J964uHuPB0V1\r\nxZXJFoaLtLGiDxDWv9MAFz2zicgaHIKFC5AxLbWzVn7+X2gHQX/DpV9D2dUM\r\nkY4BtSQ4l8GL0xOCWyeDJGNW78HD0OEajjs=\r\n=RvGv\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "4.0.0": {"name": "@npmcli/run-script", "version": "4.0.0", "dependencies": {"node-gyp": "^9.0.0", "@npmcli/node-gyp": "^2.0.0", "@npmcli/promise-spawn": "^3.0.0", "read-package-json-fast": "^2.0.3"}, "devDependencies": {"tap": "^16.0.1", "minipass": "^3.1.6", "require-inject": "^1.4.4", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "270ae8650fab28606920f16617631c3d51a5a710", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-4.0.0.tgz", "fileCount": 14, "integrity": "sha512-R/jtt8eXV/mAwz50v63/aB4bb2wR6H0LA5vCNcoXCtG9CgyhFqTVAwIjXntfsvzIJrnCFICxkmubjqsvfjFQCg==", "signatures": [{"sig": "MEUCIHX6AdMZFX+y2caOGy1OX8kZrRvtGBQzkQ1XNPTP4tAFAiEAhv5A0rsN+xbkTN4cPcjeR9loaUbNqQdz7E6kvyzKfl4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16898, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJimnFtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmojKA/9EQJ7vJ7O3BNrYW1EVqr3vmxEGS2G7WPBDrRozlm/2TcPmq5n\r\nxr90EcVrDKMEkdj4bJ4oAtIISDx6u2eWP6cxOeAJ9Ix3llOYnvjw80ZqxQf7\r\n3ruvtFSTWbxxh+EbaEg8MFeX9GolO0GOMpwk+2+NQGqfkBnVYZHFGAmPYST4\r\n3IR2XbueKemGIj1HgV0khfKQns0NbsYu2cOHnFYYeXmhjiVikxw8hK8MkSHr\r\neo6hHKQpMDkM5d8Cb3CcJ1p50dEauQzbDMu9N5rkd6H+JdQoDUAbjgAXaL53\r\n9xOET8KsLyw/tzvqRslJnx0wllhA+bAANK0TJXUEYUBD2YaYuy9EPdKhnK3U\r\n5HfsXsEKBXqTNX/WBYU5VztZK26ndB/7xhbCjtYV12kevGXNTJLTFRyGLi4G\r\n9Jtt/A9W+VYMOG8GmoMgVAwTUUZ06uMlsYifKH5htCGiQtuJwkUK7d0UOAUj\r\nj4ZhdLw/yLiSrVenGUWYkooKpQXDX9SpKU1D+9N0y6sjdgHdFgL6yUqN7rhJ\r\nbK1Gj/aUxFQOeUNH/HCRy0BvSe8waJPTB9Bwm9g2NQHEuDrZl4EFvl6BqNKM\r\nzJtIZSlR8eF0P8JeKBUmJZWikfKI9KscasnHH9JyEbXtJqpH8GpF30Ld6BOB\r\nx4ZpMRKNI8C0S3eakCrKxxqkATENCTDCXpM=\r\n=4CEQ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "4.1.0": {"name": "@npmcli/run-script", "version": "4.1.0", "dependencies": {"node-gyp": "^9.0.0", "@npmcli/node-gyp": "^2.0.0", "@npmcli/promise-spawn": "^3.0.0", "read-package-json-fast": "^2.0.3"}, "devDependencies": {"tap": "^16.0.1", "minipass": "^3.1.6", "require-inject": "^1.4.4", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "1ecd270f6a14841721848f0a7dba441676fc45ab", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-4.1.0.tgz", "fileCount": 15, "integrity": "sha512-bVX9/2YhQscdlC5WEDQ8HH7bw32klCiAvOSvUHJcmeUTUuaQ7z42KiwmnkXWqhVKKhbWPBp+5H0kN6WDyfknzw==", "signatures": [{"sig": "MEUCIBzmYVFI1YrkbDaklGhhsK+RDr9103HJyYj5nlau8LdbAiEAoUZs/e6IzytxVFO2HmQe/3b+6pHFXWkLu8Wf/ZfN/3k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19805, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJishCxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoPZA//fS31ILQ1O1t6+bKXDCXIFVfSB/tUG10tXUgkjI0GjMb1mT+I\r\neFa6yOtMjNOL014+Iy2zvJB1l1M4PsnvE0Bxd+lutqLD8uGboD4MpecITWNm\r\nNxGg8TzZJsz3RmzPqfyaBM4uymavSfANvRSeCvtgKv7lcxzkZEnDOVY9N7D1\r\nywklGFO/u5xwtGoQs/PwAWQSr22dHc+wrNnrl/2wk4a1GX2x1di512G48k/X\r\nMu4d0pNs2jiXV6QHZ+w2Yc/WVEGJEjDk0PBxiOAhJBJUC7G7ZarPQjob3fhx\r\nDy5WyFsnmkKoOhJeEr6ofmwhi81EYIOjMG5wswn+TLTrOcrHrXCB2rVSvzQj\r\nm5g5AUNKDTI4BebYQMNxznRSgH+L7d8elRU4NztDRNbUfzwi55a0zDHeM/8q\r\nta6bj82hLO4AzuPfxN6l9/6EohLqVbr3Zpffgy8xX9jV395uxqvQSyYaHP6D\r\nvgN9+2vRPeCP3OXML13ytRXBjGXbukmdamNA/+WS8J9PuoZlAQwSz9G23exr\r\nBD47KWkHd/Jb1AqTXDh6KlqaEwBNKN58/qZ6a3guTKVfFSBnPjMonIzICdHP\r\nPZPCvQ/nuU9GXyePt0at7tB+UUvxy0wBnvaLl9/P22sB1ucArBJcRTsIaRor\r\n9Af4OnEtjaS+Fr4rsM19QgCdfWXYtftX7Nc=\r\n=EThE\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "4.1.1": {"name": "@npmcli/run-script", "version": "4.1.1", "dependencies": {"node-gyp": "^9.0.0", "@npmcli/node-gyp": "^2.0.0", "@npmcli/promise-spawn": "^3.0.0", "read-package-json-fast": "^2.0.3"}, "devDependencies": {"tap": "^16.0.1", "minipass": "^3.1.6", "require-inject": "^1.4.4", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "e49c93a1e94abbecf23fdb19c51c140ad31679c0", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-4.1.1.tgz", "fileCount": 15, "integrity": "sha512-lMypA3pfeXm6+FDxG+pOXLzBeGT62i5WGSAFuwBuE4iy6HskVR/w3bjSWV9fBR6Ky6MPjcJbFCO1iKAb4dJgHA==", "signatures": [{"sig": "MEUCIQCCPM0C/v6cJLWhRCBEmJIo/xOKrEd+eC8+GnIiwcSKqAIgf81T5JHOl/6RXsstrxG6DXnyaP1R5FrBpxLz+Tj0Vk0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJis4ccACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqvkg/9EWSJ6x63j7Tmkbm+Nnthu2AUNFCqUuvSHYmC8tTwSGSOQIDa\r\nZem8Bai88r6ZgDHAr6Qtqy/K5BkBw0aoNfa0tfdcgE9I7z7TuDqev6oR5ar4\r\nzf2nQ5Nq3UVagfJM8Mvh/3PvPGCca7o8Kf1a00Xa+og2fpnIzfK95XRo4BVr\r\n67CRuf0iHkG6zaskEX9P51+oa+fnjQFxHzNYjFR0IovAMP/MHp3m901T1tQ5\r\nvfCutNWgHA31T16ar1LZoyLVJ/3mIMlrxp/MNa0gkAIwS7i/ULufcidvxzSp\r\nQMRd8CKmjqTS7mKHsRM2fKm+4UrOqr15oSV6v7YQQ4V1DFno5jbtwD7fmdkM\r\n8q+yqwkUVp78XuIx/0T6BR2nCdF0LIdw5+O5jGRR6qlaOmrJz67AJ3xnPmep\r\nVQXZW42TLzMbHsOPbGemVWZamywXbUyvxKk1cP9yGYhPnc/hNUkahI4nk0qs\r\nWsbXoqbg3mGJ1fjMcmb0aN3CpuCXYqu7nS9GXVNAi3zWgZWMJFqPuX8g/5aF\r\nJDoXTgaB+uPKAYoTqsJYdvphSwrVduzBxmOFhQ9KkhuqdmlE6Pxfgm9CnvQr\r\nMsrbklnzRdUzbVWJsN7bhW6HsTaTIvDY7ymAT3Rn/1xLVjZHvvgBwNAjmYkZ\r\nlzhh/kQtVrSOxEh2J14Yhj24AmHvw0tL1ng=\r\n=ucq4\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "4.1.2": {"name": "@npmcli/run-script", "version": "4.1.2", "dependencies": {"node-gyp": "^9.0.0", "@npmcli/node-gyp": "^2.0.0", "@npmcli/promise-spawn": "^3.0.0", "read-package-json-fast": "^2.0.3"}, "devDependencies": {"tap": "^16.0.1", "minipass": "^3.1.6", "require-inject": "^1.4.4", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "05847f11016139d22f7185a2003a07870bdee6cc", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-4.1.2.tgz", "fileCount": 15, "integrity": "sha512-fCSOS4L4yKRjX+FoWkGHugihBgUK+EjATJn6gfmZnRCt620OqRB8Mrtu/LMv7m1dMrTFVGdywsDq8wFGpyZKDA==", "signatures": [{"sig": "MEUCIEiFgpxgvc41d5zm6yUH+c70lDXKr1ybLr7q2B1SsLkxAiEApcxHHH8ekMrwTxSUTnfJmpotw6UZo+xKecCtPiQowuY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20961, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJis5lPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqjKw/8CXpKt+qhpFxmX9+MLlXMRihwTqQ0m5qkq/nicSJylSXAV72T\r\nCpqAaNQi2zNVt7leDHFprCUxhnPkNZtVig0W4sDCkVkkluUwN/I41CBmSLZy\r\n4RXwQ4mBzp8R5Zg17EQAQ0jWvG9UOuD25mDemuuuOdqfkE6qgqiqiPYmIa7S\r\naUiD8XmIxXjL10nMxfSz11BkgnKtpAxsLApDbMjnMI/7Fg+yefg272LLCM+j\r\nkAIdA8H/zIZpLhxvcl4lg76/qfdSWCo5N46NnwXeOmFpqmdo+gbvUA8qOIa+\r\nQacXKBUB+e1d0B25kaCfgSVtAOqWydRZCvq9Y0ly8ftACdvjlEwW4rjs4PnS\r\nNigDOPmlNRwCecfXyle9JLiX3RwyT2E/K3cdu/5gLYkOmQHaU3RJeEzKvwRn\r\nyIKo2qPG9ni633CE2acBcNg1mTjUJ0BNl/pKPZcShMTTKgsObHMECYE6n0Sr\r\n2rTJzf+QQumnFGqhIqTEwZOmouymGOxrKeAK91hXhBsDDSPGKprPxiV/hmKM\r\nRa01oEjA+E0yyaFNItscJjTMC5TLuTOI3dVNLwJoC8Ug4Kv47rYCIFzpVAFo\r\nZLmcLFk8JViNV6TCZuleeWRQb0Q6nIl9SdgBS+KRJxfMig5tHTo+MDCvkxsW\r\nS86mQcH97S+N7fFXXDDg5DJbCTj3eY4rcik=\r\n=ehsl\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "4.1.3": {"name": "@npmcli/run-script", "version": "4.1.3", "dependencies": {"node-gyp": "^9.0.0", "@npmcli/node-gyp": "^2.0.0", "@npmcli/promise-spawn": "^3.0.0", "read-package-json-fast": "^2.0.3"}, "devDependencies": {"tap": "^16.0.1", "minipass": "^3.1.6", "require-inject": "^1.4.4", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "b89a3f861d740379892f9b2c44f8bb66dd7b577c", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-4.1.3.tgz", "fileCount": 15, "integrity": "sha512-xb47c2KMkn6ERw2AwPPGKIITbWoXOT1yDV5rU3SYeC1vksYOodbgN0pnOptIVnRgS2e9G8R7BVDVm8lWp92unQ==", "signatures": [{"sig": "MEQCIHQuqlFBWPdCV70FLfST6qhQ6syzqW4SHZNq+IZ+HyyvAiAcNjuVY2w+p44asjSpTOF3mmg1EkDXKhx8P00/zRHmIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20994, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJitL7dACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpHNA//e3NKjJgdD4KX4MaCUGWjTUkqVU+s94yxamG8Q6xfewnoQx3c\r\nI74oVPyS0lLWIePvTB2c1tpE4H/zNi6nSCLBjWGG0DbfrodJwKiZlg+3sYFm\r\n7oyNrsNN5P4xA+oZ2bMf1khbBefqej7cPtZjZWuf7i7slcl6dffDz0rE+h44\r\nsb1bRq9gWL+ZzoKPJT17QvxiX36EM5jp+JerJUcQvAqyalLLXZUBdWLN0val\r\noo5GC2gxJqRWLR5PJ4eFUy0aEl3IyZOhs+qlwoYy/KVAATaCKsG4Q35kjepr\r\nz/XZ1ho//YCTnmfFRlBbvbBmaq4/ZRKwiA+UnB/NOGtsX6u6GvADk1xVqwoF\r\neFamcAJyvh0pSUfWtrD81ybG5GgTJQU5dQs7UgEsUtjilnDA5t88NtOfpHz7\r\n65jznLhrtWXpxutIi0JK6JGpfidMKl/EgkKMygMG5a2nCrXdVp4j6eJaGEV2\r\nAoj9Yn62hn9npIzPpaMIoGBbcs+3MAmcu/NE4lL0Q0s9OM5IWayriADCokx/\r\n6Dd1BD8N8glp64Dsf/IzreXYl/SL9U8AUMTZgbkoHoQ7CPrpHpkcn90dYhHA\r\nlCl/CK9afNV4ioe8QUjoX6TEu8i1XndSr9qW2ccl4fuL1UJWAz/E9Zt2L69M\r\nB197+RfmAXTMDss7309GUD7IVv9INyRzEYA=\r\n=O4Ga\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "4.1.4": {"name": "@npmcli/run-script", "version": "4.1.4", "dependencies": {"node-gyp": "^9.0.0", "@npmcli/node-gyp": "^2.0.0", "@npmcli/promise-spawn": "^3.0.0", "read-package-json-fast": "^2.0.3"}, "devDependencies": {"tap": "^16.0.1", "minipass": "^3.1.6", "require-inject": "^1.4.4", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "4a48774d35a2552171fc5ccd48fd9264f4683ac6", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-4.1.4.tgz", "fileCount": 15, "integrity": "sha512-1Qk/EsHBKc40XkN1dF79ztae+ua9jEjDupU0rQgO/k+94t7eFjXGN/baRvA00aEOJuTZ4VjwlC2u+XECImJi5w==", "signatures": [{"sig": "MEQCICitdjUaNLGUuHDn2RdSr2mkqbifEzi0C7a1gtE+zqF4AiBX3RpR5LmGYiIgjLbzQbQQ6s3Z66wFXhUQ61FWyhE5RA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21270, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiueTsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrf3A/9HrA3lULOK9FzqoV5C40NSh84exn4f8rtL+DszzeprB4LQl55\r\nKEF2YXAnD6TP/PazzGLoTiJoI+bSCFlScaq3eRU7HfSN40YDjJxRRNE0JZ8V\r\nQPYxbTWvlf8h9WlLAoNe+W876yXpvSNMZexFMJaHxuyfiFKc4TUY0RDzYJt3\r\ngTpa51+rk6Y5N5OS06RNvmWspszTozUrof1sUGtdB8D4uu5wO0fi2hIr0VEi\r\np9avJ7i44zwEIMIwyE/bdUA1Kkb1VYYO6QWf1Buv95lCwizft7iHQSPUj+3i\r\nESHii49P8YY31Dseo8HhltDfakeWZMDGsB8mVBhieoeKBmyuP09zNpYAnZiW\r\ntYUbR2es6mry51Xgd/Lb3x3POJPOoiFLd4H+1jvKgOZj5YMqJ6XRCcmx0zZP\r\nIDHVEhKGCqP7KJ07V/zYPFT8cKGU+kONvoePmWuabfSG2lm5GXlqqXfSmP5c\r\n2/WImIciKY9Cm5MXPFBXX96RRXZzLBanx+ZAX/CyMuQAulTVqDsmq0OM+7w4\r\nFqp1+HyhyOkc5S5mP1HU9svdABoFA2RgPoQjvYAnVZiKqwIXPYQxGXCrVX66\r\nuCZSPD9I4G4rrRUsJ5YWfa4mDh2z2F/cpQekrUUP58E//b+rvjjdD3ZdpRYI\r\n0wzBRqj+dlMOL/MLcLvgfkQ5UzEnM1Bkn5s=\r\n=MbMd\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "4.1.5": {"name": "@npmcli/run-script", "version": "4.1.5", "dependencies": {"which": "^2.0.2", "node-gyp": "^9.0.0", "@npmcli/node-gyp": "^2.0.0", "@npmcli/promise-spawn": "^3.0.0", "read-package-json-fast": "^2.0.3"}, "devDependencies": {"tap": "^16.0.1", "minipass": "^3.1.6", "require-inject": "^1.4.4", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "d60a7d41321612a9c0e1433797c10c19d0213d55", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-4.1.5.tgz", "fileCount": 15, "integrity": "sha512-FyrZkZ+O0bCnQqm+mRb6sKbEJgyJudInwFN84gCcMUcxrWkR15Ags1uOHwnxHYdpj3T5eqrCZNW/Ys20MGTQ6Q==", "signatures": [{"sig": "MEQCICjR03PQDXmViKM5H4gOA1qZqZFn+nHkBu6lQzwR5FklAiBo9Qb8b2Yari/Q/wMfIYrIUyX46rdHGjfvxtxf03FKVw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21293, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiuyM3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp6zg//dal2H3meR2TlpP9vtaTuSd4BDSn4kuXZLFR8HmNdGvnA0mZ9\r\nNj+qoXF9ay6/QD7bUhCH5jLHoi1HKHNSWVcEFxFJU3RGhrwrxJgjzEIqqt+z\r\nxWeiGJx+F77Wlt2FZ2fOK2BVKdz0BWTlZgKTvZZp/eXzYIfDF4oNItWWjl20\r\n8jR3NUKRdHtV7a8QrtUWL9l/bLlBUoeOnvHl+dEwKPRyiC/t6LYIumhCOi/z\r\nZUb6RsRMm0kHKrbqr2WMOsbnvRSGG4OgC9XIMJx3+LWoxC2+XWwsq0XGVq2k\r\ndN8Lsqe8ijdJrbWBke+jE8YAP0jAoamoqGlWhUyriBo7C01BNy4HvG4WstXY\r\n5VGQRJY1PQ3QcL2s7bxeVeXnGWXyFVcGrLNx9/1vAL5whMfhDoHNIoWDkwR0\r\nGjY6LJkEWc4APW7et6647D4QzPXtfuR65QYHNkU71zmcV/BVW7TIfQVoAEGw\r\nbl0bDZC+QlL1k6SS07VTrKfgTFRycQp7ohhGu6GDQWl4iRq1Yl89c5M+WRQf\r\ndtLYkjrhwE/AJytfLZoWYF1jRMOwIURW09KDOiE2pP8HngGKr3RuIMXHRDNu\r\nAJHDiE696X5cAhgwidtMRRXMnmkDpW0yuMHW6KuFed3Raoz8AB9+w53VirsU\r\nOA0MHYbBDeMfTgnHJwnVZMlmuqV7EW78QFE=\r\n=Nh0o\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "4.1.6": {"name": "@npmcli/run-script", "version": "4.1.6", "dependencies": {"which": "^2.0.2", "node-gyp": "^9.0.0", "@npmcli/node-gyp": "^2.0.0", "@npmcli/promise-spawn": "^3.0.0", "read-package-json-fast": "^2.0.3"}, "devDependencies": {"tap": "^16.0.1", "minipass": "^3.1.6", "require-inject": "^1.4.4", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "260496b24d3c4c2ff0cddc8d6d23b275acfc8490", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-4.1.6.tgz", "fileCount": 15, "integrity": "sha512-n2AnN2RDyfJN82MVoqoCO3O0QUXf0xrBI8pakWfSE3Tk+0d+xQaN7eTDEaUpbL1GC9jUnnkA0SPGtKSU33Ydbw==", "signatures": [{"sig": "MEQCIDyl3w65uiGTu2hOsTpGOIMJ6LK2TaI9GAClKcg/ck5jAiBgqusjFm8o1PZCYmipdXlTuEnophvbEtu4xCS1WYFseQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21355, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJizYmLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoYTxAAmRyAgWvht3mjI6GcjBnDexvBoQK6ZN9H9gsBODewN2Yjn3bF\r\nfcEav6Vn1yaZNYfqtnWooj2jU1audTSF8ylIXASkY2TfRlzpVOAnv1RZuooH\r\ni9SeIPpv2+BnUEmMT6jsPYq9Mz7byCmMmFFFrABh7aHK7m++KP6Y2cyJS7Ut\r\nYbroThSZcAQqZTZQBA47L8yBjTYVJw5hxdXq+mwiZ8mN2l927YYu7WbkCgaJ\r\n8KURMw8uxDhgkkTPYh3YFj0m8Nkwy0W0zFoqzWs4JMTi8RSECh34BXXIYB5Q\r\nFlW2S18aF253TjVQiQlBQyPEsSpIa24M9iCt8Dc2XTUYKsc2IhtW9Qh8HPUQ\r\nfbzg67sRE2JeKbcaKDRnyZlyjRZ2yYl3B+vZLAraZcKOeKp5uMkejm3E80ng\r\nvukoofpDxMYYyZrCbVOYRfa2Rizs7vtehD8rAnisw6hiTbHBXUqRWqKDLu15\r\nvbOPFy3N6NukIZGxTm3gW00pil2lcrlElyjAKax5F5oWkpOpErQzJC6LwG56\r\nv7xlmJq3B4NVcf8UsJj7J7n4seYceZh4jWEirpIvjkcO+cqoW0retpPheIDb\r\nzJ1pxLNFL5E33m/1DfU7y8Rq/23mUY1gxcV/fWNhcK6oyt4YWIn1HJesOqKP\r\n56FJBekGoP7oOuJqIdtpWriQSrWjKjXWxbU=\r\n=wQFl\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "4.1.7": {"name": "@npmcli/run-script", "version": "4.1.7", "dependencies": {"which": "^2.0.2", "node-gyp": "^9.0.0", "@npmcli/node-gyp": "^2.0.0", "@npmcli/promise-spawn": "^3.0.0", "read-package-json-fast": "^2.0.3"}, "devDependencies": {"tap": "^16.0.1", "minipass": "^3.1.6", "require-inject": "^1.4.4", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "b1a2f57568eb738e45e9ea3123fb054b400a86f7", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-4.1.7.tgz", "fileCount": 15, "integrity": "sha512-WXr/MyM4tpKA4BotB81NccGAv8B48lNH0gRoILucbcAhTQXLCoi6HflMV3KdXubIqvP9SuLsFn68Z7r4jl+ppw==", "signatures": [{"sig": "MEQCIGYP5OAWqiKPRm5hfO35kwIngbqI6EkauJtoQAIlJttkAiB8glO0XkDFJuSPBjdgS+ApHS8btctR/hZtUkmtoVPN1w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21286, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiza5XACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrq6Q/+JwEwexZkGnHz+eYwB4zF/A+ZkRnptRWGiz/M9y75WFk9G1Ud\r\nfpH+CBtx7N4yyK7S5V5yxjFSRodDllNjlJ7jWsWKq/Rc4eIPwnrVwLt9/3lR\r\nC1O0gIv3SF4k9NqAslaI6AITnUfUDPOinm0sWPEnbMUszx3J3QNkwZKJjNsE\r\nmRpu6V0ZkLDI3ohAb5nk68Q1MHLxpCL+Igz1JpifjEoglA1cb8fzpg3gooVU\r\nj+bqxEj/eWREo/RqAm++FVBCA8ROusOhBHjirJKygTvnB7oRGqqHkb4porad\r\nQV1jzUuwOJCO2bTWw8IWpMwStvJhS5PijEDzww28hr1ctWRdMrEgCreTZ9AX\r\nmHWQtUWHRXly175LJhn0zdC9It9mnJXRc1IjqTAWQ0wmFnjer13HPArK5+/G\r\n1rTH2HMIK6bEbGrCTn3awf5DIdfTFp+rf6OE2SgNNkBYcqicz0uvemqKx0wb\r\nJ6uqOYVwl6aofDzMKIK/pVOmwI/4IxqjUHS9HYMOwlQDUJij8zrHGbCYs2U4\r\nEoLeUbwyaVkMgHxO3iO0Xcy17MuHHDgDQj5Q2r/I7hk6d1UWbskO3/+ipG6+\r\n4U52CziobTn8bZMrO4ITC4ubngdLhyiGsQhSuyQaytjux3KdJBUbhIbM0ekG\r\n6mHNHAmHQX8UMqiXq8FfKhjNEVeJUWaJlns=\r\n=Uhag\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "4.2.0": {"name": "@npmcli/run-script", "version": "4.2.0", "dependencies": {"which": "^2.0.2", "node-gyp": "^9.0.0", "@npmcli/node-gyp": "^2.0.0", "@npmcli/promise-spawn": "^3.0.0", "read-package-json-fast": "^2.0.3"}, "devDependencies": {"tap": "^16.0.1", "minipass": "^3.1.6", "require-inject": "^1.4.4", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "2c25758f80831ba138afe25225d456e89acedac3", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-4.2.0.tgz", "fileCount": 15, "integrity": "sha512-e/QgLg7j2wSJp1/7JRl0GC8c7PMX+uYlA/1Tb+IDOLdSM4T7K1VQ9mm9IGU3WRtY5vEIObpqCLb3aCNCug18DA==", "signatures": [{"sig": "MEYCIQD5GFkAcTlfqDyYP/X52UgR/s6udHGqGJ190cbH/rBLtAIhAI07wJ7K/aqt6xtE69gyUpSWJqsQAb9JAKInc3nersSO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21735, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi6Ak2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmql6RAAlFaZQ9SA4K1LvZLB7JBIWkqKNcxWrQEdc3nU+HkdziQN0opL\r\nAPFVJymo2GiKGzOJzRBn+ZRYFuYni5tU5AejQphNwxAB2Q64Qhyx2hx+udfL\r\nAem7PK/qwS0gJbtQfgy8DLAz1rMeqHY6kt7zmcR9OnbDlyTFmOKKi4hWpqfg\r\nDat04805T1wwn4JfZXUwaAS3xQL0H6pRJw9vAwdCFe++36WB6BYMQg/lZlOO\r\nYjWaBXNd0nP7EzLQ5lzckEsy9e/caWJmWx80AqI1PUpkBGwRck1d7yXEnqyz\r\ntRupiMXGpLVrxvc5LB0k5GVab8E+lIh25eqzr1z+COJQGijJFHVFTS/Grdfn\r\nWNQZNaNAlBwojQ6UOZiLiZSpcqhfteCiV/uTx3WjNkrhTxeR+Pm4EiAGB6OT\r\nRqfCO7MwdvzZH7f0G/wGndJ+vTOos5Xw/jDfAfmXWDcRsSEIh0CaykXRK+fM\r\njXFAILIEn1OibfXAXdMrzN0L0LyypNhPAS/kryYQNs2fFafE6yjMApzVsoz4\r\nAv7YK96+PAo3kIxfC4tQJSYHzwwYH6+1/ndGTXvPMrsRLlynaEnZ3ehXg/aj\r\niymiijrQYNESQJvSUxdy5byeHXR9KEqSyi9KO1EOB4XTVB/ZiylnDE/ww3un\r\nLzUI4sA418CPmx2uaP+A02sGnxkM+UbDxCE=\r\n=pi8K\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "4.2.1": {"name": "@npmcli/run-script", "version": "4.2.1", "dependencies": {"which": "^2.0.2", "node-gyp": "^9.0.0", "@npmcli/node-gyp": "^2.0.0", "@npmcli/promise-spawn": "^3.0.0", "read-package-json-fast": "^2.0.3"}, "devDependencies": {"tap": "^16.0.1", "minipass": "^3.1.6", "require-inject": "^1.4.4", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "c07c5c71bc1c70a5f2a06b0d4da976641609b946", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-4.2.1.tgz", "fileCount": 15, "integrity": "sha512-7dqywvVudPSrRCW5nTHpHgeWnbBtz8cFkOuKrecm6ih+oO9ciydhWt6OF7HlqupRRmB8Q/gECVdB9LMfToJbRg==", "signatures": [{"sig": "MEUCIQCkprER5wmpNJ6WzXw/p5E3H4yJ613PxpD22aOgYtXEVgIgfexjijSD+c+hGlU+3Tcd4X+LplugG04ByhXSBUqhgUk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20578, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi886eACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmry8A//Rb5o8cGtYx8MUo4IMQk40rca2bW88gxY0r+O5VMZ+CyTaY/U\r\nexNlmh1FOL1MHhAePmxzbcwqcdoC4a0VluzonQjeLBiy8Rbxquu4AN4Lq2Qd\r\nMWh2HKmJbgdqXgaxBPo/XcdKn09rmlnrebE11e6kmeImIq2N520Ww13KF6Zz\r\nfbgy9Dt38yB41NsWmsNg9Wan5ESAA+tn92a3odR8bn2VOHjhIItpLIzVtC76\r\n02SleBMNEFYFB5xAjVHpBnsHFrbSnGXjNyILNZKsAGsyQWHlZ+lqFYbJt3aj\r\naT+X6Z1pSayQQ7k2wv3HPhNmP0qoqkNbA3USfYfX4L2gvlEy6DI8en6U3vhp\r\nrbBsL8L212z/yJlbo11+djDIzhBLQ5JtYOJR0ZnH89v5c1HXiG3sdVXX3QQT\r\nsRXPELNaEqSIfmSgiRQltdyLunODoIgHKG37FXkPHNl4JfWHSYfW5H3O9ept\r\nYHYylkIYKoATYR1N3m91r32PZxBS8jmUgX96uj2A60Y1+7zhZwvOAupa6Q69\r\nmWjiLCRsjBoNIaOxTb7pujNHuaRhHq4EES5lZl4k31u/5dtjPGuI5qkWKgyj\r\n3fdzIghUQPkeafiygHNmgSOFvLHgm2axKEE0H4Nk5N+8FUEX9krRarGnGzCn\r\nMVY3hDqT4Vj4WiRSuOloPDHoHGmRQe997K0=\r\n=LgTJ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "5.0.0": {"name": "@npmcli/run-script", "version": "5.0.0", "dependencies": {"which": "^2.0.2", "node-gyp": "^9.0.0", "@npmcli/node-gyp": "^3.0.0", "@npmcli/promise-spawn": "^4.0.0", "read-package-json-fast": "^3.0.0"}, "devDependencies": {"tap": "^16.0.1", "minipass": "^3.1.6", "require-inject": "^1.4.4", "@npmcli/template-oss": "4.5.1", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "39d10916b1f4a1e3ebc871e805bc5cdf05639aa4", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-5.0.0.tgz", "fileCount": 15, "integrity": "sha512-AuqcoTOt7v4D2JqC7mCa+eUQHb64vWbwyu9o1hoJiq2kfZNwCB71f0s7tvUw+v+1B5XMdqcr98aGzjKIg7s6Vw==", "signatures": [{"sig": "MEYCIQCPeJZhfZPb95qQ/VLFFxqSbfBjt6sdF5KI3xPiUncNkQIhANDtLnJuHhLCV603k1NAwEscXexPoYJUqi72XDh8WWOc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20542, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSaHdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp/7w/7BV/CYYScZY14yFGiLLpxRg2d3vaVkZTgwhuxXOHEaCz81TaY\r\nK7Cuy+7K9DWLRb3/4anPMfhcFFfkq7I8ass7CDGghIT1KwEscy15yQYlCTT5\r\n5pqlwySxCDn4W6nGASUiBUqnQr8NqRazkxhy9kcS4dyxc17T0mvMQ4X5Lxvo\r\n8/C6B9WEJu81aNiZFF0J5da+L/ktPTvObtkLmbMYcPVlEQCEM/TsuO83JAGQ\r\n9kEXWLhfzvDvUNUVbgEztapOmdS0eNWZRdDmWnWMPE8bgQxhaXjXQ9eXCh4u\r\nCzXQO969yKfiNHxxqLMRrcpkUB35bsicUNmUO+VDh39Mf0PWgi6Kj2gSnQQa\r\n4LwYEqILHCOUW7LEWqLfiX+9HhMF0rWP0FvVac1l/PAgzkYW4XrGplBwnjqk\r\nQlz4+Vu7Ad9MwPx2+Nz7BZMA+MZzHEQ/zQxDR0pt7q32gOlYFddptgtXnoYO\r\nTAafzeXSgdn+PVQ2bTFkeEJyRNKd5G4H6wmBsrx8GsemaxnE9m0LH+XFwpuQ\r\nvBdDGuTKydpSoqkYB/kTTacWm5LuE5c053s/uLkyaZb3N6otRfBlGBKOpPGr\r\ntLIZAid+7ccxb6Pj4fF0IFF4ACbr7yTr8DAhnRETQtJi9K7vb4ISSyNOI7qw\r\ndrIFWdeFNOKN6YfGy78hRj1RclGabdchE3E=\r\n=3R5E\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "5.0.1": {"name": "@npmcli/run-script", "version": "5.0.1", "dependencies": {"which": "^2.0.2", "node-gyp": "^9.0.0", "@npmcli/node-gyp": "^3.0.0", "@npmcli/promise-spawn": "^5.0.0", "read-package-json-fast": "^3.0.0"}, "devDependencies": {"tap": "^16.0.1", "minipass": "^3.1.6", "require-inject": "^1.4.4", "@npmcli/template-oss": "4.7.1", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "a6ae139d6486f14588b5ca612d137517a91dac58", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-5.0.1.tgz", "fileCount": 15, "integrity": "sha512-co/A+5LzdHQVLukfYT71x5e8+6HtIk2W9Jk5c8mE8LaGrkHazzRuZeyUmb6Ciaak7MRXRVOSoTaZ0zYEFVA/Gw==", "signatures": [{"sig": "MEQCIG9vkpllFgx9hQIEOveMSErEDiP3SyaBjErxZnCWmJQoAiBw0OcFnz2cMpN1PJGof2kXwtIBAOE51r8BK5MH81KggQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20542, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjWY5vACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpqvRAAnlBpebX9EtN0kp5/pDHDI6WAGkQHmA0P5n4dfMlyvcRdzbXq\r\nK1QVMxoUOjgshbFajXhfdpgO6+1VuwNJDZtgmY8ONalZt4y55s0usCTbkh2H\r\nae74CkUPFBN5NWZA3Q9kxLW6ltrjDy0J2QbVdqGARHRafeIHlba8BjBa5iQG\r\nEudlqfMsIRTHTw2BZwQ+EiKxlBXaLW32y3NpcKony9HWMQ5WXb8l4zjii0Id\r\nL3P+DeJffMnMhUrQVZ3uESZWgKVqgTMPi2MhN+6SZVRpdVoY3GIFOzLTGOBR\r\nKHVpqLt7Z50kvgM/ARwzvatT5yEAjB10fyphi4JJKw4GhN/4AERoYk80bEm9\r\n/xMDP7DTKiVUJr1Z6wAmExug7TTzpdzfDeW6hDtL1elWyHpXco8WEL3j+Q3v\r\nI3aDRiG5rR7dpXu5NP/M8d2V/PJejVT5/Wt59SRiG8X2s2Ckc2LI6+QJIdW/\r\nRJMnfnlpD/+I/P2gz8/ZcF6IgN6ZAO/6Ft+2kDyvdMQ+xJ/Q4B8q0uAWGqu1\r\nxqXq+vxPjIIAhdWq/9U/WJlVaiTo1GlklBqXxVetjBCukR/I4Wv1VN24/uQ3\r\nm/D/G4l0UfFz8SyUCussTaXn+E44byuJXMn2lds3aAaj3ayc47bGJnWdXR5q\r\no1HGL4N07e3PTI7QmWW7JQ1EgYDL3YQxBL8=\r\n=Bi6z\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "5.1.0": {"name": "@npmcli/run-script", "version": "5.1.0", "dependencies": {"which": "^2.0.2", "node-gyp": "^9.0.0", "@npmcli/node-gyp": "^3.0.0", "@npmcli/promise-spawn": "^6.0.0", "read-package-json-fast": "^3.0.0"}, "devDependencies": {"tap": "^16.0.1", "minipass": "^3.1.6", "require-inject": "^1.4.4", "@npmcli/template-oss": "4.8.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "128b83ae3ea75635c7374c182744a72955752dd1", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-5.1.0.tgz", "fileCount": 14, "integrity": "sha512-UbnNE6kr4i13iXeXYGYMe5Pn8XOhfp+q50ADdxVfLNBo+QknXs086CNP2lZ6CSIzmgzIsZMv0cvNfOcpvdopcw==", "signatures": [{"sig": "MEUCIHM90yhiTPu3WlGoskdBuU3+ZuBXh3k/BhEg1ao3W5KFAiEA0ugvDWuABwtNQ5F6iruZyBZyBv/PzMrhDy52QCOyzO8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17824, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjYXrZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp7LQ//ZqNzVi36IqQ3ce98hD3TXBV5SrWEvEDe2oPW6SgDt789PwN3\r\nUE6aTrv2JL7j0wmc25tibgqiJGfBmY1yT1B6ZX+bdL77TTvvjdAuELfeOlhS\r\n3b7JT0GBIJNiFp/h8/1tBWingGBL+vsSz7D8xxsPHaK6y1zQbfb7VgaRbp2V\r\nSq3NQk5Yzl0LVCy1XqOCmDpVNeoPaPm0NnUNoO5dx4BOG0+bQyGqn6hR9KJB\r\nD5y7wF6a8JTTlx8SUviJh3Aftl1S3T88CYiuvaQNLGgpQYfTkeEOG8RO/TvG\r\nRto0CQu4mL+7FDQiWdDXy4wVGSc5jJ8pdCw4EHI4TGolTC2r65l800gQx0r4\r\nmA5h4VktxvvcqZWr++BEqZgw8rOKINA4+4njX3AYPn7Tm5MAxXU4zAe6D18W\r\n6aMvndMu17q38sgPKfaY5QH1/zDjQUz2dhukmoABMdGqXdvnkXXMkgRfJOPA\r\n9QidttKiz5rqAX5aWY91O9ARV/DqZCgDxbZwsaEXTX9nN5H/wmA5rJXYxxh5\r\n564ktRxJrz3W2pMnChXKP0hoea3vg87IsAaV3IS8GBeQdOm0hWpyLMBMO0RI\r\nxo3qieYQ0FhKa4JP7sGN5uGAb8zc/ZpwvRfG2IlpYsPJ9Nze0fpi0X2czrFC\r\n9ETL0S6Ono06DGCbVBNCQ7ne3XebK3NQLSM=\r\n=rEpW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "5.1.1": {"name": "@npmcli/run-script", "version": "5.1.1", "dependencies": {"which": "^3.0.0", "node-gyp": "^9.0.0", "@npmcli/node-gyp": "^3.0.0", "@npmcli/promise-spawn": "^6.0.0", "read-package-json-fast": "^3.0.0"}, "devDependencies": {"tap": "^16.0.1", "minipass": "^3.1.6", "require-inject": "^1.4.4", "@npmcli/template-oss": "4.8.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "1f1da6f03c140a4b215a7b7d9d7ec5f4d80bbbc5", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-5.1.1.tgz", "fileCount": 14, "integrity": "sha512-mqj/KPVhahTNvmzk5+YsKuqYnPPh1kOJ4/rA2FR5VhPgfJtCbPuBZrlkYUI0A+z5DKzpE5TITCdlgPllpKcd/Q==", "signatures": [{"sig": "MEQCIDZFjq21TEsfa24qLx3lYHsU8cw3B/q11AlXDmIdITyAAiB1Jgk42RYf+5ydVHjSYXaRKArU04xyacMQh0H6qJCr9Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17824, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjYYQ6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqtGg//XOsh3vwknH/xA98JeNwA+A7jZrC1ZOnCqtv+CikbXC807vbX\r\nnwFrIfKKD+9f9w7nAg25Vd0MevM3r9rMRYI2WGD3yQIAN/OoJty9vB9HXp5F\r\n6lzUecfpKHwzf7FLEC3ARsjtW4h5WLt7ZQtjNu/0/bx+8DBJhK59vak2bYHp\r\nzgq+fzr9LIox5YaofgWYVaeOnmMqcClg88btIZZ+rVN5Z9dxGLwaMC/qScj3\r\nn+8C7J0cjN0hf2HlzPQ04M6XSAbp55l3VUcgGchgVcn9L40VyRqsay41kQue\r\nh1ZLzlkrtS2oPOnzn3bcIqZDCSWgo7Odd6b4L8rEvS1+OVRrzlgE6GGVNx4r\r\nTNisxtvbYL3mWJ+5SA/VjAyHz3PHoiGMN3j1TjW+N3ZDGOLP4gHuKY9a3B0B\r\nysM7+9OSg2ZQTyeERSvMqgAY3CDUmHk3YK8oYSQw5LWqFZhxMsvvge2baaxQ\r\n9IJeZ3WIVmM4Wnt3aPACl2DguU8/X9A33LlTf/AUC/YLs7Gk3EHwZBRMf0LZ\r\nllY6d9+8575e2fsV3Jvmn/kBb3aBBwjh6i4Iv5+62CSJSOA4uCwgVHsG3Yxv\r\nBsuBZJOrx7PN9/DOaaNGhmH/poR04gPLvRWMdx97vEnn3W4TAMOA+1QTRolA\r\nBsjhcTS/pgrdyWFFD5yyWWiaP/InoAT6hRM=\r\n=5My9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.0.0": {"name": "@npmcli/run-script", "version": "6.0.0", "dependencies": {"which": "^3.0.0", "node-gyp": "^9.0.0", "@npmcli/node-gyp": "^3.0.0", "@npmcli/promise-spawn": "^6.0.0", "read-package-json-fast": "^3.0.0"}, "devDependencies": {"tap": "^16.0.1", "minipass": "^3.1.6", "require-inject": "^1.4.4", "@npmcli/template-oss": "4.8.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "f89e322c729e26ae29db6cc8cc76559074aac208", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-6.0.0.tgz", "fileCount": 14, "integrity": "sha512-ql+AbRur1TeOdl1FY+RAwGW9fcr4ZwiVKabdvm93mujGREVuVLbdkXRJDrkTXSdCjaxYydr1wlA2v67jxWG5BQ==", "signatures": [{"sig": "MEUCIDGMnsAcjo5AFBZaShlPvwSUP/mg4G8h3tLOP5GevyvxAiEA+RCOFMTC4i1Z28z8Hz4jKYvJj8elUUBG1fNfm227wGs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjYbjfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrnhw/+OQTKH/c085nwezCQeub8ZXyJsCA5cGDM31aGHHhjpIJgQTA+\r\neMa3g+QbCmVgWdYCN9qApSX1jece7kHpn7h68xRVb2l3JLjjHoVObGCPhafm\r\nRn8l22yAzr3PJyKpCITikEGpCAHB61o2OoxxmrLYmXttjDAF5bGqOMztSzWe\r\nP/0YEriGFu6PwUk612BRk3aaMsATaH3056yeNjRUWQbfXnMU6Aoqztzor/j/\r\nKdZ6+kXgdTed7u/JXHLkDb3rojGORR9SzUq0A8xMzz5P1w5DA84/+afDD8Pc\r\nA95SSjZiacezhBt98CVuCiBNxbkE1NWZRa7MkQ6JMKpoLgk8j5OMqGk5FUd2\r\nrdef0vEPweovMWOI4FOQGnZTIsfxjYCbkBwyxYPfIvZqmIkHRAE/wdY86oyc\r\nz9VKcCOPNRkZxaPy7zRgF1/wZ/RhqsH0IuI8f3/MJrIZH3t1uF+Cyfpjm/o+\r\n0ddlhuE40/rC01grbuVtOc2CZRkwlCn5t3+lh08uQlXrn9DedOTksCVhnPTv\r\n9mvyogr+sivSZspjZYqp346SM9R+ckAlh9OyEz8NyALJ3Tgut1VFxWsj7BeJ\r\nfGjTFH6bA2Me/XCVu8pGv0lOBrNkrXYaDp/K8/GbVBgWQXkwXCBPthIFedm4\r\nyU8ZrZmLtkAjwkFzL3L/H7C3EecWKgIkAqY=\r\n=dfVR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.0.1": {"name": "@npmcli/run-script", "version": "6.0.1", "dependencies": {"which": "^3.0.0", "node-gyp": "^9.0.0", "@npmcli/node-gyp": "^3.0.0", "@npmcli/promise-spawn": "^6.0.0", "read-package-json-fast": "^3.0.0"}, "devDependencies": {"tap": "^16.0.1", "require-inject": "^1.4.4", "@npmcli/template-oss": "4.14.1", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "a94404783d9afaff62decb71944435d0d8a29f8e", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-6.0.1.tgz", "fileCount": 14, "integrity": "sha512-Yi04Z<PERSON>old8jcbBJD/ahKMJSQCQifH8DAbMwkBvoLaTpGFxzHC3B/5ZyoVR69q/4xedz84tvi9DJOJjNe17h+LA==", "signatures": [{"sig": "MEUCIQC3MYkFQitllkP+Q5+beHr+O4bhVYFUloIPMMinMhuqsgIgTkrZmqeqTxPaGqBEdamHlbf13HzuK3J2rP2WRSdgRnM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2frun-script@6.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 17912, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkSsgQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp+ag//U+Pnng+0XmWvNUwZc2HSadEU/IDNhdGaJHO8ZwGRD95k/Qx/\r\nNC7zTQJnhL+xHMm+n5tboEEhZhGWQX9aarjP9WM3s8SPIrQxs2jTPa8M5aDQ\r\nPkuTpYS5EmQIUvJdQ7JHwh2tnh4XfQNSSy8dipFgScRy9A+Bp+2oSfW9uZv8\r\nGYArik6ZQ/9BYV4uTBMany8Kx1WFma4EFS3J8FveZWjsUdmqAvhwZ/qERRID\r\n+qLKUhFZOjmnZH/2ralN8kfg3mG1cKIJb+rUoJ0h2HFoFFXT1aNBZIItJmj7\r\n8ak6AOfwxtatGkXB6zZn95r1as96Hea0MK0ZzWvHTnyoqNepDsH57DYEr1mA\r\n4+QFpDggKaBWEQLu3zJ9Rd2W2twtRSvSXYLDP+yy60E7bUvhrzMhb/7f2jhw\r\nig6rLexin5vnJ+wntDasesSPbuWLZ/1cAMD4ygPZq6lS2HD6Yan7pgQWsPwz\r\n5BA4X9yZQVzKBWdbnYEDhtx/GJ7S3eZS0bCbNl2CiBt4IgRNrtqNwQVZNpZT\r\nyjHqRsAEITNMzlPXuFcPysp+P5ntLrgMhjXgNhduvfuSRrTpMKILsBniorCO\r\n7XRhIwc8AGc0Ff3IfsXbsldg6bz50yNGjB6KoYrhpr5cEX6UYxXzPDSJTrhV\r\nOU5C22hzvJPU54cVER6L7RE+cM5xKAHup5U=\r\n=QLbg\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.0.2": {"name": "@npmcli/run-script", "version": "6.0.2", "dependencies": {"which": "^3.0.0", "node-gyp": "^9.0.0", "@npmcli/node-gyp": "^3.0.0", "@npmcli/promise-spawn": "^6.0.0", "read-package-json-fast": "^3.0.0"}, "devDependencies": {"tap": "^16.0.1", "require-inject": "^1.4.4", "@npmcli/template-oss": "4.15.1", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "a25452d45ee7f7fb8c16dfaf9624423c0c0eb885", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-6.0.2.tgz", "fileCount": 14, "integrity": "sha512-NCcr1uQo1k5U+SYlnIrbAh3cxy+OQT1VtqiAbxdymSlptbzBb62AjH2xXgjNCoP073hoa1CfCAcwoZ8k96C4nA==", "signatures": [{"sig": "MEUCIQCJL4BW7neUUSaFh+51GWh+WX0osY/QNnFg6hdFMhcHIQIgGRzzvHLc89mPMiFqL7tWmTS6S/kX7w6Fnglkgr4Gs0A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2frun-script@6.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 18494}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "7.0.0": {"name": "@npmcli/run-script", "version": "7.0.0", "dependencies": {"which": "^4.0.0", "node-gyp": "^9.0.0", "@npmcli/node-gyp": "^3.0.0", "@npmcli/promise-spawn": "^6.0.0", "read-package-json-fast": "^3.0.0"}, "devDependencies": {"tap": "^16.0.1", "require-inject": "^1.4.4", "@npmcli/template-oss": "4.18.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "972ae805df6c8ce0f4607dc81f26bb1242225fe6", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-7.0.0.tgz", "fileCount": 14, "integrity": "sha512-JpR7jgCPIKnke0/xJUSYgWKk6BtWQ2FPoVm4lEC4gTDOjIpBJgAfwg+nGaCwnzY8oq6I5F4r+sI1jGJk/iCh/w==", "signatures": [{"sig": "MEUCIQCFsVMd+Vix3ZHQ4trIsifB8ZMWKQ8Px2zeAyBkoYVcGwIgZ68/5vSPZ6s4hHERVBIkIC9ZYfBtx1MfQI4rvBEkhS8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2frun-script@7.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 18569}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "7.0.1": {"name": "@npmcli/run-script", "version": "7.0.1", "dependencies": {"which": "^4.0.0", "node-gyp": "^9.0.0", "@npmcli/node-gyp": "^3.0.0", "@npmcli/promise-spawn": "^7.0.0", "read-package-json-fast": "^3.0.0"}, "devDependencies": {"tap": "^16.0.1", "require-inject": "^1.4.4", "@npmcli/template-oss": "4.18.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "18eebaed96214357f618a82510411319181417bd", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-7.0.1.tgz", "fileCount": 14, "integrity": "sha512-Od/JMrgkjZ8alyBE0IzeqZDiF1jgMez9Gkc/OYrCkHHiXNwM0wc6s7+h+xM7kYDZkS0tAoOLr9VvygyE5+2F7g==", "signatures": [{"sig": "MEYCIQC4XuKmD5xARuDLc99Wut+5fpw5k8J0eW8Qdv9ZY0HUUgIhAMBaB9cmpvI87ldGJZEfPmmkdr5WxmYZOjOjhF+tj8N8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2frun-script@7.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 18569}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "7.0.2": {"name": "@npmcli/run-script", "version": "7.0.2", "dependencies": {"which": "^4.0.0", "node-gyp": "^10.0.0", "@npmcli/node-gyp": "^3.0.0", "@npmcli/promise-spawn": "^7.0.0", "read-package-json-fast": "^3.0.0"}, "devDependencies": {"tap": "^16.0.1", "require-inject": "^1.4.4", "@npmcli/template-oss": "4.19.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "497e7f058799497889df65900c711312252276d3", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-7.0.2.tgz", "fileCount": 14, "integrity": "sha512-Omu0rpA8WXvcGeY6DDzyRoY1i5DkCBkzyJ+m2u7PD6quzb0TvSqdIPOkTn8ZBOj7LbbcbMfZ3c5skwSu6m8y2w==", "signatures": [{"sig": "MEYCIQD+sMg/r9EWFAKkSppAu+jidcOsNJn6hp6F4sRwCegZ4gIhAOz7A8vAbw+4A051IJa/9jEixNvv16k0uttsNlTcBvgr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2frun-script@7.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 18483}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "7.0.3": {"name": "@npmcli/run-script", "version": "7.0.3", "dependencies": {"which": "^4.0.0", "node-gyp": "^10.0.0", "@npmcli/node-gyp": "^3.0.0", "@npmcli/promise-spawn": "^7.0.0", "read-package-json-fast": "^3.0.0"}, "devDependencies": {"tap": "^16.0.1", "require-inject": "^1.4.4", "@npmcli/template-oss": "4.21.3", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "a803e05c4b58e2a7b3f801a9f2767f22822df457", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-7.0.3.tgz", "fileCount": 14, "integrity": "sha512-ZMWGLHpzMq3rBGIwPyeaoaleaLMvrBrH8nugHxTi5ACkJZXTxXPtVuEH91ifgtss5hUwJQ2VDnzDBWPmz78rvg==", "signatures": [{"sig": "MEYCIQCwnGzw20M2auGqLvZ4KiCHsE2yXMaQcuvQ+yYBUKHHMwIhAJivwjG+2Jpd5GFiSNG4zMwyiLx0yO9xBbg/3no6byKQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2frun-script@7.0.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 18338}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "7.0.4": {"name": "@npmcli/run-script", "version": "7.0.4", "dependencies": {"which": "^4.0.0", "node-gyp": "^10.0.0", "@npmcli/node-gyp": "^3.0.0", "@npmcli/package-json": "^5.0.0", "@npmcli/promise-spawn": "^7.0.0"}, "devDependencies": {"tap": "^16.0.1", "spawk": "^1.8.1", "@npmcli/template-oss": "4.21.3", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "9f29aaf4bfcf57f7de2a9e28d1ef091d14b2e6eb", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-7.0.4.tgz", "fileCount": 13, "integrity": "sha512-9ApYM/3+rBt9V80aYg6tZfzj3UWdiYyCt7gJUD1VJKvWF5nwKDSICXbYIQbspFTq6TOpbsEtIC0LArB8d9PFmg==", "signatures": [{"sig": "MEQCIHWnQ9WIR0Kwqugh2/T3DZOefvCnuA96SnwR1webe4P8AiBQQWw3XI2wB0xGDicPvlkh6sdL60xS4vdBfefstkY1Iw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2frun-script@7.0.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 18366}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "8.0.0": {"name": "@npmcli/run-script", "version": "8.0.0", "dependencies": {"which": "^4.0.0", "node-gyp": "^10.0.0", "proc-log": "^4.0.0", "@npmcli/node-gyp": "^3.0.0", "@npmcli/package-json": "^5.0.0", "@npmcli/promise-spawn": "^7.0.0"}, "devDependencies": {"tap": "^16.0.1", "spawk": "^1.8.1", "@npmcli/template-oss": "4.21.3", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "644f8e28fd3cde25e40a79d3b35cb14076ec848b", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-8.0.0.tgz", "fileCount": 13, "integrity": "sha512-5noc+eCQmX1W9nlFUe65n5MIteikd3vOA2sEPdXtlUv68KWyHNFZnT/LDRXu/E4nZ5yxjciP30pADr/GQ97W1w==", "signatures": [{"sig": "MEYCIQD0oOGS6Q8iZ6Kxrvq6D6wNwPmBXsbMz2p6qHVoS8CnfQIhAMiMr3YHSV9gIY3lSAmlk85WZGjlUOs6GpnRSE5YDXHm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2frun-script@8.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 18171}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "8.1.0": {"name": "@npmcli/run-script", "version": "8.1.0", "dependencies": {"which": "^4.0.0", "node-gyp": "^10.0.0", "proc-log": "^4.0.0", "@npmcli/node-gyp": "^3.0.0", "@npmcli/package-json": "^5.0.0", "@npmcli/promise-spawn": "^7.0.0"}, "devDependencies": {"tap": "^16.0.1", "spawk": "^1.8.1", "@npmcli/template-oss": "4.21.4", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "a563e5e29b1ca4e648a6b1bbbfe7220b4bfe39fc", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-8.1.0.tgz", "fileCount": 13, "integrity": "sha512-y7efHHwghQfk28G2z3tlZ67pLG0XdfYbcVG26r7YIXALRsrVQcTq4/tdenSmdOrEsNahIYA/eh8aEVROWGFUDg==", "signatures": [{"sig": "MEUCIQCBPkWlkT62FwADhRfiNfXFRLe+oG2AFhVYivwUllhjzQIgcMRqwAoHCM8J1w4aL3R9qfGob/Q7glRS2kyD7z3ZfUM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2frun-script@8.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 18251}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "9.0.0": {"name": "@npmcli/run-script", "version": "9.0.0", "dependencies": {"which": "^4.0.0", "node-gyp": "^10.0.0", "proc-log": "^5.0.0", "@npmcli/node-gyp": "^4.0.0", "@npmcli/package-json": "^5.0.0", "@npmcli/promise-spawn": "^8.0.0"}, "devDependencies": {"tap": "^16.0.1", "spawk": "^1.8.1", "@npmcli/template-oss": "4.23.3", "@npmcli/eslint-config": "^5.0.0"}, "dist": {"shasum": "0c8315a7654b562f38e4845715e1c03395fe1b11", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-9.0.0.tgz", "fileCount": 13, "integrity": "sha512-5GgAfGebmUP3ktRhucOwH7Zatjhh3ANCDs9KunVPDU59QP5Gg7aSyLvKsrCGGx3GGHzjsfYIJxGQXihORiCfIg==", "signatures": [{"sig": "MEUCIQDvFmt5BWdUPgnwLV5WaWDqw3HzUdjygukQN9g0Nqd3BQIgO0MCyWzzteNx7HeA82b4Cynn5NOdQ6SVg8A9lX85+QQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2frun-script@9.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 18265}, "engines": {"node": "^18.17.0 || >=20.5.0"}}, "9.0.1": {"name": "@npmcli/run-script", "version": "9.0.1", "dependencies": {"which": "^5.0.0", "node-gyp": "^10.0.0", "proc-log": "^5.0.0", "@npmcli/node-gyp": "^4.0.0", "@npmcli/package-json": "^6.0.0", "@npmcli/promise-spawn": "^8.0.0"}, "devDependencies": {"tap": "^16.0.1", "spawk": "^1.8.1", "@npmcli/template-oss": "4.23.3", "@npmcli/eslint-config": "^5.0.0"}, "dist": {"shasum": "f90a0cf4f4e8f42d66669d3af568c5036859b654", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-9.0.1.tgz", "fileCount": 13, "integrity": "sha512-q9C0uHrb6B6cm3qXVM32UmpqTKuFGbtP23O2K5sLvPMz2hilKd0ptqGXSpuunOuOmPQb/aT5F/kCXFc1P2gO/A==", "signatures": [{"sig": "MEQCIHVunZaQErsn0fNEm5LNmoauqS1FtdP8HxK4/uWHTLD7AiAnrTvlGym0aaGnZ9ylxqhbFEMazajeyReDiOqCcWWYYQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2frun-script@9.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 18265}, "engines": {"node": "^18.17.0 || >=20.5.0"}}, "9.0.2": {"name": "@npmcli/run-script", "version": "9.0.2", "dependencies": {"which": "^5.0.0", "node-gyp": "^11.0.0", "proc-log": "^5.0.0", "@npmcli/node-gyp": "^4.0.0", "@npmcli/package-json": "^6.0.0", "@npmcli/promise-spawn": "^8.0.0"}, "devDependencies": {"tap": "^16.0.1", "spawk": "^1.8.1", "@npmcli/template-oss": "4.23.4", "@npmcli/eslint-config": "^5.0.0"}, "dist": {"shasum": "621f993d59bae770104a5b655a38c6579d5ce6be", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-9.0.2.tgz", "fileCount": 13, "integrity": "sha512-cJXiUlycdizQwvqE1iaAb4VRUM3RX09/8q46zjvy+ct9GhfZRWd7jXYVc1tn/CfRlGPVkX/u4sstRlepsm7hfw==", "signatures": [{"sig": "MEYCIQCALEFJRrew1woCqVq9do6r7+ZJlR89RuwBWigx76yhjQIhANODTkwWuAQSAII+GWghIG0blqWFpfqAhMZNm5HmIGu7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2frun-script@9.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 18265}, "engines": {"node": "^18.17.0 || >=20.5.0"}}, "9.1.0": {"name": "@npmcli/run-script", "version": "9.1.0", "dependencies": {"which": "^5.0.0", "node-gyp": "^11.0.0", "proc-log": "^5.0.0", "@npmcli/node-gyp": "^4.0.0", "@npmcli/package-json": "^6.0.0", "@npmcli/promise-spawn": "^8.0.0"}, "devDependencies": {"tap": "^16.0.1", "spawk": "^1.8.1", "@npmcli/template-oss": "4.24.1", "@npmcli/eslint-config": "^5.0.0"}, "dist": {"shasum": "6168c2be4703fe5ed31acb08a2151cb620ed30a4", "tarball": "https://registry.npmjs.org/@npmcli/run-script/-/run-script-9.1.0.tgz", "fileCount": 13, "integrity": "sha512-aoNSbxtkePXUlbZB+anS1LqsJdctG5n3UVhfU47+CDdwMi6uNTBMF9gPcQRnqghQd2FGzcwwIFBruFMxjhBewg==", "signatures": [{"sig": "MEUCIC0v56vsn5kUNeHkBIRdYp2bEbhyu2i61zY5r1thId6NAiEAii49+mSNaLb6lrwUSjtD30WybiCfXenzquSXA2yUZfY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2frun-script@9.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 18606}, "engines": {"node": "^18.17.0 || >=20.5.0"}}}, "modified": "2025-05-14T20:02:56.293Z"}