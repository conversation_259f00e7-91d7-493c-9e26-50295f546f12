{"_id": "define-properties", "_rev": "18-37ed0ecd46e72a97ea72057d5d529041", "name": "define-properties", "description": "Define multiple non-enumerable properties at once. Uses `Object.defineProperty` when available; falls back to standard assignment in older engines.", "dist-tags": {"latest": "1.2.1"}, "versions": {"1.0.0": {"name": "define-properties", "version": "1.0.0", "author": {"name": "<PERSON>"}, "description": "Define multiple non-enumerable properties at once. Uses `Object.defineProperty` when available; falls back to standard assignment in older engines.", "license": "MIT", "main": "index.js", "scripts": {"test": "npm run lint && node test/index.js && npm run security", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet", "lint": "npm run jscs && npm run eslint", "jscs": "jscs test/*.js *.js", "eslint": "eslint test/*.js *.js", "eccheck": "editorconfig-tools check *.js **/*.js > /dev/null", "security": "nsp package"}, "repository": {"type": "git", "url": "git://github.com/ljharb/define-properties.git"}, "keywords": ["Object.defineProperty", "Object.defineProperties", "object", "property descriptor", "descriptor", "define", "ES5"], "dependencies": {"foreach": "~2.0.5", "object-keys": "~1.0.2"}, "devDependencies": {"tape": "~3.0.3", "covert": "1.0.0", "jscs": "~1.9.0", "editorconfig-tools": "~0.0.1", "nsp": "~0.5.2", "eslint": "~0.11.0"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "gitHead": "7f59dd1723500ba6390a2a6fc330e20ad7a1f58c", "bugs": {"url": "https://github.com/ljharb/define-properties/issues"}, "homepage": "https://github.com/ljharb/define-properties", "_id": "define-properties@1.0.0", "_shasum": "64e04df26f37a219a4467b2cde19eb075de9d004", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "dist": {"shasum": "64e04df26f37a219a4467b2cde19eb075de9d004", "tarball": "https://registry.npmjs.org/define-properties/-/define-properties-1.0.0.tgz", "integrity": "sha512-vxfdDa3w7qextqSHUtH08ZxQKucIvu/QONdqv/h4HsQiDfE8wHXphVySR3Lgvut1LbDCwHQkP1DzcWRSuZlqKw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDibzfFKtipNe9VvTEnT7z/PNhXuZHIUf9fGk1Ij4U4GgIhAITZsMW1GBzwfg0SO7hIIJg5bwPxUL7dapWUOR+PC20b"}]}, "directories": {}}, "1.0.1": {"name": "define-properties", "version": "1.0.1", "author": {"name": "<PERSON>"}, "description": "Define multiple non-enumerable properties at once. Uses `Object.defineProperty` when available; falls back to standard assignment in older engines.", "license": "MIT", "main": "index.js", "scripts": {"test": "npm run lint && node test/index.js && npm run security", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet", "lint": "npm run jscs && npm run eslint", "jscs": "jscs test/*.js *.js", "eslint": "eslint test/*.js *.js", "eccheck": "editorconfig-tools check *.js **/*.js > /dev/null", "security": "nsp package"}, "repository": {"type": "git", "url": "git://github.com/ljharb/define-properties.git"}, "keywords": ["Object.defineProperty", "Object.defineProperties", "object", "property descriptor", "descriptor", "define", "ES5"], "dependencies": {"foreach": "~2.0.5", "object-keys": "~1.0.3"}, "devDependencies": {"tape": "~3.0.3", "covert": "1.0.0", "jscs": "~1.9.0", "editorconfig-tools": "~0.0.1", "nsp": "~0.5.2", "eslint": "~0.11.0"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "gitHead": "0fc836602486b1360bb54f430c18cebba25a0288", "bugs": {"url": "https://github.com/ljharb/define-properties/issues"}, "homepage": "https://github.com/ljharb/define-properties", "_id": "define-properties@1.0.1", "_shasum": "aa2f1a1d38cc4ebaabf314ac1e77e024060e0ec0", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "dist": {"shasum": "aa2f1a1d38cc4ebaabf314ac1e77e024060e0ec0", "tarball": "https://registry.npmjs.org/define-properties/-/define-properties-1.0.1.tgz", "integrity": "sha512-+Kc8DhpxT9gYggrcyv63MbFDXIH4ye6xzvqXStvojl74Vo2V4BqfRNWZZaeuUhmcqywFdD11Giex2COwC6AMdA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDOuXmwoDIJrWyXnymCBygPxhzg0zJH+pxNwd+4vkxvzgIhAJS2CGZSNENNLJ8taCSARD6vD7VS4nJ6Bgy8Ae5lvboN"}]}, "directories": {}}, "1.0.2": {"name": "define-properties", "version": "1.0.2", "author": {"name": "<PERSON>"}, "description": "Define multiple non-enumerable properties at once. Uses `Object.defineProperty` when available; falls back to standard assignment in older engines.", "license": "MIT", "main": "index.js", "scripts": {"test": "npm run lint && node test/index.js && npm run security", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet", "lint": "npm run jscs && npm run eslint", "jscs": "jscs test/*.js *.js", "eslint": "eslint test/*.js *.js", "eccheck": "editorconfig-tools check *.js **/*.js > /dev/null", "security": "nsp package"}, "repository": {"type": "git", "url": "git://github.com/ljharb/define-properties.git"}, "keywords": ["Object.defineProperty", "Object.defineProperties", "object", "property descriptor", "descriptor", "define", "ES5"], "dependencies": {"foreach": "^2.0.5", "object-keys": "^1.0.4"}, "devDependencies": {"tape": "^4.0.0", "covert": "^1.1.0", "jscs": "^1.13.1", "editorconfig-tools": "^0.1.1", "nsp": "^1.0.1", "eslint": "^0.21.2"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "gitHead": "37dd7335f8ec75f93ffb0768a321a8f277a2bc94", "bugs": {"url": "https://github.com/ljharb/define-properties/issues"}, "homepage": "https://github.com/ljharb/define-properties#readme", "_id": "define-properties@1.0.2", "_shasum": "6999cad02fd97bd62b06a9eb121d8d6966d48d37", "_from": ".", "_npmVersion": "2.9.0", "_nodeVersion": "2.0.2", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "dist": {"shasum": "6999cad02fd97bd62b06a9eb121d8d6966d48d37", "tarball": "https://registry.npmjs.org/define-properties/-/define-properties-1.0.2.tgz", "integrity": "sha512-pUaWgaSuCBbnAvTEFLT4+9plxRT02eXu7cRA0kwj8vjoGD+w4/uL5wLDSy+JcqTNd0kpP3/rWgPxhqylW+i7PQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBnniPlNMiOhsQ880VdcxE/tovPeXpMr8s6cWnlHCDcGAiEAmkMc+1/WBZot0JId8bk3G0NB/vROPenRp0l+8fNj21c="}]}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "directories": {}}, "1.1.0": {"name": "define-properties", "version": "1.1.0", "author": {"name": "<PERSON>"}, "description": "Define multiple non-enumerable properties at once. Uses `Object.defineProperty` when available; falls back to standard assignment in older engines.", "license": "MIT", "main": "index.js", "scripts": {"test": "npm run lint && node test/index.js && npm run security", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet", "lint": "npm run jscs && npm run eslint", "jscs": "jscs test/*.js *.js", "eslint": "eslint test/*.js *.js", "eccheck": "editorconfig-tools check *.js **/*.js > /dev/null", "security": "nsp package"}, "repository": {"type": "git", "url": "git://github.com/ljharb/define-properties.git"}, "keywords": ["Object.defineProperty", "Object.defineProperties", "object", "property descriptor", "descriptor", "define", "ES5"], "dependencies": {"foreach": "^2.0.5", "object-keys": "^1.0.4"}, "devDependencies": {"tape": "^4.0.0", "covert": "^1.1.0", "jscs": "^1.13.1", "editorconfig-tools": "^0.1.1", "nsp": "^1.0.3", "eslint": "^0.24.0"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "gitHead": "0855002376afdcbc6c6c5d56cdb207cc69231535", "bugs": {"url": "https://github.com/ljharb/define-properties/issues"}, "homepage": "https://github.com/ljharb/define-properties#readme", "_id": "define-properties@1.1.0", "_shasum": "e445de572ba03584e707e6e7fa7757bcb61e2688", "_from": ".", "_npmVersion": "2.11.1", "_nodeVersion": "2.3.0", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "dist": {"shasum": "e445de572ba03584e707e6e7fa7757bcb61e2688", "tarball": "https://registry.npmjs.org/define-properties/-/define-properties-1.1.0.tgz", "integrity": "sha512-o/M7oAJDcb9Q4BNA03OmiPpAqFcka0CAhdmF9er4P7I8PX9CLqyHLjLMJOOTjj6N/LF8/0nMaVKTbth0ouSffw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDaR4GU49eGjoyJ/nR8fT33SeiCnT4lsFkQpjyWiwENEwIhAL7HgxI6iRVZ/g2reGdHTbXfS19mULx+Npark1W4fdrr"}]}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "directories": {}}, "1.1.1": {"name": "define-properties", "version": "1.1.1", "author": {"name": "<PERSON>"}, "description": "Define multiple non-enumerable properties at once. Uses `Object.defineProperty` when available; falls back to standard assignment in older engines.", "license": "MIT", "main": "index.js", "scripts": {"test": "npm run lint && node test/index.js && npm run security", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet", "lint": "npm run jscs && npm run eslint", "jscs": "jscs test/*.js *.js", "eslint": "eslint test/*.js *.js", "eccheck": "editorconfig-tools check *.js **/*.js > /dev/null", "security": "nsp package"}, "repository": {"type": "git", "url": "git://github.com/ljharb/define-properties.git"}, "keywords": ["Object.defineProperty", "Object.defineProperties", "object", "property descriptor", "descriptor", "define", "ES5"], "dependencies": {"foreach": "^2.0.5", "object-keys": "^1.0.7"}, "devDependencies": {"tape": "^4.0.1", "covert": "^1.1.0", "jscs": "^1.13.1", "editorconfig-tools": "^0.1.1", "nsp": "^1.0.3", "eslint": "^1.0.0-rc-1"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "gitHead": "4647949f0b4da52f9968977a9be754e5e11c5ac4", "bugs": {"url": "https://github.com/ljharb/define-properties/issues"}, "homepage": "https://github.com/ljharb/define-properties#readme", "_id": "define-properties@1.1.1", "_shasum": "ac04abba5d32847f912cfbe41aed932faa14061f", "_from": ".", "_npmVersion": "2.13.0", "_nodeVersion": "2.4.0", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "dist": {"shasum": "ac04abba5d32847f912cfbe41aed932faa14061f", "tarball": "https://registry.npmjs.org/define-properties/-/define-properties-1.1.1.tgz", "integrity": "sha512-rVN/zTp+R6Tiu0GP2GYkLPZax/F5b2uh3VQnIdsfVsKHDz1yfNsUWiy367ytlDjaYzMYOhIkGLUm1PtypHMTnA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIATKnr2USfTV+5IMhzkLImZlpy7AHG9ACf8oVwUhzuSNAiEA1hXotlSI/6a6Y1Z8uBOcKELWeXHjDXXFx0uC3uEVPMs="}]}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "directories": {}}, "1.1.2": {"name": "define-properties", "version": "1.1.2", "author": {"name": "<PERSON>"}, "description": "Define multiple non-enumerable properties at once. Uses `Object.defineProperty` when available; falls back to standard assignment in older engines.", "license": "MIT", "main": "index.js", "scripts": {"test": "npm run lint && node test/index.js && npm run security", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet", "lint": "npm run jscs && npm run eslint", "jscs": "jscs test/*.js *.js", "eslint": "eslint test/*.js *.js", "eccheck": "editorconfig-tools check *.js **/*.js > /dev/null", "security": "nsp package"}, "repository": {"type": "git", "url": "git://github.com/ljharb/define-properties.git"}, "keywords": ["Object.defineProperty", "Object.defineProperties", "object", "property descriptor", "descriptor", "define", "ES5"], "dependencies": {"foreach": "^2.0.5", "object-keys": "^1.0.8"}, "devDependencies": {"tape": "^4.2.1", "covert": "^1.1.0", "jscs": "^2.3.1", "editorconfig-tools": "^0.1.1", "nsp": "^1.1.0", "eslint": "^1.6.0", "@ljharb/eslint-config": "^1.3.0"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "gitHead": "6467a10e6f493d8a1a4f6ec8442ffee137aab7ba", "bugs": {"url": "https://github.com/ljharb/define-properties/issues"}, "homepage": "https://github.com/ljharb/define-properties#readme", "_id": "define-properties@1.1.2", "_shasum": "83a73f2fea569898fb737193c8f873caf6d45c94", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.1", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "dist": {"shasum": "83a73f2fea569898fb737193c8f873caf6d45c94", "tarball": "https://registry.npmjs.org/define-properties/-/define-properties-1.1.2.tgz", "integrity": "sha512-hpr5VSFXGamODSCN6P2zdSBY6zJT7DlcBAHiPIa2PWDvfBqJQntSK0ehUoHoS6HGeSS19dgj7E+1xOjfG3zEtQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFmYfFegTKCrSnO8+YFjg0SZr5jYybWblLiyd9f62pdBAiEAhGGfud/YdiL+CoxNQRaOwOT2Ft26BF3U16av4Qj/by8="}]}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "directories": {}}, "1.1.3": {"name": "define-properties", "version": "1.1.3", "author": {"name": "<PERSON>"}, "description": "Define multiple non-enumerable properties at once. Uses `Object.defineProperty` when available; falls back to standard assignment in older engines.", "license": "MIT", "main": "index.js", "scripts": {"pretest": "npm run --silent lint", "test": "npm run --silent tests-only", "posttest": "npm run --silent security", "tests-only": "node test/index.js", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet", "lint": "npm run --silent jscs && npm run --silent eslint", "jscs": "jscs test/*.js *.js", "eslint": "eslint test/*.js *.js", "security": "nsp check"}, "repository": {"type": "git", "url": "git://github.com/ljharb/define-properties.git"}, "keywords": ["Object.defineProperty", "Object.defineProperties", "object", "property descriptor", "descriptor", "define", "ES5"], "dependencies": {"object-keys": "^1.0.12"}, "devDependencies": {"@ljharb/eslint-config": "^13.0.0", "covert": "^1.1.0", "eslint": "^5.3.0", "jscs": "^3.0.7", "nsp": "^3.2.1", "tape": "^4.9.0"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "gitHead": "e5478e3d2880b90a97daa62d76abed34d91154dd", "bugs": {"url": "https://github.com/ljharb/define-properties/issues"}, "homepage": "https://github.com/ljharb/define-properties#readme", "_id": "define-properties@1.1.3", "_npmVersion": "6.2.0", "_nodeVersion": "10.8.0", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-3MqfYKj2lLzdMSf8ZIZE/V+Zuy+BgD6f164e8K2w7dgnpKArBDerGYpM46IYYcjnkdPNMjPk9A6VFB8+3SKlXQ==", "shasum": "cf88da6cbee26fe6db7094f61d870cbd84cee9f1", "tarball": "https://registry.npmjs.org/define-properties/-/define-properties-1.1.3.tgz", "fileCount": 10, "unpackedSize": 23025, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbc67HCRA9TVsSAnZWagAAiLUP/3CxQIzm2kQKyFeL6rHJ\nHrqG2U6H8UXQbVjxAgTa4gli6BZSzIk6WkooZ1INWKJdfApxKfWJAVbRYCwD\nhXIB8rzI/kcsQPO+I1CVZvUmSdFwhtX7Wc2bzWmH5KN0m8LclaX8ddYZXXi3\nhtDIFXTVYIEam4fTdrBREO59hRopvcYBsR0QM3yjcD74FY7ugPxqzIrhhd/w\nlnbSUcFww2UKubiQX2YmNp+boJ74oEGoq7rA8L4PUdU1ffOPRZ47pAlGPVxu\noHKTk/KViS69KD1eChpS1z+ptPgZWIYobziluafItmX+A4XNFrHuaizF6s6r\no2lJWdT2DAABZlMLPaBBuiXM9U/STYLj+THpBlqSqNxXAP/c1tBIG70R0EHy\nSVnSAUcclxKQ5PfAeLBiR6sux49MvmX/Aq5ykC2OxAzgbknOQb4DSh7oOuFE\nsMxIiErRJ9gpTXVrgvlMwy6on+XqAGMhJZHDPvPrb0lw6Sv/wNwvxrd13bF9\nxKVEywS6OBgZ1Ag0+tgt+Iqp+1h7ZFMxWSzlWzJIlLwVAGKqeaCI0cDnvHaT\nB6I0PxC9mHMCBRMi9zjfGgYhzOOf+QlxthaM9fB0BDVraf4s1FIEUVfMDvCa\nGkPb86BE6dnLzCsIdt+aWbxW+lwRVwLu+QrPFFGTE44KNR3YuLG8eEEWEHpN\nHZZs\r\n=7SxH\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC0rdupohHcNiPth5mboCDG22vQVDBbBkyKmpxYVwkAfQIhAN3GX6Q+b29wDdOkUdmUxnjGUuzPsyLMftGxNeNEznj1"}]}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/define-properties_1.1.3_1534308039180_0.8446271629420612"}, "_hasShrinkwrap": false}, "1.1.4": {"name": "define-properties", "version": "1.1.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "description": "Define multiple non-enumerable properties at once. Uses `Object.defineProperty` when available; falls back to standard assignment in older engines.", "license": "MIT", "main": "index.js", "scripts": {"prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "posttest": "aud --production", "tests-only": "nyc tape 'test/**/*.js'", "lint": "eslint --ext=js,mjs ."}, "repository": {"type": "git", "url": "git://github.com/ljharb/define-properties.git"}, "keywords": ["Object.defineProperty", "Object.defineProperties", "object", "property descriptor", "descriptor", "define", "ES5"], "dependencies": {"has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}, "devDependencies": {"@ljharb/eslint-config": "^21.0.0", "aud": "^2.0.0", "eslint": "=8.8.0", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.5.3"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "gitHead": "08fa234e22964a179aad624bed13eca44ad8c6b4", "bugs": {"url": "https://github.com/ljharb/define-properties/issues"}, "homepage": "https://github.com/ljharb/define-properties#readme", "_id": "define-properties@1.1.4", "_nodeVersion": "17.9.0", "_npmVersion": "8.3.1", "dist": {"integrity": "sha512-uckOqKcfaVvtBdsVkdPv3XjveQJsNQqmhXgRi8uhvWWuPYZCNlzT8qAyblUgNoXdHdjMTzAqeGjAoli8f+bzPA==", "shasum": "0b14d7bd7fbeb2f3572c3a7eda80ea5d57fb05b1", "tarball": "https://registry.npmjs.org/define-properties/-/define-properties-1.1.4.tgz", "fileCount": 9, "unpackedSize": 10361, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDXsYGCvtExg9J1AGDfmtzB2pJPElyT+JVZGkuBGXJoXQIgHdeuoUI0aLNb+B+05UiELF99rRCn/kSrjgMoWoBzNvc="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWQ2PACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqpiw//bqOxAELjon6L7lB1w+12phuunCilvX1V98bYW2u+29Uh95oi\r\nNfYzb4AsnaDvXu5WjqOa3wlHxBMl9k8UAe5/C6KbzyiZzRuHTpLXCwHFpi9x\r\nXbvOxmG/KTeFH6QJ66PPj2B7mDDCKBdvicdOQWLlYSomngmBdE+xiBZC9vyl\r\n72VClJN4lFb92B1Bi9iak4J+jUYby8IuWTXSIksjzwBBgpw80JqBTKJ5gZAi\r\nGWFl3TmV8H0tWfmwM2RaYxUcCIka/PSsJZU22OQqncm2z9VggbGN3zL3OQe1\r\nTpPDnaC/QIC+ZYNpkP530ArtS5DKSuxiqNtgySO/uMNtdvHBBGsLs4mslwo4\r\nHGsUumHGGAcDsbN6nOfI6BlkEb5sVxmJkFm5PJcHQOVnP54e8IJjvxA1qWkM\r\nO49WMu3sbPPXjxiO60aybNXUIMdBNeuUqX96BW5kJQr83mhbW4dOIsI1k5XP\r\nbekhqLFp4tIHCoXcZg+kWTibjNoiAK9stFf0gWMWrjmQNu6m3umv8uxmH3kc\r\nC+RQCemUghhQHkJtCh9KiekYhZVIweqvyK4sUlgYE2Ev3zsA+VWllk1/rh9I\r\n/9y6V8q7IbfKC6kd3VOKgLEOG9aYNPiy23/1u9XGY29YJl7zzR7e/nnu9Iwp\r\nR2ureEUvrygxBbF7SNx7muKKBfs3Sy7Xo2g=\r\n=RjYF\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/define-properties_1.1.4_1650003343480_0.7576999598646093"}, "_hasShrinkwrap": false}, "1.2.0": {"name": "define-properties", "version": "1.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "description": "Define multiple non-enumerable properties at once. Uses `Object.defineProperty` when available; falls back to standard assignment in older engines.", "license": "MIT", "main": "index.js", "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "posttest": "aud --production", "tests-only": "nyc tape 'test/**/*.js'", "lint": "eslint --ext=js,mjs .", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/ljharb/define-properties.git"}, "keywords": ["Object.defineProperty", "Object.defineProperties", "object", "property descriptor", "descriptor", "define", "ES5"], "dependencies": {"has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}, "devDependencies": {"@ljharb/eslint-config": "^21.0.1", "aud": "^2.0.2", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "in-publish": "^2.0.1", "npmignore": "^0.3.0", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.6.3"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true, "startingVersion": "1.1.5"}, "publishConfig": {"ignore": [".github/workflows", "test/"]}, "gitHead": "aa5afa274d736c1ebd59e403b5b3b5b404689a57", "bugs": {"url": "https://github.com/ljharb/define-properties/issues"}, "homepage": "https://github.com/ljharb/define-properties#readme", "_id": "define-properties@1.2.0", "_nodeVersion": "19.6.0", "_npmVersion": "9.4.0", "dist": {"integrity": "sha512-xvqAVKGfT1+UAvPwKTVw/njhdQ8ZhXK4lI0bCIuCMrp2up9nPnaDftrLtmpTazqd1o+UY4zgzU+avtMbDP+ldA==", "shasum": "52988570670c9eacedd8064f4a990f2405849bd5", "tarball": "https://registry.npmjs.org/define-properties/-/define-properties-1.2.0.tgz", "fileCount": 9, "unpackedSize": 12453, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC2R6SBVZWJCCg7Z1PkTr8TYLGBpk5aQNaXmqzjLJml2QIgJipbNCmGIXHgvp4vOv0L/SBkYkerCHZ2PyQBAVjAScQ="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5niKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqcWBAAgCes8tVtZ6ORE3V+bO+EyPBU76QcCa52bvve/uQKu2DvcSJz\r\nnvnC1aIUlDkR8/ZHI8LC5ONJOos5nIvVwC9cjqjXekk9Y1pXtdmNmplQms04\r\njWnZGp23RWhR0RkghJT7c33Zt+6o0f0RokC/pXheR37qRTclwtx/QXHc3U15\r\nqWb8fQbotRHSnoo7bMAM6DVmEeGQD8KIPd5NtoLx6qL0IWP0kQzo1LUs8Cif\r\nE6Y6jkrknPhptz9aVOIj2H0AVtj7NzjSSxtk4qjg3v3Vw35dSj1vC5UuoT65\r\nn6bBMSk2uS7ISTcKJJsYvRHbfk06XQJdO0/LXPlzqLDjt34tA7M91l+Wmvbz\r\nJNuuoo0jmnMk0PLK4PCvAGLBnPUiBH+02iPwLL0EfrGSRaBIOEi+ZNrAqu2M\r\n8kb58GjN4qtfBwobdq2AIDSJKJ3tkss0KV7a8j54+sKyDHEoAXD/XziI4Q4v\r\nNtZCiFF4xtmphCOgj04j1Bp26gB0c6SRTXm7Glsw7vm4bpwsxxdL5GIoIQAA\r\np3laNAtXOOgVDguFoGHwfJTkL2d2lCpfiY66YpemYptsA0lQEEjvVhyrkHpO\r\nesbdlZi8EiZcZTNdto2Gj5iFr8Pqd764Z9+DLH/DaVGFcX+z6NgNRN1Ia49L\r\nLOetyEJ8/3fa4D+k3b531EH/iXJl8zQu9q0=\r\n=oqSW\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/define-properties_1.2.0_1676048522165_0.7917569075351913"}, "_hasShrinkwrap": false}, "1.2.1": {"name": "define-properties", "version": "1.2.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "description": "Define multiple non-enumerable properties at once. Uses `Object.defineProperty` when available; falls back to standard assignment in older engines.", "license": "MIT", "main": "index.js", "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "posttest": "aud --production", "tests-only": "nyc tape 'test/**/*.js'", "lint": "eslint --ext=js,mjs .", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/ljharb/define-properties.git"}, "keywords": ["Object.defineProperty", "Object.defineProperties", "object", "property descriptor", "descriptor", "define", "ES5"], "dependencies": {"define-data-property": "^1.0.1", "has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}, "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "aud": "^2.0.3", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "in-publish": "^2.0.1", "npmignore": "^0.3.0", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.6.6"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true, "startingVersion": "1.1.5"}, "publishConfig": {"ignore": [".github/workflows", "test/"]}, "_id": "define-properties@1.2.1", "gitHead": "d7a4db30214eb06d3997dc3e662c11dfe95b25bd", "bugs": {"url": "https://github.com/ljharb/define-properties/issues"}, "homepage": "https://github.com/ljharb/define-properties#readme", "_nodeVersion": "20.6.0", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==", "shasum": "10781cc616eb951a80a034bafcaa7377f6af2b6c", "tarball": "https://registry.npmjs.org/define-properties/-/define-properties-1.2.1.tgz", "fileCount": 9, "unpackedSize": 12890, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFStFcKn+/R6mBEFmTGy+05p5JlXtKJFWGMKiPfP7kwxAiEA0hUJ0Dh3kDbj4CwpXFjiqXtYo+tcy0N50rSz9aAEt4Q="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/define-properties_1.2.1_1694585113776_0.7418978055551875"}, "_hasShrinkwrap": false}}, "readme": "# define-properties <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![dependency status][deps-svg]][deps-url]\n[![dev dependency status][dev-deps-svg]][dev-deps-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\nDefine multiple non-enumerable properties at once. Uses `Object.defineProperty` when available; falls back to standard assignment in older engines.\nExisting properties are not overridden. Accepts a map of property names to a predicate that, when true, force-overrides.\n\n## Example\n\n```js\nvar define = require('define-properties');\nvar assert = require('assert');\n\nvar obj = define({ a: 1, b: 2 }, {\n\ta: 10,\n\tb: 20,\n\tc: 30\n});\nassert(obj.a === 1);\nassert(obj.b === 2);\nassert(obj.c === 30);\nif (define.supportsDescriptors) {\n\tassert.deepEqual(Object.keys(obj), ['a', 'b']);\n\tassert.deepEqual(Object.getOwnPropertyDescriptor(obj, 'c'), {\n\t\tconfigurable: true,\n\t\tenumerable: false,\n\t\tvalue: 30,\n\t\twritable: false\n\t});\n}\n```\n\nThen, with predicates:\n```js\nvar define = require('define-properties');\nvar assert = require('assert');\n\nvar obj = define({ a: 1, b: 2, c: 3 }, {\n\ta: 10,\n\tb: 20,\n\tc: 30\n}, {\n\ta: function () { return false; },\n\tb: function () { return true; }\n});\nassert(obj.a === 1);\nassert(obj.b === 20);\nassert(obj.c === 3);\nif (define.supportsDescriptors) {\n\tassert.deepEqual(Object.keys(obj), ['a', 'c']);\n\tassert.deepEqual(Object.getOwnPropertyDescriptor(obj, 'b'), {\n\t\tconfigurable: true,\n\t\tenumerable: false,\n\t\tvalue: 20,\n\t\twritable: false\n\t});\n}\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n[package-url]: https://npmjs.org/package/define-properties\n[npm-version-svg]: https://versionbadg.es/ljharb/define-properties.svg\n[deps-svg]: https://david-dm.org/ljharb/define-properties.svg\n[deps-url]: https://david-dm.org/ljharb/define-properties\n[dev-deps-svg]: https://david-dm.org/ljharb/define-properties/dev-status.svg\n[dev-deps-url]: https://david-dm.org/ljharb/define-properties#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/define-properties.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/define-properties.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/define-properties.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=define-properties\n[codecov-image]: https://codecov.io/gh/ljharb/define-properties/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/ljharb/define-properties/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/ljharb/define-properties\n[actions-url]: https://github.com/ljharb/define-properties/actions\n", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "time": {"modified": "2024-03-21T16:50:05.987Z", "created": "2015-01-04T08:34:45.318Z", "1.0.0": "2015-01-04T08:34:45.318Z", "1.0.1": "2015-01-06T22:29:12.451Z", "1.0.2": "2015-05-24T03:26:06.197Z", "1.1.0": "2015-07-02T06:52:56.628Z", "1.1.1": "2015-07-21T07:17:08.897Z", "1.1.2": "2015-10-14T22:28:41.286Z", "1.1.3": "2018-08-15T04:40:39.246Z", "1.1.4": "2022-04-15T06:15:43.631Z", "1.2.0": "2023-02-10T17:02:02.316Z", "1.2.1": "2023-09-13T06:05:13.962Z"}, "homepage": "https://github.com/ljharb/define-properties#readme", "keywords": ["Object.defineProperty", "Object.defineProperties", "object", "property descriptor", "descriptor", "define", "ES5"], "repository": {"type": "git", "url": "git://github.com/ljharb/define-properties.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/ljharb/define-properties/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"karenjli": true}}