{"_id": "defer-to-connect", "_rev": "9-e1f0d81b2aa30ac80ddcb4cd5947f421", "name": "defer-to-connect", "dist-tags": {"latest": "2.0.1"}, "versions": {"1.0.0": {"name": "defer-to-connect", "version": "1.0.0", "description": "The safe way to handle the `connect` socket event", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["socket", "connect"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "gitHead": "b8f31a50c0dbef1cd01d98ee418c1c9c8259219d", "_id": "defer-to-connect@1.0.0", "_npmVersion": "6.1.0", "_nodeVersion": "10.6.0", "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "dist": {"integrity": "sha512-1vVRtIE1M+p2xG+4bPgjc3+OUVfJZg6jFwCkEI2oktzli+lOSrHFHdzV0jsQIV499IL0f+EK+DOphojkKTJO0g==", "shasum": "9a453c479b81e6744e07dc8684c6aa5c45720fe9", "tarball": "https://registry.npmjs.org/defer-to-connect/-/defer-to-connect-1.0.0.tgz", "fileCount": 4, "unpackedSize": 2169, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbca+hCRA9TVsSAnZWagAA0nAP/jyShIFQBIdKESKp40tQ\nfowlj/WCPzcYH78RGm7ZaE5nfDOQ0Vdp+dpU0lof6Xd7xPzLqvZ+3NJIRl1C\nYiwQk+NBEGRpmCsboEKqIfbkp6+I2qSYoorPX2gSqNfvJmWHgIr1SQ2hVXDP\nCRfgesPMP+5GdDr7JB6L9JtA04JbRn4NPvxfbczbLDIlAZdrhOGf5a7goQZ4\ncXa3Kh8UAisyqQsHwUWxPhBWGZgMkjBifheG+l8rDjkhnwhlgO3ShW21xHLJ\nioWg3CX6A1Pc6jpVwMacbbQyGOaH1xee5O5HhLG3skKC0M3kL0AqiGz+fPFQ\noLLybIqWipxEzp/V9ZQihhRzxRElSdhcykqI7OXNNW4WccG/JPHzKY2ISbvZ\n+tjsslEC7Dez2Vzgf9cY4G0FHTLC0y/pxWvPOaFdOkADtHflGdaoGyTb8Yps\nA+qAw72l4GG4EsGmJd1Wk0uKTx2mtXVpuAaDl3FPXxhLqXgdAvxreKxAX8Ib\ntfkAUSZAFemrLG94sOzDDbJFZ4fTEd7ji1l6/SinxcUIwePOgebeRxBssIrt\nH080wiIbvUWftffkqZygr3SZbRVd7eINLgboT2AE7J6r/rmXTjwPiGOhzuUL\n+p/0f0ZTDL5w6b+s+sLl5zzfXmjgAjG9TCwmjAUxNfkomxe5qeP4oFy8WY5P\n05vF\r\n=8TFk\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCXTKhcDG8nSFQr9E7WoBQlRqJo2g6fNZchEHmmsGckhAIhAKqAnwCYCSsQvTvgiA4RalfFiFS4PaMLs8HqR4/F1Cvg"}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/defer-to-connect_1.0.0_1534177184826_0.13785645675145175"}, "_hasShrinkwrap": false}, "1.0.1": {"name": "defer-to-connect", "version": "1.0.1", "description": "The safe way to handle the `connect` socket event", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["socket", "connect"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "gitHead": "26f12345d47d67d82e3c1af5d0dfefca756c9e2a", "_id": "defer-to-connect@1.0.1", "_npmVersion": "6.2.0", "_nodeVersion": "10.9.0", "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "dist": {"integrity": "sha512-2e0FJesseUqQj671gvZWfUyxpnFx/5n4xleamlpCD3U6Fm5dh5qzmmLNxNhtmHF06+SYVHH8QU6FACffYTnj0Q==", "shasum": "41ec1dd670dc4c6dcbe7e54c9e44d784d025fe63", "tarball": "https://registry.npmjs.org/defer-to-connect/-/defer-to-connect-1.0.1.tgz", "fileCount": 4, "unpackedSize": 2325, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgQ27CRA9TVsSAnZWagAA6lMP/Rx26JZoiVfQqpg2mcV1\nTFfh9Q4r0ab2+sVDORWnF2oP9HijChzJ2y5fyhACxW3/CSyqFxL+gpW2wlHv\nc6LYnwU/IVt48dxSVipEHq77r4RbWSh/DHzoiGTuebUTmx9N0gIucUNvuTXL\nj0wDQHAEcnUXzL/LczAqgeJOsx+FO6EpoBEPlQveGXQAgUkOV8U7lMkz/PTW\nTnvPCqAyKT/DEAs/DnHq8RALPg50rD2zyDMNHybS/nZxvE3QwDOiLxulZhdS\nUqf4UOF2Ame/m6vkP5kGuea6ktq/Tndi8zcvPPXKFfX8HtIOG/J5hal7eL0Z\nLtPwRT176Odr72+Hk5BDlNHNbc7AaZPjiEf/oO/6E8/Z/7B4kN2ZjHgvSvoS\n/YuKOetOUmpv24BUQuJ50UasCcb6rtWrVqw9StHO3zU3/nCaCmU20NKAmrB1\nKvoa+JBuEuEC8AwSvBpLmj/Pf+4EIscemJ5eiO44ifiI5WsyqvI1xZM7yvDV\n/G6ZZWJK6gceKBv5X6RsdWo2bkdbXXAfhX4oxK5EiYtPL62K7012H+gq9/bM\nIC6FU1qL1xTblqSKaU5Xbxnw9hQ5Xaa7gRMDiYoThlua+LC93Ds+LYWJlmvM\nHQLvEB9ezN6rN+qe7ALnEQPcniTHSR/oaEB2rkPunRdaQEqQUUFgVYDzrGQf\nWGmp\r\n=IBwv\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICsn+UMRnNGhy87TgDLF2YtkHq4wfc9eqnYg7bVzgivfAiEAqSsrYJ2iNQuAk8uTn8a14ah/Qh26hCCKwy6nUwQOZ8M="}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/defer-to-connect_1.0.1_1535184315135_0.6206854802448645"}, "_hasShrinkwrap": false}, "1.0.2": {"name": "defer-to-connect", "version": "1.0.2", "description": "The safe way to handle the `connect` socket event", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["socket", "connect", "event"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/szmarczak/defer-to-connect.git"}, "bugs": {"url": "https://github.com/szmarczak/defer-to-connect/issues"}, "homepage": "https://github.com/szmarczak/defer-to-connect#readme", "gitHead": "26f12345d47d67d82e3c1af5d0dfefca756c9e2a", "_id": "defer-to-connect@1.0.2", "_npmVersion": "6.5.0-next.0", "_nodeVersion": "11.6.0", "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "dist": {"integrity": "sha512-k09hcQcTDY+cwgiwa6PYKLm3jlagNzQ+RSvhjzESOGOx+MNOuXkxTfEvPrO1IOQ81tArCFYQgi631clB70RpQw==", "shasum": "4bae758a314b034ae33902b5aac25a8dd6a8633e", "tarball": "https://registry.npmjs.org/defer-to-connect/-/defer-to-connect-1.0.2.tgz", "fileCount": 4, "unpackedSize": 2327, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcQfb+CRA9TVsSAnZWagAAqNoP/AyR7alKoClgj2lT4djS\nGY8RP4PO5OreMTqw/OfK4axn+kbOtHFIDZ6oSkGSNh4UluzPBUme7PLa9/+t\nhWwzDkP258idAzIHdz0hdg0dJU4VH379wnh4vgXrSR9pPze40tEn9LOi227s\n98KrIAiydifg7Z4IX5rswFop5JV+NqOIOEktBpd1OQfZa5jWz0yckwS64rgx\n991h97zhbbL4rjgTxS4LapM5ZZsDQmHybcj+xTjFwKUBJwuB2GkCYVdW7hs0\nHaQd8fm7uZBZVnUKgl/xkpVsgp8u2n3V4DzW6fii46WMGyjVT8jxgvoY0N2f\n2X0hs6vcGENCIbnb0t7Ju3zU4qXPy7NTYNEBM7qY6t1SKNNDtgJ9pGWNpBBD\ny5nUEXTFSJaKVJkUszr9rRuTpNRGx6eyocmjhmf0vRiMGwCbsgLBRGIQUU1f\nRThCa9wu1dkOFRTY5yTvunE+HehNZgkfMWuv+oea49xjjVQ3GReCiAD1u0Y0\nO9H/6Igr/rTGa8UUEBzVZ5RRtS9n+06NQgw2Bjys+hxih5BCDH+LClQtuPA2\nIbSHqWGd0WEaC93FTmuFsmV4NlMxAREmag5vuY4ZnpLx4eLpVzTnBZIMsDvJ\nPUDuxtLfZpLxKEe0mdRVuGMJs0bv4C9xTEBSc2/ESILGdVNvxbYNVrqsbO2s\nzozf\r\n=J1UA\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD3FdAJywZ23jOfpgEBcOFS6W/PHeIbOyNNmCk8LXI6KAIhANz+mXZSQ+2YrtsAI7TDQynruqyz6hPbamuCVHV6y0Pl"}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/defer-to-connect_1.0.2_1547826941522_0.8035673339376832"}, "_hasShrinkwrap": false}, "1.1.0": {"name": "defer-to-connect", "version": "1.1.0", "description": "The safe way to handle the `connect` socket event", "main": "dist", "scripts": {"build": "del-cli dist && tsc", "prepublishOnly": "npm run build", "test": "xo && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["socket", "connect", "event"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/szmarczak/defer-to-connect.git"}, "bugs": {"url": "https://github.com/szmarczak/defer-to-connect/issues"}, "homepage": "https://github.com/szmarczak/defer-to-connect#readme", "xo": {"extends": "xo-typescript", "extensions": ["ts"], "rules": {"ava/no-ignored-test-files": "off"}}, "devDependencies": {"@sindresorhus/tsconfig": "^0.5.0", "@types/node": "^12.12.4", "@typescript-eslint/eslint-plugin": "^1.11.0", "@typescript-eslint/parser": "^1.11.0", "ava": "^2.1.0", "coveralls": "^3.0.7", "del-cli": "^3.0.0", "eslint-config-xo-typescript": "^0.15.0", "nyc": "^14.0.0", "p-event": "^4.1.0", "ts-node": "^8.1.0", "typescript": "^3.6.4", "xo": "^0.25.3"}, "nyc": {"extension": [".ts"]}, "ava": {"babel": false, "compileEnhancements": false, "extensions": ["ts"], "require": ["ts-node/register"]}, "types": "dist", "gitHead": "8308b8500025472fc4a0131a3646d3b4579dca10", "_id": "defer-to-connect@1.1.0", "_nodeVersion": "12.10.0", "_npmVersion": "6.11.3", "dist": {"integrity": "sha512-WE2sZoctWm/v4smfCAdjYbrfS55JiMRdlY9ZubFhsYbteCK9+BvAx4YV7nPjYM6ZnX5BcoVKwfmyx9sIFTgQMQ==", "shasum": "b41bd7efa8508cef13f8456975f7a278c72833fd", "tarball": "https://registry.npmjs.org/defer-to-connect/-/defer-to-connect-1.1.0.tgz", "fileCount": 11, "unpackedSize": 10067, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdvakCCRA9TVsSAnZWagAA7vQP/iW66qVy07aYrRl4KnDp\nglPJ7OVgT0JC6XWq9xyU8Fs0LHq7hIX1HiYmYazfAo6n4sK+23QG/YeuwNAT\nMlGUucKVEey5vwrKxa71BwEb0CGueZ5objtBDWahhUAYU9vwyNlXk5Kd55p9\ni8fcYKtIzZRMxmmCcQqF4KQdzDSB9CXNCR0GPvjnp6oAOqb9YsNGGqtUBit8\nt3indi0+60gZeAPVYFaLokUYwLPCuG7R6w+jnDlCcJraxPQRqxuzsD5Fcm8d\n3dwrk9VOQaQtZ7rxq0NSN1vvLfQ1CpuK8NMdQb+WE2j3hXpu0ZNvb3nmG2hi\nNdEF2QdHFTaOZOH83TF0PKLzkxuAmuJrdV/aP/0KfcBwh9P2xY9CV1bX+Erf\nOomBSa2DR9jhvO8pH2uaAr94VRFoPEw57CD0chYsHt6Oz9U3XKgT0q80Wbb6\nl9dBbSr+C5y7tCI33ugL0LqvFUE4lw5yUZdM2Xy0OsAI1gfdwkrY5DXb8nZn\n4+KgOTa0W/VvHR9M0BHIh1AAQfLcBIDS7DrzGPiwWBpfaUgQknxNhybwtDpU\nZzZLNXftyQfyWJ/0gVpipJSZvIegpYEpsc8cBMJAt1NUAHKVeBuOV+bYnlda\n6ij56J5QcynEzJTBwd2oG9o2oE9TfgTCun6F8ebCjlCQlT+TK73S1XTzxD0M\nXlE3\r\n=OgW1\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD6NYKAOW9vCVrm9Asu/uP9W1nK/DR+5rjpFlZrG7pMqgIhAIiAcBwsXDskq7bCjFc6vzTuLCgoVVZGFwbaQTldLzUH"}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/defer-to-connect_1.1.0_1572710657931_0.10135460133503127"}, "_hasShrinkwrap": false}, "1.1.1": {"name": "defer-to-connect", "version": "1.1.1", "description": "The safe way to handle the `connect` socket event", "main": "dist", "scripts": {"build": "del-cli dist && tsc", "prepublishOnly": "npm run build", "test": "xo && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["socket", "connect", "event"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/szmarczak/defer-to-connect.git"}, "bugs": {"url": "https://github.com/szmarczak/defer-to-connect/issues"}, "homepage": "https://github.com/szmarczak/defer-to-connect#readme", "xo": {"extends": "xo-typescript", "extensions": ["ts"], "rules": {"ava/no-ignored-test-files": "off"}}, "devDependencies": {"@sindresorhus/tsconfig": "^0.5.0", "@types/node": "^12.12.4", "@typescript-eslint/eslint-plugin": "^1.11.0", "@typescript-eslint/parser": "^1.11.0", "ava": "^2.1.0", "coveralls": "^3.0.7", "create-cert": "^1.0.6", "del-cli": "^3.0.0", "eslint-config-xo-typescript": "^0.15.0", "nyc": "^14.0.0", "p-event": "^4.1.0", "ts-node": "^8.1.0", "typescript": "^3.6.4", "xo": "^0.25.3"}, "nyc": {"extension": [".ts"]}, "ava": {"babel": false, "compileEnhancements": false, "extensions": ["ts"], "require": ["ts-node/register"]}, "types": "dist", "gitHead": "dbaa61b9e7acf36d91695158ee8df632b3e3cda8", "_id": "defer-to-connect@1.1.1", "_nodeVersion": "13.0.1", "_npmVersion": "6.12.0", "dist": {"integrity": "sha512-J7thop4u3mRTkYRQ+Vpfwy2G5Ehoy82I14+14W4YMDLKdWloI9gSzRbV30s/NckQGVJtPkWNcW4oMAUigTdqiQ==", "shasum": "88ae694b93f67b81815a2c8c769aef6574ac8f2f", "tarball": "https://registry.npmjs.org/defer-to-connect/-/defer-to-connect-1.1.1.tgz", "fileCount": 11, "unpackedSize": 10992, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd57SXCRA9TVsSAnZWagAAsVMP/3xkTSWMR7PkxhCQsZDj\nmgF1D6MUTiyCp42hcrgCngiLCC9DJvwIb/5J2Lv9guq0ckheznQ3Rm3brced\n9DOma0dJwE7ExDf78dsNYkIhEWVhLbAKq2SF6ilsT5iQxGGsqEGgmHEjLqkC\nqZtnPy/si0IkhgJyhdpf9tM+6Sc6He7Xobcc6Ad6QdqoRmD3yJMRAAvJLwiQ\nCZSBYcje4Q0ond0d6ljaL1zUAS+tg9bSfV5pRfc1vIa+Rhc+GwDJk+uYQVT1\nNm6vfR64H9zQGejmy8YprpZnwkWPxsPOQbXlbZplyi7jGGHA83XhRJRVQGjk\nO06uNCP8Xp88AKfMEtwgAf0EF6dv5CYPOBABLD6YLS96qaX2zwtcVCKZjzbo\nzd2vdL1Ou7m9I9fXxLDWKLcEi7QV/5a0OI1XBCZWDC1imKIAqdLB67b38GI2\nsU5fWjx5f3RhOg1wbn34MN7Bioo352aImm7HV+sh9j4iQcrrisvUKvS+c1gT\noKu/Okk4qdMVru/H5f5HfwRNH23xN5P8PifLY+/+GUAA4uwBbYLDrTZhxKq/\nVqzR9KOCgCsBRVlPihZRZyW9GtWxoOSMAEyG5xGSgHHhsrGIlJaR/aKJwir5\nEqKXOpDW8W3g8BM7cqWW49wXKt0iy3hcqzH/LLWWCI6Ri/5NLjxwsJd2OvKw\nCJMG\r\n=Qr9R\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDavJJAQv+ZbFdLOCuG7sZ7braobr/b2l+WxcXU4M919AIhANB0cURnTV1S92pHocR9wp+XrUor1vwkKxLZ9kceaaMi"}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/defer-to-connect_1.1.1_1575466134817_0.5257928193263961"}, "_hasShrinkwrap": false}, "1.1.2": {"name": "defer-to-connect", "version": "1.1.2", "description": "The safe way to handle the `connect` socket event", "main": "dist/source", "engines": {"node": ">=10"}, "scripts": {"build": "del-cli dist && tsc", "prepare": "npm run build", "test": "xo && tsc --noEmit && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["socket", "connect", "event"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/szmarczak/defer-to-connect.git"}, "bugs": {"url": "https://github.com/szmarczak/defer-to-connect/issues"}, "homepage": "https://github.com/szmarczak/defer-to-connect#readme", "xo": {"extends": "xo-typescript", "extensions": ["ts"]}, "devDependencies": {"@ava/typescript": "^1.0.0", "@sindresorhus/tsconfig": "^0.7.0", "@types/node": "^13.5.0", "@typescript-eslint/eslint-plugin": "^2.18.0", "@typescript-eslint/parser": "^2.18.0", "ava": "^3.1.0", "coveralls": "^3.0.9", "create-cert": "^1.0.6", "del-cli": "^3.0.0", "eslint-config-xo-typescript": "^0.24.1", "nyc": "^15.0.0", "p-event": "^4.1.0", "typescript": "^3.7.5", "xo": "^0.25.3"}, "nyc": {"include": ["dist/source"], "extension": [".ts"]}, "ava": {"files": ["!**/*.d.ts"], "typescript": {"rewritePaths": {"tests/": "dist/tests/"}}}, "types": "dist/source", "gitHead": "ffd713d3f7db563d7ab459c46c891f6ff8c24ded", "_id": "defer-to-connect@1.1.2", "_nodeVersion": "13.7.0", "_npmVersion": "6.13.6", "dist": {"integrity": "sha512-pEsi2ZYLO+GroqxmPjoeKoxgZLIm4pUr6YLghbMAHXpu2vwgo910E5yfUWY39jE8zZFkhPUX4IIeWxzPxzQ1DA==", "shasum": "10aac87c9d9bd3c92b03ded222f93f83a19b6d19", "tarball": "https://registry.npmjs.org/defer-to-connect/-/defer-to-connect-1.1.2.tgz", "fileCount": 5, "unpackedSize": 5449, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeMGFeCRA9TVsSAnZWagAA0lAP/2cDbwVUJgT/Ggvp+fJp\nX8EuefHVVnf+ldwznn68vvbYUmdE7zjHUl4ra4WLo6gp0HC7Y5FSihn9LCe2\naM9Pi0aoBUUYVIvVqQF5oaZBQIACjq9ADXoM4s9Q8zJDtNsjrHdwQ2T4TmHz\n3sP4IpDxvklgwFVJQva9Cme9DKQ4aQ3kx0FsHm5GRuvt4JZR862+/4IWba2d\ngiwMWrG65ZHV1bBGceoA5l0Q//5oa3LjriO4ys6IgHOnUMqrK/76FjuLgtGT\nnyucjzxWdft7fJ5CdFydO09MJe2TjlKML5vpMc3t7KYEqtrM2tYaY+1BwAaI\nVANAySrSi8Xro9Epr5ymij+Eu51tJ8owE8X+4GOmlE7Lv2NDI+Yz2cy+4DfX\nOWHTRNCjC56sg4jRTVsAxQLyflvDUI+IdJYENhUoiaclX/+PtrkT7WJ9T32w\nV725maPmOE4jIDgrclaeWe6ubJmZQZGTsdBhzDhWQO1t7vE/nDCl7YnI9gun\nmmWGrBVVnaa4w29TrqSANfUMCAZH23A8bFlUZ3ohg51xoHmpvRPKuflZCBKk\nwBbGpe0h9bTsalWk0uPdMA5U9XV1HVaiBVc8rLR9pqhWrRnCMX1Br4HHnGJu\nbQ0u1XaFf0Dxh1oLm4QvcbTxKTCqknKUUNDnHKKrql9lRZddK6iFZxFUysbz\ne0GU\r\n=G0qj\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDZpKmbP9LElCySN3cEos47jDTWeDmX0fXYKyioq8UOBgIhAOBeajdI4A/0k2czLV+KDQLGZleSt+KXL0Q5pHeGHqr7"}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/defer-to-connect_1.1.2_1580228957627_0.7659524189994018"}, "_hasShrinkwrap": false}, "1.1.3": {"name": "defer-to-connect", "version": "1.1.3", "description": "The safe way to handle the `connect` socket event", "main": "dist", "scripts": {"build": "del-cli dist && tsc", "prepublishOnly": "npm run build", "test": "xo && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["socket", "connect", "event"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/szmarczak/defer-to-connect.git"}, "bugs": {"url": "https://github.com/szmarczak/defer-to-connect/issues"}, "homepage": "https://github.com/szmarczak/defer-to-connect#readme", "xo": {"extends": "xo-typescript", "extensions": ["ts"], "rules": {"ava/no-ignored-test-files": "off"}}, "devDependencies": {"@sindresorhus/tsconfig": "^0.5.0", "@types/node": "^12.12.4", "@typescript-eslint/eslint-plugin": "^1.11.0", "@typescript-eslint/parser": "^1.11.0", "ava": "^2.1.0", "coveralls": "^3.0.7", "create-cert": "^1.0.6", "del-cli": "^3.0.0", "eslint-config-xo-typescript": "^0.15.0", "nyc": "^14.0.0", "p-event": "^4.1.0", "ts-node": "^8.1.0", "typescript": "^3.6.4", "xo": "^0.25.3"}, "nyc": {"extension": [".ts"]}, "ava": {"babel": false, "compileEnhancements": false, "extensions": ["ts"], "require": ["ts-node/register"], "files": ["!dist/tests/test.d.ts"]}, "types": "dist", "gitHead": "251c0a7b0ddfb7147389169bd7c6c41836d03f9c", "_id": "defer-to-connect@1.1.3", "_nodeVersion": "10.17.0", "_npmVersion": "6.11.3", "dist": {"integrity": "sha512-0ISdNousHvZT2EiFlZeZAHBUvSxmKswVCEf8hW7KWgG4a8MVEu/3Vb6uWYozkjylyCxe0JBIiRB1jV45S70WVQ==", "shasum": "331ae050c08dcf789f8c83a7b81f0ed94f4ac591", "tarball": "https://registry.npmjs.org/defer-to-connect/-/defer-to-connect-1.1.3.tgz", "fileCount": 5, "unpackedSize": 5451, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeMHQvCRA9TVsSAnZWagAANCIQAJLuF5RSyLDTSSkwqeug\nICOZV1s+XPqvg/K02m8u/tIiKYDGv7jIk5I8qrTU+eyOwGc56mg6HJgStJH3\noI7tCmlubA1kHjSmLodZXw+wWxIcTvSpM8TJrAdzQOecPYMOgoT3e9WGqgeg\nwsGCm9Mv6h2nBeeJB3WwWuvkkljt03GpnmGFZkzUfJUD5mKgnwFcdmnVPEH2\nVlBBcqwupMvPoIgjcPGfEf2PpmZJEgN1KvQ69Mj1ycSaG6edJ1WrevcXLOBZ\nvTCtMLaMGirEndNjk6td2LRbcb0GrdETUUy6LcVzIugtg6r/EX2xm4D5W5rn\nm/x6ZeIImRaLqx+ihG8VjLkAm3kqEeR+nFbNJ75yP1C/qD+hgUR7gFwByHPS\nKSqel3nCWDIXub5n0lF9ta/NHBjHe6J9td8M2w1tqAjJf0QfCKVfLKd+OsZA\nultWzG+s82xWQsE3N7fav9i0CgsQlE4PywRvCTgIJV6biuah4mShkB4KOlYs\npXWV188I7+fKA9kQyzgVLRjqthAodre/4YKVTX7DfxkwpFm/GKFjG72uU/ob\nUiD9yljwZE1MPA8wvEsMqJda5udvQ1eol2zleirgQ+oGFhjtHjMj1b7+F5Ca\ne4hKF/zc0Wr9Mf0pBwmUq/hnZNst6Vb3LM0bHkm/9/F2Oc5K24eLwIiusqQ5\nwgW/\r\n=poJa\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDXQvhXn+YJXXHQ8VDb5GS7mxV18P6glYREjJRe5DH8tAIgTPU08/QsLIPVdUxKjbXbfEH5D074JSu+tUqsgedUSyU="}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/defer-to-connect_1.1.3_1580233774908_0.7156333163348343"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "defer-to-connect", "version": "2.0.0", "description": "The safe way to handle the `connect` socket event", "main": "dist/source", "engines": {"node": ">=10"}, "scripts": {"build": "del-cli dist && tsc", "prepare": "npm run build", "test": "xo && tsc --noEmit && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["socket", "connect", "event"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/szmarczak/defer-to-connect.git"}, "bugs": {"url": "https://github.com/szmarczak/defer-to-connect/issues"}, "homepage": "https://github.com/szmarczak/defer-to-connect#readme", "xo": {"extends": "xo-typescript", "extensions": ["ts"]}, "devDependencies": {"@ava/typescript": "^1.0.0", "@sindresorhus/tsconfig": "^0.7.0", "@types/node": "^13.5.0", "@typescript-eslint/eslint-plugin": "^2.18.0", "@typescript-eslint/parser": "^2.18.0", "ava": "^3.1.0", "coveralls": "^3.0.9", "create-cert": "^1.0.6", "del-cli": "^3.0.0", "eslint-config-xo-typescript": "^0.24.1", "nyc": "^15.0.0", "p-event": "^4.1.0", "typescript": "^3.7.5", "xo": "^0.25.3"}, "nyc": {"include": ["dist/source"], "extension": [".ts"]}, "ava": {"files": ["!**/*.d.ts"], "typescript": {"rewritePaths": {"tests/": "dist/tests/"}}}, "types": "dist/source", "gitHead": "7825344cf0b9a296189d1718031bfb3bee468410", "_id": "defer-to-connect@2.0.0", "_nodeVersion": "10.17.0", "_npmVersion": "6.11.3", "dist": {"integrity": "sha512-bYL2d05vOSf1JEZNx5vSAtPuBMkX8K9EUutg7zlKvTqKXHt7RhWJFbmd7qakVuf13i+IkGmp6FwSsONOf6VYIg==", "shasum": "83d6b199db041593ac84d781b5222308ccf4c2c1", "tarball": "https://registry.npmjs.org/defer-to-connect/-/defer-to-connect-2.0.0.tgz", "fileCount": 5, "unpackedSize": 5456, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeMHWGCRA9TVsSAnZWagAAoY4P/0zRtlOvThzVcme9trDn\nPKbrM3QsJJ2U5M1DA9//YjZkyuHMhyY0EmFeQ2R7Xwmt3/0z+6OEosZridg+\nqXlrkzQOlvpyE6GyT88Vs+KZY99OvpY63oKA4YL6oKq85/TgBgWP0xaTq/8X\n0BR4VCUFdudB5shYi7HSOA5+xEvbbr2Xdl/HbZeVr9ybIwxvhe3ZZieVYo2x\nVImzgKZK4iipnIRt3YDs1rCy/WnkHg07dSN6wAIzXp+dulBIFo7Og87n+5jb\nMvWPTgTt2vWB9JXgFBxuvWlxnFh7uS8RWxGjghHv4bR6JwPKcp8Pv9/xBv+C\nys4EkEklHesQvT2G5V6wnwDNeoeQ0JWu7CEHKXuTOzUVRlY7ytIs7mGynCD3\nnCKxkhWBPW2rkmQBPYTLrnSZvepQ487UTzAVFLiY2gc3+XSrFz+oDQICDbDT\nI+6Zep4B5zMjDC+HBjvf98OUgle6qu1HcDtrVfRglsUZthpAdCTQbQHCHSx5\nfSJRiOpmVZrbNuB07iD+zDMXsJqPien7R2r2j4BAxn+UPwangUYsL9gxsYSX\nQVX3dfmtXx3h1w9bP1WCgVJJm0ABIAJIeUQlwOSb+knq5McWW1EqdLvi+ZKD\nxrw22JmUiuZqnTyKgYKRVaqzxQt26fW1e2R7wFpSKkBIjAAhO/1uqRnfSv+a\nAdJq\r\n=0mz8\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDouuiAHhr34MpgEHRSl2zxUWaDoRTpmMviwe8k8Z2DRAiEAmqkMjem+jRWPJcEiqn/oLhwVJ1a1M5ux2QCRpEqjt54="}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/defer-to-connect_2.0.0_1580234117999_0.45451289831749375"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "defer-to-connect", "version": "2.0.1", "description": "The safe way to handle the `connect` socket event", "main": "dist/source", "engines": {"node": ">=10"}, "scripts": {"build": "del-cli dist && tsc", "prepare": "npm run build", "test": "xo && tsc --noEmit && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["socket", "connect", "event"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/szmarczak/defer-to-connect.git"}, "bugs": {"url": "https://github.com/szmarczak/defer-to-connect/issues"}, "homepage": "https://github.com/szmarczak/defer-to-connect#readme", "xo": {"extends": "xo-typescript", "extensions": ["ts"]}, "devDependencies": {"@ava/typescript": "^1.1.0", "@sindresorhus/tsconfig": "^0.7.0", "@types/node": "^13.5.0", "@typescript-eslint/eslint-plugin": "^2.18.0", "@typescript-eslint/parser": "^2.18.0", "ava": "^3.2.0", "coveralls": "^3.0.9", "create-cert": "^1.0.6", "del-cli": "^3.0.0", "eslint-config-xo-typescript": "^0.24.1", "nyc": "^15.0.0", "p-event": "^4.1.0", "typescript": "^3.7.5", "xo": "^0.25.3"}, "nyc": {"include": ["dist/source"], "extension": [".ts"]}, "ava": {"typescript": {"rewritePaths": {"tests/": "dist/tests/"}}}, "types": "dist/source/index.d.ts", "gitHead": "d808957298521b7bb142a3e45f1c13016b26c762", "_id": "defer-to-connect@2.0.1", "_nodeVersion": "15.7.0", "_npmVersion": "7.4.3", "dist": {"integrity": "sha512-4tvttepXG1VaYGrRibk5EwJd1t4udunSOVMdLSAL6mId1ix438oPwPZMALY41FCijukO1L0twNcGsdzS7dHgDg==", "shasum": "8016bdb4143e4632b77a3449c6236277de520587", "tarball": "https://registry.npmjs.org/defer-to-connect/-/defer-to-connect-2.0.1.tgz", "fileCount": 5, "unpackedSize": 5444, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNuemCRA9TVsSAnZWagAAdugP/1FN7iuvQg6E0aGzr7ZK\n0smavlPfhlWnqqt44SJZjKs0vEpibBku4J8WyHk2TEWg4O41NSBW0pn2usBJ\namMCUepbKaUuWZbSP84pKK5X4t0MD8h9nlZ26nEKfV/srGpbJCDIozqJRbUP\nieYtAYAKf0NPMpfYJg1WPelSV4FnCtz9sXu4PAcK7ADRUXeICcvyXUp0Z5sV\nREoY8t0VwfcbhuZ4/QqXui2Z26VDK/K4N9bmjg5Qg7dlV44WJZhUAa8RC5Sr\n6e1Ribzc9sf05p+fNnvizi3jtAGknwkMAn23Z+ldvhP+8XNZ39qN/slX3r4n\nTPxdY9rvs/AWS3mK1cXskhWqvTFRXZl668MayNDiIk99vHL3LbTmkgjxGUdM\nErp0JWJYcyIo+saqVL2UTKF0X1pApXhiDKkXufj87aybjHI5dJW2pi4BiOHd\nNdtiT71rOpAnLILanwHIy1Du6B2LlIJdVO7ndB7i4MVkzt7wz6tLJvUJuzyA\n77EW6Wq04o3k3SDrgpXnZZbCj6L/mbfQdF1zGsR5nfL+dd69Wff6Xs1KfjPB\naCgWQCVBqylHW7xLBZxxTrbZNGefFfK0ypPZZamy41OJ9AGwaBrT/9UHbLmZ\nM9b/GCVt1jTZk7S0/c5qFpFasAu66pvh1nVKXC4hFJoj+8+vq8J8UUB46Wrh\nC+b5\r\n=RccB\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDatFJj9lo2nQhxB7LSWTVmrxjByJ5UMkmPhUjnrAyIZwIhANfcco/NUd6H9n2/NotbvR4RXIt3GeXk39J2kF1sB4KM"}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/defer-to-connect_2.0.1_1614210982543_0.01936876810477761"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-08-13T16:19:44.826Z", "1.0.0": "2018-08-13T16:19:44.937Z", "modified": "2022-04-28T08:39:03.442Z", "1.0.1": "2018-08-25T08:05:15.317Z", "1.0.2": "2019-01-18T15:55:41.680Z", "1.1.0": "2019-11-02T16:04:18.060Z", "1.1.1": "2019-12-04T13:28:55.011Z", "1.1.2": "2020-01-28T16:29:17.743Z", "1.1.3": "2020-01-28T17:49:35.042Z", "2.0.0": "2020-01-28T17:55:18.173Z", "2.0.1": "2021-02-24T23:56:22.692Z"}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "description": "The safe way to handle the `connect` socket event", "keywords": ["socket", "connect", "event"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "readme": "# defer-to-connect\n\n> The safe way to handle the `connect` socket event\n\n[![Coverage Status](https://coveralls.io/repos/github/szmarczak/defer-to-connect/badge.svg?branch=master)](https://coveralls.io/github/szmarczak/defer-to-connect?branch=master)\n\nOnce you receive the socket, it may be already connected (or disconnected).<br>\nTo avoid checking that, use `defer-to-connect`. It'll do that for you.\n\n## Usage\n\n```js\nconst deferToConnect = require('defer-to-connect');\n\ndeferToConnect(socket, () => {\n    console.log('Connected!');\n});\n```\n\n## API\n\n### deferToConnect(socket, connectListener)\n\nCalls `connectListener()` when connected.\n\n### deferToConnect(socket, listeners)\n\n#### listeners\n\nAn object representing `connect`, `secureConnect` and `close` properties.\n\nCalls `connect()` when the socket is connected.<br>\nCalls `secureConnect()` when the socket is securely connected.<br>\nCalls `close()` when the socket is destroyed.\n\n## License\n\nMIT\n", "readmeFilename": "README.md", "homepage": "https://github.com/szmarczak/defer-to-connect#readme", "repository": {"type": "git", "url": "git+https://github.com/szmarczak/defer-to-connect.git"}, "bugs": {"url": "https://github.com/szmarczak/defer-to-connect/issues"}}