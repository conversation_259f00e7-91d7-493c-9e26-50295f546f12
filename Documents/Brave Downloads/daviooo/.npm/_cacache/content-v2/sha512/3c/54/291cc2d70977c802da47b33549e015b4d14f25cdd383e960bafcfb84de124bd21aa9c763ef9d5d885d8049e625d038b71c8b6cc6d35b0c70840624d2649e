{"_id": "set-blocking", "_rev": "5-ba6f0bb5d39fe53d5522291e3d478d77", "name": "set-blocking", "description": "set blocking stdio and stderr ensuring that terminal output does not truncate", "dist-tags": {"latest": "2.0.0"}, "versions": {"1.0.0": {"name": "set-blocking", "version": "1.0.0", "description": "set blocking stdio and stderr ensuring that terminal output does not truncate", "main": "index.js", "scripts": {"pretest": "standard", "test": "nyc mocha ./test/*.js", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "repository": {"type": "git", "url": "git+https://github.com/yargs/set-blocking.git"}, "keywords": ["flush", "terminal", "blocking", "shim", "stdio", "stderr"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/yargs/set-blocking/issues"}, "homepage": "https://github.com/yargs/set-blocking#readme", "devDependencies": {"chai": "^3.5.0", "coveralls": "^2.11.9", "mocha": "^2.4.5", "nyc": "^6.4.4", "standard": "^7.0.1", "standard-version": "^2.2.1"}, "files": ["index.js", "LICENSE.txt"], "gitHead": "7d0c45e56bbf7fe2634b71df47c5003b3ae1008a", "_id": "set-blocking@1.0.0", "_shasum": "cd5e5d938048df1ac92dfe92e1f16add656f5ec5", "_from": ".", "_npmVersion": "2.7.5", "_nodeVersion": "0.10.36", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"shasum": "cd5e5d938048df1ac92dfe92e1f16add656f5ec5", "tarball": "https://registry.npmjs.org/set-blocking/-/set-blocking-1.0.0.tgz", "integrity": "sha512-iBe7pLhQGlNw7om7kiwfHyWAZkD2gR9yTHu66xvjxQYTrJw73z2sxcBkKeyZQ/RrzUipgpxaATrtJlw3ezbnUA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBIBRxZXJTOAGXXXikBWdnyzlAY+gAuQxyxv0K3/VDsoAiEAqyYaNFIQSzXNY+Mjbwqi1ny/kIoQGcolhmYRqWkO700="}]}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/set-blocking-1.0.0.tgz_1463262253151_0.16861064941622317"}}, "2.0.0": {"name": "set-blocking", "version": "2.0.0", "description": "set blocking stdio and stderr ensuring that terminal output does not truncate", "main": "index.js", "scripts": {"pretest": "standard", "test": "nyc mocha ./test/*.js", "coverage": "nyc report --reporter=text-lcov | coveralls", "version": "standard-version"}, "repository": {"type": "git", "url": "git+https://github.com/yargs/set-blocking.git"}, "keywords": ["flush", "terminal", "blocking", "shim", "stdio", "stderr"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/yargs/set-blocking/issues"}, "homepage": "https://github.com/yargs/set-blocking#readme", "devDependencies": {"chai": "^3.5.0", "coveralls": "^2.11.9", "mocha": "^2.4.5", "nyc": "^6.4.4", "standard": "^7.0.1", "standard-version": "^2.2.1"}, "files": ["index.js", "LICENSE.txt"], "gitHead": "7eec10577b5fff264de477ba3b9d07f404946eff", "_id": "set-blocking@2.0.0", "_shasum": "045f9782d011ae9a6803ddd382b24392b3d890f7", "_from": ".", "_npmVersion": "2.11.3", "_nodeVersion": "0.12.7", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"shasum": "045f9782d011ae9a6803ddd382b24392b3d890f7", "tarball": "https://registry.npmjs.org/set-blocking/-/set-blocking-2.0.0.tgz", "integrity": "sha512-KiKBS8AnWGEyLzofFfmvKwpdPzqiy16LvQfK3yv/fVH7Bj13/wl3JSR1J+rfgRE9q7xUJK4qvgS8raSOeLUehw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC7PKDtLSfgEG/qqzrcxx2Khs2CK9p75Arv9+g4Bdt8aAiEApBbZwIIifoIUzRmqNhH90HEOVMzYU/PkU6J14r2GWT8="}]}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/set-blocking-2.0.0.tgz_1463525966987_0.5456729622092098"}}}, "readme": "# set-blocking\n\n[![Build Status](https://travis-ci.org/yargs/set-blocking.svg)](https://travis-ci.org/yargs/set-blocking)\n[![NPM version](https://img.shields.io/npm/v/set-blocking.svg)](https://www.npmjs.com/package/set-blocking)\n[![Coverage Status](https://coveralls.io/repos/yargs/set-blocking/badge.svg?branch=)](https://coveralls.io/r/yargs/set-blocking?branch=master)\n[![Standard Version](https://img.shields.io/badge/release-standard%20version-brightgreen.svg)](https://github.com/conventional-changelog/standard-version)\n\nset blocking `stdio` and `stderr` ensuring that terminal output does not truncate.\n\n```js\nconst setBlocking = require('set-blocking')\nsetBlocking(true)\nconsole.log(someLargeStringToOutput)\n```\n\n## Historical Context/Word of Warning\n\nThis was created as a shim to address the bug discussed in [node #6456](https://github.com/nodejs/node/issues/6456). This bug crops up on\nnewer versions of Node.js (`0.12+`), truncating terminal output.\n\nYou should be mindful of the side-effects caused by using `set-blocking`:\n\n* if your module sets blocking to `true`, it will effect other modules\n  consuming your library. In [yargs](https://github.com/yargs/yargs/blob/master/yargs.js#L653) we only call\n  `setBlocking(true)` once we already know we are about to call `process.exit(code)`.\n* this patch will not apply to subprocesses spawned with `isTTY = true`, this is\n  the [default `spawn()` behavior](https://nodejs.org/api/child_process.html#child_process_child_process_spawn_command_args_options).\n\n## License\n\nISC\n", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "time": {"modified": "2022-06-26T18:18:15.201Z", "created": "2016-05-14T21:44:13.754Z", "1.0.0": "2016-05-14T21:44:13.754Z", "2.0.0": "2016-05-17T22:59:27.619Z"}, "homepage": "https://github.com/yargs/set-blocking#readme", "keywords": ["flush", "terminal", "blocking", "shim", "stdio", "stderr"], "repository": {"type": "git", "url": "git+https://github.com/yargs/set-blocking.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/yargs/set-blocking/issues"}, "license": "ISC", "readmeFilename": "README.md", "users": {"usex": true}}