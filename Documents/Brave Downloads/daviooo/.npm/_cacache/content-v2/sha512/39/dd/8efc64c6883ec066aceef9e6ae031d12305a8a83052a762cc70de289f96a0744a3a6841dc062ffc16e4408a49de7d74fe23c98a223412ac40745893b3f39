{"_id": "tiny-emitter", "_rev": "56-13c3c53ea145acc320cc9e1770004e55", "name": "tiny-emitter", "description": "A tiny (less than 1k) event emitter library", "dist-tags": {"latest": "2.1.0"}, "versions": {"0.1.0": {"name": "tiny-emitter", "version": "0.1.0", "description": "A tiny (less than 500 bytes) event emitter library", "main": "index.js", "scripts": {"test": "node test/index.js | node_modules/.bin/tspec"}, "repository": {"type": "git", "url": "https://github.com/scottcorgan/tiny-emitter.git"}, "keywords": ["event", "emitter", "pubsub", "tiny", "events", "bind"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/scottcorgan/tiny-emitter/issues"}, "devDependencies": {"tape": "~2.3.2", "tap-spec": "~0.1.1"}, "_id": "tiny-emitter@0.1.0", "dist": {"shasum": "b8f6a6e6b3363a4e31c619c92945019cb77a8f16", "tarball": "https://registry.npmjs.org/tiny-emitter/-/tiny-emitter-0.1.0.tgz", "integrity": "sha512-uI5z0ohdGm4s3Za7tBgfMhiiNTOqXIMzwdXG9WDzosN8Iq89WmH+oFERtdPGzs1a5dURZb6Mck9DohBS5YKRug==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBXrXS+v2dpU9yXvv9fyAIy49enZ7qNnR+ABQjmcjtrwAiEAu291HKMxKNHVCjNvEfw6HNjevYHwf9hSgbFaKql3ln0="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "scottcorgan", "email": "<EMAIL>"}, "maintainers": [{"name": "scottcorgan", "email": "<EMAIL>"}], "directories": {}}, "0.1.1": {"name": "tiny-emitter", "version": "0.1.1", "description": "A tiny (less than 500 bytes) event emitter library", "main": "index.js", "scripts": {"test": "node test/index.js | node_modules/.bin/tspec"}, "repository": {"type": "git", "url": "https://github.com/scottcorgan/tiny-emitter.git"}, "keywords": ["event", "emitter", "pubsub", "tiny", "events", "bind"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/scottcorgan/tiny-emitter/issues"}, "devDependencies": {"tape": "~2.3.2", "tap-spec": "~0.1.1"}, "_id": "tiny-emitter@0.1.1", "dist": {"shasum": "6789f05e85acabb64e27fc3151c2e6ee13838bfd", "tarball": "https://registry.npmjs.org/tiny-emitter/-/tiny-emitter-0.1.1.tgz", "integrity": "sha512-02jm2CAuT7IMkbvOyU/Arv/WoPRCyBtlU+Sjp5n/yJC1zVtq5Uk4HXE3tTqVECIrQj3eWIO7YqIKjqtSc/ZT5g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGZHttbP6kIsM0itSFX13qm89h2HiEPvVcs7F9vJtYR+AiB/NPJKXuQcOpsT5mZ7aqBAWoRGEX0i66+PdqgzSy/LnA=="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "scottcorgan", "email": "<EMAIL>"}, "maintainers": [{"name": "scottcorgan", "email": "<EMAIL>"}], "directories": {}}, "0.1.2": {"name": "tiny-emitter", "version": "0.1.2", "description": "A tiny (less than 500 bytes) event emitter library", "main": "index.js", "scripts": {"test": "node test/index.js | node_modules/.bin/tspec"}, "repository": {"type": "git", "url": "https://github.com/scottcorgan/tiny-emitter.git"}, "keywords": ["event", "emitter", "pubsub", "tiny", "events", "bind"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/scottcorgan/tiny-emitter/issues"}, "devDependencies": {"tape": "~2.3.2", "tap-spec": "~0.1.1"}, "_id": "tiny-emitter@0.1.2", "dist": {"shasum": "7adfab187bf4a0cda362c88d26290e3df906419c", "tarball": "https://registry.npmjs.org/tiny-emitter/-/tiny-emitter-0.1.2.tgz", "integrity": "sha512-Z7C4vm6QFiA1QEgRKmLuM8MLmQDvJnDOULJ1/WXdCG8GA4gdbO2PX9dMi0hm/nqRKY2PK3ejTwMYF1MpyO1n2Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBj+xkUbmc8HkGOQfeBK+DtqgHXKcmh2ZH7cq7Cw+LKaAiEA09b+onB0kMklMs7aZbl8r+0VY4PttdcvIVx/SutMq+E="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "scottcorgan", "email": "<EMAIL>"}, "maintainers": [{"name": "scottcorgan", "email": "<EMAIL>"}], "directories": {}}, "0.1.3": {"name": "tiny-emitter", "version": "0.1.3", "description": "A tiny (less than 500 bytes) event emitter library", "main": "index.js", "scripts": {"test": "node test/index.js | node_modules/.bin/tspec"}, "repository": {"type": "git", "url": "https://github.com/scottcorgan/tiny-emitter.git"}, "keywords": ["event", "emitter", "pubsub", "tiny", "events", "bind"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/scottcorgan/tiny-emitter/issues"}, "devDependencies": {"tape": "~2.3.2", "tap-spec": "~0.1.1"}, "_id": "tiny-emitter@0.1.3", "dist": {"shasum": "08c97c33b87f82b3ff05715e98b18da6417f3019", "tarball": "https://registry.npmjs.org/tiny-emitter/-/tiny-emitter-0.1.3.tgz", "integrity": "sha512-G2eDUlQnp8wEv+5M3d6IDECN7woZ9ZWTwK30C6TpFkF0XuQHrQCr71g1qt3eBUMOEfnewoaVIo3LxCXS5cyGKw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDTZOXA+TNhe1NeU50kidhJdODmPttk6gsMS3vfecFbVgIgFuJAhQempZLUFKBSBwDmuY+ZxJHjqe7bc2TtlWNgdxc="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "scottcorgan", "email": "<EMAIL>"}, "maintainers": [{"name": "scottcorgan", "email": "<EMAIL>"}], "directories": {}}, "0.1.4": {"name": "tiny-emitter", "version": "0.1.4", "description": "A tiny (less than 1k) event emitter library", "main": "index.js", "scripts": {"test": "node test/index.js | node_modules/.bin/tspec", "bundle": "node_modules/.bin/browserify index.js > dist/tinyemitter.js -s TinyEmitter && echo 'Bundled'", "minify": "node_modules/.bin/uglifyjs dist/tinyemitter.js -o dist/tinyemitter.min.js && echo 'Minified'", "build": "npm test && npm run bundle && npm run minify"}, "repository": {"type": "git", "url": "https://github.com/scottcorgan/tiny-emitter.git"}, "keywords": ["event", "emitter", "pubsub", "tiny", "events", "bind"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/scottcorgan/tiny-emitter/issues"}, "devDependencies": {"tape": "~2.3.2", "tap-spec": "~0.1.1", "browserify": "~3.18.0", "uglify-js": "~2.4.8"}, "_id": "tiny-emitter@0.1.4", "dist": {"shasum": "75f04c4ce612dc5a5474acb16d0ce7019d12f039", "tarball": "https://registry.npmjs.org/tiny-emitter/-/tiny-emitter-0.1.4.tgz", "integrity": "sha512-JBjTv40sAUS5TqnVbwEXEwCX/wBqRUAyjElWfrpPD52Xcc2P6C8d12Go5LKnG7jm4m4Yun69xep5SWc015L4AQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEJQO2LVswJgvWa6fLHOqtP8r5Dq82+FOcNqHSyaJPzYAiEA4i/B7QWTkq6qi7hYJc8Rtrj3wkVM9AuJg+bpoMCw+iw="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "scottcorgan", "email": "<EMAIL>"}, "maintainers": [{"name": "scottcorgan", "email": "<EMAIL>"}], "directories": {}}, "0.1.5": {"name": "tiny-emitter", "version": "0.1.5", "description": "A tiny (less than 1k) event emitter library", "main": "index.js", "scripts": {"test": "node test/index.js | node_modules/.bin/tspec", "bundle": "node_modules/.bin/browserify index.js > dist/tinyemitter.js -s TinyEmitter && echo 'Bundled'", "minify": "node_modules/.bin/uglifyjs dist/tinyemitter.js -o dist/tinyemitter.min.js -m && echo 'Minified'", "build": "npm test && npm run bundle && npm run minify", "size": "node_modules/.bin/uglifyjs index.js -o minified.js -m && ls -l && rm minified.js"}, "repository": {"type": "git", "url": "https://github.com/scottcorgan/tiny-emitter.git"}, "keywords": ["event", "emitter", "pubsub", "tiny", "events", "bind"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/scottcorgan/tiny-emitter/issues"}, "devDependencies": {"tape": "~2.3.2", "tap-spec": "~0.1.1", "browserify": "~3.18.0", "uglify-js": "~2.4.8"}, "_id": "tiny-emitter@0.1.5", "dist": {"shasum": "650dedb3bd5604b31ee19528d11342903ce3bbdd", "tarball": "https://registry.npmjs.org/tiny-emitter/-/tiny-emitter-0.1.5.tgz", "integrity": "sha512-eXAZAcbcZHult4LkoqL4qjmj27TKNX1VEJbvihzJ7n+ALyUvXFpeDi1oZ29pr6lx4I32qYeDSxCs31Bb9B/HRg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDxpsBSTKs/ZqWNXXBoy/Eld2hbuT9suGsOBenhkDAZrQIhAKyw0Lbg1MEWgLaAQ0Dk0pwFSKHGCp856hEtB//PwN8g"}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "scottcorgan", "email": "<EMAIL>"}, "maintainers": [{"name": "scottcorgan", "email": "<EMAIL>"}], "directories": {}}, "0.1.6": {"name": "tiny-emitter", "version": "0.1.6", "description": "A tiny (less than 1k) event emitter library", "main": "index.js", "scripts": {"test": "node_modules/.bin/testling | node_modules/.bin/tspec", "bundle": "node_modules/.bin/browserify index.js > dist/tinyemitter.js -s TinyEmitter && echo 'Bundled'", "minify": "node_modules/.bin/uglifyjs dist/tinyemitter.js -o dist/tinyemitter.min.js -m && echo 'Minified'", "build": "npm test && npm run bundle && npm run minify", "size": "node_modules/.bin/uglifyjs index.js -o minified.js -m && ls -l && rm minified.js"}, "repository": {"type": "git", "url": "https://github.com/scottcorgan/tiny-emitter.git"}, "keywords": ["event", "emitter", "pubsub", "tiny", "events", "bind"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/scottcorgan/tiny-emitter/issues"}, "devDependencies": {"tape": "~2.3.2", "tap-spec": "~0.1.3", "browserify": "~3.18.0", "uglify-js": "~2.4.8", "testling": "~1.5.6"}, "testling": {"files": ["test/index.js"], "browsers": ["iexplore/10.0", "iexplore/9.0", "firefox/16..latest", "chrome/22..latest", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "_id": "tiny-emitter@0.1.6", "dist": {"shasum": "96b9374ec759e3ebc53f304084606e38491806da", "tarball": "https://registry.npmjs.org/tiny-emitter/-/tiny-emitter-0.1.6.tgz", "integrity": "sha512-Bc6+0QW0OKd9yNMMWJrlDVWHJ3NGO0OGDCFa07jnWNvS2PDUGnmaii4d9opJjU7BmQc8Zse3LpuwlYPhA/T0hA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDikKE4Mm8LTBwZ8J12Yi2xpBHpgyhQCZPLfGhVcufISAIhANijeZ001HMcWF0ienWce60ivrSMboy9k0r/02wnOLvX"}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "scottcorgan", "email": "<EMAIL>"}, "maintainers": [{"name": "scottcorgan", "email": "<EMAIL>"}], "directories": {}}, "0.1.7": {"name": "tiny-emitter", "version": "0.1.7", "description": "A tiny (less than 1k) event emitter library", "main": "index.js", "scripts": {"test": "node_modules/.bin/testling | node_modules/.bin/tspec", "bundle": "node_modules/.bin/browserify index.js > dist/tinyemitter.js -s TinyEmitter && echo 'Bundled'", "minify": "node_modules/.bin/uglifyjs dist/tinyemitter.js -o dist/tinyemitter.min.js -m && echo 'Minified'", "build": "npm test && npm run bundle && npm run minify", "size": "node_modules/.bin/uglifyjs index.js -o minified.js -m && ls -l && rm minified.js"}, "repository": {"type": "git", "url": "https://github.com/scottcorgan/tiny-emitter.git"}, "keywords": ["event", "emitter", "pubsub", "tiny", "events", "bind"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/scottcorgan/tiny-emitter/issues"}, "devDependencies": {"tape": "~2.3.2", "tap-spec": "~0.1.3", "browserify": "~3.18.0", "uglify-js": "~2.4.8", "testling": "~1.5.6"}, "testling": {"files": ["test/index.js"], "browsers": ["iexplore/10.0", "iexplore/9.0", "firefox/16..latest", "chrome/22..latest", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "_id": "tiny-emitter@0.1.7", "dist": {"shasum": "78a00bee6e55a5faf6f4b639d19ddd87ea6bdc11", "tarball": "https://registry.npmjs.org/tiny-emitter/-/tiny-emitter-0.1.7.tgz", "integrity": "sha512-3qXnUkbYNpLFjAtP6UVlTKv/JCjo+kgMe87gUVhYaKrMuuWfgRe8PBZTPjentNW07EIkeQMgTxmT5MvQe8BOsw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCmGJM5TX7OLmdB/4KEqLFML3ex7YzF/G3dmPEXO4ha7gIhAOsl1S2JlvUSSHfpTZcV8GY7kTJO1nYYLDpjvD8Ofkcj"}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "scottcorgan", "email": "<EMAIL>"}, "maintainers": [{"name": "scottcorgan", "email": "<EMAIL>"}], "directories": {}}, "0.1.8": {"name": "tiny-emitter", "version": "0.1.8", "description": "A tiny (less than 1k) event emitter library", "main": "index.js", "scripts": {"test": "node_modules/.bin/testling | node_modules/.bin/tspec", "bundle": "node_modules/.bin/browserify index.js > dist/tinyemitter.js -s TinyEmitter && echo 'Bundled'", "minify": "node_modules/.bin/uglifyjs dist/tinyemitter.js -o dist/tinyemitter.min.js -m && echo 'Minified'", "build": "npm test && npm run bundle && npm run minify", "size": "node_modules/.bin/uglifyjs index.js -o minified.js -m && ls -l && rm minified.js"}, "repository": {"type": "git", "url": "https://github.com/scottcorgan/tiny-emitter.git"}, "keywords": ["event", "emitter", "pubsub", "tiny", "events", "bind"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/scottcorgan/tiny-emitter/issues"}, "devDependencies": {"tape": "~2.3.2", "tap-spec": "~0.1.3", "browserify": "~3.18.0", "uglify-js": "~2.4.8", "testling": "~1.5.6"}, "testling": {"files": ["test/index.js"], "browsers": ["iexplore/10.0", "iexplore/9.0", "firefox/16..latest", "chrome/22..latest", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "_id": "tiny-emitter@0.1.8", "dist": {"shasum": "1aff4920502b3619717c1957cbe8d40e6e053dd1", "tarball": "https://registry.npmjs.org/tiny-emitter/-/tiny-emitter-0.1.8.tgz", "integrity": "sha512-ozmnf8DfIgHOVTnQfBmJATpLMw8P2XmFK/RC+44gIGyRWj9tI94eElmYMVMKu9xnfPjHbanUxQmRZ5T01JktXA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBYBptyD1B2tEoWpzD8ukjIaJRSay3W381IVoCNWmgSiAiEA/jAZassLffExf5cHGkU05qNBBD15kwwbcvq9oFCZ6WY="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "scottcorgan", "email": "<EMAIL>"}, "maintainers": [{"name": "scottcorgan", "email": "<EMAIL>"}], "directories": {}}, "0.1.9": {"name": "tiny-emitter", "version": "0.1.9", "description": "A tiny (less than 1k) event emitter library", "main": "index.js", "scripts": {"test": "node_modules/.bin/testling | node_modules/.bin/tspec", "bundle": "node_modules/.bin/browserify index.js > dist/tinyemitter.js -s TinyEmitter && echo 'Bundled'", "minify": "node_modules/.bin/uglifyjs dist/tinyemitter.js -o dist/tinyemitter.min.js -m && echo 'Minified'", "build": "npm test && npm run bundle && npm run minify", "size": "node_modules/.bin/uglifyjs index.js -o minified.js -m && ls -l && rm minified.js"}, "repository": {"type": "git", "url": "https://github.com/scottcorgan/tiny-emitter.git"}, "keywords": ["event", "emitter", "pubsub", "tiny", "events", "bind"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/scottcorgan/tiny-emitter/issues"}, "devDependencies": {"tape": "~2.3.2", "tap-spec": "~0.1.3", "browserify": "~3.18.0", "uglify-js": "~2.4.8", "testling": "~1.5.6"}, "testling": {"files": ["test/index.js"], "browsers": ["iexplore/10.0", "iexplore/9.0", "firefox/16..latest", "chrome/22..latest", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "_id": "tiny-emitter@0.1.9", "dist": {"shasum": "404d85c2553142fc49e3fa4e25bc72506b3b5b79", "tarball": "https://registry.npmjs.org/tiny-emitter/-/tiny-emitter-0.1.9.tgz", "integrity": "sha512-mje+7pFVtQZRB/EJLxWcgJl1zCaI8d1WbcA7kVf/5o1yElAGy/vsqTXh3Uq1tE75MrZke7lm8qkv+2Rz45Iq/g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHvZXTp0aEFxlDLrq3aaRB2CEZmBlYlq/Rmt4tJ0uk1NAiBmSvcqcPJl87VtvMxPb//6/Yo0FtzBW8xM62qnZP9OEA=="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "scottcorgan", "email": "<EMAIL>"}, "maintainers": [{"name": "scottcorgan", "email": "<EMAIL>"}], "directories": {}}, "0.1.10": {"name": "tiny-emitter", "version": "0.1.10", "description": "A tiny (less than 1k) event emitter library", "main": "index.js", "scripts": {"test": "node_modules/.bin/testling | node_modules/.bin/tspec", "bundle": "node_modules/.bin/browserify index.js > dist/tinyemitter.js -s TinyEmitter && echo 'Bundled'", "minify": "node_modules/.bin/uglifyjs dist/tinyemitter.js -o dist/tinyemitter.min.js -m && echo 'Minified'", "build": "npm test && npm run bundle && npm run minify", "size": "node_modules/.bin/uglifyjs index.js -o minified.js -m && ls -l && rm minified.js"}, "repository": {"type": "git", "url": "https://github.com/scottcorgan/tiny-emitter.git"}, "keywords": ["event", "emitter", "pubsub", "tiny", "events", "bind"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/scottcorgan/tiny-emitter/issues"}, "devDependencies": {"tape": "~2.3.2", "tap-spec": "~0.1.3", "browserify": "~3.18.0", "uglify-js": "~2.4.8", "testling": "~1.5.6"}, "testling": {"files": ["test/index.js"], "browsers": ["iexplore/10.0", "iexplore/9.0", "firefox/16..latest", "chrome/22..latest", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "_id": "tiny-emitter@0.1.10", "dist": {"shasum": "c392291c268f1186b1776d1f2b469dcb97931e30", "tarball": "https://registry.npmjs.org/tiny-emitter/-/tiny-emitter-0.1.10.tgz", "integrity": "sha512-dlspSCh2ztWdqdp1R+QdO6Hs3G7WxUIZj7BwbRuvh2aY72O77AY5xljBZH+5+QXg+WlY/exrhfmESogR3zDB1w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDmcjPcmaGS9rBOI7tXUGrKrtWNeoW8vIuIIw4Z2dctVAiAO9CHYJHU5dFPZuVwWuE0XYKm/a22ncGdqSZci1jqF/g=="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "scottcorgan", "email": "<EMAIL>"}, "maintainers": [{"name": "scottcorgan", "email": "<EMAIL>"}], "directories": {}}, "0.1.11": {"name": "tiny-emitter", "version": "0.1.11", "description": "A tiny (less than 1k) event emitter library", "main": "index.js", "scripts": {"test": "node_modules/.bin/testling | node_modules/.bin/tspec", "bundle": "node_modules/.bin/browserify index.js > dist/tinyemitter.js -s TinyEmitter && echo 'Bundled'", "minify": "node_modules/.bin/uglifyjs dist/tinyemitter.js -o dist/tinyemitter.min.js -m && echo 'Minified'", "build": "npm test && npm run bundle && npm run minify", "size": "node_modules/.bin/uglifyjs index.js -o minified.js -m && ls -l && rm minified.js"}, "repository": {"type": "git", "url": "https://github.com/scottcorgan/tiny-emitter.git"}, "keywords": ["event", "emitter", "pubsub", "tiny", "events", "bind"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/scottcorgan/tiny-emitter/issues"}, "devDependencies": {"tape": "~2.3.2", "tap-spec": "~0.1.3", "browserify": "~3.18.0", "uglify-js": "~2.4.8", "testling": "~1.5.6"}, "testling": {"files": ["test/index.js"], "browsers": ["iexplore/10.0", "iexplore/9.0", "firefox/16..latest", "chrome/22..latest", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "_id": "tiny-emitter@0.1.11", "dist": {"shasum": "472232f06679b6f9b49c5fc4162a22471b0e546d", "tarball": "https://registry.npmjs.org/tiny-emitter/-/tiny-emitter-0.1.11.tgz", "integrity": "sha512-KQe/dbKn6ZZ9Flzd0YUqzH8Xmy+HvL9gd8c9PKhs9UNdzme+XTJ3CWLK/wzQ51yJFnB/4rTSkR64YtQLCr78oA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHKE5GwyQpnD2vttF5klilmz64Am97bd0OQ2cVNAeDGxAiEAxXmg5kKcIP+Geqfd8M6q8ZSk/NsB+KNQTy+SupQxkgI="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "scottcorgan", "email": "<EMAIL>"}, "maintainers": [{"name": "scottcorgan", "email": "<EMAIL>"}], "directories": {}}, "0.1.12": {"name": "tiny-emitter", "version": "0.1.12", "description": "A tiny (less than 1k) event emitter library", "main": "index.js", "scripts": {"test": "node_modules/.bin/testling | node_modules/.bin/tspec", "bundle": "node_modules/.bin/browserify index.js > dist/tinyemitter.js -s TinyEmitter && echo 'Bundled'", "minify": "node_modules/.bin/uglifyjs dist/tinyemitter.js -o dist/tinyemitter.min.js -m && echo 'Minified'", "build": "npm test && npm run bundle && npm run minify", "size": "node_modules/.bin/uglifyjs index.js -o minified.js -m && ls -l && rm minified.js"}, "repository": {"type": "git", "url": "https://github.com/scottcorgan/tiny-emitter.git"}, "keywords": ["event", "emitter", "pubsub", "tiny", "events", "bind"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/scottcorgan/tiny-emitter/issues"}, "devDependencies": {"tape": "~2.3.2", "tap-spec": "~0.1.3", "browserify": "~3.18.0", "uglify-js": "~2.4.8", "testling": "~1.5.6"}, "testling": {"files": ["test/index.js"], "browsers": ["iexplore/10.0", "iexplore/9.0", "firefox/16..latest", "chrome/22..latest", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "_id": "tiny-emitter@0.1.12", "dist": {"shasum": "38500b9198925f08ca90523bf932282db6fd19f1", "tarball": "https://registry.npmjs.org/tiny-emitter/-/tiny-emitter-0.1.12.tgz", "integrity": "sha512-qqP0mviyclxcl9a2vm1jwTn/kObcU6eHdm0t8055dqbKIlOaOky7wFw37ABv5EdWsSWDWlMLeDdWDQcdcKGcKw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGXxrKlPPiUzpX6IymxBjJVuO7qsGrOIyzUW3Sos1EpqAiAbVq9j08u29yo37mDnLRiVSDsvdnWwXezzdyH/gJ2lnQ=="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "scottcorgan", "email": "<EMAIL>"}, "maintainers": [{"name": "scottcorgan", "email": "<EMAIL>"}], "directories": {}}, "1.0.0": {"name": "tiny-emitter", "version": "1.0.0", "description": "A tiny (less than 1k) event emitter library", "main": "index.js", "scripts": {"test": "testling | node_modules/.bin/tspec", "bundle": "browserify index.js > dist/tinyemitter.js -s TinyEmitter && echo 'Bundled'", "minify": "uglifyjs dist/tinyemitter.js -o dist/tinyemitter.min.js -m && echo 'Minified'", "build": "npm test && npm run bundle && npm run minify", "size": "uglifyjs index.js -o minified.js -m && ls -l && rm minified.js"}, "repository": {"type": "git", "url": "https://github.com/scottcorgan/tiny-emitter.git"}, "keywords": ["event", "emitter", "pubsub", "tiny", "events", "bind"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/scottcorgan/tiny-emitter/issues"}, "devDependencies": {"tape": "~2.3.2", "tap-spec": "~0.1.3", "browserify": "~3.18.0", "uglify-js": "~2.4.8", "testling": "~1.5.6"}, "testling": {"files": ["test/index.js"], "browsers": ["iexplore/10.0", "iexplore/9.0", "firefox/16..latest", "chrome/22..latest", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "81532e3600a6284a9cb87ef2f625e89df8f9282a", "homepage": "https://github.com/scottcorgan/tiny-emitter", "_id": "tiny-emitter@1.0.0", "_shasum": "de7473c10cacf92b3a007a4416dd48d8fd0034f9", "_from": ".", "_npmVersion": "1.4.23", "_npmUser": {"name": "scottcorgan", "email": "<EMAIL>"}, "maintainers": [{"name": "scottcorgan", "email": "<EMAIL>"}], "dist": {"shasum": "de7473c10cacf92b3a007a4416dd48d8fd0034f9", "tarball": "https://registry.npmjs.org/tiny-emitter/-/tiny-emitter-1.0.0.tgz", "integrity": "sha512-rc9SmgUx++AZxQ4WuK84kImJGIbhMKIPPZH3rZyyx64Tp/slcrBmvOVlSv2ly6HGImDeR+IRxxsqTyysGpzFDQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC268GSXHOk0Vbr+8uUmqNOLbmXmvln7rW0pYg5XjkJHQIgfNjTM2OdeXJCEFq2Rd5MDwPrjxQ/Q1wZn6n0Oqv96Dw="}]}, "directories": {}}, "1.0.1": {"name": "tiny-emitter", "version": "1.0.1", "description": "A tiny (less than 1k) event emitter library", "main": "index.js", "scripts": {"test": "testling | node_modules/.bin/tspec", "bundle": "browserify index.js > dist/tinyemitter.js -s TinyEmitter && echo 'Bundled'", "minify": "uglifyjs dist/tinyemitter.js -o dist/tinyemitter.min.js -m && echo 'Minified'", "build": "npm test && npm run bundle && npm run minify", "size": "uglifyjs index.js -o minified.js -m && ls -l && rm minified.js"}, "repository": {"type": "git", "url": "git+https://github.com/scottcorgan/tiny-emitter.git"}, "keywords": ["event", "emitter", "pubsub", "tiny", "events", "bind"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/scottcorgan/tiny-emitter/issues"}, "devDependencies": {"browserify": "^11.2.0", "tap-spec": "^4.1.0", "tape": "^4.2.1", "testling": "^1.7.1", "uglify-js": "^2.5.0"}, "testling": {"files": ["test/index.js"], "browsers": ["iexplore/10.0", "iexplore/9.0", "firefox/16..latest", "chrome/22..latest", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "bd8c58e4a6c0bcc5bc5c6177e1c572f7433411fc", "homepage": "https://github.com/scottcorgan/tiny-emitter#readme", "_id": "tiny-emitter@1.0.1", "_shasum": "55a5871acd12bee10638f95beb9bafd89830a7c6", "_from": ".", "_npmVersion": "2.14.4", "_nodeVersion": "4.1.1", "_npmUser": {"name": "scottcorgan", "email": "<EMAIL>"}, "dist": {"shasum": "55a5871acd12bee10638f95beb9bafd89830a7c6", "tarball": "https://registry.npmjs.org/tiny-emitter/-/tiny-emitter-1.0.1.tgz", "integrity": "sha512-2/IB8UBb6Qq4UhEBJf0ISYzhuuxfMcvgS2a/Kf1t61YLjB9O38A7ICP9G7h39du7Xlk40JDqxF0TFDRGG2nx4Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE2XqCqibLfcTsAWwUfraNMwfZ70Rhn3Zfq2cKmNUywzAiAP5nl/nectYgOPB6gnpWkXSLV0CLZ+1oZu0SW1Y9gUww=="}]}, "maintainers": [{"name": "scottcorgan", "email": "<EMAIL>"}], "directories": {}}, "1.0.2": {"name": "tiny-emitter", "version": "1.0.2", "description": "A tiny (less than 1k) event emitter library", "main": "index.js", "scripts": {"test": "testling | tap-format-spec", "bundle": "browserify index.js > dist/tinyemitter.js -s TinyEmitter && echo 'Bundled'", "minify": "uglifyjs dist/tinyemitter.js -o dist/tinyemitter.min.js -m && echo 'Minified'", "build": "npm test && npm run bundle && npm run minify", "size": "uglifyjs index.js -o minified.js -m && ls -l && rm minified.js"}, "repository": {"type": "git", "url": "git+https://github.com/scottcorgan/tiny-emitter.git"}, "keywords": ["event", "emitter", "pubsub", "tiny", "events", "bind"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/scottcorgan/tiny-emitter/issues"}, "devDependencies": {"@tap-format/spec": "0.2.0", "browserify": "^11.2.0", "tape": "^4.2.1", "testling": "^1.7.1", "uglify-js": "^2.5.0"}, "testling": {"files": ["test/index.js"], "browsers": ["iexplore/10.0", "iexplore/9.0", "firefox/16..latest", "chrome/22..latest", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "cc88f1cd3a97258d9f81f44f1fb28f0d3e40fab4", "homepage": "https://github.com/scottcorgan/tiny-emitter#readme", "_id": "tiny-emitter@1.0.2", "_shasum": "8e49470d3f55f89e247210368a6bb9fb51aa1601", "_from": ".", "_npmVersion": "2.14.14", "_nodeVersion": "5.1.1", "_npmUser": {"name": "scottcorgan", "email": "<EMAIL>"}, "dist": {"shasum": "8e49470d3f55f89e247210368a6bb9fb51aa1601", "tarball": "https://registry.npmjs.org/tiny-emitter/-/tiny-emitter-1.0.2.tgz", "integrity": "sha512-y87VH5ncX+MzK2rqEF6v7Ad8gOdQDV1jiFT5bXUrWGBTFN9EFLrvufpWryNipgFDCzQ4fTOTPUCZGLYoQ2D9UA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFNpXc0JA/sO/QPKgANKlbBxnX1/BkD5uxxR01Fvq+K8AiEAmUL9spr/L28MAdllkpVdElABKx2VMaETV10iYg4GWEI="}]}, "maintainers": [{"name": "scottcorgan", "email": "<EMAIL>"}], "directories": {}}, "1.1.0": {"name": "tiny-emitter", "version": "1.1.0", "description": "A tiny (less than 1k) event emitter library", "main": "index.js", "scripts": {"test": "testling | tap-format-spec", "bundle": "browserify index.js > dist/tinyemitter.js -s TinyEmitter && echo 'Bundled'", "minify": "uglifyjs dist/tinyemitter.js -o dist/tinyemitter.min.js -m && echo 'Minified'", "build": "npm test && npm run bundle && npm run minify", "size": "uglifyjs index.js -o minified.js -m && ls -l && rm minified.js"}, "repository": {"type": "git", "url": "git+https://github.com/scottcorgan/tiny-emitter.git"}, "keywords": ["event", "emitter", "pubsub", "tiny", "events", "bind"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/scottcorgan/tiny-emitter/issues"}, "devDependencies": {"@tap-format/spec": "0.2.0", "browserify": "^11.2.0", "tape": "^4.2.1", "testling": "^1.7.1", "uglify-js": "^2.5.0"}, "testling": {"files": ["test/index.js"], "browsers": ["iexplore/10.0", "iexplore/9.0", "firefox/16..latest", "chrome/22..latest", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "6cd3cef4872e45fac5195b1912deeb2b30da1ce1", "homepage": "https://github.com/scottcorgan/tiny-emitter#readme", "_id": "tiny-emitter@1.1.0", "_shasum": "ab405a21ffed814a76c19739648093d70654fecb", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "6.1.0", "_npmUser": {"name": "scottcorgan", "email": "<EMAIL>"}, "dist": {"shasum": "ab405a21ffed814a76c19739648093d70654fecb", "tarball": "https://registry.npmjs.org/tiny-emitter/-/tiny-emitter-1.1.0.tgz", "integrity": "sha512-HFhr+OKGIHRO6krgzEt9MqbMO98wPDzDPr1BOpM/nZCChkK40UYn8b70nSjcan4jTzDSQecy1KRVVQRohIRWrw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCAHnvRvRGc/YdGQ7JgSXsxM408X8SGdvv3PKn5zuCo7QIgdynv3Mvazg4SdQNfJfKKIUCOh67xzofAXwVxKu3gqJ8="}]}, "maintainers": [{"name": "scottcorgan", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/tiny-emitter-1.1.0.tgz_1467208205044_0.9086456855293363"}, "directories": {}}, "1.2.0": {"name": "tiny-emitter", "version": "1.2.0", "description": "A tiny (less than 1k) event emitter library", "main": "index.js", "scripts": {"test-node": "tape test/index.js | tap-format-spec", "test": "testling | tap-format-spec", "bundle": "browserify index.js > dist/tinyemitter.js -s TinyEmitter && echo 'Bundled'", "minify": "uglifyjs dist/tinyemitter.js -o dist/tinyemitter.min.js -m && echo 'Minified'", "build": "npm test && npm run bundle && npm run minify", "size": "uglifyjs index.js -o minified.js -m && ls -l && rm minified.js"}, "repository": {"type": "git", "url": "git+https://github.com/scottcorgan/tiny-emitter.git"}, "keywords": ["event", "emitter", "pubsub", "tiny", "events", "bind"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/scottcorgan/tiny-emitter/issues"}, "devDependencies": {"@tap-format/spec": "0.2.0", "browserify": "11.2.0", "tape": "4.2.1", "testling": "1.7.1", "uglify-js": "2.5.0"}, "testling": {"files": ["test/index.js"], "browsers": ["iexplore/10.0", "iexplore/9.0", "firefox/16..latest", "chrome/22..latest", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "385d1b293904679c2bf6c149b1a858cfec3068ca", "homepage": "https://github.com/scottcorgan/tiny-emitter#readme", "_id": "tiny-emitter@1.2.0", "_shasum": "6dc845052cb08ebefc1874723b58f24a648c3b6f", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.4", "_npmUser": {"name": "scottcorgan", "email": "<EMAIL>"}, "dist": {"shasum": "6dc845052cb08ebefc1874723b58f24a648c3b6f", "tarball": "https://registry.npmjs.org/tiny-emitter/-/tiny-emitter-1.2.0.tgz", "integrity": "sha512-rWjF00inHeWtT5UbQYAXoMI4hL6TRMqohuKCsODyPYYmfAxqfMnXLsIeNrbdPEkNxlk++rojVilTnI9IVmEBtA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHhko4o0gda8sQBlS8JZnMvoPS5GDDm7o6VA0yEsz10GAiEAxlVPd2URvVFpiyceazzgjiOlStEhjH/v7ROGvppCxYo="}]}, "maintainers": [{"name": "scottcorgan", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/tiny-emitter-1.2.0.tgz_1492514077407_0.878022002056241"}, "directories": {}}, "2.0.0": {"name": "tiny-emitter", "version": "2.0.0", "description": "A tiny (less than 1k) event emitter library", "main": "index.js", "scripts": {"test-node": "tape test/index.js | tap-format-spec", "test": "testling | tap-format-spec", "bundle": "browserify index.js > dist/tinyemitter.js -s TinyEmitter && echo 'Bundled'", "minify": "uglifyjs dist/tinyemitter.js -o dist/tinyemitter.min.js -m && echo 'Minified'", "build": "npm test && npm run bundle && npm run minify", "size": "uglifyjs index.js -o minified.js -m && ls -l && rm minified.js"}, "repository": {"type": "git", "url": "git+https://github.com/scottcorgan/tiny-emitter.git"}, "keywords": ["event", "emitter", "pubsub", "tiny", "events", "bind"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/scottcorgan/tiny-emitter/issues"}, "devDependencies": {"@tap-format/spec": "0.2.0", "browserify": "11.2.0", "tape": "4.2.1", "testling": "1.7.1", "uglify-js": "2.5.0"}, "testling": {"files": ["test/index.js"], "browsers": ["iexplore/10.0", "iexplore/9.0", "firefox/16..latest", "chrome/22..latest", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "087f94b4b159e6e27ed5b6fb7552338e8067f6e2", "homepage": "https://github.com/scottcorgan/tiny-emitter#readme", "_id": "tiny-emitter@2.0.0", "_shasum": "bad327adb1804b42a231afa741532bd884cd09ad", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.4", "_npmUser": {"name": "scottcorgan", "email": "<EMAIL>"}, "dist": {"shasum": "bad327adb1804b42a231afa741532bd884cd09ad", "tarball": "https://registry.npmjs.org/tiny-emitter/-/tiny-emitter-2.0.0.tgz", "integrity": "sha512-Hh5QVuntdnilFEyhc7/Sah99H9Hez02lKdpW8eMooAheOhdbcplTmbbfBTyTIlNjROqJwo2C/XT0o1PZINdPAw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBpdPwUdLgkODq7JQluN9Mg3QyxY5y/hEN0Hy9BdTmV/AiEAgf26P1dQ/v+KqJoiGRp3Bkla8OIsrRGrQjyAhdGnsqc="}]}, "maintainers": [{"name": "scottcorgan", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/tiny-emitter-2.0.0.tgz_1492522522087_0.09058226319029927"}, "directories": {}}, "2.0.1": {"name": "tiny-emitter", "version": "2.0.1", "description": "A tiny (less than 1k) event emitter library", "main": "index.js", "scripts": {"test-node": "tape test/index.js | tap-format-spec", "test": "testling | tap-format-spec", "bundle": "browserify index.js > dist/tinyemitter.js -s TinyEmitter && echo 'Bundled'", "minify": "uglifyjs dist/tinyemitter.js -o dist/tinyemitter.min.js -m && echo 'Minified'", "build": "npm test && npm run bundle && npm run minify", "size": "uglifyjs index.js -o minified.js -m && ls -l && rm minified.js"}, "repository": {"type": "git", "url": "git+https://github.com/scottcorgan/tiny-emitter.git"}, "keywords": ["event", "emitter", "pubsub", "tiny", "events", "bind"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/scottcorgan/tiny-emitter/issues"}, "devDependencies": {"@tap-format/spec": "0.2.0", "browserify": "11.2.0", "tape": "4.2.1", "testling": "1.7.1", "uglify-js": "2.5.0"}, "testling": {"files": ["test/index.js"], "browsers": ["iexplore/10.0", "iexplore/9.0", "firefox/16..latest", "chrome/22..latest", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "e5440fec657b69f1c5a29617344b1eec81e90cfc", "homepage": "https://github.com/scottcorgan/tiny-emitter#readme", "_id": "tiny-emitter@2.0.1", "_shasum": "e65919d91e488e2a78f7ebe827a56c6b188d51af", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.10.0", "_npmUser": {"name": "scottcorgan", "email": "<EMAIL>"}, "dist": {"shasum": "e65919d91e488e2a78f7ebe827a56c6b188d51af", "tarball": "https://registry.npmjs.org/tiny-emitter/-/tiny-emitter-2.0.1.tgz", "integrity": "sha512-lyBt17NpqqZEJgL9kAmYnwzo5Pb6I/4QLaKlqzPz35iIYj5BkDYRL7/2XVHD8KPnvt1pCB5SvAwDpd8DmJy8LA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHl9RNzSMbOWEt5HcfzzQlHUTuzkVLauc2I7gi0v4+nYAiEA6TGXUvQqmI/Zp0D9JmT2lSZ+lDsgLHbAWJli7wVaQiI="}]}, "maintainers": [{"name": "scottcorgan", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tiny-emitter-2.0.1.tgz_1499253393324_0.34592962940223515"}, "directories": {}}, "2.0.2": {"name": "tiny-emitter", "version": "2.0.2", "description": "A tiny (less than 1k) event emitter library", "main": "index.js", "scripts": {"test-node": "tape test/index.js | tap-format-spec", "test": "testling | tap-format-spec", "bundle": "browserify index.js > dist/tinyemitter.js -s TinyEmitter && echo 'Bundled'", "minify": "uglifyjs dist/tinyemitter.js -o dist/tinyemitter.min.js -m && echo 'Minified'", "build": "npm test && npm run bundle && npm run minify", "size": "uglifyjs index.js -o minified.js -m && ls -l && rm minified.js"}, "repository": {"type": "git", "url": "git+https://github.com/scottcorgan/tiny-emitter.git"}, "keywords": ["event", "emitter", "pubsub", "tiny", "events", "bind"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/scottcorgan/tiny-emitter/issues"}, "devDependencies": {"@tap-format/spec": "0.2.0", "browserify": "11.2.0", "tape": "4.2.1", "testling": "1.7.1", "uglify-js": "2.5.0"}, "testling": {"files": ["test/index.js"], "browsers": ["iexplore/10.0", "iexplore/9.0", "firefox/16..latest", "chrome/22..latest", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "479797e36cb983489cc95c8e175d776509151550", "homepage": "https://github.com/scottcorgan/tiny-emitter#readme", "_id": "tiny-emitter@2.0.2", "_npmVersion": "5.3.0", "_nodeVersion": "8.3.0", "_npmUser": {"name": "scottcorgan", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-2NM0auVBGft5tee/OxP4PI3d8WItkDM+fPnaRAVo6xTDI2knbz9eC5ArWGqtGlYqiH3RU5yMpdyTTO7MguC4ow==", "shasum": "82d27468aca5ade8e5fd1e6d22b57dd43ebdfb7c", "tarball": "https://registry.npmjs.org/tiny-emitter/-/tiny-emitter-2.0.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFcrhueS3l34olqxeG1sJ5j/ts1oL1F4tW+4DvQQhUdaAiEAqJuhU7ChbcQDXXsAindOGnVxW6Lwqr1n2Msc4bzfveM="}]}, "maintainers": [{"name": "scottcorgan", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tiny-emitter-2.0.2.tgz_1502970218521_0.8436653357930481"}, "directories": {}}, "2.1.0": {"name": "tiny-emitter", "version": "2.1.0", "description": "A tiny (less than 1k) event emitter library", "main": "index.js", "scripts": {"test-node": "tape test/index.js | tap-format-spec", "test": "testling | tap-format-spec", "bundle": "browserify index.js > dist/tinyemitter.js -s TinyEmitter && echo 'Bundled'", "minify": "uglifyjs dist/tinyemitter.js -o dist/tinyemitter.min.js -m && echo 'Minified'", "build": "npm test && npm run bundle && npm run minify", "size": "uglifyjs index.js -o minified.js -m && ls -l && rm minified.js"}, "repository": {"type": "git", "url": "git+https://github.com/scottcorgan/tiny-emitter.git"}, "keywords": ["event", "emitter", "pubsub", "tiny", "events", "bind"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/scottcorgan/tiny-emitter/issues"}, "devDependencies": {"@tap-format/spec": "0.2.0", "browserify": "11.2.0", "tape": "4.2.1", "testling": "1.7.1", "uglify-js": "2.5.0"}, "testling": {"files": ["test/index.js"], "browsers": ["iexplore/10.0", "iexplore/9.0", "firefox/16..latest", "chrome/22..latest", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "a6026cc94da5d2d164104dd30586eee6bb4fb29c", "homepage": "https://github.com/scottcorgan/tiny-emitter#readme", "_id": "tiny-emitter@2.1.0", "_npmVersion": "6.4.1", "_nodeVersion": "8.12.0", "_npmUser": {"name": "scottcorgan", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-NB6Dk1A9xgQPMoGqC5CVXn123gWyte215ONT5Pp5a0yt4nlEoO1ZWeCwpncaekPHXO60i47ihFnZPiRPjRMq4Q==", "shasum": "1d1a56edfc51c43e863cbb5382a72330e3555423", "tarball": "https://registry.npmjs.org/tiny-emitter/-/tiny-emitter-2.1.0.tgz", "fileCount": 10, "unpackedSize": 77178, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcWa6FCRA9TVsSAnZWagAAg5UP/1hFRSNs+J7T0dZ+okZl\nRqw0p0QT8MCOHTkPS6FaLevkPMcXKMuG+ZIlshfHmfKN0ablFgCMrIq4LrJa\nocZhOS5iBqHA9v5+qh74xnxyLrUgZN3INS0LJ5lMsSR+/04ivjNpcQgWZMn1\n7v4SpjCFAQtZH36zFQvrJzN40j+XsUvIItrYYe614gnmVOrtlh5r27VmlgQM\ndPhNgY6JIXCB8tg5jGn0VR63vJ5Q1TeNAdrswnnjueGLcrqDWOhVE6EVQauC\nM31R6oXA7miA4qIWSBAOXV7l7ZaC/DKCcJEgCuQOkQMq2aNH8JVHwePMAIP6\nwNipSC/NoiT0zTk2fX22e3knodJHH5JWJZIsRU0UZSChQ63nGtr+rgGhXAzd\nKitF2v0QFj6fHDhbuOsHQ5Lj7/6s9AKQKYyqPUDooVpFObXpqA0Ctcw1D7BA\nVc5tHJ3j/HLsg2ZdlaSUd2y0p5rez/4YTkDaA38SRMJcw9JltP2fVAypo6ck\n3xHu81lxQtE9FO61ZzgIACN5rPBmiVI6T26up0aWCLeicTzR/G21fsD1TJN9\nl7OPohk1z3DEaXTudpU09ZzUPcXZL2Ay9DG7Qy5u2mBL0gWJC8VMDmVgj9B9\nXef69mwVL1Ogz8ykkSpPJIl9wQ2O+Ww408/4lS7amCNqqVAhsv5OpbmLb+cF\nRCSK\r\n=SsCo\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFeFiZe9WgMexP55TmHk5aNnjsk8i9O4A0kgWYtxKBBPAiEA6ObBjEvejj66rj5AyE4aHLgc+KSpfUsxBlSZWw+/PWw="}]}, "maintainers": [{"name": "scottcorgan", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tiny-emitter_2.1.0_1549381252634_0.40391833556455703"}, "_hasShrinkwrap": false}}, "readme": "# tiny-emitter\n\nA tiny (less than 1k) event emitter library.\n\n## Install\n\n### npm\n\n```\nnpm install tiny-emitter --save\n```\n\n## Usage\n\n```js\nvar Emitter = require('tiny-emitter');\nvar emitter = new Emitter();\n\nemitter.on('some-event', function (arg1, arg2, arg3) {\n //\n});\n\nemitter.emit('some-event', 'arg1 value', 'arg2 value', 'arg3 value');\n```\n\nAlternatively, you can skip the initialization step by requiring `tiny-emitter/instance` instead. This pulls in an already initialized emitter.\n\n```js\nvar emitter = require('tiny-emitter/instance');\n\nemitter.on('some-event', function (arg1, arg2, arg3) {\n //\n});\n\nemitter.emit('some-event', 'arg1 value', 'arg2 value', 'arg3 value');\n```\n\n## Instance Methods\n\n### on(event, callback[, context])\n\nSubscribe to an event\n\n* `event` - the name of the event to subscribe to\n* `callback` - the function to call when event is emitted\n* `context` - (OPTIONAL) - the context to bind the event callback to\n\n### once(event, callback[, context])\n\nSubscribe to an event only **once**\n\n* `event` - the name of the event to subscribe to\n* `callback` - the function to call when event is emitted\n* `context` - (OPTIONAL) - the context to bind the event callback to\n\n### off(event[, callback])\n\nUnsubscribe from an event or all events. If no callback is provided, it unsubscribes you from all events.\n\n* `event` - the name of the event to unsubscribe from\n* `callback` - the function used when binding to the event\n\n### emit(event[, arguments...])\n\nTrigger a named event\n\n* `event` - the event name to emit\n* `arguments...` - any number of arguments to pass to the event subscribers\n\n## Test and Build\n\nBuild (Tests, Browserifies, and minifies)\n\n```\nnpm install\nnpm run build\n```\n\nTest\n\n```\nnpm install\nnpm test\n```\n\n## License\n\n[MIT](https://github.com/scottcorgan/tiny-emitter/blob/master/LICENSE)\n", "maintainers": [{"name": "scottcorgan", "email": "<EMAIL>"}], "time": {"modified": "2022-06-27T06:58:31.168Z", "created": "2014-01-03T23:22:38.411Z", "0.1.0": "2014-01-03T23:22:38.908Z", "0.1.1": "2014-01-04T00:23:57.216Z", "0.1.2": "2014-01-04T00:47:19.278Z", "0.1.3": "2014-01-04T01:28:13.939Z", "0.1.4": "2014-01-04T23:51:25.001Z", "0.1.5": "2014-01-05T00:15:42.826Z", "0.1.6": "2014-01-06T16:03:14.505Z", "0.1.7": "2014-01-06T18:53:41.066Z", "0.1.8": "2014-01-09T16:23:49.501Z", "0.1.9": "2014-01-09T16:59:31.006Z", "0.1.10": "2014-01-09T17:17:05.707Z", "0.1.11": "2014-01-09T17:40:27.044Z", "0.1.12": "2014-01-23T02:18:53.899Z", "1.0.0": "2014-09-16T19:08:32.014Z", "1.0.1": "2015-10-14T19:18:30.367Z", "1.0.2": "2015-12-10T19:27:12.356Z", "1.1.0": "2016-06-29T13:50:07.330Z", "1.2.0": "2017-04-18T11:14:38.114Z", "2.0.0": "2017-04-18T13:35:23.907Z", "2.0.1": "2017-07-05T11:16:34.251Z", "2.0.2": "2017-08-17T11:43:39.545Z", "2.1.0": "2019-02-05T15:40:52.734Z"}, "author": {"name": "<PERSON>"}, "repository": {"type": "git", "url": "git+https://github.com/scottcorgan/tiny-emitter.git"}, "users": {"parroit": true, "nichoth": true, "jondashkyle": true, "philipjc": true, "matthewh": true, "larrychen": true, "bluejeansandrain": true, "qddegtya": true, "black-black-cat": true, "cr8tiv": true, "xieranmaya": true}, "readmeFilename": "README.md", "homepage": "https://github.com/scottcorgan/tiny-emitter#readme", "keywords": ["event", "emitter", "pubsub", "tiny", "events", "bind"], "bugs": {"url": "https://github.com/scottcorgan/tiny-emitter/issues"}, "license": "MIT"}