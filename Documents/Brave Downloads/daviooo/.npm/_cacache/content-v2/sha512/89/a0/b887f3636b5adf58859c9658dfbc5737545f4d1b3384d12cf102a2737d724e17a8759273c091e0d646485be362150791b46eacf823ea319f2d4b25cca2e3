{"name": "@npmcli/config", "dist-tags": {"backport": "6.4.1", "prerelease": "10.0.0-pre.1", "latest": "10.3.0"}, "versions": {"0.0.0-pre.0": {"name": "@npmcli/config", "version": "0.0.0-pre.0", "dependencies": {"ini": "^1.3.5", "nopt": "^4.0.3", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0"}, "devDependencies": {"tap": "^14.10.8"}, "dist": {"shasum": "5eb9964bfb4fe8d616de3147a9aa65c1a223db18", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-0.0.0-pre.0.tgz", "fileCount": 12, "integrity": "sha512-V4OoqfwIqcKn5fiar3WJXon8VLw2e3zshBYK/avziG000Gxx4MvTiesYHQpDGaYJ94IBdSTCh76g9SSd7i45Rg==", "signatures": [{"sig": "MEYCIQCU+X/hV4flgxYBy3Mak5NkbJFOo5MVsfe83zrEjI8VdQIhAPxQlyxwY/9YmB9oGWkHFfrtLfihhvsxAnT/zSntQYbl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33828, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfOdA8CRA9TVsSAnZWagAA/QIQAJzN5l23inLaNhWdnRkv\n63ia/zrkJ/MsG2GovcDwZ9oDyE+Y8ju9sTzKj2SFyVKiegdbZbvUdfQ/kzTz\nVbT1RhFQCxDw25QfHiffvSnOHQ6T4kBsypmVAvyXTfF7K/yVMwYdNnKSl8Y/\nH2DKPv4eCCh6FRSbBukmi7sHvM2lpEKSZfrJ4AsFBqsJM4Cfff9EizxkegFR\noz2gNftk1PCw/a5d5FW05SyXtz60KSU3NYN1bbj3cOp36Bz9vLBeZKx5qHuH\nla0Gz83bxHNJU/Co5bfxJVeRWG5xUNWjnBlXh3IoBcjtpXAgpXJEFFZ1vhak\npQKRRH06VYLKEiXB6znjdNPmnqT2tuwtHeUfhpNkcPqqR4+Lmd/swT7PU1fJ\nvP60sjqY4oNQj/+RITwuZ5C3yq4VXoWp396lGh9B4im7LUheZl7B4bTJ+eas\nVxYgNla+aqcg6M1NwE87sUipfuKp9PIp1D4+MAZRbQJWpa4XjA/AV4+Uq7QQ\nuUyQ3ikWl0TsHKYktoC4Bihry7/rjIUNcFPBNoF5Lvw6sOsMjim4BJRZ22BQ\nc/jmTVSjCMzmTmG+VIWNE+sIucKVVJteGBVqvRQIXAvVdDe23JP1RumaDtyj\nAyj+p9nQDPKJ1mHh4tzBzGv8CmHPKOwuTO+zUQgmpBOvlh6X9lcNPMDtooSu\nsXSQ\r\n=msQq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.0": {"name": "@npmcli/config", "version": "0.0.0", "dependencies": {"ini": "^1.3.5", "nopt": "^4.0.3", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0"}, "devDependencies": {"tap": "^14.10.8"}, "dist": {"shasum": "a1fe6bf7559d6ac4d8a90d4afa8a95ca6b078e8e", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-0.0.0.tgz", "fileCount": 12, "integrity": "sha512-do2ixOlC/g0zNCnc1C52qBAA8vKJ5sxJSC9SYuTOAi7xWgmfLFulIiEd/yWqRb3AvGVRnbfge0UtBRBF7n5U9g==", "signatures": [{"sig": "MEYCIQC7PtiZLtMrI6jkyWB3nes16HQjXejKGYJOCbf14P5psQIhAKeMk0PU/AJ850Afva6ecI8QE1Bfh0VWE1h3OB1lCQBp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33899, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfOhVDCRA9TVsSAnZWagAA7dcQAI1bJrLlflNJY2IVOw5k\nkHemIn6Z2NRd5og3EG5NO9w5F4bo0laef4o4aJr9AcA4E4EEMjeMvbQiUf1Y\n8Orcr1ODlJDcZEqZWjis11ECD9JO4ILyLFFAZK0ySXMX3OUWNoQXBQCsXuAB\nQOoQGt90NcrBcbX9C/6obfFDPm8fr3zoH1swD6Qb8773ADero9QT8GsIVOr2\n4hmY7hiBeyyglegTi/TVE2FXZQijrO84jbde7MWF9ZyoSFuGGlobQXVofe2s\nIdVa435sivZHyp0zmAj0NWRQqcwKQXr2066DEj1fbYoDwvT6wEYF3mosvV8p\neY/EIU9vuwOWamPk1uP3TAm1RUTWkU+0oTJoQVEcEzCxtETHy3VMlrmJQe00\nt5RLT8Uz2XmxUrTa7prJc7vQodpc6K8v7iqIron09PGA6m13UZK9Jxq0JC+C\nPGlZLADIMhi55X1dPH49l66Jf3sAh/5Z/fhw7GIbIw7h1EL1V13I+Ujj9787\n2xxSvrOmxzV//oCTkRbvbicPs8XfJsGDsbmWzYPTIguY8kC55ouCUi7F/tUX\n9OMgPh1khs/aYX35DB4weObcqUS2uk1bsLCokGN1wKPyG4fmw29F6q9wkdRY\nCeicQSs+u1yVRXBubvLBZIsYCXoEO4hUcqZOF/msZOHy7oZsQZ271Mk6p9O2\n83oU\r\n=n96J\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "@npmcli/config", "version": "1.0.0", "dependencies": {"ini": "^1.3.5", "nopt": "^4.0.3", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0"}, "devDependencies": {"tap": "^14.10.8"}, "dist": {"shasum": "13b4f05e49f4283385d38596368f4f9923ddbc2d", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-1.0.0.tgz", "fileCount": 12, "integrity": "sha512-efYRcOhy0OcsYRQvy+257IwtopDr74uV3wz5dftxku2CvoQvulpJP5VZ0agChS9lGNIOcQ+irdPLdjELlSqoRQ==", "signatures": [{"sig": "MEUCIE+SxNU6lXEFzShGOBwTVI3PcBsFgtacUZ6cCRF0Zds+AiEAl0NUmkdWMA+pBHYWjcM7wxMke7mYUW3G3SM2zH8K+HE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39967, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfOiTOCRA9TVsSAnZWagAAwlgP/2vz2/rNbZgSJ1lsYdw/\nArz6W66u/BKO3LXUvZRlJOSvDVzkiY2gjtDVdrrrRCXOLwucrfOJvhaNWGiv\nX3XTg1WTrz+HPPwhw9pzrT6XYI+9nP8AMfmnfUcsYB4sSkffMAIubRIYrAsY\nm7ofhrVVhFc/1EUYRBXOw4I40+M1XAIU9dvJPN90uLBddLBNbqptzwL9gGWJ\nrWR1Uw7NGBoRDFolEvPnosJCv/SNgmmWS7QjYbkE20SHDkuy9kOcXxFTjfXV\nXQOUMPQbaonkIo+7HMQEInsYilhyyMvGRL8T8FaUh8XeAS2bw+iXu7TcWQc+\nUqC+PCmfAQBkc6xl2wTtkWeSLHVX1fg0NQAM+vECBeBmUZmdsOGqKrhiKxgY\niYEpChS5ihRIQXLeakXXYsLd/CNuFN1kFVMQog5OmAih742w5Tzw1UeZ0Nwk\nfNTFLZcCF2N+pTVzMr4hvEGcdLgf3sm9FM0yX7PuePuz0E98JKtDZOiqf4NO\nn8Rb9xiFzG7Z8M/uOGuf/hPvAnkKAddveZCwUDjsxH4d2jAZAeZVnxqiFZpW\nbg72DERHrgu0kB0f56Tk6QNGe7TnTOxE6+qdbHasplLQyj5u95eaHBVdSaWg\nx91KtyCYgLpbgHcoprWZh5uvPxlVlouiqfs8KnfhLDr8oUj6ZNHxOqvYOoPu\nV7yb\r\n=3Sz/\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1": {"name": "@npmcli/config", "version": "1.0.1", "dependencies": {"ini": "^1.3.5", "nopt": "^4.0.3", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0"}, "devDependencies": {"tap": "^14.10.8"}, "dist": {"shasum": "5abc24cb8f8a8ce7c31754233d9c9ece0f45ee4a", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-1.0.1.tgz", "fileCount": 12, "integrity": "sha512-QsHTHoXSJG+HQgQSY1vhT2xx/3rY51F558QRPjsTvCoejSh/TyytPb0T525fpU3Goqmkwj7qqnFXbaXzvoGNeA==", "signatures": [{"sig": "MEUCIQCiwULZewCpxwUIu+yB+K0Key222qoW6Bb95IWNHnSuxgIgXgeio6jnyJxDQr/6WrcIYoUFkv7c+KL/0fi2mcTAxi0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39966, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfOimhCRA9TVsSAnZWagAAjiIP/0JU8Z9iM++0Jj02PN4B\nNRvvzF4pQ/yt7nUY9fxSnUI1N6x+qGftO5hPb/OEyeOqfTumWGre+jTF6ytM\n0gZKX1vs8bNCCl2tv0lFrWgjA4Vlhfbmr8spENjryahYmc88BqX6lFosgPF3\n8ADpYVz4A4zsFjPiu+qIW1/PI0orN79u2j8pebSLgoswMsZRG9vZtF/gqjU3\nQ8hI1zUBXaAaNU047bmtPHCGIstuvX1T8fFeL8ZvLxFB/bOBW5BlD8WWsxj9\ndC3qRNgnJ3stnRe1psPVXE3U6+DVqVjhBrFtaxdBPfPyGTrDo9/vtZTzdwex\nHEsTwG1mVNCAJBUxJpG2tYZa19NpWXd9P+/vQUg1Y42LUbXGPH47ze0xJXRV\nBczv7S8NLZtKL+YsExxQoV38Lv1mBHTTzzNZqt2J9lvHmddDj6u0y0FsUVYK\nCGfY91wkQQuZoLjFXVFzTUWIDJmfparxR2RcpFuzoWHhtQpRtDlVh0ZQgfwG\npwQP2Q4TIJ4SECGtNIQ4rKczHWrflhPmtDfnoaeuHSKmyS4zda8yHTZl1r6K\nDIXYZ/vMZRmR5Xk8oxTZ6XwS9M4ojPZR/Eavz2vQlPvvtpXn/NuY0wYdgfXI\nZsuGn04ZCgERTqKBjvUrYyfnkVmKWOX615pjPGlMVG59Zu1plbav/cDPTHLe\nlhmM\r\n=Scyf\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2": {"name": "@npmcli/config", "version": "1.0.2", "dependencies": {"ini": "^1.3.5", "nopt": "^5.0.0", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0"}, "devDependencies": {"tap": "^14.10.8"}, "dist": {"shasum": "aaf0633b637613e31cd4dbcb3dd0106752ee69ea", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-1.0.2.tgz", "fileCount": 12, "integrity": "sha512-mFj0a+qUmeqEOjVxziyGTM+k0d1d0Dpbm5Cekaa8eOuomhQhekeHXOh9tqMeQh4fMENAKrFqKEWhH95frtB3dw==", "signatures": [{"sig": "MEQCIDYRMeqB0DcV3fznWPqWnE/MqHbu5cMDWvSqlT5BKJK0AiAz1pCvBfqO/gA2eW4ZJimSkAmxTDbWudNQ6Fh1LAJobA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40005, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfOjr8CRA9TVsSAnZWagAAUEwP/3FpFI4ooz+b86bzq5OR\nsccMA5zj87wb8zCsrGy5Ka0oOUfR9y7iE10WXlfVC3+WoCaJHyw2fn4zxOPC\nYcnvyl10iHgpPo4silgdrapHSK3b7PjVahF49j6E795g5EpuN0ef2Y/LOkFE\nmubMBsJgmgTvGB7CaRMVFX8bV31xWyWHOGIlCXNjVbwLgaacD158HvOHql8t\nx/F/dUEo59Bc9fSwVuKtSeH0Cpy1SQ3BzL0Mpa2Yw/TQ05mQpr0OKPiYpbwL\n2q+z2QgTflvfRIpx1sdqVGpAV27NR8wuOfYUX9C8Wa28ta/jQlrAkYqIs0eN\nroE0V/dgHXa9le14X7lI6yTGtB6EKd+k7eTm+24JUTMUAw5B19PgUHf6Ginu\n6u1Ibs64rtylW4UCUFC1cxnqHcKMGRM037F5cQuHhTHcrgXqm5vczYoUwls5\nYbkCMUWIfXiOaIewHPEKnzrf25Y8/M0g4JfSi/DpP8CMQ2/YAOHVEeEJmLn9\np0wueS+SX7JOtKzsQVn+Y6jgIJ2fCmkjzgAFdFcfdZthAAXGgcTI9ULIv48v\nNjS6qSpqNA11bDx4rqqcUnxsa9quEZ73TuCc3RBJhcjeRugJOmz0+IzlNy5v\nZgQBan+u0UPuAqSK8zU50m5XWUnro6StpwSgBlkcTLEaSN3NXAwWjRoGbCaW\nDMjB\r\n=tzou\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "1.1.0": {"name": "@npmcli/config", "version": "1.1.0", "dependencies": {"ini": "^1.3.5", "nopt": "^5.0.0", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0"}, "devDependencies": {"tap": "^14.10.8"}, "dist": {"shasum": "e873480a1bd9113a9d79b50e13324d4097fc36d0", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-1.1.0.tgz", "fileCount": 12, "integrity": "sha512-IOfpnauHXvwlGWqXfejqmNliIJPYErMwz71QlSTpI2XfXmxatvaf6roQcPK4dq0GAiesDU/YBaHYE8xGq2r/jg==", "signatures": [{"sig": "MEQCIQDG06qD5UFwSSVMu6ijar38Hh2JBGRTS6yMgEaK7A5PeAIfET5yIRHGAvkjPBoUT3AvvwkiS2VMSkqrBOdw76mmMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41152, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfOtsjCRA9TVsSAnZWagAAhuEP/1zeHzc7Z/vm0dD22gGZ\nWJl+KgQSV/RrBXmbNNHnqGSw05n6x7siEvBTocTmWA+Mesgqai39q7B2dLQQ\nKL1hqfpEYWIgWP2TLL+bSru5o8VVcJ2uVZPEgTofvNQAva/x355HqDRxGKdg\n9d5jQ8KAWlgIqbfqAgumu/FTprfImkL8AeP60q0W6Gztpsh3EPjZASVzDwO1\nnm3vg2qHaU500qHXYAaNaK0Gn3D8xxR/wMoU7CoHNM/m8MJa2zhOcvlC41jr\nab6kOMus+O5ewUrXTUGhfKhXlAx9XUnIks1y3nifwzpW0Q9POnsjFUB6mE6X\nPrqqFfIoA0ZjDNq2ZYvq+6YRwZDMGdDUHnfSXSNvhm7ZGEcxhg/IIJ4kPMs1\nuSAXxwNz2GGHTcbin44KrY+wPKnG5oH5koQixnNM7qVOkAGF2LFSi3ZV6J8V\nefdorwOSz4mlXDm1g9F/5G16YX2ne+bVkKFvTfvohswkXNGTrCIsiCGc9qi9\n8Bu0pyuKCEg9zRLvoaTtuiBvV8alzuWPvAg42OmI0nzZ3h62Bl92K9hDvWwI\nSy1CrgWmcadDwij0fePXrxksXDmJmb+qapotZbDcOqU9umnKkBgPofNUzLSs\ngRuAXrBwlT4iPaFt5YgSDcFosyON+qBWsq3Irs1971/+XwxBjv7ytriHJ6ON\nqKxz\r\n=o3gB\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "1.1.1": {"name": "@npmcli/config", "version": "1.1.1", "dependencies": {"ini": "^1.3.5", "nopt": "^5.0.0", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0"}, "devDependencies": {"tap": "^14.10.8"}, "dist": {"shasum": "432e97855b54ce33bc122363c103dd1826c65b7b", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-1.1.1.tgz", "fileCount": 13, "integrity": "sha512-VVXpfi0SWOvxvgL9EWncoekt383pOyfNKZSTQ382x2IRsIcGjn8soQ7I75yqLsK+snOyEpubAo0K+V1tNx12+Q==", "signatures": [{"sig": "MEUCIAptb2CEuXJCtpQWfcINXZtvpRbJzEtUqY1Vn0x/BJDUAiEA5ThKNCCUcymYSUgEL9E0K8XjSwPlJkhFZtmtD175VXk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90650, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfOv2GCRA9TVsSAnZWagAAMxkQAI/pgxzJ4ywrPggVyr7W\nUA5qVpznum8flE9gOuWXAJmnv1mk585WVbuW4V4DJMu+mkyg6P/b2WwbBRMd\nXzenA/sHxwZnH57BRuNghDuArw96cjTcuK0Q5ieR6r0E4uP9OMvPVkuYatVl\nRyOFiH+XVTkwMrICC4qObteunzYV7pjaGvnLrSa6UhTZeJC7FcO919O0Hn6D\n0WTiISVPe9asxVsqgVCClusnZTbKXWPLuCEgCkZwe1GPL78EVW+3K86kkxpi\nX6RbjabpSOy5CK0T33QM/xyVjUjpXeRrZBt8PIg5ri2MVFjqFXmxtxcuMGY2\nu5Q3y57Mn4l/iZsvnz0Gu2F0BxY5vDlrtS5VZind3RfAs+YbXHnylK5uxukJ\nnrhwwKNGlaGAEZ35UP2h0R0HmG4gNxQ+ntQZRSY03gmi5ky4tTRG9XTnpW0b\nwCsqos+283KGMawQqnXRhnv5i9H357GkxUOJRxlc02EmSnTebkmiUBoEkXpe\n6++pappp4T2zrgL44uieRJfDfMNMxO20cxY0YH7WWZ5F3P07eVvCxfp+6Ew+\nwRSPwMQitPU41i5wUsEQHsfgzHqIWmEVL6DyFl1iXSidURAKYq0yxs3IuXAH\nZ8lS+f6p+1MHpGgELvzpgHc40DT51/CajKQp+AZss+EvDUE9YWSbeJVEyyYY\n6jLC\r\n=OuZZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "1.1.2": {"name": "@npmcli/config", "version": "1.1.2", "dependencies": {"ini": "^1.3.5", "nopt": "^5.0.0", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0"}, "devDependencies": {"tap": "^14.10.8"}, "dist": {"shasum": "eeb4f7da50f3c9c2320574415a25367a5b50dee0", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-1.1.2.tgz", "fileCount": 12, "integrity": "sha512-VwLgWkYEqOm3ia1WWz18F+Ti5Dtv2ngILegiXQT+Cd9bvuu/yv9BLao8RQPvvtx+N31NQlm8C5ciZzY4+CbBOA==", "signatures": [{"sig": "MEUCIQDYLxps7IauT8pBicdBM2lHtM9/tzfSHCTqUVpjQ0IdDwIgNAwQoPj1RCP2SlsnP+E13sZd6PUf5Pj74H0F9zdoj0k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41820, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfOw2kCRA9TVsSAnZWagAAG5sP/ixLv756zNG5APFon0AL\nwQ8M14pzshoH/Upuevln95lYDcz9XR1xvmZyU49Tv1j2wPWho9c3ARB8wM/w\nxg7e22fvcdi4IxtmomiQ/ElUk0m1zAaBSnpsgTFTdHMqnUiqDzWaF7qc9X5d\ns4I+YxrfTPdQIr0YoP4Ym6VByscQRIDGvnDZkDrrJenKL31DggxlwJHOAITg\novraNk+0l8OR2hPPFyZwuc9JZd7sFOFDApQm0cFXCMWmIKd7w7uA7VO/1IQ6\nY6bUZHELAsnjFNOwj/OhBdkTr3XsNiUMHJgu0ahu5o45NvOM+nWpsJxsr9FM\ncx1z8y6BloszY2tpVJj234fdT/md0Yvum0vbIRcuCRb258yrkZYiJOBFbwoX\nFnIMHPOZrvCBEhjfwZoarH04XqlKC2pPfgwPvcIg4U5ur05eEZEbQ7L5BPR7\nVaGhaP5J4fWd9HCkhNsByTLvIwxkaB5aRsDTilisT5S32OqiWiGeL9MXXFRW\nGM9ZWlclulH18kteb4db0A+3VBUte7aPnXBYU3K4Dx6Vro2SyKzGWhx/YL1d\n3XmbC/7HQbrHNqOjF1SuHFadTtg7ldG2Dsje+3mzPdGtqEEQz4KHk5GW9psg\ns1vjnqFTIXdHeMCQ3aSAAWSHrcAo4hrMEK2TeZyEOcawL8s1rb3f7/Hzvfsg\nOarh\r\n=AN5z\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "1.1.3": {"name": "@npmcli/config", "version": "1.1.3", "dependencies": {"ini": "^1.3.5", "nopt": "^5.0.0", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0"}, "devDependencies": {"tap": "^14.10.8"}, "dist": {"shasum": "69f43e4fa1a73f3ae4713435ed2c72df625bed22", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-1.1.3.tgz", "fileCount": 12, "integrity": "sha512-g9/iH/bfepcskHojUsbaQdQgSnIPNS8FXgt+6SrnTQXyMHmxqkUwTDG4lrW97BS/jawK3pJnpUIOBEJaOddMbw==", "signatures": [{"sig": "MEUCIG9T7povropHPo7CUeBWlHPkDDeuicsPfIoOkfHpfUxRAiEA1KdiawQacuzwkgI+I8NmegYwpbXv6z8k2O8+kSVqTKI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41894, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfOw7LCRA9TVsSAnZWagAAgbkP/inKRypPMdsmbi57U/Za\nu8QsnG6gYXoQtoHaaDMGEadQQLkMF8wvF1C4Nyih2aGO5kSn2nhlAK4udNur\nOqrorzz7t6Nh8knoZ/zZOdTEGm+GTBdlMUKLDN4FBwmK+m4CdtG+IOVO0XX5\nA1TMDbtWY5kpcRbeYMraFHSYszFy2zelrdeyrIaOAgxUt7xOR/f48l2H8Zhp\n2T9I5t1ZYKNoHz9eUvhuxH3czRrwPX3DzmEDqEq/f5NkF525JvBEtjKnT0uM\n3FCLGzjbPOL5lYYEgHdx+39GWL4Kcd0SduYkjw5DgkB8VkG10u1zlUfXDlek\npbAus8cJlj1MODPN3t7lFJZekEEMZeLeJRzANzBtHVwNyUw34QYtQiPlobQD\n40/ItfFhSgeJSIRZqB5ZU4eB67BjmgHrwDRZBsIpk0qdgeqIMj4bqlEY8x+t\nY+9i2bdEpRYv2FzCD+ynJIRVfAe9FsSuKMkkW+ChtXbnVz0xuY2hoeYBD/ox\ntDY2KUNifBAnQlK03zaILR0sBIRsjGKEVxiGO6FMFzaCTaB46SkUlsDG9ubk\n/0BnEIWudoACeEFE2k6yvx+GUcmbwQ44F4hfB/eEFzqzMpKk5oG49deloaK+\nP4Bc+P1IJWtSlc7wIdHSKNTfru/dxXihIr1hlRhHPkKKzLJMHJYtcQk5PJvc\nvV4H\r\n=1p5O\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "1.1.4": {"name": "@npmcli/config", "version": "1.1.4", "dependencies": {"ini": "^1.3.5", "nopt": "^5.0.0", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0"}, "devDependencies": {"tap": "^14.10.8"}, "dist": {"shasum": "a76e794a305dda408b1ea3d353c4a7334decd2d5", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-1.1.4.tgz", "fileCount": 12, "integrity": "sha512-08hINRBFLaVlNNXFfgViejnDMfCpPgrN9iYTRuizCr/PAPhGkQ0lAhLjtdKN9MeT+tDwQQ9k/9C9B5F5/myN1w==", "signatures": [{"sig": "MEYCIQC2cT4E3ZmmoZ7p1mvM/kudZZNnqkz3btE9dLMLJW1uyQIhAIrdYX5bDxzg5GRwrbKCSPuWSyI5aVHjWJschzyQ+zwH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41979, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfOxDxCRA9TVsSAnZWagAAGZAP/iCWy+4TRxQDZf3BDxr/\n9cYMflZWkaQrDKBwTcgfXl4Riek14aMtERywhKG2QJRdrv4QG1FIJmkzRxEV\nT2ZCDMn+o1VUzPCDC3DqE2hc94cDIGA67HOcGATpmvE/cmLOLR2hGk8uPZWl\n4yriEWBD2v58nhGukM6VVQmkj0r+hYenr1FyoTUeYPMg+SGq1Ip9rLdMC2SU\nyEjCZC1fGquyFKysbzKvke/jFoQ/hZXkw2WWQQwp0aZJ7RJFbMWEKx7WFz7z\nD5tPehMgeQCQXhXqfekMqQbEdDQxjV3WOv4qQaHWHDkv8Zi4cwhsFHMeItN5\n7CvRkL1ArC1oO+q6rv6fP/QtMl4KBundLgyI9g6CGeclgPuR06nx1DeQUHWX\nOXBo+boq4FNopH44M1UN2KDk5KMQR1YM+ZMcZ1PaGh1qrKe4QypGMcdDh0lZ\nC2ozd6bjbvFl9C7bh15zpebzCSIzRnDSftJVrNg/Tp8vpEMWi0O2zhCG2zay\nuVh4FdrmX1r/M7Ffhuz+bZBX5EPTnOkR5+LSpbIIIXDtMggO83ijC5yVPQTP\nuAnKGJGvb71e+jmn0AIMbsq36XvYr/d/By8OoqpKnEo1Rw7eU8x1o7FcEkxS\nPQvlVscXzT7uPPhYhx2CyFhXbTszMJkq0k4Ub5Iij8bDRPT6U9yoyX+zQQr3\ny3TB\r\n=GT4j\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "1.1.5": {"name": "@npmcli/config", "version": "1.1.5", "dependencies": {"ini": "^1.3.5", "nopt": "^5.0.0", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0"}, "devDependencies": {"tap": "^14.10.8"}, "dist": {"shasum": "aea7c0e17cf4bfcb536fd1100607668c5036b2c3", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-1.1.5.tgz", "fileCount": 12, "integrity": "sha512-pPs8/b4zr/9qI5Kh43mxS41o4V16kfq78AA1ZbDzkr07a1ve4vGSAydb04rO3F7rnQfSebmKtHcwRorqfdDywQ==", "signatures": [{"sig": "MEUCIQCjuDMcX/O3HqS5s/CZk6fxL+wYWmeG3xafgigSOJvjUAIgb5LEP2MjQjcQFJ+pxf1Nn3rnea0vErrdSOqy5RdUasY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41951, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfPsZwCRA9TVsSAnZWagAAqkYP/2JeWIrSWI/aO/h+4Dos\nBn10KMI57HOMwfh/EqmiTS38tLTOkDmmrFijSX35beemZ9J6wNEMo1nMyCsC\noNrVBnp/m5Ult3c57K+UeNRsD4fm+n/dTQkfKtEUeCXw4XTopdABdL0TTvrA\nEXwgdvdBifQxq+dHl/IqyZnlvP0y3WgXanJv+uuA4feJqWNpOweU19o0z3Bu\nT1Fo4m4WTRzryEAhLlbfLS6kGOYWYDSSBJR0vkYy/iz9GTg/Xtnzx14mIGXP\nuJJfKAof01+r45pEVxYdv7TP820zNHMND/q1uLJC8+T2TQ3i5ffDqu6Z5gYh\n4MZ8y8VQcU0mAA2gVoeSM26WO/63M8c6x7edlIw9S9sgeAf9/gb68V2SXTPa\nwDlUyqW6yASgB/rqT4sNLAvgKQMoXnbuuQXvVMMKlx0v1GiZbNVlyJMF9Gdi\nYmj9wErMlHPodfJ16jzGZr8MTMrZX4m46bspGAHCXexIUHrqX0U28OUWbZhQ\nrxfARHyW61ha4k6LbdgL8eXGdACZr+4g4WQm2rHo7fKI9kKyG4SPdOxeQL1h\nKaISYQYheljUdOB8uw4d9OzptPDXlWXbnDh0kSH1Z1fr1f4PMSwpjfGX97SQ\nXeFz2uet2u+BU/4iOKj7smuvEYItOYOUjxe8hYwAOzaQIXTkdzQJ4TTpcT/p\ns4AP\r\n=DZW6\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "1.1.6": {"name": "@npmcli/config", "version": "1.1.6", "dependencies": {"ini": "^1.3.5", "nopt": "^5.0.0", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0"}, "devDependencies": {"tap": "^14.10.8"}, "dist": {"shasum": "c8d20c45828ef798c1bba5191c73654a2f58bb0d", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-1.1.6.tgz", "fileCount": 12, "integrity": "sha512-dPckO5zjL95CvfQEcju4kS+j8NiwlsMi2Yq5AjxAdIw1F+BUP0RsFtouboweP8EL65bfawqpD1HsPvfWq7wtEA==", "signatures": [{"sig": "MEQCIH18FoeCQzC0b00JjG9BSmXo18wHnxcrtmivzWBEIIEOAiA4hn2uOwyUAYNlUgP2PBBVq7N+mNMt/oFQYnGu37xGWA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfQEArCRA9TVsSAnZWagAAl4sQAKSquYJQHxy8ljNrGYQ1\nVqPhFJ1rg7laYtqSTMoq5PLrgdJ2LBGQB+4pMFuGiK12vFe58vjPnJOi+Bor\nw8DdyM3gDxfBAiKHkTWmeXTx4OzTg8M8zCuQiKdjAXA/tElpwZqQK1NGKGIi\nGEoJt4KdaBSD4bjfVFbkfMsi0DdBCdC1rz64jWXeTSu2/ZPUAKjE4LbtAdAO\ntdQ0Wo4EhFsmmdJ2s+qzlD18pWQ9i6qvZmeEBbaUH4WQVm1Wm/snHe7gt1Dh\nfZ24H4qhYLezyEB6GJ+uvtt4v8DQ3V3SKArb5X1LZP++nwaiW/LFgH4+n25Z\ne5xJp6jqK0pSTpE1rj3lZeB8JBWx8x7C+P9hM4EbE/Qn6R3SK4z3Z6OzjEfm\nYxatDkBWbhf61KK0bBtTibZQa7+HrASEThWKzA3dscny2OZJVRYZ4zroBmIe\n5Qs7+kuzAW4CFSnsDpvWmDu0oncZD2eC2EKN+5rxhSQXQk9DT0n9NBrN+fz7\nHiWkjK+8mG6yCrTubKPlH9HWC2dk7Y/6R/s0bvdqLpxRrsrNjMun5vjvF4HG\nOxkq1VjVQD45DxDCVuKNTZ919l/qi6E0K4QSRYk3Og3FQeQ/gJUEAgVo2Tod\nzhO5wiFXNpNYIKa11MQEg6IZ4lOIeM9aVQJxC3cxV6OA9wsYj4p//9/m84G5\nZBeW\r\n=VNkP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "1.1.7": {"name": "@npmcli/config", "version": "1.1.7", "dependencies": {"ini": "^1.3.5", "nopt": "^5.0.0", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0"}, "devDependencies": {"tap": "^14.10.8"}, "dist": {"shasum": "690dbc3002c6a7dc31f7b5c42b671617eab76dc3", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-1.1.7.tgz", "fileCount": 12, "integrity": "sha512-C5apJ5CgpFrqcR7vDG1Zd6fuwQSzkvX5r2g1CCDuSnTfT0kj12SD+r9oCFORfieSfvI1l1oC516Ou//g++h4LA==", "signatures": [{"sig": "MEYCIQCNRVZ0+KWv54yuOpyRbJFSk3gskdYhFIkFBXbghUHCEgIhAOqOfX5icb8rMo9M9347v0+G41xa5l0sOs6iFVIYlvOq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41967, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfR1hcCRA9TVsSAnZWagAA1yEP/jtoWhazcSG8FOqO9CfM\nHj+JBnAAuQ7p948AyEhIQ2dGPvYBQMJ4/rvedZ9NFtusPLZP+KoObF94nJAA\n73VpqbkIL+urUoa2++hMvDjWmGbecbEh7XcOfswGBlfeOe/KPADgCxX8FJ91\n0y5VwFDE5nfysZTh2XxjieEXswDOFIHtz12euXvCzUUxYm0+TD6D1Ef5iJam\nJmKg3leTPlvFfizoPl1C2a15yhXTzmqzbaeP+/aGAz8mSFuI/RN9G40fKKe/\ngs2d7/Xuv5PE4J5ZwffJtFluNKgBIe+jSovrFi3oHCSz/HmyPRksLAV7WGND\ntB49N1FEwM7vJxNQhQGO6GbHQ/QwLYfHykTutRiJBJ0lrJo2h9y+eE/FlcNs\nWgT8JD5pI3xuzVnoXq+uiHiUZbAIPf25xZI/2ngUlF79MkeNnRqMPhbGbrBy\n5jHcJlHNfVxthn5Km61M01BzcvXWmRUHgMxCtiHX6naEgJddfJDA10v5bFZy\nzaxz0F6zW2hSc/hJmPi3bqIKTVvjE3H42ukRweBxuz5qABJuNCN3acQSvqE6\nijYJfjlEXzj3PJGIfw1m2T/Ah+OmjrFRkLuJKBE9ZXQD6wNbDXKtgrIf7MbM\nB0lsnWT38MOrIGuwKaKEiJ6um+XNep2QGP9jpjxilKI2O0pl7F7RaJV6zNZT\nDaqd\r\n=6WUP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "1.1.8": {"name": "@npmcli/config", "version": "1.1.8", "dependencies": {"ini": "^1.3.5", "nopt": "^5.0.0", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0"}, "devDependencies": {"tap": "^14.10.8"}, "dist": {"shasum": "51de3e5ba32d7c2320e3fc6d1887f081d88b5c22", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-1.1.8.tgz", "fileCount": 12, "integrity": "sha512-hUHm14SXdn4E2JHdWGt4meejDMVsyIDUoDLxxXgoHOTmOQQUv654zf+afNRTOAV1UqvLuAJDu+WtpZvveU0grQ==", "signatures": [{"sig": "MEYCIQDIfdSpDJaTND0AQkse6OjKVBB60fYq8w4lzxfCYPVx3wIhAID+xOSPTsmpbkA7liEs4+pjKMJJZKomjLQwQTyQ4H1Q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42040, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfWu5ZCRA9TVsSAnZWagAAOdgP/j+tXF1sHivoqIZ15Mz+\nGPW3aafpmHDx/X8OvMOei+YIo+A2Z9OToS5IJiv4pVMCCkQF3srSHJtTuDqA\nHKc+/EOXZw/l3ev8w8QWcPxe0yvRpd4SI6LAVOLiAAOTg6us5x/NBA1Ai7My\nK6d3Bnp/4ufGEX3KeUwH4tooEmWPtt8N9XgTOoCJM136L6IJampail5rs4oE\n67F9tgiOX7H7WqlRocrsY5lRy7XENcQVUT0aCfmYSLTJM5A0Sueum3dtkwBm\nJOeU4M0rivPUj+O+JTa1NO3y7t3eOPIblM9z8BtEzHc7c3hLUrigcwLt/JY3\nZmwWsQ+U5lsfy6f8d+PiF59O4y2nNL/qDNyKibmA3eIVS5OXZIeLqAxW97Fa\nuENYr8f/4U7hhYT8L9C0f9UCvFJmxvW4/dOEs0/9rkSfJBTIb7781x48DX3T\n2rHKJU/rsR7aRZawkuGy2mB0ELm6ifLcFZ+AtnOuFesNL/u+ToWMpvmQ2PHL\nJpJYJPQYaOJKuxlfTkZ7WdG2V4iMkQ4ToyP4ElYkp4werX5v7u2cRJjn8GTM\nz7J39yCK53IG4Kpx1dTMWy9zdwKNv0LXTJvgfvdeWqGabUuUEuAfTaUe3No6\nDKQuc81cTIanVzZ8/MkhqSUOHvn3I11Lqv4IJ5OSMZeCMQ7xrlXedeaVdXtM\nivPP\r\n=pKqF\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "1.2.0": {"name": "@npmcli/config", "version": "1.2.0", "dependencies": {"ini": "^1.3.5", "nopt": "^5.0.0", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0"}, "devDependencies": {"tap": "^14.10.8"}, "dist": {"shasum": "afa1e1df6368c19c07fdce968c00f4b32034f262", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-1.2.0.tgz", "fileCount": 13, "integrity": "sha512-t9MstrufHzA3x3y287dIhd//jFTvGEdzeipT/sEUK/x+1VdtAI1DMyRkrqYiqgcq3d1XT/TOlWvRJ0upR/TyoQ==", "signatures": [{"sig": "MEUCIQClerLay9tb4hWUuYo3wolS4lqq1snvfPzEL3kOb4LijAIgLKVpE5pnxqGjnhEsdSbio4G1WFiKkxqIZZtBnQR+KJ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42892, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJffddeCRA9TVsSAnZWagAAiewP/AuOnYhPAYPO4VKFS6u/\nSzg9HJUoxvH0CXqSusHo5pP3Jc5GUgTV3p9nVmjd4TFRoZFIYuDVPltjhUSA\n0A5WPxMm+1PXanjb+lS5zRFd5H5W5MwdJvjLDklvzkzb3sp3Vgmi4spj+21B\ndWZDzSPVhIzmSf5tIFhrweLAYvTfIsWTZX7L3vvI8D7rJttqNIw7tptx58UF\nRzNBZJ4LPz/JtzV0z12e/JBoAPKFtfS0bjJQLNAKoqnLUeFsoL0mzhvz8TVe\n1pk3dYIMbQluSNhQpRJ106+370kyVObKbBabouCNyIeUrbc4WTqDTm9MR2yc\n7gr6musntkYsg9EVSgVPlM+NvlPSFGJwhHfv9hW4BDT9bgwsBHT4M/s8zjHC\ntg6n0po8y2MJPLMz5X6pN/nqvCWbTPTvY4wvskUURONzcoodtdjeBa9GHkYk\nIHCW60XjIoivJhi5Kq5cN5Eqedd9nNiVP7yJGWWx9WfIflRbLTrYRtF7oUP7\nSalgrRraCI7xJQ42uZukblCPkoJiVP0/pc8f+yjRQ6fxii9jFRNneDveqctM\nS00CN3jw7sgoujjOK7mc51iFFy6aWMRWJpS6ZouaRZmfJErZbl64w/VuN0ZS\ncPPEDqLIL5nOg28VP1oR4dJ3pa2pSzWmKnRvyDVxGbB1FZ2axlrPMOvR6GeL\nj/xB\r\n=hIrC\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "1.2.1": {"name": "@npmcli/config", "version": "1.2.1", "dependencies": {"ini": "^1.3.5", "nopt": "^5.0.0", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0"}, "devDependencies": {"tap": "^14.10.8"}, "dist": {"shasum": "ced9b3ad1fa69f4e27ca6af7b1c67950bcd0de8d", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-1.2.1.tgz", "fileCount": 13, "integrity": "sha512-alNV1uD+Ms5LIX08e+imTmHbyzQCMEyU3nS29SQuX3lQsl93xf0sNW3JnHTGGcBxZlX5z0pRaAu5XtE94rdS9g==", "signatures": [{"sig": "MEUCIFGVDxPdnZd1QVj3ifcS2/yViHcJefTBRmD2QgI7ha31AiEAliOMjcuXEhaQu0Hh7XzRWdH8Rq5Vzl9MqusjYM1/kh8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42900, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJffj0jCRA9TVsSAnZWagAA4pEP/jqwFViB1UyebE72qzCr\nR88HgXQEPzER2k/NayPn6j2RoonR/MrqGrsaJNdQijkRs1Wug7ODEfpyw32k\nzWDgFoeMWgLa6FZi6yv52wyMalYy6CyjIZRwfNg6K7/b8Gm7GCScxkl+gmNe\nY+7z03svscRtVJ9QhEaA0j1p+cMnB5/qFZ+N/lVP9pnix8fNPufDWvXqYrMY\nj2cFqTHHgzsqauJ85QbfaUfp4i+EfjEdvSg1LfggP4BM5sDWcXmKGrw0wJ+3\npqb4mNJ6jZpFUUYrmeVBJP8Q2oZtzytJLJlp4EzWqju0QinqiBbWnx6/0taE\nIoxuOyDOA7MTwQhn5Pd65++VisYxqppnM2lEfg9IdUW4dlN83E0rcD7og3/O\n4nlkeRy0Ca//ppx0SxYFahqH4j54xrSRwjvrFWJ/nsJkz/9y8OemFocKVPk9\nQARI7aOzzJwl855DS8lKY0OryvUnp2Jl2ev8riVibyMm30uL4iMARgetmZ8K\nzS+oizgwmmRoId4I9nQW6AT68Ocf9Pb/7+24zZFlOIyJs6eddKunYrxH3bvn\ntQ4wWPa6zDd/pCKbF9JSAHHjNZ44Zak/DiL4zMHGDf1FudduDe1P22YvRs1G\ncy02xSzWYvrjCCJjbXEfkSwBfS36I5AxU14sDyEaCChY7+NGAUhoRQq5HCLs\nD9dh\r\n=rf9P\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "1.2.2": {"name": "@npmcli/config", "version": "1.2.2", "dependencies": {"ini": "^1.3.5", "nopt": "^5.0.0", "semver": "^5.7.1", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0"}, "devDependencies": {"tap": "^14.10.8"}, "dist": {"shasum": "e1c19f8772e7217237e249e1f79c47982e9db2ef", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-1.2.2.tgz", "fileCount": 13, "integrity": "sha512-K19Sjt03Gf0qnZ/xcXOOu2d2NFV9SEOuib1ByF7pCf0cHe1y+r7tUiKGrXfDC18WDHnysZIr0sw9+wciOnc3SA==", "signatures": [{"sig": "MEUCIQCKbhOENh91WidnJBYKgvgZCX4tZYgqX+Ph++3VFxzvrQIgVtntSAaCJDNaDvdN3e40MfjEMMgTylYVw+qLM17cFNw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42924, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfxpxJCRA9TVsSAnZWagAAnwoP/0qpnS9T9fPbLmlyM0do\n5dNqqtL+iAw/PaJNvbrAMMK/TKn4G8uQ0FmnllZb4T73G2Hr0TBNaDZ+zkHV\nIM7hyPQnNQu6xLdpKV3FcDggy2wIM5o/RyiWePcwzXLY1OmKy6LyUE0SUg2W\nNdGv2UttNMl7jP32wllyo4Re/ok6bz5p0hDigx6UQoP/8hYgN/LuSw8nEQl8\nlP+/4mpC3nq/2zv83SzdnSOqQ1+A/x9E7udFxgOENGZ+Nw2V8PzWHpixBXG3\nup1w6pAuTxObdfFMdWgbDQuwig7cSTLbiuYGh0aWCBJxJzdilDWjS3xhwGD8\n7BXygLD23ZkgfbvvknNEdlSq4s33Vc1ZNt3wGQaxCohk6kE3nC8VGdH3I6s3\nlD3iuS6s7KIGVwcfZ+i6z2ZYy8jd0EwM9Rmlj8uOxPsP3yvKGBzKL2isvKVe\n73UTsFdL1AZZtExIEb2x7GLEGnqj+e2SA6mAP6YMIqyEWVqeinDhteczQp2k\n8omnt5S1JryyAQYY18lKHid5kotPnvRi2D1yEQhWYDJUWomtgRt9BUeY7BL1\nMSMUqzMg9WGIo244VSuPKTCI+4kcV8qkmkmGcl8cfl33LuQCV0pAsIAmYquO\nuytCptJJVH7YQ3d3MtwIWPiwiG5XV478ks7fw9U9VTlq7MMdnPDbKRronRZZ\nFmVC\r\n=6dNS\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "1.2.3": {"name": "@npmcli/config", "version": "1.2.3", "dependencies": {"ini": "^1.3.5", "nopt": "^5.0.0", "semver": "^7.3.4", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0"}, "devDependencies": {"tap": "^14.10.8"}, "dist": {"shasum": "a552795b48eba6691e636095bd3ff01743eec2c6", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-1.2.3.tgz", "fileCount": 13, "integrity": "sha512-rRggEgNdsG4QohT+PEErKKRqyfxqKQSs4Bm46uOQW16FwSMlHWrJo7NEB4oNrgRwjMU4bDFjuoDWpot6DQi8xw==", "signatures": [{"sig": "MEUCIE64MLbJKLVdnPG/kyjQgrEGcSiMuW0mGJfKbqo/O0liAiEAjV78/6ssF5RIxr2mMh4ukPqcXxpi2Lxb1WYEbyJe+aQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42971, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfyo/yCRA9TVsSAnZWagAADy4P/2F4O5LUfPQy4S4Jq9zg\nGqF+fqsDnbI6MvG/rFtm2WQ0mMA4/8fu9AJzEw0R223NUkS1r3aovts3/n6l\nKpQXBQX7hl43YrEPyFxX5V5BIhcpUr3TMTvvK+LqTHvylhM350f1ZKZmU9B+\nPmJ6CADmmlu+OozpyntcuoT5BPf8xKTx8VG1mU2XbdntAkjcZ/GKHSR1//1/\nxU3iQ2Mxce5hDAoib/OV9CiN4tykn1fBynRmk2TXPeAg5FPNWhQPYzMw5il3\nkB55lYvbcyIu1EplT/1Z9CnGMDXYCbIsa1ea55wYgWdOKaPO2DaFRF0lPUHw\nV43f5T6Mp3YXKXvh2WptnI+17sTZ7ss8kCkyes2JwWgIerU6Ajh6wjWZpCUp\n/P3pzM8KlfuabR9qFk0l8ObFAB74ifKGVcZYGA7r4h+7+Yb3WHRVavJqyn9O\nkoNcfxFEHvoBLaEV6Qkel6Oy/EoOAj0T76vzs1Cr1pf2C+DHhaGRgtfLflj5\nstGcJn544ewJdDM7ivfuOdf2wHwyz5cmNyQ9EYXNLL502T+5++mgTAQtjxwM\nTbeTtYc7q7s3AsYHwH4rrMrumfjvm/1dPJGlNsttvrhhUES91yZHrJmuMUrj\njnl5mZRl4STRXxbGwSUFftHg+t1DSn+5qK6Zo6N7rPs1yfomW0d4fa8pnlDE\nTPKB\r\n=VUKS\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "1.2.4": {"name": "@npmcli/config", "version": "1.2.4", "dependencies": {"ini": "^1.3.5", "nopt": "^5.0.0", "semver": "^7.3.4", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0"}, "devDependencies": {"tap": "^14.10.8"}, "dist": {"shasum": "5cd94c6fb2dff4c11f0736c13a16844bff329017", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-1.2.4.tgz", "fileCount": 13, "integrity": "sha512-q68HN9krNkBO93oIN4+FHEwc+hhskGtmniCfR5rY4HX8Bfet85o+eaXFy08mWpzoZDhh5WkkZm2BStcvFp3iBQ==", "signatures": [{"sig": "MEYCIQD4KcazxFxzGwx35yg6q+xW5iaCp18J6mpNkkWTMsecKgIhAKlvmiQuidY+C6h8wuG2WV1G6VZIh4WpMfa4HFj5ncB9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43218, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf0A/QCRA9TVsSAnZWagAAt8AP/0UkQ9i3SQCSuN7nhsuV\nRPTp5l2yJBEF1B6vzmU9dA4lm2FJfOaGd7isEypGZO6VKqa0jGrexkjN2nDW\ncNpW21eMGGJOABhOjwo3Fwh86EA0iEFALUfIrXb+JCkDluSSEiXdKSqODNIw\nNAOoe4hnD1KyeqJVjw+0DLnIH/lrm1JtJfr+JPi30K8V9Ki8HEipSNcTUn0A\nHo6fl6mX08NadPF3vzHPSIqiFX05fjDpznHNYGQE2fRht/Fw70KUiAvIqN7i\nqqvsrxh0Y2SHSKkVLWvUnT9pHTUKATOmJcn0Hiw0OoJdFXh+v5b/yv6F8xzu\nuMGRM1iElBDDntGGOngkqLs1n2mifdS3Qc2GwBf0zR7VENb9w0dw/tIq6x11\nN7BrtNwpWYNHK4gxPWlkHC62Wq+NFjVOrnwWgnQGAGcQnRYGsOFqfaDTM/7r\n31ULIGZhZQ+c11kabN1VMP1NjPo6BYcZrmPZ3G46MI3RiLcE83+vlJLEXhxT\nK2N6g9SSmtjD1SyamYtYdNIrzTc2dRb1Db3+WQ9eux4U3nEYu5txZ8jzYKNR\nkPMPDYUU3FSOtAeU5QKI0vAHc7vBMAZwgHijrJJyfW0fqX7SgpsnjrqV9aS5\n39PM4J0ylZRC5XJJl8f50feFO3LgmF9n8TF1m/Ijfq07qixbWVMzL6yU48qq\nWH1S\r\n=bwuX\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "1.2.5": {"name": "@npmcli/config", "version": "1.2.5", "dependencies": {"ini": "^1.3.5", "nopt": "^5.0.0", "semver": "^7.3.4", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0"}, "devDependencies": {"tap": "^14.10.8"}, "dist": {"shasum": "6f948170ab58f1224b09b5ddda4368dce3cbb9c0", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-1.2.5.tgz", "fileCount": 13, "integrity": "sha512-ov9hPz0cbG5WXitlNptPza7KpSV9L7iuWed1N96N96Wc45Yw7ZgeFVXM7HcSkVXZtfLE8hiJ9LZG9wstOkNCAA==", "signatures": [{"sig": "MEQCIBTFcoKDNoHthEBdiIUTXocfBOc0DjOFBkNtgNgeVFAmAiAdlehWmRojwO55WTdjF4wX4MazajT5RaBsMy2DpDcDFA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43266, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf0qORCRA9TVsSAnZWagAAH4kP/1TFs9o9OBYpSi/exsag\nCAaUInfvgk6TbKBrFNo6hhaHNqCVXjbPKWLO3hpzO+WC9tpotLlB+Le13UDX\nLnQP7lzvKbX9cPeqqVjOmro/qh+qeULiTktBTQ1VI77Jtjchaws2S1ckDnVG\n/MZrdN0SJt7UYoJr+P5eg8p56GPcCtn2wobZOsvLSi2HUMmZDLDfipvdxOcp\n7PY5Az3OZPBblgQHOXKk1pie3VIQnZPKaAK/y4Ka+Vuuv2aky9RnS9b54yF/\nIM+5r6H/+bBv0uOUy2IgF0weeXBkQz/pzbLB+iA2kbAxZ9tNO2v1LEFyY7+K\nEWJova6LqFc0E7yjIQlgirYwUxBgGDcvZ5JiWD4PeqW/ER8p29yJ3/RYury6\nBdkR8lHzTpDL/PBpEuv6w7LDvufLXHgOT2TIvoNiA44gYOVXwoViJl225phn\nf5fCYlm66jiY/YSikDwgIawyh3NLff5rRQPktIecR5KPnsSEEgh5dTH/NaD3\ni7mLyFKqB7oZtMFFG8Hmm/uJsCNjPCQFvNhTOx0GhJ2xxw4qRHPm65zG/LHS\nYqZuXzkLL3HC7nzU1YzLGz122hbV+zw2myLdW+xEj6iHp5mPx+m1lw9bsujK\n5nsX41+TehXxEYBaeddWZXZgngROu6dvduoy0Ez4NE53xgy68VJT2xz5fyuQ\nZEiK\r\n=9B8O\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "1.2.6": {"name": "@npmcli/config", "version": "1.2.6", "dependencies": {"ini": "^1.3.5", "nopt": "^5.0.0", "semver": "^7.3.4", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0"}, "devDependencies": {"tap": "^14.10.8"}, "dist": {"shasum": "fff9ca6ef49ba9d78113507ebec7b96fcf38bf76", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-1.2.6.tgz", "fileCount": 13, "integrity": "sha512-qCH3njpc4El2l92BS1oa47wy1SRyWuQGC0RqiP2n/wbS7tdbQenwbuPIUxgQ9FY4sq2uMVrNQFIgdhMNM+OYKg==", "signatures": [{"sig": "MEUCIQC6d2qXTIt0roISwBsP4vq8BHHYOacshY65TY+9Sd7CAQIgbnUzgAjcBvlS7aOEvaxs5MLXAegupIZFzqbKtClhw8w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43277, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf08ChCRA9TVsSAnZWagAAIgkP/0YNpj4iJDEtNqJ4kOLt\nFW70ofCwZzRaC1mkVbW4haH7vtxuuLk4fh2lL6xeKF0IormBlF/+/Is+ZeL9\n4427575DrFkhhgJOuRC7xebf6d+N+50m661dAnAPyvAilOAWcroWJnbkSo3V\npalagowFHzNzGE2at7fmVUYFcafMVD/+HyTGJ5yc3t+i5dCESYzLoFbHsvNE\nBsmNaYG5EfmpkdGxy34eG7RSKlAJxE9PcxlDipZdAduclDYSPDP0zqgW8Mz3\ny/wjyfkL53RjnSZnzt/0K8iDn7ANw/TMIoKXVsxBpSSasYBTPydTERxe5hV2\nADC4ZXq0irAmL2R1vBKRZcMeuZP7HUPLvZHLRw9Qd9yhrLGQK2VjP7MnMgb9\nhAIjasjxbwJUoC6UljaUySFZbk7GH5Pppewd1bGA6MvyneBurHY+nZfJUKlC\nCZCrrp1m169yJRv1AoyyEfGxEIdK4UlaQGylK+C2DzMIPeV6SJSXuPASUl6/\n45cQgvYZbS5EAA9g43RGzjft8RIIeM1aFOgeHDS3K0WFEaqNQqpBwbO10QQF\nWwNmGfN5kFIZOciCmW7Suzd0Vo3n12prq2MMkawm1jsruks8k4D2zdw0U2oi\nCkFuQdCHTAOLFBjRxtJyFBJDwHITJOlklTTuFlDOcKnLlWNtbLih/ieSpHHP\n6kKK\r\n=XFnD\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "1.2.7": {"name": "@npmcli/config", "version": "1.2.7", "dependencies": {"ini": "^2.0.0", "nopt": "^5.0.0", "semver": "^7.3.4", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0"}, "devDependencies": {"tap": "^14.10.8"}, "dist": {"shasum": "704918572049b1436302c846c16a7b5d1838982b", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-1.2.7.tgz", "fileCount": 13, "integrity": "sha512-zV1xhCK91UegZO03G7BdNSRMCTDVMB1UI31XDbZ8bjUB/8rUuFvbYoIRzZDMyUPT24ltzLQC15Ub2bzgg0ORSg==", "signatures": [{"sig": "MEYCIQDDHqLX045OTA3bLAPggso1KRZWD9dCUeEQ/A9bwdaVGQIhAIzC0GhU+8aD4SkgD5JFAQNzUbzUpN6nifHiSfBGzkFG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43277, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf0/yUCRA9TVsSAnZWagAA7s4QAIUx8ChGmP3J2E/UJCdw\nlYI3H6eTEIrxsGFfoUoRRUfte6MrFsRh4dVktnJVZ4I7QusrpzhoJfOyjUuj\nLkEKBvzTH48KhTwYWrkVG87IX1ki5Oifk+qQVNmGE0Cm9t/mQCpvvYceqzyp\nCgzzMIKL80ZLxY2PWeDzYlOcM2rzze8S5LZbo5huBmgonQAwaunDV8LnhnpN\nnms/7kCqC8sOooi+1aFx88jNZsVeQrtjPvOVZ1a6SMtx8kLvT0jb6kM1mL8n\nBWL1atHyE9UgmdRVbh4EYgKbxIgc0+PdPN8FxFfXlus3tgb/qo1xmo802EUf\nYoWAVZ2BzFqPRJ4xeL6n6i5BUqrEKBsMfPyemfJZyz2VvRvdQaP/ZveUC58S\n0fpQ6Zv2Tt79Iuu3x2X+rwI6AZ88xfbvx+OWxdWGPOJLe/MBF6YTd7c4V4ip\nXZANrF9AA83j+UfwOFlChx6wanxP5qAKvIjRaRNa2GHNKGnkwQ9cAd8353Cd\n7HsOKEQCLtjwPQMrmo4y3SZm1EzoDpbMWsqOsMKRcHEtGp6gjeW2vqk+NDxZ\nmjYmo14lu+tMwh/7zdteizNWogrgGQItzbJKgkQ0vEaNBz8L0QlbzuxrJnsw\naMafpmEV3bGDEEoeJbGPzKE2rCPrrt9RXH5qT383iF9gC6BAXC+GIRPNHZMQ\nq/uL\r\n=LDC8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "1.2.8": {"name": "@npmcli/config", "version": "1.2.8", "dependencies": {"ini": "^2.0.0", "nopt": "^5.0.0", "semver": "^7.3.4", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0"}, "devDependencies": {"tap": "^14.10.8"}, "dist": {"shasum": "3610153eec79dca2ecdc19e5d5f2c207e69b7527", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-1.2.8.tgz", "fileCount": 13, "integrity": "sha512-XFqg1uxUhEiy73hT1Z66xrMojgUOzAaCCYm12bEYBbi3wxmaer8MDRQ8ZViCacHFSZhkLVLqt/osPwKKJPduPw==", "signatures": [{"sig": "MEQCIACgZJ/vCVQApDPAAWFuUN20Ow4kDMXM04CHuASaBovyAiB9gDo50G1zc8NuHMPnaIu2C3mjjQw5wY2fPBRS/2G1HQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43764, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2UcgCRA9TVsSAnZWagAAsagQAJV6+1SllR78F/nxRC6U\nfeb+4eeCbqjekl4GUiKscIrV3TaJyamnUlay24LABTGBTluZfFK2vEnYxWzH\nIY3+Lx5fRpZu/zo/qNU3pjgsaj9H6f73Ty+si0CVCj/t3nM3kzzo/OcvCbzx\n9dDpTBEkE0NynRthX1xlYC2BriAW4lGqBkgNjWXkjNu3kM/gPJS//Wi8jYUc\nqg35ATbmOcqlWcY+tEhV2UXmqnClwV52ZxYWa6wRgcfryjatXwvVVBXG2/wH\nxYMZVs9RestodBRQaD11R4LsFV5Ijw3fzW7ryfZ38ue3lnofFSnIIqrcqQti\ndXLqvdQSlWhqqbbEyQTkpFpKxPSPaNVwE8bd2tSrWSK6JicLjawOjef1NacN\nIa0D2zCFkGNqk88xjy3+ZWXQJJ5c4EhqRe57yhC8D0L7tjbMTRqFZs9iPN3d\nHEkAs7ZLXCIkfKjHr9JK1IWRer4+H7P2fvkXFpuEuw2p6/O/035wJs5hCo8Z\ntyrHCRTqfuO4T6LRQcCnttXe9VkuQLW1ccLApTu0ADxGV/jyAwUdSWN/iLNE\nLztxj/rCuqxo6ZclnH9p9xojTIL+gCBsXPlstc4rSRADyt0d87syOGbF0waA\n/51cV4gKPNjLQ1IzOZuqb4/TflUiZf7shEJ4x2gItqg0zPqnD902WDCUbh/E\nHtVU\r\n=3ySU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "1.2.9": {"name": "@npmcli/config", "version": "1.2.9", "dependencies": {"ini": "^2.0.0", "nopt": "^5.0.0", "semver": "^7.3.4", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0"}, "devDependencies": {"tap": "^14.10.8"}, "dist": {"shasum": "75bde611c08762ab7a9814c076d7f287ba020088", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-1.2.9.tgz", "fileCount": 13, "integrity": "sha512-d7mx35ju9HFg0gNHiwMU0HXCJk1esAeRdMktLeD+K2K2awkZyEm1FyX+g8iuZbmWGAaFP/aGiXo7a0lKlmp6Xg==", "signatures": [{"sig": "MEQCIGVkqLbc7DzZ4EX/R2aAiMbV7qm46fN2ClAjnY65uPyLAiAjqGz7gkVflxBZLZgBjI79SxVUeG46LUnUIvA6UMf3Nw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43553, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGX3tCRA9TVsSAnZWagAA8HYQAKHYFrxVqnjFLkqIwqki\nlZcke2fOFySBSLtcaar3SLn2ommmq6jOKuWm99zMxeKT4Fg2yi9fT3MegILW\nZSY4gTjSgG927Butevg0P6nY+shx673PFsQvcaxWHo1Y6nXUWs9nkdpzfSmT\n5qlVHP2QEzqsQ+WT8h5Ywu95cOvxqgoeaVGFZMyREIhGGZhbxRqPHiqt5OZd\nhK6biwciz2Rysgd53RzLxOcOpm64BegqMVL5ajrYCBlVmQbYsSjiqKKCI5ko\n+sMT6+G/pD7GYCmoZv6J/0zH5fxZLaIe6ae0gIgnAXR8FElg6GrQ1ufLTLYH\ntycmUft5oEHxkxHVjjtIUP55ToYQOqtDtn7GUtAqod1U+LKcdL7JHJADZM7Y\n27z2hEZNtkuCTHqMIOLEllfcXIn1FTfQv4b7u0VHRvOX3fJHx3hQiJeQjzZ8\n1nDzY6rzEusn5dYrBh1ndE7JsYYp0nJKu+J1H7Toe7AJBIUixarFM591Sl2p\nrEVO2NqLtmeGsrA58ZNHYCTx6bfP1F1OBZBZ8hxKi3CyeRk9iJ2n4BZ/aIQi\nOl82F2ThTZM9UVth9XJivMgav8P/A8t0foVB4JOKANVakw+ract1GPjRL641\np20wb18N1gjX3yazBAvZ8QqKq/MPNG+ItTr2EJz7ER8AE3+A1AXpxqAH6Ay1\nKovz\r\n=w4DW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "2.0.0": {"name": "@npmcli/config", "version": "2.0.0", "dependencies": {"ini": "^2.0.0", "nopt": "^5.0.0", "semver": "^7.3.4", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0"}, "devDependencies": {"tap": "^14.10.8"}, "dist": {"shasum": "878f0317373ab0aaefaa9dcb23793ba7df87932b", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-2.0.0.tgz", "fileCount": 12, "integrity": "sha512-3OKVmO63FJRIrcsVI/njCC7QGKSfdCrIUUV+sP5ql8QdaPqXuaFCImqCYFDzGkcrP8PTOS3jbrRsU0Yzm04n3g==", "signatures": [{"sig": "MEUCIQCOn46VKOSGeS90T5RoT6opyQ2k0VTRDX3UrHoB3NyRwgIgf7fuhlDz+u4bBIgd9rtTntos01dLOCQwxyQi93M3lD8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43330, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgU59BCRA9TVsSAnZWagAAI4sP+wcLin8EWkq1wd98mPzc\nAWllLFk0K0ghlzR6K2yy2xszi25UrK4I1cpwB4J/WZgmbtxR2DtkbtGYTwdw\n4qYK0HFW5yUwOZfc7XLjEJcHOC+6coju1bAtFeYCmnICb54PKYfPV/Le5Hzv\npSd3I+wJl9tqcKGlC4f1H+HaFXQEMKrmT6n5axwFIiKXK+ptH46BO74saZES\nGqrelSF6QcBFprxyGlx+qK4awj2mCNWBISOKWkhcvn5DtXDr5O+cKzhpzX/S\nVcCU5gqVXFYwwcNSxEQVlG8UM/fim38XKgaz0EJurkZcORPUpHaEgRTj62e4\nVr6lnoLJkozir/1dWvBR/XFB9SZeMHNsmRa0mHCwV9ZjmxlhyOkxnXRVBpfJ\nK55aYPvCsQL/xIyd6TdzR+dxOFkeM+Pk0msVZ2oYAc9Z2SvhNvwf6GZDkrkL\neC+PrWwGB+mvdlntDC0h9FCAz2kmp2ygnJhmOGdgRmQTMEOB8a/zlXdNtwOW\nY1Wq1VRsTF0Fl+OiSXAl69WWHDuL7GUmql87Iou/Htqg21J4ugi4oSaoARDH\n57MtX8PzHF1q0u0tMTX+hf0SiBE01Fa6B/BDVdsVZzDI6Rg5dvqzsoiY+d5A\nc6fPoJtPFZGcEspXlJM7e5CfnhECiaCO5XYp7lZoEY1b1k1tz+UErMjAWef2\nTwkV\r\n=VUTV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "2.1.0": {"name": "@npmcli/config", "version": "2.1.0", "dependencies": {"ini": "^2.0.0", "nopt": "^5.0.0", "semver": "^7.3.4", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0"}, "devDependencies": {"tap": "^14.10.8"}, "dist": {"shasum": "fabfbbbebc3a417db672be4014a7ba71e6bb37f3", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-2.1.0.tgz", "fileCount": 12, "integrity": "sha512-vYTUs6b1ORqWgWFrLkyscdhyhtB1YhbtEM2iaH5rM5Bv1/tWqZEpspGLh+Re6YuPRUmXulzkf3iWhu9ntz8cVw==", "signatures": [{"sig": "MEYCIQDq/PNoGtqIbE/2hKuoqx0MoG3pyQRoY17eW2ATt4yIkgIhAJaBbyfAZETqDpSjxAiIMD0M1plvUInfzPR2pbEYsgX4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43371, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZgUeCRA9TVsSAnZWagAATUkQAJS9lrFIzXGo7ueg28tt\n5Vl66z5+a7RvzqO94t3UbHrqHo4ekBkrEzXtscmCJ1835HP0aFSFq6FvVLVk\njrgYXPzGFOeVNhNqQVWefcguwJE/fR+Qq4/eXzYKo3TLUvi1ZJ6SMkceeLY4\nsv067GlzKrbcPme6pUYnxNw5tHsLBPoB3S7NU8RExfdIZGqKUTwtRwmwo3oQ\nmRScvrS8oyQytVAQl1EFI8iAKhwfxeZB3yznXRGoElIed87WLgTsUiykAsEK\nOJp2XBB9XZ6yuSviB+7uMEfEwoep6F5tSXclYEtA0Zn3og93J6TMQTsLRx3z\nZkLoUmN9w4VAp1cqh5yx+KiwlLAwqcydswtu1t3wAlmNfuBYSRh13qG6U9Ik\nV6gQxMuhVerJFK1dCcAXe3ZrwZfSXruYO5jxjm+R4xac3fg6zzH0USFLBxTT\nmjVQBLlNwtzrn0npNrOsrPG0q9YO7n0S9iWr1rrnhP4tWHuyOAbc52n+EoTT\nk2/s9bkIi07g820vUE2eG+5RSq8IL+98TnHc+6/lzhKwTDxLV+XD7vnQ+nT7\nQ/rl8Vk+oBsgODRwXQFUcjWASxUKDOEdWRFAOtJ3Z7hXJPb/sFm0lDP5ot5Q\nwKhw3nIcV+4I5pnfBv9omVyaTP1N9P6M/Z0kqIU0hRXuX2c91k/guaxvZBns\nP6lH\r\n=SQ3s\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "2.2.0": {"name": "@npmcli/config", "version": "2.2.0", "dependencies": {"ini": "^2.0.0", "nopt": "^5.0.0", "semver": "^7.3.4", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0"}, "devDependencies": {"tap": "^15.0.4"}, "dist": {"shasum": "c3f6cb76e74691d1ae746cda482b7df751ed2124", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-2.2.0.tgz", "fileCount": 12, "integrity": "sha512-y0V3F7RCWXy8kBOvKvKSRUNKRobLB6vL/UNchy/6+IUNIqu+UyrY3Z7jvj1ZA/AkYc/0WkCUtppCo+bPhMU8Aw==", "signatures": [{"sig": "MEUCIQCa72LJlHidqFE2W22DrTwuoZ8RkMbKWfXFIBnYhJfPvAIgNgO3F8iHqwYHmK9mfJuHuNW7oZF8/7Xolbgs7OdLi3c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43986, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJggz9ACRA9TVsSAnZWagAAyzUP/jF0Q4roEIuP/zvubsTz\n+Jo//TdAnh5bW+5/aqK6AcDSmf36KQkC0kbjDC2QUvWVFvVqxBxzQLTwQORF\nF6X4qkMtZxQEdn/kiN8stqobtXP4lObccFvs+qh+nntBCDfOonnZmgjVUTRT\nBOZpwFeSzm7jaoaRH2fYU+MsUJzuRe/c3DwoVw9vHWs7+ZJrKGfoCiAmWWUu\nbG13k4MjI8o97J2e6f/qoy5X/qYJkJP0lQjxR9TEmmffq+tdBnhZ5KEXjMGk\nF1kfX0LwQ4OZuQTJM7svA3269Tl2X6zWbj/4bRQnM0IY4ueKuba2fmMvryc8\nRpyDQm6Yz4gWFSb/duqfQGhVVYFfHDlg0DaCICjvH292eVmHzJcmyy09ZOhC\nEjfNFCZp5asHzewXeEGnKEMXuG0nTEQG5SXL+O3CmYjPcoNnouqIbjUDCkhl\ng9yXKHLJNZqysOpTWQs8itKzN9M//4Ers4gfrMLEci5SAHMYa5f5CdrT0KvW\nEPtyhHCwBoAKqipbehe+aesuedaNOPAtmER+5PB0psBaNluz63PvjH4OLFW7\nFcHTgKllkERO/qJkWsUr2PawIdL7iXmvyS43LSoEGbyxe6Cciw5w5AO/EwVs\nXCT22M9Y+t7qrDUp+l3uHEO2QAfsNQ2o2BanAJTQ4JK9ZLvliyaAH4P0+c90\nq+j2\r\n=yYS3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "2.3.0": {"name": "@npmcli/config", "version": "2.3.0", "dependencies": {"ini": "^2.0.0", "nopt": "^5.0.0", "semver": "^7.3.4", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0"}, "devDependencies": {"tap": "^15.0.4"}, "dist": {"shasum": "364fbe942037e562a832a113206e14ccb651f7bc", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-2.3.0.tgz", "fileCount": 12, "integrity": "sha512-yjiC1xv7KTmUTqfRwN2ZL7BHV160ctGF0fLXmKkkMXj40UOvBe45Apwvt5JsFRtXSoHkUYy1ouzscziuWNzklg==", "signatures": [{"sig": "MEQCIH0f9ZAEaqO7DHhG9SN9SSJNajJzCjKeBBqN72vL+8ViAiAx/Z1tomcnAw94QVrdu5qijkkTfwao1Jz682SdyulLZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44090, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhLlP0CRA9TVsSAnZWagAAY5QP/AmA+D/gM8jnwuYa9nOx\nid4jWenc9wAmwJGi2vw3b0oQ4O3cgiCTQDCJ1BpiMiqrl9COFl+9KJQwhxZp\n6gsiCvL44ca3kh9FxUw09mj7/VOZ4XGNLI1J5rB07Gw5I5HndaI0fabfktc3\n50pfpWFombdgxx7/vaalyVp2wgm8hzNzjrrfl28w7mV2RzQ3BwsG291TUpJL\njIYI3veSt9L6Ww4HjFbNcFB30Zz3wb3jtIZ/EORJLKDsRYjQfYwfhktRL1Z+\nS5r0CvSa+WSpRaDYh1oRu/b5f1ZrUFd9tBMSQ9NS21QM6bHaLnYU8qHBg8ws\nD+/VAijeob5ZIeNxJxyXrCydushyWfBmkUPajISZ9U1G4oRLAN0YWhGHeeE7\noFVYtaxdJO09NZgzqJKBdaJfUwBOsQEqOesWBJt3LrYcQiq5+IezYbmAr+Ta\nkjL1fsqtxlyccW3NgyI3xO5NM7XFvGqLzRyw68wE4LRZmzQJIEFrbNLbpmmo\n9aLY/6YF1WysWmQURBAh9SSs9XTtsej9Zze6OOjUVJ0s4CJun6YrQyyUNisO\nFrmb2tgyyXPeDrZGi/QCB9XIB37/aOw9U31l4urKmZ+eg03TztZgDEfUkpHQ\nRwdhqeyBJP+uNmnhq3UtzR+gCGwaEVGyOUhHiywiXBC7pYqmc1csao42vQF9\nrsnc\r\n=tdTB\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "2.3.1": {"name": "@npmcli/config", "version": "2.3.1", "dependencies": {"ini": "^2.0.0", "nopt": "^5.0.0", "semver": "^7.3.4", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0"}, "devDependencies": {"tap": "^15.0.4"}, "dist": {"shasum": "41d80ce272831461b5cb158afa110525d4be0fed", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-2.3.1.tgz", "fileCount": 12, "integrity": "sha512-F/8R/Zqun8682TgaCILUNoaVfd1LVaYZ/jcVt9KWzfKpzcPus1zEApAl54PqVqVJbNq6f01QTDQHD6L/n56BXw==", "signatures": [{"sig": "MEQCIB2AAJ1cM5loNvRVLMX9+8rpjtxJlAOFMJGVro5nupUzAiB1SkHvRl6rZpIOptJvM7+YVCC1YqgWo7W/UXRcu+hWqw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44330}, "engines": {"node": ">=10"}}, "2.3.2": {"name": "@npmcli/config", "version": "2.3.2", "dependencies": {"ini": "^2.0.0", "nopt": "^5.0.0", "semver": "^7.3.4", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0"}, "devDependencies": {"tap": "^15.0.4"}, "dist": {"shasum": "6027efc132fcc809abef749c2f2e13dc4dcd6e0b", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-2.3.2.tgz", "fileCount": 12, "integrity": "sha512-2/9dj143BFgQR8qxJbYptd8k+4+Po2uHYq3H6498ynZcRu4LrsDlngov5HGrvo2+f0pe0fBJwDEP2rRtaW8bkw==", "signatures": [{"sig": "MEQCICEbgM3ThUAM+G5jR6sxfxHpRbKWCi3ZlMNYjGohK9D9AiB/2qxIQrKJ7ohXH3p1Rr2sMK3Exc78pp9ookXt52vsiA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44411, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhnp5DCRA9TVsSAnZWagAAtD0P/iQyTgthQ+rttM9G7Twi\n2chLBSRMyyswOOBTGKdIWeqNe3P6Y/g/PM1MHpeqC7rZbvrEJbniCl/6moqZ\nVDwnb3VULs67LPv4jgTyY/ReVcYBN+uTj4psNhuyTqDuauD0uOMSS90U45sm\n1bBer2vrIMxrKE5TOjOv9DfJlsE3ExiyMjTovyjGZpsQ4jhWl65w7ZsCYP78\nLUjfxhc95j+NT8otEDzGeP7tNJ6iZuOna3ijELpJvQrNfFBWVHuMSNo9DDGi\neh7bEcHzSJt10w6czU5EAd42VNyUmCwXh6ZTy2hvJA8iyknq1oRrT/GlKowX\n+/MerCQXiW+mbJei2AlEIcXcck0w3hrd+aJPtGhzO50lYuTFxIhc+8hoJUJE\nsthYikjxnuouZ4VoBqJQom/0sDYX80hnb4PhcMeV6dQmWKeKroWNMreeMjSh\nh63DYGRuGR3Rv0XFMU9+zZSPQYTtiPLABu1p5iQvUV9qppMP0hssm17f6SH8\n9mXB0AqiJ0ClGVUolWVr8WHFw2FFbCrNfEcPhDNjpVnonYNPErlu5Vt3fYzG\nXsEOcc6cva0Sfy2xd+yoljTGc9ydE+pDcOcbuHjrRyUthonz1GqBb6cpk/Ov\n86NtuG6+Oj2v+Rb3NV7JTH+sVPiREpzCD4Q6k1IfZxVAEsvTBHQcfTYxSrLD\nfHkD\r\n=yAqn\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "2.4.0": {"name": "@npmcli/config", "version": "2.4.0", "dependencies": {"ini": "^2.0.0", "nopt": "^5.0.0", "semver": "^7.3.4", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0"}, "devDependencies": {"tap": "^15.0.4"}, "dist": {"shasum": "1447b0274f9502871dabd3ab1d8302472d515b1f", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-2.4.0.tgz", "fileCount": 12, "integrity": "sha512-fwxu/zaZnvBJohXM3igzqa3P1IVYWi5N343XcKvKkJbAx+rTqegS5tAul4NLiMPQh6WoS5a4er6oo/ieUx1f4g==", "signatures": [{"sig": "MEUCIQC+xDEWiVpSEBhe+UGLV6XUWsxEsPSpE6hrefCtWAVltwIgd+WyiuOhv64iGO0EgDv/FEdWqyRhgPrfT7fgUIN793g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45648, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3J4RCRA9TVsSAnZWagAApGsP/j2dAIHG7J4xL9f/y5X0\nN+52XPdlblV1iYBk9pBzAyEVeP+LVQh2BH515QGm+eDbRjsE72BBzP+cRnTJ\nhMXrmlu6cpAgBVZSTsxuvx08YAdv1TLy5ca6v/eDiGqRCKvIQ/91CPgMawwE\nuseH4qEB62SC0sTCE3SNQV4Kh82oihuB2gUtDvIBaEPZRyx7a7TF3x/ZlOM6\noMsZiKHr5C9O62tgJTYpwgBnGFLIjruPWx+T7aFPUZ8qG1YO9pQgqNUOxfcU\nAJPc2w49jl6+JiHsXcs+IWfG3hT0hfg6FkfBZb3btQ0CkHik73C3dovMzWqr\n1nMex1GVaH8xyheJxFGoGkiJHE93IT2XhktKMIsEtsg9I6p99vtrdEloqA4c\n8y8qGyhF240kwC6PDwzZtg3yfuRPNSWqOLyMIzmOXqMVQjRBBLi/5BUD786v\ndqHaU6V8629xk6MCNoRv5EkTPCOYWWIlPo3BVMD3PwXlMx8jTOL/8qGG75Xb\nbVHyEGEOfyKppOV8IMMbCOBcMx/9ACz2WENutty4XhAE8Sp5KIPC6JKDCJtF\ng0XSX6YHlalJgi2LzVW4rAr7MbLTiD3lThaLYFlj62u3Kh1X//7FsFYMMNWy\n9/12EpUrI0OHcFLawp7lBpwiC3Qqe/klWTYgGmbAfqk6sjY9IjTuHCQuCl/4\n079w\r\n=zwi+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "3.0.0": {"name": "@npmcli/config", "version": "3.0.0", "dependencies": {"ini": "^2.0.0", "nopt": "^5.0.0", "semver": "^7.3.4", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0", "@npmcli/map-workspaces": "^2.0.0", "read-package-json-fast": "^2.0.3"}, "devDependencies": {"tap": "^15.0.4", "@npmcli/template-oss": "^2.5.1"}, "dist": {"shasum": "604a2d81ef15126dc4ed7820d6bf15e6af21f134", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-3.0.0.tgz", "fileCount": 12, "integrity": "sha512-2cYe2MCGintq1s6izbLYO2gAHZwNFQ92lIb5QhtpRizwHwqrV9v4+xNpvx1EBaEaqTHFR4QuozgZLA1scao/5Q==", "signatures": [{"sig": "MEUCIQCfAiRjoW2qjaQgk8pekDF0VjN32FraA4SoWFpWOkwCRwIgMs3nhO3HAnKla1RnPP2ND5q5HgYyGK26IZZulr8Hn28=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48093, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+XwYCRA9TVsSAnZWagAATigQAIXai1mvCCXsutjklhnx\nEoon2vg6v0lI+C2yhFpwUqz/XEjpGu8R3xNLB6EIavoMehujooaeTtNAyXR3\nOtB6MZpIKuoe94+TcrG8nG8VZd1T0mbCwMv5ltSTbeFaJRzPfnLCt4sWRbZy\nNrD9YZh+krjYd7o0CifKBt3XqAGqLaEzsuv9RKQx5LL5nWZejmzTWHqstkRk\nsvbOtPWCR6gu56BSNyBZTc8R2DRQeQ7WDAOXGuyJ/eXtTAHn4zHGpPDdpA/p\nd6dJTJVVtwbfXZNrJNwpheTGeXZjjpGQt4h+tuGQ8rDcb690OwHaKwIfjfYG\nWvAFTprlO+3htgdBnkNQOMVjZe4FWBvuvzyhLGnOkaqLfoc+PTT3PprrZOAT\n19aTDDkGL7Yv77KueeUGohChZguSrBpTFt2drVHXga+9P8whwYB1jnqCgl1U\nVnfmS/W5BczDPo5wJmazOS2h69sI4DWQR8PSyqM6YQtG1NZhFijPcTUir84o\nXxFH8IDCqAy0c/OIROkYjrw5anOY6DEQU/pAzt7g8cCjdQV/GhfVf6TyL1TD\neY9jc/qD+dGM4HUQfVhext7MJAFQ+fFYDTlEAa3kxopDhqQCrs6m00uhXslC\n+uHEZJazLKJuOqIPc7l4A0OYgmw/ndBMYe3061+LPxdY4IaI6EdybeEPhNbB\n7BG4\r\n=2n4B\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "3.0.1": {"name": "@npmcli/config", "version": "3.0.1", "dependencies": {"ini": "^2.0.0", "nopt": "^5.0.0", "semver": "^7.3.5", "proc-log": "^2.0.0", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0", "@npmcli/map-workspaces": "^2.0.0", "read-package-json-fast": "^2.0.3"}, "devDependencies": {"tap": "^15.1.6", "@npmcli/template-oss": "^2.7.1"}, "dist": {"shasum": "0149aa9daa84bb8dad56813b52582e43bcea1e36", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-3.0.1.tgz", "fileCount": 11, "integrity": "sha512-kNnS/JOLdj9JgUhNn0UjDYhfRdcX8HTeYcsQ/wFdM29V2H8KXDAjcQnvJas6gsZRD5HPU6YBnWVUUJDkXQzdlA==", "signatures": [{"sig": "MEUCIQCncLwFdsDvUxcAovXT64hPcC82UXd1x8+b3BzC/+BwbAIgBZEGuRDeAMvAZUg+ngC9iqUXHqORiwfjcVNqvWSCTfs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47951, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBXM5CRA9TVsSAnZWagAA87IP/1dYQL8W44KLTgjiNEul\n6wvl6MfwKFvzM+lq8lYFZFNeCEipJs7FbO6oXuEuY5t6eqZyatxpLHof/A08\nZFQe3NtgrGWTTjNKS5DlfSXh90U35QXg61BTBmypoacTIJ0n9YPjqOIpgN/2\nuwF4D2X7vhk0qftNaKQkyofg8eK/pjxWRy+7oSS8ftXSYjIT9mUMoZHl74SF\n/QGkwAfbRIiv3ZslvKq9Y19LZFEVtG5bez9wh3mWP9tOa844eaOeelmP9zPV\nDZ01V/LwO5ZCx32qYNc8XneEvYWmf0pPscS9EmjuYSl43JG4cTxaZB3LxUKN\n5P/9gDAMblVHTEqbfxJbV0/FK/z6s7CEoDn4Se+AXf7FKyZU7k52LPPDPnRy\nDqYLJGA67dZDNwNPc3AkUnn52UDDlSK/UpXhkuOPoLUcZqUvUPMCm/GLvgxM\nr1fr0DZ3XMGvvgHATk6+GK4GjMiRvi7xANRjUS8iPhmwvUh6Qnn6JifpLOwb\n16jMO7kwcxOT3Xo0GKKigFoVXIweoW9Wn6+2NMENxYg7sP+s3Ttv6MQ6yO0r\nCK7IADbyr7DUeLBurspliP7NCWrlEWs/VzdEju81jawZRr1Odg/2IEEIV51d\nktKgc0hzRDQoNpL5uMsBvQ102B1QByBooyVeHvPaqhTbGcvcoy3Itny/PyIs\nnKO1\r\n=/bza\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "4.0.0": {"name": "@npmcli/config", "version": "4.0.0", "dependencies": {"ini": "^2.0.0", "nopt": "^5.0.0", "semver": "^7.3.5", "proc-log": "^2.0.0", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0", "@npmcli/map-workspaces": "^2.0.0", "read-package-json-fast": "^2.0.3"}, "devDependencies": {"tap": "^15.1.6", "@npmcli/template-oss": "^2.7.1"}, "dist": {"shasum": "1d12f2d9cc2f1da75b56ade60daacab560d98ab5", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-4.0.0.tgz", "fileCount": 11, "integrity": "sha512-iywEsUhkA6GSgTS3vHLxHttU6lovSYt7wCGD0MOsjfd1YAUlz8243TXUKhcJiPfWvQYB4FnZkn30m3KmZS8GuA==", "signatures": [{"sig": "MEYCIQDEOuNzs7g/hO52S1xvduszgyXzY53kL6hMZHCD5WSikQIhAMqa9GsK7FVZ4N3oRWOxgMosBq4EcTlMlCYasvz3vMWa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47746, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiCvDMCRA9TVsSAnZWagAAOV0P/1Q+FPF03Q7Yz/FbZ/yP\nbdNcH/CR+l8QN1/qKmSGlJV1x4RpF1H3Strok79jtSD3cq3X4ldTG8qAp/eV\nMqLqCXZpVWAtXDv+nyjOCM1qShzLUWEbOAYEhJjs1TdyOK8IwxcSylyqawFj\nvhhQCvciH4paEl1cZ9cEh5XjLC+8cGm4QejIfFoiijNW1VaoZ23XsVcS1pFi\nLDvHkF+rVEFPqxlKrcujqSauRDEQYoaBGpqfChGtlipZpHmGs7USVqRZjbDV\n3CpEQByYTq63z4jmlcAYXFChfgYKoImUYdbnxbWFalf5u0DQF22l6IwFwsSj\nOJAOqrPMgH2w2Jjww+y2UuMCm7M5GQS/DB0eerAhO7egQpke91C0jQ13wCSE\n7lEE/vztQJs7KzSAqlwSKUpxwGBILraqSHnO3uflH1qXneIws2bev6yk9VUj\nE7B2DR3vPgQPCFy+1WfwwJPjekvAMN0aApUtWhXtN9YC3qh+lqHAfVlgUUjU\ntr0bi+Db/QkOl4hCTSzbLfElQQeM3jKb+ZDn7r404Yqq9v5X8kUiHB0Ozc8J\nkKHfI8UmkhkiSeafYLcw8A1CHaCcnx/MDE/UlUhttUNwqzQYuAlS98jIAY3U\nJYKPhrjnxiwTlPi2SxGUhqSeVdvFWeG0MgHTRv/jNXsfI8GUrbS1SJJjXeN8\nMAFV\r\n=uqmo\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "4.0.1": {"name": "@npmcli/config", "version": "4.0.1", "dependencies": {"ini": "^2.0.0", "nopt": "^5.0.0", "semver": "^7.3.5", "proc-log": "^2.0.0", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0", "@npmcli/map-workspaces": "^2.0.1", "read-package-json-fast": "^2.0.3"}, "devDependencies": {"tap": "^15.1.6", "@npmcli/template-oss": "^2.8.1"}, "dist": {"shasum": "4aa4c184f766cef6f227d0439485e4b940968c04", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-4.0.1.tgz", "fileCount": 11, "integrity": "sha512-pd9lYWFuDARcfXyXJKm/aHy7p9bYDKfUh/7Kg1L+3YYgCmqtqe4exFSnEIDuFO361A8xcKnj2Edev/Cj1cWbgg==", "signatures": [{"sig": "MEYCIQCp322BQ9oUnsg+8h2oMa1+zcRQKDevfZg1EPQKTJIfVAIhAKKj9/hLJvEwEybhBFBM0Yy4AiT2X0QEhA/blBeEHtKm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47864, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiH58GACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp44g/8DjPiMPw3BTgbpT4MXVc+1RcBzfBVVo93WB9NjoVD2lpchtVe\r\n3vCi00FHURQn9Y9F/vu44dM4JrlhK/EN0tSqkNjohbnQPtnMSawyjNLHDIsu\r\n/ZV7mPnDNOM9igkN2JoskWHsXueqyzG48QKrr+X9g9tk9FbJioUOegGKCC0e\r\nnsQEcbdUmjZTwOKI3Mvh2jAVYjOYuFaklDZfcagI86WGq2rEuayT+d9OFthY\r\nYId8+CSQc0lQuTRB0o/DnjWezmflHiMWfpAR8gPeD6PFYVDRD+KLP1NLNtkG\r\nV408WvrmC7tDvqYV+GPM0jXw7iskGdfTZhbqZIhDN0U+tWqmA91SRHawxCRm\r\npoVZppHXKozD5uAjtJJcavDTpKrKRllq2of9Axj1DrCsCd+tAZ0WBjen7AbD\r\ne/E/7Eiz2MEaHFVJDDHk6Iwy0rqxaIIK65hGVt+NFZLp+Xi+/tycveSsAfZ9\r\nHnD5lZ3mcpuzJ2J6slssYLmqhxXIzydWa0FvFi6svcX+ONrBd2nzGg+bgF9n\r\nkzIcnX8fBzzXZUPNPxGbIzsjiKqhV0Wj77I5EjkvmHa3l3B9yiC+b+T9Rrwo\r\n6L7IQ+LSYI9HV7NoD03pDmLgSTPbCFT3GszVueLcJNX+xXxt3e5I+Uhx5Zsb\r\nbuvl9Gp10aDzVyojJ1nAFDuE7i2Uk2tKjmo=\r\n=X83N\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "4.0.2": {"name": "@npmcli/config", "version": "4.0.2", "dependencies": {"ini": "^3.0.0", "nopt": "^5.0.0", "semver": "^7.3.5", "proc-log": "^2.0.0", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0", "@npmcli/map-workspaces": "^2.0.2", "read-package-json-fast": "^2.0.3"}, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "3.2.2", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "cecb171225d8325cee30ee45893eebd9503bb203", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-4.0.2.tgz", "fileCount": 11, "integrity": "sha512-UqD4h4LgyNOb7xRmAK7QXiMu0/k2x7wEzrEqXZ1wGIQVmCl3vamsAprDjAhi7IKlgRulg09hpwYs2/57OP22xw==", "signatures": [{"sig": "MEUCIQDQ8bkA3CfJyiO1RQXnvLCI5Ry9ttrDMntB4CFl2dm8qQIge4Nncbh6l7hU2zoI8qIRlr/V0Wjl70UQEjxvAf0uEoQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTHsdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqegw/+PpFb+hZTrUgON4rTWpykB8Oquzr9mBouhQ1L789TnPNd1bDI\r\nD5UwjH8kddQPphgG/mIvfPJdMSwH/voNuZ5vn6tpgwyniZMOz88QoKUbSVRn\r\ne+xm956nGIKwQt1EjLR8B+I8mJqxDSyHnIxTdWmzojaCNly0lQLyIDtDRb6l\r\nzQhS37B/ClkprhvFy/tgsjON6E/B8URmTqPUGNie9EiQL8JDngIRk7vtcKtR\r\nqHmdlPAMmUhz4FPqai0n67UApv9tQC21RUYoraF3orLTWyOXq9TrhN80q8m1\r\nOH4KaZhCCty28uN9cd9rT+yXerYQIXHCTD4hkeC83RuRoaTEZqW0NhGuCj7q\r\nlpmiJPqkheKwCdlMAgezHXP1hR+c/dCpa6hAF/k4PLnEE6glo8G1lBed+xLn\r\nu+NWEQGyKRGUNMVPL3S7XQh+aU4tblgSr0WD/Lk3gG7Jdx6g/Alvuq1oH4+C\r\nIjklYu+PZnyQH3DC34OAgKDFOAZS1NdscwMpQeHEePAO1yvgBjX9gQE7TOGV\r\nsr7gQYpkq4+b8LT6AfqMqNLB5pWsUfLefKtUWbvc0/YeXBvP62oatunuPQ6F\r\ntVf1qXZvOuMuV4LhOMYDlZPS5nQdoNO1nZxqdgQcjjtMQ5OgHXmARL+UeKyG\r\nvwb2C3JW1BaD5E8eOO/Naj7cWkun3dU11a8=\r\n=8uWA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "4.1.0": {"name": "@npmcli/config", "version": "4.1.0", "dependencies": {"ini": "^3.0.0", "nopt": "^5.0.0", "semver": "^7.3.5", "proc-log": "^2.0.0", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0", "@npmcli/map-workspaces": "^2.0.2", "read-package-json-fast": "^2.0.3"}, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "3.3.2", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "5c92e5ded2a44baf76b94926646329c3b39e79b8", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-4.1.0.tgz", "fileCount": 11, "integrity": "sha512-cPQmIQ2Q0vuOfrenrA3isikdMFMAHgzlXV+EmvZ8f2JeJsU5xTU2bG7ipXECiMvPF9nM+QDnMLuIg8QLw9H4xg==", "signatures": [{"sig": "MEUCIBH+Ubec1yMuVPiiw9oSb8T4VK7CbAlRIPNWMqy9tG0uAiEAsRsCCIMcnm/CiwBKqmsRovgM3j5NR1gZcGuRIWLOkZw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47970, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVvpeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmru7hAAoekwJ0HwSnffNCAxGleGf9Ibwg5+yOAcKqxGzmhOZtUY5Xhn\r\nqr0X2/STC5x9nWgcPgN6KevlBg4FcIISNG/5IPhbEnUZOqdT3S1HTM1CPuxi\r\nT9MMZ8gCirmGOotFF4QSZX4wVG6hPvCsJDGpwEt+CUb09SprqpDSf0kd2ZGJ\r\nGLWdEBGZrJlyyOcgWmbvsHpuCiaC7EGI4uKbqtq/0MZvJtywpQRJJ/GtjUne\r\n45ZVTsoZltbvwWUHMOehWG0uuGmSBEXvJ/VlHBXN1S0TdKrCw26mh3b8OJhQ\r\npluwnfxE+S2XJEvXv+PQtcymU5mOnhJ6bjObRwi/lk90k7rP2HIhsZ3Oed9p\r\nAeXUX9YqOVw9lPoBVmzSy4sB6YidZAYFyC1zMCEbaQRbsrmWD2sKng8LkKOR\r\nZn6GnUgQwXW6Lq4I1QnLcl3tVqqk12Cd+e9RJ6xk8e3oL5xVGscXlb/8IUfU\r\nHdZi94bOl5J1nuI4uVlyro+wGAdC1B4iiFV5Da/h7YimzWSMH+hvtA+FWGLf\r\nJ5mrnvKxubK75FqAFINhuGUxzeH+j4w29yfw1d/w20IIK3MmtV6mrWxW1W6O\r\nHHalBPUszzZUvTattw5XrdYx+0wsJ+Z1rdFyXEfaWWZnMnKyvkvGAdumLdjd\r\nnQz+a77/rJ2GnVQEdcRAj+I5LsjD3yKz158=\r\n=1LvL\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "4.2.0": {"name": "@npmcli/config", "version": "4.2.0", "dependencies": {"ini": "^3.0.0", "nopt": "^5.0.0", "semver": "^7.3.5", "proc-log": "^2.0.0", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0", "@npmcli/map-workspaces": "^2.0.2", "read-package-json-fast": "^2.0.3"}, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "62b5d2b9cbf93fb2bc9f7cc947f25d7659ef849f", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-4.2.0.tgz", "fileCount": 11, "integrity": "sha512-imWNz5dNWb2u+y41jyxL2WB389tkhu3a01Rchn16O/ur6GrnKySgOqdNG3N/9Z+mqxdISMEGKXI/POCauzz0dA==", "signatures": [{"sig": "MEYCIQCv9cn3P1tN0Cw1YwU4LMfb7CsxEbsTdxHnoatvf6CYaAIhALb2IQrbhXhwQ6tAN1mKmBjJ7i8jrQn/1a7CO/O4JBRB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48642, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1ZYUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqg3A/+Og0iM62kcKJ4k7/vtcSFG9q+XJlcj0BbimQsvs9rBp3uUSnY\r\nHv0tz8Ky3h1XCT7VmEgoaasoyZfZ7zfGLNfzfnTjwzCqDjsPiEqd6nS9G7L6\r\nvkSqR8FEgMuj3VomnutK4bq0YWg0B+u/n6BTtjyzJsEl29cXK0JyPVXJnBAb\r\n6w5S44Za2iwY7Z5SBxe1dUPZjyNzvzR7/pkxIgBNCJOWhMIZRid+u2v7mVya\r\n713zfaV4/2HpQLSh9NAXIKgaxva0UZ1KphBJbwJKMCTbMeXRSdwLMeNHUuk9\r\nQAbCGhuA/WRygQ+TS02OdKqbw60bnO3Du7YQw/KeWReDKEzrSdL1rKTBOer/\r\nOpKdUAe4/lzINQULQ3RHniTHlsa/q0mMoLSwobXLab5PS07mcD14JffieaTA\r\nFCzoHwYS8HS1huBUqWbwZLEILwzYY98224GCBwyETxK34R5EA/AcXhtW5v83\r\nCcAI9eHcaswDfo7Hf3SvbSlPV8GqSFJxxwoXWeY6lAvujvOKZy2CNCNef6FQ\r\nQ5BUGKfhV4gV1EZxi34jAAAjIIuPN0QHQkWwZdllKf8wi4bHRZH+liJ6cOQ5\r\n1DeKvTfqZPiIFC1fxSTuOMK9MxkrfhkNSUHRAHWfkkxwKrGCtElansHICZBg\r\nXEHOWFPdn4nF9w5fjyC5ZvrVqtpMjb+XsUs=\r\n=VdaA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "4.2.1": {"name": "@npmcli/config", "version": "4.2.1", "dependencies": {"ini": "^3.0.0", "nopt": "^6.0.0", "semver": "^7.3.5", "proc-log": "^2.0.0", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0", "@npmcli/map-workspaces": "^2.0.2", "read-package-json-fast": "^2.0.3"}, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "7a4b46f4a315fe369a3f8543c67fae0b89549a40", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-4.2.1.tgz", "fileCount": 11, "integrity": "sha512-iJEnXNAGGr7sGUcoKmeJNrc943vFiWrDWq6DNK/t+SuqoObmozMb3tN3G5T9yo3uBf5Cw4h+SWgoqSaiwczl0Q==", "signatures": [{"sig": "MEUCIHUxy4H9jkRZnlG8q7Vtne094/c3DCqrr1w3D6Mp1zqEAiEA77xR0k6P0TerUb8QikCkpcqEEcOEG0Zx0DTbSIAdrM8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48538, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi8o5tACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoyjRAAhLAtiyHDr+Mb0a3rHkQ5jsK8H6XUpx1l9qGbcXubzOanLynj\r\neEeXkm5+mUCJCeqe0FhwFegL2ACdAEWaPcoJqsHdQmu5/mQGBhd7X3i7kCvx\r\nRBWkaxJCGPOEkpXWD6kHHqLWpTbTpnZam6FLkhHGUmRLGncEnoUFGsBTB7ZT\r\nuiWalWFOwZFaBQodMuclJE6O6vpFmerpbPf3q7VH4VrJd9a8aVzPq+7rKvSO\r\nN+v898oqtOhsrQwE5bj7LBBSIF6PTm/WfZRUXP8dM2VOsBVqCyEFjwjTjaZl\r\nsRNiCnhUAMdPK/qnWPSxSwX2HNpFsGnj8NDfcTlHT0slUA45hl3vgrYIulyh\r\nSsuWqRkVXANFrZ5fgZ5Hg76udIZV2vXXe8PXWBvKYkQTXiAG1HB3GFWHWG1P\r\nqypugl40M0pl5XGNPk/v2znniLL6A/VWjVHgOatHFwGKVFFOwAnpk7/qVasK\r\n9gz9F4NJHEqh6eXHNGW/E3id2MsvldzRhSXKYWS516WZIlMEbHpD42cEnUVK\r\nL5r4sy1WOmtyBbvsz5jzgYaI9Fc0F8QnZjGfsCd5kaDZ2MdgMICwZ9zYscoy\r\n255L6oEeLBgYs8i5uVPJphGgozRKRl89a0ZNavZRALN243vSjVVQ6pU4O+AA\r\nzO+HGv6b7Q1C97awZoZ0DmVZRT0SgXYj1IM=\r\n=iyBp\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "4.2.2": {"name": "@npmcli/config", "version": "4.2.2", "dependencies": {"ini": "^3.0.0", "nopt": "^6.0.0", "semver": "^7.3.5", "proc-log": "^2.0.0", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0", "@npmcli/map-workspaces": "^2.0.2", "read-package-json-fast": "^2.0.3"}, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "3.6.0", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "2e3334dda84f48d059309c53d152e66b05ca24b7", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-4.2.2.tgz", "fileCount": 11, "integrity": "sha512-5GNcLd+0c4bYBnFop53+26CO5GQP0R9YcxlernohpHDWdIgzUg9I0+GEMk3sNHnLntATVU39d283A4OO+W402w==", "signatures": [{"sig": "MEUCIQDa/xXka26GflR8yHHTKa0l5JaIfk+gsbDIBvR/8zMNkQIgHo2VlARZEzCwm8aC3+d7QyrUNw0S4kBBFPkianak+pY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48944, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjB7/cACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqTmhAAkAUoj7d0n41NzgTGbqrqr+1Rj+FzW1xaz7yC1K/3Mq01Fifi\r\nHI8v036Af43d0YO6YJrl7iNsnfQcLmHyW8COtZCVPaumGA8h6lVdvwvJKOju\r\nr/Mc8Yj0Tnlr6QGloOl1cgyHkAvsvNdT35KBtFiKfe/PlQUMk18rGf0ghIEo\r\nxZ/Bu1342B2XYr9napCq9VA06kNd+ueveYNB05ev6ArYhwp+MX5hd+nMxvzx\r\n/qx2PgD4CMv+PwlL8nLXn6Z5C4SfHmFud/U7ZDsHtgCbUglbpKZrjpEIUoLr\r\nNll0TcgVS8Y/lz5UUCLwnBOjQ+aALaGW5eczJ0Yah77oK/1A33+27Ks2hcCp\r\nXiSCIvG/2IeRIN4ksc5hT/tfiBq/tOJvFX1Sn5rwIjaTxXp7nxZtIqdhaWBd\r\nmOnBc7Fd2o3u2N+nUJSLYZWh+xcwuwe9AxOs3n+zX1P25uNcHeaiVaBqJ6Sj\r\nklarQk2Gi5RToMTgAxABgco3mEG1t0qRMCpQkDyEfAjiAAnBDIwR08v8dolH\r\nmrNDST/0Xp4k7TLvUtc+kBbd0h58PEOZb6sHx+LJvA+38Y9Z3UTymSjiBcB1\r\nUsmjw1s8eW6xRdXH1vFtdke33Wyojd+kSZiYcUZvR5g5voyx6O4vc4X9NXXY\r\nxTVJGJyzkkujz/OOHp5OuYOMvfDA4Hs9p34=\r\n=8weE\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "5.0.0": {"name": "@npmcli/config", "version": "5.0.0", "dependencies": {"ini": "^3.0.0", "nopt": "^6.0.0", "semver": "^7.3.5", "proc-log": "^2.0.0", "walk-up-path": "^1.0.0", "mkdirp-infer-owner": "^2.0.0", "@npmcli/map-workspaces": "^2.0.2", "read-package-json-fast": "^2.0.3"}, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "4.5.0", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "c15214cbc225bd3cb0a93ea396d84c11fa0d07be", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-5.0.0.tgz", "fileCount": 12, "integrity": "sha512-TfJ3IRw5eKtzvzCxWbmy74KfO1ikKoWr2oPzpugo3RqSneAF/PNFZuSAuubvyv5qKjAj0hU4BC7VI2o3eOAT2A==", "signatures": [{"sig": "MEYCIQCMUMYNR46MsJ5gT1AlYiZDsP4y0qWgN46mCNDWHW/6twIhAMZhpFZRvBIpl1iRZ0FERnPFg6jn7qHXeUk80H0TfGLn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50826, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjPy3OACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpXdA//TRLweT3xdxOFCoLHYgqWveebcD/guvy5nvQuR5l2UoOSEwXF\r\nMDF8jARNb/HI4vcf6mqqFAb6M7I1CnhTx/rBUMkrE2WumiApH5ZVWt9ySq+f\r\nvjwyBDIMG6+mKgDcMWTgzc/HnonbRq/EzOVNmx8IXq5T71OyMoOtdJO3oSHj\r\neNH7K/KqjZgPQp6+P+Q8Lp7FXvjAAjkvSixEQDpt6WkvJsAm+lVHGWncJN1j\r\n4IID5/qD/iXoBMRGoXhb4z9K7d7vPbWzWkKwTg5uLCgLmOVl7JqK5RShpZto\r\nBvOMYJl9rbBE1gts2pANVC7P+afM+wSNSKpW5xd/SWFgUGsbJ+Z7VLioEaVl\r\nlfeVyHHMXwutS8ykS5Kl4zF1sPVbkCP+BJsX7uMikG5LDFeEIkFhrh3P1Au8\r\nrjV6msl0mdDyN47tlk9iwVe05vb/6EVmt7ItjjKGfKezWwqojcka3HIaJLSa\r\nVm7xGzFmdtWJryI5bpFfNEX6P3Lx0DpXzz6oFmA6/rnxFHgmOMM3nNNOPuUC\r\nlqYBSMq0XSDvsg4WU5eWrfEFP5QkGe2ky9Go73E0gqKJbGE/qecIwPJ2mvq+\r\n+pTxllOmJH0rfnWmrlD9Q3sCZS4QmrNEyCwvC79Al58bZ7XGgPccLL4SoDrw\r\nCDi+I1pT4ktd89q2Nu17PwlbtV0P5FffIHE=\r\n=Ksqw\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.0.0": {"name": "@npmcli/config", "version": "6.0.0", "dependencies": {"ini": "^3.0.0", "nopt": "^6.0.0", "semver": "^7.3.5", "proc-log": "^2.0.0", "walk-up-path": "^1.0.0", "@npmcli/map-workspaces": "^2.0.2", "read-package-json-fast": "^3.0.0"}, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "4.5.1", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "983ca72f149490913b86bfa95050c94ba7898da1", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-6.0.0.tgz", "fileCount": 12, "integrity": "sha512-PjwgQ9RjqhKkRTD1o81ak0y6XHsrVd0SjX4dJuZVDj5LUPyl4jwJxWSoqwF5+HWtM+U7MxYGsyUNDWU1fsALLw==", "signatures": [{"sig": "MEQCIAxgEu7xSfjUdW/qIue+z1ODjl6uyidbyjYZCgsHu5NZAiAPpF4QvFFgU3fdKRVGxjR6/JnfDOaPOTiQNZd+oMKcpg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50089, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSGYCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrWnQ//ah8HvM3Yz4vx+KdYzQchjVsU5Wo/MGlCoszsh/aVew8CM6CV\r\npvhGXM0OtqXLbDMizpPddoonPagxGK0cJDPMxxAgDwdhH9gSXao6CgEtHEP6\r\nopc2oIQkp7fSqKPcbEWTvg3VgNUYn4i8It1phY5lMwfI+d+wjz3E3PIbCNSM\r\n4eKR4il3k7j16YOT/724D4HFUtMgXz6Q7AgtzSfVgfU7kRz5N8VylW9swty+\r\n5qLAq4j8VBXdGFt+K8TnHfqoZPmOywCQdPu1PNi8lVpl4g7wHWQ81tgEotEY\r\ngU/QBRqKyAAjaxjx2ELQIJg2shbqoKyrM/blgPzlfG58CZqo3JMmTT6/otMk\r\nEp6K7OK0vWsBG6IQeKlY12883v3mwIfBPEDJEsogheg0MUNb0UbiRsyu0mJO\r\n+rWU/ZO8psebxOCMsjsa/ePmf1RZjbnjOV9yqs/JkwcVISp7BlqXfCgs7TXB\r\naemEByj8f8P/97p5Un3ZWDSo31Y32kb2DLEc7NPzH9r67J8Q29Dlc4c0CGSR\r\nJWHvpl8RLetLAUpD4tIjuMtjeyErxptJBP0xqfPFA8ZBTjVJLmCis+Qg8Yyr\r\nSknSVKnaBCA9YkuKdTS4XayrB00TKOpjw/cgt0oyyNc9ph08psYP08fS84Td\r\nEoNmMasqG9i5/nF3oltBWe/m/f4vmvMqwjo=\r\n=wpUO\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.0.1": {"name": "@npmcli/config", "version": "6.0.1", "dependencies": {"ini": "^3.0.0", "nopt": "^6.0.0", "semver": "^7.3.5", "proc-log": "^3.0.0", "walk-up-path": "^1.0.0", "@npmcli/map-workspaces": "^3.0.0", "read-package-json-fast": "^3.0.0"}, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "4.5.1", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "f5b5e95c37c83f767b24ac0168b75c63811047dc", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-6.0.1.tgz", "fileCount": 12, "integrity": "sha512-f8PGjhM7kKbMfEMmE8n1dW+m/7XFuvatLXqItO89ZKJwYl9Zs5d7CmsIe8n8i+4YmGYL3HqR26/mVb4oK2b6Zw==", "signatures": [{"sig": "MEUCICuyS55j9lhakI+1Sgi2ddX5P9XRvDlPT9eyoCJv+Fc1AiEA1am9TJBTi3A16p/dG2GMta+gDbUiMs8RE1aedvb2pAg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50089, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTa5/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo8rRAAlP7gSi4+pcwzWdYvurC8+3wmnBSk4P5I7xv1TFVRtMsQttNV\r\nungCbbMq3MRUjoyS4Wpd2tWV527qpZThrundVk9fG1wTVH5qFCxgLZjHemdT\r\njAlMjOWArXnMyll+SEbA3KmJY3FKJwNUNvTQVvLtt3xcSUn9Ue3f8fVToYMw\r\ntZa6O8q/9uiEJCEv75NIhDwKOW9SFjEbbz+/UAplJ3zCdcySdWGhC3ryafdM\r\nGbdX1w42gTAQT/J2w5KQKn3So/PpX9gehIxI0sTokJtisf3wxqNIuvUvQ0pY\r\nH7FUzZB+E6Y/ZyT166e+MssP/tSuj4tHmo51XLbbINyBV1nbbPndW0HwxE4t\r\nopNLX0CSs5OjY74ajkWkt9wgZdch1RLiihd+CnPqNzt2X/hMcpYwymt07dqS\r\naYwo52Xov2dL1RZxCwQuFFxOwLK7LbsGC2UkTmklx4eF7S/R2xIWuvzTHKRe\r\nYB9rXwo2vl2tGa9+ZEMPdYzOJrew/Nb4FeLx1stthePVAaloDVuR8Hj+RxLz\r\nNcScErVY3rBacosIyZf+4v0qr9OeUjFAsFZfn3VgUDi7WPrDDM0aJiq5p8+d\r\nvgkrSSJYf9muj6L7Cn5b0LtW/7zOuA4u5WJtJfQQEDf6DHgtdY0pna/9cvcO\r\nARgdXCgs6SOsafqyrMHSyyC8EGTqPNroNUw=\r\n=dOIP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.1.0": {"name": "@npmcli/config", "version": "6.1.0", "dependencies": {"ini": "^3.0.0", "nopt": "^7.0.0", "semver": "^7.3.5", "proc-log": "^3.0.0", "walk-up-path": "^1.0.0", "@npmcli/map-workspaces": "^3.0.0", "read-package-json-fast": "^3.0.0"}, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "4.8.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "a6b81ad87fe76f60b77d38114ecd33055903aa4b", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-6.1.0.tgz", "fileCount": 12, "integrity": "sha512-fPVlvy6MmSN0zgJU1TOD0fimnKVmcFpK3WuPyIQfNtCE+HMkFDN1mIKBKhUNow5QYHmCzMvGbu7pAgwdlSoaQA==", "signatures": [{"sig": "MEQCIA4+aspHkjqzehW9vAVs1TeKhuHaOStjLo44z69JsA6jAiBt7/OI/l8UV1uUg3Do5sUhPiwhD2d1hjuKu7r6WijOIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50138, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjYrWmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmogMA/+P0/9zxh/VvdiCnKFtP1MYthKivo7llZPivMBNOzmHFUIYi8K\r\nYSS1ckh+zj5auggsBUfBnxaq8MfPSxU77y07ysY+ocYdHBqjh7uKI1eqA8g+\r\nFbunJrVKMFIyOu5zz5yl3BSA0B/v/0eFmzeUcJu3JAnwQZSBwdjX7rwQAT1U\r\nucilRT7ZxF0unQfr2FmMu8hvz8m4dn6PVEU15XQKS4jPJpmmSXH2GAgzEY6C\r\nvqTOy2yNaAdIzR7oUb/T8lHpDmgyO/+G0Ds6sDI8XO2KFz/3SAJQh0nFBtJd\r\njNzbaMxu0lPlqK511eaVPsG5A+ZjPh2nAQ2vIC4+vO3reZWlkq5kD6bjdkUV\r\n6Jqmgv+lRXExuppbmRXbiKhSr1U8NuTx+mh/t6lM7Ek0xXTOZIEdwQEvPDu2\r\nd53egBpHhsUbA4VTH1E+xPEYDslQA9uyXTK8S8iSiZ1w3L1/IpNP1AuBk/cY\r\ny+86WVnnWKr2K0wRN5QJarQYxgJj+yxcFZl/VSorZqg4pKvlB7qskFXtw7Cu\r\n/AkQTREOHUmVNnk3PKVEjChmDOlieggIFJJM37MtcF/7kYe2VQlVOtLx7+va\r\n1W7bH0oYCGxVRQ2Lba8XzkkIRQWyLklo7uEjvKA6Sv5Oudrx1MJtct5RW75/\r\nTp50CHBNOVXe4y0gyDrh0N3qgWnZ9cmD3HA=\r\n=dXyW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.1.1": {"name": "@npmcli/config", "version": "6.1.1", "dependencies": {"ini": "^3.0.0", "nopt": "^7.0.0", "semver": "^7.3.5", "proc-log": "^3.0.0", "walk-up-path": "^1.0.0", "@npmcli/map-workspaces": "^3.0.0", "read-package-json-fast": "^3.0.0"}, "devDependencies": {"tap": "^16.3.2", "@npmcli/template-oss": "4.11.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "d65bd3974dd4bef66a1ce11024cb7bb461a022f8", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-6.1.1.tgz", "fileCount": 12, "integrity": "sha512-R09ig+5UGrA1lxTWMyfyUwwzR9OAtJwMHGF4opu0/ysM3nMujzVvp0DVD/BnbmcQ0w6jM4IrsCSsq6ZdcvLW/Q==", "signatures": [{"sig": "MEYCIQCLEXqk90b/u9GCZ+UHDjfCzJ01tt3UIk8eQrpM1yFtfAIhAOuidkUYyN/wfe/3HJmiNLWVfGCRFNyvYLmNd7KIz7j5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50613, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjwG38ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrVdxAAjWh6gnmYkcbUavPaAeZ8f5l7MdawYlqXlPOuL7KxDns4kNzq\r\nelSiU+iQk7yqtSkBovDR/xqfk3Dxx+5TrJz36yskl9VmU+94cWvuWxRyHxZr\r\niP9vyt2aksc+6GUvgoSZVpCbqRtFqc1uHx9gPOKeh33CIsohhewkHH1bK5Hb\r\nxgofVixPV9em21eTs8jU1LdEGoM11GM0F54wBwv/jLJboJZP9exzyZvWfSbM\r\n7eBtovjMpocdWams2Mkne0pEjt2aBux+NWTVWpy+BPsVXP+ffIorkJ3lpt63\r\n9mwq8WwhBMusMbuuE0hhfrlmto74yf+4kBbkUnwnXjDu1uqvRjgJmHDCqOTt\r\nxbXKzoGdzp34fG+CfK/TuwR0okEFTPjKSZVXJu2/2e0zXUA+4eC7zUvbrX3S\r\nA29KDwZJckHQ5U2n4Y7hKlCK4avnQHJtQcyWgH40GSJ3+9FnREKdYH6ve9hz\r\ngsYOIYtWejPv2vR5sDRFOee/qt4nfHpEwBb17AcWeeV4jHeAvd90U/LxPfsN\r\nkueB7loiJ+oL0EhXQ+YLfQ2BBLemD08MhUxOsz260xcIeVupNZLw9r7ebz06\r\nIBaQuVcasZo0KCtebuNhogpOFZwBRnEYLkeiFhvw7hfKuZwLi4j8wQ6g3wd5\r\nEBv6hm5gbOQ223w3MOL7Ld39wLpli/LRqiU=\r\n=f0xu\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.1.2": {"name": "@npmcli/config", "version": "6.1.2", "dependencies": {"ini": "^3.0.0", "nopt": "^7.0.0", "semver": "^7.3.5", "proc-log": "^3.0.0", "walk-up-path": "^1.0.0", "@npmcli/map-workspaces": "^3.0.0", "read-package-json-fast": "^3.0.0"}, "devDependencies": {"tap": "^16.3.2", "@npmcli/template-oss": "4.11.1", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "8e5f280088d391e60ad1716c3f69fc8f56cbbf9c", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-6.1.2.tgz", "fileCount": 12, "integrity": "sha512-4Hme9jhshXF0xOV64II5GmwtuCZWOlHT9zcqBPzfrIJ0AYb8/EfUtIu4LQxOWFojGdstTrGZLNHktvl9V6M9+A==", "signatures": [{"sig": "MEUCIQCjjHVm4gPbUtUV9wHKwF7K6Jr9IEJvsxfNE8tRWaSaogIgf0r+74KyeWZJW7VsT6fGutBE9xPBq4wFlhkenfWTQd0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50703, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj2zlMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoUfxAAn/lFX5CnEZ9qoihix8aBolcwX/WsuXcv5hO1KNCsaYRbOULO\r\nCD90gOrEyQtk3kN41TBSH9eL4ZJDJuJcS/Q+WE54vnFagJ5q5rMkAtnlf1SQ\r\nAsNTxIkeQAqAiiiJqPDNd+C6eXv04RcfqAOh6wtDWPIHCfvZWZBKy3Aa9rIG\r\nRKDHqPd64e/qItNbrg0QIk0eAMk9whrunz04enWEj0frFRlAtqnsU/hA3O5W\r\nOHURRQ2XkGW2O77RrCpo1zizyzFmPW5J/uQTBiKNuBOlAfSRbxzR8zXfKnnz\r\nek7I8KfXWZ/v7HyRh81lo+PbHEgWNFW1eMVA3BDj19mu6jejWStfByAtBjXY\r\ncYiidRvEfGXjbwGkF2kA+yjFtHW1+nf/t5FSmihcBW6vR/t1R1qEeUob+6Xf\r\nEEETUMANDKpT6Yz5GyGWxQYSLDWwipBcxkGYM2lZpVGoBrCy9+CAYsolWBEU\r\nMpMm0YeckV7k3UefY0iXpIQSrCSP/8kmpEb/AHlfdxb0aciPeuuVZPk7N6L1\r\nh0tMrffACHTzw1JMd/g9gZthmad2ga2tEbYVlD1RR7R6IxnYGhNyspyqt9G+\r\nk2Yi2anrOFepLSjD3ytseXuPTeC1+bdbxpQ4WqHU9VMWx2YV9yyviCsBhfbq\r\nAHu8glJT/tZEySk1IT3naPHanaXm0lph1MY=\r\n=SaRL\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.1.3": {"name": "@npmcli/config", "version": "6.1.3", "dependencies": {"ini": "^3.0.0", "nopt": "^7.0.0", "semver": "^7.3.5", "proc-log": "^3.0.0", "walk-up-path": "^1.0.0", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2"}, "devDependencies": {"tap": "^16.3.4", "@npmcli/template-oss": "4.11.4", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "8f513bf1a5dfa76f98d93d3cb6fb06b5f8960bd9", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-6.1.3.tgz", "fileCount": 12, "integrity": "sha512-c8uOXPbRq1K8YeyUszj6MBWIdB6bx6x3K/UbGmc5GF7qpv1mB893Y5SuyrH/J7Kcw24Im4ASrsztk38ZBWFS2Q==", "signatures": [{"sig": "MEYCIQDPYFbGSzYZUnWc+0MMgxGMX8umgOQm6vNjvO27bUyf1QIhAJKX2/qeD4zak6OChY/AIkg+XAwb6PtdF81a4NYQ4Dnd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50703, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4rmWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo8HA/5ACbHwjlQcCfIdtfYNmjEjRJbimMFQYKoY4LLZ6tvcFQYAIYm\r\n01W3mx6q9m6x+YCfFCZnQ0LJP2XaFQ3WCkyvayGHrjXl/eHO/CLRl/VyaQ3U\r\nzqbAHjU4q01PmjQqifz4MKNDVPDqHt1oUd25w0akCQToFCQLvwB+jXBCO7Sm\r\noZB5srCivStuzaR0UpZcv5rvthbF6XScA9pk262le1xjXlHu72LGh5Mn4PWc\r\n5pJvvwrtwqyhfkI4cBmx9cPFpWI6WcjMhd7iq+LkSLKP2PtfFQ1TljtQtRHE\r\nhjB3V+RlCANh3o3iMk260XY4ZV5d/QK5aLje2PM+aC7qr236ZqxjFhTWy9pp\r\na5k9HZrrEaMzzZfYQ+wQ6rIeFahX5maxUqwa/IJaGozxylXH0cxMIW8mPVqv\r\nnzW738HBWNw8MzBVQdtx66n7MflIUbKgo74LVePGcVaWs4+AxFt4P9y/mwct\r\nxGpfVEZ0HwcdA36DWZkFX+S1a3JQlzIQNgHmlOA9E6AxPuuvxUjEhT5d8GFL\r\nDmWFrbmuRHPzIs1phBi2a0dA+knbwmRiWDEaGxm7vNSv6lwDuspwTUZGz8Z5\r\nLYpSVJ2Tlk/pi53zuIRqtdYq2pWlBfpF9y1831FtzOeUns1JRKGzinjCc004\r\ngGoYBBZenJNzUdYOOMPGAyPd0ZUNa3CN5tE=\r\n=VWds\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.1.4": {"name": "@npmcli/config", "version": "6.1.4", "dependencies": {"ini": "^3.0.0", "nopt": "^7.0.0", "semver": "^7.3.5", "proc-log": "^3.0.0", "walk-up-path": "^1.0.0", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2"}, "devDependencies": {"tap": "^16.3.4", "@npmcli/template-oss": "4.12.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "b7751d6629892b0e1bd33b2f377e66a5ea1f7765", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-6.1.4.tgz", "fileCount": 12, "integrity": "sha512-3bIf/86iQ9ac86hy5uzE1kQnwgd9W/kmZ+K/OmYWuBMX97PQi4yLZHqn2xtfukwF/3/6NjUPxYC1H/aP3nImCA==", "signatures": [{"sig": "MEUCIFYNfsW+MIWFg/qZ03y1Das74PUY+ujZwaSPPehmT1AgAiEA7htdu0d1FWTRSrRZ/BSRarMA3JL3DAmlYkKD0K7M46Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50797, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkEfKdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqQkRAAmqBNkcF9oMgmt2ii+QYcMs+obohNpoxvDlbfudFTpg9fUFTY\r\nCeDc66thnVoI/DhFseg7q0j4Ihl6H0I6Q6NQDhT44LFciDr50e6CHoAVFm/X\r\ncTVSDDxzllABmqbaaXyLqWl++nDlUdAx1WG2DhRxUwC38Aeh+7txn8lQvtmY\r\n8lf+s9UUgcs38av8Mx3W/o0CYCIkopwQBK/Hgp7M+PKtrAI7oR1acfUyZ8fC\r\n08g6ZsqgVgJmBupEcqOk+ZPiM0J33dnFh7tt0dbLQT2+hy1b80FU1dGFqylV\r\n8sI3pgNGM0sXkAmSg6uPDXEvaXk8Jw+v6yR7k5dJrNsTriflctZ89+qbLEMI\r\n3jAYgc2tp175cLXkpYvqbJizGjtH7Cwf9Aekjgb3zuwyCssFIjhvfcKCi7Dp\r\nkoO34DUrTeUJgHGesNDAMFIzUxZSfZbaGxqKffvFNKzRUfEpiG3kE+V8BpkD\r\nkN6kX4q3l8abvKi34D4LzqZL1bY2V1DIpTml43oA2VK/s4aa1hk/m1SMLLVE\r\neFFXq59fAyQJqA9mqYUQWQdvTBl0qtYVp4/9RUDj2vBJGth5AXHjTdoLGfLn\r\n7+9yLF3JPOwNCPip0nxFddvMZNgdNjYMjChMyf5WOonrmroxvpHDETdexOvK\r\nuHrygVk6BnfBf+f52daZ0jGkTX3Z0Zti62o=\r\n=t4kG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.1.5": {"name": "@npmcli/config", "version": "6.1.5", "dependencies": {"ini": "^3.0.0", "nopt": "^7.0.0", "semver": "^7.3.5", "proc-log": "^3.0.0", "walk-up-path": "^1.0.0", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2"}, "devDependencies": {"tap": "^16.3.4", "@npmcli/template-oss": "4.12.1", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "79f6e84dbe13f0ad14133898cb4cb904cab7097a", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-6.1.5.tgz", "fileCount": 12, "integrity": "sha512-nUjJ4+Xo2/XWoq5wsF9ajRtm5xADlLjtyBQGdGlWFRgHpYggZtxQP1XteM6SvEQsHI3P9vDyLGIYO0MlNrQZwQ==", "signatures": [{"sig": "MEQCIHjqE89Ke2Nuy3g3FiqyONcjPPvLX6kcfiMUQChtcGeiAiA1aOP/+iMFDss6Htd1lNZhptV+6cClq7eYVJvgeC7nKA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50797}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.1.6": {"name": "@npmcli/config", "version": "6.1.6", "dependencies": {"ini": "^4.1.0", "nopt": "^7.0.0", "semver": "^7.3.5", "proc-log": "^3.0.0", "walk-up-path": "^3.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2"}, "devDependencies": {"tap": "^16.3.4", "@npmcli/template-oss": "4.12.1", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "2a7f3cb63a23242f80063d4625e6754969681440", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-6.1.6.tgz", "fileCount": 12, "integrity": "sha512-TM5dwgaz3Un2T5rdHQ6lX+Jj3TQxK6aV1U5OLByZiUS5qnA0NgC6U0aSESQVy80emToz8dtX3aniXD24wRnBaw==", "signatures": [{"sig": "MEQCIG2ibRHEJ6o4uWluehIrcdWNSI03j9xTcKFlul6g5cF8AiAiL63vqLr2MyVAW2fTRv81TCIBB5tcTVWZ00+Y31Xskg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50707, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkQGKLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqObg//a6phxAJRVuNetlVGEsoVb78CzNM6Vqs6zx0yYrTQf47aoyOC\r\nexaAKADFRbvkw0unc4wXOPxFK66D7Mkx09YItwNCvV7bIljlBtp40XSte8pW\r\nVjUgCdhtJvrRfLGpcja2E/qoNUiNopeRkdmhv9+XR2a2DlVJbd+3GkrcfqQb\r\nYu2aLgWZexJx4F2hWL+o2ZUCDtg4kLCVemdkt+owVQFrxXh/IqPl9iPxWXcE\r\nui7QhZlPRO3hgaq/Ni1reJGU/6XgU7rElz7gknVZMO9SRQr7C/CLGVr01DBU\r\nvXwV3Pb9h6SmCKrfdH0QYnWvvP0yifrzAqfUCh39a1SXM0T4ux40FCOZEbuI\r\nK3qXtGmtqoVS5C/LPA6wZzfeAacZJBhoIj277HCn8odI269iCfzdFIsIL/A7\r\nwig9IqF+ulER/8zZjvmJgmhPIa3QaqcGyUtjzHyDG2llsM1ju77gNFxjo/aZ\r\nsy+c7A3tRiYTiEwQOmq7s35MiGZcHfLxnYT1pSK5dKGy+9lk1so/ohZeoC2O\r\nIyAAA1g3WgaW6dzhUeNfEkr9hp/FIf1PAF8UwKJckts4TvmwMky4QAHfl58I\r\n0Mzlxsm63j8Zy9wi7CRjozT0aeTh54wkEmTsQCJ3MiIfyzOj/wIvlbnGEj2B\r\nQGRDUUzRUc4I/nTZdP5+XmQndE2LwstOntE=\r\n=JHWN\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.1.7": {"name": "@npmcli/config", "version": "6.1.7", "dependencies": {"ini": "^4.1.0", "nopt": "^7.0.0", "semver": "^7.3.5", "proc-log": "^3.0.0", "walk-up-path": "^3.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2"}, "devDependencies": {"tap": "^16.3.4", "@npmcli/template-oss": "4.14.1", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "88bcaf76e0f1f36ebf422b18513cf3bec6af781f", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-6.1.7.tgz", "fileCount": 12, "integrity": "sha512-DyACY6Mv7TH1kz2iBgwS3xE7jKsY+ukUfDyY5PLl9LZTktmBBSybDNzX3bUii+SD4j77Bx6EvgS/jsaUtV7Fng==", "signatures": [{"sig": "MEQCIGhYqidR5M3+ZtmRELdNfoGYr0Z+PaKdYpSsY7aibauPAiAdXE29hid1boQwYZq8csz9Pe18Qtk0T4qaXG1gBHgaMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50853}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.2.0": {"name": "@npmcli/config", "version": "6.2.0", "dependencies": {"ini": "^4.1.0", "nopt": "^7.0.0", "semver": "^7.3.5", "proc-log": "^3.0.0", "walk-up-path": "^3.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2"}, "devDependencies": {"tap": "^16.3.4", "@npmcli/template-oss": "4.14.1", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "5b6624e7db871d04ddf82dfaa2fa2648c78a6ba3", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-6.2.0.tgz", "fileCount": 12, "integrity": "sha512-lPAPNVUvlv6x0uwGiKzuWVUy1WSBaK5P0t9PoQQVIAbc1RaJLkaNxyUQZOrFJ7Y/ShzLw5skzruThhD9Qcju/A==", "signatures": [{"sig": "MEQCIBpQE/VxoK0B6dF9DQyWwymqmyZuyHVpsgl1WF5APwfiAiBI12sUGjNgCh/n4iPjaYfX2VzOrNjMarF8X4rNtJCWbw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51158}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.2.1": {"name": "@npmcli/config", "version": "6.2.1", "dependencies": {"ini": "^4.1.0", "nopt": "^7.0.0", "semver": "^7.3.5", "ci-info": "^3.8.0", "proc-log": "^3.0.0", "walk-up-path": "^3.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2"}, "devDependencies": {"tap": "^16.3.4", "@npmcli/mock-globals": "^1.0.0", "@npmcli/template-oss": "4.14.1", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "954cc09b727b6bfc087cb1d2a48994dc342e45cc", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-6.2.1.tgz", "fileCount": 15, "integrity": "sha512-Cj/OrSbrLvnwWuzquFCDTwFN8QmR+SWH6qLNCBttUreDkKM5D5p36SeSMbcEUiCGdwjUrVy2yd8C0REwwwDPEw==", "signatures": [{"sig": "MEUCIBiaS+7YOF6/pB6BSi6hqUejEKxUjNPdeYm/qCGzsjmYAiEA33Q2aQVZbHOMM5kaUuMmHCDE3eR/in5AVSinKmIjTnY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 126131}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "7.0.0": {"name": "@npmcli/config", "version": "7.0.0", "dependencies": {"ini": "^4.1.0", "nopt": "^7.0.0", "semver": "^7.3.5", "ci-info": "^3.8.0", "proc-log": "^3.0.0", "walk-up-path": "^3.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2"}, "devDependencies": {"tap": "^16.3.4", "@npmcli/mock-globals": "^1.0.0", "@npmcli/template-oss": "4.18.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "1ab2a9273f2b3feb77827e10c8fc19826a8ab740", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-7.0.0.tgz", "fileCount": 15, "integrity": "sha512-emDJpkPENEWBEk8Wk/W3/2ufra0fhiOsf9VLi3uwkjxbKv+enM4e9WCJEVf5Mlj2n63Qm6bifinQruek6fdnqA==", "signatures": [{"sig": "MEUCICwjyP20+1pEw62lELy327ijpIT9C1LGAE38gGoB7GpBAiEAvmEutwAZqB3TyItyM5EKUqvZYmS+c7gPAn+Ma3aNUiE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 124819}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "7.0.1": {"name": "@npmcli/config", "version": "7.0.1", "dependencies": {"ini": "^4.1.0", "nopt": "^7.0.0", "semver": "^7.3.5", "ci-info": "^3.8.0", "proc-log": "^3.0.0", "walk-up-path": "^3.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2"}, "devDependencies": {"tap": "^16.3.8", "@npmcli/mock-globals": "^1.0.0", "@npmcli/template-oss": "4.18.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "1ffd3bf36a032afad9f00329c995cbd8cdf08a70", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-7.0.1.tgz", "fileCount": 15, "integrity": "sha512-e3nDYpEgZp/68u7SVVzwFKVLoHsi4/cC24o820xbuLFxeJ81amubldPjP79+1Szvu3ec3owp0v/Uq4NVmSTXFw==", "signatures": [{"sig": "MEUCIQD0729gYuFakbrkRfVa3na3k14AhGE8X7EN2zvAKnrEiAIgRmbqUQ09wQzyF/eQC4+KzImCWNAkkEZUk5qPP13+RCY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 125154}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "7.1.0": {"name": "@npmcli/config", "version": "7.1.0", "dependencies": {"ini": "^4.1.0", "nopt": "^7.0.0", "semver": "^7.3.5", "ci-info": "^3.8.0", "proc-log": "^3.0.0", "walk-up-path": "^3.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2"}, "devDependencies": {"tap": "^16.3.8", "@npmcli/mock-globals": "^1.0.0", "@npmcli/template-oss": "4.18.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "99c67a45eb7f44d704e80b93c82aa4f6e3eddbe9", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-7.1.0.tgz", "fileCount": 15, "integrity": "sha512-n0LgXsaz7ZzCNq71nk5jGAwKGRYSJl+9TWQfJTvqIATmftw1M6PaY2/SE9u4sICYHa95zH1BQyB6ZUqPZAd1CA==", "signatures": [{"sig": "MEYCIQD/oge1rg1qrPRRJvV1Rni32q4oxjgFosG3n6trDW468wIhALOvZFoCEp5WLX/jO5Nuw9k+Fl30RVv9DX68fnw6fMWn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 125154}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "7.2.0": {"name": "@npmcli/config", "version": "7.2.0", "dependencies": {"ini": "^4.1.0", "nopt": "^7.0.0", "semver": "^7.3.5", "ci-info": "^3.8.0", "proc-log": "^3.0.0", "walk-up-path": "^3.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2"}, "devDependencies": {"tap": "^16.3.8", "@npmcli/mock-globals": "^1.0.0", "@npmcli/template-oss": "4.18.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "7f6b5aa750b2ffef613becb5413c00e3e37d51cf", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-7.2.0.tgz", "fileCount": 15, "integrity": "sha512-2NZBnYQwjuWhVE6E4zP4nN1uxfqiUMFg1KLJ9qWL2sG5/qDYb6V72uoo7C7Do3NRlSdVbR2ytBUoJcdWuhs7tQ==", "signatures": [{"sig": "MEUCIQDi588cS/ngfK+7x1qMeyr3asFCfb9a8/6CWPJ8aV31iQIgdAmozcz29dlN/JH4wmcrnHRPUw0JpGBe54TA1jzwSzA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 125436}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.3.0": {"name": "@npmcli/config", "version": "6.3.0", "dependencies": {"ini": "^4.1.0", "nopt": "^7.0.0", "semver": "^7.3.5", "ci-info": "^3.8.0", "proc-log": "^3.0.0", "walk-up-path": "^3.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2"}, "devDependencies": {"tap": "^16.3.4", "@npmcli/mock-globals": "^1.0.0", "@npmcli/template-oss": "4.19.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "9fda323682fdd0505e9584358f6de502b0d01a81", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-6.3.0.tgz", "fileCount": 15, "integrity": "sha512-gV64pm5cQ7F2oeoSJ5HTfaKxjFsvC4dAbCsQbtbOkEOymM6iZI62yNGCOLjcq/rfYX9+wVn34ThxK7GZpUwWFg==", "signatures": [{"sig": "MEUCIQCPmrb1WjN2+qUPXtCq45qru/N8Mn3fniM0+5rO3dR00AIgCRIu+VaJxaF5tW1GICnejGzT/VCqaMNt79BRfzAUG1U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 126689}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "8.0.0": {"name": "@npmcli/config", "version": "8.0.0", "dependencies": {"ini": "^4.1.0", "nopt": "^7.0.0", "semver": "^7.3.5", "ci-info": "^3.8.0", "proc-log": "^3.0.0", "walk-up-path": "^3.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2"}, "devDependencies": {"tap": "^16.3.8", "@npmcli/mock-globals": "^1.0.0", "@npmcli/template-oss": "4.19.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "2d298c84026b7a946a3fad3f5ff280144ca72c50", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-8.0.0.tgz", "fileCount": 15, "integrity": "sha512-a2ybqstXSCAbP7QghgGcOvLTBlaR3wWQyAmTfWXJld6qP6+vKQabTZQwzRPs00kKi870beNZHhV4Fvlca2l/uA==", "signatures": [{"sig": "MEYCIQDz6pYEtAgwn6MQw2q+0dj5ZBJY3WRmfyhJNBLJwQ6SywIhANSmqQCKGJiZoswA2Am319vtMaSYqt3A5AFNpDzGO3uS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 126105}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "6.4.0": {"name": "@npmcli/config", "version": "6.4.0", "dependencies": {"ini": "^4.1.0", "nopt": "^7.0.0", "semver": "^7.3.5", "ci-info": "^3.8.0", "proc-log": "^3.0.0", "walk-up-path": "^3.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2"}, "devDependencies": {"tap": "^16.3.4", "@npmcli/mock-globals": "^1.0.0", "@npmcli/template-oss": "4.19.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "3b1ddfa0c452fd09beac2cf05ca49b76c7a36bc8", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-6.4.0.tgz", "fileCount": 15, "integrity": "sha512-/fQjIbuNVIT/PbXvw178Tm97bxV0E0nVUFKHivMKtSI2pcs8xKdaWkHJxf9dTI0G/y5hp/KuCvgcUu5HwAtI1w==", "signatures": [{"sig": "MEYCIQCMd7z6IDkDjcGjiuMDjcdcrFsQrDhPl8X0bEJcvLN+5wIhAMy4NgGGNiQMh9QwiYQfkkoztFPM2j9QX+6pRAdj9gSp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 127195}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "8.0.1": {"name": "@npmcli/config", "version": "8.0.1", "dependencies": {"ini": "^4.1.0", "nopt": "^7.0.0", "semver": "^7.3.5", "ci-info": "^3.8.0", "proc-log": "^3.0.0", "walk-up-path": "^3.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2"}, "devDependencies": {"tap": "^16.3.8", "@npmcli/mock-globals": "^1.0.0", "@npmcli/template-oss": "4.19.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "7918186541276946f1ea550a8e47abb7eafb7460", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-8.0.1.tgz", "fileCount": 15, "integrity": "sha512-NKGmMYv/YTLwJr+qK9CvADSe82NTM9AFwFFpsZpVcPCT3XTdxvJBdXi8xvXWjHSCMb0Cb+7FtU/a5qqguCOhxA==", "signatures": [{"sig": "MEUCIBDcyp3XZbZexSk2/speFqBACgfxxr9PwipHgKn8NgbmAiEArEQjh/v7OWco6dolGTKdK+uSl+PeILrZ4RA3jkZiAb4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 126005}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "8.0.2": {"name": "@npmcli/config", "version": "8.0.2", "dependencies": {"ini": "^4.1.0", "nopt": "^7.0.0", "semver": "^7.3.5", "ci-info": "^4.0.0", "proc-log": "^3.0.0", "walk-up-path": "^3.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2"}, "devDependencies": {"tap": "^16.3.8", "@npmcli/mock-globals": "^1.0.0", "@npmcli/template-oss": "4.19.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "61f50dbf9a1b032e341cb4cf4f74c34d3f43d358", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-8.0.2.tgz", "fileCount": 15, "integrity": "sha512-g+DAp7bUsiUDynRI4jTO0wPAXJpy3puhovHHg8S4iq54NF8zd00588GJOm5ARxDaDUdKCUYu9E5fNc+esYl5JA==", "signatures": [{"sig": "MEUCIASNw7tYd34iupQcHUlFVfOMr/v2/C6e9ODnJqDlKf49AiEAt6uXTDjvaikQN469kC8DnR2H8R1V9NSgDNWv1BkFCcw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 126005}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "8.0.3": {"name": "@npmcli/config", "version": "8.0.3", "dependencies": {"ini": "^4.1.0", "nopt": "^7.0.0", "semver": "^7.3.5", "ci-info": "^4.0.0", "proc-log": "^3.0.0", "walk-up-path": "^3.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2"}, "devDependencies": {"tap": "^16.3.8", "@npmcli/mock-globals": "^1.0.0", "@npmcli/template-oss": "4.21.3", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "deacc4217060f3bf3e751cfdc5dd91481595aecd", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-8.0.3.tgz", "fileCount": 15, "integrity": "sha512-rqRX7/UORvm2YRImY67kyfwD9rpi5+KXXb1j/cpTUKRcUqvpJ9/PMMc7Vv57JVqmrFj8siBBFEmXI3Gg7/TonQ==", "signatures": [{"sig": "MEYCIQC5bHw1HQk/Ueq7xDj6wVYyZVVX/Tbd8UXjJhR6InXA8wIhALdEp8YX+mksK4VjKpKrtKGAycLPwqSUIu5alQqU7B3R", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 126007}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "8.1.0": {"name": "@npmcli/config", "version": "8.1.0", "dependencies": {"ini": "^4.1.0", "nopt": "^7.0.0", "semver": "^7.3.5", "ci-info": "^4.0.0", "proc-log": "^3.0.0", "walk-up-path": "^3.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2"}, "devDependencies": {"tap": "^16.3.8", "@npmcli/mock-globals": "^1.0.0", "@npmcli/template-oss": "4.21.3", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "2c7f6f80d78b9c18d8a70ae7c5fdb481be727bb0", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-8.1.0.tgz", "fileCount": 15, "integrity": "sha512-61LNEybTFaa9Z/f8y6X9s2Blc75aijZK67LxqC5xicBcfkw8M/88nYrRXGXxAUKm6GRlxTZ216dp1UK2+TbaYw==", "signatures": [{"sig": "MEQCIGHaB4WMjwT4CHRlpWjULWtmpm6xIpkqsiKlrd53OGtZAiAqqNa1A09WSMTJ9wzv5irAC3fE6BR3NBSwx0nj4XQFrQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 126215}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "6.4.1": {"name": "@npmcli/config", "version": "6.4.1", "dependencies": {"ini": "^4.1.0", "nopt": "^7.0.0", "semver": "^7.3.5", "ci-info": "^4.0.0", "proc-log": "^3.0.0", "walk-up-path": "^3.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2"}, "devDependencies": {"tap": "^16.3.4", "@npmcli/mock-globals": "^1.0.0", "@npmcli/template-oss": "4.21.3", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "006409c739635db008e78bf58c92421cc147911d", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-6.4.1.tgz", "fileCount": 15, "integrity": "sha512-uSz+elSGzjCMANWa5IlbGczLYPkNI/LeR+cHrgaTqTrTSh9RHhOFA4daD2eRUz6lMtOW+Fnsb+qv7V2Zz8ML0g==", "signatures": [{"sig": "MEQCIEnPVGEgj10FTQ44/MTon/4QgJ5gNYXBoepkwshGxMKWAiBTkJpQKnoKyXfwSqc4Hvxlk7XFbR3yiQW4NDQiMQwxzg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 127216}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "8.2.0": {"name": "@npmcli/config", "version": "8.2.0", "dependencies": {"ini": "^4.1.0", "nopt": "^7.0.0", "semver": "^7.3.5", "ci-info": "^4.0.0", "proc-log": "^3.0.0", "walk-up-path": "^3.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2"}, "devDependencies": {"tap": "^16.3.8", "@npmcli/mock-globals": "^1.0.0", "@npmcli/template-oss": "4.21.3", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "18774fc7239cfcc124ca9fdc48b1f65bb7bee191", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-8.2.0.tgz", "fileCount": 15, "integrity": "sha512-YoEYZFg0hRSRP/Chmq+J4FvULFvji6SORUYWQc10FiJ+ReAnViXcDCENg6kM6dID04bAoKNUygrby798+gYBbQ==", "signatures": [{"sig": "MEUCIQDsinSdlTKYd33/xFwku7tWH3YO74SSjBSSgqdZ8GxEggIgLgX5KjzHHhVTQRewGQ6kaVkRGZjUGgBSWy/uRs2UzuU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 126823}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "8.2.1": {"name": "@npmcli/config", "version": "8.2.1", "dependencies": {"ini": "^4.1.2", "nopt": "^7.0.0", "semver": "^7.3.5", "ci-info": "^4.0.0", "proc-log": "^3.0.0", "walk-up-path": "^3.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2"}, "devDependencies": {"tap": "^16.3.8", "@npmcli/mock-globals": "^1.0.0", "@npmcli/template-oss": "4.21.3", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "770a59cf69f486b7e05bec354ce149449b1018f9", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-8.2.1.tgz", "fileCount": 15, "integrity": "sha512-G4PknBr51bwCuY63wXSO8OakSoyHk11JYhxAZCayCAosJruX86lAstCfbr/2Fr+g6OqVz6PPfOVZ98bcoc+eQA==", "signatures": [{"sig": "MEUCIQDf8PC42TtF8VgLecwDW2encuCAMiM14A67ZktiTaZOWAIgUzKh3E2LOGzaLo68d4CX99NLV/ZxxOi9eDst3wpIsYo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 126823}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "8.2.2": {"name": "@npmcli/config", "version": "8.2.2", "dependencies": {"ini": "^4.1.2", "nopt": "^7.0.0", "semver": "^7.3.5", "ci-info": "^4.0.0", "proc-log": "^3.0.0", "walk-up-path": "^3.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2"}, "devDependencies": {"tap": "^16.3.8", "@npmcli/mock-globals": "^1.0.0", "@npmcli/template-oss": "4.21.3", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "7383559f04e753cad007c845defaacd0c47c6e30", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-8.2.2.tgz", "fileCount": 15, "integrity": "sha512-VvMHPIzcsKHCaNh9h4kCbn7NyDtcNJFMOUZ8Wu9SWhds5Egr1gMGU2fv+M50P1V5iAUZWZcv2Iguo5HTckpzww==", "signatures": [{"sig": "MEYCIQCB9RjgjtkgPDjZmJISp4LRYcg/zF4EBsUdVbR/kGLfHgIhANQRDX+rfZkEkndDsmPrnVdIEkl15kA/yuA6fv7d6Quk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134087}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "8.3.0": {"name": "@npmcli/config", "version": "8.3.0", "dependencies": {"ini": "^4.1.2", "nopt": "^7.0.0", "semver": "^7.3.5", "ci-info": "^4.0.0", "proc-log": "^4.2.0", "walk-up-path": "^3.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2"}, "devDependencies": {"tap": "^16.3.8", "@npmcli/mock-globals": "^1.0.0", "@npmcli/template-oss": "4.21.3", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "eb7aa7f228253c6bf3b77b4a03cf4d7659511909", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-8.3.0.tgz", "fileCount": 15, "integrity": "sha512-wQ0byz/w7jQZ+koT5tJtDFDVC16ye82P6frhGklu9KesZEiEqHoq1IQLhS2TPzvrkuWq/i3Id9oFreLT7KHlVQ==", "signatures": [{"sig": "MEUCIA3RzxQmxvcMr23Uu9XIwbdx1BYPmxxkc6YlLPwY3krzAiEAmDVPyZjf9793j37DVhO3O54fJ3OcfJ02FIFweHDWjwM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 133158}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "8.3.1": {"name": "@npmcli/config", "version": "8.3.1", "dependencies": {"ini": "^4.1.2", "nopt": "^7.0.0", "semver": "^7.3.5", "ci-info": "^4.0.0", "proc-log": "^4.2.0", "walk-up-path": "^3.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2"}, "devDependencies": {"tap": "^16.3.8", "@npmcli/mock-globals": "^1.0.0", "@npmcli/template-oss": "4.21.3", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "14814f52f4fb0293e45bfb1d3e6877c1d1cc514b", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-8.3.1.tgz", "fileCount": 15, "integrity": "sha512-lEY3TnkVrNUwI0vCDTFlKTbxK9DxZ83JmXXcQI7kp7pyg7zj/a36xSDmcikXvUbtV2PQpmUwmV0HDAB94NcgNA==", "signatures": [{"sig": "MEUCIAvNNPLobqwJicwcgqAkrSbRhdYKOTFhrfHcI6j0CfHwAiEA7DIc2w5zsa74H0OCqucpvw35GKjGUd0/9XaobzFIxwU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 133004}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "8.3.2": {"name": "@npmcli/config", "version": "8.3.2", "dependencies": {"ini": "^4.1.2", "nopt": "^7.2.1", "semver": "^7.3.5", "ci-info": "^4.0.0", "proc-log": "^4.2.0", "walk-up-path": "^3.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2"}, "devDependencies": {"tap": "^16.3.8", "@npmcli/mock-globals": "^1.0.0", "@npmcli/template-oss": "4.22.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "0fc36ab61a07df3bbe4ef4988c1db872b8ba1137", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-8.3.2.tgz", "fileCount": 15, "integrity": "sha512-IMzf+fhRXibqh9mBwXK/QFIr97SAlZjfwsWPEz/2pST1cE9k9LcwznO7aDNXJoMrDjxPHZmb2bAAKASsa6EedA==", "signatures": [{"sig": "MEYCIQDTSWbd9cCKxwnC+BbKGvBsJSrNSF2wxJiHjhAuPX+yMwIhAOX3VnrDayc20RjOteefeiSaOs36ychu5die0gvf2zHY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 133335}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "8.3.3": {"name": "@npmcli/config", "version": "8.3.3", "dependencies": {"ini": "^4.1.2", "nopt": "^7.2.1", "semver": "^7.3.5", "ci-info": "^4.0.0", "proc-log": "^4.2.0", "walk-up-path": "^3.0.1", "@npmcli/map-workspaces": "^3.0.2", "read-package-json-fast": "^3.0.2"}, "devDependencies": {"tap": "^16.3.8", "@npmcli/mock-globals": "^1.0.0", "@npmcli/template-oss": "4.22.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "213658d2c85dc40c7b4f9231d993678bb0c57f66", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-8.3.3.tgz", "fileCount": 15, "integrity": "sha512-sIMKHiiYr91ALiHjhPq64F5P/SCaiSyDfpNmgYHtlIJtLY445+3+r3VoREzpdDrOwIqwQ6iEHinbTfaocL0UgA==", "signatures": [{"sig": "MEQCICTY1443C1kPb+XdDZWTSDT87zR1BKZN+IW3+MAp0PISAiBUUHGWzQTBSOU/XKt+l2CatnG8fKrCnYodmP6lTV5QDw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 133490}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "8.3.4": {"name": "@npmcli/config", "version": "8.3.4", "dependencies": {"ini": "^4.1.2", "nopt": "^7.2.1", "semver": "^7.3.5", "ci-info": "^4.0.0", "proc-log": "^4.2.0", "walk-up-path": "^3.0.1", "@npmcli/package-json": "^5.1.1", "@npmcli/map-workspaces": "^3.0.2"}, "devDependencies": {"tap": "^16.3.8", "@npmcli/mock-globals": "^1.0.0", "@npmcli/template-oss": "4.22.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "e2712c2215bb2659f39718b23bf7401f9ac1da59", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-8.3.4.tgz", "fileCount": 15, "integrity": "sha512-01rtHedemDNhUXdicU7s+QYz/3JyV5Naj84cvdXGH4mgCdL+agmSYaLF4LUG4vMCLzhBO8YtS0gPpH1FGvbgAw==", "signatures": [{"sig": "MEYCIQCgBWy6qlFfV2+vZHVb+kFJT82TZVRnOU9hEh1CN/XdlgIhAM7GvOz5UGBnTsv5KXYwgLxawx+H8XkOZdLoof5t1AGz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 133516}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "9.0.0": {"name": "@npmcli/config", "version": "9.0.0", "dependencies": {"ini": "^5.0.0", "nopt": "^8.0.0", "semver": "^7.3.5", "ci-info": "^4.0.0", "proc-log": "^5.0.0", "walk-up-path": "^3.0.1", "@npmcli/package-json": "^6.0.1", "@npmcli/map-workspaces": "^4.0.1"}, "devDependencies": {"tap": "^16.3.8", "@npmcli/mock-globals": "^1.0.0", "@npmcli/template-oss": "4.23.3", "@npmcli/eslint-config": "^5.0.1"}, "dist": {"shasum": "bd810a1e9e23fcfad800e40d6c2c8b8f4f4318e1", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-9.0.0.tgz", "fileCount": 15, "integrity": "sha512-P5Vi16Y+c8E0prGIzX112ug7XxqfaPFUVW/oXAV+2VsxplKZEnJozqZ0xnK8V8w/SEsBf+TXhUihrEIAU4CA5Q==", "signatures": [{"sig": "MEQCIGyHkjZ+RMVLQl72k0tQmdNfWrRld+Twnle2SPlYv3UMAiBdb0xdMMjuP/AAjLJ5NO5IMz4bjdoJci4uL1HLFQRqLw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 133550}, "engines": {"node": "^18.17.0 || >=20.5.0"}}, "10.0.0-pre.0": {"name": "@npmcli/config", "version": "10.0.0-pre.0", "dependencies": {"ini": "^5.0.0", "nopt": "^8.0.0", "semver": "^7.3.5", "ci-info": "^4.0.0", "proc-log": "^5.0.0", "walk-up-path": "^3.0.1", "@npmcli/package-json": "^6.0.1", "@npmcli/map-workspaces": "^4.0.1"}, "devDependencies": {"tap": "^16.3.8", "@npmcli/mock-globals": "^1.0.0", "@npmcli/template-oss": "4.23.5", "@npmcli/eslint-config": "^5.0.1"}, "dist": {"shasum": "89684bc0d92424cb36c5de12ead7bcb3156db47b", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-10.0.0-pre.0.tgz", "fileCount": 15, "integrity": "sha512-lrhm3pudeLXJGGr8Xy3mbuLQ4Lg2jYef8Wi+m1/zneftvVx3S/gmx5yCA9hdHdxRfEaUKRw2TWb/cGelilXMCw==", "signatures": [{"sig": "MEQCIB3z5A02oGyMA23MHgOzmFWpOfX1im7IqYYlL32h7DuPAiAhVwtVFzKKWdLKmgyPC6XCwiczKV/OueBQup/HbMnN2w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 133557}, "engines": {"node": "^20.17.0 || >=22.9.0"}}, "10.0.0-pre.1": {"name": "@npmcli/config", "version": "10.0.0-pre.1", "dependencies": {"ini": "^5.0.0", "nopt": "^8.0.0", "semver": "^7.3.5", "ci-info": "^4.0.0", "proc-log": "^5.0.0", "walk-up-path": "^4.0.0", "@npmcli/package-json": "^6.0.1", "@npmcli/map-workspaces": "^4.0.1"}, "devDependencies": {"tap": "^16.3.8", "@npmcli/mock-globals": "^1.0.0", "@npmcli/template-oss": "4.23.5", "@npmcli/eslint-config": "^5.0.1"}, "dist": {"shasum": "73a3d273537ad7719273938c06077f31cc07ef50", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-10.0.0-pre.1.tgz", "fileCount": 15, "integrity": "sha512-XhPf+DKirAj6rXuOduuOO8NjkgLqAVHcPs5LF+w/F7FDTWxaBoSVajBzm5yFXQWdWXZLnWqiMraLofMci4BY/A==", "signatures": [{"sig": "MEQCIEmltYGo1MrDDwjNacul7tw/xBva0N33RYF+8zUjFP7lAiBdobh+S1hSOyZnbKitaANLiWh9DoDLNdkmVxeZ+XB9Ng==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 133545}, "engines": {"node": "^20.17.0 || >=22.9.0"}}, "10.0.0": {"name": "@npmcli/config", "version": "10.0.0", "dependencies": {"ini": "^5.0.0", "nopt": "^8.0.0", "semver": "^7.3.5", "ci-info": "^4.0.0", "proc-log": "^5.0.0", "walk-up-path": "^4.0.0", "@npmcli/package-json": "^6.0.1", "@npmcli/map-workspaces": "^4.0.1"}, "devDependencies": {"tap": "^16.3.8", "@npmcli/mock-globals": "^1.0.0", "@npmcli/template-oss": "4.23.6", "@npmcli/eslint-config": "^5.0.1"}, "dist": {"shasum": "41aff849fe643f57be6b7f5a62fec17464f79865", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-10.0.0.tgz", "fileCount": 15, "integrity": "sha512-SXnVmSOQKfxouhrTV2sA2s6KV9v+nfLuddljCl5CtNS89AkVk5Bqkz8wqoqIzR6KjAGjiP1zXDZokqHR5kPYoQ==", "signatures": [{"sig": "MEQCIEJCp7VTK0oY2cvQHJQcHaYt82T5dBJW36TiA/b3fgRtAiB04uM21Fsl0GEmRcH1OO2wTm96oDoEjILtYObCF8eMkg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 133539}, "engines": {"node": "^20.17.0 || >=22.9.0"}}, "10.0.1": {"name": "@npmcli/config", "version": "10.0.1", "dependencies": {"ini": "^5.0.0", "nopt": "^8.0.0", "semver": "^7.3.5", "ci-info": "^4.0.0", "proc-log": "^5.0.0", "walk-up-path": "^4.0.0", "@npmcli/package-json": "^6.0.1", "@npmcli/map-workspaces": "^4.0.1"}, "devDependencies": {"tap": "^16.3.8", "@npmcli/mock-globals": "^1.0.0", "@npmcli/template-oss": "4.23.6", "@npmcli/eslint-config": "^5.0.1"}, "dist": {"shasum": "ee569d6448b959050e92f9ce4b32de8996e7d58d", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-10.0.1.tgz", "fileCount": 15, "integrity": "sha512-772OMXb+ItCUAIfajIynTVDkNgPmrYY367NgCrcPHEHQljjZdbU2qpufk1GIxUeJnkutVFLfc5XyWyzdTO7buw==", "signatures": [{"sig": "MEUCIQD+tNs4tn9vvnGp+ddPkggiKp7TDs56sZSr8CDYDp+wQwIgVH+DqrhluVUBVq//ZTSPPnrVJ9pqO6gyW9vde1vFYcQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 133539}, "engines": {"node": "^20.17.0 || >=22.9.0"}}, "10.1.0": {"name": "@npmcli/config", "version": "10.1.0", "dependencies": {"ini": "^5.0.0", "nopt": "^8.1.0", "semver": "^7.3.5", "ci-info": "^4.0.0", "proc-log": "^5.0.0", "walk-up-path": "^4.0.0", "@npmcli/package-json": "^6.0.1", "@npmcli/map-workspaces": "^4.0.1"}, "devDependencies": {"tap": "^16.3.8", "@npmcli/mock-globals": "^1.0.0", "@npmcli/template-oss": "4.23.6", "@npmcli/eslint-config": "^5.0.1"}, "dist": {"shasum": "fe6a0bd26da87ddc92c85e0443b8ef3855c359c0", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-10.1.0.tgz", "fileCount": 15, "integrity": "sha512-ygyCJATTr+xmuQHiX28adNT3tbDcWIfHQggNtLL2ykSyH4VCF5YeG4SilZaYIxf+72GZA6CJpESaDyhq9Boozg==", "signatures": [{"sig": "MEYCIQDO22ouJefiLFfzywo+YihBR0hMC24+WNGJJiRn8pOefgIhALbHwg2tWhPq6sYSsf7psvGFipLyIO6WsDhUhG0B+dRA", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 135675}, "engines": {"node": "^20.17.0 || >=22.9.0"}}, "10.2.0": {"name": "@npmcli/config", "version": "10.2.0", "dependencies": {"ini": "^5.0.0", "nopt": "^8.1.0", "semver": "^7.3.5", "ci-info": "^4.0.0", "proc-log": "^5.0.0", "walk-up-path": "^4.0.0", "@npmcli/package-json": "^6.0.1", "@npmcli/map-workspaces": "^4.0.1"}, "devDependencies": {"tap": "^16.3.8", "@npmcli/mock-globals": "^1.0.0", "@npmcli/template-oss": "4.23.6", "@npmcli/eslint-config": "^5.0.1"}, "dist": {"shasum": "a36b82422b09ca141983c11777bbc87fe4cba019", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-10.2.0.tgz", "fileCount": 15, "integrity": "sha512-02No1usuJiw847jivY4XbhJXLt28OgLbs4TRf9zd2rmLU03py98Gpinf7yZQhLt0jh+CI+bvcULrwOnzX29eXA==", "signatures": [{"sig": "MEUCIQDqEDw+pgkYfkwrkzlhqiLWLusJLB2Il7pVPOIOw7Qu6gIgYsAFXcBvLVLD8xGFpIldnZLzwsrGA/25WRrLl1GlKX8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 136736}, "engines": {"node": "^20.17.0 || >=22.9.0"}}, "10.3.0": {"name": "@npmcli/config", "version": "10.3.0", "dependencies": {"@npmcli/map-workspaces": "^4.0.1", "@npmcli/package-json": "^6.0.1", "ci-info": "^4.0.0", "ini": "^5.0.0", "nopt": "^8.1.0", "proc-log": "^5.0.0", "semver": "^7.3.5", "walk-up-path": "^4.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^5.0.1", "@npmcli/mock-globals": "^1.0.0", "@npmcli/template-oss": "4.23.6", "tap": "^16.3.8"}, "dist": {"integrity": "sha512-52n09DvIdZq3Hd2Uc8OngwEU9PS4MJ439H6TGd10vpPL5Yp9BTw11sbrjxrJsSIz/msxkOPig0UQDjBjsPGr5A==", "shasum": "0566f6312af14f5b96ea4bd55a50319043747d22", "tarball": "https://registry.npmjs.org/@npmcli/config/-/config-10.3.0.tgz", "fileCount": 15, "unpackedSize": 137173, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDjs95+FY5LjO6ZyMQgpH5p5gXZgmCHxhMKPUsmOL9uJAIgGwwcDzmdoUMQjQX2mrqW8ZwCJ8EmgWFUIpiOn8ko+38="}]}, "engines": {"node": "^20.17.0 || >=22.9.0"}}}, "modified": "2025-05-15T17:00:40.400Z"}