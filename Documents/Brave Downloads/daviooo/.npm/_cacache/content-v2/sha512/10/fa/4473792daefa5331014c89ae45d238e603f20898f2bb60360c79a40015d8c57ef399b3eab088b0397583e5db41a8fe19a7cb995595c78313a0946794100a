{"_id": "underscore.string", "_rev": "154-6fb7abcc838be7c9c827bd8fa47c25de", "name": "underscore.string", "description": "String manipulation extensions for Underscore.js javascript library.", "dist-tags": {"latest": "3.3.6"}, "versions": {"0.9.2": {"name": "underscore.string", "version": "0.9.2", "description": "String manipulation extensions for Underscore.js javascript library.", "homepage": "https://github.com/edtsech/underscore.string", "contributors": [{"name": "Esa<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://esa-matti.suuronen.org/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://koss.nocorp.me/"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "keywords": ["underscore", "string"], "directories": {"lib": "./lib"}, "engines": {"node": "*"}, "dependencies": {"underscore": "1.1.4"}, "repository": {"type": "git", "url": "git://github.com/edtsech/underscore.string.git"}, "bugs": {"url": "https://github.com/edtsech/underscore.string/issues"}, "licenses": [{"type": "MIT"}], "_npmUser": {"name": "edtsech", "email": "<EMAIL>"}, "_id": "underscore.string@0.9.2", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.104", "_nodeVersion": "v0.6.0", "_defaultsLoaded": true, "dist": {"shasum": "023b07c749835d918119fb82d41cbf9a4bc42a94", "tarball": "https://registry.npmjs.org/underscore.string/-/underscore.string-0.9.2.tgz", "integrity": "sha512-i3vGqVAa02VcWvaj6qqOY8nafGkE4mhPpMzlUW5W269kEgj2XSxypHWc509HWlsH7fEPy/KvktqavWfivF2e0A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCpP8Ig84n4nisbCnsQB1uHtBuoklGWZ9z18oeucbjchgIhAOEo89Jrroyq/j/gUv49SYnOYYxIgT/covl8jbJAF8Kt"}]}, "maintainers": [{"name": "edtsech", "email": "<EMAIL>"}]}, "1.0.0": {"name": "underscore.string", "version": "1.0.0", "description": "String manipulation extensions for Underscore.js javascript library.", "homepage": "https://github.com/edtsech/underscore.string", "contributors": [{"name": "Esa<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://esa-matti.suuronen.org/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://koss.nocorp.me/"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "keywords": ["underscore", "string"], "directories": {"lib": "./lib"}, "engines": {"node": "*"}, "dependencies": {"underscore": "1.1.4"}, "repository": {"type": "git", "url": "git://github.com/edtsech/underscore.string.git"}, "bugs": {"url": "https://github.com/edtsech/underscore.string/issues"}, "licenses": [{"type": "MIT"}], "_npmUser": {"name": "edtsech", "email": "<EMAIL>"}, "_id": "underscore.string@1.0.0", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.104", "_nodeVersion": "v0.6.0", "_defaultsLoaded": true, "dist": {"shasum": "7f003cae4e79aa6a833b316426d6a43b77986007", "tarball": "https://registry.npmjs.org/underscore.string/-/underscore.string-1.0.0.tgz", "integrity": "sha512-UfYoC5DccLzEKyw3Ah7ajGzi6C3Xl+aOCX1s7N6Ux8a+dc38uY2cx3tjklZ5FDfmESjNp6EKe5tLqirwKFkekA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBM8PWIJeRXgU4fXrr5nZ9Th9KHs6KlsqAdas6f5RBybAiEAvIUAnuQ933eb7DgbR8Ia2SOgY0q49t2DSRORd2LyjwI="}]}, "maintainers": [{"name": "edtsech", "email": "<EMAIL>"}]}, "1.1.3": {"name": "underscore.string", "version": "1.1.3", "description": "String manipulation extensions for Underscore.js javascript library.", "homepage": "https://github.com/edtsech/underscore.string", "contributors": [{"name": "Esa<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://esa-matti.suuronen.org/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://koss.nocorp.me/"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "keywords": ["underscore", "string"], "main": "./lib/underscore.string", "directories": {"lib": "./lib"}, "engines": {"node": "*"}, "dependencies": {"underscore": "1.1.4"}, "repository": {"type": "git", "url": "git://github.com/edtsech/underscore.string.git"}, "bugs": {"url": "https://github.com/edtsech/underscore.string/issues"}, "licenses": [{"type": "MIT"}], "_npmUser": {"name": "edtsech", "email": "<EMAIL>"}, "_id": "underscore.string@1.1.3", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.104", "_nodeVersion": "v0.6.0", "_defaultsLoaded": true, "dist": {"shasum": "eed5284c6dfba2ed6db7e212d12537bcff6d7341", "tarball": "https://registry.npmjs.org/underscore.string/-/underscore.string-1.1.3.tgz", "integrity": "sha512-81z1g8ly9RokjuclbkLhoypxHW0+yf0EpCUD1JrOsRvHwlXCQdK81m/6//W5D9i+CY9Zw60FH/JjeLEx4TrUNw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB7z2ZVenow0QMXEKGF1UgNLbNiBTbeDACVsYpiJrp3pAiEAybvNFcHL8ZKolN86kgdHdih6GpP8W5WUdqy4Mxzl4Yw="}]}, "maintainers": [{"name": "edtsech", "email": "<EMAIL>"}]}, "1.1.4": {"name": "underscore.string", "version": "1.1.4", "description": "String manipulation extensions for Underscore.js javascript library.", "homepage": "https://github.com/edtsech/underscore.string", "contributors": [{"name": "Esa<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://esa-matti.suuronen.org/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://koss.nocorp.me/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "https://github.com/kruckenb", "url": "<https://github.com/kruckenb>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://tchak.net>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://funkatron.com>"}], "keywords": ["underscore", "string"], "main": "./lib/underscore.string", "directories": {"lib": "./lib"}, "engines": {"node": "*"}, "dependencies": {"underscore": "1.1.6"}, "repository": {"type": "git", "url": "git://github.com/edtsech/underscore.string.git"}, "bugs": {"url": "https://github.com/edtsech/underscore.string/issues"}, "licenses": [{"type": "MIT"}], "_npmUser": {"name": "edtsech", "email": "<EMAIL>"}, "_id": "underscore.string@1.1.4", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.104", "_nodeVersion": "v0.6.0", "_defaultsLoaded": true, "dist": {"shasum": "9be06b23b8e3d996ea2020f9984202069e3dee12", "tarball": "https://registry.npmjs.org/underscore.string/-/underscore.string-1.1.4.tgz", "integrity": "sha512-WsF8NWzIbTvxUaSOpSLq+AiO0tzweXdWQZ4w9Op8S/1BT9Fh7hCS7bfrF17vZu9kJg3pcqO+8WXfQSr1ah0f2g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC3/dQXkP6ek3nOkR4XXc2BCzMJKwEQdURlufxt2ziFowIhALrEWnxf1rTwC4uPb3CgStSmKBkDmKUtR5K1sdD7jpZr"}]}, "maintainers": [{"name": "edtsech", "email": "<EMAIL>"}]}, "1.1.5": {"name": "underscore.string", "version": "1.1.5", "description": "String manipulation extensions for Underscore.js javascript library.", "homepage": "https://github.com/edtsech/underscore.string", "contributors": [{"name": "Esa<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://esa-matti.suuronen.org/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://koss.nocorp.me/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "https://github.com/kruckenb", "url": "<https://github.com/kruckenb>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://tchak.net>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://funkatron.com>"}], "keywords": ["underscore", "string"], "main": "./lib/underscore.string", "directories": {"lib": "./lib"}, "engines": {"node": "*"}, "dependencies": {"underscore": "1.1.6"}, "repository": {"type": "git", "url": "git://github.com/edtsech/underscore.string.git"}, "bugs": {"url": "https://github.com/edtsech/underscore.string/issues"}, "licenses": [{"type": "MIT"}], "_npmUser": {"name": "edtsech", "email": "<EMAIL>"}, "_id": "underscore.string@1.1.5", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.104", "_nodeVersion": "v0.6.0", "_defaultsLoaded": true, "dist": {"shasum": "50ea8e6803230b3d6d1a4abdf77ba9b479ec3158", "tarball": "https://registry.npmjs.org/underscore.string/-/underscore.string-1.1.5.tgz", "integrity": "sha512-Ft4eNTkrKgAP3wo8vJfaIiONBH22XE4M7z8gqZgeN2LRimy/ZZ5MUd0MR7c9xKy/i4kVrZOYJrPOWHpTPG7oVg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCGkb9dNhJkeS8FrGqjjdK6qP0pMbmFwHd+DPH3WtEoRQIgbqvBwo3tOxqjww+I25waI/8HOC5Qqr9omDUdh4KF7J4="}]}, "maintainers": [{"name": "edtsech", "email": "<EMAIL>"}]}, "1.1.6": {"name": "underscore.string", "version": "1.1.6", "description": "String manipulation extensions for Underscore.js javascript library.", "homepage": "https://github.com/edtsech/underscore.string", "contributors": [{"name": "Esa<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://esa-matti.suuronen.org/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://koss.nocorp.me/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "https://github.com/kruckenb", "url": "<https://github.com/kruckenb>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://tchak.net>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://funkatron.com>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "keywords": ["underscore", "string"], "main": "./lib/underscore.string", "directories": {"lib": "./lib"}, "engines": {"node": "*"}, "dependencies": {"underscore": "1.1.7"}, "repository": {"type": "git", "url": "git://github.com/edtsech/underscore.string.git"}, "bugs": {"url": "https://github.com/edtsech/underscore.string/issues"}, "licenses": [{"type": "MIT"}], "_npmUser": {"name": "edtsech", "email": "<EMAIL>"}, "_id": "underscore.string@1.1.6", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.104", "_nodeVersion": "v0.6.0", "_defaultsLoaded": true, "dist": {"shasum": "e7dca073ccd945515a6a69e7af484aee4e52cdc9", "tarball": "https://registry.npmjs.org/underscore.string/-/underscore.string-1.1.6.tgz", "integrity": "sha512-FTvqbu8cqAzjwnjobi5X/rWjaryVA4XULdnyqFTRLD7FpKfuBNhFXqUjw9Scv5l39nmUtUMU3OGBogaaCpgFCg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDOx2PyoZt4qNRdo213HNyPNq5YGtMiEdaTQRCYkNJbLQIgRzGReL55zfuR4XuS+/iQoVhLpR4C2tk6zzZ0u0kD4Pc="}]}, "maintainers": [{"name": "edtsech", "email": "<EMAIL>"}]}, "2.0.0": {"name": "underscore.string", "version": "2.0.0", "description": "String manipulation extensions for Underscore.js javascript library.", "homepage": "http://epeli.github.com/underscore.string/", "contributors": [{"name": "Esa<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://esa-matti.suuronen.org/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://koss.nocorp.me/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "https://github.com/kruckenb", "url": "<https://github.com/kruckenb>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://tchak.net>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://funkatron.com>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "keywords": ["underscore", "string"], "main": "./lib/underscore.string", "directories": {"lib": "./lib"}, "engines": {"node": "*"}, "dependencies": {}, "repository": {"type": "git", "url": "git://github.com/epeli/underscore.string.git"}, "bugs": {"url": "https://github.com/epeli/underscore.string/issues"}, "licenses": [{"type": "MIT"}], "_npmUser": {"name": "edtsech", "email": "<EMAIL>"}, "_id": "underscore.string@2.0.0", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.104", "_nodeVersion": "v0.6.0", "_defaultsLoaded": true, "dist": {"shasum": "7470858a54a0bb3560d037da56dcc67b5181e11a", "tarball": "https://registry.npmjs.org/underscore.string/-/underscore.string-2.0.0.tgz", "integrity": "sha512-36kUytY+40vDx+/MqchfGG6/QDy/FZPR0RY/vKIEU55yAvxivOwE46rIKMQ2uCIfwjxbI0PQpmljQ2MK80oYDA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBgeqn5UTuVKDsF891irSjDpce1rTGW201p7vnvgr8oTAiEAs910Hxe7XyEUd+34dwr/9PVvo5Gfr83I0ukevXgw2OY="}]}, "maintainers": [{"name": "edtsech", "email": "<EMAIL>"}]}, "2.1.0": {"name": "underscore.string", "version": "2.1.0", "description": "String manipulation extensions for Underscore.js javascript library.", "homepage": "http://epeli.github.com/underscore.string/", "contributors": [{"name": "Esa<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://esa-matti.suuronen.org/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://koss.nocorp.me/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "https://github.com/kruckenb", "url": "<https://github.com/kruckenb>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://tchak.net>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://funkatron.com>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "keywords": ["underscore", "string"], "main": "./lib/underscore.string", "directories": {"lib": "./lib"}, "engines": {"node": "*"}, "repository": {"type": "git", "url": "git://github.com/epeli/underscore.string.git"}, "bugs": {"url": "https://github.com/epeli/underscore.string/issues"}, "licenses": [{"type": "MIT"}], "_npmUser": {"name": "epeli", "email": "<EMAIL>"}, "_id": "underscore.string@2.1.0", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.0-2", "_nodeVersion": "v0.6.8", "_defaultsLoaded": true, "dist": {"shasum": "75d68f94d862636f509bb8e86854d5397a930c39", "tarball": "https://registry.npmjs.org/underscore.string/-/underscore.string-2.1.0.tgz", "integrity": "sha512-TV24rkH9I2AS7VQf+/C7jIH3PrSPNk+QAWFKcFamiy2y+GO8XPBh1zaPk/US/HL1E3eEtQv63tsAIiUgd+7nhA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCakxPucvQ51MrA0OTPNdFdvtAM0AwkgzTYNumK3O6S9QIhAJwiQBl5jorTU7fP2eS51Va4pps+Kss9tpuN0gwqRgL+"}]}, "maintainers": [{"name": "edtsech", "email": "<EMAIL>"}, {"name": "rwz", "email": "<EMAIL>"}, {"name": "epeli", "email": "<EMAIL>"}]}, "2.1.1": {"name": "underscore.string", "version": "2.1.1", "description": "String manipulation extensions for Underscore.js javascript library.", "homepage": "http://epeli.github.com/underscore.string/", "contributors": [{"name": "Esa<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://esa-matti.suuronen.org/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://koss.nocorp.me/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "https://github.com/kruckenb", "url": "<https://github.com/kruckenb>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://tchak.net>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://funkatron.com>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "keywords": ["underscore", "string"], "main": "./lib/underscore.string", "directories": {"lib": "./lib"}, "engines": {"node": "*"}, "repository": {"type": "git", "url": "git://github.com/epeli/underscore.string.git"}, "bugs": {"url": "https://github.com/epeli/underscore.string/issues"}, "licenses": [{"type": "MIT"}], "_npmUser": {"name": "rwz", "email": "<EMAIL>"}, "_id": "underscore.string@2.1.1", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.4", "_nodeVersion": "v0.6.14", "_defaultsLoaded": true, "dist": {"shasum": "458397799114b9b67f6030bb527b0afae689c061", "tarball": "https://registry.npmjs.org/underscore.string/-/underscore.string-2.1.1.tgz", "integrity": "sha512-k9n9MA9xnuWlvG36XR8Jf+ZjVkYeEg0g+CMhXYUQ9bik/Fo3F0N3zAueSQOvocnxKE5cQwV8YlcpBrCG6o4PeQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCANhsML7zTLEZM5C8zjHFXv2uglXK8owVnu3KcRKjpcgIgaaW3VdgpdoHCjUiGiBlg9OXmHjiUjiQP902ZHkGpe70="}]}, "maintainers": [{"name": "edtsech", "email": "<EMAIL>"}, {"name": "rwz", "email": "<EMAIL>"}, {"name": "epeli", "email": "<EMAIL>"}]}, "2.3.0": {"name": "underscore.string", "version": "2.3.0", "description": "String manipulation extensions for Underscore.js javascript library.", "homepage": "http://epeli.github.com/underscore.string/", "contributors": [{"name": "Esa<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://esa-matti.suuronen.org/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<https://github.com/rwz>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://koss.nocorp.me/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "https://github.com/kruckenb", "url": "<https://github.com/kruckenb>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://tchak.net>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://funkatron.com>"}], "keywords": ["underscore", "string"], "main": "./lib/underscore.string", "directories": {"lib": "./lib"}, "engines": {"node": "*"}, "repository": {"type": "git", "url": "https://github.com/epeli/underscore.string.git"}, "bugs": {"url": "https://github.com/epeli/underscore.string/issues"}, "licenses": [{"type": "MIT"}], "_id": "underscore.string@2.3.0", "dist": {"shasum": "c437fd9916e2d05ebb89c45bf1ddaedbb4b9ab02", "tarball": "https://registry.npmjs.org/underscore.string/-/underscore.string-2.3.0.tgz", "integrity": "sha512-Vr35XgsQv1FsVwCmHE3O/hsQqHOxzPH2wu7UK1BSaEgkpeY/+yT+nNyvst9CriT8gdU7WhAGhdKGlF35MJ6Flg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDdvrmjJazl8TjT3QCCfTFDFxp+shKpHLn2bVtnZaSc6AIgDH6WHJ4GfVczKUNFFIybClzvbfxdiU9QOK31c4c3BGc="}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "rwz", "email": "<EMAIL>"}, "maintainers": [{"name": "edtsech", "email": "<EMAIL>"}, {"name": "rwz", "email": "<EMAIL>"}, {"name": "epeli", "email": "<EMAIL>"}]}, "2.3.1": {"name": "underscore.string", "version": "2.3.1", "description": "String manipulation extensions for Underscore.js javascript library.", "homepage": "http://epeli.github.com/underscore.string/", "contributors": [{"name": "Esa<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://esa-matti.suuronen.org/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<https://github.com/rwz>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://koss.nocorp.me/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "https://github.com/kruckenb", "url": "<https://github.com/kruckenb>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://tchak.net>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://funkatron.com>"}], "keywords": ["underscore", "string"], "main": "./lib/underscore.string", "directories": {"lib": "./lib"}, "engines": {"node": "*"}, "repository": {"type": "git", "url": "https://github.com/epeli/underscore.string.git"}, "bugs": {"url": "https://github.com/epeli/underscore.string/issues"}, "licenses": [{"type": "MIT"}], "_id": "underscore.string@2.3.1", "dist": {"shasum": "e2328b0c09818a8f617026427d348f96ad1099d9", "tarball": "https://registry.npmjs.org/underscore.string/-/underscore.string-2.3.1.tgz", "integrity": "sha512-i3W7dA8seyhwvNs0hltQ4e5nIrs4k2ZkVfIPONfHcU1WF6Ni2jBKtqhpMXufyhjRA6nTTGVdE5X1yiPCJY/UMA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDI3UN4sfBNBvGOdKhZhajEg7z7iWF9meXF6Yww/s2ILAiEAq4do5UY3K0MQLsnEayP2Q4TIDnkr9TUrxbZAc/JpNwM="}]}, "_npmVersion": "1.1.66", "_npmUser": {"name": "rwz", "email": "<EMAIL>"}, "maintainers": [{"name": "edtsech", "email": "<EMAIL>"}, {"name": "rwz", "email": "<EMAIL>"}, {"name": "epeli", "email": "<EMAIL>"}]}, "2.3.2": {"name": "underscore.string", "version": "2.3.2", "description": "String manipulation extensions for Underscore.js javascript library.", "homepage": "http://epeli.github.com/underscore.string/", "contributors": [{"name": "Esa<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://esa-matti.suuronen.org/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<https://github.com/rwz>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://koss.nocorp.me/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "https://github.com/kruckenb", "url": "<https://github.com/kruckenb>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://tchak.net>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://funkatron.com>"}], "keywords": ["underscore", "string"], "main": "./lib/underscore.string", "directories": {"lib": "./lib"}, "engines": {"node": "*"}, "repository": {"type": "git", "url": "https://github.com/epeli/underscore.string.git"}, "bugs": {"url": "https://github.com/epeli/underscore.string/issues"}, "licenses": [{"type": "MIT"}], "_id": "underscore.string@2.3.2", "dist": {"shasum": "c10835dc04c2677cd49e1d77d26c0d3b22cc5709", "tarball": "https://registry.npmjs.org/underscore.string/-/underscore.string-2.3.2.tgz", "integrity": "sha512-18TQoG0jbK2hyjmhWR5i6A434b3jxj8VGNWtCyeEg2jRQZ9gXhuSSGaRICNnzziIIHirpvphbymEi4rphClFvQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGCKmLPisGTBXvTma92fSGwG+brLy5gqz6E052W4+beBAiAEvBUgwXbpZ8H+CeIlwjgQDx4D0vCONFFDnqHhtMxvzA=="}]}, "_from": ".", "_npmVersion": "1.2.32", "_npmUser": {"name": "epeli", "email": "<EMAIL>"}, "maintainers": [{"name": "edtsech", "email": "<EMAIL>"}, {"name": "rwz", "email": "<EMAIL>"}, {"name": "epeli", "email": "<EMAIL>"}]}, "2.2.1": {"name": "underscore.string", "version": "2.2.1", "description": "String manipulation extensions for Underscore.js javascript library.", "homepage": "http://epeli.github.com/underscore.string/", "contributors": [{"name": "Esa<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://esa-matti.suuronen.org/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://koss.nocorp.me/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "https://github.com/kruckenb", "url": "<https://github.com/kruckenb>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://tchak.net>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://funkatron.com>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "keywords": ["underscore", "string"], "main": "./lib/underscore.string", "directories": {"lib": "./lib"}, "engines": {"node": "*"}, "repository": {"type": "git", "url": "https://github.com/epeli/underscore.string.git"}, "bugs": {"url": "https://github.com/epeli/underscore.string/issues"}, "licenses": [{"type": "MIT"}], "_id": "underscore.string@2.2.1", "dist": {"shasum": "d7c0fa2af5d5a1a67f4253daee98132e733f0f19", "tarball": "https://registry.npmjs.org/underscore.string/-/underscore.string-2.2.1.tgz", "integrity": "sha512-3FVmhXqelrj6gfgp3Bn6tOavJvW0dNH2T+heTD38JRxIrAbiuzbqjknszoOYj3DyFB1nWiLj208Qt2no/L4cIA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCG16Ak0J9h3abCD+kok27IqWVPFn+AkDsSJvW9ORQuKwIgbrITxIHej//ys9o8jSp1W0bzH+BW1PAJDx2J/PHBxOo="}]}, "_from": ".", "_npmVersion": "1.2.32", "_npmUser": {"name": "epeli", "email": "<EMAIL>"}, "maintainers": [{"name": "edtsech", "email": "<EMAIL>"}, {"name": "rwz", "email": "<EMAIL>"}, {"name": "epeli", "email": "<EMAIL>"}]}, "2.2.0-rc": {"name": "underscore.string", "version": "2.2.0-rc", "description": "String manipulation extensions for Underscore.js javascript library.", "homepage": "http://epeli.github.com/underscore.string/", "contributors": [{"name": "Esa<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://esa-matti.suuronen.org/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://koss.nocorp.me/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "https://github.com/kruckenb", "url": "<https://github.com/kruckenb>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://tchak.net>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://funkatron.com>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "keywords": ["underscore", "string"], "main": "./lib/underscore.string", "directories": {"lib": "./lib"}, "engines": {"node": "*"}, "repository": {"type": "git", "url": "https://github.com/epeli/underscore.string.git"}, "bugs": {"url": "https://github.com/epeli/underscore.string/issues"}, "licenses": [{"type": "MIT"}], "_id": "underscore.string@2.2.0-rc", "dist": {"shasum": "f03a19324d6af07c9bed2ee1a639745eb7f3b9aa", "tarball": "https://registry.npmjs.org/underscore.string/-/underscore.string-2.2.0rc.tgz", "integrity": "sha512-j+R4HRKmQwJgBiLAOlXfdT/XOoRzeyNCOk1iiD+9BZWmY2taTdlNJGMzfz9MXoebf7cZB2CDvPfa+X/ZQ+qzeQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEIXayFOndUxPXaO9JbAjDPxTjO+3oRq1at8sOocsqhtAiAu/dYaf4c3WtqT7dek/9lrq+5pJTnCdj/VHlGIpQS5UA=="}]}, "_from": ".", "_npmVersion": "1.2.32", "_npmUser": {"name": "epeli", "email": "<EMAIL>"}, "maintainers": [{"name": "edtsech", "email": "<EMAIL>"}, {"name": "rwz", "email": "<EMAIL>"}, {"name": "epeli", "email": "<EMAIL>"}]}, "2.3.3": {"name": "underscore.string", "version": "2.3.3", "description": "String manipulation extensions for Underscore.js javascript library.", "homepage": "http://epeli.github.com/underscore.string/", "contributors": [{"name": "Esa<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://esa-matti.suuronen.org/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<https://github.com/rwz>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://koss.nocorp.me/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "https://github.com/kruckenb", "url": "<https://github.com/kruckenb>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://tchak.net>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://funkatron.com>"}], "keywords": ["underscore", "string"], "main": "./lib/underscore.string", "directories": {"lib": "./lib"}, "engines": {"node": "*"}, "repository": {"type": "git", "url": "https://github.com/epeli/underscore.string.git"}, "bugs": {"url": "https://github.com/epeli/underscore.string/issues"}, "licenses": [{"type": "MIT"}], "_id": "underscore.string@2.3.3", "dist": {"shasum": "71c08bf6b428b1133f37e78fa3a21c82f7329b0d", "tarball": "https://registry.npmjs.org/underscore.string/-/underscore.string-2.3.3.tgz", "integrity": "sha512-hbD5MibthuDAu4yA5wxes5bzFgqd3PpBJuClbRxaNddxfdsz+qf+1kHwrGQFrmchmDHb9iNU+6EHDn8uj0xDJg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICGcabJJaOAUEwYtTENU/uwJBMcPu9PTzgfNZx2oigiFAiBS6P7Y35xSNZTHB9ykEH4nwUs9ojpPHVHFVepd5Q72tw=="}]}, "_from": ".", "_npmVersion": "1.2.32", "_npmUser": {"name": "epeli", "email": "<EMAIL>"}, "maintainers": [{"name": "edtsech", "email": "<EMAIL>"}, {"name": "rwz", "email": "<EMAIL>"}, {"name": "epeli", "email": "<EMAIL>"}]}, "2.4.0": {"name": "underscore.string", "version": "2.4.0", "description": "String manipulation extensions for Underscore.js javascript library.", "homepage": "http://epeli.github.com/underscore.string/", "contributors": [{"name": "Esa<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://esa-matti.suuronen.org/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<https://github.com/rwz>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://koss.nocorp.me/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "https://github.com/kruckenb", "url": "<https://github.com/kruckenb>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://tchak.net>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://funkatron.com>"}], "keywords": ["underscore", "string"], "main": "./lib/underscore.string.js", "directories": {"lib": "./lib"}, "engines": {"node": "*"}, "repository": {"type": "git", "url": "https://github.com/epeli/underscore.string.git"}, "bugs": {"url": "https://github.com/epeli/underscore.string/issues"}, "licenses": [{"type": "MIT"}], "scripts": {"test": "gulp test"}, "devDependencies": {"gulp": "~3.8.10", "gulp-uglify": "~1.0.1", "gulp-qunit": "~1.0.0", "gulp-clean": "~0.3.1", "gulp-rename": "~1.2.0"}, "_id": "underscore.string@2.4.0", "dist": {"shasum": "8cdd8fbac4e2d2ea1e7e2e8097c42f442280f85b", "tarball": "https://registry.npmjs.org/underscore.string/-/underscore.string-2.4.0.tgz", "integrity": "sha512-yxkabuCaIBnzfIvX3kBxQqCs0ar/bfJwDnFEHJUm/ZrRVhT3IItdRF5cZjARLzEnyQYtIUhsZ2LG2j3HidFOFQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCHPHv610kD1g3xa3cPeNU50QTsVKsqa3efQOQi+sZLQwIhANdjn2fG34pkD4/v8BUecNAWK0SjuxjwboO7mMJ0E23m"}]}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "epeli", "email": "<EMAIL>"}, "maintainers": [{"name": "edtsech", "email": "<EMAIL>"}, {"name": "rwz", "email": "<EMAIL>"}, {"name": "epeli", "email": "<EMAIL>"}]}, "3.0.0": {"name": "underscore.string", "version": "3.0.0", "description": "String manipulation extensions for Underscore.js javascript library.", "homepage": "http://epeli.github.com/underscore.string/", "contributors": [{"name": "Esa<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://esa-matti.suuronen.org/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<https://github.com/rwz>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://koss.nocorp.me/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "https://github.com/kruckenb", "url": "<https://github.com/kruckenb>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://tchak.net>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://funkatron.com>"}], "keywords": ["underscore", "string"], "main": "./index.js", "directories": {"lib": "./"}, "engines": {"node": "*"}, "repository": {"type": "git", "url": "https://github.com/epeli/underscore.string.git"}, "bugs": {"url": "https://github.com/epeli/underscore.string/issues"}, "licenses": [{"type": "MIT"}], "scripts": {"test": "gulp test"}, "devDependencies": {"gulp": "~3.8.10", "gulp-clean": "~0.3.1", "gulp-qunit": "~1.1.0", "gulp-rename": "~1.2.0", "gulp-uglify": "~1.0.1", "gulp-param": "~0.6.3", "gulp-bump": "~0.1.11", "gulp-replace": "~0.5.0", "gulp-browserify": "~0.5.0"}, "jshintConfig": {"node": true, "browser": true, "qunit": true, "globals": {"s": true}}, "gitHead": "8d06d26eae2f81ba8d7be6153694aa7b9c0f6cc8", "_id": "underscore.string@3.0.0", "_shasum": "590328225efd712eebddfcf2a82797826130b7b4", "_from": ".", "_npmVersion": "2.1.7", "_nodeVersion": "0.10.22", "_npmUser": {"name": "epeli", "email": "<EMAIL>"}, "maintainers": [{"name": "edtsech", "email": "<EMAIL>"}, {"name": "rwz", "email": "<EMAIL>"}, {"name": "epeli", "email": "<EMAIL>"}], "dist": {"shasum": "590328225efd712eebddfcf2a82797826130b7b4", "tarball": "https://registry.npmjs.org/underscore.string/-/underscore.string-3.0.0.tgz", "integrity": "sha512-IgHQqG9JzDwnsPtJYOdYPfoRCO+H+IDJCHy0+tZZI+Y6n1nMaoM84IloVncwaHf+er2fS4u2TcgDZJRQR5sDUQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDM+hJfhjZ7MDba9/9xyai4ubymSg+JdVQbbzrNHMe2WQIhAJdKqkKAMK2YPEspcFnXjO/jAFUg6N7YDpg9UvnFzarY"}]}}, "3.0.1": {"name": "underscore.string", "version": "3.0.1", "description": "String manipulation extensions for Underscore.js javascript library.", "homepage": "http://epeli.github.com/underscore.string/", "contributors": [{"name": "Esa<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://esa-matti.suuronen.org/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<https://github.com/rwz>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://koss.nocorp.me/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "https://github.com/kruckenb", "url": "<https://github.com/kruckenb>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://tchak.net>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://funkatron.com>"}], "keywords": ["underscore", "string"], "main": "./index.js", "directories": {"lib": "./"}, "engines": {"node": "*"}, "repository": {"type": "git", "url": "https://github.com/epeli/underscore.string.git"}, "bugs": {"url": "https://github.com/epeli/underscore.string/issues"}, "licenses": [{"type": "MIT"}], "scripts": {"test": "gulp test"}, "devDependencies": {"gulp": "~3.8.10", "gulp-clean": "~0.3.1", "gulp-qunit": "~1.1.0", "gulp-rename": "~1.2.0", "gulp-uglify": "~1.0.1", "gulp-param": "~0.6.3", "gulp-bump": "~0.1.11", "gulp-replace": "~0.5.0", "gulp-browserify": "~0.5.0"}, "jshintConfig": {"node": true, "browser": true, "qunit": true, "globals": {"s": true}}, "gitHead": "53e5628973503136d1bebf4780b73d47169b3e03", "_id": "underscore.string@3.0.1", "_shasum": "ffe8da4ca7cd11923ede7cc5371aca029068b2fe", "_from": ".", "_npmVersion": "2.1.10", "_nodeVersion": "0.10.25", "_npmUser": {"name": "s<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "edtsech", "email": "<EMAIL>"}, {"name": "rwz", "email": "<EMAIL>"}, {"name": "epeli", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "ffe8da4ca7cd11923ede7cc5371aca029068b2fe", "tarball": "https://registry.npmjs.org/underscore.string/-/underscore.string-3.0.1.tgz", "integrity": "sha512-QXdELFJDT21GDr39nQNy2bqf8O2q7Yi3DsnxxIsL5IaTVjh060Gm6Mr55LMOWsqRfhd8bvNqaDZ7O3aoc5KZpQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC83EBzk+zHgIXabCmIN745HyssYPTA+1Dgxo27h6Ln8QIhAItlAnjNaDmcQ6ONtCaMkqSYTQN13LBgXxiGdG3II32l"}]}}, "3.0.2": {"name": "underscore.string", "version": "3.0.2", "description": "String manipulation extensions for Underscore.js javascript library.", "homepage": "http://epeli.github.com/underscore.string/", "contributors": [{"name": "Esa<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://esa-matti.suuronen.org/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<https://github.com/rwz>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://koss.nocorp.me/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "https://github.com/kruckenb", "url": "<https://github.com/kruckenb>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://tchak.net>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://funkatron.com>"}], "keywords": ["underscore", "string"], "main": "./index.js", "directories": {"lib": "./"}, "engines": {"node": "*"}, "repository": {"type": "git", "url": "https://github.com/epeli/underscore.string.git"}, "bugs": {"url": "https://github.com/epeli/underscore.string/issues"}, "licenses": [{"type": "MIT"}], "scripts": {"test": "gulp test"}, "devDependencies": {"gulp": "~3.8.10", "gulp-clean": "~0.3.1", "gulp-qunit": "~1.1.0", "gulp-rename": "~1.2.0", "gulp-uglify": "~1.0.1", "gulp-param": "~0.6.3", "gulp-bump": "~0.1.11", "gulp-replace": "~0.5.0", "gulp-browserify": "~0.5.0"}, "jshintConfig": {"node": true, "browser": true, "qunit": true, "globals": {"s": true}}, "gitHead": "4252b691e043520b02d32e31658f500824c88e8a", "_id": "underscore.string@3.0.2", "_shasum": "fd2a8544342527536cc906edc7063fcd5b70f306", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "epeli", "email": "<EMAIL>"}, "maintainers": [{"name": "edtsech", "email": "<EMAIL>"}, {"name": "rwz", "email": "<EMAIL>"}, {"name": "epeli", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "fd2a8544342527536cc906edc7063fcd5b70f306", "tarball": "https://registry.npmjs.org/underscore.string/-/underscore.string-3.0.2.tgz", "integrity": "sha512-gKJyC12DneSoBZThJE1psEl6A0SRL7hIAh1GqdvS2mRr9NEGmcGxUHLHp3cS3/B6kVF+KjZS0vlkxcZpXn5vGA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCGFGyOUX0xDg15IAIBOLJ5454F5Z7t9a3ZJHxmNcI1yAIgU7QHwmAz54y9JxrY/Na1jua+4PDKGq4cbw+SzNwTAhg="}]}}, "3.0.3": {"name": "underscore.string", "version": "3.0.3", "description": "String manipulation extensions for Underscore.js javascript library.", "homepage": "http://epeli.github.com/underscore.string/", "contributors": [{"name": "Esa<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://esa-matti.suuronen.org/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<https://github.com/rwz>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://koss.nocorp.me/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "https://github.com/kruckenb", "url": "<https://github.com/kruckenb>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://tchak.net>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://funkatron.com>"}], "keywords": ["underscore", "string"], "main": "./index.js", "directories": {"lib": "./"}, "engines": {"node": "*"}, "repository": {"type": "git", "url": "https://github.com/epeli/underscore.string.git"}, "bugs": {"url": "https://github.com/epeli/underscore.string/issues"}, "licenses": [{"type": "MIT"}], "scripts": {"test": "gulp test"}, "devDependencies": {"gulp": "~3.8.10", "gulp-clean": "~0.3.1", "gulp-qunit": "~1.1.0", "gulp-rename": "~1.2.0", "gulp-uglify": "~1.0.1", "gulp-param": "~0.6.3", "gulp-bump": "~0.1.11", "gulp-replace": "~0.5.0", "gulp-browserify": "~0.5.0"}, "jshintConfig": {"node": true, "browser": true, "qunit": true, "globals": {"s": true}}, "gitHead": "26630d119c2fa3dce47ea8325480c7eb321eaf82", "_id": "underscore.string@3.0.3", "_shasum": "4617b8c1a250cf6e5064fbbb363d0fa96cf14552", "_from": ".", "_npmVersion": "2.1.10", "_nodeVersion": "0.10.25", "_npmUser": {"name": "s<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "edtsech", "email": "<EMAIL>"}, {"name": "rwz", "email": "<EMAIL>"}, {"name": "epeli", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "4617b8c1a250cf6e5064fbbb363d0fa96cf14552", "tarball": "https://registry.npmjs.org/underscore.string/-/underscore.string-3.0.3.tgz", "integrity": "sha512-C5Tr0WP0M3/KXj9/NgY1sAqJfEG6DM+shfhpM8pt6P9Nt/SQdjXy72qP/jL5ech2M/PnkiWZetOhlQ4451TBOA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFvsiyvLbN2NfKctu8HpBBEGC/m3Yu1q9d35c2VSPvxzAiA9ep2D2UcBIiJjkA8r3yy4NJsc3pj4kle57V5hk3ylaQ=="}]}}, "3.1.0": {"name": "underscore.string", "version": "3.1.0", "description": "String manipulation extensions for Underscore.js javascript library.", "homepage": "http://epeli.github.com/underscore.string/", "contributors": [{"name": "Esa<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://esa-matti.suuronen.org/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<https://github.com/rwz>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://koss.nocorp.me/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "https://github.com/kruckenb", "url": "<https://github.com/kruckenb>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://tchak.net>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://funkatron.com>"}], "keywords": ["underscore", "string"], "main": "./index.js", "directories": {"lib": "./"}, "engines": {"node": "*"}, "repository": {"type": "git", "url": "https://github.com/epeli/underscore.string.git"}, "bugs": {"url": "https://github.com/epeli/underscore.string/issues"}, "license": "MIT", "scripts": {"test": "gulp test"}, "devDependencies": {"gulp": "^3.8.11", "gulp-bench": "^1.1.0", "gulp-browserify": "~0.5.0", "gulp-bump": "~0.1.11", "gulp-istanbul": "^0.6.0", "gulp-mocha": "^2.0.0", "gulp-param": "~0.6.3", "gulp-rename": "~1.2.0", "gulp-replace": "~0.5.0", "gulp-rimraf": "^0.1.1", "gulp-uglify": "~1.0.1", "mocha": "^2.1.0", "underscore": "^1.7.0"}, "jshintConfig": {"node": true, "browser": true, "qunit": true, "globals": {"s": true}}, "dependencies": {}, "gitHead": "780b27be523a9b3e4457197e72d70a289806f66e", "_id": "underscore.string@3.1.0", "_shasum": "bc0c4e4c3baf6ec4bd143b6185c257c9622e70ca", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "1.1.0", "_npmUser": {"name": "s<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "edtsech", "email": "<EMAIL>"}, {"name": "rwz", "email": "<EMAIL>"}, {"name": "epeli", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "bc0c4e4c3baf6ec4bd143b6185c257c9622e70ca", "tarball": "https://registry.npmjs.org/underscore.string/-/underscore.string-3.1.0.tgz", "integrity": "sha512-OihyYEL36ysGSnCYl+/IF105gp4m/jwtaWjnrHCBLUi9mhR0OHwISFFXRAZrBznvdUqkXQdNEoP/vy2HTrePlA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCQdRkfvuASmX+lfvbj2Ug9azCTSX1KV+Zcf/W8PAMJUgIhALlz7KPbB2ehLUdp+KjzMacVcVkLuQTJrJgDiqaJJtJD"}]}}, "3.1.1": {"name": "underscore.string", "version": "3.1.1", "description": "String manipulation extensions for Underscore.js javascript library.", "homepage": "http://epeli.github.com/underscore.string/", "contributors": [{"name": "Esa<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://esa-matti.suuronen.org/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<https://github.com/rwz>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://koss.nocorp.me/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "https://github.com/kruckenb", "url": "<https://github.com/kruckenb>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://tchak.net>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://funkatron.com>"}], "keywords": ["underscore", "string"], "main": "./index.js", "directories": {"lib": "./"}, "engines": {"node": "*"}, "repository": {"type": "git", "url": "https://github.com/epeli/underscore.string.git"}, "bugs": {"url": "https://github.com/epeli/underscore.string/issues"}, "license": "MIT", "scripts": {"test": "gulp test"}, "devDependencies": {"gulp": "^3.8.11", "gulp-bench": "^1.1.0", "gulp-browserify": "~0.5.0", "gulp-bump": "~0.1.11", "gulp-istanbul": "^0.6.0", "gulp-mocha": "^2.0.0", "gulp-param": "~0.6.3", "gulp-rename": "~1.2.0", "gulp-replace": "~0.5.0", "gulp-rimraf": "^0.1.1", "gulp-uglify": "~1.0.1", "mocha": "^2.1.0", "underscore": "^1.7.0"}, "jshintConfig": {"node": true, "browser": true, "qunit": true, "globals": {"s": true}}, "dependencies": {}, "gitHead": "ea91a4425a2ae2bb915c7e72c2b2b2dc464a63c4", "_id": "underscore.string@3.1.1", "_shasum": "0cdd6bcad0c046fd7663d305d8a785b5da10f335", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "1.1.0", "_npmUser": {"name": "s<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "edtsech", "email": "<EMAIL>"}, {"name": "rwz", "email": "<EMAIL>"}, {"name": "epeli", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "0cdd6bcad0c046fd7663d305d8a785b5da10f335", "tarball": "https://registry.npmjs.org/underscore.string/-/underscore.string-3.1.1.tgz", "integrity": "sha512-Rw3INEb4d2IPDCNnI8DnJp5kbQH5KsF9VuHxSgazNmjgHTANNhl2DCVvWS8ZcdSrs4+B/BRBX+90HWhlri9t2Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDiY658M/t0OmcFg93n9b611miWsl6mcdicjnOxSxpNZAiBH+LelySjztSM+oE/e71+Ou8hSfXeoz/JGHqMWjZVGNQ=="}]}}, "3.2.0": {"name": "underscore.string", "version": "3.2.0", "description": "String manipulation extensions for Underscore.js javascript library.", "homepage": "http://epeli.github.com/underscore.string/", "contributors": [{"name": "Esa<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://esa-matti.suuronen.org/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<https://github.com/rwz>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://koss.nocorp.me/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "https://github.com/kruckenb", "url": "<https://github.com/kruckenb>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://tchak.net>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://funkatron.com>"}], "keywords": ["underscore", "string"], "main": "./index.js", "directories": {"lib": "./"}, "engines": {"node": "*"}, "repository": {"type": "git", "url": "https://github.com/epeli/underscore.string.git"}, "bugs": {"url": "https://github.com/epeli/underscore.string/issues"}, "license": "MIT", "scripts": {"test": "gulp test"}, "devDependencies": {"gulp": "^3.8.11", "gulp-bench": "^1.1.0", "gulp-browserify": "~0.5.0", "gulp-bump": "~0.1.11", "gulp-header": "^1.2.2", "gulp-istanbul": "^0.6.0", "gulp-mocha": "^2.0.0", "gulp-param": "~0.6.3", "gulp-rename": "~1.2.0", "gulp-replace": "~0.5.0", "gulp-rimraf": "^0.1.1", "gulp-uglify": "~1.0.1", "mocha": "^2.1.0", "underscore": "^1.7.0"}, "jshintConfig": {"node": true, "browser": true, "qunit": true, "globals": {"s": true}}, "dependencies": {}, "gitHead": "e511cdf35cff5f4f92174d1b697b9bea3fdea3d1", "_id": "underscore.string@3.2.0", "_shasum": "4ff98c6fa2b973aef63a2a67df710d2dfa15609a", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "epeli", "email": "<EMAIL>"}, "maintainers": [{"name": "edtsech", "email": "<EMAIL>"}, {"name": "rwz", "email": "<EMAIL>"}, {"name": "epeli", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "4ff98c6fa2b973aef63a2a67df710d2dfa15609a", "tarball": "https://registry.npmjs.org/underscore.string/-/underscore.string-3.2.0.tgz", "integrity": "sha512-706I/g27QxPq1DP4qe8R9tFHq23Xrrz31ocP5MHsCaHJqqbe2Vv6RoxJG3yBeXt6uk0iLQ3iUk1FwxsiR/PgDg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDhsMYdZdpw7XbtIPhxebWJ002RfXF0IaSbtxwtvLN6ZwIgTqGBFZKKxYvPhtOyQjHlX8Dk3mDVEph3DxpQGS8ZWLM="}]}}, "3.2.1": {"name": "underscore.string", "version": "3.2.1", "description": "String manipulation extensions for Underscore.js javascript library.", "homepage": "http://epeli.github.com/underscore.string/", "contributors": [{"name": "Esa<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://esa-matti.suuronen.org/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<https://github.com/rwz>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://koss.nocorp.me/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "https://github.com/kruckenb", "url": "<https://github.com/kruckenb>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://tchak.net>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://funkatron.com>"}], "keywords": ["underscore", "string"], "main": "./index.js", "directories": {"lib": "./"}, "engines": {"node": "*"}, "repository": {"type": "git", "url": "https://github.com/epeli/underscore.string.git"}, "bugs": {"url": "https://github.com/epeli/underscore.string/issues"}, "license": "MIT", "scripts": {"test": "gulp test"}, "devDependencies": {"gulp": "^3.8.11", "gulp-bench": "^1.1.0", "gulp-browserify": "~0.5.0", "gulp-bump": "~0.1.11", "gulp-header": "^1.2.2", "gulp-istanbul": "^0.6.0", "gulp-mocha": "^2.0.0", "gulp-param": "~0.6.3", "gulp-rename": "~1.2.0", "gulp-replace": "~0.5.0", "gulp-rimraf": "^0.1.1", "gulp-uglify": "~1.0.1", "mocha": "^2.1.0", "underscore": "^1.7.0"}, "jshintConfig": {"node": true, "browser": true, "qunit": true, "globals": {"s": true}}, "dependencies": {}, "gitHead": "f8b613e57ebbf926213cd52c1a34cd4c5e705cbd", "_id": "underscore.string@3.2.1", "_shasum": "e6531d55ad1388fef3fb2df2a9ef2c80e61e11ce", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "epeli", "email": "<EMAIL>"}, "maintainers": [{"name": "edtsech", "email": "<EMAIL>"}, {"name": "rwz", "email": "<EMAIL>"}, {"name": "epeli", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e6531d55ad1388fef3fb2df2a9ef2c80e61e11ce", "tarball": "https://registry.npmjs.org/underscore.string/-/underscore.string-3.2.1.tgz", "integrity": "sha512-ltevq2WOCnHxsVx7L+Cllzhx5fl28Jfp+pejVye6fu9gwk+zjXfbSA9/Q3uIHgV4KZFkvFdD4DgeDSOrEa+OIg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDuV9Y/xi8nvJe+iPCrk5LLMZHvp9v/070lwu5AcoEPgwIhAMCSmS62uor1MnxT+yzzxkTIGDghzLI3AXmmKYQNLX/1"}]}}, "3.2.2": {"name": "underscore.string", "version": "3.2.2", "description": "String manipulation extensions for Underscore.js javascript library.", "homepage": "http://epeli.github.com/underscore.string/", "contributors": [{"name": "Esa<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://esa-matti.suuronen.org/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<https://github.com/rwz>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://koss.nocorp.me/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "https://github.com/kruckenb", "url": "<https://github.com/kruckenb>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://tchak.net>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://funkatron.com>"}], "keywords": ["underscore", "string"], "main": "./index.js", "directories": {"lib": "./"}, "engines": {"node": "*"}, "repository": {"type": "git", "url": "git+https://github.com/epeli/underscore.string.git"}, "bugs": {"url": "https://github.com/epeli/underscore.string/issues"}, "license": "MIT", "scripts": {"test": "gulp test"}, "devDependencies": {"gulp": "^3.8.11", "gulp-bench": "^1.1.0", "gulp-browserify": "~0.5.0", "gulp-bump": "~0.1.11", "gulp-header": "^1.2.2", "gulp-istanbul": "^0.6.0", "gulp-mocha": "^2.0.0", "gulp-param": "~0.6.3", "gulp-rename": "~1.2.0", "gulp-replace": "~0.5.0", "gulp-rimraf": "^0.1.1", "gulp-uglify": "~1.0.1", "mocha": "^2.1.0", "underscore": "^1.7.0"}, "jshintConfig": {"node": true, "browser": true, "qunit": true, "globals": {"s": true}}, "dependencies": {}, "gitHead": "522c5254a09b04d250db44c91f4a7f3a18e6c1fb", "_id": "underscore.string@3.2.2", "_shasum": "4ed47e9eafa575a28bd1f7bc6a5bb1d9302bb11b", "_from": ".", "_npmVersion": "2.14.2", "_nodeVersion": "0.10.33", "_npmUser": {"name": "epeli", "email": "<EMAIL>"}, "maintainers": [{"name": "edtsech", "email": "<EMAIL>"}, {"name": "rwz", "email": "<EMAIL>"}, {"name": "epeli", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "4ed47e9eafa575a28bd1f7bc6a5bb1d9302bb11b", "tarball": "https://registry.npmjs.org/underscore.string/-/underscore.string-3.2.2.tgz", "integrity": "sha512-XP0cSE3454EYLo5MHQ1006cUeM9sWT4YwmHW1MlLaQa6rcc9S2sY3T4EX5M0ku1+NhABYKJ9CAMcit6I87nl8Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDMiexa3Pglg4pM5mYXLdCuGHBABnEQnlBu4yt1S4DCqQIgEOhJRcjCG3x+s5SA1s5/HtX833yUeJyoGJQFQdRxE7Q="}]}}, "3.2.3": {"name": "underscore.string", "version": "3.2.3", "description": "String manipulation extensions for Underscore.js javascript library.", "homepage": "http://epeli.github.com/underscore.string/", "contributors": [{"name": "Esa<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://esa-matti.suuronen.org/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<https://github.com/rwz>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://koss.nocorp.me/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "https://github.com/kruckenb", "url": "<https://github.com/kruckenb>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://tchak.net>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://funkatron.com>"}], "keywords": ["underscore", "string"], "main": "./index.js", "directories": {"lib": "./"}, "engines": {"node": "*"}, "repository": {"type": "git", "url": "git+https://github.com/epeli/underscore.string.git"}, "bugs": {"url": "https://github.com/epeli/underscore.string/issues"}, "license": "MIT", "scripts": {"test": "gulp test"}, "devDependencies": {"gulp": "^3.8.11", "gulp-bench": "^1.1.0", "gulp-browserify": "~0.5.0", "gulp-bump": "~0.1.11", "gulp-eslint": "^1.1.1", "gulp-header": "^1.2.2", "gulp-istanbul": "^0.6.0", "gulp-mocha": "^2.0.0", "gulp-param": "~0.6.3", "gulp-rename": "~1.2.0", "gulp-replace": "~0.5.0", "gulp-rimraf": "^0.1.1", "gulp-uglify": "~1.0.1", "mocha": "^2.1.0", "underscore": "^1.7.0"}, "jshintConfig": {"node": true, "browser": true, "qunit": true, "globals": {"s": true}}, "dependencies": {}, "gitHead": "b22908f697817e8234b8f779622d155f16651c23", "_id": "underscore.string@3.2.3", "_shasum": "806992633665d5e5fcb4db1fb3a862eb68e9e6da", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.4.1", "_npmUser": {"name": "epeli", "email": "<EMAIL>"}, "dist": {"shasum": "806992633665d5e5fcb4db1fb3a862eb68e9e6da", "tarball": "https://registry.npmjs.org/underscore.string/-/underscore.string-3.2.3.tgz", "integrity": "sha512-4FNx1KnBckIW9Z7XvptVBzhWZvyuaB9NC3fdqdAp6GIRY4r6eDAENOZx2dzPrriQVZctkTYw2J7Vi1vjHS2E3A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFJx/YIkolzV97GI9Y9qN63fSIwSezC5xhymBAJybUoAAiEAyv4S4poCuTkOvTSbiVutHThLVsteYCXUiPW6nLcI59Y="}]}, "maintainers": [{"name": "edtsech", "email": "<EMAIL>"}, {"name": "rwz", "email": "<EMAIL>"}, {"name": "epeli", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}]}, "3.3.0": {"name": "underscore.string", "version": "3.3.0", "description": "String manipulation extensions for Underscore.js javascript library.", "homepage": "http://epeli.github.com/underscore.string/", "contributors": [{"name": "Esa<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://esa-matti.suuronen.org/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<https://github.com/rwz>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://koss.nocorp.me/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "https://github.com/kruckenb", "url": "<https://github.com/kruckenb>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://tchak.net>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://funkatron.com>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<https://github.com/stoeffel>"}], "keywords": ["underscore", "string"], "main": "./index.js", "directories": {"lib": "./"}, "engines": {"node": "*"}, "repository": {"type": "git", "url": "git+https://github.com/epeli/underscore.string.git"}, "bugs": {"url": "https://github.com/epeli/underscore.string/issues"}, "license": "MIT", "scripts": {"test": "npm run test:lint && npm run test:unit && npm run coverage", "test:unit": "mocha --ui=qunit tests", "test:lint": "eslint -c .eslintrc .", "coverage": "istanbul cover ./node_modules/mocha/bin/_mocha  -- --report=lcov --ui=qunit tests", "build": "npm run build:clean && npm run build:bundle && npm run build:min", "build:clean": "rm -rf dist", "build:bundle": "mkdir dist && browserify index.js -o dist/underscore.string.js -p browserify-header", "build:min": "uglifyjs dist/underscore.string.js -o dist/underscore.string.min.js --comments", "release": "npm test && npm run release:version && npm run build && npm run release:push && npm publish", "release:version": "node scripts/bump-version.js", "release:push": "node scripts/push-tags.js"}, "devDependencies": {"browserify": "^13.0.0", "browserify-header": "^0.9.2", "eslint": "^1.10.3", "istanbul": "^0.4.2", "mocha": "^2.1.0", "mocha-lcov-reporter": "^1.0.0", "replace": "^0.3.0", "uglifyjs": "^2.4.10", "underscore": "^1.7.0"}, "jshintConfig": {"node": true, "browser": true, "qunit": true, "globals": {"s": true}}, "dependencies": {"sprintf-js": "^1.0.3", "util-deprecate": "^1.0.2"}, "gitHead": "16a7978351cd11b27306f5ccaadfd6201f3d1da8", "_id": "underscore.string@3.3.0", "_shasum": "073c939e34b627fc42bfe2518bd455465eb525bd", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.2", "_npmUser": {"name": "s<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "073c939e34b627fc42bfe2518bd455465eb525bd", "tarball": "https://registry.npmjs.org/underscore.string/-/underscore.string-3.3.0.tgz", "integrity": "sha512-yUjER5/KRwpFlSYJXb9p3b4TbqycvxzWQp7GRtzCP6ptaqRxzAlXNGqAooZvhiQA5OYNgljb5nJFfr9T7lT/iA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDDDtcjEcSrtA9Li7RZ8nl41G25z88qmBGU0dVkzAvv+gIgSl/jSJF7kccd/r3BrnmwuuqDaZk0aKKywP35WPjZpic="}]}, "maintainers": [{"name": "edtsech", "email": "<EMAIL>"}, {"name": "rwz", "email": "<EMAIL>"}, {"name": "epeli", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-6-west.internal.npmjs.com", "tmp": "tmp/underscore.string-3.3.0.tgz_1456300825430_0.7481897710822523"}}, "3.3.2": {"name": "underscore.string", "version": "3.3.2", "description": "String manipulation extensions for Underscore.js javascript library.", "homepage": "http://epeli.github.com/underscore.string/", "contributors": [{"name": "Esa<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://esa-matti.suuronen.org/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<https://github.com/rwz>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://koss.nocorp.me/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "https://github.com/kruckenb", "url": "<https://github.com/kruckenb>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://tchak.net>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://funkatron.com>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<https://github.com/stoeffel>"}], "keywords": ["underscore", "string"], "main": "./index.js", "directories": {"lib": "./"}, "engines": {"node": "*"}, "repository": {"type": "git", "url": "git+https://github.com/epeli/underscore.string.git"}, "bugs": {"url": "https://github.com/epeli/underscore.string/issues"}, "license": "MIT", "scripts": {"test": "npm run test:lint && npm run test:unit && npm run coverage", "test:unit": "mocha --ui=qunit tests", "test:lint": "eslint -c .eslintrc .", "coverage": "istanbul cover ./node_modules/mocha/bin/_mocha  -- --report=lcov --ui=qunit tests", "build": "npm run build:clean && npm run build:bundle && npm run build:min", "build:clean": "rm -rf dist", "build:bundle": "mkdir dist && browserify index.js -o dist/underscore.string.js -p browserify-header", "build:min": "uglifyjs dist/underscore.string.js -o dist/underscore.string.min.js --comments", "release": "npm test && npm run release:version && npm run build && npm run release:push && npm publish", "release:version": "node scripts/bump-version.js", "release:push": "node scripts/push-tags.js"}, "devDependencies": {"browserify": "^13.0.0", "browserify-header": "^0.9.2", "eslint": "^1.10.3", "istanbul": "^0.4.2", "mocha": "^2.1.0", "mocha-lcov-reporter": "^1.0.0", "replace": "^0.3.0", "uglifyjs": "^2.4.10", "underscore": "^1.7.0"}, "jshintConfig": {"node": true, "browser": true, "qunit": true, "globals": {"s": true}}, "dependencies": {"sprintf-js": "^1.0.3", "util-deprecate": "^1.0.2"}, "gitHead": "d29a43ea82b1ba76bc6d915750b77feece520e10", "_id": "underscore.string@3.3.2", "_shasum": "61e2efc77b47af4c8814b9a89a34885eab9704f0", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.2", "_npmUser": {"name": "s<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "61e2efc77b47af4c8814b9a89a34885eab9704f0", "tarball": "https://registry.npmjs.org/underscore.string/-/underscore.string-3.3.2.tgz", "integrity": "sha512-5+YUS4nWqC+omxz5Rp41yKx9MzYv1JwUJBOc2XjweR3U/k8uacV2Yn97i1CF1YvhMXnfDQUyLOg66fJQf8yFtw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD8iBHmYnVXNaFfWIW8a94bViEaSVL9l0Cg2ygrKNhvwwIgfFWZWBCNVJogKF9iF2liQRnIt6+ByYmvG3XRba8N6Ag="}]}, "maintainers": [{"name": "edtsech", "email": "<EMAIL>"}, {"name": "rwz", "email": "<EMAIL>"}, {"name": "epeli", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-5-east.internal.npmjs.com", "tmp": "tmp/underscore.string-3.3.2.tgz_1456301282979_0.8603144416119903"}}, "3.3.3": {"name": "underscore.string", "version": "3.3.3", "description": "String manipulation extensions for Underscore.js javascript library.", "homepage": "http://epeli.github.com/underscore.string/", "contributors": [{"name": "Esa<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://esa-matti.suuronen.org/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<https://github.com/rwz>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://koss.nocorp.me/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "https://github.com/kruckenb", "url": "<https://github.com/kruckenb>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://tchak.net>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://funkatron.com>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<https://github.com/stoeffel>"}], "keywords": ["underscore", "string"], "main": "./index.js", "directories": {"lib": "./"}, "engines": {"node": "*"}, "repository": {"type": "git", "url": "git+https://github.com/epeli/underscore.string.git"}, "bugs": {"url": "https://github.com/epeli/underscore.string/issues"}, "license": "MIT", "scripts": {"test": "npm run test:lint && npm run test:unit && npm run coverage", "test:unit": "mocha --ui=qunit tests", "test:lint": "eslint -c .eslintrc .", "coverage": "istanbul cover ./node_modules/mocha/bin/_mocha  -- --report=lcov --ui=qunit tests", "build": "npm run build:clean && npm run build:bundle && npm run build:min", "build:clean": "rm -rf dist", "build:bundle": "mkdir dist && browserify index.js -o dist/underscore.string.js -p browserify-header", "build:min": "uglifyjs dist/underscore.string.js -o dist/underscore.string.min.js --comments", "release": "npm test && npm run release:version && npm run build && npm run release:push", "release:version": "node scripts/bump-version.js", "release:push": "node scripts/push-tags.js"}, "devDependencies": {"browserify": "^13.0.0", "browserify-header": "^0.9.2", "eslint": "^1.10.3", "istanbul": "^0.4.2", "mocha": "^2.1.0", "mocha-lcov-reporter": "^1.0.0", "replace": "^0.3.0", "uglifyjs": "^2.4.10", "underscore": "^1.7.0"}, "jshintConfig": {"node": true, "browser": true, "qunit": true, "globals": {"s": true}}, "dependencies": {"sprintf-js": "^1.0.3", "util-deprecate": "^1.0.2"}, "gitHead": "47eef95bef16eed626763cc3dfb46444c01e0fac", "_id": "underscore.string@3.3.3", "_shasum": "1fc0d4544fa11c524ea2dc77422f780d162f01f5", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.2", "_npmUser": {"name": "s<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "1fc0d4544fa11c524ea2dc77422f780d162f01f5", "tarball": "https://registry.npmjs.org/underscore.string/-/underscore.string-3.3.3.tgz", "integrity": "sha512-Pty9QvBANDvWFdx81rHGrIVjUAq18TVhg4ycNrOuhvGVMGrQTiPT/mUQApmKM6y6WFjgycOkHSYqxN0ihgOTeQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDH0RfRV5vUN3l5D97yc1ipIj6gUyAbcxaqrRJbfg3qeQIhAM5JS6hiVsYptK/uL6G4j20S50cJxtN8trt7u62KlIry"}]}, "maintainers": [{"name": "edtsech", "email": "<EMAIL>"}, {"name": "rwz", "email": "<EMAIL>"}, {"name": "epeli", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-9-west.internal.npmjs.com", "tmp": "tmp/underscore.string-3.3.3.tgz_1456302231669_0.6170737454667687"}}, "3.3.4": {"name": "underscore.string", "version": "3.3.4", "description": "String manipulation extensions for Underscore.js javascript library.", "homepage": "http://epeli.github.com/underscore.string/", "contributors": [{"name": "Esa<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://esa-matti.suuronen.org/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<https://github.com/rwz>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://koss.nocorp.me/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "https://github.com/kruckenb", "url": "<https://github.com/kruckenb>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://tchak.net>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://funkatron.com>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<https://github.com/stoeffel>"}], "keywords": ["underscore", "string"], "main": "./index.js", "directories": {"lib": "./"}, "engines": {"node": "*"}, "repository": {"type": "git", "url": "git+https://github.com/epeli/underscore.string.git"}, "bugs": {"url": "https://github.com/epeli/underscore.string/issues"}, "license": "MIT", "scripts": {"test": "npm run test:lint && npm run test:unit && npm run coverage", "test:unit": "mocha --ui=qunit tests", "test:lint": "eslint -c .eslintrc .", "coverage": "istanbul cover ./node_modules/mocha/bin/_mocha  -- --report=lcov --ui=qunit tests", "build": "npm run build:clean && npm run build:bundle && npm run build:min", "build:clean": "rm -rf dist", "build:bundle": "mkdir dist && browserify index.js -o dist/underscore.string.js -p browserify-header -s s", "build:min": "uglifyjs dist/underscore.string.js -o dist/underscore.string.min.js --comments", "release": "npm test && npm run release:version && npm run build && npm run release:push", "release:version": "node scripts/bump-version.js", "release:push": "node scripts/push-tags.js"}, "devDependencies": {"browserify": "^13.0.0", "browserify-header": "^0.9.2", "eslint": "^1.10.3", "istanbul": "^0.4.2", "mocha": "^2.1.0", "mocha-lcov-reporter": "^1.0.0", "replace": "^0.3.0", "uglifyjs": "^2.4.10", "underscore": "^1.7.0"}, "jshintConfig": {"node": true, "browser": true, "qunit": true, "globals": {"s": true}}, "dependencies": {"sprintf-js": "^1.0.3", "util-deprecate": "^1.0.2"}, "gitHead": "2f78f0d6e36d553484a1bf5fe5ed1998f013dea5", "_id": "underscore.string@3.3.4", "_shasum": "2c2a3f9f83e64762fdc45e6ceac65142864213db", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.2", "_npmUser": {"name": "s<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "2c2a3f9f83e64762fdc45e6ceac65142864213db", "tarball": "https://registry.npmjs.org/underscore.string/-/underscore.string-3.3.4.tgz", "integrity": "sha512-xQOONaLk0lHHUEzc1m+d6IZX6rSsbsyndZx3+ATd0PmxH8bXv16G92UWocOS4NJMd7aeLPZM7F1ktIi/uzjDMg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD0WCONCIyznOZprnMsOWM/iuYmSUw/wKQize+FiCWBuAIhAMpVP8HM6nYVLcpEHYBBM3p7ezgrlmG1+WQUuT5bRjl0"}]}, "maintainers": [{"name": "edtsech", "email": "<EMAIL>"}, {"name": "rwz", "email": "<EMAIL>"}, {"name": "epeli", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-6-west.internal.npmjs.com", "tmp": "tmp/underscore.string-3.3.4.tgz_1456303259101_0.7602346076164395"}}, "3.3.5": {"name": "underscore.string", "version": "3.3.5", "description": "String manipulation extensions for Underscore.js javascript library.", "homepage": "http://epeli.github.com/underscore.string/", "contributors": [{"name": "Esa<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://esa-matti.suuronen.org/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<https://github.com/rwz>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://koss.nocorp.me/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "https://github.com/kruckenb", "url": "<https://github.com/kruckenb>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://tchak.net>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://funkatron.com>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<https://github.com/stoeffel>"}], "keywords": ["underscore", "string"], "main": "./index.js", "directories": {"lib": "./"}, "engines": {"node": "*"}, "repository": {"type": "git", "url": "git+https://github.com/epeli/underscore.string.git"}, "bugs": {"url": "https://github.com/epeli/underscore.string/issues"}, "license": "MIT", "scripts": {"test": "npm run test:lint && npm run test:unit && npm run coverage", "test:unit": "mocha --ui=qunit tests", "test:lint": "eslint -c .eslintrc .", "coverage": "istanbul cover ./node_modules/mocha/bin/_mocha  -- --report=lcov --ui=qunit tests", "build": "npm run build:clean && npm run build:bundle && npm run build:min", "build:clean": "rm -rf dist", "build:bundle": "mkdir dist && browserify index.js -o dist/underscore.string.js -p browserify-header -s s", "build:min": "uglifyjs dist/underscore.string.js -o dist/underscore.string.min.js --comments", "release": "npm test && npm run release:version && npm run build && npm run release:push", "release:version": "node scripts/bump-version.js", "release:push": "node scripts/push-tags.js"}, "devDependencies": {"browserify": "^13.0.0", "browserify-header": "^0.9.2", "eslint": "^1.10.3", "istanbul": "^0.4.2", "mocha": "^2.1.0", "mocha-lcov-reporter": "^1.0.0", "replace": "^0.3.0", "uglifyjs": "^2.4.10", "underscore": "^1.7.0"}, "jshintConfig": {"node": true, "browser": true, "qunit": true, "globals": {"s": true}}, "dependencies": {"sprintf-js": "^1.0.3", "util-deprecate": "^1.0.2"}, "gitHead": "fde7ed699c26d294ed466624f9d843240cd14e94", "_id": "underscore.string@3.3.5", "_npmVersion": "6.4.1", "_nodeVersion": "8.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-g+dpmgn+XBneLmXXo+sGlW5xQEt4ErkS3mgeN2GFbremYeMBSJKr9Wf2KJplQVaiPY/f7FN6atosWYNm9ovrYg==", "shasum": "fc2ad255b8bd309e239cbc5816fd23a9b7ea4023", "tarball": "https://registry.npmjs.org/underscore.string/-/underscore.string-3.3.5.tgz", "fileCount": 85, "unpackedSize": 137917, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbtlosCRA9TVsSAnZWagAAB8wP/jkRpgSNTkZZ3EMiuIhf\n1gHMdhvX18RhdPaBD0qO2+go2vDsAG9H/mi7uUrvmpRn4l3dfy8doURs6Nt3\nLzWrgAaTLYy5JfUSUErSHEDs0ecVEL3Chyyu4uAZFEUeZhuzZAkKj7KtbhNG\nAbXW3AJLnEgEUXHq2b4NrA0dH0bk0ge0/Mv2NuVv8oFEv8EA/q4G7xlWRmSx\nyTk7eygpixgMwVK2wT0RE49ju+nV6jYSZVP7y7ReMToCi6N4ZFFCFjPlwMCT\nvbAEWzGq3HuJUt/I+J0ZX/pt/UP1392AQPQ8/1oqEQbOPJWiOwaKZNFhNcnY\nF3d7FVqd2SJWcANYUUZ6EVop9GWOINfQbRgLtjaIAS5nbexhs180EJHIf1/6\n3CS9h6PplwthrSWMI3l5BLsFZLBi6PljAGhQl7prt79i/v8xmu0CMNV0+FbP\nNRBmJR40ZbN8a5ooBdpJ5kYwHXlnc7TY7ZD8PenGcl6sy8uiZAziaEGcaXvp\nj4ElbQm9RcXa2+7eajn+llRIRdE1cjlDjWPvw16DY66xe8XNv6w36xxtGrOY\n2oFsVJTERLscyWp2huTyuqGDXG+h1b3M1M3qy25rynOmv1jg7PSOsk7cOWYi\ncEcCN1KSbZx9bTRN1s5wMrZaLk7cU1wlnIOUzIaiAiWPjpB9IsjkNiRv1Kly\n+uEI\r\n=p8qX\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFwUTO6PYQRTdVrx9dPkD76F/BcQmUZT/3jeri91V1HeAiEAqZmLHTcrklwcif0TndP0hHgS7ISZ+/7ft0yThuM7Mw0="}]}, "maintainers": [{"email": "<EMAIL>", "name": "edtsech"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "rwz"}, {"email": "<EMAIL>", "name": "s<PERSON><PERSON><PERSON><PERSON>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/underscore.string_3.3.5_1538677291652_0.4582088298721445"}, "_hasShrinkwrap": false}, "3.3.6": {"name": "underscore.string", "version": "3.3.6", "description": "String manipulation extensions for Underscore.js javascript library.", "homepage": "http://epeli.github.com/underscore.string/", "contributors": [{"name": "Esa<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://esa-matti.suuronen.org/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<https://github.com/rwz>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://koss.nocorp.me/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "https://github.com/kruckenb", "url": "<https://github.com/kruckenb>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://tchak.net>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://funkatron.com>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<https://github.com/stoeffel>"}], "keywords": ["underscore", "string"], "main": "./index.js", "directories": {"lib": "./"}, "engines": {"node": "*"}, "repository": {"type": "git", "url": "git+https://github.com/epeli/underscore.string.git"}, "bugs": {"url": "https://github.com/epeli/underscore.string/issues"}, "license": "MIT", "scripts": {"test": "npm run test:lint && npm run test:unit && npm run coverage", "test:unit": "mocha --ui=qunit tests", "test:lint": "eslint -c .eslintrc .", "coverage": "istanbul cover ./node_modules/mocha/bin/_mocha  -- --report=lcov --ui=qunit tests", "build": "npm run build:clean && npm run build:bundle && npm run build:min", "build:clean": "rm -rf dist", "build:bundle": "mkdir dist && browserify index.js -o dist/underscore.string.js -p browserify-header -s s", "build:min": "uglifyjs dist/underscore.string.js -o dist/underscore.string.min.js --comments", "release": "npm test && npm run release:version && npm run build && npm run release:push", "release:version": "node scripts/bump-version.js", "release:push": "node scripts/push-tags.js"}, "devDependencies": {"browserify": "^16.2.3", "browserify-header": "^0.9.4", "eslint": "^5.6.1", "istanbul": "^0.4.5", "mocha": "^5.2.0", "mocha-lcov-reporter": "^1.3.0", "replace": "^1.0.0", "uglifyjs": "^2.4.11", "underscore": "^1.9.1"}, "jshintConfig": {"node": true, "browser": true, "qunit": true, "globals": {"s": true}}, "dependencies": {"sprintf-js": "^1.1.1", "util-deprecate": "^1.0.2"}, "volta": {"node": "16.13.2"}, "gitHead": "6a65c389135c432f77df27f606f8457849f662f2", "_id": "underscore.string@3.3.6", "_nodeVersion": "16.13.2", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-VoC83HWXmCrF6rgkyxS9GHv8W9Q5nhMKho+OadDJGzL2oDYbYEppBaCMH6pFlwLeqj2QS+hhkw2kpXkSdD1JxQ==", "shasum": "ad8cf23d7423cb3b53b898476117588f4e2f9159", "tarball": "https://registry.npmjs.org/underscore.string/-/underscore.string-3.3.6.tgz", "fileCount": 85, "unpackedSize": 139454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7RkDCRA9TVsSAnZWagAAJuAQAJoU2XR4UIBTsugh4oiu\n8N2wV3gHpyThQS+Ej3CG5pyeI3vqWh4ShBApLPlUcHjhN/BbYKT+Y5zFQJ4g\nAqqS7eSC0EmrBmDVQYsOEY/cCJ82HztmK5Et217lRmDf8Spm/bFlTAgjjYKZ\nCwPDbQ8MFkpfPCCXWj6waW6kQPSbwbZqosTuZfQiCpNy/eKGMnVlwBHl28E1\nr6aT7StxGLCUoRq40b/gKTRIPp4gEp9/xwwzOH7sCgRRueS3YdUNB/ey1h2a\nOeACgGkhoMdqGOhDZDGOXM4uz1rKFY4A25V95bIIOIO8unruioBNWjL4GX9S\nzI04SG1LtNj+sE6Y8q2iLJNlhkRnlgQuL/nfjfUYmSJq4q/0AK+ZLnPE8+CU\n+nmcOjPv4nue8CbPJq5/nqFVzAmsb1HTZnDLVCcGSuBXS9DI69YEKEc3alGK\nip0iHxMT0TN+z9qcEPowpbeZHo5mH86l9hM5o+2tJu7z0+4fj2af/mIx9TTi\nlnhFOW9rG+bpCENwPU1Y5p0q0y8AjIjZAb/zec3NH+/dn4COb2NjAbrjnnTm\negzywQ6Z83q49uq6TNmpLV67Dz1zOUyeRXFUbqYkJl7c6fwcjXTv4kydnTlp\ntFMl1L5/ftI20RuXlMyRiazgOdGN4x2stKoDaVnrZS2phNBnE4oYEgfem2TB\n3OCM\r\n=13An\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCLXuO4G6pwz68gR3LxfB37GcJzFvpvb7yBi6uBqsH52AIgUius2buMwf5q/Uqnc5Q6q4oGzwYrAwm8AlW9aKlsxD8="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rwz", "email": "<EMAIL>"}, {"name": "edtsech", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/underscore.string_3.3.6_1642928387011_0.16212082152142315"}, "_hasShrinkwrap": false}}, "readme": "<span class=\"github-only\">\n\nThe stable release documentation can be found here https://epeli.github.io/underscore.string/\n\n</span>\n\n# Underscore.string [![Build Status](https://secure.travis-ci.org/epeli/underscore.string.png?branch=master)](http://travis-ci.org/epeli/underscore.string) #\n\nJavascript lacks complete string manipulation operations.\nThis is an attempt to fill that gap. List of build-in methods can be found\nfor example from [Dive Into JavaScript][d].\nOriginally started as an Underscore.js extension but is a full standalone\nlibrary nowadays.\n\nUpgrading from 2.x to 3.x? Please read the [changelog][c].\n\n[c]: https://github.com/epeli/underscore.string/blob/master/CHANGELOG.markdown#300\n\n## Usage\n\n### For Node.js, Browserify and Webpack\n\nInstall from npm\n\n    npm install underscore.string\n\nRequire individual functions\n\n```javascript\nvar slugify = require(\"underscore.string/slugify\");\n\nslugify(\"Hello world!\");\n// => hello-world\n```\n\nor load the full library to enable chaining\n\n```javascript\nvar s = require(\"underscore.string\");\n\ns(\"   epeli  \").trim().capitalize().value();\n// => \"Epeli\"\n```\n\nbut especially when using with [Browserify][] the individual function approach\nis recommended because using it you only add those functions to your bundle you\nuse.\n\n[Browserify]: http://browserify.org/\n\n### In Meteor\n\nFrom your [Meteor][] project folder\n\n```shell\n    meteor add underscorestring:underscore.string\n```\n\nand you'll be able to access the library with the ***s*** global from both the server and the client.\n\n```javascript\ns.slugify(\"Hello world!\");\n// => hello-world\n\ns(\"   epeli  \").trim().capitalize().value();\n// => \"Epeli\"\n```\n\n[Meteor]: http://www.meteor.com/\n\n### Others\n\nThe `dist/underscore.string.js` file is an [UMD][] build. You can load it using\nan AMD loader such as [RequireJS][] or just stick it to a web page and access\nthe library from the ***s*** global.\n\n[UMD]: https://github.com/umdjs/umd\n[RequireJS]: http://requirejs.org/\n\n### Underscore.js/Lo-Dash integration\n\nIt is still possible use as Underscore.js/Lo-Dash extension\n\n```javascript\n_.mixin(s.exports());\n```\nBut it's not recommended since `include`, `contains`, `reverse` and `join`\nare dropped because they collide with the functions already defined by Underscore.js.\n\n### Lo-Dash-FP/Ramda integration\n\nIf you want to use underscore.string with [ramdajs](http://ramdajs.com/) or [Lo-Dash-FP](https://github.com/lodash/lodash-fp) you can use [underscore.string.fp](https://github.com/stoeffel/underscore.string.fp).\n\n    npm install underscore.string.fp\n\n```javascript\nvar S = require('underscore.string.fp');\nvar filter = require('lodash-fp').filter;\nvar filter = require('ramda').filter;\n\nfilter(S.startsWith('.'), [\n  '.vimrc',\n  'foo.md',\n  '.zshrc'\n]);\n// => ['.vimrc', '.zshrc']\n```\n\n## Download\n  \n  * [Development version](https://npmcdn.com/underscore.string/dist/underscore.string.js) *Uncompressed with Comments*\n  * [Production version](https://npmcdn.com/underscore.string/dist/underscore.string.min.js) *Minified*\n\n## API\n\n### Individual functions\n\n#### numberFormat(number, [ decimals=0, decimalSeparator='.', orderSeparator=',']) => string\n\nFormats the numbers.\n\n```javascript\nnumberFormat(1000, 2);\n// => \"1,000.00\"\n\nnumberFormat(123456789.123, 5, \".\", \",\");\n// => \"123,456,789.12300\"\n```\n\n\n#### levenshtein(string1, string2) => number\n\nCalculates [Levenshtein distance][ld] between two strings.\n[ld]: http://en.wikipedia.org/wiki/Levenshtein_distance\n\n```javascript\nlevenshtein(\"kitten\", \"kittah\");\n// => 2\n```\n\n#### capitalize(string, [lowercaseRest=false]) => string\n\nConverts first letter of the string to uppercase. If `true` is passed as second argument the rest\nof the string will be converted to lower case.\n\n```javascript\ncapitalize(\"foo Bar\");\n// => \"Foo Bar\"\n\ncapitalize(\"FOO Bar\", true);\n// => \"Foo bar\"\n```\n\n#### decapitalize(string) => string\n\nConverts first letter of the string to lowercase.\n\n```javascript\ndecapitalize(\"Foo Bar\");\n// => \"foo Bar\"\n```\n\n#### chop(string, step) => array\n\n```javascript\nchop(\"whitespace\", 3);\n// => [\"whi\", \"tes\", \"pac\", \"e\"]\n```\n\n#### clean(string) => string\n\nTrim and replace multiple spaces with a single space.\n\n```javascript\nclean(\" foo    bar   \");\n// => \"foo bar\"\n```\n\n#### cleanDiacritics(string) => string\n\nReplace [diacritic][dc] characters with closest ASCII equivalents. Check the\n[source][s] for supported characters. [Pull requests][p] welcome for missing\ncharacters!\n\n[dc]: https://en.wikipedia.org/wiki/Diacritic\n[s]: https://github.com/epeli/underscore.string/blob/master/cleanDiacritics.js\n[p]: https://github.com/epeli/underscore.string/blob/master/CONTRIBUTING.markdown\n\n```javascript\ncleanDiacritics(\"ääkkönen\");\n// => \"aakkonen\"\n```\n\n#### chars(string) => array\n\n```javascript\nchars(\"Hello\");\n// => [\"H\", \"e\", \"l\", \"l\", \"o\"]\n```\n\n#### swapCase(string) => string\n\nReturns a copy of the string in which all the case-based characters have had their case swapped.\n\n```javascript\nswapCase(\"hELLO\");\n// => \"Hello\"\n```\n\n#### include(string, substring) => boolean\n\nTests if string contains a substring.\n\n```javascript\ninclude(\"foobar\", \"ob\");\n// => true\n```\n\n#### count(string, substring) => number\n\nReturns number of occurrences of substring in string.\n\n```javascript\ncount(\"Hello world\", \"l\");\n// => 3\n```\n\n#### escapeHTML(string) => string\n\nConverts HTML special characters to their entity equivalents.\nThis function supports cent, yen, euro, pound, lt, gt, copy, reg, quote, amp, apos.\n\n```javascript\nescapeHTML(\"<div>Blah blah blah</div>\");\n// => \"&lt;div&gt;Blah blah blah&lt;/div&gt;\"\n```\n\n#### unescapeHTML(string) => string\n\nConverts entity characters to HTML equivalents.\nThis function supports cent, yen, euro, pound, lt, gt, copy, reg, quote, amp, apos, nbsp.\n\n```javascript\nunescapeHTML(\"&lt;div&gt;Blah&nbsp;blah blah&lt;/div&gt;\");\n// => \"<div>Blah blah blah</div>\"\n```\n\n#### insert(string, index, substring) => string\n\n```javascript\ninsert(\"Hellworld\", 4, \"o \");\n// => \"Hello world\"\n```\n\n#### replaceAll(string, find, replace, [ignorecase=false]) => string\n\n```javascript\nreplaceAll(\"foo\", \"o\", \"a\");\n// => \"faa\"\n```\n\n#### isBlank(string) => boolean\n\n```javascript\nisBlank(\"\"); // => true\nisBlank(\"\\n\"); // => true\nisBlank(\" \"); // => true\nisBlank(\"a\"); // => false\n```\n\n#### join(separator, ...strings) => string\n\nJoins strings together with given separator\n\n```javascript\njoin(\" \", \"foo\", \"bar\");\n// => \"foo bar\"\n```\n\n#### lines(str) => array\n\nSplit lines to an array\n\n```javascript\nlines(\"Hello\\nWorld\");\n// => [\"Hello\", \"World\"]\n```\n\n#### wrap(str, options) => string\n\nSplits a line `str` (default '') into several lines of size `options.width` (default 75) using a `options.seperator` (default '\\n'). If `options.trailingSpaces` is true, make each line at least `width` long using trailing spaces. If `options.cut` is true, create new lines in the middle of words. If `options.preserveSpaces` is true, preserve the space that should be there at the end of a line (only works if options.cut is false).\n\n```javascript\nwrap(\"Hello World\", { width:5 })\n// => \"Hello\\nWorld\"\n\nwrap(\"Hello World\", { width:6, seperator:'.', trailingSpaces: true })\n// => \"Hello .World \"\n\nwrap(\"Hello World\", { width:5, seperator:'.', cut:true, trailingSpaces: true })\n// => \"Hello. Worl.d    \"\n\nwrap(\"Hello World\", { width:5, seperator:'.', preserveSpaces: true })\n// => \"Hello .World\"\n\n```\n\n#### dedent(str, [pattern]) => string\n\nDedent unnecessary indentation or dedent by a pattern.\n\nCredits go to @sindresorhus.\nThis implementation is similar to https://github.com/sindresorhus/strip-indent\n\n```javascript\ndedent(\"  Hello\\n    World\");\n// => \"Hello\\n  World\"\n\ndedent(\"\\t\\tHello\\n\\t\\t\\t\\tWorld\");\n// => \"Hello\\n\\t\\tWorld\"\n\ndedent(\"    Hello\\n    World\", \"  \"); // Dedent by 2 spaces\n// => \"  Hello\\n  World\"\n```\n\n#### reverse(string) => string\n\nReturn reversed string:\n\n```javascript\nreverse(\"foobar\");\n// => \"raboof\"\n```\n\n#### splice(string, index, howmany, substring) => string\n\nLike an array splice.\n\n```javascript\nsplice(\"https://<EMAIL>/edtsech/underscore.strings\", 30, 7, \"epeli\");\n// => \"https://<EMAIL>/epeli/underscore.strings\"\n```\n\n#### startsWith(string, starts, [position]) => boolean\n\nThis method checks whether the string begins with `starts` at `position` (default: 0).\n\n```javascript\nstartsWith(\"image.gif\", \"image\");\n// => true\n\nstartsWith(\".vimrc\", \"vim\", 1);\n// => true\n```\n\n#### endsWith(string, ends, [position]) => boolean\n\nThis method checks whether the string ends with `ends` at `position` (default: string.length).\n\n```javascript\nendsWith(\"image.gif\", \"gif\");\n// => true\n\nendsWith(\"image.old.gif\", \"old\", 9);\n// => true\n```\n\n#### pred(string) => string\n\nReturns the predecessor to str.\n\n```javascript\npred(\"b\");\n// => \"a\"\n\npred(\"B\");\n// => \"A\"\n```\n\n#### succ(string) => string\n\nReturns the successor to str.\n\n```javascript\nsucc(\"a\");\n// => \"b\"\n\nsucc(\"A\");\n// => \"B\"\n```\n\n\n#### titleize(string) => string\n\n```javascript\ntitleize(\"my name is epeli\");\n// => \"My Name Is Epeli\"\n```\n\n#### camelize(string, [decapitalize=false]) => string\n\nConverts underscored or dasherized string to a camelized one. Begins with\na lower case letter unless it starts with an underscore, dash or an upper case letter.\n\n```javascript\ncamelize(\"moz-transform\");\n// => \"mozTransform\"\n\ncamelize(\"-moz-transform\");\n// => \"MozTransform\"\n\ncamelize(\"_moz_transform\");\n// => \"MozTransform\"\n\ncamelize(\"Moz-transform\");\n// => \"MozTransform\"\n\ncamelize(\"-moz-transform\", true);\n// => \"mozTransform\"\n```\n\n#### classify(string) => string\n\nConverts string to camelized class name. First letter is always upper case\n\n```javascript\nclassify(\"some_class_name\");\n// => \"SomeClassName\"\n```\n\n#### underscored(string) => string\n\nConverts a camelized or dasherized string into an underscored one\n\n```javascript\nunderscored(\"MozTransform\");\n// => \"moz_transform\"\n```\n\n#### dasherize(string) => string\n\nConverts a underscored or camelized string into an dasherized one\n\n```javascript\ndasherize(\"MozTransform\");\n// => \"-moz-transform\"\n```\n\n#### humanize(string) => string\n\nConverts an underscored, camelized, or dasherized string into a humanized one.\nAlso removes beginning and ending whitespace, and removes the postfix '_id'.\n\n```javascript\nhumanize(\"  capitalize dash-CamelCase_underscore trim  \");\n// => \"Capitalize dash camel case underscore trim\"\n```\n\n#### trim(string, [characters]) => string\n\nTrims defined characters from begining and ending of the string.\nDefaults to whitespace characters.\n\n```javascript\ntrim(\"  foobar   \");\n// => \"foobar\"\n\ntrim(\"_-foobar-_\", \"_-\");\n// => \"foobar\"\n```\n\n\n#### ltrim(string, [characters]) => string\n\nLeft trim. Similar to trim, but only for left side.\n\n#### rtrim(string, [characters]) => string\n\nRight trim. Similar to trim, but only for right side.\n\n#### truncate(string, length, [truncateString = '...']) => string\n\n```javascript\ntruncate(\"Hello world\", 5);\n// => \"Hello...\"\n\ntruncate(\"Hello\", 10);\n// => \"Hello\"\n```\n\n#### prune(string, length, pruneString) => string\n\nElegant version of truncate.  Makes sure the pruned string does not exceed the\noriginal length.  Avoid half-chopped words when truncating.\n\n```javascript\nprune(\"Hello, world\", 5);\n// => \"Hello...\"\n\nprune(\"Hello, world\", 8);\n// => \"Hello...\"\n\nprune(\"Hello, world\", 5, \" (read a lot more)\");\n// => \"Hello, world\" (as adding \"(read a lot more)\" would be longer than the original string)\n\nprune(\"Hello, cruel world\", 15);\n// => \"Hello, cruel...\"\n\nprune(\"Hello\", 10);\n// => \"Hello\"\n```\n\n#### words(str, delimiter=/\\s+/) => array\n\nSplit string by delimiter (String or RegExp), /\\s+/ by default.\n\n```javascript\nwords(\"   I   love   you   \");\n// => [\"I\", \"love\", \"you\"]\n\nwords(\"I_love_you\", \"_\");\n// => [\"I\", \"love\", \"you\"]\n\nwords(\"I-love-you\", /-/);\n// => [\"I\", \"love\", \"you\"]\n\nwords(\"   \")\n// => []\n```\n\n#### sprintf(string format, ...arguments) => string\n\nC like string formatting. Makes use of the [sprintf-js](https://npmjs.org/package/sprintf-js) package.\n\n**This function will be removed in the next major release, use the [sprintf-js](https://npmjs.org/package/sprintf-js) package instead.**\n\n```javascript\nsprintf(\"%.1f\", 1.17);\n// => \"1.2\"\n```\n\n#### pad(str, length, [padStr, type]) => string\n\npads the `str` with characters until the total string length is equal to the passed `length` parameter. By default, pads on the **left** with the space char (`\" \"`). `padStr` is truncated to a single character if necessary.\n\n```javascript\npad(\"1\", 8);\n// => \"       1\"\n\npad(\"1\", 8, \"0\");\n// => \"00000001\"\n\npad(\"1\", 8, \"0\", \"right\");\n// => \"10000000\"\n\npad(\"1\", 8, \"0\", \"both\");\n// => \"00001000\"\n\npad(\"1\", 8, \"bleepblorp\", \"both\");\n// => \"bbbb1bbb\"\n```\n\n#### lpad(str, length, [padStr]) => string\n\nleft-pad a string. Alias for `pad(str, length, padStr, \"left\")`\n\n```javascript\nlpad(\"1\", 8, \"0\");\n// => \"00000001\"\n```\n\n#### rpad(str, length, [padStr]) => string\n\nright-pad a string. Alias for `pad(str, length, padStr, \"right\")`\n\n```javascript\nrpad(\"1\", 8, \"0\");\n// => \"10000000\"\n```\n\n#### lrpad(str, length, [padStr]) => string\n\nleft/right-pad a string. Alias for `pad(str, length, padStr, \"both\")`\n\n```javascript\nlrpad(\"1\", 8, '0');\n// => \"00001000\"\n```\n\n\n#### toNumber(string, [decimals]) => number\n\nParse string to number. Returns NaN if string can't be parsed to number.\n\n```javascript\ntoNumber(\"2.556\");\n// => 3\n\ntoNumber(\"2.556\", 1);\n// => 2.6\n\ntoNumber(\"999.999\", -1);\n// => 990\n```\n\n#### strRight(string, pattern) => string\n\nSearches a string from left to right for a pattern and returns a substring consisting of the characters in the string that are to the right of the pattern or all string if no match found.\n\n```javascript\nstrRight(\"This_is_a_test_string\", \"_\");\n// => \"is_a_test_string\"\n```\n\n#### strRightBack(string, pattern) => string\n\nSearches a string from right to left for a pattern and returns a substring consisting of the characters in the string that are to the right of the pattern or all string if no match found.\n\n```javascript\nstrRightBack(\"This_is_a_test_string\", \"_\");\n// => \"string\"\n```\n\n#### strLeft(string, pattern) => string\n\nSearches a string from left to right for a pattern and returns a substring consisting of the characters in the string that are to the left of the pattern or all string if no match found.\n\n```javascript\nstrLeft(\"This_is_a_test_string\", \"_\");\n// => \"This\";\n```\n\n#### strLeftBack(string, pattern) => string\n\nSearches a string from right to left for a pattern and returns a substring consisting of the characters in the string that are to the left of the pattern or all string if no match found.\n\n```javascript\nstrLeftBack(\"This_is_a_test_string\", \"_\");\n// => \"This_is_a_test\";\n```\n\n#### stripTags(string) => string\n\nRemoves all html tags from string.\n\n```javascript\nstripTags(\"a <a href=\\\"#\\\">link</a>\");\n// => \"a link\"\n\nstripTags(\"a <a href=\\\"#\\\">link</a><script>alert(\\\"hello world!\\\")</script>\");\n// => \"a linkalert(\"hello world!\")\"\n```\n\n#### toSentence(array, [delimiter, lastDelimiter]) => string\n\nJoin an array into a human readable sentence.\n\n```javascript\ntoSentence([\"jQuery\", \"Mootools\", \"Prototype\"]);\n// => \"jQuery, Mootools and Prototype\";\n\ntoSentence([\"jQuery\", \"Mootools\", \"Prototype\"], \", \", \" unt \");\n// => \"jQuery, Mootools unt Prototype\";\n```\n\n#### toSentenceSerial(array, [delimiter, lastDelimiter]) => string\n\nThe same as `toSentence`, but adjusts delimeters to use [Serial comma](http://en.wikipedia.org/wiki/Serial_comma).\n\n```javascript\ntoSentenceSerial([\"jQuery\", \"Mootools\"]);\n// => \"jQuery and Mootools\"\n\ntoSentenceSerial([\"jQuery\", \"Mootools\", \"Prototype\"]);\n// => \"jQuery, Mootools, and Prototype\"\n\ntoSentenceSerial([\"jQuery\", \"Mootools\", \"Prototype\"], \", \", \" unt \");\n// => \"jQuery, Mootools, unt Prototype\"\n```\n\n#### repeat(string, count, [separator]) => string\n\nRepeats a string count times.\n\n```javascript\nrepeat(\"foo\", 3);\n// => \"foofoofoo\"\n\nrepeat(\"foo\", 3, \"bar\");\n// => \"foobarfoobarfoo\"\n```\n\n#### surround(string, wrap) => string\n\nSurround a string with another string.\n\n```javascript\nsurround(\"foo\", \"ab\");\n// => \"abfooab\"\n```\n\n#### quote(string, quoteChar) or q(string, quoteChar) => string\n\nQuotes a string. `quoteChar` defaults to `\"`.\n\n```javascript\nquote(\"foo\", '\"');\n// => '\"foo\"';\n```\n#### unquote(string, quoteChar) => string\n\nUnquotes a string. `quoteChar` defaults to `\"`.\n\n```javascript\nunquote('\"foo\"');\n// => \"foo\"\n\nunquote(\"'foo'\", \"'\");\n// => \"foo\"\n```\n\n\n#### slugify(string) => string\n\nTransform text into an ascii slug which can be used in safely in URLs. Replaces whitespaces, accentuated, and special characters with a dash. Limited set of non-ascii characters are transformed to similar versions in the ascii character set such as `ä` to `a`.\n\n```javascript\nslugify(\"Un éléphant à l\\'orée du bois\");\n// => \"un-elephant-a-l-oree-du-bois\"\n```\n\n***Caution: this function is charset dependent***\n\n#### naturalCmp(string1, string2) => number\n\nNaturally sort strings like humans would do. None numbers are compared by their [ASCII values](http://www.asciitable.com/). Note: this means \"a\" > \"A\". Use `.toLowerCase` if this isn't to be desired.\n\nJust past it to `Array#sort`.\n\n```javascript\n[\"foo20\", \"foo5\"].sort(naturalCmp);\n// => [\"foo5\", \"foo20\"]\n```\n\n#### toBoolean(string) => boolean\n\nTurn strings that can be commonly considered as booleas to real booleans. Such as \"true\", \"false\", \"1\" and \"0\". This function is case insensitive.\n\n```javascript\ntoBoolean(\"true\");\n// => true\n\ntoBoolean(\"FALSE\");\n// => false\n\ntoBoolean(\"random\");\n// => undefined\n```\n\nIt can be customized by giving arrays of truth and falsy value matcher as parameters. Matchers can be also RegExp objects.\n\n```javascript\ntoBoolean(\"truthy\", [\"truthy\"], [\"falsy\"]);\n// => true\n\ntoBoolean(\"true only at start\", [/^true/]);\n// => true\n```\n\n#### map(string, function) => string\n\nCreates a new string with the results of calling a provided function on every character of the given string.\n\n```javascript\nmap(\"Hello world\", function(x) {\n  return x;\n});\n// => \"Hello world\"\n\nmap(12345, function(x) {\n  return x;\n});\n// => \"12345\"\n\nmap(\"Hello world\", function(x) {\n  if (x === 'o') x = 'O';\n  return x;\n});\n// => \"HellO wOrld\"\n```\n\n### Library functions\n\nIf you require the full library you can use chaining and aliases\n\n#### s(string) => chain\n\nStart a chain. Returns an immutable chain object with the string functions as\nmethods which return a new chain object instead of the plain string value.\n\nThe chain object includes also following native Javascript string methods:\n\n  - [toUpperCase](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/toUpperCase)\n  - [toLowerCase](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/toLowerCase)\n  - [split](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/split)\n  - [replace](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/replace)\n  - [slice](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/slice)\n  - [substring](https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Global_Objects/String/substring)\n  - [substr](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/substr)\n  - [concat](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/concat)\n\n#### chain.value()\n\nReturn the string value from the chain\n\n```javascript\ns(\"  foo  \").trim().capitalize().value();\n// => \"Foo\"\n```\n\nWhen calling a method which does not return a string the resulting value is\nimmediately returned\n\n```javascript\ns(\" foobar \").trim().startsWith(\"foo\");\n// => true\n```\n\n#### chain.tap(function) => chain\n\nTap into the chain with a custom function\n\n```javascript\ns(\"foo\").tap(function(value){\n  return value + \"bar\";\n}).value();\n// => \"foobar\"\n```\n\n\n#### Aliases\n\n```javascript\nstrip     = trim\nlstrip    = ltrim\nrstrip    = rtrim\ncenter    = lrpad\nrjust     = lpad\nljust     = rpad\ncontains  = include\nq         = quote\ntoBool    = toBoolean\ncamelcase = camelize\n```\n\n## Maintainers ##\n\nThis library is maintained by\n\n  - Esa-Matti Suuronen – ***[@epeli](https://github.com/epeli)***\n  - Christoph Hermann – ***[@stoeffel](https://github.com/stoeffel)***\n\n## Licence ##\n\nThe MIT License\n\nCopyright (c) 2011 Esa-<NAME_EMAIL>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n\n\n[d]: http://www.diveintojavascript.com/core-javascript-reference/the-string-object\n", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rwz", "email": "<EMAIL>"}, {"name": "edtsech", "email": "<EMAIL>"}, {"name": "s<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-02-04T10:39:18.570Z", "created": "2011-11-10T16:30:05.477Z", "0.9.2": "2011-11-10T16:30:07.164Z", "1.0.0": "2011-11-10T16:31:05.009Z", "1.1.3": "2011-11-10T16:34:19.092Z", "1.1.4": "2011-11-10T16:34:53.263Z", "1.1.5": "2011-11-10T16:35:32.410Z", "1.1.6": "2011-11-10T16:37:00.662Z", "1.2.0": "2011-11-10T16:37:54.359Z", "2.0.0": "2011-12-02T21:49:56.435Z", "2.1.0": "2012-03-29T08:57:45.551Z", "2.1.1": "2012-04-06T03:57:55.039Z", "2.2.0": "2012-04-14T15:45:30.830Z", "2.2.0rc": "2012-04-14T15:48:07.542Z", "2.3.0": "2012-09-16T11:57:41.493Z", "2.3.1": "2012-12-02T11:58:59.883Z", "2.3.2": "2013-07-10T10:12:21.074Z", "2.2.1": "2013-07-10T10:43:54.691Z", "2.2.0-rc": "2013-07-10T18:09:53.960Z", "2.3.3": "2013-07-15T12:09:50.397Z", "2.4.0": "2014-11-15T12:45:29.583Z", "3.0.0": "2015-01-16T15:25:24.646Z", "3.0.1": "2015-01-21T09:54:57.077Z", "3.0.2": "2015-01-25T16:53:17.003Z", "3.0.3": "2015-02-08T07:30:14.710Z", "3.1.0": "2015-06-05T16:11:15.663Z", "3.1.1": "2015-06-05T16:33:20.886Z", "3.2.0": "2015-08-25T07:40:01.338Z", "3.2.1": "2015-09-02T11:23:01.596Z", "3.2.2": "2015-09-05T12:17:52.847Z", "3.2.3": "2016-01-15T17:53:26.875Z", "3.3.0": "2016-02-24T08:00:27.921Z", "3.3.2": "2016-02-24T08:08:04.016Z", "3.3.3": "2016-02-24T08:23:54.357Z", "3.3.4": "2016-02-24T08:41:01.991Z", "3.3.5": "2018-10-04T18:21:31.914Z", "3.3.6": "2022-01-23T08:59:47.193Z"}, "repository": {"type": "git", "url": "git+https://github.com/epeli/underscore.string.git"}, "users": {"fgribreau": true, "pid": true, "booyaa": true, "moonpyk": true, "darosh": true, "karudo": true, "jasonwbsg": true, "tpwk": true, "fmm": true, "nikunjchapadia": true, "yi": true, "yvesm": true, "yourhoneysky": true, "dgarlitt": true, "gdbtek": true, "zx371323": true, "icflorescu": true, "adamk": true, "schtoeffel": true, "asereware": true, "drew.brokke": true, "hootping": true, "dudley": true, "subchen": true, "l2g": true, "vkbansal": true, "itonyyo": true, "restuta": true, "mondalaci": true, "zhangyaochun": true, "nicholaslp": true, "samuelrn": true, "eviratec": true, "yrocq": true, "damianof": true, "jakedetels": true, "f124275809": true, "marsking": true, "monsterkodi": true, "faraoman": true, "matiasmarani": true, "nickeltobias": true, "fahadjadoon": true, "lacodda": true, "sibawite": true, "pddivine": true, "leesei": true, "ericteng177": true, "xiechao06": true, "vickykoblinski": true, "cueedee": true, "ishanyang": true, "karthick.t13": true, "n1kk": true, "ashish.npm": true}, "homepage": "http://epeli.github.com/underscore.string/", "keywords": ["underscore", "string"], "contributors": [{"name": "Esa<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://esa-matti.suuronen.org/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<https://github.com/rwz>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://koss.nocorp.me/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "https://github.com/kruckenb", "url": "<https://github.com/kruckenb>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://tchak.net>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<http://funkatron.com>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "<https://github.com/stoeffel>"}], "bugs": {"url": "https://github.com/epeli/underscore.string/issues"}, "readmeFilename": "README.markdown", "license": "MIT"}