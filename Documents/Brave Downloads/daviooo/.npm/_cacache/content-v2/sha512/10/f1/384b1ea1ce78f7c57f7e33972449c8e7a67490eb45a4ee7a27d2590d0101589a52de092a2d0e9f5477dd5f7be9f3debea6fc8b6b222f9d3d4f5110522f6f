{"name": "cacache", "dist-tags": {"legacy": "12.0.4", "latest": "19.0.1"}, "versions": {"1.0.0": {"name": "cacache", "version": "1.0.0", "dependencies": {"mv": "^2.1.1", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "dezalgo": "^1.0.3", "pumpify": "^1.3.5", "through2": "^2.0.1", "randomstring": "^1.1.5", "fs-write-stream-atomic": "^1.0.8"}, "devDependencies": {"tap": "^8.0.1", "standard": "^8.5.0"}, "dist": {"shasum": "91c256942eaaf0f013683c1462d30f16a450c408", "tarball": "https://registry.npmjs.org/cacache/-/cacache-1.0.0.tgz", "integrity": "sha512-TOTTgXgW1w2ZxVKMYggQWwH2VqROCc0p+HaTFltc4TY9gaSSHYMcupoYikGVBV2AvFhACdZFsfLTqMURScro9w==", "signatures": [{"sig": "MEYCIQDLENHba52P2IO7t7xZ0qswvVNerdjA2H9N71RW86zbzgIhALez+LhAlqSmGdXRDGJXzmqJ3pZd2ghEnApFbKYJpuuF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.0": {"name": "cacache", "version": "2.0.0", "dependencies": {"mv": "^2.1.1", "from2": "^2.3.0", "slide": "^1.1.6", "split": "^1.0.0", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "tar-fs": "^1.14.0", "dezalgo": "^1.0.3", "pumpify": "^1.3.5", "fs-extra": "^1.0.0", "inflight": "^1.0.6", "lockfile": "^1.0.2", "through2": "^2.0.1", "graceful-fs": "^4.1.10", "randomstring": "^1.1.5"}, "devDependencies": {"nyc": "^9.0.1", "tap": "^8.0.1", "tacks": "^1.2.2", "standard": "^8.5.0"}, "dist": {"shasum": "d05c1db1398b1ba77f6ab18c950098995d4bc8a7", "tarball": "https://registry.npmjs.org/cacache/-/cacache-2.0.0.tgz", "integrity": "sha512-SoahgLDVkxNN7yaQmEynkHASRY0KGE9AfxqCmSpvDdgxCvGqdv9Ml2BC5PzC+YnET2H0o8Erw/iwaQq/bXkwQA==", "signatures": [{"sig": "MEYCIQCED6EA+P6NrHOq6sCqROEwfxex3BZGwI0bwj5lrGhlPQIhAOZlihq423Yb4FZ2kS9FvfHYDRsjS8e/0auqj3TAlhmg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.0.0": {"name": "cacache", "version": "3.0.0", "dependencies": {"once": "^1.4.0", "slide": "^1.1.6", "split": "^1.0.0", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "dezalgo": "^1.0.3", "inflight": "^1.0.6", "lockfile": "^1.0.2", "graceful-fs": "^4.1.10", "mississippi": "^1.2.0", "randomstring": "^1.1.5"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^8.0.1", "tacks": "^1.2.2", "standard": "^8.6.0", "require-inject": "^1.4.0"}, "dist": {"shasum": "eb3d5aec86b698c336cfc2233a67687241541761", "tarball": "https://registry.npmjs.org/cacache/-/cacache-3.0.0.tgz", "integrity": "sha512-SmYhXmSBAYS4csrWWixmbLufePk40kGG4/HRrT+Ef9b7xp1KD8K+2chrQp20KPSYg2SAtWtUiZxwX8P838GX9A==", "signatures": [{"sig": "MEUCIQDv8ogCmPLWy/cGf6S8gJ76Pu1tmYuwJ+zVi6goy5i67AIgdpi8ZTpFXQZjOfEY8yAXOTQ20HothqYVr7el4uTy1AQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.0.1": {"name": "cacache", "version": "3.0.1", "dependencies": {"once": "^1.4.0", "slide": "^1.1.6", "split": "^1.0.0", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "dezalgo": "^1.0.3", "inflight": "^1.0.6", "lockfile": "^1.0.2", "graceful-fs": "^4.1.10", "mississippi": "^1.2.0", "randomstring": "^1.1.5"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^8.0.1", "tacks": "^1.2.2", "standard": "^8.6.0", "require-inject": "^1.4.0"}, "dist": {"shasum": "f2bbc3ea4603da1888c9577a288dbad3aa649cbb", "tarball": "https://registry.npmjs.org/cacache/-/cacache-3.0.1.tgz", "integrity": "sha512-Kg7W/5pjbz8xbr8BJOolHvwIFgrG1//3xVDHn/skcWCixljnyoIDWZzqr/3eyIlVAcU35MSP2f3I1MAvK7g9Ww==", "signatures": [{"sig": "MEUCIQDEmnyUSAaBUo6Ox/tHTUfjxB9I2UNJDLDhQBav42JY8wIgQFBxdTeQ8IPmGSDcAgjy3lLiinbXqrBj3H75eTM7oeE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.0.0": {"name": "cacache", "version": "4.0.0", "dependencies": {"once": "^1.4.0", "slide": "^1.1.6", "split": "^1.0.0", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "dezalgo": "^1.0.3", "inflight": "^1.0.6", "lockfile": "^1.0.2", "graceful-fs": "^4.1.10", "mississippi": "^1.2.0", "randomstring": "^1.1.5"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^9.0.3", "tacks": "^1.2.2", "standard": "^8.6.0", "require-inject": "^1.4.0"}, "dist": {"shasum": "acfe5f4dfb2265900ba51783d67a30868b652029", "tarball": "https://registry.npmjs.org/cacache/-/cacache-4.0.0.tgz", "integrity": "sha512-eQwQmmyzmz9Dqjrb+jI8QjPGY8LzZc/PVjpR/r4uApGRNp33Cuz7QJ1jXBkUN5zezXmFUlSAoBXHJevaGs51Bw==", "signatures": [{"sig": "MEUCIB1WnlDH2sBC2qtfSQaLad23ev7MIxrFYaNiQf50AhhjAiEAneUBKtZfNwheVtDr+xIQQ56Qi7oeB5OxH55G8JM400U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.0": {"name": "cacache", "version": "5.0.0", "dependencies": {"once": "^1.4.0", "slide": "^1.1.6", "split": "^1.0.0", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "dezalgo": "^1.0.3", "inflight": "^1.0.6", "lockfile": "^1.0.2", "graceful-fs": "^4.1.10", "mississippi": "^1.2.0", "randomstring": "^1.1.5"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^9.0.3", "tacks": "^1.2.2", "standard": "^8.6.0", "require-inject": "^1.4.0"}, "dist": {"shasum": "66eda54c377fe1afc485a6d76226c98e17ab7e73", "tarball": "https://registry.npmjs.org/cacache/-/cacache-5.0.0.tgz", "integrity": "sha512-tJxPN4jQY2vCGjnyBRvZ0APFi4Yq0fAzZopDoE4LpKUUwd7alUWWo6Jj5V8XU5JlTG41Dnh8wOqGfukyfxXj5A==", "signatures": [{"sig": "MEQCIHIYBN/4KwWmpDzjmE8KpQ4lJlOtSoDb4CW+M2vIcpWMAiB03U1Cm3buvHTgfuTDXsnE277duL9vrRLMf4pi6VGTSw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.1": {"name": "cacache", "version": "5.0.1", "dependencies": {"once": "^1.4.0", "slide": "^1.1.6", "split": "^1.0.0", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "dezalgo": "^1.0.3", "inflight": "^1.0.6", "lockfile": "^1.0.2", "graceful-fs": "^4.1.10", "mississippi": "^1.2.0", "unique-filename": "^1.1.0"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.0.2", "glob": "^7.1.1", "tacks": "^1.2.2", "standard": "^8.6.0", "require-inject": "^1.4.0"}, "dist": {"shasum": "253cb8cb059205110c5efe1b974dce6f31c0ddf1", "tarball": "https://registry.npmjs.org/cacache/-/cacache-5.0.1.tgz", "integrity": "sha512-Kn79LaXmGNU6IiAk3jzae6bnR4tLXpKeCMAeuYr4oFlq5NdqPyogyM1oG5Mwq0HW4XGjYW1frE20lpeH9a2AKA==", "signatures": [{"sig": "MEYCIQD+mDEZfYJPJfnr5vUCN58iQChpBkmZ4ouR00Z/9kwD0AIhAJAb66JXDBOAiYMyXbGq0NF8K7N9C7Ibz9P9zvD1Pmll", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.2": {"name": "cacache", "version": "5.0.2", "dependencies": {"once": "^1.4.0", "slide": "^1.1.6", "split": "^1.0.0", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "dezalgo": "^1.0.3", "inflight": "^1.0.6", "lockfile": "^1.0.2", "graceful-fs": "^4.1.10", "mississippi": "^1.2.0", "unique-filename": "^1.1.0"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.0.2", "glob": "^7.1.1", "tacks": "^1.2.2", "standard": "^8.6.0", "weallbehave": "^1.0.0", "require-inject": "^1.4.0", "weallcontribute": "^1.0.7"}, "dist": {"shasum": "5c1659e49fd83a3fd56010e5cdaad23f563302b5", "tarball": "https://registry.npmjs.org/cacache/-/cacache-5.0.2.tgz", "integrity": "sha512-MXq5lP7eJc6rL27zG8QtheiU+39ZVuYj91MyJK0Q249z5hpEuG2KI4JlQrt2rSxFT/THALTs/V47lKjm3YazAQ==", "signatures": [{"sig": "MEQCIEddyhSBjIr2MA2A17QEyR6RqQ7nn+PNxTBptALaKW5HAiB08KhT4ceoHwVPohAvhnkOOfwbxezYtI9Yh7G1CMnTUg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.3": {"name": "cacache", "version": "5.0.3", "dependencies": {"once": "^1.4.0", "slide": "^1.1.6", "split": "^1.0.0", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "dezalgo": "^1.0.3", "inflight": "^1.0.6", "lockfile": "^1.0.2", "graceful-fs": "^4.1.10", "mississippi": "^1.2.0", "checksum-stream": "^1.0.2", "unique-filename": "^1.1.0"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.0.2", "glob": "^7.1.1", "tacks": "^1.2.2", "standard": "^8.6.0", "weallbehave": "^1.0.0", "require-inject": "^1.4.0", "weallcontribute": "^1.0.7"}, "dist": {"shasum": "f8c651e6613865dda88ddfd87bc514d9cd34a65f", "tarball": "https://registry.npmjs.org/cacache/-/cacache-5.0.3.tgz", "integrity": "sha512-CcAr/dHgpKeidPvYSHOiM0lxH0jSMMAWtQYaGLwd7/L7ejYhYY1dLcBGpM3Mr82KXfPVqomg4xUezpsuNrni1A==", "signatures": [{"sig": "MEUCIQCzqw9RKvcZBdCr844+608knlIyBypWnhostlkFvOFKbwIgLirYazbp9YwB6Uf6mh9pTjLCURW0QSSkhhj3y7Zivo0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.0.0": {"name": "cacache", "version": "6.0.0", "dependencies": {"once": "^1.4.0", "slide": "^1.1.6", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "dezalgo": "^1.0.3", "bluebird": "^3.4.7", "lockfile": "^1.0.2", "graceful-fs": "^4.1.10", "mississippi": "^1.2.0", "@npmcorp/move": "^1.0.0", "checksum-stream": "^1.0.2", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.3.0", "glob": "^7.1.1", "chalk": "^1.1.3", "tacks": "^1.2.2", "standard": "^9.0.0", "benchmark": "^2.1.3", "weallbehave": "^1.0.0", "require-inject": "^1.4.0", "weallcontribute": "^1.0.8", "standard-version": "^4.0.0"}, "dist": {"shasum": "994ab2c3ec9c2233c1e55ea69dd54ba34539432d", "tarball": "https://registry.npmjs.org/cacache/-/cacache-6.0.0.tgz", "integrity": "sha512-Y9kWJCIW7izSd5C+lTqimyd3wm0s8ru0oZzqafJ0R2LMppBDVU1vc9BHjhQTGnrRyRezcve9qkeeeVX2X/xm0A==", "signatures": [{"sig": "MEUCIQDQsqf3s7DofbkMFLcuWplOmqzyWlA5s3Jwtc0pHeWzXwIgKXtr5YRLcwKrPD6UhleL1yLowMrNbgZ/7ftNEPZNX5s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.0.1": {"name": "cacache", "version": "6.0.1", "dependencies": {"once": "^1.4.0", "slide": "^1.1.6", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "dezalgo": "^1.0.3", "bluebird": "^3.4.7", "lockfile": "^1.0.2", "graceful-fs": "^4.1.10", "mississippi": "^1.2.0", "@npmcorp/move": "^1.0.0", "checksum-stream": "^1.0.2", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.3.0", "glob": "^7.1.1", "chalk": "^1.1.3", "tacks": "^1.2.2", "standard": "^9.0.0", "benchmark": "^2.1.3", "weallbehave": "^1.0.0", "require-inject": "^1.4.0", "weallcontribute": "^1.0.8", "standard-version": "^4.0.0"}, "dist": {"shasum": "cae27481c35ae7264d6bbccad7e520876302b77d", "tarball": "https://registry.npmjs.org/cacache/-/cacache-6.0.1.tgz", "integrity": "sha512-iPapvnle8cYMVJebsN/dwSH0H1/wqjLHDDwweL1Nbmf6j8kQo8sUTPY4aAD3HOeed1HxrHy19ntIRM+oA/lFcA==", "signatures": [{"sig": "MEUCIQDyWBG+bPAZgI72c07v0NMo1mNqetErPn6DQlCWI7wzNgIgO9cb2ZCUcBoZPnJBcQsqdSRRNUzyy2nNs0+5htWZBTA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.0.2": {"name": "cacache", "version": "6.0.2", "dependencies": {"once": "^1.4.0", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "dezalgo": "^1.0.3", "bluebird": "^3.4.7", "lockfile": "^1.0.2", "graceful-fs": "^4.1.10", "mississippi": "^1.2.0", "@npmcorp/move": "^1.0.0", "checksum-stream": "^1.0.2", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.3.0", "glob": "^7.1.1", "chalk": "^1.1.3", "tacks": "^1.2.2", "standard": "^9.0.0", "benchmark": "^2.1.3", "weallbehave": "^1.0.0", "require-inject": "^1.4.0", "weallcontribute": "^1.0.8", "standard-version": "^4.0.0"}, "dist": {"shasum": "abda997519d86232b2bf11a901e01caf03d66a93", "tarball": "https://registry.npmjs.org/cacache/-/cacache-6.0.2.tgz", "integrity": "sha512-RytdpNOuBhupGU+TVRIadtcxibrJUA+ZGUQX6fk4xs2eqyOZv+FCsvn6MsyzPbCF1/+03E3mHv4c116iM8V7iQ==", "signatures": [{"sig": "MEYCIQDqeU7LtNIKhLXoZF4B2Z6aljlRl80+iyEqjngKZ4SGTQIhAKRjVHS1OcGZpevJPY6N0UH4Y24p91BCXwabDV9VAYDY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.1.0": {"name": "cacache", "version": "6.1.0", "dependencies": {"once": "^1.4.0", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "dezalgo": "^1.0.3", "bluebird": "^3.4.7", "graceful-fs": "^4.1.10", "mississippi": "^1.2.0", "@npmcorp/move": "^1.0.0", "checksum-stream": "^1.0.2", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.3.0", "glob": "^7.1.1", "chalk": "^1.1.3", "tacks": "^1.2.2", "standard": "^9.0.1", "benchmark": "^2.1.3", "safe-buffer": "^5.0.1", "weallbehave": "^1.0.0", "require-inject": "^1.4.0", "weallcontribute": "^1.0.8", "standard-version": "^4.0.0"}, "dist": {"shasum": "7c86652a413e797680f1ef3e759b3c8f4a5fc599", "tarball": "https://registry.npmjs.org/cacache/-/cacache-6.1.0.tgz", "integrity": "sha512-j6atzTmvts0lgcwt/uhGkjDJN9CoaJw7ltbRkn7gd74r2oHMp5DFKgxoHIA6D0Qb10AlJuZ3vLq2nQ4ZOji+ZQ==", "signatures": [{"sig": "MEUCIQD13QV9OBryMe9uBqQnnW+vOuMqF13zhnJul8z+duFbAwIgYehaBw4NJp18SVjjFFiKyxEVkeDYQIGW2R6Ie1bj4sM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.1.1": {"name": "cacache", "version": "6.1.1", "dependencies": {"glob": "^7.1.1", "once": "^1.4.0", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "dezalgo": "^1.0.3", "bluebird": "^3.4.7", "graceful-fs": "^4.1.10", "mississippi": "^1.2.0", "@npmcorp/move": "^1.0.0", "checksum-stream": "^1.0.2", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.3.0", "chalk": "^1.1.3", "tacks": "^1.2.2", "standard": "^9.0.1", "benchmark": "^2.1.3", "safe-buffer": "^5.0.1", "weallbehave": "^1.0.0", "require-inject": "^1.4.0", "weallcontribute": "^1.0.8", "standard-version": "^4.0.0"}, "dist": {"shasum": "ad4405780016a33b608bf55a760aa18af0ada309", "tarball": "https://registry.npmjs.org/cacache/-/cacache-6.1.1.tgz", "integrity": "sha512-QXp5IxZFVmItqN+0cjwpzArnd2//TFQIRNx9mClIlukjRK8BH69I1FCZLHAb/aMXO/fBDLP85PS0G+jWd2rAyQ==", "signatures": [{"sig": "MEYCIQDddOufYSO8rff2W5EUIy5fbfdGFXenl21lETZZwB5W1QIhAIeheyC4qTJTlOf+v2/AXilJLFkOdw1SN5nOCnyHp8ij", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.1.2": {"name": "cacache", "version": "6.1.2", "dependencies": {"glob": "^7.1.1", "once": "^1.4.0", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "dezalgo": "^1.0.3", "bluebird": "^3.4.7", "graceful-fs": "^4.1.10", "mississippi": "^1.2.0", "@npmcorp/move": "^1.0.0", "checksum-stream": "^1.0.2", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.3.0", "chalk": "^1.1.3", "tacks": "^1.2.2", "standard": "^9.0.1", "benchmark": "^2.1.3", "safe-buffer": "^5.0.1", "weallbehave": "^1.0.0", "require-inject": "^1.4.0", "weallcontribute": "^1.0.8", "standard-version": "^4.0.0"}, "dist": {"shasum": "fba9b76f1e2a0fe6073000d9034108ca28d7b577", "tarball": "https://registry.npmjs.org/cacache/-/cacache-6.1.2.tgz", "integrity": "sha512-o+WYtDKOnIkk+WBVOiEhZvl0/D+Z9TmX/+GU10wU5bHnre4x0LkKynhW+I/hqA0L9iXfII8o9hORHjTArFuapA==", "signatures": [{"sig": "MEUCIQDRCftsZdDpDXycqW4ak/gBeU3wAVF+o32weHU+naPrUgIgG3G3nBCI5GoxVJlvdhh5wSE/wLU4BfG2PwYnxyFIenU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.2.0": {"name": "cacache", "version": "6.2.0", "dependencies": {"glob": "^7.1.1", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "bluebird": "^3.4.7", "graceful-fs": "^4.1.10", "mississippi": "^1.2.0", "@npmcorp/move": "^1.0.0", "checksum-stream": "^1.0.2", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1"}, "devDependencies": {"nyc": "^10.0.0", "tap": "^10.3.0", "chalk": "^1.1.3", "tacks": "^1.2.2", "standard": "^9.0.1", "benchmark": "^2.1.3", "safe-buffer": "^5.0.1", "weallbehave": "^1.0.0", "require-inject": "^1.4.0", "weallcontribute": "^1.0.8", "standard-version": "^4.0.0"}, "dist": {"shasum": "ed3001398eacbb3750241cc57375202bb81ae5d1", "tarball": "https://registry.npmjs.org/cacache/-/cacache-6.2.0.tgz", "integrity": "sha512-c8pv07FrC3Lj4AG5GYCOZJPKBwM5nt9LWTXabNhQU75bpAEeAlvGtiWM7GIiutKZzAYKdwCxJpAmvPOs2zOwHg==", "signatures": [{"sig": "MEUCIHHlHCCljFN75blNl7FoVEhxxZfpXSkrV/qoi6XJvTiaAiEA6PUJo5KOMhHzVwUuCCGB4qOptY0S79ik7m7gKZ5fNrg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.3.0": {"name": "cacache", "version": "6.3.0", "dependencies": {"glob": "^7.1.1", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "bluebird": "^3.4.7", "lru-cache": "^4.0.2", "graceful-fs": "^4.1.10", "mississippi": "^1.2.0", "checksum-stream": "^1.0.2", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.0"}, "devDependencies": {"nyc": "^10.2.0", "tap": "^10.3.1", "chalk": "^1.1.3", "tacks": "^1.2.2", "standard": "^9.0.2", "benchmark": "^2.1.4", "safe-buffer": "^5.0.1", "weallbehave": "^1.0.0", "require-inject": "^1.4.0", "weallcontribute": "^1.0.8", "standard-version": "^4.0.0"}, "dist": {"shasum": "ecc428901b79aabbd0b0492bca62b88cda0d4773", "tarball": "https://registry.npmjs.org/cacache/-/cacache-6.3.0.tgz", "integrity": "sha512-cVwaVKxhVlpzDTLE5CHfIMf/Wvi+CJ0co1t9ADJpU6NyXhk/KRoZRFHcTyWDF4dC6yUn1SgM1Y+WbrW/n6Z00g==", "signatures": [{"sig": "MEUCIQDdrdinGC95KISwJPE726Se6gKQL22nz+ZdRpIqsaR3NAIgacG4igBZHxoHA8GAlkhFacUed68fA6im5q8W804Enqg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.0.0": {"name": "cacache", "version": "7.0.0", "dependencies": {"glob": "^7.1.1", "ssri": "^3.0.0", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "bluebird": "^3.4.7", "lru-cache": "^4.0.2", "graceful-fs": "^4.1.10", "mississippi": "^1.2.0", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.0"}, "devDependencies": {"nyc": "^10.2.0", "tap": "^10.3.1", "chalk": "^1.1.3", "tacks": "^1.2.2", "standard": "^9.0.2", "benchmark": "^2.1.4", "safe-buffer": "^5.0.1", "weallbehave": "^1.0.0", "require-inject": "^1.4.0", "weallcontribute": "^1.0.8", "standard-version": "^4.0.0"}, "dist": {"shasum": "7e59224ff4f1ebafe5f42ff68f472d179a5c204c", "tarball": "https://registry.npmjs.org/cacache/-/cacache-7.0.0.tgz", "integrity": "sha512-KvWY0A2KKX97wwKvEeXwi1FH0O24RExZmtTnCc8LDLiJXC84E5EjkR7uXTVnXLYMWwE1+bMnRVixbILEdwMfLg==", "signatures": [{"sig": "MEUCIQDCZR9+jIkLqBrZEuixNtNmFJzG+6JCork8VhTqq+vhmwIgWaqJWmEBXTqAQzhqC30PKuIOlSSGZDpwCSObgwWw5iA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.0.1": {"name": "cacache", "version": "7.0.1", "dependencies": {"glob": "^7.1.1", "ssri": "^3.0.0", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "bluebird": "^3.4.7", "lru-cache": "^4.0.2", "graceful-fs": "^4.1.10", "mississippi": "^1.2.0", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.0"}, "devDependencies": {"nyc": "^10.2.0", "tap": "^10.3.1", "chalk": "^1.1.3", "tacks": "^1.2.2", "standard": "^9.0.2", "benchmark": "^2.1.4", "safe-buffer": "^5.0.1", "weallbehave": "^1.0.0", "require-inject": "^1.4.0", "weallcontribute": "^1.0.8", "standard-version": "^4.0.0"}, "dist": {"shasum": "7f66ca4f725b121d8037067e1979b0019727b4f4", "tarball": "https://registry.npmjs.org/cacache/-/cacache-7.0.1.tgz", "integrity": "sha512-GiYXKHelbNXdxEgwXR9HLSFSvhwGZgtmynA+onvxFUSDC0dDH7jfoCODKa7DYc0nl225+f5pQlJH24jTgi52UA==", "signatures": [{"sig": "MEUCIBnyz0F2aQ9tE7JghMDqOTpqYafeBL9hMIGrbGPSMKhMAiEAmyeMO0/CM+/iUlLO2ubrIWXc3SwuNNBbLy2QUyDDnoQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.0.2": {"name": "cacache", "version": "7.0.2", "dependencies": {"glob": "^7.1.1", "ssri": "^4.0.0", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "bluebird": "^3.4.7", "lru-cache": "^4.0.2", "graceful-fs": "^4.1.10", "mississippi": "^1.2.0", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.0"}, "devDependencies": {"nyc": "^10.2.0", "tap": "^10.3.1", "chalk": "^1.1.3", "tacks": "^1.2.2", "standard": "^9.0.2", "benchmark": "^2.1.4", "safe-buffer": "^5.0.1", "weallbehave": "^1.0.0", "require-inject": "^1.4.0", "weallcontribute": "^1.0.8", "standard-version": "^4.0.0"}, "dist": {"shasum": "5b7f7155675b0559ad98a1961f9d0a4c1260532d", "tarball": "https://registry.npmjs.org/cacache/-/cacache-7.0.2.tgz", "integrity": "sha512-<PERSON>c9qaBS9PQDEGSQM1f2jExrVG8ALIjXKKNCR8EBVUJ+C9HtG0/T26vktgCrIRqKK11V+77ED2pi/A0WFz/obUQ==", "signatures": [{"sig": "MEQCIBrDnjfy453UvdGdeKPgNT5iHaVxjihfG+9zog9UZesGAiAdYee8gey12kEWKiG+bKAQ5V/6VaixXZtq2+qiIoezcg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.0.3": {"name": "cacache", "version": "7.0.3", "dependencies": {"glob": "^7.1.1", "ssri": "^4.0.0", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "bluebird": "^3.4.7", "lru-cache": "^4.0.2", "graceful-fs": "^4.1.10", "mississippi": "^1.2.0", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.0"}, "devDependencies": {"nyc": "^10.2.0", "tap": "^10.3.1", "chalk": "^1.1.3", "tacks": "^1.2.2", "standard": "^9.0.2", "benchmark": "^2.1.4", "safe-buffer": "^5.0.1", "weallbehave": "^1.0.0", "require-inject": "^1.4.0", "weallcontribute": "^1.0.8", "standard-version": "^4.0.0"}, "dist": {"shasum": "62e876694cf2c094d319f257b83769ea752278ab", "tarball": "https://registry.npmjs.org/cacache/-/cacache-7.0.3.tgz", "integrity": "sha512-LXz2O7ylBLq1yzJpn90qT9hDuq4bBH1rKCo6gGR93hGAcWIZgKiYRivVbNx0RPXu0DJc2YeXt8/6NpAdf5hMYA==", "signatures": [{"sig": "MEYCIQD8j4ZmJ0wt9cR7O/KMSWh5+lLuf8rYj89VALmOhNQTeQIhAPwDip2+yjrIIBNO13HB4yi1cAzuLH4s6xxpo1HDa+ST", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.0.4": {"name": "cacache", "version": "7.0.4", "dependencies": {"glob": "^7.1.1", "ssri": "^4.1.0", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "bluebird": "^3.4.7", "lru-cache": "^4.0.2", "graceful-fs": "^4.1.10", "mississippi": "^1.2.0", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.0"}, "devDependencies": {"nyc": "^10.2.0", "tap": "^10.3.2", "chalk": "^1.1.3", "tacks": "^1.2.2", "standard": "^10.0.1", "benchmark": "^2.1.4", "safe-buffer": "^5.0.1", "weallbehave": "^1.0.0", "require-inject": "^1.4.0", "weallcontribute": "^1.0.8", "standard-version": "^4.0.0"}, "dist": {"shasum": "59eb6a4dca1aa3dc2a6450c097679afba37bc990", "tarball": "https://registry.npmjs.org/cacache/-/cacache-7.0.4.tgz", "integrity": "sha512-zCT7FK/JgFoMr6sGKCULVbKYU/q1aFJVDvp8oI+t/5QgkUw4Hx8CSVYACM1tNShBkd5Avuv2GfKXq+1DO0+8bQ==", "signatures": [{"sig": "MEYCIQCKXgfxDNZdoh/iAhoomqdtzxZqZ7ssUjhQB3oomGs56wIhANQUKjEOve76IlPCQZSSM5tOhzL79POrcmaQymcyd0yB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.0.5": {"name": "cacache", "version": "7.0.5", "dependencies": {"glob": "^7.1.1", "ssri": "^4.1.2", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "bluebird": "^3.4.7", "lru-cache": "^4.0.2", "graceful-fs": "^4.1.10", "mississippi": "^1.2.0", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.0"}, "devDependencies": {"nyc": "^10.2.0", "tap": "^10.3.2", "chalk": "^1.1.3", "tacks": "^1.2.2", "standard": "^10.0.1", "benchmark": "^2.1.4", "safe-buffer": "^5.0.1", "weallbehave": "^1.0.0", "require-inject": "^1.4.0", "weallcontribute": "^1.0.8", "standard-version": "^4.0.0"}, "dist": {"shasum": "31b23a28b2b1e4083e60a42df9ddd2e5dbd3b4ce", "tarball": "https://registry.npmjs.org/cacache/-/cacache-7.0.5.tgz", "integrity": "sha512-g5ObsVGIJiCMMvKgWXCeUuX+aUgT4RkzbelngjUjqtwaJDvM2CvzGcYUOziQ3fdwisGwFzW24+QqkWdzeIUWTg==", "signatures": [{"sig": "MEQCIBwdGhNYM/0Ky8wrkyHSCKCbMFVN+RyND7aLqvmSJxinAiB9uSBnEOeOoTkC7IzVsLrV0lJO48ixErar70PuJOfeXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.1.0": {"name": "cacache", "version": "7.1.0", "dependencies": {"glob": "^7.1.1", "ssri": "^4.1.2", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "bluebird": "^3.4.7", "lru-cache": "^4.0.2", "graceful-fs": "^4.1.10", "mississippi": "^1.2.0", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.0"}, "devDependencies": {"nyc": "^10.2.0", "tap": "^10.3.2", "chalk": "^1.1.3", "tacks": "^1.2.2", "standard": "^10.0.2", "benchmark": "^2.1.4", "safe-buffer": "^5.0.1", "weallbehave": "^1.0.0", "require-inject": "^1.4.0", "weallcontribute": "^1.0.8", "standard-version": "^4.0.0"}, "dist": {"shasum": "f73777163e437e4ec45e21a2be298edeb584e36f", "tarball": "https://registry.npmjs.org/cacache/-/cacache-7.1.0.tgz", "integrity": "sha512-HkbOhxXGJ80pAkyJeKJZ0zTqniCUF1zVGHPDBoJhKMSeYk2GAD6ed4Iwf/Ql00+L4COwUfld3NLCkI31u064Iw==", "signatures": [{"sig": "MEUCIEzlgUQIMOCqxcmUtt3pskeNYoKn/aw02TxFDaOQUpYCAiEAmQQRjgVgtsMZI6h3yKaFehWdVgg4J1+bvNUribnD+aI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "8.0.0": {"name": "cacache", "version": "8.0.0", "dependencies": {"glob": "^7.1.1", "ssri": "^4.1.2", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "bluebird": "^3.4.7", "lru-cache": "^4.0.2", "graceful-fs": "^4.1.10", "mississippi": "^1.2.0", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.0"}, "devDependencies": {"nyc": "^10.2.0", "tap": "^10.3.2", "chalk": "^1.1.3", "tacks": "^1.2.2", "standard": "^10.0.2", "benchmark": "^2.1.4", "safe-buffer": "^5.0.1", "weallbehave": "^1.0.0", "require-inject": "^1.4.0", "weallcontribute": "^1.0.8", "standard-version": "^4.0.0"}, "dist": {"shasum": "2c3899c16941aeb0e7378845df8aff03bb0eb81a", "tarball": "https://registry.npmjs.org/cacache/-/cacache-8.0.0.tgz", "integrity": "sha512-EU2cZHCYkIfX05XinjeKTVx/WCnJuM6Tu7I7xDrDrG2+/FY8Y6qAnsbqgE3541OAzILM/kJMuBY1gTMbeX6teQ==", "signatures": [{"sig": "MEQCIDE/hr3Az6iyPVM02DBH12yvHSXTf9r3wJRpEPIhRessAiBHf5Q+4prDbJPdl4uky/Qw1pADTf8wCK6mlM9GL7wWLA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "9.0.0": {"name": "cacache", "version": "9.0.0", "dependencies": {"glob": "^7.1.1", "ssri": "^4.1.2", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "bluebird": "^3.4.7", "lru-cache": "^4.0.2", "graceful-fs": "^4.1.10", "mississippi": "^1.2.0", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.0"}, "devDependencies": {"nyc": "^10.2.0", "tap": "^10.3.2", "chalk": "^1.1.3", "tacks": "^1.2.2", "standard": "^10.0.2", "benchmark": "^2.1.4", "safe-buffer": "^5.0.1", "weallbehave": "^1.0.0", "require-inject": "^1.4.0", "weallcontribute": "^1.0.8", "standard-version": "^4.0.0"}, "dist": {"shasum": "edcf620030b0fbf3708193f0718136eedb2170a4", "tarball": "https://registry.npmjs.org/cacache/-/cacache-9.0.0.tgz", "integrity": "sha512-VkeahOGLrti/Dh1y+UEcudtK5WWynbAWOmIuMmJ5RdR9EoPyraSuBj0BzN7nEAgRw19NNy/X1QOvU3jGWvxhJw==", "signatures": [{"sig": "MEQCIH2Y3x/DSibqxBwRYzr/ZW06ss+tfyULgqbkXIuxQvkIAiBpRWQalSLbh5cAnqrPwErP49gVKmq9zIiAgaYefhr7Lw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "9.1.0": {"name": "cacache", "version": "9.1.0", "dependencies": {"glob": "^7.1.1", "ssri": "^4.1.2", "y18n": "^3.2.1", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "bluebird": "^3.4.7", "lru-cache": "^4.0.2", "graceful-fs": "^4.1.10", "mississippi": "^1.2.0", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.0"}, "devDependencies": {"nyc": "^10.2.0", "tap": "^10.3.2", "chalk": "^1.1.3", "tacks": "^1.2.2", "standard": "^10.0.2", "benchmark": "^2.1.4", "cross-env": "^5.0.0", "safe-buffer": "^5.0.1", "weallbehave": "^1.0.0", "require-inject": "^1.4.0", "weallcontribute": "^1.0.8", "standard-version": "^4.0.0"}, "dist": {"shasum": "7972de4fa9f2a81a4b737011a2cf1f0e0d7ab213", "tarball": "https://registry.npmjs.org/cacache/-/cacache-9.1.0.tgz", "integrity": "sha512-pNQeGpcAptdM0JFJA3kQfKoMrg43vuQBgxdoqbPRNMcAjO1oXONAvN4T3RJsZsmgmvNY/bQmotne4nmsEyFn4g==", "signatures": [{"sig": "MEYCIQC4vt2VqNQCqh1E/SbUS/TBffTdKcbN9h4NPVydHdrPeQIhAN0yLoRRXmmsF3H25ijUn02ypaZ7WJ3eAy4+OTJyvhW6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "9.2.0": {"name": "cacache", "version": "9.2.0", "dependencies": {"glob": "^7.1.1", "ssri": "^4.1.2", "y18n": "^3.2.1", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "bluebird": "^3.4.7", "lru-cache": "^4.0.2", "graceful-fs": "^4.1.10", "mississippi": "^1.2.0", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.0"}, "devDependencies": {"nyc": "^10.2.0", "tap": "^10.3.2", "chalk": "^1.1.3", "tacks": "^1.2.2", "standard": "^10.0.2", "benchmark": "^2.1.4", "cross-env": "^5.0.0", "safe-buffer": "^5.0.1", "weallbehave": "^1.0.0", "require-inject": "^1.4.0", "weallcontribute": "^1.0.8", "standard-version": "^4.0.0"}, "dist": {"shasum": "5e14e78842ea7c8df0c35fc4b315452724c27b79", "tarball": "https://registry.npmjs.org/cacache/-/cacache-9.2.0.tgz", "integrity": "sha512-6p5OrZdfA2f/JX2y2u70FsG40h8bib83wBVaFnKqDLaWeii4yvkR4jCC4P9tADyee3Y9sgYnWPvv0XCZMUfPBA==", "signatures": [{"sig": "MEUCIQDKBGjZIzAnVDvXH01Ppv20TvZ5+QkAUEkgCRcjstebLQIgFfSOOtutzH4Cnd9wApcNG19pNOPsMdAZlHzkeXBfxfc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "9.2.1": {"name": "cacache", "version": "9.2.1", "dependencies": {"glob": "^7.1.1", "ssri": "^4.1.2", "y18n": "^3.2.1", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "bluebird": "^3.4.7", "lru-cache": "^4.0.2", "graceful-fs": "^4.1.10", "mississippi": "^1.2.0", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.0"}, "devDependencies": {"nyc": "^10.2.0", "tap": "^10.3.2", "chalk": "^1.1.3", "tacks": "^1.2.2", "standard": "^10.0.2", "benchmark": "^2.1.4", "cross-env": "^5.0.0", "safe-buffer": "^5.0.1", "weallbehave": "^1.0.0", "require-inject": "^1.4.0", "weallcontribute": "^1.0.8", "standard-version": "^4.0.0"}, "dist": {"shasum": "5baf6875a3ef3dca4fdf33efb9b0e3ac23a983a3", "tarball": "https://registry.npmjs.org/cacache/-/cacache-9.2.1.tgz", "integrity": "sha512-wknnaRGoo1yzuXMdXXbT/+i/78PWdjZGyNC2LY9t73zARhV0DRrOrJI+eSebosIvtiDZXHK7DiAgl94gGWdtyA==", "signatures": [{"sig": "MEYCIQCdUpFdfbj+LCgGRI7BBjccnCgHwI9qyODlRGa9WKrkUwIhAM3M0DoF5bt6PF6AYDI9aj9HxBtyjde66618acxY+jLo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "9.2.2": {"name": "cacache", "version": "9.2.2", "dependencies": {"glob": "^7.1.1", "ssri": "^4.1.2", "y18n": "^3.2.1", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "bluebird": "^3.4.7", "lru-cache": "^4.0.2", "graceful-fs": "^4.1.10", "mississippi": "^1.2.0", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.0"}, "devDependencies": {"nyc": "^10.2.0", "tap": "^10.3.2", "chalk": "^1.1.3", "tacks": "^1.2.2", "standard": "^10.0.2", "benchmark": "^2.1.4", "cross-env": "^5.0.0", "safe-buffer": "^5.0.1", "weallbehave": "^1.0.0", "require-inject": "^1.4.0", "weallcontribute": "^1.0.8", "standard-version": "^4.0.0"}, "dist": {"shasum": "cb67e5c3497d474f6b6d889a90ebfc969f2d83fa", "tarball": "https://registry.npmjs.org/cacache/-/cacache-9.2.2.tgz", "integrity": "sha512-KchIh0VVk0zpYKtztqFQDYc2ZnQAqwOO3Z5bsuxYfTJuNGvUgEVEBlEVmb/Rf3t3CKgd/8U7x2RC+lgJe0kz2Q==", "signatures": [{"sig": "MEUCID61eTMrCIcg8bcX/wmPZ9wBuPVEkDrb+fInhU3cUijKAiEAknlN7WcdFuo9mVtUVeQfWuilwwnDMV7pTvrfo/CmSiA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "9.2.3": {"name": "cacache", "version": "9.2.3", "dependencies": {"glob": "^7.1.1", "ssri": "^4.1.2", "y18n": "^3.2.1", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "bluebird": "^3.4.7", "lru-cache": "^4.0.2", "graceful-fs": "^4.1.10", "mississippi": "^1.2.0", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.0"}, "devDependencies": {"nyc": "^10.2.0", "tap": "^10.3.2", "chalk": "^1.1.3", "tacks": "^1.2.2", "standard": "^10.0.2", "benchmark": "^2.1.4", "cross-env": "^5.0.0", "safe-buffer": "^5.0.1", "weallbehave": "^1.0.0", "require-inject": "^1.4.0", "weallcontribute": "^1.0.8", "standard-version": "^4.0.0"}, "dist": {"shasum": "22edd762e8f91a2d89dc9a2f6f7f28a6b11bf71e", "tarball": "https://registry.npmjs.org/cacache/-/cacache-9.2.3.tgz", "integrity": "sha512-Xvz0paVT+igGRdGPDfMy2UgAFnbc77hp6/XruCiJQzcBtKzb+jkP1NG0kAHS8RKp3h560Fc09WnonXyT/oXMxA==", "signatures": [{"sig": "MEUCIDoSlD4qJEqVRm7vqAIcD69x6/6DGyF4l5ENC263ORfuAiEAswScIKhnocfpVQ5W5k+CLXvtnlw4VmEIksgJVC3LviI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "9.2.4": {"name": "cacache", "version": "9.2.4", "dependencies": {"glob": "^7.1.2", "ssri": "^4.1.2", "y18n": "^3.2.1", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "bluebird": "^3.4.7", "lru-cache": "^4.0.2", "graceful-fs": "^4.1.10", "mississippi": "^1.2.0", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.0"}, "devDependencies": {"nyc": "^10.3.2", "tap": "^10.3.2", "chalk": "^1.1.3", "tacks": "^1.2.2", "standard": "^10.0.2", "benchmark": "^2.1.4", "cross-env": "^5.0.0", "safe-buffer": "^5.0.1", "weallbehave": "^1.2.0", "require-inject": "^1.4.0", "weallcontribute": "^1.0.8", "standard-version": "^4.0.0"}, "dist": {"shasum": "f222f569e6d3e1415ad1ae66969c69ca0fc25955", "tarball": "https://registry.npmjs.org/cacache/-/cacache-9.2.4.tgz", "integrity": "sha512-DkEucrb5TwM6yCLgDfyHWMH3QECt9g0pMGNtuGBrALo/B0FcQSnt8B+DyyuPFqOvSOwSPZgqYD4TK9IKJBUoKg==", "signatures": [{"sig": "MEUCIQDuF5PtJeqAkDzn6Kq7rG11bmA1tDBPjTfexKASsVA/vAIgMbqQ4jzmvqI7ehZlw2QHo1LYPqOJEqQ1PiGPXwClMbA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "9.2.5": {"name": "cacache", "version": "9.2.5", "dependencies": {"glob": "^7.1.2", "ssri": "^4.1.3", "y18n": "^3.2.1", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "bluebird": "^3.5.0", "lru-cache": "^4.0.2", "graceful-fs": "^4.1.11", "mississippi": "^1.3.0", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.1"}, "devDependencies": {"nyc": "^10.3.2", "tap": "^10.3.2", "chalk": "^1.1.3", "tacks": "^1.2.2", "standard": "^10.0.2", "benchmark": "^2.1.4", "cross-env": "^5.0.0", "safe-buffer": "^5.0.1", "weallbehave": "^1.2.0", "require-inject": "^1.4.0", "weallcontribute": "^1.0.8", "standard-version": "^4.0.0"}, "dist": {"shasum": "cb401d0e59858532062de1f104097cb40c71c3bf", "tarball": "https://registry.npmjs.org/cacache/-/cacache-9.2.5.tgz", "integrity": "sha512-mURsTvkjbCSFRTdkuPhHUp9sbEHn3AVrvM4mveg/bhlKKYolfRm23TsFUVAssC9p622lwmh7pgpb+H5mSVpYcA==", "signatures": [{"sig": "MEUCIQChMINu6zfqyYQ0uIAdM/mRTA/7Atl+7haRU+BtVHpKcwIgZY4Dlg5HIYmoiMoZGdX0ugU8tCFO8If7KTCbyPOn/eA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "9.2.6": {"name": "cacache", "version": "9.2.6", "dependencies": {"glob": "^7.1.2", "ssri": "^4.1.4", "y18n": "^3.2.1", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "bluebird": "^3.5.0", "lru-cache": "^4.0.2", "graceful-fs": "^4.1.11", "mississippi": "^1.3.0", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.1"}, "devDependencies": {"nyc": "^10.3.2", "tap": "^10.3.2", "chalk": "^1.1.3", "tacks": "^1.2.2", "standard": "^10.0.2", "benchmark": "^2.1.4", "cross-env": "^5.0.0", "safe-buffer": "^5.0.1", "weallbehave": "^1.2.0", "require-inject": "^1.4.0", "weallcontribute": "^1.0.8", "standard-version": "^4.0.0"}, "dist": {"shasum": "ea5c7f2b6b514710a22a58a27f857fd972fdfa51", "tarball": "https://registry.npmjs.org/cacache/-/cacache-9.2.6.tgz", "integrity": "sha512-YK0Z5Np5t755edPL6gfdCeGxtU0rcW/DBhYhYVDckT+7AFkCCtedf2zru5NRbBLFk6e7Agi/RaqTOAfiaipUfg==", "signatures": [{"sig": "MEYCIQC4cVGXWnGqAqriagypS7UG3PnOPVOBbN36VmgOBVuMkwIhAKjqeNOwzM32T5888YUQLO2EQAXfrVwkFpKShAIjBrUj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "9.2.7": {"name": "cacache", "version": "9.2.7", "dependencies": {"glob": "^7.1.2", "ssri": "^4.1.4", "y18n": "^3.2.1", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "bluebird": "^3.5.0", "lru-cache": "^4.0.2", "graceful-fs": "^4.1.11", "mississippi": "^1.3.0", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.1"}, "devDependencies": {"nyc": "^11.0.2", "tap": "^10.3.3", "chalk": "^1.1.3", "tacks": "^1.2.2", "standard": "^10.0.2", "benchmark": "^2.1.4", "cross-env": "^5.0.0", "safe-buffer": "^5.1.0", "weallbehave": "^1.2.0", "require-inject": "^1.4.0", "weallcontribute": "^1.0.8", "standard-version": "^4.0.0"}, "dist": {"shasum": "67d71835fed94f6989bde522cf2956df862e3ca5", "tarball": "https://registry.npmjs.org/cacache/-/cacache-9.2.7.tgz", "integrity": "sha512-SWRnkRNV5h61SeTfPvVYotM2yqW5KXtG835CebVV7G5EYHQu+dgQbNkasSIcN7LWeoaViLpgaxVlt01TFpqOKw==", "signatures": [{"sig": "MEUCIQC+xzNiiaw9pXFTNOpH2ZRDKwzfZNQgvp/GFLTzbbiGugIgc03cVGZawZFMMvSsEZienOyEiWO7Pk4e2ZistWri2Tg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "9.2.8": {"name": "cacache", "version": "9.2.8", "dependencies": {"glob": "^7.1.2", "ssri": "^4.1.5", "y18n": "^3.2.1", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "bluebird": "^3.5.0", "lru-cache": "^4.0.2", "graceful-fs": "^4.1.11", "mississippi": "^1.3.0", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.1"}, "devDependencies": {"nyc": "^11.0.2", "tap": "^10.3.3", "chalk": "^1.1.3", "tacks": "^1.2.2", "standard": "^10.0.2", "benchmark": "^2.1.4", "cross-env": "^5.0.0", "safe-buffer": "^5.1.0", "weallbehave": "^1.2.0", "require-inject": "^1.4.0", "weallcontribute": "^1.0.8", "standard-version": "^4.0.0"}, "dist": {"shasum": "2e38b51161a3904e3b9fb35c0869b751f7d0bcf4", "tarball": "https://registry.npmjs.org/cacache/-/cacache-9.2.8.tgz", "integrity": "sha512-nA3gmaDPEsFWqI5eYAe35IfvW54yGJ3ns2wDopWf4iDA3fkhBNsdvnYp4NrL+L7ysMt0/isM84Mwi+b4l8/pMQ==", "signatures": [{"sig": "MEYCIQDpbJOrvPmN9xcNinxdhgggtjnYPZL11bm+ev7L/mRa8wIhALmzcG84yVipNh4vnHP0/kEXo6CAQUD+4/qhWtXwvbu9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "9.2.9": {"name": "cacache", "version": "9.2.9", "dependencies": {"glob": "^7.1.2", "ssri": "^4.1.6", "y18n": "^3.2.1", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "bluebird": "^3.5.0", "lru-cache": "^4.1.1", "graceful-fs": "^4.1.11", "mississippi": "^1.3.0", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.1"}, "devDependencies": {"nyc": "^11.0.2", "tap": "^10.3.4", "chalk": "^1.1.3", "tacks": "^1.2.2", "standard": "^10.0.2", "benchmark": "^2.1.4", "cross-env": "^5.0.1", "safe-buffer": "^5.1.0", "weallbehave": "^1.2.0", "require-inject": "^1.4.0", "weallcontribute": "^1.0.8", "standard-version": "^4.2.0"}, "dist": {"shasum": "f9d7ffe039851ec94c28290662afa4dd4bb9e8dd", "tarball": "https://registry.npmjs.org/cacache/-/cacache-9.2.9.tgz", "integrity": "sha512-ghg1j5OyTJ6qsrqU++dN23QiTDxb5AZCFGsF3oB+v9v/gY+F4X8L/0gdQMEjd+8Ot3D29M2etX5PKozHRn2JQw==", "signatures": [{"sig": "MEUCIQCw+exZ2c5OJV6fB8n9aAcKckTXf7G0He4NcAK7nXmtRQIgcEihR2a8QeZJz04Z1G3lFcxbqD4gSC3mGHzj7kTYOEw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "9.3.0": {"name": "cacache", "version": "9.3.0", "dependencies": {"glob": "^7.1.2", "ssri": "^4.1.6", "y18n": "^3.2.1", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "bluebird": "^3.5.0", "lru-cache": "^4.1.1", "graceful-fs": "^4.1.11", "mississippi": "^1.3.0", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.1"}, "devDependencies": {"nyc": "^11.1.0", "tap": "^10.7.0", "chalk": "^2.0.1", "tacks": "^1.2.2", "standard": "^10.0.2", "benchmark": "^2.1.4", "cross-env": "^5.0.1", "safe-buffer": "^5.1.1", "weallbehave": "^1.2.0", "require-inject": "^1.4.2", "weallcontribute": "^1.0.8", "standard-version": "^4.2.0"}, "dist": {"shasum": "9cd58f2dd0b8c8cacf685b7067b416d6d3cf9db1", "tarball": "https://registry.npmjs.org/cacache/-/cacache-9.3.0.tgz", "integrity": "sha512-Vbi8J1XfC8v+FbQ6QkOtKXsHpPnB0i9uMeYFJoj40EbdOsEqWB3DPpNjfsnYBkqOPYA8UvrqH6FZPpBP0zdN7g==", "signatures": [{"sig": "MEUCIQCbDLmMvoy2ecCubOpIf0ngPQ0H/dQOLIbtBNOue9bzhwIgVntgr5CAiXoyvpvxQLjBJ2fr6w2nJeNtZVOdkj8rGLM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "10.0.0": {"name": "cacache", "version": "10.0.0", "dependencies": {"glob": "^7.1.2", "ssri": "^5.0.0", "y18n": "^3.2.1", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "bluebird": "^3.5.0", "lru-cache": "^4.1.1", "graceful-fs": "^4.1.11", "mississippi": "^1.3.0", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.1"}, "devDependencies": {"nyc": "^11.1.0", "tap": "^10.7.0", "chalk": "^2.0.1", "tacks": "^1.2.2", "standard": "^10.0.2", "benchmark": "^2.1.4", "cross-env": "^5.0.1", "safe-buffer": "^5.1.1", "weallbehave": "^1.2.0", "require-inject": "^1.4.2", "weallcontribute": "^1.0.8", "standard-version": "^4.2.0"}, "dist": {"shasum": "3bba88bf62b0773fd9a691605f60c9d3c595e853", "tarball": "https://registry.npmjs.org/cacache/-/cacache-10.0.0.tgz", "integrity": "sha512-s9h6I9NY3KcBjfuS28K6XNmrv/HNFSzlpVD6eYMXugZg3Y8jjI1lUzTeUMa0oKByCDtHfsIy5Ec7KgWRnC5gtg==", "signatures": [{"sig": "MEQCIBI6ZH8L+12aFng4fcUO4Dntso7pvAU8DCr99B7Mc1d/AiBdSy7kp7ZUehYakWVnlUoSH7JPG+Mpg69qr3Dlp6NUbQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "10.0.1": {"name": "cacache", "version": "10.0.1", "dependencies": {"glob": "^7.1.2", "ssri": "^5.0.0", "y18n": "^3.2.1", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "bluebird": "^3.5.0", "lru-cache": "^4.1.1", "graceful-fs": "^4.1.11", "mississippi": "^1.3.0", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.1"}, "devDependencies": {"nyc": "^11.1.0", "tap": "^10.7.0", "chalk": "^2.0.1", "tacks": "^1.2.2", "standard": "^10.0.2", "benchmark": "^2.1.4", "cross-env": "^5.0.1", "safe-buffer": "^5.1.1", "weallbehave": "^1.2.0", "require-inject": "^1.4.2", "weallcontribute": "^1.0.8", "standard-version": "^4.2.0"}, "dist": {"shasum": "3e05f6e616117d9b54665b1b20c8aeb93ea5d36f", "tarball": "https://registry.npmjs.org/cacache/-/cacache-10.0.1.tgz", "integrity": "sha512-dRHYcs9LvG9cHgdPzjiI+/eS7e1xRhULrcyOx04RZQsszNJXU2SL9CyG60yLnge282Qq5nwTv+ieK2fH+WPZmA==", "signatures": [{"sig": "MEUCIFuWSdkd1KvL3q89iKrIVD1AUgZUVCd+LLeCyieuLEnRAiEAxQ3J4wj/cKmbRV3wKJ3FVi10B1jhzYZDRjAUsrmrt/U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "10.0.2": {"name": "cacache", "version": "10.0.2", "dependencies": {"glob": "^7.1.2", "ssri": "^5.0.0", "y18n": "^3.2.1", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "bluebird": "^3.5.0", "lru-cache": "^4.1.1", "graceful-fs": "^4.1.11", "mississippi": "^1.3.0", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.1"}, "devDependencies": {"nyc": "^11.1.0", "tap": "^10.7.0", "chalk": "^2.0.1", "tacks": "^1.2.2", "standard": "^10.0.2", "benchmark": "^2.1.4", "cross-env": "^5.0.1", "safe-buffer": "^5.1.1", "weallbehave": "^1.2.0", "require-inject": "^1.4.2", "weallcontribute": "^1.0.8", "standard-version": "^4.2.0"}, "dist": {"shasum": "105a93a162bbedf3a25da42e1939ed99ffb145f8", "tarball": "https://registry.npmjs.org/cacache/-/cacache-10.0.2.tgz", "integrity": "sha512-dljb7dk1jqO5ogE+dRpoR9tpHYv5xz9vPSNunh1+0wRuNdYxmzp9WmsyokgW/DUF1FDRVA/TMsmxt027R8djbQ==", "signatures": [{"sig": "MEUCIEvDtjyuT+r4KyJ2d+ppvxbBpomxBfvFVEcy33fl6O1EAiEAh7fkj12Lsxy8QdpYAHjM7uw7mxe9szeZGGfK7tEmMSo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "10.0.3": {"name": "cacache", "version": "10.0.3", "dependencies": {"glob": "^7.1.2", "ssri": "^5.0.0", "y18n": "^3.2.1", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "bluebird": "^3.5.0", "lru-cache": "^4.1.1", "graceful-fs": "^4.1.11", "mississippi": "^1.3.0", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.1"}, "devDependencies": {"nyc": "^11.1.0", "tap": "^10.7.0", "chalk": "^2.0.1", "tacks": "^1.2.2", "standard": "^10.0.2", "benchmark": "^2.1.4", "cross-env": "^5.0.1", "safe-buffer": "^5.1.1", "weallbehave": "^1.2.0", "require-inject": "^1.4.2", "weallcontribute": "^1.0.8", "standard-version": "^4.2.0"}, "dist": {"shasum": "3d7cac2f179ae5523e777f74c4e956ce6686f31f", "tarball": "https://registry.npmjs.org/cacache/-/cacache-10.0.3.tgz", "fileCount": 29, "integrity": "sha512-fhy5oPxjgI/pfsSPhlqCFtvuM/lvRnD0T7/fCFoXNmR6/1IKMXsjk2UlNbrOkACbm3e9Xb2TfuDZ4d6lyqHXSQ==", "signatures": [{"sig": "MEYCIQCy6/R4y4NitEiKzPjwHMSET0qfhkWMHdEFm5Fzrb3iQQIhAM7S78j3yDTXc9EOGkevYfjSnAWv/4/WvwJivAC0ENcM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101928}}, "10.0.4": {"name": "cacache", "version": "10.0.4", "dependencies": {"glob": "^7.1.2", "ssri": "^5.2.4", "y18n": "^4.0.0", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.2", "bluebird": "^3.5.1", "lru-cache": "^4.1.1", "graceful-fs": "^4.1.11", "mississippi": "^2.0.0", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.1"}, "devDependencies": {"nyc": "^11.4.1", "tap": "^11.1.0", "chalk": "^2.3.1", "tacks": "^1.2.2", "standard": "^10.0.3", "benchmark": "^2.1.4", "cross-env": "^5.1.3", "safe-buffer": "^5.1.1", "weallbehave": "^1.2.0", "require-inject": "^1.4.2", "weallcontribute": "^1.0.8", "standard-version": "^4.3.0"}, "dist": {"shasum": "6452367999eff9d4188aefd9a14e9d7c6a263460", "tarball": "https://registry.npmjs.org/cacache/-/cacache-10.0.4.tgz", "fileCount": 29, "integrity": "sha512-Dph0MzuH+rTQzGPNT9fAnrPmMmjKfST6trxJeK7NQuHRaVw24VzPRWTmg9MpcwOVQZO0E1FBICUlFeNaKPIfHA==", "signatures": [{"sig": "MEQCIA+IIfVl6yb5zzXyNC7KT/aOu3nVf/Nea1VFdJqMKEu+AiAzordymFUt7wgSzM7DgoN3Oo6mC/rPGYTvBImcEkWThw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102024}}, "11.0.0": {"name": "cacache", "version": "11.0.0", "dependencies": {"glob": "^7.1.2", "ssri": "^5.3.0", "y18n": "^4.0.0", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.2", "bluebird": "^3.5.1", "lru-cache": "^4.1.2", "graceful-fs": "^4.1.11", "mississippi": "^3.0.0", "figgy-pudding": "^3.1.0", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.1"}, "devDependencies": {"tap": "^11.1.3", "chalk": "^2.3.2", "tacks": "^1.2.2", "standard": "^11.0.1", "benchmark": "^2.1.4", "cross-env": "^5.1.4", "safe-buffer": "^5.1.1", "weallbehave": "^1.2.0", "require-inject": "^1.4.2", "weallcontribute": "^1.0.8", "standard-version": "^4.3.0"}, "dist": {"shasum": "6b7ddb262c764cf482495ab086c69ff084385821", "tarball": "https://registry.npmjs.org/cacache/-/cacache-11.0.0.tgz", "fileCount": 29, "integrity": "sha512-pWhgsZ8GL5Boz69gJ4RPM1xiyIfB5gbB1V0P1WCYjIUDeww1zSIaM63x8R7YlRV95MxvXfxB+QVeY1YdneVaiQ==", "signatures": [{"sig": "MEYCIQDW8qJsV/3Ybo9pHq6WQIqIzDLe1uLdzRr9S3cLM5fKnAIhAOpDgpAHk6jICUyCH7evOMoe3Q9rn8yVi7+TnaML8+af", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103377}}, "11.0.1": {"name": "cacache", "version": "11.0.1", "dependencies": {"glob": "^7.1.2", "ssri": "^6.0.0", "y18n": "^4.0.0", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.2", "bluebird": "^3.5.1", "lru-cache": "^4.1.2", "graceful-fs": "^4.1.11", "mississippi": "^3.0.0", "figgy-pudding": "^3.1.0", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.1"}, "devDependencies": {"tap": "^11.1.3", "chalk": "^2.3.2", "tacks": "^1.2.2", "standard": "^11.0.1", "benchmark": "^2.1.4", "cross-env": "^5.1.4", "safe-buffer": "^5.1.1", "weallbehave": "^1.2.0", "require-inject": "^1.4.2", "weallcontribute": "^1.0.8", "standard-version": "^4.3.0"}, "dist": {"shasum": "63cde88b51aa5f50741e34833c9d0048a138d1dd", "tarball": "https://registry.npmjs.org/cacache/-/cacache-11.0.1.tgz", "fileCount": 29, "integrity": "sha512-s5YA8Lva1PF76kHDquIPW1N0YJXNFiItwrrDXAn8vvunOv/VNXOR1LtQYgPBRpaweIX2xSaBpqIXCYeOTZfHSQ==", "signatures": [{"sig": "MEQCIH7racLpqvM9tiQFEqe8pseD/jfTYQg4nsgtJjVwzvcSAiBazvl/AsP1J4jApdbWMr4n9WF7EZu939cvM6Hc1LTmKg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103486}}, "11.0.2": {"name": "cacache", "version": "11.0.2", "dependencies": {"glob": "^7.1.2", "ssri": "^6.0.0", "y18n": "^4.0.0", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.2", "bluebird": "^3.5.1", "lru-cache": "^4.1.2", "graceful-fs": "^4.1.11", "mississippi": "^3.0.0", "figgy-pudding": "^3.1.0", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.1"}, "devDependencies": {"tap": "^11.1.3", "chalk": "^2.3.2", "tacks": "^1.2.2", "standard": "^11.0.1", "benchmark": "^2.1.4", "cross-env": "^5.1.4", "safe-buffer": "^5.1.1", "weallbehave": "^1.2.0", "require-inject": "^1.4.2", "weallcontribute": "^1.0.8", "standard-version": "^4.3.0"}, "dist": {"shasum": "ff30541a05302200108a759e660e30786f788764", "tarball": "https://registry.npmjs.org/cacache/-/cacache-11.0.2.tgz", "fileCount": 29, "integrity": "sha512-hMiz7LN4w8sdfmKsvNs80ao/vf2JCGWWdpu95JyY90AJZRbZJmgE71dCefRiNf8OCqiZQDcUBfYiLlUNu4/j5A==", "signatures": [{"sig": "MEQCIG4G3pWFdPAXChXJvL+TGkPm7uUb5c++WiN+VPkJNp14AiA5e0IjYZBRbAZT0akoHj+sQungHOqPeUJorPG4ngWyOA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103864, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8KCeCRA9TVsSAnZWagAARwIP/iLX3k/nIgihaXX1urCX\nq+kBkZ5pjhGAAH22PhKfhJcMmLStLEzhxQMnFDf+9HowhMHM8vccMZuZbWVW\n6j6xtdalp2L+n9TjJZIGZk5BRLF3Tc3n93G+f8cpbxVvhHnYvs80dJpUMN32\npPNFFa8C3knDGaE9UtQLhsA651OzgFSHykf9lLVJ8hvgne4VpD6wTbKNLd8D\n7UxDtj3Ue+7OTZZBZMdVTgBDrUKqDJX6zUP2dZe/Gtjnc1CtbEmBxh4wRbSx\nRJSJJxVVpwZjxVvdmHXEnwqnxXJseQQP6klSxtjJglGmc5izGFrQvL4CVQeZ\nxSSWyEn/2zmNAvbTvVhM5xsr81hWLWyEc7MbOxss1HjiDe/FJgsCRTt/FE8R\nO7aaT2r9sABBfT2YLr9b1VAcqoiL8H0ATLwe/1RMFJbLtuWkBLBpOe1Cuz5F\nDBL9JwG8rSmCfUMjlFwFXK9YFwEfV/PHz2dtGc7QN0qwyXfmPbl7jmFMJgzh\npa6Z4i72r/LGh1gI4VPQSjcYjTbRWsH9nPPjt7iY6WOF2ZEEXMQqOHvG79Mt\nFaNzzaUiPlfhTpI1iHqAS8at117kpmadxO3058jln68NnJ9CdDZZ8+V3HX8K\ntyjXxV3DrwLvCcd111kPmbE5+KSe9nfXAEL6OpTStkK6q7W9LsW/94QX9vhq\n82+u\r\n=RUWS\r\n-----END PGP SIGNATURE-----\r\n"}}, "11.0.3": {"name": "cacache", "version": "11.0.3", "dependencies": {"glob": "^7.1.2", "ssri": "^6.0.0", "y18n": "^4.0.0", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.2", "bluebird": "^3.5.1", "lru-cache": "^4.1.3", "graceful-fs": "^4.1.11", "mississippi": "^3.0.0", "figgy-pudding": "^3.1.0", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.1"}, "devDependencies": {"tap": "^12.0.1", "chalk": "^2.3.2", "tacks": "^1.2.7", "standard": "^11.0.1", "benchmark": "^2.1.4", "cross-env": "^5.1.4", "weallbehave": "^1.2.0", "require-inject": "^1.4.2", "weallcontribute": "^1.0.8", "standard-version": "^4.4.0"}, "dist": {"shasum": "632afc1c48c17cf4f37fb0044f8da184b475426d", "tarball": "https://registry.npmjs.org/cacache/-/cacache-11.0.3.tgz", "fileCount": 29, "integrity": "sha512-PKg49kjNaPFvzwcIo4mo46av7uKDnECAeyNDp3R+WTohi+BeQjPC7zcKxx7P1lyMySNBY5FeGD8Ys38VtBGcTg==", "signatures": [{"sig": "MEUCIFgXeRYJjr+So5VXEbtU7A93iuy+VfguNTvpgJoDU/HCAiEAnF1TyJtb0kZXzKF8D6//9y+BpcE/lXkNWGa3VJxGvXw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104392, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbYhgPCRA9TVsSAnZWagAAdY0P/38vCfVgefLo504o/7Sf\njknTMGrBZcxqaV6+jCdky8jz5Nje9URKhrlUyYme7ZE2rzc14N1kFvul3df1\n8NedG4rNcNiJxMpc4k7PN6Iv0sC8+uoZ38TAQpYH2brSuOEAkILDVFpAKXAA\nmoNuPs/bZSJKzNIe7gzVpIbTZaZLFLjK+QsXIa+g8/YgEDOM4BqI01A70nzG\n39gieFaqYv3EiBcSq8qFdkz547wtheGSY359XI4kYhnByE95EJ5PWOtOqxcY\nXZHycgWJEBWzh+zRXzISq3ED3cONWwmbneB9pMb4qW9kwxzsYuJ8EH7GxLyb\nvRxCKtE4qBY4CTJg/vVsfMzF/nKquRHDJKXMtsISoLi9Nm2QUsnrzJuCOT8f\nqxc0jRTEth/k5hFvbj4KXQNPokVT/IoZvvo+oaTfts+39Eum3pfs45Yfa+IN\nl/w8I3dWYPdDi0r4PIl3nqVKh5Og1lJsbc5NNnmZyBDMM4XguaTGyXpUbGL+\nRE5s0jK5QIbQUk8nSqNLlB4xTu0IUGpLBejy04drmBl6aHfmQNxPPZb3Fquh\nyG4jnx5lGPamsSonjtlH8odWZkhiTto8zlsG6WqvyE7RCMyEOZ6YYbU/eine\n7jlvSMoA/Jk04ycXJ1iOjM4rULA9ed6Ssv0yW+BwqwUg7ikYMFnAxHvjevmB\ntrNZ\r\n=m+e7\r\n-----END PGP SIGNATURE-----\r\n"}}, "11.1.0": {"name": "cacache", "version": "11.1.0", "dependencies": {"glob": "^7.1.2", "ssri": "^6.0.0", "y18n": "^4.0.0", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.2", "bluebird": "^3.5.1", "lru-cache": "^4.1.3", "graceful-fs": "^4.1.11", "mississippi": "^3.0.0", "figgy-pudding": "^3.1.0", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.1"}, "devDependencies": {"tap": "^12.0.1", "chalk": "^2.3.2", "tacks": "^1.2.7", "standard": "^11.0.1", "benchmark": "^2.1.4", "cross-env": "^5.1.4", "weallbehave": "^1.2.0", "require-inject": "^1.4.2", "weallcontribute": "^1.0.8", "standard-version": "^4.4.0"}, "dist": {"shasum": "3d76dbc2e9da413acaad2557051960a4dad3e1a4", "tarball": "https://registry.npmjs.org/cacache/-/cacache-11.1.0.tgz", "fileCount": 29, "integrity": "sha512-wFLexxfPdlvoUlpHIaU4y4Vm+Im/otOPCg1ov5g9/HRfUhVA8GpDdQL66SWBgRpgNC+5ebMT1Vr1RyPaFrJVqw==", "signatures": [{"sig": "MEUCIB1UfkkX8dJt92a2Bf+pW/TZrMEg9TIUfAjH7h3iEmNbAiEA52b3OfnnJzt5s0PdZGUp+Dt/MosyBl08XRnIUAKAqk0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105687, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbYi/bCRA9TVsSAnZWagAA3BkP/Rjumg5NF0KdlOtimiEu\nDy5v3o8DZMvpQKi/ULuvK3qY0TCCR9GJc3HKk3FCrGWUp53AwJSeBcEFM3W6\nc15/X+97adBqNv+MgkcmK1bs7V56buBfEAoIYGNfhjJu5WuYt4nHR9USdwrD\nSrBl2nR6S8iRgrlOMtSH29c5bwjxvx43omHloxUG3+WSnQnyDZ45r99izi/a\nkxouEmWMAWYPSIEsEoDtlc/VegbED1OcHyJ6EG8GRBZt0PK4coOJMnpaZiUj\npjbdTM/35jyRoKUNK22LHDe6FPFp8iD8eZTORwnMinOIcjUPcE4qo9hxOLSO\n1ucjaFWyAN4xOXoVGoijq/2heGbDMf9SGGFpeBfltiMLhZmA8NakjtrU0wbc\nlcKkTOcKtowhGlkE2bCzs6kZCbhG5NyLwJZWBd4zFN2kk4ttd6u9tK8W99LC\nCmg26BWsDhic/aleju7UIwNq8+c9uBqBBpVxJTeYU7gGrfOdZAoCsx5RjmzX\nDWXyA2h42NdLSl2t6IeFIQV4Xq1ZMB1b3/vGtA5AquFubIYe9lnxlEUjAqF5\n7L4Y6bqVB05bT7qfpq0//yesXQTeod8Zpmjy/1JYTT1U4Vxqtk6j0fjqKTmK\ngjgpHqMLzXML88eRkeyNw4BNmom97GLhpeeuo3bFJ7QqI/rTWF+85y1Tpqsa\n7dRn\r\n=yCMw\r\n-----END PGP SIGNATURE-----\r\n"}}, "11.2.0": {"name": "cacache", "version": "11.2.0", "dependencies": {"glob": "^7.1.2", "ssri": "^6.0.0", "y18n": "^4.0.0", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.2", "bluebird": "^3.5.1", "lru-cache": "^4.1.3", "graceful-fs": "^4.1.11", "mississippi": "^3.0.0", "figgy-pudding": "^3.1.0", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.1"}, "devDependencies": {"tap": "^12.0.1", "chalk": "^2.3.2", "tacks": "^1.2.7", "standard": "^11.0.1", "benchmark": "^2.1.4", "cross-env": "^5.1.4", "weallbehave": "^1.2.0", "require-inject": "^1.4.2", "weallcontribute": "^1.0.8", "standard-version": "^4.4.0"}, "dist": {"shasum": "617bdc0b02844af56310e411c0878941d5739965", "tarball": "https://registry.npmjs.org/cacache/-/cacache-11.2.0.tgz", "fileCount": 29, "integrity": "sha512-IFWl6lfK6wSeYCHUXh+N1lY72UDrpyrYQJNIVQf48paDuWbv5RbAtJYf/4gUQFObTCHZwdZ5sI8Iw7nqwP6nlQ==", "signatures": [{"sig": "MEYCIQCX0l+qlULIvxTgs35oY1EGzu6fYKuBgzjYy4XJrxQFSAIhALVEmgJ/AKqDm28THw6n1z8ZftbFs9H0k8FH6iGVRHNR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106685, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbaj3sCRA9TVsSAnZWagAAwHsP/j64ncfiQL/IKCQb45Zx\nYRD43mMsd2BDc0k+qCrOlIw2x3jA+eTh9BHjvMBE0BuMuWAjwV4wXKXjBIpu\nwBhzYxwoCEsQbksV7oTAMS1PnQiOQfuZEs2zLfRIxQ8RbeGuynbE6HIKQSL8\nedyjMvsT3rMdY2rFvClYdLxx7qwZJYdL/6GXi8K0+F4Lxe04sMkKqNvwD2UF\nL2dPeZ3OcflO+piFJTZ4/8Yrb8fwR22D0zSiknQEI9T/ojYc7WJWetIPTija\n1IS4cAww4b29oWDY7SUS3yx1+09vPPydioZSvtboIfHqjo6Ahc0QQg3e97+d\nxcS5YJdpANFA6SQHK8gnlkfWZ+AX7eVowzWG17qYqq9XEgo9RcUoKvXS3AtO\nA7HHAkr6ylzkg8JF3xLT/gtyAltAwpIYis9wRb4Vij/clML5vVHjxNb2Vmab\n3TN7JubsSDxrPsdZ8RwBW5VuwqZS5nNo/WTLrUOYQHjSP0zjek8lmq9xAP07\npHgMEAcGKZnMzGkBwBSzEsrCWKLGo2WkkogubRJClL1VAeofLcTT/VpiNLFR\n6fs8oliVIA3KmOV45PPW0LJHt/Cyj0F9jO1BlMZIAn4XSIWUF7IyDjtRqVYA\nh5Ia8Lksurz9SC2b7c8JfOPCHt7gP+RRJyzVJmpbVNO6LGi6UosvT+6SabZh\nGb91\r\n=GisX\r\n-----END PGP SIGNATURE-----\r\n"}}, "11.3.0": {"name": "cacache", "version": "11.3.0", "dependencies": {"glob": "^7.1.2", "ssri": "^6.0.0", "y18n": "^4.0.0", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.2", "bluebird": "^3.5.1", "lru-cache": "^4.1.3", "graceful-fs": "^4.1.11", "mississippi": "^3.0.0", "figgy-pudding": "^3.1.0", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.1"}, "devDependencies": {"tap": "^12.0.1", "chalk": "^2.3.2", "tacks": "^1.2.7", "standard": "^11.0.1", "benchmark": "^2.1.4", "cross-env": "^5.1.4", "weallbehave": "^1.2.0", "require-inject": "^1.4.2", "weallcontribute": "^1.0.8", "standard-version": "^4.4.0"}, "dist": {"shasum": "c37d4c12e3114064becf8904adba039a5befd4fd", "tarball": "https://registry.npmjs.org/cacache/-/cacache-11.3.0.tgz", "fileCount": 29, "integrity": "sha512-6Af/h56f+GXGAuxfutTZGxOofff+PfaZ3K0XlXjMAzS8HHijzNYySP8zHrJ0vniSzd4wrMgwOHegWh695pHSRA==", "signatures": [{"sig": "MEQCIBhda2iqAGOGj0xxcpC3DpJz95I3nRBM2RLHAeNGrjwyAiA0XbrHcMKKMWBEsaAcB2Yvtck/xVDRuXCRitimJjOxZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 110919, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb38VpCRA9TVsSAnZWagAAwjgP/1hBBUmdSNN1Z7uLlb2K\nDxWWZvH9oPsaNrU++q7PF52VgvszUNpau6lj8E33Wi4O71skDVakfEBZG5WV\nCo7Hm97nWZubL5VD/3Eb/jMe+T4PAyI7lBSefG4sDkMgvbztk7gS5BvAf6ix\nsBZAQb/PIS2f+2tQHiDrxHf1+/ut19EyYX01hOo0IZ70rhZmtVqXQhjtBpSx\nK5Ir++WREaNTRMiRVoAdqVpMl6783NKBErtFziuUm6GFA+JhOjmPm69Yhj9z\nNI3umXFqiLLfA/GdlIJIYEurcCnDqGAxcUDv5aItgXyWVK/prT4V2gPmJkOM\nuzGpXA5Kfh/KO+fjOKh0vMkNlqZROc1h4K6EYKZKeq0xlSL1JadBfTaZOIMS\n2WRhMzFjlZNy43iAtDljJIU41qtbU/fUgnGRA0f784iXAY4XDsYveKyNqOMn\nBE5fXDSuPTEg1NyvOaOYomnwfjTPa4J1kA2bM1BokrnZ5O6XfVbR6/4DvZ36\nJ5peve0oaJ5lJTC/BK5HM301/AFETOQ3H8zmUFTROModACV6l25elIyODFON\n8zMNocTqh5ClN73L4MwvxOkFFl+zmKKJ5iZFs2Eo3cukgwL3ZTOWglk3WsU9\niVGjDmd9LGjwg+vPaY/D8UxXgb4dVKajpOwepiBPhl129uXtFusaHlsiIcSs\nzYQ3\r\n=E2jl\r\n-----END PGP SIGNATURE-----\r\n"}}, "11.3.1": {"name": "cacache", "version": "11.3.1", "dependencies": {"glob": "^7.1.2", "ssri": "^6.0.0", "y18n": "^4.0.0", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.2", "bluebird": "^3.5.1", "lru-cache": "^4.1.3", "graceful-fs": "^4.1.11", "mississippi": "^3.0.0", "figgy-pudding": "^3.1.0", "unique-filename": "^1.1.0", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.1"}, "devDependencies": {"tap": "^12.0.1", "chalk": "^2.3.2", "tacks": "^1.2.7", "standard": "^11.0.1", "benchmark": "^2.1.4", "cross-env": "^5.1.4", "weallbehave": "^1.2.0", "require-inject": "^1.4.2", "weallcontribute": "^1.0.8", "standard-version": "^4.4.0"}, "dist": {"shasum": "d09d25f6c4aca7a6d305d141ae332613aa1d515f", "tarball": "https://registry.npmjs.org/cacache/-/cacache-11.3.1.tgz", "fileCount": 29, "integrity": "sha512-2PEw4cRRDu+iQvBTTuttQifacYjLPhET+SYO/gEFMy8uhi+jlJREDAjSF5FWSdV/Aw5h18caHA7vMTw2c+wDzA==", "signatures": [{"sig": "MEUCIEfdldQ1dQClW5wsI8h6c3HwBlln5CH1Wg+x6F0N1B0/AiEA73Ax84RVK3u+lcOK4F+A4+hdROjnUloKHKOXM/gZd3A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111301, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb4LNYCRA9TVsSAnZWagAAKwQP/1WNsqOngJOmHit1rRAc\ngDtvOXzeEn42MtKAQFoZOJT7zt8xNBIg83PMmRXZRtWGGkQvo07pfB4qanoE\nEvze/fDFHi/jvcToD/brYsBJZ/IMXWZs8ukx7vMj/ZdqIH6TGk0dvbOi2TP4\n1kvK9FLcp4esdRQ1so/SAvuOA//fZWaNv+JX6KvMRlLM1tPjujHV+DIRCihQ\nE1BhSYP7c0vmn2telUTfzAPBGR9CFT0h3vk5KMmZs3KvFsRRFs1+MCA+XHku\nw4KnM5zhAgPS6N83DQm01aNMDYp7f+yVmG6oj75Q7tJ+XHMotAbmz7CwwWAm\nEcUvYcLOo71e6oQESuy+VxHff5X6Rk2K3LrOi551cBqOC1u1VEna4g947imD\nhtcEURJZtkLhRARiUkyMiKaRPPOgYiOfvq289WbjroWMR3XdajOwclyn9Cpc\nLwPPp877gN/s60pkQqar5K0mcFjeUF7aLoiV7NRt2Gc/PepFg0VThQ6eitjG\nAQU+pk+a+/W3DVCqzFu98YMUoQvOMz2vViZ1irDiSC8dooH5GK8hitOQ+nHY\nKz2Xjs5uQOFZbCDHQf5pYTVX9WL4/2T4Am1Knz9uscyZGgQsgVqeX3wECkN9\nqLT5bu1KV+CoDRy6VcRTp/8ar6LE6FCR3uJZ3K2Ix0y5oxoTCPzdrY6jvyhW\nK8e5\r\n=1Wit\r\n-----END PGP SIGNATURE-----\r\n"}}, "11.3.2": {"name": "cacache", "version": "11.3.2", "dependencies": {"glob": "^7.1.3", "ssri": "^6.0.1", "y18n": "^4.0.0", "chownr": "^1.1.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.2", "bluebird": "^3.5.3", "lru-cache": "^5.1.1", "graceful-fs": "^4.1.15", "mississippi": "^3.0.0", "figgy-pudding": "^3.5.1", "unique-filename": "^1.1.1", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.1"}, "devDependencies": {"tap": "^12.1.1", "chalk": "^2.3.2", "tacks": "^1.2.7", "standard": "^11.0.1", "benchmark": "^2.1.4", "cross-env": "^5.1.4", "weallbehave": "^1.2.0", "require-inject": "^1.4.2", "weallcontribute": "^1.0.8", "standard-version": "^4.4.0"}, "dist": {"shasum": "2d81e308e3d258ca38125b676b98b2ac9ce69bfa", "tarball": "https://registry.npmjs.org/cacache/-/cacache-11.3.2.tgz", "fileCount": 29, "integrity": "sha512-E0zP4EPGDOaT2chM08Als91eYnf8Z+eH1awwwVsngUmgppfM5jjJ8l3z5vO5p5w/I3LsiXawb1sW0VY65pQABg==", "signatures": [{"sig": "MEUCIBlYmidt8JVY+cUfh6y3OUgTaYZSvUStuN9q7PKnQbf8AiEAv0k3mqZUHH/Xcd/G5GvKIJlnxKPYXgs6SKswDtJ5aZ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111544, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcHTf1CRA9TVsSAnZWagAAMtEQAJTVEcbY8QkjMVhIBBNe\nV7nbRouS62lNStW78eyOqVaakJRH/1w5oe0jzgTm27ItNM1hQae1MJyihDkg\ngs1SE0A9bsUA5IRUyGSmISa0LA/NSMLIYBO3XvLKpY+gLQ7D5enXLHWKbDJ3\nF6x3wpresZZCdynvYtiDz234ofrotdvU/xaIxrxv0laoaJElzPhuRgUShLNe\nYIQxxzF9cNYIYh3he/tiuj90NKYtbOsnHfStrxFneRqx3ynD77l/vdk9tEeM\n4THtgv538k6yN3Nd7ZqSNNTEPEvmIl/ibp5Hb96kiMKctUAsNs907k8LPW/5\nf4c/Jh+SeiIiYR/AjkatC4zWcfhk8Glxac/lNUr3ehwXi3FMTAIp0J0JHpXz\noGUhC1LNDw25Ojjm1EUthBOxLS3HQXMWG/E2Ec4I4QJXE3tYWZs5cch+SvdJ\nLadUCtsewwzMfJocPfGVyh7ujJbKeJ6MT1Lc11TQ9BBpCtjPF4skMK8btWRY\nYLspAHyDEzi7Awu3symh/t9+/4P4fjFchJ2ahGeT64QtoPomezxh8mFhKhnr\niUufymVJWs4O0STKTSpVhBk5Rb/9RVR+RLlss/xuGnBlRuTb7kVCfWCzSQU8\nZKPMIqeC6ZoPCIZ8y09XUrf/RQmMZSwws/PWBSktnLTnMozKyQv4biIvfBNl\nGPMM\r\n=/TH5\r\n-----END PGP SIGNATURE-----\r\n"}}, "11.3.3": {"name": "cacache", "version": "11.3.3", "dependencies": {"glob": "^7.1.4", "ssri": "^6.0.1", "y18n": "^4.0.0", "chownr": "^1.1.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.3", "bluebird": "^3.5.5", "lru-cache": "^5.1.1", "graceful-fs": "^4.1.15", "mississippi": "^3.0.0", "figgy-pudding": "^3.5.1", "unique-filename": "^1.1.1", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.1"}, "devDependencies": {"tap": "^12.7.0", "chalk": "^2.4.2", "tacks": "^1.3.0", "standard": "^12.0.1", "benchmark": "^2.1.4", "cross-env": "^5.1.4", "weallbehave": "^1.2.0", "require-inject": "^1.4.4", "weallcontribute": "^1.0.9", "standard-version": "^6.0.1"}, "dist": {"shasum": "8bd29df8c6a718a6ebd2d010da4d7972ae3bbadc", "tarball": "https://registry.npmjs.org/cacache/-/cacache-11.3.3.tgz", "fileCount": 29, "integrity": "sha512-p8WcneCytvzPxhDvYp31PD039vi77I12W+/KfR9S8AZbaiARFBCpsPJS+9uhWfeBfeAtW7o/4vt3MUqLkbY6nA==", "signatures": [{"sig": "MEYCIQDCOHZGE9owl15YmfLcD8kd1IZOJpyApyjo/yNgcecKbwIhAOWAqlAwBf5QzTwf2A6WOm/4ucoIh6eV6/vavELCLwxZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112357, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdB7xwCRA9TVsSAnZWagAALpIP/0hXZCajeHjjzLrBUEV7\n7EUQTQEZym84XZx0B9mgjcr+Ga/udYJjFX2s+omXnxn47sEDh3aN0AHW5n17\nBVon5YDJo8DJRR2QGBPUE/Dr/nDCPZT6Crgk3z59W/iH4BgnlIOCM4N1xZrI\neiQmlyoFw/bDCDMPNw2a3nvYHLBpYMGOrJrGz0ZxklB3WWJbh5eFsO7QNdJN\npTECf/2TU7AtwnJuo7bcD2nyKPW9z04ijV5JnCZKW3GU6yM7KnfvSb4rghep\nL2K1hhmkqt1YlztHXC/jXo6fNiZid37vcQDPVa7muKZXWOEDQexl4DO0WP1x\nAzo3VdObNRInfQp2mmDTnEfKScfEI81zcWMtfq4izWLKLOMl3QkUKGDDCpq9\nl0Gip5INniQ0cbf3Mu5XuBfzQ+f8n16vgIhkwEQj1+vUXhM6wus8td2wyCDo\nk0in7zPMhpNZmy5EI14rZtJuFQ3m9nSJN8SVi/XDcKYqXZT4/FmQZ+2m/WQ4\nzjJxN4Fh/rJFK+3Mf+itcQApjwTQlBshTkcltEeeAXsRKIGNlWApxZaGOkqm\nvxUmctFA3R7avdsmXIIj/AtwHRbGQSpJZBDR8HmuiHFkRu/JK5mmI7ohMewZ\nNkszQFCfcwYGou3dEw8tJVv0/PMUBhWCBiUQJYqdK/+t6jFKhP6dJFd5M7jH\nofMB\r\n=MkeU\r\n-----END PGP SIGNATURE-----\r\n"}}, "12.0.0": {"name": "cacache", "version": "12.0.0", "dependencies": {"glob": "^7.1.4", "ssri": "^6.0.1", "y18n": "^4.0.0", "chownr": "^1.1.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.3", "bluebird": "^3.5.5", "lru-cache": "^5.1.1", "graceful-fs": "^4.1.15", "mississippi": "^3.0.0", "figgy-pudding": "^3.5.1", "unique-filename": "^1.1.1", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.1"}, "devDependencies": {"tap": "^12.7.0", "chalk": "^2.4.2", "tacks": "^1.3.0", "standard": "^12.0.1", "benchmark": "^2.1.4", "cross-env": "^5.1.4", "weallbehave": "^1.2.0", "require-inject": "^1.4.4", "weallcontribute": "^1.0.9", "standard-version": "^6.0.1"}, "dist": {"shasum": "1ed91cc306312a53ad688b1563ce4c416faec564", "tarball": "https://registry.npmjs.org/cacache/-/cacache-12.0.0.tgz", "fileCount": 31, "integrity": "sha512-0baf1FhCp16LhN+xDJsOrSiaPDCTD3JegZptVmLDoEbFcT5aT+BeFGt3wcDU3olCP5tpTCXU5sv0+TsKWT9WGQ==", "signatures": [{"sig": "MEUCIQDel1ioqGUmZhr6d7IP22Zmda/WAXhhqd+4AvgOFUB86gIgC+rYU94wczNha5h2hNXKEcIfptyfm54ekeodCXwR0TQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 133904, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdLQxECRA9TVsSAnZWagAAnlAP/3tLeWfkXm2trXO+9nFC\nCKLz1Yum8RFhj9BlBNjC/JOWiwb0+s95R+FENdfg8mbuiyBc5SY/HV78C+Dc\ngYvrkg1piAYOna9TK+DdiQ+WafhWQohyq3X6QxatyUvsMaE3yQzo3vYBQmsA\nuZ6U7A3qPZuRaf8A9ryCaSk//EQx8P6eJqY1RmJoSC3axi3CMo6DlxzWa2mA\nEWQXxdMKD1+elGwtjkuO6XFiV6VYX/q1VneXAe5ONpcEtz1N3k77hs0a+YXi\nFCxJ6putj1Z/cIaaRxkVE7woXhTkeB2DqFCPa3DRuDTBrofHlQuH57cXcdAJ\nTwUPMnxwDjn3F8oiRcJuN/bQrrqK4Uuu0nP7SCpmdDudiUhRbTbMO9rjl5b9\nqH0PlECVEO4b6VR4jKQhqQncBRmru4eMaqpCvN9u+aSBEAEZBvQIHoNcR8GJ\nZ2MD+gocXjYctRkKqMTS8V7CuCy64hifepAO64gn/tEpy6y0YOA0I2md/Ig0\nhCs5LaQKKQ5Yit7gQBk8MUZrbm30Jk8k4PdcAzOq7zBwrK6a/Rr+pbnA8sKJ\nAKq1Q9NVbAW6zVHBOcD/2efSwkVgoFD3R/sg17TLEM+JKw9wCNj7GpxrIVMd\nkMhhPkXxfWrItB232gEzo6m7KJDHiUu9lFfpvqTbXf3sw9mnrPUACMZnqdnC\nl28U\r\n=Z5zo\r\n-----END PGP SIGNATURE-----\r\n"}}, "12.0.1": {"name": "cacache", "version": "12.0.1", "dependencies": {"glob": "^7.1.4", "ssri": "^6.0.1", "y18n": "^4.0.0", "chownr": "^1.1.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.3", "bluebird": "^3.5.5", "lru-cache": "^5.1.1", "graceful-fs": "^4.1.15", "mississippi": "^3.0.0", "figgy-pudding": "^3.5.1", "unique-filename": "^1.1.1", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.1", "@npmcli/infer-owner": "^1.0.1"}, "devDependencies": {"tap": "^12.7.0", "chalk": "^2.4.2", "tacks": "^1.3.0", "standard": "^12.0.1", "benchmark": "^2.1.4", "cross-env": "^5.1.4", "weallbehave": "^1.2.0", "require-inject": "^1.4.4", "weallcontribute": "^1.0.9", "standard-version": "^6.0.1"}, "dist": {"shasum": "527af5682b34bebdcde549ff267a4a46f2944857", "tarball": "https://registry.npmjs.org/cacache/-/cacache-12.0.1.tgz", "fileCount": 29, "integrity": "sha512-tJtxCdrtecQEmAkTCt8qKZQWANckuD6T7d0EhVUxh9lbggCedG/UoGCEyo5+/vgbpwhEQJ/FHgREsgoObex8Pg==", "signatures": [{"sig": "MEYCIQDPbMxAICI4tSgXAGvfetS+4Te+BQ8hHXvBAI2QiyeSjQIhANUoZeeEHKNSeF5y9+zxV+vppEvK8Ar/KdlwIdvBxnsw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 115180, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdMkWDCRA9TVsSAnZWagAAUNAQAJ24oeKldNsQiWWNhQqq\n3e2TfwG6w3Xd0g3J4RuU68mPTxXONa8wm6bbmtIropKpSqPVYaNSjm31LNsM\nvm4rnHfbN2iLSys8qBgtrSoDg4qWiC9s9oMhm3HHpuH83nE2eYBy4yfOgVO9\nxKwPfSOE6AJvXvDP1CgETSCIw1Yr5lwu0S+KVZ9SAhK1RbExaOaGXgYtruUE\ncigDd/Nz8Yz+NL+T5EXr8bwhMOfAUetvy7y2a+ikeeUDVaXhuznAloukMyM4\nlAS7QtDA0sFME0LVhvdu1nnWB5q3+yzgTlD/2NAdyVbkP0tLt+T2/1XTe6aS\njCn/AdjRXJPXW7PuTPwr+J1xwwM/MbMW3D9TQ+WEZ+zpzbFMKGyvDLqrszVT\n+TUCh66DXZmnIzl+Gwc8i7wFdpP/zZC6ONvarjyjtDGljpOaytGxc8LUppES\n3zSrFdsPxav+HpOVt2RbesAZ2mhFcNffjSznu24D5pGyIvDDRJLOGsd9dJ7b\nZFNPACU0Jg5LXHigMYe62NIiQBBHqAd61FYU3A6GzAFIWHJKU8GLg9t/I1un\nwWgW9++lfev0R/Ed10V1W8MZZTkxtje3EUgQqjU/Iq/pyhjDsDbg/Je1q9ye\neEJAnMogGD3/N/2XLPqv/w6OIgsImQe4xdBzjyaJ9Ynbjxozm482jg7cZIgg\nSf/q\r\n=QeUk\r\n-----END PGP SIGNATURE-----\r\n"}}, "12.0.2": {"name": "cacache", "version": "12.0.2", "dependencies": {"glob": "^7.1.4", "ssri": "^6.0.1", "y18n": "^4.0.0", "chownr": "^1.1.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.3", "bluebird": "^3.5.5", "lru-cache": "^5.1.1", "graceful-fs": "^4.1.15", "infer-owner": "^1.0.3", "mississippi": "^3.0.0", "figgy-pudding": "^3.5.1", "unique-filename": "^1.1.1", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.1"}, "devDependencies": {"tap": "^12.7.0", "chalk": "^2.4.2", "tacks": "^1.3.0", "standard": "^12.0.1", "benchmark": "^2.1.4", "cross-env": "^5.1.4", "weallbehave": "^1.2.0", "require-inject": "^1.4.4", "weallcontribute": "^1.0.9", "standard-version": "^6.0.1"}, "dist": {"shasum": "8db03205e36089a3df6954c66ce92541441ac46c", "tarball": "https://registry.npmjs.org/cacache/-/cacache-12.0.2.tgz", "fileCount": 29, "integrity": "sha512-ifKgxH2CKhJEg6tNdAwziu6Q33EvuG26tYcda6PT3WKisZcYDXsnEdnRv67Po3yCzFfaSoMjGZzJyD2c3DT1dg==", "signatures": [{"sig": "MEUCIQDxDUMpvAA/rp1kSjkjGb+8OMyWZKYGCVyH5WrAa1cW1gIgLXI6A17g4SxuhaCVYQqS393OzC0bIJNZMqa+JrPXiBk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 115251, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdMk4MCRA9TVsSAnZWagAARK8QAJADGsGGVtuD7catXO/a\nlqb285/vpwd9ZsHGUepNeMq6p53Hbek47jukUwlMR+e99dbroWfiJ3R/tnLb\nrRcUXzovB7mmmac3Br9JtVBDHKt6G6W/HexkPGdCXDE3lWk+xKax8QUgif4g\nQBc5u6IYnCDEEZRSMMVgLbUr0Cj5bzSvXewk2W9eAoAfjSkQL4HVKwmRUIKd\nKdlt2xQ3QbOYKVsd6jO3tqRzeNyVDwx9W/e6y+SPDYZzeqy2HGsLOguqAr+1\n8BhKDEtqh7hJlMDqY2EMSNokAz0r2Jz7L0N1mf40hfQZibx2dWonmiP8xPFC\nm5j2Zo4tQJlthB+S/UCcLQlgKbIHg7Ww92ZEhZYJFCNo64Pc5vUF0TLJZ6XE\nmtRzT6Ze/vO/08o9RmIyadEUfDOmY/i9larInPNDBkgwdc6UMKlevW975MI1\nLRbogcEWVVJ8NseOLpi0Nzg3IyXva7H+J6v03LvU/u+gcvzjUtSWuEdFxln9\nC2MpQ6BAktlMdz0UA2kljL1E9cvykFBBzEznAChUpmWSqUtBvAtTLWoJpBE/\nERcjOynd7v70SMyUmQ8OsflydFO6dEEHR5U2A4QMlWVaaYGarm5ViABJSo7K\nibCpB7Ef9DhFPGgqzKe/WveSaDxX7WEltT3euzKaVOrjPFrWElRWodSnziuN\nvq/X\r\n=WzUQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "12.0.3": {"name": "cacache", "version": "12.0.3", "dependencies": {"glob": "^7.1.4", "ssri": "^6.0.1", "y18n": "^4.0.0", "chownr": "^1.1.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.3", "bluebird": "^3.5.5", "lru-cache": "^5.1.1", "graceful-fs": "^4.1.15", "infer-owner": "^1.0.3", "mississippi": "^3.0.0", "figgy-pudding": "^3.5.1", "unique-filename": "^1.1.1", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.1"}, "devDependencies": {"tap": "^12.7.0", "chalk": "^2.4.2", "tacks": "^1.3.0", "standard": "^12.0.1", "benchmark": "^2.1.4", "cross-env": "^5.1.4", "weallbehave": "^1.2.0", "require-inject": "^1.4.4", "weallcontribute": "^1.0.9", "standard-version": "^6.0.1"}, "dist": {"shasum": "be99abba4e1bf5df461cd5a2c1071fc432573390", "tarball": "https://registry.npmjs.org/cacache/-/cacache-12.0.3.tgz", "fileCount": 29, "integrity": "sha512-kqdmfXEGFepesTuROHMs3MpFLWrPkSSpRqOw80RCflZXy/khxaArvFrQ7uJxSUduzAufc6G0g1VUCOZXxWavPw==", "signatures": [{"sig": "MEQCIBkXvqhIA7d1GSzl+2mo8I/lf5JECxDMERx0GPcv5tdoAiBYNKHmf17O8slJCQBm39JO51Szwh7gvVXcUMem5+xR/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 115547, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdWvksCRA9TVsSAnZWagAACsEQAJnoOZHbECu0/AKMvHME\nZspR+OhjsI8UyJa0+4bp4419RLpW/J8S5A5AgdnLXZc5+1TRXdd6oVXgqeAW\n7vswR/5SPUeljQYI8DjJELTO2dqbQRxXbeM7LaaJbQBtLMTNpzdhqNnW88/i\nDJVgUUX7hPKHVS2rRa2WbGmR5Qi92be6Q73pCdD+t1nZBc2baaPMEQzzOylk\niXM4CO96ZHLOggn95U1f2AL1RQy1pzWC/9muMpG8Yu0YIJ5JBlF60vjGTkwO\nhdrWFcy3ARZ3tjWIEIQkh2Tet/3yW1dKzk/8DnKwoJ6C+8RhUj43+fOvrOFD\nHvfk4BFZ0Dz3EW2Je83Bj0kjdvNtxZlvwH+BA6Nkq7/uNwiWP+os0A0knE0Q\njlE+Wp5LgJHAvMW95mEyMR0dtzsZ9RFM2oYDPkpLxl2LUAQ2wFQ+mqS9phIl\n3CBWb62xjgrRbh+CbqnUa44y/sU4kXFEzJs2nFj8EAZo1avF4OAsmue1Zr07\n8QKKX2JIyy9KGEhZ/rnQ5dWMJRpPjD5JzY4ejypxnxZgW9QP7h3BXnpkK/zr\nHStpZ2DH4AB8F7/Gf8cm8EcHi6c0MdlIZazJXEUgIURYkr2T0JFUlCp9b6at\nZBLn17XqPSvdewCedGvqGVOuwttsDIKz2F6iwRPXsJpx+5+zZjOQS2zKPSLu\nT60S\r\n=qek1\r\n-----END PGP SIGNATURE-----\r\n"}}, "13.0.0": {"name": "cacache", "version": "13.0.0", "dependencies": {"glob": "^7.1.4", "ssri": "^7.0.0", "p-map": "^3.0.0", "chownr": "^1.1.2", "mkdirp": "^0.5.1", "rimraf": "^2.7.1", "minipass": "^2.6.5", "lru-cache": "^5.1.1", "fs-minipass": "^1.2.7", "graceful-fs": "^4.2.2", "infer-owner": "^1.0.4", "figgy-pudding": "^3.5.1", "minipass-flush": "^1.0.3", "unique-filename": "^1.1.1", "minipass-collect": "^1.0.1", "promise-inflight": "^1.0.1", "minipass-pipeline": "^1.1.2", "move-concurrently": "^1.0.1"}, "devDependencies": {"tap": "^14.6.4", "chalk": "^2.4.2", "tacks": "^1.3.0", "standard": "^14.3.0", "benchmark": "^2.1.4", "cross-env": "^5.2.1", "weallbehave": "^1.2.0", "require-inject": "^1.4.4", "weallcontribute": "^1.0.9", "standard-version": "^7.0.0"}, "dist": {"shasum": "1797c957bcddf7bc697520920e3a284e64fc21cc", "tarball": "https://registry.npmjs.org/cacache/-/cacache-13.0.0.tgz", "fileCount": 22, "integrity": "sha512-hc9ozSyxintw3TulgdYl5q3ZMjugHYI8lE5hd1S6E1/7OwLf0vNlBdCaROlzHxE5x0lUpFx+B3iMjWmcHDRxiQ==", "signatures": [{"sig": "MEQCICUwY9WdmpYlpaIc8Ehu3TlSnVkLeqNDV7EMlHjsIW7wAiBvH/CIzKRsb4VfaRPm/HvY0dBVJdde3hqE5IXlPAo2SA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98894, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdi7oJCRA9TVsSAnZWagAAFOwP/RQ3pTGWJG1yIPEKsQRs\nRkJfdtfM71vfEFkbcVXnPZGUTh73Q2LiQVxnaKjTcdkSIVl/wnhkcMCAuElK\n/yGOey19J+JJXVwU2dmT0wV8ZkV2bh/rqALk05pSVN5M8Vo0+lPheTxZn7an\nJhJhDQaYVzi9K0Jl/cUWd5PgrxVbFHOUCiwPUZrMAvTv4uaMeqDVjiOwbGYn\nozsUslwOHlBTtoB8BWcb7em7S0K35iq/FlD129zgFfsUWOnc7tPP24ShukaO\nTGfl+8ay7dxQ7C0JncowStCrNDivBC+Zzj5tHRg9VPTV5cwSDfJfcm5VFKfo\nVBYr1Wn319tnXuOwsNZdrUH6D7Kg8nlk49lrV5Eq5vleZgzgF+7BPQX8z9Y4\nuz0dayrINZcK3eG3n0PJmlPTthPJU8nlVUMp5fGmuJSV/WhUkx9OTNEl3eMz\nALVLAF0CGdbuXtGrlygLXxX3cpl4eX/l++g8EDMztjrjezHCa7Ot+CFunPpw\nr3c4oFrVlPVpO1eW7KrKEI41rlYJVg3q3UfPI6Or12EbXQZu1lRkv9WamIEV\n2Ggr+jCplkfoBOZ6zduwJ5RqSOmkXrv8MqZREWecPhO2rOgUE7Kf3sufehwJ\niK/F+hrecwbTNSEMSmRI0giM72YQ6ymr7OJjaC+R7iC4AC6j9iVQSYRm+x05\nSs35\r\n=si4Q\r\n-----END PGP SIGNATURE-----\r\n"}}, "13.0.1": {"name": "cacache", "version": "13.0.1", "dependencies": {"glob": "^7.1.4", "ssri": "^7.0.0", "p-map": "^3.0.0", "chownr": "^1.1.2", "mkdirp": "^0.5.1", "rimraf": "^2.7.1", "minipass": "^3.0.0", "lru-cache": "^5.1.1", "fs-minipass": "^2.0.0", "graceful-fs": "^4.2.2", "infer-owner": "^1.0.4", "figgy-pudding": "^3.5.1", "minipass-flush": "^1.0.5", "unique-filename": "^1.1.1", "minipass-collect": "^1.0.2", "promise-inflight": "^1.0.1", "minipass-pipeline": "^1.2.2", "move-concurrently": "^1.0.1"}, "devDependencies": {"tap": "^14.6.9", "chalk": "^2.4.2", "tacks": "^1.3.0", "standard": "^14.3.0", "benchmark": "^2.1.4", "cross-env": "^5.2.1", "weallbehave": "^1.2.0", "require-inject": "^1.4.4", "weallcontribute": "^1.0.9", "standard-version": "^7.0.0"}, "dist": {"shasum": "a8000c21697089082f85287a1aec6e382024a71c", "tarball": "https://registry.npmjs.org/cacache/-/cacache-13.0.1.tgz", "fileCount": 22, "integrity": "sha512-5ZvAxd05HDDU+y9BVvcqYu2LLXmPnQ0hW62h32g4xBTgL/MppR4/04NHfj/ycM2y6lmTnbw6HVi+1eN0Psba6w==", "signatures": [{"sig": "MEQCIAOPOYSskXQU+4XzdLbFuOXrrd4qpMwC/1w++Sx7xZd6AiAa8osLndhocYYUjXvQDCZV3wb/u0Cw6LzeP8ZkQGyUzQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 99047, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdkm1eCRA9TVsSAnZWagAASTUP/jEBg8ZJhAowxxM44jkE\nHyrFqRUlvQaF463jFtK9zqHpCdPXxwmT6lgnPOg1OKwawt9ZYWyF1rVvV4bd\nIzR7GVqI3THIDI1g7JjlRbdItTN0412o8B0Jz+8wjMGwyaVk7uP5Lz9GWOAB\nPnMUhDcUUCK5hsEqh8JHbAJrfs8mZkrQX7HYNTYnWQEWnB7OQ8xZp/CNEioa\n7BJQ+NyEvjxbPL4UGtpROtGJ/WO2pU3hG6gdxKQ2X2DhEv2n5aRr4qMGY7//\nfTgAZfYHyfbHNQCEzMv6+dPAi0SI7e0wYTqY/eZM8Hm5BCOah63muml6+/1o\nySBrJv+j2ZOXBQLCdFGcEgYt7CQWBtwRZ39MFtOB8iK6RXkUTNmlmV3DMgLs\nyHYkxtIjc2WkrnjjcKsGFuzhug3tBpDQ26F8w9K1d0Vba9h0ULGUKDh2Lu6p\nyih3S6Hh4xB+SUgeKyeAJ08kTT6xLwGM2Ufv+Ry9hZgPXqQG3xcWCW6QFmVR\n2LolngiVqK4IeCCXATOjx45qARE4foj+1ZBksX/x6Vk4WiSA1N7dV6TxrmZ+\n3oFJM99uJ1xwfkduuTImvl1NbFAcR+KX5boaLJXgW0MfbMfFGu8A7/sN/QSo\nRb3qq48G7bE+PNNf1yxQCcxSlD9M0tf0JApNItZsjnh2tKJQ/sKWw3/jF1/X\nl3qb\r\n=0KSH\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8"}}, "14.0.0": {"name": "cacache", "version": "14.0.0", "dependencies": {"tar": "^6.0.0", "glob": "^7.1.4", "ssri": "^7.0.0", "p-map": "^3.0.0", "chownr": "^1.1.2", "mkdirp": "^1.0.3", "rimraf": "^2.7.1", "minipass": "^3.0.0", "lru-cache": "^5.1.1", "fs-minipass": "^2.0.0", "graceful-fs": "^4.2.2", "infer-owner": "^1.0.4", "figgy-pudding": "^3.5.1", "minipass-flush": "^1.0.5", "unique-filename": "^1.1.1", "minipass-collect": "^1.0.2", "promise-inflight": "^1.0.1", "minipass-pipeline": "^1.2.2", "move-concurrently": "^1.0.1"}, "devDependencies": {"tap": "^14.6.9", "chalk": "^2.4.2", "tacks": "^1.3.0", "standard": "^14.3.0", "benchmark": "^2.1.4", "cross-env": "^5.2.1", "weallbehave": "^1.2.0", "require-inject": "^1.4.4", "weallcontribute": "^1.0.9", "standard-version": "^7.0.0"}, "dist": {"shasum": "97c10fc87c4c7ee41d45e32631c26761c2687caa", "tarball": "https://registry.npmjs.org/cacache/-/cacache-14.0.0.tgz", "fileCount": 22, "integrity": "sha512-+Nr/BnA/tjAUXza9gH8F+FSP+1HvWqCKt4c95dQr4EDVJVafbzmPZpLKCkLYexs6vSd2B/1TOXrAoNnqVPfvRA==", "signatures": [{"sig": "MEYCIQD0q7EFKLSh7KamuI61Ig1PlHSfGQrePLio344P/NUiBAIhAKBFJgoC2xCTroWQvARxcL1UwwzyQi5vP0kLAS6jtIh7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 99805, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeL5PGCRA9TVsSAnZWagAA8nMP/iK0X+XlNwoBSzN/WdZX\nfec0Nm8KGGqVTRU6JCwgLu6KWcxkrEUKhaakRm1EpIH2buhemPAKl+M6/uIP\nLF+YXtKr4LvkNk5PS8jZmBvC6OG151ng73c5I2BqHf9HSmpl5iknADbtoII3\nXQEujXetENEr3UYWdkRrnI+kkI+sIl26sFaICVPWc1GYIIJN3JQRJW/tGLu8\no0d7fvx5WTZZ21NFWmJdqJjhowmONjIJ9rorahB581r1guMTjbt9Mpv+556X\nkMoX+uC37npY4ilz74AAOkzn26X0Wo9TZcXooIc2R28yPHjOLhugGKrrMpfh\nK7pbz8w6EzZ5QtSCuR2goiW1MJ4NGweuiYYPlH5zWUzrfLNgZPcDSL+3C7To\nH7shSK+vXbwoqeC33l5KGCwK0PqgzDBsGCRAcy2li33KTVxnbwGE9Z5i4piB\nJ2jy5hNiD8cYOjsk24cB/7LOQVtszGJOU42T7yhthnjAt8SBYVaVENIXjKEE\nWE5yAcpNTg6phmc1JVl8C6rEW09amaB46KeepUUfu1lRY8ecWdKR7JhjGJV9\n6aSoWvzatfwh0KBGmpUuXgB3JkbZI5dVe4ADwWNrJmRXGuOmgeu6ECmjWgEJ\nLkghjo0s7DLah1XdIAptbXp2vBFNqEsVP/BWihy4MUA6Q99VpCcfmXx/Xr1O\nhbDA\r\n=+lX4\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "15.0.0": {"name": "cacache", "version": "15.0.0", "dependencies": {"tar": "^6.0.1", "glob": "^7.1.4", "ssri": "^8.0.0", "p-map": "^3.0.0", "chownr": "^1.1.2", "mkdirp": "^1.0.3", "rimraf": "^2.7.1", "minipass": "^3.1.1", "lru-cache": "^5.1.1", "fs-minipass": "^2.0.0", "infer-owner": "^1.0.4", "minipass-flush": "^1.0.5", "unique-filename": "^1.1.1", "minipass-collect": "^1.0.2", "promise-inflight": "^1.0.1", "minipass-pipeline": "^1.2.2", "move-concurrently": "^1.0.1"}, "devDependencies": {"tap": "^14.10.6", "chalk": "^2.4.2", "tacks": "^1.3.0", "standard": "^14.3.1", "benchmark": "^2.1.4", "require-inject": "^1.4.4", "standard-version": "^7.1.0"}, "dist": {"shasum": "133b59edbd2a37ea8ef2d54964c6f247e47e5059", "tarball": "https://registry.npmjs.org/cacache/-/cacache-15.0.0.tgz", "fileCount": 23, "integrity": "sha512-L0JpXHhplbJSiDGzyJJnJCTL7er7NzbBgxzVqLswEb4bO91Zbv17OUMuUeu/q0ZwKn3V+1HM4wb9tO4eVE/K8g==", "signatures": [{"sig": "MEUCIQDQ8b6pPB95TRz6Khddxn5kW6GCA645sYWFEmWUtKdS6wIgXeUXbKASKFIHnuAVtdEm50v4XerMv1hWkLYkiTohHds=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120578, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeSz78CRA9TVsSAnZWagAArukP/jRhV5YGJdPCbGIvTj1j\nQyYUmmnISqQXuw+0Q9TOWGMOTaV+sQzRq0vZted8xaDiK4micGHFHlW+/ZPY\niFlz/l9vtv09+5TkhakTCR1X+URAcylN+tfTkKDVMptVhTvVuhosKRD80HjM\nHxdr5P2SlaOKaGlfsFeqFzMKIo/8LQTgLyrC/Yd5NiluoOihps0xi5Jy9PX4\nLTIkmWNe7SyT9Ygg8jtgAVtnh3jOupj0yMenuojd+EcwLtjsK1dbJHUHrvy8\nZfYLcGybJnl4eG/52cJoTRQcxZkNquDEghSY6KhFOXdAS+e9LeJiNK8T+8zd\nHQdLmXrDhv1/2yyrH1SsbdL8xBOc2ipOvVmVan9zL8Wh0lMSt3ipMme2hzIJ\nNmjqlIdh1dK/uJF3QcOMsyDlsa3Ra6jCtBTC2fgZWM2VG8AxdgV8CBoNX79S\nNa/wXUfY+AveixvvXHo0nD4XJo/WVIVYFqhM0Q4BSH9DeYHLbrPVBf3AGs10\nSiyzqzZ7YKQ2a7QCfvrLBtUro/YBgu0TWFFsAwqc0UH2GuW06Cu7b5uoKyc9\n4+rlNfXvBD0FJVw0w/3OB4RjT/Y/StY9tldvGuPcEj80WlzbXMO6RQNwpEli\nS/wPceAF/hDAdW3SCdYRhSI08lfToY/C+H4RUVExwsgzxW/shuUvDIrE8uD1\nJZsc\r\n=w3RA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "12.0.4": {"name": "cacache", "version": "12.0.4", "dependencies": {"glob": "^7.1.4", "ssri": "^6.0.1", "y18n": "^4.0.0", "chownr": "^1.1.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.3", "bluebird": "^3.5.5", "lru-cache": "^5.1.1", "graceful-fs": "^4.1.15", "infer-owner": "^1.0.3", "mississippi": "^3.0.0", "figgy-pudding": "^3.5.1", "unique-filename": "^1.1.1", "promise-inflight": "^1.0.1", "move-concurrently": "^1.0.1"}, "devDependencies": {"tap": "^12.7.0", "chalk": "^2.4.2", "tacks": "^1.3.0", "standard": "^12.0.1", "benchmark": "^2.1.4", "cross-env": "^5.1.4", "require-inject": "^1.4.4", "standard-version": "^6.0.1"}, "dist": {"shasum": "668bcbd105aeb5f1d92fe25570ec9525c8faa40c", "tarball": "https://registry.npmjs.org/cacache/-/cacache-12.0.4.tgz", "fileCount": 29, "integrity": "sha512-a0tMB40oefvuInr4Cwb3GerbL9xTj1D5yg0T5xrjGCGyfvbxseIXX7BAO/u/hIXdafzOI5JC3wDwHyf24buOAQ==", "signatures": [{"sig": "MEQCIDWoCDe0i1xVTIb04jB9XzIsmA84pYO1GlTicCPQ7niIAiA4quu5YiGDUZUoL+lFyvnMVVVqNU72MqLQJoJUu1pf/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 115357, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeeVLTCRA9TVsSAnZWagAAvqkP+QGEXsbyzFeaxv8Qv+TB\n8AAXsonmPOk9S+nphs2l4VZJdtEF/EpFXdiaLghALtZ4aWIYacjGklZj47oi\n+MwQSSpPpst7Wla0nCt2fq7Ejm1lVtaVYedk9WwEZKg4hdOTXoy23aKOtnYg\nlHnm/0p1F4/GNNearPtNI/T7drvNyOLYSkkEaRaH5+EVau5fkGF+o9CKVYJj\nCl9gYu+3/S2drDAf+9gYHEOOirN7Hwbx/S18j3M0E8yByrTbtYzUT3G++4ww\nqnsUwcYseQMTbqpn2ogJjNpBmzePJ17K+3Gd49NWk3nTqA+1sILTRcV4Bmfm\n2OImnazGU8KRUz/o/2yhYn1spdWrqzlD5NSuuYIu4lb/kTGN6ZhUYKsYbnx7\nbPM0J6CaagmL2GR97JLELGeMFl4qYYhWR5dn1S4lXHECnZuzhpndqNdE+BAn\nwsBjUvj7xVrzYFhQC18C/t/2tAqNevOF4yhtNQLNS3WCog0vC15F40h4N3wx\n1l7PsYNhe5zSQjWEF7jypi19HqdVfqD1EWESntGPcAedEUdB7+7YHW0ciCob\nwphqxKj7Yeo0AITk20MF24SNDmeAWf8yyYW0uEH8bYcAnRIBGHuuQWOzP5jj\nbnJvUh9MCSKQyqpwH2KzVAGLLfejoFUuI3s5tk++ZWFb6e4NVjuyq8+jCtVG\nEASj\r\n=PNlN\r\n-----END PGP SIGNATURE-----\r\n"}}, "15.0.1": {"name": "cacache", "version": "15.0.1", "dependencies": {"tar": "^6.0.2", "glob": "^7.1.4", "ssri": "^8.0.0", "p-map": "^4.0.0", "tacks": "^1.3.0", "chownr": "^2.0.0", "mkdirp": "^1.0.3", "rimraf": "^3.0.2", "minipass": "^3.1.1", "lru-cache": "^5.1.1", "move-file": "^2.0.0", "fs-minipass": "^2.0.0", "infer-owner": "^1.0.4", "minipass-flush": "^1.0.5", "unique-filename": "^1.1.1", "minipass-collect": "^1.0.2", "promise-inflight": "^1.0.1", "minipass-pipeline": "^1.2.2", "move-concurrently": "^1.0.1"}, "devDependencies": {"tap": "^14.10.6", "chalk": "^4.0.0", "standard": "^14.3.1", "benchmark": "^2.1.4", "require-inject": "^1.4.4", "standard-version": "^7.1.0"}, "dist": {"shasum": "a200a2505aced2154aac9a2150111e6954a5926a", "tarball": "https://registry.npmjs.org/cacache/-/cacache-15.0.1.tgz", "fileCount": 24, "integrity": "sha512-k427rNJGgXeXmnYiRgKoRKOsF+HAysd4NSi3fwMlSWVoIgGt6Snp8oTNKRH6kjRkrM64aQyOVl5B9hxxV+vtrg==", "signatures": [{"sig": "MEUCIFvT13hJhXIoo7x4IIQSTreQ+C+9kqmGEGRRAYMrRW2BAiEA5xQRKy78px3tlM7aVwPZvoftDe4ZWPcn9hkAmxcZprM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 128823, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJep3SpCRA9TVsSAnZWagAA+wAP/1JO1xZfz6ZgCNLKpU+s\nW59tvXNvXqHSatX6MlWK7rsxGEq2OqwtDCwn1O2MH+rIdPK8jVH+1eU60PQ9\nU8Hrw39HG+2s1ib1+57x9D9H+vcHgSdvjSqFudI7nPWKUtkjAjwEVBpTTIqC\nVXRKitT4fEoXO0bizfFrHPTeuNl4hxO7SqLSAt2lkpnZbE/GgfBW60RQFc1S\n5roMdnszH1Wog5JFCK0hNXmkdUwgYOSGjUYRpR9tcWsc2Kn8msB9ThlmLq5K\n5y+OFOA2170Su5puPbJNMQzqVCj4wZDz+BH0zwX7leNEnLuE6667Uxr/El44\n1EYrwKn1Da76/hs6sjcNN7jeErvrSMi5dKIJlru9qyAkXT5EpSszBuvGzfEG\ntrNxT5fnOpJXovJoLE/1F60YPDh0tT9+nwBcXqnRu7GbNx14MVxUUQXN9+Yj\n+tAkNE0PytCKRaa//SJAZNvIOLuCL9z9Bm4Fs81n6kf6n5N+3dEI/uc2K6Se\n5LPRYIz4cFrUTpLggN+82GzWaKN5wXsk00SFHUB2pIBEqoxK4n5mxzjgiJQh\nbRPz7AhrjG3E85zsuYsPt4HGguG+++ZLIoH8wHus1O8jZe71jY5mwW4MPDF0\nN08L7zteDEoJT7dxyJDNcpYp/9cWN7HaHBWPDuvbR/RdK/XQ3/s0AIMx/tej\nNrLD\r\n=v9aM\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "15.0.2": {"name": "cacache", "version": "15.0.2", "dependencies": {"tar": "^6.0.2", "glob": "^7.1.4", "ssri": "^8.0.0", "p-map": "^4.0.0", "chownr": "^2.0.0", "mkdirp": "^1.0.3", "rimraf": "^3.0.2", "minipass": "^3.1.1", "lru-cache": "^5.1.1", "move-file": "^2.0.0", "fs-minipass": "^2.0.0", "infer-owner": "^1.0.4", "minipass-flush": "^1.0.5", "unique-filename": "^1.1.1", "minipass-collect": "^1.0.2", "promise-inflight": "^1.0.1", "minipass-pipeline": "^1.2.2", "move-concurrently": "^1.0.1"}, "devDependencies": {"tap": "^14.10.6", "chalk": "^4.0.0", "tacks": "^1.3.0", "standard": "^14.3.1", "benchmark": "^2.1.4", "require-inject": "^1.4.4", "standard-version": "^7.1.0"}, "dist": {"shasum": "e25391962f0477f9ba16acd68a8f301d2e84d2ab", "tarball": "https://registry.npmjs.org/cacache/-/cacache-15.0.2.tgz", "fileCount": 24, "integrity": "sha512-XVCLiqTL5KaVnNKIUyZ1rTwmPSFgC8LAeV+ZsQqulmFdDkcUF/4y7duJ+tz1TJv0ZRUOdHZtVew4Ztz6LtvijA==", "signatures": [{"sig": "MEYCIQDY/OBZCbx4WblMO6oLkmlemsZDYJKYEHMAZlRfyPUBvAIhANKlpw3fQ9V7Mlol97tDNVCTUjdWc1y9Lr9mSjWqNS1n", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 129289, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJep3YNCRA9TVsSAnZWagAA/CYP/3TT0joYz9+vkRD6afX3\n4pmG7ftuSg6eBMvboATE9MfKMPX0fFP5nFmo3+QPwFjZsDNF7WC3LB+W8cZY\n1xbvK8wMatcUL4QKiqeF79+XNBvgRBKdl/C3TXxXIL+axXRWYPx/AG3kKmbK\nsk+1cZW99rPrLvN8elWdxaTrfkt3xfoZ0ViKOb6cITziPlwFK1MMzmmvYh21\n97q5E1wCeySY13iLgpY53LMpPoH3vO89/Uz5T55miOJPw8aDueQ3e2WjF27n\nQ4rp4pl7jjxtOcbz9w3bW8iVTq1zeGboy8+zH/6K5ht9Lqh++LxQ84YLZWdt\ne+7lOuotPkSWPhR+5fxz+581MWYcq8BrDmq1lcDBoB/G8G36qJBldT+MFYnB\nJ2NoWNm69uAbhXWLyvJxXkycW9OnFAOVHWP6YJfMTd8E7/JVD4TXEy6NpM6/\nn5Fk67GLY6XztJuC5SgL4tI+LPJKpNjH8pJeIp5kPOI0R4w4IDSQZkW6aVV8\nYHggkqUIwbjpia9dYFQ9h2SkDVQv0I/1PHykdAwLtrVMe/GoCsoLH4FL+QWe\n0J3HAUCA2fL4ZoBHQCioqduC1d39b+DwaIxjqT6TEGIz48h00Zp+cL8lKPwh\nI0IHmeJZb9+QzhaatUjQUVPdXOVOilFzb1KMCJRB4FrL3LSFVaQAKSkzpZ3i\nSBc7\r\n=EqCW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "15.0.3": {"name": "cacache", "version": "15.0.3", "dependencies": {"tar": "^6.0.2", "glob": "^7.1.4", "ssri": "^8.0.0", "p-map": "^4.0.0", "chownr": "^2.0.0", "mkdirp": "^1.0.3", "rimraf": "^3.0.2", "minipass": "^3.1.1", "lru-cache": "^5.1.1", "move-file": "^2.0.0", "fs-minipass": "^2.0.0", "infer-owner": "^1.0.4", "minipass-flush": "^1.0.5", "unique-filename": "^1.1.1", "minipass-collect": "^1.0.2", "promise-inflight": "^1.0.1", "minipass-pipeline": "^1.2.2"}, "devDependencies": {"tap": "^14.10.6", "chalk": "^4.0.0", "tacks": "^1.3.0", "standard": "^14.3.1", "benchmark": "^2.1.4", "require-inject": "^1.4.4", "standard-version": "^7.1.0"}, "dist": {"shasum": "2225c2d1dd8e872339950d6a39c051e0e9334392", "tarball": "https://registry.npmjs.org/cacache/-/cacache-15.0.3.tgz", "fileCount": 24, "integrity": "sha512-bc3jKYjqv7k4pWh7I/ixIjfcjPul4V4jme/WbjvwGS5LzoPL/GzXr4C5EgPNLO/QEZl9Oi61iGitYEdwcrwLCQ==", "signatures": [{"sig": "MEUCIQCBEucisUTxMM/VALV0k24e598SdAVUXfs+cokp8elHZgIgFKxz7Izhs7K6XFhlFDdTW8yVMgUBRVdqpYbBxhAz+MY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 129488, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJep3aLCRA9TVsSAnZWagAApJAP/1Vx0j7hc7C8TL/NIJzr\nhFa7N+S/1iexWVrRtPQpuf9Kj6oxKmTj9VnMO/iJCjYMBWC1D2WQIyGvc7U3\nRkt3B6s++UVmxbM1JX/8OIN4dhK6PwzrYZQF2nUvYd7YJrN76obtysyfC5Pb\nPMB41ARNFIS0Z37rPSkH/kB8wAn4hF+xLUO9tzP+zb40uMMY7gf/l5iGDzSG\nZs8U92yYwB5cSkTqHiv8oPD6N8/2mpmUAXDEUa0JqDvHGtQFeUDtxCiWjWGW\nWgY32EL0Aer2Kiba2jMAbUtXrDcBvuCEiQKHaNyFfqCqhQ08vWlKdzfCqrNb\nEzqn2R5xM1peYkjrrwuy/cYwrehAMtGjW8/ySRssM++/cB/Jt+MMsQmcUL+w\nRaIkusNTNy5iZpDPnqB/3Iow0dnPhAiGubodMQq6bq+R7jNTxpHqt9C2cVso\nAvxrFvYs/PEjEBPhzEViBAgXpx3hBGq90MzAaCl9qODbAB5Jt7063GLJos37\nYMzpBxa4vH9+J1W7ZtjtJiXSqIFpNjqF7drIiVlfoFfzuv7tWThsw3kidSBS\niBlN3XY/Y1at/UMftZtDHvAcDmyV0gYVTmOOAUrFpeXhVg+aC/Ni8NPw1UBb\nnYVUDlmoCEj0FxtPqS4853Uc16QggSa1oPnb1V/Sa78q012/KlfrJZd9j2R6\nGdJa\r\n=tBCz\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "15.0.4": {"name": "cacache", "version": "15.0.4", "dependencies": {"tar": "^6.0.2", "glob": "^7.1.4", "ssri": "^8.0.0", "p-map": "^4.0.0", "chownr": "^2.0.0", "mkdirp": "^1.0.3", "rimraf": "^3.0.2", "minipass": "^3.1.1", "lru-cache": "^5.1.1", "fs-minipass": "^2.0.0", "infer-owner": "^1.0.4", "minipass-flush": "^1.0.5", "unique-filename": "^1.1.1", "minipass-collect": "^1.0.2", "promise-inflight": "^1.0.1", "@npmcli/move-file": "^1.0.1", "minipass-pipeline": "^1.2.2"}, "devDependencies": {"tap": "^14.10.6", "chalk": "^4.0.0", "tacks": "^1.3.0", "standard": "^14.3.1", "benchmark": "^2.1.4", "require-inject": "^1.4.4", "standard-version": "^7.1.0"}, "dist": {"shasum": "b2c23cf4ac4f5ead004fb15a0efb0a20340741f1", "tarball": "https://registry.npmjs.org/cacache/-/cacache-15.0.4.tgz", "fileCount": 22, "integrity": "sha512-YlnKQqTbD/6iyoJvEY3KJftjrdBYroCbxxYXzhOzsFLWlp6KX4BOlEf4mTx0cMUfVaTS3ENL2QtDWeRYoGLkkw==", "signatures": [{"sig": "MEQCICXq1IPtOJWd1s4EIDQGapjP3h0ZlvhgnYNvKHjsGEWpAiAUeYAawVm/tUrm6ShTebeVEbJyehmgtxj1UzslUmjeHg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101129, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe1uhgCRA9TVsSAnZWagAARdsP/3cDCbZiJUoFT8OjL9ME\nbg/6A4JeHMmchM0YNLjey2FhM96SjvMgCcLaBywDtKn87ItuZD4wP+3Env5b\n+NWHSXZrdfFkK4mNnNTNUn499YfZmV/dRbb5alUibDm1LbdjGMqyRghl+veE\neFIXzR/+Am3F902D0S8z0Ig08Rbrt/3NvuAxqfPDWZbO0X7Iy/F1YL7e48Oq\nwvXC2WNXHQ20JpsPgge5CDGZEZOm9+X5kDhCmDIXxKzgTanMaYnBz7GS4nFd\nGhy/5zmhwaW2g/xUTpOENaGjaZSqyWayRcvRYv3DsRFMnGJjlMKx6Kh8bWJc\npWGmc58z/uXCTD4AQi4vgBf5wNiT0zEUpVfWhXWGFcEI5ahJ6ZBhx+RDHDlV\nYTJ6SGB1Dt0v49opaytX9ptAAPL2kWXfYuq6eLDDTHLM10hofBsybGjEwzIF\nX0NUnRXQFUB+aF22rM7nKd+CAjp0GCw4hVv3nm+eXVIgI8QW7imTkNpJt1vt\nzmrAXfRQOOlrB+IxwLmcD3CCjL99BdsRsAqhpAZ3q8thL/Vkyvwyeg6y81cC\nZ/m/3t8AGWUHjA+G2MUyTb4uiQPbVhsV6UNara9Ka2SRirgo+WGPLNxPjAlr\nvV/CHikm29BNP2c2gGyHXntZrv8w5BMJ7Ex3ldDPnWLC8bE7/BlCcQ/pEBLh\n5Sti\r\n=l2vx\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "15.0.5": {"name": "cacache", "version": "15.0.5", "dependencies": {"tar": "^6.0.2", "glob": "^7.1.4", "ssri": "^8.0.0", "p-map": "^4.0.0", "chownr": "^2.0.0", "mkdirp": "^1.0.3", "rimraf": "^3.0.2", "minipass": "^3.1.1", "lru-cache": "^6.0.0", "fs-minipass": "^2.0.0", "infer-owner": "^1.0.4", "minipass-flush": "^1.0.5", "unique-filename": "^1.1.1", "minipass-collect": "^1.0.2", "promise-inflight": "^1.0.1", "@npmcli/move-file": "^1.0.1", "minipass-pipeline": "^1.2.2"}, "devDependencies": {"tap": "^14.10.6", "chalk": "^4.0.0", "tacks": "^1.3.0", "standard": "^14.3.1", "benchmark": "^2.1.4", "require-inject": "^1.4.4", "standard-version": "^7.1.0"}, "dist": {"shasum": "69162833da29170d6732334643c60e005f5f17d0", "tarball": "https://registry.npmjs.org/cacache/-/cacache-15.0.5.tgz", "fileCount": 22, "integrity": "sha512-lloiL22n7sOjEEXdL8NAjTgv9a1u43xICE9/203qonkZUCj5X1UEWIdf2/Y0d6QcCtMzbKQyhrcDbdvlZTs/+A==", "signatures": [{"sig": "MEYCIQDOjiBohijNGmSjippumvasqOrEZM1IhUMC67RKqlWB3wIhAKBdF8TeQH7b+0vdC+MK/Q+np6F8yHhwR8z7osFJuyuJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101214, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfCRDiCRA9TVsSAnZWagAA3OEP/2xU8xoGxnBmzg7xkslx\nhwlrEXdYkjmoDmAG21tnIRiJD4wlg97cXS/dCVtuy+zP1MHAl/2AgW78O1KG\n0aATiEaB5p+TurkBdM47kuRnKTF5Eq06hXE4PBRuXSKLyMT5t3pSza910JVu\nND3cx54o81GdoOC71XPIrlZIPe5fzn/msvbv+Cjn0O6m6FPIQ8UxPDv2Yl13\nPODTmXYRfVfbaXZy6AoWcusxli7oK6aPe5VN3gvh4gZZo6vlbWKnVFeAAEp5\nrJbU/nSa3TrQ8w4DpvmvU2pq9tp32CBAF1+3+8RgqQ1yCMco+xHJKdHNuBw8\nnvz15togFKmiF3LiWEKTryEaRG6b1YaAsE/UvCFdQHQO98ThZQHvwaVVQ65W\nYGxQTJQX2l9uzYIhYUsEOqkrIOGeycXWvpwEpUkWzJSQwC2cxa4EG1i8irbc\nkF+axrkeWsBOYeiCegZUvcPV0M7R274iYbdGPv6ROgWRj5itCWrf6mMlYKBF\nO/QuKHtuxyQBfxaM5JozrO6oyaE1sVk+0y856b9oYbo0G0QiWEvUS7M3dU7a\nRYp6bZRKNvtHo3F2C3nW2W3ENOrfy0esFyXJz0Y8fZr/DaL4JGE5Hh7kSPrl\nZx4qJeTkaiqJZ3bBXwXyZs6ly5aisQHziam04wZM1aVs0y1MYOcs19LBaUBA\nSv2B\r\n=i2T6\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "15.0.6": {"name": "cacache", "version": "15.0.6", "dependencies": {"tar": "^6.0.2", "glob": "^7.1.4", "ssri": "^8.0.1", "p-map": "^4.0.0", "chownr": "^2.0.0", "mkdirp": "^1.0.3", "rimraf": "^3.0.2", "minipass": "^3.1.1", "lru-cache": "^6.0.0", "fs-minipass": "^2.0.0", "infer-owner": "^1.0.4", "minipass-flush": "^1.0.5", "unique-filename": "^1.1.1", "minipass-collect": "^1.0.2", "promise-inflight": "^1.0.1", "@npmcli/move-file": "^1.0.1", "minipass-pipeline": "^1.2.2"}, "devDependencies": {"tap": "^14.10.6", "chalk": "^4.0.0", "tacks": "^1.3.0", "standard": "^14.3.1", "benchmark": "^2.1.4", "require-inject": "^1.4.4", "standard-version": "^7.1.0"}, "dist": {"shasum": "65a8c580fda15b59150fb76bf3f3a8e45d583099", "tarball": "https://registry.npmjs.org/cacache/-/cacache-15.0.6.tgz", "fileCount": 22, "integrity": "sha512-g1WYDMct/jzW+JdWEyjaX2zoBkZ6ZT9VpOyp2I/VMtDsNLffNat3kqPFfi1eDRSK9/SuKGyORDHcQMcPF8sQ/w==", "signatures": [{"sig": "MEUCIQDrBAzO/niSUqnY3Gd1VnUw7I9YSKdMOiAyOfwIu4PClwIgH5rOg+jR7ckJYc2SFh6esuFrgZZo17FxI1YxaHn3mm4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101299, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWMVxCRA9TVsSAnZWagAAn1oP/2AFhHClE4AsJ2QNKRu+\n6EXyUj/82b1loPaGKMxOn8M+hpxZi+1gEl0g6exxIN+VizBdtXgA5GdyDSIL\nI0vuTYOCspWjA4hXKz6blCcwTBg3jRA9Wm6t/pwCvDntQWGvFcqrG09ea8Or\nj13ijLlLFDlMYK1bYeWrmF2EbJ0R2TYD2wxjYnyqBtTpLuDipwsbtXPcWVqI\nKMu+/CIQr4x1hkKcRwvXbf1i0Ah6rpWEzf/N9KjH+U4EqpJbl30+X7mJjatx\nEsVJ63dLu4Bv1ejHoji/heT1IsCUi8qvR6ciQN02GSiP7pbXysePeYY8f5jF\nnOg+mYR3edH6UBF9H2gDq4QXOS+j7VCsyaZPL7in5xA/PJqXUTdj6gJtn0lW\n3dtPN5ZDcg1/kJLIlTMkrx4mbBf8S/yA0EjgsSlG3Bmj6uphFGep2aBmCDrl\nmW33/0d7Q3in+3QqWoN8mHGvNhsuQpABMPERHdrPAitGR+E5wf+TlP76F2tL\n3u8v7NKnnEWAdWQoydWTbXtZD2RAggQWN5cOLe2thd/MTv3Zq2CbnP9SGCIN\nODGgVXJuOx2wuHErwS8MYRwbdzbAw+Hr694AZ6Ac88DZHwGeOAROpgswD1Ya\nvqJ+QFsZWM+EgTQ5Cc1qrX+x59dt7dTMrOtBkPz6RFDjFk2c0R/zziaVIcCH\nXOHP\r\n=nA5h\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "15.1.0": {"name": "cacache", "version": "15.1.0", "dependencies": {"tar": "^6.0.2", "glob": "^7.1.4", "ssri": "^8.0.1", "p-map": "^4.0.0", "chownr": "^2.0.0", "mkdirp": "^1.0.3", "rimraf": "^3.0.2", "minipass": "^3.1.1", "lru-cache": "^6.0.0", "fs-minipass": "^2.0.0", "infer-owner": "^1.0.4", "minipass-flush": "^1.0.5", "unique-filename": "^1.1.1", "minipass-collect": "^1.0.2", "promise-inflight": "^1.0.1", "@npmcli/move-file": "^1.0.1", "minipass-pipeline": "^1.2.2"}, "devDependencies": {"tap": "^15.0.9", "chalk": "^4.0.0", "tacks": "^1.3.0", "standard": "^14.3.1", "benchmark": "^2.1.4", "require-inject": "^1.4.4", "standard-version": "^7.1.0"}, "dist": {"shasum": "164c2f857ee606e4cc793c63018fefd0ea5eba7b", "tarball": "https://registry.npmjs.org/cacache/-/cacache-15.1.0.tgz", "fileCount": 21, "integrity": "sha512-mfx0C+mCfWjD1PnwQ9yaOrwG1ou9FkKnx0SvzUHWdFt7r7GaRtzT+9M8HAvLu62zIHtnpQ/1m93nWNDCckJGXQ==", "signatures": [{"sig": "MEYCIQDwt9AhuSeRpVb3KSyY7DNE8wc+6HqRA/c3v5tnTgUIYwIhANRYPyryb3R0/1zApdIe7DTpeXQNtwd/Kzmiizomj+rY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75205, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgpTMrCRA9TVsSAnZWagAAEsUQAIpj1n1QTTr3jxNMxhmv\nDR+cFD4/zpRkeFjVckrls5dqFLxPkI8Ka8uuNCplDN41UqLFIipcuSWkTk57\nVFk1ppJTVGKtWrQbPEqkESMlm2/IXBt1ehhcBMbwlGC02ipz23tjYL/KvnX0\nr5m9ZruUUfv5lPqUp6x16sFx9NbFl1ppe3IO2v27z3EpmAqXYmvHr77RGHBi\ngbG4TQa3dK7snPFsNdHuL5nXwO2P3Oj9FXJD0JQcKtA5s2xHEx/LivehpQDM\nfNDfhERfesXEX3RNzf62bo2EB9qYxYY06mXjhzhsQgcjVACbwz6+jQXShVYJ\nLOTTcDdCFQLWRcaO/B3zJYdgq5RBz3zWUp9MpvuB3jImjhGWFxwIb4Tjej0C\nQQ0dAgciMrckTbcd7adyL07DDLy/EiGZ5JmSTJZnM8kXiuPBQmksXCxd/cgn\nBwgEvu2DuTj6Kmh9LZr6c/5mcvZKedIeVgbr44wD/VlDt71dvaKwz9vHHos4\ngwvsXiXCw/oMo8iy5SKaSEydw2X4B0gzAjhXJwD3f3xpJEMxrUO8QMsKi4/j\nNXkeTP4GN8vxYD28apRBsV/lPywzdhpoTPMvP3qMANh2mzXtuOQA6TOjRRjN\nZLYC+r3apAG+z5tlOpJIXvWlr0ueSp8UF7cxvNRI9PJ9EuPBnqIG8CcHY9S8\n8GU7\r\n=AZlm\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "15.2.0": {"name": "cacache", "version": "15.2.0", "dependencies": {"tar": "^6.0.2", "glob": "^7.1.4", "ssri": "^8.0.1", "p-map": "^4.0.0", "chownr": "^2.0.0", "mkdirp": "^1.0.3", "rimraf": "^3.0.2", "minipass": "^3.1.1", "lru-cache": "^6.0.0", "fs-minipass": "^2.0.0", "infer-owner": "^1.0.4", "minipass-flush": "^1.0.5", "unique-filename": "^1.1.1", "minipass-collect": "^1.0.2", "promise-inflight": "^1.0.1", "@npmcli/move-file": "^1.0.1", "minipass-pipeline": "^1.2.2"}, "devDependencies": {"tap": "^15.0.9", "chalk": "^4.0.0", "tacks": "^1.3.0", "benchmark": "^2.1.4", "@npmcli/lint": "^1.0.1", "require-inject": "^1.4.4"}, "dist": {"shasum": "73af75f77c58e72d8c630a7a2858cb18ef523389", "tarball": "https://registry.npmjs.org/cacache/-/cacache-15.2.0.tgz", "fileCount": 21, "integrity": "sha512-uKoJSHmnrqXgthDFx/IU6ED/5xd+NNGe+Bb+kLZy7Ku4P+BaiWEUflAKPZ7eAzsYGcsAGASJZsybXp+quEcHTw==", "signatures": [{"sig": "MEYCIQDJ7HhLi3B3o4AUu4fM248uEMQgc7cPt7l413nYHABq0gIhALqrRUyllAVUoCJlemGtWzNACl8DuyOoLWLCk7ET21xU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76631, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrPsDCRA9TVsSAnZWagAA8pIP/iHbe/smP+5CjiwWd6bt\nm9CwZLyo/NiWWBv0qlqATstmqXnerF+PmB2SeOaFTiciLBYFCQKPfIWu9dsS\nyhQN0EMxYUSNzx6kCmqlSDc/9jdWhOeJ+V4p2A9JDgGfhjT199QI7/8FSZEG\nUhFMrbz3hFdhWpZAfVNb7XFZ0ZREjufCk2CLqDQ4VbWdQ772hknedUdKIEZy\neH+CAO2TSiHzjcdgzaGbIxpQorM+y5h/05/Xzlv0HbaLDe3uDINQ9E1o7hSQ\nurE2I1jKWT9SJoTq/jUC0uvEPgWfxK0d6zKme+WYNNrQCXaSHr+oUDVA09Em\nq/LOfqAqTWPuaDyxiV6pOPWr8X6ZcNL0yaKUwjCYqtkRnJSfizAi/gIliqOp\nLYBCPuxo2i57LIuMWZumPNRlF9sM2Nz63tQEsoQSppvrkZJmCIWGrHvUbMcr\nCRE+2kn0RFR9EgCrLvhZfvF4EUD3JCIXCpc8W71iLs+hQBXCfbx6ml2pFS0B\nGdqbfLT4NT2nGwbvS7tnZUeGPc4NBBolN9xaGJUJueryl8oPcBtj6B4/R8A/\nJbAGwO1cm0ntTxO+gtbvyY79IYveyJHE5ILE9gZDtsvAVTPylOfvmdyRv+t/\ncDPf70Q4hx7btOIe1+ue82k/+DyJ2aG8LH4bV5vEadDVCbiyRPCrGsZ32doA\nnMTw\r\n=/wHH\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "15.3.0": {"name": "cacache", "version": "15.3.0", "dependencies": {"tar": "^6.0.2", "glob": "^7.1.4", "ssri": "^8.0.1", "p-map": "^4.0.0", "chownr": "^2.0.0", "mkdirp": "^1.0.3", "rimraf": "^3.0.2", "minipass": "^3.1.1", "lru-cache": "^6.0.0", "@npmcli/fs": "^1.0.0", "fs-minipass": "^2.0.0", "infer-owner": "^1.0.4", "minipass-flush": "^1.0.5", "unique-filename": "^1.1.1", "minipass-collect": "^1.0.2", "promise-inflight": "^1.0.1", "@npmcli/move-file": "^1.0.1", "minipass-pipeline": "^1.2.2"}, "devDependencies": {"tap": "^15.0.9", "chalk": "^4.0.0", "tacks": "^1.3.0", "benchmark": "^2.1.4", "@npmcli/lint": "^1.0.1", "require-inject": "^1.4.4"}, "dist": {"shasum": "dc85380fb2f556fe3dda4c719bfa0ec875a7f1eb", "tarball": "https://registry.npmjs.org/cacache/-/cacache-15.3.0.tgz", "fileCount": 21, "integrity": "sha512-VVdYzXEn+cnbXpFgWs5hTT7OScegHVmLhJIR8Ufqk3iFD6A6j5iSX1KuBTfNEv4tdJWE2PzA6IVFtcLC7fN9wQ==", "signatures": [{"sig": "MEQCIHVjjZL+4iYdr/5IGpJRHcU/xoSbEHWB+YPz1hly5ZAWAiAY0kMzII7pqNDvCBXFBuDD3xIgCyJdisw+jnqo+vSSgQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76264, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhJ9GmCRA9TVsSAnZWagAAzP0P/16cO+koLcRkKtrw0kBf\nOQj1j2T0Z4DMntv7NBKjQseEtfOKeY2+uKzQVJsJSPl87yHhzF2raa3L9rTD\nDAKI4vgOnVhGuKB8OHeU4N8um5xBAC4b84dBGsfWD3PuxGXEzU2IjjhkYdkW\naERVgoGocpENWdzdwpBQQr61tr2KA5Q2Len+JM/MMCljqo2Rc9WUPpW7uQiH\ner4eNpkSM2DkXXQjcfkEJ9H5fuTQTdrD/wJV43xRQFHGLZOYcskLTB6A8OUz\nryjVzhjutR4IhnomkpTDxWPGLnMK/oSOUuTb6kUCIQq2wwt/aWgxP5LJUeon\n2nrePNp5t6R9pt7gKW1LVeIm4YMyk4JpjdxsoJ7JPWc3egravArqzo4mls+G\nZbFdV9FmfK3uXlcSJ1bSCz3QcgwEGxFZ0znjNxE+htHKsYkg342vmdzQUYA6\nJyKw0BcG9fa0ChMWPiNoPbVko8aaOG4Jn59BlUrjEqKHAz4KqbvrhwZk+I25\nbLSqXQVO0KzsUIDge6fOykorutnsxZm1dQoMUw/gVs12IV6OvMRIEHE9WlP4\nVX6q7yg/DitSmhDv77xpyF2gnrozLyN1ch2queB4ArDfCQrHXb+fVXVKxFCz\nRuw6L1mYMcmU3VuXi5oXgm6zI8mKyx+VT+5CdVtnIW1sFYQEwCkrwgT2dmka\n0GJB\r\n=XcB9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}}, "16.0.0": {"name": "cacache", "version": "16.0.0", "dependencies": {"tar": "^6.1.11", "glob": "^7.1.4", "ssri": "^8.0.1", "p-map": "^4.0.0", "chownr": "^2.0.0", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "minipass": "^3.1.1", "lru-cache": "^6.0.0", "@npmcli/fs": "^1.0.0", "fs-minipass": "^2.1.0", "infer-owner": "^1.0.4", "minipass-flush": "^1.0.5", "unique-filename": "^1.1.1", "minipass-collect": "^1.0.2", "promise-inflight": "^1.0.1", "@npmcli/move-file": "^1.1.2", "minipass-pipeline": "^1.2.4"}, "devDependencies": {"tap": "^15.0.9", "chalk": "^4.0.0", "tacks": "^1.3.0", "benchmark": "^2.1.4", "require-inject": "^1.4.4", "@npmcli/template-oss": "^2.9.2"}, "dist": {"shasum": "891149a9f9b0c8bbe6cd84d8ac246d6cf5ff429e", "tarball": "https://registry.npmjs.org/cacache/-/cacache-16.0.0.tgz", "fileCount": 19, "integrity": "sha512-pMX6sqJSlGpxCM257by5syifGb7zH6C30CaJXeGXqmKNrHKqvMmwM8KgKmsZcUAsnNQkt7WvENH2Kl53RpFQuA==", "signatures": [{"sig": "MEUCIQDvJOboMqZa2DJv+zCG8992QNyNx3YD3upSBNCY48Z9tQIgafJDw7iv5DS1Dh5+GSvm/j+IHiNP1hYWn9s2MHbSCig=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76994, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiL6ODACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr9VQ//aAsgdmba9zKLuSWXVq/Fg4AkmvjPTK3Sb7USAGK+EsTo+Ffi\r\n+wh6qyDwn/hjmRKdPvqJOlW5iBmS1ULRV4TMfZ5H0xVrmq2cNKGP9tjkgtyl\r\nF0ey+LdXtjFZ6LH7f6cru4ovIlttyDnAybwfIxfvnFDOZrwQikKwVg7r01CM\r\nuLVSn4jOJ/ZGY4yM+hUfGZ58mxERTAuG2cW6ZbGXBmFu8cn/NY2ulrqm72Ey\r\nUl8WSVYlCHX/1ARRL1yr9I7ikKknav+gaq9zalJOiQr6rIq7un2SpzG6WOYE\r\na/7UIzM2FK3WmaWRbwTI+NRMXkE0zY/xVPuHgNiOyyj1jQZOKaOCyD64jbqY\r\nkARuN1DM96CUiafJU2detACGDLLHcwF73/DKNRFKQQV01UJCDUkS4BGcxcwF\r\nq/XW+C3IaAk+NTHeLVAXU3a0pwIQGNOvknB3uNMBeNdaJw0pzqUqTqfOB0pm\r\nNgIkukIMQtxUylt0lFh6F81n94Roa3j7N2m755mRDr8pZHl/1Wi5l4mDH4tZ\r\nvBPZ6oRFCkoyj/3VHoT4Xycsokx0IvWHZu1jlb+1umgl+9+Cx2Q/qIATO8sH\r\ngjQ+75q+/8TlHDIyMyu/vic42y0+GyCRF8FAUshWwyPmH8nKIJNAdW75pZXb\r\nfmzpLT/HpUyCTnjlseHujrs7KV2/VsW50IY=\r\n=9vtZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "16.0.1": {"name": "cacache", "version": "16.0.1", "dependencies": {"tar": "^6.1.11", "glob": "^7.2.0", "ssri": "^8.0.1", "p-map": "^4.0.0", "chownr": "^2.0.0", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "minipass": "^3.1.6", "lru-cache": "^7.5.1", "@npmcli/fs": "^1.0.0", "fs-minipass": "^2.1.0", "infer-owner": "^1.0.4", "minipass-flush": "^1.0.5", "unique-filename": "^1.1.1", "minipass-collect": "^1.0.2", "promise-inflight": "^1.0.1", "@npmcli/move-file": "^1.1.2", "minipass-pipeline": "^1.2.4"}, "devDependencies": {"tap": "^15.0.9", "chalk": "^4.0.0", "tacks": "^1.3.0", "benchmark": "^2.1.4", "require-inject": "^1.4.4", "@npmcli/template-oss": "^2.9.2"}, "dist": {"shasum": "bad1d14963d9851840da3dd6c4db3b6a3bdb585d", "tarball": "https://registry.npmjs.org/cacache/-/cacache-16.0.1.tgz", "fileCount": 19, "integrity": "sha512-tHPtfdZDqQpZ15eaEZeLspIqS5mK5fOBDZi6AjuqaIi53QNVXH3dQv6uKT3YuUu6uxV/8pjU9in0CoJ8fgaHqw==", "signatures": [{"sig": "MEQCID3qgEuTfF2eeAKMKkFz1qlSk5J1YiV1IEcWVvkumZuMAiBp48wK0Y0BuR18jxZzKh/0ZFhML4WYHMLTggf4Zl87qw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76978, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiMPYyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoVLQ/9Eh77dMoOVbmbMiCyJ9XR+kODvPqxlg9l64tgHsDT5BDnKjby\r\nZORsh7YzL/mFHc9IlsMxPJjCLmPXFLv/2b6bVvb9kbEuCmZktOS5fiMucDq5\r\nBeif4rBP1O/94PS9DS2jY+IdPyJ6hND7DXzQe4kUzYWphoV5LasDnkrUkizK\r\nniipWWfLjzaDvBNs3aHcKbaYTyqRVMjV83o89hJAbXz3ekY4N1Pe6b1MQjjX\r\nzw/b/Morte2ZUBbB/+jtJ0Jz+lj+sv1kCZTMllmbNj/eJ/U+EZAFP2bMrbaZ\r\nQBua/yhMsqPcYmuoqDMG/S5avDF/gcduqpw7pKyfXkpghkbEZ9nAjdVuAITW\r\n2lBgv/yVgljyXL00mbxuHhgpZUo+i0VF2oShGCZS5NrJXcsbspSSEnwSxJ10\r\ngO8R72Hs1rQadDTPJ1+kqd+1Ic2WJyHs1tl9okh0ACtFv8VoXrV2wKiuR/Hm\r\nboKFCwMIeMjhiyscNznTF0mOI7ZiDSkAkk3H6cLaN22Wex6BxEccH9cdY0PN\r\nq/7/VWyxfjwRmFYcrxLk3c4NWWcFLY+Le2inf8zHbxoxBwRSZUYj1x4iO4TW\r\n74Q/tAX7lPu0M9kBPUdURfwfgqHi217UxKtDS2hOcqx6Y+ewFGlQFOg1uAKM\r\nWwqjh1VdrAtOvKqDMY+/G+PZ4J2uKrl3Y9s=\r\n=TXwu\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "16.0.2": {"name": "cacache", "version": "16.0.2", "dependencies": {"tar": "^6.1.11", "glob": "^7.2.0", "ssri": "^8.0.1", "p-map": "^4.0.0", "chownr": "^2.0.0", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "minipass": "^3.1.6", "lru-cache": "^7.5.1", "@npmcli/fs": "^1.0.0", "fs-minipass": "^2.1.0", "infer-owner": "^1.0.4", "minipass-flush": "^1.0.5", "unique-filename": "^1.1.1", "minipass-collect": "^1.0.2", "promise-inflight": "^1.0.1", "@npmcli/move-file": "^1.1.2", "minipass-pipeline": "^1.2.4"}, "devDependencies": {"tap": "^15.0.9", "chalk": "^4.1.2", "tacks": "^1.3.0", "benchmark": "^2.1.4", "require-inject": "^1.4.4", "@npmcli/template-oss": "^2.9.2"}, "dist": {"shasum": "b18d630b707554d7d5ce96f53bfe845233c84933", "tarball": "https://registry.npmjs.org/cacache/-/cacache-16.0.2.tgz", "fileCount": 19, "integrity": "sha512-Q17j7s8X81i/QYVrKVQ/qwWGT+pYLfpTcZ+X+p/Qw9FULy9JEfb2FECYTTt6mPV6A/vk92nRZ80ncpKxiGTrIA==", "signatures": [{"sig": "MEUCICkqIOYX5z+E/CoUgexechF4TpAxVEaB2CBdZujI1MHdAiEArJVPxOrxMWNuCIzVlL6cr+XBzZHNMjS6FBtwJq3fQRg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76978, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiM38JACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoiCBAAj3QJjlSYGWjcafex+twbUXJ6abQtLfjM9KdI8fgWJRhlBplC\r\nSz3n9BxbWUR4wingXR1jMEoFHIPDqElVfxtPnb8yLKdihlgsrjg+FwgWFYZE\r\neqTJIbLl/jBMUbNPA51ooJF6d/UiLcA+j8evbKxU4+o/lqy5SUayAP0mEQXC\r\nGFgjp/lZZ13q3MZiJ8z7nJzbeFnJ+CwZnn2Uk1EG5/20vO1F7je7QUJ1d8r1\r\nVz1ss0yNn/fUjgKjQQ1ag4Ikeal31ujZU1bfagq8Fso2KTpJCDxXjjf8rErg\r\nOVOjDZEwVKZsJqtVzKDyP4M2FyOk5VDQDn0VYhaOagcjbKlgWj3LyrLPxzbs\r\n5N+QWFTOY/Hr4XiZu9l0XVeEZQ0JXSlRzVzj4kV3pBt4RUpjTkK/B5jnN4hm\r\njRNYiNHQhOSMTjsXEC5f5KMMufWxZprmy3vepFhw53ez7LZum0U6i7vSox78\r\n1zoopuBMHVwVDx63fKneUM/ZnIzg6PLcbJ+jJM6WCURu9Oo0mQf6TNX1/FAu\r\nUsJpPeWkT0m0LA4bHEKBROWnW5XBuY4BsCSPVU0KCxps2l+PThFgC6DelWpO\r\nw0rQ7Dq2MQ/JiYKDZLY3eIgV5DCibcHtxDEtkQHH/ZxjLKqM0m7Ju/brkI84\r\n1E3+sA9K01i/VCSqtWB8sstas0o0Ylc81N8=\r\n=k1aT\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "16.0.3": {"name": "cacache", "version": "16.0.3", "dependencies": {"tar": "^6.1.11", "glob": "^7.2.0", "ssri": "^8.0.1", "p-map": "^4.0.0", "chownr": "^2.0.0", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "minipass": "^3.1.6", "lru-cache": "^7.7.1", "@npmcli/fs": "^2.1.0", "fs-minipass": "^2.1.0", "infer-owner": "^1.0.4", "minipass-flush": "^1.0.5", "unique-filename": "^1.1.1", "minipass-collect": "^1.0.2", "promise-inflight": "^1.0.1", "@npmcli/move-file": "^1.1.2", "minipass-pipeline": "^1.2.4"}, "devDependencies": {"tap": "^16.0.0", "chalk": "^4.1.2", "tacks": "^1.3.0", "benchmark": "^2.1.4", "require-inject": "^1.4.4", "@npmcli/template-oss": "3.1.2", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "0b6314bde969bd4098b03a5f90a351e8a1483f48", "tarball": "https://registry.npmjs.org/cacache/-/cacache-16.0.3.tgz", "fileCount": 19, "integrity": "sha512-eC7wYodNCVb97kuHGk5P+xZsvUJHkhSEOyNwkenqQPAsOtrTjvWOE5vSPNBpz9d8X3acIf6w2Ub5s4rvOCTs4g==", "signatures": [{"sig": "MEQCIDzGkxN+5eVLxOpeC4Z4I9hzwPuXRjc7SDhEdYnNlQRgAiBZQhbwduMWyXKwhdETbjuzdIHieII6DcYkRKFdOxxSyg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77185, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiOginACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqsAxAAirnFcnwR4WaEWeQeX5bwBeAR2ZU8bHEc6r0O0mznlyhayD5D\r\nlsZtLYgPXvjPvXVlP+sXOyCo77jxK2yZ8NuzMlHRptqSW9t7lBsCfeVs5MBQ\r\nfxKzU0gyagVCAsp7WeMwn63VKkpEMNAF8MzBMiF8lFfc/a6nXEBGoJmqIjXu\r\nrK+6SxlwNF1I6uYjWJGpZCuCjMcIv6fPMVa2ybpLkx0ZMPCaI18qafRFxmuF\r\n97RwftbKP+BFoEiSiZfloVmLcomcugsp/NWZPoXX6k6vrhX9sPcMCtga85KE\r\naXQHW/G36IC9NedMM43k/0xfsVc5qZBwLAamLWGC4V8GHAa78rLMUpltbAiz\r\nJ6kaPVucSkDOqjrmVUelBN0L2yqsYlrHTfpENuxBhFGsJ3sA7XfvaRXKsXrk\r\n6kt4SncexHrH8AZtCHr6VdQB1qp/NAimNx7h2C4LwhCNDDXT3ewfz/cbtKhf\r\nJd4+c0aL9jNCSSH1qsMGOk9j+e6EPOnlXbcSbKLLye43yk8OW5bTnwwvqZHj\r\nhqV6Br0wxtunV/+nn31wcyi8aWz5chE4s+cbnJfCfD1VnKs8aWXym2nEQ3x2\r\nLCqOkG+N1J1qod5tp0e2uBVhb4wOYEWufvqHOVCQJpqTD7CtKs3mGICX612v\r\n1uKKQu1YhJzigQdC/wOwSa0kZWBbwgZ2xJs=\r\n=35ao\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "16.0.4": {"name": "cacache", "version": "16.0.4", "dependencies": {"tar": "^6.1.11", "glob": "^7.2.0", "ssri": "^9.0.0", "p-map": "^4.0.0", "chownr": "^2.0.0", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "minipass": "^3.1.6", "lru-cache": "^7.7.1", "@npmcli/fs": "^2.1.0", "fs-minipass": "^2.1.0", "infer-owner": "^1.0.4", "minipass-flush": "^1.0.5", "unique-filename": "^1.1.1", "minipass-collect": "^1.0.2", "promise-inflight": "^1.0.1", "@npmcli/move-file": "^2.0.0", "minipass-pipeline": "^1.2.4"}, "devDependencies": {"tap": "^16.0.0", "chalk": "^4.1.2", "tacks": "^1.3.0", "benchmark": "^2.1.4", "require-inject": "^1.4.4", "@npmcli/template-oss": "3.2.2", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "66877ae82717ade4d1416d5b3caa3a870f2c6d0c", "tarball": "https://registry.npmjs.org/cacache/-/cacache-16.0.4.tgz", "fileCount": 19, "integrity": "sha512-U0D4wF3/W8ZgK4qDA5fTtOVSr0gaDfd5aa7tUdAV0uukVWKsAIn6SzXQCoVlg7RWZiJa+bcsM3/pXLumGaL2Ug==", "signatures": [{"sig": "MEUCIQCwOM4GI0lkr/HNqpAaXpz+YbAeskdbXDuoFwLrrtddqgIgLo/aqmR1LKP5TXtL9D6kAcke7ocy4p1kJu8LiRMdfT8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77185, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTKIiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpjDQ/9F0lFocPZKKwnpmH3OvecGDcRstbKvNzL5Hmvj96DWrATp1B3\r\ni0eNi40HLSC9rrblNjuU20rrZGIolNQRHaszFBnLad1c60pUWSCFEjn38Lrw\r\n3/hYxLcBm335H7VnkL1mCioHioLLp5PirzSQNKoqlbb1gdanUO+cY3b1NAEz\r\nKpGyDW46XeKMOxafSs/Eb9qSVvdFr+R2aqR0OZxhHIquZArBrcTOicEAC14N\r\nA6NJHfjJPunzUOONRKem80YtUFmmyCCOpcHm4xkxJ1b0dORWNhXk5X2atKOn\r\ng8/pMu38IiR4s6UWo9Qv+PfjWnIaWknuQVz7s0nb/vV1zp9Fh1aEYt3+6eop\r\nKRixPVLoKZALYM4RXO0W0vNxrAC4rrZtjW2SFDenArjttGSIWHzWxJlbrQEC\r\nFSQ9EkkP1cRl1Mn+yYJoLvFE/85n4fMqnsk3sZCxgDdu8xiZiLXIVLVmn59x\r\nJwixqFC7ehTkVT+VcQQ6498dBWVHxUfQBZhMoxuxWJR2+3EX2+cewwQJ4HLv\r\ng5v3wypCY5r5O3idshejfwRIBqkNnrAqzqSwoKoUz/Qg7IfIxWAe8MIqJuFE\r\nflcEqS1P48iZ8r+Fik3txa1C2pXZTY0ffp6b41yXYp/QoDvDRfZDdchs9mG7\r\nr1m8VJ2Cc54NnOsibh81Yk3TwKRrjFrX+lE=\r\n=7Cnr\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "16.0.5": {"name": "cacache", "version": "16.0.5", "dependencies": {"tar": "^6.1.11", "glob": "^8.0.1", "ssri": "^9.0.0", "p-map": "^4.0.0", "chownr": "^2.0.0", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "minipass": "^3.1.6", "lru-cache": "^7.7.1", "@npmcli/fs": "^2.1.0", "fs-minipass": "^2.1.0", "infer-owner": "^1.0.4", "minipass-flush": "^1.0.5", "unique-filename": "^1.1.1", "minipass-collect": "^1.0.2", "promise-inflight": "^1.0.1", "@npmcli/move-file": "^2.0.0", "minipass-pipeline": "^1.2.4"}, "devDependencies": {"tap": "^16.0.0", "chalk": "^4.1.2", "tacks": "^1.3.0", "benchmark": "^2.1.4", "require-inject": "^1.4.4", "@npmcli/template-oss": "3.4.1", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "149f02b276ee4773896d147f6b1559680c62cba2", "tarball": "https://registry.npmjs.org/cacache/-/cacache-16.0.5.tgz", "fileCount": 19, "integrity": "sha512-8s/08Kgu5sk9JcmOrekdUTM7cPPewz2FIQWVQOGOCdWPMRxBUD10WXApQD39Qvg1y5AKXcjo+pnOHkeveEFHCA==", "signatures": [{"sig": "MEQCIAgaHo/VaTupYZoHE3zo659R41w0zYVDKqTsBC/X4jSeAiBWf2zeKi6mxs9GRLHQfvQNmNxTVvDcFef7tKZtmqnsdQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77185, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiYHZ/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpdiw/8CpGUQuEIKisi+GKCQvWwb8vCwOlj4aZJtqxwgswvtjcn+Gok\r\nVRJHk7xyO5alWzWLi28uHcFTMNxC4DRZzY4LRhL6E9G1lgwtxsL5naATG7r2\r\nnBXWTFFBV9KwggEXHt87CRIhtONqPOMhI0QpT4QLoAV6MnYA72iQ7j4JSExA\r\nes3MmEL7SuVROQJ6ZE94RbnP9xnR2Gl6Xo9tXROcGy4t7+gISA6/DfDxmZRn\r\nelpUdK9fHutAT9JXDAZ55TrdobGI5p26kVi79VTDEn+BtcMBLPVEKc4IRQJJ\r\nu6ViP00kCAuskzgmbcQ0v+U053EL4Swt75NEI1KNKipAaFTJ8cwC7wERLXq3\r\nRSb9Kyd4o8i4PF3sn36WhpAYtfYibzEZGnON8PpsIcwWRJKMbcwoyA2HoLRf\r\nKsmyGm0nz9QZ+yS6s+Cr6WGOoSoz46IZzIxX2Q9beHGj1KgvWjwaEwPOUiRK\r\n7X3CJuut/nvL0LJHqB5pJBbH2JLtdVNYmUoA9zZRI67mmyFItiDKXVNlwOOV\r\nF90mWy6zz2+pfL2WlBPgpEFqenobAHbZUX6FQIMbavUcwt2XFtw5awuzmqDi\r\nJycaTCow0Qd9G2DPmP3OItvfthm2l7eX1FLvM3PWntsuMxIEBrAGsNz2h+mt\r\nfjqKSCSieJ+uCWPTeF2Tv7FcHiwRAUKUqFQ=\r\n=Cz6u\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "16.0.6": {"name": "cacache", "version": "16.0.6", "dependencies": {"tar": "^6.1.11", "glob": "^8.0.1", "ssri": "^9.0.0", "p-map": "^4.0.0", "chownr": "^2.0.0", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "minipass": "^3.1.6", "lru-cache": "^7.7.1", "@npmcli/fs": "^2.1.0", "fs-minipass": "^2.1.0", "infer-owner": "^1.0.4", "minipass-flush": "^1.0.5", "unique-filename": "^1.1.1", "minipass-collect": "^1.0.2", "promise-inflight": "^1.0.1", "@npmcli/move-file": "^2.0.0", "minipass-pipeline": "^1.2.4"}, "devDependencies": {"tap": "^16.0.0", "chalk": "^4.1.2", "tacks": "^1.3.0", "benchmark": "^2.1.4", "require-inject": "^1.4.4", "@npmcli/template-oss": "3.4.1", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "3e4b1e8196673486e725a66eaa747a91da753963", "tarball": "https://registry.npmjs.org/cacache/-/cacache-16.0.6.tgz", "fileCount": 19, "integrity": "sha512-9a/MLxGaw3LEGes0HaPez2RgZWDV6X0jrgChsuxfEh8xoDoYGxaGrkMe7Dlyjrb655tA/b8fX0qlUg6Ii5MBvw==", "signatures": [{"sig": "MEQCIBlRDAERvK3N75FvF3ZzvTo+fd+oum1CQvJCr1hXqxt0AiAwiEwW5UsiNwZu0aeSEMvyY5B+Np6zD1Tr3PTu4rEuCw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77252, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiYYM3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpfDQ//SRvwQqHK8jyg8TFYar5DOa+m6mYMgP7j7HZByzY3dRpi2Z7L\r\nCbuEnIQ4n/St7KSNVpIp2MqAjDiIZJnrrTIvIja8KonOl1I3GiM71M2qh5gI\r\nEzABP78pG/GRK3lS0SJeq4cM4cTRDNzjj3FDIU3xA8LnPJL+CdPCVtBZqT4U\r\ndqxhNRLqLVCG2mm9yXLxAGYslF1TNzxjNgvXBTSVJgNNHFp51AnHg1NcwcSa\r\n8L8IUzLtyz5MScQCkN++j53s67GWklGnQMkRBDHyNLbOQe1bP4rhEd0Ia7bb\r\nrjwMnsvF+eqiISB3qDMzE5ZUFBNBgfUggaBwgiprKYgFzJnKPQH/+WiclMaO\r\nyOrnrsNHVsm4BhN5oluB8dyDvcm0WrGvuFWzRYwPvja/FOgJsbiJEL86jxg0\r\ngqY0+p+i4YogNBNEdLNiRMNcuQl3DOqQKp1akZvvwWThgSbW1WcU4dFxtkn4\r\nakDU9vWMm4KXuqgFK8Nnzbt2sULpLvvbTJefa31kGw0yaUoIVfysF6rMLPJU\r\nOxl2L4263g1RcEM1SXO33Tv5vzMdLJvfVyOOSXgdU/789p/HdFBynPQQTvrR\r\neyo0uaAmWr3JeD/HyHDGvs5+ZyK+PFo3EBj6ni/AUe8MTEMeu3DZRJdEZJ2I\r\njyJvR4lJfP0TkwMQ4jZRuxeo3bqzHNkpNCI=\r\n=3V51\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "16.0.7": {"name": "cacache", "version": "16.0.7", "dependencies": {"tar": "^6.1.11", "glob": "^8.0.1", "ssri": "^9.0.0", "p-map": "^4.0.0", "chownr": "^2.0.0", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "minipass": "^3.1.6", "lru-cache": "^7.7.1", "@npmcli/fs": "^2.1.0", "fs-minipass": "^2.1.0", "infer-owner": "^1.0.4", "minipass-flush": "^1.0.5", "unique-filename": "^1.1.1", "minipass-collect": "^1.0.2", "promise-inflight": "^1.0.1", "@npmcli/move-file": "^2.0.0", "minipass-pipeline": "^1.2.4"}, "devDependencies": {"tap": "^16.0.0", "@npmcli/template-oss": "3.4.1", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "74a5d9bc4c17b4c0b373c1f5d42dadf5dc06638d", "tarball": "https://registry.npmjs.org/cacache/-/cacache-16.0.7.tgz", "fileCount": 18, "integrity": "sha512-a4zfQpp5vm4Ipdvbj+ZrPonikRhm6WBEd4zT1Yc1DXsmAxrPgDwWBLF/u/wTVXSFPIgOJ1U3ghSa2Xm4s3h28w==", "signatures": [{"sig": "MEYCIQDsXOxl1/ShfbdFt6cCZfUCSbX29VEkWTEgA0eZ7GS5LgIhAK49OW/2GbAajDyX/TSpqE5tAErFvtRbC7XGcCVwd4ky", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75549, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaZ30ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpz7Q//TPN1n3uVyEn/0U1zikLjz8+sTDh8GhovwPtZiYOnDx0dE7tV\r\n9F/I4lTCCtUH1DlhYZlclbuQIUhuIhhbXk4ZBqC9sBkTS1KKPxYvOw3f2l8P\r\nVZWft3hnRrLycWAOrbKnzKXg8PlSA5Z823d8SHmAqqp6h6luPJ62XhZ4zGWS\r\nzT+O0zOk+NXGY8cOa9V1JkB+SadHGGCiIjl06E5lZQBeFUaUiTGeBfmYZtk4\r\nGAJNqXVTBbSe7SLQISaacoPbORcOUvyy5ALbmnMc5fjp7LG1PANFN0YO3VFO\r\nbxUzoyY+Tjd00x3rLrl6c5evlpchOgUhmolLyTlidcRQrTNSJcpKhp6kscxx\r\nBYO5RQaMP2j5zC5YyFvzo5AB7Zks3jXfva0BosJNHBk13luRVKsZcxWZBu5f\r\nUszUZszLmvXVZw1vY3O5I+lQcxGZSKwXXRb4DrKilHnkyj9m/XU8pnn0JpVK\r\nhLvyGzEf0gxABWCzeL9cMztJ5nXKWMUwsQdtIMQ+EPqIY3XIwwF7kAOu39Gc\r\nSJWO93mQDr4FIp/Mq71d8W8qcBYCwrg4wxkNXz2I5/IH7B4fPqYrcjMvZpsX\r\nVlAJ49ORA14c6Su4feH/KxEyTRIxJMJJnuyRr5xoxwCcuMHmiACSteVAPVG0\r\nU1JupXuKKyEtPo8CKIkGnk9nEf78hDv/lvI=\r\n=j+W8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "16.1.0": {"name": "cacache", "version": "16.1.0", "dependencies": {"tar": "^6.1.11", "glob": "^8.0.1", "ssri": "^9.0.0", "p-map": "^4.0.0", "chownr": "^2.0.0", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "minipass": "^3.1.6", "lru-cache": "^7.7.1", "@npmcli/fs": "^2.1.0", "fs-minipass": "^2.1.0", "infer-owner": "^1.0.4", "minipass-flush": "^1.0.5", "unique-filename": "^1.1.1", "minipass-collect": "^1.0.2", "promise-inflight": "^1.0.1", "@npmcli/move-file": "^2.0.0", "minipass-pipeline": "^1.2.4"}, "devDependencies": {"tap": "^16.0.0", "@npmcli/template-oss": "3.4.3", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "87a6bae558a511c9cb2a13768073e240ca76153a", "tarball": "https://registry.npmjs.org/cacache/-/cacache-16.1.0.tgz", "fileCount": 18, "integrity": "sha512-Pk4aQkwCW82A4jGKFvcGkQFqZcMspfP9YWq9Pr87/ldDvlWf718zeI6KWCdKt/jeihu6BytHRUicJPB1K2k8EQ==", "signatures": [{"sig": "MEUCIQC1km3tWBtLXGrVaMTKsObP4LOvWGSl4lesa3vXTCuYSgIgGb5Vb12ov4kWSPMj8NoHhYDXsX0znuf/q1L/vCQ446c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73358, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihAFyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrv7RAAiXMefRBe0CwrmsOzKdCwZH97y35IiLPJWoEWODwApKcH3TTA\r\nI3Z8Hx6RlNZjImQIIQu0w5IYr+QREVcK8I/OOk8KPOhn1TYtJqCUWNtD/yDz\r\n23NQNHID3/Oi/jibTVutxXC6/Nn2MZOKtrJbFQSip06gAVTL8jW4YnopDc28\r\nzgLzPPMSToVdf2ujD/wCOhhWIXARKx7TOaooYqnVErPUvfx17yWM3H/8PF14\r\nZhw0KHwC+GoUjp0a+xVhwx+Ha+EsbifzHdyDd9/vwe4iUQ61UqJi9s1RZuxF\r\nw+/0EqJSig4GDjkn8ad27/oVle0BzddoapUfIuOzPMtzIWtWC3v10L3VK1Vr\r\nY6biYOEuon7ezd5IiXUeRcAc4n6PzZsaEQqHo6dNP/lef3bDbLWB3+MeZEA6\r\n19uaJhtT4HVlPgAdEEcYgENn72+xzpU3cI0WzQsiqsDPN9Px5bB/IATg0ZqN\r\nxzuBTjs+5vFyxK7laxiqkNLphEXgqyDSi1UhAT7CXfJ90NUUBDi5SKTBXQtq\r\nz9fXnt7OPpVEUsPwxFN7aF4PKSwZUewc0SQ8NZq2m+wgdBL7hziWFXYYIPtF\r\norXQ1gsmAjQzbaaR1rll+LMBx03A3xje5Q/PVtlzS4AZexUJmGjT6j4eKrWd\r\nZvv0ym/L9sKCbNbHwcOhOXInk1SyyfnVyuM=\r\n=wdcB\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "16.1.1": {"name": "cacache", "version": "16.1.1", "dependencies": {"tar": "^6.1.11", "glob": "^8.0.1", "ssri": "^9.0.0", "p-map": "^4.0.0", "chownr": "^2.0.0", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "minipass": "^3.1.6", "lru-cache": "^7.7.1", "@npmcli/fs": "^2.1.0", "fs-minipass": "^2.1.0", "infer-owner": "^1.0.4", "minipass-flush": "^1.0.5", "unique-filename": "^1.1.1", "minipass-collect": "^1.0.2", "promise-inflight": "^1.0.1", "@npmcli/move-file": "^2.0.0", "minipass-pipeline": "^1.2.4"}, "devDependencies": {"tap": "^16.0.0", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "4e79fb91d3efffe0630d5ad32db55cc1b870669c", "tarball": "https://registry.npmjs.org/cacache/-/cacache-16.1.1.tgz", "fileCount": 18, "integrity": "sha512-VDKN+LHyCQXaaYZ7rA/qtkURU+/yYhviUdvqEv2LT6QPZU8jpyzEkEVAcKlKLt5dJ5BRp11ym8lo3NKLluEPLg==", "signatures": [{"sig": "MEQCIEa+PxKhqWXWGy1uQFcznrApQlv7MwMnq0PT0gUoplreAiAMXdoSOxuYKT8pnrzTeeZutxkeWg/Lzd5xA8BLOAYNLQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73353, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJimO9mACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrHJQ//UZOTC5isiGefZ2xKr0JyRkIsO1u3kW4p1Z3TBsscr4rC8YcF\r\ngYW6xWBqKUNDREIVrC2h4FJKcr2gmLXeF9qjqPZlGCr1yFuKU8skVA6nuDix\r\nqMKZ3z8Coj623lwENSDa3ZYyVv0Fzy/vEeDheHxhjs55YP7dl6AWFHb639tj\r\nEk2MdrPrIhdnFEQpanxVeM8gT99129jMv0ViN1ujS1ZudOq/GO0rwZvmrzFx\r\nga6nz73bVQvw1n3/udP8aMzYecMdO5NBSZem9mUyvmRLkw7cKfPERSjdoMhQ\r\nh8NCUCGEiZCuNlhaV+oMm/RhoZl0CLhyZP6bVXkw7kkapTtE2R/DXPBbBXLL\r\n6cNNOZL6mNszlOZDnjWWUX9ybPC2BmwGBVXpt7JH1LPI26P5l1OCEYT7rpJL\r\nObLI/rQWMynlajhDDNa/z8shqwGVaQhwqX6WGVSqKBukXxs2Qe6aA8B+SAHW\r\n2dqXdEW/tU7L5haNvKVfyuqg5S87+obp0BU58OvRH9Xm71GZYNn90MjWafID\r\nF+4LWtFd7y8zmZt4NxaTZFXkufF2IlPtSr8gA8SXL4Rp8YwAKLeSw40a93JE\r\ndCZ7y1T05ub6Hv0AmvygPz7MZAsz07DcAh9h018wU7XhDBNoXhGL1mhM7mNG\r\nb4RudeNGPW00MckVPXUMxUXCmT/xFXigKYc=\r\n=0L8n\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "16.1.2": {"name": "cacache", "version": "16.1.2", "dependencies": {"tar": "^6.1.11", "glob": "^8.0.1", "ssri": "^9.0.0", "p-map": "^4.0.0", "chownr": "^2.0.0", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "minipass": "^3.1.6", "lru-cache": "^7.7.1", "@npmcli/fs": "^2.1.0", "fs-minipass": "^2.1.0", "infer-owner": "^1.0.4", "minipass-flush": "^1.0.5", "unique-filename": "^1.1.1", "minipass-collect": "^1.0.2", "promise-inflight": "^1.0.1", "@npmcli/move-file": "^2.0.0", "minipass-pipeline": "^1.2.4"}, "devDependencies": {"tap": "^16.0.0", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "a519519e9fc9e5e904575dcd3b77660cbf03f749", "tarball": "https://registry.npmjs.org/cacache/-/cacache-16.1.2.tgz", "fileCount": 18, "integrity": "sha512-Xx+xPlfCZIUHagysjjOAje9nRo8pRDczQCcXb4J2O0BLtH+xeVue6ba4y1kfJfQMAnM2mkcoMIAyOctlaRGWYA==", "signatures": [{"sig": "MEYCIQCdYJFWizItBmR7cS2pkKbM6ZA7gQN2XcPwgFJ/vfPHJQIhAKv6ZTxMKiDGEEHP9yQ9rzxB4inN23f9OoY+jUCoH0pa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73514, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi+qTfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr9wxAAlmtt9p2IQuOYFlnI8ibLNqV+6Xedp00xV5VA0w1aSyIK51i+\r\nPZUem4QDdAT+55tDV6/TRuQJst9TBhot2QxAC1mdXhNeI5/MU/+Vjli/wRrh\r\neFqDraPAtp7cm1Wh+TEDFOUOem9BXLxZQbkTbhv8L9W0ndjTy72DXf56AZvw\r\nQhb/UQd8ARxHkKnCnPGa19T81Gl8Vgx8iLxcHSd0oLfVL4L3/jPbz1eLzWu4\r\nXhk+ol3rs4Bad9fEfSD9mK457+N8rtE/E6fjm0R4lgsoWep7wF/nXHW4yIUF\r\nHQ5pOpgWT2bL7cbt0M+VZog81QKNydwuH8BXQ8Tcim6JwUqaUnHL6S0Xu1eU\r\nC8Iz84uKedW6IjPxhIwYk4GR7pko8jxtaFfRrkcJlJiQjRGXaDJ4N5Og6PUX\r\n5sLdjTzqaYQimI1D4wEDjWn8wLtG/Xuq4EMijIDh02YeEmeLlAql18uC6Lzz\r\nnoQMlB1LfF3mCis/5VTR0NI/EYOp/NWiomQ7FiAHL584meZhewMetOZiXi6t\r\ntXmCzvj2/x69uaFIrl2zM+mGNjyVbvxuEBAQzAOA27gaccFsdou0AJf5HlTy\r\nmIZOAh0QYfVXa3/pXf7VoifJ9F3gbe6Y/bWDiUvmA1F0soclpAXoiQ1xjQ6p\r\nmvz/T2JSmsCm1v0eY+pGnuMY46+OPdOWVP8=\r\n=o25x\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "16.1.3": {"name": "cacache", "version": "16.1.3", "dependencies": {"tar": "^6.1.11", "glob": "^8.0.1", "ssri": "^9.0.0", "p-map": "^4.0.0", "chownr": "^2.0.0", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "minipass": "^3.1.6", "lru-cache": "^7.7.1", "@npmcli/fs": "^2.1.0", "fs-minipass": "^2.1.0", "infer-owner": "^1.0.4", "minipass-flush": "^1.0.5", "unique-filename": "^2.0.0", "minipass-collect": "^1.0.2", "promise-inflight": "^1.0.1", "@npmcli/move-file": "^2.0.0", "minipass-pipeline": "^1.2.4"}, "devDependencies": {"tap": "^16.0.0", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "a02b9f34ecfaf9a78c9f4bc16fceb94d5d67a38e", "tarball": "https://registry.npmjs.org/cacache/-/cacache-16.1.3.tgz", "fileCount": 18, "integrity": "sha512-/+Emcj9DAXxX4cwlLmRI9c166RuL3w30zp4R7Joiv2cQTtTtA+jeuCAjH3ZlGnYS3tKENSrKhAzVVP9GVyzeYQ==", "signatures": [{"sig": "MEQCIFlRjxgz5KQjF1lQ2QasX7jYlj9784vNVboi7GYl5my5AiAtsAj3U72mT3btjYy0xZihAF2rKXOiVLyROfEATp6ntQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73514, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjBTCDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqWww//dcfkWj2UDGrPiSVloOGNt1eWJTUbd0j3xvbmiajvzbo0zg4v\r\nR8DgM4kCzSKZD0C0qmPccwKU5T7d08sKr3l9mhWfPLdxrSrNG6qnjgxDwdvQ\r\nNeDKx2tR4UYSZoVcjRMHzOuUjZY/rhS/+Gk0rMELu/+LPppJvU8gZJe1PBWc\r\nTqIryjTsgpj7bUsMTrclg6Z1vNocPjOHQAtAxsTs18YQFy3yrgkHZcTKVtml\r\nNicHGrcwbVXLfsptirbGG5ARYFSK8IVh190SZd+9YTGDX0Odl07NbMItgnfV\r\nlWfoJ4iQA5lubbpNNb5AEBtbLAuWk1SATcEiIYgpiDtIELz8vHy701e14D1z\r\ny5IyFSQ3pfvF78EemcZ8FhmD8T9ob1JuRowoWL2vMeKjbQPduYwy6wynzQHs\r\nPqBfGJ0vlUnzQiR27cmEsbpf8XterNLwJYCJfsek8YvxitORU9fPbA7RwPhm\r\nEciuG23/lJ9ZfRlAnYkgiXX8OilMD7eQgiOozRSMVoQhS7dFvFyjXkNLqEQA\r\nyloSQsfJZKAnIL5GOAAFHzYRK702JGKqw8uaq1hy1X9Zvv2PqrGMC6fJfSg5\r\nZZmb0ErmmMYSZZvRaptAxGieqd0DjnvU6GYoL/H2HsN2p5N2hXV5lVGrYs9i\r\nDUnXN8wNPLugg3FmQQVRO8rwvcqJQoW9FwE=\r\n=ld5z\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "17.0.0": {"name": "cacache", "version": "17.0.0", "dependencies": {"tar": "^6.1.11", "glob": "^8.0.1", "ssri": "^9.0.0", "p-map": "^4.0.0", "minipass": "^3.1.6", "lru-cache": "^7.7.1", "@npmcli/fs": "^3.0.0", "fs-minipass": "^2.1.0", "minipass-flush": "^1.0.5", "unique-filename": "^2.0.0", "minipass-collect": "^1.0.2", "promise-inflight": "^1.0.1", "@npmcli/move-file": "^2.0.0", "minipass-pipeline": "^1.2.4"}, "devDependencies": {"tap": "^16.0.0", "@npmcli/template-oss": "4.5.1", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "76be0cc410879a37ec08426cbfcc216a5c2f26a6", "tarball": "https://registry.npmjs.org/cacache/-/cacache-17.0.0.tgz", "fileCount": 18, "integrity": "sha512-5hYD+c8Ytmymo9b8tPgYWrWfHOzaO8M8jMUkyiEYfeLTwo70MUaD/yA1pNONi3upx02JIn3mtpDuSCXoQgtlHw==", "signatures": [{"sig": "MEUCIG56f9Q+oWq98M+0U6ooduDdW0LSf+p5a4VfMc7oDaPjAiEA+5m1nWCvw0X4jqvOflU2m4Yo4/LGCkDiKivyD2+iVok=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64953, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSFwYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqiXA/9HUHzSak3gqhYhzDSRFg6n2+CWE2J+pdHPmB3aMHY74zf+Z86\r\nT9G8hV4a2R/zpsiZBBqR2wXRInK5+TBbEVUkZDfJ8Lmz/WjVUvYpXduesLSB\r\nsQWcbIc1gtGZ5wpsHWh/ZxzmPmyuV6KNqSicbbu2TIstsyECiYiOTUo9QnFy\r\nui51BWAmIaj+OsLv/Pl1+GYP3TEb/R4CArO7ze9tsLPGpH2qLZNhpTb6aT+l\r\nldYuElnJSf0rx19iZvcmQ8xVnUummNnPlpr+Eddzsi0PBoH+51ZXwjgGi86g\r\n0FGSXWfb7st6ddXAAIt8FkvFtqTj5vRiwUDMUN2bnmv2b+3lyphBZSGCmsSD\r\n0zJJG5k9N8G2ee0DPqJ6Op44YRnXuq5m4t95nLo6he6F330amqOPIR9I+F78\r\nHCZfSu78KH8E1iRfPPLLJ9pFqmvtmjv2eA3EJG5A2sJfJeUtU7kGaa0vIK8P\r\nbfR0oUCGTSxH6N60zoH2o9ROMVzgbHbn5dbshnyYXw8hJ5HL7YBp+T15jEDG\r\neU97iljo7uAaEeHySOBIOs8q8WoWGHJL0xbxxLTSdDs2SwnhWAWvaH3uqPee\r\nXikHFQLz6tmdny88T9uzhFA/k4e1VXFoiiE+lU5GCCJ27KA6N1SuDPylcSFt\r\nzI0r+7iiCfhMv+xit2SnRu12iPGbPFUq/NY=\r\n=LREw\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "17.0.1": {"name": "cacache", "version": "17.0.1", "dependencies": {"tar": "^6.1.11", "glob": "^8.0.1", "ssri": "^10.0.0", "p-map": "^4.0.0", "minipass": "^3.1.6", "lru-cache": "^7.7.1", "@npmcli/fs": "^3.0.0", "fs-minipass": "^2.1.0", "minipass-flush": "^1.0.5", "unique-filename": "^3.0.0", "minipass-collect": "^1.0.2", "promise-inflight": "^1.0.1", "@npmcli/move-file": "^3.0.0", "minipass-pipeline": "^1.2.4"}, "devDependencies": {"tap": "^16.0.0", "@npmcli/template-oss": "4.5.1", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "bcba581a1b311b552b21f71d6205c67551a8d6d5", "tarball": "https://registry.npmjs.org/cacache/-/cacache-17.0.1.tgz", "fileCount": 18, "integrity": "sha512-HRnDSZUXB5hdCQc2wuB8eBQPe1a9PVU2Ow8zMTi82NGJZmBGNTSjEGzetlndKlqpVYBa4esdaJ2LH6/uOB4sFQ==", "signatures": [{"sig": "MEUCIH8QaPMXwyNcihIQxtfW7YsaUb74oqBiiSn5o0NjNQ1GAiEA69L/XvkOvevJIVS6FDjTP8BOwDC6fP3/T3NaQ7JEKjI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64954, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTa5/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpBIhAAms664cOOSVNx8AR31W435FP7FXHu707c7cj7xMKqAJ6gLFTE\r\n1k42gUb4j3rPix5bB7FHNst155pXP2V9MNXconIodX2FVSLeuyMKJ7UtTmQA\r\nz6rmufTQXvDufFRwmlY+bqJZu0pY9jFZgAcaHGVycIIadlyanF4l36K0j2Bg\r\n+FxAgOiNhyib4YQWcl0lMmYAa3f6xt0ULKVXVbUgOGwqAetX9fzS6uS/gjSq\r\nrOhNchPGcXSkkKB6Yuq60RppDkgaAHLvgpwJQEIBo919gQtV123tnEHKVxFs\r\nikyDseMxdB4N7g2vugptBrr/xexTn4DjKYFDcS0RriGypuDCVpH0lavkOzPW\r\nbH1wGwQsS2hVjPtlLWKeGsp6mWXaCwXVifEXeLCRjzOOP1FYlsMyBeSHWqwq\r\nilTpyFgXdMoeGd1/gMIH3JfDlJilQ99Ht3xNPMSfKkpQpPqz6YnwhlacAWwt\r\nw+Denf5qSe0TUIsX2Ak79Iu579p307qpafnfXMEFtmlcI6fyXpCtIvy3ksLb\r\nFjQ6c9u8S5r6UL/AdKLmLuG2v7z+5Zi82sHB+j8OEy2ri/vR3CcNg1QY6m8h\r\nlsD87GUSwJJ7/gQPw9J9IMqADm+JFRsLtVKv7djH5Hr+w1OEbrrRQ4Q10rvI\r\nKiJXiasvsURwmiFXDGa/aYYF4GfCaRiGg6o=\r\n=BT+j\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "17.0.2": {"name": "cacache", "version": "17.0.2", "dependencies": {"tar": "^6.1.11", "glob": "^8.0.1", "ssri": "^10.0.0", "p-map": "^4.0.0", "minipass": "^3.1.6", "lru-cache": "^7.7.1", "@npmcli/fs": "^3.1.0", "fs-minipass": "^2.1.0", "minipass-flush": "^1.0.5", "unique-filename": "^3.0.0", "minipass-collect": "^1.0.2", "promise-inflight": "^1.0.1", "minipass-pipeline": "^1.2.4"}, "devDependencies": {"tap": "^16.0.0", "@npmcli/template-oss": "4.8.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "ff2bd029bf45099b3fe711f56fbf138b846c8d6d", "tarball": "https://registry.npmjs.org/cacache/-/cacache-17.0.2.tgz", "fileCount": 18, "integrity": "sha512-rYUs2x4OjSgCQND7nTrh21AHIBFgd7s/ctAYvU3a8u+nK+R5YaX/SFPDYz4Azz7SGL6+6L9ZZWI4Kawpb7grzQ==", "signatures": [{"sig": "MEUCIB8Oaei/g6yWVxO9frxRE731BosYayeMOy7d2oGlIzeyAiEAtt18Csd7bS4lm7GY5L1l19A43xtbyfLnJSAIzWVIbyE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64923, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjZIvWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpiUw//QAsrOjAq4yJyICviLgrmxK5w/ba5QLI7+uOm7/0q2v9qe0Ao\r\nt69c/0B3GOLiVBav9UTvJjjy3AzB7urU9Tm5hLF8VE+j91kAd/NrKzyfjmhz\r\ndN8A/QmKoCAXRIQNya0HC0KAZohIgOG4b5gxRvmpfVCpL7GLdyo1qw7f3Nrm\r\n0n5IeuC3U1rVEnAucFDfkiuP1IRXlt+BZgs4xtoOPRGiQ0MLLOU3ZtsARk2X\r\nnIX/u4/Mecc5ifcXwtY8KK6agoY3Souk1Q+gjEz35/0koicOrOwRM+kf+0Lg\r\nYTeqnX9LjTGWN8xy2fRtb9LlQsUboo7H3R/5XSVyP6Xd/djFccys0g+QPrmC\r\nAaxOMMc2vzkeJgJxRp2AubQVwDo555UC3DLStFDkSw4iI7YmctxIiLzmsmNb\r\n2ue79iYQmYDGFsppsL8uwwEg5aYVYThHfsiYSxQLDIU3uH650W2LGmWMxk2H\r\nLYBdjwXxtpFieHHYVtnKLj3al23J++TBiVseHASzPejm6mit9grXLfNI/gvQ\r\nYRrYEbdP7o94M79S89pFO7L2ciwvxGVeoqdtUKAJBSoG7tNWCQFPKITy0vev\r\nGntRkI4Xow/E4K7K/6arl1GCeWJGbNg/g8qtnxqLOmArw07yDnUQxTQ36aW0\r\nwDTtxIYz5v5lk5ovAblzLe199neczA73vvQ=\r\n=OMbz\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "17.0.3": {"name": "cacache", "version": "17.0.3", "dependencies": {"tar": "^6.1.11", "glob": "^8.0.1", "ssri": "^10.0.0", "p-map": "^4.0.0", "minipass": "^4.0.0", "lru-cache": "^7.7.1", "@npmcli/fs": "^3.1.0", "fs-minipass": "^2.1.0", "minipass-flush": "^1.0.5", "unique-filename": "^3.0.0", "minipass-collect": "^1.0.2", "promise-inflight": "^1.0.1", "minipass-pipeline": "^1.2.4"}, "devDependencies": {"tap": "^16.0.0", "@npmcli/template-oss": "4.10.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "9ba14e0e50eca763ae7e2ee89036a948a13035ed", "tarball": "https://registry.npmjs.org/cacache/-/cacache-17.0.3.tgz", "fileCount": 18, "integrity": "sha512-pwsIK/grdM0cHpfVaNFxMdzElBtLJGsVPE+JnxqXP1l40O01Z1mfDE8MbRTuwomGq/UwEtGisOylutOEVJxEqg==", "signatures": [{"sig": "MEUCIE/1DlQF2iTFasMgOwF1HrOob5DfZRm7N6KdXpmeU7aUAiEA1j48fzeqD4QPNYBGbpk6CVRmRsblyaBCJkJ7rtMGB8k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64925, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkPhsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqXIRAAmL1KbbMe+Vl4ypUhnJCxY1K2k4UNtIL935vbrSU+fCRQdfxQ\r\nGvELkoCE1ku1Bjr5N9gmnjOzsvQQyX0FQPPvO3sk2NFn9GM0v5sorNumH37Q\r\nJU3eZOzajchCPrKtUtCn3G3/mfqlXsqtfb5x64XaBzUnwPkJtDobRSMyH4VY\r\nnSdkKmQHri4iQHC3LxB5lTfwdN1gwkzfehX1QHyLMTlbn6aC5/04yuSdaucR\r\n3+QyRyvy03u5V5sWshFRjzJ9scXhvH+Ko9apL/8OR+ijaXkk6FRndP0itYjS\r\nPsvaiR0SDlh31ZOWx7LQ4JwN0b/l0j5v6itu4K5+yOmVm2LO3ZKIl6jt3T+d\r\nnHZ8HqJrUl9T2zfienU4vnbSPyj8eIDPuFO5S3C8M0bnpW30MuJQ4Oj7xsv0\r\nP9zQ5LQPPpmU4iGkfJPr7lapYUN3rzE3WF9ehggBAi31KGurC7z3WAl0KsOA\r\nRv8dyIZuQ1ZRgPzTlW51UFLqecPcZUyIg8LR44ve9DVDc594s2KxN1h37hEd\r\nOuFaPAk7lp5rAjxuDmfGPnB4+fYc6U1OGgC4dN14TLOcKZ8JRJIhtoaIoiqP\r\nmD9CDRKrhRFk9rM/Xbup9C3vRroJXNPTUvydQx0hKGkBV83NaSBX79uXhHMi\r\ns5U2cYZdQvVna+iJS759cI4B+hol/9uzN6w=\r\n=nYf3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "17.0.4": {"name": "cacache", "version": "17.0.4", "dependencies": {"tar": "^6.1.11", "glob": "^8.0.1", "ssri": "^10.0.0", "p-map": "^4.0.0", "minipass": "^4.0.0", "lru-cache": "^7.7.1", "@npmcli/fs": "^3.1.0", "fs-minipass": "^3.0.0", "minipass-flush": "^1.0.5", "unique-filename": "^3.0.0", "minipass-collect": "^1.0.2", "promise-inflight": "^1.0.1", "minipass-pipeline": "^1.2.4"}, "devDependencies": {"tap": "^16.0.0", "@npmcli/template-oss": "4.11.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "5023ed892ba8843e3b7361c26d0ada37e146290c", "tarball": "https://registry.npmjs.org/cacache/-/cacache-17.0.4.tgz", "fileCount": 18, "integrity": "sha512-Z/nL3gU+zTUjz5pCA5vVjYM8pmaw2kxM7JEiE0fv3w77Wj+sFbi70CrBruUWH0uNcEdvLDixFpgA2JM4F4DBjA==", "signatures": [{"sig": "MEUCIQDwjWEqvx0NsHz65owHKg+29xqgy7Xh7LZOMYTZFhBQtgIgUrl8Bg4oh2Npzboi0K18sC6RbUY4pE/xmJ/cS+lwTdU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64925, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjm3pUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqlQxAAhDVf0mY0NHA7Eem6ZCJMyxR+P2YfNqqvi/3X5qaWhDTejH1i\r\nXFtxs8He+6m4ZgOdsU0OMHYIqgCAX9HIEi4hYlQiwLYFOh03cUomx6p5UhBe\r\nFV5Tw/pzv1/0QlHB7ahDgw6flPqSe6qkPblrst9pfrrAunw05rhszPkcL/5w\r\nmJeKPt4UEhvpH59CavUIkLfzPSuy3xQHqMy2PhWlkY9Cd9M2UoGhQWqtwnjt\r\n0k8ExjZwGJEXMFaxV99ptQ+oVVJMJ0NfKjJ4VplrEoGN3I2Okh9WWPhX/PmV\r\n1JBSsKUpXq4UwYixwY+tldZRPATOUV3h0qdcbwZ4L0Uu+LyTGZx/QiVuckYW\r\neROQX5rN7tR3R8o++BPtA1lzT7JcnNBvMFLB4Wg+VHUOmRHaER6X4XgZ/GzY\r\n70ZnGTvlZp/YCAADbpIEGh/fnk41P7BtFAnanwUzOveLqPPGPkQc916GVWMF\r\n51N+cbWjFx96pNwlh99xv6K6+boix4KRTLH9JPV/9Sm3W6EHIrFPZgD1Nw4A\r\n+9s527GsBFaB6FMvQHnHWOb/hmJaUIEBecv1OBXSltE5PvexjlVA+7W+XYso\r\nN/tRdm67sTypNmebDCHDnqoIh/Glm2qlXVINf97EQrZEx3YT82ynua7ss418\r\nddpe2WIbwSry7a4QDODxA8V897j//61GeqQ=\r\n=zdtv\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "17.0.5": {"name": "cacache", "version": "17.0.5", "dependencies": {"tar": "^6.1.11", "glob": "^9.3.1", "ssri": "^10.0.0", "p-map": "^4.0.0", "minipass": "^4.0.0", "lru-cache": "^7.7.1", "@npmcli/fs": "^3.1.0", "fs-minipass": "^3.0.0", "minipass-flush": "^1.0.5", "unique-filename": "^3.0.0", "minipass-collect": "^1.0.2", "promise-inflight": "^1.0.1", "minipass-pipeline": "^1.2.4"}, "devDependencies": {"tap": "^16.0.0", "@npmcli/template-oss": "4.12.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "6dbec26c11f1f6a2b558bc11ed3316577c339ebc", "tarball": "https://registry.npmjs.org/cacache/-/cacache-17.0.5.tgz", "fileCount": 18, "integrity": "sha512-Y/PRQevNSsjAPWykl9aeGz8Pr+OI6BYM9fYDNMvOkuUiG9IhG4LEmaYrZZZvioMUEQ+cBCxT0v8wrnCURccyKA==", "signatures": [{"sig": "MEQCIGDHRQv5hKP604ho689satt+9YqLuR8RO+GEqa0/XZdvAiBApWIoPQ7R7xNj1KRX5aJF3r/loRhE9Ko2rsE1hvB33Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64876, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkGerZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmryYA//aERMEHJ+7dTbEbVXh0pdJoktYOgOEytOdFYVwgOgVmWvWGif\r\nCpZ+Sln5RNanWUE0sEHTQjlNs1st8S5CSJd41mpl3cwPzh9boYZkGeWKCajF\r\n8XYcyRH52dVWOfAHhdZlNmUU/tWmimBnFjxPG2sZtKjuV5Hg5gCriPw9rrGu\r\nNw3XhzDyqQvy/c6SQnHwDBfWrHgEASPiFF/qiXoxUxCk9KYjgriAfVX9/AyQ\r\nbmLy8DWN7/NtVw1Je3GOiJf9suguff2Y/BK7FUfCHtKrdy4S2NcBm4sZBms+\r\n1HDo/hBfBGzAetnvabTBFrs2pPpR2ahy2lHU94oTJx8ku5EJ88Z+uaLtqRwE\r\n2LEPSG6N/nBWtF3k39emASGaLiV0ujbVZRVUaGATtIe9UYLH81cxm+2SUNcc\r\nygpPrXtX/OGXPqhzwz6pxTw8cET0ahWHeOKajoVx9i0WdZ00UScnaoQGqeY2\r\npa+V2RUA/r9vSzeXem2FNuKXHoU871mtgPGYsNVupoeQb4khOGfVBWHS+aLY\r\nsOuGBm0ibEDQTXhzKvVITRUH+L6q6hAcIR4PUksEuzfdG7ABF5oL7OceO4U3\r\nOm6p6NxlOODLAxzNb17pFC7EG61Gjh+UyFLb41aljpnrkVhWK5r8f2wX0+6V\r\naqtwQyQXXGaepKIGsxmzxzNHxznAkqWiy7c=\r\n=dcEe\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "17.0.6": {"name": "cacache", "version": "17.0.6", "dependencies": {"tar": "^6.1.11", "glob": "^10.2.2", "ssri": "^10.0.0", "p-map": "^4.0.0", "minipass": "^5.0.0", "lru-cache": "^7.7.1", "@npmcli/fs": "^3.1.0", "fs-minipass": "^3.0.0", "minipass-flush": "^1.0.5", "unique-filename": "^3.0.0", "minipass-collect": "^1.0.2", "promise-inflight": "^1.0.1", "minipass-pipeline": "^1.2.4"}, "devDependencies": {"tap": "^16.0.0", "@npmcli/template-oss": "4.14.1", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "faf9739a067e6dcfd31316df82fdf7e1ec460373", "tarball": "https://registry.npmjs.org/cacache/-/cacache-17.0.6.tgz", "fileCount": 18, "integrity": "sha512-ixcYmEBExFa/+ajIPjcwypxL97CjJyOsH9A/W+4qgEPIpJvKlC+HmVY8nkIck6n3PwUTdgq9c489niJGwl+5Cw==", "signatures": [{"sig": "MEUCIQDfqTLxGi4phuYlN1vSKf+SiSZUCRDf1o0yWdplQ1MP8gIgNNxd6upVyn2hCD05ydq5uguLJy9iSw/AFIMbZYDHe9k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/cacache@17.0.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 64916, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkSr6eACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq+UBAAopVISe3RTziAppSDWHWnG/XjL+IHqq98EqCp+14KnV+BiwDZ\r\nWWAiAbfaSFA3a5ge9zCoXJ4fPv7FvYRbwLFAMfc5ZxpnlCYVkDUxdo76iZAa\r\nGU4Gzb/fvDUwO38q8kJdE9vdoy4fEWYrLonCN6z+Mzb4yuMQQC7uKPzqeRUH\r\nO1vnnk3e197SR9gDWlU1/nJPrzImX4hq3IswNz6cYhUenNwFpmTrdV5J7a/M\r\nzG1DBHe2rFX1HVHZV9omHNlFnCnvSnzvQny6aKmv/uZf2HO3HYoklRYNFrlY\r\nhXx/Lr0Wc83OSKiL6QMPcVjmF57f0zbFaAe2Sg0Php87worhWRlygVOEO5HR\r\nKZohpGILXO5M3L7lgr5WliLwyYIHf2ZNWI6zd8MlJa+zyxD8TGyFR8/oJ1zZ\r\nYQSje8D2kTjkHcegUcLKQcmne25FuEzdDJrJ7+37ecCHPl85yUzAddiq5hKJ\r\nnFTYgcdHCm/lWbrqCkh2HfbN+0nSBhTINujK4Mgem1TD2GgiSQW4RuBVPBQ2\r\nq22/mcktS8iCuJ8SOe2hqIj7DnAlLflV5JG6+IqywYcOuCwkKXBeYjJyMP5i\r\nHyJdxpHOuIrKR8yipY0nsrm6o5ufGevXvg9w4lm7M0CvvlgeDDjHJy0BGSpD\r\nq3HDG51760CULOxzvQtizVT8DyS7GTyzbOQ=\r\n=y7F8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "17.0.7": {"name": "cacache", "version": "17.0.7", "dependencies": {"tar": "^6.1.11", "glob": "^10.2.2", "ssri": "^10.0.0", "p-map": "^4.0.0", "minipass": "^5.0.0", "lru-cache": "^7.7.1", "@npmcli/fs": "^3.1.0", "fs-minipass": "^3.0.0", "minipass-flush": "^1.0.5", "unique-filename": "^3.0.0", "minipass-collect": "^1.0.2", "minipass-pipeline": "^1.2.4"}, "devDependencies": {"tap": "^16.0.0", "@npmcli/template-oss": "4.14.1", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "f42bcbdec4886f55bb4e44e62a934b4792a98145", "tarball": "https://registry.npmjs.org/cacache/-/cacache-17.0.7.tgz", "fileCount": 17, "integrity": "sha512-2GdqQs7hl20V50cB+JEuGeR6YtcNsf1Y9+SP8YXjmGlZz4hM5Ds9s6mKo7e27r6sfF/6MhN4DKRrGldidJJWow==", "signatures": [{"sig": "MEQCIGcUEkUkC075449SkMJ0O6a40hm2pPQSBFFhyCLOuCelAiAiS1jW7DavafuTIyWJPQJ0dRSiMmDFy6pYVZd2OuaiOw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/cacache@17.0.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 63150, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkUCTKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqU3xAAkjC5ViScKF6Yyptr+Mmh4iLGSnGYqOnJ1DpyH+KVzl2K85yV\r\nvY+vGoYTumXe25XMV5+S/mSuHpopSGnwpzXTRmgW2jv+I+koFOE1RZeAZAgs\r\nSo3x45WxxvjNHhHyiXNIxQzKv/Psxirbw6AAJ+ke78EX7suckcw14YnX8lGx\r\n/8ubtHw7rkT5XPLvj5AjkmH46+fKiPOhDE7BBJUyk5MnzkjH/5K2/8IZtB43\r\n5jWAYSb3klctZwHJniUVe8kEQ/RHKbdCy2YJ0hLdWYgoZg3OobYjYPHTruiW\r\neuxNFeqmy2jLmmX76rOlRISCQZf/lKN9sJnha57/Hj/pRUrJjSmDJlxsK8ZJ\r\nftqtR4EgGwlI+QTtjj+JfXXKEXLMHeXyYbqth+Ip6nfcPqOD2dcXTilaBqc3\r\nljLqF2DU6KS6sTqBufFaE64kMVJP8466S8BV2rL3LGnDUdNqGnjaBD7p6v7T\r\nLP00OyJJOE6ecJ6nffQboD07v9p1RUeB6qc0aN9j+Ppuf69BW/2augQmPPzV\r\nYA/+Wr020VvIaV5Yvua7+clFcAWTwlVXXqQcnlLPs0lOYl4mYCA9U8r2cLbI\r\nJYIvoZe6HwuCFEc/pqBrJxBhjq8cz27COFm577mqMYiuNd7/qA4jZ1JZ6J7q\r\n/oTdjYTVtqSfUj4XXZPXzwjpoY/tX20ZRqo=\r\n=WzEo\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "17.1.0": {"name": "cacache", "version": "17.1.0", "dependencies": {"tar": "^6.1.11", "glob": "^10.2.2", "ssri": "^10.0.0", "p-map": "^4.0.0", "minipass": "^5.0.0", "lru-cache": "^7.7.1", "@npmcli/fs": "^3.1.0", "fs-minipass": "^3.0.0", "minipass-flush": "^1.0.5", "unique-filename": "^3.0.0", "minipass-collect": "^1.0.2", "minipass-pipeline": "^1.2.4"}, "devDependencies": {"tap": "^16.0.0", "@npmcli/template-oss": "4.14.1", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "b7286ef941dafe55b461cdcdceda71cacc1eb98d", "tarball": "https://registry.npmjs.org/cacache/-/cacache-17.1.0.tgz", "fileCount": 17, "integrity": "sha512-hXpFU+Z3AfVmNuiLve1qxWHMq0RSIt5gjCKAHi/M6DktwFwDdAXAtunl1i4WSKaaVcU9IsRvXFg42jTHigcC6Q==", "signatures": [{"sig": "MEYCIQDfEXwf13BB8+9EnEX7dIl6upnDqC3inlVOgTKHNj7l8wIhAMOnbONE9PCkUF1hBpywQhK1xF2FLPmkNTNmCjVsq4DQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/cacache@17.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 63165, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkUXX7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq98g/+IveAWT1LkBZIULeDxGcxy0J3rjflG13x2CPIQ1vSn6FcX3r9\r\nJRYg+a8cgTkUT4JlFeuhjzAeLqkkmbAgBcB+Y7jSV/pIWFlIIHdL020yCUCE\r\nLEKOLXwlMs3awf19nh3/zs6QYbKZR56bfPaa26sFhSdCtiVFzWVN5+wCrKHJ\r\n0kLJPaNA6m6XnPKDeCTCls6jvawz4S6CxhjBZDCgaF1aY4lPreCpwrKfKFcC\r\nmEjrfPJSlc4JXNxA2tp0UXpd46wDTL3NBtaGlVRDODiLu/KqykOcrLRuYFzv\r\nm0MVFolNJCtf+KGxUIV3sMgy8tep9t3eM2NSZAwjCC5uU/leCnXd9irpccpC\r\nmfvn8Q78LFbyr6nU5X/8137Np7bPPuQ4QVcdmP8CwfurTAhTtdqCSLIRcZXA\r\n4V2IDIO9wieBYUqxpiqYu+TmUcnYvlnA9NpbeuPvdaIrJwdu296JS5fmOV5e\r\nG/iBry5zS1y7yljBLNxQ6tDuUX26ngpah2S5RClmiS3FBB64SzvnfGJTOBcj\r\nPieA6Vx2A53sPoIZomWIaDXtb5/0/Ffsid+gkzKwrI02A3lKCdcuqF4zk0aW\r\nSliO2pYP20BDQNh0qBFALcAl0eKrF/hLGtaKsV18ewv3ffaDB8JT5ZeGAnR8\r\n22rRSv1D6ttgHspXXFAm7lbES6uVLdrr3m0=\r\n=r8v3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "17.1.1": {"name": "cacache", "version": "17.1.1", "dependencies": {"tar": "^6.1.11", "glob": "^10.2.2", "ssri": "^10.0.0", "p-map": "^4.0.0", "minipass": "^5.0.0", "lru-cache": "^7.7.1", "@npmcli/fs": "^3.1.0", "fs-minipass": "^3.0.0", "minipass-flush": "^1.0.5", "unique-filename": "^3.0.0", "minipass-collect": "^1.0.2", "minipass-pipeline": "^1.2.4"}, "devDependencies": {"tap": "^16.0.0", "@npmcli/template-oss": "4.15.1", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "f088b8cb6c10cfd3cb9e5f52e9b8fcdec315c412", "tarball": "https://registry.npmjs.org/cacache/-/cacache-17.1.1.tgz", "fileCount": 17, "integrity": "sha512-PfSvLwoYQT04wGpsivFhKyVG1i4mpA0LoxF9WdE7C46E7K170GvvXgVNiTgxmagNcXItPjzGrOBc7CBQ7HgPvg==", "signatures": [{"sig": "MEUCIExu4AEktG9lD9N1Za3tb3/9ScevpaQdFgWWitLmVgr6AiEAwKPfhSs+8G3wXhvVq5EFIFZFYcVIaRZ7lSeI/h2agAk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/cacache@17.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 63490}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "17.1.2": {"name": "cacache", "version": "17.1.2", "dependencies": {"tar": "^6.1.11", "glob": "^10.2.2", "ssri": "^10.0.0", "p-map": "^4.0.0", "minipass": "^5.0.0", "lru-cache": "^7.7.1", "@npmcli/fs": "^3.1.0", "fs-minipass": "^3.0.0", "minipass-flush": "^1.0.5", "unique-filename": "^3.0.0", "minipass-collect": "^1.0.2", "minipass-pipeline": "^1.2.4"}, "devDependencies": {"tap": "^16.0.0", "@npmcli/template-oss": "4.15.1", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "57ce9b79d300373f7266f2f1e4e1718fe09e84b4", "tarball": "https://registry.npmjs.org/cacache/-/cacache-17.1.2.tgz", "fileCount": 17, "integrity": "sha512-VcRDUtZd9r7yfGDpdm3dBDBSQbLd19IqWs9q1tuB9g6kmxYLwIjfLngRKMCfDHxReuf0SBclRuYn66Xds7jzUQ==", "signatures": [{"sig": "MEUCIFnXW5vFseCFkgw0YMI55WUnG2TZ7mKC6J9h0KV0+4CYAiEAq/orMOdG6hG+d6lH6bm9+InVV8NoM1Av8nsvwVREoZ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/cacache@17.1.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 63577}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "17.1.3": {"name": "cacache", "version": "17.1.3", "dependencies": {"tar": "^6.1.11", "glob": "^10.2.2", "ssri": "^10.0.0", "p-map": "^4.0.0", "minipass": "^5.0.0", "lru-cache": "^7.7.1", "@npmcli/fs": "^3.1.0", "fs-minipass": "^3.0.0", "minipass-flush": "^1.0.5", "unique-filename": "^3.0.0", "minipass-collect": "^1.0.2", "minipass-pipeline": "^1.2.4"}, "devDependencies": {"tap": "^16.0.0", "@npmcli/template-oss": "4.15.1", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "c6ac23bec56516a7c0c52020fd48b4909d7c7044", "tarball": "https://registry.npmjs.org/cacache/-/cacache-17.1.3.tgz", "fileCount": 17, "integrity": "sha512-jAdjGxmPxZh0IipMdR7fK/4sDSrHMLUV0+GvVUsjwyGNKHsh79kW/otg+GkbXwl6Uzvy9wsvHOX4nUoWldeZMg==", "signatures": [{"sig": "MEUCICH3oNM9eo4wWXRJ6iARYwrn7rM4kUJWq52kanR3V0K6AiEAuCBVCKpjRr2RO5GBB9X3s5E3BCXqRSg5FkgNXHbBm3E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/cacache@17.1.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 63627}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "17.1.4": {"name": "cacache", "version": "17.1.4", "dependencies": {"tar": "^6.1.11", "glob": "^10.2.2", "ssri": "^10.0.0", "p-map": "^4.0.0", "minipass": "^7.0.3", "lru-cache": "^7.7.1", "@npmcli/fs": "^3.1.0", "fs-minipass": "^3.0.0", "minipass-flush": "^1.0.5", "unique-filename": "^3.0.0", "minipass-collect": "^1.0.2", "minipass-pipeline": "^1.2.4"}, "devDependencies": {"tap": "^16.0.0", "@npmcli/template-oss": "4.18.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "b3ff381580b47e85c6e64f801101508e26604b35", "tarball": "https://registry.npmjs.org/cacache/-/cacache-17.1.4.tgz", "fileCount": 17, "integrity": "sha512-/aJwG2l3ZMJ1xNAnqbMpA40of9dj/pIH3QfiuQSqjfPJF747VR0J/bHn+/KdNnHKc6XQcWt/AfRSBft82W1d2A==", "signatures": [{"sig": "MEYCIQCW48GM5Oz3aJduR+o3Ry/4qcPlrqsNxDhvqGs+9fXlcwIhAOjF34DfTe4/+/d5YPm+ukDMa5HOAVAfTtErpsI3k2lm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/cacache@17.1.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 63627}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "18.0.0": {"name": "cacache", "version": "18.0.0", "dependencies": {"tar": "^6.1.11", "glob": "^10.2.2", "ssri": "^10.0.0", "p-map": "^4.0.0", "minipass": "^7.0.3", "lru-cache": "^10.0.1", "@npmcli/fs": "^3.1.0", "fs-minipass": "^3.0.0", "minipass-flush": "^1.0.5", "unique-filename": "^3.0.0", "minipass-collect": "^1.0.2", "minipass-pipeline": "^1.2.4"}, "devDependencies": {"tap": "^16.0.0", "@npmcli/template-oss": "4.18.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "17a9ecd6e1be2564ebe6cdca5f7cfed2bfeb6ddc", "tarball": "https://registry.npmjs.org/cacache/-/cacache-18.0.0.tgz", "fileCount": 17, "integrity": "sha512-I7mVOPl3PUCeRub1U8YoGz2Lqv9WOBpobZ8RyWFXmReuILz+3OAyTa5oH3QPdtKZD7N0Yk00aLfzn0qvp8dZ1w==", "signatures": [{"sig": "MEYCIQDAOQfYLF22FTpoGZ8BP0O88xStckklHYKXOCBhI9DI4gIhAOVjjv6P7rDSnD/I9ago995N1jWdfsx7KrgWZWz7oZgC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/cacache@18.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 63717}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "18.0.1": {"name": "cacache", "version": "18.0.1", "dependencies": {"tar": "^6.1.11", "glob": "^10.2.2", "ssri": "^10.0.0", "p-map": "^4.0.0", "minipass": "^7.0.3", "lru-cache": "^10.0.1", "@npmcli/fs": "^3.1.0", "fs-minipass": "^3.0.0", "minipass-flush": "^1.0.5", "unique-filename": "^3.0.0", "minipass-collect": "^2.0.1", "minipass-pipeline": "^1.2.4"}, "devDependencies": {"tap": "^16.0.0", "@npmcli/template-oss": "4.19.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "b026d56ad569e4f73cc07c813b3c66707d0fb142", "tarball": "https://registry.npmjs.org/cacache/-/cacache-18.0.1.tgz", "fileCount": 17, "integrity": "sha512-g4Uf2CFZPaxtJKre6qr4zqLDOOPU7bNVhWjlNhvzc51xaTOx2noMOLhfFkTAqwtrAZAKQUuDfyjitzilpA8WsQ==", "signatures": [{"sig": "MEUCIQCNzwSrBbPhHYzX0wL+JRPT6OrHgdXW5aD6ds7UrxciGQIgPHJIBPTu37fm6GptUjcXcYuBkrBog3BuhVmREZH5+WQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/cacache@18.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 63661}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "18.0.2": {"name": "cacache", "version": "18.0.2", "dependencies": {"tar": "^6.1.11", "glob": "^10.2.2", "ssri": "^10.0.0", "p-map": "^4.0.0", "minipass": "^7.0.3", "lru-cache": "^10.0.1", "@npmcli/fs": "^3.1.0", "fs-minipass": "^3.0.0", "minipass-flush": "^1.0.5", "unique-filename": "^3.0.0", "minipass-collect": "^2.0.1", "minipass-pipeline": "^1.2.4"}, "devDependencies": {"tap": "^16.0.0", "@npmcli/template-oss": "4.21.3", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "fd527ea0f03a603be5c0da5805635f8eef00c60c", "tarball": "https://registry.npmjs.org/cacache/-/cacache-18.0.2.tgz", "fileCount": 17, "integrity": "sha512-r3NU8h/P+4lVUHfeRw1dtgQYar3DZMm4/cm2bZgOvrFC/su7budSOeqh52VJIC4U4iG1WWwV6vRW0znqBvxNuw==", "signatures": [{"sig": "MEUCIB2qi4F+GiDU36NGTVPL6BfSow+8XG3/lE/ToZk0QZd/AiEAopfjPM6DUkXkywikDi7C1Puonzb0xu3UjsId8nvqryY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/cacache@18.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 63559}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "18.0.3": {"name": "cacache", "version": "18.0.3", "dependencies": {"tar": "^6.1.11", "glob": "^10.2.2", "ssri": "^10.0.0", "p-map": "^4.0.0", "minipass": "^7.0.3", "lru-cache": "^10.0.1", "@npmcli/fs": "^3.1.0", "fs-minipass": "^3.0.0", "minipass-flush": "^1.0.5", "unique-filename": "^3.0.0", "minipass-collect": "^2.0.1", "minipass-pipeline": "^1.2.4"}, "devDependencies": {"tap": "^16.0.0", "@npmcli/template-oss": "4.22.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "864e2c18414e1e141ae8763f31e46c2cb96d1b21", "tarball": "https://registry.npmjs.org/cacache/-/cacache-18.0.3.tgz", "fileCount": 17, "integrity": "sha512-qXCd4rh6I07cnDqh8V48/94Tc/WSfj+o3Gn6NZ0aZovS255bUx8O13uKxRFd2eWG0xgsco7+YItQNPaa5E85hg==", "signatures": [{"sig": "MEUCIQCC/lkDf8YH7ovTQqLbvK15fnzGfIJD1adj7kQqKK+peAIgWnG8F6vXojVvrsufossNxTJRwkt3kB21imzmvzAlRxI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/cacache@18.0.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 63516}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "18.0.4": {"name": "cacache", "version": "18.0.4", "dependencies": {"tar": "^6.1.11", "glob": "^10.2.2", "ssri": "^10.0.0", "p-map": "^4.0.0", "minipass": "^7.0.3", "lru-cache": "^10.0.1", "@npmcli/fs": "^3.1.0", "fs-minipass": "^3.0.0", "minipass-flush": "^1.0.5", "unique-filename": "^3.0.0", "minipass-collect": "^2.0.1", "minipass-pipeline": "^1.2.4"}, "devDependencies": {"tap": "^16.0.0", "@npmcli/template-oss": "4.22.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "4601d7578dadb59c66044e157d02a3314682d6a5", "tarball": "https://registry.npmjs.org/cacache/-/cacache-18.0.4.tgz", "fileCount": 17, "integrity": "sha512-B+L5iIa9mgcjLbliir2th36yEwPftrzteHYujzsx3dFP/31GCHcIeS8f5MGd80odLOjaOvSpU3EEAmRQptkxLQ==", "signatures": [{"sig": "MEUCIQDWJ6RCqGTRpi99qlJAMPAmvsazMNqnTLWlcSCUhC5pJAIgdi5+OdHdojohsNZLbQj1wy22g2D2gNSYjz3ioFSDesQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/cacache@18.0.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 63676}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "19.0.0": {"name": "cacache", "version": "19.0.0", "dependencies": {"tar": "^7.4.3", "glob": "^10.2.2", "ssri": "^12.0.0", "p-map": "^4.0.0", "minipass": "^7.0.3", "lru-cache": "^10.0.1", "@npmcli/fs": "^4.0.0", "fs-minipass": "^3.0.0", "minipass-flush": "^1.0.5", "unique-filename": "^4.0.0", "minipass-collect": "^2.0.1", "minipass-pipeline": "^1.2.4"}, "devDependencies": {"tap": "^16.0.0", "@npmcli/template-oss": "4.23.3", "@npmcli/eslint-config": "^5.0.0"}, "dist": {"shasum": "4dba1e80dfd5f2e596ba353158b333eba95dcc88", "tarball": "https://registry.npmjs.org/cacache/-/cacache-19.0.0.tgz", "fileCount": 17, "integrity": "sha512-6G/v4MKjOejWTistsnIxkqnGQ8Hm8VHwJg3fg4TvD/TpsV1q97BV7AgriqIYetn2uVe2ZXLFKDJFbwvH7RMUOg==", "signatures": [{"sig": "MEQCICvTJbbVbM/EBy6lAvguSrYwKWfB/NcByL7Ehg4AxMv9AiBhSPB+CY7ynmOTZy4lfjEXIGDwEV1d9ufO/tt/sRHoGg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/cacache@19.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 63709}, "engines": {"node": "^18.17.0 || >=20.5.0"}}, "19.0.1": {"name": "cacache", "version": "19.0.1", "dependencies": {"tar": "^7.4.3", "glob": "^10.2.2", "ssri": "^12.0.0", "p-map": "^7.0.2", "minipass": "^7.0.3", "lru-cache": "^10.0.1", "@npmcli/fs": "^4.0.0", "fs-minipass": "^3.0.0", "minipass-flush": "^1.0.5", "unique-filename": "^4.0.0", "minipass-collect": "^2.0.1", "minipass-pipeline": "^1.2.4"}, "devDependencies": {"tap": "^16.0.0", "@npmcli/template-oss": "4.23.3", "@npmcli/eslint-config": "^5.0.0"}, "dist": {"shasum": "3370cc28a758434c85c2585008bd5bdcff17d6cd", "tarball": "https://registry.npmjs.org/cacache/-/cacache-19.0.1.tgz", "fileCount": 17, "integrity": "sha512-hdsUxulXCi5STId78vRVYEtDAjq99ICAUktLTeTYsLoTE6Z8dS0c8pWNCxwdrk9YfJeobDZc2Y186hD/5ZQgFQ==", "signatures": [{"sig": "MEUCIQDxQBJgqHGgaPCxOIHeunUVtLWrsfsPWRyVj7FzQZxg6QIgWu3Mt+dJEJE/+EWSPUZtrdpJ0Mpfq669voSdHeJ6YfM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/cacache@19.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 63801}, "engines": {"node": "^18.17.0 || >=20.5.0"}}}, "modified": "2025-05-14T20:03:01.565Z"}