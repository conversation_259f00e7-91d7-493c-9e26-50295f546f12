{"_id": "are-we-there-yet", "_rev": "69-34ffbfd781616c48dc2eaaccc202dab5", "name": "are-we-there-yet", "dist-tags": {"latest": "4.0.2"}, "versions": {"1.0.0": {"name": "are-we-there-yet", "version": "1.0.0", "author": {"url": "http://re-becca.org", "name": "<PERSON>"}, "license": "ISC", "_id": "are-we-there-yet@1.0.0", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/are-we-there-yet", "bugs": {"url": "https://github.com/iarna/are-we-there-yet/issues"}, "dist": {"shasum": "30f1c33af15b2c820964b6debd230e625815120b", "tarball": "https://registry.npmjs.org/are-we-there-yet/-/are-we-there-yet-1.0.0.tgz", "integrity": "sha512-3w8QRy/gPwuF+Gd4mHSRAa1tGavIHQ4INOVX9L74NnEERhtroZgdBTrJgMprqAbNoNMIUJZzRDwUWgxtkMmr9Q==", "signatures": [{"sig": "MEUCIQCRWhmqWJIQmd+oKd4k5ihzR7aDmd7jyyFd8EzcwPFangIgJsIzuUQJAfGo1fvZ7OX/0r5Bhg1GUC39pKbu3xBF5PA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "30f1c33af15b2c820964b6debd230e625815120b", "gitHead": "8e93269cef069811c80400b91859de21f7092b2b", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "https://github.com/iarna/are-we-there-yet.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "Keep track of the overall completion of many dispirate processes", "directories": {}, "dependencies": {"delegates": "^0.1.0"}, "devDependencies": {"tap": "^0.4.13"}}, "1.0.1": {"name": "are-we-there-yet", "version": "1.0.1", "author": {"url": "http://re-becca.org", "name": "<PERSON>"}, "license": "ISC", "_id": "are-we-there-yet@1.0.1", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/are-we-there-yet", "bugs": {"url": "https://github.com/iarna/are-we-there-yet/issues"}, "dist": {"shasum": "5398006d115400b00ef4223106eae400f1d3fcdf", "tarball": "https://registry.npmjs.org/are-we-there-yet/-/are-we-there-yet-1.0.1.tgz", "integrity": "sha512-GtNc1SK1u9ZiGbQFtlBeavC1TPI0Jd3praoDDqWHTDjK0ADRDpET4YTWdIEYrbVvE80jNxoZd1HiypVIaAka6w==", "signatures": [{"sig": "MEUCIA+wB75Vj/yyQ6/J4PMQ7aAhYYH8qkvfUWLuLjLYGPrdAiEAkpBCfxJuA6QRCJve2DGi/zLMl+XMMjHlGtVqN+BX+qA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "5398006d115400b00ef4223106eae400f1d3fcdf", "gitHead": "22ba2b763c0b303aa8e215eacf00129d53717fc7", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "https://github.com/iarna/are-we-there-yet.git", "type": "git"}, "_npmVersion": "2.1.11", "description": "Keep track of the overall completion of many dispirate processes", "directories": {}, "_nodeVersion": "0.10.33", "dependencies": {"delegates": "^0.1.0"}, "devDependencies": {"tap": "^0.4.13"}}, "1.0.2": {"name": "are-we-there-yet", "version": "1.0.2", "author": {"url": "http://re-becca.org", "name": "<PERSON>"}, "license": "ISC", "_id": "are-we-there-yet@1.0.2", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/are-we-there-yet", "bugs": {"url": "https://github.com/iarna/are-we-there-yet/issues"}, "dist": {"shasum": "b518f4a6ec85862b57ce82df495bbabc76cb5246", "tarball": "https://registry.npmjs.org/are-we-there-yet/-/are-we-there-yet-1.0.2.tgz", "integrity": "sha512-CknI8GsLAoB5nzQog3B4+K2fvbC3p762rSLHaNxAgnMY9JmGxP9N6aPjSDbBdRj3U43ftM6ofYGxUNIPV9T1eA==", "signatures": [{"sig": "MEQCIBwhnFO/2bZRdU0HCVDgfa1afNVya5SdzJkKS5zeR+qgAiAKc7U7WlKwCAwZD6ERj8ZJM9bNqSj/yttHhF20jjHBJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "b518f4a6ec85862b57ce82df495bbabc76cb5246", "gitHead": "5b19772c3de79d66eb5db2900b5f8e6d73078167", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "https://github.com/iarna/are-we-there-yet.git", "type": "git"}, "_npmVersion": "2.1.11", "description": "Keep track of the overall completion of many dispirate processes", "directories": {}, "_nodeVersion": "0.10.33", "dependencies": {"delegates": "^0.1.0"}, "devDependencies": {"tap": "^0.4.13"}}, "1.0.3": {"name": "are-we-there-yet", "version": "1.0.3", "author": {"url": "http://re-becca.org", "name": "<PERSON>"}, "license": "ISC", "_id": "are-we-there-yet@1.0.3", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/are-we-there-yet", "bugs": {"url": "https://github.com/iarna/are-we-there-yet/issues"}, "dist": {"shasum": "699fa10313a9e9d1a5d68b3883c605994161fddd", "tarball": "https://registry.npmjs.org/are-we-there-yet/-/are-we-there-yet-1.0.3.tgz", "integrity": "sha512-kpnyWpDqc5pkz/QMI7MTnjF3An8r1K5QpeoeIpxJe4K+P29znwc3vn49hgXMSqatH6NEAwLfNCdK5NjY3gnkzQ==", "signatures": [{"sig": "MEQCIGer1XXq2G7U8d0R5IUf7UgzYIW27GhQ0YY7eSTeWaa7AiAyxAoxOOe+NX6ooc3VRRu78pblKeCLTj5nA/vqAApKmA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "699fa10313a9e9d1a5d68b3883c605994161fddd", "gitHead": "a038607bf1617149cd3a0cf335b3ed08341f0565", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "https://github.com/iarna/are-we-there-yet.git", "type": "git"}, "_npmVersion": "2.6.1", "description": "Keep track of the overall completion of many dispirate processes", "directories": {}, "_nodeVersion": "0.10.36", "dependencies": {"delegates": "^0.1.0"}, "devDependencies": {"tap": "^0.4.13"}}, "1.0.4": {"name": "are-we-there-yet", "version": "1.0.4", "author": {"url": "http://re-becca.org", "name": "<PERSON>"}, "license": "ISC", "_id": "are-we-there-yet@1.0.4", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/are-we-there-yet", "bugs": {"url": "https://github.com/iarna/are-we-there-yet/issues"}, "dist": {"shasum": "527fe389f7bcba90806106b99244eaa07e886f85", "tarball": "https://registry.npmjs.org/are-we-there-yet/-/are-we-there-yet-1.0.4.tgz", "integrity": "sha512-dMbzNTqRTx0dCj+ufaP8+TNH3BfP8x4WwzlNYe4T8f+Wh6SS6ZStB8PCleUSSRHumcRhag3JTxGATiuUo5C6ww==", "signatures": [{"sig": "MEYCIQC4/o/Gj/QC0fGQlxJzSO4DZJ5ih+7ye0yteoY46ESQxgIhAJURSQxqnCRQ5nFTY9OWbasEFHs2B6esCyBRIWLpVVnr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "527fe389f7bcba90806106b99244eaa07e886f85", "gitHead": "7ce414849b81ab83935a935275def01914821bde", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "https://github.com/iarna/are-we-there-yet.git", "type": "git"}, "_npmVersion": "2.0.0", "description": "Keep track of the overall completion of many dispirate processes", "directories": {}, "dependencies": {"delegates": "^0.1.0", "readable-stream": "^1.1.13"}, "devDependencies": {"tap": "^0.4.13"}}, "1.0.5": {"name": "are-we-there-yet", "version": "1.0.5", "author": {"url": "http://re-becca.org", "name": "<PERSON>"}, "license": "ISC", "_id": "are-we-there-yet@1.0.5", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/are-we-there-yet", "bugs": {"url": "https://github.com/iarna/are-we-there-yet/issues"}, "dist": {"shasum": "239f26706da902a2bffb72c33de66fdfd3798ac5", "tarball": "https://registry.npmjs.org/are-we-there-yet/-/are-we-there-yet-1.0.5.tgz", "integrity": "sha512-b84ZTSIqAebS74hzzem15clsoDPrk/5kmIUuGU613M91F/UhUDeuIMGMk7X3xIqM4b0mbeUeu2CrXwQE9IPtJQ==", "signatures": [{"sig": "MEQCIBHjn1MbhNoPxwuAI4pNjoE4rY9HHSz6EA5MeY/o8NyIAiA/+teNZpbAYOXJ8bDxYqZcnAHSsSkEAdDozqin1yNWWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "239f26706da902a2bffb72c33de66fdfd3798ac5", "gitHead": "abaff79ae17e9397eae19d29d2d75778d18aab3a", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/iarna/are-we-there-yet.git", "type": "git"}, "_npmVersion": "3.5.2", "description": "Keep track of the overall completion of many dispirate processes", "directories": {}, "_nodeVersion": "4.2.2", "dependencies": {"delegates": "^0.1.0", "readable-stream": "^2.0.0 || ^1.1.13"}, "devDependencies": {"tap": "^0.4.13"}}, "1.0.6": {"name": "are-we-there-yet", "version": "1.0.6", "author": {"url": "http://re-becca.org", "name": "<PERSON>"}, "license": "ISC", "_id": "are-we-there-yet@1.0.6", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/are-we-there-yet", "bugs": {"url": "https://github.com/iarna/are-we-there-yet/issues"}, "dist": {"shasum": "a2d28c93102aa6cc96245a26cb954de06ec53f0c", "tarball": "https://registry.npmjs.org/are-we-there-yet/-/are-we-there-yet-1.0.6.tgz", "integrity": "sha512-Zfw6bteqM9gQXZ1BIWOgM8xEwMrUGoyL8nW13+O+OOgNX3YhuDN1GDgg1NzdTlmm3j+9sHy7uBZ12r+z9lXnZQ==", "signatures": [{"sig": "MEQCIGDywI8tdrjCJRYM0skY5YEJvg2OM5UaYbddELDBGDvZAiAQ5bgsiJc0Y/DGhIQ5UQ/FPl70Xdg01HPwv+wCRa5Qkg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "a2d28c93102aa6cc96245a26cb954de06ec53f0c", "gitHead": "5f19c8b6f9c5afb8b0b17308cb9d66f7027ae526", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/iarna/are-we-there-yet.git", "type": "git"}, "_npmVersion": "3.6.0", "description": "Keep track of the overall completion of many dispirate processes", "directories": {}, "_nodeVersion": "5.4.0", "dependencies": {"delegates": "^1.0.0", "readable-stream": "^2.0.0 || ^1.1.13"}, "devDependencies": {"tap": "^5.2.0"}}, "1.1.0": {"name": "are-we-there-yet", "version": "1.1.0", "author": {"url": "http://re-becca.org", "name": "<PERSON>"}, "license": "ISC", "_id": "are-we-there-yet@1.1.0", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/are-we-there-yet", "bugs": {"url": "https://github.com/iarna/are-we-there-yet/issues"}, "dist": {"shasum": "44d019a519e7d61edfe655bd94b663560b7d04e0", "tarball": "https://registry.npmjs.org/are-we-there-yet/-/are-we-there-yet-1.1.0.tgz", "integrity": "sha512-nucNhcXfwccVJqI3U4FZykMhMea6U4KWVgPs61x28h6TjM85PSXpPSfHuEnkKnPVdyMRHtdnwd5dAJ2l7QHVMw==", "signatures": [{"sig": "MEYCIQCcHZ0oL67WJVqbpU6z4zdQFbQj+Y11SOwE22lokzzkvQIhAJTgWbvfCKIkcDnw8HRZ3g2W0H0/+YOj2hgzmxrGlVLx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "44d019a519e7d61edfe655bd94b663560b7d04e0", "gitHead": "f209ace0686b5ff06a173190c0be0caa2935fc9d", "scripts": {"test": "standard && tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/iarna/are-we-there-yet.git", "type": "git"}, "_npmVersion": "3.7.0", "description": "Keep track of the overall completion of many dispirate processes", "directories": {}, "_nodeVersion": "5.4.0", "dependencies": {"delegates": "^1.0.0", "readable-stream": "^2.0.0 || ^1.1.13"}, "devDependencies": {"tap": "^5.2.0", "standard": "^5.4.1"}}, "1.1.1": {"name": "are-we-there-yet", "version": "1.1.1", "author": {"url": "http://re-becca.org", "name": "<PERSON>"}, "license": "ISC", "_id": "are-we-there-yet@1.1.1", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/are-we-there-yet", "bugs": {"url": "https://github.com/iarna/are-we-there-yet/issues"}, "dist": {"shasum": "a84aeb12edab0a579ce2acdf183daa202533d3cd", "tarball": "https://registry.npmjs.org/are-we-there-yet/-/are-we-there-yet-1.1.1.tgz", "integrity": "sha512-mSzhaFBu1hoRPGP/RSQxlMcATakXOC/4UtAiWnn8vh2Js8AI5jyGdJHi3quSZivV+uflx1gvw6vGdVyzyUAFRw==", "signatures": [{"sig": "MEUCIHVy633YMwuqCIP/k3ytTl0GCIdKYpezfdkiqlamMChcAiEAn9HgsoUN/iw+y/qVTD02zD3R34dPoMA7/ahgAeziOOg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "a84aeb12edab0a579ce2acdf183daa202533d3cd", "gitHead": "49abcf1ac7882313a77d3304fcaba2b99125baa5", "scripts": {"test": "standard && tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/iarna/are-we-there-yet.git", "type": "git"}, "_npmVersion": "3.7.0", "description": "Keep track of the overall completion of many dispirate processes", "directories": {}, "_nodeVersion": "5.4.0", "dependencies": {"delegates": "^1.0.0", "readable-stream": "^2.0.0 || ^1.1.13"}, "devDependencies": {"tap": "^5.2.0", "standard": "^5.4.1"}}, "1.1.2": {"name": "are-we-there-yet", "version": "1.1.2", "author": {"url": "http://re-becca.org", "name": "<PERSON>"}, "license": "ISC", "_id": "are-we-there-yet@1.1.2", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/are-we-there-yet", "bugs": {"url": "https://github.com/iarna/are-we-there-yet/issues"}, "dist": {"shasum": "80e470e95a084794fe1899262c5667c6e88de1b3", "tarball": "https://registry.npmjs.org/are-we-there-yet/-/are-we-there-yet-1.1.2.tgz", "integrity": "sha512-Ph/5XGGWoEy6z2VsZmELQW3qZtrITchV1zMNt1OonqoSoYo9acY23weZ67fLKTaRdMEGBYDx/jDJBGS8YlwPvg==", "signatures": [{"sig": "MEUCIA2As2lR5/v2Jw3msL8oNzZWuA/FGrbwYQp2b4ZMPbF8AiEA7wIFIQbTlTrlQmLZMSBpudDASUnWCeJ8XxzbZRnt32w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "80e470e95a084794fe1899262c5667c6e88de1b3", "gitHead": "dd5706e2204cb681e78031d0ffe156ed4cc75823", "scripts": {"test": "standard && tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/iarna/are-we-there-yet.git", "type": "git"}, "_npmVersion": "3.8.1", "description": "Keep track of the overall completion of many dispirate processes", "directories": {}, "_nodeVersion": "4.2.2", "dependencies": {"delegates": "^1.0.0", "readable-stream": "^2.0.0 || ^1.1.13"}, "devDependencies": {"tap": "^5.7.0", "standard": "^6.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/are-we-there-yet-1.1.2.tgz_1458084397358_0.15847722673788667", "host": "packages-13-west.internal.npmjs.com"}}, "1.1.3": {"name": "are-we-there-yet", "version": "1.1.3", "author": {"url": "http://re-becca.org", "name": "<PERSON>"}, "license": "ISC", "_id": "are-we-there-yet@1.1.3", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/are-we-there-yet", "bugs": {"url": "https://github.com/iarna/are-we-there-yet/issues"}, "dist": {"shasum": "ee86447bf1a06feb4dd3aac46e023b87d7f8e2ef", "tarball": "https://registry.npmjs.org/are-we-there-yet/-/are-we-there-yet-1.1.3.tgz", "integrity": "sha512-ePoCA07iEmK7T4hYnxYP6xiTitH3MP+LYn+G4kgeYPa7FeqW/xYeSYCi3myYYQXJZh/oR2qMG8Gg3dVftqB9JA==", "signatures": [{"sig": "MEYCIQDH0mzGWRSJA0AsymoXN7jyr9g7cUBuI70lMKclXETWkQIhAIqLQCvcFfOj//AtJHsJpV7md18JXQG0w5wQbYCfd37d", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["index.js", "tracker-base.js", "tracker-group.js", "tracker-stream.js", "tracker.js", "CHANGES.md"], "gitHead": "b9037a1a39fa64ad9c1c8f9a4c9390557d8128e9", "scripts": {"test": "standard && tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/iarna/are-we-there-yet.git", "type": "git"}, "_npmVersion": "4.5.0", "description": "Keep track of the overall completion of many dispirate processes", "directories": {}, "_nodeVersion": "7.7.4", "dependencies": {"delegates": "^1.0.0", "readable-stream": "^2.0.6"}, "devDependencies": {"tap": "^5.7.0", "standard": "^6.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/are-we-there-yet-1.1.3.tgz_1492760702530_0.7655932088382542", "host": "packages-18-east.internal.npmjs.com"}}, "1.1.4": {"name": "are-we-there-yet", "version": "1.1.4", "author": {"url": "http://re-becca.org", "name": "<PERSON>"}, "license": "ISC", "_id": "are-we-there-yet@1.1.4", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/are-we-there-yet", "bugs": {"url": "https://github.com/iarna/are-we-there-yet/issues"}, "dist": {"shasum": "bb5dca382bb94f05e15194373d16fd3ba1ca110d", "tarball": "https://registry.npmjs.org/are-we-there-yet/-/are-we-there-yet-1.1.4.tgz", "integrity": "sha512-QbMPI8teYlZBIBqDgmIWfDKO149dGtQV2ium8WniCaARFFRd1e+IES1LA4sSGcVTFdVL628+163WUbxev7R/aQ==", "signatures": [{"sig": "MEUCIH3r+N9ETlfLmVu8SbRNk0+yIyOO/JX1zGsBaVc+8mczAiEAxsQTbn5MSm0cQs74C8K7y95xnDsho7V3LNaUINg59y8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["index.js", "tracker-base.js", "tracker-group.js", "tracker-stream.js", "tracker.js", "CHANGES.md"], "gitHead": "aea89b9c277c0674a2485a3eb94a7269bb2346be", "scripts": {"test": "standard && tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/iarna/are-we-there-yet.git", "type": "git"}, "_npmVersion": "4.5.0", "description": "Keep track of the overall completion of many disparate processes", "directories": {}, "_nodeVersion": "7.7.4", "dependencies": {"delegates": "^1.0.0", "readable-stream": "^2.0.6"}, "devDependencies": {"tap": "^5.7.0", "standard": "^6.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/are-we-there-yet-1.1.4.tgz_1492760790532_0.2543606413528323", "host": "packages-18-east.internal.npmjs.com"}}, "1.1.5": {"name": "are-we-there-yet", "version": "1.1.5", "author": {"url": "http://re-becca.org", "name": "<PERSON>"}, "license": "ISC", "_id": "are-we-there-yet@1.1.5", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/are-we-there-yet", "bugs": {"url": "https://github.com/iarna/are-we-there-yet/issues"}, "dist": {"shasum": "4b35c2944f062a8bfcda66410760350fe9ddfc21", "tarball": "https://registry.npmjs.org/are-we-there-yet/-/are-we-there-yet-1.1.5.tgz", "fileCount": 9, "integrity": "sha512-5hYdAkZlcG8tOLujVDTgCT+uPX0VnpAH28gWsLfzpXYm7wP6mp5Q/gYyR7YQ0cKVJcXJnl3j2kpBan13PtQf6w==", "signatures": [{"sig": "MEUCIQDG3jpxFzyeLui600cEH6i5B7HaK79UrxNBgNa+FIEVtAIgQ9TKNKVZLx9aXgZJyZsmKtuBQAE7H0uaEMtXCiypY94=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14650, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBzfBCRA9TVsSAnZWagAAtR4QAJuFZqCyX+bkCwSGz/DJ\nAXAnaUYip2Hd8sb2u28miiJwM6aE/yuu126iXLIKj4ov+LEVyiFSXNtkRc8H\nSgDvyh5WZZ8m06o78GA/0FGOxDuJHbBtTPvIFkVM9Wud3NuJ8M08mlRnZGMo\nyLzjUEOejLt5o/JjQfiftVsN9cUXt0UkfP9ZkDuqg8HrVC8k1GS0qte15sKm\nISytjUl4atLw9d6oBsWCwc7xyUSv6wSER+Fqf5vo/4+IIeOdcWFmTGrtvWdo\n8KtHUik4bmo5EE3Z3XlFfEadOd/HSkUfizFOxRaauFWpJMF24+zt1wkrZ26R\nZWc6hyIg/1xrYKgJ0uOuBhPMxcOaUZNO5h3BeQ5J/Rdzg4rAZXSA26GVe+zL\n7sZbWASLSyjquh+jiZ94ztYdS8FhfuCa12TfoHBLOu9yp3xkFHrD89FHg915\nltEo3hERMxHyQdL7Fx/V7ewNH0Y7jL0PSQyEg5Fq1F7YS2AYskAI86FCCm41\nZXP1MveV1c39oqJxpwXhr22z5ga5GDxwVr4cNT0q6hOzMJ0Yigrht+YQZLFc\n0mG6h177j8U+6Q68wK69NcGEiosZCnmMRihGvJxZ6Keq9YvGEZNcnUGdfSPJ\nxu7tGhwr78HDOPTvLzCE9H4nBsBNTMMqJkMAcqjN3u90KGPIMyl/K0S3rfnv\nIiix\r\n=Mjkz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "files": ["index.js", "tracker-base.js", "tracker-group.js", "tracker-stream.js", "tracker.js", "CHANGES.md"], "gitHead": "b4d023b8b754b9d2d540c9db21cc7edf2962ea63", "scripts": {"test": "standard && tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/iarna/are-we-there-yet.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "Keep track of the overall completion of many disparate processes", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"delegates": "^1.0.0", "readable-stream": "^2.0.6"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.0.1", "standard": "^11.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/are-we-there-yet_1.1.5_1527199680706_0.2972836172536619", "host": "s3://npm-registry-packages"}}, "1.1.6": {"name": "are-we-there-yet", "version": "1.1.6", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "are-we-there-yet@1.1.6", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/are-we-there-yet", "bugs": {"url": "https://github.com/npm/are-we-there-yet/issues"}, "tap": {"lines": 92, "branches": 68, "functions": 86, "statements": 92}, "dist": {"shasum": "bc9101d19670c7bdb1546ed036568a6c9879ee79", "tarball": "https://registry.npmjs.org/are-we-there-yet/-/are-we-there-yet-1.1.6.tgz", "fileCount": 8, "integrity": "sha512-+1byPnimWdGcKFRS48zG73nxM08kamPFReUYvEmRXI3E8E4YhF4voMRDaGlfGD1UeRHEgs4NhQCE28KI8JVj1A==", "signatures": [{"sig": "MEYCIQCKGK8FQmjbXHfCf+xAjHHIeBx90618e4vG952zodDBSQIhAKG+jBq9rGOYwPuIdOsneVQY+ygxnlXrzf4pZNEwqR2q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14102, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhL7qFCRA9TVsSAnZWagAA4M8P/ibVhI2zpv0aofx/ynom\nzXiczlV15USnLd8Ga3oHUHUPOoq51DsROVIdcclK/WoxCBxzyTjdytFqNprn\n5ems6wPm39fju1SKjUGSySTnBOvp409W/UzzBqMXJthhG1IRKxQH6FmgaoR5\nig7i4koQt/cs3zSj3gFnzkLqbPDhDNh1DgzDkaSTcOh2g8ODUEWpQn99pUwp\nENPrliKw9jKISPA7172ciZow7qHnoWIYWU1WAtGwwQXyeKdR9zQmvLwDRaBq\nmyGExjrD/MVFTpE61oW2/d1+URgaUWUf68Q+qFp/4fqJhUJMMhz31clzcTFm\n+JoBHWFq8oyCH+HNJ2gK4OLUB0O9MWe0Vt3rRrY0gyMuul0NpLBZu7y2cSoc\n2CgKeLyvypK2vq2V6VXnEZXqerwTg/kCdV67DM+Jk2rOA/WM/N7dwt8VySkb\nMfCGg/3YnaIOm43KXZ4c8Kjzx83wk/Np4mA6qZyiwrN8oq+H7B3BMHox16iu\nET1lVvN+p0mltRZVy7EkLldGmB48KhHIfr2iptDR/MlOa6WByLH6Z6Jxmsea\n2eVdUC9giA8MooA4PNpUDPY/T7Z/lUUtEK3ztq5z3SjS4jxMIIoC5UgeeHHH\nIV/ACXegjNUPmW9eYxbSLURWHPkBeD1JDgpemmCuHefOaMuoyuSZ031KJi7N\nphq7\r\n=jLYl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=10"}, "gitHead": "e3265d679d77ef6c9cef6025e49ba4728863581a", "scripts": {"lint": "eslint '**/*.js'", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postsnap": "npm run lintfix --", "posttest": "npm run lint", "npmclilint": "npmcli-lint", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/npm/are-we-there-yet.git", "type": "git"}, "_npmVersion": "7.21.1", "description": "Keep track of the overall completion of many disparate processes", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"delegates": "^1.0.0", "readable-stream": "^3.6.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.0.9", "eslint": "^7.32.0", "eslint-plugin-node": "^11.1.0", "@npmcli/template-oss": "^1.0.2", "@npmcli/eslint-config": "^1.0.0"}, "templateVersion": "1.0.2", "_npmOperationalInternal": {"tmp": "tmp/are-we-there-yet_1.1.6_1630517892864_0.8715523825223108", "host": "s3://npm-registry-packages"}}, "1.1.7": {"name": "are-we-there-yet", "version": "1.1.7", "author": {"url": "http://re-becca.org", "name": "<PERSON>"}, "license": "ISC", "_id": "are-we-there-yet@1.1.7", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/are-we-there-yet", "bugs": {"url": "https://github.com/iarna/are-we-there-yet/issues"}, "dist": {"shasum": "b15474a932adab4ff8a50d9adfa7e4e926f21146", "tarball": "https://registry.npmjs.org/are-we-there-yet/-/are-we-there-yet-1.1.7.tgz", "fileCount": 9, "integrity": "sha512-nxwy40TuMiUGqMyRHgCSWZ9FM4VAoRP4xUYSTv5ImRog+h9yISPbVH7H8fASCIzYn9wlEv4zvFL7uKDMCFQm3g==", "signatures": [{"sig": "MEUCIQC0XBovkU8LWRDUQRy+SJViubPFQZ8+Bh2kzD0dvbILFQIgbPsvVqi4ZIwKPFW85NSdXGSswtLP0zTNs9/QVi861R8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14650, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhMN71CRA9TVsSAnZWagAAll8P+gObYFf8WlJu4PAv2A39\n4CV7tgKibyTKWKZD2gWS/zs1g5u5Sa17XXVuYCE47scQNGe+j9aYo9tWmvru\nd0rGnLliEhcFtqtPDfZ2Egg14mwqXBY217X8k/AESW4pka8j6gdjYKuFkLtQ\nGZ+fjEL3AspHrtshNUv5LjB7XKPGDX4vDD14kIQXTG7I3bpeiKdE0s0whFnq\nO+b6mPDMyigS2FRLRIkyffZVrswdSg0o+bI3OHJEW4dNpR16tOFDFymf+pTz\nJAxcBEJyH4Vq5J7u2idZe67TBXV66XUetpWEVehD8cM00n5P3B8eU3Wh8VM+\nfCGUWzJr2CoAH1bRKIqP6UFc1MbQ7Pt1GiqGGK2ZBVr1Tw+eY3Cq/gfH/IFk\n8Zh7Z6XDyNRVWwT6ROSq59xmy1eaN+p5ld6zVvUVFlJAjCJcZn4UDsXtcIjA\nwfMDThy/unpGKNj4THXPVwW5LoPoU20QhOytK63fx9gmQ5lSy37AZ5NaRMbx\nc9OVVzK9ZSIVTUweiwrR3kuylY6LL20Is0RfFGxgzynB0wvBXfiLX3b7B8rc\nH4zuYOmswcUl8WxYWsyIN6nOO+pSdLagADtTjuk9mk48vS0KdkWylfuKdPto\nTPh6NWnFMG7o/6zqo4e7QGGD9/kJdN8eHg9b/cNMAMSeh8GYmktrYIBSAGM2\n30an\r\n=KF3C\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "ff269ace9fb7dad79bbec11abc102d5e9ee25625", "scripts": {"test": "standard && tap test/*.js"}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/iarna/are-we-there-yet.git", "type": "git"}, "_npmVersion": "7.21.1", "description": "Keep track of the overall completion of many disparate processes", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"delegates": "^1.0.0", "readable-stream": "^2.0.6"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.0.1", "standard": "^11.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/are-we-there-yet_1.1.7_1630592756982_0.9446616056787884", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "are-we-there-yet", "version": "2.0.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "are-we-there-yet@2.0.0", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/are-we-there-yet", "bugs": {"url": "https://github.com/npm/are-we-there-yet/issues"}, "tap": {"lines": 92, "branches": 68, "functions": 86, "statements": 92}, "dist": {"shasum": "372e0e7bd279d8e94c653aaa1f67200884bf3e1c", "tarball": "https://registry.npmjs.org/are-we-there-yet/-/are-we-there-yet-2.0.0.tgz", "fileCount": 8, "integrity": "sha512-Ci/qENmwHnsYo9xKIcUJN5LeDKdJ6R1Z1j9V/J5wyq8nh/mYPEpIKJbBZXtZjG04HiK7zV/p6Vs9952MrMeUIw==", "signatures": [{"sig": "MEYCIQCncFZRuGigxnf8gMhE8fjIxzhi/JEn5wjgh1gXRZ4O/wIhAJLR8SvdL0WMeFUMiUnIImnxW3L22v1vML7uOunVYd4T", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14102, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhMN89CRA9TVsSAnZWagAAkD0P/Rclchdcfk0nwl7mqSLO\nHS2c2P8GqQpWTdJ6XLaSNbbupqQXsidLHmPag0ohkEPZ0CxRExFrNVl7VSmk\nK7t+h0s0k3hzo2PVo2PIKzM0U+5d7JQ85MUYGQ2cJIgB88z6d2R9LZQQoZrz\nf8Xn6KFk22sBkP+RVvalF5QjlpsUxfHz96DssmVy8hfDE0NACnTcpguwK0tW\nrhNS6rbPcSfung7+FLw4BTm8uOAV3P5k/2RAkaRrYSZ6Tw+z9gWrotg0m4Df\n66wWHEfoIzL1fMsGBX5ObYW1W1NILsAPSYI/A1yeSy5MWhJAHeYnFOUkHlHm\nIAgCD0aOsj/3zdZiaWqAIYSrB6VDfPL3d/oD/r25acQHcyQCcQNn6/EraYw7\nr0z6hV+H3zWdZ6n74hi/jh7s3NoDb8fXndaqj6OO60SeScKTqb0gSVyEHs8H\nXXPbKbOHevCvD2B8FHV0Im+Duu15hABK5UijuYR4ys9OBUakr0ddsI2NduV4\nbU0v1vaHSm8GkAZN+I6Bw58zpLbvoviBXRh0cQU5QF0HdII+6L2AJyH/jZ1G\nysAs+I+I5xKKF7ssdq9A7bbSs/jW2BTV5w2ZY89cOlWa9eWJVbvLwXmQVxJ4\nzfYPGeVXhOMo4cpHxy2jXzfPJCJm1945AOp477Pa/7vU7yILTOFNvXiK84Di\nvlfR\r\n=aJ/V\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=10"}, "gitHead": "eb250b483dc0947e5693788f811d0d36f317d9e1", "scripts": {"lint": "eslint '**/*.js'", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postsnap": "npm run lintfix --", "posttest": "npm run lint", "npmclilint": "npmcli-lint", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/npm/are-we-there-yet.git", "type": "git"}, "_npmVersion": "7.21.1", "description": "Keep track of the overall completion of many disparate processes", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"delegates": "^1.0.0", "readable-stream": "^3.6.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.0.9", "eslint": "^7.32.0", "eslint-plugin-node": "^11.1.0", "@npmcli/template-oss": "^1.0.2", "@npmcli/eslint-config": "^1.0.0"}, "templateVersion": "1.0.2", "_npmOperationalInternal": {"tmp": "tmp/are-we-there-yet_2.0.0_1630592828976_0.7195111432442682", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "are-we-there-yet", "version": "3.0.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "are-we-there-yet@3.0.0", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/are-we-there-yet", "bugs": {"url": "https://github.com/npm/are-we-there-yet/issues"}, "tap": {"lines": 92, "branches": 68, "functions": 86, "statements": 92}, "dist": {"shasum": "ba20bd6b553e31d62fc8c31bd23d22b95734390d", "tarball": "https://registry.npmjs.org/are-we-there-yet/-/are-we-there-yet-3.0.0.tgz", "fileCount": 8, "integrity": "sha512-0GWpv50YSOcLXaN6/FAKY3vfRbllXWV2xvfA/oKJF8pzFhWXPV+yjhJXDBbjscDYowv7Yw1A3uigpzn5iEGTyw==", "signatures": [{"sig": "MEQCICf/iEPttWMvi6Qo7WA0s+JlhUVEk2WwtJjmDgAiSQhHAiBjRyKekpctNP5c+v/6c2o/SyA4Y6I6Yi+9UKskBaUtjQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14230, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBAg9CRA9TVsSAnZWagAAd+wQAJE48i7WC5oCp1A9SvbR\n899qpa7M/w7HegYiTI9alK+RVXlaMmNz5u7NWkcUpAKIodPc0HZ2tjv9wJ2g\noyQfazFFLWco7FHStJYyIJmD/k8HSQWBHzq/2AISNuTtCHc+0EmXOd1oZ5ct\nNu9NCa64Wv8HtLsuVhpA5Yf/TI+0RVZLDmuPdT62cnX/kbxUC77XF95V/fkj\nyM2aYxW2gYVYJVs9jkEEPqd5UVwoj1Os9VdRYgaBtHtePsBWJrowJIB7qkcl\n/AWQho2vvhYv6cleo5a3Jdghmd3khQsw2u/xqGWUB7AopboH0CCpP/C/mf9y\n1jlMf3QDwQFI6HKPVv4MVrAU73DamRdmC1dnrU6CiBv5PlbclVScS+fFFnsh\n07un5hN0qZ1dCYF1DJNsruSHUoWHnYfB/BBB/wAAQSzNmq9IBaeDbdvOXNHt\nhjXEuyKl8ai85o94hN7P8vDWynES+wkDWvXMKSQ1+8GMN2vjQ0Q4rUSnbWpt\nBO78asjWDTwrE4ci09vXPCOlIKcWtMOu368mZh2HVzEIupRx0GBJKB1Ulnxj\npB0g8IuNAzp0DOFDBrDDkyGLq4RhfdS76tUEx+7sAs2eu3VimUukZwGK5PM5\n+CFjG/B70LPUEUluXSwBV724PEbr7ozwbY/6WoKRAN5CH+F0i8leLgn79M1q\nvko3\r\n=1hQn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}, "gitHead": "2f6883e5d43c6ef2f5b0173413cae28da648437e", "scripts": {"lint": "eslint '**/*.js'", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "npm-template-check", "postsnap": "npm run lintfix --", "posttest": "npm run lint", "npmclilint": "npmcli-lint", "preversion": "npm test", "postversion": "npm publish", "template-copy": "npm-template-copy --force", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/npm/are-we-there-yet.git", "type": "git"}, "_npmVersion": "8.4.1", "description": "Keep track of the overall completion of many disparate processes", "directories": {}, "templateOSS": {"version": "2.7.1"}, "_nodeVersion": "16.14.0", "dependencies": {"delegates": "^1.0.0", "readable-stream": "^3.6.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.0.9", "eslint": "^8.8.0", "eslint-plugin-node": "^11.1.0", "@npmcli/template-oss": "^2.7.1", "@npmcli/eslint-config": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/are-we-there-yet_3.0.0_1644431421185_0.13324852005976795", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "are-we-there-yet", "version": "3.0.1", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "are-we-there-yet@3.0.1", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/are-we-there-yet", "bugs": {"url": "https://github.com/npm/are-we-there-yet/issues"}, "tap": {"lines": 92, "branches": 68, "functions": 86, "statements": 92}, "dist": {"shasum": "679df222b278c64f2cdba1175cdc00b0d96164bd", "tarball": "https://registry.npmjs.org/are-we-there-yet/-/are-we-there-yet-3.0.1.tgz", "fileCount": 8, "integrity": "sha512-QZW4EDmGwlYur0Yyf/b2uGucHQMa8aFUP7eu9ddR73vvhFyt4V0Vl3QHPcTNJ8l6qYOBdxgXdnBXQrHilfRQBg==", "signatures": [{"sig": "MEUCIQDWAfVciYujC6PyWKoSvlzBP0Iwxm7Rez4aORAIqOUMdQIgJzSK70P11AP2o3SIYa4HmgNNW2QYLfEgnG91wJXbOGE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14296, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi4av/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpO5hAAolKGuOkA9/TZaxrz7oFdR4b3mgJmhOMKQidQhvP7Sj+whPC6\r\nApC19EtGJls9scPTMTP0Ogs6qtHSx14jHuJBP69a9HKNKIM7Tkr1rzdJKP+2\r\ntNeij11PbNn4EAP10royQftc1Bnwo5B8p4a0mvJvdHUS6Bx8repW5hhOb/aw\r\nYwCWQysVktsL6+1ZcSYXDZzZc4M0GNeO+hErzhNc8tsLhKHu0zAn0rNBGKbN\r\nEMsKwBaGpboPKzuLOA32zzx+gF87L8tn5R291Lu8LPIdJWYbRa/wU7fiNUbX\r\nnpEBE9LCbpziMVQMo5gQdra4wkIkgvmAYQdFUMOMml6kAYoAJJ97Sx0jbNe5\r\ni37UK1G75RMOk8ZwZ7bkPJ/vXTiJcIEX3Ny6+e9szc3zruUusjVICTuxLxfd\r\nG3XtjHlKzxh/nzJUpeCTi6Vuftu1vL/6vMxC/nmwNuG6qgGDxgauC9yuG104\r\nhE4ubJcpYmsKKbmUEipv0zPHJFovIImkiKMxunr/bADOva8F3kFz0fcXlFR8\r\nbqCM396UcQVTH2a7DrPaPAMiO7wORPuwvCrGUqsS9MTW1kxnlK6twzHuB6xm\r\nC8TTanrX3MOtfzvIvJwRNbbgIqh8TcobBgDBWHXJKPGzDpXNbkSrWxbTcR4u\r\nCVHzd2INtvikVl3oIPJGn2gdLhyd3eqF90Y=\r\n=d6Xk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "gitHead": "a7d9cce05be9a5607161c1f30b536c034d699257", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "postsnap": "npm run lintfix --", "posttest": "npm run lint", "npmclilint": "npmcli-lint", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/npm/are-we-there-yet.git", "type": "git"}, "_npmVersion": "8.13.2", "description": "Keep track of the overall completion of many disparate processes", "directories": {}, "templateOSS": {"version": "3.5.0", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.6.0", "dependencies": {"delegates": "^1.0.0", "readable-stream": "^3.6.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/are-we-there-yet_3.0.1_1658956799129_0.3093270015535099", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "are-we-there-yet", "version": "4.0.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "are-we-there-yet@4.0.0", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/are-we-there-yet", "bugs": {"url": "https://github.com/npm/are-we-there-yet/issues"}, "tap": {"lines": 92, "nyc-arg": ["--exclude", "tap-snapshots/**"], "branches": 68, "functions": 86, "statements": 92}, "dist": {"shasum": "3ff397dc14f08b52dd8b2a64d3cee154ab8760d2", "tarball": "https://registry.npmjs.org/are-we-there-yet/-/are-we-there-yet-4.0.0.tgz", "fileCount": 8, "integrity": "sha512-nSXlV+u3vtVjRgihdTzbfWYzxPWGo424zPgQbHD0ZqIla3jqYAewDcvee0Ua2hjS5IfTAmjGlx1Jf0PKwjZDEw==", "signatures": [{"sig": "MEUCIBISUshupwt9Pw29zIidi9cQqScLCFdp7p0chgLkcbU6AiEAmMNfUXl8t69CAfsdYgbL2ElGQ/mztlWtBdrGAiallDw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14212, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSPIeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpr6A/+Nv5zlYn4jNrOcljJaZlfMPwJf+35+EI6FUKYyqFAlUj0G9Vr\r\nQJsT6QcC8RAQ1w4UzLED7YDJDs8rKU2ChgpCdAFjgZBZvRPcFn4ka4qo3eWO\r\np4th4aWcpdDvf09b4eZFfpr/IIAf2LJ+kScoOIakVfgPJzaP0jqth2MhAUDz\r\ner40CE+Ev5rtqBs5bCEdqYa6ifPBulu2cDWQdR/i/nGONGPzXvJ+pblVKHau\r\nIi6iKuXmZbARiPLyrH8sEwEtEsYYn5ng7mCcA32Si4VNL9wteWO32omD+B7j\r\nbe7DiI5nSjubDSvo3f1XtUKkKdD0lJ+PxmfAI8ETlmJKQRvfNPfH3ns5QUvi\r\nWVJS5dpJRn0sxX0ZQ8ZAojhTYhKx6l809OQoENaxuEjSyZt51ZI6DRzws8Xg\r\nl8bdDgi+j+anemkFd2itN/VYE/5KIeh+XpYyuHs0uRNm7NzkE6uKaWDelaUW\r\nw5reODLb0fQ41GZgc2YqUov0SiZi220p9phxXpeiRpULw6cUS8zOg5liY74n\r\n3n6e3FPVacfFNxHRcWQC1DvhXGUzZpGqmJBByufNw9lpYc8/7RzLqRs2Jh3i\r\nv3obNwlmlXFk6s2QHBI1Yq2DlX3GRCFWS4jOubqO0O4xY6k66598KjLRhSuo\r\nR9Y7dTRoDexeopMDmQe7IXe30mMPFZ3pNOQ=\r\n=Ie2f\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "gitHead": "ce167b77898ec7b53c572ff78271599a5d7c6d33", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "postsnap": "npm run lintfix --", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/npm/are-we-there-yet.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Keep track of the overall completion of many disparate processes", "directories": {}, "templateOSS": {"version": "4.5.1", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.10.0", "dependencies": {"delegates": "^1.0.0", "readable-stream": "^4.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "4.5.1", "@npmcli/eslint-config": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/are-we-there-yet_4.0.0_1665724958122_0.30115864970038375", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "are-we-there-yet", "version": "4.0.1", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "are-we-there-yet@4.0.1", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/are-we-there-yet", "bugs": {"url": "https://github.com/npm/are-we-there-yet/issues"}, "tap": {"lines": 92, "nyc-arg": ["--exclude", "tap-snapshots/**"], "branches": 68, "functions": 86, "statements": 92}, "dist": {"shasum": "05a6fc0e5f70771b673e82b0f915616e0ace8fd3", "tarball": "https://registry.npmjs.org/are-we-there-yet/-/are-we-there-yet-4.0.1.tgz", "fileCount": 8, "integrity": "sha512-2zuA+jpOYBRgoBCfa+fB87Rk0oGJjDX6pxGzqH6f33NzUhG25Xur6R0u0Z9VVAq8Z5JvQpQI6j6rtonuivC8QA==", "signatures": [{"sig": "MEYCIQCoxlL3kM5cdzKWBlosxIAFIEVxv40Y24FjnEk9Vdu/rQIhAN/XrMUDobjT6cLeKMlP0T0bORweirEnFJQ0SmK7XgBl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/are-we-there-yet@4.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13639}, "main": "lib/index.js", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "gitHead": "d3ded79372ea233782297abfae1126d542480bf3", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "postsnap": "npm run lintfix --", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/npm/are-we-there-yet.git", "type": "git"}, "_npmVersion": "9.8.0", "description": "Keep track of the overall completion of many disparate processes", "directories": {}, "templateOSS": {"publish": true, "version": "4.17.0", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.16.1", "dependencies": {"delegates": "^1.0.0", "readable-stream": "^4.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "4.17.0", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/are-we-there-yet_4.0.1_1689627504944_0.15928879383501893", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "are-we-there-yet", "version": "4.0.2", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "are-we-there-yet@4.0.2", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/are-we-there-yet", "bugs": {"url": "https://github.com/npm/are-we-there-yet/issues"}, "tap": {"lines": 92, "nyc-arg": ["--exclude", "tap-snapshots/**"], "branches": 68, "functions": 86, "statements": 92}, "dist": {"shasum": "aed25dd0eae514660d49ac2b2366b175c614785a", "tarball": "https://registry.npmjs.org/are-we-there-yet/-/are-we-there-yet-4.0.2.tgz", "fileCount": 8, "integrity": "sha512-ncSWAawFhKMJDTdoAeOV+jyW1VCMj5QIAwULIBV0SSR7B/RLPPEQiknKcg/RIIZlUQrxELpsxMiTUoAQ4sIUyg==", "signatures": [{"sig": "MEQCIB8YYM9vOzlyC6/SxmtgznS0bRhMV7W3I1g4UQtiJ52hAiAjYiYMf/939jMt6aBP8pESa9P9Je731xEoQXyp6kdXMQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/are-we-there-yet@4.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13527}, "main": "lib/index.js", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "gitHead": "4f4c7daa424e5514a5da4067249ee5b96c20d6df", "scripts": {"lint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "postsnap": "npm run lintfix --", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "deprecated": "This package is no longer supported.", "repository": {"url": "git+https://github.com/npm/are-we-there-yet.git", "type": "git"}, "_npmVersion": "10.2.5", "description": "Keep track of the overall completion of many disparate processes", "directories": {}, "templateOSS": {"publish": true, "version": "4.21.3", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "4.21.3", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/are-we-there-yet_4.0.2_1704468939884_0.028530956795204565", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2014-12-08T07:52:31.405Z", "modified": "2024-07-31T15:23:55.694Z", "1.0.0": "2014-12-08T07:52:31.405Z", "1.0.1": "2014-12-20T00:41:45.176Z", "1.0.2": "2014-12-23T18:42:32.377Z", "1.0.3": "2015-03-03T16:02:36.752Z", "1.0.4": "2015-04-01T05:20:34.126Z", "1.0.5": "2015-12-06T00:50:14.344Z", "1.0.6": "2016-01-27T23:14:53.606Z", "1.1.0": "2016-01-30T00:55:59.820Z", "1.1.1": "2016-01-30T01:47:51.372Z", "1.1.2": "2016-03-15T23:26:37.866Z", "1.1.3": "2017-04-21T07:45:04.633Z", "1.1.4": "2017-04-21T07:46:32.532Z", "1.1.5": "2018-05-24T22:08:00.772Z", "1.1.6": "2021-09-01T17:38:13.163Z", "1.1.7": "2021-09-02T14:25:57.235Z", "2.0.0": "2021-09-02T14:27:09.143Z", "3.0.0": "2022-02-09T18:30:21.409Z", "3.0.1": "2022-07-27T21:19:59.597Z", "4.0.0": "2022-10-14T05:22:38.303Z", "4.0.1": "2023-07-17T20:58:25.113Z", "4.0.2": "2024-01-05T15:35:40.021Z"}, "bugs": {"url": "https://github.com/npm/are-we-there-yet/issues"}, "author": {"name": "GitHub Inc."}, "license": "ISC", "homepage": "https://github.com/npm/are-we-there-yet", "repository": {"url": "git+https://github.com/npm/are-we-there-yet.git", "type": "git"}, "description": "Keep track of the overall completion of many disparate processes", "maintainers": [{"email": "<EMAIL>", "name": "hashtagchris"}, {"email": "<EMAIL>", "name": "reggi"}, {"email": "<EMAIL>", "name": "npm-cli-ops"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "fritzy"}, {"email": "<EMAIL>", "name": "gar"}], "readme": "are-we-there-yet\n----------------\n\nTrack complex hierarchies of asynchronous task completion statuses.  This is\nintended to give you a way of recording and reporting the progress of the big\nrecursive fan-out and gather type workflows that are so common in async.\n\nWhat you do with this completion data is up to you, but the most common use case is to\nfeed it to one of the many progress bar modules.\n\nMost progress bar modules include a rudimentary version of this, but my\nneeds were more complex.\n\nUsage\n=====\n\n```javascript\nvar TrackerGroup = require(\"are-we-there-yet\").TrackerGroup\n\nvar top = new TrackerGroup(\"program\")\n\nvar single = top.newItem(\"one thing\", 100)\nsingle.completeWork(20)\n\nconsole.log(top.completed()) // 0.2\n\nfs.stat(\"file\", function(er, stat) {\n  if (er) throw er  \n  var stream = top.newStream(\"file\", stat.size)\n  console.log(top.completed()) // now 0.1 as single is 50% of the job and is 20% complete\n                              // and 50% * 20% == 10%\n  fs.createReadStream(\"file\").pipe(stream).on(\"data\", function (chunk) {\n    // do stuff with chunk\n  })\n  top.on(\"change\", function (name) {\n    // called each time a chunk is read from \"file\"\n    // top.completed() will start at 0.1 and fill up to 0.6 as the file is read\n  })\n})\n```\n\nShared Methods\n==============\n\n* var completed = tracker.completed()\n\nImplemented in: `Tracker`, `TrackerGroup`, `TrackerStream`\n\nReturns the ratio of completed work to work to be done. Range of 0 to 1.\n\n* tracker.finish()\n\nImplemented in: `Tracker`, `TrackerGroup`\n\nMarks the tracker as completed. With a TrackerGroup this marks all of its\ncomponents as completed.\n\nMarks all of the components of this tracker as finished, which in turn means\nthat `tracker.completed()` for this will now be 1.\n\nThis will result in one or more `change` events being emitted.\n\nEvents\n======\n\nAll tracker objects emit `change` events with the following arguments:\n\n```\nfunction (name, completed, tracker)\n```\n\n`name` is the name of the tracker that originally emitted the event,\nor if it didn't have one, the first containing tracker group that had one.\n\n`completed` is the percent complete (as returned by `tracker.completed()` method).\n\n`tracker` is the tracker object that you are listening for events on.\n\nTrackerGroup\n============\n\n* var tracker = new TrackerGroup(**name**)\n\n  * **name** *(optional)* - The name of this tracker group, used in change\n    notifications if the component updating didn't have a name. Defaults to undefined.\n\nCreates a new empty tracker aggregation group. These are trackers whose\ncompletion status is determined by the completion status of other trackers added to this aggregation group.\n\nEx.\n\n```javascript\nvar tracker = new TrackerGroup(\"parent\")\nvar foo = tracker.newItem(\"firstChild\", 100)\nvar bar = tracker.newItem(\"secondChild\", 100)\n\nfoo.finish()\nconsole.log(tracker.completed()) // 0.5\nbar.finish()\nconsole.log(tracker.completed()) // 1\n```\n\n* tracker.addUnit(**otherTracker**, **weight**)\n\n  * **otherTracker** - Any of the other are-we-there-yet tracker objects\n  * **weight** *(optional)* - The weight to give the tracker, defaults to 1.\n\nAdds the **otherTracker** to this aggregation group. The weight determines\nhow long you expect this tracker to take to complete in proportion to other\nunits.  So for instance, if you add one tracker with a weight of 1 and\nanother with a weight of 2, you're saying the second will take twice as long\nto complete as the first.  As such, the first will account for 33% of the\ncompletion of this tracker and the second will account for the other 67%.\n\nReturns **otherTracker**.\n\n* var subGroup = tracker.newGroup(**name**, **weight**)\n\nThe above is exactly equivalent to:\n\n```javascript\n  var subGroup = tracker.addUnit(new TrackerGroup(name), weight)\n```\n\n* var subItem = tracker.newItem(**name**, **todo**, **weight**)\n\nThe above is exactly equivalent to:\n\n```javascript\n  var subItem = tracker.addUnit(new Tracker(name, todo), weight)\n```\n\n* var subStream = tracker.newStream(**name**, **todo**, **weight**)\n\nThe above is exactly equivalent to:\n\n```javascript\n  var subStream = tracker.addUnit(new TrackerStream(name, todo), weight)\n```\n\n* console.log( tracker.debug() )\n\nReturns a tree showing the completion of this tracker group and all of its\nchildren, including recursively entering all of the children.\n\nTracker\n=======\n\n* var tracker = new Tracker(**name**, **todo**)\n\n  * **name** *(optional)* The name of this counter to report in change\n    events.  Defaults to undefined.\n  * **todo** *(optional)* The amount of work todo (a number). Defaults to 0.\n\nOrdinarily these are constructed as a part of a tracker group (via\n`newItem`).\n\n* var completed = tracker.completed()\n\nReturns the ratio of completed work to work to be done. Range of 0 to 1. If\ntotal work to be done is 0 then it will return 0.\n\n* tracker.addWork(**todo**)\n\n  * **todo** A number to add to the amount of work to be done.\n\nIncreases the amount of work to be done, thus decreasing the completion\npercentage.  Triggers a `change` event.\n\n* tracker.completeWork(**completed**)\n\n  * **completed** A number to add to the work complete\n\nIncrease the amount of work complete, thus increasing the completion percentage.\nWill never increase the work completed past the amount of work todo. That is,\npercentages > 100% are not allowed. Triggers a `change` event.\n\n* tracker.finish()\n\nMarks this tracker as finished, tracker.completed() will now be 1. Triggers\na `change` event.\n\nTrackerStream\n=============\n\n* var tracker = new TrackerStream(**name**, **size**, **options**)\n\n  * **name** *(optional)* The name of this counter to report in change\n    events.  Defaults to undefined.\n  * **size** *(optional)* The number of bytes being sent through this stream.\n  * **options** *(optional)* A hash of stream options\n\nThe tracker stream object is a pass through stream that updates an internal\ntracker object each time a block passes through.  It's intended to track\ndownloads, file extraction and other related activities. You use it by piping\nyour data source into it and then using it as your data source.\n\nIf your data has a length attribute then that's used as the amount of work\ncompleted when the chunk is passed through.  If it does not (eg, object\nstreams) then each chunk counts as completing 1 unit of work, so your size\nshould be the total number of objects being streamed.\n\n* tracker.addWork(**todo**)\n\n  * **todo** Increase the expected overall size by **todo** bytes.\n\nIncreases the amount of work to be done, thus decreasing the completion\npercentage.  Triggers a `change` event.\n", "readmeFilename": "README.md", "users": {"mk": true, "dm07": true, "f_vdd": true, "iarna": true, "ncrosby": true, "snopeks": true, "wenbing": true, "byoigres": true, "henrytseng": true, "danbluefoot": true}}