{"_id": "webidl-conversions", "_rev": "27-c603cc712d28b132e19d39c2a57cf18f", "name": "webidl-conversions", "description": "Implements the WebIDL algorithms for converting to and from JavaScript values", "dist-tags": {"latest": "7.0.0"}, "versions": {"1.0.0": {"name": "webidl-conversions", "version": "1.0.0", "description": "Implements the WebIDL algorithms for converting to and from JavaScript values", "main": "lib/index.js", "scripts": {"test": "mocha --compilers js:mocha-traceur test/*.js"}, "repository": {"type": "git", "url": "https://github.com/domenic/webidl-conversions"}, "keywords": ["webidl", "web", "types"], "files": ["lib/", "LICENSE.md"], "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "http://domenic.me/"}, "license": "BSD-2-<PERSON><PERSON>", "bugs": {"url": "https://github.com/domenic/webidl-conversions/issues"}, "devDependencies": {"mocha": "^1.21.4", "mocha-traceur": "domenic/mocha-traceur#patch-1", "traceur": "^0.0.58"}, "gitHead": "e9437f96768ef3a77440978792fdd667d1fb54fa", "homepage": "https://github.com/domenic/webidl-conversions", "_id": "webidl-conversions@1.0.0", "_shasum": "7ecdd236d22f60d35881c26b1c9aac38a9812532", "_from": ".", "_npmVersion": "1.4.16", "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "maintainers": [{"name": "domenic", "email": "<EMAIL>"}], "dist": {"shasum": "7ecdd236d22f60d35881c26b1c9aac38a9812532", "tarball": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-1.0.0.tgz", "integrity": "sha512-<PERSON><PERSON><PERSON>zezoWWukQXlmHPgmooeZT53O1auXZvj2waIzkU8eX3qffTcglD1mK8LPHfzsdZ6m87X7cYxQ44fEA/JdrUw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE2NDYDv18cJyAV2Jfh9FW/WiZpZ5iCTrU1pS4UqXC4lAiAm04Tk9Bj2To+nfUKJ0zvR3xM80ffJC+Bv42hI0FiCzw=="}]}, "directories": {}}, "1.0.1": {"name": "webidl-conversions", "version": "1.0.1", "description": "Implements the WebIDL algorithms for converting to and from JavaScript values", "main": "lib/index.js", "scripts": {"test": "mocha --compilers js:mocha-traceur test/*.js"}, "repository": {"type": "git", "url": "https://github.com/domenic/webidl-conversions"}, "keywords": ["webidl", "web", "types"], "files": ["lib/", "LICENSE.md"], "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "http://domenic.me/"}, "license": "BSD-2-<PERSON><PERSON>", "bugs": {"url": "https://github.com/domenic/webidl-conversions/issues"}, "devDependencies": {"mocha": "^1.21.4", "mocha-traceur": "domenic/mocha-traceur#patch-1", "traceur": "^0.0.58"}, "browserify": {"transform": ["es6ify"]}, "gitHead": "fcd60fbfca200b73705d0b961fd4a97877c64a02", "homepage": "https://github.com/domenic/webidl-conversions", "_id": "webidl-conversions@1.0.1", "_shasum": "34f0456035f4dbcdac194b344a86abb3189353bb", "_from": ".", "_npmVersion": "1.4.16", "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "maintainers": [{"name": "domenic", "email": "<EMAIL>"}], "dist": {"shasum": "34f0456035f4dbcdac194b344a86abb3189353bb", "tarball": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-1.0.1.tgz", "integrity": "sha512-JRQB/72raxkJPCG95zGggNqElQ2OXOfUueBCkQwgkvE2DyzVVI0UeN7Ffd9VYqB8J3e5cbGdQfTe+sbwEnMLPw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDNB3fy+72iKSDRAF8vnGElMvENgz+8D3zgQ1aXqPq4KQIhALLXZOdNMrBEAEI8+7l6tMVHpRhWf8sjIjAfB7bph0dI"}]}, "directories": {}}, "1.1.0": {"name": "webidl-conversions", "version": "1.1.0", "description": "Implements the WebIDL algorithms for converting to and from JavaScript values", "main": "lib/index.js", "scripts": {"test": "mocha --compilers js:mocha-traceur test/*.js"}, "repository": {"type": "git", "url": "https://github.com/domenic/webidl-conversions"}, "keywords": ["webidl", "web", "types"], "files": ["lib/", "LICENSE.md"], "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "http://domenic.me/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"mocha": "^1.21.4", "mocha-traceur": "^2.0.0", "traceur": "^0.0.66"}, "browserify": {"transform": ["es6ify"]}, "gitHead": "ccdf8cfbae03863584a1c9208bf2ba1ad26f1a90", "bugs": {"url": "https://github.com/domenic/webidl-conversions/issues"}, "homepage": "https://github.com/domenic/webidl-conversions", "_id": "webidl-conversions@1.1.0", "_shasum": "5b51cc436b3ea959f74dcf64576aef45fc5ac551", "_from": ".", "_npmVersion": "2.0.0", "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "maintainers": [{"name": "domenic", "email": "<EMAIL>"}], "dist": {"shasum": "5b51cc436b3ea959f74dcf64576aef45fc5ac551", "tarball": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-1.1.0.tgz", "integrity": "sha512-ZZJPecNCddu0lev0PLdiMGvAEzr0gJkaUkgLYsX1vHkd7cIJ5604wfnt6peoZ0pKXJ6ouRQ1GxwOeIRQQuL0eg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE1npwjaDzqSA/Cp5NafFaW3mZTJnmIOH3zdlwS0yq16AiEAroBOVh+qhc8jfIFYmPqePD9twkq3RWtvfxREn/T8hnk="}]}, "directories": {}}, "1.2.0": {"name": "webidl-conversions", "version": "1.2.0", "description": "Implements the WebIDL algorithms for converting to and from JavaScript values", "main": "lib/index.js", "scripts": {"test": "mocha --compilers js:mocha-traceur test/*.js"}, "repository": {"type": "git", "url": "https://github.com/domenic/webidl-conversions"}, "keywords": ["webidl", "web", "types"], "files": ["lib/", "LICENSE.md"], "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "http://domenic.me/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"mocha": "^1.21.4", "mocha-traceur": "^2.0.0", "traceur": "^0.0.66"}, "browserify": {"transform": ["es6ify"]}, "gitHead": "106f0b26e55ee0644c898350b1813393dac748bb", "bugs": {"url": "https://github.com/domenic/webidl-conversions/issues"}, "homepage": "https://github.com/domenic/webidl-conversions", "_id": "webidl-conversions@1.2.0", "_shasum": "83a54429e22e634d800cf1e27fd4aad12ee8d8e0", "_from": ".", "_npmVersion": "2.0.0", "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "maintainers": [{"name": "domenic", "email": "<EMAIL>"}], "dist": {"shasum": "83a54429e22e634d800cf1e27fd4aad12ee8d8e0", "tarball": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-1.2.0.tgz", "integrity": "sha512-szZaKxzEIOtshkFEbjRz9OCXFvqkrRBeXqv48v78ta3G73Vt62Piaxi7q0tUMg4LJ8ZQfDNHikp1v25K1p/5wQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHRozjPuwn3nBXddRc1ELrds5Y5TH0GjQ3smvwhuRSlXAiEA/Kfto4qu0ZG41joq/W9EDkX6+4Cp7qtaU/87O+EFLBA="}]}, "directories": {}}, "1.2.1": {"name": "webidl-conversions", "version": "1.2.1", "description": "Implements the WebIDL algorithms for converting to and from JavaScript values", "main": "lib/index.js", "scripts": {"test": "mocha --compilers js:mocha-traceur test/*.js"}, "repository": {"type": "git", "url": "https://github.com/domenic/webidl-conversions"}, "keywords": ["webidl", "web", "types"], "files": ["lib/"], "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"mocha": "^1.21.4", "mocha-traceur": "^2.0.0", "traceur": "^0.0.79"}, "browserify": {"transform": ["es6ify"]}, "traceur-runner": true, "gitHead": "39d954a64a8958130416d128de1cb8640d72b9a0", "bugs": {"url": "https://github.com/domenic/webidl-conversions/issues"}, "homepage": "https://github.com/domenic/webidl-conversions", "_id": "webidl-conversions@1.2.1", "_shasum": "0b5b694eddb964c0ad2eac21772b9e667ee527eb", "_from": ".", "_npmVersion": "2.1.3", "_nodeVersion": "0.10.33", "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "maintainers": [{"name": "domenic", "email": "<EMAIL>"}], "dist": {"shasum": "0b5b694eddb964c0ad2eac21772b9e667ee527eb", "tarball": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-1.2.1.tgz", "integrity": "sha512-xiHn+egAZFVCYMLthfhO0ySeQAhi9rc7XDqnlXK5Y4UnIfXhRz3CDjCmMY/D3cOKOmhouvfgFLozhF1ZmJNtgg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHpLuK5YdoDfNliIt94zv7TXeQ+rwybnFxFaCfucbsxPAiEAx34Nb2gnIsxEQhs3Yx7QTxIWxTmmqZXih8PfMVap6o8="}]}, "directories": {}}, "2.0.0": {"name": "webidl-conversions", "version": "2.0.0", "description": "Implements the WebIDL algorithms for converting to and from JavaScript values", "main": "lib/index.js", "scripts": {"test": "mocha test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/jsdom/webidl-conversions.git"}, "keywords": ["webidl", "web", "types"], "files": ["lib/"], "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"mocha": "^1.21.4"}, "gitHead": "5fe0ad00d2f1ff5c48787e459c067ef27b1ef5a1", "bugs": {"url": "https://github.com/jsdom/webidl-conversions/issues"}, "homepage": "https://github.com/jsdom/webidl-conversions#readme", "_id": "webidl-conversions@2.0.0", "_shasum": "878f40d16ab093b76dd1196e096a69761ede5ebf", "_from": ".", "_npmVersion": "2.14.3", "_nodeVersion": "4.1.0", "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}], "dist": {"shasum": "878f40d16ab093b76dd1196e096a69761ede5ebf", "tarball": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-2.0.0.tgz", "integrity": "sha512-E0ZNrv2EXbyjkATrM74Cdjat/Wi8yKXaDgG33RI6UWGjm+8jva2+Mb1PJOpAPwOJYCzDYXhjc2K5Nn+F5OxT/g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHKhZPbLvrcnjCuVOwst/KyoqWw6Kuf5+KTYz1veh8/aAiEArbBOpvF3gelZpYSt45PeYtiei28YmLjJsz/Gk1Z6HuY="}]}, "directories": {}}, "2.0.1": {"name": "webidl-conversions", "version": "2.0.1", "description": "Implements the WebIDL algorithms for converting to and from JavaScript values", "main": "lib/index.js", "scripts": {"test": "mocha test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/jsdom/webidl-conversions.git"}, "keywords": ["webidl", "web", "types"], "files": ["lib/"], "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"mocha": "^1.21.4"}, "gitHead": "46f236ff6c2fd5a83cddffaadb78c8d9edbb679b", "bugs": {"url": "https://github.com/jsdom/webidl-conversions/issues"}, "homepage": "https://github.com/jsdom/webidl-conversions#readme", "_id": "webidl-conversions@2.0.1", "_shasum": "3bf8258f7d318c7443c36f2e169402a1a6703506", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.2", "_npmUser": {"name": "sebmaster", "email": "<EMAIL>"}, "dist": {"shasum": "3bf8258f7d318c7443c36f2e169402a1a6703506", "tarball": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-2.0.1.tgz", "integrity": "sha512-OZ7I/f0sM+T28T2/OXinNGfmvjm3KKptdyQy8NPRZyLfYBn+9vt72Bfr+uQaE9OvWyxJjQ5kHFygH2wOTUb76g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCEzRrGcwvf3GzddVLf8gnbD7qPFQ5faYgjk+AH87vVSwIgKc7UyPu/B9skucIv9fWFwIXQ5VsHavriroTeAm6ftww="}]}, "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}], "directories": {}}, "3.0.0": {"name": "webidl-conversions", "version": "3.0.0", "description": "Implements the WebIDL algorithms for converting to and from JavaScript values", "main": "lib/index.js", "scripts": {"test": "mocha test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/jsdom/webidl-conversions.git"}, "keywords": ["webidl", "web", "types"], "files": ["lib/"], "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"mocha": "^1.21.4"}, "gitHead": "f764f90bfbd7ec091aad93f4b4444e6599e1ee1e", "bugs": {"url": "https://github.com/jsdom/webidl-conversions/issues"}, "homepage": "https://github.com/jsdom/webidl-conversions#readme", "_id": "webidl-conversions@3.0.0", "_shasum": "60d90575959fdcd22786279bb45ace5be17ef48c", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.2", "_npmUser": {"name": "sebmaster", "email": "<EMAIL>"}, "dist": {"shasum": "60d90575959fdcd22786279bb45ace5be17ef48c", "tarball": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.0.tgz", "integrity": "sha512-GOK494qXZl0XBbr/YPRW2BL6oLfe+d0KDSWndKH7NdxJwJ0fCdhQG46Q2D7BmIS6cN/FJgtju8srRt/jlG9abA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBrjJk3CowiWWkMkQqH30DIiXNpGIizaS59wQThHPLTrAiBxhQ8CUK4m0SjZ2f19jc/+2Y1ZmvczBz0BZMh/KjqBZw=="}]}, "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}], "directories": {}}, "3.0.1": {"name": "webidl-conversions", "version": "3.0.1", "description": "Implements the WebIDL algorithms for converting to and from JavaScript values", "main": "lib/index.js", "scripts": {"test": "mocha test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/jsdom/webidl-conversions.git"}, "keywords": ["webidl", "web", "types"], "files": ["lib/"], "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"mocha": "^1.21.4"}, "gitHead": "e21056f891736db755cfe0d72e4ce083a9034ace", "bugs": {"url": "https://github.com/jsdom/webidl-conversions/issues"}, "homepage": "https://github.com/jsdom/webidl-conversions#readme", "_id": "webidl-conversions@3.0.1", "_shasum": "24534275e2a7bc6be7bc86611cc16ae0a5654871", "_from": ".", "_npmVersion": "3.5.3", "_nodeVersion": "5.3.0", "_npmUser": {"name": "sebmaster", "email": "<EMAIL>"}, "dist": {"shasum": "24534275e2a7bc6be7bc86611cc16ae0a5654871", "tarball": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.1.tgz", "integrity": "sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDzhytf8d6Ch5BtXXuf+2pDKkCboj177ReEzVvuRh1VHwIhANMEPdP96NnmsOGqfhifuuWBk2zx0DTXJUtSFDxw4s/T"}]}, "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}], "directories": {}}, "4.0.0": {"name": "webidl-conversions", "version": "4.0.0", "description": "Implements the WebIDL algorithms for converting to and from JavaScript values", "main": "lib/index.js", "scripts": {"lint": "eslint .", "test": "mocha test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/jsdom/webidl-conversions.git"}, "keywords": ["webidl", "web", "types"], "files": ["lib/"], "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"eslint": "^3.15.0", "mocha": "^1.21.4"}, "gitHead": "c6833ad0cdd6c4610bf8bba4cbc72852584e1f03", "bugs": {"url": "https://github.com/jsdom/webidl-conversions/issues"}, "homepage": "https://github.com/jsdom/webidl-conversions#readme", "_id": "webidl-conversions@4.0.0", "_shasum": "0a8c727ae4e5649687b7742368dcfbf13ed40118", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "7.2.1", "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "dist": {"shasum": "0a8c727ae4e5649687b7742368dcfbf13ed40118", "tarball": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-4.0.0.tgz", "integrity": "sha512-x9o0i806VMP+ksK4KpUO7ncu2J53kvMYE8lnc9kyQRqwviYAo8RKHF8ey5YSEXL/OchPAwOLE10dugAeZZjcVw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDRTQdDrnAZBqNvdtyekGdQqM2r5mL8LPFFvIH/qYGQKAIhAJaqK5a2wtmipHhQx5aks3BYQnacxO9Kgv0hVn8/zpG3"}]}, "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/webidl-conversions-4.0.0.tgz_1486853103342_0.555978914257139"}, "directories": {}}, "4.0.1": {"name": "webidl-conversions", "version": "4.0.1", "description": "Implements the WebIDL algorithms for converting to and from JavaScript values", "main": "lib/index.js", "scripts": {"lint": "eslint .", "test": "mocha test/*.js", "coverage": "nyc mocha test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/jsdom/webidl-conversions.git"}, "keywords": ["webidl", "web", "types"], "files": ["lib/"], "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"eslint": "^3.15.0", "mocha": "^1.21.4", "nyc": "^10.1.2"}, "gitHead": "542e4ae1d660ccdfc9f158c8bf0a1ce9af846412", "bugs": {"url": "https://github.com/jsdom/webidl-conversions/issues"}, "homepage": "https://github.com/jsdom/webidl-conversions#readme", "_id": "webidl-conversions@4.0.1", "_shasum": "8015a17ab83e7e1b311638486ace81da6ce206a0", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "7.2.1", "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "dist": {"shasum": "8015a17ab83e7e1b311638486ace81da6ce206a0", "tarball": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-4.0.1.tgz", "integrity": "sha512-witT6j7HkPKirOw99009MFopOOTA6gRdqi6n8+lBcwk/HpqXZ8sARHtR05njqMFjeNWA7MteRxF+KAhCojB9Pg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD21A0fA5QG78tuOHBAKOQQseQTldAN2hWgXGjH8m9PWQIhAO6bEA3QETXPCrC+OH3PKKe3bp/gJg9ofiRPzYMieJyB"}]}, "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/webidl-conversions-4.0.1.tgz_1487625578701_0.24767242977395654"}, "directories": {}}, "4.0.2": {"name": "webidl-conversions", "version": "4.0.2", "description": "Implements the WebIDL algorithms for converting to and from JavaScript values", "main": "lib/index.js", "scripts": {"lint": "eslint .", "test": "mocha test/*.js", "coverage": "nyc mocha test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/jsdom/webidl-conversions.git"}, "keywords": ["webidl", "web", "types"], "files": ["lib/"], "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"eslint": "^3.15.0", "mocha": "^1.21.4", "nyc": "^10.1.2"}, "gitHead": "074b6ec5371edb7f0275625702eda6aecf1c24af", "bugs": {"url": "https://github.com/jsdom/webidl-conversions/issues"}, "homepage": "https://github.com/jsdom/webidl-conversions#readme", "_id": "webidl-conversions@4.0.2", "_npmVersion": "5.3.0", "_nodeVersion": "8.3.0", "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-YQ+BmxuTgd6UXZW3+ICGfyqRyHXVlD5GtQr5+qjiNW7bF0cqrzX500HVXPBOvgXb5YnzDd+h0zqyv61KUD7+Sg==", "shasum": "a855980b1f0b6b359ba1d5d9fb39ae941faa63ad", "tarball": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-4.0.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDreL8a7I0D8dM5HggAJm3DF/5e1/daInapzyj4sQi7jAiEAkrD7ZdIHbueoJrKoaUkja8pZjhmMRc247Sb8d0r4eb4="}]}, "maintainers": [{"email": "<EMAIL>", "name": "sebmaster"}, {"email": "<EMAIL>", "name": "domenic"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/webidl-conversions-4.0.2.tgz_1502681485162_0.9069484581705183"}, "directories": {}}, "5.0.0": {"name": "webidl-conversions", "version": "5.0.0", "description": "Implements the WebIDL algorithms for converting to and from JavaScript values", "main": "lib/index.js", "scripts": {"lint": "eslint .", "test": "mocha test/*.js", "coverage": "nyc mocha test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/jsdom/webidl-conversions.git"}, "keywords": ["webidl", "web", "types"], "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"eslint": "^6.7.2", "mocha": "^6.2.2", "nyc": "^14.1.1"}, "engines": {"node": ">=8"}, "gitHead": "2fde130c3574fffe9bf806b863464b5423f149b8", "bugs": {"url": "https://github.com/jsdom/webidl-conversions/issues"}, "homepage": "https://github.com/jsdom/webidl-conversions#readme", "_id": "webidl-conversions@5.0.0", "_nodeVersion": "12.1.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-VlZwKPCkYKxQgeSbH5EyngOmRp7Ww7I9rQLERETtf5ofd9pGeswWiOtogpEO850jziPRarreGxn5QIiTqpb2wA==", "shasum": "ae59c8a00b121543a2acc65c0434f57b0fc11aff", "tarball": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-5.0.0.tgz", "fileCount": 4, "unpackedSize": 19951, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd7AomCRA9TVsSAnZWagAAovgQAJyAeW5E6ZV1SmhjG/yR\nI8Jz9lp1cMeImibQUSHkOf//iUb1gAV4oBhNF69054cGav+cHvngqQG/wJUb\nOREAAh5uiuJZ0/ue8Vy2gemB7jpztRIw+QygVwyQwlj/C+9+j9QKiNzqz3z5\ndQoEqwR+VM/elWhUQ2dPFtR3f/ihEF4bfHr7biRuLa0sroHJ2wf4ss32svVn\nmMhh0yJVdmJtCUa0SmdL6+BvCX8j/CLBSYzS+eK1aATmN7N7rYahOHLd2HK9\nDQVBtX+gXFmDao3femuPKehIUlHK/KxmGd1UreYFxk15fAc2ychIo/lQkD+S\nOQTeuRn12XSEzQKaGPNB3GUUgLUuXoQXXTwdWIdyy+qvlnqVnoZT87ej+nhD\nPWE+X6ytIeIBZW9YAW9YgsXCwlD78Uz+i+QYH2ZTNZzh/lSUsW4vybB3BATD\nNx+9YYRyRcozJ6rnLmMvrmFrLkW3o+B/OpO66jnJdAtzsSJ0OJd0dJEiKph9\nyvF3r3igI5chT+WhMZFvdfcomF94BhwZqMemp0KgRZ7fDZa4Dob5AfUnkNd5\ngMnDFM7s7T/OVtW2etVNPC+zf80B8gJNv5vOoKRVV15kaKdFEUNeh8d5phEJ\ncUqxAGUoarEHFjVWW3UVok0/poj08brByDLu39VbsTBIUGV8c0/E+Zbymqjl\nSFEq\r\n=sjKZ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCFW5t74b589+/OyNjaayl0bsZwqOslWcn37t3kN9xyyQIgejUVenmaFteP/ADUTW96Hz19lyfC5DCcYU2lv6ecX8M="}]}, "maintainers": [{"email": "<EMAIL>", "name": "domenic"}, {"email": "<EMAIL>", "name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel"}, {"email": "<EMAIL>", "name": "sebmaster"}, {"email": "<EMAIL>", "name": "timothy<PERSON>"}, {"email": "<EMAIL>", "name": "tmpvar"}, {"email": "<EMAIL>", "name": "zirro"}], "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/webidl-conversions_5.0.0_1575750181659_0.6579450587017879"}, "_hasShrinkwrap": false}, "6.0.0": {"name": "webidl-conversions", "version": "6.0.0", "description": "Implements the WebIDL algorithms for converting to and from JavaScript values", "main": "lib/index.js", "scripts": {"lint": "eslint .", "test": "mocha test/*.js", "coverage": "nyc mocha test/*.js"}, "repository": {"type": "git", "url": "https://github.com/jsdom/webidl-conversions.git"}, "keywords": ["webidl", "web", "types"], "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"eslint": "^6.8.0", "mocha": "^7.1.1", "nyc": "^15.0.0"}, "engines": {"node": ">=10.4"}, "licenseText": "# The BSD 2-Clause License\n\nCopyright (c) 2014, Domenic Denicola\nAll rights reserved.\n\nRedistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:\n\n1. Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.\n\n2. Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.\n\nTHIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n", "_id": "webidl-conversions@6.0.0", "dist": {"shasum": "ff41d921371f43e772dba311b146ab6c0ef0ead4", "integrity": "sha512-jTZAeJnc6D+yAOjygbJOs33kVQIk5H6fj9SFDOhIKjsf9HiAzL/c+tAJsc8ASWafvhNkH+wJZms47pmajkhatA==", "tarball": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-6.0.0.tgz", "fileCount": 6, "unpackedSize": 22712, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeeRXzCRA9TVsSAnZWagAAHj8P/RCygDuQz5kPvwh5GYHg\nBBkzUE2PuXzwtfzCEiEmUkM4MWHgHrP4G4oxpkpXy5/xo95Ao2fUR4RCWpv0\nZb89j5FxrggQCvTlkVqo9FCcHtB5gRkkpuoHrmiDEaLBPAWIoEeA8gOEOS5u\nKWbFN5WEQXxQFNZHZixFDilBdhJKQ7lcZCN1WRhVq9IyVeORnfR2HxRyKwq8\nJ4RofUHxXLVLoCjr00s5jlc4P6DO5rPXTGY3D5MC9Vjhub9EE5mJ4Ls6RxZ5\n/IZe/QqmGqVPbzAciG24Zg5t1QPI3tQNKdoBMjNpJI2+Cqk0cHXvBH1ub68r\nc/sqcVLZCTgotnaKP/H01NmQn9rjUdZ/96SysUThmebUzuwLJHv5nIRnDCPU\nXllfcFntgnYw/uHWInSLeCb+XfjaUdpTXkkPeSnJwQSIJEPbxnI4sSoGcAuj\nn9X012Gnp5z/5XEh+7y/61alZsU4S5DLjCH52E28pRBp02RsmR9jwwBb0JHj\no2/JrOte9ZEQ+KlaPVYnD3zT6xGSINU5lJ5z0LkyIeeHIlYqBX4c4klaGuEE\n1JBEHvuQpJk/TUcQhNmMMahmA5PrTQbPzwo0YF9Kakr3iCypwh5NvJ2DsApH\nj0kAMhaXJISz9U4oc7wn6+O5krrZz5KHGDZrsZGe2XTPcs1WhpYXfWOG7CoG\neKUe\r\n=0Hh1\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDhXmux8TVK1aIsSjtPfnRjukAWG9Ko/jixgXsP+hi0igIgK092y7kIbPoEOC+HWgu+FwsAmqXrgK2cjkj5dUWIwbg="}]}, "maintainers": [{"email": "<EMAIL>", "name": "domenic"}, {"email": "<EMAIL>", "name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel"}, {"email": "<EMAIL>", "name": "sebmaster"}, {"email": "<EMAIL>", "name": "timothy<PERSON>"}, {"email": "<EMAIL>", "name": "tmpvar"}, {"email": "<EMAIL>", "name": "zirro"}], "_npmUser": {"name": "timothy<PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/webidl-conversions_6.0.0_1584993779180_0.1748446005748423"}, "_hasShrinkwrap": false}, "6.1.0": {"name": "webidl-conversions", "version": "6.1.0", "description": "Implements the WebIDL algorithms for converting to and from JavaScript values", "main": "lib/index.js", "scripts": {"lint": "eslint .", "test": "mocha test/*.js", "coverage": "nyc mocha test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/jsdom/webidl-conversions.git"}, "keywords": ["webidl", "web", "types"], "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"eslint": "^6.8.0", "mocha": "^7.1.1", "nyc": "^15.0.0"}, "engines": {"node": ">=10.4"}, "gitHead": "8fe35a24803805c96d5b8a1085cd8ea797d00b62", "bugs": {"url": "https://github.com/jsdom/webidl-conversions/issues"}, "homepage": "https://github.com/jsdom/webidl-conversions#readme", "_id": "webidl-conversions@6.1.0", "_nodeVersion": "13.6.0", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-qBIvFLGiBpLjfwmYAaHPXsn+ho5xZnGvyGvsarywGNc8VyQJUMHJ8OBKGGrPER0okBeMDaan4mNBlgBROxuI8w==", "shasum": "9111b4d7ea80acd40f5270d666621afa78b69514", "tarball": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-6.1.0.tgz", "fileCount": 4, "unpackedSize": 25919, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJemdGmCRA9TVsSAnZWagAAVtQP/0/HzgPb/xh03IoVAmLu\nkS6nLo7xeU2m7z9V/z/Y3r5nAhump/5NwKnQwsHBNwJlqZVSjzBK9TCIlZ19\n7ZA0I3GCfbldEOC0i5f5Q5wb/NlM1SQ0X+ns6Suy1VA+5eg6apTPZdEMUfEs\nG/G7orVAg92uChLXnx+Wqpnxcep0b9jbzT9Bj92vzP4asMKmmMKytTvHihZc\nFimW6qmuvZHik/lA5xN0RFj7KnoZTLxxt3euSLOJ3WjkeV3b3Sm3SqSpJpj0\nJqlOAO1oYCwzkfLaBUAMDftbZuKfKypVmyCKV1eLomX3XKhf6lP/VVoeE1ao\nzMfgjUKVzfCGHZJh5NR879ELysQBycesXuV87T/DtKDpBlKag/u4+jaS2tnf\nWB9Ijcohtlfo2+FtRVqed79z2pFP59o5QaRGXp9uQOmHykS0GGNpdBnUP8TT\nWI3Onf6gOMiMvpDXa6oqQErTCj8u2ssNXiaWmqmPDcGvyJ8d8iL62krA+ijs\n0dbDLtd/HFrvtZFccH7JpN+hQwjTATmByxu1K4UcqVyeWql0h50ek0vENT45\nQvGsSdNbvcHVFkO2XxmCbMhuEkp5YPdGKSAKzkNudAaZpGWOo2+hhPkCnpSC\nJ3V5BWDMaQ2kZoySCPcVpl+08IuWaYm/a62lBzLddmJwh5my8gbxX7U2RKzN\niW4l\r\n=xLeb\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDCV8rYLJRh8sH5olqYL3gI6BXO0IbvDkOzjQw0kDl0YAIhAKbdyCW2oBo9TxWUY8+MJcMT7ZxareWjbZ2z2TfGohDo"}]}, "maintainers": [{"email": "<EMAIL>", "name": "domenic"}, {"email": "<EMAIL>", "name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel"}, {"email": "<EMAIL>", "name": "sebmaster"}, {"email": "<EMAIL>", "name": "timothy<PERSON>"}, {"email": "<EMAIL>", "name": "tmpvar"}, {"email": "<EMAIL>", "name": "zirro"}], "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/webidl-conversions_6.1.0_1587138982145_0.8082011982955031"}, "_hasShrinkwrap": false}, "7.0.0": {"name": "webidl-conversions", "version": "7.0.0", "description": "Implements the WebIDL algorithms for converting to and from JavaScript values", "main": "lib/index.js", "scripts": {"lint": "eslint .", "test": "mocha test/*.js", "test-no-sab": "mocha --parallel --jobs 2 --require test/helpers/delete-sab.js test/*.js", "coverage": "nyc mocha test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/jsdom/webidl-conversions.git"}, "keywords": ["webidl", "web", "types"], "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"@domenic/eslint-config": "^1.3.0", "eslint": "^7.32.0", "mocha": "^9.1.1", "nyc": "^15.1.0"}, "engines": {"node": ">=12"}, "gitHead": "7b8f708ffd7476fb60d18cef75bbdf4e60831191", "bugs": {"url": "https://github.com/jsdom/webidl-conversions/issues"}, "homepage": "https://github.com/jsdom/webidl-conversions#readme", "_id": "webidl-conversions@7.0.0", "_nodeVersion": "16.9.1", "_npmVersion": "7.21.1", "dist": {"integrity": "sha512-VwddBukDzu71offAQR975unBIGqfKZpM+8ZX6ySk8nYhVoo5CYaZyzt3YBvYtRtO+aoGlqxPg/B87NGVZ/fu6g==", "shasum": "256b4e1882be7debbf01d05f0aa2039778ea080a", "tarball": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-7.0.0.tgz", "fileCount": 4, "unpackedSize": 24242, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhPVHKCRA9TVsSAnZWagAAQf8P/3MK9mHGqmcyYC+udJ2M\nPCRfgJqenbBMzAh7MULPImllbS6Uqx8eovj77reae34qtFEXeCXtZWghniBi\nTbGqYF+ZMYBcdAj6tbtL+J6Y71G58BnD+/3albTjuUJ+eoEePpZxOS3iU9yg\nls45OrTiyoJx6hCyRtRK08O/2LhryYmKhNqrIWJ6M59UEcZERLlL7DP9+F1Z\nSY5orK40kgtxAtdnMmEW3HWWkCeznaSeRUf644fV5Im0ZVABlO5O2hTI4oqm\nEhMaTJL2KmBEAXONP1YQ/3xc+n8By2AJ3dZ7XXefb8ZHOPAL5wkoCG844Qxo\nJOlLkQVaZSiTOBZuQMKTHWaCtLXXmxpUEe/u69wruaBJGnPIvDt/4RVfFqq3\n12LrHOj2W6gLsK7+8B2rhRH1K45CSsjjll1F5UTulWJEFGvmiOdWNPYjnMVf\n9yjlhIkHsDcuMDJdKseD3yjZRHpfZCd7v7UADwjgBt11s3Pb5+SfWkidkY63\nwdHGh4rwvZMxu5NvmL4YmwhdDzU2REMOuqacf/QQ552wqBKKwECYeMpgz1gE\nxVSgUrf4B2F+5uZibM2vssHsfkSSQMJqB+kZ8K4PsLGPG/Z2hsByWAozDIzv\ndUbUp3CTg/WGwkbZxqGTsleeS8/WGceaoggedc3VdMY640W5laLyLvKx2WfB\nfbZx\r\n=Lefy\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHyXRqxzmxnnM8Ki06j4FTK42iKSO7EEoNpF5/wz1hJ7AiAXyuglgTQI9q+xpyB4sNm+Mp4en4OyMDXQMzcB4KsUgg=="}]}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/webidl-conversions_7.0.0_1631408586608_0.07909340053624736"}, "_hasShrinkwrap": false}}, "readme": "# Web IDL Type Conversions on JavaScript Values\n\nThis package implements, in JavaScript, the algorithms to convert a given JavaScript value according to a given [Web IDL](http://heycam.github.io/webidl/) [type](http://heycam.github.io/webidl/#idl-types).\n\nThe goal is that you should be able to write code like\n\n```js\n\"use strict\";\nconst conversions = require(\"webidl-conversions\");\n\nfunction doStuff(x, y) {\n    x = conversions[\"boolean\"](x);\n    y = conversions[\"unsigned long\"](y);\n    // actual algorithm code here\n}\n```\n\nand your function `doStuff` will behave the same as a Web IDL operation declared as\n\n```webidl\nundefined doStuff(boolean x, unsigned long y);\n```\n\n## API\n\nThis package's main module's default export is an object with a variety of methods, each corresponding to a different Web IDL type. Each method, when invoked on a JavaScript value, will give back the new JavaScript value that results after passing through the Web IDL conversion rules. (See below for more details on what that means.) Alternately, the method could throw an error, if the Web IDL algorithm is specified to do so: for example `conversions[\"float\"](NaN)` [will throw a `TypeError`](http://heycam.github.io/webidl/#es-float).\n\nEach method also accepts a second, optional, parameter for miscellaneous options. For conversion methods that throw errors, a string option `{ context }` may be provided to provide more information in the error message. (For example, `conversions[\"float\"](NaN, { context: \"Argument 1 of Interface's operation\" })` will throw an error with message `\"Argument 1 of Interface's operation is not a finite floating-point value.\"`)\n\nIf we are dealing with multiple JavaScript realms (such as those created using Node.js' [vm](https://nodejs.org/api/vm.html) module or the HTML `iframe` element), and exceptions from another realm need to be thrown, one can supply an object option `globals` containing the following properties:\n\n```js\n{\n  globals: {\n    Number,\n    String,\n    TypeError\n  }\n}\n```\n\nThose specific functions will be used when throwing exceptions.\n\nSpecific conversions may also accept other options, the details of which can be found below.\n\n## Conversions implemented\n\nConversions for all of the basic types from the Web IDL specification are implemented:\n\n- [`any`](https://heycam.github.io/webidl/#es-any)\n- [`undefined`](https://heycam.github.io/webidl/#es-undefined)\n- [`boolean`](https://heycam.github.io/webidl/#es-boolean)\n- [Integer types](https://heycam.github.io/webidl/#es-integer-types), which can additionally be provided the boolean options `{ clamp, enforceRange }` as a second parameter\n- [`float`](https://heycam.github.io/webidl/#es-float), [`unrestricted float`](https://heycam.github.io/webidl/#es-unrestricted-float)\n- [`double`](https://heycam.github.io/webidl/#es-double), [`unrestricted double`](https://heycam.github.io/webidl/#es-unrestricted-double)\n- [`DOMString`](https://heycam.github.io/webidl/#es-DOMString), which can additionally be provided the boolean option `{ treatNullAsEmptyString }` as a second parameter\n- [`ByteString`](https://heycam.github.io/webidl/#es-ByteString), [`USVString`](https://heycam.github.io/webidl/#es-USVString)\n- [`object`](https://heycam.github.io/webidl/#es-object)\n- [Buffer source types](https://heycam.github.io/webidl/#es-buffer-source-types), which can additionally be provided with the boolean option `{ allowShared }` as a second parameter\n\nAdditionally, for convenience, the following derived type definitions are implemented:\n\n- [`ArrayBufferView`](https://heycam.github.io/webidl/#ArrayBufferView), which can additionally be provided with the boolean option `{ allowShared }` as a second parameter\n- [`BufferSource`](https://heycam.github.io/webidl/#BufferSource)\n- [`DOMTimeStamp`](https://heycam.github.io/webidl/#DOMTimeStamp)\n\nDerived types, such as nullable types, promise types, sequences, records, etc. are not handled by this library. You may wish to investigate the [webidl2js](https://github.com/jsdom/webidl2js) project.\n\n### A note on the `long long` types\n\nThe `long long` and `unsigned long long` Web IDL types can hold values that cannot be stored in JavaScript numbers. Conversions are still accurate as we make use of BigInt in the conversion process, but in the case of `unsigned long long` we simply cannot represent some possible output values in JavaScript. For example, converting the JavaScript number `-1` to a Web IDL `unsigned long long` is supposed to produce the Web IDL value `18446744073709551615`. Since we are representing our Web IDL values in JavaScript, we can't represent `18446744073709551615`, so we instead the best we could do is `18446744073709551616` as the output.\n\nTo mitigate this, we could return the raw BigInt value from the conversion function, but right now it is not implemented. If your use case requires such precision, [file an issue](https://github.com/jsdom/webidl-conversions/issues/new).\n\nOn the other hand, `long long` conversion is always accurate, since the input value can never be more precise than the output value.\n\n### A note on `BufferSource` types\n\nAll of the `BufferSource` types will throw when the relevant `ArrayBuffer` has been detached. This technically is not part of the [specified conversion algorithm](https://heycam.github.io/webidl/#es-buffer-source-types), but instead part of the [getting a reference/getting a copy](https://heycam.github.io/webidl/#ref-for-dfn-get-buffer-source-reference%E2%91%A0) algorithms. We've consolidated them here for convenience and ease of implementation, but if there is a need to separate them in the future, please open an issue so we can investigate.\n\n## Background\n\nWhat's actually going on here, conceptually, is pretty weird. Let's try to explain.\n\nWeb IDL, as part of its madness-inducing design, has its own type system. When people write algorithms in web platform specs, they usually operate on Web IDL values, i.e. instances of Web IDL types. For example, if they were specifying the algorithm for our `doStuff` operation above, they would treat `x` as a Web IDL value of [Web IDL type `boolean`](http://heycam.github.io/webidl/#idl-boolean). Crucially, they would _not_ treat `x` as a JavaScript variable whose value is either the JavaScript `true` or `false`. They're instead working in a different type system altogether, with its own rules.\n\nSeparately from its type system, Web IDL defines a [\"binding\"](http://heycam.github.io/webidl/#ecmascript-binding) of the type system into JavaScript. This contains rules like: when you pass a JavaScript value to the JavaScript method that manifests a given Web IDL operation, how does that get converted into a Web IDL value? For example, a JavaScript `true` passed in the position of a Web IDL `boolean` argument becomes a Web IDL `true`. But, a JavaScript `true` passed in the position of a [Web IDL `unsigned long`](http://heycam.github.io/webidl/#idl-unsigned-long) becomes a Web IDL `1`. And so on.\n\nFinally, we have the actual implementation code. This is usually C++, although these days [some smart people are using Rust](https://github.com/servo/servo). The implementation, of course, has its own type system. So when they implement the Web IDL algorithms, they don't actually use Web IDL values, since those aren't \"real\" outside of specs. Instead, implementations apply the Web IDL binding rules in such a way as to convert incoming JavaScript values into C++ values. For example, if code in the browser called `doStuff(true, true)`, then the implementation code would eventually receive a C++ `bool` containing `true` and a C++ `uint32_t` containing `1`.\n\nThe upside of all this is that implementations can abstract all the conversion logic away, letting Web IDL handle it, and focus on implementing the relevant methods in C++ with values of the correct type already provided. That is payoff of Web IDL, in a nutshell.\n\nAnd getting to that payoff is the goal of _this_ project—but for JavaScript implementations, instead of C++ ones. That is, this library is designed to make it easier for JavaScript developers to write functions that behave like a given Web IDL operation. So conceptually, the conversion pipeline, which in its general form is JavaScript values ↦ Web IDL values ↦ implementation-language values, in this case becomes JavaScript values ↦ Web IDL values ↦ JavaScript values. And that intermediate step is where all the logic is performed: a JavaScript `true` becomes a Web IDL `1` in an unsigned long context, which then becomes a JavaScript `1`.\n\n## Don't use this\n\nSeriously, why would you ever use this? You really shouldn't. Web IDL is … strange, and you shouldn't be emulating its semantics. If you're looking for a generic argument-processing library, you should find one with better rules than those from Web IDL. In general, your JavaScript should not be trying to become more like Web IDL; if anything, we should fix Web IDL to make it more like JavaScript.\n\nThe _only_ people who should use this are those trying to create faithful implementations (or polyfills) of web platform interfaces defined in Web IDL. Its main consumer is the [jsdom](https://github.com/jsdom/jsdom) project.\n", "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "time": {"modified": "2022-06-28T23:27:47.825Z", "created": "2014-08-15T20:52:19.528Z", "1.0.0": "2014-08-15T20:52:19.528Z", "1.0.1": "2014-08-21T21:21:18.456Z", "1.1.0": "2014-10-13T22:12:31.432Z", "1.2.0": "2014-10-14T20:29:18.113Z", "1.2.1": "2014-12-16T22:35:25.554Z", "2.0.0": "2015-09-19T21:32:35.845Z", "2.0.1": "2015-11-09T02:11:51.581Z", "3.0.0": "2015-11-09T02:12:10.465Z", "3.0.1": "2016-01-04T06:08:05.108Z", "4.0.0": "2017-02-11T22:45:05.564Z", "4.0.1": "2017-02-20T21:19:40.959Z", "4.0.2": "2017-08-14T03:31:26.237Z", "5.0.0": "2019-12-07T20:23:01.802Z", "6.0.0": "2020-03-23T20:02:59.337Z", "6.1.0": "2020-04-17T15:56:22.269Z", "7.0.0": "2021-09-12T01:03:06.782Z"}, "keywords": ["webidl", "web", "types"], "repository": {"type": "git", "url": "git+https://github.com/jsdom/webidl-conversions.git"}, "author": {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/"}, "license": "BSD-2-<PERSON><PERSON>", "readmeFilename": "README.md", "homepage": "https://github.com/jsdom/webidl-conversions#readme", "bugs": {"url": "https://github.com/jsdom/webidl-conversions/issues"}}