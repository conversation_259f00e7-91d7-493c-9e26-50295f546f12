{"source": 1090165, "name": "safe-eval", "dependency": "safe-eval", "title": "safe-eval vulnerable to Prototype Pollution", "url": "https://github.com/advisories/GHSA-33vh-7x8q-mg35", "severity": "critical", "versions": ["0.0.0", "0.1.0", "0.2.0", "0.3.0", "0.4.0", "0.4.1"], "vulnerableVersions": ["0.0.0", "0.1.0", "0.2.0", "0.3.0", "0.4.0", "0.4.1"], "cwe": ["CWE-1321"], "cvss": {"score": 9.8, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H"}, "range": "<=0.4.1", "id": "0QZApv2wl3GBYiF2QAxMkdoOo6B1YMrJLyoGkrevkKSDotWN42yuNi7riqRbo/ha9GDrvuYfEHadsASTous7oA=="}