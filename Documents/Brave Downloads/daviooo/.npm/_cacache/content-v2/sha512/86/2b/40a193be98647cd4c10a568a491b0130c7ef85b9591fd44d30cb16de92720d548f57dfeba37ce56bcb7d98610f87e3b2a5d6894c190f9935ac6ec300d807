{"_id": "escape-latex", "_rev": "26-d6a3d7df53f983c43a60ea5bd9dfd8fc", "name": "escape-latex", "description": "Escape LaTeX special characters with Javascript", "dist-tags": {"latest": "1.2.0", "next": "1.0.0-beta.3"}, "versions": {"0.0.1": {"name": "escape-latex", "version": "0.0.1", "description": "Escape LaTeX special characters with Javascript", "main": "index.js", "scripts": {"test": "mocha -u tdd"}, "repository": {"type": "git", "url": "https://github.com/dangmai/escape-latex"}, "keywords": ["latex", "escape"], "devDependencies": {"mocha": "*", "chai": "*"}, "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "escape-latex@0.0.1", "dist": {"shasum": "ceaaa984f97d0053096fbee3d05703ded85128ac", "tarball": "https://registry.npmjs.org/escape-latex/-/escape-latex-0.0.1.tgz", "integrity": "sha512-r50rBLjh8sNjcjlD66CNNsSSQ59nwT/kgjCi36FvxvapFyQk8TtOwgLtmKeQGoQUL7vjKtNhZn8qvkTOzCF12A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCUt3EfzJhWatnRCWPLppCLYD/aN3BuSDnr1RgFzGax+wIgClHLFuTLodQSToHiqqvtCZOujp0Fq50kUnm0v0FxtSs="}]}, "_npmVersion": "1.1.63", "_npmUser": {"name": "dang<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "dang<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.1.0": {"name": "escape-latex", "version": "0.1.0", "description": "Escape LaTeX special characters with Javascript", "main": "index.js", "scripts": {"test": "mocha -u tdd"}, "repository": {"type": "git", "url": "https://github.com/dangmai/escape-latex"}, "keywords": ["latex", "escape"], "devDependencies": {"mocha": "*", "chai": "*"}, "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "escape-latex@0.1.0", "dist": {"shasum": "27906339bd4a096c65e0c52d08c0af0cf8cfd321", "tarball": "https://registry.npmjs.org/escape-latex/-/escape-latex-0.1.0.tgz", "integrity": "sha512-x4apvTkjifaEmRI9oqJpSvUeyt5GfdmPoiaU2wBqGBvCDucOB+jmha1QGw+YF4LtO2w4DFL1GnJi3IOcOhLPSw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEj2Jv5AIMHnAjRMnWOQuQ3JVj34VA5NaKvApJegF10LAiBPKpjDeP9kqBe36tGjRgvSkxXOYAdOWMFI/Ubkhj7v9Q=="}]}, "_npmVersion": "1.1.63", "_npmUser": {"name": "dang<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "dang<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.1.1": {"name": "escape-latex", "version": "0.1.1", "description": "Escape LaTeX special characters with Javascript", "main": "index.js", "scripts": {"test": "mocha -u tdd"}, "repository": {"type": "git", "url": "https://github.com/dangmai/escape-latex"}, "keywords": ["latex", "escape"], "devDependencies": {"mocha": "*", "chai": "*"}, "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "escape-latex@0.1.1", "dist": {"shasum": "825de398ecfa4c6be02ddfd4ddbcb63f0475a17f", "tarball": "https://registry.npmjs.org/escape-latex/-/escape-latex-0.1.1.tgz", "integrity": "sha512-+UxHaX3HpBhDuILanf1+yWWNMKARFE+fHJAXNxhjpl/Yuv9ufEtxh0zetPjVGMOQ0zyAZlY4Y6qAyvVx5KnZmw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDJc3SYQ+COGnYQc8JNtZDvo3rW5gV+Q2y4ZCv6QiyX3wIgSBmwLQ1yvLzw8tX4tSmnvocjIlpQsyIXrvyEIALDK88="}]}, "_npmVersion": "1.1.63", "_npmUser": {"name": "dang<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "dang<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.1.2": {"name": "escape-latex", "version": "0.1.2", "description": "Escape LaTeX special characters with Javascript", "main": "index.js", "scripts": {"test": "mocha -u tdd"}, "repository": {"type": "git", "url": "https://github.com/dangmai/escape-latex"}, "keywords": ["latex", "escape"], "devDependencies": {"mocha": "*", "chai": "*"}, "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "escape-latex@0.1.2", "dist": {"shasum": "c73febc52e9a987476de98abf92ae053124e1227", "tarball": "https://registry.npmjs.org/escape-latex/-/escape-latex-0.1.2.tgz", "integrity": "sha512-FH1Nwg53RpkAGvlpKGV7V4QaCEeGtkB829lKjNs0uwBh8Yw3JLMS/fZG/h5/T8WDCAy/ahi0ejlY0/58VF82aA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCWd1Qng2zoMEGbGRBuvD8OISgmqrWXjKaFarLBEGGCuQIhAOH8VBPsiecNAVk1FrlYhluxsm5BFQe53ZavgATCnyNH"}]}, "_npmVersion": "1.1.63", "_npmUser": {"name": "dang<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "dang<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.1.3": {"name": "escape-latex", "version": "0.1.3", "description": "Escape LaTeX special characters with Javascript", "main": "index.js", "scripts": {"test": "mocha -u tdd"}, "repository": {"type": "git", "url": "git+https://github.com/dangmai/escape-latex.git"}, "keywords": ["latex", "escape"], "devDependencies": {"mocha": "2.4.5", "chai": "3.5.0"}, "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "gitHead": "b08776dfd6b2e8eeafca88bf22679d22163d9815", "bugs": {"url": "https://github.com/dangmai/escape-latex/issues"}, "homepage": "https://github.com/dangmai/escape-latex#readme", "_id": "escape-latex@0.1.3", "_shasum": "97efd3e6421b89a37e19c686b0ca600f91650c1c", "_from": ".", "_npmVersion": "3.6.0", "_nodeVersion": "5.7.1", "_npmUser": {"name": "dang<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "97efd3e6421b89a37e19c686b0ca600f91650c1c", "tarball": "https://registry.npmjs.org/escape-latex/-/escape-latex-0.1.3.tgz", "integrity": "sha512-r2enrdSO0fnCaUbXS83T/upAdYSPmut9DC51zpgMsJIA0vkR9xYLfTLaOdo1HbPJ+qQ3ErqZ6P7sHOm8ZaFZ2A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDbCXQf40H/gVujgjsbDqaYwg/PvLuiubHfuRF5FcU8twIhAMWyqDyl8uw7NTSF1ushZ22zN4O1XmSZL0b3pvRc8pxf"}]}, "maintainers": [{"name": "dang<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/escape-latex-0.1.3.tgz_1457639747924_0.1608553749974817"}, "directories": {}}, "0.1.4": {"name": "escape-latex", "version": "0.1.4", "description": "Escape LaTeX special characters with Javascript", "main": "index.js", "scripts": {"test": "mocha -u tdd", "preversion": "npm test", "postversion": "git push && git push --tags"}, "repository": {"type": "git", "url": "git+https://github.com/dangmai/escape-latex.git"}, "keywords": ["latex", "escape"], "devDependencies": {"mocha": ">= 2.4.5", "chai": ">= 3.5.0"}, "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "gitHead": "8664d65bb82e3de34ad17eafa59d0a2baa130ed5", "bugs": {"url": "https://github.com/dangmai/escape-latex/issues"}, "homepage": "https://github.com/dangmai/escape-latex#readme", "_id": "escape-latex@0.1.4", "_shasum": "4836386afac8e0c001177ff61f1c27fa1e69117f", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "dang<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "4836386afac8e0c001177ff61f1c27fa1e69117f", "tarball": "https://registry.npmjs.org/escape-latex/-/escape-latex-0.1.4.tgz", "integrity": "sha512-Zi/xExQLIjUmx6ewxmZ5UJvAtIjPQNdqd6r8gjBqOQVYb7OUylb27jbdw8ZTArKbgVnP32ov4HT1oCOBekHm1Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDqu8b4AKqM6JKX7TjYWLmJrY9R2v+XKjwlUUKUAPEZqAIhAK1jgC+K81tOdJ8o+hh37P0tTFQLRMso9tVbE+Uo7ttC"}]}, "maintainers": [{"name": "dang<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/escape-latex-0.1.4.tgz_1487799725399_0.041212217416614294"}, "directories": {}}, "0.1.5": {"name": "escape-latex", "version": "0.1.5", "description": "Escape LaTeX special characters with Javascript", "main": "index.js", "scripts": {"test": "mocha -u tdd", "preversion": "npm test", "postversion": "git push && git push --tags"}, "repository": {"type": "git", "url": "git+https://github.com/dangmai/escape-latex.git"}, "keywords": ["latex", "escape"], "devDependencies": {"mocha": ">= 2.4.5", "chai": ">= 3.5.0"}, "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "gitHead": "2da149fabfaa236856ac77d6bf5a9694963da4ed", "bugs": {"url": "https://github.com/dangmai/escape-latex/issues"}, "homepage": "https://github.com/dangmai/escape-latex#readme", "_id": "escape-latex@0.1.5", "_shasum": "ee55db8b0e0d20dd4a659fe66030dd73af61fb04", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "dang<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "ee55db8b0e0d20dd4a659fe66030dd73af61fb04", "tarball": "https://registry.npmjs.org/escape-latex/-/escape-latex-0.1.5.tgz", "integrity": "sha512-033iXVZ+1NtOf0zNSnUQ297TTHGWS7s9x4l52xK7K/VkMA5/l8ByjsSmSYVMjJwT8cYDLVF6ZPhu5oGLIvm8zg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDKg/Lgj+qlkRbfUglXCWkgMU6vh5PIILaQvHv+T2YMSAiAJPbUi1oY5vJckZ1BBecVaY7RQzZPl2kM8Hb1ekqkMnQ=="}]}, "maintainers": [{"name": "dang<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/escape-latex-0.1.5.tgz_1487801122248_0.24068489600904286"}, "directories": {}}, "1.0.0-beta.1": {"name": "escape-latex", "version": "1.0.0-beta.1", "description": "Escape LaTeX special characters with Javascript", "main": "./dist/index.js", "files": ["dist"], "scripts": {"test": "mocha --require babel-core/register -u tdd ./src/**/*.test.js", "preversion": "npm test && npm build", "postversion": "git push && git push --tags", "precommit": "npm run lint && lint-staged", "prettier": "prettier --write ./src/**/*.js", "lint": "eslint ./src", "init": "mkdir dist", "clean": "rm -rf dist", "prebuild": "npm run clean && npm run init", "build": "babel ./src -d ./dist --ignore index.test.js"}, "lint-staged": {"*.{js,json,css,md}": ["npm run prettier", "git add"]}, "eslintConfig": {"parserOptions": {"ecmaVersion": 8}, "extends": ["eslint:recommended", "google", "prettier"], "env": {"node": "true"}}, "babel": {"presets": [["env", {"targets": {"node": "4.0"}}]]}, "prettier": {"trailingComma": "all"}, "repository": {"type": "git", "url": "git+https://github.com/dangmai/escape-latex.git"}, "keywords": ["latex", "escape"], "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-preset-env": "^1.6.1", "chai": "^4.1.2", "eslint": "^4.15.0", "eslint-config-google": "^0.9.1", "eslint-config-prettier": "^2.9.0", "husky": "^0.14.3", "lint-staged": "^6.0.0", "mocha": "^4.1.0", "prettier": "^1.9.2"}, "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "gitHead": "529206fb3c55521f2d7117cce772105d594e0d75", "bugs": {"url": "https://github.com/dangmai/escape-latex/issues"}, "homepage": "https://github.com/dangmai/escape-latex#readme", "_id": "escape-latex@1.0.0-beta.1", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.2", "_npmUser": {"name": "dang<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-LVe9t6z9AqLaPpPlBlG71onIV1Md8uuuAQsHb4isAwn2joslN6tdKtRryW3QLxeAYaOtC507jrbIKoeHAPR2ow==", "shasum": "b8f2233d6710f2ded27f59aaf2049704b4ed0733", "tarball": "https://registry.npmjs.org/escape-latex/-/escape-latex-1.0.0-beta.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCoQHBteb7jsG2fxcNZM5mFFOcKv9oDSm+UhbBwIwEfvgIhAKeQdmPvpI8LGic/eoiy/OU15LeX++2JLNLDhSiVD2Fk"}]}, "maintainers": [{"name": "dang<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/escape-latex-1.0.0-beta.1.tgz_1516147012851_0.9209698659833521"}, "directories": {}}, "1.0.0-beta.2": {"name": "escape-latex", "version": "1.0.0-beta.2", "description": "Escape LaTeX special characters with Javascript", "main": "./dist/index.js", "files": ["dist"], "scripts": {"test": "mocha --require babel-core/register -u tdd ./src/**/*.test.js", "preversion": "npm test && npm build", "postversion": "git push && git push --tags", "precommit": "npm run lint && lint-staged", "prettier": "prettier --write ./src/**/*.js", "lint": "eslint ./src", "init": "mkdir dist", "clean": "rm -rf dist", "prebuild": "npm run clean && npm run init", "build": "babel ./src -d ./dist --ignore index.test.js"}, "lint-staged": {"*.{js,json,css,md}": ["npm run prettier", "git add"]}, "eslintConfig": {"parserOptions": {"ecmaVersion": 8}, "extends": ["eslint:recommended", "google", "prettier"], "env": {"node": "true"}}, "babel": {"presets": [["env", {"targets": {"node": "4.0"}}]]}, "prettier": {"trailingComma": "all"}, "repository": {"type": "git", "url": "git+https://github.com/dangmai/escape-latex.git"}, "keywords": ["latex", "escape"], "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-preset-env": "^1.6.1", "chai": "^4.1.2", "eslint": "^4.15.0", "eslint-config-google": "^0.9.1", "eslint-config-prettier": "^2.9.0", "husky": "^0.14.3", "lint-staged": "^6.0.0", "mocha": "^5.0.0", "prettier": "^1.9.2"}, "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "gitHead": "acadfb243507a92cabfe7d7106f8827ae0353bfb", "bugs": {"url": "https://github.com/dangmai/escape-latex/issues"}, "homepage": "https://github.com/dangmai/escape-latex#readme", "_id": "escape-latex@1.0.0-beta.2", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.2", "_npmUser": {"name": "dang<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Z9B8KWNLQNIMdexcrsNhVjvFlz2+HurhE1pQQRnUP+KJZ6/n5MxkoW66DZwMcGMwr/mtm/rGtjH2Yy1c8+zIUA==", "shasum": "ed8e9a2cd7df95347a3cc2ecd2d900f34f168b73", "tarball": "https://registry.npmjs.org/escape-latex/-/escape-latex-1.0.0-beta.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDWmVHf7tQIXXLRuH8W+8Zh9Hq94Td83DDEa/Zu4uIz0wIga92TacX0nmW0hMP3mMNq9JVTV5qVD1khthSeRxLTi+8="}]}, "maintainers": [{"name": "dang<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/escape-latex-1.0.0-beta.2.tgz_1516746181312_0.41056756977923214"}, "directories": {}}, "1.0.0-beta.3": {"name": "escape-latex", "version": "1.0.0-beta.3", "description": "Escape LaTeX special characters with Javascript", "main": "./dist/index.js", "files": ["dist"], "scripts": {"test": "mocha --require babel-core/register -u tdd ./src/**/*.test.js", "preversion": "npm test && npm build", "postversion": "git push && git push --tags", "precommit": "npm run lint && lint-staged", "prettier": "prettier --write ./src/**/*.js", "lint": "eslint ./src", "init": "mkdir dist", "clean": "rm -rf dist", "prebuild": "npm run clean && npm run init", "build": "babel ./src -d ./dist --ignore index.test.js"}, "lint-staged": {"*.{js,json,css,md}": ["npm run prettier", "git add"]}, "eslintConfig": {"parserOptions": {"ecmaVersion": 8}, "extends": ["eslint:recommended", "google", "prettier"], "env": {"node": "true"}}, "babel": {"presets": [["env", {"targets": {"node": "4.0"}}]]}, "prettier": {"trailingComma": "all"}, "repository": {"type": "git", "url": "git+https://github.com/dangmai/escape-latex.git"}, "keywords": ["latex", "escape"], "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-preset-env": "^1.6.1", "chai": "^4.1.2", "eslint": "^4.15.0", "eslint-config-google": "^0.9.1", "eslint-config-prettier": "^2.9.0", "husky": "^0.14.3", "lint-staged": "^6.0.0", "mocha": "^5.0.0", "prettier": "^1.9.2"}, "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "gitHead": "6cf728601b1ec29b1ea3555a556d77b42521f386", "bugs": {"url": "https://github.com/dangmai/escape-latex/issues"}, "homepage": "https://github.com/dangmai/escape-latex#readme", "_id": "escape-latex@1.0.0-beta.3", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.2", "_npmUser": {"name": "dang<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-cTJHbrjZ1vgAdHGetDpmz6ha9H9/+IqHckWfpVg5rKvPpc538O7X4G/McDrhKHigqs7LEzGA7/Vg42gOKRZDrQ==", "shasum": "7eec5ffabc71d6670ad0cb3a1c8aaf402c8a0c2a", "tarball": "https://registry.npmjs.org/escape-latex/-/escape-latex-1.0.0-beta.3.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCNIyA1t1860/FLhI1c7SX85ZlhrWj6tDBbuKBnJre4ZQIgcaLAwdcItVisXhpAP1AgUXWA4PD7heJd4/NTVxS5LiY="}]}, "maintainers": [{"name": "dang<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/escape-latex-1.0.0-beta.3.tgz_1516813205119_0.868806695798412"}, "directories": {}}, "1.0.0": {"name": "escape-latex", "version": "1.0.0", "description": "Escape LaTeX special characters with Javascript", "main": "./dist/index.js", "files": ["dist"], "scripts": {"test": "mocha --require babel-core/register -u tdd ./src/**/*.test.js", "preversion": "npm test && npm build", "postversion": "git push && git push --tags", "precommit": "npm run lint && lint-staged", "prettier": "prettier --write ./src/**/*.js", "lint": "eslint ./src", "init": "mkdir dist", "clean": "rm -rf dist", "prebuild": "npm run clean && npm run init", "build": "babel ./src -d ./dist --ignore index.test.js"}, "lint-staged": {"*.{js,json,css,md}": ["npm run prettier", "git add"]}, "eslintConfig": {"parserOptions": {"ecmaVersion": 8}, "extends": ["eslint:recommended", "google", "prettier"], "env": {"node": "true"}}, "babel": {"presets": [["env", {"targets": {"node": "4.0"}}]]}, "prettier": {"trailingComma": "all"}, "repository": {"type": "git", "url": "git+https://github.com/dangmai/escape-latex.git"}, "keywords": ["latex", "escape"], "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-preset-env": "^1.6.1", "chai": "^4.1.2", "eslint": "^4.15.0", "eslint-config-google": "^0.9.1", "eslint-config-prettier": "^2.9.0", "husky": "^0.14.3", "lint-staged": "^6.0.0", "mocha": "^5.0.0", "prettier": "^1.9.2"}, "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "gitHead": "7eed97535da08a69b5d6b3ae6cbcf5cac18bf2f6", "bugs": {"url": "https://github.com/dangmai/escape-latex/issues"}, "homepage": "https://github.com/dangmai/escape-latex#readme", "_id": "escape-latex@1.0.0", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.2", "_npmUser": {"name": "dang<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-oogO9Cg3n/4nspF34CTfXFymgI79skca66DebIIQgxVy6qRVqczl/ji2YGAqhFCzpD/oAt/fCWF4qlhMAfda+g==", "shasum": "74b9e94d8c178645704c33791e95a4155b59718f", "tarball": "https://registry.npmjs.org/escape-latex/-/escape-latex-1.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDi34BuW2ISjcwP6GoK+LiwOVZL7pcA5LG59M3u1qmGCAiBh6fI5EHxB1Xgjbirl6E/TDVtwXNkFspmSWI80kVhhnA=="}]}, "maintainers": [{"name": "dang<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/escape-latex-1.0.0.tgz_1516913586723_0.754734574817121"}, "directories": {}}, "1.0.1": {"name": "escape-latex", "version": "1.0.1", "description": "Escape LaTeX special characters with Javascript", "main": "./dist/index.js", "files": ["dist"], "scripts": {"test": "mocha --require babel-core/register -u tdd ./src/**/*.test.js", "preversion": "npm test && npm build", "postversion": "git push && git push --tags", "precommit": "npm run lint && lint-staged", "prettier": "prettier --write ./src/**/*.js", "lint": "eslint ./src", "init": "mkdir dist", "clean": "rm -rf dist", "prebuild": "npm run clean && npm run init", "build": "babel ./src -d ./dist --ignore index.test.js"}, "lint-staged": {"*.{js,json,css,md}": ["npm run prettier", "git add"]}, "eslintConfig": {"parserOptions": {"ecmaVersion": 8}, "extends": ["eslint:recommended", "google", "prettier"], "env": {"node": "true"}}, "prettier": {"trailingComma": "all"}, "repository": {"type": "git", "url": "git+https://github.com/dangmai/escape-latex.git"}, "keywords": ["latex", "escape"], "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-preset-env": "^1.6.1", "chai": "^4.1.2", "eslint": "^4.15.0", "eslint-config-google": "^0.9.1", "eslint-config-prettier": "^2.9.0", "husky": "^0.14.3", "lint-staged": "^7.0.5", "mocha": "^5.0.0", "prettier": "^1.9.2"}, "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "gitHead": "2f37a9b39cff0bfee8456023243a5885c3a05149", "bugs": {"url": "https://github.com/dangmai/escape-latex/issues"}, "homepage": "https://github.com/dangmai/escape-latex#readme", "_id": "escape-latex@1.0.1", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.2", "_npmUser": {"name": "dang<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-bufASdPCqtyUA1p4KbeywqK0ehgoss2j9bCHIGSQMGRvBsj9hCI0v4Pc0vjQtwEKG4Fa2rlNEH7fGNnXqgjTKQ==", "shasum": "d9cc390a837a17c038d310458641326436197317", "tarball": "https://registry.npmjs.org/escape-latex/-/escape-latex-1.0.1.tgz", "fileCount": 3, "unpackedSize": 4686, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa63jlCRA9TVsSAnZWagAARwEP/1NKjgV8QXfbHaOnZL2W\nM4cHPfaePjpCtTF6lfJ/JUlldtUZ0xiKd0AR+t5eELW92eBAeKdgJwpLy7P8\nBHutVhZUayZ2VybdXFD5W1B9blhcsawjGATry96TwQUnpgd+ROd7UYZjoVbK\nSgf2ze+ALgQqrvRn/fh4GvLKvUQtu3XG61pX4xCtNOVu9DLS8ItupuxusY/o\nN0ANUYUJB9PybNDmJ5yxaWctayYDJ6RM9OUjMppkycbjzBsM0jIavHgqVHGI\n/fcATJG236zqxJM81c9IqEdRsuIBxl7KXI9BMEOgp5MrABk/FbOAYMfWTBe+\n2LsCX+hjty2ajLny7iVZsCz7KdBDfjIW+/pyo90/6cXTZ+2GVusyZTU6UMub\nsHAiFo4QNn9b01oG2Iedputc8QnFOnlxULaF9u+2cvbZUrU1Lz3TC14sTgrK\n8rIIjxRfiNO7oYojVI2jBfagMZwzUHqv5JBFEZ/Go50/30Rx+vtC3FbYkIJV\nrt8TdEZmgz5kreAT2SROvk9upqD0+rjAez00p/GZXyH3vdSdVN0uAJcvq4wl\nvCaCPIUEKOhdFfLafYhiMTj/rN70N6oPDtsEfCS4l5D0q67IIPf5bLwxl51R\nqiMV0knOLz6CKFFxzRDOk+im15Op5oF/n5URyKMY9Evo19AS7DKADpV/BDio\nlO2T\r\n=LIFO\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIApF2qaUx+Web4nBAiEp940Y/Ajw+g8tRs76fucHUnEaAiAQLI/n4TjI15Jk9YToNM308mLVFE/PF2ZuztfemyiYaA=="}]}, "maintainers": [{"name": "dang<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/escape-latex_1.0.1_1525381348791_0.9682818276566763"}, "_hasShrinkwrap": false}, "1.0.3": {"name": "escape-latex", "version": "1.0.3", "description": "Escape LaTeX special characters with Javascript", "main": "./dist/index.js", "files": ["dist"], "scripts": {"test": "mocha --require babel-core/register -u tdd ./src/**/*.test.js", "preversion": "npm test && npm run build", "postversion": "git push && git push --tags", "precommit": "npm run lint && lint-staged", "prettier": "prettier --write ./src/**/*.js", "lint": "eslint ./src", "init": "mkdir dist", "clean": "rm -rf dist", "prebuild": "npm run clean && npm run init", "build": "babel ./src -d ./dist --ignore index.test.js"}, "lint-staged": {"*.{js,json,css,md}": ["npm run prettier", "git add"]}, "eslintConfig": {"parserOptions": {"ecmaVersion": 8}, "extends": ["eslint:recommended", "google", "prettier"], "env": {"node": "true"}}, "prettier": {"trailingComma": "all"}, "repository": {"type": "git", "url": "git+https://github.com/dangmai/escape-latex.git"}, "keywords": ["latex", "escape"], "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-preset-env": "^1.6.1", "chai": "^4.1.2", "eslint": "^4.15.0", "eslint-config-google": "^0.9.1", "eslint-config-prettier": "^2.9.0", "husky": "^0.14.3", "lint-staged": "^7.0.5", "mocha": "^5.0.0", "prettier": "^1.9.2"}, "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "gitHead": "fc0ead6b1aa210cc5556d118ab50c7e4005c2425", "bugs": {"url": "https://github.com/dangmai/escape-latex/issues"}, "homepage": "https://github.com/dangmai/escape-latex#readme", "_id": "escape-latex@1.0.3", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.2", "_npmUser": {"name": "dang<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-GfKaG/7FOKdIdciylIzgaShBTPjdGQ5LJ2EcKLKXPLpcMO1MvCEVotkhydEShwCINRacZr2r3fk5A1PwZ4e5sA==", "shasum": "1b9085e3e66570faabe21f05655643f64e78717a", "tarball": "https://registry.npmjs.org/escape-latex/-/escape-latex-1.0.3.tgz", "fileCount": 4, "unpackedSize": 7126, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa64ASCRA9TVsSAnZWagAAYJYP+gMnBzws2P1PVGJEUzHR\nJMwIo3SQShoPmmV0ZPP0FNxLtvdr4wbRdCVplcPBDCIeZQKANRMjvajXUS8B\n2pPIFhSzUF0w5G//FdJ9xdSrbjphRRqZFS/ht6vyFGTqjs0dYKD2FhQnQEZw\nB8bqP/Ltyvxzw6sZ2XP4mx+OEPRkbLNtGo5nNkTwGZmKtAp0Bj6eweTLYZkU\n4lFzs2YgpqO9aa/0EBCXlYqLdo58ACcFOiQahOZxy0UWKVT+fZj0Ff4O0XPY\nTW7J/W+nbeXM9fhYh10xwSwiWxfHSHIG129AClMbWZNQ5F1Jj9YxQ/XVTYV8\nPSOKlTjskdIt+34EriNKo8xcgDdlgscDcBqKwoWaPfCgfqPqRp42CSYHRBl9\nT9WiQjOyB9SovIIymxhRUOQ7fBwUVUVdssBT2s4gJR/R1qmVpSdkDVEeWOpp\n3frMYjmxw4AMzmk/9l3bd1toQMpcLVKPZmj6VZmcqd9wqlLkC0Btu8mQ4zZc\nd+ZL10/XHvHKU49Ce5RnwLQwyY9Vs+19na88dcf8qa4MNWRzn2rCrKwmpYHx\npn+QT0Db8oJUDq3nzfLJG5O8HmMh0dL/l3/aI2V+pJRdGUCsjAZFEVRTmfVw\n0Ix/ypfsqW5nwkjsUeu8Nz4yB5lf9PTb/lrRGlMWfnaHXKiva1QJTIH3q6Nw\ndx5A\r\n=mPUu\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD/uyeQRJ4Vr/w4TM4GtVr9ngVo78Ni7fStSfVk3emangIgfSmljO/5/zJNkiXGVtPRHWgZSyUG1ShI25OtW233E9M="}]}, "maintainers": [{"name": "dang<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/escape-latex_1.0.3_1525383185444_0.9026072678012629"}, "_hasShrinkwrap": false}, "1.1.0": {"name": "escape-latex", "version": "1.1.0", "description": "Escape LaTeX special characters with Javascript", "main": "./dist/index.js", "files": ["dist"], "scripts": {"test": "mocha --require babel-core/register -u tdd ./src/**/*.test.js", "preversion": "npm test && npm run build", "postversion": "git push && git push --tags", "precommit": "npm run lint && lint-staged", "prettier": "prettier --write ./src/**/*.js", "lint": "eslint ./src", "init": "mkdir dist", "clean": "rm -rf dist", "prebuild": "npm run clean && npm run init", "build": "babel ./src -d ./dist --ignore index.test.js"}, "lint-staged": {"*.{js,json,css,md}": ["npm run prettier", "git add"]}, "eslintConfig": {"parserOptions": {"ecmaVersion": 8}, "extends": ["eslint:recommended", "google", "prettier"], "env": {"node": "true"}}, "prettier": {"trailingComma": "all"}, "repository": {"type": "git", "url": "git+https://github.com/dangmai/escape-latex.git"}, "keywords": ["latex", "escape"], "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-plugin-transform-object-assign": "^6.22.0", "babel-preset-env": "^1.6.1", "chai": "^4.1.2", "eslint": "^4.15.0", "eslint-config-google": "^0.9.1", "eslint-config-prettier": "^2.9.0", "husky": "^0.14.3", "lint-staged": "^7.0.5", "mocha": "^5.0.0", "prettier": "^1.9.2"}, "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "gitHead": "09c3c70f87708335aba29839704bd9bbf91f8373", "bugs": {"url": "https://github.com/dangmai/escape-latex/issues"}, "homepage": "https://github.com/dangmai/escape-latex#readme", "_id": "escape-latex@1.1.0", "_npmVersion": "5.6.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "dang<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-7k372jNDrL8uW7P/Sw8IkF+QcaeGoyjzrLx4pJj/CSIe02CvxL1wUJ+qMVVHsna/jNZ6PD6aCo7iEeRnXTzvdw==", "shasum": "c0a94a51eb8c73c3a67a95cc90fbb626cef54539", "tarball": "https://registry.npmjs.org/escape-latex/-/escape-latex-1.1.0.tgz", "fileCount": 4, "unpackedSize": 7473, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbFaX+CRA9TVsSAnZWagAAxIQQAJiJ/1NYJCVY5+dSQ/Cp\n131rkzOfN6E+YcAeCHiPYjT554N/YNIBDjv6sLqCL074JVcifQbmmgTaqFyP\n3KaskRC2nQAf+qft0ocnqJBvP5oJ9cCraetd6THBT+WjcpZAQehZ2iq7fugv\n6A3u8CkEJ+ko8djxWiz54xsX/62uINVitWPHJCTPGN++NDVnufSduOYS7PdY\nknZKzyB/JADrWNb4cNzTxeCyf9vasfOdWNuRFRJbrf3HuZZId/k7MxxojSy5\nfa3xer2T7Dla1nkrSpgoDmaNYpPSS3vO7LNZfo1QV805vKokN2kDD/i3q4+f\nfHwTKYHt6sT8ouCMy5FVdi0BbTi1UsgNZ7KsomrjWkpHPNIAS8Mp5uPGTroZ\nKvPPvTA7xL22r8Dx5gR6bA1KPOcglxLNjl7oTMczs7Jt2Niwholn9b55Pjm8\n/cMQr7jywhLguvDujJTJ1/W2/u5jVVsFr1f7wG/MvaFytpa65Dsm5zazi2cB\nFcaqBsx2TsdzkAFbLpOlzRxJAj5oMsDApNwi09HNLWCCdfkKrAMOpWk81gkn\nRP4DJRxSckNUfmLSYaUpNr9w0M6GJrnskjpwP/4jjO6IcQc+7kmylbQp4DnB\n648pGuOvrNJHdCBaa1snC+VUj5+urzXd+YsmtR8mhCH6379/rl/yyBUuL6DE\nyeDR\r\n=C+uz\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICbsiALHMDA36eW0sVBGfr3XdEql0cI/Wj7HNccZFK08AiEA3YlzUoKxjWvY6bE7qZxH7q3ZKi0nLnMfzdeu2fjbSY4="}]}, "maintainers": [{"name": "dang<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/escape-latex_1.1.0_1528145404458_0.7708611872994364"}, "_hasShrinkwrap": false}, "1.1.1": {"name": "escape-latex", "version": "1.1.1", "description": "Escape LaTeX special characters with Javascript", "main": "./dist/index.js", "files": ["dist"], "scripts": {"test": "mocha --require babel-core/register -u tdd ./src/**/*.test.js", "preversion": "npm test && npm run build", "postversion": "git push && git push --tags", "precommit": "npm run lint && lint-staged", "prettier": "prettier --write ./src/**/*.js", "lint": "eslint ./src", "init": "mkdir dist", "clean": "rm -rf dist", "prebuild": "npm run clean && npm run init", "build": "babel ./src -d ./dist --ignore index.test.js"}, "lint-staged": {"*.{js,json,css,md}": ["npm run prettier", "git add"]}, "eslintConfig": {"parserOptions": {"ecmaVersion": 8}, "extends": ["eslint:recommended", "google", "prettier"], "env": {"node": "true"}}, "prettier": {"trailingComma": "all"}, "repository": {"type": "git", "url": "git+https://github.com/dangmai/escape-latex.git"}, "keywords": ["latex", "escape"], "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-plugin-transform-object-assign": "^6.22.0", "babel-preset-env": "^1.6.1", "chai": "^4.1.2", "eslint": "^5.0.1", "eslint-config-google": "^0.9.1", "eslint-config-prettier": "^3.0.1", "husky": "^0.14.3", "lint-staged": "^7.0.5", "mocha": "^5.0.0", "prettier": "^1.9.2"}, "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "gitHead": "ce61ea0fdc080fada8d6c83cd20caa9df3abe92f", "bugs": {"url": "https://github.com/dangmai/escape-latex/issues"}, "homepage": "https://github.com/dangmai/escape-latex#readme", "_id": "escape-latex@1.1.1", "_npmVersion": "6.2.0", "_nodeVersion": "10.9.0", "_npmUser": {"name": "dang<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-N2D6Z2kXh8x/pQNQH+natXDCwrzghhXMRII5dZ518mlTLeuba80NL0LCQyaahqOrAidoLivmmG6GKPnGhHse+A==", "shasum": "657d8632af8849a5db8766778d4a43da9dec3376", "tarball": "https://registry.npmjs.org/escape-latex/-/escape-latex-1.1.1.tgz", "fileCount": 4, "unpackedSize": 7468, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbeydfCRA9TVsSAnZWagAAv9EP/3VXtIzyuVdHwQ0uLKK4\n9jaGvS7GMknquLtyR1ky/36yI/WPKm5kiI+zYsIvjCKvYUZTWTtquw/0WO6K\nPa/xd4WAZRVEcA2cePlCjhnEUhtFJgKQTAGUpVOApN+JO1wtUJ6LsTJI7MQx\nb/qGFrUq/PBIMKPbmGgVkRklisH36yjOS0EJmTnOPX33gdFE7fgV2EcwT3pt\naiNW2rtCiJE2Nc8zr4ZjnRuIH8XPT1oPPFiENLsJ63aPzLTeWPs2KQGHjiak\nZssFUEKPk6owVPGYbUr5Z0B7kMMhUQAOHhm0W8sz8seFDsz1R2bF2NSx1Igf\noYecVG6ETE7Sq7JhuIiz4yWDwW3FvdqKCQNGuInRmU/vn+Vw47arncF7fi+I\nTnyllocYv1f8ZKHzs0kWs2vOzia4e7EI4F9lDcehEPgqrtPSfHslSZjIzdZb\nkgGg7FcWD4gi9fDQrVjY6jZ5HQC2/R/1/NlWgYjU50ytmuoSzM24Mv4gDCA6\nhWX/n1zfQGFxKgF/sxmOGBSeYS865rrWCTIWn3JUDnIRZnxyVj2N5FwVJSYJ\nUXwkV8dprkm7FP8o0WQgem74cl3iFsWvkD2auDojPpt1pSl4iyv23pWhHQwy\nyWvynv1ANdnHX0jR4C6TvixIPwElAzfQvtnBobjAT8AnWc9VfjX7jxymfE4X\nYnVT\r\n=hA4T\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC76cw59U33pm+vqCjj6gCRZ4zIH8fS6DhhuGeb9yQOWAiBUyueMNZqRBtgB5L5jOoP46DuM2XONk37Ze6dlqbtaww=="}]}, "maintainers": [{"name": "dang<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/escape-latex_1.1.1_1534797663355_0.9020681057912046"}, "_hasShrinkwrap": false}, "1.2.0": {"name": "escape-latex", "version": "1.2.0", "description": "Escape LaTeX special characters with Javascript", "main": "./dist/index.js", "scripts": {"test": "mocha --require babel-core/register -u tdd ./src/**/*.test.js", "preversion": "npm test && npm run build", "postversion": "git push && git push --tags", "precommit": "npm run lint && lint-staged", "prettier": "prettier --write ./src/**/*.js", "lint": "eslint ./src", "init": "mkdir dist", "clean": "rm -rf dist", "prebuild": "npm run clean && npm run init", "build": "babel ./src -d ./dist --ignore index.test.js"}, "lint-staged": {"*.{js,json,css,md}": ["npm run prettier", "git add"]}, "eslintConfig": {"parserOptions": {"ecmaVersion": 8}, "extends": ["eslint:recommended", "google", "prettier"], "env": {"node": "true"}}, "prettier": {"trailingComma": "all"}, "repository": {"type": "git", "url": "git+https://github.com/dangmai/escape-latex.git"}, "keywords": ["latex", "escape"], "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-plugin-transform-object-assign": "^6.22.0", "babel-preset-env": "^1.6.1", "chai": "^4.1.2", "eslint": "^5.0.1", "eslint-config-google": "^0.9.1", "eslint-config-prettier": "^3.0.1", "husky": "^0.14.3", "lint-staged": "^7.0.5", "mocha": "^5.0.0", "prettier": "^1.9.2"}, "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "gitHead": "15cc5ef8572ba0fe7e0d96ec4339ceec549c8d6b", "bugs": {"url": "https://github.com/dangmai/escape-latex/issues"}, "homepage": "https://github.com/dangmai/escape-latex#readme", "_id": "escape-latex@1.2.0", "_npmVersion": "6.4.1", "_nodeVersion": "8.12.0", "_npmUser": {"name": "dang<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-nV5aVWW1K0wEiUIEdZ4erkGGH8mDxGyxSeqPzRNtWP7ataw+/olFObw7hujFWlVjNsaDFw5VZ5NzVSIqRgfTiw==", "shasum": "07c03818cf7dac250cce517f4fda1b001ef2bca1", "tarball": "https://registry.npmjs.org/escape-latex/-/escape-latex-1.2.0.tgz", "fileCount": 4, "unpackedSize": 7474, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb1LZ+CRA9TVsSAnZWagAAjMAP/jBPKHZ2+Uyl+l9aG3Xv\nreN3ZH5FZNa7FwQyICTvQ+OAKQUNE4nVt7A2/VrFGYi/K2ja2Ix1U+SgoDzt\naepZxIfyWbB7MCOmMr35Ldmomq/vtCjYQyhpMZDi3Iz/tE4ZRgoHVIwoRrQJ\nTCSoSOU4WvKeOho95vcgBKEbfRKGuYc3oTfTxLSheR9oaItPMTiuME0D+Ast\nnsp6YHm8R3XynYTrXwLyPcSPpUat/xOwz26PmUIeVjh2FkuG+I7vpwJiQa49\n+MKjF5jt6mJpBEWuJzmMaCHE3sW8NHNVkfeysj7qgD/cKqG/hWrG3QtL02tN\n8scPiMWZNVXO5JVU8mad4UUCdBLFqUE0MrXLzJSKJbdO38In1bascjymLKsW\nhJr7aNIhC5OmtjGbvVIIdZu9mqGoIDUC9tOAxyE0XvEPM35x9A4mF1+MdbhR\nHsfMiniVLujMZrRvvfuAlVxhSsaQIHbxXxoEwC1iBo8bGJUIxlq438Ao2fZL\nOe12gloFKGMNmy9horQyP9u2pMQrtjvhKE30ZRKM6DJIhhEqxCA8kIVeSdbP\n7k7ZHKBmc+p2/RCCigdqyEMPVKQo98SdqYKyk/e0Ml8Qxe8Mdl4GiVSK2ukU\nt2za4aBz6ySQaMC3SYIQdCDqhNR6/53UaUdG+sb5BKpskDfTiZVgO2xzi5rW\nbcbX\r\n=xTyh\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDgKbIHj4ttkL3RBIlzACIZWbLj5CFF5hjzFt6yv1CHAAiAfccB+KiRFsozEQJCy3WNnwTgVeIvEWhxd6t9wFxhihw=="}]}, "maintainers": [{"name": "dang<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/escape-latex_1.2.0_1540667005948_0.37336630788936165"}, "_hasShrinkwrap": false}}, "readme": "# escape-latex\n\n[![Greenkeeper badge](https://badges.greenkeeper.io/dangmai/escape-latex.svg)](https://greenkeeper.io/)\n\n[![Build Status](https://travis-ci.org/dangmai/escape-latex.png)](https://travis-ci.org/dangmai/escape-latex)\n\nEscape LaTeX special characters with Javascript in NodeJS (>= 4.x) environment.\n\n## Usage\n\n```javascript\nnpm install escape-latex\nvar lescape = require('escape-latex');\nlescape(\"String to be escaped here #yolo\");\n```\n\n## API\n\n```javascript\nlescape((input: String), {\n  preserveFormatting: Boolean,\n  escapeMapFn: Function,\n});\n```\n\nBy default,\n`escape-latex` only escapes characters that would result in malformed LaTeX.\nThese characters include `# $ % & \\ ^ _ { }`.\n\nThis means that the final LaTeX output might not look the same as your input Javascript string.\nFor example, multiple spaces are kept as-is, which may be truncated to 1 space by your LaTeX software.\n\nIf you want the final output string to be as similar to your input Javascript string as possible,\nyou can set the `preserveFormatting` param to `true`, like so:\n\n```javascript\nlescape(\"Hello   World\", { preserveFormatting: true });\n// Hello~~~World\n```\n\nWhich will be converted to three non-breaking spaces by your LaTeX software.\n\nThe list of format characters that are escaped include `space, \\t (tab), – (en-dash), — (em-dash)`.\n\nThere is also the param `escapeMapFn` to modify the mapping of escaped characters,\nso you can add/modify/remove your own escapes if necessary.\n\nIt accepts a callback function that takes in the default character escapes and the formatting escapes as parameters, and returns a complete escape mapping. Here's an example:\n\n```javascript\nlescape(\"Hello   World\", {\n  preseveFormatting: true,\n  escapeMapFn: function(defaultEscapes, formattingEscapes) {\n    formattingEscapes[\" \"] = \"\\\\\\\\\";\n    return Object.assign({}, defaultEscapes, formattingEscapes);\n  },\n});\n// Hello\\\\\\\\\\\\world\n```\n\n## Testing\n\n```\nnpm test\n```\n\n## Notes\n\n* If you are updating from `escape-latex < 1.0.0`,\n  the `en-dash` and `em-dash` are no longer escaped by default.\n  Please use `preserveFormatting` to turn them on if necessary.\n\n## License\n\nMIT\n", "maintainers": [{"name": "dang<PERSON><PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2022-06-17T17:21:35.262Z", "created": "2012-10-27T19:39:14.978Z", "0.0.1": "2012-10-27T19:39:16.153Z", "0.1.0": "2012-10-27T19:54:44.729Z", "0.1.1": "2012-10-28T00:05:11.448Z", "0.1.2": "2012-10-30T13:26:30.154Z", "0.1.3": "2016-03-10T19:55:51.693Z", "0.1.4": "2017-02-22T21:42:07.220Z", "0.1.5": "2017-02-22T22:05:22.840Z", "1.0.0-beta.0": "2018-01-16T23:48:58.702Z", "1.0.0-beta.1": "2018-01-16T23:56:54.138Z", "1.0.0-beta.2": "2018-01-23T22:23:02.431Z", "1.0.0-beta.3": "2018-01-24T17:00:05.264Z", "1.0.0": "2018-01-25T20:53:07.731Z", "1.0.1": "2018-05-03T21:02:28.854Z", "1.0.3": "2018-05-03T21:33:05.534Z", "1.1.0": "2018-06-04T20:50:04.856Z", "1.1.1": "2018-08-20T20:41:03.487Z", "1.2.0": "2018-10-27T19:03:26.058Z"}, "author": {"name": "<PERSON><PERSON>"}, "repository": {"type": "git", "url": "git+https://github.com/dangmai/escape-latex.git"}, "homepage": "https://github.com/dangmai/escape-latex#readme", "keywords": ["latex", "escape"], "bugs": {"url": "https://github.com/dangmai/escape-latex/issues"}, "license": "MIT", "readmeFilename": "README.md"}