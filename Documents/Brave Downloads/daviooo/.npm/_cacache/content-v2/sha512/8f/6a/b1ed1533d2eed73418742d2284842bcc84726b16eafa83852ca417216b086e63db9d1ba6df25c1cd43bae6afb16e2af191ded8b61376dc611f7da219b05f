{"_id": "regenerator-runtime", "_rev": "53-851dbafdedf26e9ec84ec3c3ee799910", "name": "regenerator-runtime", "time": {"modified": "2023-12-15T20:53:01.355Z", "created": "2014-07-04T06:36:41.889Z", "0.0.1": "2014-07-04T06:36:41.889Z", "0.9.0": "2016-05-02T02:32:32.344Z", "0.9.1": "2016-05-02T02:41:22.237Z", "0.9.2": "2016-05-02T03:07:26.802Z", "0.9.3": "2016-05-02T03:41:25.693Z", "0.9.4": "2016-05-02T03:50:43.910Z", "0.9.5": "2016-05-02T14:34:50.845Z", "0.9.6": "2016-11-04T14:40:04.326Z", "0.10.0": "2016-11-21T00:10:39.115Z", "0.10.1": "2016-12-08T15:12:48.955Z", "0.10.2": "2017-02-17T16:12:45.310Z", "0.10.3": "2017-02-17T21:52:25.245Z", "0.10.4": "2017-04-26T13:56:35.098Z", "0.10.5": "2017-04-28T14:47:13.861Z", "0.11.0": "2017-08-15T19:21:58.729Z", "0.11.1": "2017-12-07T19:36:07.829Z", "0.12.0": "2018-06-23T18:17:35.251Z", "0.12.1": "2018-08-03T19:39:42.041Z", "0.13.0": "2018-11-16T18:34:03.996Z", "0.13.1": "2018-11-16T18:50:12.200Z", "0.13.2": "2019-03-07T20:30:56.636Z", "0.13.3": "2019-07-19T15:24:21.582Z", "0.13.4": "2020-02-23T17:18:45.003Z", "0.13.5": "2020-03-12T22:32:21.035Z", "0.13.6": "2020-07-21T23:31:24.385Z", "0.13.7": "2020-07-22T22:12:51.380Z", "0.13.8": "2021-03-24T18:31:42.588Z", "0.13.9": "2021-07-22T22:21:44.111Z", "0.13.10": "2022-10-13T16:30:00.251Z", "0.13.11": "2022-11-14T18:15:22.830Z", "0.14.0": "2023-08-04T17:35:20.982Z", "0.14.1": "2023-12-15T20:52:02.938Z"}, "maintainers": [{"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist-tags": {"latest": "0.14.1", "next": "0.14.1"}, "description": "Runtime for Regenerator-compiled generator and async functions.", "readme": "# regenerator-runtime\n\nStandalone runtime for\n[Regenerator](https://github.com/facebook/regenerator)-compiled generator\nand `async` functions.\n\nTo import the runtime as a module (recommended), either of the following\nimport styles will work:\n```js\n// CommonJS\nconst regeneratorRuntime = require(\"regenerator-runtime\");\n\n// ECMAScript 2015\nimport regeneratorRuntime from \"regenerator-runtime\";\n```\n\nTo ensure that `regeneratorRuntime` is defined globally, either of the\nfollowing styles will work:\n```js\n// CommonJS\nrequire(\"regenerator-runtime/runtime\");\n\n// ECMAScript 2015\nimport \"regenerator-runtime/runtime.js\";\n```\n\nTo get the absolute file system path of `runtime.js`, evaluate the\nfollowing expression:\n```js\nrequire(\"regenerator-runtime/path\").path\n```\n", "versions": {"0.9.0": {"name": "regenerator-runtime", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Runtime for Regenerator-compiled generator and async functions.", "version": "0.9.0", "main": "runtime.js", "keywords": ["regenerator", "runtime", "generator", "async"], "repository": {"type": "git", "url": "https://github.com/facebook/regenerator/tree/master/packages/regenerator-runtime"}, "license": "MIT", "_id": "regenerator-runtime@0.9.0", "scripts": {}, "_shasum": "5ddf497001cdbfdeed0b23c8da345d5d7fe7e65b", "_from": ".", "_npmVersion": "3.3.9", "_nodeVersion": "6.0.0", "_npmUser": {"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "5ddf497001cdbfdeed0b23c8da345d5d7fe7e65b", "tarball": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.9.0.tgz", "integrity": "sha512-eWrec6qzggtfYp1CfY4lI2jNbIQk4mvQYORvxi3zzZF4EpPJL/X43YHem2vQVVuk77AlUq8VqyU6yv6mlXGYsg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDsLIGisLFJv7bYudlm3/7bv81aHMKrl1zM8pwh0eDiAAIgNNFuHhNA8UHHiDueiM9aI/aWV6cB5A1ijoaCrGhtoQw="}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/regenerator-runtime-0.9.0.tgz_1462156351288_0.37193154939450324"}, "directories": {}}, "0.9.1": {"name": "regenerator-runtime", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Runtime for Regenerator-compiled generator and async functions.", "version": "0.9.1", "main": "runtime-module.js", "keywords": ["regenerator", "runtime", "generator", "async"], "repository": {"type": "git", "url": "https://github.com/facebook/regenerator/tree/master/packages/regenerator-runtime"}, "license": "MIT", "_id": "regenerator-runtime@0.9.1", "scripts": {}, "_shasum": "e761fcb913158f52f23e114c5c9bd6a246dd295d", "_from": ".", "_npmVersion": "3.3.9", "_nodeVersion": "6.0.0", "_npmUser": {"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e761fcb913158f52f23e114c5c9bd6a246dd295d", "tarball": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.9.1.tgz", "integrity": "sha512-pUeH/RUy8FzdtiUQafNtOk5C1I7IUMfDuVq83GN4t1z2NHaLeRnPwGwevPy5LmCoUY+DuYv5pjx6ozK/hFZ3pQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFMYnxzn9reh6E2JNGaB4FfTyNhTYKf4AIr7QicJFM2XAiEAu/d6tUQB0ZFOfDS3H9U1nvvHhnyIrkjzMdCYFjBWHuE="}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/regenerator-runtime-0.9.1.tgz_1462156881175_0.18197224894538522"}, "directories": {}}, "0.9.2": {"name": "regenerator-runtime", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Runtime for Regenerator-compiled generator and async functions.", "version": "0.9.2", "main": "runtime-module.js", "keywords": ["regenerator", "runtime", "generator", "async"], "repository": {"type": "git", "url": "https://github.com/facebook/regenerator/tree/master/packages/regenerator-runtime"}, "license": "MIT", "_id": "regenerator-runtime@0.9.2", "scripts": {}, "_shasum": "66742ebb5656ad517fe7feef5cb236d839e1975c", "_from": ".", "_npmVersion": "3.3.9", "_nodeVersion": "6.0.0", "_npmUser": {"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "66742ebb5656ad517fe7feef5cb236d839e1975c", "tarball": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.9.2.tgz", "integrity": "sha512-mnsBWTJL9XMvh9Pit6uv5GRlXGfN4c/VltXe5xhQZtC6yzkVEOL0ii5rK+kmZYHbOxcIK1Jypiw379+0+wMXHA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCvjOo59vvZi43GYGT4znYo2rLtmbMSICszYE9rpomx8wIhAMQHXquAKuq+ZY9RsoT+rvHeOPiK6vrPgumh7o/8fyNO"}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/regenerator-runtime-0.9.2.tgz_1462158445810_0.24356031534262002"}, "directories": {}}, "0.9.3": {"name": "regenerator-runtime", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Runtime for Regenerator-compiled generator and async functions.", "version": "0.9.3", "main": "runtime-module.js", "keywords": ["regenerator", "runtime", "generator", "async"], "repository": {"type": "git", "url": "https://github.com/facebook/regenerator/tree/master/packages/regenerator-runtime"}, "license": "MIT", "_id": "regenerator-runtime@0.9.3", "scripts": {}, "_shasum": "348a25f0e42c2fa73fe4df97b30bc8a5690b6aab", "_from": ".", "_npmVersion": "3.3.9", "_nodeVersion": "6.0.0", "_npmUser": {"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "348a25f0e42c2fa73fe4df97b30bc8a5690b6aab", "tarball": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.9.3.tgz", "integrity": "sha512-WdcwiXIJQfFVSjQwki3mgxNC5DRwbnAhXCRxJiKYd/YMdTgfxa+3/3dJKfLUmuhtLMcr5mfGy11i7DYxwG4ThQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDAQZ7ZRguHKALDzVaWOQseLYYyw/Skonl3b6iM/Zv1qgIhAJ4ERRGehSHtXUw27r5H/J2frGNxppM3Qg5NcCMAY7e0"}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/regenerator-runtime-0.9.3.tgz_1462160483561_0.8289865111000836"}, "directories": {}}, "0.9.4": {"name": "regenerator-runtime", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Runtime for Regenerator-compiled generator and async functions.", "version": "0.9.4", "main": "runtime-module.js", "keywords": ["regenerator", "runtime", "generator", "async"], "repository": {"type": "git", "url": "https://github.com/facebook/regenerator/tree/master/packages/regenerator-runtime"}, "license": "MIT", "_id": "regenerator-runtime@0.9.4", "scripts": {}, "_shasum": "2c3dde92a86059bc946e1295047b648cdd37a1ad", "_from": ".", "_npmVersion": "3.3.9", "_nodeVersion": "6.0.0", "_npmUser": {"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "2c3dde92a86059bc946e1295047b648cdd37a1ad", "tarball": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.9.4.tgz", "integrity": "sha512-Ac7pVnTV8+qJj0WWcox6zQ87aOg83gtfT7dPh1/ld+QYo5vDTkQk2vgJShtt0PfWUobo6x1aZTg2IiNBnzq+LA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC5xuMaxs20KgOnfPHFdS/JFVKcNaHgyZ5tErTlDEsIawIhAMEGJ3223THgEbaqUyiomUBp7oYEuOsoOx5zYez00m6s"}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/regenerator-runtime-0.9.4.tgz_1462161041787_0.4779401086270809"}, "directories": {}}, "0.9.5": {"name": "regenerator-runtime", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Runtime for Regenerator-compiled generator and async functions.", "version": "0.9.5", "main": "runtime-module.js", "keywords": ["regenerator", "runtime", "generator", "async"], "repository": {"type": "git", "url": "https://github.com/facebook/regenerator/tree/master/packages/regenerator-runtime"}, "license": "MIT", "_id": "regenerator-runtime@0.9.5", "scripts": {}, "_shasum": "403d6d40a4bdff9c330dd9392dcbb2d9a8bba1fc", "_from": ".", "_npmVersion": "3.3.9", "_nodeVersion": "7.0.0-pre", "_npmUser": {"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "403d6d40a4bdff9c330dd9392dcbb2d9a8bba1fc", "tarball": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.9.5.tgz", "integrity": "sha512-yfa7FL3t5vHRZm8oR2GIhyCA70HKJdHP4zJ6/M5FCfzEvkP5qUfuy2+uH7P8N6rpRBHotcLOAtKVkXUv4qBR6w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBW+tv5mjhgGoLVY348ZtFssaoyevpFhd37s3VbBlxC5AiEA4z6Og0+DD5KnIIjjkOeJuegjTOAcvoV2WqQVph+wQd4="}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/regenerator-runtime-0.9.5.tgz_1462199689741_0.4502820009365678"}, "directories": {}}, "0.9.6": {"name": "regenerator-runtime", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Runtime for Regenerator-compiled generator and async functions.", "version": "0.9.6", "main": "runtime-module.js", "keywords": ["regenerator", "runtime", "generator", "async"], "repository": {"type": "git", "url": "https://github.com/facebook/regenerator/tree/master/packages/regenerator-runtime"}, "license": "MIT", "_id": "regenerator-runtime@0.9.6", "scripts": {}, "_shasum": "d33eb95d0d2001a4be39659707c51b0cb71ce029", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "4.6.1", "_npmUser": {"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "d33eb95d0d2001a4be39659707c51b0cb71ce029", "tarball": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.9.6.tgz", "integrity": "sha512-D0Y/JJ4VhusyMOd/o25a3jdUqN/bC85EFsaoL9Oqmy/O4efCh+xhp7yj2EEOsj974qvMkcW8AwUzJ1jB/MbxCw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICsaPq72QzuOqnJalC9nZgUUAZEd86D9SEh98j5Wsh5xAiBJPkMojbfEuv9QAKPDwl6eFoGpl2AxG4teyHL6h/nY8A=="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/regenerator-runtime-0.9.6.tgz_1478270402434_0.11256932467222214"}, "directories": {}}, "0.10.0": {"name": "regenerator-runtime", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Runtime for Regenerator-compiled generator and async functions.", "version": "0.10.0", "main": "runtime-module.js", "keywords": ["regenerator", "runtime", "generator", "async"], "repository": {"type": "git", "url": "https://github.com/facebook/regenerator/tree/master/packages/regenerator-runtime"}, "license": "MIT", "_id": "regenerator-runtime@0.10.0", "scripts": {}, "_shasum": "86208e34e24f14b1d5d6f093f003ef2d9af8b4a4", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "4.6.2", "_npmUser": {"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "86208e34e24f14b1d5d6f093f003ef2d9af8b4a4", "tarball": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.10.0.tgz", "integrity": "sha512-a+/IAY6RqjSc6+74gBoMe/P3iutzEXqIFO6JjhDrfwT4BwMR1WS95kr8Mjxpu3E5P+JXk/QhRD3y1xjPAIrYXQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCaRoIUEGBHdwJyM4+1d/E1kIZuUJLW1WXKDM7slNLipwIgS4OTfjIuI/2duK7mW86WxOhRBwi1iYLUBUJPQEzVLdQ="}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/regenerator-runtime-0.10.0.tgz_1479687038582_0.13848980842158198"}, "directories": {}}, "0.10.1": {"name": "regenerator-runtime", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Runtime for Regenerator-compiled generator and async functions.", "version": "0.10.1", "main": "runtime-module.js", "keywords": ["regenerator", "runtime", "generator", "async"], "repository": {"type": "git", "url": "https://github.com/facebook/regenerator/tree/master/packages/regenerator-runtime"}, "license": "MIT", "_id": "regenerator-runtime@0.10.1", "scripts": {}, "_shasum": "257f41961ce44558b18f7814af48c17559f9faeb", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "257f41961ce44558b18f7814af48c17559f9faeb", "tarball": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.10.1.tgz", "integrity": "sha512-yL4kTlBYB9AY7if6kXggxKHLChvKo13nfGGrzJYm7JiaXDAk49Y4iebEIHrI7KNDUtceZyB73GspyWqHw/o6Vg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCQjZERPhnRC/5LZNmujcEImsGtn1zrAhrG0XNPqXYm+AIgRhOhCIOlr7kzw9b4X2fM3uZiuMPRwXOgMTNh/rC4+CY="}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/regenerator-runtime-0.10.1.tgz_1481209968425_0.14842746453359723"}, "directories": {}}, "0.10.2": {"name": "regenerator-runtime", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Runtime for Regenerator-compiled generator and async functions.", "version": "0.10.2", "main": "runtime-module.js", "keywords": ["regenerator", "runtime", "generator", "async"], "repository": {"type": "git", "url": "https://github.com/facebook/regenerator/tree/master/packages/regenerator-runtime"}, "license": "MIT", "_id": "regenerator-runtime@0.10.2", "scripts": {}, "_shasum": "d8180f73493b2f216f6b7282ce1d943372c2c56f", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "4.7.3", "_npmUser": {"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "d8180f73493b2f216f6b7282ce1d943372c2c56f", "tarball": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.10.2.tgz", "integrity": "sha512-Wc64J1P/7AZUzQ0bBZhzIDPwVkd1bF26EpYg5HeojyDKuFFCsq6MIXs9wnYcX1GKnHpNjpmvhI/7JXeJLUWjLQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC/0Is4+EKi3bxVzpow1mW5eeZ5KYuRlTYdwtgwpq8+fQIgFlxJS9ZIPTKKsvbkoUoN4RF+Kml3yAj//xpXvgfAhPc="}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/regenerator-runtime-0.10.2.tgz_1487347963894_0.9866049448028207"}, "directories": {}}, "0.10.3": {"name": "regenerator-runtime", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Runtime for Regenerator-compiled generator and async functions.", "version": "0.10.3", "main": "runtime-module.js", "keywords": ["regenerator", "runtime", "generator", "async"], "repository": {"type": "git", "url": "https://github.com/facebook/regenerator/tree/master/packages/regenerator-runtime"}, "license": "MIT", "_id": "regenerator-runtime@0.10.3", "scripts": {}, "_shasum": "8c4367a904b51ea62a908ac310bf99ff90a82a3e", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "4.7.3", "_npmUser": {"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "8c4367a904b51ea62a908ac310bf99ff90a82a3e", "tarball": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.10.3.tgz", "integrity": "sha512-RusOd4jJBo15r6iD1HOzdzSdS7AB3a32p0dsbr8J/5lrDUk+3LljNBzmn3KS3fi71wN07IwQORf16xWKxWjtLg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID4s0Yl58BNi5LW2jeB8lBqNzVxVSEwyB4tvRNn4hpXeAiBvhBOCRAMhuWzv9g9YXhRnQQRGRfNFrmmYpz94IAeuaA=="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/regenerator-runtime-0.10.3.tgz_1487368343412_0.9572089267894626"}, "directories": {}}, "0.10.4": {"name": "regenerator-runtime", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Runtime for Regenerator-compiled generator and async functions.", "version": "0.10.4", "main": "runtime-module.js", "keywords": ["regenerator", "runtime", "generator", "async"], "repository": {"type": "git", "url": "https://github.com/facebook/regenerator/tree/master/packages/regenerator-runtime"}, "license": "MIT", "_id": "regenerator-runtime@0.10.4", "scripts": {}, "_shasum": "74cb6598d3ba2eb18694e968a40e2b3b4df9cf93", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.4", "_npmUser": {"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "74cb6598d3ba2eb18694e968a40e2b3b4df9cf93", "tarball": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.10.4.tgz", "integrity": "sha512-Nch5GfLDZFmaNWebC17UKy+KEfHa1QiqM55YlIpOvXjiVCqYYpgWl64Fe6AhBxoThfUZ/HUBTBZgO6bmf59t6Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICNX46levAlzW0xhi3wS8DebZa5qm5jDTgBwopYigt0nAiBJ4RKBsI5Jqz3xDFUDB5SOJ+Q4V+ShU6tVlnnU4PgTjw=="}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/regenerator-runtime-0.10.4.tgz_1493214993829_0.654222869547084"}, "directories": {}}, "0.10.5": {"name": "regenerator-runtime", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Runtime for Regenerator-compiled generator and async functions.", "version": "0.10.5", "main": "runtime-module.js", "keywords": ["regenerator", "runtime", "generator", "async"], "repository": {"type": "git", "url": "https://github.com/facebook/regenerator/tree/master/packages/regenerator-runtime"}, "license": "MIT", "_id": "regenerator-runtime@0.10.5", "scripts": {}, "_shasum": "336c3efc1220adcedda2c9fab67b5a7955a33658", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.4", "_npmUser": {"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "336c3efc1220adcedda2c9fab67b5a7955a33658", "tarball": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.10.5.tgz", "integrity": "sha512-02YopEIhAgiBHWeoTiA8aitHDt8z6w+rQqNuIftlM+ZtvSl/brTouaU7DW6GO/cHtvxJvS4Hwv2ibKdxIRi24w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGO3MIrTCp5FZE4r3wDsLTk1xtUt9IoZXpp85dJY+jbYAiARiJJb5+Py4Fnh+7z/VLExe+04H/J87n6lykCrs/amvA=="}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/regenerator-runtime-0.10.5.tgz_1493390833247_0.5373470620252192"}, "directories": {}}, "0.11.0": {"name": "regenerator-runtime", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Runtime for Regenerator-compiled generator and async functions.", "version": "0.11.0", "main": "runtime-module.js", "keywords": ["regenerator", "runtime", "generator", "async"], "repository": {"type": "git", "url": "https://github.com/facebook/regenerator/tree/master/packages/regenerator-runtime"}, "license": "MIT", "_id": "regenerator-runtime@0.11.0", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-/aA0kLeRb5N9K0d4fw7ooEbI+xDe+DKD499EQqygGqeS8N3xto15p09uY2xj7ixP81sNPXvRLnAQIqdVStgb1A==", "shasum": "7e54fe5b5ccd5d6624ea6255c3473be090b802e1", "tarball": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.11.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDtGatEdUd5YfMeyab21Sl7JnzyIUbMlhE0XLnCxgWxuAiBA/LmpwrIPo3P+y9EMx6z80Tg9fD6auodlufCkyafUVg=="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/regenerator-runtime-0.11.0.tgz_1502824917902_0.7827742118388414"}, "directories": {}}, "0.11.1": {"name": "regenerator-runtime", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Runtime for Regenerator-compiled generator and async functions.", "version": "0.11.1", "main": "runtime-module.js", "keywords": ["regenerator", "runtime", "generator", "async"], "repository": {"type": "git", "url": "https://github.com/facebook/regenerator/tree/master/packages/regenerator-runtime"}, "license": "MIT", "_id": "regenerator-runtime@0.11.1", "_npmVersion": "5.4.2", "_nodeVersion": "8.8.1", "_npmUser": {"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-MguG95oij0fC3QV3URf4V2SDYGJhJnJGqvIIgdECeODCT98wSWDAJ94SSuVpYQUoTcGUIL6L4yNB7j1DFFHSBg==", "shasum": "be05ad7f9bf7d22e056f9726cee5017fbf19e2e9", "tarball": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.11.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD6Mb5WwZQ2XdTHEzViILGmsfI3wgAFLIWM3lsFDKoTkgIgeLIrGCyRkRm+ZHJpcW0vwMBdb3iVLlsq84p7Y1NmVP0="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/regenerator-runtime-0.11.1.tgz_1512675366931_0.09084331477060914"}, "directories": {}}, "0.12.0": {"name": "regenerator-runtime", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Runtime for Regenerator-compiled generator and async functions.", "version": "0.12.0", "main": "runtime-module.js", "keywords": ["regenerator", "runtime", "generator", "async"], "repository": {"type": "git", "url": "https://github.com/facebook/regenerator/tree/master/packages/regenerator-runtime"}, "license": "MIT", "_id": "regenerator-runtime@0.12.0", "_npmVersion": "6.1.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-SpV2LhF5Dm9UYMEprB3WwsBnWwqTrmjrm2UZb42cl2G02WVGgx7Mg8aa9pdLEKp6hZ+/abcMc2NxKA8f02EG2w==", "shasum": "8052ac952d85b10f3425192cd0c53f45cf65c6cb", "tarball": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.12.0.tgz", "fileCount": 5, "unpackedSize": 26075, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbLo6/CRA9TVsSAnZWagAApQEP/0T5963PlkAR6KWaUWIi\n0Cb7AIIixeGvcEM5FYKeJSOHCCCesj9EQneTjrTDiIFLQvNW9anaafgxHWIQ\nECSXZ+nuwFq5VnHAsHPOUiXO34iSsdDxikdwk8rkQi8nZ06iG7xeSiC39ZWr\n3jVRX+fxVii9kVZnGGLPbXU7SzZoHeUp13GmMzriiPMIHdJZcoat+8Cp53+o\nJZOxdmRwcjP5i0IaQ/Q7No5OlA+C1oc9F8iRmkWTuvQV/ICYBEDR3KF5e41c\n57Q8PUpD75FJsdMujgDWSyTXi7AKDKz8bzssNWq4xyRX95zxX4EpgkHRHA9e\n36RdBtiP5RfpjKtqAJ7XWaSOmLZwm9kvUsq3AVkDaVw0Url9vDEi0Icf77K9\n3EYZkt6tQlkzhuOU2bmsBG7Z/8/3bbFsxbGTQgQ69y/io3O05rA0yZxgJAyS\nAgMDBI1d3YxGpAHJv541F0y5bMG3d+GCeuFdyLzAAlQh2J04TFcXiIR/jFpS\nEIkaqVHKsbvMGxWMdkjdxLeu+s4IF6Ro+1+88LKZ4b6bdETO2BSfc/y0oyOu\nLfyKHVchtkwmTOuBA0WQ6SpOzxZsMymXdyMvIkXdeoJc/sjMLXQ/BNNEwtME\nkZLPa+no9wxWqkNXZB0+WtHGnYUcdWSaUX5b9BKEDLfiCqsO+kMx7qe0ODpd\n2cyz\r\n=LCoT\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIExyDZIQywRobJt9nZ3sjr8/YE6maJg39s16+IZH8/3tAiBMpLn1VNTa20AwdCIwURI3m6LD8IHdaAPt3SSUM2BM8w=="}]}, "maintainers": [{"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/regenerator-runtime_0.12.0_1529777855144_0.8979190791806309"}, "_hasShrinkwrap": false}, "0.12.1": {"name": "regenerator-runtime", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Runtime for Regenerator-compiled generator and async functions.", "version": "0.12.1", "main": "runtime-module.js", "keywords": ["regenerator", "runtime", "generator", "async"], "repository": {"type": "git", "url": "https://github.com/facebook/regenerator/tree/master/packages/regenerator-runtime"}, "license": "MIT", "_id": "regenerator-runtime@0.12.1", "_npmVersion": "6.3.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-odxIc1/vDlo4iZcfXqRYFj0vpXFNoGdKMAUieAlFYO6m/nl5e9KR/beGf41z4a1FI+aQgtjhuaSlDxQ0hmkrHg==", "shasum": "fa1a71544764c036f8c49b13a08b2594c9f8a0de", "tarball": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.12.1.tgz", "fileCount": 5, "unpackedSize": 26161, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZK9+CRA9TVsSAnZWagAA2BwQAJPn+PlBggGwPG54+O3G\nCXSf8KAJpzd5dA0aU4xfmxWtydoKH2QnxZZUTRJSiwFs1n7U9337TvnddQ97\nmpPshueqdm3xs5A4obxVmSx63ah742sKOtuxDLPYgadcq/rEfg96Pkqxla9a\nnOXDgNHJUKaSFzWXW0clBF4pc07WkBTVKdDaxsnQGm5LuBvUI+z1j7byvIsh\nf+/k4EsZHFjFclDGZ96kc6yXVeNSVPIg33Oly+4MGEB9Jqu5S/Dm0njvNqVh\nM+TrNxw9K4rTvgI5gjTIbwFfY0YnKHGvMW7D50tsTpLgYEQFpGrwTko1QIcb\nwcqt/0rWstz2FOdfKo5eVkaf4unppOc8/HWnStYn8k1SoU3R4YbBpzYOeOkU\nI4V9ePZlp1LVHNQcSMln1MSGwwqYbLtU+uSbTSs+tDWSxhpYWBgVce32YFmg\npUDv/LqQpt/N3matymk7/naO5HntY3nNuUhvSc4Hx1D0sZlPnoV1DF2lBpLb\nU113a9ubemjq/hsmHHLgmc1lBrWOFiKpmAWmZcJrBCHNCFq2GLle3X6CyTcK\nI724eweeNt/+a1zzzekpBjdPIS4Vx3ZVds8co9rYi65C1vGgW8C430pJLeKk\nTeh1D96uLJJ/QIVEqsfYoyuFyx+kggqLNiWxpR+qP5Zwz9QL3gaXplrdcGbE\nNzCV\r\n=QgQc\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCIWWjwo/8/elKQrr8+ynK8WEtTL3qtZgM5lauh9hBxVQIgVEhE9Kv7VutSwRihd+gifK5BPa2r1c/m9Bx3BfplQFI="}]}, "maintainers": [{"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/regenerator-runtime_0.12.1_1533325181899_0.976485214602661"}, "_hasShrinkwrap": false}, "0.13.0": {"name": "regenerator-runtime", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Runtime for Regenerator-compiled generator and async functions.", "version": "0.13.0", "main": "runtime.js", "keywords": ["regenerator", "runtime", "generator", "async"], "repository": {"type": "git", "url": "https://github.com/facebook/regenerator/tree/master/packages/regenerator-runtime"}, "license": "MIT", "_id": "regenerator-runtime@0.13.0", "_npmVersion": "6.4.1", "_nodeVersion": "8.11.4", "_npmUser": {"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-M+ZF87GohPezdyULPGyY7GCefKlL67/2kZr5m1csoc8qJBH99drdnvzWN5TmvuKrvcdeqHiMUAjx/dx4nkZBAg==", "shasum": "16f4a0c32a1beeaad37cecf5b20e4edaf1100eb9", "tarball": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.0.tgz", "fileCount": 4, "unpackedSize": 24690, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb7w2cCRA9TVsSAnZWagAA+eMP/ApNJ23/grBouxVtUvRd\nXcRAqDhSbh0qCByftflr34n69o/nK76l+uuOtbg9HJrgT3Jl9LQEJMNjkQnD\ncMXU324iwh3/m4eFslBSgP4vHWRFUTKatULNqDDjxgZZeUSLj+fbuE5IEKhb\nnYO1PEEKIX8+tMtF5Cl4wOXynefohkmqyTyHZFqOKPIa23rIWy7JLjQkzhtq\nfK5ZLMF50+tV2S6FJEC4/+XLq9pLu99aUjyOKWT4VTrm2edWMnZANjeximHt\nIpgQhKKxFb5wnJ2E5VpmQbH0E5Pdge7WWSRCSo+FCQLxTW+EY4/Jv5PcT3iG\n7ckKUprIMWabe9k+a/Fiy6JTZPLUrhsyE/xTs/uudgSv8Ktbq10b7UN+173s\nczQ0Lmn3uzJCsyJ/AFJ4YSledGWi6RMpUDrdLCIW8179Nnomvjc8eT7EXm/i\n4aewKnZI/ADyHbBCeM36GsmwFboPilWuKN/TpqPVGB0mq5LwwjCc+wYhTY5+\n24ZtFp7hGQyaD2ffuMrGlmVuEA7B7dQ4osh4hJqqOxAikTcFuF1ffXXgB+7R\nZ/mbxMrseBt8I1UfZzrm6JTJDEVO98kSIO/qiDZSWyiWNLvt64uz3Zqx+Dsh\nxiQv9+jtnxTevOaMbl/RyjYEqgYpaPyU/nbM9jRp26M3Mlx32joqQCydD3PB\nL/R2\r\n=HCc9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCdRmSzLp+dljAmUS3HXSN3C1fzKH49+6m0WAH6GALIIgIhAKYNZDxud+aGlr/ajr4ov2U3aUuQMfa1UuYdlo3iTLJP"}]}, "maintainers": [{"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/regenerator-runtime_0.13.0_1542393243890_0.4568335596359805"}, "_hasShrinkwrap": false}, "0.13.1": {"name": "regenerator-runtime", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Runtime for Regenerator-compiled generator and async functions.", "version": "0.13.1", "main": "runtime.js", "keywords": ["regenerator", "runtime", "generator", "async"], "repository": {"type": "git", "url": "https://github.com/facebook/regenerator/tree/master/packages/regenerator-runtime"}, "license": "MIT", "_id": "regenerator-runtime@0.13.1", "_npmVersion": "6.4.1", "_nodeVersion": "8.11.4", "_npmUser": {"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-5KzMIyPLvfdPmvsdlYsHqITrDfK9k7bmvf97HvHSN4810i254ponbxCQ1NukpRWlu6en2MBWzAlhDExEKISwAA==", "shasum": "522ea2aafd9200a00eee143dc14219a35a0f3991", "tarball": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.1.tgz", "fileCount": 4, "unpackedSize": 24765, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb7xFkCRA9TVsSAnZWagAAxQoQAJ3h/F9HulEQGRVwHJ6K\nJcKX7nFIlRWsmToc5/EKgqMYx/8EeTmdksMj8N8if4QhbMIVYJI67CbTgeVz\ntqHx4eJc+qlD2gYzUYQi4865WJD8ddClHQutueG5diLXLDHdjJO1BiEMBUyb\niYM2+VsT+YtoxpVUmCx6NXOG23PkVSLiKa+C9DEqWQAGUZbDA7MTAsv2nM9/\n9Yxn7FkQOTWlgtB8/2dx2rx5nMK295zZvzxOTyit6Eg2ekzxJkvsfejC8ygJ\n308G0ZxoUl0WWfGnbUBYr/iKhdMo/orxyQe5QNKA5kHMQ7mk2vX/pDyxHBh9\n8jHDfx95kEWFk6yGJAGhxMh4ZJsdTQy7y4xhNChI+m3OetHppvs/WHzqAUz1\njfV3MejgSAxe1uUG7dWpxdlFw28kvXq1eI4roxszjJcbq0sX8kUfxGO9Z63T\nKWn5nbjixkmlYvjCdsvHsAQlCQLePZqsAtVfJ5ajoITZIsCdNEUdSGvKooET\nJu2rbBGkYTl16CyLPu+oLAxDPXnacjrSP4sP4x7Wyy8xD6nol/Pr6ztCP/2E\nSq+alsinLPPVGd2M3sjW7PbOiyiz22DEGmlc12TOZA5/XdmIh+vji6fACIwc\n+8SMj3ujqsVbJLTA1xCxVbrPmWupF4/H34s+HG4/JrGgY/tFz+bxDXETDEtn\n3OET\r\n=GrDQ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDSJKziUwcbqG9ZDkhwlm7kKHpADuwV8/VdHDmhZK+DZQIhAOv4sKxj+7PmmfHeAsEnZc3oJ9mv7+H9vvkEZ65lWYXG"}]}, "maintainers": [{"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/regenerator-runtime_0.13.1_1542394212059_0.7211625959030727"}, "_hasShrinkwrap": false}, "0.13.2": {"name": "regenerator-runtime", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Runtime for Regenerator-compiled generator and async functions.", "version": "0.13.2", "main": "runtime.js", "keywords": ["regenerator", "runtime", "generator", "async"], "sideEffects": true, "repository": {"type": "git", "url": "https://github.com/facebook/regenerator/tree/master/packages/regenerator-runtime"}, "license": "MIT", "readme": "# regenerator-runtime\n\nStandalone runtime for\n[Regenerator](https://github.com/facebook/regenerator)-compiled generator\nand `async` functions.\n\nTo import the runtime as a module (recommended), either of the following\nimport styles will work:\n```js\n// CommonJS\nconst regeneratorRuntime = require(\"regenerator-runtime\");\n\n// ECMAScript 2015\nimport regeneratorRuntime from \"regenerator-runtime\";\n```\n\nTo ensure that `regeneratorRuntime` is defined globally, either of the\nfollowing styles will work:\n```js\n// CommonJS\nrequire(\"regenerator-runtime/runtime\");\n\n// ECMAScript 2015\nimport \"regenerator-runtime/runtime\";\n```\n\nTo get the absolute file system path of `runtime.js`, evaluate the\nfollowing expression:\n```js\nrequire(\"regenerator-runtime/path\").path\n```\n", "readmeFilename": "README.md", "_id": "regenerator-runtime@0.13.2", "_nodeVersion": "8.15.1", "_npmVersion": "6.8.0", "_npmUser": {"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-S/TQAZJO+D3m9xeN1WTI8dLKBBiRgXBlTJvbWjCThHWZj9EvHK70Ff50/tYj2J/fvBY6JtFVwRuazHN2E7M9BA==", "shasum": "32e59c9a6fb9b1a4aff09b4930ca2d4477343447", "tarball": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.2.tgz", "fileCount": 4, "unpackedSize": 25563, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcgX+BCRA9TVsSAnZWagAAwyMP/iba3+H+k7IBro3rxnHK\nJ9LCjU5xUoIzuRHwRI91z2p2wMopQyy0H6f9Bu6G4O1aNwY9shkreIDEIwJL\nka3KGYlhwsD8dEkV34OZY/pG/SyhLIOS+FflOz9sPDC0CC562XOmUS30uSmp\nV6+sA3VFiQYVb6yKB4LbZLQSHpHfvTnqASXs4gNC/WNK1LnMctrrL85A14U5\niqnv20aEd3grpk67lRw3pQAvQsh8zC7K9572FwlBO9AuxYtpnC29zPD0RK1I\ndpr/irhSxT17+G5Jxh5Sn0uNzCyYkfvDY9/wiIYM65O3dqkbX/w0CMlveNua\no6M/5vFzHrz5MZxv3ARXlT2G64jZmZnWbBUYHB368iXdDQHJh6y5NM5u9coT\n/bb8Nexh0E+Rfu9niyDBVWGyDf6bxHn41b+1cSCc2sssznHF13dDmH5kICgq\nqyQVJ9VygdqV4Czi038np6wzFqolqVddOBxZR6hHkv3RLzN52DBofSEo7/bI\noW8OylY2BstrtF60FfwdFj2IgPueXQ8ZRItHa853IZr7wESbQNj52Q9LLT5D\nr5vuE6LhABaJ4n3qK26vqRvcX9e3wdUmhQg/ntra2eoURmR6MCYz9Xiv5ncR\n8SqAzIn8dPAzAUM7J/v9qGIU+4LgWQP3krxn7OoTehigFwQdxVXQndtWSpDh\nbV+B\r\n=oFv7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCeOAHlCFNtVxaDMlowXjSg1S6L5QW8mpo7wigUjJdifgIgWdmM8rWNxRMx0TofmFRI74RjpEDu4pRI99f4O0PvoDs="}]}, "maintainers": [{"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/regenerator-runtime_0.13.2_1551990656482_0.7714252213361896"}, "_hasShrinkwrap": false}, "0.13.3": {"name": "regenerator-runtime", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Runtime for Regenerator-compiled generator and async functions.", "version": "0.13.3", "main": "runtime.js", "keywords": ["regenerator", "runtime", "generator", "async"], "sideEffects": true, "repository": {"type": "git", "url": "https://github.com/facebook/regenerator/tree/master/packages/regenerator-runtime"}, "license": "MIT", "_id": "regenerator-runtime@0.13.3", "_nodeVersion": "8.16.0", "_npmVersion": "6.10.0", "_npmUser": {"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-naKIZz2GQ8JWh///G7L3X6LaQUAMp2lvb1rvwwsURe/VXwD6VMfr+/1NuNw3ag8v2kY1aQ/go5SNn79O9JU7yw==", "shasum": "7cf6a77d8f5c6f60eb73c5fc1955b2ceb01e6bf5", "tarball": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.3.tgz", "fileCount": 5, "unpackedSize": 26643, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdMeCmCRA9TVsSAnZWagAASkwP/12RRKJkMppsL7WXJsPi\nSfpWsn7nuKHsc3ZuN/N3XOPYDNMRF2bvxZcpimlH6l/1nGwj5y6U3QK3yCOP\nclMp7obI8VYdDO3yqe+0arRcU3gnyPuiMpU2786S9DUcjQhMpuQh1gzROkO7\nR1x8mzpKYU/XWsB0KMLstI21RXpOMOJNNW+PZCmNxXOXnepHKIKj1zGl7RoS\n11gngHFO61KGNdo7l8b1eA6J+8IUZiunc4puvhw58svWzKmGTT4D12bN+H55\n6PgtlcsrTguprO++9jMeQt5vQPN+AmKqa9iTuWnRSclVzynjPt6MFQEUHHQO\nmDf/giBd8yWgyrSn6gtYcempi7Ws019ihxPiNIL0QQ+6DnUsDgH/DwzZzjPZ\nfDrJfxIzKORJhZAXYTgHpav+KuEsI+iWrQayhABJbnV9KggWuPdgF1lUtKP9\n7bs/WuvACs9MOKh/Hpm4aoQyfaOmJqiRKJ3aH0vkJz3ZNd4tUB9cjIeQi0mD\nrJMI37MDYjyoIjUPcj29JlB2e0DNcDq9aVz8fua4CTAxi8rAzfzdR9JWa1aa\n9PXDDjSemJIP1f6lLpW8O9E7Pi5QhyT/ea2oF3nlMQOkpw6T9folxCvwabvF\nET1jUAWY8nuiAXrkievTFuDFcWJFWVyY0h+XVzZ9UxPgcYeGGgMzykF4dEnb\nVjre\r\n=4dLw\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCb5nHUiK4yyVKT9EQTh74Ejo1WvQ0kuHeLBsVZzp/ijwIhAKS7hRAgRxFuWUPtlEETeniqwavUk/E57cvkEIxyZ05/"}]}, "maintainers": [{"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/regenerator-runtime_0.13.3_1563549861300_0.9905042524082779"}, "_hasShrinkwrap": false}, "0.13.4": {"name": "regenerator-runtime", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Runtime for Regenerator-compiled generator and async functions.", "version": "0.13.4", "main": "runtime.js", "keywords": ["regenerator", "runtime", "generator", "async"], "sideEffects": true, "repository": {"type": "git", "url": "https://github.com/facebook/regenerator/tree/master/packages/regenerator-runtime"}, "license": "MIT", "readme": "# regenerator-runtime\n\nStandalone runtime for\n[Regenerator](https://github.com/facebook/regenerator)-compiled generator\nand `async` functions.\n\nTo import the runtime as a module (recommended), either of the following\nimport styles will work:\n```js\n// CommonJS\nconst regeneratorRuntime = require(\"regenerator-runtime\");\n\n// ECMAScript 2015\nimport regeneratorRuntime from \"regenerator-runtime\";\n```\n\nTo ensure that `regeneratorRuntime` is defined globally, either of the\nfollowing styles will work:\n```js\n// CommonJS\nrequire(\"regenerator-runtime/runtime\");\n\n// ECMAScript 2015\nimport \"regenerator-runtime/runtime\";\n```\n\nTo get the absolute file system path of `runtime.js`, evaluate the\nfollowing expression:\n```js\nrequire(\"regenerator-runtime/path\").path\n```\n", "readmeFilename": "README.md", "_id": "regenerator-runtime@0.13.4", "_nodeVersion": "12.16.1", "_npmVersion": "6.13.6", "_npmUser": {"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-plpwicqEzfEyTQohIKktWigcLzmNStMGwbOUbykx51/29Z3JOGYldaaNGK7ngNXV+UcoqvIMmloZ48Sr74sd+g==", "shasum": "e96bf612a3362d12bb69f7e8f74ffeab25c7ac91", "tarball": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.4.tgz", "fileCount": 5, "unpackedSize": 26756, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeUrP1CRA9TVsSAnZWagAAhugP/i61w9BA4HFesU9Hn5aM\nP+RAKlJUw5dYMyxQCJj+SFuB+dAdFI6CTbmneQNHTKyk9DG4Fb6aNT9RM/ST\n3TK1LRWwKPcB/SqCa+A65FJaNs1FoLBB9jenbTGcp8QcYpncarrqEsfq0yvi\nkKTwGwykc+W0ntobsn+m5/hPjt1JCjwmU1/L7Gd78/FDAuGjA9LHbBKCVv5X\nKG9lNsb8KN+xgFkWTqXF4URXfNQ0zWA5Xy2FY+SBcFEkPkemXLYPMHWBnzyw\nkcqxvBFj7+iGpqCFzaX2nGtGPGNLyKPCmVCzmFf5vWBzVU+BGhOeCpdGW+4z\n9qfzgimqfralGm8gyckycsj1FJcyP6mhwDIXJUbk3PDovtGqs1j3U7TtP/60\nONczQLLeUmoYL6aT2D8H7SmI9dEKNsZR6+bQxYnjKu9saR29n1hshbH7ErTv\npBS+lE5vOIR7sus3H/ZuhRW++u+gw0ZCTDfahGGrYtZRiOD66DrQ4I72iIkp\nxhPRyITRuOA0uR00QoGoltkp7xYo0JB5HyZq6fb3Wmpbwx+a4KE/MhImoJh5\nr2119YuDWjB2nJ7/CAppMCG4eCa156HJrIvTIoZ2wBh8pGFuUiRvEj370H1r\n4s6nOkCqmPawjAbwneQmO0k9VAQdnweJnoT4JCZ5997Ja7shwgdIlIbLd7mr\ngb9C\r\n=4hZs\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDptBdy7r8iu7Kuudan8HshJPAxhC79wouIqY1jQaNnoQIhAL1Dy0wDHUNrf3T3OzN8dXtRwXOuXEDjsbZCfOh9Q828"}]}, "maintainers": [{"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/regenerator-runtime_0.13.4_1582478324888_0.09946330951840676"}, "_hasShrinkwrap": false}, "0.13.5": {"name": "regenerator-runtime", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Runtime for Regenerator-compiled generator and async functions.", "version": "0.13.5", "main": "runtime.js", "keywords": ["regenerator", "runtime", "generator", "async"], "sideEffects": true, "repository": {"type": "git", "url": "https://github.com/facebook/regenerator/tree/master/packages/regenerator-runtime"}, "license": "MIT", "_id": "regenerator-runtime@0.13.5", "_nodeVersion": "12.16.1", "_npmVersion": "6.14.0", "_npmUser": {"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-ZS5w8CpKFinUzOwW3c83oPeVXoNsrLsaCoLtJvAClH135j/R77RuymhiSErhm2lKcwSCIpmvIWSbDkIfAqKQlA==", "shasum": "d878a1d094b4306d10b9096484b33ebd55e26697", "tarball": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.5.tgz", "fileCount": 5, "unpackedSize": 26759, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJearh1CRA9TVsSAnZWagAAWHoP/2B2G5Ugc3eUFfxIJln/\n/PHLm7YBbSqwsEJzUYZWmECCapZRdLJ0cNgVK6BJ53cX5d7N4ouSCIp5muEA\nFSXccFlzYOI9/L8gaG2QFeRIeqDrDiWVaKQkj857JkXQUtDNn5oARHTrnVDp\nizA7KqAhcVGhv9Z3C66dCmDqgbJHxGtaoHimVcE/BK3r9ldBqu4AAm+gnLks\n19WwQw24OVrBh0KtjQwYCwM4JQ3PuMp0OSzBelCl/19GgNzLtCc0S50kqJHa\njr1n/KNBZH5v8L1DZF2B1yEBlVkbZ2Lx6oHcG+7N4y6yzLLSNtRcOYofXq6U\nLi0JMv3IxykI8IKQHYg74xcMyxOh62EQzDVeHV3MbhJdiMQpO/rB4tSkiZk2\nCUQgAGCGwLbQ9tYGVbQHweR0AOp8t/6wbufPdE7SxEnflj/02E5PHilHo7mK\nr787MpZDS27eCaAMJ/nlof+PgSmPLLDzgYvjENBIeW21dAy+q+/7aZciFJAU\nBZ7DnyrIn/3AV22x4gZa4Gn7pu9R9j+fYkJcB7JcQ9IG4abywQy1S+mXPWF5\ndEHWLJjvERumYApYetb/E/7gAPWAR5Vor1DAzObEEEQNDF2ICMCfOhBWdutJ\nmxQby8qqOMGIui1dQmwd8uF/h/A0Z45I2gstU4XOqrabQIRFaS2Ukd2/vPdn\nWOb/\r\n=MCiP\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDXoxetbh+1wG7EKIQ0VHj9+Amz3vctMK4qemCoCLUOoAIhAORtA53+qR2ipw4XM4omuBmdSskrJCz+gKVcbjDT+a6B"}]}, "maintainers": [{"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/regenerator-runtime_0.13.5_1584052340867_0.2509480314182633"}, "_hasShrinkwrap": false}, "0.13.6": {"name": "regenerator-runtime", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Runtime for Regenerator-compiled generator and async functions.", "version": "0.13.6", "main": "runtime.js", "keywords": ["regenerator", "runtime", "generator", "async"], "sideEffects": true, "repository": {"type": "git", "url": "https://github.com/facebook/regenerator/tree/master/packages/regenerator-runtime"}, "license": "MIT", "_id": "regenerator-runtime@0.13.6", "_nodeVersion": "12.14.1", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-GmwlGiazQEbOwQWDdbbaP10i15pGtScYWLbMZuu+RKRz0cZ+g8IUONazBnaZqe7j1670IV1HgE4/8iy7CQPf4Q==", "shasum": "d236043c46ffab2968c1ef651803d8acdea8ed65", "tarball": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.6.tgz", "fileCount": 5, "unpackedSize": 26982, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfF3rMCRA9TVsSAnZWagAAtaQQAKOySkuPN7BlgU+SQV2p\nKmZutO4W5POcxW9oDQjJnx1+VRTIgeTEVEYUr7nqMhHEf1Z+T83+aeuro8dm\n5E6br38YcbNEZu7O+RhaoOgUN1fuAexGR/l60lXKaVJa2ntoIvoIaEdocT0h\nnzSc5d2x2xgcXQ8FWuoHEHrdcnGuoyg8QpYCsW3BT+PByBYODYFgMo+7MgXh\ne7xBsHbc4doGfDKiZwQnFsNSPEisuJMeFWO34ATDJDpQFfogNfTDxzDlYRHX\nDkth0z75UEU8Ri/X3CRGVI9MFNuc01j36VXIskLJitSgFfx73wpaFAKGikdo\nfGpMiERnwPdM6ii+/TW98EA4htL+x1uOlT9gxnFg6UaxhLlz/CRJyVmF+VEg\nSLkfBhnCaXTmDJY8LEWgixLa3kHvEOjWhJCkjxHa05iS+MwTgucuF78/yvYZ\nr2A2cwX+ZmaPKtMvT8yHnwZ39mxwO2mjyUz4d/wUpj047k9g6jL2Tv2EaODO\nPWho+wesrNC3zIgBKT45eR7YUF0NF/g1VNBNWJz6nF2HI6u7PM0OPgPQ61qA\nJZTQ3keKXFAK/TSjG8Iuey7NwXY+IGMscwVFVIsCGSxjy4eVty1wGSetxwrd\nsRFZnPOfOg71qwTCgZJGJbmjH5qJ2I8Kf9vmgW4wudreq3jiZyMk6I0+9zu3\nO+uf\r\n=ZWu7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG/3TEhzjJmP6ggzjrfRCiPyOH3X6vzFE3XBGHqfSLidAiAJv6cbXLNIUP5xW78QC3wQJ4hs7hskJ/4qT2M2IaoWLw=="}]}, "maintainers": [{"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/regenerator-runtime_0.13.6_1595374284282_0.9828912860213683"}, "_hasShrinkwrap": false}, "0.13.7": {"name": "regenerator-runtime", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Runtime for Regenerator-compiled generator and async functions.", "version": "0.13.7", "main": "runtime.js", "keywords": ["regenerator", "runtime", "generator", "async"], "sideEffects": true, "repository": {"type": "git", "url": "https://github.com/facebook/regenerator/tree/master/packages/regenerator-runtime"}, "license": "MIT", "_id": "regenerator-runtime@0.13.7", "_nodeVersion": "12.14.1", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-a54FxoJDIr27pgf7IgeQGxmqUNYrcV338lf/6gH456HZ/PhX+5BcwHXG9ajESmwe6WRO0tAzRUrRmNONWgkrew==", "shasum": "cac2dacc8a1ea675feaabaeb8ae833898ae46f55", "tarball": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.7.tgz", "fileCount": 5, "unpackedSize": 27153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfGLnjCRA9TVsSAnZWagAAoaAP/3KfzMAdgDq8/Q2rg6IB\n4BSPFMTIkSypvPZ7PSoG4JE3eDpTaM2eVY3/B60UNCb0eLeugXyVxGkF39G5\n02Iv+kP1knXMcquetdxvTGXWhaB4A1d/dZLBHmrB0pdwo+dtl2w9237zyfyo\nH0jKNTjUW/IecvrjR/RDd2CmmPEpQUkSStkGdWj/Bjf9gmyww6lPDnNbAkCB\nOyXceLy/T5JrVtyKX/RbQWvj0osN0gB9GgJFFEEWj/BuHBLgS1+zpvX5AuSO\nXQGQ96vmjBpLWpASRIDEQKjSBKs4tFbKy4aP8VQTITvlIJHVf6dwZ+y2fEML\ntxhkk/4vMutWDXnBEKJVz9X6ISBR2nFCkrxVB5EKdSxXp/e58WV2+ou9Dq5h\ngb41g6ee1aRjfhSMM8SWjEfxHy+r2BDWLEl1mVGT8ag8r1LS1v/m1jY83NPy\nLN2eMpudjQrcc2f7X01NWDBv1K4MQfkNLeB5L4O8sVemxXLh/lEo0kDVOxO2\nl0t2tI3CXGM3a+2+sOo6jH8JwBTu2wG5DMlqnHXBH681MwfF+XMmGhTLgvkw\nLAVqCB3TalWkWXOktKGnqSkjMNoJ7UbGNfb4NEl3NPPc86eZxWh95QCyp1ep\nEH4vjqr/eKZOSMf8Wmoiohu/CdA7dGB6kCmzhF+lEu60r4mzbhkQMs6eiYni\nSRFc\r\n=JPnQ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCxqEz72+dq8sXpTECr1RHDgW3pONSlpoHNf7AUOOaAvAIgCP34tyLaBRYyKmw3pwAKogAg+ahlnNmH81KQ8nSMQP0="}]}, "maintainers": [{"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/regenerator-runtime_0.13.7_1595455971250_0.14560406492092715"}, "_hasShrinkwrap": false}, "0.13.8": {"name": "regenerator-runtime", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Runtime for Regenerator-compiled generator and async functions.", "version": "0.13.8", "main": "runtime.js", "keywords": ["regenerator", "runtime", "generator", "async"], "sideEffects": true, "repository": {"type": "git", "url": "https://github.com/facebook/regenerator/tree/master/packages/regenerator-runtime"}, "license": "MIT", "readme": "# regenerator-runtime\n\nStandalone runtime for\n[Regenerator](https://github.com/facebook/regenerator)-compiled generator\nand `async` functions.\n\nTo import the runtime as a module (recommended), either of the following\nimport styles will work:\n```js\n// CommonJS\nconst regeneratorRuntime = require(\"regenerator-runtime\");\n\n// ECMAScript 2015\nimport regeneratorRuntime from \"regenerator-runtime\";\n```\n\nTo ensure that `regeneratorRuntime` is defined globally, either of the\nfollowing styles will work:\n```js\n// CommonJS\nrequire(\"regenerator-runtime/runtime\");\n\n// ECMAScript 2015\nimport \"regenerator-runtime/runtime.js\";\n```\n\nTo get the absolute file system path of `runtime.js`, evaluate the\nfollowing expression:\n```js\nrequire(\"regenerator-runtime/path\").path\n```\n", "readmeFilename": "README.md", "_id": "regenerator-runtime@0.13.8", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.11", "dist": {"integrity": "sha512-o/ASGwgZ6UiVjspr4YnzHKF1NbBdX+mCPkSeymofk/d7I+csCYn3ZgZMMVtXeecpT8DBiI2nAlYkHd+xNCqu4A==", "shasum": "72dcec846a36a2672d69501c35db75a91bce4875", "tarball": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.8.tgz", "fileCount": 5, "unpackedSize": 27234, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW4WPCRA9TVsSAnZWagAARnQP/iWp5ZRvX3AK4SGqfSTR\nrM40XppiWuBKdeT9AQ15o7jQ6bgjzFx+cWJtRg+7hEQaeGiKIiwcdDe1R/XF\n8AO+Pv1sVN8pGzFSafG8AW939eVK57xR7umti3I02/bXz3b+1JObYuIbb3Hc\nC3IxjZaLyksNx5Zbwo1a3ZW2Iokggpe8mXKg9Ca7cf4NI1iVxH5ZArbUbG7p\nN1k1A2nrgICkOSWi0iz85i1UhAF8UNoFvMv9VKVm+0IGFiE8ayFFDMGGJshs\n7yvpHwt1PEM8cBfhXbJRTakuMhH4vvn8DW1jOFlXxc9PcRIgwvAkaRQIP6Bs\nrzDpBHkJl1h+ov50RSt4GimMNXqjc5n9Jx/Cs+xjdZ7mHzJ3IkvCd8dBdgno\n4+K1409lLo0UBv+6a9wMIWpqApEYt/gVHGjQtPfg/OC+obpc5m6o+IjSQUfU\nXLd7g/7i1Gb6x/8bVUvH2FExRDJgbi7oYw6B+C4EM3NnpkJeCcfiIH01QYXd\nCTGTThE/xrbjtiu8nUUPcwd3NzHQ4YQUU/TAPIJqa7K/5tNo2B1xvr+dy7v1\nbbRggJjESlmSxuospaMuiH7qkfqDtgWsxRGsRCkg0TtXDBkK3OA90Y/w+89A\ntduJ+MC/QDoR5tPzlLnF9UBBEygXa6O/SyDF222ZY6Xmem9dPq7ZaoX20rf9\n/qUe\r\n=nZ4O\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC7h5aJ801MXw95DIQgg0V9zgZ6TgWjj6iMzTE+n0H+swIhAK0AUcF3hGD2J559wckBUlO6PTQrCNKsfF0/aQQ6CM5M"}]}, "_npmUser": {"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/regenerator-runtime_0.13.8_1616610702367_0.08849270346387761"}, "_hasShrinkwrap": false}, "0.13.9": {"name": "regenerator-runtime", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Runtime for Regenerator-compiled generator and async functions.", "version": "0.13.9", "main": "runtime.js", "keywords": ["regenerator", "runtime", "generator", "async"], "sideEffects": true, "repository": {"type": "git", "url": "https://github.com/facebook/regenerator/tree/master/packages/runtime"}, "license": "MIT", "_id": "regenerator-runtime@0.13.9", "_nodeVersion": "14.17.3", "_npmVersion": "6.14.13", "dist": {"integrity": "sha512-p3VT+cOEgxFsRRA9X4lkI1E+k2/CtnKtU4gcxyaCUreilL/vqI6CdZ3wxVUx3UOUg+gnUOQQcRI7BmSI656MYA==", "shasum": "8925742a98ffd90814988d7566ad30ca3b263b52", "tarball": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.9.tgz", "fileCount": 5, "unpackedSize": 27401, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg+e94CRA9TVsSAnZWagAA720P/Al7knam21RB3+efNOtJ\nS1x1unfxhvMfTCgYy3acSllxXypSrgHwcWkpSo8TR0xTXNb93LXknYPxwKum\nitEQQ3GLQamg1UdQRNrgQR8v1BFsjEF41DW9yu4USukEUmDEIx65CNTAmUub\nEdXor2crc9U90I84wQsDvAWi1hJhAyU4kekOfY/pYkk+x5RmYejP6oyXfaVK\nfMUIDjcfQARAESawCTTBqzm32JNP45YAjQHfNDjDqJuf+SzKBjnxpCnjMJLu\nMyXjTgVR84esM2rFZUJwB0fH6j5H9+7IDaMgDib1gj3rAWtdYBCe+L4Clr6Q\nIXgLgr+gWXGDD5gxTqzMUNMIclqvYqLWTv8zvviI9LaYfRVQZebBKEx4rw+I\nt5mLAQA0XJ0WclBfA9rRYMR0iNPfgHgUr5Uw1dEps5p2+cveJvO8VzlTwvpj\nyExBoD0owkXxVvmA+uXLqSEsTYQqnGunVkqyZ1F46EUcFADgra/Zur/yXBl9\nJmChXVh3P2IUOjyvt7lMrZkw4Y4HjphU6+SAPI8b847rqoCSbkRPWRMocXXC\n94rLLsDxCSNQN1kmu99jNmjfNNz2cWhub76jEpr98Jbqy0eIg2ol9hywb07k\new5wT3BAbVKeU5jsPDa0xeERLriIXcE/QkW4rhIPYzyguH87/i4R6S5cmvxp\nGPve\r\n=gx8W\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHAI+rIefi8pQXUVOsWjgwOKv+eQiEgO9pYUpHsO6OOkAiEAr7bkxIOoIyHrj3/kechRwGwk4hvoGstLcazE6wIB5o4="}]}, "_npmUser": {"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/regenerator-runtime_0.13.9_1626992503893_0.20377358589553873"}, "_hasShrinkwrap": false}, "0.13.10": {"name": "regenerator-runtime", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Runtime for Regenerator-compiled generator and async functions.", "version": "0.13.10", "main": "runtime.js", "keywords": ["regenerator", "runtime", "generator", "async"], "sideEffects": true, "repository": {"type": "git", "url": "git+https://github.com/facebook/regenerator.git#master"}, "license": "MIT", "gitHead": "53cf890f79bcd132c51ed1700b4368594f755a3b", "bugs": {"url": "https://github.com/facebook/regenerator/issues"}, "homepage": "https://github.com/facebook/regenerator/tree/master#readme", "_id": "regenerator-runtime@0.13.10", "_nodeVersion": "16.16.0", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-KepLsg4dU12hryUO7bp/axHAKvwGOCV0sGloQtpagJ12ai+ojVDqkeGSiRX1zlq+kjIMZ1t7gpze+26QqtdGqw==", "shasum": "ed07b19616bcbec5da6274ebc75ae95634bfc2ee", "tarball": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.10.tgz", "fileCount": 5, "unpackedSize": 27683, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDMILCiUeXFR6EddK4WbgNU0sSALtrbc297lo3tyeTilAIhAK4AlJ3+3ihxnsnEvuLXIuGnwYEXH/dc5VOZ3A0ynbhB"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSD0IACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrGzhAAjKj/fYXelYOeV3d6/Aw2va0pngpT6TM9KRO1l4AetUieELdk\r\nla7HPNBLzecje1cNnv5BE1Flcp+nnnpJYnppWVIqbsMXqQY3Ijk8Xvd+5JRq\r\nCO+ga1HkFZJOzYD1pK592dY9nSrjqKUShb0r8Y7YAtwU45zZ2f6sqxkKoKa8\r\njD33KoR2mqPYjldjE6KTF+ZdJcBEKn9NdjevKhhaET9j86y/wJJ4u454TUSe\r\nd0hH+j9tUSpQrHc7r49QsIXam4XQcbEGJxuZCYFOJAioc8i7es0UKy7iMc25\r\nHLMx/p5Hu2Wk81gdu2ZCl54xwHLERum3+NXFjAqsEd1SkWgfrbpOiIJ5E8RS\r\niV3BVYjm2lkjUPJs7OfRdZoc3bHfsC2MWY6g8/zW2V/2DCvoqPZkrWFhS5R8\r\n9wEbIqoJkbj3EkVn0wzN2rjnlyrf/uyUj1LqRx3TROgVusonyqN0JbGUNF/V\r\nKbHaE30OZcSAPV89vGIE9qDQsbqtYsJBupD3XElen+jwaGfkjGi1XtSZNEQQ\r\nFTkHyWiSqJGLiC5+m3samvHwS5lYkkVQ7fxbSawEauGKq3tvmxFiYKxne6TB\r\nRyt1zFHDSihXin0zqGhPxaM51ZJMoi6rS7/razUSl9Nv1UKZs1ryVqNoFlI0\r\nFYfTA8MRSEVrG1RTBCvTepyzd1mQONyKNwA=\r\n=geE4\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/regenerator-runtime_0.13.10_1665678600082_0.8327173359082725"}, "_hasShrinkwrap": false}, "0.13.11": {"name": "regenerator-runtime", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Runtime for Regenerator-compiled generator and async functions.", "version": "0.13.11", "main": "runtime.js", "keywords": ["regenerator", "runtime", "generator", "async"], "sideEffects": true, "repository": {"type": "git", "url": "git+https://github.com/facebook/regenerator.git#main"}, "license": "MIT", "gitHead": "e4b592a44ef0d3a366cc7ad6125c4d9c8f6cc597", "bugs": {"url": "https://github.com/facebook/regenerator/issues"}, "homepage": "https://github.com/facebook/regenerator/tree/main#readme", "_id": "regenerator-runtime@0.13.11", "_nodeVersion": "16.18.0", "_npmVersion": "8.19.3", "dist": {"integrity": "sha512-kY1AZVr2Ra+t+piVaJ4gxaFaReZVH40AKNo7UCX6W+dEwBo/2oZJzqfuN1qLq1oL45o56cPaTXELwrTh8Fpggg==", "shasum": "f6dca3e7ceec20590d07ada785636a90cdca17f9", "tarball": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.11.tgz", "fileCount": 5, "unpackedSize": 27759, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID2+dvkiD4C4+aOTfXfJ2Kzsxq3osSsh3cRuGGCusBDRAiAPsG8bA6P3U8flY5oOaq5bV/vM3nylsrFtNiRA6m63ZA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjcoW6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoBXQ//c5gV1W15zfaI24REh7wpDihDP7HB3uZJa1Aj+g4EI2w4c7d4\r\nHkXfq1Sh2jaGTbTie3CVNw3t4pQIk7pWndZsuMMt6e3TAS0r9jXkib4nUU4I\r\nojpJ97k+2H3VBk8TBhJNS4w2qnYs12V1lYYwH4Lrh2oSzf/E5U/zqFuAoDN7\r\nrL+ujVmXyWxAzdiJmBADdZ+XXMLyo++8+gS4of6Hyi8pFSt9zAknSEaEJGoS\r\nkyRyQtLjXApZuIBLucJ0u0CPtHOY7yr6dkaHFGt5TgAUeTxhXPSef7GHUxkU\r\nQMx0ctd5Wmm7+/6xq9BkbcmhbMAbeMl21/+1qOLW5X9QW7dfv7ZzmPx26YxX\r\nSRYgP1UIE35RYv3Cz/C7uHo2lQ3GJLB2rsqNe2BBZUwy74n2/Z8EA/gfAys6\r\neLm8HyNT9/xpQVVSI3uY5qDUl8L6YlxBS57bfb8lh+8ab08nYK/30rCBklG+\r\nDcHMploDiciZCPuDgRArugwRFCr3nbe7MVDkpgVpdwLYTfxhbkaWdYq5soPx\r\nCpzzzS2x3ZjVR6cda7W6hJ0MIVapntfiserw4ZF9ryrs9RfZ2guEyaAVQDU0\r\ngBIzjemh6KIKLDorzAwFgxfccjETIpcZGiuZaZDiUU0gbYJ6gPwIgLknYvC8\r\n/zNGSb2k4V5F2ItoGnf5N8Xy16CMsHje59s=\r\n=EROf\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/regenerator-runtime_0.13.11_1668449722556_0.3341559789725068"}, "_hasShrinkwrap": false}, "0.14.0": {"name": "regenerator-runtime", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Runtime for Regenerator-compiled generator and async functions.", "version": "0.14.0", "main": "runtime.js", "keywords": ["regenerator", "runtime", "generator", "async"], "sideEffects": true, "repository": {"type": "git", "url": "git+https://github.com/facebook/regenerator.git#main"}, "license": "MIT", "readme": "# regenerator-runtime\n\nStandalone runtime for\n[Regenerator](https://github.com/facebook/regenerator)-compiled generator\nand `async` functions.\n\nTo import the runtime as a module (recommended), either of the following\nimport styles will work:\n```js\n// CommonJS\nconst regeneratorRuntime = require(\"regenerator-runtime\");\n\n// ECMAScript 2015\nimport regeneratorRuntime from \"regenerator-runtime\";\n```\n\nTo ensure that `regeneratorRuntime` is defined globally, either of the\nfollowing styles will work:\n```js\n// CommonJS\nrequire(\"regenerator-runtime/runtime\");\n\n// ECMAScript 2015\nimport \"regenerator-runtime/runtime.js\";\n```\n\nTo get the absolute file system path of `runtime.js`, evaluate the\nfollowing expression:\n```js\nrequire(\"regenerator-runtime/path\").path\n```\n", "readmeFilename": "README.md", "gitHead": "314889174f08cd754421cdbaa3529c27fac8826a", "bugs": {"url": "https://github.com/facebook/regenerator/issues"}, "homepage": "https://github.com/facebook/regenerator/tree/main#readme", "_id": "regenerator-runtime@0.14.0", "_nodeVersion": "18.13.0", "_npmVersion": "8.19.3", "dist": {"integrity": "sha512-srw17NI0TUWHuGa5CFGGmhfNIeja30WMBfbslPNhf6JrqQlLN5gcrvig1oqPxiVaXb0oW0XRKtH6Nngs5lKCIA==", "shasum": "5e19d68eb12d486f797e15a3c6a918f7cec5eb45", "tarball": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.14.0.tgz", "fileCount": 5, "unpackedSize": 27765, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCdIxPua3akp9DIHHQsnxgEmrcO0tB/JPcMn7KLIVsSgAIgP02DOUIn5nSZCSebX8gH76ghwdVZwlXOrF64x/YORa0="}]}, "_npmUser": {"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/regenerator-runtime_0.14.0_1691170520735_0.754132762881516"}, "_hasShrinkwrap": false}, "0.14.1": {"name": "regenerator-runtime", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Runtime for Regenerator-compiled generator and async functions.", "version": "0.14.1", "main": "runtime.js", "keywords": ["regenerator", "runtime", "generator", "async"], "sideEffects": true, "repository": {"type": "git", "url": "git+https://github.com/facebook/regenerator.git#main"}, "license": "MIT", "_id": "regenerator-runtime@0.14.1", "gitHead": "6be1e6295ce1577142c44863175d5180d91f8f00", "bugs": {"url": "https://github.com/facebook/regenerator/issues"}, "homepage": "https://github.com/facebook/regenerator/tree/main#readme", "_nodeVersion": "20.7.0", "_npmVersion": "10.1.0", "dist": {"integrity": "sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw==", "shasum": "356ade10263f685dda125100cd862c1db895327f", "tarball": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.14.1.tgz", "fileCount": 5, "unpackedSize": 27863, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICjlZeDknRhFYa9gRb1eH16nlEBlJ9XLgBKRKw7zp0i+AiBLkZbNEzSrQi7OWsY/syMt8wu2qZ5E4KGXfbF/Mjumsg=="}]}, "_npmUser": {"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ben<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/regenerator-runtime_0.14.1_1702673522773_0.9695616841897761"}, "_hasShrinkwrap": false}}, "keywords": ["regenerator", "runtime", "generator", "async"], "repository": {"type": "git", "url": "git+https://github.com/facebook/regenerator.git#main"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "readmeFilename": "README.md", "users": {"leapm": true, "ungurys": true, "alexxnica": true, "fanyegong": true, "panlw": true, "flumpus-dev": true}, "homepage": "https://github.com/facebook/regenerator/tree/main#readme", "bugs": {"url": "https://github.com/facebook/regenerator/issues"}}