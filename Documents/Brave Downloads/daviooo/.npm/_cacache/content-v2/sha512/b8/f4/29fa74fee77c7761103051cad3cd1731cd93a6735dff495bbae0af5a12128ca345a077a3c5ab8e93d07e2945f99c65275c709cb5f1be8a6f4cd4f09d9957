{"_id": "object-assign", "_rev": "129-9bc1ddd9948c96da627f61103d03e5d3", "name": "object-assign", "dist-tags": {"latest": "4.1.1"}, "versions": {"0.1.0": {"name": "object-assign", "version": "0.1.0", "keywords": ["object", "assign", "extend", "properties", "es6", "ecmascript", "harmony", "ponyfill", "prollyfill", "polyfill", "shim", "browser"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-assign@0.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/object-assign", "bugs": {"url": "https://github.com/sindresorhus/object-assign/issues"}, "dist": {"shasum": "d10994e1618922b563524299a272de5602f04ca0", "tarball": "https://registry.npmjs.org/object-assign/-/object-assign-0.1.0.tgz", "integrity": "sha512-60VOGyo5lPaua1bxHeCrhKvcH3FcWP9Tvi2zrcyASFH12gzBsHaOcF+xuGDIb3y9LKKOwd4Y1eiqSJH1UtUrrA==", "signatures": [{"sig": "MEYCIQCIvBhIIxindpuyJFyXhes+agXcvy1h2Q40db3awZHkRAIhAP9bUU/txtdrvzL28uVxuuzbE+oEhYZqxak7CWjCDRiW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["object-assign.js"], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/sindresorhus/object-assign", "type": "git"}, "_npmVersion": "1.3.25", "description": "ES6 Object.assign() ponyfill", "directories": {}, "devDependencies": {"mocha": "*"}}, "0.1.1": {"name": "object-assign", "version": "0.1.1", "keywords": ["object", "assign", "extend", "properties", "es6", "ecmascript", "harmony", "ponyfill", "prollyfill", "polyfill", "shim", "browser"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-assign@0.1.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/object-assign", "bugs": {"url": "https://github.com/sindresorhus/object-assign/issues"}, "dist": {"shasum": "0eb570293ca87b272a0f3a62b50adb9a5f0a8f1a", "tarball": "https://registry.npmjs.org/object-assign/-/object-assign-0.1.1.tgz", "integrity": "sha512-+j27a/+Pal2/Q2gdNvo/qtrQtASikOLmv3GmfrDRIyWjmm9z+ZbH+UOmYnUg8KmBwfE9NmPlhWtmSSSNcgfKJg==", "signatures": [{"sig": "MEUCIH2aKk+bbDDOXDqdy6OR65yxMafoBdIVbo4L2df0/opzAiEA0e/atA096Evpl2sGwgyrZM5kz67gVn9WCqCQtKOlk2Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "object-assign.js", "_from": ".", "files": ["object-assign.js"], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/sindresorhus/object-assign", "type": "git"}, "_npmVersion": "1.3.25", "description": "ES6 Object.assign() ponyfill", "directories": {}, "devDependencies": {"mocha": "*"}}, "0.1.2": {"name": "object-assign", "version": "0.1.2", "keywords": ["object", "assign", "extend", "properties", "es6", "ecmascript", "harmony", "ponyfill", "prollyfill", "polyfill", "shim", "browser"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-assign@0.1.2", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/object-assign", "bugs": {"url": "https://github.com/sindresorhus/object-assign/issues"}, "dist": {"shasum": "036992f073aff7b2db83d06b3fb3155a5ccac37f", "tarball": "https://registry.npmjs.org/object-assign/-/object-assign-0.1.2.tgz", "integrity": "sha512-tBiEbE5Yeu7sxExjNe2bceZsgGKJtrgUWtC2PqJsUdRAoPgrHRhd1R2pJ1Y/g3H1dxuaNqd6U6WV/vzS2wa14A==", "signatures": [{"sig": "MEQCIEf+rbzzNRIPcIkiP8TN/6STyd0972/UdkUHaTwL10RwAiA/lLG14cbpUecjGo3343T8W3m3jAElKV+CFrkwRvEjGQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "object-assign.js", "_from": ".", "files": ["object-assign.js"], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/sindresorhus/object-assign", "type": "git"}, "_npmVersion": "1.3.25", "description": "ES6 Object.assign() ponyfill", "directories": {}, "devDependencies": {"mocha": "*"}}, "0.2.0": {"name": "object-assign", "version": "0.2.0", "keywords": ["object", "assign", "extend", "properties", "es6", "ecmascript", "harmony", "ponyfill", "prollyfill", "polyfill", "shim", "browser"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-assign@0.2.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/object-assign", "bugs": {"url": "https://github.com/sindresorhus/object-assign/issues"}, "dist": {"shasum": "ab5833b3c86fb5b286c91fedc772e7939fc7b30c", "tarball": "https://registry.npmjs.org/object-assign/-/object-assign-0.2.0.tgz", "integrity": "sha512-cUklMd++52CmDhrrBPrWSgv8Qk21OP7fe34zEpnHPLAgx9lxbnUpejcSfQgkFE+zLz4RtqogezUabLzQfjKXFg==", "signatures": [{"sig": "MEQCIC7mLjmZDfKSBLFa9hadc04GSkQ2yaTju2GCsZVj+oYwAiAzoF29VHYHiMCiKkNar48c2jclOEqI11/GC10XOynS7g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "object-assign.js", "_from": ".", "files": ["object-assign.js"], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/sindresorhus/object-assign", "type": "git"}, "_npmVersion": "1.4.2", "description": "ES6 Object.assign() ponyfill", "directories": {}, "devDependencies": {"mocha": "*"}}, "0.2.1": {"name": "object-assign", "version": "0.2.1", "keywords": ["object", "assign", "extend", "properties", "es6", "ecmascript", "harmony", "ponyfill", "prollyfill", "polyfill", "shim", "browser"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-assign@0.2.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/object-assign", "bugs": {"url": "https://github.com/sindresorhus/object-assign/issues"}, "dist": {"shasum": "bd4dd0bab7fa495408d9781e8a9a2180d2bdae52", "tarball": "https://registry.npmjs.org/object-assign/-/object-assign-0.2.1.tgz", "integrity": "sha512-PgJQLmEIuEhyTHIGns0jYUGb9qDUTKYqfLOjSYte4zb6xzhYQ+VsluuAShEABkh2Xz2An79SGf4/ksxjfqa4lA==", "signatures": [{"sig": "MEUCIQDTvKfscPD8tKhbz3hlsXhXSxnrdGJi9reJBcBJGnNSGwIgAJogJeJHDAGT8A9WmuEXAa9UnXFrTgw819p94/JtCb4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "object-assign.js", "_from": ".", "files": ["object-assign.js"], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/sindresorhus/object-assign", "type": "git"}, "_npmVersion": "1.4.4", "description": "ES6 Object.assign() ponyfill", "directories": {}, "devDependencies": {"mocha": "*"}}, "0.2.2": {"name": "object-assign", "version": "0.2.2", "keywords": ["object", "assign", "extend", "properties", "es6", "ecmascript", "harmony", "ponyfill", "prollyfill", "polyfill", "shim", "browser"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-assign@0.2.2", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/object-assign", "bugs": {"url": "https://github.com/sindresorhus/object-assign/issues"}, "dist": {"shasum": "e0a78bc56af9c092051167f6b8f23249e7dde1a6", "tarball": "https://registry.npmjs.org/object-assign/-/object-assign-0.2.2.tgz", "integrity": "sha512-GBw66O/X6uqIpuonDvRD0mOCr54BalNuUxwrsyvbOeO+PxR3jAir1ETnJEgIwnGJjnrRjZtUeJd8NfyfnzfDaA==", "signatures": [{"sig": "MEUCIQD30lcuIqTWdJcNEmyb/9OwZmfyedYSal5mAO+ibVe4vwIgA2fMSn1FVQy7o4q02iXKmz8a5IKCX+BhPPnYEDeF520=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "object-assign.js", "_from": ".", "files": ["object-assign.js"], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/sindresorhus/object-assign", "type": "git"}, "_npmVersion": "1.4.6", "description": "ES6 Object.assign() ponyfill", "directories": {}, "devDependencies": {"mocha": "*"}}, "0.3.0": {"name": "object-assign", "version": "0.3.0", "keywords": ["object", "assign", "extend", "properties", "es6", "ecmascript", "harmony", "ponyfill", "prollyfill", "polyfill", "shim", "browser"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-assign@0.3.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/object-assign", "bugs": {"url": "https://github.com/sindresorhus/object-assign/issues"}, "dist": {"shasum": "22f8550b76bf86679efd2117c568081ab302ac4d", "tarball": "https://registry.npmjs.org/object-assign/-/object-assign-0.3.0.tgz", "integrity": "sha512-uZGJfRFgToFrGGbFNryV2lenyGK11ffP8NgdxcFxqS0eXNIgwfMEPVxR2gRE1FZj9AXMvlwp9YkCuOhhtixJzQ==", "signatures": [{"sig": "MEUCIQCCEmiYoRrtDhEq4TKXlRDAHioMTeomPQU2rJRIsUR1RgIgT0Eu3IPiFDUwA+0Zn8UNR/LIUDO5jBXD7+EbJ7jgV5g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "object-assign.js", "_from": ".", "files": ["object-assign.js"], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/sindresorhus/object-assign", "type": "git"}, "_npmVersion": "1.4.6", "description": "ES6 Object.assign() ponyfill", "directories": {}, "devDependencies": {"mocha": "*"}}, "0.3.1": {"name": "object-assign", "version": "0.3.1", "keywords": ["object", "assign", "extend", "properties", "es6", "ecmascript", "harmony", "ponyfill", "prollyfill", "polyfill", "shim", "browser"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-assign@0.3.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/object-assign", "bugs": {"url": "https://github.com/sindresorhus/object-assign/issues"}, "dist": {"shasum": "060e2a2a27d7c0d77ec77b78f11aa47fd88008d2", "tarball": "https://registry.npmjs.org/object-assign/-/object-assign-0.3.1.tgz", "integrity": "sha512-4gWmwoU6o9UImLLzq+8R+kzWT0ABYdKXuvSp08JpYzhibFvdUirMfE9nE5yYHcG1k9ClcVueR4TolZpRvwg5og==", "signatures": [{"sig": "MEQCIFslubk15SfJM1L55QZjrh8+IBplY10elL8h/zAeoBOIAiBR/JlDtmone70dzMXYPP8Bogs5PO9o+i1FGiI834AnHQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "object-assign.js", "_from": ".", "files": ["object-assign.js"], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/sindresorhus/object-assign", "type": "git"}, "_npmVersion": "1.4.6", "description": "ES6 Object.assign() ponyfill", "directories": {}, "devDependencies": {"mocha": "*"}}, "0.4.0": {"name": "object-assign", "version": "0.4.0", "keywords": ["object", "assign", "extend", "properties", "es6", "ecmascript", "harmony", "ponyfill", "prollyfill", "polyfill", "shim", "browser"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-assign@0.4.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/object-assign", "bugs": {"url": "https://github.com/sindresorhus/object-assign/issues"}, "dist": {"shasum": "e9ee69b52192780b4d062a3a86ac2fa2adc58951", "tarball": "https://registry.npmjs.org/object-assign/-/object-assign-0.4.0.tgz", "integrity": "sha512-tIZc30wg3KE13fsd1vp6iyc4fzS+NM6TDxB3YaOOQBeiUqgjoS6mX5DyU0MRCPQVXVhEcqupOoD4TZGBkKXH3w==", "signatures": [{"sig": "MEUCIQDXfctuwt/5c6Oz+u49uyfqvsdzxvEYgUPJuw2MA5FypgIgbMbHqzVagHuE8SiP9/Xykjz+lA2OzCNWqDGCTjgy6Ls=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "object-assign.js", "_from": ".", "files": ["object-assign.js"], "_shasum": "e9ee69b52192780b4d062a3a86ac2fa2adc58951", "engines": {"node": ">=0.10.0"}, "gitHead": "dc8cdabc34cab661419bffe01723a0d649255a2e", "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/sindresorhus/object-assign", "type": "git"}, "_npmVersion": "1.4.14", "description": "ES6 Object.assign() ponyfill", "directories": {}, "devDependencies": {"mocha": "*"}}, "1.0.0": {"name": "object-assign", "version": "1.0.0", "keywords": ["object", "assign", "extend", "properties", "es6", "ecmascript", "harmony", "ponyfill", "prollyfill", "polyfill", "shim", "browser"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-assign@1.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/object-assign", "bugs": {"url": "https://github.com/sindresorhus/object-assign/issues"}, "dist": {"shasum": "e65dc8766d3b47b4b8307465c8311da030b070a6", "tarball": "https://registry.npmjs.org/object-assign/-/object-assign-1.0.0.tgz", "integrity": "sha512-LpUkixU1BUMQ6bwUHbOue4IGGbdRbxi+IEZw7zHniw78erlxrKGHbhfLbHIsI35LGbGqys6QOrjVmLnD2ie+1A==", "signatures": [{"sig": "MEYCIQDBuO0C6dqYjAOz2bII4Em2f2aVwGM3C8vo3PfIqRfZHgIhAMsL3jj/zenm014zsBYuxSjYiqsboLloro35/mY2lZ8Z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "e65dc8766d3b47b4b8307465c8311da030b070a6", "engines": {"node": ">=0.10.0"}, "gitHead": "a17eef6882cf3ffcee46f7d9d5a5ba0abc6b029c", "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/object-assign", "type": "git"}, "_npmVersion": "1.4.21", "description": "ES6 Object.assign() ponyfill", "directories": {}, "devDependencies": {"mocha": "*"}}, "2.0.0": {"name": "object-assign", "version": "2.0.0", "keywords": ["object", "assign", "extend", "properties", "es6", "ecmascript", "harmony", "ponyfill", "prollyfill", "polyfill", "shim", "browser"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-assign@2.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/object-assign", "bugs": {"url": "https://github.com/sindresorhus/object-assign/issues"}, "dist": {"shasum": "f8309b09083b01261ece3ef7373f2b57b8dd7042", "tarball": "https://registry.npmjs.org/object-assign/-/object-assign-2.0.0.tgz", "integrity": "sha512-TTVfbeUpQoCNyoOddbCTlMYnK8LsIpLD72jtE6SjwYL2JRr7lskqbMghqdTFp9wHWrZAlDWYUJ1unzPnWWPWQA==", "signatures": [{"sig": "MEYCIQD3rlhjIeZKrIMk88ZHUQVyZubeLCHQrrxI3eXbPM5IOQIhAIO8+33TKZMC1hjFc2AXTEa87YSW3YKVCHdSrT3prOFD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "f8309b09083b01261ece3ef7373f2b57b8dd7042", "engines": {"node": ">=0.10.0"}, "gitHead": "4cd0365f5a78dd369473ca0bbd31f7b234148c42", "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/object-assign", "type": "git"}, "_npmVersion": "2.1.5", "description": "ES6 Object.assign() ponyfill", "directories": {}, "_nodeVersion": "0.10.32", "devDependencies": {"mocha": "*"}}, "3.0.0": {"name": "object-assign", "version": "3.0.0", "keywords": ["object", "assign", "extend", "properties", "es6", "ecmascript", "harmony", "ponyfill", "prollyfill", "polyfill", "shim", "browser"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-assign@3.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/object-assign", "bugs": {"url": "https://github.com/sindresorhus/object-assign/issues"}, "dist": {"shasum": "9bedd5ca0897949bca47e7ff408062d549f587f2", "tarball": "https://registry.npmjs.org/object-assign/-/object-assign-3.0.0.tgz", "integrity": "sha512-jHP15vXVGeVh1HuaA2wY6lxk+whK/x4KBG88VXeRma7CCun7iGD5qPc4eYykQ9sdQvg8jkwFKsSxHln2ybW3xQ==", "signatures": [{"sig": "MEYCIQD94E/hWr5qdRxtTI4BU98p4b21RmdtJd7Jer1VURsJQwIhAMAMGiXeXY81FFBbKv4JOed6//Ia2B9ki7GDVdnEd3+9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "9bedd5ca0897949bca47e7ff408062d549f587f2", "engines": {"node": ">=0.10.0"}, "gitHead": "02622dcb0d82bd81a071ed0b04fedf5e5eea7059", "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/object-assign", "type": "git"}, "_npmVersion": "2.10.1", "description": "ES6 Object.assign() ponyfill", "directories": {}, "_nodeVersion": "0.12.4", "devDependencies": {"mocha": "*"}}, "2.1.1": {"name": "object-assign", "version": "2.1.1", "keywords": ["object", "assign", "extend", "properties", "es6", "ecmascript", "harmony", "ponyfill", "prollyfill", "polyfill", "shim", "browser"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-assign@2.1.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/object-assign", "bugs": {"url": "https://github.com/sindresorhus/object-assign/issues"}, "dist": {"shasum": "43c36e5d569ff8e4816c4efa8be02d26967c18aa", "tarball": "https://registry.npmjs.org/object-assign/-/object-assign-2.1.1.tgz", "integrity": "sha512-CdsOUYIh5wIiozhJ3rLQgmUTgcyzFwZZrqhkKhODMoGtPKM+wt0h0CNIoauJWMsS9822EdzPsF/6mb4nLvPN5g==", "signatures": [{"sig": "MEUCIBeYRIlcnvZjXN5YJkydgaGFumdUVQMB40esEKP0OU8vAiEAt12irKPoxwdYtyD1Dlr+QoqL1hwX16deO39LhB4peAQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "43c36e5d569ff8e4816c4efa8be02d26967c18aa", "engines": {"node": ">=0.10.0"}, "gitHead": "4cd0365f5a78dd369473ca0bbd31f7b234148c42", "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/object-assign", "type": "git"}, "_npmVersion": "2.10.1", "description": "ES6 Object.assign() ponyfill", "directories": {}, "_nodeVersion": "0.12.4", "devDependencies": {"mocha": "*"}}, "4.0.0": {"name": "object-assign", "version": "4.0.0", "keywords": ["object", "assign", "extend", "properties", "es6", "ecmascript", "harmony", "ponyfill", "prollyfill", "polyfill", "shim", "browser"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-assign@4.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/object-assign#readme", "bugs": {"url": "https://github.com/sindresorhus/object-assign/issues"}, "dist": {"shasum": "1e876669ea263a6d4f37e9504dc859fefc7b4506", "tarball": "https://registry.npmjs.org/object-assign/-/object-assign-4.0.0.tgz", "integrity": "sha512-Iejvk7ZL9eGjp6QU+Ep0deFdLnCJUKeMitmape/YRAYFNK3vI48uF3ckyWLbmHom4V7MOgPTH9lhubQYiC9D6w==", "signatures": [{"sig": "MEQCIHDNWKwM5ffWNRlOptRoCqSDzitIvqjwoF+UHzNQh36LAiAXmkf2c0OVLnJL+W1RUbd8Nkb/EUwaws0MCdAf3xKefQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "1e876669ea263a6d4f37e9504dc859fefc7b4506", "engines": {"node": ">=0.10.0"}, "gitHead": "38bd16197af7cdfbf902d56197bde4fdc9ce4701", "scripts": {"test": "mocha", "bench": "matcha bench.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/object-assign.git", "type": "git"}, "_npmVersion": "2.13.5", "description": "ES6 Object.assign() ponyfill", "directories": {}, "_nodeVersion": "0.12.7", "devDependencies": {"mocha": "*", "lodash": "^3.10.1", "matcha": "^0.6.0"}}, "4.0.1": {"name": "object-assign", "version": "4.0.1", "keywords": ["object", "assign", "extend", "properties", "es6", "ecmascript", "harmony", "ponyfill", "prollyfill", "polyfill", "shim", "browser"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-assign@4.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/object-assign#readme", "bugs": {"url": "https://github.com/sindresorhus/object-assign/issues"}, "xo": {"envs": ["node", "mocha"]}, "dist": {"shasum": "99504456c3598b5cad4fc59c26e8a9bb107fe0bd", "tarball": "https://registry.npmjs.org/object-assign/-/object-assign-4.0.1.tgz", "integrity": "sha512-c6legOHWepAbWnp3j5SRUMpxCXBKI4rD7A5Osn9IzZ8w4O/KccXdW0lqdkQKbpk0eHGjNgKihgzY6WuEq99Tfw==", "signatures": [{"sig": "MEUCIQCtF2mzGk7iSB40J0hQYY7tqnnRcPqx9v/Nv2xK6i2hsgIgV8ZJdKb6C8S7Lir3GWQCjwGSOO70xBHBT2WWl1bvTd0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "99504456c3598b5cad4fc59c26e8a9bb107fe0bd", "engines": {"node": ">=0.10.0"}, "gitHead": "b0c40d37cbc43e89ad3326a9bad4c6b3133ba6d3", "scripts": {"test": "xo && mocha", "bench": "matcha bench.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/object-assign.git", "type": "git"}, "_npmVersion": "2.13.3", "description": "ES6 Object.assign() ponyfill", "directories": {}, "_nodeVersion": "3.0.0", "devDependencies": {"xo": "*", "mocha": "*", "lodash": "^3.10.1", "matcha": "^0.6.0"}}, "4.1.0": {"name": "object-assign", "version": "4.1.0", "keywords": ["object", "assign", "extend", "properties", "es2015", "ecmascript", "harmony", "ponyfill", "prollyfill", "polyfill", "shim", "browser"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-assign@4.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "spicyj", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/object-assign#readme", "bugs": {"url": "https://github.com/sindresorhus/object-assign/issues"}, "dist": {"shasum": "7a3b3d0e98063d43f4c03f2e8ae6cd51a86883a0", "tarball": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.0.tgz", "integrity": "sha512-Lbc7GfN7XFaK30bzUN3cDYLOkT0dH05S0ax1QikylHUD9+Z9PRF3G1iYwX3kcz+6AlzTFGkUgMxz6l3aUwbwTA==", "signatures": [{"sig": "MEUCIAdHzM+IBxnCrzJoK8aw+wy<PERSON>ouk+/vxiTwpv8zhyfA/aAiEA5DbQgAihrv724K9MtfFIxMZrmYwJRLF+r221JmqHYjc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "7a3b3d0e98063d43f4c03f2e8ae6cd51a86883a0", "engines": {"node": ">=0.10.0"}, "gitHead": "72fe21c86911758f3342fdf41c2a57860d5829bc", "scripts": {"test": "xo && mocha", "bench": "matcha bench.js"}, "_npmUser": {"name": "spicyj", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/object-assign.git", "type": "git"}, "_npmVersion": "2.14.19", "description": "ES2015 Object.assign() ponyfill", "directories": {}, "_nodeVersion": "4.1.0", "devDependencies": {"xo": "*", "mocha": "*", "lodash": "^4.8.2", "matcha": "^0.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/object-assign-4.1.0.tgz_1462212593641_0.3332549517508596", "host": "packages-16-east.internal.npmjs.com"}}, "4.1.1": {"name": "object-assign", "version": "4.1.1", "keywords": ["object", "assign", "extend", "properties", "es2015", "ecmascript", "harmony", "ponyfill", "prollyfill", "polyfill", "shim", "browser"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "object-assign@4.1.1", "maintainers": [{"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "spicyj", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/object-assign#readme", "bugs": {"url": "https://github.com/sindresorhus/object-assign/issues"}, "dist": {"shasum": "2109adc7965887cfc05cbbd442cac8bfbb360863", "tarball": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==", "signatures": [{"sig": "MEUCIQCJxQuZmlAryzqc1NiNMWyTZ26Y+FO6f+HVICCm5oJBWgIgc1i0sdNz1TWpRbRY3fXQLpopoXsesTKr/RbLRT2QuqE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "2109adc7965887cfc05cbbd442cac8bfbb360863", "engines": {"node": ">=0.10.0"}, "gitHead": "a89774b252c91612203876984bbd6addbe3b5a0e", "scripts": {"test": "xo && ava", "bench": "matcha bench.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/object-assign.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "ES2015 `Object.assign()` ponyfill", "directories": {}, "_nodeVersion": "4.6.2", "devDependencies": {"xo": "^0.16.0", "ava": "^0.16.0", "lodash": "^4.16.4", "matcha": "^0.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/object-assign-4.1.1.tgz_1484580915042_0.07107710791751742", "host": "packages-12-west.internal.npmjs.com"}}}, "time": {"created": "2014-02-02T15:47:58.857Z", "modified": "2025-02-16T08:13:33.616Z", "0.1.0": "2014-02-02T15:47:58.857Z", "0.1.1": "2014-02-04T20:02:13.757Z", "0.1.2": "2014-02-04T21:03:17.088Z", "0.2.0": "2014-02-15T21:39:23.178Z", "0.2.1": "2014-03-24T14:06:35.136Z", "0.2.2": "2014-04-08T13:53:46.644Z", "0.3.0": "2014-04-11T22:34:55.461Z", "0.3.1": "2014-04-30T22:36:40.407Z", "0.4.0": "2014-08-13T07:47:00.902Z", "1.0.0": "2014-08-14T10:45:03.502Z", "2.0.0": "2014-11-22T03:40:09.589Z", "2.1.0": "2015-06-04T13:06:53.193Z", "3.0.0": "2015-06-04T14:09:02.051Z", "2.0.1": "2015-06-05T14:24:31.942Z", "2.1.1": "2015-06-05T14:26:28.479Z", "4.0.0": "2015-08-20T09:06:05.735Z", "4.0.1": "2015-08-20T12:34:04.010Z", "4.1.0": "2016-05-02T18:09:55.804Z", "4.1.1": "2017-01-16T15:35:15.282Z"}, "bugs": {"url": "https://github.com/sindresorhus/object-assign/issues"}, "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/sindresorhus/object-assign#readme", "keywords": ["object", "assign", "extend", "properties", "es2015", "ecmascript", "harmony", "ponyfill", "prollyfill", "polyfill", "shim", "browser"], "repository": {"url": "git+https://github.com/sindresorhus/object-assign.git", "type": "git"}, "description": "ES2015 `Object.assign()` ponyfill", "maintainers": [{"name": "sophiebits", "email": "<EMAIL>"}, {"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "readme": "# object-assign [![Build Status](https://travis-ci.org/sindresorhus/object-assign.svg?branch=master)](https://travis-ci.org/sindresorhus/object-assign)\n\n> ES2015 [`Object.assign()`](http://www.2ality.com/2014/01/object-assign.html) [ponyfill](https://ponyfill.com)\n\n\n## Use the built-in\n\nNode.js 4 and up, as well as every evergreen browser (Chrome, Edge, Firefox, Opera, Safari),\nsupport `Object.assign()` :tada:. If you target only those environments, then by all\nmeans, use `Object.assign()` instead of this package.\n\n\n## Install\n\n```\n$ npm install --save object-assign\n```\n\n\n## Usage\n\n```js\nconst objectAssign = require('object-assign');\n\nobjectAssign({foo: 0}, {bar: 1});\n//=> {foo: 0, bar: 1}\n\n// multiple sources\nobjectAssign({foo: 0}, {bar: 1}, {baz: 2});\n//=> {foo: 0, bar: 1, baz: 2}\n\n// overwrites equal keys\nobjectAssign({foo: 0}, {foo: 1}, {foo: 2});\n//=> {foo: 2}\n\n// ignores null and undefined sources\nobjectAssign({foo: 0}, null, {bar: 1}, undefined);\n//=> {foo: 0, bar: 1}\n```\n\n\n## API\n\n### objectAssign(target, [source, ...])\n\nAssigns enumerable own properties of `source` objects to the `target` object and returns the `target` object. Additional `source` objects will overwrite previous ones.\n\n\n## Resources\n\n- [ES2015 spec - Object.assign](https://people.mozilla.org/~jorendorff/es6-draft.html#sec-object.assign)\n\n\n## Related\n\n- [deep-assign](https://github.com/sindresorhus/deep-assign) - Recursive `Object.assign()`\n\n\n## License\n\nMIT © [Sindre Sorhus](https://sindresorhus.com)\n", "readmeFilename": "readme.md", "users": {"iolo": true, "kai_": true, "zema": true, "andin": true, "doruk": true, "panlw": true, "robur": true, "tsyue": true, "ugarz": true, "acmeid": true, "arttse": true, "atriix": true, "crees!": true, "isa424": true, "kaapex": true, "maxidr": true, "monjer": true, "phuson": true, "vutran": true, "wvlvik": true, "xu_q90": true, "yann86": true, "belcour": true, "dkannan": true, "elmorec": true, "tzq1011": true, "ungurys": true, "alexkval": true, "antouank": true, "chaofeis": true, "freebird": true, "ivanoats": true, "koulmomo": true, "meligatt": true, "nicodinh": true, "oilchris": true, "qddegtya": true, "wangfeia": true, "wkaifang": true, "xiaochao": true, "james3299": true, "jjj201200": true, "justjavac": true, "levisl176": true, "liugenbin": true, "nice_body": true, "rylan_yan": true, "balexandre": true, "brainpoint": true, "huangdawei": true, "krugarmatt": true, "mysticatea": true, "qqqppp9998": true, "rajikaimal": true, "shipengyan": true, "flumpus-dev": true, "mattattaque": true, "nisimjoseph": true, "rakeshmakam": true, "scotchulous": true, "sinsaints76": true, "steindaniel": true, "josejaguirre": true, "loselovegirl": true, "mystaticself": true, "prabhash1785": true, "chinawolf_wyp": true, "markthethomas": true, "parkerproject": true, "schnittstabil": true, "dotnetcarpenter": true}}