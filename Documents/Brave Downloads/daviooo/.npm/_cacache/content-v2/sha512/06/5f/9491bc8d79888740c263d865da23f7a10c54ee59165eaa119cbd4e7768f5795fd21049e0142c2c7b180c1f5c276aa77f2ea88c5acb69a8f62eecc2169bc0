{"_id": "functions-have-names", "_rev": "10-a5c0a3d2fe9bafb43839b9fd43642861", "name": "functions-have-names", "dist-tags": {"latest": "1.2.3"}, "versions": {"1.0.0": {"name": "functions-have-names", "version": "1.0.0", "description": "Does this JS environment support the `name` property on functions?", "main": "index.js", "scripts": {"version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "lint": "eslint .", "pretest": "npm run lint", "tests-only": "node test", "test": "npm run tests-only", "posttest": "npx aud"}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/functions-have-names.git"}, "keywords": ["function", "name", "es5", "names", "functions", "ie"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/functions-have-names/issues"}, "homepage": "https://github.com/ljharb/functions-have-names#readme", "devDependencies": {"@ljharb/eslint-config": "^13.1.1", "auto-changelog": "^1.14.1", "eslint": "^5.16.0", "tape": "^4.11.0"}, "auto-changelog": {"output": "CHANGELOG.md", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "gitHead": "836819cbfe022801a63c05152ee6b01b7cfa63e2", "_id": "functions-have-names@1.0.0", "_nodeVersion": "12.6.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-Kj/Lkj0QrXGtgxXuHRdRTj1YYL+NJ/WDJak0PAPWwGetWRvyW0db5s7mBRlzMUHNCQrIRkMAous5qngB5K3cmw==", "shasum": "883f3295f82d311e7d66fde96c8b00c93ed64c7c", "tarball": "https://registry.npmjs.org/functions-have-names/-/functions-have-names-1.0.0.tgz", "fileCount": 10, "unpackedSize": 14564, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdNql/CRA9TVsSAnZWagAAbR0P/iiS5Q5affahdKblslxy\ndjiNYVgUrSLBSfKgzexgFyRLTAs7cppOyTjxCAH86ePHrbbXr8atW18nrWFK\n+snrFZkbFKyFiQfhmMyLWdJrfqucI8zFYeWb1JdIhsRJ+2EcYXhoImBaol4h\nzVDRDQRyE7jCFtry8wpJEVLhEmnErEPdOeNFlu4xUA0K79wdnvpN32xoC7VV\nWGUYxo5XM8EUkr+RKR3kzgaKUaZQW15SyF9lnRtWsGZhuRWjeCSBeDv6z/ab\nfct7G5Sc/iGko/VuRGfaJ4FInBB8fjcsc6VVwqauU5ron32vGCC0c4nYrCpE\nOuItDt8pinWitXG0/3vL+PyLPJqRzbLGQTzZSKoN9pT96b69a8VBK7r5FcX0\nwZYgVPSdTM40v81VFiP+UX0dlcOFrs+OOHP45DYa/37hFav0280vWiC6nvkB\nfk+CKnleC2zKbWG/n5pudy9BPhst5oihsPYEuR2Phzas05aR+OVcLJz0XitM\nU5RmXcyWTwR0CJIG87sjGFZwYYghVkBUq8f0PGiqP24yuzHlAgzaiOsKgdED\nkpnloifuVLAEyrx5KPtOKp9/IBULo2mHLRMsyiTkmscZdxIaumDv0Ry2Ruev\nnGRTAhT9Rny7qhYpJmOr8knZmjGUbjmQ2DyRlLF+RibNxC/vBlzwVjynZjKP\n1Tb5\r\n=JL9j\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBeKQJqmyQ8hgp27BMKh8XZxL5+H4j82yfgr6+rFmCCgAiBtqK+5ThttKqoPkJRebEn7SezU8QN4qStnQvujoe4fMA=="}]}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/functions-have-names_1.0.0_1563863422598_0.9162368257567872"}, "_hasShrinkwrap": false}, "1.1.0": {"name": "functions-have-names", "version": "1.1.0", "description": "Does this JS environment support the `name` property on functions?", "main": "index.js", "scripts": {"version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "lint": "eslint .", "pretest": "npm run lint", "tests-only": "node test", "test": "npm run tests-only", "posttest": "npx aud"}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/functions-have-names.git"}, "keywords": ["function", "name", "es5", "names", "functions", "ie"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/functions-have-names/issues"}, "homepage": "https://github.com/ljharb/functions-have-names#readme", "devDependencies": {"@ljharb/eslint-config": "^13.1.1", "auto-changelog": "^1.14.1", "eslint": "^5.16.0", "tape": "^4.11.0"}, "auto-changelog": {"output": "CHANGELOG.md", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "gitHead": "12a3d1c38a18efe8d4b373a8d240512465f5fb23", "_id": "functions-have-names@1.1.0", "_nodeVersion": "12.6.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-JybK7HbxvvEaphOTDs46DTo4erQ0SWwBwSIHfEsCjKcIHAQk3z7HvGTSIWeJPuHgjN9S39Kh3aXsJKVIQ7zy0A==", "shasum": "ed5ed6a574ff6acd6d8f3a9181d35fcd6abbd2fb", "tarball": "https://registry.npmjs.org/functions-have-names/-/functions-have-names-1.1.0.tgz", "fileCount": 11, "unpackedSize": 16214, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdN0uhCRA9TVsSAnZWagAAcmcP/3Znztt0b73JF2aB9cA9\nBHB5LuFfyjPXBQ1wmwMo8R9zSCOFp8+07iaoyLoBNnzecKsDmMRvWMa0ahKS\nI8IztddntF2C97EoMCQg7SeyGBMCUnbOlE029h0O1SFpt2ZBFAGi2NfE5Wye\nR3ckvNc5hdcSRSQZCMRjRAOq+SWSfXyTHzVBaEQsV/NDtUSrjyArRyLtoI6V\nlYVN3fWrFN7/RFJI05L+c690vxE5oKD9Q3fAEe8FrKO5D1jc8b1Y0LayywJf\n/NT/n4lYulmlg/Qsdr0FtZqCZ+8hqH7cRqhFRQ4D7oI5eDVEPRgqCufhu4PN\nGyUWjXwm6CMcmaSYpgUWSvgrm02Buj+5WDpN0JFMaZWLxiiMa+tXz99IHjJe\nthN4EdjE7gJLErY79EkxiPzm2QBh+hPfGGelGZ0Tb+oaC3e3ZYlLaBYKVB/n\nUK6XUVRaOwb/PMa6OT78q3zjkSEVFRsILtDJPcnUFbQx2LxNjs6oo4M7BYg7\nNB719QMPThOiu56+2rTq5G5d2Jqcp/CFrcfJVXu9i78LBxMxfrZgeEBZbGM8\n2kZNOcForNdbtJIbyX6Be9nVzx8WxOYozUP63vDDrUwq4xDg5XdKFOTxDxk3\neBwvViUl+brd7f3XXAvTAl8Uc1xl0xB2Iga02xCszgAWRNMaJehzhIZDNOK+\n4Kx3\r\n=zkFd\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDfoohnSXaHNCQXWgeSe6S4RVo3FniCMioLd6X7ZRm9+AIgBam5W9eVH8TuDaL8wBCpW6gzCCthiOjWDkG+ARSQisE="}]}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/functions-have-names_1.1.0_1563904928955_0.259192563162733"}, "_hasShrinkwrap": false}, "1.1.1": {"name": "functions-have-names", "version": "1.1.1", "description": "Does this JS environment support the `name` property on functions?", "main": "index.js", "scripts": {"version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "lint": "eslint .", "pretest": "npm run lint", "tests-only": "node test", "test": "npm run tests-only", "posttest": "npx aud"}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/functions-have-names.git"}, "keywords": ["function", "name", "es5", "names", "functions", "ie"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/functions-have-names/issues"}, "homepage": "https://github.com/ljharb/functions-have-names#readme", "devDependencies": {"@ljharb/eslint-config": "^13.1.1", "auto-changelog": "^1.14.1", "eslint": "^5.16.0", "tape": "^4.11.0"}, "auto-changelog": {"output": "CHANGELOG.md", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "gitHead": "1dd3b0103a84eabc11992bd0ec4fe8598c729530", "_id": "functions-have-names@1.1.1", "_nodeVersion": "12.7.0", "_npmVersion": "6.10.0", "dist": {"integrity": "sha512-U0kNHUoxwPNPWOJaMG7Z00d4a/qZVrFtzWJRaK8V9goaVOCXBSQSJpt3MYGNtkScKEBKovxLjnNdC9MlXwo5Pw==", "shasum": "79d35927f07b8e7103d819fed475b64ccf7225ea", "tarball": "https://registry.npmjs.org/functions-have-names/-/functions-have-names-1.1.1.tgz", "fileCount": 11, "unpackedSize": 16824, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdOMzeCRA9TVsSAnZWagAA+k4P/3+t60t+RhXEBYFEgsv9\nSgQTb07PDKnOhaEPWxkZKih6ncrBZCN1Tdn2mxCCPCzNxxZO8cqd5R6vhdoI\npBIbvMf797ea9Glkr3qBRk0KlF9el01+d8N2EnycNooXlZNsGGzv8WGF7Q3s\nKUpE6Nw0/nBsSW7ulWdQ5Gi3EZP7yh2ovS06/RFvVsNGIFTQtpxSiSXjMOLp\nYqHf8HDIsGsThzaa05Ojt10nwN9TSC309nktVQoVG3P3mpuVIp25T1lRq+lx\nx9jVs25b+nHtuCwH7FbJ9sftfQt9Vzb1jgBIzJYFVVBCNdX27QLQjZY7Eygq\nxqJ6ou7xGECc7QuIy4Gn6+AjdCiTsbg5zu3Qh/4yc2QEIRmnodm6bt3fekwP\nGwcNUaeRZCNLVGxuDbeGIqNL51mLGCV+QHpkpxboTXVyDzXBRh+AQlgoL9DB\n1QWiv6ES/NX4nh1/45jO9TsP5HvjdNotcna/+M0E4Zmg/TkyiMUIp4wPbO3o\np5pVPcDMcRZ14Gp7nZTKnvPXENBTsqgN9CtJaRdd+LK1Tr8xHQAYTjycgmbp\nVAzti00yiIOvtpsqs2YG/O7wFwOa7bJq6bf8RCNz/XQCvOxYIDierz6PbxFt\nO7DsVmVZCfwJpjXPHEn/UwfeeJmv8VM3ruTK/JorU9G1Hs4S5N9Sbu5M85LN\nfFqS\r\n=3iS2\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFHe1GSNAHvTEiyrWWsyuguS8xz9gUdFHLI306lpAIg3AiBiR+PTtjjLKlpMHxFz8mFNv6ygnYXFTmAXyrP3dhAL8w=="}]}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/functions-have-names_1.1.1_1564003549701_0.20301168907471645"}, "_hasShrinkwrap": false}, "1.2.0": {"name": "functions-have-names", "version": "1.2.0", "description": "Does this JS environment support the `name` property on functions?", "main": "index.js", "scripts": {"version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "lint": "eslint .", "pretest": "npm run lint", "tests-only": "node test", "test": "npm run tests-only", "posttest": "npx aud"}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/functions-have-names.git"}, "keywords": ["function", "name", "es5", "names", "functions", "ie"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/functions-have-names/issues"}, "homepage": "https://github.com/ljharb/functions-have-names#readme", "devDependencies": {"@ljharb/eslint-config": "^14.1.0", "auto-changelog": "^1.16.1", "eslint": "^6.5.1", "tape": "^4.11.0"}, "auto-changelog": {"output": "CHANGELOG.md", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "gitHead": "bd0dfeaaeac821d7d35c48d5401f3fe035ad3bf0", "_id": "functions-have-names@1.2.0", "_nodeVersion": "12.12.0", "_npmVersion": "6.11.3", "dist": {"integrity": "sha512-zKXyzksTeaCSw5wIX79iCA40YAa6CJMJgNg9wdkU/ERBrIdPSimPICYiLp65lRbSBqtiHql/HZfS2DyI/AH6tQ==", "shasum": "83da7583e4ea0c9ac5ff530f73394b033e0bf77d", "tarball": "https://registry.npmjs.org/functions-have-names/-/functions-have-names-1.2.0.tgz", "fileCount": 10, "unpackedSize": 18091, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdrVZ/CRA9TVsSAnZWagAArUcP/A+DA/ROHvpnW+dTdJQq\nsY2bIE01kKP8RV/wFnY3/grRotmKycWhhGhQDlbardC6DI3iOWYhHKK4dspU\nWjkCjGEZLtqj7KrMNNuoZx9FbH+Um3TP7xkuyo1rw8vgPtT4tmHH8J3oJ5EJ\nOb8kp5GnxM0tsnTWznwCdzcMcFWWtVVrD+JQFiDk44S40/v5HLdf1Q4sJMHV\nPHCyhWnvk6G4NauXTbpJO3BWy6B3klQ2k/Nqd33XMAQpROnj+iykt+j60Ri2\n6B4gd82uCyhBhxoL7Vm33jZu+/rrdzzuSO1JJoLbdsduxHg9oQC5eq49bKhe\nVbZGx16UzyuPu75NPuVNSPk9wmECKtPEx8pjKAocgVTtpYHSgdb1k0l7aixT\nwpApOych1DkZ+POqE8MesROw/hljiwYTX4bFzCc4cRv819wScmlau7vCUsl0\ntiwEc4x5sClDBhfWTsX0yV35dZbAhppWFpysMDD7R1UWphPE7KbSyNwTPabC\naXh9Ql23b/waKOeF4DLs8tkuHVJYR1nZ3vODvx4I3L1F2oH+mna4W6ihyL//\n1bQiTUpMx17cpgc7Lzwn0jFWiWL+/FRnWaAIPC3moB5v0g+2htLaqptA5/8K\nji91iRIE4hJmEy6XjV9upiOKNgZvPDqhPguY6qQugtXPMQjgTHye2QdjukI9\nNDCq\r\n=3yDh\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDHGSaJxBEODvRxKwITYTyF+/s2URjcvkFTUsKaPFFlsAIhANqlzCbLqnsFBVj67vX+Ju8A7ekLbn8JPawb/6uAxHil"}]}, "maintainers": [{"email": "<EMAIL>", "name": "lj<PERSON>b"}, {"email": "<EMAIL>", "name": "substack"}], "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/functions-have-names_1.2.0_1571640958974_0.3953173595085666"}, "_hasShrinkwrap": false}, "1.2.1": {"name": "functions-have-names", "version": "1.2.1", "description": "Does this JS environment support the `name` property on functions?", "main": "index.js", "scripts": {"prepublish": "safe-publish-latest", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "lint": "eslint .", "pretest": "npm run lint", "tests-only": "node test", "test": "npm run tests-only", "posttest": "npx aud"}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/functions-have-names.git"}, "keywords": ["function", "name", "es5", "names", "functions", "ie"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/functions-have-names/issues"}, "homepage": "https://github.com/ljharb/functions-have-names#readme", "devDependencies": {"@ljharb/eslint-config": "^15.1.0", "auto-changelog": "^1.16.2", "eslint": "^6.8.0", "safe-publish-latest": "^1.1.4", "tape": "^5.0.0-next.4"}, "auto-changelog": {"output": "CHANGELOG.md", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "gitHead": "3c35e021f8228fc802f78384ff70a5395bb042d4", "_id": "functions-have-names@1.2.1", "_nodeVersion": "13.6.0", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-j48B/ZI7VKs3sgeI2cZp7WXWmZXu7Iq5pl5/vptV5N2mq+DGFuS/ulaDjtaoLpYzuD6u8UgrUKHfgo7fDTSiBA==", "shasum": "a981ac397fa0c9964551402cdc5533d7a4d52f91", "tarball": "https://registry.npmjs.org/functions-have-names/-/functions-have-names-1.2.1.tgz", "fileCount": 10, "unpackedSize": 10392, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJSKeCRA9TVsSAnZWagAAYYMP/jqu0HmXyRLSVI9wb9gt\n4296rCCU/NYuWN8pG1rtwpDGVrermclU4jP0vfIAmML07hTZcxHHZN1zTVfY\nImvrrGjo/osKqDZl1lJHMEvqxS6+D+m6jjatQl8rda9HfbMqmYMrvRi9p/wu\n1+V6OW3lWq6pIFJcMJ29jhtILsiQ6px8nPbou8Nk8KYDNwEdDvSy7Nlzia5U\nkA26Y/AV+bvXawxU+9ipapH+7aJzKFQ8PH+aCfui/zP2EC9tjVVT+lqPxCod\njESMd7rlijqZSI/nqSOeNjugxreqqrNktmAoSPSpcs3O/9jqvO4RrKzkaYCZ\nRNHMPCXAJgihE6x4YLUdLP4JJuM2YnC1QhpjHGTzxW0lAqEeS49+8F2NR0ZF\n1l46fx8IIJbnGNpu/lKVE8LgYrhxxraFp7GPTbHwgYHfGwS5f1N5V5PaSmpb\nZTOl3CfrswTHthUMwG+C43YUtZSwbJ5KeRDcbfnvf/C1jPHYlpKRBBzXjj0J\niKTV8roj1mk1DnXWim6sQ/yU+9HPLsRIn/krHnaLOqACYoRFzaFcuVCoEnr7\nb+2sVzgh3rpT3tHDKrXWD0Uo2H8COesjIEf/fcCUYx3pRy3VfjmCETqbijAz\nG/q31JY/kCN5EsGpQKuE3m9/haMktiwheoXwoZB+IWIRBVwQZcMXzB+K7b3A\nNlqE\r\n=k93F\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQClj0I6kv5trBorY7GxnRSPsqJqYwhJ9CGwiaJd76ZJqgIhAPCNUOYMeJXv/7jyuU+DMpwgGI5UWRr5w71rW0YdNTO0"}]}, "maintainers": [{"email": "<EMAIL>", "name": "lj<PERSON>b"}], "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/functions-have-names_1.2.1_1579491998400_0.998646132034126"}, "_hasShrinkwrap": false}, "1.2.2": {"name": "functions-have-names", "version": "1.2.2", "description": "Does this JS environment support the `name` property on functions?", "main": "index.js", "scripts": {"prepublish": "safe-publish-latest", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "lint": "eslint --ext=js,mjs .", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "aud --production"}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/functions-have-names.git"}, "keywords": ["function", "name", "es5", "names", "functions", "ie"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/functions-have-names/issues"}, "homepage": "https://github.com/inspect-js/functions-have-names#readme", "devDependencies": {"@ljharb/eslint-config": "^17.3.0", "aud": "^1.1.3", "auto-changelog": "^2.2.1", "eslint": "^7.15.0", "nyc": "^10.3.2", "safe-publish-latest": "^1.1.4", "tape": "^5.0.1"}, "auto-changelog": {"output": "CHANGELOG.md", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "gitHead": "4e0c4904d5eb99d7b30b371371c890a3d92de20a", "_id": "functions-have-names@1.2.2", "_nodeVersion": "14.15.1", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-bLgc3asbWdwPbx2mNk2S49kmJCuQeu0nfmaOgbs8WIyzzkw3r4htszdIi9Q9EMezDPTYuJx2wvjZ/EwgAthpnA==", "shasum": "98d93991c39da9361f8e50b337c4f6e41f120e21", "tarball": "https://registry.npmjs.org/functions-have-names/-/functions-have-names-1.2.2.tgz", "fileCount": 11, "unpackedSize": 13768, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf1+kCCRA9TVsSAnZWagAA/gkP/1qaT0ysIF3+ZCGRrLe+\nh6bGCzq96L5F+XgbK+H+W6d1Squb0uIqTdmmgZgFPt4DNeQSxR2WqLXMXG03\npGA7I89JOt8fq9w6frO66XjU38j7YAf4A1TuN8zuB+gWlEuH4+afrOKGHf6f\nv64mZFzCaai84gVupijjSHue1xnW0vLIDor/Zw+BXAVSDB7gU9sUIcjAIQ5i\ndrwdJlGOLZaWCsmorkTAuHPYZCu/DDUzWzTevJ4dIW0F0iSkthZv3O25Sf4B\nv2t0XuhSdk5R+NK7+yK75KXG5XFLisaGEhH6DGkA5tLzDYafkWpCMIcRG5KA\nc2ILvl66HCHCKrXEST3N4ffKFmCWr19wujWu8wiS5cpBjVsfo9BFFYhPj7lU\nau1kdKy8ww6qryM1NtWwl1LPudJpw7+uLIhPkTpV2n2HoGDO5WLXxQ4Du1cO\nrmOYk28zj1aoCDlHL5AevGywEQmSIT0Hm6Ufsv9O2/BensXq9eVpxDld2Gs1\nbFMdUAqEj9zqcIlVfbfWm0CVybrOGHAll7jKwR9gioOdzWbrb1H/fTz6UkkZ\n/fkLZRNP7aGbMfejdAjUJbvrVg11E6gXiAzv8DzuspbKpJjo/bXV4fN8FKHs\n4FwXAZLZmYVlvikb3a6NC6J+lIrWBwwYHSojWI2yFLDyHZOiji43IceozF1e\n0xva\r\n=cD+y\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD8QcKu5eIYNDzzipTaG2uytZMXzqIpEtvBDTp7KeZonQIgdpMZyJkLM0sChy6+KwJZCcFeYYVWsN9XT0JCad+fzMs="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/functions-have-names_1.2.2_1607985409732_0.44451598576801743"}, "_hasShrinkwrap": false}, "1.2.3": {"name": "functions-have-names", "version": "1.2.3", "description": "Does this JS environment support the `name` property on functions?", "main": "index.js", "scripts": {"prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "lint": "eslint --ext=js,mjs .", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "aud --production"}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/functions-have-names.git"}, "keywords": ["function", "name", "es5", "names", "functions", "ie"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/functions-have-names/issues"}, "homepage": "https://github.com/inspect-js/functions-have-names#readme", "devDependencies": {"@ljharb/eslint-config": "^21.0.0", "aud": "^2.0.0", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.5.3"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "gitHead": "3ac881daaf2066bff7c422fd606271f4976efa8f", "_id": "functions-have-names@1.2.3", "_nodeVersion": "17.9.0", "_npmVersion": "8.3.1", "dist": {"integrity": "sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==", "shasum": "0404fe4ee2ba2f607f0e0ec3c80bae994133b834", "tarball": "https://registry.npmjs.org/functions-have-names/-/functions-have-names-1.2.3.tgz", "fileCount": 10, "unpackedSize": 16715, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDnNp/V0tmDq952jUbzEnl3RCpwvV0Aw2N8ZioeAsEDNQIgA2sElx2n7sVxNBIG28fgIJet38VpATyI7ATWHiy8P+k="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiXvUVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoJiA//cgU7E3j5K1NFz7EI4w6CvCRFpQ9CkxU+UN3aolKZBtE3h0cf\r\nUJZ2Sez5kb2mlhBootAYW48pgixbJlGgvkHCUm5cph/gEmwdcnzVN7xY/Dxa\r\nGT1A6A2k/Neaj28lTw975wYJtZ/qrNCX98O99VTLgJjmsUIfh600opi1vUJz\r\nj0cMWrUGmkshphR9WbkXzPkhC9T0vanXh/HKvfKuYJ2bbQeS+IQjblWadp9b\r\nO7Hjk18T3g9nRAt+PmHmv1IAwErHPCKCjLqvO11BlfAggO5xGbCpjhwKaDhJ\r\n++ZL1U5dWVENGt5kjx4GilT6lGKkDYnrOi75E4GIAVvUN0/DdGOkxzrj6Cra\r\nk0/oeLMhtlGrjqQ6eu+Eyqxsy/JCFu+zvzoLtSsuGS5PLo1WuPjSFopM6GF5\r\nErm8hTAFU8hDccjmVI42XWlRVXbcnawA71L2zl4K72HOzO6npK2LT2brCjhv\r\njoo2LU91oG76Ye0fsdMrzTH9boawPQOz1lxvHxK9o5WYoIEhNp4Sa5BWs/Nc\r\naBiP1QOFNKocpxK4h0eqwbcNpTNYMsLjdddFDjX47MOlZwqpUbNX0JRMywlV\r\njhJXnXRITBtojQCrTDpZbIGpkAeJH1tw1k/fTVh15ziouVwG9u+CEV+I18rZ\r\nHHibj0hW+soq+dxDY0a/XmdReE9x91tKBTc=\r\n=HSuE\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/functions-have-names_1.2.3_1650390293261_0.17004319693476"}, "_hasShrinkwrap": false}}, "time": {"created": "2019-07-23T06:30:22.597Z", "1.0.0": "2019-07-23T06:30:22.741Z", "modified": "2023-07-12T19:05:02.835Z", "1.1.0": "2019-07-23T18:02:09.061Z", "1.1.1": "2019-07-24T21:25:50.114Z", "1.2.0": "2019-10-21T06:55:59.086Z", "1.2.1": "2020-01-20T03:46:38.570Z", "1.2.2": "2020-12-14T22:36:49.892Z", "1.2.3": "2022-04-19T17:44:53.457Z"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "description": "Does this JS environment support the `name` property on functions?", "homepage": "https://github.com/inspect-js/functions-have-names#readme", "keywords": ["function", "name", "es5", "names", "functions", "ie"], "repository": {"type": "git", "url": "git+https://github.com/inspect-js/functions-have-names.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/inspect-js/functions-have-names/issues"}, "license": "MIT", "readme": "# functions-have-names <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![dependency status][deps-svg]][deps-url]\n[![dev dependency status][dev-deps-svg]][dev-deps-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\nDoes this JS environment support the `name` property on functions?\n\n## Example\n\n```js\nvar functionsHaveNames = require('functions-have-names');\nvar assert = require('assert');\n\nassert.equal(functionsHaveNames(), true); // will be `false` in IE 6-8\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n[package-url]: https://npmjs.org/package/functions-have-names\n[npm-version-svg]: https://versionbadg.es/inspect-js/functions-have-names.svg\n[deps-svg]: https://david-dm.org/inspect-js/functions-have-names.svg\n[deps-url]: https://david-dm.org/inspect-js/functions-have-names\n[dev-deps-svg]: https://david-dm.org/inspect-js/functions-have-names/dev-status.svg\n[dev-deps-url]: https://david-dm.org/inspect-js/functions-have-names#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/functions-have-names.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/functions-have-names.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/functions-have-names.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=functions-have-names\n[codecov-image]: https://codecov.io/gh/inspect-js/functions-have-names/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/inspect-js/functions-have-names/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/inspect-js/functions-have-names\n[actions-url]: https://github.com/inspect-js/functions-have-names/actions\n", "readmeFilename": "README.md", "users": {"flumpus-dev": true}}