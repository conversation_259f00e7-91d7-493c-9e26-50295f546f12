{"_id": "nan", "_rev": "202-0e34ca8dcc5448dc857d7b534516c3bc", "name": "nan", "dist-tags": {"latest": "2.22.2"}, "versions": {"0.3.0-wip": {"name": "nan", "version": "0.3.0-wip", "license": "MIT", "_id": "nan@0.3.0-wip", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/rvagg/nan/issues"}, "dist": {"shasum": "fd51b1e427db1e5952abbc5eaa053a50bddb795f", "tarball": "https://registry.npmjs.org/nan/-/nan-0.3.0-wip.tgz", "integrity": "sha512-17C3Lhq7MFQBJHet/itebOpFUD8g7h6accAsjoYhzD/1QoTzbCQkPbeoBx4IKfmhu+Mn/97xBjvjsmDdlHSJ/w==", "signatures": [{"sig": "MEUCIQCr7Y0v9rj7PhqCLaAsOih2Nam2m83TEA+QRP/qUD7wWwIgAJh2HSxAIWmPgNsbl9ivZIUM98y0s2Hw3yfitUTI9VQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": ".index.js", "_from": ".", "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rvagg/nan.git", "type": "git"}, "_npmVersion": "1.3.5", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}}, "0.3.0-wip2": {"name": "nan", "version": "0.3.0-wip2", "license": "MIT", "_id": "nan@0.3.0-wip2", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/rvagg/nan/issues"}, "dist": {"shasum": "0455cc096df55927852e524f195b42631317d21c", "tarball": "https://registry.npmjs.org/nan/-/nan-0.3.0-wip2.tgz", "integrity": "sha512-2N9aT3PALI9MBr5bPQWsTa+wb5zwwfJrzwW/L4nCmifHkGgPsOEq7gEkSZerdtyPGcsRc1Zznpj2etgGYuGJ3Q==", "signatures": [{"sig": "MEUCIQDZl2tBHuVarxYwwdf8vSX8eV/zhCuaBZGJBeD65+fG1wIgKhb6pfj6/Ac46OU65BceNpG2g6RjMF+BUyYDFfl3gZg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": ".index.js", "_from": ".", "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rvagg/nan.git", "type": "git"}, "_npmVersion": "1.3.5", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}}, "0.3.0": {"name": "nan", "version": "0.3.0", "license": "MIT", "_id": "nan@0.3.0", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/rvagg/nan/issues"}, "dist": {"shasum": "8c053549c789b3a1c6169f9bf7949750c6117054", "tarball": "https://registry.npmjs.org/nan/-/nan-0.3.0.tgz", "integrity": "sha512-5wrpBO98RPztMyLHutsacK5vMRrN9uuoK/SfLR8vAzKEXwe7FOpv+GNqzixVzJYUvB2qGCPZVRMt+VY0zAFHFA==", "signatures": [{"sig": "MEQCIARHfQAvAIextYJHrG8QsNduwcHuwXoiaFm2AsYgMcEtAiBKHF3spgw+coNAyHndtp80i1N9xZm4fqLeuYiR+JQ0UA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": ".index.js", "_from": ".", "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rvagg/nan.git", "type": "git"}, "_npmVersion": "1.3.8", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}}, "0.3.1": {"name": "nan", "version": "0.3.1", "license": "MIT", "_id": "nan@0.3.1", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/rvagg/nan/issues"}, "dist": {"shasum": "672523b3fbdbc8e73213dc1951b636e98b3b176f", "tarball": "https://registry.npmjs.org/nan/-/nan-0.3.1.tgz", "integrity": "sha512-HvotUkoHKbiUYCV7XSDNaxn+VgDJt7gEGlSWLL2ZWJCNYmb0iQjmxLumGk6IUIbAWbJPTAZdbZN7n9jjA0rA8Q==", "signatures": [{"sig": "MEYCIQD2NTt+ubK4Y39Oh4YQNW5kzPPcihe6IJGgeIMMqiPQaAIhAMs2Zbmq5AI0gvQhNx8tnGdthTgdgPR3YaRSyTm2DprO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": ".index.js", "_from": ".", "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rvagg/nan.git", "type": "git"}, "_npmVersion": "1.3.8", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}}, "0.3.2": {"name": "nan", "version": "0.3.2", "license": "MIT", "_id": "nan@0.3.2", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/rvagg/nan/issues"}, "dist": {"shasum": "0df1935cab15369075ef160ad2894107aa14dc2d", "tarball": "https://registry.npmjs.org/nan/-/nan-0.3.2.tgz", "integrity": "sha512-V9/Pyy5Oelv6vVJP9X+dAzU3IO19j6YXrJnODHxP2h54hTvfFQGahdsQV6Ule/UukiEJk1SkQ/aUyWUm61RBQw==", "signatures": [{"sig": "MEYCIQCAtpDlURCZtbTjvrOoEHdoOwklrrs5RWIU2JY3MlkoPgIhAJUOAo3vRbbaenrdCV+042XKNEOzqLonmBqgiFOG24Y0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": ".index.js", "_from": ".", "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rvagg/nan.git", "type": "git"}, "_npmVersion": "1.3.8", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}}, "0.4.0": {"name": "nan", "version": "0.4.0", "license": "MIT", "_id": "nan@0.4.0", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/rvagg/nan/issues"}, "dist": {"shasum": "c099837dd37983b0fbe8cbc800bf56e07e36acd9", "tarball": "https://registry.npmjs.org/nan/-/nan-0.4.0.tgz", "integrity": "sha512-/U3FZLl4bDKVtYnO+vXAtV7MyPTy49gJHoxNLHQCrn0B6Iw5dHtR0/ZSTgSrgTYrfAFydiHrTmwDqJxKqt3RQA==", "signatures": [{"sig": "MEUCICv5M5HWwJZY8K7WWwyZfX9pmVtB8NvxTH1KZK6hca96AiEAx13HkNz623VEtXHB9mT5l7nUYcdff5RFxgQus5WuOHY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": ".index.js", "_from": ".", "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rvagg/nan.git", "type": "git"}, "_npmVersion": "1.3.8", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}}, "0.4.1": {"name": "nan", "version": "0.4.1", "license": "MIT", "_id": "nan@0.4.1", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/rvagg/nan/issues"}, "dist": {"shasum": "6bca2af7af5ae7f76e9aa96c80897605bcd0407f", "tarball": "https://registry.npmjs.org/nan/-/nan-0.4.1.tgz", "integrity": "sha512-OQF9XmAAqhVnKsZ8HfaW89gT08ZUUFxPRCKkp8gFsi6z5mQs8pseLZ93Q7mPyfP+GzXlChmc4S7gu3mVWLe58Q==", "signatures": [{"sig": "MEQCIGY5Hgt0TQPVnBvcEcjXRUMU8QSMhyiRo6EyIUw4kMTQAiAmZhEP/jjE+WwxowIDt83z7S1wRUmO2NKBGtihct7p6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": ".index.js", "_from": ".", "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rvagg/nan.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}}, "0.4.2": {"name": "nan", "version": "0.4.2", "license": "MIT", "_id": "nan@0.4.2", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/nan", "bugs": {"url": "https://github.com/rvagg/nan/issues"}, "dist": {"shasum": "e23acfd4811f60ca5cf98c06c1fea8682539d351", "tarball": "https://registry.npmjs.org/nan/-/nan-0.4.2.tgz", "integrity": "sha512-KuyWfOr5x+BRA8gWrrIrn2n2/x5lVJ2LFmrMzjuKM0pD344STwJlKjZKHTA4ewsxb+QHmoGVdjqhCkuDGVESTA==", "signatures": [{"sig": "MEQCIDu8HrWjeQCL88ISTy0iNUKWg3WeLSXVuX0LLGnQ/MN+AiBYrHgFNBtFRbiKgWICsk6ZPGdUDbAAUwP4FJDAVuBfFw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": ".index.js", "_from": ".", "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rvagg/nan.git", "type": "git"}, "_npmVersion": "1.3.13", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}}, "0.4.3": {"name": "nan", "version": "0.4.3", "license": "MIT", "_id": "nan@0.4.3", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/rvagg/nan/issues"}, "dist": {"shasum": "6f4274c4fa753be8c562822501a7e925ad2af035", "tarball": "https://registry.npmjs.org/nan/-/nan-0.4.3.tgz", "integrity": "sha512-YOLPRJzkVJxJXQIk+xu8RM0TFTKaIUuy0p1pZxLKJTyI3765IN40eqw4L92Lmz9ttJlbsF5SY3vTtmIDLvnzJQ==", "signatures": [{"sig": "MEUCIEJyS3zEElFnE9k786XAXmcPgLuuPbuPBtOE1HkPdLMbAiEAsR54Sw7X4QI8aZmMvMLBwJo6eT0KW0MN7hHeqEgTYVc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": ".index.js", "_from": ".", "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rvagg/nan.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}}, "0.4.4": {"name": "nan", "version": "0.4.4", "license": "MIT", "_id": "nan@0.4.4", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/nan", "bugs": {"url": "https://github.com/rvagg/nan/issues"}, "dist": {"shasum": "9db58eae63cee119807a0d01afd0655d956bea75", "tarball": "https://registry.npmjs.org/nan/-/nan-0.4.4.tgz", "integrity": "sha512-1BCQoua8F/wV0oNYIfk37hWYx/2A9rwlyQj4+KT/bXz2BuQ4QHU/RauYrNSGIXnLcJcTGVoww/nglxH7Nu59pQ==", "signatures": [{"sig": "MEQCIH0wv5c89kV/QlVZuVRKKksH9ah+G5OF1LtMD0fkSLFIAiAXWD92C5L4C/mSzLANzQpWkKLjVL/+KPS6wvMyGWqSRA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": ".index.js", "_from": ".", "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rvagg/nan.git", "type": "git"}, "_npmVersion": "1.3.13", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}}, "0.5.0": {"name": "nan", "version": "0.5.0", "license": "MIT", "_id": "nan@0.5.0", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/nan", "bugs": {"url": "https://github.com/rvagg/nan/issues"}, "dist": {"shasum": "fff2b55c761245ef11965d03214d8b4836ef8d79", "tarball": "https://registry.npmjs.org/nan/-/nan-0.5.0.tgz", "integrity": "sha512-ZL85zJhkO1GdhRi0Y0tMCodQy51PsAGRETPAd1eIQkBGa3MoRG7YGp8YNCThGV9FF/TE1Oj0zpnb5TWq/NVKSQ==", "signatures": [{"sig": "MEUCIQCy76xE9B5VABsOHchle6Yaj5Ip7dGmrO4SL3Ub+a4AfQIgYXW6EQ+XnrC0aqgVwn+Hp3kbsvqLTNa5XXq8A7cndEc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rvagg/nan.git", "type": "git"}, "_npmVersion": "1.3.14", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}}, "0.5.1": {"name": "nan", "version": "0.5.1", "license": "MIT", "_id": "nan@0.5.1", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/rvagg/nan/issues"}, "dist": {"shasum": "3dbf3b1a8ab63beb34e39210266c269a0c5d2df2", "tarball": "https://registry.npmjs.org/nan/-/nan-0.5.1.tgz", "integrity": "sha512-SUzZC6qWPmL2qs8il8QtjC7K9q8gBfUmeERs2MK1JNmQASNQJysiIJe2Um4brtk+bMKBc1/uRLtvSA/S2HDYgQ==", "signatures": [{"sig": "MEYCIQDUcxetgOvVFRjzmLB5d96uMNywm81zCXxbPQbr1aUlNgIhAPmShaLj1DIjFk/wfj7Lc9eWz3CTh848qPZMuNhJxer3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rvagg/nan.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}}, "0.5.2": {"name": "nan", "version": "0.5.2", "license": "MIT", "_id": "nan@0.5.2", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/nan", "bugs": {"url": "https://github.com/rvagg/nan/issues"}, "dist": {"shasum": "217df903f50b0647d97c5f28f1c5d12cb0803e09", "tarball": "https://registry.npmjs.org/nan/-/nan-0.5.2.tgz", "integrity": "sha512-TNqfIqSy0wLXZC3SdEx94RmHlWqSuL0FRjVU/MPNRmd8MKPEOKxQyHa2mblR5iYFnPBFEwkgk1e6crhwU8Jvbw==", "signatures": [{"sig": "MEYCIQCGyg7AY0j8tEITdxldmFS4XCfBUvLQeQavtxHR/+K6owIhANlOLSJaAVIyOEaXuf9qwsEfqH4/jq+LFQvuMnZLArhs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rvagg/nan.git", "type": "git"}, "_npmVersion": "1.3.14", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}}, "0.6.0": {"name": "nan", "version": "0.6.0", "license": "MIT", "_id": "nan@0.6.0", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/nan", "bugs": {"url": "https://github.com/rvagg/nan/issues"}, "dist": {"shasum": "a54ebe59717b467c77425302bd7c17574c887aaa", "tarball": "https://registry.npmjs.org/nan/-/nan-0.6.0.tgz", "integrity": "sha512-z0w3Bvz7+dTQ4jIxYcbPdfNnpc6Zv3dJzY2mIZm4s5Idki81iaIC5HLj8I9iQpBz+WLgqjv8cVveP3cNabdYog==", "signatures": [{"sig": "MEUCIQCQZjpFdVqg4xADLFD8vC5zDMdPNM6jM8An4bQudNGQegIgcJOHe9+7i1W78cvCY/TD/h/Dxgn1fRaE40TKSWLD9E8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rvagg/nan.git", "type": "git"}, "_npmVersion": "1.3.15", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}}, "0.7.0": {"name": "nan", "version": "0.7.0", "license": "MIT", "_id": "nan@0.7.0", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/rvagg/nan/issues"}, "dist": {"shasum": "3c9cc0a4e021c9cc2b699df1d94944e51c5352b9", "tarball": "https://registry.npmjs.org/nan/-/nan-0.7.0.tgz", "integrity": "sha512-3wn3gA8tsdoYKbf6TlxVPlous0pYOIyNLoA7qxfITzneZyneZukmASNKL7UvrJvWv0ydSF2itbj5J+kyAwQssA==", "signatures": [{"sig": "MEYCIQChNInvTciizKrSAQMY/3uVLTqd2a0eqguz5/3dGrwDDQIhAIohKpvPZAAWjeFa2UpLGhLm8O9+0gG+dE6V35e2M3aD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rvagg/nan.git", "type": "git"}, "_npmVersion": "1.2.30", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}}, "0.7.1": {"name": "nan", "version": "0.7.1", "license": "MIT", "_id": "nan@0.7.1", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/nan", "bugs": {"url": "https://github.com/rvagg/nan/issues"}, "dist": {"shasum": "fc576184a1037b3e5675f2ba3740dee2044ff8f4", "tarball": "https://registry.npmjs.org/nan/-/nan-0.7.1.tgz", "integrity": "sha512-v6yVgq9fQ+32fboF2WKLp+1/ghIY8HLvXRkRGwm560gMcayxn61HvIvMdRehuwEEOrkxSx1IaDklPoSU2a/gLw==", "signatures": [{"sig": "MEYCIQCdgnj83C8i8pqnTuPUYae1eyKzDfVUPvIyUgVFIIKpmwIhAIRcHjFvFgzM/av2VbgvDWvFVYjQR3mW+bU1iWB54B6n", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rvagg/nan.git", "type": "git"}, "_npmVersion": "1.3.21", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}}, "0.8.0": {"name": "nan", "version": "0.8.0", "license": "MIT", "_id": "nan@0.8.0", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/nan", "bugs": {"url": "https://github.com/rvagg/nan/issues"}, "dist": {"shasum": "022a8fa5e9fe8420964ac1fb3dc94e17f449f5fd", "tarball": "https://registry.npmjs.org/nan/-/nan-0.8.0.tgz", "integrity": "sha512-M+vkONtYYOj3IH4em2DsC4PxXtihcZSxHfoQnEIDJqQt7PrWMvWW5YCoAYvHHChffVW5BazqzZrTsa/+Y5cBLw==", "signatures": [{"sig": "MEQCIAs2B/taadSiCuK0dFxa6i1llZibnvdPFAPbGlS8h7TSAiAHTDZqCsHXHY70HwckDxnGOI9MbyI/HqSd0Q1PwfUAWQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rvagg/nan.git", "type": "git"}, "_npmVersion": "1.3.21", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}}, "1.0.0": {"name": "nan", "version": "1.0.0", "license": "MIT", "_id": "nan@1.0.0", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/nan", "bugs": {"url": "https://github.com/rvagg/nan/issues"}, "dist": {"shasum": "ae24f8850818d662fcab5acf7f3b95bfaa2ccf38", "tarball": "https://registry.npmjs.org/nan/-/nan-1.0.0.tgz", "integrity": "sha512-Wm2/nFOm2y9HtJfgOLnctGbfvF23FcQZeyUZqDD8JQG3zO5kXh3MkQKiUaA68mJiVWrOzLFkAV1u6bC8P52DJA==", "signatures": [{"sig": "MEYCIQC243NcRMIJzlJH+DT+t3EjnMqyUH0i1HAa0yqSl65zEwIhAJ802fKi+MUd0PoPVM+ETvDtDMccEOEZEf4Dx8C2GfD1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rvagg/nan.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}}, "1.1.0": {"name": "nan", "version": "1.1.0", "license": "MIT", "_id": "nan@1.1.0", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/nan", "bugs": {"url": "https://github.com/rvagg/nan/issues"}, "dist": {"shasum": "6ca1ab85dc2cd75d6e7d5cbf3d9491bbb97d4aa5", "tarball": "https://registry.npmjs.org/nan/-/nan-1.1.0.tgz", "integrity": "sha512-q6fOeP8iRnaCUn4q3SOBKiQzgoa4WzikjK4wpabXqjSpuMS2G5gh5Ogvf2GAVGuap2msd3CvDwWkI2gL/6anSA==", "signatures": [{"sig": "MEQCIC36WW6aeVnBqKzp2qHqzXA3StlVrKP2STP0J5VmI/XeAiBYCLEW1nG3VMtPIqQvCndBrJJkeHCdYgyguvINDByBIA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rvagg/nan.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}}, "1.1.1": {"name": "nan", "version": "1.1.1", "license": "MIT", "_id": "nan@1.1.1", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/nan", "bugs": {"url": "https://github.com/rvagg/nan/issues"}, "dist": {"shasum": "1057b5526920e77f268d8e5e1d274a6c19251d18", "tarball": "https://registry.npmjs.org/nan/-/nan-1.1.1.tgz", "integrity": "sha512-w2Wry3jicT764kylhUhOzdgmfkhVdHh1iQpkHIGY1Kw5Zu1C7CiTQlwQ4fYwq04mi2hASEcQXzHEQzl1grzROg==", "signatures": [{"sig": "MEUCIExjUEOCgp4lr9dXFNNZt17S0YHWAT2pwi+BNWRKKCumAiEA4Yf1HeomhQxV8oeJUJ/sPrOARqgLJu3kxPUw3yBwvSA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rvagg/nan.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}}, "1.1.2": {"name": "nan", "version": "1.1.2", "license": "MIT", "_id": "nan@1.1.2", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/nan", "bugs": {"url": "https://github.com/rvagg/nan/issues"}, "dist": {"shasum": "bbd48552fc0758673ebe8fada360b60278a6636b", "tarball": "https://registry.npmjs.org/nan/-/nan-1.1.2.tgz", "integrity": "sha512-J7y8XfGMJyLdCesSUmrjwFqK+5Do+TxVthz/lDcfF8olnl3y8pNOw+8yL4mblVSc2DW24n2SyiD736JiVA58Vg==", "signatures": [{"sig": "MEUCIQDpdDufKF7JhOezX/c0FI6W1DT5QtJgeFsPjau62iwViwIgaoYbKvHe73Gxn3wdUwxP/55zPfpKiU91AYKu9yDIbFI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rvagg/nan.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}}, "1.2.0": {"name": "nan", "version": "1.2.0", "license": "MIT", "_id": "nan@1.2.0", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/nan", "bugs": {"url": "https://github.com/rvagg/nan/issues"}, "dist": {"shasum": "9c4d63ce9e4f8e95de2d574e18f7925554a8a8ef", "tarball": "https://registry.npmjs.org/nan/-/nan-1.2.0.tgz", "integrity": "sha512-+iQUm6k6b0RnqPbly89pWp42YzGncSg0hJsonfk0P6q1/kNaT6vWHEAT6fY5ld0BpE4gch5EWDIisCzv+94Gpg==", "signatures": [{"sig": "MEUCIQCrOErBga7zsYphUJ5vZ6i6YqwEPjxNNwXRxBW0uigrYwIgBsH4ninH0XESnHWbJ+HBvgGtvwQHJxqHNtaxFnc9cro=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rvagg/nan.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}}, "1.3.0": {"name": "nan", "version": "1.3.0", "license": "MIT", "_id": "nan@1.3.0", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/nan", "bugs": {"url": "https://github.com/rvagg/nan/issues"}, "dist": {"shasum": "9a5b8d5ef97a10df3050e59b2c362d3baf779742", "tarball": "https://registry.npmjs.org/nan/-/nan-1.3.0.tgz", "integrity": "sha512-2xPpifjI6OzUZtboDKhRlk9u98eV395Twdp0i0GnwP9PLGpphm4R7Q0wIZXmgmu31ukqUJCg1uXlD44fph/sKg==", "signatures": [{"sig": "MEUCIDVyezOwx7G7PMbDvk3IgyKYiS8DrJihO4EPUU1bCWI6AiEAmN1OG9/zSbk9j7thcIL4uG68oSmrYEjjBt3YbmaUe7E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_shasum": "9a5b8d5ef97a10df3050e59b2c362d3baf779742", "gitHead": "e482fbe142e58373d5a24f4e5a60c61e22a13f83", "scripts": {"test": "tap --gc test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --directory test"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rvagg/nan.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}, "devDependencies": {"tap": "~0.4.12", "xtend": "~4.0.0", "bindings": "~1.2.1", "node-gyp": "~1.0.1"}}, "1.4.0": {"name": "nan", "version": "1.4.0", "license": "MIT", "_id": "nan@1.4.0", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/nan", "bugs": {"url": "https://github.com/rvagg/nan/issues"}, "dist": {"shasum": "d49a8e21da02b88f8b175a5300bdfd5e9a5d5362", "tarball": "https://registry.npmjs.org/nan/-/nan-1.4.0.tgz", "integrity": "sha512-oUu05HlDbmapw/rerHAIkjMIMAgvFTOf7qlzcv/bY2mfEk3Oc/M+4HYy5WHIIA1VjGiu1nOcmGPd+6v7JxwfBQ==", "signatures": [{"sig": "MEUCIGGcNv/gFI6xFbe6npm3AjeIP4qDkEopBq4Izc+203zvAiEAps04xJCtJ8NCl4PaiBhJ7tGyGLAI7Kem3nQ58RoUYcs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_shasum": "d49a8e21da02b88f8b175a5300bdfd5e9a5d5362", "gitHead": "7eb51725bc0aae4de3cebe3554df304f36fb074c", "scripts": {"test": "tap --gc test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --directory test"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rvagg/nan.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}, "devDependencies": {"tap": "~0.4.13", "xtend": "~4.0.0", "bindings": "~1.2.1", "node-gyp": "~1.0.2"}}, "1.4.1": {"name": "nan", "version": "1.4.1", "license": "MIT", "_id": "nan@1.4.1", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/nan", "bugs": {"url": "https://github.com/rvagg/nan/issues"}, "dist": {"shasum": "0a2bb562c558b440005b1f7eb8b31ccbdb565d5f", "tarball": "https://registry.npmjs.org/nan/-/nan-1.4.1.tgz", "integrity": "sha512-30AEkPxTer+aJg/qlT/S8sy95Apg8KDXnvMafvTvwW+B21Vn+HJoeOkMFjLdYzbeeOHr2kWYTRrSiWH74+IuGg==", "signatures": [{"sig": "MEUCIQD3cvdUtxCAwyGT+LywtThGXxtuCY08xK4+gHavX3vudQIgB3B8G7OQYmEU+OI2YiKmmuX/Mblt7+3lSzWwr6cxSos=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_shasum": "0a2bb562c558b440005b1f7eb8b31ccbdb565d5f", "scripts": {"test": "tap --gc test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rvagg/nan.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}, "devDependencies": {"tap": "~0.4.13", "xtend": "~4.0.0", "bindings": "~1.2.1", "node-gyp": "~1.0.2"}}, "1.5.0": {"name": "nan", "version": "1.5.0", "license": "MIT", "_id": "nan@1.5.0", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/nan", "bugs": {"url": "https://github.com/rvagg/nan/issues"}, "dist": {"shasum": "2b3c05bc361f52e50aea2c49077783aa67c5b7fb", "tarball": "https://registry.npmjs.org/nan/-/nan-1.5.0.tgz", "integrity": "sha512-BIYJxDTMPpLaZbM3CQumNaK1oLWPgPPdIKFFRAeGgRw8DKzValLmvEtaD3Zh4X4saGusMk1OzIsUHex3MW6bxw==", "signatures": [{"sig": "MEUCIQC/ISEbxONPi5kaU41nQIf4GMCo72mRLJB1nLMM2utiIQIgO4cc2k6vFgrX9Mw+ugfpdbBodutJlczrVpFdSH0Llac=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_shasum": "2b3c05bc361f52e50aea2c49077783aa67c5b7fb", "gitHead": "6fea75acc8f78124756ff4d8e536b6196aca3d37", "scripts": {"test": "tap --gc test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rvagg/nan.git", "type": "git"}, "_npmVersion": "2.1.18", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}, "_nodeVersion": "0.11.14", "devDependencies": {"tap": "~0.4.13", "xtend": "~4.0.0", "bindings": "~1.2.1", "node-gyp": "~1.0.2"}}, "1.4.2": {"name": "nan", "version": "1.4.2", "license": "MIT", "_id": "nan@1.4.2", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/nan", "bugs": {"url": "https://github.com/rvagg/nan/issues"}, "dist": {"shasum": "394c45ae2e3493a35f9adf96d33ee01098ff38d9", "tarball": "https://registry.npmjs.org/nan/-/nan-1.4.2.tgz", "integrity": "sha512-UZzHh3KUotiaSR/5vx7d2Ju83dBxJCUZb4/kOJjFjvoazU+4RaKrR4p93Jc9wMjQXdAHztCFCZhE2OCDlRVfcw==", "signatures": [{"sig": "MEYCIQDwj4Q27ylg3YqQsofQLXOxLW0opnnDz/3KmtZq1MENFAIhALTLR+v7G6jOqccMejzfnAp+neYfkvd66y73r7BPCJ7X", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_shasum": "394c45ae2e3493a35f9adf96d33ee01098ff38d9", "gitHead": "769993a03894c421384564c1ea00a109667aede8", "scripts": {"test": "tap --gc test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --directory test"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rvagg/nan.git", "type": "git"}, "_npmVersion": "2.1.6", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}, "_nodeVersion": "0.10.33", "devDependencies": {"tap": "~0.4.13", "xtend": "~4.0.0", "bindings": "~1.2.1", "node-gyp": "~1.0.2"}}, "1.4.3": {"name": "nan", "version": "1.4.3", "license": "MIT", "_id": "nan@1.4.3", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/nan", "bugs": {"url": "https://github.com/rvagg/nan/issues"}, "dist": {"shasum": "c56b5404698063696f597435f9163c312aea5009", "tarball": "https://registry.npmjs.org/nan/-/nan-1.4.3.tgz", "integrity": "sha512-sZJUTDM4+nDMorRUb2WS0nv5ihun+xqlHhKgKCKi0s4nI/tj5NVHlEm13hEySdZ15iAORPaJ50Gv6yi93bPPIw==", "signatures": [{"sig": "MEUCIDtUlwK1wodZVI60y4ShDSdcHOMtEdOD0b4+Z42RnBboAiEA4GNCanyBnuK3SL8Nv44g2mZ9efZrK2mQd7p5mqv5X4s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_shasum": "c56b5404698063696f597435f9163c312aea5009", "gitHead": "75121a4413c4729170ec97c73c4826600f4a763e", "scripts": {"test": "tap --gc test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --directory test"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rvagg/nan.git", "type": "git"}, "_npmVersion": "2.1.6", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}, "_nodeVersion": "0.10.33", "devDependencies": {"tap": "~0.4.13", "xtend": "~4.0.0", "bindings": "~1.2.1", "node-gyp": "~1.0.2"}}, "1.5.1": {"name": "nan", "version": "1.5.1", "license": "MIT", "_id": "nan@1.5.1", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/nan", "bugs": {"url": "https://github.com/rvagg/nan/issues"}, "dist": {"shasum": "a565e4d4143cb49afdd3fe07e4c8aeaa1e7e0603", "tarball": "https://registry.npmjs.org/nan/-/nan-1.5.1.tgz", "integrity": "sha512-c1qKzRC0PDlKysux3nZ5FIpBi88+k5ob6/HOk/XUMzjBKXpI7749zLmFCEdu/jAaKp8GZjyieaNzLeW4cuYNRg==", "signatures": [{"sig": "MEUCIHPmzesCEtiHbJIyxRrs1CHCA9hfrGZ27EFxx6zUxC/HAiEA2+kSb6tGHxR4YSqkw4cStFWdzpvAywEgne/Z+97AjWc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_shasum": "a565e4d4143cb49afdd3fe07e4c8aeaa1e7e0603", "gitHead": "d05708b0aa6afebe9363b9f34cc5958d2af771f3", "scripts": {"test": "tap --gc test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --directory test"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rvagg/nan.git", "type": "git"}, "_npmVersion": "2.1.6", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}, "_nodeVersion": "0.10.33", "devDependencies": {"tap": "~0.4.13", "xtend": "~4.0.0", "bindings": "~1.2.1", "node-gyp": "~1.0.2"}}, "1.5.2": {"name": "nan", "version": "1.5.2", "license": "MIT", "_id": "nan@1.5.2", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/nan", "bugs": {"url": "https://github.com/rvagg/nan/issues"}, "dist": {"shasum": "17da56116d035c6a25f18e9d6b356d4199744aa8", "tarball": "https://registry.npmjs.org/nan/-/nan-1.5.2.tgz", "integrity": "sha512-cLFab72kuTP9neuPpxJfs1527EDlyf+bwJRqeagLT+x0j9X0003Cj/RDhxw341ZBZWKhJxEfoHaz9UnOR8yTaQ==", "signatures": [{"sig": "MEUCIGmcbiJs4yKKwoa1+GJlEwaRxRyp6P3xzm2U8z4C1C/oAiEA9SJHBqcaJDBGw1qje4PTSupR93MQuDCD24kieZkAjXo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_shasum": "17da56116d035c6a25f18e9d6b356d4199744aa8", "gitHead": "f93a47f06efedd72b37c1a3250040aed496b6e8d", "scripts": {"test": "tap --gc test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rvagg/nan.git", "type": "git"}, "_npmVersion": "2.0.0", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}, "devDependencies": {"tap": "~0.5.0", "xtend": "~4.0.0", "bindings": "~1.2.1", "node-gyp": "~1.0.2"}}, "1.6.0": {"name": "nan", "version": "1.6.0", "license": "MIT", "_id": "nan@1.6.0", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/nan", "bugs": {"url": "https://github.com/rvagg/nan/issues"}, "dist": {"shasum": "a3b14a6608a31d9c2c062ca5f2c5ae77e9399f95", "tarball": "https://registry.npmjs.org/nan/-/nan-1.6.0.tgz", "integrity": "sha512-mc/Rtr01XtAbZAd/fIo30oen92r33amo6X1HVexdrWLVFUHU+Iphz5J+dpQko8m3bKq7kNBDwMm4wWuxEcLbPw==", "signatures": [{"sig": "MEYCIQCiZkoiJxawWCK7j8JPD22wor1UymG7qafr/JneEgvqFQIhAOl0K7uLJAjihAs97rLbkANymrTKaNiOMtND7VsqzoB1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_shasum": "a3b14a6608a31d9c2c062ca5f2c5ae77e9399f95", "gitHead": "e4a76669d7ca47081bbf666434784e9bfbbb633b", "scripts": {"test": "tap --gc test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rvagg/nan.git", "type": "git"}, "_npmVersion": "2.0.0", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}, "devDependencies": {"tap": "~0.5.0", "xtend": "~4.0.0", "bindings": "~1.2.1", "node-gyp": "~1.0.2"}}, "1.5.3": {"name": "nan", "version": "1.5.3", "license": "MIT", "_id": "nan@1.5.3", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/nan", "bugs": {"url": "https://github.com/rvagg/nan/issues"}, "dist": {"shasum": "4cd0ecc133b7b0700a492a646add427ae8a318eb", "tarball": "https://registry.npmjs.org/nan/-/nan-1.5.3.tgz", "integrity": "sha512-bx4zL/LwIZ5vzB0VOSYQ/JEcFoLh3DR5Xr3EcegEdkt3P+8RN2jTcrS+2p6zR7A23twJkHDaajS+9lc7mMpaKQ==", "signatures": [{"sig": "MEYCIQDRtMyAlJIxbSwc4vYHA5IXU8exq2rQfdFLz1hdtnJVTQIhAK0snaSiFlBKjDdJU9635+eZg/sPLU7jzQjQtnvJUVby", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_shasum": "4cd0ecc133b7b0700a492a646add427ae8a318eb", "gitHead": "28ea7e1b769f790c69deaf141b47e4d41e176e8b", "scripts": {"test": "tap --gc test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --directory test"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rvagg/nan.git", "type": "git"}, "_npmVersion": "2.1.18", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}, "_nodeVersion": "1.0.3", "devDependencies": {"tap": "~0.5.0", "xtend": "~4.0.0", "bindings": "~1.2.1", "node-gyp": "~1.0.2"}}, "1.6.1": {"name": "nan", "version": "1.6.1", "license": "MIT", "_id": "nan@1.6.1", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/nan", "bugs": {"url": "https://github.com/rvagg/nan/issues"}, "dist": {"shasum": "69bc50b2d727f3df01145a963f7d2e4da5ff9184", "tarball": "https://registry.npmjs.org/nan/-/nan-1.6.1.tgz", "integrity": "sha512-DnYggEsRq+Y8JLxEHtHAgRUHnINqpMQS6vocnaNPBX4DrkKAUapj0REzIKutr26KyawMu2F5obAGXT8a5o/P5A==", "signatures": [{"sig": "MEYCIQCCWfPTd7CAe43swz1hEm3syw9xKURxFk+jm7JD1nhVLgIhAMO+MAymx9lUL01ifkIW//FVgYsgL2KQdyvfraRoBl+t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_shasum": "69bc50b2d727f3df01145a963f7d2e4da5ff9184", "gitHead": "87c127bba328c8229f2e8e3875b19422888abe5f", "scripts": {"test": "tap --gc test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --directory test"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rvagg/nan.git", "type": "git"}, "_npmVersion": "2.1.18", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}, "_nodeVersion": "1.0.3", "devDependencies": {"tap": "~0.5.0", "xtend": "~4.0.0", "bindings": "~1.2.1", "node-gyp": "~1.0.2"}}, "1.6.2": {"name": "nan", "version": "1.6.2", "license": "MIT", "_id": "nan@1.6.2", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/nan", "bugs": {"url": "https://github.com/rvagg/nan/issues"}, "dist": {"shasum": "2657d1c43b00f1e847e083832285b7d8f5ba8ec8", "tarball": "https://registry.npmjs.org/nan/-/nan-1.6.2.tgz", "integrity": "sha512-2k6Qbb1IebL65KNFFuR6h5jkcMSgn7o+6XXxLCLyWf5K0+/SniVYI7crdNLTE7lYOz/yYN8lP4qIwmjL6tm0qw==", "signatures": [{"sig": "MEUCIQD92aNNXM8X2Z89YXVTtmXZxgSGFg3XakamLRqBqFR6hAIgNIvmGJk0PcrEyfvcYQEjkYfQpDnNoRyGIsu1Jyeq9Vk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_shasum": "2657d1c43b00f1e847e083832285b7d8f5ba8ec8", "gitHead": "ab0e5eed8d4aa36111bf8f44cf75644ece327e98", "scripts": {"test": "tap --gc test/js/*-test.js", "rebuild-tests": "pangyp rebuild --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rvagg/nan.git", "type": "git"}, "_npmVersion": "2.0.0", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}, "devDependencies": {"tap": "~0.5.0", "xtend": "~4.0.0", "pangyp": "~2.0.1", "bindings": "~1.2.1", "node-gyp": "~1.0.2"}}, "1.7.0": {"name": "nan", "version": "1.7.0", "license": "MIT", "_id": "nan@1.7.0", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/nan", "bugs": {"url": "https://github.com/rvagg/nan/issues"}, "dist": {"shasum": "755b997404e83cbe7bc08bc3c5c56291bce87438", "tarball": "https://registry.npmjs.org/nan/-/nan-1.7.0.tgz", "integrity": "sha512-QOnoQzrbpKmKWwa52gS93mGcIupR2MnlLbV66I5ddxscnyyPpy4is7yE2IG/nNOFHb1DbSYR61wYZEv4ukHdlA==", "signatures": [{"sig": "MEUCIH23o1yqp+2PMcRgyyqecwTXbA9qAI7G7AixK8x+zdBhAiEA0ge1lKuZWHD5xQS3TRNJTqkerqtSVMzgj9Kr9HvSl6U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_shasum": "755b997404e83cbe7bc08bc3c5c56291bce87438", "gitHead": "550efb5dde5cb6bf79db87ab48dce850e56e971a", "scripts": {"test": "tap --gc test/js/*-test.js", "rebuild-tests": "pangyp rebuild --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/rvagg/nan.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}, "_nodeVersion": "0.12.0", "devDependencies": {"tap": "~0.5.0", "xtend": "~4.0.0", "pangyp": "~2.0.1", "bindings": "~1.2.1", "node-gyp": "~1.0.2"}}, "1.8.0": {"name": "nan", "version": "1.8.0", "license": "MIT", "_id": "nan@1.8.0", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/iojs/nan#readme", "bugs": {"url": "https://github.com/iojs/nan/issues"}, "dist": {"shasum": "0b46b0463a4b6439f72f5a2143775e69827e02e6", "tarball": "https://registry.npmjs.org/nan/-/nan-1.8.0.tgz", "integrity": "sha512-eh24nMYu6eqpReKEoQt4F6Xe+I2+6NFPEvQAfcAPoBdF72DnIonEjtifcIE0wOkIL4Yezw3wEzKhqmaCKqMpuw==", "signatures": [{"sig": "MEUCIQDgEdSvJsRaLsQZY0pyngvlpSwGWv10oXN8/T5rRZu1ZAIgLwiWb+PtqMKWqiDlEmIjF7xt20or6nO8opWNOTjpZXY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_shasum": "0b46b0463a4b6439f72f5a2143775e69827e02e6", "gitHead": "2aef83e5b6b5f7a59bb0b5a820e55ce934eabbba", "scripts": {"test": "tap --gc test/js/*-test.js", "rebuild-tests": "pangyp rebuild --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/iojs/nan.git", "type": "git"}, "_npmVersion": "2.8.3", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}, "_nodeVersion": "0.12.2", "devDependencies": {"tap": "~0.7.1", "xtend": "~4.0.0", "pangyp": "~2.0.1", "bindings": "~1.2.1", "node-gyp": "~1.0.2"}}, "1.8.1": {"name": "nan", "version": "1.8.1", "license": "MIT", "_id": "nan@1.8.1", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/iojs/nan#readme", "bugs": {"url": "https://github.com/iojs/nan/issues"}, "dist": {"shasum": "6b2f119c88942f7e24f97b0cfde135ff96d4a66d", "tarball": "https://registry.npmjs.org/nan/-/nan-1.8.1.tgz", "integrity": "sha512-t2vBtSOI49G0IZl8C3+iaCjnmXJwvoP+rMSMOnXBb5h/kZ3XmK/Z+a5p3TabnZAXlH3fRrH76Yy6w323R8NA3g==", "signatures": [{"sig": "MEUCIQCCzAbHCR5VLcNLDwFl8gRp1q9TeZt92t/S5mT6KcBQeQIgL0FOzbvLo8pOM7uEL0K6xoaBpHe0mmiYqG/LjUNzszI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_shasum": "6b2f119c88942f7e24f97b0cfde135ff96d4a66d", "gitHead": "2a07f672c08f8dd65aeb35d94df40f6e96126666", "scripts": {"test": "tap --gc test/js/*-test.js", "rebuild-tests": "pangyp rebuild --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/iojs/nan.git", "type": "git"}, "_npmVersion": "2.8.3", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}, "_nodeVersion": "0.12.2", "devDependencies": {"tap": "~0.7.1", "xtend": "~4.0.0", "pangyp": "~2.0.1", "bindings": "~1.2.1", "node-gyp": "~1.0.2"}}, "1.8.2": {"name": "nan", "version": "1.8.2", "license": "MIT", "_id": "nan@1.8.2", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/iojs/nan#readme", "bugs": {"url": "https://github.com/iojs/nan/issues"}, "dist": {"shasum": "131518535fa0c49e91f2d1a776f65bb04577dea0", "tarball": "https://registry.npmjs.org/nan/-/nan-1.8.2.tgz", "integrity": "sha512-C6qK2RVlitnERNJsvBFRx/Mv73quOgzhrzml6Qn4cYvKXOJKbcz7xwPx5JgSjTfbCJeOZHyD2prftJxxtbUyJQ==", "signatures": [{"sig": "MEYCIQC0gupsDZu2eHztt6AlU4wxnh/rJTec7PVrpcp2ITyhDQIhAOIW6sQ/lSY1EkTcwVS51/dxQ05IGysbgM+2DO/8b/BY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_shasum": "131518535fa0c49e91f2d1a776f65bb04577dea0", "gitHead": "76495edfd7b642532e6f9f412403e6e2392df94b", "scripts": {"test": "tap --gc test/js/*-test.js", "rebuild-tests": "pangyp rebuild --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/iojs/nan.git", "type": "git"}, "_npmVersion": "2.8.3", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}, "_nodeVersion": "0.12.2", "devDependencies": {"tap": "~0.7.1", "xtend": "~4.0.0", "pangyp": "~2.0.1", "bindings": "~1.2.1", "node-gyp": "~1.0.2"}}, "1.8.3": {"name": "nan", "version": "1.8.3", "license": "MIT", "_id": "nan@1.8.3", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/iojs/nan#readme", "bugs": {"url": "https://github.com/iojs/nan/issues"}, "dist": {"shasum": "2f4ec4932c7a2250b5ef4b4597fc5e76af021229", "tarball": "https://registry.npmjs.org/nan/-/nan-1.8.3.tgz", "integrity": "sha512-cbNSZMqO5i3u499SXLxUHJvUIzibmxfsWEgCGBEdWvsQ7IsaCxeXIxYe4MzjVA4AutRFrDxie9Ih6sTxmf/OIg==", "signatures": [{"sig": "MEUCIHYT0/IeWJMzN5nLHYQxoJlQMID2yrQq2S0TIqWYuUAYAiEAmZzdFTUdopnYECSXLW/eDwebVTdjC7BcmsaspseOPxQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_shasum": "2f4ec4932c7a2250b5ef4b4597fc5e76af021229", "gitHead": "93784ad3aa9355b515bdd7efaceb67b42140b8f2", "scripts": {"test": "tap --gc test/js/*-test.js", "rebuild-tests": "pangyp rebuild --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/iojs/nan.git", "type": "git"}, "_npmVersion": "2.8.3", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}, "_nodeVersion": "0.12.2", "devDependencies": {"tap": "~0.7.1", "xtend": "~4.0.0", "pangyp": "~2.0.1", "bindings": "~1.2.1", "node-gyp": "~1.0.2"}}, "1.8.4": {"name": "nan", "version": "1.8.4", "license": "MIT", "_id": "nan@1.8.4", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/iojs/nan#readme", "bugs": {"url": "https://github.com/iojs/nan/issues"}, "dist": {"shasum": "3c76b5382eab33e44b758d2813ca9d92e9342f34", "tarball": "https://registry.npmjs.org/nan/-/nan-1.8.4.tgz", "integrity": "sha512-609zQ1h3ApgH/94qmbbEklSrjcYYXCHnsWk4MAojq4OUk3tidhDYhPaMasMFKsZPZ96r4eQA1hbR2W4H7/77XA==", "signatures": [{"sig": "MEUCIGXbVgTXN1c4oe2ipHjcb2N6VUHkLvMr6iRLXh+FN1CDAiEAw85sdPUV6l/5Mqo7iGevQvvlxFESv2ZGcvEPq1YaaD4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_shasum": "3c76b5382eab33e44b758d2813ca9d92e9342f34", "gitHead": "ed3bbf4ced0cf7937b4e4164766797f71aa97f3d", "scripts": {"test": "tap --gc test/js/*-test.js", "rebuild-tests": "pangyp rebuild --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/iojs/nan.git", "type": "git"}, "_npmVersion": "2.8.3", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}, "_nodeVersion": "0.12.2", "devDependencies": {"tap": "~0.7.1", "xtend": "~4.0.0", "pangyp": "~2.0.1", "bindings": "~1.2.1", "node-gyp": "~1.0.2"}}, "1.9.0": {"name": "nan", "version": "1.9.0", "license": "MIT", "_id": "nan@1.9.0", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "1a9cd2755609766f5c291e4194fce39fde286515", "tarball": "https://registry.npmjs.org/nan/-/nan-1.9.0.tgz", "integrity": "sha512-piQCQCuCG3N+cx9/izRvyYZsr7EbTgOxEJACZ5Ag1jhPHT2cCiMKiOARYkRTkkCJOgD6HAhVxuR/m4hp/F54pQ==", "signatures": [{"sig": "MEUCIQCFtweytmNzdUGZ8l9kB1S025RzZVF8qC4BNWD/95wLsAIgNYVyjxiL4kOeMsHO7/W+Kw54Cdr1CuIzgZi6m/011Ck=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_shasum": "1a9cd2755609766f5c291e4194fce39fde286515", "gitHead": "399b3a54ada39a7cf7a11978ea727eae3686666e", "scripts": {"test": "tap --gc test/js/*-test.js", "rebuild-tests": "pangyp rebuild --msvs_version=2013 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "2.13.3", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}, "_nodeVersion": "0.12.2", "devDependencies": {"tap": "~0.7.1", "xtend": "~4.0.0", "pangyp": "~2.2.0", "bindings": "~1.2.1", "node-gyp": "~2.0.2"}}, "2.0.0": {"name": "nan", "version": "2.0.0", "license": "MIT", "_id": "nan@2.0.0", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "bcdd32340befef23a588ea66b2f38902a2b82e42", "tarball": "https://registry.npmjs.org/nan/-/nan-2.0.0.tgz", "integrity": "sha512-UrwEh19bB+HlrWAu20nTDMAKWiwHb6j1HflcLjgkJTxMeMCoPYkCM/o9xEgnZF1jyGB28ku5Pcsv6AGI9mlDCw==", "signatures": [{"sig": "MEQCID8kdvojzo/gA1fqUdPxjKIXe8aayHHDe1V6g0wwRbs6AiBtTlxToeoPvX/9Jf7ArX0mlvNTMvX9AiXV2cU2XhnDpw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_shasum": "bcdd32340befef23a588ea66b2f38902a2b82e42", "gitHead": "4ed41d760313b648b4d212d6ff5374668757be4f", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc test/js/*-test.js", "rebuild-tests": "pangyp rebuild --msvs_version=2013 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "2.13.3", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}, "_nodeVersion": "0.12.2", "devDependencies": {"tap": "~0.7.1", "xtend": "~4.0.0", "pangyp": "~2.2.0", "bindings": "~1.2.1", "node-gyp": "~2.0.2"}}, "2.0.1": {"name": "nan", "version": "2.0.1", "license": "MIT", "_id": "nan@2.0.1", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "acbeda51fbff253fe1438f71c6df758a58a6c30b", "tarball": "https://registry.npmjs.org/nan/-/nan-2.0.1.tgz", "integrity": "sha512-OypWxJ/TT1w2JikvtU+uPbR9KRJYTrB8f0kgMnM/ZDMpi8xXKsD4gSIZqy/F+iLfyHPBDPMuT+iG+zO8XXtu1w==", "signatures": [{"sig": "MEUCIQD/fjFstn4YIOhpEjKf0hFes6of2wzpFWj9wrpQzqsnIQIgGdztD+qYeVc5cPa0AckRTzrMMFw/7cTDCbEP6OZ5vF0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_shasum": "acbeda51fbff253fe1438f71c6df758a58a6c30b", "gitHead": "b8bfceff634d009fbc399bbae4321afecf5f2254", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc test/js/*-test.js", "rebuild-tests": "pangyp rebuild --msvs_version=2013 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "2.11.3", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}, "_nodeVersion": "0.12.7", "devDependencies": {"tap": "~0.7.1", "xtend": "~4.0.0", "pangyp": "~2.2.0", "bindings": "~1.2.1", "node-gyp": "~2.0.2"}}, "2.0.2": {"name": "nan", "version": "2.0.2", "license": "MIT", "_id": "nan@2.0.2", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "326b3076add027caa3878aa68aef18516694d9ec", "tarball": "https://registry.npmjs.org/nan/-/nan-2.0.2.tgz", "integrity": "sha512-InDXitmZQCW/ZzHMqns1XeCSfw+VMfssZiQatuBm2KNBThW75BwP23csnFiY4XTf4cqlTYEBUKEGSHBjE55sVg==", "signatures": [{"sig": "MEUCIQCILUvY+pWc46XKF2Yta2UA4E1A+9uU9CsaucO1peZwAQIgIrRGMhrArlta9gOZtUfn+pfDDWjCUdy/9rBRr+HiMGs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_shasum": "326b3076add027caa3878aa68aef18516694d9ec", "gitHead": "e6fe13be317181468258cf6e1724efcf4482d44a", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc test/js/*-test.js", "rebuild-tests": "pangyp rebuild --msvs_version=2013 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "2.13.3", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}, "_nodeVersion": "3.0.0", "devDependencies": {"tap": "~0.7.1", "xtend": "~4.0.0", "pangyp": "~2.2.0", "bindings": "~1.2.1", "node-gyp": "~2.0.2"}}, "2.0.3": {"name": "nan", "version": "2.0.3", "license": "MIT", "_id": "nan@2.0.3", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "39252b7a399715750b76abc3c4625076a5420a51", "tarball": "https://registry.npmjs.org/nan/-/nan-2.0.3.tgz", "integrity": "sha512-3bw9KEozh0mi08hv79qYTsG3lKRKPV8UA2ziG7h7Wv+k/65uoC7XoIc2MXcbmExzIuQysvJfdRDxCMZRN21AjQ==", "signatures": [{"sig": "MEUCIBN6vAKqXvZcA+PQtIcDPTehQgC2A/F/303EoAbwZynGAiEA1mpNeSRKhygwIS1EGJXTAgLBE1sNwzr/MWUxgka6Jr4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_shasum": "39252b7a399715750b76abc3c4625076a5420a51", "gitHead": "719ba85039fc9fe3ca169c2eade76d250f0f208a", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc test/js/*-test.js", "rebuild-tests": "pangyp rebuild --msvs_version=2013 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "2.13.3", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}, "_nodeVersion": "3.0.0", "devDependencies": {"tap": "~0.7.1", "xtend": "~4.0.0", "pangyp": "~2.2.0", "bindings": "~1.2.1", "node-gyp": "~2.0.2"}}, "2.0.4": {"name": "nan", "version": "2.0.4", "license": "MIT", "_id": "nan@2.0.4", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "f81d4c18aa3c8300dec2f336c52fb827cadfa719", "tarball": "https://registry.npmjs.org/nan/-/nan-2.0.4.tgz", "integrity": "sha512-HMNT7x22EVlHTT1x/+lzYIOI6Ug0FSEttSVHBk8SfVDmKtDraWVsfJRuIWDgpqVc1SNqEdkZuCMAjCBiWz5R6A==", "signatures": [{"sig": "MEUCIQDT7OIxcWyVbscy/EqUsCEmC49mAqmma8ouYlC0IUlgRgIgJC028/nJ+fYhVXCj+W5Q71p/j2rVvVLgcCOufrKCvXk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_shasum": "f81d4c18aa3c8300dec2f336c52fb827cadfa719", "gitHead": "736a7d4f1692591d9256b88ed5484736134bd107", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc test/js/*-test.js", "rebuild-tests": "pangyp rebuild --msvs_version=2013 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "2.13.3", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}, "_nodeVersion": "3.0.0", "devDependencies": {"tap": "~0.7.1", "xtend": "~4.0.0", "pangyp": "~2.2.0", "bindings": "~1.2.1", "node-gyp": "~2.0.2"}}, "2.0.5": {"name": "nan", "version": "2.0.5", "license": "MIT", "_id": "nan@2.0.5", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "365888014be1fd178db0cbfa258edf7b0cb1c408", "tarball": "https://registry.npmjs.org/nan/-/nan-2.0.5.tgz", "integrity": "sha512-NkETl8SYi9UygsNaQNN+ZkEPpb4TY9qEMp9MJvxKFmyl6Lid++oKf0UETvAZ6SFwZ/z1vUOLTic3/CXF5fGM+w==", "signatures": [{"sig": "MEUCIQDPiP1oGEckD5fi5bWS6WgpkDAD6RfgHH9u5LCwz8UnNwIgMTJ9P8Vune4ogOdpNmlvd9OgbS88NbAEIwx0T3kQnHE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_shasum": "365888014be1fd178db0cbfa258edf7b0cb1c408", "gitHead": "d13a2e9ce762fd130877b53c71d35963fa2cf689", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc test/js/*-test.js", "rebuild-tests": "pangyp rebuild --msvs_version=2013 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "2.13.3", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}, "_nodeVersion": "3.0.0", "devDependencies": {"tap": "~0.7.1", "xtend": "~4.0.0", "pangyp": "~2.2.0", "bindings": "~1.2.1", "node-gyp": "~2.0.2"}}, "2.0.6": {"name": "nan", "version": "2.0.6", "license": "MIT", "_id": "nan@2.0.6", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "1bf60f2a4f91da0d426516f136f51725a092c316", "tarball": "https://registry.npmjs.org/nan/-/nan-2.0.6.tgz", "integrity": "sha512-A3Fm4JG5o/+yrzE7C0yIDmqnM3ZHdbAOYushb1fihI+27QAAGKk9k5MUCMBp0vQkApugacQWEHroVTCgK+CSOA==", "signatures": [{"sig": "MEQCIFVLXUOuQ8A8jieftXOp+vsTWb29nF/VuzIEIhc+IpHsAiAPr8iwmU0hBJ5tHXr4XXF9bAh6gjEFBW2t8kg+A+h3rQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_shasum": "1bf60f2a4f91da0d426516f136f51725a092c316", "gitHead": "b6bc21134b818708db918b62d7764ea713f46d52", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc test/js/*-test.js", "rebuild-tests": "pangyp rebuild --msvs_version=2013 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "2.13.3", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}, "_nodeVersion": "3.0.0", "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "pangyp": "~2.2.0", "bindings": "~1.2.1", "node-gyp": "~2.0.2", "commander": "^2.8.1"}}, "2.0.7": {"name": "nan", "version": "2.0.7", "license": "MIT", "_id": "nan@2.0.7", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "c726ce45dbd863b46234e4dfe5bf02d0cb309cd8", "tarball": "https://registry.npmjs.org/nan/-/nan-2.0.7.tgz", "integrity": "sha512-f13apYZUwD9j1SixHsN+7YCLTjCuxihIisoVSD+xZBUdsDbPa7+tbihVEziKzj94bv9ki16eSFk/KTm0slnEzw==", "signatures": [{"sig": "MEQCIAJr5IY9Izi5CmPWD/DgE+yMHi3NFie0Dg/Y+I3faQcVAiAjCWMcI2b1A1mtY948tlzrhTcTjrf+1NgGY1mTR2ShrQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_shasum": "c726ce45dbd863b46234e4dfe5bf02d0cb309cd8", "gitHead": "c68f4bee08ceca279f264903f2b91b54e6e5e168", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc test/js/*-test.js", "rebuild-tests": "pangyp rebuild --msvs_version=2013 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "2.13.3", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}, "_nodeVersion": "3.0.0", "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "pangyp": "~2.2.0", "bindings": "~1.2.1", "node-gyp": "~2.0.2", "commander": "^2.8.1"}}, "2.0.8": {"name": "nan", "version": "2.0.8", "license": "MIT", "_id": "nan@2.0.8", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "c15fd99dd4cc323d1c2f94ac426313680e606392", "tarball": "https://registry.npmjs.org/nan/-/nan-2.0.8.tgz", "integrity": "sha512-8TH9gfaYj3F26VSFkW3njLCMaqmVGK0EQl8SoEOUXAfRtrlyXZjVEiYIvfdtuUYFz/sPedPATvvWBGnJmRPfXg==", "signatures": [{"sig": "MEUCIQDaPGkgFXd+gIMtRgAcAeEEI+ayIPS2UDBPWGMiW2p/NgIgGZHDr9pxOKnM+cZ5pYzoWHYUdJCJsewaLmagkYq+UJQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_shasum": "c15fd99dd4cc323d1c2f94ac426313680e606392", "gitHead": "505803bbd83dbd43c06e179b78b690e52e17d317", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc test/js/*-test.js", "rebuild-tests": "pangyp rebuild --msvs_version=2013 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "2.13.3", "description": "Native Abstractions for Node.js: C++ header for Node 0.8->0.12 compatibility", "directories": {}, "_nodeVersion": "3.2.0", "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "pangyp": "~2.2.0", "bindings": "~1.2.1", "node-gyp": "~2.0.2", "commander": "^2.8.1"}}, "2.0.9": {"name": "nan", "version": "2.0.9", "license": "MIT", "_id": "nan@2.0.9", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "d02a770f46778842cceb94e17cab31ffc7234a05", "tarball": "https://registry.npmjs.org/nan/-/nan-2.0.9.tgz", "integrity": "sha512-n/DwWW228mhTWvHPMbewAgQadftYbZxrvC674Vv/rnpptpLxpOFL65wBrndtm+cpYs06OjqVCM76iIS0PDWfzA==", "signatures": [{"sig": "MEQCIGPrL+okRw5UOIu7WU+/aBmN8N2T7dTZ7ruv7lH7i8vhAiA2wOtXPpg/mLAExc+w0nSrY9h4cq8Wr9qWhG2H85p+nw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": "nan-2.0.9.tgz", "_shasum": "d02a770f46778842cceb94e17cab31ffc7234a05", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc test/js/*-test.js", "rebuild-tests": "pangyp rebuild --msvs_version=2013 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "_resolved": "file:nan-2.0.9.tgz", "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "2.14.2", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 4 compatibility", "directories": {}, "_nodeVersion": "4.0.0", "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "pangyp": "~2.2.0", "bindings": "~1.2.1", "node-gyp": "~2.0.2", "commander": "^2.8.1"}}, "2.1.0": {"name": "nan", "version": "2.1.0", "license": "MIT", "_id": "nan@2.1.0", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "020a7ccedc63fdee85f85967d5607849e74abbe8", "tarball": "https://registry.npmjs.org/nan/-/nan-2.1.0.tgz", "integrity": "sha512-JeVtkKWF38ENQcoPKBtV1FO/Z2FcupFE2NZytXjjWaKYNyy2QXbzBouGAOlQ/vO+as7r4QM5w7ZJrkh3d6uaTg==", "signatures": [{"sig": "MEUCIQCjM2DGJbu8fu2eO9LplUC+yXRlXEmgz3IMEvnn1tXkHwIgFFElSReruvmZyhHGp+F5YAeEV5QHYIC0A72F1bdOdAA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": "nan-2.1.0.tgz", "_shasum": "020a7ccedc63fdee85f85967d5607849e74abbe8", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --msvs_version=2013 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "_resolved": "file:nan-2.1.0.tgz", "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "3.3.5", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 4 compatibility", "directories": {}, "_nodeVersion": "4.1.2", "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "bindings": "~1.2.1", "node-gyp": "~3.0.1", "commander": "^2.8.1"}}, "2.2.0": {"name": "nan", "version": "2.2.0", "license": "MIT", "_id": "nan@2.2.0", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "779c07135629503cf6a7b7e6aab33049b3c3853c", "tarball": "https://registry.npmjs.org/nan/-/nan-2.2.0.tgz", "integrity": "sha512-yvXSs1wSGktjWKsJ4FZz5OEgIx7eJnu5HFq5H1xYegQlBW26xI2mzfNwQINFn7mlE+Z+v0Yh57/rh2ZZLluuvQ==", "signatures": [{"sig": "MEUCIQC+LC/0XpstAL8PubyHwOhlX+KJa0Ci16SlrnZzxGlWcAIgCMkYiij0D+1t0iW7qGUHCfvSsKkR4lOfEzkrXzKzyC4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": "nan-2.2.0.tgz", "_shasum": "779c07135629503cf6a7b7e6aab33049b3c3853c", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --msvs_version=2013 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "_resolved": "file:nan-2.2.0.tgz", "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "3.3.6", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 4 compatibility", "directories": {}, "_nodeVersion": "5.0.0", "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "bindings": "~1.2.1", "node-gyp": "~3.0.1", "commander": "^2.8.1"}}, "2.2.1": {"name": "nan", "version": "2.2.1", "license": "MIT", "_id": "nan@2.2.1", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "d68693f6b34bb41d66bc68b3a4f9defc79d7149b", "tarball": "https://registry.npmjs.org/nan/-/nan-2.2.1.tgz", "integrity": "sha512-FE2BQjWYIvGpoogMsRBxjCtvhpZx/cWgg9xHrsnL/0YIfys4fkCrlzA5Ky12zhnTV+7Fd7dIGVmlodpnITi6nQ==", "signatures": [{"sig": "MEUCIQCeC2W+vlEamnGQoW5x4Hdgubrj9b4SjK+KNFlpNa7LqAIgDf93/L3D1f1INm2aTMMAUbP5gisYvtfxBYHdEBmeSZE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": "nan-2.2.1.tgz", "_shasum": "d68693f6b34bb41d66bc68b3a4f9defc79d7149b", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --msvs_version=2013 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "_resolved": "file:nan-2.2.1.tgz", "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "3.3.6", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 4 compatibility", "directories": {}, "_nodeVersion": "5.0.0", "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "bindings": "~1.2.1", "node-gyp": "~3.0.1", "commander": "^2.8.1"}, "_npmOperationalInternal": {"tmp": "tmp/nan-2.2.1.tgz_1459265439909_0.12396649201400578", "host": "packages-12-west.internal.npmjs.com"}}, "2.3.0": {"name": "nan", "version": "2.3.0", "license": "MIT", "_id": "nan@2.3.0", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "e3e5ce03d1811ca92641d0a77934336473ee66be", "tarball": "https://registry.npmjs.org/nan/-/nan-2.3.0.tgz", "integrity": "sha512-z+EaT4aJa8GL6GKIpKx4hxb0yPJJ2wkmFncsyPXk5wpzPHVstYB7FC/pKU9odzDmOsyEu/Q1a5gTEdNAAubcYw==", "signatures": [{"sig": "MEYCIQDboC8byf0KWnnmEoRpvjPSLiu7Js//V0aZOULLNuZsNwIhAP60RvJlxQqfLkj7H7Sf3GwsE67UoCzHqV6g80m7jyHE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": "nan-2.3.0.tgz", "_shasum": "e3e5ce03d1811ca92641d0a77934336473ee66be", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --msvs_version=2013 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "_resolved": "file:nan-2.3.0.tgz", "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 6 compatibility", "directories": {}, "_nodeVersion": "6.0.0", "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "bindings": "~1.2.1", "node-gyp": "~3.0.1", "commander": "^2.8.1"}, "_npmOperationalInternal": {"tmp": "tmp/nan-2.3.0.tgz_1461751797395_0.23794877855107188", "host": "packages-12-west.internal.npmjs.com"}}, "2.3.1": {"name": "nan", "version": "2.3.1", "license": "MIT", "_id": "nan@2.3.1", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "a4d3e9bfeee09d782d37db161b517221138c2a85", "tarball": "https://registry.npmjs.org/nan/-/nan-2.3.1.tgz", "integrity": "sha512-FuW57NPSHDL8Ew4vmnJZu8DvqFwj5ydq2GhbVU2LbidFfhP9GIiHJ4Q70VS518iOJXbw2dFlhyPo8/W6drkaxA==", "signatures": [{"sig": "MEUCIBpRhuwWm5ZTGBCO9+gE/fGTT5X6Y77U3/Vs7FaoXjBFAiEAgI8ZEnBJVmEn5E3KgD4kDrcxLrAycoTwjIUCnwO6SQw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": "nan-2.3.1.tgz", "_shasum": "a4d3e9bfeee09d782d37db161b517221138c2a85", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --msvs_version=2013 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "_resolved": "file:nan-2.3.1.tgz", "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 6 compatibility", "directories": {}, "_nodeVersion": "6.0.0", "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "bindings": "~1.2.1", "node-gyp": "~3.0.1", "commander": "^2.8.1"}, "_npmOperationalInternal": {"tmp": "tmp/nan-2.3.1.tgz_1461774167709_0.5660416295286268", "host": "packages-12-west.internal.npmjs.com"}}, "2.3.2": {"name": "nan", "version": "2.3.2", "license": "MIT", "_id": "nan@2.3.2", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "4d4ecf17e1da4e989efb4f273d8d00201cad087e", "tarball": "https://registry.npmjs.org/nan/-/nan-2.3.2.tgz", "integrity": "sha512-adcTZ1iaCNtKdDhvf2s06V9fftOgEj8sE4+nYU72zEweGfP3D5X5sAw1t+BVd6wN714pfYBh526lcM5ztjAuEg==", "signatures": [{"sig": "MEQCIDwea3b6+ZCw/RJ4dPzumVs+d6Cyw/w0AAWR/yrW3NoqAiA27Qsi33PzIGF/wiOhRunq2fQZW7KsdMPEwdUrBgpzTQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": "nan-2.3.2.tgz", "_shasum": "4d4ecf17e1da4e989efb4f273d8d00201cad087e", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --msvs_version=2013 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "_resolved": "file:nan-2.3.2.tgz", "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 6 compatibility", "directories": {}, "_nodeVersion": "6.0.0", "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "bindings": "~1.2.1", "node-gyp": "~3.0.1", "commander": "^2.8.1"}, "_npmOperationalInternal": {"tmp": "tmp/nan-2.3.2.tgz_1461778534440_0.04312888509593904", "host": "packages-16-east.internal.npmjs.com"}}, "2.3.3": {"name": "nan", "version": "2.3.3", "license": "MIT", "_id": "nan@2.3.3", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "64dd83c9704a83648b6c72b401f6384bd94ef16f", "tarball": "https://registry.npmjs.org/nan/-/nan-2.3.3.tgz", "integrity": "sha512-Y4Tfsnp29e4tBd5afJ7F31o7H9k5tJiKLhGc6AuFB6OGCbUPg5LUUFkLtN4zxjZmMuCFc4MiBbucd85u+k7vwg==", "signatures": [{"sig": "MEQCIGQQPQJqFpPC29n+TYVIroevrJXS90g8EXs9I/5FdCn2AiAOkJsCagGgvzEaVPZT80/WGDT0GxFO7tonQhzD9FZYIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": "nan-2.3.3.tgz", "_shasum": "64dd83c9704a83648b6c72b401f6384bd94ef16f", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc --stderr test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --msvs_version=2013 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "_resolved": "file:nan-2.3.3.tgz", "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "3.3.6", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 6 compatibility", "directories": {}, "_nodeVersion": "5.0.0", "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "bindings": "~1.2.1", "node-gyp": "~3.0.1", "commander": "^2.8.1"}, "_npmOperationalInternal": {"tmp": "tmp/nan-2.3.3.tgz_1462313618725_0.044748055282980204", "host": "packages-16-east.internal.npmjs.com"}}, "2.3.4": {"name": "nan", "version": "2.3.4", "license": "MIT", "_id": "nan@2.3.4", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "a7d5eb1cb727f8123a2dda6a883c006b30896718", "tarball": "https://registry.npmjs.org/nan/-/nan-2.3.4.tgz", "integrity": "sha512-h9kwT/5LB/PFjb026UiDVJ4D944oKlFeJRGFXK5dC4em262HqfS2O3WS0O4Uft1HblyfIPGV7+qQbuuf6FkOVA==", "signatures": [{"sig": "MEQCIC6T8QrI03Nuvzn8Br0KR6tr2sToJu3hmbJDob3IW629AiAY/On6cRA2IcSInDDBvc6EHH7zf3XjjVwMsUxhWNwsqQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": "nan-2.3.4.tgz", "_shasum": "a7d5eb1cb727f8123a2dda6a883c006b30896718", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc --stderr test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --msvs_version=2013 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "_resolved": "file:nan-2.3.4.tgz", "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "3.3.6", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 6 compatibility", "directories": {}, "_nodeVersion": "5.0.0", "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "bindings": "~1.2.1", "node-gyp": "~3.0.1", "commander": "^2.8.1"}, "_npmOperationalInternal": {"tmp": "tmp/nan-2.3.4.tgz_1464646356651_0.48181944130919874", "host": "packages-16-east.internal.npmjs.com"}}, "2.3.5": {"name": "nan", "version": "2.3.5", "license": "MIT", "_id": "nan@2.3.5", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "822a0dc266290ce4cd3a12282ca3e7e364668a08", "tarball": "https://registry.npmjs.org/nan/-/nan-2.3.5.tgz", "integrity": "sha512-+1vWEe1RBUNgjZJGAXxVDyNmH3TTG8AaLj0Qw5Ye/gqwrpDWn43WNF3/HcHnRpzm+gWqW65oXYQdu6UvBC/+vA==", "signatures": [{"sig": "MEQCIE2IMdI37eQIQt+UCezNf5i6aHWUxfYnjLfRfBhlUS/oAiBa/piF23mPn1TSdEqj9LiUvJtWiFxmVgkoDUoQ5mws3w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": "nan-2.3.5.tgz", "_shasum": "822a0dc266290ce4cd3a12282ca3e7e364668a08", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc --stderr test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --msvs_version=2013 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "_resolved": "file:nan-2.3.5.tgz", "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "3.3.6", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 6 compatibility", "directories": {}, "_nodeVersion": "5.0.0", "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "bindings": "~1.2.1", "node-gyp": "~3.0.1", "commander": "^2.8.1"}, "_npmOperationalInternal": {"tmp": "tmp/nan-2.3.5.tgz_1464707164994_0.4295874561648816", "host": "packages-12-west.internal.npmjs.com"}}, "2.4.0": {"name": "nan", "version": "2.4.0", "license": "MIT", "_id": "nan@2.4.0", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "fb3c59d45fe4effe215f0b890f8adf6eb32d2232", "tarball": "https://registry.npmjs.org/nan/-/nan-2.4.0.tgz", "integrity": "sha512-Ym8Mn5u8D8Fwo7fHWhD7xEyKe/y/J8Epkxp6iJfZhtgnRva+GN+dQddiWGE2cksWCV92K/HzdHlJWo7aZJDlFw==", "signatures": [{"sig": "MEQCIFGxW3SJQ5d2PczElw0rKgCaV2mGqK4g4iqYo04yMcrPAiA8DGTD8YYJrsz1P6R+2bhg5Hl2M7c2xOFJgDfjYVdTmw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": "nan-2.4.0.tgz", "_shasum": "fb3c59d45fe4effe215f0b890f8adf6eb32d2232", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc --stderr test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --msvs_version=2013 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "_resolved": "file:nan-2.4.0.tgz", "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "3.3.6", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 6 compatibility", "directories": {}, "_nodeVersion": "5.0.0", "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "bindings": "~1.2.1", "node-gyp": "~3.0.1", "commander": "^2.8.1", "readable-stream": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/nan-2.4.0.tgz_1468158679820_0.6951719264034182", "host": "packages-16-east.internal.npmjs.com"}}, "2.5.0": {"name": "nan", "version": "2.5.0", "license": "MIT", "_id": "nan@2.5.0", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "aa8f1e34531d807e9e27755b234b4a6ec0c152a8", "tarball": "https://registry.npmjs.org/nan/-/nan-2.5.0.tgz", "integrity": "sha512-mtnOmPhIP3vYfr1bCdvUv/2NYr1boRWckFpR3hAk15R5q2R/tK6wo4bcQZUE/PdTzODHNMr7jmZA1ybvxOBDxg==", "signatures": [{"sig": "MEUCIBHsDn8Uu7uq7/O0Ynk0rRzyUIUG2F8JWhp84XrZfBVMAiEAqrOcgh5BdUM87FxJrYxVe75Y49s736VaOMXTWKloZE4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": "nan-2.5.0.tgz", "_shasum": "aa8f1e34531d807e9e27755b234b4a6ec0c152a8", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc --stderr test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --msvs_version=2013 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "_resolved": "file:nan-2.5.0.tgz", "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 7 compatibility", "directories": {}, "_nodeVersion": "6.3.1", "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "bindings": "~1.2.1", "node-gyp": "~3.0.1", "commander": "^2.8.1", "readable-stream": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/nan-2.5.0.tgz_1482346189010_0.8120697599370033", "host": "packages-12-west.internal.npmjs.com"}}, "2.5.1": {"name": "nan", "version": "2.5.1", "license": "MIT", "_id": "nan@2.5.1", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "d5b01691253326a97a2bbee9e61c55d8d60351e2", "tarball": "https://registry.npmjs.org/nan/-/nan-2.5.1.tgz", "integrity": "sha512-Mvo2RwemW12NRql4qU21+Sdtu8CAfn2RaCp8+p6N+4oQQvAM1DfO9R/ZyJOJaLdsMLHw84WJEo2AKZar4KANXA==", "signatures": [{"sig": "MEUCIFJe6Y+1wv+/MD8uX7Le/7sfiDqC0u7xd57ESpc0+88qAiEA4+wGic6J1G3VwyvO8RYT2zuZRd0OqgRujEAZliONTik=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": "nan-2.5.1.tgz", "_shasum": "d5b01691253326a97a2bbee9e61c55d8d60351e2", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc --stderr test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --msvs_version=2013 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "_resolved": "file:nan-2.5.1.tgz", "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "4.0.5", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 7 compatibility", "directories": {}, "_nodeVersion": "7.4.0", "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "bindings": "~1.2.1", "node-gyp": "~3.0.1", "commander": "^2.8.1", "readable-stream": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/nan-2.5.1.tgz_1485124061146_0.7941144248470664", "host": "packages-18-east.internal.npmjs.com"}}, "2.6.0": {"name": "nan", "version": "2.6.0", "license": "MIT", "_id": "nan@2.6.0", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mkrufky", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "b9b0a907d796d0d336fd73afce24f5e1aa929934", "tarball": "https://registry.npmjs.org/nan/-/nan-2.6.0.tgz", "integrity": "sha512-xLnGUTBKFRJKyurk6WpRyVYW+3V/3c4KMRl7VLApY2TATWJhFtOI7mOAlTKLPZZUzHgcs2BD7APLAo4by/HerQ==", "signatures": [{"sig": "MEQCIHUO4cyiYjgAnur3G86bqdisopD7JtYm9270NaqSTbphAiAJ+Knu4OT5jfx4aFPwVqNAfeM48JNE6zb5RvbAmty1jQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": "nan-2.6.0.tgz", "_shasum": "b9b0a907d796d0d336fd73afce24f5e1aa929934", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc --stderr test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --msvs_version=2013 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "_resolved": "file:nan-2.6.0.tgz", "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 7 compatibility", "directories": {}, "_nodeVersion": "7.5.0", "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "bindings": "~1.2.1", "node-gyp": "~3.0.1", "commander": "^2.8.1", "readable-stream": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/nan-2.6.0.tgz_1491431846889_0.2909555535297841", "host": "packages-18-east.internal.npmjs.com"}}, "2.6.1": {"name": "nan", "version": "2.6.1", "license": "MIT", "_id": "nan@2.6.1", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mkrufky", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "8c84f7b14c96b89f57fbc838012180ec8ca39a01", "tarball": "https://registry.npmjs.org/nan/-/nan-2.6.1.tgz", "integrity": "sha512-MDLQjH7o4LxVY8JyOnhVJfzkb0VKg8wInSzmt/TAIbpkpKwDgtf8BnkyWkjwnws7Jmc8nfm1EhFfwQNIAhMXQA==", "signatures": [{"sig": "MEUCIQDAGnALp9wEHLwl8iSFu22fPEg4NfOQFSbUdgCpnVLGrgIgdvDaLlx6zpKqLuuC6CmTPo1eu3+IG5TkMsEoZ3m4Hvc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_shasum": "8c84f7b14c96b89f57fbc838012180ec8ca39a01", "gitHead": "a80a0e652da1c010bf8aba3c725b667fadb53261", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc --stderr test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --msvs_version=2013 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 7 compatibility", "directories": {}, "_nodeVersion": "6.10.2", "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "bindings": "~1.2.1", "node-gyp": "~3.0.1", "commander": "^2.8.1", "readable-stream": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/nan-2.6.1.tgz_1491457816058_0.0568844648078084", "host": "packages-12-west.internal.npmjs.com"}}, "2.6.2": {"name": "nan", "version": "2.6.2", "license": "MIT", "_id": "nan@2.6.2", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mkrufky", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "e4ff34e6c95fdfb5aecc08de6596f43605a7db45", "tarball": "https://registry.npmjs.org/nan/-/nan-2.6.2.tgz", "integrity": "sha512-K3AyXBwEX6HF3eV46B9YLyyJ66DiBI66v+pTQfz+AyMOdOtg2aH/3WNSDbb3cVDLfPEJmx70aDu60aDTG5x+sw==", "signatures": [{"sig": "MEYCIQDDShK44lwMGbqSrHdgdOUiaWva+BW910Yvuku7/odSpAIhAK1TvGUmBqEHzEA2ufLv8U437fpmAAr+cApgltrnsVPF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": ".", "_shasum": "e4ff34e6c95fdfb5aecc08de6596f43605a7db45", "gitHead": "f0b2f64c1e5317888f2e12fdefb2f105e7018552", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc --stderr test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --msvs_version=2013 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 7 compatibility", "directories": {}, "_nodeVersion": "7.5.0", "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "bindings": "~1.2.1", "node-gyp": "~3.0.1", "commander": "^2.8.1", "readable-stream": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/nan-2.6.2.tgz_1492029516320_0.2352329883724451", "host": "packages-12-west.internal.npmjs.com"}}, "2.7.0": {"name": "nan", "version": "2.7.0", "license": "MIT", "_id": "nan@2.7.0", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mkrufky", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "d95bf721ec877e08db276ed3fc6eb78f9083ad46", "tarball": "https://registry.npmjs.org/nan/-/nan-2.7.0.tgz", "integrity": "sha512-8XxKHG2WLQF/U18y3wviZGtZ+z3pqV4Pni112/qhxbhtxdXeqk17RMHqsEf9JTlT+uUZ3mKSHV9CCFz60zOQtQ==", "signatures": [{"sig": "MEYCIQDjUub775L7BxJn2xQxuhI87qRrBje4bPyYN/A7v7CBNwIhALr/CqsFvRCr+6p6/c2smT01f0VHw2JUUUfx0hiUY7rk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": "nan-2.7.0.tgz", "_shasum": "d95bf721ec877e08db276ed3fc6eb78f9083ad46", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc --stderr test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --msvs_version=2013 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "_resolved": "file:nan-2.7.0.tgz", "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 8 compatibility", "directories": {}, "_nodeVersion": "6.11.0", "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "bindings": "~1.2.1", "node-gyp": "~3.0.1", "commander": "^2.8.1", "readable-stream": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/nan-2.7.0.tgz_1504053769999_0.18853025324642658", "host": "s3://npm-registry-packages"}}, "2.8.0": {"name": "nan", "version": "2.8.0", "license": "MIT", "_id": "nan@2.8.0", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}, {"name": "kkoopa", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mkrufky", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "ed715f3fe9de02b57a5e6252d90a96675e1f085a", "tarball": "https://registry.npmjs.org/nan/-/nan-2.8.0.tgz", "integrity": "sha512-kBTsZNixwSmgVRl1nIVCkQzqRmosFpnY/pLPYo8xC7Mu9ehnKkbrMsM4xb889UafRGLqJ58hKZp+Dn4XVP9Bpg==", "signatures": [{"sig": "MEUCIQCrbS3k54UAbXc380IG1X9E9Z1pCXjXsM8Jobtdttg3kAIgUDa2C3bzCYeDUX3nlwvfDg97e0lNNf0IXzW7xSNPtRQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "include_dirs.js", "_from": "nan-2.8.0.tgz", "_shasum": "ed715f3fe9de02b57a5e6252d90a96675e1f085a", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc --stderr test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --msvs_version=2015 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "_resolved": "file:nan-2.8.0.tgz", "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 9 compatibility", "directories": {}, "_nodeVersion": "6.12.0", "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "request": "=2.81.0", "bindings": "~1.2.1", "node-gyp": "~3.6.2", "commander": "^2.8.1", "readable-stream": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/nan-2.8.0.tgz_1510745663002_0.45465062628500164", "host": "s3://npm-registry-packages"}}, "2.9.1": {"name": "nan", "version": "2.9.1", "license": "MIT", "_id": "nan@2.9.1", "maintainers": [{"name": "kkoopa", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mkrufky", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "af88fcaee5292992c5b755121ceeaa74536fc228", "tarball": "https://registry.npmjs.org/nan/-/nan-2.9.1.tgz", "fileCount": 45, "integrity": "sha512-c609vVPyCEuuzqOjx3hwsSZMXLg5QTzbTfgBmEx6N444ymBt1+Yg/rTGr2+4S3VJ3btXI8m1TZ7nLcYcRTZYuQ==", "signatures": [{"sig": "MEQCIByVXxsSHY36GCgq+f+MjcKbr5W3/Y0sCRXUcM7L1Az6AiAqwY7g33PVCJSTqefGW9QuNCUVgTWLzDgjr9Z7EnsF4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 404355}, "main": "include_dirs.js", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc --stderr test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --msvs_version=2015 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 9 compatibility", "directories": {}, "_nodeVersion": "9.6.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "request": "=2.81.0", "bindings": "~1.2.1", "node-gyp": "~3.6.2", "commander": "^2.8.1", "readable-stream": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/nan_2.9.1_1519319649692_0.4583243317207173", "host": "s3://npm-registry-packages"}}, "2.9.2": {"name": "nan", "version": "2.9.2", "license": "MIT", "_id": "nan@2.9.2", "maintainers": [{"name": "kkoopa", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mkrufky", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "f564d75f5f8f36a6d9456cca7a6c4fe488ab7866", "tarball": "https://registry.npmjs.org/nan/-/nan-2.9.2.tgz", "fileCount": 45, "integrity": "sha512-ltW65co7f3PQWBDbqVvaU1WtFJUsNW7sWWm4HINhbMQIyVyzIeyZ8toX5TC5eeooE6piZoaEh4cZkueSKG3KYw==", "signatures": [{"sig": "MEUCID9JhhX+hAyf0qTkdsMMNQ6D5R/SvAHxldYojIa3iCGeAiEA0uIUolTlFooEU+MQT7FVcTeUe4hpFqxWJAI6lrYl7ZI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 404456}, "main": "include_dirs.js", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc --stderr test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --msvs_version=2015 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 9 compatibility", "directories": {}, "_nodeVersion": "9.6.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "request": "=2.81.0", "bindings": "~1.2.1", "node-gyp": "~3.6.2", "commander": "^2.8.1", "readable-stream": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/nan_2.9.2_1519342457594_0.2577351460235977", "host": "s3://npm-registry-packages"}}, "2.10.0": {"name": "nan", "version": "2.10.0", "license": "MIT", "_id": "nan@2.10.0", "maintainers": [{"name": "kkoopa", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mkrufky", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "96d0cd610ebd58d4b4de9cc0c6828cda99c7548f", "tarball": "https://registry.npmjs.org/nan/-/nan-2.10.0.tgz", "fileCount": 46, "integrity": "sha512-bAdJv7fBLhWC+/Bls0Oza+mvTaNQtP+1RyhhhvD95pgUJz6XM5IzgmxOkItJ9tkoCiplvAnXI1tNmmUD/eScyA==", "signatures": [{"sig": "MEYCIQCTQux7HV5ecI2+3IEIoypVpQgruKqwg6XK7QatFfUD5QIhAPAP+guY3qer8wyhE592oUCCtqJXq8Wh+aXumsQR8QIH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 409953}, "main": "include_dirs.js", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc --stderr test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --msvs_version=2015 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 9 compatibility", "directories": {}, "_nodeVersion": "9.6.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "request": "=2.81.0", "bindings": "~1.2.1", "node-gyp": "~3.6.2", "commander": "^2.8.1", "readable-stream": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/nan_2.10.0_1521216708687_0.31611161513830033", "host": "s3://npm-registry-packages"}}, "2.11.0": {"name": "nan", "version": "2.11.0", "license": "MIT", "_id": "nan@2.11.0", "maintainers": [{"name": "kkoopa", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mkrufky", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "574e360e4d954ab16966ec102c0c049fd961a099", "tarball": "https://registry.npmjs.org/nan/-/nan-2.11.0.tgz", "fileCount": 46, "integrity": "sha512-F4miItu2rGnV2ySkXOQoA8FKz/SR2Q2sWP0sbTxNxz/tuokeC8WxOhPMcwi0qIyGtVn/rrSeLbvVkznqCdwYnw==", "signatures": [{"sig": "MEYCIQCi0S5lKpKLaJ9x+XX09bs5CZe/HckzarLUxMoZLIHcXQIhAKbhvUE1LTbkTmYCQar2b2Rij4eGiSJWzAJcaS9i9UKy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 412174, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgU8BCRA9TVsSAnZWagAAJNIP/2+bZl2y/eC/prNgAOPV\nOOAp8aPI1FAe9jcly9hjQqplKPvIY8XXP5WK6pq+ce1l5vyvHdtcQum977I7\nMLH3MiEj31aIcNrI0/G9LTMqmHMct+qKK4OjyjhupRFuQCHI+BPXGTrQVZ2S\nkUi76QwLOXMnu9rRwmVS+rEjRu+W0TT+sQb4sspBdhG/4qnyp/rp+L2Shdv8\nLlRB4EC7j8N6aosWBrhyVBPX59yliFn0z7mAuFswqdcm4TI3rtTXn1yIDkaW\nyukbkwOBAtL1dKfV7rlmjiS2Retp9FdF3X5D4tVdwbKSd3+5uMYUmz5CsoLI\nbY9uOdjHZGByFfVbMI8qZA6vf0HNZ2/0O7n+f1qGOVFCS4JvjHqgZa6eLjCD\nntdvJmAruNmtIhlXMmq+rUIyA6H4KaaDI2gSggeFVacpuTnFUKW6HN/fGQpB\n2COAPd6cjFJJ47v/YdKBzGZLuUXQ21Je2xxLeGYSd253XSQcu4rPYFTdSSTF\nbuLmG7SObSG/F9hDntzUpCP2hDn5ekmGJwZMRH2h9RuuVMISx3P48Zht6htZ\nTfE8KmOR0ncQtf94jX6KmqFJZjkrZatCCJmTkWWJGjch1/qP/qb1mLjo0hAz\niqq3pAGb2PwF1lRSAAbdqjDIUbGdO1a3NEkvDBH9JtafBBV5ZiEsNBzqhsXs\nEmh8\r\n=CVBp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "include_dirs.js", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc --stderr test/js/*-test.js", "test:worker": "node --experimental-worker test/tap-as-worker.js --gc --stderr test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --msvs_version=2015 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 9 compatibility", "directories": {}, "_nodeVersion": "9.6.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "request": "=2.81.0", "bindings": "~1.2.1", "node-gyp": "~3.6.2", "commander": "^2.8.1", "readable-stream": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/nan_2.11.0_1535201025004_0.7520281082678468", "host": "s3://npm-registry-packages"}}, "2.11.1": {"name": "nan", "version": "2.11.1", "license": "MIT", "_id": "nan@2.11.1", "maintainers": [{"name": "kkoopa", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mkrufky", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "90e22bccb8ca57ea4cd37cc83d3819b52eea6766", "tarball": "https://registry.npmjs.org/nan/-/nan-2.11.1.tgz", "fileCount": 47, "integrity": "sha512-iji6k87OSXa0CcrLl9z+ZiYSuR2o+c0bGuNmXdrhTQTakxytAFsC56SArGYoiHlJlFoHSnvmhpceZJaXkVuOtA==", "signatures": [{"sig": "MEQCICZDeikItDAz1df0uxhkhkw4KnMK1hX1M4koX0KMrhGTAiBR89lnZslEd4UWB/uNanrNkxUDC4Ul+QmAfuGGQCFZ9g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 487268, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbrzNSCRA9TVsSAnZWagAA0vgQAJoa+4F+vH2jgoMAIh2M\nduUOqUMSIKBSqcO1+OzZj8Xi1NTQXKnuxwUpcLfG7kUFSTXMNQ5pqXv4om8/\nViw9to8ZE5wAlK3Uqejk5qaQgVNmxeWtO91R85q3KNI6l1Z4qaRVJN13GwnS\nS1iwDlG/3dKLWAAcoJjP20uCHSvFmEsR+FJy0G5y76s+QSJNFI0TmZRXpowJ\nTezFZsBU/EFRBKKIv//nNvuSxh1/TITrD72sshjpjWdwWqJK0T9emtuBOGFT\nhjsYWGeykfu3HjxBwrzG4hNhyO7nQCl59TRoiPKyqNb70W9RvjJAcldwcMqX\nBddgZ5lZq4+qw+KBGnkoHvO5cS6CQoe3cgFNILGq1NNbPc22Kwp5CVdy1TXv\nC7BcutM20izHrQeBnilZTy+PFBx3eNixWJHFyVDZeGrF47t2jqKvcLEm1eF8\n7pA6/VxXDvE2D+MBjfpAWfSbZ0bR0jhDYvly+1DufutyCd+G6JzR04FsGg1y\nk873NoJ6P9WlZhzvnLBaoWOaQP8XOcxcugSQPP8lTA4hDmQwXY9K8Rwi6Ry2\nw+wGn2WAUQXQhKHJto6dJiIZyJ/yt0h5rjGnwzraXReQQXcTif2dSkvpAgPK\nRXVMRKQIh3us3mU6rSXwg08T4NopJLDLdEuwYYvoKy7xPZLx5uzP/phWLJsb\nG33O\r\n=ig8G\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "include_dirs.js", "gitHead": "85a74a1a724ddffdb81f709b745d5707a8a0f699", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc --stderr test/js/*-test.js", "test:worker": "node --experimental-worker test/tap-as-worker.js --gc --stderr test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --msvs_version=2015 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 10 compatibility", "directories": {}, "_nodeVersion": "9.6.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "request": "=2.81.0", "bindings": "~1.2.1", "node-gyp": "~3.6.2", "commander": "^2.8.1", "readable-stream": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/nan_2.11.1_1538208593407_0.7919453530392533", "host": "s3://npm-registry-packages"}}, "2.12.0": {"name": "nan", "version": "2.12.0", "license": "MIT", "_id": "nan@2.12.0", "maintainers": [{"name": "kkoopa", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mkrufky", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "9d443fdb5e13a20770cc5e602eee59760a685885", "tarball": "https://registry.npmjs.org/nan/-/nan-2.12.0.tgz", "fileCount": 46, "integrity": "sha512-zT5nC0JhbljmyEf+Z456nvm7iO7XgRV2hYxoBtPpnyp+0Q4aCoP6uWNn76v/I6k2kCYNLWqWbwBWQcjsNI/bjw==", "signatures": [{"sig": "MEQCIFXCBrpJ3uPKQH2EbxzEUBsiEtSS0iFmoOgMblKmx6YxAiArmnN/LHsgyCTTHGTmT68STBXktX0xpgsudAiELnJ9oQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 414432, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcFk+qCRA9TVsSAnZWagAAKTYP/ifKNoarxTcvHhn31rYR\nU2Gr9fmUnNnakkjaUwsJofQvqrBJDMzmT5t0gbHSrFRsTr8Jx2WgmIotef84\njyZfNqI09UM2xHXOKqUiZTcQlAQ9VErtHi2PDWr/A1i1qv/3AGPxYRMpbWR1\nTgI2bC1YGAq87470BB41C0BRfEXd2c6DffBYFEgkWoyDiAHrSltNwziENf9B\nV05ovWxdIf3ugIpgSKPYms+ff+lM4G7AcdDmg5zHw1PwmUcMN4hNRFROD+dN\nWGpNzVCyEZ7aIa1iwOcMzRm8s0OtUCo+pxq/nzOB0Oo9jtuEkH/hwSZbw0OL\n/wY57CsIAkbDj8/QBLDfQRjOWJ7pINiaY9smRmZzFkGnr216ZhvYAPaFpzeQ\nV+ioAr8mXnlJVQcoo0EXBave1DWt7ucZm3y9Hc0HeE267sJQMkwDJnGZxFDE\ntSq4RZwT8Kjzl4fDmWWEisIJAoOCYvgzF0sYvCODbMOJ9NQtwt7X8ZzPnrkQ\nBuB8D7HtcsUPgPAglq+XbjxKRTx4HP3HnMWieLNwLrBWhJG6QG25eabMpoYC\nV0DeDFYx6YebK01kmAmnZ8uxx1uBScKD91FwxijkWfwSPakxrX97aocYFXJw\naOFtkc0/vf6p/o3cpXKNwZ5dALAiqU2qPENY6kEwsV6eyX4RzCSKQBKN+cbc\nW3aJ\r\n=bGq5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "include_dirs.js", "gitHead": "e8f8519db5f784b6b581b94d14bfe8df7cba91d2", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc --stderr test/js/*-test.js", "test:worker": "node --experimental-worker test/tap-as-worker.js --gc --stderr test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --msvs_version=2015 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 11 compatibility", "directories": {}, "_nodeVersion": "9.6.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "request": "=2.81.0", "bindings": "~1.2.1", "node-gyp": "~3.6.2", "commander": "^2.8.1", "readable-stream": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/nan_2.12.0_1544966058111_0.3054513371345562", "host": "s3://npm-registry-packages"}}, "2.12.1": {"name": "nan", "version": "2.12.1", "license": "MIT", "_id": "nan@2.12.1", "maintainers": [{"name": "kkoopa", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mkrufky", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "7b1aa193e9aa86057e3c7bbd0ac448e770925552", "tarball": "https://registry.npmjs.org/nan/-/nan-2.12.1.tgz", "fileCount": 46, "integrity": "sha512-JY7V6lRkStKcKTvHO5NVSQRv+RV+FIL5pvDoLiAtSL9pKlC5x9PKQcZDsq7m4FO4d57mkhC6Z+QhAh3Jdk5JFw==", "signatures": [{"sig": "MEUCIAX3ol5lEIx6iTEWfisinCycjSVBMtjX1AqUFZRY/igrAiEA+q1y6GdcG1UiOkpNj0g6fVAprHd8gTiRTdVUp+0v+D4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 414999, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcGWAzCRA9TVsSAnZWagAAkoMQAJgEI+g1PFR6UlfI3nbv\n7HlsOR47Y2VNinIsyoQECyE1QNoaPH9f/1TflublZFh6pOCWyClc6MaFEP2e\nbQf7qkHTJfjviaLSUc0pqzve3DxKVoXbvRANhxBoWipHXkFTvQoPWc3f3LYx\nEvNlyOX9L6nHMaeMNooeD5Fcj5ZtGyc80IWouGTatOVLoQO1n5JAEb9PHuJD\nwgHfZHnkNOYO1YQ/9VeRVQ9K5z8mgcQhcBfKaSJrc9DolihwuvJ+iy4RWx3C\nHWpYzpJzo/xDNtX5LH9sZaezljt/vI1wacGdVGtXBM5GVTkb1pIDhDG5n8Vo\ntUHdVwLL7+sH/kjyurKU9L5spbkAJEgmpS8lerx+tD8HqOtv6R8EqPhADm6F\nI1bpMzoul8ALiUat+MUF81KivuwEJ0ydTED5tKnTno7btU0M2cC8otbXpXvg\nngVSotsAs9IVifZAGHwk0rmTckj1pdxL6WR+4/o+7y4c9xXBI6DNbS/DfuQe\nfSTgg8TJRVLlf/7vAE64L3q16EdCt9h5qdbfgvpK0Ww9fg/3vw9UuIvz/1z2\nMWXOWFVDZDSpFPXf8lanaes9IKdhO3arZJHlRnmFZ/HjCzzG5YHpy8Qsg7wT\n1jo0m/kdnW7onz9aoSZ40D6oGDLdp32QoJH2JFrhVl0fh7hetr8905KMkVpu\nZnpp\r\n=tMc9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "include_dirs.js", "gitHead": "1b77c1186c7f36b17f73256c5b04c496a12adf4b", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc --stderr test/js/*-test.js", "test:worker": "node --experimental-worker test/tap-as-worker.js --gc --stderr test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --msvs_version=2015 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 11 compatibility", "directories": {}, "_nodeVersion": "9.6.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "request": "=2.81.0", "bindings": "~1.2.1", "node-gyp": "~3.6.2", "commander": "^2.8.1", "readable-stream": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/nan_2.12.1_1545166898928_0.20846162122668432", "host": "s3://npm-registry-packages"}}, "2.13.0": {"name": "nan", "version": "2.13.0", "license": "MIT", "_id": "nan@2.13.0", "maintainers": [{"name": "kkoopa", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mkrufky", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "7bdfc27dd3c060c46e60b62c72b74012d1a4cd68", "tarball": "https://registry.npmjs.org/nan/-/nan-2.13.0.tgz", "fileCount": 46, "integrity": "sha512-5DDQvN0luhXdut8SCwzm/ZuAX2W+fwhqNzfq7CZ+OJzQ6NwpcqmIGyLD1R8MEt7BeErzcsI0JLr4pND2pNp2Cw==", "signatures": [{"sig": "MEUCIQCQOKfAu0xo1Olsv5feqEn33J+SsvvNtJe5keMAP9DD2gIgBAwdAT1QWBm1scjem259vAzOl/zgzjcQh53Cql9osNI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 416211, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJciYbLCRA9TVsSAnZWagAADc0QAKNxJUs+1fHRtmFMIAv7\nXb2ObCu8VH6wPDDvhpqr+pFIQIocBsmqNdr8rD7d2WlVxThrmDQ2pkVqQOVC\nGgSjChjFVcUA/6kcFYfbX+OzS8aZirFKdr2a5RDrsK6QvaoAmw0MhMU5u3RN\nnLuvjfnlFQCm6yLfUZSRYKK70EA3n5IbjidWNMuqPvKJ3M4mXa/naSVNGqGA\nCJsUufCaMRl4P120+4+250RS8nJO55PWeHsRIwgudoQyxEc5ItH0QeGw6tNe\nrHv9lM1Yf0AQ8Zlkl0RbvX7vcMTo6wTGWGVCklP7ApBzL90oasoJ4U0TWsGc\nJUcdUKVD04xoVvuXch4YL9UWcmH4R35L3eOHAOknQiv2TqXzi6vdePCdpE1M\nNAuyCEvuvruDMLLUSQJ1ch98mvscKIfZwOOcmSIbdPzY99lPDdWgpkPNamMF\n9eM19nBvuJaKLssjIcpTcKYEERlhiWTZ6kMhc4i/w+3zySO7T6usBYsZjNyT\nEaf6WWrBLrYtRnio7hSAgiIayHmbgritN8ErvP7/SkJYtAp/Zm88elQxtRcE\nivBnVnATCcdXQBtn9O1B8mhJEefIrNVlR90L8nfoo9q5ntthGVysyqgKmReA\noHjmrlK3Gl6eY2gdK8WAA4fYZmxnRtzrci8Y1yrn4mvbBjKsU8CPwZ+WtweA\nLB6a\r\n=S1LC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "include_dirs.js", "gitHead": "24c6357e53f02c5344847e8f64bb02db45ba5627", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc --stderr test/js/*-test.js", "test:worker": "node --experimental-worker test/tap-as-worker.js --gc --stderr test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --msvs_version=2015 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 11 compatibility", "directories": {}, "_nodeVersion": "9.6.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "request": "=2.81.0", "bindings": "~1.2.1", "node-gyp": "~3.6.2", "commander": "^2.8.1", "readable-stream": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/nan_2.13.0_1552516810400_0.9078450704100733", "host": "s3://npm-registry-packages"}}, "2.13.1": {"name": "nan", "version": "2.13.1", "license": "MIT", "_id": "nan@2.13.1", "maintainers": [{"name": "kkoopa", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mkrufky", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "a15bee3790bde247e8f38f1d446edcdaeb05f2dd", "tarball": "https://registry.npmjs.org/nan/-/nan-2.13.1.tgz", "fileCount": 46, "integrity": "sha512-I6YB/YEuDeUZMmhscXKxGgZlFnhsn5y0hgOZBadkzfTRrZBtJDZeg6eQf7PYMIEclwmorTKK8GztsyOUSVBREA==", "signatures": [{"sig": "MEUCIQCPDkQcv631rNNmVlHSh9L4ZhetuAKD8OlIlUBeWtIu5QIgIWCrmL7dwff2uwXZ9HgWqumRAB793BNAn/iiOEEtAlM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 416465, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcis3lCRA9TVsSAnZWagAA99kP/iAWCAc0pkQp7r4B/2f3\n2VQBLO8I5QThewxHroCALastX+4feQXXBreL5nrkuZy0Qki0EfzSZsTaDJz9\nslPnBF9MrT00o2vx6OKhxae9G6n0gZ5Mx+TqxiKdAl9op3RjqRtZHAygheSF\n0HdrtlRAZxplrMH9d/qvuEF20KmT2hjQ/epD90g0bH5hM3g7KzqgLIuZ7V/+\nbz6SEv+bI/l10jo7BYo89tjr12RreCkFy9RbvyqjXDHBCbM9wn9zczWH5WCJ\nh6/n1d9zxnj63xJ5AFYRpWPJ24kndw6jtvR4g3fa+R8gNnhKP5xXpoePtLfZ\nO+0/zANjDQf7qXeAsk6SywbFlAiFE2QftuJqTphOG26hrbWGURsNHsOy2Q94\nU7Q+vy8fehSJ48cHwhTuoc1TLTwKQWH/+daIuaslALmPvWtIi6ZJrf9Jrulp\nCO3GY+JTZmQKBnFvXGhRG6QjdfzGu05D4XTBesqVj+MVf7weXs548NPUitIg\n/qwjADZRrlCNijCC39JSgGuDf2xJzqg/APk5Mmy13piX4m2zyc2Q27fagPgb\nzcNYj5OZLHnotgbBBaukJOBob/n0+/OzUVBdLItwMNPE7IfJNo1+4gdVYG1+\n4rzVugjlQg5Nne0Ifts1vzDVUJ1A6DoxAeEz9Zn7ghTiDgQXSW550ziDDsCY\nWEfj\r\n=Qy+m\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "include_dirs.js", "gitHead": "19f08c2394b19a5dbe854020fed0446606d68955", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc --stderr test/js/*-test.js", "test:worker": "node --experimental-worker test/tap-as-worker.js --gc --stderr test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --msvs_version=2015 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 11 compatibility", "directories": {}, "_nodeVersion": "9.6.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "request": "=2.81.0", "bindings": "~1.2.1", "node-gyp": "~3.6.2", "commander": "^2.8.1", "readable-stream": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/nan_2.13.1_1552600548761_0.5425426860056854", "host": "s3://npm-registry-packages"}}, "2.13.2": {"name": "nan", "version": "2.13.2", "license": "MIT", "_id": "nan@2.13.2", "maintainers": [{"name": "kkoopa", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mkrufky", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "f51dc7ae66ba7d5d55e1e6d4d8092e802c9aefe7", "tarball": "https://registry.npmjs.org/nan/-/nan-2.13.2.tgz", "fileCount": 46, "integrity": "sha512-TghvYc72wlMGMVMluVo9WRJc0mB8KxxF/gZ4YYFy7V2ZQX9l7rgbPg7vjS9mt6U5HXODVFVI2bOduCzwOMv/lw==", "signatures": [{"sig": "MEYCIQCkvzukBxO83/GpQX4rxtXl54DJDLXXCit2hG57fumcWwIhAMaFC0FFGwrWtRs3zmHfaUOk59VCjvuW6av11aOAPtcr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 416510, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcl5T0CRA9TVsSAnZWagAAiQ4QAKLRrhcLF7X35CmFE7Q1\n9kSj7Madc0MFOwpqAMjTALGe/zlu5sjAFu6aRRDLhLKOVel7PsX+FuNENv4A\nCTz5VQu8Q7Gx3uCq+1R5RReogxyQzhQR0gpJS2b3tm/nza8cBSLBS5G7F5HN\n5bD0V0b2lTDAaqsfUVRq0ocuXRY5uu79JfCnF4w0I+W4AfUKVIMI1xXjSiJo\n3kTXdMbjDXkCzy6picwLGte0EYMy005XcB4o0U9l1xkvQ/ebn9lsQHD+MGOX\nz0zLyMT+ZwnUD7XMdHmdLYQBRC4pkekHzGhB6lzNietmaIXgQboTkd86AjqQ\niYifuF7yNL3CipBgcdBE5B1gPIx22TfGjSnIFXuYjbtYWIbxkOWU42c5E2hy\nDS3WOkcHNu75ygW4gNcH4QPcHD3foFS3nPl5pShW9u1PpKKSuQiezPk0VMxP\n0hNmP5s3pp82I8cLPhs/qc55NCecGJnu82PM/osXOV9Tn7VBGstIXOKElyV0\nPevcUfnviqUyHwn6itFajsvoTUFLTQSz/YYCeOI1nXgoca/5cwOy2Ta2bYxT\nlEDSWve7kONxW0WRUcYQlz6iz7vrwu+xADX0wEwPImttEUuJ7h7zhU4scGAT\n7x4jTEYIQy5OZOjWrMZPlRyD/PBYq8UCQ3nic21IRc1B81/OVxrJ3Dcw3Pgd\n49k2\r\n=DnIP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "include_dirs.js", "gitHead": "c428b85786c41e4d55c11c02fe04ef1adba80934", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc --stderr test/js/*-test.js", "test:worker": "node --experimental-worker test/tap-as-worker.js --gc --stderr test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --msvs_version=2015 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 11 compatibility", "directories": {}, "_nodeVersion": "9.6.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "request": "=2.81.0", "bindings": "~1.2.1", "node-gyp": "~3.6.2", "commander": "^2.8.1", "readable-stream": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/nan_2.13.2_1553437939841_0.6110006893669775", "host": "s3://npm-registry-packages"}}, "2.14.0": {"name": "nan", "version": "2.14.0", "license": "MIT", "_id": "nan@2.14.0", "maintainers": [{"name": "kkoopa", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mkrufky", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "7818f722027b2459a86f0295d434d1fc2336c52c", "tarball": "https://registry.npmjs.org/nan/-/nan-2.14.0.tgz", "fileCount": 46, "integrity": "sha512-INOFj37C7k3AfaNTtX8RhsTw7qRy7eLET14cROi9+5HAVbbHuIWUHEauBv5qT4Av2tWasiTY1Jw6puUNqRJXQg==", "signatures": [{"sig": "MEUCIQDSSMAvJIIcN1n+b3sd4XvjsQIilxi1gzolaD8zl/vUVwIgA0lqFK4gQUr978FOUhlyA0jvWHkv3ywdAsKVmrrVpzc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 417060, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc3bXpCRA9TVsSAnZWagAApycP/2mhtcraLrh5RRziC2xM\nUKDaEjBIznHn7PqDWecW3HW/CGrUfmuxpDCBlE2fBm2cxF26nB/CV6xoD2W1\nkyu4MkRcKiAvNW0h4a5sTwXey0U6xc8JRBhBTWgfiY2aQoA4fIO5akmbIa/w\nyT6tspFQ0BQWKBrxlSOqfQ/lARU4Dzy2eiKu1aWMffQecgqOPTrlb5/QxFsr\nUmfdePyO2odVrjDiiNJsfkHeMh66ED4n7szrRjwkz7FTkoEF0dMOw/bWJU2X\nEYVziHG7rm9B4Blzv6KtqG4Y9Gty8epOG+itSLQcc088H86TXYXJn6y75+lf\n1Lw5E9lxw5XLfVnE7uzr0NVHhcj/M3RaGzXySi7v2AWMbF6wqxtAdebdNaew\nIpc3+NVj2OjA/79bzzhU76MG0PrYURE8UmVugHZHGp9L1PraD/jWqCT1JMGE\n1qGu2dSlsqoyNe9Y1pTs/kc+I4KLjwDqo14NyQQk8quvkyHrQ66oO5lAPN4U\n5ms7vR+FOZYrtX7jQDTAs9N169N0IxhGwgsUH4zRza2RkkdTzsjyEnPhqlrI\n6tIrfYldrudrGtcNmLJqY7fTUA86L3f7SQvlnpfsDPUFagfwQr4Uz9JE8q1k\nhbe8CqTuhbET2AZNFlBxf0ii3ipwNXXJtB/0hIVQCfud1vbdS1IuOibc2/YY\n6ep0\r\n=AZxH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "include_dirs.js", "gitHead": "1dcc61bd06d84e389bfd5311b2b1492a14c74201", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc --stderr test/js/*-test.js", "test:worker": "node --experimental-worker test/tap-as-worker.js --gc --stderr test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --msvs_version=2015 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 11 compatibility", "directories": {}, "_nodeVersion": "9.6.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "request": "=2.81.0", "bindings": "~1.2.1", "node-gyp": "~3.6.2", "commander": "^2.8.1", "readable-stream": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/nan_2.14.0_1558033896058_0.6879275796293267", "host": "s3://npm-registry-packages"}}, "2.14.1": {"name": "nan", "version": "2.14.1", "license": "MIT", "_id": "nan@2.14.1", "maintainers": [{"name": "kkoopa", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mkrufky", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "d7be34dfa3105b91494c3147089315eff8874b01", "tarball": "https://registry.npmjs.org/nan/-/nan-2.14.1.tgz", "fileCount": 46, "integrity": "sha512-isWHgVjnFjh2x2yuJ/tj3JbwoHu3UC2dX5G/88Cm24yB6YopVgxvBObDY7n5xW6ExmFhJpSEQqFPvq9zaXc8Jw==", "signatures": [{"sig": "MEUCIQCoOvFkC2M8ctpo9ZpebIfzJU9cqph+G4Yjmiq38noC0AIgJEIxk3mL5YEGXCDTINAzi+AmKwa+H6gM18vVRC2M8g4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 417637, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJen0KtCRA9TVsSAnZWagAAEJAP/AuPED/luXpLqjqUtyRc\nm1zYL5L3e5XyxkCZ+/Agc1gi/p1SG0zFo5XrgabTVcPsyZ2RwAyB+hUCDlC7\nHRp/VbfR/lvEGvcznzsjm4pcn+8oIM5vR+RM035LR+RsvZ+TxBVc4P6Oe9vT\njwsPquNy/PJseIygzNvteINOr5iGUVVOq61pkqzmUP3WrydxVYY6kLn1y+F2\nM1poBixx4NOHdhTuv65B7N7hK7A5YDVZhetGEA8tDEF4KCOB2vgx/kWdaz2k\nA/r94QRILObXIFriUM0CNI5p/gBEIEJ9art5U7eiQwvDJXKmAkSR2nSsJMf7\nusH4+Ot40dVCUbLw/kGYpPAJ+BTdPpFqmW5UFkheCaq/Neihkioj6RDJ8eco\n8Oh4xVPLH9/cT1IhvYlKRmuWyPKU57y9saEvB1PWcUecp/fkc1Aonptak6dK\n87DAk9+85S/mwhQExlwc9SSBsPXIyY6UjSmwfEUPuY9BlD8SmLasdkIPFC8V\nEcjzebcVo8kd5nOz/9PrwZE5Gkfs4B5SpJTK92ZLYOIEcht2l4CvglELBxbj\n5Hd+scHKmWvHyrShBkRie2BDQXzroDbGYqoKHuzAOgv075APk9yvEYOnqP/4\nzz68DIGbYgNwPCLEEO4q34fdX/1SHtZ+Yb+sN+AqULIVHjmzAKKkcDgsLNmK\nqOt2\r\n=EVf+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "include_dirs.js", "gitHead": "eaae2683a3df542376797a11385b87b9c0d071c5", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc --stderr test/js/*-test.js", "test:worker": "node --experimental-worker test/tap-as-worker.js --gc --stderr test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --msvs_version=2015 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 14 compatibility", "directories": {}, "_nodeVersion": "9.6.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "request": "=2.81.0", "bindings": "~1.2.1", "node-gyp": "~3.6.2", "commander": "^2.8.1", "readable-stream": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/nan_2.14.1_1587495596751_0.585087272794657", "host": "s3://npm-registry-packages"}}, "2.14.2": {"name": "nan", "version": "2.14.2", "license": "MIT", "_id": "nan@2.14.2", "maintainers": [{"name": "kkoopa", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mkrufky", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "f5376400695168f4cc694ac9393d0c9585eeea19", "tarball": "https://registry.npmjs.org/nan/-/nan-2.14.2.tgz", "fileCount": 46, "integrity": "sha512-M2ufzIiINKCuDfBSAUr1vWQ+vuVcA9kqx8JJUsbQi6yf1uGRyb7HfpdfUr5qLXf3B/t8dPvcjhKMmlfnP47EzQ==", "signatures": [{"sig": "MEQCH2IwkZaE4CjWRLr9h/NQcFR7mOEhBltEN96Kcb/3wCACIQCmQIC5xSlm4KD823GQes29Mai0YvI9VtKD691mpFhn8g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 417991, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfhaJzCRA9TVsSAnZWagAAqa0P/33rKLxI1mx53dz2Z6/B\nQQAJOUcgZgpIdY7LkaDGw6oTRyYoDg9uq1aOW7uL6OTJGZb/0UH3LS/gs5CK\nUk1CnYitqHSmyzOCO6tZyQmiFqYqLJf5drQn+uV9sU3bi2+u42Z03vTv5v+j\nEwRLH3TfeMbrEOgDdBuo6RB+jgaZHKyEXsvCe6NXXAaAQ8as82vtDOA1FOzq\nhB91RTYgS57lF3g/ciWg/Bvb7o2Fwep7+Ah/22Cpz9UC8fm8QGz4/lQRba3m\n504ah7HXRoF79+HqvntRKF0j8fJ8fpX3cu7wQ5UND8bj/VhhNpKZtM/tCv98\nq00KMaWYNqD+NJYKULLS658iJVjpPNr9UElb3+jNDCoX+UEvdOckuJKwo5u0\n7HRmqpE6Pb0MGfVqhHK6K3buwg+eccMcCKPiHHaiZvqa/jD0savGNx2ylMVD\niTpyl8XV+ZU9vfR1tJ3OFYzOGfXOGG5LqOut/hT0qjBLTWXL3ajPcxCNdJRH\nWSrDJ7Y3da/KkyMdX37t39Q6JkQbD2hz2nGNIr1pe36CNqx8FMOvnHG2Gwuy\nGFdcABFLpkppKhHmRebaRlQXJ416KHzxUkD+A/5DcUb7ChoJgn4j6TN7ewVb\nNOb7UllppKKHOseXgre8uJM1vVslNLVsBYzfgv58uhSTbMvo5QBuK/QHKilw\nRoBa\r\n=4oJR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "include_dirs.js", "gitHead": "7c3fc6884666bd19b597ea58a99f51f2f4860c4e", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc --stderr test/js/*-test.js", "test:worker": "node --experimental-worker test/tap-as-worker.js --gc --stderr test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --msvs_version=2015 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 14 compatibility", "directories": {}, "_nodeVersion": "9.6.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "request": "=2.81.0", "bindings": "~1.2.1", "node-gyp": "~3.6.2", "commander": "^2.8.1", "readable-stream": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/nan_2.14.2_1602593394727_0.8742800087816833", "host": "s3://npm-registry-packages"}}, "2.15.0": {"name": "nan", "version": "2.15.0", "license": "MIT", "_id": "nan@2.15.0", "maintainers": [{"name": "kkoopa", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mkrufky", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "3f34a473ff18e15c1b5626b62903b5ad6e665fee", "tarball": "https://registry.npmjs.org/nan/-/nan-2.15.0.tgz", "fileCount": 47, "integrity": "sha512-8ZtvEnA2c5aYCZYd1cvgdnU6cqwixRoYg70xPLWUws5ORTa/lnw+u4amixRS/Ac5U5mQVgp9pnlSUnbNWFaWZQ==", "signatures": [{"sig": "MEQCIB2Zc9H0OIp1szE6mdB6aBU4unglZt6zMe9AUrwhXB9uAiBPiZEOczJ/8FY427YXHTNy0Cjy424gNT/ECzMUG5FIcw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 422231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhCryPCRA9TVsSAnZWagAAcu4P/Am7dSL+2F1Onhq/bVKA\nbbPw7Na1fM2siWCblsXDxyDR/MKEApsFQFoBUH34AtN/0LAe/TdcHYTNmRe6\nsMj8Ut+G+bbod4QA4xgsWIIBuPgqDuFVNQOKl8VeOI3nmRmfa0dq/J3SBLon\nAmWKpMAMKTO7n9UOfqzK2F3Twnd0S1x0IuOWxCl8yQaAojdGmgt6jBmDnzzn\ndcrPg647/tfG4oMzBLGPw466XKn8Vyl7v4MxGeORirVEXpAELA0F2K+fIUpL\nWDm/9x7gF9Y+X4UCuHPWxNndJTwWeJRvlBrobXWsGUPBJc2M2PZMy1C2gUQC\nRH0Yiujeo7dakze+e7nSfEtTeexzA5wNMVbylpb1Fom6EfKBI4p+3rUi4wZd\nnmNBRfazt+9LWztJxrR65Kt1OcXZFtseplr8ibxx21pSi0YX9AIXKMRmewnp\nuYUVkwXkkSeWczQgE63kaGNsix/OtN2ESEn3JyM74wEp/r+jTZUA+MMAv3SG\nlt/PhchQaiU8I/ZLWSHwICWzYMzEd7PejWJ5Qj7cGHGwhzwCvUeodUFazgE4\nhlVKzR8Uwdc/kpu8EkB+xkqkiDvaKw/6Kd73Lw3ElPeAXXsfz7+g03qZCocX\nGk7oQribCHzsnomfh4Hw/Ik7t+nBbF68GFrb+Gp9NMJAmiTOqmxuDXDhBFJi\nNUKf\r\n=WgvI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "include_dirs.js", "gitHead": "8502c2417b9c1d0ea86c1e0aea6e975d26bf682b", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc --stderr test/js/*-test.js", "test:worker": "node --experimental-worker test/tap-as-worker.js --gc --stderr test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --msvs_version=2015 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 14 compatibility", "directories": {}, "_nodeVersion": "9.6.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "request": "=2.81.0", "bindings": "~1.2.1", "node-gyp": "~3.6.2", "commander": "^2.8.1", "readable-stream": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/nan_2.15.0_1628093583315_0.20149876055617688", "host": "s3://npm-registry-packages"}}, "2.16.0": {"name": "nan", "version": "2.16.0", "license": "MIT", "_id": "nan@2.16.0", "maintainers": [{"name": "kkoopa", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mkrufky", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "664f43e45460fb98faf00edca0bb0d7b8dce7916", "tarball": "https://registry.npmjs.org/nan/-/nan-2.16.0.tgz", "fileCount": 47, "integrity": "sha512-UdAqHyFngu7TfQKsCBgAA6pWDkT8MAO7d0jyOecVhN5354xbLqdn8mV9Tat9gepAupm0bt2DbeaSC8vS52MuFA==", "signatures": [{"sig": "MEQCIHVjXzivYF3hOXU9fxRqSho8nssRbOAd1fdls0zmnkaJAiAZR2VASD82lewv/SBq0EXg4sAr1e7jH1tJyR4BwempZQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 422414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijimrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq1VA/9HTCiBC8rskDfNvzkp7a1wAYpjPIC9Ih4bc1rpy0Os6g9sgsI\r\nSF8Fsxzf8gaUmU5nMTRJ0jnmmMk4O73+ZwXaKaeZHZKrziFKmEcKPedaiDlD\r\n8XFxpnvvki/vg4Mx7bxebI/+OLkJgHxHTGCqHn/WHjq7su/NADmdLlc2AtMW\r\nwgCBHQlwgslTIQHr42quOnqSueNNtT2jSQCIbypH5+ex875bMLvga0qSdV2N\r\nCA+K/7wzpba/KJXxhiJ2pER7mEH3ntDAb7sROziyZ2/qnJi6vMOsO8+0m5gZ\r\nG3dV/1BjGyYW8mySJGekrazQUeQa413RY1fHdSWLNtUiRZQqhA4+yaOCvsx4\r\nznUy1Qi/4d3ZMpFKI5iqw9mvvosVP7i1rISz016zeiqJZSEKaV/sSpUa9xFn\r\nbGLOGmMHnBzSV0W2zG+lK6LknZlyEL173iuSrmQccvWIUNvDFUAsXo71KPbw\r\nn7Mjg7h9MuM+8msw1Vp8ZzzgveJfY5oRF6XaTY2X1GT+mZaFMC++5UTL3NJQ\r\nGJaaQ13HZIFoQ6+CpNDWWkaj1SQ/A2qD//I+cKbaoE40wtzHiXLReXSS3rpO\r\nHAY9whAA8CVSJC2pH/lA9ugBM06euSCfk4knoHhrw/gSXNfV7vBxjkFF/Rzd\r\n8K+KAQOLcl1YXKN3PHQ47jj/NOE0qX7G7pY=\r\n=O0dW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "include_dirs.js", "gitHead": "22c8ac4b6de36a66a1cfef9363d4d7c12db87cd6", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc --stderr test/js/*-test.js", "test:worker": "node --experimental-worker test/tap-as-worker.js --gc --stderr test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --msvs_version=2015 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "8.10.0", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 14 compatibility", "directories": {}, "_nodeVersion": "18.2.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "request": "=2.81.0", "bindings": "~1.2.1", "node-gyp": "~8.4.1", "commander": "^2.8.1", "readable-stream": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/nan_2.16.0_1653483946865_0.982634005374736", "host": "s3://npm-registry-packages"}}, "2.17.0": {"name": "nan", "version": "2.17.0", "license": "MIT", "_id": "nan@2.17.0", "maintainers": [{"name": "kkoopa", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mkrufky", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "c0150a2368a182f033e9aa5195ec76ea41a199cb", "tarball": "https://registry.npmjs.org/nan/-/nan-2.17.0.tgz", "fileCount": 47, "integrity": "sha512-2ZTgtl0nJsO0KQCjEpxcIr5D+Yv90plTitZt9JBfQvVJDS5seMl3FOvsh3+9CoYWXf/1l5OaZzzF6nDm4cagaQ==", "signatures": [{"sig": "MEUCICwKS0XDYptFL3HNplaXDZK7FJE2GJn8XDBbHX2SaWHOAiEAjamPYr1w31XwubEuRh1ZxFesoA5UVr3ukzUk+PTKegI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 423681, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRG0dACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqHTg/9Hos40C/iVx+AltNrRoB+BEosgSbP1NKSz/5Zd7yGoFm5piw7\r\n1iVhiWPfr4dmMBYk0WTz2O8EkMEb3NFvHiXgUxbw5gVttmJv8dtK4KLWvDpJ\r\ndB+OkWgSZzPV4hwvcsUvNS+cM0PlJVtEZaUs1qaw1cR3q8F3zksvj549sHON\r\nyXtMl6b/2BMeAvOtDtQk1PlV38zccTEU2o7OQ4jsrKM2FlJsEWBu7h3uCt/V\r\n6VHo2S+AszuXbgpmSAS9uwPHLuuwQ9Cw+GwpaYAZUPasKnl+t+OOWN6jeyOh\r\n2pcu6uAFI8geIqs9cwmrt6zYeJGYkBXoNWd6DEeIMrPiYcEXN7IaAyEiGKia\r\n3leVyvr8cST/41BmngDiE4bJW8gNclHpQJ5VFnnnu4RlxXlv8GtAR9wa3/b7\r\nicp4MzXzu3MxoFZ4NXjAXuur8bvT7KhlO/gS3jw2BrZgSanQdLo1LOhXcQlV\r\nNpFO0K6BjIGBOAQwy5ZNqzatTAHKaV5fMxRROvaU3il6Y6DbNI/FrrivEP2H\r\nLGq0QwoLMjuRRVyA+00M6083YC6tZrkCmcOadqG1fRyxccQzmNtAkYZBl27e\r\n2b2HaWM0bZRzsByimMA/kdHLVXMIlzh5jlSrbsM0EdCGMtIl683O+YJUrBmP\r\nYGrDYiCCXGcbUaorJFlpgy3WsyW5mG4VHaI=\r\n=J6PH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "include_dirs.js", "_from": "file:nan-2.17.0.tgz", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc --stderr test/js/*-test.js", "test:worker": "node --experimental-worker test/tap-as-worker.js --gc --stderr test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --msvs_version=2015 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "_resolved": "/Users/<USER>/dev/nan/nan-2.17.0.tgz", "_integrity": "sha512-2ZTgtl0nJsO0KQCjEpxcIr5D+Yv90plTitZt9JBfQvVJDS5seMl3FOvsh3+9CoYWXf/1l5OaZzzF6nDm4cagaQ==", "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 18 compatibility", "directories": {}, "_nodeVersion": "18.10.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "request": "=2.81.0", "bindings": "~1.2.1", "node-gyp": "~8.4.1", "commander": "^2.8.1", "readable-stream": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/nan_2.17.0_1665428765125_0.7014403304923522", "host": "s3://npm-registry-packages"}}, "2.18.0": {"name": "nan", "version": "2.18.0", "license": "MIT", "_id": "nan@2.18.0", "maintainers": [{"name": "kkoopa", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mkrufky", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "26a6faae7ffbeb293a39660e88a76b82e30b7554", "tarball": "https://registry.npmjs.org/nan/-/nan-2.18.0.tgz", "fileCount": 48, "integrity": "sha512-W7tfG7vMOGtD30sHoZSSc/JVYiyDPEyQVso/Zz+/uQd0B0L46gtC+pHha5FFMRpil6fm/AoEcRWyOVi4+E/f8w==", "signatures": [{"sig": "MEQCIBpOKh6yXYTBkwVaS2V1epgbYm4hHRi/cjgrpMF0+1cvAiAV+u1Yh4Jvlz0p6cslnkBr5bfLHqZsJNx0qbSiKGRh7Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 429252}, "main": "include_dirs.js", "gitHead": "e14bdcd1f72d62bca1d541b66da43130384ec213", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc --stderr test/js/*-test.js", "test:worker": "node --experimental-worker test/tap-as-worker.js --gc --stderr test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --msvs_version=2015 --directory test", "rebuild-tests-2017": "node-gyp rebuild --msvs_version=2017 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "9.8.0", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 20 compatibility", "directories": {}, "_nodeVersion": "20.5.1", "_hasShrinkwrap": false, "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "request": "=2.81.0", "bindings": "~1.2.1", "node-gyp": "~8.4.1", "commander": "^2.8.1", "readable-stream": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/nan_2.18.0_1694521797551_0.578758532935979", "host": "s3://npm-registry-packages"}}, "2.19.0": {"name": "nan", "version": "2.19.0", "license": "MIT", "_id": "nan@2.19.0", "maintainers": [{"name": "kkoopa", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mkrufky", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "bb58122ad55a6c5bc973303908d5b16cfdd5a8c0", "tarball": "https://registry.npmjs.org/nan/-/nan-2.19.0.tgz", "fileCount": 48, "integrity": "sha512-nO1xXxfh/RWNxfd/XPfbIfFk5vgLsAxUR9y5O0cHMJu/AW9U95JLXqthYHjEp+8gQ5p96K9jUp8nbVOxCdRbtw==", "signatures": [{"sig": "MEUCIQCdAXFWeGLRtmK3MyyaMUl2z5Ke8yfC8SAtm84k/uwZLQIgXxjJ30EyPqkIaCp0PDCBV0VH74poMHhrRsAS3Gi4AVU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 429494}, "main": "include_dirs.js", "gitHead": "56585a1cd21f25b4d56168063cedaf0831c63fef", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc --stderr test/js/*-test.js", "test:worker": "node --experimental-worker test/tap-as-worker.js --gc --stderr test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --msvs_version=2015 --directory test", "rebuild-tests-2017": "node-gyp rebuild --msvs_version=2017 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 20 compatibility", "directories": {}, "_nodeVersion": "21.6.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "request": "=2.81.0", "bindings": "~1.2.1", "node-gyp": "~8.4.1", "commander": "^2.8.1", "readable-stream": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/nan_2.19.0_1709725761021_0.46349921049935805", "host": "s3://npm-registry-packages"}}, "2.20.0": {"name": "nan", "version": "2.20.0", "license": "MIT", "_id": "nan@2.20.0", "maintainers": [{"name": "kkoopa", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mkrufky", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "08c5ea813dd54ed16e5bd6505bf42af4f7838ca3", "tarball": "https://registry.npmjs.org/nan/-/nan-2.20.0.tgz", "fileCount": 48, "integrity": "sha512-bk3gXBZDGILuuo/6sKtr0DQmSThYHLtNCdSdXk9YkxD/jK6X2vmCyyXBBxyqZ4XcnzTyYEAThfX3DCEnLf6igw==", "signatures": [{"sig": "MEYCIQCiCEu3UrTQt5aXDwb4hVHaW1tBK6LTnyLa1EjNyFIPPwIhAMXF/Qquf+5EFiP5CMM7iEBD/h8hYzosKMrnjvvg7J8X", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 430121}, "main": "include_dirs.js", "gitHead": "4d1d74e0a9ea8fa8091b88e45dc57e4c7eb9cdf7", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc --stderr test/js/*-test.js", "test:worker": "node --experimental-worker test/tap-as-worker.js --gc --stderr test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --msvs_version=2015 --directory test", "rebuild-tests-2017": "node-gyp rebuild --msvs_version=2017 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 22 compatibility", "directories": {}, "_nodeVersion": "22.2.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "request": "=2.81.0", "bindings": "~1.2.1", "node-gyp": "~8.4.1", "commander": "^2.8.1", "readable-stream": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/nan_2.20.0_1718189657303_0.16058017966505989", "host": "s3://npm-registry-packages"}}, "2.21.0": {"name": "nan", "version": "2.21.0", "license": "MIT", "_id": "nan@2.21.0", "maintainers": [{"name": "kkoopa", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mkrufky", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "203ab765a02e6676c8cb92e1cad9503e7976d55b", "tarball": "https://registry.npmjs.org/nan/-/nan-2.21.0.tgz", "fileCount": 48, "integrity": "sha512-MCpOGmdWvAOMi4RWnpxS5G24l7dVMtdSHtV87I3ltjaLdFOTO74HVJ+DfYiAXjxGKsYR/UCmm1rBwhMN7KqS1A==", "signatures": [{"sig": "MEQCIFdF2Ht5H4Rd5XEZhbNkzd+vnyNHIlicwHVOgD3LhwdhAiBQUtMffn9hu3f7FNLJ4upRkfOMnyMWmLwBmGJXvJEp4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 441285}, "main": "include_dirs.js", "gitHead": "1c56d98f23b3085a1c47967c509266062c61c783", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc --stderr test/js/*-test.js", "test:worker": "node --experimental-worker test/tap-as-worker.js --gc --stderr test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --msvs_version=2015 --directory test", "rebuild-tests-2017": "node-gyp rebuild --msvs_version=2017 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 22 compatibility", "directories": {}, "_nodeVersion": "22.9.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "request": "=2.81.0", "bindings": "~1.2.1", "node-gyp": "~8.4.1", "commander": "^2.8.1", "readable-stream": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/nan_2.21.0_1728555296850_0.7522604408561917", "host": "s3://npm-registry-packages"}}, "2.22.0": {"name": "nan", "version": "2.22.0", "license": "MIT", "_id": "nan@2.22.0", "maintainers": [{"name": "kkoopa", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mkrufky", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "31bc433fc33213c97bad36404bb68063de604de3", "tarball": "https://registry.npmjs.org/nan/-/nan-2.22.0.tgz", "fileCount": 140, "integrity": "sha512-nbajikzWTMwsW+eSsNm3QwlOs7het9gGJU5dDZzRTQGk03vyBOauxgI4VakDzE0PtsGTmXPsXTbbjVhRwR5mpw==", "signatures": [{"sig": "MEUCIDcJNUeaLmBLSxkAEA1Dwe+3+ud8+eKnARePovRaiDZ4AiEA0PIt95B7WHXybWadnzxwTz1JWwW0kpOiwvvhR/10XoI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1142201}, "main": "include_dirs.js", "gitHead": "f5921c7eb97dacb926485d7084ed475963773d07", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc --stderr test/js/*-test.js", "test:worker": "node --experimental-worker test/tap-as-worker.js --gc --stderr test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --msvs_version=2015 --directory test", "rebuild-tests-2017": "node-gyp rebuild --msvs_version=2017 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 23 compatibility", "directories": {}, "_nodeVersion": "22.9.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "request": "=2.81.0", "bindings": "~1.2.1", "node-gyp": "~8.4.1", "commander": "^2.8.1", "readable-stream": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/nan_2.22.0_1728621863380_0.875895001015415", "host": "s3://npm-registry-packages"}}, "2.22.1": {"name": "nan", "version": "2.22.1", "license": "MIT", "_id": "nan@2.22.1", "maintainers": [{"name": "kkoopa", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/rvagg", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/kkoopa/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/trevnorris", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/TooTallNate", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/bnoordhuis", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/agnat", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mkrufky", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/nan#readme", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "dist": {"shasum": "27aacbba463b05ed7751d3c0035f73cb1afcfb75", "tarball": "https://registry.npmjs.org/nan/-/nan-2.22.1.tgz", "fileCount": 49, "integrity": "sha512-pfRR4ZcNTSm2ZFHaztuvbICf+hyiG6ecA06SfAxoPmuHjvMu0KUIae7Y8GyVkbBqeEIidsmXeYooWIX9+qjfRQ==", "signatures": [{"sig": "MEYCIQCVMUIJQOixULzwBIpN9BqKA7hMS2HPCgNGpIc29P9VlAIhAOM37xCBodLYkbMSwCvJchGm7xKAhmI4xuFz8bZaEtxn", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 443868}, "main": "include_dirs.js", "gitHead": "84e835e92186d49d1a82ab4fa76b259ce1464b72", "scripts": {"docs": "doc/.build.sh", "test": "tap --gc --stderr test/js/*-test.js", "test:worker": "node --experimental-worker test/tap-as-worker.js --gc --stderr test/js/*-test.js", "rebuild-tests": "node-gyp rebuild --directory test", "rebuild-tests-2015": "node-gyp rebuild --msvs_version=2015 --directory test"}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/nan.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 22 compatibility", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "devDependencies": {"tap": "~0.7.1", "glob": "^5.0.14", "xtend": "~4.0.0", "request": "=2.81.0", "bindings": "~1.2.1", "node-gyp": "~v10.3.1", "commander": "^2.8.1", "readable-stream": "^2.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/nan_2.22.1_1740136636256_0.3146344250555102", "host": "s3://npm-registry-packages-npm-production"}}, "2.22.2": {"name": "nan", "version": "2.22.2", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 22 compatibility", "main": "include_dirs.js", "repository": {"type": "git", "url": "git://github.com/nodejs/nan.git"}, "scripts": {"test": "tap --gc --stderr test/js/*-test.js", "test:worker": "node --experimental-worker test/tap-as-worker.js --gc --stderr test/js/*-test.js", "rebuild-tests-2015": "node-gyp rebuild --msvs_version=2015 --directory test", "rebuild-tests": "node-gyp rebuild --directory test", "docs": "doc/.build.sh"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/rvagg"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/kkoopa/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/trevnorris"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/TooTallNate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/brett19"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/agnat"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/mkrufky"}], "devDependencies": {"bindings": "~1.2.1", "commander": "^2.8.1", "glob": "^5.0.14", "request": "=2.81.0", "node-gyp": "~v10.3.1", "readable-stream": "^2.1.4", "tap": "~0.7.1", "xtend": "~4.0.0"}, "license": "MIT", "gitHead": "3bf886345aa9be3ae2ee73068906de1a6f2c51a3", "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "homepage": "https://github.com/nodejs/nan#readme", "_id": "nan@2.22.2", "_nodeVersion": "18.19.1", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-DANghxFkS1plDdRsX0X9pm0Z6SJNN6gBdtXfanwoZ8hooC5gosGFSBGRYHUVPz1asKA/kMRqDRdHrluZ61SpBQ==", "shasum": "6b504fd029fb8f38c0990e52ad5c26772fdacfbb", "tarball": "https://registry.npmjs.org/nan/-/nan-2.22.2.tgz", "fileCount": 49, "unpackedSize": 444175, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCEBiyVVmoCWBkCDOZ+ssOApwBuFtIoNfVE53PCVx43egIhAPGamN7OZUqw2y5ErkuEEinu2s07BAZL8xyLvc100znC"}]}, "_npmUser": {"name": "kkoopa", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "kkoopa", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/nan_2.22.2_1740587778959_0.7301812902244671"}, "_hasShrinkwrap": false}}, "time": {"created": "2013-08-16T10:54:38.330Z", "modified": "2025-02-26T16:36:19.353Z", "0.3.0-wip": "2013-08-16T10:54:45.384Z", "0.3.0-wip2": "2013-08-16T11:38:17.241Z", "0.3.0": "2013-08-19T11:56:01.536Z", "0.3.1": "2013-08-20T09:17:59.275Z", "0.3.2": "2013-08-30T12:53:23.650Z", "0.4.0": "2013-09-02T11:49:34.846Z", "0.4.1": "2013-09-16T13:35:24.529Z", "0.4.2": "2013-11-02T02:40:01.651Z", "0.4.3": "2013-11-02T02:56:46.951Z", "0.4.4": "2013-11-02T03:39:07.560Z", "0.5.0": "2013-11-11T02:36:36.947Z", "0.5.1": "2013-11-11T21:43:27.912Z", "0.5.2": "2013-11-16T10:25:47.488Z", "0.6.0": "2013-11-21T01:22:46.218Z", "0.7.0": "2013-12-16T14:55:11.928Z", "0.7.1": "2014-01-09T12:23:38.047Z", "0.8.0": "2014-01-09T12:30:42.175Z", "1.0.0": "2014-05-04T12:20:09.986Z", "1.1.0": "2014-05-25T01:13:41.611Z", "1.1.1": "2014-05-28T10:20:37.994Z", "1.1.2": "2014-05-28T13:07:41.871Z", "1.2.0": "2014-06-04T21:24:39.703Z", "1.3.0": "2014-08-02T12:07:40.701Z", "1.4.0": "2014-11-01T22:48:41.300Z", "1.4.1": "2014-11-08T16:19:39.727Z", "1.5.0": "2015-01-14T19:39:39.575Z", "1.4.2": "2015-01-15T03:53:24.469Z", "1.4.3": "2015-01-15T03:54:10.813Z", "1.5.1": "2015-01-15T03:54:54.300Z", "1.5.2": "2015-01-22T23:54:56.053Z", "1.6.0": "2015-01-23T00:29:18.252Z", "1.5.3": "2015-01-23T00:40:19.788Z", "1.6.1": "2015-01-23T00:48:11.043Z", "1.6.2": "2015-02-06T18:39:26.282Z", "1.7.0": "2015-02-28T12:18:10.217Z", "1.8.0": "2015-04-23T14:29:59.549Z", "1.8.1": "2015-04-23T17:59:00.393Z", "1.8.2": "2015-04-23T19:54:07.123Z", "1.8.3": "2015-04-26T14:29:27.155Z", "1.8.4": "2015-04-26T14:32:39.821Z", "1.9.0": "2015-07-31T11:18:16.059Z", "2.0.0": "2015-07-31T11:56:05.961Z", "2.0.1": "2015-08-06T11:17:23.696Z", "2.0.2": "2015-08-06T11:41:46.078Z", "2.0.3": "2015-08-06T23:20:47.997Z", "2.0.4": "2015-08-07T00:05:12.716Z", "2.0.5": "2015-08-10T14:53:43.186Z", "2.0.6": "2015-08-26T07:48:29.905Z", "2.0.7": "2015-08-26T07:52:04.898Z", "2.0.8": "2015-08-28T08:02:26.997Z", "2.0.9": "2015-09-08T22:57:25.991Z", "2.1.0": "2015-10-08T14:39:47.658Z", "2.2.0": "2016-01-09T15:12:57.471Z", "2.2.1": "2016-03-29T15:30:42.770Z", "2.3.0": "2016-04-27T10:10:00.078Z", "2.3.1": "2016-04-27T16:22:50.179Z", "2.3.2": "2016-04-27T17:35:35.526Z", "2.3.3": "2016-05-03T22:13:40.112Z", "2.3.4": "2016-05-30T22:12:38.756Z", "2.3.5": "2016-05-31T15:06:07.585Z", "2.4.0": "2016-07-10T13:51:21.410Z", "2.5.0": "2016-12-21T18:49:51.532Z", "2.5.1": "2017-01-22T22:27:41.920Z", "2.6.0": "2017-04-05T22:37:27.792Z", "2.6.1": "2017-04-06T05:50:18.273Z", "2.6.2": "2017-04-12T20:38:38.470Z", "2.7.0": "2017-08-30T00:42:51.289Z", "2.8.0": "2017-11-15T11:34:24.395Z", "2.9.1": "2018-02-22T17:14:09.800Z", "2.9.2": "2018-02-22T23:34:17.650Z", "2.10.0": "2018-03-16T16:11:48.820Z", "2.11.0": "2018-08-25T12:43:45.113Z", "2.11.1": "2018-09-29T08:09:53.621Z", "2.12.0": "2018-12-16T13:14:18.284Z", "2.12.1": "2018-12-18T21:01:39.060Z", "2.13.0": "2019-03-13T22:40:10.625Z", "2.13.1": "2019-03-14T21:55:48.868Z", "2.13.2": "2019-03-24T14:32:19.987Z", "2.14.0": "2019-05-16T19:11:36.198Z", "2.14.1": "2020-04-21T18:59:56.894Z", "2.14.2": "2020-10-13T12:49:54.960Z", "2.15.0": "2021-08-04T16:13:03.440Z", "2.16.0": "2022-05-25T13:05:47.001Z", "2.17.0": "2022-10-10T19:06:05.290Z", "2.18.0": "2023-09-12T12:29:57.733Z", "2.19.0": "2024-03-06T11:49:21.170Z", "2.20.0": "2024-06-12T10:54:17.465Z", "2.21.0": "2024-10-10T10:14:57.115Z", "2.22.0": "2024-10-11T04:44:23.602Z", "2.22.1": "2025-02-21T11:17:16.539Z", "2.22.2": "2025-02-26T16:36:19.218Z"}, "bugs": {"url": "https://github.com/nodejs/nan/issues"}, "license": "MIT", "homepage": "https://github.com/nodejs/nan#readme", "repository": {"type": "git", "url": "git://github.com/nodejs/nan.git"}, "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 22 compatibility", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/rvagg"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/kkoopa/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/trevnorris"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/TooTallNate"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/brett19"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/bnoordhuis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/agnat"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/mkrufky"}], "maintainers": [{"name": "kkoopa", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "readme": "Native Abstractions for Node.js\n===============================\n\n**A header file filled with macro and utility goodness for making add-on development for Node.js easier across versions 8, 10, 12, 14, 16, 17, 18, 19, 20, 21, and 22.**\n\n***Current version: 2.22.2***\n\n*(See [CHANGELOG.md](https://github.com/nodejs/nan/blob/master/CHANGELOG.md) for complete ChangeLog)*\n\n[![NPM](https://nodei.co/npm/nan.png?downloads=true&downloadRank=true)](https://nodei.co/npm/nan/) [![NPM](https://nodei.co/npm-dl/nan.png?months=6&height=3)](https://nodei.co/npm/nan/)\n\n[![Build status](https://ci.appveyor.com/api/projects/status/kh73pbm9dsju7fgh)](https://ci.appveyor.com/project/RodVagg/nan)\n\nThanks to the crazy changes in V8 (and some in Node core), keeping native addons compiling happily across versions, particularly 0.10 to 0.12 to 4.0, is a minor nightmare. The goal of this project is to store all logic necessary to develop native Node.js addons without having to inspect `NODE_MODULE_VERSION` and get yourself into a macro-tangle.\n\nThis project also contains some helper utilities that make addon development a bit more pleasant.\n\n * **[News & Updates](#news)**\n * **[Usage](#usage)**\n * **[Example](#example)**\n * **[API](#api)**\n * **[Tests](#tests)**\n * **[Known issues](#issues)**\n * **[Governance & Contributing](#governance)**\n\n<a name=\"news\"></a>\n\n## News & Updates\n\n<a name=\"usage\"></a>\n\n## Usage\n\nSimply add **NAN** as a dependency in the *package.json* of your Node addon:\n\n``` bash\n$ npm install --save nan\n```\n\nPull in the path to **NAN** in your *binding.gyp* so that you can use `#include <nan.h>` in your *.cpp* files:\n\n``` python\n\"include_dirs\" : [\n    \"<!(node -e \\\"require('nan')\\\")\"\n]\n```\n\nThis works like a `-I<path-to-NAN>` when compiling your addon.\n\n<a name=\"example\"></a>\n\n## Example\n\nJust getting started with Nan? Take a look at the **[Node Add-on Examples](https://github.com/nodejs/node-addon-examples)**.\n\nRefer to a [quick-start **Nan** Boilerplate](https://github.com/fcanas/node-native-boilerplate) for a ready-to-go project that utilizes basic Nan functionality.\n\nFor a simpler example, see the **[async pi estimation example](https://github.com/nodejs/nan/tree/master/examples/async_pi_estimate)** in the examples directory for full code and an explanation of what this Monte Carlo Pi estimation example does. Below are just some parts of the full example that illustrate the use of **NAN**.\n\nYet another example is **[nan-example-eol](https://github.com/CodeCharmLtd/nan-example-eol)**. It shows newline detection implemented as a native addon.\n\nAlso take a look at our comprehensive **[C++ test suite](https://github.com/nodejs/nan/tree/master/test/cpp)** which has a plethora of code snippets for your pasting pleasure.\n\n<a name=\"api\"></a>\n\n## API\n\nAdditional to the NAN documentation below, please consult:\n\n* [The V8 Getting Started * Guide](https://v8.dev/docs/embed)\n* [V8 API Documentation](https://v8docs.nodesource.com/)\n* [Node Add-on Documentation](https://nodejs.org/api/addons.html)\n\n<!-- START API -->\n\n### JavaScript-accessible methods\n\nA _template_ is a blueprint for JavaScript functions and objects in a context. You can use a template to wrap C++ functions and data structures within JavaScript objects so that they can be manipulated from JavaScript. See the V8 Embedders Guide section on [Templates](https://github.com/v8/v8/wiki/Embedder%27s-Guide#templates) for further information.\n\nIn order to expose functionality to JavaScript via a template, you must provide it to V8 in a form that it understands. Across the versions of V8 supported by NAN, JavaScript-accessible method signatures vary widely, NAN fully abstracts method declaration and provides you with an interface that is similar to the most recent V8 API but is backward-compatible with older versions that still use the now-deceased `v8::Argument` type.\n\n* **Method argument types**\n - <a href=\"doc/methods.md#api_nan_function_callback_info\"><b><code>Nan::FunctionCallbackInfo</code></b></a>\n - <a href=\"doc/methods.md#api_nan_property_callback_info\"><b><code>Nan::PropertyCallbackInfo</code></b></a>\n - <a href=\"doc/methods.md#api_nan_return_value\"><b><code>Nan::ReturnValue</code></b></a>\n* **Method declarations**\n - <a href=\"doc/methods.md#api_nan_method\"><b>Method declaration</b></a>\n - <a href=\"doc/methods.md#api_nan_getter\"><b>Getter declaration</b></a>\n - <a href=\"doc/methods.md#api_nan_setter\"><b>Setter declaration</b></a>\n - <a href=\"doc/methods.md#api_nan_property_getter\"><b>Property getter declaration</b></a>\n - <a href=\"doc/methods.md#api_nan_property_setter\"><b>Property setter declaration</b></a>\n - <a href=\"doc/methods.md#api_nan_property_enumerator\"><b>Property enumerator declaration</b></a>\n - <a href=\"doc/methods.md#api_nan_property_deleter\"><b>Property deleter declaration</b></a>\n - <a href=\"doc/methods.md#api_nan_property_query\"><b>Property query declaration</b></a>\n - <a href=\"doc/methods.md#api_nan_index_getter\"><b>Index getter declaration</b></a>\n - <a href=\"doc/methods.md#api_nan_index_setter\"><b>Index setter declaration</b></a>\n - <a href=\"doc/methods.md#api_nan_index_enumerator\"><b>Index enumerator declaration</b></a>\n - <a href=\"doc/methods.md#api_nan_index_deleter\"><b>Index deleter declaration</b></a>\n - <a href=\"doc/methods.md#api_nan_index_query\"><b>Index query declaration</b></a>\n* Method and template helpers\n - <a href=\"doc/methods.md#api_nan_set_method\"><b><code>Nan::SetMethod()</code></b></a>\n - <a href=\"doc/methods.md#api_nan_set_prototype_method\"><b><code>Nan::SetPrototypeMethod()</code></b></a>\n - <a href=\"doc/methods.md#api_nan_set_accessor\"><b><code>Nan::SetAccessor()</code></b></a>\n - <a href=\"doc/methods.md#api_nan_set_named_property_handler\"><b><code>Nan::SetNamedPropertyHandler()</code></b></a>\n - <a href=\"doc/methods.md#api_nan_set_indexed_property_handler\"><b><code>Nan::SetIndexedPropertyHandler()</code></b></a>\n - <a href=\"doc/methods.md#api_nan_set_template\"><b><code>Nan::SetTemplate()</code></b></a>\n - <a href=\"doc/methods.md#api_nan_set_prototype_template\"><b><code>Nan::SetPrototypeTemplate()</code></b></a>\n - <a href=\"doc/methods.md#api_nan_set_instance_template\"><b><code>Nan::SetInstanceTemplate()</code></b></a>\n - <a href=\"doc/methods.md#api_nan_set_call_handler\"><b><code>Nan::SetCallHandler()</code></b></a>\n - <a href=\"doc/methods.md#api_nan_set_call_as_function_handler\"><b><code>Nan::SetCallAsFunctionHandler()</code></b></a>\n\n### Scopes\n\nA _local handle_ is a pointer to an object. All V8 objects are accessed using handles, they are necessary because of the way the V8 garbage collector works.\n\nA handle scope can be thought of as a container for any number of handles. When you've finished with your handles, instead of deleting each one individually you can simply delete their scope.\n\nThe creation of `HandleScope` objects is different across the supported versions of V8. Therefore, NAN provides its own implementations that can be used safely across these.\n\n - <a href=\"doc/scopes.md#api_nan_handle_scope\"><b><code>Nan::HandleScope</code></b></a>\n - <a href=\"doc/scopes.md#api_nan_escapable_handle_scope\"><b><code>Nan::EscapableHandleScope</code></b></a>\n\nAlso see the V8 Embedders Guide section on [Handles and Garbage Collection](https://github.com/v8/v8/wiki/Embedder%27s%20Guide#handles-and-garbage-collection).\n\n### Persistent references\n\nAn object reference that is independent of any `HandleScope` is a _persistent_ reference. Where a `Local` handle only lives as long as the `HandleScope` in which it was allocated, a `Persistent` handle remains valid until it is explicitly disposed.\n\nDue to the evolution of the V8 API, it is necessary for NAN to provide a wrapper implementation of the `Persistent` classes to supply compatibility across the V8 versions supported.\n\n - <a href=\"doc/persistent.md#api_nan_persistent_base\"><b><code>Nan::PersistentBase & v8::PersistentBase</code></b></a>\n - <a href=\"doc/persistent.md#api_nan_non_copyable_persistent_traits\"><b><code>Nan::NonCopyablePersistentTraits & v8::NonCopyablePersistentTraits</code></b></a>\n - <a href=\"doc/persistent.md#api_nan_copyable_persistent_traits\"><b><code>Nan::CopyablePersistentTraits & v8::CopyablePersistentTraits</code></b></a>\n - <a href=\"doc/persistent.md#api_nan_persistent\"><b><code>Nan::Persistent</code></b></a>\n - <a href=\"doc/persistent.md#api_nan_global\"><b><code>Nan::Global</code></b></a>\n - <a href=\"doc/persistent.md#api_nan_weak_callback_info\"><b><code>Nan::WeakCallbackInfo</code></b></a>\n - <a href=\"doc/persistent.md#api_nan_weak_callback_type\"><b><code>Nan::WeakCallbackType</code></b></a>\n\nAlso see the V8 Embedders Guide section on [Handles and Garbage Collection](https://v8.dev/docs/embed#handles-and-garbage-collection).\n\n### New\n\nNAN provides a `Nan::New()` helper for the creation of new JavaScript objects in a way that's compatible across the supported versions of V8.\n\n - <a href=\"doc/new.md#api_nan_new\"><b><code>Nan::New()</code></b></a>\n - <a href=\"doc/new.md#api_nan_undefined\"><b><code>Nan::Undefined()</code></b></a>\n - <a href=\"doc/new.md#api_nan_null\"><b><code>Nan::Null()</code></b></a>\n - <a href=\"doc/new.md#api_nan_true\"><b><code>Nan::True()</code></b></a>\n - <a href=\"doc/new.md#api_nan_false\"><b><code>Nan::False()</code></b></a>\n - <a href=\"doc/new.md#api_nan_empty_string\"><b><code>Nan::EmptyString()</code></b></a>\n\n\n### Converters\n\nNAN contains functions that convert `v8::Value`s to other `v8::Value` types and native types. Since type conversion is not guaranteed to succeed, they return `Nan::Maybe` types. These converters can be used in place of `value->ToX()` and `value->XValue()` (where `X` is one of the types, e.g. `Boolean`) in a way that provides a consistent interface across V8 versions. Newer versions of V8 use the new `v8::Maybe` and `v8::MaybeLocal` types for these conversions, older versions don't have this functionality so it is provided by NAN.\n\n - <a href=\"doc/converters.md#api_nan_to\"><b><code>Nan::To()</code></b></a>\n\n### Maybe Types\n\nThe `Nan::MaybeLocal` and `Nan::Maybe` types are monads that encapsulate `v8::Local` handles that _may be empty_.\n\n* **Maybe Types**\n  - <a href=\"doc/maybe_types.md#api_nan_maybe_local\"><b><code>Nan::MaybeLocal</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_maybe\"><b><code>Nan::Maybe</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_nothing\"><b><code>Nan::Nothing</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_just\"><b><code>Nan::Just</code></b></a>\n* **Maybe Helpers**\n  - <a href=\"doc/maybe_types.md#api_nan_call\"><b><code>Nan::Call()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_to_detail_string\"><b><code>Nan::ToDetailString()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_to_array_index\"><b><code>Nan::ToArrayIndex()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_equals\"><b><code>Nan::Equals()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_new_instance\"><b><code>Nan::NewInstance()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_get_function\"><b><code>Nan::GetFunction()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_set\"><b><code>Nan::Set()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_define_own_property\"><b><code>Nan::DefineOwnProperty()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_force_set\"><del><b><code>Nan::ForceSet()</code></b></del></a>\n  - <a href=\"doc/maybe_types.md#api_nan_get\"><b><code>Nan::Get()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_get_property_attribute\"><b><code>Nan::GetPropertyAttributes()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_has\"><b><code>Nan::Has()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_delete\"><b><code>Nan::Delete()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_get_property_names\"><b><code>Nan::GetPropertyNames()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_get_own_property_names\"><b><code>Nan::GetOwnPropertyNames()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_set_prototype\"><b><code>Nan::SetPrototype()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_object_proto_to_string\"><b><code>Nan::ObjectProtoToString()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_has_own_property\"><b><code>Nan::HasOwnProperty()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_has_real_named_property\"><b><code>Nan::HasRealNamedProperty()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_has_real_indexed_property\"><b><code>Nan::HasRealIndexedProperty()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_has_real_named_callback_property\"><b><code>Nan::HasRealNamedCallbackProperty()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_get_real_named_property_in_prototype_chain\"><b><code>Nan::GetRealNamedPropertyInPrototypeChain()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_get_real_named_property\"><b><code>Nan::GetRealNamedProperty()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_call_as_function\"><b><code>Nan::CallAsFunction()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_call_as_constructor\"><b><code>Nan::CallAsConstructor()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_get_source_line\"><b><code>Nan::GetSourceLine()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_get_line_number\"><b><code>Nan::GetLineNumber()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_get_start_column\"><b><code>Nan::GetStartColumn()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_get_end_column\"><b><code>Nan::GetEndColumn()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_clone_element_at\"><b><code>Nan::CloneElementAt()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_has_private\"><b><code>Nan::HasPrivate()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_get_private\"><b><code>Nan::GetPrivate()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_set_private\"><b><code>Nan::SetPrivate()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_delete_private\"><b><code>Nan::DeletePrivate()</code></b></a>\n  - <a href=\"doc/maybe_types.md#api_nan_make_maybe\"><b><code>Nan::MakeMaybe()</code></b></a>\n\n### Script\n\nNAN provides `v8::Script` helpers as the API has changed over the supported versions of V8.\n\n - <a href=\"doc/script.md#api_nan_compile_script\"><b><code>Nan::CompileScript()</code></b></a>\n - <a href=\"doc/script.md#api_nan_run_script\"><b><code>Nan::RunScript()</code></b></a>\n - <a href=\"doc/script.md#api_nan_script_origin\"><b><code>Nan::ScriptOrigin</code></b></a>\n\n\n### JSON\n\nThe _JSON_ object provides the C++ versions of the methods offered by the `JSON` object in javascript. V8 exposes these methods via the `v8::JSON` object.\n\n - <a href=\"doc/json.md#api_nan_json_parse\"><b><code>Nan::JSON.Parse</code></b></a>\n - <a href=\"doc/json.md#api_nan_json_stringify\"><b><code>Nan::JSON.Stringify</code></b></a>\n\nRefer to the V8 JSON object in the [V8 documentation](https://v8docs.nodesource.com/node-8.16/da/d6f/classv8_1_1_j_s_o_n.html) for more information about these methods and their arguments.\n\n### Errors\n\nNAN includes helpers for creating, throwing and catching Errors as much of this functionality varies across the supported versions of V8 and must be abstracted.\n\nNote that an Error object is simply a specialized form of `v8::Value`.\n\nAlso consult the V8 Embedders Guide section on [Exceptions](https://v8.dev/docs/embed#exceptions) for more information.\n\n - <a href=\"doc/errors.md#api_nan_error\"><b><code>Nan::Error()</code></b></a>\n - <a href=\"doc/errors.md#api_nan_range_error\"><b><code>Nan::RangeError()</code></b></a>\n - <a href=\"doc/errors.md#api_nan_reference_error\"><b><code>Nan::ReferenceError()</code></b></a>\n - <a href=\"doc/errors.md#api_nan_syntax_error\"><b><code>Nan::SyntaxError()</code></b></a>\n - <a href=\"doc/errors.md#api_nan_type_error\"><b><code>Nan::TypeError()</code></b></a>\n - <a href=\"doc/errors.md#api_nan_throw_error\"><b><code>Nan::ThrowError()</code></b></a>\n - <a href=\"doc/errors.md#api_nan_throw_range_error\"><b><code>Nan::ThrowRangeError()</code></b></a>\n - <a href=\"doc/errors.md#api_nan_throw_reference_error\"><b><code>Nan::ThrowReferenceError()</code></b></a>\n - <a href=\"doc/errors.md#api_nan_throw_syntax_error\"><b><code>Nan::ThrowSyntaxError()</code></b></a>\n - <a href=\"doc/errors.md#api_nan_throw_type_error\"><b><code>Nan::ThrowTypeError()</code></b></a>\n - <a href=\"doc/errors.md#api_nan_fatal_exception\"><b><code>Nan::FatalException()</code></b></a>\n - <a href=\"doc/errors.md#api_nan_errno_exception\"><b><code>Nan::ErrnoException()</code></b></a>\n - <a href=\"doc/errors.md#api_nan_try_catch\"><b><code>Nan::TryCatch</code></b></a>\n\n\n### Buffers\n\nNAN's `node::Buffer` helpers exist as the API has changed across supported Node versions. Use these methods to ensure compatibility.\n\n - <a href=\"doc/buffers.md#api_nan_new_buffer\"><b><code>Nan::NewBuffer()</code></b></a>\n - <a href=\"doc/buffers.md#api_nan_copy_buffer\"><b><code>Nan::CopyBuffer()</code></b></a>\n - <a href=\"doc/buffers.md#api_nan_free_callback\"><b><code>Nan::FreeCallback()</code></b></a>\n\n### Nan::Callback\n\n`Nan::Callback` makes it easier to use `v8::Function` handles as callbacks. A class that wraps a `v8::Function` handle, protecting it from garbage collection and making it particularly useful for storage and use across asynchronous execution.\n\n - <a href=\"doc/callback.md#api_nan_callback\"><b><code>Nan::Callback</code></b></a>\n\n### Asynchronous work helpers\n\n`Nan::AsyncWorker`, `Nan::AsyncProgressWorker` and `Nan::AsyncProgressQueueWorker` are helper classes that make working with asynchronous code easier.\n\n - <a href=\"doc/asyncworker.md#api_nan_async_worker\"><b><code>Nan::AsyncWorker</code></b></a>\n - <a href=\"doc/asyncworker.md#api_nan_async_progress_worker\"><b><code>Nan::AsyncProgressWorkerBase &amp; Nan::AsyncProgressWorker</code></b></a>\n - <a href=\"doc/asyncworker.md#api_nan_async_progress_queue_worker\"><b><code>Nan::AsyncProgressQueueWorker</code></b></a>\n - <a href=\"doc/asyncworker.md#api_nan_async_queue_worker\"><b><code>Nan::AsyncQueueWorker</code></b></a>\n\n### Strings & Bytes\n\nMiscellaneous string & byte encoding and decoding functionality provided for compatibility across supported versions of V8 and Node. Implemented by NAN to ensure that all encoding types are supported, even for older versions of Node where they are missing.\n\n - <a href=\"doc/string_bytes.md#api_nan_encoding\"><b><code>Nan::Encoding</code></b></a>\n - <a href=\"doc/string_bytes.md#api_nan_encode\"><b><code>Nan::Encode()</code></b></a>\n - <a href=\"doc/string_bytes.md#api_nan_decode_bytes\"><b><code>Nan::DecodeBytes()</code></b></a>\n - <a href=\"doc/string_bytes.md#api_nan_decode_write\"><b><code>Nan::DecodeWrite()</code></b></a>\n\n\n### Object Wrappers\n\nThe `ObjectWrap` class can be used to make wrapped C++ objects and a factory of wrapped objects.\n\n - <a href=\"doc/object_wrappers.md#api_nan_object_wrap\"><b><code>Nan::ObjectWrap</code></b></a>\n\n\n### V8 internals\n\nThe hooks to access V8 internals—including GC and statistics—are different across the supported versions of V8, therefore NAN provides its own hooks that call the appropriate V8 methods.\n\n - <a href=\"doc/v8_internals.md#api_nan_gc_callback\"><b><code>NAN_GC_CALLBACK()</code></b></a>\n - <a href=\"doc/v8_internals.md#api_nan_add_gc_epilogue_callback\"><b><code>Nan::AddGCEpilogueCallback()</code></b></a>\n - <a href=\"doc/v8_internals.md#api_nan_remove_gc_epilogue_callback\"><b><code>Nan::RemoveGCEpilogueCallback()</code></b></a>\n - <a href=\"doc/v8_internals.md#api_nan_add_gc_prologue_callback\"><b><code>Nan::AddGCPrologueCallback()</code></b></a>\n - <a href=\"doc/v8_internals.md#api_nan_remove_gc_prologue_callback\"><b><code>Nan::RemoveGCPrologueCallback()</code></b></a>\n - <a href=\"doc/v8_internals.md#api_nan_get_heap_statistics\"><b><code>Nan::GetHeapStatistics()</code></b></a>\n - <a href=\"doc/v8_internals.md#api_nan_set_counter_function\"><b><code>Nan::SetCounterFunction()</code></b></a>\n - <a href=\"doc/v8_internals.md#api_nan_set_create_histogram_function\"><b><code>Nan::SetCreateHistogramFunction()</code></b></a>\n - <a href=\"doc/v8_internals.md#api_nan_set_add_histogram_sample_function\"><b><code>Nan::SetAddHistogramSampleFunction()</code></b></a>\n - <a href=\"doc/v8_internals.md#api_nan_idle_notification\"><del><b><code>Nan::IdleNotification()</code></b></del></a>\n - <a href=\"doc/v8_internals.md#api_nan_low_memory_notification\"><b><code>Nan::LowMemoryNotification()</code></b></a>\n - <a href=\"doc/v8_internals.md#api_nan_context_disposed_notification\"><b><code>Nan::ContextDisposedNotification()</code></b></a>\n - <a href=\"doc/v8_internals.md#api_nan_get_internal_field_pointer\"><b><code>Nan::GetInternalFieldPointer()</code></b></a>\n - <a href=\"doc/v8_internals.md#api_nan_set_internal_field_pointer\"><b><code>Nan::SetInternalFieldPointer()</code></b></a>\n - <a href=\"doc/v8_internals.md#api_nan_adjust_external_memory\"><b><code>Nan::AdjustExternalMemory()</code></b></a>\n\n\n### Miscellaneous V8 Helpers\n\n - <a href=\"doc/v8_misc.md#api_nan_utf8_string\"><b><code>Nan::Utf8String</code></b></a>\n - <a href=\"doc/v8_misc.md#api_nan_get_current_context\"><b><code>Nan::GetCurrentContext()</code></b></a>\n - <a href=\"doc/v8_misc.md#api_nan_set_isolate_data\"><b><code>Nan::SetIsolateData()</code></b></a>\n - <a href=\"doc/v8_misc.md#api_nan_get_isolate_data\"><b><code>Nan::GetIsolateData()</code></b></a>\n - <a href=\"doc/v8_misc.md#api_nan_typedarray_contents\"><b><code>Nan::TypedArrayContents</code></b></a>\n\n\n### Miscellaneous Node Helpers\n\n - <a href=\"doc/node_misc.md#api_nan_asyncresource\"><b><code>Nan::AsyncResource</code></b></a>\n - <a href=\"doc/node_misc.md#api_nan_make_callback\"><b><code>Nan::MakeCallback()</code></b></a>\n - <a href=\"doc/node_misc.md#api_nan_module_init\"><b><code>NAN_MODULE_INIT()</code></b></a>\n - <a href=\"doc/node_misc.md#api_nan_export\"><b><code>Nan::Export()</code></b></a>\n\n<!-- END API -->\n\n\n<a name=\"tests\"></a>\n\n### Tests\n\nTo run the NAN tests do:\n\n``` sh\nnpm install\nnpm run-script rebuild-tests\nnpm test\n```\n\nOr just:\n\n``` sh\nnpm install\nmake test\n```\n\n<a name=\"issues\"></a>\n\n## Known issues\n\n### Compiling against Node.js 0.12 on OSX\n\nWith new enough compilers available on OSX, the versions of V8 headers corresponding to Node.js 0.12\ndo not compile anymore. The error looks something like:\n\n```\n❯   CXX(target) Release/obj.target/accessors/cpp/accessors.o\nIn file included from ../cpp/accessors.cpp:9:\nIn file included from ../../nan.h:51:\nIn file included from /Users/<USER>/.node-gyp/0.12.18/include/node/node.h:61:\n/Users/<USER>/.node-gyp/0.12.18/include/node/v8.h:5800:54: error: 'CreateHandle' is a protected member of 'v8::HandleScope'\n  return Handle<T>(reinterpret_cast<T*>(HandleScope::CreateHandle(\n                                        ~~~~~~~~~~~~~^~~~~~~~~~~~\n```\n\nThis can be worked around by patching your local versions of v8.h corresponding to Node 0.12 to make\n`v8::Handle` a friend of `v8::HandleScope`. Since neither Node.js not V8 support this release line anymore\nthis patch cannot be released by either project in an official release.\n\nFor this reason, we do not test against Node.js 0.12 on OSX in this project's CI. If you need to support\nthat configuration, you will need to either get an older compiler, or apply a source patch to the version\nof V8 headers as a workaround.\n\n<a name=\"governance\"></a>\n\n## Governance & Contributing\n\nNAN is governed by the [Node.js Addon API Working Group](https://github.com/nodejs/CTC/blob/master/WORKING_GROUPS.md#addon-api)\n\n### Addon API Working Group (WG)\n\nThe NAN project is jointly governed by a Working Group which is responsible for high-level guidance of the project.\n\nMembers of the WG are also known as Collaborators, there is no distinction between the two, unlike other Node.js projects.\n\nThe WG has final authority over this project including:\n\n* Technical direction\n* Project governance and process (including this policy)\n* Contribution policy\n* GitHub repository hosting\n* Maintaining the list of additional Collaborators\n\nFor the current list of WG members, see the project [README.md](./README.md#collaborators).\n\nIndividuals making significant and valuable contributions are made members of the WG and given commit-access to the project. These individuals are identified by the WG and their addition to the WG is discussed via GitHub and requires unanimous consensus amongst those WG members participating in the discussion with a quorum of 50% of WG members required for acceptance of the vote.\n\n_Note:_ If you make a significant contribution and are not considered for commit-access log an issue or contact a WG member directly.\n\nFor the current list of WG members / Collaborators, see the project [README.md](./README.md#collaborators).\n\n### Consensus Seeking Process\n\nThe WG follows a [Consensus Seeking](https://en.wikipedia.org/wiki/Consensus-seeking_decision-making) decision making model.\n\nModifications of the contents of the NAN repository are made on a collaborative basis. Anybody with a GitHub account may propose a modification via pull request and it will be considered by the WG. All pull requests must be reviewed and accepted by a WG member with sufficient expertise who is able to take full responsibility for the change. In the case of pull requests proposed by an existing WG member, an additional WG member is required for sign-off. Consensus should be sought if additional WG members participate and there is disagreement around a particular modification.\n\nIf a change proposal cannot reach a consensus, a WG member can call for a vote amongst the members of the WG. Simple majority wins.\n\n<a id=\"developers-certificate-of-origin\"></a>\n\n## Developer's Certificate of Origin 1.1\n\nBy making a contribution to this project, I certify that:\n\n* (a) The contribution was created in whole or in part by me and I\n  have the right to submit it under the open source license\n  indicated in the file; or\n\n* (b) The contribution is based upon previous work that, to the best\n  of my knowledge, is covered under an appropriate open source\n  license and I have the right under that license to submit that\n  work with modifications, whether created in whole or in part\n  by me, under the same open source license (unless I am\n  permitted to submit under a different license), as indicated\n  in the file; or\n\n* (c) The contribution was provided directly to me by some other\n  person who certified (a), (b) or (c) and I have not modified\n  it.\n\n* (d) I understand and agree that this project and the contribution\n  are public and that a record of the contribution (including all\n  personal information I submit with it, including my sign-off) is\n  maintained indefinitely and may be redistributed consistent with\n  this project or the open source license(s) involved.\n\n<a name=\"collaborators\"></a>\n\n### WG Members / Collaborators\n\n<table><tbody>\n<tr><th align=\"left\">Rod Vagg</th><td><a href=\"https://github.com/rvagg\">GitHub/rvagg</a></td><td><a href=\"http://twitter.com/rvagg\">Twitter/@rvagg</a></td></tr>\n<tr><th align=\"left\">Benjamin Byholm</th><td><a href=\"https://github.com/kkoopa/\">GitHub/kkoopa</a></td><td>-</td></tr>\n<tr><th align=\"left\">Trevor Norris</th><td><a href=\"https://github.com/trevnorris\">GitHub/trevnorris</a></td><td><a href=\"http://twitter.com/trevnorris\">Twitter/@trevnorris</a></td></tr>\n<tr><th align=\"left\">Nathan Rajlich</th><td><a href=\"https://github.com/TooTallNate\">GitHub/TooTallNate</a></td><td><a href=\"http://twitter.com/TooTallNate\">Twitter/@TooTallNate</a></td></tr>\n<tr><th align=\"left\">Brett Lawson</th><td><a href=\"https://github.com/brett19\">GitHub/brett19</a></td><td><a href=\"http://twitter.com/brett19x\">Twitter/@brett19x</a></td></tr>\n<tr><th align=\"left\">Ben Noordhuis</th><td><a href=\"https://github.com/bnoordhuis\">GitHub/bnoordhuis</a></td><td><a href=\"http://twitter.com/bnoordhuis\">Twitter/@bnoordhuis</a></td></tr>\n<tr><th align=\"left\">David Siegel</th><td><a href=\"https://github.com/agnat\">GitHub/agnat</a></td><td><a href=\"http://twitter.com/agnat\">Twitter/@agnat</a></td></tr>\n<tr><th align=\"left\">Michael Ira Krufky</th><td><a href=\"https://github.com/mkrufky\">GitHub/mkrufky</a></td><td><a href=\"http://twitter.com/mkrufky\">Twitter/@mkrufky</a></td></tr>\n</tbody></table>\n\n## Licence &amp; copyright\n\nCopyright (c) 2018 NAN WG Members / Collaborators (listed above).\n\nNative Abstractions for Node.js is licensed under an MIT license. All rights not explicitly granted in the MIT license are reserved. See the included LICENSE file for more details.\n", "readmeFilename": "README.md", "users": {"djk": true, "andr": true, "detj": true, "usex": true, "fivdi": true, "panlw": true, "sopov": true, "xrush": true, "ecomfe": true, "emarcs": true, "ga1989": true, "horpto": true, "js3692": true, "koslun": true, "mortiy": true, "pandao": true, "rexpan": true, "abetomo": true, "braviel": true, "ceejbot": true, "chaoliu": true, "crycode": true, "fatelei": true, "jalcine": true, "mkrufky": true, "nohomey": true, "taoyuan": true, "wouldgo": true, "xtx1130": true, "y-a-v-a": true, "dozoisch": true, "faraoman": true, "kriswill": true, "nicknaso": true, "theheros": true, "yashprit": true, "atesgoral": true, "blitzprog": true, "daviddias": true, "debashish": true, "foliveira": true, "guananddu": true, "magemagic": true, "mojaray2k": true, "nbuchanan": true, "semenovem": true, "slickmonk": true, "steel1990": true, "chesstrian": true, "coderaiser": true, "javascript": true, "liushoukai": true, "monolithed": true, "shuoshubao": true, "yunnysunny": true, "flumpus-dev": true, "hal9zillion": true, "lukicdarkoo": true, "magicxiao85": true, "wangnan0610": true, "estliberitas": true, "highlanderkev": true, "markthethomas": true, "matteo.collina": true, "shanewholloway": true}}