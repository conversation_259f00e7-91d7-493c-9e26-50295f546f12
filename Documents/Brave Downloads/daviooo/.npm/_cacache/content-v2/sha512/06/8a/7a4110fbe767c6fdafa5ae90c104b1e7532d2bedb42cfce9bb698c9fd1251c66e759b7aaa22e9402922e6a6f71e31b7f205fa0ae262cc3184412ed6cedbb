{"_id": "is-string", "_rev": "18-7c926a50a0fd1bcb57c936324b033a6e", "name": "is-string", "dist-tags": {"latest": "1.1.1"}, "versions": {"1.0.0": {"name": "is-string", "version": "1.0.0", "keywords": ["String", "string", "ES6", "toStringTag", "@@toStringTag", "String object"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "is-string@1.0.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/is-string", "bugs": {"url": "https://github.com/ljharb/is-string/issues"}, "dist": {"shasum": "7f13aa94f67b7ed5c6c6884efd40192bf8b4a6c1", "tarball": "https://registry.npmjs.org/is-string/-/is-string-1.0.0.tgz", "integrity": "sha512-6SIfYVTmaaKLrmsqZy7kW7MLcGCbAJyS0i6jBWKq1iyTJoQDPT//Es3piZf0sjkmT1zuijDbUeXCLWOsXoKiSA==", "signatures": [{"sig": "MEYCIQD+tGp1ZHCghZs0FaavaubBV0K6AmjzyTnB+ZFMS2BYIwIhAMlSwF8bBHHvCW8egfRq2GncX80DwRd351eWdPtGamhr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "7f13aa94f67b7ed5c6c6884efd40192bf8b4a6c1", "engines": {"node": ">= 0.4"}, "gitHead": "b85f8e81b1b00643d732a91a28df2556b9019c0f", "scripts": {"jscs": "jscs test.js *.js", "lint": "npm run jscs && npm run eslint", "test": "npm run lint && node --harmony --es-staging test.js && npm run security", "eslint": "eslint test.js *.js", "eccheck": "editorconfig-tools check *.js **/*.js > /dev/null", "coverage": "covert test.js", "security": "nsp package", "coverage-quiet": "covert test.js --quiet"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/is-string.git", "type": "git"}, "_npmVersion": "2.3.0", "description": "Is this value a JS String object or primitive? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "directories": {}, "_nodeVersion": "1.0.4", "dependencies": {}, "devDependencies": {"is": "~2.2.0", "nsp": "~1.0.0", "jscs": "~1.10.0", "tape": "~3.4.0", "covert": "1.0.0", "eslint": "~0.13.0", "semver": "~4.2.0", "foreach": "~2.0.5", "indexof": "~0.0.1", "editorconfig-tools": "~0.0.1"}}, "1.0.1": {"name": "is-string", "version": "1.0.1", "keywords": ["String", "string", "ES6", "toStringTag", "@@toStringTag", "String object"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "is-string@1.0.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/is-string", "bugs": {"url": "https://github.com/ljharb/is-string/issues"}, "dist": {"shasum": "8fb31696250ff0cde959a1c77a46fc2ff04e1d29", "tarball": "https://registry.npmjs.org/is-string/-/is-string-1.0.1.tgz", "integrity": "sha512-WUy3ayRrU45LAVYoWTD3Qqmc2YTr3UifJE0HWTtKs1IPmVZKu6xHULKL3mxZoRkGGtyhbPGJB149s0i8Ttqhpg==", "signatures": [{"sig": "MEUCIEV6EqyGbdpDShHUsg83v4XUq1QN973jPyeMSdgn0O+UAiEAs7lq7jINGrujMdpBMAglm7pMQlNw/9bxfTt936ZLUMo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "8fb31696250ff0cde959a1c77a46fc2ff04e1d29", "engines": {"node": ">= 0.4"}, "gitHead": "cdcb9ff46f78715e8aea501520931a364b55cb38", "scripts": {"jscs": "jscs test.js *.js", "lint": "npm run jscs && npm run eslint", "test": "npm run lint && node --harmony --es-staging test.js && npm run security", "eslint": "eslint test.js *.js", "eccheck": "editorconfig-tools check *.js **/*.js > /dev/null", "coverage": "covert test.js", "security": "nsp package", "coverage-quiet": "covert test.js --quiet"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/is-string.git", "type": "git"}, "_npmVersion": "2.3.0", "description": "Is this value a JS String object or primitive? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "directories": {}, "_nodeVersion": "1.0.4", "dependencies": {}, "devDependencies": {"is": "~2.2.0", "nsp": "~1.0.1", "jscs": "~1.10.0", "tape": "~3.4.0", "covert": "1.0.1", "eslint": "~0.13.0", "semver": "~4.2.0", "foreach": "~2.0.5", "indexof": "~0.0.1", "editorconfig-tools": "~0.0.1"}}, "1.0.2": {"name": "is-string", "version": "1.0.2", "keywords": ["String", "string", "ES6", "toStringTag", "@@toStringTag", "String object"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "is-string@1.0.2", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/is-string", "bugs": {"url": "https://github.com/ljharb/is-string/issues"}, "dist": {"shasum": "24ed6b08eb6b37d1192d0b24d7c9f0097f8f8b41", "tarball": "https://registry.npmjs.org/is-string/-/is-string-1.0.2.tgz", "integrity": "sha512-UwrokKB/wJuNIikzyp97vqiHkgcDeWUkQjdAIxLOHcI0TKUDjCGly+TOqyeUOHCIXelI7fINSm0CEycESTo1Yg==", "signatures": [{"sig": "MEQCID0r8ZOVWUGbwKbpHDavOjHKfLfqq1b/9SWM81BCcEtGAiAKSePap7Cf7ED8JHu3nQI9Ds6S4Fol9CPSccnFxsW06Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "24ed6b08eb6b37d1192d0b24d7c9f0097f8f8b41", "engines": {"node": ">= 0.4"}, "gitHead": "9e147ee789890b3a9c5eb5270d300ae0fc9584a7", "scripts": {"jscs": "jscs test.js *.js", "lint": "npm run jscs && npm run eslint", "test": "npm run lint && node --harmony --es-staging test.js && npm run security", "eslint": "eslint test.js *.js", "eccheck": "editorconfig-tools check *.js **/*.js > /dev/null", "coverage": "covert test.js", "security": "nsp package", "coverage-quiet": "covert test.js --quiet"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/is-string.git", "type": "git"}, "_npmVersion": "2.3.0", "description": "Is this value a JS String object or primitive? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "directories": {}, "_nodeVersion": "1.0.4", "dependencies": {}, "devDependencies": {"is": "~2.2.0", "nsp": "~1.0.0", "jscs": "~1.10.0", "tape": "~3.4.0", "covert": "1.0.0", "eslint": "~0.13.0", "semver": "~4.2.0", "foreach": "~2.0.5", "indexof": "~0.0.1", "editorconfig-tools": "~0.0.1"}}, "1.0.4": {"name": "is-string", "version": "1.0.4", "keywords": ["String", "string", "ES6", "toStringTag", "@@toStringTag", "String object"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "is-string@1.0.4", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/is-string", "bugs": {"url": "https://github.com/ljharb/is-string/issues"}, "dist": {"shasum": "cc3a9b69857d621e963725a24caeec873b826e64", "tarball": "https://registry.npmjs.org/is-string/-/is-string-1.0.4.tgz", "integrity": "sha512-K8n0GHfQD7Xe5ns1rkbLDtE+lq2ezEVwF4Bc4FDBeHP2yZwwnud8iVAE/38EdEAz6aB5kraCjOcEow/brYQdaw==", "signatures": [{"sig": "MEUCIQDWKu0P0OaNLfkb7WezpPw/Sgh7JdZ6SS2AdlJh9Pgd2QIgaCCZZeTVRkR4ILznuSPgAUZ3u7cnx9YqdUn6QR+S3UA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "cc3a9b69857d621e963725a24caeec873b826e64", "engines": {"node": ">= 0.4"}, "gitHead": "bfc054522e27639dbd279a39bd7302fc859a1fd6", "scripts": {"jscs": "jscs test.js *.js", "lint": "npm run jscs && npm run eslint", "test": "npm run lint && node --harmony --es-staging test.js && npm run security", "eslint": "eslint test.js *.js", "eccheck": "editorconfig-tools check *.js **/*.js > /dev/null", "coverage": "covert test.js", "security": "nsp package", "coverage-quiet": "covert test.js --quiet"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/is-string.git", "type": "git"}, "_npmVersion": "2.3.0", "description": "Is this value a JS String object or primitive? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "directories": {}, "_nodeVersion": "1.0.4", "dependencies": {}, "devDependencies": {"is": "~2.2.0", "nsp": "~1.0.0", "jscs": "~1.10.0", "tape": "~3.4.0", "covert": "1.0.0", "eslint": "~0.13.0", "semver": "~4.2.0", "foreach": "~2.0.5", "indexof": "~0.0.1", "editorconfig-tools": "~0.0.1"}}, "1.0.5": {"name": "is-string", "version": "1.0.5", "keywords": ["String", "string", "ES6", "toStringTag", "@@toStringTag", "String object"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-string@1.0.5", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/is-string#readme", "bugs": {"url": "https://github.com/ljharb/is-string/issues"}, "dist": {"shasum": "40493ed198ef3ff477b8c7f92f644ec82a5cd3a6", "tarball": "https://registry.npmjs.org/is-string/-/is-string-1.0.5.tgz", "fileCount": 10, "integrity": "sha512-buY6VNRjhQMiF1qWDouloZlQbRhDPCebwxSjxMjxgemYT46YMd2NR0/H+fBhEfWX4A/w9TBJ+ol+okqJKFE6vQ==", "signatures": [{"sig": "MEYCIQD7BXmxcjlCbuLD7wJCmKVsTvi7SQaPTV6M7UIBvFio3wIhALdcwr6EKBSCQNhKbTV5Cghqj5kSwK7HHgR5LdZ0+ufo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15748, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd+wJlCRA9TVsSAnZWagAAIZwP/RhpzsytVwSHPJO1mmOt\n+oi+W8vddnmeiaw0E9UfMkG+qAx0T+j19ATZi9gTPd/mjQe8tRSnrTGySv22\niWOqzjL7tLHZZDYRgrpVzRQ2aTjN7/bANSdWB8kAJmqFA5BIRWKLBOPJKP9n\nf79dSQJVpy8SYH7XK/Upydu46Mi+18hYXQEz7/a4ICvXweaibyi7cbI8AUnh\nVb9GBmXOHGamHtgmvAVjxMf1ibIt7M0J9Jw3WYveVFr4pnHXkDOIePg6MarM\nVIcqsHXS26CI4oDnvGH5QMziwMecuNie8578bPACitJ78RTo1SLHxKMkYE60\n8o4NnmIzFQMw81JZJan146hMNPhoR42BgJaXhhF8iCYWHO0RNpwK+Lt1p+Sx\nhkfGfWVu5N3KPGo/M61HJEcL9JG/4YfMkYlqOAQu+Ssh25E6Lt71xyYyqyTQ\n+a9+VxQmpCbefPSrQ/BfFdCJTUxmhyLuYZTNXVV1eERf06eoC3YRQQwBL3kD\nQ4muIEgC1yFICJFN9KCRzBZQ88lKLRX6tsexisEVJRyKeCbM/MW+dJK5czKt\nWDkNMxRV54qRJSeq7uQdwf3PIfs3nv5/xRia8flY8FXbLG6lN7Efn+76BdkL\nWogmNGq/yINoV5vch30VEBAKJ1AQiagHIJ476fCYwGjVjFs6usQoqeU1tmce\nL96r\r\n=mp5t\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "fdf72b6bf93438b8e7a54345467ab8cf35ef56e2", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "eccheck": "eclint check *.js **/*.js > /dev/null", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "coverage": "covert test/index.js", "posttest": "npx aud", "prepublish": "safe-publish-latest", "tests-only": "node --harmony --es-staging test", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/is-string.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Is this value a JS String object or primitive? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "directories": {}, "_nodeVersion": "13.4.0", "dependencies": {}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"is": "^3.3.0", "tape": "^4.12.0", "covert": "^1.1.1", "eclint": "^2.8.1", "eslint": "^6.7.2", "foreach": "^2.0.5", "indexof": "^0.0.1", "auto-changelog": "^1.16.2", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^15.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/is-string_1.0.5_1576731236842_0.1626502503812357", "host": "s3://npm-registry-packages"}}, "1.0.6": {"name": "is-string", "version": "1.0.6", "keywords": ["String", "string", "ES6", "toStringTag", "@@toStringTag", "String object"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-string@1.0.6", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/is-string#readme", "bugs": {"url": "https://github.com/ljharb/is-string/issues"}, "dist": {"shasum": "3fe5d5992fb0d93404f32584d4b0179a71b54a5f", "tarball": "https://registry.npmjs.org/is-string/-/is-string-1.0.6.tgz", "fileCount": 10, "integrity": "sha512-2gdzbKUuqtQ3lYNrUTQYoClPhm7oQu4UdpSZMp1/DGgkHBT8E2Z1l0yMdb6D4zNAxwDiMv8MdulKROJGNl0Q0w==", "signatures": [{"sig": "MEQCICrXbPETeq4LYsdqgOiSR5uA2My4O+0s/1RhIu1vVhnJAiATubAjEdrDgKDcQQMyBU40qonx5X5U03j9fYVHBis3Mg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18683, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJglhOSCRA9TVsSAnZWagAAMNcQAINwpRWsW5l4nDhpmstQ\n0T8TUNIA0A6NncRA9QWkBXm1TqaHdeqVutcMUZLf8iUMPmWraYPdJO1v6xuQ\nWpSTU61upZnLVyQ9hLMvBwa3szZSDgPj4VdeO1SbrcNtnC6Tj6pv4m/mwNpa\nNDnCzpkNZr5XhThJzy9Ejs+vXeGvxK+KwZgpjrmQzn9dzHZ3tQk2II3YLTOK\nl/FFtptvABZUlKtnB2symg3pkX3FmLWKrzpBrkXgQnUMYxCKqGXKUgCRTDhq\nPFqD91bspMKqhFQleq6TavEHYfx8751POfGbnpr32CuzcGd4ZQnkHxL93TDF\nLrl7zC1tc+90IKkEyaA1OOsGUXt4vOn1JE2mvsk33yWAHF3sKV86d4GRQDOC\n4JmACyFzPl9tUV/QTvI5RQAWL8ObTD4krSYSAI/7g91fA3+Qtaj4swVkgs9c\nKQBtKTddh6O+/UCwZHDdqmYxPy9KTB0vhPMTiEkhv/9eVO0I4Osb46yp77YT\nkc/IIBYAI1vk3dG3oiePfaWBj2O2bQgbyMxIBmJb6Qd1XCjrmUO2ACwpDGxu\nJI4IuGhkQ7owkJyc+N2DyCUaNgZnAbyfJfjFwMjjxwVfHuX0M3Jofuf/n3L/\nAET8FEbDGaxP5tXTJ/4o1hYEUtWRnCjj7zF57oQVX19kFTwGCYhr5Fbc5aVa\n0sKS\r\n=v1+S\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "19b18d18fb869a4aba24cd138d5a14db18781007", "scripts": {"lint": "eslint .", "test": "npm run tests-only && npm run test:corejs", "eccheck": "eclint check *.js **/*.js > /dev/null", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "npx aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "test:corejs": "nyc tape test-corejs.js", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/is-string.git", "type": "git"}, "_npmVersion": "7.10.0", "description": "Is this value a JS String object or primitive? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "directories": {}, "_nodeVersion": "16.0.0", "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"is": "^3.3.0", "aud": "^1.1.5", "nyc": "^10.3.2", "tape": "^5.2.2", "eclint": "^2.8.1", "eslint": "^7.26.0", "core-js": "^3.12.0", "foreach": "^2.0.5", "indexof": "^0.0.1", "auto-changelog": "^2.2.1", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-string_1.0.6_1620448145714_0.7428584206574895", "host": "s3://npm-registry-packages"}}, "1.0.7": {"name": "is-string", "version": "1.0.7", "keywords": ["String", "string", "ES6", "toStringTag", "@@toStringTag", "String object"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-string@1.0.7", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/is-string#readme", "bugs": {"url": "https://github.com/ljharb/is-string/issues"}, "dist": {"shasum": "0dd12bf2006f255bb58f695110eff7491eebc0fd", "tarball": "https://registry.npmjs.org/is-string/-/is-string-1.0.7.tgz", "fileCount": 10, "integrity": "sha512-tE2UXzivje6ofPW7l23cjDOMa09gb7xlAqG6jG5ej6uPV32TlWP3NKPigtaGeHNu9fohccRYvIiZMfOOnOYUtg==", "signatures": [{"sig": "MEUCIQDrMR5LYqOEuY+76J6tOnn3Vf2LWzL/OqelV5Okd7It7gIgeh1mf1KCSI3ssw2vOVfMQYHzFdZgPI/EsHgDexBe32g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19127, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhDI/KCRA9TVsSAnZWagAA2MUP/3j1XMf0sNf2RkuI0d41\nM4tsQeyS4Q/do4DjTDLtee+86wNaC6mILVMzDRp1ljjq5VXYt4TArxY8UYHN\n1FzMiHSNT8F2EDE020f0fr7/oLuQk4acwS281wo0sHaOQ1JM30TZ3viwkJwi\nPd3XWOIgtfOxEySMuTnfCpfbkSbhH9nSirt9rvX45RXaAiE0aFNpnHDeLJWQ\nUrP7pOAmXf1fv9Mbdf85Pc/Rk8kkGh8VGAyQnnG2l/oeeL+qv1G2uYB9KYJ1\n3DvuVD3YGFQYoz1yQqHEKrX8XyYMYYPrPVxx6s+kvgi8ojU1LwZDOgrIEg3i\nO+aPjsjRfcl1ZOBKRh8Qs1DagJ3c2qBnWGfqxGDkaX0mrFhQ3piMkWLsGy5a\ntaBw+TcMOdS+UI/n5z/efNIhRWr8u7tg8T9iNChCJ7Oa5u55NEe9/oMx8S2I\naBOgbBu3P/H7A24gOAWRzI9IQ1ktiJ6TRItmcwNSUVifkXmpKreTy3DTGrTh\nbRM3GWbsdIfAW4HAtOqNbLGQqdJ/avI2zDxQwKNV+A0Cie3QN2Gg/GX3oxtK\nOKdNWKl6aVWbvwkqhk6kP6H8NFRJ/RF1znpQwH/Xx8BzVpDRD9LnCYaVplDY\n5LR3c2Je+TuHlAs5rlXbIDQZo71PF+sA6LcdZxD7bIWo7KFUHbJUkSCzixqs\nWPwh\r\n=/ypc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "e8a5b6d659f59e744ae03b2557e0482cd69194b3", "scripts": {"lint": "eslint .", "test": "npm run tests-only && npm run test:corejs", "eccheck": "eclint check *.js **/*.js > /dev/null", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "npx aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "test:corejs": "nyc tape test-corejs.js", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/is-string.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Is this value a JS String object or primitive? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "directories": {}, "_nodeVersion": "16.6.1", "dependencies": {"has-tostringtag": "^1.0.0"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"is": "^3.3.0", "aud": "^1.1.5", "nyc": "^10.3.2", "tape": "^5.3.0", "eclint": "^2.8.1", "eslint": "^7.32.0", "core-js": "^3.16.0", "foreach": "^2.0.5", "indexof": "^0.0.1", "auto-changelog": "^2.3.0", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-string_1.0.7_1628213194121_0.927164020122276", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "is-string", "version": "1.1.0", "keywords": ["String", "string", "ES6", "toStringTag", "@@toStringTag", "String object"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-string@1.1.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-string#readme", "bugs": {"url": "https://github.com/inspect-js/is-string/issues"}, "dist": {"shasum": "8cb83c5d57311bf8058bc6c8db294711641da45d", "tarball": "https://registry.npmjs.org/is-string/-/is-string-1.1.0.tgz", "fileCount": 11, "integrity": "sha512-PlfzajuF9vSo5wErv3MJAKD/nqf9ngAs1NFQYm16nUYFO2IzxJ2hcm+IOCg+EEopdykNNUhVq5cz35cAUxU8+g==", "signatures": [{"sig": "MEUCIE1Gdep3Y/d8JkbCCQKpIkE3mxKyVh+cTzwUR0/WMdeXAiEAr56thvP+KbD1OZt0EONAjEdrOgqyTCE57i29Nbk/MMQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23487}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "9621fdcbb70a114eee8b4cb43e0d44e42f867cd6", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only && npm run test:corejs", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "tsc -p . && attw -P", "posttest": "npx npm@'>=10.2' audit --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "test:corejs": "nyc tape test-corejs.js", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/inspect-js/is-string.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Is this value a JS String object or primitive? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "directories": {}, "_nodeVersion": "23.3.0", "dependencies": {"call-bind": "^1.0.7", "has-tostringtag": "^1.0.2"}, "publishConfig": {"ignore": [".github/workflows", "test-corejs.js"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"is": "^3.3.0", "nyc": "^10.3.2", "tape": "^5.9.0", "eclint": "^2.8.1", "eslint": "=8.8.0", "core-js": "^3.39.0", "encoding": "^0.1.13", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "next", "@types/tape": "^5.6.5", "@types/core-js": "^2.5.8", "auto-changelog": "^2.5.0", "@ljharb/tsconfig": "^0.2.0", "@types/call-bind": "^1.0.5", "safe-publish-latest": "^2.0.0", "@arethetypeswrong/cli": "^0.17.0", "@ljharb/eslint-config": "^21.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/is-string_1.1.0_1733115228459_0.11226985895509523", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "is-string", "version": "1.1.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "description": "Is this value a JS String object or primitive? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "license": "MIT", "main": "index.js", "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test:corejs": "nyc tape test-corejs.js", "test": "npm run tests-only && npm run test:corejs", "posttest": "npx npm@'>=10.2' audit --production", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && attw -P", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/inspect-js/is-string.git"}, "keywords": ["String", "string", "ES6", "toStringTag", "@@toStringTag", "String object"], "dependencies": {"call-bound": "^1.0.3", "has-tostringtag": "^1.0.2"}, "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/core-js": "^2.5.8", "@types/tape": "^5.7.0", "auto-changelog": "^2.5.0", "core-js": "^3.39.0", "eclint": "^2.8.1", "encoding": "^0.1.13", "eslint": "=8.8.0", "in-publish": "^2.0.1", "is": "^3.3.0", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "publishConfig": {"ignore": [".github/workflows", "test-corejs.js"]}, "_id": "is-string@1.1.1", "gitHead": "fbbcdaed74d18b6b6899b0a6e6310f5ce31d70c7", "types": "./index.d.ts", "bugs": {"url": "https://github.com/inspect-js/is-string/issues"}, "homepage": "https://github.com/inspect-js/is-string#readme", "_nodeVersion": "23.4.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-BtEeSsoaQjlSPBemMQIrY1MY0uM6vnS1g5fmufYOtnxLGUZM2178PKbhsk7Ffv58IX+ZtcvoGwccYsh0PglkAA==", "shasum": "92ea3f3d5c5b6e039ca8677e5ac8d07ea773cbb9", "tarball": "https://registry.npmjs.org/is-string/-/is-string-1.1.1.tgz", "fileCount": 11, "unpackedSize": 24001, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCzfCzMVjLe6vdpsgEY953VMyOXYF/FaKLpQE7V5Ib53AIgOlsz0vkTESkY8t3j06H2UowO/GxdsMFkTWNAOJn9PE4="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/is-string_1.1.1_1734317940245_0.671461883620325"}, "_hasShrinkwrap": false}}, "time": {"created": "2015-01-29T08:31:52.824Z", "modified": "2024-12-16T02:59:00.595Z", "1.0.0": "2015-01-29T08:31:52.824Z", "1.0.1": "2015-01-29T17:40:10.387Z", "1.0.2": "2015-01-29T17:46:47.721Z", "1.0.4": "2015-01-30T07:47:09.149Z", "1.0.5": "2019-12-19T04:53:57.008Z", "1.0.6": "2021-05-08T04:29:05.860Z", "1.0.7": "2021-08-06T01:26:34.304Z", "1.1.0": "2024-12-02T04:53:48.619Z", "1.1.1": "2024-12-16T02:59:00.418Z"}, "bugs": {"url": "https://github.com/inspect-js/is-string/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/inspect-js/is-string#readme", "keywords": ["String", "string", "ES6", "toStringTag", "@@toStringTag", "String object"], "repository": {"type": "git", "url": "git://github.com/inspect-js/is-string.git"}, "description": "Is this value a JS String object or primitive? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "readme": "# is-string <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![dependency status][deps-svg]][deps-url]\n[![dev dependency status][dev-deps-svg]][dev-deps-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\nIs this value a JS String object or primitive? This module works cross-realm/iframe, and despite ES6 @@toStringTag.\n\n## Example\n\n```js\nvar isString = require('is-string');\nvar assert = require('assert');\n\nassert.notOk(isString(undefined));\nassert.notOk(isString(null));\nassert.notOk(isString(false));\nassert.notOk(isString(true));\nassert.notOk(isString(function () {}));\nassert.notOk(isString([]));\nassert.notOk(isString({}));\nassert.notOk(isString(/a/g));\nassert.notOk(isString(new RegExp('a', 'g')));\nassert.notOk(isString(new Date()));\nassert.notOk(isString(42));\nassert.notOk(isString(NaN));\nassert.notOk(isString(Infinity));\nassert.notOk(isString(new Number(42)));\n\nassert.ok(isString('foo'));\nassert.ok(isString(Object('foo')));\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n[package-url]: https://npmjs.org/package/is-string\n[npm-version-svg]: https://versionbadg.es/inspect-js/is-string.svg\n[deps-svg]: https://david-dm.org/inspect-js/is-string.svg\n[deps-url]: https://david-dm.org/inspect-js/is-string\n[dev-deps-svg]: https://david-dm.org/inspect-js/is-string/dev-status.svg\n[dev-deps-url]: https://david-dm.org/inspect-js/is-string#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/is-string.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/is-string.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/is-string.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=is-string\n[codecov-image]: https://codecov.io/gh/inspect-js/is-string/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/inspect-js/is-string/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/inspect-js/is-string\n[actions-url]: https://github.com/inspect-js/is-string/actions\n", "readmeFilename": "README.md"}