{"_id": "node-addon-api", "_rev": "71-941e31e017d051539a44d0b8a3631e50", "name": "node-addon-api", "dist-tags": {"latest": "8.3.0"}, "versions": {"0.1.0": {"name": "node-addon-api", "version": "0.1.0", "license": "MIT", "_id": "node-addon-api@0.1.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}], "homepage": "https://github.com/nodejs/node-api", "bugs": {"url": "https://github.com/nodejs/node-api/issues"}, "dist": {"shasum": "f0a473c8e462773ff01a1e2be375178c4de39af1", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-0.1.0.tgz", "integrity": "sha512-ysffDGPYHfpaVmMi/QCWmd5vKxadnc0c4Zg9c/ElsE/spGuPHlvvfXkFbspdEenMAEYs3eFkt96eUcycMxQaGA==", "signatures": [{"sig": "MEUCIQCLJpcW4lMBddVhf6jkqH86/TCPNBIwDx9CGF60r+ENBAIgegcZaCWv3JfXtY3w2GboHyq6yNOfi2DrmZO2FGXuZ9Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "f0a473c8e462773ff01a1e2be375178c4de39af1", "gitHead": "53b0b8b9ce55919e36f0006b36c91217b6f56816", "scripts": {"test": "node --expose-gc test", "pretest": "node-gyp rebuild -C test"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/node-api.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Node.js API (N-API)", "directories": {}, "_nodeVersion": "6.10.1", "dependencies": {}, "devDependencies": {"node-gyp": "^3.6.0"}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api-0.1.0.tgz_1493939539921_0.5477822034154087", "host": "packages-12-west.internal.npmjs.com"}}, "0.2.0": {"name": "node-addon-api", "version": "0.2.0", "license": "MIT", "_id": "node-addon-api@0.2.0", "maintainers": [{"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "cf0f8ecd37b5c4088b8065c138730ad4f6629264", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-0.2.0.tgz", "integrity": "sha512-OprZzZqB2Pn9GSo4FXum3l1b5Q1JNg1D/ABKmxAcBM00wSv5yzV6Gm94iDHmYd11NtFVVFC+BbHpgaIw74lB4g==", "signatures": [{"sig": "MEYCIQDvpIcmhm7bifpIuR48mykEFWx8dmmgrotwCfLs2fBOCQIhAPrPO9mVqa+ut0EpyIfN7Oqf40EoJRsfRhUAE1yc0SqW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "cf0f8ecd37b5c4088b8065c138730ad4f6629264", "gitHead": "df3c99ecf3254cb8cc7fc66b247f4ad3f73d849e", "scripts": {"test": "node --expose-gc test", "pretest": "node-gyp rebuild -C test"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Node.js API (N-API)", "directories": {}, "_nodeVersion": "6.10.1", "dependencies": {}, "devDependencies": {"node-gyp": "^3.6.0"}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api-0.2.0.tgz_1495133200671_0.6417490739841014", "host": "packages-12-west.internal.npmjs.com"}}, "0.3.0": {"name": "node-addon-api", "version": "0.3.0", "license": "MIT", "_id": "node-addon-api@0.3.0", "maintainers": [{"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "9f78149f519144cb47dde3e658bb395c835d610f", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-0.3.0.tgz", "integrity": "sha512-XTR4rYvYFWIEb91Lo8+Wi+2C2QJADI29cVkxMAP+Tq7DixkGmGv0pOW5nc6oF2QwPrg5+Sfg9CqWUYgxuHY4OA==", "signatures": [{"sig": "MEQCICQ54ZKBcZ8an93hJj4bDpYYvhXnopVSmagFrvWh2hLVAiA6SDBba3l1I9ynGqKCGJ9HZ38zxvS2nhJwdBf3hyTwgw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "9f78149f519144cb47dde3e658bb395c835d610f", "gitHead": "b3531140475d7c11d8ad4eac930e56f9b96f93c0", "scripts": {"test": "node test", "pretest": "node-gyp rebuild -C test"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "4.5.0", "description": "Node.js API (N-API)", "directories": {}, "_nodeVersion": "8.0.0-nightly201705185de722ab6d", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api-0.3.0.tgz_1495561712478_0.711710260482505", "host": "s3://npm-registry-packages"}}, "0.3.1": {"name": "node-addon-api", "version": "0.3.1", "license": "MIT", "_id": "node-addon-api@0.3.1", "maintainers": [{"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "3ac3393e5b2b778549e0d99c1b8a32f79ce05922", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-0.3.1.tgz", "integrity": "sha512-/8kB0PYn5lMHDt8k8OmuUIkP2PoiYbQSqjGZNZDR2iriK4cNHutN56nun7ogtMWXhmU1HMR/SnnDUfSWxRdabg==", "signatures": [{"sig": "MEYCIQCk8cz1YR8CAk+H02fl79TU3kH4H275xFBQXYrRXqdffQIhAOu30LyW1rZ7nUIkIHfNtXvn2WluSeuQAlSp7Q3j027c", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "22416e8dcba25ff6ff1a600097ee9fd8ac30f875", "scripts": {"test": "node test", "pretest": "node-gyp rebuild -C test"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "5.0.0-beta.56", "description": "Node.js API (N-API)", "directories": {}, "_nodeVersion": "8.0.0-nightly20170524260cd411d4", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api-0.3.1.tgz_1495691385433_0.4386101649142802", "host": "s3://npm-registry-packages"}}, "0.3.4": {"name": "node-addon-api", "version": "0.3.4", "license": "MIT", "_id": "node-addon-api@0.3.4", "maintainers": [{"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "af3259040fc861c1812886ddd149b36d3aa63183", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-0.3.4.tgz", "integrity": "sha512-gZ7oL1NuwRwq01zde4MeRjSGMvxU2TsJ0l6anf2yHMoSc3b43wBaSzGpme/Jbul+CAmQONIIrONVp0cpaJCarg==", "signatures": [{"sig": "MEYCIQCTm+aqZfdgqGRAejUPJZU9N2XC5mZ3VKJCvZ3zHLdHHAIhAINDV1OlvnfuWTquUYea+pCEpKl+RrO9Odl82oM79sNB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "3aab97b2bd19563a851ac223c8220e3ca3d05661", "scripts": {"test": "node test", "pretest": "node-gyp rebuild -C test"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "Node.js API (N-API)", "directories": {}, "_nodeVersion": "8.1.0", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api-0.3.4.tgz_1497388769678_0.06508532632142305", "host": "s3://npm-registry-packages"}}, "0.3.5": {"name": "node-addon-api", "version": "0.3.5", "license": "MIT", "_id": "node-addon-api@0.3.5", "maintainers": [{"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "67e7a4abba26918561efa383fa5be4c91525c48b", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-0.3.5.tgz", "integrity": "sha512-TKtnrlB7rq/v9m4u3gAHt3O1YREgo4x/pgOucxUWb1bTEvte1+vAY1JXExK6WlXjMOnquX0vzejR3mZMt4AT9w==", "signatures": [{"sig": "MEQCIF2x7RzFA11i42uXsK37MKf/+GetGl0B8Xh1Vd+R2iOcAiBSQglYwwk8JrzowFXZn94ywK0fobx/XvlBWUhesrYMww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "67e7a4abba26918561efa383fa5be4c91525c48b", "gitHead": "b1ef1b7df542266691d4f43eaaffb54c4c1fa587", "scripts": {"test": "node test", "pretest": "node-gyp rebuild -C test"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Node.js API (N-API)", "directories": {}, "_nodeVersion": "6.9.4", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api-0.3.5.tgz_1498505964341_0.5701525383628905", "host": "s3://npm-registry-packages"}}, "0.4.0": {"name": "node-addon-api", "version": "0.4.0", "license": "MIT", "_id": "node-addon-api@0.4.0", "maintainers": [{"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "15e5256750ca0682c5f4060302f802097f753740", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-0.4.0.tgz", "integrity": "sha512-yMDJsQiaA01fhm6MO/tBqLXkXUf1FW40+JQ90Tm+RbKmewVGMZR4jD7ZK99Zse+/YC4723r3aJ6h7wZbKRdH3g==", "signatures": [{"sig": "MEUCIQCwQJFTP/OHH5flBPma+v2lGaYqmTUxQMx4PcpOP0/1igIgFdXsHEFuzFWgF6SLQx5Mx/psZVKYWGZP2zTHWVXwOS8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "10ef2932508ab6a5575707fc060f966fb9b25cd4", "scripts": {"test": "node test", "pretest": "node-gyp rebuild -C test"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "Node.js API (N-API)", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api-0.4.0.tgz_1499729735037_0.8656034101732075", "host": "s3://npm-registry-packages"}}, "0.5.0": {"name": "node-addon-api", "version": "0.5.0", "license": "MIT", "_id": "node-addon-api@0.5.0", "maintainers": [{"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "f378d18447cc33a7190d689a0e78d8e059c3c0eb", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-0.5.0.tgz", "integrity": "sha512-c0IqEJ4gqRk2r2XhZIPRhQ+KjTZERXYrd9VrPs50kPhwjDFOQH6CLwHb69tWJH3+FeQR11FXaN86/OK4MmFwHw==", "signatures": [{"sig": "MEUCIQC7cH7m+8nAC5wZyVQ5QVdS0BPcf7olguAnXK7CQh9ocgIgIBo7TJ8VozGC1kEW07nELHVfC6AuokWngU5HybZnVIw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "d455c5215c7bbf5a19abc2d34f11f60077b72d55", "scripts": {"test": "node test", "pretest": "node-gyp rebuild -C test"}, "_npmUser": {"name": "mhdawson1", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "Node.js API (N-API)", "directories": {}, "_nodeVersion": "9.0.0-pre", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api-0.5.0.tgz_1500394879124_0.3414635672233999", "host": "s3://npm-registry-packages"}}, "0.5.1": {"name": "node-addon-api", "version": "0.5.1", "license": "MIT", "_id": "node-addon-api@0.5.1", "maintainers": [{"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "b1bf4f6d91e4d10ae5d697fbcc5b0a57886fc6a2", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-0.5.1.tgz", "integrity": "sha512-3//80u0PIUHvozitcQ/DBaaS46qa4iO9FNyQEGPybMpRyCVXyKz/I64Eok0pu+vIRriqqHyL4nhlnuTwC0d9ow==", "signatures": [{"sig": "MEYCIQCqeGe9fZu0JLQ7FxcBV1gGPKnp2X3Jj7TIUBh1yC2nSAIhAPg/xBXLQYfaJ+Q2BNx1X62Nvq4Wx2F8A1t3ehfzw+AH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "0986f83e57307e38c1aac6aa9bfe230bdfe095e2", "scripts": {"test": "node test", "pretest": "node-gyp rebuild -C test"}, "_npmUser": {"name": "mhdawson1", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "Node.js API (N-API)", "directories": {}, "_nodeVersion": "9.0.0-pre", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api-0.5.1.tgz_1500413548729_0.030465385410934687", "host": "s3://npm-registry-packages"}}, "0.6.2": {"name": "node-addon-api", "version": "0.6.2", "license": "MIT", "_id": "node-addon-api@0.6.2", "maintainers": [{"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "54f2719ca4a618b4eed70224181f0b816373325e", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-0.6.2.tgz", "integrity": "sha512-bMxUO1Fo50bb3B23G7ivNtwmMZfm262xxrkrDn9OSLZ+3eDatp0/vab5nPintawCnls3gA8LXY2wpSbPMFfyKw==", "signatures": [{"sig": "MEUCIFVVbw7h0rWhVVKmL4AJYPt6KreDXLbsUyezsNAEDKRMAiEAx+eYnLbc/USkdlxwxfxdK2FRKL1rUgNfz980Ea3h0hM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "ca9254832e15494ede96249302ba93ec2fb51720", "scripts": {"test": "node test", "pretest": "node-gyp rebuild -C test"}, "_npmUser": {"name": "mhdawson1", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Node.js API (N-API)", "directories": {}, "_nodeVersion": "9.0.0-pre", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api-0.6.2.tgz_1503329262827_0.9881750061176717", "host": "s3://npm-registry-packages"}}, "0.6.3": {"name": "node-addon-api", "version": "0.6.3", "license": "MIT", "_id": "node-addon-api@0.6.3", "maintainers": [{"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "3905f0876eaa391d19e0cfdc7f10661e1897a475", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-0.6.3.tgz", "integrity": "sha512-FikxTt0OaQ716zlsKKxZoRRyd0aAOJNRKK2ne4dS2R4FoEJXb7HyyxDTnYnLc22u0N2S2wiYuRm2gvehEdn4zQ==", "signatures": [{"sig": "MEYCIQCW0xnoiYxXzk+OfgtJTsQUlzKIZHjzrPcTwZOrH8021QIhAK4nP+uFvWD3pMsCapBqE59fh341URWNgtDrPKaiVnXB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "790de9f473d05c2a4a3c0eeddbc6415dfd47bed3", "scripts": {"doc": "doxygen doc/Doxyfile", "test": "node test", "pretest": "node-gyp rebuild -C test"}, "_npmUser": {"name": "mhdawson1", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Node.js API (N-API)", "directories": {}, "_nodeVersion": "9.0.0-pre", "dependencies": {}, "devDependencies": {"safe-buffer": "^5.1.1"}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api-0.6.3.tgz_1503686688126_0.5833845392335206", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "node-addon-api", "version": "1.0.0", "license": "MIT", "_id": "node-addon-api@1.0.0", "maintainers": [{"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "ce62bc6865f60f8d65c0441f2d1a5bdba5cda590", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-1.0.0.tgz", "integrity": "sha512-7hQ0EKaQFt6plfAuTHfG5Vh5kCN+pyEZvjG0Xal3Tzv19spQnhIrh8IP7+UxbvJ0meTbktgrlN+tR5kFr14yZg==", "signatures": [{"sig": "MEMCHy7zNWxw/3uIlWpZgUEJHETHDVmgvY0BXbV07pHw4hcCIGwprZ718RDtrp5Dicvwtnq9awsWVmMu2YN+IbfkuOIl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "ce62bc6865f60f8d65c0441f2d1a5bdba5cda590", "gitHead": "82c2fa5a20a4c76ebd656a5aa676c34ac6ca1a28", "scripts": {"doc": "doxygen doc/Doxyfile", "test": "node test", "pretest": "node-gyp rebuild -C test"}, "_npmUser": {"name": "mhdawson1", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Node.js API (N-API)", "directories": {}, "_nodeVersion": "6.11.3", "dependencies": {}, "devDependencies": {"safe-buffer": "^5.1.1"}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api-1.0.0.tgz_1506518924654_0.23994539212435484", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "node-addon-api", "version": "1.1.0", "license": "MIT", "_id": "node-addon-api@1.1.0", "maintainers": [{"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/kirbysayshi", "name": "<PERSON>"}, {"url": "https://github.com/anisha-rohra", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/davedoesdev", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/romandev", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/koistya", "name": "<PERSON>"}, {"url": "https://github.com/kfarnung", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/NickNaso", "name": "<PERSON>"}, {"url": "https://github.com/rolft<PERSON>mans", "name": "<PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "9f14bf703b4f5cc1bfe6bc4a30d8c8280b2d87b9", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-1.1.0.tgz", "integrity": "sha512-WSGZ/xdGcyhNY2neCHS/JKQXdbOU5qZp8EGwxpEWSqOmI9+sCwO93npSH93ipa4z13+KZhGUgA5g/EAKH/F+Wg==", "signatures": [{"sig": "MEYCIQCUGfqLt/jq49fA1T9aUK8F85F/tgFkc4EW6s4WUoaxhQIhAL3yDBPZktQothLm73k1vQdvBwc7JcpSLNBDtrDyNrl8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "128ca1b21e1b04279505591177df295e39dd83f5", "scripts": {"doc": "doxygen doc/Doxyfile", "test": "node test", "pretest": "node-gyp rebuild -C test"}, "_npmUser": {"name": "mhdawson1", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Node.js API (N-API)", "directories": {}, "_nodeVersion": "8.9.0", "dependencies": {}, "devDependencies": {"safe-buffer": "^5.1.1"}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api-1.1.0.tgz_1511192866461_0.5753951275255531", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "node-addon-api", "version": "1.2.0", "license": "MIT", "_id": "node-addon-api@1.2.0", "maintainers": [{"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/kirbysayshi", "name": "<PERSON>"}, {"url": "https://github.com/anisha-rohra", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/davedoesdev", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/romandev", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/koistya", "name": "<PERSON>"}, {"url": "https://github.com/kfarnung", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/NickNaso", "name": "<PERSON>"}, {"url": "https://github.com/rolft<PERSON>mans", "name": "<PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "2d378bbed60cbb7b9e2c505c6833eed8723f41c4", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-1.2.0.tgz", "integrity": "sha512-VDpTV5VK4kCqTfK5I7JAphXrL3txLbfshhjtscdYTuI4PkGvjWBFxNkctYdcRsMwgObyqVcm12tsiKZD76/d8g==", "signatures": [{"sig": "MEUCIGTVdpq4i5Bzk2yspj2CcxINPVhrGNI1AOzE8MB7tRrpAiEAr0l0F91Kxf8Maiw2mQDrjL9kDA+7Jsi07MhsL/8XXaA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "4b5a261d804574f77d426fd2093d4389288ac163", "scripts": {"doc": "doxygen doc/Doxyfile", "test": "node test", "pretest": "node-gyp rebuild -C test"}, "_npmUser": {"name": "mhdawson1", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Node.js API (N-API)", "directories": {}, "_nodeVersion": "8.9.0", "dependencies": {}, "devDependencies": {"safe-buffer": "^5.1.1"}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api-1.2.0.tgz_1515712231150_0.22229782515205443", "host": "s3://npm-registry-packages"}}, "1.3.0": {"name": "node-addon-api", "version": "1.3.0", "license": "MIT", "_id": "node-addon-api@1.3.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/kirbysayshi", "name": "<PERSON>"}, {"url": "https://github.com/anisha-rohra", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/corymickelson", "name": "<PERSON>"}, {"url": "https://github.com/davedoesdev", "name": "<PERSON>"}, {"url": "https://github.com/ebickle", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/jschlight", "name": "<PERSON>"}, {"url": "https://github.com/romandev", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/koistya", "name": "<PERSON>"}, {"url": "https://github.com/kfarnung", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/kYroL01", "name": "Michele Campus"}, {"url": "https://github.com/NickNaso", "name": "<PERSON>"}, {"url": "https://github.com/rolft<PERSON>mans", "name": "<PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "9280a6afd4d0bd9401ef3c60b936beeea0a6b3d3", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-1.3.0.tgz", "fileCount": 61, "integrity": "sha512-yagD4yKkZLeG4EJkh+8Qbqhqw+owDQ/PowqD8vb5a5rfNXS/PRC21SGyIbUVXfPp/jl4s+jyeZj6xnLnDPLazw==", "signatures": [{"sig": "MEUCICI+WM+KzSnXU5R0cexT7jrTnXafI4Anx8hChxQaCZYnAiEAwTJW/2bRbEaCqouIE5lKBnlTJggimtpNMalm9hqQfSE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 503682, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8f0wCRA9TVsSAnZWagAA0WkQAJGJc3oPVB9O/VGLaJDJ\nL6OsMu9vMb/gQFnTaZ4SCdNOF2RVpopO0TydFBkIRPN5ZjkhoRYVi32oo+aD\nDckmqh+slP4YrC2WZYmXRmIbCdStNTkVFqR5yPcNepdPdgmzsIrXrmLPhFDH\nWY5KLoewAlSTEpkGFiXRkzKDiQ5POCKQ7kYWrkAxJuZCTodSp4Gk4AVo9Qbx\n7Aj5X5Ntvug03ETE0QHIFNEnLI0WoPANE5C+JbxIWvYwEcKD9RJ6Ytr0KSS9\nft/+/h2PANj/kSztU8uw9TihE+6f/hKQrA75TmoCc95wXh+NKrdXS6M4pdU7\nGDU7b/5bgwRR/WvhEX1J13vGjJvIJiw547UFjQInqv4b3W6NQ5E1ICM0tg6w\nXM7sgxEv5o0QmezYrAGLWDzW/gte2VK0esE8JhZFkzDxbVmGKCMuHcfB/1E3\nVdnZnfbFmtf2TmkqmTxLOrdxuhREXbSPL4NQncfZlYPzaF0KQN1AcmcC/gFM\nuKnqYW4/v4LpvsU+Q3Bu9LZDo2sSB4xax6DvSAb/uiUqiYqxsaZJiMEje972\nRuVgHqU8ccd4+l54HmmpPLmmUI3blMs0huGkYDxLrJobCrieSd+ad3pF0l62\nHO565+tLlIeNHOOL/wm3nLHGKcL+l1fseFeyqMd4YKmfwPIYxg6BZ6ZSU1ae\n9nHV\r\n=R+Nb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "a750ed1932e4b03c457b7b93e08cb07ec3ae8201", "scripts": {"doc": "doxygen doc/Doxyfile", "test": "node test", "pretest": "node-gyp rebuild -C test"}, "_npmUser": {"name": "mhdawson1", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Node.js API (N-API)", "directories": {}, "_nodeVersion": "11.0.0-pre", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"safe-buffer": "^5.1.1"}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api_1.3.0_1525808431928_0.4385847245093317", "host": "s3://npm-registry-packages"}}, "1.4.0": {"name": "node-addon-api", "version": "1.4.0", "license": "MIT", "_id": "node-addon-api@1.4.0", "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/kirbysayshi", "name": "<PERSON>"}, {"url": "https://github.com/anisha-rohra", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rivertam", "name": "<PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/corymickelson", "name": "<PERSON>"}, {"url": "https://github.com/davedoesdev", "name": "<PERSON>"}, {"url": "https://github.com/ebickle", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/jschlight", "name": "<PERSON>"}, {"url": "https://github.com/romandev", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/joshgarde", "name": "j<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/koistya", "name": "<PERSON>"}, {"url": "https://github.com/kfarnung", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/kYroL01", "name": "Michele Campus"}, {"url": "https://github.com/NickNaso", "name": "<PERSON>"}, {"url": "https://github.com/iSkore", "name": "<PERSON>"}, {"url": "https://github.com/rolft<PERSON>mans", "name": "<PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "4c43e4c1ed8fbf3176ce71003f352329adad87eb", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-1.4.0.tgz", "fileCount": 62, "integrity": "sha512-agquHPHnxYGox7Rjz2+TZQeOiH8IVbNFSTyTPA+peMUAP6klgrBH5dcwHsNNChQh7l/dtF0JNmZPbCqd5OXOIQ==", "signatures": [{"sig": "MEUCIQDbWXY+pLNVcFp0GuruyDh1cbWIc12Q4yTw/kRZ8pObBgIgaWGSqg8nWfZaOgBk34bic8iVjx1d3DjhOFkKcQAH7VU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 575688, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUQ1+CRA9TVsSAnZWagAAzAMP/3qMiC7GFK4oXH6Gd/To\nmRYUazuAUCyMq1UMueqmst/9zUV7EdXMtmTrue871w1KH7pzZFddFw1egS6U\notGR4r9ca/3THGKjj2Wl6PEdWEU64rlpiYhIstrNi4tp6rCs3zhuUQnptbXy\nCdW8MWRLXyBdXexG9R+dwO9Aut4CScPCOa2Q2oWCM1VmLg6qjahfTwKEcB63\nC9A0eGSL/JfxPNdiUFMN5JSmI8u0SV7qz2AFipD9S68ZqzeAkgIVUSQd71B5\nlinsTfzwGppTAWnFGu+8jQ0C0Zwbhlv19n68l/pfDc+iLeWgXYSCoU6LN150\nvcaUVwVH1sdg7S0CNqMqFmmZlBFzXjVP+KD649XpaOr3lWC/3YZWB52JDYZI\nHqkCRBk9GGveLz67dKv5F52HckHpHExfVAjAnOSCwyfNZSvJPZjBpXPdyy0G\n52R5QsfS64kr/e00cBUciuMUpRroLjGrTM28FmA/FB4zJrsouBSmNnJ0xTNi\nRhvU68TVhqCsCv/rKb8x2Cad0OvGsnQPEGeHQz3SZ24zYU2qYfxOrrKBOFQC\nufLm+clFTLKCFLXPKK3ihkBM5s3Fow18Yc0HSisOquLGC2zP2imC+4kEmTkX\nCVtuo+2k4nmmZJ3NhO6DJ0dlLL9eRdkpgnDgncm9jIwIXeKUnZLIeIGmttvU\nPpkq\r\n=80aj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "2885c185912655076e0eb01de6d7e5e6393cbd19", "scripts": {"doc": "doxygen doc/Doxyfile", "test": "node test", "pretest": "node-gyp rebuild -C test"}, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Node.js API (N-API)", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"safe-buffer": "^5.1.1"}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api_1.4.0_1532038525974_0.37633167406305157", "host": "s3://npm-registry-packages"}}, "1.5.0": {"name": "node-addon-api", "version": "1.5.0", "license": "MIT", "_id": "node-addon-api@1.5.0", "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/kirbysayshi", "name": "<PERSON>"}, {"url": "https://github.com/anisha-rohra", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/BotellaA", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rivertam", "name": "<PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/corymickelson", "name": "<PERSON>"}, {"url": "https://github.com/davedoesdev", "name": "<PERSON>"}, {"url": "https://github.com/nadongguri", "name": "Dongjin Na"}, {"url": "https://github.com/ebickle", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/devsnek", "name": "<PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/yjaeseok", "name": "<PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/jschlight", "name": "<PERSON>"}, {"url": "https://github.com/romandev", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/joshgarde", "name": "j<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/koistya", "name": "<PERSON>"}, {"url": "https://github.com/kfarnung", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/kYroL01", "name": "Michele Campus"}, {"url": "https://github.com/mcheshkov", "name": "<PERSON>"}, {"url": "https://github.com/NickNaso", "name": "<PERSON>"}, {"url": "https://github.com/iSkore", "name": "<PERSON>"}, {"url": "https://github.com/DaAitch", "name": "<PERSON>"}, {"url": "https://github.com/rolft<PERSON>mans", "name": "<PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "55be6b3da36e746f4b1f2af16c2adf67647d1ff8", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-1.5.0.tgz", "fileCount": 71, "integrity": "sha512-YsL/8dpBWxCFj3wAVAa/ceN4TlT8lACK8EgpuN0q/4ecflWHDuKpodb+tt7Rx22r/6FJ2f+IT25XSsXnZGwYgA==", "signatures": [{"sig": "MEQCIHChMZnUejgwGVAAzUTaAQHn/kxQ7h2sTNKvk7Npk0odAiBmNNwPev3r39hfVEU+44TvXFytwHGGo/g9H0ajn+s0nA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 686906, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbtFrACRA9TVsSAnZWagAAyqIP/3p6EHvp0NtykCHZVH3f\n8/UJI8c+prLjuqC0s2gWfbnwF1TuECxSJ8cAECcoIHigyzn9tEwsK9jJT8S3\nVrgGhHD/6vqISX+LY5Cffv3PpoyBNw6avmhYyPOzIO4wwouX1YxlI25Y8fQ6\nzTxJXiSVkLVbDTGLmED6A1Gvx9ZPJtW4sHVXGMEcmRLqgRnaXAis7tooMs6b\nKpCtjk8hcoTDhzHOU2G/C4V6JWeSgmS6tq/yDu+kWyrDpX0bFSoxCbI2v7ZR\ncCKb6caTxb26YkfKkQwITSvXTl30pFZzblhb6c1XDQLEzSOFVFaDOAkmEN9J\nFan5IrrxY9md+VRDJlBM5pOn8w6vsEsZDScy8O+d4vRdEgEOVT2kRmaKoLx6\n6Nd11DSR67V1MjVzt+g3FEWshPvFxc4+4scp7ENt6H856KQrbNTEqIrjGI1Q\nEHRIsKbhO/BemZLjcf2JmXwTPC648bQqGMB9YFVd8BK+Oj5RObfbzrBdtNFZ\nV+gZSBMkStEz73GQMQTN08ISHHygmt3eZ37IBRiQzdOP2rXVfAokv3nc8+fJ\npG1jSer1C6F2MSO0X9VB6YFgg6HpJSfNFkZ52Xndt5LQE/883iwf2bsKCMJ4\nlG+PjwPPzpkKSzyX79fQzWZNPCrrDQbMCKJXLsvvOPIxxmsN2eSsAhmv8H6j\nzDDY\r\n=r8KR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "ffebf9ba9a3cfefe602756ffe0fc700a39e078ca", "scripts": {"doc": "doxygen doc/Doxyfile", "test": "node test", "pretest": "node-gyp rebuild -C test"}, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Node.js API (N-API)", "directories": {}, "_nodeVersion": "10.11.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"safe-buffer": "^5.1.1"}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api_1.5.0_1538546367447_0.4017298088344614", "host": "s3://npm-registry-packages"}}, "1.6.0": {"name": "node-addon-api", "version": "1.6.0", "license": "MIT", "_id": "node-addon-api@1.6.0", "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/kirbysayshi", "name": "<PERSON>"}, {"url": "https://github.com/anisha-rohra", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/BotellaA", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rivertam", "name": "<PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/corymickelson", "name": "<PERSON>"}, {"url": "https://github.com/davedoesdev", "name": "<PERSON>"}, {"url": "https://github.com/nadongguri", "name": "Dongjin Na"}, {"url": "https://github.com/ebickle", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/devsnek", "name": "<PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/yjaeseok", "name": "<PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/jschlight", "name": "<PERSON>"}, {"url": "https://github.com/romandev", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/joshgarde", "name": "j<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/koistya", "name": "<PERSON>"}, {"url": "https://github.com/kfarnung", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/kYroL01", "name": "Michele Campus"}, {"url": "https://github.com/mcheshkov", "name": "<PERSON>"}, {"url": "https://github.com/NickNaso", "name": "<PERSON>"}, {"url": "https://github.com/iSkore", "name": "<PERSON>"}, {"url": "https://github.com/DaAitch", "name": "<PERSON>"}, {"url": "https://github.com/rolft<PERSON>mans", "name": "<PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}, {"url": "https://github.com/fraxken", "name": "<PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "bbb1a32395245fd2cddcfed8312b3dbd6511b6c4", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-1.6.0.tgz", "fileCount": 72, "integrity": "sha512-HEUPBHfdH4CLR1Qq4/Ek8GT/qFSvpApjJQmcYdLCL51ADU/Y11kMuFAdIevhNrPh3ylqVGA8k6vI/oi4YUAHbA==", "signatures": [{"sig": "MEUCID5otM2uxZf3XzMaZL15TGG3jTd0311dapQISCHxDMR3AiEAhBp5MsZ4UNfB90iGuzTP+SarjIaXl227ywsqkQ6tkXo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 691819, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb3NHRCRA9TVsSAnZWagAA1FAP/A0fT8g/IcUoDl+OVbDN\na28cM8QxMSW3r9Vjip+IDwUn91oQFpmioWbSTwxoa+MuwXatBC07eepcmWpY\nmgHjoaXln9qq3GbOvAImGPK1g9/k51v2SREsqp2U5ECMb6LloCK0FvGAPEL+\n1j1LBfKTOuMFsXDJ2f3Ffhbd8duR6rZXpsxyAp/95QAbLxfhwDFl2JgfG/g0\nTI7Et1yAH00kK6o0auLc9r3tF9Kdj513unqLQBtCneDQZhF6QvJ1yfcOeMIv\nSW8zXcFwJ2FfRDa84e3rN9hIRUvYmI8Ra/nd7Ap/c7xNlA9zWISWNT/28k8T\n31i7m9/XlyWRObxp6We1OduLLOov/55gdVFGuvEv+RcEBakz6MaHZhK4DfeY\n6XUA/hgKMPhAi4nIvlqKkneJatmqtE3eMqdTss+u2MQTAJvL1zgCsA3g8cQd\n1wt4y8V1AIrQndKjBZGe6sO0RXhc9fDTtI62GvFY7uOOm56iPSZs6o70EKB1\ncWPG6gGYhTOZEv4E+986pW+zuM9XPyOquyCGafJHKAA6nZNeHL1a9c+QIKgr\nKwh7jjWo+S3r/k/6igJRjFrdTU1h0gWApF52da/MWOPSzy1oy61xrWGiqS/6\n5wIoKxQ/jgrAw/o1S3I+FlDReTrt2BgzNrrbGLCmPbxSRt4q6ul1aLOwjPCt\n1QYM\r\n=+l6X\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "322dc0943e52950954bd0559b5967ee321d2556f", "scripts": {"doc": "doxygen doc/Doxyfile", "test": "node test", "pretest": "node-gyp rebuild -C test"}, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Node.js API (N-API)", "directories": {}, "_nodeVersion": "10.13.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"safe-buffer": "^5.1.1"}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api_1.6.0_1541198288833_0.4982463173971634", "host": "s3://npm-registry-packages"}}, "1.6.1": {"name": "node-addon-api", "version": "1.6.1", "license": "MIT", "_id": "node-addon-api@1.6.1", "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/kirbysayshi", "name": "<PERSON>"}, {"url": "https://github.com/anisha-rohra", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/BotellaA", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rivertam", "name": "<PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/corymickelson", "name": "<PERSON>"}, {"url": "https://github.com/davedoesdev", "name": "<PERSON>"}, {"url": "https://github.com/nadongguri", "name": "Dongjin Na"}, {"url": "https://github.com/ebickle", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/devsnek", "name": "<PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/yjaeseok", "name": "<PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/jschlight", "name": "<PERSON>"}, {"url": "https://github.com/romandev", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/joshgarde", "name": "j<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/koistya", "name": "<PERSON>"}, {"url": "https://github.com/kfarnung", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/kYroL01", "name": "Michele Campus"}, {"url": "https://github.com/mcheshkov", "name": "<PERSON>"}, {"url": "https://github.com/NickNaso", "name": "<PERSON>"}, {"url": "https://github.com/iSkore", "name": "<PERSON>"}, {"url": "https://github.com/DaAitch", "name": "<PERSON>"}, {"url": "https://github.com/rolft<PERSON>mans", "name": "<PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}, {"url": "https://github.com/fraxken", "name": "<PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "a9881c8dbc6400bac6ddedcb96eccf8051678536", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-1.6.1.tgz", "fileCount": 72, "integrity": "sha512-GcLOYrG5/enbqH4SMsqXt6GQUQGGnDnE3FLDZzXYkCgQHuZV5UDFR+EboeY8kpG0avroyOjpFQ2qLEBosFcRIA==", "signatures": [{"sig": "MEUCIA1XrDV2ALicXvv1DS9rzuPsR5zDRIpteA3TIhQ4jJsFAiEAnSu5/Vk6q8AWZdSQeD8NVVyXNnu2AiduApGNF9n6JN8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 692846, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb7Hr2CRA9TVsSAnZWagAAumEP/03nO1/epzPNCG+D9+zn\ne5CSL2VPO6DbeDqpdDS88ulLxsumaXLUbsE/86ugLLDAxPiKoBby2LqIvcbI\n/Jr9ivUgWiYwRYeHQ/Iz14N6G31j9b3Zf2aHAUfJHSDgjOd0152eFGyM9Vxm\nFyVBt9jeSytkw1aK95dqGUsNmDNSduVrFtF7yxAnnCAi8yEUg6MZEA3YVtVi\ne0yKTxURT6ip7OHaZhZ1xBn9LJufhtUqPabCX2SlB8SLNN9N3kWyUeWQCXMO\n591k1VtzN//IlQ7wa7jE6PQ1DnK4+PawyFWErR4qhRUy5ZFPdsFtlgyeIQlO\nHIlQL7rRK2I3TOYDKkCcf/FEbcblpAV/Lk2x197SjeIOpp1Dy6YqF72xlXaR\nbCP6HIY42qO0VfcdjI4De/h79UCRQ6/IiZuc9nD3OO7NtxPUxm1kBjASe53y\n9YJ8vnX3US+hA5PVvQJrDOxHQjEz8e/VBBJZHe/b0Ur5Jb+yR5Zax72BUgS2\noCyC/9S0MRPu9qOmf5f3WjYfMXxBya5u4bLkP5J7Au3fYwZkVrxn0E4RTfPQ\ngW83w0E4BTjxeDLdSPsp7j8yYMJQ8EqbARmzoE5mGFMTXd9AV5oozhU5wqDl\nEoYdeYJzOSfOHU7oOE249n77/la+NPXZ9S+sE9HJ1TtUZdfGs23PXG1YeyN6\n2tc2\r\n=N/zm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "269bf12e5f1908aeaa04de6f2efb26eb286cea0a", "scripts": {"doc": "doxygen doc/Doxyfile", "test": "node test", "pretest": "node-gyp rebuild -C test"}, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Node.js API (N-API)", "directories": {}, "_nodeVersion": "10.13.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"safe-buffer": "^5.1.1"}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api_1.6.1_1542224629730_0.3889545930678706", "host": "s3://npm-registry-packages"}}, "1.6.2": {"name": "node-addon-api", "version": "1.6.2", "license": "MIT", "_id": "node-addon-api@1.6.2", "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/kirbysayshi", "name": "<PERSON>"}, {"url": "https://github.com/anisha-rohra", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/BotellaA", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rivertam", "name": "<PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/corymickelson", "name": "<PERSON>"}, {"url": "https://github.com/davedoesdev", "name": "<PERSON>"}, {"url": "https://github.com/nadongguri", "name": "Dongjin Na"}, {"url": "https://github.com/ebickle", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/devsnek", "name": "<PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/yjaeseok", "name": "<PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/jschlight", "name": "<PERSON>"}, {"url": "https://github.com/romandev", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/joshgarde", "name": "j<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/koistya", "name": "<PERSON>"}, {"url": "https://github.com/kfarnung", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/kYroL01", "name": "Michele Campus"}, {"url": "https://github.com/mcheshkov", "name": "<PERSON>"}, {"url": "https://github.com/NickNaso", "name": "<PERSON>"}, {"url": "https://github.com/iSkore", "name": "<PERSON>"}, {"url": "https://github.com/DaAitch", "name": "<PERSON>"}, {"url": "https://github.com/rolft<PERSON>mans", "name": "<PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}, {"url": "https://github.com/fraxken", "name": "<PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "d8aad9781a5cfc4132cc2fecdbdd982534265217", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-1.6.2.tgz", "fileCount": 72, "integrity": "sha512-479Bjw9nTE5DdBSZZWprFryHGjUaQC31y1wHo19We/k0BZlrmhqQitWoUL0cD8+scljCbIUL+E58oRDEakdGGA==", "signatures": [{"sig": "MEYCIQCkKED3215yhJ1UJJtx/eX64bf0h48LZ25r7LbPf9MQkQIhAL9kvSA2WvKBE1AvppsfIHZBvzvowI+MvsFHKNVMjaIi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 693219, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb/zO2CRA9TVsSAnZWagAAs1MQAIgGbxp7wy5b01lKjGAy\nKR8wspROReIbsWDwq/8vVXXy+wYBrlxI46vWWRJ80INu8jbh8x9MrWpFuM7f\n+oN6bENtkYIzzh1V+L3KN8Pv/5w1cwtVFGnEWwJJL/crmER5otLmyNTIrlDY\nMlY6175Fwmz4vD4ooeFiN1+RnoKRwZGgmcYjt1vmtmRYGfIQl9FCkg1VBovL\nr9mSEo9D1tJ9Gtlj6p8igCqGzhB1wxNV0RjraMrb5IMHNUu28A6/xeYA+AZY\naPtYG5+aova3wS1AS6Y1kH0vunrNhjPnbqrMRu7/Ee42mt3vbBPpOCr+X2VQ\ngWj3xxwm1IWrYsNVbpkZs+A3JcglkJB9pWnGZ5/QBt3HI8QqYP5+eghG7VrO\nfb/I28FeLj9dQEJnyN+B+BntqNkbO4R4PVFYzTwbvb5l41ymLLlPwuxVoVSj\nJi9R2TVwPGzg2+cK1C5SWQZC5JmrA/sB8RI1OpxfJ4WfWtrHjhXIAxS2EJTX\nFuBYByb57SdqOmEgqHiNjZ1nfz/rh+Wdp8TYo3n0akgZWoJz1imdPuaeZVbo\nIaZBgdIDWXaesKLh1j2iIVkwTONTykUO7NPR9PVj7U9LFKriXZqQIWzPOYMC\n/oiNmgmuP97GFjiwpGvPVONAQhUA35J1OwqylMvRTmm8ZbI4IuEoMmFAA0he\n/tQB\r\n=CMUs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "d8e9c2245abb7fbc67f5696bcaebd10085a4688e", "scripts": {"doc": "doxygen doc/Doxyfile", "test": "node test", "pretest": "node-gyp rebuild -C test"}, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Node.js API (N-API)", "directories": {}, "_nodeVersion": "10.13.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"safe-buffer": "^5.1.1"}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api_1.6.2_1543451573406_0.5831295775900345", "host": "s3://npm-registry-packages"}}, "1.6.3": {"name": "node-addon-api", "version": "1.6.3", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "_id": "node-addon-api@1.6.3", "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/abhi11210646", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kirbysayshi", "name": "<PERSON>"}, {"url": "https://github.com/anisha-rohra", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/BotellaA", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rivertam", "name": "<PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gallafent", "name": "<PERSON>"}, {"url": "https://github.com/bmacnaughton", "name": "<PERSON>"}, {"url": "https://github.com/corymickelson", "name": "<PERSON>"}, {"url": "https://github.com/davedoesdev", "name": "<PERSON>"}, {"url": "https://github.com/nadongguri", "name": "Dongjin Na"}, {"url": "https://github.com/ebickle", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/devsnek", "name": "<PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/DuBistKomisch", "name": "<PERSON>"}, {"url": "https://github.com/yjaeseok", "name": "<PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/jschlight", "name": "<PERSON>"}, {"url": "https://github.com/romandev", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/joshgarde", "name": "j<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/koistya", "name": "<PERSON>"}, {"url": "https://github.com/kfarnung", "name": "<PERSON>"}, {"url": "https://github.com/lmartorella", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/kYroL01", "name": "Michele Campus"}, {"url": "https://github.com/mcheshkov", "name": "<PERSON>"}, {"url": "https://github.com/NickNaso", "name": "<PERSON>"}, {"url": "https://github.com/iSkore", "name": "<PERSON>"}, {"url": "https://github.com/DaAitch", "name": "<PERSON>"}, {"url": "https://github.com/rolft<PERSON>mans", "name": "<PERSON>"}, {"url": "https://github.com/okuryu", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/sam-github", "name": "<PERSON>"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}, {"url": "https://github.com/fraxken", "name": "<PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "3998d4593e2dca2ea82114670a4eb003386a9fe1", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-1.6.3.tgz", "fileCount": 72, "integrity": "sha512-FXWH6mqjWgU8ewuahp4spec8LkroFZK2NicOv6bNwZC3kcwZUI8LeZdG80UzTSLLhK4T7MsgNwlYDVRlDdfTDg==", "signatures": [{"sig": "MEQCID6LezkqNQgennboovaH76u/dAmL9Zktk8QME9qW5xkfAiAjqSC5RN3GYWPKIKT0yy7S9qdgvfMcGJUq82m5cpLVcg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 702577, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpSdpCRA9TVsSAnZWagAAiaEP/j4TMeiVWO8i1p3mGsew\nEFXhQZhhmoGjytXSzynRxZ7j5UlMZfjEQAVqE2zNk22+rwewSpS9sU97mLF4\n9jANcehBLEh5pENHd3G1mD2ItnT/Wta/OzMTDT6h58Xx1FNQ3ufJiojcmuKU\n7StecToG/0Moi72seDtufWnlvyxp92tgYwFyEbTQDvLl0nJS5tV90GuYgEcw\nsB29UDVAodnjWoa8FZg550lBufrU916Tm/A+h0mMcryEtyVrfucIuEWNLskV\nrvfDgRG7/KllNgOEnHPqvdbWnF+H/EOqS6BBGtmx6cP/RcUFi2DD5IUFeUFR\ncvKT1hVKM91glbYXTPssexgSkP+0RIL+DCu2PXR/mqtpx/tbtNODXSCPAsB1\n9plboD98xIu1vnqSFfYn6Pijey8a+8pWhwISiCGnE2k+dMslnzhy28ckuuT5\nbiewOfEqfjKY+6oORARPMUlRS/jclZzNM2ZHslnxxP5AcP0UgTWS6rvlU5WL\n70rxnY6ltCdDJe/9NydWyD8dsMjK/6CZzrRSkcdKHEnFaAciOUXPXty3OJSu\nqGa3cKY/ztd7VqwsR8EjYvvImyO3ORXz3+dzUDHl5+XBpcRUIBXrrzMS8u1n\nPcepT0a60pq077MMzqL6qLADRgrGpSUy5BTdnqCAzNjiBnAeQ7sQnNMMxQTp\n0jXl\r\n=A5p1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "c12c42519c108b859c7d9f3a3ebb902f948eabd6", "scripts": {"dev": "node test", "doc": "doxygen doc/Doxyfile", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "pretest": "node-gyp rebuild -C test", "dev:incremental": "node test", "predev:incremental": "node-gyp configure build -C test --debug"}, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Node.js API (N-API)", "directories": {}, "_nodeVersion": "10.15.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"safe-buffer": "^5.1.1"}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api_1.6.3_1554327400750_0.06315390023221634", "host": "s3://npm-registry-packages"}}, "1.7.0": {"name": "node-addon-api", "version": "1.7.0", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "_id": "node-addon-api@1.7.0", "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/abhi11210646", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jmendeth", "name": "<PERSON>dez"}, {"url": "https://github.com/kirbysayshi", "name": "<PERSON>"}, {"url": "https://github.com/anisha-rohra", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/BotellaA", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rivertam", "name": "<PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gallafent", "name": "<PERSON>"}, {"url": "https://github.com/bmacnaughton", "name": "<PERSON>"}, {"url": "https://github.com/corymickelson", "name": "<PERSON>"}, {"url": "https://github.com/davedoesdev", "name": "<PERSON>"}, {"url": "https://github.com/nadongguri", "name": "Dongjin Na"}, {"url": "https://github.com/ebickle", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/devsnek", "name": "<PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/DuBistKomisch", "name": "<PERSON>"}, {"url": "https://github.com/yjaeseok", "name": "<PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/jschlight", "name": "<PERSON>"}, {"url": "https://github.com/romandev", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/joshgarde", "name": "j<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/<PERSON>Eady", "name": "<PERSON>"}, {"url": "https://github.com/koistya", "name": "<PERSON>"}, {"url": "https://github.com/kfarnung", "name": "<PERSON>"}, {"url": "https://github.com/lmartorella", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/kYroL01", "name": "Michele Campus"}, {"url": "https://github.com/mcheshkov", "name": "<PERSON>"}, {"url": "https://github.com/NickNaso", "name": "<PERSON>"}, {"url": "https://github.com/iSkore", "name": "<PERSON>"}, {"url": "https://github.com/DaAitch", "name": "<PERSON>"}, {"url": "https://github.com/rolft<PERSON>mans", "name": "<PERSON>"}, {"url": "https://github.com/ross-weir", "name": "<PERSON>"}, {"url": "https://github.com/okuryu", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/sam-github", "name": "<PERSON>"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}, {"url": "https://github.com/fraxken", "name": "<PERSON>"}, {"url": "https://github.com/tux3", "name": "Tux3"}, {"url": "https://github.com/morokosi", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "d2897e0a2f35895676133004abbe6a7af6e55f79", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-1.7.0.tgz", "fileCount": 74, "integrity": "sha512-TaiwMuQqmonaIz/dI+a3V2XE67872jC2Z+fOzKuH4piwxGZN48NwVy75hL8shzQL09Nfl/Avk7md7dVcMG0zlA==", "signatures": [{"sig": "MEUCIHqc6AUHRkzNnOBfjn3B40YVhTni178/G5aLg+o0ClhgAiEA3+l0tnDXWf5+i2LbfsESHzanLXuAh3HFHDcDX2DjEO0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 753921, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdNyhrCRA9TVsSAnZWagAAiUgP/RGnhWWdNVp7s7SR2m7+\nlyRZ/dpZrDbahl1fNCv4e+x+s+uCxGFCytCQNjCVsMt+j8+6bL9GkULfoCST\nCb0XawcH9fjLDcZ5N91UPxkNOO+HuS6ytZaOZrMhzopMsQ6lzftXvvlcXcOX\n7KeO+U+p0mmEXB8h1f/w401pRM2ZCD2y47Ck7OD4sV8Sa1V8942cUS1SJLiq\nXePvXAR542TjqAkX7fZo2Dy8RbJjrsCvxRyC388cClxsrLOu64h6mbIkPMBl\n4u8slMRJj3j0RB0mPpnhkm5FTiEQD723ex8iYZOYCh+5xODrsZ1QiYyoUHaB\nKfcpJ09fWG2bqfnaezq9/fdR8xP/fvk4G7Oz4kfqJbRa/qg5rJ8y73IrBa/J\nJHe15aX8kj7qg9PRkr2zE+ACE2GORPeykCzO1Ht/z666mpnVxKNCQKuVBH5U\nZ4LKIEYnsg3SDYJeyV1LDnrDeB2nj5MoBMgqpERyHyHm09lGlNcwFSvfsVR6\nNg+fgH90ot6MRIw2iXx4hRCghvm4hqFZTCBZedI01lJV+WQqhgiwujwwBRPY\njNAnx9XdJ7nUKz1w6oamx4YGOhuGQOqvkH2QawBc4z8c6nQqLNvzeiD5ijrL\nCOVJoKxmFBqGp4F+iVyAIl6pS2H+fugGhX+wVhYsiZzddhiD4ZiuZ0K2P2fr\nAtKZ\r\n=Khkz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "0a1380c896f657bbd619755f4fafa84880db4824", "scripts": {"dev": "node test", "doc": "doxygen doc/Doxyfile", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "pretest": "node-gyp rebuild -C test", "dev:incremental": "node test", "predev:incremental": "node-gyp configure build -C test --debug"}, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Node.js API (N-API)", "directories": {}, "_nodeVersion": "10.16.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"safe-buffer": "^5.1.1"}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api_1.7.0_1563895912920_0.3449440298137434", "host": "s3://npm-registry-packages"}}, "1.7.1": {"name": "node-addon-api", "version": "1.7.1", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "_id": "node-addon-api@1.7.1", "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/abhi11210646", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jmendeth", "name": "<PERSON>dez"}, {"url": "https://github.com/kirbysayshi", "name": "<PERSON>"}, {"url": "https://github.com/anisha-rohra", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/BotellaA", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rivertam", "name": "<PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gallafent", "name": "<PERSON>"}, {"url": "https://github.com/bmacnaughton", "name": "<PERSON>"}, {"url": "https://github.com/corymickelson", "name": "<PERSON>"}, {"url": "https://github.com/davedoesdev", "name": "<PERSON>"}, {"url": "https://github.com/nadongguri", "name": "Dongjin Na"}, {"url": "https://github.com/ebickle", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/devsnek", "name": "<PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/DuBistKomisch", "name": "<PERSON>"}, {"url": "https://github.com/yjaeseok", "name": "<PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/jschlight", "name": "<PERSON>"}, {"url": "https://github.com/romandev", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/joshgarde", "name": "j<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/<PERSON>Eady", "name": "<PERSON>"}, {"url": "https://github.com/koistya", "name": "<PERSON>"}, {"url": "https://github.com/kfarnung", "name": "<PERSON>"}, {"url": "https://github.com/lmartorella", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/kYroL01", "name": "Michele Campus"}, {"url": "https://github.com/mcheshkov", "name": "<PERSON>"}, {"url": "https://github.com/NickNaso", "name": "<PERSON>"}, {"url": "https://github.com/iSkore", "name": "<PERSON>"}, {"url": "https://github.com/DaAitch", "name": "<PERSON>"}, {"url": "https://github.com/rolft<PERSON>mans", "name": "<PERSON>"}, {"url": "https://github.com/ross-weir", "name": "<PERSON>"}, {"url": "https://github.com/okuryu", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/sam-github", "name": "<PERSON>"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}, {"url": "https://github.com/fraxken", "name": "<PERSON>"}, {"url": "https://github.com/tux3", "name": "Tux3"}, {"url": "https://github.com/morokosi", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "cf813cd69bb8d9100f6bdca6755fc268f54ac492", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-1.7.1.tgz", "fileCount": 74, "integrity": "sha512-2+DuKodWvwRTrCfKOeR24KIc5unKjOh8mz17NCzVnHWfjAdDqbfbjqh7gUT+BkXBRQM52+xCHciKWonJ3CbJMQ==", "signatures": [{"sig": "MEUCIEm6dYxY9d72xaKrG6GLPdHiM4eyOxuY3MuMDnaGvaf4AiEAkHMps63hgVF8gzaQvExfH30lSHziii0E66bTHGHRjzA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 754629, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdN4UzCRA9TVsSAnZWagAAmqIQAIMD3nkIpsL9LDmXNExo\nayAeld/BoyuOVJY1meVpgBL7DaTp4ovjFgjH27m2pc4fScsVuB+01AuU+E1J\nkI8bmIf93+oiIP4qqhRBxnXoBqxnAVrZ2sAbxhhR+2SouDY5d2gqtfDSHOTD\nx6XVbLe3AMddhN+0h3tvi+LlmJb7gp6x2ZF0Ox4Vpb9Sykrd/U50ksbSuR2G\nAdJY/UxDkbUlONwgEgfYJo6oILYGZ6NzMp/dLmeU8AFUpYBrM/onDmak36RU\n3pq8pMqXxO9TwJrq0c0OW8bdn9PegQUwQ3uCLKEMcuAXvBa6JIsnsSkrSmVA\nMW5Ojc/yG5kzJ+rV28WDNCvb/MGOnHHBQgov70IE6U4F4DJOuLgZNyoP9EaU\nF34YI15lZ1GzA1sgPhvQUPKvYieYuP+GSPqfZy8VZgU7OEYTa6no7A2n9s8S\n670pPuh2E9/sn2t9fu9ZMgY5QxSKYzEqvatVEXiwKpcCxyhrnsGBq/2CNN7s\nVY2Oth9qq5DlAqhEuQGl5hIAwKF78I0SZX1xLkbCYyNbg+NfXB3fo9l5x5aD\nanmMPbcfoBlvI4snGhXeUUeRqmHKC9F3ToUO63glCO4VgQIHgsigkqoICUgy\nlyUDinkYjuBLNcFRmoGlLQJARnwYWXiT9+eysqlepHxTQaatuxaz9iVBKstu\nLgj+\r\n=XkqK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "5a7f8b2c7daae0ed898c198e13e56332169b3571", "scripts": {"dev": "node test", "doc": "doxygen doc/Doxyfile", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "pretest": "node-gyp rebuild -C test", "dev:incremental": "node test", "predev:incremental": "node-gyp configure build -C test --debug"}, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Node.js API (N-API)", "directories": {}, "_nodeVersion": "10.16.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"safe-buffer": "^5.1.1"}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api_1.7.1_1563919666527_0.4381203217246479", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "node-addon-api", "version": "2.0.0", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "_id": "node-addon-api@2.0.0", "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/abhi11210646", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jmendeth", "name": "<PERSON>dez"}, {"url": "https://github.com/kirbysayshi", "name": "<PERSON>"}, {"url": "https://github.com/anisha-rohra", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/BotellaA", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rivertam", "name": "<PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gallafent", "name": "<PERSON>"}, {"url": "https://github.com/bmacnaughton", "name": "<PERSON>"}, {"url": "https://github.com/corymickelson", "name": "<PERSON>"}, {"url": "https://github.com/davedoesdev", "name": "<PERSON>"}, {"url": "https://github.com/nadongguri", "name": "Dongjin Na"}, {"url": "https://github.com/ebickle", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/devsnek", "name": "<PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/DuBistKomisch", "name": "<PERSON>"}, {"url": "https://github.com/yjaeseok", "name": "<PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/jschlight", "name": "<PERSON>"}, {"url": "https://github.com/romandev", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/joshgarde", "name": "j<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/<PERSON>Eady", "name": "<PERSON>"}, {"url": "https://github.com/koistya", "name": "<PERSON>"}, {"url": "https://github.com/kfarnung", "name": "<PERSON>"}, {"url": "https://github.com/legendecas", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/lmartorella", "name": "<PERSON>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/mikepricedev", "name": "<PERSON>"}, {"url": "https://github.com/kYroL01", "name": "Michele Campus"}, {"url": "https://github.com/mcheshkov", "name": "<PERSON>"}, {"url": "https://github.com/NickNaso", "name": "<PERSON>"}, {"url": "https://github.com/iSkore", "name": "<PERSON>"}, {"url": "https://github.com/anurbol", "name": "Nurbol <PERSON>"}, {"url": "https://github.com/DaAitch", "name": "<PERSON>"}, {"url": "https://github.com/rolft<PERSON>mans", "name": "<PERSON>"}, {"url": "https://github.com/ross-weir", "name": "<PERSON>"}, {"url": "https://github.com/okuryu", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/sam-github", "name": "<PERSON>"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}, {"url": "https://github.com/fraxken", "name": "<PERSON>"}, {"url": "https://github.com/timrach", "name": "<PERSON>"}, {"url": "https://github.com/tniessen", "name": "<PERSON>"}, {"url": "https://github.com/tux3", "name": "Tux3"}, {"url": "https://github.com/morokosi", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "f9afb8d777a91525244b01775ea0ddbe1125483b", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-2.0.0.tgz", "fileCount": 76, "integrity": "sha512-ASCL5U13as7HhOExbT6OlWJJUV/lLzL2voOSP1UVehpRD8FbSrSDjfScK/KwAvVTI5AS6r4VwbOMlIqtvRidnA==", "signatures": [{"sig": "MEYCIQCDjZEBt7YwwKYS4J0XVy7AwSNGoVwhpvsBE69kHW0TWAIhAOl3w9sRGS+7NWZ3uQGNI1i3IwtjGCq+P7KTS1+f+OZb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 795610, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd37s8CRA9TVsSAnZWagAAcqsP/2E74j5SY6p6YxehOXJE\n6M76kSoK62mFTHQnwTuOKMVyxF9r2zDCq0KF3qBZHL8ONFz01i2JoYdtiqp1\nbR/qLvyv+G71k2xk0V8weMZ4EOOhwU1VqOZS4w7+24fTC2fYo18M3zKCRyBr\nVTwTdwckkH4D34leD5cBTiKlZbJoMtGCe6mYo7s0QRYBSvwETXJB8vy91Pnp\nidCRDzWs76sorgC7cp+q9ewoWqzHGLfqObfO7s7iSuYRbDdyAi+Q1t4lPJWl\nvdf6HVNGoaqlEZwpqESJB+Db55Tc5WrHjUHNBZRMnwy1u3V3I0Ab30MFl82s\nDPc4H8qNn1VSMuC9pnJR4GMDHV76PTRtKAByVxidRWaQ3Gtdgz8GDgghrjaE\ng8mTSuV8B8jkvRhqGr+mfKVeSDVMpi2tlz6+Nl+rqxNJNT0IKeqVBisETnNy\nxUaGErtmWGpvu8MLJDB0gDIZpCTZBSMY0A26mvcZrNpBBUxzkG/moKC9Z0pi\nDQt+tlwIj3JtfM3DJ9AHi+mAu5e7XU7pxWdxj8kucFsWKiv7ROy82zzOFZ1x\np37sVuq5gpxEu5DW8dN2nVEMULA+21h7baz4LTrYuFryjpRGHxDOD1H0bD9c\nRoqQZEDDF4LISOJTPx0czkPsisL15zUDn0yOLFnB9bSuCD6caLqsm64FerFe\nPf/V\r\n=auyA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "f677794b31876c7b235e69b3a636b59310b2d5c0", "scripts": {"dev": "node test", "doc": "doxygen doc/Doxyfile", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "pretest": "node-gyp rebuild -C test", "dev:incremental": "node test", "predev:incremental": "node-gyp configure build -C test --debug"}, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "Node.js API (N-API)", "directories": {}, "_nodeVersion": "12.13.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"safe-buffer": "^5.1.1"}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api_2.0.0_1574943547928_0.09148514299582189", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "node-addon-api", "version": "3.0.0", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "_id": "node-addon-api@3.0.0", "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/abhi11210646", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jmendeth", "name": "<PERSON>dez"}, {"url": "https://github.com/timarandras", "name": "<PERSON><PERSON><PERSON>, Dr"}, {"url": "https://github.com/kirbysayshi", "name": "<PERSON>"}, {"url": "https://github.com/anisha-rohra", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/BotellaA", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/azlan", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rivertam", "name": "<PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gallafent", "name": "<PERSON>"}, {"url": "https://github.com/bmacnaughton", "name": "<PERSON>"}, {"url": "https://github.com/corymickelson", "name": "<PERSON>"}, {"url": "https://github.com/davedoesdev", "name": "<PERSON>"}, {"url": "https://github.com/dmitryash", "name": "<PERSON>"}, {"url": "https://github.com/nadongguri", "name": "Dongjin Na"}, {"url": "https://github.com/ebickle", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/gms1", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/devsnek", "name": "<PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ikokostya", "name": "ikokostya"}, {"url": "https://github.com/DuBistKomisch", "name": "<PERSON>"}, {"url": "https://github.com/yjaeseok", "name": "<PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/jschlight", "name": "<PERSON>"}, {"url": "https://github.com/romandev", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/joshgarde", "name": "j<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kelvinhammond", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/<PERSON>Eady", "name": "<PERSON>"}, {"url": "https://github.com/koistya", "name": "<PERSON>"}, {"url": "https://github.com/kfarnung", "name": "<PERSON>"}, {"url": "https://github.com/legendecas", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/lmartorella", "name": "<PERSON>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/mikepricedev", "name": "<PERSON>"}, {"url": "https://github.com/kYroL01", "name": "Michele Campus"}, {"url": "https://github.com/mcheshkov", "name": "<PERSON>"}, {"url": "https://github.com/NickNaso", "name": "<PERSON>"}, {"url": "https://github.com/iSkore", "name": "<PERSON>"}, {"url": "https://github.com/anurbol", "name": "Nurbol <PERSON>"}, {"url": "https://github.com/DaAitch", "name": "<PERSON>"}, {"url": "https://github.com/rolft<PERSON>mans", "name": "<PERSON>"}, {"url": "https://github.com/ross-weir", "name": "<PERSON>"}, {"url": "https://github.com/okuryu", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/sam-github", "name": "<PERSON>"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}, {"url": "https://github.com/fraxken", "name": "<PERSON>"}, {"url": "https://github.com/timrach", "name": "<PERSON>"}, {"url": "https://github.com/tniessen", "name": "<PERSON>"}, {"url": "https://github.com/tux3", "name": "Tux3"}, {"url": "https://github.com/morokosi", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/fs-eire", "name": "<PERSON><PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "812446a1001a54f71663bed188314bba07e09247", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-3.0.0.tgz", "fileCount": 78, "integrity": "sha512-sSHCgWfJ+Lui/u+0msF3oyCgvdkhxDbkCS6Q8uiJquzOimkJBvX6hl5aSSA7DR1XbMpdM8r7phjcF63sF4rkKg==", "signatures": [{"sig": "MEUCIHkisNAOYyIMCw4xEYY6tpQWPE7/mgcXYetadtIhBrSTAiEA3BGI9+DNdbPEAMIr3rSIWpr+cqTHAJObDLtlcZi2y/Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 713271, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqhn/CRA9TVsSAnZWagAA7mEQAIcYwISvHoGwMWZI3vUF\ntu8hjEh7mvVqIbJ9RgnTrLXBw1q0mF4pp/ySAaH5ly9sZEQrMRMuLu7ET00A\nJ9z0SpcnJMiTdoi06xSudVQcZbLS16mOvi7IZ2Whx/yyN9pPanCr7gMWbm0J\nFsK4IaHAMsSrKNs+23MkIu+R4s3z71UI03kUIoDDGsrNXBeztMa9CtzKeZ6w\nQ3uzR/+ePASpqXAfARQFZ/OUxWd7COr5RKFa7a+VmgYIopk2o29RaBeA0OML\nJjbIeEDTS/hroxvz/iWJROFI+p+A/RHv9KWep/WFYS8IAagJdKFq8Iyz1vGA\nPMQXvn2WACGIYK4PDWnq/kGFwSnq8gAewbpldp210o1n9IEbxVXN7syVoVod\nVOX/zf5s8LXktqIxS2Rlu+CK/H+H9yjCANtPw7H5yHzAxLeiYLPBzdmcAEE1\nDzEa7IYLyDIGx950ZuRjV+cLs6IHMS63cqCxvJtjIoeyBgRBbqdhzpaBOpSr\nmXD/PTl/BylM2kt5p6Y/HszXfKq6FnqOB4YSY6dfgi8arCLt774h1QCWNCcx\nbiTBD+NfP1uj0Vqy3cqohGmJGMHznGrv3rAFRBxFPRPwV1VlnxFj6lWdBO68\nBVApH2tNafE3K9zOf2u0nyeXcU5Me2n/hvUrTwhSYSYhbZW9F5qrYswtxGwn\nNi0i\r\n=vI92\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "081cdc2f732ce759df777ffd69e0b4c2bd318b0d", "gypfile": false, "scripts": {"dev": "node test", "doc": "doxygen doc/Doxyfile", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "pretest": "node-gyp rebuild -C test", "benchmark": "node benchmark", "prebenchmark": "node-gyp rebuild -C benchmark", "dev:incremental": "node test", "predev:incremental": "node-gyp configure build -C test --debug"}, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Node.js API (N-API)", "directories": {}, "_nodeVersion": "10.20.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"benchmark": "^2.1.4", "safe-buffer": "^5.1.1"}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api_3.0.0_1588206078552_0.23174397823089143", "host": "s3://npm-registry-packages"}}, "1.7.2": {"name": "node-addon-api", "version": "1.7.2", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "_id": "node-addon-api@1.7.2", "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/abhi11210646", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jmendeth", "name": "<PERSON>dez"}, {"url": "https://github.com/kirbysayshi", "name": "<PERSON>"}, {"url": "https://github.com/anisha-rohra", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/BotellaA", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rivertam", "name": "<PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gallafent", "name": "<PERSON>"}, {"url": "https://github.com/bmacnaughton", "name": "<PERSON>"}, {"url": "https://github.com/corymickelson", "name": "<PERSON>"}, {"url": "https://github.com/davedoesdev", "name": "<PERSON>"}, {"url": "https://github.com/nadongguri", "name": "Dongjin Na"}, {"url": "https://github.com/ebickle", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/devsnek", "name": "<PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/DuBistKomisch", "name": "<PERSON>"}, {"url": "https://github.com/yjaeseok", "name": "<PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/jschlight", "name": "<PERSON>"}, {"url": "https://github.com/romandev", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/joshgarde", "name": "j<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/<PERSON>Eady", "name": "<PERSON>"}, {"url": "https://github.com/koistya", "name": "<PERSON>"}, {"url": "https://github.com/kfarnung", "name": "<PERSON>"}, {"url": "https://github.com/lmartorella", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/kYroL01", "name": "Michele Campus"}, {"url": "https://github.com/mcheshkov", "name": "<PERSON>"}, {"url": "https://github.com/NickNaso", "name": "<PERSON>"}, {"url": "https://github.com/iSkore", "name": "<PERSON>"}, {"url": "https://github.com/DaAitch", "name": "<PERSON>"}, {"url": "https://github.com/rolft<PERSON>mans", "name": "<PERSON>"}, {"url": "https://github.com/ross-weir", "name": "<PERSON>"}, {"url": "https://github.com/okuryu", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/sam-github", "name": "<PERSON>"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}, {"url": "https://github.com/fraxken", "name": "<PERSON>"}, {"url": "https://github.com/tniessen", "name": "<PERSON>"}, {"url": "https://github.com/tux3", "name": "Tux3"}, {"url": "https://github.com/morokosi", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "3df30b95720b53c24e59948b49532b662444f54d", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-1.7.2.tgz", "fileCount": 74, "integrity": "sha512-ibPK3iA+vaY1eEjESkQkM0BbCqFOaZMiXRTtdB0u7b4djtY6JnsjvPdUHVMg6xQt3B8fpTTWHI9A+ADjM9frzg==", "signatures": [{"sig": "MEUCIQCJaGUGEEZ5DHorfH7UI/MN6gfPuj6+44Ie0hnoL1PK5wIgBBQ7HjtIvnSFcWcMAK3+5WYnWjBTyBc0UvOYVVPkdVs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 755159, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe1qODCRA9TVsSAnZWagAA80gQAIZvIit7wiaCy3q4Vf/+\netsExs3EB84sJbxM+Ef+6/iHTdEM2HoTC98jhOfrAOliTGs+tOhKIxcI/krO\nZhOuFWw5NnTp+13jaiKPLIG715SKqt7BBhfkh4dwxvYAT/VcjgOCxLO2j3xO\na+1jw4X6a924UJ6ad82L7A7bkQiVdiGAeg4+7RSIghl1ACTAVEvX4dTP29Gn\n7jmL31kZqhvSzCzJ6aUVHTAmaV507XmHetqxkBmRML4nhJ442ctVbC8E4X/V\nBlEItwZiRJw9BXKsNjpymjSP/HkNXDNUDQsx31WhrbTHCJyQ1WW48HmRdsyY\nkt6zZYqsXmySHaaLhRKeuBKCVjOVEmu8Hmt8NalE53y4ttxF7kz/VihHEcsp\nE2rXWLJGqRCBriOMKY2Qcv2L610C5oPE7+kv3QX+riKuq4mTyRS/ZhxcMprG\nMoE8ifj7Wl4Gds1ywn+XCBI7YLzeUSyXuEuewS+TZOU7fs6yRUbpvqp4s42p\n1rHwAxTOhrmRHE7EliTpIoZmFelUbpCK8UEVWS2FAHSreKWTzYUTlWfSjgD8\nWDeMXaYitis8zduWBU9OVkKuWb2R1H3XamBssluS9qvBn832z5L3DxOy8YLp\niTFbws8qFC1sghibUujW8srvYgqXHmrCNVB1Sg4ymj44Mt+gjSi4Vkte7E59\nfFFl\r\n=hYxD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "74a09c7c4f24445aaca37948624c10dc2e771a1e", "scripts": {"dev": "node test", "doc": "doxygen doc/Doxyfile", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "pretest": "node-gyp rebuild -C test", "dev:incremental": "node test", "predev:incremental": "node-gyp configure build -C test --debug"}, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Node.js API (N-API)", "directories": {}, "_nodeVersion": "12.18.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"safe-buffer": "^5.1.1"}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api_1.7.2_1591124867121_0.18325729961033654", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "node-addon-api", "version": "2.0.1", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "_id": "node-addon-api@2.0.1", "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/abhi11210646", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jmendeth", "name": "<PERSON>dez"}, {"url": "https://github.com/kirbysayshi", "name": "<PERSON>"}, {"url": "https://github.com/anisha-rohra", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/BotellaA", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rivertam", "name": "<PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gallafent", "name": "<PERSON>"}, {"url": "https://github.com/bmacnaughton", "name": "<PERSON>"}, {"url": "https://github.com/corymickelson", "name": "<PERSON>"}, {"url": "https://github.com/davedoesdev", "name": "<PERSON>"}, {"url": "https://github.com/nadongguri", "name": "Dongjin Na"}, {"url": "https://github.com/ebickle", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/devsnek", "name": "<PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/DuBistKomisch", "name": "<PERSON>"}, {"url": "https://github.com/yjaeseok", "name": "<PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/jschlight", "name": "<PERSON>"}, {"url": "https://github.com/romandev", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/joshgarde", "name": "j<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/<PERSON>Eady", "name": "<PERSON>"}, {"url": "https://github.com/koistya", "name": "<PERSON>"}, {"url": "https://github.com/kfarnung", "name": "<PERSON>"}, {"url": "https://github.com/legendecas", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/lmartorella", "name": "<PERSON>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/mikepricedev", "name": "<PERSON>"}, {"url": "https://github.com/kYroL01", "name": "Michele Campus"}, {"url": "https://github.com/mcheshkov", "name": "<PERSON>"}, {"url": "https://github.com/NickNaso", "name": "<PERSON>"}, {"url": "https://github.com/iSkore", "name": "<PERSON>"}, {"url": "https://github.com/anurbol", "name": "Nurbol <PERSON>"}, {"url": "https://github.com/DaAitch", "name": "<PERSON>"}, {"url": "https://github.com/rolft<PERSON>mans", "name": "<PERSON>"}, {"url": "https://github.com/ross-weir", "name": "<PERSON>"}, {"url": "https://github.com/okuryu", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/sam-github", "name": "<PERSON>"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}, {"url": "https://github.com/fraxken", "name": "<PERSON>"}, {"url": "https://github.com/timrach", "name": "<PERSON>"}, {"url": "https://github.com/tniessen", "name": "<PERSON>"}, {"url": "https://github.com/tux3", "name": "Tux3"}, {"url": "https://github.com/morokosi", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "4fd0931bf6d7e48b219ff3e6abc73cbb0252b7a3", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-2.0.1.tgz", "fileCount": 76, "integrity": "sha512-2WVfwRfIr1AVn3dRq4yRc2Hn35ND+mPJH6inC6bjpYCZVrpXPB4j3T6i//OGVfqVsR1t/X/axRulDsheq4F0LQ==", "signatures": [{"sig": "MEUCIAZsiPEFFGTWx0f2G/rKUF3FXldMtrWOZcYkHgdUPrRuAiEAv6mENXZZdYE4/t5nGIEucnIkXvl5pfnuTwtm5012ERM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 796088, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe1qUhCRA9TVsSAnZWagAAaKMP/AjfLdXIVInJyKMI9Zsl\n06z+jQxyRN+JTVkEMCNsXCRAzLGMP29JqVeY4Zu0Y2WAAd6FGKF6Y1B69tK4\nLxl369GLiMKicTlo1+eTcq11nhg3ELvTNw03o5JAYhSKyKl2VPF4D0eniRjT\nL0OpsPnh1sH3tod7RZ//LyrYEWM9GXAMp75raHk6LtsdNQgmi/nksI+vWIn0\nDXQ3QoDqnwGaLfQSh+xBQu1n+0SijFTSlqipMn58XJlN6YOoR3shtFW5uWsE\nCyX8nq9KEDe5e/MbKv+2XhGhRwfUuueM97hGCEwVOQIqdhPutsQAnh7zT64g\nSdKxNx9rXswR4Ka+38YcoGBqYcNfd3HTS5LP+GqV964yvnRIjO/GA7h/K+ZC\nfdQduWbzTDSfKLFbShkTeDUK7tAped4LnuJkjjBuUrZZD7+nLGBXzKJJXT+H\n49O7yieHbMIlABxm2KL+8L8TACYy5CyrZ1EgYRrB4OLyX2ubD1UvwwK4dsuH\n7I1XY9e+LJEkUCzZCtdZjEm7aPAwul5hJSXmVgL6S/3Qc9kFHoyCAcFR50n5\nuL0CmmyCtE0+Og7BVvU/EdsLP0PECYXcZYyRQVWcC2dN/mVNXEO1CpiXKlDd\nbUkjnZv/eOdacmeJg6y0Qt5l++EHHdC3sn+gKZvE97qYgiw0JEKZSL/Zlbqb\n7vDs\r\n=eeTZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "2b78b54b52c427926e7bb851a7205b103dc4e447", "scripts": {"dev": "node test", "doc": "doxygen doc/Doxyfile", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "pretest": "node-gyp rebuild -C test", "dev:incremental": "node test", "predev:incremental": "node-gyp configure build -C test --debug"}, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Node.js API (N-API)", "directories": {}, "_nodeVersion": "12.18.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"safe-buffer": "^5.1.1"}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api_2.0.1_1591125281032_0.753966058856824", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "node-addon-api", "version": "2.0.2", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "_id": "node-addon-api@2.0.2", "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/abhi11210646", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jmendeth", "name": "<PERSON>dez"}, {"url": "https://github.com/kirbysayshi", "name": "<PERSON>"}, {"url": "https://github.com/anisha-rohra", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/BotellaA", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rivertam", "name": "<PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gallafent", "name": "<PERSON>"}, {"url": "https://github.com/bmacnaughton", "name": "<PERSON>"}, {"url": "https://github.com/corymickelson", "name": "<PERSON>"}, {"url": "https://github.com/davedoesdev", "name": "<PERSON>"}, {"url": "https://github.com/nadongguri", "name": "Dongjin Na"}, {"url": "https://github.com/ebickle", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/devsnek", "name": "<PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/DuBistKomisch", "name": "<PERSON>"}, {"url": "https://github.com/yjaeseok", "name": "<PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/jschlight", "name": "<PERSON>"}, {"url": "https://github.com/romandev", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/joshgarde", "name": "j<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/<PERSON>Eady", "name": "<PERSON>"}, {"url": "https://github.com/koistya", "name": "<PERSON>"}, {"url": "https://github.com/kfarnung", "name": "<PERSON>"}, {"url": "https://github.com/legendecas", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/lmartorella", "name": "<PERSON>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/mikepricedev", "name": "<PERSON>"}, {"url": "https://github.com/kYroL01", "name": "Michele Campus"}, {"url": "https://github.com/mcheshkov", "name": "<PERSON>"}, {"url": "https://github.com/NickNaso", "name": "<PERSON>"}, {"url": "https://github.com/iSkore", "name": "<PERSON>"}, {"url": "https://github.com/anurbol", "name": "Nurbol <PERSON>"}, {"url": "https://github.com/DaAitch", "name": "<PERSON>"}, {"url": "https://github.com/rolft<PERSON>mans", "name": "<PERSON>"}, {"url": "https://github.com/ross-weir", "name": "<PERSON>"}, {"url": "https://github.com/okuryu", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/sam-github", "name": "<PERSON>"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}, {"url": "https://github.com/fraxken", "name": "<PERSON>"}, {"url": "https://github.com/timrach", "name": "<PERSON>"}, {"url": "https://github.com/tniessen", "name": "<PERSON>"}, {"url": "https://github.com/tux3", "name": "Tux3"}, {"url": "https://github.com/morokosi", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "432cfa82962ce494b132e9d72a15b29f71ff5d32", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-2.0.2.tgz", "fileCount": 76, "integrity": "sha512-Ntyt4AIXyaLIuMHF6IOoTakB3K+RWxwtsHNRxllEoA6vPwP9o4866g6YWDLUdnucilZhmkxiHwHr11gAENw+QA==", "signatures": [{"sig": "MEUCIQD+3lU/l//2KfX/PVISHDufmPUHB2vA4yHbKTif+d4UDAIgKsKnHyCT/rMradVVmryN9l0jzRsA0KLhBxCSgu6XVOg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 799465, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe/KFCCRA9TVsSAnZWagAAEzQP/1f97X6mN50co0ogM8C1\njWfOx3f/cXLXuLuZdIb4ArExI8rAb1vVyxPpRL/TIab2nWmyYpLDWEQfSuKw\n9lo3ZgUVQQzbIdCUZ+9dL2SdeWM1+MbGdwmbAaARYRnSDAajrVXUahBNQtY0\n+6ZmOJ8huvMQBdtoEtDIFaQWbj/8rVbdXXdRzkYOxWzR298oUklKf/pm9FLS\nXe7VqgmploD4jijsuUoB3+y0Y2Wc1kD8KgzkvXgkmKjAyFs7rFXfdzaJqmNb\ndnOCQAvN6IKtzJwUmaKGwpLNiwyBiNR2OI17asCRjPSWKSgq3YIN97q91LIm\nRa2OnJaHZMSgmmtSqDTLHXrjq6EUOnR2iNQAhFqQa+WFX1DF+r0Zeu4ZIOyD\nOXzCLkA69SbIY/ejv7WTU3CLptQD3xFvhi8mweZgN5DP8InOilZLL4zvpkE1\nni00WEqUSeES3AbiJO4rSnWSudo3qKluKE1jyQ7gJXSQ+mp1CNJVnCoIBVHi\nSlKGXS18TPuMSdeKPmNZBU+LWLXUyLgjflC4QJ+Md2EyMZoQGPpT+2OWNM1Z\nQO6hCd9MCvMEqyVpEt3k9kl5vnVLPyWhH7AYGDK6Pfo1OF5eKchuyZJffLNh\nB844sP7GO0JuWE2w9QCXiZX/+JlYLeNp7XKRtHCVuategsV37j0uASlsOGFE\nxxZw\r\n=Vcr9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "7582fafebb7d92570fafc4cf0939f538b629c06f", "scripts": {"dev": "node test", "doc": "doxygen doc/Doxyfile", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "pretest": "node-gyp rebuild -C test", "dev:incremental": "node test", "predev:incremental": "node-gyp configure build -C test --debug"}, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Node.js API (N-API)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"safe-buffer": "^5.1.1"}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api_2.0.2_1593614657658_0.495650358650533", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "node-addon-api", "version": "3.0.1", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "_id": "node-addon-api@3.0.1", "maintainers": [{"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/abhi11210646", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jmendeth", "name": "<PERSON>dez"}, {"url": "https://github.com/timarandras", "name": "<PERSON><PERSON><PERSON>, Dr"}, {"url": "https://github.com/kirbysayshi", "name": "<PERSON>"}, {"url": "https://github.com/anisha-rohra", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/BotellaA", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/azlan", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rivertam", "name": "<PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gallafent", "name": "<PERSON>"}, {"url": "https://github.com/bmacnaughton", "name": "<PERSON>"}, {"url": "https://github.com/corymickelson", "name": "<PERSON>"}, {"url": "https://github.com/danbev", "name": "<PERSON>"}, {"url": "https://github.com/davedoesdev", "name": "<PERSON>"}, {"url": "https://github.com/dmitryash", "name": "<PERSON>"}, {"url": "https://github.com/nadongguri", "name": "Dongjin Na"}, {"url": "https://github.com/ebickle", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/gms1", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/devsnek", "name": "<PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ikokostya", "name": "ikokostya"}, {"url": "https://github.com/DuBistKomisch", "name": "<PERSON>"}, {"url": "https://github.com/yjaeseok", "name": "<PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/japj", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jschlight", "name": "<PERSON>"}, {"url": "https://github.com/romandev", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/joshgarde", "name": "j<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/hanazuki", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kelvinhammond", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/<PERSON>Eady", "name": "<PERSON>"}, {"url": "https://github.com/koistya", "name": "<PERSON>"}, {"url": "https://github.com/kfarnung", "name": "<PERSON>"}, {"url": "https://github.com/legendecas", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/lovell", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/lmartorella", "name": "<PERSON>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/mikepricedev", "name": "<PERSON>"}, {"url": "https://github.com/kYroL01", "name": "Michele Campus"}, {"url": "https://github.com/mcheshkov", "name": "<PERSON>"}, {"url": "https://github.com/NickNaso", "name": "<PERSON>"}, {"url": "https://github.com/iSkore", "name": "<PERSON>"}, {"url": "https://github.com/anurbol", "name": "Nurbol <PERSON>"}, {"url": "https://github.com/DaAitch", "name": "<PERSON>"}, {"url": "https://github.com/rolft<PERSON>mans", "name": "<PERSON>"}, {"url": "https://github.com/ross-weir", "name": "<PERSON>"}, {"url": "https://github.com/okuryu", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/sam-github", "name": "<PERSON>"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}, {"url": "https://github.com/fraxken", "name": "<PERSON>"}, {"url": "https://github.com/timrach", "name": "<PERSON>"}, {"url": "https://github.com/tniessen", "name": "<PERSON>"}, {"url": "https://github.com/tux3", "name": "Tux3"}, {"url": "https://github.com/morokosi", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/fs-eire", "name": "<PERSON><PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "990544a2607ec3f538443df4858f8c40089b7783", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-3.0.1.tgz", "fileCount": 78, "integrity": "sha512-YUpjl57P55u2yUaKX5Bgy4t5s6SCNYMg+62XNg+k41aYbBL1NgWrZfcgljR5MxDxHDjzl0qHDNtH6SkW4DXNCA==", "signatures": [{"sig": "MEUCIQDQJljFLpE37/FSl9VXSmDNfLWJMGhtZI2r4DMT/jPkPwIgZUU7Nqr0BjWyQXOolLKHSlQa43p4iNQ1EPHgBHx2E4w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 719815, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfDIZZCRA9TVsSAnZWagAAu8UP/jrQ47b9HLZh/KI97kqo\nc87BkQZ/WkwbNsSLyYl3YIkMv8VsprkuMEvX6M1C0lKJueuJdt8BPioNDK9g\nHn9OO1DPn6AUoXRUqUiUeHnYFnrQDFIWK/nfDCPGbbTh4jtyViQQwzXw8UNJ\no9n116+Y54IeSSJ7ZQ1aAGQSReAld0KNd8p4IroQJnV9wuOc9urmwcHQN/Sw\nrJkZTiJHwaoXIzCQJExejzLQvKqckPy8ulm/FHZTt+rc3v+njKW+B9g+rxke\nEtAO2PBAL/lDZ3RfGsdAy54NhjajRy1yp78IvHv/Z2/BO7vhfnsFLcT1LoKr\nba96zeGbuYdeO8IZKkzH7cOsZ2lvhhRjhm9chl9v9k6TRK+hQq8iIzEq7l5Z\nJ+FdnieeKVfvltMw1Y7CYNg5EiEp0dTVI+QrgcpEJREjwHES4bFxTgZVkruF\nE5vE8BYuePRxZzQ+0vOtzeM0Wigyi0t2fycQfPWzo+2N4uwR4IO85Z4A64X3\ncf0YxdMs97DsNcKmp7LKy4/lrbjDBZGvGkV8kcx46ZHBTfFNDIM+xfjFWQBW\nl0ciZhwWW+U0E92Genp+l6CsmzFMNCRStJA+op4KUlGJMTls0uUPkucheyyW\nQmKv21X9nyG7Fbq2tgUqNb0X5olnKAuaAdSBmP3mQYYSAtaiqNLIcttFHXnn\nLdQd\r\n=d16l\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "61c98463939630bb52438bb8ea1fb1fdf651af1c", "gypfile": false, "scripts": {"dev": "node test", "doc": "doxygen doc/Doxyfile", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "pretest": "node-gyp rebuild -C test", "benchmark": "node benchmark", "prebenchmark": "node-gyp rebuild -C benchmark", "dev:incremental": "node test", "predev:incremental": "node-gyp configure build -C test --debug"}, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Node.js API (N-API)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"benchmark": "^2.1.4", "safe-buffer": "^5.1.1"}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api_3.0.1_1594656344563_0.1674197905666035", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "node-addon-api", "version": "3.0.2", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "_id": "node-addon-api@3.0.2", "maintainers": [{"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/abhi11210646", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jmendeth", "name": "<PERSON>dez"}, {"url": "https://github.com/timarandras", "name": "<PERSON><PERSON><PERSON>, Dr"}, {"url": "https://github.com/kirbysayshi", "name": "<PERSON>"}, {"url": "https://github.com/anisha-rohra", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/BotellaA", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/azlan", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rivertam", "name": "<PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gallafent", "name": "<PERSON>"}, {"url": "https://github.com/bmacnaughton", "name": "<PERSON>"}, {"url": "https://github.com/corymickelson", "name": "<PERSON>"}, {"url": "https://github.com/danbev", "name": "<PERSON>"}, {"url": "https://github.com/davedoesdev", "name": "<PERSON>"}, {"url": "https://github.com/dmitryash", "name": "<PERSON>"}, {"url": "https://github.com/nadongguri", "name": "Dongjin Na"}, {"url": "https://github.com/ebickle", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/gms1", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/devsnek", "name": "<PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ikokostya", "name": "ikokostya"}, {"url": "https://github.com/DuBistKomisch", "name": "<PERSON>"}, {"url": "https://github.com/yjaeseok", "name": "<PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/japj", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jschlight", "name": "<PERSON>"}, {"url": "https://github.com/romandev", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/joshgarde", "name": "j<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/hanazuki", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kelvinhammond", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/<PERSON>Eady", "name": "<PERSON>"}, {"url": "https://github.com/Nishikoh", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/koistya", "name": "<PERSON>"}, {"url": "https://github.com/kfarnung", "name": "<PERSON>"}, {"url": "https://github.com/legendecas", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/lovell", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/lmartorella", "name": "<PERSON>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/mikepricedev", "name": "<PERSON>"}, {"url": "https://github.com/kYroL01", "name": "Michele Campus"}, {"url": "https://github.com/mcheshkov", "name": "<PERSON>"}, {"url": "https://github.com/NickNaso", "name": "<PERSON>"}, {"url": "https://github.com/iSkore", "name": "<PERSON>"}, {"url": "https://github.com/anurbol", "name": "Nurbol <PERSON>"}, {"url": "https://github.com/pacop", "name": "pacop"}, {"url": "https://github.com/DaAitch", "name": "<PERSON>"}, {"url": "https://github.com/rolft<PERSON>mans", "name": "<PERSON>"}, {"url": "https://github.com/ross-weir", "name": "<PERSON>"}, {"url": "https://github.com/okuryu", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/sam-github", "name": "<PERSON>"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}, {"url": "https://github.com/fraxken", "name": "<PERSON>"}, {"url": "https://github.com/timrach", "name": "<PERSON>"}, {"url": "https://github.com/tniessen", "name": "<PERSON>"}, {"url": "https://github.com/tux3", "name": "Tux3"}, {"url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/morokosi", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/fs-eire", "name": "<PERSON><PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "04bc7b83fd845ba785bb6eae25bc857e1ef75681", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-3.0.2.tgz", "fileCount": 81, "integrity": "sha512-+D4s2HCnxPd5PjjI0STKwncjXTUKKqm74MDMz9OPXavjsGmjkvwgLtA5yoxJUdmpj52+2u+RrXgPipahKczMKg==", "signatures": [{"sig": "MEYCIQDEVEywxSofMc2h5FkGwWlIkKFpDfel3+gaxEPUWcLjGAIhAOTMEghFdoUvW8FjfqdMoZ6Uek07QnEK1vQAZqimFPsC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 736462, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfZNuJCRA9TVsSAnZWagAAB1UP/iVoKMn69rDMis491dUD\nD3Ffar/LW0mQULTgsZtLFAYlFwQb51TLUAULoKUd4WikrzRQL/1jqmDYVP79\n6INgq4PPEYV+VNDex/7LtnlFRAZmoSp7q49KOY7JGy2lJwD0uhk37fz1sys/\nt989vsWKmo/7+YRZZfDZDaksv1jmLI/N8P/OMntTykWOT3J4IXuJyjLl8JHf\nBuuDY4/dnbDNyuW7n619443dbyvYFDHV4RMUgfMK4JlKv9bPJOqQiLptGFDw\nz8ajGTt/z0XHpDRFRCK/ItG75EgvMsnc6FLLPvQsZFOXxj/6/kNZdncUIrde\nJtzr3owJVihILnN473o0LrC28jWjCs32yMYHUu30HdxHuRDiRfhJWTDz3gPm\nzV93qdXhj0pW5oU8w+mZVMOwxmf4H1NjYziszOkJkfLvz7K/7HhMkG1GWMnD\nQWvTjh0RkMxO9gzE67KfkILw1PUQkcLZaPuoCLw7PDoU2cdk32nY71zSXbP5\nBIwXe8YLLKtZcyc+MlXifyvX8kITGiPYI/I77fwEMlCPhF8+6a3w+ZT5lIWj\nVVxFpjVHmbyhXiDHUDyNdedpPGj54qhfvWSgAvpdRW0ldYDMbwrJVvK0UP42\nfXTWa96iD77ryZB+RDN0u6HQrEhoUYrMaJWWJ7qx0PYFVK0gBWm84mj27Zet\nqB3R\r\n=CSpt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "79a777e1f85305b374878d757fd4a2c60e70abe7", "gypfile": false, "scripts": {"dev": "node test", "doc": "doxygen doc/Doxyfile", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "pretest": "node-gyp rebuild -C test", "benchmark": "node benchmark", "prebenchmark": "node-gyp rebuild -C benchmark", "dev:incremental": "node test", "predev:incremental": "node-gyp configure build -C test --debug"}, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Node.js API (N-API)", "directories": {}, "_nodeVersion": "14.11.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"bindings": "^1.5.0", "fs-extra": "^9.0.1", "benchmark": "^2.1.4", "safe-buffer": "^5.1.1"}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api_3.0.2_1600445320926_0.9558192508787307", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "node-addon-api", "version": "3.1.0", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "_id": "node-addon-api@3.1.0", "maintainers": [{"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/abhi11210646", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jmendeth", "name": "<PERSON>dez"}, {"url": "https://github.com/timarandras", "name": "<PERSON><PERSON><PERSON>, Dr"}, {"url": "https://github.com/kirbysayshi", "name": "<PERSON>"}, {"url": "https://github.com/anisha-rohra", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/BotellaA", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/azlan", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rivertam", "name": "<PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gallafent", "name": "<PERSON>"}, {"url": "https://github.com/blagoev", "name": "blagoev"}, {"url": "https://github.com/bmacnaughton", "name": "<PERSON>"}, {"url": "https://github.com/corymickelson", "name": "<PERSON>"}, {"url": "https://github.com/danbev", "name": "<PERSON>"}, {"url": "https://github.com/RaisinTen", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/davedoesdev", "name": "<PERSON>"}, {"url": "https://github.com/dmitryash", "name": "<PERSON>"}, {"url": "https://github.com/nadongguri", "name": "Dongjin Na"}, {"url": "https://github.com/fholzer", "name": "<PERSON>"}, {"url": "https://github.com/ebickle", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/gms1", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/devsnek", "name": "<PERSON>"}, {"url": "https://github.com/helio-frota", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ikokostya", "name": "ikokostya"}, {"url": "https://github.com/DuBistKomisch", "name": "<PERSON>"}, {"url": "https://github.com/yjaeseok", "name": "<PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/japj", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jschlight", "name": "<PERSON>"}, {"url": "https://github.com/romandev", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/joshgarde", "name": "j<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/hanazuki", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kelvinhammond", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/<PERSON>Eady", "name": "<PERSON>"}, {"url": "https://github.com/kidneysolo", "name": "kidneysolo"}, {"url": "https://github.com/Nishikoh", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/koistya", "name": "<PERSON>"}, {"url": "https://github.com/kfarnung", "name": "<PERSON>"}, {"url": "https://github.com/legendecas", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/lovell", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/lmartorella", "name": "<PERSON>"}, {"url": "https://github.com/mastergberry", "name": "<PERSON>gberry"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/mikepricedev", "name": "<PERSON>"}, {"url": "https://github.com/kYroL01", "name": "Michele Campus"}, {"url": "https://github.com/mcheshkov", "name": "<PERSON>"}, {"url": "https://github.com/nempoBu4", "name": "nempoBu4"}, {"url": "https://github.com/NickNaso", "name": "<PERSON>"}, {"url": "https://github.com/iSkore", "name": "<PERSON>"}, {"url": "https://github.com/seishun", "name": "<PERSON>"}, {"url": "https://github.com/anurbol", "name": "Nurbol <PERSON>"}, {"url": "https://github.com/pacop", "name": "pacop"}, {"url": "https://github.com/DaAitch", "name": "<PERSON>"}, {"url": "https://github.com/rolft<PERSON>mans", "name": "<PERSON>"}, {"url": "https://github.com/ross-weir", "name": "<PERSON>"}, {"url": "https://github.com/okuryu", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/sam-github", "name": "<PERSON>"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}, {"url": "https://github.com/fraxken", "name": "<PERSON>"}, {"url": "https://github.com/timrach", "name": "<PERSON>"}, {"url": "https://github.com/tniessen", "name": "<PERSON>"}, {"url": "https://github.com/tux3", "name": "Tux3"}, {"url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/morokosi", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/fs-eire", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ZzqiZQute", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "98b21931557466c6729e51cb77cd39c965f42239", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-3.1.0.tgz", "fileCount": 88, "integrity": "sha512-flmrDNB06LIl5lywUz7YlNGZH/5p0M7W28k8hzd9Lshtdh1wshD2Y+U4h9LD6KObOy1f+fEVdgprPrEymjM5uw==", "signatures": [{"sig": "MEQCIGW0k4WANvIhpQFfv4YfSqLb2kbz8vsARHocD7lWgpTUAiACqcPEuzbHjgJ2Ha+54li2EPNxmdPaxfwMiZwKUB7Pgg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 692509, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2qkuCRA9TVsSAnZWagAAbK8P/i6P1TmBeYy/RWIxyNrE\n7PFL361QMxY7JXAELMQ//Xfqt9Y0058LUfbt0DItVAcjad80EMKA0xwsxiuW\nY67fqhWMLAvvs9wK07gDay7o2bjWTn0XYHsUwJZf9mW1kKn+XYqt9WREk6gO\nxKl0GJtrlZMDP5mTdR0wt3tQXnSrKUV0nMF2vb/uliZFHRYpcfLcAMzu/vFn\nPuV/QHdmTMxBuUEaJTWPOrumaHzynZODbrCRls6HFVDSBjsnFwz5DvYbQBzt\nmt1k1ztMcn30HDunI9mSLnm0p7d2o+dVddTkt4U8UZ9psZpTzaeBvvnL0KLG\nhqv8+Hl5IUeZnUj/MBO0tHEEQH0MyU4MunN3RPNjQhwRJ7AOKnYaMDhpDj3j\n/t1cO1APaPSS6ByA08R1VjuVjz9QjggZdEO8OO+PLN9SDaeorDLLbzPLVC4U\nC8/jxsmqO+GGeK2U1dlaNPN9vWX5r/pZOSi1cKvolfIVenoxAemdM3TwHD5X\nOk5ZqUpJDPKmlQn5WRBHOY5TIUWtQYQikl8Mp1zFvYYnSP74WcMj8KbEZAvS\ng2mS8q+otQxua/vgMu87RAtDTHwk0r/GNve1fMu01QdIBeLeu5BXJPeEQT/3\nNuNkBbMJK70et4Xh6wChy/+7txRaRRFP/YXt6poAYuGCj97qdYyhvKAIoCHZ\nJIEH\r\n=yded\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "1bdc7584cdea3c5761385c7c08a06e12d9852972", "gypfile": false, "scripts": {"dev": "node test", "doc": "doxygen doc/Doxyfile", "lint": "node tools/clang-format.js", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "pretest": "node-gyp rebuild -C test", "lint:fix": "git-clang-format '*.h', '*.cc'", "benchmark": "node benchmark", "prebenchmark": "node-gyp rebuild -C benchmark", "dev:incremental": "node test", "predev:incremental": "node-gyp configure build -C test --debug"}, "support": true, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "pre-commit": "lint", "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Node.js API (N-API)", "directories": {}, "_nodeVersion": "14.15.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"bindings": "^1.5.0", "fs-extra": "^9.0.1", "benchmark": "^2.1.4", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1", "clang-format": "^1.4.0"}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api_3.1.0_1608165677935_0.7147698416734058", "host": "s3://npm-registry-packages"}}, "3.2.0": {"name": "node-addon-api", "version": "3.2.0", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "_id": "node-addon-api@3.2.0", "maintainers": [{"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/abhi11210646", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jmendeth", "name": "<PERSON>dez"}, {"url": "https://github.com/timarandras", "name": "<PERSON><PERSON><PERSON>, Dr"}, {"url": "https://github.com/kirbysayshi", "name": "<PERSON>"}, {"url": "https://github.com/anisha-rohra", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/BotellaA", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/azlan", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rivertam", "name": "<PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gallafent", "name": "<PERSON>"}, {"url": "https://github.com/blagoev", "name": "blagoev"}, {"url": "https://github.com/bmacnaughton", "name": "<PERSON>"}, {"url": "https://github.com/corymickelson", "name": "<PERSON>"}, {"url": "https://github.com/danbev", "name": "<PERSON>"}, {"url": "https://github.com/RaisinTen", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/davedoesdev", "name": "<PERSON>"}, {"url": "https://github.com/dmitryash", "name": "<PERSON>"}, {"url": "https://github.com/nadongguri", "name": "Dongjin Na"}, {"url": "https://github.com/fholzer", "name": "<PERSON>"}, {"url": "https://github.com/ebickle", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/gms1", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/devsnek", "name": "<PERSON>"}, {"url": "https://github.com/helio-frota", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ikokostya", "name": "ikokostya"}, {"url": "https://github.com/JckXia", "name": "<PERSON>"}, {"url": "https://github.com/DuBistKomisch", "name": "<PERSON>"}, {"url": "https://github.com/yjaeseok", "name": "<PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/japj", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jschlight", "name": "<PERSON>"}, {"url": "https://github.com/romandev", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/JoseExposito", "name": "<PERSON>"}, {"url": "https://github.com/joshgarde", "name": "j<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/hanazuki", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kelvinhammond", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/<PERSON>Eady", "name": "<PERSON>"}, {"url": "https://github.com/kecsou", "name": "Kévin VOYER"}, {"url": "https://github.com/kidneysolo", "name": "kidneysolo"}, {"url": "https://github.com/Nishikoh", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/koistya", "name": "<PERSON>"}, {"url": "https://github.com/kfarnung", "name": "<PERSON>"}, {"url": "https://github.com/legendecas", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Brooooooklyn", "name": "LongYinan"}, {"url": "https://github.com/lovell", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/lmartorella", "name": "<PERSON>"}, {"url": "https://github.com/mastergberry", "name": "<PERSON>gberry"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/mikepricedev", "name": "<PERSON>"}, {"url": "https://github.com/kYroL01", "name": "Michele Campus"}, {"url": "https://github.com/mcheshkov", "name": "<PERSON>"}, {"url": "https://github.com/nempoBu4", "name": "nempoBu4"}, {"url": "https://github.com/NickNaso", "name": "<PERSON>"}, {"url": "https://github.com/iSkore", "name": "<PERSON>"}, {"url": "https://github.com/seishun", "name": "<PERSON>"}, {"url": "https://github.com/anurbol", "name": "Nurbol <PERSON>"}, {"url": "https://github.com/pacop", "name": "pacop"}, {"url": "https://github.com/DaAitch", "name": "<PERSON>"}, {"url": "https://github.com/rolft<PERSON>mans", "name": "<PERSON>"}, {"url": "https://github.com/ross-weir", "name": "<PERSON>"}, {"url": "https://github.com/okuryu", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/sam-github", "name": "<PERSON>"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}, {"url": "https://github.com/fraxken", "name": "<PERSON>"}, {"url": "https://github.com/timrach", "name": "<PERSON>"}, {"url": "https://github.com/tniessen", "name": "<PERSON>"}, {"url": "https://github.com/tux3", "name": "Tux3"}, {"url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/morokosi", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/fs-eire", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ZzqiZQute", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "7028b56a7eb572b73873aed731a7f9c9365f5ee4", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-3.2.0.tgz", "fileCount": 18, "integrity": "sha512-kcwSAWhPi4+QzAtsL2+2s/awvDo2GKLsvMCwNRxb5BUshteXU8U97NCyvQDsGKs/m0He9WcG4YWew/BnuLx++w==", "signatures": [{"sig": "MEUCIDRDepnLOWdMPdDMczqImyaKpVyB2lpq7kgZaTbvWmsHAiEAiAME7WARMys2mj4dfrQqX0jUf17DLULajNb6Sbcz+kk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 419809, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgoqenCRA9TVsSAnZWagAA5HgP/iqSPqFoDvreCqGR8Z76\n06E7jTR+NPRuVG4pXrcAHCIH5oXSq1Synazo/16Zrmf/UUea8OmU3YILv1cf\n2vVGVoNDNBAIKvAriaFZbZFw+eLhqsmFd6dgXv2jIvcf9eLtAGsIpv6kvwtK\nt1lYGha4wYBJalUAj+Sz+5BNVtXjMqg/sDp4gXuhdVZiF8ZeAIWEsDyh+BP/\nwj0fEN5cuSthtJkFPM6frr3xBv9lQKmeaSRaY1nRxH1f9eJOZy2ziWPXe6HR\ncVbVN+gSBs0YM/+ZfiVuMjzvezOa7jn7TtX7LpF0BxewSq6argcjLIPsDHP7\n3jPtGCgY3ZJFz5S9jBX/eggVium/Xh2emI4EFAgRyTfvgc+1aLyf9dcEXiPc\nubKtNY/qRcixg5AbBRz1PF/TItNO0Y5N/gwjy2ilAff4xrV0eIxFYsPfZnJe\np7gjRyXdMFn3Yu+zzdRxndvZu2GKyBZMx0bqXA/4JDZZgsUMfHlziopJ4rn7\nYUeGw4F+Mo7e1XrH2lpKqIN/0QThWw9X9FpNF5KHqVBnLsYmHnaQH7F46lnN\n7YZIck5xH+huqqR1g7TtL/7CNhrespaIf0oZ4mHBAR+S3w1ytqHoxjXd/NgX\nzM2gmMI7u53ds71BzfSXHuFASrtGtpZB2+a85hdILKqFORpKX5poiqYo1ILM\nIoe9\r\n=imr9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "60348d1e38ddd334e6d56a8c69e88be89ab6aeb6", "gypfile": false, "scripts": {"dev": "node test", "doc": "doxygen doc/Doxyfile", "lint": "node tools/clang-format", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "pretest": "node-gyp rebuild -C test", "lint:fix": "node tools/clang-format --fix", "benchmark": "node benchmark", "prebenchmark": "node-gyp rebuild -C benchmark", "dev:incremental": "node test", "predev:incremental": "node-gyp configure build -C test --debug"}, "support": true, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "pre-commit": "lint", "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "7.11.2", "description": "Node.js API (Node-API)", "directories": {}, "_nodeVersion": "16.1.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"bindings": "^1.5.0", "fs-extra": "^9.0.1", "benchmark": "^2.1.4", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1", "clang-format": "^1.4.0"}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api_3.2.0_1621272487345_0.12156209064080126", "host": "s3://npm-registry-packages"}}, "3.2.1": {"name": "node-addon-api", "version": "3.2.1", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "_id": "node-addon-api@3.2.1", "maintainers": [{"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/abhi11210646", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jmendeth", "name": "<PERSON>dez"}, {"url": "https://github.com/timarandras", "name": "<PERSON><PERSON><PERSON>, Dr"}, {"url": "https://github.com/kirbysayshi", "name": "<PERSON>"}, {"url": "https://github.com/anisha-rohra", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/BotellaA", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/azlan", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rivertam", "name": "<PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gallafent", "name": "<PERSON>"}, {"url": "https://github.com/blagoev", "name": "blagoev"}, {"url": "https://github.com/bmacnaughton", "name": "<PERSON>"}, {"url": "https://github.com/corymickelson", "name": "<PERSON>"}, {"url": "https://github.com/danbev", "name": "<PERSON>"}, {"url": "https://github.com/RaisinTen", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/davedoesdev", "name": "<PERSON>"}, {"url": "https://github.com/dmitryash", "name": "<PERSON>"}, {"url": "https://github.com/nadongguri", "name": "Dongjin Na"}, {"url": "https://github.com/fholzer", "name": "<PERSON>"}, {"url": "https://github.com/ebickle", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/gms1", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/devsnek", "name": "<PERSON>"}, {"url": "https://github.com/helio-frota", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ikokostya", "name": "ikokostya"}, {"url": "https://github.com/JckXia", "name": "<PERSON>"}, {"url": "https://github.com/DuBistKomisch", "name": "<PERSON>"}, {"url": "https://github.com/yjaeseok", "name": "<PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/japj", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jschlight", "name": "<PERSON>"}, {"url": "https://github.com/romandev", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/JoseExposito", "name": "<PERSON>"}, {"url": "https://github.com/joshgarde", "name": "j<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/hanazuki", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kelvinhammond", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/<PERSON>Eady", "name": "<PERSON>"}, {"url": "https://github.com/kecsou", "name": "Kévin VOYER"}, {"url": "https://github.com/kidneysolo", "name": "kidneysolo"}, {"url": "https://github.com/Nishikoh", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/koistya", "name": "<PERSON>"}, {"url": "https://github.com/kfarnung", "name": "<PERSON>"}, {"url": "https://github.com/legendecas", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Brooooooklyn", "name": "LongYinan"}, {"url": "https://github.com/lovell", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/lmartorella", "name": "<PERSON>"}, {"url": "https://github.com/mastergberry", "name": "<PERSON>gberry"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/mikepricedev", "name": "<PERSON>"}, {"url": "https://github.com/kYroL01", "name": "Michele Campus"}, {"url": "https://github.com/mcheshkov", "name": "<PERSON>"}, {"url": "https://github.com/nempoBu4", "name": "nempoBu4"}, {"url": "https://github.com/NickNaso", "name": "<PERSON>"}, {"url": "https://github.com/iSkore", "name": "<PERSON>"}, {"url": "https://github.com/seishun", "name": "<PERSON>"}, {"url": "https://github.com/anurbol", "name": "Nurbol <PERSON>"}, {"url": "https://github.com/pacop", "name": "pacop"}, {"url": "https://github.com/DaAitch", "name": "<PERSON>"}, {"url": "https://github.com/rolft<PERSON>mans", "name": "<PERSON>"}, {"url": "https://github.com/ross-weir", "name": "<PERSON>"}, {"url": "https://github.com/okuryu", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/sam-github", "name": "<PERSON>"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}, {"url": "https://github.com/fraxken", "name": "<PERSON>"}, {"url": "https://github.com/timrach", "name": "<PERSON>"}, {"url": "https://github.com/tniessen", "name": "<PERSON>"}, {"url": "https://github.com/tux3", "name": "Tux3"}, {"url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/morokosi", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/fs-eire", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ZzqiZQute", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "81325e0a2117789c0128dab65e7e38f07ceba161", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-3.2.1.tgz", "fileCount": 18, "integrity": "sha512-mmcei9JghVNDYydghQmeDX8KoAm0FAiYyIcUt/N4nhyAipB17pllZQDOJD2fotxABnt4Mdz+dKTO7eftLg4d0A==", "signatures": [{"sig": "MEQCIAro6Ym8EQEed5L1Reg3ZK8nngodEteqnctqQunFUpvRAiBC4QRIsR3yEdZ6/H35MVBOew0ImbUY+84wgMcbsrxk5Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 420094, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgsTWLCRA9TVsSAnZWagAAQEUP/3dbqM4Gk480m0LSJdSm\nK4BWKYQae8ECUMOXOMw9ASq5dJFydgciaOAYjLF/VMWSNkcVkmDAvBi2K8gN\n+VgMVzZNJ/9M/TCWUOH82Y9lWrBOQYjRKXJ1/xETw/g+xJtrk7YWhsOqltbN\nUJTrNiEHJTdh+gnD+abr7zr1ywKBVfHLgC8OJ+KeHK0ZUVeegHYRa+NLJYsf\ndQk/wJdzOr6XRcWxEduEfiiCfrq4liQEV+svvTmz4fhCnzJnwxwi1S6CHqY9\ncsS5YNgWh6eGLGV/5F8q5riTDF4E4jn0lJNMRYM1JPOk202lAOjTMxnEp879\na6sk3emQ8CLDk/eqNBgYbuQ1mLicehgzHRnKIsTz5CXF4fkjPJd8rfUY3nXL\njUFvO/BxLJ5KiQZajiwGsm5uWWatsTxD6EOhxZzdUgJbadzEok5xeWA8KgHH\nIfDSlskCpjkAfRxs6kRyxdPT4O8QPw9gR9nxWx5j68p9aiuUpSnsTaRjcbTf\njpYPAUv+Q0yAzBAUVXhCv4Q8SV1WAEuji8+e++L4StUp6R2Tc4x/1owd9Y0h\nNvdfWFFPlBTay58YdCmwqKpIEqwZabKSvcAx3PZa8vdGCjR7sSMSaJPcI7d+\nCHtkmxO1nH9dClV+uKu4n5JWUcmh9WeYtLkKpVXWOd4yN0GpfplSbT+XsAiz\nEgGP\r\n=em9R\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "a0c0492f7ceee6a35de9b272ba2231824a61beca", "gypfile": false, "scripts": {"dev": "node test", "doc": "doxygen doc/Doxyfile", "lint": "node tools/clang-format", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "pretest": "node-gyp rebuild -C test", "lint:fix": "node tools/clang-format --fix", "benchmark": "node benchmark", "prebenchmark": "node-gyp rebuild -C benchmark", "dev:incremental": "node test", "predev:incremental": "node-gyp configure build -C test --debug"}, "support": true, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "pre-commit": "lint", "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "7.11.2", "description": "Node.js API (Node-API)", "directories": {}, "_nodeVersion": "16.1.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"bindings": "^1.5.0", "fs-extra": "^9.0.1", "benchmark": "^2.1.4", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1", "clang-format": "^1.4.0"}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api_3.2.1_1622226315540_0.2932902386891143", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "node-addon-api", "version": "4.0.0", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "_id": "node-addon-api@4.0.0", "maintainers": [{"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/abhi11210646", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jmendeth", "name": "<PERSON>dez"}, {"url": "https://github.com/timarandras", "name": "<PERSON><PERSON><PERSON>, Dr"}, {"url": "https://github.com/kirbysayshi", "name": "<PERSON>"}, {"url": "https://github.com/anisha-rohra", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/BotellaA", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/azlan", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rivertam", "name": "<PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gallafent", "name": "<PERSON>"}, {"url": "https://github.com/blagoev", "name": "blagoev"}, {"url": "https://github.com/bmacnaughton", "name": "<PERSON>"}, {"url": "https://github.com/corymickelson", "name": "<PERSON>"}, {"url": "https://github.com/danbev", "name": "<PERSON>"}, {"url": "https://github.com/RaisinTen", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/davedoesdev", "name": "<PERSON>"}, {"url": "https://github.com/dmitryash", "name": "<PERSON>"}, {"url": "https://github.com/nadongguri", "name": "Dongjin Na"}, {"url": "https://github.com/fholzer", "name": "<PERSON>"}, {"url": "https://github.com/ebickle", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/gms1", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/devsnek", "name": "<PERSON>"}, {"url": "https://github.com/helio-frota", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ikokostya", "name": "ikokostya"}, {"url": "https://github.com/JckXia", "name": "<PERSON>"}, {"url": "https://github.com/DuBistKomisch", "name": "<PERSON>"}, {"url": "https://github.com/yjaeseok", "name": "<PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/japj", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jschlight", "name": "<PERSON>"}, {"url": "https://github.com/romandev", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/JoseExposito", "name": "<PERSON>"}, {"url": "https://github.com/joshgarde", "name": "j<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/hanazuki", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kelvinhammond", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/<PERSON>Eady", "name": "<PERSON>"}, {"url": "https://github.com/kecsou", "name": "Kévin VOYER"}, {"url": "https://github.com/kidneysolo", "name": "kidneysolo"}, {"url": "https://github.com/Nishikoh", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/koistya", "name": "<PERSON>"}, {"url": "https://github.com/kfarnung", "name": "<PERSON>"}, {"url": "https://github.com/legendecas", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Brooooooklyn", "name": "LongYinan"}, {"url": "https://github.com/lovell", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/lmartorella", "name": "<PERSON>"}, {"url": "https://github.com/mastergberry", "name": "<PERSON>gberry"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/mikepricedev", "name": "<PERSON>"}, {"url": "https://github.com/kYroL01", "name": "Michele Campus"}, {"url": "https://github.com/mcheshkov", "name": "<PERSON>"}, {"url": "https://github.com/nempoBu4", "name": "nempoBu4"}, {"url": "https://github.com/NickNaso", "name": "<PERSON>"}, {"url": "https://github.com/iSkore", "name": "<PERSON>"}, {"url": "https://github.com/seishun", "name": "<PERSON>"}, {"url": "https://github.com/anurbol", "name": "Nurbol <PERSON>"}, {"url": "https://github.com/pacop", "name": "pacop"}, {"url": "https://github.com/DaAitch", "name": "<PERSON>"}, {"url": "https://github.com/rolft<PERSON>mans", "name": "<PERSON>"}, {"url": "https://github.com/ross-weir", "name": "<PERSON>"}, {"url": "https://github.com/okuryu", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/sam-github", "name": "<PERSON>"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}, {"url": "https://github.com/fraxken", "name": "<PERSON>"}, {"url": "https://github.com/timrach", "name": "<PERSON>"}, {"url": "https://github.com/tniessen", "name": "<PERSON>"}, {"url": "https://github.com/tux3", "name": "Tux3"}, {"url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/morokosi", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/fs-eire", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ZzqiZQute", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "ac128f43eff7fac4b5f5ef2f39d6d7c2709efead", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-4.0.0.tgz", "fileCount": 17, "integrity": "sha512-ALmRVBFzfwldBfk3SbKfl6+PVMXiCPKZBEfsJqB/EjXAMAI+MfFrEHR+GMRBuI162DihZ1QjEZ8ieYKuRCJ8Hg==", "signatures": [{"sig": "MEYCIQD1D3qKPikx9cHZnNqMbazkZIGIK43JWWMTq2Mg/gAJQwIhAOeOPoEviu7x8/GCPunsYIXhJ54RKolEggJzD5TzSBMG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 354758, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgyKq/CRA9TVsSAnZWagAA8owP/2LHicgSxL8wwiBW1M9N\nk5S1+AKUJy6qRnH2yCZIMibL9hCfBBzCypPXDlDkTfMyMzxvq+J9rIIx+QV3\nRIMfhIXqEKY9N0xG09uExT7+uWhAuYkJ6DRkirKVqU3CE4w2NQ6rRx0wHXEg\numzYQGXHa6zfkmUOnWJkW4Zt1J5dfIhmtgZvYO/5iY+uhCIs3n2UoSmCW2cw\ntsy4rdVe/Bu8rL2KRIvrmhCLJYA3zVSBgP564u0CP/6blyn0LueylAkERBnK\niKfYeOv93p4swf6LOhU35OL8JFLw1Pw7a0Occn0b1yD1b7fGcoTV5rD5XgSZ\n8ArKVHnx9fZAPuDLka4V6OQOcm3HizUOggdK4pkpIzF46BiaYSKaA7LMEHaZ\nWhto/Z0HC3R23bo60tT5BbZM7vRI62UfbUDjxmI10QPPonAylsoRt/6ivX41\n2bkG7TZTR4qOulzDaVyLckZpC+xdbmPQzYR75jUHpw9SjXh5xew2O3yAGXUA\nShXrB6E0jOaon3RrQFaq4UH+EidoDO3zObz5RbjNGdTvJ1+bPucjJSalmMF3\n/IW1+JP5aAKMSKbN1T1caSRmFkBROD+DIEJUKVQS68xpdabk9PnFuFM81zAg\nrViIeObdaVYUnxN8d+aj8pVaojCJmvK3jw5Emo3ESDbME7/eA9W0Yx1Fe+Ic\nlBLn\r\n=jYMR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "ad76ad07f914fab02be5778ec67485916c4626d9", "gypfile": false, "scripts": {"dev": "node test", "doc": "doxygen doc/Doxyfile", "lint": "node tools/clang-format", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "pretest": "node-gyp rebuild -C test", "lint:fix": "node tools/clang-format --fix", "benchmark": "node benchmark", "prebenchmark": "node-gyp rebuild -C benchmark", "dev:incremental": "node test", "predev:incremental": "node-gyp configure build -C test --debug"}, "support": true, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "pre-commit": "lint", "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "7.15.1", "description": "Node.js API (Node-API)", "directories": {}, "_nodeVersion": "16.3.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"bindings": "^1.5.0", "fs-extra": "^9.0.1", "benchmark": "^2.1.4", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1", "clang-format": "^1.4.0"}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api_4.0.0_1623763647162_0.5015393581552483", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "node-addon-api", "version": "4.1.0", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "_id": "node-addon-api@4.1.0", "maintainers": [{"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/abhi11210646", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jmendeth", "name": "<PERSON>dez"}, {"url": "https://github.com/timarandras", "name": "<PERSON><PERSON><PERSON>, Dr"}, {"url": "https://github.com/kirbysayshi", "name": "<PERSON>"}, {"url": "https://github.com/anisha-rohra", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/BotellaA", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/azlan", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rivertam", "name": "<PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gallafent", "name": "<PERSON>"}, {"url": "https://github.com/blagoev", "name": "blagoev"}, {"url": "https://github.com/bmacnaughton", "name": "<PERSON>"}, {"url": "https://github.com/corymickelson", "name": "<PERSON>"}, {"url": "https://github.com/danbev", "name": "<PERSON>"}, {"url": "https://github.com/RaisinTen", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/davedoesdev", "name": "<PERSON>"}, {"url": "https://github.com/dmitryash", "name": "<PERSON>"}, {"url": "https://github.com/nadongguri", "name": "Dongjin Na"}, {"url": "https://github.com/fholzer", "name": "<PERSON>"}, {"url": "https://github.com/ebickle", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/gms1", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/devsnek", "name": "<PERSON>"}, {"url": "https://github.com/helio-frota", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ikokostya", "name": "ikokostya"}, {"url": "https://github.com/JckXia", "name": "<PERSON>"}, {"url": "https://github.com/DuBistKomisch", "name": "<PERSON>"}, {"url": "https://github.com/yjaeseok", "name": "<PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/japj", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jschlight", "name": "<PERSON>"}, {"url": "https://github.com/romandev", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/JoseExposito", "name": "<PERSON>"}, {"url": "https://github.com/joshgarde", "name": "j<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/hanazuki", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kelvinhammond", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/<PERSON>Eady", "name": "<PERSON>"}, {"url": "https://github.com/kecsou", "name": "Kévin VOYER"}, {"url": "https://github.com/kidneysolo", "name": "kidneysolo"}, {"url": "https://github.com/Nishikoh", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/koistya", "name": "<PERSON>"}, {"url": "https://github.com/kfarnung", "name": "<PERSON>"}, {"url": "https://github.com/legendecas", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Brooooooklyn", "name": "LongYinan"}, {"url": "https://github.com/lovell", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/lmartorella", "name": "<PERSON>"}, {"url": "https://github.com/mastergberry", "name": "<PERSON>gberry"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/mikepricedev", "name": "<PERSON>"}, {"url": "https://github.com/kYroL01", "name": "Michele Campus"}, {"url": "https://github.com/mcheshkov", "name": "<PERSON>"}, {"url": "https://github.com/nempoBu4", "name": "nempoBu4"}, {"url": "https://github.com/NickNaso", "name": "<PERSON>"}, {"url": "https://github.com/iSkore", "name": "<PERSON>"}, {"url": "https://github.com/seishun", "name": "<PERSON>"}, {"url": "https://github.com/anurbol", "name": "Nurbol <PERSON>"}, {"url": "https://github.com/pacop", "name": "pacop"}, {"url": "https://github.com/DaAitch", "name": "<PERSON>"}, {"url": "https://github.com/rolft<PERSON>mans", "name": "<PERSON>"}, {"url": "https://github.com/ross-weir", "name": "<PERSON>"}, {"url": "https://github.com/okuryu", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/sam-github", "name": "<PERSON>"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}, {"url": "https://github.com/fraxken", "name": "<PERSON>"}, {"url": "https://github.com/timrach", "name": "<PERSON>"}, {"url": "https://github.com/tniessen", "name": "<PERSON>"}, {"url": "https://github.com/tux3", "name": "Tux3"}, {"url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/morokosi", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/fs-eire", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ZzqiZQute", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "f1722f1f60793584632ffffb79e12ca042c48bd0", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-4.1.0.tgz", "fileCount": 17, "integrity": "sha512-Zz1o1BDX2VtduiAt6kgiUl8jX1Vm3NMboljFYKQJ6ee8AGfiTvM2mlZFI3xPbqjs80rCQgiVJI/DjQ/1QJ0HwA==", "signatures": [{"sig": "MEUCIHbGshX/ATCioCGRX2i8u16QHwvMhBhE4Jr8S/IItdacAiEA7N0mZEgKRtgfRTo1m6G7HghkxOTeNOhY4Vh0KUFYeKs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 374174, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhJjJrCRA9TVsSAnZWagAAmk8P/1g2YdLC5ALfrS6+E0DC\ny2B6ZM1f30281cJmu9h6odef5YXHZ/bDZ4to/9J/DMySXfKVrP2t6pDXjhzf\nce2iw6Q1+26N5W20iEPJb3dobwjvdeUpgjgmhpWBc5cJEiOpo0w9DbX1zH5W\ngaMKArc3o3qR7Qqq/CG1JQ7D1nRjcNlvRDBn0Bls2AELSLal2Ce7Lj/BYK+U\n6sCnx4NLD4E4FDWxjJZwxnoejnOW0ggboyg3l7YLfFG768N9PgmY2wyrRjFH\nsXnQ1KEabKlcTr26V2i5/0WMAClSbFdLp1Glvh4OzC6lBalVOslTwt3fPWBK\nVjfOCfEvxVtUSRxTa4WZmqdxlVRLqy//PbEWZCMcWmawfZzGqO7lFUCjykfK\ne1rsHn668vL0PjE/OdLv5gCyWbOn2ylRBDkuOHBl+1+uz+2NDZr4/NK11KOl\nBiiehTJnCyjU2wNwzPZ3KOYofH1GsCu5TrZA2+nPa3Id+S0wPqf1NkDd4NVK\nl24QjCtsCbhxt/VyRYxfN2/jxHoH+Ikj0tC91C6Zmk3MvpLTnv8CIRtcXPTi\nZi49Ss6O7IHGLdqvULi0ejp73cYmMqRzV6w+3lyabf6EqBn3cZKpaOO3ByUk\noNyfWC+nIPlxRvQah0+D+QnA1u/sP1Z8vACxnV9+5NuCPFI0PMUbLUA2yMAO\nLwnQ\r\n=HSXX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "4a3de56c3e4ed0031635a2f642b27efeeed00add", "gypfile": false, "scripts": {"dev": "node test", "doc": "doxygen doc/Doxyfile", "lint": "node tools/clang-format", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "pretest": "node-gyp rebuild -C test", "lint:fix": "node tools/clang-format --fix", "benchmark": "node benchmark", "prebenchmark": "node-gyp rebuild -C benchmark", "dev:incremental": "node test", "predev:incremental": "node-gyp configure build -C test --debug"}, "support": true, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "pre-commit": "lint", "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Node.js API (Node-API)", "directories": {}, "_nodeVersion": "16.7.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"bindings": "^1.5.0", "fs-extra": "^9.0.1", "benchmark": "^2.1.4", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1", "clang-format": "^1.4.0"}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api_4.1.0_1629893227569_0.690821099562914", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "node-addon-api", "version": "4.2.0", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "_id": "node-addon-api@4.2.0", "maintainers": [{"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/abhi11210646", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jmendeth", "name": "<PERSON>dez"}, {"url": "https://github.com/timarandras", "name": "<PERSON><PERSON><PERSON>, Dr"}, {"url": "https://github.com/kirbysayshi", "name": "<PERSON>"}, {"url": "https://github.com/anisha-rohra", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/BotellaA", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/azlan", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rivertam", "name": "<PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gallafent", "name": "<PERSON>"}, {"url": "https://github.com/blagoev", "name": "blagoev"}, {"url": "https://github.com/bmacnaughton", "name": "<PERSON>"}, {"url": "https://github.com/corymickelson", "name": "<PERSON>"}, {"url": "https://github.com/danbev", "name": "<PERSON>"}, {"url": "https://github.com/RaisinTen", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/davedoesdev", "name": "<PERSON>"}, {"url": "https://github.com/deepakrkris", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/dmitryash", "name": "<PERSON>"}, {"url": "https://github.com/nadongguri", "name": "Dongjin Na"}, {"url": "https://github.com/fholzer", "name": "<PERSON>"}, {"url": "https://github.com/ebickle", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/gms1", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/devsnek", "name": "<PERSON>"}, {"url": "https://github.com/helio-frota", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ikokostya", "name": "ikokostya"}, {"url": "https://github.com/JckXia", "name": "<PERSON>"}, {"url": "https://github.com/DuBistKomisch", "name": "<PERSON>"}, {"url": "https://github.com/yjaeseok", "name": "<PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/japj", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jschlight", "name": "<PERSON>"}, {"url": "https://github.com/romandev", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/JoseExposito", "name": "<PERSON>"}, {"url": "https://github.com/joshgarde", "name": "j<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/hanazuki", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kelvinhammond", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/<PERSON>Eady", "name": "<PERSON>"}, {"url": "https://github.com/kecsou", "name": "Kévin VOYER"}, {"url": "https://github.com/kidneysolo", "name": "kidneysolo"}, {"url": "https://github.com/Nishikoh", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/koistya", "name": "<PERSON>"}, {"url": "https://github.com/kfarnung", "name": "<PERSON>"}, {"url": "https://github.com/legendecas", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Brooooooklyn", "name": "LongYinan"}, {"url": "https://github.com/lovell", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/lmartorella", "name": "<PERSON>"}, {"url": "https://github.com/mastergberry", "name": "<PERSON>gberry"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/mikepricedev", "name": "<PERSON>"}, {"url": "https://github.com/kYroL01", "name": "Michele Campus"}, {"url": "https://github.com/mcheshkov", "name": "<PERSON>"}, {"url": "https://github.com/nempoBu4", "name": "nempoBu4"}, {"url": "https://github.com/NickNaso", "name": "<PERSON>"}, {"url": "https://github.com/iSkore", "name": "<PERSON>"}, {"url": "https://github.com/seishun", "name": "<PERSON>"}, {"url": "https://github.com/anurbol", "name": "Nurbol <PERSON>"}, {"url": "https://github.com/pacop", "name": "pacop"}, {"url": "https://github.com/DaAitch", "name": "<PERSON>"}, {"url": "https://github.com/rolft<PERSON>mans", "name": "<PERSON>"}, {"url": "https://github.com/ross-weir", "name": "<PERSON>"}, {"url": "https://github.com/okuryu", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/sam-github", "name": "<PERSON>"}, {"url": "https://github.com/strager", "name": "strager"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}, {"url": "https://github.com/fraxken", "name": "<PERSON>"}, {"url": "https://github.com/timrach", "name": "<PERSON>"}, {"url": "https://github.com/tniessen", "name": "<PERSON>"}, {"url": "https://github.com/todoroff", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/tux3", "name": "Tux3"}, {"url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/morokosi", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/fs-eire", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ZzqiZQute", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "117cbb5a959dff0992e1c586ae0393573e4d2a87", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-4.2.0.tgz", "fileCount": 17, "integrity": "sha512-eazsqzwG2lskuzBqCGPi7Ac2UgOoMz8JVOXVhTvvPDYhthvNpefx8jWD8Np7Gv+2Sz0FlPWZk0nJV0z598Wn8Q==", "signatures": [{"sig": "MEUCIQCYvKhHOs7x0hOsu05dqQb5LtIVAyJS+mlX+Iw1VVYmqwIgMn482Y0EHMk2IyKRH+3yJQTwvvORY+4agS5j6do4Jmw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 374940, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh202YCRA9TVsSAnZWagAA/ewQAI50xnJBhlk+zQ7ynK/f\ndW731N6vDFlfbUM/l17ok9Ba6TdBbntzcxVhU8pfvsvxnHoD7Yd2ApYOBSr2\n1c4d1ePIaLvVLa5WWTCc5vIo9U2pvshVizD4u07XnyVl2/YK82CF5BGD0+Cp\n+bqEGGCS5fvpNeDfhgc0wlgxVk9oIDEXLfaj4ImnLEqL0QAu8iUzAz5pBs4x\ni7S2IgXkNcPg21y6bdu9p8Nz6f17f4XCA4S9o6bc66+iVPA2lxmGHWoaLrzt\n4/SrEEV48NXIKdsCo58An36HN1BgVzsGLi07wSnsCmobU+zqXWNhI+poI5Pl\nXfXrXivItQ6TvNZLalgRTBMfVH/v1JcjJ+njhtuCsnAOSZXPBjxrgn84tsrN\n1WhLqobc38BIuIq1JFsKUXHu2NdExTntlTUndKJ4690P9qwU9IC7CtxnOxRX\nQBx/Sf/213w9G86OYVDs6MwL6qCchbotEVZZ3Yd0qLgRhHLVFr3ZUmAerMdc\ngq/kxN49eD2/09eX/yPABVUm7x1XZzz5++OqDrEeOnSkS43wIY0Abnwj7/51\nfJ9PyKVLlui29nTBBN5qI7ZSrx6VER8pQ43yjt7/RivgqNZvtuNgg0bh0l1H\nr3+A9SpZJ6BMpasKR+779JPZCzN/lJjUlo4yVoaVmgEaIuy4Xq5W7ho6D4GG\nYM34\r\n=XVIb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "79d5651a11ed9f7b922cf1d5534b9ed5b37d742d", "gypfile": false, "scripts": {"dev": "node test", "doc": "doxygen doc/Doxyfile", "lint": "node tools/clang-format", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "pretest": "node-gyp rebuild -C test", "lint:fix": "node tools/clang-format --fix", "benchmark": "node benchmark", "prebenchmark": "node-gyp rebuild -C benchmark", "dev:incremental": "node test", "predev:incremental": "node-gyp configure build -C test --debug"}, "support": true, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "pre-commit": "lint", "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "7.21.1", "description": "Node.js API (Node-API)", "directories": {}, "_nodeVersion": "16.9.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"path": "^0.12.7", "bindings": "^1.5.0", "fs-extra": "^9.0.1", "benchmark": "^2.1.4", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1", "clang-format": "^1.4.0"}, "optionalDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api_4.2.0_1631897783997_0.5996735517896796", "host": "s3://npm-registry-packages"}}, "4.3.0": {"name": "node-addon-api", "version": "4.3.0", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "_id": "node-addon-api@4.3.0", "maintainers": [{"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/abhi11210646", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jmendeth", "name": "<PERSON>dez"}, {"url": "https://github.com/alexanderfloh", "name": "<PERSON>"}, {"url": "https://github.com/timarandras", "name": "<PERSON><PERSON><PERSON>, Dr"}, {"url": "https://github.com/kirbysayshi", "name": "<PERSON>"}, {"url": "https://github.com/anisha-rohra", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/BotellaA", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/azlan", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rivertam", "name": "<PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gallafent", "name": "<PERSON>"}, {"url": "https://github.com/blagoev", "name": "blagoev"}, {"url": "https://github.com/bmacnaughton", "name": "<PERSON>"}, {"url": "https://github.com/corymickelson", "name": "<PERSON>"}, {"url": "https://github.com/danbev", "name": "<PERSON>"}, {"url": "https://github.com/RaisinTen", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/davedoesdev", "name": "<PERSON>"}, {"url": "https://github.com/deepakrkris", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/dmitryash", "name": "<PERSON>"}, {"url": "https://github.com/nadongguri", "name": "Dongjin Na"}, {"url": "https://github.com/rubiagatra", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/fholzer", "name": "<PERSON>"}, {"url": "https://github.com/ebickle", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/gms1", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/devsnek", "name": "<PERSON>"}, {"url": "https://github.com/helio-frota", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ikokostya", "name": "ikokostya"}, {"url": "https://github.com/JckXia", "name": "<PERSON>"}, {"url": "https://github.com/DuBistKomisch", "name": "<PERSON>"}, {"url": "https://github.com/yjaeseok", "name": "<PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/japj", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jschlight", "name": "<PERSON>"}, {"url": "https://github.com/romandev", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/JoseExposito", "name": "<PERSON>"}, {"url": "https://github.com/joshgarde", "name": "j<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/hanazuki", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kelvinhammond", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/<PERSON>Eady", "name": "<PERSON>"}, {"url": "https://github.com/kecsou", "name": "Kévin VOYER"}, {"url": "https://github.com/kidneysolo", "name": "kidneysolo"}, {"url": "https://github.com/Nishikoh", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/koistya", "name": "<PERSON>"}, {"url": "https://github.com/kfarnung", "name": "<PERSON>"}, {"url": "https://github.com/legendecas", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Brooooooklyn", "name": "LongYinan"}, {"url": "https://github.com/lovell", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/lmartorella", "name": "<PERSON>"}, {"url": "https://github.com/mastergberry", "name": "<PERSON>gberry"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/mikepricedev", "name": "<PERSON>"}, {"url": "https://github.com/kYroL01", "name": "Michele Campus"}, {"url": "https://github.com/mcheshkov", "name": "<PERSON>"}, {"url": "https://github.com/nempoBu4", "name": "nempoBu4"}, {"url": "https://github.com/NickNaso", "name": "<PERSON>"}, {"url": "https://github.com/iSkore", "name": "<PERSON>"}, {"url": "https://github.com/seishun", "name": "<PERSON>"}, {"url": "https://github.com/anurbol", "name": "Nurbol <PERSON>"}, {"url": "https://github.com/pacop", "name": "pacop"}, {"url": "https://github.com/DaAitch", "name": "<PERSON>"}, {"url": "https://github.com/rolft<PERSON>mans", "name": "<PERSON>"}, {"url": "https://github.com/ross-weir", "name": "<PERSON>"}, {"url": "https://github.com/okuryu", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/sam-github", "name": "<PERSON>"}, {"url": "https://github.com/strager", "name": "strager"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}, {"url": "https://github.com/fraxken", "name": "<PERSON>"}, {"url": "https://github.com/timrach", "name": "<PERSON>"}, {"url": "https://github.com/tniessen", "name": "<PERSON>"}, {"url": "https://github.com/todoroff", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/tux3", "name": "Tux3"}, {"url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/WenheLI", "name": "WenheLI"}, {"url": "https://github.com/morokosi", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/fs-eire", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ZzqiZQute", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "52a1a0b475193e0928e98e0426a0d1254782b77f", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-4.3.0.tgz", "fileCount": 18, "integrity": "sha512-73sE9+3UaLYYFmDsFZnqCInzPyh3MqIwZO9cw58yIqAZhONrrabrYyYe3TuIqtIiOuTXVhsGau8hcrhhwSsDIQ==", "signatures": [{"sig": "MEYCIQCbh0z3KBr+6iEy9bz/2VrKw8bL2dVYuTD0ASkkDUQQQQIhAJ5/Gwd0eTw8yijZpq4ReUp16d2uWCmHkUfSnAydAPpG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 384042, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6sv8CRA9TVsSAnZWagAA6csP/0+vUmMkOtOapGaIgSqh\nZ/jUf+9H5Cz1pqGUY1dqe6QoHawi/zCaTByPCNnEM5gfIcPAv3GO023RvUYq\n8Nz9ZJ4rrWTMb3QEJlJcLtbOsEAP0as/zG6mNWVvviVAiTpV9JZiaemSusuM\nY3/rH6iCmLFtwWnSzrd8Z3ZhFHExKns8Lq4YTPnIhGlbJjvPlPicTtqGS+ho\nty5N+2ZjVB2JloW1yaFpicWjv/l7+2gzdDAi/rkLygYybijkDHVwyhkeKfR5\n3YtMTzu//fVUR1FGSwiGpZFKUOE+uUF9dnNODCurOoFdpLadXq+4cC1qOZWx\nG8+5PIO0Zmr13tVc8ZNbW/RUn8VA62qLDMy9oTEtlyNzVtX4FNSUv41GvJdY\nLcbahh/Z6vg0vPGWGS25+bAUlBLglvkcX4aJRNbdyAnxu2GBOJYH1n5zdsE/\nOJYeb7PfbaWnxsNNqOOqT7dq9QFYlKHRzxxCJjihZnUGXHHf7GHwnnJGwyL9\nI0OG/wb+wiW12vT8IgU/vZGz5K9ky3m0x6i67SopCOEqdrWubp0p5HAwMXnG\n/hEKsFFfOVeFK2rwOPEIPvdBvCLrLWI1S3gXIkFmmuXG5oO6eDiuM+2aYLsP\n/bl7bWs00i2ZcPXEXuk7tLeeQTA2FTDOOAagjUlk+tNOkNJ+kFcb7PxyiZch\nnI1e\r\n=lQKC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "133e13d0d51e5e4bc75295b9bad07d0483da178d", "gypfile": false, "scripts": {"dev": "node test", "doc": "doxygen doc/Doxyfile", "lint": "node tools/eslint-format && node tools/clang-format", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "pretest": "node-gyp rebuild -C test", "lint:fix": "node tools/clang-format --fix && node tools/eslint-format --fix", "benchmark": "node benchmark", "prebenchmark": "node-gyp rebuild -C benchmark", "dev:incremental": "node test", "predev:incremental": "node-gyp configure build -C test --debug"}, "support": true, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "pre-commit": "lint", "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "8.3.1", "description": "Node.js API (Node-API)", "directories": {}, "_nodeVersion": "17.4.0", "_hasShrinkwrap": false, "devDependencies": {"path": "^0.12.7", "eslint": "^7.32.0", "bindings": "^1.5.0", "fs-extra": "^9.0.1", "benchmark": "^2.1.4", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1", "clang-format": "^1.4.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.24.2", "eslint-plugin-promise": "^5.1.0", "eslint-config-standard": "^16.0.3", "eslint-config-semistandard": "^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api_4.3.0_1642777596668_0.14728813538445684", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "node-addon-api", "version": "5.0.0", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "_id": "node-addon-api@5.0.0", "maintainers": [{"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/abhi11210646", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jmendeth", "name": "<PERSON>dez"}, {"url": "https://github.com/alexanderfloh", "name": "<PERSON>"}, {"url": "https://github.com/timarandras", "name": "<PERSON><PERSON><PERSON>, Dr"}, {"url": "https://github.com/kirbysayshi", "name": "<PERSON>"}, {"url": "https://github.com/anisha-rohra", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/BotellaA", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/azlan", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rivertam", "name": "<PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gallafent", "name": "<PERSON>"}, {"url": "https://github.com/blagoev", "name": "blagoev"}, {"url": "https://github.com/bmacnaughton", "name": "<PERSON>"}, {"url": "https://github.com/corymickelson", "name": "<PERSON>"}, {"url": "https://github.com/danbev", "name": "<PERSON>"}, {"url": "https://github.com/RaisinTen", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/davedoesdev", "name": "<PERSON>"}, {"url": "https://github.com/deepakrkris", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/dmitryash", "name": "<PERSON>"}, {"url": "https://github.com/nadongguri", "name": "Dongjin Na"}, {"url": "https://github.com/rubiagatra", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ebickle", "name": "<PERSON>"}, {"url": "https://github.com/extremeheat", "name": "extremeheat"}, {"url": "https://github.com/fholzer", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/gms1", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/devsnek", "name": "<PERSON>"}, {"url": "https://github.com/helio-frota", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ikokostya", "name": "ikokostya"}, {"url": "https://github.com/JckXia", "name": "<PERSON>"}, {"url": "https://github.com/DuBistKomisch", "name": "<PERSON>"}, {"url": "https://github.com/yjaeseok", "name": "<PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/japj", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jschlight", "name": "<PERSON>"}, {"url": "https://github.com/romandev", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/JoseExposito", "name": "<PERSON>"}, {"url": "https://github.com/joshgarde", "name": "j<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/hanazuki", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kelvinhammond", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/<PERSON>Eady", "name": "<PERSON>"}, {"url": "https://github.com/kecsou", "name": "Kévin VOYER"}, {"url": "https://github.com/kidneysolo", "name": "kidneysolo"}, {"url": "https://github.com/Nishikoh", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/koistya", "name": "<PERSON>"}, {"url": "https://github.com/kfarnung", "name": "<PERSON>"}, {"url": "https://github.com/legendecas", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Brooooooklyn", "name": "LongYinan"}, {"url": "https://github.com/lovell", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/lmartorella", "name": "<PERSON>"}, {"url": "https://github.com/mastergberry", "name": "<PERSON>gberry"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/mikepricedev", "name": "<PERSON>"}, {"url": "https://github.com/kYroL01", "name": "Michele Campus"}, {"url": "https://github.com/mcheshkov", "name": "<PERSON>"}, {"url": "https://github.com/nempoBu4", "name": "nempoBu4"}, {"url": "https://github.com/NickNaso", "name": "<PERSON>"}, {"url": "https://github.com/iSkore", "name": "<PERSON>"}, {"url": "https://github.com/seishun", "name": "<PERSON>"}, {"url": "https://github.com/anurbol", "name": "Nurbol <PERSON>"}, {"url": "https://github.com/pacop", "name": "pacop"}, {"url": "https://github.com/DaAitch", "name": "<PERSON>"}, {"url": "https://github.com/rgerd", "name": "rgerd"}, {"url": "https://github.com/rolft<PERSON>mans", "name": "<PERSON>"}, {"url": "https://github.com/ross-weir", "name": "<PERSON>"}, {"url": "https://github.com/okuryu", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/sam-github", "name": "<PERSON>"}, {"url": "https://github.com/strager", "name": "strager"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}, {"url": "https://github.com/fraxken", "name": "<PERSON>"}, {"url": "https://github.com/timrach", "name": "<PERSON>"}, {"url": "https://github.com/tniessen", "name": "<PERSON>"}, {"url": "https://github.com/todoroff", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/tux3", "name": "Tux3"}, {"url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/vmoroz", "name": "<PERSON>"}, {"url": "https://github.com/WenheLI", "name": "WenheLI"}, {"url": "https://github.com/meixg", "name": "<PERSON><PERSON>ng <PERSON>"}, {"url": "https://github.com/morokosi", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/fs-eire", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ZzqiZQute", "name": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "7d7e6f9ef89043befdb20c1989c905ebde18c501", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-5.0.0.tgz", "fileCount": 18, "integrity": "sha512-CvkDw2OEnme7ybCykJpVcKH+uAOLV2qLqiyla128dN9TkEWfrYmxG6C2boDe5KcNQqZF3orkqzGgOMvZ/JNekA==", "signatures": [{"sig": "MEUCIQDmTr7OsBXjkvX1a1pIoBgUEbOcObR7oRZmoyIJTq4gSQIgEZ85Ny60c7G3g1YZz0Dgk7UE73/MJd1Efq3087px/M4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 387017, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJib7fGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr/WA/+PaK+VRE7ssR0weWbl1LJgde0I1WDsbpL19izska3umTPme5G\r\nJEhrMMLq1VGkchBLmwIplAQki6BPApJ1TECfD3zub1+QjxfQIEevKD2W2fRN\r\np2c9486Q2CCHGR/Wpe9TqiHb3NXUfZpe+q/bZ10QlVLRQuJZ7QnVjsPBipkR\r\ncvPrUNc95LZDWIozWnf4UIoeXrJ5HMLvQ4v9S8V5TrluR3IshSdlcxirsogU\r\nIPZNmmd+f9eSvosDluIUIV/OuAcAb3f1r8WNI2r7UmSXbW3zf59nOO780eEj\r\nDN8pb3BjCvbhZOLNSkUZVHtbDInFFqOtrvfJM5Y+DoYLOGuuYHz7yWdG48N0\r\nBLdh6g5XlGyWo/aMPrEyXj0sIIofM196U1hBAhEWn/AgtjE9AH50v+TVv0+N\r\nOat6w6w25AOroQTqGPMOwKzqhK+aEiXsMmRIb50BEhixt1fmSeQ7dWamaWq4\r\n2fL17U0jazPwcHTYvMmpmKkVPm8Fnl6RnSp3MeuQfX0J7G1ydUj0c9vMoDsN\r\nv78ezy9QyCJx/a+noSJlDB0DIdR0cVO5VNNGbAbG9k6o8tgCjNPRvemH6876\r\nUhMFY1h6hi+OOOA5sMqCXmyvFN01ZIS1F8TUAKloVTeCG2PuzPGBPhTulpVu\r\naWL3Y86KFIo+5L0+JZ2GW9qCSnD75wIqYZs=\r\n=/WY+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "e469ee6b88c2efeb20e05c9a19c16e73a43592d5", "gypfile": false, "scripts": {"dev": "node test", "doc": "doxygen doc/Doxyfile", "lint": "node tools/eslint-format && node tools/clang-format", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "pretest": "node-gyp rebuild -C test", "lint:fix": "node tools/clang-format --fix && node tools/eslint-format --fix", "benchmark": "node benchmark", "prebenchmark": "node-gyp rebuild -C benchmark", "dev:incremental": "node test", "predev:incremental": "node-gyp configure build -C test --debug"}, "support": true, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "pre-commit": "lint", "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "8.6.0", "description": "Node.js API (Node-API)", "directories": {}, "_nodeVersion": "18.0.0", "_hasShrinkwrap": false, "devDependencies": {"path": "^0.12.7", "eslint": "^7.32.0", "bindings": "^1.5.0", "fs-extra": "^9.0.1", "benchmark": "^2.1.4", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1", "clang-format": "^1.4.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.24.2", "eslint-plugin-promise": "^5.1.0", "eslint-config-standard": "^16.0.3", "eslint-config-semistandard": "^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api_5.0.0_1651488710517_0.7269271347383355", "host": "s3://npm-registry-packages"}}, "5.1.0": {"name": "node-addon-api", "version": "5.1.0", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "_id": "node-addon-api@5.1.0", "maintainers": [{"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/abhi11210646", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jmendeth", "name": "<PERSON>dez"}, {"url": "https://github.com/alexanderfloh", "name": "<PERSON>"}, {"url": "https://github.com/ammarfaizi2", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/timarandras", "name": "<PERSON><PERSON><PERSON>, Dr"}, {"url": "https://github.com/kirbysayshi", "name": "<PERSON>"}, {"url": "https://github.com/anisha-rohra", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/BotellaA", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/azlan", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rivertam", "name": "<PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gallafent", "name": "<PERSON>"}, {"url": "https://github.com/blagoev", "name": "blagoev"}, {"url": "https://github.com/bmacnaughton", "name": "<PERSON>"}, {"url": "https://github.com/corymickelson", "name": "<PERSON>"}, {"url": "https://github.com/danbev", "name": "<PERSON>"}, {"url": "https://github.com/dantehemerson", "name": "<PERSON>"}, {"url": "https://github.com/RaisinTen", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/davedoesdev", "name": "<PERSON>"}, {"url": "https://github.com/deepakrkris", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/dmitryash", "name": "<PERSON>"}, {"url": "https://github.com/nadongguri", "name": "Dongjin Na"}, {"url": "https://github.com/rubiagatra", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ebickle", "name": "<PERSON>"}, {"url": "https://github.com/extremeheat", "name": "extremeheat"}, {"url": "https://github.com/F3n67u", "name": "<PERSON>"}, {"url": "https://github.com/fholzer", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/gms1", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/devsnek", "name": "<PERSON>"}, {"url": "https://github.com/helio-frota", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ikokostya", "name": "ikokostya"}, {"url": "https://github.com/JckXia", "name": "<PERSON>"}, {"url": "https://github.com/DuBistKomisch", "name": "<PERSON>"}, {"url": "https://github.com/yjaeseok", "name": "<PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/egg-bread", "name": "<PERSON>"}, {"url": "https://github.com/japj", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jschlight", "name": "<PERSON>"}, {"url": "https://github.com/romandev", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/JoseExposito", "name": "<PERSON>"}, {"url": "https://github.com/joshgarde", "name": "j<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/julianmesa-gitkraken", "name": "<PERSON>"}, {"url": "https://github.com/hanazuki", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kelvinhammond", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/<PERSON>Eady", "name": "<PERSON>"}, {"url": "https://github.com/kecsou", "name": "Kévin VOYER"}, {"url": "https://github.com/kidneysolo", "name": "kidneysolo"}, {"url": "https://github.com/Nishikoh", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/koistya", "name": "<PERSON>"}, {"url": "https://github.com/kfarnung", "name": "<PERSON>"}, {"url": "https://github.com/nullromo", "name": "<PERSON>"}, {"url": "https://github.com/legendecas", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Brooooooklyn", "name": "LongYinan"}, {"url": "https://github.com/lovell", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/lmartorella", "name": "<PERSON>"}, {"url": "https://github.com/mastergberry", "name": "<PERSON>gberry"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/mikepricedev", "name": "<PERSON>"}, {"url": "https://github.com/kYroL01", "name": "Michele Campus"}, {"url": "https://github.com/mcheshkov", "name": "<PERSON>"}, {"url": "https://github.com/nempoBu4", "name": "nempoBu4"}, {"url": "https://github.com/NickNaso", "name": "<PERSON>"}, {"url": "https://github.com/iSkore", "name": "<PERSON>"}, {"url": "https://github.com/seishun", "name": "<PERSON>"}, {"url": "https://github.com/anurbol", "name": "Nurbol <PERSON>"}, {"url": "https://github.com/pacop", "name": "pacop"}, {"url": "https://github.com/petersandor", "name": "<PERSON>"}, {"url": "https://github.com/DaAitch", "name": "<PERSON>"}, {"url": "https://github.com/rgerd", "name": "rgerd"}, {"url": "https://github.com/richardlau", "name": "<PERSON>"}, {"url": "https://github.com/rolft<PERSON>mans", "name": "<PERSON>"}, {"url": "https://github.com/ross-weir", "name": "<PERSON>"}, {"url": "https://github.com/okuryu", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/chineduG", "name": "<PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/sam-github", "name": "<PERSON>"}, {"url": "https://github.com/strager", "name": "strager"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}, {"url": "https://github.com/fraxken", "name": "<PERSON>"}, {"url": "https://github.com/timrach", "name": "<PERSON>"}, {"url": "https://github.com/tniessen", "name": "<PERSON>"}, {"url": "https://github.com/todoroff", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/tux3", "name": "Tux3"}, {"url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/vmoroz", "name": "<PERSON>"}, {"url": "https://github.com/WenheLI", "name": "WenheLI"}, {"url": "https://github.com/meixg", "name": "<PERSON><PERSON>ng <PERSON>"}, {"url": "https://github.com/morokosi", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/fs-eire", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ZzqiZQute", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/F3n67u", "name": "<PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "49da1ca055e109a23d537e9de43c09cca21eb762", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-5.1.0.tgz", "fileCount": 18, "integrity": "sha512-eh0GgfEkpnoWDq+VY8OyvYhFEzBk6jIYbRKdIlyTiAXIVJ8PyBaKb0rp7oDtoddbdoHWhq8wwr+XZ81F1rpNdA==", "signatures": [{"sig": "MEYCIQCGKHQYKR8EoyskwDDPfLWnWgIk4BQ16l2/ef5Ovne7aQIhAPyKrR8bi2u/EX6khmyNj3s/Pzbu95hnnXThfnfCVc5c", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 379971, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjwULpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrJ8A/9E/pwiR1N5T91twxyYGVlvgf0n6SNZeqPDFJwDbZyC1mMfn2S\r\n2dpjMShLAGD7ZmG3NH7RpxqNHc6onnOJNIywIB8MH5lI+cdTMm4bfExJo/cs\r\n0S6DR6F79DElW+K6pQX2z6RrFwgFOZci1XaqF3ouXMKyytjgR4L+V6NFNAv+\r\nurynpdjRrwh88t9HArQz6WRQLrmv5DPcuhWLEGYq9xk+tD0gwMaLABysNxFQ\r\nbzpKPLMDCjwE00cAtkglUzEJaaiLS9i/JHUe+z8hN2Qkcqj5oYtVG2CM0Xj3\r\nSgEMXAjriXKvihExvavb2W8hWcEHGcHHu5dWjO8XoLLRb2NpWGRNUqG/q6ot\r\n93W0wuv2LizPGAz45TyxL0zGlKDTpKSOaiWySwhYOpATjyt1+Ds34fzPsOU9\r\n/oF5cvhie2kt3oj8XHZYGRRiGdnuYWBNLrj4lFuVb7F44w8TRMDlsHTDRZQX\r\nS39bVtG7qyoBZxA1JjzRCg24b4poJ6Bv1iPi5zH5/PcmdiiVoKnd13OdKu+W\r\nt+nHA933ZQmIGcieC6g2TylFWk7fHDMvqIg4oIm8cgwd6h2kVCGzWDcgX+Ty\r\nX8wpzDDYsPuJE1mBvbn8blROmH0F+P2ND5lyFGHsRyABAthuCJfDN4Q8dwml\r\noxaL0MboMKzS3D0SXUX1rXjtdH8l/ySXo8Q=\r\n=I+q+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "34221c1fe69bbbe4762a6a3bede80456327dd3c2", "gypfile": false, "scripts": {"dev": "node test", "doc": "doxygen doc/Doxyfile", "lint": "node tools/eslint-format && node tools/clang-format", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "pretest": "node-gyp rebuild -C test", "lint:fix": "node tools/clang-format --fix && node tools/eslint-format --fix", "benchmark": "node benchmark", "test:debug": "node-gyp rebuild -C test --debug && NODE_API_BUILD_CONFIG=Debug node ./test/index.js", "prebenchmark": "node-gyp rebuild -C benchmark", "dev:incremental": "node test", "predev:incremental": "node-gyp configure build -C test --debug"}, "support": true, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "pre-commit": "lint", "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Node.js API (Node-API)", "directories": {}, "_nodeVersion": "16.18.0", "_hasShrinkwrap": false, "devDependencies": {"path": "^0.12.7", "eslint": "^7.32.0", "bindings": "^1.5.0", "fs-extra": "^9.0.1", "benchmark": "^2.1.4", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1", "clang-format": "^1.4.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.24.2", "eslint-plugin-promise": "^5.1.0", "eslint-config-standard": "^16.0.3", "eslint-config-semistandard": "^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api_5.1.0_1673609961479_0.7021857707619277", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "node-addon-api", "version": "6.0.0", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "_id": "node-addon-api@6.0.0", "maintainers": [{"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/abhi11210646", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jmendeth", "name": "<PERSON>dez"}, {"url": "https://github.com/alexanderfloh", "name": "<PERSON>"}, {"url": "https://github.com/ammarfaizi2", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/timarandras", "name": "<PERSON><PERSON><PERSON>, Dr"}, {"url": "https://github.com/kirbysayshi", "name": "<PERSON>"}, {"url": "https://github.com/anisha-rohra", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/BotellaA", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/azlan", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rivertam", "name": "<PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gallafent", "name": "<PERSON>"}, {"url": "https://github.com/blagoev", "name": "blagoev"}, {"url": "https://github.com/bmacnaughton", "name": "<PERSON>"}, {"url": "https://github.com/corymickelson", "name": "<PERSON>"}, {"url": "https://github.com/danbev", "name": "<PERSON>"}, {"url": "https://github.com/dantehemerson", "name": "<PERSON>"}, {"url": "https://github.com/RaisinTen", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/davedoesdev", "name": "<PERSON>"}, {"url": "https://github.com/deepakrkris", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/dmitryash", "name": "<PERSON>"}, {"url": "https://github.com/nadongguri", "name": "Dongjin Na"}, {"url": "https://github.com/rubiagatra", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ebickle", "name": "<PERSON>"}, {"url": "https://github.com/extremeheat", "name": "extremeheat"}, {"url": "https://github.com/F3n67u", "name": "<PERSON>"}, {"url": "https://github.com/fholzer", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/gms1", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/devsnek", "name": "<PERSON>"}, {"url": "https://github.com/helio-frota", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ikokostya", "name": "ikokostya"}, {"url": "https://github.com/JckXia", "name": "<PERSON>"}, {"url": "https://github.com/DuBistKomisch", "name": "<PERSON>"}, {"url": "https://github.com/yjaeseok", "name": "<PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/egg-bread", "name": "<PERSON>"}, {"url": "https://github.com/japj", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jschlight", "name": "<PERSON>"}, {"url": "https://github.com/romandev", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/JoseExposito", "name": "<PERSON>"}, {"url": "https://github.com/joshgarde", "name": "j<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/julianmesa-gitkraken", "name": "<PERSON>"}, {"url": "https://github.com/hanazuki", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kelvinhammond", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/<PERSON>Eady", "name": "<PERSON>"}, {"url": "https://github.com/kecsou", "name": "Kévin VOYER"}, {"url": "https://github.com/kidneysolo", "name": "kidneysolo"}, {"url": "https://github.com/Nishikoh", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/koistya", "name": "<PERSON>"}, {"url": "https://github.com/kfarnung", "name": "<PERSON>"}, {"url": "https://github.com/nullromo", "name": "<PERSON>"}, {"url": "https://github.com/legendecas", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Brooooooklyn", "name": "LongYinan"}, {"url": "https://github.com/lovell", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/lmartorella", "name": "<PERSON>"}, {"url": "https://github.com/mastergberry", "name": "<PERSON>gberry"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/mikepricedev", "name": "<PERSON>"}, {"url": "https://github.com/kYroL01", "name": "Michele Campus"}, {"url": "https://github.com/mcheshkov", "name": "<PERSON>"}, {"url": "https://github.com/nempoBu4", "name": "nempoBu4"}, {"url": "https://github.com/NickNaso", "name": "<PERSON>"}, {"url": "https://github.com/iSkore", "name": "<PERSON>"}, {"url": "https://github.com/seishun", "name": "<PERSON>"}, {"url": "https://github.com/anurbol", "name": "Nurbol <PERSON>"}, {"url": "https://github.com/pacop", "name": "pacop"}, {"url": "https://github.com/petersandor", "name": "<PERSON>"}, {"url": "https://github.com/DaAitch", "name": "<PERSON>"}, {"url": "https://github.com/rgerd", "name": "rgerd"}, {"url": "https://github.com/richardlau", "name": "<PERSON>"}, {"url": "https://github.com/rolft<PERSON>mans", "name": "<PERSON>"}, {"url": "https://github.com/ross-weir", "name": "<PERSON>"}, {"url": "https://github.com/okuryu", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/chineduG", "name": "<PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/sam-github", "name": "<PERSON>"}, {"url": "https://github.com/strager", "name": "strager"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}, {"url": "https://github.com/fraxken", "name": "<PERSON>"}, {"url": "https://github.com/timrach", "name": "<PERSON>"}, {"url": "https://github.com/tniessen", "name": "<PERSON>"}, {"url": "https://github.com/todoroff", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/tux3", "name": "Tux3"}, {"url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/vmoroz", "name": "<PERSON>"}, {"url": "https://github.com/WenheLI", "name": "WenheLI"}, {"url": "https://github.com/meixg", "name": "<PERSON><PERSON>ng <PERSON>"}, {"url": "https://github.com/morokosi", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/fs-eire", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ZzqiZQute", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/F3n67u", "name": "<PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "cfb3574e6df708ff71a30db6c4762d9e06e11c27", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-6.0.0.tgz", "fileCount": 18, "integrity": "sha512-GyHvgPvUXBvAkXa0YvYnhilSB1A+FRYMpIVggKzPZqdaZfevZOuzfWzyvgzOwRLHBeo/MMswmJFsrNF4Nw1pmA==", "signatures": [{"sig": "MEUCIQC/kSRgJ1SrAfUbdHkOhW25MkzDS0oBWmM3cLIlukmx/gIgeelRJg4C23MHRWmHEm3ladBYAk35593nWmHpdlsfpz8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 379710, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5n1zACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrAPw/7BUC0mETAmvJUxEVjvJAqqr7mUYTX1RlgVwtKMEtWDwmJcGUe\r\nY4K6eSp10AX6QvIWwURVhzY//ULQMqK/lX7njPuUyxSMpp0Wh4TCx16EhCL/\r\nLhn5KGEcsOe3OaH+2aq21NBP2tTMbPE8mQja8VHb9LyuZgQE3XOcxXIfTGBu\r\nclSkK8LNcOvCh3Z+pP7doCOXBm/5BgvaymUwQ8q0+hAedJj5KXhZBaG6rGkp\r\npz/11Y/qWT+1C+NigojYl41DQlWT8+BL3z20CbiGqfWthb7zRVoiHlHI1A+o\r\n6hAWM+PSFmzHlXP+30NWKBr8+J8B5J4jDq7cOurAlfvghwXdKWcKXy5i6Pdw\r\nYPSgd2XgFHH8oYBQTNfGbkyH1CnNK8iwpJBcRAFH+iG6IylyeRaz+7AjJyIU\r\nWyAxLCIYJQe4vFJqdzHTn6P665eOq9ipK4h8wDjdjJHVfWMSVg976BOGehtg\r\nIivih065Jun4ZNK9He3j1HcanlNWXteXmaHvztL95PtGG6JCSq7ITnH/Ax80\r\n19P6H0EEjElaw8ZFTJZJGtN1Jfwca5NJCiOBMdrNtNQpTN1j/BcV20I78OOA\r\n9Z6JT3v/hIhx/np9Y6Ri4EG9DtlOPomzP323Ayw7pFZjMFqXQtom7TuMw6t+\r\n86GQ0nycaFuSH17F1kwy8Ptc99xkyIqp9A8=\r\n=O8mV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "c51d6b321fa4fbbc134630ce0c72bdf9a2664b2d", "gypfile": false, "scripts": {"dev": "node test", "doc": "doxygen doc/Doxyfile", "lint": "node tools/eslint-format && node tools/clang-format", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "pretest": "node-gyp rebuild -C test", "lint:fix": "node tools/clang-format --fix && node tools/eslint-format --fix", "benchmark": "node benchmark", "test:debug": "node-gyp rebuild -C test --debug && NODE_API_BUILD_CONFIG=Debug node ./test/index.js", "prebenchmark": "node-gyp rebuild -C benchmark", "dev:incremental": "node test", "predev:incremental": "node-gyp configure build -C test --debug"}, "support": true, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "pre-commit": "lint", "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "9.3.1", "description": "Node.js API (Node-API)", "directories": {}, "_nodeVersion": "19.5.0", "_hasShrinkwrap": false, "devDependencies": {"path": "^0.12.7", "eslint": "^7.32.0", "bindings": "^1.5.0", "fs-extra": "^9.0.1", "benchmark": "^2.1.4", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1", "clang-format": "^1.4.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.24.2", "eslint-plugin-promise": "^5.1.0", "eslint-config-standard": "^16.0.3", "eslint-config-semistandard": "^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api_6.0.0_1676049779732_0.07094269885607285", "host": "s3://npm-registry-packages"}}, "6.1.0": {"name": "node-addon-api", "version": "6.1.0", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "_id": "node-addon-api@6.1.0", "maintainers": [{"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/abhi11210646", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jmendeth", "name": "<PERSON>dez"}, {"url": "https://github.com/alexanderfloh", "name": "<PERSON>"}, {"url": "https://github.com/ammarfaizi2", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/timarandras", "name": "<PERSON><PERSON><PERSON>, Dr"}, {"url": "https://github.com/kirbysayshi", "name": "<PERSON>"}, {"url": "https://github.com/anisha-rohra", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/BotellaA", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/azlan", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rivertam", "name": "<PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gallafent", "name": "<PERSON>"}, {"url": "https://github.com/blagoev", "name": "blagoev"}, {"url": "https://github.com/bmacnaughton", "name": "<PERSON>"}, {"url": "https://github.com/corymickelson", "name": "<PERSON>"}, {"url": "https://github.com/danbev", "name": "<PERSON>"}, {"url": "https://github.com/dantehemerson", "name": "<PERSON>"}, {"url": "https://github.com/RaisinTen", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/davedoesdev", "name": "<PERSON>"}, {"url": "https://github.com/deepakrkris", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/dmitryash", "name": "<PERSON>"}, {"url": "https://github.com/nadongguri", "name": "Dongjin Na"}, {"url": "https://github.com/rubiagatra", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ebickle", "name": "<PERSON>"}, {"url": "https://github.com/extremeheat", "name": "extremeheat"}, {"url": "https://github.com/F3n67u", "name": "<PERSON>"}, {"url": "https://github.com/fholzer", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/gms1", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/devsnek", "name": "<PERSON>"}, {"url": "https://github.com/helio-frota", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ikokostya", "name": "ikokostya"}, {"url": "https://github.com/JckXia", "name": "<PERSON>"}, {"url": "https://github.com/DuBistKomisch", "name": "<PERSON>"}, {"url": "https://github.com/yjaeseok", "name": "<PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/egg-bread", "name": "<PERSON>"}, {"url": "https://github.com/japj", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jschlight", "name": "<PERSON>"}, {"url": "https://github.com/romandev", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/JoseExposito", "name": "<PERSON>"}, {"url": "https://github.com/joshgarde", "name": "j<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/julianmesa-gitkraken", "name": "<PERSON>"}, {"url": "https://github.com/hanazuki", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kelvinhammond", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/<PERSON>Eady", "name": "<PERSON>"}, {"url": "https://github.com/kecsou", "name": "Kévin VOYER"}, {"url": "https://github.com/kidneysolo", "name": "kidneysolo"}, {"url": "https://github.com/Nishikoh", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/koistya", "name": "<PERSON>"}, {"url": "https://github.com/kfarnung", "name": "<PERSON>"}, {"url": "https://github.com/nullromo", "name": "<PERSON>"}, {"url": "https://github.com/legendecas", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Brooooooklyn", "name": "LongYinan"}, {"url": "https://github.com/lovell", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/lmartorella", "name": "<PERSON>"}, {"url": "https://github.com/mastergberry", "name": "<PERSON>gberry"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>"}, {"url": "https://github.com/RedBeard0531", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/mikepricedev", "name": "<PERSON>"}, {"url": "https://github.com/kYroL01", "name": "Michele Campus"}, {"url": "https://github.com/mcheshkov", "name": "<PERSON>"}, {"url": "https://github.com/nempoBu4", "name": "nempoBu4"}, {"url": "https://github.com/NickNaso", "name": "<PERSON>"}, {"url": "https://github.com/iSkore", "name": "<PERSON>"}, {"url": "https://github.com/seishun", "name": "<PERSON>"}, {"url": "https://github.com/anurbol", "name": "Nurbol <PERSON>"}, {"url": "https://github.com/pacop", "name": "pacop"}, {"url": "https://github.com/petersandor", "name": "<PERSON>"}, {"url": "https://github.com/DaAitch", "name": "<PERSON>"}, {"url": "https://github.com/rgerd", "name": "rgerd"}, {"url": "https://github.com/richardlau", "name": "<PERSON>"}, {"url": "https://github.com/rolft<PERSON>mans", "name": "<PERSON>"}, {"url": "https://github.com/ross-weir", "name": "<PERSON>"}, {"url": "https://github.com/okuryu", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/chineduG", "name": "<PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/sam-github", "name": "<PERSON>"}, {"url": "https://github.com/strager", "name": "strager"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}, {"url": "https://github.com/fraxken", "name": "<PERSON>"}, {"url": "https://github.com/timrach", "name": "<PERSON>"}, {"url": "https://github.com/tniessen", "name": "<PERSON>"}, {"url": "https://github.com/todoroff", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/toyobayashi", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/tux3", "name": "Tux3"}, {"url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/vmoroz", "name": "<PERSON>"}, {"url": "https://github.com/WenheLI", "name": "WenheLI"}, {"url": "https://github.com/meixg", "name": "<PERSON><PERSON>ng <PERSON>"}, {"url": "https://github.com/morokosi", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/fs-eire", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ZzqiZQute", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/F3n67u", "name": "<PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "ac8470034e58e67d0c6f1204a18ae6995d9c0d76", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-6.1.0.tgz", "fileCount": 18, "integrity": "sha512-+eawOlIgy680F0kBzPUNFhMZGtJ1YmqM6l4+Crf4IkImjYrO/mqPwRMh352g23uIaQKFItcQ64I7KMaJxHgAVA==", "signatures": [{"sig": "MEYCIQCepwXNKYPG1xYGIJPpVCIhf4Zf8QotuNkY7DIeYPhFaQIhALIJjWmIp0YD/jysdP9Qj9yyUURKJ5yaT69BqvJ1b4IV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 393771, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkQUvSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmreWBAAgtePyzNCext5Hp+0q3oiMEUBtG5EHvOR1KGo8Mfo6VdNs2io\r\n/rtsPuPPp5867qy+Vj+30y8UPcBdVVIzd6tFs7W653Dr7RWDkymq+GOwDVUw\r\njXC2P5y6/w6PtHWdjEUcSe8A5Oc/gZX5onioOKwxJq9Mfil/ngUaOgR1Hq1r\r\nTQPrhi8wDpgcy92VDYEFduLZPTMSu01dWkzFuPVy41GS3lQjSB2SW6xwECll\r\n5ThpFuirFC4nvhMlBjHcoWlUYJXjII27iNIjvU72EyfCkQeWd58vvzBaT7v0\r\nwrGUqQoUt1gxuayYjytQ5JwCudz8pGIALw2uEAK3xCVs0mJPX2Jwyd1/ocAo\r\n2bNIDCOZrIq+uDQ/naHYqMuNv3Nu/hDM32J1sCxh0Aq0rQvR3GNUYsYvJqTH\r\nUdGQ+dpOE8K4sOpCQ7HAu5WTfcAByEhqmyVQXs3Rn0oyt4ikebttW6jujc2z\r\nkKu4rydf9dEL7OXkOBtAjM03pSY/JJZX7J6IJFacxRJnTNapNviZBYtrzmqL\r\n7KZgVT2zCKNyhPkwUpYIOvZjRSh05FsKhnnv9KkM2sZNJyZvfyrs+Alk8yBd\r\n5wGGjXR8ikzSqsmxTv/bwmKIBM6TROsRe2s1oW62ay0GA53FJJ2XQRdOdTzR\r\nWdDKgeFY+wiLv0ABXyr3zBQo6r4s+xZaRgs=\r\n=O4QY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "0e34f228399b7ef7b2223e8320d8260e6a707e03", "gypfile": false, "scripts": {"dev": "node test", "doc": "doxygen doc/Doxyfile", "lint": "node tools/eslint-format && node tools/clang-format", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "pretest": "node-gyp rebuild -C test", "lint:fix": "node tools/clang-format --fix && node tools/eslint-format --fix", "benchmark": "node benchmark", "test:debug": "node-gyp rebuild -C test --debug && NODE_API_BUILD_CONFIG=Debug node ./test/index.js", "prebenchmark": "node-gyp rebuild -C benchmark", "dev:incremental": "node test", "predev:incremental": "node-gyp configure build -C test --debug"}, "support": true, "_npmUser": {"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, "pre-commit": "lint", "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "Node.js API (Node-API)", "directories": {}, "_nodeVersion": "18.15.0", "_hasShrinkwrap": false, "devDependencies": {"path": "^0.12.7", "eslint": "^7.32.0", "bindings": "^1.5.0", "fs-extra": "^9.0.1", "benchmark": "^2.1.4", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1", "clang-format": "^1.4.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.24.2", "eslint-plugin-promise": "^5.1.0", "eslint-config-standard": "^16.0.3", "eslint-config-semistandard": "^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api_6.1.0_1682000850093_0.23091534096277933", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "node-addon-api", "version": "7.0.0", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "_id": "node-addon-api@7.0.0", "maintainers": [{"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "<PERSON>ev<PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/abhi11210646", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jmendeth", "name": "<PERSON>dez"}, {"url": "https://github.com/alexanderfloh", "name": "<PERSON>"}, {"url": "https://github.com/ammarfaizi2", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/timarandras", "name": "<PERSON><PERSON><PERSON>, Dr"}, {"url": "https://github.com/kirbysayshi", "name": "<PERSON>"}, {"url": "https://github.com/anisha-rohra", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/BotellaA", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/azlan", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rivertam", "name": "<PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gallafent", "name": "<PERSON>"}, {"url": "https://github.com/blagoev", "name": "blagoev"}, {"url": "https://github.com/bmacnaughton", "name": "<PERSON>"}, {"url": "https://github.com/corymickelson", "name": "<PERSON>"}, {"url": "https://github.com/danbev", "name": "<PERSON>"}, {"url": "https://github.com/dantehemerson", "name": "<PERSON>"}, {"url": "https://github.com/RaisinTen", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/davedoesdev", "name": "<PERSON>"}, {"url": "https://github.com/deepakrkris", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/dmitryash", "name": "<PERSON>"}, {"url": "https://github.com/nadongguri", "name": "Dongjin Na"}, {"url": "https://github.com/rubiagatra", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ebickle", "name": "<PERSON>"}, {"url": "https://github.com/extremeheat", "name": "extremeheat"}, {"url": "https://github.com/F3n67u", "name": "<PERSON>"}, {"url": "https://github.com/fholzer", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/gms1", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/devsnek", "name": "<PERSON>"}, {"url": "https://github.com/helio-frota", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ikokostya", "name": "ikokostya"}, {"url": "https://github.com/JckXia", "name": "<PERSON>"}, {"url": "https://github.com/DuBistKomisch", "name": "<PERSON>"}, {"url": "https://github.com/yjaeseok", "name": "<PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/egg-bread", "name": "<PERSON>"}, {"url": "https://github.com/japj", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jschlight", "name": "<PERSON>"}, {"url": "https://github.com/romandev", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/JoseExposito", "name": "<PERSON>"}, {"url": "https://github.com/joshgarde", "name": "j<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/julianmesa-gitkraken", "name": "<PERSON>"}, {"url": "https://github.com/hanazuki", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kelvinhammond", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/<PERSON>Eady", "name": "<PERSON>"}, {"url": "https://github.com/kecsou", "name": "Kévin VOYER"}, {"url": "https://github.com/kidneysolo", "name": "kidneysolo"}, {"url": "https://github.com/Nishikoh", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/koistya", "name": "<PERSON>"}, {"url": "https://github.com/kfarnung", "name": "<PERSON>"}, {"url": "https://github.com/nullromo", "name": "<PERSON>"}, {"url": "https://github.com/legendecas", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Brooooooklyn", "name": "LongYinan"}, {"url": "https://github.com/lovell", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/lmartorella", "name": "<PERSON>"}, {"url": "https://github.com/mastergberry", "name": "<PERSON>gberry"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>"}, {"url": "https://github.com/RedBeard0531", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/mikepricedev", "name": "<PERSON>"}, {"url": "https://github.com/kYroL01", "name": "Michele Campus"}, {"url": "https://github.com/mcheshkov", "name": "<PERSON>"}, {"url": "https://github.com/nempoBu4", "name": "nempoBu4"}, {"url": "https://github.com/NickNaso", "name": "<PERSON>"}, {"url": "https://github.com/iSkore", "name": "<PERSON>"}, {"url": "https://github.com/seishun", "name": "<PERSON>"}, {"url": "https://github.com/anurbol", "name": "Nurbol <PERSON>"}, {"url": "https://github.com/pacop", "name": "pacop"}, {"url": "https://github.com/petersandor", "name": "<PERSON>"}, {"url": "https://github.com/DaAitch", "name": "<PERSON>"}, {"url": "https://github.com/rgerd", "name": "rgerd"}, {"url": "https://github.com/richardlau", "name": "<PERSON>"}, {"url": "https://github.com/rolft<PERSON>mans", "name": "<PERSON>"}, {"url": "https://github.com/ross-weir", "name": "<PERSON>"}, {"url": "https://github.com/okuryu", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/chineduG", "name": "<PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/sam-github", "name": "<PERSON>"}, {"url": "https://github.com/strager", "name": "strager"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}, {"url": "https://github.com/fraxken", "name": "<PERSON>"}, {"url": "https://github.com/timrach", "name": "<PERSON>"}, {"url": "https://github.com/tniessen", "name": "<PERSON>"}, {"url": "https://github.com/todoroff", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/toyobayashi", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/tux3", "name": "Tux3"}, {"url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/vmoroz", "name": "<PERSON>"}, {"url": "https://github.com/WenheLI", "name": "WenheLI"}, {"url": "https://github.com/meixg", "name": "<PERSON><PERSON>ng <PERSON>"}, {"url": "https://github.com/morokosi", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/fs-eire", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ZzqiZQute", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/F3n67u", "name": "<PERSON>"}, {"url": "https://github.com/wanlu", "name": "wanlu wang"}, {"url": "https://github.com/chearon", "name": "<PERSON>"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "8136add2f510997b3b94814f4af1cce0b0e3962e", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-7.0.0.tgz", "fileCount": 18, "integrity": "sha512-vgbBJTS4m5/KkE16t5Ly0WW9hz46swAstv0hYYwMtbG7AznRhNyfLRe8HZAiWIpcHzoO7HxhLuBQj9rJ/Ho0ZA==", "signatures": [{"sig": "MEYCIQDVQCkEY5AOexkrn1XlJwa6qtfPtdIMNWCVhn4XWhkvBgIhAI7SPi0z2r74j/sM2Yu6WXSacMjaeIndKhQ/7l8baLwV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 394960}, "main": "index.js", "gitHead": "39a25bf27788ff7a7ea5c64978c4dcd1e7b9d80d", "gypfile": false, "scripts": {"dev": "node test", "doc": "doxygen doc/Doxyfile", "lint": "node tools/eslint-format && node tools/clang-format", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "pretest": "node-gyp rebuild -C test", "lint:fix": "node tools/clang-format --fix && node tools/eslint-format --fix", "benchmark": "node benchmark", "test:debug": "node-gyp rebuild -C test --debug && NODE_API_BUILD_CONFIG=Debug node ./test/index.js", "prebenchmark": "node-gyp rebuild -C benchmark", "dev:incremental": "node test", "predev:incremental": "node-gyp configure build -C test --debug"}, "support": true, "_npmUser": {"name": "<PERSON>ev<PERSON><PERSON>", "email": "<EMAIL>"}, "pre-commit": "lint", "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Node.js API (Node-API)", "directories": {}, "_nodeVersion": "16.15.1", "_hasShrinkwrap": false, "devDependencies": {"path": "^0.12.7", "eslint": "^7.32.0", "bindings": "^1.5.0", "fs-extra": "^9.0.1", "benchmark": "^2.1.4", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1", "clang-format": "^1.4.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.24.2", "eslint-plugin-promise": "^5.1.0", "eslint-config-standard": "^16.0.3", "eslint-config-semistandard": "^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api_7.0.0_1686837348497_0.048134329676088994", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "node-addon-api", "version": "7.1.0", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "_id": "node-addon-api@7.1.0", "maintainers": [{"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "<PERSON>ev<PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/abhi11210646", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jmendeth", "name": "<PERSON>dez"}, {"url": "https://github.com/alexanderfloh", "name": "<PERSON>"}, {"url": "https://github.com/ammarfaizi2", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/timarandras", "name": "<PERSON><PERSON><PERSON>, Dr"}, {"url": "https://github.com/kirbysayshi", "name": "<PERSON>"}, {"url": "https://github.com/anisha-rohra", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/BotellaA", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/azlan", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rivertam", "name": "<PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gallafent", "name": "<PERSON>"}, {"url": "https://github.com/blagoev", "name": "blagoev"}, {"url": "https://github.com/bmacnaughton", "name": "<PERSON>"}, {"url": "https://github.com/corymickelson", "name": "<PERSON>"}, {"url": "https://github.com/danbev", "name": "<PERSON>"}, {"url": "https://github.com/dantehemerson", "name": "<PERSON>"}, {"url": "https://github.com/RaisinTen", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/davedoesdev", "name": "<PERSON>"}, {"url": "https://github.com/deepakrkris", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/dmitryash", "name": "<PERSON>"}, {"url": "https://github.com/nadongguri", "name": "Dongjin Na"}, {"url": "https://github.com/rubiagatra", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ebickle", "name": "<PERSON>"}, {"url": "https://github.com/extremeheat", "name": "extremeheat"}, {"url": "https://github.com/F3n67u", "name": "<PERSON>"}, {"url": "https://github.com/fholzer", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/gms1", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/devsnek", "name": "<PERSON>"}, {"url": "https://github.com/helio-frota", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ikokostya", "name": "ikokostya"}, {"url": "https://github.com/JckXia", "name": "<PERSON>"}, {"url": "https://github.com/DuBistKomisch", "name": "<PERSON>"}, {"url": "https://github.com/yjaeseok", "name": "<PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/egg-bread", "name": "<PERSON>"}, {"url": "https://github.com/japj", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jschlight", "name": "<PERSON>"}, {"url": "https://github.com/romandev", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/JoseExposito", "name": "<PERSON>"}, {"url": "https://github.com/joshgarde", "name": "j<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/julianmesa-gitkraken", "name": "<PERSON>"}, {"url": "https://github.com/hanazuki", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kelvinhammond", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/<PERSON>Eady", "name": "<PERSON>"}, {"url": "https://github.com/kecsou", "name": "Kévin VOYER"}, {"url": "https://github.com/kidneysolo", "name": "kidneysolo"}, {"url": "https://github.com/Nishikoh", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/koistya", "name": "<PERSON>"}, {"url": "https://github.com/kfarnung", "name": "<PERSON>"}, {"url": "https://github.com/nullromo", "name": "<PERSON>"}, {"url": "https://github.com/legendecas", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Brooooooklyn", "name": "LongYinan"}, {"url": "https://github.com/lovell", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/lmartorella", "name": "<PERSON>"}, {"url": "https://github.com/mastergberry", "name": "<PERSON>gberry"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>"}, {"url": "https://github.com/RedBeard0531", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/mikepricedev", "name": "<PERSON>"}, {"url": "https://github.com/kYroL01", "name": "Michele Campus"}, {"url": "https://github.com/mcheshkov", "name": "<PERSON>"}, {"url": "https://github.com/nempoBu4", "name": "nempoBu4"}, {"url": "https://github.com/NickNaso", "name": "<PERSON>"}, {"url": "https://github.com/iSkore", "name": "<PERSON>"}, {"url": "https://github.com/seishun", "name": "<PERSON>"}, {"url": "https://github.com/anurbol", "name": "Nurbol <PERSON>"}, {"url": "https://github.com/pacop", "name": "pacop"}, {"url": "https://github.com/petersandor", "name": "<PERSON>"}, {"url": "https://github.com/DaAitch", "name": "<PERSON>"}, {"url": "https://github.com/rgerd", "name": "rgerd"}, {"url": "https://github.com/richardlau", "name": "<PERSON>"}, {"url": "https://github.com/rolft<PERSON>mans", "name": "<PERSON>"}, {"url": "https://github.com/ross-weir", "name": "<PERSON>"}, {"url": "https://github.com/okuryu", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/chineduG", "name": "<PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/sam-github", "name": "<PERSON>"}, {"url": "https://github.com/strager", "name": "strager"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}, {"url": "https://github.com/fraxken", "name": "<PERSON>"}, {"url": "https://github.com/timrach", "name": "<PERSON>"}, {"url": "https://github.com/tniessen", "name": "<PERSON>"}, {"url": "https://github.com/todoroff", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/toyobayashi", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/tux3", "name": "Tux3"}, {"url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/vmoroz", "name": "<PERSON>"}, {"url": "https://github.com/WenheLI", "name": "WenheLI"}, {"url": "https://github.com/meixg", "name": "<PERSON><PERSON>ng <PERSON>"}, {"url": "https://github.com/morokosi", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/fs-eire", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ZzqiZQute", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/F3n67u", "name": "<PERSON>"}, {"url": "https://github.com/wanlu", "name": "wanlu wang"}, {"url": "https://github.com/chearon", "name": "<PERSON>"}, {"url": "https://github.com/MarxJiao", "name": "<PERSON>"}, {"url": "https://github.com/tuhalf", "name": "Ömer AKGÜL"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "71f609369379c08e251c558527a107107b5e0fdb", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-7.1.0.tgz", "fileCount": 19, "integrity": "sha512-mNcltoe1R8o7STTegSOHdnJNN7s5EUvhoS7ShnTHDyOSd+8H+UdWODq6qSv67PjC8Zc5JRT8+oLAMCr0SIXw7g==", "signatures": [{"sig": "MEUCIA0OtSxTKEQBE8iO0FUCCRS4E2NqBSSgMgCpIQoI3OHgAiEA3lZ7t/VZJl+x1M/++iLHBPWd3G4eGxkihSuvsOb+jew=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 396366}, "main": "index.js", "engines": {"node": "^16 || ^18 || >= 20"}, "gitHead": "185dda1b2f32a3e9f9aba612bf7cbed3ad7196d4", "gypfile": false, "scripts": {"dev": "node test", "doc": "doxygen doc/Doxyfile", "lint": "node tools/eslint-format && node tools/clang-format", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "pretest": "node-gyp rebuild -C test", "lint:fix": "node tools/clang-format --fix && node tools/eslint-format --fix", "benchmark": "node benchmark", "test:debug": "node-gyp rebuild -C test --debug && NODE_API_BUILD_CONFIG=Debug node ./test/index.js", "prebenchmark": "node-gyp rebuild -C benchmark", "dev:incremental": "node test", "predev:incremental": "node-gyp configure build -C test --debug"}, "support": true, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "pre-commit": "lint", "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Node.js API (Node-API)", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "devDependencies": {"path": "^0.12.7", "eslint": "^7.32.0", "bindings": "^1.5.0", "fs-extra": "^11.1.1", "benchmark": "^2.1.4", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1", "clang-format": "^1.4.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.24.2", "eslint-plugin-promise": "^5.1.0", "eslint-config-standard": "^16.0.3", "eslint-config-semistandard": "^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api_7.1.0_1705653985118_0.47525771598566946", "host": "s3://npm-registry-packages"}}, "8.0.0": {"name": "node-addon-api", "version": "8.0.0", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "_id": "node-addon-api@8.0.0", "maintainers": [{"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "<PERSON>ev<PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/abhi11210646", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jmendeth", "name": "<PERSON>dez"}, {"url": "https://github.com/alexanderfloh", "name": "<PERSON>"}, {"url": "https://github.com/ammarfaizi2", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/timarandras", "name": "<PERSON><PERSON><PERSON>, Dr"}, {"url": "https://github.com/kirbysayshi", "name": "<PERSON>"}, {"url": "https://github.com/anisha-rohra", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/BotellaA", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/azlan", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rivertam", "name": "<PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gallafent", "name": "<PERSON>"}, {"url": "https://github.com/blagoev", "name": "blagoev"}, {"url": "https://github.com/bmacnaughton", "name": "<PERSON>"}, {"url": "https://github.com/corymickelson", "name": "<PERSON>"}, {"url": "https://github.com/danbev", "name": "<PERSON>"}, {"url": "https://github.com/dantehemerson", "name": "<PERSON>"}, {"url": "https://github.com/RaisinTen", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/davedoesdev", "name": "<PERSON>"}, {"url": "https://github.com/deepakrkris", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/dmitryash", "name": "<PERSON>"}, {"url": "https://github.com/nadongguri", "name": "Dongjin Na"}, {"url": "https://github.com/rubiagatra", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ebickle", "name": "<PERSON>"}, {"url": "https://github.com/extremeheat", "name": "extremeheat"}, {"url": "https://github.com/F3n67u", "name": "<PERSON>"}, {"url": "https://github.com/fholzer", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/gms1", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/devsnek", "name": "<PERSON>"}, {"url": "https://github.com/helio-frota", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ikokostya", "name": "ikokostya"}, {"url": "https://github.com/JckXia", "name": "<PERSON>"}, {"url": "https://github.com/DuBistKomisch", "name": "<PERSON>"}, {"url": "https://github.com/yjaeseok", "name": "<PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/egg-bread", "name": "<PERSON>"}, {"url": "https://github.com/japj", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jschlight", "name": "<PERSON>"}, {"url": "https://github.com/romandev", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/JoseExposito", "name": "<PERSON>"}, {"url": "https://github.com/joshgarde", "name": "j<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/julianmesa-gitkraken", "name": "<PERSON>"}, {"url": "https://github.com/hanazuki", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kelvinhammond", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/<PERSON>Eady", "name": "<PERSON>"}, {"url": "https://github.com/kecsou", "name": "Kévin VOYER"}, {"url": "https://github.com/kidneysolo", "name": "kidneysolo"}, {"url": "https://github.com/Nishikoh", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/koistya", "name": "<PERSON>"}, {"url": "https://github.com/kfarnung", "name": "<PERSON>"}, {"url": "https://github.com/nullromo", "name": "<PERSON>"}, {"url": "https://github.com/legendecas", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Brooooooklyn", "name": "LongYinan"}, {"url": "https://github.com/lovell", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/lmartorella", "name": "<PERSON>"}, {"url": "https://github.com/mastergberry", "name": "<PERSON>gberry"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>"}, {"url": "https://github.com/RedBeard0531", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/mikepricedev", "name": "<PERSON>"}, {"url": "https://github.com/kYroL01", "name": "Michele Campus"}, {"url": "https://github.com/mcheshkov", "name": "<PERSON>"}, {"url": "https://github.com/nempoBu4", "name": "nempoBu4"}, {"url": "https://github.com/NickNaso", "name": "<PERSON>"}, {"url": "https://github.com/iSkore", "name": "<PERSON>"}, {"url": "https://github.com/seishun", "name": "<PERSON>"}, {"url": "https://github.com/anurbol", "name": "Nurbol <PERSON>"}, {"url": "https://github.com/pacop", "name": "pacop"}, {"url": "https://github.com/petersandor", "name": "<PERSON>"}, {"url": "https://github.com/DaAitch", "name": "<PERSON>"}, {"url": "https://github.com/rgerd", "name": "rgerd"}, {"url": "https://github.com/richardlau", "name": "<PERSON>"}, {"url": "https://github.com/rolft<PERSON>mans", "name": "<PERSON>"}, {"url": "https://github.com/ross-weir", "name": "<PERSON>"}, {"url": "https://github.com/okuryu", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/chineduG", "name": "<PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/sam-github", "name": "<PERSON>"}, {"url": "https://github.com/strager", "name": "strager"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}, {"url": "https://github.com/fraxken", "name": "<PERSON>"}, {"url": "https://github.com/timrach", "name": "<PERSON>"}, {"url": "https://github.com/tniessen", "name": "<PERSON>"}, {"url": "https://github.com/todoroff", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/toyobayashi", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/tux3", "name": "Tux3"}, {"url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/vmoroz", "name": "<PERSON>"}, {"url": "https://github.com/WenheLI", "name": "WenheLI"}, {"url": "https://github.com/meixg", "name": "<PERSON><PERSON>ng <PERSON>"}, {"url": "https://github.com/morokosi", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/fs-eire", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ZzqiZQute", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/F3n67u", "name": "<PERSON>"}, {"url": "https://github.com/wanlu", "name": "wanlu wang"}, {"url": "https://github.com/chearon", "name": "<PERSON>"}, {"url": "https://github.com/MarxJiao", "name": "<PERSON>"}, {"url": "https://github.com/tuhalf", "name": "Ömer AKGÜL"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "5453b7ad59dd040d12e0f1a97a6fa1c765c5c9d2", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-8.0.0.tgz", "fileCount": 19, "integrity": "sha512-ipO7rsHEBqa9STO5C5T10fj732ml+5kLN1cAG8/jdHd56ldQeGj3Q7+scUS+VHK/qy1zLEwC4wMK5+yM0btPvw==", "signatures": [{"sig": "MEQCIGNFKrMIWHvXgjGAd9zAmgvVh9wM6vMlU9rJ1m6rSSApAiBkvsMgjix15XaejKZ8gVBECfjMAIh4n4FY/wneT0iSfA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 387097}, "main": "index.js", "engines": {"node": "^18 || ^20 || >= 21"}, "gitHead": "d1ba547e91b192152bfc314ab85436de1538b4ec", "gypfile": false, "scripts": {"dev": "node test", "doc": "doxygen doc/Doxyfile", "lint": "node tools/eslint-format && node tools/clang-format", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "pretest": "node-gyp rebuild -C test", "lint:fix": "node tools/clang-format --fix && node tools/eslint-format --fix", "benchmark": "node benchmark", "test:debug": "node-gyp rebuild -C test --debug && NODE_API_BUILD_CONFIG=Debug node ./test/index.js", "prebenchmark": "node-gyp rebuild -C benchmark", "dev:incremental": "node test", "predev:incremental": "node-gyp configure build -C test --debug"}, "support": true, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "pre-commit": "lint", "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Node.js API (Node-API)", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "devDependencies": {"path": "^0.12.7", "eslint": "^7.32.0", "bindings": "^1.5.0", "fs-extra": "^11.1.1", "benchmark": "^2.1.4", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1", "clang-format": "^1.4.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.24.2", "eslint-plugin-promise": "^5.1.0", "eslint-config-standard": "^16.0.3", "eslint-config-semistandard": "^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api_8.0.0_1709622148067_0.23918645999912647", "host": "s3://npm-registry-packages"}}, "8.1.0": {"name": "node-addon-api", "version": "8.1.0", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "_id": "node-addon-api@8.1.0", "maintainers": [{"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "<PERSON>ev<PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/abhi11210646", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jmendeth", "name": "<PERSON>dez"}, {"url": "https://github.com/alexanderfloh", "name": "<PERSON>"}, {"url": "https://github.com/ammarfaizi2", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/timarandras", "name": "<PERSON><PERSON><PERSON>, Dr"}, {"url": "https://github.com/kirbysayshi", "name": "<PERSON>"}, {"url": "https://github.com/anisha-rohra", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/BotellaA", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/azlan", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rivertam", "name": "<PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gallafent", "name": "<PERSON>"}, {"url": "https://github.com/blagoev", "name": "blagoev"}, {"url": "https://github.com/bmacnaughton", "name": "<PERSON>"}, {"url": "https://github.com/corymickelson", "name": "<PERSON>"}, {"url": "https://github.com/danbev", "name": "<PERSON>"}, {"url": "https://github.com/dantehemerson", "name": "<PERSON>"}, {"url": "https://github.com/RaisinTen", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/davedoesdev", "name": "<PERSON>"}, {"url": "https://github.com/deepakrkris", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/dmitryash", "name": "<PERSON>"}, {"url": "https://github.com/nadongguri", "name": "Dongjin Na"}, {"url": "https://github.com/rubiagatra", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ebickle", "name": "<PERSON>"}, {"url": "https://github.com/extremeheat", "name": "extremeheat"}, {"url": "https://github.com/F3n67u", "name": "<PERSON>"}, {"url": "https://github.com/fholzer", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/gms1", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/devsnek", "name": "<PERSON>"}, {"url": "https://github.com/helio-frota", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ikokostya", "name": "ikokostya"}, {"url": "https://github.com/JckXia", "name": "<PERSON>"}, {"url": "https://github.com/DuBistKomisch", "name": "<PERSON>"}, {"url": "https://github.com/yjaeseok", "name": "<PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/egg-bread", "name": "<PERSON>"}, {"url": "https://github.com/japj", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jschlight", "name": "<PERSON>"}, {"url": "https://github.com/romandev", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/JoseExposito", "name": "<PERSON>"}, {"url": "https://github.com/joshgarde", "name": "j<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/julianmesa-gitkraken", "name": "<PERSON>"}, {"url": "https://github.com/hanazuki", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kelvinhammond", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/<PERSON>Eady", "name": "<PERSON>"}, {"url": "https://github.com/kecsou", "name": "Kévin VOYER"}, {"url": "https://github.com/kidneysolo", "name": "kidneysolo"}, {"url": "https://github.com/Nishikoh", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/koistya", "name": "<PERSON>"}, {"url": "https://github.com/kfarnung", "name": "<PERSON>"}, {"url": "https://github.com/nullromo", "name": "<PERSON>"}, {"url": "https://github.com/legendecas", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Brooooooklyn", "name": "LongYinan"}, {"url": "https://github.com/lovell", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/lmartorella", "name": "<PERSON>"}, {"url": "https://github.com/mastergberry", "name": "<PERSON>gberry"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>"}, {"url": "https://github.com/RedBeard0531", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/mikepricedev", "name": "<PERSON>"}, {"url": "https://github.com/kYroL01", "name": "Michele Campus"}, {"url": "https://github.com/mcheshkov", "name": "<PERSON>"}, {"url": "https://github.com/nempoBu4", "name": "nempoBu4"}, {"url": "https://github.com/NickNaso", "name": "<PERSON>"}, {"url": "https://github.com/iSkore", "name": "<PERSON>"}, {"url": "https://github.com/seishun", "name": "<PERSON>"}, {"url": "https://github.com/anurbol", "name": "Nurbol <PERSON>"}, {"url": "https://github.com/pacop", "name": "pacop"}, {"url": "https://github.com/petersandor", "name": "<PERSON>"}, {"url": "https://github.com/DaAitch", "name": "<PERSON>"}, {"url": "https://github.com/rgerd", "name": "rgerd"}, {"url": "https://github.com/richardlau", "name": "<PERSON>"}, {"url": "https://github.com/rolft<PERSON>mans", "name": "<PERSON>"}, {"url": "https://github.com/ross-weir", "name": "<PERSON>"}, {"url": "https://github.com/okuryu", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/chineduG", "name": "<PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/sam-github", "name": "<PERSON>"}, {"url": "https://github.com/strager", "name": "strager"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}, {"url": "https://github.com/fraxken", "name": "<PERSON>"}, {"url": "https://github.com/timrach", "name": "<PERSON>"}, {"url": "https://github.com/tniessen", "name": "<PERSON>"}, {"url": "https://github.com/todoroff", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/toyobayashi", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/tux3", "name": "Tux3"}, {"url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/vmoroz", "name": "<PERSON>"}, {"url": "https://github.com/WenheLI", "name": "WenheLI"}, {"url": "https://github.com/meixg", "name": "<PERSON><PERSON>ng <PERSON>"}, {"url": "https://github.com/morokosi", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/fs-eire", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ZzqiZQute", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/F3n67u", "name": "<PERSON>"}, {"url": "https://github.com/wanlu", "name": "wanlu wang"}, {"url": "https://github.com/chearon", "name": "<PERSON>"}, {"url": "https://github.com/MarxJiao", "name": "<PERSON>"}, {"url": "https://github.com/tuhalf", "name": "Ömer AKGÜL"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "55a573685dd4bd053f189cffa4e6332d2b1f1645", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-8.1.0.tgz", "fileCount": 19, "integrity": "sha512-yBY+qqWSv3dWKGODD6OGE6GnTX7Q2r+4+DfpqxHSHh8x0B4EKP9+wVGLS6U/AM1vxSNNmUEuIV5EGhYwPpfOwQ==", "signatures": [{"sig": "MEUCIQC7o+YSYz7Nj0bFdstH1ALmtUb9NYLQ1x/TyggYiwbMEQIgVQlk21YbROhD7cM0l7ufFIy0e07LiRyDO1I6XsqOA7A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 391869}, "main": "index.js", "engines": {"node": "^18 || ^20 || >= 21"}, "gitHead": "bc5acef9dd5298cbbcabd5c01c9590ada683951d", "gypfile": false, "scripts": {"dev": "node test", "doc": "doxygen doc/Doxyfile", "lint": "node tools/eslint-format && node tools/clang-format", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "pretest": "node-gyp rebuild -C test", "lint:fix": "node tools/clang-format --fix && node tools/eslint-format --fix", "benchmark": "node benchmark", "test:debug": "node-gyp rebuild -C test --debug && NODE_API_BUILD_CONFIG=Debug node ./test/index.js", "prebenchmark": "node-gyp rebuild -C benchmark", "create-coverage": "npm test --coverage", "dev:incremental": "node test", "predev:incremental": "node-gyp configure build -C test --debug", "report-coverage-xml": "rm -rf coverage-xml && mkdir coverage-xml && gcovr -e test --merge-mode-functions merge-use-line-max --xml -o ./coverage-xml/coverage-cxx.xml test", "report-coverage-html": "rm -rf coverage-html && mkdir coverage-html && gcovr -e test --merge-mode-functions merge-use-line-max --html-nested ./coverage-html/index.html test"}, "support": true, "_npmUser": {"name": "nodejs-foundation", "email": "<EMAIL>"}, "pre-commit": "lint", "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Node.js API (Node-API)", "directories": {}, "_nodeVersion": "20.15.0", "_hasShrinkwrap": false, "devDependencies": {"path": "^0.12.7", "eslint": "^7.32.0", "semver": "^7.6.0", "bindings": "^1.5.0", "fs-extra": "^11.1.1", "benchmark": "^2.1.4", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1", "clang-format": "^1.4.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.24.2", "eslint-plugin-promise": "^5.1.0", "eslint-config-standard": "^16.0.3", "eslint-config-semistandard": "^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api_8.1.0_1720429074135_0.5276460310288342", "host": "s3://npm-registry-packages"}}, "7.1.1": {"name": "node-addon-api", "version": "7.1.1", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "_id": "node-addon-api@7.1.1", "maintainers": [{"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "<PERSON>ev<PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/abhi11210646", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jmendeth", "name": "<PERSON>dez"}, {"url": "https://github.com/alexanderfloh", "name": "<PERSON>"}, {"url": "https://github.com/ammarfaizi2", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/timarandras", "name": "<PERSON><PERSON><PERSON>, Dr"}, {"url": "https://github.com/kirbysayshi", "name": "<PERSON>"}, {"url": "https://github.com/anisha-rohra", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/BotellaA", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/azlan", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rivertam", "name": "<PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gallafent", "name": "<PERSON>"}, {"url": "https://github.com/blagoev", "name": "blagoev"}, {"url": "https://github.com/bmacnaughton", "name": "<PERSON>"}, {"url": "https://github.com/corymickelson", "name": "<PERSON>"}, {"url": "https://github.com/danbev", "name": "<PERSON>"}, {"url": "https://github.com/dantehemerson", "name": "<PERSON>"}, {"url": "https://github.com/RaisinTen", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/davedoesdev", "name": "<PERSON>"}, {"url": "https://github.com/deepakrkris", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/dmitryash", "name": "<PERSON>"}, {"url": "https://github.com/nadongguri", "name": "Dongjin Na"}, {"url": "https://github.com/rubiagatra", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ebickle", "name": "<PERSON>"}, {"url": "https://github.com/extremeheat", "name": "extremeheat"}, {"url": "https://github.com/F3n67u", "name": "<PERSON>"}, {"url": "https://github.com/fholzer", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/gms1", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/devsnek", "name": "<PERSON>"}, {"url": "https://github.com/helio-frota", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ikokostya", "name": "ikokostya"}, {"url": "https://github.com/JckXia", "name": "<PERSON>"}, {"url": "https://github.com/DuBistKomisch", "name": "<PERSON>"}, {"url": "https://github.com/yjaeseok", "name": "<PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/egg-bread", "name": "<PERSON>"}, {"url": "https://github.com/japj", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jschlight", "name": "<PERSON>"}, {"url": "https://github.com/romandev", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/JoseExposito", "name": "<PERSON>"}, {"url": "https://github.com/joshgarde", "name": "j<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/julianmesa-gitkraken", "name": "<PERSON>"}, {"url": "https://github.com/hanazuki", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kelvinhammond", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/<PERSON>Eady", "name": "<PERSON>"}, {"url": "https://github.com/kecsou", "name": "Kévin VOYER"}, {"url": "https://github.com/kidneysolo", "name": "kidneysolo"}, {"url": "https://github.com/Nishikoh", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/koistya", "name": "<PERSON>"}, {"url": "https://github.com/kfarnung", "name": "<PERSON>"}, {"url": "https://github.com/nullromo", "name": "<PERSON>"}, {"url": "https://github.com/legendecas", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Brooooooklyn", "name": "LongYinan"}, {"url": "https://github.com/lovell", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/lmartorella", "name": "<PERSON>"}, {"url": "https://github.com/mastergberry", "name": "<PERSON>gberry"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>"}, {"url": "https://github.com/RedBeard0531", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/mikepricedev", "name": "<PERSON>"}, {"url": "https://github.com/kYroL01", "name": "Michele Campus"}, {"url": "https://github.com/mcheshkov", "name": "<PERSON>"}, {"url": "https://github.com/nempoBu4", "name": "nempoBu4"}, {"url": "https://github.com/NickNaso", "name": "<PERSON>"}, {"url": "https://github.com/iSkore", "name": "<PERSON>"}, {"url": "https://github.com/seishun", "name": "<PERSON>"}, {"url": "https://github.com/anurbol", "name": "Nurbol <PERSON>"}, {"url": "https://github.com/pacop", "name": "pacop"}, {"url": "https://github.com/petersandor", "name": "<PERSON>"}, {"url": "https://github.com/DaAitch", "name": "<PERSON>"}, {"url": "https://github.com/rgerd", "name": "rgerd"}, {"url": "https://github.com/richardlau", "name": "<PERSON>"}, {"url": "https://github.com/rolft<PERSON>mans", "name": "<PERSON>"}, {"url": "https://github.com/ross-weir", "name": "<PERSON>"}, {"url": "https://github.com/okuryu", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/chineduG", "name": "<PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/sam-github", "name": "<PERSON>"}, {"url": "https://github.com/strager", "name": "strager"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}, {"url": "https://github.com/fraxken", "name": "<PERSON>"}, {"url": "https://github.com/timrach", "name": "<PERSON>"}, {"url": "https://github.com/tniessen", "name": "<PERSON>"}, {"url": "https://github.com/todoroff", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/toyobayashi", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/tux3", "name": "Tux3"}, {"url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/vmoroz", "name": "<PERSON>"}, {"url": "https://github.com/WenheLI", "name": "WenheLI"}, {"url": "https://github.com/meixg", "name": "<PERSON><PERSON>ng <PERSON>"}, {"url": "https://github.com/morokosi", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/fs-eire", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ZzqiZQute", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/F3n67u", "name": "<PERSON>"}, {"url": "https://github.com/wanlu", "name": "wanlu wang"}, {"url": "https://github.com/chearon", "name": "<PERSON>"}, {"url": "https://github.com/MarxJiao", "name": "<PERSON>"}, {"url": "https://github.com/tuhalf", "name": "Ömer AKGÜL"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "1aba6693b0f255258a049d621329329322aad558", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-7.1.1.tgz", "fileCount": 19, "integrity": "sha512-5m3bsyrjFWE1xf7nz7YXdN4udnVtXK6/Yfgn5qnahL6bCkf2yKt4k3nuTKAtT4r3IG8JNR2ncsIMdZuAzJjHQQ==", "signatures": [{"sig": "MEUCIEXMQqSmOJMzuSxQGyLOE1l0PNqOV5eFnpxIY/DmOnkaAiEA+B6rRlodLNAxw3pcP+UXGdVZrKMHZnizga+70Z2z28I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 396312}, "main": "index.js", "gitHead": "5e96a5460f2538a06f87e592d6aa349a7f08b04a", "gypfile": false, "scripts": {"dev": "node test", "doc": "doxygen doc/Doxyfile", "lint": "node tools/eslint-format && node tools/clang-format", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "pretest": "node-gyp rebuild -C test", "lint:fix": "node tools/clang-format --fix && node tools/eslint-format --fix", "benchmark": "node benchmark", "test:debug": "node-gyp rebuild -C test --debug && NODE_API_BUILD_CONFIG=Debug node ./test/index.js", "prebenchmark": "node-gyp rebuild -C benchmark", "dev:incremental": "node test", "predev:incremental": "node-gyp configure build -C test --debug"}, "support": true, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "pre-commit": "lint", "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "10.3.0", "description": "Node.js API (Node-API)", "directories": {}, "_nodeVersion": "22.2.0", "_hasShrinkwrap": false, "devDependencies": {"path": "^0.12.7", "eslint": "^7.32.0", "bindings": "^1.5.0", "fs-extra": "^11.1.1", "benchmark": "^2.1.4", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1", "clang-format": "^1.4.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.24.2", "eslint-plugin-promise": "^5.1.0", "eslint-config-standard": "^16.0.3", "eslint-config-semistandard": "^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api_7.1.1_1720779307443_0.7401679488879174", "host": "s3://npm-registry-packages"}}, "8.2.0": {"name": "node-addon-api", "version": "8.2.0", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "_id": "node-addon-api@8.2.0", "maintainers": [{"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "<PERSON>ev<PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/abhi11210646", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jmendeth", "name": "<PERSON>dez"}, {"url": "https://github.com/alexanderfloh", "name": "<PERSON>"}, {"url": "https://github.com/ammarfaizi2", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/timarandras", "name": "<PERSON><PERSON><PERSON>, Dr"}, {"url": "https://github.com/kirbysayshi", "name": "<PERSON>"}, {"url": "https://github.com/anisha-rohra", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/BotellaA", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/azlan", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rivertam", "name": "<PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gallafent", "name": "<PERSON>"}, {"url": "https://github.com/blagoev", "name": "blagoev"}, {"url": "https://github.com/bmacnaughton", "name": "<PERSON>"}, {"url": "https://github.com/corymickelson", "name": "<PERSON>"}, {"url": "https://github.com/danbev", "name": "<PERSON>"}, {"url": "https://github.com/dantehemerson", "name": "<PERSON>"}, {"url": "https://github.com/RaisinTen", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/davedoesdev", "name": "<PERSON>"}, {"url": "https://github.com/deepakrkris", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/dmitryash", "name": "<PERSON>"}, {"url": "https://github.com/nadongguri", "name": "Dongjin Na"}, {"url": "https://github.com/rubiagatra", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ebickle", "name": "<PERSON>"}, {"url": "https://github.com/extremeheat", "name": "extremeheat"}, {"url": "https://github.com/F3n67u", "name": "<PERSON>"}, {"url": "https://github.com/fholzer", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/gms1", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/devsnek", "name": "<PERSON>"}, {"url": "https://github.com/helio-frota", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ikokostya", "name": "ikokostya"}, {"url": "https://github.com/JckXia", "name": "<PERSON>"}, {"url": "https://github.com/DuBistKomisch", "name": "<PERSON>"}, {"url": "https://github.com/yjaeseok", "name": "<PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/egg-bread", "name": "<PERSON>"}, {"url": "https://github.com/japj", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jschlight", "name": "<PERSON>"}, {"url": "https://github.com/romandev", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/JoseExposito", "name": "<PERSON>"}, {"url": "https://github.com/joshgarde", "name": "j<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/julianmesa-gitkraken", "name": "<PERSON>"}, {"url": "https://github.com/hanazuki", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kelvinhammond", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/<PERSON>Eady", "name": "<PERSON>"}, {"url": "https://github.com/kecsou", "name": "Kévin VOYER"}, {"url": "https://github.com/kidneysolo", "name": "kidneysolo"}, {"url": "https://github.com/Nishikoh", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/koistya", "name": "<PERSON>"}, {"url": "https://github.com/kfarnung", "name": "<PERSON>"}, {"url": "https://github.com/nullromo", "name": "<PERSON>"}, {"url": "https://github.com/legendecas", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Brooooooklyn", "name": "LongYinan"}, {"url": "https://github.com/lovell", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/lmartorella", "name": "<PERSON>"}, {"url": "https://github.com/mastergberry", "name": "<PERSON>gberry"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>"}, {"url": "https://github.com/RedBeard0531", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/mikepricedev", "name": "<PERSON>"}, {"url": "https://github.com/kYroL01", "name": "Michele Campus"}, {"url": "https://github.com/mcheshkov", "name": "<PERSON>"}, {"url": "https://github.com/nempoBu4", "name": "nempoBu4"}, {"url": "https://github.com/NickNaso", "name": "<PERSON>"}, {"url": "https://github.com/iSkore", "name": "<PERSON>"}, {"url": "https://github.com/seishun", "name": "<PERSON>"}, {"url": "https://github.com/anurbol", "name": "Nurbol <PERSON>"}, {"url": "https://github.com/pacop", "name": "pacop"}, {"url": "https://github.com/petersandor", "name": "<PERSON>"}, {"url": "https://github.com/DaAitch", "name": "<PERSON>"}, {"url": "https://github.com/rgerd", "name": "rgerd"}, {"url": "https://github.com/richardlau", "name": "<PERSON>"}, {"url": "https://github.com/rolft<PERSON>mans", "name": "<PERSON>"}, {"url": "https://github.com/ross-weir", "name": "<PERSON>"}, {"url": "https://github.com/okuryu", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/chineduG", "name": "<PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/sam-github", "name": "<PERSON>"}, {"url": "https://github.com/strager", "name": "strager"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}, {"url": "https://github.com/fraxken", "name": "<PERSON>"}, {"url": "https://github.com/timrach", "name": "<PERSON>"}, {"url": "https://github.com/tniessen", "name": "<PERSON>"}, {"url": "https://github.com/todoroff", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/toyobayashi", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/tux3", "name": "Tux3"}, {"url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/vmoroz", "name": "<PERSON>"}, {"url": "https://github.com/WenheLI", "name": "WenheLI"}, {"url": "https://github.com/meixg", "name": "<PERSON><PERSON>ng <PERSON>"}, {"url": "https://github.com/morokosi", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/fs-eire", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ZzqiZQute", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/F3n67u", "name": "<PERSON>"}, {"url": "https://github.com/wanlu", "name": "wanlu wang"}, {"url": "https://github.com/chearon", "name": "<PERSON>"}, {"url": "https://github.com/MarxJiao", "name": "<PERSON>"}, {"url": "https://github.com/tuhalf", "name": "Ömer AKGÜL"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "ad92cacecc86834304053fd0089f718b72ff4e65", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-8.2.0.tgz", "fileCount": 19, "integrity": "sha512-qnyuI2ROiCkye42n9Tj5aX1ns7rzj6n7zW1XReSnLSL9v/vbLeR6fJq6PU27YU/ICfYw6W7Ouk/N7cysWu/hlw==", "signatures": [{"sig": "MEQCIBUr5oFs9TT/ieYjOqqDAdSRA4pLrzz6C2uoHYpnvbeNAiBI3bhqf/yfe2KyzUpqPHQM3dsO1kNVuH37gCyesrHRlw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 400952}, "main": "index.js", "engines": {"node": "^18 || ^20 || >= 21"}, "gitHead": "5b1a57d9e3c8b1c496c63bf31514f7ce4a7c1a8b", "gypfile": false, "scripts": {"dev": "node test", "doc": "doxygen doc/Doxyfile", "lint": "node tools/eslint-format && node tools/clang-format", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "pretest": "node-gyp rebuild -C test", "lint:fix": "node tools/clang-format --fix && node tools/eslint-format --fix", "benchmark": "node benchmark", "test:debug": "node-gyp rebuild -C test --debug && NODE_API_BUILD_CONFIG=Debug node ./test/index.js", "prebenchmark": "node-gyp rebuild -C benchmark", "create-coverage": "npm test --coverage", "dev:incremental": "node test", "predev:incremental": "node-gyp configure build -C test --debug", "report-coverage-xml": "rm -rf coverage-xml && mkdir coverage-xml && gcovr -e test --merge-mode-functions merge-use-line-max --xml -o ./coverage-xml/coverage-cxx.xml test", "report-coverage-html": "rm -rf coverage-html && mkdir coverage-html && gcovr -e test --merge-mode-functions merge-use-line-max --html-nested ./coverage-html/index.html test"}, "support": true, "_npmUser": {"name": "nodejs-foundation", "email": "<EMAIL>"}, "pre-commit": "lint", "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Node.js API (Node-API)", "directories": {}, "_nodeVersion": "20.17.0", "_hasShrinkwrap": false, "devDependencies": {"path": "^0.12.7", "eslint": "^7.32.0", "semver": "^7.6.0", "bindings": "^1.5.0", "fs-extra": "^11.1.1", "benchmark": "^2.1.4", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1", "clang-format": "^1.4.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.24.2", "eslint-plugin-promise": "^5.1.0", "eslint-config-standard": "^16.0.3", "eslint-config-semistandard": "^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api_8.2.0_1727706616679_0.8751262390896011", "host": "s3://npm-registry-packages"}}, "8.2.1": {"name": "node-addon-api", "version": "8.2.1", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "_id": "node-addon-api@8.2.1", "maintainers": [{"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "<PERSON>ev<PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/abhi11210646", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jmendeth", "name": "<PERSON>dez"}, {"url": "https://github.com/alexanderfloh", "name": "<PERSON>"}, {"url": "https://github.com/ammarfaizi2", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/timarandras", "name": "<PERSON><PERSON><PERSON>, Dr"}, {"url": "https://github.com/kirbysayshi", "name": "<PERSON>"}, {"url": "https://github.com/anisha-rohra", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/BotellaA", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/azlan", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rivertam", "name": "<PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gallafent", "name": "<PERSON>"}, {"url": "https://github.com/blagoev", "name": "blagoev"}, {"url": "https://github.com/bmacnaughton", "name": "<PERSON>"}, {"url": "https://github.com/corymickelson", "name": "<PERSON>"}, {"url": "https://github.com/danbev", "name": "<PERSON>"}, {"url": "https://github.com/dantehemerson", "name": "<PERSON>"}, {"url": "https://github.com/RaisinTen", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/davedoesdev", "name": "<PERSON>"}, {"url": "https://github.com/deepakrkris", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/dmitryash", "name": "<PERSON>"}, {"url": "https://github.com/nadongguri", "name": "Dongjin Na"}, {"url": "https://github.com/rubiagatra", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ebickle", "name": "<PERSON>"}, {"url": "https://github.com/extremeheat", "name": "extremeheat"}, {"url": "https://github.com/F3n67u", "name": "<PERSON>"}, {"url": "https://github.com/fholzer", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/gms1", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/devsnek", "name": "<PERSON>"}, {"url": "https://github.com/helio-frota", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ikokostya", "name": "ikokostya"}, {"url": "https://github.com/JckXia", "name": "<PERSON>"}, {"url": "https://github.com/DuBistKomisch", "name": "<PERSON>"}, {"url": "https://github.com/yjaeseok", "name": "<PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/egg-bread", "name": "<PERSON>"}, {"url": "https://github.com/japj", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jschlight", "name": "<PERSON>"}, {"url": "https://github.com/romandev", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/JoseExposito", "name": "<PERSON>"}, {"url": "https://github.com/joshgarde", "name": "j<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/julianmesa-gitkraken", "name": "<PERSON>"}, {"url": "https://github.com/hanazuki", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kelvinhammond", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/<PERSON>Eady", "name": "<PERSON>"}, {"url": "https://github.com/kecsou", "name": "Kévin VOYER"}, {"url": "https://github.com/kidneysolo", "name": "kidneysolo"}, {"url": "https://github.com/Nishikoh", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/koistya", "name": "<PERSON>"}, {"url": "https://github.com/kfarnung", "name": "<PERSON>"}, {"url": "https://github.com/nullromo", "name": "<PERSON>"}, {"url": "https://github.com/legendecas", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Brooooooklyn", "name": "LongYinan"}, {"url": "https://github.com/lovell", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/lmartorella", "name": "<PERSON>"}, {"url": "https://github.com/mastergberry", "name": "<PERSON>gberry"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>"}, {"url": "https://github.com/RedBeard0531", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/mikepricedev", "name": "<PERSON>"}, {"url": "https://github.com/kYroL01", "name": "Michele Campus"}, {"url": "https://github.com/mcheshkov", "name": "<PERSON>"}, {"url": "https://github.com/nempoBu4", "name": "nempoBu4"}, {"url": "https://github.com/NickNaso", "name": "<PERSON>"}, {"url": "https://github.com/iSkore", "name": "<PERSON>"}, {"url": "https://github.com/seishun", "name": "<PERSON>"}, {"url": "https://github.com/anurbol", "name": "Nurbol <PERSON>"}, {"url": "https://github.com/pacop", "name": "pacop"}, {"url": "https://github.com/petersandor", "name": "<PERSON>"}, {"url": "https://github.com/DaAitch", "name": "<PERSON>"}, {"url": "https://github.com/rgerd", "name": "rgerd"}, {"url": "https://github.com/richardlau", "name": "<PERSON>"}, {"url": "https://github.com/rolft<PERSON>mans", "name": "<PERSON>"}, {"url": "https://github.com/ross-weir", "name": "<PERSON>"}, {"url": "https://github.com/okuryu", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/chineduG", "name": "<PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/sam-github", "name": "<PERSON>"}, {"url": "https://github.com/strager", "name": "strager"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}, {"url": "https://github.com/fraxken", "name": "<PERSON>"}, {"url": "https://github.com/timrach", "name": "<PERSON>"}, {"url": "https://github.com/tniessen", "name": "<PERSON>"}, {"url": "https://github.com/todoroff", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/toyobayashi", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/tux3", "name": "Tux3"}, {"url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/vmoroz", "name": "<PERSON>"}, {"url": "https://github.com/WenheLI", "name": "WenheLI"}, {"url": "https://github.com/meixg", "name": "<PERSON><PERSON>ng <PERSON>"}, {"url": "https://github.com/morokosi", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/fs-eire", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ZzqiZQute", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/F3n67u", "name": "<PERSON>"}, {"url": "https://github.com/wanlu", "name": "wanlu wang"}, {"url": "https://github.com/chearon", "name": "<PERSON>"}, {"url": "https://github.com/MarxJiao", "name": "<PERSON>"}, {"url": "https://github.com/tuhalf", "name": "Ömer AKGÜL"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "43a993f110b88e22ba48bcd65e16b92165a6b002", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-8.2.1.tgz", "fileCount": 19, "integrity": "sha512-vmEOvxwiH8tlOcv4SyE8RH34rI5/nWVaigUeAUPawC6f0+HoDthwI0vkMu4tbtsZrXq6QXFfrkhjofzKEs5tpA==", "signatures": [{"sig": "MEYCIQCfvwrjh9pz8BZLCZ8XCWeM0THiQGS1DtQkgZKsi+/23QIhALB8lg9ngWe0dzFauiSnqh60pOi5+iA1gH1ntphzizfx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 402038}, "main": "index.js", "engines": {"node": "^18 || ^20 || >= 21"}, "gitHead": "49652bd698a1285a857c3d8a675c31c4c016dd30", "gypfile": false, "scripts": {"dev": "node test", "doc": "doxygen doc/Doxyfile", "lint": "node tools/eslint-format && node tools/clang-format", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "pretest": "node-gyp rebuild -C test", "lint:fix": "node tools/clang-format --fix && node tools/eslint-format --fix", "benchmark": "node benchmark", "test:debug": "node-gyp rebuild -C test --debug && NODE_API_BUILD_CONFIG=Debug node ./test/index.js", "prebenchmark": "node-gyp rebuild -C benchmark", "create-coverage": "npm test --coverage", "dev:incremental": "node test", "predev:incremental": "node-gyp configure build -C test --debug", "report-coverage-xml": "rm -rf coverage-xml && mkdir coverage-xml && gcovr -e test --merge-mode-functions merge-use-line-max --xml -o ./coverage-xml/coverage-cxx.xml test", "report-coverage-html": "rm -rf coverage-html && mkdir coverage-html && gcovr -e test --merge-mode-functions merge-use-line-max --html-nested ./coverage-html/index.html test"}, "support": true, "_npmUser": {"name": "nodejs-foundation", "email": "<EMAIL>"}, "pre-commit": "lint", "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Node.js API (Node-API)", "directories": {}, "_nodeVersion": "20.17.0", "_hasShrinkwrap": false, "devDependencies": {"path": "^0.12.7", "eslint": "^7.32.0", "semver": "^7.6.0", "bindings": "^1.5.0", "fs-extra": "^11.1.1", "benchmark": "^2.1.4", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1", "clang-format": "^1.4.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.24.2", "eslint-plugin-promise": "^5.1.0", "eslint-config-standard": "^16.0.3", "eslint-config-semistandard": "^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api_8.2.1_1728659243786_0.42222731566238214", "host": "s3://npm-registry-packages"}}, "8.2.2": {"name": "node-addon-api", "version": "8.2.2", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "_id": "node-addon-api@8.2.2", "maintainers": [{"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "<PERSON>ev<PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/abhi11210646", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jmendeth", "name": "<PERSON>dez"}, {"url": "https://github.com/alexanderfloh", "name": "<PERSON>"}, {"url": "https://github.com/ammarfaizi2", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/timarandras", "name": "<PERSON><PERSON><PERSON>, Dr"}, {"url": "https://github.com/kirbysayshi", "name": "<PERSON>"}, {"url": "https://github.com/anisha-rohra", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/addaleax", "name": "<PERSON>"}, {"url": "https://github.com/BotellaA", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/azlan", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/rivertam", "name": "<PERSON>"}, {"url": "https://github.com/kkoopa", "name": "<PERSON>"}, {"url": "https://github.com/gallafent", "name": "<PERSON>"}, {"url": "https://github.com/blagoev", "name": "blagoev"}, {"url": "https://github.com/bmacnaughton", "name": "<PERSON>"}, {"url": "https://github.com/corymickelson", "name": "<PERSON>"}, {"url": "https://github.com/danbev", "name": "<PERSON>"}, {"url": "https://github.com/dantehemerson", "name": "<PERSON>"}, {"url": "https://github.com/RaisinTen", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/davedoesdev", "name": "<PERSON>"}, {"url": "https://github.com/deepakrkris", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/dmitryash", "name": "<PERSON>"}, {"url": "https://github.com/nadongguri", "name": "Dongjin Na"}, {"url": "https://github.com/rubiagatra", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ebickle", "name": "<PERSON>"}, {"url": "https://github.com/extremeheat", "name": "extremeheat"}, {"url": "https://github.com/F3n67u", "name": "<PERSON>"}, {"url": "https://github.com/fholzer", "name": "<PERSON>"}, {"url": "https://github.com/gabrielschulhof", "name": "<PERSON>"}, {"url": "https://github.com/gms1", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/devsnek", "name": "<PERSON>"}, {"url": "https://github.com/helio-frota", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/digitalinfinity", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ikokostya", "name": "ikokostya"}, {"url": "https://github.com/JckXia", "name": "<PERSON>"}, {"url": "https://github.com/DuBistKomisch", "name": "<PERSON>"}, {"url": "https://github.com/yjaeseok", "name": "<PERSON>"}, {"url": "https://github.com/jasongin", "name": "<PERSON>"}, {"url": "https://github.com/egg-bread", "name": "<PERSON>"}, {"url": "https://github.com/japj", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jschlight", "name": "<PERSON>"}, {"url": "https://github.com/romandev", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/JoseExposito", "name": "<PERSON>"}, {"url": "https://github.com/joshgarde", "name": "j<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/julianmesa-gitkraken", "name": "<PERSON>"}, {"url": "https://github.com/hanazuki", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kelvinhammond", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/<PERSON>Eady", "name": "<PERSON>"}, {"url": "https://github.com/kecsou", "name": "Kévin VOYER"}, {"url": "https://github.com/kidneysolo", "name": "kidneysolo"}, {"url": "https://github.com/Nishikoh", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/koistya", "name": "<PERSON>"}, {"url": "https://github.com/kfarnung", "name": "<PERSON>"}, {"url": "https://github.com/nullromo", "name": "<PERSON>"}, {"url": "https://github.com/legendecas", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Brooooooklyn", "name": "LongYinan"}, {"url": "https://github.com/lovell", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/lmartorella", "name": "<PERSON>"}, {"url": "https://github.com/mastergberry", "name": "<PERSON>gberry"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>"}, {"url": "https://github.com/RedBeard0531", "name": "<PERSON>"}, {"url": "https://github.com/mcollina", "name": "<PERSON>"}, {"url": "https://github.com/mhdawson", "name": "<PERSON>"}, {"url": "https://github.com/mikepricedev", "name": "<PERSON>"}, {"url": "https://github.com/kYroL01", "name": "Michele Campus"}, {"url": "https://github.com/mcheshkov", "name": "<PERSON>"}, {"url": "https://github.com/nempoBu4", "name": "nempoBu4"}, {"url": "https://github.com/NickNaso", "name": "<PERSON>"}, {"url": "https://github.com/iSkore", "name": "<PERSON>"}, {"url": "https://github.com/seishun", "name": "<PERSON>"}, {"url": "https://github.com/anurbol", "name": "Nurbol <PERSON>"}, {"url": "https://github.com/pacop", "name": "pacop"}, {"url": "https://github.com/petersandor", "name": "<PERSON>"}, {"url": "https://github.com/DaAitch", "name": "<PERSON>"}, {"url": "https://github.com/rgerd", "name": "rgerd"}, {"url": "https://github.com/richardlau", "name": "<PERSON>"}, {"url": "https://github.com/rolft<PERSON>mans", "name": "<PERSON>"}, {"url": "https://github.com/ross-weir", "name": "<PERSON>"}, {"url": "https://github.com/okuryu", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/chineduG", "name": "<PERSON>"}, {"url": "https://github.com/sampsongao", "name": "<PERSON>"}, {"url": "https://github.com/sam-github", "name": "<PERSON>"}, {"url": "https://github.com/strager", "name": "strager"}, {"url": "https://github.com/boingoing", "name": "<PERSON>"}, {"url": "https://github.com/fraxken", "name": "<PERSON>"}, {"url": "https://github.com/timrach", "name": "<PERSON>"}, {"url": "https://github.com/tniessen", "name": "<PERSON>"}, {"url": "https://github.com/todoroff", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/toyobayashi", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/tux3", "name": "Tux3"}, {"url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/vmoroz", "name": "<PERSON>"}, {"url": "https://github.com/WenheLI", "name": "WenheLI"}, {"url": "https://github.com/meixg", "name": "<PERSON><PERSON>ng <PERSON>"}, {"url": "https://github.com/morokosi", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/fs-eire", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/ZzqiZQute", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/F3n67u", "name": "<PERSON>"}, {"url": "https://github.com/wanlu", "name": "wanlu wang"}, {"url": "https://github.com/chearon", "name": "<PERSON>"}, {"url": "https://github.com/MarxJiao", "name": "<PERSON>"}, {"url": "https://github.com/tuhalf", "name": "Ömer AKGÜL"}], "homepage": "https://github.com/nodejs/node-addon-api", "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "dist": {"shasum": "3658f78d04d260aa95931d3bbc45f22ce433b821", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-8.2.2.tgz", "fileCount": 19, "integrity": "sha512-9emqXAKhVoNrQ792nLI/wpzPpJ/bj/YXxW0CvAau1+RdGBcCRF1Dmz7719zgVsQNrzHl9Tzn3ImZ4qWFarWL0A==", "signatures": [{"sig": "MEUCIQCZtkwrazen7HDzwEReYhJ61TGdX3CnR7vQqYkNx0+HwgIgK1Cm8jUOcTjKFDYuffVmVg8U011TKb9g7oA9sjK5cHA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/node-addon-api@8.2.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 402588}, "main": "index.js", "engines": {"node": "^18 || ^20 || >= 21"}, "gitHead": "264bc1b94fea7c05fcbed3eac47b205c61b199ac", "gypfile": false, "scripts": {"dev": "node test", "doc": "doxygen doc/Doxyfile", "lint": "node tools/eslint-format && node tools/clang-format", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "pretest": "node-gyp rebuild -C test", "lint:fix": "node tools/clang-format --fix && node tools/eslint-format --fix", "benchmark": "node benchmark", "test:debug": "node-gyp rebuild -C test --debug && NODE_API_BUILD_CONFIG=Debug node ./test/index.js", "prebenchmark": "node-gyp rebuild -C benchmark", "create-coverage": "npm test --coverage", "dev:incremental": "node test", "predev:incremental": "node-gyp configure build -C test --debug", "report-coverage-xml": "rm -rf coverage-xml && mkdir coverage-xml && gcovr -e test --merge-mode-functions merge-use-line-max --xml -o ./coverage-xml/coverage-cxx.xml test", "report-coverage-html": "rm -rf coverage-html && mkdir coverage-html && gcovr -e test --merge-mode-functions merge-use-line-max --html-nested ./coverage-html/index.html test"}, "support": true, "_npmUser": {"name": "nodejs-foundation", "email": "<EMAIL>"}, "pre-commit": "lint", "repository": {"url": "git://github.com/nodejs/node-addon-api.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Node.js API (Node-API)", "directories": {}, "_nodeVersion": "22.10.0", "_hasShrinkwrap": false, "devDependencies": {"path": "^0.12.7", "eslint": "^7.32.0", "semver": "^7.6.0", "bindings": "^1.5.0", "fs-extra": "^11.1.1", "benchmark": "^2.1.4", "pre-commit": "^1.2.2", "safe-buffer": "^5.1.1", "clang-format": "^1.4.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.24.2", "eslint-plugin-promise": "^5.1.0", "eslint-config-standard": "^16.0.3", "eslint-config-semistandard": "^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-addon-api_8.2.2_1731072259348_0.002851102518716342", "host": "s3://npm-registry-packages"}}, "8.3.0": {"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/abhi11210646"}, {"name": "<PERSON>dez", "url": "https://github.com/jmendeth"}, {"name": "<PERSON>", "url": "https://github.com/alexanderfloh"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/ammarfaizi2"}, {"name": "<PERSON><PERSON><PERSON>, Dr", "url": "https://github.com/timarandras"}, {"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/BotellaA"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/azlan"}, {"name": "<PERSON>", "url": "https://github.com/rivertam"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gallafent"}, {"name": "blagoev", "url": "https://github.com/blagoev"}, {"name": "<PERSON>", "url": "https://github.com/bmacnaughton"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/danbev"}, {"name": "<PERSON>", "url": "https://github.com/dantehemerson"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/RaisinTen"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/deepakrkris"}, {"name": "<PERSON>", "url": "https://github.com/dmitryash"}, {"name": "Dongjin Na", "url": "https://github.com/nadongguri"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rubiagatra"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "extremeheat", "url": "https://github.com/extremeheat"}, {"name": "<PERSON>", "url": "https://github.com/F3n67u"}, {"name": "<PERSON>", "url": "https://github.com/fholzer"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/gms1"}, {"name": "<PERSON>", "url": "https://github.com/devsnek"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/helio-frota"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "ikokostya", "url": "https://github.com/ikokostya"}, {"name": "<PERSON>", "url": "https://github.com/JckXia"}, {"name": "<PERSON>", "url": "https://github.com/DuBistKomisch"}, {"name": "<PERSON>", "url": "https://github.com/yjaeseok"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/egg-bread"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/japj"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "<PERSON>", "url": "https://github.com/JoseExposito"}, {"name": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joshgarde"}, {"name": "<PERSON>", "url": "https://github.com/julianmesa-gitkraken"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/hanazuki"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kelvinhammond"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>Eady"}, {"name": "Kévin VOYER", "url": "https://github.com/kecsou"}, {"name": "kidneysolo", "url": "https://github.com/kidneysolo"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Nishikoh"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON>", "url": "https://github.com/nullromo"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/legendecas"}, {"name": "LongYinan", "url": "https://github.com/Brooooooklyn"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/lovell"}, {"name": "<PERSON>", "url": "https://github.com/lmartorella"}, {"name": "<PERSON>gberry", "url": "https://github.com/mastergberry"}, {"name": "<PERSON>", "url": "https://github.com/mathiask88"}, {"name": "<PERSON>", "url": "https://github.com/RedBeard0531"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/mikepricedev"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/mcheshkov"}, {"name": "nempoBu4", "url": "https://github.com/nempoBu4"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/iSkore"}, {"name": "<PERSON>", "url": "https://github.com/seishun"}, {"name": "Nurbol <PERSON>", "url": "https://github.com/anurbol"}, {"name": "pacop", "url": "https://github.com/pacop"}, {"name": "<PERSON>", "url": "https://github.com/petersandor"}, {"name": "<PERSON>", "url": "https://github.com/DaAitch"}, {"name": "rgerd", "url": "https://github.com/rgerd"}, {"name": "<PERSON>", "url": "https://github.com/richardlau"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/ross-weir"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/okuryu"}, {"name": "<PERSON>", "url": "https://github.com/chineduG"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/sam-github"}, {"name": "strager", "url": "https://github.com/strager"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}, {"name": "<PERSON>", "url": "https://github.com/fraxken"}, {"name": "<PERSON>", "url": "https://github.com/timrach"}, {"name": "<PERSON>", "url": "https://github.com/tniessen"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/todoroff"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/toyobayashi"}, {"name": "Tux3", "url": "https://github.com/tux3"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/vmoroz"}, {"name": "WenheLI", "url": "https://github.com/WenheLI"}, {"name": "<PERSON><PERSON>ng <PERSON>", "url": "https://github.com/meixg"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/morokosi"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/fs-eire"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ZzqiZQute"}, {"name": "<PERSON>", "url": "https://github.com/F3n67u"}, {"name": "wanlu wang", "url": "https://github.com/wanlu"}, {"name": "<PERSON>", "url": "https://github.com/chearon"}, {"name": "<PERSON>", "url": "https://github.com/MarxJiao"}, {"name": "Ömer AKGÜL", "url": "https://github.com/tuhalf"}], "description": "Node.js API (Node-API)", "devDependencies": {"benchmark": "^2.1.4", "bindings": "^1.5.0", "clang-format": "^1.4.0", "eslint": "^9.13.0", "fs-extra": "^11.1.1", "neostandard": "^0.11.7", "pre-commit": "^1.2.2", "semver": "^7.6.0"}, "directories": {}, "gypfile": false, "homepage": "https://github.com/nodejs/node-addon-api", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "main": "index.js", "name": "node-addon-api", "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"prebenchmark": "node-gyp rebuild -C benchmark", "benchmark": "node benchmark", "create-coverage": "npm test --coverage", "report-coverage-html": "rm -rf coverage-html && mkdir coverage-html && gcovr -e test --merge-mode-functions merge-use-line-max --html-nested ./coverage-html/index.html test", "report-coverage-xml": "rm -rf coverage-xml && mkdir coverage-xml && gcovr -e test --merge-mode-functions merge-use-line-max --xml -o ./coverage-xml/coverage-cxx.xml test", "pretest": "node-gyp rebuild -C test", "test": "node test", "test:debug": "node-gyp rebuild -C test --debug && NODE_API_BUILD_CONFIG=Debug node ./test/index.js", "predev": "node-gyp rebuild -C test --debug", "dev": "node test", "predev:incremental": "node-gyp configure build -C test --debug", "dev:incremental": "node test", "doc": "doxygen doc/Doxyfile", "lint": "eslint && node tools/clang-format", "lint:fix": "eslint --fix && node tools/clang-format --fix"}, "pre-commit": "lint", "version": "8.3.0", "support": true, "engines": {"node": "^18 || ^20 || >= 21"}, "_id": "node-addon-api@8.3.0", "gitHead": "398eec6b0b396fa5fae7d1a8bcac9f01758878ae", "_nodeVersion": "22.11.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-8VOpLHFrOQlAH+qA0ZzuGRlALRA6/LVh8QJldbrC4DY0hXoMP0l4Acq8TzFC018HztWiRqyCEj2aTWY2UvnJUg==", "shasum": "ec3763f18befc1cdf66d11e157ce44d5eddc0603", "tarball": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-8.3.0.tgz", "fileCount": 18, "unpackedSize": 403298, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/node-addon-api@8.3.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCQu9cjCj+utwmBSGDUVR0AMcuy9SsrVUVd+DKOeJ/jkgIhANXVNV2V8le1jot7Xt1Yu0ElsQULDEKJif3HMK5VGrM6"}]}, "_npmUser": {"name": "nodejs-foundation", "email": "<EMAIL>"}, "maintainers": [{"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "<PERSON>ev<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-addon-api_8.3.0_1732897180679_0.7989046002536839"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-05-04T23:12:20.155Z", "modified": "2024-11-29T16:19:41.321Z", "0.1.0": "2017-05-04T23:12:20.155Z", "0.2.0": "2017-05-18T18:46:40.908Z", "0.3.0": "2017-05-23T17:48:32.810Z", "0.3.1": "2017-05-25T05:49:45.540Z", "0.3.4": "2017-06-13T21:19:29.962Z", "0.3.5": "2017-06-26T19:39:24.416Z", "0.4.0": "2017-07-10T23:35:36.279Z", "0.5.0": "2017-07-18T16:21:20.691Z", "0.5.1": "2017-07-18T21:32:30.392Z", "0.6.2": "2017-08-21T15:27:44.115Z", "0.6.3": "2017-08-25T18:44:49.305Z", "1.0.0": "2017-09-27T13:28:45.780Z", "1.1.0": "2017-11-20T15:47:47.630Z", "1.2.0": "2018-01-11T23:10:32.346Z", "1.3.0": "2018-05-08T19:40:32.175Z", "1.4.0": "2018-07-19T22:15:26.032Z", "1.5.0": "2018-10-03T05:59:27.663Z", "1.6.0": "2018-11-02T22:38:08.964Z", "1.6.1": "2018-11-14T19:43:49.848Z", "1.6.2": "2018-11-29T00:32:53.499Z", "1.6.3": "2019-04-03T21:36:40.886Z", "1.7.0": "2019-07-23T15:31:53.195Z", "1.7.1": "2019-07-23T22:07:46.657Z", "2.0.0": "2019-11-28T12:19:08.099Z", "3.0.0": "2020-04-30T00:21:18.730Z", "1.7.2": "2020-06-02T19:07:47.267Z", "2.0.1": "2020-06-02T19:14:41.201Z", "2.0.2": "2020-07-01T14:44:17.785Z", "3.0.1": "2020-07-13T16:05:44.718Z", "3.0.2": "2020-09-18T16:08:41.104Z", "3.1.0": "2020-12-17T00:41:18.137Z", "3.2.0": "2021-05-17T17:28:07.534Z", "3.2.1": "2021-05-28T18:25:15.697Z", "4.0.0": "2021-06-15T13:27:27.332Z", "4.1.0": "2021-08-25T12:07:07.771Z", "4.2.0": "2021-09-17T16:56:24.180Z", "4.3.0": "2022-01-21T15:06:36.896Z", "5.0.0": "2022-05-02T10:51:50.672Z", "5.1.0": "2023-01-13T11:39:21.661Z", "6.0.0": "2023-02-10T17:22:59.896Z", "6.1.0": "2023-04-20T14:27:30.305Z", "7.0.0": "2023-06-15T13:55:48.692Z", "7.1.0": "2024-01-19T08:46:25.251Z", "8.0.0": "2024-03-05T07:02:28.228Z", "8.1.0": "2024-07-08T08:57:54.301Z", "7.1.1": "2024-07-12T10:15:07.595Z", "8.2.0": "2024-09-30T14:30:16.924Z", "8.2.1": "2024-10-11T15:07:24.071Z", "8.2.2": "2024-11-08T13:24:19.573Z", "8.3.0": "2024-11-29T16:19:40.909Z"}, "bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "license": "MIT", "homepage": "https://github.com/nodejs/node-addon-api", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "description": "Node.js API (Node-API)", "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/abhi11210646"}, {"name": "<PERSON>dez", "url": "https://github.com/jmendeth"}, {"name": "<PERSON>", "url": "https://github.com/alexanderfloh"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/ammarfaizi2"}, {"name": "<PERSON><PERSON><PERSON>, Dr", "url": "https://github.com/timarandras"}, {"name": "<PERSON>", "url": "https://github.com/kirbysayshi"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/anisha-rohra"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/BotellaA"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/a<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/azlan"}, {"name": "<PERSON>", "url": "https://github.com/rivertam"}, {"name": "<PERSON>", "url": "https://github.com/kkoopa"}, {"name": "<PERSON>", "url": "https://github.com/gallafent"}, {"name": "blagoev", "url": "https://github.com/blagoev"}, {"name": "<PERSON>", "url": "https://github.com/bmacnaughton"}, {"name": "<PERSON>", "url": "https://github.com/corymickelson"}, {"name": "<PERSON>", "url": "https://github.com/danbev"}, {"name": "<PERSON>", "url": "https://github.com/dantehemerson"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/RaisinTen"}, {"name": "<PERSON>", "url": "https://github.com/davedoesdev"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/deepakrkris"}, {"name": "<PERSON>", "url": "https://github.com/dmitryash"}, {"name": "Dongjin Na", "url": "https://github.com/nadongguri"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rubiagatra"}, {"name": "<PERSON>", "url": "https://github.com/ebickle"}, {"name": "extremeheat", "url": "https://github.com/extremeheat"}, {"name": "<PERSON>", "url": "https://github.com/F3n67u"}, {"name": "<PERSON>", "url": "https://github.com/fholzer"}, {"name": "<PERSON>", "url": "https://github.com/gabrielschulhof"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/gms1"}, {"name": "<PERSON>", "url": "https://github.com/devsnek"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/helio-frota"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/digitalinfinity"}, {"name": "ikokostya", "url": "https://github.com/ikokostya"}, {"name": "<PERSON>", "url": "https://github.com/JckXia"}, {"name": "<PERSON>", "url": "https://github.com/DuBistKomisch"}, {"name": "<PERSON>", "url": "https://github.com/yjaeseok"}, {"name": "<PERSON>", "url": "https://github.com/jasongin"}, {"name": "<PERSON>", "url": "https://github.com/egg-bread"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/japj"}, {"name": "<PERSON>", "url": "https://github.com/jschlight"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romandev"}, {"name": "<PERSON>", "url": "https://github.com/JoseExposito"}, {"name": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/joshgarde"}, {"name": "<PERSON>", "url": "https://github.com/julianmesa-gitkraken"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/hanazuki"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kelvinhammond"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>Eady"}, {"name": "Kévin VOYER", "url": "https://github.com/kecsou"}, {"name": "kidneysolo", "url": "https://github.com/kidneysolo"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Nishikoh"}, {"name": "<PERSON>", "url": "https://github.com/koistya"}, {"name": "<PERSON>", "url": "https://github.com/kfarnung"}, {"name": "<PERSON>", "url": "https://github.com/nullromo"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/legendecas"}, {"name": "LongYinan", "url": "https://github.com/Brooooooklyn"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/lovell"}, {"name": "<PERSON>", "url": "https://github.com/lmartorella"}, {"name": "<PERSON>gberry", "url": "https://github.com/mastergberry"}, {"name": "<PERSON>", "url": "https://github.com/mathiask88"}, {"name": "<PERSON>", "url": "https://github.com/RedBeard0531"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/mhdawson"}, {"name": "<PERSON>", "url": "https://github.com/mikepricedev"}, {"name": "Michele Campus", "url": "https://github.com/kYroL01"}, {"name": "<PERSON>", "url": "https://github.com/mcheshkov"}, {"name": "nempoBu4", "url": "https://github.com/nempoBu4"}, {"name": "<PERSON>", "url": "https://github.com/NickNaso"}, {"name": "<PERSON>", "url": "https://github.com/iSkore"}, {"name": "<PERSON>", "url": "https://github.com/seishun"}, {"name": "Nurbol <PERSON>", "url": "https://github.com/anurbol"}, {"name": "pacop", "url": "https://github.com/pacop"}, {"name": "<PERSON>", "url": "https://github.com/petersandor"}, {"name": "<PERSON>", "url": "https://github.com/DaAitch"}, {"name": "rgerd", "url": "https://github.com/rgerd"}, {"name": "<PERSON>", "url": "https://github.com/richardlau"}, {"name": "<PERSON>", "url": "https://github.com/rolft<PERSON>mans"}, {"name": "<PERSON>", "url": "https://github.com/ross-weir"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/okuryu"}, {"name": "<PERSON>", "url": "https://github.com/chineduG"}, {"name": "<PERSON>", "url": "https://github.com/sampsongao"}, {"name": "<PERSON>", "url": "https://github.com/sam-github"}, {"name": "strager", "url": "https://github.com/strager"}, {"name": "<PERSON>", "url": "https://github.com/boingoing"}, {"name": "<PERSON>", "url": "https://github.com/fraxken"}, {"name": "<PERSON>", "url": "https://github.com/timrach"}, {"name": "<PERSON>", "url": "https://github.com/tniessen"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/todoroff"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/toyobayashi"}, {"name": "Tux3", "url": "https://github.com/tux3"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/vmoroz"}, {"name": "WenheLI", "url": "https://github.com/WenheLI"}, {"name": "<PERSON><PERSON>ng <PERSON>", "url": "https://github.com/meixg"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/morokosi"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/fs-eire"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ZzqiZQute"}, {"name": "<PERSON>", "url": "https://github.com/F3n67u"}, {"name": "wanlu wang", "url": "https://github.com/wanlu"}, {"name": "<PERSON>", "url": "https://github.com/chearon"}, {"name": "<PERSON>", "url": "https://github.com/MarxJiao"}, {"name": "Ömer AKGÜL", "url": "https://github.com/tuhalf"}], "maintainers": [{"name": "nicknaso", "email": "nicoladel<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "gabrielschulhof", "email": "<EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "<PERSON>ev<PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "README.md", "readmeFilename": "", "users": {"kael": true, "fhinkel": true, "faraoman": true, "johniexu": true, "nicknaso": true, "sn0wdr1am": true}}