{"_id": "html-entities", "_rev": "108-acddd779159adc70b759dd3dab6ef6cc", "name": "html-entities", "description": "Fastest HTML entities encode/decode library.", "dist-tags": {"latest": "2.5.2"}, "versions": {"1.0.0": {"name": "html-entities", "version": "1.0.0", "description": "Faster HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "repository": {"type": "git", "url": "https://github.com/mdevils/node-html-entities.git"}, "main": "index", "engines": ["node >= 0.4.0"], "license": "MIT", "_id": "html-entities@1.0.0", "dist": {"shasum": "e30e06d2ea4a93f7baef53f56ae87b94743679ff", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-1.0.0.tgz", "integrity": "sha512-+cSsKDwjrW3jegpN7D7TBI+j0lmAdjvyfRx0AXYG9J4o7it6OTl65Wb8msjip2rAab13r3M9m7Ac1hRm8nYTxg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCrqBvaXvkVhdseFjhGAttCFwUqboJ+mTHGziypp7R7lwIgEQ/P4ajy7eqMEVe2Smgbgh1KVMoBsY8EwieUFWoPvzQ="}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "directories": {}}, "1.0.1": {"name": "html-entities", "version": "1.0.1", "description": "Faster HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "repository": {"type": "git", "url": "https://github.com/mdevils/node-html-entities.git"}, "main": "index", "engines": ["node >= 0.4.0"], "license": "MIT", "_id": "html-entities@1.0.1", "dist": {"shasum": "9e9a18e2e768738e7b356bb4f518b7db79b80e17", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-1.0.1.tgz", "integrity": "sha512-EgAGCEgiEvZl+WO13kgAo+xy+ESQsQ2lCnY8WYaXZcW8RFwq+7Ap3N7QFkHjEavXGVoHpnj0VM0GS5ZiWq4RJA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD5aNYaRf9Af6XYBOCMXUux69RlT2OoFRz9QfT5nRfEQAIgJvT3X3SEZlfkZEx2JybLC4MjF3iz5ulhLW+HPa2e+D4="}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "directories": {}}, "1.0.2": {"name": "html-entities", "version": "1.0.2", "description": "Faster HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "repository": {"type": "git", "url": "https://github.com/mdevils/node-html-entities.git"}, "main": "index", "engines": ["node >= 0.4.0"], "license": "MIT", "_id": "html-entities@1.0.2", "dist": {"shasum": "c4cc879ca443bf79d1d605356bf4ab39af55f77b", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-1.0.2.tgz", "integrity": "sha512-RVK9nOVIr7TLlRtC+W3LMMj18Fdg+MQWdn2lz/7cC2eaNphh9PimeMSTQZpvXGOeBqqeBfqetqpjjtQ6a3kStA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCx9HrtI4OaslK0pcFG2+BagXfqL79bcYH+QLIr3lTZ2gIhAOIjxCps7sEYV7kmlL3wPCs+WUOqU7kYxrn1sGVcHfMM"}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "directories": {}}, "1.0.3": {"name": "html-entities", "version": "1.0.3", "description": "Faster HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"colors": "*"}, "repository": {"type": "git", "url": "https://github.com/mdevils/node-html-entities.git"}, "main": "index", "engines": ["node >= 0.4.0"], "license": "MIT", "_id": "html-entities@1.0.3", "dist": {"shasum": "a3ac7dece3736e904332443483ec3200574d0a3c", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-1.0.3.tgz", "integrity": "sha512-cbLuAAhM1HPo7shtUfmcN+pWhkoJLggphg9N9VlIGceVs8ssd11usNNS0extyqlR5LMVE2ef/eY+4U568P68mA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBDgJE5obvcSGlUQH33syLuwqSCa3mp+G42l4CfbE9VEAiEA8a0hlIT9DXotfLjJTvE4wJLtjIqNgfq2thTN6ggMRfE="}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "directories": {}}, "1.0.4": {"name": "html-entities", "version": "1.0.4", "description": "Faster HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"colors": "*"}, "repository": {"type": "git", "url": "https://github.com/mdevils/node-html-entities.git"}, "main": "index", "engines": ["node >= 0.4.0"], "license": "MIT", "_id": "html-entities@1.0.4", "dist": {"shasum": "ab4612a58f9d24a590461be3bc480bc20374358c", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-1.0.4.tgz", "integrity": "sha512-A5YTSoHwQSUK/wI3qfGX2/DuN6xPeGuxQfKLQ24J6tsMTnxtOPrsLBwMacMhANHr7cBm8anzrrxjCop4YjKn3Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID51xWVoHZjpvxrxXknzmrbOez69Yb55sdRD0jBUFWqaAiBVvo+QVEQEL6ETUNHI5MEUmyL0D+RwIW28PTJskxpt1g=="}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "directories": {}}, "1.0.5": {"name": "html-entities", "version": "1.0.5", "description": "Faster HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"colors": "*"}, "repository": {"type": "git", "url": "https://github.com/mdevils/node-html-entities.git"}, "main": "index", "engines": ["node >= 0.4.0"], "license": "MIT", "_id": "html-entities@1.0.5", "dist": {"shasum": "f48de682346205c7a91ef75e8e4a0450a0ed6332", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-1.0.5.tgz", "integrity": "sha512-0LdIsJqOIq6Jy+r9pMxAasvNiWG+1d0PuxHMcfJqheIJWE/XKnCYM73A/G7soJVmwsis8eVkB5U6IY0yKf5ovw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCpi2cBrJSqhKTf6sH+Cii4263cLBV+8Y5/+vcEj4B92gIhAODuTu8NaAwB1P1yVNJPu8v2gGWIHvTfP9wzUH2yhTqg"}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "directories": {}}, "1.0.6": {"name": "html-entities", "version": "1.0.6", "description": "Faster HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"colors": "*", "node-html-encoder": "*", "entities": "*"}, "repository": {"type": "git", "url": "https://github.com/mdevils/node-html-entities.git"}, "main": "index", "engines": ["node >= 0.4.0"], "license": "MIT", "_id": "html-entities@1.0.6", "dist": {"shasum": "5a2ab18e1415b4e4605eab158b3e633d0d0dd665", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-1.0.6.tgz", "integrity": "sha512-2wmhiD3Sedz6VkM9KQkbaeFVilGbL24Gh9/9QOV5NoVgl0Y+d3/n5Asb1X+xEmAyLQ69tMG+o1DBiHsA4uk5bA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBdD7Nv3i8/wgtgb14aFkMjCDJJza2iHWfq2eZBo41BpAiBz6A/c3tcgd5NLoR+TTL6GrWaYPDoD3S3PTKa6qWqbQw=="}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "directories": {}}, "1.0.7": {"name": "html-entities", "version": "1.0.7", "description": "Faster HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"colors": "*", "node-html-encoder": "*", "entities": "*"}, "repository": {"type": "git", "url": "https://github.com/mdevils/node-html-entities.git"}, "main": "index", "engines": ["node >= 0.4.0"], "license": "MIT", "_id": "html-entities@1.0.7", "dist": {"shasum": "b72d12d96629c34461aa3b65a925bfa0c59a3b1a", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-1.0.7.tgz", "integrity": "sha512-vveDzqNbblkqyNYVdtTROgV79wIRzKCwtxI0w4ppyCsWUItTzGnFRQCHN81Zz1eFBkv3vTGyNFKBA3XJPwizsg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDQkL1tNj/MYH0TdXq8hUVhddHrJhrfXmS4qkxH98KbUAIgL96cjwkExxA5VDlpQXmabRzC1uroT1S/Qk/zG9NnMuU="}]}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "directories": {}}, "1.0.8": {"name": "html-entities", "version": "1.0.8", "description": "Faster HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"colors": "*", "node-html-encoder": "*", "entities": "*"}, "repository": {"type": "git", "url": "https://github.com/mdevils/node-html-entities.git"}, "main": "index", "engines": ["node >= 0.4.0"], "license": "MIT", "_id": "html-entities@1.0.8", "dist": {"shasum": "3b449b212207c4a495582fb6f419718e34e63747", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-1.0.8.tgz", "integrity": "sha512-bjxqikFVT3Pdl2EwMBYd3upMUezYIhgEGdvLKnpK2RfyuTvB/pv+h0KVUy55PHyLxPwWB9F2xlDokfDGgMPJBw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGAYWzCg/KHqkC42ptiaMvOXJElivDbY0g1k6yj/JnU0AiEAkekW4Dp/bdZRUKFC/jFo9+4dfIMxzEyACrjT4a5xhTI="}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "directories": {}}, "1.0.9": {"name": "html-entities", "version": "1.0.9", "description": "Faster HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"colors": "*", "node-html-encoder": "*", "entities": "*"}, "repository": {"type": "git", "url": "https://github.com/mdevils/node-html-entities.git"}, "main": "index", "engines": ["node >= 0.4.0"], "license": "MIT", "_id": "html-entities@1.0.9", "dist": {"shasum": "f60f64c5f5424a718cc6d6c8b2ee0238e7b19914", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-1.0.9.tgz", "integrity": "sha512-jW/8VZ8tXhAVR8z3ajNocijyDpXW0h9nn4rs9hk7Wg/nOOwtWa7KnH1A6VWB/r/ZKtzOgfO5iFqWESTzjMEurw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFXMrsm5deKFjcWxwQhKKW7HC+KPUeoY4Bfz7DJ5Toi4AiBcFRPXi8rNO9I1oXL9wEUQA6iqzRJC/X6MCjr2DC7zxQ=="}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "directories": {}}, "1.0.10": {"name": "html-entities", "version": "1.0.10", "description": "Faster HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"colors": "*", "node-html-encoder": "*", "entities": "*"}, "repository": {"type": "git", "url": "https://github.com/mdevils/node-html-entities.git"}, "main": "index", "engines": ["node >= 0.4.0"], "license": "MIT", "_id": "html-entities@1.0.10", "dist": {"shasum": "0dea59130dd50df154e82c4cf71d08c2c9dc968b", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-1.0.10.tgz", "integrity": "sha512-m8IoGR6C9czZTkdI8J3yPC/dNF0qRg1Ipov7pte4MaZdA4F+GfNqtgwI3TPFMxrMRHnpcWFsdHuwvv23TyfOcw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEFXgFga2NIdxmZu/jqfis0SVJvPC45mLcdUDyyE6VH5AiA+WVosXVtP98uRekHeCC/jEQ8RvNq/gcTiTDDKykZFew=="}]}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "directories": {}}, "1.1.0": {"name": "html-entities", "version": "1.1.0", "description": "Faster HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"colors": "*", "node-html-encoder": "*", "entities": "*"}, "repository": {"type": "git", "url": "https://github.com/mdevils/node-html-entities.git"}, "main": "index", "engines": ["node >= 0.4.0"], "license": "MIT", "gitHead": "0e246d3ea8885a857783c51aff7529950e4175ff", "bugs": {"url": "https://github.com/mdevils/node-html-entities/issues"}, "homepage": "https://github.com/mdevils/node-html-entities", "_id": "html-entities@1.1.0", "scripts": {}, "_shasum": "07ce0ca796235b52ee98c8d8a7a72e05f63b759a", "_from": ".", "_npmVersion": "1.4.16", "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "dist": {"shasum": "07ce0ca796235b52ee98c8d8a7a72e05f63b759a", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-1.1.0.tgz", "integrity": "sha512-0tIeSFHvOE+V7CsKid0dfQXB4Q/1WzXTm+OpcfFexb9UjDsDhflXETriQgaA5vj7H2w0PkzSLbHXCGhYL4QPLg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCEdv7euvCcwG0mahA8cQlpSKntJbW/fJJ3dOoObEuXEgIgFNqY7l7bDJouE1QI5lkF2xULIXlLKfumL2bbR/bU1dY="}]}, "directories": {}}, "1.1.1": {"name": "html-entities", "version": "1.1.1", "description": "Faster HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"colors": "*", "node-html-encoder": "*", "entities": "*"}, "repository": {"type": "git", "url": "https://github.com/mdevils/node-html-entities.git"}, "main": "index", "engines": ["node >= 0.4.0"], "license": "MIT", "gitHead": "d95308e71ebb70a6f5ab716fcd9183373629f528", "bugs": {"url": "https://github.com/mdevils/node-html-entities/issues"}, "homepage": "https://github.com/mdevils/node-html-entities", "_id": "html-entities@1.1.1", "scripts": {}, "_shasum": "03ba38a4af2eda04c0633368111432c42dea61e1", "_from": ".", "_npmVersion": "1.4.16", "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "dist": {"shasum": "03ba38a4af2eda04c0633368111432c42dea61e1", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-1.1.1.tgz", "integrity": "sha512-ozkDPZF6yCfv0RkcrDx5nB7NOI/ytTcBRVE0CpD6vIj/CT4U74qCd4Dq1dtqsJaUPyGCm8UTO6Gg5jxH7gJlpw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCj4gZ0pMnPOpzzz2ueS+XFP1SjRixf8ktb/ZT7aSxXuAIgVX8grumd40+mYsXFojcriGfO6FyGJ4+SGLXxhubABus="}]}, "directories": {}}, "1.1.2": {"name": "html-entities", "version": "1.1.2", "description": "Faster HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"chai": "^1.9.1", "mocha": "^1.21.4", "unit-coverage": "^3.0.1", "node-html-encoder": "*", "entities": "*", "coveralls": "^2.11.2"}, "repository": {"type": "git", "url": "https://github.com/mdevils/node-html-entities.git"}, "main": "index", "engines": ["node >= 0.4.0"], "unit-coverage": {"common": ["-s", "lib/**/*.js", "-t", "test/**/*.js"]}, "scripts": {"test": "mocha --recursive -R spec test", "benchmark": "node benchmark/benchmark", "coverage": "unit-coverage run -p common", "coverage-html": "unit-coverage run -p common -r html -o coverage.html", "travis": "npm test && unit-coverage run -p common -r lcov -o coverage.lcov && cat coverage.lcov | coveralls"}, "files": ["index.js", "lib", "LICENSE"], "license": "MIT", "gitHead": "25acb062772079a4473402270cb1621edc0f84a1", "bugs": {"url": "https://github.com/mdevils/node-html-entities/issues"}, "homepage": "https://github.com/mdevils/node-html-entities", "_id": "html-entities@1.1.2", "_shasum": "b59d13ad79a74bf5e4f1f0072ad69182f88bb1c4", "_from": ".", "_npmVersion": "1.4.16", "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "dist": {"shasum": "b59d13ad79a74bf5e4f1f0072ad69182f88bb1c4", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-1.1.2.tgz", "integrity": "sha512-co7T1FCW0eOB+N/enHotcp71xqHcXj2COWeT2oV3edjfSIbPl3GVzygRUY/VoTZdwBzUOu/kdSIKmP7plSXfRw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIClkX/VS7KZs18upWZ1BBg+yEy3QYh1oxb6zeRQqEoghAiAJRjoWaL7aGuGraO0wAyAuseahLZ0Sry0UyKf59453vw=="}]}, "directories": {}}, "1.1.3": {"name": "html-entities", "version": "1.1.3", "description": "Faster HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"chai": "^1.9.1", "mocha": "^1.21.4", "unit-coverage": "^3.0.1", "node-html-encoder": "*", "entities": "*", "coveralls": "^2.11.2"}, "repository": {"type": "git", "url": "git+https://github.com/mdevils/node-html-entities.git"}, "main": "index", "engines": ["node >= 0.4.0"], "unit-coverage": {"common": ["-s", "lib/**/*.js", "-t", "test/**/*.js"]}, "scripts": {"test": "mocha --recursive -R spec test", "benchmark": "node benchmark/benchmark", "coverage": "unit-coverage run -p common", "coverage-html": "unit-coverage run -p common -r html -o coverage.html", "travis": "npm test && unit-coverage run -p common -r lcov -o coverage.lcov && cat coverage.lcov | coveralls"}, "files": ["index.js", "lib", "LICENSE"], "license": "MIT", "gitHead": "06e39e68edb58cb72e9f313499916aaa2dc15bc8", "bugs": {"url": "https://github.com/mdevils/node-html-entities/issues"}, "homepage": "https://github.com/mdevils/node-html-entities#readme", "_id": "html-entities@1.1.3", "_shasum": "7e3b47640f5ea69b097ae8bbbe7cb8ca941d85bd", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.5", "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "dist": {"shasum": "7e3b47640f5ea69b097ae8bbbe7cb8ca941d85bd", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-1.1.3.tgz", "integrity": "sha512-Ayw0Uwn5tImSQ0ttDWSH/cntm8jx8PnH/HiqipSIPs6SjTZitQCvS1pAImXlBnFu2RoHPxnSGKFyx9ka2k+NJQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCJMJHSeHUrMk+SjZOFPK9wV/I2RS6WBW3ITqTgOKWjEwIgLmo+t8/BqLs9hBnWm9Ld8+x0THd5HGs4qOGUGPM0q8Q="}]}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "directories": {}}, "1.2.0": {"name": "html-entities", "version": "1.2.0", "description": "Faster HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"chai": "^1.9.1", "mocha": "^1.21.4", "unit-coverage": "^3.0.1", "node-html-encoder": "*", "entities": "*", "coveralls": "^2.11.2"}, "repository": {"type": "git", "url": "git+https://github.com/mdevils/node-html-entities.git"}, "main": "index", "engines": ["node >= 0.4.0"], "unit-coverage": {"common": ["-s", "lib/**/*.js", "-t", "test/**/*.js"]}, "scripts": {"test": "mocha --recursive -R spec test", "benchmark": "node benchmark/benchmark", "coverage": "unit-coverage run -p common", "coverage-html": "unit-coverage run -p common -r html -o coverage.html", "travis": "npm test && unit-coverage run -p common -r lcov -o coverage.lcov && cat coverage.lcov | coveralls"}, "files": ["index.js", "lib", "LICENSE"], "license": "MIT", "gitHead": "3a2cd397697c872325f526bb422bdee74f089cda", "bugs": {"url": "https://github.com/mdevils/node-html-entities/issues"}, "homepage": "https://github.com/mdevils/node-html-entities#readme", "_id": "html-entities@1.2.0", "_shasum": "41948caf85ce82fed36e4e6a0ed371a6664379e2", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.5", "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "dist": {"shasum": "41948caf85ce82fed36e4e6a0ed371a6664379e2", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-1.2.0.tgz", "integrity": "sha512-0md7tlUUyb0BEQGsZzbqty1CgV6RESOoxdivt94AScqhBhYsPCCQCOaGvur/RospMjYpPJ7iFe3zw4Bu4SVA8g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCRaQ6qFygqeuWoXNidrdXI64n6TUAsVwX7SbmlfL6c+wIhAPT8vY8xgP84cKJYRez0I5CPrMqDyT6YZfnDbhbOygDK"}]}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "directories": {}}, "1.2.1": {"name": "html-entities", "version": "1.2.1", "description": "Faster HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"chai": "^1.9.1", "mocha": "^1.21.4", "unit-coverage": "^3.0.1", "node-html-encoder": "*", "entities": "*", "coveralls": "^2.11.2"}, "repository": {"type": "git", "url": "git+https://github.com/mdevils/node-html-entities.git"}, "main": "index", "engines": ["node >= 0.4.0"], "unit-coverage": {"common": ["-s", "lib/**/*.js", "-t", "test/**/*.js"]}, "scripts": {"test": "mocha --recursive -R spec test", "benchmark": "node benchmark/benchmark", "coverage": "unit-coverage run -p common", "coverage-html": "unit-coverage run -p common -r html -o coverage.html", "travis": "npm test && unit-coverage run -p common -r lcov -o coverage.lcov && cat coverage.lcov | coveralls"}, "files": ["index.js", "lib", "LICENSE"], "license": "MIT", "gitHead": "dc08bde42ee6468d60ca617061b0b37b2edc45ca", "bugs": {"url": "https://github.com/mdevils/node-html-entities/issues"}, "homepage": "https://github.com/mdevils/node-html-entities#readme", "_id": "html-entities@1.2.1", "_shasum": "0df29351f0721163515dfb9e5543e5f6eed5162f", "_from": ".", "_npmVersion": "2.15.12", "_nodeVersion": "6.10.0", "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "dist": {"shasum": "0df29351f0721163515dfb9e5543e5f6eed5162f", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-1.2.1.tgz", "integrity": "sha512-LSGr3unsIfdtmwikBiTnJGuqn8lvv/wbNveg42owC3EhR0ZddBTkGW4ReqqQ7DB6QWUFhqMWbYuldgAuZJZcdw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEqHoyFESZ5FjwOlljhdCr/bWzQ9CpSaVLTyWvCZyOVUAiEA421blnxoASO1caMM+Wfi77kOSuWqMM5QHd2xoQjboDw="}]}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/html-entities-1.2.1.tgz_1493034372090_0.6101754817645997"}, "directories": {}}, "1.3.0": {"name": "html-entities", "version": "1.3.0", "description": "Faster HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"@types/chai": "^4.2.11", "@types/mocha": "^7.0.2", "@types/node": "^13.11.1", "chai": "^1.9.1", "coveralls": "^2.11.2", "entities": "*", "mocha": "^1.21.4", "node-html-encoder": "*", "ts-node": "^8.8.2", "typescript": "^3.8.3"}, "repository": {"type": "git", "url": "https://github.com/mdevils/node-html-entities.git"}, "main": "./lib/index.js", "typings": "./lib/index", "types": "./lib/index", "scripts": {"test": "mocha --recursive -r ts-node/register test/**/*.ts", "benchmark": "ts-node benchmark/benchmark", "travis": "yarn test", "build": "tsc", "prepublishOnly": "yarn build"}, "license": "MIT", "licenseText": "Copyright (c) 2013 <PERSON><PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.", "_id": "html-entities@1.3.0", "dist": {"shasum": "4b0f7485de8ef5e05cace963e34beccf9abe6086", "integrity": "sha512-pxm+ybv9V5bYs2c2LifOTC4YIpMySXFXlGbT5QgdnAuhZadHVicAHptKd4AXxbK6/ZTSiDv3pDoWdLbCA8BrYw==", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-1.3.0.tgz", "fileCount": 13, "unpackedSize": 65121, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJekdcECRA9TVsSAnZWagAAnvsP/R2ss5gdQGPEwuicU7Ef\n0TDMqMMxGjpkDZxfepT5jCbDNbelEAbMUSiKHKtG6gXaXHJ0Jw5pdfSkE/6u\nW2f8/Y5jWHAB0mlJgem+bfUu/x4lKgKXOE9sNov2ftHc8M/dQapBtbodgTcM\nTmz2QQng1O1y3a8D4CMWib4EWfANTlPz8DpGRdifhFwBLEct8Ct7v7OqTdnS\nMYgobQXFydmaNzf75pOYZ0yCSKbRY/cdz1/ftMWRYiEIfMxzow6hR5p1mAGT\nxTKFBFvI8hl1hZ8yAD5jgIgEqIm2MwcaA3k5t2AhngxwvOoLZydCC0qN2B8M\nQJ8ndf5kPKjy7zLvhbv0lULpB0gjbTDISmWvqCX5frJF32QiQ2Zvd+Awfibp\nS98GLZxBY9xtR2iUos0bpGOFlxaX6kzvbxOi1iGL6pgGrFrbWxlKqnwqL8XH\n8gk53PSzAWPeVD5ZArlPUPtcbdpkIf2eBNdUGIYaEtg5IBHlDvJVrxGMgQAU\npzUv0bd4Sy+GukLxaLhNdiPx5oR4NuVz2I6PaOAm6nS3YIbSp06ydEoZvXjH\nb6LmzTyhbfQnKHW6qS5/YPz3TqmVkjsMuwi6qWVREA50IGFKRbZrXklPH1sd\n9cb/wp2R8Bs6FHDN83Bl3tlcpUWLna3i9LPeFh6C9D3anZyTU2+0yq8C2Bx7\nbSIS\r\n=ablZ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBQf21StssEb7lQvLuJBb+HXkZTKbiYkl1GCVsA0E0WDAiEA3EXKvulG3HjAwTb4GXaDQ1cSdJCECUkGJQgqhrH6MrA="}]}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/html-entities_1.3.0_1586616067767_0.5276347932721486"}, "_hasShrinkwrap": false}, "1.3.1": {"name": "html-entities", "version": "1.3.1", "description": "Faster HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"@types/chai": "^4.2.11", "@types/mocha": "^7.0.2", "@types/node": "^13.11.1", "chai": "^1.9.1", "coveralls": "^2.11.2", "entities": "*", "mocha": "^1.21.4", "node-html-encoder": "*", "ts-node": "^8.8.2", "typescript": "^3.8.3"}, "repository": {"type": "git", "url": "https://github.com/mdevils/node-html-entities.git"}, "main": "./lib/index.js", "typings": "./lib/index", "types": "./lib/index", "scripts": {"test": "mocha --recursive -r ts-node/register test/**/*.ts", "benchmark": "ts-node benchmark/benchmark", "travis": "yarn test", "build": "tsc", "prepublishOnly": "yarn build"}, "license": "MIT", "licenseText": "Copyright (c) 2013 <PERSON><PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.", "_id": "html-entities@1.3.1", "dist": {"shasum": "fb9a1a4b5b14c5daba82d3e34c6ae4fe701a0e44", "integrity": "sha512-rhE/4Z3hIhzHAUKbW8jVcCyuT5oJCXXqhN/6mXXVCpzTmvJnoH2HL/bt3EZ6p55jbFJBeAe1ZNpL5BugLujxNA==", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-1.3.1.tgz", "fileCount": 13, "unpackedSize": 65121, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJekdeBCRA9TVsSAnZWagAAQV4P+webeAevZODu9+gXi2mS\nEXeT1kfegaY0fvDMAtAh6H1FPFnO/+7flvn9e2vMxoTlkm0dabFiiyosMdeM\nSiLZP/rR1tOvuVGyZbrdYNj+HENQGAZMhcEyIAFupTbxPVqsZCLllFlxEaw3\nDTcAnzGsXGBGrJeV3rKyimANCsOre/ivZgQOMKTyPmBgMLet3QJ7VvqzSLrN\neYXe+pLqp1j3V/ChNbUPBi3p+pp1PYDGNBCW7VnOnotNkoRYm6R9+CSR7YUA\nOC9jc4kYBodw3vjGn7uvUMucO1tiG58jGIk9c4hRpxCXoMfboPt4/V0QgHZf\nRCPPWjKiXKq0Ng0bCh3g37oUuglHQIbLG58c/hLdfA2//l4mUxxYyuG7t7KE\n83jhAawfI30IT+kboFPr7gDBchpaaYO87i6RyIsroqXbPoVAiwXhyjHIiTjv\nx03L9Gma02FeWOt5ywJYNQ+BEmR13iqxyNn8RIXUlEZ3PGadVg8kX1ovRVez\nYJT07nc1+xSO5+Cs0kSc/AW0lHWyno9oqav0x6iiAJrS003VQMFA0BQ4mULW\nJiglQ/swQpyoH9ulzwpW0lnYbxH0UCVpjlOEyq9CYnWAhGAL2KK4A/3UH3wG\nBB49+Rop8to81xFUXobLjrxx0UNAkqlUEsNtcZDds958ho49NQB7IHgkjiR1\nKhmH\r\n=rd9F\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCos0JHG/nxYaiHcPQsXbQGrmRwdW+19Hti3ttgnyMn2QIhAM2ddIMpcKtAm13f5LKEyz2DOBOCcKjrLYEpRxpsFh4e"}]}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/html-entities_1.3.1_1586616193036_0.15238387866380965"}, "_hasShrinkwrap": false}, "1.3.2": {"name": "html-entities", "version": "1.3.2", "description": "Faster HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"@types/chai": "^4.2.11", "@types/mocha": "^7.0.2", "@types/node": "^13.13.4", "chai": "^4.2.0", "coveralls": "^3.1.0", "entities": "^2.0.0", "mocha": "^7.1.2", "node-html-encoder": "^0.0.2", "ts-node": "^8.9.1", "typescript": "^3.8.3"}, "repository": {"type": "git", "url": "https://github.com/mdevils/node-html-entities.git"}, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "types": "./lib/index.d.ts", "scripts": {"test": "mocha --recursive -r ts-node/register test/**/*.ts", "benchmark": "ts-node benchmark/benchmark", "travis": "yarn test", "build": "tsc", "prepublishOnly": "yarn build"}, "license": "MIT", "licenseText": "Copyright (c) 2013 <PERSON><PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.", "_id": "html-entities@1.3.2", "dist": {"shasum": "435f66668813b8e8157166655605ce4db324efa8", "integrity": "sha512-WDzyvI2FV3UIripMO4nqQbxIysC35l63C+Eiz4yG5VXFAG7e8zG8bPGSIg7oc6xjGY9Pjbsu855lc7STRJNQBQ==", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-1.3.2.tgz", "fileCount": 13, "unpackedSize": 65129, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf1o8XCRA9TVsSAnZWagAAC2IQAI5Gx8NrLrGsZFjil4nt\nlfre6ixn2eHHDKCAHaWSZIL+SREDecGh+7wYQzTMaFcrRzsHVDsHt2oqwLvl\nRZ0ZqBHj/ju1OV6BgfcwIscYgzA4E9BVGueoms538jIce0a9th0HrZMeog4r\niyEZluJQgXUuKwc/5gvX03kT4PStWJvWqKv1MJ9mbeF1pdJ1Bwam+20aa0yu\nqIMEZWtq8SQSdgke4H9UAUJ2/olnAIIEdUdiZgcm/VKP1IObZOpWuE7I6p8V\nmwOE+J23zhmKw3NoWnugYYr7Mc4Ad0c8+4AXm9ouJ1vCGWVqRkBpORctPkzl\nlN6I3+fxARWJSlnP1uuzxpWLuAd8O7ERAmXlKJxKBhmBldS/tZuG2eMIqpiu\nMIiwP69OU9XhKOxq3gk3/3nlCnYqlcFCFRztHg/6IrRUa9/5aXC7jOueolr4\nOVO8hExvjc3bMCUqXDra9a3OyE92lzDb7mhiq29DKYofVo/WJytxAFVIgRM0\nGU2FXa+YSE1UPT5Rk79QnXH5Xi5iiJBOCjt1fjj2BGeW0OJlvPvVt43YywnD\nPAVvfXocviAoJorNnEy42AaO8ZbakXyul2Z60OEJ6xSGkfCgwXoVh18QCEbC\n/ZFbQhVOKkHi6eLPrirdf9V/EB9VxmuTG6skQBcDNLyYuUTlFhSE4dKBfT7F\nxrZd\r\n=3MFI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCfbSqFcLrtPY+zu6IjKiuYWAVR/qoPugKR0tZh73X2lAIhALBDAH7MJNhLxpiogbGJH1c2DENFgnjU53m6NHduk0W/"}]}, "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/html-entities_1.3.2_1607896854951_0.0850808166006598"}, "_hasShrinkwrap": false}, "1.3.3": {"name": "html-entities", "version": "1.3.3", "description": "Faster HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"@types/chai": "^4.2.11", "@types/mocha": "^7.0.2", "@types/node": "^13.13.4", "chai": "^4.2.0", "coveralls": "^3.1.0", "entities": "^2.0.0", "mocha": "^7.1.2", "node-html-encoder": "^0.0.2", "ts-node": "^8.9.1", "typescript": "^3.8.3"}, "repository": {"type": "git", "url": "https://github.com/mdevils/node-html-entities.git"}, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "types": "./lib/index.d.ts", "scripts": {"test": "mocha --recursive -r ts-node/register test/**/*.ts", "benchmark": "ts-node benchmark/benchmark", "travis": "yarn test", "build": "tsc", "prepublishOnly": "yarn build"}, "license": "MIT", "licenseText": "Copyright (c) 2013 <PERSON><PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.", "_id": "html-entities@1.3.3", "dist": {"shasum": "3dca638a43ee7de316fc23067398491152ad4736", "integrity": "sha512-/VulV3SYni1taM7a4RMdceqzJWR39gpZHjBwUnsCFKWV/GJkD14CJ5F7eWcZozmHJK0/f/H5U3b3SiPkuvxMgg==", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-1.3.3.tgz", "fileCount": 13, "unpackedSize": 65368, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf1ptMCRA9TVsSAnZWagAAb88P/3IVg+A5e0p8gVHk6Wpe\nI2HqpJjBfzpKpUij/4YeEyamjNLE3nsNliDhNLrOyLp8FwPpwe2fY++Kry7A\nMtBCwufgSeH3sV+UMyf+sUQCXvZAKGnxS23rVUblVKpWzQrZvrKnaVLjDFh9\ni5fq70IRhQjZ3HgzmM9mimXNZZm3NHMp3APXn1BFh46d8uDms9RHQZ9/qJlJ\nwZljsgBST0L6uhHXp7cUkraO90YjpaU/Qpi5mdQs+/7NtTKV+YjyqqeHQxuw\nL5cbH3F+I1ZvP/YyLRk86AldAFjZt4d/hocRLVLYbc7sOjOnzGiQQy/qCarC\nUDkjh0l9AahYcp7hFgv6mzHIdtZG96pBhY3hwi4hvPphRm5ypZsP8Y3MwEvi\nKEUrBczInp+nrGNfTMEEHKKy0EvME/u0BVAsvCtcwqo3X7tDLgS+psy2xTWv\nuUzIFKm7unACEMn5MD0HR5nRqSvojPnanDfxc6uaoVK3xpzXBgXnkqK2BpZD\nO371SQ+dyg+ePxkHe6xAaPGgA1v3GFRE/c+WRARkTpKmaQ+eOw8swxL4/Tzp\nyfXmy4mti7b0mg4QkcE44J0HZlTDpJxNG0QTjtTwS7ok2PwPhN48ZArbJNhm\nudJkCT82ojRHS3xQ7D10rSGLmP7p6hi9A8Uqopq2B0lZtQFFDg75fjaq20I/\no0c0\r\n=bCOJ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCi68zCevkDgma07O5fTdbfpSa/c0rbppiu83zmN1mTnQIgQ6w1pHK/h3YHOjmszRmuZkZlLMWDor8fr/XvBLVAklg="}]}, "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/html-entities_1.3.3_1607899980566_0.164069743688664"}, "_hasShrinkwrap": false}, "1.4.0": {"name": "html-entities", "version": "1.4.0", "description": "Faster HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"@types/benchmark": "^2.1.0", "@types/chai": "^4.2.11", "@types/mocha": "^7.0.2", "@types/node": "^13.13.4", "benchmark": "^2.1.4", "chai": "^4.2.0", "coveralls": "^3.1.0", "entities": "^2.0.0", "mocha": "^7.1.2", "node-html-encoder": "^0.0.2", "ts-node": "^8.9.1", "typescript": "^3.8.3"}, "repository": {"type": "git", "url": "https://github.com/mdevils/node-html-entities.git"}, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "types": "./lib/index.d.ts", "scripts": {"test": "mocha --recursive -r ts-node/register test/**/*.ts", "benchmark": "ts-node benchmark/benchmark", "travis": "yarn test", "build": "tsc", "prepublishOnly": "yarn build"}, "license": "MIT", "licenseText": "Copyright (c) 2013 <PERSON><PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.", "_id": "html-entities@1.4.0", "dist": {"shasum": "cfbd1b01d2afaf9adca1b10ae7dffab98c71d2dc", "integrity": "sha512-8nxjcBcd8wovbeKx7h3wTji4e6+rhaVuPNpMqwWgnHh+N9ToqsCs6XztWRBPQ+UtzsoMAdKZtUENoVzU/EMtZA==", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-1.4.0.tgz", "fileCount": 15, "unpackedSize": 68625, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf3UKHCRA9TVsSAnZWagAAWoIQAJgNG53kc3bgCgIqrtZy\nnP0akSQi0NhWjGrtRaSxiGgomYzIpDrNZ7iBrITvza523CluNdW3slahOyIc\nwaDNFDKox7GqdI1acSUZ8FD2FIos2NeX3IzxKDY8enKt6rz0M3ZWuPPAR6rc\nlbJlQGFgDIkBsEZU/uo6iJQltzNHEcG37v3aa35sHIe37zb5//Gj331GKWFb\nt31D99tAzd/bXvDaH5+bd/k5C2LReAN4wnYEEhvdZDHEDtUeyfT3aTy3j1do\nN8+wyhbtKJHhUQp5LbEOG1+UD6ErhZHBOhnJSSIUNMoo2u6JJt0d4wsXgOTt\ntgSTcW53eg6vQdeXhorXVlLHyG6oXXc3GCjZ62bA0bWwD5m0H8Rwm1CQXReC\nGHtUG0YPRWIy/N2OnjJdMaPAwqE4TGJgVWDkCTph1z6uKGTGVcLboOVYfqyK\npPpcDhbs4clj6T85ah+tm2K3OmtCelBdbRgXNCUVGYCmk9bAkYv/OhT0wdKp\nZtboXibEFpET7HzzBrUMXKZxsIYxmZKoe8onJkF/+Qwd94DlndLeYx5I8On6\nefU8X/yGpL5reXivpck9Bk7sLZGVK6xGMJgRNEM2Ac3WxGkp3vneXIpExdgZ\n0lj2n/uPXR7SOuz8ALp0LbFgZ2YmZ6rLUe4G7Hicjn96EHRk1J/S7YSWxTbK\n9wtO\r\n=LZlN\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEH7ZCYgshTc/JrscSdGZ+1BzBlR3bAMK1T1I0X/w02AAiBI/TRGeswH33AcNlS9Urit/WbmNDwa9YcatPXwYgIcUQ=="}]}, "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/html-entities_1.4.0_1608336006902_0.21367564556080332"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "html-entities", "version": "2.0.0", "description": "Faster HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"@types/benchmark": "^2.1.0", "@types/chai": "^4.2.11", "@types/he": "^1.1.1", "@types/mocha": "^7.0.2", "@types/node": "^13.13.4", "@typescript-eslint/eslint-plugin": "^4.6.1", "@typescript-eslint/parser": "^4.6.1", "benchmark": "^2.1.4", "browserify": "^17.0.0", "chai": "^4.2.0", "coveralls": "^3.1.0", "entities": "^2.1.0", "eslint": "^7.12.1", "eslint-config-prettier": "^6.15.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.1.4", "he": "^1.2.0", "mocha": "^7.1.2", "prettier": "^2.1.2", "ts-node": "^8.9.1", "typescript": "^3.8.3", "uglify-js": "^3.12.3"}, "repository": {"type": "git", "url": "https://github.com/mdevils/node-html-entities.git"}, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "types": "./lib/index.d.ts", "scripts": {"test": "mocha --recursive -r ts-node/register test/**/*.ts", "test:lib": "TEST_LIB=1 yarn test", "benchmark": "ts-node benchmark/benchmark", "travis": "yarn test", "lint": "eslint src/**.ts", "build": "yarn build:ts && yarn build:concat && yarn build:minify && yarn build:cleanup && yarn test:lib", "build:ts": "tsc", "build:concat": "browserify -s html-entities --bare lib/index.js -o lib/index.js", "build:minify": "uglifyjs --compress --mangle --output lib/index.js -- lib/index.js", "build:cleanup": "cd lib && ls -1 | grep -vE 'index.js|index.d.ts' | xargs rm -Rf", "prepublishOnly": "yarn build"}, "license": "MIT", "licenseText": "Copyright (c) 2013 <PERSON><PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.", "_id": "html-entities@2.0.0", "dist": {"shasum": "716a0b682b71377395fe0d837f9537dd3f41a7ab", "integrity": "sha512-zr5pwWJAN6vbBlqQguwADegf1g9l7IKSme6Rd1sFGFk/5Mc72E1an1dW5+65SzS8omJYHf+3k/OKbQ9u+J9dyg==", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-2.0.0.tgz", "fileCount": 7, "unpackedSize": 84908, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf6j7jCRA9TVsSAnZWagAA0BIP/jVnvUVb5+8v/4lAZ58q\nkJW1V5BE9T41UpErjZKo3xQQZxqMeM15Zv/VQXcVNFfS+3BUX8MaJx60bHm9\nOwcDqsOo38bqDtAn/s8gIEUvbUAq6aYx03HQUWvLfMdShrmKLeAloyIoozSN\npAKZWdmAbGlh7NGVMABCoAUoLoLfQUW0HBcdDGUz2YlluJTCOBgc7kh21nUH\n4geVfYirbjfpGOIKaQpbGtH+6Fu0GvkMI9Ng4umD7cMtoc9eBhvP6RLFMoNF\nEIEKH4kKbBwXEJxVw8WjcttnosxsqW3tuCQmQtTV1df1j83YINWgzQeQu829\n4Ib3FRKih/ooQHe+loJjG8dEP4vqavpbwq4QDNI7WOqZCdMaFqT+bsJc2xU5\nXhAy+CCtrCtJdnyVfK7z/0KhNQjUt4YgEnOLYLBD4kDxuo0PhMCgGu5Pz9Z2\nleR9yslW45451WF3adbW/uI/h2H2CVqcNggKIrOMtDa8Vr0s1gVCXFzsvAdm\n/Dj3bJXIVepu9+Qs5XGqsA1AqpYc4B1BFBpaahd94SnAqtUKMQwV/chR+xBv\nzHcjgiAAWybFW/3UPIVgZLiVfIOKISeSQ7WfvHtsYpnadhXiAMcDl4h9Vovt\nE4xM663lMneGQATk4HGZz2EwpIy0YW2Tn5cuGnpVRA8TKPeLMVhgCLKINdJF\nPN4Z\r\n=kaKm\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC80VKSv8WZi9o/PpD2fCeFVDXeu9NQqusAckTTbnV2hAiEAiEwUtFKDHueM77G2TuhK5h4nmOSLRPpRxArJS2BaeDc="}]}, "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/html-entities_2.0.0_1609187042952_0.30324612528762684"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "html-entities", "version": "2.0.1", "description": "Faster HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"@types/benchmark": "^2.1.0", "@types/chai": "^4.2.11", "@types/he": "^1.1.1", "@types/mocha": "^7.0.2", "@types/node": "^13.13.4", "@typescript-eslint/eslint-plugin": "^4.6.1", "@typescript-eslint/parser": "^4.6.1", "benchmark": "^2.1.4", "browserify": "^17.0.0", "chai": "^4.2.0", "coveralls": "^3.1.0", "entities": "^2.1.0", "eslint": "^7.12.1", "eslint-config-prettier": "^6.15.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.1.4", "he": "^1.2.0", "mocha": "^7.1.2", "prettier": "^2.1.2", "ts-node": "^8.9.1", "typescript": "^3.8.3", "uglify-js": "^3.12.3"}, "repository": {"type": "git", "url": "https://github.com/mdevils/node-html-entities.git"}, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "types": "./lib/index.d.ts", "scripts": {"test": "mocha --recursive -r ts-node/register test/**/*.ts", "test:lib": "TEST_LIB=1 yarn test", "benchmark": "ts-node benchmark/benchmark", "lint": "eslint src/**.ts", "build": "yarn build:ts && yarn build:concat && yarn build:minify && yarn build:cleanup && yarn test:lib", "build:ts": "tsc", "build:concat": "browserify -s html-entities --bare lib/index.js -o lib/index.js", "build:minify": "uglifyjs --compress --mangle --output lib/index.js -- lib/index.js", "build:cleanup": "cd lib && ls -1 | grep -vE 'index.js|index.d.ts' | xargs rm -Rf", "prepublishOnly": "yarn build"}, "license": "MIT", "licenseText": "Copyright (c) 2013 <PERSON><PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.", "_id": "html-entities@2.0.1", "dist": {"shasum": "e1a0e02de21dbc0dc549b6e185d4c81bce3a6755", "integrity": "sha512-HeQejGV7VxmGvIuIJziNUd7vAYqW1TKbtMVWl8O8c4Pf8BVQHwpXFsoNxKdxcDKdufara2D3tBfhPLnnvFwhGA==", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-2.0.1.tgz", "fileCount": 7, "unpackedSize": 84621, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf6kjCCRA9TVsSAnZWagAAwKIP/3nfOi/KaeTP+WhqVbMA\nE+g6PhHSoEQU8t2YDAUXI8sM+DNcd1SYnpSy3lNpAb4KjdIfX+Wd1uf1PDAs\n3XhPwMAVSIsZlmTJRqsztx1gMBSsT5D7F3TUGf0L/+65EEDU2N0qdPLvxXLf\nfUjfJd8UIFb0ZZcBoYIzMZxWDkzyRc7jb5hY2AI1UQHF5FgCvaa8WXZDEyob\nWGTirOyemt93cuskSHbcpqH7SzX5hnGQa42fbFVrsLxwGr+lZ5cocMsbKVBw\nWmkHP9Nrhm6KD7+8VIjXd/jzNxIAw6dNeMR+rqyb4yXYiwA4/Nbs2qCdPgYI\nGyQQT++1n9PCRP01dy8WfmpYlw+Ny/LNKUtnp1FD5dCZfrFrLPiUEePbKeCF\nkp/NQ6SBrUlAQDePW/9k2d0uOZYC+QtKZBVmM+mpUE94yEubaK3fdalrKyPA\nEnCkZjZylTLjEdO6Bz+asUSCWs+s14FUnuoS2juOKrujEo0Y7s+o2MLsIrG3\n2+r7aTCEqO/IBPtkBSMKugk6Lb7ZmpnSJUYHmhFADmxGYxZPLodWnq0MTyhg\n/rSvV3eB9hf5WPUSuWU3smbIOQ3vWQoXUaPTzGyzAJ5+DSkr9lvZKc5pVnA3\nHGkSIA8LK5xE45TU/EbO3/WsC649EEbd5nPuEk/kAB7nhXAhsFsQl6/os0e0\nogX5\r\n=48UK\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCVc3bTksRmr3PaRdTgs8JaSrzieramONtvfnoz9vMsjAIhAPt3NrgTYS2Sjr6/N4RhSFsAoCs8JHko6FOohk1HDKm1"}]}, "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/html-entities_2.0.1_1609189570244_0.30845896402575246"}, "_hasShrinkwrap": false}, "2.0.2": {"name": "html-entities", "version": "2.0.2", "description": "Faster HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"@types/benchmark": "^2.1.0", "@types/chai": "^4.2.11", "@types/he": "^1.1.1", "@types/mocha": "^7.0.2", "@types/node": "^13.13.4", "@typescript-eslint/eslint-plugin": "^4.6.1", "@typescript-eslint/parser": "^4.6.1", "benchmark": "^2.1.4", "browserify": "^17.0.0", "chai": "^4.2.0", "coveralls": "^3.1.0", "entities": "^2.1.0", "eslint": "^7.12.1", "eslint-config-prettier": "^6.15.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.1.4", "he": "^1.2.0", "husky": "^4.3.6", "mocha": "^7.1.2", "prettier": "^2.1.2", "ts-node": "^8.9.1", "typescript": "^3.8.3", "uglify-js": "^3.12.3"}, "repository": {"type": "git", "url": "https://github.com/mdevils/node-html-entities.git"}, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "types": "./lib/index.d.ts", "scripts": {"test": "mocha --recursive -r ts-node/register test/**/*.ts", "test:lib": "TEST_LIB=1 yarn test", "benchmark": "ts-node benchmark/benchmark", "lint": "eslint src/**.ts", "build": "yarn build:ts && yarn build:concat && yarn build:minify && yarn build:cleanup && yarn test:lib", "build:ts": "tsc", "build:concat": "browserify -s html-entities --bare lib/index.js -o lib/index.js", "build:minify": "uglifyjs --compress --mangle --output lib/index.js -- lib/index.js", "build:cleanup": "cd lib && ls -1 | grep -vE 'index.js|index.d.ts' | xargs rm -Rf", "prepublishOnly": "yarn build"}, "husky": {"hooks": {"pre-commit": "yarn lint && yarn test"}}, "license": "MIT", "licenseText": "Copyright (c) 2013 <PERSON><PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.", "_id": "html-entities@2.0.2", "dist": {"shasum": "3e0cda2387515b2dc6a947163fd51f6b48d80186", "integrity": "sha512-4IHIARvqG6F1gACykQUXc24KuAM8vbmKHq738CIVsPNBIp/xqmYFbIEEeA51SdjF+mxk/I52reZMNVK8Kzq9Ig==", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-2.0.2.tgz", "fileCount": 7, "unpackedSize": 84804, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf6vcsCRA9TVsSAnZWagAAPaoP/12FUblIYrKjxob9KLYx\nFovlnSHOF7KYjL9IYUURtAMdU8j3/Kj9N3H7P0PfEyjnz3YbU79qyZmkeCTP\n97cRpocn+18L+c7IRTvaq0CHYoYRJRre48S9fsM2PYkMDq5SHPAsLtzBEMnj\nn0Ogw73h75GNpvxsKlluvbjVr8sO0Ky//fiZCfNQlAF0Evl4D2Dw657KV4vl\nh/g2ikQBn1bak3Zk+Pl7CNl/FgC37J4bq6RDzEs5PqOKELLNHCl36sqvPxSn\nUxi+CRY783Ei6oTZK7vawQJd0j8dmsVBN6dayzxvALEDT5VyEwxJOHJGaNXJ\nYxMpmobtFDc0kksx4cyP5xrTsUFfe4/KDLovBCp5bK3zeMrnu9XIkjLiO2Hi\n/3noXdw4Fj88qe4SPaSWPrTbLciEqGnNlyt7jQBTvvlI4ALMXvpITMKr4Zax\n4ImdK5hfHCl3aK/9ZHbCmt8rg2Na/psrzilpiVI3vYqEBMtB8/1hJFYKwszi\ndh6+XDSZHvUJQ1AUNCxBTlZZhpdMb0qFKhsNG8/kudd9ay8Rw1bTy1Fd1NXi\njrMsyUMG5sOOIPeRRfivXUshZ3W++OnImYVbV2Vm/Dnv4q8BIL1NL0DsMH+i\nUu4RQWQ+Menp2YlgjSuBLRwvkd6o7jsPXM7j4HxIaIS18sNpQDNeq0mJRDQP\nGb53\r\n=+T1s\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIESma6DfImDEhN5iX0VWLvD25f6KYgVHhtjNHyKvEixoAiEA4STskWluqaY4PZBITyC2tPriAPsgzohZcYqKEp0+2D8="}]}, "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/html-entities_2.0.2_1609234220365_0.7228194758759856"}, "_hasShrinkwrap": false}, "2.0.3": {"name": "html-entities", "version": "2.0.3", "description": "Fastest HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"@types/benchmark": "^2.1.0", "@types/chai": "^4.2.11", "@types/he": "^1.1.1", "@types/mocha": "^7.0.2", "@types/node": "^13.13.4", "@typescript-eslint/eslint-plugin": "^4.6.1", "@typescript-eslint/parser": "^4.6.1", "benchmark": "^2.1.4", "browserify": "^17.0.0", "chai": "^4.2.0", "coveralls": "^3.1.0", "entities": "^2.1.0", "eslint": "^7.12.1", "eslint-config-prettier": "^6.15.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.1.4", "he": "^1.2.0", "husky": "^4.3.6", "mocha": "^7.1.2", "prettier": "^2.1.2", "ts-node": "^8.9.1", "typescript": "^3.8.3", "uglify-js": "^3.12.3"}, "repository": {"type": "git", "url": "https://github.com/mdevils/node-html-entities.git"}, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "types": "./lib/index.d.ts", "scripts": {"test": "mocha --recursive -r ts-node/register test/**/*.ts", "test:lib": "TEST_LIB=1 yarn test", "benchmark": "ts-node benchmark/benchmark", "lint": "eslint src/**.ts", "build": "yarn build:ts && yarn build:concat && yarn build:minify && yarn build:cleanup && yarn test:lib", "build:ts": "tsc", "build:concat": "browserify -s html-entities --bare lib/index.js -o lib/index.js", "build:minify": "uglifyjs --compress --mangle --output lib/index.js -- lib/index.js", "build:cleanup": "cd lib && ls -1 | grep -vE 'index.js|index.d.ts' | xargs rm -Rf", "prepublishOnly": "yarn build"}, "husky": {"hooks": {"pre-commit": "yarn lint && yarn test"}}, "license": "MIT", "licenseText": "Copyright (c) 2013 <PERSON><PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.", "_id": "html-entities@2.0.3", "dist": {"shasum": "f6ab5fae8900b2348be9fb2166ed3cc6045205d9", "integrity": "sha512-gmHgMT443G7C1ygLCPYBHX9tYeb2RqshhGlKS6b5LC9CnDaPzd1UdvK+Sj5lgoBfJSaBUQE3aIFLSgq4+NB2uw==", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-2.0.3.tgz", "fileCount": 8, "unpackedSize": 88398, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgCiEcCRA9TVsSAnZWagAAeJsQAI7cifa3GhktCBQXDzou\n7JoaIA9v9ufT+x5Fys6wcI1NROB6q6XCibXc1RdCKlTtBFkEYRAjAY0hwxh0\nbt+2tsgTd98fSSbEnV0v6d30mbuhdVJNEWga64kLlQmyFU0/qezSRivovjXV\nzRU6Izh5274E4ZWh9WC9bduoflxJojP4Oyq9trpnwz1BiTnOyw/TUdy6fryP\nJJvLKzMvwmvFN/5xMETNwwEjNAhfzB5eY8zg3I+cdBOJBmVNdfdmiY1hfvyZ\nNFvZhQwE0xYzc83AkjVJ9Y/a7B1guhDADuvVjR1aPxoCErpJM84p3clsKPaH\nnt+rO3dsurdiT52v8iGr/MPNaBxeIinh6RKaYiVNpKV4UZ+r01DQG+O6SU56\ntg7oHDGTZlfHQQHB9qchcZdAss9mwKXO6Q9ZM3ZIEJ4+oiYz5FG9nfNUhkmH\njIY6cQQgquKIbB5lrXIgoB6o61Mzb6wkd3o92GeNwmqMTD2GcvbPeYQBRanX\ncRKhYq4BxSPFka79/0A31lk8qKXj2lquqfBg//H6umaMjpUH9WfCCrFXlASL\n/H5s7jcSmIBAdQoMKCrjngSq6PzVysp4Fv81hMoPUwApco2+qxnsRqI0LSj7\nZ/MXIGXwZllRt8KJhDXnM3qjyBzQB5tE8XCx9A5RueRKTar0THhEr4vXjV5n\no7D9\r\n=e3Rm\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCOR3eBDJO4IwXGFGGNe0WG9C3YZr1paS6GOAMqc6dmHQIgUHSM74Czpxl4th8Pp+rp5BDzPcgqME3ibUC+YaAsSPE="}]}, "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/html-entities_2.0.3_1611276571755_0.503724990468793"}, "_hasShrinkwrap": false}, "2.0.4": {"name": "html-entities", "version": "2.0.4", "description": "Fastest HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"@types/benchmark": "^2.1.0", "@types/chai": "^4.2.11", "@types/he": "^1.1.1", "@types/mocha": "^7.0.2", "@types/node": "^13.13.4", "@typescript-eslint/eslint-plugin": "^4.6.1", "@typescript-eslint/parser": "^4.6.1", "benchmark": "^2.1.4", "chai": "^4.2.0", "entities": "^2.1.0", "eslint": "^7.12.1", "eslint-config-prettier": "^6.15.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.1.4", "he": "^1.2.0", "husky": "^4.3.6", "mocha": "^7.1.2", "prettier": "^2.1.2", "ts-node": "^8.9.1", "typescript": "^3.8.3"}, "repository": {"type": "git", "url": "https://github.com/mdevils/node-html-entities.git"}, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "types": "./lib/index.d.ts", "scripts": {"test": "mocha --recursive -r ts-node/register test/**/*.ts", "test:lib": "TEST_LIB=1 yarn test", "benchmark": "ts-node benchmark/benchmark", "lint": "eslint src/**.ts", "build": "rm -Rf lib/* && tsc && yarn test:lib", "prepublishOnly": "yarn build"}, "husky": {"hooks": {"pre-commit": "yarn lint && yarn test"}}, "license": "MIT", "licenseText": "Copyright (c) 2013 <PERSON><PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.", "_id": "html-entities@2.0.4", "dist": {"shasum": "40fadfbf0a5059fe887a522e82e5858146148bd3", "integrity": "sha512-0KXw8Jx9PuopSrUKiANKS4I8ovQvpIjcO14tvb5eco3qAgXIrf85roz48v0m+UPkoffmG/dgnWjCr9kU1vDlBA==", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-2.0.4.tgz", "fileCount": 14, "unpackedSize": 150958, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgCwl9CRA9TVsSAnZWagAA91cP/1N6yH3aqgxbYuXShj08\n0TOLtM9CwjX4p2h2ptYCY8TB2FzXGXMncJvZH/fWmPKwHQy6uRK1nfejiwGN\nUyRt6i78KfTmpMAfKXqDLPg6bjkplumYXb6N1H4nr47SaEUBl9u2caxDQ34x\neRKbgDOLF1YVhplivwo5xqJUu/uGmCiVEBtRgL0AccBiTLsOHjclXWTP9x2Y\n2p5QRfLNCCbkANK9KsAVHeAjbh5cUsQK/5jkL5PU9cW7sShHk2fuWflj3dM5\ngx+nyQaDLsTonviUj/4ngnoqgUqFmf/8atwzqlqw0O9Oecd9s/ixpLu43E8x\nbGW+sW1YXI7qP53KO9kucNou2N8lUidz+9mU+zprAA6e7dt7TNyLNAv1AVMw\nY6qF1QqHckFIoRPsaVC4MKNeLvRTAI4DW7nFQWEnYJuKyOYKp80VcwQGvUgl\nEyIYE3nEaJ+xPQ8eWsbgPxSW4rrq5Rzl8nPJ7bA677RT3LfvXaoANKTsNr4g\nvdSM4WwSFtURuaxEnNvd9oV+QHPcKCRVswkdPcMtzwwome9pLvmJeYK1kZua\nat42uTn62vL3cBSPSUUFdtVyRc2Q/IXAchiCsxVDaMEsiJ+goE8vajoU3GDK\nWCNQ15exX5nOI6AwOIdaV1zp2UXPmDYDvDw+Dhl9uKD/8k/qkach3/TakxSV\nsZSy\r\n=8qTh\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFTod4tEh2sVdOeJ9ZRoqAdB+6aTuYnMdeEHDgDzCiOLAiEAlnxoYOuobE4WRu/s9WqQDeygSiWgEo3TSNOVWKTRjRQ="}]}, "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/html-entities_2.0.4_1611336061110_0.05942539124575297"}, "_hasShrinkwrap": false}, "2.0.5": {"name": "html-entities", "version": "2.0.5", "description": "Fastest HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"@types/benchmark": "^2.1.0", "@types/chai": "^4.2.11", "@types/he": "^1.1.1", "@types/mocha": "^7.0.2", "@types/node": "^13.13.4", "@typescript-eslint/eslint-plugin": "^4.6.1", "@typescript-eslint/parser": "^4.6.1", "benchmark": "^2.1.4", "chai": "^4.2.0", "entities": "^2.1.0", "eslint": "^7.12.1", "eslint-config-prettier": "^6.15.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.1.4", "he": "^1.2.0", "husky": "^4.3.6", "mocha": "^7.1.2", "prettier": "^2.1.2", "ts-node": "^8.9.1", "typescript": "^3.8.3"}, "repository": {"type": "git", "url": "https://github.com/mdevils/node-html-entities.git"}, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "types": "./lib/index.d.ts", "scripts": {"test": "mocha --recursive -r ts-node/register test/**/*.ts", "test:lib": "TEST_LIB=1 yarn test", "benchmark": "ts-node benchmark/benchmark", "lint": "eslint src/**.ts", "build": "rm -Rf lib/* && tsc && yarn test:lib", "prepublishOnly": "yarn build"}, "husky": {"hooks": {"pre-commit": "yarn lint && yarn test"}}, "license": "MIT", "licenseText": "Copyright (c) 2013 <PERSON><PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.", "_id": "html-entities@2.0.5", "dist": {"shasum": "3e65d672c42339e101394a602c2f56a0f448eb66", "integrity": "sha512-xC2xJWCbMCxeqHpyCrg+iF3vT7hfHcSXTGFyYKb18EOoHR7t7pCPpH51t3XhzhLYDSCZpX7eGRvC6uKWrATGIA==", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-2.0.5.tgz", "fileCount": 14, "unpackedSize": 152878, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgDqUZCRA9TVsSAnZWagAAq44QAJ3xBHg2XfCmZUUv4Vvd\nhdfW/i7EhTUPgLBQdAmugmxBu46pSThVK9z7OlDnlobB30YFPbfI2scG31kR\nnpOj+oPDIZcmvD9PNPrbf/5FrRTnMJYeUAhPf5FWSoXUKNU7wOz+b4xDySKg\np9fajFReWd7lCZa1wpQR5Kf9JrJ49L0h/mg6bYjfd8FezyGgdgZzU9wLz3vw\nQeQYl1rAUXVsVvLU0vmPysPXK6EqjJ9WfUDFFgUr70YvrR90xYGeaVcpbNob\n89ybvJvW+QLQ8zejkz0GOzHwN0cB5lC0qqZ5JIuVU4bYWGVS9inTBNF9x6YV\n2YcteF9rznFF/zAd8qAfKBVaWOEE/abFDUoAKxGqrHjYyqcGCCALGUH4aUXF\nsXXZd/Sh83EoMeT7L9exhES7/rzSCuZAbvC95SGuNAV+9S/5CcPcDjPTxyoi\nizgNwBhio9YmPy5G5pFIpDQkD7jRAkFMLlJuu3Yf/WRPdfziephkkGPHPX3B\nHTc/sTX6Nlv8MOeex8zTG80OnZHpZqPpheqtzL+p1hkylJASkMnT5bRse+ls\nB8Q+uIsO81boRmrDls+xJf7kzk0pOiJe/B+hhHQtTVgcAsLVLtWScVhoLNlv\nj2QVZ/Ux/ghxZX7SzaLErthx6eWodW/wlM9iWYoJxfqkS9KHWredwKwA3XC0\nqV+2\r\n=HPsh\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBqWlW1uSz+JX5oqGzRehz89hzBx80YG/oeJI3V2WsJ8AiEA9d8qT2/iipzN4P4ShUoUUXQw0aZtlc4MR4m7ngW1oRk="}]}, "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/html-entities_2.0.5_1611572504855_0.5062899851374332"}, "_hasShrinkwrap": false}, "2.0.6": {"name": "html-entities", "version": "2.0.6", "description": "Fastest HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"@types/benchmark": "^2.1.0", "@types/chai": "^4.2.11", "@types/he": "^1.1.1", "@types/mocha": "^7.0.2", "@types/node": "^13.13.4", "@typescript-eslint/eslint-plugin": "^4.6.1", "@typescript-eslint/parser": "^4.6.1", "benchmark": "^2.1.4", "chai": "^4.2.0", "entities": "^2.2.0", "eslint": "^7.12.1", "eslint-config-prettier": "^6.15.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.1.4", "he": "^1.2.0", "husky": "^4.3.6", "mocha": "^7.1.2", "prettier": "^2.1.2", "ts-node": "^8.9.1", "typescript": "^3.8.3"}, "repository": {"type": "git", "url": "https://github.com/mdevils/node-html-entities.git"}, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "types": "./lib/index.d.ts", "scripts": {"test": "mocha --recursive -r ts-node/register test/**/*.ts", "test:lib": "TEST_LIB=1 yarn test", "benchmark": "ts-node benchmark/benchmark", "lint": "eslint src/**.ts", "build": "rm -Rf lib/* && tsc && yarn test:lib", "prepublishOnly": "yarn build"}, "husky": {"hooks": {"pre-commit": "yarn lint && yarn test"}}, "license": "MIT", "licenseText": "Copyright (c) 2013 <PERSON><PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.", "_id": "html-entities@2.0.6", "dist": {"shasum": "e773b9acfad65718dd194e599f1b11cf5704b925", "integrity": "sha512-SiH2gwQT78XunjDZfXAa6KrE91kzZUGbef3PL9JmWS+DhL48/+FC+1sNh++/titGzIs8NvH+xuNam/FwV7mJsQ==", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-2.0.6.tgz", "fileCount": 14, "unpackedSize": 152930, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgDqgnCRA9TVsSAnZWagAAzEYP/RaVD2F2FXvOxgVswJg7\nTJel1mXxRSoajbohsY0EoRDkGGB7YwCqIVjK4/ToTIcYQZfXwbHV1WX3+lDL\nJYohFgAAdMboI2kBen8xq3d59MhP7rSMnsJeiYHqfZ55tKrRlpUho7kxU5Ie\n1vXUsDxBbH6euXXBNq0Dx1ShLPk+AD+bdFUNzy6HtmjPhrRMUghfHXUN1U55\nTBP+GaJ3DLKdguo9/EYks7mEGjcZ2jwll7yc33kY2y0CgLneU2PKKlxZe7QW\nRsODaG9uQBvTXOnKFVPuvuHjfcXc5m3uIhOtC7e6sBqSyqFDd60aFO+Tq+nO\n1xDoi4eOvw0poFyMvySNWCtBjpxG+lYWBT1dItEZjLoGzDE0OOa/xqGeeFMW\nzCa4kk6ISHEjVAD5x9ZBN85HLGLjfamB6XBTw3ZPiYXFoZdZde8NngdDXyW9\n/1B8awmTjr9a8f3YbGNbWsWBGeepIBfrivN/n4CPZMjo6p+amykn+Vs0ypmC\nPIheZv7AOD6C2wR9Ins32MMUklFSaS/xvPtWkPaczHO7aKG6Fvb5L0mZJYWU\nySgqMnBdT9eivU3mNIhY6El3j+WvF/B1HOzSrOnD8wC0MEG9ZAhIydR3CpF8\nq31Safd+kbM0pC/1GVAK3lxAjR8315QRy/3vIianQE0U0asiW7yiXbg96bSY\nG3Wv\r\n=b2zv\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEW2VS096jV4/cg/wK4+gmNFvCDTZnz5Ge7UASINqSVUAiEArkTsv8o+F5PhmeSJ9XLdfJ3qApfldTZkZHt/dhU0v5c="}]}, "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/html-entities_2.0.6_1611573286896_0.44573834215270014"}, "_hasShrinkwrap": false}, "2.1.0": {"name": "html-entities", "version": "2.1.0", "description": "Fastest HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"@types/benchmark": "^2.1.0", "@types/chai": "^4.2.11", "@types/he": "^1.1.1", "@types/mocha": "^7.0.2", "@types/node": "^13.13.4", "@typescript-eslint/eslint-plugin": "^4.6.1", "@typescript-eslint/parser": "^4.6.1", "benchmark": "^2.1.4", "chai": "^4.2.0", "entities": "^2.2.0", "eslint": "^7.12.1", "eslint-config-prettier": "^6.15.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.1.4", "he": "^1.2.0", "husky": "^4.3.6", "mocha": "^7.1.2", "prettier": "^2.1.2", "ts-node": "^8.9.1", "typescript": "^3.8.3"}, "repository": {"type": "git", "url": "https://github.com/mdevils/node-html-entities.git"}, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "types": "./lib/index.d.ts", "scripts": {"test": "mocha --recursive -r ts-node/register test/**/*.ts", "test:lib": "TEST_LIB=1 yarn test", "benchmark": "ts-node benchmark/benchmark", "lint": "eslint src/**.ts", "build": "rm -Rf lib/* && tsc && yarn test:lib", "prepublishOnly": "yarn build"}, "husky": {"hooks": {"pre-commit": "yarn lint && yarn test"}}, "license": "MIT", "licenseText": "Copyright (c) 2013 <PERSON><PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.", "_id": "html-entities@2.1.0", "dist": {"shasum": "f5de1f8d5e1f16859a74aa73a90f0db502ca723a", "integrity": "sha512-u+OHVGMH5P1HlaTFp3M4HolRnWepgx5rAnYBo+7/TrBZahuJjgQ4TMv2GjQ4IouGDzkgXYeOI/NQuF95VOUOsQ==", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-2.1.0.tgz", "fileCount": 14, "unpackedSize": 154260, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgEe+aCRA9TVsSAnZWagAA1fUP/ivgt5kE1dRegpA5ypmk\n1A/VUx+EQmJfT5nbgpvEuJDEABLwXQz8dXU1hLthh1nGOJEaaighlYWW8r7u\nqCsXhyvoPE3tWLF+shqm9AwTywDfhwauNEGOvZhM5GJGjAsiGqj9GCwSiuGG\nVISXy+7cgAX1nELg+KdplBEUcux76p8mfpuO3xBgGBoOW1+JlgFsXBFwdxP6\nhYrM5xsSr+NEMQ6VPM0PhKjsDVwU+GpN94P1YdlIw98pYP45KWg3IUrX4mK3\n4rGdD60VugWaApeBhug12QhloI6eyFkUyPY7kBKJV0TC0NsgRmffq9iny4TA\n+Vb9lSaET1nPh9OF+ncsdHUYWnOhmdiULRuuZ9S6k74f+JhtbGaxPnGdkZls\nwPmETfE6+VlWkHeGBgsIDqm/agkbO8NVId0n4smwkdt9tbBqNwUMgkTADgdO\noW6DPBajg68vlFy+YR21ZyXco3C9cI+q4ku7YnX980k3WARoWcEAgPla/edo\ne4t1va2XYmYjt/crw1tEk7aCY5HwhLEdmbUdS+fgejve2vU61JYMXsoP1r4l\nLg5PwrRVDPXdeFfGPA4zAtt7f6nrqEWUeCCKOY4E7c4itrcqnW/eNjRaAgAQ\nX8gnEwSus75zRb/ntwk+XNYm5m1XsufzFX70Z8pjCBN1pelngtvqMdnaZ/MP\nEKKB\r\n=u3K2\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHPkltJrD3Ejxky8F2vIIZCsoOReVy1F7sKt+mnFH0hHAiEAiYk/qJg4ZTlE8tBfo7XzxOG2L8mHWOxC8TmYRd+yLvw="}]}, "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/html-entities_2.1.0_1611788185796_0.768827295269058"}, "_hasShrinkwrap": false}, "2.1.1": {"name": "html-entities", "version": "2.1.1", "description": "Fastest HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"@types/benchmark": "^2.1.0", "@types/chai": "^4.2.11", "@types/he": "^1.1.1", "@types/mocha": "^7.0.2", "@types/node": "^13.13.4", "@typescript-eslint/eslint-plugin": "^4.6.1", "@typescript-eslint/parser": "^4.6.1", "benchmark": "^2.1.4", "chai": "^4.2.0", "entities": "^2.2.0", "eslint": "^7.12.1", "eslint-config-prettier": "^6.15.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.1.4", "he": "^1.2.0", "husky": "^4.3.6", "mocha": "^7.1.2", "prettier": "^2.1.2", "ts-node": "^8.9.1", "typescript": "^3.8.3"}, "repository": {"type": "git", "url": "https://github.com/mdevils/node-html-entities.git"}, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "types": "./lib/index.d.ts", "scripts": {"test": "mocha --recursive -r ts-node/register test/**/*.ts", "test:lib": "TEST_LIB=1 yarn test", "benchmark": "ts-node benchmark/benchmark", "lint": "eslint src/**.ts", "build": "rm -Rf lib/* && tsc && yarn test:lib", "prepublishOnly": "yarn build"}, "husky": {"hooks": {"pre-commit": "yarn lint && yarn test"}}, "license": "MIT", "licenseText": "Copyright (c) 2013 <PERSON><PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON><PERSON><PERSON>G<PERSON> HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.", "_id": "html-entities@2.1.1", "dist": {"shasum": "faa5fc521e2d18499627835d22be3554c202208f", "integrity": "sha512-HjNLgm9Ba8zKd6NDMkXa0mMPn3eDUxOUnEIm/qy2Rm6rnqRHgI9DpMYIv1Fndu8haUmfMQHNYNrlNKmdU8GMnQ==", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-2.1.1.tgz", "fileCount": 14, "unpackedSize": 155439, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgT2BUCRA9TVsSAnZWagAAHzgP/3AK6UMiKdAKZ2v5/kTz\n++GWflmLt7M/V1KhfE8SKzRjd8sXRqHap8+Cxq1vHh5wErR3ThAT6j/EGJy5\n2HpU2YmpT7YjbhE5CyNeqJPQMwGxb4SSivsfbV6UAXYQHmeNqWXDuqQI2NSC\nUGbkxip56RKcXHQwjzFzIcOxG5+pIayGm/JYqmkcUanyP9/e80dCIbiz2fP4\nbRJU1Q0H2uFmLOrpW0FLjzNh3k7jz2haJTvTUFp3CsaLEfmyqQElY4kKEvI/\nG3qqTaXeXZSKpIwNvxJzWL5snT2m6cxBGSolj+rdY0NzlYdD3hG8ZGnNXkhC\nrPGKZzBxdkLKYEb7JoPXzOh5MCHQkTQAvwa0DNlZe4yFtKQrzPZXZNWVm+xD\noKh+ApzO+zsZVvTLi6EEnYCtVC+Iaw14Ne06bmgSWTVjLafUCi/KZ267LqxZ\nPfajPJ1bwVlw4jy4rsVYHEcXvxyQTnEtPGz9AyG1TSIlEPOo0LArO5J4GZA2\nnFizC/G48DVq3znFcwVGiP/ByLt1wWUpp2QhTDVTdeHCA8Hv65reo7d6g7v0\npLclFZr0XekEDWchl5Uu1lYlHevViTDH2SbyySODxOngA3xEdZKPSQSxIrsT\nXsmGjv14/canY2jea29+wWYS9oC3VNRVpujvFJuoGSLvEWfjP34ByPMDcQU+\n1UtW\r\n=L4ec\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQClwoTR+Y3XYg9Df+fm4aZfGTj4OqXhR8sUBT90REy2bgIhAJdF1vJ0kQgL2Xrc4tfBBHF/O3ixBQc9dMhpJDtOLngI"}]}, "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/html-entities_2.1.1_1615814739962_0.13552058695799474"}, "_hasShrinkwrap": false}, "2.2.0": {"name": "html-entities", "version": "2.2.0", "description": "Fastest HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"@types/benchmark": "^2.1.0", "@types/chai": "^4.2.11", "@types/he": "^1.1.1", "@types/mocha": "^7.0.2", "@types/node": "^13.13.4", "@typescript-eslint/eslint-plugin": "^4.6.1", "@typescript-eslint/parser": "^4.6.1", "benchmark": "^2.1.4", "chai": "^4.2.0", "entities": "^2.2.0", "eslint": "^7.12.1", "eslint-config-prettier": "^6.15.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.1.4", "he": "^1.2.0", "husky": "^4.3.6", "mocha": "^7.1.2", "prettier": "^2.1.2", "ts-node": "^8.9.1", "typescript": "^3.8.3"}, "repository": {"type": "git", "url": "git+https://github.com/mdevils/html-entities.git"}, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "types": "./lib/index.d.ts", "scripts": {"test": "mocha --recursive -r ts-node/register test/**/*.ts", "test:lib": "TEST_LIB=1 yarn test", "benchmark": "ts-node benchmark/benchmark", "lint": "eslint src/**.ts", "build": "rm -Rf lib/* && tsc && yarn test:lib", "prepublishOnly": "yarn build"}, "husky": {"hooks": {"pre-commit": "yarn lint && yarn test"}}, "license": "MIT", "gitHead": "e84b655d6317c3dd4a074d8c1ac3c1f37e503262", "bugs": {"url": "https://github.com/mdevils/html-entities/issues"}, "homepage": "https://github.com/mdevils/html-entities#readme", "_id": "html-entities@2.2.0", "_nodeVersion": "14.9.0", "_npmVersion": "6.14.7", "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-wy65ltEBQPf8884vKoA8fHh+A0++7N/NaBIR+hFesj5HhJK0ANSYlXtBMxV5zo/7pj1os51BQWb02WLNTBghyQ==", "shasum": "25115921cb7234d44ea76f23f0f31798a595777c", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-2.2.0.tgz", "fileCount": 12, "unpackedSize": 157813, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgX2BzCRA9TVsSAnZWagAADd0QAInmRarA6gfNU5oWUwxY\nYmUzjjEW3Wwz6FZzPjfCUv5Cdo6LUpcIOQpzfyFRdveYZkZKCkMsZascs56M\nXy9hkzvf1m0jS8jgStOTFMALMXCblDhWgstxYE3BGdfnNOg8fBDJVAprRjbb\n0IKePwCalxJe6WfdcH/g/RQ7VKj9gUDxaxkrn/F54mVCfEiWYjVbVDFnLx9C\nutpG1nngf1zfYRBtXzlluekuMtEYK+kzD5xgapcSbFbhWzJGPtVeQ6kxrqR3\n3IQCTwRD6YJV/9IthlqJawQECIssQBsOTCnOe3MwYIa1X/JRZxG8wAwFbJI5\napzVrjHKK/6486O3WF8IaGeV6/ZN/R9bG0LjgGl5kVdFla8KgvRZOFWEHAR3\nKZh2aekTbZdvvThlsvgNGfFHhOKR3jHChhgpTl7x0qqV3uOFnznLYCKDXAaz\nSLqqPWqsuQFGfAI6y85+SyvS28jFHhFBKNt/I5OwQK8ql7/+Pu8c4Sy57yZd\nqeFytCn7qFNeCL7kAcLmRYaSWXQxHHZNums9VQ/r9KqDjpm360BU4F+cPacP\nDpaLrlsWzlpA3XLDrXopFdO1zSzxHBjk3cHNB19+CJdTe1Tmxvf/QlgFKGz5\ngcfHUKz5DkJd7VsRAkFRyyH+U+IPHMiBa+2JmbBrQV2Do9LMZqzBox3SHq9M\ngmim\r\n=08/B\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC8uBFWPzZlV49eaIeDqHWphV4h87XUHE0JcpCQmL8kHAIhAKLuD6baRRQUaXSpzgQwGpSzB33+MqotLP4u+MUZBfD4"}]}, "directories": {}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/html-entities_2.2.0_1616863346798_0.7444385698373572"}, "_hasShrinkwrap": false}, "2.3.0": {"name": "html-entities", "version": "2.3.0", "description": "Fastest HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"@types/benchmark": "^2.1.0", "@types/chai": "^4.2.11", "@types/he": "^1.1.1", "@types/mocha": "^7.0.2", "@types/node": "^13.13.4", "@typescript-eslint/eslint-plugin": "^4.6.1", "@typescript-eslint/parser": "^4.6.1", "benchmark": "^2.1.4", "chai": "^4.2.0", "entities": "^2.2.0", "eslint": "^7.12.1", "eslint-config-prettier": "^6.15.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.1.4", "flowgen": "^1.13.0", "he": "^1.2.0", "husky": "^4.3.6", "mocha": "^7.1.2", "prettier": "^2.1.2", "ts-node": "^8.9.1", "typescript": "^3.8.3"}, "repository": {"type": "git", "url": "git+https://github.com/mdevils/html-entities.git"}, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "types": "./lib/index.d.ts", "scripts": {"test": "mocha --recursive -r ts-node/register test/**/*.ts", "test:lib": "TEST_LIB=1 yarn test", "benchmark": "ts-node benchmark/benchmark", "lint": "eslint src/**.ts", "flow-type-gen": "flowgen --add-flow-header lib/index.d.ts -o lib/index.js.flow", "build": "rm -Rf lib/* && tsc && yarn flow-type-gen && yarn test:lib", "prepublishOnly": "yarn build"}, "husky": {"hooks": {"pre-commit": "yarn lint && yarn test"}}, "license": "MIT", "gitHead": "290e224dfa60589b364baf6bbab9902960745d31", "bugs": {"url": "https://github.com/mdevils/html-entities/issues"}, "homepage": "https://github.com/mdevils/html-entities#readme", "_id": "html-entities@2.3.0", "_nodeVersion": "14.9.0", "_npmVersion": "6.14.7", "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-/XzLX7A79umjBdw7yYw99bk20uj+L1hQINB029One2OCIqtHj0o5F+XMRam1pveHF4pEYZyr8RSLYWdhguHrwg==", "shasum": "83474887115f12d33a7b85f6573c95223c2940a4", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-2.3.0.tgz", "fileCount": 13, "unpackedSize": 158913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgYFCICRA9TVsSAnZWagAAa8UP/R9sW6d1E0Gl6S1Uao2d\nW7zVnELXzMWPZxq978MUPXT6qsPOdDhLI5UMEHPm/Cs12eFVNlYWB2lnMPcY\nkD1FzhGlH/oMZ4E3D+BNjvx+PXLM7OpOM7uIBTvGIfrDFyMBMA6jBhJxJ6Qf\nPSrn3cppG11TNFefJiSdR3KzG6mChX6P8ingw6QnXkMWxD0gW+eXjjBcj7ny\n0k1fJEDU9bBvzGJFuGmvm7glOOYf86/krM5FNfolxos2hxI/qtqiB8bGF1oR\n0BTI3qOOsypF/MDbhBNm0ofsUai8Z46Agayzi6iJzzL8B8AIH4ZmZDMVWDoP\nmBsTAfM078zC1egKLGzUqqQHxQEu0K+XFgzOJEENu4B67FJ/EbdSbdZAY6YP\n17sYuPZQ2agGLJES+3F+vsj3jBYydGEeKN2ik3NlMXRFYPFiShyi9Pz9Y0Q7\n/+dbpSKMRQ+tSuYDfFVbMSF2LOaXfBUatUn9NDbO0Ka+buuAToJkjiJm2Js+\nMcmTgN9WqOjdNdAGM1CREKLSL0XdNaVV+f+g3UEay+nlxEhPCHTWpVisGP76\nzwA/7KIBGAoeTgRGnkdbeK+CSQetQaY+CYvkFxccZF8A4/vamAQg6yr0/6pK\nQb0qNtQ1+J9ktRcG4wcrmLXLn7VmmhShVVw+Hrfp7IwgQn9B6fFrBE3TYjW3\nF4ED\r\n=2Xfb\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIELP/4NXghYWg40UG1zMUR0getDwI8ov89EM3F6Iq6R5AiB/GI4QBIpeLCQsyWWUH9m+sidzkfWfI/q/QfNag8Nyhw=="}]}, "directories": {}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/html-entities_2.3.0_1616924807856_0.1300927521718438"}, "_hasShrinkwrap": false}, "2.3.1": {"name": "html-entities", "version": "2.3.1", "description": "Fastest HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"@types/benchmark": "^2.1.0", "@types/chai": "^4.2.11", "@types/he": "^1.1.1", "@types/mocha": "^7.0.2", "@types/node": "^13.13.4", "@typescript-eslint/eslint-plugin": "^4.6.1", "@typescript-eslint/parser": "^4.6.1", "benchmark": "^2.1.4", "chai": "^4.2.0", "entities": "^2.2.0", "eslint": "^7.12.1", "eslint-config-prettier": "^6.15.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.1.4", "flowgen": "^1.13.0", "he": "^1.2.0", "husky": "^4.3.6", "mocha": "^7.1.2", "prettier": "^2.1.2", "ts-node": "^8.9.1", "ttypescript": "^1.5.12", "typescript": "^3.8.3", "typescript-transform-macros": "^1.1.1"}, "repository": {"type": "git", "url": "git+https://github.com/mdevils/html-entities.git"}, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "types": "./lib/index.d.ts", "scripts": {"test": "TS_NODE_COMPILER=ttypescript mocha --recursive -r ts-node/register test/**/*.ts", "test:lib": "TEST_LIB=1 yarn test", "benchmark": "TS_NODE_COMPILER=ttypescript ts-node benchmark/benchmark", "lint": "eslint src/**.ts", "flow-type-gen": "flowgen --add-flow-header lib/index.d.ts -o lib/index.js.flow", "build": "rm -Rf lib/* && ttsc && yarn flow-type-gen && yarn test:lib", "prepublishOnly": "yarn build"}, "husky": {"hooks": {"pre-commit": "yarn lint && yarn test"}}, "license": "MIT", "gitHead": "d853697f6c3a2b1a374d08811c20a151ba367f51", "bugs": {"url": "https://github.com/mdevils/html-entities/issues"}, "homepage": "https://github.com/mdevils/html-entities#readme", "_id": "html-entities@2.3.1", "_nodeVersion": "14.9.0", "_npmVersion": "6.14.7", "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-GqonC/ZciYWdIh/yI0R8dlWNbI6sUbqi04r0x2lzvESNGvNn9Axt9g89Zo0itnpTuAT/Fh+hezP3DioCDvwcNw==", "shasum": "3a61f1ce9dc098267ad161f1e7b7eca81361e80e", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-2.3.1.tgz", "fileCount": 13, "unpackedSize": 161256, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgYLqNCRA9TVsSAnZWagAAiuMQAIALXhUP0FkaQJpXG4qY\nA2nkPsrrEd1tyRBMpWB+7BQsquAe7j720dTVMVIwvmyJwvrQ7TFVPmZcdOc7\n+1lsjR/rzbog4tfxlYu/9zRvxPgOnfZUMctDRaw2WbNqZkr3RHJC4hmERJ3l\n+NoztKNgYXV1pr/DtsGEFmLYXHkdjeJ3oqGOmeP9JgiRCHW7Am53QR1x990R\nl8Rqso1xAAdtKQR9iYbHd7qi3+9iP0lGyQPUfQlkiV8lHJJaS7tG/hH0r8fo\nJRfy2OlF41Ol2MSUMbxRbs1/9TK2QxLn3jiN1jZ4V9teZpxj3yKKA13vZ9Uv\nVXZMFV1csx9iXrkEbVf7khmERqnTqgQjapgDqZc/vhXExm1JmUbItXNUI9Ov\nduMeATIEesYRxvgJLh9XvCOMv2nM8W4JrfdLHx0koVTGMrMqhkexC5E/PjTf\nw2ggGZQQpHOOGV+sAIn8L1z54ydyDF60KzCRZa0NyZr7WPluO/qznUGdYlQ+\nR1gPKonzGVRygUVjjOPNq1STAfKGtYFNBdMeWfWikJuyLyBn8k6eE+/M0HIe\ngwz2737zHePXlOAjIjXyD8w7bbNBBrgSt7BG9R90xwlj1ytlRQT4nLafk9v1\nJ2D1BQ7WYoqz+fkUpmVZ+D5c81Q30tcS1bMKsQGypQ/7Ncz4YQNGoe7FrNPI\nK2nP\r\n=fTQQ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDxiLvTt+QI6bzy6YDVOqrbQA0ZqYl6SeYEzP38dB22kwIgdEH3n/fXzVI2DI2rND8XM3eLmUe21UapEyyJOXfmBvM="}]}, "directories": {}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/html-entities_2.3.1_1616951949223_0.5373685312805849"}, "_hasShrinkwrap": false}, "2.3.2": {"name": "html-entities", "version": "2.3.2", "description": "Fastest HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"@types/benchmark": "^2.1.0", "@types/chai": "^4.2.11", "@types/he": "^1.1.1", "@types/mocha": "^7.0.2", "@types/node": "^13.13.4", "@typescript-eslint/eslint-plugin": "^4.6.1", "@typescript-eslint/parser": "^4.6.1", "benchmark": "^2.1.4", "chai": "^4.2.0", "entities": "^2.2.0", "eslint": "^7.12.1", "eslint-config-prettier": "^6.15.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.1.4", "flowgen": "^1.13.0", "he": "^1.2.0", "husky": "^4.3.6", "mocha": "^7.1.2", "prettier": "^2.1.2", "terser": "^5.6.1", "ts-node": "^8.9.1", "ttypescript": "^1.5.12", "typescript": "^3.8.3", "typescript-transform-macros": "^1.1.1"}, "repository": {"type": "git", "url": "git+https://github.com/mdevils/html-entities.git"}, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "types": "./lib/index.d.ts", "scripts": {"test": "TS_NODE_COMPILER=ttypescript mocha --recursive -r ts-node/register test/**/*.ts", "test:lib": "TEST_LIB=1 yarn test", "benchmark": "TS_NODE_COMPILER=ttypescript ts-node benchmark/benchmark", "lint": "eslint src/**.ts", "flow-type-gen": "flowgen --add-flow-header lib/index.d.ts -o lib/index.js.flow", "remove-unused-declarations": "find lib -type f \\( -name '*.d.ts' ! -name index.d.ts \\) | xargs rm", "minimize-lib-files": "find lib -type f \\( -name '*.js' ! -name index.js \\) | while read fn; do terser $fn -o $fn; done", "build": "rm -Rf lib/* && ttsc && yarn remove-unused-declarations && yarn flow-type-gen && yarn minimize-lib-files && yarn test:lib", "prepublishOnly": "yarn build"}, "husky": {"hooks": {"pre-commit": "yarn lint && yarn test"}}, "license": "MIT", "gitHead": "778ec5fa32ce32b730533cedaf1c71c94542e252", "bugs": {"url": "https://github.com/mdevils/html-entities/issues"}, "homepage": "https://github.com/mdevils/html-entities#readme", "_id": "html-entities@2.3.2", "_nodeVersion": "14.9.0", "_npmVersion": "6.14.7", "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-c3Ab/url5ksaT0WyleslpBEthOzWhrjQbg75y7XUsfSzi3Dgzt0l8w5e7DylRn15MTlMMD58dTfzddNS2kcAjQ==", "shasum": "760b404685cb1d794e4f4b744332e3b00dcfe488", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-2.3.2.tgz", "fileCount": 10, "unpackedSize": 99459, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgYfESCRA9TVsSAnZWagAAndsP+gK6JyPDLp6UXy5LYa5M\nfx9G2+KoW51cDxQRVBk6t53QNUz1kc/yjdldS0Bk5/bgUVrFPdeYsqplP3A4\n2yCAPE5N9QmZwWQ8JUqLFb7tHMxUpO4uTP75tz5y3Ps1BoWJhcXMns3Ej/wb\nG5746F3CbLAitXbI7kBaWmaaLjAB4kXHgkfmVbOt/sHablzkeFHNg5OXAJ48\nWzh3zQSRHM5UzfwDcbKFBXkSrGq+D5ENhzAn3Pl090dRmRDlWQ7pJMYpxR+C\ngrSmoincQOtjSDiXdLT441AzXzC9MqjIZ2ZznZP40AIXLTCrH/6ezBX1k9RV\nZVOHn2ZVFJryfO6y24d/aHof2vaxu8ucvL2EseZcMW9I9u0Kv60UWjIsOBjx\nsbZKzq0dzN/+3fiD31lQtBq8dhNVyBdDycP6TsaIoSn66FpOz9Eq5imOLmLd\nUtBphjh4WKEYlH0CfUW1sey81Bmh8hIkXwtXG1FPFL49ULJbTbM5UQGDcdbB\njCDnXkqoZUh3DYDCOvLMZUbJus0+4S5Vz1mBlwlT4t0oqdnkjTsEG2OSKQzn\nB0YSqS79sSB4t/pLmuwMVhte/ktWZTLj08q4REjQ9sJruir8iB1fsZN6VRJm\n6nm44+/i+WDVqZSN6Xh7WbZgxqvKQlOGY9zGWP3ukmOotCRuStk2/4D9koOP\nA4c7\r\n=wCzo\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC+iPk7TkWqsv0LQZX646SXJfx/iZX7wwFjIclxS75eyAiBagklau8q22sXhbTuopMi0pSQ5qRSmjYFhaA7aa/Kfyg=="}]}, "directories": {}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/html-entities_2.3.2_1617031442380_0.24599284608439342"}, "_hasShrinkwrap": false}, "2.3.3": {"name": "html-entities", "version": "2.3.3", "description": "Fastest HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "devDependencies": {"@types/benchmark": "^2.1.0", "@types/chai": "^4.2.11", "@types/he": "^1.1.1", "@types/mocha": "^7.0.2", "@types/node": "^13.13.4", "@typescript-eslint/eslint-plugin": "^4.6.1", "@typescript-eslint/parser": "^4.6.1", "benchmark": "^2.1.4", "chai": "^4.2.0", "entities": "^3.0.1", "eslint": "^7.12.1", "eslint-config-prettier": "^6.15.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.1.4", "flowgen": "^1.13.0", "he": "^1.2.0", "husky": "^4.3.6", "mocha": "^9.1.3", "prettier": "^2.1.2", "terser": "^5.6.1", "ts-node": "^8.9.1", "ttypescript": "^1.5.12", "typescript": "^3.8.3", "typescript-transform-macros": "^1.1.1"}, "repository": {"type": "git", "url": "git+https://github.com/mdevils/html-entities.git"}, "sideEffects": false, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "types": "./lib/index.d.ts", "scripts": {"test": "TS_NODE_COMPILER=ttypescript mocha --recursive -r ts-node/register test/**/*.ts", "test:lib": "TEST_LIB=1 npm run test", "benchmark": "TS_NODE_COMPILER=ttypescript ts-node benchmark/benchmark", "lint": "eslint src/**.ts", "flow-type-gen": "flowgen --add-flow-header lib/index.d.ts -o lib/index.js.flow", "remove-unused-declarations": "find lib -type f \\( -name '*.d.ts' ! -name index.d.ts \\) | xargs rm", "minimize-lib-files": "find lib -type f \\( -name '*.js' ! -name index.js \\) | while read fn; do terser $fn -o $fn; done", "build": "rm -Rf lib/* && ttsc && npm run remove-unused-declarations && npm run flow-type-gen && npm run minimize-lib-files && npm run test:lib", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "npm run lint && npm run test"}}, "license": "MIT", "gitHead": "84cc32dee7999c354a4788d48f2f51f8f563094f", "bugs": {"url": "https://github.com/mdevils/html-entities/issues"}, "homepage": "https://github.com/mdevils/html-entities#readme", "_id": "html-entities@2.3.3", "_nodeVersion": "16.12.0", "_npmVersion": "8.5.0", "dist": {"integrity": "sha512-DV5Ln36z34NNTDgnz0EWGBLZENelNAtkiFA4kyNOG2tDI6Mz1uSWiq1wAKdyjnJwyDiDO7Fa2SO1CTxPXL8VxA==", "shasum": "117d7626bece327fc8baace8868fa6f5ef856e46", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-2.3.3.tgz", "fileCount": 9, "unpackedSize": 95450, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiP6xrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmozNg//ezKMM+ozmoE0Y66TXSTVv5Z3vwA5boSTWsovSFXBf3xLz/b/\r\nY9wFjwcuA1uWri1LE4JW6vxxZAHz01kAhG/IGRivnApA7aJlevuSrpvieSXq\r\nMQD8347rL9RiHILeeuqEoffLY/bNUCPYXU9rLnSnHre4yQNrSij0uRBq0s5S\r\nlL7+q/1a2fYYVtIEYFAuaO5WYpusniHNXi+ig32cAK8KPwvi7DYT5onp6Sj0\r\nFLKno4XOjw90IzFVvq8H7n7vAlB38x9vcxy/qvkROIx6EYSPvuRCoEgnuTuB\r\n7gBy2U19uO2PiEGSE6J5kR/ejuf+C9j5DbyEEqubMJBPh9C+qnuCVEhWyW0V\r\nMc3j4KTQNI5/sj5U88+5gFSVOOBa/s/l9xKQ18CEfAXjhcDUwvUtwOp8GzVz\r\n8lUkSCxKSyPOoKrpcrDbtSjU6FFo/oyUM3zanpP07Q6LXWIbiO4b4BojIcSi\r\nAOoaDuUcP4+UGoi/ZGoOLpjYU1ijXeCmU3yQQ23zWP7LqfS9GMS/0wGRgePy\r\n+jQUV0VI0YlHr4lRrC1jhgdozn/buIxBgszhptcaF00mVzlaysnrF44+dIRf\r\nSbkTy6nbYuD3sZiUp1ZfgpMRKTolgoFHca6ZqDCyXzEd1b84JGCre9s/imMH\r\ntLZ96APb1OzEvbiixWC3wrX5SWsWEyYmqdM=\r\n=Z15g\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCQiP95jNc6CJZ/PJeJc1pwyPjvjOkkdMgBMGBxIcUKyQIhAK2pf/P+7rO4mx9UkiQFjCaBkNn0E7F/bHt9/3RKZe+/"}]}, "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/html-entities_2.3.3_1648340075083_0.31200862692707854"}, "_hasShrinkwrap": false}, "2.3.4": {"name": "html-entities", "version": "2.3.4", "description": "Fastest HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "devDependencies": {"@types/benchmark": "^2.1.0", "@types/chai": "^4.2.11", "@types/he": "^1.1.1", "@types/mocha": "^7.0.2", "@types/node": "^13.13.4", "@typescript-eslint/eslint-plugin": "^4.6.1", "@typescript-eslint/parser": "^4.6.1", "benchmark": "^2.1.4", "chai": "^4.2.0", "entities": "^3.0.1", "eslint": "^7.12.1", "eslint-config-prettier": "^6.15.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.1.4", "flowgen": "^1.13.0", "he": "^1.2.0", "husky": "^4.3.6", "mocha": "^9.2.2", "prettier": "^2.1.2", "terser": "^5.6.1", "ts-node": "^8.9.1", "ttypescript": "^1.5.12", "typescript": "^3.8.3", "typescript-transform-macros": "^1.1.1"}, "repository": {"type": "git", "url": "git+https://github.com/mdevils/html-entities.git"}, "sideEffects": false, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "types": "./lib/index.d.ts", "scripts": {"test": "TS_NODE_COMPILER=ttypescript mocha --recursive -r ts-node/register test/**/*.ts", "test:lib": "TEST_LIB=1 npm run test", "benchmark": "TS_NODE_COMPILER=ttypescript ts-node benchmark/benchmark", "lint": "eslint src/**.ts", "flow-type-gen": "flowgen --add-flow-header lib/index.d.ts -o lib/index.js.flow", "remove-unused-declarations": "find lib -type f \\( -name '*.d.ts' ! -name index.d.ts \\) | xargs rm", "minimize-lib-files": "find lib -type f \\( -name '*.js' ! -name index.js \\) | while read fn; do terser $fn -o $fn; done", "build": "rm -Rf lib/* && ttsc && npm run remove-unused-declarations && npm run flow-type-gen && npm run minimize-lib-files && npm run test:lib", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "npm run lint && npm run test"}}, "license": "MIT", "gitHead": "68ad94a3383bb2a6c6ecd03e1974c0f0f2e99fb7", "bugs": {"url": "https://github.com/mdevils/html-entities/issues"}, "homepage": "https://github.com/mdevils/html-entities#readme", "_id": "html-entities@2.3.4", "_nodeVersion": "18.16.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-TtiHkpRBqP40OzizVWjwBPBsiqchEZxAg/nys6D6lIpdoVLo7sWZ/5Sf/s4UaBHQ6pzUzEr3NiItvEoO46sPtQ==", "shasum": "c65db1302652a0a2986f216ef911df65015dc1ac", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-2.3.4.tgz", "fileCount": 9, "unpackedSize": 97463, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICRbB7OxF6NllLkemD/DRJh8GcLuCGTHRUFjPC8OqgpxAiEA7iGD4IY9iB4DOzOKoJ9bcDGYX65jJlaNAH2chBRglDQ="}]}, "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/html-entities_2.3.4_1685992386918_0.9766617521801344"}, "_hasShrinkwrap": false}, "2.3.5": {"name": "html-entities", "version": "2.3.5", "description": "Fastest HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "devDependencies": {"@types/benchmark": "^2.1.0", "@types/chai": "^4.2.11", "@types/he": "^1.1.1", "@types/mocha": "^7.0.2", "@types/node": "^13.13.4", "@typescript-eslint/eslint-plugin": "^4.6.1", "@typescript-eslint/parser": "^4.6.1", "benchmark": "^2.1.4", "chai": "^4.2.0", "entities": "^4.5.0", "eslint": "^7.12.1", "eslint-config-prettier": "^6.15.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.1.4", "flowgen": "^1.13.0", "he": "^1.2.0", "husky": "^4.3.6", "mocha": "^9.2.2", "prettier": "^2.1.2", "terser": "^5.6.1", "ts-node": "^8.9.1", "ttypescript": "^1.5.15", "typescript": "^3.8.3", "typescript-transform-macros": "^1.1.1"}, "repository": {"type": "git", "url": "git+https://github.com/mdevils/html-entities.git"}, "sideEffects": false, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "types": "./lib/index.d.ts", "scripts": {"test": "TS_NODE_COMPILER=ttypescript mocha --recursive -r ts-node/register test/**/*.ts", "test:lib": "TEST_LIB=1 npm run test", "benchmark": "TS_NODE_COMPILER=ttypescript ts-node benchmark/benchmark", "lint": "eslint src/**.ts", "flow-type-gen": "flowgen --add-flow-header lib/index.d.ts -o lib/index.js.flow", "remove-unused-declarations": "find lib -type f \\( -name '*.d.ts' ! -name index.d.ts \\) | xargs rm", "minimize-lib-files": "find lib -type f \\( -name '*.js' ! -name index.js \\) | while read fn; do terser $fn -o $fn; done", "build": "rm -Rf lib/* && ttsc && npm run remove-unused-declarations && npm run flow-type-gen && npm run minimize-lib-files && npm run test:lib", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "npm run lint && npm run test"}}, "license": "MIT", "gitHead": "f5cc120737b0ca390439a1045186d3bb520fd5b8", "bugs": {"url": "https://github.com/mdevils/html-entities/issues"}, "homepage": "https://github.com/mdevils/html-entities#readme", "_id": "html-entities@2.3.5", "_nodeVersion": "18.16.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-72TJlcMkYsEJASa/**************************+22Ap0Txnlx91sfeB+/A7wNZg7UxtZdhAW4y+/jimrdg==", "shasum": "9f117bf6a5962efc31e094f6c6dad3cf3b95e33e", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-2.3.5.tgz", "fileCount": 9, "unpackedSize": 95839, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCvKEuC0fYnDX3fkp7tc/FjKKHixP4zHENSDT7tEVumNgIhALziI3IR4D/uIvC3WwwHChURcariT7LicZwaTU/0VPFI"}]}, "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/html-entities_2.3.5_1686041114088_0.15696316876194372"}, "_hasShrinkwrap": false}, "2.3.6": {"name": "html-entities", "version": "2.3.6", "description": "Fastest HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/mdevils"}, {"type": "patreon", "url": "https://patreon.com/mdevils"}], "devDependencies": {"@types/benchmark": "^2.1.0", "@types/chai": "^4.2.11", "@types/he": "^1.1.1", "@types/mocha": "^7.0.2", "@types/node": "^13.13.4", "@typescript-eslint/eslint-plugin": "^4.6.1", "@typescript-eslint/parser": "^4.6.1", "benchmark": "^2.1.4", "chai": "^4.2.0", "entities": "^4.5.0", "eslint": "^7.12.1", "eslint-config-prettier": "^6.15.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.1.4", "flowgen": "^1.13.0", "he": "^1.2.0", "husky": "^4.3.6", "mocha": "^9.2.2", "prettier": "^2.1.2", "terser": "^5.6.1", "ts-node": "^8.9.1", "ttypescript": "^1.5.15", "typescript": "^3.8.3", "typescript-transform-macros": "^1.1.1"}, "repository": {"type": "git", "url": "git+https://github.com/mdevils/html-entities.git"}, "sideEffects": false, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "types": "./lib/index.d.ts", "scripts": {"test": "TS_NODE_COMPILER=ttypescript mocha --recursive -r ts-node/register test/**/*.ts", "test:lib": "TEST_LIB=1 npm run test", "benchmark": "TS_NODE_COMPILER=ttypescript ts-node benchmark/benchmark", "lint": "eslint src/**.ts", "flow-type-gen": "flowgen --add-flow-header lib/index.d.ts -o lib/index.js.flow", "remove-unused-declarations": "find lib -type f \\( -name '*.d.ts' ! -name index.d.ts \\) | xargs rm", "minimize-lib-files": "find lib -type f \\( -name '*.js' ! -name index.js \\) | while read fn; do terser $fn -o $fn; done", "build": "rm -Rf lib/* && ttsc && npm run remove-unused-declarations && npm run flow-type-gen && npm run minimize-lib-files && npm run test:lib", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "npm run lint && npm run test"}}, "license": "MIT", "gitHead": "4227e0a0fd80850e07e3378ef36d566b2c9c5f36", "bugs": {"url": "https://github.com/mdevils/html-entities/issues"}, "homepage": "https://github.com/mdevils/html-entities#readme", "_id": "html-entities@2.3.6", "_nodeVersion": "18.16.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-9o0+dcpIw2/HxkNuYKxSJUF/MMRZQECK4GnF+oQOmJ83yCVHTWgCH5aOXxK5bozNRmM8wtgryjHD3uloPBDEGw==", "shasum": "966391d58e5737c77bca4025e31721b496ab7454", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-2.3.6.tgz", "fileCount": 9, "unpackedSize": 96027, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFsUKqXAImz9UUUrJzrdvdS/xqSJ6oznhTftZaydVwHbAiEAiFX54gPm0uwI9KAAsp7yeth4Fdoz5r5/6AP9Y5M0738="}]}, "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/html-entities_2.3.6_1686648138033_0.9844348494435242"}, "_hasShrinkwrap": false}, "2.4.0": {"name": "html-entities", "version": "2.4.0", "description": "Fastest HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/mdevils"}, {"type": "patreon", "url": "https://patreon.com/mdevils"}], "devDependencies": {"@types/benchmark": "^2.1.0", "@types/chai": "^4.2.11", "@types/he": "^1.1.1", "@types/mocha": "^7.0.2", "@types/node": "^13.13.4", "@typescript-eslint/eslint-plugin": "^4.6.1", "@typescript-eslint/parser": "^4.6.1", "benchmark": "^2.1.4", "chai": "^4.2.0", "entities": "^4.5.0", "eslint": "^7.12.1", "eslint-config-prettier": "^6.15.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.1.4", "flowgen": "^1.13.0", "he": "^1.2.0", "husky": "^4.3.6", "mocha": "^9.2.2", "prettier": "^2.1.2", "terser": "^5.6.1", "ts-node": "^8.9.1", "ttypescript": "^1.5.15", "typescript": "^3.8.3", "typescript-transform-macros": "^1.1.1"}, "repository": {"type": "git", "url": "git+https://github.com/mdevils/html-entities.git"}, "sideEffects": false, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "types": "./lib/index.d.ts", "scripts": {"test": "TS_NODE_COMPILER=ttypescript mocha --recursive -r ts-node/register test/**/*.ts", "test:lib": "TEST_LIB=1 npm run test", "benchmark": "TS_NODE_COMPILER=ttypescript ts-node benchmark/benchmark", "lint": "eslint src/**.ts", "flow-type-gen": "flowgen --add-flow-header lib/index.d.ts -o lib/index.js.flow", "remove-unused-declarations": "find lib -type f \\( -name '*.d.ts' ! -name index.d.ts \\) | xargs rm", "minimize-lib-files": "find lib -type f \\( -name '*.js' ! -name index.js \\) | while read fn; do terser $fn -o $fn; done", "build": "rm -Rf lib/* && ttsc && npm run remove-unused-declarations && npm run flow-type-gen && npm run minimize-lib-files && npm run test:lib", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "npm run lint && npm run test"}}, "license": "MIT", "gitHead": "8425deeb6a8678dd874a4b6fd2b1c4b308ac2836", "bugs": {"url": "https://github.com/mdevils/html-entities/issues"}, "homepage": "https://github.com/mdevils/html-entities#readme", "_id": "html-entities@2.4.0", "_nodeVersion": "20.2.0", "_npmVersion": "9.6.6", "dist": {"integrity": "sha512-igBTJcNNNhvZFRtm8uA6xMY6xYleeDwn3PeBCkDz7tHttv4F2hsDI2aPgNERWzvRcNYHNT3ymRaQzllmXj4YsQ==", "shasum": "edd0cee70402584c8c76cc2c0556db09d1f45061", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-2.4.0.tgz", "fileCount": 9, "unpackedSize": 96537, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCEFUEYrZo0WNUvW+wXr2ihnRfKWbZemcpe7ykRPZptqgIgaYBmj59VShJwTaIju8o0DePURqcOuhEPEjpnqrw5YIs="}]}, "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/html-entities_2.4.0_1687645268037_0.16687683320744395"}, "_hasShrinkwrap": false}, "2.5.0": {"name": "html-entities", "version": "2.5.0", "description": "Fastest HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/mdevils"}, {"type": "patreon", "url": "https://patreon.com/mdevils"}], "devDependencies": {"@types/benchmark": "^2.1.0", "@types/chai": "^4.2.11", "@types/he": "^1.1.1", "@types/mocha": "^7.0.2", "@types/node": "^13.13.4", "@typescript-eslint/eslint-plugin": "^4.6.1", "@typescript-eslint/parser": "^4.6.1", "benchmark": "^2.1.4", "chai": "^4.2.0", "entities": "^4.5.0", "eslint": "^7.12.1", "eslint-config-prettier": "^6.15.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.1.4", "flowgen": "^1.13.0", "he": "^1.2.0", "husky": "^4.3.6", "mocha": "^9.2.2", "prettier": "^2.1.2", "terser": "^5.6.1", "ts-node": "^8.9.1", "typescript": "^3.8.3"}, "repository": {"type": "git", "url": "git+https://github.com/mdevils/html-entities.git"}, "sideEffects": false, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "types": "./lib/index.d.ts", "scripts": {"test": "mocha --recursive -r ts-node/register test/**/*.ts", "test:watch": "mocha -w --recursive -r ts-node/register test/**/*.ts", "test:lib": "TEST_LIB=1 npm run test", "benchmark": "ts-node benchmark/benchmark", "lint": "eslint src/**.ts", "flow-type-gen": "flowgen --add-flow-header lib/index.d.ts -o lib/index.js.flow", "remove-unused-declarations": "find lib -type f \\( -name '*.d.ts' ! -name index.d.ts \\) | xargs rm", "minimize-lib-files": "cd lib && find . -type f \\( -name '*.js' \\) | while read fn; do terser --source-map \"content='$fn.map',filename='$fn.map',url='$fn.map'\" $fn -o $fn; done", "build": "rm -Rf lib/* && tsc && npm run remove-unused-declarations && npm run flow-type-gen && npm run minimize-lib-files && npm run test:lib", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "npm run lint && npm run test"}}, "license": "MIT", "_id": "html-entities@2.5.0", "gitHead": "05131cc3733b37b427d1c1786bef6f43b53448bd", "bugs": {"url": "https://github.com/mdevils/html-entities/issues"}, "homepage": "https://github.com/mdevils/html-entities#readme", "_nodeVersion": "21.6.1", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-g3xzbdhD5HamT50Sc7/KVvFvU3SUMpKmJkQWYVRhcXHzwUzRXZt5HzTNwn/5BNMm4bECBZc5YnuPh0J8PIBbAQ==", "shasum": "cb1346d0bca6df719c7f6e9ab09d4d916d1141d4", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-2.5.0.tgz", "fileCount": 13, "unpackedSize": 143567, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD1WYYU9vYxcT7EdRHRPq1iA8yu9GEIOdTiKSC7bYL/4QIhAIws2JlkLHb/BXt8/PWXkuPfp0LfhkwxCOofCz+ljwmF"}]}, "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/html-entities_2.5.0_1709509187357_0.9305082550890262"}, "_hasShrinkwrap": false}, "2.5.1": {"name": "html-entities", "version": "2.5.1", "description": "Fastest HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/mdevils"}, {"type": "patreon", "url": "https://patreon.com/mdevils"}], "devDependencies": {"@types/benchmark": "^2.1.0", "@types/chai": "^4.2.11", "@types/he": "^1.1.1", "@types/mocha": "^7.0.2", "@types/node": "^13.13.4", "@typescript-eslint/eslint-plugin": "^4.6.1", "@typescript-eslint/parser": "^4.6.1", "benchmark": "^2.1.4", "chai": "^4.2.0", "entities": "^4.5.0", "eslint": "^7.12.1", "eslint-config-prettier": "^6.15.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.1.4", "flowgen": "^1.13.0", "he": "^1.2.0", "husky": "^4.3.6", "mocha": "^9.2.2", "prettier": "^2.1.2", "terser": "^5.6.1", "ts-node": "^8.9.1", "typescript": "^3.8.3"}, "repository": {"type": "git", "url": "git+https://github.com/mdevils/html-entities.git"}, "sideEffects": false, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "types": "./lib/index.d.ts", "scripts": {"test": "mocha --recursive -r ts-node/register test/**/*.ts", "test:watch": "mocha -w --recursive -r ts-node/register test/**/*.ts", "test:lib": "TEST_LIB=1 npm run test", "benchmark": "ts-node benchmark/benchmark", "lint": "eslint src/**.ts", "flow-type-gen": "flowgen --add-flow-header lib/index.d.ts -o lib/index.js.flow", "remove-unused-declarations": "find lib -type f \\( -name '*.d.ts' ! -name index.d.ts \\) | xargs rm", "minimize-lib-files": "cd lib && find . -type f \\( -name '*.js' \\) | while read fn; do terser --source-map \"content='$fn.map',filename='$fn.map',url='$fn.map'\" $fn -o $fn; done", "build": "rm -Rf lib/* && tsc && npm run remove-unused-declarations && npm run flow-type-gen && npm run minimize-lib-files && npm run test:lib", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "npm run lint && npm run test"}}, "license": "MIT", "_id": "html-entities@2.5.1", "gitHead": "c1be453c49c82ba824883413745b1e09cc4af637", "bugs": {"url": "https://github.com/mdevils/html-entities/issues"}, "homepage": "https://github.com/mdevils/html-entities#readme", "_nodeVersion": "21.6.1", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-jzoocRv3BaCh44WNWrWg1D/1zIVPcNAh7aaZpnkdV8eYRqX9X1Xy5Nd4SVsMRAc3g08so4KeM7w10GDRzxcISA==", "shasum": "d6e7c8b996fda5e2aa04e36046a27800dc0c047d", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-2.5.1.tgz", "fileCount": 18, "unpackedSize": 455393, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBPnKU9aXh22Yk2SUfGU8ZqbTcraAqJvJ8nsIKA3JxsLAiBbL7l8TUEe5PXVVNUSb39ul/6LYgk+yT9aYk/ybIalSg=="}]}, "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/html-entities_2.5.1_1709553744986_0.39116144045885215"}, "_hasShrinkwrap": false}, "2.5.2": {"name": "html-entities", "version": "2.5.2", "description": "Fastest HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/mdevils"}, {"type": "patreon", "url": "https://patreon.com/mdevils"}], "devDependencies": {"@types/benchmark": "^2.1.0", "@types/chai": "^4.2.11", "@types/he": "^1.1.1", "@types/mocha": "^7.0.2", "@types/node": "^13.13.4", "@typescript-eslint/eslint-plugin": "^4.6.1", "@typescript-eslint/parser": "^4.6.1", "benchmark": "^2.1.4", "chai": "^4.2.0", "entities": "^4.5.0", "eslint": "^7.12.1", "eslint-config-prettier": "^6.15.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.1.4", "flowgen": "^1.13.0", "he": "^1.2.0", "husky": "^4.3.6", "mocha": "^9.2.2", "prettier": "^2.1.2", "terser": "^5.6.1", "ts-node": "^8.9.1", "typescript": "^3.8.3"}, "repository": {"type": "git", "url": "git+https://github.com/mdevils/html-entities.git"}, "sideEffects": false, "main": "./lib/index.js", "typings": "./lib/index.d.ts", "types": "./lib/index.d.ts", "scripts": {"test": "mocha --recursive -r ts-node/register test/**/*.ts", "test:watch": "mocha -w --recursive -r ts-node/register test/**/*.ts", "test:lib": "TEST_LIB=1 npm run test", "benchmark": "ts-node benchmark/benchmark", "lint": "eslint src/**.ts", "flow-type-gen": "flowgen --add-flow-header lib/index.d.ts -o lib/index.js.flow", "remove-unused-declarations": "find lib -type f \\( -name '*.d.ts' ! -name index.d.ts \\) | xargs rm", "minimize-lib-files": "cd lib && find . -type f \\( -name '*.js' \\) | while read fn; do terser --source-map \"content='$fn.map',filename='$fn.map',url='$fn.map'\" $fn -o $fn; done", "build": "rm -Rf lib/* && tsc && npm run remove-unused-declarations && npm run flow-type-gen && npm run minimize-lib-files && npm run test:lib", "prepublishOnly": "npm run build"}, "husky": {"hooks": {"pre-commit": "npm run lint && npm run test"}}, "license": "MIT", "_id": "html-entities@2.5.2", "gitHead": "33d4acf0294d76032ca131dccccdd9520772aea6", "bugs": {"url": "https://github.com/mdevils/html-entities/issues"}, "homepage": "https://github.com/mdevils/html-entities#readme", "_nodeVersion": "21.6.1", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-K//PSRMQk4FZ78Kyau+mZurHn3FH0Vwr+H36eE0rPbeYkRRi9YxceYPhuN60UwWorxyKHhqoAJl2OFKa4BVtaA==", "shasum": "201a3cf95d3a15be7099521620d19dfb4f65359f", "tarball": "https://registry.npmjs.org/html-entities/-/html-entities-2.5.2.tgz", "fileCount": 17, "unpackedSize": 287386, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC1LHycciNQbWDkiuodmp+iBnDI1Uq2CG7jvVMRAP/LiwIhALHuo4QV6jtam3DDblSAXEsPj1cLm5UXJ5B1XVBa47M7"}]}, "_npmUser": {"name": "mdevils", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/html-entities_2.5.2_1709554183352_0.8576240928504364"}, "_hasShrinkwrap": false}}, "readme": "html-entities\n=============\n\nFastest HTML entities library.\n\nComes with both TypeScript and Flow types.\n\nInstallation\n------------\n\n```bash\n$ npm install html-entities\n```\n\nUsage\n-----\n\n### encode(text, options)\n\nEncodes text replacing HTML special characters (`<>&\"'`) and/or other character ranges depending on `mode` option value.\n\n```js\nimport {encode} from 'html-entities';\n\nencode('< > \" \\' & © ∆');\n// -> '&lt; &gt; &quot; &apos; &amp; © ∆'\n\nencode('< ©', {mode: 'nonAsciiPrintable'});\n// -> '&lt; &copy;'\n\nencode('< ©', {mode: 'nonAsciiPrintable', level: 'xml'});\n// -> '&lt; &#169;'\n\nencode('< > \" \\' & ©', {mode: 'nonAsciiPrintableOnly', level: 'xml'});\n// -> '< > \" \\' & &#169;'\n```\n\nOptions:\n\n#### level\n\n * `all` alias to `html5` (default).\n * `html5` uses `HTML5` named references.\n * `html4` uses `HTML4` named references.\n * `xml` uses `XML` named references.\n\n#### mode\n\n * `specialChars` encodes only HTML special characters (default).\n * `nonAscii` encodes HTML special characters and everything outside the [ASCII character range](https://en.wikipedia.org/wiki/ASCII).\n * `nonAsciiPrintable` encodes HTML special characters and everything outiside of the [ASCII printable characters](https://en.wikipedia.org/wiki/ASCII#Printable_characters).\n * `nonAsciiPrintableOnly` everything outiside of the [ASCII printable characters](https://en.wikipedia.org/wiki/ASCII#Printable_characters) keeping HTML special characters intact.\n * `extensive` encodes all non-printable characters, non-ASCII characters and all characters with named references.\n\n#### numeric\n\n * `decimal` uses decimal numbers when encoding html entities. i.e. `&#169;` (default).\n * `hexadecimal` uses hexadecimal numbers when encoding html entities. i.e. `&#xa9;`.\n\n\n### decode(text, options)\n\nDecodes text replacing entities to characters. Unknown entities are left as is.\n\n```js\nimport {decode} from 'html-entities';\n\ndecode('&lt; &gt; &quot; &apos; &amp; &#169; &#8710;');\n// -> '< > \" \\' & © ∆'\n\ndecode('&copy;', {level: 'html5'});\n// -> '©'\n\ndecode('&copy;', {level: 'xml'});\n// -> '&copy;'\n```\n\nOptions:\n\n#### level\n\n * `all` alias to `html5` (default).\n * `html5` uses `HTML5` named references.\n * `html4` uses `HTML4` named references.\n * `xml` uses `XML` named references.\n\n#### scope\n\n * `body` emulates behavior of browser when parsing tag bodies: entities without semicolon are also replaced (default).\n * `attribute` emulates behavior of browser when parsing tag attributes: entities without semicolon are replaced when not followed by equality sign `=`.\n * `strict` ignores entities without semicolon.\n\n### decodeEntity(text, options)\n\nDecodes a single HTML entity. Unknown entitiy is left as is.\n\n```js\nimport {decodeEntity} from 'html-entities';\n\ndecodeEntity('&lt;');\n// -> '<'\n\ndecodeEntity('&copy;', {level: 'html5'});\n// -> '©'\n\ndecodeEntity('&copy;', {level: 'xml'});\n// -> '&copy;'\n```\n\nOptions:\n\n#### level\n\n * `all` alias to `html5` (default).\n * `html5` uses `HTML5` named references.\n * `html4` uses `HTML4` named references.\n * `xml` uses `XML` named references.\n\nPerformance\n-----------\n\nStatistically significant comparison with other libraries using `benchmark.js`.\nResults by this library are marked with `*`.\nThe source code of the benchmark is available at `benchmark/benchmark.ts`.\n\n```\nCommon\n\n    Initialization / Load speed\n\n      * #1: html-entities x 2,632,942 ops/sec ±3.71% (72 runs sampled)\n        #2: entities x 1,379,154 ops/sec ±5.87% (75 runs sampled)\n        #3: he x 1,334,035 ops/sec ±3.14% (83 runs sampled)\n\nHTML5\n\n    Encode test\n\n      * #1: html-entities.encode - html5, nonAscii x 415,806 ops/sec ±0.73% (85 runs sampled)\n      * #2: html-entities.encode - html5, nonAsciiPrintable x 401,420 ops/sec ±0.35% (93 runs sampled)\n        #3: entities.encodeNonAsciiHTML x 401,235 ops/sec ±0.41% (88 runs sampled)\n        #4: entities.encodeHTML x 284,868 ops/sec ±0.45% (93 runs sampled)\n      * #5: html-entities.encode - html5, extensive x 237,613 ops/sec ±0.42% (93 runs sampled)\n        #6: he.encode x 91,459 ops/sec ±0.50% (84 runs sampled)\n\n    Decode test\n\n        #1: entities.decodeHTMLStrict x 614,920 ops/sec ±0.41% (89 runs sampled)\n        #2: entities.decodeHTML x 577,698 ops/sec ±0.44% (90 runs sampled)\n      * #3: html-entities.decode - html5, strict x 323,680 ops/sec ±0.39% (92 runs sampled)\n      * #4: html-entities.decode - html5, body x 297,548 ops/sec ±0.45% (91 runs sampled)\n      * #5: html-entities.decode - html5, attribute x 293,617 ops/sec ±0.37% (94 runs sampled)\n        #6: he.decode x 145,383 ops/sec ±0.36% (94 runs sampled)\n\nHTML4\n\n    Encode test\n\n      * #1: html-entities.encode - html4, nonAscii x 379,799 ops/sec ±0.29% (96 runs sampled)\n      * #2: html-entities.encode - html4, nonAsciiPrintable x 350,003 ops/sec ±0.42% (92 runs sampled)\n      * #3: html-entities.encode - html4, extensive x 169,759 ops/sec ±0.43% (90 runs sampled)\n\n    Decode test\n\n      * #1: html-entities.decode - html4, attribute x 291,048 ops/sec ±0.42% (92 runs sampled)\n      * #2: html-entities.decode - html4, strict x 287,110 ops/sec ±0.56% (93 runs sampled)\n      * #3: html-entities.decode - html4, body x 285,529 ops/sec ±0.57% (93 runs sampled)\n\nXML\n\n    Encode test\n\n        #1: entities.encodeXML x 418,561 ops/sec ±0.80% (90 runs sampled)\n      * #2: html-entities.encode - xml, nonAsciiPrintable x 402,868 ops/sec ±0.30% (89 runs sampled)\n      * #3: html-entities.encode - xml, nonAscii x 403,669 ops/sec ±7.87% (83 runs sampled)\n      * #4: html-entities.encode - xml, extensive x 237,766 ops/sec ±0.45% (93 runs sampled)\n\n    Decode test\n\n        #1: entities.decodeXML x 888,700 ops/sec ±0.48% (93 runs sampled)\n      * #2: html-entities.decode - xml, strict x 353,127 ops/sec ±0.40% (92 runs sampled)\n      * #3: html-entities.decode - xml, body x 355,796 ops/sec ±1.58% (86 runs sampled)\n      * #4: html-entities.decode - xml, attribute x 369,454 ops/sec ±8.74% (84 runs sampled)\n\nEscaping\n\n    Escape test\n\n        #1: entities.escapeUTF8 x 1,308,013 ops/sec ±0.37% (91 runs sampled)\n      * #2: html-entities.encode - xml, specialChars x 1,258,760 ops/sec ±1.00% (93 runs sampled)\n        #3: he.escape x 822,569 ops/sec ±0.24% (94 runs sampled)\n        #4: entities.escape x 434,243 ops/sec ±0.34% (91 runs sampled)\n```\n\nLicense\n-------\n\nMIT\n\nSecurity contact information\n----------------------------\n\nTo report a security vulnerability, please use the\n[Tidelift security contact](https://tidelift.com/security). Tidelift will\ncoordinate the fix and disclosure.\n\n`html-entities` for enterprise\n------------------------------\n\nAvailable as part of the Tidelift Subscription\n\nThe maintainers of `html-entities` and thousands of other packages are working with\nTidelift to deliver commercial support and maintenance for the open source\ndependencies you use to build your applications. Save time, reduce risk, and\nimprove code health, while paying the maintainers of the exact dependencies you\nuse.\n[Learn more.](https://tidelift.com/subscription/pkg/npm-html-entities?utm_source=npm-html-entities&utm_medium=referral&utm_campaign=enterprise)\n", "maintainers": [{"name": "mdevils", "email": "<EMAIL>"}], "time": {"modified": "2024-03-04T12:09:43.994Z", "created": "2013-02-15T15:08:31.052Z", "1.0.0": "2013-02-15T15:08:33.911Z", "1.0.1": "2013-02-15T15:13:29.309Z", "1.0.2": "2013-02-20T09:14:36.442Z", "1.0.3": "2013-02-20T11:32:02.907Z", "1.0.4": "2013-02-20T11:34:09.463Z", "1.0.5": "2013-02-20T13:35:06.581Z", "1.0.6": "2013-02-20T16:52:23.002Z", "1.0.7": "2013-02-20T18:08:26.029Z", "1.0.8": "2013-02-21T10:43:06.556Z", "1.0.9": "2013-02-21T11:12:26.425Z", "1.0.10": "2013-02-23T16:39:35.580Z", "1.1.0": "2014-08-18T23:05:50.705Z", "1.1.1": "2014-08-28T13:21:31.251Z", "1.1.2": "2015-01-12T10:11:08.776Z", "1.1.3": "2015-07-18T14:48:28.315Z", "1.2.0": "2015-10-08T20:11:47.023Z", "1.2.1": "2017-04-24T11:46:12.756Z", "1.3.0": "2020-04-11T14:41:07.925Z", "1.3.1": "2020-04-11T14:43:13.247Z", "1.3.2": "2020-12-13T22:00:55.080Z", "1.3.3": "2020-12-13T22:53:00.686Z", "1.4.0": "2020-12-19T00:00:07.018Z", "2.0.0": "2020-12-28T20:24:03.118Z", "2.0.1": "2020-12-28T21:06:10.396Z", "2.0.2": "2020-12-29T09:30:20.529Z", "2.0.3": "2021-01-22T00:49:31.884Z", "2.0.4": "2021-01-22T17:21:01.257Z", "2.0.5": "2021-01-25T11:01:44.982Z", "2.0.6": "2021-01-25T11:14:47.083Z", "2.1.0": "2021-01-27T22:56:25.950Z", "2.1.1": "2021-03-15T13:25:40.143Z", "2.2.0": "2021-03-27T16:42:26.993Z", "2.3.0": "2021-03-28T09:46:48.073Z", "2.3.1": "2021-03-28T17:19:09.365Z", "2.3.2": "2021-03-29T15:24:02.527Z", "2.3.3": "2022-03-27T00:14:35.230Z", "2.3.4": "2023-06-05T19:13:07.127Z", "2.3.5": "2023-06-06T08:45:14.290Z", "2.3.6": "2023-06-13T09:22:18.168Z", "2.4.0": "2023-06-24T22:21:08.208Z", "2.5.0": "2024-03-03T23:39:47.542Z", "2.5.1": "2024-03-04T12:02:25.178Z", "2.5.2": "2024-03-04T12:09:43.564Z"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/mdevils/html-entities.git"}, "users": {"roboterhund87": true, "radist2s": true, "mightyiam": true, "alejcerro": true, "itonyyo": true, "jcottam": true, "frankymartz": true, "kaperstone": true, "chrisyipw": true, "iolo": true, "itskdk": true, "ahsanshafiq": true, "developit": true, "ugarz": true, "jakub.knejzlik": true, "beytek": true, "taichiro": true, "azevedo": true, "soulevans07": true, "jovinbm": true, "cantidio": true, "shrimpseaweed": true, "warblesync": true, "jian263994241": true, "xch": true, "shuoshubao": true, "mort3za": true, "edwardxyt": true, "codeinpixel": true, "wvlvik": true, "penglyu": true, "flftfqwxf": true, "zeroknight": true, "xiechao06": true, "arhankamra": true}, "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "license": "MIT", "readmeFilename": "README.md", "homepage": "https://github.com/mdevils/html-entities#readme", "bugs": {"url": "https://github.com/mdevils/html-entities/issues"}}