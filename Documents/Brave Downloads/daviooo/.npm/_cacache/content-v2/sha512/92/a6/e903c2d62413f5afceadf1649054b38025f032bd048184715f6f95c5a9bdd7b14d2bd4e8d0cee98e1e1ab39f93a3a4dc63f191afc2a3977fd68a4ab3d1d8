{"_id": "array-buffer-byte-length", "_rev": "2-6a4c2fa4bf3a47bcdf019b16cd75b75b", "name": "array-buffer-byte-length", "dist-tags": {"latest": "1.0.2"}, "versions": {"1.0.0": {"name": "array-buffer-byte-length", "version": "1.0.0", "keywords": ["shim", "polyfill", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "byteLength", "byte", "length", "es-shim API", "es-shims"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "array-buffer-byte-length@1.0.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/array-buffer-byte-length#readme", "bugs": {"url": "https://github.com/inspect-js/array-buffer-byte-length/issues"}, "dist": {"shasum": "fabe8bc193fea865f317fe7807085ee0dee5aead", "tarball": "https://registry.npmjs.org/array-buffer-byte-length/-/array-buffer-byte-length-1.0.0.tgz", "fileCount": 9, "integrity": "sha512-LPuwb2P+NrQw3XhxGc36+XSvuBPopovXYTR9Ew++Du9Yb/bx5AzBfrIsBoj0EZUifjQU+sHL21sseZ3jerWO/A==", "signatures": [{"sig": "MEYCIQCGrXpGWEJqU+COhz+NGVVV2kgIu+KdlLx2NTNSI5DhNQIhAKw8AmptRkb4N9oVyzX1Ph3ymYdK89GjLsK1wCtisPvP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8053, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj/mkqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrqMA/+LOEQuP9n2bVQGecVF8ASabIPdw0R/nW0z2pPTWCwjcY5itQb\r\naloF6PP43IJvsg8RUAMl6IcPmZKpNY4bNpLQ5WYDUuB0Jbg/Opi3EfeGfiqw\r\nxf+Mze7NrthBZlTFEbNql+YOpuzkLItoe5PRvTPFmVDHRnf2Sfb+uM81A7ry\r\nwF8eZzTAy/pqBv2S8+K//X0m22Wq/Ed77A2CujfOpBl6Ilr4X+n0VxtdGFdW\r\n+N7Vrql8vkG6eck6o4XIuCj9CP4bzdCcyqmKRmEQ0MSKiovfUF5CxSpE0rIO\r\nZ02ADqbstEnsD5wUWnd5s1QrRiKCvzYtr/eZ7zyJsLz23XYK3h7jafRqYBDP\r\ng63A+0Q/i/Nx8KuNSfdkzGfkZ8ipFV0QEd9choe4VMUePCOs+7g5KIeV40kX\r\nEubTNZBjk4CCtNLcsW9sNvd9R4QWv45yxhja/K5I8h0hOZCe4XCM/lUJJB2r\r\nI3pRmjHFOMbBHOnKOPg/wASDAY4RkQhAWdXopyxU2j71tPO2QgS0QfNAIJsv\r\nL3vRH9Qw0qR9xDQ+uWZUHpENzv+R6UD/zQGHuWfsJ//32BM2D4X2Hq9/wj+L\r\nCs2iPyZCfHJARav4fqpzw7GeVj/PUB0JcO/DxjAz/8PNXdDxFJ0Ps0N4iPWM\r\nRKpdEajGj+rmGSb62TkbIRAkJNUTFTW+t6A=\r\n=VAku\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "0a880c014f5c635059dc93beae99016e3f9319ce", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js"}, "repository": {"url": "git+https://github.com/inspect-js/array-buffer-byte-length.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "Get the byte length of an ArrayBuffer, even in engines without a `.byteLength` method.", "directories": {}, "_nodeVersion": "19.7.0", "dependencies": {"call-bind": "^1.0.2", "is-array-buffer": "^3.0.1"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.2", "nyc": "^10.3.2", "tape": "^5.6.3", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "auto-changelog": "^2.4.0", "object-inspect": "^1.12.3", "es-value-fixtures": "^1.4.2", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/array-buffer-byte-length_1.0.0_1677617450019_0.841504032980966", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "array-buffer-byte-length", "version": "1.0.1", "keywords": ["shim", "polyfill", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "byteLength", "byte", "length", "es-shim API", "es-shims"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "array-buffer-byte-length@1.0.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/array-buffer-byte-length#readme", "bugs": {"url": "https://github.com/inspect-js/array-buffer-byte-length/issues"}, "dist": {"shasum": "1e5583ec16763540a27ae52eed99ff899223568f", "tarball": "https://registry.npmjs.org/array-buffer-byte-length/-/array-buffer-byte-length-1.0.1.tgz", "fileCount": 11, "integrity": "sha512-ahC5W1xgou+KTXix4sAO8Ki12Q+jf4i0+tmk3sC+zgcynshkHxzpXdImBehiUYKKKDwvfFiJl1tZt6ewscS1Mg==", "signatures": [{"sig": "MEUCIHXWvObtSl9k/Kfdv5aFVuIs/G9l/CwYOyZ7Kam5rqJCAiEAlF/2FYgFxF0nHqq7ojak6u0enCbFh3inzbM6nkGKCeM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13524}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "b946a8a8e116c2d1a7d513bb7c5b8ff034437f07", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "tsc -p .", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js"}, "repository": {"url": "git+https://github.com/inspect-js/array-buffer-byte-length.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Get the byte length of an ArrayBuffer, even in engines without a `.byteLength` method.", "directories": {}, "sideEffects": false, "_nodeVersion": "21.6.0", "dependencies": {"call-bind": "^1.0.5", "is-array-buffer": "^3.0.4"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.4", "nyc": "^10.3.2", "tape": "^5.7.4", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "^5.4.0-dev.20240202", "@types/tape": "^5.6.4", "auto-changelog": "^2.4.0", "object-inspect": "^1.13.1", "@types/for-each": "^0.3.3", "@types/call-bind": "^1.0.5", "es-value-fixtures": "^1.4.2", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.1.0", "@types/object-inspect": "^1.8.4", "@types/es-value-fixtures": "^1.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/array-buffer-byte-length_1.0.1_1707003121957_0.34637006364609313", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "array-buffer-byte-length", "version": "1.0.2", "description": "Get the byte length of an ArrayBuffer, even in engines without a `.byteLength` method.", "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "sideEffects": false, "types": "./index.d.ts", "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run lint", "prelint": "evalmd README.md", "lint": "eslint --ext=js,mjs .", "postlint": "tsc && attw -P", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "npx npm@'>= 10.2' audit --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "keywords": ["shim", "polyfill", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "byteLength", "byte", "length", "es-shim API", "es-shims"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/inspect-js/array-buffer-byte-length.git"}, "bugs": {"url": "https://github.com/inspect-js/array-buffer-byte-length/issues"}, "homepage": "https://github.com/inspect-js/array-buffer-byte-length#readme", "dependencies": {"call-bound": "^1.0.3", "is-array-buffer": "^3.0.5"}, "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/es-value-fixtures": "^1.4.4", "@types/for-each": "^0.3.3", "@types/object-inspect": "^1.13.0", "@types/tape": "^5.8.0", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "es-value-fixtures": "^1.5.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.3", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "testling": {"files": "test/index.js"}, "publishConfig": {"ignore": [".github/workflows"]}, "engines": {"node": ">= 0.4"}, "_id": "array-buffer-byte-length@1.0.2", "gitHead": "59deea89141b4aeeb64122e5e15efda485942c00", "_nodeVersion": "23.5.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-LHE+8BuR7RYGDKvnrmcuSq3tDcKv9OFEXQt/HpbZhY7V6h0zlUXutnAD82GiFx9rdieCMjkvtcsPqBwgUl1Iiw==", "shasum": "384d12a37295aec3769ab022ad323a18a51ccf8b", "tarball": "https://registry.npmjs.org/array-buffer-byte-length/-/array-buffer-byte-length-1.0.2.tgz", "fileCount": 11, "unpackedSize": 12007, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAaCdr9JYB+kRRTH+Z6sDjGejVTXOLq6hbzzkcK311z6AiEAy7XS/vV4N3AcZt8/fpVs14MoW3LEzSmJLWALVZkGgiY="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/array-buffer-byte-length_1.0.2_1734657717417_0.8112415031209577"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-02-28T20:50:50.018Z", "modified": "2024-12-20T01:21:57.781Z", "1.0.0": "2023-02-28T20:50:50.182Z", "1.0.1": "2024-02-03T23:32:02.101Z", "1.0.2": "2024-12-20T01:21:57.597Z"}, "bugs": {"url": "https://github.com/inspect-js/array-buffer-byte-length/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/inspect-js/array-buffer-byte-length#readme", "keywords": ["shim", "polyfill", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "byteLength", "byte", "length", "es-shim API", "es-shims"], "repository": {"type": "git", "url": "git+https://github.com/inspect-js/array-buffer-byte-length.git"}, "description": "Get the byte length of an ArrayBuffer, even in engines without a `.byteLength` method.", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "readme": "# array-buffer-byte-length <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\nGet the byte length of an ArrayBuffer, even in engines without a `.byteLength` method.\n\n## Example\n\n```js\nconst assert = require('assert');\nconst byteLength = require('array-buffer-byte-length');\n\nassert.equal(byteLength([]), NaN, 'an array is not an ArrayBuffer, yields NaN');\n\nassert.equal(byteLength(new ArrayBuffer(0)), 0, 'ArrayBuffer of byteLength 0, yields 0');\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n[package-url]: https://npmjs.org/package/array-buffer-byte-length\n[npm-version-svg]: https://versionbadg.es/inspect-js/array-buffer-byte-length.svg\n[deps-svg]: https://david-dm.org/inspect-js/array-buffer-byte-length.svg\n[deps-url]: https://david-dm.org/inspect-js/array-buffer-byte-length\n[dev-deps-svg]: https://david-dm.org/inspect-js/array-buffer-byte-length/dev-status.svg\n[dev-deps-url]: https://david-dm.org/inspect-js/array-buffer-byte-length#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/array-buffer-byte-length.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/array-buffer-byte-length.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/array-buffer-byte-length.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=array-buffer-byte-length\n[codecov-image]: https://codecov.io/gh/inspect-js/array-buffer-byte-length/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/inspect-js/array-buffer-byte-length/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/inspect-js/array-buffer-byte-length\n[actions-url]: https://github.com/inspect-js/array-buffer-byte-length/actions\n", "readmeFilename": "README.md"}