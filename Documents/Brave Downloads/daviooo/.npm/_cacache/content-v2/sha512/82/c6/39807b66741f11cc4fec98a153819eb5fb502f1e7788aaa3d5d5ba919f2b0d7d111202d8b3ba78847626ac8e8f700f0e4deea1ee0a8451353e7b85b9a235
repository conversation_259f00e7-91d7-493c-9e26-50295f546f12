{"_id": "object-keys", "_rev": "105-608e8f8f7248ddae12506c589f0f42d3", "name": "object-keys", "description": "An Object.keys replacement, in case Object.keys is not available. From https://github.com/es-shims/es5-shim", "dist-tags": {"latest": "1.1.1"}, "versions": {"0.0.1": {"name": "object-keys", "version": "0.0.1", "author": {"name": "<PERSON>"}, "description": "An Object.keys replacement, in case Object.keys is not available. From https://github.com/kriskowal/es5-shim", "license": "MIT", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git://github.com/ljharb/objectkeys.git"}, "keywords": ["Object.keys", "keys", "ES5", "shim"], "devDependencies": {"tap": "~0.4.1"}, "testling": {"files": "test/index.js", "browsers": ["ie/6..latest", "firefox/3..latest", "firefox/nightly", "chrome/4..latest", "chrome/canary", "opera/10..latest", "opera/next", "safari/5..latest", "ipad/6..latest", "iphone/6..latest"]}, "_id": "object-keys@0.0.1", "dist": {"shasum": "ab917307b1042981453e094c41049246e99602d6", "tarball": "https://registry.npmjs.org/object-keys/-/object-keys-0.0.1.tgz", "integrity": "sha512-/aM4V/OT388JOkoQJ57Gxeg43O8qI89rybO5CgLo1i4Z1rI/LXnC8RTdZZxmpxC273gOECNPb2qW9jerijQAwA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHpOEk4wRRe8+XOD49ps98iZzl4U4078lTFmqufTj3eQAiBw262wqFt1AZUWcLtoA6F+KvHVEkwE97aLaycw/EDPYw=="}]}, "_from": ".", "_npmVersion": "1.2.15", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "directories": {}, "deprecated": "Please update to the latest object-keys"}, "0.1.0": {"name": "object-keys", "version": "0.1.0", "author": {"name": "<PERSON>"}, "description": "An Object.keys replacement, in case Object.keys is not available. From https://github.com/kriskowal/es5-shim", "license": "MIT", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object-keys.git"}, "keywords": ["Object.keys", "keys", "ES5", "shim"], "devDependencies": {"tap": "~0.4.1", "tape": "~0.3.2"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0", "chrome/22.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/5.0.5..latest", "ipad/6.0..latest", "iphone/6.0..latest"]}, "_id": "object-keys@0.1.0", "dist": {"shasum": "f60a5d0b3f878089a4b9645b26e43df09436dbb8", "tarball": "https://registry.npmjs.org/object-keys/-/object-keys-0.1.0.tgz", "integrity": "sha512-nmv/hFMWJmfEUuMUjE2m2ZDmwi4Q9RDeZto0S04PfD8wnwINgJT5Raib18UT/EAa/A3tIhpEPHewLX83OCRSzQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDvuUuQtK1x8psnRhTMpgczIFTWOSlTu0hV+851vuWrKQIgYdB+mrlzs3w/Bw74j9ju2BZEFbhiDMpRVvbTsHBJodc="}]}, "_from": ".", "_npmVersion": "1.2.14", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "directories": {}, "deprecated": "Please update to the latest object-keys"}, "0.1.1": {"name": "object-keys", "version": "0.1.1", "author": {"name": "<PERSON>"}, "description": "An Object.keys replacement, in case Object.keys is not available. From https://github.com/kriskowal/es5-shim", "license": "MIT", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object-keys.git"}, "keywords": ["Object.keys", "keys", "ES5", "shim"], "dependencies": {"indexof": "~0.0.1", "is-extended": "~0.0.4"}, "devDependencies": {"tap": "~0.4.1", "tape": "~0.3.3"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0", "chrome/22.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/5.0.5..latest", "ipad/6.0..latest", "iphone/6.0..latest"]}, "_id": "object-keys@0.1.1", "dist": {"shasum": "e35f1c8d9cbc5fe503c1b13ad57c334e3f637b3e", "tarball": "https://registry.npmjs.org/object-keys/-/object-keys-0.1.1.tgz", "integrity": "sha512-0YAQMhYdszhy3qw0CZHKp2/+pw0VIBSbb5G5oMItAXW384Qbi6XRg4J8Q9O8kg43WVcFyFUT+GCCTt/rz6890w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBRuwzoWqmGMBA4CCDak783BfOsQ6ycfmHlEnZqC5gWnAiAKsWT0JVhP/+dBICcDXulO75XJTJjG4yGibvL1UpxT6A=="}]}, "_from": ".", "_npmVersion": "1.2.14", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "directories": {}, "deprecated": "Please update to the latest object-keys"}, "0.1.2": {"name": "object-keys", "version": "0.1.2", "author": {"name": "<PERSON>"}, "description": "An Object.keys replacement, in case Object.keys is not available. From https://github.com/kriskowal/es5-shim", "license": "MIT", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object-keys.git"}, "keywords": ["Object.keys", "keys", "ES5", "shim"], "dependencies": {"indexof": "~0.0.1", "is-extended": "~0.0.5"}, "devDependencies": {"tap": "~0.4.1", "tape": "~0.3.3"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0", "chrome/22.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/5.0.5..latest", "ipad/6.0..latest", "iphone/6.0..latest"]}, "_id": "object-keys@0.1.2", "dist": {"shasum": "df74e8662eb0e8b5ee64fc8eda750c2db4debc7b", "tarball": "https://registry.npmjs.org/object-keys/-/object-keys-0.1.2.tgz", "integrity": "sha512-W<PERSON><PERSON>ee5aYXB5Iu7bfsD3wSdO9TaYqwrIfqHWoQQHIx3XbvhslTBAyqY+tOp9DpaNGjE75vM9IhwMFbDcEs0Ntw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHy+GeOlh7SUtU0NRAUk02ZvdJF+bUX7/XN9IrbewUc3AiEAmkuTbEEx+bYIAsEWhbMxFynWo+j5mtMl0weHc2vIqvE="}]}, "_from": ".", "_npmVersion": "1.2.15", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "directories": {}, "deprecated": "Please update to the latest object-keys"}, "0.1.3": {"name": "object-keys", "version": "0.1.3", "author": {"name": "<PERSON>"}, "description": "An Object.keys replacement, in case Object.keys is not available. From https://github.com/kriskowal/es5-shim", "license": "MIT", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object-keys.git"}, "keywords": ["Object.keys", "keys", "ES5", "shim"], "dependencies": {"foreach": "~2.0.1", "indexof": "~0.0.1", "is-extended": "~0.0.5"}, "devDependencies": {"tape": "~0.3.3"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0", "chrome/22.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/5.0.5..latest", "ipad/6.0..latest", "iphone/6.0..latest"]}, "_id": "object-keys@0.1.3", "dist": {"shasum": "201972597dfdbaef2512144a969351b67340966d", "tarball": "https://registry.npmjs.org/object-keys/-/object-keys-0.1.3.tgz", "integrity": "sha512-P40wNJQL4FoACelJjjI0N0iO3oRfiy0Pvym34FvBmJbArXAmIj0u8p8dLPFjKtN3Bikqb2I3kYJLjS2RnIP2KQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEr/gqaTEuK7tXhDHdY5SxqAScNngeW1qXRGkAYdsqLEAiEAtFJBjZIJKSFL9yK6M4lUIVRqPyeLc4o5JBFufjryS9A="}]}, "_from": ".", "_npmVersion": "1.2.14", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "directories": {}, "deprecated": "Please update to the latest object-keys"}, "0.1.4": {"name": "object-keys", "version": "0.1.4", "author": {"name": "<PERSON>"}, "description": "An Object.keys replacement, in case Object.keys is not available. From https://github.com/kriskowal/es5-shim", "license": "MIT", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object-keys.git"}, "keywords": ["Object.keys", "keys", "ES5", "shim"], "dependencies": {"foreach": "~2.0.1", "indexof": "~0.0.1", "is": "~0.2.0"}, "devDependencies": {"tape": "~0.3.3"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0", "chrome/22.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/5.0.5..latest", "ipad/6.0..latest", "iphone/6.0..latest"]}, "_id": "object-keys@0.1.4", "dist": {"shasum": "094b203cdc23c0d61b04f13cc8135fe964cc314a", "tarball": "https://registry.npmjs.org/object-keys/-/object-keys-0.1.4.tgz", "integrity": "sha512-EhLn1BDThRMKDUnB4a9Pu99R0V7FvciLi4M2Y7fyoa/qnl202sd4RhLuYCL6IfR0f133TaWpP4JgNPRpMBac6Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDCCt8XSKs0l1ykra1eRaTbBjfgyO/RAqXJZUWBu0LmewIhAP0IMkVWwACYSzm5FboLyb096r0WXAhEQaQS2m74C3E7"}]}, "_from": ".", "_npmVersion": "1.2.14", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "directories": {}, "deprecated": "Please update to the latest object-keys"}, "0.1.5": {"name": "object-keys", "version": "0.1.5", "author": {"name": "<PERSON>"}, "description": "An Object.keys replacement, in case Object.keys is not available. From https://github.com/kriskowal/es5-shim", "license": "MIT", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object-keys.git"}, "keywords": ["Object.keys", "keys", "ES5", "shim"], "dependencies": {"foreach": "~2.0.1", "indexof": "~0.0.1", "is": "~0.2.1"}, "devDependencies": {"tape": "~0.3.3"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest"]}, "_id": "object-keys@0.1.5", "dist": {"shasum": "ff9b7518e468804c4066ac553c5d452ec8ffbb27", "tarball": "https://registry.npmjs.org/object-keys/-/object-keys-0.1.5.tgz", "integrity": "sha512-FWlklzi/z7zzTVU/hnBrUUyiMRw894gIwpgUCkeFqWSXD/m3y7KUzbcWe6oJWPr+PEZ/ACLa/lDWLIQsYmY0ng==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBdydC523dr2nuIF2D77vsdvVS6m7etNCltX7XRKBrgKAiAzGtetgYZd9SXs1ixPt+EqqAMeabvo92SNOZHVRFqXbQ=="}]}, "_from": ".", "_npmVersion": "1.2.14", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "directories": {}, "deprecated": "Please update to the latest object-keys"}, "0.1.6": {"name": "object-keys", "version": "0.1.6", "author": {"name": "<PERSON>"}, "description": "An Object.keys replacement, in case Object.keys is not available. From https://github.com/kriskowal/es5-shim", "license": "MIT", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object-keys.git"}, "keywords": ["Object.keys", "keys", "ES5", "shim"], "dependencies": {"foreach": "~2.0.1", "indexof": "~0.0.1", "is": "~0.2.2"}, "devDependencies": {"tape": "~0.3.3"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest"]}, "_id": "object-keys@0.1.6", "dist": {"shasum": "2d8d2c3e3f57979d08e56c5a72532750e8fc9aae", "tarball": "https://registry.npmjs.org/object-keys/-/object-keys-0.1.6.tgz", "integrity": "sha512-0EVnJ1F9QV4PMwv/hVwr1Ww/4AmGuHC4Wk1TlkWzUjvxZOZsD/I3jYrP3Cj1wX0C4i5fmAlopJdnQ0XiHOup+Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB42TE8/g3tYNAcNlrGjmxVF0slnQzgQqHN6Ozb32j5cAiAb8eJ+WLFiVR5jEpIgg7FddzSK6CcDOrZS7fhOEaEVAA=="}]}, "_from": ".", "_npmVersion": "1.2.14", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "directories": {}, "deprecated": "Please update to the latest object-keys"}, "0.1.7": {"name": "object-keys", "version": "0.1.7", "author": {"name": "<PERSON>"}, "description": "An Object.keys replacement, in case Object.keys is not available. From https://github.com/kriskowal/es5-shim", "license": "MIT", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object-keys.git"}, "keywords": ["Object.keys", "keys", "ES5", "shim"], "dependencies": {"foreach": "~2.0.1", "indexof": "~0.0.1", "is": "~0.2.3"}, "devDependencies": {"tape": "~0.3.3"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest"]}, "_id": "object-keys@0.1.7", "dist": {"shasum": "fefce99868aeb040f357b3d3aa29ad26ec30bbd2", "tarball": "https://registry.npmjs.org/object-keys/-/object-keys-0.1.7.tgz", "integrity": "sha512-q2+Sfmxqz5jDT7Ri0GZmZog2DCmsYzUo39+ESQFgE6AYSTITCZnrhp5thlTTWKxP0ilN23pvE5voVH2SAQp73Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC0ZbU1lhEEgxMCvgRZhnhW4CeB2kRvMvEeAQGqoxfxHgIgHu6pVbufE1cs9nnihYjZrfi6oEN4sQDd1+IVeMnv9us="}]}, "_from": ".", "_npmVersion": "1.2.14", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "directories": {}, "deprecated": "Please update to the latest object-keys"}, "0.1.8": {"name": "object-keys", "version": "0.1.8", "author": {"name": "<PERSON>"}, "description": "An Object.keys replacement, in case Object.keys is not available. From https://github.com/kriskowal/es5-shim", "license": "MIT", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object-keys.git"}, "keywords": ["Object.keys", "keys", "ES5", "shim"], "dependencies": {"foreach": "~2.0.1", "indexof": "~0.0.1", "is": "~0.2.6"}, "devDependencies": {"tape": "~1.0.2"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest"]}, "_id": "object-keys@0.1.8", "dist": {"shasum": "d40164df81104b0da49edfa6aba9dd29eb480293", "tarball": "https://registry.npmjs.org/object-keys/-/object-keys-0.1.8.tgz", "integrity": "sha512-QVLwfAl2DJtsOVW8BXxa8g9gjzqwAJijFj/hTCOknQ5uIfonbZIEeX+asYCgq93HYkfcMkWL51H6z3XLwALVaw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEstxBPiFXo98Vg3f5JaR5PY3HzLTVOKyPP3xJxIHeCHAiAJIjSWZJuR7zKuGcfALY9bv20LttxAEtWMngbhCcVELA=="}]}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "directories": {}, "deprecated": "Please update to the latest object-keys"}, "0.2.0": {"name": "object-keys", "version": "0.2.0", "author": {"name": "<PERSON>"}, "description": "An Object.keys replacement, in case Object.keys is not available. From https://github.com/kriskowal/es5-shim", "license": "MIT", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object-keys.git"}, "keywords": ["Object.keys", "keys", "ES5", "shim"], "dependencies": {"foreach": "~2.0.1", "indexof": "~0.0.1", "is": "~0.2.6"}, "devDependencies": {"tape": "~1.0.2"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest"]}, "_id": "object-keys@0.2.0", "dist": {"shasum": "cddec02998b091be42bf1035ae32e49f1cb6ea67", "tarball": "https://registry.npmjs.org/object-keys/-/object-keys-0.2.0.tgz", "integrity": "sha512-XODjdR2pBh/1qrjPcbSeSgEtKbYo7LqYNq64/TPuCf7j9SfDD3i21yatKoIy39yIWNvVM59iutfQQpCv1RfFzA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDHqI4i8TCwYU0W7hvKd5jX2WFPHuJ0kESFyw/as3++xgIgaKT/CU6g2wUXjGaGccKcj5U4akUaDasKizs8P3yDewE="}]}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "directories": {}, "deprecated": "Please update to the latest object-keys"}, "0.3.0": {"name": "object-keys", "version": "0.3.0", "author": {"name": "<PERSON>"}, "description": "An Object.keys replacement, in case Object.keys is not available. From https://github.com/kriskowal/es5-shim", "license": "MIT", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object-keys.git"}, "keywords": ["Object.keys", "keys", "ES5", "shim"], "dependencies": {"foreach": "~2.0.3", "is": "~0.2.6"}, "devDependencies": {"tape": "~1.0.2", "indexof": "~0.0.1"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest"]}, "bugs": {"url": "https://github.com/ljharb/object-keys/issues"}, "_id": "object-keys@0.3.0", "dist": {"shasum": "4ce2945fee6669cf98424bbaa0f59c244ff97f1d", "tarball": "https://registry.npmjs.org/object-keys/-/object-keys-0.3.0.tgz", "integrity": "sha512-5NWmqk9N0NPSzhUAjJwjA1fbpYkmCyc3DRpIObOIsOTEz98JZg8fiJUbnxKofPrRXXW/J5Sh0M4pku7my7KHWw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDt2X4Q2m0E/f+ITcYDQdhb9WZQobOe3l/s8X+WttvWEAiAn0ThWjlLuWOUW3FrAinp3k15grW86MXXMLNCLKBiOpg=="}]}, "_from": ".", "_npmVersion": "1.2.21", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "directories": {}, "deprecated": "Please update to the latest object-keys"}, "0.4.0": {"name": "object-keys", "version": "0.4.0", "author": {"name": "<PERSON>"}, "description": "An Object.keys replacement, in case Object.keys is not available. From https://github.com/kriskowal/es5-shim", "license": "MIT", "main": "index.js", "scripts": {"test": "node test/index.js"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object-keys.git"}, "keywords": ["Object.keys", "keys", "ES5", "shim"], "dependencies": {}, "devDependencies": {"foreach": "~2.0.3", "is": "~0.2.6", "tape": "~1.0.4", "indexof": "~0.0.1"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "bugs": {"url": "https://github.com/ljharb/object-keys/issues"}, "_id": "object-keys@0.4.0", "dist": {"shasum": "28a6aae7428dd2c3a92f3d95f21335dd204e0336", "tarball": "https://registry.npmjs.org/object-keys/-/object-keys-0.4.0.tgz", "integrity": "sha512-ncrLw+X55z7bkl5PnUvHwFK9FcGuFYo9gtjws2XtSzL+aZ8tm830P60WJ0dSmFVaSalWieW5MD7kEdnXda9yJw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHUDMLh3fWS5OpydQINZqo8WFrJ3lqEJiDuN+YFRsxG3AiBbbYCG5+dD0UXyu+R6+L4BfEXZJeODELgzbKLRDEEWOw=="}]}, "_from": ".", "_npmVersion": "1.3.5", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "directories": {}, "deprecated": ""}, "0.5.0": {"name": "object-keys", "version": "0.5.0", "author": {"name": "<PERSON>"}, "description": "An Object.keys replacement, in case Object.keys is not available. From https://github.com/es-shims/es5-shim", "license": "MIT", "main": "index.js", "scripts": {"test": "node test/index.js", "coverage": "covert test/index.js", "coverage-quiet": "covert test/index.js --quiet"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object-keys.git"}, "keywords": ["Object.keys", "keys", "ES5", "shim"], "dependencies": {}, "devDependencies": {"foreach": "~2.0.4", "is": "~0.2.7", "tape": "~2.3.2", "indexof": "~0.0.1", "covert": "~0.3.0"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "bugs": {"url": "https://github.com/ljharb/object-keys/issues"}, "homepage": "https://github.com/ljharb/object-keys", "_id": "object-keys@0.5.0", "dist": {"shasum": "09e211f3e00318afc4f592e36e7cdc10d9ad7293", "tarball": "https://registry.npmjs.org/object-keys/-/object-keys-0.5.0.tgz", "integrity": "sha512-2GU36PPj0BVaGl9JDw1zY5vkLMV1hQ1QtI+PoBq7f5bZKY2j/7IO0uQDv0UcuBhimMYnditq7dz+uO9C1TXV4w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDMwauwpEiIxU1RlG+eAIRnOLrboadeDQRORnvEQufqswIhANVI50TQxUwOhs2291FQ2NIdlE1uCKDjOx8jTVsGXZEn"}]}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "directories": {}}, "0.5.1": {"name": "object-keys", "version": "0.5.1", "author": {"name": "<PERSON>"}, "description": "An Object.keys replacement, in case Object.keys is not available. From https://github.com/es-shims/es5-shim", "license": "MIT", "main": "index.js", "scripts": {"test": "node test/index.js", "coverage": "covert test/index.js", "coverage-quiet": "covert test/index.js --quiet"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object-keys.git"}, "keywords": ["Object.keys", "keys", "ES5", "shim"], "dependencies": {}, "devDependencies": {"foreach": "~2.0.4", "is": "~0.3.0", "tape": "~2.10.2", "indexof": "~0.0.1", "covert": "~0.3.1"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "bugs": {"url": "https://github.com/ljharb/object-keys/issues"}, "homepage": "https://github.com/ljharb/object-keys", "_id": "object-keys@0.5.1", "dist": {"shasum": "0eb20ffa0ce7c01977648681b42c515f297d2cc1", "tarball": "https://registry.npmjs.org/object-keys/-/object-keys-0.5.1.tgz", "integrity": "sha512-VVh5OqHlY0N4Hueq9KteojSoj8BmEZeKC+nFyAmQFGF37dJSbcFB4jNhV7+6Xnn6t4t3jh0P0Cuy0hEA+xq+Mg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDoseRCnmntQ8ISi56+7YstplMr/rq01BE5OsLrcg/b6gIhAJKIVTCp4DmdXm8LavjKRr4lG/KH6m/RyVA72NGkvDSc"}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "directories": {}}, "0.6.0": {"name": "object-keys", "version": "0.6.0", "author": {"name": "<PERSON>"}, "description": "An Object.keys replacement, in case Object.keys is not available. From https://github.com/es-shims/es5-shim", "license": "MIT", "main": "index.js", "scripts": {"test": "npm run lint && node test/index.js", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet", "lint": "jscs test/*.js *.js"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object-keys.git"}, "keywords": ["Object.keys", "keys", "ES5", "shim"], "dependencies": {}, "devDependencies": {"foreach": "~2.0.4", "is": "~0.3.0", "tape": "~2.13.3", "indexof": "~0.0.1", "covert": "~0.4.0", "jscs": "~1.5.8"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "gitHead": "3cbf74b330bb04f263a96d59925db5704c08968c", "bugs": {"url": "https://github.com/ljharb/object-keys/issues"}, "homepage": "https://github.com/ljharb/object-keys", "_id": "object-keys@0.6.0", "_shasum": "4638690dfaf1e65a63d43b5855d2f6ce04aeef6d", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "dist": {"shasum": "4638690dfaf1e65a63d43b5855d2f6ce04aeef6d", "tarball": "https://registry.npmjs.org/object-keys/-/object-keys-0.6.0.tgz", "integrity": "sha512-NwTyBxMHbTVCd46WsQlY4WMwYoJ+PXkIkU6x/S22usMJQewtKMrwPAV9jtB6HBXnL4+EzaXQrtllK0MPl+V4PQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEAekrpsVGBhFATo6EM1rcjVMSHjzwnEm8OSWZY5YhYqAiEA1YnRXNwRpg9sHlQweTFu1/6zpLR4rTQ50u+odWruJ+o="}]}, "directories": {}}, "0.6.1": {"name": "object-keys", "version": "0.6.1", "author": {"name": "<PERSON>"}, "description": "An Object.keys replacement, in case Object.keys is not available. From https://github.com/es-shims/es5-shim", "license": "MIT", "main": "index.js", "scripts": {"test": "npm run lint && node test/index.js", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet", "lint": "jscs test/*.js *.js"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object-keys.git"}, "keywords": ["Object.keys", "keys", "ES5", "shim"], "dependencies": {}, "devDependencies": {"foreach": "~2.0.4", "is": "~2.0.0", "tape": "~2.14.0", "indexof": "~0.0.1", "covert": "~1.0.0", "jscs": "~1.5.8"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "gitHead": "cfa534edc801eef5a3fd01512b30b025d177a79a", "bugs": {"url": "https://github.com/ljharb/object-keys/issues"}, "homepage": "https://github.com/ljharb/object-keys", "_id": "object-keys@0.6.1", "_shasum": "ed8d052b3662b093c9ee00152c259815c0db4d3c", "_from": ".", "_npmVersion": "1.4.23", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "dist": {"shasum": "ed8d052b3662b093c9ee00152c259815c0db4d3c", "tarball": "https://registry.npmjs.org/object-keys/-/object-keys-0.6.1.tgz", "integrity": "sha512-yFH+vVBczUKglNkPAb96wIWXv1AqdR4PCdoL8fYt6+uqm/Ucn4G7NVOgI54GG6Pai8yswIqzZIz0kLq4/3egQQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDhFPCvJ7VZ/jc+5VfjHDrTEIbXXMDA+p7qQPbB7D7QhgIhAIpfJj4sqHZfgrQO3bYBRwxqahD1d23Zea/rSaIZhqJ+"}]}, "directories": {}}, "1.0.0": {"name": "object-keys", "version": "1.0.0", "author": {"name": "<PERSON>"}, "description": "An Object.keys replacement, in case Object.keys is not available. From https://github.com/es-shims/es5-shim", "license": "MIT", "main": "index.js", "scripts": {"test": "npm run lint && node test/index.js", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet", "lint": "jscs test/*.js *.js"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object-keys.git"}, "keywords": ["Object.keys", "keys", "ES5", "shim"], "dependencies": {}, "devDependencies": {"foreach": "~2.0.4", "is": "~2.0.0", "tape": "~2.14.0", "indexof": "~0.0.1", "covert": "~1.0.0", "jscs": "~1.5.8"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "gitHead": "f78356a5eda9b059acdc841607edbd3940aed477", "bugs": {"url": "https://github.com/ljharb/object-keys/issues"}, "homepage": "https://github.com/ljharb/object-keys", "_id": "object-keys@1.0.0", "_shasum": "1b66cc8cafc27391944098216726f746b15c2a30", "_from": ".", "_npmVersion": "1.4.23", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "dist": {"shasum": "1b66cc8cafc27391944098216726f746b15c2a30", "tarball": "https://registry.npmjs.org/object-keys/-/object-keys-1.0.0.tgz", "integrity": "sha512-7zE2Pyy6jZ30PT8LSB/J+WfBvd8gw6PClm9Ilhq/S42rZ32NiDgBD0GtBDcmeObLtRIAC087WNyCW4QLAF/F1A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD4P78vN3qENInRoJidkqsanNRVgDGq1o1IDbclEaeAugIgGF9eS40md3HbBTo2TP+LbsCZhL+mjAqG91O7hwXfVPY="}]}, "directories": {}}, "1.0.1": {"name": "object-keys", "version": "1.0.1", "author": {"name": "<PERSON>"}, "description": "An Object.keys replacement, in case Object.keys is not available. From https://github.com/es-shims/es5-shim", "license": "MIT", "main": "index.js", "scripts": {"test": "npm run lint && node test/index.js", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet", "lint": "jscs test/*.js *.js"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object-keys.git"}, "keywords": ["Object.keys", "keys", "ES5", "shim"], "dependencies": {}, "devDependencies": {"foreach": "~2.0.4", "is": "~2.0.1", "tape": "~2.14.0", "indexof": "~0.0.1", "covert": "~1.0.0", "jscs": "~1.6.1"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "gitHead": "2ecbaaa0405c2f03e8b669ccf4b70376318a8f8b", "bugs": {"url": "https://github.com/ljharb/object-keys/issues"}, "homepage": "https://github.com/ljharb/object-keys", "_id": "object-keys@1.0.1", "_shasum": "55802e85842c26bbb5ebbc157abf3be302569ba8", "_from": ".", "_npmVersion": "1.4.23", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "dist": {"shasum": "55802e85842c26bbb5ebbc157abf3be302569ba8", "tarball": "https://registry.npmjs.org/object-keys/-/object-keys-1.0.1.tgz", "integrity": "sha512-DsJ69TA3wPICBmxYj6rij6uGKvKb9s2mtebzhuN/eI1GabJ3xC7fZ7PWjW0GS06hSclD0GxKGGAHQo5P7R2ZTg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD4gYBznqPY/77jmrmzAiN5nRqHR25mrZuveDAAkyBi/wIhANnwIwT2H5eNkTIWUt3c+j4p5ovDyUM83vj0pvCHuFBL"}]}, "directories": {}}, "1.0.2": {"name": "object-keys", "version": "1.0.2", "author": {"name": "<PERSON>"}, "description": "An Object.keys replacement, in case Object.keys is not available. From https://github.com/es-shims/es5-shim", "license": "MIT", "main": "index.js", "scripts": {"test": "npm run lint && node test/index.js && npm run security", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet", "lint": "npm run jscs && npm run eslint", "jscs": "jscs test/*.js *.js", "eslint": "eslint test/*.js *.js", "eccheck": "editorconfig-tools check *.js **/*.js > /dev/null", "security": "nsp package"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object-keys.git"}, "keywords": ["Object.keys", "keys", "ES5", "shim"], "dependencies": {}, "devDependencies": {"foreach": "~2.0.5", "is": "~2.2.0", "tape": "~3.0.3", "indexof": "~0.0.1", "covert": "1.0.0", "jscs": "~1.9.0", "editorconfig-tools": "~0.0.1", "nsp": "~0.5.2", "eslint": "~0.10.2"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "gitHead": "06f2d46a85a0be12fc9e0377e3ce7bef32be5eb3", "bugs": {"url": "https://github.com/ljharb/object-keys/issues"}, "homepage": "https://github.com/ljharb/object-keys", "_id": "object-keys@1.0.2", "_shasum": "810205bc58367a1d9dcf9e8b7b8c099ef2503c6c", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "dist": {"shasum": "810205bc58367a1d9dcf9e8b7b8c099ef2503c6c", "tarball": "https://registry.npmjs.org/object-keys/-/object-keys-1.0.2.tgz", "integrity": "sha512-QaJ3L+WfJ2mCirdIvDbXRW8q76+WnsITenRbpAAJ2Z/fPcKaXvRAn94rv1YzwUGqxj/m08vu3HBvR6WdxXXRsw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAh4SRfmAsWSFsJGW/zwtEkL5i6WpjFvxOkmwnfDW/LTAiBr8+G5luLEkszDkl+ANwlTeCyO/PceL8aRv/UO/XqUtw=="}]}, "directories": {}}, "1.0.3": {"name": "object-keys", "version": "1.0.3", "author": {"name": "<PERSON>"}, "description": "An Object.keys replacement, in case Object.keys is not available. From https://github.com/es-shims/es5-shim", "license": "MIT", "main": "index.js", "scripts": {"test": "npm run lint && node test/index.js && npm run security", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet", "lint": "npm run jscs && npm run eslint", "jscs": "jscs test/*.js *.js", "eslint": "eslint test/*.js *.js", "eccheck": "editorconfig-tools check *.js **/*.js > /dev/null", "security": "nsp package"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object-keys.git"}, "keywords": ["Object.keys", "keys", "ES5", "shim"], "dependencies": {}, "devDependencies": {"foreach": "~2.0.5", "is": "~2.2.0", "tape": "~3.0.3", "indexof": "~0.0.1", "covert": "1.0.0", "jscs": "~1.9.0", "editorconfig-tools": "~0.0.1", "nsp": "~0.5.2", "eslint": "~0.11.0"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "gitHead": "f0fc8ccdf81843fa7aa88c85777cf717c3ead129", "bugs": {"url": "https://github.com/ljharb/object-keys/issues"}, "homepage": "https://github.com/ljharb/object-keys", "_id": "object-keys@1.0.3", "_shasum": "1b679dbec65103da488edb32f782bd9a15e3de0a", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "dist": {"shasum": "1b679dbec65103da488edb32f782bd9a15e3de0a", "tarball": "https://registry.npmjs.org/object-keys/-/object-keys-1.0.3.tgz", "integrity": "sha512-C9AHglIN4DeikXJitZAmcls7Ics4QJr0QnVXFtK4wVly8zo0udlW96Hfw0kLQ0LqiE21Z2HgBMIS7C6/s4L2Tg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGYBo/Zp8ilkQTBiGuDQvrpFHmLCZGxdimx6CQPuVK4PAiEAofwl6l/SVKlk89+QpAy6VRVczBPULX48M5hGH78V7Vc="}]}, "directories": {}}, "1.0.4": {"name": "object-keys", "version": "1.0.4", "author": {"name": "<PERSON>"}, "description": "An Object.keys replacement, in case Object.keys is not available. From https://github.com/es-shims/es5-shim", "license": "MIT", "main": "index.js", "scripts": {"test": "npm run lint && node test/index.js && npm run security", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet", "lint": "npm run jscs && npm run eslint", "jscs": "jscs test/*.js *.js", "eslint": "eslint test/*.js *.js", "eccheck": "editorconfig-tools check *.js **/*.js > /dev/null", "security": "nsp package"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object-keys.git"}, "keywords": ["Object.keys", "keys", "ES5", "shim"], "dependencies": {}, "devDependencies": {"foreach": "^2.0.5", "is": "^3.0.1", "tape": "^4.0.0", "indexof": "^0.0.1", "covert": "^1.1.0", "jscs": "^1.13.1", "editorconfig-tools": "^0.1.1", "nsp": "^1.0.1", "eslint": "^0.21.0"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "gitHead": "fc869b3088d6047bcbf42e534304ffe034b06cb0", "bugs": {"url": "https://github.com/ljharb/object-keys/issues"}, "homepage": "https://github.com/ljharb/object-keys#readme", "_id": "object-keys@1.0.4", "_shasum": "b115f96a7ee837df1517fbc5bd91ea965e37685c", "_from": ".", "_npmVersion": "2.9.0", "_nodeVersion": "2.0.2", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "dist": {"shasum": "b115f96a7ee837df1517fbc5bd91ea965e37685c", "tarball": "https://registry.npmjs.org/object-keys/-/object-keys-1.0.4.tgz", "integrity": "sha512-+MtQIw3zdFntcjAKeWGPRbCj0SZeCSN1Yhp1jAI1GmPgF6wCHTJkhJgfPE3kHgryFpX2MgFWQLcKsqHlSlPD9A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDKrpjpmWkxUnjvqfhVyWqDoQh7rExWokqaM7GWI3do6wIhAONj4OP7k0W21ye/Mzi92MX8ageuQydTsWFK4cB75Zik"}]}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "directories": {}}, "1.0.5": {"name": "object-keys", "version": "1.0.5", "author": {"name": "<PERSON>"}, "description": "An Object.keys replacement, in case Object.keys is not available. From https://github.com/es-shims/es5-shim", "license": "MIT", "main": "index.js", "scripts": {"test": "npm run lint && node test/index.js && npm run security", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet", "lint": "npm run jscs && npm run eslint", "jscs": "jscs test/*.js *.js", "eslint": "eslint test/*.js *.js", "eccheck": "editorconfig-tools check *.js **/*.js > /dev/null", "security": "nsp package"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object-keys.git"}, "keywords": ["Object.keys", "keys", "ES5", "shim"], "dependencies": {}, "devDependencies": {"foreach": "^2.0.5", "is": "^3.0.1", "tape": "^4.0.0", "indexof": "^0.0.1", "covert": "^1.1.0", "jscs": "^1.13.1", "editorconfig-tools": "^0.1.1", "nsp": "^1.0.3", "eslint": "^0.24.0"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "gitHead": "a6fb624febfdbde087b5637bedd5233054520b18", "bugs": {"url": "https://github.com/ljharb/object-keys/issues"}, "homepage": "https://github.com/ljharb/object-keys#readme", "_id": "object-keys@1.0.5", "_shasum": "84fe12516867496e97796a49db0a89399053fe06", "_from": ".", "_npmVersion": "2.11.3", "_nodeVersion": "2.3.2", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "dist": {"shasum": "84fe12516867496e97796a49db0a89399053fe06", "tarball": "https://registry.npmjs.org/object-keys/-/object-keys-1.0.5.tgz", "integrity": "sha512-ads8edXgDSXcILPLzQa0i8HaXMSPoCj1SYW8C+W+fL8cTIcpxp8M3/wFu4ODfegdiKP9LEatqLbcd7noEtoL2g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAdMlFebBqVB9aq9/VQPFrEI72Ai91euXsNekOZS67lKAiB6/Y3cE8bKzOVtc/erkWzyFt9rjyc4HBMVfRy2SN9V2A=="}]}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "directories": {}}, "1.0.6": {"name": "object-keys", "version": "1.0.6", "author": {"name": "<PERSON>"}, "description": "An Object.keys replacement, in case Object.keys is not available. From https://github.com/es-shims/es5-shim", "license": "MIT", "main": "index.js", "scripts": {"test": "npm run lint && node test/index.js && npm run security", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet", "lint": "npm run jscs && npm run eslint", "jscs": "jscs test/*.js *.js", "eslint": "eslint test/*.js *.js", "eccheck": "editorconfig-tools check *.js **/*.js > /dev/null", "security": "nsp package"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object-keys.git"}, "keywords": ["Object.keys", "keys", "ES5", "shim"], "dependencies": {}, "devDependencies": {"foreach": "^2.0.5", "is": "^3.0.1", "tape": "^4.0.0", "indexof": "^0.0.1", "covert": "^1.1.0", "jscs": "^1.13.1", "editorconfig-tools": "^0.1.1", "nsp": "^1.0.3", "eslint": "^0.24.0"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "gitHead": "3b0fbe74b40b5d78661461339f09a82f45a0a345", "bugs": {"url": "https://github.com/ljharb/object-keys/issues"}, "homepage": "https://github.com/ljharb/object-keys#readme", "_id": "object-keys@1.0.6", "_shasum": "f910c99bb3f57d8ba29b6580e1508eb0ebbfc177", "_from": ".", "_npmVersion": "2.11.3", "_nodeVersion": "2.3.3", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "dist": {"shasum": "f910c99bb3f57d8ba29b6580e1508eb0ebbfc177", "tarball": "https://registry.npmjs.org/object-keys/-/object-keys-1.0.6.tgz", "integrity": "sha512-JFO9tB3N/R17IA/IVKb3K0amIIpaR5T7CSg9z47uRXOFv9Kw1LOm1t3NB6FjosNIuKqNwpExODZqNnJb8zIZgQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHoYa/sXYRU7F0BxiOvK1r/U1E2lj0iXpqZwIHQMjc29AiA66y8fVi2dNIyHyihDxm0gL/8pAm04MHpq25c6K5c33g=="}]}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "directories": {}}, "1.0.7": {"name": "object-keys", "version": "1.0.7", "author": {"name": "<PERSON>"}, "description": "An Object.keys replacement, in case Object.keys is not available. From https://github.com/es-shims/es5-shim", "license": "MIT", "main": "index.js", "scripts": {"test": "npm run lint && node test/index.js && npm run security", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet", "lint": "npm run jscs && npm run eslint", "jscs": "jscs test/*.js *.js", "eslint": "eslint test/*.js *.js", "eccheck": "editorconfig-tools check *.js **/*.js > /dev/null", "security": "nsp package"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object-keys.git"}, "keywords": ["Object.keys", "keys", "ES5", "shim"], "dependencies": {}, "devDependencies": {"foreach": "^2.0.5", "is": "^3.0.1", "tape": "^4.0.0", "indexof": "^0.0.1", "covert": "^1.1.0", "jscs": "^1.13.1", "editorconfig-tools": "^0.1.1", "nsp": "^1.0.3", "eslint": "^1.0.0-rc-1"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "gitHead": "c0c183e0aaed86487218f46127fcebec9258e84e", "bugs": {"url": "https://github.com/ljharb/object-keys/issues"}, "homepage": "https://github.com/ljharb/object-keys#readme", "_id": "object-keys@1.0.7", "_shasum": "e7d117261139d6acac8f0afabf261d700ebb0b93", "_from": ".", "_npmVersion": "2.13.0", "_nodeVersion": "2.4.0", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "dist": {"shasum": "e7d117261139d6acac8f0afabf261d700ebb0b93", "tarball": "https://registry.npmjs.org/object-keys/-/object-keys-1.0.7.tgz", "integrity": "sha512-SLdJAA8lTumufd2VJDOEXwfb81eE/ujQccVmFsofTnoPv1RvHqSlrMjDkq06lTaqnJxCDaY3d8rUwUJIeFk5sA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCkZcFwsc8+AngNfDPYrHnGBGkQHFSyW+hmnv8O33Ng3AIgSwHqYxg+rHqOlyoYbs/OqwzctpcSXfGjgbwpsa34dDw="}]}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "directories": {}}, "1.0.8": {"name": "object-keys", "version": "1.0.8", "author": {"name": "<PERSON>"}, "description": "An Object.keys replacement, in case Object.keys is not available. From https://github.com/es-shims/es5-shim", "license": "MIT", "main": "index.js", "scripts": {"test": "npm run lint && node test/index.js && npm run security", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet", "lint": "npm run jscs && npm run eslint", "jscs": "jscs test/*.js *.js", "eslint": "eslint test/*.js *.js", "eccheck": "editorconfig-tools check *.js **/*.js > /dev/null", "security": "nsp package"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object-keys.git"}, "keywords": ["Object.keys", "keys", "ES5", "shim"], "dependencies": {}, "devDependencies": {"foreach": "^2.0.5", "is": "^3.1.0", "tape": "^4.2.1", "indexof": "^0.0.1", "covert": "^1.1.0", "jscs": "^2.3.1", "editorconfig-tools": "^0.1.1", "nsp": "^1.1.0", "eslint": "^1.6.0", "@ljharb/eslint-config": "^1.3.0"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "gitHead": "f094a4832583959d0a0a132ea80efa2f44a5d58e", "bugs": {"url": "https://github.com/ljharb/object-keys/issues"}, "homepage": "https://github.com/ljharb/object-keys#readme", "_id": "object-keys@1.0.8", "_shasum": "9a71ce236e200a943d7fbddba25332fba057c205", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.1", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "dist": {"shasum": "9a71ce236e200a943d7fbddba25332fba057c205", "tarball": "https://registry.npmjs.org/object-keys/-/object-keys-1.0.8.tgz", "integrity": "sha512-yMyMdHyEjnPMnRpKnwOQLtTcS/2DQCItvwFh/A0RFvorh1aWqsIO46ZzfkaT0CmPXcKjCtrq7DhZo+unsR99hA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFHd2Fd8fYxiukgf0PCCQ4pAuKxhEwsecMScXYwTs/ntAiEA4b6t2m3zTXFek1FJZm3TEuhYZFwPhYCknyORUWHNXXo="}]}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "directories": {}}, "1.0.9": {"name": "object-keys", "version": "1.0.9", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "***************"}], "description": "An Object.keys replacement, in case Object.keys is not available. From https://github.com/es-shims/es5-shim", "license": "MIT", "main": "index.js", "scripts": {"test": "npm run lint && node test/index.js && npm run security", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet", "lint": "npm run jscs && npm run eslint", "jscs": "jscs test/*.js *.js", "eslint": "eslint test/*.js *.js", "eccheck": "editorconfig-tools check *.js **/*.js > /dev/null", "security": "nsp package"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object-keys.git"}, "keywords": ["Object.keys", "keys", "ES5", "shim"], "dependencies": {}, "devDependencies": {"foreach": "^2.0.5", "is": "^3.1.0", "tape": "^4.2.1", "indexof": "^0.0.1", "covert": "^1.1.0", "jscs": "^2.3.4", "editorconfig-tools": "^0.1.1", "nsp": "^1.1.0", "eslint": "^1.7.2", "@ljharb/eslint-config": "^1.4.1"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "gitHead": "e4331f920ff49824ad999b3449005349e31139f9", "bugs": {"url": "https://github.com/ljharb/object-keys/issues"}, "homepage": "https://github.com/ljharb/object-keys#readme", "_id": "object-keys@1.0.9", "_shasum": "cabb1202d9a7af29b50edface8094bb46da5ea21", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.1", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "dist": {"shasum": "cabb1202d9a7af29b50edface8094bb46da5ea21", "tarball": "https://registry.npmjs.org/object-keys/-/object-keys-1.0.9.tgz", "integrity": "sha512-xRGFTKkyFuP9AilRkEw4KfMPqaD9spcc6PVVPiOxAau61l+m/4zHUW6crXGtSt8lBfXD2vgnqNFFY8cr8NOBTQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBJQxUXhh6hPZURj6mH0fOxfW7ePLUq0TXl/tfNBFT3aAiAsoqWESRjpTfRmFkKnYvuJgqI1ovXv42EHD2LzxAs71A=="}]}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "directories": {}}, "1.0.10": {"name": "object-keys", "version": "1.0.10", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "***************"}], "description": "An Object.keys replacement, in case Object.keys is not available. From https://github.com/es-shims/es5-shim", "license": "MIT", "main": "index.js", "scripts": {"pretest": "npm run --silent lint", "test": "npm run --silent tests-only", "posttest": "npm run --silent security", "tests-only": "node test/index.js", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet", "lint": "npm run --silent jscs && npm run --silent eslint", "jscs": "jscs test/*.js *.js", "eslint": "eslint test/*.js *.js", "security": "nsp check"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object-keys.git"}, "keywords": ["Object.keys", "keys", "ES5", "shim"], "dependencies": {}, "devDependencies": {"foreach": "^2.0.5", "is": "^3.1.0", "tape": "^4.6.0", "indexof": "^0.0.1", "covert": "^1.1.0", "jscs": "^3.0.6", "nsp": "^2.5.0", "eslint": "^3.0.0", "@ljharb/eslint-config": "^6.0.0"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "gitHead": "a12ae2c01a443afb43414ab844175d2b6d5cd50a", "bugs": {"url": "https://github.com/ljharb/object-keys/issues"}, "homepage": "https://github.com/ljharb/object-keys#readme", "_id": "object-keys@1.0.10", "_shasum": "57e67f7041b66d145c45136fa8040a32717f7465", "_from": ".", "_npmVersion": "3.9.5", "_nodeVersion": "6.2.2", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "dist": {"shasum": "57e67f7041b66d145c45136fa8040a32717f7465", "tarball": "https://registry.npmjs.org/object-keys/-/object-keys-1.0.10.tgz", "integrity": "sha512-fKnqZ/+BvdAsCto14RQRo1q0W9ObXswVgq2Vc/y/OQXfGVom9jEJ193KpHjgkO7QJNCxy8hBWTDBYUsSBExYFA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCUJa2v4dH/fEuWBmVFTYGyt6k+uRH9k63SnIhS07UPggIgarq6DuufB4ttn5xtTjnxChN0qzqHsyhVN2mhkaF1IBk="}]}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/object-keys-1.0.10.tgz_1467655315616_0.8326317083556205"}, "directories": {}}, "1.0.11": {"name": "object-keys", "version": "1.0.11", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "***************"}], "description": "An Object.keys replacement, in case Object.keys is not available. From https://github.com/es-shims/es5-shim", "license": "MIT", "main": "index.js", "scripts": {"pretest": "npm run --silent lint", "test": "npm run --silent tests-only", "posttest": "npm run --silent security", "tests-only": "node test/index.js", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet", "lint": "npm run --silent jscs && npm run --silent eslint", "jscs": "jscs test/*.js *.js", "eslint": "eslint test/*.js *.js", "security": "nsp check"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object-keys.git"}, "keywords": ["Object.keys", "keys", "ES5", "shim"], "dependencies": {}, "devDependencies": {"foreach": "^2.0.5", "is": "^3.1.0", "tape": "^4.6.0", "indexof": "^0.0.1", "covert": "^1.1.0", "jscs": "^3.0.6", "nsp": "^2.5.0", "eslint": "^3.0.0", "@ljharb/eslint-config": "^6.0.0"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "gitHead": "3f869cc4b9f0f0489b2af7e80964f90d6c4403a4", "bugs": {"url": "https://github.com/ljharb/object-keys/issues"}, "homepage": "https://github.com/ljharb/object-keys#readme", "_id": "object-keys@1.0.11", "_shasum": "c54601778ad560f1142ce0e01bcca8b56d13426d", "_from": ".", "_npmVersion": "3.9.5", "_nodeVersion": "6.2.2", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "dist": {"shasum": "c54601778ad560f1142ce0e01bcca8b56d13426d", "tarball": "https://registry.npmjs.org/object-keys/-/object-keys-1.0.11.tgz", "integrity": "sha512-I0jUsqFqmQFOIhQQFlW8QDuX3pVqUWkiiavYj8+TBiS7m+pM9hPCxSnYWqL1hHMBb7BbQ2HidT+6CZ8/BT/ilw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGVatYL5nqFjnyTPO0/FYHebFDZUNL6H4evuOwJXOd20AiAVQtHX+GpfjVa90v7F8y+Z0Nkf/bKGSVeNf/Sqys+gRg=="}]}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/object-keys-1.0.11.tgz_1467740975903_0.8028358130250126"}, "directories": {}}, "1.0.12": {"name": "object-keys", "version": "1.0.12", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "***************"}], "description": "An Object.keys replacement, in case Object.keys is not available. From https://github.com/es-shims/es5-shim", "license": "MIT", "main": "index.js", "scripts": {"pretest": "npm run --silent lint", "test": "npm run --silent tests-only", "posttest": "npm run --silent security", "tests-only": "node test/index.js", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet", "lint": "npm run --silent jscs && npm run --silent eslint", "jscs": "jscs test/*.js *.js", "eslint": "eslint test/*.js *.js", "security": "nsp check"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object-keys.git"}, "keywords": ["Object.keys", "keys", "ES5", "shim"], "dependencies": {}, "devDependencies": {"@ljharb/eslint-config": "^12.2.1", "covert": "^1.1.0", "eslint": "^4.19.1", "foreach": "^2.0.5", "indexof": "^0.0.1", "is": "^3.2.1", "jscs": "^3.0.7", "nsp": "^3.2.1", "tape": "^4.9.1"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "gitHead": "e3acd057c5b7be1029b3b9f6f69133292d77d558", "bugs": {"url": "https://github.com/ljharb/object-keys/issues"}, "homepage": "https://github.com/ljharb/object-keys#readme", "_id": "object-keys@1.0.12", "_npmVersion": "6.1.0", "_nodeVersion": "10.4.1", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-FTMyFUm2wBcGHnH2eXmz7tC6IwlqQZ6mVZ+6dm6vZ4IQIHjs6FdNsQBuKGPuUUUY6NfJw2PshC08Tn6LzLDOag==", "shasum": "09c53855377575310cca62f55bb334abff7b3ed2", "tarball": "https://registry.npmjs.org/object-keys/-/object-keys-1.0.12.tgz", "fileCount": 11, "unpackedSize": 28233, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbKChkCRA9TVsSAnZWagAAVoIP/1jqNQKOVOx4jOpaSivw\nSRxdpzt236t7t9D5YnDgXhT5hrfbfajBz59CVtikezyYS3+ccurC/M2fTHno\nAT8VGxLmemptEoH7woqX27cFdWDFqyMlyfYZkC29w59+cXq44+J3+VFtyd8s\nV09lwj934D/DxdSCKZ/BVuZoffigow37yg7kIC9+VVS0em2XG3W633V8LQAF\nrRiUVSk0ne/BlO1TWV5fTmPQwranmUMnodZAqarVn2/vl0wN8rCTM9qGHdGH\nYWJNQC0ed73ZWOJN+C+OeQqtRdmjS/s5MbLrnMC7JdSQqEDFr6cuLf6TXYa8\nQmy4MCwN7IN1+XeUbDLsOQ1NdjIg9TVlybL5HjKiBjL5FYcjiZQHvtLYTOLa\n/x4eteDcVF8WObCLsUfrB3XuwH2sJX1tACds7IalOS0WLR2bHeBGjejQFyKK\n6k8strtCWMxaWt/nRSTOpZZfMz/HMtHmqVJ3C/VZGYvoexpt6EXqZm4Yemtx\n7AS82sEnfnKF92m/EXZbdP5Gz0fnAksKtzOncsFCOk7qomkD0PLNZkhIadfX\nWTOz9FW+gQNA+im76POpLk8EwQBFYIfTQesLVYB243Z3jH6O5EuTTkzclkWU\nVNXHIoouGL3S+1gPactA2lr6PM4G0hTkco98HSEvHMZpdgoHtz4Jx8xMtX6N\nGJLk\r\n=/f2l\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDBTqK5jg1fMSZC7viYJn8AgFqfaNKFJrUlPYMQVnIReQIgUtgVmrbrFyXy3Qupn7eBPqwBkzKQXN6D+aQtkn5/tEk="}]}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/object-keys_1.0.12_1529358434802_0.4383878957043432"}, "_hasShrinkwrap": false}, "1.1.0": {"name": "object-keys", "version": "1.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "***************"}], "description": "An Object.keys replacement, in case Object.keys is not available. From https://github.com/es-shims/es5-shim", "license": "MIT", "main": "index.js", "scripts": {"pretest": "npm run --silent lint", "test": "npm run --silent tests-only", "posttest": "npm run --silent audit", "tests-only": "node test/index.js", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet", "lint": "eslint .", "preaudit": "npm install --package-lock --package-lock-only", "audit": "npm audit", "postaudit": "rm package-lock.json"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object-keys.git"}, "keywords": ["Object.keys", "keys", "ES5", "shim"], "dependencies": {}, "devDependencies": {"@ljharb/eslint-config": "^13.1.1", "covert": "^1.1.1", "eslint": "^5.13.0", "foreach": "^2.0.5", "indexof": "^0.0.1", "is": "^3.3.0", "tape": "^4.9.2"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "gitHead": "abd4ff039708a0166a57388b348730cbda4a1593", "bugs": {"url": "https://github.com/ljharb/object-keys/issues"}, "homepage": "https://github.com/ljharb/object-keys#readme", "_id": "object-keys@1.1.0", "_npmVersion": "6.5.0", "_nodeVersion": "11.8.0", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-6OO5X1+2tYkNyNEx6TsCxEqFfRWaqx6EtMiSbGrw8Ob8v9Ne+Hl8rBAgLBZn5wjEz3s/s6U1WXFUFOcxxAwUpg==", "shasum": "11bd22348dd2e096a045ab06f6c85bcc340fa032", "tarball": "https://registry.npmjs.org/object-keys/-/object-keys-1.1.0.tgz", "fileCount": 11, "unpackedSize": 26395, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcYQZGCRA9TVsSAnZWagAAu9oP/3ed1S6D9BtHLFImT804\nudV4Mm/anfUE0jBXkJXLab4+qVIgkkqQOnEIg/Wl1ea/UHN/r21cRAaxVcdx\nqH6c5bcRpIr5gWNpcXnCgUVzOZHz2woa0jgZ4YQsAvt67m1lPAZBSppp98tx\nvw//RLPRQB0MNppFXRMLQEMABvIyP7bvNy6wK/SHp21hLuxzKdEmkwYwgKDN\nlhKdtpJQDrRjAmD6w2i8GW71S4K+Kis/ugzDGYX5eM4iwxJYQaKhtsgRJe34\nohUKHsVDe54wAYo4ZfJp+oJFLdJoY5DtYZM8VRkFlIya8X339oietwezlVoB\n0t3/8LuwJJZrG3tprTl6ek4d2AFL2Mf/xJhxSwKLY8B6UpFD+yNn4PuUt70u\ndea9T4zC9KE0swRdxLNRkKDTexCMlq3I3LZ28rH5MyPfcgdZMs1v9tGlaWhL\n7lXxQ5DIq/MSoMDKgOh8T0OBbvapnIfb6f1cqJgreZ0W0mpTm2Fu6joBfJKp\n7rp4erjunUXk4vNTsUxB9R/DMJgiefPRf7XPMG7evhO+MLlh3380Hf5DTf6E\nAqLqOZnAYLBzjigMxK8C3F0jxLVTFuwjA0Z3qejpWgwdl7RF7qyxekeSI1/g\n02ewe6HLdJilSbcev4i1zF8IKjGXFMoCWkVhpiz/q4DlNJbIv25C1482NbrF\nw+qE\r\n=/QeE\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICDx3IvajHE/2cYYP/a/E25efhfP7DH10u0fg5s8927jAiEAmE9cIJNUB1ec8+cg2AkIDXhtz8ipkE80rlnTC4g6Pps="}]}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/object-keys_1.1.0_1549862469286_0.18723271962423693"}, "_hasShrinkwrap": false}, "1.1.1": {"name": "object-keys", "version": "1.1.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "***************"}], "description": "An Object.keys replacement, in case Object.keys is not available. From https://github.com/es-shims/es5-shim", "license": "MIT", "main": "index.js", "scripts": {"pretest": "npm run --silent lint", "test": "npm run --silent tests-only", "posttest": "npm run --silent audit", "tests-only": "node test/index.js", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet", "lint": "eslint .", "preaudit": "npm install --package-lock --package-lock-only", "audit": "npm audit", "postaudit": "rm package-lock.json"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object-keys.git"}, "keywords": ["Object.keys", "keys", "ES5", "shim"], "dependencies": {}, "devDependencies": {"@ljharb/eslint-config": "^13.1.1", "covert": "^1.1.1", "eslint": "^5.13.0", "foreach": "^2.0.5", "indexof": "^0.0.1", "is": "^3.3.0", "tape": "^4.9.2"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "gitHead": "ba2c1989270c7de969aa8498fc3b7c8e677806f3", "bugs": {"url": "https://github.com/ljharb/object-keys/issues"}, "homepage": "https://github.com/ljharb/object-keys#readme", "_id": "object-keys@1.1.1", "_nodeVersion": "11.13.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==", "shasum": "1c47f272df277f3b1daf061677d9c82e2322c60e", "tarball": "https://registry.npmjs.org/object-keys/-/object-keys-1.1.1.tgz", "fileCount": 11, "unpackedSize": 26544, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcqWC7CRA9TVsSAnZWagAApOoQAIGgpq1xnaDCEe3hqJFt\n1fjwFbEKHTyK59hA/zVmvvR4ikMeltZc5KAIKNt5XyDBO4NtuRcA7E1b3D7C\nFX/IMtPwIq5OvZLWhEnGBNTmwlVq8PI9DwZ6AE2hWM4JAmkT5tay7QtjDAur\nYRdTEEB3eqWETNiaybnF9d1GLKuH4dKcM/v9yiHMp+qa9Ivpe9VtWRj7WTr+\nkxc39JZdSVGFbVYNCFkZ8oyj5VbLOtyMB++6JxbR9fYlZ06ibmT+XrFsz7CF\nr7hQ/XFHlyodg0pi34+YhlyDAsPIvk8DOxDoKGs4aFZ6EqZm3hVnWaAlKqgX\n3ikZAT9Z/4d9icoRkEhVMj7INySL4bSd7lFDIlwGruc4j6U6b6phhwgIlhQE\nMsnWmnLL7/AAaPB8oiNhb8Lt/9/jRJsAHwRBRH9NN/DH2VyP0F2hzp66L5dF\niIVw9YUIBCOzfRg5Gr0qd0GCGbIefcq4AomxsJEdBbV+3AFkJvj7dibMrSb7\nJcBVC/TwJCjNv+Ols7VZE+Yj6ZYbNrsuh5KbPkdFchg6qNgds1Dh1tH8GwrJ\nULdSyACz/0stHNGr8p+Boa85mDseApgozr42UUHdEQyohO1/meNonjDGJl9w\ncLlcHMcR7hnLzp4v54jcv+q74EDZa15iEk/ckLdYFoUXXhRhBJo0XyG43zf8\nEO6O\r\n=PzOq\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDEXnivOzyezLnJgG1VUzWQj/PurnTkz1ZGYX4uYuK0JgIhAKKg/wGdPhn67UneiElcVANcbwapE+3GGhHJftwDOncL"}]}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/object-keys_1.1.1_1554604218505_0.17078310534837748"}, "_hasShrinkwrap": false}}, "readme": "#object-keys <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![Build Status][travis-svg]][travis-url]\n[![dependency status][deps-svg]][deps-url]\n[![dev dependency status][dev-deps-svg]][dev-deps-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\n[![browser support][testling-svg]][testling-url]\n\nAn Object.keys shim. Invoke its \"shim\" method to shim Object.keys if it is unavailable.\n\nMost common usage:\n```js\nvar keys = Object.keys || require('object-keys');\n```\n\n## Example\n\n```js\nvar keys = require('object-keys');\nvar assert = require('assert');\nvar obj = {\n\ta: true,\n\tb: true,\n\tc: true\n};\n\nassert.deepEqual(keys(obj), ['a', 'b', 'c']);\n```\n\n```js\nvar keys = require('object-keys');\nvar assert = require('assert');\n/* when Object.keys is not present */\ndelete Object.keys;\nvar shimmedKeys = keys.shim();\nassert.equal(shimmedKeys, keys);\nassert.deepEqual(Object.keys(obj), keys(obj));\n```\n\n```js\nvar keys = require('object-keys');\nvar assert = require('assert');\n/* when Object.keys is present */\nvar shimmedKeys = keys.shim();\nassert.equal(shimmedKeys, Object.keys);\nassert.deepEqual(Object.keys(obj), keys(obj));\n```\n\n## Source\nImplementation taken directly from [es5-shim][es5-shim-url], with modifications, including from [lodash][lodash-url].\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n[package-url]: https://npmjs.org/package/object-keys\n[npm-version-svg]: http://versionbadg.es/ljharb/object-keys.svg\n[travis-svg]: https://travis-ci.org/ljharb/object-keys.svg\n[travis-url]: https://travis-ci.org/ljharb/object-keys\n[deps-svg]: https://david-dm.org/ljharb/object-keys.svg\n[deps-url]: https://david-dm.org/ljharb/object-keys\n[dev-deps-svg]: https://david-dm.org/ljharb/object-keys/dev-status.svg\n[dev-deps-url]: https://david-dm.org/ljharb/object-keys#info=devDependencies\n[testling-svg]: https://ci.testling.com/ljharb/object-keys.png\n[testling-url]: https://ci.testling.com/ljharb/object-keys\n[es5-shim-url]: https://github.com/es-shims/es5-shim/blob/master/es5-shim.js#L542-589\n[lodash-url]: https://github.com/lodash/lodash\n[npm-badge-png]: https://nodei.co/npm/object-keys.png?downloads=true&stars=true\n[license-image]: http://img.shields.io/npm/l/object-keys.svg\n[license-url]: LICENSE\n[downloads-image]: http://img.shields.io/npm/dm/object-keys.svg\n[downloads-url]: http://npm-stat.com/charts.html?package=object-keys\n\n", "maintainers": [{"email": "<EMAIL>", "name": "lj<PERSON>b"}], "time": {"modified": "2023-06-09T21:33:04.590Z", "created": "2013-03-29T20:44:12.281Z", "0.0.1": "2013-03-29T20:44:12.881Z", "0.0.2": "2013-03-30T16:13:52.880Z", "0.1.0": "2013-03-30T20:58:48.065Z", "0.1.1": "2013-04-02T06:16:54.290Z", "0.1.2": "2013-04-03T16:43:21.243Z", "0.1.3": "2013-04-08T01:18:51.713Z", "0.1.4": "2013-04-09T00:47:37.900Z", "0.1.5": "2013-04-14T12:27:20.913Z", "0.1.6": "2013-04-17T07:18:02.522Z", "0.1.7": "2013-04-18T02:23:24.367Z", "0.1.8": "2013-05-10T17:32:12.476Z", "0.2.0": "2013-05-10T18:52:03.655Z", "0.3.0": "2013-05-18T22:06:13.036Z", "0.4.0": "2013-08-14T08:10:10.483Z", "0.5.0": "2014-01-30T09:28:17.465Z", "0.5.1": "2014-03-10T06:43:32.469Z", "0.6.0": "2014-08-01T07:22:33.482Z", "0.6.1": "2014-08-26T05:51:23.007Z", "1.0.0": "2014-08-26T19:21:11.757Z", "1.0.1": "2014-09-03T07:19:08.654Z", "1.0.2": "2014-12-28T09:03:12.859Z", "1.0.3": "2015-01-06T22:27:00.343Z", "1.0.4": "2015-05-23T20:19:48.735Z", "1.0.5": "2015-07-03T23:43:33.872Z", "1.0.6": "2015-07-09T15:41:54.153Z", "1.0.7": "2015-07-18T19:23:11.235Z", "1.0.8": "2015-10-14T22:21:16.304Z", "1.0.9": "2015-10-19T22:07:23.370Z", "1.0.10": "2016-07-04T18:01:59.134Z", "1.0.11": "2016-07-05T17:49:39.399Z", "1.0.12": "2018-06-18T21:47:14.916Z", "1.1.0": "2019-02-11T05:21:09.393Z", "1.1.1": "2019-04-07T02:30:18.674Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object-keys.git"}, "users": {"claudiopro": true, "brostoch": true, "rocket0191": true, "flumpus-dev": true}, "readmeFilename": "README.md", "homepage": "https://github.com/ljharb/object-keys#readme", "keywords": ["Object.keys", "keys", "ES5", "shim"], "bugs": {"url": "https://github.com/ljharb/object-keys/issues"}, "license": "MIT", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "***************"}]}