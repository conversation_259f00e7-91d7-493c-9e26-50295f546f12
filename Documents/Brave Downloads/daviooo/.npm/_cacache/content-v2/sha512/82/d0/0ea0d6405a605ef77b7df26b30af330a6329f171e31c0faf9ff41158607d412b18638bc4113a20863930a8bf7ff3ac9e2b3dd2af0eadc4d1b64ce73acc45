{"_id": "responselike", "_rev": "13-a9ea5ae33a650bc2844235b3710435cd", "name": "responselike", "description": "A response-like object for mocking a Node.js HTTP response stream", "dist-tags": {"latest": "3.0.0"}, "versions": {"0.1.0": {"name": "responselike", "version": "0.1.0", "description": "A response-like object for mocking a native Node.js HTTP response", "keywords": ["http", "https", "response", "request", "responselike"], "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "git+https://github.com/lukechilds/responselike.git"}, "author": {"name": "lukechilds"}, "license": "MIT", "gitHead": "603af7088210b166764883e84c3d4b6ac495ab53", "bugs": {"url": "https://github.com/lukechilds/responselike/issues"}, "homepage": "https://github.com/lukechilds/responselike#readme", "_id": "responselike@0.1.0", "_shasum": "93684efcea96d2c1e96f88a1dbec48d5cc1e5ebd", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.7.0", "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "dist": {"shasum": "93684efcea96d2c1e96f88a1dbec48d5cc1e5ebd", "tarball": "https://registry.npmjs.org/responselike/-/responselike-0.1.0.tgz", "integrity": "sha512-r1+s1Fv6ueVUxyO00sUY36Ti6Jbbj3Q6zce8VUxbosmt2KiX+QbpaYCkgrepvs8zUZo/ScnjS0vHELFkDw4Kvg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBDFk08Qvh88lM7FvC4uocbdA+LGdPYj1ERVmK24Fk6VAiBOgi8/9m5Q9Wiysu7oN21PKSi3frpIm+i2yTB838yjRg=="}]}, "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/responselike-0.1.0.tgz_1493884273503_0.28556131827645004"}, "directories": {}}, "0.2.0": {"name": "responselike", "version": "0.2.0", "description": "A response-like object for mocking a Node.js HTTP response stream", "main": "src/index.js", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "xo": {"extends": "xo-lukechilds"}, "keywords": ["http", "https", "response", "mock", "request", "responselike"], "repository": {"type": "git", "url": "git+https://github.com/lukechilds/responselike.git"}, "author": {"name": "lukechilds"}, "license": "MIT", "devDependencies": {"ava": "^0.19.1", "coveralls": "^2.13.1", "eslint-config-xo-lukechilds": "^1.0.0", "nyc": "^10.3.2", "xo": "^0.19.0"}, "gitHead": "3b21755458e0fbae4ad51cc2ba672888e97546e7", "bugs": {"url": "https://github.com/lukechilds/responselike/issues"}, "homepage": "https://github.com/lukechilds/responselike#readme", "_id": "responselike@0.2.0", "_npmVersion": "5.0.3", "_nodeVersion": "8.0.0", "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-cDn1dhdwkAr3fjk36gC0qB4oU78IdJUl8YKdqTrYzED2O3BWIBGYwxIrWDmC00/Nrn9p3q40icy350fTQgOuxw==", "shasum": "81b12fe4b59e4356b41c1f8db8f24dc07db0bace", "tarball": "https://registry.npmjs.org/responselike/-/responselike-0.2.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICOl4MGLD/Ld1JZ/MEBKmSFNVZqpHbYzpKogoNgf1QQ/AiAN7ese1yIR3QDE5sczV1hgFqxnzYLNjlXPf1nYIbKYPQ=="}]}, "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/responselike-0.2.0.tgz_1497870552306_0.4606168167665601"}, "directories": {}}, "1.0.0": {"name": "responselike", "version": "1.0.0", "description": "A response-like object for mocking a Node.js HTTP response stream", "main": "src/index.js", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "xo": {"extends": "xo-lukechilds"}, "keywords": ["http", "https", "response", "mock", "request", "responselike"], "repository": {"type": "git", "url": "git+https://github.com/lukechilds/responselike.git"}, "author": {"name": "lukechilds"}, "license": "MIT", "devDependencies": {"ava": "^0.19.1", "coveralls": "^2.13.1", "eslint-config-xo-lukechilds": "^1.0.0", "get-stream": "^3.0.0", "nyc": "^10.3.2", "xo": "^0.19.0"}, "dependencies": {"lowercase-keys": "^1.0.0"}, "gitHead": "8d5ea0b607b862239c10ea5f3af301369773107f", "bugs": {"url": "https://github.com/lukechilds/responselike/issues"}, "homepage": "https://github.com/lukechilds/responselike#readme", "_id": "responselike@1.0.0", "_npmVersion": "5.0.3", "_nodeVersion": "8.0.0", "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-lBZecxOwpiohz05HLwKmE8tSTNta9RnpSaSM/n52gTGnFfzxg/Ffh2PpmQGl/stsQYyq5Noymvj+Ctwqemjuig==", "shasum": "271c6b7fbe4d3efdc5a63f9b43628e59690119de", "tarball": "https://registry.npmjs.org/responselike/-/responselike-1.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGsgYQQGudSM3mR6RiNIX0yQuBcDG6/vb3Nlrs1ea+vdAiAI/fqU/GcFAYxhOgCAG6XDrVfuwchHJ8Y1spClOq6X5A=="}]}, "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/responselike-1.0.0.tgz_1498030086941_0.4431796863209456"}, "directories": {}}, "1.0.1": {"name": "responselike", "version": "1.0.1", "description": "A response-like object for mocking a Node.js HTTP response stream", "main": "src/index.js", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "xo": {"extends": "xo-lukechilds"}, "keywords": ["http", "https", "response", "mock", "request", "responselike"], "repository": {"type": "git", "url": "git+https://github.com/lukechilds/responselike.git"}, "author": {"name": "lukechilds"}, "license": "MIT", "devDependencies": {"ava": "^0.20.0", "coveralls": "^2.13.1", "eslint-config-xo-lukechilds": "^1.0.0", "get-stream": "^3.0.0", "nyc": "^11.0.3", "xo": "^0.19.0"}, "dependencies": {"lowercase-keys": "^1.0.0"}, "gitHead": "0d59bc473f63d2155aa0ff24d81951d134a74ea2", "bugs": {"url": "https://github.com/lukechilds/responselike/issues"}, "homepage": "https://github.com/lukechilds/responselike#readme", "_id": "responselike@1.0.1", "_shasum": "7af77f584979e9aee316d5a8923c762bbc0f90d8", "_from": ".", "_npmVersion": "4.6.1", "_nodeVersion": "8.1.2", "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "dist": {"shasum": "7af77f584979e9aee316d5a8923c762bbc0f90d8", "tarball": "https://registry.npmjs.org/responselike/-/responselike-1.0.1.tgz", "integrity": "sha512-cRg0EuVN2JQvxVRY/OP5S+U8ahRl1YuXEZmKWFbF9zsC5rBv8mlB4lsHxDh+vbpEAQVfTv67Y48ZWVVuPjJuzQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAVa/0OVPGhha0CeAamh3L0nbNOd6cFzB5tdCzolw/RYAiEAt6q9ykZC/DipmxFY4UiEjQNRNUYbYwHJHucgoz1R5I8="}]}, "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/responselike-1.0.1.tgz_1498827878831_0.5962018952704966"}, "directories": {}}, "1.0.2": {"name": "responselike", "version": "1.0.2", "description": "A response-like object for mocking a Node.js HTTP response stream", "main": "src/index.js", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "xo": {"extends": "xo-lukechilds"}, "keywords": ["http", "https", "response", "mock", "request", "responselike"], "repository": {"type": "git", "url": "git+https://github.com/lukechilds/responselike.git"}, "author": {"name": "lukechilds"}, "license": "MIT", "devDependencies": {"ava": "^0.22.0", "coveralls": "^2.13.1", "eslint-config-xo-lukechilds": "^1.0.0", "get-stream": "^3.0.0", "nyc": "^11.1.0", "xo": "^0.19.0"}, "dependencies": {"lowercase-keys": "^1.0.0"}, "gitHead": "69366dfd6e161b46bc4503c114d067b87e30dd60", "bugs": {"url": "https://github.com/lukechilds/responselike/issues"}, "homepage": "https://github.com/lukechilds/responselike#readme", "_id": "responselike@1.0.2", "_shasum": "918720ef3b631c5642be068f15ade5a46f4ba1e7", "_from": ".", "_npmVersion": "4.6.1", "_nodeVersion": "8.4.0", "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "dist": {"shasum": "918720ef3b631c5642be068f15ade5a46f4ba1e7", "tarball": "https://registry.npmjs.org/responselike/-/responselike-1.0.2.tgz", "integrity": "sha512-/Fpe5guzJk1gPqdJLJR5u7eG/gNY4nImjbRDaVWVMRhne55TCmj2i9Q+54PBRfatRC8v/rIiv9BN0pMd9OV5EQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG8hTy9GgA2v1okocONuClb24mHaskOC7OYuGQKXBuvgAiEA7nDKBQxuQuuLmfQqfwwlEH94eYaPJFeJ1BjXTCCMtUQ="}]}, "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/responselike-1.0.2.tgz_1502988434730_0.1581996688619256"}, "directories": {}}, "2.0.0": {"name": "responselike", "version": "2.0.0", "description": "A response-like object for mocking a Node.js HTTP response stream", "main": "src/index.js", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "xo": {"extends": "xo-lukechilds"}, "keywords": ["http", "https", "response", "mock", "request", "responselike"], "repository": {"type": "git", "url": "git+https://github.com/lukechilds/responselike.git"}, "author": {"name": "lukechilds"}, "license": "MIT", "devDependencies": {"ava": "^0.25.0", "coveralls": "^3.0.0", "eslint-config-xo-lukechilds": "^1.0.0", "get-stream": "^3.0.0", "nyc": "^11.8.0", "xo": "^0.19.0"}, "dependencies": {"lowercase-keys": "^2.0.0"}, "gitHead": "6dff4cd69cfc91710dc9864e26d3a3dff558376a", "bugs": {"url": "https://github.com/lukechilds/responselike/issues"}, "homepage": "https://github.com/lukechilds/responselike#readme", "_id": "responselike@2.0.0", "_nodeVersion": "10.15.3", "_npmVersion": "6.9.2", "dist": {"integrity": "sha512-xH48u3FTB9VsZw7R+vvgaKeLKzT6jOogbQhEe/jewwnZgzPcnyWui2Av6JpoYZF/91uueC+lqhWqeURw5/qhCw==", "shasum": "26391bcc3174f750f9a79eacc40a12a5c42d7723", "tarball": "https://registry.npmjs.org/responselike/-/responselike-2.0.0.tgz", "fileCount": 4, "unpackedSize": 4626, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdnQooCRA9TVsSAnZWagAAJy8P/iYRnJqTgkCPFJ/JbQVS\n0WIVX62VbUjgBuCPZtY1sc55fENFgyamx9zP54FVPCT3mui/ozPLXp+jqwu1\nMlGPhvGKoUKdbIbDYHeVkge+77RbuIE+f8tQ3pv40Ig6yrnnsKQTO7P99W4R\ndZb+RbpWbvdOfGGVurZdIoCNQm0nKbiWkQE3G6vypVlIHxsI4YQP9FUPxkbi\nikDvA2kYN2mlel4JecgMQ2Z1H1KmeoFHItleGyCsqFMFLH1KCcKJCV8BY+NQ\nXM9wmL6tchtYi0NbXOyHubZfZf0eb4REmujij/vAWU5oJGfb65aUQRDvax5U\nHWVib0OXPLrKQLRotr0hRG+mXGD3KZmunRZFxOqQvHfMvW9qmmeWpu0FJw9I\nyDppUbudoCPpuelr9SIKQEt5/gAuWPzIpXOMRKeeH2+kEBU5eN3AybkZIk+Q\nqOTOVCgYAdCFqev9AMwwb6ZKY9cKGYPPDUCwvZSGkFbci8LaDixQkNBGmpgh\nxtkfnhBA6BLJ+Ld/0hzsTzy+fqGr3WefZ9PilTPFTXRXaeU6noI/XDFA9mhb\nBljujyGGQFpEc1dZ9o6+IJg7JJodWEiUar8o1DCVntO2TZBmgtTwrDeVGluu\nqPhDajkHYA132ary5CVKGlX8fvAfIhmZ/LclxPIm7QMQLIfGqGpfboRacp0E\nSdaB\r\n=0pem\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDGxxWynaVqjBD2ymC+zLnzG53RC7V0CbNCGfGqoeXBQAiEAyCu363J1Yvn4dOq08mGgTarD6gO6qW5XQnCRriDeqEI="}]}, "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/responselike_2.0.0_1570572839625_0.018801265196460415"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "responselike", "version": "2.0.1", "description": "A response-like object for mocking a Node.js HTTP response stream", "funding": "https://github.com/sponsors/sindresorhus", "main": "src/index.js", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "xo": {"extends": "xo-lukechilds"}, "keywords": ["http", "https", "response", "mock", "request", "responselike"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/responselike.git"}, "author": {"name": "lukechilds"}, "license": "MIT", "devDependencies": {"ava": "^0.25.0", "coveralls": "^3.0.0", "eslint-config-xo-lukechilds": "^1.0.0", "get-stream": "^3.0.0", "nyc": "^11.8.0", "xo": "^0.19.0"}, "dependencies": {"lowercase-keys": "^2.0.0"}, "gitHead": "07ece79927bb2deb6f3b9a73d6894298f664dcff", "bugs": {"url": "https://github.com/sindresorhus/responselike/issues"}, "homepage": "https://github.com/sindresorhus/responselike#readme", "_id": "responselike@2.0.1", "_nodeVersion": "14.19.3", "_npmVersion": "8.3.2", "dist": {"integrity": "sha512-4gl03wn3hj1HP3yzgdI7d3lCkF95F21Pz4BPGvKHinyQzALR5CapwC8yIi0Rh58DEMQ/SguC03wFj2k0M/mHhw==", "shasum": "9a0bc8fdc252f3fb1cca68b016591059ba1422bc", "tarball": "https://registry.npmjs.org/responselike/-/responselike-2.0.1.tgz", "fileCount": 4, "unpackedSize": 4685, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICxf+lCXIJc7cZlDif/ZiaScVQz+rIravDKRf4RSbidaAiBnx8Wi0Nw64pVrjKz5UKbH06Kbo1jd3EbW6w2nH/Br5g=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1XcJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqgVQ/6An3u6OWxIw20NFuE2n3fb/OaX5fEQRUjiW5jjzA3DJ4hXN9U\r\nC32ZzgA1nPCPDUSrt2jGlQl7p4f9KVMVMF+x2Ge1DYjOBd/7S3H/1ywtToRM\r\nIAjZ2dynGPaUT2RIF1yzQHaHhgbWdXSLhSVJheWnJNC3MiY9DUhiEXrRrha6\r\n4WxxHS0aW3zhi1Yb3yrQuzay8VL/3c0dwm+MoT3mRPuHZh961E34FMSMxY5C\r\n0yQczaSbXNeYDQUX4swJGdxTN6F3OcaPYpxePaGokoWz8ujjAfXntx1x4AXA\r\nxYBjWTCbZFML1ln0PjOix/sXkgaZBB1/kLcupXDvMFAwicd/zGL1KrKf+6g7\r\nWvkSnx9ymzWYii7qFpNvVMoGnyGAbG0lgBgXMz9ROCAATP/Q/1sentBBXyOD\r\nRWLmPWZhKnAfwEmra14Mb2AHvLADshtM7Lqpg05Hcfs9Y+4cIT5S7GZhycJ+\r\n5RW7+snSoNDlYqp7Q0HvvSC+DJ2tDa6lqtWIRajHdGzLueE+nSRQX2+PuU2k\r\nJVTNuT+ZWLG/okvA3eSZfAeenhuxPCW3X3lDfhQpEO8sguBusuJbl43LjFDp\r\nBIfQ4uM4bPFwoqHK2louOFDvOuFf296NS8CxFHjrm7FUdVmUUQNv25STpbVY\r\nk5DQjdxgXqNHEkMwd4jCpVqNghSJFac5AqI=\r\n=ODGz\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/responselike_2.0.1_1658156809372_0.7707064851724228"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "responselike", "version": "3.0.0", "description": "A response-like object for mocking a Node.js HTTP response stream", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/responselike.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://lukechilds.co.uk"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=14.16"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["http", "https", "response", "mock", "test", "request", "responselike"], "dependencies": {"lowercase-keys": "^3.0.0"}, "devDependencies": {"ava": "^4.3.1", "get-stream": "^6.0.1", "tsd": "^0.22.0", "xo": "^0.50.0"}, "types": "./index.d.ts", "gitHead": "2ec61fa6535fe2a793ad32016b1f1b95d16d5e06", "bugs": {"url": "https://github.com/sindresorhus/responselike/issues"}, "homepage": "https://github.com/sindresorhus/responselike#readme", "_id": "responselike@3.0.0", "_nodeVersion": "14.19.3", "_npmVersion": "8.3.2", "dist": {"integrity": "sha512-40yHxbNcl2+rzXvZuVkrYohathsSJlMTXKryG5y8uciHv1+xDLHQpgjG64JUO9nrEq2jGLH6IZ8BcZyw3wrweg==", "shasum": "20decb6c298aff0dbee1c355ca95461d42823626", "tarball": "https://registry.npmjs.org/responselike/-/responselike-3.0.0.tgz", "fileCount": 5, "unpackedSize": 5554, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDxNJRKTcmeSe83f3aRc9xxYAzi6/TaCiWKNZTcGuMHcgIgfaXZaLlXKzxuC5mjA8RI8D10mWvfLVaCOyMLDtXlks8="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1cszACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoqsA//fEwtDenRzXIywsEemvT0TSMNBuBH9TsvK5LXumUbtjMUDVfJ\r\n4XMEGqPfrh5DdpVMk2V7t61qOqvk6YJIIRx7OSJZDeeFlOdDlBmdRXen7Uy6\r\nh+EjN7ikoIMBN0wgxPIYYojX9ffQWHH785zL0Jvrm3tFjyNCdpKJvvhzFJR8\r\nsx6edWB2boAWb4X8YDXxHUNWIlbGaq3fY6ewg+TQ0cqES5UF6NYLtT81Q7O5\r\nVNEFYAAQBpFGYxt8k8csBolTMQkO8VAq/pYhobXdcigNN/1ImWcdzVTddE3f\r\nGHUoSCxwRV+xOe6T1avBktsPCaNyh0cEUqH0ax6GVRsq3XJjvoWueQXy6O2L\r\nof7UWfKPyU2s838oE/y3lg+uM0MHx1rZWNEXDZEVvoZEkttk6+awd3hPF3bJ\r\nItfaFu66KyxPjHpUSWSNqpFbwC//mpftcHZf++//G6NpSnuaw39o/bKzTEST\r\nisiPZrrqG/eCJ8ome6Zb1i2JubLWNoKfgBlZfsv8C33mJvQOsF7kf28lyHES\r\n6LAemKTXo8cyhYC5Hgy7dJQ5UoSCweHRGLRJvKBNZfawYr2Tc2E4Y2W7c18o\r\nPpr3usuZcLnb6C2jynx3TR6X0367zzRKqRPhGrfu7rHvZzrc1o0Gz0m+8Eoi\r\nk3Nra1Qvb8+kZHUaBroryaITxG9QftYqPFk=\r\n=NyHG\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/responselike_3.0.0_1658178355629_0.3164521105754696"}, "_hasShrinkwrap": false}}, "readme": "# responselike\n\n> A response-like object for mocking a Node.js HTTP response stream\n\nReturns a streamable response object similar to a [Node.js HTTP response stream](https://nodejs.org/api/http.html#http_class_http_incomingmessage). Useful for formatting cached responses so they can be consumed by code expecting a real response.\n\n## Install\n\n```sh\nnpm install responselike\n```\n\n## Usage\n\n```js\nimport Response from 'responselike';\n\nconst response = new Response({\n\tstatusCode: 200,\n\theaders: {\n\t\tfoo: 'bar'\n\t},\n\tbody: Buffer.from('Hi!'),\n\turl: 'https://example.com'\n});\n\nresponse.statusCode;\n// 200\n\nresponse.headers;\n// {foo: 'bar'}\n\nresponse.body;\n// <Buffer 48 69 21>\n\nresponse.url;\n// 'https://example.com'\n\nresponse.pipe(process.stdout);\n// 'Hi!'\n```\n\n## API\n\n### new Response(options?)\n\nReturns a streamable response object similar to a [Node.js HTTP response stream](https://nodejs.org/api/http.html#http_class_http_incomingmessage).\n\n#### options\n\nType: `object`\n\n##### statusCode\n\nType: `number`\n\nThe HTTP response status code.\n\n##### headers\n\nType: `object`\n\nThe HTTP headers. Keys will be automatically lowercased.\n\n##### body\n\nType: `Buffer`\n\nThe response body. The Buffer contents will be streamable but is also exposed directly as `response.body`.\n\n##### url\n\nType: `string`\n\nThe request URL string.\n", "maintainers": [{"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "time": {"modified": "2023-06-09T21:33:01.885Z", "created": "2017-05-04T07:51:15.890Z", "0.1.0": "2017-05-04T07:51:15.890Z", "0.2.0": "2017-06-19T11:09:12.432Z", "1.0.0": "2017-06-21T07:28:07.128Z", "1.0.1": "2017-06-30T13:04:39.730Z", "1.0.2": "2017-08-17T16:47:15.629Z", "2.0.0": "2019-10-08T22:13:59.825Z", "2.0.1": "2022-07-18T15:06:49.818Z", "3.0.0": "2022-07-18T21:05:55.896Z"}, "homepage": "https://github.com/sindresorhus/responselike#readme", "keywords": ["http", "https", "response", "mock", "test", "request", "responselike"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/responselike.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://lukechilds.co.uk"}, "bugs": {"url": "https://github.com/sindresorhus/responselike/issues"}, "license": "MIT", "readmeFilename": "readme.md", "users": {"flumpus-dev": true}}