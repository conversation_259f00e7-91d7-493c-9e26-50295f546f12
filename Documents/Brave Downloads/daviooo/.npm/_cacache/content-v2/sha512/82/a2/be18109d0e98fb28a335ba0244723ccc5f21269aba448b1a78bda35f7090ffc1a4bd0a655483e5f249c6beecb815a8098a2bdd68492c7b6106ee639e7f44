{"_id": "es-get-iterator", "_rev": "7-73967554f35d64af838716c4fd455e4b", "name": "es-get-iterator", "dist-tags": {"latest": "1.1.3"}, "versions": {"1.0.0": {"name": "es-get-iterator", "version": "1.0.0", "description": "Get an iterator for any JS language value. Works robustly across all environments, all versions.", "main": "index", "exports": {".": [{"browser": "./index.js", "default": "./node.js"}, "./node.js"], "./package": "./package.json", "./package.json": "./package.json"}, "type": "commonjs", "scripts": {"prepublish": "safe-publish-latest", "lint": "eslint .", "pretest": "npm run lint", "tests-only": "nyc node test/${TEST_VARIANT-}", "test": "npm run tests-only", "posttest": "npx aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/es-get-iterator.git"}, "keywords": ["iterator", "Symbol.iterator", "iterable", "collection", "next", "iteration"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/es-get-iterator/issues"}, "homepage": "https://github.com/ljharb/es-get-iterator#readme", "devDependencies": {"@ljharb/eslint-config": "^15.0.2", "auto-changelog": "^1.16.2", "core-js": "^2.6.10 || ^3.4.2", "es5-shim": "^4.5.13", "es6-shim": "^0.35.5", "eslint": "^6.7.1", "for-each": "^0.3.3", "has-bigints": "^1.0.0", "nyc": "^10.3.2", "object-inspect": "^1.7.0", "safe-publish-latest": "^1.1.4", "tape": "^4.11.0"}, "testling": {"files": "./test/browser.js"}, "greenkeeper": {"ignore": ["nyc"]}, "dependencies": {"es-abstract": "^1.16.2", "has-symbols": "^1.0.1", "is-arguments": "^1.0.4", "is-map": "^2.0.0", "is-set": "^2.0.0", "is-string": "^1.0.4", "isarray": "^2.0.5"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "gitHead": "e77c9850213b5f40e6d34a81a7b589c695e04141", "_id": "es-get-iterator@1.0.0", "_nodeVersion": "13.2.0", "_npmVersion": "6.13.1", "dist": {"integrity": "sha512-IhfNVZEhHFZ6NOy5/SDCNvHpbGHePOl4vpYB4sebNrMOZfizdpLtiYAN9Yc1wBlgyG5Z22fLVtxmmnko45YSmQ==", "shasum": "2af15002c9ae52d5954b7e065ca5b85b9d638d66", "tarball": "https://registry.npmjs.org/es-get-iterator/-/es-get-iterator-1.0.0.tgz", "fileCount": 18, "unpackedSize": 21814, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd3K/vCRA9TVsSAnZWagAAseIQAKN5r6hR+UNzqTORckl5\nmY8hiKu0RtgdJyg43+s6juO0pJJejtNgpXtAAjfuNwQzytNpGjVYpxOaZrJw\nb/4J3VpZGGEqkTvW+8Grw8TKDAww6QS3hp8ur2+3WcCX6n9UMgXYkWGQru1r\naYiMt9sRPYP7hb2oQk475EfURjemNICRYt2ExhKDraH0JwssshWIAa1u9efp\nuZQZJOLlqUJIvP0Futrgbt2LWptp2O9rof0Gat0OcvzCPoI9N5v+iJteMY6T\nyKzI8XKlmOog5yhoEhbypjsc1Vm/1a7TOlAke8Mc5Ojk31mmfFYXECSUJ8up\nPSo65im0Ap5fg59GXPM4ULOgUHbXI1xPpOAuFTWZtDPFZSb+49vfMgn5EFJH\nrLJ1QQs8i73BvcRycmq4+1unTFdNS91mja4UsoE8yqnzuiAq5VTZ0BUZWjTc\nicPEOChHlrMOt0ytLnR2W+/gUcrJHIkwTFzWhgDV6g04t7vs5PLX6sof+COV\nUoLL5iAt1QJP75e0911sSpQbbFtMPWhMe8hIUxfKC5nMW7Gyvbj8u0a+bF33\n1UbmHXUfkcv9JqW7saW1AfPmMtiK9nCRGCmuq76QtvVvFwAjt+i7n4ZMgKNs\nsXOAHsEhKtBIm+sZEDyRWOKHuRd6Tt4zCfRrTKv+AgHDKhqR9EKgtVGKmY0x\nhANp\r\n=AJpP\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEHbEwMsMWHLNHfkU7Sz0OrZozec05lhATuhEJQZgRfrAiAAgYAkR5olB/hF342pERLPJ9xAU6JkgK3872GBLR3k7A=="}]}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/es-get-iterator_1.0.0_1574744046817_0.1538596718625953"}, "_hasShrinkwrap": false}, "1.0.1": {"name": "es-get-iterator", "version": "1.0.1", "description": "Get an iterator for any JS language value. Works robustly across all environments, all versions.", "main": "index", "exports": {".": [{"browser": "./index.js", "default": "./node.js"}, "./node.js"], "./package": "./package.json", "./package.json": "./package.json"}, "type": "commonjs", "scripts": {"prepublish": "safe-publish-latest", "lint": "eslint .", "pretest": "npm run lint", "tests-only": "nyc node test/${TEST_VARIANT-}", "test": "npm run tests-only", "posttest": "npx aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/es-get-iterator.git"}, "keywords": ["iterator", "Symbol.iterator", "iterable", "collection", "next", "iteration"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/es-get-iterator/issues"}, "homepage": "https://github.com/ljharb/es-get-iterator#readme", "devDependencies": {"@ljharb/eslint-config": "^15.0.2", "auto-changelog": "^1.16.2", "core-js": "^2.6.10 || ^3.4.2", "es5-shim": "^4.5.13", "es6-shim": "^0.35.5", "eslint": "^6.7.1", "for-each": "^0.3.3", "has-bigints": "^1.0.0", "nyc": "^10.3.2", "object-inspect": "^1.7.0", "safe-publish-latest": "^1.1.4", "tape": "^4.11.0"}, "testling": {"files": "./test/browser.js"}, "greenkeeper": {"ignore": ["nyc"]}, "dependencies": {"es-abstract": "^1.16.2", "has-symbols": "^1.0.1", "is-arguments": "^1.0.4", "is-map": "^2.0.0", "is-set": "^2.0.0", "is-string": "^1.0.4", "isarray": "^2.0.5"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "gitHead": "c3ac1e0257b4c5be3958e02d6353730c0d93e6a3", "_id": "es-get-iterator@1.0.1", "_nodeVersion": "13.2.0", "_npmVersion": "6.13.1", "dist": {"integrity": "sha512-pswpctxWRElQDcP0RJy0qmNrpf6nH9SeQl8dra5fFHBPHKfpVIST27Kv4j5enE8JhIssRBI4QPMrNvcyIPhapQ==", "shasum": "ebc4d3bbc2d59dd95a0ecef441ca2dbce7d9e2cd", "tarball": "https://registry.npmjs.org/es-get-iterator/-/es-get-iterator-1.0.1.tgz", "fileCount": 19, "unpackedSize": 22854, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd32jbCRA9TVsSAnZWagAAKagQAKOs5aHcLvwOgbIbs3ae\nD0ce8BrvTHEQSpPYFviJeeP9o/+VfnS0Y5rc/ivkG5AlUzYIDXGzf6SIIBd7\nDdjqKLwQP8Tlh6T7yqVkSsAZ8L8xm/y5Gim4gbpQm2J/x+SQd4947l5Y2vph\nwhHquL+tRILUIFjBKWz7EzOqDsEd7Lot/3SrH+r3pr9p1Fj87Xpe4UR31V/T\nDEZiZ/g/8i3qQcRDkdLX2Ij5kHhRi09/moacuEiPpzo047/ldOmyc8pE43lB\n1YuZbhJyKlsvH8vq9l4XW2ItIUZTnGq1NX4W1kAZi3y1If0K/jvEuexcefZE\nHwHCsBkUO9rNOQyhn66j2wZ/6z49iiawUpfjLmG8ufeV4XeRl0JPfbrXTfPh\nys1TtsjdUVjTQb55mV72u0dXhTRfoEfL/F8iARbqPCLhDveFcmtt1IBNiq3s\nlyBEZ9dZZ+d24f051GZf237C8L5SEyGcpH5x+p/Bw7x79rvplgYZkwx5oLBT\nHSwC5lY/Fo2aSOVV2R8mRz9EhpcNR6LS4pqc0ziUR53IPMdzpZDk3MNHEASN\nT7tuhFISa62o13+Qzt+v9Ai7uxabgkwQo4/lFXhAsaPCl4ws2HVGzu8wUJjx\nv/n/yL1kKSab7Mdu6iHEhCPK6RK+6VM5QcNVB04J2wI5JWrhR/q23MylMZ3b\nSiey\r\n=+6ge\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDKP6C56A0CBnkFIbhbe7CmaoBPVxnIKry33AHQcPRU4AIgIpBYYaYiX248fD0enWN3qtCsXrQzUL/KhM1jBCiFTYA="}]}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/es-get-iterator_1.0.1_1574922458804_0.08473760863313218"}, "_hasShrinkwrap": false}, "1.0.2": {"name": "es-get-iterator", "version": "1.0.2", "description": "Get an iterator for any JS language value. Works robustly across all environments, all versions.", "main": "index", "exports": {".": [{"browser": "./index.js", "default": "./node.js"}, "./node.js"], "./package": "./package.json", "./package.json": "./package.json"}, "type": "commonjs", "scripts": {"prepublish": "safe-publish-latest", "lint": "eslint .", "pretest": "npm run lint", "tests-only": "nyc node test/${TEST_VARIANT-}", "test": "npm run tests-only", "posttest": "npx aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/es-get-iterator.git"}, "keywords": ["iterator", "Symbol.iterator", "iterable", "collection", "next", "iteration"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/es-get-iterator/issues"}, "homepage": "https://github.com/ljharb/es-get-iterator#readme", "devDependencies": {"@ljharb/eslint-config": "^15.0.2", "auto-changelog": "^1.16.2", "core-js": "^2.6.10 || ^3.4.2", "es5-shim": "^4.5.13", "es6-shim": "^0.35.5", "eslint": "^6.7.2", "for-each": "^0.3.3", "has-bigints": "^1.0.0", "nyc": "^10.3.2", "object-inspect": "^1.7.0", "safe-publish-latest": "^1.1.4", "tape": "^4.11.0"}, "testling": {"files": "./test/browser.js"}, "greenkeeper": {"ignore": ["nyc"]}, "dependencies": {"es-abstract": "^1.17.0-next.1", "has-symbols": "^1.0.1", "is-arguments": "^1.0.4", "is-map": "^2.0.0", "is-set": "^2.0.0", "is-string": "^1.0.4", "isarray": "^2.0.5"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "gitHead": "6d087cbd9ae21fc4a9ba2833cb9a3831460a5e5d", "_id": "es-get-iterator@1.0.2", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.1", "dist": {"integrity": "sha512-ZHb4fuNK3HKHEOvDGyHPKf5cSWh/OvAMskeM/+21NMnTuvqFvz8uHatolu+7Kf6b6oK9C+3Uo1T37pSGPWv0MA==", "shasum": "bc99065aa8c98ce52bc86ab282dedbba4120e0b3", "tarball": "https://registry.npmjs.org/es-get-iterator/-/es-get-iterator-1.0.2.tgz", "fileCount": 19, "unpackedSize": 23233, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd9/CdCRA9TVsSAnZWagAAElgQAJNJrb1z8ShAOtmNMIFB\nZhJM705zioF9fef+Yu0PzVQUjA49d6LCicOi9b+cN1oEOeW3+FUju+YXyd35\nsuGfqZkd6HayXuiz4dupxmTVPjAYjzPuln1G+6zDFzHyJuvvOug4GT5Ppf5S\nkFdFTQzb67uaibwddPUlh08ZTmwET7K+1sKnxOEcJiSBICQlJY46RIO16xSt\nC0uHwH/EyPPjSJiHpxfEUy+wIEEaF1V9iGGL+R6T7o2Lj8mIfjtPabWIRrcE\nvpaEAyLWEzwPptW2huHRM9GQjI1hLGAEx542GtAbgBkmvGcUPQc+a5Xdu+D9\nBvYHNNBTmZpfIXfbXfEmVJrErE9gDk8cAVFPXZgWfLN7DJKaHUWW5al1KtMQ\nbom4nnoIx5laOgAKCTLl7PabftjxVSfvL/sMJIkMoFflnYXRmB5Cki0PCARz\n9ogFLw/kmq0JLCONiU3vI3PTCusPSL0ABNNVv7kz7OKzdYgEBk5gT7p+Do9K\nfqhXB6W8CdX3Wgnbs/JXIpsXQNfmkKcMko6IjbR/z2/vBIfbHMsRM38uC1dH\njAkdfJuCkf08X9d09Vzcm5VxZ2qKVSjbaSWCnh0Pq8qmeabRFVIBfW6TC5n9\nzoTXYVd+2DNcoF2yiTjxjKxtycpyOSsxso1h5INK/1UGFmB2GDEQ42b6/yYm\nwHvZ\r\n=Wq5i\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBg2s5e99/+3u9FV7iRk2RvVfj2tpNFiBVa7wS769KQeAiB1OSrbhMyxPWn0inbTXYh5Q0xcOl6AsTfyxcrIuCn3DA=="}]}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/es-get-iterator_1.0.2_1576530076853_0.36654094578237295"}, "_hasShrinkwrap": false}, "1.1.0": {"name": "es-get-iterator", "version": "1.1.0", "description": "Get an iterator for any JS language value. Works robustly across all environments, all versions.", "main": "index", "exports": {".": [{"browser": "./index.js", "import": "./node.mjs", "default": "./node.js"}, "./node.js"], "./package": "./package.json", "./package.json": "./package.json"}, "type": "commonjs", "scripts": {"prepublish": "safe-publish-latest", "lint": "eslint --ext=js,mjs .", "pretest": "npm run lint", "tests-only": "nyc node test/${TEST_VARIANT-}", "tests-esm": "nyc node test/node.mjs", "test": "npm run tests-only && npm run tests-esm", "posttest": "npx aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/es-get-iterator.git"}, "keywords": ["iterator", "Symbol.iterator", "iterable", "collection", "next", "iteration"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/es-get-iterator/issues"}, "homepage": "https://github.com/ljharb/es-get-iterator#readme", "devDependencies": {"@ljharb/eslint-config": "^16.0.0", "auto-changelog": "^1.16.2", "core-js": "^2.6.10 || ^3.4.2", "es5-shim": "^4.5.13", "es6-shim": "^0.35.5", "eslint": "^6.8.0", "for-each": "^0.3.3", "has-bigints": "^1.0.0", "nyc": "^10.3.2", "object-inspect": "^1.7.0", "safe-publish-latest": "^1.1.4", "tape": "^5.0.0-next.3"}, "testling": {"files": "./test/index.js"}, "greenkeeper": {"ignore": ["nyc"]}, "dependencies": {"es-abstract": "^1.17.4", "has-symbols": "^1.0.1", "is-arguments": "^1.0.4", "is-map": "^2.0.1", "is-set": "^2.0.1", "is-string": "^1.0.5", "isarray": "^2.0.5"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "gitHead": "973827273316446fa4b757cf64e35032df1c7d19", "_id": "es-get-iterator@1.1.0", "_nodeVersion": "13.7.0", "_npmVersion": "6.13.6", "dist": {"integrity": "sha512-UfrmHuWQlNMTs35e1ypnvikg6jCz3SK8v8ImvmDsh36fCVUR1MqoFDiyn0/k52C8NqO3YsO8Oe0azeesNuqSsQ==", "shasum": "bb98ad9d6d63b31aacdc8f89d5d0ee57bcb5b4c8", "tarball": "https://registry.npmjs.org/es-get-iterator/-/es-get-iterator-1.1.0.tgz", "fileCount": 19, "unpackedSize": 26082, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeLSjRCRA9TVsSAnZWagAAkusP/36y+hMg9GE+wUAcALvS\nl5Dego3bKnuVkq5fjhKHl1t4Yy7Ak/eCQm7ynD2seCvO9tTKyDWmqDACbvYf\nIPqYxOcvO0hvlM7TaqluGSRKwnRGiV/AsnHXsmVrYKORSRdwvhwqK6mo/naE\nIEmFSKpPEAp+rpcRvNuNP3eh4cl3zhmo9K4JIGCmT16oPtJXyj128mIwHBW1\nbLx6YSAf7aT8S/qRBp/Cof1/I0yJ5TcXE6Pzs7wF1k79Y8Hihv/bZL0BxjjO\nDRUi/HETTBQnXQQ/lAlhZVBUBmHS+/QRPRDP1OrMk/I6xL/6J3n7d2XGaw+U\ncGAH/YW8plwFZO/9Y57G9L5gIXI1eC6GyuorW8CJUVT6pNhy0OtA2lJM5x/T\nNIdQc3CfnhawuJJDT18rQp+AD4cmrw06Lx3ILBzXacojxdr9D5mHhzfVZrer\n6iCVFT5gaNxaqVXKVoJDEFqIljOzrOx+iEYILYSvR1RzbT3jmyHbsMPCUvxD\ns/1VzrbzPT/8JAx4MBgiGuG8eQjo9PjHtNJgV2jfGSFQ653d/dd7FszWBnZG\nNsdqMesN4dhz8WZFWKsB/DtJD/1tF+UWceVZYyTJ15teyseRCAGXd8UhjJsb\noqFYOO1Er6w+uUFU3uZC6+meuhBXEWOdJR3DNR7cLPZMMBFtuEhQvGYAyvDh\nH1He\r\n=ULMS\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD5zQshm3vDvMC+JHb/By8wSynn6eAUENJeqeMLrKrceAIgQoGBU1OqZ0q7INtHlxflYE1M3QB8PDq+sXeyo0jRIIg="}]}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/es-get-iterator_1.1.0_1580017872972_0.7464850820185129"}, "_hasShrinkwrap": false}, "1.1.1": {"name": "es-get-iterator", "version": "1.1.1", "description": "Get an iterator for any JS language value. Works robustly across all environments, all versions.", "main": "index", "exports": {".": [{"browser": "./index.js", "import": "./node.mjs", "default": "./node.js"}, "./node.js"], "./package": "./package.json", "./package.json": "./package.json"}, "type": "commonjs", "scripts": {"prepublish": "safe-publish-latest", "lint": "eslint --ext=js,mjs .", "pretest": "npm run lint", "tests-only": "nyc node test/${TEST_VARIANT-}", "tests-esm": "TEST_VARIANT=node.mjs npm run tests-only", "tests-preload-es6-shim": "TEST_VARIANT=es6-shim PRELOAD_GET_ITERATOR=true npm run tests-only", "test": "npm run tests-only && npm run tests-esm && npm run tests-preload-es6-shim", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/es-get-iterator.git"}, "keywords": ["iterator", "Symbol.iterator", "iterable", "collection", "next", "iteration"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/es-get-iterator/issues"}, "homepage": "https://github.com/ljharb/es-get-iterator#readme", "devDependencies": {"@ljharb/eslint-config": "^17.2.0", "aud": "^1.1.3", "auto-changelog": "^2.2.1", "core-js": "^2.6.10 || ^3.4.2", "es5-shim": "^4.5.14", "es6-shim": "^0.35.6", "eslint": "^7.12.1", "for-each": "^0.3.3", "has-bigints": "^1.0.0", "nyc": "^10.3.2", "object-inspect": "^1.8.0", "safe-publish-latest": "^1.1.4", "tape": "^5.0.1"}, "testling": {"files": "./test/index.js"}, "greenkeeper": {"ignore": ["nyc"]}, "dependencies": {"call-bind": "^1.0.0", "get-intrinsic": "^1.0.1", "has-symbols": "^1.0.1", "is-arguments": "^1.0.4", "is-map": "^2.0.1", "is-set": "^2.0.1", "is-string": "^1.0.5", "isarray": "^2.0.5"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "gitHead": "6b68c743d3fd8a52a86856ea55c7947461e9982b", "_id": "es-get-iterator@1.1.1", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-qorBw8Y7B15DVLaJWy6WdEV/ZkieBcu6QCq/xzWzGOKJqgG1j754vXRfZ3NY7HSShneqU43mPB4OkQBTkvHhFw==", "shasum": "b93ddd867af16d5118e00881396533c1c6647ad9", "tarball": "https://registry.npmjs.org/es-get-iterator/-/es-get-iterator-1.1.1.tgz", "fileCount": 23, "unpackedSize": 34058, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfpYymCRA9TVsSAnZWagAAUdYP/RnPwwlMttLL0IG8Wxc+\nqOLArC8JntvFl53Hju4gYoHZITo0NdJi2ELlG/vyjWf6vCo6pL8V3QIRSA6x\nw4CIWgsZ8IzcpRjs/SxS+I98buCaR4NdlVGy5zLYC1J1ozdqL5cmKow82w6w\nV3zlHiKNZMsnK4OD/iweaRjNRg3j66pQnv8Ttcd77wLUsH1Y1Ngx2QWkBYXq\n3uBuaFkd8yakR+0NMfqj8jXNXdpG89Liv8tjzKYC4HWLMkLD1fK+4/BpEUWa\nICRPb0XqFlhwXP84Hsg2N/yOB3cCNS6LYv/LO5O5v0RJhWhDxsLkPvOPEFf1\nWKHE92XkGfVAK+qDrC3zwSmcR759h89iMuFJ5PxG8zD1dltr7EXwzqzMBHBY\nvrKKRKUICV1TmgDiYxeutcQCV3aXU2XJ3UtWnq910CAZumeR9Xd/c221XoPi\ntI+kfDeWLLZp1K+fkg/MwSOT88p+CF5Tth6teDMPQfQBB/qIyj7KLMCEFnDs\n70AnyeiQxbloaLXY0mNTeNp0TWF+goxtjRFDdPnZa+h+ALdGQ/DB0AC7M/Hc\nW3fBPiIhIHZKUqvYD2KILTk6daG5U72AAncO2cUakip094fucQlZFVrd9ua+\ntEnQsC+AD2znn0HTSKgyvR2soZKjmUr1lfGrNZ8t11z/sCN+RWcvA/gR9ogs\nl3FM\r\n=x3/B\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHyeD2txqnxHVVWtgfECJG7l+P5bJ8nqnQkoenCyFjs5AiEAn56oX6sZNFG74jyyKzIwG2mEpnkKEldQY/XMY3sr3iE="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/es-get-iterator_1.1.1_1604684965758_0.6291401422414911"}, "_hasShrinkwrap": false}, "1.1.2": {"name": "es-get-iterator", "version": "1.1.2", "description": "Get an iterator for any JS language value. Works robustly across all environments, all versions.", "main": "./index.js", "browser": "./index.js", "exports": {".": [{"browser": "./index.js", "import": "./node.mjs", "default": "./node.js"}, "./node.js"], "./package": "./package.json", "./package.json": "./package.json"}, "type": "commonjs", "scripts": {"prepublish": "safe-publish-latest", "lint": "eslint --ext=js,mjs .", "pretest": "npm run lint", "tests-only": "nyc node test/${TEST_VARIANT-}", "tests-esm": "TEST_VARIANT=node.mjs npm run tests-only", "tests-preload-es6-shim": "TEST_VARIANT=es6-shim PRELOAD_GET_ITERATOR=true npm run tests-only", "test": "npm run tests-only && npm run tests-esm && npm run tests-preload-es6-shim", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/es-get-iterator.git"}, "keywords": ["iterator", "Symbol.iterator", "iterable", "collection", "next", "iteration"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/es-get-iterator/issues"}, "homepage": "https://github.com/ljharb/es-get-iterator#readme", "devDependencies": {"@ljharb/eslint-config": "^17.5.0", "aud": "^1.1.3", "auto-changelog": "^2.2.1", "core-js": "^2.6.10 || ^3.4.2", "es5-shim": "^4.5.15", "es6-shim": "^0.35.6", "eslint": "^7.18.0", "for-each": "^0.3.3", "has-bigints": "^1.0.1", "nyc": "^10.3.2", "object-inspect": "^1.9.0", "safe-publish-latest": "^1.1.4", "tape": "^5.1.1"}, "testling": {"files": "./test/index.js"}, "greenkeeper": {"ignore": ["nyc"]}, "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.1.0", "has-symbols": "^1.0.1", "is-arguments": "^1.1.0", "is-map": "^2.0.2", "is-set": "^2.0.2", "is-string": "^1.0.5", "isarray": "^2.0.5"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "gitHead": "58601cc38b4608f33a8c1bfe932c79c1afc43ef0", "_id": "es-get-iterator@1.1.2", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"integrity": "sha512-+DTO8GYwbMCwbywjimwZMHp8AuYXOS2JZFWoi2AlPOS3ebnII9w/NLpNZtA7A0YLaVDw+O7KFCeoIV7OPvM7hQ==", "shasum": "9234c54aba713486d7ebde0220864af5e2b283f7", "tarball": "https://registry.npmjs.org/es-get-iterator/-/es-get-iterator-1.1.2.tgz", "fileCount": 16, "unpackedSize": 28466, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgEIApCRA9TVsSAnZWagAAy3MP/2vXA+7Ba86U64qzwkUV\nZAiV/tOuuVgrlANfizGYtZfs4kYv9qTnAOm0j9hxOQLjck1cQ6j5CzSg7+CR\nWgEHl4GvGXRwapwMny9VHHGtjtgym2A01/HlmDqWTgzLHiibd5IIOTOGsFRn\nksIsfVJAPXdFS5sKfMq+aads3nY6dBc/k34IV3N76YCnarcgurjXJnIpQRlY\ntZMvFUsdbOhtrzP/Uo7Sc601sLwyGYFH0AbvubrwXN3+mRsgAMTbgbNdDmWD\nkD/HCEMk+NK+XlENt73PPvVFDObosboHoNrFGnqS8BUXDJMd8tLMj37TgCl8\njr1UTOFZrXEzy97aLEKpx6/xK8vONX/PrRFE96XrzILmKvcHoLnGI50Z4TM0\nD+FTky4OniEyt/NR0XfK4IXQjs/0ajD+HJGlkRoC3eerP0Vk5y6h8vaTVu3F\nd0S9K+JcZhmaVrirBMSsm1Jcs/Cv76ZMoCPyzPBHj2aayBdXxPVvrIf8I0i1\nB5RLkCz1pPwKHoJi0G2HQjnlwXI7bY3p9OpoRkMRK+A39Pq9RiFkqQ44XVv6\n9elOf+GimPNfqJnjf4MUHjZIM81EE6kkK31A4O/MjYWZlvDYg26lPq9eWusa\n2shBDHXwL/9Q/SbPEbJx8vVpDE/Cu3Os4G59CCTSPUXWlL9o2SBDfALbhc2d\nhx4U\r\n=/h4o\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA/0hneoTsifVXHeWC9x+xjSpjOsF+hx9bQf3zPCkoCnAiBpmVns1AjHUnUzZ9bipt89F/tsskw8HuZISGiD38ACUg=="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/es-get-iterator_1.1.2_1611694121012_0.4158267443335717"}, "_hasShrinkwrap": false}, "1.1.3": {"name": "es-get-iterator", "version": "1.1.3", "description": "Get an iterator for any JS language value. Works robustly across all environments, all versions.", "main": "./index.js", "browser": "./index.js", "exports": {".": [{"browser": "./index.js", "import": "./node.mjs", "default": "./node.js"}, "./node.js"], "./package": "./package.json", "./package.json": "./package.json"}, "type": "commonjs", "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "lint": "eslint --ext=js,mjs .", "pretest": "npm run lint", "tests-only": "nyc node test/${TEST_VARIANT-}", "tests-esm": "TEST_VARIANT=node.mjs npm run tests-only", "tests-preload-es6-shim": "TEST_VARIANT=es6-shim PRELOAD_GET_ITERATOR=true npm run tests-only", "test": "npm run tests-only && TEST_VARIANT=node npm run tests-only && npm run tests-esm && npm run tests-preload-es6-shim", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/es-get-iterator.git"}, "keywords": ["iterator", "Symbol.iterator", "iterable", "collection", "next", "iteration"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/es-get-iterator/issues"}, "homepage": "https://github.com/ljharb/es-get-iterator#readme", "devDependencies": {"@ljharb/eslint-config": "^21.0.1", "aud": "^2.0.2", "auto-changelog": "^2.4.0", "core-js": "^2.6.10 || ^3.4.2", "es5-shim": "^4.6.7", "es6-shim": "^0.35.7", "eslint": "=8.8.0", "for-each": "^0.3.3", "has-bigints": "^1.0.2", "npmignore": "^0.3.0", "nyc": "^10.3.2", "object-inspect": "^1.12.3", "safe-publish-latest": "^2.0.0", "tape": "^5.6.1"}, "testling": {"files": "./test/index.js"}, "greenkeeper": {"ignore": ["nyc"]}, "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.1.3", "has-symbols": "^1.0.3", "is-arguments": "^1.1.1", "is-map": "^2.0.2", "is-set": "^2.0.2", "is-string": "^1.0.7", "isarray": "^2.0.5", "stop-iteration-iterator": "^1.0.0"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "publishConfig": {"ignore": [".github/workflows"]}, "gitHead": "ed0e55512a2cadbbd845f1fa8e06d51fca606423", "_id": "es-get-iterator@1.1.3", "_nodeVersion": "19.4.0", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-sPZmqHBe6JIiTfN5q2pEi//TwxmAFHwj/XEuYjTuse78i8KxaqMTTzxPoFKuzRpDpTJ+0NAbpfenkmH2rePtuw==", "shasum": "3ef87523c5d464d41084b2c3c9c214f1199763d6", "tarball": "https://registry.npmjs.org/es-get-iterator/-/es-get-iterator-1.1.3.tgz", "fileCount": 15, "unpackedSize": 31633, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDTPR6e5r55SrMJv2tDMEAsuWDkdU9m6FwslhVUQQ564AiA4GNt0GTNK2HQQH7Cx9o00PkZNdoO/yhpTWd19VqJAfA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjwQ0WACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrdMg//X6vnG/v//1Jf2LJ47+ehsXf/OPZelrGs8vjjixPVfY9aSaLu\r\n2i3Vx3BPrSdmd01Oa98NJrr7UTPnizsjQLbXapR7DRLRmNsJPG29t58EnW/s\r\nwbA+8qyDkw/DXDFnD9DJCLDsW8ZUTzTDDFcu+JYRvmn4he3Avevr7zAAPSbb\r\nZjnhCvYayTxcs8Miu2rOcLRADC9cagGJ2KK6IZtcKZGJUBeinbMWwOeHA6N7\r\nn+N8KLMz5ZlJ3iRpYyu7ap755EoZSmVnYOISAbOlbCfi2zsqxdOQ3ulqP7Qo\r\nqdjuFoXxJ3+3W66iqIBpSpk0cPzhrtx0o2Pd/bf51/pP7slmN+95HsWGIBYx\r\nHkxcmV6fzY8kuW6lDwIZ63v2Baa6bCpZboG91YsQCuK6S4q2OhCglmgxPobj\r\nI7DpKLipn77u8V5xI76XEQ3NwHvTGg6hVoSMDAONmPLjHkiFcJvJ8t/zMj+x\r\nP8FbySnLyleXOeZBRq0sRHONux9wKUSzgG7ZrdySVd4avgkC5qhpnGoLRPuF\r\nMrwcaCgosV0BCh1xwzqGivss+auW+JdZBex/XGoUsd8ch4genfw7ySX/6S86\r\n/51YfmT/XoEGYscADPFhCDFths0gvpLIi+d7fcBDq5FoEwc8ztjfeYDr9g3P\r\nQ9V8y+ooEw6TzYqKq9LOYt3Op0SZqdHEKmY=\r\n=ahn+\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/es-get-iterator_1.1.3_1673596182750_0.1227648994005146"}, "_hasShrinkwrap": false}}, "time": {"created": "2019-11-26T04:54:06.816Z", "1.0.0": "2019-11-26T04:54:06.917Z", "modified": "2023-01-13T07:49:43.021Z", "1.0.1": "2019-11-28T06:27:39.030Z", "1.0.2": "2019-12-16T21:01:16.996Z", "1.1.0": "2020-01-26T05:51:13.222Z", "1.1.1": "2020-11-06T17:49:25.923Z", "1.1.2": "2021-01-26T20:48:41.155Z", "1.1.3": "2023-01-13T07:49:42.931Z"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "description": "Get an iterator for any JS language value. Works robustly across all environments, all versions.", "homepage": "https://github.com/ljharb/es-get-iterator#readme", "keywords": ["iterator", "Symbol.iterator", "iterable", "collection", "next", "iteration"], "repository": {"type": "git", "url": "git+https://github.com/ljharb/es-get-iterator.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/ljharb/es-get-iterator/issues"}, "license": "MIT", "readme": "# es-get-iterator <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![dependency status][deps-svg]][deps-url]\n[![dev dependency status][dev-deps-svg]][dev-deps-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\nGet an iterator for any JS language value. Works robustly across all environments, all versions.\n\nIn modern engines, `value[Symbol.iterator]()` is sufficient to produce an iterator (an object with a `.next` method) for that object. However, older engines:\n - may lack `Symbol` support altogether\n - may have `Symbol.iterator` but not implement it on everything it should, like arguments objects\n - may have `Map` and `Set`, but a non-standard name for the iterator-producing method (`.iterator` or `['@@iterator']`, eg)\n - may be old versions of Firefox that produce values until they throw a StopIteration exception, rather than having iteration result objects\n - may be polyfilled/shimmed/shammed, with `es6-shim` or `core-js` or similar\n\nThis library attempts to provide an abstraction over all that complexity!\n\nIn node v13+, `exports` is used to provide a lean implementation that lacks all the complexity described above, in combination with the `browser` field so that bundlers will pick up the proper implementation.\n\n## Targeting browsers with Symbol support\n\nIf you are targeting browsers that definitely all have Symbol support, then you can configure your bundler to replace `require('has-symbols')()` with a literal `true`, which should allow dead code elimination to reduce the size of the bundled code.\n\n### With `@rollup/plugin-replace`\n\n```js\n// rollup.config.js\n\nimport replace from '@rollup/plugin-replace';\n\nexport default {\n\t...\n\tplugins: [\n\t\treplace({\n\t\t\t\"require('has-symbols')()\": 'true',\n\t\t\tdelimiters: ['', '']\n\t\t})\n\t]\n};\n```\n\n## Example\n\n```js\nvar getIterator = require('es-get-iterator');\nvar assert = require('assert');\n\nvar iterator = getIterator('a 💩');\nassert.deepEqual(\n\t[iterator.next(), iterator.next(), iterator.next(), iterator.next()],\n\t[{ done: false, value: 'a' }, { done: false, value: ' ' }, { done: false, value: '💩' }, { done: true, value: undefined }]\n);\n\nvar iterator = getIterator([1, 2]);\nassert.deepEqual(\n\t[iterator.next(), iterator.next(), iterator.next()],\n\t[{ done: false, value: 1 }, { done: false, value: 2 }, { done: true, value: undefined }]\n);\n\nvar iterator = getIterator(new Set([1, 2]));\nassert.deepEqual(\n\t[iterator.next(), iterator.next(), iterator.next()],\n\t[{ done: false, value: 1 }, { done: false, value: 2 }, { done: true, value: undefined }]\n);\n\nvar iterator = getIterator(new Map([[1, 2], [3, 4]]));\nassert.deepEqual(\n\t[iterator.next(), iterator.next(), iterator.next()],\n\t[{ done: false, value: [1, 2] }, { done: false, value: [3, 4] }, { done: true, value: undefined }]\n);\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n[package-url]: https://npmjs.org/package/es-get-iterator\n[npm-version-svg]: https://versionbadg.es/ljharb/es-get-iterator.svg\n[deps-svg]: https://david-dm.org/ljharb/es-get-iterator.svg\n[deps-url]: https://david-dm.org/ljharb/es-get-iterator\n[dev-deps-svg]: https://david-dm.org/ljharb/es-get-iterator/dev-status.svg\n[dev-deps-url]: https://david-dm.org/ljharb/es-get-iterator#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/es-get-iterator.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/es-get-iterator.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/es-get-iterator.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=es-get-iterator\n", "readmeFilename": "README.md"}