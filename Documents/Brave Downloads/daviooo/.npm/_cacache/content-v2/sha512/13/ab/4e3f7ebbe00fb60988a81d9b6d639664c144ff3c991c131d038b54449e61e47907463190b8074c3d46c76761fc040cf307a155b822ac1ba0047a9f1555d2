{"_id": "colors", "_rev": "620-ee21dda61dc54deed8891f616c4661c4", "name": "colors", "dist-tags": {"next": "1.2.0-rc0", "latest": "1.4.0"}, "versions": {"0.3.0": {"name": "colors", "version": "0.3.0", "author": {"name": "Marak Squires"}, "_id": "colors@0.3.0", "dist": {"shasum": "c247d64d34db0ca4dc8e43f3ecd6da98d0af94e7", "tarball": "https://registry.npmjs.org/colors/-/colors-0.3.0.tgz", "integrity": "sha512-zRIkNRjxdyFV2Vuq0Bh8hL/rWgQsBM19aB6Uq9CMot2olUuD1DEPon9SB3GZNDrfOojb6a74AQhSM5BKrAr9tA==", "signatures": [{"sig": "MEUCICRqPL7VdkrAHuzrVpv6dX8WD+S94YEPC+hQFbdIYJq4AiEA+CH3w4QZe+VLar1k8wfi074wEbLrDjWehJxhsNg3Scc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "colors", "engine": ["node >=0.1.90"], "engines": {"node": "*"}, "repository": {"url": "http://github.com/Marak/colors.js.git", "type": "git"}, "_npmVersion": "0.2.7-2", "description": "get colors in your node.js console like what", "directories": {}, "_nodeVersion": "v0.3.1-pre", "_nodeSupported": true}, "0.5.0": {"name": "colors", "version": "0.5.0", "author": {"name": "Marak Squires"}, "_id": "colors@0.5.0", "dist": {"shasum": "ac3ed125fcd8ccbb939b796117bf05d5f15c3e67", "tarball": "https://registry.npmjs.org/colors/-/colors-0.5.0.tgz", "integrity": "sha512-DJtS4jZW42qETwIXy2DzMEdR5CUaqKMikEpbVwezGE3d4HQAtqymihNHe/Sei9oenjJRRhkx62EWseRZZPH3GQ==", "signatures": [{"sig": "MEUCIEJQIb7RlHszoLxFAdX/FB8CAKRZDJi3FeWiAZ+NamikAiEAzqgWkuXs0SmiFzNgSv01m/PKTzAs3X8ZQYhFXm7oI9w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "colors", "files": [""], "engine": ["node >=0.1.90"], "engines": {"node": "*"}, "repository": {"url": "git://github.com/Marak/colors.js.git", "type": "git"}, "_npmVersion": "0.3.14", "description": "get colors in your node.js console like what", "directories": {}, "_nodeVersion": "v0.5.0-pre", "_defaultsLoaded": true, "_engineSupported": true}, "0.5.1": {"name": "colors", "version": "0.5.1", "author": {"name": "Marak Squires"}, "_id": "colors@0.5.1", "maintainers": [{"name": "marak", "email": "<EMAIL>"}], "dist": {"shasum": "7d0023eaeb154e8ee9fce75dcb923d0ed1667774", "tarball": "https://registry.npmjs.org/colors/-/colors-0.5.1.tgz", "integrity": "sha512-XjsuUwpDeY98+yz959OlUK6m7mLBM+1MEG5oaenfuQnNnrQk1WvtcvFgN3FNDP3f2NmZ211t0mNEfSEN1h0eIg==", "signatures": [{"sig": "MEUCIQC/gRfqd7UYjhgybWekUSrXq3sUWG++iWDVC6CeKpZbywIgPZqtCDXNK5bhlgV3Vk4M/iDXR2sbkZSD7dTM8CumbAo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "colors", "engines": {"node": ">=0.1.90"}, "repository": {"url": "git://github.com/Marak/colors.js.git", "type": "git"}, "_npmVersion": "1.0.30", "description": "get colors in your node.js console like what", "directories": {}, "_nodeVersion": "v0.4.10", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/colors/0.5.1/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.6.0": {"name": "colors", "version": "0.6.0", "author": {"name": "Marak Squires"}, "_id": "colors@0.6.0", "maintainers": [{"name": "marak", "email": "<EMAIL>"}], "dist": {"shasum": "07ec10d8ac4f5a2e78f8d820e3e7832b3b463cad", "tarball": "https://registry.npmjs.org/colors/-/colors-0.6.0.tgz", "integrity": "sha512-2V4kRAScZXSowesJtXO7wOul0LQ8uXfTQwNx8g+zTOAGy8ukY1/H/jR+Wy2DVOZQXFLlrocllyvYuNQkbRgzGw==", "signatures": [{"sig": "MEYCIQCtaCpR63fV4htFwZwnLTcG0axBWlZbp/DE0ngxYvhGwwIhAIDRNU7RxomKCz8hEZPHFQvWZo5rvADZOa/bkGlWlIE1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "colors", "engines": {"node": ">=0.1.90"}, "_npmUser": {"name": "marak", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Marak/colors.js.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "get colors in your node.js console like what", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.6.0-1": {"name": "colors", "version": "0.6.0-1", "author": {"name": "Marak Squires"}, "_id": "colors@0.6.0-1", "maintainers": [{"name": "marak", "email": "<EMAIL>"}], "dist": {"shasum": "6dbb68ceb8bc60f2b313dcc5ce1599f06d19e67a", "tarball": "https://registry.npmjs.org/colors/-/colors-0.6.0-1.tgz", "integrity": "sha512-Za<PERSON>tySU44lmZRP6M+CovFWnu7QnxLTsr/3wURb7BCOV1/gKjUb/3uu3NsLR+fvA2Jfs6sNfwcVq0Tp2mWYbuxg==", "signatures": [{"sig": "MEYCIQDzZWDH/YD6i9KDQWNwABpgQy0llB+/yL4lOM7YUZDkaQIhAMOFXVTgP9xFhkso+ghmzmlPGzUpYvpkBWrDXa5/C/3V", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "colors", "engines": {"node": ">=0.1.90"}, "_npmUser": {"name": "marak", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/Marak/colors.js.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "get colors in your node.js console like what", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.6.1": {"name": "colors", "version": "0.6.1", "keywords": ["ansi", "terminal", "colors"], "author": {"name": "Marak Squires"}, "_id": "colors@0.6.1", "maintainers": [{"name": "marak", "email": "<EMAIL>"}], "homepage": "https://github.com/Marak/colors.js", "bugs": {"url": "https://github.com/Marak/colors.js/issues"}, "dist": {"shasum": "59c7799f6c91e0e15802980a98ed138b3c78f4e9", "tarball": "https://registry.npmjs.org/colors/-/colors-0.6.1.tgz", "integrity": "sha512-DdUIWF0E63UvZTBYYlE7GDg2fdN7VJcOcL8uI9ZZt4/6P4swW4gha0JTScEWuIAsmYFvyp5P2aodEf6TTJ2Ikg==", "signatures": [{"sig": "MEYCIQChrnm+1rX25LoUiWhKPSX+OQnqUKHRJM1f6UxTNY8+4QIhAIZcfu7wjrosVK7CUnSKqWY7Ks6MIaV3yMtwTUi4c5zz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "colors", "_from": ".", "engines": {"node": ">=0.1.90"}, "_npmUser": {"name": "marak", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/Marak/colors.js.git", "type": "git"}, "_npmVersion": "1.2.30", "description": "get colors in your node.js console like what", "directories": {}}, "0.6.2": {"name": "colors", "version": "0.6.2", "keywords": ["ansi", "terminal", "colors"], "author": {"name": "Marak Squires"}, "_id": "colors@0.6.2", "maintainers": [{"name": "marak", "email": "<EMAIL>"}], "homepage": "https://github.com/Marak/colors.js", "bugs": {"url": "https://github.com/Marak/colors.js/issues"}, "dist": {"shasum": "2423fe6678ac0c5dae8852e5d0e5be08c997abcc", "tarball": "https://registry.npmjs.org/colors/-/colors-0.6.2.tgz", "integrity": "sha512-OsSVtHK8Ir8r3+Fxw/b4jS1ZLPXkV6ZxDRJQzeD7qo0SqMXWrHDM71DgYzPMHY8SFJ0Ao+nNU2p1MmwdzKqPrw==", "signatures": [{"sig": "MEUCIQDcDfFMtmn0LCID1ee4ZZJhcuC/RAy2l/2chcDDTJeO0AIgNZcKjmKAjSp09ifwdY1ayhxk99a5Lp8mvX8ZwCnhhEM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "colors", "_from": ".", "engines": {"node": ">=0.1.90"}, "_npmUser": {"name": "marak", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/Marak/colors.js.git", "type": "git"}, "_npmVersion": "1.2.30", "description": "get colors in your node.js console like what", "directories": {}}, "1.0.0": {"name": "colors", "version": "1.0.0", "keywords": ["ansi", "terminal", "colors"], "author": {"name": "Marak Squires"}, "license": "MIT", "_id": "colors@1.0.0", "maintainers": [{"name": "marak", "email": "<EMAIL>"}], "homepage": "https://github.com/Marak/colors.js", "bugs": {"url": "https://github.com/Marak/colors.js/issues"}, "dist": {"shasum": "d907bed5e1dcbdee6a0a5533f76855c7df506580", "tarball": "https://registry.npmjs.org/colors/-/colors-1.0.0.tgz", "integrity": "sha512-S0Ym2OuHlhVUE3L3VzBUjIdVaF1suqn3FZEAQIQLCzBYhV3/58I8MIuFqdoqeX6RP7qmcEoSz2QUaMw9HokQVA==", "signatures": [{"sig": "MEUCIBTi95HV2PtCj8M+0J0XNahvShn0ge4Q8yePbCb0MolYAiEA6UgCp4226uF1aA5Q5lIaKQ9GidWuFTxN0F0JG9XYr10=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index", "_from": ".", "_shasum": "d907bed5e1dcbdee6a0a5533f76855c7df506580", "engines": {"node": ">=0.1.90"}, "gitHead": "fee9d2475f03729ee6567c38be7686540b7a3cdf", "scripts": {"test": "node tests/basic-test.js && node tests/safe-test.js"}, "_npmUser": {"name": "marak", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/Marak/colors.js.git", "type": "git"}, "_npmVersion": "2.0.2", "description": "get colors in your node.js console", "directories": {}, "_nodeVersion": "0.11.13"}, "1.0.1": {"name": "colors", "version": "1.0.1", "keywords": ["ansi", "terminal", "colors"], "author": {"name": "Marak Squires"}, "license": "MIT", "_id": "colors@1.0.1", "maintainers": [{"name": "marak", "email": "<EMAIL>"}], "homepage": "https://github.com/Marak/colors.js", "bugs": {"url": "https://github.com/Marak/colors.js/issues"}, "dist": {"shasum": "3414b3ebe708dd2e0e299074af0acfda20dc7764", "tarball": "https://registry.npmjs.org/colors/-/colors-1.0.1.tgz", "integrity": "sha512-alinZFcKeAOIiu4iL3/WaI9XZ42VmPJuYOyZOBDXklvjpVyXjRQ099og4fDqHG0fl9U2AlUIgLC/g7HPrnRWmQ==", "signatures": [{"sig": "MEUCIQD4KIwJvFzjXPycd+9LIlfpNxm4lOJug8BHNQKi9gpd+QIgAYnI0FC3SVHYBBhP389jMbncv1PnqVyXKJ1mNV4b+sc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index", "_from": ".", "_shasum": "3414b3ebe708dd2e0e299074af0acfda20dc7764", "engines": {"node": ">=0.1.90"}, "gitHead": "ea942de28794b7a1a1fff0caad30c8268411e9ed", "scripts": {"test": "node tests/basic-test.js && node tests/safe-test.js"}, "_npmUser": {"name": "marak", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/Marak/colors.js.git", "type": "git"}, "_npmVersion": "2.0.2", "description": "get colors in your node.js console", "directories": {}, "_nodeVersion": "0.11.13"}, "1.0.2": {"name": "colors", "version": "1.0.2", "keywords": ["ansi", "terminal", "colors"], "author": {"name": "Marak Squires"}, "license": "MIT", "_id": "colors@1.0.2", "maintainers": [{"name": "marak", "email": "<EMAIL>"}], "homepage": "https://github.com/Marak/colors.js", "bugs": {"url": "https://github.com/Marak/colors.js/issues"}, "dist": {"shasum": "6788c0d667206b8262ec5de49dd6cc7d662b5b42", "tarball": "https://registry.npmjs.org/colors/-/colors-1.0.2.tgz", "integrity": "sha512-AyUNq2E52kBd1dC1lnXfW6DYmxXkcMEN3K/9xed329BHeGIDJ46zXFg7nbE7FGfXMp/xgiX4RA8omrQGcuJB5A==", "signatures": [{"sig": "MEUCIAlykj7ywZVEgOJie62SqvTqJFZS6WXV2PyagZTaBR0jAiEAmH66t+vJ1dX012J5Q/MTf6NqmR8C85C66icXHvCMmp0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index", "_from": ".", "_shasum": "6788c0d667206b8262ec5de49dd6cc7d662b5b42", "engines": {"node": ">=0.1.90"}, "gitHead": "ac1429ddf307c218336fc02a0da818c8a6c6837f", "scripts": {"test": "node tests/basic-test.js && node tests/safe-test.js"}, "_npmUser": {"name": "marak", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/Marak/colors.js.git", "type": "git"}, "_npmVersion": "2.0.2", "description": "get colors in your node.js console", "directories": {}, "_nodeVersion": "0.11.13"}, "1.0.3": {"name": "colors", "version": "1.0.3", "keywords": ["ansi", "terminal", "colors"], "author": {"name": "Marak Squires"}, "license": "MIT", "_id": "colors@1.0.3", "maintainers": [{"name": "marak", "email": "<EMAIL>"}], "homepage": "https://github.com/Marak/colors.js", "bugs": {"url": "https://github.com/Marak/colors.js/issues"}, "dist": {"shasum": "0433f44d809680fdeb60ed260f1b0c262e82a40b", "tarball": "https://registry.npmjs.org/colors/-/colors-1.0.3.tgz", "integrity": "sha512-pFGrxThWcWQ2MsAz6RtgeWe4NK2kUE1WfsrvvlctdII745EW9I0yflqhe7++M5LEc7bV2c/9/5zc8sFcpL0Drw==", "signatures": [{"sig": "MEYCIQDN6Kk7FVvcFzZZ7l3/QE/ovdLAKjFcais1TI9RCZuOcAIhAPZ/gKL0BalAuVxwnyn2a6Um1oeWTwPOyk1IS7xgF5Nl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index", "_from": ".", "_shasum": "0433f44d809680fdeb60ed260f1b0c262e82a40b", "engines": {"node": ">=0.1.90"}, "gitHead": "e9e6557cc0fa26dba1a20b0d45e92de982f4047c", "scripts": {"test": "node tests/basic-test.js && node tests/safe-test.js"}, "_npmUser": {"name": "marak", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/Marak/colors.js.git", "type": "git"}, "_npmVersion": "2.0.2", "description": "get colors in your node.js console", "directories": {}, "_nodeVersion": "0.11.13"}, "1.1.0": {"name": "colors", "version": "1.1.0", "keywords": ["ansi", "terminal", "colors"], "author": {"name": "Marak Squires"}, "license": "MIT", "_id": "colors@1.1.0", "maintainers": [{"name": "marak", "email": "<EMAIL>"}], "homepage": "https://github.com/Marak/colors.js", "bugs": {"url": "https://github.com/Marak/colors.js/issues"}, "dist": {"shasum": "ea2b7dfd38a7bb631323d29b108fa78fd16dc523", "tarball": "https://registry.npmjs.org/colors/-/colors-1.1.0.tgz", "integrity": "sha512-CZfU5tRW4Jf20pFPByP/2sT2Ra2qM3B+Eb9ZZxpJEvR07yy3J2Kh9b8Bu5Gn4i3mcc/ViWBM6PRvLJeBBCjvJQ==", "signatures": [{"sig": "MEYCIQC9PavMsnIyFaZe/HX0ReQ61sf6TmV/qF5C9pLSkRsoIwIhANCZQngc0JpHT1Lozvyo0/29MOQ6YAIN34ylYNmsM0KQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib", "_from": ".", "files": ["examples", "lib", "LICENSE", "safe.js", "themes"], "_shasum": "ea2b7dfd38a7bb631323d29b108fa78fd16dc523", "engines": {"node": ">=0.1.90"}, "gitHead": "b9ff17af7ce2548a93e34001dcb75777561cae17", "scripts": {"test": "node tests/basic-test.js && node tests/safe-test.js"}, "_npmUser": {"name": "marak", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/Marak/colors.js.git", "type": "git"}, "_npmVersion": "2.1.8", "description": "get colors in your node.js console", "directories": {}, "_nodeVersion": "0.11.13"}, "1.1.1": {"name": "colors", "version": "1.1.1", "keywords": ["ansi", "terminal", "colors"], "author": {"name": "Marak Squires"}, "license": "MIT", "_id": "colors@1.1.1", "maintainers": [{"name": "marak", "email": "<EMAIL>"}], "homepage": "https://github.com/Marak/colors.js", "bugs": {"url": "https://github.com/Marak/colors.js/issues"}, "dist": {"shasum": "6fc916ea57eca2edb7aa1771f4a8c21789f79e0b", "tarball": "https://registry.npmjs.org/colors/-/colors-1.1.1.tgz", "integrity": "sha512-OjybZda/ap4Vr+7hIh98OThjZkbZbNSt339spVRi1foCyJwkCBjD9DEshsSwkPqyoJiuGO3yQx0HKhDwGH4KyA==", "signatures": [{"sig": "MEYCIQDr9XxPpyuLz8XLv95B6sSu344HU2clyR2SSbI5PlY2KQIhANjlrGTIYBPDZKqnRdvNp/F2uuRjrjX+k1eW7CdxVIlW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib", "_from": ".", "files": ["examples", "lib", "LICENSE", "safe.js", "themes"], "_shasum": "6fc916ea57eca2edb7aa1771f4a8c21789f79e0b", "engines": {"node": ">=0.1.90"}, "gitHead": "5958f1677d827fec37275427476499d84613e4a4", "scripts": {"test": "node tests/basic-test.js && node tests/safe-test.js"}, "_npmUser": {"name": "marak", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/Marak/colors.js.git", "type": "git"}, "_npmVersion": "2.1.8", "description": "get colors in your node.js console", "directories": {}, "_nodeVersion": "0.11.13"}, "1.1.2": {"name": "colors", "version": "1.1.2", "keywords": ["ansi", "terminal", "colors"], "author": {"name": "Marak Squires"}, "license": "MIT", "_id": "colors@1.1.2", "maintainers": [{"name": "marak", "email": "<EMAIL>"}], "homepage": "https://github.com/Marak/colors.js", "bugs": {"url": "https://github.com/Marak/colors.js/issues"}, "dist": {"shasum": "168a4701756b6a7f51a12ce0c97bfa28c084ed63", "tarball": "https://registry.npmjs.org/colors/-/colors-1.1.2.tgz", "integrity": "sha512-ENwblkFQpqqia6b++zLD/KUWafYlVY/UNnAp7oz7LY7E924wmpye416wBOmvv/HMWzl8gL1kJlfvId/1Dg176w==", "signatures": [{"sig": "MEUCIGQaK2OhJC9JUHqdf6b3JHbBzUPNwm+6VHgGtPZvBHsmAiEAq6gFf4dZ+ldi8kOOSF8TngHTPkTBDJt8XKkqGZyzJpU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib", "_from": ".", "files": ["examples", "lib", "LICENSE", "safe.js", "themes"], "_shasum": "168a4701756b6a7f51a12ce0c97bfa28c084ed63", "engines": {"node": ">=0.1.90"}, "gitHead": "8bf2ad9fa695dcb30b7e9fd83691b139fd6655c4", "scripts": {"test": "node tests/basic-test.js && node tests/safe-test.js"}, "_npmUser": {"name": "marak", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/Marak/colors.js.git", "type": "git"}, "_npmVersion": "2.1.8", "description": "get colors in your node.js console", "directories": {}, "_nodeVersion": "0.11.13"}, "1.2.0-rc0": {"name": "colors", "version": "1.2.0-rc0", "keywords": ["ansi", "terminal", "colors"], "author": {"name": "Marak Squires"}, "license": "MIT", "_id": "colors@1.2.0-rc0", "maintainers": [{"name": "dabh", "email": "<EMAIL>"}, {"name": "marak", "email": "<EMAIL>"}], "homepage": "https://github.com/Marak/colors.js", "bugs": {"url": "https://github.com/Marak/colors.js/issues"}, "dist": {"shasum": "55fa8c0c4454606756cd96ebcf520a9fcf743d11", "tarball": "https://registry.npmjs.org/colors/-/colors-1.2.0-rc0.tgz", "fileCount": 19, "integrity": "sha512-NCYIuw9bgeBSokEO9eMoKtyXscuNdn/oaMWWDBXr+fp/BHd5HBhVP1FWHkRkCXPqZgEfWeP4ID+xM4rQPjnXjA==", "signatures": [{"sig": "MEUCIQDqBFAfO9JTMExeE5Z+VcPh3UHmeiK+0EyMS/CHIFykOgIgM2d2sdY3z9ZkRs0eMR8w95/HWgzgay03ZEkYEcTgu+c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31382}, "main": "lib/index.js", "files": ["examples", "lib", "LICENSE", "safe.js", "themes"], "readme": "# colors.js\n[![Build Status](https://travis-ci.org/Marak/colors.js.svg?branch=master)](https://travis-ci.org/Marak/colors.js)\n[![version](https://img.shields.io/npm/v/colors.svg)](https://www.npmjs.org/package/colors)\n[![dependencies](https://david-dm.org/Marak/colors.js.svg)](https://david-dm.org/Marak/colors.js)\n[![devDependencies](https://david-dm.org/Marak/colors.js/dev-status.svg)](https://david-dm.org/Marak/colors.js#info=devDependencies)\n\n## get color and style in your node.js console\n\n![Demo](https://raw.githubusercontent.com/Marak/colors.js/master/screenshots/colors.png)\n\n## Installation\n\n    npm install colors\n\n## colors and styles!\n\n### text colors\n\n  - black\n  - red\n  - green\n  - yellow\n  - blue\n  - magenta\n  - cyan\n  - white\n  - gray\n  - grey\n\n### background colors\n\n  - bgBlack\n  - bgRed\n  - bgGreen\n  - bgYellow\n  - bgBlue\n  - bgMagenta\n  - bgCyan\n  - bgWhite\n\n### styles\n\n  - reset\n  - bold\n  - dim\n  - italic\n  - underline\n  - inverse\n  - hidden\n  - strikethrough\n\n### extras\n\n  - rainbow\n  - zebra\n  - america\n  - trap\n  - random\n\n\n## Usage\n\nBy popular demand, `colors` now ships with two types of usages!\n\nThe super nifty way\n\n```js\nvar colors = require('colors');\n\nconsole.log('hello'.green); // outputs green text\nconsole.log('i like cake and pies'.underline.red) // outputs red underlined text\nconsole.log('inverse the color'.inverse); // inverses the color\nconsole.log('OMG Rainbows!'.rainbow); // rainbow\nconsole.log('Run the trap'.trap); // Drops the bass\n\n```\n\nor a slightly less nifty way which doesn't extend `String.prototype`\n\n```js\nvar colors = require('colors/safe');\n\nconsole.log(colors.green('hello')); // outputs green text\nconsole.log(colors.red.underline('i like cake and pies')) // outputs red underlined text\nconsole.log(colors.inverse('inverse the color')); // inverses the color\nconsole.log(colors.rainbow('OMG Rainbows!')); // rainbow\nconsole.log(colors.trap('Run the trap')); // Drops the bass\n\n```\n\nI prefer the first way. Some people seem to be afraid of extending `String.prototype` and prefer the second way. \n\nIf you are writing good code you will never have an issue with the first approach. If you really don't want to touch `String.prototype`, the second usage will not touch `String` native object.\n\n## Disabling Colors\n\nTo disable colors you can pass the following arguments in the command line to your application:\n\n```bash\nnode myapp.js --no-color\n```\n\n## Console.log [string substitution](http://nodejs.org/docs/latest/api/console.html#console_console_log_data)\n\n```js\nvar name = 'Marak';\nconsole.log(colors.green('Hello %s'), name);\n// outputs -> 'Hello Marak'\n```\n\n## Custom themes\n\n### Using standard API\n\n```js\n\nvar colors = require('colors');\n\ncolors.setTheme({\n  silly: 'rainbow',\n  input: 'grey',\n  verbose: 'cyan',\n  prompt: 'grey',\n  info: 'green',\n  data: 'grey',\n  help: 'cyan',\n  warn: 'yellow',\n  debug: 'blue',\n  error: 'red'\n});\n\n// outputs red text\nconsole.log(\"this is an error\".error);\n\n// outputs yellow text\nconsole.log(\"this is a warning\".warn);\n```\n\n### Using string safe API\n\n```js\nvar colors = require('colors/safe');\n\n// set single property\nvar error = colors.red;\nerror('this is red');\n\n// set theme\ncolors.setTheme({\n  silly: 'rainbow',\n  input: 'grey',\n  verbose: 'cyan',\n  prompt: 'grey',\n  info: 'green',\n  data: 'grey',\n  help: 'cyan',\n  warn: 'yellow',\n  debug: 'blue',\n  error: 'red'\n});\n\n// outputs red text\nconsole.log(colors.error(\"this is an error\"));\n\n// outputs yellow text\nconsole.log(colors.warn(\"this is a warning\"));\n\n```\n\n### Combining Colors\n\n```javascript\nvar colors = require('colors');\n\ncolors.setTheme({\n  custom: ['red', 'underline']\n});\n\nconsole.log('test'.custom);\n```\n\n*Protip: There is a secret undocumented style in `colors`. If you find the style you can summon him.*\n", "engines": {"node": ">=0.1.90"}, "gitHead": "15abdf6f8577c0540abf1dec52bef504fea8a71f", "scripts": {"test": "node tests/basic-test.js && node tests/safe-test.js"}, "_npmUser": {"name": "dabh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/Marak/colors.js.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "get colors in your node.js console", "directories": {}, "_nodeVersion": "8.9.4", "_hasShrinkwrap": false, "readmeFilename": "ReadMe.md", "_npmOperationalInternal": {"tmp": "tmp/colors_1.2.0-rc0_1518821109073_0.13931783279293097", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "colors", "version": "1.2.0", "keywords": ["ansi", "terminal", "colors"], "author": {"name": "Marak Squires"}, "license": "MIT", "_id": "colors@1.2.0", "maintainers": [{"name": "dabh", "email": "<EMAIL>"}, {"name": "marak", "email": "<EMAIL>"}], "homepage": "https://github.com/Marak/colors.js", "bugs": {"url": "https://github.com/Marak/colors.js/issues"}, "dist": {"shasum": "24ec7283fcc91557801b22521e4619fedc7ca306", "tarball": "https://registry.npmjs.org/colors/-/colors-1.2.0.tgz", "fileCount": 19, "integrity": "sha512-lweugcX5nailCqZBttArTojZZpHGWhmFJX78KJHlxwhM8tLAy5QCgRgRxrubrksdvA+2Y3inWG5TToyyjL82BQ==", "signatures": [{"sig": "MEQCIETyvbGDNQrHQbo0/Obgft6UufJBVZo99rDkj5oEd7x2AiAG24gncbOe4a6AyFipG+TnTAS1snLT5ma6BLXvH1Iklg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31582}, "main": "lib/index.js", "files": ["examples", "lib", "LICENSE", "safe.js", "themes"], "engines": {"node": ">=0.1.90"}, "gitHead": "cc857f297748633785b2bcf555f226930c8dee4d", "scripts": {"test": "node tests/basic-test.js && node tests/safe-test.js"}, "_npmUser": {"name": "dabh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/Marak/colors.js.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "get colors in your node.js console", "directories": {}, "_nodeVersion": "8.9.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/colors_1.2.0_1520664520245_0.45840454675504927", "host": "s3://npm-registry-packages"}}, "1.2.1": {"name": "colors", "version": "1.2.1", "keywords": ["ansi", "terminal", "colors"], "author": {"name": "Marak Squires"}, "license": "MIT", "_id": "colors@1.2.1", "maintainers": [{"name": "dabh", "email": "<EMAIL>"}, {"name": "marak", "email": "<EMAIL>"}], "homepage": "https://github.com/Marak/colors.js", "bugs": {"url": "https://github.com/Marak/colors.js/issues"}, "dist": {"shasum": "f4a3d302976aaf042356ba1ade3b1a2c62d9d794", "tarball": "https://registry.npmjs.org/colors/-/colors-1.2.1.tgz", "fileCount": 21, "integrity": "sha512-s8+wktIuDSLffCywiwSxQOMqtPxML11a/dtHE17tMn4B1MSWw/C22EKf7M2KGUBcDaVFEGT+S8N02geDXeuNKg==", "signatures": [{"sig": "MEQCIGq+Nj3qppv1bBpIu2+GdLJta+46a5iqf+WneBGzq2zvAiBqDUHRxJGhTAFV9zzK4kRH1+kCBvUi/KyOSYMid5z2Lg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37634}, "main": "lib/index.js", "files": ["examples", "lib", "LICENSE", "safe.js", "themes", "index.d.ts", "safe.d.ts"], "engines": {"node": ">=0.1.90"}, "gitHead": "dc82cc01d00df2f1cdcf90cba699a5a61da70446", "scripts": {"test": "node tests/basic-test.js && node tests/safe-test.js"}, "_npmUser": {"name": "dabh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/Marak/colors.js.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "get colors in your node.js console", "directories": {}, "_nodeVersion": "8.9.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/colors_1.2.1_1520833946745_0.24793897380744756", "host": "s3://npm-registry-packages"}}, "1.2.2": {"name": "colors", "version": "1.2.2", "keywords": ["ansi", "terminal", "colors"], "author": {"name": "Marak Squires"}, "license": "MIT", "_id": "colors@1.2.2", "maintainers": [{"name": "dabh", "email": "<EMAIL>"}, {"name": "marak", "email": "<EMAIL>"}], "homepage": "https://github.com/Marak/colors.js", "bugs": {"url": "https://github.com/Marak/colors.js/issues"}, "dist": {"shasum": "325e0c9ae2840dd0b8fb2d57be13b97f134af11a", "tarball": "https://registry.npmjs.org/colors/-/colors-1.2.2.tgz", "fileCount": 21, "integrity": "sha512-/8jUaPrx/L4tv/1bsZiUhURANUYWWs/cIT0QaYcgRunB29cqAAOFov+mH4FAGZ/o/c4t0LwZLq6V7GcgPJMFyg==", "signatures": [{"sig": "MEUCIQDLNw5Wl7W9tJm+rgQjfXOfrBD5zFdIfIms2Wbn2lnPYgIgdQMjGAk+phdUth4YM4RSPOvLlJsNdrgTkn2nPcVoBEM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36582, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa52ODCRA9TVsSAnZWagAAKbAP/imFLF4cYsEwnMW5yXhQ\non1ZJe39u7cu7up4RlTyPFwdW5o+vt1X3DG0QL3+EMtDj3eArtoXHslwXv35\n2qTpEl6R2RSxS1Q2Mc7Iyad41ip2PBlraiiI5ALphCu5fRNNOKYV4BxAeBjB\nExxHBtktdyi2kDJb4ITj7KMZI5e7CyFW7201TrGNhMRD7fQpTbEmYSyzfOy1\nBtTf1VpZtGXD7z4KIerjjqCSTV6rtsObvxJyzKnrVB3WLQ/qLMYFi8RDftwX\ng4Phl8TMQ8TBMAte6Yl+NF2vdZmhqOPtu/2HgRY+fg4NmnOsP/hzNvc3ww0U\nsC5k+HWNYEQZ237fccl/DcrqXj6Pfz6yfq1LKamrUVMZABqKSXnon9MbDkiU\nblhkyhqyu6TwiHOaU0fCvhwatTzpCwzFywnDgdZfIYTkWTrkojIaI6vuK8/d\n85p/2mhhjCPHxz0x+JuUH86ZgpyXnsycZysdlj/k6PTsfE2wVOqeK1n26ZXY\nPe10indhed4mcyw+0UKeE4vcB1r0BAC5xD/Mf48G4Q6iMtDDGYHzh84pwWS2\n1x2GLoMbgnr/JRfbbkUaUEUGOxD2WmyM2UanLrokpW1PnzNhcvSura/Qdjn6\nqCxh0196OybQZVmAmH6xMLIHH7S53WFt5ccxtxcjFNE6FcX72Z38BXo0ECRk\nBQC4\r\n=N0ea\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "files": ["examples", "lib", "LICENSE", "safe.js", "themes", "index.d.ts", "safe.d.ts"], "engines": {"node": ">=0.1.90"}, "gitHead": "f35a3325b014ed7d03dc5018bf20b93a4483ead6", "scripts": {"test": "node tests/basic-test.js && node tests/safe-test.js"}, "_npmUser": {"name": "dabh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/Marak/colors.js.git", "type": "git"}, "_npmVersion": "5.8.0", "description": "get colors in your node.js console", "directories": {}, "_nodeVersion": "8.9.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/colors_1.2.2_1525113730721_0.5074781704524156", "host": "s3://npm-registry-packages"}}, "1.2.3": {"name": "colors", "version": "1.2.3", "keywords": ["ansi", "terminal", "colors"], "author": {"name": "Marak Squires"}, "license": "MIT", "_id": "colors@1.2.3", "maintainers": [{"name": "dabh", "email": "<EMAIL>"}, {"name": "marak", "email": "<EMAIL>"}], "homepage": "https://github.com/Marak/colors.js", "bugs": {"url": "https://github.com/Marak/colors.js/issues"}, "dist": {"shasum": "1b152a9c4f6c9f74bc4bb96233ad0b7983b79744", "tarball": "https://registry.npmjs.org/colors/-/colors-1.2.3.tgz", "fileCount": 21, "integrity": "sha512-qTfM2pNFeMZcLvf/RbrVAzDEVttZjFhaApfx9dplNjvHSX88Ui66zBRb/4YGob/xUWxDceirgoC1lT676asfCQ==", "signatures": [{"sig": "MEYCIQDPhJSEQ4MBGifJYbMYjungdLhpNp9XZzoxGM6zlhbr6AIhALF9fWl9WKYxzk+z5JDZHk1XafQJl7sYKWN+WbZfxgBF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37117, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa55QsCRA9TVsSAnZWagAAvAYQAKBcVWNj2B4SgtVaKU1s\nrUH7OapgQ9b8NOfy5g7el3+uqWYIiaO+8Bwct3KJMyncewWrifDS+BQ+mhbx\nUZyTz1wKo0/JXrhl5WErlWr1dkeGt1wLw1M357oeSV0ZmGbcfYFwloRCCdWt\n/Bjoi0x5xEh5ZJMmApercCMbIb8dtqRLWEd9JzlSIinem/VZCaPY/NRtFxsn\npAhtQK4eV/Rm88nOwp7bo5Prn/+YUz2UAqVg9J5d8F9Ll4mgdGbn0V9V3J2p\n+75PKrK0C+dUO93+Cq/VyrbhVsR1tC7aUBJCLwwKPO4G3PgW754Q4IQKzUdp\nlVUKcGjaqo8GCJQjvglORXLizGZUMt8VdaTeDUsGkkZ7W7UQtn8DodpCAl3B\ncxP/dInX35Rx61eKJBN5hTmTLFEN9NPu1v5FejBHPVQQCV6Fq+W4hm4t6dwc\nMYoN2DX1xyMZhBpCQh12cIZ71PIizzEyek5GCEongkcawSNUAQLgT6AO0nMM\ncgiiTXrOoDnEYVLQodrxpz17H4J6x5rRpHQpTYrqaH3qSRWnWJbHfVO1vBlj\nRUH5bwaiEGvJwouvN26vyURwqfOBR20W7MLy7sV566CcLscTopZaRrsf4Euw\nhxMDCD/V8y40QfiErVqCZ56/J2rtB3dRBcDKXdw4ss7iNHvA4wyCxQ6RXvqb\nS059\r\n=3FE3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "files": ["examples", "lib", "LICENSE", "safe.js", "themes", "index.d.ts", "safe.d.ts"], "engines": {"node": ">=0.1.90"}, "gitHead": "64a2ea25100061a6bad7448a23f6a281353e96ef", "scripts": {"lint": "eslint . --fix", "test": "node tests/basic-test.js && node tests/safe-test.js"}, "_npmUser": {"name": "dabh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/Marak/colors.js.git", "type": "git"}, "_npmVersion": "5.8.0", "description": "get colors in your node.js console", "directories": {}, "_nodeVersion": "8.9.4", "_hasShrinkwrap": false, "devDependencies": {"eslint": "^4.19.1", "eslint-config-google": "^0.9.1"}, "_npmOperationalInternal": {"tmp": "tmp/colors_1.2.3_1525126186446_0.23671168908496565", "host": "s3://npm-registry-packages"}}, "1.2.4": {"name": "colors", "version": "1.2.4", "keywords": ["ansi", "terminal", "colors"], "author": {"name": "Marak Squires"}, "license": "MIT", "_id": "colors@1.2.4", "maintainers": [{"name": "dabh", "email": "<EMAIL>"}, {"name": "marak", "email": "<EMAIL>"}], "homepage": "https://github.com/Marak/colors.js", "bugs": {"url": "https://github.com/Marak/colors.js/issues"}, "dist": {"shasum": "e0cb41d3e4b20806b3bfc27f4559f01b94bc2f7c", "tarball": "https://registry.npmjs.org/colors/-/colors-1.2.4.tgz", "fileCount": 21, "integrity": "sha512-6Y+iBnWmXL+AWtlOp2Vr6R2w5MUlNJRwR0ShVFaAb1CqWzhPOpQg4L0jxD+xpw/Nc8QJwaq3KM79QUCriY8CWQ==", "signatures": [{"sig": "MEUCIQD2RBqiMtPqpMx820fwnMe9AKRyPAYUEOEwU/Gh2WeGMQIgUDKBo2gZAd8MJYePPxjbnJ/ZZWLXZvrDXZkTFcBQK0E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37120, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa7QEnCRA9TVsSAnZWagAAI2sP/ioRpjx4vK3BfUsOhvzD\nPW9/++JL9bzIE29FLqq/8s/1eGPbKjr6CQAKVQbN2xeBOq/1N7cMkVirg7yM\nR15QhsUp/X0Kp0piC4uuVlhtj5vApJZY6JfbUuZ2OTu09vZdx0y8FZFYeDWy\n02Y4tNHcc6k/QrImx4DHpuhj6XHt4Zlo8vTs1WOd3XlzyUogkhsvehizdeS3\nWEJkNgw7Jiyf+ewamIP2Nj598fM7ZRDLhJ8lZ1WDeUgMbChvcVVaYYOaRThb\nZApY5+0SdW0O353m0bkgPP8CFAavSxgdU0oS8oMJW1MTvKuq4X6w3kRFyqbA\nEB5/nKxwNjyv3UPlnr/NMnJfsoP93UFDq4TTMBPZQN1wFfimITuQXIhwH9ej\nifBkqXs4F489NysPDGlZJpDNgm9LIK3B4QkGePqc7Hy/lf4JDQFicSOkVEGm\nyfkFXgTJBbsh4KfvovK9Gnags2ricsgvNg0WJD3bQ0+CBW6cRRdQJUvaWzSE\nAxjLx3SJbR9yNFFAqlidD5XnwGXQwVV3+l6ocHmwrhOyo51JR3WFRd2Pr0Ie\nk3YzjVbDvKHKiFdK4CztGGRWSvkyMN3J3L8yV6xJFx97fRvFA+zerp1YzgCk\nsTQvgfF5a422HGUZNa8JK24Ewun+PCy46ZNXEHZ0Pwaj6moHrpgom6D5VfOA\nh/cs\r\n=0z3l\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "files": ["examples", "lib", "LICENSE", "safe.js", "themes", "index.d.ts", "safe.d.ts"], "engines": {"node": ">=0.1.90"}, "gitHead": "32906a23ad0d84c1234358ab39dbf39f75b82ac2", "scripts": {"lint": "eslint . --fix", "test": "node tests/basic-test.js && node tests/safe-test.js"}, "_npmUser": {"name": "dabh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/Marak/colors.js.git", "type": "git"}, "_npmVersion": "5.8.0", "description": "get colors in your node.js console", "directories": {}, "_nodeVersion": "8.9.4", "_hasShrinkwrap": false, "devDependencies": {"eslint": "^4.19.1", "eslint-config-google": "^0.9.1"}, "_npmOperationalInternal": {"tmp": "tmp/colors_1.2.4_1525481766193_0.877299018573249", "host": "s3://npm-registry-packages"}}, "1.2.5": {"name": "colors", "version": "1.2.5", "keywords": ["ansi", "terminal", "colors"], "author": {"name": "Marak Squires"}, "license": "MIT", "_id": "colors@1.2.5", "maintainers": [{"name": "dabh", "email": "<EMAIL>"}, {"name": "marak", "email": "<EMAIL>"}], "homepage": "https://github.com/Marak/colors.js", "bugs": {"url": "https://github.com/Marak/colors.js/issues"}, "dist": {"shasum": "89c7ad9a374bc030df8013241f68136ed8835afc", "tarball": "https://registry.npmjs.org/colors/-/colors-1.2.5.tgz", "fileCount": 21, "integrity": "sha512-erNRLao/Y3Fv54qUa0LBB+//Uf3YwMUmdJinN20yMXm9zdKKqH9wt7R9IIVZ+K7ShzfpLV/Zg8+VyrBJYB4lpg==", "signatures": [{"sig": "MEUCIB3rFPlwV2FdHqmXJv2eWcae+eQFSHVFZ+hhCb4cD9uWAiEAoNpB7sA1IHXwfoPM81u7DSwUOST7YJQ9X3YorCF/u9U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37183, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa9TUPCRA9TVsSAnZWagAAcMIP/1ba16EnDEj6pM0451yG\n8zIN7EEQlctxJVuQD6ihsU79jkdUg0UxEtPBEpfd6CMhoLC3iNSTkxk2rrVM\n5b+9Cay7AZM3QgYsdDn2rxH1G0tQBCvDIGNPncUMskhPnNmO1QCKfUlQW9lc\nz/**********************************/NpJb652jVbkTDXaTkaQ0gBq\n0K1dsyoEdgyC9w+nFNy4eWBmliM0JHxsxV9bFCSxLXkDmCAX7N2abv/P9w6+\nr0HfEc9gWqqs7a1K7OvnaLH6sMS9dDssnoJZQ2ZfQfLE11FCgAgQ7fxJxjH5\ncTsRy+k3Wi2sdi2jzaKozVnMXDN+z3rGJtG3DEVjG7M0lPkJVXsF7WiGaxel\nMyt3MJwGVyXtgS6ohoivT7o/lwQN4rhEHmEhAFqQJopiLzI2Q0VlMZwj7314\ngGxyXjcuH47q8UhcEQ6Wqa0zj808caB8WGybGhnSJscd0g9AB6YS+0Nyey/x\nebgW1bwOpvndbiobZOksiVWvAfVTWsqOb6y+cgbe9TQJg4xCWFnSHGAAysE5\ndgOIxX8YecQqffFbXJ4RCQrpmGR9av+5Hi+kPaPxFfy6UUk/60cE2dNMoMqH\n7NaTMCZT1rcqAhDtFI6oQazFf59i9DrIH7I2EHasHOjjVsyKEE6hP/yjNtYz\nStZn\r\n=9DAp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "files": ["examples", "lib", "LICENSE", "safe.js", "themes", "index.d.ts", "safe.d.ts"], "engines": {"node": ">=0.1.90"}, "gitHead": "a8ce90c51c929aa7759cca33bccb859027385709", "scripts": {"lint": "eslint . --fix", "test": "node tests/basic-test.js && node tests/safe-test.js"}, "_npmUser": {"name": "dabh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/Marak/colors.js.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "get colors in your node.js console", "directories": {}, "_nodeVersion": "10.0.0", "_hasShrinkwrap": false, "devDependencies": {"eslint": "^4.19.1", "eslint-config-google": "^0.9.1"}, "_npmOperationalInternal": {"tmp": "tmp/colors_1.2.5_1526019342480_0.768282433974782", "host": "s3://npm-registry-packages"}}, "1.3.0": {"name": "colors", "version": "1.3.0", "keywords": ["ansi", "terminal", "colors"], "author": {"name": "Marak Squires"}, "license": "MIT", "_id": "colors@1.3.0", "maintainers": [{"name": "dabh", "email": "<EMAIL>"}, {"name": "marak", "email": "<EMAIL>"}], "homepage": "https://github.com/Marak/colors.js", "bugs": {"url": "https://github.com/Marak/colors.js/issues"}, "dist": {"shasum": "5f20c9fef6945cb1134260aab33bfbdc8295e04e", "tarball": "https://registry.npmjs.org/colors/-/colors-1.3.0.tgz", "fileCount": 21, "integrity": "sha512-EDpX3a7wHMWFA7PUHWPHNWqOxIIRSJetuwl0AS5Oi/5FMV8kWm69RTlgm00GKjBO1xFHMtBbL49yRtMMdticBw==", "signatures": [{"sig": "MEQCIAKyWnovvD0ujkexC4knkkQ9Feo67tlZ9GfxXEWKrwvSAiAOa6Q3IVbB7ckQgviU+JTkf7F6u7UfZ00K9lcJPWJnhA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37445, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbAfIgCRA9TVsSAnZWagAA7r0P/RKSj0jbws40ohxFeXRE\ntFsE/c7qEBdAhPSP1RevobLNfQeXdK3Ag4vQzAfoFphmwkqrsjxTfYIKfAsy\nVO00M9IkXf4YVzeXq9pEoUsLrE9YnYfuYvsv86iIsq0tcyZUgxH4Zlo9qshc\nrg8WE2u3s3bmYPRYQ8PKPdqyDTk8nnsnCpZwmbQzI94rU0iiYP66CUVmmgUq\nSizX0VKZx6BoWN5kVl2E2h6QmO68N0H3TRyIWXVo0XTo2M0PbLMN9TJwyJHU\nMCi/IO6WysM1w5WgH4HZdJO4PJId7t+Nc5FEbc2/lyBu9cizL5d4vRrKwu0f\nr/khiesY9qUVpa2eTo0xEH4zzxt+srS/lc6BiZJ0mMv+AQX/joX616UUOFAF\nqcgXu/qA4i48OEEsMY+Xyl/laYYuIezoA/p0tXK8W10BLsoyYgFGBp0R/6b3\nSjsu2oaPjPqlDKhRPGhbAmeeI7qh1iomZwSkSrxhbITjd3/aXEtEUtet0ZeW\nVH7GJ51EbYtwjtf4rrDdZ8c1WmzvjSbBfUfceDs0BRv29r65eYq9S06c+xaH\ntcEdqriuxCr7Y0ngO3H1dfOAZ1A2jlJKl/4pyrOCKJoh/ihW0P3LwU8FxvNS\nj4XyPaKhzc/4mGiazU9ux5IxVX2kTnrjnAhIKIsReqJCeT2uUaUPOqc1CYYj\n+eOk\r\n=lS6Q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "files": ["examples", "lib", "LICENSE", "safe.js", "themes", "index.d.ts", "safe.d.ts"], "engines": {"node": ">=0.1.90"}, "gitHead": "2894751d40e66ba3b6ff7930e50f6446c7b3b50f", "scripts": {"lint": "eslint . --fix", "test": "node tests/basic-test.js && node tests/safe-test.js"}, "_npmUser": {"name": "dabh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/Marak/colors.js.git", "type": "git"}, "_npmVersion": "5.8.0", "description": "get colors in your node.js console", "directories": {}, "_nodeVersion": "8.9.4", "_hasShrinkwrap": false, "devDependencies": {"eslint": "^4.19.1", "eslint-config-google": "^0.9.1"}, "_npmOperationalInternal": {"tmp": "tmp/colors_1.3.0_1526854175623_0.73772667015416", "host": "s3://npm-registry-packages"}}, "1.3.1": {"name": "colors", "version": "1.3.1", "keywords": ["ansi", "terminal", "colors"], "author": {"name": "Marak Squires"}, "license": "MIT", "_id": "colors@1.3.1", "maintainers": [{"name": "dabh", "email": "<EMAIL>"}, {"name": "marak", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/DABH", "name": "DABH"}], "homepage": "https://github.com/Marak/colors.js", "bugs": {"url": "https://github.com/Marak/colors.js/issues"}, "dist": {"shasum": "4accdb89cf2cabc7f982771925e9468784f32f3d", "tarball": "https://registry.npmjs.org/colors/-/colors-1.3.1.tgz", "fileCount": 21, "integrity": "sha512-jg/vxRmv430jixZrC+La5kMbUWqIg32/JsYNZb94+JEmzceYbWKTsv1OuTp+7EaqiaWRR2tPcykibwCRgclIsw==", "signatures": [{"sig": "MEUCIQD1aEMlIhobqsDkkK/KemfmNFIrCdJa6/r7gGsZFG/ZdQIgfgmv1QOKFooEE8UWMH0jB2k6Ec+Ng16IZmvtFlXFTso=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37480, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbVP/jCRA9TVsSAnZWagAATscQAI9ayKFI/pnNeXoFVLUl\ndh0Lua+Bsza4mjIV9cPvf6oT8Y4JBg4lDyJ952oKyxItiZuwI9JSfXy0xGNb\nVKU358jj1gtGTSIiD+vB3WXvK5Wy8DDWxe9aJ8qm+XxDy5cSOiSlT/d8dD6E\nCQb0JFRu3Wy2iShVBoRVAwvGJ2esz2B6vZiH8YXJUh6IjPodonkbSEqMi/wl\nUeec2oBk+4XlCejsf8gOJRaIEDwOlbi/ans3r/nksyUwZ0aPMi10c4uiQboz\nGHUgGQsHVTMJ1Yu7EXfRwHzIGEPLMrvQK9IIQpCYtecOL0VQnMzeOaIOGFj6\n4UZtIQkdo0xBRgDj+Eo+KF2PIG7gjwT5OVQsrf6gRRFk8KOLfgmswi5S5o9+\nMKq3Aah3eACKAL9LGxN4lz6T04EwagSS28HGjB6lEXo/LLmnDIQHaHBCzrsh\nOQbvTuW+vjgB5TWFi9BIe4iCsm/LsGEW/kSFp101Z5of0lxLPXBfdxIaDVfc\nwxbfHgDXxujsvXuM0F/gG2qTG72UrDwcrK4k8SZEiIKppNu/XUz3Ffsr2tyU\nHNwTqdwEpYqeXqFYjBQDpPpKG4qboeJnJzB3jxLpVH0nOWmYKT88JZOlB8FR\nB+lJVDOtq80ovh/m3JCw3l5WQip3rdtpbf9kZr49I6KirOc7Tn99UMpE6f41\ngPdf\r\n=dv89\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "files": ["examples", "lib", "LICENSE", "safe.js", "themes", "index.d.ts", "safe.d.ts"], "engines": {"node": ">=0.1.90"}, "gitHead": "2631877f888a3f1642b61c8d9bee241489544626", "scripts": {"lint": "eslint . --fix", "test": "node tests/basic-test.js && node tests/safe-test.js"}, "_npmUser": {"name": "dabh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/Marak/colors.js.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "get colors in your node.js console", "directories": {}, "_nodeVersion": "8.11.2", "_hasShrinkwrap": false, "devDependencies": {"eslint": "^5.2.0", "eslint-config-google": "^0.9.1"}, "_npmOperationalInternal": {"tmp": "tmp/colors_1.3.1_1532297187465_0.6923656221587124", "host": "s3://npm-registry-packages"}}, "1.3.2": {"name": "colors", "version": "1.3.2", "keywords": ["ansi", "terminal", "colors"], "author": {"name": "Marak Squires"}, "license": "MIT", "_id": "colors@1.3.2", "maintainers": [{"name": "dabh", "email": "<EMAIL>"}, {"name": "marak", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/DABH", "name": "DABH"}], "homepage": "https://github.com/Marak/colors.js", "bugs": {"url": "https://github.com/Marak/colors.js/issues"}, "dist": {"shasum": "2df8ff573dfbf255af562f8ce7181d6b971a359b", "tarball": "https://registry.npmjs.org/colors/-/colors-1.3.2.tgz", "fileCount": 22, "integrity": "sha512-rhP0JSBGYvpcNQj4s5AdShMeE5ahMop96cTeDl/v9qQQm2fYClE2QXZRi8wLzc+GmXSxdIqqbOIAhyObEXDbfQ==", "signatures": [{"sig": "MEUCIQDElffm0hc1kOCO743HuS9URTvaH5G94uESazjafsSiAwIgTLYAZNQrc8mH/LB/7KNr0okyuZnSaYt2TrSEovlHfQE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53884, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfbASCRA9TVsSAnZWagAAupgP/RLhkx6+gaWO1J8LFqSK\nfLEiSYsTJxe/EmilFi7w/+QBc8Ye0vehdJkvtvfASZfKTI4hM/1RArQsbWDr\nI+FCN1g23KOXAW2kblwsLdOOsYtanULRVHf6KVdVqbhnNpeKOLH52CODo343\nNoy3OGyVR/FRxwNZ6j7xECJj4Q8ET5/q+fodW8vHOuHm8xz7mr8FZrvWWggc\nCih41IedHCR/N8oeWmJtHWR1viD5NINxQr1k8/srD+VVIiRa7C7GG4yMcc7b\nxX2cGBT+CFs0oEjvfsMFaZdbmVLeJWv5B00xQxbRkrIKvA63057sHO7WLA/I\nEKCucGz0HF9N55x4mOIhrX2r89qMNhnsvEUMY66GXMnfPGSdOyKDa7GmicYV\nwX7Vm2mmneAWwF+IsSpyx5H9i+Ul0kg4vcbcmS1uXVh7aJBwqTlhPSk0SanN\n6tiIh/F7QVS4Z1tWJB+7ZahshZRT+I/pxag12/GFkcu3zWns9HgZlPwp5dvK\nztRLcvzNjNsiuCAyLVDOh1iFcNnhOGoKcI5J9Og/CiI/VaTZmsT38RJ8qHQv\nqaewEh7JcYbuRgffwOe/a1IZF/eUctSrfh0IfNRTosM3wHIh1f7aIHCF+S67\n9j8kLY8RCxLQH7h5XxH+yQxa0TAwhKObL4qYZqlTKEpMaIPXpMeyAB1R/8X9\nWnT0\r\n=ZBF8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "files": ["examples", "lib", "LICENSE", "safe.js", "themes", "index.d.ts", "safe.d.ts"], "engines": {"node": ">=0.1.90"}, "gitHead": "4a6d75d01c4389a9e9f7288cc2434b95decbcd58", "scripts": {"lint": "eslint . --fix", "test": "node tests/basic-test.js && node tests/safe-test.js"}, "_npmUser": {"name": "dabh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/Marak/colors.js.git", "type": "git"}, "_npmVersion": "6.4.0", "description": "get colors in your node.js console", "directories": {}, "_nodeVersion": "8.11.2", "_hasShrinkwrap": false, "devDependencies": {"eslint": "^5.4.0", "eslint-config-google": "^0.9.1"}, "_npmOperationalInternal": {"tmp": "tmp/colors_1.3.2_1534963730147_0.3209457183870088", "host": "s3://npm-registry-packages"}}, "1.3.3": {"name": "colors", "version": "1.3.3", "keywords": ["ansi", "terminal", "colors"], "author": {"name": "Marak Squires"}, "license": "MIT", "_id": "colors@1.3.3", "maintainers": [{"name": "dabh", "email": "<EMAIL>"}, {"name": "marak", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/DABH", "name": "DABH"}], "homepage": "https://github.com/Marak/colors.js", "bugs": {"url": "https://github.com/Marak/colors.js/issues"}, "dist": {"shasum": "39e005d546afe01e01f9c4ca8fa50f686a01205d", "tarball": "https://registry.npmjs.org/colors/-/colors-1.3.3.tgz", "fileCount": 21, "integrity": "sha512-mmGt/1pZqYRjMxB1axhTo16/snVZ5krrKkcmMeVKxzECMMXoCgnvTPp10QgHfcbQZw8Dq2jMNG6je4JlWU0gWg==", "signatures": [{"sig": "MEUCICipx6PPqA1PUDO8Wka1rt001O3oBQgBDT4kfS0pm+sPAiEA/uwElQvM5O/gZ6wZ0uNCUOgZi/M60Vdw8AHftO4a0pU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37746, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcDcL1CRA9TVsSAnZWagAAZhMP/RLpT/dg263sOTxYH3sF\ntwWPJmzlEg73lve6fi5la0FMcrt5qGVk+xFW3UjDtjNR4RZEvSqXkr/2lvZq\na9xr3WL0TLC83FYKNPVBX6xYR+IszHL0FmSGumLArPO+fnp1r96w9S4ZqWUd\n1In1tZZwqr79u+1r1y5zgS2XVDKmSi3G176b1dIwmT0rN6wtMq31gka9EMJg\ntlSRpPjJLB51MJZFMubAxVVUvTo1Xof5232r/0vtVeD5lQmC24YW5Oo3Yf8Y\n2gXtaE6t0K1w4pCCU9sYGuDIVcBiPQsZsOcFnLivSh1v+L9YpbyRSLnPO+pL\nj996D9sLMGUaInnDyR2hU1pAuw4gJkKWCIKpHXmMf7eJTTAO9co0PkHRVPyz\nmgv34r1NJyuFncj/fZ/WVKrSp68y0YsFckciMzvsEQXrz4SHMTng0yYQbp1W\n4XDSMxVbLRUNizN7B0J+q8KLzmirKg1xzSjeRrLXAdAhVZXk1nCGCMkpCBa7\ndUQRV5MK6XRi50DKgVdKQuGl95NxqJqmYDauT2xG1PB2rlHKZtMsYo74BXH9\nzzrmQ1QiDWp9BVi6bzZmJnPzwBfUQV6M0bQ1x8txXOHso0hoEILXCrcrGeK7\nfzlysIUd9VNy5H0gVQa3XPB66N0G3bHacDT/inDxXp3MFLz/6S/q43g9JO8w\n6FqR\r\n=mC+K\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=0.1.90"}, "gitHead": "b63ef88e521b42920a9e908848de340b31e68c9d", "scripts": {"lint": "eslint . --fix", "test": "node tests/basic-test.js && node tests/safe-test.js"}, "_npmUser": {"name": "dabh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/Marak/colors.js.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "get colors in your node.js console", "directories": {}, "_nodeVersion": "8.11.2", "_hasShrinkwrap": false, "devDependencies": {"eslint": "^5.2.0", "eslint-config-google": "^0.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/colors_1.3.3_1544405748861_0.9904919417477176", "host": "s3://npm-registry-packages"}}, "1.4.0": {"name": "colors", "version": "1.4.0", "keywords": ["ansi", "terminal", "colors"], "author": {"name": "Marak Squires"}, "license": "MIT", "_id": "colors@1.4.0", "maintainers": [{"name": "dabh", "email": "<EMAIL>"}, {"name": "marak", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/DABH", "name": "DABH"}], "homepage": "https://github.com/Marak/colors.js", "bugs": {"url": "https://github.com/Marak/colors.js/issues"}, "dist": {"shasum": "c50491479d4c1bdaed2c9ced32cf7c7dc2360f78", "tarball": "https://registry.npmjs.org/colors/-/colors-1.4.0.tgz", "fileCount": 21, "integrity": "sha512-a+UqTh4kgZg/SlGvfbzDHpgRu7AAQOmmqRHJnxhRZICKFUT91brVhNNt58CMWU9PsBbv3PDCZUHbVxuDiH2mtA==", "signatures": [{"sig": "MEUCIEqqoYqiKKYLhRLb0NRtJc0q8pI6cT9mB/ondo0h4VTOAiEA1pFlhTTgaW3rcDnJiwEfrbSEziFHKaTtxQ7szhKwr8I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39506, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdiAfACRA9TVsSAnZWagAA7gMP/1eUoL2YZSoe4XH3p7o5\n2NRhGJuE+81Kwbl/2+HWvlWGXxTo1vLYWGVAfBVYtEuUdnPlMOpCEyqdB8Ng\nqMr9acH/8ZkHKRyNYu9GeDLWWUFx8wv94qpcmnuqgp+24X3gBhiS7hnG6UJh\nL4kKUSycTGp0FFWPQ4tdpBuvC6PDGTowPfHh/oj0RosHygRcW6F4V5HDyws1\nQTnuE3k5vBhhzKQQ4oktGCUuQATqsg89lDDSw5hjThBf2y5ZrpF6qLVoiLgm\noMrEF3vDOIyf63naUmj/3qzBYFfQZU3wlGyaRfNxdqNooKW2QOb/x2XFtP46\nYibCl2xhGA0JsinmaAclbLfDkZSZs1bsjpj2xUOFJjQOeMReeS2PzCgHRBJy\nT9ow3X6MbRblOcWuX8Bbhr8kg9Av1xx2A9mtJ7G/DVuHLHBQOTro2l/qIb5M\nf9Z/++j4P1lMMKBp5jHvCRUNq9jgWdSaT9NHo1RvNKuEZM9mxyzyygcidj5w\ngaCGQ5G5kFOKAgmN1LvRYai5P31waqJ+Wr96g6XRfA9SBeeeX12v481jpKQm\nVZ6khQeII1VUgbadjWWegRAobEkW5JXLjdZbISZeIBahs5bOWPFiAFXowf4O\n7Hygdj0xtzkH/sWJeKUCxnzX6VN/mtu+QkPfjGBgDHNL4gtZsGDAizcsFuly\nDWUs\r\n=9VVk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=0.1.90"}, "gitHead": "baa0e1c7dc50d868354206b9ea71273e3f05f593", "scripts": {"lint": "eslint . --fix", "test": "node tests/basic-test.js && node tests/safe-test.js"}, "_npmUser": {"name": "dabh", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/Marak/colors.js.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "get colors in your node.js console", "directories": {}, "_nodeVersion": "10.16.3", "_hasShrinkwrap": false, "devDependencies": {"eslint": "^5.2.0", "eslint-config-google": "^0.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/colors_1.4.0_1569195967207_0.2781122116893864", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2011-03-15T10:12:18.245Z", "modified": "2025-01-04T00:24:02.631Z", "0.3.0": "2011-03-15T10:12:18.245Z", "0.5.0": "2011-03-15T10:12:18.245Z", "0.5.1": "2011-09-25T13:04:44.328Z", "0.6.0": "2011-12-09T11:32:57.649Z", "0.6.0-1": "2011-12-09T12:35:05.501Z", "0.6.1": "2013-07-26T05:49:48.513Z", "0.6.2": "2013-08-21T23:14:55.906Z", "1.0.0": "2014-10-01T11:29:39.467Z", "1.0.1": "2014-10-01T16:07:39.787Z", "1.0.2": "2014-10-02T19:55:43.478Z", "1.0.3": "2014-10-09T10:03:24.510Z", "1.1.0": "2015-04-29T07:15:49.571Z", "1.1.1": "2015-06-17T12:52:33.681Z", "1.1.2": "2015-06-17T13:03:00.583Z", "1.2.0-rc0": "2018-02-16T22:45:09.148Z", "1.2.0": "2018-03-10T06:48:40.310Z", "1.2.1": "2018-03-12T05:52:26.861Z", "1.2.2": "2018-04-30T18:42:10.848Z", "1.2.3": "2018-04-30T22:09:46.566Z", "1.2.4": "2018-05-05T00:56:06.245Z", "1.2.5": "2018-05-11T06:15:42.549Z", "1.3.0": "2018-05-20T22:09:35.759Z", "1.3.1": "2018-07-22T22:06:27.574Z", "1.3.2": "2018-08-22T18:48:50.238Z", "1.3.3": "2018-12-10T01:35:48.987Z", "1.4.0": "2019-09-22T23:46:07.522Z", "1.4.44-liberty-2": "2022-01-08T04:24:16.448Z", "1.4.1": "2022-01-08T19:22:05.133Z", "1.4.2": "2022-01-09T19:35:35.048Z"}, "bugs": {"url": "https://github.com/Marak/colors.js/issues"}, "author": {"name": "Marak Squires"}, "license": "MIT", "homepage": "https://github.com/Marak/colors.js", "keywords": ["ansi", "terminal", "colors"], "repository": {"url": "git+ssh://**************/Marak/colors.js.git", "type": "git"}, "description": "get colors in your node.js console", "contributors": [{"url": "https://github.com/DABH", "name": "DABH"}], "maintainers": [{"name": "marak", "email": "<EMAIL>"}], "readme": "", "readmeFilename": "", "users": {"52u": true, "gux": true, "pid": true, "qk4": true, "ubi": true, "vur": true, "blog": true, "dodo": true, "dofy": true, "fill": true, "gamr": true, "j3kz": true, "jits": true, "lova": true, "neo1": true, "ohom": true, "owaz": true, "pana": true, "smd4": true, "vaju": true, "vasc": true, "8code": true, "ali1k": true, "bjmin": true, "boogy": true, "demod": true, "gayan": true, "guoer": true, "haeck": true, "holly": true, "junos": true, "kikna": true, "metaa": true, "necr0": true, "panlw": true, "paulj": true, "pmasa": true, "r3nya": true, "seldo": true, "tchey": true, "temsa": true, "tht13": true, "vb078": true, "xrush": true, "xzycn": true, "yepzy": true, "youcp": true, "zoxon": true, "zuosh": true, "456wyc": true, "ajduke": true, "alandu": true, "aleivc": true, "apopek": true, "arsari": true, "ashkyd": true, "avdons": true, "baiang": true, "bifuer": true, "binnng": true, "bpatel": true, "buzuli": true, "chaowi": true, "chilts": true, "chrisx": true, "clisun": true, "coleww": true, "crwnvr": true, "egantz": true, "eins78": true, "esenor": true, "f2enav": true, "formix": true, "gliviu": true, "h0ward": true, "huarse": true, "iksnae": true, "jimnox": true, "jonoco": true, "jsolis": true, "kagawa": true, "knoja4": true, "leesei": true, "ljmf00": true, "milesq": true, "mpcref": true, "nescio": true, "noccer": true, "novalu": true, "pandao": true, "potnox": true, "qlqllu": true, "rangzf": true, "shriek": true, "starak": true, "suziam": true, "tahark": true, "tarcio": true, "tedyhy": true, "tigefa": true, "tjwebb": true, "trevin": true, "viztor": true, "vutran": true, "wlfyit": true, "yoksel": true, "yuch4n": true, "zaggen": true, "zechau": true, "zithan": true, "zwxajh": true, "abetomo": true, "adritek": true, "amaynut": true, "anoubis": true, "arielfr": true, "artem_m": true, "azevedo": true, "barenko": true, "cblumer": true, "cocopas": true, "edision": true, "ettalea": true, "ezeikel": true, "flyslow": true, "funroll": true, "geassor": true, "helsner": true, "hingsir": true, "io2work": true, "itonyyo": true, "jacques": true, "jaguarj": true, "jaxcode": true, "jcottam": true, "jyounce": true, "kerry95": true, "kontrax": true, "making3": true, "maxlevs": true, "moonpyk": true, "mykhael": true, "noopkat": true, "onbjerg": true, "pdedkov": true, "raelgor": true, "rcastro": true, "rosshal": true, "rpgreen": true, "sendypw": true, "sfgarza": true, "sopepos": true, "studi11": true, "supnate": true, "tengisb": true, "tin-lek": true, "tooyond": true, "touskar": true, "vrfrnco": true, "wezhang": true, "x0000ff": true, "xfloops": true, "xxronis": true, "ziehlke": true, "zonetti": true, "ahvonenj": true, "aidenzou": true, "alivesay": true, "andydrew": true, "artjacob": true, "avianflu": true, "bouchezb": true, "congcong": true, "darkwark": true, "dercoder": true, "dolymood": true, "draganhr": true, "drewnull": true, "edalorzo": true, "elussich": true, "froguard": true, "hootping": true, "huacnlee": true, "hummatli": true, "icaliman": true, "ifeature": true, "jackvial": true, "jun01ito": true, "korooney": true, "kotik990": true, "l0n9h02n": true, "leadfast": true, "leodutra": true, "losymear": true, "meetravi": true, "nraibaud": true, "nsmithau": true, "nukisman": true, "phette23": true, "phil1929": true, "pnevares": true, "putaoshu": true, "qddegtya": true, "rbartoli": true, "rfortune": true, "semencov": true, "sibawite": true, "sobralia": true, "solzimer": true, "steakeye": true, "streamer": true, "surfacew": true, "szymex73": true, "tamer1an": true, "thorsson": true, "tmcguire": true, "tmurngon": true, "tonydieu": true, "vishwasc": true, "warjiang": true, "wkaifang": true, "wynfrith": true, "xgheaven": true, "xiaobing": true, "yashprit": true, "zenfeder": true, "zeusdeux": true, "zguillez": true, "0x9r3ydu5": true, "adamkdean": true, "alejcerro": true, "antixrist": true, "aprilchen": true, "benpptung": true, "cilindrox": true, "cmilhench": true, "coachshea": true, "davidrlee": true, "dbrockman": true, "dennisgnl": true, "dimoreira": true, "erniep888": true, "evanlucas": true, "ezcabrera": true, "fgribreau": true, "fistynuts": true, "gpmetheny": true, "hacksalot": true, "happywang": true, "heartnett": true, "hellgrenj": true, "iceriver2": true, "inderdeep": true, "jakedalus": true, "jasonwbsg": true, "jesusgoku": true, "joaocunha": true, "jokarlist": true, "karmadude": true, "keanodejs": true, "kulakowka": true, "lazycoder": true, "leahcimic": true, "madsummer": true, "magemagic": true, "mahamdani": true, "mastayoda": true, "matthiasg": true, "megadrive": true, "mikestaub": true, "momepukku": true, "mordenius": true, "myxvisual": true, "newblt123": true, "nmccready": true, "otaviolms": true, "peunzhang": true, "rbecheras": true, "rosterloh": true, "rubiadias": true, "rylan_yan": true, "sfabrizio": true, "shakakira": true, "shikloshi": true, "shushanfx": true, "sparkrico": true, "spekkionu": true, "spielberg": true, "sqrtthree": true, "steel1990": true, "sternelee": true, "vancarney": true, "xiaokaike": true, "xunnamius": true, "yyscamper": true, "abonventre": true, "adammacias": true, "afollestad": true, "aitorllj93": true, "alanerzhao": true, "alexg53090": true, "andriecool": true, "ansonhorse": true, "apache2046": true, "aquiandres": true, "ashish.npm": true, "avivharuzi": true, "bengarrett": true, "berkmann18": true, "blakedietz": true, "blakmatrix": true, "brainpoint": true, "bruinebeer": true, "cbpetersen": true, "cestrensem": true, "cfleschhut": true, "davidchase": true, "degouville": true, "dmandola11": true, "donvercety": true, "dxdspirits": true, "f124275809": true, "fabianbach": true, "gerst20051": true, "ianpaschal": true, "incendiary": true, "jakedetels": true, "jiang-xuan": true, "jungae1000": true, "kaiquewdev": true, "kankungyip": true, "kennethjor": true, "kevin-wynn": true, "latinosoft": true, "mark24code": true, "maxmaximov": true, "morogasper": true, "nicomf1982": true, "panoptican": true, "piecioshka": true, "princetoad": true, "qqqppp9998": true, "rocket0191": true, "ryanlittle": true, "sanketss84": true, "scriptnull": true, "shoresh319": true, "shuoshubao": true, "skyinlayer": true, "tangweikun": true, "temoto-kun": true, "windqyoung": true, "chrisakakay": true, "chrisyboy53": true, "codeprowong": true, "cycomachead": true, "deneboulton": true, "evert-arias": true, "flumpus-dev": true, "frknbasaran": true, "garenyondem": true, "grantgeorge": true, "heyimeugene": true, "highgravity": true, "hkgsherlock": true, "hongbo-miao": true, "jonatasnona": true, "kirkhammetz": true, "kodekracker": true, "linuxwizard": true, "lotfire.dev": true, "maninbucket": true, "maskedcoder": true, "micromax720": true, "morishitter": true, "mseminatore": true, "phoward8020": true, "prestorondo": true, "raisiqueira": true, "rakeshalhan": true, "reergymerej": true, "riaanpelser": true, "sammyteahan": true, "schwartzman": true, "scytalezero": true, "soenkekluth": true, "stonecypher": true, "swedendrift": true, "thangakumar": true, "victorquinn": true, "wangnan0610": true, "andrewtimney": true, "battlemidget": true, "blackkoi0606": true, "brentonhouse": true, "divanvisagie": true, "dominik.palo": true, "fanchangyong": true, "filipesoccol": true, "freaktechnik": true, "ghostcode521": true, "iori20091101": true, "ivangaravito": true, "jamesallured": true, "josejaguirre": true, "joshuabriter": true, "justintormey": true, "kevinohara80": true, "matiasmarani": true, "mswanson1524": true, "sexyoung1985": true, "simon.turvey": true, "stpettersens": true, "wesleylhandy": true, "yourhoneysky": true, "bernardhamann": true, "chinawolf_wyp": true, "edwin_estrada": true, "ferchoriverar": true, "hellocodeming": true, "hibrahimsafak": true, "humantriangle": true, "ironclad.soul": true, "michaelchance": true, "nonemoticoner": true, "onofremartins": true, "pablo.tavarez": true, "playthefallen": true, "richardcfelix": true, "scottfreecode": true, "shen-weizhong": true, "shrimpseaweed": true, "stephencorwin": true, "stone_breaker": true, "tranceyos2419": true, "vehbiemiroglu": true, "blade254353074": true, "classicoldsong": true, "dorianamouroux": true, "eirikbirkeland": true, "imaginegenesis": true, "kaveh.ghaboosi": true, "qaisaboujaoude": true, "anatolie_sernii": true, "joaquin.briceno": true, "jonathanbergson": true, "pensierinmusica": true, "sametsisartenep": true, "cosmin.vasilache": true, "jonathanmelville": true, "theoryofnekomata": true, "ys_sidson_aidson": true, "ognjen.jevremovic": true, "timur.shemsedinov": true, "variousmilkshakes": true}}