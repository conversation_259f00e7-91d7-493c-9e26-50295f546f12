{"_id": "color-support", "_rev": "10-e9ee35199cfd1757df425cb363cc9078", "name": "color-support", "description": "A module which will endeavor to guess your terminal's level of color support.", "dist-tags": {"latest": "1.1.3"}, "versions": {"1.0.1": {"name": "color-support", "version": "1.0.1", "description": "A module which will endeavor to guess your terminal's level of color support.", "main": "index.js", "browser": "browser.js", "bin": {"color-support": "bin.js"}, "devDependencies": {"tap": "^6.2.0"}, "scripts": {"test": "tap test/*.js --cov --branches=100"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/color-support.git"}, "keywords": ["terminal", "color", "support", "xterm", "truecolor", "256"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "files": ["browser.js", "index.js", "bin.js"], "gitHead": "2f95d5f5c1cb053e9e3d9c5e6afeda7de19f470f", "bugs": {"url": "https://github.com/isaacs/color-support/issues"}, "homepage": "https://github.com/isaacs/color-support#readme", "_id": "color-support@1.0.1", "_shasum": "770d39616e1bce2a599ba88c7b9c36a4e0ef3258", "_from": ".", "_npmVersion": "3.10.6", "_nodeVersion": "4.4.4", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "770d39616e1bce2a599ba88c7b9c36a4e0ef3258", "tarball": "https://registry.npmjs.org/color-support/-/color-support-1.0.1.tgz", "integrity": "sha512-/2YwQp8FANiwBjBDw6mBl+cUgkC/0z2OlZB0A39enyc0L1NHIrRui2XCj4wpt2bXpUvj84owsKDAD4BbMbAYBA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDqOAmwUgX/iOd2PEOhfXyycziylmAtaPU+c2BIfiJKLAIgFekZBdKRUsPctC5ogcAUG2yOsdnpeIRyWJv0YZgvGQE="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/color-support-1.0.1.tgz_1468744903556_0.7890250165946782"}, "directories": {}}, "1.1.0": {"name": "color-support", "version": "1.1.0", "description": "A module which will endeavor to guess your terminal's level of color support.", "main": "index.js", "browser": "browser.js", "bin": {"color-support": "bin.js"}, "devDependencies": {"tap": "^6.2.0"}, "scripts": {"test": "tap test/*.js --cov --branches=100"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/color-support.git"}, "keywords": ["terminal", "color", "support", "xterm", "truecolor", "256"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "files": ["browser.js", "index.js", "bin.js"], "gitHead": "7481cb1ba475e61c6aab48efef4cca3a17e87961", "bugs": {"url": "https://github.com/isaacs/color-support/issues"}, "homepage": "https://github.com/isaacs/color-support#readme", "_id": "color-support@1.1.0", "_shasum": "26e99f49eac1792b1b1cb339bb6b0569bd6748ac", "_from": ".", "_npmVersion": "3.10.6", "_nodeVersion": "4.4.4", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "26e99f49eac1792b1b1cb339bb6b0569bd6748ac", "tarball": "https://registry.npmjs.org/color-support/-/color-support-1.1.0.tgz", "integrity": "sha512-zMp0AKeAhBcVXSqNu/MU7QuUG2UISAZqrArl/T93fYIiZuEt2tHIFCh6STvXXaaFH8pBu/XjwnKoykV/T2xGzw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCcAqAKAPiHJ1jRXx9jcGP6jQsVTd3IYml0MZpfinv9rgIhAO1twe4FLGsolBRoaVBq5awGVuYf2Rqr4izTekV0qJQy"}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/color-support-1.1.0.tgz_1468820831805_0.9082229007035494"}, "directories": {}}, "1.1.1": {"name": "color-support", "version": "1.1.1", "description": "A module which will endeavor to guess your terminal's level of color support.", "main": "index.js", "browser": "browser.js", "bin": {"color-support": "bin.js"}, "devDependencies": {"tap": "^6.2.0"}, "scripts": {"test": "tap test/*.js --cov --branches=100"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/color-support.git"}, "keywords": ["terminal", "color", "support", "xterm", "truecolor", "256"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "files": ["browser.js", "index.js", "bin.js"], "gitHead": "bab9034d6d09d5039b8caec1c91d2b626e74270c", "bugs": {"url": "https://github.com/isaacs/color-support/issues"}, "homepage": "https://github.com/isaacs/color-support#readme", "_id": "color-support@1.1.1", "_shasum": "04816947ba6b06d364e3f13fe045280b93b688cd", "_from": ".", "_npmVersion": "3.10.6", "_nodeVersion": "4.4.4", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "04816947ba6b06d364e3f13fe045280b93b688cd", "tarball": "https://registry.npmjs.org/color-support/-/color-support-1.1.1.tgz", "integrity": "sha512-H0cXGQskyq9JZ3uGOqNlK76HoJlGoVQ0N+jkYGKi6KofqTFwtWqnzypo6dTHxsE7MPQZKxd1Wcoda1/wFf6F1Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCKkI+feuSrSB7d/gg74N+VA89ZerhQLY5a1Vi9/UvKlAIgGw7i9BndXivdT1WkhjQzmlmoFBRDoZXp4G3D3WhW1Y0="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/color-support-1.1.1.tgz_1469557472977_0.455952369607985"}, "directories": {}}, "1.1.2": {"name": "color-support", "version": "1.1.2", "description": "A module which will endeavor to guess your terminal's level of color support.", "main": "index.js", "browser": "browser.js", "bin": {"color-support": "bin.js"}, "devDependencies": {"tap": "^6.2.0"}, "scripts": {"test": "tap test/*.js --cov --branches=100"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/color-support.git"}, "keywords": ["terminal", "color", "support", "xterm", "truecolor", "256"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "files": ["browser.js", "index.js", "bin.js"], "gitHead": "6941307a36600398d397b71289432f12ebbb1e8c", "bugs": {"url": "https://github.com/isaacs/color-support/issues"}, "homepage": "https://github.com/isaacs/color-support#readme", "_id": "color-support@1.1.2", "_shasum": "49cc99b89d1bdef1292e9d9323c66971a33eb89d", "_from": ".", "_npmVersion": "3.10.7", "_nodeVersion": "6.5.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "49cc99b89d1bdef1292e9d9323c66971a33eb89d", "tarball": "https://registry.npmjs.org/color-support/-/color-support-1.1.2.tgz", "integrity": "sha512-sISSv+Rs3XcL9kVuNAqkVIMfF+TOeCx1J113dicbt9bUkcLelqhBj7jaRwmNc30Q5bqlM1gCBbPgrl4sd/v9aA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDq4hucG1yDLta8d2rm5x2axuyYtMInGbPtodaJT4S3FAIhAJ67gxaAfUOj3H5HlKrLpvXdFg176gRXJCP/Y13tBWDk"}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/color-support-1.1.2.tgz_1475875804420_0.9299137168563902"}, "directories": {}}, "1.1.3": {"name": "color-support", "version": "1.1.3", "description": "A module which will endeavor to guess your terminal's level of color support.", "main": "index.js", "browser": "browser.js", "bin": {"color-support": "bin.js"}, "devDependencies": {"tap": "^10.3.3"}, "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/color-support.git"}, "keywords": ["terminal", "color", "support", "xterm", "truecolor", "256"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "files": ["browser.js", "index.js", "bin.js"], "gitHead": "41d86ea5525fcd56765eb0067f1434b2ed5aec81", "bugs": {"url": "https://github.com/isaacs/color-support/issues"}, "homepage": "https://github.com/isaacs/color-support#readme", "_id": "color-support@1.1.3", "_npmVersion": "5.0.3", "_nodeVersion": "8.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-qiBjkpbMLO/HL68y+lh4q0/O1MZFj2RX6X/KmMa3+gJD3z+WwI1ZzDHysvqHGS3mP6mznPckpXmw1nI9cJjyRg==", "shasum": "93834379a1cc9a0c61f82f52f0d04322251bd5a2", "tarball": "https://registry.npmjs.org/color-support/-/color-support-1.1.3.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDNKNoYjwCTTeg4L9GdwzfXIxbUy6gIlWr5glermhYb5AIhALZB9VHPwDLDCem3cIjvXSlDVb4P0yQVDLEhXoPQ2YyT"}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/color-support-1.1.3.tgz_1496781653952_0.28517320565879345"}, "directories": {}}}, "readme": "# color-support\n\nA module which will endeavor to guess your terminal's level of color\nsupport.\n\n[![Build Status](https://travis-ci.org/isaacs/color-support.svg?branch=master)](https://travis-ci.org/isaacs/color-support) [![Coverage Status](https://coveralls.io/repos/github/isaacs/color-support/badge.svg?branch=master)](https://coveralls.io/github/isaacs/color-support?branch=master)\n\nThis is similar to `supports-color`, but it does not read\n`process.argv`.\n\n1. If not in a node environment, not supported.\n\n2. If stdout is not a TTY, not supported, unless the `ignoreTTY`\n   option is set.\n\n3. If the `TERM` environ is `dumb`, not supported, unless the\n   `ignoreDumb` option is set.\n\n4. If on Windows, then support 16 colors.\n\n5. If using Tmux, then support 256 colors.\n\n7. Handle continuous-integration servers.  If `CI` or\n   `TEAMCITY_VERSION` are set in the environment, and `TRAVIS` is not\n   set, then color is not supported, unless `ignoreCI` option is set.\n\n6. Guess based on the `TERM_PROGRAM` environ.  These terminals support\n   16m colors:\n\n    - `iTerm.app` version 3.x supports 16m colors, below support 256\n    - `MacTerm` supports 16m colors\n    - `Apple_Terminal` supports 256 colors\n    - Have more things that belong on this list?  Send a PR!\n\n8. Make a guess based on the `TERM` environment variable.  Any\n   `xterm-256color` will get 256 colors.  Any screen, xterm, vt100,\n   color, ansi, cygwin, or linux `TERM` will get 16 colors.\n\n9. If `COLORTERM` environment variable is set, then support 16 colors.\n\n10. At this point, we assume that color is not supported.\n\n## USAGE\n\n```javascript\nvar testColorSupport = require('color-support')\nvar colorSupport = testColorSupport(/* options object */)\n\nif (!colorSupport) {\n  console.log('color is not supported')\n} else if (colorSupport.has16m) {\n  console.log('\\x1b[38;2;102;194;255m16m colors\\x1b[0m')\n} else if (colorSupport.has256) {\n  console.log('\\x1b[38;5;119m256 colors\\x1b[0m')\n} else if (colorSupport.hasBasic) {\n  console.log('\\x1b[31mbasic colors\\x1b[0m')\n} else {\n  console.log('this is impossible, but colors are not supported')\n}\n```\n\nIf you don't have any options to set, you can also just look at the\nflags which will all be set on the test function itself.  (Of course,\nthis doesn't return a falsey value when colors aren't supported, and\ndoesn't allow you to set options.)\n\n```javascript\nvar colorSupport = require('color-support')\n\nif (colorSupport.has16m) {\n  console.log('\\x1b[38;2;102;194;255m16m colors\\x1b[0m')\n} else if (colorSupport.has256) {\n  console.log('\\x1b[38;5;119m256 colors\\x1b[0m')\n} else if (colorSupport.hasBasic) {\n  console.log('\\x1b[31mbasic colors\\x1b[0m')\n} else {\n  console.log('colors are not supported')\n}\n```\n\n## Options\n\nYou can pass in the following options.\n\n* ignoreTTY - default false.  Ignore the `isTTY` check.\n* ignoreDumb - default false.  Ignore `TERM=dumb` environ check.\n* ignoreCI - default false.  Ignore `CI` environ check.\n* env - Object for environment vars. Defaults to `process.env`.\n* stream - Stream for `isTTY` check. Defaults to `process.stdout`.\n* term - String for `TERM` checking. Defaults to `env.TERM`.\n* alwaysReturn - default false.  Return an object when colors aren't\n  supported (instead of returning `false`).\n* level - A number from 0 to 3.  This will return a result for the\n  specified level.  This is useful if you want to be able to set the\n  color support level explicitly as a number in an environment\n  variable or config, but then use the object flags in your program.\n  Except for `alwaysReturn` to return an object for level 0, all other\n  options are ignored, since no checking is done if a level is\n  explicitly set.\n\n## Return Value\n\nIf no color support is available, then `false` is returned by default,\nunless the `alwaysReturn` flag is set to `true`.  This is so that the\nsimple question of \"can I use colors or not\" can treat any truthy\nreturn as \"yes\".\n\nOtherwise, the return object has the following fields:\n\n* `level` - A number from 0 to 3\n    * `0` - No color support\n    * `1` - Basic (16) color support\n    * `2` - 256 color support\n    * `3` - 16 million (true) color support\n* `hasBasic` - Boolean\n* `has256` - Boolean\n* `has16m` - Boolean\n\n## CLI\n\nYou can run the `color-support` bin from the command line which will\njust dump the values as this module calculates them in whatever env\nit's run.  It takes no command line arguments.\n\n## Credits\n\nThis is a spiritual, if not actual, fork of\n[supports-color](http://npm.im/supports-color) by the ever prolific\n[Sindre Sorhus](http://npm.im/~sindresorhus).\n", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "time": {"modified": "2023-04-12T02:46:12.865Z", "created": "2016-07-17T08:41:45.378Z", "1.0.1": "2016-07-17T08:41:45.378Z", "1.1.0": "2016-07-18T05:47:13.648Z", "1.1.1": "2016-07-26T18:24:36.477Z", "1.1.2": "2016-10-07T21:30:04.663Z", "1.1.3": "2017-06-06T20:40:54.019Z"}, "homepage": "https://github.com/isaacs/color-support#readme", "keywords": ["terminal", "color", "support", "xterm", "truecolor", "256"], "repository": {"type": "git", "url": "git+https://github.com/isaacs/color-support.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/color-support/issues"}, "license": "ISC", "readmeFilename": "README.md", "users": {"flumpus-dev": true}}