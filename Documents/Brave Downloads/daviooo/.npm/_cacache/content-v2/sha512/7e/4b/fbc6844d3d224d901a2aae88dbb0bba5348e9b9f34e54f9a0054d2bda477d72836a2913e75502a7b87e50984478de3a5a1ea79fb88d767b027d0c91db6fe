{"_id": "chownr", "_rev": "20-90495d74329ca460eb3891370490cdfa", "name": "chownr", "description": "like `chown -R`", "dist-tags": {"latest": "3.0.0"}, "versions": {"0.0.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "chownr", "description": "like `chown -R`", "version": "0.0.1", "repository": {"type": "git", "url": "git://github.com/isaacs/chownr.git"}, "main": "chownr.js", "devDependencies": {"tap": "0.2", "mkdirp": "0.3", "rimraf": ""}, "scripts": {"test": "tap test/*.js"}, "license": "BSD", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "chownr@0.0.1", "dependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.1.23", "_nodeVersion": "v0.7.10-pre", "_defaultsLoaded": true, "dist": {"shasum": "51d18189d9092d5f8afd623f3288bfd1c6bf1a62", "tarball": "https://registry.npmjs.org/chownr/-/chownr-0.0.1.tgz", "integrity": "sha512-goAG4rAgFydYcD0ixqyMaONTiGLscYfXk9IT7gOYyR18Mu3ZSIffnFivWTT+HPuFeby9RPTopOR8JxbYroiScA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFCWRXIwtH1vUDB5JvyMcn7DyafVazvypvls+O2InJS+AiEAgW2h+n5AitUzMh/5X+32FPewVjjDr1vChV57BnhQYj4="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}}, "0.0.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "chownr", "description": "like `chown -R`", "version": "0.0.2", "repository": {"type": "git", "url": "git://github.com/isaacs/chownr.git"}, "main": "chownr.js", "devDependencies": {"tap": "0.2", "mkdirp": "0.3", "rimraf": ""}, "scripts": {"test": "tap test/*.js"}, "license": "ISC", "gitHead": "3cafeb70b2c343e893f710750406b3909ec537cb", "bugs": {"url": "https://github.com/isaacs/chownr/issues"}, "homepage": "https://github.com/isaacs/chownr#readme", "_id": "chownr@0.0.2", "_shasum": "2f9aebf746f90808ce00607b72ba73b41604c485", "_from": ".", "_npmVersion": "2.10.0", "_nodeVersion": "2.0.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "2f9aebf746f90808ce00607b72ba73b41604c485", "tarball": "https://registry.npmjs.org/chownr/-/chownr-0.0.2.tgz", "integrity": "sha512-4sa<PERSON><PERSON><PERSON>+/DavveVRsu49tUbYvLn5cS75w8gLQr14jXlFxSNbuoY7G6gPjcVfgdQ+c4BW02b0hXV5nOXYFD7Fmpw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBCBCMfeEePsLQyOk1jXdcXYULF8SZXXZzSh2tzayPYdAiEAneysBWMIAnmmLEu9OFY554vrIad6qVWPg/jlsIjIucg="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}}, "1.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "chownr", "description": "like `chown -R`", "version": "1.0.0", "repository": {"type": "git", "url": "git://github.com/isaacs/chownr.git"}, "main": "chownr.js", "devDependencies": {"mkdirp": "0.3", "rimraf": "", "tap": "^1.2.0"}, "scripts": {"test": "tap test/*.js"}, "license": "ISC", "gitHead": "4f72743895927db8108dbf3d5462c667db22ebce", "bugs": {"url": "https://github.com/isaacs/chownr/issues"}, "homepage": "https://github.com/isaacs/chownr#readme", "_id": "chownr@1.0.0", "_shasum": "02855833d20515cf2681c717d686bb8c1f3ea91a", "_from": ".", "_npmVersion": "3.2.2", "_nodeVersion": "2.2.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "02855833d20515cf2681c717d686bb8c1f3ea91a", "tarball": "https://registry.npmjs.org/chownr/-/chownr-1.0.0.tgz", "integrity": "sha512-AUNcIMR3gp65x7Qv4ZMbdNURtEd30PN0MF78j5EleWeTveh7/DyHNkL+NisebEupdPrA3zxITYQnrDo6KEcQJQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICpNpYQpXOx59Fdb29xL2VVBE+x2HmrdoHuo1QpCqdnVAiBS45nN+FybaOnW9o5A4EkY2JdS4FL7xhez+HdL62H7hQ=="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}}, "1.0.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "chownr", "description": "like `chown -R`", "version": "1.0.1", "repository": {"type": "git", "url": "git://github.com/isaacs/chownr.git"}, "main": "chownr.js", "files": ["chownr.js"], "devDependencies": {"mkdirp": "0.3", "rimraf": "", "tap": "^1.2.0"}, "scripts": {"test": "tap test/*.js"}, "license": "ISC", "gitHead": "c6c43844e80d7c7045e737a72b9fbb1ba0579a26", "bugs": {"url": "https://github.com/isaacs/chownr/issues"}, "homepage": "https://github.com/isaacs/chownr#readme", "_id": "chownr@1.0.1", "_shasum": "e2a75042a9551908bebd25b8523d5f9769d79181", "_from": ".", "_npmVersion": "3.2.2", "_nodeVersion": "2.2.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "e2a75042a9551908bebd25b8523d5f9769d79181", "tarball": "https://registry.npmjs.org/chownr/-/chownr-1.0.1.tgz", "integrity": "sha512-cKnqUJAC8G6cuN1DiRRTifu+s1BlAQNtalzGphFEV0pl0p46dsxJD4l1AOlyKJeLZOFzo3c34R7F3djxaCu8Kw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDGhBClUvLsS7zZUjeZOe+v40HobUvxSC4LFiXX0dLmJQIgDoTR/Cv+bd+zPe8otngAmmSIh7/bphKw3KC8h+fyyc0="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}}, "1.1.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "chownr", "description": "like `chown -R`", "version": "1.1.0", "repository": {"type": "git", "url": "git://github.com/isaacs/chownr.git"}, "main": "chownr.js", "devDependencies": {"mkdirp": "0.3", "rimraf": "", "tap": "^12.0.1"}, "scripts": {"test": "tap test/*.js --cov", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "license": "ISC", "gitHead": "76c21fad5b9e518b3dba16a1bd53bd6f5f2c2e5c", "bugs": {"url": "https://github.com/isaacs/chownr/issues"}, "homepage": "https://github.com/isaacs/chownr#readme", "_id": "chownr@1.1.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.10.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-BGowLy8nGWXPbtRR/8imBkaAFdArC2ES+q4HvCy8RruTpKU3MEgxVpT+AxlkAax0ykKqnoNnHAZh+Ryu0eFCIw==", "shasum": "17405cadd8706bc41f017b564bb2de460381e7d1", "tarball": "https://registry.npmjs.org/chownr/-/chownr-1.1.0.tgz", "fileCount": 4, "unpackedSize": 3828, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbnaJ3CRA9TVsSAnZWagAA/dgP/2yH2/y8vSOL20GKVFNh\nPgKIQOFhZCbZ97iZD105qpMVjYI2S6qOXV7chayXDrYtVfudZ3VZLF6nDQW+\nq2PQ8FgvSk1zN6xegTvIlEb0fboWHi4VZ4Ca1vg8GDdIrSefnxocbq75KvNx\n170Pc/NM4Q2W1tXhMTpLCAtHcvT+fMsOUI9DDWxoNLvMwWcY2JS6ggc9FPii\nySbbsoz9RkKIkxJm2Lv28mcrAzq9eBPC9Q0f5r/P+0dc4YWAf8kNTasxXWNI\nCtLUcOXxYtDQ6OJqFimDa0wrccnEjIDo8PKP/15FuC4doFByteq1zmtE++dH\nETDXZd4+b7JrSGC9EVTJA7uSss/gXK6/bYOP+r59Ld39FHBUK+xegBYMCnCk\nWacMiZ9y/TYjKI1v4RTfDxd21+VbqRFn+V/ZSUFRTyNqtLdu91Aaxc4Xa1Ga\n7NJosnfcGN01jr6SqDEPBi2RiUQm+20by+j8H+nViBXUmHqdkGsVVxCqKYXs\nxktIKdaSovdFH+VP2oJ04Vj0o9tlhcG5HNfeURZjU0LZDufpi/JPdfN5l/7c\nompEaIvMLLyke8GLPmuSZSPlRCzbIQka7NsN9v0G1DH/7kna7+9tlaa6pkqN\nMv1/NFwC9Ake2miQjXnC8LA9Pn0jUz5v4cubX8g9u+P68noLXDcCEUbuHE2j\n68at\r\n=Q33U\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCZ7jBdFD9XIfjaTdkFrTTz4himPSCRbg1lQftCaBb+WgIgOnX9lWJtzg/jFPB8oJ9CRIDe8U5iU8xrkAip0iO4/VU="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/chownr_1.1.0_1537057399049_0.3682169782824076"}, "_hasShrinkwrap": false}, "1.1.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "chownr", "description": "like `chown -R`", "version": "1.1.1", "repository": {"type": "git", "url": "git://github.com/isaacs/chownr.git"}, "main": "chownr.js", "devDependencies": {"mkdirp": "0.3", "rimraf": "", "tap": "^12.0.1"}, "scripts": {"test": "tap test/*.js --cov", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "license": "ISC", "gitHead": "7a5c3d57c3691eebb9c66fa00fa6003d02ef9440", "bugs": {"url": "https://github.com/isaacs/chownr/issues"}, "homepage": "https://github.com/isaacs/chownr#readme", "_id": "chownr@1.1.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.10.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-j38EvO5+LHX84jlo6h4UzmOwi0UgW61WRyPtJz4qaadK5eY3BTS5TY/S1Stc3Uk2lIM6TPevAlULiEJwie860g==", "shasum": "54726b8b8fff4df053c42187e801fb4412df1494", "tarball": "https://registry.npmjs.org/chownr/-/chownr-1.1.1.tgz", "fileCount": 4, "unpackedSize": 3901, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbncqBCRA9TVsSAnZWagAAzhUP/1pR2jFff+rVryKPm7jF\nc+IGuZPRxl4qLEjUSNSuVmq+eSWK3zMwSm+59eyI/+ArsjZvtIEi9TUo5ddV\n9vwa64C57bjTcbsSJgHUK+N+8qWggo3nXHYUhFUyVgaVihvVt4LUYPhWFpp2\nzjDdysskuD3hIkcsRPN7123OZwvl9NXU5E/DFmZJ95Jz0tVrABhE5GptOSab\nK69de/oz7tK+3tFAcApq1xNYfzkPSEQscQum+sleV56SEaEUQJfQzJC4iccM\nK7DjBNGkdH+japMi2vD9je2Jo5949wXBgOgjcZmoSgzb2gWivB4HcGeYh/fS\n5yX2CLCy0VOFdkK6C1bxIzxHQEqALY1pnPos1HkXSJMvzWdhNnq+n62IPOh9\n8MXEf0fUOdDfKSwAyOphtWmfu7Wy9gUrYDg4rHPDTQjGDM+mvIIMxMIwmQ0k\nsOOZMdXaaKf3tXtqNUT1j96hgVBTQ7X8/HrXxPuQI9hBrh4CdR7TekZFZ0NG\nrkiNT5fEGwpEz0EeiNlMkaNPkw+hvg6nTFp+0tePyWPC1weMlNCbhm4jipHt\nKl9t8ox+bsoKH/UviCYjC3XfJSEGqBpaox7i8Pjvcp25s4p9FwNiExE0pe2I\nvbWrbGEVL8lsoCJi84uI2n5PBcOyN51srZmWI6P9EU1nYzDPF/B2Z0N8yw5K\n8ks4\r\n=wIis\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDXRVurQN+65NVUc5nv4C9M2/0zLdSc7T0KQfSYOMVHNwIhAP7p4PJT/G0uTIqUNue/2gPi+4Jft32oBeZeLsV0kYIR"}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/chownr_1.1.1_1537067648822_0.8733677031057796"}, "_hasShrinkwrap": false}, "1.1.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "chownr", "description": "like `chown -R`", "version": "1.1.2", "repository": {"type": "git", "url": "git://github.com/isaacs/chownr.git"}, "main": "chownr.js", "devDependencies": {"mkdirp": "0.3", "rimraf": "", "tap": "^12.0.1"}, "scripts": {"test": "tap test/*.js --cov", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "license": "ISC", "gitHead": "cf3b27b5723045b01f7bbe68a874423d978683f7", "bugs": {"url": "https://github.com/isaacs/chownr/issues"}, "homepage": "https://github.com/isaacs/chownr#readme", "_id": "chownr@1.1.2", "_nodeVersion": "12.4.0", "_npmVersion": "6.10.1-next.0", "dist": {"integrity": "sha512-GkfeAQh+QNy3wquu9oIZr6SS5x7wGdSgNQvD10X3r+AZr1Oys22HW8kAmDMvNg2+Dm0TeGaEuO8gFwdBXxwO8A==", "shasum": "a18f1e0b269c8a6a5d3c86eb298beb14c3dd7bf6", "tarball": "https://registry.npmjs.org/chownr/-/chownr-1.1.2.tgz", "fileCount": 4, "unpackedSize": 4925, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdHR8GCRA9TVsSAnZWagAApbkP/R0CiSVRS8E4FwRY/hoU\nXD6FlTt1IVt3HAvHs6stXKdkUV9EWYKXLjFuEj/hpCBp4ENrIdIX4saVAVYc\n2R3XKtSWHARdmXq/GHTidzp2tU4j42TSXWl9xEU1Jh7NY7/nBOSxNjL51rPp\n2XkD7B0Jw0YCPUb1yk/Ludn8+i2MqjCIZxsq1cA7OTtlAdMcBNXBfT9eeFhZ\ndH9UKOQcwL6lzMn0ZMVVrZ//A8ly8Pml66ZwHrulkNBc9Ghbwcwhx58R+3C/\n0mCVzdQ3a414qVlwoMoSCS424VhKvxE9oxWMkZuKeBQXwGDuBmZO6XV2CuJn\nVWAl9DtVL4439qry95xnnqFCCzBqjQVvNKBhLyqFvI1y5gFYelr3veRqr8Oy\nnOs7XHsMdHeN5vQFEultyU69DGjYDtvh3FMVbp/CeRObHdMrLdWlHwGonqTH\nj6gO4Gu/kcFMmlqkX0kEgrtVfhDU6da5RtwkNoV1ZtHboYyhsU1Q/DWP+FII\nRB8h+CHVOoihZSn1kIxfQQO/E1DVTgMRgKF0UyEZIihryRJgYSqIyNTodrJh\nXrOodmBwVklhCYrBzKue4T9phn4buHLtDSRdc/9rO3i9oa2RK1X3GRchm6uZ\nC6Thf/RfUaKy09OqG2mxi4izXDi870vbPhIaBtFXzU+BMw5vRN2arsCCmXSr\nmBKR\r\n=p5u9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCP9DX1lQiURpaNumwl1HYAZ+1SJ59H4EXjHpA4DHlx8AIhAJ27EgRt380Pjs6TAweSyFzY8RfuTIv433hdmaAlPsQv"}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/chownr_1.1.2_1562189571355_0.8148922644594261"}, "_hasShrinkwrap": false}, "1.1.3": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "chownr", "description": "like `chown -R`", "version": "1.1.3", "repository": {"type": "git", "url": "git://github.com/isaacs/chownr.git"}, "main": "chownr.js", "devDependencies": {"mkdirp": "0.3", "rimraf": "", "tap": "^12.0.1"}, "scripts": {"test": "tap test/*.js --cov", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "license": "ISC", "gitHead": "deaa058afe2a04c6528965a218ece1226a9ee2ae", "bugs": {"url": "https://github.com/isaacs/chownr/issues"}, "homepage": "https://github.com/isaacs/chownr#readme", "_id": "chownr@1.1.3", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"integrity": "sha512-i70fVHhmV3DtTl6nqvZOnIjbY0Pe4kAUjwHj8z0zAdgBtYrJyYwLKCCuRBQ5ppkyL0AkN7HKRnETdmdp1zqNXw==", "shasum": "42d837d5239688d55f303003a508230fa6727142", "tarball": "https://registry.npmjs.org/chownr/-/chownr-1.1.3.tgz", "fileCount": 4, "unpackedSize": 4945, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdiv/WCRA9TVsSAnZWagAAYqAP/2j2OpJYBCwWko0V34P/\n6YwcVdnX4OUfICGDwX3Nvoz7lByUkQ3AXlQ6WTwuMssmT6YRFmYX+sSww8XK\nXI4v7RKSLpqV1ZsQFPe9Wqc79dP6lsYJoxiOBah31jmZYX4vKknam/IMZS/+\nMsK1hUTD41v/xWGsOtmZbxuXfUHoxAyhMwo8cAzdUUOsx4mr75q2vf16rnU4\nSUWHM+66PQCw2fvj+PXk2IXRDPI/WjOZbqY6ehVM2E2i52NGPRJn5xjzoxbc\nd4vTAtWcUV5WfdWAZ6h+gM7DFHiXGtcb0AtaGPYO7o/tGKbAT3vbhUM4jieY\nyz1XR205fIRluR7CNRi/JTQ5QFMpedtag60nT3WTwWMz3vjIAoD8etNPdZUW\nnN8y0vD0KTJHKAxwQkMgN67N0bPpZk/RAQRzDNzk8ymX5PNK0Wbs1dvv6Xm0\nmDNd5F5XUKwjf+zl32d+CdusNWzzRjiSxeNW9sEpjt0LgbTagWbTWJxNtRNA\nzIwW/gyM99nZv7W9s0LdC5BgVDeAk2ecjwsdn4b37/f+tb1IPCQLQtw69kXu\niDhxbhIL+s1i4ssul7FAYhO1zZQG3faF3kQg4TB9YDq0FPTZ9bQLbSlYJ17s\nQMqsPGTL6hkxCIaRiVwI1uewfYq7GMH4stpvOBOFNpe3ntAZ5dnK3g9Jua9o\nJTDY\r\n=VIgB\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHvTnm3JTtp4BY65AxU99voVXRMe3XTFHoVu+jnfpZXLAiAVsTwvkYGUxMmLbbEjdDcKF7g8yntkCd7b4bbAqxtG3g=="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/chownr_1.1.3_1569390549998_0.778423331031457"}, "_hasShrinkwrap": false}, "1.1.4": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "chownr", "description": "like `chown -R`", "version": "1.1.4", "repository": {"type": "git", "url": "git://github.com/isaacs/chownr.git"}, "main": "chownr.js", "devDependencies": {"mkdirp": "0.3", "rimraf": "^2.7.1", "tap": "^14.10.6"}, "tap": {"check-coverage": true}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "license": "ISC", "gitHead": "814f6422241dcc1bbb324fa29cd6d9f6dc2141ae", "bugs": {"url": "https://github.com/isaacs/chownr/issues"}, "homepage": "https://github.com/isaacs/chownr#readme", "_id": "chownr@1.1.4", "_nodeVersion": "13.7.0", "_npmVersion": "6.13.7", "dist": {"integrity": "sha512-jJ0bqzaylmJtVnNgzTeSOs8DPavpbYgEr/b0YL8/2GO3xJEhInFmhKMUnEJQjZumK7KXGFhUy89PrsJWlakBVg==", "shasum": "6fc9d7b42d32a583596337666e7d08084da2cc6b", "tarball": "https://registry.npmjs.org/chownr/-/chownr-1.1.4.tgz", "fileCount": 4, "unpackedSize": 5709, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeQ2NeCRA9TVsSAnZWagAAgjsP/RcBpHpxPazXjd9noJxf\n8TRBu2JP/zftaLpBoBVUWlBdnlYS8rjwuZmi8SPazhXddUpl55lD3ZHyWclf\n6AFvlPP5enIwcbpb6Q54c9vIAxQYoagUD9iQ6U62w+T5908PKi9ha1DEv1+2\n+il9xSwj5P0Qq8AhZV0EY3SZ7Lb+kf5rRPrqVKz4UqIs2NbkvMk/Er/+wGSa\nYjWxti2FCSyUcwakYg95eEm0JP1SqbdCswOG4wAKT9I4zF4MDv7ogTNmY5+F\nj1v8OkW9swaSrzPmIInvPNC2dFAQy80/DK7X8S6h3tSjKE3sZcrDdi+3Rte0\nYZtZwby4NRkLfGmAILt6swlEcCDhL92uHUWuX06z5/GsZDYRAp4O4BPmPsNV\nUnflZjqx12aot820CSODekwxetT0e2IQFWNhyn9FdVeGVVJ90rEAhE5A1NbX\nXr/uHMrNl++QXdIoy+HvQxANWPq3QFqmrlZtfXQOJSFS7DPFAp65eFJvRWuo\nhFgOIGRsmxFPLPmOCgP3xp/NOJl4Upen3eabA2xBSVdEJg4yB6OyiUOel5a9\n+xrE/HIOgwFxz+M9p7t2eTqH3LajzqxEFEb2pFwgox/efUVdtKXWckFWiYfg\nWEWwa89Js62El4/6ne8mIka6k9OV6dFHbMWhxH9PE7AUufwhJw1/7JtWAUHZ\n8wXy\r\n=4vE7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFR6jb5t3PGaTtcGfSw0On9rGq2N1CmxrIBxXLB4kpBhAiEA5xDlsglIhfF54mN9sg+xgnDCteuKxlcZ8Qt1Sk7WRwo="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/chownr_1.1.4_1581474654243_0.7221239722665642"}, "_hasShrinkwrap": false}, "2.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "chownr", "description": "like `chown -R`", "version": "2.0.0", "repository": {"type": "git", "url": "git://github.com/isaacs/chownr.git"}, "main": "chownr.js", "devDependencies": {"mkdirp": "0.3", "rimraf": "^2.7.1", "tap": "^14.10.6"}, "tap": {"check-coverage": true}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "license": "ISC", "engines": {"node": ">=10"}, "gitHead": "f9f9d866bebb2f9ff8efc93b78305f9d999c6f17", "bugs": {"url": "https://github.com/isaacs/chownr/issues"}, "homepage": "https://github.com/isaacs/chownr#readme", "_id": "chownr@2.0.0", "_nodeVersion": "13.7.0", "_npmVersion": "6.13.7", "dist": {"integrity": "sha512-bIomtDF5KGpdogkLd9VspvFzk9KfpyyGlS8YFVZl7TGPBHL5snIOnxeshwVgPteQ9b4Eydl+pVbIyE1DcvCWgQ==", "shasum": "15bfbe53d2eab4cf70f18a8cd68ebe5b3cb1dece", "tarball": "https://registry.npmjs.org/chownr/-/chownr-2.0.0.tgz", "fileCount": 4, "unpackedSize": 5748, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeQ2PkCRA9TVsSAnZWagAApeIP/2yqh1W+b3D7qaCsODPY\nG/eO4oYZvjf7XYYvExZs3oiFkHWKHLgIZH0Z1NdhfyxG/pyVnDpF6ewy03z8\nuNYo3aHX3h5bgiwbgAaNWa+ZxhNbeQ8CJWZNH9OYT37aKB9XvamCdLt7btO+\nkJbkYkdlz/3XTMP7CXxsMng0qorjHHk0IyNJZ1Bcy+NSeKPFlyq7/8E5VIK2\nsoz2Zz2pXAi0nKsrJdMzjjAwm50bKRq9eD1gZE2nUFfUjICk0A9d9PTc+2Pn\nalkGyPLMTpTxiTvaWLr+CXAhudfhBbteUVz1CFi6hXR+iVevCqcVewuzWijL\nDlFzTz/TQOR1i6/aH4FUVIdm1BS6jee+JVLCAH58zbdQR1QYQV8MukKHocpH\nWNuLPyX/YyCjU9+LlPMX0pLpikjReZgxZkpZdtIYPtN6u3c4zNhub9jNlaNz\nlcgSAk/0LpH0lSs+Zh3GxBd/O43fXfchPHoIHqILIH8oTRSXLzw6tG+5LNkt\nS/JWmOz+RXp0AzRrPz9ra09ssJIKzYhqprxBLXwvj3MnbZzev9GfDgrPoBCk\n93CA+q3eS32Wg1D18yqC0spgEfsptypdnxWhVijnFZ+egcdMFpTRb6HzKyEj\n5FYbiqMkP2aHnQD7SEIcxQmkF9izZnHfmftOjI2lzjyfeKl9+F46smiJRoeh\nhn9O\r\n=R+L8\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC/ziEIBMm9nLYVawYleu/fQ1UujEqC9o4ZssCQ/CBLKAIhALKls4xaLdIDO4YvLKv5P/PgchXHvwp3avlCHkhFJnHR"}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/chownr_2.0.0_1581474787748_0.7116861792550564"}, "_hasShrinkwrap": false}, "3.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "chownr", "description": "like `chown -R`", "version": "3.0.0", "repository": {"type": "git", "url": "git://github.com/isaacs/chownr.git"}, "devDependencies": {"@types/node": "^20.12.5", "mkdirp": "^3.0.1", "prettier": "^3.2.5", "rimraf": "^5.0.5", "tap": "^18.7.2", "tshy": "^1.13.1", "typedoc": "^0.25.12"}, "scripts": {"prepare": "tshy", "pretest": "npm run prepare", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "format": "prettier --write . --loglevel warn", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts"}, "license": "BlueOak-1.0.0", "engines": {"node": ">=18"}, "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "type": "module", "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "_id": "chownr@3.0.0", "gitHead": "8b9800ac5fe4da0b58bffc9c66dd618f0721472d", "bugs": {"url": "https://github.com/isaacs/chownr/issues"}, "homepage": "https://github.com/isaacs/chownr#readme", "_nodeVersion": "20.11.0", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-+IxzY9BZOQd/XuYPRmrvEVjF/nqj5kgT4kEq7VofrDoM1MxoRjEWkrCC3EtLi59TVawxTAn+orJwFQcrqEN1+g==", "shasum": "9855e64ecd240a9cc4267ce8a4aa5d24a1da15e4", "tarball": "https://registry.npmjs.org/chownr/-/chownr-3.0.0.tgz", "fileCount": 13, "unpackedSize": 22048, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICj2JI5k5zfx2BHcQesjOytbMnelQ7RAI5CZ0velowoUAiBotyrWkSaXkpGsKM9m5yZEK7xq9/AaTT9jAzEV7ri8rQ=="}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/chownr_3.0.0_1712439149991_0.9315611454069008"}, "_hasShrinkwrap": false}}, "readme": "Like `chown -R`.\n\nTakes the same arguments as `fs.chown()`\n", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "time": {"modified": "2024-04-06T21:32:30.344Z", "created": "2012-06-04T04:01:25.807Z", "0.0.1": "2012-06-04T04:01:28.039Z", "0.0.2": "2015-05-20T07:04:02.130Z", "1.0.0": "2015-08-09T22:22:45.361Z", "1.0.1": "2015-08-09T22:24:36.640Z", "1.1.0": "2018-09-16T00:23:19.205Z", "1.1.1": "2018-09-16T03:14:08.990Z", "1.1.2": "2019-07-03T21:32:51.511Z", "1.1.3": "2019-09-25T05:49:10.172Z", "1.1.4": "2020-02-12T02:30:54.444Z", "2.0.0": "2020-02-12T02:33:07.865Z", "3.0.0": "2024-04-06T21:32:30.155Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "repository": {"type": "git", "url": "git://github.com/isaacs/chownr.git"}, "users": {"jswartwood": true, "brandonpapworth": true}, "homepage": "https://github.com/isaacs/chownr#readme", "bugs": {"url": "https://github.com/isaacs/chownr/issues"}, "license": "BlueOak-1.0.0", "readmeFilename": "README.md"}