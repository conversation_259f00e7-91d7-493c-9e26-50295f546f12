{"_id": "rc", "_rev": "192-a4b77bacee073d1a2e75e2162750aead", "name": "rc", "description": "hardwired configuration loader", "dist-tags": {"latest": "1.2.8"}, "versions": {"0.0.1": {"name": "rc", "version": "0.0.1", "description": "hardwired configuration loader", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": "", "keywords": ["config", "rc", "unix", "defaults"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "license": "BSD", "dependencies": {"config-chain": "~0.3.2", "optimist": "~0.3.4"}, "_id": "rc@0.0.1", "dist": {"shasum": "e7f74105d0607bffe4c7d444fdfd5cc589b74ab5", "tarball": "https://registry.npmjs.org/rc/-/rc-0.0.1.tgz", "integrity": "sha512-Y430MQMrLzajiAP1NatCQMwdzlsksU3YpaXs56902zqUW1+ylDPo2a6puzf2LbOIgtzsbHD8f6Js3Xzl1TCE1A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC4BFL8ZDKKOnFz+QcG0E5g8tTgzAusphfHPPQZZ/PEbgIgKWyv2zWJ9FwdSxgJWqAwPd0Nb/uCnGYByAqm1onmZDo="}]}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "directories": {}}, "0.0.2": {"name": "rc", "version": "0.0.2", "description": "hardwired configuration loader", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": "", "keywords": ["config", "rc", "unix", "defaults"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "license": "BSD", "dependencies": {"config-chain": "~0.3.2", "optimist": "~0.3.4"}, "_id": "rc@0.0.2", "dist": {"shasum": "c89ee8eed7abd846886279d4fbf74bc0dd135035", "tarball": "https://registry.npmjs.org/rc/-/rc-0.0.2.tgz", "integrity": "sha512-IdDzM3lTmHdGQGdL4toEdrIq1rJpxL3szk7zUnq6Wrf+ZbkZ6Q2PKwx1YOWZIhOxx3oNDblLnpjMAnMPKIaDew==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD4UyIoI0pwpyIf0srFFyl28vw/SvmVfbIkagsRXdqJOQIhAPH1s/M7ncN2P6j5w9/CUAQhuAAQJOk6WO7AEvJu2Qdn"}]}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "directories": {}}, "0.0.3": {"name": "rc", "version": "0.0.3", "description": "hardwired configuration loader", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": "", "keywords": ["config", "rc", "unix", "defaults"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "license": "BSD", "dependencies": {"config-chain": "~0.3.2", "optimist": "~0.3.4"}, "_id": "rc@0.0.3", "dist": {"shasum": "539d7eb4f688c21698ace152fa1e696067b5cb2e", "tarball": "https://registry.npmjs.org/rc/-/rc-0.0.3.tgz", "integrity": "sha512-KGWeRBq9EpJMqaRHwfTroeyh560fU0SMsQdL763Rvo9jwCPQxnqTkCE0Q+T7FPEowkRiEFifGcxQ1vQRIWUhpg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDb9HXwxfExijE/07IYoXhNQ699g3d28E+6QLRFFUZA7QIhAK168sWzUXK0qwoNoEacaVGRBbwMkhtDnejf89QcTDzz"}]}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "directories": {}}, "0.0.4": {"name": "rc", "version": "0.0.4", "description": "hardwired configuration loader", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "https://github.com/dominictarr/rc.githttps://github.com/dominictarr/rc.git"}, "keywords": ["config", "rc", "unix", "defaults"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "license": "BSD", "dependencies": {"config-chain": "~0.3.2", "optimist": "~0.3.4"}, "_id": "rc@0.0.4", "dist": {"shasum": "94ed0c9dbb34f27337d575f142e286a5b7932b22", "tarball": "https://registry.npmjs.org/rc/-/rc-0.0.4.tgz", "integrity": "sha512-gfGffy36i7/yS1Qqnli+ng8BYJz4p4MnzOX0JREnqQshdzrxP+/F+bE7iChy8ZO09+0suAsPjMWNMFcb/Kgaqg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCm+OtLmI/fUgq8MINeNvrX5dNytZo2NjHYg/L1KPtLzgIgIP4OhnvX79sKFyi4ZXJpVVoSF3BY2qq9tzq0ahVwlf0="}]}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "directories": {}}, "0.0.5": {"name": "rc", "version": "0.0.5", "description": "hardwired configuration loader", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "https://github.com/dominictarr/rc.git"}, "keywords": ["config", "rc", "unix", "defaults"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "license": "BSD", "dependencies": {"config-chain": "~0.3.2", "optimist": "~0.3.4"}, "_id": "rc@0.0.5", "dist": {"shasum": "6320841ae5d84ccfd3112135361b74d9712001bd", "tarball": "https://registry.npmjs.org/rc/-/rc-0.0.5.tgz", "integrity": "sha512-DIs65LvaZJ4D2Aa+ZNb04dhdEZRv/+S99d2SkxGpKhx3RA4c4OY1dxH2G7PdNWvKXEJVeMr7VV70g5GVQFY0Qg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCd0Zw8fMib4YDtlxWmehpGSWWtQWqI+Yu+kwa9ZhdjOQIhAJK0kzBlfOj3on4KVtR1a0LgxwxeEaIe8Kp9vSbuLk2Y"}]}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "directories": {}}, "0.0.6": {"name": "rc", "version": "0.0.6", "description": "hardwired configuration loader", "main": "index.js", "browserify": "browser.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "https://github.com/dominictarr/rc.git"}, "keywords": ["config", "rc", "unix", "defaults"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "license": "BSD", "dependencies": {"config-chain": "~0.3.2", "optimist": "~0.3.4"}, "_id": "rc@0.0.6", "dist": {"shasum": "3414f18eb0651ee04aad1c44687175468b5973c6", "tarball": "https://registry.npmjs.org/rc/-/rc-0.0.6.tgz", "integrity": "sha512-Fs/dVYky3CuickPlDCXvP9B53U7sHJBw4rSbVihlkT5VKsfUFygyYoSLRscZur7YefrUJv9jaf6JE1jvb8ZFSA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCTj8DA1h5CCn23+Q4MudEbnotvF4uPtvalO+bsaeYViAIgAujGuju5J2kBnklYad5LhqnXkn5Wk9qltKu0VjPOEeU="}]}, "_npmVersion": "1.1.65", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "directories": {}}, "0.0.7": {"name": "rc", "version": "0.0.7", "description": "hardwired configuration loader", "main": "index.js", "browserify": "browser.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "https://github.com/dominictarr/rc.git"}, "keywords": ["config", "rc", "unix", "defaults"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "license": "BSD", "dependencies": {"config-chain": "~1.1.3", "optimist": "~0.3.4"}, "_id": "rc@0.0.7", "dist": {"shasum": "08a120085580fade6886e338579632727f70fc21", "tarball": "https://registry.npmjs.org/rc/-/rc-0.0.7.tgz", "integrity": "sha512-RVN30xJuija0qkO69pidtmibt9ehwWELBVmodjTaNQpH5AyWDV9VF8CJ5ozkJYhex1MNnnOMOSupya/EZO/3GA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCwJoRcDRkhrIMOoyvI1KiN6I7UWpIdZzggnaElwsY5UwIhAJhbRIe9gysOHv1bFkUqlAD6daNbPhQA94KI6eXal0QO"}]}, "_npmVersion": "1.1.70", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "directories": {}}, "0.0.8": {"name": "rc", "version": "0.0.8", "description": "hardwired configuration loader", "main": "index.js", "browserify": "browser.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "https://github.com/dominictarr/rc.git"}, "keywords": ["config", "rc", "unix", "defaults"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "license": "BSD", "dependencies": {"config-chain": "~0.3", "optimist": "~0.3.4"}, "_id": "rc@0.0.8", "dist": {"shasum": "e3d280dfc4b9af5b1495f20503de09ccf46aa121", "tarball": "https://registry.npmjs.org/rc/-/rc-0.0.8.tgz", "integrity": "sha512-9dWwGVkZVS5qyEGzIDwdEw6qCVtPYP5zJkxl9zCLWwNNShwhOa/ZZakjIBWw7RjnS43VYhSGk364O6AVCGdkSw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCmXFWvQXCU/kDQasBNVJFLQlWXq3nZaH9g0aiY6v2cYgIhALG2vrqrcVHFzBgIF63i7h78MO1sznRa0DvrwIYqHHzD"}]}, "_from": ".", "_npmVersion": "1.2.3", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "directories": {}}, "0.1.0": {"name": "rc", "version": "0.1.0", "description": "hardwired configuration loader", "main": "index.js", "browserify": "browser.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "https://github.com/dominictarr/rc.git"}, "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./index.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "license": "BSD", "dependencies": {"config-chain": "~0.3", "optimist": "~0.3.4", "deep-extend": "~0.2.5"}, "_id": "rc@0.1.0", "dist": {"shasum": "54d1efb3cfb7683241fcd4fa5c440f7261389f3c", "tarball": "https://registry.npmjs.org/rc/-/rc-0.1.0.tgz", "integrity": "sha512-uOLsX9lK5cYhu8LAZ7Vr/KOWH0jY7ickUB1wmLjoC649awtMUTzzLfwXmtLpppMLdhIUGYTa9RpmWKY7eSQULw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAhEIVQa9xEnbC7Av+JyQ7vRr4mM1i8cvlsky5BmuaY2AiEA6RGarZMTCS4+skIJOEAQdV2CeHSQKl2+b/0nQD9x8Os="}]}, "_from": ".", "_npmVersion": "1.2.3", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "directories": {}}, "0.1.1": {"name": "rc", "version": "0.1.1", "description": "hardwired configuration loader", "main": "index.js", "browserify": "browser.js", "scripts": {"test": "set -e; node test/test.js; node test/ini.js"}, "repository": {"type": "git", "url": "https://github.com/dominictarr/rc.git"}, "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./index.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "license": "BSD", "dependencies": {"optimist": "~0.3.4", "deep-extend": "~0.2.5", "ini": "~1.1.0"}, "_id": "rc@0.1.1", "dist": {"shasum": "5a3fc59ca0b5c00a078e1a10aba928bb6edefb04", "tarball": "https://registry.npmjs.org/rc/-/rc-0.1.1.tgz", "integrity": "sha512-qHWi5wqEqK/3QcT8wYEW9idVGMua7OBcDKrCxOfDIuW3QDwYrOQgpAiCGlwqUGj3UUw4Xa/mIgF6FjpCMwbrSw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC/67jHP0B8AaHqBBDjs3iz+yTJJKz0qg9XF3ancVC1TAIgTJUZlOxNP6phW2qWbg5aXp3wFDUpp6ZU0gZcJPo5Jkg="}]}, "_from": ".", "_npmVersion": "1.2.3", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "directories": {}}, "0.1.2": {"name": "rc", "version": "0.1.2", "description": "hardwired configuration loader", "main": "index.js", "browserify": "browser.js", "scripts": {"test": "set -e; node test/test.js; node test/ini.js"}, "repository": {"type": "git", "url": "https://github.com/dominictarr/rc.git"}, "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./index.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "licenses": ["BSD", "MIT", "Apache2"], "dependencies": {"optimist": "~0.3.4", "deep-extend": "~0.2.5", "ini": "~1.1.0"}, "_id": "rc@0.1.2", "dist": {"shasum": "17fc9e976e4bed6540c64a11c6fd6bf59a2d4ee0", "tarball": "https://registry.npmjs.org/rc/-/rc-0.1.2.tgz", "integrity": "sha512-eFgoeyuJJLA9TvFxkDj0FdtLKodASuEGvVa/ISpgQgw8L/P0tdJ+nTlwy5r6Ut3zRAQFIRqQq/iv6pTSGvBW/g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDn6YPVC55iOihgV+ELALd8uuoLP5Qw4D/LsxF+Mh/h5gIgb9PBUVEtnDuXbz2NccOPAH+2N+Vite/foA8yVYEY2To="}]}, "_from": ".", "_npmVersion": "1.2.3", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "directories": {}}, "0.1.3": {"name": "rc", "version": "0.1.3", "description": "hardwired configuration loader", "main": "index.js", "browserify": "browser.js", "scripts": {"test": "set -e; node test/test.js; node test/ini.js"}, "repository": {"type": "git", "url": "https://github.com/dominictarr/rc.git"}, "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./index.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "licenses": ["BSD", "MIT", "Apache2"], "dependencies": {"optimist": "~0.3.4", "deep-extend": "~0.2.5", "ini": "~1.1.0"}, "_id": "rc@0.1.3", "dist": {"shasum": "7c1c841ec6c58e7d536af0b8e41377d05f607369", "tarball": "https://registry.npmjs.org/rc/-/rc-0.1.3.tgz", "integrity": "sha512-UZPIJGRJJP1TEJH6NOJzCsMTP9v5nMrYubLYn9gmyB1DPPNVSBGI74DkWLwxjNcqDJ6sH59tJ6XS4hw4BYxNJA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC1LpQTcOqkYgQskAET+J/UQQCWMuJUM73RFyktIpXs3wIgGNMM4QZyD+vwL9+4Ph6/afd+sjH8AePx67X4O8XSh/Y="}]}, "_from": ".", "_npmVersion": "1.2.3", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "directories": {}}, "0.2.0": {"name": "rc", "version": "0.2.0", "description": "hardwired configuration loader", "main": "index.js", "browserify": "browser.js", "scripts": {"test": "set -e; node test/test.js; node test/ini.js"}, "repository": {"type": "git", "url": "https://github.com/dominictarr/rc.git"}, "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./index.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "licenses": ["BSD", "MIT", "Apache2"], "dependencies": {"optimist": "~0.3.4", "deep-extend": "~0.2.5", "ini": "~1.1.0"}, "_id": "rc@0.2.0", "dist": {"shasum": "8c169e4fef2ddd3f4ee0fae1afd59b1da98c8d16", "tarball": "https://registry.npmjs.org/rc/-/rc-0.2.0.tgz", "integrity": "sha512-+F65blXxvv2JHdNraEL1IZimIAlvrzcmzHNZx472zDeEiWDXhzCitLo4Tc3IZlFgV542Mrt0m+6IYZdeHkvmdA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGfmCt0CM03WR1LAtIfauQ/WKbGrllkPCoDRFjaFVSclAiAaD/BK4JbCcIh3EmFoLQBdKz+Gtcp2HMTEIMywcmZuug=="}]}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "directories": {}}, "0.3.0": {"name": "rc", "version": "0.3.0", "description": "hardwired configuration loader", "main": "index.js", "browserify": "browser.js", "scripts": {"test": "set -e; node test/test.js; node test/ini.js"}, "repository": {"type": "git", "url": "https://github.com/dominictarr/rc.git"}, "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./index.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "licenses": ["BSD", "MIT", "Apache2"], "dependencies": {"optimist": "~0.3.4", "deep-extend": "~0.2.5", "ini": "~1.1.0"}, "_id": "rc@0.3.0", "dist": {"shasum": "caffdaafc17e8608e50db0c6ee63f1c344d9ac58", "tarball": "https://registry.npmjs.org/rc/-/rc-0.3.0.tgz", "integrity": "sha512-FmkMbxM++hZK9i7l/QrgeoAjE5j+HCdsmZgHO2VA2KrsQigPqf9KVu45dlzdhk6NdYOM/xoj78nGVAiWIdwcMA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDyr7hYqZxPsOYHnjb1NPRU+k/nxrG6SKZ1t4Htk3QgkAIhALhecBP9+t/emifpmTLoeJn1dNtJFROMIcIZHUtYyykn"}]}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "directories": {}}, "0.3.1": {"name": "rc", "version": "0.3.1", "description": "hardwired configuration loader", "main": "index.js", "browserify": "browser.js", "scripts": {"test": "set -e; node test/test.js; node test/ini.js"}, "repository": {"type": "git", "url": "https://github.com/dominictarr/rc.git"}, "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./index.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "licenses": ["BSD", "MIT", "Apache2"], "dependencies": {"optimist": "~0.3.4", "deep-extend": "~0.2.5", "ini": "~1.1.0"}, "bugs": {"url": "https://github.com/dominictarr/rc/issues"}, "_id": "rc@0.3.1", "dist": {"shasum": "1da1bef8cf8201cafd3725bd82b31d1cf7321248", "tarball": "https://registry.npmjs.org/rc/-/rc-0.3.1.tgz", "integrity": "sha512-wSbH13wNe85fB66nBLX5IDS+4znImihay5axsj/j2qlz4eZ1rkrWAoXRG+mlpzbAW0mthIkkhEksySFZoc/A6Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEnlGVHZIhWG7CHBtmTPCBJCQ8BmS20itu+u0EoBOW6GAiATFlEY46bBJkArraHMY7a3XOGUMbWeC5jSTbO6MW8GuA=="}]}, "_from": ".", "_npmVersion": "1.3.6", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "directories": {}}, "0.3.2": {"name": "rc", "version": "0.3.2", "description": "hardwired configuration loader", "main": "index.js", "browserify": "browser.js", "scripts": {"test": "set -e; node test/test.js; node test/ini.js"}, "repository": {"type": "git", "url": "https://github.com/dominictarr/rc.git"}, "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./index.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "licenses": ["BSD", "MIT", "Apache2"], "dependencies": {"optimist": "~0.3.4", "deep-extend": "~0.2.5", "ini": "~1.1.0"}, "bugs": {"url": "https://github.com/dominictarr/rc/issues"}, "_id": "rc@0.3.2", "dist": {"shasum": "cd8f895690f764621ccec110516b225286e8f69f", "tarball": "https://registry.npmjs.org/rc/-/rc-0.3.2.tgz", "integrity": "sha512-K0QRPd5/Y16UvQunJJ6mghYt3gLY8UD0KQPZIuaCr9/oyj4Mjy02SS4GaelpET2fnUbZ/yml7BRg3FZXmHLBrw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCFZ3gYrLj0zCYb9CzpTXShzh4T6pI6dTEd9sH4Vwxo5QIhAJcGVsAsDKYcctHSSEhnLhGrPfRXvtm3beW5LZSuLp/j"}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "directories": {}}, "0.3.3": {"name": "rc", "version": "0.3.3", "description": "hardwired configuration loader", "main": "index.js", "browserify": "browser.js", "scripts": {"test": "set -e; node test/test.js; node test/ini.js"}, "repository": {"type": "git", "url": "https://github.com/dominictarr/rc.git"}, "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./index.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "licenses": ["BSD", "MIT", "Apache2"], "dependencies": {"optimist": "~0.3.4", "deep-extend": "~0.2.5", "ini": "~1.1.0"}, "bugs": {"url": "https://github.com/dominictarr/rc/issues"}, "homepage": "https://github.com/dominictarr/rc", "_id": "rc@0.3.3", "dist": {"shasum": "2eac52d221dfdf5ba512def3ea27500a68dc3c07", "tarball": "https://registry.npmjs.org/rc/-/rc-0.3.3.tgz", "integrity": "sha512-1bXQu6AO7ALoX8gyxwXqCzqYHj09YRJ2CmsLrHylqzMZbgwmRyV2fm+pY2rHsugLi8KGQsCzccwtInOKhO6qjg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCa6Y/XZwrz65bfjiAetiSyQbZ/1fI5Drei4O6+F1j0agIhAJmYnoZxsm8PRWDgbR+nWD4UkSxlofczQMHvVNuXPS7P"}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "directories": {}}, "0.3.4": {"name": "rc", "version": "0.3.4", "description": "hardwired configuration loader", "main": "index.js", "browserify": "browser.js", "scripts": {"test": "set -e; node test/test.js; node test/ini.js"}, "repository": {"type": "git", "url": "https://github.com/dominictarr/rc.git"}, "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./index.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "licenses": ["BSD", "MIT", "Apache2"], "dependencies": {"minimist": "~0.0.7", "deep-extend": "~0.2.5", "ini": "~1.1.0"}, "bugs": {"url": "https://github.com/dominictarr/rc/issues"}, "homepage": "https://github.com/dominictarr/rc", "_id": "rc@0.3.4", "dist": {"shasum": "01101660fb38363c22afbddfad46ecdb92b42df9", "tarball": "https://registry.npmjs.org/rc/-/rc-0.3.4.tgz", "integrity": "sha512-oMTVslgIJboMk2qfhlPX3huOfzOm5MLAwfW0te+QnM5kgOHaV/lAaqZTwHUhOu3g9rE3LI8njyzk/99yCmcnnw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB78jALSAqJQ01ovlRkZvan6q6K25x6p6Y9Zx7Q5r1aEAiB8+uLnLuONt/Hk9/7JY6u0ibILb05YdQBBHh7E95aBrA=="}]}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "directories": {}}, "0.3.5": {"name": "rc", "version": "0.3.5", "description": "hardwired configuration loader", "main": "index.js", "browserify": "browser.js", "scripts": {"test": "set -e; node test/test.js; node test/ini.js"}, "repository": {"type": "git", "url": "https://github.com/dominictarr/rc.git"}, "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./index.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "licenses": ["BSD", "MIT", "Apache2"], "dependencies": {"minimist": "~0.0.7", "deep-extend": "~0.2.5", "ini": "~1.1.0"}, "bugs": {"url": "https://github.com/dominictarr/rc/issues"}, "homepage": "https://github.com/dominictarr/rc", "_id": "rc@0.3.5", "dist": {"shasum": "fce2220593be57aa1296685a7e37ed003dfcc728", "tarball": "https://registry.npmjs.org/rc/-/rc-0.3.5.tgz", "integrity": "sha512-QOAK72kdSN8d1wkK1QwHGksba0AyUT/tWQPsFQGHmBPSoPY8Boqr26YATE/nCt/RQ1+iijOUpDjY1QdWBVVMtw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCCTGdllu181klV25YbYMB9FNpuHTB2vu8hblxPWYwfpwIgSr3bg7Hgv2ge6ftlwAxsBuSkKLp0g4xgOKkHpPqWrFY="}]}, "_from": ".", "_npmVersion": "1.4.6", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "directories": {}}, "0.4.0": {"name": "rc", "version": "0.4.0", "description": "hardwired configuration loader", "main": "index.js", "browserify": "browser.js", "scripts": {"test": "set -e; node test/test.js; node test/ini.js"}, "repository": {"type": "git", "url": "https://github.com/dominictarr/rc.git"}, "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./index.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "licenses": ["BSD", "MIT", "Apache2"], "dependencies": {"minimist": "~0.0.7", "deep-extend": "~0.2.5", "strip-json-comments": "0.1.x", "ini": "~1.1.0"}, "bugs": {"url": "https://github.com/dominictarr/rc/issues"}, "homepage": "https://github.com/dominictarr/rc", "_id": "rc@0.4.0", "_shasum": "ce24a2029ad94c3a40d09604a87227027d7210d3", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "dist": {"shasum": "ce24a2029ad94c3a40d09604a87227027d7210d3", "tarball": "https://registry.npmjs.org/rc/-/rc-0.4.0.tgz", "integrity": "sha512-EKfbybKSjhlaJG8FYEDJSwSBnaYUQvV+NJaL2dM1HqqdRVkixkC2nRreI1yrTG5oORgMMh2Q3///e7wVUz//ag==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCQ7nH1g0QiUqaTZSvumLZpkW73XaQyo3SHTHlllDck2QIhAPq+5OtV2mMQbeQSD+rtwJb5lBXqbQrxWhL5A7AzeWE0"}]}, "directories": {}}, "0.5.0": {"name": "rc", "version": "0.5.0", "description": "hardwired configuration loader", "main": "index.js", "browserify": "browser.js", "scripts": {"test": "set -e; node test/test.js; node test/ini.js; node test/nested-env-vars.js"}, "repository": {"type": "git", "url": "https://github.com/dominictarr/rc.git"}, "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./index.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "licenses": ["BSD", "MIT", "Apache2"], "dependencies": {"minimist": "~0.0.7", "deep-extend": "~0.2.5", "strip-json-comments": "0.1.x", "ini": "~1.1.0"}, "bugs": {"url": "https://github.com/dominictarr/rc/issues"}, "homepage": "https://github.com/dominictarr/rc", "_id": "rc@0.5.0", "_shasum": "ef5c93e645cc0bc9b7318ed24fc804f36073e6df", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "dist": {"shasum": "ef5c93e645cc0bc9b7318ed24fc804f36073e6df", "tarball": "https://registry.npmjs.org/rc/-/rc-0.5.0.tgz", "integrity": "sha512-e3M1Un1lwDilsLlIqF7KFDPybj4kZoI6MhlIUBVXR8RE5n/Y2itMDddOpgralfJmBrF3ZAyn0I/3Ww3oCvH+XQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD7AlSzOoEnyKXKFxE98I/oN7xSdWM8N9IWSmxJ82ZqHAIhANetdgEkoYmWmbQkvmx8Fqzm3tafNRREyjpvDknaKo1+"}]}, "directories": {}}, "0.5.1": {"name": "rc", "version": "0.5.1", "description": "hardwired configuration loader", "main": "index.js", "browserify": "browser.js", "scripts": {"test": "set -e; node test/test.js; node test/ini.js; node test/nested-env-vars.js"}, "repository": {"type": "git", "url": "https://github.com/dominictarr/rc.git"}, "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./index.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "licenses": ["BSD", "MIT", "Apache2"], "dependencies": {"minimist": "~0.0.7", "deep-extend": "~0.2.5", "strip-json-comments": "0.1.x", "ini": "~1.1.0"}, "gitHead": "24cc92c2f9c6d7193c283bc6be7164d6fa4fcf23", "bugs": {"url": "https://github.com/dominictarr/rc/issues"}, "homepage": "https://github.com/dominictarr/rc", "_id": "rc@0.5.1", "_shasum": "b88ef9421a08151352a659e0c3a58c4b82eb7576", "_from": ".", "_npmVersion": "1.4.26", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "dist": {"shasum": "b88ef9421a08151352a659e0c3a58c4b82eb7576", "tarball": "https://registry.npmjs.org/rc/-/rc-0.5.1.tgz", "integrity": "sha512-B1EiD9IPNyNm0i4JAdyakpxah4jNUmxbx5uhaVRmjZ/py4MnjNLxX2hyk+kTQVatPDgdquY3pat+HsGj6n87Vg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDcMrNqN58xDZbP7A2gjDKeEOrcBMi4abtx81qlS7sA4AIhAPbFKobIPqN7+/cXTfDpNX/qHido7bxjmw/sKgwhlRtV"}]}, "directories": {}}, "0.5.2": {"name": "rc", "version": "0.5.2", "description": "hardwired configuration loader", "main": "index.js", "browserify": "browser.js", "scripts": {"test": "set -e; node test/test.js; node test/ini.js; node test/nested-env-vars.js"}, "repository": {"type": "git", "url": "https://github.com/dominictarr/rc.git"}, "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./index.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "licenses": ["BSD", "MIT", "Apache2"], "dependencies": {"minimist": "~0.0.7", "deep-extend": "~0.2.5", "strip-json-comments": "0.1.x", "ini": "~1.1.0"}, "gitHead": "7b07492852a07de465547bb1a3bad0fec75fb4b1", "bugs": {"url": "https://github.com/dominictarr/rc/issues"}, "homepage": "https://github.com/dominictarr/rc", "_id": "rc@0.5.2", "_shasum": "6b8e05894a158e3d4a15e7daddb970f4d15a024f", "_from": ".", "_npmVersion": "1.4.26", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "dist": {"shasum": "6b8e05894a158e3d4a15e7daddb970f4d15a024f", "tarball": "https://registry.npmjs.org/rc/-/rc-0.5.2.tgz", "integrity": "sha512-MEO3tvG7jJFbJNUMXhvynsTRcAfe2es1BOwsY69v70SyYOL3VTUpOL9SIJZAs/WJe0xA8s77UaYYrrQ+IfyAVw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDcutsldNwVSaHG39MuDd06NfgB9lqb+H+wGJ2Ol8UlYwIhAKUt/QNIsZ6eoMd8PYGK8jv4bA6i54XfoIbZDEkwZW+/"}]}, "directories": {}}, "0.5.3": {"name": "rc", "version": "0.5.3", "description": "hardwired configuration loader", "main": "index.js", "browserify": "browser.js", "scripts": {"test": "set -e; node test/test.js; node test/ini.js; node test/nested-env-vars.js"}, "repository": {"type": "git", "url": "https://github.com/dominictarr/rc.git"}, "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./index.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "licenses": ["BSD", "MIT", "Apache2"], "dependencies": {"minimist": "~0.0.7", "deep-extend": "~0.2.5", "strip-json-comments": "0.1.x", "ini": "~1.1.0"}, "gitHead": "af9b3ad9d2dc65501c871c8bc4b30d9b8fe23c48", "bugs": {"url": "https://github.com/dominictarr/rc/issues"}, "homepage": "https://github.com/dominictarr/rc", "_id": "rc@0.5.3", "_shasum": "2247b3baf3219ccfd7e489ffd3eafa0640b37fd9", "_from": ".", "_npmVersion": "1.4.26", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "dist": {"shasum": "2247b3baf3219ccfd7e489ffd3eafa0640b37fd9", "tarball": "https://registry.npmjs.org/rc/-/rc-0.5.3.tgz", "integrity": "sha512-TVxfsD8h7nhCDMuU6Kka0JFg08N6on7sdZq5ibyD0uK0eFP3PKLzPSrYA0ghhv+Q8YYSKdx5PZh3/BJCQD9QIw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCN2AUJSbMUYyHVtxaViO/MDKDCxZPeY1DU8EzCfjaFLQIhANxhL5+Su+eeuKYmS0p/uO7F3ulbFBGRv615AFts/ZBY"}]}, "directories": {}}, "0.5.4": {"name": "rc", "version": "0.5.4", "description": "hardwired configuration loader", "main": "index.js", "browserify": "browser.js", "scripts": {"test": "set -e; node test/test.js; node test/ini.js; node test/nested-env-vars.js"}, "repository": {"type": "git", "url": "https://github.com/dominictarr/rc.git"}, "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./index.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "licenses": ["BSD", "MIT", "Apache2"], "dependencies": {"minimist": "~0.0.7", "deep-extend": "~0.2.5", "strip-json-comments": "0.1.x", "ini": "~1.1.0"}, "gitHead": "b670e6dce7a9295c2629cdb881e78e6ff91875f4", "bugs": {"url": "https://github.com/dominictarr/rc/issues"}, "homepage": "https://github.com/dominictarr/rc", "_id": "rc@0.5.4", "_shasum": "14169483ccb33b2695a349dbd96ef54e4e92d54f", "_from": ".", "_npmVersion": "1.4.26", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "dist": {"shasum": "14169483ccb33b2695a349dbd96ef54e4e92d54f", "tarball": "https://registry.npmjs.org/rc/-/rc-0.5.4.tgz", "integrity": "sha512-+nZeuzZh5CJyUCZgzmgJTwvslF31AlBqWTOhXDRgO8z4mKHJhPVbuig86GImSGDtOva+R+/TdEni46lL1ji7Gw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHmm0ZSKq0OvLQnlS1KQQLmLrHrt4kMSa9SCgVn3XVgTAiBH1E74SzVerm79sm5GP0rYrrPc2D38ig5k+JurrZBB9A=="}]}, "directories": {}}, "0.5.5": {"name": "rc", "version": "0.5.5", "description": "hardwired configuration loader", "main": "index.js", "browserify": "browser.js", "scripts": {"test": "set -e; node test/test.js; node test/ini.js; node test/nested-env-vars.js"}, "repository": {"type": "git", "url": "https://github.com/dominictarr/rc.git"}, "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./index.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "licenses": ["BSD", "MIT", "Apache2"], "dependencies": {"minimist": "~0.0.7", "deep-extend": "~0.2.5", "strip-json-comments": "0.1.x", "ini": "~1.3.0"}, "gitHead": "eb2ef879b3740bcdf686d9d269977121b3571523", "bugs": {"url": "https://github.com/dominictarr/rc/issues"}, "homepage": "https://github.com/dominictarr/rc", "_id": "rc@0.5.5", "_shasum": "541cc3300f464b6dfe6432d756f0f2dd3e9eb199", "_from": ".", "_npmVersion": "2.1.11", "_nodeVersion": "0.10.31", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "dist": {"shasum": "541cc3300f464b6dfe6432d756f0f2dd3e9eb199", "tarball": "https://registry.npmjs.org/rc/-/rc-0.5.5.tgz", "integrity": "sha512-QkkuJHJX7fh7nCFLPIZ9h/E2/V9ZULgH3GsQtS1N2ZbHYmRtp+ZE37mB79ZlFa/AM0zoE8qjzTpeu8Rwer00ug==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCMYsYz096kN16cwOCHF18cnQ6+5su5a8pIEXVGQue75AIhAOdGUs0U8F+KZPq6YKDeFpww7gxISmnPMhl09Q+U9F15"}]}, "directories": {}}, "0.6.0": {"name": "rc", "version": "0.6.0", "description": "hardwired configuration loader", "main": "index.js", "browserify": "browser.js", "scripts": {"test": "set -e; node test/test.js; node test/ini.js; node test/nested-env-vars.js"}, "repository": {"type": "git", "url": "https://github.com/dominictarr/rc.git"}, "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./index.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "licenses": ["BSD", "MIT", "Apache2"], "dependencies": {"minimist": "~0.0.7", "deep-extend": "~0.2.5", "strip-json-comments": "0.1.x", "ini": "~1.3.0"}, "gitHead": "1f2ccd5baeb81933b754e8ced208c7f91b870f2d", "bugs": {"url": "https://github.com/dominictarr/rc/issues"}, "homepage": "https://github.com/dominictarr/rc", "_id": "rc@0.6.0", "_shasum": "e1c930059af831c85413fe275ae2f40f4e3c5371", "_from": ".", "_npmVersion": "2.4.1", "_nodeVersion": "0.10.35", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "dist": {"shasum": "e1c930059af831c85413fe275ae2f40f4e3c5371", "tarball": "https://registry.npmjs.org/rc/-/rc-0.6.0.tgz", "integrity": "sha512-CyXW7wwc9dN+GZ3puG6NZyeIY4P8SrXdLLB5OAF/kJzHZOWFt4Pdzm7LbRRoyElkwuC4vj/TxnZyoNsySALYjg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCIdL5vUVkRJH5hrsgs9pAYxI+SRn1OP6UIMKQ+7qNT3gIgd/vbWgkZnDg/oXz22+GT5/mKBiboBLR5RZCZUJUZxiM="}]}, "directories": {}}, "1.0.0": {"name": "rc", "version": "1.0.0", "description": "hardwired configuration loader", "main": "index.js", "browserify": "browser.js", "scripts": {"test": "set -e; node test/test.js; node test/ini.js; node test/nested-env-vars.js"}, "repository": {"type": "git", "url": "https://github.com/dominictarr/rc.git"}, "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./index.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "licenses": ["BSD", "MIT", "Apache2"], "dependencies": {"minimist": "~0.0.7", "deep-extend": "~0.2.5", "strip-json-comments": "0.1.x", "ini": "~1.3.0"}, "gitHead": "647ba0b545c1e82bf4a9e45fdbdc27ecb364137e", "bugs": {"url": "https://github.com/dominictarr/rc/issues"}, "homepage": "https://github.com/dominictarr/rc", "_id": "rc@1.0.0", "_shasum": "17f0266df3bc4a5c1ee8b8b1f1b86ab181c3cd62", "_from": ".", "_npmVersion": "2.4.1", "_nodeVersion": "0.10.35", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "dist": {"shasum": "17f0266df3bc4a5c1ee8b8b1f1b86ab181c3cd62", "tarball": "https://registry.npmjs.org/rc/-/rc-1.0.0.tgz", "integrity": "sha512-fN2ntXCXoc8KvLrw6ljXTOOFilmiNhVFJsJu4O9tiUCMxOOEAeVUV7n+tx+zm/xsQ6MTtKkAHimavNSR1jhZlA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC+lJBo939h2mX74jsVIiRh/te9e1Ty1Sgu+GIFpte+dAiAfqY5phMTLOdv1AnwoZVnVP4fXnZFx7k+cuSdEaPwXjQ=="}]}, "directories": {}}, "1.0.1": {"name": "rc", "version": "1.0.1", "description": "hardwired configuration loader", "main": "index.js", "browserify": "browser.js", "scripts": {"test": "set -e; node test/test.js; node test/ini.js; node test/nested-env-vars.js"}, "repository": {"type": "git", "url": "https://github.com/dominictarr/rc.git"}, "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./index.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "licenses": ["BSD", "MIT", "Apache2"], "dependencies": {"minimist": "~0.0.7", "deep-extend": "~0.2.5", "strip-json-comments": "0.1.x", "ini": "~1.3.0"}, "gitHead": "e5aec73dfea4ab8dcaba58732e51e1b39991bcd2", "bugs": {"url": "https://github.com/dominictarr/rc/issues"}, "homepage": "https://github.com/dominictarr/rc", "_id": "rc@1.0.1", "_shasum": "f919c25e804cb0aa60f6fd92d929fc86b45013e8", "_from": ".", "_npmVersion": "2.4.1", "_nodeVersion": "0.10.35", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "dist": {"shasum": "f919c25e804cb0aa60f6fd92d929fc86b45013e8", "tarball": "https://registry.npmjs.org/rc/-/rc-1.0.1.tgz", "integrity": "sha512-ETZrj3/z/7vVJDa05GuMAeWAWdKQwA/J9LGJgXF4GeXD8AlEFomc4mAoPdQbTTfOfeOhXBdhhaTi4gKAPpEeLg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH4RYfcvJhJjwv/LFNYeEgMGhxW0+h79s3Bo242WPLBZAiEAsLeOICqs8OvkoO2ES1tnVg8CJzknaa3gR6AqrhDEIx0="}]}, "directories": {}}, "1.0.2": {"name": "rc", "version": "1.0.2", "description": "hardwired configuration loader", "main": "index.js", "browserify": "browser.js", "scripts": {"test": "set -e; node test/test.js; node test/ini.js; node test/nested-env-vars.js"}, "repository": {"type": "git", "url": "https://github.com/dominictarr/rc.git"}, "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./index.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "license": "(BSD OR MIT OR Apache2)", "dependencies": {"minimist": "~0.0.7", "deep-extend": "~0.2.5", "strip-json-comments": "0.1.x", "ini": "~1.3.0"}, "gitHead": "27a9370ee2be826320a5d593c164125552ab38af", "bugs": {"url": "https://github.com/dominictarr/rc/issues"}, "homepage": "https://github.com/dominictarr/rc", "_id": "rc@1.0.2", "_shasum": "c31d997029ab8f76420462b2717880a7d7e83568", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "dist": {"shasum": "c31d997029ab8f76420462b2717880a7d7e83568", "tarball": "https://registry.npmjs.org/rc/-/rc-1.0.2.tgz", "integrity": "sha512-7bHDh7fxAldPinj+jSfG+IH0BYJCH5f3dVv8vwCE6R8KgrNyLtuBTk5kur6TJP/A9ZIkZFlaq+N7iWXVVfUcxw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH4nOOqbqgNLqkpNxivVbQN0lEWECSdAzeWhyQEuZxwcAiB7wyKlLqSpd/Q8Zu0VRgsWqrHT1kyzNb8+9YXdLVVAAw=="}]}, "directories": {}}, "1.0.3": {"name": "rc", "version": "1.0.3", "description": "hardwired configuration loader", "main": "index.js", "browserify": "browser.js", "scripts": {"test": "set -e; node test/test.js; node test/ini.js; node test/nested-env-vars.js"}, "repository": {"type": "git", "url": "https://github.com/dominictarr/rc.git"}, "license": "(BSD OR MIT OR Apache2)", "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./index.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "dependencies": {"minimist": "~0.0.7", "deep-extend": "~0.2.5", "strip-json-comments": "0.1.x", "ini": "~1.3.0"}, "gitHead": "2a4f80785efa317eaf38499fa1f9dcd3c60e54e9", "bugs": {"url": "https://github.com/dominictarr/rc/issues"}, "homepage": "https://github.com/dominictarr/rc", "_id": "rc@1.0.3", "_shasum": "51bf28d21f13a9324528a9633460161ad9a39f77", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "dist": {"shasum": "51bf28d21f13a9324528a9633460161ad9a39f77", "tarball": "https://registry.npmjs.org/rc/-/rc-1.0.3.tgz", "integrity": "sha512-ciRvtwCjcw/nIs/LeUA8BjPjS58zad0+8/5Q7Vxb1Bim8HOi4QAw3xWu7wYLgTnMSsyOLwg57t7Ik71OgpcuyA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCoULgo2qZMZhesZg82BzeIdVbReudHupZEhF/+iOta2AIhAPRhYrmyG3LkYsdWS3BdSG6j1T39kZPmdH1yd6rodWM4"}]}, "directories": {}}, "1.1.0": {"name": "rc", "version": "1.1.0", "description": "hardwired configuration loader", "main": "index.js", "browserify": "browser.js", "scripts": {"test": "set -e; node test/test.js; node test/ini.js; node test/nested-env-vars.js"}, "repository": {"type": "git", "url": "git+https://github.com/dominictarr/rc.git"}, "license": "(BSD-2-<PERSON><PERSON> OR MIT OR Apache-2.0)", "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./index.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "dependencies": {"minimist": "^1.1.2", "deep-extend": "~0.2.5", "strip-json-comments": "0.1.x", "ini": "~1.3.0"}, "gitHead": "12c770e344a9bc92387240c1b5dc6ee1cc4e8fa8", "bugs": {"url": "https://github.com/dominictarr/rc/issues"}, "homepage": "https://github.com/dominictarr/rc#readme", "_id": "rc@1.1.0", "_shasum": "979b5f3dd75e2afa2f113f003abfaf11fc326aa6", "_from": ".", "_npmVersion": "2.12.0", "_nodeVersion": "2.3.1", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "dist": {"shasum": "979b5f3dd75e2afa2f113f003abfaf11fc326aa6", "tarball": "https://registry.npmjs.org/rc/-/rc-1.1.0.tgz", "integrity": "sha512-WJICMOUI6vCI6sphqpyBX4luxYFTjXLXcHsjlkzuJCewoclOuCD26DLE4slzZgKFdD09pXEO5qJPXcRqsJq3iQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGlESvJh4HAJtXpUdSWQHTjE04tQknPZ30XJncJLHdXqAiBtoVv9LUhFNCVmzbzVqPQtVkUZt8a+1peuwIHA0ZgX2w=="}]}, "directories": {}}, "1.1.1": {"name": "rc", "version": "1.1.1", "description": "hardwired configuration loader", "main": "index.js", "browserify": "browser.js", "scripts": {"test": "set -e; node test/test.js; node test/ini.js; node test/nested-env-vars.js"}, "repository": {"type": "git", "url": "git+https://github.com/dominictarr/rc.git"}, "license": "(BSD-2-<PERSON><PERSON> OR MIT OR Apache-2.0)", "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./index.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "dependencies": {"minimist": "^1.1.2", "deep-extend": "~0.2.5", "strip-json-comments": "0.1.x", "ini": "~1.3.0"}, "gitHead": "836087bc6df40efb690c52df89f3589fbd656f9d", "bugs": {"url": "https://github.com/dominictarr/rc/issues"}, "homepage": "https://github.com/dominictarr/rc#readme", "_id": "rc@1.1.1", "_shasum": "56e161429f16164cd7fd9302cb964486621f419a", "_from": ".", "_npmVersion": "3.3.1", "_nodeVersion": "2.3.1", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "dist": {"shasum": "56e161429f16164cd7fd9302cb964486621f419a", "tarball": "https://registry.npmjs.org/rc/-/rc-1.1.1.tgz", "integrity": "sha512-s0V7caU4LWJ8aIMpS2scuc4QYb3IywJQ4WK6vltINx7Mhj80+kCtNkprzVsbKZAwyh3JIWCi12aBvqaqKNFCmw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHugoYIOLWKly1Ty5+78vLQD1vGBbOhuveTGRDLPYPe1AiAIRBbNW+luLZPESzwHoBqHnZz8KntkWSbCnovOiIopSQ=="}]}, "directories": {}}, "1.1.2": {"name": "rc", "version": "1.1.2", "description": "hardwired configuration loader", "main": "index.js", "browserify": "browser.js", "scripts": {"test": "set -e; node test/test.js; node test/ini.js; node test/nested-env-vars.js"}, "repository": {"type": "git", "url": "git+https://github.com/dominictarr/rc.git"}, "license": "(BSD-2-<PERSON><PERSON> OR MIT OR Apache-2.0)", "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./index.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "dependencies": {"minimist": "^1.1.2", "deep-extend": "~0.2.5", "strip-json-comments": "0.1.x", "ini": "~1.3.0"}, "gitHead": "d6ac3a3ac1555b25964e963fb949cd1d30b32c0f", "bugs": {"url": "https://github.com/dominictarr/rc/issues"}, "homepage": "https://github.com/dominictarr/rc#readme", "_id": "rc@1.1.2", "_shasum": "8828cab62d8054602c063d9e5572686988c1c468", "_from": ".", "_npmVersion": "3.3.1", "_nodeVersion": "2.3.1", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "dist": {"shasum": "8828cab62d8054602c063d9e5572686988c1c468", "tarball": "https://registry.npmjs.org/rc/-/rc-1.1.2.tgz", "integrity": "sha512-Ui6OZgbLVMO3+xaZJnfh7mHf/TLRZsNYfukFzp1UcHaNfyeKU7FCmD3BPuhrtLoAe6sCxLItH6bMaI/TylkStg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEJBV4IkzMUYXp3Fu2I5W4wHmoxuIGQAMCKCpZrE3ARcAiEAwg7Ag8h6CWrYX/wPW801c4iryooTs41tsXkKLieN+eY="}]}, "directories": {}}, "1.1.3": {"name": "rc", "version": "1.1.3", "description": "hardwired configuration loader", "main": "index.js", "browserify": "browser.js", "scripts": {"test": "set -e; node test/test.js; node test/ini.js; node test/nested-env-vars.js"}, "repository": {"type": "git", "url": "git+https://github.com/dominictarr/rc.git"}, "license": "(BSD-2-<PERSON><PERSON> OR MIT OR Apache-2.0)", "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./index.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "dependencies": {"deep-extend": "~0.4.0", "ini": "~1.3.0", "minimist": "~1.1.2", "strip-json-comments": "~1.0.4"}, "gitHead": "14f71cc496488eb93bc2c475fe953aec24bb5492", "bugs": {"url": "https://github.com/dominictarr/rc/issues"}, "homepage": "https://github.com/dominictarr/rc#readme", "_id": "rc@1.1.3", "_shasum": "8baef13fb8f9a97d716a012de892d910af30356a", "_from": ".", "_npmVersion": "2.14.8", "_nodeVersion": "4.2.1", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "dist": {"shasum": "8baef13fb8f9a97d716a012de892d910af30356a", "tarball": "https://registry.npmjs.org/rc/-/rc-1.1.3.tgz", "integrity": "sha512-jRdZP+dbfc/GuuVIJdtsbh0TNmWODVzN3/cgV+tof2SjLMJNXIBjKNzbM6TaWbF69wWLYQeJfOcos6HKCCcPLg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBjtar7qX2CtneGt/NW5kijVV+HQ47G5nKNudmC70zVWAiEAnFzBcAkIIpM0rXpxYfi+8aS/b50XhlTc354GgolTaO0="}]}, "directories": {}}, "1.1.4": {"name": "rc", "version": "1.1.4", "description": "hardwired configuration loader", "main": "index.js", "browserify": "browser.js", "scripts": {"test": "set -e; node test/test.js; node test/ini.js; node test/nested-env-vars.js"}, "repository": {"type": "git", "url": "git+https://github.com/dominictarr/rc.git"}, "license": "(BSD-2-<PERSON><PERSON> OR MIT OR Apache-2.0)", "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./index.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "dependencies": {"deep-extend": "~0.4.0", "ini": "~1.3.0", "minimist": "~1.1.2", "strip-json-comments": "~1.0.4"}, "gitHead": "91a57bebf8095e56921e6768c89c249cbf61651a", "bugs": {"url": "https://github.com/dominictarr/rc/issues"}, "homepage": "https://github.com/dominictarr/rc#readme", "_id": "rc@1.1.4", "_shasum": "94c26969acf1b09b98c5b041a3e499c6923487f0", "_from": ".", "_npmVersion": "2.14.8", "_nodeVersion": "4.2.1", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "dist": {"shasum": "94c26969acf1b09b98c5b041a3e499c6923487f0", "tarball": "https://registry.npmjs.org/rc/-/rc-1.1.4.tgz", "integrity": "sha512-ghw4zG45jxvox4NyIVzIkgh2GH5QOp75ASpnxO5zuPMk12kLUruoX/JeV1SavT9w4HiZtbvHAeQiug4F9cDDRQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICwYwJXJEauQ4LiPWl7tAJOfsqUBdcf0m7h6b5BXEKWhAiEA2UPQUmoPz9Ix5cT2Im1JTckuS/oqmEeF2jUdHit8f00="}]}, "directories": {}}, "1.1.5": {"name": "rc", "version": "1.1.5", "description": "hardwired configuration loader", "main": "index.js", "browserify": "browser.js", "scripts": {"test": "set -e; node test/test.js; node test/ini.js; node test/nested-env-vars.js"}, "repository": {"type": "git", "url": "git+https://github.com/dominictarr/rc.git"}, "license": "(BSD-2-<PERSON><PERSON> OR MIT OR Apache-2.0)", "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./index.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "dependencies": {"deep-extend": "~0.4.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~1.0.4"}, "gitHead": "cc0cf524d5fb9fe9aa0fc95914e21db34fdbd34e", "bugs": {"url": "https://github.com/dominictarr/rc/issues"}, "homepage": "https://github.com/dominictarr/rc#readme", "_id": "rc@1.1.5", "_shasum": "3bae28d7bed87d1ccb5863f8dce8c27f2ceee89c", "_from": ".", "_npmVersion": "2.14.8", "_nodeVersion": "4.2.1", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "dist": {"shasum": "3bae28d7bed87d1ccb5863f8dce8c27f2ceee89c", "tarball": "https://registry.npmjs.org/rc/-/rc-1.1.5.tgz", "integrity": "sha512-AOxs3IqfKmtsN72+npXugfd1RcRIhoMGAVBjoTMBSgCHRVtadTW1WH4mlGQ7s3ZPURaVptpoGiqs+AwFreuhvg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDk/pr8R+DVLYrsP/uMTr1GcVMK/TGzTVvWzGM5eZQnmgIhAJQkgHMdTLbXUxcyKwjPGZ1LNS2c7q2Qtdy85773Bo12"}]}, "directories": {}}, "1.1.6": {"name": "rc", "version": "1.1.6", "description": "hardwired configuration loader", "main": "index.js", "browserify": "browser.js", "scripts": {"test": "set -e; node test/test.js; node test/ini.js; node test/nested-env-vars.js"}, "repository": {"type": "git", "url": "git+https://github.com/dominictarr/rc.git"}, "license": "(BSD-2-<PERSON><PERSON> OR MIT OR Apache-2.0)", "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./index.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "dependencies": {"deep-extend": "~0.4.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~1.0.4"}, "gitHead": "132062de0e61881a025cc4784d9a2798409c2bf1", "bugs": {"url": "https://github.com/dominictarr/rc/issues"}, "homepage": "https://github.com/dominictarr/rc#readme", "_id": "rc@1.1.6", "_shasum": "43651b76b6ae53b5c802f1151fa3fc3b059969c9", "_from": ".", "_npmVersion": "3.5.1", "_nodeVersion": "4.2.3", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "dist": {"shasum": "43651b76b6ae53b5c802f1151fa3fc3b059969c9", "tarball": "https://registry.npmjs.org/rc/-/rc-1.1.6.tgz", "integrity": "sha512-kKP2v/do9XlmKORXSHhVRnrsS0LVUhwfFhs/7fsLxQDYxuR+LA46Zivb3XBgomwRUwmf5W6QDqyQxQIhE850eA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG5joJmfajGDwUr+53S2Ps28HrvDaz5f9m7gT6vflRNpAiBGwdgd8foxPi4qLr3LLIa5E5r9bo+7dg7Z8jyoRDlBXQ=="}]}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "directories": {}}, "1.1.7": {"name": "rc", "version": "1.1.7", "description": "hardwired configuration loader", "main": "index.js", "browserify": "browser.js", "scripts": {"test": "set -e; node test/test.js; node test/ini.js; node test/nested-env-vars.js"}, "repository": {"type": "git", "url": "git+https://github.com/dominictarr/rc.git"}, "license": "(BSD-2-<PERSON><PERSON> OR MIT OR Apache-2.0)", "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./index.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "dependencies": {"deep-extend": "~0.4.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "gitHead": "13bca1296fa95cd7795d30681652b2b7499d85d3", "bugs": {"url": "https://github.com/dominictarr/rc/issues"}, "homepage": "https://github.com/dominictarr/rc#readme", "_id": "rc@1.1.7", "_shasum": "c5ea564bb07aff9fd3a5b32e906c1d3a65940fea", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.9.4", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "dist": {"shasum": "c5ea564bb07aff9fd3a5b32e906c1d3a65940fea", "tarball": "https://registry.npmjs.org/rc/-/rc-1.1.7.tgz", "integrity": "sha512-6O2HbJTSrysm7Fismxe1DVrEiJdN3DkbBH7SKbEqPnWhjoIG+giX5k9UezdopF86tdQTGwo5wKswMfWBX0wz5w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGjZ6oqsOlsOgp6FPscywXDlfvWdfOE0mSkFA+HN4derAiEAzNdpjzhThrPmCU/O7kNFqhJmvoeLpz4YPahoyEGWff0="}]}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/rc-1.1.7.tgz_1487322919345_0.9128507312852889"}, "directories": {}}, "1.2.0": {"name": "rc", "version": "1.2.0", "description": "hardwired configuration loader", "main": "index.js", "browserify": "browser.js", "scripts": {"test": "set -e; node test/test.js; node test/ini.js; node test/nested-env-vars.js"}, "repository": {"type": "git", "url": "git+https://github.com/dominictarr/rc.git"}, "license": "(BSD-2-<PERSON><PERSON> OR MIT OR Apache-2.0)", "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./index.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "dependencies": {"deep-extend": "~0.4.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "gitHead": "71d7d1524f7421c5357daac289961b49f039d19d", "bugs": {"url": "https://github.com/dominictarr/rc/issues"}, "homepage": "https://github.com/dominictarr/rc#readme", "_id": "rc@1.2.0", "_shasum": "c7de973b7b46297c041366b2fd3d2363b1697c66", "_from": ".", "_npmVersion": "4.0.5", "_nodeVersion": "7.4.0", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "dist": {"shasum": "c7de973b7b46297c041366b2fd3d2363b1697c66", "tarball": "https://registry.npmjs.org/rc/-/rc-1.2.0.tgz", "integrity": "sha512-pNtp0lTaeanPwd8KcRWvIN4laa0PZkdHn49JeC8wdNBibqnnAMUThZHPs3+Qdw8UdPE4lvzgF9CdMURaUhUtMQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCuL80tej6C2bjdlhr2C9XA8PU62ZhMMoFwI7YKFzQwlwIhALYH1TI4ccNwS1YdlwIW6/6n57sTI/JrzAS+gH5q+F5y"}]}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/rc-1.2.0.tgz_1490813267488_0.12326159630902112"}, "directories": {}}, "1.2.1": {"name": "rc", "version": "1.2.1", "description": "hardwired configuration loader", "main": "index.js", "browserify": "browser.js", "scripts": {"test": "set -e; node test/test.js; node test/ini.js; node test/nested-env-vars.js"}, "repository": {"type": "git", "url": "git+https://github.com/dominictarr/rc.git"}, "license": "(BSD-2-<PERSON><PERSON> OR MIT OR Apache-2.0)", "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./index.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "dependencies": {"deep-extend": "~0.4.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "gitHead": "41251ff2bdc6a067dd3bf77efcdad57cae23b515", "bugs": {"url": "https://github.com/dominictarr/rc/issues"}, "homepage": "https://github.com/dominictarr/rc#readme", "_id": "rc@1.2.1", "_shasum": "2e03e8e42ee450b8cb3dce65be1bf8974e1dfd95", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.9.4", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "dist": {"shasum": "2e03e8e42ee450b8cb3dce65be1bf8974e1dfd95", "tarball": "https://registry.npmjs.org/rc/-/rc-1.2.1.tgz", "integrity": "sha512-5kLVpOvFh6zdjGL2+UmCXd/nonPuxsRjM0LktPM6CtpFYOrZSd9rF4tveeMtql3HU6AsAovgqR8k9HQOSfXLMQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG7ENJucC9WUPz3U6G7MxYDIqZzUtQxr2cPsX4B0mn9iAiEAkHTIb48rER6kQAHk+jrtK7d70UnktQ2inVtkYjgD39s="}]}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/rc-1.2.1.tgz_1491263242441_0.15247246017679572"}, "directories": {}}, "1.2.2": {"name": "rc", "version": "1.2.2", "description": "hardwired configuration loader", "main": "index.js", "browser": "browser.js", "scripts": {"test": "set -e; node test/test.js; node test/ini.js; node test/nested-env-vars.js"}, "repository": {"type": "git", "url": "git+https://github.com/dominictarr/rc.git"}, "license": "(BSD-2-<PERSON><PERSON> OR MIT OR Apache-2.0)", "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./index.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "dependencies": {"deep-extend": "~0.4.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "gitHead": "acc920366eed02985fd16bf623004ef0a292107c", "bugs": {"url": "https://github.com/dominictarr/rc/issues"}, "homepage": "https://github.com/dominictarr/rc#readme", "_id": "rc@1.2.2", "_shasum": "d8ce9cb57e8d64d9c7badd9876c7c34cbe3c7077", "_from": ".", "_npmVersion": "4.6.1", "_nodeVersion": "6.5.0", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "dist": {"shasum": "d8ce9cb57e8d64d9c7badd9876c7c34cbe3c7077", "tarball": "https://registry.npmjs.org/rc/-/rc-1.2.2.tgz", "integrity": "sha512-hYYDK9dNqK5Zyd9GOfJU30cuSVXiTDxsOXvh/pUcDLxc4M1sY+T7xGfi17MjQmLz8VV4oTkVHEGrRvNVatiAsQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDaHPN4Ryemq3vJQc7Vr+hQtFV9SAw4DkfcB/eSmjLmhQIhAMF3jxo4oxjWigCe9Od04WMdTymoeYWNdM91nvPnt7yc"}]}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rc-1.2.2.tgz_1508011466129_0.3276489407289773"}, "directories": {}}, "1.2.3": {"name": "rc", "version": "1.2.3", "description": "hardwired configuration loader", "main": "index.js", "browser": "browser.js", "scripts": {"test": "set -e; node test/test.js; node test/ini.js; node test/nested-env-vars.js"}, "repository": {"type": "git", "url": "git+https://github.com/dominictarr/rc.git"}, "license": "(BSD-2-<PERSON><PERSON> OR MIT OR Apache-2.0)", "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./cli.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "dependencies": {"deep-extend": "~0.4.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "gitHead": "c5345c1fa05d9e65ceae5b1589c6a2b62a099b25", "bugs": {"url": "https://github.com/dominictarr/rc/issues"}, "homepage": "https://github.com/dominictarr/rc#readme", "_id": "rc@1.2.3", "_shasum": "51575a900f8dd68381c710b4712c2154c3e2035b", "_from": ".", "_npmVersion": "4.6.1", "_nodeVersion": "6.5.0", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "dist": {"shasum": "51575a900f8dd68381c710b4712c2154c3e2035b", "tarball": "https://registry.npmjs.org/rc/-/rc-1.2.3.tgz", "integrity": "sha512-bqnwRdnCh6NCDNwZhtlE/rD4ICtHgMs64TV5L7KbLmrRgx+6VRsQlLgL+iNUf1gDlieElSKdR5ak0EEcdt7zKA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAvrLlrgl6XBC+typA8vG81Z6GFCzBCvAABqJyARNl/MAiEA+P6GQsaTDOu5bW8kNeIEfaPFUmifWb0UM5Eaill9NYk="}]}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rc-1.2.3.tgz_1515449709808_0.885112258605659"}, "directories": {}}, "1.2.4": {"name": "rc", "version": "1.2.4", "description": "hardwired configuration loader", "main": "index.js", "browser": "browser.js", "scripts": {"test": "set -e; node test/test.js; node test/ini.js; node test/nested-env-vars.js"}, "repository": {"type": "git", "url": "git+https://github.com/dominictarr/rc.git"}, "license": "(BSD-2-<PERSON><PERSON> OR MIT OR Apache-2.0)", "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./cli.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "dependencies": {"deep-extend": "~0.4.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "gitHead": "383266e7b4c8bb506f04f6f885f4a86f9021770f", "bugs": {"url": "https://github.com/dominictarr/rc/issues"}, "homepage": "https://github.com/dominictarr/rc#readme", "_id": "rc@1.2.4", "_shasum": "a0f606caae2a3b862bbd0ef85482c0125b315fa3", "_from": ".", "_npmVersion": "4.6.1", "_nodeVersion": "6.5.0", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "dist": {"shasum": "a0f606caae2a3b862bbd0ef85482c0125b315fa3", "tarball": "https://registry.npmjs.org/rc/-/rc-1.2.4.tgz", "integrity": "sha512-G/5Zya+N+U0PghLlYBp14l8f8W77EsVRMPh2zIAa6e8ETSUnWwGky6UfxNvlSToEX8CcAkdy/ZRBoMQNfaCyVg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDgwK/ICJzR6RMP81bK13fMchpX9O2VRugn5g5GtdATFwIgCIE80kki2HilbhfMz3FaIp88ZHwULbjYmnChWGf//qI="}]}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rc-1.2.4.tgz_1516166155880_0.6010237459558994"}, "directories": {}}, "1.2.5": {"name": "rc", "version": "1.2.5", "description": "hardwired configuration loader", "main": "index.js", "browser": "browser.js", "scripts": {"test": "set -e; node test/test.js; node test/ini.js; node test/nested-env-vars.js"}, "repository": {"type": "git", "url": "git+https://github.com/dominictarr/rc.git"}, "license": "(BSD-2-<PERSON><PERSON> OR MIT OR Apache-2.0)", "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./cli.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "dependencies": {"deep-extend": "~0.4.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "gitHead": "5cac462702238a6f38fac2e0ba8e9407cd067d0e", "bugs": {"url": "https://github.com/dominictarr/rc/issues"}, "homepage": "https://github.com/dominictarr/rc#readme", "_id": "rc@1.2.5", "_shasum": "275cd687f6e3b36cc756baa26dfee80a790301fd", "_from": ".", "_npmVersion": "4.6.1", "_nodeVersion": "6.5.0", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "dist": {"shasum": "275cd687f6e3b36cc756baa26dfee80a790301fd", "tarball": "https://registry.npmjs.org/rc/-/rc-1.2.5.tgz", "integrity": "sha512-W9DW3KA9AMPhH/9ptNc+yy6BgL8xSAJINMdA8NRi3XKbb3FFUp/szBcNb4BdV3VAma8CinXnWxlrPjP5tEGBug==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCukCYJC6HG+swtuZM+pViCYLYBCVNDOE/8a61KAfVpKwIgUU6hzjPH3vjQc5pi+OhCXYvpZR9y/usg4urN+NlKpWc="}]}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rc-1.2.5.tgz_1517361001588_0.7331709044519812"}, "directories": {}}, "1.2.6": {"name": "rc", "version": "1.2.6", "description": "hardwired configuration loader", "main": "index.js", "browser": "browser.js", "scripts": {"test": "set -e; node test/test.js; node test/ini.js; node test/nested-env-vars.js"}, "repository": {"type": "git", "url": "git+https://github.com/dominictarr/rc.git"}, "license": "(BSD-2-<PERSON><PERSON> OR MIT OR Apache-2.0)", "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./cli.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "dependencies": {"deep-extend": "~0.4.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "gitHead": "4c5afda643b52caac3d6385aab0c8b6740b2b128", "bugs": {"url": "https://github.com/dominictarr/rc/issues"}, "homepage": "https://github.com/dominictarr/rc#readme", "_id": "rc@1.2.6", "_shasum": "eb18989c6d4f4f162c399f79ddd29f3835568092", "_from": ".", "_npmVersion": "4.6.1", "_nodeVersion": "6.5.0", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "dist": {"shasum": "eb18989c6d4f4f162c399f79ddd29f3835568092", "tarball": "https://registry.npmjs.org/rc/-/rc-1.2.6.tgz", "fileCount": 13, "unpackedSize": 17297, "integrity": "sha512-KS1nzya3gqWi8rPtBXza+gi9QtvAKx6aD8QywD03WDRMGPAZkQdNc8ZBoYpfrJxOvqkZxjRDAp0XIrxNBAbDUg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCneUsHw8j3HU6y8jB70HpVUoXHUHNKCtfpb68zW++htQIhAKh3+7QJhhvshToKm9Da1VrHQYvaz7yG4/z6d7kQPEsy"}]}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rc_1.2.6_1520915935841_0.6436299953957936"}, "_hasShrinkwrap": false}, "1.2.7": {"name": "rc", "version": "1.2.7", "description": "hardwired configuration loader", "main": "index.js", "browser": "browser.js", "scripts": {"test": "set -e; node test/test.js; node test/ini.js; node test/nested-env-vars.js"}, "repository": {"type": "git", "url": "git+https://github.com/dominictarr/rc.git"}, "license": "(BSD-2-<PERSON><PERSON> OR MIT OR Apache-2.0)", "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./cli.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "dependencies": {"deep-extend": "^0.5.1", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "gitHead": "9d21a3f5a1e173113b6ef9c85a37859613a67f75", "bugs": {"url": "https://github.com/dominictarr/rc/issues"}, "homepage": "https://github.com/dominictarr/rc#readme", "_id": "rc@1.2.7", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.1", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-LdLD8xD4zzLsAT5xyushXDNscEjB7+2ulnl8+r1pnESlYtlJtVSoCMBGr30eDRJ3+2Gq89jK9P9e4tCEH1+ywA==", "shasum": "8a10ca30d588d00464360372b890d06dacd02297", "tarball": "https://registry.npmjs.org/rc/-/rc-1.2.7.tgz", "fileCount": 12, "unpackedSize": 17255, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa5gVbCRA9TVsSAnZWagAAr+0P/ivC/FUx9+VuHBjBYX1H\nyyqY/9QFAV7FXG2cXePfGUk2HktIR7nRL6wvXzsR+ox7KRK+dBv3GVBD3DDm\nYQeezOAfdhEcLc7cgkzHSXcMX9W//TZbaVAULiTzKU5GpTjGfMZ5IKYGeKNX\nhYFcfcmzpUOlfy+xVV5MGxq2j0pbTCEUuS8eHahf8QPliNjeed0k+BkohR2x\nd1qcjpuMg41s4a7F6GZuSkH1HdAkj0EzXk5CyLZNyPudk79pvmmuvVbnvCaj\nXuGBFmDJY1hR+7G4A2952+yCSGgBWTdPkwKJ2e/AzSQpE8us0UQkBYX3JCXA\nsrTc6Os6Wa1qb5QyFRxGEFEjWPdrlfIRN6t8UQts5qjnWuf56BsgU8GxOxvd\nOwdacLV1Se7w4B/N2qMQrr7hkPewMcLavkbgeaATbiC83VGNPK4bJ4u9jJzX\nMu1MTsyi4OEfS1aoBcRIfZVrJ7mWMJqxiDeIF+gaNeOZghK/1bhcDmRwmb0t\nuG8Ur51teXSHVpYyNRbobhkheLlEjgw6gkPP/KNq8QO09HTvxEvHfSwzTQtn\nZ3zb7rsgtrxcvkR2ti7wK6GeMImyXkVxLVP1V7TfVdVN0cmHysd/Zcn0Z8ls\naiQSHetojcvVxndLq/rsOkwEQsrDj1Ss2ix7mR0moFMN+mllCJnl4DqMGPPz\n42hs\r\n=NcNs\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEOnzf/52b1kBg5nu0vcNz7xsRH5o1hzYQg+UWKwEa99AiAj45aIWATLuNZ1xnPJ9BGWX5N8JH20DWBpy/3FjPl2AA=="}]}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rc_1.2.7_1525024090089_0.3599692046997329"}, "_hasShrinkwrap": false}, "1.2.8": {"name": "rc", "version": "1.2.8", "description": "hardwired configuration loader", "main": "index.js", "browser": "browser.js", "scripts": {"test": "set -e; node test/test.js; node test/ini.js; node test/nested-env-vars.js"}, "repository": {"type": "git", "url": "git+https://github.com/dominictarr/rc.git"}, "license": "(BSD-2-<PERSON><PERSON> OR MIT OR Apache-2.0)", "keywords": ["config", "rc", "unix", "defaults"], "bin": {"rc": "./cli.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "dependencies": {"deep-extend": "^0.6.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "gitHead": "a97f6adcc37ee1cad06ab7dc9b0bd842bbc5c664", "bugs": {"url": "https://github.com/dominictarr/rc/issues"}, "homepage": "https://github.com/dominictarr/rc#readme", "_id": "rc@1.2.8", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.1", "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-y3bGgqKj3QBdxLbLkomlohkvsA8gdAiUQlSBJnBhfn+BPxg4bc62d8TcBW15wavDfgexCgccckhcZvywyQYPOw==", "shasum": "cd924bf5200a075b83c188cd6b9e211b7fc0d3ed", "tarball": "https://registry.npmjs.org/rc/-/rc-1.2.8.tgz", "fileCount": 12, "unpackedSize": 17255, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh24AeCRA9TVsSAnZWagAAcVgP/R8fpD8bpNqLHxby9KcJ\n24wpMlSlljJ4oskqfmrYAtwVQR8TWMirdti902r1Ivu881pdapvPas5AF/r3\n0MLwOIL1oq32rv+yadKHjfMCy1ww5s1G2PvnsbFiwbzdADuQURuUdLCFLP92\ngjE64X6yJc0t6KncjHU9NMgu3im4Gk6WFMWnfv50Lv0UwF+2Lz1I8nlP/nrv\nO5dwcqQ8rRqLcniPIntLKqj+E9tlvteRu2yWu53rdbPdadjlaxXp22zuB8I+\nPVzfLk/FpSWyCpyDnuLJUl84lwdL8AWf1czIDGss7BRe+DYhGGyFF24wQZY3\nkLJxvd2ow/po4xBjtk0uhY0/cysWLpjJEzsCfg6osArth3CC8YrYUSEgbDEq\nn/fS3wdvsoJHbZPO4CyPK1CbG8oDeVzO2ypxhXt2LGcQj7jfjBw7K8ilPCdk\nnQJh2zSQGgS8UdlIyVExP7k9J0m/oSeTOy33XFywRKkZXChHGH3M14Z9aGLC\nAaXQoKwy4uPXeRsoHeDDkIRZk3Vi/oA4i2HZrZSvk6GNHaADoPwnaD2eNx6+\n0dYp9L97n5GpbPcHkc+lyrt60nmtbc/1+05rpwIJ9bo3ItEk0j/7mjXrop5/\nxKaWvoaZ0dnAYjRjdp9UY6G+xuh4wZxWX2sPuoVmuuxcn5bJvvXCuW7ZvMbf\n0o+x\r\n=GiCU\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDjXOqF4nHQMFsn7LQD1oAl4r/OLm4gGUiQyi2+Ott9AgIgcVJllTHUjZLvK0THpAorSQ8UmYrkDTd0i5qwnjYed0A="}]}, "maintainers": [{"name": "dominictarr", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/rc_1.2.8_1527378233915_0.6821654736416851"}, "_hasShrinkwrap": false}}, "readme": "", "maintainers": [{"email": "<EMAIL>", "name": "powersource"}, {"email": "<EMAIL>", "name": "arj03"}, {"email": "<EMAIL>", "name": "staltz"}, {"email": "<EMAIL>", "name": "mixmix"}, {"email": "<EMAIL>", "name": "cel"}, {"email": "<EMAIL>", "name": "christian<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "p<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "happy0"}, {"email": "<EMAIL>", "name": "chere<PERSON><PERSON><PERSON>"}], "time": {"modified": "2023-07-21T15:51:42.677Z", "created": "2012-08-05T13:49:44.698Z", "0.0.1": "2012-08-05T13:49:46.664Z", "0.0.2": "2012-08-05T15:24:51.295Z", "0.0.3": "2012-08-05T19:28:30.864Z", "0.0.4": "2012-08-14T17:03:21.021Z", "0.0.5": "2012-08-21T16:49:51.668Z", "0.0.6": "2012-11-01T11:19:27.846Z", "0.0.7": "2013-01-24T15:50:09.170Z", "0.0.8": "2013-02-06T08:33:14.173Z", "0.1.0": "2013-03-30T07:25:35.949Z", "0.1.1": "2013-04-08T20:35:11.790Z", "0.1.2": "2013-04-27T14:36:59.537Z", "0.1.3": "2013-04-27T14:37:32.512Z", "0.2.0": "2013-05-19T13:27:39.498Z", "0.3.0": "2013-05-21T14:30:57.888Z", "0.3.1": "2013-08-29T21:11:11.467Z", "0.3.2": "2013-11-26T05:43:04.283Z", "0.3.3": "2014-01-16T09:55:29.302Z", "0.3.4": "2014-02-21T00:03:58.553Z", "0.3.5": "2014-04-17T00:36:24.773Z", "0.4.0": "2014-05-24T13:56:43.890Z", "0.5.0": "2014-07-30T01:41:38.772Z", "0.5.1": "2014-08-31T18:27:32.857Z", "0.5.2": "2014-10-27T17:00:57.210Z", "0.5.3": "2014-11-01T13:23:24.349Z", "0.5.4": "2014-11-03T16:45:20.714Z", "0.5.5": "2015-01-04T07:07:47.844Z", "0.6.0": "2015-02-02T21:26:55.754Z", "1.0.0": "2015-03-19T03:37:02.013Z", "1.0.1": "2015-03-28T22:10:10.832Z", "1.0.2": "2015-05-21T08:00:16.737Z", "1.0.3": "2015-05-21T12:58:13.246Z", "1.1.0": "2015-07-25T20:11:29.561Z", "1.1.1": "2015-08-30T20:31:59.631Z", "1.1.2": "2015-10-02T02:10:26.777Z", "1.1.3": "2015-11-05T07:40:20.884Z", "1.1.4": "2015-11-05T07:41:16.123Z", "1.1.5": "2015-11-05T19:01:45.285Z", "1.1.6": "2015-12-29T07:12:02.074Z", "1.1.7": "2017-02-17T09:15:19.960Z", "1.2.0": "2017-03-29T18:47:47.771Z", "1.2.1": "2017-04-03T23:47:22.735Z", "1.2.2": "2017-10-14T20:04:26.233Z", "1.2.3": "2018-01-08T22:15:09.940Z", "1.2.4": "2018-01-17T05:15:56.187Z", "1.2.5": "2018-01-31T01:10:01.725Z", "1.2.6": "2018-03-13T04:38:55.888Z", "1.2.7": "2018-04-29T17:48:10.164Z", "1.2.8": "2018-05-26T23:43:53.976Z", "1.2.9": "2021-11-04T15:30:19.438Z", "1.3.9": "2021-11-04T15:30:34.911Z", "2.3.9": "2021-11-04T15:30:47.021Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com"}, "repository": {"type": "git", "url": "git+https://github.com/dominictarr/rc.git"}, "users": {"chrisdickinson": true, "strathausen": true, "vkadam": true, "dickeylth": true, "dramors": true, "logankoester": true, "wenbing": true, "mattmcmanus": true, "larixk": true, "yaniv": true, "jprempeh": true, "tyandell": true, "alanshaw": true, "akiva": true, "haeck": true, "phette23": true, "nayrangnu": true, "jesusgoku": true, "gavinengel": true, "joshwyatt": true, "kwpeters": true, "gztomas": true, "bret": true, "brandonb927": true, "ubi": true, "pdedkov": true, "bagocius": true, "detj": true, "ugarz": true, "nickleefly": true, "alexkval": true, "pos": true, "sidwood": true, "bojand": true, "pstoev": true, "amio": true, "zhanghaili": true, "jensnilsson": true, "shujianbu": true, "markthethomas": true, "rochejul": true, "biasso": true, "lordvlad": true, "winniehell": true, "preco21": true, "shannonmoeller": true, "jerrywu": true, "shanewholloway": true, "about_hiroppy": true, "itsananderson": true, "gurunate": true, "jetze": true, "ragingsmurf": true, "coolhanddev": true, "mhaidarh": true, "kistoryg": true, "prometheas": true, "joaquin.briceno": true, "cmtegner": true, "giussa_dan": true, "aemonge": true, "abernier": true, "quafoo": true, "joshuagross": true, "heartnett": true, "psychollama": true, "kulyk404": true, "keenwon": true, "xinwangwang": true, "arniu": true, "juampynr": true, "landen": true, "xieranmaya": true, "seangenabe": true, "hektve87": true, "losymear": true, "daizch": true, "flumpus-dev": true}, "readmeFilename": "", "homepage": "https://github.com/dominictarr/rc#readme", "keywords": ["config", "rc", "unix", "defaults"], "bugs": {"url": "https://github.com/dominictarr/rc/issues"}, "license": "(BSD-2-<PERSON><PERSON> OR MIT OR Apache-2.0)"}