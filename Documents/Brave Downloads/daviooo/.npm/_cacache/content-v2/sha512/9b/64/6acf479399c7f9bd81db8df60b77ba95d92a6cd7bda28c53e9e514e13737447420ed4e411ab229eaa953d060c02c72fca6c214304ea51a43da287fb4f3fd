{"_id": "complex.js", "_rev": "51-a757d01abec663018e768c6a04170371", "name": "complex.js", "dist-tags": {"latest": "2.4.2"}, "versions": {"1.0.0": {"name": "complex.js", "version": "1.0.0", "keywords": ["math", "complex", "number", "calculus"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "complex.js@1.0.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://github.com/infusion/Complex.js", "bugs": {"url": "https://github.com/infusion/Complex.js/issues"}, "dist": {"shasum": "d2a9476f47589fef5a2c657a9de9c34cd141bcbb", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-1.0.0.tgz", "integrity": "sha512-5IB8PML2z/LSH9BDh7GglCsmE6G9I1IYclkWMhYjEHXCqt+LSQHdzZoqEa2YNH+Qt3b9y3oyou2ou2wFjrGHNg==", "signatures": [{"sig": "MEUCIQC52awveMD2eqPq/xXnNaLBk49gwIiVGgpzcEefWnV4jQIgJXN5P/95WnNGSDAjF+WxXEqZMK+PAcNmigFX8X/AymQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "complex", "_from": ".", "title": "complex.js", "_shasum": "d2a9476f47589fef5a2c657a9de9c34cd141bcbb", "engines": {"node": "*"}, "gitHead": "e8ebe2fbbd3f2854417ef325246ca7c148e08699", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/MIT", "type": "MIT"}, {"url": "http://www.opensource.org/licenses/GPL-2.0", "type": "GPL"}], "repository": {"url": "git://github.com/infusion/Complex.js.git", "type": "git"}, "_npmVersion": "2.11.3", "description": "A complex number library", "directories": {}, "_nodeVersion": "0.12.4", "devDependencies": {"mocha": "*"}}, "1.1.0": {"name": "complex.js", "version": "1.1.0", "keywords": ["math", "complex", "number", "calculus"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "complex.js@1.1.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://github.com/infusion/Complex.js", "bugs": {"url": "https://github.com/infusion/Complex.js/issues"}, "dist": {"shasum": "bd3067d3cc4ed538894fd51152df9d493dbdf672", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-1.1.0.tgz", "integrity": "sha512-mEgJQiWiryq32jTFn5fVejCD2Gwr4NaoJj++48J/6HsC11kEsEJEAd6iO9Rsk7G7VqkKagM3Jh/e3nxDxtP7IA==", "signatures": [{"sig": "MEQCIF7NNloMt/AzX930O5aHAxzkMu6gmR0zBuKR4MugXRLeAiANgfEaWj1shipkdbLdpPraLef1JOY9jnZy7Y5EBvhw8w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "complex", "_from": ".", "title": "complex.js", "_shasum": "bd3067d3cc4ed538894fd51152df9d493dbdf672", "engines": {"node": "*"}, "gitHead": "3ab13dcd2976e08667c6b52e9a3fd46517a9bc6e", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/MIT", "type": "MIT"}, {"url": "http://www.opensource.org/licenses/GPL-2.0", "type": "GPL"}], "repository": {"url": "git://github.com/infusion/Complex.js.git", "type": "git"}, "_npmVersion": "2.11.3", "description": "A complex number library", "directories": {}, "_nodeVersion": "0.12.4", "devDependencies": {"mocha": "*"}}, "1.2.0": {"name": "complex.js", "version": "1.2.0", "keywords": ["math", "complex", "number", "calculus"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "complex.js@1.2.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://github.com/infusion/Complex.js", "bugs": {"url": "https://github.com/infusion/Complex.js/issues"}, "dist": {"shasum": "267dd7f8070fc4406d7f9f2579a1a73747f4bd2e", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-1.2.0.tgz", "integrity": "sha512-9yB9suMHADRyUnf5CCP25RhAJ29/brzSow8lDv5TSqlXp9t/fKTaUyDNn5Y5LNyeeRcmtpGZPzZH+jNloE/iAg==", "signatures": [{"sig": "MEUCIBJXp7wixCzMDLdRcurkWWNtTWnJEGX9G7an98nIBEIYAiEAvXURBUCUTvg1QJS7cRCP0NfCTXPLZYMKUbEeJVDPby0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "complex", "_from": ".", "title": "complex.js", "_shasum": "267dd7f8070fc4406d7f9f2579a1a73747f4bd2e", "engines": {"node": "*"}, "gitHead": "0aa860c8a6dfd969174e7af55ae4641c00a4af83", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/MIT", "type": "MIT"}, {"url": "http://www.opensource.org/licenses/GPL-2.0", "type": "GPL"}], "repository": {"url": "git://github.com/infusion/Complex.js.git", "type": "git"}, "_npmVersion": "2.12.0", "description": "A complex number library", "directories": {}, "_nodeVersion": "0.12.5", "devDependencies": {"mocha": "*"}}, "1.2.2": {"name": "complex.js", "version": "1.2.2", "keywords": ["math", "complex", "number", "calculus"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "complex.js@1.2.2", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://github.com/infusion/Complex.js", "bugs": {"url": "https://github.com/infusion/Complex.js/issues"}, "dist": {"shasum": "1c5defd9b1257f2e785f911e66248f1e639034e4", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-1.2.2.tgz", "integrity": "sha512-4Bjl+29vySBn/2rRV02mcAZjMRtmqyPTpREQgCpPtrTIxhWo6oWA9JFdxqNsqHqqwUtkq1/GAE39kIjRlTCamg==", "signatures": [{"sig": "MEQCIDLJAZydm1Il4Isyr5ytOkgP1vaWHVne6K+9WmfnwfpPAiBA6JPMmJtOeCZ7oLkhk0uM+3eQjwGb4BxgfRfPrGyVow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "complex", "_from": ".", "title": "complex.js", "_shasum": "1c5defd9b1257f2e785f911e66248f1e639034e4", "engines": {"node": "*"}, "gitHead": "4869d5c5a1ac46c8862cc5a09bd220fe855f6e6d", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/MIT", "type": "MIT"}, {"url": "http://www.opensource.org/licenses/GPL-2.0", "type": "GPL"}], "repository": {"url": "git://github.com/infusion/Complex.js.git", "type": "git"}, "_npmVersion": "2.12.0", "description": "A complex number library", "directories": {}, "_nodeVersion": "0.12.5", "devDependencies": {"mocha": "*"}}, "1.3.0": {"name": "complex.js", "version": "1.3.0", "keywords": ["math", "complex", "number", "calculus"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "complex.js@1.3.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://github.com/infusion/Complex.js", "bugs": {"url": "https://github.com/infusion/Complex.js/issues"}, "dist": {"shasum": "397b51c97e1a80e739aeafbc9c4476859a2d5084", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-1.3.0.tgz", "integrity": "sha512-zb0J8xspt0B9io5Gq7sYoyZwkT5i4GgF6hXqUextVYu2nN7Zl6MUht4cVm4OHwpTg6vz7dZ0xhRRoYqTrlxSbw==", "signatures": [{"sig": "MEUCIEssja4VsDAgUrfUOVXvDGZZ0/V9FQk7AncMVhEWUTuQAiEA1h0jwpOvE44lEqCy6xx9AquAW0yCixDsj13UEZllsOs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "complex", "_from": ".", "title": "complex.js", "_shasum": "397b51c97e1a80e739aeafbc9c4476859a2d5084", "engines": {"node": "*"}, "gitHead": "4869d5c5a1ac46c8862cc5a09bd220fe855f6e6d", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/MIT", "type": "MIT"}, {"url": "http://www.opensource.org/licenses/GPL-2.0", "type": "GPL"}], "repository": {"url": "git://github.com/infusion/Complex.js.git", "type": "git"}, "_npmVersion": "2.12.0", "description": "A complex number library", "directories": {}, "_nodeVersion": "0.12.5", "devDependencies": {"mocha": "*"}}, "1.4.0": {"name": "complex.js", "version": "1.4.0", "keywords": ["math", "complex", "number", "calculus"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "complex.js@1.4.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://github.com/infusion/Complex.js", "bugs": {"url": "https://github.com/infusion/Complex.js/issues"}, "dist": {"shasum": "8e8175df2404692bce7857ba2cfb905f4cc4889c", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-1.4.0.tgz", "integrity": "sha512-t9gbFj5Pp/wSwOBRLy1agAn1QVDywMwz6+V8UGYkgspZSjJZS5e8hSv8dImv5vKiXzD5NjrjZJbuUDdOUQLgVA==", "signatures": [{"sig": "MEQCIBKTduVUiKn1kjsgWPcFYJJBTTSiPrxqSyT1hCDe0c46AiAv8UzX4BwLxfQkLtMWW8spRWpDZYQ9DjzZDvrPnrZXEw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "complex", "_from": ".", "title": "complex.js", "_shasum": "8e8175df2404692bce7857ba2cfb905f4cc4889c", "engines": {"node": "*"}, "gitHead": "b20d3eb5c686579b334df691b931d1fe7d291c99", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/MIT", "type": "MIT"}, {"url": "http://www.opensource.org/licenses/GPL-2.0", "type": "GPL"}], "repository": {"url": "git://github.com/infusion/Complex.js.git", "type": "git"}, "_npmVersion": "2.12.0", "description": "A complex number library", "directories": {}, "_nodeVersion": "0.12.5", "devDependencies": {"mocha": "*"}}, "1.5.0": {"name": "complex.js", "version": "1.5.0", "keywords": ["math", "complex", "number", "calculus"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "complex.js@1.5.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://github.com/infusion/Complex.js", "bugs": {"url": "https://github.com/infusion/Complex.js/issues"}, "dist": {"shasum": "2053a78b0d3cdf773acb1c62b3c01310a1d8c6e1", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-1.5.0.tgz", "integrity": "sha512-n9QUUBwY+Al4/SFD3f+Orz+jWSm3knN1ALGkn/XuR5aL7cpnfAumvi0IspPl0iU/Es6gTakJB9geTeGB3QB/Dg==", "signatures": [{"sig": "MEQCIGVTyDkzDx83f24DtGNnpNxhITMnVMwaqWJqQlNs89Q9AiA1y2To9rWB8HhOdS0jcNmJgVCNMfmxlZTM5kT0Zj3eqQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "complex", "_from": ".", "title": "complex.js", "_shasum": "2053a78b0d3cdf773acb1c62b3c01310a1d8c6e1", "engines": {"node": "*"}, "gitHead": "568caff1379d411d0c1c675250064327fed900c0", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/MIT", "type": "MIT"}, {"url": "http://www.opensource.org/licenses/GPL-2.0", "type": "GPL"}], "repository": {"url": "git://github.com/infusion/Complex.js.git", "type": "git"}, "_npmVersion": "2.13.0", "description": "A complex number library", "directories": {}, "_nodeVersion": "0.12.7", "devDependencies": {"mocha": "*"}}, "1.6.0": {"name": "complex.js", "version": "1.6.0", "keywords": ["math", "complex", "number", "calculus", "parser"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "complex.js@1.6.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://github.com/infusion/Complex.js", "bugs": {"url": "https://github.com/infusion/Complex.js/issues"}, "dist": {"shasum": "4f08ca16f42ef4419a045ca8dacbc5fc9e6db111", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-1.6.0.tgz", "integrity": "sha512-8fp2pmohOvRWEs5kJw0mFvtJM/69EDgsgJhEwsFgk374jGREdbQ24c1ymYvm7Imirzcae59bRggbasouj/Z9Uw==", "signatures": [{"sig": "MEUCIBEJIJuyNTxxegax+tP5TTDLQPXe3e65Wwy6UOEeGxFHAiEAuV24DXCW/a83xhBkhbmu0x2SRuV0XfFCmv8xJAbgzVw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "complex", "_from": ".", "title": "complex.js", "_shasum": "4f08ca16f42ef4419a045ca8dacbc5fc9e6db111", "engines": {"node": "*"}, "gitHead": "1a09e5f66bcec5538395576178fad55261fa0bf1", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/MIT", "type": "MIT"}, {"url": "http://www.opensource.org/licenses/GPL-2.0", "type": "GPL"}], "repository": {"url": "git://github.com/infusion/Complex.js.git", "type": "git"}, "_npmVersion": "3.5.3", "description": "A complex number library", "directories": {}, "_nodeVersion": "5.5.0", "devDependencies": {"mocha": "*"}}, "1.7.0": {"name": "complex.js", "version": "1.7.0", "keywords": ["math", "complex", "number", "calculus", "parser"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0", "_id": "complex.js@1.7.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://github.com/infusion/Complex.js", "bugs": {"url": "https://github.com/infusion/Complex.js/issues"}, "dist": {"shasum": "dbe185e55bc8cd7a0b2f98c436a055c0eab6fb9f", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-1.7.0.tgz", "integrity": "sha512-YnZOe+dpNfxp2Ud3rN1wUMUwPbWW7G1ExFG9gVqwUTNFBWYr6YTaEIYuKlU+IFSqKpJCRQjshT9xJBbe5+RqBw==", "signatures": [{"sig": "MEUCIAo7uFGYcmXAxmSVrwpFHuWbAw46Dne1h29n/lDhkTCWAiEAsA+DsWljQemLdrpwuoCX4gXBX6Ky+JmWkpnc2n04mwI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "complex", "_from": ".", "title": "complex.js", "_shasum": "dbe185e55bc8cd7a0b2f98c436a055c0eab6fb9f", "engines": {"node": "*"}, "gitHead": "11637bb6794c387f838ae9b669edbf58c9f2dbe8", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Complex.js.git", "type": "git"}, "_npmVersion": "3.6.0", "description": "A complex number library", "directories": {}, "_nodeVersion": "5.5.0", "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/complex.js-1.7.0.tgz_1454517753478_0.17230653716251254", "host": "packages-6-west.internal.npmjs.com"}}, "1.8.0": {"name": "complex.js", "version": "1.8.0", "keywords": ["math", "complex", "number", "calculus", "parser"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0", "_id": "complex.js@1.8.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://github.com/infusion/Complex.js", "bugs": {"url": "https://github.com/infusion/Complex.js/issues"}, "dist": {"shasum": "825258fce554156b217e03e4d5a5de62242ba43d", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-1.8.0.tgz", "integrity": "sha512-TxDwKwbc01vZBBigQLPFNUDk4NCgl//I6WfrpWV7BP4leOO1RQO5aEw9cj0Fd/O5BC7UwM3H+ALMwMiIUXAgcw==", "signatures": [{"sig": "MEYCIQCwmhT40DqrDoXDTYO+6B7mGALR9gzhBG5CqGYB26JWegIhAJm4hpN3w7MuWb1iFfWP7KSbope2xZDR9KQ8eVnrblTn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "complex", "_from": ".", "title": "complex.js", "_shasum": "825258fce554156b217e03e4d5a5de62242ba43d", "engines": {"node": "*"}, "gitHead": "bc4c18af5c7452910a244e865086c8f968ca60c4", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Complex.js.git", "type": "git"}, "_npmVersion": "3.6.0", "description": "A complex number library", "directories": {}, "_nodeVersion": "5.5.0", "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/complex.js-1.8.0.tgz_1454519694309_0.6761363251134753", "host": "packages-9-west.internal.npmjs.com"}}, "1.8.2": {"name": "complex.js", "version": "1.8.2", "keywords": ["math", "complex", "number", "calculus", "parser"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0", "_id": "complex.js@1.8.2", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://github.com/infusion/Complex.js", "bugs": {"url": "https://github.com/infusion/Complex.js/issues"}, "dist": {"shasum": "da1563bec91ea221ad9aa938969fa70f28f6debb", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-1.8.2.tgz", "integrity": "sha512-cxhsn25l3odKJ7WOTH2LFlRLEuQOLinrvGO+cAjWHWXY2EB0LRpJc80+aO12fNiN315oDeL7TNmpuj4/mp9flg==", "signatures": [{"sig": "MEUCIQDqNnfklbp7RxPC8mOo3zzS/iAcXXgWk8IP5QHlsN8CcgIgPcy9gECbfTCBJ1pdc75GUN82OCcCVIOqTD5g4emdM7I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "complex", "_from": ".", "title": "complex.js", "_shasum": "da1563bec91ea221ad9aa938969fa70f28f6debb", "engines": {"node": "*"}, "gitHead": "908329629dfd7ec459700de4680dc89c2ceb69e7", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Complex.js.git", "type": "git"}, "_npmVersion": "3.6.0", "description": "A complex number library", "directories": {}, "_nodeVersion": "5.5.0", "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/complex.js-1.8.2.tgz_1454618366516_0.03404287062585354", "host": "packages-6-west.internal.npmjs.com"}}, "1.8.5": {"name": "complex.js", "version": "1.8.5", "keywords": ["math", "complex", "number", "calculus", "parser"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0", "_id": "complex.js@1.8.5", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://github.com/infusion/Complex.js", "bugs": {"url": "https://github.com/infusion/Complex.js/issues"}, "dist": {"shasum": "8700329bda921baeaca3bdb29a80ce2918e709ea", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-1.8.5.tgz", "integrity": "sha512-1uz9faA8GUa9dQk8/niYwyOArEyiC6S6hYJc85XoXGIleoTKm/l8gTDHk5JxbDCTucWIa5sxTYABGlX6ulL37w==", "signatures": [{"sig": "MEYCIQDENOQtokO/OYMFxYjgqW/HRsl3S/i7f14rPMWg94imkwIhAK0QRpxv4Nyy+xbeuA8ZGyQozKsnOhUKYAWadaFX5Ud/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "complex", "_from": ".", "title": "complex.js", "_shasum": "8700329bda921baeaca3bdb29a80ce2918e709ea", "engines": {"node": "*"}, "gitHead": "e22382d76bfca4e33a4feb503f17e0665b78ec10", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Complex.js.git", "type": "git"}, "_npmVersion": "3.6.0", "description": "A complex number library", "directories": {}, "_nodeVersion": "5.5.0", "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/complex.js-1.8.5.tgz_1454629874527_0.7951013299170882", "host": "packages-9-west.internal.npmjs.com"}}, "1.9.0": {"name": "complex.js", "version": "1.9.0", "keywords": ["math", "complex", "number", "calculus", "parser"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0", "_id": "complex.js@1.9.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://github.com/infusion/Complex.js", "bugs": {"url": "https://github.com/infusion/Complex.js/issues"}, "dist": {"shasum": "3f6d7e0ad5b458fad3ccfc732961b34c91e2ba4c", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-1.9.0.tgz", "integrity": "sha512-1FlF6qOhwpFBoNPTJJAZYjpIp9kGAgsvbToSQJ5HAD9Kb/FcioxNijzQWnCxFMuaceAjjxWAgGiiRjZD6M0t+Q==", "signatures": [{"sig": "MEUCIHPYSV7CpHy7oHYtPmwPeLS35/vcusohzrxVgCSBxNJTAiEAoyS+FEA31UBJDh8Vi+3tkh5L/atsu0TI48C0AxAJZWU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "complex", "_from": ".", "title": "complex.js", "_shasum": "3f6d7e0ad5b458fad3ccfc732961b34c91e2ba4c", "engines": {"node": "*"}, "gitHead": "986ed0d9c7f85c747b6cd2b572feba71de26f73d", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Complex.js.git", "type": "git"}, "_npmVersion": "3.7.1", "description": "A complex number library", "directories": {}, "_nodeVersion": "5.5.0", "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/complex.js-1.9.0.tgz_1454719209526_0.8941158074885607", "host": "packages-6-west.internal.npmjs.com"}}, "1.9.1": {"name": "complex.js", "version": "1.9.1", "keywords": ["math", "complex", "number", "calculus", "parser"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0", "_id": "complex.js@1.9.1", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://github.com/infusion/Complex.js", "bugs": {"url": "https://github.com/infusion/Complex.js/issues"}, "dist": {"shasum": "88916274e5dbfb0b78b6ca090b659df3395e1a1a", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-1.9.1.tgz", "integrity": "sha512-5mc1Lgu5cRbFeQxsctcsBX6VOyj2Cuk2ROV0XS5iQaHa7nvLEPtY9hP12t8MJTohwbDhMr1PevYUhV78gITEBA==", "signatures": [{"sig": "MEUCIBy5cZk9L1iX3GDhPDp/nIhqDUnSSdCcwHYfcg1Q6pLgAiEA0LvKmAiCiKdcdcAWFmW3mjN2HpH7/BbrNl05jXj9ZMA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "complex", "_from": ".", "title": "complex.js", "_shasum": "88916274e5dbfb0b78b6ca090b659df3395e1a1a", "engines": {"node": "*"}, "gitHead": "fe0afe6d0041767cc6a9af63d0a9a9ce021bb80c", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Complex.js.git", "type": "git"}, "_npmVersion": "3.7.1", "description": "A complex number library", "directories": {}, "_nodeVersion": "5.5.0", "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/complex.js-1.9.1.tgz_1454805248412_0.7292434240225703", "host": "packages-9-west.internal.npmjs.com"}}, "1.9.2": {"name": "complex.js", "version": "1.9.2", "keywords": ["math", "complex", "number", "calculus", "parser"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0", "_id": "complex.js@1.9.2", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://github.com/infusion/Complex.js", "bugs": {"url": "https://github.com/infusion/Complex.js/issues"}, "dist": {"shasum": "7a18a5a355c20a0d5a7176352cee9a9ad07f525d", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-1.9.2.tgz", "integrity": "sha512-CiV/H293KMmjXddof5ojiAmGGCwI3oCh2vCTXabIqoos2NdzjaPh9lHUOmOstLj3K6p7CYmyecMyUNtgAp02GA==", "signatures": [{"sig": "MEUCIQCKTLRvTJUprmtkM86GgBifo5bz0CIecO2cvtCcmwqP8AIgFsi+a4y8zHWJ7SvJMRX0OkNRUnZ0JRN8DSrEWNIWrWY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "complex", "_from": ".", "title": "complex.js", "_shasum": "7a18a5a355c20a0d5a7176352cee9a9ad07f525d", "engines": {"node": "*"}, "gitHead": "d59e77ea4a2f0aafd66039114787beb2774d1ce5", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Complex.js.git", "type": "git"}, "_npmVersion": "3.7.1", "description": "A complex number library", "directories": {}, "_nodeVersion": "5.5.0", "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/complex.js-1.9.2.tgz_1454853329981_0.4291346138343215", "host": "packages-5-east.internal.npmjs.com"}}, "1.9.3": {"name": "complex.js", "version": "1.9.3", "keywords": ["math", "complex", "number", "calculus", "parser"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0", "_id": "complex.js@1.9.3", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://github.com/infusion/Complex.js", "bugs": {"url": "https://github.com/infusion/Complex.js/issues"}, "dist": {"shasum": "51e06fe7f618f7d5c6f567dbaf82a84fd865f63f", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-1.9.3.tgz", "integrity": "sha512-cex+zmbQU2o858b5+b5NZXXOTPjW9+Kj47l4PopyVliqHg3xiOc/vGQkvG6EGgfPfPUQi0DxmlzZXtKyEbrkUg==", "signatures": [{"sig": "MEYCIQCEMcvsk8vbrnXaxyBc+lPaYNEd6ggiHBrDgqaHsUnCtAIhAO5ODzu+rwkSJOmYlW1fshBXiETDokGUMVaIA3/hHsM/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "complex", "_from": ".", "title": "complex.js", "_shasum": "51e06fe7f618f7d5c6f567dbaf82a84fd865f63f", "engines": {"node": "*"}, "gitHead": "710da5c1b79ed9a495b0721058dab3928a70fc09", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Complex.js.git", "type": "git"}, "_npmVersion": "3.7.1", "description": "A complex number library", "directories": {}, "_nodeVersion": "5.5.0", "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/complex.js-1.9.3.tgz_1455016918595_0.8771818436216563", "host": "packages-6-west.internal.npmjs.com"}}, "1.9.6": {"name": "complex.js", "version": "1.9.6", "keywords": ["math", "complex", "number", "calculus", "parser"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0", "_id": "complex.js@1.9.6", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://github.com/infusion/Complex.js", "bugs": {"url": "https://github.com/infusion/Complex.js/issues"}, "dist": {"shasum": "9a440371d26978d795ef1f1fee0bb50ff4e3b25b", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-1.9.6.tgz", "integrity": "sha512-oBB2koN1Tq1gj+R9tkJ0phNQC1qVtz5Xx3oovEQZSFiZZQBG22KxaTNINS7iR6nNajteEfnntpBQZakg9iHbKQ==", "signatures": [{"sig": "MEQCIANUtnSAT/uzZjjykWDxeVTUHFOBzMRDsVp8wQOhTBgfAiBBQKKfHbn3MmnQhwZCX9rPE3xmEd8YT/hLZU8t5N/MRw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "complex", "_from": ".", "title": "complex.js", "_shasum": "9a440371d26978d795ef1f1fee0bb50ff4e3b25b", "engines": {"node": "*"}, "gitHead": "a371bba91f1c7526c6f6d55c2c512e091371d8c0", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Complex.js.git", "type": "git"}, "_npmVersion": "3.7.1", "description": "A complex number library", "directories": {}, "_nodeVersion": "5.5.0", "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/complex.js-1.9.6.tgz_1455161556448_0.9659912055358291", "host": "packages-9-west.internal.npmjs.com"}}, "2.0.0": {"name": "complex.js", "version": "2.0.0", "keywords": ["math", "complex", "number", "calculus", "parser"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0", "_id": "complex.js@2.0.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://github.com/infusion/Complex.js", "bugs": {"url": "https://github.com/infusion/Complex.js/issues"}, "dist": {"shasum": "55645f6bc816f0d6af1d8eac11e1c33d0e7cdea3", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-2.0.0.tgz", "integrity": "sha512-xwQNka8zCTv5UsULAK+f65A8UBpYHFzgIsuz93+GagtOzsbXNUtuLH6flxDfUOAL7bBI7Ht+g6xKIecRMeTEEQ==", "signatures": [{"sig": "MEUCIFbB6g2wBq7lJ9k7DqXL9z4fnb4aXUT47zrtlH+z1ysWAiEA10M9576Gx2JuESDSKxO/qOP0utYNardminljiPdPnCY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "complex", "_from": ".", "title": "complex.js", "_shasum": "55645f6bc816f0d6af1d8eac11e1c33d0e7cdea3", "engines": {"node": "*"}, "gitHead": "39ac7fbd5fbe99f1478d9fb68d802a1764459dfe", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Complex.js.git", "type": "git"}, "_npmVersion": "3.7.1", "description": "A complex number library", "directories": {}, "_nodeVersion": "5.5.0", "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/complex.js-2.0.0.tgz_1455374650798_0.9785580337047577", "host": "packages-6-west.internal.npmjs.com"}}, "2.0.1": {"name": "complex.js", "version": "2.0.1", "keywords": ["math", "complex", "number", "calculus", "parser"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0", "_id": "complex.js@2.0.1", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://github.com/infusion/Complex.js", "bugs": {"url": "https://github.com/infusion/Complex.js/issues"}, "dist": {"shasum": "ea90c7a05aeceaf3a376d2c0f6a78421727d6879", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-2.0.1.tgz", "integrity": "sha512-zu2kFxCi8x66budGAEfHcLk0+dcT4EdtiwJ9oIFrvDq8ARJ4T4F3hC0qXc5tn+FzIOYaEOZfO6ew6t6wmGfwpg==", "signatures": [{"sig": "MEUCIEEfvvt4MrS0x3M+w7qiJYP56u4LvuYxDJP0+MB/6GnAAiEAxuVBABVNv7nRB7EowLth+qhlIhWuwMFDKgRsyB7vOEY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "complex", "_from": ".", "title": "complex.js", "_shasum": "ea90c7a05aeceaf3a376d2c0f6a78421727d6879", "engines": {"node": "*"}, "gitHead": "5e06a9f9c66e002d479e4f3730a90151f7f790c0", "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Complex.js.git", "type": "git"}, "_npmVersion": "3.7.1", "description": "A complex number library", "directories": {}, "_nodeVersion": "5.5.0", "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/complex.js-2.0.1.tgz_1455421118571_0.852902399841696", "host": "packages-5-east.internal.npmjs.com"}}, "2.0.2": {"name": "complex.js", "version": "2.0.2", "keywords": ["complex numbers", "math", "complex", "number", "calculus", "parser", "arithmetic"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0", "_id": "complex.js@2.0.2", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://github.com/infusion/Complex.js", "bugs": {"url": "https://github.com/infusion/Complex.js/issues"}, "dist": {"shasum": "1ed89cfb3ecd7d8abe894d5b3984cba8ad513c38", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-2.0.2.tgz", "integrity": "sha512-qLrGq1LiMSHMzE5Dl4hUGx4/i9qwEEMOziXU39q3Xkam1EmSZVkCaxyMD2jWyjhjfpPqPxZWASqgEomXgUG11Q==", "signatures": [{"sig": "MEUCIFEZ0jZ/reavyyFqu/9gFiNv3w26PxyOHxpSR15Fyb8MAiEA//6gOIHJ2Eomk6YLxOS6LN0Ntvc4urGu84BcGckVi88=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "complex", "_from": ".", "title": "complex.js", "_shasum": "1ed89cfb3ecd7d8abe894d5b3984cba8ad513c38", "engines": {"node": "*"}, "gitHead": "a07decec4bef6392fdc0f0b645b71aa99cc55ecd", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Complex.js.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "A complex number library", "directories": {"example": "examples"}, "_nodeVersion": "7.10.0", "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/complex.js-2.0.2.tgz_1496005277446_0.24484794796444476", "host": "s3://npm-registry-packages"}}, "2.0.3": {"name": "complex.js", "version": "2.0.3", "keywords": ["complex numbers", "math", "complex", "number", "calculus", "parser", "arithmetic"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0", "_id": "complex.js@2.0.3", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://github.com/infusion/Complex.js", "bugs": {"url": "https://github.com/infusion/Complex.js/issues"}, "dist": {"shasum": "2a60d5fda4c344b119c5bbdd33847ec6cfd9e417", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-2.0.3.tgz", "integrity": "sha512-dq6ftnRxbK5vMZX/u38TKssMNlUiplnlSudrqUhpJ4i3PkrYfXlGt/9yhTk8NT6LL1jv7GLpsCKXKXyYBgjsQg==", "signatures": [{"sig": "MEYCIQD/5QyaTesDqqnSCakxNj2GtGKyHORQEDCARt1jwcAYvAIhAMCqf//5P3fpskbAU1iNIBVk4SGSToNt7EDeaZ+yzcno", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "complex", "_from": ".", "title": "complex.js", "_shasum": "2a60d5fda4c344b119c5bbdd33847ec6cfd9e417", "engines": {"node": "*"}, "gitHead": "6ed489d0941a7ca42ac3178df088e9b6bc51be40", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Complex.js.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "A complex number library", "directories": {"example": "examples"}, "_nodeVersion": "7.10.0", "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/complex.js-2.0.3.tgz_1496006983984_0.3868541589472443", "host": "s3://npm-registry-packages"}}, "2.0.4": {"name": "complex.js", "version": "2.0.4", "keywords": ["complex numbers", "math", "complex", "number", "calculus", "parser", "arithmetic"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0", "_id": "complex.js@2.0.4", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://github.com/infusion/Complex.js", "bugs": {"url": "https://github.com/infusion/Complex.js/issues"}, "dist": {"shasum": "d8e7cfb9652d1e853e723386421c1a0ca7a48373", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-2.0.4.tgz", "integrity": "sha512-Syl95HpxUTS0QjwNxencZsKukgh1zdS9uXeXX2Us0pHaqBR6kiZZi0AkZ9VpZFwHJyVIUVzI4EumjWdXP3fy6w==", "signatures": [{"sig": "MEQCIGH8xBSf+J32Imms+h8mRDaXxJxPVMHLO/xoDl/ui9rVAiBK8dX5hkkVgdCUAuVMd++lma7wT2dqY3L8PDf8u2iXPA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "complex", "title": "complex.js", "engines": {"node": "*"}, "gitHead": "e08bc3c59c9ef2fc66e761111dba2a9906092dad", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Complex.js.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "A complex number library", "directories": {"example": "examples"}, "_nodeVersion": "8.1.0", "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/complex.js-2.0.4.tgz_1497430425722_0.32762213004752994", "host": "s3://npm-registry-packages"}}, "2.0.5": {"name": "complex.js", "version": "2.0.5", "keywords": ["complex numbers", "math", "complex", "number", "calculus", "parser", "arithmetic"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0", "_id": "complex.js@2.0.5", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://github.com/infusion/Complex.js", "bugs": {"url": "https://github.com/infusion/Complex.js/issues"}, "dist": {"shasum": "c4c7cb982e01cae38930f3253bbcbb458ac8df15", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-2.0.5.tgz", "fileCount": 9, "integrity": "sha512-gu/ct0eLKWWjpwEtM9J5rwPeohgYDF3pdo48Gj2/JSb7KQtgJZF34ExoPnAg/5xizaJqxDjOhVRwzM1lw3ZkQA==", "signatures": [{"sig": "MEYCIQCJG7vPDLOYs+0Qm5lgOZLXhym6otWcmXaYgizIhYiSyQIhAJg8Vb+qCVoSRcImY9fuxpClHbq8yGfM4T3QMG75aA7m", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70526}, "main": "complex", "title": "complex.js", "engines": {"node": "*"}, "gitHead": "a7c08d03ce8f0829273da23c3fdbcbad3b4a32ae", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Complex.js.git", "type": "git"}, "_npmVersion": "5.8.0", "description": "A complex number library", "directories": {"example": "examples"}, "_nodeVersion": "9.9.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/complex.js_2.0.5_1522352424313_0.7129464531907463", "host": "s3://npm-registry-packages"}}, "2.0.6": {"name": "complex.js", "version": "2.0.6", "keywords": ["complex numbers", "math", "complex", "number", "calculus", "parser", "arithmetic"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0", "_id": "complex.js@2.0.6", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://github.com/infusion/Complex.js", "bugs": {"url": "https://github.com/infusion/Complex.js/issues"}, "dist": {"shasum": "f028adc3e64bc1e669815b3cd2af461bcc757cbf", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-2.0.6.tgz", "fileCount": 9, "integrity": "sha512-YbHL8qea4bA0FN/mdsuLviATGRVg5kqnSMxKQ4isj7nxQctEdMOUQymuo7jqTKJCRpnZMBSBYeRm2OY7DPZ59A==", "signatures": [{"sig": "MEUCIQC/P3UXUi5tfeGhsFHcKToIAS+bpsZ2htRBI1sdpm/MdwIgVKqWI9kjFgZBAYE/T/kAto8ZYembXDyhe5jsAP7M3pI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70516}, "main": "complex", "title": "complex.js", "engines": {"node": "*"}, "gitHead": "a4656f424a37ffcbaee2ddc1934960e8ba2e8b79", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Complex.js.git", "type": "git"}, "_npmVersion": "5.8.0", "description": "A complex number library", "directories": {"example": "examples"}, "_nodeVersion": "9.9.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/complex.js_2.0.6_1522357403210_0.07044372168583513", "host": "s3://npm-registry-packages"}}, "2.0.7": {"name": "complex.js", "version": "2.0.7", "keywords": ["complex numbers", "math", "complex", "number", "calculus", "parser", "arithmetic"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0", "_id": "complex.js@2.0.7", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://github.com/infusion/Complex.js", "bugs": {"url": "https://github.com/infusion/Complex.js/issues"}, "dist": {"shasum": "9403e829a64f8193d891ef2c8ba18656eb3a620d", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-2.0.7.tgz", "fileCount": 9, "integrity": "sha512-kBCAGswFA1D8itZdVnnRoLJhYEIgT0Ijvpi9bDj1AOZvACQkiMgLXpdyMbtGTFB2mkcJtGM0335W2/nfVnrFtA==", "signatures": [{"sig": "MEQCIGvQlTk+/IBNQeZFM/rU/Xy9BvTyDG/wISsXmj73PGtTAiAEL4uV1qZXQqXgFB3sJA8DdCjE8ccHlasdYzNWJfNwtA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70300, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa0TUCCRA9TVsSAnZWagAANhUP/jFVK2pLQ5TEVg8c6OkW\n2mgJtM+XzAufaPQNkBFCNj02KAW1/5QwLkoYRjUpZWec3cpRstdhX9wHFRO3\n6p8KH2xx9dlAC/48u4MdJJaGhiJwWoAXeIXOZM8HRyQt+dDcBde2tafzNyDB\nsJB8MqgsaAYTUnGomIIlz88JD16jlV7sAM5XbKhi808caH8wmoCNuug6YW9z\n+NJ7Ygyk1wQ0dJzus/fpaJwYz06HgC2CueMn8zFvpa2P0poWzRqOToOv5OkV\nQT7sfREIPGiHsuPmrItg4MSfVpZvgMfgM0D0BlvFGhdJDqNJtgFLjgLNadwG\nqC5bqz7d1FrRlWuUvdDAZ7AHyhlus/QvX4oGqzgci5IGSjR+Mx/Ife9AeMPJ\nv42qshCEg2vM4B/HyJ9yG0uwQ79CA/dlh2DFzEO9BbVMrNhIkMmBg98rIf2z\niCaBtmhIPxi3KvDjGI2CxrMEbBcC1pqK9bnPqT6jga840KVoXdO7X4DTYsIR\n3zyTw/K842bIoWg1G+xveW540K3QNJazTSK33L0RjAojmOr7zGgRtEzeLDmq\nV6+shrwyhLz3tYdRr6jW+x2+tF1+Rjn2HMi/se8Lm9GWeDPh+6X6ZiI18i0I\niE3zmPSo+r4zOzrYCg0pW6IyKb2bBfegqB4jgGUUGIQzrzqLIcE9ryYiGLUr\np1Y/\r\n=e62i\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "complex", "title": "complex.js", "engines": {"node": "*"}, "gitHead": "ba8decc29eb8f5561377a63b0d651f0898e06b96", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Complex.js.git", "type": "git"}, "_npmVersion": "5.8.0", "description": "A complex number library", "directories": {"example": "examples"}, "_nodeVersion": "9.11.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/complex.js_2.0.7_1523660032924_0.40071633580150157", "host": "s3://npm-registry-packages"}}, "2.0.9": {"name": "complex.js", "version": "2.0.9", "keywords": ["complex numbers", "math", "complex", "number", "calculus", "parser", "arithmetic"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0", "_id": "complex.js@2.0.9", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://github.com/infusion/Complex.js", "bugs": {"url": "https://github.com/infusion/Complex.js/issues"}, "dist": {"shasum": "40f81911d18013980dffd0941e85f0a0eabb55b4", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-2.0.9.tgz", "fileCount": 9, "integrity": "sha512-mM/odYc282XuIUJJ/syeKnzF+cMjfP5OWvdeIRm2iWmrjEvCY/Z5hyDD+Yio8N6cANHnRP1H0PqV48QT9CZ8EQ==", "signatures": [{"sig": "MEUCIDMYRSKuFfPBmcW+PYeOd3Vsq6k4Onh17PbW7a3ef+3GAiEAy6TzkJkO3mxlLM3AAGbuFU7LBTszsUIMahd2CgDaiBs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70533, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa5Wp+CRA9TVsSAnZWagAAJ0oP/A8Mw+v5PzB1hcjCiiP+\nYObE73Q7dkqYIZa95pvWa3c4TKViEOklEqyT3ueb5ZRN6w1/Wxg5pJAFHo+6\nvJ9SbegvLhtN86Q6QSknd0oamSAwHUyLnFtiOGHEJJAkPgJGreC/HkFlGVTZ\nRyQTCy+lxguSsTaWlQ5jSnrWBUmwjblhcJ2yMrrb4zi5HStwfqHRO9PW4Rq+\n/3EIAr1Vvcjn5/TbQW+yGeKhISY8QLAeKlsqXo6ImMYRSHkytDzxGHdc1XK/\n/dG62ATGAEZd16effIlcZGw4aoeydP5UeJs0haE4Zt9Gx1B/V4kRDadYd2D/\nOWjTu2y9icAgMQlirFdnQKXlzx77wXRIZN9j3tvExmZA9jW+dkuPoFIxa1Hv\nKstKCAco71nS3iCwfsYLlpGKKw04ij9cHd40k9JyF9CFvTeNN2zJ4iCMwp86\nRhq77O165ZkBmLE+Gn9eVOpDHmlNYrecyEFNkUWQ8TsrPRDM3+1NGaIzI2Qo\nXcfyO8Bu2bdYayyQIJPK3hAJBBM9V3G3agDgo4n+rlm1sGK5DRG2IZA3+wO0\ntsrZrYAlSaQL5Bl/v9rGXMugxkyK7tjK+yLSHEifPnHBEkiPzPlBIQN5aN6S\nfXPqGtfEQZZB4fbGOrH74gvGuhe5Lv4VLwxLfEpD6C9EPCqwX34H7R7vz+Bd\nngnO\r\n=6nIp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "complex", "title": "complex.js", "engines": {"node": "*"}, "gitHead": "cacff05d41fbbd1c3fb24fc70cebd6458479a5a9", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Complex.js.git", "type": "git"}, "_npmVersion": "5.8.0", "description": "A complex number library", "directories": {"example": "examples"}, "_nodeVersion": "9.11.1", "dependencies": {"npm": "^6.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/complex.js_2.0.9_1524984445240_0.14066760638811093", "host": "s3://npm-registry-packages"}}, "2.0.10": {"name": "complex.js", "version": "2.0.10", "keywords": ["complex numbers", "math", "complex", "number", "calculus", "parser", "arithmetic"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0", "_id": "complex.js@2.0.10", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://github.com/infusion/Complex.js", "bugs": {"url": "https://github.com/infusion/Complex.js/issues"}, "dist": {"shasum": "afac74e4fb9131e8709f9c300420ce58fa36f18d", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-2.0.10.tgz", "fileCount": 9, "integrity": "sha512-PsT3WqpnTjS2ijoMM8XodCi/BYO04vkS8kBg1YXcqf5KcnKVV6uXUc1eeLHhBksj8i7Vu9iQF2/6ZG9gqI6CPQ==", "signatures": [{"sig": "MEUCIQCMpW/XLqgiC3V5HtOIVfG30AEXg9/uYYdswMWHteCB4QIgU3r3m9zJZbAUjplvwUZtJeTPs5lyDF3esVgvM/PpW0M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70484, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa5YbWCRA9TVsSAnZWagAAfTkP/3qsKi3/FWTrNH5VAuVh\nQHIDDaQkMGoKdw9Dm97LDY82PMb6tjyTpqCL3cfLqXCrVrPEQ68TB+0WlYXA\n+OVqqpt5eo0c5avmmsR2Ha0agDq1gpSw27jKUEshY3ZK4G0jhtcY0pUeAgqX\nhuNZwtUgPs/LIkxSWT3btly9PvEuDa5nOZ9Pj/fmQGnJ8FXX77p9K6D2IxBg\n+q78kqXX8rAs8RrW7waCz1m3krB8Ga8P2NzhnEIV8HNqOoPMDGXQiInuucLj\ng/tGhCD/E88LV3UyIZMnJFj1rL4F16uRjC11NqchQSJRfyuqyl8Z/spO92Zy\nAMp33btWkhzzvi7q8p+gRNBpzDceuvjlTeyjMH7NSrjQe48vuvyo8jx94xJr\nmQ3Ai4DLUxQxUeJ27EG1N74OjlUkYjI2RZjVFfa5LIe7MEIhMdvxpOLjuWqf\nRZL2RuRpDZmoMegLkwfpquUwo8hBIkV2LkMvbeqnFn7Ira+Zbrz0YVFZ9nX0\nW7IEGb/hF6g5uhGh2qOyaAHugAsosXnl9yUU9dtE9MMx/CL/OztOLScQgRE8\ngVu26eMR61xkaFR13wHvde0OA87HG/Vl9ICYrxWsg/wn6qmUiLBdKx/E1ijg\n5lI/NLcGHemABEXG+aqg3TWhx3vT5iSFsEr6dl34VLoXdazO3uFWgJj6tFe6\nEY9y\r\n=jyY1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "complex", "title": "complex.js", "engines": {"node": "*"}, "gitHead": "e04462ac3fff23ca2693662f7bdfd2bf6ea9ef70", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Complex.js.git", "type": "git"}, "_npmVersion": "5.8.0", "description": "A complex number library", "directories": {"example": "examples"}, "_nodeVersion": "9.11.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/complex.js_2.0.10_1524991702133_0.9596300196235732", "host": "s3://npm-registry-packages"}}, "2.0.11": {"name": "complex.js", "version": "2.0.11", "keywords": ["complex numbers", "math", "complex", "number", "calculus", "parser", "arithmetic"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0", "_id": "complex.js@2.0.11", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://github.com/infusion/Complex.js", "bugs": {"url": "https://github.com/infusion/Complex.js/issues"}, "dist": {"shasum": "09a873fbf15ffd8c18c9c2201ccef425c32b8bf1", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-2.0.11.tgz", "fileCount": 9, "integrity": "sha512-6IArJLApNtdg1P1dFtn3dnyzoZBEF0MwMnrfF1exSBRpZYoy4yieMkpZhQDC0uwctw48vii0CFVyHfpgZ/DfGw==", "signatures": [{"sig": "MEYCIQCzJq471DH1fKaQgHhBoJTB7f7bldIqUhrIQhbhgxljzgIhAKqNbznoKHDn/EotMqnpkH5Iy7CUKpccj1C/qY8Jr8dd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70787, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbPKwkCRA9TVsSAnZWagAAdI8P/i2RqP6fN0LIVCVLrz9u\nP+wnbBf/5iLgpR0vhAFia/CtG9s58cdhq2YIwf1TMCiUER5gp0m9ASlWKjQR\n6ZNvrRVrfpff/jWFVQ8oXnSHF1r0FuaQuLS8MPAnCzY4pkY/xVxaARFWGaTj\n5oU4cY2oEgWq7SyesjLiOLL9k/B53c2JP47HNqbRbwBxCwhsOlQ0Ow8OuWm5\nrGCUVr5L7DPCSuHmayvyPcgt/2Xs11jLjYrJrcT+kqidt4qgOZXVcsOjNiwq\nxqBilta85U60tdNVnWOAS4NUsSOoX9unHRPrF21qJB5pbVgT2XL/fOS7AE39\nrL9Is5YcyryFNAlD2lBwkNu6ChT5MSoJlwQtJYjAZCFlv9WPtOtMBJzb2KA4\nULFCTEWr3q//EAC/FvC1S6WTl4GRLWgWN53fwGZOFsxxuiSXPMNDkKVwUJLz\n/3wK1Xwy9VRrGMlwWqofOnVQPr/yLoApRIPONuGCMlVhWG2jj/XgqTfNml40\nRMhVYeuZajr9V0h71ssIFGF4jS5aQMXyIFHQ69f3XJJm+pwSevYwMNxBWOPs\np+uLhnVNPkTsQ0+XdF7nKySiruE/OdXgSXnvzxgUhns3H4v37CdkqnN/gKEL\n5SrzXhwNFqNJ6axZ0xLVkeDQI9Nlru6Czkm8G3Qb8v/dD8HoFTWR9KIsimSw\nCXse\r\n=vxEJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "complex", "title": "complex.js", "engines": {"node": "*"}, "gitHead": "390707b443497acafa2de66e5a91229fa8c7b293", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Complex.js.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "A complex number library", "directories": {"example": "examples"}, "_nodeVersion": "10.5.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/complex.js_2.0.11_1530702884217_0.5083592832938013", "host": "s3://npm-registry-packages"}}, "2.0.12": {"name": "complex.js", "version": "2.0.12", "keywords": ["complex numbers", "math", "complex", "number", "calculus", "parser", "arithmetic"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0-or-later", "_id": "complex.js@2.0.12", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://github.com/infusion/Complex.js", "bugs": {"url": "https://github.com/infusion/Complex.js/issues"}, "dist": {"shasum": "fa4df97d8928e5f7b6a86b35bdeecc3a3eda8a22", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-2.0.12.tgz", "fileCount": 9, "integrity": "sha512-oQX99fwL6LrTVg82gDY1dIWXy6qZRnRL35N+YhIX0N7tSwsa0KFy6IEMHTNuCW4mP7FS7MEqZ/2I/afzYwPldw==", "signatures": [{"sig": "MEUCIQDVrYkrVZ0JYskItMl52D1e4GljAtzkDivr5X3BCAs/HwIgLZnJSN7TF4NocGVhPT4+IGJ06663Dl50T6Q7mlDN7dc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71857, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgOqRNCRA9TVsSAnZWagAA1zsP/31TJ12A6tUFqG8td8ms\nAo4Fy98HaiG1N9arVb7uYnZ1QF/2gG0BJ3xA2tJMSOYyAVAurVE6H/RO2BJg\nUrFkuZVbYZUCT2Qi/nSOMBtiK+uJIDzPBKT7UCgCkz8POVQK1b0NEZSYP8HH\nU47EkgLYagQyWk6kq5iINi9XQ/7ixwveViPmzxbjEt2Fgu8WrhjUErM6pdPF\n/ZV4DIB8nvXH7UaBOzcuFRocGAQOpzs7RiOGzeBcBy4Jf64G67DveKGLFTLA\nWdvAlt/i1Q4WLFOQHuYSQbHvqImnGWhcYKVwDxO1ysG7nNqac0TYPSiZdwOf\nrrWpPXIsRghdsDuSJ17KJY54qBFS18xkCh9JUBs1cfZin0KJfYTxWF+Jk9JV\nVK6Rn1j6US9E3KvXN0csqJpzHM2pF5DLaznK2Ka0KK9caL03qTbNzVAYlLcp\nt6oSE/GOeH1J84SMX1A9dW7vb0rzwzumQXLxQYRh5IspwZm3miMNei9Qjz9y\n1kvHYAFnQDYmk0rL6mZjgmYjRD2C9q5tzkq1rQ1ByaDCjm1DBeGCDvq6yyXS\nY9reMc1O+pXHmPhf/ffxREl+BjGSh5jr8ZWUvwmtQO+qTlK6p5T4+Dh3NxHn\n5Y5/wLv94iPBDQddFZIbrnLl+T1VuHLBLvUyoBLCylP9TO/NsmlWfUxnvLSn\nNqT0\r\n=li0r\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "complex", "title": "complex.js", "engines": {"node": "*"}, "gitHead": "d25dc30af4077bd2798f3b4648e96b88e607de11", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Complex.js.git", "type": "git"}, "_npmVersion": "7.5.2", "description": "A complex number library", "directories": {"example": "examples"}, "_nodeVersion": "15.8.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/complex.js_2.0.12_1614455885451_0.27795181511273737", "host": "s3://npm-registry-packages"}}, "2.0.13": {"name": "complex.js", "version": "2.0.13", "keywords": ["complex numbers", "math", "complex", "number", "calculus", "parser", "arithmetic"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT OR GPL-2.0-or-later", "_id": "complex.js@2.0.13", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://github.com/infusion/Complex.js", "bugs": {"url": "https://github.com/infusion/Complex.js/issues"}, "dist": {"shasum": "00cf7ba082565e164813b7bbbb0ced5d2aba172a", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-2.0.13.tgz", "fileCount": 9, "integrity": "sha512-UEWd3G3/kd3lJmsdLsDh9qfinJlujL4hIFn3Vo4/G5eqehPsgCHf2CLhFs77tVkOp2stt/jbNit7Q1XFANFltA==", "signatures": [{"sig": "MEUCIQCwNy3XeITOnDXzgcd3PuP5X3L7sMReGMOA9ACrg07izQIga2S5YoCn6ArgLY1rJ4tNt3RNr/8MzJB1o6fTWiKV7AA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71329, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgm9J4CRA9TVsSAnZWagAA7OUP/3ZZdwWTvZgmclXRdmnY\n89CVpxxDq8+PkT/7U5G9OnkYvFQdu3QE0Yp+UJpsirhQeIAxZms9A9BA+wr9\naWoExMRllT34X2u62Y8Qg9LuJyTm6ecMsnxsfdx0VA+SY483km0URKjijj1b\n1OilwXCt7V2RRhs3yWR1AhjamLvFxpO6YHGcUcwmPHhHbmmggmgBc113ORGm\nx+uC3dBdAa8z6Mm8TD+2CELWyqU3SYDRFC7atyeG9Uv4Bv1g+YzfxTPM1VED\nv0uKX5FgoY5V19zfD5tHpeHNt4dpofIbYo4aLzwItmBtkW4bHGxoABhcD/u4\nAuAj6Da3foMt6yjLYjrq44L9K7OiKuUP27S+T1skvFVcjd84SdGEHuHcP9Cx\nKS7U4UM9W2gQHOqvoPWKI6Cx4kUD9W6HlJ35cXuNeTp6WOkyDp3dSP8mFkUr\nHb7VnKUfF1Kg+Zb9NfswgQ93yt/0PusumOtyF1GP19rnh3K0zpGg/wcfoK87\n3vKXKYiNNj08RN76qoNyK52FdHMhjK3hB27u1H19+G1xSx+jVIg0aleLzZkt\n8Uw03zzKBi4zl7Bg3Sfffp0Vk/8MDESOlasf9Z1FXRxztiO0YamQc2xTCaKj\nDJa0/aF4zNew9lzLgDOkk5NO704kPJF3fD2Gw6wZCz48CLjAaK6N7koJAXfW\n1Lkg\r\n=InsP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "complex", "title": "complex.js", "engines": {"node": "*"}, "gitHead": "f4e80bd3c5fdde7e54ceed90d1d0a74f60b7eb02", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Complex.js.git", "type": "git"}, "_npmVersion": "7.11.2", "description": "A complex number library", "directories": {"example": "examples"}, "_nodeVersion": "15.8.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/complex.js_2.0.13_1620824696163_0.34336344315795864", "host": "s3://npm-registry-packages"}}, "2.0.14": {"name": "complex.js", "version": "2.0.14", "keywords": ["complex numbers", "math", "complex", "number", "calculus", "parser", "arithmetic"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "complex.js@2.0.14", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://github.com/infusion/Complex.js", "bugs": {"url": "https://github.com/infusion/Complex.js/issues"}, "dist": {"shasum": "a4409bfcfd307e1e0a5dc5c0566c2d2c0e9cf6a3", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-2.0.14.tgz", "fileCount": 10, "integrity": "sha512-wQCuHGoB32gqLbXUL9iim/ANC1oMjeIt6EarSJymAVoQYVSOcnMgyZnH5QxOC3K5bCvRa7toeXLvyJOl5USevg==", "signatures": [{"sig": "MEQCIFhZ1wFPXUpyX2UFVOPpbiIM3tsX8RgxQzEYLCRjPMvNAiAQ/BHgm+wgy1rtsr99RCRd6AX1Ao/D2kQFF6mzCkL68w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75942, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg4ZjQCRA9TVsSAnZWagAA/2UP/1NUw95Kp3kJanmc3ooU\nwsdsP93ekZcrSBLQTRkcysyoZYt7yyykT/EBmuE54+Kzffr4FnX5/M9U57WX\nJA3GuvqdMw2FtVb9IBvvTlcHMvXBPb5PsjWG5srVh49WiHeio44517kQhYf6\n1gt7XOAJQpn6Z+iKKzJZiAJx6MZGW4Nyozob0UsUi0KGr2Yqh1jzzNR/ni9A\nRCxgpI3dO+EY3teQpPfSJYld1RiRaU6mF5vHdZ0mM5NupXvUebTuuUye9+V5\nCK2SimYbMuxxGzr+z/YDhyPGgIPJcbKoBdReI45zCAFgpykVTN3R+posZ/rq\nO80byWgTOwYMN1uNboOsC5aR6bBsCx+qjMgRFnS9k0HbBXzQyb8kV8eeeNcT\n1FY0xUm2+O2WgG+eAIRIee0/csVIMdctzFNfNcOiFjs9bD62htic60gNFKid\nLesjFemXJfAipP8YQCDkqeXNZoxMPecPrDgLvTC4BmsVeHiT5xglf2l6oQxN\nqU9E7/66iPNV4Oyl0vJsTMswu283iR+m9sKD/rSC1Tzv/1QLhnQ33BzK8Eks\nSxfP8HMXoJANY6uao1BBaSldzpQ25s05g+oSUFy165DWn+Srk8ggutj6TRDO\nNCo96zKkSlUWxlmqzct8NcN5zPO0F3c6eS3jv5hrjCVJ81kHvlnRorxP5CS/\nlTJ/\r\n=JWbw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "complex", "title": "complex.js", "types": "complex.d.ts", "engines": {"node": "*"}, "funding": {"url": "https://www.patreon.com/infusion", "type": "patreon"}, "gitHead": "89313ee710e41546dab872f9e0da25c2376f7dbc", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Complex.js.git", "type": "git"}, "_npmVersion": "7.19.0", "description": "A complex number library", "directories": {"example": "examples"}, "_nodeVersion": "16.4.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/complex.js_2.0.14_1625397456193_0.4429346281605009", "host": "s3://npm-registry-packages"}}, "2.0.15": {"name": "complex.js", "version": "2.0.15", "keywords": ["complex numbers", "math", "complex", "number", "calculus", "parser", "arithmetic"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "complex.js@2.0.15", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://github.com/infusion/Complex.js", "bugs": {"url": "https://github.com/infusion/Complex.js/issues"}, "dist": {"shasum": "7add6848b4c1d12aa9262f7df925ebe7a51a7406", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-2.0.15.tgz", "fileCount": 10, "integrity": "sha512-gDBvQU8IG139ZBQTSo2qvDFP+lANMGluM779csXOr6ny1NUtA3wkUnCFjlDNH/moAVfXtvClYt6G0zarFbtz5w==", "signatures": [{"sig": "MEUCIQCeDTItnwKYpDJWj6rDQoX8ir/F/EZMlW0gGaoXcCiYPQIgJ3eMIHgaUiz/DgUqdOOooYQF+BT3qCHEEIV5rUF1Elc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76606, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg4aKzCRA9TVsSAnZWagAAYq8P+wZsbaenGv9suxcg+MbM\n2EBtLhiADA08QWxTjR28kinLYrAgdDn0XfpCs3YHaEctcIxuZKRnXlL0FB5f\n8E6F2CLeVyfQk5VxFT34BvCj75SyRuI/VAUnw9oVHW6ZefVll6F+voYCKhNA\noawGuzDHeFhfa73FhkUKsBOngfy6zl3QO/fUwkF4m9JCdDt7K2azAVPsvtua\npXbFUdZzfKb0F2QJz0qJQWQmCbeDsY1oOdrg2FrkfgQo4obm8+Rb1ls9LlWK\n1RLojxH6VE2BvimwEg7VOP7pTjuhxb9JlPubtg/XZ/M5IqmUI9j6csRK8KCr\nSgRwdHjoQBNDa56iI1zVg4rTKYKj98x3fIdJm1CguTNjuwzKjpwlw5CzmAsr\nz3E1+QMm2LmB5xHCnK2Y13AeyQNzQGW/RSvn75jykwtGDabhcTIvF3619JIH\n/1Xkvp/l+bCTCNIW0DJmMjy+o/LpvNRrtYPmUiNT4H87xLmUFqYXyl5mzwzW\nLhb6zdKSYo+pI3a/RjbBl1cp87BkPx1wtpkRb82SHERC+w5yM00p6PcUAvzs\nJgtn7T6N1eB/aTCGTrW9iw1vGkSHHEAZtJaJMLpM1jIMOQ+MrPSdPQP1DcAX\ng3QpvBNr7fi6lLsWwLn5W5wiOcn/2iAMNt1UJ4w+RQPrhfCptjHaCNy+eYFk\niVYc\r\n=ailw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "complex", "title": "complex.js", "types": "complex.d.ts", "engines": {"node": "*"}, "funding": {"url": "https://www.patreon.com/infusion", "type": "patreon"}, "gitHead": "997d3ae3eed5886fd0295d4d27f67801d0954d02", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Complex.js.git", "type": "git"}, "_npmVersion": "7.19.0", "description": "A complex numbers library", "directories": {"example": "examples"}, "_nodeVersion": "16.4.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/complex.js_2.0.15_1625399987369_0.3381569240868443", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "complex.js", "version": "2.1.0", "keywords": ["complex numbers", "math", "complex", "number", "calculus", "parser", "arithmetic"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "complex.js@2.1.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://github.com/infusion/Complex.js", "bugs": {"url": "https://github.com/infusion/Complex.js/issues"}, "dist": {"shasum": "211258e271345213c6f004c0ea90a34eb1e0c753", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-2.1.0.tgz", "fileCount": 10, "integrity": "sha512-RdcrDz7YynXp/YXGwXIZ4MtmxXXniT5WmLFRX93cuXUX+0geWAqB8l1BoLXF+3BkzviVzHlpw27P9ow7MvlcmA==", "signatures": [{"sig": "MEQCIAQe6P6BCDH4MyLwqVm1nSdYGOo956gXJVKPS79DJFudAiAz1/uPYPl3YmpNdlhRUwlqdnLqqnYUerSN/t7ayBVZ2Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78753, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiQJ3+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqUog/+I8ZbZ5S+1z4EcJ4rPQESW62027m8ryzDBPUrA8PgNssPVsWT\r\nOUTtWVhUT1yfIx6+8MNjVd7CSbU5wCGB59gBR+byYrPMY9cNTv9w51IL22gQ\r\n57Rw2EUCJmIzD0x9eZNZ46VB0YHMviecIRBLIhMj59K0OcyTUsO2EWOAuI7e\r\noYHwscQsR0Q1MxNGLyOYa06Jt7M/RvgZve0gy/FlDymh/ZnRZTZnxgidwZQc\r\nsz+r0Ac3ch0oPfv69rk2ZVa9tHByUv539IPTqsQNbbhpLjToCkEYcKCVw7++\r\nutzy9qBHpsTcMU0JGVIO8KzNdsJRVJvkQ53qGDmkasXR/gs5BiXBJcNT+UaC\r\nrKgrt8zFREQ7KLzS1IweMXtyCZq1+Op0oSDeYULxWRcaqwy+cCOkOxNl6l0C\r\nbOFK6kYai+/JQLO6Kvi8dZqRicSPDmX3EHixLhk+ZMsvL1eGjABXm2mkcTVk\r\nef3umKHLn9eEhkWhKwe4P0aZS/UTXs7CTREeNAupo88mDcIxy2uYUUk+jTTg\r\nZRafNpU+D6hGoWa2ahJjaPyqGCGvrmrnRLnIQZJjmMBGWXElp5vWvzfhR+n2\r\ny903c5pYu3yUDlHLSvTy2xUnZUvXhIizsgWiXsJnvGSU5/0UX5D+qsr3evR3\r\nSSuwHHWTLU9BopckC1Akm2CMkOjInBEiuLU=\r\n=K9Vs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "complex", "title": "complex.js", "types": "complex.d.ts", "engines": {"node": "*"}, "funding": {"url": "https://www.patreon.com/infusion", "type": "patreon"}, "gitHead": "054f08bcd1e6fe2f7151041c26983f3b7f30a3bf", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Complex.js.git", "type": "git"}, "_npmVersion": "8.5.5", "description": "A complex numbers library", "directories": {"example": "examples"}, "_nodeVersion": "16.4.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/complex.js_2.1.0_1648401918193_0.30618323149757165", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "complex.js", "version": "2.1.1", "keywords": ["complex numbers", "math", "complex", "number", "calculus", "parser", "arithmetic"], "author": {"url": "http://www.xarg.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "complex.js@2.1.1", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://github.com/infusion/Complex.js", "bugs": {"url": "https://github.com/infusion/Complex.js/issues"}, "dist": {"shasum": "0675dac8e464ec431fb2ab7d30f41d889fb25c31", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-2.1.1.tgz", "fileCount": 10, "integrity": "sha512-8njCHOTtFFLtegk6zQo0kkVX1rngygb/KQI6z1qZxlFI3scluC+LVTCFbrkWjBv4vvLlbQ9t88IPMC6k95VTTg==", "signatures": [{"sig": "MEUCIFWFggI263xsftdL/lG25Jo/hvYN8VfWgj4CMnqqZ4LbAiEAuCPBAvPUgLuqt6X0r4LtuPf2HY48NY90yMqXeoFiT2o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78909, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVcMsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpUjw//SxIOuMe7l/Avnw7ZWx3Pi0d58dkR3eNYx2q1EqgolFd+1jjX\r\n4MXiHza5mRyk/VfGfs5q3XLTzZA20c6J5XUggi8TCsCxZfoW30Ew0sCQITII\r\n9sNzIwcSzJ4SHgJZUvO7OvEZXsjAcnx+PWo7srBc+ep8HSGrbk4uzmxmVV0r\r\nbueSu9iS/eOTs+TDNHi9gZ3oEvksEBX+ahhlGKbXSl8hfNNAPIEg1eLrtZ38\r\nt+di/IiMSFBBJzBAmqRLcTaDrvl08YDJE52PFO/ShXCjOuLJWKN9OrnkjhT8\r\nIYABjZQHPNuN8+d+a9fMI+IiDJ6wtT2m9V/5OPzjhn0UDns2+Pp0hn0mMR4J\r\njqVQ+L/XYBnZvO4TF5Wj87/ExlkJCuTCx/qQvplNotZxbH27KxJKmsH6UVU6\r\nvvkzpenSw7omv/5Jx5MxdYxrqK5h2BYedpaKJ1X2053LdbrX0xH9la8d9QHl\r\nEFyZ1bGaxF4bO26FYwrlAT1T10FB3FwxNBAzZmlUowj1ZdogGeVHuemzfxCd\r\nSlnUe1WWOGDeQHVgrly+0PPk3rmAjZAi3BWuasAXO6/3jMx3YJ1fcA8KlyqT\r\nJPiOKhNLY8287Cuc+nRJSHh1FcKHp2JP8iZFJ1TsQo8vBP4iUhHRZa1htkmx\r\n8ZALPfDrMBBXppciNYnlGRiIFrIx9tmbeGA=\r\n=oOfh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "complex", "title": "complex.js", "types": "complex.d.ts", "engines": {"node": "*"}, "funding": {"url": "https://www.patreon.com/infusion", "type": "patreon"}, "gitHead": "d995ca105e8adef4c38d0ace50643daf84e0dd1c", "private": false, "scripts": {"test": "mocha tests/*.js"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/infusion/Complex.js.git", "type": "git"}, "_npmVersion": "8.5.5", "description": "A complex numbers library", "directories": {"example": "examples"}, "_nodeVersion": "16.4.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*"}, "_npmOperationalInternal": {"tmp": "tmp/complex.js_2.1.1_1649787691827_0.34960219794405867", "host": "s3://npm-registry-packages"}}, "2.2.0": {"name": "complex.js", "version": "2.2.0", "keywords": ["complex numbers", "math", "complex", "number", "calculus", "parser", "arithmetic"], "author": {"url": "https://raw.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "complex.js@2.2.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://raw.org/article/complex-numbers-in-javascript/", "bugs": {"url": "https://github.com/rawify/Complex.js/issues"}, "dist": {"shasum": "22c3ab1a5db16ae9550acc7a6e015e10a68e2b84", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-2.2.0.tgz", "fileCount": 10, "integrity": "sha512-gKZLyOdqy5BZTh3PxB0RPuF51D8Go7MW2gV0uuagTW/lncAqndrbMmLiTwrfi6qSYx9tKU/b3GxNpLaz+wMBlw==", "signatures": [{"sig": "MEQCIFsnPegv6yYNKFp4ZBsONF3JcjMrrYi7b3e4IgS9M93uAiBcDNeJT/5h7ELy4Z4doQ9OFzHhJauSH5OYdyPCHCtlTg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 132190}, "main": "./dist/complex.js", "title": "Complex.js", "types": "./complex.d.ts", "unpkg": "./dist/complex.min.js", "module": "./dist/complex.mjs", "browser": "./dist/complex.min.js", "engines": {"node": "*"}, "exports": {".": {"types": "./complex.d.ts", "import": "./dist/complex.mjs", "require": "./dist/complex.js"}}, "funding": {"url": "https://github.com/sponsors/rawify", "type": "github"}, "gitHead": "c6c5995e3fe4f5247a6069e1bdc42b93ac6ac73d", "private": false, "scripts": {"test": "mocha tests/*.js", "build": "crude-build Complex"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/rawify/Complex.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A complex numbers library", "directories": {"test": "tests", "example": "examples"}, "_nodeVersion": "16.20.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*", "crude-build": "^0.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/complex.js_2.2.0_1728060965200_0.46616829763092693", "host": "s3://npm-registry-packages"}}, "2.2.1": {"name": "complex.js", "version": "2.2.1", "keywords": ["complex numbers", "math", "complex", "number", "calculus", "parser", "arithmetic"], "author": {"url": "https://raw.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "complex.js@2.2.1", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://raw.org/article/complex-numbers-in-javascript/", "bugs": {"url": "https://github.com/rawify/Complex.js/issues"}, "dist": {"shasum": "d1d8af8dfdd79f7dc7c9916eef3e12270c01a519", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-2.2.1.tgz", "fileCount": 10, "integrity": "sha512-D+EeuMW8pCz+Lt1YbUSfpZsT/Stt6slSqsRG7dTqLB/g4t3I+qse2lxsa00BUta7Jgo24Bw90QaVpv1eziuzHA==", "signatures": [{"sig": "MEQCID40VSz6X9xinIKt5sngQmvVcIKoM//VuD1E2OduH7pLAiBazuZFH5J5xK/QTBgFbZmiynxnyIbmso7Tz75cDQJUMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 132241}, "main": "./dist/complex.js", "title": "Complex.js", "types": "./complex.d.ts", "unpkg": "./dist/complex.min.js", "module": "./dist/complex.mjs", "browser": "./dist/complex.min.js", "engines": {"node": "*"}, "exports": {".": {"types": "./complex.d.ts", "import": "./dist/complex.mjs", "require": "./dist/complex.js"}}, "funding": {"url": "https://github.com/sponsors/rawify", "type": "github"}, "gitHead": "acdd9fdaa995ef2bd13b2b7d22b765d82a08cac7", "private": false, "scripts": {"test": "mocha tests/*.js", "build": "crude-build Complex"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/rawify/Complex.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A complex numbers library", "directories": {"test": "tests", "example": "examples"}, "_nodeVersion": "16.20.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*", "crude-build": "^0.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/complex.js_2.2.1_1728134814123_0.8764417485124594", "host": "s3://npm-registry-packages"}}, "2.2.2": {"name": "complex.js", "version": "2.2.2", "keywords": ["complex numbers", "math", "complex", "number", "calculus", "parser", "arithmetic"], "author": {"url": "https://raw.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "complex.js@2.2.2", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://raw.org/article/complex-numbers-in-javascript/", "bugs": {"url": "https://github.com/rawify/Complex.js/issues"}, "dist": {"shasum": "e4ebc9198247029b974e5feb2bb3ce01aa2e66a5", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-2.2.2.tgz", "fileCount": 10, "integrity": "sha512-R98hR2Ouk5MzYpHVLA/22dPK39miQmwitr7UkVe7DYHCqH9FC8haRcSaeSVTEUG9XOUgvgIUE8vUurp0ZThc1w==", "signatures": [{"sig": "MEQCIClcOoS0N1khaWzj3XOqI+6ttPqxqWvjWuvbtmn5WkHTAiBzaY0IbUv9jUPRj6UPbnAcpGARctVkzpU8ZN/+LmTr/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 132241}, "main": "./dist/complex.js", "title": "Complex.js", "types": "./complex.d.ts", "unpkg": "./dist/complex.min.js", "module": "./dist/complex.mjs", "browser": "./dist/complex.min.js", "engines": {"node": "*"}, "exports": {".": {"types": "./complex.d.ts", "import": "./dist/complex.mjs", "require": "./dist/complex.js"}}, "funding": {"url": "https://github.com/sponsors/rawify", "type": "github"}, "gitHead": "97934efc9b1989489ed978dbb52c27ac115501ad", "private": false, "scripts": {"test": "mocha tests/*.js", "build": "crude-build Complex"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/rawify/Complex.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A complex numbers library", "directories": {"test": "tests", "example": "examples"}, "_nodeVersion": "16.20.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*", "crude-build": "^0.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/complex.js_2.2.2_1728238129968_0.6385077840319611", "host": "s3://npm-registry-packages"}}, "2.2.3": {"name": "complex.js", "version": "2.2.3", "keywords": ["complex numbers", "math", "complex", "number", "calculus", "parser", "arithmetic"], "author": {"url": "https://raw.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "complex.js@2.2.3", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://raw.org/article/complex-numbers-in-javascript/", "bugs": {"url": "https://github.com/rawify/Complex.js/issues"}, "dist": {"shasum": "6a285c6915bcd2228d0dc8255c4c24fabc3aeea8", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-2.2.3.tgz", "fileCount": 10, "integrity": "sha512-XnOksGXxhQTvL3LjUgwiOPqL7vF7uikCQE/jpuylNpXmG2LZ+l0z1t6qIlJ2TJVDteXPHhlYd3+mhHOGeTFfsg==", "signatures": [{"sig": "MEUCIFYXL37AfOX6dizmV+0AR45ub3MQEP7yia28ZeUxwNg6AiEAw+AOu7H/urkjuwFVrTj1geofaNZYC4DYSRWbkoSEGPE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 132250}, "main": "./dist/complex.js", "title": "Complex.js", "types": "./complex.d.ts", "unpkg": "./dist/complex.min.js", "module": "./dist/complex.mjs", "browser": "./dist/complex.min.js", "engines": {"node": "*"}, "exports": {".": {"types": "./complex.d.ts", "import": "./dist/complex.mjs", "require": "./dist/complex.js"}}, "funding": {"url": "https://github.com/sponsors/rawify", "type": "github"}, "gitHead": "36012b55afb25eb66e6849775790f2b682905519", "private": false, "scripts": {"test": "mocha tests/*.js", "build": "crude-build Complex"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/rawify/Complex.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A complex numbers library", "directories": {"test": "tests", "example": "examples"}, "_nodeVersion": "16.20.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*", "crude-build": "^0.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/complex.js_2.2.3_1728238460184_0.3685826972665043", "host": "s3://npm-registry-packages"}}, "2.2.4": {"name": "complex.js", "version": "2.2.4", "keywords": ["complex numbers", "math", "complex", "number", "calculus", "parser", "arithmetic"], "author": {"url": "https://raw.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "complex.js@2.2.4", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://raw.org/article/complex-numbers-in-javascript/", "bugs": {"url": "https://github.com/rawify/Complex.js/issues"}, "dist": {"shasum": "d796ef4f2092d6f229351ed4ab0e9f4d743c01ca", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-2.2.4.tgz", "fileCount": 10, "integrity": "sha512-pXNwUld1KC6YJmeNhOWtwCSRQF4384CHeZc6m/YmpRl8vwRaiwFHOxt7RTqbWZtwCu9z48JW5BtUmovZEuovkw==", "signatures": [{"sig": "MEQCIBsnBCUk7JNoV3pWw6JN9oazRjViYa8cboLvV1WhoyBRAiAdXET2+Nzomumw7T6WXh9aR632GSJ2HDb1C+OD7M2WuA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 133374}, "main": "./dist/complex.js", "title": "Complex.js", "types": "./complex.d.ts", "unpkg": "./dist/complex.min.js", "module": "./dist/complex.mjs", "engines": {"node": "*"}, "exports": {".": {"types": "./complex.d.ts", "import": "./dist/complex.mjs", "require": "./dist/complex.js"}}, "funding": {"url": "https://github.com/sponsors/rawify", "type": "github"}, "gitHead": "116eff6f257e7520ed4061a133a4b806e76aafb5", "private": false, "scripts": {"test": "mocha tests/*.js", "build": "crude-build Complex"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/rawify/Complex.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A complex numbers library", "directories": {"test": "tests", "example": "examples"}, "_nodeVersion": "16.20.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*", "crude-build": "^0.0.7"}, "_npmOperationalInternal": {"tmp": "tmp/complex.js_2.2.4_1728410659673_0.8883053554296325", "host": "s3://npm-registry-packages"}}, "2.2.5": {"name": "complex.js", "version": "2.2.5", "keywords": ["complex numbers", "math", "complex", "number", "calculus", "parser", "arithmetic"], "author": {"url": "https://raw.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "complex.js@2.2.5", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://raw.org/article/complex-numbers-in-javascript/", "bugs": {"url": "https://github.com/rawify/Complex.js/issues"}, "dist": {"shasum": "505b9a71a95a3e6dd5687814e04072b5c0fa573d", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-2.2.5.tgz", "fileCount": 10, "integrity": "sha512-U3pSYTZz5Af/xvHgKQkJYHBMGmae7Ms51qqJougCR05YWF1Fihef4LRfOpBFONH2gvPFHMZq2rhx0I44DG23xw==", "signatures": [{"sig": "MEUCIB+x1s+shw/8rVAUQDtu4/fx8iDTHEODmn+8kszYZjHGAiEAksBU+JfQbeD1VmFfRiK6m7G6wjPOFhr+nQy423hYVm4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 132442}, "main": "./dist/complex.js", "title": "Complex.js", "types": "./complex.d.ts", "unpkg": "./dist/complex.min.js", "module": "./dist/complex.mjs", "browser": "./dist/complex.min.js", "engines": {"node": "*"}, "exports": {".": {"types": "./complex.d.ts", "import": "./dist/complex.mjs", "require": "./dist/complex.js"}}, "funding": {"url": "https://github.com/sponsors/rawify", "type": "github"}, "gitHead": "0f868fb81b57e8013c6ad3be6e9f956632508a4c", "private": false, "scripts": {"test": "mocha tests/*.js", "build": "crude-build Complex"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/rawify/Complex.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A complex numbers library", "directories": {"test": "tests", "example": "examples"}, "_nodeVersion": "16.20.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*", "crude-build": "^0.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/complex.js_2.2.5_1728455665735_0.9711670230986655", "host": "s3://npm-registry-packages"}}, "2.3.0": {"name": "complex.js", "version": "2.3.0", "keywords": ["complex numbers", "math", "complex", "number", "calculus", "parser", "arithmetic"], "author": {"url": "https://raw.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "complex.js@2.3.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://raw.org/article/complex-numbers-in-javascript/", "bugs": {"url": "https://github.com/rawify/Complex.js/issues"}, "dist": {"shasum": "c5fb6e329cd853518402a18f499f4fb37f285e87", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-2.3.0.tgz", "fileCount": 10, "integrity": "sha512-wWHzifVdUPbPBhh+ObvpVGIzrAQjTvmnnEJKBfLW5YbyAB6OXQ0r+Q92fByMIrSSlxUuCujqxriJSR6R/kVxPA==", "signatures": [{"sig": "MEYCIQCHfbGrfkLiSg+XqE43UeNwE/s6HCv3DipxKrFtFs6zvwIhALPtx6dwrn3v88it4UugRVjJNBrsFHhw9FnwCoqh54CR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 132593}, "main": "./dist/complex.js", "title": "Complex.js", "types": "./complex.d.ts", "unpkg": "./dist/complex.min.js", "module": "./dist/complex.mjs", "browser": "./dist/complex.min.js", "engines": {"node": "*"}, "exports": {".": {"types": "./complex.d.ts", "import": "./dist/complex.mjs", "require": "./dist/complex.js"}}, "funding": {"url": "https://github.com/sponsors/rawify", "type": "github"}, "gitHead": "a4606f9f4a6071e8976a05244f09d9cfd68b2058", "private": false, "scripts": {"test": "mocha tests/*.js", "build": "crude-build Complex"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/rawify/Complex.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A complex numbers library", "directories": {"test": "tests", "example": "examples"}, "_nodeVersion": "16.20.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*", "crude-build": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/complex.js_2.3.0_1728569201216_0.2343825073527448", "host": "s3://npm-registry-packages"}}, "2.4.0": {"name": "complex.js", "version": "2.4.0", "keywords": ["complex numbers", "math", "complex", "number", "calculus", "parser", "arithmetic"], "author": {"url": "https://raw.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "complex.js@2.4.0", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://raw.org/article/complex-numbers-in-javascript/", "bugs": {"url": "https://github.com/rawify/Complex.js/issues"}, "dist": {"shasum": "1eb10d6c6ae49d29fc91b0c3b1dac27f917ff7cf", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-2.4.0.tgz", "fileCount": 10, "integrity": "sha512-PhTrCnB5SLBicgCtDmKzM0d8YmubCKhpAkrYn7Rk73Pc4m2P61K972QZ+aS3LRETMoBq/ng1UepwC52EN3wC8g==", "signatures": [{"sig": "MEYCIQDot9K2aSsVa5iy5kW1DUYWc6hVluVhgRZbpGtioqerpQIhAN/jtasqidU2XPJL20rlA1DkbjivPSC17usVjVgaaCDU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 136024}, "main": "./dist/complex.js", "title": "Complex.js", "types": "./complex.d.ts", "unpkg": "./dist/complex.min.js", "module": "./dist/complex.mjs", "browser": "./dist/complex.min.js", "engines": {"node": "*"}, "exports": {".": {"types": "./complex.d.ts", "import": "./dist/complex.mjs", "require": "./dist/complex.js"}}, "funding": {"url": "https://github.com/sponsors/rawify", "type": "github"}, "gitHead": "bfc42820b4f484270cac076362a40f22fb07493e", "private": false, "scripts": {"test": "mocha tests/*.js", "build": "crude-build Complex"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/rawify/Complex.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A complex numbers library", "directories": {"test": "tests", "example": "examples"}, "_nodeVersion": "16.20.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*", "crude-build": "^0.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/complex.js_2.4.0_1730128789877_0.3105596795718861", "host": "s3://npm-registry-packages"}}, "2.4.1": {"name": "complex.js", "version": "2.4.1", "keywords": ["complex numbers", "math", "complex", "number", "calculus", "parser", "arithmetic"], "author": {"url": "https://raw.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "complex.js@2.4.1", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "homepage": "https://raw.org/article/complex-numbers-in-javascript/", "bugs": {"url": "https://github.com/rawify/Complex.js/issues"}, "dist": {"shasum": "4ae590551e921e5637e12d6dfd6ebdb14ec1177e", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-2.4.1.tgz", "fileCount": 10, "integrity": "sha512-QNaQnI+uI5mp3vm+i0M7Fkc1eCHAFzbsNJIhIeybP1ZVQquBhW6jx28jFbDCyOlykjDjtfgDMJjus0mbsukrig==", "signatures": [{"sig": "MEYCIQCB0NNJknE19sVLctvsGRsQvgl8SHxHzOBNrz4BEa0kFwIhAIdS10JWCdXWK2j/WOJ5qRSikMRV4vfUtXzBJo8kg0bo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 136306}, "main": "./dist/complex.js", "title": "Complex.js", "types": "./complex.d.ts", "unpkg": "./dist/complex.min.js", "module": "./dist/complex.mjs", "browser": "./dist/complex.min.js", "engines": {"node": "*"}, "exports": {".": {"types": "./complex.d.ts", "import": "./dist/complex.mjs", "require": "./dist/complex.js"}}, "funding": {"url": "https://github.com/sponsors/rawify", "type": "github"}, "gitHead": "6ee03df1bef664715f2efa5c85e6d387ec2d8e4e", "private": false, "scripts": {"test": "mocha tests/*.js", "build": "crude-build Complex"}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/rawify/Complex.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A complex numbers library", "directories": {"test": "tests", "example": "examples"}, "_nodeVersion": "16.20.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "*", "crude-build": "^0.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/complex.js_2.4.1_1730309482603_0.502981790680672", "host": "s3://npm-registry-packages"}}, "2.4.2": {"name": "complex.js", "title": "Complex.js", "version": "2.4.2", "homepage": "https://raw.org/article/complex-numbers-in-javascript/", "bugs": {"url": "https://github.com/rawify/Complex.js/issues"}, "description": "A complex numbers library", "keywords": ["complex numbers", "math", "complex", "number", "calculus", "parser", "arithmetic"], "private": false, "main": "./dist/complex.js", "module": "./dist/complex.mjs", "types": "./complex.d.ts", "browser": "./dist/complex.min.js", "unpkg": "./dist/complex.min.js", "exports": {".": {"types": "./complex.d.ts", "require": "./dist/complex.js", "import": "./dist/complex.mjs"}}, "repository": {"type": "git", "url": "git+ssh://**************/rawify/Complex.js.git"}, "funding": {"type": "github", "url": "https://github.com/sponsors/rawify"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://raw.org/"}, "license": "MIT", "engines": {"node": "*"}, "directories": {"example": "examples", "test": "tests"}, "scripts": {"build": "crude-build Complex", "test": "mocha tests/*.js"}, "devDependencies": {"crude-build": "^0.1.2", "mocha": "*"}, "gitHead": "59b17a863cfc9b544dedfd6d0f9327b394ba5ee6", "_id": "complex.js@2.4.2", "_nodeVersion": "16.20.1", "_npmVersion": "8.19.4", "dist": {"integrity": "sha512-qtx7HRhPGSCBtGiST4/WGHuW+zeaND/6Ld+db6PbrulIB1i2Ev/2UPiqcmpQNPSyfBKraC0EOvOKCB5dGZKt3g==", "shasum": "76f260a9e7e232d8ad26348484a9b128c13fcc9a", "tarball": "https://registry.npmjs.org/complex.js/-/complex.js-2.4.2.tgz", "fileCount": 10, "unpackedSize": 136100, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBGxl1cpBRMS/yrSa8DyvqnKFUjlTw6rvzb+JoHUuyvoAiAVE2nHdiRvx9nSpOjSR5wf2qQjvLCSw4es3OUgIlhr0Q=="}]}, "_npmUser": {"name": "infusion", "email": "<EMAIL>"}, "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/complex.js_2.4.2_1730824743359_0.8767894404231737"}, "_hasShrinkwrap": false}}, "time": {"created": "2015-06-22T01:48:31.487Z", "modified": "2024-11-05T16:39:03.745Z", "1.0.0": "2015-06-22T01:48:31.487Z", "1.1.0": "2015-06-22T10:05:34.354Z", "1.2.0": "2015-07-02T00:00:55.429Z", "1.2.2": "2015-07-03T13:58:30.576Z", "1.3.0": "2015-07-03T14:17:23.630Z", "1.4.0": "2015-07-03T14:23:21.035Z", "1.5.0": "2015-07-13T19:57:13.952Z", "1.6.0": "2016-01-29T00:51:55.725Z", "1.7.0": "2016-02-03T16:42:36.259Z", "1.8.0": "2016-02-03T17:14:57.104Z", "1.8.2": "2016-02-04T20:39:29.325Z", "1.8.5": "2016-02-04T23:51:16.981Z", "1.9.0": "2016-02-06T00:40:12.069Z", "1.9.1": "2016-02-07T00:34:10.819Z", "1.9.2": "2016-02-07T13:55:31.157Z", "1.9.3": "2016-02-09T11:22:01.445Z", "1.9.6": "2016-02-11T03:32:39.672Z", "2.0.0": "2016-02-13T14:44:14.275Z", "2.0.1": "2016-02-14T03:38:40.684Z", "2.0.2": "2017-05-28T21:01:18.754Z", "2.0.3": "2017-05-28T21:29:45.078Z", "2.0.4": "2017-06-14T08:53:46.784Z", "2.0.5": "2018-03-29T19:40:24.398Z", "2.0.6": "2018-03-29T21:03:23.311Z", "2.0.7": "2018-04-13T22:53:53.154Z", "2.0.9": "2018-04-29T06:47:25.367Z", "2.0.10": "2018-04-29T08:48:22.222Z", "2.0.11": "2018-07-04T11:14:44.294Z", "2.0.12": "2021-02-27T19:58:05.619Z", "2.0.13": "2021-05-12T13:04:56.376Z", "2.0.14": "2021-07-04T11:17:36.353Z", "2.0.15": "2021-07-04T11:59:47.539Z", "2.1.0": "2022-03-27T17:25:18.344Z", "2.1.1": "2022-04-12T18:21:32.050Z", "2.2.0": "2024-10-04T16:56:05.392Z", "2.2.1": "2024-10-05T13:26:54.339Z", "2.2.2": "2024-10-06T18:08:50.157Z", "2.2.3": "2024-10-06T18:14:20.386Z", "2.2.4": "2024-10-08T18:04:19.855Z", "2.2.5": "2024-10-09T06:34:25.952Z", "2.3.0": "2024-10-10T14:06:41.379Z", "2.4.0": "2024-10-28T15:19:50.080Z", "2.4.1": "2024-10-30T17:31:22.761Z", "2.4.2": "2024-11-05T16:39:03.564Z"}, "bugs": {"url": "https://github.com/rawify/Complex.js/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://raw.org/"}, "license": "MIT", "homepage": "https://raw.org/article/complex-numbers-in-javascript/", "keywords": ["complex numbers", "math", "complex", "number", "calculus", "parser", "arithmetic"], "repository": {"type": "git", "url": "git+ssh://**************/rawify/Complex.js.git"}, "description": "A complex numbers library", "maintainers": [{"name": "infusion", "email": "<EMAIL>"}], "readme": "# Complex.js - ℂ in JavaScript\n\n[![NPM Package](https://img.shields.io/npm/v/complex.js.svg?style=flat)](https://npmjs.org/package/complex.js \"View this project on npm\")\n[![MIT license](http://img.shields.io/badge/license-MIT-brightgreen.svg)](http://opensource.org/licenses/MIT)\n\nComplex.js is a well tested JavaScript library to work with [complex number arithmetic](https://raw.org/book/analysis/complex-numbers/) in JavaScript. It implements every elementary complex number manipulation function and the API is intentionally similar to [Fraction.js](https://github.com/rawify/Fraction.js). Furthermore, it's the basis of [Polynomial.js](https://github.com/infusion/Polynomial.js) and [Math.js](https://github.com/josdejong/mathjs).\n\n\n## Examples\n\n\n```js\nlet Complex = require('complex.js');\n\nlet c = new Complex(\"99.3+8i\");\nc.mul({re: 3, im: 9}).div(4.9).sub(3, 2);\n```\n\nA classical use case for complex numbers is solving quadratic equations `ax² + bx + c = 0` for all `a, b, c ∈ ℝ`:\n\n```js\n\nfunction quadraticRoot(a, b, c) {\n  let sqrt = Complex(b * b - 4 * a * c).sqrt()\n  let x1 = Complex(-b).add(sqrt).div(2 * a)\n  let x2 = Complex(-b).sub(sqrt).div(2 * a)\n  return {x1, x2}\n}\n\n// quadraticRoot(1, 4, 5) -> -2 ± i\n```\n\nFor cubic roots have a look at [RootFinder](https://github.com/rawify/RootFinder.js) which uses Complex.js.\n\n## Parser\n\n\nAny function (see below) as well as the constructor of the *Complex* class parses its input like this.\n\nYou can pass either Objects, Doubles or Strings.\n\n### Objects\n\n```javascript\nnew Complex({re: real, im: imaginary});\nnew Complex({arg: angle, abs: radius});\nnew Complex({phi: angle, r: radius});\nnew Complex([real, imaginary]); // Vector/Array syntax\n```\nIf there are other attributes on the passed object, they're not getting preserved and have to be merged manually.\n\n**Note:** Object attributes have to be of type Number to avoid undefined behavior.\n\n### Doubles\n\n```javascript\nnew Complex(55.4);\n```\n\n### Strings\n\n```javascript\nnew Complex(\"123.45\");\nnew Complex(\"15+3i\");\nnew Complex(\"i\");\n```\n\n### Two arguments\n\n```javascript\nnew Complex(3, 2); // 3+2i\n```\n\n## Attributes\n\n\nEvery complex number object exposes its real and imaginary part as attribute `re` and `im`:\n\n```javascript\nlet c = new Complex(3, 2);\n\nconsole.log(\"Real part:\", c.re); // 3\nconsole.log(\"Imaginary part:\", c.im); // 2\n```\n\n## Functions\n\n\nComplex sign()\n---\nReturns the complex sign, defined as the complex number normalized by it's absolute value\n\nComplex add(n)\n---\nAdds another complex number\n\nComplex sub(n)\n---\nSubtracts another complex number\n\nComplex mul(n)\n---\nMultiplies the number with another complex number\n\nComplex div(n)\n---\nDivides the number by another complex number\n\nComplex pow(exp)\n---\nReturns the number raised to the complex exponent (Note: `Complex.ZERO.pow(0) = Complex.ONE` by convention)\n\nComplex sqrt()\n---\nReturns the complex square root of the number\n\nComplex exp(n)\n---\nReturns `e^n` with complex exponent `n`.\n\nComplex log()\n---\nReturns the natural logarithm (base `E`) of the actual complex number\n\n_Note:_ The logarithm to a different base can be calculated with `z.log().div(Math.log(base))`.\n\ndouble abs()\n---\nCalculates the magnitude of the complex number\n\ndouble arg()\n---\nCalculates the angle of the complex number\n\nComplex inverse()\n---\nCalculates the multiplicative inverse of the complex number (1 / z)\n\nComplex conjugate()\n---\nCalculates the conjugate of the complex number (multiplies the imaginary part with -1)\n\nComplex neg()\n---\nNegates the number (multiplies both the real and imaginary part with -1) in order to get the additive inverse\n\nComplex floor([places=0])\n---\nFloors the complex number parts towards zero\n\nComplex ceil([places=0])\n---\nCeils the complex number parts off zero\n\nComplex round([places=0])\n---\nRounds the complex number parts\n\nboolean equals(n)\n---\nChecks if both numbers are exactly the same, if both numbers are infinite they\nare considered **not** equal.\n\nboolean isNaN()\n---\nChecks if the given number is not a number\n\nboolean isFinite()\n---\nChecks if the given number is finite\n\nComplex clone()\n---\nReturns a new Complex instance with the same real and imaginary properties\n\nArray toVector()\n---\nReturns a Vector of the actual complex number with two components\n\nString toString()\n---\nReturns a string representation of the actual number. As of v1.9.0 the output is a bit more human readable\n\n```javascript\nnew Complex(1, 2).toString(); // 1 + 2i\nnew Complex(0, 1).toString(); // i\nnew Complex(9, 0).toString(); // 9\nnew Complex(1, 1).toString(); // 1 + i\n```\n\ndouble valueOf()\n---\nReturns the real part of the number if imaginary part is zero. Otherwise `null`\n\n\n## Trigonometric functions\n\nThe following trigonometric functions are defined on Complex.js:\n\n| Trig | Arcus | Hyperbolic | Area-Hyperbolic |\n|------|-------|------------|------------------|\n| sin()  | asin()  | sinh()       | asinh()            |\n| cos()  | acos()  | cosh()       | acosh()            |\n| tan()  | atan()  | tanh()       | atanh()            |\n| cot()  | acot()  | coth()       | acoth()            |\n| sec()  | asec()  | sech()       | asech()            |\n| csc()  | acsc()  | csch()       | acsch()            |\n\n\n## Geometric Equivalence\n\n\nComplex numbers can also be seen as a vector in the 2D space. Here is a simple overview of basic operations and how to implement them with complex.js:\n\nNew vector\n---\n```js\nlet v1 = new Complex(1, 0);\nlet v2 = new Complex(1, 1);\n```\n\nScale vector\n---\n```js\nscale(v1, factor):= v1.mul(factor)\n```\n\nVector norm\n---\n```js\nnorm(v):= v.abs()\n```\n\nTranslate vector\n---\n```js\ntranslate(v1, v2):= v1.add(v2)\n```\n\nRotate vector around center\n---\n```js\nrotate(v, angle):= v.mul({abs: 1, arg: angle})\n```\n\nRotate vector around a point\n---\n```js\nrotate(v, p, angle):= v.sub(p).mul({abs: 1, arg: angle}).add(p)\n```\n\nDistance to another vector\n---\n```js\ndistance(v1, v2):= v1.sub(v2).abs()\n```\n\n## Constants\n\n\nComplex.ZERO\n---\nA complex zero value (south pole on the Riemann Sphere)\n\nComplex.ONE\n---\nA complex one instance\n\nComplex.INFINITY\n---\nA complex infinity value (north pole on the Riemann Sphere)\n\nComplex.NAN\n---\nA complex NaN value (not on the Riemann Sphere)\n\nComplex.I\n---\nAn imaginary number i instance\n\nComplex.PI\n---\nA complex PI instance\n\nComplex.E\n---\nA complex euler number instance\n\nComplex.EPSILON\n---\nA small epsilon value used for `equals()` comparison in order to circumvent double imprecision.\n\n\n## Installation\n\nInstalling complex.js is as easy as cloning this repo or use one of the following command:\n\n\n```bash\nnpm install complex.js\n```\n\n## Using Complex.js with the browser\n\n```html\n<script src=\"complex.min.js\"></script>\n<script>\n    console.log(Complex(\"4+3i\"));\n</script>\n```\n\n\n\n## Coding Style\n\nAs every library I publish, Complex.js is also built to be as small as possible after compressing it with Google Closure Compiler in advanced mode. Thus the coding style orientates a little on maxing-out the compression rate. Please make sure you keep this style if you plan to extend the library.\n\n## Building the library\n\nAfter cloning the Git repository run:\n\n```\nnpm install\nnpm run build\n```\n\n## Run a test\n\nTesting the source against the shipped test suite is as easy as\n\n```\nnpm run test\n```\n\n## Copyright and Licensing\n\nCopyright (c) 2024, [Robert Eisele](https://raw.org/)\nLicensed under the MIT license.\n", "readmeFilename": "README.md", "users": {"rreusser": true, "snowdream": true, "xieranmaya": true, "johnsmithcoder": true}}