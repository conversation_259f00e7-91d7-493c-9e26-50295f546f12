{"source": 1093733, "name": "safe-eval", "dependency": "safe-eval", "title": "Sandbox Breakout / Arbitrary Code Execution in safe-eval", "url": "https://github.com/advisories/GHSA-hrpq-r399-whgw", "severity": "critical", "versions": ["0.0.0", "0.1.0", "0.2.0", "0.3.0", "0.4.0", "0.4.1"], "vulnerableVersions": ["0.0.0", "0.1.0", "0.2.0", "0.3.0", "0.4.0", "0.4.1"], "cwe": ["CWE-94"], "cvss": {"score": 9.8, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H"}, "range": "<=0.4.1", "id": "IqPdMuA1iFH22BHrQqttrK5sa3em78L2uomsxQqFccKjW8mdf8yKW7vVxO+x+rARHHvC5+hD+cBSEP7DBXjtxg=="}