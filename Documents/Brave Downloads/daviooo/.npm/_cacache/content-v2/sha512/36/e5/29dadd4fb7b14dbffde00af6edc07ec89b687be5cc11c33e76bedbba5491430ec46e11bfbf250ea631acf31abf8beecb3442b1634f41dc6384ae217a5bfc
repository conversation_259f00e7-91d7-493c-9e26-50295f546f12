{"_id": "javascript-natural-sort", "_rev": "11-63489a0edc4b975d7aacd7d34ada53c1", "name": "javascript-natural-sort", "description": "Natural Sort algorithm for Javascript - Version 0.7 - Released under MIT license", "dist-tags": {"latest": "0.7.1"}, "versions": {"0.7.0": {"name": "javascript-natural-sort", "version": "0.7.0", "description": "Natural Sort algorithm for Javascript - Version 0.7 - Released under MIT license", "main": "naturalSort.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/Bill4Time/javascript-natural-sort.git"}, "keywords": ["natural", "sort", "javascript", "array", "sort", "sorting"], "author": {"name": "<PERSON>", "url": "based on chunking idea from <PERSON>, packaged by @khous of Bill4Time"}, "license": "MIT", "bugs": {"url": "https://github.com/Bill4Time/javascript-natural-sort/issues"}, "homepage": "https://github.com/Bill4Time/javascript-natural-sort", "_id": "javascript-natural-sort@0.7.0", "dist": {"shasum": "92a3ab8a950e33911be9ee194fbc5de47c2fcfe6", "tarball": "https://registry.npmjs.org/javascript-natural-sort/-/javascript-natural-sort-0.7.0.tgz", "integrity": "sha512-832wQntlzMV+w77GrxwxHaU4rS/6XQGFdE9VgqHI9fuf2gLAt9lPFMpV4MXDX/wvc4AmDnYikRFScLtrkbav1Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGZIudE5f3j2V6o8YkeBarWAbY2Y7sI8NNrUqZpp9aK4AiEArhvhAk5gBdvfGonsq1k4CK+vz5cLfxqjedu2cvZ297o="}]}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "kyle<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "kyle<PERSON><PERSON>", "email": "<EMAIL>"}]}, "0.7.1": {"name": "javascript-natural-sort", "version": "0.7.1", "description": "Natural Sort algorithm for Javascript - Version 0.7 - Released under MIT license", "main": "naturalSort.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/Bill4Time/javascript-natural-sort.git"}, "keywords": ["natural", "sort", "javascript", "array", "sort", "sorting"], "author": {"name": "<PERSON>", "url": "based on chunking idea from <PERSON>, packaged by @khous of Bill4Time"}, "license": "MIT", "bugs": {"url": "https://github.com/Bill4Time/javascript-natural-sort/issues"}, "homepage": "https://github.com/Bill4Time/javascript-natural-sort", "_id": "javascript-natural-sort@0.7.1", "dist": {"shasum": "f9e2303d4507f6d74355a73664d1440fb5a0ef59", "tarball": "https://registry.npmjs.org/javascript-natural-sort/-/javascript-natural-sort-0.7.1.tgz", "integrity": "sha512-nO6jcEfZWQXDhOiBtG2KvKyEptz7RVbpGP4vTD2hLBdmNQSsCiicO2Ioinv6UI4y9ukqnBpy+XZ9H6uLNgJTlw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEG6mYkDnfifpYrFH4pzYEx2enpK6+GYB0NbnTls+khiAiBoes3FlMuVXpacN24yKjkuZ4EA4KDxrl5FpOJV7vYhvg=="}]}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "kyle<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "kyle<PERSON><PERSON>", "email": "<EMAIL>"}]}}, "readme": "### Simple numerics\r\n\r\n```javascript\r\n>>> ['10',9,2,'1','4'].sort(naturalSort)\r\n['1',2,'4',9,'10']\r\n```\r\n\r\n### Floats\r\n\r\n```javascript\r\n>>> ['10.0401',10.022,10.042,'10.021999'].sort(naturalSort)\r\n['10.021999',10.022,'10.0401',10.042]\r\n```\r\n\r\n### Float & decimal notation\r\n\r\n```javascript\r\n>>> ['10.04f','10.039F','10.038d','10.037D'].sort(naturalSort)\r\n['10.037D','10.038d','10.039F','10.04f']\r\n```\r\n\r\n### Scientific notation\r\n\r\n```javascript\r\n>>> ['1.528535047e5','1.528535047e7','1.528535047e3'].sort(naturalSort)\r\n['1.528535047e3','1.528535047e5','1.528535047e7']\r\n```\r\n\r\n### IP addresses\r\n\r\n```javascript\r\n>>> ['*************','***********','***********'].sort(naturalSort)\r\n['***********','*************','***********']\r\n```\r\n\r\n### Filenames\r\n\r\n```javascript\r\n>>> ['car.mov','01alpha.sgi','001alpha.sgi','my.string_41299.tif'].sort(naturalSort)\r\n['001alpha.sgi','01alpha.sgi','car.mov','my.string_41299.tif'\r\n```\r\n\r\n### Dates\r\n\r\n```javascript\r\n>>> ['10/12/2008','10/11/2008','10/11/2007','10/12/2007'].sort(naturalSort)\r\n['10/11/2007', '10/12/2007', '10/11/2008', '10/12/2008']\r\n```\r\n\r\n### Money\r\n\r\n```javascript\r\n>>> ['$10002.00','$10001.02','$10001.01'].sort(naturalSort)\r\n['$10001.01','$10001.02','$10002.00']\r\n```\r\n\r\n### Movie Titles\r\n\r\n```javascript\r\n>>> ['1 Title - The Big Lebowski','1 Title - Gattaca','1 Title - Last Picture Show'].sort(naturalSort)\r\n['1 Title - Gattaca','1 Title - Last Picture Show','1 Title - The Big Lebowski']\r\n```\r\n\r\n### By default - case-sensitive sorting\r\n\r\n```javascript\r\n>>> ['a', 'B'].sort(naturalSort);\r\n['B', 'a']\r\n```\r\n\r\n### To enable case-insensitive sorting\r\n```javascript\r\n>>> naturalSort.insensitive = true;\r\n>>> ['a', 'B'].sort(naturalSort);\r\n['a', 'B']\r\n```\r\n", "maintainers": [{"name": "kyle<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "overset", "email": "<EMAIL>"}], "time": {"modified": "2022-06-19T03:53:01.099Z", "created": "2014-01-01T00:16:50.104Z", "0.7.0": "2014-01-01T00:16:50.715Z", "0.7.1": "2014-01-02T17:54:40.720Z"}, "author": {"name": "<PERSON>", "url": "based on chunking idea from <PERSON>, packaged by @khous of Bill4Time"}, "repository": {"type": "git", "url": "https://github.com/Bill4Time/javascript-natural-sort.git"}, "users": {"felixfbecker": true}, "homepage": "https://github.com/Bill4Time/javascript-natural-sort", "keywords": ["natural", "sort", "javascript", "array", "sort", "sorting"], "bugs": {"url": "https://github.com/Bill4Time/javascript-natural-sort/issues"}, "license": "MIT", "readmeFilename": "README.md"}