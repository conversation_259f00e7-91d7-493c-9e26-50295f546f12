{"_id": "fi<PERSON>t", "_rev": "123-db52e3f12b7e8a496045bf5840962d5c", "name": "fi<PERSON>t", "dist-tags": {"beta": "1.6.0-beta.0", "latest": "1.8.1"}, "versions": {"1.0.1": {"name": "fi<PERSON>t", "version": "1.0.1", "keywords": ["fi<PERSON>t", "ascii", "art", "banner", "ansi"], "author": {"url": "http://patorjk.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "figlet@1.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/patorjk/figlet.js/issues"}, "dist": {"shasum": "412708e601bd157decca06c34dca023038e6f8de", "tarball": "https://registry.npmjs.org/figlet/-/figlet-1.0.1.tgz", "integrity": "sha512-2p8CgR+hOJ1oNmA/W95rvg4tA7tZzifGUtpT4iRZpEXPgrIPaHsbj4Ps8m1db7wj93On3Nt5IekCkes3j097Rw==", "signatures": [{"sig": "MEYCIQDapn8lfZzOzAKCBh0TQ0zDgwjbjUTViUvxZ+J/RWG7CQIhAJ9kzsHkMTJG4rE+MrGHsJ/1vJvU/HK4yYBETKu5Vy/9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-figlet.js", "_from": ".", "engines": {"node": ">= 0.4.0"}, "scripts": {}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/MIT", "type": "MIT"}], "repository": {"url": "https://github.com/patorjk/figlet.js.git", "type": "git"}, "_npmVersion": "1.2.18", "description": "Creates ASCII Art from text. A full implementation of the FIGfont spec.", "directories": {}, "dependencies": {}, "devDependencies": {}}, "1.0.2": {"name": "fi<PERSON>t", "version": "1.0.2", "keywords": ["fi<PERSON>t", "ascii", "art", "banner", "ansi"], "author": {"url": "http://patorjk.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "figlet@1.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/patorjk/figlet.js/issues"}, "dist": {"shasum": "0ab7066cd4f92ec34710a2a6346b47a79224eaae", "tarball": "https://registry.npmjs.org/figlet/-/figlet-1.0.2.tgz", "noattachment": true}, "main": "./lib/node-figlet.js", "_from": ".", "engines": {"node": ">= 0.4.0"}, "scripts": {}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/MIT", "type": "MIT"}], "repository": {"url": "https://github.com/patorjk/figlet.js.git", "type": "git"}, "_npmVersion": "1.2.18", "description": "Creates ASCII Art from text. A full implementation of the FIGfont spec.", "directories": {}, "dependencies": {}, "devDependencies": {}}, "1.0.3": {"name": "fi<PERSON>t", "version": "1.0.3", "keywords": ["fi<PERSON>t", "ascii", "art", "banner", "ansi"], "author": {"url": "http://patorjk.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "figlet@1.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/patorjk/figlet.js/issues"}, "dist": {"shasum": "3aed01a2945038f61bcd6a6a752c524c4982dc66", "tarball": "https://registry.npmjs.org/figlet/-/figlet-1.0.3.tgz", "integrity": "sha512-kZ6CP7gsHQCSJD/kYk36XpH2aWaHcVNqr07hvyndeCdJCIWF+a84wZyHnk9HZR8Z23m3nJZayQ6gTtOXrWceLg==", "signatures": [{"sig": "MEYCIQDsOARqWxYw1XG6E299a7c5rSl5EtBNfuPzm+awXPAPSgIhAPchxe+hFI91s4FBhsovSpOI0tmHnNjqRj9mY6RpL8EU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-figlet.js", "_from": ".", "engines": {"node": ">= 0.4.0"}, "scripts": {}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/MIT", "type": "MIT"}], "repository": {"url": "https://github.com/patorjk/figlet.js.git", "type": "git"}, "_npmVersion": "1.2.18", "description": "Creates ASCII Art from text. A full implementation of the FIGfont spec.", "directories": {}, "dependencies": {}, "devDependencies": {}}, "1.0.5": {"name": "fi<PERSON>t", "version": "1.0.5", "keywords": ["fi<PERSON>t", "ascii", "art", "banner", "ansi"], "author": {"url": "http://patorjk.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "figlet@1.0.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/patorjk/figlet.js", "bugs": {"url": "https://github.com/patorjk/figlet.js/issues"}, "dist": {"shasum": "24ff4cade245984a47727022acf40b5df40e63cc", "tarball": "https://registry.npmjs.org/figlet/-/figlet-1.0.5.tgz", "integrity": "sha512-94Yy2e0tcAzTYBJ+qA9fdM3GZ2NPtV8wOfK/aAYPZ/BHc/NzBzo/5JQqaDZnoC1/qoBb36ThILDKeincF8VYhw==", "signatures": [{"sig": "MEQCIEeNV/aM2kdNdua4mUdcQNycw/za6KZV1jDZtkrwrcqQAiAOv7MwOL2k0Q1YoDa2hVs5ssiIjGN5jGur/GY+vZJQSQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-figlet.js", "_from": ".", "engines": {"node": ">= 0.4.0"}, "scripts": {}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/MIT", "type": "MIT"}], "repository": {"url": "https://github.com/patorjk/figlet.js.git", "type": "git"}, "_npmVersion": "1.3.21", "description": "Creates ASCII Art from text. A full implementation of the FIGfont spec.", "directories": {}, "dependencies": {}, "devDependencies": {}}, "1.0.6": {"name": "fi<PERSON>t", "version": "1.0.6", "keywords": ["fi<PERSON>t", "ascii", "art", "banner", "ansi"], "author": {"url": "http://patorjk.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "figlet@1.0.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/patorjk/figlet.js", "bugs": {"url": "https://github.com/patorjk/figlet.js/issues"}, "dist": {"shasum": "8893599945ac6df57d9bd04aa03d831615a2e3f8", "tarball": "https://registry.npmjs.org/figlet/-/figlet-1.0.6.tgz", "integrity": "sha512-X3FaReN62IZhUCpGJDW2zd8tAJtQzRxAFp+MHxPrFM+koOWdD8CCN1QaH04PxwqFlL8N+s77vMNaRdD2gb5FTw==", "signatures": [{"sig": "MEUCIG6ZdBd25hfQCbXERMio9iAxNU7KrKBLXQDZKKV9EJrvAiEAwee3mTWL0yEWzv/QM+xHeCw/jsgWv218pk5/1a501S8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-figlet.js", "_from": ".", "engines": {"node": ">= 0.4.0"}, "scripts": {}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/MIT", "type": "MIT"}], "repository": {"url": "https://github.com/patorjk/figlet.js.git", "type": "git"}, "_npmVersion": "1.3.21", "description": "Creates ASCII Art from text. A full implementation of the FIGfont spec.", "directories": {}, "dependencies": {}, "devDependencies": {}}, "1.0.7": {"name": "fi<PERSON>t", "version": "1.0.7", "keywords": ["fi<PERSON>t", "ascii", "art", "banner", "ansi"], "author": {"url": "http://patorjk.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "figlet@1.0.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/patorjk/figlet.js", "bugs": {"url": "https://github.com/patorjk/figlet.js/issues"}, "dist": {"shasum": "e4ba113124e7e68b1577726ddf20b4d79232e1cb", "tarball": "https://registry.npmjs.org/figlet/-/figlet-1.0.7.tgz", "integrity": "sha512-qDsaCt4GT5oOCe/8nTo/5Bqn9PCI0bjHuNsNy0nTXwhydjz3RCsCW8ymQ5n8HzFghZ3ZzrQM/H4AZARTxvLaZg==", "signatures": [{"sig": "MEQCIEL9Z0nMJbZSZb/S7EcMySilk1lMFrWU2lzHOlabf85XAiAJIfsp7Sa+Wl1cS4Jk9Ovm7RJGkgNRpMzIfM+b061rLw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-figlet.js", "_from": ".", "engines": {"node": ">= 0.4.0"}, "scripts": {}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/MIT", "type": "MIT"}], "repository": {"url": "https://github.com/patorjk/figlet.js.git", "type": "git"}, "_npmVersion": "1.3.21", "description": "Creates ASCII Art from text. A full implementation of the FIGfont spec.", "directories": {}, "dependencies": {}, "devDependencies": {}}, "1.0.8": {"name": "fi<PERSON>t", "version": "1.0.8", "keywords": ["fi<PERSON>t", "ascii", "art", "banner", "ansi"], "author": {"url": "http://patorjk.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "figlet@1.0.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/patorjk/figlet.js", "bugs": {"url": "https://github.com/patorjk/figlet.js/issues"}, "dist": {"shasum": "de9566f656c2ffca98879d0cc73203e374022239", "tarball": "https://registry.npmjs.org/figlet/-/figlet-1.0.8.tgz", "integrity": "sha512-4eeKKrygrgTRcm0epf+6EfcGvDO7NOMO+MFIi19qxqCkY0gxS6DTsGljWryfP6i0ZQnhqPNBfj9rQyVv5ax3oA==", "signatures": [{"sig": "MEUCIQDyXNhE5+zAKr6ZbdzoC9iFh23NJ63z3T8a0sDz2a/+FwIgZXzlbknHw8JztipaZKulv89BQoXnrEPsurUQEaBUHzg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-figlet.js", "_from": ".", "engines": {"node": ">= 0.4.0"}, "scripts": {}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/MIT", "type": "MIT"}], "repository": {"url": "https://github.com/patorjk/figlet.js.git", "type": "git"}, "_npmVersion": "1.3.21", "description": "Creates ASCII Art from text. A full implementation of the FIGfont spec.", "directories": {}, "dependencies": {"optimist": "~0.6.0"}, "devDependencies": {"grunt": "~0.4.2", "grunt-contrib-clean": "~0.4.0", "grunt-contrib-jshint": "~0.6.0", "grunt-contrib-uglify": "~0.2.0", "grunt-contrib-nodeunit": "~0.2.0"}}, "1.0.9": {"name": "fi<PERSON>t", "version": "1.0.9", "keywords": ["fi<PERSON>t", "ascii", "art", "banner", "ansi"], "author": {"url": "http://patorjk.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "figlet@1.0.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/patorjk/figlet.js", "bugs": {"url": "https://github.com/patorjk/figlet.js/issues"}, "dist": {"shasum": "5007a3aa3b9e9abf0143d9dca7a9686e2e02ee0e", "tarball": "https://registry.npmjs.org/figlet/-/figlet-1.0.9.tgz", "integrity": "sha512-N5SCpi7FehmFuBfw1ixDHfKsiUoPvVjYFPLsFrm57q7LmWQVfWfQkPVUwTbp0bQ4aXfVm9GQRlNNgcpcRZyPRQ==", "signatures": [{"sig": "MEQCIC8EBwMdWGN0WlztycbBlnSJgpRjl22xVrxrkcIRE9dmAiBPOM+EjRvfalLB8gtbLAYU+S/WZbE3jwN8fpugAJgB/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-figlet.js", "_from": ".", "_shasum": "5007a3aa3b9e9abf0143d9dca7a9686e2e02ee0e", "engines": {"node": ">= 0.4.0"}, "scripts": {}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/MIT", "type": "MIT"}], "repository": {"url": "https://github.com/patorjk/figlet.js.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Creates ASCII Art from text. A full implementation of the FIGfont spec.", "directories": {}, "dependencies": {}, "devDependencies": {"grunt": "~0.4.2", "grunt-contrib-clean": "~0.4.0", "grunt-contrib-jshint": "~0.6.0", "grunt-contrib-uglify": "~0.2.0", "grunt-contrib-nodeunit": "~0.2.0"}}, "1.0.10": {"name": "fi<PERSON>t", "version": "1.0.10", "keywords": ["fi<PERSON>t", "ascii", "art", "banner", "ansi"], "author": {"url": "http://patorjk.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "figlet@1.0.10", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/patorjk/figlet.js", "bugs": {"url": "https://github.com/patorjk/figlet.js/issues"}, "dist": {"shasum": "cebd2f4131a507cce84e42ba370136a73d7eb5dc", "tarball": "https://registry.npmjs.org/figlet/-/figlet-1.0.10.tgz", "integrity": "sha512-kBQ581ndOLzISy15XQx27QHRiBzihv7AgxZTCR+YLkUDwFzReABoMWs407XUb/XBwz4V1OY1jviqDW6IgsNGMQ==", "signatures": [{"sig": "MEUCIC3I4SIAdAZo1qi2LztQzaIGiro7YKnqsLCIJSdRz30AAiEA2MJcPm9H0T4ORlWaZqm+QZlni4GnzK4z4PTvSMvIaLc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-figlet.js", "_from": ".", "_shasum": "cebd2f4131a507cce84e42ba370136a73d7eb5dc", "engines": {"node": ">= 0.4.0"}, "scripts": {}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/MIT", "type": "MIT"}], "repository": {"url": "https://github.com/patorjk/figlet.js.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Creates ASCII Art from text. A full implementation of the FIGfont spec.", "directories": {}, "dependencies": {}, "devDependencies": {"async": "~0.9.0", "grunt": "~0.4.2", "grunt-contrib-clean": "~0.4.0", "grunt-contrib-jshint": "~0.6.0", "grunt-contrib-uglify": "~0.2.0", "grunt-contrib-nodeunit": "~0.2.0"}}, "1.1.0": {"name": "fi<PERSON>t", "version": "1.1.0", "keywords": ["fi<PERSON>t", "ascii", "art", "banner", "ansi"], "author": {"url": "http://patorjk.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "figlet@1.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/patorjk/figlet.js", "bugs": {"url": "https://github.com/patorjk/figlet.js/issues"}, "dist": {"shasum": "944acd2765252127a946158a949b2c200102b375", "tarball": "https://registry.npmjs.org/figlet/-/figlet-1.1.0.tgz", "integrity": "sha512-S1v+L7S0yGpib47263pBnPH1DtOernMgpPYy+U2UOCKSu37aCCKNpMdwN3Li+NxWbaYgCcGB2p1YhkleOeivOA==", "signatures": [{"sig": "MEUCIGbWA0yOU3s5t4yra7B8cRVxfnpQ9mkwr4rDEYNFBc6CAiEA2riQNedRCXq8/dAQ86Rooj2ffcGH3UgS9IfhFfI1YzA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-figlet.js", "_from": ".", "_shasum": "944acd2765252127a946158a949b2c200102b375", "engines": {"node": ">= 0.4.0"}, "scripts": {}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/MIT", "type": "MIT"}], "repository": {"url": "https://github.com/patorjk/figlet.js.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Creates ASCII Art from text. A full implementation of the FIGfont spec.", "directories": {}, "dependencies": {}, "devDependencies": {"async": "~0.9.0", "grunt": "~0.4.2", "grunt-contrib-clean": "~0.4.0", "grunt-contrib-jshint": "~0.6.0", "grunt-contrib-uglify": "~0.2.0", "grunt-contrib-nodeunit": "~0.2.0"}}, "1.1.1": {"name": "fi<PERSON>t", "version": "1.1.1", "keywords": ["fi<PERSON>t", "ascii", "art", "banner", "ansi"], "author": {"url": "http://patorjk.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "figlet@1.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/patorjk/figlet.js", "bugs": {"url": "https://github.com/patorjk/figlet.js/issues"}, "dist": {"shasum": "501a6d60e6eee22826522ec11b611fc1282d0f0d", "tarball": "https://registry.npmjs.org/figlet/-/figlet-1.1.1.tgz", "integrity": "sha512-qiL6DcaLFRtntNxSn/CPcp49Xo2KNbUi3BL0snZBpXnsMzAL/QWGsA+SD5iTcbvO7npfAwPwQXbigoRDAdYmkg==", "signatures": [{"sig": "MEQCIAMuyHzh59X758GDBr2oMLL1b0Y+/2+v9H13qJsEaTiyAiAxSup5Zj4sBScQQQ84ZWWBNmLwMfKYAh1xz2U1UtIDIw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-figlet.js", "_from": ".", "_shasum": "501a6d60e6eee22826522ec11b611fc1282d0f0d", "engines": {"node": ">= 0.4.0"}, "scripts": {"test": "grunt test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/patorjk/figlet.js.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Creates ASCII Art from text. A full implementation of the FIGfont spec.", "directories": {}, "dependencies": {}, "devDependencies": {"async": "~0.9.0", "grunt": "~0.4.2", "grunt-contrib-clean": "~0.4.0", "grunt-contrib-jshint": "~0.6.0", "grunt-contrib-uglify": "~0.2.0", "grunt-contrib-nodeunit": "~0.2.0"}}, "1.1.2": {"name": "fi<PERSON>t", "version": "1.1.2", "keywords": ["fi<PERSON>t", "ascii", "art", "banner", "ansi"], "author": {"url": "http://patorjk.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "figlet@1.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/patorjk/figlet.js", "bugs": {"url": "https://github.com/patorjk/figlet.js/issues"}, "dist": {"shasum": "2562bf0b7323d640133c54d4c7390da66805c3b3", "tarball": "https://registry.npmjs.org/figlet/-/figlet-1.1.2.tgz", "integrity": "sha512-C5dtMRJj5ZRjKH47V8oRMlwlIKDNQDr5qoAcIrOIw4a0Gfh4Ce11bzwhD8vT3+rQI5G2Xs6TC9P3C/NtTtNvvg==", "signatures": [{"sig": "MEUCIGp0fZzLJ4QQa7zekDv5f5XXHSE9PL3BewqG5YUyut9TAiEAmhIuhqABweLFpHae6VBLWxPtlXZGv3pNYZY5EHIwhko=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-figlet.js", "_from": ".", "_shasum": "2562bf0b7323d640133c54d4c7390da66805c3b3", "engines": {"node": ">= 0.4.0"}, "scripts": {"test": "grunt test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/patorjk/figlet.js.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Creates ASCII Art from text. A full implementation of the FIGfont spec.", "directories": {}, "dependencies": {}, "devDependencies": {"async": "~0.9.0", "grunt": "~0.4.2", "grunt-contrib-clean": "~0.4.0", "grunt-contrib-jshint": "~0.6.0", "grunt-contrib-uglify": "~0.2.0", "grunt-contrib-nodeunit": "~0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/figlet-1.1.2.tgz_1461897488006_0.9165256042033434", "host": "packages-16-east.internal.npmjs.com"}}, "1.2.0": {"name": "fi<PERSON>t", "version": "1.2.0", "keywords": ["fi<PERSON>t", "ascii", "art", "banner", "ansi"], "author": {"url": "http://patorjk.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "figlet@1.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/patorjk/figlet.js", "bugs": {"url": "https://github.com/patorjk/figlet.js/issues"}, "dist": {"shasum": "6c46537378fab649146b5a6143dda019b430b410", "tarball": "https://registry.npmjs.org/figlet/-/figlet-1.2.0.tgz", "integrity": "sha512-guFwfHoBeH1bhSi+XEQlflOo51S/6xNjGROulD/0zLS7VavVN51dwdbwkjrvLzrAXHfT6moNN8IDj3NvvEtGhQ==", "signatures": [{"sig": "MEYCIQDCF3AMgCvgAtaca06KkIm6x5rFkQh/CWU0uLFkJ/qHRgIhAIGSiWL4e/lS1UtNYA6IlyJX03A7I9UwhY8EMMjC7gi+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/node-figlet.js", "_from": ".", "_shasum": "6c46537378fab649146b5a6143dda019b430b410", "browser": "./lib/figlet.js", "engines": {"node": ">= 0.4.0"}, "scripts": {"test": "grunt test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/patorjk/figlet.js.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Creates ASCII Art from text. A full implementation of the FIGfont spec.", "directories": {}, "dependencies": {}, "devDependencies": {"async": "~0.9.0", "grunt": "~0.4.2", "grunt-contrib-clean": "~0.4.0", "grunt-contrib-jshint": "~0.6.0", "grunt-contrib-uglify": "~0.2.0", "grunt-contrib-nodeunit": "~0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/figlet-1.2.0.tgz_1475028725930_0.14830448082648218", "host": "packages-12-west.internal.npmjs.com"}}, "1.2.1": {"name": "fi<PERSON>t", "version": "1.2.1", "keywords": ["fi<PERSON>t", "ascii", "art", "banner", "ansi"], "author": {"url": "http://patorjk.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "figlet@1.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/patorjk/figlet.js#readme", "bugs": {"url": "https://github.com/patorjk/figlet.js/issues"}, "dist": {"shasum": "48d35df9d9b10b1b3888302e6e57904a0b00509c", "tarball": "https://registry.npmjs.org/figlet/-/figlet-1.2.1.tgz", "fileCount": 308, "integrity": "sha512-qc8gycfnnfOmfvPl7Fi3JeTbcvdmbZkckyUVGGAM02je7Ookvu+bBfKy1I4FKqTsQHCs3ARJ76ip/k98r+OQuQ==", "signatures": [{"sig": "MEUCIQDc0jtPSrVHWqldh6thgS+Iy43Jex3I2NH+zAivG2yemAIgNqk/RPDhQRCXJLPM0pDhwcAuO+lPScPSP1BMqYqpkK0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3109332, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbvVEwCRA9TVsSAnZWagAAUMAP/AhPgKwU3gjlF3qw1oCB\nKhns6Fm8P4YbLuEfqex2miRU37mHgd/4C1UKq3w/kbf+HEPNfAdgae/aJlxQ\nlFeASwao+nubHylqHZpsUXYYHgqIDE0EsJcKf6IzoIpmUp+j8P+FkstEZDEZ\ni6ciHOOGMRk14r8yZHFmp/Ql7vCB23R9Ps5N2QALTIxYpN2etBimUhNcjNTS\nBTuZOjY5ouELSxYxHTjKts/ALGmgznE+Z5WFxRLnUlPDbHLxcEXPB2EnEOhl\nTTG/9Zm+BBJXwksyorDDzb9E3iM9mzqqeC1ujAPKZIPbQVZ6XsH5Ox5qrZtY\nFiSgP8qigEfcnNiVO8eOPt10OFarh4E5gPaXAqWbgYuVbkMPRZBwCIhXUadN\nuk++fnXNe+dURXg2kPS+y25sZShSNvjnXB86YN0fMIFSIvCCTIKl6jp/Sx53\nzAujat7tJlvCEFzr946XDibBjqGzck1sI7CF8wextQ3ga3/fECH3iqjVmacf\n5WQcVsKlO/1vgenU+JAqR3Zic//hQQVvqiNk14czm69f04OjJbV6OBWyw8Ug\nXkTIt12xAUjRAqFfDSZphvD1BUgjpmzSNxaH/FfKtVJcZIWVIR4fIS4ggqjQ\ngu6AFdRnLVfcpbyaAsF3r9b5C3FZ7Zv4UqtHxlnQxD89OqAqXo0WRhkTtq1B\nUqpz\r\n=tMeg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/node-figlet.js", "browser": "./lib/figlet.js", "engines": {"node": ">= 0.4.0"}, "gitHead": "ee6072bdc0775c0056e56bfa79ae08000e1833b5", "scripts": {"test": "grunt test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/patorjk/figlet.js.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "Creates ASCII Art from text. A full implementation of the FIGfont spec.", "directories": {}, "_nodeVersion": "10.3.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"async": "~0.9.0", "grunt": "^1.0.3", "grunt-contrib-clean": "~0.4.0", "grunt-contrib-jshint": "^2.0.0", "grunt-contrib-uglify": "^4.0.0", "grunt-contrib-nodeunit": "~0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/figlet_1.2.1_1539133743463_0.35493502534084254", "host": "s3://npm-registry-packages"}}, "1.2.2": {"name": "fi<PERSON>t", "version": "1.2.2", "keywords": ["fi<PERSON>t", "ascii", "art", "banner", "ansi"], "author": {"url": "http://patorjk.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "figlet@1.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/patorjk/figlet.js#readme", "bugs": {"url": "https://github.com/patorjk/figlet.js/issues"}, "dist": {"shasum": "bbabad29508dfd89e9eca0c0c1ed5c5a179aa23e", "tarball": "https://registry.npmjs.org/figlet/-/figlet-1.2.2.tgz", "fileCount": 307, "integrity": "sha512-9HkCLpMlnAt52FUJOH1aw9UKqe66AM3rjAysPhKJ96rh++LduQPFp33zPgt/nzgol3YSnJifs0K77va07edvIQ==", "signatures": [{"sig": "MEUCIQCPghQDqmoBG1XrqhMfsI6iPPNkXn9+Ibn0UgOqGC4RRwIgfGUE65hrMJJD+ZVc94VjAPVQmyh40UbEVg6tWNVrIVA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3105999, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc6n7hCRA9TVsSAnZWagAA0twP/AyJkVhQpiRMrv++m0iW\nTxt1pqZOoIdVjUYP63AYK1Koar6YU2mr3hFVwn60iyYrRSPzd9jRfBiBUau6\nLzsuzXXF16aMc4ImD6NuRvwTX3R0LdLzz87ZwADv43CAfdueTxoCjUrnRXwW\nILMFOygxmwpa+PZ8E55bPBrDyGABkclxWfTT83WDJ0v7ArXGncLQgGoYRadL\ndUKR1IxC5/PMdduZ8OlzOiKXTZyewKCCBTu9eOnG6Sspn/ibQewvWoX+HQYB\nxjc4dfCp4rMPuXt3VGp1krXwtU2ubaqfrSHmHnFPXLmTW9zlKUZ0Q8e+Sg4A\nVYYd52542ARHBJIh4oCZKoDiIlTGjEjh5QDsBwLNUPbw2HbDnYbUamN0w078\ntuf3hMimKPGxvbTpN77AShL5JuQmxf4uIhnRH2sZqj/oEyX1hC4A1VuQgz4F\nqjXkEkRah7VLFMG4QvACeR8oYI7E2fOezNcAFBsDE972NuatCYikqktyEa6u\nD5YT7lktgiwY1SjuvyX4wtVIeNA7Wcw1DE5cIaEC07yVVzYIpL6i3a6wzgKo\n1JzbIcuCErv1i7NcYUftqSgGSwaf8TiwxCtkePzd/Lxu9+TIWMC7tze+n2Wg\nskQh43ifaZ53EclwiPYzx4lkG1zigF0fUCX4zh7qArGL5w+2MyXuLPGKWdLD\nRwMR\r\n=xSG3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/node-figlet.js", "_from": ".", "_shasum": "bbabad29508dfd89e9eca0c0c1ed5c5a179aa23e", "browser": "./lib/figlet.js", "engines": {"node": ">= 0.4.0"}, "gitHead": "e24a3e93e487876ae72c499d3f74484310a27153", "scripts": {"test": "grunt test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/patorjk/figlet.js.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "Creates ASCII Art from text. A full implementation of the FIGfont spec.", "directories": {}, "_nodeVersion": "7.9.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"async": "~0.9.0", "grunt": "^1.0.3", "grunt-contrib-clean": "~0.4.0", "grunt-contrib-jshint": "^2.0.0", "grunt-contrib-uglify": "^4.0.0", "grunt-contrib-nodeunit": "~0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/figlet_1.2.2_1558871776691_0.016464099310681712", "host": "s3://npm-registry-packages"}}, "1.2.3": {"name": "fi<PERSON>t", "version": "1.2.3", "keywords": ["fi<PERSON>t", "ascii", "art", "banner", "ansi"], "author": {"url": "http://patorjk.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "figlet@1.2.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/patorjk/figlet.js#readme", "bugs": {"url": "https://github.com/patorjk/figlet.js/issues"}, "dist": {"shasum": "7d25df546f41fc411c2a8b88012d48d55de72129", "tarball": "https://registry.npmjs.org/figlet/-/figlet-1.2.3.tgz", "fileCount": 595, "integrity": "sha512-+F5zdvZ66j77b8x2KCPvWUHC0UCKUMWrewxmewgPlagp3wmDpcrHMbyv/ygq/6xoxBPGQA+UJU3SMoBzKoROQQ==", "signatures": [{"sig": "MEYCIQCYycgTbj00rgAplKKEV0DeoOaWTM1KdkzvEm+91SIh9wIhAPxcRqN4SPAhQ6uXR/m35NeYMlN0LtErmFVg4Zycu1ps", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5996434, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc6ojdCRA9TVsSAnZWagAAhOsQAIMLtuR050/oRIfP/eh0\n48Xk1sPJ847qdoDtfDEh+yh4R2zZZnnov4h4utdSWAmP6XBIjUPyRl6Nwkbf\nH/BQUjZlhk+JND7eKSrnzGslL7/K+0jixrl0lo2OWUD3dq0lo36KdWPe1EOR\ngfq+ngROJt/AIlqnAj1+95RPwR3hp3CEzS78cHfwWY3FwSN+2Np6xiScjbJQ\n+7YMqJLdCeoN0r08q4zkLZg4VhdOcMw2hCT4wtnqjL0LEtUztev34QalV1nw\nFzjuO5ooO6sr/n9K3iLNiIw2wYihEl5Ch+MsOX8NPkVEOmJZRkp+FjCalE++\nbRm4mqkktM6pyqeNetIb20Je78BeT42E/XZzT9dWSKbsL7xpZpQF5ux5nLsO\nUKDIHyN98fp8Tip3VjdO0rIBwfvvwhNCS6PmbPdXRNqiD5wx2MvLaa3/fV37\nH4V8vti68QMBB3G2XyFlU8aR1tPmFLrkA1QIuXPehup/mrcEEhu0yaAVg+69\nOJUr5qsRWj0iYI6H2BR22jFg1+ltGev3bcYa/EXoMttHZi8V0eiNyUsn6/7i\nKGVjRv1y2red4ulCuC8wpR2d/Mmcn9LQsPBSt5nA1ypdeJfPFy0j6uI4AvyT\nV8CtG0MiNTj1nsEUiO9/dXyXlIyh0Wx1oGQFo0jTzh87qNuN8VFlrNaYY5At\ni7gS\r\n=FVBb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/node-figlet.js", "browser": "./lib/figlet.js", "engines": {"node": ">= 0.4.0"}, "gitHead": "c939421681ce3a07590fc92f5fd81147a6c58920", "scripts": {"test": "grunt test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/patorjk/figlet.js.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Creates ASCII Art from text. A full implementation of the FIGfont spec.", "directories": {}, "_nodeVersion": "12.3.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"async": "~0.9.0", "grunt": "^1.0.3", "grunt-contrib-clean": "~0.4.0", "grunt-contrib-jshint": "^2.0.0", "grunt-contrib-uglify": "^4.0.0", "grunt-contrib-nodeunit": "~0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/figlet_1.2.3_1558874333029_0.3406638905876558", "host": "s3://npm-registry-packages"}}, "1.2.4": {"name": "fi<PERSON>t", "version": "1.2.4", "keywords": ["fi<PERSON>t", "ascii", "art", "banner", "ansi"], "author": {"url": "http://patorjk.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "figlet@1.2.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/patorjk/figlet.js#readme", "bugs": {"url": "https://github.com/patorjk/figlet.js/issues"}, "dist": {"shasum": "2d2f48b61a77418ba6cbd9db2e25da27efee16d0", "tarball": "https://registry.npmjs.org/figlet/-/figlet-1.2.4.tgz", "fileCount": 594, "integrity": "sha512-mv8YA9RruB4C5QawPaD29rEVx3N97ZTyNrE4DAfbhuo6tpcMdKnPVo8MlyT3RP5uPcg5M14bEJBq7kjFf4kAWg==", "signatures": [{"sig": "MEUCIQCQcbkkaBDwH4iWfsUUlFfczR/FZvPSgM/s9UftUfSNugIgbMOVDKA7KrcrWYgIXMcKTECjGB5w2evAwrZJR7ylyvc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6049265, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdbHOOCRA9TVsSAnZWagAAvhoQAJbIGbL5rdh8Sp6PIgbf\nQa+9LNV6hK9CVme8nBpIA577wUu3yh/eHgL5ZIJW62W4DsQaJ5Ve8J3z2ETi\nWfDs7SQ1BOGBhR4FQPkvl/0yRcrzuBFydKekDExgLR444cETkBSTYWZMXDB/\nYZFu56xXhyGUhr7hEYQ026eeQsWqUuT/b+yjgx6G0RHlStBE69YvImLSRlya\nZYkW+MEok55rvi0CKRbo8c8C5GyGImSVg9X0BguL/GQdNCrJwRr4QwtbovVW\nzYiqloS7+JUrGvmLMMjVgVTUi7fCaAyZQuoIJDDkmTyQlDAa9RxZE3IvRTUU\nV67ZZ0KL7gxm1pViKyDE+/FWb3Cv7no6uAxMu8XdC5xrzSyVNCEF0gG8ZzC+\ncLV4OIzlgrLa8vV9L+FFwlxqTt3/BtP3FTOycOaahm9MdAUeGH7n0njIZ/Co\nIjDeQJB5AHZQL5B84LP/LnAypUcsgLN3xeljMYTiZairJ5cTkZ6akqBXn7lc\nTbhXpEKotcHlbIarTvONUE2xTkwDu+Ti97sL4/aqstwa2ePcsJ//AhhuxYLL\nXuIp5/O5611CRJqKDwKO0gpBpc9AR+LPOLL51DlxPTcr3gXLH4JbbLFMxUgS\nyZS/SfJNNGh2ZARj0Oc12g5dRdBSa5Cb2ZaY47Fuax4PFtM3DrjTmd3smVw9\n8RDp\r\n=Hmep\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/node-figlet.js", "browser": "./lib/figlet.js", "engines": {"node": ">= 0.4.0"}, "gitHead": "4c91d2f7be47a33934437cbd26de0c6461ce807d", "scripts": {"test": "grunt test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/patorjk/figlet.js.git", "type": "git"}, "_npmVersion": "6.10.3", "description": "Creates ASCII Art from text. A full implementation of the FIGfont spec.", "directories": {}, "_nodeVersion": "12.9.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"async": "~0.9.0", "grunt": "^1.0.3", "grunt-contrib-clean": "~0.4.0", "grunt-contrib-jshint": "^2.0.0", "grunt-contrib-uglify": "^4.0.0", "grunt-contrib-nodeunit": "~0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/figlet_1.2.4_1567388558068_0.11384697481555417", "host": "s3://npm-registry-packages"}}, "1.3.0": {"name": "fi<PERSON>t", "version": "1.3.0", "keywords": ["fi<PERSON>t", "ascii", "art", "banner", "ansi"], "author": {"url": "http://patorjk.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "figlet@1.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/patorjk/figlet.js#readme", "bugs": {"url": "https://github.com/patorjk/figlet.js/issues"}, "dist": {"shasum": "c49e3d92907ba13bebadc7124f76ba71f1f32ef0", "tarball": "https://registry.npmjs.org/figlet/-/figlet-1.3.0.tgz", "fileCount": 596, "integrity": "sha512-f7A8aOJAfyehLJ7lQ6rEA8WJw7kOk3lfWRi5piSjkzbK5YkI5sqO8eiLHz1ehO+DM0QYB85i8VfA6XIGUbU1dg==", "signatures": [{"sig": "MEQCIDiWKj6WEMtDIkRRhmmBuf7xV3P0LriyhNp/7T7ph3QcAiApfk5OYW7IVIVCvieaeqYfWi0sD2WNqBx8DOSRQl02Ww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6074243, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeUpXLCRA9TVsSAnZWagAAEHEP/igCcPRN7K7biih2cKlh\ne4uGwIZspQj1MDR3urx3rYfOre1+c1VqQHvn0rD892OMQH5ZOwmm2NPgT2Bm\niuEoDVED96MfE8KJ41kEaxA/4TxiiaErntTjEvSkNDAR8MtZstgsaJ+3Z8yq\n46DY1HAm3AU3miB0IUM3SuKMMpBUQl25YNqahfxjkFT1LweLR7T2mU3yZZyN\nz2zBOytNv8ILZJTMaAD0MabCWJbGiDncKOphyzAYc0xBNqfnE7Yi/cvEtrEJ\nmBig2ufH+6ngEF8G8RtwIzcM1TWLPuX+D7Xg62tGocMffiD0w53VBKZa9zJ6\nWhr8t4LQkUbIE0Lu0W01iE6e0RlDfTpKmRnNNqGd0T/MjX9OWMYgJCzvX/Pj\nF5Qdi5td/sNCdSZJIbi0YHKFaT863hYapcVEdhY1r+MM988v5FsHfcS2/aEH\nRFZaxUByzpQE/4N4V4USyk4JSlG0zBMYeOMX8jZWldMtkn///JelcWx/oN1+\nQykY0UZPjBE5S+TNVlOl25lypczmhhNujPKxviAc9zkYD0QlVDDQ2y/w2u9k\nvCjbNm9voFwK/NMbF1GUY9S8Cn+gyLyUfYRGDbSQOov0hGVREgs9xRkjYq46\nuu2qy/3miiYAcaX/ozGTanPrZzXvnBlcuGn0OQd3Pu/pFMnr4z7P+b3T7zb/\nbwDx\r\n=T3hy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/node-figlet.js", "browser": "./lib/figlet.js", "engines": {"node": ">= 0.4.0"}, "gitHead": "f96d1453d7adc78458ca8f896b3b3424e61b8481", "scripts": {"test": "grunt test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/patorjk/figlet.js.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Creates ASCII Art from text. A full implementation of the FIGfont spec.", "directories": {}, "_nodeVersion": "12.12.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"async": "~0.9.0", "grunt": "^1.0.3", "grunt-contrib-clean": "~0.4.0", "grunt-contrib-jshint": "^2.0.0", "grunt-contrib-uglify": "^4.0.0", "grunt-contrib-nodeunit": "~0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/figlet_1.3.0_1582470603021_0.09667506901851253", "host": "s3://npm-registry-packages"}}, "1.4.0": {"name": "fi<PERSON>t", "version": "1.4.0", "keywords": ["fi<PERSON>t", "ascii", "art", "banner", "ansi"], "author": {"url": "http://patorjk.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "figlet@1.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/patorjk/figlet.js#readme", "bugs": {"url": "https://github.com/patorjk/figlet.js/issues"}, "dist": {"shasum": "21c5878b3752a932ebdb8be400e2d10bbcddfd60", "tarball": "https://registry.npmjs.org/figlet/-/figlet-1.4.0.tgz", "fileCount": 595, "integrity": "sha512-CxxIjEKHlqGosgXaIA+sikGDdV6KZOOlzPJnYuPgQlOSHZP5h9WIghYI30fyXnwEVeSH7Hedy72gC6zJrFC+SQ==", "signatures": [{"sig": "MEQCIAeQMBTdkzEYVK6a4pJ9UMDPmJjcq9j+zbF+ZSxMsqypAiAyOZa77Imq0MorCJGP76xtKftbJ6+cn4Z1Va9rGUZK0A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5979546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJephhYCRA9TVsSAnZWagAA/tEP+wXUn+LL0ePMx7UtIyxp\n7uSqys6l065zjFUy8rxmwXXZ3DBOVWXeZFG70uBCO+ZNm0BD1lgqjZ1zTGtl\nSO8Glhe8nfbu8viYNqoqzOib4u3KMc/Up7VK2dEzSTd5pz+CjdblCkvAh/bX\nS+Q+HT6N5z17OrLe1zsHS2tqY8ZJMiukj30yjzZuHZa4gKUN1bFgnqAQl8jD\nEMgMOQuAht3WMiQd65cATz0Xig1RpRVBKlt1nEUC7VwCSVYZKUP4L1T3BK45\nieCfCE7jch9M6i2UKmPfYM1LhsqmLpefXv+4a3PgPF3EfBXjNGzvAZhsqkaH\nMPzz8gv7H7YzTsO33pbprxj3wlS5hMe0gnuCgZo0P9aTByXL4RX0/sOMA8LG\no6O3jtLsPVNlCt+oR/x1lK1NeynKKxD5CE2TszXl8aRDvJg2ceHsEHV7vFst\nNe7uErRtAovysFJxi16BuhMoNVfd3FVze4Nt072J9sMJ1q2w7pWaMdZ4RMSd\neWs3L01I5JVRt+s/3GFlxN6vIguQXPsDZ0UB7un2c06u7hP/O5l3wqgyCNdu\nzsPJql80JI+jVjWrAdP2qXtrTtSmxNPjNnYscQ9YjsfRDnyxQ2w10w/vm5TQ\ndSgCDEOmy3WWH2jLMhgTRn8H8Pc13MnjjJd2D4itGTn1xxjLKTRT9zKqPpHY\n+9oe\r\n=qYHT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/node-figlet.js", "browser": "./lib/figlet.js", "engines": {"node": ">= 0.4.0"}, "gitHead": "90493be153f136c6b82c03c8d7a6fc5a9f170272", "scripts": {"test": "grunt test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/patorjk/figlet.js.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Creates ASCII Art from text. A full implementation of the FIGfont spec.", "directories": {}, "_nodeVersion": "12.12.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"async": "~0.9.0", "grunt": "^1.0.3", "grunt-contrib-clean": "~0.4.0", "grunt-contrib-jshint": "^2.0.0", "grunt-contrib-uglify": "^4.0.0", "grunt-contrib-nodeunit": "~0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/figlet_1.4.0_1587943512259_0.3779437030620614", "host": "s3://npm-registry-packages"}}, "1.5.0": {"name": "fi<PERSON>t", "version": "1.5.0", "keywords": ["fi<PERSON>t", "ascii", "art", "banner", "ansi"], "author": {"url": "http://patorjk.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "figlet@1.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/patorjk/figlet.js#readme", "bugs": {"url": "https://github.com/patorjk/figlet.js/issues"}, "dist": {"shasum": "2db4d00a584e5155a96080632db919213c3e003c", "tarball": "https://registry.npmjs.org/figlet/-/figlet-1.5.0.tgz", "fileCount": 603, "integrity": "sha512-ZQJM4aifMpz6H19AW1VqvZ7l4pOE9p7i/3LyxgO2kp+PO/VcDYNqIHEMtkccqIhTXMKci4kjueJr/iCQEaT/Ww==", "signatures": [{"sig": "MEYCIQC5sRatbkSQ32sk0KP79Dw5Cavu/uYX0qcrko6defXHRgIhAMkqSbczfodeBDhizJXQhAE3LHHNysYrKukVvlwOk6pa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6016423, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfC53/CRA9TVsSAnZWagAAjYAP/R2offWp3Lfwa3PEdarI\nh5dSjMn8KK8d3mBCwnn9z68rw04Vad3Klv4iNYEDkQsCaH6rRM1BLDxiMe+H\n8qn9KqSZGhkaYqfkcrq7wXUei77wCbJ1FhUmJfjpHvHJGCi282fG33F/BZ9R\nIt9rQQdp6Q8nYYpalOsexiylRtdi6HZbgeuLvFynoLxCbbGTA1jy76EqpE8z\nI6+/mRnHLtb9rjew9IS5awfKzQ2ZQKTwkoDHNfEoPgjas/RBMAviD4w8D06E\nwHr1EbAr7q1ygvB3EqjqH9dZU7QdgrzBCAyNQjKoVHqeQCaK1BV7df/f9XQZ\n4WKYPy99U+18R3bhsA7TtUEiWremI08vw+V02p3VJJ0x8fvkNam9196TBZ90\nrGMV//kac0oKmYr9U8YzlhEkqfAF63Mg0DHrFjs/LKtW+CyChE+i386hPfrN\n3/GpGsroP1GXKZdePbe54U/EnE9MbaYNIEUW/HHyDOEnxynbQEffGRangRFH\nmQBb30on/be1a4YXRHqIK7YQgIMe4J0T7xZHB+bTw8UZX2+ECp7tqovOHPGg\neeUTvGQD9AFv3PIpFujT/nHtiDHIlVealz1CBgXKxt1dVfuYaB9zIvq0twgm\nlzmBtPOsFKZukVU5qYy6dx2rC+8ZgJGVbiRCNMk3iGvrmnSjnZII02NIM93q\nCsZJ\r\n=aqtZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/node-figlet.js", "browser": "./lib/figlet.js", "engines": {"node": ">= 0.4.0"}, "gitHead": "9eeee203fd0094fe5e63440c0ba85d9f225254c1", "scripts": {"test": "grunt test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/patorjk/figlet.js.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Creates ASCII Art from text. A full implementation of the FIGfont spec.", "directories": {}, "_nodeVersion": "12.12.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"async": "~0.9.0", "grunt": "^1.0.3", "grunt-contrib-clean": "~0.4.0", "grunt-contrib-jshint": "^2.0.0", "grunt-contrib-uglify": "^4.0.0", "grunt-contrib-nodeunit": "~0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/figlet_1.5.0_1594596863137_0.2852399806410453", "host": "s3://npm-registry-packages"}}, "1.5.2": {"name": "fi<PERSON>t", "version": "1.5.2", "keywords": ["fi<PERSON>t", "ascii", "art", "banner", "ansi"], "author": {"url": "http://patorjk.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "figlet@1.5.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/patorjk/figlet.js#readme", "bugs": {"url": "https://github.com/patorjk/figlet.js/issues"}, "dist": {"shasum": "dda34ff233c9a48e36fcff6741aeb5bafe49b634", "tarball": "https://registry.npmjs.org/figlet/-/figlet-1.5.2.tgz", "fileCount": 604, "integrity": "sha512-WOn21V8AhyE1QqVfPIVxe3tupJacq1xGkPTB4iagT6o+P2cAgEOOwIxMftr4+ZCTI6d551ij9j61DFr0nsP2uQ==", "signatures": [{"sig": "MEQCIE9og70nKieOlBvn55eFuEjh5TqkOn2G9gjEyxariVZ0AiBJ26+mzvPAk5tbSRGKFta+2z88zgRbPVLvNOFV7gYhSg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6014923, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhFJjvCRA9TVsSAnZWagAAbOQP/jJkR26BFqrzZp4U6zly\nWzEyapIpIEvJkTyZzcA2k8asMTNqviCBnKxEQLiCGtQaH0kzw+7j4fc0yMEG\nJ+5M5OhIqG8Di5JhDkbhyCVIhstMdcx0naPQkzk8qXB2qd3oUs1a+W49BoMt\nl53TDGXoNs+C4kXkAcyC6S/zhQoqpAp5m3sUINiIAZzo8yMVpKdG55hnbiEn\nliy3ENCW7HjTkN3oj6J8Jlqnin1D1bFLT9c7N7vnowWp8qMb/jrcS+dbHPKT\nsFsdLN/RbD6BLcT+Gps6eLz2tT+QG6qC67hI9CFALDYMSQsEmFKkNjK80g9L\nPYnfzLSWORrDFez/lpu4ciA6bFIx4hXdP9+omUibL7S3ZsL5veudtw4aPbMk\n7ijNO/wodcwKf6ZcyOcs5xp3yjy/UW8QyymsA1xXniU9rb9/VDoOiKe8bZmn\n4aRxEIb2VgkPHfcpaX/RNgr47TydPZvb4TBthrscJpHU5ODxfJHu/PWgPMue\nQONHqxCWR0w2ro1Ca4Eaue5IO9fHLrLfEuVX+iac1WrwXbGu1MIp9oNmvRrw\n8oVQYhqG7lpag61+ISdWmfLRqHBPWjLAhsdhd32tgVjKh9I3DyGIaXm6QemS\n0wNHfsBqVZPi805omNrzh0c8QKi1uFDqzTkXVPtfCvKUc4qLDHiNcDXTX7zS\n079j\r\n=g+D6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/node-figlet.js", "browser": "./lib/figlet.js", "engines": {"node": ">= 0.4.0"}, "gitHead": "fd9cddca37d56a98657263721d26cc7e27120640", "scripts": {"test": "grunt test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/patorjk/figlet.js.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Creates ASCII Art from text. A full implementation of the FIGfont spec.", "directories": {}, "_nodeVersion": "12.12.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"async": "~0.9.0", "grunt": "^1.0.3", "grunt-contrib-clean": "~0.4.0", "grunt-contrib-jshint": "^2.0.0", "grunt-contrib-uglify": "^4.0.0", "grunt-contrib-nodeunit": "~0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/figlet_1.5.2_1628739823095_0.3015619249612147", "host": "s3://npm-registry-packages"}}, "1.6.0-beta.0": {"name": "fi<PERSON>t", "version": "1.6.0-beta.0", "keywords": ["fi<PERSON>t", "ascii", "art", "banner", "ansi"], "author": {"url": "http://patorjk.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "figlet@1.6.0-beta.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/patorjk/figlet.js#readme", "bugs": {"url": "https://github.com/patorjk/figlet.js/issues"}, "bin": {"figlet": "bin/index.js"}, "dist": {"shasum": "2b4ed9e9d814e827b62a465a158c3b39559e9125", "tarball": "https://registry.npmjs.org/figlet/-/figlet-1.6.0-beta.0.tgz", "fileCount": 611, "integrity": "sha512-JC7WtHCI9AHgdp1rhGUpfuDssAb03b8Z3CUQkn7XRv5Q1w+DMsZp6+Ns2SUWJ1Qkuq3+3oTfDL8nYrAo+DMcPg==", "signatures": [{"sig": "MEYCIQDKi4BNp/bHAQ4RemYABZoM5EHP86YFGtDgEx73zvmYvwIhAOuzbOVAMvfcDtamnRgjxNazEhNqCeTdLiHKoLSUBIaN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6016974, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkMVYMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqB+A/+NsfMq0OYwjZorV3+Fc3TJuOziH/oxH9/zivQ2HhyJ0HONkWY\r\ni9GzBj1sGLiDEB5aLR8ViYqG2ey/fnAjxqOZ/B9f44Ix/fmcmmuZnnTtrTO5\r\nxI3o2FHYjlZe3JZlE56iUaYVQGcsOBqIc1X0DNl4bR7KYhODmUsh/EN3u9y3\r\nO3gIfZOh++TjK8Z+BU71Mt5iuBR4iQMxBUgfoIhQ/otxqA+QC/RvZ4jgt3dF\r\nYVwkot4a+4UgG+Xd4lrKzvIxiPjShxM6m6QQdLDh48VJcR0yjouSRkkbVhM8\r\nkWZhelf9omuLYJkM0EN0ghuP8mjxcBcmqjtV6zof2smrhirpNhO0RExhlGdV\r\ngkF4tKjEW9jMIac6wtA4jGLzPfdJcu0njAK92jOlA2rxtu7so1+vnOKe4pBC\r\nhx20/r7p/adfiIZjYNXAuSm5fxqUF6cCGk8hDZadElVcGtlppJxkF/Pa0UAJ\r\nO9L0Pb8Pv4luhgQpJfN0JqgMWvh4LPAnTs9GRJZ0HWv36CO5Mev6FhirRn+h\r\n0NHHePx2DLQa0BKXJrUtstRYttbWDhXbnXvQTFT4V6d8/Yil7cpqw2obcY3+\r\niVXXS8EKxftU5tMDB2andpI3H54+5g/xkvWVqGOdfUMgJ3f8c9+5ZuRRz3JL\r\ndDl+XnkeeuWTL49THjL9aZhkAby39okkX1c=\r\n=EY+T\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/node-figlet.js", "browser": "./lib/figlet.js", "engines": {"node": ">= 0.4.0"}, "gitHead": "db38c0dec44396171de349e86843f1a21a9f0301", "scripts": {"test": "grunt test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/patorjk/figlet.js.git", "type": "git"}, "_npmVersion": "8.5.5", "description": "Creates ASCII Art from text. A full implementation of the FIGfont spec.", "directories": {}, "_nodeVersion": "17.8.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"async": "~1.0.0", "grunt": "^1.0.3", "grunt-contrib-clean": "^2.0.1", "grunt-contrib-jshint": "^3.2.0", "grunt-contrib-uglify": "^4.0.0", "grunt-contrib-nodeunit": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/figlet_1.6.0-beta.0_1680954891631_0.7999630216585507", "host": "s3://npm-registry-packages"}}, "1.6.0-beta.1": {"name": "fi<PERSON>t", "version": "1.6.0-beta.1", "keywords": ["fi<PERSON>t", "ascii", "art", "banner", "ansi"], "author": {"url": "http://patorjk.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "figlet@1.6.0-beta.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/patorjk/figlet.js#readme", "bugs": {"url": "https://github.com/patorjk/figlet.js/issues"}, "bin": {"figlet": "bin/index.js"}, "dist": {"shasum": "181d933d8d60f24bd45ea94b0c875dcb290058a3", "tarball": "https://registry.npmjs.org/figlet/-/figlet-1.6.0-beta.1.tgz", "fileCount": 611, "integrity": "sha512-NaI+wzVYM3PhVEZM68hGj1RbddV9xtrAEDtaCOxg+gTG++jZW6V6HSDdk543KvbvsX+MlS9tBT3d9/m9Z2xh3g==", "signatures": [{"sig": "MEUCIQCiAXmQEctlMBxpG6ogeQcwjwYNLYPPLkq+6pg5ogDlSQIgcRWjFRCHbW826zLLqx9r7fy8yQGuVitB1KTiLziur+U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6017034, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkMWh9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpEuRAAoHVl3qyuZ3+c+pmNhWVYTT2do3+ngu/uE7SejaBfkc8zhY/q\r\n1FtcWHyEvaj/eNvOZcvIv7pdF1EHsN0oK4sn/wXDw6+gVvb111Nc1LDC7TCP\r\nk6CsMkgUCq2h+qzfH9yYIY0SISJwfWFFk7+hWIsob12rknBjnokIRbBAIacz\r\nGwdOGTjr8IOnG2fgXQkmB/qbdKYhJ1oUVjbgofb8EOB084Wz06qCZkz9I/LC\r\n8JnBYr1egD+tQt/FE8I4j5YfAzWGvosmFy5+M4bpRjRJDczpV8nCT1SBdhDM\r\n96DTeDh2aDDdrdZSO/uWeIuE0W4nwz8IXwE3b1DSZ46a8YFouiK/ggWUnsAw\r\nzfJkcrWlWmEz3kuOgk0z4ehmfHt6k5CJXG55dKmZQwgQ4/v8DIKUOL8voOwh\r\nvaBQztbAFOLZG1jrIHfVodjQfAn/XyGPcwABjSWnGmRODNbccmts5KVF9EYN\r\nNk1gfofCPLAcAnVjUQCEwNViGz/G4gYarFWYkvdMRzBD4vaiKHoJnbp7bJY2\r\nGfhA5krNmscZCgLAQoHKY59ZJCb29G1uYYL9q5JMXc9354vkEJI0inhc6lwe\r\nCjnUmuwZTCxJouT/nqMiDrWe1HQx7tVcXD7lbQPm70fj9grkC45dTQH/A1/1\r\nlPSSt5V3BYflEIF6T1Ay25c0s47EhvlWcY8=\r\n=2CU/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/node-figlet.js", "browser": "./lib/figlet.js", "engines": {"node": ">= 0.4.0"}, "gitHead": "6c833deb4e46605ca2995cc9e5dc5ca0c37e9ee6", "scripts": {"test": "grunt test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/patorjk/figlet.js.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "Creates ASCII Art from text. A full implementation of the FIGfont spec.", "directories": {}, "_nodeVersion": "19.8.1", "_hasShrinkwrap": false, "devDependencies": {"async": "~3.2.4", "grunt": "^1.6.1", "grunt-contrib-clean": "~2.0.1", "grunt-contrib-jshint": "^3.2.0", "grunt-contrib-uglify": "^5.2.2", "grunt-contrib-nodeunit": "~5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/figlet_1.6.0-beta.1_1680959612690_0.6931573249717318", "host": "s3://npm-registry-packages"}}, "1.6.0": {"name": "fi<PERSON>t", "version": "1.6.0", "keywords": ["fi<PERSON>t", "ascii", "art", "banner", "ansi"], "author": {"url": "http://patorjk.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "figlet@1.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/patorjk/figlet.js#readme", "bugs": {"url": "https://github.com/patorjk/figlet.js/issues"}, "bin": {"figlet": "bin/index.js"}, "dist": {"shasum": "812050fa9f01043b4d44ddeb11f20fb268fa4b93", "tarball": "https://registry.npmjs.org/figlet/-/figlet-1.6.0.tgz", "fileCount": 614, "integrity": "sha512-31EQGhCEITv6+hi2ORRPyn3bulaV9Fl4xOdR169cBzH/n1UqcxsiSB/noo6SJdD7Kfb1Ljit+IgR1USvF/XbdA==", "signatures": [{"sig": "MEYCIQDQQdss2WS4ql0H8mlIgtCOszYoTL52ziXhSNCWpJjYdwIhAOpJn5GmX60ctv6bTW7nYNnM0kWvVCRXl4BnKQDDnnEc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6014753, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkMXiwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmom+A/5ALUV4gxVE4hnCoBxpHL1tgr52ff6vHLYGJ/PL850bEu+13LD\r\nCCBUq3QbWaAx81EqZPzRNiO2SDDn5bvlNgMHq6iAh0MvtNRaAVZfDVuJ+d0z\r\nbrEOxlWpaP578wMVsLrTEX3gb8xz3HZ7VoMBPaLO18tRYC4XTBaedNu0feRe\r\nv5zMHlIR7PpK5sJrRW1aXy8F8tePptEULVS5z2C3BoDVsja/Oo0RnCyLwcvE\r\nMmu4ojbiczWNWWCy6HhYbXkyYIuekdgcoLkNp0k4f70vy/Mx/ZLwBGLY3KDK\r\nWN63OeeFIH+37s0Mmze7PUULWp8NE/E5j4pPYFEKMz43bfKHxizNzEn3DKeC\r\n3BcHDd3ujZTuPw9FZYJZ6w2G0+RicYiH4vy2+JDr3HlQbCDlIO1cCrtYyKYO\r\n1BcH0YGa2zYiHiEhLFKS5WmOY3VRmP8waMTBnErH8/JAIilqtZ4iyHxBwn6b\r\nmwwqjsVj4dCAGjbRtt9pzIftlnaFmqQygLYBKn68xMr+/DLFXSOam8K61qiI\r\n15ZdWHP75VGxPdn+ARsOi2hSxuOsPZEVikDz4k5pBjr9W5/yclqGPcT+jNXe\r\n+UVi2pLLBvlCDGlbC6fQD+Ey5cwyyfFXk3tm4HcoS8L0/xk0SYDWWKPCepaS\r\nxME1wKsDU2kK07X0eDOb+pgSlx7mJN91hBY=\r\n=ijTL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/node-figlet.js", "browser": "./lib/figlet.js", "engines": {"node": ">= 0.4.0"}, "gitHead": "e01be3eab219f526369acc066bb5ebf48bad9ad5", "scripts": {"test": "grunt test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/patorjk/figlet.js.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "Creates ASCII Art from text. A full implementation of the FIGfont spec.", "directories": {}, "_nodeVersion": "19.8.1", "_hasShrinkwrap": false, "devDependencies": {"async": "~3.2.4", "grunt": "^1.6.1", "prettier": "2.8.7", "grunt-contrib-clean": "~2.0.1", "grunt-contrib-jshint": "^3.2.0", "grunt-contrib-uglify": "^5.2.2", "grunt-contrib-nodeunit": "~5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/figlet_1.6.0_1680963760359_0.38949168611251017", "host": "s3://npm-registry-packages"}}, "1.7.0": {"name": "fi<PERSON>t", "version": "1.7.0", "keywords": ["fi<PERSON>t", "ascii", "art", "banner", "ansi"], "author": {"url": "http://patorjk.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "figlet@1.7.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/patorjk/figlet.js#readme", "bugs": {"url": "https://github.com/patorjk/figlet.js/issues"}, "bin": {"figlet": "bin/index.js"}, "dist": {"shasum": "46903a04603fd19c3e380358418bb2703587a72e", "tarball": "https://registry.npmjs.org/figlet/-/figlet-1.7.0.tgz", "fileCount": 617, "integrity": "sha512-gO8l3wvqo0V7wEFLXPbkX83b7MVjRrk1oRLfYlZXol8nEpb/ON9pcKLI4qpBv5YtOTfrINtqb7b40iYY2FTWFg==", "signatures": [{"sig": "MEYCIQDP9/+kzOs+2WmclUs7teK7Fku6UyxfJcvxjVpUSh6kbQIhAOLHOqvWyrWPyFCqWecVaXupxXGVH6SV1rKIp2XCedSG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6072316}, "main": "./lib/node-figlet.js", "browser": "./lib/figlet.js", "engines": {"node": ">= 0.4.0"}, "gitHead": "37c63bdc8d983fc34a437e67bbb531d8713b4a3e", "scripts": {"test": "grunt test", "prepare": "husky install"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/patorjk/figlet.js.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "Creates ASCII Art from text. A full implementation of the FIGfont spec.", "directories": {}, "lint-staged": {"**/*": "prettier --write --ignore-unknown"}, "_nodeVersion": "19.8.1", "_hasShrinkwrap": false, "devDependencies": {"async": "~3.2.4", "grunt": "^1.6.1", "husky": "^8.0.3", "prettier": "2.8.7", "lint-staged": "^13.2.1", "grunt-contrib-clean": "~2.0.1", "grunt-contrib-jshint": "^3.2.0", "grunt-contrib-uglify": "^5.2.2", "grunt-contrib-nodeunit": "~5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/figlet_1.7.0_1698025547182_0.5760805201425041", "host": "s3://npm-registry-packages"}}, "1.8.0": {"name": "fi<PERSON>t", "version": "1.8.0", "keywords": ["fi<PERSON>t", "ascii", "art", "banner", "ansi"], "author": {"url": "http://patorjk.com/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "figlet@1.8.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/patorjk/figlet.js#readme", "bugs": {"url": "https://github.com/patorjk/figlet.js/issues"}, "bin": {"figlet": "bin/index.js"}, "dist": {"shasum": "1b93c4f65f4c1a3b1135221987eee8cf8b9c0ac7", "tarball": "https://registry.npmjs.org/figlet/-/figlet-1.8.0.tgz", "fileCount": 622, "integrity": "sha512-chzvGjd+Sp7KUvPHZv6EXV5Ir3Q7kYNpCr4aHrRW79qFtTefmQZNny+W1pW9kf5zeE6dikku2W50W/wAH2xWgw==", "signatures": [{"sig": "MEYCIQDr5rooT0tJYDSDjrQzU9PFtt63yB5Kw0M7wUpKr+45iwIhAM8oYr2aKi4fNjFHnbxNUR8vDrNUXvqlRaLSPMCZHtY0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6125008}, "main": "./lib/node-figlet.js", "browser": "./lib/figlet.js", "engines": {"node": ">= 0.4.0"}, "gitHead": "cf153f20c2c81a2d13d021894b9ebe79d81b63cd", "scripts": {"test": "grunt test", "prepare": "husky install"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/patorjk/figlet.js.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "Creates ASCII Art from text. A full implementation of the FIGfont spec.", "directories": {}, "lint-staged": {"**/*": "prettier --write --ignore-unknown"}, "_nodeVersion": "19.8.1", "_hasShrinkwrap": false, "devDependencies": {"async": "~3.2.4", "grunt": "^1.6.1", "husky": "^8.0.3", "prettier": "2.8.7", "lint-staged": "^13.2.1", "grunt-contrib-clean": "~2.0.1", "grunt-contrib-jshint": "^3.2.0", "grunt-contrib-uglify": "^5.2.2", "grunt-contrib-nodeunit": "~5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/figlet_1.8.0_1728432933477_0.0789586557899471", "host": "s3://npm-registry-packages"}}, "1.8.1": {"name": "fi<PERSON>t", "version": "1.8.1", "description": "Creates ASCII Art from text. A full implementation of the FIGfont spec.", "keywords": ["fi<PERSON>t", "ascii", "art", "banner", "ansi"], "bin": {"figlet": "bin/index.js"}, "repository": {"type": "git", "url": "git+https://github.com/patorjk/figlet.js.git"}, "bugs": {"url": "https://github.com/patorjk/figlet.js/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://patorjk.com/"}, "license": "MIT", "main": "./lib/node-figlet.js", "browser": "./lib/figlet.js", "devDependencies": {"async": "~3.2.4", "grunt": "^1.6.1", "grunt-contrib-clean": "~2.0.1", "grunt-contrib-jshint": "^3.2.0", "grunt-contrib-nodeunit": "~5.0.0", "grunt-contrib-uglify": "^5.2.2", "husky": "^8.0.3", "lint-staged": "^13.2.1", "prettier": "2.8.7"}, "scripts": {"test": "grunt test", "prepare": "husky install"}, "lint-staged": {"**/*": "prettier --write --ignore-unknown"}, "engines": {"node": ">= 0.4.0"}, "_id": "figlet@1.8.1", "gitHead": "1e1b5226a14094c4ba54dab4ec3076abbe988227", "homepage": "https://github.com/patorjk/figlet.js#readme", "_nodeVersion": "23.5.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-kEC3Sme+YvA8Hkibv0NR1oClGcWia0VB2fC1SlMy027cwe795Xx40Xiv/nw/iFAwQLupymWh+uhAAErn/7hwPg==", "shasum": "e8e8a07e8c16be24c31086d7d5de8a9b9cf7f0fd", "tarball": "https://registry.npmjs.org/figlet/-/figlet-1.8.1.tgz", "fileCount": 622, "unpackedSize": 6130902, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIGdp/6YTUyID4u94wav2i4z9Ar0lX6/gsGNaxlte9n/UAiEA8aXu0mOoKOA6eYIo01pDTMA2XjKvbp5KNa/PJs61MLA="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/figlet_1.8.1_1744416760409_0.42846704557870874"}, "_hasShrinkwrap": false}}, "time": {"created": "2013-12-23T06:15:54.632Z", "modified": "2025-04-12T00:12:41.202Z", "1.0.1": "2013-12-23T06:15:56.757Z", "1.0.2": "2013-12-23T06:28:27.119Z", "1.0.3": "2013-12-23T23:57:52.358Z", "1.0.5": "2013-12-26T05:50:49.135Z", "1.0.6": "2013-12-27T23:09:36.194Z", "1.0.7": "2013-12-28T05:35:08.904Z", "1.0.8": "2014-01-02T20:14:28.365Z", "1.0.9": "2014-06-07T23:02:05.935Z", "1.0.10": "2014-07-31T01:34:35.603Z", "1.1.0": "2014-08-16T03:45:51.542Z", "1.1.1": "2015-06-11T00:25:52.485Z", "1.1.2": "2016-04-29T02:38:08.997Z", "1.2.0": "2016-09-28T02:12:08.396Z", "1.2.1": "2018-10-10T01:09:03.621Z", "1.2.2": "2019-05-26T11:56:16.897Z", "1.2.3": "2019-05-26T12:38:53.257Z", "1.2.4": "2019-09-02T01:42:38.292Z", "1.3.0": "2020-02-23T15:10:03.281Z", "1.4.0": "2020-04-26T23:25:12.445Z", "1.5.0": "2020-07-12T23:34:23.376Z", "1.5.2": "2021-08-12T03:43:43.371Z", "1.6.0-beta.0": "2023-04-08T11:54:51.972Z", "1.6.0-beta.1": "2023-04-08T13:13:32.996Z", "1.6.0": "2023-04-08T14:22:40.651Z", "1.7.0": "2023-10-23T01:45:47.551Z", "1.8.0": "2024-10-09T00:15:33.832Z", "1.8.1": "2025-04-12T00:12:40.708Z"}, "bugs": {"url": "https://github.com/patorjk/figlet.js/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://patorjk.com/"}, "license": "MIT", "homepage": "https://github.com/patorjk/figlet.js#readme", "keywords": ["fi<PERSON>t", "ascii", "art", "banner", "ansi"], "repository": {"type": "git", "url": "git+https://github.com/patorjk/figlet.js.git"}, "description": "Creates ASCII Art from text. A full implementation of the FIGfont spec.", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "```\n___________.___  ________.__          __          __\n\\_   _____/|   |/  _____/|  |   _____/  |_       |__| ______\n |    __)  |   /   \\  ___|  | _/ __ \\   __\\      |  |/  ___/\n |     \\   |   \\    \\_\\  \\  |_\\  ___/|  |        |  |\\___ \\\n \\___  /   |___|\\______  /____/\\___  >__| /\\ /\\__|  /____  >\n     \\/                \\/          \\/     \\/ \\______|    \\/\n\n```\n\n[![Build Status](https://travis-ci.org/patorjk/figlet.js.svg)](https://travis-ci.org/patorjk/figlet.js)\n[![NPM Downloads](https://img.shields.io/npm/dt/figlet.svg?style=flat)](https://npmcharts.com/compare/figlet?minimal=true)\n\nThis project aims to fully implement the FIGfont spec in JavaScript. It works in the browser and with Node.js. You can see it in action here: http://patorjk.com/software/taag/ (the figlet.js file was written to power that application)\n\n## Quick Start - Node.js\n\nInstall:\n\n```sh\nnpm install figlet\n```\n\nSimple usage:\n\n```js\nvar figlet = require(\"figlet\");\n\nfiglet(\"Hello World!!\", function (err, data) {\n  if (err) {\n    console.log(\"Something went wrong...\");\n    console.dir(err);\n    return;\n  }\n  console.log(data);\n});\n```\n\nThat should print out:\n\n```\n  _   _      _ _        __        __         _     _ _ _\n | | | | ___| | | ___   \\ \\      / /__  _ __| | __| | | |\n | |_| |/ _ \\ | |/ _ \\   \\ \\ /\\ / / _ \\| '__| |/ _` | | |\n |  _  |  __/ | | (_) |   \\ V  V / (_) | |  | | (_| |_|_|\n |_| |_|\\___|_|_|\\___/     \\_/\\_/ \\___/|_|  |_|\\__,_(_|_)\n```\n\n## Basic Usage - Node.js\n\n### text\n\nCalling the figlet object as a function is shorthand for calling the text function. This method allows you to create ASCII Art from text. It takes in 3 parameters:\n\n- Input Text - A string of text to turn into ASCII Art.\n- Options - Either a string indicating the font name or an options object (description below).\n- Callback - Optional function to execute with the generated ASCII Art.\n- Return value is a promise that resolves to generated ASCII Art.\n\nExample:\n\n```js\nfiglet.text(\n  \"Boo!\",\n  {\n    font: \"Ghost\",\n    horizontalLayout: \"default\",\n    verticalLayout: \"default\",\n    width: 80,\n    whitespaceBreak: true,\n  },\n  function (err, data) {\n    if (err) {\n      console.log(\"Something went wrong...\");\n      console.dir(err);\n      return;\n    }\n    console.log(data);\n  }\n);\n```\n\nThat will print out:\n\n```\n.-. .-')                            ,---.\n\\  ( OO )                           |   |\n ;-----.\\  .-'),-----.  .-'),-----. |   |\n | .-.  | ( OO'  .-.  '( OO'  .-.  '|   |\n | '-' /_)/   |  | |  |/   |  | |  ||   |\n | .-. `. \\_) |  |\\|  |\\_) |  |\\|  ||  .'\n | |  \\  |  \\ |  | |  |  \\ |  | |  |`--'\n | '--'  /   `'  '-'  '   `'  '-'  '.--.\n `------'      `-----'      `-----' '--'\n```\n\nSimilary you can use Promise API:\n\n```js\ntry {\n  console.log(\n    await figlet.text(\"Boo!\", {\n      font: \"Ghost\",\n      horizontalLayout: \"default\",\n      verticalLayout: \"default\",\n      width: 80,\n      whitespaceBreak: true,\n    })\n  );\n} catch (err) {\n  console.log(\"Something went wrong...\");\n  console.dir(err);\n}\n```\n\nThis will print the same output.\n\n### textSync\n\nThis method is the synchronous version of the method above.\n\n- Input Text - A string of text to turn into ASCII Art.\n- Font Options - Either a string indicating the font name or an options object (description below).\n\nExample:\n\n```js\nconsole.log(\n  figlet.textSync(\"Boo!\", {\n    font: \"Ghost\",\n    horizontalLayout: \"default\",\n    verticalLayout: \"default\",\n    width: 80,\n    whitespaceBreak: true,\n  })\n);\n```\n\nThat will print out:\n\n```\n.-. .-')                            ,---.\n\\  ( OO )                           |   |\n ;-----.\\  .-'),-----.  .-'),-----. |   |\n | .-.  | ( OO'  .-.  '( OO'  .-.  '|   |\n | '-' /_)/   |  | |  |/   |  | |  ||   |\n | .-. `. \\_) |  |\\|  |\\_) |  |\\|  ||  .'\n | |  \\  |  \\ |  | |  |  \\ |  | |  |`--'\n | '--'  /   `'  '-'  '   `'  '-'  '.--.\n `------'      `-----'      `-----' '--'\n```\n\n### Options\n\nThe options object has several parameters which you can set:\n\n#### font\n\nType: `String`\nDefault value: `'Standard'`\n\nA string value that indicates the FIGlet font to use.\n\n#### horizontalLayout\n\nType: `String`\nDefault value: `'default'`\n\nA string value that indicates the horizontal layout to use. FIGlet fonts have 5 possible values for this: \"default\", \"full\", \"fitted\", \"controlled smushing\", and \"universal smushing\". \"default\" does the kerning the way the font designer intended, \"full\" uses full letter spacing, \"fitted\" moves the letters together until they almost touch, and \"controlled smushing\" and \"universal smushing\" are common FIGlet kerning setups.\n\n#### verticalLayout\n\nType: `String`\nDefault value: `'default'`\n\nA string value that indicates the vertical layout to use. FIGlet fonts have 5 possible values for this: \"default\", \"full\", \"fitted\", \"controlled smushing\", and \"universal smushing\". \"default\" does the kerning the way the font designer intended, \"full\" uses full letter spacing, \"fitted\" moves the letters together until they almost touch, and \"controlled smushing\" and \"universal smushing\" are common FIGlet kerning setups.\n\n#### width\n\nType: `Number`\nDefault value: `undefined`\n\nThis option allows you to limit the width of the output. For example, if you want your output to be a max of 80 characters wide, you would set this option to 80. [Example](https://github.com/patorjk/figlet.js/blob/master/examples/front-end/index.htm)\n\n#### whitespaceBreak\n\nType: `Boolean`\nDefault value: `false`\n\nThis option works in conjunction with \"width\". If this option is set to true, then the library will attempt to break text up on whitespace when limiting the width. [Example](https://github.com/patorjk/figlet.js/blob/master/examples/front-end/index.htm)\n\n### Understanding Kerning\n\nThe 2 layout options allow you to override a font's default \"kerning\". Below you can see how this effects the text. The string \"Kerning\" was printed using the \"Standard\" font with horizontal layouts of \"default\", \"fitted\" and then \"full\".\n\n```\n  _  __               _\n | |/ /___ _ __ _ __ (_)_ __   __ _\n | ' // _ \\ '__| '_ \\| | '_ \\ / _` |\n | . \\  __/ |  | | | | | | | | (_| |\n |_|\\_\\___|_|  |_| |_|_|_| |_|\\__, |\n                              |___/\n  _  __                   _\n | |/ / ___  _ __  _ __  (_) _ __    __ _\n | ' / / _ \\| '__|| '_ \\ | || '_ \\  / _` |\n | . \\|  __/| |   | | | || || | | || (_| |\n |_|\\_\\\\___||_|   |_| |_||_||_| |_| \\__, |\n                                    |___/\n  _  __                        _\n | |/ /   ___   _ __   _ __   (_)  _ __     __ _\n | ' /   / _ \\ | '__| | '_ \\  | | | '_ \\   / _` |\n | . \\  |  __/ | |    | | | | | | | | | | | (_| |\n |_|\\_\\  \\___| |_|    |_| |_| |_| |_| |_|  \\__, |\n                                           |___/\n```\n\nIn most cases you'll either use the default setting or the \"fitted\" setting. Most fonts don't support vertical kerning, but a hand full of them do (like the \"Standard\" font).\n\n### metadata\n\nThe metadata function allows you to retrieve a font's default options and header comment. Example usage:\n\n```js\nfiglet.metadata(\"Standard\", function (err, options, headerComment) {\n  if (err) {\n    console.log(\"something went wrong...\");\n    console.dir(err);\n    return;\n  }\n  console.dir(options);\n  console.log(headerComment);\n});\n```\n\nThe function also return a promise that return an array with two values:\n\n```js\ntry {\n  const [options, headerComment] = await figlet.metadata(\"Standard\");\n  console.dir(options);\n  console.log(headerComment);\n} catch (err) {\n  console.log(\"something went wrong...\");\n  console.dir(err);\n}\n```\n\n### fonts\n\nThe fonts function allows you to get a list of all of the available fonts. Example usage:\n\n```js\nfiglet.fonts(function (err, fonts) {\n  if (err) {\n    console.log(\"something went wrong...\");\n    console.dir(err);\n    return;\n  }\n  console.dir(fonts);\n});\n```\n\n`fonts` is Node.js only.\n\n### fontsSync\n\nThe synchronous version of the fonts method\n\n```js\nconsole.log(figlet.fontsSync());\n```\n\nsame as `fonts`, `fontsSync` is Node.js only.\n\n### parseFont\n\nAllows you to use a font from another source.\n\n```js\nconst fs = require(\"fs\");\nconst path = require(\"path\");\n\nlet data = fs.readFileSync(path.join(__dirname, \"myfont.flf\"), \"utf8\");\nfiglet.parseFont(\"myfont\", data);\nconsole.log(figlet.textSync(\"myfont!\", \"myfont\"));\n```\n\n## Getting Started - Webpack / React\n\nWebpack/React usage will be very similar to what's talked about in the \"Getting Started - The Browser\" section. The main difference is that you import fonts via the importable-fonts folder. Example:\n\n```js\nimport figlet from \"figlet\";\nimport standard from \"figlet/importable-fonts/Standard.js\";\n\nfiglet.parseFont(\"Standard\", standard);\n\nfiglet.text(\n  \"test\",\n  {\n    font: \"Standard\",\n  },\n  function (err, data) {\n    console.log(data);\n  }\n);\n```\n\n## Getting Started - The Browser\n\nThe browser API is the same as the Node API with the exception of the \"fonts\" method not being available. The browser version also requires [fetch API](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API) (or a [shim](https://github.com/github/fetch)) for its loadFont function.\n\nExample usage:\n\n```html\n<script\n  type=\"text/javascript\"\n  src=\"//cdnjs.cloudflare.com/ajax/libs/fetch/1.0.0/fetch.min.js\"\n></script>\n<script type=\"text/javascript\" src=\"figlet.js\"></script>\n\n<script>\n  figlet(inputText, \"Standard\", function (err, text) {\n    if (err) {\n      console.log(\"something went wrong...\");\n      console.dir(err);\n      return;\n    }\n    console.log(text);\n  });\n</script>\n```\n\n### textSync\n\nThe browser API supports synchronous mode so long as fonts used are preloaded.\n\nExample:\n\n```js\nfiglet.defaults({ fontPath: \"assets/fonts\" });\n\nfiglet.preloadFonts([\"Standard\", \"Ghost\"], ready);\n\nfunction ready() {\n  console.log(figlet.textSync(\"ASCII\"));\n  console.log(figlet.textSync(\"Art\", \"Ghost\"));\n}\n```\n\nThat will print out:\n\n```\n     _     ____    ____  ___  ___\n    / \\   / ___|  / ___||_ _||_ _|\n   / _ \\  \\___ \\ | |     | |  | |\n  / ___ \\  ___) || |___  | |  | |\n /_/   \\_\\|____/  \\____||___||___|\n\n   ('-.     _  .-')   .-') _\n  ( OO ).-.( \\( -O ) (  OO) )\n  / . --. / ,------. /     '._\n  | \\-.  \\  |   /`. '|'--...__)\n.-'-'  |  | |  /  | |'--.  .--'\n \\| |_.'  | |  |_.' |   |  |\n  |  .-.  | |  .  '.'   |  |\n  |  | |  | |  |\\  \\    |  |\n  `--' `--' `--' '--'   `--'\n\n```\n\nSee the examples folder for a more robust front-end example.\n\n## Getting Started - Command Line\n\nTo use figlet.js on the command line, install figlet-cli:\n\n```sh\nnpm install -g figlet-cli\n```\n\nAnd then you should be able run from the command line. Example:\n\n```sh\nfiglet -f \"Dancing Font\" \"Hi\"\n```\n\nFor more info see the [figlet-cli](https://github.com/patorjk/figlet-cli).\n\n## Contributors\n\nThanks goes to these people: ([emoji key](https://allcontributors.org/docs/en/emoji-key))\n\n<!-- ALL-CONTRIBUTORS-LIST:START - Do not remove or modify this section -->\n<!-- prettier-ignore-start -->\n<!-- markdownlint-disable -->\n<table>\n  <tbody>\n    <tr>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"http://patorjk.com/\"><img src=\"https://avatars.githubusercontent.com/u/521224?v=4?s=100\" width=\"100px;\" alt=\"patorjk\"/><br /><sub><b>patorjk</b></sub></a><br /><a href=\"#code-patorjk\" title=\"Code\">💻</a> <a href=\"#doc-patorjk\" title=\"Documentation\">📖</a> <a href=\"#test-patorjk\" title=\"Tests\">⚠️</a> <a href=\"#infra-patorjk\" title=\"Infrastructure (Hosting, Build-Tools, etc)\">🚇</a> <a href=\"#example-patorjk\" title=\"Examples\">💡</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://jcu.bi\"><img src=\"https://avatars.githubusercontent.com/u/280241?v=4?s=100\" width=\"100px;\" alt=\"Jakub T. Jankiewicz\"/><br /><sub><b>Jakub T. Jankiewicz</b></sub></a><br /><a href=\"#code-jcubic\" title=\"Code\">💻</a> <a href=\"#doc-jcubic\" title=\"Documentation\">📖</a> <a href=\"#test-jcubic\" title=\"Tests\">⚠️</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://tracker1.dev/\"><img src=\"https://avatars.githubusercontent.com/u/444316?v=4?s=100\" width=\"100px;\" alt=\"Michael J. Ryan\"/><br /><sub><b>Michael J. Ryan</b></sub></a><br /><a href=\"#code-tracker1\" title=\"Code\">💻</a> <a href=\"#doc-tracker1\" title=\"Documentation\">📖</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://github.com/seriousManual\"><img src=\"https://avatars.githubusercontent.com/u/1330022?v=4?s=100\" width=\"100px;\" alt=\"Manuel Ernst\"/><br /><sub><b>Manuel Ernst</b></sub></a><br /><a href=\"#code-seriousManual\" title=\"Code\">💻</a> <a href=\"#doc-seriousManual\" title=\"Documentation\">📖</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://github.com/eiriksm\"><img src=\"https://avatars.githubusercontent.com/u/865153?v=4?s=100\" width=\"100px;\" alt=\"Eirik Stanghelle Morland\"/><br /><sub><b>Eirik Stanghelle Morland</b></sub></a><br /><a href=\"#infra-eiriksm\" title=\"Infrastructure (Hosting, Build-Tools, etc)\">🚇</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"http://und.ooo\"><img src=\"https://avatars.githubusercontent.com/u/46262811?v=4?s=100\" width=\"100px;\" alt=\"George\"/><br /><sub><b>George</b></sub></a><br /><a href=\"#example-Horhik\" title=\"Examples\">💡</a> <a href=\"#doc-Horhik\" title=\"Documentation\">📖</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"http://websemantics.ca\"><img src=\"https://avatars.githubusercontent.com/u/2190455?v=4?s=100\" width=\"100px;\" alt=\"Adnan M.Sagar, PhD\"/><br /><sub><b>Adnan M.Sagar, PhD</b></sub></a><br /><a href=\"#code-websemantics\" title=\"Code\">💻</a> <a href=\"#doc-websemantics\" title=\"Documentation\">📖</a></td>\n    </tr>\n    <tr>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"http://abhishekchoudhary.com.np\"><img src=\"https://avatars.githubusercontent.com/u/61597896?v=4?s=100\" width=\"100px;\" alt=\"Abhishek Choudhary\"/><br /><sub><b>Abhishek Choudhary</b></sub></a><br /><a href=\"#doc-shreemaan-abhishek\" title=\"Documentation\">📖</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://github.com/JasonGoemaat\"><img src=\"https://avatars.githubusercontent.com/u/114062?v=4?s=100\" width=\"100px;\" alt=\"Jason\"/><br /><sub><b>Jason</b></sub></a><br /><a href=\"#code-JasonGoemaat\" title=\"Code\">💻</a> <a href=\"#doc-JasonGoemaat\" title=\"Documentation\">📖</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://github.com/mbodomi\"><img src=\"https://avatars.githubusercontent.com/u/390802?v=4?s=100\" width=\"100px;\" alt=\"mbodomi\"/><br /><sub><b>mbodomi</b></sub></a><br /><a href=\"#design-mbodomi\" title=\"Design\">🎨</a> <a href=\"#code-mbodomi\" title=\"Code\">💻</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://orkhan-huseyn.github.io\"><img src=\"https://avatars.githubusercontent.com/u/21221412?v=4?s=100\" width=\"100px;\" alt=\"Orkhan Huseynli\"/><br /><sub><b>Orkhan Huseynli</b></sub></a><br /><a href=\"#code-orkhan-huseyn\" title=\"Code\">💻</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://melcher.io\"><img src=\"https://avatars.githubusercontent.com/u/35605787?v=4?s=100\" width=\"100px;\" alt=\"Domenic Melcher\"/><br /><sub><b>Domenic Melcher</b></sub></a><br /><a href=\"#doc-LetsMelon\" title=\"Documentation\">📖</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://github.com/a-raccoon\"><img src=\"https://avatars.githubusercontent.com/u/1052090?v=4?s=100\" width=\"100px;\" alt=\"a-raccoon\"/><br /><sub><b>a-raccoon</b></sub></a><br /><a href=\"#doc-a-raccoon\" title=\"Documentation\">📖</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"http://about.me/peterdehaan\"><img src=\"https://avatars.githubusercontent.com/u/557895?v=4?s=100\" width=\"100px;\" alt=\"Peter deHaan\"/><br /><sub><b>Peter deHaan</b></sub></a><br /><a href=\"#doc-pdehaan\" title=\"Documentation\">📖</a></td>\n    </tr>\n    <tr>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://ionicabizau.net\"><img src=\"https://avatars.githubusercontent.com/u/2864371?v=4?s=100\" width=\"100px;\" alt=\"Ionică Bizău (Johnny B.)\"/><br /><sub><b>Ionică Bizău (Johnny B.)</b></sub></a><br /><a href=\"#doc-IonicaBizau\" title=\"Documentation\">📖</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://www.t1st3.com/\"><img src=\"https://avatars.githubusercontent.com/u/1469638?v=4?s=100\" width=\"100px;\" alt=\"t1st3\"/><br /><sub><b>t1st3</b></sub></a><br /><a href=\"#code-t1st3\" title=\"Code\">💻</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://github.com/timhudson\"><img src=\"https://avatars.githubusercontent.com/u/122594?v=4?s=100\" width=\"100px;\" alt=\"Tim Hudson\"/><br /><sub><b>Tim Hudson</b></sub></a><br /><a href=\"#code-timhudson\" title=\"Code\">💻</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://github.com/Lev-Shapiro\"><img src=\"https://avatars.githubusercontent.com/u/96536068?v=4?s=100\" width=\"100px;\" alt=\"Lev-Shapiro\"/><br /><sub><b>Lev-Shapiro</b></sub></a><br /><a href=\"#code-Lev-Shapiro\" title=\"Code\">💻</a> <a href=\"#example-Lev-Shapiro\" title=\"Examples\">💡</a></td>\n    </tr>\n  </tbody>\n</table>\n\n<!-- markdownlint-restore -->\n<!-- prettier-ignore-end -->\n\n<!-- ALL-CONTRIBUTORS-LIST:END -->\n\n## Release History\n\n- 2025.04.11 v1.8.1 Added miniwi font.\n- 2024.10.08 v1.8.0 Added support for promises for loadFont, preloadedFonts, and metadata methods. 5 fonts added: DiamFont, RubiFont, CosMike2, BlurVision ASCII, and Shaded Blocky.\n- 2023.10.01 v1.7.0 Added support for promises for text method.\n- 2023.04.08 v1.6.0 Added npx support (ex: npx figlet test).\n- 2021.08.11 v1.5.2 Minor bug fixes.\n- 2020.07.12 v1.5.1 Fixed with vertical smushing, updated lodash version.\n- 2020.07.12 v1.5.0 Added width and whitespaceBreak options.\n- 2020.04.26 v1.4.0 Removed jQuery from preloader and examples.\n- 2020.02.23 v1.3.0 Added the \"ANSI Regular\" font and updated the README with info on how to use with Webpack.\n- 2018.03.26 v1.2.1 parseFont works in node for adding fonts manually\n- 2016.09.27 v1.2.0 jQuery replaced with fetch API / polyfill.\n- 2016.04.28 v1.1.2 textSync now works in the browser with font pre-loading.\n- 2014.08.15 v1.1.0 Sync functions added.\n- 2014.07.31 v1.0.1 Bug fixes.\n- 2013.12.28 v1.0.7 README update and minor tweaks.\n- 2013.01.02 v1.0.8 Added tests and command line info.\n", "readmeFilename": "README.md", "users": {"abg": true, "ubi": true, "vbv": true, "dofy": true, "j3kz": true, "vaju": true, "akiva": true, "t1st3": true, "tttai": true, "xkema": true, "ackhub": true, "eseath": true, "ruzzll": true, "shriek": true, "wickie": true, "yuch4n": true, "eli_yao": true, "preco21": true, "samtsai": true, "timwzou": true, "ungurys": true, "yanghcc": true, "alexwang": true, "gurunate": true, "huiyifyj": true, "itaditya": true, "johniexu": true, "krabello": true, "stanzhai": true, "vlasterx": true, "asawq2006": true, "azazeln28": true, "codepanda": true, "ctesniere": true, "geeksunny": true, "goliatone": true, "herlon214": true, "landy2014": true, "ptallen63": true, "subfuzion": true, "wolfram77": true, "ahmetertem": true, "azharakbar": true, "douxuesong": true, "jakewlacey": true, "jkirchartz": true, "lius971125": true, "manikantag": true, "monolithed": true, "pedroparra": true, "rocket0191": true, "santi8ago8": true, "ahmed-dinar": true, "flumpus-dev": true, "hal9zillion": true, "jacktan1991": true, "michalskuza": true, "mikewesthad": true, "mseminatore": true, "nikostoulas": true, "raisiqueira": true, "soenkekluth": true, "volkanongun": true, "davidbwaters": true, "ghostcode521": true, "joshdoescode": true, "justdomepaul": true, "naticarri441": true, "tamicejas141": true, "zhenguo.zhao": true, "deantawonezvi": true, "juliamerida41": true, "ocamposroxy41": true, "peter.forgacs": true, "joaquin.briceno": true}}