{"name": "@npmcli/metavuln-calculator", "dist-tags": {"prerelease": "4.0.0-pre.0", "latest": "9.0.0"}, "versions": {"1.0.0": {"name": "@npmcli/metavuln-calculator", "version": "1.0.0", "dependencies": {"pacote": "^11.1.11", "semver": "^7.3.2", "cacache": "^15.0.5"}, "devDependencies": {"tap": "^14.10.8", "require-inject": "^1.4.4"}, "dist": {"shasum": "ee71f101581e36058084b533f99ec3e6c7ee7357", "tarball": "https://registry.npmjs.org/@npmcli/metavuln-calculator/-/metavuln-calculator-1.0.0.tgz", "fileCount": 7, "integrity": "sha512-BzFNWElLl99WqqkxBWHPBSZbKGbH4qJa0vICgRff+PWl0nIT0nDn0wJV3EBCDDWnrVqomH29ZENZc1SkRQ0C7A==", "signatures": [{"sig": "MEQCIAeJ2bbCNman3zmnrwIY4A5F3TdsjMv0acFq3OgGg7xsAiA/8l9CiweFBQTnaYOyOVyWJ2vwonJ6wMtx8Hg7eXy0Qw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25598, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfYV/SCRA9TVsSAnZWagAAEMkP/3Iy+VQv9mmwYlaz9AE/\n4472pw5P+QD40tvw7fuDs+aR9a7fK61xvWJ1CXTZbh805ptfvsbbGbk4JvyV\nGntGVVfsnmuA3HRMm/9QD2PoHX3/qXbQf32M3WU4JkN5s5kpOqpSe+BvFcLl\nlOfN1+9uELDa48mRw7NjPH/YlhPSnTmdB0fBqtDc1EE8T3nds+9yJMsfJjFQ\nnTwiWXtcGxmQ0K7Cl3TH7bE2fg0eidp1RnEIKsigy0+QKN/3zXJWo009+IZV\nXMBF0geJPHr2F4KG1csSuvp5oJFP8g359kfq5Cxm7Vg2rJVQSgJhUrgjshYE\n3DH6UbH84AqK3ui+KZUZGFMqhwFzV5lrW1qnUUbOLB7DRCgcoDdtzMDq7tuj\nQx/f9s1GzCzDVV52pHh/4a2fANaqg7gn6JvATUK2dO9bNjbG+Yn4h7jSwFDB\nsUWOAkQm9zQOEM3v+SJSmAzAQypdW0A3e2aceLlTIYJ3KpsBHOmtIpU142xq\nZRVuxkVgAC60BgiFggM27xPf+QYH29xC6GtY1R6kk/8uK6HooXXSr/9bwp7N\ndIEXYb6JdjBZG6rTMslKEAViKvmG0FKpBFTptHY6GwAB7IKTrq68iPLQDPJR\n/U32ZeccyVjopE6u0hEzUmYQeYm2/d1WEUQXMva3iwYgFuheznDMhdybE8Q+\nn0wq\r\n=VsmS\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1": {"name": "@npmcli/metavuln-calculator", "version": "1.0.1", "dependencies": {"pacote": "^11.1.11", "semver": "^7.3.2", "cacache": "^15.0.5"}, "devDependencies": {"tap": "^14.10.8", "require-inject": "^1.4.4"}, "dist": {"shasum": "2f8bfcd7b7ad4a2a95adb78dd7a7d0896015f14e", "tarball": "https://registry.npmjs.org/@npmcli/metavuln-calculator/-/metavuln-calculator-1.0.1.tgz", "fileCount": 9, "integrity": "sha512-ezAi4lvICICG613CPvavqCn76jjkiQS+Hag8qMQInLitEjIyzVBud6ATfYIhDcH3d8RnxtMXe3kvKs6+JqTnJA==", "signatures": [{"sig": "MEQCIAvK4sX5ovVEqqT2fECGiJ6DLl8kXln6gbM1BQVWQBqHAiBibO+2kf7PbOTEIhylfC7Ws+yxp1dljGIDaZ7t9baC3Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31643, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf/4xUCRA9TVsSAnZWagAA3jIQAJEYaIjRoD9/FVnxjqMk\nayfCYiqZqGw19E4LTyGdOcSkXhxq//9fmj6PfnPs0nUMu37qip2enEFY/RLJ\nUpcJRHnXwRE6MBVHUbauqoqtZ1PYuzYzM9YwUVTvRFR1lsUIotZMvYqIaCFH\nRRnAaQYFeD34LYCxhNY7HKm3qQSJx4knFTpkdw2wMgdxQlblFohqCBkNRgq/\nZsEj5pOGxPGDPOIFoQ5saY63zPhxtPpJFNLg7fVl7JEehdvVbCnNlMc7qIUS\ni7oS7FxmdmOjvUteZkQfpUQrjbXhWArvrxO9xtebP65D2ypFCTdK9EhaVTH1\nRuR7BAwRsBlp97uFYvQqIjduyMQ61kTkpaa9M9+HnUPwY9UMEcgOZtvePc5R\nLKLE/vvEkb26FIfHE96Fq7ugkFjsKNDcXXpv2fpDJfFgIl+iJ0uas2tRVXmF\nfvjPdoM4iKSnALLoi5WlCzpkw61Q34wK9UHjHzrM2rBxxcdToknU6m/sV+W7\n9z5nEnzWmQeX2FIBWxv7x1JL07f++toXkxoS8ofIhskKYVqnIT6AS0VHI32p\nF5RYw0wR8R+mt/dXpPGiA35OFcCW5NC1aePs8LnnY+QZoUN0gQT6ADMbC4ym\nJaijHfEqQN1ZYm2ra3SOkVKHXgR9SzvHkQnUjBCbF+rebP5Y81I5ceK21pCZ\nJRBo\r\n=Gtw1\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2": {"name": "@npmcli/metavuln-calculator", "version": "1.0.2", "dependencies": {"pacote": "^11.1.11", "semver": "^7.3.2", "cacache": "^15.0.5"}, "devDependencies": {"tap": "^14.10.8", "require-inject": "^1.4.4"}, "dist": {"shasum": "4332b3840c95c1aba1a518bab958cd9c6b870b56", "tarball": "https://registry.npmjs.org/@npmcli/metavuln-calculator/-/metavuln-calculator-1.0.2.tgz", "fileCount": 7, "integrity": "sha512-mQuOq4sZYOdjz49KH/DUwL+FsLVxiN5KDO/bnBXFon+kUxGHDoUYL+bvOD1o00IYL1q3LtXoPlFlQ+OYJQffhw==", "signatures": [{"sig": "MEUCIACjNG8CrcFf2RLUoPa8QzCssZNa5rkkQqUR/Z8IJK9OAiEA7Iv/E9nDw1wYtLazeQ2zlmE3a+7DDKk7xvTsvK9aq4Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29332, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgAfEDCRA9TVsSAnZWagAAfEUQAJJ221lgi88zXC+DWX3b\nMBPTDVtomSmmfltaohZNzs9FrByR6ixfqit7JltByZgbmuivaQZ3whWIAugz\n5Hgn0bzVCOuU6x0vYObqicqDg3LxuoKIpgzMX6GEcyvGqqOo304LNIogo8MA\n10nIGYR9P+iCShe7/ftxJt0dUCdf3TBNlgBYyy+eDJIJK/cehO9toXhdrP6N\n71YSKZWJTmbwBrywcOuo5/FP0FB9i8CB6zw11MKsKqTYPThWcc0EgH//y3rF\nd9ZimWw+BGq9R5mhoiAlyUr0UsL8p9rL4sXwaJddCwPj2ECq8aDZfEal4zA9\n/cRW5tCPB3fa4Tost/4mAaS+614PvJ8c0U5wTmKxsParqO6HYaEKNBOr7Yd3\nPg0g6n4hqL48xmBhUJruA28x2rwI8t3BB8B6r28tlv/XUOlgWwEsskKRJsTT\nnmXZqdA+BZ63i9QBlKDmUPDTkghZ4To12QFoJMw3X3pxjCqprseEdJKLErPV\n3YLwV2yiDFkgksSk7P3U2sB/0dI89gResUTZGdJrlgvOuyYOeN1V7yVn9FjV\ni+PFHYZ5501i7tTTlSmv7xFP+KxW1oQzOwDsaG5uFHjG+wpOesUQt8MZqIVG\nHku3D3C+D/04bQEEZnzTPYJSfWVUI/TnqilWrHRLdxax1gZctlDox9W1R2sV\nT48c\r\n=vzgf\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.0": {"name": "@npmcli/metavuln-calculator", "version": "1.1.0", "dependencies": {"pacote": "^11.1.11", "semver": "^7.3.2", "cacache": "^15.0.5"}, "devDependencies": {"tap": "^14.10.8", "eslint": "^7.20.0", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-promise": "^4.3.1", "eslint-plugin-standard": "^4.1.0"}, "dist": {"shasum": "61fbb8a70b618fee5a0ba818018b0476263e523e", "tarball": "https://registry.npmjs.org/@npmcli/metavuln-calculator/-/metavuln-calculator-1.1.0.tgz", "fileCount": 7, "integrity": "sha512-fb51<PERSON>yiWHjeqqFez9FXhvr+E2Dv4ZjPGVgnj8QC1xjHRSw4gMRIO8pNCzU11WYQ2wZxoHBhPMgovZGxP5lP74g==", "signatures": [{"sig": "MEQCIFXVmjCRuG70wAn8QaJw39ggmQy4y6Eesy/QrHb4sapGAiBqQN1ueo1wB2PE+++BEhh+6Q26vPdjh8mnymDOlpyHVw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29745, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLuY9CRA9TVsSAnZWagAAbDwQAKQxyXPV452RvlD28mO0\nZi6vb2KtUgcvaxjQnIOihXjlnFfQ5F5x6zub3geWr9gEQeh9N8NzaFFrKgHG\nYfGBTRj477prZ2wOyYVaHUO1bMbOvyH/jEZE3MTUh5Z5DV416UeEZhnndgkS\nvjcW3lNWH/BiMXfczPdT1UVZPp3cnfagHp0auSD//zl0tTL6rsNHcTOi80fC\na3xTDKJ+KP50r7nVDRRPiXGxuG8sNUy1ZctqOCUJtCWcgW5TP3iSkcG0Ts3u\nu+CTRMaz3wvtllFXlUiawo2GyM++7wYn3AgykiucwvzeYwETpDeP0LsJh4l8\n+NiUIpUFjDDCwI7ovtmsLR+WmExdv0IHNcIQsy0Oc0oLXngO/U6OujGbFI6d\nnNf0MrUE2ukHSvpucFNllK/7xQnfXPXwGAesvXXvalflM1RX2FO0YVCvezRe\na0rSEkGY6hISlnxNg/pDEmPowDNzAHtxK7CzQwdo/uVr7GcETLf6GCSn4u3q\nlTaq4IWtmYRi4TDDWD+8zK9tBEF8jZoLQ/4xbKNnhzO6LdPcanxoGMtH7L/+\ngk2sybyVXTdU/InOaDGAPVtmOnOyzrbKohD8t71Pfn6cD/b09S+Wkx5UIRs3\nmI0oY88zwH0n6xscGtI20nWlL63Vonns+JVvVzlBWeow0koVEKqnM7TcXnaS\nyB+W\r\n=Q3Ks\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.1": {"name": "@npmcli/metavuln-calculator", "version": "1.1.1", "dependencies": {"pacote": "^11.1.11", "semver": "^7.3.2", "cacache": "^15.0.5"}, "devDependencies": {"tap": "^14.10.8", "eslint": "^7.20.0", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-promise": "^4.3.1", "eslint-plugin-standard": "^4.1.0"}, "dist": {"shasum": "2f95ff3c6d88b366dd70de1c3f304267c631b458", "tarball": "https://registry.npmjs.org/@npmcli/metavuln-calculator/-/metavuln-calculator-1.1.1.tgz", "fileCount": 7, "integrity": "sha512-9xe+ZZ1iGVaUovBVFI9h3qW+UuECUzhvZPxK9RaEA2mjU26o5D0JloGYWwLYvQELJNmBdQB6rrpuN8jni6LwzQ==", "signatures": [{"sig": "MEUCIQDJvOgYhviurYStPGBgBEGfvdeMoqupHZo78v0FT+5w4wIgEAzuAnSmFZbdLmlNwbzAI4UMwjy0a+Qfeb5BdQoovM4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29776, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXh7UCRA9TVsSAnZWagAA2QwQAKNwCrZvTPY8jaw0SF/P\nfE2kzvqK4Gl2miXH6Hx1OKEOGbUYkEKA106V5JHVCy+AaMB4C0Y2c5eUBNjs\nUTN0pZHWjXwDvZ1mdnJbA8yaiQDjDomu1Wt7fe7j08EwzkHR9eLyGuSpsU+8\n45Jgrw9wWnLN8yOVv6wjnKlFLsvv0+FApqOcLXPmDXfd9BsmHaVlgIoisnev\njVuAr7L6YpTl7ZOtVzcNQM6OdxXfqgzOzqob/H5RpeaNC6UMr3pARsJCoPlA\n0oCIFnJUYJb1TGcOwPnFjBWggCHfyL549PpdSr0zkWmcS79Vdp+mor6NjU0A\nfmqBr8EkYt4Q9VWuZ7svxtj4Rsud56tTd2RBXv3O54mTU9baOoVaHXNdmw/d\n5aCW4pa42HbcGhLgc5qnpIdY6dAvTr0fSN4/ZTbjRmhwpk9KP4Bd10IY2/o2\nYN8gAoEbkurzEMXEd+KxCPJtU2t1t082PjB6sY0q6QEBHHCtgSLyWxz+aDxj\nV3VwkJVSMgXXjH7Fqw9zhHeOoDQ/wIh2+7iXosvVTiXgd0tr4sGYLgf+IpO9\ndIzC0BsibOnLVlLoDugULrSE7Shm7kzwz2xvZYOJhjQq6Kc8wZyZPdsqMOYb\n3p/5LPG8D25KRvyW9SCCjMLAzi7OTIg+IHEM6mJnBcQ8bq5SIClqtElSw89O\ns6X/\r\n=4n5u\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0": {"name": "@npmcli/metavuln-calculator", "version": "2.0.0", "dependencies": {"pacote": "^12.0.0", "semver": "^7.3.2", "cacache": "^15.0.5", "json-parse-even-better-errors": "^2.3.1"}, "devDependencies": {"tap": "^14.10.8", "eslint": "^7.20.0", "require-inject": "^1.4.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-promise": "^4.3.1", "eslint-plugin-standard": "^4.1.0"}, "dist": {"shasum": "70937b8b5a5cad5c588c8a7b38c4a8bd6f62c84c", "tarball": "https://registry.npmjs.org/@npmcli/metavuln-calculator/-/metavuln-calculator-2.0.0.tgz", "fileCount": 7, "integrity": "sha512-VVW+JhWCKRwCTE+0xvD6p3uV4WpqocNYYtzyvenqL/u1Q3Xx6fGTJ+6UoIoii07fbuEO9U3IIyuGY0CYHDv1sg==", "signatures": [{"sig": "MEQCID7HIbRDvHc8/4wm3E6+GzdVvFYlpc1b21sr6tuXD5H0AiAYrcQKTvr+dOCJRW+/TexWXvaktFeeP80lmxzvcP3ufg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29885}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "3.0.0": {"name": "@npmcli/metavuln-calculator", "version": "3.0.0", "dependencies": {"pacote": "^13.0.1", "semver": "^7.3.5", "cacache": "^15.3.0", "json-parse-even-better-errors": "^2.3.1"}, "devDependencies": {"tap": "^15.1.6", "require-inject": "^1.4.4", "@npmcli/template-oss": "^2.7.1"}, "dist": {"shasum": "25b47f7dc3c027ec561d8d46ca4fbd732439531d", "tarball": "https://registry.npmjs.org/@npmcli/metavuln-calculator/-/metavuln-calculator-3.0.0.tgz", "fileCount": 7, "integrity": "sha512-tIzAdW3DAvlyuQyYvy7WuDKaJs55LoXFAIyglZTrHsc9DGZWP1YVL7+8WFKqx+lHyHUEkfk02Dc8ie4JWtNO6w==", "signatures": [{"sig": "MEUCIQDEzNT4ALW1ls569vcQfrycF6LRNnsL5hM4RdrUARqr4wIgTQyvV+B/9kljbFb6vchbRQMpCdGNuZbnloyx4DVxSDo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30133, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDUzsCRA9TVsSAnZWagAAqgMQAIHm3ymhzzcg+4DaIe2T\nJ9L46wm31RVQoatYWQ83jT+YDk4MD7xS834yQl5+OGCjZ29d1r/X+kvF65lb\nSBSMz9/kMq15RcCRubKYKGKpTrtQbJWBKvPY17CtGbo83t8gMrOwG8u/DTlA\nPq+Djmh6q3kQxWZ3MSCMWKKMiDSPi61LWrPj+HP2OFOlipSSqsn5EgHCVhw6\nydsUBvKaRoq2gvFBBLsPMywzP93pkGfmprDLcvdgHRmdaF5wV4P1ttnsFrWu\neIi13hkpmcsICHCtQMStGDjbqbXAXE/SLC8C5dtv8j3YTIaGGu8OQkLuZDiD\nR3nBLuZlESC692bz9PiNaFUASB8bS7HnvsWfmGGC44+v5pBgTaj74WYrXZSf\nqxG7dFPQxwjEq+TNR4dmLsrYTL3vNK1pjv2YyHMhHIlF9+CIzTiDfCogJahw\nO4L0c6UdqL70tqiGTz5sr5wb7kklULoVd/husbN9RUuUsI6f2QSbuzWBJnqM\n7WOdu2QQ7YjIN38bjaLeteA3HbfW56Mfrr2dyFpXK73foIC9Ou9E7I0P/H32\n5KpaVOKUtbDq1sPdo0OYNYhnyEpPSZm2cJW68JgeFXjI/T3JC8QjUuhiLTTG\n+u6OHLa1SLE6Aqko+Tm3wdc0+x8MS486nTn/FMQ8QJP1diN+GRfKBqzcBlk3\nyHSB\r\n=n/+R\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "3.0.1": {"name": "@npmcli/metavuln-calculator", "version": "3.0.1", "dependencies": {"pacote": "^13.0.3", "semver": "^7.3.5", "cacache": "^16.0.0", "json-parse-even-better-errors": "^2.3.1"}, "devDependencies": {"tap": "^15.1.6", "require-inject": "^1.4.4", "@npmcli/template-oss": "^2.9.2"}, "dist": {"shasum": "b486beee5f4ccde54b138f1e399aa0da8b4ab694", "tarball": "https://registry.npmjs.org/@npmcli/metavuln-calculator/-/metavuln-calculator-3.0.1.tgz", "fileCount": 7, "integrity": "sha512-XnaD5UfV/qQoIEPWnfBntw8Ik5HWkxEc1wCmfHxhogdj06bwP51nAyU3QLBdhnFsmQQElqV0S8eHXn2zEXnSZw==", "signatures": [{"sig": "MEUCIQC6Ne2pmG8o9x2raTX7gTJcE8ZifeenWjohf4ieA+RoaQIgbZOUkSLcdWkYSlPUQ7IvxxRD4rQPPJE9vuYTrEZ/y1s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30133, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiL6lFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpiNg//cIIRvX4KcCfTbBkr3UgAE97nh3md71jBFWYYZv6GBFJk+2Q/\r\n7W4ZuWhUs1GMmzuwovJVgOlzVrR9+JAlYmwr5XbKW2+RZKVRkTSVE9UDQtEk\r\n45wCqmLsHAG/VIAkXYlLKva38tBfx0HKfApOwVb7qxCPCpQeHZEWdoxpmgt8\r\nxXeyxM7IDuZ5sIXTLSiLxVJRwyLPM/PGPACOrJW7v/B864qUwn9wt5/4VuAC\r\nNCHsiWEG1g8lQgYjT7ItSbeSaWqpposEg9DdU1ZZi+93HpdxpHaCE+t/vZWT\r\nY8db3pH/djuGGJ1Jp9TnrWyroWj75CQZPsFj3HpHL0xv1sYehk80Y0nwLNED\r\n/ihPgMdKuSpY+ClcGkzVSWSSdZ8ViV8128LYpbswviBsSYqUYRHQ4kxyp9x/\r\nSND/POFVnGLeOdq11a4RAgh3IGyg8WTt+b/D5oEuzwygehdIUpyACTrqMyOp\r\nlNfQaQfVyryxeKafhPzGm+j0dkM36gTpW36Woyxgt30puwfBdCZmlyqSHHxY\r\nKQikMpeaCG9UHci0h65Sqo7lj1ab4ThDp/Bx467bsodDNBy/WpW23UiAqR4y\r\nUZzLMmZ5eWMc5GTXzuMA46KcH1tUzX9HNGWyKg/TPzNONbK1Gqk3Dh80W9Tv\r\neqH9vSQ7AgHP15Gr8gpF0+HdUBy+A9PzYzc=\r\n=6QNw\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "3.1.0": {"name": "@npmcli/metavuln-calculator", "version": "3.1.0", "dependencies": {"pacote": "^13.0.3", "semver": "^7.3.5", "cacache": "^16.0.0", "json-parse-even-better-errors": "^2.3.1"}, "devDependencies": {"tap": "^16.0.1", "require-inject": "^1.4.4", "@npmcli/template-oss": "3.2.0", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "b1c2f0991c4f2d992b1615a54d4358c05efc3702", "tarball": "https://registry.npmjs.org/@npmcli/metavuln-calculator/-/metavuln-calculator-3.1.0.tgz", "fileCount": 7, "integrity": "sha512-Q5fbQqGDlYqk7kWrbg6E2j/mtqQjZop0ZE6735wYA1tYNHguIDjAuWs+kFb5rJCkLIlXllfapvsyotYKiZOTBA==", "signatures": [{"sig": "MEUCIQDmFxJB4QRQhT5i8Fd29wRn1X4EkjW7NkWahGO/B4KYuAIgKsLNsEmFFhWcjadvp+hla7/H8KhQCTDNFKVUyJiZWo4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30371, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiSyzgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoElg/9Hlcb1hOe19US6lJkXO5luM7BEvbocHZwCIKXGCpkGhowDZ1J\r\n72wZ5dEKYiY5gR2m8AMV4lMx4IWXXSme1G5mMg9IqWHo2YaXHu3Vji5ZeG1e\r\ndapqreSvJV+jgDI9OuxYSQEdx5wvV+uDhF0/2+xxWNmirK4j11AX0cQ830P4\r\nJ46g/5nyWA3qZRQrRdxhMBqsdNC34JXsVB5+iCDWPZMverKdlYerQy/lbN5A\r\nACMh5dAIdM/3nYrcyfU+rIdrBI5hoSnJmvdTU+bV1AmW5kqiBgEQ/BSHOtKu\r\nLTWiXX/Bhfv4uyidXhv8rtVHtIJ8Wh0w42g3sLRRdVTKmw0gHE9ZVqw4mBiv\r\ni/oAAdOuseXv1t7I+PXvC6jU4g6/QNs+sj4HpgJSUteAq4JAaycupF5SFV5V\r\nI7V/P21hMWSiP97Ivms4lF94NFLz7uuoJBlpesNGgVqjzcd+f+aJWgo0xi0H\r\nNTg5EeLe5duewXczXzYzgVlOV3GSrcQbB53wZ8rP5v7sbDJUx5sWzw+HRMnA\r\nFHhTg7bDZaf8eQTMb3pCIEiNdpzv1fL+u/GTislM2TOxUKbkSCuvHaCNE40I\r\nw79vo7xpUvTUJYDufTtH00QN6tm3x5yb7ZKdWBHPUQbsz/Qp4SnUOkgdKNP3\r\nA+lhpi4Atl40iznwiKCX7MNb3XWbofhyffw=\r\n=nsvs\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "3.1.1": {"name": "@npmcli/metavuln-calculator", "version": "3.1.1", "dependencies": {"pacote": "^13.0.3", "semver": "^7.3.5", "cacache": "^16.0.0", "json-parse-even-better-errors": "^2.3.1"}, "devDependencies": {"tap": "^16.0.1", "require-inject": "^1.4.4", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "9359bd72b400f8353f6a28a25c8457b562602622", "tarball": "https://registry.npmjs.org/@npmcli/metavuln-calculator/-/metavuln-calculator-3.1.1.tgz", "fileCount": 7, "integrity": "sha512-n69ygIaqAedecLeVH3KnO39M6ZHiJ2dEv5A7DGvcqCB8q17BGUgW8QaanIkbWUo2aYGZqJaOORTLAlIvKjNDKA==", "signatures": [{"sig": "MEUCIHHToL0EtFSUe3DEBreyegd64QEetTvlL9PSN/W8TaD2AiEAwUTDLEeI3GggUOnuuWDzNU+EB4mQV8yT70nB7irdF8g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30393, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivLzyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqefQ//ZCG/B5Lgy5WhClNiFQoyAGtp4RZTYwFXwEkimgeHMEykvdn8\r\nI++D3YKTU6XHVMHcEJECu0BOzCqp+WEZIvrhdOV4ueHIfCDkDStsAnMaTK+c\r\nYg693cG5ZzTgzA6TufdOsaEgATtgwSBGwqAeNIoxPELb1yieggwnRT8L12iv\r\nvyzXnCCW2EGVMNPFY6uU65vyEC2SwJQIpsMRFSFKZYZnsU+ZtVmgOxG3uV+y\r\nES/bOjQ/jh59GWFYxQZaCACBxs0Y/NHfi9mAfhaxWT46Uvd5qJS7x5WRz01z\r\niQ0IgSRO/M3Pn83y8SAT340COGH+vuxYFtuKZsNpKTO8+Yz3YE7XRVQNoXPC\r\nbOCMuKHGmuu8qL5GMrYRhiSIfad7zFSycFGDqxN8J4+b52Eo97lq0Lq2zwjz\r\nfICjUPy88c9IredmC+8+t7noJv+ZVLrrezRHDqkkYQLPiRYmk2TYQ8Re9y32\r\nCO7HCNhtDXIRHMkGrT3o3SiSVaGTeUD7gwPT0ABCPEZKBbQH0cb6/U3dQqM6\r\nANMCBlWdpkXnfPIIlpHQkefSOKF6hN+EriL+OS8hOGKGx6RXI4q+9p4efDht\r\nTO2XuHDleE+wpPbAirkAJYMZnB189Uy2sAenL/rnMw5gpHE/qqFvR7PH/sqF\r\nG+UW2GdsFYsTk9BGRfbVAMP1xCKRUYg1UWk=\r\n=mYMQ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "4.0.0-pre.0": {"name": "@npmcli/metavuln-calculator", "version": "4.0.0-pre.0", "dependencies": {"pacote": "^14.0.0 || ^14.0.0-pre.0", "semver": "^7.3.5", "cacache": "^16.0.0", "json-parse-even-better-errors": "^2.3.1"}, "devDependencies": {"tap": "^16.0.1", "require-inject": "^1.4.4", "@npmcli/template-oss": "4.4.2", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "f9459d652ae497f97f7c596d0118de80fe3991e9", "tarball": "https://registry.npmjs.org/@npmcli/metavuln-calculator/-/metavuln-calculator-4.0.0-pre.0.tgz", "fileCount": 7, "integrity": "sha512-2r<PERSON><PERSON>hovlcZMkqKm2cOWuZ0YsXIcP3iARsm+aYn/SLXK9aWRMVTW1f4fpDjtSvkZkaQVr48ofSG3YLYwlersSQA==", "signatures": [{"sig": "MEUCIQCg9a34UsIDgmxvNqWgvKoDmOVi6RGZRbh05FupHCqsNwIgBNNanRdhF8q1LC7Ma12ENjwevWaad/KA2cDsL0ICwEc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30365, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjLRGiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpBjg//ffDGVJ72rycLm7qvaJZ7j4KCxahrkNRxC+Gtm21eVUG4c7EQ\r\nhsUxh6REtCKUGl12+lW9skxuB336ySsPhnJZDABlwuBvGLR2KeFO1UngE3rn\r\nvjD51R+dUoMCdFhkq9bOPZAVMXJ5uER2PeAA4ztYP2o/6Bt3d35Bf7RtbOVg\r\n+CRQRoA7I8TigclrQJifhyGKMBcJW4KMqM75DwGnvAjpBHYVAId11kVwb+jc\r\n2UU47vR2h+x+OCnQGyWQ6ZkxtkFitqd79EIlgIl+qdIoU4FPrpVhY4qMvmjF\r\nxv3wI/kKSq/S8hnqzeg8QF+0sYXLqYIPrzKWpqDiFtNGReAsc6DPGYHti/jJ\r\n458ZwAb2G2nVG2YJBpEcnyXQFyRsDu2nXIJF6oo9RE8rm6Z70pCKJ8LaIRgX\r\n9lyMuFKH97yY/NhUtd9RQ5bT/cLYbYmhhqoIer/vlLRzpZy3xPOQVoqtXSUe\r\n7JG/NxxkONwHPxX09E3l5zUVw7U2JdHIvQ+L1bAX6uvGd8Gri8mY1URgHCPn\r\nK3WUjQZ6+aq5NV1ihwrVZbkKvLq1A8sV/TiYF7LglSFZ2P5EYHPOydqKV70p\r\ncLsuE+CrATsJuf7a5XvwQsLiW7YHV36BvK5MksCISmN2Oq6ThFlHnKokrYFL\r\nh9Set9yMM9ykFgzWe3AAXIzlls6aWgTpsk8=\r\n=t66K\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "4.0.0": {"name": "@npmcli/metavuln-calculator", "version": "4.0.0", "dependencies": {"pacote": "^14.0.0", "semver": "^7.3.5", "cacache": "^16.0.0", "json-parse-even-better-errors": "^2.3.1"}, "devDependencies": {"tap": "^16.0.1", "require-inject": "^1.4.4", "@npmcli/template-oss": "4.4.4", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "73fb20696ff6fec61fefe98854cf84ab598fe76f", "tarball": "https://registry.npmjs.org/@npmcli/metavuln-calculator/-/metavuln-calculator-4.0.0.tgz", "fileCount": 7, "integrity": "sha512-RHNlpbXW0onc8inOANEC55m4zjRbhw11OwAof967MgzQauLFjoCAANhx3Ugvse/mXglb48uS3Jy+XjbYPOuqsg==", "signatures": [{"sig": "MEQCIFJl0KheP6WKZgYKm4QnwlqCDDi1x8S+XbvqBs2KofrbAiBI/avdWgetun95oy7ZdEUZmS9K8pfYm2npyqEKUPa7Yw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30342, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjPb+lACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmparA/+JjwTgFs91u4/sp6JTIwzivrHQG4/liqHugcUWlGhEdV3kU0a\r\n6l/ZVSnE9Uj/p43QUnBKPn4kOfPLD2o/RrFhr3RlDDlOqX58CiFupoELv1TX\r\nGm9EzdWKKBuMreilwk/RLOjcVeB26k7gi2X6nAWhsv8ZTPfT27w/x3i1L+73\r\n+Uq/2vOZY8pM3sY+E3fPBTKhdcbGnto1jBilx8BKo8I9A26DhpimAiqPmqZ7\r\nbskEaSdObYbL+A+Qtx1axViZWTI+EjSpWF+slYfq5A1zlt2502lGL40QAfPX\r\nB5iwj2KIeFHS07zgg+uEKottX8HHG6XEx9ymOo1gJsUpwDg6P2o4Ne3nF+fS\r\nHB+/ZDuQMaasrrupQCzUGD42dCLH8idO/wyPUZyJjP8sJSbO9V6vAdVMie7Y\r\nelUoE8q/uTHlyd/58p44R0F38A4nEzL5W6sBMJSBgc60KK551XeI6l/1RNa7\r\nO4m0na53S2R6nq0FQ/utcvwoE/nFOZn98NEQg9Ms+rwnOqV/wDiD5n/5ZGgJ\r\ncjuE7nXlW64S5kGIfc/7s0zRQMjJ/Wxgga0HDzynBXHvTxwd+VJQqA6fE7YB\r\n9vVRUYDETohWsO0b1D4585kU/snuWkUKRbxB2CBy++L8f6JI9hIvtLpZPOnV\r\n8CiD8yp0PdtaNtxJD8yzF9701jWiXyw1iqA=\r\n=Qbhv\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "5.0.0": {"name": "@npmcli/metavuln-calculator", "version": "5.0.0", "dependencies": {"pacote": "^15.0.0", "semver": "^7.3.5", "cacache": "^17.0.0", "json-parse-even-better-errors": "^3.0.0"}, "devDependencies": {"tap": "^16.0.1", "require-inject": "^1.4.4", "@npmcli/template-oss": "4.5.1", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "917c3be49ebed0b424b07f38060b929127e4c499", "tarball": "https://registry.npmjs.org/@npmcli/metavuln-calculator/-/metavuln-calculator-5.0.0.tgz", "fileCount": 7, "integrity": "sha512-BBFQx4M12wiEuVwCgtX/Depx0B/+NHMwDWOlXT41/Pdy5W/1Fenk+hibUlMSrFWwASbX+fY90UbILAEIYH02/A==", "signatures": [{"sig": "MEUCIQDf6T8ervouL/UQR1JwK30YycUYFg57NiB4QHUYY2aTXQIgWsVSprvMJ+HC90CsLTSqOSaPkxXOa1cFJXiNUbVTqqg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30342, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSHlMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoN3w//WByfQU37ZCx3Eat7iPSoWRrvFj/1A8bJ5kCWOhBN48FvFZRN\r\nSYd89sq5j2dS6bt6ZcK0pb9GolwJRTR5k91cTBvILk6UDFClA7iWHA2V/+mP\r\nMx6zkJfRfOf3uvqKX2ygNLl2yq7OHzUZJzV83BTUEmEVSMDwidm2PIw1jW8P\r\njc8UU40M0qRHrmBspHydMBqSs5eQym6HkY65T6vLsHWZ5jU4I0CoGyIbMYPq\r\n0iNt3F1qmGsv2MKIuRJx9kphPfbsnrKdTMEkMzImoyslhj7685uZrWJQDQR/\r\nWJdvhjEXAhBCiBVLIytO/QUAomvlpwPBSmDQ0FIyNKBPYFHxhS1eZaB73x2U\r\nKyiogIjsMUzOtxpElboQm9NYusXHgsIv6ttROXBgLSoyIB11AKlKbR4pTDfb\r\nTGqm7lB0La5mMX6g9RWU+AcJ5aP1Dn+4hfarFKl2QL2XRZoN2ZRULuktGjjG\r\nMJeEvUEyPaoUf9MFeHSAbp+sRs+12knLIZAo9cbprnjSalTPMy9QnXUD46xN\r\niL6H5ZJ7dCI2UZ7u1cDa3APV5RApYBr8JYoZCnhNg1XUVScQRE/8isYSWcB5\r\nFbA+z9GqrQWu1lvbztuMrmFlEPjQW4BoRWZX2fsMg8K9Ui9ar79kBff431OC\r\nJG7dyEZpD1G/ZShnlO0w85uCOvTgRasBQ2M=\r\n=PimW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "5.0.1": {"name": "@npmcli/metavuln-calculator", "version": "5.0.1", "dependencies": {"pacote": "^15.0.0", "semver": "^7.3.5", "cacache": "^17.0.0", "json-parse-even-better-errors": "^3.0.0"}, "devDependencies": {"tap": "^16.0.1", "require-inject": "^1.4.4", "@npmcli/template-oss": "4.13.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "426b3e524c2008bcc82dbc2ef390aefedd643d76", "tarball": "https://registry.npmjs.org/@npmcli/metavuln-calculator/-/metavuln-calculator-5.0.1.tgz", "fileCount": 7, "integrity": "sha512-qb8Q9wIIlEPj3WeA1Lba91R4ZboPL0uspzV0F9uwP+9AYMVB2zOoa7Pbk12g6D2NHAinSbHh6QYmGuRyHZ874Q==", "signatures": [{"sig": "MEUCIQD7txOCRt8BbH089IxdTQtDuweYAgJWyrcBdgvGNeyUEAIgOwnYAaADCnHiiY+dO2hyFN4AbYYKt0AWSOU7iKyxMGs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30376, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkNwXuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmodwRAAioNoC+fybowufHsD2LFUaiKmUEDZZx/ndWPEuhtkcRxHnXHj\r\ngkQ2PcF/wCgKn1vBMIdy74Z3G2nMx8QTvoG4C4DpDIwGcguZsAfMznmCu9Zy\r\nevrkxtSKyXw9kBCfTML8Ojx381wPVV4cLgoM0kY3eiDPI07NLh07VzKThp1M\r\ndlqiBSy6Ojla69YXBnsLAqW+jkzTjodAprMOmaFqT7EN0fw5/1jRT1pnbrZc\r\nJ3ym9WRvZIeNE9PSwRWYdYsIkQfLwlFY7lVV4z+Ta8Pm+zdNZUr+HPKIbL19\r\n6gv16AYi+BWyzt8mxD72hLB5VYfC55BkfiVp5DtYVK24wcUKBIEStjAb3hR6\r\nYUBqR3BP4KylevMmXyvf+L7QNNLlWguNnUFFgWqGwv6PnTTPEmqgbZt4HB3X\r\nzIgZBtclkt9cuNv9GEdplPnpk+n+C4BNnmBcNx/+QtlGonY5CUSk5Jhjro+C\r\nnMB8PH4ry7Ce4lBAUzywa/MxROFxVXzMtuuj6jskaOb1ywVLPR7nLZ/2JBYJ\r\nlGWNux6IuKP91dOupwNvf7DL3sdG8B8CPnghBm8iLUFB/iKMMJ+RVqyKCYER\r\nOlODFTidFpNA1+7jEW9ODv5cDlb8r4ibwP+PTqfgKVjO3RZ8XceO9Qm2G97j\r\nsmL/DvI+g2ccer1EwPW/+HSCEPeU4cas3S4=\r\n=OP3E\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "6.0.0": {"name": "@npmcli/metavuln-calculator", "version": "6.0.0", "dependencies": {"pacote": "^15.0.0", "semver": "^7.3.5", "cacache": "^17.0.0", "json-parse-even-better-errors": "^3.0.0"}, "devDependencies": {"tap": "^16.0.1", "require-inject": "^1.4.4", "@npmcli/template-oss": "4.18.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "ba237c07a31f49cff96141758298e0062195c849", "tarball": "https://registry.npmjs.org/@npmcli/metavuln-calculator/-/metavuln-calculator-6.0.0.tgz", "fileCount": 7, "integrity": "sha512-h3zA2YSo7H3ZV1W4ZvlDTLaAbBwyOs6HEYhxrhl25Wtl49P7dLb8V2uFUb3dFZ8e4Ic+iF1cRMMWq9ATriYVqg==", "signatures": [{"sig": "MEUCIQCXH6qrpyaJPauIp2/wJHDDug+p4kivsvWhLb+qjSTn8AIgY7EqlBR4BHBZSvcXyMrfec3yX73WRrCGd3SK348lHTg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2fmetavuln-calculator@6.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 30451}, "engines": {"node": "^16.13.0 || >=18.0.0"}}, "6.0.1": {"name": "@npmcli/metavuln-calculator", "version": "6.0.1", "dependencies": {"pacote": "^16.0.0", "semver": "^7.3.5", "cacache": "^17.0.0", "json-parse-even-better-errors": "^3.0.0"}, "devDependencies": {"tap": "^16.0.1", "require-inject": "^1.4.4", "@npmcli/template-oss": "4.18.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "1e1006c15dd5ec0f218fbe8786d336248e3be481", "tarball": "https://registry.npmjs.org/@npmcli/metavuln-calculator/-/metavuln-calculator-6.0.1.tgz", "fileCount": 7, "integrity": "sha512-S7Mgb2gizh3LK+VEMYbPfIwJNaEnZuFGwNBAGkXSjvBqkU8rx/y6L14dMZjAIgS4st2vgkWs1bWKHi8mWkl41Q==", "signatures": [{"sig": "MEYCIQC5PJw4vPM6G99vtTBYxt8mFzo2TNa8HabW02C8h0FANQIhAJ/HW+1UM2xoP5ZfGO7Y1bvK6/xyKcjcP6VNRYZbB7ND", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2fmetavuln-calculator@6.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 30451}, "engines": {"node": "^16.13.0 || >=18.0.0"}}, "7.0.0": {"name": "@npmcli/metavuln-calculator", "version": "7.0.0", "dependencies": {"pacote": "^17.0.0", "semver": "^7.3.5", "cacache": "^18.0.0", "json-parse-even-better-errors": "^3.0.0"}, "devDependencies": {"tap": "^16.0.1", "require-inject": "^1.4.4", "@npmcli/template-oss": "4.18.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "fb59245926d7f677db904177f9aca15ac883d6cb", "tarball": "https://registry.npmjs.org/@npmcli/metavuln-calculator/-/metavuln-calculator-7.0.0.tgz", "fileCount": 7, "integrity": "sha512-Pw0tyX02VkpqlIQlG2TeiJNsdrecYeUU0ubZZa9pi3N37GCsxI+en43u4hYFdq+eSx1A9a9vwFAUyqEtKFsbHQ==", "signatures": [{"sig": "MEYCIQCwuWT/SjGFD+GxLq3lbiC9vtJgO4x7BWnWmbzB4Q7IpAIhALQUSgJbOgmiVHCjBZqWTKX/v2v5kmnyZvom0eeGoex6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2fmetavuln-calculator@7.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 30451}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "7.0.1": {"name": "@npmcli/metavuln-calculator", "version": "7.0.1", "dependencies": {"pacote": "^18.0.0", "semver": "^7.3.5", "cacache": "^18.0.0", "json-parse-even-better-errors": "^3.0.0"}, "devDependencies": {"tap": "^16.0.1", "require-inject": "^1.4.4", "@npmcli/template-oss": "4.21.4", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "a8697fb71c5012ed15d008059b2634cdc1a7b907", "tarball": "https://registry.npmjs.org/@npmcli/metavuln-calculator/-/metavuln-calculator-7.0.1.tgz", "fileCount": 7, "integrity": "sha512-665lHkHWufnWA3i6H1dJ/EDvK0s0JHrECuBLv/SdLsB53e4v70twsK1baDxatn1pZp7g11HhH80upkfgsBAFFg==", "signatures": [{"sig": "MEUCIQCnqCBuE4QDV6I/h1MwkIeJICHydSY/7INUETfQ40gNXAIgPj/jFTDT09xVZE+1Xu3l68aq40QF/UnZlPS9o9xQSMc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2fmetavuln-calculator@7.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 30472}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "7.1.0": {"name": "@npmcli/metavuln-calculator", "version": "7.1.0", "dependencies": {"pacote": "^18.0.0", "semver": "^7.3.5", "cacache": "^18.0.0", "proc-log": "^4.1.0", "json-parse-even-better-errors": "^3.0.0"}, "devDependencies": {"tap": "^16.0.1", "require-inject": "^1.4.4", "@npmcli/template-oss": "4.21.4", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "70aad00623d47297cd2c950a686ef4220e4a9040", "tarball": "https://registry.npmjs.org/@npmcli/metavuln-calculator/-/metavuln-calculator-7.1.0.tgz", "fileCount": 7, "integrity": "sha512-D4VZzVLZ4Mw+oUCWyQ6qzlm5SGlrLnhKtZscDwQXFFc1FUPvw69Ibo2E5ZpJAmjFSYkA5UlCievWmREW0JLC3w==", "signatures": [{"sig": "MEUCIAGIBdor1Oeamhu+TyjUjgeHe1/afUdY9Ssp6GVsYT5qAiEA1XrWZFGTgN7xOS611+F9AtENiiuoAYY57EfNYCQTzvA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2fmetavuln-calculator@7.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 30374}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "7.1.1": {"name": "@npmcli/metavuln-calculator", "version": "7.1.1", "dependencies": {"pacote": "^18.0.0", "semver": "^7.3.5", "cacache": "^18.0.0", "proc-log": "^4.1.0", "json-parse-even-better-errors": "^3.0.0"}, "devDependencies": {"tap": "^16.0.1", "require-inject": "^1.4.4", "@npmcli/template-oss": "4.22.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "4d3b6c3192f72bc8ad59476de0da939c33877fcf", "tarball": "https://registry.npmjs.org/@npmcli/metavuln-calculator/-/metavuln-calculator-7.1.1.tgz", "fileCount": 7, "integrity": "sha512-Nkxf96V0lAx3HCpVda7Vw4P23RILgdi/5K1fmj2tZkWIYLpXAN8k2UVVOsW16TsS5F8Ws2I7Cm+PU1/rsVF47g==", "signatures": [{"sig": "MEUCIQCDjhJhIePogju6o57UBY9DwrrtYYserkw2sgGPOSHthAIgQMhvVpB2uEgdI9ZPrzPu4x/Iu7EmHXfcueOSE+cb1T8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2fmetavuln-calculator@7.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 30376}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "8.0.0": {"name": "@npmcli/metavuln-calculator", "version": "8.0.0", "dependencies": {"pacote": "^19.0.0", "semver": "^7.3.5", "cacache": "^19.0.0", "proc-log": "^5.0.0", "json-parse-even-better-errors": "^4.0.0"}, "devDependencies": {"tap": "^16.0.1", "require-inject": "^1.4.4", "@npmcli/template-oss": "4.23.3", "@npmcli/eslint-config": "^5.0.0"}, "dist": {"shasum": "70d465ed5d7dc1ccad182ab8ebdbe5363ba0fde8", "tarball": "https://registry.npmjs.org/@npmcli/metavuln-calculator/-/metavuln-calculator-8.0.0.tgz", "fileCount": 7, "integrity": "sha512-zR2TGfhR8fH1u4VRz9fuC7r1nI9dweViRDsFnMH8J89OA90lJNwF6idTttEzYSWaOTW4NVoAIB6+ujV+/wI+kg==", "signatures": [{"sig": "MEYCIQDzAL8kJ9gPV12seMF1QNurGYy97d9TNrh/WFCOIYkLeQIhAOEnLlgNu1xn6LPVHxZh5PYNk9EKJFn5M1w9o7E9ZEvc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2fmetavuln-calculator@8.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 30386}, "engines": {"node": "^18.17.0 || >=20.5.0"}}, "8.0.1": {"name": "@npmcli/metavuln-calculator", "version": "8.0.1", "dependencies": {"pacote": "^20.0.0", "semver": "^7.3.5", "cacache": "^19.0.0", "proc-log": "^5.0.0", "json-parse-even-better-errors": "^4.0.0"}, "devDependencies": {"tap": "^16.0.1", "require-inject": "^1.4.4", "@npmcli/template-oss": "4.23.3", "@npmcli/eslint-config": "^5.0.0"}, "dist": {"shasum": "c14307a1f0e43524e7ae833d1787c2e0425a9f44", "tarball": "https://registry.npmjs.org/@npmcli/metavuln-calculator/-/metavuln-calculator-8.0.1.tgz", "fileCount": 7, "integrity": "sha512-WXlJx9cz3CfHSt9W9Opi1PTFc4WZLFomm5O8wekxQZmkyljrBRwATwDxfC9iOXJwYVmfiW1C1dUe0W2aN0UrSg==", "signatures": [{"sig": "MEUCIEfH3IOSe2Aj8nM9EHLExerA+I5t5Ob4jKjYBTjppeNPAiEA9ZQZpyYXKbCRIhXLT+Bu6Sx1a6qovPgZWJE66l75xok=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2fmetavuln-calculator@8.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 30386}, "engines": {"node": "^18.17.0 || >=20.5.0"}}, "9.0.0": {"name": "@npmcli/metavuln-calculator", "version": "9.0.0", "dependencies": {"pacote": "^21.0.0", "semver": "^7.3.5", "cacache": "^19.0.0", "proc-log": "^5.0.0", "json-parse-even-better-errors": "^4.0.0"}, "devDependencies": {"tap": "^16.0.1", "require-inject": "^1.4.4", "@npmcli/template-oss": "4.23.4", "@npmcli/eslint-config": "^5.0.0"}, "dist": {"shasum": "7e54d7c9f33999fde0ad2998904e0edd1627e26d", "tarball": "https://registry.npmjs.org/@npmcli/metavuln-calculator/-/metavuln-calculator-9.0.0.tgz", "fileCount": 7, "integrity": "sha512-znLKqdy1ZEGNK3VB9j/RzGyb/P0BJb3fGpvEbHIAyBAXsps2l1ce8SVHfsGAFLl9s8072PxafqTn7RC8wSnQPg==", "signatures": [{"sig": "MEQCIF74cCgdW0gbiVQIYdm2lpPAExp0bM50HsgRzDODWHTtAiB3hePObKKHcC0AN8K1dAR/G4u8Q4vg/F4aZDNKF6EwiQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2fmetavuln-calculator@9.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 30386}, "engines": {"node": "^20.17.0 || >=22.9.0"}}}, "modified": "2025-05-14T20:03:21.574Z"}