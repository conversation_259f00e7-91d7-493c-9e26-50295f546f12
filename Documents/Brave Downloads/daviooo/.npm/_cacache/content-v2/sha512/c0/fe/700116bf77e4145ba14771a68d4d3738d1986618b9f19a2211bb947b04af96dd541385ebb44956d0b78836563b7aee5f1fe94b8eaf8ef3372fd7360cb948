{"_id": "http2-wrapper", "_rev": "59-a359efd855681d8c995bb0668eba057a", "name": "http2-wrapper", "dist-tags": {"latest": "2.2.1"}, "versions": {"0.1.0": {"name": "http2-wrapper", "version": "0.1.0", "description": "Use HTTP2 the same way like HTTP1", "main": "source", "engines": {"node": ">=10.8.0"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "files": ["source"], "keywords": ["http2"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "gitHead": "111a702011223ebb4282b4f8939b1388148a59af", "_id": "http2-wrapper@0.1.0", "_npmVersion": "6.2.0", "_nodeVersion": "10.8.0", "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "dist": {"integrity": "sha512-+D+l9UEEFHy5v0B7Dp+3xWXKcJ0S7T0DSK0pq7IRZzOhckt5sIe9M6kWZ3YOvBOzQQ2HwEzcYI5ZYxFkQm+FrA==", "shasum": "36763ccc7d6717c603d7aa24a2911ce0a5497281", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-0.1.0.tgz", "fileCount": 6, "unpackedSize": 12400, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbwj6CRA9TVsSAnZWagAAxc4P/iqvT/Dzq6lYEqMGEIQs\nn5QHbCF/Rag7GCJQXendZg3Q2zcvFoBG3ZFUq1EVVUk21tkoPpFcW9SAzqpw\nQkrnbTRgZstnu9EGZCQsi0mG3xBhF80/KCsyp+Xzjh9KPuj/317Lv9wb0cEy\n9KCtxJQAfJgZF2+77pKIsfNefqEU91Ztj5/3QnBUERr/YCqAXkeSkoQG6r+g\nus/auyR0tVUuDvOFRu2UBCCGlR0En9tCl4Xg1U40eslpzDM6VPLbQuolS9q+\nHpjXOEIXFF6oFBj9OGxyRS5j949fGgJVVM6Thp9lg/Yfp0wx7xG2x8Sk/vnm\n8WobwxBs51pKFs3JRzcU084YXYFTj7aEA7ZCZG+hS1lAJdLVCHan+giZXdSl\n0x+wDTkLI1xnZKvTdFLheRloUF9evMVmrpgFt03slH0poAx8SzGiDiNkPyoN\nomaPuke/N2Kk8E99hrvvDZkggRXJM6tar5b7dV7HPS7Iyr8D1wFD2x7p8EQk\nYEZQUmfzgTQDCnZudJMmyfSm3cdbT4k5c/BahoO+X+197jF4gS0RMWAJiFXa\nAEZ23WxRXs+AUFRVjAohIMw259bB6KYEQ655D7WyI9E9BaEip1PKu9+akmep\nRDSb2G/WE8foX5c0CNnq6EPJSMMlzM98ydKkw1JRD6bXmffpHWNpPxVjh6HU\nXFGD\r\n=+21k\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDhtOAaemEiOX/u+QJ7u98S4qVg20jYAs1q6+EpkaQ98gIhAO4N8WRfXRaPqto3lu9x8DxAtFJm25NStFegN9Ivsl+7"}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_0.1.0_1534003450225_0.8627512754203885"}, "_hasShrinkwrap": false}, "0.1.1": {"name": "http2-wrapper", "version": "0.1.1", "description": "Use HTTP2 the same way like HTTP1", "main": "source", "engines": {"node": ">=10.8.0"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "files": ["source"], "keywords": ["http2"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "xo": {"rules": {"unicorn/filename-case": "camelCase"}}, "gitHead": "3d331bbf941eb4c2cd94b31c3a0bf506c01c7381", "_id": "http2-wrapper@0.1.1", "_npmVersion": "6.2.0", "_nodeVersion": "10.8.0", "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "dist": {"integrity": "sha512-ViW4Sez8FNvRT7xi00OmkxNus1ig8MeIL9WQK3GifwvdOrA5PxJO34bzh4iPqx4zF2nJnXrx4UYpMv+z0lY+EA==", "shasum": "38a30aa182de97537495a50c63e1bb2ea9718078", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-0.1.1.tgz", "fileCount": 6, "unpackedSize": 12463, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbby/CCRA9TVsSAnZWagAA6qEQAKI555X4mH5ZFM7O+KbL\nOD4X5I5cCrSQtcjdNJq3ULbzH3XIrss1T6FA+AvZH0eywD1pS7pM3lgY1S7Z\nhwCfhsE7tf9RTttGolYg6nwnhF7BGwkkSdS9uzqUIN1AThiDhJb3S0ZcrmMM\nnc8ebNG0WweT0crweeJ8b4IlVNSpP/OjUBlrnZ6ZltiJMX78aYSJqJ6bQkrx\n5QirSvTUqbh1k8IM3k8wjvToZXGtGT43AGyCmLraCLDjGSxpogsyULloxC3d\n9Ye59WA7HZ6uSkoQnk7GiojoYc77ev5YezFSjvvptFwUxTVZZiOgx51KdnsX\nANSTeYowlDLhbsZYZ8d3ML+Yj3YLjAsdW+KwlOc3PbzOZt9/Gafv008DsaGw\ny+UK8elzKyyYqFa4NAXSuOBigDmiyDMN21XmoPuyG+gjqSc1P4cqC/ysQ1Av\nfGyJ97RRX95UmgexChtOp6jkj3sJPiyd74q+PnA316eK+OFMW9IB5usaCq3c\nnZpgFv+LUtu1hrJONeSb/C0E9zjH+eplvJfrWqFuFcihaJxQ8S+PLiIrF/In\n2lHljFbUHMHgkQkC6+fILgplbP3qWmwVdImw2W5iDWLlBkhaJSQna5xJIMDn\nVg8nURvZiMtuMBzKGai9/Ah2mSw0UvGEDrE3t46iV1D139nT1wwjBaWIlxxy\nWaEp\r\n=eBqW\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEb2mz5F/V+JxHKFvhubPpKDJ0Ya3EMRahAMyqejty+wAiEArKgfm3jPDQm6OJKE9pS21w/PYj0vfQ+XIGe0uFLgrxs="}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_0.1.1_1534013378295_0.8349303732007076"}, "_hasShrinkwrap": false}, "0.2.0": {"name": "http2-wrapper", "version": "0.2.0", "description": "Use HTTP2 the same way like HTTP1", "main": "source", "engines": {"node": ">=10.8.0"}, "scripts": {"test": "xo && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "files": ["source"], "keywords": ["http2"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "xo": {"rules": {"unicorn/filename-case": "camelCase"}}, "devDependencies": {"ava": "^0.25.0", "coveralls": "^3.0.2", "delay": "^3.0.0", "get-stream": "^4.0.0", "nyc": "^12.0.2", "p-event": "^2.1.0", "pem": "^1.12.5", "to-readable-stream": "^1.0.0", "xo": "^0.22.0"}, "gitHead": "f3b4078f400af7e3a3afe17684d44eb454032fdd", "_id": "http2-wrapper@0.2.0", "_npmVersion": "6.1.0", "_nodeVersion": "10.6.0", "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "dist": {"integrity": "sha512-N+/ejhwHTKe+wP7vMyIv8KzkB4hE6OagVwNtfqoPf7iUnFVc3+1Cr+CDlCCqzOYO7kn+TUUzdr67JHlf0gQ07g==", "shasum": "d11890ba890f5a2c3c95343967e80b6c9d184a84", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-0.2.0.tgz", "fileCount": 7, "unpackedSize": 13743, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbcGs+CRA9TVsSAnZWagAAT74QAJuLP0IKIzooRZDIZTQ3\nXYFhIS0suTSV9TVbD+hQUHFhXq51BXLM115AKDiAEeWFK4R8bkc47CRuU4VV\n/fMViAeEpKy4dNGzb91H11uJtwkEqe6MLb446amPghq+xXl7DGshpiXaNm26\nFGaAuPCtFYQdpOZiM88NMJAjfBSEPlExnHBddCMDeiwnauh0nhYh/EhMhlLR\ntHeiHJ4P7dHp2yl0KKaZ4yI5MdrclDNeISLake9pArdZp4ze74PUuFya67GH\n7kyCX0w96vNfNQldlgzP6sO82SYSpo+rA5r3Rx8uPsgCki4+b8<PERSON><PERSON>d<PERSON><PERSON>m<PERSON>lev\nmNh74erCSOG4r0M+Dm5Zsco5bE9k5qum78zLssFIZKY98ZkDlshGxa8OI9QD\n9z3HrU0+xxp6r7RVMtXKZ0Ze3kzVjJJUgqFDFfI4oZLtfyAeBYjgHW9u/gGw\nP7O6wQvJWBVtPy9ATJoBDEhbMGlu1WcQyqmyhUnERWpMFIM0j34VelWYlFbZ\nf2TlA8BAPfXlk85UGYcgRRcfiCB4cGZsI+AYdfHeVW/FhRG/LajkouYcIFlf\nUEaTixT1E2riFrKlqV2WC7a6EIv2g9vY5m3NrotneIGw8pLic/1eivodn4sG\nACZF3BVv2bXTBmpLCburVYgw9bRhpWTwuNn/zJ876T/BlN1ZXMzzuZXkSl26\nBWbJ\r\n=qpDz\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC1Ztgn1edObTB7vHnTiHRqQOnr1YSdhVzdRrGom/slcAiEA/8C21QTmNrg9XqmvYPZFtIL5ZtwwHB+e0ho/Cp94ab8="}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_0.2.0_1534094142203_0.8917654439917717"}, "_hasShrinkwrap": false}, "0.3.0": {"name": "http2-wrapper", "version": "0.3.0", "description": "Use HTTP2 the same way like HTTP1", "main": "source", "engines": {"node": ">=10.8.0"}, "scripts": {"test": "xo && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "files": ["source"], "keywords": ["http2"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "xo": {"rules": {"unicorn/filename-case": "camelCase"}}, "devDependencies": {"@sindresorhus/is": "^0.11.0", "ava": "^0.25.0", "coveralls": "^3.0.2", "delay": "^3.0.0", "get-stream": "^4.0.0", "nyc": "^12.0.2", "p-event": "^2.1.0", "pem": "^1.12.5", "to-readable-stream": "^1.0.0", "xo": "^0.22.0"}, "dependencies": {"defer-to-connect": "^1.0.0"}, "gitHead": "f7cf8916593472871d85085825ccbfa4c498c028", "_id": "http2-wrapper@0.3.0", "_npmVersion": "6.2.0", "_nodeVersion": "10.9.0", "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "dist": {"integrity": "sha512-13LX3p5U4t8K91mSI8XihOQ7mS7Sq4wuROzjHh0k5XLat+ZSStoGei+6huTaMQ4kTcX0YBIbDt1lmo/cvIwlew==", "shasum": "94300d8b4ce010c2a889c2e5c34eb3267cdcf4fa", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-0.3.0.tgz", "fileCount": 8, "unpackedSize": 18255, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfaHyCRA9TVsSAnZWagAADOwP/3oly53KoGA5q8IkNJRj\niQL4WTrlV7aHoYQwM5t/8D6ah5Z7lIiydrE3Uxk75b03xyEgasGPKGtPKZzi\nZLlhbFRbFrQUD0Xksy6MGZRMS4kMcv/jXilao9sxJU1dpkHwyAMB8jPthm2j\nInPr71S4Wn5WXmqUfMKKKZcYvS9y9jWJFymlUXYbh0sjG81XWbZrrobSsfe1\nDP/LoXX9djaX3pdjDGKmtpBbpRdxNs2aNKANzZpd3rgXy6jTqRwlg+waHntX\nnDGreI3oiOniAVLY2FjYKYaaee62sg5ADNuVcfMuSSnb5d82XyfdzpuDhzZ4\nvKAnCCIl6TA6yVFoOQujNzNN6phUGO43TFEQC9jF88Pm4mRMxgbWxGBKinGT\nHK7b/gdAjBWxpm0c6KLLjjVirL31ozHbVhDwGseQgJ5bmNgd1FXQ9UblxjZ9\nlFN/uOg9jbwgx4HmJSYL8zmV5hWp75qv8uPW3Ifn48vfWfL0l8HF+cc87hEX\nKsco32R9nXmCoagadP/miKwlxybgtvCybS5+J77Dvt3wYfKYYeEmXiceFPmK\n4MI+j1OIYtbJwPnlSBqlP+N22qXuZumARz+tImYgYf9DKcToFX75beWGNm8W\ncqf3KjgYaEz6OJg29lOlFnZ0OxlenK9aUEZi0xiflVG50J8aOm1+BRE3GVsn\nsyYK\r\n=jFn0\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAkVo5Ou8lDJaivPqV2YgqA94SG6C0L/jjlFBTeR1AH5AiEAgtkvw/+yIoJ6BNdRlpqWpionc8ghvAyeeWcyC516qVg="}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_0.3.0_1534960114145_0.4912294718407584"}, "_hasShrinkwrap": false}, "0.3.1": {"name": "http2-wrapper", "version": "0.3.1", "description": "Use HTTP2 the same way like HTTP1", "main": "source", "engines": {"node": ">=10.8.0"}, "scripts": {"test": "xo && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "files": ["source"], "keywords": ["http2"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "xo": {"rules": {"unicorn/filename-case": "camelCase"}}, "devDependencies": {"@sindresorhus/is": "^0.11.0", "ava": "^0.25.0", "coveralls": "^3.0.2", "delay": "^3.0.0", "get-stream": "^4.0.0", "nyc": "^12.0.2", "p-event": "^2.1.0", "pem": "^1.12.5", "to-readable-stream": "^1.0.0", "xo": "^0.22.0"}, "dependencies": {"defer-to-connect": "^1.0.0"}, "gitHead": "732f065d8a054e4f9b5660db678f6715df4900c9", "_id": "http2-wrapper@0.3.1", "_npmVersion": "6.2.0", "_nodeVersion": "10.9.0", "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "dist": {"integrity": "sha512-aQ<PERSON>ee+D5ed0D0vFxcutXTnjcUhj/S1m6s2u7TohQEVWOYft36ioMx8PXf582yRiMKkCHXRODrJKQHnzVO9JGA==", "shasum": "2da85cd0c67c41116a40d55b243093d6b9fdcf07", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-0.3.1.tgz", "fileCount": 8, "unpackedSize": 18490, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfay7CRA9TVsSAnZWagAAtC0P/1+Tde9tlmX5YapqcqxQ\nkrm3fX2FkGD682LE5Jk0ZsayX19U3kYWLBTAX31bWRXxRYL7mBgJ8E8WZASR\ncggpnW5Umyo+lkp7O5SEUfMFeT9xLVMx1ItN4MNvwS4Qu6LZaW7PpkPGNKrM\neCDUF7e93cG2BKtltjNtpRqXi/cgUIrm79giU6rxzdvYk11JjYtRgH60qVtE\n983kTxLRW56dDzq/tzb9v8ZWEacbstjAadD3duout4UeTFIG/FGert6RvpVF\nfy9fECSU1XHF7LVqvVokzj6BXv8z6bo+AoHG8VtaQ1+oml5S9kUM5vP6gpN3\nkpfIuORveitNYWrzx0EACBTwV/3trw3RHJq1+ojEpf6MAPiB4GH4GLQe23OU\n13/5Lp2eBcVfAZ6qxGBgFpwWG5relVWhUFEL0qCoxiA3YuRJjjGn72Nzr/pu\nMhl8OVCUXHt9+iIHOQLKrNpqBtzA5nesTr53bNvnkOUcSLAKGQbujzphCaZ0\nO+m/+tTuVIZKVCZg1EefPx/SWUsF0BgGk1/9dsUKMGw7UOFv6vbKsIBb1qpz\no4xKuYoGSD5XhCannMUmQhHLEwO/hT0xvv9Kj7ux5+5bjNo5ne0zaFYF+xeJ\nnYF+2g+emn9qudiIXg+RCK028CQ54sJg6skfDegVBZXektplD/s5rMruq6zH\nzbVv\r\n=wb7J\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGMwVepkJup53+XuHAs37DGkpxp6mqnU82rTRX8FI5cFAiEAghCUPzNb/V2P8TMJP6Qsx2QHfOrMrBCO5Me+D0sqiG4="}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_0.3.1_1534962874614_0.6636298896557444"}, "_hasShrinkwrap": false}, "0.3.2": {"name": "http2-wrapper", "version": "0.3.2", "description": "Use HTTP2 the same way like HTTP1", "main": "source", "engines": {"node": ">=10.8.0"}, "scripts": {"test": "xo && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "files": ["source"], "keywords": ["http2"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "xo": {"rules": {"unicorn/filename-case": "camelCase"}}, "devDependencies": {"@sindresorhus/is": "^0.11.0", "ava": "^0.25.0", "coveralls": "^3.0.2", "delay": "^3.0.0", "get-stream": "^4.0.0", "nyc": "^12.0.2", "p-event": "^2.1.0", "pem": "^1.12.5", "to-readable-stream": "^1.0.0", "xo": "^0.22.0"}, "dependencies": {"defer-to-connect": "^1.0.0", "url-parse-lax": "^3.0.0"}, "gitHead": "f616d0694052c8dca9078c4823560b122e834a2e", "_id": "http2-wrapper@0.3.2", "_npmVersion": "6.2.0", "_nodeVersion": "10.9.0", "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "dist": {"integrity": "sha512-6ow5mQYY0Er98cJYLLFkGugKeT9DJMrFRX6jf4ymDRZc0EodDcV3hLP4xsibwQCXAqbXwiPuh+FGnLeRb6J+7w==", "shasum": "8723a17be26570b7f77d15c16f3168f587ad5c16", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-0.3.2.tgz", "fileCount": 8, "unpackedSize": 18795, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfs/RCRA9TVsSAnZWagAAMkwP/j7E/BEKpAqphBff5TfM\nyF2OFmSCiedn2uwTiU83cO9n6RBBOGSpRJ+i6A2vNkRi30uqNKppUpoxnAgS\nFpwUFHJ9Nw+kKElQXyuq9uH5GzIQjmux8Cctj4FAUQJFeonGvu9A4pBL7/kY\nWBh5H6kiRbTQGPtTipGaSxA46j7iNI5lwI9tuAYbieBwxAYr2v/y5Hs5tkkD\nyLeiLHYh04pWo/fOrIFa6wjARHRr6mQXtlfWDGyLo4OIVe+EKegg430Njtgx\nVBFt+XZLJc/1kJt9jJNWqyV5vDxLELvN6DmJC/ef9+DYpQk7YRvZXpdpswnK\n2ZiglDjTJW8Z6hkZ7nArWESzABTJdm6ikgcQ7jD7cwbDw9UC15iD6AfLexQF\noZYJP/1GUXW7EoWyqvaIwrODq/HgtI4mGv1Ii7WcChhNdIyQzEw6poCVq5z3\nIzBT0YEdes1ECX5S4Xf3t6cprQHUn7tXKPEd679uFAsJLCH5ScIYcvB2Aj0u\n+htB8zvLZVZsV80vuIzyLnLIk0Lg8IkvBHnR/4sybANTeXHPjvQ35Jl9xzGG\nmap6Jt7PHw2kFFNmrUG+uUvuBxYnnIC0qvOjxfKeX5UknbXZvxlI/EkNniHJ\nT4Y2IqEj3dUL1ITMy3IRadCfHOwKF4KjrYSgb7OzEeI0UnzgTvtbwYeIxUtE\nDOS1\r\n=PMOj\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCeap+LdehiHmGdYStjs1vO0DlBAKzvDbbT0cXX0+gLtAIgFfxUeKfdwbxw4lrZ4Z7u0KjCfnixdsFl0ptPW7GuWVw="}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_0.3.2_1535037392377_0.8510217530478388"}, "_hasShrinkwrap": false}, "0.4.0": {"name": "http2-wrapper", "version": "0.4.0", "description": "HTTP2 client, but with the HTTP1 API", "main": "source", "engines": {"node": ">=10.10.0"}, "scripts": {"test": "xo && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["http2"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "devDependencies": {"@sindresorhus/is": "^0.14.0", "ava": "^1.0.1", "coveralls": "^3.0.2", "delay": "^4.1.0", "get-stream": "^4.0.0", "nyc": "^13.1.0", "p-event": "^2.1.0", "pem": "^1.12.5", "resolve-alpn": "^1.0.0", "tempy": "^0.2.1", "to-readable-stream": "^1.0.0", "xo": "^0.23.0"}, "gitHead": "4cdfacb4e90a6170376d8f91aeb4a6cc9f097dfe", "_id": "http2-wrapper@0.4.0", "_npmVersion": "6.4.1", "_nodeVersion": "11.1.0", "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "dist": {"integrity": "sha512-n77AhxTc+tcrHRH+Ziaj9D4v+RMVc8XrMit03UXUEMtW9hnLLvMfxJ2P+R3yhG8J8uVOH6fESyMPiOKFCmiKHA==", "shasum": "4e339072f2a53981f5eabf15a5a9fb0bf652e8b4", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-0.4.0.tgz", "fileCount": 10, "unpackedSize": 20637, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcH7dQCRA9TVsSAnZWagAAjDAP/jlNPqjTPs68fqi58ik2\nSHKNe5r7uaXsf4aLrmZzWPpT97Mf8cxWvB/8+UiaadSYYq7rhd870Oi5IbiQ\n9MpWooZ2vTnlsuGPWMdl7mHPb248NisfZ/NPtwZxFta17eGTn80oznMT+vi9\njZg4pF+PdVXO5Wmdtd9YcaSlrh6MFQpUPcIstf6npsWbB7gGW9OYRMZR4CK5\nG0TnTVAJD2zogjCakC95cWFx4npqAwMPaHH+AutaG3I3zKqYqq67SkTfCf/o\n1JiCZ2cWmj5QZdpCifFVU8UbZpKu3xZBlHVlAvLH3NpUVH9uzgxJ4cTMHNoo\nmcL+B3CTdHew7Rny8tY4laqu3qn3HVDGamz7S5MpatZ7VFeQwhpzjhJxF1z+\nUAqDFwVY8FaESsO2e0jEcYetBibwbkPYyRLWGhPKW9reMfGyPxmhbPMNxZJY\nVXK3zkVkORKfBDJnGdpjQu7jmwgXUMisGWYA7k22oJPMf8eGrWVqZXtkcTYU\noaiS4kcyMRwcwmqOuDYc35ke1S4yBY6CLpBksrZjGKP2O/aX4V6K2DoDfphC\nvKO9AUhfAUaesY+7KfqypMV6pVM0onciT2hn813QJ//J1O0lw/R/3BjeFdb/\npGDNkZytMaRCuiQRpS8QgkwqfN3nAbZEKNx8W0ywKjf5FK2hXICeQIH6N1xo\nnDlc\r\n=INsU\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCF0YqaqKiNDcNHW3vZuhuL2e+i24yQVwCFtmvC1vs6eAIhAKqiIBcheZAk51oAFDSP7F6j6jHSO3E4kHWyaDANequx"}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_0.4.0_1545582416096_0.6856880076974965"}, "_hasShrinkwrap": false}, "0.4.1": {"name": "http2-wrapper", "version": "0.4.1", "description": "HTTP2 client, but with the HTTP1 API", "main": "source", "engines": {"node": ">=10.10.0"}, "scripts": {"test": "xo && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["http2"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "devDependencies": {"@sindresorhus/is": "^0.14.0", "ava": "^1.0.1", "coveralls": "^3.0.2", "delay": "^4.1.0", "get-stream": "^4.0.0", "nyc": "^13.1.0", "p-event": "^2.1.0", "pem": "^1.12.5", "resolve-alpn": "^1.0.0", "tempy": "^0.2.1", "to-readable-stream": "^1.0.0", "xo": "^0.23.0"}, "gitHead": "f24fbae3912b8b63c98f65ef840ea51bc3109fc3", "_id": "http2-wrapper@0.4.1", "_npmVersion": "6.5.0", "_nodeVersion": "11.1.0", "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "dist": {"integrity": "sha512-p8fbt5cvFLQv33VWrSkOfqHcqdatTWV7RXV3I0fFek0Rvm/liIi06ayRzaWpEQSHb6/fmWhudllMAbVq052lCg==", "shasum": "b368731b21193678cbf7f2f9be148bf215b4d86c", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-0.4.1.tgz", "fileCount": 12, "unpackedSize": 21839, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcILi6CRA9TVsSAnZWagAAyY8P/0rFBJEw7odLBsMi5qbA\nfJ9FrV1ODAWmwkU5IMJJcrwKqRjduTg9sMUiLpqPUe7jNO5TdKOHOavR6aNR\nIIautiu5zyJjTiSNeRTVwkyHfp1rANdBZrGxjZJSAmeW8U94CcjD8Hykx0Yb\ntDp+Dg3LAryMCL4YHwznLFCadVhPI/lYWnRwijQxp/CG54eVe0WxjhVolXjA\nGx/jgNr+A+UcqKPdTCE7ul+NgfJn92ViWsMsRSF2IAxgSNVrual/F8WEtlg+\nnI4ohIfQmLYVGrQPBl3IwogRq42OYP57cU5IZMYQvn/7+gSmJYC1UGqF2V4c\nntG2P/9STPYRhBARlMkxsebhLRYRTwH+KGU2Ru3xFhMfZwQ3VxAWIl1k6uik\n1m2VYHzmGoiuvYMGUZ/P4mL2XOiw7FTVlWLmZZcYFwgMvuI6FYX/yw5c2rOU\n3KWY6YWoBtEYi0wpGMEpXy8OxkLYoAQvzgltnU+cEcy8s+fBAdzQJj0HupyU\nlwFx4gwZm6w5kKuHQVLYAgbm4lEy30KhUusiwlxFgYesPu/yYTblxyrWIebF\nsMzDXt4BxYQ/keJoMA1ApCtNgq/+wXWQFb7ptCu0LlhV/RSMaBrWJGaHNaTj\nNKML91EisR/TRzOsUo+ntJ0cQwAXtBhnN0A4AZms5v0Xp2swEBG36WaZ3J6n\n1Rq0\r\n=qJ3S\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAkxfeQRr3MuxAN9Az64jgTIzEWGZu+M7CmEDd/Q33szAiEA7sgfv9q289fJpM5Xy2P70Dm3iJNu0rZXhi43LT8IJzw="}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_0.4.1_1545648313682_0.8216361904048723"}, "_hasShrinkwrap": false}, "0.4.2": {"name": "http2-wrapper", "version": "0.4.2", "description": "HTTP2 client, but with the HTTP1 API", "main": "source", "engines": {"node": ">=10.10.0"}, "scripts": {"test": "xo && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["http2"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "devDependencies": {"@sindresorhus/is": "^0.14.0", "ava": "^1.0.1", "coveralls": "^3.0.2", "delay": "^4.1.0", "get-stream": "^4.0.0", "nyc": "^13.1.0", "p-event": "^2.1.0", "pem": "^1.12.5", "resolve-alpn": "^1.0.0", "tempy": "^0.2.1", "to-readable-stream": "^1.0.0", "xo": "^0.23.0"}, "gitHead": "330b66341fa8303335ea618c659d1536b9e41b9a", "_id": "http2-wrapper@0.4.2", "_npmVersion": "6.5.0", "_nodeVersion": "11.1.0", "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "dist": {"integrity": "sha512-KVZKv+Ubod1tTSdikPsNif9dq2cl9oOpQDYu2qJPDnFlT4X/8jHdg7zZtEvfwgDY4+aN/b5ww75hxCEh1EEoOg==", "shasum": "a2a7ad60610e5b7f91712af56645d805d323e11f", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-0.4.2.tgz", "fileCount": 12, "unpackedSize": 21900, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcI7iHCRA9TVsSAnZWagAAV+kP/AudSCbV/S/qHuSwU6dF\nRvmAlQU07+37EMwmVM9VNNqONzCSEA5SNjK8ft3WiKmXUbV7lt/gefQvPecG\nBcev5jhAI7g4XmB62C6FqE6MZ/BexrbvfejmhW6LGmxNgUJejKUxMNsnPWPh\np93jBLl9QcZVJtEK8i2pKjiRJiNd+wl72MeqHLP+AQBCpzPrni/95pQR7OAd\n/xyuhP+O7cRxTqpAmmBGf2pky7HOCsDVSVjvhxjWUNeF7r1t6hVepoWIQk1w\nlF2gA+P17N8ik+lcFpC7Hs1Af4I0Cfe1bSdZGhFPi/ISmPpYok/8l5PI5l2J\nAjtVBSaaC1MgaTO6gajKKchVBj94dHMvxRpPDcmehWKEPWqmkKNQGe5SSUgI\nR4B5Mq7CU8A+b7bmuwAoOIYhpFHrBAsIZEl5eB6nUoMHp2EIkCkIUui9C4qr\n1WTV5RCr9E4lL5tert9OsA6rdmvXqeSJ83IajK7TvZG09V//I7ki058nfGgZ\n3Si1bOrma6WwNTnXTBPsjBrMv65XjLHOp7VWCG6vZ+WC/Rg2uHpvSCaP0k0d\nQr2akR5org4NzL0ZdJjxBCDvq6Y/x+kj5QusTWqdnuxCiQqfv4FzLONyP0ez\ncTpUglWLFmEau7BfS5RjGhu6gDURdIBLShOmJ22g32uPSLAVeoWqlxR578Ee\nE8Ft\r\n=pVVg\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDZE3q+q27hKQ8CM1q3+ZUr1i+z3IW7WBI2Gj7sMLAKNAiEA5x5UnN8TnTaNkaKiqt8quA9NvxQoorzDC961IUgD8uk="}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_0.4.2_1545844871037_0.6440664817809703"}, "_hasShrinkwrap": false}, "0.5.0": {"name": "http2-wrapper", "version": "0.5.0", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "engines": {"node": ">=10.10.0"}, "scripts": {"test": "xo && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"resolve-alpn": "^1.0.0"}, "devDependencies": {"@sindresorhus/is": "^0.14.0", "ava": "^1.0.1", "benchmark": "^2.1.4", "coveralls": "^3.0.4", "delay": "^4.3.0", "get-stream": "^4.0.0", "nyc": "^13.1.0", "p-event": "^2.1.0", "pem": "^1.12.5", "tempy": "^0.2.1", "timekeeper": "^2.2.0", "to-readable-stream": "^1.0.0", "xo": "^0.23.0"}, "ava": {"timeout": "2m"}, "gitHead": "1b0604e0e7cd6fd7ffb9cd4a6cd38c4c29f97be1", "_id": "http2-wrapper@0.5.0", "_nodeVersion": "12.6.0", "_npmVersion": "6.10.0", "dist": {"integrity": "sha512-s3eJoqvVtSUK/ciMTmGB3ncCQYklUZTEO6tAjmC03Nnl9TLzC3X5pPEZtkG8kZNCDYJ4BYLw3IeAbytYowjmYQ==", "shasum": "d8f08bcbfe91140c3eb5e946b7032a6cdaf78e85", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-0.5.0.tgz", "fileCount": 13, "unpackedSize": 30831, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdKaZGCRA9TVsSAnZWagAAn+YP/iilTzaa35XqA8ddT6+b\nYyQG/+/mTyqLY+tN6nmNZ9EFFVqX7DNnRC1aWX1boNQc3KtYQK7qFYzEhEEe\njgYIvSfb9ifybwED50bHwoxBiG076Nk1cUg/76iYC4/k4AiaA+6y9sLW0Wrf\nJofu4NgnNRXN7gWjB/ioS+ehPlCga6zSsHRuIPqWX52hoWCcFztJa9Ah3ekJ\n3YQdqi/dbQzLGjkz1vtOUBrMiIiLR9q1TpA5IWtARaT6HGBZP4l0P6L1q9fd\n0ZqZLbBQjjMtQc2alHmUHHlkGKGZqls0SdkM8plG4jVUOMQwkdFDw+PPxrni\n/gWPe9JdZTv9xK4YTcVrunVCY3wpsFy6GmNbbfalBCE/YIHFNJenSGfsKmkl\n/xwcjO3u5pLc5IzhMnOgS3TEcUuxhIlm3xDHu5aW7uA8R6pFUVpHltQK2sLm\n+0K6KWhqrcv7DYhUFGkZl4YC8/ymJerQ5MY2/IVSrDWFl4Q9omHaNiCEcr3e\nIovNd3oQ9mtxIlsWef0M7u1vs1ZIYCJKKJb4K2DQxK5WXf6ehma24KWgzbJU\njdtPeoJI6tYI+qSrKdDR6SyGXjtpX5iqQIF959lxC9qhplyUrmRdrec+GpfO\njMFqShbb+YwPEWSPoFws4tXxIsT+Fwj7ZouprWpOFISSdSBNFs++4Xj100MH\nl1R+\r\n=UKsx\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHz6CJ4oF9MP8cuNj8aG9yX+hSOdC3T+AM1qb/qiG7MSAiEAvSu+FJAZRf8Rrf2sc/MNZ27xmzFxDCQKyh2bE6d9wpU="}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_0.5.0_1563010629270_0.4943789081330441"}, "_hasShrinkwrap": false}, "0.5.1": {"name": "http2-wrapper", "version": "0.5.1", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "engines": {"node": ">=10.10.0"}, "scripts": {"test": "xo && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"resolve-alpn": "^1.0.0"}, "devDependencies": {"@sindresorhus/is": "^0.14.0", "ava": "^1.0.1", "benchmark": "^2.1.4", "coveralls": "^3.0.4", "delay": "^4.3.0", "get-stream": "^4.0.0", "nyc": "^13.1.0", "p-event": "^2.1.0", "pem": "^1.12.5", "tempy": "^0.2.1", "timekeeper": "^2.2.0", "to-readable-stream": "^1.0.0", "xo": "^0.23.0"}, "ava": {"timeout": "2m"}, "gitHead": "4f65cbd873fad8340049e5faabbcaf1b6a72f620", "_id": "http2-wrapper@0.5.1", "_nodeVersion": "12.6.0", "_npmVersion": "6.10.0", "dist": {"integrity": "sha512-OA7seoxDNNpDLUacXfkt/Rh9PUWNbRNXSfKDuTWEHdPFGTJD48tXDmL15JG9esR0u1CuZ1bA2rRmzX+/4isC9Q==", "shasum": "b1a961ab7777e6a7d6840ec30b61d829fe4321ae", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-0.5.1.tgz", "fileCount": 13, "unpackedSize": 31596, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdKizLCRA9TVsSAnZWagAABEsP/i3KZ+eufsXOtL7c7yu6\n5MZkk5ewDojFsb4feeb5JW7vrEuNlHhPvmBv3LinGq+Z6lrLUrVtUznZpsVB\n2kbNRNGdCZ4daNW98tr2LqLZS2qs+BvfgbTj4kOnYcXzUdS7U5tZssann28Z\n3GpgW2o2F59clixVrL9w4GbzwjJU5Ww7zYn3XcZHM3A0juycISkElWDxsRLP\nUOfisjMfMHzhMibk+QH8iWvqBzucCLgmiPcyzOXakyyofaFzA21J1dsr3SDI\neBl8i6fDWTmKP4GKXZE4CPkInBOTIvrkjmn+TPZ0TPcYht/T25QNmPOFwuYN\nx2cdP+exPM9bat6IMNETYexph9XxLhCqKozz+JwrNq9be7DqPZK4mPOukz30\nAfVbo/woYGH2Rxr9G3gZfUzJ9aYp+Fymfu7rqzT+Dlht7XIwkrvuURV8WzK1\nuBroVg7Mi+IhvsooY2zBLuaDzpUzzbFGmQPE4ZvqtfMOIphNrizU84+sQJL0\no4Fv2/NoDggsNR27xLRulogi4Ti7ZyUKJGrcYK//io4VXHw4Vvww0r0BFSom\nNlkDGcN6lXkWopAc1Wlf2Ey2w/45J5qf4RcoNb60qwV51z/kC8r/vMJlWgU3\nBdJ8/NW/T1yztsoMn5MLtUJScjI4vxU1Q/QzQgo84M5zXwzCjIEhVQQBhlLe\nxpHY\r\n=oAy0\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCbA9rEg29OgoeqzZ1M9PfZkFU7zgoDRVe2mPJOzUvzVwIhAJzJPTjHrg32uKvdReCed8eJpnJpvhLzS1SuyY4zbN/e"}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_0.5.1_1563045066409_0.3306368591624542"}, "_hasShrinkwrap": false}, "0.5.2": {"name": "http2-wrapper", "version": "0.5.2", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "engines": {"node": ">=10.10.0"}, "scripts": {"test": "xo && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"resolve-alpn": "^1.0.0"}, "devDependencies": {"@sindresorhus/is": "^1.0.0", "ava": "^2.2.0", "benchmark": "^2.1.4", "coveralls": "^3.0.5", "delay": "^4.3.0", "get-stream": "^5.1.0", "nyc": "^14.1.1", "p-event": "^4.1.0", "pem": "^1.14.2", "tempy": "^0.3.0", "timekeeper": "^2.2.0", "to-readable-stream": "^2.1.0", "xo": "^0.24.0"}, "ava": {"timeout": "2m", "concurrency": "4", "helpers": ["test/helpers/*"]}, "gitHead": "61e93f8508927b0a59f46224baae49dd9b4b2a71", "_id": "http2-wrapper@0.5.2", "_nodeVersion": "12.6.0", "_npmVersion": "6.10.0", "dist": {"integrity": "sha512-9ttevk2G/tWhg/ePEUEtrBaujsof06KeFY6mIVN50OqZfh8Y//5HB/d7f93wUWHisxjXfm7ckhOhYTSt6jzwPw==", "shasum": "53b61ced7a1c437b7ebf756d8dd0cb248856fc16", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-0.5.2.tgz", "fileCount": 13, "unpackedSize": 31702, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdKtC3CRA9TVsSAnZWagAAyiMQAJWNiY+M98l31OjyzCP0\nU2XDHGC8Rf7I2nSO7o+ApD183DqgalFvitrsLcicZucFlyHEllabAYkfCt/K\np83rBg+IHxhGSxZJ4+o4NHXbr+PUftVY/5MMBlcVL9idlS+F/VwC9dyjEL9W\n7lv3YuEOTK4mPj5p1nWlA9bL5cVnP9zKjLwb4pJZ9sNj54bwprH9R7N7g6KC\nBd04yufJtk2fsuDz6lyIRjc3gxU5K0r6ZPl680nHtQ2EMp+5Xk7Qzqa17SYt\nVE0RcBQFEtDEqFxKQYg74mkjWCM0BnWKgtditmMoRHm6ovC4KP7hK9Nt+/tH\nEC2DAskDIebgvVG4YKKvMbdbNMRRiUpiCqgPRnSYDZUYWGCnuy9eCew/pXhJ\nK/MB1wolxWmpJXyddsUgGRkzBEGPfs6X+fpYJaw+QuCVw/b4JDNbtRyI0KcH\nGmdf0JtE0d+ajQCguaoiVYsRLZNcSxVLl2J35pzpYBsGaY8AJXoMdBxl7roE\nyXiWsa1IEUCwqpBAWUD3zLsxAqqpT/zfbR99WHQ5dUB6cgPUD2kPqmkmitkm\nZSs+WaZNxzyVdsE3CR6CdyABtOzvZZEVtUjwO7vov+vZDcAWaB5Beg1uLbd6\nVxGGtL8FD0OmY8rYWWz+kKeu7dCz4BG3aSu7norcrZlcVDtl7Ao8mKFfY4f0\nJjB0\r\n=++9D\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAgc+ihTvOTtaSAv9DsEDNAoJz0Fp/Uc4M3xRjIZmY4HAiANzY9LjNYwXKkiuNnFQlNpxJYUlHupUy66fBVnAb64ZA=="}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_0.5.2_1563087030806_0.7170464003406534"}, "_hasShrinkwrap": false}, "0.6.0": {"name": "http2-wrapper", "version": "0.6.0", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "engines": {"node": ">=10.10.0"}, "scripts": {"test": "xo && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"resolve-alpn": "^1.0.0"}, "devDependencies": {"@sindresorhus/is": "^1.0.0", "ava": "^2.2.0", "benchmark": "^2.1.4", "coveralls": "^3.0.5", "delay": "^4.3.0", "get-stream": "^5.1.0", "nyc": "^14.1.1", "p-event": "^4.1.0", "pem": "^1.14.2", "tempy": "^0.3.0", "timekeeper": "^2.2.0", "to-readable-stream": "^2.1.0", "xo": "^0.24.0"}, "ava": {"timeout": "2m", "concurrency": "4", "helpers": ["test/helpers/*"]}, "gitHead": "abb98247f2df953b85c4bf001cd44dd1a1c56504", "_id": "http2-wrapper@0.6.0", "_nodeVersion": "12.6.0", "_npmVersion": "6.10.0", "dist": {"integrity": "sha512-V9b6k9agCchSbZjAP1Zo9IXisahx96XcebPFS/ch25p3JsNNuxs0/1bwq8kEmDCgdBL+rTrisbDiHVDoSG0W4A==", "shasum": "b755b2e50b69faddaf482402fb81b0fdf42a8c8c", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-0.6.0.tgz", "fileCount": 13, "unpackedSize": 31856, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdKuLACRA9TVsSAnZWagAATikP/3TkKGvqFSGQ8Jbbk4uz\n8AvNuOhGuctKhysJ5cxzNzAy++O+czoy9nckj/YKRs4b1qTcc8lZ4UzPySpH\n/kmIgMt+NrLpfh8PZm4Ch3bl4yEHmEBtVic/p9paAvGKdCxvl/x0rWudMrjY\nZ3EU75FMRZOap9lWkBp+R4F7pcegQjH8jfr0sbZ+dTy7TF5RBVwieUe30ecb\nr2EzbzdTHQZRe+SC/zYeNgDxzF5cjJ+5RaH7u8OaPOtQUhNDtzRl+CP9En1a\n8PscrANlwR+9sNrTfhstwKSBfKA3iwutRO2JLkwG7mx4J3TM3afFmTak4c+W\nxX+l9IzaCHTzNP9lujEBDdP3ePfAymmpsRvIXDuHxm20akcrLvRiXLKhe6Rh\nmt+/zHQ6oNMbasH6AI0u5vIYbu0N7CI6RIxQp5bqK9EDUSeR1tqpoefvZ56p\n74+eYEEX/4FCRgW6t7i1jXhQRLC2JoN7Hl1mkUKC61ujk9r7+XJVWEi7PJdL\ntyz4sRU2ALF7l5/iThE5EhPLNtSSf+Ox5v8qW0rUs0s6AHjWGbybDbIy/Fgu\n9ROc7eOEldcp0sKfGlXZTEvYKTdgtHi2/AYh+CJ82Hkz9x11BVb4/777lx+m\nangNRapXFjsFQNOEQ363mXfuB+0J7gjiOVPhM7jZ/9xjl+nwRMTvvQKCsbmj\nOM1f\r\n=NaoC\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCk5KVTqnEfhrDBZ0rP+Xll26nAQEAWNu/xuGtLrK1wSwIgWaFwlSKLc8N1v8VJP7/SLHrTl6Oerj3lOWjXqX8efDI="}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_0.6.0_1563091647662_0.40217225360345155"}, "_hasShrinkwrap": false}, "0.7.0": {"name": "http2-wrapper", "version": "0.7.0", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "engines": {"node": ">=8.16.0"}, "scripts": {"test": "xo && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"resolve-alpn": "^1.0.0"}, "devDependencies": {"@sindresorhus/is": "^1.0.0", "ava": "^2.2.0", "benchmark": "^2.1.4", "coveralls": "^3.0.5", "delay": "^4.3.0", "get-stream": "^5.1.0", "nyc": "^14.1.1", "p-event": "^4.1.0", "pem": "^1.14.2", "tempy": "^0.3.0", "timekeeper": "^2.2.0", "to-readable-stream": "^2.1.0", "xo": "^0.24.0"}, "ava": {"timeout": "2m", "concurrency": "4", "helpers": ["test/helpers/*"]}, "gitHead": "a018586b17924573a64fb8b1949b2def34e8b5d1", "_id": "http2-wrapper@0.7.0", "_nodeVersion": "12.6.0", "_npmVersion": "6.10.0", "dist": {"integrity": "sha512-KSwFRgqprv5rtiDszy1IOMf5OlgOahQzwlW2YqSlQZJKOMDFGNbkZDLvmuoWl1/hPKyVNtaBd+oB8sN0DlXuEg==", "shasum": "9e9330f5c47746028ff77b862854f6fe9cca2b4f", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-0.7.0.tgz", "fileCount": 13, "unpackedSize": 32083, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdKuqVCRA9TVsSAnZWagAA3vQP/3ENyKwjaL098AHFYonN\nOHxKuy1w3IAfJXA3b3FW9KPq0SuGyL9sPT9WhqG9Q+sZ3jz44TDxLtV1tQRq\nk/iQ+Rv5a0Hs4qKKBRk88orZ1WGZkyOWdO3nSYECMsWBTOez7yBpg3lp01OP\nSZkEc07v5q7qzX2OvAFK6QtN8i0k3UTynUVIwFoR4QLICIuBtSQg1aEKPBNk\nUf1ggHWU0QDacNh1JAikwu7vHf6XyPJ12uvxpWVjZk3XiM5TYwawukBVY408\nYRql+uh0rvMWC65L9rGQMAenMo4VycfTAjLOiotRQFAczGj/sl03lKNGal9x\nTKiVtfK0alN46Yl6j0EHcQsQ2I+X4BHfkGwX6cdnZc1nBnjy1oeKG3QM5a1u\nicRbYXc/yvtTg9yG5gCCOE0ZAnHTiWHUWbBOHQpkSz9K+nNGEdw1pd0UGOXA\n6evrdikSqTgvALj62fM7iVCULYxBXJSRkHjFq0x6v11UFYBmwaIju5GaToi6\nvxIBaueVCW0PpX2jB+BOUTv2VwJobW347M4h8JC5VVOHwXVYgr72hkoSSBn3\nfqhDSArpI0zP5jK5mSOW3caaGV2d4uT+xKB6sF8AOdkncidKREu/4x3Eo+uS\nK3VRYU4r3KvDCYxfFyBNdrc+0V9DWHW9/pigfSEoKGmfUIhVQKa+YDmV4bIb\nPDrE\r\n=Hu9b\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDKTR2CY4Rr4qo18QHjwDfAWG+7/ZXoE5953TKyOxPOTAiEA8LNDf4TYx1IH6Qr3zCpR7RXZH2FHXDMkWxw8X4rZ4p0="}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_0.7.0_1563093653072_0.21036012918448432"}, "_hasShrinkwrap": false}, "0.8.0": {"name": "http2-wrapper", "version": "0.8.0", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "engines": {"node": ">=8.16.0"}, "scripts": {"test": "xo && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"resolve-alpn": "^1.0.0"}, "devDependencies": {"@sindresorhus/is": "^1.0.0", "ava": "^2.2.0", "benchmark": "^2.1.4", "coveralls": "^3.0.5", "delay": "^4.3.0", "get-stream": "^5.1.0", "nyc": "^14.1.1", "p-event": "^4.1.0", "pem": "^1.14.2", "tempy": "^0.3.0", "timekeeper": "^2.2.0", "to-readable-stream": "^2.1.0", "xo": "^0.24.0"}, "ava": {"timeout": "2m", "concurrency": "4", "helpers": ["test/helpers/*"]}, "gitHead": "b3ed08d24b1aeb0b356ac0b331a5b3adbb749d92", "_id": "http2-wrapper@0.8.0", "_nodeVersion": "11.13.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-uB4tzeT2HMpoVijZc3qaEpbdCnRN2oRPW2bGsG6MYtCAmRnv2bfR3dow8fNDGiNFN9aJkV4vkEdI+M+SXTcDug==", "shasum": "ca2b758edb53440efd9c78388475bf268441713b", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-0.8.0.tgz", "fileCount": 14, "unpackedSize": 32377, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdKvU/CRA9TVsSAnZWagAAtB4P/AloZGUaF6NNA228L2HV\nMVwHpJA7/S6xha63MBmKRScbgNuKUAlN6imnTjp2yTFVIlTcrHdRq3ZPtzvV\nbXo5kMp65rsW8rnKpNuQX0z66A4/aaorJOPvc8Cz30uXFEp5inNj6M6mzbYg\no+NgFtyPX22wmcjpTXNp86/k550mjSKIWEMto6797qS97mPCq1M/z4WOBtAs\nFTVoZ1SGi5Yk3XRlPp4fKLf0oQITZ01CAC2lcX15Svft9nYJ0S1n9g7j9Z5x\nYXW/rRqsA0+gW7t/Nzn7ZK4365DsTPy0J+P8U+iWCOO8OMo/NkUeVLk9TlPh\noM22IPddpVSiYj7W2o4S0Y7ZzL3kDQuuhr0xQae+knWNyUVEAv/FCQBciqKU\nYsDf0p2z2+FSx9orvfva7+Ya5f/F/rWfY23Wgm+7+oVo+X1ORXyfc+CJ0SVW\nVTrm7p1+zFEh2nNmyowNEQgUO+y4JL6MWXxFYcIEwcJEgqJdwzM1Qo98q13Y\nJaRlA60UrOseB3OAAie3dVGFw6+cA2EXFAhh1vCqPXF6+XBRomCyfMlk9Lke\nMa7MiEZVYYzZZ9/FAbCc37/PQmv1/aLio/vRDCwa3ylGdpRb0GEkBfN65DBd\nxIsH9fWBSqS+KdUfS1mlxwdaesrOdXhpIQEEdS6VcUMhzM2lGPumlegH2OY0\nLSia\r\n=wu+C\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBgnv4NxuyAE1bgNI1IYH0GBs3weVykogcfH+iqaPuRxAiB52fb1hAebp3q5Fu0v910tfECjMxIkVI4A4M4RG2Wf7A=="}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_0.8.0_1563096382975_0.8549122962500226"}, "_hasShrinkwrap": false}, "1.0.0-alpha.0": {"name": "http2-wrapper", "version": "1.0.0-alpha.0", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "engines": {"node": ">=8.16.0"}, "scripts": {"test": "echo XO $(xo --version) && echo AVA $(ava --version) && xo && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^4.0.1", "resolve-alpn": "^1.0.0"}, "devDependencies": {"@sindresorhus/is": "^1.0.0", "ava": "^2.2.0", "benchmark": "^2.1.4", "coveralls": "^3.0.5", "create-cert": "^1.0.6", "get-stream": "^5.1.0", "nyc": "^14.1.1", "p-event": "^4.1.0", "tempy": "^0.3.0", "to-readable-stream": "^2.1.0", "xo": "^0.24.0"}, "ava": {"timeout": "2m", "concurrency": "4", "helpers": ["test/helpers/*"]}, "gitHead": "edd1e32204b7db74a50db1e9f59a22eae5856887", "_id": "http2-wrapper@1.0.0-alpha.0", "_nodeVersion": "12.6.0", "_npmVersion": "6.10.0", "dist": {"integrity": "sha512-mRG7A6RDkvKVuRmJFqD/BcfV/bblWFzh1RzJw4VRsJUqC0A0MsGXN92Qwe2wDj69TIybaAVBpOM6Csitc6g7uw==", "shasum": "e82a04f90254f5bf760e07b5921fa74ef50841df", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-1.0.0-alpha.0.tgz", "fileCount": 14, "unpackedSize": 33035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdMEu3CRA9TVsSAnZWagAATFQP/2UwMla+X4/pf6z74EdV\nZqY627PyHm2IIJV08m/pvdrd2aDY+uOK3HaZRPiUe5x387SepSTm9sXW4ssM\nm3i4g89NP8NW1/mG696t1rHXQlBMNcBODITo63Zwlvo7dh7p4UUEtcfb/ehO\nV5SUNV6W/B3xkZh+AvBoBuENH5+cJRwM7wcBfS6f/q9s2eB0Ki8cQ4v/pY+I\naffvTRU5sMAVIklZlupVjtff2TMQOC4uceQ6NV02Li1Gm1AFsONEYlQaXTSX\ncCgyJfTb3rXdYKxej5N0eOu69qD0brh+o2pzK6YwFFCgKPm1vSJ+psT3KGbw\nciqbW1K71Ya9bq0wox6nguDQQvSk00paCVLOkVpJIA5ePIO4aslXLG4kfxk5\nm8fP8kMeuT6/yQ0q3G6gRWVpuInBymsdbB3qEmpClROX/9o/LybL6MLVKqzh\nuyJBPrAKJbX4AqVTIoRPZjMCPBzfjRyc50EL8PEMdgEuAROGBVu26tIOW9Ld\nCuMLEC1QUeF3eH8mMtymwtMzQ1A0Mezh2fhqrzBm69eAPWNYmZYGjS0sdk6h\nMczOUo66rBGjf+fH5JrblLzBV+Mn12pv35/lzcDa8fb9BSeqnqTRfF6tpnqa\nBLVMNN4VRh+Og4lqpB00JxwMa4ed6HajTWV7lMf4tZY/EhTwPHfnWj8hr4GU\nnTJg\r\n=1NcO\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDtzT6yTLNxiyMYyrfefNXJavQjIQn8fzPLvqlRgPRmPAiAQ2VGaYfuY94wPUdpkjiNnvJx/EXvkKyOHMEMLLPvjig=="}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_1.0.0-alpha.0_1563446198392_0.8324560733342727"}, "_hasShrinkwrap": false}, "1.0.0-beta.0": {"name": "http2-wrapper", "version": "1.0.0-beta.0", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "engines": {"node": ">=10"}, "scripts": {"test": "echo XO $(xo --version) && echo AVA $(ava --version) && xo && nyc --reporter=html --reporter=text ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^4.0.1", "resolve-alpn": "^1.0.0"}, "devDependencies": {"@sindresorhus/is": "^1.0.0", "ava": "^2.4.0", "benchmark": "^2.1.4", "coveralls": "^3.0.5", "create-cert": "^1.0.6", "get-stream": "^5.1.0", "got": "^9.6.0", "lolex": "^4.2.0", "many-keys-map": "^1.0.2", "nyc": "^14.1.1", "p-event": "^4.1.0", "tempy": "^0.3.0", "to-readable-stream": "^2.1.0", "tsd": "^0.8.0", "xo": "^0.24.0"}, "ava": {"timeout": "2m", "concurrency": "4", "helpers": ["test/helpers/*"]}, "gitHead": "d0a50c9c22c3e2a64d29b62c495817e9787c6bab", "_id": "http2-wrapper@1.0.0-beta.0", "_nodeVersion": "10.17.0", "_npmVersion": "6.11.3", "dist": {"integrity": "sha512-A+w1F0KH1ujjqjaY5TEZOoxvwXtScq7/ketGZUFMrozoyFQvXvJuOWalEmEGQwM+YTKSTp+wiEC9gABMjXJ4CA==", "shasum": "4841223404fbaa2fb497fd78a6105ebdc8be09e6", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-1.0.0-beta.0.tgz", "fileCount": 13, "unpackedSize": 46341, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdvw4uCRA9TVsSAnZWagAAlugP/0iP0qaPY+zifD4LNmQk\nVR9YEYnpaKU7SQoGsoEWvp7bjldC5JwZNGOtlTU6Gw9ryBCdX6krhV1F0VYi\nfTwU6DCJZC36C1LjB5JeqE1/52tY/Ulxz2K6nb2c+9NFV+LnyQ2hqwCW8VXs\n4c8QpEu7+rkbsnWkdbrr2GczUc4BkM3CaTl2NiRSVxZYKlHKzIe06bF8Dnny\nvzezONMDM/ebqWcVhxsO50ik/1k50Le71MxAXlnFICOX34hfO77SO7bIzsPv\n5pqlFEt7meFtgV52RPHpuQ5hAdJv47ut2/Ypz3E59s2zqw1Xt064IAbk8oDZ\nnN97TTObiTb5WuZkzoqtK5n1FpFIuMR1Je/uc7xf+W7hju5wvpLeq8hzH2Ac\ntflhZ/NZ9cClGiVK1Q3b2kSflfxZrdb70n/IkVq5AjYcIj91gslvmIe0PuKZ\njkTsedQlOxAyReqQaP2aQKohOvFjn5gme8eXMdWlKgZD2GNNX99LajJ6P2TC\nOAzSBaK9MTN1QlvR6OZvSYgKAzSQd2mitgnAA5tUiDOdjfRxkcMVmLGoiuAl\nf//ImILB6CPQgO3bi/1f9yRektE/UtIjspf46dBclIu8Lh0GSzCfgeciXu2G\n6+MY6XBTBqfxW3j8+f7aBd9tBNy4l70nzAIaVBe80WWJAEoPTZFClV4Gpnz1\nrC2G\r\n=bZv3\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBmpF9ArzUgeolb3aZGzT32AbhWhJa4I1aBGceDfo9hpAiAvdwb/jyoHdSG6tn+8lVZIsNNo91WbHu6ei1eAO78bVA=="}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_1.0.0-beta.0_1572802094409_0.14807011907396217"}, "_hasShrinkwrap": false}, "1.0.0-beta.1": {"name": "http2-wrapper", "version": "1.0.0-beta.1", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "engines": {"node": ">=10"}, "scripts": {"test": "echo XO $(xo --version) && echo AVA $(ava --version) && xo && nyc --reporter=html --reporter=text ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^4.0.1", "resolve-alpn": "^1.0.0"}, "devDependencies": {"@sindresorhus/is": "^1.0.0", "ava": "^2.4.0", "benchmark": "^2.1.4", "coveralls": "^3.0.5", "create-cert": "^1.0.6", "get-stream": "^5.1.0", "got": "^10.0.0-alpha.3", "lolex": "^4.2.0", "many-keys-map": "^1.0.2", "nyc": "^14.1.1", "p-event": "^4.1.0", "tempy": "^0.3.0", "to-readable-stream": "^2.1.0", "tsd": "^0.8.0", "xo": "^0.24.0"}, "ava": {"timeout": "2m", "concurrency": "4", "helpers": ["test/helpers/*"]}, "gitHead": "763fb5f434aeb688a1c8b67fcf31c5e6bbe25f6b", "_id": "http2-wrapper@1.0.0-beta.1", "_nodeVersion": "13.0.1", "_npmVersion": "6.12.0", "dist": {"integrity": "sha512-tW4zg+mFVJXSLiWCTnQOSIBGEfIwBFhmv2oXvjHLlXyXwSrIOD0RZQ2kO6OfzAfZsiV66som4b5Mg84aV/U1VQ==", "shasum": "7dc274ada695b4b5f1a1dffd0daff01fd68a4901", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-1.0.0-beta.1.tgz", "fileCount": 13, "unpackedSize": 46452, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdvyFVCRA9TVsSAnZWagAAA1UP/1mFA6U1uDrzTxMISXN9\nVBnGom7m0Hzjhj4SquYWXCFNhWSkGqOkf2IoYKlcl/37AZKfWY+txYIP/PzV\n59JVdeYScxSU4pkoZq+oDIT4ruwA59+mX0s+xAsm0caYCHsk8Xtf05GoEkEO\n/0F1/aXKGmbg1QcrItA2FZ5p8m73qqzC5AaprRKW5FPFqiwVqmk/xB8QvqNl\nKPrWSbRQNWP2I5468+V4BrqTR72Et/fDU8itezWlt8F4eQNJYdFS/WlTf6xv\nAfD8hB1KvCub7FQpJSkr/G3SHmXn2I6DllWvZhJlrx8oQKv6ZZ+vbSnp4scA\nxlN0oEj5VbP6JMCcn/XHcXjzf2k4vbKHb30zRIW6rVYvgxUA6IDqMcILrMdp\nBAlhKuQHvaNvKiQtylgktXO7b6a48GVS9q4dqNRPoBnzkvhPx+4Md/ILaP1u\nuxfQot/by+g7k80Bp9TFUNcquiUSn88q7tqQbm6gsmI43FprRvpyyMFqpvTv\n7PGN8MYwRxEHHNE8IL19JkblaGiqvowQsBq/PbazsMXZDjzpWxiBFOstxPrg\nPRHcc100MH8uvJocNEn1RH015xd58/i6gY7PKmG4DfTqnm2RIMEZm2fi4wx5\n26FSPJfS68Ow4Y5ncN/yp/YyEbX5NOJbrPtLShd1a53+4I2g7weCpxyWUxq0\ncw0N\r\n=NWiu\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCu48EskhJ6avdhYGOG3E2YHOwBarpwbTibFC6T6ply1gIhAJ78FH/QezBg9FTKDShozgV8wqWE6h8hzm4jVqdNG4Pk"}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_1.0.0-beta.1_1572806996996_0.5853433579751015"}, "_hasShrinkwrap": false}, "1.0.0-beta.2": {"name": "http2-wrapper", "version": "1.0.0-beta.2", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "engines": {"node": ">=10"}, "scripts": {"test": "echo XO $(xo --version) && echo AVA $(ava --version) && xo && nyc --reporter=html --reporter=text ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^4.0.1", "resolve-alpn": "^1.0.0"}, "devDependencies": {"@sindresorhus/is": "^1.0.0", "ava": "^2.4.0", "benchmark": "^2.1.4", "coveralls": "^3.0.5", "create-cert": "^1.0.6", "get-stream": "^5.1.0", "got": "^10.0.0-alpha.3", "lolex": "^4.2.0", "many-keys-map": "^1.0.2", "nyc": "^14.1.1", "p-event": "^4.1.0", "tempy": "^0.3.0", "to-readable-stream": "^2.1.0", "tsd": "^0.8.0", "xo": "^0.24.0"}, "ava": {"timeout": "2m", "concurrency": "4", "helpers": ["test/helpers/*"]}, "gitHead": "842ce093b6e839fecbb3b188d33a8d240cf8f7a5", "_id": "http2-wrapper@1.0.0-beta.2", "_nodeVersion": "13.0.1", "_npmVersion": "6.12.0", "dist": {"integrity": "sha512-3BAqpoAfN1C8SiVJVIKGSdwp+JSrMWXL+UiZ1joE8sHZLeo8GhRsj/bGsqyuFA/9XPikkpR4ijDOVTRNiSgJBQ==", "shasum": "b2e5dfddefb908bdbfb7d97f882d8bed8e4c1994", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-1.0.0-beta.2.tgz", "fileCount": 12, "unpackedSize": 46140, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd4t7gCRA9TVsSAnZWagAADmMP/3Rbms3vO0Tf9yxjrzLu\nIsQc11x3b1j0Hs9hs5KvTVoyMnHi/4iijClY5UAgKuvjiH97dtOo6UJL3iWx\n2oM2ASeexLXkSmlrTHfQUGhLwuQALitLJLhAf3rZSyYKMVQCm039ckysnXBK\n1hR0kJepqfTnAHUusRIDiq4gApb0+1P3zGtwWqta8ard5+P7UKNN+vg1uNFG\nZBWPlfw2j8AmczmJJod5oaFON1p9Gyfbp74Cnjo2x+Rhx1LJWWMcGAx16dNn\nWjksegLLnvNfkIuxqL8gRWxdAxWCWBCVzVVtW3+QTrQS6KF9YUaw1tpTi3Oi\nlXX6VcamxtlASZ4TCQ/RcD3ZbBN+i5rbWQrRL6wqW8MeA4jcjgbx+u/wdlHm\nrOsT/FhDQ4m9DT1KoG3bFIRsNFREDBcKLm0TC6wafUHtkLsm9sUuXSuA5mmw\niLaK+taEJSP7LnIL8jaN4t5eDswXpeQAD7pb+vw+L7P4KdrMR/m+vQlC4ZxP\nhq4Dn8OEMzTDx3GQ451FO7zbw6la4a6KQU2/MObbvq19yPALp9kUU7/spjxj\nZXaXZCSj1LsSO8SvPYuHbqVoD8qterIqYpbgJp3cRKHbBsEctqc++MD1CHwO\np0xd/Kfwcaa8sBa6683MRUUB5Aq+G9qCnyJIk0I47lCnpuMsyt4SVb4SyrlO\nCU8m\r\n=4t/Q\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC+qbnXc+uljMZzrImk4IFVZBwnNBxxzyFatQJR49iPcAIhAOw1tj4sdbLRqk82vC2WzQJlQbwSXUd2nffd+tMg6VEz"}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_1.0.0-beta.2_1575149280432_0.6124514491568411"}, "_hasShrinkwrap": false}, "1.0.0-beta.3": {"name": "http2-wrapper", "version": "1.0.0-beta.3", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "engines": {"node": ">=10"}, "scripts": {"test": "echo XO $(xo --version) && echo AVA $(ava --version) && xo && nyc --reporter=html --reporter=text ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^4.0.1", "resolve-alpn": "^1.0.0"}, "devDependencies": {"@sindresorhus/is": "^1.0.0", "ava": "^2.4.0", "benchmark": "^2.1.4", "coveralls": "^3.0.5", "create-cert": "^1.0.6", "get-stream": "^5.1.0", "got": "^10.0.0-alpha.3", "lolex": "^4.2.0", "many-keys-map": "^1.0.2", "nyc": "^14.1.1", "p-event": "^4.1.0", "tempy": "^0.3.0", "to-readable-stream": "^2.1.0", "tsd": "^0.8.0", "xo": "^0.24.0"}, "ava": {"timeout": "2m", "concurrency": "4", "helpers": ["test/helpers/*"]}, "gitHead": "ee607fb509ffdd4b031ae8c64232b239526eee9b", "_id": "http2-wrapper@1.0.0-beta.3", "_nodeVersion": "13.0.1", "_npmVersion": "6.12.0", "dist": {"integrity": "sha512-Sn9zlWnUgFGlYqVf9Q/VKAPYSbeSMR+cPZ1NmFCCnnCQ3DcjTjGINc66hhfwQf7JhvvGKjvqy4hybuTXHzUsdg==", "shasum": "7c134ecc170d65a93d72b2924983e84e3753a2f5", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-1.0.0-beta.3.tgz", "fileCount": 12, "unpackedSize": 46053, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd5COyCRA9TVsSAnZWagAAg34P/3cr2eLeotL547vuPRkH\nehAyrYmSGbaFkLYFNRZ5DC8/gweto7+REL51jK+ar3ZUY59PksF2ARQc3cFV\nD09u+jrOcK6XzN8R3uIOGYYiGJXy5X1UEhpJgjcyCvlH05n8uWgMKCo4fWdH\nlL3vyJ1ylw7Ba05sVzPtTUUEJj9Q/joLUkEyVMzoyw+QoWyqSQ8dibqYWRn5\nG97vY9+krZf7cMI0jI8js1JBKZpqeZJ6H3Q9A1N6bhq35nuL9+ZGmgYiV1Xw\nYlWytk83dYlj9ard5vxbmn3m6sVO4I/QMSSn5cbOQnK1pg/NyNznZ7Vt13p3\n2omKzAzTBODL0Pm8mRqwdJFrzXs1PpsuKrkOZgimrhw4gC1tderehrlBrYAW\no02h3R6gtlmXe89/9vgHtPIFxXExzd5FRImWztpfG+jj9VdNyJbVaZDPTJAb\nQndvfUCzE0kUZKdZen/bqLt1YSB9khh3H6RIlGjDSLeIwBAwYZBSXTAYILRe\nBkLs6kmm182Z59mlpH4iIVqdXKXwlBCNq7aWdmHwm/7W4na7soy6k2KO6eKg\nu2napEEGaqgGKj1pg6qVAcEY4nUsu5+607xj77M7yD3PzTo4c/lBYZsRMyky\nDmUH+ly6Zs80rIoJZ+L4ouVdCUeMLGJtSxhxXty2uPJhnpL+3N1CtZubaPum\nfEv0\r\n=MjEN\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGGTLAGG/pvYJOXkJcfdo6/GGCPVd6mzzxgT2BLhV22sAiEAhUrkjt1KIfceXuPmR7Z+bZeXYTgof0KtYXw4v+gzBXc="}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_1.0.0-beta.3_1575232433649_0.956752778823821"}, "_hasShrinkwrap": false}, "1.0.0-beta.4": {"name": "http2-wrapper", "version": "1.0.0-beta.4", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "engines": {"node": ">=10"}, "scripts": {"test": "echo XO $(xo --version) && echo AVA $(ava --version) && xo && nyc --reporter=html --reporter=text ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^5.0.0", "resolve-alpn": "^1.0.0"}, "devDependencies": {"@sindresorhus/is": "^2.0.0", "ava": "^3.3.0", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "create-cert": "^1.0.6", "get-stream": "^5.1.0", "got": "^10.5.5", "lolex": "^6.0.0", "many-keys-map": "^1.0.2", "nyc": "^15.0.0", "p-event": "^4.1.0", "tempy": "^0.4.0", "to-readable-stream": "^2.1.0", "tsd": "^0.11.0", "xo": "^0.26.1"}, "ava": {"timeout": "2m"}, "gitHead": "06fe86263388ee1b9d9c90830bdfbbb974935690", "_id": "http2-wrapper@1.0.0-beta.4", "_nodeVersion": "13.7.0", "_npmVersion": "6.13.6", "dist": {"integrity": "sha512-XBMghMByX5W1+R39nQxTKvXF4q+9VTvEsVrpyKra+g/qEYzLqQuDPzn5NdpRGRVPBPfdElegM8C3CU+/K1t2mg==", "shasum": "ae4dfc34968d50f2fd05404849075ccea1a4c29e", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-1.0.0-beta.4.tgz", "fileCount": 12, "unpackedSize": 47601, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeSt2GCRA9TVsSAnZWagAA5WAP/1Tr6u3OIyGMTJ+NGY3r\n1/mXvAoSvmc7QIGigy676Ckc/u41xUSPSJI4W63ZMyLD/wMT4ilZUT536qd1\nP4BilR9ja1w7+m2xrpODtAwSyPwNjMBjMjmh8cUSbQCUrvOK8Hujj68r9bp1\nM8ys3Q9UPM0bwviFc0Ozon8UW3rlHpc7oGcWaf7u7HPubUJW0kiGAGBK2VH0\nbh8ALomgZQrOviHe9dqfsfdgL7hkb7cnhonOjCXqV8bFpT88CEdf4YkO8Yla\nVohgWUzCWM2YfVH67MDgPz8E118A/gg87SjETdR8Gt2DjVTuc/b5UwXCmRWp\naJjyqr4WGqE/g9S3OIBzIfeccj2ayn2ZC9vgb48DR5HCOx27DwYsgYAM93oj\nNj4Nk/ZMVdV7ZDv4DovTY8J9Vki09x3238FCnt+gKkxhkpcGX1dkB+4INZ5V\nBC+SfPhgAfEsIYnsUTQz7pqcDEyDT5RZd1IBxX5oqKDGgvEHfFhGuyqlAsbT\nts9tQUUcVWDDYn4V30B8u0nVPXO80FlXPQcia5/locO8P08OYtyNt7WVQCgw\n1jdBlolCX2F/Kg6cMlpoi5fR5sjR5148bjAi3BLkd7v0uyZk+fntQLRU1794\n2n5Ed1Xd1A9/SrLPuZCeUzcuEtQqqXRvCAgrKFhy6Mbl2E1yFxX+brvLXKrJ\nygNX\r\n=uKA8\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDkGOGZi8AVmJViHVbNQEJSbZjC0GJT9RNWSoZsN/uLRwIgH+5jWf/0HidqM70+afMNMa6VT0sTtUI57GRjVKY3mZk="}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_1.0.0-beta.4_1581964677819_0.3734992310940546"}, "_hasShrinkwrap": false}, "1.0.0-beta.4.1": {"name": "http2-wrapper", "version": "1.0.0-beta.4.1", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "engines": {"node": ">=10"}, "scripts": {"test": "echo XO $(xo --version) && echo AVA $(ava --version) && xo && nyc --reporter=html --reporter=text ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^5.0.0", "resolve-alpn": "^1.0.0"}, "devDependencies": {"@sindresorhus/is": "^2.0.0", "ava": "^3.3.0", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "create-cert": "^1.0.6", "get-stream": "^5.1.0", "got": "^10.5.5", "lolex": "^6.0.0", "many-keys-map": "^1.0.2", "nyc": "^15.0.0", "p-event": "^4.1.0", "tempy": "^0.4.0", "to-readable-stream": "^2.1.0", "tsd": "^0.11.0", "xo": "^0.26.1"}, "ava": {"timeout": "2m"}, "gitHead": "8c38c7e5ec6a56cc6e2901aa67844fea7f69da3d", "_id": "http2-wrapper@1.0.0-beta.4.1", "_nodeVersion": "13.7.0", "_npmVersion": "6.13.6", "dist": {"integrity": "sha512-V6RvReLl+qKMQuM8lPC1ax+0MfuYmiloqWHrovkBS9HGWaqm1K2+/+EFJBEGsTxUQuA1SCXSeXLvKMgP5FkNMQ==", "shasum": "82423fe8161153954ba8f0cdde8f3fee8adf78f4", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-1.0.0-beta.4.1.tgz", "fileCount": 12, "unpackedSize": 47633, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeWUd5CRA9TVsSAnZWagAA1awP/1erDdR8C45SEyd4mgy5\nY7d1f651NNb+AnI9vCQnneqhejwCnSpbXLn0BWyjFsgMbg8rZrceFVd9p6wg\nVnEmSgSaBvVJ46Ie0dCKWPlC598j2iSUJ0eFog46QnlQ9tLKKkybyG5Cv1+3\n8JoQ/+Lxf7pw7GO+FrQv3Foc+dZqmUKoFtWBnTBoI/4jEEgckajpYWgfFPRC\nRV6tKL2yloZZQBItrmCikUFSwx2kla0mHznitchdIxwhu1l4cyOm4t9St9Ek\npa+5fsRCF/cDOViYHMShaUfsHEYkn94wwlwJqVt6MYJl5jEETAtAN+dyW6k3\nnwv8oeaj54qIkoKR2Yu1VQjweowHeShziJW3MQ3PiWlzda2AoiW7v8/Bk1UO\nC25xwEORF7rCwOQZ4UNF2wfsjB7FTok5mHxFCJVF3m9Zn3HzdmJvlVLEYU4Y\nX+2qdMc416nB7/gN8cw7y/Be17A8ipHyDAUneoB+qEDoW/al77lWJz6tdg0n\nfZsjiSK62DX1ej0b3vcTia5/XMxa7rDVaLqjlIwXYzWdqTDd+uRV9pjobyC4\nktyJw4ooiw+DyCHE3NV2O6rPPktVwVYWN4rlGsiVGisx/YlF20mYlrzda1pr\nuZ/6swqw4wCXdoQ/407+IUCteSStplrF4MxWC5oqbvsdggQNzUwwP8YpgRh8\nLM2b\r\n=UfUo\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDKkZ1JpzLYhpvqk1XZObFWPZN/wpsx5wrDIZc5NXloMgIgHQ0fm60UskHO1dGHUsnzosT4RDhjrhLKJXx52IaCfXU="}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_1.0.0-beta.4.1_1582909305369_0.5026009482211509"}, "_hasShrinkwrap": false}, "1.0.0-beta.4.2": {"name": "http2-wrapper", "version": "1.0.0-beta.4.2", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "engines": {"node": ">=10"}, "scripts": {"test": "echo XO $(xo --version) && echo AVA $(ava --version) && xo && nyc --reporter=html --reporter=text ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^5.0.0", "resolve-alpn": "^1.0.0"}, "devDependencies": {"@sindresorhus/is": "^2.0.0", "ava": "^3.3.0", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "create-cert": "^1.0.6", "get-stream": "^5.1.0", "got": "^10.5.5", "lolex": "^6.0.0", "many-keys-map": "^1.0.2", "nyc": "^15.0.0", "p-event": "^4.1.0", "tempy": "^0.4.0", "to-readable-stream": "^2.1.0", "tsd": "^0.11.0", "xo": "^0.26.1"}, "ava": {"timeout": "2m"}, "gitHead": "8c38c7e5ec6a56cc6e2901aa67844fea7f69da3d", "_id": "http2-wrapper@1.0.0-beta.4.2", "_nodeVersion": "13.7.0", "_npmVersion": "6.13.6", "dist": {"integrity": "sha512-CxPnkyLBpmnHPJceN54cVRWfXmmzOhuTHydhV5vCNpcgu4CseIzWaJV+cibXbd9QEC4dwXnQ5eqIdwZwsmtV1A==", "shasum": "cd57caee93a0d4196f9ce4e98b5949dcd135cdef", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-1.0.0-beta.4.2.tgz", "fileCount": 12, "unpackedSize": 47633, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeWUeSCRA9TVsSAnZWagAA/qMP/jHJi5I2soIClhmrVhGm\njLuVaC0ai8aAiPZVxWePooMRLD1bXohbVqCEnLz0MMe5BVQ0m147Hw6xrBYt\ndeEmRie9wCx7DEo8Z1Tc2z17Al85VPkjEwmYHuB2T/G9dzD7zmJLpufuQfmO\nKCLr1fYnjg0Tc9R3UIoVzqPMwNbS4MThNpNSAzqOQ3ofo0utAv5Ccaj9acYc\nDhJsGH3VEQgSzXGaC6yVXFtmM8dEfnqRynQz2iR8V6NKdNQlFOhRfKsly0wO\nPxtUR1iMH8uXBrAgyDlOxpksUgzypOqlGSpxYmRywpxOSnQ2U3P7PDennJEL\naP9ujGyPKVKg7y16K8iE12Q0pwDAxHSbn4lYbrgEM3wW9zGFGmQa29ujAtOj\nujN4YwTrMF57GTunmWLe3I26TNdNzo6KP9FUCOlIQQW9lrPVlM+LEztcCe6J\nvuYTOjq2v/ML5B0eRz1m2MsqS3Pf/E+fU4Yk3jo1Elip9zTtc4JLYc4pUcPg\nxKsaxPBIVlSzqPLzZBrhjuy42gMdJjAvhmMrMsTHWAktY8aT1tjqIyVqTULF\nOkqsnA+r1ZGcpVtIzNQ8Nv+a1cMbYSLfPGgA9qKTfsZlXGCB49cmlcwGAvIb\nnmkGNaaQcfgt+OSH841SdfEuu3yaKOJhn111oBHr6VTGqhug1XGxVg8WV5le\nWrtz\r\n=iYi9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH6XgO6LORmA63r+1Jd05G0WyMIE6GLauHG3VmRLc2e3AiApHzQNNrzQasEf4rwBTumT4fLAOGAZzXnPouebMeEmOw=="}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_1.0.0-beta.4.2_1582909329649_0.19412262227574661"}, "_hasShrinkwrap": false}, "1.0.0-beta.4.3": {"name": "http2-wrapper", "version": "1.0.0-beta.4.3", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "engines": {"node": ">=10"}, "scripts": {"test": "echo XO $(xo --version) && echo AVA $(ava --version) && xo && nyc --reporter=html --reporter=text ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^5.0.0", "resolve-alpn": "^1.0.0"}, "devDependencies": {"@sindresorhus/is": "^2.0.0", "ava": "^3.3.0", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "create-cert": "^1.0.6", "get-stream": "^5.1.0", "got": "^10.5.5", "lolex": "^6.0.0", "many-keys-map": "^1.0.2", "nyc": "^15.0.0", "p-event": "^4.1.0", "tempy": "^0.4.0", "to-readable-stream": "^2.1.0", "tsd": "^0.11.0", "xo": "^0.26.1"}, "ava": {"timeout": "2m"}, "gitHead": "81dc79f25fb20265b1f860a77efe2971c3abfedf", "_id": "http2-wrapper@1.0.0-beta.4.3", "_nodeVersion": "13.9.0", "_npmVersion": "6.13.7", "dist": {"integrity": "sha512-r0Rr4+g7RF0RHBuArwOYHvFFcD2GchoXk4Q331K4h1IQ0kXqowXNBR0n71jQGzav2GHg9BnNYMC1dFxAr66rIg==", "shasum": "85b499cfada6a5b2c22f08190716c7dcc8a0bc81", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-1.0.0-beta.4.3.tgz", "fileCount": 12, "unpackedSize": 47940, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJebNaVCRA9TVsSAnZWagAAPJgP/3FDhjWFez+E1+3eYxbP\n9GNoBqMBVZkHd+85cHmiqZjbe6w7ZnNcuq8amoYbC5QpeGyAFrSG/MhpO8I2\nh8lg38Duc89GV1O7AsK2j0F3PrOMPKhUv4hjAHNNDle1tZtHdKIpnXC3Kcxv\niQmkp7rvJcX1ieQW0vMtOO/qO/lqKwdyjz5d+H9EFyLaYC5MT0F/HwZMlA5l\nSo9WY7b5N9iiEbIe+wG77/uMEKVn+/49K2BiaZETap+RRFMXKtjKrMNkvhyO\nxdZ5rfhF7AxCDxwvbXvEA+YqH/BuzL/iiKkOCWKWbIiMXbcURUR9kKzPdhG1\nhA0JqtL7SP8XMSjkpjBX8yiauEc1oU/VJTQmNSpS31KsALefJeCf94Wz6S0F\nudozekFkkA8wlplt1n0WD8hJq78iJJ/PMKRKpeBajfwg2Z4RuYyF6+OeeBEW\nJYhOCr1uSX1hIFiJwd205pedWuNfN4CwCnv52nwkn9RjE9mq9uZ2Zn+DZxUl\nbCzVq0IW5KXLKP7Gol/RVqJlpN7PXHMPsDeIelRN7ocgUBpK5wVi7aNg3U8C\nAmmYH96tpIXcSfeTFBZkuNKL+YMzIGM8me+4XCkWPwkDou9gzTs458n2sSZQ\nTtZZdlDutziBlaq2vyf2XZUE5DhLEeXDH9tZHfHzutvDj1naH6bptgn5UXLB\nA8BP\r\n=Tml/\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCrkMnsNZzpd17sTfwQH4vE8Dp0qUbMhkUPCuq6MPMS/wIhALM3LGbPorEk1WXB9ukscpXRs40n2ANpDkIxu+rf5iVO"}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_1.0.0-beta.4.3_1584191124631_0.497575235209071"}, "_hasShrinkwrap": false}, "1.0.0-beta.4.4": {"name": "http2-wrapper", "version": "1.0.0-beta.4.4", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "engines": {"node": ">=10.19.0"}, "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text ava"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^5.0.0", "resolve-alpn": "^1.0.0"}, "devDependencies": {"@sindresorhus/is": "^2.0.0", "ava": "^3.3.0", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "get-stream": "^5.1.0", "got": "^10.5.5", "lolex": "^6.0.0", "many-keys-map": "^1.0.2", "nyc": "^15.0.0", "p-event": "^4.1.0", "tempy": "^0.4.0", "to-readable-stream": "^2.1.0", "tsd": "^0.11.0", "xo": "^0.26.1"}, "ava": {"timeout": "2m"}, "gitHead": "fb185c4d78d702724701e604972b9d154a03a2cc", "_id": "http2-wrapper@1.0.0-beta.4.4", "_nodeVersion": "13.13.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-CHpjljc2VeZQ1Lk02dwAfOq0k7uY+MSmt9or0zQiyPr++BIngUKIDRoy/MfX0jVZbpG/GjGVZK3K9ac5pBS//A==", "shasum": "6e1c71b5ddff3037ce16053b20d1044b1e8c0df5", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-1.0.0-beta.4.4.tgz", "fileCount": 12, "unpackedSize": 47607, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJenzxHCRA9TVsSAnZWagAA9cwP/0RQxA03o0+9+i8b5RSR\nm3T2SDoXg944utuhaSnz9v8Vxnz0ce4VayPgNeWhtzyJDwarjVHckL2/GqIW\n+Q6mA9Qmjb6Li9whsWaOfd5ilHmbTuRnKvucQImG1aHrJAiJqdeRrSZX71hg\nG/i+YvtEm/fBAlnaZVdbLnFSktXA6W7Rg10FKsJC3tjUNqjTsMw5Ois9mQfP\nwqKCMd7SPfl8XF4fo4y4Ci5xFhGnYd50OxO32CeTViNaWGkB0xKTKClhCDFq\njQsrsBInrPaNdYb1WgwYQGKnLFcCxTTThB99K98yp38Q3trGWwlyxhtVwEPY\n7FByq7xoohLHW/FG7Di/z0Nhd/JnO5Jxftmv3oehRO38ujU3Mtd3SAR1WqS8\n1P6PMIjZgkyL4i/ORJPTcFS4G6G524A2EHs1Nysp8ISg/2jVSt9iQ2rB+4tq\nMDTlh7DdYeeonPJNHpxg3r5NcAcvfTspKporA5XwQzp7eZtS3NRhi43kYgcK\nnaER75qKntDcaOZ0G8x2ojCDSOnrQtRxcUBSHv/6jgDraGVEPoYwbIe3yd1z\njTgl44l2fXvZXNsuWn/Kfy2SErsPfW4cHeafB50Ed7ls4Rjj4JMRLd8vGWBL\nsWgP4PIRrNlnemnW803sy9x9K3/15UU9unhfRjmegSjuquD11Zz2vt7Zobxt\nXo8R\r\n=s3te\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEU1n0xeECFe8zYc4kE4Ji63HlpXa67K5JH+coGEEAEMAiEA+TwYNRSdmul/hvLz71gVJWHWCjZINJOSsl72/xmTk1k="}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_1.0.0-beta.4.4_1587493959409_0.9760588704647293"}, "_hasShrinkwrap": false}, "1.0.0-beta.4.5": {"name": "http2-wrapper", "version": "1.0.0-beta.4.5", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "engines": {"node": ">=10.19.0"}, "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text ava"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^5.0.0", "resolve-alpn": "^1.0.0"}, "devDependencies": {"@sindresorhus/is": "^2.0.0", "ava": "^3.3.0", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "get-stream": "^5.1.0", "got": "^11.0.2", "lolex": "^6.0.0", "many-keys-map": "^1.0.2", "nyc": "^15.0.0", "p-event": "^4.1.0", "tempy": "^0.4.0", "to-readable-stream": "^2.1.0", "tsd": "^0.11.0", "xo": "^0.26.1"}, "ava": {"timeout": "2m"}, "gitHead": "2a30f723bfdeee2d4cee0fa9369de3448a6a32e6", "_id": "http2-wrapper@1.0.0-beta.4.5", "_nodeVersion": "14.0.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-hRoAcIg26mAenbhZH4yQKpHzdbjHGM2a8JCtGJUIwFtqP82IeuMcmJwXHD6eFkILxDp0AyvaRMNrnV4aSaq9pg==", "shasum": "ac8e8f1cbf4aa79e3274c89e954d18697ab31e85", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-1.0.0-beta.4.5.tgz", "fileCount": 13, "unpackedSize": 48095, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqC8eCRA9TVsSAnZWagAAuzIP/iLdhEbke/ujx06pTlSA\nnvb784Fvzbu0lUHVXl47Z+OwMuTW4/oipbfNhzW9kg6F3nQvUJuvW2FKHjX1\nUVxqXvwXp2AQ01Jn+6dL/YBp0tZU16eaIfu2u3K2LJbFTqxEaJBfDY6G+wyP\nvVOc3Tpvi/0oJF++JIpiLLjDAvGyugVqg5p1tXqZnki9Dvj4jYjrp8jPRI0U\nIh/PFy4utpyGaBPy1OKrITxwbW7FqrTciujseXN90Hd6jKZEvx/Uid2KpzTz\n7h/N2YSDDQh/zWXuBwI6zs+SurPNfdZdKuw0OasOJ8zn9uFt/8T8RWGMgdt+\n2b7Pkzy1AH36B4kBDggcNioW0PLUiXR6I11s+W4WgNTr5/cfiTrnjSoILUu5\n3g5XzKXIe0XHCxL2/rVfa/QRmUTSJtB8q0NcsoR0Ox0S+gtEhxiMGcRfWsiX\nAuav+4tpKYiLyoz9l8rUGqN1hYmI6No9eYHURkfe0q6UHgZ2JacyV461OjeR\npbbhFRHexV3W5WMwb5Rc/dInz6OZGoNE3g0yfBw+GefYXgDsEbLk9fEIc7E8\n6H17idb+BYbqGF5bL46XnUSBQPN1AapMtM1Va5H4mnx+OHPf/9lLTN32h4Sb\npRu01Ckoui1YGCBJet8CqJ00YpUlLY9ZFuI1mOpCg1YfQ1RMow8Elcb9QeBD\n6CoJ\r\n=I9g9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCxsOzdi/ZPZC0JpLJBOgv9PG5g+noEezxKgYyu10C0KQIgI4vzJtRQ7MESAuZF7ByXPEtYG9SndOk+zf2DhGJ0fKc="}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_1.0.0-beta.4.5_1588080413852_0.2825970380162326"}, "_hasShrinkwrap": false}, "1.0.0-beta.4.6": {"name": "http2-wrapper", "version": "1.0.0-beta.4.6", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "engines": {"node": ">=10.19.0"}, "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text ava"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^5.0.0", "resolve-alpn": "^1.0.0"}, "devDependencies": {"@sindresorhus/is": "^2.0.0", "ava": "^3.3.0", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "get-stream": "^5.1.0", "got": "^11.0.2", "lolex": "^6.0.0", "many-keys-map": "^1.0.2", "nyc": "^15.0.0", "p-event": "^4.1.0", "tempy": "^0.4.0", "to-readable-stream": "^2.1.0", "tsd": "^0.11.0", "xo": "^0.26.1"}, "ava": {"timeout": "2m"}, "gitHead": "1d67f716852a670a082a27a5c434317bc3e042e1", "_id": "http2-wrapper@1.0.0-beta.4.6", "_nodeVersion": "14.2.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-9oB4BiGDTI1FmIBlOF9OJ5hwJvcBEmPCqk/hy314Uhy2uq5TjekUZM8w8SPLLlUEM+mxNhXdPAXfrJN2Zbb/GQ==", "shasum": "9438f0fceb946c8cbd365076c228a4d3bd4d0143", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-1.0.0-beta.4.6.tgz", "fileCount": 13, "unpackedSize": 48325, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeuFxvCRA9TVsSAnZWagAAVRcP/A4vh4WLqY3UwSxPOnWR\nd/IZSUiMuDHGrgpWEPGUglp5h30WSqliFRuPZm9K5tP3TTxYIw3yy2HAtM+e\nOWDE0qYJy9V6rSghDdWw5CX5d51BZLxQo66suZvGoZ4amIsPYcLf0yNBSCmH\nWVIiedXWsA/1k6ZX6Pcnx3IP6aYX2rarjvEsaVR9AFixUqEnlnHOE1Nr79v7\ndMKB58gBz8LZG9cpEhrNk4KRv1dPhoh9K6eKrd/ZPnSsgmBYlutAtvYRmZXY\nxu7dJOkfSWrPp7tJ4CCbQmcehCBybjve5GYpl8UOo5k4en40raCHRDa81+f4\nS2CiDSrzUaMFbo7SY6CA9qgn0eIPrhlYK7ksYY+veO46vssaXehn1PoVEUyh\nAiq7ogU0dqr9B+6DaqYpcc+cR7kCTizUM8IeboIa7GBn4DdCv+3kQq8U7abI\n3McokyU7waN0j+W9EIG8DQWb0rTGeSUSSDX9thFjcnum9afCAen5qx5OfxAJ\ncZ3YBihqW2RiVbMxt6tvTB9xgrTi2jvHn+JUtT/EfpZ07TPMtNr13w95UIvD\nHd1ZiVZ+ZiRsJAPys2fqqxj9sT7MIqkb+H6lMzDEaVzxLGwVUIMHIrwHFh4S\nVO59LxsvO9XFbkgmZ3GHC/HEdrJUjeH0oMsNdWczEEUEcft3PiClrZDYTlBw\ngRu6\r\n=cy8h\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC8J4Da6dNqYspx/wmgHFhldmSEcMcVD7wo5d6U99Wo/AIhAMXCIpwRpvZPVRfE7v6uQgFmC5MQ/QwjoqEW5K+27vbT"}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_1.0.0-beta.4.6_1589140591512_0.581673227358505"}, "_hasShrinkwrap": false}, "1.0.0-beta.4.7": {"name": "http2-wrapper", "version": "1.0.0-beta.4.7", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "engines": {"node": ">=10.21.0"}, "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text ava"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.0.0"}, "devDependencies": {"@sindresorhus/is": "^2.0.0", "ava": "^3.3.0", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "get-stream": "^5.1.0", "got": "^11.0.2", "lolex": "^6.0.0", "many-keys-map": "^1.0.2", "nyc": "^15.0.0", "p-event": "^4.1.0", "tempy": "^0.4.0", "to-readable-stream": "^2.1.0", "tsd": "^0.11.0", "xo": "^0.26.1"}, "ava": {"timeout": "2m"}, "gitHead": "d477ab95d117a365b6d9fb27efe0e9a5d99dd9d4", "_id": "http2-wrapper@1.0.0-beta.4.7", "_nodeVersion": "10.21.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-Lps+Stww4OdXyoQaHRiD1UG5v+iiM/QutahJrTw/0f3aGl52/lA5JZ7oVKwshln9SFUE54eT2denWhbEBFMcvQ==", "shasum": "f51885d21f0838348bdf656fb5443e6be49df666", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-1.0.0-beta.4.7.tgz", "fileCount": 13, "unpackedSize": 48905, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfAoorCRA9TVsSAnZWagAAhJYP/3bwUzLVZc8Hw5LiukGd\nE3Vf9AB/XWXo4/RrAZ/sTSiPfiGfmd3kc57Uy82Bp9wXefjYY+ID0pYxkjZF\nLPBtZ38yi3kSYJgFfKI1lYHIAlsbq58EEQqGPENJ8RymdFo0OlqsdeDlvPVW\naLO6zNIlLvhNLJNtb0J3ZmNcXsr7FdMO2vBjuWDj1AjIOSi0CQEZ8U5odg0p\nyuTr7a1KMVRDx6sdo9BmBteYax8U4e2HVkCLyLMXhxiJSTxqQ2a2/3M/sKAU\nJL2aMlp/8QQOezE8jyicBbROiVhcM+fV0Ust1hmpYAJNU23GthMOzVwUsRP/\nq8lBHq9EWFSFFaw4abVnaKYkIEz42XHTVyftxhqIlJqboWB4gNRfNeN7jaUy\n2SyLRxbmZAZlAax6Z7KkAkkpHD+F3xJqkzbbD76QZPfRZb30zgQhwCR16Rtc\nskzW19/2gDhFF7lqNhCwsNdTi3geYLuB/M+SuOELFFKw7IhVm8R7J7j/GtPI\nAMGsQbPsxHRaFLsTW7v4pKnzYVabmKBUkxD2Paf/LbOmLaBmXHKD428x7qwN\nRHyZH26DzmuXvW0Axci1pc7zQAvgjF1VkkXbheBLYzT2rLwl6Jk37+i7e1j3\n0zZpq4qCWwvF3mae39icot4NTB/XlK27UcVlatf/fiU55phuzLFlsUYm1jZG\nTmb/\r\n=CxBj\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC15ok+MNnWXIYdimAgMy6ISLtZdvMDOWQXWflVRFEl6AiBnt+lxSZkl/xoI5B8R/LptHk63qM9FmhgYvrYsv4k2/w=="}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_1.0.0-beta.4.7_1594001962964_0.7196419499827198"}, "_hasShrinkwrap": false}, "1.0.0-beta.4.8": {"name": "http2-wrapper", "version": "1.0.0-beta.4.8", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "engines": {"node": ">=10.19.0"}, "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text ava"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.0.0"}, "devDependencies": {"@sindresorhus/is": "^2.0.0", "ava": "^3.3.0", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "get-stream": "^5.1.0", "got": "^11.0.2", "lolex": "^6.0.0", "many-keys-map": "^1.0.2", "nyc": "^15.0.0", "p-event": "^4.1.0", "tempy": "^0.4.0", "to-readable-stream": "^2.1.0", "tsd": "^0.11.0", "xo": "^0.26.1"}, "ava": {"timeout": "2m"}, "gitHead": "494c46190cac68fd9c73cb9e0e8e16c352a3e90d", "_id": "http2-wrapper@1.0.0-beta.4.8", "_nodeVersion": "14.2.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-3XyRMia2QMy59nbxvXNX+57opqcy7/GlkJEaB7uRNw+TZw6mOiK3936IO83mVy7mzCttE66Cy9QEZ/xPXYmoCQ==", "shasum": "d3285f7421d7795f9dbcd4b57d92a2f73c0d958d", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-1.0.0-beta.4.8.tgz", "fileCount": 13, "unpackedSize": 48905, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfA2zWCRA9TVsSAnZWagAAMQkP/2Lz2BfJfOEH8wIvCCDP\nNRs/6UAa/JdewTYXiznIvuA74LidiSt0dgCtoZkiXLh7RxBRpNCg0a110Q8b\n+CvOySBDvGbDAngETAGgcpHVkwQNbg4/NjDomx37CVVhjEznDhppLjls6Qe4\nLwrGSDEuWTNmZAIYnTtW3EMl5+gSwdpjojgrZIijTPD8DwIHah6L9+FCTJOB\ngVzFHD+BfDVQuxRTb5UdB2MNPZE2eF7E/jJ3+AufIoxlPNeRrNQYpSobtlqq\nwL/0tacO42v9zDPKFPgTgkPp4EIQhS5bgi9jHTukppNde9I14j9NqNIOIyJC\nq9P5Ep6JhCsTjdBHarLVYOK0uf1DDR/WeXaHywNh28cQDxfA1A82eWKZ/rwb\no0Pc7rvPDdOQ+ckJKYcrreYJ4T7ymggFKmaWUd7HdnoIKA0DGBNtMmJMkHa1\n5Z3PStkVPNms/dN4GvDg3p7MqXxaVmPyIICoGuBuwVZ7Ldtj1kHugJjXW50l\nBmdXc02heyODA9+UgqhNREuAExy4Sr4cQWCeE34sTu6syW14Lo9xaw9vLmN3\nnqRrlAXd4LJPmWRheptn7WSCNKz6W2BLl6ob/HeHYzbHAFTyay/e+4MVi0aL\nUaRcyeCm6ESDEzYBEME/CmWX3pWWi5vfkqkX0vIF7YxlCoa6XHxvdBpBi5Jl\nF+zY\r\n=5L2V\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH8ikeu3BqckGYFTRhieOKPIkURuomK7fAb85uayzWvOAiEAonJ5UpBjFmpsSYwzO4QUSbiGSYCTc80ve6/GpWUh/QQ="}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_1.0.0-beta.4.8_1594059990555_0.5348367344553686"}, "_hasShrinkwrap": false}, "1.0.0-beta.5.0": {"name": "http2-wrapper", "version": "1.0.0-beta.5.0", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "engines": {"node": ">=10.19.0"}, "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text --reporter=html ava"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.0.0"}, "devDependencies": {"@sindresorhus/is": "^2.0.0", "ava": "^3.3.0", "benchmark": "^2.1.4", "coveralls": "^3.0.9", "get-stream": "^5.1.0", "got": "^11.4.0", "lolex": "^6.0.0", "many-keys-map": "^1.0.2", "nyc": "^15.0.0", "p-event": "^4.1.0", "tempy": "^0.4.0", "to-readable-stream": "^2.1.0", "tsd": "^0.11.0", "xo": "^0.26.1"}, "ava": {"timeout": "2m"}, "gitHead": "46774f94cb3d2aeaa304f447e8416d7c1dd3d7b3", "_id": "http2-wrapper@1.0.0-beta.5.0", "_nodeVersion": "14.2.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-yUzY7UQjB2uNg+xqOQLxdJ3QvaGpOrchBCmbyZ2zeC8I+CXrZtXfGp8VvyGz9W9Q4XVdq6+JYpclRiqWttZR+w==", "shasum": "ae76ef7c1310a2d13d6d77f19b39c4ff951468be", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-1.0.0-beta.5.0.tgz", "fileCount": 13, "unpackedSize": 50040, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfB6HzCRA9TVsSAnZWagAAHBMQAJBvrPo7k23nvSyQ6+JM\na8V82SONvIfVxAUcfXDCwhSY9/1ECQeWN9KtU20oXwU2iAOLoRYfWTczG3v8\nM8rCXYvIePcSyyiL2867+JvJR1OsEesnrsVQ9FvaBTqPp6omDHVVx6jkYy9h\nWi8ki9uxW4yILi5bftICAGdnCjJt1mXrKBfUTtd50JIu4Oszi/0dS8bXFXgI\n2+lqdYKR5BH9jEATWqHar27bWceXPT1n3H/Sw0ZZe0NKWFqQR5u8I5drOaxy\n8sMVXSywiLINLKoD+K/wugarLrBKGZlPHFws6msE6DUs2CJbBdGmrTItvelf\nYC5GqEAoaIPwP1AoFDYGMvqZy4S3ZouMlJtKMmXcWKHQdrK8SKke4hdzYFe8\n0Duj4P6CQ5mOkT2srASDWG0IFT98+P1+uMU9Vwal/5RP6TLAjkisZtg1J2Fp\nliEOVXn3gk3UNZLfau/4j7gitn+Iq4IbbRlHPrkRj7efyBw7bsji6qok4dKT\nkgGep/0KiSZK55VTEt5kT9a8PBSfk3Bk3gG15cUr8yF4RV62TWGgQIk27ZAS\nYVWHCvVaMAa0ZARxsekmxkpHcseTt9mV1pD8wPAdTegZKzv5nH4jnbcQ3SN8\nnPAkOfSt9wvhkKhWSM1xxCcPvODwERsxdPB+XxBmkKi/SEwYVnQQtsduy6QY\nXZ0c\r\n=YbKI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDk1M/trXw3sbTFuQaFEC7p/yw/DNbrjLXNuchBu+PFlAiAezssj17YExJWsdI7kFikC09Y1+L6qjcxGP1jk0xy3rw=="}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_1.0.0-beta.5.0_1594335731446_0.05496138546618745"}, "_hasShrinkwrap": false}, "1.0.0-beta.5.1": {"name": "http2-wrapper", "version": "1.0.0-beta.5.1", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "engines": {"node": ">=10.19.0"}, "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text --reporter=html ava"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.0.0"}, "devDependencies": {"@sindresorhus/is": "^3.0.0", "ava": "^3.10.1", "benchmark": "^2.1.4", "get-stream": "^5.1.0", "got": "^11.5.0", "http2-proxy": "^5.0.51", "lolex": "^6.0.0", "many-keys-map": "^1.0.2", "nyc": "^15.1.0", "p-event": "^4.2.0", "tempy": "^0.5.0", "to-readable-stream": "^2.1.0", "tsd": "^0.13.1", "xo": "^0.32.1"}, "ava": {"timeout": "2m"}, "gitHead": "b54f3a5cbeec56d0fb3eb0f1aa9f2b4f68d6b539", "_id": "http2-wrapper@1.0.0-beta.5.1", "_nodeVersion": "10.21.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-rjEF+kcLAr8UhXwnNDgnAmM5I5JlKiCm6oQyjdFSa+TUBbnHT8UXO1uU8IIzqxes/0CGHmru7r2QnYohburCxg==", "shasum": "71150c3ea23d992c66d74786296d0aa29b616cc7", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-1.0.0-beta.5.1.tgz", "fileCount": 13, "unpackedSize": 51598, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfCNwWCRA9TVsSAnZWagAAcykQAKUu/wV7s3SOUJAkcbz+\nBT7W64DRJFbeNnyEC1CeQsxLLFPyCjooTk/Z9UpLus/oTEc5GOZcxJbiqYzI\ngfaheUDHr9UPWoiAK2BvDbPI0tXow6701BfsQLs7S8/YEdnw+ZpWQbbBb8e4\nKkeUEyF+k8CY1ZEDJ1Z5VdHPb4ykrwyHSgJz50RY916jcETxh3Im+WswiBj4\nXoTlZ0qEJp2MbDhntGNqo1RfYCHPN27E2HLH6Curzidttx0oZF/gfqx8tiE1\nmlspVM9BDk6mQVtpNosJWVKcmObsv8cRiuLUk28oLFMpM/jf0uYsIQvFOF0E\nwdcOa53SjxzLl9eTfS/fv6W4HOn9v9vwchoQY4GCpOdXT20A1yOBcUlbq3Qz\nyL/RwZfAdIFiwCu5WQh6gqpvh2Nw/uqGkmVHRKaTdqKG6HkKDa20fFIx446f\nX6rHEP2JhuLy2Eu+3R1HCWPp77+9FZotxtOAWzTTNLxqTMpz0w8Z0Rdnn3Yo\n4pxtd25/VT8xa0kc2IXmAtJoUAgXjNqSXzm2HJ9eDORzP4Z0/YnjDI3mCY0F\npQZWzjRNlH+eKBVx+r3JJe+n5EeReJQajGng9HS1hi1jfdYGp25FFNWig9wk\nHgUb6LaiiVxFqxww+TY0FRBmYda3nyptUQna/uAJ6gvmziLIr/GMJlW8vUxd\nxj0C\r\n=t5+5\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCKj6T7yX7wdp9vYh2Pu0R3UCNAPLgM6lkd2PDjwlr1XgIhAOk5X1BM9KbFBK9DIOJA8nHT6FjxjMSrsPl99HCLWXcf"}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_1.0.0-beta.5.1_1594416149880_0.7719712730014323"}, "_hasShrinkwrap": false}, "1.0.0-beta.5.2": {"name": "http2-wrapper", "version": "1.0.0-beta.5.2", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "engines": {"node": ">=10.19.0"}, "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text --reporter=html ava"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.0.0"}, "devDependencies": {"@sindresorhus/is": "^3.0.0", "ava": "^3.10.1", "benchmark": "^2.1.4", "get-stream": "^5.1.0", "got": "^11.5.0", "http2-proxy": "^5.0.51", "lolex": "^6.0.0", "many-keys-map": "^1.0.2", "nyc": "^15.1.0", "p-event": "^4.2.0", "tempy": "^0.5.0", "to-readable-stream": "^2.1.0", "tsd": "^0.13.1", "xo": "^0.32.1"}, "ava": {"timeout": "2m"}, "gitHead": "875a33a6adebe2df1547d6452902c04bf51f2af4", "_id": "http2-wrapper@1.0.0-beta.5.2", "_nodeVersion": "14.2.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-xYz9goEyBnC8XwXDTuC/MZ6t+MrKVQZOk4s7+PaDkwIsQd8IwqvM+0M6bA/2lvG8GHXcPdf+MejTUeO2LCPCeQ==", "shasum": "8b923deb90144aea65cf834b016a340fc98556f3", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-1.0.0-beta.5.2.tgz", "fileCount": 13, "unpackedSize": 53118, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfCSPfCRA9TVsSAnZWagAABiMP/1w8L06YRbbGAyoQQ8j3\nbdB3Ef1lB6tiRSWOIpQGYFZ072amF6lt/YoKLuh9EaDaMAaCjcol70UQrzUm\ntxixHWBLaAURJE15Ya+WGzOrI2X7LFHTFcHmjz6553cSn6spV9hdoRjONrZn\nZF7HAxfeKFbXMWDwl9q1C3Dmj0j1fVJecFBPO6aau7EEqNqh5TgIC3v/b5d9\n71Tt1Kc3N5LHMH2IaXbWpQWgQfgFM2pPv/N+6SF8qOIS0ScirsrjYukZkkXX\n10lsEigLQBcl7FO5FtFSbbHW4KM1LbMfOmW7Z3+GbwSZbYS9pGaZwi/Ijdr2\nFAG7vEyPWcbl97NcORVW7qNl5sg0BT/rVQzf96ns1WgAozGyaXNRIat7FUxt\npWfVxqfvDF43Nwh6G72qHaZg63CtaRkPRo1dypoKilBFZuhVJtw6q/UGcSaA\nXRkiw98ga/1p1tqii0FSFG6QLxre43yBC/kFVmzbKEt0WYtsuQ8prjsEYrQi\n82Ep7QBXW/r0VvtvmTSwH4RQNvY2clJ86ksPplNJI0VkE30vlzzfon5vprgV\njXdFqnAjqmj6pY1lwKd7tIuUDkuueOtfbVNK3VYiZuQcy2VjRSKwxy5+10R1\nMj/3WIg+jtWwJhu9L7EKyBS/pE9Ppa3HDyK2OqPEPKSdsCfg9c50YGB925wX\n9qcG\r\n=7w83\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICJpDUpvakNpBnCpWcFZZLX5OHPzxa8PBjXWIe/1hSvlAiAgQ0tKClJ1BeG6A+tbgvljbwflAM+no1DWcRMO0IMQ8g=="}]}, "maintainers": [{"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_1.0.0-beta.5.2_1594434526971_0.19797404101828953"}, "_hasShrinkwrap": false}, "1.0.0": {"name": "http2-wrapper", "version": "1.0.0", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "types": "index.d.ts", "engines": {"node": ">=15.10.0"}, "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text --reporter=html ava && tsd"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.0.0"}, "devDependencies": {"@sindresorhus/is": "^4.0.0", "ava": "^3.15.0", "benchmark": "^2.1.4", "get-stream": "^6.0.0", "got": "^11.8.1", "http2-proxy": "^5.0.53", "https-proxy-agent": "^5.0.0", "lolex": "^6.0.0", "many-keys-map": "^1.0.3", "nyc": "^15.1.0", "p-event": "^4.2.0", "tempy": "^1.0.0", "to-readable-stream": "^2.1.0", "tsd": "^0.14.0", "websocket-stream": "^5.5.2", "ws": "^7.4.2", "xo": "^0.37.1"}, "ava": {"timeout": "10s"}, "nyc": {"include": ["source"]}, "xo": {"rules": {"unicorn/no-for-loop": "off"}}, "gitHead": "b92f832d28dea8a63e721767ad1b53912360213a", "_id": "http2-wrapper@1.0.0", "_nodeVersion": "15.7.0", "_npmVersion": "7.4.3", "dist": {"integrity": "sha512-2VYyYpVxapB8mZEkLo7o0qvJNFfhrGwE3zD1C2ivM2UW6AdeMGOEOE5xyQTwMj2MPtpxtKWQrivzN5abl9PAcw==", "shasum": "af946b951ba48438cf83fdf6d0bbd0629c74716d", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-1.0.0.tgz", "fileCount": 26, "unpackedSize": 68721, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgN78gCRA9TVsSAnZWagAAed4P/R5kos1xIomsMgZkRkj0\nRAa9IYGt7f+dVX7TznhVnyhW3UmdSW5K8koPQ6dwvcbdTw+UskGoCy203lkt\nVtvi2Qde9h8GAnTEBybk6szltFG/R5402sLrQZwarzjAeMb8hmlrjQySAQmG\nZP1mt0s2/UCWOJysw/7R60YOXPzfxBoCGkYq3p43sZXDcgl+Tjpmifl5nZM/\nfFgi2pWVXsThkWoztTEouXY8ediiSwxXVOLiiFxwyBgfv/4LNn2Xa+s3SRez\nOh2FXVE7W28gCF4/QfwY9/3ESUgcxZkXHwt14fgZzZRCssts5YJh0nwc/jyC\nM1CcB66VJWPEbEBe0S7gjlGzc4O0gbD28xK8cf48BxJqPk6c6XEVbkzJzX2x\n2341HXzoKGqGyc5HW/8hWLHlF54fI794ig2fqomxUlIdHCBLYDTKQ2M/SOEJ\nio8/aiqGtQdI+hyYxosqtvDtSucujd5xAC00yzAXt/4U6iKbtdGN3xmnCPiA\ne8G4g7k1htTY+4ftuoU2Xqn6XA+s8CD87A3UJ15scU0jsAsEomq4k7eYKJTA\nhOCbhdXVhDVG/zOIezswDk1o2ncMaFaTIQUodaU6B2enehdZwLAmnB38vsBO\n0vvoq+vBSazP7yaSkqMmeeJ5GsLVrmpOuRwyF7XT8KWUYdTjXoCuY7Da0rwb\nqRg1\r\n=pOQ/\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEoE8rVeEbUhz2IhPqVSSEzSWDjaAu62PTdNUFMpZnulAiBUnJ+N/gTQIHkD03C+R4DY47G4e6xPB5/F6c6DGnijEQ=="}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_1.0.0_1614266143698_0.516163775772787"}, "_hasShrinkwrap": false}, "1.0.1": {"name": "http2-wrapper", "version": "1.0.1", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "types": "index.d.ts", "engines": {"node": ">=10.19.0"}, "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text --reporter=html ava && tsd"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.0.0"}, "devDependencies": {"@sindresorhus/is": "^4.0.0", "ava": "^3.15.0", "benchmark": "^2.1.4", "get-stream": "^6.0.0", "got": "^11.8.1", "http2-proxy": "^5.0.53", "https-proxy-agent": "^5.0.0", "lolex": "^6.0.0", "many-keys-map": "^1.0.3", "nyc": "^15.1.0", "p-event": "^4.2.0", "tempy": "^1.0.0", "to-readable-stream": "^2.1.0", "tsd": "^0.14.0", "websocket-stream": "^5.5.2", "ws": "^7.4.2", "xo": "^0.37.1"}, "ava": {"timeout": "10s"}, "nyc": {"include": ["source"]}, "xo": {"rules": {"unicorn/no-for-loop": "off"}}, "gitHead": "47bab15ec479bd34a0f9d6a39c104a2d672eea74", "_id": "http2-wrapper@1.0.1", "_nodeVersion": "15.7.0", "_npmVersion": "7.4.3", "dist": {"integrity": "sha512-4u2ARcyXepY27EXX/bpr6dT0vj7xjt7/EeLThyjam6YJsklyMFX0+F2s0tHHYEk9bXHOfb116XZJBsqUzXsACw==", "shasum": "cf25c3fa648041f038abb0b261d15d01988545de", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-1.0.1.tgz", "fileCount": 26, "unpackedSize": 68721, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgN8qGCRA9TVsSAnZWagAAZ3sP/2cbHLc6oS7vILEBRR5s\nuvW14o+FL/BmU0y9cwp7bhjdXKvWO+9HVd7Ii4yrKRC643bUqD2AtrHFEU4l\nDWLeMuGlsK9GgpT1Rfh+IOb8Zs/9DtkCTKJ0uRZ/U9c7RgyzQefOkZGrwWC4\nJMq3821mMqNg63buqbSUuQfp442hwylGiC86TKyPrtcAhSG4C8dfqhNxpQJc\nVaNd5bFlQ+Vv+Mh15CWP65M9IS/xmqyvPpR7i1xYSlN9hbMLJtDAa+HOhjvj\nmKQf4iGydR31ZdpnZNxQ9Iti/rn9SX3IroWdcfvQ0fXsSc3acT6Eq0CpUIH7\nO95RjK4FPOpzQpSEnaRw/hB20JqDdliG6JnSQzpRlTZee9gI+E6WWJvyUAfA\na1ZZghJoVALsGI52ttsAHEq/XknW43OERaaFKeDVrd/oKTio2LMX++brt8NA\nyEkGf8fwHLnBRcuUTbEP8HQpokdlJIuMl380GobzEDHamdLKifMa1ifDCu+v\nHvYim1lyzMSiKg5hpY3ngnl2U4nt0azFmscrDemH3RpfCB//q90Hx0olpXN2\nZj7jTwUazV+HU2u3qaTZRGtLcGfJ9KtMwdnWWCflTSsiZvTcz3qgtUJsWLJI\nSL+DMXtvYuxugwAZr01WOHnVEuy1JUy8tp46Ew7nwcZNehqrEoV4c7HMJvWu\noc72\r\n=sR2B\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEMCIHz9h+AX2+bR8mUu8wZnycwYr1FcFwf4bdvLzRtXz/tNAh9PkrXmLBGqdalD9cq2pyAf6ceu0JDaAPKsppU95Nol"}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_1.0.1_1614269062041_0.13848645893582612"}, "_hasShrinkwrap": false}, "1.0.3": {"name": "http2-wrapper", "version": "1.0.3", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "engines": {"node": ">=10.19.0"}, "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text --reporter=html ava"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.0.0"}, "devDependencies": {"@sindresorhus/is": "^3.0.0", "ava": "^3.10.1", "benchmark": "^2.1.4", "get-stream": "^5.1.0", "got": "^11.5.0", "http2-proxy": "^5.0.51", "lolex": "^6.0.0", "many-keys-map": "^1.0.2", "nyc": "^15.1.0", "p-event": "^4.2.0", "tempy": "^0.5.0", "to-readable-stream": "^2.1.0", "tsd": "^0.13.1", "xo": "^0.32.1"}, "ava": {"timeout": "2m"}, "gitHead": "6400d9cf6f253e83f6d3348593f83488cd434b79", "_id": "http2-wrapper@1.0.3", "_nodeVersion": "15.10.0", "_npmVersion": "7.5.3", "dist": {"integrity": "sha512-V+23sDMr12Wnz7iTcDeJr3O6AIxlnvT/bmaAAAP/Xda35C90p9599p0F1eHR/N1KILWSoWVAiOMFjBBXaXSMxg==", "shasum": "b8f55e0c1f25d4ebd08b3b0c2c079f9590800b3d", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-1.0.3.tgz", "fileCount": 13, "unpackedSize": 53109, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgODTVCRA9TVsSAnZWagAAl0cQAIBRLUXacN7hWE1Dqpxl\n6CKpAsJLCDlUlEMB89a9rCs8a35RfdNEZbVROe/tMQy2yeTwSUDX+W7UHcM0\ncCuEP7elXbk7vHuSCjjS5e/ETcL6vkgIASvhG/NuClAtZ37zdE2F7MEzXkzF\nZq3h512T9XyH1OIFE5/a0nCQ72E+O2qrU76HvWVFHDgTj6AgzIAwi14jbdX7\nBvtBe14zY+LPnogyKIDDotuMvBc8Fs3cllTRSeJFqZnU6lA6SScrxlI550bv\nPPe8L04A2KzPZ5/a3hNdYRYvBc7HbNU6WEGGJwd1ulorrCIZtK+nJwrxGOrO\n5EJV8It9woXmv1iwi+/i/BFCABEkVEav4TV3zcv/TEFWNPzVsi6wqjyp+SfY\nB3IPNyX55gKTr7xKLQIpgPuHR5sc8SCYtIM+NX6oZwJrLG5ld5elU6HUz0dx\nVaTUWsSLja22RiVPPbFimA1hzyLnEALEQ0s85Pk93iejzcySMebyiDT3OS+x\n+aVLlgYKv1O1FxqB9+9g+nKUrDw4fyL1lHhOTlsxJnepIAFjp+zWQIE9A3zV\nsNq1z82SjFGyXAGYLr8+AcDcVP0jvo9IWdahqLDACxGjPyGZNBWulzwCQU8J\ni2lAySA1S/rQHxtLFIctMFh/Y/4L322q0RLbdXKDobXu1pfsmtZg08yQHmjE\n+PeC\r\n=8QIJ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDoSF/k3IbJXxKZAwzFVhpVtvq/FhHup/Uli1SUzYHQgwIhANUNjlkzI5SLafDLdti+m6WytKvWAR8HzE1g/GaIJIVl"}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_1.0.3_1614296277481_0.5756070954856376"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "http2-wrapper", "version": "2.0.0", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "types": "index.d.ts", "engines": {"node": ">=10.19.0"}, "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text --reporter=html ava && tsd"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.0.0"}, "devDependencies": {"@sindresorhus/is": "^4.0.0", "ava": "^3.15.0", "benchmark": "^2.1.4", "get-stream": "^6.0.0", "got": "^11.8.1", "http2-proxy": "^5.0.53", "https-proxy-agent": "^5.0.0", "lolex": "^6.0.0", "many-keys-map": "^1.0.3", "nyc": "^15.1.0", "p-event": "^4.2.0", "tempy": "^1.0.0", "to-readable-stream": "^2.1.0", "tsd": "^0.14.0", "websocket-stream": "^5.5.2", "ws": "^7.4.2", "xo": "^0.37.1"}, "ava": {"timeout": "10s"}, "nyc": {"include": ["source"]}, "xo": {"rules": {"unicorn/no-for-loop": "off"}}, "gitHead": "0773c56c4068231a266b46e70491a26a36828323", "_id": "http2-wrapper@2.0.0", "_nodeVersion": "15.10.0", "_npmVersion": "7.5.3", "dist": {"integrity": "sha512-owI8loiei7I6jOMMadPKdo1bMkieU0ZEoh/hts/R+Ld0c1SGq2EmVMU2ea47a30UO55f9lQZrXlp9D8/DJo/FQ==", "shasum": "d7f028f3c4f53f167a2a6f44fe683acc860fb36b", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-2.0.0.tgz", "fileCount": 26, "unpackedSize": 68925, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgODUICRA9TVsSAnZWagAAMB8P/j3zo/tmU8seIicmNI5y\nfayFvE1RAc9wF2lxYiF51/kxk5wi2zsruQjJ7DnRbQBrob0Dcdk+XtNWqrwJ\nmKmrDPe1Lx4JdyDkoGILqhA/kkzQ0aeX9MjjMSF3lVhnNzERYbFu7qr42XFp\n9xC6UPXAf7rOFJpuI7SzZ8Fp2odLDAj208H4vhaYXJblTP/A0EJGo+Ps8LKy\n2eikwU1VXYXxJ+XUydEeuRLEjgc+Y3lyjvOBAmM2snR2TTGL/PidEq6AanIS\nxUKgfPchnwywxweqoQ1GmwejkAOtfRexavo9pKqtD7JWfARlEYLIIr+NsBRF\nELRTrnVRtiTb0z6Y9xHRkes4TJTsN8eE1o4YTvxJO0Nh+im64xtOPFx9BN3g\ngcyrtBRQHD+0Ssh58OblBR/HUlzMFhjH1HvBR+ZLtSYnjU+vTdbHMHgQrUUi\nIKPe+T7PGbbuKuo2+7h8cMV0QwMpyb8tTe6hzPJgnEG2U35LmqTKkU7OQ9vy\nKGF2vo8W9HCfYJmmiRRQUCl/vVXm1F/DxYt/DLbgd4WNggChskYKALnHls+s\nm20xLQwUipzAviOSN/YeT0+SACow+RsU5lJ2koUUwI8t7cx/3hxQds3kBHSU\nb4I9L1F3zGElF9gTsGLmPA3QkiNKSG/Pc1UyhLaCD4hU4Zel4goXrjTHzGaX\n1Al0\r\n=/8OG\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICN7HFqRkRHJxvAHSOqCKx7aUGOAqJEq93BoSJFK2e9zAiBJa6tuiyGKEX8+JjC8VacYN3A3lwlJCPm3XyOB3OQwHA=="}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_2.0.0_1614296328027_0.5849345776525212"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "http2-wrapper", "version": "2.0.1", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "types": "index.d.ts", "engines": {"node": ">=10.19.0"}, "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text --reporter=html ava && tsd"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.0.0"}, "devDependencies": {"@sindresorhus/is": "^4.0.0", "ava": "^3.15.0", "benchmark": "^2.1.4", "get-stream": "^6.0.0", "got": "^11.8.1", "http2-proxy": "^5.0.53", "https-proxy-agent": "^5.0.0", "lolex": "^6.0.0", "many-keys-map": "^1.0.3", "nyc": "^15.1.0", "p-event": "^4.2.0", "tempy": "^1.0.0", "to-readable-stream": "^2.1.0", "tsd": "^0.14.0", "websocket-stream": "^5.5.2", "ws": "^7.4.2", "xo": "^0.37.1"}, "ava": {"timeout": "10s"}, "nyc": {"include": ["source"]}, "xo": {"rules": {"unicorn/no-for-loop": "off"}}, "gitHead": "6c0e0a3dc65a897d94e0cd38cc3a031d11c159df", "_id": "http2-wrapper@2.0.1", "_nodeVersion": "15.10.0", "_npmVersion": "7.5.3", "dist": {"integrity": "sha512-I3k0JUdpmgCsyMYGx+lx8ra6Rqvflh8ecf4RGuD9kag9NC4BrqdLwKoQo5XYX3IvcIF2IIyuvyhuRv3S4BdVnQ==", "shasum": "74037ab78afbad0be0481a7e796915d95256947a", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-2.0.1.tgz", "fileCount": 26, "unpackedSize": 68923, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgaq9uCRA9TVsSAnZWagAA3a4P/1efQtIkz8x5IYwxZB7M\n/rJye3so8ejTuMQ0GBBmO9LfrgFAPNw8zWAduVlSPsHeyIckCZbmOLzE2PMB\nZpG6drsY+XY2zHYFM9+WagignZOqrA7ZyC6JZNqx2utnnsWKV1EbPqe3iCfB\nG5w8Rbk6PwxMeNOg0TMWCDvCbmEtv2/OjFwWYc4cD82E3YKTIiGclQ4L060K\nWfisty7E8ARsB28z7fg1zuSfuo8MHirdE5qbAw3jy7sg5uaBxCorP2Sufl//\nNhPCwqkOa8Bg+yzm4iMSVPRo3ZstD+4dhwNpQup1gjJMGkCof49FDcAENW3U\ntmyNbrgVcbBSXndSYuQdcBPzPOpIhQuj/FlldGYdZyqqhUBHeKCEwzWIddZL\nW9f7lMewbd+Ujm14zplzys8aExklSvWiADA7pm+4kTZSTokhFXLV/wUfGPp7\nh3NPrZFz2Cf2IXEGQPK03RjSC5ZQlSk0jVYpWeVYQJpTX9Td+TsfzRJY6mVr\nkh72P+yudoxcUeiyUNeRxZilPLUEijofz/jNe9TYDK2DAgqOHNj4PLqspq9q\nPJctbmN9yLkWs3A89hnS02ArjMIC2MRJggAJKYiZqr6woLdhQPyLaILNXkii\nFFklug5kvdO1g5HoukCi+Srsu43gvXhcpNwOP1VgpRIg24ki5MtuncLNAkyi\nPeZX\r\n=P/7H\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDS7T548OKKoJ3fr4SnUbg+ULVDOn6YUqZqUtmPYBfxEwIgR+Isf9CnLbS+wUuryUQcKhYnH3fT16Ihs4vJRViqkgY="}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_2.0.1_1617604461398_0.09034741848073558"}, "_hasShrinkwrap": false}, "2.0.2": {"name": "http2-wrapper", "version": "2.0.2", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "types": "index.d.ts", "engines": {"node": ">=10.19.0"}, "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text --reporter=html ava && tsd"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.0.1"}, "devDependencies": {"@sindresorhus/is": "^4.0.0", "ava": "^3.15.0", "benchmark": "^2.1.4", "get-stream": "^6.0.0", "got": "^11.8.1", "http2-proxy": "^5.0.53", "https-proxy-agent": "^5.0.0", "lolex": "^6.0.0", "many-keys-map": "^1.0.3", "nyc": "^15.1.0", "p-event": "^4.2.0", "tempy": "^1.0.0", "to-readable-stream": "^2.1.0", "tsd": "^0.14.0", "websocket-stream": "^5.5.2", "ws": "^7.4.2", "xo": "^0.37.1"}, "ava": {"timeout": "10s"}, "nyc": {"include": ["source"]}, "xo": {"rules": {"unicorn/no-for-loop": "off"}}, "gitHead": "c7465195a17297513582868359ca1924b7247c7c", "_id": "http2-wrapper@2.0.2", "_nodeVersion": "15.10.0", "_npmVersion": "7.5.3", "dist": {"integrity": "sha512-wlSyEkb7yMhXiGc++UlaaawxsDo9E5IF04N8eirz/uSp4ZelcO77LpVNgedLYlTMAONcatJXdkJreMpIH3RXbQ==", "shasum": "ef932bcb6363b9d1bbabf354f50118f599915e8e", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-2.0.2.tgz", "fileCount": 26, "unpackedSize": 69069, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgc0o/CRA9TVsSAnZWagAAjPIP/iKtrLIkNh3YUObN2E61\nHlyjIqkJsEiJGitHJOgcO/JTXsmUrlxAWxsOZ2ydnwErgfWtmhOszd367FOz\nl69WEgtSZOjNCQ7PQd+1X9Hn3I/42cBrjzA3cbusUXGMyJn3UgE569g9jdjV\nPN8P6V9wswLQ/35WaVoPMW/34Hxve+6AJhL+/IaCR1Qq4ZgD+WTSvfCaCixb\ntYbbqI43bPVKOIsqYBjT22BnBSQ11EsZ69eAc724+Qv/UaH1fFDMN/5Lcg6K\n6tGAk07yEtrZFgH4ZtCoUDdOQpARxOaBIBkOD5dmRwYo6tOG+H14ySEbu3wI\na6/+bHrx46uN7m6CGI2YZ5Ahyx6sPfM5XwAnv1tJWGkiYFxdxQtkuirZDhDM\n/siOOcyk/9jg+Tis5pHIn52jluLGCDTE6eY7vVhQeMmqCrqvg62HhibRqX/a\nW+j3gxlhZFEXVYuvN5DWnIPbBIiyBly8DVyte2Y2jZBC5adem7ZPIlUs6lD4\n0rd/ZBUE3wKgZr63+Vb/vmhMoGrmcAeqmEdeYXDTFA4MCrR3ICIrTVKZpOmY\nOOPFUwt319D0oHlcBQFOd02Yim/dogROpYudHGntyzrga3x0N2jczS4c5d5r\nRzFJjzkgA56mhbwsDYNniGJHWDGENkLcqU4nQxwknGRPuzXMJ6T85RipB54T\nxA5C\r\n=jJac\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAD8fjsgUB1DheWxWk2fN3ZdtX+Xr4dkycj44g67fVi+AiEA3+6LAltLFZI/FFKqePFmyGhZmRQgh1lC3VtT2oNVYuM="}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_2.0.2_1618168383394_0.7818230089929035"}, "_hasShrinkwrap": false}, "2.0.3": {"name": "http2-wrapper", "version": "2.0.3", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "types": "index.d.ts", "engines": {"node": ">=10.19.0"}, "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text --reporter=html ava && tsd"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.1.1"}, "devDependencies": {"@sindresorhus/is": "^4.0.0", "ava": "^3.15.0", "benchmark": "^2.1.4", "get-stream": "^6.0.0", "got": "^11.8.1", "http2-proxy": "^5.0.53", "https-proxy-agent": "^5.0.0", "lolex": "^6.0.0", "many-keys-map": "^1.0.3", "nyc": "^15.1.0", "p-event": "^4.2.0", "tempy": "^1.0.0", "to-readable-stream": "^2.1.0", "tsd": "^0.14.0", "websocket-stream": "^5.5.2", "ws": "^7.4.2", "xo": "^0.37.1"}, "ava": {"timeout": "10s"}, "nyc": {"include": ["source"]}, "xo": {"rules": {"unicorn/no-for-loop": "off"}}, "gitHead": "72297e2e86e172bf15012b4ec50199e82ee96c58", "_id": "http2-wrapper@2.0.3", "_nodeVersion": "15.10.0", "_npmVersion": "7.5.3", "dist": {"integrity": "sha512-yVarx9zqcswOrYnjQF/cpFRJMplOK3u3uAW6O97cq7gfGdg+zyM3gERwrT6STCDr04LiIEFeRz3F/npE1YdfHA==", "shasum": "b2a08225fda6d016ea79fc75680209db5854a78c", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-2.0.3.tgz", "fileCount": 26, "unpackedSize": 69301, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgc1WbCRA9TVsSAnZWagAAGUQP/A0qE2WxMxmkPcxZh2qa\nvaOtZeRek7mOx7BU0nj+dUmOk6mOwwmvLwUAmbHyf8XfkYJsMUAsiJIvkx51\nlwTpXdlqCa7wvSb+FjykeX/DXEhqaGWEipkRqM1B7SFEkNuXiWE/L0MYHS2c\ncjgRrp1Ko65B4WmzO4cxSygXe3yzEAp1eK53LKvt61/gfXxzQ67u34nCqBlu\nQWGQsydJl+sQ+V2TyHks2fRAvf27Vc6ppJe/JhuzhaDOmbhZq4CVo6Oqdrnq\nKXtK/1TgkN3aoq/JN17EwDnkYTBxbuwatXKCn6SY1uPrpb3tHHLdC/ho4YeR\nqBWjk6k7tI/8FED7iHkblUJ8RzMVL/Cw9t/bC+6vZ9thdUEPc8tEJe47gVTU\nbKD9xtcz+UOyuLO9GjvNtrFLoMIFRnlvSOgnRpTNOVas9miFe+zZFul3eLkz\nVHeSI9LH/Rz7aIpFcGC6B7sAQwaH2eUl/Puhnx5D/J2kBvrs6sE5XZUOc4E/\nINp8MpC4JQmGP0oLTb2b+AyFXUAoVHe1xXZWkKFGsPaAB5Wkh4SAZZP/jrwd\nVaWGKdoOdRp7GfPDjNdKaobAPFebP/WOoUv3SZT1tke5LTvkDa0L0juj5PXw\nD+nVbA+OnAzLhiE7Vk2xB9WSVvQwaOO7OYQX6IlCHb9BoJIsx8Apvrm0soOT\ndoJ2\r\n=vmJf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICCkevRAdezlMm2B/rsu52gAnfcDrIcEvYV4ZrTPd40/AiBEydXDxBduRgNwxm5wtGGqiA6yRtYAlhXqAZBKwbGssg=="}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_2.0.3_1618171290518_0.3480129920610122"}, "_hasShrinkwrap": false}, "2.0.4": {"name": "http2-wrapper", "version": "2.0.4", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "types": "index.d.ts", "engines": {"node": ">=10.19.0"}, "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text --reporter=html ava && tsd"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.1.1"}, "devDependencies": {"@sindresorhus/is": "^4.0.0", "ava": "^3.15.0", "benchmark": "^2.1.4", "get-stream": "^6.0.0", "got": "^11.8.1", "http2-proxy": "^5.0.53", "https-proxy-agent": "^5.0.0", "lolex": "^6.0.0", "many-keys-map": "^1.0.3", "nyc": "^15.1.0", "p-event": "^4.2.0", "tempy": "^1.0.0", "to-readable-stream": "^2.1.0", "tsd": "^0.14.0", "websocket-stream": "^5.5.2", "ws": "^7.4.2", "xo": "^0.37.1"}, "ava": {"timeout": "10s"}, "nyc": {"include": ["source"]}, "xo": {"rules": {"unicorn/no-for-loop": "off"}}, "gitHead": "7a8c0a76cea0fbe6d9594e3b17d8d58abb77f7c3", "_id": "http2-wrapper@2.0.4", "_nodeVersion": "15.10.0", "_npmVersion": "7.5.3", "dist": {"integrity": "sha512-UIZx2QcPDzOxHbke6UQt2TDiLIfJQy5Z0moGXePpLYIBIubN0yr650r0NBjCaROzzEMmsfU1jF7dkkikaZ9E9g==", "shasum": "871e8e3c7fd8ddec21fb1a7d948b99e211bb7d86", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-2.0.4.tgz", "fileCount": 26, "unpackedSize": 69515, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgh4FFCRA9TVsSAnZWagAAeqoP/2EmsYib0+sXJkRj/9jk\nK27hGs95T7KNLxJAuoUYTpbcYhNgX9O1uglkABKJOahzbVtYkpSNwW5Akc1f\nWSahZ+UDyUA+qtnYiEQdjIgknofgQtFFYEb/5//SWkEpqBPjPxmCg+EYd/Ke\nmEv8iHi6ZgHQsmYLJguIFuWuM1g1hvMH1Xsd6/GfpaCUqp389nyVpvrRCKDe\nUiIzDcTDcLoR7wiX9qwpwj84Gl+qFF7CK/GM7DCVOw77rlAluYYoK5528NVl\nJmmm1TaGJIDvszdOuszEjtS6f4jAT7q99/KdogZFGYAOZtrLE2sKAvtcBMmH\nshOPobB42grhZqXwq0NI05uLERqj+pdE7zVX1Eg72GQthjfKkjSdaZgqSC0t\n86OjjXIaT8Rsm6qVIOjDTaHDuoEEupdGZLIYKjH4lFJr3AzvEY0+JhNhm5Gc\nfX6V7fSAAK6AJxMp0KdLAoxPzJRYVtHi2DVZVf2nSpZEb9Ro79+V/CjxFla/\n47XsnVvvhMcrdPZBiA4jSc8NOp3mV4jVTlNb6lSz/5H8r5dwcRJU95kSr8rX\n3CivzVMq06DhCMuN4aHjpiiiTqpJ/bVMXSKJxZxxzk1kLKJgIsGIJ/njGZQA\nqKIvCqnMpkp7fv7jn2PNnatqW+dKYrBkSgJqqw8uX8zp2HXxrg5hD78nFHal\nE1Z+\r\n=/8yM\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDFayha9UkAa3Z/yrBie+9yK/FJFglHHQMVCltRdJHVLwIgKudHSrs+nB9FxoWn7/AQ19CHAHqleO063nZN3pkjiMA="}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_2.0.4_1619493189311_0.8046602139227328"}, "_hasShrinkwrap": false}, "2.0.5": {"name": "http2-wrapper", "version": "2.0.5", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "types": "index.d.ts", "engines": {"node": ">=10.19.0"}, "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text --reporter=html ava && tsd"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.1.1"}, "devDependencies": {"@sindresorhus/is": "^4.0.0", "ava": "^3.15.0", "benchmark": "^2.1.4", "get-stream": "^6.0.0", "got": "^11.8.1", "http2-proxy": "^5.0.53", "https-proxy-agent": "^5.0.0", "lolex": "^6.0.0", "many-keys-map": "^1.0.3", "nyc": "^15.1.0", "p-event": "^4.2.0", "tempy": "^1.0.0", "to-readable-stream": "^2.1.0", "tsd": "^0.14.0", "websocket-stream": "^5.5.2", "ws": "^7.4.2", "xo": "^0.37.1"}, "ava": {"timeout": "10s"}, "nyc": {"include": ["source"]}, "xo": {"rules": {"unicorn/no-for-loop": "off"}}, "gitHead": "7a8c0a76cea0fbe6d9594e3b17d8d58abb77f7c3", "_id": "http2-wrapper@2.0.5", "_nodeVersion": "16.0.0", "_npmVersion": "7.10.0", "dist": {"integrity": "sha512-W8+pfYl0iQ27NjvhDrbuQKaMBjBAWIZRHdKvmctV2c8a/naI7SsZgU3e04lCYrgxqnJ2sNPsBBrVI8kBeE/Pig==", "shasum": "d4464509df69b6f82125b6cb337dbb80101f406c", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-2.0.5.tgz", "fileCount": 26, "unpackedSize": 69564, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgh+2vCRA9TVsSAnZWagAAQ6EP/RXCgSIWz2er1q9AE4Yy\nrMoK8HhjXQOlkXSSR7z8QMwYkXE8Z3o2v6dhHzkEchGrDD43LCPll7fDj3hy\nsTx6LUSEPZNHDGJkQ4sgMIEmjMrMwvRDpKinyQ1+uhI1A0b9xugR8BgnuxvW\n+z+wT1PJKeJICJDlH2jXC+LxmBUv2TLGt9o+G95Z336w9zyDwUR8v9dMWojb\nefSZrWJLhKW3VagdL3BQ+vE7sp1FtwqOn14aFzr9TSmMKBViGz4c/503Uho+\n4T1IjECQMX65kqQCOzYjbzdFTW7hyEMqXaKhbwcpb3ufpkDhSoJIjzUIMpN2\nJlfl0h/z1EEQGrKtrnS7ZCNS/SVosMvDHgy4WIMxXwaEisjewBE2zWaU51Pc\nHXqVt0/OuXIRmBQ0LBRE9R8Ism4aV9SK86Ero3u8hQab/UBFOy6kMZuNmrgr\nDwX+tsglxu17keE/DLBbYl/SFD3c01kAay8rU6Ysl55JWwNKupbGZRe0plI2\na+MwNit0ovbAXY7FLiJ8SvCcIWeqyr2UvT8WpXJC7a5nLHabTMpfHF3b+EjM\n6RQUNm/cFzDUhBVlNhPPsBvBaPy1F6ljFZ4VTHafI4ai8in71r9kD4KVXHz0\npe96/OH1TFawLk7/GUH5axxgJsCH20rtCe+Pr/e0DccIB6p59QZAhN8YGsBy\n2oMf\r\n=HYcs\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICNgNYT/OScSyZZpfuPb6157op9sAQfIQePPCCvud8FbAiEAtY3mV1buTPlM+6weixK9isOC7p+QdF7zqlKNGM8ybhA="}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_2.0.5_1619520943054_0.003547292848876893"}, "_hasShrinkwrap": false}, "2.0.6": {"name": "http2-wrapper", "version": "2.0.6", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "types": "index.d.ts", "engines": {"node": ">=10.19.0"}, "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text --reporter=html ava && tsd"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.1.2"}, "devDependencies": {"@sindresorhus/is": "^4.0.1", "ava": "^3.15.0", "benchmark": "^2.1.4", "get-stream": "^6.0.1", "got": "^11.8.2", "http2-proxy": "^5.0.53", "https-proxy-agent": "^5.0.0", "lolex": "^6.0.0", "many-keys-map": "^1.0.3", "nyc": "^15.1.0", "p-event": "^4.2.0", "tempy": "^1.0.1", "to-readable-stream": "^2.1.0", "tsd": "^0.17.0", "websocket-stream": "^5.5.2", "ws": "^7.5.3", "xo": "0.39.1"}, "ava": {"timeout": "10s"}, "nyc": {"include": ["source"]}, "xo": {"rules": {"unicorn/no-for-loop": "off", "unicorn/prefer-module": "off", "comma-dangle": "off", "@typescript-eslint/comma-dangle": "off", "quotes": ["error", "single", {"avoidEscape": true, "allowTemplateLiterals": true}], "operator-linebreak": ["error", "before"]}}, "gitHead": "7c7e8a9d5d9efda44fd24be7cfa5119ec4f84045", "_id": "http2-wrapper@2.0.6", "_nodeVersion": "15.10.0", "_npmVersion": "7.5.3", "dist": {"integrity": "sha512-H+hYLensBXnG2jPmG85pJVkkaiBMlktQo+IswUBQGzCUmfoX/C/1cY1UdJgJHRtd3rmB47QrxcD+nRcIBCUn0Q==", "shasum": "2c9d1f919f0936f45c1073894a20161f4d7d41ba", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-2.0.6.tgz", "fileCount": 26, "unpackedSize": 70137, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg7IRECRA9TVsSAnZWagAALQ4QAKNSwBDZRsGa2r3Oprnd\nMWk3kvLvqWg1EfJoyTgi3/KOXUywtsOE3e0geG7bYt9qBZUcHecwon5727LT\n9vgVdoB7afj7R3i4tnI09ULhyzw7im9ivnOoXpOCbFXm/PJpdDGnh+arCrzm\n0IGCbV5XVhlctiqLYuO1MmmU34bTUOCfx8GbBc1CUZVpR4MwRdVEUVB5RYTF\nT/ikGZGUpIJCBzI3dz3lkMevYLTIQ87wqDsYaqJ7OCziLyHCK/Jzs4iy8WEp\nfHljo7VA9qohnKCcDAH3Kvu0wMQCFawkzXX9O6q+jemlqfhbT4SNfhUyl0fz\ny5LE0PA/EZ39DY+ZcURAsFpYHbS2UHbOtNxRUEFx/2Y0NS7VRgRVMZYmFuRM\nEL/jgRq6nXzVxAHsiPAH9dziqR/YNe7VpWs9JgEuyQndx3KrwaJ5q5KYOV7d\ntTPDPVOf+tyLne/6Aq7rLltriyry25MXcSedcQXEEk1MXbeCPJ7zi9H4JyB7\nG/B3B+DrEMCfEBlew1nM7q0lRgqFBVzvLaBh2fx1uXj0Zx1bNUAfbTiPag7H\nloKaqeXeVx6wcwwQAMitLldo0/dp2UxcT2Tlfs8EfNKjDXXhDN1dR9v6pMZS\nrtmY1ef52vtkOMfPrVPo6ySfjSoQY6ASI0SK8gvOADouaLpLNIbneomv0KSc\nwPk1\r\n=conE\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC0c/A8dCxGQl/Ntc4jHUs2r59fDMTvA8TSiqFxUkbkYgIgOBdp+mczBTNPCYD7vrAoFg21cTA1wbQf4IGJMtz83GU="}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_2.0.6_1626113091945_0.20688944709276758"}, "_hasShrinkwrap": false}, "2.0.7": {"name": "http2-wrapper", "version": "2.0.7", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "types": "index.d.ts", "engines": {"node": ">=10.19.0"}, "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text --reporter=html ava && tsd"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.1.2"}, "devDependencies": {"@sindresorhus/is": "^4.0.1", "ava": "^3.15.0", "benchmark": "^2.1.4", "get-stream": "^6.0.1", "got": "^11.8.2", "http2-proxy": "^5.0.53", "https-proxy-agent": "^5.0.0", "lolex": "^6.0.0", "many-keys-map": "^1.0.3", "nyc": "^15.1.0", "p-event": "^4.2.0", "tempy": "^1.0.1", "to-readable-stream": "^2.1.0", "tsd": "^0.17.0", "websocket-stream": "^5.5.2", "ws": "^7.5.3", "xo": "0.39.1"}, "ava": {"timeout": "10s"}, "nyc": {"include": ["source"]}, "xo": {"rules": {"unicorn/no-for-loop": "off", "unicorn/prefer-module": "off", "comma-dangle": "off", "@typescript-eslint/comma-dangle": "off", "quotes": ["error", "single", {"avoidEscape": true, "allowTemplateLiterals": true}], "operator-linebreak": ["error", "before"]}}, "gitHead": "86bf5cdd184cd37bdaa2a70d84efd7a7c645cc2b", "_id": "http2-wrapper@2.0.7", "_nodeVersion": "15.10.0", "_npmVersion": "7.5.3", "dist": {"integrity": "sha512-FanPo0KDXKpIVVIW8txifCfQXAjWwZe6nWgTNiVdsmFj6UwvqX+RYrqyvn6SwMNV/zDEgqANorrK9xauYV5e1A==", "shasum": "0d95bbf74254fb850852d31f02f516b72e72904c", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-2.0.7.tgz", "fileCount": 26, "unpackedSize": 70120, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg7K6tCRA9TVsSAnZWagAA1lMQAI0la/M2QtM4ZOmArfsN\n16561OPgCedflGi76KZ35iaoRac9pklC/HczVvCTChOgLOB3L7PQYdNgC4eR\ng66+3k0KuBJJpHNBwWkGFjxrIi2N+Zm+/gZa0KtOwaDb1QQjn7jXrQR0jN2E\nTfGzy0xMXYtMsTI0DGPqrj2ZLHwJAXj1UgpuhSUxO/9sQ5Z/WTRw/8sSoLlB\n7FOK0lV+IWtjx1np4Ev9KFkvZwhEiVKb903RB7nmgz4qkRM0oEMUSo/e+MB3\naUySLHs8LTYPZCaQ52TmpODCdneLxmx/Ud6o51WM/j3yfeTFINuiOphC+VXr\njq/TzXLrYUPYOygAqFzYidNODv6AKNATv3w/+E+bOxv6xBM14ewDmy5c4sOe\nnT6vhsYqxA2MZdGiz9R4XdN+Bv/lHVYeLjpvMB4iqoxdxsMsmVyMu91BHi9X\nyR1lvOZI/mevIo7F5Ve4QMcvJS4k0PL0Qm79/Yi/V1XQsQTRmKmyWmrk/KQV\nR6q21hKwAzNC0U8WIE4PfgDZYXSwtCNynsmV8UCeSoR/t7p7d0W+WIyLlgKC\nsS7TvgujIabaRLXqwdS3nsjr2Xm9AVVLJmV3haeMGHbsP9lcCyPgHufSMWlU\nWKt4uTSZ4fKJ20AU41QdaVJJtRLwuMtJWDFCgLo5vXFWBsgDreWP1r65B9yt\nY6Tq\r\n=co5Y\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF/v//BnDyhWTyo3Z1D57ftUXj54RmT+yCbmR7h5f3r+AiAvv2CXAL28o0rhzbFxfadOcjZgCVMtjANDuuBMXP/x7w=="}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_2.0.7_1626123948810_0.6934213051275915"}, "_hasShrinkwrap": false}, "2.0.8": {"name": "http2-wrapper", "version": "2.0.8", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "types": "index.d.ts", "engines": {"node": ">=10.19.0"}, "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text --reporter=html ava && tsd"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.1.2"}, "devDependencies": {"@sindresorhus/is": "^4.0.1", "ava": "^3.15.0", "benchmark": "^2.1.4", "get-stream": "^6.0.1", "got": "^11.8.2", "http2-proxy": "^5.0.53", "https-proxy-agent": "^5.0.0", "lolex": "^6.0.0", "many-keys-map": "^1.0.3", "nyc": "^15.1.0", "p-event": "^4.2.0", "tempy": "^1.0.1", "to-readable-stream": "^2.1.0", "tsd": "^0.17.0", "websocket-stream": "^5.5.2", "ws": "^7.5.3", "xo": "0.39.1"}, "ava": {"timeout": "10s"}, "nyc": {"include": ["source"]}, "xo": {"rules": {"unicorn/no-for-loop": "off", "unicorn/prefer-module": "off", "comma-dangle": "off", "@typescript-eslint/comma-dangle": "off", "quotes": ["error", "single", {"avoidEscape": true, "allowTemplateLiterals": true}], "operator-linebreak": ["error", "before"]}}, "gitHead": "592daaaf328b297fb0a9e25c7e065857434bf423", "_id": "http2-wrapper@2.0.8", "_nodeVersion": "15.10.0", "_npmVersion": "7.5.3", "dist": {"integrity": "sha512-rmBwAAQzgzDNijvK9Yr6QhOPMjO4dqhN07Nza0xHZiW1+P27K/Pa1KYyxsLIzEFHd89LkizufKckg5HWCg7nFw==", "shasum": "3d486686f74399bf2dcb77b4047d115a68334c06", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-2.0.8.tgz", "fileCount": 26, "unpackedSize": 70140, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg82MbCRA9TVsSAnZWagAAQ38QAJPTYnAS0w0bO0rQUsbz\noZcuveJxmIaERyOf9yT81eTej+C2YtNTyoIKXhY5wlv3mInbIM8ozE9QkS/K\nnGndGum0jZh6Qy6m58c5urqB8DATe8RKpnWD3M4MgFI+QraT99rGRH7MFVL5\nBEuehjBTWVNgkO6skB239rfveWzYgdKkAE+pRoEn0d61PnBKfhXZfpVmbU0f\nXaPEmhez4v2i03PS34Qkng5W4Z/SPzrMwr1P1ARtAsJ7P6dOvFf3R292VdH+\n07wKI0juPfrFrKe9anrc6MHAPa8le559RfVfvge4D/6efsP00epP+wnB94G0\nlQ5N46hT/eoq0HrTt7+vIOwFrGaWDFtOT1/gm1/PuQAKd4nlgM5EnT2VT01/\njJLCj+1t2PklUSw8dyFp7mDGeieFVCb1MIFjboYKuXOpkSPkiOlIU0IlLZ9F\ng5Pur7C+3etHwBjJeaTWrDKu42CSKyljwYro+l5cmKZsNMvv0Ll/tn+grKdq\ncqnKEIvpY7FVHxLF2k1hNwOzPZW58gWx/Bhf9fPXg9FT7pjyqmdRZu7yAl7p\nQn9DdO0i5r/D+uKn4wmM0UFUuXcaVbHlIao0mTNB2v1x+MEc8fwGbvLz7Wpa\nrST858m74jg+Vp2mUMuSXIWyFKVKil/rXH0yv5BrpgqFClgILPrshT1UUwA9\nmtVu\r\n=R6sJ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDW4TzefLft/seYOyd8hUlom0lQ6Vtxa2C/KX4VCOIO0AIhAP3qUcqfI/iFgTfL2GP7X5iZzWuR/cyHbYPcFy08V6/A"}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_2.0.8_1626563354873_0.831545871565301"}, "_hasShrinkwrap": false}, "2.0.9": {"name": "http2-wrapper", "version": "2.0.9", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "types": "index.d.ts", "engines": {"node": ">=10.19.0"}, "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text --reporter=html ava && tsd"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.1.2"}, "devDependencies": {"@sindresorhus/is": "^4.0.1", "ava": "^3.15.0", "benchmark": "^2.1.4", "get-stream": "^6.0.1", "got": "^11.8.2", "http2-proxy": "^5.0.53", "https-proxy-agent": "^5.0.0", "lolex": "^6.0.0", "many-keys-map": "^1.0.3", "nyc": "^15.1.0", "p-event": "^4.2.0", "tempy": "^1.0.1", "to-readable-stream": "^2.1.0", "tsd": "^0.17.0", "websocket-stream": "^5.5.2", "ws": "^7.5.3", "xo": "0.39.1"}, "ava": {"timeout": "10s"}, "nyc": {"include": ["source"]}, "xo": {"rules": {"unicorn/no-for-loop": "off", "unicorn/prefer-module": "off", "comma-dangle": "off", "@typescript-eslint/comma-dangle": "off", "quotes": ["error", "single", {"avoidEscape": true, "allowTemplateLiterals": true}], "operator-linebreak": ["error", "before"]}}, "gitHead": "e2b57034686455d2aeab80d7d3d61bc7a0a0139a", "_id": "http2-wrapper@2.0.9", "_nodeVersion": "16.5.0", "_npmVersion": "7.19.1", "dist": {"integrity": "sha512-54StjdIuctcLOco22y3T/4wYU6tygJFisKwizffceCoy7FTpr02Ym7X6hz2YpJspNI+0RtoBkd7L/jUysZwXew==", "shasum": "b62a021dbc1d9a9ee0a50165b2c512d79d7717be", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-2.0.9.tgz", "fileCount": 26, "unpackedSize": 70612, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg+CdoCRA9TVsSAnZWagAA4oMP/jlQR1nv7P+Efn+HqA6y\nrtuFCObgMGE0QIG6bihDB7SSAOEpK+lcSnMhpLl8X4nYsjWPrYRk1hUKkt0y\nFoMogHadnOUL5Vq2cDh/jZWaAakk8jjVG6OLO53sqs5W7sxJKhkJLK29A32Q\ntbftLP5D2Y/kYZOwzCqgdyI99xvMJFTqZhnAH78AJiakDXLhpWlmyqKIoy7/\nFh8xVpdi6Q4RJON8AZj3dnx85hcwZx0qhCKmh4qSqlLBg6z9OnLN3p9iiFsc\nV0TFEgJeaG7ZrgELlYbFVTt5aGQQWSreg7yJ8aFpUYsJYus97rC3g+iADZrk\nHWiEpR/Ymkyuf2GhxlYpBaKlDqZk2dgEBK2/EwPZNTjNczed1cv2fCgzrcoG\n/4tz94f8RdyW6Y5InjIzXTdryZnjQdPkzZRiQieSLxOEGvKURtLxCG0oh8uZ\nhLmEIlAFrZXQl8JZux2CvW7DrdVlPzBoC0Ys5nn/gTBJo+GP4HwqMlY1JGd4\nt9ZLpnNPWpKshhifUS+t39dYrXcFQHRLJCoPQnACIyQy6Y0cGMpaCaRHpqG6\nHyF3JZFcCa1yQ1nexxsIMgM2Ci0+9qyjVQ9JdMe562hzSZnu/eIegZ/cKB5S\nWooPTfmGke/jToM/si1ot7FDEdupN7IagxPuM/qtvp0tLIESI706fgH6dSvR\nvoVe\r\n=c6+6\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDwOgZEzmEMAwptbt367XsdaBkEI4LA+o96AQ6B9uWB/QIgGbudTRIKTamri9cEOy4ZJP2ymThFyTgvQegGzgWh4d8="}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_2.0.9_1626875752363_0.6433786913716253"}, "_hasShrinkwrap": false}, "2.1.0": {"name": "http2-wrapper", "version": "2.1.0", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "types": "index.d.ts", "engines": {"node": ">=10.19.0"}, "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text --reporter=html ava && tsd"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.2.0"}, "devDependencies": {"@sindresorhus/is": "^4.0.1", "ava": "^3.15.0", "benchmark": "^2.1.4", "get-stream": "^6.0.1", "got": "^11.8.2", "http2-proxy": "^5.0.53", "https-proxy-agent": "^5.0.0", "lolex": "^6.0.0", "many-keys-map": "^1.0.3", "nyc": "^15.1.0", "p-event": "^4.2.0", "tempy": "^1.0.1", "to-readable-stream": "^2.1.0", "tsd": "^0.17.0", "websocket-stream": "^5.5.2", "ws": "^7.5.3", "xo": "0.39.1"}, "ava": {"timeout": "10s"}, "nyc": {"include": ["source"]}, "xo": {"rules": {"unicorn/no-for-loop": "off", "unicorn/prefer-module": "off", "comma-dangle": "off", "@typescript-eslint/comma-dangle": "off", "quotes": ["error", "single", {"avoidEscape": true, "allowTemplateLiterals": true}], "operator-linebreak": ["error", "before"]}}, "gitHead": "c93d67c099f99e91d43706cfd576da6e11178fe5", "_id": "http2-wrapper@2.1.0", "_nodeVersion": "16.5.0", "_npmVersion": "7.19.1", "dist": {"integrity": "sha512-0vLISDsHPGRQbl1WUHw/67JxbeO0/qz/YKIkmlOXdxUgW5YE5aBcw37eHcesW5yP4aSO8EtS2Y4jZQfGnVKq/Q==", "shasum": "5f134de55fb7f141a6675d47a21004ded126aca9", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-2.1.0.tgz", "fileCount": 26, "unpackedSize": 73692, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg+ETyCRA9TVsSAnZWagAA87EQAJWbkcX/lAAVSfEiHeDG\nU3Izp7Ebk5N7GVuy4Fa3ZVgbr/oCCLqBf7+IgX5UFbCV8bRY0TFoNc/wYFNq\nRqSsSbdYSCQr/QRDYdxmdBG/9PnPZGg7EtITwuyHNmeAa0OMPjkFggYjXjq0\n0PVv/Ep5KsDaCnmZgDe7zRjOptjz+pWqDso/PP6QLhAz+5Ar0lCs91/ANbgk\n3zjsOGhD07gPkL/5sI3lL5Jq1r9YnioNSC2CE1Ue9EXv36mRrw11IveuZP/w\nPYykixu2Vw8gU/GoN6U3emvxinfdPRvbYsbDMnnydbMSGUsdoji8FljdKt0d\naIhdWzTmzdFUUSjahNTD/gipETyc7oy8rsbociBi/hhqUunl2xThtwXItVy3\nmaDs4Kr2HVaZes1hEYbP/vVgdZ2Pwk9ZcBhev3u7niLzgCjszAxAblN4ZTsq\nQlrGbOiAJHKqwq/WzLDSGYQmsvBTGS6aESrpZjrgazfNrTZU1+gzuFQRJozx\n5GQ3RfLxCsc3qWNuZYWJrsV5gBrwIx4ovTr8Cz5AZd4FHW9mZkQqGV9w+bNF\nfTCgwv0Hqr1Gyb1+shiOm4RowhhQFrt2amvEiEj0unq1ZteICxwGEZK98tbb\nzgnmjelJQH3KDOuA17B6uS11TFBmHDflHJdlFLRyYowjiOMpXFSO0jC6ltPx\n8kcd\r\n=ZELo\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDXi7wjy3N5a8IkQfVdnknkavFimGe4juI6vqElfaC2UQIgHIJEbkkJ567yuqdCg8BphO6Zqj0RDOnuapKJAlx12ns="}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_2.1.0_1626883314360_0.6203063112489338"}, "_hasShrinkwrap": false}, "2.1.1": {"name": "http2-wrapper", "version": "2.1.1", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "types": "index.d.ts", "engines": {"node": ">=10.19.0"}, "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text --reporter=html ava && tsd"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.2.0"}, "devDependencies": {"@sindresorhus/is": "^4.0.1", "ava": "^3.15.0", "benchmark": "^2.1.4", "get-stream": "^6.0.1", "got": "^11.8.2", "http2-proxy": "^5.0.53", "https-proxy-agent": "^5.0.0", "lolex": "^6.0.0", "many-keys-map": "^1.0.3", "nyc": "^15.1.0", "p-event": "^4.2.0", "tempy": "^1.0.1", "to-readable-stream": "^2.1.0", "tsd": "^0.17.0", "websocket-stream": "^5.5.2", "ws": "^7.5.3", "xo": "0.39.1"}, "ava": {"timeout": "10s"}, "nyc": {"include": ["source"]}, "xo": {"rules": {"unicorn/no-for-loop": "off", "unicorn/prefer-module": "off", "comma-dangle": "off", "@typescript-eslint/comma-dangle": "off", "quotes": ["error", "single", {"avoidEscape": true, "allowTemplateLiterals": true}], "operator-linebreak": ["error", "before"]}}, "gitHead": "dd61aba78001734cf51a645b3681efccb45286e7", "_id": "http2-wrapper@2.1.1", "_nodeVersion": "16.5.0", "_npmVersion": "7.19.1", "dist": {"integrity": "sha512-0oZx87wxAeyn2zFcSXT0QcMoMmzNWr2eW7H9mf70kQPKenkuopc/IWAfV5OJcrHklFEZ0Oi5ehzA2WvREXqwSg==", "shasum": "ece069ebcea33c0edc4c5eba15e11379459c85a6", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-2.1.1.tgz", "fileCount": 26, "unpackedSize": 73744, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg+FLTCRA9TVsSAnZWagAAdQgP/jwI3HtSGsYLIrPlr6rl\nGuRzFDmUwfbS+HH5Eixx/mJsmjK0soRaxCk4iWqN/dsABCGuRD5LWWYv9EAS\nlGNKLRHivTAUmZ44bCVJUUeWOTMmC3FOZO73XT//schP9y+lGPjBEl1qnf1Y\nc5Gsd4adwjAo71b5cmtmLMWM5D8iNcC4u0v/2Ni9C8/UN6Eywd6mdDXii4Sb\nMfDFhw6SLqsGMqOPTX9POOiPVRi6jepqMA54BbBT0RBw0EvBZaDX3GUfY12P\n8nCPRSQ3nEykZdh5VvNn0HYYc6+uLEUkBJMtUMdWz/pJtslSL8bCYLlINKsH\nxZDtz/pXWoXs8coBuMtSkLGd6MIbN6BS1d8/yfRgZOm2pIT4kJ4W7O2El41q\nNdSePtCPuYnrmZVHVF+l2avM4Erpn+BkrkhlCFz6Sc2pDJnteeKd4x0BFzf6\n3Wf5EriG1v0UTf1UAxNIgBpdnZHtJft1wWHjOehKk7BavjlMDHennCE9wTpK\no1oSmgAGP6PHTIAAz++kou8ocqkZSKGtzaGeJev+q7nb5EFS+ubYx+/NZxG+\ndoJ0tdEEdQFmyPM6LMGJZ6O6VHxsVknMV3CqWtx4TY3IDZV/WIYeRd01/10/\nf2uOQDHxl3RRg4BEYvISHQCi5/X0Hob6FzfYQRqfdCxnXjfrSW5E+QF4W+Zv\nfcL5\r\n=pF4A\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD53k4YQojwNqtLt9GbXfXFYvDm+GBdQwdNY2IUSLLqQQIhAOUeNhkYYwvjTOjZutImlO0DPtexTc95fvfQC0lDYOJL"}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_2.1.1_1626886866884_0.17809578999549558"}, "_hasShrinkwrap": false}, "2.1.2": {"name": "http2-wrapper", "version": "2.1.2", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "types": "index.d.ts", "engines": {"node": ">=10.19.0"}, "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text --reporter=html ava && tsd"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.2.0"}, "devDependencies": {"@sindresorhus/is": "^4.0.1", "ava": "^3.15.0", "benchmark": "^2.1.4", "get-stream": "^6.0.1", "got": "^11.8.2", "http2-proxy": "^5.0.53", "https-proxy-agent": "^5.0.0", "lolex": "^6.0.0", "many-keys-map": "^1.0.3", "nyc": "^15.1.0", "p-event": "^4.2.0", "tempy": "^1.0.1", "to-readable-stream": "^2.1.0", "tsd": "^0.17.0", "websocket-stream": "^5.5.2", "ws": "^7.5.3", "xo": "0.39.1"}, "ava": {"timeout": "10s"}, "nyc": {"include": ["source"]}, "xo": {"rules": {"unicorn/no-for-loop": "off", "unicorn/prefer-module": "off", "comma-dangle": "off", "@typescript-eslint/comma-dangle": "off", "quotes": ["error", "single", {"avoidEscape": true, "allowTemplateLiterals": true}], "operator-linebreak": ["error", "before"]}}, "gitHead": "f594014c7c449f4a0027f6f5e1e7abd2f5a05c4f", "_id": "http2-wrapper@2.1.2", "_nodeVersion": "16.5.0", "_npmVersion": "7.19.1", "dist": {"integrity": "sha512-v8PcmeO0wR5nopZHMI3OD8Hu2fXZ8wlx091CK+0J5N34AqUqx5r+aY95EnPgoLxmySKdxhXBsEnY37lUZGRktQ==", "shasum": "71e7bd2a0f4e8711631fc41f384ef19f9d4d3e29", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-2.1.2.tgz", "fileCount": 26, "unpackedSize": 73909, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhDQhcCRA9TVsSAnZWagAAWPkQAKQn288v0mIKzLnRq7MV\nJRBfMQDpEHYtz4+7+IzYyHLLYzlWU42+Yx0/r711xUy0/oaokPv4GhfaeN0o\nPq+R+qHsx2IHOdP38FOK1Z0UJcF5UtZe6YffhThLJqSHfCqMQftkcr05nFgv\n5yO8jtFP0LpBzoCBD3ZVg8KxUs8vaXOpdDIQGeOXu6W2A1XaFqVkRnwrkG/n\n0nZQ5u99Lfgm60KhSqEOcrFib+f5glPrAajSXupIcgWoACgB1ijtrG3tRvfV\nv7yQt55q/H4c3hgbaoes/iJbgXT1keWA2tkoageFdjEazxbWlsvrIMhB6Ert\nJXI+yL+q4ldVY8J3+nIe8HJz6egNi5XMS9q2SJ44UeuogCjvqw+5RMddcBG8\nHfVQEY4oKsX/rH0Ffb1BlDf/tGXQ8SpCZcyYIEA82IQRjelyP7/34uK4gcAv\ndc7uC6FHVncWRsbzmtWX6JROq9ukixPQvJ265ftROlNtMmzVn490O7rySNBo\nBQqkCFhqUGBUqDKLZOWEc+vxVQwyAXjd2HQPy/tj+dPkJb8jN29b1tx1q3++\nmhml724N8Hhe2CYt1W6y54yi8ZZI9Nlwgh+GtqtGpKJp8uHoKxnCx7pneSJ1\nR9353/VoqABMQrHyZY15h9bc8FqCMdoBUsdeM4lWIDlr8CQplR6T0ovXDk5Q\naHcg\r\n=fyLN\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICgQAL9oexX9wumP4tLF+GPN/p3oUwZyTSlPfMj89SdjAiAHxxf2R4h0Gak0/54UK1UAJxm5wQuo8PFuxzroaI/EWg=="}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_2.1.2_1628244060485_0.7754347501980818"}, "_hasShrinkwrap": false}, "2.1.3": {"name": "http2-wrapper", "version": "2.1.3", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "types": "index.d.ts", "engines": {"node": ">=10.19.0"}, "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text --reporter=html ava && tsd"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.2.0"}, "devDependencies": {"@sindresorhus/is": "^4.0.1", "ava": "^3.15.0", "benchmark": "^2.1.4", "get-stream": "^6.0.1", "got": "^11.8.2", "http2-proxy": "^5.0.53", "https-proxy-agent": "^5.0.0", "lolex": "^6.0.0", "many-keys-map": "^1.0.3", "nyc": "^15.1.0", "p-event": "^4.2.0", "tempy": "^1.0.1", "to-readable-stream": "^2.1.0", "tsd": "^0.17.0", "websocket-stream": "^5.5.2", "ws": "^7.5.3", "xo": "0.39.1"}, "ava": {"timeout": "10s"}, "nyc": {"include": ["source"]}, "xo": {"rules": {"unicorn/no-for-loop": "off", "unicorn/prefer-module": "off", "comma-dangle": "off", "@typescript-eslint/comma-dangle": "off", "quotes": ["error", "single", {"avoidEscape": true, "allowTemplateLiterals": true}], "operator-linebreak": ["error", "before"]}}, "gitHead": "fc9aebe4da0ac2a053d8e021cfe9ca6cec4a69cf", "_id": "http2-wrapper@2.1.3", "_nodeVersion": "16.6.2", "_npmVersion": "7.20.3", "dist": {"integrity": "sha512-ZMeX9xKgacZ0OtGVQHFqwLpzfq9B/sDPonPjGaazD07Qdrf4/ZKBIXjrw5Bboxb3HudcLumaLdtihPl2nNVIlw==", "shasum": "17dc12bf39eb7983c30cb9d3e437891fa9976388", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-2.1.3.tgz", "fileCount": 26, "unpackedSize": 73941, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhGiSFCRA9TVsSAnZWagAA+/sP/3jVD5HOgEZPp2y5Ty10\nLI1B7Z2bZeF77Lmy1IqKazhXX/hfY7jKHEtqaeHUJ4ZAc/UUrePJyBR4tatq\nPr/uCXsu5SxbE0+EjcjdX4mh8zvij/uE8GQLtWj05iGe5d5B8jiUQ2Wgi6ut\nNC9kSgxO4dLOaU7TVYUbIHQQwbGsk66bApOItwUo+VDoJa1CYcHBlFzRiAWT\nGz/7t0H99HwoCvcdQbI4Er8rnbT5SQtctVReUBTbfvSHqYnEowe68MpaOEbX\nHQdJgCustFp/I4JH7uS3swkn9i8juAqt6t1a28o/4ly8/bN7e1V18bYAIJLo\nMG/dmUiK652x8Hx9v9Z71ucttKfnytPg/+qhOW6siWzA1kMtFF3HEie5iomS\neujgC8dCwnfxm8H2ABmsWKM5jglJ4yb+EJPZbAeDweoAP9QyESJtkEGLHprj\n9e8IvxUs/JYyBXqKDULBmHDzMoOMjv9ln4Qq1YmSMiKUnKpDcdcP+z2rqI3u\n2Kr0mSprR97HXP+CMOyeaJ0gi2WEcTzRyE+a0I3WKmsfRPAmpX37agWBcw2E\nCEbnaYfGbrfwEdvmColsUXffZ0WyRMwXm6vGZDwEpcqagM2BQ84l8VcwOWk7\noSsf7O9dAFdV1TitlSmx6VrGqgZe5Y5/GYyvb3B/5Lq6q2HYRbfZazjR6mCQ\n49lO\r\n=Jzaw\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDGAlIKOjYgO/sJg5l+9SWpDylZwp5YLfHXn5rVYP369QIhAPjQDUOgoBFNTVl2c1f/2Cu2N7NnCkmcJ7jFObY2zQka"}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_2.1.3_1629103237065_0.14148603853778363"}, "_hasShrinkwrap": false}, "2.1.4": {"name": "http2-wrapper", "version": "2.1.4", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "types": "index.d.ts", "engines": {"node": ">=10.19.0"}, "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text --reporter=html ava && tsd"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.2.0"}, "devDependencies": {"@sindresorhus/is": "^4.0.1", "ava": "^3.15.0", "benchmark": "^2.1.4", "get-stream": "^6.0.1", "got": "^11.8.2", "http2-proxy": "^5.0.53", "https-proxy-agent": "^5.0.0", "lolex": "^6.0.0", "many-keys-map": "^1.0.3", "nyc": "^15.1.0", "p-event": "^4.2.0", "tempy": "^1.0.1", "to-readable-stream": "^2.1.0", "tsd": "^0.17.0", "websocket-stream": "^5.5.2", "ws": "^7.5.3", "xo": "0.39.1"}, "ava": {"timeout": "10s"}, "nyc": {"include": ["source"]}, "xo": {"rules": {"unicorn/no-for-loop": "off", "unicorn/prefer-module": "off", "comma-dangle": "off", "@typescript-eslint/comma-dangle": "off", "quotes": ["error", "single", {"avoidEscape": true, "allowTemplateLiterals": true}], "operator-linebreak": ["error", "before"]}}, "gitHead": "cceda0048625ba1ce8579b3c7473968ef6f202ac", "_id": "http2-wrapper@2.1.4", "_nodeVersion": "16.6.2", "_npmVersion": "7.20.3", "dist": {"integrity": "sha512-On8RTSivn1IdX/rSjQmFV2pVfH/t8juFy4fjAs4Rp1naYHS10JmfMpChRrq62YQT5dlx09YWxXjiNck3Abld2Q==", "shasum": "ef114c049a48862ed798552b75548edd236b7a84", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-2.1.4.tgz", "fileCount": 26, "unpackedSize": 74029, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhICCHCRA9TVsSAnZWagAAfyEP/jWz4/W8F+pSyI7+Ykpz\n36OIyOJAYOzmlzaTw1QrNKZaGebx+KOcDZfMkx8Qua3ma0EG/Am8fXyBKtU9\nxPF4VVj1dRpfkp+CBWpxUaQBPKXv1OKihuRRI7oQoF4hCirOaBygBOiVDosO\nkoRyGD70fNoOwc1lwV3N7Y8dy/coFKggW+TaDroU+kI2QEYfooXuUw3vKPDs\nVvoD8cle8a1ouHYgrTRE94TWqDwnA2ZzAQsr57asdn9PSX8rrCFrTYmkTpJM\nKa8ouGNrUgiz3aAnZvVBbvnfpuKaiXy9XyxtDKSlT1NUlJr2SIRKAoRm550e\nUalCFIPBvBw4T4IrHzs5sUOn1du96I+rnb8y34I4FkI3i2AU5LTf25jJUFoN\nVw01bOTn74n8XD14izfMafMvNLZBHrzG76wBfvIUILJWWnHJz1zbIKykqpLr\nL9Zynz47efRP9I4flQjNxulDG6Vv/e8bEQbl2UmebQlHiN/DQN/L2ZbqGbfi\n+1iwQGW4tqX2C3jUXIU47XD2zzQfhNv9UxH3TYFoBFqH5/+p+gt2kgTSPNX9\ny75zUCmgEKCiUlc0VddMn5F3yKo+Yv+Dcg43fnBtLfMmdIOBszgzHyVQHlw1\nVQh1npfglYzEFfQpdbFMFMHW5NjADdujBn05n6wnH8nS8zURgYEtrdsLjaiC\n4OQ5\r\n=0cF5\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD4nSj1jg7nxftw9cArKqVYWPEUcRifhWggMFYNSzpeSwIgKiQ1DOoA5efiaZ3lgyD+3h1zy4SJcIE0vvRDuQ1FhHs="}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_2.1.4_1629495431458_0.5790639156702069"}, "_hasShrinkwrap": false}, "2.1.5": {"name": "http2-wrapper", "version": "2.1.5", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "types": "index.d.ts", "engines": {"node": ">=10.19.0"}, "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text --reporter=html ava && tsd"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.2.0"}, "devDependencies": {"@sindresorhus/is": "^4.0.1", "ava": "^3.15.0", "benchmark": "^2.1.4", "get-stream": "^6.0.1", "got": "^11.8.2", "http2-proxy": "^5.0.53", "https-proxy-agent": "^5.0.0", "lolex": "^6.0.0", "many-keys-map": "^1.0.3", "nyc": "^15.1.0", "p-event": "^4.2.0", "tempy": "^1.0.1", "to-readable-stream": "^2.1.0", "tsd": "^0.17.0", "websocket-stream": "^5.5.2", "ws": "^7.5.3", "xo": "0.39.1"}, "ava": {"timeout": "10s"}, "nyc": {"include": ["source"]}, "xo": {"rules": {"unicorn/no-for-loop": "off", "unicorn/prefer-module": "off", "comma-dangle": "off", "@typescript-eslint/comma-dangle": "off", "quotes": ["error", "single", {"avoidEscape": true, "allowTemplateLiterals": true}], "operator-linebreak": ["error", "before"]}}, "gitHead": "92c80623446d9ccf2d8e1de0791103c50a6d23ef", "_id": "http2-wrapper@2.1.5", "_nodeVersion": "15.14.0", "_npmVersion": "7.7.6", "dist": {"integrity": "sha512-sL5KnQjHpny6DE00nBlyUICyLNXJ5KhAJ1C4yLbfmYhGpY97cC/QcuMxCpQ6kE2jywwid4VizAt7cOaAIIxZKQ==", "shasum": "1416adaa02a82215f4e26b778d884859a5145bf8", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-2.1.5.tgz", "fileCount": 26, "unpackedSize": 74338, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhKMUJCRA9TVsSAnZWagAAofYP/ivpM2Bldil4MDvMJTbN\nL316W2JJh6dze7BdA4NGu19YE+kmzbNJiXuRkHnC8hU6nn5cd9aGTVdFVqbN\nKH3xVHy0oZs4vTABWsNVO5AZtcHH+jYHnCdoHEcmQ4ksitfyKzNlK7JbWF+A\nG5jsONAP+k9EV2yLQrtK3AUY2EAnrm3tRe46tNup5CZX1e53Uj/uyyryuIZV\n42VHEgJCeVapLOEo/G9P6tSw0FfMcJesLi5Jf5Hwy8d9pgc9V2L3CCf0rOGX\nM3+sO4CQ531UDpHABlSigTjBmUtEPOYgXG3JfQjabndPOn8XK7B3SbJ7PoKz\nOayC2Z4lMrXeCb/VhmWxBot0yH0RUiTenTEOx50PRGcwO7PFFZZ6ubIfZBxf\nXDplvwbET0kJmqoQbDIvIEL+UXm4/oUiWbby3FoNcU635pTTvLRA+/1Rhti/\nKwDhfWX9JOelTZmoT27hD0WnO6Ms0/nJLXwBooAzVhD0uAar0+H86qjz5MXb\ncoh/lAKfaVauVbXQPKB7MDTEZghhZzZwPF++gTeBfvqE7qAposUGtbGNXGK5\n9yscfCOPjPoy+erQTsXi4Q+2n2G2VymV41AnRfDJL5xgxbvIuTbF4mGU96NK\n9ZHpv4n8Tll7gWDu7kFCEwhGiIHsIvV0hO0cehVNU7oW6Akexe1VkuQOkq2R\nXBNg\r\n=8rps\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDHsl0Hlqr0Exs+DpWenZ6cqknWdcoPtUS+swr7x3E9/QIhAJ8RUeaYLNiQBKdaHxPOQlosh9Vb29RYoT17M5eLjjHs"}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_2.1.5_1630061833547_0.7868665804732689"}, "_hasShrinkwrap": false}, "2.1.6": {"name": "http2-wrapper", "version": "2.1.6", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "types": "index.d.ts", "engines": {"node": ">=10.19.0"}, "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text --reporter=html ava && tsd"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.2.0"}, "devDependencies": {"@sindresorhus/is": "^4.0.1", "ava": "^3.15.0", "benchmark": "^2.1.4", "get-stream": "^6.0.1", "got": "^11.8.2", "http2-proxy": "^5.0.53", "https-proxy-agent": "^5.0.0", "lolex": "^6.0.0", "many-keys-map": "^1.0.3", "nyc": "^15.1.0", "p-event": "^4.2.0", "tempy": "^1.0.1", "to-readable-stream": "^2.1.0", "tsd": "^0.17.0", "websocket-stream": "^5.5.2", "ws": "^7.5.3", "xo": "0.39.1"}, "ava": {"timeout": "10s"}, "nyc": {"include": ["source"]}, "xo": {"rules": {"unicorn/no-for-loop": "off", "unicorn/prefer-module": "off", "comma-dangle": "off", "@typescript-eslint/comma-dangle": "off", "quotes": ["error", "single", {"avoidEscape": true, "allowTemplateLiterals": true}], "operator-linebreak": ["error", "before"]}}, "gitHead": "477f8ebf3092abd460b4f7ffc54f62922c91a4ef", "_id": "http2-wrapper@2.1.6", "_nodeVersion": "10.23.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-XWJN8O++v1HTnk7yRsiS0X/XU6NCIj1mjDjKy+ZyVDoRk233XlLmVTuUMgfwYGaumccmyjBdpCyqkZOVUtp3ww==", "shasum": "96ed27548ee01dcafecbe901e6a4f03e93c21c2e", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-2.1.6.tgz", "fileCount": 26, "unpackedSize": 74358, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhKRChCRA9TVsSAnZWagAADPYP/Rki1Qsk5g15RKukEr5C\n+Ot9x6xfoalOjnb2zU0V0KIFTkEyV2GHba4Zy7gsOIy5qh2S00GP/W1lnY46\nPjHVlLeeYLWKMVCfOL517f9KsPVt5p90USd2YHIwQXBsZ1i9Q7D9/Xkr8ov6\nOplS7J1wolhuPBGwS/tt834PF0pcR7opN93z2jJZ6WUxtXtg95q/9EMuLSAg\nCbzlNSHJuiTWDb7MqkmuOVkCCqGH2f2ei0hG3y0jOOaJeCScGeOuwm7J4ZTs\ngP0zbSR10rU2VAQ941OadtSDVS0iNrdEBfvxW6/BiDhYa3JROiqlBcFVsZID\n5UgIS694LVdGz/lX3FLqa0yshqIL7/JsSONs/AO3nrl7/+RCGlSb1JUIyUli\nOPB3jWLTWrkzWosMVpJt8jC6C+0UTsmbcNCKqPzf/L2R7YdpgZusamwCyxcZ\nQmpwcMWv6/ekCfh1zypGQ+l5sNRoItAQub/UJXuLuqA6xKyAlbE6U16FhK8C\nx6YX+g6hKy80+0g6BeVh4KPnlcC1HkMzFJJMRsRDt6//t4xIRh7Qzz4peDqi\nb1muv6JixolY+YVYnmEmGDj4V9iHna+DcInzLA9M+2wX56BHgVrQFgY3fBM6\nH0mNSXVZGcqHQ7dG1kQTrKhiY3QA5hkJLCOOFq2YOmiDQ/FWT0Q2bnsb0Dj0\nvg+h\r\n=pKaX\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCevRND3CEWDL68PmYk5iB8qkx2Xt/ckaLahgkEQA+OhwIgN9+GC6DKk96FO18lKi8ko7dwVhiJhBGnnlqK7l+8u5s="}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_2.1.6_1630081185143_0.4461992449033001"}, "_hasShrinkwrap": false}, "2.1.7": {"name": "http2-wrapper", "version": "2.1.7", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "types": "index.d.ts", "engines": {"node": ">=10.19.0"}, "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text --reporter=html ava && tsd"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.2.0"}, "devDependencies": {"@sindresorhus/is": "^4.0.1", "ava": "^3.15.0", "benchmark": "^2.1.4", "get-stream": "^6.0.1", "got": "^11.8.2", "http2-proxy": "^5.0.53", "https-proxy-agent": "^5.0.0", "lolex": "^6.0.0", "many-keys-map": "^1.0.3", "nyc": "^15.1.0", "p-event": "^4.2.0", "tempy": "^1.0.1", "to-readable-stream": "^2.1.0", "tsd": "^0.17.0", "websocket-stream": "^5.5.2", "ws": "^7.5.3", "xo": "0.39.1"}, "ava": {"timeout": "10s"}, "nyc": {"include": ["source"]}, "xo": {"rules": {"unicorn/no-for-loop": "off", "unicorn/prefer-module": "off", "comma-dangle": "off", "@typescript-eslint/comma-dangle": "off", "quotes": ["error", "single", {"avoidEscape": true, "allowTemplateLiterals": true}], "operator-linebreak": ["error", "before"]}}, "gitHead": "477f8ebf3092abd460b4f7ffc54f62922c91a4ef", "_id": "http2-wrapper@2.1.7", "_nodeVersion": "16.8.0", "_npmVersion": "7.21.0", "dist": {"integrity": "sha512-tTITv4rKsjRb39Ulkc5p2GGykW2LCCj8QqkWRvKi7NDXpYC+jymBorGz9xmQbL9u/zFcn3PY2jFA9Hy9JOtvWA==", "shasum": "6100c7bfb3bcb5e6542aa49e03976e5048a2966f", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-2.1.7.tgz", "fileCount": 26, "unpackedSize": 74372, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhKSsuCRA9TVsSAnZWagAAix4P/1LgaPKRGUwO4low7kvv\nEoAoAx31Cldu7/NnuIPel6SWyHPnjjcXn8m+TbY9daDysAM4hwP2wutGc2tp\npVd1n1mk3qblz5bMR3KWTsSf9RiCRBnhlLL6iL4oJkfQBhHx1eXy8KzJEZJ4\n8aiFnLQ4jtySThtXOO1G9ogFknt051kf4G7VHZGF6nF3OypB+ymTp0ri60Gn\nQWhd3AXG3FLXihYO3TZK8oIpHCN3OGz8TVRLI4DXPdGpKKIRiKyLCYx5RAKe\ndLOaGS0yQjl/hW0bHyWaDC8cNpliDFc8pyX5c5Hf6nCZrmIlLx8ZJNODHL/Z\ngiOJpGCEAHVhVhzibm0/4zJFE9Jk6JGLzmK5jI34BWTYCZbic6DNgr1UplQy\nEB0OMNn8YFZCaTvn6cvhFgIA5PUCtmRDnh+KfaOaUnlv5lHT+LCE2q1MBzGZ\n7SRjlYWXjRmXW8xZgcLFvau4nMkM2cfiFvidQ6RsfeJ9gEnN1WCHATlhIpGK\neU4AwNfyfmRGfWUShn85bGpfPXStpaNGpP1efV5xBTfzvphsG63R9gTCDq49\n0SttJ4SfhdtX13qR2hN6LkOs9aI3PR/arHhtW3PYErCDy2W72YxSTLrtdyr4\nueVzuSCHVwfw22biqQ0qvmJmc/M13MFIiwzBAFNTKGRLsX21YCkV4m8VV3U8\nVguN\r\n=yR8B\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGZda/nfdrfMB07Nk48Ix9GTVruUXwLt59/QyCtCjjsKAiEAjea03gpb92G4Fdcg7XHFnZyqmqFpnELryQ7OWwLzmjA="}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_2.1.7_1630087982264_0.2473400839091029"}, "_hasShrinkwrap": false}, "2.1.8": {"name": "http2-wrapper", "version": "2.1.8", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "types": "index.d.ts", "engines": {"node": ">=10.19.0"}, "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text --reporter=html ava && tsd"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.2.0"}, "devDependencies": {"@sindresorhus/is": "^4.0.1", "ava": "^3.15.0", "benchmark": "^2.1.4", "get-stream": "^6.0.1", "got": "^11.8.2", "http2-proxy": "^5.0.53", "https-proxy-agent": "^5.0.0", "lolex": "^6.0.0", "many-keys-map": "^1.0.3", "nyc": "^15.1.0", "p-event": "^4.2.0", "tempy": "^1.0.1", "to-readable-stream": "^2.1.0", "tsd": "^0.17.0", "websocket-stream": "^5.5.2", "ws": "^7.5.3", "xo": "0.39.1"}, "ava": {"timeout": "10s"}, "nyc": {"include": ["source"]}, "xo": {"rules": {"unicorn/no-for-loop": "off", "unicorn/prefer-module": "off", "comma-dangle": "off", "@typescript-eslint/comma-dangle": "off", "quotes": ["error", "single", {"avoidEscape": true, "allowTemplateLiterals": true}], "operator-linebreak": ["error", "before"]}}, "gitHead": "e1c487b5048305ed8272c36864c45225ecb5675d", "_id": "http2-wrapper@2.1.8", "_nodeVersion": "16.8.0", "_npmVersion": "7.21.0", "dist": {"integrity": "sha512-LuhKyylBW4hOnuIi3t0lMq8Tqr1KRFc+kdIu43zeXl6GFHTA0dTeeFCS0JIxNyh9KvFiT0jdWpyOtnJfQzJsLA==", "shasum": "0dd1ed09eccfcfcb2eb9541cae4efe9b721bbc61", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-2.1.8.tgz", "fileCount": 26, "unpackedSize": 74892, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhKWHdCRA9TVsSAnZWagAAs8IP/2knIdJNYIgmviQbkI9P\nTBMeadvV5/ffSlDkbH44WKAuVKeam9p21QKuXOFWc3XXxx9wEXe9qRHT23Pu\n8Mj1M3cBYfhdaA6GKX8kFfYdQW3QohkExqmBsNdECuXdZOHofLmGd2OmIf+/\n+Wdu8ndci6hJECEMHqaG63uOA81h74vwS5MBivaqUUJEwIkHYTJi9TEJGrya\njRTRpnU9MfeHSz5sJKkPaDlLEgItf47DbbO2f/mCOF9bQgD1gJa52UzjM81k\nDho42eRze7j6+ZPwG8X7FzPdFYiLwxxWvN9YjZ6PTA2hllEOE4lEEdvWRNLh\nGnkP34CMmdrxkugap2YBx92pMPPLaG5hEzvqmlnQswEe+0xZYGP3zywL4FYW\naPjoLG1R8GdDOpQFwpiCQXU+uhSdzvyWg9gPOC6h2g3UJ+O43zS/eRq1MTJ8\nbiV38oOJtJq5aSi1BlEnGI1KJ8TcIzgVT+b+fz//SRe9su+8R8Ste0gK7rOM\nwFw6F+Imw/4qQAP8LnCqs/v6e/19usqzziSGp71NgVn6Tj4U5HC6d5IC37QQ\nkX+tludF9QYJI2lup8/9dKJqJgYBUzbO51TNC/H27JNsBQS3uNxwq6qE/ux/\ncvxanxYtKD9l9UhdXbnmcyNqrHyQ6jYQkF8OqsQvCXkj8CsZBTeTyeC82/vp\nGHzg\r\n=jDrA\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDboezs9gwPeK6dtO+Yq9ZC/HMJlOP9tRpMbj3QlxTXSAIgCUTcnbt/QTAmqGrP4nhtXQ2rq8h+09oYa6iudr6C+ME="}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_2.1.8_1630101980898_0.6602151559440557"}, "_hasShrinkwrap": false}, "2.1.9": {"name": "http2-wrapper", "version": "2.1.9", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "types": "index.d.ts", "engines": {"node": ">=10.19.0"}, "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text --reporter=html ava && tsd"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.2.0"}, "devDependencies": {"@sindresorhus/is": "^4.0.1", "ava": "^3.15.0", "benchmark": "^2.1.4", "get-stream": "^6.0.1", "got": "^11.8.2", "http2-proxy": "^5.0.53", "https-proxy-agent": "^5.0.0", "lolex": "^6.0.0", "many-keys-map": "^1.0.3", "nyc": "^15.1.0", "p-event": "^4.2.0", "tempy": "^1.0.1", "to-readable-stream": "^2.1.0", "tsd": "^0.17.0", "websocket-stream": "^5.5.2", "ws": "^7.5.3", "xo": "0.39.1"}, "ava": {"timeout": "10s"}, "nyc": {"include": ["source"]}, "xo": {"rules": {"unicorn/no-for-loop": "off", "unicorn/prefer-module": "off", "comma-dangle": "off", "@typescript-eslint/comma-dangle": "off", "quotes": ["error", "single", {"avoidEscape": true, "allowTemplateLiterals": true}], "operator-linebreak": ["error", "before"]}}, "gitHead": "b9a1f46a38ca4fede79c016fd897d338ebc25d05", "_id": "http2-wrapper@2.1.9", "_nodeVersion": "16.10.0", "_npmVersion": "7.24.0", "dist": {"integrity": "sha512-z5jPLkDXHsQM762XFo4XproHTXT0lMQscKCQMPGccHDzY0kNxmUxWyGkW66zB2RGAr9pF9Tzc5Dmmv8Uh8HW3Q==", "shasum": "d27b9475f132973a74b4ee50901b951ff6e56020", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-2.1.9.tgz", "fileCount": 26, "unpackedSize": 74921, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG/JNz6ILw1ZGAQoSj7pGkrhjSX7HaZ2ltl4W5GeFux1AiBKwK9f6JbGKXgoJp5xDkLD2g84m+gEDSI08nssopmXEQ=="}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_2.1.9_1633704875940_0.726725060655117"}, "_hasShrinkwrap": false}, "2.1.10": {"name": "http2-wrapper", "version": "2.1.10", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "types": "index.d.ts", "engines": {"node": ">=10.19.0"}, "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text --reporter=html ava && tsd"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.2.0"}, "devDependencies": {"@sindresorhus/is": "^4.0.1", "ava": "^3.15.0", "benchmark": "^2.1.4", "get-stream": "^6.0.1", "got": "^11.8.2", "http2-proxy": "^5.0.53", "https-proxy-agent": "^5.0.0", "lolex": "^6.0.0", "many-keys-map": "^1.0.3", "nyc": "^15.1.0", "p-event": "^4.2.0", "tempy": "^1.0.1", "to-readable-stream": "^2.1.0", "tsd": "^0.17.0", "websocket-stream": "^5.5.2", "ws": "^7.5.3", "xo": "0.39.1"}, "ava": {"timeout": "10s"}, "nyc": {"include": ["source"]}, "xo": {"rules": {"unicorn/no-for-loop": "off", "unicorn/prefer-module": "off", "comma-dangle": "off", "@typescript-eslint/comma-dangle": "off", "quotes": ["error", "single", {"avoidEscape": true, "allowTemplateLiterals": true}], "operator-linebreak": ["error", "before"]}}, "gitHead": "fda4d4ecf007ab2bea752e33c680bdbb80f634ec", "_id": "http2-wrapper@2.1.10", "_nodeVersion": "16.13.0", "_npmVersion": "8.3.0", "dist": {"integrity": "sha512-QHgsdYkieKp+6JbXP25P+tepqiHYd+FVnDwXpxi/BlUcoIB0nsmTOymTNvETuTO+pDuwcSklPE72VR3DqV+Haw==", "shasum": "307cd0cee2564723692ad34c2d570d12f10e83be", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-2.1.10.tgz", "fileCount": 26, "unpackedSize": 75102, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhwFVECRA9TVsSAnZWagAAdD8P/ikkKii7zadknY5tVNA8\nQXbqI/gHhAyf+lEy87UPOYvKYhI9CVcrbKwljYLbMNe7RSUxFiIz6kfg3qla\nvtkcvDEbPT6feJ/iu7aAIsZoC2PXMMLXsEIH2z8E+wrbO5qiG2uQKB61A1dO\nHiC/+xtkh0A7MR3F4sCgpnTVYK6n6UIQUyWdRXgOUB//EblwxWqPymAkFzzA\n0HTxnk1d4PBLOJW3BHyO/VLnKae2xZnO6eYJ5QwOgWPXFismtxcrdwCPIbYV\norhy3PBlswQsfDxb/jpQoadTxu/R1DU8DmPj85W9S2dZ5jngCuGZHcOrxDIf\nJii4hWz9zcqJGKUEw5btvfn34YcY3jaFJfPqe2ST0fV3UJxolucuvysWzy2h\nG+qxk/qrgjKkEXFT3sLB5Rrq9owLkVS4XRilAk8PmSbA8APL9L0kek+Pv+84\nXZ+7a+VPxNGq/A+AWLYTZOV5AYFknP9F/6eGrGYGVgT4BeKHa1VjfHzlWuHl\n4jV6XaYL5TecelxZfsDGD7EBefiMKSphHxWIISQMUI44xrhDcvVPZMwQXoQN\nKiMeUT6JBn5JRMUOhScz3L2DUkv0SdxaGTVrKM93X7jiQkHd1vVSUyFKIXA+\n6NAV513KjAPzfG+MdVw3W2pKJLy+yKcREzmnFOK+wlYdH4I0OI1D3l/2a0BZ\ni0tG\r\n=ebhm\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC4auFcMpmnAajZCuvsNSt+ndUe5zupn/Mq4nDuzymFEAIhAOPbx51lInDccfyksMK9MQKmkrcYCADlFAa5+Q1VpxUy"}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_2.1.10_1639994692665_0.13649870102447648"}, "_hasShrinkwrap": false}, "2.1.11": {"name": "http2-wrapper", "version": "2.1.11", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "types": "index.d.ts", "engines": {"node": ">=10.19.0"}, "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text --reporter=html ava && tsd"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.2.0"}, "devDependencies": {"@sindresorhus/is": "^4.0.1", "ava": "^3.15.0", "benchmark": "^2.1.4", "get-stream": "^6.0.1", "got": "^11.8.2", "http2-proxy": "^5.0.53", "https-proxy-agent": "^5.0.0", "lolex": "^6.0.0", "many-keys-map": "^1.0.3", "nyc": "^15.1.0", "p-event": "^4.2.0", "tempy": "^1.0.1", "to-readable-stream": "^2.1.0", "tsd": "^0.17.0", "websocket-stream": "^5.5.2", "ws": "^7.5.3", "xo": "0.39.1"}, "ava": {"timeout": "10s"}, "nyc": {"include": ["source"]}, "xo": {"rules": {"unicorn/no-for-loop": "off", "unicorn/prefer-module": "off", "comma-dangle": "off", "@typescript-eslint/comma-dangle": "off", "quotes": ["error", "single", {"avoidEscape": true, "allowTemplateLiterals": true}], "operator-linebreak": ["error", "before"]}}, "gitHead": "c6b70d3bef12876534e1f75f873965741af3c2ee", "_id": "http2-wrapper@2.1.11", "_nodeVersion": "17.9.0", "_npmVersion": "8.5.5", "dist": {"integrity": "sha512-aNAk5JzLturWEUiuhAN73Jcbq96R7rTitAoXV54FYMatvihnpD2+6PUgU4ce3D/m5VDbw+F5CsyKSF176ptitQ==", "shasum": "d7c980c7ffb85be3859b6a96c800b2951ae257ef", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-2.1.11.tgz", "fileCount": 26, "unpackedSize": 75109, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCHuphx5FPTgWHv8KJvs+fh8yJeV/w4qlmLJD8GRXZ9kAIgbvAJfeJ/kao45o6EK042M/HjEQoKPkp+pkiqfsL6JtQ="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZYl4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoqwg//R2GV3zYqR+iA7btedWllVqG5FJWFf8xu5g1EnQurXuAXEADN\r\nMSBlDiA7sUsjoIgUvKmonWDGv4DSXci+p356XJRmBKCqzWbFs2/NgTrT06XT\r\nMW4+8a0cqs0TbP5tDn5GGvkriVDjKHKc5hyAOS6UpTekPIVIkOZkUge46MVh\r\nGNpeoI00gb6xEBGHxrOVrAXTOdDZG/9BleKD5R30LeAGEmodrDW29svDQWb2\r\nbuVwoUJDxFb4p9ccU/nqYx1yzm67BJXcrjNF+vZQGbNwUvXq2nVzXgFmQ4Xy\r\nznaCKV+zL6TEIktBlEbQQ2ppCIrBW1tX0AyFI1IQYrQTFjLCxA46rMhAzNQg\r\nK9fG674NHFFw+WJ1Ul9oY8C6Zd1cuQEGUQy2bFspwa8TZN46wH1LTqQLSfuE\r\n+kEOZ27TlQUO5xS0ZK47eXcslV2KxCRd4xtd/zavu8g8JM8kOoXkZ7q6/z2q\r\n3uEc6vFnvgVr5QsRdqlvuGngBHFG8+xmor4OwyYcKS+DSyRCvzUD4YpitYPo\r\n27hhczrRQoRRB5Re+obwMEbvVIZRYBgbs3eqn1QSjblZvSJ51dhapVwttHrb\r\niXVabHgdJxG0YWIv32TC6htg8cbpnnfyxNzh12ypZSAvKkgtEJcd10LpdUxe\r\nHyJvx0ESMoLSHmsAc4WW5Ss7BY3+9KVYgjs=\r\n=1XIy\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_2.1.11_1650821496455_0.8330997623760206"}, "_hasShrinkwrap": false}, "2.2.0": {"name": "http2-wrapper", "version": "2.2.0", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "types": "index.d.ts", "engines": {"node": ">=10.19.0"}, "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text --reporter=html ava && tsd"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.2.0"}, "devDependencies": {"@sindresorhus/is": "^4.0.1", "ava": "^3.15.0", "benchmark": "^2.1.4", "get-stream": "^6.0.1", "got": "^11.8.2", "http2-proxy": "^5.0.53", "https-proxy-agent": "^5.0.0", "lolex": "^6.0.0", "many-keys-map": "^1.0.3", "nyc": "^15.1.0", "p-event": "^4.2.0", "tempy": "^1.0.1", "to-readable-stream": "^2.1.0", "tsd": "^0.17.0", "websocket-stream": "^5.5.2", "ws": "^7.5.3", "xo": "0.39.1"}, "ava": {"timeout": "10s"}, "nyc": {"include": ["source"]}, "xo": {"rules": {"unicorn/no-for-loop": "off", "unicorn/prefer-module": "off", "comma-dangle": "off", "@typescript-eslint/comma-dangle": "off", "quotes": ["error", "single", {"avoidEscape": true, "allowTemplateLiterals": true}], "operator-linebreak": ["error", "before"]}}, "gitHead": "b55f9f5668e2e57b63daa54bfff4004d75fa63b8", "_id": "http2-wrapper@2.2.0", "_nodeVersion": "18.9.0", "_npmVersion": "8.19.1", "dist": {"integrity": "sha512-kZB0wxMo0sh1PehyjJUWRFEd99KC5TLjZ2cULC4f9iqJBAmKQQXEICjxl5iPJRwP40dpeHFqqhm7tYCvODpqpQ==", "shasum": "b80ad199d216b7d3680195077bd7b9060fa9d7f3", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-2.2.0.tgz", "fileCount": 26, "unpackedSize": 75301, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBP8NeqxV/QeoFfEbE5Qu4tBf25BcgVQoLBFZRuxW1HXAiAF6F/w5G58uQbq9MeDr9+Nyf06cYpfsEKvUUgXRRoGDg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjc52vACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrO+Q//YBnzipgXrpjVRImQZZ701CGVYumP8jHZZsOY+GqTihW4DEJz\r\n27GbfIxbZKI4oerBlj+1k8Jt7h6M1t2QTl1sAFRAo9WGscQQFHLBLn3nFmFY\r\njL2KSmMeizfoUgRoNXFf475p3+Zaf8g/UTXUsCJjoLlLfPe78B4GTLL+TVS6\r\n58V296UP0uBz6lOjxmSm4bNQoOj9mApG4O6gJcff/Ht/8eOTTFCXDsFJ956p\r\nnEtVDoMDmep0MwLdsKNzoYcGZ1MKtRQvG6pV2kCQowOTzKYUy9kbo+Mq6Ddp\r\n0mnZnVI0kVtDNS+TI9hEl7PdgL7SZqaMBIzcXhcemZVmzZ1yAT47o2EGPGfN\r\nmYLBBIDRADeslRW8pUcsuBRYZ3kXwpxqmShWOZTmbgzN5M/IWAC1GdIu3u11\r\nCaiGYhiA9YVH/b+jUbR8kCTzNhdB+8aHKUNrLirXY6qGUZLbmOnu0fSOfegd\r\nEEJVy4YRwIWLI3i7LwzUyVpbpZtGAmgHjEFItq7lgk2ZLeRYxhe6uHPQUnSm\r\n6fEey6rVed6k0ihwOxuVCGdRBbegnv/ggKR/51msa0m6iTI9trGguVXpKzxI\r\ncPS1Xc3uzpOUzG92979i7ob9+2kzkvTjGM71T2dw+FEIxklaM0g9haZvstME\r\nBahZA+UQcp9cRzC5+ZE8Fl+G3dP2aNcLfzo=\r\n=Sc+p\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_2.2.0_1668521391540_0.8476501932302429"}, "_hasShrinkwrap": false}, "2.2.1": {"name": "http2-wrapper", "version": "2.2.1", "description": "HTTP2 client, just with the familiar `https` API", "main": "source", "types": "index.d.ts", "engines": {"node": ">=10.19.0"}, "scripts": {"test": "xo && nyc --reporter=lcovonly --reporter=text --reporter=html ava && tsd"}, "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "dependencies": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.2.0"}, "devDependencies": {"@sindresorhus/is": "^4.0.1", "ava": "^3.15.0", "benchmark": "^2.1.4", "get-stream": "^6.0.1", "got": "^11.8.2", "http2-proxy": "^5.0.53", "https-proxy-agent": "^5.0.0", "lolex": "^6.0.0", "many-keys-map": "^1.0.3", "nyc": "^15.1.0", "p-event": "^4.2.0", "tempy": "^1.0.1", "to-readable-stream": "^2.1.0", "tsd": "^0.17.0", "websocket-stream": "^5.5.2", "ws": "^7.5.3", "xo": "0.39.1"}, "ava": {"timeout": "10s"}, "nyc": {"include": ["source"]}, "xo": {"rules": {"unicorn/no-for-loop": "off", "unicorn/prefer-module": "off", "comma-dangle": "off", "@typescript-eslint/comma-dangle": "off", "quotes": ["error", "single", {"avoidEscape": true, "allowTemplateLiterals": true}], "operator-linebreak": ["error", "before"]}}, "_id": "http2-wrapper@2.2.1", "gitHead": "03bbcd2ddab06807e2f7bbd3d4de8a6831084d8f", "_nodeVersion": "21.1.0", "_npmVersion": "10.2.0", "dist": {"integrity": "sha512-V5nVw1PAOgfI3Lmeaj2Exmeg7fenjhRUgz1lPSezy1CuhPYbgQtbQj4jZfEAEMlaL+vupsvhjqCyjzob0yxsmQ==", "shasum": "310968153dcdedb160d8b72114363ef5fce1f64a", "tarball": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-2.2.1.tgz", "fileCount": 26, "unpackedSize": 75884, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCSWn6UHmB1JuGxsbKnD39Bg77n9iMbig2LDRXlsCd8sAIhAK0FEMOL5fXHEMvOIPL0a1kUvcrbV6sxCIDBiLWKXCOH"}]}, "_npmUser": {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http2-wrapper_2.2.1_1699632665705_0.8521568694677721"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-08-11T16:04:10.224Z", "0.1.0": "2018-08-11T16:04:10.311Z", "modified": "2023-11-10T16:11:06.097Z", "0.1.1": "2018-08-11T18:49:38.353Z", "0.2.0": "2018-08-12T17:15:42.329Z", "0.3.0": "2018-08-22T17:48:34.224Z", "0.3.1": "2018-08-22T18:34:34.712Z", "0.3.2": "2018-08-23T15:16:32.458Z", "0.4.0": "2018-12-23T16:26:56.236Z", "0.4.1": "2018-12-24T10:45:13.827Z", "0.4.2": "2018-12-26T17:21:11.225Z", "0.5.0": "2019-07-13T09:37:09.464Z", "0.5.1": "2019-07-13T19:11:06.600Z", "0.5.2": "2019-07-14T06:50:30.976Z", "0.6.0": "2019-07-14T08:07:27.816Z", "0.7.0": "2019-07-14T08:40:53.225Z", "0.8.0": "2019-07-14T09:26:23.143Z", "1.0.0-alpha.0": "2019-07-18T10:36:38.630Z", "1.0.0-beta.0": "2019-11-03T17:28:14.534Z", "1.0.0-beta.1": "2019-11-03T18:49:57.216Z", "1.0.0-beta.2": "2019-11-30T21:28:00.589Z", "1.0.0-beta.3": "2019-12-01T20:33:53.767Z", "1.0.0-beta.4": "2020-02-17T18:37:57.958Z", "1.0.0-beta.4.1": "2020-02-28T17:01:45.457Z", "1.0.0-beta.4.2": "2020-02-28T17:02:09.893Z", "1.0.0-beta.4.3": "2020-03-14T13:05:24.796Z", "1.0.0-beta.4.4": "2020-04-21T18:32:39.550Z", "1.0.0-beta.4.5": "2020-04-28T13:26:54.031Z", "1.0.0-beta.4.6": "2020-05-10T19:56:31.617Z", "1.0.0-beta.4.7": "2020-07-06T02:19:23.108Z", "1.0.0-beta.4.8": "2020-07-06T18:26:30.649Z", "1.0.0-beta.5.0": "2020-07-09T23:02:11.575Z", "1.0.0-beta.5.1": "2020-07-10T21:22:30.063Z", "1.0.0-beta.5.2": "2020-07-11T02:28:47.084Z", "1.0.0": "2021-02-25T15:15:43.856Z", "1.0.1": "2021-02-25T16:04:22.243Z", "1.0.3": "2021-02-25T23:37:57.603Z", "2.0.0": "2021-02-25T23:38:48.197Z", "2.0.1": "2021-04-05T06:34:21.613Z", "2.0.2": "2021-04-11T19:13:03.554Z", "2.0.3": "2021-04-11T20:01:30.696Z", "2.0.4": "2021-04-27T03:13:09.489Z", "2.0.5": "2021-04-27T10:55:43.207Z", "2.0.6": "2021-07-12T18:04:52.160Z", "2.0.7": "2021-07-12T21:05:48.977Z", "2.0.8": "2021-07-17T23:09:15.047Z", "2.0.9": "2021-07-21T13:55:52.546Z", "2.1.0": "2021-07-21T16:01:54.539Z", "2.1.1": "2021-07-21T17:01:07.074Z", "2.1.2": "2021-08-06T10:01:00.728Z", "2.1.3": "2021-08-16T08:40:37.229Z", "2.1.4": "2021-08-20T21:37:11.649Z", "2.1.5": "2021-08-27T10:57:13.752Z", "2.1.6": "2021-08-27T16:19:45.282Z", "2.1.7": "2021-08-27T18:13:02.394Z", "2.1.8": "2021-08-27T22:06:21.063Z", "2.1.9": "2021-10-08T14:54:36.129Z", "2.1.10": "2021-12-20T10:04:52.829Z", "2.1.11": "2022-04-24T17:31:36.624Z", "2.2.0": "2022-11-15T14:09:51.735Z", "2.2.1": "2023-11-10T16:11:05.951Z"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "szmar<PERSON>ak", "email": "sz.marc<PERSON>@gmail.com"}], "description": "HTTP2 client, just with the familiar `https` API", "homepage": "https://github.com/szmarczak/http2-wrapper#readme", "keywords": ["http2", "https", "http", "request"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http2-wrapper.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "bugs": {"url": "https://github.com/szmarczak/http2-wrapper/issues"}, "license": "MIT", "readme": "# http2-wrapper\n> HTTP/2 client, just with the familiar `https` API\n\n[![Node CI](https://github.com/szmarczak/http2-wrapper/workflows/Node%20CI/badge.svg)](https://github.com/szmarczak/http2-wrapper/actions)\n[![codecov](https://codecov.io/gh/szmarczak/http2-wrapper/branch/master/graph/badge.svg)](https://codecov.io/gh/szmarczak/http2-wrapper)\n[![npm](https://img.shields.io/npm/dm/http2-wrapper.svg)](https://www.npmjs.com/package/http2-wrapper)\n[![install size](https://packagephobia.now.sh/badge?p=http2-wrapper)](https://packagephobia.now.sh/result?p=http2-wrapper)\n\nThis package was created to support HTTP/2 without the need to rewrite your code.<br>\nI recommend adapting to the [`http2`](https://nodejs.org/api/http2.html) module if possible - it's much simpler to use and has many cool features!\n\n**Tip**: `http2-wrapper` is very useful when you rely on other modules that use the HTTP/1 API and you want to support HTTP/2.\n\n**Pro Tip**: While the native `http2` doesn't have agents yet, you can use `http2-wrapper` Agents and still operate on the native HTTP/2 streams.\n\n## Installation\n\n> `$ npm install http2-wrapper`<br>\n> `$ yarn add http2-wrapper`\n\n## Usage\n\n```js\nconst http2 = require('http2-wrapper');\n\nconst options = {\n\thostname: 'nghttp2.org',\n\tprotocol: 'https:',\n\tpath: '/httpbin/post',\n\tmethod: 'POST',\n\theaders: {\n\t\t'content-length': 6\n\t}\n};\n\nconst request = http2.request(options, response => {\n\tconsole.log('statusCode:', response.statusCode);\n\tconsole.log('headers:', response.headers);\n\n\tconst body = [];\n\tresponse.on('data', chunk => {\n\t\tbody.push(chunk);\n\t});\n\tresponse.on('end', () => {\n\t\tconsole.log('body:', Buffer.concat(body).toString());\n\t});\n});\n\nrequest.on('error', console.error);\n\nrequest.write('123');\nrequest.end('456');\n\n// statusCode: 200\n// headers: [Object: null prototype] {\n//   ':status': 200,\n//   date: 'Fri, 27 Sep 2019 19:45:46 GMT',\n//   'content-type': 'application/json',\n//   'access-control-allow-origin': '*',\n//   'access-control-allow-credentials': 'true',\n//   'content-length': '239',\n//   'x-backend-header-rtt': '0.002516',\n//   'strict-transport-security': 'max-age=31536000',\n//   server: 'nghttpx',\n//   via: '1.1 nghttpx',\n//   'alt-svc': 'h3-23=\":4433\"; ma=3600',\n//   'x-frame-options': 'SAMEORIGIN',\n//   'x-xss-protection': '1; mode=block',\n//   'x-content-type-options': 'nosniff'\n// }\n// body: {\n//   \"args\": {},\n//   \"data\": \"123456\",\n//   \"files\": {},\n//   \"form\": {},\n//   \"headers\": {\n//     \"Content-Length\": \"6\",\n//     \"Host\": \"nghttp2.org\"\n//   },\n//   \"json\": 123456,\n//   \"origin\": \"xxx.xxx.xxx.xxx\",\n//   \"url\": \"https://nghttp2.org/httpbin/post\"\n// }\n```\n\n## API\n\n**Note:** The `session` option was renamed to `tlsSession` for better readability.\n\n**Note:** The `timeout` option applies to HTTP/2 streams only. In order to set session timeout, pass an Agent with custom `timeout` option set.\n\n### http2.auto(url, options, callback)\n\nPerforms [ALPN](https://nodejs.org/api/tls.html#tls_alpn_and_sni) negotiation.\nReturns a Promise giving proper `ClientRequest` instance (depending on the ALPN).\n\n**Note**: The `agent` option represents an object with `http`, `https` and `http2` properties.\n\n```js\nconst http2 = require('http2-wrapper');\n\nconst options = {\n\thostname: 'httpbin.org',\n\tprotocol: 'http:', // Try changing this to https:\n\tpath: '/post',\n\tmethod: 'POST',\n\theaders: {\n\t\t'content-length': 6\n\t}\n};\n\n(async () => {\n\ttry {\n\t\tconst request = await http2.auto(options, response => {\n\t\t\tconsole.log('statusCode:', response.statusCode);\n\t\t\tconsole.log('headers:', response.headers);\n\n\t\t\tconst body = [];\n\t\t\tresponse.on('data', chunk => body.push(chunk));\n\t\t\tresponse.on('end', () => {\n\t\t\t\tconsole.log('body:', Buffer.concat(body).toString());\n\t\t\t});\n\t\t});\n\n\t\trequest.on('error', console.error);\n\n\t\trequest.write('123');\n\t\trequest.end('456');\n\t} catch (error) {\n\t\tconsole.error(error);\n\t}\n})();\n\n// statusCode: 200\n// headers: { connection: 'close',\n//   server: 'gunicorn/19.9.0',\n//   date: 'Sat, 15 Dec 2018 18:19:32 GMT',\n//   'content-type': 'application/json',\n//   'content-length': '259',\n//   'access-control-allow-origin': '*',\n//   'access-control-allow-credentials': 'true',\n//   via: '1.1 vegur' }\n// body: {\n//   \"args\": {},\n//   \"data\": \"123456\",\n//   \"files\": {},\n//   \"form\": {},\n//   \"headers\": {\n//     \"Connection\": \"close\",\n//     \"Content-Length\": \"6\",\n//     \"Host\": \"httpbin.org\"\n//   },\n//   \"json\": 123456,\n//   \"origin\": \"xxx.xxx.xxx.xxx\",\n//   \"url\": \"http://httpbin.org/post\"\n// }\n```\n\n### http2.auto.protocolCache\n\nAn instance of [`quick-lru`](https://github.com/sindresorhus/quick-lru) used for ALPN cache.\n\nThere is a maximum of 100 entries. You can modify the limit through `protocolCache.maxSize` - note that the change will be visible globally.\n\n### http2.auto.createResolveProtocol(cache, queue, connect)\n\n#### cache\n\nType: `Map<string, string>`\n\nThis is the store where cached ALPN protocols are put into.\n\n#### queue\n\nType: `Map<string, Promise>`\n\nThis is the store that contains pending ALPN negotiation promises.\n\n#### connect\n\nType: `(options, callback) => TLSSocket | Promise<TLSSocket>`\n\nSee https://github.com/szmarczak/resolve-alpn#connect\n\n### http2.auto.resolveProtocol(options)\n\nReturns a `Promise<{alpnProtocol: string}>`.\n\n### http2.request(url, options, callback)\n\nSame as [`https.request`](https://nodejs.org/api/https.html#https_https_request_options_callback).\n\n##### options.h2session\n\nType: `Http2Session`<br>\n\nThe session used to make the actual request. If none provided, it will use `options.agent` to get one.\n\n### http2.get(url, options, callback)\n\nSame as [`https.get`](https://nodejs.org/api/https.html#https_https_get_options_callback).\n\n### new http2.ClientRequest(url, options, callback)\n\nSame as [`https.ClientRequest`](https://nodejs.org/api/https.html#https_class_https_clientrequest).\n\n### new http2.IncomingMessage(socket)\n\nSame as [`https.IncomingMessage`](https://nodejs.org/api/https.html#https_class_https_incomingmessage).\n\n### new http2.Agent(options)\n\n**Note:** this is **not** compatible with the classic `http.Agent`.\n\nUsage example:\n\n```js\nconst http2 = require('http2-wrapper');\n\nclass MyAgent extends http2.Agent {\n\tcreateConnection(origin, options) {\n\t\tconsole.log(`Connecting to ${http2.Agent.normalizeOrigin(origin)}`);\n\t\treturn http2.Agent.connect(origin, options);\n\t}\n}\n\nhttp2.get({\n\thostname: 'google.com',\n\tagent: new MyAgent()\n}, response => {\n\tresponse.on('data', chunk => console.log(`Received chunk of ${chunk.length} bytes`));\n});\n```\n\n#### options\n\nEach option is an `Agent` property and can be changed later.\n\n##### timeout\n\nType: `number`<br>\nDefault: `0`\n\nIf there's no activity after `timeout` milliseconds, the session will be closed. If `0`, no timeout is applied.\n\n##### maxSessions\n\nType: `number`<br>\nDefault: `Infinity`\n\nThe maximum amount of sessions in total.\n\n##### maxEmptySessions\n\nType: `number`<br>\nDefault: `10`\n\nThe maximum amount of empty sessions in total. An empty session is a session with no pending requests.\n\n##### maxCachedTlsSessions\n\nType: `number`<br>\nDefault: `100`\n\nThe maximum amount of cached TLS sessions.\n\n#### agent.protocol\n\nType: `string`<br>\nDefault: `https:`\n\n#### agent.settings\n\nType: `object`<br>\nDefault: `{enablePush: false}`\n\n[Settings](https://nodejs.org/api/http2.html#http2_settings_object) used by the current agent instance.\n\n#### agent.normalizeOptions([options](https://github.com/szmarczak/http2-wrapper/blob/master/source/agent.js))\n\nReturns a string representing normalized options.\n\n```js\nAgent.normalizeOptions({servername: 'example.com'});\n// => ':::::::::::::::::::::::::::::::::::::'\n```\n\n#### agent.getSession(origin, options)\n\n##### [origin](https://nodejs.org/api/http2.html#http2_http2_connect_authority_options_listener)\n\nType: `string` `URL` `object`\n\nOrigin used to create new session.\n\n##### [options](https://nodejs.org/api/http2.html#http2_http2_connect_authority_options_listener)\n\nType: `object`\n\nOptions used to create new session.\n\nReturns a Promise giving free `Http2Session`. If no free sessions are found, a new one is created.\n\nA session is considered free when pending streams count is less than max concurrent streams settings.\n\n#### agent.getSession([origin](#origin), [options](options-1), listener)\n\n##### listener\n\nType: `object`\n\n```\n{\n\treject: error => void,\n\tresolve: session => void\n}\n```\n\nIf the `listener` argument is present, the Promise will resolve immediately. It will use the `resolve` function to pass the session.\n\n#### agent.request([origin](#origin), [options](#options-1), [headers](https://nodejs.org/api/http2.html#http2_headers_object), [streamOptions](https://nodejs.org/api/http2.html#http2_clienthttp2session_request_headers_options))\n\nReturns a Promise giving `Http2Stream`.\n\n#### agent.createConnection([origin](#origin), [options](#options-1))\n\nReturns a new `TLSSocket`. It defaults to `Agent.connect(origin, options)`.\n\n#### agent.closeEmptySessions(count)\n\n##### count\n\nType: `number`\nDefault: `Number.POSITIVE_INFINITY`\n\nMakes an attempt to close empty sessions. Only sessions with 0 concurrent streams will be closed.\n\n#### agent.destroy(reason)\n\nDestroys **all** sessions.\n\n#### agent.emptySessionCount\n\nType: `number`\n\nA number of empty sessions.\n\n#### agent.pendingSessionCount\n\nType: `number`\n\nA number of pending sessions.\n\n#### agent.sessionCount\n\nType: `number`\n\nA number of all sessions held by the Agent.\n\n#### Event: 'session'\n\n```js\nagent.on('session', session => {\n\t// A new session has been created by the Agent.\n});\n```\n\n## Proxy support\n\nCurrently `http2-wrapper` provides support for these proxies:\n\n- `HttpOverHttp2`\n- `HttpsOverHttp2`\n- `Http2OverHttp2`\n- `Http2OverHttp`\n- `Http2OverHttps`\n\nAny of the above can be accessed via `http2wrapper.proxies`. Check out the [`examples/proxies`](examples/proxies) directory to learn more.\n\n**Note:** If you use the `http2.auto` function, the real IP address will leak. `http2wrapper` is not aware of the context. It will create a connection to the end server using your real IP address to get the ALPN protocol. Then it will create another connection using proxy. To migitate this, you need to pass a custom `resolveProtocol` function as an option:\n\n```js\nconst resolveAlpnProxy = new URL('****************************************');\nconst connect = async (options, callback) => new Promise((resolve, reject) => {\n\tconst host = `${options.host}:${options.port}`;\n\n\t(async () => {\n\t\ttry {\n\t\t\tconst request = await http2.auto(resolveAlpnProxy, {\n\t\t\t\tmethod: 'CONNECT',\n\t\t\t\theaders: {\n\t\t\t\t\thost\n\t\t\t\t},\n\t\t\t\tpath: host,\n\n\t\t\t\t// For demo purposes only!\n\t\t\t\trejectUnauthorized: false,\n\t\t\t});\n\n\t\t\trequest.end();\n\n\t\t\trequest.once('error', reject);\n\n\t\t\trequest.once('connect', (response, socket, head) => {\n\t\t\t\tif (head.length > 0) {\n\t\t\t\t\treject(new Error(`Unexpected data before CONNECT tunnel: ${head.length} bytes`));\n\n\t\t\t\t\tsocket.destroy();\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tconst tlsSocket = tls.connect({\n\t\t\t\t\t...options,\n\t\t\t\t\tsocket\n\t\t\t\t}, callback);\n\n\t\t\t\tresolve(tlsSocket);\n\t\t\t});\n\t\t} catch (error) {\n\t\t\treject(error);\n\t\t}\n\t})();\n});\n\n// This is required to prevent leaking real IP address on ALPN negotiation\nconst resolveProtocol = http2.auto.createResolveProtocol(new Map(), new Map(), connect);\n\nconst request = await http2.auto('https://httpbin.org/anything', {\n\tagent: {…},\n\tresolveProtocol\n}, response => {\n\t// Read the response here\n});\n\nrequest.end();\n```\n\nSee [`unknown-over-unknown.js`](examples/proxies/unknown-over-unknown.js) to learn more.\n\n## Mirroring another server\n\nSee [`examples/proxies/mirror.js`](examples/proxies/mirror.js) for an example.\n\n## [WebSockets over HTTP/2](https://tools.ietf.org/html/rfc8441)\n\nSee [`examples/ws`](examples/ws) for an example.\n\n## Push streams\n\nSee [`examples/push-stream`](examples/push-stream) for an example.\n\n## Related\n\n- [`got`](https://github.com/sindresorhus/got) - Simplified HTTP requests\n- [`http2-proxy`](https://github.com/nxtedition/node-http2-proxy) - A simple http/2 & http/1.1 spec compliant proxy helper for Node.\n\n## License\n\nMIT\n", "readmeFilename": "README.md"}