{"_id": "deep-extend", "_rev": "61-ee676bd8c6f349ebde66bc0f8bf4619d", "name": "deep-extend", "description": "Recursive object extending", "dist-tags": {"latest": "0.6.0"}, "versions": {"0.2.0": {"name": "deep-extend", "description": "Recursive object extending.", "version": "0.2.0", "homepage": "https://github.com/unclechu/node-deep-extend", "repository": {"type": "git", "url": "git://github.com/unclechu/node-deep-extend.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index", "engines": {"node": ">=0.4"}, "_npmUser": {"name": "uncle<PERSON>", "email": "<EMAIL>"}, "_id": "deep-extend@0.2.0", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.12", "_nodeVersion": "v0.6.14", "_defaultsLoaded": true, "dist": {"shasum": "0335c263045d1b79a8599123414e6eedbc505ea9", "tarball": "https://registry.npmjs.org/deep-extend/-/deep-extend-0.2.0.tgz", "integrity": "sha512-OKCJ3J2LEjc5NUZMJbD6SFmMVyx5jej8vNM9AeyGh6QcexNLXb7oX4BW4TsmoMlgErRW6DozH71dKa64bptudw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFAILgKtanDEOSrEXEAQUhyVeqTnlfKJ1kEonxZwjXp+AiAx8H+jn5tbJFkf95r7KGl4lA/eOrn+gpxY0/LzKBM1jA=="}]}, "maintainers": [{"name": "uncle<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.2.1": {"name": "deep-extend", "description": "Recursive object extending.", "version": "0.2.1", "homepage": "https://github.com/unclechu/node-deep-extend", "repository": {"type": "git", "url": "git://github.com/unclechu/node-deep-extend.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index", "engines": {"node": ">=0.4"}, "_npmUser": {"name": "uncle<PERSON>", "email": "<EMAIL>"}, "_id": "deep-extend@0.2.1", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.12", "_nodeVersion": "v0.6.14", "_defaultsLoaded": true, "dist": {"shasum": "38e3c530b94a412b29f027a6387c7ac69cfe4405", "tarball": "https://registry.npmjs.org/deep-extend/-/deep-extend-0.2.1.tgz", "integrity": "sha512-pVZpwRGM5mOl0rjlzc+4OASK4phlOuD4JKAzaLNWDMQPmnpAatPdJZ+c5jJBQeK60kRqf7bvF5NntOILWUJyBw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGqlApQ2tZRq/UCZ+Q3QRp2Q/pUvqhISUZ0qUeqyqXnXAiEAhqfsFSLRqKDCnHATqtePySgIXRW/ysBsimEec3eIfYg="}]}, "maintainers": [{"name": "uncle<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.2.2": {"name": "deep-extend", "description": "Recursive object extending.", "version": "0.2.2", "homepage": "https://github.com/unclechu/node-deep-extend", "repository": {"type": "git", "url": "git://github.com/unclechu/node-deep-extend.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index", "engines": {"node": ">=0.4"}, "_npmUser": {"name": "uncle<PERSON>", "email": "<EMAIL>"}, "_id": "deep-extend@0.2.2", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.12", "_nodeVersion": "v0.6.14", "_defaultsLoaded": true, "dist": {"shasum": "92a65646fa84b91a43623b278ee6933965bf04a2", "tarball": "https://registry.npmjs.org/deep-extend/-/deep-extend-0.2.2.tgz", "integrity": "sha512-HLtVhBd8Fqd5RhFcLRJGiTrPlMRPZgmcs62wQ30KVsZ2KWdz5qT2VOftxK9fJ+HoM1TYnecqLzuyKs3GtlhaYw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCtgBqO6yG+m6YKhIclr2ETJZabHcOPtJ4oJp+UJsjUugIgfdYYfZFMnp1UHZjBujA6AgweBSaF77k/VfptgVfStcY="}]}, "maintainers": [{"name": "uncle<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.2.4": {"name": "deep-extend", "description": "Recursive object extending.", "version": "0.2.4", "homepage": "https://github.com/unclechu/node-deep-extend", "repository": {"type": "git", "url": "git://github.com/unclechu/node-deep-extend.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index", "engines": {"node": ">=0.4"}, "_id": "deep-extend@0.2.4", "dist": {"shasum": "d0c5d0e3486e264db71ddf4fa8f0807a189decdd", "tarball": "https://registry.npmjs.org/deep-extend/-/deep-extend-0.2.4.tgz", "integrity": "sha512-k92xpjGWoIDAjaIOJiTpa8YLuOqdG3LheAta4/yYgR9hBoEtHUlH5l8cqItGRhoPP5gdXJhdHtTQUEQrqxtH0A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCOztenL/lEHAuifF0Ap+RTnqu2yu8vsckNA5wXjmjoTgIhAMemUSLK6ZPciicZy6KvyYVqj9pjuNKD6emcsy2WB40U"}]}, "_npmVersion": "1.1.65", "_npmUser": {"name": "uncle<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "uncle<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.2.5": {"name": "deep-extend", "description": "Recursive object extending.", "version": "0.2.5", "homepage": "https://github.com/unclechu/node-deep-extend", "repository": {"type": "git", "url": "git://github.com/unclechu/node-deep-extend.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index", "engines": {"node": ">=0.4"}, "_id": "deep-extend@0.2.5", "dist": {"shasum": "04471b170de4afdb150f2e8b530b2974dbfee90d", "tarball": "https://registry.npmjs.org/deep-extend/-/deep-extend-0.2.5.tgz", "integrity": "sha512-eDfCIB4+S60rLYFqFArSQ8sjuoo4qKbcRPuJtu1Ti7DtYXOmiPQhcQwy6L7K37ctI09tDko9Crd40A6JTnXxmg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCnDuE5+ev3eWzhbsOQ6JJRyk+bVLaRJ0hGYK2iwX+oIgIhAPT23+AZlFWjl4WR1yJs/kSnGAY9ctMq5qjFc5WiSm/C"}]}, "_npmVersion": "1.1.65", "_npmUser": {"name": "uncle<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "uncle<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.2.6": {"name": "deep-extend", "description": "Recursive object extending.", "license": "MIT", "version": "0.2.6", "homepage": "https://github.com/unclechu/node-deep-extend", "repository": {"type": "git", "url": "git://github.com/unclechu/node-deep-extend.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index", "engines": {"node": ">=0.4"}, "bugs": {"url": "https://github.com/unclechu/node-deep-extend/issues"}, "_id": "deep-extend@0.2.6", "dist": {"shasum": "1f767e02b46d88d0a4087affa4b11b1b0b804250", "tarball": "https://registry.npmjs.org/deep-extend/-/deep-extend-0.2.6.tgz", "integrity": "sha512-xEFxy1RLylg6EAgbMGkwMzSKa6IO/ywlL3HB+1EhDYB+w4GkRpEKZysyzU+aSfiTPeaPHWqJJlfhq25unOMF2A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDDCRVoyJdK58dh4/ai/aM7zf5qWNf0SeXV8KIqeVfsEAIgOa6aPnhCXi7zbvuaWsboHbAUP/QEDefiR3sWSwjOfDo="}]}, "_from": ".", "_npmVersion": "1.3.2", "_npmUser": {"name": "uncle<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "uncle<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.2.7": {"name": "deep-extend", "description": "Recursive object extending.", "license": "MIT", "version": "0.2.7", "homepage": "https://github.com/unclechu/node-deep-extend", "repository": {"type": "git", "url": "git://github.com/unclechu/node-deep-extend.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index", "engines": {"node": ">=0.4"}, "_id": "deep-extend@0.2.7", "dist": {"shasum": "4de94495073900fa866a11029f9b3b8a5e56014f", "tarball": "https://registry.npmjs.org/deep-extend/-/deep-extend-0.2.7.tgz", "integrity": "sha512-1yIOhYbr5L77M/PlaP1PD61HHKGMCSIrk2VB2zO8dP971S72XGzQtDis+fvndgT3sv1+hUNbyHyOEoPtB6mP1g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD37PqP9UW8qsET7IWunxq3Yj3o4tT1tBz1r3TqWYye0wIgVamW+qeMpzubQC32azuGcsKJgEI7e0EO0GkxT8n6lYA="}]}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "uncle<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "uncle<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.2.8": {"name": "deep-extend", "description": "Recursive object extending.", "license": "MIT", "version": "0.2.8", "homepage": "https://github.com/unclechu/node-deep-extend", "repository": {"type": "git", "url": "git://github.com/unclechu/node-deep-extend.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "uncle<PERSON>"}, "main": "index", "engines": {"node": ">=0.4"}, "_id": "deep-extend@0.2.8", "dist": {"shasum": "6d2893a805286e46d8243137c32fb991b50f4299", "tarball": "https://registry.npmjs.org/deep-extend/-/deep-extend-0.2.8.tgz", "integrity": "sha512-GypqJltv1XFZEd6Ad3H4JBIeEoH/rVGOMmo0eU8GVcyl/QzDKF7esRhPVJ2wd4qSnJDaP1RSbNxMw3w7laje3g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGqaUNq944swfkov1TWZue5abJQTJlRjf6Iav2mtR/NaAiA5epYyWvYkBaDgu5uAUhx9e3p4bYMsIy1sO5KTOJ+WzA=="}]}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "uncle<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "uncle<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.2.9": {"name": "deep-extend", "description": "Recursive object extending.", "license": "MIT", "version": "0.2.9", "homepage": "https://github.com/unclechu/node-deep-extend", "repository": {"type": "git", "url": "git://github.com/unclechu/node-deep-extend.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "uncle<PERSON>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/rprieto"}], "main": "index", "engines": {"node": ">=0.4"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "~1.19.0", "should": "~3.3.2"}, "directories": {"test": "./test"}, "bugs": {"url": "https://github.com/unclechu/node-deep-extend/issues"}, "_id": "deep-extend@0.2.9", "dist": {"shasum": "f078b2166e4ae9b638969644eaa9521e2e257c87", "tarball": "https://registry.npmjs.org/deep-extend/-/deep-extend-0.2.9.tgz", "integrity": "sha512-WTPvr2r/SoV8AmNK/rYo9vkmZLc+0ICi/BzCxF9hJ6U4D4cv0zFKtlxv6lAX3LdDaSXcBjDneBkQF2vzvYmq5Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDM63GPseXC7kpAwamNOB6QvzdwqKTUx26RQ5zxhJv3kwIgQuiKQgLYTuEtKCdE/neO+SvviVrCCC6UYAgw+/JN7MI="}]}, "_from": ".", "_npmVersion": "1.4.6", "_npmUser": {"name": "uncle<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "uncle<PERSON>", "email": "<EMAIL>"}]}, "0.2.10": {"name": "deep-extend", "description": "Recursive object extending.", "license": "MIT", "version": "0.2.10", "homepage": "https://github.com/unclechu/node-deep-extend", "repository": {"type": "git", "url": "git://github.com/unclechu/node-deep-extend.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "uncle<PERSON>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/rprieto"}], "main": "index", "engines": {"node": ">=0.4"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "~1.19.0", "should": "~3.3.2"}, "directories": {"test": "./test"}, "bugs": {"url": "https://github.com/unclechu/node-deep-extend/issues"}, "_id": "deep-extend@0.2.10", "dist": {"shasum": "8dd87f56835e91a7da57d07f3c5472165cf5d467", "tarball": "https://registry.npmjs.org/deep-extend/-/deep-extend-0.2.10.tgz", "integrity": "sha512-ctgw6aBg92C1uV2w6ORUwrL1IVsSUCmIgvu/1EdT7NCzgRdJOYv4NXecPlAXY/ZOu0kR0MEPfOWAQ0wYEumG9w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEBAimH4+UgxOT/JpNXH2yQnC5jct3zshBwTRI/Sxqk/AiA4S2gl9OrcxqVjSFgB8ZzvTafIwJcUxeD6za2YhRLK5g=="}]}, "_from": ".", "_npmVersion": "1.4.6", "_npmUser": {"name": "uncle<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "uncle<PERSON>", "email": "<EMAIL>"}]}, "0.2.11": {"name": "deep-extend", "description": "Recursive object extending.", "license": "MIT", "version": "0.2.11", "homepage": "https://github.com/unclechu/node-deep-extend", "repository": {"type": "git", "url": "git://github.com/unclechu/node-deep-extend.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "uncle<PERSON>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/rprieto"}], "main": "index", "engines": {"node": ">=0.4"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "~1.19.0", "should": "~3.3.2"}, "directories": {"test": "./test"}, "bugs": {"url": "https://github.com/unclechu/node-deep-extend/issues"}, "_id": "deep-extend@0.2.11", "dist": {"shasum": "7a16ba69729132340506170494bc83f7076fe08f", "tarball": "https://registry.npmjs.org/deep-extend/-/deep-extend-0.2.11.tgz", "integrity": "sha512-t2N+4ihO7YgydJOUI47I6GdXpONJ+jUZmYeTNiifALaEduiCja1mKcq3tuSp0RhA9mMfxdMN3YskpwB7puMAtw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDgliWSoPHIYst1VEwD+DddrnC+Nl2wjznmuwfL+P8g+AiBXBQB0X1TI39rKTLGT23Ugyj+mojWzlqo8eJO/qO3UdA=="}]}, "_from": ".", "_npmVersion": "1.4.6", "_npmUser": {"name": "uncle<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "uncle<PERSON>", "email": "<EMAIL>"}]}, "0.3.0": {"name": "deep-extend", "description": "Recursive object extending.", "license": "MIT", "version": "0.3.0", "homepage": "https://github.com/unclechu/node-deep-extend", "repository": {"type": "git", "url": "git://github.com/unclechu/node-deep-extend.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "uncle<PERSON>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/rprieto"}, {"name": "<PERSON>", "url": "https://github.com/maxmaximov"}], "main": "index", "engines": {"node": ">=0.4"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "~1.19.0", "should": "~3.3.2"}, "directories": {"test": "./test"}, "bugs": {"url": "https://github.com/unclechu/node-deep-extend/issues"}, "_id": "deep-extend@0.3.0", "dist": {"shasum": "5f34df22a50751cffe7699594c22262383ce1619", "tarball": "https://registry.npmjs.org/deep-extend/-/deep-extend-0.3.0.tgz", "integrity": "sha512-5izsLJWC7YEnRS15bS9WBFnRi3ngfjtAkwWbsLUuUKN74OgYE1PXzHAHnzLK9tIA7KymU/6pf+TQArxVSuUbsQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDNQdWTDYVTLGWPpvmMSY1ur05sfsxWaZ0PSTiauyOwvgIhAMQi/DULZClQ0VE+coSXE3wo2RRSwBU2ohIcS9+dxaSc"}]}, "_from": ".", "_npmVersion": "1.4.6", "_npmUser": {"name": "uncle<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "uncle<PERSON>", "email": "<EMAIL>"}]}, "0.3.1": {"name": "deep-extend", "description": "Recursive object extending.", "license": "MIT", "version": "0.3.1", "homepage": "https://github.com/unclechu/node-deep-extend", "tags": ["deep-extend", "extend", "deep", "recursive", "xtend", "clone", "merge", "json"], "repository": {"type": "git", "url": "git://github.com/unclechu/node-deep-extend.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "uncle<PERSON>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/rprieto"}, {"name": "<PERSON>", "url": "https://github.com/maxmaximov"}], "main": "index", "engines": {"node": ">=0.4"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "~1.19.0", "should": "~3.3.2"}, "directories": {"test": "./test"}, "bugs": {"url": "https://github.com/unclechu/node-deep-extend/issues"}, "_id": "deep-extend@0.3.1", "dist": {"shasum": "ae82a18ed83ca4ea00c32a05d8b6dba34f0c60ea", "tarball": "https://registry.npmjs.org/deep-extend/-/deep-extend-0.3.1.tgz", "integrity": "sha512-U+kBNbclKK3xoCAWl0oTnO9kXlwAGuZUUVVKV6OsJ+9kZ7vExsBh7C338laIoxnvMLrpxjwtMtUcSVRXqbucnA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEib5HcBj01MP9J/i/cGGjJzkPb03/BtBFg564CkUQ1yAiBsGUBvJ1PDj2kjTCCo0eJj3/2itZL1pU2VDUNE55rhdQ=="}]}, "_from": ".", "_npmVersion": "1.4.6", "_npmUser": {"name": "uncle<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "uncle<PERSON>", "email": "<EMAIL>"}]}, "0.3.2": {"name": "deep-extend", "description": "Recursive object extending.", "license": "MIT", "version": "0.3.2", "homepage": "https://github.com/unclechu/node-deep-extend", "keywords": ["deep-extend", "extend", "deep", "recursive", "xtend", "clone", "merge", "json"], "repository": {"type": "git", "url": "git://github.com/unclechu/node-deep-extend.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "uncle<PERSON>"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/rprieto"}, {"name": "<PERSON>", "url": "https://github.com/maxmaximov"}], "main": "index", "engines": {"node": ">=0.4"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "~1.19.0", "should": "~3.3.2"}, "directories": {"test": "./test"}, "bugs": {"url": "https://github.com/unclechu/node-deep-extend/issues"}, "_id": "deep-extend@0.3.2", "dist": {"shasum": "c75dff89f1c639e98438894d156c30b4f87b3a56", "tarball": "https://registry.npmjs.org/deep-extend/-/deep-extend-0.3.2.tgz", "integrity": "sha512-QvE0JF5LyyIRm6mKJTthqNpOcpu6eCjMmYc4M8Qfz2cqgbcItoHEZLbRct6qDMgu+FvQmwZg28rjLRY6OI59Bw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDNF0sDaVqRXaZE5mknTeR938uZsVwq920QM8twArVtqAiEAozEnuMEJJycfvVNaEImwBYthjkIvTltDR1R14gUVLU8="}]}, "_from": ".", "_npmVersion": "1.4.6", "_npmUser": {"name": "uncle<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "uncle<PERSON>", "email": "<EMAIL>"}]}, "0.3.3": {"name": "deep-extend", "description": "Recursive object extending.", "license": "MIT", "version": "0.3.3", "homepage": "https://github.com/unclechu/node-deep-extend", "keywords": ["deep-extend", "extend", "deep", "recursive", "xtend", "clone", "merge", "json"], "licenses": [{"type": "MIT", "url": "https://raw.githubusercontent.com/unclechu/node-deep-extend/master/LICENSE"}], "repository": {"type": "git", "url": "git://github.com/unclechu/node-deep-extend.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/unclechu/node-deep-extend/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/rprieto"}, {"name": "<PERSON>", "url": "https://github.com/maxmaximov"}], "main": "index", "engines": {"node": ">=0.4", "iojs": ">=1"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "^2.2.1", "should": "^5.2.0"}, "directories": {"test": "./test/"}, "files": ["test/index.spec.js", "test/mocha.opts", "index.js", "LICENSE", "README.md"], "gitHead": "de130b1670b7a8772b7e3497ccd335d18f49862c", "_id": "deep-extend@0.3.3", "_shasum": "2e8adfe69ed580ea019ce2efa9fa5f5106ea39c7", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "1.2.0", "_npmUser": {"name": "uncle<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "uncle<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "2e8adfe69ed580ea019ce2efa9fa5f5106ea39c7", "tarball": "https://registry.npmjs.org/deep-extend/-/deep-extend-0.3.3.tgz", "integrity": "sha512-3tDBMZ+aWYx0DkHEEafhCxg3Ts8WHFMuyrnXz65aWwUgvftTydALHDHtGwb/4AogYLpQSdS0R0aL4Jiu+yeQMw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDenuxoW70lGEc0Q+lBqCxjbd2XHL0lvf0sAcCcQ1attAiBpjTxidT8iMLKV1n9yTc4INlRdey+bkx/xkNOH703fnQ=="}]}}, "0.4.0": {"name": "deep-extend", "description": "Recursive object extending", "license": "MIT", "version": "0.4.0", "homepage": "https://github.com/unclechu/node-deep-extend", "keywords": ["deep-extend", "extend", "deep", "recursive", "xtend", "clone", "merge", "json"], "licenses": [{"type": "MIT", "url": "https://raw.githubusercontent.com/unclechu/node-deep-extend/master/LICENSE"}], "repository": {"type": "git", "url": "git://github.com/unclechu/node-deep-extend.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/unclechu/node-deep-extend/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/rprieto"}, {"name": "<PERSON>", "url": "https://github.com/maxmaximov"}], "main": "lib/deep-extend.js", "engines": {"node": ">=0.4.0", "iojs": ">=1.0.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "^2.2.1", "should": "^5.2.0"}, "directories": {"lib": "./lib/", "test": "./test/"}, "files": ["lib/deep-extend.js", "test/index.spec.js", "test/mocha.opts", "index.js", "LICENSE", "README.md"], "gitHead": "9ea374c54bdff7e42be163ae09670ea8cf3bbc7e", "_id": "deep-extend@0.4.0", "_shasum": "f58b46db58eb5d6439cdd0f2e6cafb4739fc1283", "_from": ".", "_npmVersion": "2.8.3", "_nodeVersion": "1.8.1", "_npmUser": {"name": "uncle<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "uncle<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "f58b46db58eb5d6439cdd0f2e6cafb4739fc1283", "tarball": "https://registry.npmjs.org/deep-extend/-/deep-extend-0.4.0.tgz", "integrity": "sha512-91gHkKCKP+MHRTWofs0PEZXD4zWMu0Nx1FR0NLDTNiEQ31GATJLlf86iUtZ5xcPEqC4fhzkE7DkooC7q43tVbw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQChQRt7wRJUR+5e5y0lhY47aWtJlzLCtfnExDgPQANWGgIgGf+U1/EM7PxWBe/X2O4JwELqTKyqlQr3rpmtzcQtLmY="}]}}, "0.4.1": {"name": "deep-extend", "description": "Recursive object extending", "license": "MIT", "version": "0.4.1", "homepage": "https://github.com/unclechu/node-deep-extend", "keywords": ["deep-extend", "extend", "deep", "recursive", "xtend", "clone", "merge", "json"], "licenses": [{"type": "MIT", "url": "https://raw.githubusercontent.com/unclechu/node-deep-extend/master/LICENSE"}], "repository": {"type": "git", "url": "git://github.com/unclechu/node-deep-extend.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/unclechu/node-deep-extend/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/rprieto"}, {"name": "<PERSON>", "url": "https://github.com/maxmaximov"}], "main": "lib/deep-extend.js", "engines": {"node": ">=0.12.0", "iojs": ">=1.0.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "^2.2.1", "should": "^5.2.0"}, "directories": {"lib": "./lib/", "test": "./test/"}, "files": ["lib/deep-extend.js", "index.js"], "gitHead": "08e39356bba769744c669eb219a31fee07decd19", "_id": "deep-extend@0.4.1", "_shasum": "efe4113d08085f4e6f9687759810f807469e2253", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "uncle<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "efe4113d08085f4e6f9687759810f807469e2253", "tarball": "https://registry.npmjs.org/deep-extend/-/deep-extend-0.4.1.tgz", "integrity": "sha512-td/IpUMQ7E0yjmU8s3orgl5+Fvv41C8p0zzf+IRYVuE3bM5Fc0G3hyln0mz9VOPt5T7KahPU3hMdR0B6t9cdhQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGxKbjD22T+0Hh1UEkrjSW8yUfzlXJYzEiq+UmsI9oaVAiEAje8OpI4daMG+3xRd8w6O6N62onW4M0umVaWeSHJa2Rs="}]}, "maintainers": [{"name": "uncle<PERSON>", "email": "<EMAIL>"}]}, "0.4.2": {"name": "deep-extend", "description": "Recursive object extending", "license": "MIT", "version": "0.4.2", "homepage": "https://github.com/unclechu/node-deep-extend", "keywords": ["deep-extend", "extend", "deep", "recursive", "xtend", "clone", "merge", "json"], "licenses": [{"type": "MIT", "url": "https://raw.githubusercontent.com/unclechu/node-deep-extend/master/LICENSE"}], "repository": {"type": "git", "url": "git://github.com/unclechu/node-deep-extend.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/unclechu/node-deep-extend/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/rprieto"}, {"name": "<PERSON>", "url": "https://github.com/maxmaximov"}, {"name": "<PERSON>", "url": "https://github.com/maxdeviant"}], "main": "lib/deep-extend.js", "engines": {"node": ">=0.12.0", "iojs": ">=1.0.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "^2.2.1", "should": "^5.2.0"}, "directories": {"lib": "./lib/", "test": "./test/"}, "files": ["lib/deep-extend.js", "index.js"], "gitHead": "8957a2ed0bdf814c6da61ac8a18c1d553d229272", "_id": "deep-extend@0.4.2", "_shasum": "48b699c27e334bf89f10892be432f6e4c7d34a7f", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.9.4", "_npmUser": {"name": "uncle<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "48b699c27e334bf89f10892be432f6e4c7d34a7f", "tarball": "https://registry.npmjs.org/deep-extend/-/deep-extend-0.4.2.tgz", "integrity": "sha512-cQ0iXSEKi3JRNhjUsLWvQ+MVPxLVqpwCd0cFsWbJxlCim2TlCo1JvN5WaPdPvSpUdEnkJ/X+mPGcq5RJ68EK8g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDes9VCRERPHw9FtIQDU8dVYSVfUIvXHuXrcWz4QD45OAiEAywHq+NlYp9H93swwsYotwhGltf3HU6B53x/RvRAQmBo="}]}, "maintainers": [{"name": "uncle<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/deep-extend-0.4.2.tgz_1494506815488_0.10077105974778533"}}, "0.5.0": {"name": "deep-extend", "description": "Recursive object extending", "license": "MIT", "version": "0.5.0", "homepage": "https://github.com/unclechu/node-deep-extend", "keywords": ["deep-extend", "extend", "deep", "recursive", "xtend", "clone", "merge", "json"], "licenses": [{"type": "MIT", "url": "https://raw.githubusercontent.com/unclechu/node-deep-extend/master/LICENSE"}], "repository": {"type": "git", "url": "git://github.com/unclechu/node-deep-extend.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/unclechu/node-deep-extend/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/rprieto"}, {"name": "<PERSON>", "url": "https://github.com/maxmaximov"}, {"name": "<PERSON>", "url": "https://github.com/maxdeviant"}], "main": "lib/deep-extend.js", "engines": {"node": ">=0.10.0", "iojs": ">=1.0.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "2.2.1", "should": "5.2.0"}, "files": ["index.js", "lib/"], "gitHead": "adb9c495d1dccbff3e87cdae157dc856f3a05c65", "_id": "deep-extend@0.5.0", "_shasum": "6ef4a09b05f98b0e358d6d93d4ca3caec6672803", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.9.4", "_npmUser": {"name": "uncle<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "6ef4a09b05f98b0e358d6d93d4ca3caec6672803", "tarball": "https://registry.npmjs.org/deep-extend/-/deep-extend-0.5.0.tgz", "integrity": "sha512-avbqfQK2M9h+GcX7OVmEvnhodw0rFdzXYRGD4b17BIRxyXVo9he2AIaE0ts1PrPA8sHgv0N6dHd99bL7Hky4Qg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB+J0PzgFoILkGQ48pW0CC2jQi4zBUCwnsSYCgQIJVpyAiB2fobSO/jZtt2ITyAAZo88h59OMp8G39Fx6tP8FmXMiw=="}]}, "maintainers": [{"name": "uncle<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/deep-extend-0.5.0.tgz_1494512875662_0.9282552199438214"}, "directories": {}}, "0.5.1": {"name": "deep-extend", "description": "Recursive object extending", "license": "MIT", "version": "0.5.1", "homepage": "https://github.com/unclechu/node-deep-extend", "keywords": ["deep-extend", "extend", "deep", "recursive", "xtend", "clone", "merge", "json"], "licenses": [{"type": "MIT", "url": "https://raw.githubusercontent.com/unclechu/node-deep-extend/master/LICENSE"}], "repository": {"type": "git", "url": "git://github.com/unclechu/node-deep-extend.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/unclechu/node-deep-extend/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/rprieto"}, {"name": "<PERSON>", "url": "https://github.com/maxmaximov"}, {"name": "<PERSON>", "url": "https://github.com/maxdeviant"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mwakerman"}], "main": "lib/deep-extend.js", "engines": {"node": ">=0.10.0", "iojs": ">=1.0.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "2.2.1", "should": "5.2.0"}, "files": ["index.js", "lib/"], "gitHead": "2e0110ed4c997bbd9bf29df1692d53494d9e90d4", "_id": "deep-extend@0.5.1", "_npmVersion": "5.8.0", "_nodeVersion": "8.11.1", "_npmUser": {"name": "uncle<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-N8vBdOa+DF7zkRrDCsaOXoCs/E2fJfx9B9MrKnnSiHNh4ws7eSys6YQE4KvT1cecKmOASYQBhbKjeuDD9lT81w==", "shasum": "b894a9dd90d3023fbf1c55a394fb858eb2066f1f", "tarball": "https://registry.npmjs.org/deep-extend/-/deep-extend-0.5.1.tgz", "fileCount": 6, "unpackedSize": 9027, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa5FZ9CRA9TVsSAnZWagAAT2IP/izHpoEiDyZQg9AV8jbR\nzDo2OFM54+daVV6eYYFJ9cj/d7LLJEeqxd2f4cMp4I78WeQG9MNJCOufwnVI\npduSg2/aGNOWdfDwbj9ueWqBFqtu/AEvh9jLZiUbSWh2G2rsmLsU9/czistP\nhZPU3VzwZhmunMH7GJraD+8bVv5BPzPZJuh5/Ey9NPyh39I5OpxIWX2CTWJV\n1QwUPYn5nuZ+u/1ZZqThZVnWo0oQqk7nOQ0y8DKCPGeZTe64wteupw/ns1+w\nsOqZEOkbr/lrMqn9pzU2j5Bt80tabAnwnhyu4GNoGhAzgPJI1Pyfkx1KT+eY\nOCJes2aJOmsgI5HkpzuHK1VlURiaV68kQDgHj1+mZLkD+wlt8UnB8SrC8INV\nbvvMEcuqkuxmSXDH/rYzKbikTs6OLUXMN2Jxl2tj9XTco+3gincGYjOB4F1M\nPGzZyFRQl9O7sEar2ZFVpx2/BgCjEFku/qjJ/YsbBf3MY9Q/2+9RHpTrgoSo\nKqrXVa1ORPUdbFW3ZMweY8Jx6PHX3NKCkvtUEAPs4qf4MG4oaELo1oHp72zY\nlDBG+A+MDnDd5XiemcVruAIxAb9+0NOkIYzipuRa0wsEXtnsDtWUZ+NjvAav\nB4NTS/orR1Dfwwuk8u3zT9AuWzRrE9zselpFICouSpgvP1KJqXvB5oRP7Ulf\nRwyZ\r\n=DCF2\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCUhU3Ybp0NqO/4qTLuY8xrQmPYRhLIboTtvWXERh2QfgIgf30kEhIbfIXjaVwi9DZlk1u42z1iiVhveLaC9fwmEqs="}]}, "maintainers": [{"name": "uncle<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deep-extend_0.5.1_1524913788873_0.2165986777699198"}, "_hasShrinkwrap": false}, "0.6.0": {"name": "deep-extend", "description": "Recursive object extending", "license": "MIT", "version": "0.6.0", "homepage": "https://github.com/unclechu/node-deep-extend", "keywords": ["deep-extend", "extend", "deep", "recursive", "xtend", "clone", "merge", "json"], "licenses": [{"type": "MIT", "url": "https://raw.githubusercontent.com/unclechu/node-deep-extend/master/LICENSE"}], "repository": {"type": "git", "url": "git://github.com/unclechu/node-deep-extend.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/unclechu/node-deep-extend/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/rprieto"}, {"name": "<PERSON>", "url": "https://github.com/maxmaximov"}, {"name": "<PERSON>", "url": "https://github.com/maxdeviant"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mwakerman"}], "main": "lib/deep-extend.js", "engines": {"node": ">=4.0.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "5.2.0", "should": "13.2.1"}, "files": ["index.js", "lib/"], "gitHead": "f3f2b4f30fffe8abc9a99a7d6469fb354ca206e9", "_id": "deep-extend@0.6.0", "_npmVersion": "6.0.1", "_nodeVersion": "8.11.0", "_npmUser": {"name": "uncle<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-LOHxIOaPYdHlJRtCQfDIVZtfw/ufM8+rVj649RIHzcm/vGwQRXFt6OPqIFWsm2XEMrNIEtWR64sY1LEKD2vAOA==", "shasum": "c4fa7c95404a17a9c3e8ca7e1537312b736330ac", "tarball": "https://registry.npmjs.org/deep-extend/-/deep-extend-0.6.0.tgz", "fileCount": 6, "unpackedSize": 9190, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBF2+CRA9TVsSAnZWagAAWWsP+wayRHY/u4XwuxCopF6q\nVXfgmhXakYrxCXNpIUVCPC2lBajceZ6X4IOqXIIMnta+x+aYha0TAqH6FT+k\n5f9heBarQk4e3oLMJgaFDYkdTPOqVpDy95g+WOW+NiZN/87z2WnRsPzknkpY\n7R8FQGyR9PEromNmL4pCQY+0G1U4MB8o4rWqu9wp8oY2ebYJ3yNWe2CT9PeZ\nmIGjqyWLH84t8Yp40onc/15YHIAheYA5l9eD7pZeuxc+kddfSDbiwwpedWCV\nJLmKNnNoI4EHDuf+M8RN3U7CJpzAJWKSPN5wId+vFOjGaMnbrSA83DS66JvV\n8JVnzCFhbfpabiqDfkzE8Lq+QRE4P25Tjbc9kxS9yuQTShF3AJUzZUl5rQsw\n5Ov0fOt+BaT1e1/CvYlH8M8tvnGDPkYZ1A3tyRtjZUG/tNkNlLethX77kaf/\nzvnrdsOmReJ8wAacGKEB8+VLVaYg4bDCd5FGgSzO66J0OW2MYghZQi/9Scz8\nzgWmYdgrJ8ftxMDWrOqvcxI+df2bATGtXg1djPb0snsye3HAlNnWDK0lMtwF\n9K22xEK0jaqwDnwCFZKQU2aC6zWgQxlByZu2zA8Kiy3ahRe3XIdUEmxjsmuo\nc13UN9IIjuqHv+2P1U9kL6OEy2omLae/rd/GRy1xPfE/1EdkrcQ//0kTsVBh\nXI++\r\n=u4KH\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCoKLsp9tx+V4Ml/i4mjNmRPutiOqPORUCt5+BH4brt5wIgQKkA2ZjY6i4rQQjOd7465XFu6IFCWRY9VFfofCgKAVM="}]}, "maintainers": [{"name": "uncle<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deep-extend_0.6.0_1527012797367_0.028784991930715043"}, "_hasShrinkwrap": false}}, "readme": "Deep Extend\n===========\n\nRecursive object extending.\n\n[![Build Status](https://api.travis-ci.org/unclechu/node-deep-extend.svg?branch=master)](https://travis-ci.org/unclechu/node-deep-extend)\n\n[![NPM](https://nodei.co/npm/deep-extend.png?downloads=true&downloadRank=true&stars=true)](https://nodei.co/npm/deep-extend/)\n\nInstall\n-------\n\n```bash\n$ npm install deep-extend\n```\n\nUsage\n-----\n\n```javascript\nvar deepExtend = require('deep-extend');\nvar obj1 = {\n  a: 1,\n  b: 2,\n  d: {\n    a: 1,\n    b: [],\n    c: { test1: 123, test2: 321 }\n  },\n  f: 5,\n  g: 123,\n  i: 321,\n  j: [1, 2]\n};\nvar obj2 = {\n  b: 3,\n  c: 5,\n  d: {\n    b: { first: 'one', second: 'two' },\n    c: { test2: 222 }\n  },\n  e: { one: 1, two: 2 },\n  f: [],\n  g: (void 0),\n  h: /abc/g,\n  i: null,\n  j: [3, 4]\n};\n\ndeepExtend(obj1, obj2);\n\nconsole.log(obj1);\n/*\n{ a: 1,\n  b: 3,\n  d:\n   { a: 1,\n     b: { first: 'one', second: 'two' },\n     c: { test1: 123, test2: 222 } },\n  f: [],\n  g: undefined,\n  c: 5,\n  e: { one: 1, two: 2 },\n  h: /abc/g,\n  i: null,\n  j: [3, 4] }\n*/\n```\n\nUnit testing\n------------\n\n```bash\n$ npm test\n```\n\nChangelog\n---------\n\n[CHANGELOG.md](./CHANGELOG.md)\n\nAny issues?\n-----------\n\nPlease, report about issues\n[here](https://github.com/unclechu/node-deep-extend/issues).\n\nLicense\n-------\n\n[MIT](./LICENSE)\n", "maintainers": [{"name": "uncle<PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-07-10T23:19:22.518Z", "created": "2012-03-29T22:27:49.013Z", "0.2.0": "2012-03-29T22:27:51.560Z", "0.2.1": "2012-03-29T22:46:08.606Z", "0.2.2": "2012-04-11T03:57:45.123Z", "0.2.4": "2012-11-13T06:25:33.628Z", "0.2.5": "2012-11-13T08:38:57.215Z", "0.2.6": "2013-08-26T17:27:10.679Z", "0.2.7": "2014-02-01T15:53:52.356Z", "0.2.8": "2014-02-01T16:39:27.191Z", "0.2.9": "2014-05-27T08:43:02.801Z", "0.2.10": "2014-05-27T09:50:42.551Z", "0.2.11": "2014-07-28T17:16:26.230Z", "0.3.0": "2014-10-03T20:28:28.129Z", "0.3.1": "2014-10-03T20:34:01.297Z", "0.3.2": "2014-10-03T20:36:18.936Z", "0.3.3": "2015-03-28T13:10:03.417Z", "0.4.0": "2015-05-01T19:52:47.885Z", "0.4.1": "2016-01-16T23:14:09.322Z", "0.4.2": "2017-05-11T12:46:56.910Z", "0.5.0": "2017-05-11T14:27:57.546Z", "0.5.1": "2018-04-28T11:09:48.953Z", "0.6.0": "2018-05-22T18:13:17.476Z"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/unclechu/node-deep-extend.git"}, "readmeFilename": "README.md", "homepage": "https://github.com/unclechu/node-deep-extend", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/rprieto"}, {"name": "<PERSON>", "url": "https://github.com/maxmaximov"}, {"name": "<PERSON>", "url": "https://github.com/maxdeviant"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mwakerman"}], "bugs": {"url": "https://github.com/unclechu/node-deep-extend/issues"}, "license": "MIT", "keywords": ["deep-extend", "extend", "deep", "recursive", "xtend", "clone", "merge", "json"], "users": {"gdbtek": true, "jessaustin": true, "deepanchor": true, "abdihaikal": true, "tommyzzm": true, "ruyadorno": true, "nichoth": true, "sebastian1118": true, "xiongwilee": true, "jybleau": true, "dzhou777": true, "martinspinks": true, "edwardxyt": true, "delacap": true, "71emj1": true, "professorcoal": true, "mikroacse": true, "flumpus-dev": true}}