{"_id": "tar-stream", "_rev": "107-d3faf532e910ed18d11e3900deda7b9a", "name": "tar-stream", "description": "tar-stream is a streaming tar parser and generator and nothing else. It operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "dist-tags": {"latest": "3.1.7"}, "versions": {"0.1.0": {"name": "tar-stream", "version": "0.1.0", "description": "tar-stream is an alternative tar parser. It is streams2, does not have a fstream dependency and does not do any file io.", "repository": {"type": "git", "url": "git://github.com:mafintosh/tar-stream.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "dependencies": {"end-of-stream": "~0.1.3", "bl": "~0.6.0"}, "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "_id": "tar-stream@0.1.0", "dist": {"shasum": "cc9b9bfd9d47cba1fc1ee48c1c78109f3c06a3d0", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-0.1.0.tgz", "integrity": "sha512-gMZM7ib6mTQAm2b9VEHAV+UUp3GeF6/veBSVWWsbV36QKs+TndnJrLBQi5UiABDiMMNVhNC4LMA6gRRpjn97mw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDAgnQUySOiX03qMVwMpLj1VP1kkiQZAfMXKsmaO4J91gIhAO6JPHo666G3fXqoFdC2c5NPGFZqnwe1N4Pozq/wF0hO"}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.1.1": {"name": "tar-stream", "version": "0.1.1", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means easily you can extract/parse tarballs without ever hitting the file system.", "repository": {"type": "git", "url": "git://github.com:mafintosh/tar-stream.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "dependencies": {"end-of-stream": "~0.1.3", "bl": "~0.6.0"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "_id": "tar-stream@0.1.1", "dist": {"shasum": "efd896a159ef21024964a23f9bbc0bdb495ab8a8", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-0.1.1.tgz", "integrity": "sha512-CiItR6Bw99aBriluyJhwWe3KPVaVBFUq+9qhl0oHHDZVduqiehXJWfxVg3wTLLXbZGpCFcAOl9KosjIOJLHTBQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDtQW3guCxe3nIAlusqSnrYNsHR6Z6hUXH1PWAXAR1qtAiEAtC4SvQsJhHLTRANFIl5ZwL5CB5Fy+awE5d2fLOHxMdo="}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.1.2": {"name": "tar-stream", "version": "0.1.2", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means easily you can extract/parse tarballs without ever hitting the file system.", "repository": {"type": "git", "url": "git://github.com:mafintosh/tar-stream.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "dependencies": {"end-of-stream": "~0.1.3", "bl": "~0.6.0"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "_id": "tar-stream@0.1.2", "dist": {"shasum": "731ed228041c2c3dd7609c41f187f0508a2810f8", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-0.1.2.tgz", "integrity": "sha512-zk8FRTt2N2UnVkv9ezEuVSnX97NkQvd59Cfd6OkwxtU87yLr31uTqSbdrUsDRyKJLldn3VNvNt5TdnNxQDqJcg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG5Oo0F09MAVqUGekk+LG1p00KaqOPpc1+BAnlw2C2gQAiA1Wgo87CJDc9a7PMjku50Q3oHu55LLlgkRwmLm7UYxpw=="}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.2.0": {"name": "tar-stream", "version": "0.2.0", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "repository": {"type": "git", "url": "git://github.com:mafintosh/tar-stream.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "dependencies": {"end-of-stream": "~0.1.3", "bl": "~0.6.0"}, "devDependencies": {"tap": "~0.4.6", "concat-stream": "~1.2.1"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "_id": "tar-stream@0.2.0", "dist": {"shasum": "ed2b9a0b933ab2a2d53f7b58fcf8f1a37dfdc3e3", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-0.2.0.tgz", "integrity": "sha512-NCvnG5NFKcp6tTZPsFSZlhc9Af+e9pKmmv//EEW+bgOYw7xCqeeO8Ztk1IV2MacnsV4QqY0K57Pb1AuMYC/Ucg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD03KTTaUgXBYoI6RfGkK1n25EY5UWiKXpSaKje2gg8swIhAIspN+3ZIQM6FIT6Cq5G6F52cuGvOb+QN94bTfD6GiMw"}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.2.1": {"name": "tar-stream", "version": "0.2.1", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "repository": {"type": "git", "url": "git://github.com:mafintosh/tar-stream.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "dependencies": {"end-of-stream": "~0.1.3", "bl": "~0.6.0"}, "devDependencies": {"tap": "~0.4.6", "concat-stream": "~1.2.1"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "_id": "tar-stream@0.2.1", "dist": {"shasum": "c57b99e9888712f174b82ba5102a01e73943d6ec", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-0.2.1.tgz", "integrity": "sha512-g/uR02KQOmcgUIiHEOOa2T1HS3mV44SBCzrt6JQAvSzqf02bLrYNpAyujLY+ItCInH4u/ce6ZfOuo2Ix5bnu1A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH/A5aphCXMaaXoyMz5x0GJq4wLBvss2tlt/JdkZjZGIAiEAxuo9aCr3H6HR84LqUZLAEerrUhvlEpcN2+dMU8ZJI1w="}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.2.2": {"name": "tar-stream", "version": "0.2.2", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "repository": {"type": "git", "url": "git://github.com:mafintosh/tar-stream.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "dependencies": {"end-of-stream": "~0.1.3", "bl": "~0.6.0"}, "devDependencies": {"tap": "~0.4.6", "concat-stream": "~1.2.1"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "_id": "tar-stream@0.2.2", "dist": {"shasum": "a69a40ba0768a546c852736fa237be1fa252342a", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-0.2.2.tgz", "integrity": "sha512-NEtz/s1stOyuZgCgMeBDXU6nteMctCidf95hzQNx1ubadxHK61ozqLJiekvH2RxM64quXY+7EP4uWdfCRGB4IQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDxytPxxddpf6OsmGMwXTOD4jpyt3+wl+mDJjtrTxxIggIgO55GlQ/MQhAFVjWEGtQth5p5oV1c1IlB9NcK/PLiPqo="}]}, "_from": ".", "_npmVersion": "1.3.17", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.2.3": {"name": "tar-stream", "version": "0.2.3", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "repository": {"type": "git", "url": "git://github.com:mafintosh/tar-stream.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "dependencies": {"end-of-stream": "~0.1.3", "bl": "~0.6.0"}, "devDependencies": {"tap": "~0.4.6", "concat-stream": "~1.2.1"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "_id": "tar-stream@0.2.3", "dist": {"shasum": "6ad3bae7f4fdc6dd55a0cea0b3ed10681d4123ff", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-0.2.3.tgz", "integrity": "sha512-wCwam7Fvk5tyZhmnN7Y8NWDWq0uRZc9zDQhtzEH/NSCc35Wi811MCDzt/5PPsF+BlvEd+PI0xKfZpUOCAMIETA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHMEkuLnSVdu1G+sqWpFJb6MCtLFFOgHO/KJm4owlm42AiB6mrTAddYKQLPrsLGxXLMjyfPCj+O922CHtdk3WITXig=="}]}, "_from": ".", "_npmVersion": "1.3.17", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.2.4": {"name": "tar-stream", "version": "0.2.4", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "repository": {"type": "git", "url": "git://github.com:mafintosh/tar-stream.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 0.8.0"}, "dependencies": {"end-of-stream": "~0.1.3", "bl": "~0.6.0"}, "devDependencies": {"tap": "~0.4.6", "concat-stream": "~1.2.1", "readable-stream": "~1.1.9"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "_id": "tar-stream@0.2.4", "dist": {"shasum": "15b0ca1ba40dc45845bdb68983e3e66fcfc7ce27", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-0.2.4.tgz", "integrity": "sha512-QF5pZlxqqJFgSR6BGelbIaQCBx9aGDKNT154ANUCrkRAACSGYt7OU8KhtFB5PvxcPPkpDWgYZ+MO+Tz9ezTpVw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDWxN6HZzU5m22K8e5YlAgzkkeaToTCWRWQ0oy9xe52ewIgcsSS+GDXQQPf1Bd4ie4L1b9Iq360x+mZx5F2N4NtBAs="}]}, "_from": ".", "_npmVersion": "1.3.17", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.2.5": {"name": "tar-stream", "version": "0.2.5", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "repository": {"type": "git", "url": "git://github.com:mafintosh/tar-stream.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 0.8.0"}, "dependencies": {"end-of-stream": "~0.1.3", "bl": "~0.6.0"}, "devDependencies": {"tap": "~0.4.6", "concat-stream": "~1.2.1", "readable-stream": "~1.1.9"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "_id": "tar-stream@0.2.5", "dist": {"shasum": "3212630dcdee5dddd38128e04ea7c98536d44edf", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-0.2.5.tgz", "integrity": "sha512-ie1BL/w/7iHNETh2HxC9uV02Pq2U0K4TpYmD4VvRWjkOe0S6B4TUmh56VTDcLCXrpYT4KUsY0k6s+nxBNFsGnQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDdFRraBeBLlhHHuqLUSCFrutQc5OezrFwk8PBGDVALLAiBOPznq7zHk/PuONV+n+Aito4COWsCLKA3I+6rUTGSruA=="}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.3.0": {"name": "tar-stream", "version": "0.3.0", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "repository": {"type": "git", "url": "git://github.com:mafintosh/tar-stream.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 0.8.0"}, "dependencies": {"end-of-stream": "~0.1.3", "bl": "~0.6.0"}, "devDependencies": {"tap": "~0.4.6", "concat-stream": "~1.2.1", "readable-stream": "~1.1.9"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "_id": "tar-stream@0.3.0", "dist": {"shasum": "5769793f8e6ae8f46813a3d80fa194187d494efd", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-0.3.0.tgz", "integrity": "sha512-uiOWrvUUdsIUgTBq62b614GJQHkv+/4m1hfagG50OeCQNoBL07i+bEryM3S8yhTuOzSB49iHKKWVeYzZ7UrgDg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDUvJ9BnOkGtLWuNyiNjwFrrWYONTnU9af6WgM770MaxAiA97fytV6JROuAVoM1/Xy5tNBaR3/Ab/p+F/GqVzmvPmA=="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.3.1": {"name": "tar-stream", "version": "0.3.1", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "repository": {"type": "git", "url": "git://github.com:mafintosh/tar-stream.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 0.8.0"}, "dependencies": {"bl": "~0.6.0", "end-of-stream": "~0.1.3", "readable-stream": "~1.0.26-4"}, "devDependencies": {"tap": "~0.4.6", "concat-stream": "~1.2.1"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "_id": "tar-stream@0.3.1", "dist": {"shasum": "95b4d0239c617d2cd701bb382700908ce09c81a5", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-0.3.1.tgz", "integrity": "sha512-tvCmuu4Y845Njk31tl5HPPKYjffSeGCi6MHCrYLqIG0dwC5phubbjL5RCpVn0r7+Ipc8cbI6YGVLaPGGVkKWsw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD8ASkKYXO7sDaSih1NrVnI0CGc5e4Gyg/94XG9PZqi1AIgElruUc5hHRjcQTrICEG8Twi/zuK3moMVektPsqusqlE="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.3.2": {"name": "tar-stream", "version": "0.3.2", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "repository": {"type": "git", "url": "git://github.com:mafintosh/tar-stream.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 0.8.0"}, "dependencies": {"bl": "~0.6.0", "end-of-stream": "~0.1.3", "readable-stream": "~1.0.26-4"}, "devDependencies": {"tap": "~0.4.6", "concat-stream": "~1.2.1"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "_id": "tar-stream@0.3.2", "dist": {"shasum": "b492bc55ce3bd8b55a043fc77d8dea8cf999ce36", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-0.3.2.tgz", "integrity": "sha512-rj/P0ivRn48V5uqU0Mhapob1LHMQRP49sT7V5/FI0bdvXVYEk8/nyBoLI2i41a2+ojZ1p9aczSDoWok+6ThWQw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDsOLTh5AefFdwmzeAdLNHJoQQm72gmNeZnYs2s8vFfUAiAxX82UvSalI/FzCEZLAWLx2gkfmztMN2oft7IJvTKLlw=="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.3.3": {"name": "tar-stream", "version": "0.3.3", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "repository": {"type": "git", "url": "git://github.com:mafintosh/tar-stream.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 0.8.0"}, "dependencies": {"bl": "~0.6.0", "end-of-stream": "~0.1.3", "readable-stream": "~1.0.26-4"}, "devDependencies": {"tap": "~0.4.6", "concat-stream": "~1.2.1"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "_id": "tar-stream@0.3.3", "dist": {"shasum": "23ca53bd738b8702272a80e26cc938bc4b84b87b", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-0.3.3.tgz", "integrity": "sha512-fTNtCsmgktzOWIzXVthXp+U4ShLk8KDWWI6TRedUkRvO3MHV1R5Y08BlVD0YaVZ1GVGFaD63+veqMhhwtv+b3w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGaicB7HeQNk5yTKrFDxL2xkTN/kTIR3KYIciILH8cmPAiEAqqUaZRnM9QTtJaGqKERYHTobufGf+YPJ73gFfSyNexw="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.4.0": {"name": "tar-stream", "version": "0.4.0", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "repository": {"type": "git", "url": "git://github.com:mafintosh/tar-stream.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 0.8.0"}, "dependencies": {"bl": "~0.6.0", "end-of-stream": "~0.1.3", "readable-stream": "~1.0.26-4"}, "devDependencies": {"tap": "~0.4.6", "concat-stream": "~1.2.1"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "_id": "tar-stream@0.4.0", "_shasum": "58f0e893d8a90b333795f6f62b28a39acd9b52cb", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "58f0e893d8a90b333795f6f62b28a39acd9b52cb", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-0.4.0.tgz", "integrity": "sha512-aujUNx8RcrYV169hlHePm8J1ZhyApG4QeCWSUBUt7GslgReun6gS01ukoGOYEg9L4mowkqMISqK33OO/at5I0Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICWvBO5aw+JdCeIr1t9/tgLL4Oungr7CjkGW+DnGCp9lAiBgBiclpmr0UQYsybTgYM8o9ojpmkP9rPa5b4LBo14hlA=="}]}, "directories": {}}, "0.4.1": {"name": "tar-stream", "version": "0.4.1", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "repository": {"type": "git", "url": "git://github.com:mafintosh/tar-stream.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 0.8.0"}, "dependencies": {"bl": "~0.6.0", "end-of-stream": "~0.1.3", "readable-stream": "~1.0.26-4", "xtend": "^3.0.0"}, "devDependencies": {"tap": "~0.4.6", "concat-stream": "~1.2.1"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "_id": "tar-stream@0.4.1", "_shasum": "3ac4e8ba7de71b8532efd8f30f491355c8665934", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "3ac4e8ba7de71b8532efd8f30f491355c8665934", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-0.4.1.tgz", "integrity": "sha512-hCXEBs8kIDOWEIxQdGYtob1I4VyMz0plH5P7WZem4ZDITbpq+e+TQEl5JeZE8KRqcpIfzYyN+do+6HaPQUW25w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG0l/ddSG62IJDeWDShNdRlHvIj6YBxgJHOTxN8VS2y5AiEA8Si6Hh2Ku00aVPzS2P+eYpnBxvsMOied0X2FWcdYjyc="}]}, "directories": {}}, "0.4.2": {"name": "tar-stream", "version": "0.4.2", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "repository": {"type": "git", "url": "git://github.com:mafintosh/tar-stream.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 0.8.0"}, "dependencies": {"bl": "~0.6.0", "end-of-stream": "~0.1.3", "readable-stream": "~1.0.26-4", "xtend": "~3.0.0"}, "devDependencies": {"tap": "~0.4.6", "concat-stream": "~1.2.1"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "_id": "tar-stream@0.4.2", "_shasum": "a86dde835c84ab377747cc72cf25d9804c589c86", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "a86dde835c84ab377747cc72cf25d9804c589c86", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-0.4.2.tgz", "integrity": "sha512-mkiRUxuwKyBmWa8L3wkk0R9MP112KrGG1U2+XGO87k9emQVx9KsyM/wPv6ptD0a8g0H0X8UCNZJBcwdEZuRDBw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCRp2FAU5Ptl2i/OKImx93dZd0sixho27m7HBJh0pE0pgIgVTUWbLnb8dCrkcYdwiu92Ezo8etYHvsBZIm1b+x+xr4="}]}, "directories": {}}, "0.4.3": {"name": "tar-stream", "version": "0.4.3", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "repository": {"type": "git", "url": "git://github.com:mafintosh/tar-stream.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 0.8.0"}, "dependencies": {"bl": "~0.8.1", "end-of-stream": "~0.1.3", "readable-stream": "~1.0.26-4", "xtend": "~3.0.0"}, "devDependencies": {"tap": "~0.4.6", "concat-stream": "~1.2.1"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "_id": "tar-stream@0.4.3", "_shasum": "16d0038c0de0bcf8edc96b82c95b17b155ae6789", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "16d0038c0de0bcf8edc96b82c95b17b155ae6789", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-0.4.3.tgz", "integrity": "sha512-6ftjZRQFo2FJCHVJOpzIlj4EQApnCxAkkKQ2P7cZfQkQTf+Qy+QwApdUNpPvfQvDcc/miWe5whqRANu2yU31Og==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHEVr8Jnc7x3flhKDHXSD6bcYk1SeVPoxcoCQj/PecO2AiEAlivNgAozbYdsEtIFNYf2qQ8YYKi27NdEUTsAdQjMhEg="}]}, "directories": {}}, "0.4.4": {"name": "tar-stream", "version": "0.4.4", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "repository": {"type": "git", "url": "git://github.com:mafintosh/tar-stream.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 0.8.0"}, "dependencies": {"bl": "~0.8.1", "end-of-stream": "~0.1.3", "readable-stream": "~1.0.26-4", "xtend": "~3.0.0"}, "devDependencies": {"tap": "~0.4.6", "concat-stream": "~1.2.1"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "main": "index.js", "directories": {"test": "test"}, "license": "MIT", "_id": "tar-stream@0.4.4", "_shasum": "55733222ca3e1ebf58f2805b5b666596e1f8d5f3", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "55733222ca3e1ebf58f2805b5b666596e1f8d5f3", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-0.4.4.tgz", "integrity": "sha512-OqjOR/0uRzX1NO0xEZa3/CCOEgPMu7a0Pv26lSMeJfXFdiiQFeU63Szf/xWJHqBiTuEcqZqZ9pxTJSUP0wCU5A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE61kWOSf2Skas2Sip8ESpVoKoQZOXDFh9PHhqbn7qo+AiBIi4PUqxXhb3QWhVIMQHEWov3C7vjnTX/biMXbhnD+JQ=="}]}}, "0.4.5": {"name": "tar-stream", "version": "0.4.5", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "repository": {"type": "git", "url": "git://github.com:mafintosh/tar-stream.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 0.8.0"}, "dependencies": {"bl": "^0.9.0", "end-of-stream": "^1.0.0", "readable-stream": "^1.0.27-1", "xtend": "^4.0.0"}, "devDependencies": {"concat-stream": "^1.4.6", "tape": "^2.14.0"}, "scripts": {"test": "tape test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "main": "index.js", "directories": {"test": "test"}, "license": "MIT", "gitHead": "6dfff6b2728f42f67d235b831e0d3f80a7a13f8d", "_id": "tar-stream@0.4.5", "_shasum": "a5e0a7521f57974c919b2b99fdae55589aeefa46", "_from": ".", "_npmVersion": "1.4.23", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "a5e0a7521f57974c919b2b99fdae55589aeefa46", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-0.4.5.tgz", "integrity": "sha512-tCk82ueWJSVF/E8cFLk8LQVwdjyvaenqSrBs5PRUhe0M/5u0NYJNkAc06tYSshr3FbRBbjtKbSSS2XaZ9VPyww==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCBBIMit8RJo19igTNmOoRgs45JddF1aMlBDXXomRBdIQIgcv+YHddcy4nOxphfLeU0wyYM5pdYEQDA4wUyLh8UhIw="}]}}, "0.4.6": {"name": "tar-stream", "version": "0.4.6", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "repository": {"type": "git", "url": "git://github.com:mafintosh/tar-stream.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 0.8.0"}, "dependencies": {"bl": "^0.9.0", "end-of-stream": "^1.0.0", "readable-stream": "^1.0.27-1", "xtend": "^4.0.0"}, "devDependencies": {"concat-stream": "^1.4.6", "tape": "^2.14.0"}, "scripts": {"test": "tape test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "main": "index.js", "directories": {"test": "test"}, "license": "MIT", "gitHead": "0b57f1c1c6aaaea09574e2a0fd1845639a006f6b", "_id": "tar-stream@0.4.6", "_shasum": "1857d5d517181d5b2ab80b127c6c1e30e2832f48", "_from": ".", "_npmVersion": "1.4.23", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "1857d5d517181d5b2ab80b127c6c1e30e2832f48", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-0.4.6.tgz", "integrity": "sha512-bStluFllw5ynrvMxbP0ZlLr72Pa1wF7wrXueU1Z9uBCmoUcDeDEhLMVLtKUkZQasqFi6qhX5Ho3Kr5LpBz3RwA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC1B5xInvvf9VEZR4s2We0Qp+q0tfmvYV3A4ZT+EVi46AIhAJ9Z1a16D4h3mlxg1X2cC3ourqvIYm0uN8mP5wot90ku"}]}}, "0.4.7": {"name": "tar-stream", "version": "0.4.7", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "repository": {"type": "git", "url": "git://github.com:mafintosh/tar-stream.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 0.8.0"}, "dependencies": {"bl": "^0.9.0", "end-of-stream": "^1.0.0", "readable-stream": "^1.0.27-1", "xtend": "^4.0.0"}, "devDependencies": {"concat-stream": "^1.4.6", "tape": "^2.14.0"}, "scripts": {"test": "tape test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "main": "index.js", "directories": {"test": "test"}, "license": "MIT", "gitHead": "3bd4c7a0c34e4357105940baf1ff18b1f05a041b", "_id": "tar-stream@0.4.7", "_shasum": "1f1d2ce9ebc7b42765243ca0e8f1b7bfda0aadcd", "_from": ".", "_npmVersion": "1.4.23", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "1f1d2ce9ebc7b42765243ca0e8f1b7bfda0aadcd", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-0.4.7.tgz", "integrity": "sha512-8/A2iGloynV8Q0cb43ez+aK1PEYWueUr4yPrenbwOJR3Y63VjaIPIravWB6VcYAz4jQfzr4TLX8i3/tDhkzPRw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEqcsggNoLygE8X+/yBESLjlWY56NTXPMFJwgdpvB4UHAiBsyxU6/IKE42agVPbEgMqnomt0LldV1MZv05cfDmEoXw=="}]}}, "1.0.0": {"name": "tar-stream", "version": "1.0.0", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "repository": {"type": "git", "url": "git://github.com:mafintosh/tar-stream.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 0.8.0"}, "dependencies": {"bl": "^0.9.0", "end-of-stream": "^1.0.0", "readable-stream": "^1.0.27-1", "xtend": "^4.0.0"}, "devDependencies": {"concat-stream": "^1.4.6", "tape": "^2.14.0"}, "scripts": {"test": "tape test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "main": "index.js", "directories": {"test": "test"}, "license": "MIT", "gitHead": "b51602478a26458e17ca9d8f72fb9ccd8db5e8eb", "_id": "tar-stream@1.0.0", "_shasum": "6d28e2a0693ecd5669750e3326d004baa27ebd69", "_from": ".", "_npmVersion": "2.0.0", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "6d28e2a0693ecd5669750e3326d004baa27ebd69", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-1.0.0.tgz", "integrity": "sha512-CyRf3FCDhMCu/WGg3QaHmMVMbRVFwSevw1b1LZYs77pHYWX5HMlMvxnELUMU8H3AqIjDwFYcWfKWYaZSuhjn5w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFOMKaOa082MIZuAOkjgoaT7VJsf7uAx+aFnMNFZ5DPzAiEAifTBVPK6CSXxZtB9GusNQaNBalc/EDqZ1nlQkPNO3ts="}]}}, "1.0.2": {"name": "tar-stream", "version": "1.0.2", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "repository": {"type": "git", "url": "git://github.com:mafintosh/tar-stream.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 0.8.0"}, "dependencies": {"bl": "^0.9.0", "end-of-stream": "^1.0.0", "readable-stream": "^1.0.27-1", "xtend": "^4.0.0"}, "devDependencies": {"concat-stream": "^1.4.6", "tape": "^2.14.0"}, "scripts": {"test": "tape test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "main": "index.js", "files": ["*.js", "LICENSE"], "directories": {"test": "test"}, "license": "MIT", "gitHead": "97ad73faca5f3957cbdb314dcce420e72000caa2", "_id": "tar-stream@1.0.2", "_shasum": "fd19b4a17900fa704f6a133e3045aead0562ab95", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "fd19b4a17900fa704f6a133e3045aead0562ab95", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-1.0.2.tgz", "integrity": "sha512-rQQftEc/Xv3NidhGkCIaagEozVBIQbphu5WzvTukUw6aYJadRZ04QPEIzOASMJ4r//7XGRi0mtX8m2u7BqxrMw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHcCCg3IUeysxfBxt9NqCRx3u9C5SSNXsbNmJMa4r28OAiBpuDqwGkN8MGBNJy4dyn6/SjsWgMRl4E0KpwBo7vGlJg=="}]}}, "1.1.1": {"name": "tar-stream", "version": "1.1.1", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "repository": {"type": "git", "url": "git://github.com:mafintosh/tar-stream.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 0.8.0"}, "dependencies": {"bl": "^0.9.0", "end-of-stream": "^1.0.0", "readable-stream": "^1.0.27-1", "xtend": "^4.0.0"}, "devDependencies": {"concat-stream": "^1.4.6", "tape": "^2.14.0"}, "scripts": {"test": "tape test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "main": "index.js", "files": ["*.js", "LICENSE"], "directories": {"test": "test"}, "license": "MIT", "gitHead": "5d6f693f952df77259c6f427994f91083ae645bf", "_id": "tar-stream@1.1.1", "_shasum": "7a9324331ebb74c5b5721f1177086e58a1c6a9cc", "_from": ".", "_npmVersion": "2.1.8", "_nodeVersion": "0.10.33", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "7a9324331ebb74c5b5721f1177086e58a1c6a9cc", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-1.1.1.tgz", "integrity": "sha512-qPzzIsTtn8Dd0wfBH4hK+s0QKC9Ca+8ZpUJT3UeVXMy6kkArCsUD3iHg1vqmf658zzJkVkMrRr3cUmnGAiENEg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCcwIPZXWgt633GKAgeN7zjmzLkYUoK0F2qKi+Vylax2gIgL9VbpWe9S7GLjV30NFrzUN671nUGxn4K2lRj3E6Uxdk="}]}}, "1.1.2": {"name": "tar-stream", "version": "1.1.2", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "repository": {"type": "git", "url": "git://github.com:mafintosh/tar-stream.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 0.8.0"}, "dependencies": {"bl": "^0.9.0", "end-of-stream": "^1.0.0", "readable-stream": "^1.0.33", "xtend": "^4.0.0"}, "devDependencies": {"concat-stream": "^1.4.6", "tape": "^3.0.3"}, "scripts": {"test": "tape test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "main": "index.js", "files": ["*.js", "LICENSE"], "directories": {"test": "test"}, "license": "MIT", "gitHead": "d1b85db2af5ad57591bc255739ace5c6ad513e25", "_id": "tar-stream@1.1.2", "_shasum": "14652d7bdb5a557ef58e55ccee68a4d642963d6e", "_from": ".", "_npmVersion": "2.1.17", "_nodeVersion": "0.10.35", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "14652d7bdb5a557ef58e55ccee68a4d642963d6e", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-1.1.2.tgz", "integrity": "sha512-Hwq2afKC11h85gl3xoVHokqVVQyR2Y2+kEKEjukcRyTrcjCT/5zGMMcU5LjqP+HxEWiCrf6CAMhxzp6FeQLJ/w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCaDWm0CKfXDkSBiHNtBRSDjIpkGVWQDMzdCNrfCsWpHgIgGKrUu8ebCHNtCt7bwcMg2LnQC6zehd4PmQeUSrj75/Q="}]}}, "1.1.3": {"name": "tar-stream", "version": "1.1.3", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "repository": {"type": "git", "url": "git://github.com:mafintosh/tar-stream.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 0.8.0"}, "dependencies": {"bl": "^0.9.0", "end-of-stream": "^1.0.0", "readable-stream": "~1.0.33", "xtend": "^4.0.0"}, "devDependencies": {"concat-stream": "^1.4.6", "tape": "^3.0.3"}, "scripts": {"test": "tape test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "main": "index.js", "files": ["*.js", "LICENSE"], "directories": {"test": "test"}, "license": "MIT", "gitHead": "05d3af9d1b9ac4df2b4483aad1eef0205519bc6c", "_id": "tar-stream@1.1.3", "_shasum": "e6f15e92d0a49187fda8d7e6e69cd8878aaee7cc", "_from": ".", "_npmVersion": "2.7.5", "_nodeVersion": "1.6.4", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e6f15e92d0a49187fda8d7e6e69cd8878aaee7cc", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-1.1.3.tgz", "integrity": "sha512-eQikGT4a9CVve8yNY9FY9tTedi0F8p5qJfzKFmcsyBYr8L58Hy0af8qqQMZK/uxnqjlPtMi8fX8mrK7RhSGvEQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAJTY3L6Whgf3zr0D8j19d33XEesb+T467ivb3BXK0MVAiEAhP4ytZgQlFlNnpfPpyw884mRJFqfeIDV7iZa0x/PrQ0="}]}}, "1.1.4": {"name": "tar-stream", "version": "1.1.4", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "repository": {"type": "git", "url": "git://github.com:mafintosh/tar-stream.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 0.8.0"}, "dependencies": {"bl": "^0.9.0", "end-of-stream": "^1.0.0", "readable-stream": "~1.0.33", "xtend": "^4.0.0"}, "devDependencies": {"concat-stream": "^1.4.6", "tape": "^3.0.3"}, "scripts": {"test": "tape test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "main": "index.js", "files": ["*.js", "LICENSE"], "directories": {"test": "test"}, "license": "MIT", "gitHead": "5fe70a962c8d4000921f04d304fe2d85adb3b0f7", "_id": "tar-stream@1.1.4", "_shasum": "e6c3ffc4305f7d537b6ec697823dd86e4d61ca63", "_from": ".", "_npmVersion": "2.7.5", "_nodeVersion": "1.6.4", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e6c3ffc4305f7d537b6ec697823dd86e4d61ca63", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-1.1.4.tgz", "integrity": "sha512-eE2JMYggURToocH+5QZq1n9ajNDENz16W44aQJAh6oGm/xlvy8JH07REIfYq9OkPRgKXFK7NuVB4Xg8DDFGI/A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCFMzmJg2Kr69c0lLLs31/ZySfHwTufnYk3rHLcV2gX3wIgPfQbvnFrYfRE1PLz6ohgb49ntQ1i2a35HBKpCq8Q4JU="}]}}, "1.1.5": {"name": "tar-stream", "version": "1.1.5", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 0.8.0"}, "dependencies": {"bl": "^0.9.0", "end-of-stream": "^1.0.0", "readable-stream": "~1.0.33", "xtend": "^4.0.0"}, "devDependencies": {"concat-stream": "^1.4.6", "tape": "^3.0.3"}, "scripts": {"test": "tape test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "main": "index.js", "files": ["*.js", "LICENSE"], "directories": {"test": "test"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/mafintosh/tar-stream.git"}, "gitHead": "72e8736c83455192fdcc20c0252d9df9f83350df", "_id": "tar-stream@1.1.5", "_shasum": "be9218c130c20029e107b0f967fb23de0579d13c", "_from": ".", "_npmVersion": "2.9.0", "_nodeVersion": "2.0.0", "_npmUser": {"name": "maxogden", "email": "<EMAIL>"}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "dist": {"shasum": "be9218c130c20029e107b0f967fb23de0579d13c", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-1.1.5.tgz", "integrity": "sha512-Rzjha52eroBb64qzIwG4Z7goRVD/GquOt0M6qHz7IYcvYYsm1v8JjaCCyJXIGu+rcENKvxTuihKP2svtPhMJKw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDTOW7CZ6C6zoJH1qtz6qv+5v4wprXR1Dzi3tilp81n9AiEA7rcqdJ9ZepkCYwMjsrc6efRnMpsCrYXsnaVleMRx0dM="}]}}, "1.2.0": {"name": "tar-stream", "version": "1.2.0", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 0.8.0"}, "dependencies": {"bl": "^0.9.0", "end-of-stream": "^1.0.0", "readable-stream": "^2.0.0", "xtend": "^4.0.0"}, "devDependencies": {"concat-stream": "^1.4.6", "tape": "^3.0.3"}, "scripts": {"test": "tape test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "main": "index.js", "files": ["*.js", "LICENSE"], "directories": {"test": "test"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/mafintosh/tar-stream.git"}, "gitHead": "1092f7a61687f1eaac5b3f31fd602c40fbaf239b", "_id": "tar-stream@1.2.0", "_shasum": "76d1220d462afd41e8cf14cda439b797b46ed5c1", "_from": ".", "_npmVersion": "2.11.0", "_nodeVersion": "2.2.1", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "76d1220d462afd41e8cf14cda439b797b46ed5c1", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-1.2.0.tgz", "integrity": "sha512-9UwTthNMWRqQfVDinYxBsOBSqP6Gb6RdQt9RHWSCeg7QnbZ6IIot/PXfLImyXUQaJ8XOOhAtYEDfu6OlTKAsOA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF3AGiy4hTVprvbCfho/j7m49zLgMg+DFLxL97KYhRaRAiADovOr7MXi8uLaumP2gEi8YiZiAZrP4FOYzZP6xsrtiA=="}]}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}]}, "1.2.1": {"name": "tar-stream", "version": "1.2.1", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 0.8.0"}, "dependencies": {"bl": "^1.0.0", "end-of-stream": "^1.0.0", "readable-stream": "^2.0.0", "xtend": "^4.0.0"}, "devDependencies": {"concat-stream": "^1.4.6", "tape": "^3.0.3"}, "scripts": {"test": "tape test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "main": "index.js", "files": ["*.js", "LICENSE"], "directories": {"test": "test"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/mafintosh/tar-stream.git"}, "gitHead": "02bbe131f2c7df3fa1f44fecdf96d6241d0cf412", "_id": "tar-stream@1.2.1", "_shasum": "7b09e93b93f02bce74f060d5f2146ac7cccf6021", "_from": ".", "_npmVersion": "2.11.0", "_nodeVersion": "2.2.1", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "7b09e93b93f02bce74f060d5f2146ac7cccf6021", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-1.2.1.tgz", "integrity": "sha512-9j2rQ2q3nZbzQnTkd6yZxi8itECkBWZAp3BWTOjrVOqDCbbzD4Cuc9wlNypau83mgb2OZdZe19aG0TcdCRCECA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDVqFt1ZqM6fMI8GrICYXyb235dQfU3T0gzeZAqvtX0iQIhAKdEaJi+taLclISvPw88WtI53x0xH2csMczCcWhosCzI"}]}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}]}, "1.2.2": {"name": "tar-stream", "version": "1.2.2", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 0.8.0"}, "dependencies": {"bl": "^1.0.0", "end-of-stream": "^1.0.0", "readable-stream": "^2.0.0", "xtend": "^4.0.0"}, "devDependencies": {"concat-stream": "^1.4.6", "tape": "^3.0.3"}, "scripts": {"test": "tape test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "main": "index.js", "files": ["*.js", "LICENSE"], "directories": {"test": "test"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/mafintosh/tar-stream.git"}, "gitHead": "afd4441700640230446d99604c1162dcc4395190", "_id": "tar-stream@1.2.2", "_shasum": "9632f23d98fd33d41661bbdec05489120dec6028", "_from": ".", "_npmVersion": "2.14.4", "_nodeVersion": "4.1.1", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "9632f23d98fd33d41661bbdec05489120dec6028", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-1.2.2.tgz", "integrity": "sha512-G/fd3GmoueafSvxxTw8G33YVT0Bdo3V295R944XNvmqYfTQ8nxfdXeu8jTgpDrOqR397x+2mt01p9crBQmEb2Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBaGWUlSqnr29HF0gb3mBt7YVRUESEiR4djeA5q6t1YJAiEAk+Mpr7DHS61pZ4j4G79UH/046gJRY4uZYSRUn4FQOWA="}]}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}]}, "1.3.0": {"name": "tar-stream", "version": "1.3.0", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 0.8.0"}, "dependencies": {"bl": "^1.0.0", "end-of-stream": "^1.0.0", "readable-stream": "^2.0.0", "xtend": "^4.0.0"}, "devDependencies": {"concat-stream": "^1.4.6", "tape": "^3.0.3"}, "scripts": {"test": "tape test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "main": "index.js", "files": ["*.js", "LICENSE"], "directories": {"test": "test"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/mafintosh/tar-stream.git"}, "gitHead": "788b6e94285f709ff8ee62ce42c47f6b04b35521", "_id": "tar-stream@1.3.0", "_shasum": "7626cc66149f1b1a7c88b0eae3af62c9e45ec8ee", "_from": ".", "_npmVersion": "2.14.4", "_nodeVersion": "4.1.1", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "7626cc66149f1b1a7c88b0eae3af62c9e45ec8ee", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-1.3.0.tgz", "integrity": "sha512-nzU5Cvqk+0lxigM9845P8MR+CmtO44fauoLRzLPD1RM2vd22JrAGBd96NO6w2DtycUfk8kj06/oWmLJ0/WGKyQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDyxd+vLstUvqXnPiq0jaYCYOYrkeFs2YbJfBd4FyAwzgIgJcDUl1PAT5eiBX+dlOwlBv/GjoqNqT66r6gwgDPkIzk="}]}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}]}, "1.3.1": {"name": "tar-stream", "version": "1.3.1", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 0.8.0"}, "dependencies": {"bl": "^1.0.0", "end-of-stream": "^1.0.0", "readable-stream": "^2.0.0", "xtend": "^4.0.0"}, "devDependencies": {"concat-stream": "^1.4.6", "standard": "^5.3.1", "tape": "^3.0.3"}, "scripts": {"test": "standard && tape test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "main": "index.js", "files": ["*.js", "LICENSE"], "directories": {"test": "test"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/mafintosh/tar-stream.git"}, "gitHead": "8e3b1749a322937238236ad05a3ec1122f32ed22", "_id": "tar-stream@1.3.1", "_shasum": "490ec2ad1ec5823fce63f18bb904c7469cd70897", "_from": ".", "_npmVersion": "2.14.4", "_nodeVersion": "4.1.1", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "490ec2ad1ec5823fce63f18bb904c7469cd70897", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-1.3.1.tgz", "integrity": "sha512-aSDEP/6YVWMOByk8dMWAlO2aIQ2VIh0UyKQcaK26s9h9AhmkwTuE1y5BbErV2N412i3Dm1FFVr5qTby7rHa9Tg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDDcE5y6iDssAoLkEUWzZIuOtgueOCUHwqVJD7fRrx3zgIhAOr0CJVCdYUoyuC6KYumC2KJBqUYi3Bb22aWNgu2xY8Z"}]}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}]}, "1.3.2": {"name": "tar-stream", "version": "1.3.2", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 0.8.0"}, "dependencies": {"bl": "^1.0.0", "end-of-stream": "^1.0.0", "readable-stream": "^2.0.0", "xtend": "^4.0.0"}, "devDependencies": {"concat-stream": "^1.4.6", "standard": "^5.3.1", "tape": "^3.0.3"}, "scripts": {"test": "standard && tape test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "main": "index.js", "files": ["*.js", "LICENSE"], "directories": {"test": "test"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/mafintosh/tar-stream.git"}, "gitHead": "2332d878a61de8a0d6b739502eb3d11deb955b01", "_id": "tar-stream@1.3.2", "_shasum": "724d1ab4801c9b3149cdea765fe8c90ea71f6606", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.3", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "724d1ab4801c9b3149cdea765fe8c90ea71f6606", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-1.3.2.tgz", "integrity": "sha512-PfKfRGsvI7I2xDqnu/v7kJcKnOCRJgdWGbZTdbEfoJK4sSenLpYAOuu6BeyPBKevPuw0zCNU7fGWM+Uky7eloQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCegpr9uZLu5Ujjy4NFZg8mmE0F2MvpGq1cSjyjwNbpDwIgNj0u4SdcItuxGd9JqKtEExpb2t4MQ8Mzchtoxnx7c0U="}]}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-5-east.internal.npmjs.com", "tmp": "tmp/tar-stream-1.3.2.tgz_1455706289617_0.9983606818132102"}}, "1.4.0": {"name": "tar-stream", "version": "1.4.0", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 0.8.0"}, "dependencies": {"bl": "^1.0.0", "end-of-stream": "^1.0.0", "readable-stream": "^2.0.0", "xtend": "^4.0.0"}, "devDependencies": {"concat-stream": "^1.4.6", "standard": "^5.3.1", "tape": "^3.0.3"}, "scripts": {"test": "standard && tape test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "main": "index.js", "files": ["*.js", "LICENSE"], "directories": {"test": "test"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/mafintosh/tar-stream.git"}, "gitHead": "7ce8960772190c8f13a5f49a7fee2f231cdb9235", "_id": "tar-stream@1.4.0", "_shasum": "19f7fbc0868b3ec283fa87df4ca906d4b16854f2", "_from": ".", "_npmVersion": "2.14.20", "_nodeVersion": "4.4.1", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "19f7fbc0868b3ec283fa87df4ca906d4b16854f2", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-1.4.0.tgz", "integrity": "sha512-v1l4mW+qvotJi9RSUb+mDS8+Xi8qZW1hLbigyVHPgo8HkWm5BtSbq+8yAk8x/SDDdox6wVqC2WPvxBqnbNIruw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF2UAzlm+M6EuGyVpFWCws2XHIOq2728/hL0T5saZ7svAiEA9Qwden9SkTg+/WgtUpBpwKSyV5NPaFPjbqy/Mo2YPoI="}]}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/tar-stream-1.4.0.tgz_1459791293246_0.602965455269441"}}, "1.5.0": {"name": "tar-stream", "version": "1.5.0", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 0.8.0"}, "dependencies": {"bl": "^1.0.0", "end-of-stream": "^1.0.0", "readable-stream": "^2.0.0", "xtend": "^4.0.0"}, "devDependencies": {"concat-stream": "^1.4.6", "standard": "^5.3.1", "tape": "^3.0.3"}, "scripts": {"test": "standard && tape test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "main": "index.js", "files": ["*.js", "LICENSE"], "directories": {"test": "test"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/mafintosh/tar-stream.git"}, "gitHead": "17a6500850bab799f0ed6fc03237098b4acbe7de", "_id": "tar-stream@1.5.0", "_shasum": "6ad0dba3af49463d713ea2c0ab6cefdae5236a0c", "_from": ".", "_npmVersion": "2.14.20", "_nodeVersion": "4.4.1", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "6ad0dba3af49463d713ea2c0ab6cefdae5236a0c", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-1.5.0.tgz", "integrity": "sha512-dg6kd8dmGKmvDr6vPk0N2+3YEXcvaDjUvp5D+qNEPz/XVdB46Wq5I0EAkqVCW77Y51VzyUk1sGNkTyXve09K1w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDC3smkrGW5vBEa3cq7GPlgPJC4iOn6Z/iJHQGHnv2XTwIgXjxUfBOBO6fvq8q/XTYK6iDu9ZrU6O/IN011OyKhHKs="}]}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/tar-stream-1.5.0.tgz_1459887022859_0.016089207958430052"}}, "1.5.1": {"name": "tar-stream", "version": "1.5.1", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 0.8.0"}, "dependencies": {"bl": "^1.0.0", "end-of-stream": "^1.0.0", "readable-stream": "^2.0.0", "xtend": "^4.0.0"}, "devDependencies": {"concat-stream": "^1.4.6", "standard": "^5.3.1", "tape": "^3.0.3"}, "scripts": {"test": "standard && tape test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "main": "index.js", "files": ["*.js", "LICENSE"], "directories": {"test": "test"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/mafintosh/tar-stream.git"}, "gitHead": "71439bf2df547900b4e098916042ddf294c11b17", "_id": "tar-stream@1.5.1", "_shasum": "516c74d1bea3e131cc0b9348929c9a83f0a2ad11", "_from": ".", "_npmVersion": "2.14.20", "_nodeVersion": "4.4.1", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "516c74d1bea3e131cc0b9348929c9a83f0a2ad11", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-1.5.1.tgz", "integrity": "sha512-S+oMrPcS84i2q+seVI4s6BY9shfY3vCKHPoeu4kBHWb1ufL3UKHgO324hYjaXt8T8m+ArdUowiI5OX14+7JKCg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDiBDRMv/jsb3KEYNHAlCGw5lt420dO6VKqYBoUWQ2/xAIgX/H+Tn+lnJiOT1X4ElqVy6kHnWDIg9LnbIeZuT7N0Bk="}]}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/tar-stream-1.5.1.tgz_1459970127346_0.5329633220098913"}}, "1.5.2": {"name": "tar-stream", "version": "1.5.2", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 0.8.0"}, "dependencies": {"bl": "^1.0.0", "end-of-stream": "^1.0.0", "readable-stream": "^2.0.0", "xtend": "^4.0.0"}, "devDependencies": {"concat-stream": "^1.4.6", "standard": "^5.3.1", "tape": "^3.0.3"}, "scripts": {"test": "standard && tape test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "main": "index.js", "files": ["*.js", "LICENSE"], "directories": {"test": "test"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/mafintosh/tar-stream.git"}, "gitHead": "7c279d66989a3bdde45f1eb661edaa846540d984", "_id": "tar-stream@1.5.2", "_shasum": "fbc6c6e83c1a19d4cb48c7d96171fc248effc7bf", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.3", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "fbc6c6e83c1a19d4cb48c7d96171fc248effc7bf", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-1.5.2.tgz", "integrity": "sha512-X2iZpARyDjlkj6Tz3nlI1lY4a4k+xEatPgQg7O2WiUMTXIrjVp8R86K3AdWfHp+Q3jsaLE2FLlHES+PA5zwAhA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBigjrP8nNfYcNrGoescweDKLt8jCFBxdYh0bf5aLTRbAiAkiBfWPaDKyt+oqrK64fArw5M5DKdT53bSo7YC3Nz3pA=="}]}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/tar-stream-1.5.2.tgz_1461071501210_0.40823886124417186"}}, "1.5.3": {"name": "tar-stream", "version": "1.5.3", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 0.8.0"}, "dependencies": {"bl": "^1.0.0", "end-of-stream": "^1.0.0", "readable-stream": "^2.0.0", "xtend": "^4.0.0"}, "devDependencies": {"concat-stream": "^1.4.6", "standard": "^5.3.1", "tape": "^3.0.3"}, "scripts": {"test": "standard && tape test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "main": "index.js", "files": ["*.js", "LICENSE"], "directories": {"test": "test"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/mafintosh/tar-stream.git"}, "gitHead": "f4a2cf1e16901de6c18847cce10c5ead56b89b8c", "_id": "tar-stream@1.5.3", "_shasum": "62707bc5fd18c3cdb9b8765e9ea60e26b6f6c109", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.9.5", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "62707bc5fd18c3cdb9b8765e9ea60e26b6f6c109", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-1.5.3.tgz", "integrity": "sha512-35jbTluPa2ucMkQMkWf4ySPXnYr1prfxyhixIkqhKt8gv80Z3shxqoytd11UyIr5pzeAvHYxcBtrRTgN4/5taw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCgqz+FpUt0ikOxLSZTRry2/eQkD/QZdIxd7LZPE1LoMwIhAMkjSYhggfzlzNfAgTLO00h1y25x8hHmh49s5nvYquGd"}]}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/tar-stream-1.5.3.tgz_1494508339541_0.7545076759997755"}}, "1.5.4": {"name": "tar-stream", "version": "1.5.4", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 0.8.0"}, "dependencies": {"bl": "^1.0.0", "end-of-stream": "^1.0.0", "readable-stream": "^2.0.0", "xtend": "^4.0.0"}, "devDependencies": {"concat-stream": "^1.4.6", "standard": "^5.3.1", "tape": "^3.0.3"}, "scripts": {"test": "standard && tape test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "main": "index.js", "files": ["*.js", "LICENSE"], "directories": {"test": "test"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/mafintosh/tar-stream.git"}, "gitHead": "c5f214402dea8071a7a7d51c0c5ad4eee883eb40", "_id": "tar-stream@1.5.4", "_shasum": "36549cf04ed1aee9b2a30c0143252238daf94016", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.9.5", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "36549cf04ed1aee9b2a30c0143252238daf94016", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-1.5.4.tgz", "integrity": "sha512-P5OxsOVWKagZkHOJo6KVdGPBVyB+JGxXi0vT1M6j2ED2nsQqWcsNOktFZENdOQK9dh2/zooCQjV8YicIlEc+pg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDyObw4Fv5vWJ4u7lMB7JHJtbQ2JNUf8auMfXGyPz03rgIgAQy4R8ahm7ruez5Ouni6q92TPPZk+eTUCoNtoklWJT8="}]}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/tar-stream-1.5.4.tgz_1494517059169_0.4700461607426405"}}, "1.5.5": {"name": "tar-stream", "version": "1.5.5", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 0.8.0"}, "dependencies": {"bl": "^1.0.0", "end-of-stream": "^1.0.0", "readable-stream": "^2.0.0", "xtend": "^4.0.0"}, "devDependencies": {"concat-stream": "^1.4.6", "standard": "^5.3.1", "tape": "^3.0.3"}, "scripts": {"test": "standard && tape test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "main": "index.js", "files": ["*.js", "LICENSE"], "directories": {"test": "test"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/mafintosh/tar-stream.git"}, "gitHead": "209ceac59b8b38ae4ad760b524dc8509f51349ef", "_id": "tar-stream@1.5.5", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-mQdgLPc/Vjfr3VWqWbfxW8yQNiJCbAZ+Gf6GDu1Cy0bdb33ofyiNGBtAY96jHFhDuivCwgW1H9DgTON+INiXgg==", "shasum": "5cad84779f45c83b1f2508d96b09d88c7218af55", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-1.5.5.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFMf5O4+0IRfALmTI5XoM9T6ySqr2/kb+zqfSEGhfXqIAiEAzQtGUn7mq8va6sz4LOXh03x5Z6TO4FbHBBtubUeRQyw="}]}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-stream-1.5.5.tgz_1510572680855_0.9013987586367875"}}, "1.5.6": {"name": "tar-stream", "version": "1.5.6", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 0.8.0"}, "dependencies": {"bl": "^1.0.0", "buffer-alloc": "^1.1.0", "end-of-stream": "^1.0.0", "readable-stream": "^2.0.0", "to-buffer": "^1.1.0", "xtend": "^4.0.0"}, "devDependencies": {"concat-stream": "^1.4.6", "standard": "^5.3.1", "tape": "^3.0.3"}, "scripts": {"test": "standard && tape test/extract.js test/pack.js", "test-all": "standard && tape test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "main": "index.js", "files": ["*.js", "LICENSE"], "directories": {"test": "test"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/mafintosh/tar-stream.git"}, "gitHead": "b6dd099d17f558959eea92bff3ffb53e03f3ae46", "_id": "tar-stream@1.5.6", "_npmVersion": "5.6.0", "_nodeVersion": "9.11.1", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-tFG9xPbc4Y7CubEwriTss87tdcBQDsw81ejJyCbT4ALNYkNsdPqCfCD6Gkg3OpRkUkq6VO7qpNfwoQAuk/aeNQ==", "shasum": "b0d86269e04bf286ceb59215a499b026e1ab52c8", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-1.5.6.tgz", "fileCount": 7, "unpackedSize": 26470, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa4bnaCRA9TVsSAnZWagAArlYQAIzNg8RgJSWdrJPnQf0z\nENZ67w+nhvlzXbYGv41q5I8UrJNuHPbmizs5nUQtcJ4Vp/N1KOXrmtCLKGB3\nm8m5r1V9O3dqljfAsvrOQz/6F/8bBhi/2oNSle/zS2U059Dhc0FxjUImgqnS\nShiWV/4Dko3RG5PxrNJpmByl9t6CPJ1CoIMbmbSmklMDUqRlW4KS4KiDCBLn\nhpHGh/qRtS83zuSaOYRmvQt3j/H6f5OS8uYp/GAsCLxXVv5mXRYSmAaSNDzM\n+CiH70jPxVwxV97LCCLwv6wn+jU6KsNl1jPet/tMsI/bPf9VZ3OBnSVvb9yd\n9TZw9j5BjDA/0UnLy2RfUq2QW5nrdu69ycnCFi6uZ3XQNe1cnjabt+049S2k\nrnwFk0yls+7UMHKPIq5IyBnmadwyeCX7QLLlZ1i+1tk25CzhO7wmsTh7qoG7\nRT7QebbTtHUdFVDtFOZ51TMMhuUDcQRTc75q4qnrJTAMHQmboUR8TiGi0vIV\nVHLN1aMH/nUHYN9kCMflcX4fgI1zNSDnhF7MUJ9pKvD1kF7k2kbT22mMm5fP\ndFgc7RscGBBU/DtYjK5UiAefKOe3IjXdGuuPY9dyVzJ+ufAJJSH0Apjwivu7\noppXbGQ/7AcxrSPVpVCcSeXLm7Cr1CyAClDbWv7K5JiSKTkf1KHy14Jgptd5\naEBT\r\n=7Ywt\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEC8AcI4C22BRh47BS0zpS/9IqjlGL8kY0DS++ggY0NmAiEAk7VC7Rym8tYacjbJcXnSAqtybCyP8OGyOz5GDeALtCU="}]}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-stream_1.5.6_1524742616773_0.38943661981076083"}, "_hasShrinkwrap": false}, "1.5.7": {"name": "tar-stream", "version": "1.5.7", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 0.8.0"}, "dependencies": {"bl": "^1.0.0", "buffer-alloc": "^1.1.0", "end-of-stream": "^1.0.0", "fs-constants": "^1.0.0", "readable-stream": "^2.0.0", "to-buffer": "^1.1.0", "xtend": "^4.0.0"}, "devDependencies": {"concat-stream": "^1.6.2", "standard": "^11.0.1", "tape": "^4.9.0"}, "scripts": {"test": "standard && tape test/extract.js test/pack.js", "test-all": "standard && tape test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "main": "index.js", "files": ["*.js", "LICENSE"], "directories": {"test": "test"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/mafintosh/tar-stream.git"}, "gitHead": "c0b13f7329d0459310d75e4dacf16aa9e960b7b5", "_id": "tar-stream@1.5.7", "_npmVersion": "5.6.0", "_nodeVersion": "9.11.1", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-euy<PERSON>myeJpl6wjWGnzPfZRQ1IGLNbqEDJWuYyue9sixDY2vfqVBRiIqZLWYhWG5da031WF4qEN9D9Ek9lZHiH1Q==", "shasum": "7f54380f49019231a8c5ddffbd1da882d2f66057", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-1.5.7.tgz", "fileCount": 7, "unpackedSize": 26503, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa4ejWCRA9TVsSAnZWagAAuRoP/3emhaHLCDeBeV+6aFE3\nePMQzs1mPCOxcDeiZqcMAKybCcyAv+mfJlbdz6jdTYfexQev5yTGfLcF5E8y\nrghetoGltu+PYrPpeL74JqOx8ECHWFVkLd5rwALaTGtUNJtN+pOnZ+GSoxc1\nwgKZ7gdhG6GHFlbef3WIqZcfo/nM2UD/v+IXHhILpHgLmMsbJVMXOgnEZOz4\necl4nBPOIrwx0PvVspIjqf9EmMI4b2nSMUwRCv53hzSz8L8vaS9E3k+jz1yt\nNXZOgO2kwqybWPnE7+SCL7g9et6F6v12dqtzpeVzS0I7e8P+Z+UMtpfsJdW7\nyJ9uU9/zbTUQ1paQOFowBqg5FvciEGPUjZvSqDaEDg62yIHF0Ylf/vs20Aum\ngIhVvI2E4JqDjzcmgtUndvHadueffO68tA/xYGc+Xp3FXewJzX0jaKU1q0nH\nAIgr9RyuyfVhdZDxEErGPFHKzLSLvD2MBV/savCM4vZy+2Yyt+nygdHkU28i\nk5kc5aoJ1qUU4KrSru9mlzKpvcNxgRrLTvKtYJkSTqRqW/LtXBY5RLO1KbhY\ncy+OPNPZAsryDKD6LrE+KYzmtuwRDvxCGi+kmQywQrG2Fmw0JCGp7yR17v1s\ny9uZPSWAkS+HaRfYiBDxxZ2MtoWOkKxdsF7+DV5sURWxKCO+6P+17S9fMBzE\n1pX1\r\n=RJed\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDFfps+lqyKL8BXvXXgN1QjCmLepWFz2zJy9GNgbC1dHwIgdecAIGrZ1bMoAA7Tx5UA1k/grK+m4isv65Yj3c1X7bg="}]}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-stream_1.5.7_1524754645379_0.1452860403660743"}, "_hasShrinkwrap": false}, "1.6.0": {"name": "tar-stream", "version": "1.6.0", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 0.8.0"}, "dependencies": {"bl": "^1.0.0", "buffer-alloc": "^1.1.0", "end-of-stream": "^1.0.0", "fs-constants": "^1.0.0", "readable-stream": "^2.0.0", "to-buffer": "^1.1.0", "xtend": "^4.0.0"}, "devDependencies": {"concat-stream": "^1.6.2", "standard": "^11.0.1", "tape": "^4.9.0"}, "scripts": {"test": "standard && tape test/extract.js test/pack.js", "test-all": "standard && tape test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "main": "index.js", "files": ["*.js", "LICENSE"], "directories": {"test": "test"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/mafintosh/tar-stream.git"}, "gitHead": "59abdc85c0eb1406c845d4bdba973ebf95c009ca", "_id": "tar-stream@1.6.0", "_npmVersion": "5.6.0", "_nodeVersion": "9.11.1", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-lh2iAPG/BHNmN6WB9Ybdynk9rEJ5GD/dy4zscHmVlwa1dq2tpE+BH78i5vjYwYVWEaOXGBjzxr89aVACF17Cpw==", "shasum": "a50efaa7b17760b82c27b3cae4a301a8254a5715", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-1.6.0.tgz", "fileCount": 7, "unpackedSize": 26782, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa40meCRA9TVsSAnZWagAARWcP/3Zmn4pquiN2OxaYJTOp\nvxFrQhbJuU4qMJ7vRTuagpXpCvKkA9k3iHE4+J2Yo2WczVEItvyAc14XE4J9\n+nmxXw3TDU4ZXvl1q5IiqVf7o4u02tTwWD1QbduKkZHsLQLe70q9ty7lj5VI\n7PgiSxjtIR3lEjLQBt5SwUYYoE4TWsCosQ53AdjGsdmQrjay4CLNZNfk0fW+\ntiVLI5D7Je+PKSzlit2gets6VZm0xOh8JoRhyPY/tfppMqg/8t+k0SlqAdg+\nVvf6rFvBhTfqEQBiAdhpDk36ZrJXzx+yLigmm+mm5flagBiJm69lqw3lKlfT\n2nOV7uJZw5UwbglgHw5blw2jWeYO9TChBtDkEp2u+3OYkm6XpFGCPd9Es8uW\nlWZyGBM+KBLCcLTsT+Twjk8EJQvwrZ/vjgA5W2vfMdn6vXUlf1ILa0Q4PU+k\nWlGV2eQLlSDQg3gX2aFcXySbmMeMCul5paRGhewZZoiCL0E8XdQxrnsTJY4Q\nzHE/F6zVLx0d+E31Cq2bjRSNFv7/W/mIv1Xre0cTqcASTRed4D7qnaDDBVgB\neueTZvRodYL9K1MGh2pSUahCLQTrTWyWz5HFFKTIOZJRb6iQT9JMHkSSCY77\n79Ff/n4dXR/fIu/MCP5DQVCMKdHiztRiwjrvgE/1/Hdho1LZEF6W04eeBB6Z\nxShw\r\n=rFBi\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDbFk3UPGFGMHML9ubn3KCdLtxdBPtgEq/DHN1NaI3OzwIgYJ7zz/Nb3yU2emg4oubuxYM+lpbR3LeKhfwVUDbJsgM="}]}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-stream_1.6.0_1524844957036_0.1675912968248512"}, "_hasShrinkwrap": false}, "1.6.1": {"name": "tar-stream", "version": "1.6.1", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 0.8.0"}, "dependencies": {"bl": "^1.0.0", "buffer-alloc": "^1.1.0", "end-of-stream": "^1.0.0", "fs-constants": "^1.0.0", "readable-stream": "^2.3.0", "to-buffer": "^1.1.0", "xtend": "^4.0.0"}, "devDependencies": {"concat-stream": "^1.6.2", "standard": "^11.0.1", "tape": "^4.9.0"}, "scripts": {"test": "standard && tape test/extract.js test/pack.js", "test-all": "standard && tape test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "main": "index.js", "files": ["*.js", "LICENSE"], "directories": {"test": "test"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/mafintosh/tar-stream.git"}, "gitHead": "9f9ccbc127ffd599e6219af94e8cfe2dd1ca823a", "_id": "tar-stream@1.6.1", "_npmVersion": "5.6.0", "_nodeVersion": "9.11.1", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-IFLM5wp3QrJODQFPm6/to3LJZrONdBY/otxcvDIQzu217zKye6yVR3hhi9lAjrC2Z+m/j5oDxMPb1qcd8cIvpA==", "shasum": "f84ef1696269d6223ca48f6e1eeede3f7e81f395", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-1.6.1.tgz", "fileCount": 7, "unpackedSize": 26782, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+f1JCRA9TVsSAnZWagAAnWkP/jHPHuyNZ/H3BaXHWUQ7\nIZ3ukXdSYH+ZGSgM6/JcRz91xFiejwnCuX8tjwgjjpeHn7wpM4/taINZge3I\n13Xc9uF+zabqnO542jH4pCjrztE8hovGM1IDoZtwi6H4uinuLkYY27WsTGCO\ncSC74aUDU9LZx7hrIfnvq5PYhm4X3rEV0EcSmG9I+lusha2GU6BKw2CFQpAO\nvERga3WM4tFOSKFagu1gvReU/77Lyw376M1eAghMV2ZjUV/CyVMyzzvC4S2u\n1k/Lq81XcODc2mNl9M8HNuQMaPusqT4XzJnjzL4kVUYiwhdr7Lncq29dufDZ\n7TS1HZgWJQezFg+pnlspTgpkLrS5uDk6JncCM/wbgpyGP1q8lv/LKYbvk1D4\nvBpTzK7Qay7h8/Re7liu0zJXnO1cXwHauaO+WQXkpPBFGOoDvPugbyq6E8OV\nMtzm6QMrvmfe9hkgndII3Oj08ga6DKP9DF/nDxNZ9UPGkriczhVYsHJO4Ujp\nmH+UxqC18uruZzJlUju6VXsV4ufEBraaZ8KkGBjnV376054tnkOJIDTwjk1M\nbfdABGfeMXHJsM9+exUVnGEhIKeSszxiYpACiQFgUNpP1hDFU3aUchOYEUc0\nXamSbx7md5wqCVN0l6q+1YADEzJoWpkGBWojWZZY30jw5PJvuT0e4/VsKxrg\noD/a\r\n=qby5\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBvz3E5D3C9fgPxssE4Dl+lDC70Gsm8gK7eGIiXkOgxsAiEA49walr+wdvzvQml7UNggvahocpCJNlI1E0M5x6sJV6g="}]}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-stream_1.6.1_1526332743849_0.4437324256027526"}, "_hasShrinkwrap": false}, "1.6.2": {"name": "tar-stream", "version": "1.6.2", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">= 0.8.0"}, "dependencies": {"bl": "^1.0.0", "buffer-alloc": "^1.2.0", "end-of-stream": "^1.0.0", "fs-constants": "^1.0.0", "readable-stream": "^2.3.0", "to-buffer": "^1.1.1", "xtend": "^4.0.0"}, "devDependencies": {"concat-stream": "^1.6.2", "standard": "^11.0.1", "tape": "^4.9.0"}, "scripts": {"test": "standard && tape test/extract.js test/pack.js", "test-all": "standard && tape test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "main": "index.js", "directories": {"test": "test"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/mafintosh/tar-stream.git"}, "gitHead": "a9927c7c989dc5ee2f16d1d9e27a1c028c9de708", "_id": "tar-stream@1.6.2", "_npmVersion": "6.2.0", "_nodeVersion": "10.11.0", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-rzS0heiNf8Xn7/mpdSVVSMAWAoy9bfb1WOTYC78Z0UQKeKa/CWS8FOq0lKGNa8DWKAn9gxjCvMLYc5PGXYlK2A==", "shasum": "8ea55dab37972253d9a9af90fdcd559ae435c555", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-1.6.2.tgz", "fileCount": 7, "unpackedSize": 26782, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbpjdBCRA9TVsSAnZWagAA6qMP/2NLa3eMJBbvkSu6C+bD\n6dCiNPs5Y01QcivkSgtlTHBH2MT/kdOpUUV0S8Rgn++sIwNXKsPp+QJ+YPmW\n8Gb8PY35GwA/FHeMJ6hXKxcov96somIJ+lTsHvxeZxqB08fGvsGYMgTCqNfX\nVw8HA5cBC80t50cYWXKv8yVBqVBwJcIxEqJMdokx3ABbgJXvJM5zQiFGH+0m\n1QkNFUcOhAWGcUfZ144rMp/Pl5zvb94sGr2295FDlQDK4PUzh1FrE4HapF6Y\nRXBqV4TklnlWvHFHjI5+F8ziXBNKBa18fDWvHz9qICD92Ml2+vgkjFdGcqsB\nhRFjDfJmGEQOpkloMR60HPbXijLJNrnC1PG6TnM5z6fOLZukeojwAQ1EbbNm\nUO8EwoMIJ9dbbdycOLSGkEGZGh2GImMRzyFHwhnUg3qELkxG1nyQehB6+UBC\ntTlXQIDPKAjW4fsl2NBEDU1m814cxOqb0XTh+jmy2JbZBqX+qSIe3kFEdcNo\n8N5WP6BfkHK57T09N2QZkn1PIK6ldsjLiWhNZpuBeYwpBzfLkixA4mYPzDV0\nI3fVKHdSZUn5HKbpSRM+YKe9XIY0fORUZpZBjfg/eOxXxDfgXTYxls2GBZMi\nThTAxj40vKKE0W/qsSLP0GRSgDcnL0hHr+lM9JIJtZfvh698ehAgs3zGWMs3\nEAaa\r\n=Lqwy\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC6swtsD7TKUXT8Y4aO70l1opH95Ebjv52OUEpC2aYflQIhAN3tnGeoNEBB+IPrD9Kwht8aw/rWY1UvwZid+aeUNjru"}]}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-stream_1.6.2_1537619777167_0.6014045653605695"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "tar-stream", "version": "2.0.0", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "dependencies": {"bl": "^2.2.0", "end-of-stream": "^1.4.1", "fs-constants": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^3.1.1"}, "devDependencies": {"concat-stream": "^2.0.0", "standard": "^12.0.1", "tape": "^4.9.2"}, "scripts": {"test": "standard && tape test/extract.js test/pack.js", "test-all": "standard && tape test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "main": "index.js", "directories": {"test": "test"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/mafintosh/tar-stream.git"}, "gitHead": "9b5d814fa740ccb149f68773f122e7eee55cda29", "_id": "tar-stream@2.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.1", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-n2vtsWshZOVr/SY4KtslPoUlyNh06I2SGgAOCZmquCEjlbV/LjY2CY80rDtdQRHFOYXNlgBDo6Fr3ww2CWPOtA==", "shasum": "8829bbf83067bc0288a9089db49c56be395b6aea", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-2.0.0.tgz", "fileCount": 7, "unpackedSize": 26568, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcWAo/CRA9TVsSAnZWagAAiYcQAJVGHHslbALub1j2ArUD\nNe9H79/3mldQ4JAlhyJ+qp3BJg5bPIHg9hsRhsfN6FKdFWdV1MuyS+VAcgrT\naVDo7a8zSYCQ3Y+Xf3NtIXvclkFED1ZFJ1QlddK/NF8smUnoY2Ogwlbs7Y5o\n7vkgozTruHX5Qhu59kNVyaLETi+P51uIKfEHFJP41TDO4+V3Nne0N97NSjWs\nPJE+xfR/dZlHOA3HFhr8wvSGikvZkL1E8yOJ+MDfPy/uTqsKUgGUz4CiYwgZ\nnmco63ScvHoM6cy1yXoat2XfXXr4gv1+eaTVpEiCj0/k5d+KD2Bbe84IDQKD\nxd8CC2Na9xg4Obqk/mIdnV/rUiGN4qpjMraAFaDrV+L0259NccoYRgNaNnFW\nZnT/vb5DfGmF5G7lcgF43b0Rut56YagCNQnyQyhrTSvXXLwkPWbSffCbNQ0k\nUawbpz7S+abHwf74GSODXazy9M4fAT3iwDQEBnNA4LGQyGt5yDgLO2Zj2j9r\n4nKAjS60zoB0/f45x3vupJy0QSrvg817xizktcon/lyp3p6/m9tolIf5zlhs\niR7sErOJxCE9UAX0Ku+iHjdhH3gbh1mFNT8MMs2A8E6OVYo+P/CshjfiBmFS\n38Qr21/2lV/2/INUvk+vBXzb8Rt2i/GuxpjH9mL+2BW2Bw9XrT3haEwrLXKR\nHRI4\r\n=H6Ka\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFt2YfULoDw6m6DXn9vOs/FM5RQ4tGgTFXZb0NbXlaqbAiEAoXWoiyL9U8o8DHsPYz5OGTBpGotts4Znyt6MgRUuDQk="}]}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-stream_2.0.0_1549273662664_0.7972210682300505"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "tar-stream", "version": "2.0.1", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "dependencies": {"bl": "^3.0.0", "end-of-stream": "^1.4.1", "fs-constants": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^3.1.1"}, "devDependencies": {"concat-stream": "^2.0.0", "standard": "^12.0.1", "tape": "^4.9.2"}, "scripts": {"test": "standard && tape test/extract.js test/pack.js", "test-all": "standard && tape test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "main": "index.js", "directories": {"test": "test"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/mafintosh/tar-stream.git"}, "gitHead": "b6392bdc8a3c28288a33508f152cfb98eb0c776f", "_id": "tar-stream@2.0.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.1", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-I6OJF7wE62BC6zNPdHDtseK0D0187PBjbKSLYY4ffvVkBM6tyBn2O9plDvVM2229/mozfEL/X3++qSvYYQE2xw==", "shasum": "42fbe41cd1cc5e6657c813e7d98e7afca2858a8c", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-2.0.1.tgz", "fileCount": 7, "unpackedSize": 26568, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcd8KPCRA9TVsSAnZWagAAOVAQAIO6JdBtaB+WHMWiTJao\n3/6+Qivpawir7MHOCyHhOMDcLHOjay0DMIN8bztTk/d9vqrsPBpzQNdxdTg/\nXYZZWB+OY69aiBbX/WX3yQt366G1ykYNYAlALZBrxK6qPUofFNpcFOiFoH3L\nFLqTaNfyirW3mPDmiZudFD6+E98r6Xe8+U75P1j52qCbWPbIKAh58ArljHFS\nyXk6RUQqKp7qQyCNwXhVvCaERBwBGTdlKdZlnIycun9NmiUBeZVPyJfzTVcK\nzpY93u/X6WyqKJ85mXS4kaSWmd/GLJgF94OFaH973NW4/9J1KPG2uYFZZ8OW\ngTCbO8nUG19NaYbWApoZ5B2N+CKo0ApyC5aZfYD40jr6Chfeylh3ofXVY/OV\nUIPuJ9p4Jl/ODtWoZ4+AQsaSpKYCw+mnRLCjXzBQJoNI9aKgljht8AoZlJhB\noGIGoi49fkEFECW/5KCxZaCKWSCDlBZAe4O9TmyMPK/MDIylPIgR3iEo5H2h\nQSNPJXGRSX97mu2CAGnfhRbgawgIK2A9ZYqdwKIuPMdC59s3h0ZLXaIXSqY6\nfxgSUUdWZ1htlV/Oev/TqOUrTmDyYY4eykRo8PaCk2NDNlJBhjEI/1gDMZLA\nbkOz3cMnAzcLvx0MWSf5Jkpk9NVPPfKddE/iykA/Cs0GC4LBDjw1861/SMAX\n8oDl\r\n=2yYj\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBQoVQdBCV6sCNoCIFs80tIk9Q6pyvkb9DG9bDJIoa9cAiBtxt+7Rz36lVkGbAmvYJ5er76kwtrpOB0DgHIm7Ty5kw=="}]}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-stream_2.0.1_1551352463108_0.836535531557645"}, "_hasShrinkwrap": false}, "2.1.0": {"name": "tar-stream", "version": "2.1.0", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "dependencies": {"bl": "^3.0.0", "end-of-stream": "^1.4.1", "fs-constants": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^3.1.1"}, "devDependencies": {"concat-stream": "^2.0.0", "standard": "^12.0.1", "tape": "^4.9.2"}, "scripts": {"test": "standard && tape test/extract.js test/pack.js", "test-all": "standard && tape test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "main": "index.js", "directories": {"test": "test"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/mafintosh/tar-stream.git"}, "gitHead": "9f10b5fc261cb600bd3c8e3c8d66ff7837e594a2", "_id": "tar-stream@2.1.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.1", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-+DAn4Nb4+gz6WZigRzKEZl1QuJVOLtAwwF+WUxy1fJ6X63CaGaUAxJRD2KEn1OMfcbCjySTYpNC6WmfQoIEOdw==", "shasum": "d1aaa3661f05b38b5acc9b7020efdca5179a2cc3", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-2.1.0.tgz", "fileCount": 7, "unpackedSize": 27320, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc9sR2CRA9TVsSAnZWagAAgsgP/ii3l92+LRzyiQxrBeTX\n92A65nTNrZpbAEZL5qB24fFCUy8Fps4PQq/NyYujLc0BvHG+xZ+EHBnP2vrm\nwn/I/xQHsuCCcp9Lxr6UZovbxNozkSA9TKeYuQxPuvXxf0l1rGF1i8q0jqet\nUHRg9T5Qv3KNmrs2HOm2iQKO3k4tqzLJh/YDtD3+ma61G5eCUnBCF4opxAHd\n0dqihSRz7jsTRTKSFWNDHdHEcGV/OvFTvw1oE9l4oLJBCUnVHvQcai5xgdk6\nFmeTqWdwe9cwZ2pbb1pf3QlzsOEUC9doVosnsU0OiZ7qW+UOEdOtOELYjzsi\nBAS4hAC8IV1XwV+IeGBbpczRjFYBrBLukRESotkn74UkNT+miKghhQ3yT73y\nLI080JNFiXuqfRP5pz1Ihu0Pfv4aRFjBWo2UlbuUuX+UBQto4NJG2eU4swAH\nnGhZPMfweOlvKX2LCFGibDNdJBzzBDHr2Y3gIrH/57jFoCzNlMouDiwhhq64\nARc3314/s8jhTONi36sb9yUqW9OSEY1V5BNeSx8lHJhfWSCvbp2/yVaExrcJ\nd4lB0yneYoyUggflYkvpCgfCfQjNce54cS2vLRxcLADkh69nWsM6HB/Q78JZ\nLiiLiXzlu6vdI22+hebLZgGCO2lvxeQJYdhuFsn8U8mLfkuhw0DM/Y6sQNoj\nx0uS\r\n=evjK\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG024xG29iK1r57k4+ycKUdSIutMGnDtld65HoBGLLXVAiADhqzHie4AAwVmYAPOQq7HQZXpf5oTeq1c5mQYR6s1Og=="}]}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-stream_2.1.0_1559676021154_0.27249664943694163"}, "_hasShrinkwrap": false}, "2.1.1": {"name": "tar-stream", "version": "2.1.1", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "dependencies": {"bl": "^4.0.1", "end-of-stream": "^1.4.1", "fs-constants": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^3.1.1"}, "devDependencies": {"concat-stream": "^2.0.0", "standard": "^12.0.1", "tape": "^4.9.2"}, "scripts": {"test": "standard && tape test/extract.js test/pack.js", "test-all": "standard && tape test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "main": "index.js", "directories": {"test": "test"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/mafintosh/tar-stream.git"}, "gitHead": "52458371725b68f0c69d8a054d24a39880b6a627", "_id": "tar-stream@2.1.1", "_nodeVersion": "13.9.0", "_npmVersion": "6.13.7", "dist": {"integrity": "sha512-GZjLk64XcE/58qwIc1ZfXGqTSE4OutPMEkfBE/oh9eJ4x1eMRjYkgrLrav7PzddpvIpSJSGi8FgNNYXdB9Vumg==", "shasum": "e188b1dd74b940b9efc4a832a9a2dc957845937c", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-2.1.1.tgz", "fileCount": 7, "unpackedSize": 27181, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeYMvACRA9TVsSAnZWagAANMYP/1RkiRNHAxyV6JJEGLgq\nZhKIkUceMmcgw5jMWuicNhBJmkSOt8N+1frBV8e+MdMW/gN/vMtz3+2i+ATR\nd04nPz4ElUQK5QUZcPYVndmkFN9UpDv+IPHxGVLaWucF86F7q/I+B+mQb8l6\nKoAnws+pejvmqTIy+CowiKMk3q/M/9xDAmwv5nrfBlzWg7E/5vD+Upmsc3PF\ny9KMEoFBnL7/8y5RNHamirB9q2CPiB4FJL4VJQMj2R9xBnIqB7l2SrLhOWn0\nVjErHS2iCVqf8wvjES96S4GZGlT8fvqHzu1+4tuWPfQ9RYMUKq1D89TQKJHN\nF1ImgFGHrtGMFklAX0q7k6YHmXnr2i1NwBN2uT5C3fWzVmZ8QLAwtYubArsW\n8YmhHVFI5aZSBy8NZ6b+Ghnv00PdapYyK0qDEdrqLdtkf7EdOjzDwbcYwdnn\neTH+n6FKRWRbZfvPNGKsV52EVib41vZWovInCNce4U6yYKHwGy0SPCYkEnFB\nTQudq9CTlaqxpq37WqivOjFMdFudFmLQ+GrEH8dJZWUcOu3bRfvM/PHBTWs6\nGuD2PvTjcpRmnkgb26AoaIHIw0PFgxNjcB5DTAKWp+f/qt4dlu7fIU8jiQRT\niwFN6NCeq27YXpg463/OMlt2dnZS7iMjwNJddyTzKWXmA17TpEejqQw/sggv\nag2v\r\n=29O+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDGqXUnL1RuU6OdYBImG5W5YmOuHIURObWKZULPPej9rAiA3Mv8BKlJqMVQeUmTBm51rULdduAXbTKF68RT9NwIc/A=="}]}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-stream_2.1.1_1583401919813_0.5811462005648347"}, "_hasShrinkwrap": false}, "2.1.2": {"name": "tar-stream", "version": "2.1.2", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "dependencies": {"bl": "^4.0.1", "end-of-stream": "^1.4.1", "fs-constants": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^3.1.1"}, "devDependencies": {"concat-stream": "^2.0.0", "standard": "^12.0.1", "tape": "^4.9.2"}, "scripts": {"test": "standard && tape test/extract.js test/pack.js", "test-all": "standard && tape test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "main": "index.js", "directories": {"test": "test"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/mafintosh/tar-stream.git"}, "gitHead": "f639c6ea853bfd44eaf95e01f39309bfd07f4ba8", "_id": "tar-stream@2.1.2", "_nodeVersion": "13.9.0", "_npmVersion": "6.13.7", "dist": {"integrity": "sha512-UaF6FoJ32WqALZGOIAApXx+OdxhekNMChu6axLJR85zMMjXKWFGjbIRe+J6P4UnRGg9rAwWvbTT0oI7hD/Un7Q==", "shasum": "6d5ef1a7e5783a95ff70b69b97455a5968dc1325", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-2.1.2.tgz", "fileCount": 7, "unpackedSize": 27230, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeZ1PNCRA9TVsSAnZWagAAeEYP/iDOW4fAkYEb8NgWszwA\nxwUiXi3qXa/Ik8Ude7NrAbPL9QrYXa+OhFkuRMh7shMyGZYUZh3r3lw3p52Z\nU5iyDHriBaNwmI0olk7XVS7AMeccInlgobb5NGe4SPcBwl5rMC88EKwb6eSF\nNU9Du9zDAULRlHy8TSmAkKVnflYR60febmCccWUK9kR2aB4oWwU4+Wed6+Ah\nyVf4lj8dQkY9jdfP1XwztvcSfckheY/AWmVE3jHPSMT3foAgfn/P9AylB1kc\n4wWgZQTiakRo6Is592cne0eiD5w/GfR0HVhlu9SSCB5XZ2WzTqz6bC2jNVot\nZE/WRxpNOZw1+jUGS0E6l91YMBXfTaJBlhEf/rPnn+0/e2VyQL0NI1eEuWYI\nZUJ3H8WSt2QNturzSS8Xe8+QLOjjelCJrJFY167fGoj2TVHr5Q6QUStGRHcX\nUUca4KCKX/Vjq3TvTwQsRNDuAsIJ6oFMoZ1+kth0kIk2G+qmX+UUPCDkNRe/\nsLjJQjxKAc9i86FUnYSSN8oP3TyniF52yWUbVlC4cG5uOLD56DZe/6zq0Jhn\ns5K8Y99bJFZ6SWgu7XZnUZRGCCKNGOV3xNHOsAqy0CFIwPb78UwdB2Rnf9vI\nBXuw8lOCsoz8Zxx7hf3BYZ9Yj+gX1E9Dmq74pecUjXTJK+CrtA5K6ai8Dznq\nmNwT\r\n=vVte\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD6onzYNO07hMUkdJ5ezoBC+PXYWovctCZqJZDYAiaX8wIhAOG2h2Hj/l1Uze9dmpaCltnswQTBLuLTlTKt8QcnJ7hL"}]}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-stream_2.1.2_1583829965410_0.3279636950703184"}, "_hasShrinkwrap": false}, "2.1.3": {"name": "tar-stream", "version": "2.1.3", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "dependencies": {"bl": "^4.0.1", "end-of-stream": "^1.4.1", "fs-constants": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^3.1.1"}, "devDependencies": {"concat-stream": "^2.0.0", "standard": "^12.0.1", "tape": "^4.9.2"}, "scripts": {"test": "standard && tape test/extract.js test/pack.js", "test-all": "standard && tape test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "main": "index.js", "directories": {"test": "test"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/mafintosh/tar-stream.git"}, "engines": {"node": ">=6"}, "gitHead": "b44f5937f949cf5dd07e3abdf2f449bed2c034dc", "_id": "tar-stream@2.1.3", "_nodeVersion": "14.4.0", "_npmVersion": "6.14.5", "dist": {"integrity": "sha512-Z9yri56Dih8IaK8gncVPx4Wqt86NDmQTSh49XLZgjWpGZL9GK9HKParS2scqHCC4w6X9Gh2jwaU45V47XTKwVA==", "shasum": "1e2022559221b7866161660f118255e20fa79e41", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-2.1.3.tgz", "fileCount": 8, "unpackedSize": 27587, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfBFQpCRA9TVsSAnZWagAAFvYP/2uPSKgiPO7bzDM3iFPn\nu4gmF9CvY561KRt2fVR/a3jn+R0adF1Dp8Lg5f55gxwVMtK4D1VdIVsahOls\n3b9g5fiMNm8/jHugxUH5xK6RvB5YuONpk1y8lpdHqyTu/nrLIm6F4nFcWpPI\n5UGvH9PKUYX4h5ekzCi8M8HPAZQHRbbwIGK3iiTFOzlrUK0ODZYseNKI3jok\nNIipHSy0+Lt/L+IcHNRDm6b7tdFwov03Qf+Nmr/LJiku+EwAtfEFqHo5Uci/\nQYVG/TFesybGL0tqDzeq/m++WwQRKclFR32ytuOjZ/gVvpBL0uOBK5/yq4dh\nGb/c01Nus7HJm82zLu+ICX7sZOS8U+veXmXLDEvDPP2p6cp5e+DS2Kq8/opY\n3tzINIJ4LFtV7lDFXDDhj7GfsQ71RIlF3EnTfR7I0gN24smLBAqXE+RxHrr2\noYLcODkTXSGpNDCJrejJF61eyQyt5USjKqtytDB7RJgMbtrYajTOP17uDo6N\nfKCxIE1CxOCeyoT2IDEylhyJSNus2S0kB2KIwVApS/JKeccQDm67SbdLM83p\nRYKvkL5BKuX5vw8ECQ9kHVDs7FsTqpDfzNjBZP3oRvdwMZZ6PNHyuVWPeAhn\nWz5V2TeuXL64B8sZJLfZ+hPoRKxbpM+/w1hKD+LNh6W/iQbDz34ahipET09f\nRDib\r\n=Hlnd\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEkBBkf7KLkrMxJbYLUmspHJDvvbilq7kQ5NXdlpPXfsAiBd7fBMSZu+aLz4GwxHbZ1BVuuZZhnV90ugPIufaLx/ZQ=="}]}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-stream_2.1.3_1594119209171_0.2551925880157919"}, "_hasShrinkwrap": false}, "2.1.4": {"name": "tar-stream", "version": "2.1.4", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "dependencies": {"bl": "^4.0.3", "end-of-stream": "^1.4.1", "fs-constants": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^3.1.1"}, "devDependencies": {"concat-stream": "^2.0.0", "standard": "^12.0.1", "tape": "^4.9.2"}, "scripts": {"test": "standard && tape test/extract.js test/pack.js", "test-all": "standard && tape test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "main": "index.js", "directories": {"test": "test"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/mafintosh/tar-stream.git"}, "engines": {"node": ">=6"}, "gitHead": "03edcabc53bc34c030216659c0ebdf0256c8941e", "_id": "tar-stream@2.1.4", "_nodeVersion": "12.17.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-o3pS2zlG4gxr67GmFYBLlq+dM8gyRGUOvsrHclSkvtVtQbjV0s/+ZE8OpICbaj8clrX3tjeHngYGP7rweaBnuw==", "shasum": "c4fb1a11eb0da29b893a5b25476397ba2d053bfa", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-2.1.4.tgz", "fileCount": 8, "unpackedSize": 27587, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfWiaQCRA9TVsSAnZWagAAZ0YQAJsFiSsKCHggzJgNEQIZ\nM/gp9Bc+gSI9s/pQlka9XdGZzhbavIOUAK8oB/7espJU6hehCpVnENGwPAWU\nGJp0yE4gosGVnlO0sZpvZqJmwFTly0gLRRVxqimcIjIMdM7tE4sI2QRfvQRU\nqG/+xn5ZX8+lDg9+EkKrtElz2jHCLL4sgzIAsTGeTGdHejGU+zygooSINVdh\nXufT4iOkIAhSzV33GD1Cov/siWSLn/S9ZPTJWVfIOV+hy76CMFiQ3gfJs9xG\nb+A01Mi+DrP7bwon3dGwNlRJkSNpFDsYLOBVa9MNAtiyTt35EdPZG58NYgli\nGIOie/ptdLsr8AKZc8IqMaKfXt/TWZspGJOySd0Mc4mnf7dVVZTNK2Gw9h0C\n5G5HO4php2yDZ1IKYm9BMIWfzcRZhTZWXQumV0ZTPNWM6bHtmAtu0rSv66mU\nlGpxdtIARK5ZaR8DTByDrsiQSSStEGNx+RSsui4wxHzq++giLfIHQosGnDfw\nwSft4/HHL/plO1F3ksmso1DVRS7Um+D4vAOGZ749/+MlA4F7cXwe3N12d6Go\n6VsIPpHqzpfOhZiQg8xVcn454Tk+6UOeg0XuYQ+J4iTUV9V8HX3a46tBjT07\nrED42HXelpBWLOSm8n+Ds7Uep6RJnck+BjI4EG8DI7wvhjVHbfh5ofHX5dmf\nJoQY\r\n=avLJ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBIc1WDBnsdjeGRrEW6jwPtsjfnG0B1md/VdnHMXBdjkAiEA+jEjkYulreFgINZEtIgBRqrQmjohFs7BHkvcwDAAFVc="}]}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-stream_2.1.4_1599743631934_0.3487702700338906"}, "_hasShrinkwrap": false}, "2.2.0": {"name": "tar-stream", "version": "2.2.0", "description": "tar-stream is a streaming tar parser and generator and nothing else. It is streams2 and operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "dependencies": {"bl": "^4.0.3", "end-of-stream": "^1.4.1", "fs-constants": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^3.1.1"}, "devDependencies": {"concat-stream": "^2.0.0", "standard": "^12.0.1", "tape": "^4.9.2"}, "scripts": {"test": "standard && tape test/extract.js test/pack.js", "test-all": "standard && tape test/*.js"}, "keywords": ["tar", "tarball", "parse", "parser", "generate", "generator", "stream", "stream2", "streams", "streams2", "streaming", "pack", "extract", "modify"], "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "main": "index.js", "directories": {"test": "test"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/mafintosh/tar-stream.git"}, "engines": {"node": ">=6"}, "gitHead": "e08fcd845744c43e1e71343f117be9993208103a", "_id": "tar-stream@2.2.0", "_nodeVersion": "14.15.1", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-ujeqbceABgwMZxEJnk2HDY2DlnUZ+9oEcb1KzTVfYHio0UE6dG71n60d8D2I4qNvleWrrXpmjpt7vZeF1LnMZQ==", "shasum": "acad84c284136b060dc3faa64474aa9aebd77287", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-2.2.0.tgz", "fileCount": 8, "unpackedSize": 27671, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf6wOBCRA9TVsSAnZWagAAYUMP/i/xeTHUpQ106W/eJAKz\neFf1qDIPOxxAQmlFiv1+UAH3QEatLRO0XS8pHIieec5A09sjUwRz9plB8r/E\n0/Z+XCphQbNBJH5cuj17UcuQmwd04c6ScOG2SmKxJM1tHs8FxsUlHN6SOoRe\n4a0TC5o4UivXKGPr9HUFditBKq2Bx1Hyg0rpvNWaK6AAqTk7Avm/SJnffzbc\n2j4seXGhGICVnLaJNmtZjU2fPuWulF6RVfK+LyLJNB863FZmesoTTnOkoyY4\nMDV9F6eSBp+lcfkLk8tdOpjupZBAaA8FVMgbOhJaue8tJiYjuYuxbQlynZRl\nzEiIYBQ64L/5SoCNZmLEDgkiYqGzZGoLH2+2lONmbqLRD23LTpol6P7GFuum\nfE2ILIvbSSxLszEm1R00SwEq320yRd925g7N8cgPyWmaTr6aPOLoHNdL7Ndf\nA7vnPD7WMwOux/E+mgVR+ClzHczVdjowIL96gUl5COryLiOsEtyLjhVqwZHU\nupLYLjEiYXj5yl2K8pIrxFABbIrMFO9ovFbhK5f5+l7T34SuPMzXibPORf8r\nL108s4O0jkLYgS6En83kBBx2E+aH6nSrLaEXb+iLhslJVOAdcgxXcH8fqeBY\n/TAkdbOer0xqPvhAboQFFHIGBgy0nMBgSzYmscPimyR5Y9p9PahTSPpbxOMv\nKEvH\r\n=MReE\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFkuYhSj3Ua/dBK7kw0MOxUnksBoF/iiGwb8f4ZNq3HKAiA5UgyZZpbWBSquCBytmo6gGQLcgUwccm0/EWKjhPrNgQ=="}]}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-stream_2.2.0_1609237377288_0.8579540534693273"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "tar-stream", "version": "3.0.0", "description": "tar-stream is a streaming tar parser and generator and nothing else. It operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "main": "index.js", "scripts": {"test": "standard && brittle test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/tar-stream.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "dependencies": {"b4a": "^1.6.1", "bl": "^6.0.0", "streamx": "^2.12.5"}, "devDependencies": {"brittle": "^3.1.1", "concat-stream": "^2.0.0", "standard": "^17.0.0"}, "gitHead": "ca0e270f11dc61f507f7972e53071b8fa99e66bf", "_id": "tar-stream@3.0.0", "_nodeVersion": "16.17.0", "_npmVersion": "8.15.0", "dist": {"integrity": "sha512-O6OfUKBbQOqAhh6owTWmA730J/yZCYcpmZ1DBj2YX51ZQrt7d7NgzrR+CnO9wP6nt/viWZW2XeXLavX3/ZEbEg==", "shasum": "43de9f0e8d1bccc0036dbe2731260ca7668406b6", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-3.0.0.tgz", "fileCount": 7, "unpackedSize": 25889, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEowVrIvI26bXP73jj7FipWKYNErEfRUoMfFDFgujg+cAiBTvKOVBROLpxYzSpTvI2z+qBuVzwRA0L47TOX5CT1BWQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjli4lACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrLwhAAoAhfRE92L5f4vq0FquU7edWLBjjjff6FqXkD2xtiZozUuuAD\r\n0Bv9UzTe/pIQN0bVZcx+YHXi777xhMMmrGa7LH+Dd74AUO0odOX9DdwNELnX\r\nTSCW9NByiPS9WXGVIp09675vVUapBb/nY5eTSjXOTuH8aB0buGkzQpQRnhOi\r\nFdGB3HIo+VniwkK2ETctm1ibdUYdwpb4cqy2p6sKBwBw/BpP81s3VZXW3yk6\r\nlHCU5AHVqri7d9/hJndxSkcgsqUTKfO4cn91tJcGpfmLFhxGEpTKWAVGLP7l\r\ndzLuo7UdJ9GotfnXIaZmoVdwrIdnrOREq1W6sQMg+8zO1a4wjnWRYLTbNXur\r\nkzpL1RKgoiVjdYve4zdy3zFIGGxJ5LZ1xskv/ajfdUosSOotuTOJzsACcSlf\r\n3m7mugvm/KGtiqkQ2nB2MLq0qG4tjk0Fk7LIAbvOkQZmuiTDeVbvEgbs5vCe\r\nLXEybym9bUEbcc2H/otxbGpoSSZKuJ91uJuyx513tBsHy5g7+URw+SU6L3O9\r\nmo8cGUwmp4QDLftMxz7HeBX8BsKfUNZh+8sOtJqCXb1KnY1OgjASzGk0FT94\r\nZkyGnpa4d3n5OmKN+7fhQSxKItoiDhq3XNnIEgpkUtTfcP2Kv9NCjNgKe53F\r\nrpW9fjEgEAQs1IrFNt5nRPlV2ZL2HnHnG4Q=\r\n=/fq0\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-stream_3.0.0_1670786596950_0.5897474495349386"}, "_hasShrinkwrap": false}, "3.1.0": {"name": "tar-stream", "version": "3.1.0", "description": "tar-stream is a streaming tar parser and generator and nothing else. It operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "main": "index.js", "browser": {"fs": false}, "scripts": {"test": "standard && brittle test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/tar-stream.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "dependencies": {"b4a": "^1.6.4", "streamx": "^2.15.0"}, "devDependencies": {"brittle": "^3.3.2", "concat-stream": "^2.0.0", "standard": "^17.0.1"}, "gitHead": "2a1b1e9d999028f76abcaf860572589c7a815122", "_id": "tar-stream@3.1.0", "_nodeVersion": "18.16.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-vErRPRZkU5MLOAuHhZzsgRfWoRBx8oJH4slT+JKM5alLD5uKhLpZeS2Ck9hkE5JFywrKncNoiI8Gt0inkyC0Sw==", "shasum": "7c08339b5bc2f79e6055b350c3143328002bbad8", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-3.1.0.tgz", "fileCount": 8, "unpackedSize": 31582, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCHzlyLMUNtPnykE7RfH4BFavocKu7P5VR4tqklSxyX7AIhAKdCuQuH/QwBSOAFZMw1lE9st/BpzLqP9/4suBRANj+U"}]}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-stream_3.1.0_1687021969492_0.027490617744034607"}, "_hasShrinkwrap": false}, "3.1.1": {"name": "tar-stream", "version": "3.1.1", "description": "tar-stream is a streaming tar parser and generator and nothing else. It operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "main": "index.js", "browser": {"fs": false}, "scripts": {"test": "standard && brittle test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/tar-stream.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "dependencies": {"b4a": "^1.6.4", "streamx": "^2.15.0"}, "devDependencies": {"brittle": "^3.3.2", "concat-stream": "^2.0.0", "standard": "^17.0.1"}, "gitHead": "6c30681b3a79d6fd666b6554f81a068e73a06723", "_id": "tar-stream@3.1.1", "_nodeVersion": "18.16.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-Y5wIzRqqz0k7UHHfW34YM//w2krbMFDPxOJEg5hnVuilaY6Onqdrw66dLNJFxLoiFSCPXUEeQOHVMQ9Rx8fkKw==", "shasum": "e21eabdddde8b2ba7007026aeac956b239acdf84", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-3.1.1.tgz", "fileCount": 8, "unpackedSize": 31592, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH+Fyb8FRtx0Q7ueVwJPvzhRSBYqM2K7Wd67miv42oHVAiEAhfs1Dk3n72kMDLiEr7CKlf5QCQAWJQSO+6aUM8MALh4="}]}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-stream_3.1.1_1687031278349_0.121376868824149"}, "_hasShrinkwrap": false}, "3.1.2": {"name": "tar-stream", "version": "3.1.2", "description": "tar-stream is a streaming tar parser and generator and nothing else. It operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "main": "index.js", "browser": {"fs": false}, "scripts": {"test": "standard && brittle test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/tar-stream.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "dependencies": {"b4a": "^1.6.4", "streamx": "^2.15.0"}, "devDependencies": {"brittle": "^3.3.2", "concat-stream": "^2.0.0", "standard": "^17.0.1"}, "gitHead": "cd0566896782ccb53cd38943997d5dc3b0de87ed", "_id": "tar-stream@3.1.2", "_nodeVersion": "18.16.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-rEZHMKQop/sTykFtONCcOxyyLA+9hriHJlECxfh4z+Nfew87XGprDLp9RYFCd7yU+z3ePXfHlPbZrzgSLvc16A==", "shasum": "5abe709c3f63de7c25f4645f1e5272efd871ad3f", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-3.1.2.tgz", "fileCount": 8, "unpackedSize": 31620, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDUPPnDps1FIgZriNlJoTeT4VcFCXhxovHLXOrOKCKQzAiBsiABu0Fwwj0u9Wrnb5JhOOs3HBahasRp0SR2vc73LwQ=="}]}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-stream_3.1.2_1687168272104_0.5693951883134158"}, "_hasShrinkwrap": false}, "3.1.3": {"name": "tar-stream", "version": "3.1.3", "description": "tar-stream is a streaming tar parser and generator and nothing else. It operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "main": "index.js", "browser": {"fs": false}, "scripts": {"test": "standard && brittle test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/tar-stream.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "dependencies": {"b4a": "^1.6.4", "streamx": "^2.15.0"}, "devDependencies": {"brittle": "^3.3.2", "concat-stream": "^2.0.0", "standard": "^17.0.1"}, "gitHead": "0490aeda616e666ea5b2bbc8758c8574fdb8d600", "_id": "tar-stream@3.1.3", "_nodeVersion": "18.16.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-oX1ywwb1FyBeLDOV/c9oHPT5y9RcjYkWmmYL5wxcPtKqraL9V0IaeHaX5cRmk5WPVX5z9kmxfsUwiHamIs5CvA==", "shasum": "007239066e95fd3c8c38b3886e46818f1dc07398", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-3.1.3.tgz", "fileCount": 8, "unpackedSize": 31742, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCjtCAE1Hxt5ljVXktVN0ncO/kepkE6NaWEEClyuWyfSAIhAOoptgBGNJCmijWfsUm3OIHHiXgMI241CmvdB4cE9vLe"}]}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-stream_3.1.3_1687258594181_0.251712819042186"}, "_hasShrinkwrap": false}, "3.1.4": {"name": "tar-stream", "version": "3.1.4", "description": "tar-stream is a streaming tar parser and generator and nothing else. It operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "main": "index.js", "browser": {"fs": false}, "scripts": {"test": "standard && brittle test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/tar-stream.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "dependencies": {"b4a": "^1.6.4", "fast-fifo": "^1.2.0", "streamx": "^2.15.0"}, "devDependencies": {"brittle": "^3.3.2", "concat-stream": "^2.0.0", "standard": "^17.0.1"}, "gitHead": "6ed3778c6645f7675956f9b2789206fd35f65488", "_id": "tar-stream@3.1.4", "_nodeVersion": "18.16.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-IlHr7ZOW6XaVBCrSCokUJG4IqUuRcWW76B8XbrtCotbaDh6zVGE7WPCzaSz1CN+acFmWiwoa+cE4RZsom0RzXg==", "shasum": "a06c3cefe558f8a5a6e88ddfbf6471782ad8dd42", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-3.1.4.tgz", "fileCount": 8, "unpackedSize": 31769, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEpZG+CVqP8tUlnVAVzPUlYKkRvJhb8+f+HTfNx+PL2uAiEAraRJV0eXx616pW1csRnjmRFfuHUR2nHriEhuDrKpVJA="}]}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-stream_3.1.4_1687372750123_0.4574033144844418"}, "_hasShrinkwrap": false}, "3.1.5": {"name": "tar-stream", "version": "3.1.5", "description": "tar-stream is a streaming tar parser and generator and nothing else. It operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "main": "index.js", "browser": {"fs": false}, "scripts": {"test": "standard && brittle test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/tar-stream.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "dependencies": {"b4a": "^1.6.4", "fast-fifo": "^1.2.0", "streamx": "^2.15.0"}, "devDependencies": {"brittle": "^3.3.2", "concat-stream": "^2.0.0", "standard": "^17.0.1"}, "gitHead": "93ab386855248506d19a6a8135247b5f40bb5ddc", "_id": "tar-stream@3.1.5", "_nodeVersion": "18.16.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-i+IP13ZyzEOOEkuPrvYokgzW3zcL3rdjmIk0O4veDoegHzySYG4xa2NzKb5pYc/8a8K49iBVMyavS6HT72VMSQ==", "shasum": "cfff83145c4b1db204d4e44b0e975d1da964a49e", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-3.1.5.tgz", "fileCount": 8, "unpackedSize": 31929, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCg+fK38uhJ6IWXK5h79OlLJDQhDVPnWptCFkJv0TnCNgIgDn7n4NsNkb4rWdiF2ZtQU0YZxI1r+hG6xh9OJi4KyGM="}]}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-stream_3.1.5_1688310684891_0.015691909901662804"}, "_hasShrinkwrap": false}, "3.1.6": {"name": "tar-stream", "version": "3.1.6", "description": "tar-stream is a streaming tar parser and generator and nothing else. It operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "main": "index.js", "browser": {"fs": false}, "scripts": {"test": "standard && brittle test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/tar-stream.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "dependencies": {"b4a": "^1.6.4", "fast-fifo": "^1.2.0", "streamx": "^2.15.0"}, "devDependencies": {"brittle": "^3.3.2", "concat-stream": "^2.0.0", "standard": "^17.0.1"}, "gitHead": "03da589543d45a4eeed561b4029e4b538e4fa990", "_id": "tar-stream@3.1.6", "_nodeVersion": "18.16.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-B/UyjYwPpMBv+PaFSWAmtYjwdrlEaZQEhMIBFNC5oEG8lpiW8XjcSdmEaClj28ArfKScKHs2nshz3k2le6crsg==", "shasum": "6520607b55a06f4a2e2e04db360ba7d338cc5bab", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-3.1.6.tgz", "fileCount": 8, "unpackedSize": 31990, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF5sPveVe9ckE3dVj8tmURF77Iveiy5qaTYltZUv8sovAiBR30fkNuYqyQkDsJ4BMNWfMWfQZoNoSTdZCIiL1pKXRw=="}]}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-stream_3.1.6_1688571560467_0.7977111812152637"}, "_hasShrinkwrap": false}, "3.1.7": {"name": "tar-stream", "version": "3.1.7", "description": "tar-stream is a streaming tar parser and generator and nothing else. It operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.", "main": "index.js", "browser": {"fs": false}, "scripts": {"test": "standard && brittle test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/tar-stream.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "homepage": "https://github.com/mafintosh/tar-stream", "dependencies": {"b4a": "^1.6.4", "fast-fifo": "^1.2.0", "streamx": "^2.15.0"}, "devDependencies": {"brittle": "^3.3.2", "concat-stream": "^2.0.0", "standard": "^17.0.1"}, "_id": "tar-stream@3.1.7", "gitHead": "3f1dae3dda207948b58297893e0f54a322d55bbf", "_nodeVersion": "20.10.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-qJj60CXt7IU1Ffyc3NJMjh6EkuCFej46zUqJ4J7pqYlThyd9bO0XBTmcOIhSzZJVWfsLks0+nle/j538YAW9RQ==", "shasum": "24b3fb5eabada19fe7338ed6d26e5f7c482e792b", "tarball": "https://registry.npmjs.org/tar-stream/-/tar-stream-3.1.7.tgz", "fileCount": 8, "unpackedSize": 31996, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDo/sxdamuxQDzjS8MFD68CAGsdLhHClbPmKiO6Bp26AAIgC+vGhK2U2Fzw9mbqP21cEvjfDpRZZEzIFjdla6cJv5k="}]}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar-stream_3.1.7_1705695259947_0.29092683450852097"}, "_hasShrinkwrap": false}}, "readme": "# tar-stream\n\ntar-stream is a streaming tar parser and generator and nothing else. It operates purely using streams which means you can easily extract/parse tarballs without ever hitting the file system.\n\nNote that you still need to gunzip your data if you have a `.tar.gz`. We recommend using [gunzip-maybe](https://github.com/mafintosh/gunzip-maybe) in conjunction with this.\n\n```\nnpm install tar-stream\n```\n\n[![build status](https://secure.travis-ci.org/mafintosh/tar-stream.png)](http://travis-ci.org/mafintosh/tar-stream)\n[![License](https://img.shields.io/badge/license-MIT-blue.svg)](http://opensource.org/licenses/MIT)\n\n## Usage\n\ntar-stream exposes two streams, [pack](https://github.com/mafintosh/tar-stream#packing) which creates tarballs and [extract](https://github.com/mafintosh/tar-stream#extracting) which extracts tarballs. To [modify an existing tarball](https://github.com/mafintosh/tar-stream#modifying-existing-tarballs) use both.\n\n\nIt implementes USTAR with additional support for pax extended headers. It should be compatible with all popular tar distributions out there (gnutar, bsdtar etc)\n\n## Related\n\nIf you want to pack/unpack directories on the file system check out [tar-fs](https://github.com/mafintosh/tar-fs) which provides file system bindings to this module.\n\n## Packing\n\nTo create a pack stream use `tar.pack()` and call `pack.entry(header, [callback])` to add tar entries.\n\n``` js\nconst tar = require('tar-stream')\nconst pack = tar.pack() // pack is a stream\n\n// add a file called my-test.txt with the content \"Hello World!\"\npack.entry({ name: 'my-test.txt' }, 'Hello World!')\n\n// add a file called my-stream-test.txt from a stream\nconst entry = pack.entry({ name: 'my-stream-test.txt', size: 11 }, function(err) {\n  // the stream was added\n  // no more entries\n  pack.finalize()\n})\n\nentry.write('hello')\nentry.write(' ')\nentry.write('world')\nentry.end()\n\n// pipe the pack stream somewhere\npack.pipe(process.stdout)\n```\n\n## Extracting\n\nTo extract a stream use `tar.extract()` and listen for `extract.on('entry', (header, stream, next) )`\n\n``` js\nconst extract = tar.extract()\n\nextract.on('entry', function (header, stream, next) {\n  // header is the tar header\n  // stream is the content body (might be an empty stream)\n  // call next when you are done with this entry\n\n  stream.on('end', function () {\n    next() // ready for next entry\n  })\n\n  stream.resume() // just auto drain the stream\n})\n\nextract.on('finish', function () {\n  // all entries read\n})\n\npack.pipe(extract)\n```\n\nThe tar archive is streamed sequentially, meaning you **must** drain each entry's stream as you get them or else the main extract stream will receive backpressure and stop reading.\n\n## Extracting as an async iterator\n\nThe extraction stream in addition to being a writable stream is also an async iterator\n\n``` js\nconst extract = tar.extract()\n\nsomeStream.pipe(extract)\n\nfor await (const entry of extract) {\n  entry.header // the tar header\n  entry.resume() // the entry is the stream also\n}\n```\n\n## Headers\n\nThe header object using in `entry` should contain the following properties.\nMost of these values can be found by stat'ing a file.\n\n``` js\n{\n  name: 'path/to/this/entry.txt',\n  size: 1314,        // entry size. defaults to 0\n  mode: 0o644,       // entry mode. defaults to to 0o755 for dirs and 0o644 otherwise\n  mtime: new Date(), // last modified date for entry. defaults to now.\n  type: 'file',      // type of entry. defaults to file. can be:\n                     // file | link | symlink | directory | block-device\n                     // character-device | fifo | contiguous-file\n  linkname: 'path',  // linked file name\n  uid: 0,            // uid of entry owner. defaults to 0\n  gid: 0,            // gid of entry owner. defaults to 0\n  uname: 'maf',      // uname of entry owner. defaults to null\n  gname: 'staff',    // gname of entry owner. defaults to null\n  devmajor: 0,       // device major version. defaults to 0\n  devminor: 0        // device minor version. defaults to 0\n}\n```\n\n## Modifying existing tarballs\n\nUsing tar-stream it is easy to rewrite paths / change modes etc in an existing tarball.\n\n``` js\nconst extract = tar.extract()\nconst pack = tar.pack()\nconst path = require('path')\n\nextract.on('entry', function (header, stream, callback) {\n  // let's prefix all names with 'tmp'\n  header.name = path.join('tmp', header.name)\n  // write the new entry to the pack stream\n  stream.pipe(pack.entry(header, callback))\n})\n\nextract.on('finish', function () {\n  // all entries done - lets finalize it\n  pack.finalize()\n})\n\n// pipe the old tarball to the extractor\noldTarballStream.pipe(extract)\n\n// pipe the new tarball the another stream\npack.pipe(newTarballStream)\n```\n\n## Saving tarball to fs\n\n\n``` js\nconst fs = require('fs')\nconst tar = require('tar-stream')\n\nconst pack = tar.pack() // pack is a stream\nconst path = 'YourTarBall.tar'\nconst yourTarball = fs.createWriteStream(path)\n\n// add a file called YourFile.txt with the content \"Hello World!\"\npack.entry({ name: 'YourFile.txt' }, 'Hello World!', function (err) {\n  if (err) throw err\n  pack.finalize()\n})\n\n// pipe the pack stream to your file\npack.pipe(yourTarball)\n\nyourTarball.on('close', function () {\n  console.log(path + ' has been written')\n  fs.stat(path, function(err, stats) {\n    if (err) throw err\n    console.log(stats)\n    console.log('Got file info successfully!')\n  })\n})\n```\n\n## Performance\n\n[See tar-fs for a performance comparison with node-tar](https://github.com/mafintosh/tar-fs/blob/master/README.md#performance)\n\n# License\n\nMIT\n", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "time": {"modified": "2024-01-19T20:14:20.295Z", "created": "2013-12-20T14:21:12.813Z", "0.1.0": "2013-12-20T14:21:12.813Z", "0.1.1": "2013-12-20T19:57:40.873Z", "0.1.2": "2013-12-20T20:02:53.388Z", "0.2.0": "2013-12-21T01:35:45.343Z", "0.2.1": "2013-12-21T02:12:01.498Z", "0.2.2": "2013-12-23T00:43:45.694Z", "0.2.3": "2013-12-23T13:27:25.219Z", "0.2.4": "2014-01-06T08:56:28.147Z", "0.2.5": "2014-01-13T20:37:59.453Z", "0.3.0": "2014-03-24T14:55:20.947Z", "0.3.1": "2014-04-02T21:58:45.362Z", "0.3.2": "2014-04-03T00:31:00.726Z", "0.3.3": "2014-04-11T12:27:14.102Z", "0.4.0": "2014-05-30T11:56:59.914Z", "0.4.1": "2014-05-30T12:22:45.536Z", "0.4.2": "2014-05-30T14:48:16.738Z", "0.4.3": "2014-06-27T07:20:13.464Z", "0.4.4": "2014-06-30T07:59:17.934Z", "0.4.5": "2014-08-19T08:04:24.894Z", "0.4.6": "2014-09-23T13:53:58.603Z", "0.4.7": "2014-09-23T14:29:44.924Z", "1.0.0": "2014-09-24T04:14:50.529Z", "1.0.1": "2014-10-23T06:29:12.854Z", "1.0.2": "2014-10-23T06:33:10.514Z", "1.1.0": "2014-11-18T14:13:21.895Z", "1.1.1": "2014-11-18T14:14:58.747Z", "1.1.2": "2015-01-07T09:19:42.552Z", "1.1.3": "2015-04-07T16:09:41.687Z", "1.1.4": "2015-04-18T19:32:23.941Z", "1.1.5": "2015-05-15T00:03:55.182Z", "1.2.0": "2015-06-16T20:09:29.654Z", "1.2.1": "2015-06-21T22:28:14.672Z", "1.2.2": "2015-10-15T15:54:00.312Z", "1.3.0": "2015-10-28T17:10:59.760Z", "1.3.1": "2015-11-06T23:30:43.887Z", "1.3.2": "2016-02-17T10:51:31.964Z", "1.4.0": "2016-04-04T17:34:55.417Z", "1.5.0": "2016-04-05T20:10:25.147Z", "1.5.1": "2016-04-06T19:15:30.189Z", "1.5.2": "2016-04-19T13:11:42.644Z", "1.5.3": "2017-05-11T13:12:21.158Z", "1.5.4": "2017-05-11T15:37:39.859Z", "1.5.5": "2017-11-13T11:31:21.776Z", "1.5.6": "2018-04-26T11:36:56.873Z", "1.5.7": "2018-04-26T14:57:25.539Z", "1.6.0": "2018-04-27T16:02:37.117Z", "1.6.1": "2018-05-14T21:19:03.931Z", "1.6.2": "2018-09-22T12:36:17.334Z", "2.0.0": "2019-02-04T09:47:42.806Z", "2.0.1": "2019-02-28T11:14:23.257Z", "2.1.0": "2019-06-04T19:20:21.638Z", "2.1.1": "2020-03-05T09:51:59.920Z", "2.1.2": "2020-03-10T08:46:05.562Z", "2.1.3": "2020-07-07T10:53:29.292Z", "2.1.4": "2020-09-10T13:13:52.082Z", "2.2.0": "2020-12-29T10:22:57.508Z", "3.0.0": "2022-12-11T19:23:17.192Z", "3.1.0": "2023-06-17T17:12:49.671Z", "3.1.1": "2023-06-17T19:47:58.571Z", "3.1.2": "2023-06-19T09:51:12.259Z", "3.1.3": "2023-06-20T10:56:34.357Z", "3.1.4": "2023-06-21T18:39:10.385Z", "3.1.5": "2023-07-02T15:11:25.091Z", "3.1.6": "2023-07-05T15:39:20.672Z", "3.1.7": "2024-01-19T20:14:20.100Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/tar-stream.git"}, "homepage": "https://github.com/mafintosh/tar-stream", "bugs": {"url": "https://github.com/mafintosh/tar-stream/issues"}, "readmeFilename": "README.md", "users": {"uwo": true, "shanewholloway": true, "sintaxi": true, "fishrock123": true, "timdream": true, "djblue": true, "ahme-t": true, "emilbay": true, "carvantes": true, "heineiuo": true, "janx": true, "flumpus-dev": true}, "license": "MIT"}