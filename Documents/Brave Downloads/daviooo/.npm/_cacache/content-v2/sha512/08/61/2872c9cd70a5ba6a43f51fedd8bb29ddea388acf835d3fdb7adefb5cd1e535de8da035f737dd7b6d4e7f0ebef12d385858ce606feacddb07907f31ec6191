{"_id": "mime-types", "_rev": "162-432f5f4c0c2c7390e46e6562f3224e03", "name": "mime-types", "dist-tags": {"latest": "2.1.35", "next": "3.0.0"}, "versions": {"0.1.0": {"name": "mime-types", "version": "0.1.0", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "mime-types@0.1.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/expressjs/mime-types", "bugs": {"url": "https://github.com/expressjs/mime-types/issues"}, "dist": {"shasum": "480f66fa19ef6ff039712a5393a4bc4901b5bf40", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-0.1.0.tgz", "integrity": "sha512-pq+MbLrwKOQmBZE0w+la1YxhF/duOl1G009IqhSKqiv5lC8nZOMz8WGs1P8TV1ZCqn3bN2jrpWCLkIs926c8pg==", "signatures": [{"sig": "MEQCIHlqdvJ95y9xbYRNRXoyGTq8XGnkzDrxlOYM5Q1sEKZkAiBDPWcmtj7+3IA9giFMpeMl6w6mctPhFUROiotru7LK6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib", "_from": ".", "scripts": {"test": "make test"}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/expressjs/mime-types", "type": "git"}, "_npmVersion": "1.4.6", "description": "ultimate mime type utility", "directories": {}, "devDependencies": {"co": "3", "mocha": "1", "cogent": "0", "should": "3"}}, "1.0.0": {"name": "mime-types", "version": "1.0.0", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "mime-types@1.0.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/mime-types", "bugs": {"url": "https://github.com/expressjs/mime-types/issues"}, "dist": {"shasum": "6a7b4a6af2e7d92f97afe03f047c7801e8f001d2", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-1.0.0.tgz", "integrity": "sha512-aP3BmIq4ZAPJt6KywU5HbiG0UwCTHZA2JWHO9aLaxyr8OhPOiK4RPSZcS6TDS7zNzGDC3AACnq/XTuEsd/M1Kg==", "signatures": [{"sig": "MEYCIQCU1DxnTHMCsdF29zM5sgqh/w1t+F3c3myRkTsT+ReqqAIhAJ+sXjwY8FX+aPYCVkJ6wUTdGzsEy1Z7LctKT9Dz8PGe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib", "_from": ".", "_shasum": "6a7b4a6af2e7d92f97afe03f047c7801e8f001d2", "scripts": {"test": "make test"}, "_npmUser": {"name": "fishrock123", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/mime-types", "type": "git"}, "_npmVersion": "1.4.9", "description": "ultimate mime type utility", "directories": {}, "devDependencies": {"co": "3", "mocha": "1", "cogent": "0", "should": "3"}}, "1.0.1": {"name": "mime-types", "version": "1.0.1", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "mime-types@1.0.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/mime-types", "bugs": {"url": "https://github.com/expressjs/mime-types/issues"}, "dist": {"shasum": "4d9ad71bcd4cdef6be892c21b5b81645607c0b8f", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-1.0.1.tgz", "integrity": "sha512-PltPKnzFcnj7RB0hFxrnMmuvn1kNy2dxsVJB4Zjub/E+0lv3zBfg3PL7fthEyiQxHI7LM0CathyZBorEOxk0nA==", "signatures": [{"sig": "MEYCIQD9ExA1FgOeajZjnWKHdDr+RcIFaAqGbUj04cuWmT1bQAIhAJwgi36Y6wXwH9NhmC6TYNO5s9gzcr5CBgL1Q7k/pLPM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib", "_from": ".", "_shasum": "4d9ad71bcd4cdef6be892c21b5b81645607c0b8f", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "fishrock123", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/mime-types", "type": "git"}, "_npmVersion": "1.4.9", "description": "The ultimate javascript content-type utility.", "directories": {}, "devDependencies": {"co": "3", "mocha": "1", "cogent": "0", "should": "3"}}, "1.0.2": {"name": "mime-types", "version": "1.0.2", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "mime-types@1.0.2", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/mime-types", "bugs": {"url": "https://github.com/expressjs/mime-types/issues"}, "dist": {"shasum": "995ae1392ab8affcbfcb2641dd054e943c0d5dce", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-1.0.2.tgz", "integrity": "sha512-echfutj/t5SoTL4WZpqjA1DCud1XO0WQF3/GJ48YBmc4ZMhCK77QA6Z/w6VTQERLKuJ4drze3kw2TUT8xZXVNw==", "signatures": [{"sig": "MEUCIGTZn5ZV2nCKdge933Lg6AvKXp1JvaL1X71hULpjhaXLAiEAyEMe0qKXWBwowL/gmnLRE4c/I4kRwKqQ/EFg7sPD0wY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib", "_from": ".", "_shasum": "995ae1392ab8affcbfcb2641dd054e943c0d5dce", "engines": {"node": ">= 0.8.0"}, "gitHead": "e82b23836eb42003b8346fb31769da2fb7eb54e8", "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/mime-types", "type": "git"}, "_npmVersion": "1.4.21", "description": "The ultimate javascript content-type utility.", "directories": {}, "devDependencies": {"co": "3", "mocha": "1", "cogent": "0", "should": "3"}}, "2.0.0": {"name": "mime-types", "version": "2.0.0", "keywords": ["mime", "types"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "mime-types@2.0.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "4a85688446a4d94a03909e0ae292766744a3c313", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.0.0.tgz", "integrity": "sha512-63bzg+SNOn8tpkiVG/RNcTkj+LvpB1K2L72Te5uvtbU3Zm41kB+PyEVmZUCADnKS8dBu9L8OJo1wZdpfmUnTUg==", "signatures": [{"sig": "MEUCIQDLoExMc3ubY0DJv7sDTz38kLezhrmOrjTeNo0L3s+GJgIgB5TMmlZZBwc7pW8d7u0oPM+Z/7sYWAWjRUCPL7fPh4o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "4a85688446a4d94a03909e0ae292766744a3c313", "engines": {"node": ">= 0.8.0"}, "gitHead": "c5a3c150ab62deeb61da5bd0b6265115b1b35f27", "scripts": {"test": "mocha --reporter spec --require should test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --require should test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot --require should test/test.js"}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/jshttp/mime-types", "type": "git"}, "_npmVersion": "1.4.21", "description": "The ultimate javascript content-type utility.", "directories": {}, "dependencies": {"mime-db": "~1.0.1"}, "devDependencies": {"mocha": "1", "should": "3", "istanbul": "0"}}, "2.0.1": {"name": "mime-types", "version": "2.0.1", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.0.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "7f5b4712592e7dd46ca733fd1c5f5d71356de615", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.0.1.tgz", "integrity": "sha512-eN798SjxgETHm9yA+Stq5CJl7zqF0vxn9HnRX4fv0SXNOI9MaHKeaqNGCOX1qGtcwKJy3XctHZDucFN0tO9MrQ==", "signatures": [{"sig": "MEUCIERGot7cci7ws3vuxU6SCRqQiVSIai/zLsToUliAB9teAiEA1eTA0c6ClshkNWvIXidSPTboJsXe0pzY7DLDcQjf/IU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "7f5b4712592e7dd46ca733fd1c5f5d71356de615", "engines": {"node": ">= 0.6"}, "gitHead": "c6e40fb033331aa87d9d4a5f1e5c0245e9edb2f6", "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-types", "type": "git"}, "_npmVersion": "1.4.21", "description": "The ultimate javascript content-type utility.", "directories": {}, "dependencies": {"mime-db": "~1.0.1"}, "devDependencies": {"mocha": "1", "istanbul": "0"}}, "2.0.2": {"name": "mime-types", "version": "2.0.2", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.0.2", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "c74b779f2896c367888622bd537aaaad4c0a2c08", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.0.2.tgz", "integrity": "sha512-hH83aTR9/ZC0yNJtFn44fLX0G7Ueu75+uIGQM314YtkqHGw3qVxfy9oCHF2FDx1kh3VnAOrdzSBcsVol1sNjlg==", "signatures": [{"sig": "MEUCIA2xn0b9zZe8Le8qwqb+glew3CPT5ehWqdGJZP0WKi/7AiEAmNfVviTercHk357E4zFOaS8s152ckI5ohV2C4O3W/ik=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "c74b779f2896c367888622bd537aaaad4c0a2c08", "engines": {"node": ">= 0.6"}, "gitHead": "7272f212651dcaca233803c58dc251b20668ca61", "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-types", "type": "git"}, "_npmVersion": "1.4.21", "description": "The ultimate javascript content-type utility.", "directories": {}, "dependencies": {"mime-db": "~1.1.0"}, "devDependencies": {"mocha": "1", "istanbul": "0"}}, "2.0.3": {"name": "mime-types", "version": "2.0.3", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.0.3", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "70b5cb5165b55dcb4972839f16d6077b0bb506f4", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.0.3.tgz", "integrity": "sha512-gLTMzv0arNJm8WabXwaqtjbPJ9VlqG93T8xCUN90FBESlyGIR1KMIJPmYicpC50AwsoYOhixwtrC7yistoAsgg==", "signatures": [{"sig": "MEUCIQCEwa3sNjMdxqIXWIkx8toafNN6Lo0PCRs5YWNcs+azCAIgeq1gN5URJx86RGf2KmUfqpz27DGCeDe6+uZve4tDIoQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "70b5cb5165b55dcb4972839f16d6077b0bb506f4", "engines": {"node": ">= 0.6"}, "gitHead": "7d38db8c0576cf7a2dd49df4818dc129090b3a2f", "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-types", "type": "git"}, "_npmVersion": "1.4.21", "description": "The ultimate javascript content-type utility.", "directories": {}, "dependencies": {"mime-db": "~1.2.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0"}}, "2.0.4": {"name": "mime-types", "version": "2.0.4", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.0.4", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "855a612979141d806ba5104294a28c731c6ea790", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.0.4.tgz", "integrity": "sha512-OBFm5ilBk5s73L32FCteA+9ai+4j7iIs/MmJe/JjneNym1FTV/D4AgTOlk0DJhHG9eRLPLcg8bCZ8K5c/rgdkw==", "signatures": [{"sig": "MEUCIQD8NyV0m8ZRfn0v1105Ql/PWywPVm765Xv6I7W1fuSC0AIgPnCbuNpZtjlxtslix6sQL3Bp080hmyltJwVAkvq0Rlg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "855a612979141d806ba5104294a28c731c6ea790", "engines": {"node": ">= 0.6"}, "gitHead": "63a9b82e6e364d62428ed5459e5486504c489bf2", "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-types", "type": "git"}, "_npmVersion": "1.4.21", "description": "The ultimate javascript content-type utility.", "directories": {}, "dependencies": {"mime-db": "~1.3.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0"}}, "2.0.5": {"name": "mime-types", "version": "2.0.5", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.0.5", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "017db3ef9aff1fc7229e9babe2ac4b032baf837d", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.0.5.tgz", "integrity": "sha512-m7ETTXgolXpu7j7GII4WZSmIrT8n8mgf/SeTzyYLJcK7Cdf6agUHOClX2xUoGobX2bBEY1xxx/nUTlMgHFA28A==", "signatures": [{"sig": "MEQCIDZe3xqe903D3tvsyIKz9BjmcQrL4vd/cOPKRD5XOtnsAiAycy3xRSCFo29q/0JhM5sV54LKugpP8N7xgqoS0NR58g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "017db3ef9aff1fc7229e9babe2ac4b032baf837d", "engines": {"node": ">= 0.6"}, "gitHead": "5f45d92a916100f86bf0eca1d953d523fcc2dfaf", "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-types", "type": "git"}, "_npmVersion": "1.4.28", "description": "The ultimate javascript content-type utility.", "directories": {}, "dependencies": {"mime-db": "~1.3.1"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.5"}}, "2.0.6": {"name": "mime-types", "version": "2.0.6", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.0.6", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "9deae929dc382665ea42e3848d1d3e3c4aef16e9", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.0.6.tgz", "integrity": "sha512-gVHjR7TGmrp7i5rY5p9NUBlTUJZiToR1Upu+HN/XeHmpBaBDTM3JGxbY4Nh0LebiniHZyleA3lWtU+bpP8Ce4Q==", "signatures": [{"sig": "MEUCIGi3V8gfhEwRlsy+5qhZPkISOdfK/8/uwqmMCDYFcN0yAiEAoe1EzVNDxKTLNs7BvWFPYBZ+4KCRS3p78QWJZ0dovfI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "9deae929dc382665ea42e3848d1d3e3c4aef16e9", "engines": {"node": ">= 0.6"}, "gitHead": "5348665cf1f2b3611a415da91aac17d431ddd019", "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-types", "type": "git"}, "_npmVersion": "1.4.28", "description": "The ultimate javascript content-type utility.", "directories": {}, "dependencies": {"mime-db": "~1.4.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.5"}}, "2.0.7": {"name": "mime-types", "version": "2.0.7", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.0.7", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "0cb58d0403aec977357db324eea67e40c32b44b2", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.0.7.tgz", "integrity": "sha512-vBZyuPcgzFDeTz1nWiY/X4RKKPxgf+VsrygiGo5azqz3P5teR3JDbSlGGWzn9Bu0jSMcaRFH+x15bgiaIq5RvQ==", "signatures": [{"sig": "MEUCIHTqsAOSk2eUBWvq21MGd1GT7YcLapIANskC6i76dIjWAiEAzDR6Ox7PLOMAZFQ8cykuAsB24h5QINwQl5kw912zWJM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "0cb58d0403aec977357db324eea67e40c32b44b2", "engines": {"node": ">= 0.6"}, "gitHead": "4216c095dcc3390c2b8f4a96a9eae94d11420f56", "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-types", "type": "git"}, "_npmVersion": "1.4.28", "description": "The ultimate javascript content-type utility.", "directories": {}, "dependencies": {"mime-db": "~1.5.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.5"}}, "2.0.8": {"name": "mime-types", "version": "2.0.8", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.0.8", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "5612bf6b9ec8a1285a81184fa4237fbfdbb89a7e", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.0.8.tgz", "integrity": "sha512-grE/DCZ4qGuR3JLlB8cPoqeCUNIO8AGyWILVh6y24gxTQlmxVmvV8MvVGErka7UroXOAwQaF9WU9B76uqlVJsQ==", "signatures": [{"sig": "MEUCIQCQu4cNNUbh1uqjJmZH+WYkbatjfGN6XqRAY9+qOavkIQIgXNc05QA3MXZjnVoe+pMdZ+SahdkU18sp45XmaM0c0Us=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "5612bf6b9ec8a1285a81184fa4237fbfdbb89a7e", "engines": {"node": ">= 0.6"}, "gitHead": "19e01e8bd630a1719ada4a3e3e9b7192b4ddb034", "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-types", "type": "git"}, "_npmVersion": "1.4.28", "description": "The ultimate javascript content-type utility.", "directories": {}, "dependencies": {"mime-db": "~1.6.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.5"}}, "2.0.9": {"name": "mime-types", "version": "2.0.9", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.0.9", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "e8449aff27b1245ddc6641b524439ae80c4b78a6", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.0.9.tgz", "integrity": "sha512-BbLj70Zd5NxN2pQLkzHQqteIZF8bqHruYJrO/6holhSKEcm24acJ4T+nVLcXOlXn4JRLbRuBsq6C+sr+rDM2/Q==", "signatures": [{"sig": "MEUCIQDqX3cEiEJBbjMLXpdzjqnGVmMq6Z3Tbdq/4wPdlXR5zQIgOTCwIsrHm7Zd7kusphz5Dg7OXUkrOJ/rdfLIOc5kJrU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "e8449aff27b1245ddc6641b524439ae80c4b78a6", "engines": {"node": ">= 0.6"}, "gitHead": "1c6d55da440b6a9d2c0e9c2faac98e6b1be47fc7", "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-types", "type": "git"}, "_npmVersion": "1.4.28", "description": "The ultimate javascript content-type utility.", "directories": {}, "dependencies": {"mime-db": "~1.7.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.5"}}, "2.0.10": {"name": "mime-types", "version": "2.0.10", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.0.10", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "eacd81bb73cab2a77447549a078d4f2018c67b4d", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.0.10.tgz", "integrity": "sha512-6BrF520aDPQWWSWcQYthxitp0XWLnB1R/EpjtTeA8q80XxcDkOf0G7ba7j6ml0xlpb2VIPR0NmcsARPZb3CfnQ==", "signatures": [{"sig": "MEUCIQD5tcDgBOeky6Wbtw1MFrZJlmQMXLkHSYVJk4UfFnj/9gIgUDgBkDbB1WSfuyClIU4eKXXlx4O34QYtOLgoSpbfkrg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "eacd81bb73cab2a77447549a078d4f2018c67b4d", "engines": {"node": ">= 0.6"}, "gitHead": "9d4533a2b3a68af48a7f3ded9f8f525648e7bcc1", "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-types", "type": "git"}, "_npmVersion": "1.4.28", "description": "The ultimate javascript content-type utility.", "directories": {}, "dependencies": {"mime-db": "~1.8.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.7"}}, "2.0.11": {"name": "mime-types", "version": "2.0.11", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.0.11", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "bf3449042799d877c815c29929d1e74760e72007", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.0.11.tgz", "integrity": "sha512-+94e9boSSjaNLBNyZw87aPZ2sf184sHucMXLRj1mlBV0E4D2RW8Yi6dup+i1cBc37SzWMlOdn0K4rgWOUdShBA==", "signatures": [{"sig": "MEYCIQDjuMzHes8sqVyE1bWz5rXpXMKbI72EEwoz7/PpkJLNwwIhAMPDCtBdQzWiODoFywu1MHYvcqiBNAo85PuprQupPdCP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "bf3449042799d877c815c29929d1e74760e72007", "engines": {"node": ">= 0.6"}, "gitHead": "4b233cfbb6704e88eca121e9d9f6e6f23957e48a", "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-types", "type": "git"}, "_npmVersion": "1.4.28", "description": "The ultimate javascript content-type utility.", "directories": {}, "dependencies": {"mime-db": "~1.9.1"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.9"}}, "2.0.12": {"name": "mime-types", "version": "2.0.12", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.0.12", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "87ae9f124e94f8e440c93d1a72d0dccecdb71135", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.0.12.tgz", "integrity": "sha512-KeYczXUPHXZR0KJXsGYggj5C3Etc77fn2es24XtW7sIuRmaC2J9M9sB2GAtugIKFaNNoVR1fX0L4orVnzJpJcA==", "signatures": [{"sig": "MEUCIQCJ8S6GsvMJSAPqUud4pvoqoEVhqm5okX/Ub3j1qcnWLAIgA0/PHaYvAa6beZA+lF/ONfnZUJYCaAHijkFO2o0fsQ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "87ae9f124e94f8e440c93d1a72d0dccecdb71135", "engines": {"node": ">= 0.6"}, "gitHead": "a1e3c4b6bbb6afb615c8a058481d58cb57a4cb95", "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-types", "type": "git"}, "_npmVersion": "1.4.28", "description": "The ultimate javascript content-type utility.", "directories": {}, "dependencies": {"mime-db": "~1.10.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.9"}}, "2.0.13": {"name": "mime-types", "version": "2.0.13", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.0.13", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "333f5579ae6eb203fd8f30a568a869d31a8f1cac", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.0.13.tgz", "integrity": "sha512-qIR+xv6bDd85+ec+VDbr2uBERjiXdeqyFLdsAiXGqboCX5RgF41UZeV2S6RpYK/4bu6ZNsi5oIl5Ct/VGrjK8A==", "signatures": [{"sig": "MEYCIQD2mS9Gkk6kPLWh6+TZA3n805iTxUebRdCG4XsiDRnkGgIhAKmtK5ekySxU5l9vdI6V3tV9rasbxR6zAN6pq+zesfwO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "333f5579ae6eb203fd8f30a568a869d31a8f1cac", "engines": {"node": ">= 0.6"}, "gitHead": "4e53ef0ec8b614992d5d8212e5aff1151ee97738", "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-types", "type": "git"}, "_npmVersion": "1.4.28", "description": "The ultimate javascript content-type utility.", "directories": {}, "dependencies": {"mime-db": "~1.11.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.9"}}, "2.0.14": {"name": "mime-types", "version": "2.0.14", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.0.14", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "310e159db23e077f8bb22b748dabfa4957140aa6", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.0.14.tgz", "integrity": "sha512-2ZHUEstNkIf2oTWgtODr6X0Cc4Ns/RN/hktdozndiEhhAC2wxXejF1FH0XLHTEImE9h6gr/tcnr3YOnSGsxc7Q==", "signatures": [{"sig": "MEYCIQDLfMeIKOQNqG8+l6BFjBdTTICusSHLKtqbmtQXx+8kFQIhAJnDnOIJWwV+pBGTeKj5PkFB4ZGcPyjydEc25IYAF6EX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "310e159db23e077f8bb22b748dabfa4957140aa6", "engines": {"node": ">= 0.6"}, "gitHead": "7d53a3351581eb3d7ae1e846ea860037bce6fe3f", "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-types", "type": "git"}, "_npmVersion": "1.4.28", "description": "The ultimate javascript content-type utility.", "directories": {}, "dependencies": {"mime-db": "~1.12.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.9"}}, "2.1.0": {"name": "mime-types", "version": "2.1.0", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.1.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "26e401fb4ccc1fa5c8b15bac4a1aefb9af05b0b1", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.0.tgz", "integrity": "sha512-E3ZX8HRfMgWjjMcENMFAK/uT+7ILMM3Tq7sx8lQid8YGdOsNC4LtYA5UWl86TUN5rF653NtAxn5zZBA3WC19ww==", "signatures": [{"sig": "MEQCIHSoJf6QyTAIhNIuFPDU7bvZS5K1h5vd5fPfEsruWFT+AiAR7ror3Elr781AzcsoahKvV72r1H4C7Dpjt+TlKUQmEg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "26e401fb4ccc1fa5c8b15bac4a1aefb9af05b0b1", "engines": {"node": ">= 0.6"}, "gitHead": "95130f68c743520b8b54ce7341d84c0833cbb375", "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-types", "type": "git"}, "_npmVersion": "1.4.28", "description": "The ultimate javascript content-type utility.", "directories": {}, "dependencies": {"mime-db": "~1.13.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.14"}}, "2.1.1": {"name": "mime-types", "version": "2.1.1", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.1.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "c7b692796d5166f4826d10b4675c8a916657d04e", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.1.tgz", "integrity": "sha512-Ny1adSku8PrRhb8t9UiC7TZ7CI2ik8sgsDxnkGBNFIVHo9H0CcIpurlhLZ2UH7jpoa06HT0GSUfv++7B/lnd+A==", "signatures": [{"sig": "MEYCIQC+mHeamiX6kJVOAR8ntpByzjbpd9OO/KsMUiQrAc+A/wIhAI+DlgWRauDQROoRRz3GcE6RfD5xmRIum5xRI6zECikM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "c7b692796d5166f4826d10b4675c8a916657d04e", "engines": {"node": ">= 0.6"}, "gitHead": "c067c0b0d8a2e7df82c356dc295852688be1245c", "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-types", "type": "git"}, "_npmVersion": "1.4.28", "description": "The ultimate javascript content-type utility.", "directories": {}, "dependencies": {"mime-db": "~1.13.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.14"}}, "2.1.2": {"name": "mime-types", "version": "2.1.2", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.1.2", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "6545ccd95afe77b9c655d81c2e6ceace36257227", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.2.tgz", "integrity": "sha512-u531XpUuatmDePEt7aT0ONMOxlsiiY8rCOgPHKL6uZwQ6QgGvgq1Z+oeYpcWTf4ip/4Q6rosnZWjvt6BMLU1cQ==", "signatures": [{"sig": "MEUCIEY6n1qQfZuhCaxeqggiIQ+xNyVjq249cetQdj6C50WKAiEAn1h144G89AjPRGc0OE6PEjLTe5orZGfL5HH1tlLaVG8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "6545ccd95afe77b9c655d81c2e6ceace36257227", "engines": {"node": ">= 0.6"}, "gitHead": "5704d7bb0cf90f14b442f0b954ace6c1dbc5f435", "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-types", "type": "git"}, "_npmVersion": "1.4.28", "description": "The ultimate javascript content-type utility.", "directories": {}, "dependencies": {"mime-db": "~1.14.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.16"}}, "2.1.3": {"name": "mime-types", "version": "2.1.3", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.1.3", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "f259849c7eb1f85b8f5f826187278a7f74f0c966", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.3.tgz", "integrity": "sha512-yG2aUisDDz12mVDXJAqB4akjYZO8pFntdklyHAHCghr7lgAVTp7t8mrH7e5J6O5ngQD7av4h/2bdWRXU3A1goQ==", "signatures": [{"sig": "MEYCIQCxvReYWWK2a1rTVOF5EAm7gkFDqDwxc7We7WPXMqRVTgIhAPSlbSx3wgXAxPuYnwDaSEavrPPxpLDZ9sxlTlOgDE8S", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "f259849c7eb1f85b8f5f826187278a7f74f0c966", "engines": {"node": ">= 0.6"}, "gitHead": "565c49ad5683d4a123a170da3444ed32ce426c3a", "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-types", "type": "git"}, "_npmVersion": "1.4.28", "description": "The ultimate javascript content-type utility.", "directories": {}, "dependencies": {"mime-db": "~1.15.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.17"}}, "2.1.4": {"name": "mime-types", "version": "2.1.4", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.1.4", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "6562b328e341245cb63b14473b1d12b40dec5884", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.4.tgz", "integrity": "sha512-Drx8+Rsyw683vPZ91L9aFwuuuwy1zsZMmSUBXSIf0MXU+OckhmTxKEzLLI3IYsX71AAcP4+pj9YoVQjRwYLJdw==", "signatures": [{"sig": "MEUCIQDS7SfpGoNFa97+OgF8xYTEJsthL523WS5Upb7ZQirbKAIgDZUqQ9Mcey9nuolt1gFCt2XOrWWTN0tBLPhv7z+4GNQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "6562b328e341245cb63b14473b1d12b40dec5884", "engines": {"node": ">= 0.6"}, "gitHead": "0d801665a7374c71d905d14bc3afc2b0624bb896", "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-types", "type": "git"}, "_npmVersion": "1.4.28", "description": "The ultimate javascript content-type utility.", "directories": {}, "dependencies": {"mime-db": "~1.16.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.17"}}, "2.1.5": {"name": "mime-types", "version": "2.1.5", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.1.5", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "2355ac0e1e0c5a68d8df6474b431192743f0a3ea", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.5.tgz", "integrity": "sha512-yMHYuggR1CpBWq0MKMIDiZCpB8PPW+axaSoBoBiSLvPsOJEFoqX3m3Rq3RwuX7xPSLBlwsyYUfU+cBKHgYYvHg==", "signatures": [{"sig": "MEUCIQDrV4eiLMJR1vUlNB4CXhDQxXJ0i7Y5j7rXwOzLX24oCAIgT/CH728yAx81Q+nBDSJSXHOZ7CqGYBJwSC3dR7TDD9c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "2355ac0e1e0c5a68d8df6474b431192743f0a3ea", "engines": {"node": ">= 0.6"}, "gitHead": "de48c96e731e5903433ac2cb5c0d9fd056d9d19b", "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-types", "type": "git"}, "_npmVersion": "1.4.28", "description": "The ultimate javascript content-type utility.", "directories": {}, "dependencies": {"mime-db": "~1.17.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.18"}}, "2.1.6": {"name": "mime-types", "version": "2.1.6", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.1.6", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "949f8788411864ddc70948a0f21c43f29d25667c", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.6.tgz", "integrity": "sha512-/Wu40EFhOQ8MUNVFOFM3kuVGjGKrkWoLGzi7OHeTA/BTTKNkHD7jp/VrbX/bU883aDrpnsoFDPl9pxXY57Rq6Q==", "signatures": [{"sig": "MEYCIQDyVSNZi8RpX5eURNN6A/Th52jwEbGt4Aq/QnO5cC3JdgIhANS/980ggpo4zgWRdmeYSDu4gNDah0Q1po50uG13P6sK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "949f8788411864ddc70948a0f21c43f29d25667c", "engines": {"node": ">= 0.6"}, "gitHead": "8bfa7c2c9a4ad07f5807c7a0e547e0246155944d", "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-types", "type": "git"}, "_npmVersion": "1.4.28", "description": "The ultimate javascript content-type utility.", "directories": {}, "dependencies": {"mime-db": "~1.18.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.19"}}, "2.1.7": {"name": "mime-types", "version": "2.1.7", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.1.7", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "ff603970e3c731ef6f7f4df3c9a0f463a13c2755", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.7.tgz", "integrity": "sha512-m+j0nh7H0xUSa4fvA9QuwEhMhKxepAY4jbKOrpjdGJyyzgTCo2219L4kn8zLx0tXuQsmLf//4wMreA6YBpAw+Q==", "signatures": [{"sig": "MEYCIQDG2I3dibcE6VdUGG3McxLfm314XSzThyWIHEVQKs0whQIhAJDl4y3E++eBL13CdqL4McIxziT0GBE1IrljslxaK22c", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "ff603970e3c731ef6f7f4df3c9a0f463a13c2755", "engines": {"node": ">= 0.6"}, "gitHead": "43f860c7df4a70246272194d601348865d550298", "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-types", "type": "git"}, "_npmVersion": "1.4.28", "description": "The ultimate javascript content-type utility.", "directories": {}, "dependencies": {"mime-db": "~1.19.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.20"}}, "2.1.8": {"name": "mime-types", "version": "2.1.8", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.1.8", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "faf57823de04bc7cbff4ee82c6b63946e812ae72", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.8.tgz", "integrity": "sha512-PHhLyM6wFxef+LfMcX2GvMqEY8mAK4Sv3DmqqW+1sYKGMZRmJkyF6ZgvhJGF3W59YL+4yMC7c2CHfG+lcbqY5A==", "signatures": [{"sig": "MEUCIBvjN8Ms2hv9F6ulgO7UGetulGIZVF15BPr7CuwjmiuKAiEA9FWGsZPjn+rX+4YQuOEpuaiNOnvkRZH8Ev/tOuDep6w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "faf57823de04bc7cbff4ee82c6b63946e812ae72", "engines": {"node": ">= 0.6"}, "gitHead": "100876a23fab896d8cf0d904fc9778dbdfc1695b", "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-types", "type": "git"}, "_npmVersion": "1.4.28", "description": "The ultimate javascript content-type utility.", "directories": {}, "dependencies": {"mime-db": "~1.20.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.4.1"}}, "2.1.9": {"name": "mime-types", "version": "2.1.9", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.1.9", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "dfb396764b5fdf75be34b1f4104bc3687fb635f8", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.9.tgz", "integrity": "sha512-omiqgDW9rOHbvPP6PrGKQvSIpNh5OplM4hfU2aA+cThb51GKaWxL9Fd8OWNK/ZbtxOCJn5ydQ83PXKMyz9XRFg==", "signatures": [{"sig": "MEYCIQCowD8llnMV4BHV0QRM5qb96SDJp/kd8wpzKFzTHzwmtQIhAJjrV9ygxUdbb+MX1CSOF8qDqyPBr2lAW7a15stfvOaM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "dfb396764b5fdf75be34b1f4104bc3687fb635f8", "engines": {"node": ">= 0.6"}, "gitHead": "329f1c77e1a77c8fac59b15038e3808e9e314d96", "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-types", "type": "git"}, "_npmVersion": "1.4.28", "description": "The ultimate javascript content-type utility.", "directories": {}, "dependencies": {"mime-db": "~1.21.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.4.1"}}, "2.1.10": {"name": "mime-types", "version": "2.1.10", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.1.10", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types#readme", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "b93c7cb4362e16d41072a7e54538fb4d43070837", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.10.tgz", "integrity": "sha512-cjDtNq/7/kWehCc5gPzRnwBMJ8lx+kmg1rvBtbx8o84v+E3Gtc+hVgAXXhLcpICjQbgQGFYSt8WCCmen5fOQaQ==", "signatures": [{"sig": "MEYCIQD1lu9M2gJOwwQ7Nr4+OAQR0B+/vHfkL1CTZVFi5nMeBQIhAN1JNTu4Lh7eM08/rA7u+yT8RC+A/w1xQ1f98ABz0Iyt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "b93c7cb4362e16d41072a7e54538fb4d43070837", "engines": {"node": ">= 0.6"}, "gitHead": "70785d38e9cc251137b00f73ab3d3257c4aea203", "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-types.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "The ultimate javascript content-type utility.", "directories": {}, "_nodeVersion": "4.2.3", "dependencies": {"mime-db": "~1.22.0"}, "devDependencies": {"mocha": "1.21.5", "istanbul": "0.4.2"}, "_npmOperationalInternal": {"tmp": "tmp/mime-types-2.1.10.tgz_1455575237256_0.9163766100537032", "host": "packages-9-west.internal.npmjs.com"}}, "2.1.11": {"name": "mime-types", "version": "2.1.11", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.1.11", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types#readme", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "c259c471bda808a85d6cd193b430a5fae4473b3c", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.11.tgz", "integrity": "sha512-14dD2ItPaGFLVyhddUE/Rrtg+g7v8RmBLjN5Xsb3fJJLKunoZOw3I3bK6csjoJKjaNjcXo8xob9kHDyOpJfgpg==", "signatures": [{"sig": "MEUCIQDFjqcWXhyxsLWtYTpj7iWEPm9VzpZQHg29PeLG6iGY5QIgeeTccjqEpIotPXPiKyKU1sj6x2b/bUNpDsbuJpWGjIk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "c259c471bda808a85d6cd193b430a5fae4473b3c", "engines": {"node": ">= 0.6"}, "gitHead": "298ffcf490a5d6e60edea7bf7a69036df04846b1", "scripts": {"test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-types.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "The ultimate javascript content-type utility.", "directories": {}, "_nodeVersion": "4.4.3", "dependencies": {"mime-db": "~1.23.0"}, "devDependencies": {"mocha": "1.21.5", "istanbul": "0.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/mime-types-2.1.11.tgz_1462165365027_0.7217204745393246", "host": "packages-12-west.internal.npmjs.com"}}, "2.1.12": {"name": "mime-types", "version": "2.1.12", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.1.12", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types#readme", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "152ba256777020dd4663f54c2e7bc26381e71729", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.12.tgz", "integrity": "sha512-irQD8Ww11AaU8vbCRjMuaq4huvb2ITxVg/VDBrvf8keFtbWZ3zbGO0tvsCMbD7JlR8mOYw0WbAqi4sL8KGUd5w==", "signatures": [{"sig": "MEYCIQCFQpN5/gWYTDsfIstIdxGcRjm2rToPtNweAPmuvQsr/wIhAMgfDWEaSYTK8EM+WTp/zaa3t20DNNkWJ6mkrEcngoL9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "152ba256777020dd4663f54c2e7bc26381e71729", "engines": {"node": ">= 0.6"}, "gitHead": "7193a9094e2efe31da93988350bb0b32ab18b1ea", "scripts": {"lint": "eslint **/*.js", "test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-types.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "The ultimate javascript content-type utility.", "directories": {}, "_nodeVersion": "4.5.0", "dependencies": {"mime-db": "~1.24.0"}, "devDependencies": {"mocha": "1.21.5", "eslint": "3.5.0", "istanbul": "0.4.5", "eslint-plugin-promise": "2.0.1", "eslint-config-standard": "6.0.1", "eslint-plugin-standard": "2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/mime-types-2.1.12.tgz_1474237415119_0.03028594213537872", "host": "packages-12-west.internal.npmjs.com"}}, "2.1.13": {"name": "mime-types", "version": "2.1.13", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.1.13", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "e07aaa9c6c6b9a7ca3012c69003ad25a39e92a88", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.13.tgz", "integrity": "sha512-ryBDp1Z/6X90UvjUK3RksH0IBPM137T7cmg4OgD5wQBojlAiUwuok0QeELkim/72EtcYuNlmbkrcGuxj3Kl0YQ==", "signatures": [{"sig": "MEQCIHqHsyNAgRxO9qHQrwguRWhhjLgVC0y8D4KUwQD6FC/DAiByZ4oOhrBxiIJsiySbcRhPa41vqfq3zg42mXMbF3+6tA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "e07aaa9c6c6b9a7ca3012c69003ad25a39e92a88", "engines": {"node": ">= 0.6"}, "gitHead": "83e91a5aea93858bc95ec95a99309592cba0ffe3", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-types", "type": "git"}, "_npmVersion": "1.4.28", "description": "The ultimate javascript content-type utility.", "directories": {}, "dependencies": {"mime-db": "~1.25.0"}, "devDependencies": {"mocha": "1.21.5", "eslint": "3.10.2", "istanbul": "0.4.5", "eslint-plugin-promise": "3.4.0", "eslint-config-standard": "6.2.1", "eslint-plugin-standard": "2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/mime-types-2.1.13.tgz_1479505166253_0.5666956284549087", "host": "packages-12-west.internal.npmjs.com"}}, "2.1.14": {"name": "mime-types", "version": "2.1.14", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.1.14", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types#readme", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "f7ef7d97583fcaf3b7d282b6f8b5679dab1e94ee", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.14.tgz", "integrity": "sha512-okJd888hr2XVzvoRxEOnEEgmlsOnLVoIOAKoBgYMLOQPNQFprAx970dULXC3ueiQMIiTsSxUFSpa2y3IlBefCg==", "signatures": [{"sig": "MEYCIQD9SIFUqn/2Na8WrmaJkJfUG8pcg41b3YuQescwSvZHVwIhAPqo+oZjNWsU0W4iUuSRypdABgr52yJehqn/Waz/x9xr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "f7ef7d97583fcaf3b7d282b6f8b5679dab1e94ee", "engines": {"node": ">= 0.6"}, "gitHead": "d7d15e50fe6b3a2da79d855015d25efa50e9f157", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-types.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "The ultimate javascript content-type utility.", "directories": {}, "_nodeVersion": "4.6.1", "dependencies": {"mime-db": "~1.26.0"}, "devDependencies": {"mocha": "1.21.5", "eslint": "3.13.1", "istanbul": "0.4.5", "eslint-plugin-promise": "3.4.0", "eslint-config-standard": "6.2.1", "eslint-plugin-standard": "2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/mime-types-2.1.14.tgz_1484458141358_0.7563668976072222", "host": "packages-18-east.internal.npmjs.com"}}, "2.1.15": {"name": "mime-types", "version": "2.1.15", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.1.15", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types#readme", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "a4ebf5064094569237b8cf70046776d09fc92aed", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.15.tgz", "integrity": "sha512-PjleM8evsL+OvsuE6EXom+8QAcSYALjmw+vYFqH8I+/+wNlewVgbM7/O1wcdCVL/ta8SC6l6BEK7A0/mZywpfg==", "signatures": [{"sig": "MEYCIQDfItjuv+3QRopNeBiGSQ1JgrT4xYJrXDg5gqz9OOXzRwIhAOpeOs7b5tVZkqLhuSNc3XKTxECV3JEqYHGKBSoM8kXv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "a4ebf5064094569237b8cf70046776d09fc92aed", "engines": {"node": ">= 0.6"}, "gitHead": "c44863eb0463ee16f3eb04576591cc4c4d6b214c", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-types.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "The ultimate javascript content-type utility.", "directories": {}, "_nodeVersion": "4.7.3", "dependencies": {"mime-db": "~1.27.0"}, "devDependencies": {"mocha": "1.21.5", "eslint": "3.18.0", "istanbul": "0.4.5", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "7.1.0", "eslint-plugin-standard": "2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/mime-types-2.1.15.tgz_1490327753615_0.3609113476704806", "host": "packages-12-west.internal.npmjs.com"}}, "2.1.16": {"name": "mime-types", "version": "2.1.16", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.1.16", "maintainers": [{"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types#readme", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "2b858a52e5ecd516db897ac2be87487830698e23", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.16.tgz", "integrity": "sha512-YjTLbZxlgVrR0Gv3KxaqEcTDMoxI+kjRw8box2aRPm0IDtIqP6hC6pv5F2ONy7UcgTtSQE6zAqkZE7jDP0gb1g==", "signatures": [{"sig": "MEUCIGM3IGDBOTMKmajE+CSd2timb8BdCe6K3D22C8aldx+WAiEAmU+43f+3EtECoKcLLDe0Eh1BPNuNI4T6YMEqPTu7Adg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "2b858a52e5ecd516db897ac2be87487830698e23", "engines": {"node": ">= 0.6"}, "gitHead": "a776f883a8bb1d50588224c46caefa6fc313f790", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-types.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "The ultimate javascript content-type utility.", "directories": {}, "_nodeVersion": "6.11.1", "dependencies": {"mime-db": "~1.29.0"}, "devDependencies": {"mocha": "1.21.5", "eslint": "3.19.0", "istanbul": "0.4.5", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/mime-types-2.1.16.tgz_1500950558329_0.4321689426433295", "host": "s3://npm-registry-packages"}}, "2.1.17": {"name": "mime-types", "version": "2.1.17", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.1.17", "maintainers": [{"name": "fishrock123", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types#readme", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "09d7a393f03e995a79f8af857b70a9e0ab16557a", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.17.tgz", "integrity": "sha512-rOFZoFAbaupSpzARUe5CU1P9mwfX+lIFAuj0soNsEZEnrHu6LZNyV7/FClEB/oF9A1o5KStlumRjW6D4Q2FRCA==", "signatures": [{"sig": "MEQCIGwLdLUbCupNVQckI8mYNBUvBSDNNIRScFOCU5C4C9aLAiBVNN7h6abChyA8p8MEgNOQKdJ7niK4qUGf6J4SRUy9mA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "09d7a393f03e995a79f8af857b70a9e0ab16557a", "engines": {"node": ">= 0.6"}, "gitHead": "80039fe78213821c2e9b25132d6b02cc37202e8a", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-types.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "The ultimate javascript content-type utility.", "directories": {}, "_nodeVersion": "6.11.1", "dependencies": {"mime-db": "~1.30.0"}, "devDependencies": {"mocha": "1.21.5", "eslint": "3.19.0", "istanbul": "0.4.5", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/mime-types-2.1.17.tgz_1504322793218_0.6663200033362955", "host": "s3://npm-registry-packages"}}, "2.1.18": {"name": "mime-types", "version": "2.1.18", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.1.18", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types#readme", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "6f323f60a83d11146f831ff11fd66e2fe5503bb8", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.18.tgz", "fileCount": 5, "integrity": "sha512-lc/aahn+t4/SWV/qcmumYjymLsWfN3ELhpmVuUFjgsORruuZPVSwAQryq+HHGvO/SI2KVX26bx+En+zhM8g8hQ==", "signatures": [{"sig": "MEUCIHdWvpu26LAu2XkoSWvKs9Z50rck4Fia9wYScp9+mF79AiEA9LkLbhm64ld0N+b8gFj91H0ndPTJ7ohdws7ke+ho2Qw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14341}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "gitHead": "076f7902e3a730970ea96cd0b9c09bb6110f1127", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-types.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "The ultimate javascript content-type utility.", "directories": {}, "_nodeVersion": "6.13.0", "dependencies": {"mime-db": "~1.33.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "1.21.5", "eslint": "3.19.0", "istanbul": "0.4.5", "eslint-plugin-node": "5.2.1", "eslint-plugin-import": "2.8.0", "eslint-plugin-promise": "3.6.0", "eslint-config-standard": "10.2.1", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/mime-types_2.1.18_1518802461370_0.7224202442179994", "host": "s3://npm-registry-packages"}}, "2.1.19": {"name": "mime-types", "version": "2.1.19", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.1.19", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types#readme", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "71e464537a7ef81c15f2db9d97e913fc0ff606f0", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.19.tgz", "fileCount": 5, "integrity": "sha512-P1tKYHVSZ6uFo26mtnve4HQFE3koh1UWVkp8YUC+ESBHe945xWSoXuHHiGarDqcEZ+whpCDnlNw5LON0kLo+sw==", "signatures": [{"sig": "MEQCICdJLF/w/mVo2z2d6UKp6rIlq5gyBph4U+PiFa8JcPHFAiAcMz1hk32osGmknxLXamYVO/4Nksvp6hpb/ulsbbwFfw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14676, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbTtBXCRA9TVsSAnZWagAAjF4QAJrZ4PBxWvh7l1DeYPzG\nmVMsannUqqjnDbtNJX441rdKJi8ammCnBz8lK026+cfLCwr3lzBHvuJw3Vl4\nel2wygL1+DbCiK/r53ATrRmdbD5/F/DYGE9lYN75O3vxs8t98dlBzqSH1aZx\n2tG3pwmfTGa0/TVkzpNrCvolxXYleo7orXZ5PSf2tRLkeGV18625cgYKlnTR\nUdyLQfpHtLwmARJB5xM/279rM1jXA/oK0V1IMcjpEG/+bBL8bHGkpwsqrOb9\nSLwxQiDIWNN8uh23g4VB7KjUSKmjBvM+Aq9D7UPf7x5Iqti6Fe0mTNvnkbla\n83kMaBHgMINTpTu4Lh64C/eahlo54OBRTZWopP2qrUgUZIQdLvWH2P3fKWXt\n1IcEn4sq/8r7wtq9q3dEZcrbN2wEsStFUVOJ1Stn9jDXIntwEhvno5fTPBo0\nws03MtNrL0ka17mLjAgZX8c+3c1xCVbejiwAy7YN9gutZfQJ5cmV0hRzspa/\nJTC1LVfSMhFj5PhqliXG3MGA6zUHMSACveRsBw/2AEcd+lp5WSGbbdkelskT\n7SbwxD+Fe288EPA6SAWz4C2HZBNujHi2GUbQDZYdCg2VmptzR/S6hy70ROsx\nBlDWxCNWABHd9tnp0JL2yg6FXUtRLPc7hZTl1ecb2bO/AdfIz0TjwSJW+QU/\ncWQe\r\n=Sdv/\r\n-----END PGP SIGNATURE-----\r\n"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "gitHead": "7c4ce23d7354fbf64c69d7b7be8413c4ba2add78", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-types.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "The ultimate javascript content-type utility.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"mime-db": "~1.35.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "1.21.5", "eslint": "4.19.1", "istanbul": "0.4.5", "eslint-plugin-node": "6.0.1", "eslint-plugin-import": "2.13.0", "eslint-plugin-promise": "3.8.0", "eslint-config-standard": "11.0.0", "eslint-plugin-standard": "3.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/mime-types_2.1.19_1531891799651_0.29011555741327655", "host": "s3://npm-registry-packages"}}, "2.1.20": {"name": "mime-types", "version": "2.1.20", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.1.20", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types#readme", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "930cb719d571e903738520f8470911548ca2cc19", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.20.tgz", "fileCount": 5, "integrity": "sha512-HrkrPaP9vGuWbLK1B1FfgAkbqNjIuy4eHlIYnFi7kamZyLLrGlo2mpcx0bBmNpKqBtYtAfGbodDddIgddSJC2A==", "signatures": [{"sig": "MEUCIHGjASLv/vy65ZUZcXwElV8dKwTSvliw1AWdZfnuPD/YAiEAsD2bd9Aeep2vfxcrnRypLqJjqhgisPohxiOLig7z7Zg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14871, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgySOCRA9TVsSAnZWagAAmgAP/0/KvhTzYKLlVNqKYS25\nm/8NWOT2ANSUzj3Z6+WuqmmeVWS7VVvmOI/2OscrQLX7nFEMicjz9DHZtCVW\nWBeLLbBEmx7cT1WOZA37jM2t+2BOz2w689YSwkasLjh9lZPZVEVvuww7248u\nQnwbxBM34ZSZFyMoaxfyotRt9KUMjsuOCTigTJscaayw+ViObrGvEK6vp6Q6\nkdTJ5oTMCo3ahS0H9VVEPU6yb3C9/5Szvp21sUOYWoJq2Rv7QXDI2R+UZzpA\n5CJetFzTXOHzyRW2eEdXpEowAaZbykn1O7Dul9nAZJLODMvNFaKXAmQNVq9Q\nqFFI0ECf77Ijg9Am5zNaeSRuujGZ351WpVzgct8gCepUgPRn1Ro302pXFKte\n/mMSFARUYA+MkdDNH8l+73byG3ipw4YN38nQd7KKyngpxirFvDvqBUg17c3+\ntBksa4PHLP0+qVHhjeL+A19bVByQHP0P+jWkpTiJSVvIgk92pPVpIjxmnuI7\nlb76ypP946/Kgrm16+o4nKCDIhGAnRYmmC8wCN8kBM7YNW7vICTOx6NwH03V\nA9zo4NCVackZxuALNrQ9p7VcMWk6Sn+NFFHov1YV6iAlA8L/rMajb19IBwnc\nSOPH2ezEd5E9wqrotB30FgPo9IDDfPJGVh55xGL69PO86m3ANZdIdSGW0RHO\ne5M2\r\n=qmfT\r\n-----END PGP SIGNATURE-----\r\n"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "gitHead": "1b0f6e8b605fa83c961ef352be701e23f66cf917", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot test/test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-types.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "The ultimate javascript content-type utility.", "directories": {}, "_nodeVersion": "8.11.4", "dependencies": {"mime-db": "~1.36.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "1.21.5", "eslint": "4.19.1", "istanbul": "0.4.5", "eslint-plugin-node": "6.0.1", "eslint-plugin-import": "2.13.0", "eslint-plugin-promise": "3.8.0", "eslint-config-standard": "11.0.0", "eslint-plugin-standard": "3.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/mime-types_2.1.20_1535321229921_0.8062784689775195", "host": "s3://npm-registry-packages"}}, "2.1.21": {"name": "mime-types", "version": "2.1.21", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.1.21", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types#readme", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "28995aa1ecb770742fe6ae7e58f9181c744b3f96", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.21.tgz", "fileCount": 5, "integrity": "sha512-3iL6DbwpyLzjR3xHSFNFeb9Nz/M8WDkX33t1GFQnFOllWk8pOrh/LSrB5OXlnlW5P9LH73X6loW/eogc+F5lJg==", "signatures": [{"sig": "MEUCIDP7agiW15n0NRJZsLKFjYqHlc11iYYbmz5/pVa8IKGyAiEA6b9uLb/Mw5ewJ8ZaokHX61a8FKklucJJ99NnpQ/7X4Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14860, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbyqLkCRA9TVsSAnZWagAAVigP/RL9Plb3H2UFfyVhYQQx\neXr1yHPsEB46hVDaySgvX1YMFiaWibmX+gh7qLp27cjmSyM0O9dxPEt9dFc5\nMQ9aJudZ+Qy6/RFQ2KhPTwkor/rgKx83SXNLru7JT4W/3TuyglYfOdypaDeG\nYBckwRhkA28NQNlPs70nVzAnR66QDvNNcmO7ScdG4qNNCn9YziF8WoXl6xpx\n0SYHRHmiFU22rJGT/8fAm57qFKHUVnisGUrB2QNoVpZH7euxfMfHGS40k7BR\nnP2y0q14jVp+oXNH0vrCtY70/Ys95f+JzHvIJSDh49GCnlM7qN0M+cLFs1hP\ns3/9IRkehSPrb6iyPcoRTsLLYaZq307ce1wM/AJhIJe287Z4pVbmvEinppwR\nPqltP7erKXX9B+eIYPxFQKWbgjkGma9R2PMXgINjT1JW0whjANiu1N+4VcyJ\nkGmgf3Kf6C2VmtwFyyW3bUkpH1mVsTX78vC3wD1YdXjzCgdmPsOAY7JYGBfY\nAHr6/DyG/rgPKsr9lMSs6n33ONbfyHl9VzBT/+gNaj18AqF3bRnfMhWPptJI\n4wVOonYgazuC2w7zYh9m3Wu6CK/lDqlohT7cpSUyJWELbANxbJLZpyP2IJdB\nZvLOL4Cj9lkTgEonV5gN6GDFiIkJkUbtNRCxPrH7sCNrV4TpQf+3CKMcu3SV\nwQsc\r\n=EdfX\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "1aed04a9c43b8b920c0f77d1cb160d42079d978c", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-types.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "The ultimate javascript content-type utility.", "directories": {}, "_nodeVersion": "8.12.0", "dependencies": {"mime-db": "~1.37.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "13.1.0", "mocha": "5.2.0", "eslint": "5.7.0", "eslint-plugin-node": "7.0.1", "eslint-plugin-import": "2.14.0", "eslint-plugin-promise": "4.0.1", "eslint-config-standard": "12.0.0", "eslint-plugin-standard": "4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/mime-types_2.1.21_1540006627863_0.6812190744417017", "host": "s3://npm-registry-packages"}}, "2.1.22": {"name": "mime-types", "version": "2.1.22", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.1.22", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types#readme", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "fe6b355a190926ab7698c9a0556a11199b2199bd", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.22.tgz", "fileCount": 5, "integrity": "sha512-aGl6TZGnhm/li6F7yx82bJiBZwgiEa4Hf6CNr8YO+r5UHr53tSTYZb102zyU50DOWWKeOv0uQLRL0/9EiKWCog==", "signatures": [{"sig": "MEUCIQCh7nRfU0AcYuRt44Gmtl3SM38W6IV03fMJ4ndozcEHGQIgJkyNkGrkybmVDGn643rzYXu0HyrJ9TPF7ANMP/uKt3Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcZfAKCRA9TVsSAnZWagAAkLQP/jW4lBe78qBJSqi0BdL3\nbvjlEBy3K8HNUwrGAPhw36ooYolZtw6mZET7Ylm0taGaWYz7IPrGwmGK3TB3\nUE+OZPGEiolp+cSybxdo3eX3/AzoCVmOk5hJ+RWVzu+wUVQ4tAn3Yrt7drit\nhnNfW4mIjVsT8R2i9XHYvYyTXaz2rtyN9x9TLIdecKgbhYj526b5ZVR3gC2d\nuZ4B4b/Cg3kdVgUt0tzOO8Isw81Kj/bYIrgwa7RqdUwZTcA11TeGBiKtVDye\nSBRYyUniD65y6aykhPxjKWsso5btbzuA0OU3TK0hT/HE+NkOq5VcupUPvPBM\nTOGjVMlQNhGqsJcmJ0UoW3CKvN6g3p1xKGtytRXhPcA4pRUKgTEGOTMxblwi\nkoEnQ4C19er4CfVBGUprXpwczPkZJ5VjhSSKOpT+YNib6vZUQCivAUHTV7eN\nNEaM1LFoKuX4wL9anYd+XtShyyVsql3vtdcHXDprh/02qQHzvELY+py04XpG\nHZcgdA3YT2Bb7jyZpDwwLr8kwmtSQZDYaS/PV+bqGHbdhuwjdbJS6AaDiks0\nq+iBL1OYv/NWK9bCHRmwQVL7tr9iyKax9LnlUIK8QWtcyFogHt3bgNWQeFYa\nJdMhgN0/FmRmPjOnTftDWHU8+TaSFfGXp796ZFPBv0h8mFJlGlVTa/otM76/\n+ijp\r\n=j8br\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "0ac5fe1ad9008b4b141e9f32fb4d63a6478669ba", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-types.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "The ultimate javascript content-type utility.", "directories": {}, "_nodeVersion": "8.15.0", "dependencies": {"mime-db": "~1.38.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "13.3.0", "mocha": "5.2.0", "eslint": "5.13.0", "eslint-plugin-node": "7.0.1", "eslint-plugin-import": "2.16.0", "eslint-plugin-promise": "4.0.1", "eslint-config-standard": "12.0.0", "eslint-plugin-standard": "4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/mime-types_2.1.22_1550184458136_0.07147731380368416", "host": "s3://npm-registry-packages"}}, "2.1.23": {"name": "mime-types", "version": "2.1.23", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.1.23", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types#readme", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "d4eacd87de99348a6858fe1e479aad877388d977", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.23.tgz", "fileCount": 5, "integrity": "sha512-ROk/m+gMVSrRxTkMlaQOvFmFmYDc7sZgrjjM76abqmd2Cc5fCV7jAMA5XUccEtJ3cYiYdgixUVI+fApc2LkXlw==", "signatures": [{"sig": "MEUCIBiJMfltsur0Dqbg+edK1tBmOFYtyjFgyh8aCk3UDfS9AiEAoCFqqmuB3X50rZyNjnJjJjHhijtii+9CwPr+0514E/Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15698, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJct//rCRA9TVsSAnZWagAADUcP/1Sx8SZMb+Y0DvqR26zd\nsX74VQgoI4cXQmf1hh/jo2EUpvSK1xxz9H2fQKeF+HPreIhbqretsWfqh4Sd\nsbKVqusZ8xWMz1+Y1whWFzpGMeAe/Fkatka+YgHOsxA9RVcxVVkLCg4PR7ve\n1OxGIUtI5Eqhb7/UTcNY2ha+GwA/RgVnrImoTwNAucLpcaR8KCwpMCd+36nH\n/Rwz6v634o9dHZhvbId/kwpYaoKp3FACuGnmaydheqfv3bm+lqTvLpCCekD6\nTnXI5YJdQWEZp23MVqHCEL3/vYvcck2drlkD8cMLm6Z/6gBGIgvXLqcVnmvQ\nMcJ/MxzGEpaf51594zBAIXcj492IVSw0WN6W+vf0cRyLCqMPGFtRkzJfx/FU\nkp7dkjkpqP41yRWO42dBfSuqRzfPhNr1UPyLjFN6ZSc/MrzijVQKFY0cNmOS\nc81ZSp2RymrE9rJbyZ3DQv9AW4uVy0Was+39+NqtQTMJ6vh7xp+9nUKtnWFy\nQiES2KNAmIOIe4nbNavnbJiFUVzxFqP+NdsKRThXafzYSWJ8URakA3k3u2SK\nHTRbCr18uu+I1ZzZySbvPpE+L6ZzsL3ozbndpei7sDl6QUuHukIjpqH6+M/2\nYmkOnvpDjvNQPbPvqBcZ4zRdpZ3Gqgbu1eZNkwSHyo+tkTaUkZqsw+VlTGwC\nvHXB\r\n=ccqn\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "28b73337cb1aa1583b72d448f30e2f49d6f5f07e", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-types.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "The ultimate javascript content-type utility.", "directories": {}, "_nodeVersion": "8.15.1", "dependencies": {"mime-db": "~1.39.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "14.0.0", "mocha": "6.1.3", "eslint": "5.16.0", "eslint-plugin-node": "8.0.1", "eslint-plugin-import": "2.17.2", "eslint-plugin-promise": "4.1.1", "eslint-config-standard": "12.0.0", "eslint-plugin-standard": "4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/mime-types_2.1.23_1555562474870_0.5469095710208383", "host": "s3://npm-registry-packages"}}, "2.1.24": {"name": "mime-types", "version": "2.1.24", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.1.24", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types#readme", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "b6f8d0b3e951efb77dedeca194cff6d16f676f81", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.24.tgz", "fileCount": 5, "integrity": "sha512-Wa<PERSON><PERSON>3MCl5fapm3oLxU4eYDw77IQM2ACcxQ9RIxfaC3ooc6PFuBMGZZsYpvoXS5D5QTWPieo1jjLdAm3TBP3cQ==", "signatures": [{"sig": "MEQCIDnSwNQjYSbe8NZJNBA7kQVbgPH/o2y9AAAUKzbmtgkrAiA36WEVtao5ZaGOvsKLRfX48sI0IvNiZ+BmL+XFzgl8yQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15858, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcu+enCRA9TVsSAnZWagAAZDAP/131rZIx2LnrEtxcAVE1\ntwbiuGeMaYMICmK5M+sbC810UK7KyRpukkRs+nWlvSp4CEF5it0NcnKTDgrG\nsxhl4T1TuaYVq9T3WuI+GOP6462yBna6aZXss0obI9fS1V9h8c0K77JUK3mt\n5VATHB8jtNivRIWfUue17GZ5cj0wR528iDoLQwxhnXWm8RugwysWg+IbavVF\nampKZue/RHvOTcz8cLGdj0O4PP0niH1B3oaOCSf6JHoM7N2D+ymv843YSbLE\nGbHjfHRQ+UO5Fy8FTZNVd/6uVIW/VJJTrDqZwCuIzBCPMcEB8KKd0ZjkfVsY\nivEecHoT2BUgBBXs11CcRkUjZAesmmSMr187RO/Cot9DnosRu60dX/VW2Izb\nXyHuH8V5S61SVIOkb8GHtP9pYqYLa07qR5D1vw2kGnonBuCndkOIOYV6PXgv\n4b2OXxuHAmMY9f+HhfsKbZaHdxFcSFJ3KhvpfGyB56yl6dSz1fO0OcXhu2E1\ndLlQlWvVs8i5BtVkYXb+5Js5v5Yua4UhL5Piyz6SxogH8XTtDFQktztne5xE\nhHC7NqHIivWFjCKTSp7iDf0kaqNFwVvwaPGfcMKDp7PMb6qdwUyiSwvDAOES\n1htGf6DLsG9Q7pIXpkhfwyhCk6H1RE4v7lEKWa6wK106ygBUDccVWXw7X+dN\n32yl\r\n=h9nD\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "e5b5f41ef0d7e4e66eb9baeec7920c0cd9ec81f0", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-types.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "The ultimate javascript content-type utility.", "directories": {}, "_nodeVersion": "8.15.1", "dependencies": {"mime-db": "1.40.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "14.0.0", "mocha": "6.1.4", "eslint": "5.16.0", "eslint-plugin-node": "8.0.1", "eslint-plugin-import": "2.17.2", "eslint-plugin-promise": "4.1.1", "eslint-config-standard": "12.0.0", "eslint-plugin-standard": "4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/mime-types_2.1.24_1555818406742_0.4628586923544964", "host": "s3://npm-registry-packages"}}, "2.1.25": {"name": "mime-types", "version": "2.1.25", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.1.25", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types#readme", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "39772d46621f93e2a80a856c53b86a62156a6437", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.25.tgz", "fileCount": 5, "integrity": "sha512-5KhStqB5xpTAeGqKBAMgwaYMnQik7teQN4IAzC7npDv6kzeU6prfkR67bc87J1kWMPGkoaZSq1npmexMgkmEVg==", "signatures": [{"sig": "MEQCIFw+zp4whraZte+dGIDBH1N1uARzpC4WgHaj6OobSVWOAiBPJ9rSIX8K6tWSm/JYP9IkfKc5ZqMX5qMMoJazTLiCSA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16062, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdyr8pCRA9TVsSAnZWagAAQFsQAIs5oj9Qzx/oRzFtRYQ6\n994TJBdmn2s3giERKNeCoI+ZPmw11cEiOt4DLGGRZ2Q5Zd7zcCMQkJIfCcsS\nmSEgcwDh4gxn2uLM74W0TuB5Hk9Vy3150JFn0XR1T323G+4gB5Oc1TTHKEQh\nJOjEQOf7QM4raSdTEkF1YxLbaAlTHoZq54ScUJCo5mAHbEfL/D1wc5/zILJK\n2fSOcpzfRh+a3Vw8T/fM4dbsBXk82I3PFIX+yl27LK41Ft9jg5CkWNtRMjVo\nTrhYeuAJVRRObzavI7q7xbz2EBmKSr7LcofvDSmAagDYVe4pUrkP9H0sZUAR\noF/oBIRDW+UwzzrMOo0Dx6Oe5d21kNQcX5bs4fCZXfgJZ5STzV0iNtp9i4D1\noybPr2K2MMGEfptRbDytfLnRyFSq2TUxCYCzPCO3KRAX63hBNoeYyfLnU6UZ\niBf5MEcUKskhv74mRbalpg8nPl1/a2igceK0vfNnqWUjSFA1AfLOgveZgsna\nXV2iVghx4AMBL0N3jP6EUsUYrnqQnDCRcyBE/mBno8d6X14lCXZPQkE7il9X\ntRt2ZVe01UmuIPzQASmn8k/u5m+xXjbLmrio0Wjq4Tsq66Jvh54qnjiK8Gm7\nQ724kz5XoULTQXqJNLAaKNOl75ubN44aQfFbfxUhou6n+tF5gQJ9xtsdasPN\n31r5\r\n=VcRg\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "7aaede09275fb002f7bbd63060ed4c3a98575b9d", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-types.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "The ultimate javascript content-type utility.", "directories": {}, "_nodeVersion": "12.12.0", "dependencies": {"mime-db": "1.42.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "14.1.1", "mocha": "6.2.2", "eslint": "6.6.0", "eslint-plugin-node": "10.0.0", "eslint-plugin-import": "2.18.2", "eslint-plugin-promise": "4.2.1", "eslint-config-standard": "14.1.0", "eslint-plugin-standard": "4.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/mime-types_2.1.25_1573568296656_0.4870118202385958", "host": "s3://npm-registry-packages"}}, "2.1.26": {"name": "mime-types", "version": "2.1.26", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.1.26", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types#readme", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "9c921fc09b7e149a65dfdc0da4d20997200b0a06", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.26.tgz", "fileCount": 5, "integrity": "sha512-01paPWYgLrkqAyrlDorC1uDwl2p3qZT7yl806vW7DvDoxwXi46jsjFbg+WdwotBIk6/MbEhO/dh5aZ5sNj/dWQ==", "signatures": [{"sig": "MEUCIQCtXf0UK4C7N/yRvpCjmX8DIfhmDJTTSwAPNnXe6o/kZwIgLOSivqF0gySyHrmkGLLkyvahxExw6S7Hvrp17p5uvbw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16332, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeEq3rCRA9TVsSAnZWagAAA8gP+wc64WF1FCVIqdKE+1lh\nEOIJKGWOzbGMNLFAM8ksv/d1Qr9SlOiN2yOc15gt95xrLiacgeB+Gz9V93NC\nwgmzm8kUvDmM6qaLZwfY9q3A6wn3f4fOA75Cj/Xj4k1bZuzeE8wCGMv8r99R\nmgWacq5ptU4Y0kO1/cUK+VCRFK5GL0gbCNUS3phdFfSxWAQO+Gj08eSECD+V\nuLx+0pb4UI17s4C2ZLKIJJIWQSAH/8bYI5MflHF4dC5ZS5ar3oXtPAorQ9Xe\nCIEeg1oN8p/+aWFK/uu2kUmNBAYmfSqwFVPLUgqSfvY+4EACXrR5hxa9n3Mz\nj5tbxoxjURhDeFOPgdZL34QKVpil/QyZ+V4QZ4iKfKpR3el3Gl5UBEDqP5BV\nK6MrticiP8Gg5kR17msv+4HG2bJN7RmMMhYKvBxdpIqljs3UfJEtKRHduDVb\nZ3m+F8YDpThYFcJ108wRb+HerQNY8q2xzBppxZ0ndF+icVdVrxm5ZMfrPmQJ\nwF3zuarhw+YE6Ryga8JVHn32qXubycJXopNWD3TBcT73540Wc5LIb4Zr7038\nS8mwTJA6OYiUEEplYHTqXJEj4b433djytwzLtUdgtKAuUYECphahF+/dx+/c\n35a5goj9MZ5T68or3kRgXFin+dQ6hKB5ZAxfdxFpv/ovsB3WIQC9cWFFaeLH\niaRj\r\n=EtV3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "73f9933bfa5247337b459240ec67ea6045cdec84", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-types.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "The ultimate javascript content-type utility.", "directories": {}, "_nodeVersion": "12.12.0", "dependencies": {"mime-db": "1.43.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.0.0", "mocha": "7.0.0", "eslint": "6.8.0", "eslint-plugin-node": "11.0.0", "eslint-plugin-import": "2.19.1", "eslint-plugin-promise": "4.2.1", "eslint-config-standard": "14.1.0", "eslint-plugin-standard": "4.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/mime-types_2.1.26_1578282475037_0.17516931585429973", "host": "s3://npm-registry-packages"}}, "2.1.27": {"name": "mime-types", "version": "2.1.27", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.1.27", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types#readme", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "47949f98e279ea53119f5722e0f34e529bec009f", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.27.tgz", "fileCount": 5, "integrity": "sha512-JIhqnCasI9yD+SsmkquHBxTSEuZdQX5BuQnS2Vc7puQQQ+8yiP5AY5uWhpdv4YL4VM5c6iliiYWPgJ/nJQLp7w==", "signatures": [{"sig": "MEUCIQC0wUYtHRp1p0qLUchlbTTzL8JeMa29dScmOWX53yjF7gIgIri+dyG7yTLLJbEBVEhbhStAkheE6ScTla1q5wOiW5Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16721, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeol6sCRA9TVsSAnZWagAAb7gP/2rhSx5SYxg68mROGmpc\nK4P6pyMxslHCfxYa4EUnJ4NmZAJKoGAeqLNxf/ty5QFlczy2kATAx6Ed/Jo9\nev4jGBiHbZ3WiTCdG9iE43fP4uKRoZ0a19suEC9t4mmx0y3VaBqGNIlEZgQM\nUZKLhbMYu8PEmA4FeFVcKOzEQVmMaNPSek9iaSNgxyD8nL6O+qEhTtH/1BHV\ng59VWglcXaKsK7sQGM4Ljn+bXqDgQhNpZqF4zoONKcxb7TrYp/NESCKk+Qdi\nlEBCaXdT++/UwnRh0p+nffllnurrE7ctt76LCJP8W9ObMqKSpmT4yQmpDp/J\n8hoRyCK+Z2Ktb8ZWkM5chHAiL9hrQVKzhKB2Cbr72itq7E+5Umv0Otis2C6b\nc7vIbbMtuui4fy7ZAo1ywoMT53jyAkqesJHjIlkBMlXoSIm3eyzKuQXDsJ1C\nRjhI6ik8j7rViDQ18TCyFeSZZw6LxB0gqTRuvLz+VbDVSLNoH6+OaY0bl62s\n4iWL4xTqm8xFsyewzt8fl7H7nIEfETjcEwcaKljjyfncjLla5iXVXOAvZ6dR\ncdLA1fMFH3YNEjVgR0g5elVHQNk6+VdN5c4lZ7vRPc3NJCTcljyTegiWCK2U\ni6/aVv6I3FiCSkegYy/E02P0UW6OcmecrMY5gnenMtAG5lqlKBHOL06aegiA\nSD9R\r\n=th56\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "47b62ac45e9b176a2af35532d0eea4968bb9eb6d", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec test/test.js", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-types.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "The ultimate javascript content-type utility.", "directories": {}, "_nodeVersion": "13.12.0", "dependencies": {"mime-db": "1.44.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.0.1", "mocha": "7.1.1", "eslint": "6.8.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.20.2", "eslint-plugin-promise": "4.2.1", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "1.0.2", "eslint-plugin-standard": "4.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/mime-types_2.1.27_1587699372290_0.7009359023949084", "host": "s3://npm-registry-packages"}}, "2.1.28": {"name": "mime-types", "version": "2.1.28", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.1.28", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types#readme", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "1160c4757eab2c5363888e005273ecf79d2a0ecd", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.28.tgz", "fileCount": 5, "integrity": "sha512-0TO2yJ5YHYr7M2zzT7gDU1tbwHxEUWBCLt0lscSNpcdAfFyJOVEpRYNS7EXVcTLNj/25QO8gulHC5JtTzSE2UQ==", "signatures": [{"sig": "MEUCIQDCzQqPiWMTcBG0s7toRWSxAET4EYdWYE8lVHvIOlqKUgIgH0nVwO5kQfb6KeF0AsaQjjAqckK1c3otA+C/HbhjAEA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17239, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf7/aLCRA9TVsSAnZWagAA4b8P/0hpf52i4C88kB3UA8oz\npo/u9QkTi05T9dS+KahT/EqupImZwY9PmtPHg2OPs/gEWUySbIAC6zQAgE2m\n639o96AGgIYqdqYVNJOFU/H9W5knxn3bi0des3LUpxKCyCIQbdHkHtjiAaHV\nN0HLoNjQ+lGFfB2zjdBxbxPdeqJZ/n0LpVtnUgQUAeX/+oPOoaS6gREobICc\nORfHLB+kCdXT7eHtCX3ztdrxvZF2qbyOgprXqwO8TX/25Ncx2ZzT55kMGX/0\nOUlHtJzeht0pI0vfd2MBrA3F4pd5kkGxKclwZQ2wro3c35HJja1V/wcdLJZH\nHwivDxdjrOWGwyVWj1OYBCkKn8dR5uaPygyKaSbC6TL/cMU8ixCPbeYLz6O+\nF9zJWkky52bp+3L1MmCmFRTE379zdCGVN7Pu2hnb+EawOsUFm8SghsSlXWOX\n+ep6VOVeu8c04Ztg2cVZigImdQAKcw0yh8UgkAruuZ2vwDT7oFLKzlcqaZxN\nui5EyRsfwB8gQkVyPvQ38VyE6PO0YD9UgJWgva++zgA88MrP7XIOH63zraYP\nAM5vbJfYjNbGuAsIBb1jJ+ZXD/JPMyI36155m4JpF5QcMrgjMr6yyOzNALpI\nJxFz02YU4C3YVeteQpBmFEJi+lA8WM+xkf0ksxr7I6anWC2+v3Aj6hivIGGL\nDmhi\r\n=Y1QJ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "4a4f710d9f0c59fc998e444cb70df02d79b4f932", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec test/test.js", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-types.git", "type": "git"}, "_npmVersion": "6.14.6", "description": "The ultimate javascript content-type utility.", "directories": {}, "_nodeVersion": "12.18.3", "dependencies": {"mime-db": "1.45.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "8.2.1", "eslint": "7.17.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.22.1", "eslint-plugin-promise": "4.2.1", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "1.0.2", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/mime-types_2.1.28_1609561739020_0.7011492525542153", "host": "s3://npm-registry-packages"}}, "2.1.29": {"name": "mime-types", "version": "2.1.29", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.1.29", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types#readme", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "1d4ab77da64b91f5f72489df29236563754bb1b2", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.29.tgz", "fileCount": 5, "integrity": "sha512-Y/jMt/S5sR9OaqteJtslsFZKWOIIqMACsJSiHghlCAyhf7jfVYjKBmLiX8OgpWeW+fjJ2b+Az69aPFPkUOY6xQ==", "signatures": [{"sig": "MEUCIQDQsXtjFyYVD0dvBCFYEG/qNx6AKc2c6pseBChTmwRS1AIgIyusCktQ1//5jC+0/dP1wQMx4fxe04MdSkgBtjmdqEY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17269, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLbXsCRA9TVsSAnZWagAAN8QP/0fQuVlbabyn+AqL6W9j\nlB7SUvGTIW9kblMg1EcbOhUJLPgWPr1YztDkqy4s08ajnvz/ZPTNTImaaH4J\nT+OGx/aZ5YWRFGm+Q9rJKEs0hfmsr7b7xd536VDU6vdmTVT4WXPZZxmIKLDr\nY7TgpgIvS1gGOnRInsQYAuKwJyx9o91BO1j6OKFpI/P2+LAmvBm0HXuH5W7/\nvziN3ezjuPla8SGlm3aIDILKt/l0ygf9hp5Nkfz5CjBc9t/V4dfI/uZmBgiO\n+AuVQs2szigH8dKmRAYseJg7qb9jxv8ea5MH5XyDK6PdPGEaiTvgF0f023XC\nghsN4H0g7Wab6ZTm6g2HaYZ6I/PLHeVnCF4mbDjkqD0vUt9yUva1RnRucU8B\nUz7Z88Q3I1swcD57Q6Ubn08Ay4FOtstD2R0SPBycUCaUyeoJZgP319GDZZ1p\ncCh7TFBos9NCcYm63Ot94k80hahmYvhBnPajQWs1LnRoGRGRkfKLLvxFOgmp\nhU+H6z3mGrah5A0vLMGVJFnx6+NZYDAB8wQbn3P6vTIB5EIUAB2aE1+3gebp\nLapTgJbQJ1rd1zbMwGQCGs+3Iwbm953eGsKy7bgJisZQ42wvbfm04gLlFEEH\nLwJQAPEDHnuYwxhibgWVAtgnRx2uyqqlCNvzxrbyB6TQbgofp65/m9p9jow8\ncN38\r\n=DBai\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "9a05a599f071203318ab2c3d848c6a6e46a59fe3", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-types.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "The ultimate javascript content-type utility.", "directories": {}, "_nodeVersion": "14.15.4", "dependencies": {"mime-db": "1.46.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "8.3.0", "eslint": "7.20.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.22.1", "eslint-plugin-promise": "4.3.1", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.0.0", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/mime-types_2.1.29_1613608428286_0.45392395861166324", "host": "s3://npm-registry-packages"}}, "2.1.30": {"name": "mime-types", "version": "2.1.30", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.1.30", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types#readme", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "6e7be8b4c479825f85ed6326695db73f9305d62d", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.30.tgz", "fileCount": 5, "integrity": "sha512-crmjA4bLtR8m9qLpHvgxSChT+XoSlZi8J4n/aIdn3z92e/U47Z0V/yl+Wh9W046GgFVAmoNR/fmdbZYcSSIUeg==", "signatures": [{"sig": "MEUCIDkIFQTGAwxTzTK6MtyfGlilHWj9MAMetw0NyxUQCTu4AiEAlPyYmIM4ENhcq1XTPJWwHGKFt7xSOBMmDqetl1JENcA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17521, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZqZUCRA9TVsSAnZWagAAs9UP/A5iuknBRjYcu8Mq+IGc\ncNK6EPRoemRc7dH1qtqpqYyMNXmRPaaysMaxccbmaQ0t5L9O/e0QFqsDHxSY\n5+/RsmiyzUlQIANV6URMRP1vdIp+H4tUHSvfAiZq09PFmBGsTgtDMsJ3mSgc\nb/LsfBu7EKr2nNdwc1wM9BJ6RT5e0j5oZeEsawJpYDabfrkEkG+LRtmLhQVt\nByP+JgMTaIFpyDh3NCbACp//gVsDebzVXAhaaDPGXbvT8Ix7qtr0MgQO8CBF\npb7x4Wz25B93SwzuE+SiasEt+xjn/t1spyjyL1SlRHbGfy6ddkPOeUd/08sQ\npkzQ63MJ7JwC5fn1MFhJiGyk0WFqBikVe6f9qJTjuEfmkiq5DNvQ7QT10r92\n9P8wpobVwnngmq00yXO88KpzAOvWo8L+Yam6A5TrwwaMUyo8Z20yQE8sSapk\n/p6Rr96hlhtOKN0Ajc5c15WTxrDfKCBPOf5XQYTbTgLuFn5X4p+uWQI+HDmV\nbqG/pJ6i0tPmfA7cJ55nkUt5+oLnkSHfp/k/Uw6pD52l9POYKTUaZmTb6kTP\nqzBsUGV9JXOZ8RS1eC2C44+VDUGYV/5n6B/aUdRSxoDNyOA55Eq6jQc/G9Ry\nOVmTYwP5uF1G1yAU0SrunshnJzhHCuXsK7hba9F9JCF+c5fD35zjSC4Yujjh\nvj5s\r\n=G7h/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "b0abe0c67c6654cb9a2efb9a92c776eb160d7b27", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-types.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "The ultimate javascript content-type utility.", "directories": {}, "_nodeVersion": "14.15.4", "dependencies": {"mime-db": "1.47.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "8.3.2", "eslint": "7.23.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.22.1", "eslint-plugin-promise": "4.3.1", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.0.0", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/mime-types_2.1.30_1617339987950_0.13379782228732684", "host": "s3://npm-registry-packages"}}, "2.1.31": {"name": "mime-types", "version": "2.1.31", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.1.31", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types#readme", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "a00d76b74317c61f9c2db2218b8e9f8e9c5c9e6b", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.31.tgz", "fileCount": 5, "integrity": "sha512-XGZnNzm3QvgKxa8dpzyhFTHmpP3l5YNusmne07VUOXxou9CqUqYa/HBy124RqtVh/O2pECas/MOcsDgpilPOPg==", "signatures": [{"sig": "MEQCIHW2PDMM0nd3Z3wuCr4z9jRlMB0og3wKe0cjkU0fBbLFAiBh6rBUgAtX1Hf51hXoh8ZIKkZPsrupAwAgfCYh7lb5XA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17728, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgtm5kCRA9TVsSAnZWagAAqdsP/RsukuEKfoJXgXBQiB+q\nZIdiG03Vw4Frf6QHmUu39wovbuuSEor5tKyhBbdXXr3+7rCjLsBSukg3G13j\n3KXtkAOhxMQ2Wtcw2KkzmJQUDNSj38bZkaA2JqOnlH7IcPY+LPI4l53vtknb\ndKGJ6pXsZbWni+Lp4AjNHvLyqeGtHO3bMf4bAErSoXvHWC+TGtcFg+Af4BXj\nnDq2wKKGnMJSExKAg6uygAnq+o7rZ63/b1rH5rx8XzRwdv7DEVox6xlLBl3W\naXw2CXWjbtMIsCfPPbxuddjnI1689cKZP/9eL242HKOM0/7W+rxKu1+G/VnG\n0vGmIrVEzwF3XtnFxJjCvN8Ph3+i4Oti/A6hUmBq+C/GKI8Mi+beT7xF0WhD\nnPxxxzsTKGd8XWq4IreSMEca/kQAbM9RDFJ3Natl8cz0gL2g1w/ka5q+yi+O\nkM5yCvwRopqqmlNdlLqNrZ7wSpTXmYtu9d4ZGq9ESDPjrXSOvMiuUnyFyryj\nurJCWtIuIaqCSDbQH1kTIBd4CUoPqjuzhy5334S2aFGai//vDCgnKuxy6mge\nOZTLJK2diCT4w9yq+sQavNpgRBfzclMgzQh6EM58h/1HSotCH+OTqScsgUnr\nD0fpWAedc3V8sXdcVhit0JbWl3ntfjXRjJtWuuhaFnEE45kMU+gv9BABpG9w\n8EVm\r\n=ZZke\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "cbeaf522ad6f7b51600bd9ec5ee8e7a968e2e18b", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-types.git", "type": "git"}, "_npmVersion": "7.11.2", "description": "The ultimate javascript content-type utility.", "directories": {}, "_nodeVersion": "16.1.0", "dependencies": {"mime-db": "1.48.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "8.4.0", "eslint": "7.27.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.23.4", "eslint-plugin-promise": "5.1.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.0", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/mime-types_2.1.31_1622568548229_0.7284665852367624", "host": "s3://npm-registry-packages"}}, "2.1.32": {"name": "mime-types", "version": "2.1.32", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.1.32", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types#readme", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "1d00e89e7de7fe02008db61001d9e02852670fd5", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.32.tgz", "fileCount": 5, "integrity": "sha512-hJGaVS4G4c9TSMYh2n6SQAGrC4RnfU+daP8G7cSCmaqNjiOoUY0VHCMS42pxnQmVF1GWwFhbHWn3RIxCqTmZ9A==", "signatures": [{"sig": "MEYCIQDylXjqfOz0J4YJ3AtqePTK1prsvB1+yMYTRN4Zs9TxfwIhAIvKhaKirfbkisc/gPBEuhOL3LD72PZb/Yw7a3U4YRY+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17879, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhAETGCRA9TVsSAnZWagAAGiMP+wYFvintggJN99pE8Mlw\ns0IfZ6uMHGxlyHwQsWLfxaP0pph81W4dPwBfNuBOsmZSqkBQEj3cmf6usxEv\nq8FhC6kNYmOP8gXpgsTZSCnDUbOF5f6XgMjrb4CnQ8JHosxRINMGtAYhzLgZ\nHKxxWP3gF5JZ2KRCA28MNY9tPuh8PoCpshTs7/r6ZokMQWem2lPdSM4MicSY\nty9IHRQ0zvz63T4EapeRHpPiHMJCy/YdT5HRPZEoA7WpJgosnS7mV4xWvoeC\nK5wRx7JwOhTzXl8MGhhiAaZdya0IyEuqHnCowzns274bzF2UpDPRgZ8RmcEf\nBKJdC/l7+fODW9M3CVbfpQjB76/N+SHDg9BdgM/NSvZWF6OhwRi8T55SqbOC\nDtieITMHY13qItRJQD4BXX1O3ZO3oY6ByZY4mvFEcCrs+CN0ep+hcPT0elZi\n2KA4UIdxw06KRCWZnmIjiaMVBbS7whaerJ9FOK+1n8G6+oTdKG04babjU0u9\nqXlzAkVd/zax8emFHnRN4N9fpm5LGGKCwZ+bRJV3fa9vxHYoXtRB05U6Yht7\n9EZ6ga9Y+uggU3z00yjJ1NFH1KeFQYsriU4/KKScRqtvnOXIqhR5j8Hg7sVr\nvaI8pbcAyekPUUsvHr9ohJUpwnEBSErxp9ALuQFXPFjL06wFkfHoJEEozRqW\nvmKc\r\n=FOl8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "0cc1e563988dc69ffc9fa642a881e2cf7eef55c9", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-types.git", "type": "git"}, "_npmVersion": "7.11.2", "description": "The ultimate javascript content-type utility.", "directories": {}, "_nodeVersion": "16.1.0", "dependencies": {"mime-db": "1.49.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "9.0.3", "eslint": "7.31.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.23.4", "eslint-plugin-promise": "5.1.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.0", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/mime-types_2.1.32_1627407558456_0.18706801791640593", "host": "s3://npm-registry-packages"}}, "2.1.33": {"name": "mime-types", "version": "2.1.33", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.1.33", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types#readme", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "1fa12a904472fafd068e48d9e8401f74d3f70edb", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.33.tgz", "fileCount": 5, "integrity": "sha512-plLElXp7pRDd0bNZHw+nMd52vRYjLwQjygaNg7ddJ2uJtTlmnTCjWuPKxVu6//AdaRuME84SvLW91sIkBqGT0g==", "signatures": [{"sig": "MEUCIQDq92rsYYuczr7cbuZFLE6col2gidrF5mmTrIljHjjdoAIgD5eUWLXSD2NkBdZHtk7khcGl0hvyTi+rrw4JLgWnzHc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18034}, "engines": {"node": ">= 0.6"}, "gitHead": "c6ff9b224577f0cd49f1155f421b24c24a57bc3e", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-types.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "The ultimate javascript content-type utility.", "directories": {}, "_nodeVersion": "16.7.0", "dependencies": {"mime-db": "1.50.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "9.1.2", "eslint": "7.32.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.24.2", "eslint-plugin-promise": "5.1.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/mime-types_2.1.33_1633108162751_0.35499888332958607", "host": "s3://npm-registry-packages"}}, "2.1.34": {"name": "mime-types", "version": "2.1.34", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.1.34", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types#readme", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "5a712f9ec1503511a945803640fafe09d3793c24", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.34.tgz", "fileCount": 5, "integrity": "sha512-6cP692WwGIs9XXdOO4++N+7qjqv0rqxxVvJ3VHPh/Sc9mVZcQP+ZGhkKiTvWMQRr2tbHkJP/Yn7Y0npb3ZBs4A==", "signatures": [{"sig": "MEQCICrS4/CeTGb/xoKmJYyMNY4DKY/kwAcjBaRdgjOYeY9bAiARG0M4E4wnyXjl+NmfjJ37G8zQq42Hnvdu0QxkyapmaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18023, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2zP6CRA9TVsSAnZWagAA1x8P/ifj4ONj3DOWsbTa1NpW\nMg27qCdjH+Vuf+GkffHvrz/QB0/JTzp5bHCzujkH6/jFC4tTQWZPiiEwYHwM\nUij+WwjePeVSqaE+llVVQGh6JLiQ82I+imby/V5+k/AYU/vrnRioZJr73xlq\nnaPtMu7RpW7GJaQnNd8BCWjGSnN0q6IVhf+zySC3j8cet6iCo8kZRkoZtVWX\nZRWz74XQUOdA0gAmH+q2GlcLRZwz1+wUU2cl3U01dJ2r5sAdQk5beWdZNErx\nDmU2SDyAvTWxjs+Nic27jPmHJMtnJZBPRgWiuHgx8QNln8cHcpfo0yeKiheQ\njGeh7sSmOSLhVmgBu8AjnjXAKFHtUp5qKQeOg4ka5o+I3hYjtdoxzcfonZbF\n2jPhFHQQwPeJ/thAV2pEpNUCrsajOwGIMVmtVAuYnCv0mZU36qwGuiCk1sT9\nZ5zPsS2LnrjTVzO/Tvr0EwIFwn77KCrqmGBWIc/dqAoQnArkFghDugIqrV9y\nJLacPAGONVJAw1+xOo7Z+lwdZid6M8zXpepg/b2n8uF4uc4Aw7NIDjdpn0dU\nr7XiAkBa0C6znCpCVjFi0ITX9woYJv53aREXjG5X3MqlOeBbZDzz36m/+5YG\nQYrIt+ga/OGxvZVcYsTynZjh2qoaD+CoI0n6UF4uJEK0dRYlbVKEkexeD93G\n4DEM\r\n=pCSp\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "a50dafc08c7d1969ba581a51de5d94493b713bef", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-types.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "The ultimate javascript content-type utility.", "directories": {}, "_nodeVersion": "16.7.0", "dependencies": {"mime-db": "1.51.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "9.1.3", "eslint": "7.32.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.2", "eslint-plugin-promise": "5.1.1", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/mime-types_2.1.34_1636432221719_0.7160929014759547", "host": "s3://npm-registry-packages"}}, "2.1.35": {"name": "mime-types", "version": "2.1.35", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@2.1.35", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types#readme", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "381a871b62a734450660ae3deee44813f70d959a", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "fileCount": 5, "integrity": "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==", "signatures": [{"sig": "MEQCIE0POPFsQ1njxVBqIqEP9T3OT48luQI5vMvaem7SelGFAiAqt9YWLmp/N3wfzUGgSr+0ZPqGVYT9H644ZN7yqpytxQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18272, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiLOC7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpT2w/9GeLyJAyqEq3ZT7nLLZUi4oEEIcrEj7wz8UgiRjyOJPFHZBFG\r\nBJltzgFWbOf62sJvn1ZekIt6kq2OecNFdVjLe1sRHUfE+Irf8Z3oCT9RAwZ8\r\n2JYz8jP7VQ1p6ZsK8gQ43Bc8sBlZB81coPyjCkmwGc8yNy1amT1LMVMU8PRd\r\nuxfMoN0vEis3nARzPlx9fb7KKqS7rFL1zAaSUKhDLomS/gNOLsLtjyrlQo02\r\nvxX43VLGJvK2Wpd7TD/qvGvdGQ5VSm++TUfW3tviXQHwP4Meiy8zU09+OYXO\r\n0+ij2TlSLls7zNoQOWiZgHJYUP0WAZW4doxm9Sk9hA5RLGn3ukgMydYjyq66\r\nZJUCpYr8p2Y+1LcUHEdmgwfqsUdhX55IwyH8DwvlPHf619eZz7dM9aOWtBHS\r\nYAXUuzdJFlem2ezr1FhH1y7A/yPyXr2pqyzx9Qxp/d2yuaSVU82KCa9l7oJI\r\nnV8lubKUFxlJgIbFjkoxmvnDt9q0+MTzntrznHIcuD2wgRRKuk5Ima5udd0o\r\nSy/mLV7inWSaq9dWWzdZpYZDwGve4tcMr8LNlweZ80H2SFWjJdkBhf22i2Gr\r\nKgwvndofh9GfV2hnBFqL6MT/nJDm67lHf/Q/9frsfArA9Pb2OhapTzHthg08\r\nnNpZRTFrJfHM03pS8bEc33gaAQupl0vL+Fw=\r\n=eKhF\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "ef932231c20e716ec27ea159c082322c3c485b66", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-types.git", "type": "git"}, "_npmVersion": "8.3.1", "description": "The ultimate javascript content-type utility.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"mime-db": "1.52.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "9.2.2", "eslint": "7.32.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/mime-types_2.1.35_1647108282894_0.3187771888128905", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "mime-types", "version": "3.0.0", "keywords": ["mime", "types"], "license": "MIT", "_id": "mime-types@3.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, {"name": "ulisesgascon", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-types#readme", "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "dist": {"shasum": "148453a900475522d095a445355c074cca4f5217", "tarball": "https://registry.npmjs.org/mime-types/-/mime-types-3.0.0.tgz", "fileCount": 6, "integrity": "sha512-XqoSHeCGjVClAmoGFG3lVFqQFRIrTVw2OH3axRqAcfaw+gHWIfnASS92AV+Rl/mk0MupgZTRHQOjxY6YVnzK5w==", "signatures": [{"sig": "MEYCIQCVk46UyrD+LHLM8l14XNi0V4ET7cNqJ0kGXNsmAop5TwIhAOPXSGh/qdfZo8etB72Tp/l54se3tl8bbPXse+Nc2Jdc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22277}, "engines": {"node": ">= 0.6"}, "gitHead": "4b3ae3831b18e1ce72af9ee75f691701aef8432f", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec test/test.js", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-types.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "The ultimate javascript content-type utility.", "directories": {}, "_nodeVersion": "22.2.0", "dependencies": {"mime-db": "^1.53.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "15.1.0", "mocha": "10.2.0", "eslint": "8.33.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-promise": "6.1.1", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "3.0.0", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/mime-types_3.0.0_1725112633991_0.3284378548950295", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2014-05-02T15:54:00.885Z", "modified": "2024-10-10T20:56:28.927Z", "0.1.0": "2014-05-02T15:54:00.885Z", "1.0.0": "2014-05-12T20:00:39.325Z", "1.0.1": "2014-06-24T20:45:58.288Z", "1.0.2": "2014-08-04T03:44:33.290Z", "2.0.0": "2014-09-02T08:32:26.198Z", "2.0.1": "2014-09-08T05:02:04.030Z", "2.0.2": "2014-09-29T01:50:31.565Z", "2.0.3": "2014-11-09T18:09:36.046Z", "2.0.4": "2014-12-10T17:57:57.684Z", "2.0.5": "2014-12-29T19:57:28.126Z", "2.0.6": "2014-12-30T17:41:58.705Z", "2.0.7": "2014-12-30T20:27:02.217Z", "2.0.8": "2015-01-30T04:58:29.008Z", "2.0.9": "2015-02-10T05:07:45.349Z", "2.0.10": "2015-03-14T00:57:09.091Z", "2.0.11": "2015-05-05T16:56:57.397Z", "2.0.12": "2015-05-20T03:10:38.358Z", "2.0.13": "2015-06-01T04:37:42.299Z", "2.0.14": "2015-06-06T22:55:10.695Z", "2.1.0": "2015-06-08T03:53:55.197Z", "2.1.1": "2015-06-08T14:30:21.703Z", "2.1.2": "2015-06-26T02:31:38.715Z", "2.1.3": "2015-07-13T22:21:31.913Z", "2.1.4": "2015-07-31T01:37:15.784Z", "2.1.5": "2015-08-20T18:14:08.205Z", "2.1.6": "2015-09-04T00:14:46.978Z", "2.1.7": "2015-09-20T18:44:04.903Z", "2.1.8": "2015-12-01T05:55:42.054Z", "2.1.9": "2016-01-06T17:52:14.411Z", "2.1.10": "2016-02-15T22:27:21.187Z", "2.1.11": "2016-05-02T05:02:47.292Z", "2.1.12": "2016-09-18T22:23:37.122Z", "2.1.13": "2016-11-18T21:39:28.247Z", "2.1.14": "2017-01-15T05:29:02.108Z", "2.1.15": "2017-03-24T03:55:55.438Z", "2.1.16": "2017-07-25T02:42:39.330Z", "2.1.17": "2017-09-02T03:26:34.147Z", "2.1.18": "2018-02-16T17:34:21.420Z", "2.1.19": "2018-07-18T05:29:59.712Z", "2.1.20": "2018-08-26T22:07:10.102Z", "2.1.21": "2018-10-20T03:37:07.996Z", "2.1.22": "2019-02-14T22:47:38.276Z", "2.1.23": "2019-04-18T04:41:15.063Z", "2.1.24": "2019-04-21T03:46:47.001Z", "2.1.25": "2019-11-12T14:18:16.771Z", "2.1.26": "2020-01-06T03:47:55.198Z", "2.1.27": "2020-04-24T03:36:12.387Z", "2.1.28": "2021-01-02T04:28:59.153Z", "2.1.29": "2021-02-18T00:33:48.436Z", "2.1.30": "2021-04-02T05:06:28.101Z", "2.1.31": "2021-06-01T17:29:08.384Z", "2.1.32": "2021-07-27T17:39:18.598Z", "2.1.33": "2021-10-01T17:09:22.903Z", "2.1.34": "2021-11-09T04:30:21.904Z", "2.1.35": "2022-03-12T18:04:43.042Z", "3.0.0": "2024-08-31T13:57:14.134Z"}, "bugs": {"url": "https://github.com/jshttp/mime-types/issues"}, "license": "MIT", "homepage": "https://github.com/jshttp/mime-types#readme", "keywords": ["mime", "types"], "repository": {"url": "git+https://github.com/jshttp/mime-types.git", "type": "git"}, "description": "The ultimate javascript content-type utility.", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://searchbeam.jit.su", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "maintainers": [{"email": "<EMAIL>", "name": "b<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>dd"}, {"email": "<EMAIL>", "name": "ulisesgascon"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "jonathan<PERSON><PERSON><PERSON>@gmail.com", "name": "jongleberry"}], "readme": "", "readmeFilename": "", "users": {"arniu": true, "eyson": true, "laomu": true, "r3nya": true, "shide": true, "svell": true, "dankle": true, "glebec": true, "h0ward": true, "quafoo": true, "shaner": true, "sitnin": true, "anoubis": true, "flyslow": true, "gollojs": true, "hagb4rd": true, "maheshj": true, "marinru": true, "noderat": true, "wgerven": true, "xtx1130": true, "zolrath": true, "buzz-dee": true, "byoigres": true, "esundahl": true, "maxogden": true, "moimikey": true, "wisecolt": true, "xgheaven": true, "chrisyipw": true, "cspotcode": true, "emircanok": true, "finnhvman": true, "jerkovicl": true, "ldq-first": true, "mastayoda": true, "mjurincic": true, "mojaray2k": true, "papasavva": true, "qingleili": true, "snowdream": true, "byossarian": true, "davidbraun": true, "gld1982ltd": true, "goodseller": true, "jessaustin": true, "rocket0191": true, "shuoshubao": true, "codeinpixel": true, "flumpus-dev": true, "galenandrew": true, "xinwangwang": true, "zixinliango": true, "ghostcode521": true, "iori20091101": true, "zhenguo.zhao": true, "ivan403704409": true, "mdedirudianto": true, "pablo.tavarez": true, "robinblomberg": true, "danielbankhead": true, "imaginegenesis": true, "jakub.knejzlik": true, "germanattanasio": true, "marcellodesales": true}}