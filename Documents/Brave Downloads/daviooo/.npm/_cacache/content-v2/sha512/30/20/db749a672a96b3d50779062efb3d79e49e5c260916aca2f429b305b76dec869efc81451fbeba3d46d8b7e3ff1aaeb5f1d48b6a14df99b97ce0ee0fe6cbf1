{"_id": "isarray", "_rev": "28-bea0d0cbc3d827ee452c01d1568fb4fc", "name": "isarray", "description": "Array#isArray for older browsers", "dist-tags": {"latest": "2.0.5"}, "versions": {"0.0.0": {"name": "isarray", "description": "Array#isArray for older browsers", "version": "0.0.0", "repository": {"type": "git", "url": "git://github.com/juliangruber/isarray.git"}, "homepage": "https://github.com/juliangruber/isarray", "main": "index.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {}, "devDependencies": {"tap": "*"}, "keywords": ["browser", "isarray", "array"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "_id": "isarray@0.0.0", "dist": {"shasum": "643ef22a01dd5b244411c2afedd4347489fb392e", "tarball": "https://registry.npmjs.org/isarray/-/isarray-0.0.0.tgz", "integrity": "sha512-HCR8/V314rUpAmD4us1VAw1KOax3FVCJLPzqrujO2u3S4pB5coi5fFuKQm7TBleebbyKrBHE8GvqMh2RUT4ybA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC9ykCD4yD1B2CO2LSw7/dlefkeGq7qrOha/P9ZBPDfVAiEAsDUhPZZLr6kdW3f5lle1JfBwjz50oa0mpJA7/FGHXys="}]}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.0.1": {"name": "isarray", "description": "Array#isArray for older browsers", "version": "0.0.1", "repository": {"type": "git", "url": "git://github.com/juliangruber/isarray.git"}, "homepage": "https://github.com/juliangruber/isarray", "main": "index.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {}, "devDependencies": {"tap": "*"}, "keywords": ["browser", "isarray", "array"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "_id": "isarray@0.0.1", "dist": {"shasum": "8a18acfca9a8f4177e09abfc6038939b05d1eedf", "tarball": "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz", "integrity": "sha512-D2S+3GLxWH+uhrNEcoh/fnmYeP8E8/zHl644d/jdA0g2uyXvy3sb0qxotE+ne0LtccHknQzWwZEzhak7oJ0COQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBPfZAGPIzb1jvl1OJJddOoX1tlU58b+7JShsEyOXi8zAiBdSQG5MPTABKb+Y2liFTp6O9g765DGhL4TVA1aYH01AA=="}]}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.0.0": {"name": "isarray", "description": "Array#isArray for older browsers", "version": "1.0.0", "repository": {"type": "git", "url": "git://github.com/juliangruber/isarray.git"}, "homepage": "https://github.com/juliangruber/isarray", "main": "index.js", "dependencies": {}, "devDependencies": {"tape": "~2.13.4"}, "keywords": ["browser", "isarray", "array"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test.js", "browsers": ["ie/8..latest", "firefox/17..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "scripts": {"test": "tape test.js"}, "gitHead": "2a23a281f369e9ae06394c0fb4d2381355a6ba33", "bugs": {"url": "https://github.com/juliangruber/isarray/issues"}, "_id": "isarray@1.0.0", "_shasum": "bb935d48582cba168c06834957a54a3e07124f11", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.1.0", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "bb935d48582cba168c06834957a54a3e07124f11", "tarball": "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz", "integrity": "sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDAUFha3cw0zrMmy/gRN+em7pQTK/Th9NvZu629sSQJEQIhAOA18G6lEgwD6Vu7/NsqFDjSDBx0WIS23RbPOBWbtgx+"}]}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "2.0.0": {"name": "isarray", "description": "Array#isArray for older browsers", "version": "2.0.0", "repository": {"type": "git", "url": "git://github.com/juliangruber/isarray.git"}, "homepage": "https://github.com/juliangruber/isarray", "main": "index.js", "dependencies": {}, "devDependencies": {"tape": "~2.13.4"}, "keywords": ["browser", "isarray", "array"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test.js", "browsers": ["ie/8..latest", "firefox/17..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "scripts": {"test": "tape test.js"}, "gitHead": "5ccb3ceb49561cd262ff596994e2aa0dfad94da9", "bugs": {"url": "https://github.com/juliangruber/isarray/issues"}, "_id": "isarray@2.0.0", "_shasum": "3ac1d0b7cb66703a6aa1feaa92aedcb49b5ebf53", "_from": ".", "_npmVersion": "2.15.8", "_nodeVersion": "4.4.7", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "3ac1d0b7cb66703a6aa1feaa92aedcb49b5ebf53", "tarball": "https://registry.npmjs.org/isarray/-/isarray-2.0.0.tgz", "integrity": "sha512-+7GtYSNjbLkjIGQy5ucmTjyY9ANbbW77DOT6nbQN4ZDo3CnST3HtjnGowbi+NF2pCPCba1rLO9qLlrFBsvDeew==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDU4MsIzRsmyBCYNs5G2VctL/4NgsFZTCNA60Z/ii9zsgIgPv5LRYjtZLuzL4NxjxcWqwUM/QwgKwbD3/qFgzbU1RM="}]}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/isarray-2.0.0.tgz_1474735671571_0.3229408422484994"}, "directories": {}}, "2.0.1": {"name": "isarray", "description": "Array#isArray for older browsers", "version": "2.0.1", "repository": {"type": "git", "url": "git://github.com/juliangruber/isarray.git"}, "homepage": "https://github.com/juliangruber/isarray", "main": "index.js", "files": ["index.js"], "dependencies": {}, "devDependencies": {"tape": "~2.13.4"}, "keywords": ["browser", "isarray", "array"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test.js", "browsers": ["ie/8..latest", "firefox/17..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "scripts": {"test": "tape test.js"}, "gitHead": "d7f00583d8f58215cea94a81de85c6b4d33bead2", "bugs": {"url": "https://github.com/juliangruber/isarray/issues"}, "_id": "isarray@2.0.1", "_shasum": "a37d94ed9cda2d59865c9f76fe596ee1f338741e", "_from": ".", "_npmVersion": "2.15.8", "_nodeVersion": "4.4.7", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "a37d94ed9cda2d59865c9f76fe596ee1f338741e", "tarball": "https://registry.npmjs.org/isarray/-/isarray-2.0.1.tgz", "integrity": "sha512-c2cu3UxbI+b6kR3fy0nRnAhodsvR9dx7U5+znCOzdj6IfP3upFURTr0Xl5BlQZNKZjEtxrmVyfSdeE3O57smoQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHusWwMdPqpW/ux5SH8ju4nstKJKn6AjUXLX8PnQYUjNAiBfvjfC0hLe0MYms3zvDISc04OltXAlAtLRSFbQQTbiWQ=="}]}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/isarray-2.0.1.tgz_1475080038422_0.2867460672277957"}, "directories": {}}, "2.0.2": {"name": "isarray", "description": "Array#isArray for older browsers", "version": "2.0.2", "repository": {"type": "git", "url": "git://github.com/juliangruber/isarray.git"}, "homepage": "https://github.com/juliangruber/isarray", "main": "index.js", "files": ["index.js"], "dependencies": {}, "devDependencies": {"tape": "~2.13.4"}, "keywords": ["browser", "isarray", "array"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test.js", "browsers": ["ie/8..latest", "firefox/17..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "scripts": {"test": "tape test.js"}, "gitHead": "a4ea9e106b6608b2c0099835a8e11286a981f4b2", "bugs": {"url": "https://github.com/juliangruber/isarray/issues"}, "_id": "isarray@2.0.2", "_npmVersion": "5.2.0", "_nodeVersion": "8.1.0", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-DKCXsIpN+352zm3Sd75dY9HyaIcxU6grh118GhTPXc85jJ5nEGKOsPQOwSuE7aRd+XVRcC/Em6W44onQEQ9RBg==", "shasum": "5aa99638daf2248b10b9598b763a045688ece3ee", "tarball": "https://registry.npmjs.org/isarray/-/isarray-2.0.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCSPvF5ImggkPNN7vMLeaTC9vcTNwSAOc3k3ZmfkwKDfwIgCItZHAolJMI5ARzkRCg7ojSX69viRkwC68HbmtWcEKQ="}]}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/isarray-2.0.2.tgz_1499849555618_0.5219890710432082"}, "directories": {}}, "2.0.3": {"name": "isarray", "description": "Array#isArray for older browsers", "version": "2.0.3", "repository": {"type": "git", "url": "git://github.com/juliangruber/isarray.git"}, "homepage": "https://github.com/juliangruber/isarray", "main": "index.js", "files": ["index.js"], "dependencies": {}, "devDependencies": {"tape": "~2.13.4"}, "keywords": ["browser", "isarray", "array"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test.js", "browsers": ["ie/8..latest", "firefox/17..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "scripts": {"test": "tape test.js"}, "gitHead": "bea848b59ebc001e6af306ba4a9656b802af282a", "bugs": {"url": "https://github.com/juliangruber/isarray/issues"}, "_id": "isarray@2.0.3", "_npmVersion": "5.5.1", "_nodeVersion": "9.0.0", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-KhjEV7y7pkyOeEZfHnN3aad5508EleFYsMhZXxr5Xo+Gh2gNA02Me6K0BYKiYHb6dw+q7XknaopupAgzCzoCJQ==", "shasum": "ff8dbee3f576335273f1de2d6b420627717f0714", "tarball": "https://registry.npmjs.org/isarray/-/isarray-2.0.3.tgz", "fileCount": 3, "unpackedSize": 3348, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCB0GKhMii2HRaC/rXmtNWqe7rzKZsz9fzsfK4u5N7gNwIhAJXON6zP9u0p2GVxr1BE/1xlyra3/6oRBooMXp66i5gP"}]}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/isarray_2.0.3_1518169983460_0.3616417416504163"}, "_hasShrinkwrap": false}, "2.0.4": {"name": "isarray", "description": "Array#isArray for older browsers", "version": "2.0.4", "repository": {"type": "git", "url": "git://github.com/juliangruber/isarray.git"}, "homepage": "https://github.com/juliangruber/isarray", "main": "index.js", "files": ["index.js"], "dependencies": {}, "devDependencies": {"tape": "~2.13.4"}, "keywords": ["browser", "isarray", "array"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test.js", "browsers": ["ie/8..latest", "firefox/17..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "scripts": {"test": "tape test.js"}, "gitHead": "4a2ac854d773a8016654489dd3ce9f00ad32217d", "bugs": {"url": "https://github.com/juliangruber/isarray/issues"}, "_id": "isarray@2.0.4", "_npmVersion": "5.5.1", "_nodeVersion": "9.0.0", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-GMxXOiUirWg1xTKRipM0Ek07rX+ubx4nNVElTJdNLYmNO/2YrDkgJGw9CljXn+r4EWiDQg/8lsRdHyg2PJuUaA==", "shasum": "38e7bcbb0f3ba1b7933c86ba1894ddfc3781bbb7", "tarball": "https://registry.npmjs.org/isarray/-/isarray-2.0.4.tgz", "fileCount": 3, "unpackedSize": 3443, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG6jNiRbidYzNlojs5ZxJ9Zn8OJqVdfwx5S8AoleQD6QAiApjxFKfMCRL63PIjn9z6RYT0g9g93oGZzqpOj7vj7ANg=="}]}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/isarray_2.0.4_1518248454307_0.6842994172703947"}, "_hasShrinkwrap": false}, "2.0.5": {"name": "isarray", "description": "Array#isArray for older browsers", "version": "2.0.5", "repository": {"type": "git", "url": "git://github.com/juliangruber/isarray.git"}, "homepage": "https://github.com/juliangruber/isarray", "main": "index.js", "dependencies": {}, "devDependencies": {"tape": "~2.13.4"}, "keywords": ["browser", "isarray", "array"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test.js", "browsers": ["ie/8..latest", "firefox/17..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "scripts": {"test": "tape test.js"}, "gitHead": "63ea4ca0a0d6b0574d6a470ebd26880c3026db4a", "bugs": {"url": "https://github.com/juliangruber/isarray/issues"}, "_id": "isarray@2.0.5", "_nodeVersion": "12.0.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-xHjhDr3cNBK0BzdUJSPXZntQUx/mwMS5Rw4A7lPJ90XGAO6ISP/ePDNuo0vhqOZU+UD5JoodwCAAoZQd3FeAKw==", "shasum": "8af1e4c1221244cc62459faf38940d4e644a5723", "tarball": "https://registry.npmjs.org/isarray/-/isarray-2.0.5.tgz", "fileCount": 4, "unpackedSize": 3430, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdI0NSCRA9TVsSAnZWagAAzjYP/RFkf3rbIX0T4eFE3hpL\nr80bdfbn0AIjPAaKMIteJztmVZfIWH5/shXvJWT88AAdLVfGH+fTn//XVMF9\neYesuQYcBIRI65hMVENmmaTuz9Pyrmq52xl8W4UU+NRKKC1qwXrGUWbsmFOF\n7Cvvd2fy7J7Z5cyR+6sP2TAY7cRgzYdNPf7FVE9la/Ahg4DUb7ETTUbrKwN9\npMM+RJEtDA1vRuMUFeh0t6DTCLn7EkJcc/qfm3jePJJ1OXkO8dkfIhtCfWeG\nkePKQiI6+n32kJJtku1F3GxCGgwQiOXI5sX3hPix3oHGs3q5zBIrgCVoj6GZ\nNnhTfcHTvnnhYak8TNmSVATzhhOdNLJUY9+szxbGr0XGBF+Lgi1FFb2Spdyc\nvcqG7O4m4PHPrRsNZRrsTeQ3RcfZcoZ9zmdKqea4QzA4FQjPB1RZom4STuXX\nLIpiylo8H/6GodraHrSJ256xSeGy5P3sp6qsKRGtxhJwfdQNCFeCqFMNgmNL\n2TJLm5ojvsViOHeJonDa4olaVWh6eaI2T9UzH61yR71/E0Gbjb4D0e9f/ex5\nvpnvgUcFutSk7E9IbnZHao4B61o53r0qJWaxgdzL22QuFJEOszoiqF9gL2Ou\nIO3toVc6CROs6zznHpM2VlMB7SuOCC45lQsPORPW617nDtt77oKQrCU/Bqq9\nOMMX\r\n=GpHT\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDD/lI5h9BqcZJstNkqWiU1eb1AK3v8GF/unH7kFQoVAgIhAM/IOaXZD4iV3y6Oi/VcOJo2db44krki8kEv7vUXioPA"}]}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/isarray_2.0.5_1562592082229_0.4278235819723575"}, "_hasShrinkwrap": false}}, "readme": "\n# isarray\n\n`Array#isArray` for older browsers and deprecated Node.js versions.\n\n[![build status](https://secure.travis-ci.org/juliangruber/isarray.svg)](http://travis-ci.org/juliangruber/isarray)\n[![downloads](https://img.shields.io/npm/dm/isarray.svg)](https://www.npmjs.org/package/isarray)\n\n[![browser support](https://ci.testling.com/juliangruber/isarray.png)\n](https://ci.testling.com/juliangruber/isarray)\n\n__Just use Array.isArray directly__, unless you need to support those older versions.\n\n## Usage\n\n```js\nvar isArray = require('isarray');\n\nconsole.log(isArray([])); // => true\nconsole.log(isArray({})); // => false\n```\n\n## Installation\n\nWith [npm](https://npmjs.org) do\n\n```bash\n$ npm install isarray\n```\n\nThen bundle for the browser with\n[browserify](https://github.com/substack/node-browserify).\n\n## Sponsors\n\nThis module is proudly supported by my [Sponsors](https://github.com/juliangruber/sponsors)!\n\nDo you want to support modules like this to improve their quality, stability and weigh in on new features? Then please consider donating to my [Patreon](https://www.patreon.com/juliangruber). Not sure how much of my modules you're using? Try [feross/thanks](https://github.com/feross/thanks)!\n", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-07-12T19:07:21.350Z", "created": "2013-05-22T19:10:00.756Z", "0.0.0": "2013-05-22T19:10:04.137Z", "0.0.1": "2013-05-27T17:40:35.695Z", "1.0.0": "2015-12-10T10:05:07.067Z", "2.0.0": "2016-09-24T16:47:53.485Z", "2.0.1": "2016-09-28T16:27:20.434Z", "2.0.2": "2017-07-12T08:52:36.536Z", "2.0.3": "2018-02-09T09:53:04.544Z", "2.0.4": "2018-02-10T07:40:59.507Z", "2.0.5": "2019-07-08T13:21:22.377Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "repository": {"type": "git", "url": "git://github.com/juliangruber/isarray.git"}, "users": {"tunnckocore": true, "billfeller": true, "simplyianm": true, "nickeltobias": true, "monjer": true, "tobiasnickel": true, "joshfm821": true, "mojaray2k": true, "flumpus-dev": true}, "homepage": "https://github.com/juliangruber/isarray", "keywords": ["browser", "isarray", "array"], "license": "MIT", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/juliangruber/isarray/issues"}}