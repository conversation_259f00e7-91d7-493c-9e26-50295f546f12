{"name": "@sigstore/sign", "dist-tags": {"latest": "3.1.0"}, "versions": {"1.0.0": {"name": "@sigstore/sign", "version": "1.0.0", "dependencies": {"@sigstore/bundle": "^1.1.0", "make-fetch-happen": "^11.0.1", "@sigstore/protobuf-specs": "^0.2.0"}, "devDependencies": {"@sigstore/jest": "^0.0.0", "@sigstore/mock": "^0.2.0", "@sigstore/rekor-types": "^1.0.0", "@types/make-fetch-happen": "^10.0.0"}, "dist": {"shasum": "6b08ebc2f6c92aa5acb07a49784cb6738796f7b4", "tarball": "https://registry.npmjs.org/@sigstore/sign/-/sign-1.0.0.tgz", "fileCount": 75, "integrity": "sha512-INxFVNQteLtcfGmcoldzV6Je0sbbfh9I16DM4yJPw3j5+TFP8X6uIiA18mvpEa9yyeycAKgPmOA3X9hVdVTPUA==", "signatures": [{"sig": "MEUCIApIJ/Mujs8eNbTW05z1EQVIz4zMdPx7JihEQe7n0RxuAiEAzpDP0ZgGSvbetuTFOlfYt5rRirZNNSqp4aLPnx5GDj8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@sigstore%2fsign@1.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 90024}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "2.0.0": {"name": "@sigstore/sign", "version": "2.0.0", "dependencies": {"@sigstore/bundle": "^2.0.0", "make-fetch-happen": "^13.0.0", "@sigstore/protobuf-specs": "^0.2.1"}, "devDependencies": {"@sigstore/jest": "^0.0.0", "@sigstore/mock": "^0.3.0", "@sigstore/rekor-types": "^2.0.0", "@types/make-fetch-happen": "^10.0.0"}, "dist": {"shasum": "ebd6e76227259d82e592d7651d97126c04a04e3f", "tarball": "https://registry.npmjs.org/@sigstore/sign/-/sign-2.0.0.tgz", "fileCount": 73, "integrity": "sha512-f+r1jEDwM5969DTORRln9sDmWjTy1cOQzhU/iisGNzFdbF2TglmwNScbH6aiQ6QH4lc3jOXNMgKP6sec1kSVKA==", "signatures": [{"sig": "MEYCIQCSaIJzYAcl5njEKCtG6qvnb4eGJ9KUr7d7ROamHj+r8QIhANJ787778FDessciBur6q6a8ZV7jPXHJ6SMJXfYzgD4X", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@sigstore%2fsign@2.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 88476}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "2.1.0": {"name": "@sigstore/sign", "version": "2.1.0", "dependencies": {"@sigstore/bundle": "^2.1.0", "make-fetch-happen": "^13.0.0", "@sigstore/protobuf-specs": "^0.2.1"}, "devDependencies": {"@sigstore/jest": "^0.0.0", "@sigstore/mock": "^0.4.0", "@sigstore/rekor-types": "^2.0.0", "@types/make-fetch-happen": "^10.0.0"}, "dist": {"shasum": "801f4b5f60e13ecd1925117a7d084ab7b2199f01", "tarball": "https://registry.npmjs.org/@sigstore/sign/-/sign-2.1.0.tgz", "fileCount": 73, "integrity": "sha512-4VRpfJxs+8eLqzLVrZngVNExVA/zAhVbi4UT4zmtLi4xRd7vz5qie834OgkrGsLlLB1B2nz/3wUxT1XAUBe8gw==", "signatures": [{"sig": "MEUCIAk5QBA+plQz5a67gkbt74lWQRnUY9uiDUiyh86FC1qvAiEAnWTq3GLBwYvjFjMF3M4HFa9YB70JgAMprAG0bFsDGMk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@sigstore%2fsign@2.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 91398}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "2.2.0": {"name": "@sigstore/sign", "version": "2.2.0", "dependencies": {"@sigstore/bundle": "^2.1.0", "make-fetch-happen": "^13.0.0", "@sigstore/protobuf-specs": "^0.2.1"}, "devDependencies": {"@sigstore/jest": "^0.0.0", "@sigstore/mock": "^0.6.0", "@sigstore/rekor-types": "^2.0.0", "@types/make-fetch-happen": "^10.0.3"}, "dist": {"shasum": "4918207d8356877ab42d85d360d5729e9b3ec65a", "tarball": "https://registry.npmjs.org/@sigstore/sign/-/sign-2.2.0.tgz", "fileCount": 73, "integrity": "sha512-AAbmnEHDQv6CSfrWA5wXslGtzLPtAtHZleKOgxdQYvx/s76Fk6T6ZVt7w2IGV9j1UrFeBocTTQxaXG2oRrDhYA==", "signatures": [{"sig": "MEUCIQDCkQHSeKsEZ7KC3lvHdZudlh0u2Fr87gYmQHU+6wL0NgIgfbtRYB8EYtrNW+jJ4yQVgSMpvaplPGEz/FiPKm+VZR8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@sigstore%2fsign@2.2.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 91545}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "2.2.1": {"name": "@sigstore/sign", "version": "2.2.1", "dependencies": {"@sigstore/core": "^0.2.0", "@sigstore/bundle": "^2.1.1", "make-fetch-happen": "^13.0.0", "@sigstore/protobuf-specs": "^0.2.1"}, "devDependencies": {"@sigstore/jest": "^0.0.0", "@sigstore/mock": "^0.6.3", "@sigstore/rekor-types": "^2.0.0", "@types/make-fetch-happen": "^10.0.4"}, "dist": {"shasum": "b37383db1f25ab20cfec980d23ce08e6f99e6caf", "tarball": "https://registry.npmjs.org/@sigstore/sign/-/sign-2.2.1.tgz", "fileCount": 63, "integrity": "sha512-U5sKQEj+faE1MsnLou1f4DQQHeFZay+V9s9768lw48J4pKykPj34rWyI1lsMOGJ3Mae47Ye6q3HAJvgXO21rkQ==", "signatures": [{"sig": "MEUCIDJlicNNjqGqhf8D6Hw/D0N35x9HbwWRD8aMn1IJM9G8AiEA+VL5vn2cHBTU0/YA4W0/mcid0fSeV8sNehp5/NZJo4k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@sigstore%2fsign@2.2.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 84159}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "2.2.2": {"name": "@sigstore/sign", "version": "2.2.2", "dependencies": {"@sigstore/core": "^1.0.0", "@sigstore/bundle": "^2.1.1", "make-fetch-happen": "^13.0.0", "@sigstore/protobuf-specs": "^0.2.1"}, "devDependencies": {"@sigstore/jest": "^0.0.0", "@sigstore/mock": "^0.6.4", "@sigstore/rekor-types": "^2.0.0", "@types/make-fetch-happen": "^10.0.4"}, "dist": {"shasum": "a958388fd20a7c367e20dd3604de3b47cc0b2b47", "tarball": "https://registry.npmjs.org/@sigstore/sign/-/sign-2.2.2.tgz", "fileCount": 63, "integrity": "sha512-mAifqvvGOCkb5BJ5d/SRrVP5+kKCGxtcHuti6lgqZalIfNxikxlJMMptOqFp9+xV5LAnJMSaMWtzvcgNZ3PlPA==", "signatures": [{"sig": "MEUCIQCrgS8tSYO8sWJMnMXoLsrBrkExR6vIM7ZMv6tkgAva2wIgO9tOBI3AJISX3M6+f9ozMH4aJ+xuWbcRtaRxRdKN5vk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@sigstore%2fsign@2.2.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 84213}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "2.2.3": {"name": "@sigstore/sign", "version": "2.2.3", "dependencies": {"@sigstore/core": "^1.0.0", "@sigstore/bundle": "^2.2.0", "make-fetch-happen": "^13.0.0", "@sigstore/protobuf-specs": "^0.3.0"}, "devDependencies": {"@sigstore/jest": "^0.0.0", "@sigstore/mock": "^0.6.5", "@sigstore/rekor-types": "^2.0.0", "@types/make-fetch-happen": "^10.0.4"}, "dist": {"shasum": "f07bcd2cfee654fade867db44ae260f1a0142ba4", "tarball": "https://registry.npmjs.org/@sigstore/sign/-/sign-2.2.3.tgz", "fileCount": 63, "integrity": "sha512-LqlA+ffyN02yC7RKszCdMTS6bldZnIodiox+IkT8B2f8oRYXCB3LQ9roXeiEL21m64CVH1wyveYAORfD65WoSw==", "signatures": [{"sig": "MEUCIQDhdkQ1ENaOhcvByvLXegQtCuedHcQgNST7JrHU7Tv22wIgD8i/7iJYqb3JQmgFQo5wyaMegIX56ND7ORLZjZjdO+0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@sigstore%2fsign@2.2.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 84213}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "2.3.0": {"name": "@sigstore/sign", "version": "2.3.0", "dependencies": {"@sigstore/core": "^1.0.0", "@sigstore/bundle": "^2.3.0", "make-fetch-happen": "^13.0.0", "@sigstore/protobuf-specs": "^0.3.1"}, "devDependencies": {"@sigstore/jest": "^0.0.0", "@sigstore/mock": "^0.7.0", "@sigstore/rekor-types": "^2.0.0", "@types/make-fetch-happen": "^10.0.4"}, "dist": {"shasum": "c35e10a3d707e0c69a29bd9f93fa2bdc6275817c", "tarball": "https://registry.npmjs.org/@sigstore/sign/-/sign-2.3.0.tgz", "fileCount": 63, "integrity": "sha512-tsAyV6FC3R3pHmKS880IXcDJuiFJiKITO1jxR1qbplcsBkZLBmjrEw5GbC7ikD6f5RU1hr7WnmxB/2kKc1qUWQ==", "signatures": [{"sig": "MEQCIHj8J1odlV1nLuBqcajUsYBYkaWghCC4q4WG7AflUnxhAiB69I3uWG8R2E5Tpno6ASY+40WBT85y/RgFMmkjiP0aRA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@sigstore%2fsign@2.3.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 84521}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "2.3.1": {"name": "@sigstore/sign", "version": "2.3.1", "dependencies": {"proc-log": "^4.2.0", "promise-retry": "^2.0.1", "@sigstore/core": "^1.0.0", "@sigstore/bundle": "^2.3.0", "make-fetch-happen": "^13.0.1", "@sigstore/protobuf-specs": "^0.3.1"}, "devDependencies": {"@sigstore/jest": "^0.0.0", "@sigstore/mock": "^0.7.3", "@types/promise-retry": "^1.1.6", "@sigstore/rekor-types": "^2.0.0", "@types/make-fetch-happen": "^10.0.4"}, "dist": {"shasum": "4fc4e6faee5689b5e9d42e97f1207273b7dd7b7f", "tarball": "https://registry.npmjs.org/@sigstore/sign/-/sign-2.3.1.tgz", "fileCount": 65, "integrity": "sha512-YZ71wKIOweC8ViUeZXboz0iPLqMkskxuoeN/D1CEpAyZvEepbX9oRMIoO6a/DxUqO1VEaqmcmmqzSiqtOsvSmw==", "signatures": [{"sig": "MEUCIDGtuDZLsIsKMBTboqkYRhkeGWbemfdOGlDN5jJ7D/6HAiEAuNhSHDkwCDl7WdfhE6qJ/u/7JS6cPPONO+IY/6JPqt4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@sigstore%2fsign@2.3.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 85257}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "2.3.2": {"name": "@sigstore/sign", "version": "2.3.2", "dependencies": {"proc-log": "^4.2.0", "promise-retry": "^2.0.1", "@sigstore/core": "^1.0.0", "@sigstore/bundle": "^2.3.2", "make-fetch-happen": "^13.0.1", "@sigstore/protobuf-specs": "^0.3.2"}, "devDependencies": {"@sigstore/jest": "^0.0.0", "@sigstore/mock": "^0.7.4", "@types/promise-retry": "^1.1.6", "@sigstore/rekor-types": "^2.0.0", "@types/make-fetch-happen": "^10.0.4"}, "dist": {"shasum": "d3d01e56d03af96fd5c3a9b9897516b1233fc1c4", "tarball": "https://registry.npmjs.org/@sigstore/sign/-/sign-2.3.2.tgz", "fileCount": 65, "integrity": "sha512-5Vz5dPVuunIIvC5vBb0APwo7qKA4G9yM48kPWJT+OEERs40md5GoUR1yedwpekWZ4m0Hhw44m6zU+ObsON+iDA==", "signatures": [{"sig": "MEUCIQCjOP03l7wbH4YC5SdTW4DaY8b28Ovg/fKRkARuNPW3UQIgD60Xd1xrkTCMOoGCUUxufT4oaa0P59XJ0UANFs+DEBU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@sigstore%2fsign@2.3.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 85257}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "3.0.0": {"name": "@sigstore/sign", "version": "3.0.0", "dependencies": {"proc-log": "^5.0.0", "promise-retry": "^2.0.1", "@sigstore/core": "^2.0.0", "@sigstore/bundle": "^3.0.0", "make-fetch-happen": "^14.0.1", "@sigstore/protobuf-specs": "^0.3.2"}, "devDependencies": {"@sigstore/jest": "^0.0.0", "@sigstore/mock": "^0.8.0", "@types/promise-retry": "^1.1.6", "@sigstore/rekor-types": "^3.0.0", "@types/make-fetch-happen": "^10.0.4"}, "dist": {"shasum": "70752aaa54dfeafa0b0fbe1f58ebe9fe3d621f8f", "tarball": "https://registry.npmjs.org/@sigstore/sign/-/sign-3.0.0.tgz", "fileCount": 65, "integrity": "sha512-UjhDMQOkyDoktpXoc5YPJpJK6IooF2gayAr5LvXI4EL7O0vd58okgfRcxuaH+YTdhvb5aa1Q9f+WJ0c2sVuYIw==", "signatures": [{"sig": "MEYCIQDlKxIjkNCKJKZh4JYmSt5MWUk5xY3k05nSR1EPlpg9JAIhAN6PAFMOMqDmkvJyehMe+1mYwwCa0nJD8vkEDw9ZB9bZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@sigstore%2fsign@3.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 84932}, "engines": {"node": "^18.17.0 || >=20.5.0"}}, "3.1.0": {"name": "@sigstore/sign", "version": "3.1.0", "dependencies": {"@sigstore/bundle": "^3.1.0", "@sigstore/core": "^2.0.0", "@sigstore/protobuf-specs": "^0.4.0", "make-fetch-happen": "^14.0.2", "proc-log": "^5.0.0", "promise-retry": "^2.0.1"}, "devDependencies": {"@sigstore/jest": "^0.0.0", "@sigstore/mock": "^0.10.0", "@sigstore/rekor-types": "^3.0.0", "@types/make-fetch-happen": "^10.0.4", "@types/promise-retry": "^1.1.6"}, "dist": {"integrity": "sha512-knzjmaOHOov1Ur7N/z4B1oPqZ0QX5geUfhrVaqVlu+hl0EAoL4o+l0MSULINcD5GCWe3Z0+YJO8ues6vFlW0Yw==", "shasum": "5d098d4d2b59a279e9ac9b51c794104cda0c649e", "tarball": "https://registry.npmjs.org/@sigstore/sign/-/sign-3.1.0.tgz", "fileCount": 65, "unpackedSize": 85597, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@sigstore%2fsign@3.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDRz0pO2bP4SZB3YHCsHBY80AS4jGAu3Z6QhJ4P3H+47QIgUdISaaujR25YYzjvK5hoIcPGMvtAIZuluoiT1Jixk7g="}]}, "engines": {"node": "^18.17.0 || >=20.5.0"}}}, "modified": "2025-02-04T20:35:49.171Z"}