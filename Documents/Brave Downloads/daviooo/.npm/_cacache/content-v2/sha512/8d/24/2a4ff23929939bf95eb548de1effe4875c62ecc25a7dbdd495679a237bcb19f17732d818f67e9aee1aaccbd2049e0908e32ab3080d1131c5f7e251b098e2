{"_id": "agentkeepalive", "_rev": "93-f03858936de00b5c6aae3ccf58921d02", "name": "agentkeepalive", "dist-tags": {"node-0.10": "0.2.4", "release-2": "2.2.0", "latest-2": "2.2.0", "latest": "4.6.0", "latest-3": "3.5.3"}, "versions": {"0.0.1": {"name": "agentkeepalive", "version": "0.0.1", "keywords": ["http", "agent", "keepalive"], "author": {"url": "http://fengmk2.github.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@0.0.1", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/TBEDP/agentkeepalive/issues"}, "dist": {"shasum": "db2ed72edc581d7e334ccaaab597b11673f90ae8", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-0.0.1.tgz", "integrity": "sha512-26ltAABGev4dn41mDQ1bYlu2VOM/OKW2R79+G9LDYxr6GcjhhFD2Xg4pEigpN/tISu0w2kPFwbqkZq9QcFOTcA==", "signatures": [{"sig": "MEUCIDI8NQE/j6u4aRDlG9tM9AxY7USZ/WPjhJpmjr3ZD5A6AiEArScfk5l5n2Ey0d6FvvQY1okpQ8KOsSaUzpSTojW5K4w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "scripts": {"test": "make test"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TBEDP/agentkeepalive.git", "type": "git"}, "_npmVersion": "1.1.59", "description": "Missing keepalive http.Agent", "directories": {"test": "test"}, "devDependencies": {"mocha": "*", "should": "*", "visionmedia-jscoverage": "*"}}, "0.0.3": {"name": "agentkeepalive", "version": "0.0.3", "keywords": ["http", "agent", "keepalive"], "author": {"url": "http://fengmk2.github.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@0.0.3", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/TBEDP/agentkeepalive/issues"}, "dist": {"shasum": "29299467a7dd16639d7ac0eb1350551b41618ad1", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-0.0.3.tgz", "integrity": "sha512-AeKI8xKkDQ038OYNrfyP/OYtOSUgEsKZveDZNSZhw5J/wrfJER8lQ6++aFCC+B9Z9R3j3cOUR7fdwmcTYxJYFA==", "signatures": [{"sig": "MEUCIHntKsjbmN0q3aO8IfARxb78qL9HEWBnMzOg0uwKhx0IAiEAxISmbGIJ39wIMiY+6Z82HY7d9709Kk5GjjezLLLdyv8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "scripts": {"test": "make test"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TBEDP/agentkeepalive.git", "type": "git"}, "_npmVersion": "1.1.59", "description": "Missing keepalive http.Agent", "directories": {"test": "test"}, "devDependencies": {"mocha": "*", "should": "*", "visionmedia-jscoverage": "*"}}, "0.1.0": {"name": "agentkeepalive", "version": "0.1.0", "keywords": ["http", "agent", "keepalive"], "author": {"url": "http://fengmk2.github.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@0.1.0", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/TBEDP/agentkeepalive/issues"}, "dist": {"shasum": "d9f47d0ab8eb3ccaca9fd0a5365b47659f038649", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-0.1.0.tgz", "integrity": "sha512-WyaKzgk5wpaKg+gJ29dZTm8oLKhOloHTGQ9xQoa1/1anLx8yn3vssbHuoJ4yF6Zz9PLFRZfL5x15i9Qz2bGUmg==", "signatures": [{"sig": "MEUCIQCYyD6+A8Jxn2ZTuJAmNKp22FaQqNegE/n9NcohE23AFgIgI9tYZE4tG6AcPahGo/H1gGYfTKjF3SPv5bOI9eTC4uE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "scripts": {"test": "make test"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TBEDP/agentkeepalive.git", "type": "git"}, "_npmVersion": "1.1.49", "description": "Missing keepalive http.Agent", "directories": {"test": "test"}, "devDependencies": {"mocha": "*", "should": "*", "visionmedia-jscoverage": "*"}}, "0.1.1": {"name": "agentkeepalive", "version": "0.1.1", "keywords": ["http", "agent", "keepalive"], "author": {"url": "http://fengmk2.github.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@0.1.1", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/TBEDP/agentkeepalive/issues"}, "dist": {"shasum": "ddebdf14d1ec093c709ca1893760e14319e511db", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-0.1.1.tgz", "integrity": "sha512-M5q9EqZmjTQIXfUrl/FfRgxJ7sWycKQ0wHo7gIAbvl9+OL5UUAkNAXt5v7SnIq+xgJV5qNG7zdlf7wDaEGnXHQ==", "signatures": [{"sig": "MEQCIFDGWAZcdyiwMqTYb/9b7lMPKuvLHdBEGp+kuvxvGt52AiAI1/FljBeFRYZ7aElWt34ePxeJzLjWd+5ijGg2HhjSSw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "scripts": {"test": "make test"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TBEDP/agentkeepalive.git", "type": "git"}, "_npmVersion": "1.1.59", "description": "Missing keepalive http.Agent", "directories": {"test": "test"}, "devDependencies": {"mocha": "*", "should": "*", "visionmedia-jscoverage": "*"}}, "0.1.2": {"name": "agentkeepalive", "version": "0.1.2", "keywords": ["http", "agent", "keepalive"], "author": {"url": "http://fengmk2.github.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@0.1.2", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/TBEDP/agentkeepalive/issues"}, "dist": {"shasum": "daf6dd19479f560500d28dd3692e4d2714c985c2", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-0.1.2.tgz", "integrity": "sha512-lz0wI6dPTZbLgoVJfteMzZxjW6D95a4qiuqpUdYWgSXawVy3L5mWgju67+ZuQPWyFiWvP1XukydtJT22bvp56A==", "signatures": [{"sig": "MEUCIGYym5g2eheNF59UN6ozxJzpNMIgG2pjT+7UzIRNAc9SAiEAtW3Km689koKjxrYwFJ4cu8sU3YRQc5loppiQrDUVwEo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "scripts": {"test": "make test"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TBEDP/agentkeepalive.git", "type": "git"}, "_npmVersion": "1.1.59", "description": "Missing keepalive http.Agent", "directories": {"test": "test"}, "devDependencies": {"mocha": "*", "should": "*", "visionmedia-jscoverage": "*"}}, "0.1.3": {"name": "agentkeepalive", "version": "0.1.3", "keywords": ["http", "agent", "keepalive"], "author": {"url": "http://fengmk2.github.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@0.1.3", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/TBEDP/agentkeepalive/issues"}, "dist": {"shasum": "cc7eb5d9093e402d7fe73277c7a4d86f3e510388", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-0.1.3.tgz", "integrity": "sha512-nWfk3npeM1PpHGPBPlF8DK2zLIKTfv2AGU/nFsUAsMGUyNHRW1WzzNwTeDY8Kz/iT9yjv3xloDPtASTr7m7dPQ==", "signatures": [{"sig": "MEUCIDA969Nh4UIWnqohHcf05TSFKNe9Nl9BbLHxE80ThXRQAiEAhIkewDA35cZ/CDBWjBSL43ql9HFsFwF3sNFaTRzMdxY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "scripts": {"test": "make test"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TBEDP/agentkeepalive.git", "type": "git"}, "_npmVersion": "1.1.59", "description": "Missing keepalive http.Agent", "directories": {"test": "test"}, "devDependencies": {"mocha": "*", "should": "*", "visionmedia-jscoverage": "*"}}, "0.1.4": {"name": "agentkeepalive", "version": "0.1.4", "keywords": ["http", "agent", "keepalive"], "author": {"url": "http://fengmk2.github.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@0.1.4", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/TBEDP/agentkeepalive/issues"}, "dist": {"shasum": "af64b718ab7477cada79d739761429d8d77bc7c4", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-0.1.4.tgz", "integrity": "sha512-U2oQumKUVw5wTTWizBQBrkfbEQoLDrbnMAIn5YVNlBwi/SKlBDLt1AqGjm7BuvO+FhohiKzBM4Srd7UDJ/69Uw==", "signatures": [{"sig": "MEQCIFsxyaN1hrojJZb+Suy2KvaUX3G2RviqP+HHry9cFFwQAiBD3j5Fhe1GBNzca2ggajVWKJjtO2lawTIrn3Bh0pGA7g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "scripts": {"test": "make test"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TBEDP/agentkeepalive.git", "type": "git"}, "_npmVersion": "1.1.61", "description": "Missing keepalive http.Agent", "directories": {"test": "test"}, "devDependencies": {"mocha": "*", "should": "*", "visionmedia-jscoverage": "*"}}, "0.1.5": {"name": "agentkeepalive", "version": "0.1.5", "keywords": ["http", "agent", "keepalive"], "author": {"url": "http://fengmk2.github.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@0.1.5", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/TBEDP/agentkeepalive/issues"}, "dist": {"shasum": "d8deacd5afb1023294222fc2479a9dd035fd90b7", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-0.1.5.tgz", "integrity": "sha512-66UIpYHYaoebXMRvuZ7P+9n6vOpWS31MIWDcpQS22In1bidIIXs2+fYJIqBinKSid3QcUPfDqYQLX8F+b+5Zhg==", "signatures": [{"sig": "MEUCIQDqNIl720w7wDLFQDOdow2QyLI5pCwr9r/epQAf8BqurQIgJ+SWkvT91vSrVDWP3eLbVUug5xFynPxVvYfZX6WJcdU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "scripts": {"test": "make test"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TBEDP/agentkeepalive.git", "type": "git"}, "_npmVersion": "1.1.61", "description": "Missing keepalive http.Agent", "directories": {"test": "test"}, "devDependencies": {"mocha": "*", "should": "*", "pedding": "*", "visionmedia-jscoverage": "*"}}, "0.2.0": {"name": "agentkeepalive", "version": "0.2.0", "keywords": ["http", "agent", "keepalive"], "author": {"url": "http://fengmk2.github.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@0.2.0", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/TBEDP/agentkeepalive/issues"}, "dist": {"shasum": "ad293d6c5b91a8eac300b67d0c211f1ba2883d77", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-0.2.0.tgz", "integrity": "sha512-f8JB6qfpWtGf+fpxATre+XGA+FgFJYgGOkgWSDZffYFt/wHA0afYhKxC/261vlDpL62MOTaZFs7yFSeBF8TLdw==", "signatures": [{"sig": "MEQCIAp1kjg3xipy6Q6s4ZuXwcOl+gRTpU6YaVh45ZFP6ovbAiBe+isNkcnx2PmVEOKOeC+uNJzsJu9dtUsRyWsb0QyNBw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "engines": {"node": ">= 0.10.0"}, "scripts": {"test": "make test-all"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TBEDP/agentkeepalive.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "Missing keepalive http.Agent", "directories": {"test": "test"}, "devDependencies": {"mocha": "*", "should": "*", "blanket": "*", "pedding": "*", "coveralls": "*", "travis-cov": "*", "mocha-lcov-reporter": "*"}}, "0.2.1": {"name": "agentkeepalive", "version": "0.2.1", "keywords": ["http", "agent", "keepalive"], "author": {"url": "http://fengmk2.github.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@0.2.1", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/TBEDP/agentkeepalive", "bugs": {"url": "https://github.com/TBEDP/agentkeepalive/issues"}, "dist": {"shasum": "135e7bee6c12596d1a853de9df8f5b74b2020489", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-0.2.1.tgz", "integrity": "sha512-Gn5qN6/9awKZ53QQZsPaNMRY0ed3OXh6eRvyuyo36+3vFBxsM96f7MDJdf9Kx/y8RySDVMa9NFT0jLyY1Rgihw==", "signatures": [{"sig": "MEUCIDhlaPOkKenCHs4dLr/t2HhDT9aofFqzjGTDkZ+dBEwlAiEAmjE56oVuSZBKU+gGYu0o/j3CGPu8t/acpWdrDDtRVO4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "engines": {"node": ">= 0.11.8"}, "scripts": {"test": "make test-all"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TBEDP/agentkeepalive.git", "type": "git"}, "_npmVersion": "1.3.13", "description": "Missing keepalive http.Agent", "directories": {"test": "test"}, "devDependencies": {"mocha": "*", "should": "*", "blanket": "*", "pedding": "*", "coveralls": "*", "travis-cov": "*", "mocha-lcov-reporter": "*"}}, "0.2.2": {"name": "agentkeepalive", "version": "0.2.2", "keywords": ["http", "agent", "keepalive"], "author": {"url": "http://fengmk2.github.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@0.2.2", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/TBEDP/agentkeepalive", "bugs": {"url": "https://github.com/TBEDP/agentkeepalive/issues"}, "dist": {"shasum": "59c441a4db4ae6eafd03f0aa38a428182ffd615b", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-0.2.2.tgz", "integrity": "sha512-uJME0a1lriSJFTz4Q1cDQKIc69oHfjF5LOKGESHgW4BbTOuSFlEUtaVzkUhkMf8vO9hYJKGQtjpfYlVKNwwfqQ==", "signatures": [{"sig": "MEYCIQD/2TTgfa+f0Qh67r54fnh50yvjJhNKZtYDJiGPezRoQQIhAMY+vDRFkMnGm2i5efioNBTUtP7dlnCElpg1z12Y1WEx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "make test-all"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TBEDP/agentkeepalive.git", "type": "git"}, "_npmVersion": "1.3.13", "description": "Missing keepalive http.Agent", "directories": {"test": "test"}, "devDependencies": {"mocha": "*", "should": "*", "blanket": "*", "pedding": "*", "coveralls": "*", "travis-cov": "*", "mocha-lcov-reporter": "*"}}, "1.0.0": {"name": "agentkeepalive", "version": "1.0.0", "keywords": ["http", "agent", "keepalive"], "author": {"url": "http://fengmk2.github.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@1.0.0", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/agentkeepalive", "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "dist": {"shasum": "e55a850247fd8d53990d6d21e5fc10b2b4b2a361", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-1.0.0.tgz", "integrity": "sha512-vBVuPSgXN1jkJjkpmkJpNuW2x/BRODC9WIxl+QeEi0jxLCQW3SHYWCBzRYKb9ePbRopHx0UT5iQSFECWCkdI9w==", "signatures": [{"sig": "MEUCIQClQHECw3ShJLWhrd+4/n+De1vA5UHUc3PKm6j87qy0GQIgV7+U3FDlsuml1iMiPKBeKDY6EdVD/oHlPqR5fGNkN50=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "e55a850247fd8d53990d6d21e5fc10b2b4b2a361", "engines": {"node": ">= 0.11.12"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/agentkeepalive.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Missing keepalive http.Agent", "directories": {}, "dependencies": {}, "devDependencies": {"autod": "*", "mocha": "*", "should": "~4.0.4", "pedding": "~1.0.0", "istanbul": "*", "should-http": "*"}}, "1.1.0": {"name": "agentkeepalive", "version": "1.1.0", "keywords": ["http", "agent", "keepalive"], "author": {"url": "http://fengmk2.github.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@1.1.0", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/agentkeepalive", "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "dist": {"shasum": "827d10e5f804d5a1282deac1e95c3ac417c88737", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-1.1.0.tgz", "integrity": "sha512-64E1s4Vj2hs8QN+1iYBs98/n8A370Bk13qjUUi9iThHxO1lMhDs8vV8CdHOmr1a7UD2jFFcCPROy4P2CLaxgXw==", "signatures": [{"sig": "MEUCIQCEPkpqruTxnZXJHOkSJ1GLgebuqGsYEh865ZRqEg+BHAIgJuCrj1VQFcPwSw9rWFpwwpJTbnRewACRPpu7tI8VyS0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "827d10e5f804d5a1282deac1e95c3ac417c88737", "engines": {"node": ">= 0.11.12"}, "gitHead": "620bb51328876e63ef1119a39b6ea3e7b703c824", "scripts": {"test": "make test"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/agentkeepalive.git", "type": "git"}, "_npmVersion": "1.5.0-alpha-4", "description": "Missing keepalive http.Agent", "directories": {}, "dependencies": {}, "devDependencies": {"autod": "*", "mocha": "*", "should": "~4.0.4", "pedding": "~1.0.0", "istanbul": "*", "should-http": "*"}}, "1.2.0": {"name": "agentkeepalive", "version": "1.2.0", "keywords": ["http", "agent", "keepalive"], "author": {"url": "http://fengmk2.github.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@1.2.0", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/dead-horse", "name": "dead-horse", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/agentkeepalive", "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "dist": {"shasum": "cccd8db524052c5d0907210d7092cd0d4156a5bc", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-1.2.0.tgz", "integrity": "sha512-dXPpy69ZGPHZ+++bAhn0yL9TMgXxfyzoPQGNnIgVWl/ZQKoO+a2XwA/NujKyGybxC1Wi6ezEhAVWP0ccklSC+w==", "signatures": [{"sig": "MEUCIBcnbZbnpidI+KKkJjYSKQ1Nb4yPpOdRxPn8wOmrvifdAiEAjVFOG8ExboPT4KEnU2OXNGQA1cA5P8UY2mxJafuTZVA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "cccd8db524052c5d0907210d7092cd0d4156a5bc", "engines": {"node": ">= 0.11.12"}, "scripts": {"cnpm": "npm install --registry=https://registry.npm.taobao.org", "test": "mocha -R spec -t 5000 -r should-http test/*.test.js", "autod": "autod -w --prefix '~' && npm run cnpm", "jshint": "jshint .", "test-cov": "node node_modules/.bin/istanbul cover node_modules/.bin/_mocha -- -t 5000 -r should-http test/*.test.js", "test-travis": "node node_modules/.bin/istanbul cover node_modules/.bin/_mocha --report lcovonly -- -t 5000 -r should-http test/*.test.js", "contributors": "contributors -f plain -o AUTHORS"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/agentkeepalive.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Missing keepalive http.Agent", "directories": {}, "dependencies": {}, "devDependencies": {"autod": "*", "mocha": "*", "should": "~4.0.4", "pedding": "~1.0.0", "istanbul": "*", "should-http": "~0.0.2", "contributors": "*"}}, "0.2.3": {"name": "agentkeepalive", "version": "0.2.3", "keywords": ["http", "agent", "keepalive"], "author": {"url": "http://fengmk2.github.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@0.2.3", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/TBEDP/agentkeepalive", "bugs": {"url": "https://github.com/TBEDP/agentkeepalive/issues"}, "dist": {"shasum": "86a42da07b548ba92ad5c5f64d58ce1f269e5fad", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-0.2.3.tgz", "integrity": "sha512-7hY<PERSON>dWkWdWneHJNm9O8Gcp1GLxwSxoK4H6M00QFcLLQQLVmFv2RglCbjq/MgY2untd9QREbcgjwWDIGv3jrHg==", "signatures": [{"sig": "MEQCIB/pnZEhxnaDL8n98MOQF6vu9AbeNo/lqeXzV+Bzj5uhAiBO94XActMBALIsrJTb8pEv5e5ElI+2jb0cManVtDb56A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "86a42da07b548ba92ad5c5f64d58ce1f269e5fad", "engines": {"node": ">= 0.8.0"}, "gitHead": "a57f62bb306a60723e1edcb0b7f712ad07e44ae9", "scripts": {"test": "make test-all"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TBEDP/agentkeepalive.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "Missing keepalive http.Agent", "directories": {"test": "test"}, "devDependencies": {"mocha": "*", "should": "*", "blanket": "*", "pedding": "*", "coveralls": "*", "travis-cov": "*", "mocha-lcov-reporter": "*"}}, "0.2.4": {"name": "agentkeepalive", "version": "0.2.4", "keywords": ["http", "agent", "keepalive", "agentkeepalive"], "author": {"url": "http://fengmk2.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@0.2.4", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/agentkeepalive", "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "dist": {"shasum": "d13edee4e0190f613f123e7ff9d9778f793396e6", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-0.2.4.tgz", "integrity": "sha512-JGATBdS+EKbjOYrQB4O7BhfAgmGaH3W+HxRcwG7FXCmwJCHuAxUu+si8jBrqk4/so8bTYOZYp8Asi5rBTN8Rew==", "signatures": [{"sig": "MEUCIElLpiwqaX2G7rCRHuzko9KDbBDaLPKtm+Lb3ysX2kidAiEAuJre7lCx+AU35iUJtmo/11fED3tmIxRLIpbVrrBm/xY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib/"], "_shasum": "d13edee4e0190f613f123e7ff9d9778f793396e6", "engines": {"node": "0.10.x"}, "gitHead": "22379a398c046e4c2c5f848d587c6fc5ca8122d4", "scripts": {"test": "make test"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/agentkeepalive.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "Missing keepalive http.Agent", "directories": {}, "devDependencies": {"mocha": "*", "should": "*", "pedding": "*"}}, "1.2.1": {"name": "agentkeepalive", "version": "1.2.1", "keywords": ["http", "agent", "keepalive"], "author": {"url": "http://fengmk2.github.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@1.2.1", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/dead-horse", "name": "dead-horse", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/agentkeepalive", "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "dist": {"shasum": "00ffa628bdd9dc69246fcac4e8680e31da5dc5a0", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-1.2.1.tgz", "integrity": "sha512-cXsWOl0CaZZ+9Fg/jFWChQGypmCdA40/VbeCunCGC6b7pbgGGdMw+lscCQibpMUGuxFHgeo63c7fm8zKNr584w==", "signatures": [{"sig": "MEUCICQVNEW44rl7WTxkEXvGDGMVgn23I9Qy/Q2kr64UOsYAAiEA7SPNi6aomJnloL/uTjx/YfUSOAudeie8iwzlapO85RY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib/"], "_shasum": "00ffa628bdd9dc69246fcac4e8680e31da5dc5a0", "engines": {"node": ">= 0.11.12"}, "gitHead": "c8df0037ee8078281dcbeac3f3f3f5030e7526c3", "scripts": {"cnpm": "npm install --registry=https://registry.npm.taobao.org", "test": "mocha -R spec -t 5000 -r should-http test/*.test.js", "autod": "autod -w --prefix '~' && npm run cnpm", "jshint": "jshint .", "test-cov": "node node_modules/.bin/istanbul cover node_modules/.bin/_mocha -- -t 5000 -r should-http test/*.test.js", "test-travis": "node node_modules/.bin/istanbul cover node_modules/.bin/_mocha --report lcovonly -- -t 5000 -r should-http test/*.test.js", "contributors": "contributors -f plain -o AUTHORS"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/agentkeepalive.git", "type": "git"}, "_npmVersion": "2.7.1", "description": "Missing keepalive http.Agent", "directories": {}, "_nodeVersion": "1.6.2", "dependencies": {}, "devDependencies": {"autod": "*", "mocha": "*", "should": "~4.0.4", "pedding": "~1.0.0", "istanbul": "*", "should-http": "~0.0.2", "contributors": "*"}}, "2.0.0": {"name": "agentkeepalive", "version": "2.0.0", "keywords": ["http", "agent", "keepalive"], "author": {"url": "http://fengmk2.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@2.0.0", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/dead-horse", "name": "dead-horse", "email": "<EMAIL>"}, {"url": "https://github.com/lattmann", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/agentkeepalive", "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "dist": {"shasum": "2be42b5c6407bcd15a5c22f0388d6421846dec73", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-2.0.0.tgz", "integrity": "sha512-XWYYMvSUUn0rakxauclJNyboYocaH/W/4Bes8nrn8OpV2shoo9XoFMS8XYkvWjFlqblDTbZTBF0Z2vSxmFRSiw==", "signatures": [{"sig": "MEQCIHIBaQ2rPi6UsRfu37iVNz/fJBYr5Qt6mYv0h2qQBF6hAiBBCrWnFEEc+/ICWLTLvtz8QZMMDGBkDCJntblMr8SRow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib/"], "_shasum": "2be42b5c6407bcd15a5c22f0388d6421846dec73", "engines": {"node": ">= 0.10.0"}, "gitHead": "0832654093f6cc1961ba1e5b8d2e50f0df84b7bb", "scripts": {"cnpm": "npm install --registry=https://registry.npm.taobao.org", "test": "mocha -R spec -t 5000 -r should-http test/*.test.js", "autod": "autod -w --prefix '~' && npm run cnpm", "jshint": "jshint .", "test-cov": "node node_modules/.bin/istanbul cover node_modules/.bin/_mocha -- -t 5000 -r should-http test/*.test.js", "test-travis": "node node_modules/.bin/istanbul cover node_modules/.bin/_mocha --report lcovonly -- -t 5000 -r should-http test/*.test.js", "contributors": "contributors -f plain -o AUTHORS"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/agentkeepalive.git", "type": "git"}, "_npmVersion": "2.7.4", "description": "Missing keepalive http.Agent", "directories": {}, "_nodeVersion": "1.6.3", "dependencies": {}, "devDependencies": {"autod": "*", "mocha": "*", "should": "~4.0.4", "pedding": "~1.0.0", "istanbul": "*", "should-http": "~0.0.2", "contributors": "*"}}, "2.0.1": {"name": "agentkeepalive", "version": "2.0.1", "keywords": ["http", "agent", "keepalive"], "author": {"url": "http://fengmk2.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@2.0.1", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/dead-horse", "name": "dead-horse", "email": "<EMAIL>"}, {"url": "https://github.com/lattmann", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/agentkeepalive", "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "dist": {"shasum": "e351c667f3eedb00c06601250359565dac5bb9eb", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-2.0.1.tgz", "integrity": "sha512-C3dQsWo/fwc0oQ1RN4J8zTBshnfb3DcRAVO3xwz01oATlKa+31TJS/ykjAdyvCFqWhU093m1uSEbW27B32ndlg==", "signatures": [{"sig": "MEUCIQC97w8JrUobS1d3fo92R3ysRdqs+lxPwRLuMhVEgPeb6AIgBvnAEtHwNzBIjEiqyGGJseWJxz2CtstyXlw60EXcZDo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib/"], "_shasum": "e351c667f3eedb00c06601250359565dac5bb9eb", "engines": {"node": ">= 0.10.0"}, "gitHead": "6f059d7c05942f1442e95c7d4b9bec5398645745", "scripts": {"cnpm": "npm install --registry=https://registry.npm.taobao.org", "test": "mocha -R spec -t 5000 -r should-http test/*.test.js", "autod": "autod -w --prefix '~' && npm run cnpm", "jshint": "jshint .", "test-cov": "node node_modules/.bin/istanbul cover node_modules/.bin/_mocha -- -t 5000 -r should-http test/*.test.js", "test-travis": "node node_modules/.bin/istanbul cover node_modules/.bin/_mocha --report lcovonly -- -t 5000 -r should-http test/*.test.js", "contributors": "contributors -f plain -o AUTHORS"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/agentkeepalive.git", "type": "git"}, "_npmVersion": "2.7.6", "description": "Missing keepalive http.Agent", "directories": {}, "_nodeVersion": "1.7.1", "dependencies": {}, "devDependencies": {"autod": "*", "mocha": "*", "should": "~4.0.4", "pedding": "~1.0.0", "istanbul": "*", "should-http": "~0.0.2", "contributors": "*"}}, "2.0.2": {"name": "agentkeepalive", "version": "2.0.2", "keywords": ["http", "agent", "keepalive"], "author": {"url": "http://fengmk2.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@2.0.2", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/dead-horse", "name": "dead-horse", "email": "<EMAIL>"}, {"url": "https://github.com/lattmann", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/pmalouin", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/agentkeepalive", "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "dist": {"shasum": "a3661c406ed120bb11ca488d67b3276c604ccfe7", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-2.0.2.tgz", "integrity": "sha512-SRor8K5V3AZTXq+9NpUQWM9/979+WBBpdERDyV17VenUhIc2Sc+O4Gpi6Mw8VYRHOYXPF0qqh3dCvPeaEv+4Xw==", "signatures": [{"sig": "MEUCIB9I8kpbXjTrYGazPe+R5hMwTIT1gzWXaI5+axZF0lWrAiEAo3CP9fz1quVY8+6Q084chXwd/cdaBiIW4TXW9crnEBk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib/"], "_shasum": "a3661c406ed120bb11ca488d67b3276c604ccfe7", "engines": {"node": ">= 0.10.0"}, "gitHead": "a020b0d6f8e9d86521c329d976d4d3fe16948ba3", "scripts": {"cnpm": "npm install --registry=https://registry.npm.taobao.org", "test": "mocha -R spec -t 5000 -r should-http test/*.test.js", "autod": "autod -w --prefix '~' && npm run cnpm", "jshint": "jshint .", "test-cov": "node node_modules/.bin/istanbul cover node_modules/.bin/_mocha -- -t 5000 -r should-http test/*.test.js", "test-travis": "node node_modules/.bin/istanbul cover node_modules/.bin/_mocha --report lcovonly -- -t 5000 -r should-http test/*.test.js", "contributors": "contributors -f plain -o AUTHORS"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/agentkeepalive.git", "type": "git"}, "_npmVersion": "2.7.4", "description": "Missing keepalive http.Agent", "directories": {}, "_nodeVersion": "0.12.2", "dependencies": {}, "devDependencies": {"autod": "*", "mocha": "*", "should": "~4.0.4", "pedding": "~1.0.0", "istanbul": "*", "should-http": "~0.0.2", "contributors": "*"}}, "2.0.3": {"name": "agentkeepalive", "version": "2.0.3", "keywords": ["http", "agent", "keepalive"], "author": {"url": "http://fengmk2.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@2.0.3", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/dead-horse", "name": "dead-horse", "email": "<EMAIL>"}, {"url": "https://github.com/lattmann", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/pmalouin", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/agentkeepalive#readme", "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "dist": {"shasum": "13a2984f423d96a45a04d760f439acc6f35c1de0", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-2.0.3.tgz", "integrity": "sha512-HyX+Bb6BbSFGXl6Zc6kLlIYhfXEC8w6zNqSz7ijdj1CqhqvDyABtKuFefDemcSCxzGiaPHjTTkf6DUug/nNAMg==", "signatures": [{"sig": "MEQCIFz5QTlkhn28exM6+TGayEunr/c1I0BL4p2RxLWjyBmNAiBG6k3yG53/hy8Xfy6tkpP/16em5cFTo6rrhetIkg3/hg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib/"], "_shasum": "13a2984f423d96a45a04d760f439acc6f35c1de0", "engines": {"node": ">= 0.10.0"}, "gitHead": "acffea904c62b0d83821d70c3dc161b444697acc", "scripts": {"cnpm": "npm install --registry=https://registry.npm.taobao.org", "test": "mocha -R spec -t 5000 -r should-http test/*.test.js", "autod": "autod -w --prefix '~' && npm run cnpm", "jshint": "jshint .", "test-cov": "node node_modules/.bin/istanbul cover node_modules/.bin/_mocha -- -t 5000 -r should-http test/*.test.js", "test-travis": "node node_modules/.bin/istanbul cover node_modules/.bin/_mocha --report lcovonly -- -t 5000 -r should-http test/*.test.js", "contributors": "contributors -f plain -o AUTHORS"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/agentkeepalive.git", "type": "git"}, "_npmVersion": "2.13.2", "description": "Missing keepalive http.Agent", "directories": {}, "_nodeVersion": "2.5.0", "dependencies": {}, "devDependencies": {"autod": "*", "mocha": "*", "should": "~4.0.4", "pedding": "~1.0.0", "istanbul": "*", "should-http": "~0.0.2", "contributors": "*"}}, "2.0.4": {"name": "agentkeepalive", "version": "2.0.4", "keywords": ["http", "https", "agent", "keepalive", "agentkeepalive"], "author": {"url": "http://fengmk2.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@2.0.4", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/dead-horse", "name": "dead-horse", "email": "<EMAIL>"}, {"url": "https://github.com/lattmann", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/pmalouin", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/agentkeepalive#readme", "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "dist": {"shasum": "b35ef9f5e848ed50bf46f344294531c2632facb1", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-2.0.4.tgz", "integrity": "sha512-u1SMt/WLxrJ7sFbwqB44NcBnN1tjo4rrDjRJl6KmOJv4zofgyvmG3xk7cYZhnQnkDt1jZpY72WivrJOmYXEwOw==", "signatures": [{"sig": "MEUCIQDQAXVMbm+P1nQJ6E9dOpR0mlF+lnZY2x23zvP+iw5BgwIgbB2xSmGRx9KOl3FDqMLw6MaLZ2fsoHHaU03UgOAA3VI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib"], "_shasum": "b35ef9f5e848ed50bf46f344294531c2632facb1", "engines": {"node": ">= 0.10.0"}, "gitHead": "47c8dcdf56b0115cabc5008b8a693c1b950cde75", "scripts": {"ci": "npm run lint && npm run test-cov", "lint": "jshint .", "test": "mocha -R spec -t 5000 -r should-http test/*.test.js", "autod": "autod -w --prefix '~'", "codecov": "npm i codecov && codecov", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- -t 5000 -r should-http test/*.test.js", "contributors": "contributors -f plain -o AUTHORS"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/agentkeepalive.git", "type": "git"}, "_npmVersion": "2.14.20", "description": "Missing keepalive http.Agent", "directories": {}, "_nodeVersion": "4.4.0", "dependencies": {}, "devDependencies": {"autod": "*", "mocha": "*", "jshint": "^2.9.1", "should": "4", "pedding": "1", "istanbul": "*", "should-http": "~0.0.2", "contributors": "*"}, "_npmOperationalInternal": {"tmp": "tmp/agentkeepalive-2.0.4.tgz_1457881277239_0.8944101899396628", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.5": {"name": "agentkeepalive", "version": "2.0.5", "keywords": ["http", "https", "agent", "keepalive", "agentkeepalive"], "author": {"url": "http://fengmk2.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@2.0.5", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/dead-horse", "name": "dead-horse", "email": "<EMAIL>"}, {"url": "https://github.com/lattmann", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/pmalouin", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/agentkeepalive#readme", "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "dist": {"shasum": "42834ae8835dff9c7ffcaed53f5666bf817aa279", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-2.0.5.tgz", "integrity": "sha512-dlXxjfkCrcEPmvJju6ypP6/eq1q0l+cu0u10IhKfiwMoy4yH73n0TQ2jMO2H39xbcC3Q4cWUFPkNk1b3GLEklg==", "signatures": [{"sig": "MEUCIGLPUtK3q5TnpfxXEfFxbkj873QJE1Ain1pfQRZJdTJhAiEAlMg99/PMscEsu14YjUWbbpp3m8mkdGkYVIIes2981GE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib"], "_shasum": "42834ae8835dff9c7ffcaed53f5666bf817aa279", "engines": {"node": ">= 0.10.0"}, "gitHead": "b1c40a349f156e862896e6f3631b0dd95c5a27f7", "scripts": {"ci": "npm run lint && npm run test-cov", "lint": "jshint .", "test": "mocha -R spec -t 5000 -r should-http test/*.test.js", "autod": "autod -w --prefix '~'", "codecov": "npm i codecov && codecov", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- -t 5000 -r should-http test/*.test.js", "contributors": "contributors -f plain -o AUTHORS"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/agentkeepalive.git", "type": "git"}, "_npmVersion": "2.14.20", "description": "Missing keepalive http.Agent", "directories": {}, "_nodeVersion": "4.4.0", "dependencies": {}, "devDependencies": {"autod": "*", "mocha": "*", "jshint": "^2.9.1", "should": "4", "pedding": "1", "istanbul": "*", "should-http": "~0.0.2", "contributors": "*"}, "_npmOperationalInternal": {"tmp": "tmp/agentkeepalive-2.0.5.tgz_1458128222073_0.1455770218744874", "host": "packages-13-west.internal.npmjs.com"}}, "2.1.0": {"name": "agentkeepalive", "version": "2.1.0", "keywords": ["http", "https", "agent", "keepalive", "agentkeepalive"], "author": {"url": "http://fengmk2.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@2.1.0", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/dead-horse", "name": "dead-horse", "email": "<EMAIL>"}, {"url": "https://github.com/lattmann", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/pmalouin", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/agentkeepalive#readme", "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "dist": {"shasum": "04cd596f9251aa44832c36fb488edf474c0f6463", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-2.1.0.tgz", "integrity": "sha512-wXHoislGDqbvzwIDi3REKvXw1QKWubHNWhL9jEFw+LPpY/XTU//OdJlByscS6GYW5192D4LXoXmeYjeUMmv5WA==", "signatures": [{"sig": "MEUCIFCf0TOITTaVpg1k9N7BLO+st/auWnFEUIA7e/NdL8E/AiEArFPS8ttLEC4AxyXbnbxpOkaYehJVBDOJnAKBSrPWnNo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib"], "_shasum": "04cd596f9251aa44832c36fb488edf474c0f6463", "engines": {"node": ">= 0.10.0"}, "gitHead": "f2ca7326098266bd755b47b41637c6984939af1c", "scripts": {"ci": "npm run lint && npm run test-cov", "lint": "jshint .", "test": "mocha -R spec -t 5000 -r should-http test/*.test.js", "autod": "autod -w --prefix '~'", "codecov": "npm i codecov && codecov", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- -t 5000 -r should-http test/*.test.js", "contributors": "contributors -f plain -o AUTHORS"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/agentkeepalive.git", "type": "git"}, "_npmVersion": "2.15.0", "description": "Missing keepalive http.Agent", "directories": {}, "_nodeVersion": "4.4.2", "dependencies": {}, "devDependencies": {"autod": "*", "mocha": "*", "jshint": "^2.9.1", "should": "4", "pedding": "1", "istanbul": "*", "should-http": "~0.0.2", "contributors": "*"}, "_npmOperationalInternal": {"tmp": "tmp/agentkeepalive-2.1.0.tgz_1459566024865_0.9564925443846732", "host": "packages-12-west.internal.npmjs.com"}}, "2.1.1": {"name": "agentkeepalive", "version": "2.1.1", "keywords": ["http", "https", "agent", "keepalive", "agentkeepalive"], "author": {"url": "http://fengmk2.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@2.1.1", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/dead-horse", "name": "dead-horse", "email": "<EMAIL>"}, {"url": "https://github.com/lattmann", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/pmalouin", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/saperal", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/agentkeepalive#readme", "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "dist": {"shasum": "39da7132d52e3088c28167ad402146ef3e3dc131", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-2.1.1.tgz", "integrity": "sha512-8eFQ3XobhZ9agbn58bP75IxX3nyLXrQ0NrfnphhJehXgVj6L5NJhP9GN4LtVJ/EuOug9U21VAkAF/cHLiTCMQQ==", "signatures": [{"sig": "MEUCIDnPzgtg/G44rCE3yLeWdZN+GEQMeKe8vKrlT38tvObOAiEAzTi+RfXrP6igyqTMccVGmhe4RstcxTKYXPe0HDG3NSI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib"], "_shasum": "39da7132d52e3088c28167ad402146ef3e3dc131", "engines": {"node": ">= 0.10.0"}, "gitHead": "2d5e2dee577b6f8932b7a2e43629ed72281e17e0", "scripts": {"ci": "npm run lint && npm run test-cov", "lint": "jshint .", "test": "mocha -R spec -t 5000 -r should-http test/*.test.js", "autod": "autod -w --prefix '~'", "codecov": "npm i codecov && codecov", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- -t 5000 -r should-http test/*.test.js", "contributors": "contributors -f plain -o AUTHORS"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/agentkeepalive.git", "type": "git"}, "_npmVersion": "2.15.0", "description": "Missing keepalive http.Agent", "directories": {}, "_nodeVersion": "4.4.2", "dependencies": {}, "devDependencies": {"autod": "*", "mocha": "*", "jshint": "^2.9.1", "should": "4", "pedding": "1", "istanbul": "*", "should-http": "~0.0.2", "contributors": "*"}, "_npmOperationalInternal": {"tmp": "tmp/agentkeepalive-2.1.1.tgz_1459874955546_0.06252268934622407", "host": "packages-12-west.internal.npmjs.com"}}, "2.2.0": {"name": "agentkeepalive", "version": "2.2.0", "keywords": ["http", "https", "agent", "keepalive", "agentkeepalive"], "author": {"url": "http://fengmk2.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@2.2.0", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/dead-horse", "name": "dead-horse", "email": "<EMAIL>"}, {"url": "https://github.com/lattmann", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/pmalouin", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/saperal", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/DylanPiercey", "name": "<PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/node-modules/agentkeepalive#readme", "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "dist": {"shasum": "c5d1bd4b129008f1163f236f86e5faea2026e2ef", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-2.2.0.tgz", "integrity": "sha512-TnB6ziK363p7lR8QpeLC8aMr8EGYBKZTpgzQLfqTs3bR0Oo5VbKdwKf8h0dSzsYrB7lSCgfJnMZKqShvlq5Oyg==", "signatures": [{"sig": "MEUCIQCor9TSBkFNRsgLKnyr4+aF/T0a/ZyJeqTCa4xkuCRRpAIgLaqw474npTpIoTGDR5e/Umx3K6bIqxXXoOLifZ90IJE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "browser.js", "lib"], "_shasum": "c5d1bd4b129008f1163f236f86e5faea2026e2ef", "browser": "browser.js", "engines": {"node": ">= 0.10.0"}, "gitHead": "2a913cc13a5802283693577f46284247c3108aa2", "scripts": {"ci": "npm run lint && npm run test-cov", "lint": "jshint .", "test": "mocha -R spec -t 5000 -r should-http test/*.test.js", "codecov": "npm i codecov && codecov", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- -t 5000 -r should-http test/*.test.js"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/agentkeepalive.git", "type": "git"}, "_npmVersion": "2.15.5", "description": "Missing keepalive http.Agent", "directories": {}, "_nodeVersion": "4.4.6", "dependencies": {}, "devDependencies": {"mocha": "*", "jshint": "^2.9.1", "should": "4", "pedding": "1", "istanbul": "*", "should-http": "~0.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/agentkeepalive-2.2.0.tgz_1466918887669_0.06435476918704808", "host": "packages-16-east.internal.npmjs.com"}}, "3.0.0": {"name": "agentkeepalive", "version": "3.0.0", "keywords": ["http", "https", "agent", "keepalive", "agentkeepalive"], "author": {"url": "https://fengmk2.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@3.0.0", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/dead-horse", "name": "dead-horse", "email": "<EMAIL>"}, {"url": "https://github.com/lattmann", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/pmalouin", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/saperal", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/DylanPiercey", "name": "<PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/node-modules/agentkeepalive#readme", "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "ci": {"version": "4.3.2, 4, 6, 7"}, "dist": {"shasum": "677f16743988754fef16d44eb94cacf1726b7f14", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-3.0.0.tgz", "integrity": "sha512-z5Mt3LERjjdjN4X2IuKGb+kH5T05hNkoGwxonfv36qcPhJf+DOmJjyJN93LwaWdebmoWoly8drzRteP7ZWmoLw==", "signatures": [{"sig": "MEUCIQCBVOg1NywjU1sKZYrz6zr3bomP+417rl3F0vT1pOuizQIgO/tdRP355ZkZwvct+yholXh7+oqowtTJp5VtBDV3aJc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "browser.js", "lib"], "_shasum": "677f16743988754fef16d44eb94cacf1726b7f14", "browser": "browser.js", "engines": {"node": ">= 4.0.0"}, "gitHead": "fe733e77c505ef634965fa01d8066e200a2a83eb", "scripts": {"ci": "npm run lint && npm run cov", "cov": "egg-bin cov", "lint": "eslint lib test index.js", "test": "egg-bin test"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/agentkeepalive.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "Missing keepalive http.Agent", "directories": {}, "_nodeVersion": "6.9.2", "dependencies": {}, "devDependencies": {"egg-ci": "^1.1.0", "eslint": "^3.12.2", "egg-bin": "^1.9.1", "pedding": "1", "eslint-config-egg": "^3.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/agentkeepalive-3.0.0.tgz_1482168363521_0.593105707783252", "host": "packages-12-west.internal.npmjs.com"}}, "3.1.0": {"name": "agentkeepalive", "version": "3.1.0", "keywords": ["http", "https", "agent", "keepalive", "agentkeepalive"], "author": {"url": "https://fengmk2.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@3.1.0", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/dead-horse", "name": "dead-horse", "email": "<EMAIL>"}, {"url": "https://github.com/lattmann", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/pmalouin", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/saperal", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/DylanPiercey", "name": "<PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/node-modules/agentkeepalive#readme", "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "ci": {"version": "4.3.2, 4, 6, 7"}, "dist": {"shasum": "0393a4f1e68f85d355887c2e71681b28f3b7df35", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-3.1.0.tgz", "integrity": "sha512-4nsVMtu7Ckl6JURPIKt0yOv1YX5a8u+kM2WwtHvA3MOJQi44WLB3rheFhFf8GrJ4k20bpgYEM+8iaRHFJMq3Nw==", "signatures": [{"sig": "MEUCIQC0Y4zm1LdzHkvMwzn8D53dJzzm/pIYbNNkE2B8ojMEiQIgHHRFZzyH3gN1NJp/TwWAezk02pzDsRg4DBQJIiOFEBc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "browser.js", "lib"], "_shasum": "0393a4f1e68f85d355887c2e71681b28f3b7df35", "browser": "browser.js", "engines": {"node": ">= 4.0.0"}, "gitHead": "e2219df1b248475fc3816abcd50a726d3ca3cca0", "scripts": {"ci": "npm run lint && npm run cov", "cov": "egg-bin cov", "lint": "eslint lib test index.js", "test": "egg-bin test"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/agentkeepalive.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Missing keepalive http.Agent", "directories": {}, "_nodeVersion": "4.7.3", "dependencies": {"humanize-ms": "^1.2.0"}, "devDependencies": {"egg-ci": "^1.1.0", "eslint": "^3.12.2", "egg-bin": "^1.9.1", "pedding": "1", "eslint-config-egg": "^3.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/agentkeepalive-3.1.0.tgz_1487562760741_0.9611334570217878", "host": "packages-12-west.internal.npmjs.com"}}, "3.2.0": {"name": "agentkeepalive", "version": "3.2.0", "keywords": ["http", "https", "agent", "keepalive", "agentkeepalive"], "author": {"url": "https://fengmk2.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@3.2.0", "maintainers": [{"name": "fengmk2", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/dead-horse", "name": "dead-horse", "email": "<EMAIL>"}, {"url": "https://github.com/lattmann", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/pmalouin", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/saperal", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/DylanPiercey", "name": "<PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"url": "https://github.com/marciorodrigues87", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/agentkeepalive#readme", "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "ci": {"version": "4.3.2, 4, 6, 7, 8"}, "dist": {"shasum": "dcc9b272541d2fd2e9cf79fb0fdb192a6c5d60cd", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-3.2.0.tgz", "integrity": "sha512-2QsVsulmNFZDyMIZLQIpFnWsfX5JU3uOC/oGB1oEX8Q6rISfmYmhiGqBBKiOUxF+u8CpvkLCG/pmekM61L4MYA==", "signatures": [{"sig": "MEYCIQDBQG8eOKKlZvgUl/0dqsAmVLHMsE/0ulNGPErnpxZ+SwIhAKQOUrMeEweLK6IAT37MvO+XrsuJvxbiP/cswRqN7WH/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["index.js", "browser.js", "lib"], "browser": "browser.js", "engines": {"node": ">= 4.0.0"}, "gitHead": "6f7edb9ae836d9210af052b9ca0d696e0957d171", "scripts": {"ci": "npm run lint && npm run cov", "cov": "egg-bin cov", "lint": "eslint lib test index.js", "test": "egg-bin test", "autod": "autod"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/agentkeepalive.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "Missing keepalive http.Agent", "directories": {}, "_nodeVersion": "8.1.0", "dependencies": {"humanize-ms": "^1.2.1"}, "devDependencies": {"autod": "^2.8.0", "egg-ci": "^1.7.0", "eslint": "^3.19.0", "egg-bin": "^1.10.3", "pedding": "^1.1.0", "eslint-config-egg": "^4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/agentkeepalive-3.2.0.tgz_1497079347512_0.12120322906412184", "host": "s3://npm-registry-packages"}}, "3.3.0": {"name": "agentkeepalive", "version": "3.3.0", "keywords": ["http", "https", "agent", "keepalive", "agentkeepalive"], "author": {"url": "https://fengmk2.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@3.3.0", "maintainers": [{"name": "popomore", "email": "<EMAIL>"}, {"name": "dead_horse", "email": "<EMAIL>"}, {"name": "dead-horse", "email": "<EMAIL>"}, {"name": "fengmk2", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/dead-horse", "name": "dead-horse", "email": "<EMAIL>"}, {"url": "https://github.com/lattmann", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/pmalouin", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/saperal", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/DylanPiercey", "name": "<PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"url": "https://github.com/marciorodrigues87", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/agentkeepalive#readme", "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "ci": {"version": "4.3.2, 4, 6, 7, 8"}, "dist": {"shasum": "6d5de5829afd3be2712201a39275fd11c651857c", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-3.3.0.tgz", "integrity": "sha512-9yhcpXti2ZQE7bxuCsjjWNIZoQOd9sZ1ZBovHG0YeCRohFv73SLvcm73PC9T3olM4GyozaQb+4MGdQpcD8m7NQ==", "signatures": [{"sig": "MEUCIGS/Tt5eyLGXNXuEgQaBKdQOd4OkLM/PvmGd+0hjgLQBAiEAs42DKJ2gb3i5ICMW6IKmG1ndo05sAD7HiybtXEAa3cg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["index.js", "browser.js", "lib"], "browser": "browser.js", "engines": {"node": ">= 4.0.0"}, "gitHead": "7c46df116b7b9c0eb9f7e3925a44a6af56297c7d", "scripts": {"ci": "npm run lint && npm run cov", "cov": "egg-bin cov", "lint": "eslint lib test index.js", "test": "egg-bin test", "autod": "autod"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/agentkeepalive.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "Missing keepalive http.Agent", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {"humanize-ms": "^1.2.1"}, "devDependencies": {"autod": "^2.8.0", "egg-ci": "^1.7.0", "eslint": "^3.19.0", "egg-bin": "^1.10.3", "pedding": "^1.1.0", "eslint-config-egg": "^4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/agentkeepalive-3.3.0.tgz_1497930142598_0.03618317795917392", "host": "s3://npm-registry-packages"}}, "3.4.0": {"name": "agentkeepalive", "version": "3.4.0", "keywords": ["http", "https", "agent", "keepalive", "agentkeepalive"], "author": {"url": "https://fengmk2.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@3.4.0", "maintainers": [{"name": "dead_horse", "email": "<EMAIL>"}, {"name": "dead-horse", "email": "<EMAIL>"}, {"name": "fengmk2", "email": "<EMAIL>"}, {"name": "popomore", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/dead-horse", "name": "dead-horse", "email": "<EMAIL>"}, {"url": "https://github.com/lattmann", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/pmalouin", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/saperal", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/DylanPiercey", "name": "<PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"url": "https://github.com/marciorodrigues87", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/agentkeepalive#readme", "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "ci": {"version": "4.3.2, 4, 6, 8, 9"}, "dist": {"shasum": "92487926ec1a93100a89a8a46a6b2d82513543ce", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-3.4.0.tgz", "fileCount": 8, "integrity": "sha512-RypT3apziwtLsJTtab5kzqADuzWaYVqFPQo7X8QSYuteaw9GGNPsB5fTy8BVcCVish3cD9yLroR7oUVlZybhpQ==", "signatures": [{"sig": "MEUCIA+ECF7rjNmb2OW7h6SBLSMdqyBwTxgLKTba3NBoivKTAiEA0vmnV7TFz2wiSJB9F0dCricKHj01G7Ar/tY6g9nxdV4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30751}, "main": "index.js", "files": ["index.js", "browser.js", "lib"], "browser": "browser.js", "engines": {"node": ">= 4.0.0"}, "gitHead": "9322baa6adb1d80ed63a5ffec6b6aff4d64f9a2a", "scripts": {"ci": "npm run lint && npm run cov", "cov": "egg-bin cov", "lint": "eslint lib test index.js", "test": "egg-bin test", "autod": "autod"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/agentkeepalive.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Missing keepalive http.Agent", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"humanize-ms": "^1.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"autod": "^2.8.0", "egg-ci": "^1.7.0", "eslint": "^3.19.0", "egg-bin": "^1.10.3", "pedding": "^1.1.0", "eslint-config-egg": "^4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/agentkeepalive_3.4.0_1519727150128_0.1608892226380476", "host": "s3://npm-registry-packages"}}, "3.4.1": {"name": "agentkeepalive", "version": "3.4.1", "keywords": ["http", "https", "agent", "keepalive", "agentkeepalive"], "author": {"url": "https://fengmk2.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@3.4.1", "maintainers": [{"name": "dead_horse", "email": "<EMAIL>"}, {"name": "dead-horse", "email": "<EMAIL>"}, {"name": "fengmk2", "email": "<EMAIL>"}, {"name": "popomore", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/dead-horse", "name": "dead-horse", "email": "<EMAIL>"}, {"url": "https://github.com/lattmann", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/pmalouin", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/saperal", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/DylanPiercey", "name": "<PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"url": "https://github.com/marciorodrigues87", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/agentkeepalive#readme", "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "ci": {"version": "4.3.2, 4, 6, 8, 9"}, "dist": {"shasum": "aa95aebc3a749bca5ed53e3880a09f5235b48f0c", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-3.4.1.tgz", "fileCount": 8, "integrity": "sha512-MPIwsZU9PP9kOrZpyu2042kYA8Fdt/AedQYkYXucHgF9QoD9dXVp0ypuGnHXSR0hTstBxdt85Xkh4JolYfK5wg==", "signatures": [{"sig": "MEQCIAI5sUY1P/w45b0rkarmMnU+SlrB83Bxnx34Yi1At1cxAiA0wEBV0jELceRnoHSi7bx5j5Whn/g5MNUJVC/jn9zZ5g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31581}, "main": "index.js", "files": ["index.js", "browser.js", "lib"], "browser": "browser.js", "engines": {"node": ">= 4.0.0"}, "gitHead": "7cbca8e766cdf94ca4afbf3418f38b4f68e1b1de", "scripts": {"ci": "npm run lint && npm run cov", "cov": "egg-bin cov", "lint": "eslint lib test index.js", "test": "egg-bin test", "autod": "autod"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/agentkeepalive.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Missing keepalive http.Agent", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"humanize-ms": "^1.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"autod": "^2.8.0", "egg-ci": "^1.7.0", "eslint": "^3.19.0", "egg-bin": "^1.10.3", "pedding": "^1.1.0", "eslint-config-egg": "^4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/agentkeepalive_3.4.1_1520518061267_0.45985943272846863", "host": "s3://npm-registry-packages"}}, "3.5.0": {"name": "agentkeepalive", "version": "3.5.0", "keywords": ["http", "https", "agent", "keepalive", "agentkeepalive"], "author": {"url": "https://fengmk2.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@3.5.0", "maintainers": [{"name": "dead_horse", "email": "<EMAIL>"}, {"name": "dead-horse", "email": "<EMAIL>"}, {"name": "fengmk2", "email": "<EMAIL>"}, {"name": "popomore", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/dead-horse", "name": "dead-horse", "email": "<EMAIL>"}, {"url": "https://github.com/lattmann", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/pmalouin", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/saperal", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/DylanPiercey", "name": "<PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"url": "https://github.com/marciorodrigues87", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/agentkeepalive#readme", "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "ci": {"version": "4, 6, 8, 10"}, "dist": {"shasum": "f9d5aea53da99e86034295e7a3eb54f32254cb4d", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-3.5.0.tgz", "fileCount": 8, "integrity": "sha512-BcQ/ohRFRNgLZScxK5F/v+2ZP6UEsTOqK/0YmcfJJl/NRyI3pI8YmyT4/meS87PTN1xSJtpc8M9VygmwvMvLSA==", "signatures": [{"sig": "MEUCIQCKzqApY11+Iqg/SW6vQfITN3BEpWXlPJ/NwEoflpD4jAIgG2OEuNFdS7J0jwLMk2/cszbeLQIsnhRyRdjW5BD9IJQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32239, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbX9N8CRA9TVsSAnZWagAAfOgP/jKQo72GrZW8AmqilX8s\nXipxhMHry0r/Cmaoerh/Wh8m2esXGxt6HR+PHyIC2Y4w+rZ2SLmNyAm3Nb8W\n9UFWplb5aTZBaAsCcC8Ub0Z3G43oLyBRbMfUlOw+2couDTgNeVr2/eiEJK57\nlw1MnPcvbrbGLnPvawO67yTRUcuZVdCeovdeQ+OIwqjoawP9LHEd0vlgKps8\nLwY1ebs0uzrxrH98skMBE6klgEiuDXxFxnZfgkXojtXAUz3zOQ2WEpklZyCU\n/uA+WLa6rgoS7ZhpqoKarC4mhHRb/W/B3bYMyNSYCfUM/LoDuCp/RCDgVoPQ\nW/sxKBdnpelUkCn/5yyVXAwUiGc4Pq7+91NR3PZNDiSYxUqIWYzefBkUHbfv\nAjTqJRNyJRk/Wp0CUJ8NQPHLBpsZS/r6ek9CuhxNFdtWp7twgb6d5xOo40q3\n1bOjoCtvFVMy0oHxSxdrdxvmJTNun+rbMfCSJkWV88Cm2oOB2VvfkuYkCxVb\nG4GwTiF1toD1peULGCPEmbAZEuSlpsu6Ov6q6ejLvpvy9/8GGuPJ07UB4iKp\nG9MQgfoXEhCKwoTXn2TsazkoRIYZqBrZuO95HUfGL65NlIGedZujWnJ8/2Xp\nswbLMQ3Mf35kZBh4nbbYsIk0/L9+p381TK50FIgrH5C8qWVwr4y1eMqtlbJt\n7Eas\r\n=WMOZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "files": ["index.js", "browser.js", "lib"], "browser": "browser.js", "engines": {"node": ">= 4.0.0"}, "gitHead": "52a36e033645968cd0a482f143132aa5dc80452d", "scripts": {"ci": "npm run lint && npm run cov", "cov": "egg-bin cov", "lint": "eslint lib test index.js", "test": "egg-bin test", "autod": "autod"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/agentkeepalive.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Missing keepalive http.Agent", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"humanize-ms": "^1.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"autod": "^3.0.1", "egg-ci": "^1.8.0", "eslint": "^4.19.1", "egg-bin": "^1.11.1", "pedding": "^1.1.0", "eslint-config-egg": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/agentkeepalive_3.5.0_1533006716902_0.40398063695483466", "host": "s3://npm-registry-packages"}}, "3.5.1": {"name": "agentkeepalive", "version": "3.5.1", "keywords": ["http", "https", "agent", "keepalive", "agentkeepalive"], "author": {"url": "https://fengmk2.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@3.5.1", "maintainers": [{"name": "dead_horse", "email": "<EMAIL>"}, {"name": "dead-horse", "email": "<EMAIL>"}, {"name": "fengmk2", "email": "<EMAIL>"}, {"name": "popomore", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/dead-horse", "name": "dead-horse", "email": "<EMAIL>"}, {"url": "https://github.com/lattmann", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/pmalouin", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/saperal", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/DylanPiercey", "name": "<PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"url": "https://github.com/marciorodrigues87", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/agentkeepalive#readme", "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "ci": {"version": "4, 6, 8, 10"}, "dist": {"shasum": "4eba75cf2ad258fc09efd506cdb8d8c2971d35a4", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-3.5.1.tgz", "fileCount": 9, "integrity": "sha512-Cte/sTY9/XcygXjJ0q58v//SnEQ7ViWExKyJpLJlLqomDbQyMLh6Is4KuWJ/wmxzhiwkGRple7Gqv1zf6Syz5w==", "signatures": [{"sig": "MEUCIHjIMij24rlDToNH1GQBTD19k18jY0a8xrtnYS0lwMYiAiEAruQWmbkWZ1aVpsZW7gNGR2hwv3W5i/3ZRiz6HWtfed4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33579, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbX9kiCRA9TVsSAnZWagAA5y0P/0WGxUZ4OlOz0AWM8kSu\nd0l4jbPH2ETwz7T2VNcsdjCPkuMM3ffNiQ6LDwG8+1V7ziPOHkUkWbSEsWz7\nlsjwQ0EFBrAI4HbDECt0jz58c15xT6LzsFOmk5VB5KqrJN7MpJeXkFXAHi/t\n8RoQlKvm4RKYD3OimNgoz2XPJg22LAocyL1NGsQsXLhTfy5sFJHtUYY55z43\nx9kjMOE99PifloUHqeAUmIx9MoYajwCygizcWivcc4M/LQDz1Fle2c/a1U0P\npyxPf3fsS+/fbfxPayZ8NjRYvNwK9uWzNfgWWP1C+0jCa27cBUoAvBPuwqSX\nrqsjpRypD1crQ7OZd+9a1jRobTf/FlP9f8yD35vHDAYXyNVy7VzaK3yoBLQc\nNAc6QjQz8ywmwPbOPOeTpPHokAV8jACUqV3pBwud/wLys1dPKnSViSIaS3pl\n1ZH/KzeRxI7CRaFHYnkWo/Ht9sG6586skoQrM3JHh/irjDjhc/Es1SFwk3Bm\neQi9fsF06NELcnnM+emuVe6NxzUH9E5urxfXJHUfut3i0tehmBHKx9AmIz9d\nCeJlSpHBiVQvsVMJXeDgRRhTcUAKBX+xjk/4Cow0W4uByII0MNxVCM+tWwOk\n76OcAhA/TCfPh4x8k2sOsOQA+5KagPk8Z1IfkWy8J/8aOaB+C3KIImXb4cwo\nJ37V\r\n=X7V3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "files": ["index.js", "index.d.ts", "browser.js", "lib"], "browser": "browser.js", "engines": {"node": ">= 4.0.0"}, "gitHead": "82ff0e85bfbc282c4bad1ccfabf46d4a82943f6d", "scripts": {"ci": "npm run lint && npm run cov", "cov": "egg-bin cov", "lint": "eslint lib test index.js", "test": "egg-bin test", "autod": "autod"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/agentkeepalive.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Missing keepalive http.Agent", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"humanize-ms": "^1.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"autod": "^3.0.1", "egg-ci": "^1.8.0", "eslint": "^4.19.1", "egg-bin": "^1.11.1", "pedding": "^1.1.0", "eslint-config-egg": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/agentkeepalive_3.5.1_1533008162506_0.5135082950329302", "host": "s3://npm-registry-packages"}}, "3.5.2": {"name": "agentkeepalive", "version": "3.5.2", "keywords": ["http", "https", "agent", "keepalive", "agentkeepalive"], "author": {"url": "https://fengmk2.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@3.5.2", "maintainers": [{"name": "dead_horse", "email": "<EMAIL>"}, {"name": "dead-horse", "email": "<EMAIL>"}, {"name": "fengmk2", "email": "<EMAIL>"}, {"name": "popomore", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/dead-horse", "name": "dead-horse", "email": "<EMAIL>"}, {"url": "https://github.com/lattmann", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/pmalouin", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/saperal", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/DylanPiercey", "name": "<PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"url": "https://github.com/marciorodrigues87", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/agentkeepalive#readme", "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "ci": {"version": "4, 6, 8, 10"}, "dist": {"shasum": "a113924dd3fa24a0bc3b78108c450c2abee00f67", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-3.5.2.tgz", "fileCount": 9, "integrity": "sha512-e0L/HNe6qkQ7H19kTlRRqUibEAwDK5AFk6y3PtMsuut2VAH6+Q4xZml1tNDJD7kSAyqmbG/K08K5WEJYtUrSlQ==", "signatures": [{"sig": "MEYCIQCaN+xxuTMLlsX17XLAAfSrsTNXA/8Ad9IVqJKvC/IpxAIhAJJGlmDGdhMDDzjaphZeCdqoF0xg5NMKBd5pkky3mV/I", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33856, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbyM0rCRA9TVsSAnZWagAAdfcP/iuqBeQ0FWimc6r+AI6u\nJZPyyv2COo0NSYqJ/HRj5q6QCmGDQ/mQbI9RNvs0v3rNkWrbF/uTsEPmyarm\nl2DICLplp+PWtO5mgUh+eKM+t3qV1YnXEsevTAbs0Ss3pCX+RFfcy3dV2omO\nVekIQwSRV0/D9DrtcdSdLjvMDIEuU7eufukXHE2p/O8RrHRQ9sq8/az9Tczn\nIRBKi8ddhPny2V4zXrNHv3y67sStw/WycB8OwfT7dVeM6w1/1hEHkiHfb6Ka\nVxLOkRdrMFrd7hzbIBxqv5ZH4mvUM+fJeyFXnn3fFIEsgvHm+XzCzk0RKPtl\nv9UxqGJsL9jDnMnPQQAr/P9EhmBsiWjdmIzdpnw2Uzbt9lCuxH0olvDCwUy4\nY1OsE/UeqPSS1YHBTXloOVHquNoqz8DntNDcZJmxw/qW5a1hp0PJsCcgDWZt\n4B57pxw6+QANjQPhGl3CMaDJc8j1iVQQ+D3KGAWSugqKLILydCMudu5ApV+Y\nPztJnssBsB662mxI4AwzaWGsXZ0eDQkk7xcuZmltyP7J+Uu8R1H4+cT03rZI\nP7UVwfMGT2muYyU6n2kj/iF/wg3LggHiZm1SBwzGEhh0z5NsicYgNV7ioD5R\nSgv1UpCdhOgD4CL9CRtAr0bEKbnVKkJMPlFb59OSbEV4RO/Kmz/iZSQTx3I4\nuKvY\r\n=WJtV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">= 4.0.0"}, "gitHead": "9b81362c8b67f39b7e496168bc814bae95b2c654", "scripts": {"ci": "npm run lint && npm run cov", "cov": "egg-bin cov", "lint": "eslint lib test index.js", "test": "egg-bin test", "autod": "autod"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/agentkeepalive.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Missing keepalive http.Agent", "directories": {}, "_nodeVersion": "10.11.0", "dependencies": {"humanize-ms": "^1.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"autod": "^3.0.1", "egg-ci": "^1.8.0", "eslint": "^4.19.1", "egg-bin": "^1.11.1", "pedding": "^1.1.0", "eslint-config-egg": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/agentkeepalive_3.5.2_1539886378728_0.7187388240221688", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "agentkeepalive", "version": "4.0.0", "keywords": ["http", "https", "agent", "keepalive", "agentkeepalive", "HttpAgent", "HttpsAgent"], "author": {"url": "https://fengmk2.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@4.0.0", "maintainers": [{"name": "dead_horse", "email": "<EMAIL>"}, {"name": "dead-horse", "email": "<EMAIL>"}, {"name": "fengmk2", "email": "<EMAIL>"}, {"name": "popomore", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/dead-horse", "name": "dead-horse", "email": "<EMAIL>"}, {"url": "https://github.com/lattmann", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/pmalouin", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/saperal", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/DylanPiercey", "name": "<PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"url": "https://github.com/marciorodrigues87", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/agentkeepalive#readme", "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "ci": {"version": "8, 10"}, "dist": {"shasum": "777e7e4c828bde2690cc7a374ede587d45ba4271", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-4.0.0.tgz", "fileCount": 10, "integrity": "sha512-McCjHBAI3FGTy9XOMDr/8qzXK4VBL3h+O/TqlBdsyiwX21SdPCQhPIX0Y1HS5ctO5m2maVQRJWEn0IrzKRB6oQ==", "signatures": [{"sig": "MEUCIHQMdepPyKnMU37Dhrg2ykLtM8ix8/1SRD1ddKun+1qHAiEAwrdeSOpH1fooYpYM5K4V7FBOfx1Z6Nim3FQGQ7qHkXA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32071, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbzf3FCRA9TVsSAnZWagAA0iYP+wb9voqXSTuwdM88RdVd\nMlpn4LxnmrO72yJ2sbBMLq67h/qMuv0fOIv/klOol63k0doMItOvKLfD2fHN\n/0ZI8tknANi46jawTXrTTMoXW62WYQg51z8d1xSrLxtqWMmQlHMIIww2gxej\nXk19yXwShHhaSaoYyktikVhLjoSZr2+ziqLFZOqIPPUsX9M/i9AjugWD0vbO\nn57JZ24N2PtIn/eICSaPauqkCc1JQGaKnIInFitfWuCY9g/SwpSfldUnUx4g\nizx1TFfHXIuxBT9HluV03gronCf+Mc/v4J77b6raSWzdmSGZCUIQA6Y3fZtK\npaYMEleXHzqSp9CANyNJhok+rwtGoqBjJjlhLhsTTiHFaa9ziANpRPyfwBBG\nGhujNryTi+TroUq+z7Qfpb88NqCACJ1fAuY3INpyGjr2f4Uq4003y8fgfSgT\nKpOZ96k3rGnYQ6PtMao5oy4xwwkpGuSYupvpqaGdwUmI/ej1ILLdTNMgj/UE\nRM1cJ6/du7eTMgglZAcFAAmjFlTmsOvJrH+9V/BL1DVPSxhmyUZn4qxVEEEY\neR60/Ca/W496CeNg8PPn3+7TC5ZZL6LlS5TJppx+VsnUSBUMM7vcnBvZiaUd\ndCYyjR+ME2lR5mBfD4q+vxfOTn4nU+VmAIar+FALzcfYuNlRWtR+AS9IC2qH\nTdvc\r\n=zaxf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">= 8.0.0"}, "gitHead": "0de1968973acce3a373af5ed381b8c8da7b25e2b", "scripts": {"ci": "npm run lint && npm run cov", "cov": "egg-bin cov", "lint": "eslint lib test index.js", "test": "npm run lint && egg-bin test", "autod": "autod", "test-local": "egg-bin test"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/agentkeepalive.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Missing keepalive http.Agent", "directories": {}, "_nodeVersion": "8.11.4", "dependencies": {"depd": "^1.1.2", "debug": "^4.1.0", "humanize-ms": "^1.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"mm": "^2.4.1", "autod": "^3.0.1", "egg-ci": "^1.10.0", "eslint": "^5.7.0", "egg-bin": "^4.9.0", "pedding": "^1.1.0", "eslint-config-egg": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/agentkeepalive_4.0.0_1540226500501_0.7738264829072481", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "agentkeepalive", "version": "4.0.1", "keywords": ["http", "https", "agent", "keepalive", "agentkeepalive", "HttpAgent", "HttpsAgent"], "author": {"url": "https://fengmk2.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@4.0.1", "maintainers": [{"name": "dead_horse", "email": "<EMAIL>"}, {"name": "dead-horse", "email": "<EMAIL>"}, {"name": "fengmk2", "email": "<EMAIL>"}, {"name": "popomore", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/dead-horse", "name": "dead-horse", "email": "<EMAIL>"}, {"url": "https://github.com/lattmann", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/pmalouin", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/saperal", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/DylanPiercey", "name": "<PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"url": "https://github.com/marciorodrigues87", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/agentkeepalive#readme", "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "ci": {"version": "8, 10"}, "dist": {"shasum": "bf8b06b06ac13f09761eceaee650a1f7db55c775", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-4.0.1.tgz", "fileCount": 10, "integrity": "sha512-Jcflxjnsivff6EhbDTy9IQq15Cp0enI0wL/Yf95OJMlomvsl+HTwbCiKVBiFomzJBrC9nb3X6zqjU3W7j+kN/w==", "signatures": [{"sig": "MEUCIQCrsaj9H/mJ4bomKvJ82P80a1dJ9odwgB8A+wjCSnBRuAIgOnLeeJ5dSRfDHY9GLKGwi4VBMFN2lNyILfHUX+fe8zs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33134, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcbAtYCRA9TVsSAnZWagAAWeIQAIREKohj62F+Y7wgmaGx\nJeZPJ/DmL8qlG4xm4H4m5ugajACjrPDugSr5ypoXgOI01nX8AJCANIky9P6/\nPLpp/mL8LK+2LV19yMDWBO4QYgGEOppEv1QuVN6GslTx3Hw+NTfvk90o8uz0\n6ATqFDgj9gOvuiou1enCUjF8qy0qsXGR7R6F2xKsAJz+y+qScUoaqDOQfU0M\ng0rZ0AiK+SaZ/zpuNU5COuaxSWTPbnWY2xPIJIhPabvIRJBzkAWtC1qr/Kg8\n1DEmzHl0EjuXKAZYimUs1wM9LtwZNrMGoBPht5XlVr1saKkhuWBOob1CzCMK\nS3kahnYmmujVXabj+27Bq8PYodwZkvduCxjLsjVeZ4avfOoUHkXVZDUhzWB/\nywVMh47QnbYlXXnmGdMEd3UndsFBYxvlWd7mJRSNbjrNutikmuY8GHKKAi7X\nmMLaKTX7dy3A+OOQe+I0m13BJtfC49wcAW6hgzCR4ELX5ak3yk4Z7RTyfwHf\n4yJ/ztuZ1E0/ZddmbpMjSOpN/w/uHC7SDDuzv6Bbpayusr7ndciDDG1n7HcP\nwnA4gH1PzToZSU1iUqerCOaw6xjExY2DJB/mzgZbND3b7nzephu6K1tuzQfA\nOUm9+Gd6ctlv0AO9zlIvzdCZw0GttYY9ZQY+1GqwV6INW6JJMZwmyYTeWXOf\nUmIF\r\n=eWcL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">= 8.0.0"}, "gitHead": "f3d9c87f4af90f6026c904e112e47a95aaa2338d", "scripts": {"ci": "npm run lint && npm run cov", "cov": "egg-bin cov", "lint": "eslint lib test index.js", "test": "npm run lint && egg-bin test", "autod": "autod", "test-local": "egg-bin test"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/agentkeepalive.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "Missing keepalive http.Agent", "directories": {}, "_nodeVersion": "10.15.0", "dependencies": {"depd": "^1.1.2", "debug": "^4.1.0", "humanize-ms": "^1.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"mm": "^2.4.1", "autod": "^3.0.1", "egg-ci": "^1.10.0", "eslint": "^5.7.0", "egg-bin": "^4.9.0", "pedding": "^1.1.0", "eslint-config-egg": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/agentkeepalive_4.0.1_1550584663683_0.7256080071297932", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "agentkeepalive", "version": "4.0.2", "keywords": ["http", "https", "agent", "keepalive", "agentkeepalive", "HttpAgent", "HttpsAgent"], "author": {"url": "https://fengmk2.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@4.0.2", "maintainers": [{"name": "dead_horse", "email": "<EMAIL>"}, {"name": "dead-horse", "email": "<EMAIL>"}, {"name": "fengmk2", "email": "<EMAIL>"}, {"name": "popomore", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/dead-horse", "name": "dead-horse", "email": "<EMAIL>"}, {"url": "https://github.com/lattmann", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/pmalouin", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/saperal", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/DylanPiercey", "name": "<PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"url": "https://github.com/marciorodrigues87", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/agentkeepalive#readme", "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "ci": {"version": "8, 10"}, "dist": {"shasum": "38a490b779a97bd542d153e5a7da0d1fdef35dd3", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-4.0.2.tgz", "fileCount": 10, "integrity": "sha512-A5gSniD4xMCYtSD4ilUHpQRB9ZbNjtIPittKUv7bA0j0UCwbT3EJBUYLKPJ/dtmaXRYWI2mG4/O90xbi7oahNw==", "signatures": [{"sig": "MEYCIQCENA9khRmwdra6vcS9I0oOJhPK3BAeAc/YZUrxP62jMwIhAPj2ZcaZ8c2hr95wPI5tDVDUJJULN9aGuEASBtfyx5Q5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33340, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcbB22CRA9TVsSAnZWagAAqcMP/RCHpivoSkuWeJQvPTnJ\nTm+y/3uRgHuSufNEBEEUkQCt4eOQTw0ZUHNm24swsPzTc/gL0QlKMXs2AyX+\n5BQCDvZ0tpNV/rAvdKgQqz/YqWO9S4t9SYNu0y8dxxaQ3pprSH3ThplxilMU\nbtrMMxVJRz2IkcSMFp/WiH/skazL9ViVOS9MLpBHggIrx392ki2/KtMqoBDK\noNNRO0KpjkfD5a5jWVEhbkufBI6fkxA2BZRQwJCsW5EebC3q7dZQvmrXi9EH\ndKXo4/n8IkbiFzkONd9djtsTYpk0cY+9a2J0jDfEd1zwW00BYwxdNwc//SwP\nD+R8Go1gZHV/drrLDaU+5uIVjX8Zczn3DiaQ9YX074Cgkl8rCwgrM3q/7yuk\njWxOF+3KJ2LF+qlMhyaDZapJufJ74iVC3FjoTjtiqONt62jwh1ITAEX8d15t\nN4eexIbWoyC3AXbe9fcJE02bk4vScYGfPVrsExBGi50t8e0Hk+/0K9lrq4bC\n/33BMOZDabxDvAKAWTLZiLq/8/NnGC0CcjGFQzPuoZ7wBpisNMcZDoMGjP+K\n1gmzq0dVculuGklsJgTsq1JMgn0I6OI7Lj6N3E2k0nRg6hLfhhJZU9tVXgak\nUHPWDLVtgtv0/bif3lpNHyX2cLPg0XJ57kVt1ORO37okdEQgnbCv16MKmv5m\n2NK1\r\n=9MBI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">= 8.0.0"}, "gitHead": "48e01a4d3e15888d396d2a45305d976f654afdb7", "scripts": {"ci": "npm run lint && npm run cov", "cov": "egg-bin cov", "lint": "eslint lib test index.js", "test": "npm run lint && egg-bin test", "autod": "autod", "test-local": "egg-bin test"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/agentkeepalive.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "Missing keepalive http.Agent", "directories": {}, "_nodeVersion": "10.15.0", "dependencies": {"depd": "^1.1.2", "debug": "^4.1.0", "humanize-ms": "^1.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"mm": "^2.4.1", "autod": "^3.0.1", "egg-ci": "^1.10.0", "eslint": "^5.7.0", "egg-bin": "^4.9.0", "pedding": "^1.1.0", "eslint-config-egg": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/agentkeepalive_4.0.2_1550589365966_0.2554575440966036", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "agentkeepalive", "version": "4.1.0", "keywords": ["http", "https", "agent", "keepalive", "agentkeepalive", "HttpAgent", "HttpsAgent"], "author": {"url": "https://fengmk2.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@4.1.0", "maintainers": [{"name": "dead_horse", "email": "<EMAIL>"}, {"name": "dead-horse", "email": "<EMAIL>"}, {"name": "fengmk2", "email": "<EMAIL>"}, {"name": "popomore", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/dead-horse", "name": "dead-horse", "email": "<EMAIL>"}, {"url": "https://github.com/lattmann", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/pmalouin", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/saperal", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/DylanPiercey", "name": "<PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"url": "https://github.com/marciorodrigues87", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/agentkeepalive#readme", "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "ci": {"version": "8, 10"}, "dist": {"shasum": "a48e040ed16745dd29ce923675f60c9c90f39ee0", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-4.1.0.tgz", "fileCount": 10, "integrity": "sha512-CW/n1wxF8RpEuuiq6Vbn9S8m0VSYDMnZESqaJ6F2cWN9fY8rei2qaxweIaRgq+ek8TqfoFIsUjaGNKGGEHElSg==", "signatures": [{"sig": "MEUCIHNS8N/UHbSFHGCv75kcvIz96fzYetfmkXDQ8MiGWLr1AiEAoaTpQpS3wYn/rMzsFVvfm9bbSBybiq+bYdtXoGEJSZo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34764, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdoZHrCRA9TVsSAnZWagAAJDsQAImuXr4ETocBk6OL5wIH\nbI6hPmcWtO/ilMzJRKHhEzcdryzcao9J3KUjUlSAEsecpuDS+QCyoUNCCum/\n23yRzKlS2Byvyk4HM37IUXG1wnogrYc46fHED8yg5RhNkgdzi3Apo3HvQ6Re\nNIUhey1DlCpRsRJ+ba5Zs2tG5Dv4wNQgfiUSAvqzVnVpD2va93iPsUAIqsxE\nlcGOuiH+uZQRVXNCFp4wpy5pKjDerra5Pe+8tK+QM4ZF0NSPTfnSZ6lOcbf1\nhMb+LPKDEWqXmK1eOX66L7RgtTP/+rWv6IwPZJ7ZuVLp0uxEdTLmJcQIQt8b\n/z++3ewPW5kLfG5UwDy/b0AZ+gpXCaxpXbwsjr5T7v0YOqvV/As4fws4b4MO\nJIzqwJVsR1d8V4rFTh7aJ7zCUXhfTvgwCS/ZeRz5HVDCL82yhU0TNz9qfvoC\nNnMtT4XeDmY4MscMyjefxm6xSntYT4UL15dz8GseTOsxSWGfd5mQPdpcUJis\njsOk9kJ0/JnM9U8MUljUTO79UNkwsX1gm+ky7xVwxDbJEA50ge++K5jr1yWO\nLCoGGDkodUTiAmH1wjlCOZQviBr7q7KqQyOCU1yAjDW94R/pdSEh+TZGfT9J\nxHL+inxz5oO7a6a7Jn4V9msyTxEr2M50HQreA3RuEE4f3ZW4y6Gl1POS8RVI\nwq+n\r\n=yGmE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">= 8.0.0"}, "gitHead": "2a25153af13dd573fddc21245d6bcda93b4a715b", "scripts": {"ci": "npm run lint && npm run cov", "cov": "egg-bin cov", "lint": "eslint lib test index.js", "test": "npm run lint && egg-bin test", "autod": "autod", "test-local": "egg-bin test"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/agentkeepalive.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Missing keepalive http.Agent", "directories": {}, "_nodeVersion": "12.12.0", "dependencies": {"depd": "^1.1.2", "debug": "^4.1.0", "humanize-ms": "^1.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"mm": "^2.4.1", "autod": "^3.0.1", "egg-ci": "^1.10.0", "eslint": "^5.7.0", "egg-bin": "^4.9.0", "pedding": "^1.1.0", "eslint-config-egg": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/agentkeepalive_4.1.0_1570869738985_0.2109597588737957", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "agentkeepalive", "version": "4.1.1", "keywords": ["http", "https", "agent", "keepalive", "agentkeepalive", "HttpAgent", "HttpsAgent"], "author": {"url": "https://fengmk2.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@4.1.1", "maintainers": [{"name": "dead_horse", "email": "<EMAIL>"}, {"name": "dead-horse", "email": "<EMAIL>"}, {"name": "fengmk2", "email": "<EMAIL>"}, {"name": "popomore", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/dead-horse", "name": "dead-horse", "email": "<EMAIL>"}, {"url": "https://github.com/lattmann", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/pmalouin", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/saperal", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/DylanPiercey", "name": "<PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"url": "https://github.com/marciorodrigues87", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/agentkeepalive#readme", "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "ci": {"version": "8, 10, 12"}, "dist": {"shasum": "a161e1ae254d64010ad52b207bba1befbce9af6f", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-4.1.1.tgz", "fileCount": 10, "integrity": "sha512-dDiBQp9Y0rxRdtNnBYVtd8zNGBj6P/s+/CXYpXLDSCeJZngzjLWLNM/ZOUC8CHbFE+AD/Lm4HMhQfroysdgnew==", "signatures": [{"sig": "MEUCIQCBaOIhiL+d+54V6IQK<PERSON>cZevHob0iTfR0HyKzEsYQFfYgIgf3bDRqm+q4lYlW6trngtYFWc4hL96SyDi6adc1fbj9I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35590, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJepDdxCRA9TVsSAnZWagAAK90QAJnTNvxXtJPYMGIc/4Oh\nxbK3CGQpCRx6gJ43nLxmgLUSwOLjGSBItlVd9VMb9Q8DChX9Iv8iR4EgDxbw\ng9dXYwdaYG9zLhMuWW0do6/S717dx+tvTGTFK9PoDeNT7LWVfFvWVYjL3Igo\nFjuvTNVJfsRDXTROdgBiSKQPpZD8frRqfiJ8Gv57qnSyYCXwRIkY5RlUWiv+\ns6qoX0OenmgsfSxakVlStg8LOOKFWdCRB1E+qhvwPCs6x8OKN4QBd6fvqMSV\nVD2vv49uQ+ovAXRq8uLOa/Dtm82XtAiLHVvkNlcgMJENbSjBi6J6YgeqFOFF\noHlnb6HmdcZeTUKk98VidhAQfMboFbGh49ysO9zJeEr32DrHlJgc4WlK2yIW\n1KJfMAfUkhldD+bbvL3x0zOtzNQKrlwqbIjDPhtIvkC0giW9nURf3u2SjzdS\no4iZl2Ucou1fkkPP3RvtDV16qTjfnkssTwZG+LcBn4DShH4CaQGWTTmXSxht\n+v9JULD5qwbMfI/eGg3OArTuVT3/P3fBadLpzqH3BGrAdQ4Ed5ytD8BOqf3U\ndCdrZV8fpwyxni8tLaT2twypfBsh86heA+I3SyeAX6cf0LM/LzEipvUAy8eN\n/P5WoaJ4p/5xfD5wbjhOTmZoBvv5ICvONBp41+RUe8ooUU9mOUsFj991hcyq\nxnej\r\n=k8rj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">= 8.0.0"}, "gitHead": "567ab3532338482212423334e2a290cc01c94e1d", "scripts": {"ci": "npm run lint && npm run cov", "cov": "egg-bin cov", "lint": "eslint lib test index.js", "test": "npm run lint && egg-bin test", "autod": "autod", "test-local": "egg-bin test"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/agentkeepalive.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Missing keepalive http.Agent", "directories": {}, "_nodeVersion": "12.16.2", "dependencies": {"depd": "^1.1.2", "debug": "^4.1.0", "humanize-ms": "^1.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"mm": "^2.4.1", "autod": "^3.0.1", "coffee": "^5.3.0", "egg-ci": "^1.10.0", "eslint": "^5.7.0", "egg-bin": "^4.9.0", "pedding": "^1.1.0", "typescript": "^3.8.3", "eslint-config-egg": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/agentkeepalive_4.1.1_1587820400767_0.8933461356064938", "host": "s3://npm-registry-packages"}}, "4.1.2": {"name": "agentkeepalive", "version": "4.1.2", "keywords": ["http", "https", "agent", "keepalive", "agentkeepalive", "HttpAgent", "HttpsAgent"], "author": {"url": "https://fengmk2.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@4.1.2", "maintainers": [{"name": "dead_horse", "email": "<EMAIL>"}, {"name": "dead-horse", "email": "<EMAIL>"}, {"name": "fengmk2", "email": "<EMAIL>"}, {"name": "popomore", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/dead-horse", "name": "dead-horse", "email": "<EMAIL>"}, {"url": "https://github.com/lattmann", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/pmalouin", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/saperal", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/DylanPiercey", "name": "<PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"url": "https://github.com/marciorodrigues87", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/agentkeepalive#readme", "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "ci": {"os": {"github": "linux"}, "type": "github", "version": "8, 10, 12"}, "dist": {"shasum": "d7bc3bafd7ea88032f6be22b6f1389b3d42c9c91", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-4.1.2.tgz", "fileCount": 10, "integrity": "sha512-waNHE7tQBBn+2qXucI8HY0o2Y0OBPWldWOWsZwY71JcCm4SvrPnWdceFfB5NIXSqE8Ewq6VR/Qt5b1i69P6KCQ==", "signatures": [{"sig": "MEQCIBt00pjlQjiEnU69i2dyEJCF0qlpVBG9U/bEZudrm49WAiBfMjqEa0x9PxZrFYp8o1Fd1CQKzaDJxhC6bxrP9HbD+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36362, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJepFJNCRA9TVsSAnZWagAAo58P/2mFWh8yd19i4LHDljbK\n4imETYqCjKBAktT1HKwvnchcrXm8hbufIGGOTWclFZF7HXS4Urns7dHLCQQ4\nuPRJSZyrAELoXVitIRjFpquFGTOM8wP0mb+a/M8gU1MHLWl27z/zPsvphJMp\n6sQYGnTYfEV3C3AnRxNtYc++TUZv+JWhdy+uD8RtekI5a8kifI28DAuMNrmy\njCgdPQgKt/nNjw79hW+KV5+Tso+G6D7JjFLFbReb2k70zpgdWjR4LJicKi3Q\noITriifNS5ypJheR4uXgxGoMC0mUTwYQPx0afpnUjrDBQWB5kOnyRcjPZVwh\n117dZqXi4T0fWpZyuOFs/Gx7dUuy7i2NjWt83kWRJWa5Pe+GuZhzyq3BLEYR\n8vGcwaa6dZvljJFLiqFe0ZKH98wJmgh9pHXYG9ydRbuukoP7Kn5Z5AC9VvoY\nl9W8Lz51GZjO3JpxRnYuPDzj5eFu3iwxoEw4xMsck/lZMa+4aLGXbP3zUgpU\nVZZIU+eNXkzw2NSSVGVcYaq+HTYmmRusundoFEahuvr/+gN/dsRlTsOGOdtY\n0p/D5c+XrUXB9doJcK+Txixc3U5RhBX/zCO5AAvAVgFknT20BvWKM08mRTZf\n+jcafBh2Ut70ekDf+WT2kG11kMwjdzodHs4h08PckL/yVqB6LRbspJUb3fjL\nV7U9\r\n=RshN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">= 8.0.0"}, "gitHead": "d633af36cabca747fdb36dbba1f574e8e5b95058", "scripts": {"ci": "npm run lint && npm run cov", "cov": "cross-env DEBUG=agentkeepalive egg-bin cov --full-trace", "lint": "eslint lib test index.js", "test": "npm run lint && egg-bin test --full-trace", "autod": "autod", "test-local": "egg-bin test --full-trace"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/agentkeepalive.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Missing keepalive http.Agent", "directories": {}, "_nodeVersion": "12.16.2", "dependencies": {"depd": "^1.1.2", "debug": "^4.1.0", "humanize-ms": "^1.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"mm": "^2.4.1", "autod": "^3.0.1", "coffee": "^5.3.0", "egg-ci": "^1.10.0", "eslint": "^5.7.0", "egg-bin": "^4.9.0", "pedding": "^1.1.0", "cross-env": "^6.0.3", "typescript": "^3.8.3", "eslint-config-egg": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/agentkeepalive_4.1.2_1587827276585_0.0368166634273519", "host": "s3://npm-registry-packages"}}, "4.1.3": {"name": "agentkeepalive", "version": "4.1.3", "keywords": ["http", "https", "agent", "keepalive", "agentkeepalive", "HttpAgent", "HttpsAgent"], "author": {"url": "https://fengmk2.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@4.1.3", "maintainers": [{"name": "dead_horse", "email": "<EMAIL>"}, {"name": "dead-horse", "email": "<EMAIL>"}, {"name": "fengmk2", "email": "<EMAIL>"}, {"name": "popomore", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/dead-horse", "name": "dead-horse", "email": "<EMAIL>"}, {"url": "https://github.com/lattmann", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/pmalouin", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/saperal", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/DylanPiercey", "name": "<PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"url": "https://github.com/marciorodrigues87", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/agentkeepalive#readme", "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "ci": {"os": {"github": "linux"}, "type": "github", "version": "8, 10, 12, 14"}, "dist": {"shasum": "360a09d743a1f4fde749f9ba07caa6575d08259a", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-4.1.3.tgz", "fileCount": 10, "integrity": "sha512-wn8fw19xKZwdGPO47jivonaHRTd+nGOMP1z11sgGeQzDy2xd5FG0R67dIMcKHDE2cJ5y+YXV30XVGUBPRSY7Hg==", "signatures": [{"sig": "MEQCICBof1bh9jzH4yZpVEz1YwBCLXR2aWjou4suPMBb5aNCAiA5zjzdDkUw+Rb3ZDet3/SbdHO2rWZAE/lKOT+pyaBTog==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36924, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe5vmtCRA9TVsSAnZWagAAauUP/jxZXWACJDKB7fc8Mi5Z\nxvv54eCb0R+rLf6+ZXNlX1HuopNCNZOdhSqeuGkLlUhowU4rWldkJ6Yq2hp5\nc9twW1MwP+9iNzsk5hXSKFbQV5z/capVfssBRX/WWXKHliOelAhLm5FIhOWr\nWZHKBPhEwjxHBo1Q3GIi9c5pqaCZsH1ZiP5JYex1evrnnigYt96pIy1ivv/0\n7wio9uTqBToOEmaT1draXbqxilkK31xCV2NJdiIAJAnOgVWAyWj6SC4CmMLI\nC55ASSEYoYg8IWQfmT7R3yDje7i/0J99GuljNXf01Uy3v0Dujepazb+JrrcM\npxTahnKCiHW0jk8zAC2mpYsyyO9moNiVdwGSj5SZvXWj55HHUdFS4Jv5sC0Z\nVp2kghbLnQOyRheJplRuZ4YQTPMS8Scod3YtoFN4vbrRAzrA5gvpox9cMINZ\n9xCA3HCfiu1GzSCbpan+Jdw6ZrarZvU/VUa+1R1RriCCRcndPFlTxsn0AiXT\nZ7reUixeyJGM9xS1F5GPYwF1aKo1KOfi1Mhcm77f1ZsxqfyXqY15IkUVjWXC\nOPCXHUxAo4H8rxsIG3TLNspKgwMJAFEAw/T+aKkGnZnNlHLQwVgw9XhoS5lY\nVnEEAa431uyGt80Yoh5MFbWI31mYJoSZbMqY2wcI38XE3Q0osRh+eVzCHU9c\nHHrv\r\n=QHiz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">= 8.0.0"}, "gitHead": "18f5688651f128ed44eac906ae36cd3de3877c75", "scripts": {"ci": "npm run lint && npm run cov", "cov": "cross-env DEBUG=agentkeepalive egg-bin cov --full-trace", "lint": "eslint lib test index.js", "test": "npm run lint && egg-bin test --full-trace", "autod": "autod", "test-local": "egg-bin test --full-trace"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/agentkeepalive.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Missing keepalive http.Agent", "directories": {}, "_nodeVersion": "12.18.0", "dependencies": {"depd": "^1.1.2", "debug": "^4.1.0", "humanize-ms": "^1.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"mm": "^2.4.1", "autod": "^3.0.1", "coffee": "^5.3.0", "egg-ci": "^1.10.0", "eslint": "^5.7.0", "egg-bin": "^4.9.0", "pedding": "^1.1.0", "cross-env": "^6.0.3", "typescript": "^3.8.3", "eslint-config-egg": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/agentkeepalive_4.1.3_1592195500843_0.2116189226278422", "host": "s3://npm-registry-packages"}}, "4.1.4": {"name": "agentkeepalive", "version": "4.1.4", "keywords": ["http", "https", "agent", "keepalive", "agentkeepalive", "HttpAgent", "HttpsAgent"], "author": {"url": "https://fengmk2.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@4.1.4", "maintainers": [{"name": "dead-horse", "email": "<EMAIL>"}, {"name": "dead_horse", "email": "<EMAIL>"}, {"name": "fengmk2", "email": "<EMAIL>"}, {"name": "popomore", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/dead-horse", "name": "dead-horse", "email": "<EMAIL>"}, {"url": "https://github.com/lattmann", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/pmalouin", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/saperal", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/DylanPiercey", "name": "<PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"url": "https://github.com/marciorodrigues87", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/agentkeepalive#readme", "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "ci": {"os": {"github": "linux"}, "type": "github", "version": "8, 10, 12, 14"}, "dist": {"shasum": "d928028a4862cb11718e55227872e842a44c945b", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-4.1.4.tgz", "fileCount": 10, "integrity": "sha512-+V/rGa3EuU74H6wR04plBb7Ks10FbtUQgRj/FQOG7uUIEuaINI+AiqJR1k6t3SVNs7o7ZjIdus6706qqzVq8jQ==", "signatures": [{"sig": "MEYCIQDl8jFa5R4mW002vbCoxqDBqMYqtAPXnf0fxT1yEc3ayAIhAPBnQar3tXg6zE8BcchuM36uSuksRkyGhhRz+zCBAG5E", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37186, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgHKagCRA9TVsSAnZWagAA7eAP+wUtMICQUoTc/eNcW5FM\nyV6XfXrvEaAeuEziNeosbzkpyM93+pQf6VC6TWQFkErwV/jkUJxtDnYmnY8e\nXHeA3w9olrU7pK2SD61ifN4AvrcE44XLC3ImnvxakIhS1PTHKudIk2652851\nFVdnCO4z1b/4Ln8krW9quNvod+QnudHfUg3QtGuPNHgXEEh+a2IMEBlhNKJb\nGanvfrq7ZFKmOUVOKCth2uA8EREdiFd/HgxoQUiNWfdnMboGVXdYaca3Vti4\nkuH8oRVN3+FojwmgRNJoJWkcMbpLtYI/Bd1jPz7hqJHdafCPh1Hqr9CRVG3K\nGXaQsjvd50sgrsJHltoTZ14nPDrzuWWEkfdItxG25Kh2BxDOYKR4j/a9Jvvp\n+08VINNGHV5/8CZN5VuRxwvMNGww8AlzMt1Sqk4Sb98RAeDWoFIHposapsQZ\n8f320yvwXapDmNyuUq0FixrBDpvmOEw0a8O8+6VItXObaehfBE8ZKUlfXkvQ\nTmOPlYDDiJjB2o0vvkQQiRFKm4Gfh8bAvlfsmOykqla+Gz8EIkfMwFcxMJrs\nZBoE5qYFq3S2BI4Dz2aeAZcG+8TrvXa0S/T339hqK57ThfwstSsTorPGOwMs\nZBAvKd4bs3t7iBDHuqIyJjtQ6XWH9ocwyQparkM9k2Gm3xWNtmIzpnP8UF0S\nP7rv\r\n=mpAc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">= 8.0.0"}, "gitHead": "01e61e9e53526df2231152ef4287d38332f4db45", "scripts": {"ci": "npm run lint && npm run cov", "cov": "cross-env DEBUG=agentkeepalive egg-bin cov --full-trace", "lint": "eslint lib test index.js", "test": "npm run lint && egg-bin test --full-trace", "autod": "autod", "test-local": "egg-bin test --full-trace"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/agentkeepalive.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Missing keepalive http.Agent", "directories": {}, "_nodeVersion": "12.20.1", "dependencies": {"depd": "^1.1.2", "debug": "^4.1.0", "humanize-ms": "^1.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"mm": "^2.4.1", "autod": "^3.0.1", "coffee": "^5.3.0", "egg-ci": "^1.10.0", "eslint": "^5.7.0", "egg-bin": "^4.9.0", "pedding": "^1.1.0", "cross-env": "^6.0.3", "typescript": "^3.8.3", "eslint-config-egg": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/agentkeepalive_4.1.4_1612490400040_0.2855063189560194", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "agentkeepalive", "version": "4.2.0", "keywords": ["http", "https", "agent", "keepalive", "agentkeepalive", "HttpAgent", "HttpsAgent"], "author": {"url": "https://fengmk2.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@4.2.0", "maintainers": [{"name": "dead-horse", "email": "<EMAIL>"}, {"name": "dead_horse", "email": "<EMAIL>"}, {"name": "fengmk2", "email": "<EMAIL>"}, {"name": "popomore", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/dead-horse", "name": "dead-horse", "email": "<EMAIL>"}, {"url": "https://github.com/lattmann", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/pmalouin", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/saperal", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/DylanPiercey", "name": "<PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"url": "https://github.com/marciorodrigues87", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/agentkeepalive#readme", "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "ci": {"os": {"github": "linux"}, "type": "github", "version": "8, 10, 12, 14, 16"}, "dist": {"shasum": "616ce94ccb41d1a39a45d203d8076fe98713062d", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-4.2.0.tgz", "fileCount": 10, "integrity": "sha512-0PhAp58jZNw13UJv7NVdTGb0ZcghHUb3DrZ046JiiJY/BOaTTpbwdHq2VObPCBV8M2GPh7sgrJ3AQ8Ey468LJw==", "signatures": [{"sig": "MEUCIQCZmNod964BgUx69fIHYUlsLTn6yh1v3g+L096xgEk26gIgeE+wjiDCCE0OQ+hT/UWmVRj0vcOjs9Abal/YvSo8WD8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38465, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzoduCRA9TVsSAnZWagAAA+YP/iLqPRhX0H8h+GVw7slI\nOGxlhYbUPuWfxM7rPse3tnND66kyvoBdI23u7K/+Y/4F+HA0xBgxdQLi1oCl\nN2yfpGNW4JaedixAM1ihd3L2P71MPEYVmlTnlr6G5fKqrtQOyExPpQPA6v3M\n+qwkhTo4POcQ+Vwrtj0FeujxFQhBNH0wfNJMAxF0DLH25HGzTQU+48fOa2J4\nBZdh5AmXl82J6Gs6w/Ke6sqf0/01Sbslr2IKubYeZfGIR12vND2Tf2a34Yj+\nSOsj0pupSlo7Cjecuh28DRy8p4o5VjOIby9GURyiBTF2VuCubLa15Vhuv5jK\nUAVoLjluXGicP6xFaqYpwW4DvK2X9Cz5Qj0DI1LQQ2cGYcnHzRgC7CNe1Glg\nT6IPtvtKzMbpi09dmuYGmtGuspokKQAaUz2xX993jKi4uEwqgCRL5eQzcGJo\nC3zAkWHEMG+o0a4OOlvBzldImzpFNCdrbg0oy/hkUYav2vWprUalYngxx+AZ\nBuMhdAMPu8FiAIUnK7+D5Swogv+cWYdQBrRo0n8TBHBtRKU82peFbYYHTX/G\n/xv91sUtlDYhdkfwgIHcpK+pyP1GmixC5FrISDlMkzq88sByN+K5kLoC3fY/\nV/Z5jXqnkD6g107pT8rq0M0aQFSl0dLASpjLxbpnbkG7AD4jmtfWTxYzazMx\nMsjk\r\n=jRkQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">= 8.0.0"}, "gitHead": "49e363a8f17a9c20ea98efd35798dd23a9fcd5f5", "scripts": {"ci": "npm run lint && npm run cov", "cov": "cross-env DEBUG=agentkeepalive egg-bin cov --full-trace", "lint": "eslint lib test index.js", "test": "npm run lint && egg-bin test --full-trace", "autod": "autod", "test-local": "egg-bin test --full-trace"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/agentkeepalive.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "Missing keepalive http.Agent", "directories": {}, "_nodeVersion": "16.13.1", "dependencies": {"depd": "^1.1.2", "debug": "^4.1.0", "humanize-ms": "^1.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"mm": "^2.4.1", "autod": "^3.0.1", "coffee": "^5.3.0", "egg-ci": "^1.10.0", "eslint": "^5.7.0", "egg-bin": "^4.9.0", "pedding": "^1.1.0", "cross-env": "^6.0.3", "typescript": "^3.8.3", "eslint-config-egg": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/agentkeepalive_4.2.0_1640925037895_0.19732360208417643", "host": "s3://npm-registry-packages"}}, "4.2.1": {"name": "agentkeepalive", "version": "4.2.1", "keywords": ["http", "https", "agent", "keepalive", "agentkeepalive", "HttpAgent", "HttpsAgent"], "author": {"url": "https://fengmk2.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@4.2.1", "maintainers": [{"name": "dead-horse", "email": "<EMAIL>"}, {"name": "dead_horse", "email": "<EMAIL>"}, {"name": "fengmk2", "email": "<EMAIL>"}, {"name": "popomore", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/dead-horse", "name": "dead-horse", "email": "<EMAIL>"}, {"url": "https://github.com/lattmann", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/pmalouin", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/saperal", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/DylanPiercey", "name": "<PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"url": "https://github.com/marciorodrigues87", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/agentkeepalive#readme", "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "ci": {"os": {"github": "linux"}, "type": "github", "version": "8, 10, 12, 14, 16"}, "dist": {"shasum": "a7975cbb9f83b367f06c90cc51ff28fe7d499717", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-4.2.1.tgz", "fileCount": 10, "integrity": "sha512-Zn4cw2NEqd+9fiSVWMscnjyQ1a8Yfoc5oBajLeo5w+YBHgDUcEBY2hS4YpTz6iN5f/2zQiktcuM6tS8x1p9dpA==", "signatures": [{"sig": "MEUCIBq9NoardCOXLteUG8giVoVzAlIu1TGH9Chj9Ghw2BAAAiEAlfuRwddYckAonvp7SUKhY4Elzyn3dKTJ5SnrQVSZsts=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38796, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiE6nvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmosUQ//VfPZCmDB2G8oO0cGX1Qqr9hURrNdKF+OWOZ5IzHwlpgs5M5d\r\nnOFPcFKaCH9hL5N6beWsu9kLEDA7YSLwj9y5KQ92hgMhWCnnatjhtM/6r34x\r\nDB9MPPm6gO0Ro2GpMtxfRupS95T8qCMnPK9OmbCMG7JHEcyc+DmhzHpJFhcy\r\nIHo2VlBpLIE1szpXOArMxhbSC74r85EGdB5F8GmJO6bheeJZfwpZDP5XZ2+5\r\ni0zyjNMWpRPd3cDmdNyhsHSe6LpWMXMAjiWtdh/Dr0EQW5fvxUQBekKMC1op\r\nLnH2Kx6dUYZdMkVXy2uLvEkJM9wY6yTXIWBeNEFE6hJWcxn+BOr4mtMmc10u\r\nolDN2vstmjwJGvNL0NdipEKZmI3Ifl5VGbAGNlWUSmwN9gpuPBxtSDh693Rh\r\n0vQUVpyOW9QovC6Gdrc32Pm3JAkSTFKfQhkYypj47F7nEQRAQYPWq2ONrNT3\r\n6NWqhEitXydzAcKxEEhgWfqalHEfQL3a++qTsGq0Z35F+URPyDaaqtU/rUDG\r\nWUdz8RZ4w61YoFb4Fj2vuFkubDp3JJDAJp3aWyPWdTsWD/Ku9/eqYQKzildE\r\ndNhdtWkCw36cXMW1fMCvEqhluFjeqwsIdDSJn5s/zgo5yPYEiAH/P1/8rIPn\r\nWwpF9hkU98ge8Fja1pl//tPcMzcDwU4P+Ws=\r\n=rgeA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">= 8.0.0"}, "gitHead": "04c95516b952a9bc44ee3bc6e2e5208722ab1906", "scripts": {"ci": "npm run lint && npm run cov", "cov": "cross-env DEBUG=agentkeepalive egg-bin cov --full-trace", "lint": "eslint lib test index.js", "test": "npm run lint && egg-bin test --full-trace", "autod": "autod", "test-local": "egg-bin test --full-trace"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/agentkeepalive.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "Missing keepalive http.Agent", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"depd": "^1.1.2", "debug": "^4.1.0", "humanize-ms": "^1.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"mm": "^2.4.1", "autod": "^3.0.1", "coffee": "^5.3.0", "egg-ci": "^1.10.0", "eslint": "^5.7.0", "egg-bin": "^4.9.0", "pedding": "^1.1.0", "cross-env": "^6.0.3", "typescript": "^3.8.3", "eslint-config-egg": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/agentkeepalive_4.2.1_1645455855447_0.6516260509846938", "host": "s3://npm-registry-packages"}}, "4.3.0": {"name": "agentkeepalive", "version": "4.3.0", "keywords": ["http", "https", "agent", "keepalive", "agentkeepalive", "HttpAgent", "HttpsAgent"], "author": {"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@4.3.0", "maintainers": [{"name": "dead-horse", "email": "<EMAIL>"}, {"name": "dead_horse", "email": "<EMAIL>"}, {"name": "fengmk2", "email": "<EMAIL>"}, {"name": "popomore", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/agentkeepalive#readme", "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "dist": {"shasum": "bb999ff07412653c1803b3ced35e50729830a255", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-4.3.0.tgz", "fileCount": 10, "integrity": "sha512-7Epl1Blf4Sy37j4v9f9FjICCh4+KAQOyXgHEwlyBiAQLbhKdq/i2QQU3amQalS/wPhdPzDXPL5DMR5bkn+YeWg==", "signatures": [{"sig": "MEUCIAxNf9nEpe8iMI5uEBmSYpOMOvPc5wDep1UNoxIMcVoqAiEAhJzEqI1m4wpxfnAuPFkY0mQOvv202D0sIMT5xwGO4Ek=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42812, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBWx0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqnGA//UN5/u4azxcQAy5MtPwdy1GvY5OPsp45b8vkKfIWFAmhO1495\r\nC4b9hTexcpeimdzv3OBMUuhqK5NZlvpOzwQE2Rh9DIuaOOp/anKhKyB3Neuk\r\nHey6kt+8lk4pE+PBLao8f1wRxjXagtVd/4gJbWWJjDirE7KIT/MxseB4l8Pt\r\nil2aiYBsv2ED+Sn3NTb6W3B/9ToMfi2UjZmYrnKOsJI/TQh8qrEVlPJpCP//\r\nuYEJVs+DlLbx5geDE2L94OfgmB/tAtDPJotCxlpdtZJqOmRSpFlkSLZU9PVO\r\nyoVPR0DSDn53OdhXOa/gzoYmsrhIm6HEh+vHe2u/AvYCITfMA9AViFE8t9I8\r\nniUhS9no0ZxqzCVojC5y4Ki9zA4FFWOnMi6SeEwqM0IC6nxGROicHP+eLzO2\r\n/IT6+q9Ji7D64bu0ugNsZejKtBzukHugQk7uVKvhIHfJtDabOlsa/wC3kAJ9\r\nTlN8I+mtUiNi9m12gsEkO94oW53WQAd9D1MrBZqTKX5mP41AeB7ZCmK/rL7d\r\n3n9gUL2GdkQB6NuPw0nMed/sG784yXTthqG5SXsggDTMQlWSL+q1P780yUVg\r\nn5ql/nAWfaUNiSQKVSTXcKz1rfB5VW2T5XiWLgPfwgXISR8aTl3TXNvsCz4J\r\nugu/7W8GNtKJU5kViFs+8RmZVLF8ZKen/i8=\r\n=Smvs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">= 8.0.0"}, "gitHead": "209271446fd157080874f7a1529c23e8964cc66f", "scripts": {"ci": "npm run lint && npm run cov", "cov": "cross-env DEBUG=agentkeepalive egg-bin cov --full-trace", "lint": "eslint lib test index.js", "test": "npm run lint && egg-bin test --full-trace", "test-local": "egg-bin test --full-trace", "contributor": "git-contributor"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/agentkeepalive.git", "type": "git"}, "_npmVersion": "6.14.17", "description": "Missing keepalive http.Agent", "directories": {}, "_nodeVersion": "18.14.1", "dependencies": {"depd": "^2.0.0", "debug": "^4.1.0", "humanize-ms": "^1.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"mm": "^2.4.1", "coffee": "^5.3.0", "eslint": "^5.7.0", "egg-bin": "^4.9.0", "pedding": "^1.1.0", "cross-env": "^6.0.3", "typescript": "^3.8.3", "git-contributor": "^2.0.0", "eslint-config-egg": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/agentkeepalive_4.3.0_1678077044605_0.45022699016584933", "host": "s3://npm-registry-packages"}}, "4.4.0": {"name": "agentkeepalive", "version": "4.4.0", "keywords": ["http", "https", "agent", "keepalive", "agentkeepalive", "HttpAgent", "HttpsAgent"], "author": {"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@4.4.0", "maintainers": [{"name": "dead-horse", "email": "<EMAIL>"}, {"name": "dead_horse", "email": "<EMAIL>"}, {"name": "fengmk2", "email": "<EMAIL>"}, {"name": "popomore", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/agentkeepalive#readme", "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "dist": {"shasum": "6304d7f60e503ecd7d39b43942f7698d77c60497", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-4.4.0.tgz", "fileCount": 10, "integrity": "sha512-MysLRwkhsJTZKs+fsZIsTgBlr3IjQroonVJWMSqC9k3LS6f6ZifePl9fCqOtvc8p0CeYDSZVFvytdkwhOGaSZA==", "signatures": [{"sig": "MEYCIQDxzbX/fA+488X19Z227AyEBHzr/LSwqwUFzu8R8VO1JAIhAPK0M61U9kXsaBqx/SFGyd1SbQB0RR8eJd0ax6iH4/Id", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43457}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">= 8.0.0"}, "gitHead": "6479ff48ad43862d2f025dcd40a41252f128911b", "scripts": {"ci": "npm run lint && npm run cov", "cov": "cross-env DEBUG=agentkeepalive egg-bin cov --full-trace", "lint": "eslint lib test index.js", "test": "npm run lint && egg-bin test --full-trace", "test-local": "egg-bin test --full-trace", "contributor": "git-contributor"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/agentkeepalive.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Missing keepalive http.Agent", "directories": {}, "_nodeVersion": "18.17.0", "dependencies": {"depd": "^2.0.0", "debug": "^4.1.0", "humanize-ms": "^1.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"mm": "^2.4.1", "coffee": "^5.3.0", "eslint": "^5.7.0", "egg-bin": "^4.9.0", "pedding": "^1.1.0", "cross-env": "^6.0.3", "typescript": "^3.8.3", "git-contributor": "^2.0.0", "eslint-config-egg": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/agentkeepalive_4.4.0_1691174217417_0.9244335799886405", "host": "s3://npm-registry-packages"}}, "4.5.0": {"name": "agentkeepalive", "version": "4.5.0", "keywords": ["http", "https", "agent", "keepalive", "agentkeepalive", "HttpAgent", "HttpsAgent"], "author": {"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@4.5.0", "maintainers": [{"name": "dead-horse", "email": "<EMAIL>"}, {"name": "dead_horse", "email": "<EMAIL>"}, {"name": "fengmk2", "email": "<EMAIL>"}, {"name": "popomore", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/agentkeepalive#readme", "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "dist": {"shasum": "2673ad1389b3c418c5a20c5d7364f93ca04be923", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-4.5.0.tgz", "fileCount": 10, "integrity": "sha512-5GG/5IbQQpC9FpkRGsSvZI5QYeSCzlJHdpBQntCsuTOxhKD8lqKhrleg2Yi7yvMIf82Ycmmqln9U8V9qwEiJew==", "signatures": [{"sig": "MEQCIHF1VQcfeKnD3Ho2G16x+3vQwo1NsQBFSYkIf2zLzp/SAiAEomM/ws5cVr9ycmzcdR162WwVyqsjMkXeERFelkkJ2Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43696}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">= 8.0.0"}, "gitHead": "6db01a0c45ca4e0d9dac12147329fb3539f8728f", "scripts": {"ci": "npm run lint && npm run cov", "cov": "cross-env NODE_DEBUG=agentkeepalive egg-bin cov --full-trace", "lint": "eslint lib test index.js", "test": "npm run lint && egg-bin test --full-trace", "test-local": "egg-bin test --full-trace", "contributor": "git-contributor"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/agentkeepalive.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Missing keepalive http.Agent", "directories": {}, "_nodeVersion": "18.17.0", "dependencies": {"humanize-ms": "^1.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"mm": "^2.4.1", "coffee": "^5.3.0", "eslint": "^5.7.0", "egg-bin": "^4.9.0", "pedding": "^1.1.0", "cross-env": "^6.0.3", "typescript": "^3.8.3", "git-contributor": "^2.0.0", "eslint-config-egg": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/agentkeepalive_4.5.0_1691335852337_0.06213152533231869", "host": "s3://npm-registry-packages"}}, "3.5.3": {"name": "agentkeepalive", "version": "3.5.3", "keywords": ["http", "https", "agent", "keepalive", "agentkeepalive"], "author": {"url": "https://fengmk2.com", "name": "fengmk2", "email": "<EMAIL>"}, "license": "MIT", "_id": "agentkeepalive@3.5.3", "maintainers": [{"name": "dead-horse", "email": "<EMAIL>"}, {"name": "dead_horse", "email": "<EMAIL>"}, {"name": "fengmk2", "email": "<EMAIL>"}, {"name": "popomore", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/fengmk2", "name": "fengmk2", "email": "<EMAIL>"}, {"url": "https://github.com/willwhite", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/dead-horse", "name": "dead-horse", "email": "<EMAIL>"}, {"url": "https://github.com/lattmann", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/pmalouin", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/saperal", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/DylanPiercey", "name": "<PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}, {"url": "https://github.com/marciorodrigues87", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/node-modules/agentkeepalive#readme", "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "dist": {"shasum": "c210afce942b4287e2df2fbfe6c0d57eda2ce634", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-3.5.3.tgz", "fileCount": 9, "integrity": "sha512-yqXL+k5rr8+ZRpOAntkaaRgWgE5o8ESAj5DyRmVTCSoZxXmqemb9Dd7T4i5UzwuERdLAJUy6XzR9zFVuf0kzkw==", "signatures": [{"sig": "MEQCIEfAh83Np+Q9BZEX8pVHHBiTMwbP01ClAUIXXPmOdye9AiAExb/Q1bcfE5wcSslxbKEFeABvWegK32TNIlai6RAjiQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34211}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">= 4.0.0"}, "gitHead": "8ac7a27bb54137351e6ec8ef3efcf62cddbc3b40", "scripts": {"ci": "npm run lint && npm run cov", "cov": "egg-bin cov", "lint": "eslint lib test index.js", "test": "egg-bin test"}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-modules/agentkeepalive.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Missing keepalive http.Agent", "directories": {}, "_nodeVersion": "14.21.3", "dependencies": {"humanize-ms": "^1.2.1"}, "publishConfig": {"tag": "latest-3"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"eslint": "^4.19.1", "egg-bin": "^1.11.1", "pedding": "^1.1.0", "eslint-config-egg": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/agentkeepalive_3.5.3_1715183016101_0.4468229670107169", "host": "s3://npm-registry-packages"}}, "4.6.0": {"name": "agentkeepalive", "version": "4.6.0", "description": "Missing keepalive http.Agent", "main": "index.js", "browser": "browser.js", "scripts": {"contributor": "git-contributor", "test": "npm run lint && egg-bin test --full-trace", "test-local": "egg-bin test --full-trace", "cov": "cross-env NODE_DEBUG=agentkeepalive egg-bin cov --full-trace", "ci": "npm run lint && npm run cov", "lint": "eslint lib test index.js"}, "repository": {"type": "git", "url": "git://github.com/node-modules/agentkeepalive.git"}, "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "keywords": ["http", "https", "agent", "keepalive", "agentkeepalive", "HttpAgent", "HttpsAgent"], "dependencies": {"humanize-ms": "^1.2.1"}, "devDependencies": {"coffee": "^5.3.0", "cross-env": "^6.0.3", "egg-bin": "^4.9.0", "eslint": "^5.7.0", "eslint-config-egg": "^7.1.0", "git-contributor": "^2.0.0", "mm": "^2.4.1", "pedding": "^1.1.0", "typescript": "^3.8.3"}, "engines": {"node": ">= 8.0.0"}, "author": {"name": "fengmk2", "email": "<EMAIL>", "url": "https://github.com/fengmk2"}, "license": "MIT", "_id": "agentkeepalive@4.6.0", "gitHead": "422e9fc35aaa076da6120f634f85ffc8c860d85a", "types": "./index.d.ts", "homepage": "https://github.com/node-modules/agentkeepalive#readme", "_nodeVersion": "22.12.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-kja8j7PjmncONqaTsB8fQ+wE2mSU2DJ9D4XKoJ5PFWIdRMa6SLSN1ff4mOr4jCbfRSsxR4keIiySJU0N9T5hIQ==", "shasum": "35f73e94b3f40bf65f105219c623ad19c136ea6a", "tarball": "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-4.6.0.tgz", "fileCount": 9, "unpackedSize": 33916, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHIBstwDsGZiSAnGBCUSe0wJrXECkYCK92CtcDxlyMi4AiAWIq4vBpTFax+tRABg5ZBDEmEivXximCyQTi0HCJ7n4w=="}]}, "_npmUser": {"name": "fengmk2", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "dead-horse", "email": "<EMAIL>"}, {"name": "dead_horse", "email": "<EMAIL>"}, {"name": "fengmk2", "email": "<EMAIL>"}, {"name": "popomore", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/agentkeepalive_4.6.0_1735441031258_0.6666414305023092"}, "_hasShrinkwrap": false}}, "time": {"created": "2012-08-24T13:22:15.355Z", "modified": "2024-12-29T02:57:11.634Z", "0.0.1": "2012-08-24T13:22:20.344Z", "0.0.3": "2012-08-25T18:29:01.374Z", "0.1.0": "2012-08-27T18:44:04.943Z", "0.1.1": "2012-08-28T12:20:46.377Z", "0.1.2": "2012-08-29T08:55:56.729Z", "0.1.3": "2012-08-30T13:47:27.734Z", "0.1.4": "2012-09-20T04:11:17.715Z", "0.1.5": "2012-09-23T15:02:45.864Z", "0.2.0": "2013-11-07T01:38:18.080Z", "0.2.1": "2013-11-08T09:23:02.749Z", "0.2.2": "2013-11-18T17:17:31.730Z", "1.0.0": "2014-08-12T17:44:20.777Z", "1.1.0": "2014-08-28T10:54:28.974Z", "1.2.0": "2014-09-02T16:02:45.454Z", "0.2.3": "2014-10-16T05:31:59.672Z", "0.2.4": "2015-03-23T10:51:46.833Z", "1.2.1": "2015-03-23T14:16:31.172Z", "2.0.0": "2015-04-01T15:38:59.596Z", "2.0.1": "2015-04-18T16:21:22.552Z", "2.0.2": "2015-04-25T06:30:35.375Z", "2.0.3": "2015-08-03T04:06:11.145Z", "2.0.4": "2016-03-13T15:01:19.842Z", "2.0.5": "2016-03-16T11:37:04.408Z", "2.1.0": "2016-04-02T03:00:25.352Z", "2.1.1": "2016-04-05T16:49:16.037Z", "2.2.0": "2016-06-26T05:28:10.045Z", "3.0.0": "2016-12-19T17:26:05.553Z", "3.1.0": "2017-02-20T03:52:40.964Z", "3.2.0": "2017-06-10T07:22:27.600Z", "3.3.0": "2017-06-20T03:42:22.688Z", "3.4.0": "2018-02-27T10:25:50.280Z", "3.4.1": "2018-03-08T14:07:41.323Z", "3.5.0": "2018-07-31T03:11:56.988Z", "3.5.1": "2018-07-31T03:36:02.572Z", "3.5.2": "2018-10-18T18:12:58.863Z", "4.0.0": "2018-10-22T16:41:40.736Z", "4.0.1": "2019-02-19T13:57:43.827Z", "4.0.2": "2019-02-19T15:16:06.107Z", "4.1.0": "2019-10-12T08:42:19.120Z", "4.1.1": "2020-04-25T13:13:20.978Z", "4.1.2": "2020-04-25T15:07:56.753Z", "4.1.3": "2020-06-15T04:31:41.031Z", "4.1.4": "2021-02-05T02:00:00.217Z", "4.2.0": "2021-12-31T04:30:38.024Z", "4.2.1": "2022-02-21T15:04:15.583Z", "4.3.0": "2023-03-06T04:30:44.796Z", "4.4.0": "2023-08-04T18:36:57.563Z", "4.5.0": "2023-08-06T15:30:52.495Z", "3.5.3": "2024-05-08T15:43:36.244Z", "4.6.0": "2024-12-29T02:57:11.450Z"}, "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "author": {"name": "fengmk2", "email": "<EMAIL>", "url": "https://github.com/fengmk2"}, "license": "MIT", "homepage": "https://github.com/node-modules/agentkeepalive#readme", "keywords": ["http", "https", "agent", "keepalive", "agentkeepalive", "HttpAgent", "HttpsAgent"], "repository": {"type": "git", "url": "git://github.com/node-modules/agentkeepalive.git"}, "description": "Missing keepalive http.Agent", "maintainers": [{"name": "dead-horse", "email": "<EMAIL>"}, {"name": "dead_horse", "email": "<EMAIL>"}, {"name": "fengmk2", "email": "<EMAIL>"}, {"name": "popomore", "email": "<EMAIL>"}], "readme": "# agentkeepalive\n\n[![NPM version][npm-image]][npm-url]\n[![Known Vulnerabilities][snyk-image]][snyk-url]\n[![Node.js CI](https://github.com/node-modules/agentkeepalive/actions/workflows/nodejs.yml/badge.svg)](https://github.com/node-modules/agentkeepalive/actions/workflows/nodejs.yml)\n[![npm download][download-image]][download-url]\n\n[npm-image]: https://img.shields.io/npm/v/agentkeepalive.svg?style=flat\n[npm-url]: https://npmjs.org/package/agentkeepalive\n[snyk-image]: https://snyk.io/test/npm/agentkeepalive/badge.svg?style=flat-square\n[snyk-url]: https://snyk.io/test/npm/agentkeepalive\n[download-image]: https://img.shields.io/npm/dm/agentkeepalive.svg?style=flat-square\n[download-url]: https://npmjs.org/package/agentkeepalive\n\nThe enhancement features `keep alive` `http.Agent`. Support `http` and `https`.\n\n## What's different from original `http.Agent`?\n\n- `keepAlive=true` by default\n- Disable Nagle's algorithm: `socket.setNoDelay(true)`\n- Add free socket timeout: avoid long time inactivity socket leak in the free-sockets queue.\n- Add active socket timeout: avoid long time inactivity socket leak in the active-sockets queue.\n- TTL for active socket.\n\n## Node.js version required\n\nSupport Node.js >= `8.0.0`\n\n## Install\n\n```bash\n$ npm install agentkeepalive --save\n```\n\n## new Agent([options])\n\n* `options` {Object} Set of configurable options to set on the agent.\n  Can have the following fields:\n  * `keepAlive` {Boolean} Keep sockets around in a pool to be used by\n    other requests in the future. Default = `true`.\n  * `keepAliveMsecs` {Number} When using the keepAlive option, specifies the initial delay\n    for TCP Keep-Alive packets. Ignored when the keepAlive option is false or undefined. Defaults to 1000.\n    Default = `1000`.  Only relevant if `keepAlive` is set to `true`.\n  * `freeSocketTimeout`: {Number} Sets the free socket to timeout\n    after `freeSocketTimeout` milliseconds of inactivity on the free socket.\n    The default [server-side timeout](https://nodejs.org/api/http.html#serverkeepalivetimeout) is 5000 milliseconds, to [avoid ECONNRESET exceptions](https://medium.com/ssense-tech/reduce-networking-errors-in-nodejs-23b4eb9f2d83), we set the default value to `4000` milliseconds.\n    Only relevant if `keepAlive` is set to `true`.\n  * `timeout`: {Number} Sets the working socket to timeout\n    after `timeout` milliseconds of inactivity on the working socket.\n    Default is `freeSocketTimeout * 2` so long as that value is greater than or equal to 8 seconds, otherwise the default is 8 seconds.\n  * `maxSockets` {Number} Maximum number of sockets to allow per\n    host. Default = `Infinity`.\n  * `maxFreeSockets` {Number} Maximum number of sockets (per host) to leave open\n    in a free state. Only relevant if `keepAlive` is set to `true`.\n    Default = `256`.\n  * `socketActiveTTL` {Number} Sets the socket active time to live, even if it's in use.\n    If not set, the behaviour keeps the same (the socket will be released only when free)\n    Default = `null`.\n\n## Usage\n\n```js\nconst http = require('http');\nconst HttpAgent = require('agentkeepalive').HttpAgent;\n\nconst keepaliveAgent = new HttpAgent({\n  maxSockets: 100,\n  maxFreeSockets: 10,\n  timeout: 60000, // active socket keepalive for 60 seconds\n  freeSocketTimeout: 30000, // free socket keepalive for 30 seconds\n});\n\nconst options = {\n  host: 'cnodejs.org',\n  port: 80,\n  path: '/',\n  method: 'GET',\n  agent: keepaliveAgent,\n};\n\nconst req = http.request(options, res => {\n  console.log('STATUS: ' + res.statusCode);\n  console.log('HEADERS: ' + JSON.stringify(res.headers));\n  res.setEncoding('utf8');\n  res.on('data', function (chunk) {\n    console.log('BODY: ' + chunk);\n  });\n});\nreq.on('error', e => {\n  console.log('problem with request: ' + e.message);\n});\nreq.end();\n\nsetTimeout(() => {\n  if (keepaliveAgent.statusChanged) {\n    console.log('[%s] agent status changed: %j', Date(), keepaliveAgent.getCurrentStatus());\n  }\n}, 2000);\n\n```\n\n### `getter agent.statusChanged`\n\ncounters have change or not after last checkpoint.\n\n### `agent.getCurrentStatus()`\n\n`agent.getCurrentStatus()` will return a object to show the status of this agent:\n\n```js\n{\n  createSocketCount: 10,\n  closeSocketCount: 5,\n  timeoutSocketCount: 0,\n  requestCount: 5,\n  freeSockets: { 'localhost:57479:': 3 },\n  sockets: { 'localhost:57479:': 5 },\n  requests: {}\n}\n```\n\n### Support `https`\n\n```js\nconst https = require('https');\nconst HttpsAgent = require('agentkeepalive').HttpsAgent;\n\nconst keepaliveAgent = new HttpsAgent();\n// https://www.google.com/search?q=nodejs&sugexp=chrome,mod=12&sourceid=chrome&ie=UTF-8\nconst options = {\n  host: 'www.google.com',\n  port: 443,\n  path: '/search?q=nodejs&sugexp=chrome,mod=12&sourceid=chrome&ie=UTF-8',\n  method: 'GET',\n  agent: keepaliveAgent,\n};\n\nconst req = https.request(options, res => {\n  console.log('STATUS: ' + res.statusCode);\n  console.log('HEADERS: ' + JSON.stringify(res.headers));\n  res.setEncoding('utf8');\n  res.on('data', chunk => {\n    console.log('BODY: ' + chunk);\n  });\n});\n\nreq.on('error', e => {\n  console.log('problem with request: ' + e.message);\n});\nreq.end();\n\nsetTimeout(() => {\n  console.log('agent status: %j', keepaliveAgent.getCurrentStatus());\n}, 2000);\n```\n\n### Support `req.reusedSocket`\n\nThis agent implements the `req.reusedSocket` to determine whether a request is send through a reused socket.\n\nWhen server closes connection at unfortunate time ([keep-alive race](https://code-examples.net/en/q/28a8069)), the http client will throw a `ECONNRESET` error. Under this circumstance, `req.reusedSocket` is useful when we want to retry the request automatically.\n\n```js\nconst http = require('http');\nconst HttpAgent = require('agentkeepalive').HttpAgent;\nconst agent = new HttpAgent();\n\nconst req = http\n  .get('http://localhost:3000', { agent }, (res) => {\n    // ...\n  })\n  .on('error', (err) => {\n    if (req.reusedSocket && err.code === 'ECONNRESET') {\n      // retry the request or anything else...\n    }\n  })\n```\n\nThis behavior is consistent with Node.js core. But through `agentkeepalive`, you can use this feature in older Node.js version.\n\n## [Benchmark](https://github.com/node-modules/agentkeepalive/tree/master/benchmark)\n\nrun the benchmark:\n\n```bash\ncd benchmark\nsh start.sh\n```\n\nIntel(R) Core(TM)2 Duo CPU     P8600  @ 2.40GHz\n\nnode@v0.8.9\n\n50 maxSockets, 60 concurrent, 1000 requests per concurrent, 5ms delay\n\nKeep alive agent (30 seconds):\n\n```js\nTransactions:          60000 hits\nAvailability:         100.00 %\nElapsed time:          29.70 secs\nData transferred:        14.88 MB\nResponse time:            0.03 secs\nTransaction rate:      2020.20 trans/sec\nThroughput:           0.50 MB/sec\nConcurrency:           59.84\nSuccessful transactions:       60000\nFailed transactions:             0\nLongest transaction:          0.15\nShortest transaction:         0.01\n```\n\nNormal agent:\n\n```js\nTransactions:          60000 hits\nAvailability:         100.00 %\nElapsed time:          46.53 secs\nData transferred:        14.88 MB\nResponse time:            0.05 secs\nTransaction rate:      1289.49 trans/sec\nThroughput:           0.32 MB/sec\nConcurrency:           59.81\nSuccessful transactions:       60000\nFailed transactions:             0\nLongest transaction:          0.45\nShortest transaction:         0.00\n```\n\nSocket created:\n\n```bash\n[proxy.js:120000] keepalive, 50 created, 60000 requestFinished, 1200 req/socket, 0 requests, 0 sockets, 0 unusedSockets, 50 timeout\n{\" <10ms\":662,\" <15ms\":17825,\" <20ms\":20552,\" <30ms\":17646,\" <40ms\":2315,\" <50ms\":567,\" <100ms\":377,\" <150ms\":56,\" <200ms\":0,\" >=200ms+\":0}\n----------------------------------------------------------------\n[proxy.js:120000] normal   , 53866 created, 84260 requestFinished, 1.56 req/socket, 0 requests, 0 sockets\n{\" <10ms\":75,\" <15ms\":1112,\" <20ms\":10947,\" <30ms\":32130,\" <40ms\":8228,\" <50ms\":3002,\" <100ms\":4274,\" <150ms\":181,\" <200ms\":18,\" >=200ms+\":33}\n```\n\n## License\n\n[MIT](LICENSE)\n\n<!-- GITCONTRIBUTOR_START -->\n\n## Contributors\n\n|[<img src=\"https://avatars.githubusercontent.com/u/156269?v=4\" width=\"100px;\"/><br/><sub><b>fengmk2</b></sub>](https://github.com/fengmk2)<br/>|[<img src=\"https://avatars.githubusercontent.com/u/985607?v=4\" width=\"100px;\"/><br/><sub><b>dead-horse</b></sub>](https://github.com/dead-horse)<br/>|[<img src=\"https://avatars.githubusercontent.com/u/5557458?v=4\" width=\"100px;\"/><br/><sub><b>AndrewLeedham</b></sub>](https://github.com/AndrewLeedham)<br/>|[<img src=\"https://avatars.githubusercontent.com/u/5243774?v=4\" width=\"100px;\"/><br/><sub><b>ngot</b></sub>](https://github.com/ngot)<br/>|[<img src=\"https://avatars.githubusercontent.com/u/25919630?v=4\" width=\"100px;\"/><br/><sub><b>wrynearson</b></sub>](https://github.com/wrynearson)<br/>|[<img src=\"https://avatars.githubusercontent.com/u/26738844?v=4\" width=\"100px;\"/><br/><sub><b>aaronArinder</b></sub>](https://github.com/aaronArinder)<br/>|\n| :---: | :---: | :---: | :---: | :---: | :---: |\n|[<img src=\"https://avatars.githubusercontent.com/u/10976983?v=4\" width=\"100px;\"/><br/><sub><b>alexpenev-s</b></sub>](https://github.com/alexpenev-s)<br/>|[<img src=\"https://avatars.githubusercontent.com/u/959726?v=4\" width=\"100px;\"/><br/><sub><b>blemoine</b></sub>](https://github.com/blemoine)<br/>|[<img src=\"https://avatars.githubusercontent.com/u/398027?v=4\" width=\"100px;\"/><br/><sub><b>bdehamer</b></sub>](https://github.com/bdehamer)<br/>|[<img src=\"https://avatars.githubusercontent.com/u/4985201?v=4\" width=\"100px;\"/><br/><sub><b>DylanPiercey</b></sub>](https://github.com/DylanPiercey)<br/>|[<img src=\"https://avatars.githubusercontent.com/u/3770250?v=4\" width=\"100px;\"/><br/><sub><b>cixel</b></sub>](https://github.com/cixel)<br/>|[<img src=\"https://avatars.githubusercontent.com/u/2883231?v=4\" width=\"100px;\"/><br/><sub><b>HerringtonDarkholme</b></sub>](https://github.com/HerringtonDarkholme)<br/>|\n|[<img src=\"https://avatars.githubusercontent.com/u/1433247?v=4\" width=\"100px;\"/><br/><sub><b>denghongcai</b></sub>](https://github.com/denghongcai)<br/>|[<img src=\"https://avatars.githubusercontent.com/u/1847934?v=4\" width=\"100px;\"/><br/><sub><b>kibertoad</b></sub>](https://github.com/kibertoad)<br/>|[<img src=\"https://avatars.githubusercontent.com/u/5236150?v=4\" width=\"100px;\"/><br/><sub><b>pangorgo</b></sub>](https://github.com/pangorgo)<br/>|[<img src=\"https://avatars.githubusercontent.com/u/588898?v=4\" width=\"100px;\"/><br/><sub><b>mattiash</b></sub>](https://github.com/mattiash)<br/>|[<img src=\"https://avatars.githubusercontent.com/u/182440?v=4\" width=\"100px;\"/><br/><sub><b>nabeelbukhari</b></sub>](https://github.com/nabeelbukhari)<br/>|[<img src=\"https://avatars.githubusercontent.com/u/1411117?v=4\" width=\"100px;\"/><br/><sub><b>pmalouin</b></sub>](https://github.com/pmalouin)<br/>|\n[<img src=\"https://avatars.githubusercontent.com/u/1404810?v=4\" width=\"100px;\"/><br/><sub><b>SimenB</b></sub>](https://github.com/SimenB)<br/>|[<img src=\"https://avatars.githubusercontent.com/u/2630384?v=4\" width=\"100px;\"/><br/><sub><b>vinaybedre</b></sub>](https://github.com/vinaybedre)<br/>|[<img src=\"https://avatars.githubusercontent.com/u/10933333?v=4\" width=\"100px;\"/><br/><sub><b>starkwang</b></sub>](https://github.com/starkwang)<br/>|[<img src=\"https://avatars.githubusercontent.com/u/6897780?v=4\" width=\"100px;\"/><br/><sub><b>killagu</b></sub>](https://github.com/killagu)<br/>|[<img src=\"https://avatars.githubusercontent.com/u/15345331?v=4\" width=\"100px;\"/><br/><sub><b>tony-gutierrez</b></sub>](https://github.com/tony-gutierrez)<br/>|[<img src=\"https://avatars.githubusercontent.com/u/5856440?v=4\" width=\"100px;\"/><br/><sub><b>whxaxes</b></sub>](https://github.com/whxaxes)<br/>\n\nThis project follows the git-contributor [spec](https://github.com/xudafeng/git-contributor), auto updated at `Sat Aug 05 2023 02:36:31 GMT+0800`.\n\n<!-- GITCONTRIBUTOR_END -->\n", "readmeFilename": "README.md", "users": {"syzer": true, "timdp": true, "devinlin": true, "duartemendes": true, "arnaud.demouhy": true}}