{"_id": "deep-equal", "_rev": "77-256ad9a8767b353a4f902f518781e1ea", "name": "deep-equal", "description": "node's assert.deepEqual algorithm", "dist-tags": {"latest": "2.2.3", "v1-backport": "1.1.2"}, "versions": {"0.0.0": {"name": "deep-equal", "version": "0.0.0", "description": "node's assert.deepEqual algorithm", "main": "index.js", "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"test": "tap test/*.js"}, "devDependencies": {"tap": "0.0.x"}, "repository": {"type": "git", "url": "git://github.com/substack/node-deep-equal.git"}, "keywords": ["equality", "equal", "compare"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT/X11", "engine": {"node": ">=0.4"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "_id": "deep-equal@0.0.0", "dependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"shasum": "99679d3bbd047156fcd450d3d01eeb9068691e83", "tarball": "https://registry.npmjs.org/deep-equal/-/deep-equal-0.0.0.tgz", "integrity": "sha512-p1bI/kkDPT6auUI0U+WLuIIrzmDIDo80I406J8tT4y6I4ZGtBuMeTudrKDtBdMJFAcxqrQdx27gosqPVyY3IvQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGtsYzX7IJGOmUCj1ktokCJotn9aDwCcoF9N/MdZZdMkAiEA7NgCWAo6D2muBBxE4ok7dbmHttsY6zOZplSH5BsxjzE="}]}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}]}, "0.1.0": {"name": "deep-equal", "version": "0.1.0", "description": "node's assert.deepEqual algorithm", "main": "index.js", "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"test": "tap test/*.js"}, "devDependencies": {"tap": "~0.3.0", "tape": "~0.0.5"}, "repository": {"type": "git", "url": "http://github.com/substack/node-deep-equal.git"}, "keywords": ["equality", "equal", "compare"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": {"ie": [6, 7, 8, 9], "ff": [3.5, 10, 15], "chrome": [10, 22], "safari": [5.1], "opera": [12]}}, "bugs": {"url": "https://github.com/substack/node-deep-equal/issues"}, "_id": "deep-equal@0.1.0", "dist": {"shasum": "81fcefc84551d9d67cccdd80e1fced7f355e146f", "tarball": "https://registry.npmjs.org/deep-equal/-/deep-equal-0.1.0.tgz", "integrity": "sha512-44UCTCBjR4F3sWYpivyWb2wEqTLDIS6eUH+s2vhErSEbnqtavvkYFninPmvETeDyBo3Y79peYd5g1mDrkJGifg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDjNhyYtOIZy9nNK5Ut93EA1nv4JzAcejyfp8dgITJVXQIgUCK+UDU+s41hIZ3b9NgmezUR0QYYI67EB9eBlQ28tww="}]}, "_from": ".", "_npmVersion": "1.3.7", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}]}, "0.1.1": {"name": "deep-equal", "version": "0.1.1", "description": "node's assert.deepEqual algorithm", "main": "index.js", "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"test": "tap test/*.js"}, "devDependencies": {"tap": "~0.3.0", "tape": "~0.0.5"}, "repository": {"type": "git", "url": "http://github.com/substack/node-deep-equal.git"}, "keywords": ["equality", "equal", "compare"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": {"ie": [6, 7, 8, 9], "ff": [3.5, 10, 15], "chrome": [10, 22], "safari": [5.1], "opera": [12]}}, "bugs": {"url": "https://github.com/substack/node-deep-equal/issues"}, "homepage": "https://github.com/substack/node-deep-equal", "_id": "deep-equal@0.1.1", "dist": {"shasum": "8a55b7eddb6ea545a55231fe0a405ebf05077e62", "tarball": "https://registry.npmjs.org/deep-equal/-/deep-equal-0.1.1.tgz", "integrity": "sha512-WrYua5j/Ut/If9NkkRf2MLm7y5gevhxjNnbYsWmfmsGSTX/uWnUk02yV4hECuWiNNjPMz2Nv6wwOSOG4RwJoFg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCGeajwDkWK2VHQ5LadiPy/5OcJCed3MiCE4QaMsr90mQIgfy3/xWgBcprv92CzKQJzn/Ha0zUriJKQCk92+88sqbQ="}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}]}, "0.1.2": {"name": "deep-equal", "version": "0.1.2", "description": "node's assert.deepEqual algorithm", "main": "index.js", "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"test": "tape test/*.js"}, "devDependencies": {"tape": "~2.3.2"}, "repository": {"type": "git", "url": "http://github.com/substack/node-deep-equal.git"}, "keywords": ["equality", "equal", "compare"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": {"ie": [6, 7, 8, 9], "ff": [3.5, 10, 15], "chrome": [10, 22], "safari": [5.1], "opera": [12]}}, "bugs": {"url": "https://github.com/substack/node-deep-equal/issues"}, "homepage": "https://github.com/substack/node-deep-equal", "_id": "deep-equal@0.1.2", "dist": {"shasum": "b246c2b80a570a47c11be1d9bd1070ec878b87ce", "tarball": "https://registry.npmjs.org/deep-equal/-/deep-equal-0.1.2.tgz", "integrity": "sha512-rUCt39nKM7s6qUyYgp/reJmtXjgkOS/JbLO24DioMZaBNkD3b7C7cD3zJjSyjclEElNTpetAIRD6fMIbBIbX1Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCC++96Xy5GcT3zgzyuo5YAvrcn88LmdtYN8y+lEUAnQgIgRkOWOy68Sj4nG/sjuL3QW8WjvI4Qsm//UfD0YNHwzGI="}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}]}, "0.2.0": {"name": "deep-equal", "version": "0.2.0", "description": "node's assert.deepEqual algorithm", "main": "index.js", "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"test": "tape test/*.js"}, "devDependencies": {"tape": "~2.3.2"}, "repository": {"type": "git", "url": "http://github.com/substack/node-deep-equal.git"}, "keywords": ["equality", "equal", "compare"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": {"ie": [6, 7, 8, 9], "ff": [3.5, 10, 15], "chrome": [10, 22], "safari": [5.1], "opera": [12]}}, "bugs": {"url": "https://github.com/substack/node-deep-equal/issues"}, "homepage": "https://github.com/substack/node-deep-equal", "_id": "deep-equal@0.2.0", "dist": {"shasum": "81994cd7332efcf72a373e7f2ba490b2763159b5", "tarball": "https://registry.npmjs.org/deep-equal/-/deep-equal-0.2.0.tgz", "integrity": "sha512-iw7hU1n+M8FAjNB/q34dNMg9sGgUZJrCxOhhtyD7nJ9up7YS7Y7V3H48mF9SCvg8g28tN70HGiMHwtWH4XDRbg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCGbF1JYl+2n2bsBqckoHA6Pm7z2vT+gGfc8uhBI+MAgAIhAOjx4jPw7kdqpGfGBmc+BoeTPlW6aNt/AzRkmfN/nVyv"}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}]}, "0.2.1": {"name": "deep-equal", "version": "0.2.1", "description": "node's assert.deepEqual algorithm", "main": "index.js", "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"test": "tape test/*.js"}, "devDependencies": {"tape": "~2.3.2"}, "repository": {"type": "git", "url": "http://github.com/substack/node-deep-equal.git"}, "keywords": ["equality", "equal", "compare"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": {"ie": [6, 7, 8, 9], "ff": [3.5, 10, 15], "chrome": [10, 22], "safari": [5.1], "opera": [12]}}, "bugs": {"url": "https://github.com/substack/node-deep-equal/issues"}, "homepage": "https://github.com/substack/node-deep-equal", "_id": "deep-equal@0.2.1", "dist": {"shasum": "fad7a793224cbf0c3c7786f92ef780e4fc8cc878", "tarball": "https://registry.npmjs.org/deep-equal/-/deep-equal-0.2.1.tgz", "integrity": "sha512-m+FbkTWIPrb/qgd/v/A1ZcLDyS0sIvlCEOWPTaHaK3GM6SY71VZ/mP4YDKNquKBrkS7m3HRCO5+tLhsjeMVKxA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCbf9Ww//GhV0X9TTIgHwNfhlcsVf+nGZGSgPlB1itaTQIhAKK4YrzonKJJ/rrwiUIyXGconmOSlQ0vpN1ZiXmYYwie"}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}]}, "0.2.2": {"name": "deep-equal", "version": "0.2.2", "description": "node's assert.deepEqual algorithm", "main": "index.js", "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"test": "tape test/*.js"}, "devDependencies": {"tape": "^3.5.0"}, "repository": {"type": "git", "url": "http://github.com/substack/node-deep-equal.git"}, "keywords": ["equality", "equal", "compare"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": {"ie": [6, 7, 8, 9], "ff": [3.5, 10, 15], "chrome": [10, 22], "safari": [5.1], "opera": [12]}}, "gitHead": "05cd26a25f0d7babf0c2758827b4dafec9d0582e", "bugs": {"url": "https://github.com/substack/node-deep-equal/issues"}, "homepage": "https://github.com/substack/node-deep-equal", "_id": "deep-equal@0.2.2", "_shasum": "84b745896f34c684e98f2ce0e42abaf43bba017d", "_from": ".", "_npmVersion": "2.3.0", "_nodeVersion": "0.10.35", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "dist": {"shasum": "84b745896f34c684e98f2ce0e42abaf43bba017d", "tarball": "https://registry.npmjs.org/deep-equal/-/deep-equal-0.2.2.tgz", "integrity": "sha512-FXgye2Jr6oEk01S7gmSrHrPEQ1ontR7wwl+nYiZ8h4SXlHVm0DYda74BIPcHz2s2qPz4+375IcAz1vsWLwddgQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC+Fw66UkiF43PzGiQq+7h6bT4dr+Ea0Fig8+s57khO2AIhAPXmGG3BWCQH/kdtrM9ARs0nslUOFuoZx3iIytqS7sIZ"}]}}, "1.0.0": {"name": "deep-equal", "version": "1.0.0", "description": "node's assert.deepEqual algorithm", "main": "index.js", "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"test": "tape test/*.js"}, "devDependencies": {"tape": "^3.5.0"}, "repository": {"type": "git", "url": "http://github.com/substack/node-deep-equal.git"}, "keywords": ["equality", "equal", "compare"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": {"ie": [6, 7, 8, 9], "ff": [3.5, 10, 15], "chrome": [10, 22], "safari": [5.1], "opera": [12]}}, "gitHead": "39c740ebdafed9443912a4ef1493b18693934daf", "bugs": {"url": "https://github.com/substack/node-deep-equal/issues"}, "homepage": "https://github.com/substack/node-deep-equal", "_id": "deep-equal@1.0.0", "_shasum": "d4564f07d2f0ab3e46110bec16592abd7dc2e326", "_from": ".", "_npmVersion": "2.3.0", "_nodeVersion": "0.10.35", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "dist": {"shasum": "d4564f07d2f0ab3e46110bec16592abd7dc2e326", "tarball": "https://registry.npmjs.org/deep-equal/-/deep-equal-1.0.0.tgz", "integrity": "sha512-jAJ/YpGYX9qUM0ZMv7FWuz4JlECyma+QhYzFecyqGweO1MNyyzIi5eRPZTXgaCkra7jjxTt03AEeNp+7e1GJ3A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB2XXJAqdwmN0mTbQQ9cpffan+n7aerJhSjOLAH8AfREAiBSBnv3LvAYnyD9DB/w2kFFqpasij0kZLVUW1AM8KXxGQ=="}]}}, "1.0.1": {"name": "deep-equal", "version": "1.0.1", "description": "node's assert.deepEqual algorithm", "main": "index.js", "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"test": "tape test/*.js"}, "devDependencies": {"tape": "^3.5.0"}, "repository": {"type": "git", "url": "git+ssh://**************/substack/node-deep-equal.git"}, "keywords": ["equality", "equal", "compare"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": {"ie": [6, 7, 8, 9], "ff": [3.5, 10, 15], "chrome": [10, 22], "safari": [5.1], "opera": [12]}}, "gitHead": "59c511f5aeae19e3dd1de054077a789d7302be34", "bugs": {"url": "https://github.com/substack/node-deep-equal/issues"}, "homepage": "https://github.com/substack/node-deep-equal#readme", "_id": "deep-equal@1.0.1", "_shasum": "f5d260292b660e084eff4cdbc9f08ad3247448b5", "_from": ".", "_npmVersion": "3.2.2", "_nodeVersion": "2.4.0", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "dist": {"shasum": "f5d260292b660e084eff4cdbc9f08ad3247448b5", "tarball": "https://registry.npmjs.org/deep-equal/-/deep-equal-1.0.1.tgz", "integrity": "sha512-bHtC0iYvWhyaTzvV3CZgPeZQqCOBGyGsVV7v4eevpdkLHfiSrXUdBG+qAuSz4RI70sszvjQ1QSZ98An1yNwpSw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDJyx+iPkMWnYBOhUAQutcektHxV4ls9YVe5k58d3Q9LgIgPXeDf5xex5Q0TzjG3F8PsnlFXdrpLIb47or0qf8nAIg="}]}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}]}, "1.1.0": {"name": "deep-equal", "version": "1.1.0", "description": "node's assert.deepEqual algorithm", "main": "index.js", "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"pretest": "npm run lint", "lint": "eslint .", "tests-only": "tape test/*", "test": "npm run tests-only"}, "dependencies": {"is-arguments": "^1.0.4", "is-date-object": "^1.0.1", "is-regex": "^1.0.4", "object-is": "^1.0.1", "object-keys": "^1.1.1", "regexp.prototype.flags": "^1.2.0"}, "devDependencies": {"@ljharb/eslint-config": "^13.1.1", "eslint": "^5.16.0", "tape": "^4.11.0"}, "repository": {"type": "git", "url": "git+ssh://**************/substack/node-deep-equal.git"}, "keywords": ["equality", "equal", "compare"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": {"ie": [6, 7, 8, 9], "ff": [3.5, 10, 15], "chrome": [10, 22], "safari": [5.1], "opera": [12]}}, "gitHead": "6099799587240963f9ebef6f2a819fcf28add15b", "bugs": {"url": "https://github.com/substack/node-deep-equal/issues"}, "homepage": "https://github.com/substack/node-deep-equal#readme", "_id": "deep-equal@1.1.0", "_nodeVersion": "12.9.1", "_npmVersion": "6.10.2", "dist": {"integrity": "sha512-ZbfWJq/wN1Z273o7mUSjILYqehAktR2NVoSrOukDkU9kg2v/Uv89yU4Cvz8seJeAmtN5oqiefKq8FPuXOboqLw==", "shasum": "3103cdf8ab6d32cf4a8df7865458f2b8d33f3745", "tarball": "https://registry.npmjs.org/deep-equal/-/deep-equal-1.1.0.tgz", "fileCount": 12, "unpackedSize": 25392, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdZhQ9CRA9TVsSAnZWagAA8ukP/R/S/Xq8/fH3oZVEKfEn\nJKtrK/5ZrJw4jLl5ASI8s8trOgXArGRgyu8qdWH4lZk9/g2eiogrMZeujkzN\n6S6PVs9xNE9PYU7oOfOcY0aHF5cwDhOC453QSYTZGbj9VkCmS0Ez1WpIldwf\nDdPnxXFejBzesOOa/zYkRRRHtdBq/jauHz0K+KOXEgXODMJa2vRoLn9HJD1P\ngnwtsW6judAX8Q9qakDRX0Y28u+Ax8dMjnE9Dz5NeBcGR0nJ1ALgiUG9wyjN\nqaCl4cyoEwmVk+exCbIZHn6VgDWodo/JzQmTJbV5Wjn0ctzJa/NK7Tce9JHJ\nLh7x4TPZliRSDosbLMW994K/HgI0Xbz1fiilRGNdNkG5/ZV99tzTmfm8fhYP\nGZrr6jTo5Vzwu0YS55YHt95c5vxlKKuMFeyM0AvpKgggYQbGvXn+lCR+P31l\nZv6sg6UaTG9AeaeiG65JXPMwAWkeoLezgOc0YlqWF3tSWnR43r9qJILuyp+L\nhacNC7nQhapImJ35K2yrx0NndJ+V27DIY+3uXyKmpvBMnpVCEXBObGB+rklu\n9BNtq+uZF6MIYoLk/GrQ8wizQA2b0neerRktqkaWW5gWeSl3u4ey4GvfSi80\nJWyWHHn4nbkJkDcjNmVgytbvCQqGGlR4CQeBj5m0MUoSFLFABVRn/GZszVCa\nKkh8\r\n=Piz6\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDkxYNbGPnrg9rp2sGcpNouCmFHyA+MMlpHcF0loU8s5wIhANMtK43odHrKNE4STDUHix3NGlKvrEdfCqz3VgYwMDIi"}]}, "maintainers": [{"email": "<EMAIL>", "name": "lj<PERSON>b"}, {"email": "<EMAIL>", "name": "substack"}], "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deep-equal_1.1.0_1566970940528_0.38605092487709936"}, "_hasShrinkwrap": false}, "1.1.1": {"name": "deep-equal", "version": "1.1.1", "description": "node's assert.deepEqual algorithm", "main": "index.js", "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"prepublish": "safe-publish-latest", "pretest": "npm run lint", "lint": "eslint .", "tests-only": "tape test/*", "test": "npm run tests-only", "posttest": "npx aud", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "dependencies": {"is-arguments": "^1.0.4", "is-date-object": "^1.0.1", "is-regex": "^1.0.4", "object-is": "^1.0.1", "object-keys": "^1.1.1", "regexp.prototype.flags": "^1.2.0"}, "devDependencies": {"@ljharb/eslint-config": "^15.0.1", "auto-changelog": "^1.16.2", "eslint": "^6.6.0", "safe-publish-latest": "^1.1.4", "tape": "^4.11.0"}, "repository": {"type": "git", "url": "git+ssh://**************/substack/node-deep-equal.git"}, "keywords": ["equality", "equal", "compare"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": {"ie": [6, 7, 8, 9], "ff": [3.5, 10, 15], "chrome": [10, 22], "safari": [5.1], "opera": [12]}}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "gitHead": "7f21ce5ca6ac3d62f183071a12f58b9fd010bd34", "bugs": {"url": "https://github.com/substack/node-deep-equal/issues"}, "homepage": "https://github.com/substack/node-deep-equal#readme", "_id": "deep-equal@1.1.1", "_nodeVersion": "13.1.0", "_npmVersion": "6.12.1", "dist": {"integrity": "sha512-yd9c5AdiqVcR+JjcwUQb9DkhJc8ngNr0MahEBGvDiJw8puWab2yZlh+nkasOnZP+EGTAP6rRp2JzJhJZzvNF8g==", "shasum": "b5c98c942ceffaf7cb051e24e1434a25a2e6076a", "tarball": "https://registry.npmjs.org/deep-equal/-/deep-equal-1.1.1.tgz", "fileCount": 12, "unpackedSize": 26384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdyxQ4CRA9TVsSAnZWagAAv+AP+weArKrZAaxlVcIgMlsi\n7WKBt2xzQrp+vei93GwLnBlutPEN1kuJ4UWTsV1mfS/s8MAHRhi5XuXOXS/l\nMswzTaAd63FUrBEpVFmUv4d4gFlV4qAKozEt/fE6A//LDpr6/RoeUVsWdtlu\nOiEn7dTsothIb8yf/FxCwivk7Vu+QA/JxKLYuAiX44xbG6b20fBVi+8wXzbJ\nWF2PILj0wrOFf5wn4ybsmC7QtFjehGti0EruiPzA6luoe6UeqND/ZDfxxrzw\nBz4v1jXY63BeDWyQYTecfAszkXu+kznruS4C31OxG9I5g93h2m552zZBWHjn\n4+2UNoh9xMkGgVqAo94pOF2xa3TZSZlJAeMc+hLEt65y5aZZ8eil3ynBqyJb\nidzz30j++zDN5v54ZuoVTlAhh30i+Pib2sGFrpgiuysscA20Sc4ch+6leoiJ\n9gR/+6OvLoMKzf+2T8E97lFfIe8Iwyi7SHIB6u5SwrMd1CCiN1VmPzH0jq+F\n5YS5oZSU3gbHfYQ/sEseGWUQNqnK9c0JuXwVhmuIWBAxg8U87RrFOjG4M5Mc\nPOdyZzlSCOGCmgVQ+HBvxH7jZGdM/4g3I2Hyu7+ybJ4ATdCLxi5NHUq7KzxI\nEjx06NOARGAIH7MUtkPrqDb3+XyS0e0UXQbPGHhRk52EdtLOlSEJ26e+fqij\nedwu\r\n=avCO\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD4K9yhzYMUIC5YYq7fTGJoDoYk9vtv6V0AKldaO7qI/AIgRDY+tEFbeP1ge4UCz7MEnSMa1REZCB4adFLGtBWBIhc="}]}, "maintainers": [{"email": "<EMAIL>", "name": "lj<PERSON>b"}, {"email": "<EMAIL>", "name": "substack"}], "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deep-equal_1.1.1_1573590072165_0.7652971980130199"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "deep-equal", "version": "2.0.1", "description": "node's assert.deepEqual algorithm", "main": "index.js", "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package": "./package.json", "./package.json": "./package"}, "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"prepublish": "safe-publish-latest", "pretest": "npm run lint", "lint": "eslint .", "tests-only": "tape test/*", "test": "npm run tests-only", "posttest": "npx aud", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "dependencies": {"es-abstract": "^1.16.3", "es-get-iterator": "^1.0.1", "is-arguments": "^1.0.4", "is-date-object": "^1.0.1", "is-regex": "^1.0.4", "isarray": "^2.0.5", "object-is": "^1.0.1", "object-keys": "^1.1.1", "regexp.prototype.flags": "^1.2.0", "side-channel": "^1.0.1", "which-boxed-primitive": "^1.0.1", "which-collection": "^1.0.0"}, "devDependencies": {"@ljharb/eslint-config": "^15.0.2", "auto-changelog": "^1.16.2", "eslint": "^6.7.2", "has-symbols": "^1.0.1", "has-typed-arrays": "^1.0.0", "object.assign": "^4.1.0", "object.getownpropertydescriptors": "^2.0.3", "safe-publish-latest": "^1.1.4", "semver": "^6.3.0", "tape": "^4.11.0"}, "repository": {"type": "git", "url": "git+ssh://**************/inspect-js/node-deep-equal.git"}, "keywords": ["equality", "equal", "compare"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://substack.net"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": {"ie": [6, 7, 8, 9], "ff": [3.5, 10, 15], "chrome": [10, 22], "safari": [5.1], "opera": [12]}}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "browser": {"assert.js": false}, "readme": "# deep-equal\n\nNode's `assert.deepEqual() algorithm` as a standalone module.\n\nThis module is around [46 times faster](https://gist.github.com/substack/2790507#gistcomment-3099862) than wrapping `assert.deepEqual()` in a `try/catch`.\n\n[![build status](https://secure.travis-ci.com/inspect-js/node-deep-equal.png)](https://travis-ci.org/inspect-js/node-deep-equal)\n\n# example\n\n``` js\nvar equal = require('deep-equal');\nconsole.dir([\n    equal(\n        { a : [ 2, 3 ], b : [ 4 ] },\n        { a : [ 2, 3 ], b : [ 4 ] }\n    ),\n    equal(\n        { x : 5, y : [6] },\n        { x : 5, y : 6 }\n    )\n]);\n```\n\n# methods\n\n``` js\nvar deepEqual = require('deep-equal')\n```\n\n## deepEqual(a, b, opts)\n\nCompare objects `a` and `b`, returning whether they are equal according to a\nrecursive equality algorithm.\n\nIf `opts.strict` is `true`, use strict equality (`===`) to compare leaf nodes.\nThe default is to use coercive equality (`==`) because that's how\n`assert.deepEqual()` works by default.\n\n# install\n\nWith [npm](https://npmjs.org) do:\n\n```\nnpm install deep-equal\n```\n\n# test\n\nWith [npm](https://npmjs.org) do:\n\n```\nnpm test\n```\n", "readmeFilename": "readme.markdown", "gitHead": "8ba8dbceb1a836f26a61a54b597c8bb3eac8cb8d", "bugs": {"url": "https://github.com/inspect-js/node-deep-equal/issues"}, "homepage": "https://github.com/inspect-js/node-deep-equal#readme", "_id": "deep-equal@2.0.1", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.1", "dist": {"integrity": "sha512-7Et6r6XfNW61CPPCIYfm1YPGSmh6+CliYeL4km7GWJcpX5LTAflGF8drLLR+MZX+2P3NZfAfSduutBbSWqER4g==", "shasum": "fc12bbd6850e93212f21344748682ccc5a8813cf", "tarball": "https://registry.npmjs.org/deep-equal/-/deep-equal-2.0.1.tgz", "fileCount": 11, "unpackedSize": 53653, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd7eghCRA9TVsSAnZWagAAiwIP/3zHahQJBXVU9ZJVNIay\n/ARzUiVgqRlWJFhIJfob4VvXwjIFSJOjcWZBlNgNnEJoYupBfFJwJZyY97n9\nrqi7YtVrl/l1KW6d8LmI3Mc0dKv6fKHaYg90jNhyjKfAb3s70CJJ3s/Bx1fB\nNVxieoCyn9NWciX56OYlIvhNWfgDV/DQNf2HvJngo4ugCCELxcaYXmaJ4Ex3\nM80DJVOiS1/KO8LCUmhQTtzyOLlLtwyrGfyxWUxQRMZotMbws/ZEFe828NWr\nknyjMH/TK8QRtQ4YXJJRFCJjR9xOdTxgIJrtThtOG93d3h6Cmxrh9oKGwqvh\n6Nc2s0nFtJxUthgkelaOfM78T3x7lK8VdAWqdyWGmvLv90NMViUgBq1/ORKa\ns7fAXRAGxtj9T9BDGpPGfSg9EkkYssZRU5HHmkUPD/9he/HOnRcrPujMaoo4\nHwpK0x5Z1MQY/9zUGTCZuq/p9xb8mNriQw0GvtattVNuXkoSS99WMv0hEnPU\njDidZJzg3DfClYx1Rrv04s6nexhwxBbaeHDWuW4dM4fgc+fON8M+ZvvefqJW\n06/KQiw6YA6RiPPEd4u0lEJadRDQKwQH53Cz9ATybQFKH39vuYSlbxPKte6Z\nNPdsO0P5J5Xppfe5QwOpWmt+bvVBmuO9JYkA7jEyICY1EMJGSfsiqUkbwMUL\nIwC/\r\n=m/Yb\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCTGQJ4N9tI9Z2X3d+Kns8zq6S4sOomznOh8SJ6m84TLgIgLEpZRbtDpORph+9vIn0zTpKpep/JUX3UFu1/xVoI/3w="}]}, "maintainers": [{"email": "<EMAIL>", "name": "lj<PERSON>b"}, {"email": "<EMAIL>", "name": "substack"}], "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deep-equal_2.0.1_1575872545025_0.7778746444985405"}, "_hasShrinkwrap": false}, "2.0.2": {"name": "deep-equal", "version": "2.0.2", "description": "node's assert.deepEqual algorithm", "main": "index.js", "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package": "./package.json", "./package.json": "./package"}, "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"prepublish": "safe-publish-latest", "pretest": "npm run lint", "lint": "eslint .", "tests-only": "nyc tape test/*", "test": "npm run tests-only", "posttest": "npx aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "dependencies": {"es-abstract": "^1.17.5", "es-get-iterator": "^1.1.0", "is-arguments": "^1.0.4", "is-date-object": "^1.0.2", "is-regex": "^1.0.5", "isarray": "^2.0.5", "object-is": "^1.0.2", "object-keys": "^1.1.1", "regexp.prototype.flags": "^1.3.0", "side-channel": "^1.0.2", "which-boxed-primitive": "^1.0.1", "which-collection": "^1.0.1", "which-typed-array": "^1.1.1"}, "devDependencies": {"@ljharb/eslint-config": "^16.0.0", "auto-changelog": "^1.16.3", "eslint": "^6.8.0", "has-symbols": "^1.0.1", "has-typed-arrays": "^1.0.0", "nyc": "^10.3.2", "object.assign": "^4.1.0", "object.getownpropertydescriptors": "^2.1.0", "safe-publish-latest": "^1.1.4", "semver": "^6.3.0", "tape": "^5.0.0-next.5"}, "repository": {"type": "git", "url": "git+ssh://**************/inspect-js/node-deep-equal.git"}, "keywords": ["equality", "equal", "compare"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://substack.net"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": {"ie": [6, 7, 8, 9], "ff": [3.5, 10, 15], "chrome": [10, 22], "safari": [5.1], "opera": [12]}}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "greenkeeper": {"ignore": ["semver"]}, "browser": {"assert.js": false}, "gitHead": "7eedb2dd9d1d41e5f462460ae19e54c276ae4c4d", "bugs": {"url": "https://github.com/inspect-js/node-deep-equal/issues"}, "homepage": "https://github.com/inspect-js/node-deep-equal#readme", "_id": "deep-equal@2.0.2", "_nodeVersion": "13.12.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-kX0bjV7tdMuhrhzKPEnVwqfQCuf+IEfN+4Xqv4eKd75xGRyn8yzdQ9ujPY6a221rgJKyQC4KBu1PibDTpa6m9w==", "shasum": "e68291e245493ae908ca7190c1deea57a01ed82b", "tarball": "https://registry.npmjs.org/deep-equal/-/deep-equal-2.0.2.tgz", "fileCount": 14, "unpackedSize": 64958, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJehEA2CRA9TVsSAnZWagAAROkQAIHdo41ek37IWIXMcbQI\n6/lMcSK4m18G7hB1ryOhzu40XoXfL7FiYDCtUcvdkQleaTcfTqKQXkFAp+TZ\nushTrItJ/a7+ccorrTuOrc56soKbcxcEQNv+CWi2Ny7PfvHFtRVmRRP+1CKk\n8Ril0i6QiZ9aU4maYpGwVWJu/7+Bbp/pLcheeN47dOD3Mqj8DPw5VeWfReT5\nU8G3BY5G0rKTASyngi2arwFAto0fWG7XNwHwkVwvnDNI1CyuZuo1l2uRT67J\nhXVArKLH/gNSu8EPg02ssJaMtEJaIfTYMN62tCfUphoQC8PfcwkTrVbOUQQR\nWbh0jpqGi6P11so9Tg8hi3l25u+edYznHxMGqWExY1nNuZ0j00LLXouGIci0\nZ8ezEfMsNDXx2KW7efU88aFDFxXo4STvV3S5rJy3Bc/6GGio6m7Knnw3FxA4\nosLWoDM6/L81PwKBLFtAy4B683tc/u7mh8Ag4GaiGvZSdZPitxF1er6Z4Y/X\nEhuau0jbrfGzI/rZj2LvhZMMUeVhQvjFmCTR+QdYL8K/sXsAQaqhkCuNIjyK\njEJMYCEJ/7il0HdO+AFGgA7R1hRpDnWjZGpGnPRbMyQplyQKtG+1kYEZS0qy\n5Zk1Vq0raB82R5ySzpvjHsRm6puqviUdb7IgKjt2T4xcT9wIorL97hcLlp4c\nhHCE\r\n=rtlE\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAxmll4iEok2AJOD5reKEjXRRfR2Bckrs+A+xyFI1dE2AiEAxxZ0iPIXjlETPaz5F7YtzNPB/uQxZCnSxqyHLMESj8k="}]}, "maintainers": [{"email": "<EMAIL>", "name": "lj<PERSON>b"}, {"email": "<EMAIL>", "name": "substack"}], "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deep-equal_2.0.2_1585725494188_0.11829287453491855"}, "_hasShrinkwrap": false}, "2.0.3": {"name": "deep-equal", "version": "2.0.3", "description": "node's assert.deepEqual algorithm", "main": "index.js", "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package": "./package.json", "./package.json": "./package"}, "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"prepublish": "safe-publish-latest", "pretest": "npm run lint", "lint": "eslint .", "tests-only": "nyc tape test/*", "test": "npm run tests-only", "posttest": "npx aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "dependencies": {"es-abstract": "^1.17.5", "es-get-iterator": "^1.1.0", "is-arguments": "^1.0.4", "is-date-object": "^1.0.2", "is-regex": "^1.0.5", "isarray": "^2.0.5", "object.assign": "^4.1.0", "object-is": "^1.1.2", "object-keys": "^1.1.1", "regexp.prototype.flags": "^1.3.0", "side-channel": "^1.0.2", "which-boxed-primitive": "^1.0.1", "which-collection": "^1.0.1", "which-typed-array": "^1.1.2"}, "devDependencies": {"@ljharb/eslint-config": "^16.0.0", "aud": "^1.1.1", "auto-changelog": "^2.0.0", "eslint": "^6.8.0", "has-symbols": "^1.0.1", "has-typed-arrays": "^1.0.0", "nyc": "^10.3.2", "object.getownpropertydescriptors": "^2.1.0", "safe-publish-latest": "^1.1.4", "semver": "^6.3.0", "tape": "^5.0.0-next.5"}, "repository": {"type": "git", "url": "git+ssh://**************/inspect-js/node-deep-equal.git"}, "keywords": ["equality", "equal", "compare"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://substack.net"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": {"ie": [6, 7, 8, 9], "ff": [3.5, 10, 15], "chrome": [10, 22], "safari": [5.1], "opera": [12]}}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "greenkeeper": {"ignore": ["nyc", "semver"]}, "browser": {"assert.js": false}, "gitHead": "ab21672f28a4bf4edc73ad5399c912b8bd383824", "bugs": {"url": "https://github.com/inspect-js/node-deep-equal/issues"}, "homepage": "https://github.com/inspect-js/node-deep-equal#readme", "_id": "deep-equal@2.0.3", "_nodeVersion": "14.0.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-Spqdl4H+ky45I9ByyJtXteOm9CaIrPmnIPmOhrkKGNYWeDgCvJ8jNYVCTjChxW4FqGuZnLHADc8EKRMX6+CgvA==", "shasum": "cad1c15277ad78a5c01c49c2dee0f54de8a6a7b0", "tarball": "https://registry.npmjs.org/deep-equal/-/deep-equal-2.0.3.tgz", "fileCount": 16, "unpackedSize": 65915, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeooMbCRA9TVsSAnZWagAA0s0P/1Tgz89/yVZQNNIfemrq\n71X4LVBQnxoqQzGLOL++thOeG060B0c0oVCklMl1cg5W3zkE8nrmr+aZ/J/t\nhL9uCktnHeDAJGqhwQ9TtQbJ/FbN7Z+Iz9qEe6ZVwyIZi9l7lt6qxu5Ym8IY\nAzeg7Uf9Rg3BQzD5Y8kF6nHlicFxWAcRntNDVY5tNmYi9ebVLXY+o1VuQn6X\nG710yCoBsfD9LjTJUKQiphcsgmWgWZy9Nk+RnWOQld/dfq1qvfkuF5AQ0UJ8\nTiD9N0Zvv2GvROlRZUcbpEZaXfuI7zih8hn5bRleE/z2pIRTrWeUM0wjNNa7\nbZl8DIqU9mU2fHXbcdCZeaO3Tm4fV/WoDZ4LZJ2rb+PDBf/xbPuid60fMexv\n2+4n50ivDu03xxAyOSkuW4oyrKuUK/v8cwvHlqRLm4loWeQBlDYSuhGbV1GC\nzdno47krxdsJeMrmbPjaVwg+joBTwDyou/OzwHe2Ibp09HnHmBOJEZ7MQB8C\nK7zEdOQT8ssgnhMTe8ZK4J9Lha+WfFvgDl5E8L4fV8lEteUcrJzbRx0IqFGJ\nsIAfXyuPG39MTFZel2hcLEEZqeRDdFnNdZhq8p8ajOBZlWhqmyBPNxyhXBvK\nuKaMbRXzj0qadJkuNT4fuQmjCdJKsoKe6n61Mxd0TS9j8hP/no/mcT5eG1II\na+q9\r\n=M6gZ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGzV8/INNVVdlm0fE3luGvt4d+Zw5VWQBskh9rhdP1aZAiEAh2T86GCtR+Hvakk4fIukT/f5MykgWhvnbQghQpUT9yM="}]}, "maintainers": [{"email": "<EMAIL>", "name": "lj<PERSON>b"}, {"email": "<EMAIL>", "name": "substack"}], "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deep-equal_2.0.3_1587708698645_0.7178129698926239"}, "_hasShrinkwrap": false}, "2.0.4": {"name": "deep-equal", "version": "2.0.4", "description": "node's assert.deepEqual algorithm", "main": "index.js", "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package": "./package.json", "./package.json": "./package.json"}, "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"prepublish": "safe-publish-latest", "pretest": "npm run lint", "lint": "eslint .", "tests-only": "nyc tape test/*", "test": "npm run tests-only", "posttest": "npx aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "dependencies": {"es-abstract": "^1.18.0-next.1", "es-get-iterator": "^1.1.0", "is-arguments": "^1.0.4", "is-date-object": "^1.0.2", "is-regex": "^1.1.1", "isarray": "^2.0.5", "object-is": "^1.1.3", "object-keys": "^1.1.1", "object.assign": "^4.1.1", "regexp.prototype.flags": "^1.3.0", "side-channel": "^1.0.3", "which-boxed-primitive": "^1.0.1", "which-collection": "^1.0.1", "which-typed-array": "^1.1.2"}, "devDependencies": {"@ljharb/eslint-config": "^17.2.0", "aud": "^1.1.2", "auto-changelog": "^2.2.1", "eslint": "^7.10.0", "has-symbols": "^1.0.1", "has-typed-arrays": "^1.0.0", "nyc": "^10.3.2", "object.getownpropertydescriptors": "^2.1.0", "safe-publish-latest": "^1.1.4", "semver": "^6.3.0", "tape": "^5.0.1"}, "repository": {"type": "git", "url": "git+ssh://**************/inspect-js/node-deep-equal.git"}, "keywords": ["equality", "equal", "compare"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://substack.net"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": {"ie": [6, 7, 8, 9], "ff": [3.5, 10, 15], "chrome": [10, 22], "safari": [5.1], "opera": [12]}}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "greenkeeper": {"ignore": ["nyc", "semver"]}, "browser": {"assert.js": false}, "gitHead": "db63ef87deaab3bb8ca464b69522d2eb980660e2", "bugs": {"url": "https://github.com/inspect-js/node-deep-equal/issues"}, "homepage": "https://github.com/inspect-js/node-deep-equal#readme", "_id": "deep-equal@2.0.4", "_nodeVersion": "14.12.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-BUfaXrVoCfgkOQY/b09QdO9L3XNoF2XH0A3aY9IQwQL/ZjLOe8FQgCNVl1wiolhsFo8kFdO9zdPViCPbmaJA5w==", "shasum": "6b0b407a074666033169df3acaf128e1c6f3eab6", "tarball": "https://registry.npmjs.org/deep-equal/-/deep-equal-2.0.4.tgz", "fileCount": 14, "unpackedSize": 67599, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfdqsNCRA9TVsSAnZWagAA+zIP/0iFSlvF2VapBrzoBzXb\nrBjENpN/rUaqyR4QEzOzMH0fjxoRP7/nRTjISZe7wNMb9FQuHCTV+cJVNhrv\nhQzRIT9WdcUEAF70mZdcNTNU+nHGRQ7gSronKfmklk2KZCiMUOxsX+VnRnhf\nnLa19wZ4xGvkD25KIATuMKYJ3bWYZE0+03ofVKcXxpgYTUd/cCBN8ebebFvT\nb9TBW0tLiP8NJpRO7yKp0HblKjjo8LMwCtJWa42VCuahCjNQ5JY6mFtlOf2X\nG7+VjTGoQN1ch89jN+4pVd6iTPbf9YnXYgjcpoXxZALUXExosmY/4RwqVzws\n79mggSFKtyjfW6ycXeCplBWFOYU/QrhmIDjMQu0iuVPevwyih6e7L1Tby5Pz\nBCmuoaKnoed7aaKaXn9m+tqPQa4yId6c0s6Yp6YcDO3oZ5zNQEmtbp3rD7j6\n7XE0azs9QymEoWIipGoCoXwVKs4CleIgvByu7HHKtXrMVFa536sN0P2m/UhY\nqsXr3b8PdAnGzsK3FvP/aHHXG5VEevzgM3mOC4DXdO/493OmT79juJZbc+Ir\nmLTIrKaCn1Z0L44zLsjkiLDFU0tG4gZW2X/H+MOWoNh0kavH+Fp7PUD0LI1G\n8XswYdrxIcfFakSqjDaMH7I6hwUTA+1PShqK/OKTkiw8SFOkDGCaRJ8Ck9q8\nDbKf\r\n=F1Lw\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC+P+FyNY7HlbX5rJqgWYEUrYhXrdSDvNuaWPJ5iTjBiAIhAIsNW6rVnZvT1S5B1a3dA6GBy+5yIG/zfbwpkeFGhZiW"}]}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deep-equal_2.0.4_1601612556755_0.6157641233458735"}, "_hasShrinkwrap": false}, "2.0.5": {"name": "deep-equal", "version": "2.0.5", "description": "node's assert.deepEqual algorithm", "main": "index.js", "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package": "./package.json", "./package.json": "./package.json"}, "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"prepublish": "safe-publish-latest", "pretest": "npm run lint", "lint": "eslint .", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "npx aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "dependencies": {"call-bind": "^1.0.0", "es-get-iterator": "^1.1.1", "get-intrinsic": "^1.0.1", "is-arguments": "^1.0.4", "is-date-object": "^1.0.2", "is-regex": "^1.1.1", "isarray": "^2.0.5", "object-is": "^1.1.4", "object-keys": "^1.1.1", "object.assign": "^4.1.2", "regexp.prototype.flags": "^1.3.0", "side-channel": "^1.0.3", "which-boxed-primitive": "^1.0.1", "which-collection": "^1.0.1", "which-typed-array": "^1.1.2"}, "devDependencies": {"@ljharb/eslint-config": "^17.3.0", "aud": "^1.1.3", "auto-changelog": "^2.2.1", "eslint": "^7.14.0", "has-symbols": "^1.0.1", "has-typed-arrays": "^1.0.0", "nyc": "^10.3.2", "object.getownpropertydescriptors": "^2.1.1", "safe-publish-latest": "^1.1.4", "semver": "^6.3.0", "tape": "^5.0.1"}, "repository": {"type": "git", "url": "git+ssh://**************/inspect-js/node-deep-equal.git"}, "keywords": ["equality", "equal", "compare"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://substack.net"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": {"ie": [6, 7, 8, 9], "ff": [3.5, 10, 15], "chrome": [10, 22], "safari": [5.1], "opera": [12]}}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "greenkeeper": {"ignore": ["nyc", "semver"]}, "browser": {"assert.js": false}, "gitHead": "7d2a306d57fc92f1b138fed177a8f611503e4df2", "bugs": {"url": "https://github.com/inspect-js/node-deep-equal/issues"}, "homepage": "https://github.com/inspect-js/node-deep-equal#readme", "_id": "deep-equal@2.0.5", "_nodeVersion": "14.15.1", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-nPiRgmbAtm1a3JsnLCf6/SLfXcjyN5v8L1TXzdCmHrXJ4hx+gW/w1YCcn7z8gJtSiDArZCgYtbao3QqLm/N1Sw==", "shasum": "55cd2fe326d83f9cbf7261ef0e060b3f724c5cb9", "tarball": "https://registry.npmjs.org/deep-equal/-/deep-equal-2.0.5.tgz", "fileCount": 20, "unpackedSize": 74471, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfxruxCRA9TVsSAnZWagAADX0P/Ahk9lx3SS0wfcmnC5df\nmfR2hhHK9fnxnlZ7BGAko5xbP77gqiAOZjR7Dx5wIY2yXF1ROMi7OzCYi+ky\nOEjhp3szx6iwo8AMLsP2JtHbrcVIF/nHYRKsStohtQXm+aXLcJxLNrdLp/xh\nCFlVgHhNTTOWqjmBhv8jyHVXzFmKpFQU5sxltEojyJYK2cW7p7VGNK+BuNR+\nwjjt29tDEAgPJdcT7zNjGh0AlFaSMLfQqBgvjLZ2LyeHq/d3PPtwXnhIfOl9\nw1Knwv1ef1bBRyVZ+GWFvB1XS8c/H1TFK2eTASIMvsSSUWD3I9EqGpw2Lp1a\ndGgSdl+NJB4BRcZvktlcxgi767oxe0+sf2XKwkBKRDdGhGkCP7pWHIVRgQdl\n5mLBBr+VqWZolpX/ha5tPuKjzrEcbKmwVKp2QwcwHrQdFr/xGm/42KUeFKtI\nCaVjm0YUUL6TSS5z9q8sg0zi53V9fNR2XwU4JWOc/8bGCM8RWKUu5jitJp4X\n77dy67ftX70XgnuiHw05KeMARo5cKuwX1ubr84RslwG3ZmueC6nGmw5gwUzy\nO683uVUth4zXnIcNSa6PbT160juwBYMIZQ601kDMqMV3cTvza8goDeH2Nqmg\nBadvgZrPQIhcFVYrhMuLVpg22gqif/iUdwJYA6WukThUYdejxE0nx/YmNrlh\norsO\r\n=L5Lf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBRKFIkZjJq3rAQUy2RaU1RAzb1IwhNfw4Bb7n+Hx+QHAiB56XnDsj/qU4Ru2rg8v8+qrwRaYnZGogr0WT3ujsYBDQ=="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deep-equal_2.0.5_1606859696458_0.7208159424571532"}, "_hasShrinkwrap": false}, "2.1.0": {"name": "deep-equal", "version": "2.1.0", "description": "node's assert.deepEqual algorithm", "main": "index.js", "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package": "./package.json", "./package.json": "./package.json"}, "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "lint": "eslint --ext=js,mjs .", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "dependencies": {"call-bind": "^1.0.2", "es-get-iterator": "^1.1.2", "get-intrinsic": "^1.1.3", "is-arguments": "^1.1.1", "is-date-object": "^1.0.5", "is-regex": "^1.1.4", "isarray": "^2.0.5", "object-is": "^1.1.5", "object-keys": "^1.1.1", "object.assign": "^4.1.4", "regexp.prototype.flags": "^1.4.3", "side-channel": "^1.0.4", "which-boxed-primitive": "^1.0.2", "which-collection": "^1.0.1", "which-typed-array": "^1.1.8"}, "devDependencies": {"@ljharb/eslint-config": "^21.0.0", "aud": "^2.0.1", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "has-symbols": "^1.0.3", "has-typed-arrays": "^1.0.1", "in-publish": "^2.0.1", "npmignore": "^0.3.0", "nyc": "^10.3.2", "object.getownpropertydescriptors": "^2.1.4", "safe-publish-latest": "^2.0.0", "semver": "^6.3.0", "tape": "^5.6.1"}, "repository": {"type": "git", "url": "git+ssh://**************/inspect-js/node-deep-equal.git"}, "keywords": ["equality", "equal", "compare"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://substack.net"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": {"ie": [6, 7, 8, 9], "ff": [3.5, 10, 15], "chrome": [10, 22], "safari": [5.1], "opera": [12]}}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "greenkeeper": {"ignore": ["nyc", "semver"]}, "browser": {"assert.js": false}, "publishConfig": {"ignore": [".github/workflows"]}, "gitHead": "3588f3540c7d0084f8382c723985eec8641c20da", "bugs": {"url": "https://github.com/inspect-js/node-deep-equal/issues"}, "homepage": "https://github.com/inspect-js/node-deep-equal#readme", "_id": "deep-equal@2.1.0", "_nodeVersion": "19.0.0", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-2pxgvWu3Alv1PoWEyVg7HS8YhGlUFUV7N5oOvfL6d+7xAmLSemMwv/c8Zv/i9KFzxV5Kt5CAvQc70fLwVuf4UA==", "shasum": "5ba60402cf44ab92c2c07f3f3312c3d857a0e1dd", "tarball": "https://registry.npmjs.org/deep-equal/-/deep-equal-2.1.0.tgz", "fileCount": 12, "unpackedSize": 76632, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDmV5U1XE17y9kh43fla/o2h/76+lwOri1mZ1ix143yzgIgSfMZVhyqf+4OKBTY0g6WXihwdLC90hh13hCS03Rp5Pc="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjYrDoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrgJA/9GzNfOT7Zft7StFgUqc32YUVyiyuvaQnTuxJA0QkOw1xzEYsl\r\nXs8yMkBj5dH/3UvJx1ETKZhmuDajBrpcT1QmTKSuibx+0vwZb02BhT6VKx38\r\nKfijM/vG8g0Uc36Y1oYPYxy93z92Z+/o3W9gKxMepaJz5Tk5tq7Fi6Nl12fX\r\nDVA3xHq8o8cGuqZCzDsQnX0gjoKr38a9JBmOjOCbtVRk/YAJDBgjvQg0JOD/\r\nbcby2yvJ2U+HUBpGE8+NSegUmzc5C09MJmv6bDVnjTQP1TJWGDtApFupQXA0\r\nNGVK0iMCorURghEArjlGCYjjW5WNjK9mUoi7JjMJxR/m51aCp8SQ7qr6YeTr\r\nWztMIlx1AhCh796O3feBCnYd540R+4rgwjiP7UOoIMA71tfYZ8TMsM5UlEBB\r\nzcjQGOr6q2blHvz/ULgH1EjYks74DdwQIftTwJe9ogFc18p49W08u+2jCI5/\r\nZJIMkmuOrC/Ul84z3TOtyjEtyCRcDTLMTBA0Io+uwU0uteJYnyym71JfNHkQ\r\nfV/5emYvDM+DftZ0BBkEQ2l/Iuntnr7GxNIcLxuzmrt4nCOV2g6bltEBzffv\r\n77yksLYbWME49dPs7vaS21brhVHABdDkN4p9O45+VIe4CMsktqv3Em4TI8W2\r\nwnX2VGuZ5fKQAh4bnZsaoxg5OcammF9OLUM=\r\n=qCXk\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deep-equal_2.1.0_1667412200014_0.21451041949420024"}, "_hasShrinkwrap": false}, "2.2.0": {"name": "deep-equal", "version": "2.2.0", "description": "node's assert.deepEqual algorithm", "main": "index.js", "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package": "./package.json", "./package.json": "./package.json"}, "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "lint": "eslint --ext=js,mjs .", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "dependencies": {"call-bind": "^1.0.2", "es-get-iterator": "^1.1.2", "get-intrinsic": "^1.1.3", "is-arguments": "^1.1.1", "is-array-buffer": "^3.0.1", "is-date-object": "^1.0.5", "is-regex": "^1.1.4", "is-shared-array-buffer": "^1.0.2", "isarray": "^2.0.5", "object-is": "^1.1.5", "object-keys": "^1.1.1", "object.assign": "^4.1.4", "regexp.prototype.flags": "^1.4.3", "side-channel": "^1.0.4", "which-boxed-primitive": "^1.0.2", "which-collection": "^1.0.1", "which-typed-array": "^1.1.9"}, "devDependencies": {"@ljharb/eslint-config": "^21.0.1", "aud": "^2.0.2", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "has-symbols": "^1.0.3", "has-typed-arrays": "^1.0.1", "in-publish": "^2.0.1", "npmignore": "^0.3.0", "nyc": "^10.3.2", "object.getownpropertydescriptors": "^2.1.5", "safe-publish-latest": "^2.0.0", "semver": "^6.3.0", "tape": "^5.6.1"}, "repository": {"type": "git", "url": "git+ssh://**************/inspect-js/node-deep-equal.git"}, "keywords": ["equality", "equal", "compare"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://substack.net"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": {"ie": [6, 7, 8, 9], "ff": [3.5, 10, 15], "chrome": [10, 22], "safari": [5.1], "opera": [12]}}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "greenkeeper": {"ignore": ["nyc", "semver"]}, "browser": {"assert.js": false}, "publishConfig": {"ignore": [".github/workflows"]}, "gitHead": "b9d9e54c27e5ff5827650ff57b5c8d8ffd451b27", "bugs": {"url": "https://github.com/inspect-js/node-deep-equal/issues"}, "homepage": "https://github.com/inspect-js/node-deep-equal#readme", "_id": "deep-equal@2.2.0", "_nodeVersion": "19.3.0", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-RdpzE0Hv4lhowpIUKKMJfeH6C1pXdtT1/it80ubgWqwI3qpuxUBpC1S4hnHg+zjnuOoDkzUtUCEEkG+XG5l3Mw==", "shasum": "5caeace9c781028b9ff459f33b779346637c43e6", "tarball": "https://registry.npmjs.org/deep-equal/-/deep-equal-2.2.0.tgz", "fileCount": 12, "unpackedSize": 79889, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICFpVPX/At0kjEhgCXoK6Qa9yWNE9q776WnDQqQRUckHAiEAhkeEXhsOsVc/GGwhv0OjfY0nykQJHbyYrfBX7WeI8wY="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjt1cbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpDXw//Ufunc3Pkc0yB6VdCFPGh05FsxXhlvFwYqOIpLvBfyd/9+3lA\r\n1X2mFsh03InFA6czJa0E0mOSaCFXwK21palqM0VJP5kil384fpyUWSpSeYWC\r\nvxdZIDE6Y6F5cPIywYsjbzTQmlDDxLiWibvPyC5gLh6tgbUw+Jn6KI3beFCV\r\nqq/hZ8xp2lwP95H/J777WTnOgb/SUO6zPKfzEIqF5vHJI54SyIrwNQhsJ6Sj\r\n1SrIL8Crmg/CocLJV4AfLFqtNvzBeUsq4/Z2pX7zmmiCnhAI0jkyvzr19RL7\r\nNo+uzZwPk6FewCBOatOAQyhuZScyu4luFNSt9poId9yAtj9lYZbBrSkp2P4a\r\n099QipgBsQAdkpguY1LK8nFoXbjvCjPZJ6rp0VQtgzRbVFZl/rpLGIqnHvgt\r\n8ksMVD5nwySRFCllXKc4m2+rYF9FuI1C0JvyIBnh1AX5fB3Iwn20vA3RjcOD\r\nX8NwZazl273lgwJ+C64s/sMLgNV4HK24hCIXZ9AHVXFnvDFWP+W2K+8dyb6W\r\nY9PAdWkU64ATvM2Ig38eSm9Gdp+OO2Un2IlNGDShWyDOP7NRO1skrhlNLw29\r\nFWP3x7HX2x9rLMysYURbzzUZoRS362OPYLAH80KiKpHulKCj7xZMflM5zhcq\r\nYy0pNli/zTONOBy/FnA5remPJ4U6jciHn1o=\r\n=IpZR\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deep-equal_2.2.0_1672959771652_0.025338480338187974"}, "_hasShrinkwrap": false}, "2.2.1": {"name": "deep-equal", "version": "2.2.1", "description": "node's assert.deepEqual algorithm", "main": "index.js", "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package": "./package.json", "./package.json": "./package.json"}, "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "lint": "eslint --ext=js,mjs .", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "dependencies": {"array-buffer-byte-length": "^1.0.0", "call-bind": "^1.0.2", "es-get-iterator": "^1.1.3", "get-intrinsic": "^1.2.0", "is-arguments": "^1.1.1", "is-array-buffer": "^3.0.2", "is-date-object": "^1.0.5", "is-regex": "^1.1.4", "is-shared-array-buffer": "^1.0.2", "isarray": "^2.0.5", "object-is": "^1.1.5", "object-keys": "^1.1.1", "object.assign": "^4.1.4", "regexp.prototype.flags": "^1.5.0", "side-channel": "^1.0.4", "which-boxed-primitive": "^1.0.2", "which-collection": "^1.0.1", "which-typed-array": "^1.1.9"}, "devDependencies": {"@ljharb/eslint-config": "^21.0.1", "aud": "^2.0.2", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "has-symbols": "^1.0.3", "has-typed-arrays": "^1.0.1", "in-publish": "^2.0.1", "npmignore": "^0.3.0", "nyc": "^10.3.2", "object.getownpropertydescriptors": "^2.1.6", "safe-publish-latest": "^2.0.0", "semver": "^6.3.0", "tape": "^5.6.3"}, "repository": {"type": "git", "url": "git+ssh://**************/inspect-js/node-deep-equal.git"}, "keywords": ["equality", "equal", "compare"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://substack.net"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": {"ie": [6, 7, 8, 9], "ff": [3.5, 10, 15], "chrome": [10, 22], "safari": [5.1], "opera": [12]}}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "greenkeeper": {"ignore": ["nyc", "semver"]}, "browser": {"assert.js": false}, "publishConfig": {"ignore": [".github/workflows"]}, "gitHead": "6ea6b8d2967201a3c3f2976b972da1405fffcf81", "bugs": {"url": "https://github.com/inspect-js/node-deep-equal/issues"}, "homepage": "https://github.com/inspect-js/node-deep-equal#readme", "_id": "deep-equal@2.2.1", "_nodeVersion": "20.0.0", "_npmVersion": "9.6.4", "dist": {"integrity": "sha512-lKdkdV6EOGoVn65XaOsPdH4rMxTZOnmFyuIkMjM1i5HHCbfjC97dawgTAy0deYNfuqUqW+Q5VrVaQYtUpSd6yQ==", "shasum": "c72ab22f3a7d3503a4ca87dde976fe9978816739", "tarball": "https://registry.npmjs.org/deep-equal/-/deep-equal-2.2.1.tgz", "fileCount": 12, "unpackedSize": 81213, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFKcood95cU+beboSKx6NsNl63rXkaV7INFuHAKgS297AiEAkP60Njk54qlNiby+uy0cONVuUnyONj2fPxCWZwV0Bvk="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkSh6mACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrgXQ/+IFr05Jb3JZXb3smHkDoQycHaw506LCzKkzTuQ0sj3b5CHas1\r\nOel6eypcX3Z+G/FrcMer1RG3qcf9J8ZD5Jyn151Av5WzvTa15L6oCQLOAghP\r\nUSYzj0FkAeo6FQ5gJKoRSHk7YHn90xd6oW8mAv5gZg6ne4GCI7aT51bH0CCA\r\n5F4ldjgJZk9NJwC+84CfZHBgP7rGZtI+VSj3hMhegKJ6BqzTgn/Jv4Yg+gAA\r\nDzbh5e4J2zBlbzdUN42Q8WENR3sB2vhvNkm0khXzaubQrywqxGUMExkP9ib8\r\nbYWbfrmeNEEZ5oEMrvwX0YqrZWUYAfSwP/xLIy55Wy9JjpEpKH0FVJdYaqd7\r\nV28aBzOe6oEw1ifDAEtPajzQamXNr/ZnHMwJ3vR/B+y5i9+lSaHyM7RTS07M\r\ns6TqQ5yTRPfXsQTkO4sW8pLQb71GB9gJR94NUMuvFJPIy1Rw6TNwzt9/NU9t\r\nKoO1ILOm8/fNkGytG3o3/NTTOdH1RloNkjiVZTgY9aGkvUdchpATO/09wTAP\r\nyhWAxIPdf11KTslRgfCl/XfHjURDCfkxsATLiPVJA3XT/hf+UlhUjL05FNJF\r\nqsSrdYyZGat/kG7K4C7RFHv21UuSzf6Dt+KyOizDxUtw1K6Wh3233sRqHrwx\r\nFFxcu6Rdrf1o1wnVXr7YFieC3E5wyQkreQM=\r\n=QrEi\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deep-equal_2.2.1_1682579109917_0.9323798979101192"}, "_hasShrinkwrap": false}, "2.2.2": {"name": "deep-equal", "version": "2.2.2", "description": "node's assert.deepEqual algorithm", "main": "index.js", "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package": "./package.json", "./package.json": "./package.json"}, "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "lint": "eslint --ext=js,mjs .", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "dependencies": {"array-buffer-byte-length": "^1.0.0", "call-bind": "^1.0.2", "es-get-iterator": "^1.1.3", "get-intrinsic": "^1.2.1", "is-arguments": "^1.1.1", "is-array-buffer": "^3.0.2", "is-date-object": "^1.0.5", "is-regex": "^1.1.4", "is-shared-array-buffer": "^1.0.2", "isarray": "^2.0.5", "object-is": "^1.1.5", "object-keys": "^1.1.1", "object.assign": "^4.1.4", "regexp.prototype.flags": "^1.5.0", "side-channel": "^1.0.4", "which-boxed-primitive": "^1.0.2", "which-collection": "^1.0.1", "which-typed-array": "^1.1.9"}, "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "aud": "^2.0.3", "auto-changelog": "^2.4.0", "available-typed-arrays": "^1.0.5", "eslint": "=8.8.0", "for-each": "^0.3.3", "has-symbols": "^1.0.3", "has-typed-arrays": "^1.0.1", "in-publish": "^2.0.1", "npmignore": "^0.3.0", "nyc": "^10.3.2", "object.getownpropertydescriptors": "^2.1.6", "safe-publish-latest": "^2.0.0", "semver": "^6.3.0", "tape": "^5.6.4"}, "repository": {"type": "git", "url": "git+ssh://**************/inspect-js/node-deep-equal.git"}, "keywords": ["equality", "equal", "compare"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://substack.net"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": {"ie": [6, 7, 8, 9], "ff": [3.5, 10, 15], "chrome": [10, 22], "safari": [5.1], "opera": [12]}}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "greenkeeper": {"ignore": ["nyc", "semver"]}, "browser": {"assert.js": false}, "publishConfig": {"ignore": [".github/workflows"]}, "gitHead": "3d1ee6069d6a82d0e1dfc4b5654d4504261ea866", "bugs": {"url": "https://github.com/inspect-js/node-deep-equal/issues"}, "homepage": "https://github.com/inspect-js/node-deep-equal#readme", "_id": "deep-equal@2.2.2", "_nodeVersion": "20.4.0", "_npmVersion": "9.7.2", "dist": {"integrity": "sha512-xjVyBf0w5vH0I42jdAZzOKVldmPgSulmiyPRywoyq7HXC9qdgo17kxJE+rdnif5Tz6+pIrpJI8dCpMNLIGkUiA==", "shasum": "9b2635da569a13ba8e1cc159c2f744071b115daa", "tarball": "https://registry.npmjs.org/deep-equal/-/deep-equal-2.2.2.tgz", "fileCount": 12, "unpackedSize": 82799, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAM8wZyDNxGX+2AbJcJGhaDzT7oqIz//gcvWzg2wk3NHAiEAySnQUyjPj61psIZBx2GV3lJhcZkYmq5U8Jk9VDH4bcQ="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deep-equal_2.2.2_1689014965016_0.06904569905678026"}, "_hasShrinkwrap": false}, "1.1.2": {"name": "deep-equal", "version": "1.1.2", "description": "node's assert.deepEqual algorithm", "main": "index.js", "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "lint": "eslint --ext=js,mjs .", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "dependencies": {"is-arguments": "^1.1.1", "is-date-object": "^1.0.5", "is-regex": "^1.1.4", "object-is": "^1.1.5", "object-keys": "^1.1.1", "regexp.prototype.flags": "^1.5.1"}, "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "aud": "^2.0.3", "auto-changelog": "^2.4.0", "define-data-property": "^1.1.1", "eslint": "=8.8.0", "has-typed-arrays": "^1.0.1", "in-publish": "^2.0.1", "npmignore": "^0.3.0", "nyc": "^10.3.2", "object.getownpropertydescriptors": "^2.1.7", "safe-publish-latest": "^2.0.0", "semver": "^6.3.1", "tape": "^5.7.2"}, "repository": {"type": "git", "url": "git+ssh://**************/inspect-js/node-deep-equal.git"}, "keywords": ["equality", "equal", "compare"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "engines": {"node": ">= 0.4"}, "testling": {"files": "test/*.js", "browsers": {"ie": [6, 7, 8, 9], "ff": [3.5, 10, 15], "chrome": [10, 22], "safari": [5.1], "opera": [12]}}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "browser": {"assert.js": false}, "publishConfig": {"ignore": [".github/workflows"]}, "_id": "deep-equal@1.1.2", "readme": "# deep-equal <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n<PERSON><PERSON>'s `assert.deepEqual() algorithm` as a standalone module, that also works in browser environments.\n\nIt mirrors the robustness of node's own `assert.deepEqual` and is robust against later builtin modification.\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\n# example\n\n``` js\nvar equal = require('deep-equal');\nconsole.dir([\n    equal(\n        { a : [ 2, 3 ], b : [ 4 ] },\n        { a : [ 2, 3 ], b : [ 4 ] }\n    ),\n    equal(\n        { x : 5, y : [6] },\n        { x : 5, y : 6 }\n    )\n]);\n```\n\n# methods\n\n``` js\nvar deepEqual = require('deep-equal')\n```\n\n## deepEqual(a, b, opts)\n\nCompare objects `a` and `b`, returning whether they are equal according to a recursive equality algorithm.\n\nIf `opts.strict` is `true`, use strict equality (`===`) to compare leaf nodes.\nThe default is to use coercive equality (`==`) because that's how `assert.deepEqual()` works by default.\n\n# install\n\nWith [npm](https://npmjs.org) do:\n\n```\nnpm install deep-equal\n```\n\n# test\n\nWith [npm](https://npmjs.org) do:\n\n```\nnpm test\n```\n\n[package-url]: https://npmjs.org/package/deep-equal\n[npm-version-svg]: https://versionbadg.es/inspect-js/deep-equal.svg\n[deps-svg]: https://david-dm.org/inspect-js/node-deep-equal.svg\n[deps-url]: https://david-dm.org/inspect-js/node-deep-equal\n[dev-deps-svg]: https://david-dm.org/inspect-js/node-deep-equal/dev-status.svg\n[dev-deps-url]: https://david-dm.org/inspect-js/node-deep-equal#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/deep-equal.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/deep-equal.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/deep-equal.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=deep-equal\n[codecov-image]: https://codecov.io/gh/inspect-js/node-deep-equal/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/inspect-js/node-deep-equal/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/inspect-js/node-deep-equal\n[actions-url]: https://github.com/inspect-js/node-deep-equal/actions\n", "readmeFilename": "readme.markdown", "gitHead": "be5f0362c99f4f205628f289a04f703a1023005f", "bugs": {"url": "https://github.com/inspect-js/node-deep-equal/issues"}, "homepage": "https://github.com/inspect-js/node-deep-equal#readme", "_nodeVersion": "21.1.0", "_npmVersion": "10.2.0", "dist": {"integrity": "sha512-5tdhKF6DbU7iIzrIOa1AOUt39ZRm13cmL1cGEh//aqR8x9+tNfbywRf0n5FD/18OKMdo7DNEtrX2t22ZAkI+eg==", "shasum": "78a561b7830eef3134c7f6f3a3d6af272a678761", "tarball": "https://registry.npmjs.org/deep-equal/-/deep-equal-1.1.2.tgz", "fileCount": 15, "unpackedSize": 75315, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHa2pZDcbT1RfvesS1yWTfNR6fB+OhWL/U4HN3DbTsYcAiEAwL3qcpQ4jXvg8H4A8637V7ReXU8cDMTgxVkGoNo09qc="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deep-equal_1.1.2_1699489482899_0.3900737675225001"}, "_hasShrinkwrap": false}, "2.2.3": {"name": "deep-equal", "version": "2.2.3", "description": "node's assert.deepEqual algorithm", "main": "index.js", "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package": "./package.json", "./package.json": "./package.json"}, "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "lint": "eslint --ext=js,mjs .", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "dependencies": {"array-buffer-byte-length": "^1.0.0", "call-bind": "^1.0.5", "es-get-iterator": "^1.1.3", "get-intrinsic": "^1.2.2", "is-arguments": "^1.1.1", "is-array-buffer": "^3.0.2", "is-date-object": "^1.0.5", "is-regex": "^1.1.4", "is-shared-array-buffer": "^1.0.2", "isarray": "^2.0.5", "object-is": "^1.1.5", "object-keys": "^1.1.1", "object.assign": "^4.1.4", "regexp.prototype.flags": "^1.5.1", "side-channel": "^1.0.4", "which-boxed-primitive": "^1.0.2", "which-collection": "^1.0.1", "which-typed-array": "^1.1.13"}, "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "aud": "^2.0.3", "auto-changelog": "^2.4.0", "available-typed-arrays": "^1.0.5", "eslint": "=8.8.0", "for-each": "^0.3.3", "has-proto": "^1.0.1", "has-symbols": "^1.0.3", "has-typed-arrays": "^1.0.1", "in-publish": "^2.0.1", "npmignore": "^0.3.0", "nyc": "^10.3.2", "object.getownpropertydescriptors": "^2.1.7", "safe-publish-latest": "^2.0.0", "semver": "^6.3.1", "tape": "^5.7.2"}, "repository": {"type": "git", "url": "git+ssh://**************/inspect-js/node-deep-equal.git"}, "keywords": ["equality", "equal", "compare"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://substack.net"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "engines": {"node": ">= 0.4"}, "testling": {"files": "test/*.js", "browsers": {"ie": [6, 7, 8, 9], "ff": [3.5, 10, 15], "chrome": [10, 22], "safari": [5.1], "opera": [12]}}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "browser": {"assert.js": false}, "publishConfig": {"ignore": [".github/workflows"]}, "_id": "deep-equal@2.2.3", "gitHead": "48d3bb5b7fe3e65fd564b737c69a9411eb40bc65", "bugs": {"url": "https://github.com/inspect-js/node-deep-equal/issues"}, "homepage": "https://github.com/inspect-js/node-deep-equal#readme", "_nodeVersion": "21.1.0", "_npmVersion": "10.2.0", "dist": {"integrity": "sha512-ZIwpnevOurS8bpT4192sqAowWM76JDKSHYzMLty3BZGSswgq6pBaH3DhCSW5xVAZICZyKdOBPjwww5wfgT/6PA==", "shasum": "af89dafb23a396c7da3e862abc0be27cf51d56e1", "tarball": "https://registry.npmjs.org/deep-equal/-/deep-equal-2.2.3.tgz", "fileCount": 12, "unpackedSize": 90347, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEqtFA8A6edqkPZPtrMtlomXsXSi/TTumYo1j59LHRKQAiEAljlz+9ubbEB1nA5sLGKIGlUGuHdP36OqBXfrRU8UeoQ="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/deep-equal_2.2.3_1699489605478_0.14523276866726342"}, "_hasShrinkwrap": false}}, "readme": "# deep-equal <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n<PERSON><PERSON>'s `assert.deepEqual() algorithm` as a standalone module, that also works in browser environments.\n\nIt mirrors the robustness of node's own `assert.deepEqual` and is robust against later builtin modification.\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\n# example\n\n``` js\nvar equal = require('deep-equal');\nconsole.dir([\n    equal(\n        { a : [ 2, 3 ], b : [ 4 ] },\n        { a : [ 2, 3 ], b : [ 4 ] }\n    ),\n    equal(\n        { x : 5, y : [6] },\n        { x : 5, y : 6 }\n    )\n]);\n```\n\n# methods\n\n``` js\nvar deepEqual = require('deep-equal')\n```\n\n## deepEqual(a, b, opts)\n\nCompare objects `a` and `b`, returning whether they are equal according to a recursive equality algorithm.\n\nIf `opts.strict` is `true`, use strict equality (`===`) to compare leaf nodes.\nThe default is to use coercive equality (`==`) because that's how `assert.deepEqual()` works by default.\n\n# install\n\nWith [npm](https://npmjs.org) do:\n\n```\nnpm install deep-equal\n```\n\n# test\n\nWith [npm](https://npmjs.org) do:\n\n```\nnpm test\n```\n\n[package-url]: https://npmjs.org/package/deep-equal\n[npm-version-svg]: https://versionbadg.es/inspect-js/deep-equal.svg\n[deps-svg]: https://david-dm.org/inspect-js/node-deep-equal.svg\n[deps-url]: https://david-dm.org/inspect-js/node-deep-equal\n[dev-deps-svg]: https://david-dm.org/inspect-js/node-deep-equal/dev-status.svg\n[dev-deps-url]: https://david-dm.org/inspect-js/node-deep-equal#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/deep-equal.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/deep-equal.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/deep-equal.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=deep-equal\n[codecov-image]: https://codecov.io/gh/inspect-js/node-deep-equal/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/inspect-js/node-deep-equal/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/inspect-js/node-deep-equal\n[actions-url]: https://github.com/inspect-js/node-deep-equal/actions\n", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "time": {"modified": "2024-05-06T13:39:30.880Z", "created": "2012-02-11T05:42:13.188Z", "0.0.0": "2012-02-11T05:42:14.579Z", "0.1.0": "2013-10-14T14:52:24.917Z", "0.1.1": "2013-12-20T20:40:18.118Z", "0.1.2": "2013-12-21T03:05:03.548Z", "0.2.0": "2014-01-29T22:04:03.965Z", "0.2.1": "2014-01-29T22:05:28.806Z", "0.2.2": "2015-02-07T18:27:09.674Z", "1.0.0": "2015-02-07T18:27:38.585Z", "1.0.1": "2015-08-29T21:02:28.562Z", "1.1.0": "2019-08-28T05:42:20.711Z", "1.1.1": "2019-11-12T20:21:12.351Z", "2.0.0": "2019-12-03T23:33:03.946Z", "2.0.1": "2019-12-09T06:22:25.201Z", "2.0.2": "2020-04-01T07:18:14.359Z", "2.0.3": "2020-04-24T06:11:38.843Z", "2.0.4": "2020-10-02T04:22:36.888Z", "2.0.5": "2020-12-01T21:54:56.583Z", "2.1.0": "2022-11-02T18:03:20.238Z", "2.2.0": "2023-01-05T23:02:51.866Z", "2.2.1": "2023-04-27T07:05:10.112Z", "2.2.2": "2023-07-10T18:49:25.156Z", "1.1.2": "2023-11-09T00:24:43.132Z", "2.2.3": "2023-11-09T00:26:45.738Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "repository": {"type": "git", "url": "git+ssh://**************/inspect-js/node-deep-equal.git"}, "users": {"freethenation": true, "dam": true, "pana": true, "popomore": true, "zerodi": true, "amirmehmood": true, "simplyianm": true, "joris-van-der-wel": true, "fwoelffel": true, "mightyiam": true, "koulmomo": true, "agat": true, "mkamakura": true, "leodutra": true, "klyngbaek": true, "yatsu": true, "j.su": true, "ackhub": true, "rtivital": true, "jovinbm": true, "akarem": true, "shanewholloway": true, "brainpoint": true, "jacob-beltran": true, "tzq1011": true, "qujian": true, "theaklair": true, "erikvold": true, "dm7": true, "rfortune": true, "gleb_cher": true, "nickeljew": true, "tedyhy": true, "leafac": true, "bcowgi11": true}, "readmeFilename": "readme.markdown", "homepage": "https://github.com/inspect-js/node-deep-equal#readme", "keywords": ["equality", "equal", "compare"], "bugs": {"url": "https://github.com/inspect-js/node-deep-equal/issues"}, "license": "MIT", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://substack.net"}, {"name": "<PERSON>", "email": "<EMAIL>"}]}