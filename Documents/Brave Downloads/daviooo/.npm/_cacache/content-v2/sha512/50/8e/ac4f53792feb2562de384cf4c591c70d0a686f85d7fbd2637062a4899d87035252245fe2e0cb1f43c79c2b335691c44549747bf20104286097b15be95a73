{"_id": "keyv", "_rev": "71-b90fbd204fc72ff10edad19f8af1fb55", "name": "keyv", "dist-tags": {"next": "5.0.0-rc.1", "latest": "5.2.3"}, "versions": {"0.0.0": {"name": "keyv", "version": "0.0.0", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@0.0.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/keyv", "bugs": {"url": "https://github.com/lukechilds/keyv/issues"}, "dist": {"shasum": "45b4452124bba1b11d531998f60c4385ffa375f8", "tarball": "https://registry.npmjs.org/keyv/-/keyv-0.0.0.tgz", "integrity": "sha512-+rm41suCsJSxZlLE1GpaGbremsOK2cg4NzfXxi/6j8Yu7CN5impbABvf/eojr8XEX7R5k7wwVoiTUMCcIWSJ7A==", "signatures": [{"sig": "MEQCIGd4Bpc/aHBjXvEsTL/63WwAQjViSCA/z93SowI83jYXAiAdnzcNlLjPFSSj3W0Y3OaFXv5/13UgCOKZBaOxyfRmHQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "45b4452124bba1b11d531998f60c4385ffa375f8", "gitHead": "81a01f3f851c11e430fdcf6614cf07de7543d2e9", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/keyv.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Simple key/value store with support for multiple backends", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.19.1", "nyc": "^10.3.2", "coveralls": "^2.13.1"}, "_npmOperationalInternal": {"tmp": "tmp/keyv-0.0.0.tgz_1499435005768_0.6846128811594099", "host": "s3://npm-registry-packages"}}, "0.1.0": {"name": "keyv", "version": "0.1.0", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@0.1.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/keyv", "bugs": {"url": "https://github.com/lukechilds/keyv/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "29c39c4b98a917d64498667b7f4581b17635bc13", "tarball": "https://registry.npmjs.org/keyv/-/keyv-0.1.0.tgz", "integrity": "sha512-uJ5yhLUM40vnbVgnupDQEDlT2rK/Z7WItoDcMJuT2PISVNYDiHUHlHi66qUqmwIY4RqK3ehIf399LQqLxwnXsA==", "signatures": [{"sig": "MEUCIEJINy+BDmywxIC3qYlKHUbxDNYao3FjSRV8ml0N3nY+AiEA63hfQFKBd5zJqg5ygqkKS2G5T/gil5KkUBdeiOlJ8L4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "29c39c4b98a917d64498667b7f4581b17635bc13", "gitHead": "65d992321f7c3a5debfe3fd2b31375f51cd665d5", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/keyv.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Simple key/value store with support for multiple backends", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.19.1", "nyc": "^10.3.2", "delay": "^2.0.0", "coveralls": "^2.13.1", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv-0.1.0.tgz_1499778130524_0.5003696596249938", "host": "s3://npm-registry-packages"}}, "0.1.1": {"name": "keyv", "version": "0.1.1", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@0.1.1", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/keyv", "bugs": {"url": "https://github.com/lukechilds/keyv/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "0b2b85e07c48918bd60a53fdd91b4fb18a5a5384", "tarball": "https://registry.npmjs.org/keyv/-/keyv-0.1.1.tgz", "integrity": "sha512-UNSGgYoQpmiU26gvz8hp640RqWpBqEXIHsA9YIoKfincTJcXUdh14krlLnY2aAxvlcdUnEstJ4xxDgaxc6Vzhg==", "signatures": [{"sig": "MEUCIHe51IlQuUjdI+Rk89Jys67XkqWbmtL+d10iaOhUgS2JAiEAsb/+Px1xMnpMLyfo+rH3BMcG7RijZs1DcscLeXOBp00=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "0b2b85e07c48918bd60a53fdd91b4fb18a5a5384", "gitHead": "7d2d0aa9fb18b10e222dc969e777742ecb353883", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/keyv.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Simple key/value store with support for multiple backends", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.19.1", "nyc": "^10.3.2", "coveralls": "^2.13.1", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv-0.1.1.tgz_1499781279752_0.5905001286882907", "host": "s3://npm-registry-packages"}}, "0.1.2": {"name": "keyv", "version": "0.1.2", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@0.1.2", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/keyv", "bugs": {"url": "https://github.com/lukechilds/keyv/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "7bbd67ed0ee512d6e4a21fff2fb73022562456d1", "tarball": "https://registry.npmjs.org/keyv/-/keyv-0.1.2.tgz", "integrity": "sha512-pbMvgrNckRakkNg3g/UWjJFkFOVZ7wvER7pwNJ2u0v810mWsUuUaITid+9soFj0Jz69UHIhmzr3rGN6ap578ww==", "signatures": [{"sig": "MEUCIFyWQjMWLfMeebPlO2rBMk17CmO7DTgzl3FfS8EbE04WAiEAiUIOvp7KqyPXGJbPW93TXbc//swC8HU6nw3cL8udhK4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "7bbd67ed0ee512d6e4a21fff2fb73022562456d1", "gitHead": "cdff7db698f975e3d557d430ab1e31a1a549e286", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/keyv.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Simple key/value store with support for multiple backends", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.19.1", "nyc": "^10.3.2", "delay": "^2.0.0", "coveralls": "^2.13.1", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv-0.1.2.tgz_1499782265200_0.7421580879017711", "host": "s3://npm-registry-packages"}}, "0.1.3": {"name": "keyv", "version": "0.1.3", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@0.1.3", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/keyv", "bugs": {"url": "https://github.com/lukechilds/keyv/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "72de0fbb676abff7015c5b81c76a8156ad47d2f6", "tarball": "https://registry.npmjs.org/keyv/-/keyv-0.1.3.tgz", "integrity": "sha512-21qcKCWlXHdECf6lDmU2FLY2EGSt4XJDI5Yjv1AD7xE7tf/VkemjMdAGIzVnFlD0QbQSLZMr3G+nmQowgLK/uQ==", "signatures": [{"sig": "MEQCIC7y4cN4DaQavPzGqh8oa6FO/xIP0P2wUrqpjEKEU+enAiAhem1sOOwuX+pnmE3yObCL/Z6RZhCt346YL54MTqEu0w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "72de0fbb676abff7015c5b81c76a8156ad47d2f6", "gitHead": "08950876ed72e4fae12c9e7aa6e6fc82a8092314", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/keyv.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Simple key/value store with support for multiple backends", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.19.1", "nyc": "^10.3.2", "delay": "^2.0.0", "coveralls": "^2.13.1", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv-0.1.3.tgz_1499790512545_0.4035629774443805", "host": "s3://npm-registry-packages"}}, "0.2.0": {"name": "keyv", "version": "0.2.0", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@0.2.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/keyv", "bugs": {"url": "https://github.com/lukechilds/keyv/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "269d21f5da3aa077f84701deb2340ba050323dd4", "tarball": "https://registry.npmjs.org/keyv/-/keyv-0.2.0.tgz", "integrity": "sha512-mZdWIZhasIZYjpowOFd863Rw2T5rwNHFIfVToUoKREp8RfYRDt2DyVBv6oPLPaCRVfFmeALFlV84rXVOBzfqjA==", "signatures": [{"sig": "MEUCIQDmnJt1erM03npfy9BnDxv5zIy0dNsTsg7O5LFIPsw1IwIgYpW6v42UevG6ZE7znnsHEZOxWm8qRAevczZSpy6rztg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "269d21f5da3aa077f84701deb2340ba050323dd4", "gitHead": "2349182d83dffc26c29f8bbf3bc714d9d1032a11", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/keyv.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Simple key/value store with support for multiple backends", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.19.1", "nyc": "^10.3.2", "delay": "^2.0.0", "coveralls": "^2.13.1", "keyv-api-tests": "*", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv-0.2.0.tgz_1499957090064_0.1645263903774321", "host": "s3://npm-registry-packages"}}, "0.2.1": {"name": "keyv", "version": "0.2.1", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@0.2.1", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/keyv", "bugs": {"url": "https://github.com/lukechilds/keyv/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "e083d440155f056c4cd651e5385b9ff6833f798c", "tarball": "https://registry.npmjs.org/keyv/-/keyv-0.2.1.tgz", "integrity": "sha512-PfEzh8soPZRwYgygbIjkkU9QHScouBImxVizE4EQz+AlCv2xpA2OHnrVLVuaeAAhDkktjCdam3tSgcQXQQGJCQ==", "signatures": [{"sig": "MEUCIBwkiB2GlJ4zjhDCmsibgbD+ZiZrKuC9XhUYsDJUFUjHAiEA3x9CqECv1gw6DtDzuMHmML5jKHJlvm9iKXlJbhK5lQo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "e083d440155f056c4cd651e5385b9ff6833f798c", "gitHead": "7540c2f8c85661e9d2b380681bb8aece029d8145", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/keyv.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Simple key/value store with support for multiple backends", "directories": {}, "_nodeVersion": "4.8.4", "dependencies": {}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.19.1", "nyc": "^10.3.2", "delay": "^2.0.0", "coveralls": "^2.13.1", "keyv-api-tests": "*", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv-0.2.1.tgz_1500056763584_0.04589479323476553", "host": "s3://npm-registry-packages"}}, "0.3.0": {"name": "keyv", "version": "0.3.0", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@0.3.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/keyv", "bugs": {"url": "https://github.com/lukechilds/keyv/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "c362c48f2052cf570709b09884306f9a7fc583f4", "tarball": "https://registry.npmjs.org/keyv/-/keyv-0.3.0.tgz", "integrity": "sha512-4y4mDmtEt3JgN6KT7xTIT1uS8K0poOQZbACj7pWq5BRZiagOpXEGdHbuEKaokFpq4d2czPWYDLqg/gT7WJTB9w==", "signatures": [{"sig": "MEUCIQCfKQvIhb7561rHOLIsv78tBaOwF3qV2RmtC1a6IRMrbgIgSAnstk0EvNAG+8afa+CwF07Mg+iuV3nu4oi/pOYhMcE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "c362c48f2052cf570709b09884306f9a7fc583f4", "gitHead": "f3b5d14d0b93c89e9fa994119a5358e5c9b7c34c", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/keyv.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Simple key/value store with support for multiple backends", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {"json-buffer": "^3.0.0"}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.19.1", "nyc": "^10.3.2", "delay": "^2.0.0", "coveralls": "^2.13.1", "keyv-api-tests": "*", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv-0.3.0.tgz_1500317790952_0.9430915275588632", "host": "s3://npm-registry-packages"}}, "0.3.1": {"name": "keyv", "version": "0.3.1", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@0.3.1", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/keyv", "bugs": {"url": "https://github.com/lukechilds/keyv/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "4b0ac0caab27218c32f1faf54ac11809d9ec1a73", "tarball": "https://registry.npmjs.org/keyv/-/keyv-0.3.1.tgz", "integrity": "sha512-3DWsRK+Qlt8lydCIFxNLHBdhri1RIe6lDlUFzSnW/Roz377HCVqhKZ+j/oPGhJg788XG0TSv96ICvA160x0FZQ==", "signatures": [{"sig": "MEUCIGuRL5DuvYpaHXw2L0YcpS+J6JnfHRaVoFVzhAyDqEzgAiEA6nb2e55DfLBp+4KvksGyykwqOp4E8ylIOZQG6egXQ1I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "4b0ac0caab27218c32f1faf54ac11809d9ec1a73", "gitHead": "f4b2d502fa49916f8fcb1ec6d86267b9ada51c2e", "scripts": {"test": "xo && nyc ava test/keyv.js", "coverage": "nyc report --reporter=text-lcov | coveralls", "test:full": "xo && nyc ava --serial"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/keyv.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Simple key/value store with support for multiple backends", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {"json-buffer": "^3.0.0"}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.19.1", "nyc": "^10.3.2", "delay": "^2.0.0", "coveralls": "^2.13.1", "keyv-mongo": "*", "keyv-redis": "*", "keyv-api-tests": "*", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv-0.3.1.tgz_1500385004401_0.4757091256324202", "host": "s3://npm-registry-packages"}}, "0.4.0": {"name": "keyv", "version": "0.4.0", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@0.4.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/keyv", "bugs": {"url": "https://github.com/lukechilds/keyv/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "6e1ce070cc33157a8a9aeaf7db68322a5d0cb872", "tarball": "https://registry.npmjs.org/keyv/-/keyv-0.4.0.tgz", "integrity": "sha512-7acSRddeoWbNCpOLSRzSFrqdZKGVUlRwdmA0dmloFj22YThLrsVvNuydGRstvb08gdvZiMA6wszox+WRqgtyeg==", "signatures": [{"sig": "MEQCICREodOQeSSQj6sJMNMJ6mA71Myy6HzAkxaULmpqI031AiAbndG55OJ1X0zotASyLlCcmsoEUajcFBUmt4tWg02r+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "6e1ce070cc33157a8a9aeaf7db68322a5d0cb872", "gitHead": "3ed0b7f7ddacd883b481a2a4f5d56c75542664bf", "scripts": {"test": "xo && nyc ava test/keyv.js", "coverage": "nyc report --reporter=text-lcov | coveralls", "test:full": "xo && nyc ava --serial"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/keyv.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Simple key/value store with support for multiple backends", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {"json-buffer": "^3.0.0"}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.19.1", "nyc": "^10.3.2", "delay": "^2.0.0", "coveralls": "^2.13.1", "keyv-mongo": "*", "keyv-redis": "*", "get-root-module": "^0.1.3", "keyv-test-suite": "*", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv-0.4.0.tgz_1501081199882_0.30825260002166033", "host": "s3://npm-registry-packages"}}, "0.5.0": {"name": "keyv", "version": "0.5.0", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@0.5.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/keyv", "bugs": {"url": "https://github.com/lukechilds/keyv/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "33e439fa40bca8605af98aa5268160b95a7c118e", "tarball": "https://registry.npmjs.org/keyv/-/keyv-0.5.0.tgz", "integrity": "sha512-YvH6s5FFD1Rmxie+jwgYPhKfm9B6gk7kUcCFAVxac6tQH7PjrlVhOUTUQ1nnjlFA/356E6UcSIpuMpdT1ZE18Q==", "signatures": [{"sig": "MEUCIHlimIc+gbBihhH55cZ+yRNm7ryVGpyLZaK36DLxg0CUAiEA8jBgb2mEDS4il+MwDh7vWxpY4Qi0oZSX/JfTzgZf69M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "33e439fa40bca8605af98aa5268160b95a7c118e", "gitHead": "0ce0c78c4e0e00f7b0a15b0177c0d041dabdbdd8", "scripts": {"test": "xo && nyc ava test/keyv.js", "coverage": "nyc report --reporter=text-lcov | coveralls", "test:full": "xo && nyc ava --serial"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/keyv.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Simple key/value store with support for multiple backends", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {"json-buffer": "^3.0.0"}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.20.0", "nyc": "^11.0.3", "this": "^1.0.2", "delay": "^2.0.0", "coveralls": "^2.13.1", "keyv-mongo": "*", "keyv-redis": "*", "keyv-sqlite": "*", "keyv-test-suite": "*", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv-0.5.0.tgz_1501334496319_0.8208789532072842", "host": "s3://npm-registry-packages"}}, "0.6.0": {"name": "keyv", "version": "0.6.0", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@0.6.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/keyv", "bugs": {"url": "https://github.com/lukechilds/keyv/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "63adea0116a8264c9099fcc6617f3f46869c20bc", "tarball": "https://registry.npmjs.org/keyv/-/keyv-0.6.0.tgz", "integrity": "sha512-7w+HFSMY0qz3XHH/fmFOwMr3eUQODrIUGJPgOetd9i5Te+0ty6XdKWorgZzJ/Z6sqUTwJFFNzlhNvl8ye68p4g==", "signatures": [{"sig": "MEUCIDNViGClxsjKEK8IJuLeDYMAOAFyTIfW4KhLx9gwyZZWAiEA201SdVHa+qChhDjbyD3d8fg/X/HsFAEc9/NRLtuhK84=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "63adea0116a8264c9099fcc6617f3f46869c20bc", "gitHead": "bee96d9a02ec1de10ef4502c1911b36eb859595c", "scripts": {"test": "xo && nyc ava test/keyv.js", "coverage": "nyc report --reporter=text-lcov | coveralls", "test:full": "xo && nyc ava --serial"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/keyv.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Simple key/value store with support for multiple backends", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {"json-buffer": "^3.0.0"}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.20.0", "nyc": "^11.0.3", "this": "^1.0.2", "delay": "^2.0.0", "coveralls": "^2.13.1", "keyv-mongo": "*", "keyv-redis": "*", "keyv-sqlite": "*", "keyv-postgres": "^0.1.0", "keyv-test-suite": "*", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv-0.6.0.tgz_1501370211724_0.36876085330732167", "host": "s3://npm-registry-packages"}}, "0.7.0": {"name": "keyv", "version": "0.7.0", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@0.7.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/keyv", "bugs": {"url": "https://github.com/lukechilds/keyv/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "425b7267d282e5f43fd0e3bacc18d6314495f030", "tarball": "https://registry.npmjs.org/keyv/-/keyv-0.7.0.tgz", "integrity": "sha512-9v/DJu7fslKwlHx7zY3jrbmomJowoZ9XCXFGGu4KcN0DG2qDwaGRVzaFt5gt0swwTQGHyqxtesDg7UF8JYXCew==", "signatures": [{"sig": "MEUCIQDfFudZtTxygjeWgVp1y3qcR7mj36YtXbsbPTczmgWJlAIgFnjFvsaMg4YIvVJ3UbwqfRZfhkTK2g+mJLUYRVu2+AI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "425b7267d282e5f43fd0e3bacc18d6314495f030", "gitHead": "8414dde0a0a13ed75ccf39f33e4ea5ae60bdeea8", "scripts": {"test": "xo && nyc ava test/keyv.js", "coverage": "nyc report --reporter=text-lcov | coveralls", "test:full": "xo && nyc ava --serial"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/keyv.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Simple key/value store with support for multiple backends", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {"json-buffer": "^3.0.0"}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.20.0", "nyc": "^11.0.3", "this": "^1.0.2", "delay": "^2.0.0", "coveralls": "^2.13.1", "keyv-mongo": "*", "keyv-redis": "*", "keyv-sqlite": "*", "keyv-postgres": "^0.1.0", "keyv-test-suite": "*", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv-0.7.0.tgz_1501399369388_0.22141490271314979", "host": "s3://npm-registry-packages"}}, "0.8.0": {"name": "keyv", "version": "0.8.0", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@0.8.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/keyv", "bugs": {"url": "https://github.com/lukechilds/keyv/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "e27685f5248945188eedead24e42840fe44841c7", "tarball": "https://registry.npmjs.org/keyv/-/keyv-0.8.0.tgz", "integrity": "sha512-taK9uXZw88h90FqMgjgbr4AzLt1EIuV4mEwcBZO1xk8KeT+yhwg9ElWBVPWp0tqQqYUckJHNwQAtXNxsN5rGXQ==", "signatures": [{"sig": "MEQCICQpR1c9bCSWHXHt013NTTVnoMg7cYbVlS0EZv41HK0UAiBhd1FYazvpdsDl1PEcU0scfs7WZwk2XV7/lFOP9irv6g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "e27685f5248945188eedead24e42840fe44841c7", "gitHead": "cc4650b3156550c06e295192f74737a73c667bbc", "scripts": {"test": "xo && nyc ava test/keyv.js", "coverage": "nyc report --reporter=text-lcov | coveralls", "test:full": "xo && nyc ava --serial"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/keyv.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Simple key/value store with support for multiple backends", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {"json-buffer": "3.0.0"}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.20.0", "nyc": "^11.0.3", "this": "^1.0.2", "delay": "^2.0.0", "coveralls": "^2.13.1", "keyv-mongo": "1.0.0", "keyv-mysql": "0.2.0", "keyv-redis": "1.3.1", "keyv-sqlite": "1.2.0", "keyv-postgres": "1.0.1", "keyv-test-suite": "1.4.2", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv-0.8.0.tgz_1501603295636_0.9365875863004476", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "keyv", "version": "1.0.0", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@1.0.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/keyv", "bugs": {"url": "https://github.com/lukechilds/keyv/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "cb9c3f2d71a5aed6c53a34035c5ec232c3964ecc", "tarball": "https://registry.npmjs.org/keyv/-/keyv-1.0.0.tgz", "integrity": "sha512-ULOIQNmc6zeMd/8fGA3tSKgCV60sQ65z5Wg779pDaUnq6zRLQKpPlRYEMqq9GNoaSD9e4ikZK8yWcgL3MJHJAw==", "signatures": [{"sig": "MEUCICpPgoqPotU1ODwZNJc6diGs728ekGXH83ZUNa13VS1sAiEA3Rm5RpEMHEGfcxezNvGCI+6hW7krXoPCUivvUjH1JFI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "cb9c3f2d71a5aed6c53a34035c5ec232c3964ecc", "gitHead": "701c530d7ed0239c6aa304277c2fdc24b7de828f", "scripts": {"test": "xo && nyc ava test/keyv.js", "coverage": "nyc report --reporter=text-lcov | coveralls", "test:full": "xo && nyc ava --serial"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/keyv.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Simple key-value storage with support for multiple backends", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {"json-buffer": "3.0.0"}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.20.0", "nyc": "^11.0.3", "this": "^1.0.2", "delay": "^2.0.0", "coveralls": "^2.13.1", "keyv-mongo": "*", "keyv-mysql": "*", "keyv-redis": "*", "keyv-sqlite": "*", "keyv-postgres": "*", "keyv-test-suite": "*", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv-1.0.0.tgz_1501712882500_0.88813791051507", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "keyv", "version": "1.0.1", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@1.0.1", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/keyv", "bugs": {"url": "https://github.com/lukechilds/keyv/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "77b38aecad1927729ba155c6452d9130fa00918b", "tarball": "https://registry.npmjs.org/keyv/-/keyv-1.0.1.tgz", "integrity": "sha512-aweO1jzSGED1yex6jprSEcaLYoh1CyuJtcNjx5oaV50GDw1Y4/SZceRgTFb9sYbHPYSMK31SghldlqU3fPglvQ==", "signatures": [{"sig": "MEUCIQDuUh6bgscK+TSUmBMgUb1MLsvxIJX2Yl8SBWVg29LuCwIgMRHZyEx9DMFWs1c0yCNbHobUdHZRJZh9B2uvis2Sk20=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "77b38aecad1927729ba155c6452d9130fa00918b", "gitHead": "57441ba8e0db0e2e034f81b7a9e0ba1d713d1e32", "scripts": {"test": "xo && nyc ava test/keyv.js", "coverage": "nyc report --reporter=text-lcov | coveralls", "test:full": "xo && nyc ava --serial"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/keyv.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Simple key-value storage with support for multiple backends", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {"json-buffer": "3.0.0"}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.20.0", "nyc": "^11.0.3", "this": "^1.0.2", "delay": "^2.0.0", "coveralls": "^2.13.1", "keyv-mongo": "*", "keyv-mysql": "*", "keyv-redis": "*", "keyv-sqlite": "*", "keyv-postgres": "*", "keyv-test-suite": "*", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv-1.0.1.tgz_1501718251114_0.04029018827714026", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "keyv", "version": "1.0.2", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@1.0.2", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/keyv", "bugs": {"url": "https://github.com/lukechilds/keyv/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "db8c813f47de55778ea4497dae1c6eb779918b24", "tarball": "https://registry.npmjs.org/keyv/-/keyv-1.0.2.tgz", "integrity": "sha512-axWNfdI6mnkOn2Q34aKlNaXZ/7U94gbm6kTwZ/T4nWs7clqPE11FWxF48p7OSulpA3wvNac7Xo+g12M4aRObHw==", "signatures": [{"sig": "MEUCIQCPSmDiOGn5QubW82wKW8MQukReWYWSNph5o7UjbsetMgIgbjUf5vZBoFfKN25cibEXma01W3fbpYL4nf6WOgMQG1c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "db8c813f47de55778ea4497dae1c6eb779918b24", "gitHead": "d96a19e172b049ef622ac9b28f8d4d4458e7cd65", "scripts": {"test": "xo && nyc ava test/keyv.js", "coverage": "nyc report --reporter=text-lcov | coveralls", "test:full": "xo && nyc ava --serial"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/keyv.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Simple key-value storage with support for multiple backends", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {"json-buffer": "3.0.0"}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.20.0", "nyc": "^11.0.3", "this": "^1.0.2", "delay": "^2.0.0", "coveralls": "^2.13.1", "keyv-mongo": "*", "keyv-mysql": "*", "keyv-redis": "*", "timekeeper": "^1.0.0", "keyv-sqlite": "*", "keyv-postgres": "*", "keyv-test-suite": "*", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv-1.0.2.tgz_1501754368232_0.23013364523649216", "host": "s3://npm-registry-packages"}}, "1.0.3": {"name": "keyv", "version": "1.0.3", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@1.0.3", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/keyv", "bugs": {"url": "https://github.com/lukechilds/keyv/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "d5e96a4b6486b4765252a9903af7eb7a9b1ddd0e", "tarball": "https://registry.npmjs.org/keyv/-/keyv-1.0.3.tgz", "integrity": "sha512-Li7/L+pCKSoBC+OMcDs2a1Vbn40HG52+5eK1fjFYkgpHvKXFbXf5T14R1sassTJw9LTwc/jeZvXB5jEG4Cd79w==", "signatures": [{"sig": "MEYCIQC9UHX42a57jVtQAR8tpxquJWVdcXlHoOpqXxVerjND7QIhALn0rTfVAMYq4yLEph72ScjN3TUfxgr/Bk7ZmeIEUpvB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "d5e96a4b6486b4765252a9903af7eb7a9b1ddd0e", "gitHead": "d942a2b38a49b9c8d8f1b51fa67ae39dda6f143f", "scripts": {"test": "xo && nyc ava test/keyv.js", "coverage": "nyc report --reporter=text-lcov | coveralls", "test:full": "xo && nyc ava --serial"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/keyv.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Simple key-value storage with support for multiple backends", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {"json-buffer": "3.0.0"}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.20.0", "nyc": "^11.0.3", "this": "^1.0.2", "delay": "^2.0.0", "coveralls": "^2.13.1", "keyv-mongo": "*", "keyv-mysql": "*", "keyv-redis": "*", "timekeeper": "^1.0.0", "keyv-sqlite": "*", "keyv-postgres": "*", "keyv-test-suite": "*", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv-1.0.3.tgz_1501754741987_0.44476545066572726", "host": "s3://npm-registry-packages"}}, "1.0.4": {"name": "keyv", "version": "1.0.4", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@1.0.4", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/keyv", "bugs": {"url": "https://github.com/lukechilds/keyv/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "20c4c26cd6a6da33950f1f488ca3e02570a21766", "tarball": "https://registry.npmjs.org/keyv/-/keyv-1.0.4.tgz", "integrity": "sha512-d+MUnkUjXw0xWaC8WkK/paT6WzXyOKlwBku/PB2ikECndw2ugxedOQmfdRm+8Cx9YYN1Rkpae6n7K+bAk9omQg==", "signatures": [{"sig": "MEYCIQCdSGaoNy7l9ElGikhvzx4jYsb6LyrmmX+KKPbuP6BTOgIhAIi6SqODdzftLb/gY10hTWVE5weCVcNvJbtd5qlT/NQz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "20c4c26cd6a6da33950f1f488ca3e02570a21766", "gitHead": "62fb070b0a7dd9c3969a43e545bf2cd0a7aa514f", "scripts": {"test": "xo && nyc ava test/keyv.js", "coverage": "nyc report --reporter=text-lcov | coveralls", "test:full": "xo && nyc ava --serial"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/keyv.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Simple key-value storage with support for multiple backends", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {"json-buffer": "3.0.0"}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.20.0", "nyc": "^11.0.3", "this": "^1.0.2", "coveralls": "^2.13.1", "keyv-mongo": "*", "keyv-mysql": "*", "keyv-redis": "*", "timekeeper": "^1.0.0", "keyv-sqlite": "*", "keyv-postgres": "*", "keyv-test-suite": "*", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv-1.0.4.tgz_1501795153456_0.3612369014881551", "host": "s3://npm-registry-packages"}}, "1.0.5": {"name": "keyv", "version": "1.0.5", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@1.0.5", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/keyv", "bugs": {"url": "https://github.com/lukechilds/keyv/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "6041d069383831f82fcddb647d628229652988bb", "tarball": "https://registry.npmjs.org/keyv/-/keyv-1.0.5.tgz", "integrity": "sha512-NLTXqYBwIJDbh/pTbzwri0MeSkqWH5qf596VoXaCZ2BnlYjDO0ghLEbfFK8fks1oOwUpsZDI5d3I1SrnsVRsWA==", "signatures": [{"sig": "MEYCIQDikEsoCLsITZyhKrLqGYpO9LA968MJKR8gPlSO2ATVKQIhAOhJRw1swd6KroBjblYuIX12c+fMBdJGhHSEGZFCI9Mt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "6041d069383831f82fcddb647d628229652988bb", "gitHead": "00fb1f228b97689799fb5ef7993b0426e05acbcd", "scripts": {"test": "xo && nyc ava test/keyv.js", "coverage": "nyc report --reporter=text-lcov | coveralls", "test:full": "xo && nyc ava --serial"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/keyv.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Simple key-value storage with support for multiple backends", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {"json-buffer": "3.0.0"}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.20.0", "nyc": "^11.0.3", "this": "^1.0.2", "coveralls": "^2.13.1", "keyv-mongo": "*", "keyv-mysql": "*", "keyv-redis": "*", "timekeeper": "^1.0.0", "keyv-sqlite": "*", "keyv-postgres": "*", "keyv-test-suite": "*", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv-1.0.5.tgz_1502370332763_0.6003021828364581", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "keyv", "version": "2.0.0", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@2.0.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/keyv", "bugs": {"url": "https://github.com/lukechilds/keyv/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "884d585bb4e595654f027577480f4ab82fa99bac", "tarball": "https://registry.npmjs.org/keyv/-/keyv-2.0.0.tgz", "integrity": "sha512-lQyKweZHlVoOsCALsHvvQg/HipWqOX0x3D9vuc15joBaKWovhsJ8ALrLXaTMxDc8bq46EQoeheBVOvaWpa4Hyw==", "signatures": [{"sig": "MEUCIGK/E32U3+zmEcL4GbCT29osJjvjRD9pC1Xb9smnxpLfAiEA3zIkiiqwc7ecG8EflMFcz4mhj6K/fftmzJAQLgf2HVU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "884d585bb4e595654f027577480f4ab82fa99bac", "gitHead": "1899d6c05665f161bf05b738f1d234732ecc4362", "scripts": {"test": "xo && nyc ava test/keyv.js", "coverage": "nyc report --reporter=text-lcov | coveralls", "test:full": "xo && nyc ava --serial"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/keyv.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Simple key-value storage with support for multiple backends", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {"json-buffer": "3.0.0"}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.20.0", "nyc": "^11.0.3", "this": "^1.0.2", "coveralls": "^2.13.1", "timekeeper": "^1.0.0", "@keyv/mongo": "*", "@keyv/mysql": "*", "@keyv/redis": "*", "@keyv/sqlite": "*", "@keyv/postgres": "*", "@keyv/test-suite": "*", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv-2.0.0.tgz_1502465640941_0.7615975437220186", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "keyv", "version": "2.0.1", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@2.0.1", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/keyv", "bugs": {"url": "https://github.com/lukechilds/keyv/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "d4181858d0ba639adaeb9eca456372f0ffcdc809", "tarball": "https://registry.npmjs.org/keyv/-/keyv-2.0.1.tgz", "integrity": "sha512-Ey7bZxnLwQ7RxWnBueGlHuz0ZFZuWrAMMN0dSG3Akd+/Ucx3XdEMeEGhya4y3ONDREAHniDEENmD7oixhskHtA==", "signatures": [{"sig": "MEQCIBtsVGYyxWrHm1ak5etmIHmjHl6Xr1sVGVRnbL7dB5cBAiBkXrJGyBB/5+De6yK3oVJRyDOV1/5tgfKUNrLFG/Do8A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "d4181858d0ba639adaeb9eca456372f0ffcdc809", "gitHead": "0ac673ff916a4dca3adc4b81cf3926cef0e1dde5", "scripts": {"test": "xo && nyc ava test/keyv.js", "coverage": "nyc report --reporter=text-lcov | coveralls", "test:full": "xo && nyc ava --serial"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/keyv.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Simple key-value storage with support for multiple backends", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {"json-buffer": "3.0.0"}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.20.0", "nyc": "^11.0.3", "this": "^1.0.2", "coveralls": "^2.13.1", "timekeeper": "^1.0.0", "@keyv/mongo": "*", "@keyv/mysql": "*", "@keyv/redis": "*", "@keyv/sqlite": "*", "@keyv/postgres": "*", "@keyv/test-suite": "*", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv-2.0.1.tgz_1502630241834_0.5835089134052396", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "keyv", "version": "2.0.2", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@2.0.2", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/keyv", "bugs": {"url": "https://github.com/lukechilds/keyv/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "5e23fde48a4c59818eddf028cd829798830242f1", "tarball": "https://registry.npmjs.org/keyv/-/keyv-2.0.2.tgz", "integrity": "sha512-xnkdTmZcVf6DWlGVEK1UC05Y2kF+vOOSjMSgI6D8Xtb6UoGpBCg6hYLBMqziVRPPNYe8cfE9dIptGA2kRk/VwQ==", "signatures": [{"sig": "MEQCIDrZfBK7dtANqDB8kaM3VPmXcsKWH9FiSY87UtakdTNZAiA8NLEC2wcXUccj4hfT4p+RHDeJZHmzlEytv0pXg2OOOw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "_from": ".", "_shasum": "5e23fde48a4c59818eddf028cd829798830242f1", "gitHead": "b0c377156057a14e9f9a8f24ab05bf30f72a65b1", "scripts": {"test": "xo && nyc ava test/keyv.js", "coverage": "nyc report --reporter=text-lcov | coveralls", "test:full": "xo && nyc ava --serial"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/keyv.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Simple key-value storage with support for multiple backends", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"json-buffer": "3.0.0"}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.22.0", "nyc": "^11.0.3", "this": "^1.0.2", "coveralls": "^2.13.1", "timekeeper": "^1.0.0", "@keyv/mongo": "*", "@keyv/mysql": "*", "@keyv/redis": "*", "@keyv/sqlite": "*", "@keyv/postgres": "*", "@keyv/test-suite": "*", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv-2.0.2.tgz_1502988362165_0.7405309518799186", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "keyv", "version": "3.0.0", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@3.0.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/keyv", "bugs": {"url": "https://github.com/lukechilds/keyv/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "44923ba39e68b12a7cec7df6c3268c031f2ef373", "tarball": "https://registry.npmjs.org/keyv/-/keyv-3.0.0.tgz", "integrity": "sha512-eguHnq22OE3uVoSYG0LVWNP+4ppamWr9+zWBe1bsNcovIMy6huUJFPgy4mGwCd/rnl3vOLGW1MTlu4c57CT1xA==", "signatures": [{"sig": "MEQCIDYspCmrIeCNRKLtc83tY4+ZyeYpEUPvfHMoGh9faEmRAiADnA01EisjLkep88YRNQaVf85MS46uTHpw+NBmGmwuXQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/index.js", "gitHead": "33ae64413338c465999863ea5545c1ae20b54ddb", "scripts": {"test": "xo && nyc ava test/keyv.js", "coverage": "nyc report --reporter=text-lcov | coveralls", "test:full": "xo && nyc ava --serial"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/keyv.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Simple key-value storage with support for multiple backends", "directories": {}, "_nodeVersion": "8.5.0", "dependencies": {"json-buffer": "3.0.0"}, "devDependencies": {"xo": "^0.19.0", "ava": "^0.22.0", "nyc": "^11.0.3", "this": "^1.0.2", "coveralls": "^3.0.0", "timekeeper": "^2.0.0", "@keyv/mongo": "*", "@keyv/mysql": "*", "@keyv/redis": "*", "@keyv/sqlite": "*", "@keyv/postgres": "*", "@keyv/test-suite": "*", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv-3.0.0.tgz_1507037905372_0.20707569341175258", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "keyv", "version": "3.1.0", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@3.1.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/keyv", "bugs": {"url": "https://github.com/lukechilds/keyv/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "ecc228486f69991e49e9476485a5be1e8fc5c4d9", "tarball": "https://registry.npmjs.org/keyv/-/keyv-3.1.0.tgz", "fileCount": 4, "integrity": "sha512-9ykJ/46SN/9KPM/sichzQ7OvXyGDYKGTaDlKMGCAlg2UK8KRy4jb0d8sFc+0Tt0YYnThq8X2RZgCg74RPxgcVA==", "signatures": [{"sig": "MEUCIEOtAgfJKeegjNvHmmx2/ntSt4ieF20bmuLkVYQlyYrCAiEAtxYGCUv1AZrDqqolL6n5qatJBgnS1UQ4MRbWFXC0Jyk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14661, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfIFjCRA9TVsSAnZWagAAylYP/2zYdghWdARJ2ELExwVV\nCwmegbGhsK6Y1MSoi5Uca/6VF+pgSd6kx6yXgHzBv19AXdytTo4+3+/2qZwJ\nd1pxNOKXC5tN7E+pSeNhy487SFQhmVyRgzpjhk1YuHuuaV1GeDfcb/SY0A4y\n5pQjhYt+BI0xompo2DG6O10QF0mAzTYnu8+wTzaCz2lYob4qJXvDIpmbqBU+\nPt9vutfuTQx4tdXMYqXG1C00he1kDxQvO8FyYK+ojRsJr/g8cySZoQTzi50U\nhTOBT00IoujROa9HWQwc5TmhzFLWraepDVk4Xv/ajV1c5FTWSyttlQQIy/zZ\nE08d3B5KUVz9N4j5OncB4M+dzouR8lBQqeZ8xzXQBy07tvZo21vd/y2Ct5Hd\nTSxMShnHk7591DFJjycp7hePVuIWwwyJ76+sFLwfH/ekylCE6UNnoKt34pwn\n2WmTVgBLFO3t6CU7W7qEdzWh+Fw3Cn2IrgXVDP7UdADFxMjT6QE1P4UvyQYQ\nFSygu1xcJb8oNIwMCtaQx8xObWRIkh7n799mpw3kv8QBgJ0yoNesEMg+yp/Y\nmY2NbPqEagWLnGVDVH2NoBoJrbRzYg0Ww3jI+/N2N3TL6oN2iDYUqFgvA6mS\nRsGDWR9ZRbA6+CVBcqyAdcOG/PWKKrhLSOSbqh2ZsbsiR8ZmXUpBNsA81knm\nebZA\r\n=w+Ka\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "gitHead": "de2e7a2ffc4a5007569b62a31956d7b692a011e7", "scripts": {"test": "xo && nyc ava test/keyv.js", "coverage": "nyc report --reporter=text-lcov | coveralls", "test:full": "xo && nyc ava --serial"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/keyv.git", "type": "git"}, "_npmVersion": "6.3.0", "description": "Simple key-value storage with support for multiple backends", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"json-buffer": "3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.20.1", "ava": "^0.25.0", "nyc": "^11.0.3", "this": "^1.0.2", "coveralls": "^3.0.0", "timekeeper": "^2.0.0", "@keyv/mongo": "*", "@keyv/mysql": "*", "@keyv/redis": "*", "@keyv/sqlite": "*", "@keyv/postgres": "*", "@keyv/test-suite": "*", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_3.1.0_1534886243091_0.33516952118680954", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "keyv", "version": "4.0.0", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@4.0.0", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/keyv", "bugs": {"url": "https://github.com/lukechilds/keyv/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "2d1dab694926b2d427e4c74804a10850be44c12f", "tarball": "https://registry.npmjs.org/keyv/-/keyv-4.0.0.tgz", "fileCount": 4, "integrity": "sha512-U7ioE8AimvRVLfw4LffyOIRhL2xVgmE8T22L6i0BucSnBUyv4w+I7VN/zVZwRKHOI6ZRUcdMdWHQ8KSUvGpEog==", "signatures": [{"sig": "MEYCIQCa0q8kOz8d/KjKaHRsNu/LoqiQNvZFOYmo7bW+2dwhOQIhANlEBxYjQJ3T74zaPvuVV6w5Ffn0QKtT5ctt4nNbUPCJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15397, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdz416CRA9TVsSAnZWagAAhMgP/RwJpkLCpGii+tD7G14e\nr29ymNZEZ07C83RfPE3wwtMCjAzOsKWUbRnhaXbcAGYdvZ4tBNYnca/0DDsf\nkQM88qFSQlLIxXucrh1/iF03053/IOmJKV3id7Tu/fpBqpkDZ++xl6z6ksrQ\norGc9goq3NORNR8FWuLUSiygoY7WNl3ejI4n10FT2K2BOKxq0rTnEuuvHji2\n7xmDkbwp8NwcFmQlgveCFvfvl/yItLaoUVvqIqYE4j9iMM+DfTvCFSfhn1mc\n0f7gz22qOESSSzIO3SXbmd/opckPQ6j3Xri1Sgszn3a/vTGFW/u5LXgXeG5j\nknlls142ixZKNjmDVer2aHoWXiijSa10tRl5L5AmCzAMOT230SBAQPZrB5bH\n9SwL+KUf1r066UL+UoCBMdqAX2g5XmFUG2KECTihF4e5kIF5ADAYc9vm6Er/\nP70MdLllVVWcCVPkpc/nYuFUofR8Ux44gxbNgMbVY05tKuJaEcE39ROk7QJG\nuzZOvO2VWQAL1vtttCQ/BXY8Qr3IJ80ya7ktUnTlsUW0uTMKGfs92lH4aZo8\nFHP1YhL6CCCjgi57CkPmjY0CYD6j36odSgMvPaRbIoHkMQOowRo/XQ1adDl8\nPB5C0Va/+BslY6mEKz2ot4Q8VZWJd+TEWAn0rMVYrIv3A951ER0wMzre77TU\nWIWm\r\n=iaUV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "gitHead": "d288a1896ea7601f4a74ecbb086e8917c370367f", "scripts": {"test": "xo && nyc ava test/keyv.js", "coverage": "nyc report --reporter=text-lcov | coveralls", "test:full": "xo && nyc ava --serial"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/keyv.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Simple key-value storage with support for multiple backends", "directories": {}, "_nodeVersion": "12.11.1", "dependencies": {"json-buffer": "3.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^2.2.0", "nyc": "^14.1.1", "this": "^1.0.2", "coveralls": "^3.0.0", "timekeeper": "^2.0.0", "@keyv/mongo": "*", "@keyv/mysql": "*", "@keyv/redis": "*", "@keyv/sqlite": "*", "@keyv/postgres": "*", "@keyv/test-suite": "*", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_4.0.0_1573883258440_0.7027583669283106", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "keyv", "version": "4.0.1", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@4.0.1", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/keyv", "bugs": {"url": "https://github.com/lukechilds/keyv/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "9fe703cb4a94d6d11729d320af033307efd02ee6", "tarball": "https://registry.npmjs.org/keyv/-/keyv-4.0.1.tgz", "fileCount": 4, "integrity": "sha512-xz6Jv6oNkbhrFCvCP7HQa8AaII8y8LRpoSm661NOKLr4uHuBwhX4epXrPQgF3+xdJnN4Esm5X0xwY4bOlALOtw==", "signatures": [{"sig": "MEYCIQDl3nIpOpGKweq5F5GmtmHMRvDo/8K+w+oM2ntCyWrHaQIhAK6FT5P9RZsP0tZvU/P3tCTzqjnr3iqNSuS2KwSN0KKx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15463, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesRDUCRA9TVsSAnZWagAACvIP/3Qp9fwwplef1Z0afrT1\nvUwQOoA2cOf0iPqIkLudMfj4zRuArljSEHJOzWbMIPuiZvnQHfpadUoaxsg2\nw18HSyqv3FJu4oxsm3Mhcyso5/B8bRPyYR+ELkR2i8scwwyuIyyYLpWjThSC\ncyQcVH27kQ0i1JEAIRhq8niwX4L5Jyvbi/7gE01k1gTgxSrx22mJXGp2/Uvh\nRbSGgXlcW3qO8T5EvQKvmv4FejHJD3maGv9zOJHbqafZkrjXmwzSscFH0fJ0\n9gFYUxRogLfN3BxZMXZaTJCSu9jTL1hhV64+7cg+8EYPE6xw2FQDT2LSShur\nXF/Ow6jwI3sK3p134z7vv3ix9azNd3wPPCbmfx/jQCYf4lwUuVC3sSsbttQc\nS8A9Nsn944xVd4LatLCLMBB15mctQsXK2xcHYgqYDoeG7DsIdSVMaRePDp/b\nK62b02x+VtY6uFCt4hUTqFMJvGCF7TXBxr6pS3uS14TCFtEuU8Z3UovNjqJn\noUSX9ZOwdqc18Iu5EyGaCzQ+k+e9CcHLPZps+5vuxE+zNAKBwWnZf8wErllD\nHR/FKmjQHrcqNR1NTOuOOBrx7HE9pt0cAyUGVo6PCBLWcsk4uY7R3e7GFJAb\nZj+YlmclGqlCK0OoWSihTRMq2quVXfPsT3jbJs3wwBFQ56cfnEV/Wt3ZdfjD\n0e/y\r\n=P2ce\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "gitHead": "4cea2ce404713e6a8385fbe35caab77ada7d6735", "scripts": {"test": "xo && nyc ava test/keyv.js", "coverage": "nyc report --reporter=text-lcov | coveralls", "test:full": "xo && nyc ava --serial"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/keyv.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Simple key-value storage with support for multiple backends", "directories": {}, "_nodeVersion": "10.20.1", "dependencies": {"json-buffer": "3.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.3", "ava": "^2.2.0", "nyc": "^14.1.1", "this": "^1.0.2", "coveralls": "^3.0.0", "timekeeper": "^2.0.0", "@keyv/mongo": "*", "@keyv/mysql": "*", "@keyv/redis": "*", "@keyv/sqlite": "*", "@keyv/postgres": "*", "@keyv/test-suite": "*", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_4.0.1_1588662483597_0.4135374693839484", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "keyv", "version": "4.0.2", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@4.0.2", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/keyv", "bugs": {"url": "https://github.com/lukechilds/keyv/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "586777fc38c28badebd4d9cb5e000c09a2bc2777", "tarball": "https://registry.npmjs.org/keyv/-/keyv-4.0.2.tgz", "fileCount": 4, "integrity": "sha512-EApxc1YZC/Uf+8dsrGqDqUK3cW8oPLgyeATzELkyAFE9OTf2jpFbi2qLtlrONy+SMmupkR3p5jGWeMEd0Z/ZHg==", "signatures": [{"sig": "MEQCIHerDaWyHZt9U3zJytTgDU//owLZ6v5kJfo9zDTGkmklAiALjhAo7a3TTCYUsjnPxOupdfnxlrJXeQTDyU4TYsJXkA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15561, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfYfMcCRA9TVsSAnZWagAAF6QP/RUa2Ew6D0k7h2kVwGG1\nBUMKFAnpZtGa11Z6cb1QInOsMZXHwwnYZhf7RhOe2KtniIJ0UrQ1uyPEb+dc\nzHuuH8WH0h4hnn5FPH2TQGzp5kUhwMX9qQO4JUvINq33lNhESfLk5oOrkIj3\nodny7QMLSm1u2JID0Dfzd32GJdnDljDYIOjTQk7f69femipP0I7HE32h72yk\nW7ziEsok9hnsqhGIDGwEvZ6dnNpMNr2GGyDzV15U/US+oMuTCu6PFemBpOdD\nMvoDyz8jp0A0MZKdfiSRfcMof62rlP8iBkRyPWBUie0ttLaqeF0DxwaeGRl5\nN7ls0TIwHvQF8gIlz8yMXl+WT1mqxjHsHXOwenYeWg26ChrdPl0ZUm6tiipE\nXdJ7yoLwi+b9UXuctquQ9u3eFsz7Y91aZDAyF2AbWIxbXmZAjVBebtl5nxsZ\ne6z04fdhuk5CPo3IuT+ptwbtPUhs3Lywli6xeATT+m5zKT+8qlxzjxeMVzB6\n0qyBwhdXR9EYoF9c2GI23CQlLno/xDxs/hjdMGhjsikjz2X7F7cB0zQUe8ce\nkE4HatQ2AiVNVLOPpOLNu+TTIutBhyoU2hTEnfOimSI3F8QVYUv838Z4Hh4N\nn1N6YeIN49VgBzgf1FY7DexSJOjVhO6I7R1CoqDxuK8AMyApRTwuiRP5eMSL\nB/3j\r\n=VSq8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "gitHead": "e297e27d07007e962024d3cdb7046c99109f3aee", "scripts": {"test": "xo && nyc ava test/keyv.js", "coverage": "nyc report --reporter=text-lcov | coveralls", "test:full": "xo && nyc ava --serial"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/keyv.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Simple key-value storage with support for multiple backends", "directories": {}, "_nodeVersion": "12.16.2", "dependencies": {"json-buffer": "3.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.3", "ava": "^2.2.0", "nyc": "^14.1.1", "this": "^1.0.2", "coveralls": "^3.0.0", "timekeeper": "^2.0.0", "@keyv/mongo": "*", "@keyv/mysql": "*", "@keyv/redis": "*", "@keyv/sqlite": "*", "@keyv/postgres": "*", "@keyv/test-suite": "*", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_4.0.2_1600254747563_0.970572232650732", "host": "s3://npm-registry-packages"}}, "4.0.3": {"name": "keyv", "version": "4.0.3", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://lukechilds.co.uk", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@4.0.3", "maintainers": [{"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/lukechilds/keyv", "bugs": {"url": "https://github.com/lukechilds/keyv/issues"}, "xo": {"extends": "xo-lukechilds"}, "dist": {"shasum": "4f3aa98de254803cafcd2896734108daa35e4254", "tarball": "https://registry.npmjs.org/keyv/-/keyv-4.0.3.tgz", "fileCount": 4, "integrity": "sha512-zdGa2TOpSZPq5mU6iowDARnMBZgtCqJ11dJROFi6tg6kTn4nuUdU09lFyLFSaHrWqpIJ+EBq4E8/Dc0Vx5vLdA==", "signatures": [{"sig": "MEUCIHMT5laGaoN8J8+j0Ja/6YVlvjH63Kf2QqsOl8kj81VnAiEA7KKexmrr0tRNybO5Xvt1xrcpydlhH2CDtGPaX2nxC5Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15463, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfYzzSCRA9TVsSAnZWagAAuAsP/jXztRmb9yzLmDptWPW+\naRCUFlcudjusxd3hVSL6RtBdXFtCkUi9/rFCYkAYvn3Ykgai63W5LX3C2Ew5\nWfZhP8h+yWFb8m4VYqnUyhFWsc5XGwmLL/ECAkNzQ9UOb6f0+5fySOjh8d0/\nrlDWExY6LkUm/a5OHJI5r7pd4c3DuCJTVc6ewE9HTyH3nKiNncQQ20J556PX\nNyVm7B6nPajM+e/26wtc95hEaruJu2WGUNm3mxEOnZAU1nIAeJkrVxfM0xjE\nPwXtfQKmR++MO<PERSON><PERSON>zZJ9ZCCp7M2djJIzGyXC02a/LsePorNSXV7fU6Ru7huK\nNz3U2Hx04wMPot2YNrdOXeHFvGqHjf/smLp7xIaN5Mk53J7MNX3DI5qI+BpP\nDBC8SsI1vLhuH/SX8cZgiGXVn3SMA2y2ts2UFjOdIYwMMjkunH/CXh3exLus\n+jdonWdu1TopZl40+098AJbIJtUkfufaHwA1rGKHJc4a5mAICTLMdlSN5ruq\nH1ApW0GZxv7OhJzhXCQRdTRaYzvimhGIYIWH+5nGKPmwAp5XMXsUnx69u9kD\ngolP6tnu0u+mJQ5KKIlMvICK/i/FHIZgKeDMq1zGv5eSmlTF/saHlyFFEZsG\nsZeofpnjB1bXJnLHsgHGN9T4TTq6+FalfcgMQMp5QoGCElI/76len3HodSWI\n0eRQ\r\n=fXg0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "gitHead": "ab99cada0186e6dc83420ceaa85919d1bae871dd", "scripts": {"test": "xo && nyc ava test/keyv.js", "coverage": "nyc report --reporter=text-lcov | coveralls", "test:full": "xo && nyc ava --serial"}, "_npmUser": {"name": "lukechilds", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukechilds/keyv.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Simple key-value storage with support for multiple backends", "directories": {}, "_nodeVersion": "12.16.2", "dependencies": {"json-buffer": "3.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.3", "ava": "^2.2.0", "nyc": "^14.1.1", "this": "^1.0.2", "coveralls": "^3.0.0", "timekeeper": "^2.0.0", "@keyv/mongo": "*", "@keyv/mysql": "*", "@keyv/redis": "*", "@keyv/sqlite": "*", "@keyv/postgres": "*", "@keyv/test-suite": "*", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_4.0.3_1600339154163_0.2801113233468182", "host": "s3://npm-registry-packages"}}, "4.0.4": {"name": "keyv", "version": "4.0.4", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@4.0.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/keyv", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "xo": {"rules": {"unicorn/prefer-module": 0}, "extends": "xo-lukechilds"}, "dist": {"shasum": "f040b236ea2b06ed15ed86fbef8407e1a1c8e376", "tarball": "https://registry.npmjs.org/keyv/-/keyv-4.0.4.tgz", "fileCount": 6, "integrity": "sha512-vqNHbAc8BBsxk+7QBYLW0Y219rWcClspR6WSeoHYKG5mnsSoOH+BL1pWq02DDCVdvvuUny5rkBlzMRzoqc+GIg==", "signatures": [{"sig": "MEUCIGPGzY+AtKeOz6ijhwFntnmTVzVJZt+URgTIKN61flY+AiEAs1kdZX7KUo1ekDBAXGLq07Dm0afPaxa9I+6kJ5prCPk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16124}, "main": "src/index.js", "scripts": {"test": "xo && nyc ava test/keyv.js", "coverage": "nyc report --reporter=text-lcov > coverage.lcov", "test:full": "xo && nyc ava --serial", "test:services:stop": "docker-compose -f ./test/storage-adapters/services-compose.yaml down -v", "test:services:start": "docker-compose -f ./test/storage-adapters/services-compose.yaml up -d"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/keyv.git", "type": "git"}, "description": "Simple key-value storage with support for multiple backends", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2017-2021 <PERSON> & <PERSON> Childs\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"json-buffer": "3.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.46.3", "ava": "^3.15.0", "nyc": "^15.1.0", "this": "^1.0.2", "codecov": "^3.8.3", "timekeeper": "^2.0.0", "@keyv/mongo": "*", "@keyv/mysql": "*", "@keyv/redis": "*", "@keyv/sqlite": "*", "@keyv/postgres": "*", "@keyv/test-suite": "*", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_4.0.4_1635719954583_0.7519637662417311", "host": "s3://npm-registry-packages"}}, "4.0.5": {"name": "keyv", "version": "4.0.5", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@4.0.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/keyv", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "xo": {"rules": {"unicorn/prefer-module": 0}, "extends": "xo-lukechilds"}, "dist": {"shasum": "bb12b467aba372fab2a44d4420c00d3c4ebd484c", "tarball": "https://registry.npmjs.org/keyv/-/keyv-4.0.5.tgz", "fileCount": 15, "integrity": "sha512-531pkGLqV3BMg0eDqqJFI0R1mkK1Nm5xIP2mM6keP5P8WfFtCkg2IOwplTUmlGoTgIg9yQYZ/kdihhz89XH3vA==", "signatures": [{"sig": "MEUCIAdC26QLuWspy1OvqbssJCKStIKifLvkS7EwPwFOfyHDAiEAsT24dmS/n+nQh7q7czLTaC3+Mn2xu8Y0hxXRKHOaFNI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41993, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2brrCRA9TVsSAnZWagAAjk0QAJgI9WKba8af+z38vMEU\nQHbA1ECbhOYMFXjK2SKx3XgBhHmaCNkdxPnQJbna8i1tHbcT7/j2qnYhShyH\n/wH81b+zS1WkzdzMMnSwU+HMV0DcbWu31rqSnXhWitqZHPQvzQtI+UD2d9SQ\n1tt7fn1EQjZ0KD/ZbkPsYpBird4xYaPQkEUzpqtQXG1b38vB8xqha21ufLeX\nwi5adfg2lvmvtxw1W9kffcTyU2J4fIgFqh8isCcxlyBVUC28w4zI7nLlTqag\nt/ZB/b+ADfajTA29wih6vSfXXVWuICUnrqHNYUSe/wjAyBsltyNSveSShxN1\nWhBSbq2XhdOqlmg6lg9LeUv0sRHeJ0SfLUt9sAVdGoUOvlOybtZWoanHU1TM\nipq4v8tqUNubK2qygjXz+uUt/D8IQ+i5R/AS4djG6zTjsQBu2cLD4c41RcBS\nHV/w+XFmTMHm6wguPqmjaTpLqb5408doyn1Xby3nnQT5d6cC2bYYhenJaiAA\nge0n/3zbYuiNkOq1TELTJGfO9LrmcJv9PKL6jJy0U1dmH6C0YTjY49+lNWe7\nAEmdbp8O1rnxWRGB417G82cFkWcODkhgtSJSqo/xbMybR99AKPzW017OpkF+\nE8fZJs8PsPf0sgX0oKUcE0/9+s3jjYd/AskR9e9GzqiVECcvm6j/CIuurOq+\n3qAM\r\n=vSUn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "scripts": {"test": "xo && nyc ava --serial", "clean": "rm -rf node_modules && rm -rf .nyc_output && rm -rf coverage.lcov && rm -rf ./test/testdb.sqlite", "coverage": "nyc report --reporter=text-lcov > coverage.lcov"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/keyv.git", "type": "git"}, "description": "Simple key-value storage with support for multiple backends", "directories": {}, "dependencies": {"json-buffer": "3.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.46.3", "ava": "^3.15.0", "nyc": "^15.1.0", "this": "^1.0.2", "timekeeper": "^2.0.0", "@keyv/mongo": "*", "@keyv/mysql": "*", "@keyv/redis": "*", "@keyv/sqlite": "*", "@keyv/postgres": "*", "@keyv/test-suite": "*", "eslint-plugin-promise": "^5.1.1", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_4.0.5_1641659114940_0.02990875275840943", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "keyv", "version": "4.1.0", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@4.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/keyv", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "xo": {"rules": {"unicorn/prefer-module": 0}, "extends": "xo-lukechilds"}, "dist": {"shasum": "8ab5ca4ae6a34e05c629531d9a7f871575af0d5b", "tarball": "https://registry.npmjs.org/keyv/-/keyv-4.1.0.tgz", "fileCount": 17, "integrity": "sha512-YsY3wr6HabE11/sscee+3nZ03XjvkrPWGouAmJFBdZoK92wiOlJCzI5/sDEIKdJhdhHO144ei45U9gXfbu14Uw==", "signatures": [{"sig": "MEUCICYKdE0jJPArP5v8o0K78P3WPtX9ejEazG58LOm2L/+UAiEA3krMC83tpiJb1DP7RY6HXUnov8BmNqvLqLrK/CvywzM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48103, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+bLUCRA9TVsSAnZWagAAlLcQAJlTD4OrQ7TG9zyY3VmP\nxrYVLExXFUupgzFu3K8HQGCglEBzpcp78LLYGJ992ONiCkvTC7JWSjLGQW1S\nMfePYuogmrFpErOl2ETupmeRXqfiiXGg7lLcrmjERXXZGMrhEpeUDlA0W45M\n3CdRtS4dJ/PGmIMTBrg5X1mZaGEv12+t/kOoZN2fi2AJLyfhSBQZSA+SXPi2\neRfeIcGoEEUA09JgrZ5Jg6J/8BdyWF2IAamoUnlRwHQhgcSID3CgJiPJvh+5\nkcxJyx6ivPvjSXCs/BeuripADUI5PeimgW/B9Z0xOAi308Ptpi1DoR1XgvPY\nSYlYmxSAKTZxY6I46SNMqxNKEFGVgEI45UIqrY9U42Zs64MozTMrK4JKzypa\njmG+64t0btnZNTw2qJDuGmcAnfXmL2A8bUa3CSANzJLV1KOkut13O1tNrW+P\nMerqhF516UjVyttd7/3xHwo0rRENCKHTwX18iJBGRsol7yiTSFWXQFWiYhBX\nyNJsHL2PYobUSl6rem1Qv7G0XJnszbOVzFHzTcKQjgWetjhlqKq4EQXGNbl9\nHbtLx4eh8CQNXmiX0i8YYmGgw3zwEb4+8LTpG2857yTviJQratQXn45i/q80\n/PqfdJGcd/+loIB7nUl8YmNuG90tBqk1Vs0A5IApkuaVJ9AXrFpoYBXp8pe9\nSq+n\r\n=MrEs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "scripts": {"test": "xo && nyc ava --serial", "clean": "rm -rf node_modules && rm -rf .nyc_output && rm -rf coverage.lcov && rm -rf ./test/testdb.sqlite", "coverage": "nyc report --reporter=text-lcov > coverage.lcov"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/keyv.git", "type": "git"}, "description": "Simple key-value storage with support for multiple backends", "directories": {}, "dependencies": {"json-buffer": "3.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.46.3", "ava": "^3.15.0", "nyc": "^15.1.0", "pify": "5.0.0", "this": "^1.0.2", "timekeeper": "^2.0.0", "@keyv/test-suite": "*", "eslint-plugin-promise": "^5.1.1", "eslint-config-xo-lukechilds": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_4.1.0_1643754196120_0.3170710386536675", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "keyv", "version": "4.1.1", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@4.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/keyv", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "xo": {"rules": {"unicorn/prefer-module": 0}, "extends": "xo-lukechilds"}, "dist": {"shasum": "02c538bfdbd2a9308cc932d4096f05ae42bfa06a", "tarball": "https://registry.npmjs.org/keyv/-/keyv-4.1.1.tgz", "fileCount": 15, "integrity": "sha512-tGv1yP6snQVDSM4X6yxrv2zzq/EvpW+oYiUz6aueW1u9CtS8RzUQYxxmFwgZlO2jSgCxQbchhxaqXXp2hnKGpQ==", "signatures": [{"sig": "MEYCIQCAqFSbdZFJxccbb5E9GDWeC/xkV6ls/sBb4CeI5j2TOgIhAO+KFy4Uwt4xLDlpVf0PVq6pFRSr+2GMndBlDfemQE5Z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47348, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAHA6CRA9TVsSAnZWagAAU30P/RzdGJFOUykotHiMYhmg\noNAhcvoyaIZ5rJXK0ibT3VkAjAY8TVLF1E6fSgwVftJY6F/l0fM2vmO/0L07\nOoL4z9hep8rECWJUUyumyg1PjdzpNm5H4x6IsUBGTnZ+l7bAmK9Scx3JNEUl\n9JB/NIg/PTN0hLPcSDDhAdfy9qq8qGJAoRttkJzPHNa5crcMjmdwmZaE6Vet\npqBlYo0QWMwwBg4oeJlkBGcZOW2/j91vFDHo7B07NqpF2waY/PYDXO6MNoY3\n1pu//ecf9ojjwo9SAg+q0LexbxESRXM90+6MG/DgJ9xK3zEl2+PMayMosKt3\nURcZXADhPiu0QVMAZyli8uDDxhzh+KnS1HcPkCFDr2KXvIHH9hvR69iwfmqo\n4Z5yEu3ZR+EqZSB+Ear3xWR8TFm3D+iWQd9NGbK969FWkeJZ1w9Va5ctBn7v\nhx58w8ok4GJ7svqOT0jJFNjgG9Ks7qe5pdXPaP9Gw2nrFIgnjHpZP12tOaOt\nSkStQdixAxeOS90tt2CG0APfcWHfSafhX6/Tbd2AgIyccM7xYiGvx2M0UXPN\nelp6eQrtVbeWvsG04Xw7EVNtAg7MExi4s0f2DvLuWyZrc9gCicRNqG/vgPMY\n0StTQEhjRHi6DwGK/YMtDRNZSVQhtkBxUIIp30hnEEWwbqA2Z6Bpuy0dJFJn\n3FqO\r\n=YT3k\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "scripts": {"test": "xo && nyc ava --serial", "clean": "rm -rf node_modules && rm -rf .nyc_output && rm -rf coverage.lcov && rm -rf ./test/testdb.sqlite", "coverage": "nyc report --reporter=text-lcov > coverage.lcov"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/keyv.git", "type": "git"}, "description": "Simple key-value storage with support for multiple backends", "directories": {}, "dependencies": {"json-buffer": "3.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.47.0", "ava": "^4.0.1", "nyc": "^15.1.0", "pify": "5.0.0", "this": "^1.1.0", "timekeeper": "^2.2.0", "@keyv/test-suite": "*", "eslint-plugin-promise": "^6.0.0", "eslint-config-xo-lukechilds": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_4.1.1_1644195898571_0.07875407806992607", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "keyv", "version": "4.2.0", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@4.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/keyv", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "xo": {"rules": {"unicorn/prefer-module": 0}, "extends": "xo-lukechilds"}, "tsd": {"directory": "test"}, "dist": {"shasum": "accda690a99bba7b691cfeaef241937839b2e42d", "tarball": "https://registry.npmjs.org/keyv/-/keyv-4.2.0.tgz", "fileCount": 6, "integrity": "sha512-mcRm0yqY2Z2FdE3Tkb7hNEUN7J7VdedNZ8F6vS5jX04gNo2pyOWqfyW+chW9amiS3gbULPucyRzVq0gjPUmhTA==", "signatures": [{"sig": "MEUCIQDuPCiGiAtln7n08diFBHMxDpiLfZ5AI5hq58ymokGhuwIgPAXcGk33xaATYLsav/lQRdRy1yJjmpZiF/wQ5Ab9fMQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22612, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiSMG7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrKwQ/+IyahdiDkVOE1TY8nzr0vQLtvKr0ZJKd3bru7jyTQQ7oQF5YL\r\nSOvfAe5I4mmHrFV9Dm1NGS95lhVIwgjcZNbbK7AYeP7T4XrEhPSKUpzm2TNp\r\nPaYPGoBaCe5e0EDj2y4pvNK7w0gnLciTlZFmXj3dPZ7tFJ38RG9jePibXAZj\r\n6DnIabFLj3Z99UA24TqfBv3en6rZ3SxDsCL/UreXIj3U2Q/KoVg4Duw99j8E\r\nyEkaObMwmY0eHDu7HSJ7VQqRnHzFtIXu8jbFejGeEBeM3bwKf+GRASRkcnES\r\nhyyH0MkHuES8E98iTq6X8KTTSxf/3efkeEDB9PdwJyUioS85j1zKYzRq6IsS\r\nAzfEmo7xwEY8AMvcfpeCJztNlIwc+HV6b51NL4vc8HJaCTN7t7bx29eMHGL4\r\nmt7NOJaiePWpYHAv3dZkbE5pGsUL6bsgn11gtIKQYH4fKl7Ppzi9r2ULKxqT\r\nuSDE9VbQfPGz56IFFx8u26OdLEcqY8dhebOXNsF2w1iDSR/c4YapF5nezfN0\r\ndAWD7+RMhCbNmahdF0tPzBTgpY6iywr6L4CtUeU5E/DrPxS8bjkhN2JnvI4Q\r\njPMrWJFQXUv0GREzu6LOW4NNEdFr1uFoiuRs6ByXwxAMKEH4uec3WwT9oP0j\r\n4Vc6GH/4SQv2T9JbMgxqWHrUp6YjKspBh8U=\r\n=nLY7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "types": "./src/index.d.ts", "scripts": {"test": "xo && nyc ava --serial", "clean": "rm -rf node_modules && rm -rf .nyc_output && rm -rf coverage.lcov && rm -rf ./test/testdb.sqlite", "coverage": "nyc report --reporter=text-lcov > coverage.lcov"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/keyv.git", "type": "git"}, "description": "Simple key-value storage with support for multiple backends", "directories": {}, "dependencies": {"json-buffer": "3.0.1", "compress-brotli": "^1.3.6"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.48.0", "ava": "^4.1.0", "nyc": "^15.1.0", "tsd": "^0.20.0", "pify": "5.0.0", "this": "^1.1.0", "timekeeper": "^2.2.0", "typescript": "^4.6.3", "@keyv/test-suite": "*", "eslint-plugin-promise": "^6.0.0", "eslint-config-xo-lukechilds": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_4.2.0_1648935355576_0.5056621272509279", "host": "s3://npm-registry-packages"}}, "4.2.1": {"name": "keyv", "version": "4.2.1", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@4.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/keyv", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "xo": {"rules": {"unicorn/prefer-module": 0, "unicorn/prefer-node-protocol": 0}}, "tsd": {"directory": "test"}, "dist": {"shasum": "61c836fc3cdc9d73d9292b09965210f394588903", "tarball": "https://registry.npmjs.org/keyv/-/keyv-4.2.1.tgz", "fileCount": 6, "integrity": "sha512-cAJq5cTfxQdq1DHZEVNpnk4mEvhP+8UP8UQftLtTtJ98beKkRHf+62M0mIDM2u/IWXyP8bmGB375/6uGdSX2MA==", "signatures": [{"sig": "MEUCIHu1XUTszSRvJjQlWhUBqkFqL6kakk1WNxs/2G4ob+VYAiEAjt+944iJd477odrLF56Vt1yEiIAC1viQgefpTHJqAVI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22584, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiSiPCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpIHxAAoQ0mTETwAzut48ox2FdYx4iKN+bV9TyulTkCEd6EVPYOmbOF\r\n8nEg3OTv/T/1SzgrdA650iZ0mzMNy1IqhM5Vota2yNHFpMjxnVtmCgSyfKJ9\r\nPMdvf3zIkpgbi5b+Heg0QMxHiE/e8FMRGzwO11qweUACDHWV1JnSnmaNJfBK\r\nEeTWvKGd2oyQ5RPAGcpIknfKQA0v46gEx4JhLNJI0LDH9GJ/fF9w+aDYUegG\r\n5leYzKstwrj/n0B1BskScpJoUMy8/rtZ7UANN+9NYwsBApDJmIWWx3NDIapu\r\nAwvbz0pspEwSf2UxrVp394d3u9a7QW/GKIOs1fQHUSgUkdXGc3xcLLem0R6z\r\nR1aCDT3CqHivxTSue5QRMn1goJpgNG/bT75+y0HJneNcllQeks4oU3V59aUP\r\nkP1Ry4vTzV2YV5fPBK6ArJgCoEMwaqPTQRFwa44+s0iG5Yjz7uOEBVbhq9kf\r\nGD4aDEZrVcQn7dnESHNv8TdTlVuMQL9utyAOzrg4Tb5/iEcmbNJ/pxPUnN4l\r\nkfwd+HIhADi3Kqktua+0jt8BKuiGKd/gz/F+pE8VJ5/+e0h7Zhe9fdUcjTCu\r\nqymbaS+M2CFskJxTJqIhwBOXkXlxkJqFd4KvXmrdHrbOnGRTbNe+fyrTILuH\r\nbzerrXydP2un4Vph468KcTG0iZoMQFcQtlI=\r\n=f9aX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "types": "./src/index.d.ts", "scripts": {"test": "xo && nyc ava --serial", "clean": "rm -rf node_modules && rm -rf .nyc_output && rm -rf coverage.lcov && rm -rf ./test/testdb.sqlite", "coverage": "nyc report --reporter=text-lcov > coverage.lcov"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/keyv.git", "type": "git"}, "description": "Simple key-value storage with support for multiple backends", "directories": {}, "dependencies": {"json-buffer": "3.0.1", "compress-brotli": "^1.3.6"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.48.0", "ava": "^4.1.0", "nyc": "^15.1.0", "tsd": "^0.20.0", "pify": "5.0.0", "this": "^1.1.0", "timekeeper": "^2.2.0", "typescript": "^4.6.3", "@keyv/test-suite": "*", "eslint-plugin-promise": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_4.2.1_1649025985947_0.7004534075141942", "host": "s3://npm-registry-packages"}}, "4.2.2": {"name": "keyv", "version": "4.2.2", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@4.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/keyv", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "xo": {"rules": {"unicorn/prefer-module": 0, "unicorn/prefer-node-protocol": 0}}, "tsd": {"directory": "test"}, "dist": {"shasum": "4b6f602c0228ef4d8214c03c520bef469ed6b768", "tarball": "https://registry.npmjs.org/keyv/-/keyv-4.2.2.tgz", "fileCount": 6, "integrity": "sha512-uYS0vKTlBIjNCAUqrjlxmruxOEiZxZIHXyp32sdcGmP+ukFrmWUnE//RcPXJH3Vxrni1H2gsQbjHE0bH7MtMQQ==", "signatures": [{"sig": "MEQCIEbUO8uAlfK/kt2egmoz67YuEgCfGYKg/zwGLFNJ2EZ1AiB1+UFygvItxk15fyggY9NExlBq/DbOhehxEDx6IExQLw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24868, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTibsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmolJg/+ILXXEtRnDMpZSWnLN4vHWcBGeQGYUkj1jyTi5SSQD900R7vx\r\nql4WS7PSy8NG9XctcCoWjFR2MlD4VWdYV4qd6Oi1XDcKX8LD0fh2Bm1ci9eu\r\nMytoToJ9vAK3qXRjBUg3+IF+nPfFk00oRaYYVdl5ExBhl8L0VgjMUXnMT8F9\r\nhhZ7/HBlk3IuXKuUPpi1UVZ3CLPQPNMzHf2y8O0kmAfdHcCOs3nDWULApSQm\r\nyf4tYdMWFkFGvU4jjoOG+Lsb/XEuGIeSDfQDf2dJyVF9CGIeRmQo9NXgldqW\r\nmXcLowAFDvY7oJ/Vrk5OBv3SMgm8QmgApldjatfVialTvLkBZAjMBwWrTWJF\r\n5wIhimM9No0LsnD6DmDefFYtVqkD//k4VQdDUMiQ2EOIT4aIvFYUh8dt297U\r\nJ3Rsgluq8Dv6lpNPJltwNNFQ18jALVhF6trWaRK5GtcBAZVGkqjTvSQTPc3h\r\n7MjIkSefEeljdXDf1WTHbD/njcZWhO/Qt89UH6GzrBt+UYPhxi1JE35QYcuL\r\n9ypngEwJ/HbD8xY0tep5LMrYH6KpZJu1W1Bom3MxYV1QojFV+fGkr+hcWMpk\r\nOl+wzp9/zmp68Q+yFGHHDIDZ0kitiHxok0mq5OtPfwtVchV096Pk0xkead/l\r\nx8ku9rW/QgCB9y+puzzVXKa+bLLXTM4ewcQ=\r\n=fwZ0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "types": "./src/index.d.ts", "scripts": {"test": "xo && nyc ava --serial", "clean": "rm -rf node_modules && rm -rf .nyc_output && rm -rf coverage.lcov && rm -rf ./test/testdb.sqlite", "coverage": "nyc report --reporter=text-lcov > coverage.lcov"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/keyv.git", "type": "git"}, "description": "Simple key-value storage with support for multiple backends", "directories": {}, "dependencies": {"json-buffer": "3.0.1", "compress-brotli": "^1.3.6"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.48.0", "ava": "^4.1.0", "nyc": "^15.1.0", "tsd": "^0.20.0", "pify": "5.0.0", "this": "^1.1.0", "timekeeper": "^2.2.0", "typescript": "^4.6.3", "@keyv/test-suite": "*", "eslint-plugin-promise": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_4.2.2_1649288940148_0.7440416401656218", "host": "s3://npm-registry-packages"}}, "4.2.5": {"name": "keyv", "version": "4.2.5", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@4.2.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/keyv", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "xo": {"rules": {"unicorn/prefer-module": 0, "unicorn/prefer-node-protocol": 0}}, "tsd": {"directory": "test"}, "dist": {"shasum": "ee4ec65995800ce86de2d4d90ceeb587a14fb138", "tarball": "https://registry.npmjs.org/keyv/-/keyv-4.2.5.tgz", "fileCount": 6, "integrity": "sha512-WQpVVZvax2BpGIXviWPnBuwbTPd3qcMcmsWzJJWKyV5r3JUw5kEkbs5FguywUxd+Oi4OpuSmDQE5qtH51u9Hqw==", "signatures": [{"sig": "MEYCIQCZXPqXgzxEEryv78aFx1HuD6nOlBAk6Wk2ZK59C9f1LwIhAJcNSb100fj2qMFjJ4CNGqIkuF+uKu9a+TJwlyKJRFTB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25285, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiebIrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp2nw/+OUuJGvB2OnZXJo27BpZ+euWClwOrzy6L12q/FzkL6hmk7CQW\r\nJau/U7iIX8usGfVInL5dqf9G9VO7Wryb4gKLjCdZP7arr2jHlqvbbsK3hnfV\r\n1it/BPNM0EFoBAgd0oHQzGFgMkyO8Xza8cw1HMiKBs04vH8gu+pKdo1C+PZO\r\nQ2rb7/G7XngH6hUqz6eO1z2yb3xqJUiTE2jn+7R2DCMhF2v0hmj0z2hP9Ih3\r\n6afAbD0pGXMEm+2lLiod6JEcXg+NVvwY+zWOzQJXOoZtHqltpQvK07W6Ky+n\r\nHAxBrUMfTBsZwM+LOfAn0kDc5k+oZrMs5gaSdX1wwaok6avcjzE+HS184p1B\r\n6yX/qpeNlJPRyfVHdqfez2b5w7rC7yYPQmh/gIv5THOXQ5RCoqmQlGgvPaJx\r\nWY8MiVuF6kgMWLvBWuGEjdLlDjTIG/CXebQ5fi7+5NI9f4qjo1gxhcO3ahe4\r\nI7duExjaheCgn+q9HVimQrhds6M0wEgO2SBnZaUgE1sMH1WQAMrXW7a9RJWX\r\nQnTMAvTj23l+zZ1+KhESJDKce5T2ONtpSBdKAK7be7QsmeErcfyoqvHql1/w\r\nRQqTgJFgtdAldZTpZ87v+X8RFuhanUVWWU3TzkdW1wq7g/dUZOAh8NNzjumK\r\nt2UK+VIXut2wonXGrka5xInfpJqeZuhbG7I=\r\n=md1M\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "types": "./src/index.d.ts", "scripts": {"test": "xo && nyc ava --serial", "clean": "rm -rf node_modules && rm -rf .nyc_output && rm -rf coverage.lcov && rm -rf ./test/testdb.sqlite", "coverage": "nyc report --reporter=text-lcov > coverage.lcov"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/keyv.git", "type": "git"}, "description": "Simple key-value storage with support for multiple backends", "directories": {}, "dependencies": {"json-buffer": "3.0.1", "compress-brotli": "^1.3.8"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.48.0", "ava": "^4.2.0", "nyc": "^15.1.0", "tsd": "^0.20.0", "pify": "5.0.0", "this": "^1.1.0", "timekeeper": "^2.2.0", "typescript": "^4.6.4", "@keyv/test-suite": "*", "eslint-plugin-promise": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_4.2.5_1652142635213_0.4050488551993563", "host": "s3://npm-registry-packages"}}, "4.2.6": {"name": "keyv", "version": "4.2.6", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@4.2.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/keyv", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "xo": {"rules": {"unicorn/prefer-module": 0, "unicorn/prefer-node-protocol": 0}}, "tsd": {"directory": "test"}, "dist": {"shasum": "d698f061e71bab3c5ab70e43907d7b8baa1bab9c", "tarball": "https://registry.npmjs.org/keyv/-/keyv-4.2.6.tgz", "fileCount": 6, "integrity": "sha512-mmMlAFwZnCBk7QDA23I49T5VP2NdSsbaT4yrm14cwnlQ67t+wHWPtYj+/gbzeAbcprGbUSzWP3jBE1WKfSERnQ==", "signatures": [{"sig": "MEQCIANl03Df/INzR8uOyUl5CFzARX5nYZt8Q/LU1n0aMfZjAiBtH+NI2evxhCWz/ulPGtkezdcshSS/fXZi/UpkrQQaZw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25247, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiee5WACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrvmQ//Yq18wO4b0vvDMdQfZaIXY5F0NfXc8Gg41OSJLBNAEVHoOvF/\r\n4uf4KBg8R2qBkfKoFsXsanEf9ynmvdCGO2EflUo9WvX32ihyOdFNhDNHq4OE\r\nBx4I0uFHXHczGuH8DLYiyIfwtVOqFKEdyHMUoZWJvzJLd6ushuKsx/ENpz8h\r\ndX3ryO288wFpbQ15Eipq1QnEAUNJKNUCb6gXpOMOgJME7PtNuL8mfQKXltiM\r\nlBmjCaCI/l8mqSnhakoIJSAK4WDK40Pl1Jt9e58Is4GOATkIrJ1qEmxPaOY+\r\nDP8iLwRAl1pbpNVSSNDpcy/9o8rdgMBLkh3TfgJQ1EslVRgTSz1wvDra7HNW\r\n/XCsLnxbcrlecEgav9q07SkAPwXX7ZLYdcCb+YDlhrOpvLXLggYwrfNNNVFe\r\n2WPccDK20CcZZa6ml1IQuvQR2uPepwVvLX1AdPEy5zDq+lvL3335F+RPZRlj\r\nVwoSK/FR+iSg2V2DLIdwKs+002Rb2hBAjWyaMPY8Pw536dwL50zET3FjZh7k\r\ntf5Pz3PW8Ornbbg1WFIVvKxwZ0rkDWrD73ox4iIp30ar7sP6F61z4q6ewTt/\r\nHP5k7c5OlnB3h9ZBRnFFxP1QXOYwEOwZ+UPcfkwTGzl+r/aZsTXEmNO2YpLj\r\nCcUbnmm4D9oKjF4LpsKFuo45+/SzsD1lb80=\r\n=iCWk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "types": "./src/index.d.ts", "scripts": {"test": "xo && nyc ava --serial", "clean": "rm -rf node_modules && rm -rf .nyc_output && rm -rf coverage.lcov && rm -rf ./test/testdb.sqlite", "coverage": "nyc report --reporter=text-lcov > coverage.lcov"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/keyv.git", "type": "git"}, "description": "Simple key-value storage with support for multiple backends", "directories": {}, "dependencies": {"json-buffer": "3.0.1", "compress-brotli": "^1.3.8"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.48.0", "ava": "^4.2.0", "nyc": "^15.1.0", "tsd": "^0.20.0", "pify": "5.0.0", "this": "^1.1.0", "timekeeper": "^2.2.0", "typescript": "^4.6.4", "@keyv/test-suite": "*", "eslint-plugin-promise": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_4.2.6_1652158038705_0.44174847868244105", "host": "s3://npm-registry-packages"}}, "4.2.7": {"name": "keyv", "version": "4.2.7", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@4.2.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/keyv", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "xo": {"rules": {"unicorn/prefer-module": 0, "unicorn/prefer-node-protocol": 0}}, "tsd": {"directory": "test"}, "dist": {"shasum": "00b8994d46098e8eb8c933cb29aaaf18be5effea", "tarball": "https://registry.npmjs.org/keyv/-/keyv-4.2.7.tgz", "fileCount": 6, "integrity": "sha512-HeOstD8SXvtWoQhMMBCelcUuZsiV7T7MwsADtOXT0KuwYP9nCxrSoMDeLXNDTLN3VFSuRp38JzoGbbTboq3QQw==", "signatures": [{"sig": "MEQCIH/KQkY5yl7ZiDS94/jmtVay/I0ea3tCNdYQhPA62tYiAiAmXckOdW9BQIO5aO6EAXhrQLOYm45fTT0XbU75JBsWDw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25370, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJieo4UACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqEThAAlVKKTiHIxIagznwSFPzIXz0zPOor5U8H6ojx3j2/ryajUSsB\r\nMUdhWi1lPTVCcHqc1A7dSUgRFPYebUDHkeWIqR3Lk/r9hZm2ROkeejKCH+3O\r\nLZ0ka6TzA5Cvfa3o8yvTobdfwSMZnEBwBjddrACw+bcfK5YT6DCj79xCX5sP\r\npspL4M3A008ePAxry1m6zz45AMXhI0M7J7NgCOSPfcKgMh1uJalAEI/41xwS\r\n1EeWYauZTP0Y0ACFHDumBNXyFOkhJsccFZhuywiODo9kbNWmdolqqgFjjfXB\r\n7q5Ux26dL2MaWmvapI+75K21P9PCYKVCFQ8KKbMdOz4o3GP6CPZciHfEaK1D\r\nP/5bnDLpytn6p8xGpPJbU1VAFDGpN1sLisc7Ttt1d58SMlW2/vBY+WqDZBrz\r\nutBulXBIN9Vn0xUVDvpPq8w//8lXGYkuibU6dm4htqc4frTlkX6R5mvbeKPh\r\nG/W0MCqQC5kgd0SGgQeAQhipXoEVg56XCAH/iNw3eS5hseU3n4CMNIkbqtLx\r\n3eowv2XmSQLzShQOIzjem0N7uMS3rQnDqb8mJ9VthlpK/N+k6cCwr7PTMHF/\r\nrcVyBlT7BdiYquic53l1wbadHgdGYaOUWlrLUHEoQqY9ofX+AI7OfZZgrsT4\r\nGT0QRu6rKW/rry2TVGGj2FUwdouoTxd5WyE=\r\n=SsIE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "types": "./src/index.d.ts", "scripts": {"test": "xo && nyc ava --serial", "clean": "rm -rf node_modules && rm -rf .nyc_output && rm -rf coverage.lcov && rm -rf ./test/testdb.sqlite", "coverage": "nyc report --reporter=text-lcov > coverage.lcov"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/keyv.git", "type": "git"}, "description": "Simple key-value storage with support for multiple backends", "directories": {}, "dependencies": {"json-buffer": "3.0.1", "compress-brotli": "^1.3.8"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.48.0", "ava": "^4.2.0", "nyc": "^15.1.0", "tsd": "^0.20.0", "pify": "5.0.0", "this": "^1.1.0", "timekeeper": "^2.2.0", "typescript": "^4.6.4", "@keyv/test-suite": "*", "eslint-plugin-promise": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_4.2.7_1652198932297_0.43784125090894777", "host": "s3://npm-registry-packages"}}, "4.2.8": {"name": "keyv", "version": "4.2.8", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@4.2.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/keyv", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "xo": {"rules": {"unicorn/prefer-module": 0, "unicorn/prefer-node-protocol": 0}}, "tsd": {"directory": "test"}, "dist": {"shasum": "6c91189fe6134d8a526cb360566693c095fcfb60", "tarball": "https://registry.npmjs.org/keyv/-/keyv-4.2.8.tgz", "fileCount": 6, "integrity": "sha512-IZZo6krhHWPhgsP5mBkEdPopVPN/stgCnBVuqi6dda/Nm5mDTOSVTrFMkWqlJsDum+B0YSe887tNxdjDWkO7aQ==", "signatures": [{"sig": "MEQCID7AyfGfY8rP5h5nr+3XKqXhaL0hVivnDAfzQyuftv5XAiAK79WcwdZ4YKcQcFfJA45D2D0EOfgtUmXd2AlihT/KaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25370, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifWwWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmquMhAAnL+38sJUOYE32UA+5pRMBpyWV6yBcICJJ4Cpcx7wktoXDDmd\r\noqBpWZPvctXXeg4rGtIWj+28SVaRpYP/WIzhnk8FKGl1wNB3enyFjsQX2DVZ\r\nghMt2LFR9OotTyDdnFbHIjPy0imOVITXIQPtCPRWDmJHbZHquAj72fXF1YTO\r\nD5W9aJl+UTiyPZUdEJBND5mEqVVGFVE9b2VUZo/xambtOvIG7LUTKHnGwg8E\r\nJxS+CNZ8A6C+FmaGZZ3NeMftuosnWMla4FkQKWpNTR4n3n/BX4zFwRuRhbs3\r\n04o2y+hnZPuQWqWY5unfqDVdnPw3C+iNQ3a5ZIeDGZ3gdKjwsoX1Ysfwf8g6\r\nUmlWPqMA/XsTHhbVcKh2Cg1r1eGCsC7tkQAAfo7Iuy+uZ+3JrYCTOAZLbBc7\r\no3+7LFI/XcqqZIIXD/FniE6V8cC4y76V9M1DLgnx+8HGK9eMvlD/G8dtLJHG\r\nzd98gcIEh3XCYBIDZw2ROGTrjkeLrSaj65Z0dEUiFufeVaokpuFOT8SakejS\r\n9qCH39lEQyoySQ7y20o5phvpNUkEnd8lpzZPoc9IqDFZGXF29jTW0kzMdw7b\r\npUYh8oWTo7psNBDmGIg9zv53XFQiopjrlJkynDz2H8CxprGVKIFW7qtTWrtt\r\neQXwPqprhCQMUjWnbgM0L4jcRSiDRzHUk2c=\r\n=jPr5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "types": "./src/index.d.ts", "scripts": {"test": "xo && nyc ava --serial", "clean": "rm -rf node_modules && rm -rf .nyc_output && rm -rf coverage.lcov && rm -rf ./test/testdb.sqlite", "coverage": "nyc report --reporter=text-lcov > coverage.lcov"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/keyv.git", "type": "git"}, "description": "Simple key-value storage with support for multiple backends", "directories": {}, "dependencies": {"json-buffer": "3.0.1", "compress-brotli": "^1.3.8"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.48.0", "ava": "^4.2.0", "nyc": "^15.1.0", "tsd": "^0.20.0", "pify": "5.0.0", "this": "^1.1.0", "timekeeper": "^2.2.0", "typescript": "^4.6.4", "@keyv/test-suite": "*", "eslint-plugin-promise": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_4.2.8_1652386838467_0.4189005265145327", "host": "s3://npm-registry-packages"}}, "4.2.9": {"name": "keyv", "version": "4.2.9", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@4.2.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/keyv", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "xo": {"rules": {"unicorn/prefer-module": 0, "unicorn/prefer-node-protocol": 0}}, "tsd": {"directory": "test"}, "dist": {"shasum": "b8f25d4968b583ed7f07f<PERSON>dab646d4baadad6b", "tarball": "https://registry.npmjs.org/keyv/-/keyv-4.2.9.tgz", "fileCount": 6, "integrity": "sha512-vqRBrN4xQHud7UMAGzGGFbt96MtGB9pb0OOg8Dhtq5RtiswCb1pCFq878iqC4hdeOP6eDPnCoFxA+2TXx427Ow==", "signatures": [{"sig": "MEUCIALPvnIPKVDK6o2oQWRMHfKv/L8/lV4gOQJIFM8Kw6PfAiEA/5uRGNxY0e0mL0YRj5dEi5ctZoaO0o+wyamKSPPeq58=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25371, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihRyoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrgYhAAivIwiGk8QBLCvG2nAaIshcIsQkfUEjw1kRzNR2rmGIW8imgc\r\nyMr76hsTkYcNj9nxUMOvHIcSkk7qDQWDlJZWS40MTLXPCnappmphi3gyUj80\r\nacjJV5zeh36pUTbzo5wQh+dfrd/ZV8OnIAjlUB7dqyznYK7lxaX5r4o1VhAj\r\nysYFRDo548hXxpijPrTbxWfeTN1qtJWNRM5L2n5tBR4atZyR7ecoBQE1rpck\r\ndScflmLB6Bntt+xVivIeiIiF7XVtSBZwFQzsq9QMGFEddybQa5oVDJqusQz9\r\nL3Q8cC0+WorB5tNR1JzxHNnJh1Ojrk8nsRfa/git9ukOdDQojgeSYbwtfOha\r\n3k+0gnaLMNPJ/jdLCGSzZ7f0DAEVlGyPHupd1JZMuE8rgdDkPr2TBYN4Oq6E\r\nypQpBgkz2sx0LTYROlGLiFMjPFFvj6fFEz2wLXDMgSGfrTtm/RVOV/i9VkJe\r\nmm63YTrwtPYRAEdmv9fzI8HplTjPU49jOtedqXLwcYffoeLRbzn1qwU+kl+F\r\nvCwSkle8TcvAZCUr8Ls04BmUdH3fAtwfgghd3HCAlW4gJXCBD8BcNMKBX1qw\r\nj2NSsqSOqBnciY+aiH9nMEGBlBDcpouLwopCA3Z5NLjSewoD3SvJb3azt8ng\r\noR87IIXz3U6Csy/jCHcA7xM1XLw/jNLNGfE=\r\n=w1VZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "types": "./src/index.d.ts", "scripts": {"test": "xo && nyc ava --serial", "clean": "rm -rf node_modules && rm -rf .nyc_output && rm -rf coverage.lcov && rm -rf ./test/testdb.sqlite", "coverage": "nyc report --reporter=text-lcov > coverage.lcov"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/keyv.git", "type": "git"}, "description": "Simple key-value storage with support for multiple backends", "directories": {}, "dependencies": {"json-buffer": "3.0.1", "compress-brotli": "^1.3.8"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.48.0", "ava": "^4.2.0", "nyc": "^15.1.0", "tsd": "^0.20.0", "pify": "5.0.0", "this": "^1.1.0", "timekeeper": "^2.2.0", "typescript": "^4.6.4", "@keyv/test-suite": "*", "eslint-plugin-promise": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_4.2.9_1652890792080_0.7627702960202147", "host": "s3://npm-registry-packages"}}, "4.3.0": {"name": "keyv", "version": "4.3.0", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@4.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/keyv", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "xo": {"rules": {"unicorn/prefer-module": 0, "unicorn/prefer-node-protocol": 0}}, "tsd": {"directory": "test"}, "dist": {"shasum": "b4352e0e4fe7c94111947d6738a6d3fe7903027c", "tarball": "https://registry.npmjs.org/keyv/-/keyv-4.3.0.tgz", "fileCount": 4, "integrity": "sha512-C30Un9+63J0CsR7Wka5quXKqYZsT6dcRQ2aOwGcSc3RiQ4HGWpTAHlCA+puNfw2jA/s11EsxA1nCXgZRuRKMQQ==", "signatures": [{"sig": "MEYCIQCq8jryWt9HxqA9J/jqw5eyNUj2EYhUayvyV/CCWBIExQIhAKcEqT/vg/8D4O3sYw43hcbn+PvjWx8GXMwAoyIvDOkT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25484, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijRjbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmogDA/+P06HwmlO+yumSnXI46tE+3KQdE/Rbwam6BaBGsBUr88PVBnh\r\nNMc8gpN84MwXB7KaCpVIvWqI2CDCzSxcyllicevZw2xaMRq3pTiZm9hA7OEK\r\nSKnBOfG1QHpXlNEHGn2XqsgV6NsThZG17sB7hPEdF7ZBuiLGVy/pxGsMFj+g\r\nXGbUyBgvSFKB7emvordKJ+sKtneGJpkXxKu0xkJfkGCdV9I6qveAXh184jf4\r\n4aMaFhrlhWNNKG38WcGgJ8z+0C8Qv5J77eNfwVcxBA1weQxA/akc3pjwQ3Vz\r\nkldxuoMxrNJOgRjRACpr9S74TMq5ArfNXB9IrrEquIUvgo1dajf95IrD7G1a\r\nINQU/E+AmzTjCE79iQ6g5GTO6+PQuNq1ZojgIij+dP9QNuw0BUomredEHpjW\r\nf4x7fvAPHQxq/zj665sFoTSHUbBDbp1zJxWJwKBCIn4ki5AAl20PsCS4PLby\r\nZcvpdpK8Et94BrlqtrLmRNLnMUjuoxTb4wL1ytgTF5orVFnHGpPOoEG8ikLJ\r\nrkJdaXB4WINpdtYwcMS6+dLFKVozGVbbwJBYaKmbg8tlTKmVK0RAU2GWSl3N\r\nK46Y0i1p3VfkGkYvsREjihB5wh3ReFegM+e42y/m07DTASc4dhQF6LgQIjdv\r\nCqOh6OqEln2OCOduat6oCa/9hAfUqktBkjc=\r\n=SQYC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "types": "./src/index.d.ts", "gitHead": "2930eeb6b5c8a3b2617ccc8ba2414aedb5c9dc39", "scripts": {"test": "xo && nyc ava --serial", "clean": "rm -rf node_modules && rm -rf .nyc_output && rm -rf coverage.lcov && rm -rf ./test/testdb.sqlite", "coverage": "nyc report --reporter=text-lcov > coverage.lcov"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/keyv.git", "type": "git"}, "workspaces": ["packages/test-suite"], "_npmVersion": "8.3.0", "description": "Simple key-value storage with support for multiple backends", "directories": {}, "_nodeVersion": "16.13.1", "dependencies": {"json-buffer": "3.0.1", "compress-brotli": "^1.3.8"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.48.0", "ava": "^4.2.0", "nyc": "^15.1.0", "tsd": "^0.20.0", "pify": "5.0.0", "this": "^1.1.0", "timekeeper": "^2.2.0", "typescript": "^4.6.4", "@keyv/test-suite": "*", "eslint-plugin-promise": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_4.3.0_1653414107064_0.6044117261179354", "host": "s3://npm-registry-packages"}}, "4.3.1": {"name": "keyv", "version": "4.3.1", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@4.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/keyv", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "xo": {"rules": {"unicorn/prefer-module": 0, "unicorn/prefer-node-protocol": 0}}, "tsd": {"directory": "test"}, "dist": {"shasum": "7970672f137d987945821b1a07b524ce5a4edd27", "tarball": "https://registry.npmjs.org/keyv/-/keyv-4.3.1.tgz", "fileCount": 6, "integrity": "sha512-nwP7AQOxFzELXsNq3zCx/oh81zu4DHWwCE6W9RaeHb7OHO0JpmKS8n801ovVQC7PTsZDWtPA5j1QY+/WWtARYg==", "signatures": [{"sig": "MEUCIC1okOh3hZfPAb7kWZhaFNGJaFHAeegzeYprvcb/ctkeAiEAq8+pjfTOBEp0OUuCKc+mxJyVyutwiAvozRoze7kM+tA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26036, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisLVkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoqQw/+P33frWvpGO5inOTTdONpXmdfRljI6Lhl6A6wPiG2bKRrmKoE\r\n8+ugsb2ej73Z7QdVNZ+JvMoPUT5y+box1kKc35KOzJz+/+T866tWWytcS5Mh\r\nh0RGYlBxD8wIaexiCDPDk0WJun3DDaN39orJrLwUrMoJa0fa5rRwq4EKu4Og\r\nxm4IW8TmAn49B7B54oKN8Bh0PKUg4Nciiy41DgB56GXeQcZIkxYuiT1gXQLc\r\nm6kJuDi/EiTuACzEMQLBYjTj7MsuBKu22T7xOpNGsvENd0anOw7vq/tzJPSZ\r\n0YyB2Ioe8Jry9r7sDD4nggs5uvxykQ3NjzmuC0kw0c7hZEoDxEq9gLRUXBrr\r\nKhv6SMbK5sdw4xR9M1FBYlpyHLzSyjiGh7cNCK8qhTcSf0igDvcoqn40Q8TU\r\nIEcWPaRw52nhcM4AukFlCVSMkqwCnM6+Yq/AV0HY06/L/LcTuTPrfuaxQIx+\r\nY2vPktIOyVpvzQ+7MMk+EI1ELJQvI5NziIUdzbSsvdekCnuRWJQemGVLOqHE\r\nq3J5B7RNWqKyeNpqI/eSacWC42G82b05p9bJImUNuW8pG7YV/8wjvNHU2D/A\r\nOxt+mFpeGc8d+w/Hk0b8j4GDVRyYx4RyCAA9G+m+CNr+ST/HlMIYReZBnUPb\r\n+9K4ffbUeIAUS/o6/yhUS9/8myNkEP8j9AE=\r\n=xxcu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "types": "./src/index.d.ts", "scripts": {"test": "xo && nyc ava --serial", "clean": "rm -rf node_modules && rm -rf .nyc_output && rm -rf coverage.lcov && rm -rf ./test/testdb.sqlite", "coverage": "nyc report --reporter=text-lcov > coverage.lcov"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/keyv.git", "type": "git"}, "description": "Simple key-value storage with support for multiple backends", "directories": {}, "dependencies": {"json-buffer": "3.0.1", "compress-brotli": "^1.3.8"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.50.0", "ava": "^4.3.0", "nyc": "^15.1.0", "tsd": "^0.21.0", "pify": "5.0.0", "this": "^1.1.0", "timekeeper": "^2.2.0", "typescript": "^4.7.4", "@keyv/test-suite": "*", "eslint-plugin-promise": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_4.3.1_1655747939923_0.6427553893521367", "host": "s3://npm-registry-packages"}}, "4.3.2": {"name": "keyv", "version": "4.3.2", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@4.3.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/keyv", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "xo": {"rules": {"unicorn/prefer-module": 0, "unicorn/prefer-node-protocol": 0}}, "tsd": {"directory": "test"}, "dist": {"shasum": "e839df676a0c7ee594c8835e7c1c83742558e5c2", "tarball": "https://registry.npmjs.org/keyv/-/keyv-4.3.2.tgz", "fileCount": 6, "integrity": "sha512-kn8WmodVBe12lmHpA6W8OY7SNh6wVR+Z+wZESF4iF5FCazaVXGWOtnbnvX0tMQ1bO+/TmOD9LziuYMvrIIs0xw==", "signatures": [{"sig": "MEUCIQDYoW2ub+seWpW3dxx06yj2ClR9GQWmV965KexDbLsB8wIgZ5StCtG+f4CCv/DqvvJvFYn/cHguGJLRJ5leSJEpltM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25971, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisylPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrNdg//TPx17lgFDmgVDcuBrJwTa5RtUK4eZpxnATzA0MU2c1irlbLp\r\n8ZgD3cJPmGpMjo16bTOHBx6pSoCQbv141tQY0wye3H4KCpG8TOgHJ5NY4Oz2\r\nq2NXCrG2SFX+vG11Pau6Mf/3A+iyyZrH+vivCYZruD62A7lCZBFRnpcRX8uq\r\nN3skU/dQsHnQyLQR69zSZoty4+tuOr6Gb3Q3bFiN2O931K9hb4o0rZYjn6IQ\r\nQoStqTB3y3nZzfpteIr4VdAxRjvnYRL6k0QKdXK5crhBtFhJg28ipfkKCaJT\r\nwwZdTsBPHpQrfK1sVmAIXhmSsPGWF5LSQYMiXp4FnrbHTwPFA7zuV5vo8pzY\r\nzRLzIxwtiFVn5hbmXoD18nBtEQCap5Z6gREmh3Y51WeiZ6BgbKMS77N+Fqvo\r\nF13UDqs4TUWpwlqj77vYDJJglp35k2uJlfUMzaozt2pCxoKeuUXJgVyY/UX7\r\n5OZ0RN7A8I3RXnCSCZ7EfZwpRDzMZHHWIdrx5kAEvx0iknk1lphiquqSyHib\r\nOc8ujVRf9kHiKbx/S0DB6RKbTO56Ijoo2o+FPxLqOc9prGj9VUTAieEWhn/Z\r\n13OqjtjZhBLtKbgJqgkieXofvazsYINdvZvXJ7M0IQTqZLctxMfn9HVei4Tq\r\nDAovPky93SJuiSyXtwT7oq3TgbnbsbX3x5k=\r\n=GAai\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "types": "./src/index.d.ts", "scripts": {"test": "xo && nyc ava --serial", "clean": "rm -rf node_modules && rm -rf .nyc_output && rm -rf coverage.lcov && rm -rf ./test/testdb.sqlite", "coverage": "nyc report --reporter=text-lcov > coverage.lcov"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/keyv.git", "type": "git"}, "description": "Simple key-value storage with support for multiple backends", "directories": {}, "dependencies": {"json-buffer": "3.0.1", "compress-brotli": "^1.3.8"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.50.0", "ava": "^4.3.0", "nyc": "^15.1.0", "tsd": "^0.21.0", "pify": "5.0.0", "this": "^1.1.0", "timekeeper": "^2.2.0", "typescript": "^4.7.4", "@keyv/test-suite": "*", "eslint-plugin-promise": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_4.3.2_1655908686933_0.7438292824903867", "host": "s3://npm-registry-packages"}}, "4.3.3": {"name": "keyv", "version": "4.3.3", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@4.3.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/keyv", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "xo": {"rules": {"unicorn/prefer-module": 0, "unicorn/prefer-node-protocol": 0}}, "tsd": {"directory": "test"}, "dist": {"shasum": "6c1bcda6353a9e96fc1b4e1aeb803a6e35090ba9", "tarball": "https://registry.npmjs.org/keyv/-/keyv-4.3.3.tgz", "fileCount": 6, "integrity": "sha512-AcysI17RvakTh8ir03+a3zJr5r0ovnAH/XTXei/4HIv3bL2K/jzvgivLK9UuI/JbU1aJjM3NSAnVvVVd3n+4DQ==", "signatures": [{"sig": "MEUCIHaDe+QSQLlH51dX4V7D085+NwJR3vTYszPNZV1A4efmAiEAnJZLJFZU6W142IgLoOnR1HQeJK1Th9FBHADTCeuqebg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26176, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1JxTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrrKQ//QXFkAwC8KEPQ+HHGdYl4woClY9f3371oSurpj/t67Lr/EcyB\r\nspjmcZ70tz7xRMN/dLHUighVxgEg8GNPxxKi0CvUQAwKn2m+NawXUrmLuMIm\r\nB07pQbhP/XJiR8o0xeNNjPW8eOwVBMWVxTN0z1FIL0RliJXar5hEEr2DVjkP\r\nrQn2ZP/fLDTj4NtiKxv7d1nSrjhbWQ3peA+kJb1WrcreWTg/D3Rt9tb8efkC\r\n50AGrAYklkIk6djLa+3LrXBfcbn8nvfZKmqzKjmBKmiPnkIqkACcxgihZKsJ\r\nH0X6V8umnCdgR6tt9Q0c0Uuae1zqStISofHNEcOriP+g8caJ3vfveZVz0okU\r\nCvA7j4S7XBA+zCeW/rUx1OLXjBp25YyKvGCsb8XiNu7mVUKVe9rMwHmYo+VA\r\niDg4VTWoZovNMcoYJAh0D2NHCtBSxRzq/f2cAto3Iq4ngboCV7qSapDtsz9M\r\nIxZMGkUdvYITelyfX9XfawXQBEsfk0Tc55Lu8+c5ITpVCGHIxT/UZRsm9N6r\r\nN/WLaCUMMbhQgM8WkRmXdHUKzpBPMpWnVLUrlTuaAJAzqcHrAKbeb9H54FuR\r\nXj7YMWo0TrC6Oqm4xlK8+0MvrJxe+sxCaacUfQ/6EuSX6VpC/FJC0RH2VoQF\r\neXkgU+yastNVEEFdW5f31mJOVF5t7LVsJ2k=\r\n=noBN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "types": "./src/index.d.ts", "scripts": {"test": "xo && nyc ava --serial", "clean": "rm -rf node_modules && rm -rf .nyc_output && rm -rf coverage.lcov && rm -rf ./test/testdb.sqlite", "coverage": "nyc report --reporter=text-lcov > coverage.lcov"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/keyv.git", "type": "git"}, "description": "Simple key-value storage with support for multiple backends", "directories": {}, "dependencies": {"json-buffer": "3.0.1", "compress-brotli": "^1.3.8"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.50.0", "ava": "^4.3.0", "nyc": "^15.1.0", "tsd": "^0.22.0", "pify": "5.0.0", "this": "^1.1.0", "eslint": "^8.19.0", "timekeeper": "^2.2.0", "typescript": "^4.7.4", "@keyv/test-suite": "*", "eslint-plugin-promise": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_4.3.3_1658100819332_0.10918382960740658", "host": "s3://npm-registry-packages"}}, "4.4.0": {"name": "keyv", "version": "4.4.0", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@4.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/keyv", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "xo": {"rules": {"unicorn/prefer-module": 0, "unicorn/prefer-node-protocol": 0}}, "tsd": {"directory": "test"}, "dist": {"shasum": "8415fa45948201bb1a0bf4ed83dad7ae6babe866", "tarball": "https://registry.npmjs.org/keyv/-/keyv-4.4.0.tgz", "fileCount": 6, "integrity": "sha512-BYafwt3p7j3Nczk/4IHsCXVV10Qy+pjPC6fpu91bmQl7d8XCrcmttI+dFb7qvp+5wq3QYl1tpL1YdwElzeMiLA==", "signatures": [{"sig": "MEUCIBSV5YlZETYeh3wot4x01SGQ+hPmVWjrhqOcGW7tFprTAiEAiyZuuns+5HCUSdPJnBdji1Hqd8J1WXBtH5bf6nw/1oc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26069, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjAnHnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp7vg//Wa2EJ6qgGxeT3wgQfw7V10yXsFP9jC9D4bKi6h4SZt/r/eiJ\r\n6SQ3cR0Kw6/YIHxJEpjq6syIOkrRqzs2q/C8dUx01D3l4jmUqB84UsWJ2Lld\r\nHITSERv/yHR4xixMy20hg42mbKawJjvCvLPIBmi9AMH5K2DfGTPfj+OBJ7V0\r\nuI4rWxzeh/bL+OZLEu1s3mxbr8uLoWCGvFKvVXo2lXYzS48UEc55lcm9OnIR\r\n/SUdKAD4/5vyvsUf1hnjV+FdFkOeREEzKkVegpZZ3tjwMniySJtWn/SGNx0H\r\nV5o/QxRA/q+DOC/X3cAd58VeWZOey/gImv2VMetkH9CQYGMEUX50NKtiQ3lR\r\n21LLsuf4KGNU0X4C7OUIvwjVoBGGJLGyVRVHk9FVDh1WpIIP8z9/wu8XyMZ1\r\nbg5KeO2PyNotNv317jUKQh9uQz3IK7Rm7uByNmeV7OhAOFS7zSU8e8wDzGbJ\r\nW01MwXYYb0/6w5u25vg1NmhE1n6k3XLRPCTISNOVn2gOmSCxdWJrb0jyKj3H\r\nvPRrmMhIr97/lmbPj3vMerzKlsJ/+CuoJAmBcMBygomd5AawoQXGzVNJpH9R\r\n0ea0eGClt0GYyOEnnsUkj6M4H9zG8a2PaYgQaqvAy0GYk4FYIdkBQpeGmSJ0\r\nogrHosZvCAJfyX/Hl8YGj8/+0CA3NeOmg5A=\r\n=H0Fm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "node": ">=14", "types": "./src/index.d.ts", "scripts": {"test": "xo && nyc ava --serial", "clean": "rm -rf node_modules && rm -rf .nyc_output && rm -rf coverage.lcov && rm -rf ./test/testdb.sqlite", "coverage": "nyc report --reporter=text-lcov > coverage.lcov"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/keyv.git", "type": "git"}, "description": "Simple key-value storage with support for multiple backends", "directories": {}, "dependencies": {"json-buffer": "3.0.1", "compress-brotli": "^1.3.8"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.51.0", "ava": "^4.3.1", "nyc": "^15.1.0", "tsd": "^0.22.0", "pify": "5.0.0", "this": "^1.1.0", "eslint": "^8.22.0", "timekeeper": "^2.2.0", "typescript": "^4.7.4", "@keyv/test-suite": "*", "eslint-plugin-promise": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_4.4.0_1661104615279_0.3711340774945222", "host": "s3://npm-registry-packages"}}, "4.4.1": {"name": "keyv", "version": "4.4.1", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@4.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/keyv", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "xo": {"rules": {"unicorn/prefer-module": 0, "unicorn/prefer-node-protocol": 0}}, "tsd": {"directory": "test"}, "dist": {"shasum": "5d97bae8dfbb6788ebc9330daf5eb6582e2d3d1c", "tarball": "https://registry.npmjs.org/keyv/-/keyv-4.4.1.tgz", "fileCount": 6, "integrity": "sha512-PzByhNxfBLnSBW2MZi1DF+W5+qB/7BMpOokewqIvqS8GFtP7xHm2oeGU72Y1fhtfOv/FiEnI4+nyViYDmUChnw==", "signatures": [{"sig": "MEUCIQDZtUw5TTY68xY3wLzuLOVgxHk8YJQM/lrLMk4Usdcy0wIgDQmAvuVAHGlft3Ssu8uZ8tzoH6phoyj2vo5qsOOPXRk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjApm+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo0ow/9E02LOQ1aj0yizbT6lRJSbnGUwJ9rmhcG/rLbExr2JRLmZFW6\r\nQILoFtrcCMZYZVbVWqxTq+H5egQWlMr6Aq2MI0+9h8IBYg6jbOSJDho7N1D+\r\nm2940nc1M3B3bM3YRnahaWUsDOfu2O7ne4fMcdzFC/a9pOmfXWz7PhIWekeC\r\n5HRMHSKx8/UC7GjvYBAD8zTvB31Mp51W3uxglCmjceTkE2+mipiKgJr+Ncw4\r\nHS8jmuozgOb/pfZ6H9cMqXM9c3nlPXyy96F7LxUIZn+dfyu3b3CMEn/cgg13\r\n/TV/Rq4370sWpXk3aigDZbRM9M2yXtnGIk+SIpIgolzrF7hBZ36GZIlAiceZ\r\nvKZuUeAIaA4k+3wNpR0UnThCDSywF/Bc8qu68xw4soPRCkTIP3O0NpCt+0Md\r\nCbvYw1ADYmzd0xFP9Aq/+4A+CB2x5of7aoI6Gtff3mwxMPji+0N13PPVAIME\r\nc/UlHTIcUrWM6C5fTSXhcAnW18pmxfG0M5LkMIlKMVGjvTqs0s3yplKBFWul\r\nG09RR/F9XWYDRHD9KlvweWdj6RHTxlXIjscLdmwzuRJejl+qoU3GCLmqn+rS\r\nYxAW7T/4tLuAC8DkOq6g2DbRT0Z2N7jDXm/JKfwxWSUIM2eTAISJW4ZqwoQn\r\nvTdqVilrBg04s/zNiqMBV3+8Hj4H+A7PuLQ=\r\n=SYz8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "types": "./src/index.d.ts", "scripts": {"test": "xo && nyc ava --serial", "clean": "rm -rf node_modules && rm -rf .nyc_output && rm -rf coverage.lcov && rm -rf ./test/testdb.sqlite", "coverage": "nyc report --reporter=text-lcov > coverage.lcov"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/keyv.git", "type": "git"}, "description": "Simple key-value storage with support for multiple backends", "directories": {}, "dependencies": {"json-buffer": "3.0.1", "compress-brotli": "^1.3.8"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.51.0", "ava": "^4.3.1", "nyc": "^15.1.0", "tsd": "^0.22.0", "pify": "5.0.0", "this": "^1.1.0", "eslint": "^8.22.0", "timekeeper": "^2.2.0", "typescript": "^4.7.4", "@keyv/test-suite": "*", "eslint-plugin-promise": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_4.4.1_1661114813969_0.11050517542290295", "host": "s3://npm-registry-packages"}}, "4.5.0": {"name": "keyv", "version": "4.5.0", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@4.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/keyv", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "xo": {"rules": {"unicorn/prefer-module": 0, "unicorn/prefer-node-protocol": 0}}, "tsd": {"directory": "test"}, "dist": {"shasum": "dbce9ade79610b6e641a9a65f2f6499ba06b9bc6", "tarball": "https://registry.npmjs.org/keyv/-/keyv-4.5.0.tgz", "fileCount": 6, "integrity": "sha512-2YvuMsA+jnFGtBareKqgANOEKe1mk3HKiXu2fRmAfyxG0MJAywNhi5ttWA3PMjl4NmpyjZNbFifR2vNjW1znfA==", "signatures": [{"sig": "MEQCIH9SIFCkKvrxlWye0TBs+uG3NPqMtfErR3BIjWteIfCXAiAeGUSYQlYRfaajZ6fIT0cU0cI4Metx9MiFEhci8filyQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26391, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjEjdWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpYfg/+K08IpnWfoa2enxzAchQ9BXYsAgkoVGfv55H8v0bJf7tKxXvs\r\nhLsqx3nj03UbzI7SS3kgYp4RBK7zUsCEf9MWHsgf8PlZnmCCtXBMmPBpJx3a\r\nfRbyv1w0fahaGI21iuuzbRuW92VKFqRgYsLvXHdsUGSLh4nYMGAmLOZ8SM75\r\nesWZPe535u9z4z8aSKbZEYASHsNw40vowdMB6dJmenpU4dvYLWN04Lxzgraq\r\n//bUZasKx2Q7YixjVO3daheBQrHSC1TeZdCCtqrYmj2ajWwoEDAmvtmQkk1M\r\nPPgQmPlFZYYl3zc5+J+4xIuckpPeuByHL/6KN/LihnUhvdjGOTxhf2eS5JNO\r\n0EaXCha4wzzwQyrjwOIK2WmbLXGJJP3qjf2Ima4G7W7EZNTp9+7ZmCxa6Ckp\r\n1FnlJ9AS+BrJUAXsgPnoJsOG388aT94ZjMAareD2nauAIKxuXG0eVTkB01lU\r\nYIflr8jmlrPcIOcbuh1XgIHeSyoTszb6htaBohhuRUmOdEbLW3aTOfL/31ya\r\nexyY9K14ucUrnJHua2JigG99i3hUEIgckoWKQROoXDqCxc2BIaP+2bM94CFp\r\n2bq15JoyKhCDvwACBFimeSmk7qEHUMO91lXFlFDgv4b059uNhUvmPmHw2jBf\r\nJFE/nucosFZGnZREWQxqGo4Nj856gBop7P0=\r\n=bKvG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "types": "./src/index.d.ts", "scripts": {"test": "xo && nyc ava --serial", "clean": "rm -rf node_modules && rm -rf .nyc_output && rm -rf coverage.lcov && rm -rf ./test/testdb.sqlite", "coverage": "nyc report --reporter=text-lcov > coverage.lcov"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/keyv.git", "type": "git"}, "description": "Simple key-value storage with support for multiple backends", "directories": {}, "dependencies": {"json-buffer": "3.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.51.0", "ava": "^4.3.0", "nyc": "^15.1.0", "tsd": "^0.22.0", "pify": "5.0.0", "this": "^1.1.0", "eslint": "^8.19.0", "timekeeper": "^2.2.0", "typescript": "^4.7.4", "@keyv/test-suite": "*", "eslint-plugin-promise": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_4.5.0_1662138197846_0.2135408133004102", "host": "s3://npm-registry-packages"}}, "4.5.1": {"name": "keyv", "version": "4.5.1", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@4.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/keyv", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "xo": {"rules": {"unicorn/prefer-module": 0, "unicorn/prefer-node-protocol": 0, "@typescript-eslint/consistent-type-definitions": 0}}, "tsd": {"directory": "test"}, "dist": {"shasum": "9ef39afc091caea4aa4b216b0e114412a9396c3d", "tarball": "https://registry.npmjs.org/keyv/-/keyv-4.5.1.tgz", "fileCount": 6, "integrity": "sha512-DONNzZ8b9FLIRIX8kF+JByejgBoGUD4msUvRlHnPMqTb49MwW3thKgB6yGsshzOLW0Bol6SGu5TysvSsM6apdA==", "signatures": [{"sig": "MEUCIHdcJ3giLWokCwXhr2XWSG8DV/Ovf7VtNzVS5pZkz5+OAiEAkWTh7ATqurPm4ynmI7eUd0issFwbKxlp0u1OqfckASg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27436, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjaEeQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpDUQ/9F1FzJJTshme4mt9IuNN75/ex6mmXLPRRx6f5U0JkmuJyEQqV\r\nXBMXX5GhBDLoBRRoz7wmnjXdtNlA/akBR6ByocyL7c1nkSZL1W+Hzeb3jSTt\r\nGwv78PI3Jw1Z+LnqJA69+lRILbUQAWAJwJZt7NM9o4vzunjdue8pGaFRv7gw\r\nLx7ILnqzrOXGzCXsfxXzssjAmLQtJhGWWV1VQqar2s++jUrkxl8TGDWSYgQD\r\namfgOfB7qo1/MchrNT2VeddibMeMEMFFF00BYqAUexwA7KztQNtXzQPKbVwO\r\n2VR3loRLgHdD49IP40ZeksoLfTJ9gKHX60BxSefBZJIc1iY3O2gxC8no7L+J\r\nRewPC1vy/drL+eMk+ZKnYahU3RNOAHg1WeKzc7cCTB9y2LCF+/kniGcIQcQ3\r\n3biEhqt/awakJK4AiYUC1XumUXJom6KlQzB7Mst3nXjCjnKTGh319mSKp7/C\r\nnTeEmsZgOBT3Pf/qy0ZPfUZxkNJ9pod6Fz+Y7rmr/GFSsmpayJSBkDG90VFK\r\n39dAq2i+MMjCh8hZuBE8IYjemVVTvQehjy94wIAQ5ylh084jqN9ZqHanGuKZ\r\nxMJC6fjWhtv/HuimyrenVe4Cgu5z+Dfcq26v1Y1MX6UhHXjQmudb51U/xvDp\r\nBGgORze1zA1yMrOq1TLPFQlTp/gVJ6xsZG8=\r\n=aMTK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "types": "./src/index.d.ts", "scripts": {"test": "xo && nyc ava --serial", "clean": "rm -rf node_modules && rm -rf .nyc_output && rm -rf coverage.lcov && rm -rf ./test/testdb.sqlite", "coverage": "nyc report --reporter=text-lcov > coverage.lcov"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/keyv.git", "type": "git"}, "description": "Simple key-value storage with support for multiple backends", "directories": {}, "dependencies": {"json-buffer": "3.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.52.4", "ava": "^5.0.1", "nyc": "^15.1.0", "tsd": "^0.24.1", "pify": "5.0.0", "this": "^1.1.0", "eslint": "^8.26.0", "timekeeper": "^2.2.0", "typescript": "^4.8.4", "@keyv/test-suite": "*", "eslint-plugin-promise": "^6.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_4.5.1_1667778447773_0.28279368159797613", "host": "s3://npm-registry-packages"}}, "4.5.2": {"name": "keyv", "version": "4.5.2", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@4.5.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/keyv", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "xo": {"rules": {"unicorn/prefer-module": 0, "unicorn/prefer-node-protocol": 0, "@typescript-eslint/consistent-type-definitions": 0}}, "tsd": {"directory": "test"}, "dist": {"shasum": "0e310ce73bf7851ec702f2eaf46ec4e3805cce56", "tarball": "https://registry.npmjs.org/keyv/-/keyv-4.5.2.tgz", "fileCount": 6, "integrity": "sha512-5MHbFaKn8cNSmVW7BYnijeAVlE4cYA/SVkifVgrh7yotnfhKmjuXpDKjrABLnT0SfHWV21P8ow07OGfRrNDg8g==", "signatures": [{"sig": "MEQCIDRHH7VSXvCNsmiqeSpspc2Bj6rU2x0nGQEXR8d+28FQAiAejI1jNsOL6hwpVH8Zz9fYe+jqvmqxmYtE3dr9MXz0Kw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27468, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjaSLyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpMxhAAkiJtapdvYppAFaMz2RADwlzOTwsBnnYte4OIR0/+0sVAonhA\r\niRXC3SVxA0xBbNQHPrtsCe/rAwaGUKn1bKh1wc1piYbWzCvFVCYck3dm03JP\r\nYNTeaj5Uw9BD7WwdXecmm06uXAKX3HfC2FQHTDT35Ccl+nagJqkzU4cy8TMi\r\nRlUET7jseGrKXTSk7Re9/4ik5RCA7zL7mTMdKh/AyBDhmb/9B5wmBNIxkene\r\n+jR0N73NeYVzubCFtcVyumhX2Y8CweBJo4MeJ0lSK/FvRksAU6qrv42gcCK4\r\nj0A6nDHhPGgITPulx44y9UQmtZ5Nv6fEK44C6Jw3Mh4zhPETCooNM/siyIQV\r\nYlCaaylqvXLnX7pDRCSli3ZFBH9tH+aboekkZVZd8wdJXJCuz8HVy0/sImTp\r\n6lnH+BasDemIjmHyeZRmEeXZ50SA8rWFfHNBZvLT2Fna+dWW4z1aMSn1bgig\r\ndlGnwn9jOr8rMVbW3+mjoVTi+pkI7i64NI7NMfE8Uuv2eAxr+BK+6/hd7bs/\r\nsmzC8vH8QyOLL3U5NMkA0isVDhkl12BMIb7SIFvp3RU7biFvG0AIPMZXvs5G\r\nG2PTuqNp+08w8ORG5ZzCJ0TimzSsGicadXxeXXCfNgUpIPdl6k5RPcAic53m\r\nAso2r4g6A23rsl5+CbDAoDbtJr2Q/MRSOP8=\r\n=aafD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/index.js", "types": "./src/index.d.ts", "scripts": {"test": "xo && nyc ava --serial", "clean": "rm -rf node_modules && rm -rf .nyc_output && rm -rf coverage.lcov && rm -rf ./test/testdb.sqlite", "coverage": "nyc report --reporter=text-lcov > coverage.lcov"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/keyv.git", "type": "git"}, "description": "Simple key-value storage with support for multiple backends", "directories": {}, "dependencies": {"json-buffer": "3.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.52.4", "ava": "^5.0.1", "nyc": "^15.1.0", "tsd": "^0.24.1", "pify": "5.0.0", "this": "^1.1.0", "eslint": "^8.26.0", "timekeeper": "^2.2.0", "typescript": "^4.8.4", "@keyv/test-suite": "*", "eslint-plugin-promise": "^6.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_4.5.2_1667834610432_0.3553457222311349", "host": "s3://npm-registry-packages"}}, "4.5.3": {"name": "keyv", "version": "4.5.3", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@4.5.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/keyv", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "xo": {"rules": {"unicorn/prefer-module": 0, "unicorn/no-typeof-undefined": 0, "unicorn/prefer-event-target": 0, "unicorn/prefer-node-protocol": 0, "@typescript-eslint/consistent-type-definitions": 0}}, "tsd": {"directory": "test"}, "dist": {"shasum": "00873d2b046df737963157bd04f294ca818c9c25", "tarball": "https://registry.npmjs.org/keyv/-/keyv-4.5.3.tgz", "fileCount": 6, "integrity": "sha512-QCiSav9WaX1PgETJ+SpNnx2PRRapJ/oRSXM4VO5OGYGSjrxbKPVFVhB3l2OCbLCk329N8qyAtsJjSjvVBWzEug==", "signatures": [{"sig": "MEQCIDlYYiRBWrAmp3zG2bD2N4q+eKKZkbezQ7pq0RUmLDEPAiA3hOEmYUoIbea5sSDNvrn7FyXlYsozZfJHWv2v6S6s4g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27828}, "main": "src/index.js", "types": "./src/index.d.ts", "scripts": {"test": "xo && c8 ava --serial", "build": "echo 'No build step required.'", "clean": "rm -rf node_modules && rm -rf ./coverage && rm -rf ./test/testdb.sqlite", "prepare": "yarn build", "test:ci": "xo && ava --serial"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/keyv.git", "type": "git"}, "description": "Simple key-value storage with support for multiple backends", "directories": {}, "dependencies": {"json-buffer": "3.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"tsd": "^0.28.1", "pify": "^5.0.0", "eslint": "^8.42.0", "timekeeper": "^2.2.0", "@keyv/test-suite": "*", "eslint-plugin-promise": "^6.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_4.5.3_1689521975575_0.1971298010517013", "host": "s3://npm-registry-packages"}}, "4.5.4": {"name": "keyv", "version": "4.5.4", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@4.5.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/keyv", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "xo": {"rules": {"unicorn/prefer-module": 0, "unicorn/no-typeof-undefined": 0, "unicorn/prefer-event-target": 0, "unicorn/prefer-node-protocol": 0, "@typescript-eslint/consistent-type-definitions": 0}}, "tsd": {"directory": "test"}, "dist": {"shasum": "a879a99e29452f942439f2a405e3af8b31d4de93", "tarball": "https://registry.npmjs.org/keyv/-/keyv-4.5.4.tgz", "fileCount": 6, "integrity": "sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==", "signatures": [{"sig": "MEUCIFXCz2pv9p8Kh/Wv6ymxIRKYcY3kCH3Gik7kKWpdDllQAiEA52qU4jmbB8gbNL8i7CCryMmUpJrQD/YZKQmDGAQozcw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27751}, "main": "src/index.js", "types": "./src/index.d.ts", "scripts": {"test": "xo && c8 ava --serial", "build": "echo 'No build step required.'", "clean": "rm -rf node_modules && rm -rf ./coverage && rm -rf ./test/testdb.sqlite", "prepare": "yarn build", "test:ci": "xo && ava --serial"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/keyv.git", "type": "git"}, "description": "Simple key-value storage with support for multiple backends", "directories": {}, "dependencies": {"json-buffer": "3.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"tsd": "^0.29.0", "pify": "^5.0.0", "eslint": "^8.51.0", "timekeeper": "^2.3.1", "@keyv/test-suite": "*", "eslint-plugin-promise": "^6.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_4.5.4_1696697634295_0.33368982442991646", "host": "s3://npm-registry-packages"}}, "5.0.0-rc.1": {"name": "keyv", "version": "5.0.0-rc.1", "keywords": ["key", "value", "store", "cache", "ttl"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@5.0.0-rc.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/keyv", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "xo": {"rules": {"guard-for-in": "off", "no-await-in-loop": "off", "import/extensions": "off", "unicorn/prefer-module": "off", "unicorn/no-typeof-undefined": "off", "unicorn/prefer-event-target": "off", "unicorn/prefer-node-protocol": "off", "@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/no-unsafe-call": "off", "import/no-extraneous-dependencies": "off", "@typescript-eslint/no-for-in-array": "off", "@typescript-eslint/no-var-requires": "off", "@typescript-eslint/no-unsafe-return": "off", "@typescript-eslint/naming-convention": "off", "@typescript-eslint/no-unsafe-argument": "off", "@typescript-eslint/no-unsafe-assignment": "off", "@typescript-eslint/prefer-ts-expect-error": "off", "@typescript-eslint/consistent-type-assertions": "off", "@typescript-eslint/consistent-type-definitions": "off", "@typescript-eslint/no-confusing-void-expression": "off"}}, "tsd": {"directory": "test"}, "dist": {"shasum": "b88e615b443267a18855247c6ff0e39cbe9a3d28", "tarball": "https://registry.npmjs.org/keyv/-/keyv-5.0.0-rc.1.tgz", "fileCount": 30, "integrity": "sha512-e/DaaSwvSV5PAVYUbwvmaM8GMue69feOSxOkH5PVz0b6JrNk4oQeaiVVlYzdGJyUR6/9RddLdnaggCVsmzjTIA==", "signatures": [{"sig": "MEUCIQCaEur35uo+2uTNv2IzGofzbngXRjZJn0hu7a+q46KJKQIgHcfB42siDeJ1IlB8DaxjxeXmGSYK2MRXpUmzleIjL04=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93735}, "main": "dist/cjs/index.js", "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "exports": {".": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}}, "scripts": {"test": "xo --fix && vitest run --coverage", "build": "tsc --project tsconfig.csj.json && tsc --project tsconfig.esm.json", "clean": "rm -rf node_modules && rm -rf ./coverage && rm -rf ./test/testdb.sqlite && rm -rf ./dist", "prepare": "yarn build", "test:ci": "xo && vitest --run --sequence.setupFiles=list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/keyv.git", "type": "git"}, "description": "Simple key-value storage with support for multiple backends", "directories": {}, "dependencies": {"@keyv/serialize": "*"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"xo": "^0.58.0", "tsd": "^0.31.0", "pify": "^5.0.0", "timekeeper": "^2.3.1", "@keyv/test-suite": "*"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_5.0.0-rc.1_1716165147785_0.5638149024884604", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "keyv", "version": "5.0.0", "keywords": ["key", "value", "store", "cache", "ttl", "key-value", "storage", "backend", "adapter", "redis", "mongodb", "sqlite", "mysql", "postgresql", "memory", "node-cache", "lru-cache", "lru", "cache-manager"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@5.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/keyv", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "xo": {"rules": {"guard-for-in": "off", "no-await-in-loop": "off", "import/extensions": "off", "unicorn/prefer-module": "off", "unicorn/no-typeof-undefined": "off", "unicorn/prefer-event-target": "off", "unicorn/prefer-node-protocol": "off", "@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/no-unsafe-call": "off", "import/no-extraneous-dependencies": "off", "@typescript-eslint/no-for-in-array": "off", "@typescript-eslint/no-var-requires": "off", "@typescript-eslint/no-unsafe-return": "off", "@typescript-eslint/naming-convention": "off", "@typescript-eslint/no-unsafe-argument": "off", "@typescript-eslint/no-unsafe-assignment": "off", "@typescript-eslint/prefer-ts-expect-error": "off", "@typescript-eslint/consistent-type-assertions": "off", "@typescript-eslint/consistent-type-definitions": "off", "@typescript-eslint/no-confusing-void-expression": "off"}}, "tsd": {"directory": "test"}, "dist": {"shasum": "f7dc34681f8a9e823dab5664ae7b3e74202a5701", "tarball": "https://registry.npmjs.org/keyv/-/keyv-5.0.0.tgz", "fileCount": 31, "integrity": "sha512-qtmxo+NR06YxIrywQzkxQG5bsNKR4GlOHZzrJHKsRXATU8ivqptnMllSuPIJRtaLmDGWdAK/n6JVfbBgRfTuXQ==", "signatures": [{"sig": "MEQCIGxm3g2cb0ULLwgysTV/FQbxwbQWuah6D0vzeD6KQ+eJAiAN/7SISsse0wDnLd2anRjnSBal0hxdiGmM4CLqCnS7sw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93651}, "main": "dist/cjs/index.js", "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "exports": {".": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}}, "scripts": {"test": "xo --fix && vitest run --coverage", "build": "rm -rf dist && tsc --project tsconfig.cjs.json && tsc --project tsconfig.esm.json", "clean": "rm -rf node_modules && rm -rf ./coverage && rm -rf ./test/testdb.sqlite && rm -rf ./dist", "prepare": "yarn build", "test:ci": "xo && vitest --run --sequence.setupFiles=list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/keyv.git", "type": "git"}, "description": "Simple key-value storage with support for multiple backends", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2017-2021 Luke Childs\nCopyright (c) 2021-2022 Jared Wray\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"@keyv/serialize": "*"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.59.3", "tsd": "^0.31.1", "timekeeper": "^2.3.1", "@keyv/test-suite": "*"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_5.0.0_1724195997897_0.48768524235510435", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "keyv", "version": "5.0.1", "keywords": ["key", "value", "store", "cache", "ttl", "key-value", "storage", "backend", "adapter", "redis", "mongodb", "sqlite", "mysql", "postgresql", "memory", "node-cache", "lru-cache", "lru", "cache-manager"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@5.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/keyv", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "xo": {"rules": {"guard-for-in": "off", "no-await-in-loop": "off", "import/extensions": "off", "unicorn/prefer-module": "off", "import/no-named-as-default": "off", "unicorn/no-typeof-undefined": "off", "unicorn/prefer-event-target": "off", "unicorn/prefer-node-protocol": "off", "@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/no-unsafe-call": "off", "import/no-extraneous-dependencies": "off", "@typescript-eslint/no-for-in-array": "off", "@typescript-eslint/no-var-requires": "off", "@typescript-eslint/no-unsafe-return": "off", "@typescript-eslint/naming-convention": "off", "@typescript-eslint/no-unsafe-argument": "off", "@typescript-eslint/no-unsafe-assignment": "off", "@typescript-eslint/prefer-ts-expect-error": "off", "@typescript-eslint/consistent-type-assertions": "off", "@typescript-eslint/consistent-type-definitions": "off", "@typescript-eslint/no-confusing-void-expression": "off"}}, "tsd": {"directory": "test"}, "dist": {"shasum": "4c7918f33cb7fc6403a6d8efa4edf854ee2574b1", "tarball": "https://registry.npmjs.org/keyv/-/keyv-5.0.1.tgz", "fileCount": 9, "integrity": "sha512-NH+3ditq1O5uTSQiiHrGOTkwUniRox/lZ8tHARdsu5Skyv0AhZca0OCycWfR1fTECvSRftMQnXqx7cBpxo8G1g==", "signatures": [{"sig": "MEQCIEFNTE2MQrOEDSwMmGYznFfRhSjoA77vFI4gF9NL1lWoAiB7z8HD7qlxPy7u+7ya1e37dw5W7OTrBLAbRNgNHmpTfA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55992}, "main": "dist/index.cjs", "type": "module", "types": "dist/index.d.ts", "module": "dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "xo --fix && vitest run --coverage", "build": "rm -rf dist && tsup src/index.ts --format cjs,esm --dts --clean", "clean": "rm -rf node_modules && rm -rf ./coverage && rm -rf ./test/testdb.sqlite && rm -rf ./dist", "prepare": "yarn build", "test:ci": "xo && vitest --run --sequence.setupFiles=list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/keyv.git", "type": "git"}, "description": "Simple key-value storage with support for multiple backends", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2017-2021 Luke Childs\nCopyright (c) 2021-2022 Jared Wray\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"@keyv/serialize": "*"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.59.3", "tsd": "^0.31.1", "timekeeper": "^2.3.1", "@keyv/test-suite": "*"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_5.0.1_1724283304127_0.06839354514246931", "host": "s3://npm-registry-packages"}}, "5.0.2": {"name": "keyv", "version": "5.0.2", "keywords": ["key", "value", "store", "cache", "ttl", "key-value", "storage", "backend", "adapter", "redis", "mongodb", "sqlite", "mysql", "postgresql", "memory", "node-cache", "lru-cache", "lru", "cache-manager"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@5.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/keyv", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "xo": {"rules": {"guard-for-in": "off", "no-await-in-loop": "off", "import/extensions": "off", "unicorn/prefer-module": "off", "import/no-named-as-default": "off", "unicorn/no-typeof-undefined": "off", "unicorn/prefer-event-target": "off", "unicorn/prefer-node-protocol": "off", "@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/no-unsafe-call": "off", "import/no-extraneous-dependencies": "off", "@typescript-eslint/no-for-in-array": "off", "@typescript-eslint/no-var-requires": "off", "@typescript-eslint/no-unsafe-return": "off", "@typescript-eslint/naming-convention": "off", "@typescript-eslint/no-unsafe-argument": "off", "@typescript-eslint/no-unsafe-assignment": "off", "@typescript-eslint/prefer-ts-expect-error": "off", "@typescript-eslint/consistent-type-assertions": "off", "@typescript-eslint/consistent-type-definitions": "off", "@typescript-eslint/no-confusing-void-expression": "off"}}, "tsd": {"directory": "test"}, "dist": {"shasum": "206a15f29ccea1de320773367a7c0e52f87d3164", "tarball": "https://registry.npmjs.org/keyv/-/keyv-5.0.2.tgz", "fileCount": 9, "integrity": "sha512-x1B+bM+i7lX1vIwtN9G1nykZxT3g/e7k9uoR4lPl2o8JCHyMClzDtb2lu5yI+NmqCN1tp/uA9+x14t1SGwo/iQ==", "signatures": [{"sig": "MEUCIQCzrSa5ppFZSbHAak6S+xHh98+JK11f1lRxQsbBTgWUjAIgenuOXrmzb1o5zsbRgPIQ+Z34lSBdfFYTRLHLct+hMyA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55988}, "main": "dist/index.cjs", "type": "module", "types": "dist/index.d.ts", "module": "dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "xo --fix && vitest run --coverage", "build": "rimraf ./dist && tsup src/index.ts --format cjs,esm --dts --clean", "clean": "rimraf ./node_modules ./coverage ./test/testdb.sqlite ./dist", "prepare": "yarn build", "test:ci": "xo && vitest --run --sequence.setupFiles=list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/keyv.git", "type": "git"}, "description": "Simple key-value storage with support for multiple backends", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2017-2021 Luke Childs\nCopyright (c) 2021-2022 Jared Wray\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"@keyv/serialize": "*"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.59.3", "tsd": "^0.31.2", "rimraf": "^6.0.1", "timekeeper": "^2.3.1", "@keyv/test-suite": "*"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_5.0.2_1727042137489_0.4105780596825914", "host": "s3://npm-registry-packages"}}, "5.0.3": {"name": "keyv", "version": "5.0.3", "keywords": ["key", "value", "store", "cache", "ttl", "key-value", "storage", "backend", "adapter", "redis", "mongodb", "sqlite", "mysql", "postgresql", "memory", "node-cache", "lru-cache", "lru", "cache-manager"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@5.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/keyv", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "xo": {"rules": {"guard-for-in": "off", "no-await-in-loop": "off", "import/extensions": "off", "unicorn/prefer-module": "off", "import/no-named-as-default": "off", "unicorn/no-typeof-undefined": "off", "unicorn/prefer-event-target": "off", "unicorn/prefer-node-protocol": "off", "@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/no-unsafe-call": "off", "import/no-extraneous-dependencies": "off", "@typescript-eslint/no-for-in-array": "off", "@typescript-eslint/no-var-requires": "off", "@typescript-eslint/no-unsafe-return": "off", "@typescript-eslint/naming-convention": "off", "@typescript-eslint/no-unsafe-argument": "off", "@typescript-eslint/no-unsafe-assignment": "off", "@typescript-eslint/prefer-ts-expect-error": "off", "@typescript-eslint/consistent-type-assertions": "off", "@typescript-eslint/consistent-type-definitions": "off", "@typescript-eslint/no-confusing-void-expression": "off"}}, "tsd": {"directory": "test"}, "dist": {"shasum": "f0a5a3b3bf41ee4a15a1c481140c0f1e26e6af3f", "tarball": "https://registry.npmjs.org/keyv/-/keyv-5.0.3.tgz", "fileCount": 9, "integrity": "sha512-WmefGWaWkWiWDkIasfHxpWmM1lych/LPtRmNj8jnIQVGLsAgFw73Vg9utZ7ss97/JwRlERABb/fSejTPY4hlZQ==", "signatures": [{"sig": "MEUCIEzo2w7XF75mBUK5bJV8mx0ZiG7NZAra80u6ewOZaxOhAiEAjpV+vhEo2l1HdpDakxcZyV+ruUOqkG9IXTIEhlaNLTs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55988}, "main": "dist/index.cjs", "type": "module", "types": "dist/index.d.ts", "module": "dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "xo --fix && vitest run --coverage", "build": "rimraf ./dist && tsup src/index.ts --format cjs,esm --dts --clean", "clean": "rimraf ./node_modules ./coverage ./test/testdb.sqlite ./dist", "prepare": "yarn build", "test:ci": "xo && vitest --run --sequence.setupFiles=list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/keyv.git", "type": "git"}, "description": "Simple key-value storage with support for multiple backends", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2017-2021 Luke Childs\nCopyright (c) 2021-2022 Jared Wray\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"@keyv/serialize": "*"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.59.3", "tsd": "^0.31.2", "rimraf": "^6.0.1", "timekeeper": "^2.3.1", "@keyv/test-suite": "*"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_5.0.3_1727042404419_0.5940678180085694", "host": "s3://npm-registry-packages"}}, "5.1.0": {"name": "keyv", "version": "5.1.0", "keywords": ["key", "value", "store", "cache", "ttl", "key-value", "storage", "backend", "adapter", "redis", "mongodb", "sqlite", "mysql", "postgresql", "memory", "node-cache", "lru-cache", "lru", "cache-manager"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@5.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/keyv", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "xo": {"rules": {"guard-for-in": "off", "no-await-in-loop": "off", "import/extensions": "off", "unicorn/prefer-module": "off", "import/no-named-as-default": "off", "unicorn/no-typeof-undefined": "off", "unicorn/prefer-event-target": "off", "unicorn/prefer-node-protocol": "off", "@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/no-unsafe-call": "off", "import/no-extraneous-dependencies": "off", "@typescript-eslint/no-for-in-array": "off", "@typescript-eslint/no-var-requires": "off", "@typescript-eslint/no-unsafe-return": "off", "@typescript-eslint/naming-convention": "off", "@typescript-eslint/no-unsafe-argument": "off", "@typescript-eslint/no-unsafe-assignment": "off", "@typescript-eslint/prefer-ts-expect-error": "off", "@typescript-eslint/consistent-type-assertions": "off", "@typescript-eslint/consistent-type-definitions": "off", "@typescript-eslint/no-confusing-void-expression": "off"}}, "tsd": {"directory": "test"}, "dist": {"shasum": "b371f54dff428e1a1bfe71328578266ebb62c470", "tarball": "https://registry.npmjs.org/keyv/-/keyv-5.1.0.tgz", "fileCount": 9, "integrity": "sha512-FUr1fbKVsj9IZkPkY9reJ80Lp2B3ldtFXH+xK0wvZYzOpwgHV1er3xP4JUhu2cgkV2X3BJQw+hzAbIGqa+hNIA==", "signatures": [{"sig": "MEYCIQDJJ7g4s2XZ+aJ1YC+A8NjFMxPLP5p+DarTQCd+satqEQIhAOXNo/BveaRiesvrI7Imz1JgahMk+ywSxgLdZljgwUst", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57508}, "main": "dist/index.cjs", "type": "module", "types": "dist/index.d.ts", "module": "dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "xo --fix && vitest run --coverage", "build": "rimraf ./dist && tsup src/index.ts --format cjs,esm --dts --clean", "clean": "rimraf ./node_modules ./coverage ./test/testdb.sqlite ./dist", "prepare": "yarn build", "test:ci": "xo && vitest --run --sequence.setupFiles=list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jaredwray/keyv.git", "type": "git"}, "description": "Simple key-value storage with support for multiple backends", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2017-2021 Luke Childs\nCopyright (c) 2021-2022 Jared Wray\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"@keyv/serialize": "*"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.59.3", "tsd": "^0.31.2", "rimraf": "^6.0.1", "timekeeper": "^2.3.1", "@keyv/test-suite": "*"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_5.1.0_1728170094905_0.133642528060115", "host": "s3://npm-registry-packages"}}, "5.1.1": {"name": "keyv", "version": "5.1.1", "keywords": ["key", "value", "store", "cache", "ttl", "key-value", "storage", "backend", "adapter", "redis", "mongodb", "sqlite", "mysql", "postgresql", "memory", "node-cache", "lru-cache", "lru", "cache-manager"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@5.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/keyv", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "xo": {"rules": {"guard-for-in": "off", "no-await-in-loop": "off", "import/extensions": "off", "unicorn/prefer-module": "off", "import/no-named-as-default": "off", "unicorn/no-typeof-undefined": "off", "unicorn/prefer-event-target": "off", "unicorn/prefer-node-protocol": "off", "@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/no-unsafe-call": "off", "import/no-extraneous-dependencies": "off", "@typescript-eslint/no-for-in-array": "off", "@typescript-eslint/no-var-requires": "off", "@typescript-eslint/no-unsafe-return": "off", "@typescript-eslint/naming-convention": "off", "@typescript-eslint/no-unsafe-argument": "off", "@typescript-eslint/no-unsafe-assignment": "off", "@typescript-eslint/prefer-ts-expect-error": "off", "@typescript-eslint/consistent-type-assertions": "off", "@typescript-eslint/consistent-type-definitions": "off", "@typescript-eslint/no-confusing-void-expression": "off"}}, "tsd": {"directory": "test"}, "dist": {"shasum": "936f692e0273294fd69654fc2c827bf766e15910", "tarball": "https://registry.npmjs.org/keyv/-/keyv-5.1.1.tgz", "fileCount": 7, "integrity": "sha512-pB8Z9XLdjaIOqf5SVqxhgIjLFs5P7XlpCHSrqjNm1GgRCCMNvCM9G6BzlzW3Q+M2pYogjkQ1VA9mb0IqwqtNUA==", "signatures": [{"sig": "MEYCIQDT2/A8bbStiJndWeWuq7kd1czUh8cynfjXpez3uuhFrQIhAKFZbMMmv8iXxampEQ3DrGNbiSUTQ7TWqohOLAhpj/0P", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59588}, "main": "dist/index.cjs", "type": "module", "_from": "file:keyv-5.1.1.tgz", "types": "dist/index.d.ts", "module": "dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "xo --fix && vitest run --coverage", "build": "rimraf ./dist && tsup src/index.ts --format cjs,esm --dts --clean", "clean": "rimraf ./node_modules ./coverage ./test/testdb.sqlite ./dist", "test:ci": "xo && vitest --run --sequence.setupFiles=list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/h1/n3vxgc0n1sn5_9pxftc4p6l80000gn/T/d81890306139eee44eea4cf153639eee/keyv-5.1.1.tgz", "_integrity": "sha512-pB8Z9XLdjaIOqf5SVqxhgIjLFs5P7XlpCHSrqjNm1GgRCCMNvCM9G6BzlzW3Q+M2pYogjkQ1VA9mb0IqwqtNUA==", "repository": {"url": "git+https://github.com/jaredwray/keyv.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "Simple key-value storage with support for multiple backends", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"@keyv/serialize": "*"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.59.3", "tsd": "^0.31.2", "rimraf": "^6.0.1", "timekeeper": "^2.3.1", "@keyv/mongo": "*", "@keyv/sqlite": "*", "@keyv/memcache": "*", "@keyv/test-suite": "*", "@keyv/compress-gzip": "*", "@keyv/compress-brotli": "*"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_5.1.1_1729523762091_0.9427292574863713", "host": "s3://npm-registry-packages"}}, "5.1.2": {"name": "keyv", "version": "5.1.2", "keywords": ["key", "value", "store", "cache", "ttl", "key-value", "storage", "backend", "adapter", "redis", "mongodb", "sqlite", "mysql", "postgresql", "memory", "node-cache", "lru-cache", "lru", "cache-manager"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@5.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/keyv", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "xo": {"rules": {"guard-for-in": "off", "no-await-in-loop": "off", "import/extensions": "off", "unicorn/prefer-module": "off", "import/no-named-as-default": "off", "unicorn/no-typeof-undefined": "off", "unicorn/prefer-event-target": "off", "unicorn/prefer-node-protocol": "off", "@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/no-unsafe-call": "off", "import/no-extraneous-dependencies": "off", "@typescript-eslint/no-for-in-array": "off", "@typescript-eslint/no-var-requires": "off", "@typescript-eslint/no-unsafe-return": "off", "@typescript-eslint/naming-convention": "off", "@typescript-eslint/no-unsafe-argument": "off", "@typescript-eslint/no-unsafe-assignment": "off", "@typescript-eslint/prefer-ts-expect-error": "off", "@typescript-eslint/consistent-type-assertions": "off", "@typescript-eslint/consistent-type-definitions": "off", "@typescript-eslint/no-confusing-void-expression": "off"}}, "tsd": {"directory": "test"}, "dist": {"shasum": "554772806cbc0b50fed378cb4f21cea7c3bcc8c5", "tarball": "https://registry.npmjs.org/keyv/-/keyv-5.1.2.tgz", "fileCount": 7, "integrity": "sha512-gOWSYwaPJsBNB8r+ptJDHLgyw7C7YPeta3gDHfn9kEYMgWNGqw2m+GOvfl6V2CsaiuNfxd1LFz7pnXD5KsFU3w==", "signatures": [{"sig": "MEQCIBWPmrYyru8chmwbxTIYewf2On8/0B0NDHUHQJONWvgPAiBXX93vhFvesdc+z+7NE4HsHEAH3lbK3s3eIKdu9GFiqQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60070}, "main": "dist/index.cjs", "type": "module", "_from": "file:keyv-5.1.2.tgz", "types": "dist/index.d.ts", "module": "dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "xo --fix && vitest run --coverage", "build": "rimraf ./dist && tsup src/index.ts --format cjs,esm --dts --clean", "clean": "rimraf ./node_modules ./coverage ./test/testdb.sqlite ./dist", "test:ci": "xo && vitest --run --sequence.setupFiles=list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/h1/n3vxgc0n1sn5_9pxftc4p6l80000gn/T/ad29c9f00a88ee32236c1a330eda5b17/keyv-5.1.2.tgz", "_integrity": "sha512-gOWSYwaPJsBNB8r+ptJDHLgyw7C7YPeta3gDHfn9kEYMgWNGqw2m+GOvfl6V2CsaiuNfxd1LFz7pnXD5KsFU3w==", "repository": {"url": "git+https://github.com/jaredwray/keyv.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "Simple key-value storage with support for multiple backends", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"@keyv/serialize": "*"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.59.3", "tsd": "^0.31.2", "rimraf": "^6.0.1", "timekeeper": "^2.3.1", "@keyv/mongo": "*", "@keyv/sqlite": "*", "@keyv/memcache": "*", "@keyv/test-suite": "*", "@keyv/compress-gzip": "*", "@keyv/compress-brotli": "*"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_5.1.2_1729538969019_0.3787309192799493", "host": "s3://npm-registry-packages"}}, "5.1.3": {"name": "keyv", "version": "5.1.3", "keywords": ["key", "value", "store", "cache", "ttl", "key-value", "storage", "backend", "adapter", "redis", "mongodb", "sqlite", "mysql", "postgresql", "memory", "node-cache", "lru-cache", "lru", "cache-manager"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@5.1.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/keyv", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "xo": {"rules": {"guard-for-in": "off", "no-await-in-loop": "off", "import/extensions": "off", "unicorn/prefer-module": "off", "import/no-named-as-default": "off", "unicorn/no-typeof-undefined": "off", "unicorn/prefer-event-target": "off", "unicorn/prefer-node-protocol": "off", "@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/no-unsafe-call": "off", "import/no-extraneous-dependencies": "off", "@typescript-eslint/no-for-in-array": "off", "@typescript-eslint/no-var-requires": "off", "@typescript-eslint/no-unsafe-return": "off", "@typescript-eslint/naming-convention": "off", "@typescript-eslint/no-unsafe-argument": "off", "@typescript-eslint/no-unsafe-assignment": "off", "@typescript-eslint/prefer-ts-expect-error": "off", "@typescript-eslint/consistent-type-assertions": "off", "@typescript-eslint/consistent-type-definitions": "off", "@typescript-eslint/no-confusing-void-expression": "off"}}, "tsd": {"directory": "test"}, "dist": {"shasum": "9471f0c086ca346e648825953b6f16bd903ac062", "tarball": "https://registry.npmjs.org/keyv/-/keyv-5.1.3.tgz", "fileCount": 7, "integrity": "sha512-qWefZgq3GByWFTQgn+C9U7iaHe+mwAVb1utqbna15ggTO5RiLSL91NY+5liTgzchAhGhI6MOn5NVVn/eR1FkPA==", "signatures": [{"sig": "MEQCIDlbRoCxZUOpzCUWrNam4UH4TcqIfNyPqTNx6fP1kisNAiAgZkBUvhk2SeGL88cH7zdZuoJjPz02UzbpsA75y3IOpg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60352}, "main": "dist/index.cjs", "type": "module", "_from": "file:keyv-5.1.3.tgz", "types": "dist/index.d.ts", "module": "dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "xo --fix && vitest run --coverage", "build": "rimraf ./dist && tsup src/index.ts --format cjs,esm --dts --clean", "clean": "rimraf ./node_modules ./coverage ./test/testdb.sqlite ./dist", "test:ci": "xo && vitest --run --sequence.setupFiles=list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/h1/n3vxgc0n1sn5_9pxftc4p6l80000gn/T/0e278d0030b3bbedfea3df9f3fc95e97/keyv-5.1.3.tgz", "_integrity": "sha512-qWefZgq3GByWFTQgn+C9U7iaHe+mwAVb1utqbna15ggTO5RiLSL91NY+5liTgzchAhGhI6MOn5NVVn/eR1FkPA==", "repository": {"url": "git+https://github.com/jaredwray/keyv.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Simple key-value storage with support for multiple backends", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"@keyv/serialize": "*"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.59.3", "tsd": "^0.31.2", "rimraf": "^6.0.1", "timekeeper": "^2.3.1", "@keyv/mongo": "*", "@keyv/sqlite": "*", "@keyv/memcache": "*", "@keyv/test-suite": "*", "@keyv/compress-gzip": "*", "@keyv/compress-brotli": "*"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_5.1.3_1730564794236_0.8508280191087574", "host": "s3://npm-registry-packages"}}, "5.2.0": {"name": "keyv", "version": "5.2.0", "keywords": ["key", "value", "store", "cache", "ttl", "key-value", "storage", "backend", "adapter", "redis", "mongodb", "sqlite", "mysql", "postgresql", "memory", "node-cache", "lru-cache", "lru", "cache-manager"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@5.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/keyv", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "xo": {"rules": {"guard-for-in": "off", "no-await-in-loop": "off", "import/extensions": "off", "unicorn/prefer-module": "off", "import/no-named-as-default": "off", "unicorn/no-typeof-undefined": "off", "unicorn/prefer-event-target": "off", "unicorn/prefer-node-protocol": "off", "@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/no-unsafe-call": "off", "import/no-extraneous-dependencies": "off", "@typescript-eslint/no-for-in-array": "off", "@typescript-eslint/no-var-requires": "off", "@typescript-eslint/no-unsafe-return": "off", "@typescript-eslint/naming-convention": "off", "@typescript-eslint/no-unsafe-argument": "off", "@typescript-eslint/no-unsafe-assignment": "off", "@typescript-eslint/prefer-ts-expect-error": "off", "@typescript-eslint/consistent-type-assertions": "off", "@typescript-eslint/consistent-type-definitions": "off", "@typescript-eslint/no-confusing-void-expression": "off"}}, "tsd": {"directory": "test"}, "dist": {"shasum": "da9f52532cf64c2616d8b66bb5cda4a997c7f874", "tarball": "https://registry.npmjs.org/keyv/-/keyv-5.2.0.tgz", "fileCount": 7, "integrity": "sha512-LKtKPS5JVvm6DQrd/9JN7RTGzT9s0XMiS5l02fm0cCgnhUTa7O/fmXNa9+TmeWBsSV4KiZCOt5VKt6mhplG1RQ==", "signatures": [{"sig": "MEUCIQDdE1szmpVmLbFH/y7drjj1eoE3+P+661YYm0ibEmYL7AIgavz9l3I9VJsPg4JXGpZkCkwCM8/jfqgyjp2c9dpudr0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82627}, "main": "dist/index.cjs", "type": "module", "_from": "file:keyv-5.2.0.tgz", "types": "dist/index.d.ts", "module": "dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "xo --fix && vitest run --coverage", "build": "rimraf ./dist && tsup src/index.ts --format cjs,esm --dts --clean", "clean": "rimraf ./node_modules ./coverage ./test/testdb.sqlite ./dist", "test:ci": "xo && vitest --run --sequence.setupFiles=list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/h1/n3vxgc0n1sn5_9pxftc4p6l80000gn/T/73dadaaa6e32f5f528e8127450e8e076/keyv-5.2.0.tgz", "_integrity": "sha512-LKtKPS5JVvm6DQrd/9JN7RTGzT9s0XMiS5l02fm0cCgnhUTa7O/fmXNa9+TmeWBsSV4KiZCOt5VKt6mhplG1RQ==", "repository": {"url": "git+https://github.com/jaredwray/keyv.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Simple key-value storage with support for multiple backends", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"@keyv/serialize": "*"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.59.3", "tsd": "^0.31.2", "rimraf": "^6.0.1", "timekeeper": "^2.3.1", "@keyv/mongo": "*", "@keyv/sqlite": "*", "@keyv/memcache": "*", "@keyv/test-suite": "*", "@keyv/compress-gzip": "*", "@keyv/compress-brotli": "*"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_5.2.0_1731185377744_0.693760442712877", "host": "s3://npm-registry-packages"}}, "5.2.1": {"name": "keyv", "version": "5.2.1", "keywords": ["key", "value", "store", "cache", "ttl", "key-value", "storage", "backend", "adapter", "redis", "mongodb", "sqlite", "mysql", "postgresql", "memory", "node-cache", "lru-cache", "lru", "cache-manager"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@5.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/keyv", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "xo": {"rules": {"guard-for-in": "off", "no-await-in-loop": "off", "import/extensions": "off", "unicorn/prefer-module": "off", "import/no-named-as-default": "off", "unicorn/no-typeof-undefined": "off", "unicorn/prefer-event-target": "off", "unicorn/prefer-node-protocol": "off", "@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/no-unsafe-call": "off", "import/no-extraneous-dependencies": "off", "@typescript-eslint/no-for-in-array": "off", "@typescript-eslint/no-var-requires": "off", "@typescript-eslint/no-unsafe-return": "off", "@typescript-eslint/naming-convention": "off", "@typescript-eslint/no-unsafe-argument": "off", "@typescript-eslint/no-unsafe-assignment": "off", "@typescript-eslint/prefer-ts-expect-error": "off", "@typescript-eslint/consistent-type-assertions": "off", "@typescript-eslint/consistent-type-definitions": "off", "@typescript-eslint/no-confusing-void-expression": "off"}}, "tsd": {"directory": "test"}, "dist": {"shasum": "908612ecdd8491927fdf38417692777669b87277", "tarball": "https://registry.npmjs.org/keyv/-/keyv-5.2.1.tgz", "fileCount": 7, "integrity": "sha512-tpIgCaY02VCW2Pz0zAn4guyct+IeH6Mb5wZdOvpe4oqXeQOJO0C3Wo8fTnf7P3ZD83Vr9kghbkNmzG3lTOhy/A==", "signatures": [{"sig": "MEUCIQCnwVBXXDvNMer/IRO4nPuZ82AhKn+OjRlMOF3TqPdF+gIgMpexchjJrqoqgTG0M+bDPe5R3s2/IbrO5xedWv6hMB0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83014}, "main": "dist/index.cjs", "type": "module", "_from": "file:keyv-5.2.1.tgz", "types": "dist/index.d.ts", "module": "dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "xo --fix && vitest run --coverage", "build": "rimraf ./dist && tsup src/index.ts --format cjs,esm --dts --clean", "clean": "rimraf ./node_modules ./coverage ./test/testdb.sqlite ./dist", "test:ci": "xo && vitest --run --sequence.setupFiles=list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/h1/n3vxgc0n1sn5_9pxftc4p6l80000gn/T/ffa5b6f372fa906581121d4d668dbd0f/keyv-5.2.1.tgz", "_integrity": "sha512-tpIgCaY02VCW2Pz0zAn4guyct+IeH6Mb5wZdOvpe4oqXeQOJO0C3Wo8fTnf7P3ZD83Vr9kghbkNmzG3lTOhy/A==", "repository": {"url": "git+https://github.com/jaredwray/keyv.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Simple key-value storage with support for multiple backends", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"@keyv/serialize": "*"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.59.3", "tsd": "^0.31.2", "rimraf": "^6.0.1", "timekeeper": "^2.3.1", "@keyv/mongo": "*", "@keyv/sqlite": "*", "@keyv/memcache": "*", "@keyv/test-suite": "*", "@keyv/compress-gzip": "*", "@keyv/compress-brotli": "*"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_5.2.1_1731190464707_0.7411652110182811", "host": "s3://npm-registry-packages"}}, "5.2.2": {"name": "keyv", "version": "5.2.2", "keywords": ["key", "value", "store", "cache", "ttl", "key-value", "storage", "backend", "adapter", "redis", "mongodb", "sqlite", "mysql", "postgresql", "memory", "node-cache", "lru-cache", "lru", "cache-manager"], "author": {"url": "http://jaredwray.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keyv@5.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "homepage": "https://github.com/jaredwray/keyv", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "xo": {"rules": {"guard-for-in": "off", "no-await-in-loop": "off", "import/extensions": "off", "unicorn/prefer-module": "off", "import/no-named-as-default": "off", "unicorn/no-typeof-undefined": "off", "unicorn/prefer-event-target": "off", "unicorn/prefer-node-protocol": "off", "@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/no-unsafe-call": "off", "import/no-extraneous-dependencies": "off", "@typescript-eslint/no-for-in-array": "off", "@typescript-eslint/no-var-requires": "off", "@typescript-eslint/no-unsafe-return": "off", "@typescript-eslint/naming-convention": "off", "@typescript-eslint/no-unsafe-argument": "off", "@typescript-eslint/no-unsafe-assignment": "off", "@typescript-eslint/prefer-ts-expect-error": "off", "@typescript-eslint/consistent-type-assertions": "off", "@typescript-eslint/consistent-type-definitions": "off", "@typescript-eslint/no-confusing-void-expression": "off"}}, "tsd": {"directory": "test"}, "dist": {"shasum": "5909eac90c76c4296921d964ab233e5590326bfa", "tarball": "https://registry.npmjs.org/keyv/-/keyv-5.2.2.tgz", "fileCount": 7, "integrity": "sha512-CRPP4Sq5ofbUE8s4FOirFmDgHeKZFRrH/8+WOUNvLJiMIplRMfnMjxmbaDb+zVd7ex0gGAWqMhZHfcL2u6PrNQ==", "signatures": [{"sig": "MEUCID0HtJeCzQiqnADPfDE0L51mbVNqrmIlBNsfMq/dctD4AiEAneIOwuFp+rVUeLs5cgZBkKeHF2stRkCPhIh3s6ZrNfQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82243}, "main": "dist/index.cjs", "type": "module", "_from": "file:keyv-5.2.2.tgz", "types": "dist/index.d.ts", "module": "dist/index.js", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"test": "xo --fix && vitest run --coverage", "build": "rimraf ./dist && tsup src/index.ts --format cjs,esm --dts --clean", "clean": "rimraf ./node_modules ./coverage ./test/testdb.sqlite ./dist", "test:ci": "xo && vitest --run --sequence.setupFiles=list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/gs/5m4m2s857ts7l7nbvv0b57r80000gn/T/0b1f3058bbdbc6c811fb93224fa76b10/keyv-5.2.2.tgz", "_integrity": "sha512-CRPP4Sq5ofbUE8s4FOirFmDgHeKZFRrH/8+WOUNvLJiMIplRMfnMjxmbaDb+zVd7ex0gGAWqMhZHfcL2u6PrNQ==", "repository": {"url": "git+https://github.com/jaredwray/keyv.git", "type": "git"}, "_npmVersion": "10.9.1", "description": "Simple key-value storage with support for multiple backends", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"@keyv/serialize": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.60.0", "tsd": "^0.31.2", "rimraf": "^6.0.1", "timekeeper": "^2.3.1", "@keyv/mongo": "^3.0.1", "@keyv/sqlite": "^4.0.1", "@keyv/memcache": "^2.0.1", "@keyv/test-suite": "^2.0.3", "@keyv/compress-gzip": "^2.0.2", "@keyv/compress-brotli": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/keyv_5.2.2_1733967413271_0.9775075845355303", "host": "s3://npm-registry-packages-npm-production"}}, "5.2.3": {"name": "keyv", "version": "5.2.3", "description": "Simple key-value storage with support for multiple backends", "type": "module", "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"require": "./dist/index.cjs", "import": "./dist/index.js"}}, "xo": {"rules": {"import/no-named-as-default": "off", "unicorn/prefer-module": "off", "unicorn/prefer-node-protocol": "off", "@typescript-eslint/consistent-type-definitions": "off", "unicorn/no-typeof-undefined": "off", "unicorn/prefer-event-target": "off", "import/no-extraneous-dependencies": "off", "import/extensions": "off", "@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/no-unsafe-call": "off", "@typescript-eslint/no-for-in-array": "off", "guard-for-in": "off", "no-await-in-loop": "off", "@typescript-eslint/no-unsafe-return": "off", "@typescript-eslint/no-unsafe-assignment": "off", "@typescript-eslint/no-unsafe-argument": "off", "@typescript-eslint/naming-convention": "off", "@typescript-eslint/consistent-type-assertions": "off", "@typescript-eslint/no-confusing-void-expression": "off", "@typescript-eslint/no-var-requires": "off", "@typescript-eslint/prefer-ts-expect-error": "off"}}, "repository": {"type": "git", "url": "git+https://github.com/jaredwray/keyv.git"}, "keywords": ["key", "value", "store", "cache", "ttl", "key-value", "storage", "backend", "adapter", "redis", "mongodb", "sqlite", "mysql", "postgresql", "memory", "node-cache", "lru-cache", "lru", "cache-manager"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jaredwray.com"}, "license": "MIT", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "homepage": "https://github.com/jaredwray/keyv", "dependencies": {"@keyv/serialize": "^1.0.2"}, "devDependencies": {"rimraf": "^6.0.1", "timekeeper": "^2.3.1", "tsd": "^0.31.2", "xo": "^0.60.0", "@keyv/compress-brotli": "^2.0.2", "@keyv/compress-gzip": "^2.0.2", "@keyv/memcache": "^2.0.1", "@keyv/mongo": "^3.0.1", "@keyv/sqlite": "^4.0.1", "@keyv/test-suite": "^2.0.3"}, "tsd": {"directory": "test"}, "scripts": {"build": "rimraf ./dist && tsup src/index.ts --format cjs,esm --dts --clean", "test": "xo --fix && vitest run --coverage", "test:ci": "xo && vitest --run --sequence.setupFiles=list", "clean": "rimraf ./node_modules ./coverage ./test/testdb.sqlite ./dist"}, "_id": "keyv@5.2.3", "_integrity": "sha512-AGKecUfzrowabUv0bH1RIR5Vf7w+l4S3xtQAypKaUpTdIR1EbrAcTxHCrpo9Q+IWeUlFE2palRtgIQcgm+PQJw==", "_resolved": "/private/var/folders/h1/n3vxgc0n1sn5_9pxftc4p6l80000gn/T/0031cda55e51c3d2f4cf578fdc8cff63/keyv-5.2.3.tgz", "_from": "file:keyv-5.2.3.tgz", "_nodeVersion": "20.17.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-AGKecUfzrowabUv0bH1RIR5Vf7w+l4S3xtQAypKaUpTdIR1EbrAcTxHCrpo9Q+IWeUlFE2palRtgIQcgm+PQJw==", "shasum": "32db1a4aa8d05e2b8ab82688a57ddc5d2184a25c", "tarball": "https://registry.npmjs.org/keyv/-/keyv-5.2.3.tgz", "fileCount": 7, "unpackedSize": 83646, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD8faVzNkPMxsfmdstRNZqFdMNvaJMjDAPZRUZGpfbDSgIgTkDlKouojhakOPMsfsSONdlkcot5sMbuxmXof4xXOC4="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/keyv_5.2.3_1734806689948_0.8079582818226816"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-07-07T13:43:26.836Z", "modified": "2024-12-21T18:44:50.384Z", "0.0.0": "2017-07-07T13:43:26.836Z", "0.1.0": "2017-07-11T13:02:11.437Z", "0.1.1": "2017-07-11T13:54:40.726Z", "0.1.2": "2017-07-11T14:11:06.245Z", "0.1.3": "2017-07-11T16:28:33.460Z", "0.2.0": "2017-07-13T14:44:51.123Z", "0.2.1": "2017-07-14T18:26:04.556Z", "0.3.0": "2017-07-17T18:56:31.829Z", "0.3.1": "2017-07-18T13:36:45.337Z", "0.4.0": "2017-07-26T15:00:00.863Z", "0.5.0": "2017-07-29T13:21:37.392Z", "0.6.0": "2017-07-29T23:16:52.728Z", "0.7.0": "2017-07-30T07:22:50.416Z", "0.8.0": "2017-08-01T16:01:36.571Z", "1.0.0": "2017-08-02T22:28:03.485Z", "1.0.1": "2017-08-02T23:57:32.945Z", "1.0.2": "2017-08-03T09:59:29.263Z", "1.0.3": "2017-08-03T10:05:43.135Z", "1.0.4": "2017-08-03T21:19:14.351Z", "1.0.5": "2017-08-10T13:05:33.813Z", "2.0.0": "2017-08-11T15:34:02.022Z", "2.0.1": "2017-08-13T13:17:22.860Z", "2.0.2": "2017-08-17T16:46:03.155Z", "3.0.0": "2017-10-03T13:38:26.570Z", "3.1.0": "2018-08-21T21:17:23.168Z", "4.0.0": "2019-11-16T05:47:38.606Z", "4.0.1": "2020-05-05T07:08:03.807Z", "4.0.2": "2020-09-16T11:12:27.718Z", "4.0.3": "2020-09-17T10:39:14.276Z", "4.0.4": "2021-10-31T22:39:14.725Z", "4.0.5": "2022-01-08T16:25:15.143Z", "4.1.0": "2022-02-01T22:23:16.260Z", "4.1.1": "2022-02-07T01:04:58.718Z", "4.2.0": "2022-04-02T21:35:55.729Z", "4.2.1": "2022-04-03T22:46:26.130Z", "4.2.2": "2022-04-06T23:49:00.321Z", "4.2.5": "2022-05-10T00:30:35.380Z", "4.2.6": "2022-05-10T04:47:18.851Z", "4.2.7": "2022-05-10T16:08:52.405Z", "4.2.8": "2022-05-12T20:20:38.697Z", "4.2.9": "2022-05-18T16:19:52.282Z", "4.3.0": "2022-05-24T17:41:47.279Z", "4.3.1": "2022-06-20T17:59:00.080Z", "4.3.2": "2022-06-22T14:38:07.136Z", "4.3.3": "2022-07-17T23:33:39.514Z", "4.4.0": "2022-08-21T17:56:55.479Z", "4.4.1": "2022-08-21T20:46:54.209Z", "4.5.0": "2022-09-02T17:03:18.104Z", "4.5.1": "2022-11-06T23:47:27.971Z", "4.5.2": "2022-11-07T15:23:30.660Z", "4.5.3": "2023-07-16T15:39:35.756Z", "4.5.4": "2023-10-07T16:53:54.487Z", "5.0.0-rc.1": "2024-05-20T00:32:27.951Z", "5.0.0": "2024-08-20T23:19:58.028Z", "5.0.1": "2024-08-21T23:35:04.273Z", "5.0.2": "2024-09-22T21:55:37.703Z", "5.0.3": "2024-09-22T22:00:04.664Z", "5.1.0": "2024-10-05T23:14:55.083Z", "5.1.1": "2024-10-21T15:16:02.316Z", "5.1.2": "2024-10-21T19:29:29.378Z", "5.1.3": "2024-11-02T16:26:34.454Z", "5.2.0": "2024-11-09T20:49:37.969Z", "5.2.1": "2024-11-09T22:14:24.985Z", "5.2.2": "2024-12-12T01:36:53.484Z", "5.2.3": "2024-12-21T18:44:50.185Z"}, "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jaredwray.com"}, "license": "MIT", "homepage": "https://github.com/jaredwray/keyv", "keywords": ["key", "value", "store", "cache", "ttl", "key-value", "storage", "backend", "adapter", "redis", "mongodb", "sqlite", "mysql", "postgresql", "memory", "node-cache", "lru-cache", "lru", "cache-manager"], "repository": {"type": "git", "url": "git+https://github.com/jaredwray/keyv.git"}, "description": "Simple key-value storage with support for multiple backends", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}], "readme": "<h1 align=\"center\"><img width=\"250\" src=\"https://jaredwray.com/images/keyv.svg\" alt=\"keyv\"></h1>\n\n> Simple key-value storage with support for multiple backends\n\n[![build](https://github.com/jaredwray/keyv/actions/workflows/tests.yaml/badge.svg)](https://github.com/jaredwray/keyv/actions/workflows/tests.yaml)\n[![codecov](https://codecov.io/gh/jaredwray/keyv/branch/main/graph/badge.svg?token=bRzR3RyOXZ)](https://codecov.io/gh/jaredwray/keyv)\n[![npm](https://img.shields.io/npm/dm/keyv.svg)](https://www.npmjs.com/package/keyv)\n[![npm](https://img.shields.io/npm/v/keyv.svg)](https://www.npmjs.com/package/keyv)\n\nKeyv provides a consistent interface for key-value storage across multiple backends via storage adapters. It supports TTL based expiry, making it suitable as a cache or a persistent key-value store.\n\n# Features\n\nThere are a few existing modules similar to Keyv, however Keyv is different because it:\n\n- Isn't bloated\n- Has a simple Promise based API\n- Suitable as a TTL based cache or persistent key-value store\n- [Easily embeddable](#add-cache-support-to-your-module) inside another module\n- Works with any storage that implements the [`Map`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Map) API\n- Handles all JSON types plus `Buffer`\n- Supports namespaces\n- Wide range of [**efficient, well tested**](#official-storage-adapters) storage adapters\n- Connection errors are passed through (db failures won't kill your app)\n- Supports the current active LTS version of Node.js or higher\n\n# Usage\n\nInstall Keyv.\n\n```\nnpm install --save keyv\n```\n\nBy default everything is stored in memory, you can optionally also install a storage adapter.\n\n```\nnpm install --save @keyv/redis\nnpm install --save @keyv/valkey\nnpm install --save @keyv/mongo\nnpm install --save @keyv/sqlite\nnpm install --save @keyv/postgres\nnpm install --save @keyv/mysql\nnpm install --save @keyv/etcd\nnpm install --save @keyv/memcache\n```\n\nFirst, create a new Keyv instance. \n\n```js\nimport Keyv from 'keyv';\n```\n\n# Type-safe Usage\n\nYou can create a `Keyv` instance with a generic type to enforce type safety for the values stored. Additionally, both the `get` and `set` methods support specifying custom types for specific use cases.\n\n## Example with Instance-level Generic Type:\n\n```ts\nconst keyv = new Keyv<number>(); // Instance handles only numbers\nawait keyv.set('key1', 123);\nconst value = await keyv.get('key1'); // value is inferred as number\n```\n\n## Example with Method-level Generic Type:\n\nYou can also specify a type directly in the `get` or `set` methods, allowing flexibility for different types of values within the same instance.\n\n```ts\nconst keyv = new Keyv(); // Generic type not specified at instance level\n\nawait keyv.set<string>('key2', 'some string'); // Method-level type for this value\nconst strValue = await keyv.get<string>('key2'); // Explicitly typed as string\n\nawait keyv.set<number>('key3', 456); // Storing a number in the same instance\nconst numValue = await keyv.get<number>('key3'); // Explicitly typed as number\n```\n\nThis makes `Keyv` highly adaptable to different data types while maintaining type safety.\n\n# Using Storage Adapters\n\nOnce you have created your Keyv instance you can use it as a simple key-value store with `in-memory` by default. To use a storage adapter, create an instance of the adapter and pass it to the Keyv constructor. Here are some examples:\n\n```js\n// redis\nimport KeyvRedis from '@keyv/redis';\n\nconst keyv = new Keyv(new KeyvRedis('redis://user:pass@localhost:6379'));\n```\n\nYou can also pass in a storage adapter with other options such as `ttl` and `namespace` (example using `sqlite`):\n\n```js\n//sqlite\nimport KeyvSqlite from '@keyv/sqlite';\n\nconst keyvSqlite = new KeyvSqlite('sqlite://path/to/database.sqlite');\nconst keyv = new Keyv({ store: keyvSqlite, ttl: 5000, namespace: 'cache' });\n```\n\nTo handle an event you can do the following:\n\n```js\n// Handle DB connection errors\nkeyv.on('error', err => console.log('Connection Error', err));\n```\n\nNow lets do an end-to-end example using `Keyv` and the `Redis` storage adapter:\n\n```js\nimport Keyv from 'keyv';\nimport KeyvRedis from '@keyv/redis';\n\nconst keyvRedis = new KeyvRedis('redis://user:pass@localhost:6379');\nconst keyv = new Keyv({ store: keyvRedis });\n\nawait keyv.set('foo', 'expires in 1 second', 1000); // true\nawait keyv.set('foo', 'never expires'); // true\nawait keyv.get('foo'); // 'never expires'\nawait keyv.delete('foo'); // true\nawait keyv.clear(); // undefined\n```\n\nIt's is just that simple! Keyv is designed to be simple and easy to use.\n\n# Namespaces\n\nYou can namespace your Keyv instance to avoid key collisions and allow you to clear only a certain namespace while using the same database.\n\n```js\nconst users = new Keyv(new KeyvRedis('redis://user:pass@localhost:6379'), { namespace: 'users' });\nconst cache = new Keyv(new KeyvRedis('redis://user:pass@localhost:6379'), { namespace: 'cache' });\n\nawait users.set('foo', 'users'); // true\nawait cache.set('foo', 'cache'); // true\nawait users.get('foo'); // 'users'\nawait cache.get('foo'); // 'cache'\nawait users.clear(); // undefined\nawait users.get('foo'); // undefined\nawait cache.get('foo'); // 'cache'\n```\n\n# Events\n\nKeyv is a custom `EventEmitter` and will emit an `'error'` event if there is an error.\nIf there is no listener for the `'error'` event, an uncaught exception will be thrown.\nTo disable the `'error'` event, pass `emitErrors: false` in the constructor options.\n\n```js\nconst keyv = new Keyv({ emitErrors: false });\n```\n\nIn addition it will emit `clear` and `disconnect` events when the corresponding methods are called.\n\n```js\nconst keyv = new Keyv();\nconst handleConnectionError = err => console.log('Connection Error', err);\nconst handleClear = () => console.log('Cache Cleared');\nconst handleDisconnect = () => console.log('Disconnected');\n\nkeyv.on('error', handleConnectionError);\nkeyv.on('clear', handleClear);\nkeyv.on('disconnect', handleDisconnect);\n```\n\n# Hooks\n\nKeyv supports hooks for `get`, `set`, and `delete` methods. Hooks are useful for logging, debugging, and other custom functionality. Here is a list of all the hooks:\n\n```\nPRE_GET\nPOST_GET\nPRE_GET_MANY\nPOST_GET_MANY\nPRE_SET\nPOST_SET\nPRE_DELETE\nPOST_DELETE\n```\n\nYou can access this by importing `KeyvHooks` from the main Keyv package.\n\n```js\nimport Keyv, { KeyvHooks } from 'keyv';\n```\n\n```js\n//PRE_SET hook\nconst keyv = new Keyv();\nkeyv.hooks.addHandler(KeyvHooks.PRE_SET, (key, value) => console.log(`Setting key ${key} to ${value}`));\n\n//POST_SET hook\nconst keyv = new Keyv();\nkeyv.hooks.addHandler(KeyvHooks.POST_SET, (key, value) => console.log(`Set key ${key} to ${value}`));\n```\n\nIn these examples you can also manipulate the value before it is set. For example, you could add a prefix to all keys.\n\n```js\nconst keyv = new Keyv();\nkeyv.hooks.addHandler(KeyvHooks.PRE_SET, (key, value) => {\n  console.log(`Setting key ${key} to ${value}`);\n  key = `prefix-${key}`;\n});\n```\n\nNow this key will have prefix- added to it before it is set.\n\nIn `PRE_DELETE` and `POST_DELETE` hooks, the value could be a single item or an `Array`. This is based on the fact that `delete` can accept a single key or an `Array` of keys.\n\n\n# Custom Serializers\n\nKeyv uses [`buffer`](https://nodejs.org/api/buffer.html) for data serialization to ensure consistency across different backends.\n\nYou can optionally provide your own serialization functions to support extra data types or to serialize to something other than JSON.\n\n```js\nconst keyv = new Keyv({ serialize: JSON.stringify, deserialize: JSON.parse });\n```\n\n**Warning:** Using custom serializers means you lose any guarantee of data consistency. You should do extensive testing with your serialisation functions and chosen storage engine.\n\nIf you do not want to use serialization you can set the `serialize` and `deserialize` functions to `undefined`. This will also turn off compression.\n\n```js\nconst keyv = new Keyv();\nkeyv.serialize = undefined;\nkeyv.deserialize = undefined;\n```\n\n# Official Storage Adapters\n\nThe official storage adapters are covered by [over 150 integration tests](https://github.com/jaredwray/keyv/actions/workflows/tests.yaml) to guarantee consistent behaviour. They are lightweight, efficient wrappers over the DB clients making use of indexes and native TTLs where available.\n\nDatabase | Adapter | Native TTL\n---|---|---\nRedis | [@keyv/redis](https://github.com/jaredwray/keyv/tree/master/packages/redis) | Yes\nValkey | [@keyv/valkey](https://github.com/jaredwray/keyv/tree/master/packages/valkey) | Yes\nMongoDB | [@keyv/mongo](https://github.com/jaredwray/keyv/tree/master/packages/mongo) | Yes \nSQLite | [@keyv/sqlite](https://github.com/jaredwray/keyv/tree/master/packages/sqlite) | No \nPostgreSQL | [@keyv/postgres](https://github.com/jaredwray/keyv/tree/master/packages/postgres) | No \nMySQL | [@keyv/mysql](https://github.com/jaredwray/keyv/tree/master/packages/mysql) | No \nEtcd | [@keyv/etcd](https://github.com/jaredwray/keyv/tree/master/packages/etcd) | Yes\nMemcache | [@keyv/memcache](https://github.com/jaredwray/keyv/tree/master/packages/memcache) | Yes\n\n# Third-party Storage Adapters\n\nYou can also use third-party storage adapters or build your own. Keyv will wrap these storage adapters in TTL functionality and handle complex types internally.\n\n```js\nimport Keyv from 'keyv';\nimport myAdapter from 'my-adapter';\n\nconst keyv = new Keyv({ store: myAdapter });\n```\n\nAny store that follows the [`Map`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Map) api will work.\n\n```js\nnew Keyv({ store: new Map() });\n```\n\nFor example, [`quick-lru`](https://github.com/sindresorhus/quick-lru) is a completely unrelated module that implements the Map API.\n\n```js\nimport Keyv from 'keyv';\nimport QuickLRU from 'quick-lru';\n\nconst lru = new QuickLRU({ maxSize: 1000 });\nconst keyv = new Keyv({ store: lru });\n```\n\nThe following are third-party storage adapters compatible with Keyv:\n\n- [quick-lru](https://github.com/sindresorhus/quick-lru) - Simple \"Least Recently Used\" (LRU) cache\n- [keyv-file](https://github.com/zaaack/keyv-file) - File system storage adapter for Keyv\n- [keyv-dynamodb](https://www.npmjs.com/package/keyv-dynamodb) - DynamoDB storage adapter for Keyv\n- [keyv-lru](https://www.npmjs.com/package/keyv-lru) - LRU storage adapter for Keyv\n- [keyv-null](https://www.npmjs.com/package/keyv-null) - Null storage adapter for Keyv\n- [keyv-firestore ](https://github.com/goto-bus-stop/keyv-firestore) – Firebase Cloud Firestore adapter for Keyv\n- [keyv-mssql](https://github.com/pmorgan3/keyv-mssql) - Microsoft Sql Server adapter for Keyv\n- [keyv-azuretable](https://github.com/howlowck/keyv-azuretable) - Azure Table Storage/API adapter for Keyv\n- [keyv-arango](https://github.com/TimMikeladze/keyv-arango) - ArangoDB storage adapter for Keyv\n- [keyv-momento](https://github.com/momentohq/node-keyv-adaptor/) - Momento storage adapter for Keyv\n- [@resolid/keyv-sqlite](https://github.com/huijiewei/keyv-sqlite) - A new SQLite storage adapter for Keyv\n\n# Compression\n\nKeyv supports `gzip` and `brotli` compression. To enable compression, pass the `compress` option to the constructor.\n\n```js\nimport Keyv from 'keyv';\nimport KeyvGzip from '@keyv/compress-gzip';\n\nconst keyvGzip = new KeyvGzip();\nconst keyv = new Keyv({ compression: KeyvGzip });\n```\n\nYou can also pass a custom compression function to the `compression` option. Following the pattern of the official compression adapters.\n\n## Want to build your own CompressionAdapter? \n\nGreat! Keyv is designed to be easily extended. You can build your own compression adapter by following the pattern of the official compression adapters based on this interface:\n\n```typescript\ninterface CompressionAdapter {\n\tasync compress(value: any, options?: any);\n\tasync decompress(value: any, options?: any);\n\tasync serialize(value: any);\n\tasync deserialize(value: any);\n}\n```\n\nIn addition to the interface, you can test it with our compression test suite using @keyv/test-suite:\n\n```js\nimport { keyvCompresstionTests } from '@keyv/test-suite';\nimport KeyvGzip from '@keyv/compress-gzip';\n\nkeyvCompresstionTests(test, new KeyvGzip());\n```\n\n# API\n\n## new Keyv([storage-adapter], [options]) or new Keyv([options])\n\nReturns a new Keyv instance.\n\nThe Keyv instance is also an `EventEmitter` that will emit an `'error'` event if the storage adapter connection fails.\n\n## storage-adapter\n\nType: `KeyvStorageAdapter`<br />\nDefault: `undefined`\n\nThe connection string URI.\n\nMerged into the options object as options.uri.\n\n## .namespace\n\nType: `String`\nDefault: `'keyv'`\n\nThis is the namespace for the current instance. When you set it it will set it also on the storage adapter. This is the preferred way to set the namespace over `.opts.namespace`.\n\n## options\n\nType: `Object`\n\nThe options object is also passed through to the storage adapter. Check your storage adapter docs for any extra options.\n\n## options.namespace\n\nType: `String`<br />\nDefault: `'keyv'`\n\nNamespace for the current instance.\n\n## options.ttl\n\nType: `Number`<br />\nDefault: `undefined`\n\nDefault TTL. Can be overridden by specififying a TTL on `.set()`.\n\n## options.compression\n\nType: `@keyv/compress-<compression_package_name>`<br />\nDefault: `undefined`\n\nCompression package to use. See [Compression](#compression) for more details.\n\n## options.serialize\n\nType: `Function`<br />\nDefault: `JSON.stringify`\n\nA custom serialization function.\n\n## options.deserialize\n\nType: `Function`<br />\nDefault: `JSON.parse`\n\nA custom deserialization function.\n\n## options.store\n\nType: `Storage adapter instance`<br />\nDefault: `new Map()`\n\nThe storage adapter instance to be used by Keyv.\n\n# Keyv Instance\n\nKeys must always be strings. Values can be of any type.\n\n## .set(key, value, [ttl])\n\nSet a value.\n\nBy default keys are persistent. You can set an expiry TTL in milliseconds.\n\nReturns a promise which resolves to `true`.\n\n## .get(key, [options])\n\nReturns a promise which resolves to the retrieved value.\n\n### options.raw\n\nType: `Boolean`<br />\nDefault: `false`\n\nIf set to true the raw DB object Keyv stores internally will be returned instead of just the value.\n\nThis contains the TTL timestamp.\n\n## .delete(key)\n\nDeletes an entry.\n\nReturns a promise which resolves to `true` if the key existed, `false` if not.\n\n## .clear()\n\nDelete all entries in the current namespace.\n\nReturns a promise which is resolved when the entries have been cleared.\n\n## .iterator()\n\nIterate over all entries of the current namespace.\n\nReturns a iterable that can be iterated by for-of loops. For example:\n\n```js\n// please note that the \"await\" keyword should be used here\nfor await (const [key, value] of this.keyv.iterator()) {\n  console.log(key, value);\n};\n```\n\n# API - Properties\n\n## .namespace\n\nType: `String`\n\nThe namespace for the current instance. This will define the namespace for the current instance and the storage adapter. If you set the namespace to `undefined` it will no longer do key prefixing.\n\n```js\nconst keyv = new Keyv({ namespace: 'my-namespace' });\nconsole.log(keyv.namespace); // 'my-namespace'\n```\n\nhere is an example of setting the namespace to `undefined`:\n\n```js\nconst keyv = new Keyv();\nconsole.log(keyv.namespace); // 'keyv' which is default\nkeyv.namespace = undefined;\nconsole.log(keyv.namespace); // undefined\n```\n\n## .ttl\n\nType: `Number`<br />\nDefault: `undefined`\n\nDefault TTL. Can be overridden by specififying a TTL on `.set()`. If set to `undefined` it will never expire.\n\n```js\nconst keyv = new Keyv({ ttl: 5000 });\nconsole.log(keyv.ttl); // 5000\nkeyv.ttl = undefined;\nconsole.log(keyv.ttl); // undefined (never expires)\n```\n\n## .store\n\nType: `Storage adapter instance`<br />\nDefault: `new Map()`\n\nThe storage adapter instance to be used by Keyv. This will wire up the iterator, events, and more when a set happens. If it is not a valid Map or Storage Adapter it will throw an error. \n\n```js\nimport KeyvSqlite from '@keyv/sqlite';\nconst keyv = new Keyv();\nconsole.log(keyv.store instanceof Map); // true\nkeyv.store = new KeyvSqlite('sqlite://path/to/database.sqlite');\nconsole.log(keyv.store instanceof KeyvSqlite); // true\n```\n\n## .serialize\n\nType: `Function`<br />\nDefault: `JSON.stringify`\n\nA custom serialization function used for any value. \n\n```js\nconst keyv = new Keyv();\nconsole.log(keyv.serialize); // JSON.stringify\nkeyv.serialize = value => value.toString();\nconsole.log(keyv.serialize); // value => value.toString()\n```\n\n## .deserialize\n\nType: `Function`<br />\nDefault: `JSON.parse`\n\nA custom deserialization function used for any value.\n\n```js\nconst keyv = new Keyv();\nconsole.log(keyv.deserialize); // JSON.parse\nkeyv.deserialize = value => parseInt(value);\nconsole.log(keyv.deserialize); // value => parseInt(value)\n```\n\n## .compression\n\nType: `CompressionAdapter`<br />\nDefault: `undefined`\n\nthis is the compression package to use. See [Compression](#compression) for more details. If it is undefined it will not compress (default).\n\n```js\nimport KeyvGzip from '@keyv/compress-gzip';\n\nconst keyv = new Keyv();\nconsole.log(keyv.compression); // undefined\nkeyv.compression = new KeyvGzip();\nconsole.log(keyv.compression); // KeyvGzip\n```\n\n## .useKeyPrefix\n\nType: `Boolean`<br />\nDefault: `true`\n\nIf set to `true` Keyv will prefix all keys with the namespace. This is useful if you want to avoid collisions with other data in your storage.\n\n```js\nconst keyv = new Keyv({ useKeyPrefix: false });\nconsole.log(keyv.useKeyPrefix); // false\nkeyv.useKeyPrefix = true;\nconsole.log(keyv.useKeyPrefix); // true\n```\n\n# How to Contribute\n\nWe welcome contributions to Keyv! 🎉 Here are some guides to get you started with contributing:\n\n* [Contributing](https://github.com/jaredwray/keyv/blob/main/CONTRIBUTING.md) - Learn about how to contribute to Keyv\n* [Code of Conduct](https://github.com/jaredwray/keyv/blob/main/CODE_OF_CONDUCT.md) - Learn about the Keyv Code of Conduct\n* [How to Contribute](https://github.com/jaredwray/keyv/blob/main/README.md) - How do develop in the Keyv mono repo! \n\n# License\n\n[MIT © Jared Wray](LICENSE)\n", "readmeFilename": "README.md", "users": {"ash": true, "rummik": true, "shanewholloway": true}}