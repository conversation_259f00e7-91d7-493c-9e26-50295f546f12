{"_id": "side-channel-weakmap", "_rev": "2-03a85ec2ebf0511223fe6733b626f945", "name": "side-channel-weakmap", "dist-tags": {"latest": "1.0.2"}, "versions": {"1.0.0": {"name": "side-channel-weakmap", "version": "1.0.0", "keywords": ["weakmap", "map", "side", "channel", "metadata"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "side-channel-weakmap@1.0.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/side-channel-weakmap#readme", "bugs": {"url": "https://github.com/ljharb/side-channel-weakmap/issues"}, "dist": {"shasum": "531137685635cd42515ed7f6fc7ae19cd3ab837d", "tarball": "https://registry.npmjs.org/side-channel-weakmap/-/side-channel-weakmap-1.0.0.tgz", "fileCount": 12, "integrity": "sha512-lvUwe8p1cHNQKdC317/Nt4B6vD3/o6zs4Rhc6M1JwospZ87d8l/w3sDKOLbsPTBpCG7Xn5g1nVKgF5qOAe4AiA==", "signatures": [{"sig": "MEUCIQCnS0PqrDt5Q0a7niev5WS/w76umBSgdYq2G1Tv7hNPzQIgfPONnlYytl/U1N3va0jqpgOOYgySmUmW+qtr734yocY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14019}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "113a6c464a71e45a62c25d18a3c151b30f680e95", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "tsc -p . && attw -P", "posttest": "npx npm@'>=10.2' audit --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/side-channel-weakmap.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Store information about any JS value in a side channel. Uses WeakMap if available.", "directories": {}, "_nodeVersion": "23.4.0", "dependencies": {"es-errors": "^1.3.0", "call-bound": "^1.0.2", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.0"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"nyc": "^10.3.2", "tape": "^5.9.0", "eclint": "^2.8.1", "eslint": "=8.8.0", "encoding": "^0.1.13", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "next", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "@ljharb/tsconfig": "^0.2.2", "@types/call-bind": "^1.0.5", "safe-publish-latest": "^2.0.0", "@types/get-intrinsic": "^1.2.3", "@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@types/object-inspect": "^1.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/side-channel-weakmap_1.0.0_1733868939857_0.*****************", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.1": {"name": "side-channel-weakmap", "version": "1.0.1", "keywords": ["weakmap", "map", "side", "channel", "metadata"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "side-channel-weakmap@1.0.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/side-channel-weakmap#readme", "bugs": {"url": "https://github.com/ljharb/side-channel-weakmap/issues"}, "dist": {"shasum": "3b08f3f51386da21f17e0cc1f78492368dad00f2", "tarball": "https://registry.npmjs.org/side-channel-weakmap/-/side-channel-weakmap-1.0.1.tgz", "fileCount": 12, "integrity": "sha512-1v3pa5azlNqqdoT8A1K5uIxdu6uyBCg3ulmYdYgpOTYQKrIVQTwly+N8oHm0/ALjFzAm+Ogg/UlMVX9nqwFY5g==", "signatures": [{"sig": "MEQCIBFrzlOkgctkx+KYY9yDbNRfw9xP1XB8fgskjEP0Fx9fAiB9wcPkKN/dGzNJuj7XvgaD3ALZO5TRS8Htlzvx5Va8IQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14415}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "1cfd735c57d4b0ee92c442ec10607be0d0629b76", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "tsc -p . && attw -P", "posttest": "npx npm@'>=10.2' audit --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/side-channel-weakmap.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Store information about any JS value in a side channel. Uses WeakMap if available.", "directories": {}, "_nodeVersion": "23.4.0", "dependencies": {"es-errors": "^1.3.0", "call-bound": "^1.0.2", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.1"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"nyc": "^10.3.2", "tape": "^5.9.0", "eclint": "^2.8.1", "eslint": "=8.8.0", "encoding": "^0.1.13", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "next", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "@ljharb/tsconfig": "^0.2.2", "@types/call-bind": "^1.0.5", "safe-publish-latest": "^2.0.0", "@types/get-intrinsic": "^1.2.3", "@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@types/object-inspect": "^1.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/side-channel-weakmap_1.0.1_1733893837718_0.7478232686957718", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.2": {"name": "side-channel-weakmap", "version": "1.0.2", "description": "Store information about any JS value in a side channel. Uses WeakMap if available.", "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "types": "./index.d.ts", "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && attw -P", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "npx npm@'>=10.2' audit --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/side-channel-weakmap.git"}, "keywords": ["weakmap", "map", "side", "channel", "metadata"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/side-channel-weakmap/issues"}, "homepage": "https://github.com/ljharb/side-channel-weakmap#readme", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.1"}, "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/call-bind": "^1.0.5", "@types/get-intrinsic": "^1.2.3", "@types/object-inspect": "^1.13.0", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "eclint": "^2.8.1", "encoding": "^0.1.13", "eslint": "=8.8.0", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "engines": {"node": ">= 0.4"}, "_id": "side-channel-weakmap@1.0.2", "gitHead": "ca1790715cf6f3cb35a4acd4fd0731790f9814b3", "_nodeVersion": "23.4.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==", "shasum": "11dda19d5368e40ce9ec2bdc1fb0ecbc0790ecea", "tarball": "https://registry.npmjs.org/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz", "fileCount": 12, "unpackedSize": 14667, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD9ighOR+5F/NNtDuqeh0Xi+6vI25HMiyQQWkmnwmPvOQIgRWs5RN7qMoTC5yz3gvh9nCYcCuiFkvPsvv2sJFlReo8="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/side-channel-weakmap_1.0.2_1733895551318_0.16134513836504416"}, "_hasShrinkwrap": false}}, "time": {"created": "2024-12-10T22:15:39.856Z", "modified": "2024-12-11T05:39:11.658Z", "1.0.0": "2024-12-10T22:15:40.021Z", "1.0.1": "2024-12-11T05:10:37.904Z", "1.0.2": "2024-12-11T05:39:11.495Z"}, "bugs": {"url": "https://github.com/ljharb/side-channel-weakmap/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/ljharb/side-channel-weakmap#readme", "keywords": ["weakmap", "map", "side", "channel", "metadata"], "repository": {"type": "git", "url": "git+https://github.com/ljharb/side-channel-weakmap.git"}, "description": "Store information about any JS value in a side channel. Uses WeakMap if available.", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "readme": "# side-channel-weakmap <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\nStore information about any JS value in a side channel. Uses WeakMap if available.\n\nWarning: this implementation will leak memory until you `delete` the `key`.\nUse [`side-channel`](https://npmjs.com/side-channel) for the best available strategy.\n\n## Getting started\n\n```sh\nnpm install --save side-channel-weakmap\n```\n\n## Usage/Examples\n\n```js\nconst assert = require('assert');\nconst getSideChannelList = require('side-channel-weakmap');\n\nconst channel = getSideChannelList();\n\nconst key = {};\nassert.equal(channel.has(key), false);\nassert.throws(() => channel.assert(key), TypeError);\n\nchannel.set(key, 42);\n\nchannel.assert(key); // does not throw\nassert.equal(channel.has(key), true);\nassert.equal(channel.get(key), 42);\n\nchannel.delete(key);\nassert.equal(channel.has(key), false);\nassert.throws(() => channel.assert(key), TypeError);\n```\n\n## Tests\n\nClone the repo, `npm install`, and run `npm test`\n\n[package-url]: https://npmjs.org/package/side-channel-weakmap\n[npm-version-svg]: https://versionbadg.es/ljharb/side-channel-weakmap.svg\n[deps-svg]: https://david-dm.org/ljharb/side-channel-weakmap.svg\n[deps-url]: https://david-dm.org/ljharb/side-channel-weakmap\n[dev-deps-svg]: https://david-dm.org/ljharb/side-channel-weakmap/dev-status.svg\n[dev-deps-url]: https://david-dm.org/ljharb/side-channel-weakmap#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/side-channel-weakmap.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/side-channel-weakmap.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/side-channel-weakmap.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=side-channel-weakmap\n[codecov-image]: https://codecov.io/gh/ljharb/side-channel-weakmap/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/ljharb/side-channel-weakmap/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/ljharb/side-channel-weakmap\n[actions-url]: https://github.com/ljharb/side-channel-weakmap/actions\n", "readmeFilename": "README.md"}