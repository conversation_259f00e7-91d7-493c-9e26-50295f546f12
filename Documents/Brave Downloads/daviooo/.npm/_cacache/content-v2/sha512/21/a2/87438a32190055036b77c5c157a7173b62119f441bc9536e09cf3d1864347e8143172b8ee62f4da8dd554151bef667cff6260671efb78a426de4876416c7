{"_id": "chart.js", "_rev": "194-ced434d303b99d483e28e918fc9f5b22", "name": "chart.js", "dist-tags": {"next": "4.0.0-release", "latest": "4.4.9"}, "versions": {"1.0.1-beta.2": {"name": "chart.js", "version": "1.0.1-beta.2", "_id": "chart.js@1.0.1-beta.2", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}], "homepage": "http://www.chartjs.org", "bugs": {"url": "https://github.com/nnnick/Chart.js/issues"}, "dist": {"shasum": "26d752ec3a343079be489892b3020548e1106259", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-1.0.1-beta.2.tgz", "integrity": "sha512-gUFlEoQY4/SgsAoSB6kqRjgWdHbW6J/r6tQjKZdybmhIBH+JxFw9yffwq/S13Gh4Zc7TPm7DPECrR7YkEWXNTA==", "signatures": [{"sig": "MEUCICeVrM8yyPWp7BNlCFyHk+LuMDiJTSczuhVdgiFG/P3XAiEA6dtA6Uojh2WKrOL/QupHwusZBp34d/7qpTj0lZZwf4U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "26d752ec3a343079be489892b3020548e1106259", "gitHead": "529d582f03eacddb49091203d70ff9bc15dff757", "scripts": {}, "_npmUser": {"name": "nnnick", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nnnick/Chart.js.git", "type": "git"}, "_npmVersion": "1.4.20", "dependences": {}, "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "devDependencies": {"gulp": "3.5.x", "gulp-size": "~0.4.0", "gulp-util": "~2.2.x", "gulp-concat": "~2.1.x", "gulp-jshint": "~1.5.1", "gulp-uglify": "~0.2.x", "gulp-connect": "~2.0.5"}}, "1.0.1": {"name": "chart.js", "version": "1.0.1", "_id": "chart.js@1.0.1", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}], "homepage": "http://www.chartjs.org", "bugs": {"url": "https://github.com/nnnick/Chart.js/issues"}, "dist": {"shasum": "b09d83bf4aa7e7ece1e06b2f6898c2dfa5750c05", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-1.0.1.tgz", "integrity": "sha512-9m9zyHXaH/cqJXGOFVDCb3b2q/WtiTaqZOJ3GnFLxaM1pg/HTerVL6WCNtK9C31eAhkneF/6Z4sB2Ohrru4SOQ==", "signatures": [{"sig": "MEUCIEZtJwiUbtJnO1phE31CQ/pzLC+90wYRKNrmcgDb9hRzAiEA1izcoWfDD1P6RS6VqdorHhM7QrenPVT7fh6wqt6tikQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "Chart.js", "_from": ".", "_shasum": "b09d83bf4aa7e7ece1e06b2f6898c2dfa5750c05", "gitHead": "02f858b264de6c6c1af0579b05b7b5913d0d61ef", "scripts": {}, "_npmUser": {"name": "nnnick", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nnnick/Chart.js.git", "type": "git"}, "_npmVersion": "1.4.20", "dependences": {}, "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "devDependencies": {"gulp": "3.5.x", "semver": "^3.0.1", "inquirer": "^0.5.1", "gulp-size": "~0.4.0", "gulp-util": "~2.2.x", "gulp-concat": "~2.1.x", "gulp-jshint": "~1.5.1", "gulp-uglify": "~0.2.x", "gulp-connect": "~2.0.5", "gulp-replace": "^0.4.0", "gulp-html-validator": "^0.0.2"}}, "1.0.2": {"name": "chart.js", "version": "1.0.2", "_id": "chart.js@1.0.2", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}], "homepage": "http://www.chartjs.org", "bugs": {"url": "https://github.com/nnnick/Chart.js/issues"}, "spm": {"main": "Chart.js"}, "dist": {"shasum": "ad57d2229cfd8ccf5955147e8121b4911e69dfe7", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-1.0.2.tgz", "integrity": "sha512-DThQjhXwGtKyFCx3F8uhIcTTcFEr1Elifv66KG+2akTsA0pRRLits5MjN9sU/xsX1SIRIUcpwcOBvhulACHoSA==", "signatures": [{"sig": "MEUCIGu61jVXYuxsGg9yOwvxuD1ncYfkj5Rho5iS5392wcVBAiEAqjks/uQvZyvdxTqEM2oBjBk30eIdnsQQpPgRzl9glio=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "Chart.js", "_from": ".", "_shasum": "ad57d2229cfd8ccf5955147e8121b4911e69dfe7", "gitHead": "930b16a0af59201dcfcd1594b0e7540db4d04c9f", "scripts": {}, "_npmUser": {"name": "nnnick", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nnnick/Chart.js.git", "type": "git"}, "_npmVersion": "1.4.20", "dependences": {}, "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "devDependencies": {"gulp": "3.5.x", "semver": "^3.0.1", "inquirer": "^0.5.1", "gulp-size": "~0.4.0", "gulp-util": "~2.2.x", "gulp-concat": "~2.1.x", "gulp-jshint": "~1.5.1", "gulp-uglify": "~0.2.x", "gulp-connect": "~2.0.5", "gulp-replace": "^0.4.0", "gulp-html-validator": "^0.0.2"}}, "1.1.0": {"name": "chart.js", "version": "1.1.0", "license": "MIT", "_id": "chart.js@1.1.0", "maintainers": [{"name": "etimberg", "email": "<EMAIL>"}, {"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}], "homepage": "http://www.chartjs.org", "bugs": {"url": "https://github.com/nnnick/Chart.js/issues"}, "spm": {"main": "Chart.js"}, "dist": {"shasum": "50a868dba9a07c69e40b1c4b29d2948627a6140d", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-1.1.0.tgz", "integrity": "sha512-Cq6mYOSdsfhkZaccz2vU2CojnGPRTrcmuL1vmaL11tY1x14QeazCNe+MeCPkkMUm6JcnQUV9PLzlc9n35xpo+A==", "signatures": [{"sig": "MEQCIG9OTMx/FuX/muBnYmsoh0c3WlxyTm1NNJp8VdjKoReGAiAZZE7mAKLLVDnVJT8iH8MFfzyZ4ZciFnjK/tnLueRTTw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "Chart.js", "_from": ".", "_shasum": "50a868dba9a07c69e40b1c4b29d2948627a6140d", "gitHead": "ef923dfe289fc41326be58f3f6e4d991c8eb741f", "scripts": {}, "_npmUser": {"name": "nnnick", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nnnick/Chart.js.git", "type": "git"}, "_npmVersion": "3.7.3", "dependences": {}, "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "5.6.0", "devDependencies": {"gulp": "3.9.x", "semver": "^3.0.1", "inquirer": "^0.5.1", "gulp-size": "~0.4.0", "gulp-util": "~2.2.x", "gulp-concat": "~2.1.x", "gulp-jshint": "~1.5.1", "gulp-uglify": "~1.5.x", "gulp-connect": "~2.0.5", "gulp-replace": "^0.4.0", "gulp-livereload": "^3.8.0", "gulp-html-validator": "^0.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js-1.1.0.tgz_1459526924335_0.7063350817188621", "host": "packages-12-west.internal.npmjs.com"}}, "1.1.1": {"name": "chart.js", "version": "1.1.1", "license": "MIT", "_id": "chart.js@1.1.1", "maintainers": [{"name": "etimberg", "email": "<EMAIL>"}, {"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}], "homepage": "http://www.chartjs.org", "bugs": {"url": "https://github.com/nnnick/Chart.js/issues"}, "spm": {"main": "Chart.js"}, "dist": {"shasum": "a9b17054220bd45cbdb176fd6bcb8783ef871a7d", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-1.1.1.tgz", "integrity": "sha512-1lcx4nsmYk3C50xMWFucOccdU7jh+RoHP25IiPlUoE6Qf317my+Nm48JOyPFrBRscqy6O4kkxif+omPKPkqWMA==", "signatures": [{"sig": "MEUCIA4/yc5fyjYDpSV+sZQ0+sf35IXOhEl3uEeCzsOtjrTuAiEA3h30tyUOmukEiJvEJyC/AoxSdsNzcq7AaLXl4iH6Gvk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "Chart.js", "_from": ".", "_shasum": "a9b17054220bd45cbdb176fd6bcb8783ef871a7d", "gitHead": "a62537a80029cd5a2e230769a652904e2de2d5d4", "scripts": {}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/nnnick/Chart.js.git", "type": "git"}, "_npmVersion": "3.7.3", "dependences": {}, "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "4.2.6", "devDependencies": {"gulp": "3.9.x", "semver": "^3.0.1", "inquirer": "^0.5.1", "gulp-size": "~0.4.0", "gulp-util": "~2.2.x", "gulp-concat": "~2.1.x", "gulp-jshint": "~1.5.1", "gulp-uglify": "~1.5.x", "gulp-connect": "~2.0.5", "gulp-replace": "^0.4.0", "gulp-livereload": "^3.8.0", "gulp-html-validator": "^0.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js-1.1.1.tgz_1459896878690_0.1684930867049843", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.0": {"name": "chart.js", "version": "2.0.0", "_id": "chart.js@2.0.0", "maintainers": [{"name": "etimberg", "email": "<EMAIL>"}, {"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}], "homepage": "http://www.chartjs.org", "bugs": {"url": "https://github.com/nnnick/Chart.js/issues"}, "spm": {"main": "Chart.js"}, "dist": {"shasum": "02328f25860a3f3dad822f373cb58f5d5315bb3a", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-2.0.0.tgz", "integrity": "sha512-CoWjnRvFwgngHpiNAcWFt7+VkmKX4mho+70seaT27+WZSiVN2a1fcLQOUfKq3k5u4lsJP1cgaP9+ztEgL09/1A==", "signatures": [{"sig": "MEUCIQCpZO4j+yf0y6m+/FZgGV5/M27uFz/QW0k8XdQIY8e8GQIgepKvNIbOctukttwTJZFhAImuP/eSyuTP+UOpoZRv7sA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/chart.js", "_from": ".", "_shasum": "02328f25860a3f3dad822f373cb58f5d5315bb3a", "gitHead": "ebb8ca233d769b8c565b84393f1ceedbe5359bc9", "scripts": {}, "_npmUser": {"name": "etimberg", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nnnick/Chart.js.git", "type": "git"}, "_npmVersion": "3.6.0", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "5.6.0", "dependencies": {"moment": "^2.10.6", "chartjs-color": "^1.0.2"}, "devDependencies": {"gulp": "3.9.x", "karma": "^0.12.37", "jquery": "^2.1.4", "semver": "^3.0.1", "jasmine": "^2.3.2", "gulp-umd": "~0.2.0", "inquirer": "^0.5.1", "watchify": "^3.7.0", "coveralls": "^2.11.6", "gulp-size": "~0.4.0", "gulp-util": "~2.2.x", "browserify": "^13.0.0", "gulp-karma": "0.0.4", "gulp-concat": "~2.1.x", "gulp-jshint": "~1.5.1", "gulp-uglify": "~0.2.x", "gulp-connect": "~2.0.5", "gulp-replace": "^0.4.0", "jasmine-core": "^2.3.4", "merge-stream": "^1.0.0", "karma-jasmine": "^0.3.6", "gulp-streamify": "^1.0.2", "jshint-stylish": "~2.1.0", "karma-coverage": "^0.5.1", "karma-browserify": "^5.0.1", "browserify-istanbul": "^0.2.1", "gulp-html-validator": "^0.0.2", "vinyl-source-stream": "^1.1.0", "karma-chrome-launcher": "^0.2.0", "karma-firefox-launcher": "^0.1.6", "karma-jasmine-html-reporter": "^0.1.8"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js-2.0.0.tgz_1460203742194_0.8092795952688903", "host": "packages-16-east.internal.npmjs.com"}}, "2.0.1": {"name": "chart.js", "version": "2.0.1", "_id": "chart.js@2.0.1", "maintainers": [{"name": "etimberg", "email": "<EMAIL>"}, {"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}], "homepage": "http://www.chartjs.org", "bugs": {"url": "https://github.com/nnnick/Chart.js/issues"}, "spm": {"main": "Chart.js"}, "dist": {"shasum": "c23377e266f4a5516fec38fa0c1c74461500e419", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-2.0.1.tgz", "integrity": "sha512-RbQzh6wQdT10Myb+Dg1egD5uDeGTrWiFNDSwLr3kXooZ8s0PMRqmF8bq6CVOAhzEPsGtO8MAQDmfJYLkRWgdKw==", "signatures": [{"sig": "MEUCIQCSTvpjS+bQKqijVMHuwt+r+BvVretd2r7PPGOEaa1ufQIgFxDJOUV0brmZveFqTOVW1sTdNes4BWMnYPESC68U7kU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/chart.js", "_from": ".", "_shasum": "c23377e266f4a5516fec38fa0c1c74461500e419", "gitHead": "5e8bfea53e9c18469c5a20cfc3092a7be0f240c9", "scripts": {}, "_npmUser": {"name": "etimberg", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nnnick/Chart.js.git", "type": "git"}, "_npmVersion": "3.6.0", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "5.6.0", "dependencies": {"moment": "^2.10.6", "chartjs-color": "^1.0.2"}, "devDependencies": {"gulp": "3.9.x", "karma": "^0.12.37", "jquery": "^2.1.4", "semver": "^3.0.1", "jasmine": "^2.3.2", "gulp-umd": "~0.2.0", "inquirer": "^0.5.1", "watchify": "^3.7.0", "coveralls": "^2.11.6", "gulp-size": "~0.4.0", "gulp-util": "~2.2.x", "browserify": "^13.0.0", "gulp-karma": "0.0.4", "gulp-concat": "~2.1.x", "gulp-jshint": "~1.5.1", "gulp-uglify": "~0.2.x", "gulp-connect": "~2.0.5", "gulp-replace": "^0.4.0", "jasmine-core": "^2.3.4", "merge-stream": "^1.0.0", "karma-jasmine": "^0.3.6", "gulp-streamify": "^1.0.2", "jshint-stylish": "~2.1.0", "karma-coverage": "^0.5.1", "karma-browserify": "^5.0.1", "browserify-istanbul": "^0.2.1", "gulp-html-validator": "^0.0.2", "vinyl-source-stream": "^1.1.0", "karma-chrome-launcher": "^0.2.0", "karma-firefox-launcher": "^0.1.6", "karma-jasmine-html-reporter": "^0.1.8"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js-2.0.1.tgz_1460834816576_0.38206038367934525", "host": "packages-16-east.internal.npmjs.com"}}, "2.0.2": {"name": "chart.js", "version": "2.0.2", "_id": "chart.js@2.0.2", "maintainers": [{"name": "etimberg", "email": "<EMAIL>"}, {"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}], "homepage": "http://www.chartjs.org", "bugs": {"url": "https://github.com/nnnick/Chart.js/issues"}, "spm": {"main": "Chart.js"}, "dist": {"shasum": "47f1d49aa5fcf513c5c5ea518436699329edc52b", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-2.0.2.tgz", "integrity": "sha512-DaPAa5yXisNGR3ow34e5d8oVmh7RKOJpGBGeLPmHulsUt3Ii7/Z6DqM03yBe5PAlbNFvEZBG9vVXCrWVwb3VrQ==", "signatures": [{"sig": "MEUCIBHpT5TcCsqk9bysQYNLTPjBHx746myDwiVfWI77yQM8AiEA3KVm88YwaCuzoiuS7mepcHI5rmQftRTJl1TkB6iCkf8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/chart.js", "_from": ".", "_shasum": "47f1d49aa5fcf513c5c5ea518436699329edc52b", "gitHead": "67625063a3c1242ca13858aa629668007b5b8106", "scripts": {}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/nnnick/Chart.js.git", "type": "git"}, "_npmVersion": "3.7.3", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "4.2.6", "dependencies": {"moment": "^2.10.6", "chartjs-color": "^1.0.2"}, "devDependencies": {"gulp": "3.9.x", "karma": "^0.12.37", "jquery": "^2.1.4", "semver": "^3.0.1", "jasmine": "^2.3.2", "gulp-umd": "~0.2.0", "inquirer": "^0.5.1", "watchify": "^3.7.0", "coveralls": "^2.11.6", "gulp-size": "~0.4.0", "gulp-util": "~2.2.x", "browserify": "^13.0.0", "gulp-karma": "0.0.4", "gulp-concat": "~2.1.x", "gulp-jshint": "~1.5.1", "gulp-uglify": "~0.2.x", "gulp-connect": "~2.0.5", "gulp-replace": "^0.4.0", "jasmine-core": "^2.3.4", "merge-stream": "^1.0.0", "karma-jasmine": "^0.3.6", "gulp-streamify": "^1.0.2", "jshint-stylish": "~2.1.0", "karma-coverage": "^0.5.1", "karma-browserify": "^5.0.1", "browserify-istanbul": "^0.2.1", "gulp-html-validator": "^0.0.2", "vinyl-source-stream": "^1.1.0", "karma-chrome-launcher": "^0.2.0", "karma-firefox-launcher": "^0.1.6", "karma-jasmine-html-reporter": "^0.1.8"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js-2.0.2.tgz_1460846772379_0.007163290865719318", "host": "packages-16-east.internal.npmjs.com"}}, "2.1.0": {"name": "chart.js", "version": "2.1.0", "license": "MIT", "_id": "chart.js@2.1.0", "maintainers": [{"name": "etimberg", "email": "<EMAIL>"}, {"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}], "homepage": "http://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "spm": {"main": "Chart.js"}, "dist": {"shasum": "806918899479e3fdb72c3543ff80adb2beccd76c", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-2.1.0.tgz", "integrity": "sha512-lyMoQIaXT75C7QnixJ/3D5Yjg88qZ2EYgS3yRBhRK+if4vg0oe3TgZXNRVg5R/ktVmKFyPs91tq29fGJ0/63Ug==", "signatures": [{"sig": "MEUCIQDgBo8IsCLJqg4DzLTNrkvYnNjP2XYS0KzEP3vmXH3U4wIgM1dwh1ECBtpT05KvBmRu195OnVd3y5ZEq0dIWMYGeJM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/chart.js", "_from": ".", "_shasum": "806918899479e3fdb72c3543ff80adb2beccd76c", "gitHead": "2ee37e12e379ca9b8ad3be7105715bf8c543ead0", "scripts": {}, "_npmUser": {"name": "etimberg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "3.6.0", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "5.6.0", "dependencies": {"moment": "^2.10.6", "chartjs-color": "^1.0.2"}, "devDependencies": {"gulp": "3.9.x", "karma": "^0.12.37", "jquery": "^2.1.4", "semver": "^3.0.1", "jasmine": "^2.3.2", "gulp-umd": "~0.2.0", "inquirer": "^0.5.1", "watchify": "^3.7.0", "coveralls": "^2.11.6", "gulp-size": "~0.4.0", "gulp-util": "~2.2.x", "browserify": "^13.0.0", "gulp-karma": "0.0.4", "gulp-concat": "~2.1.x", "gulp-insert": "~0.5.0", "gulp-jshint": "~1.5.1", "gulp-uglify": "~0.2.x", "gulp-connect": "~2.0.5", "gulp-replace": "^0.4.0", "jasmine-core": "^2.3.4", "merge-stream": "^1.0.0", "karma-jasmine": "^0.3.6", "gulp-streamify": "^1.0.2", "jshint-stylish": "~2.1.0", "karma-coverage": "^0.5.1", "karma-browserify": "^5.0.1", "browserify-istanbul": "^0.2.1", "gulp-html-validator": "^0.0.2", "vinyl-source-stream": "^1.1.0", "karma-chrome-launcher": "^0.2.0", "karma-firefox-launcher": "^0.1.6", "karma-jasmine-html-reporter": "^0.1.8"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js-2.1.0.tgz_1462317628644_0.11133707570843399", "host": "packages-12-west.internal.npmjs.com"}}, "2.1.1": {"name": "chart.js", "version": "2.1.1", "license": "MIT", "_id": "chart.js@2.1.1", "maintainers": [{"name": "etimberg", "email": "<EMAIL>"}, {"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}], "homepage": "http://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "spm": {"main": "Chart.js"}, "dist": {"shasum": "6859d22858f49ab9db0d1c58a3d29076e68f74a2", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-2.1.1.tgz", "integrity": "sha512-6j+BDh4g2tAcV3gY1DrYQt5gJgP362qaL8MX07vEfuCansle4XPKGKTEz3bqtZ0qKfsYRmWTg91MRWcz1ZdALw==", "signatures": [{"sig": "MEQCIHnMp5lr6rsNvdKgxRCaPaHNcrB+sT8s76/GJxqUAS1FAiB8jaJP8HDLeG7cqUhq/yZaa/7zgY5CBQjfUYQs/IgpjQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/chart.js", "_from": ".", "_shasum": "6859d22858f49ab9db0d1c58a3d29076e68f74a2", "gitHead": "29ab84c4c5504850e58935f9e59e4dbb22cb31d1", "scripts": {}, "_npmUser": {"name": "etimberg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "3.6.0", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "5.6.0", "dependencies": {"moment": "^2.10.6", "chartjs-color": "^1.0.2"}, "devDependencies": {"gulp": "3.9.x", "karma": "^0.12.37", "jquery": "^2.1.4", "semver": "^3.0.1", "jasmine": "^2.3.2", "gulp-umd": "~0.2.0", "inquirer": "^0.5.1", "watchify": "^3.7.0", "coveralls": "^2.11.6", "gulp-size": "~0.4.0", "gulp-util": "~2.2.x", "browserify": "^13.0.0", "gulp-karma": "0.0.4", "gulp-concat": "~2.1.x", "gulp-insert": "~0.5.0", "gulp-jshint": "~1.5.1", "gulp-uglify": "~0.2.x", "gulp-connect": "~2.0.5", "gulp-replace": "^0.4.0", "jasmine-core": "^2.3.4", "merge-stream": "^1.0.0", "karma-jasmine": "^0.3.6", "gulp-streamify": "^1.0.2", "jshint-stylish": "~2.1.0", "karma-coverage": "^0.5.1", "karma-browserify": "^5.0.1", "browserify-istanbul": "^0.2.1", "gulp-html-validator": "^0.0.2", "vinyl-source-stream": "^1.1.0", "karma-chrome-launcher": "^0.2.0", "karma-firefox-launcher": "^0.1.6", "karma-jasmine-html-reporter": "^0.1.8"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js-2.1.1.tgz_1462623548997_0.9562391776125878", "host": "packages-16-east.internal.npmjs.com"}}, "2.1.2": {"name": "chart.js", "version": "2.1.2", "license": "MIT", "_id": "chart.js@2.1.2", "maintainers": [{"name": "etimberg", "email": "<EMAIL>"}, {"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}], "homepage": "http://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "spm": {"main": "Chart.js"}, "dist": {"shasum": "d3511268d0baa57769b8ce2842323e4e8ccfb3a1", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-2.1.2.tgz", "integrity": "sha512-Uvn/FsubCixYRXFcxLeeBYRqsYYGr1ayrberPNBVVR2XtfUdhp45XEpTTz2K+H4Gk+Bm7t3yjD5TFGcYhCtZ2A==", "signatures": [{"sig": "MEUCID3B80Q5vX7KOepVpAFP0ZQ+a/1yA6lhwvKFnKf0CpljAiEAlwLo3tfuTFyCUMcY4SGAj1pKVTmoU5HIia7zRpUGIxc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/chart.js", "_from": ".", "_shasum": "d3511268d0baa57769b8ce2842323e4e8ccfb3a1", "gitHead": "8ffbe234931036a73147401f11dac0562715aeb4", "scripts": {}, "_npmUser": {"name": "etimberg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "3.6.0", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "5.6.0", "dependencies": {"moment": "^2.10.6", "chartjs-color": "^1.0.2"}, "devDependencies": {"gulp": "3.9.x", "karma": "^0.12.37", "jquery": "^2.1.4", "semver": "^3.0.1", "jasmine": "^2.3.2", "gulp-umd": "~0.2.0", "inquirer": "^0.5.1", "watchify": "^3.7.0", "coveralls": "^2.11.6", "gulp-size": "~0.4.0", "gulp-util": "~2.2.x", "browserify": "^13.0.0", "gulp-karma": "0.0.4", "gulp-concat": "~2.1.x", "gulp-insert": "~0.5.0", "gulp-jshint": "~1.5.1", "gulp-uglify": "~0.2.x", "gulp-connect": "~2.0.5", "gulp-replace": "^0.4.0", "jasmine-core": "^2.3.4", "merge-stream": "^1.0.0", "karma-jasmine": "^0.3.6", "gulp-streamify": "^1.0.2", "jshint-stylish": "~2.1.0", "karma-coverage": "^0.5.1", "karma-browserify": "^5.0.1", "browserify-istanbul": "^0.2.1", "gulp-html-validator": "^0.0.2", "vinyl-source-stream": "^1.1.0", "karma-chrome-launcher": "^0.2.0", "karma-firefox-launcher": "^0.1.6", "karma-jasmine-html-reporter": "^0.1.8"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js-2.1.2.tgz_1462721499689_0.6925138481892645", "host": "packages-12-west.internal.npmjs.com"}}, "2.1.3": {"name": "chart.js", "version": "2.1.3", "license": "MIT", "_id": "chart.js@2.1.3", "maintainers": [{"name": "etimberg", "email": "<EMAIL>"}, {"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}], "homepage": "http://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "spm": {"main": "Chart.js"}, "dist": {"shasum": "710027edfc159d6f949c6537729431c72d4dca49", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-2.1.3.tgz", "integrity": "sha512-mSrhIl4Vn+J1fL6sl5uM2ENtR0jd3h7nJoD8eUG7FzM/X8IVJx7s7CjN8+oxDQ9Gbp60/nTMm1O3McoesHaRqg==", "signatures": [{"sig": "MEUCIQDA+Vhwq9f++eDprdVwYjD7xCfiG+FVqmLko5TDl4ioRwIgUC3k+DClt6am7laPez3NQqeo3Tc9rRiiKysI3KPu8BY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/chart.js", "_from": ".", "_shasum": "710027edfc159d6f949c6537729431c72d4dca49", "gitHead": "691a7c6a4bef0c6e0b8d1c82bafd4e2807239fac", "scripts": {}, "_npmUser": {"name": "etimberg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "3.6.0", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "5.6.0", "dependencies": {"moment": "^2.10.6", "chartjs-color": "^1.0.2"}, "devDependencies": {"gulp": "3.9.x", "karma": "^0.12.37", "jquery": "^2.1.4", "semver": "^3.0.1", "jasmine": "^2.3.2", "gulp-umd": "~0.2.0", "inquirer": "^0.5.1", "watchify": "^3.7.0", "coveralls": "^2.11.6", "gulp-size": "~0.4.0", "gulp-util": "~2.2.x", "browserify": "^13.0.0", "gulp-karma": "0.0.4", "gulp-concat": "~2.1.x", "gulp-insert": "~0.5.0", "gulp-jshint": "~1.5.1", "gulp-uglify": "~0.2.x", "gulp-connect": "~2.0.5", "gulp-replace": "^0.4.0", "jasmine-core": "^2.3.4", "merge-stream": "^1.0.0", "karma-jasmine": "^0.3.6", "gulp-streamify": "^1.0.2", "jshint-stylish": "~2.1.0", "karma-coverage": "^0.5.1", "karma-browserify": "^5.0.1", "browserify-istanbul": "^0.2.1", "gulp-html-validator": "^0.0.2", "vinyl-source-stream": "^1.1.0", "karma-chrome-launcher": "^0.2.0", "karma-firefox-launcher": "^0.1.6", "karma-jasmine-html-reporter": "^0.1.8"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js-2.1.3.tgz_1463093250261_0.1186228112783283", "host": "packages-16-east.internal.npmjs.com"}}, "2.1.4": {"name": "chart.js", "version": "2.1.4", "license": "MIT", "_id": "chart.js@2.1.4", "maintainers": [{"name": "etimberg", "email": "<EMAIL>"}, {"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}], "homepage": "http://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "spm": {"main": "Chart.js"}, "dist": {"shasum": "9dc81095ed5cc7942865e50e4c10cf548aa97f09", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-2.1.4.tgz", "integrity": "sha512-Z3irn7K0BinTdd8+XcJBVWFZHcYDAtBKC7ofmbCwgmRTon1B79XzPX+TueKPIigbHXRtzccsTaiwo3RWdrbQrA==", "signatures": [{"sig": "MEYCIQDteDLrb6HzNS5N/cQmUwEwl/zx290PtNmCFp7sw3oANwIhAKJHTI0xnTzMAzQNwEDdUeCmEBL7at5wQk9Dy6iizDh5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/chart.js", "_from": ".", "_shasum": "9dc81095ed5cc7942865e50e4c10cf548aa97f09", "gitHead": "40d76b6a7ba14aeac69b05ad15d713c3402867b2", "scripts": {}, "_npmUser": {"name": "etimberg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "3.6.0", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "5.6.0", "dependencies": {"moment": "^2.10.6", "chartjs-color": "^2.0.0"}, "devDependencies": {"gulp": "3.9.x", "karma": "^0.12.37", "jquery": "^2.1.4", "semver": "^3.0.1", "jasmine": "^2.3.2", "gulp-umd": "~0.2.0", "inquirer": "^0.5.1", "watchify": "^3.7.0", "coveralls": "^2.11.6", "gulp-size": "~0.4.0", "gulp-util": "~2.2.x", "browserify": "^13.0.0", "gulp-karma": "0.0.4", "gulp-concat": "~2.1.x", "gulp-insert": "~0.5.0", "gulp-jshint": "~1.5.1", "gulp-uglify": "~0.2.x", "gulp-connect": "~2.0.5", "gulp-replace": "^0.4.0", "jasmine-core": "^2.3.4", "merge-stream": "^1.0.0", "karma-jasmine": "^0.3.6", "gulp-streamify": "^1.0.2", "jshint-stylish": "~2.1.0", "karma-coverage": "^0.5.1", "karma-browserify": "^5.0.1", "browserify-istanbul": "^0.2.1", "gulp-html-validator": "^0.0.2", "vinyl-source-stream": "^1.1.0", "karma-chrome-launcher": "^0.2.0", "karma-firefox-launcher": "^0.1.6", "karma-jasmine-html-reporter": "^0.1.8"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js-2.1.4.tgz_1464303619822_0.5535072141792625", "host": "packages-16-east.internal.npmjs.com"}}, "2.1.5": {"name": "chart.js", "version": "2.1.5", "license": "MIT", "_id": "chart.js@2.1.5", "maintainers": [{"name": "etimberg", "email": "<EMAIL>"}, {"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}], "homepage": "http://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "spm": {"main": "Chart.js"}, "dist": {"shasum": "419272499a466874454c5132c9acc4385f638947", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-2.1.5.tgz", "integrity": "sha512-WYZho0vTINa7ZUHfDiLw5nzfUcLXjMwfNDZcY2LQhB/3QY9BStQq6S6qF2Ip2XBdqKj7Nf6v/bJRWcX/h1S3LA==", "signatures": [{"sig": "MEYCIQCoH4Z17FADrrxsiBnIvSoX7nm2S0kjszz/j7akOqlSlAIhAIm1G/6dybytpOJl/jDdAcVzTO386wxvGKMUYu3n74kY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/chart.js", "_from": ".", "_shasum": "419272499a466874454c5132c9acc4385f638947", "gitHead": "44e40045897880f0cc837a8f41eccc1dea6b2ce6", "scripts": {}, "_npmUser": {"name": "etimberg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "3.6.0", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "5.6.0", "dependencies": {"moment": "^2.10.6", "chartjs-color": "^2.0.0"}, "devDependencies": {"gulp": "3.9.x", "karma": "^0.12.37", "jquery": "^2.1.4", "semver": "^3.0.1", "jasmine": "^2.3.2", "gulp-umd": "~0.2.0", "inquirer": "^0.5.1", "watchify": "^3.7.0", "coveralls": "^2.11.6", "gulp-size": "~0.4.0", "gulp-util": "~2.2.x", "browserify": "^13.0.0", "gulp-karma": "0.0.4", "gulp-concat": "~2.1.x", "gulp-insert": "~0.5.0", "gulp-jshint": "~1.5.1", "gulp-uglify": "~0.2.x", "gulp-connect": "~2.0.5", "gulp-replace": "^0.4.0", "jasmine-core": "^2.3.4", "merge-stream": "^1.0.0", "karma-jasmine": "^0.3.6", "gulp-streamify": "^1.0.2", "jshint-stylish": "~2.1.0", "karma-coverage": "^0.5.1", "bundle-collapser": "^1.2.1", "karma-browserify": "^5.0.1", "browserify-istanbul": "^0.2.1", "gulp-html-validator": "^0.0.2", "vinyl-source-stream": "^1.1.0", "karma-chrome-launcher": "^0.2.0", "karma-firefox-launcher": "^0.1.6", "karma-jasmine-html-reporter": "^0.1.8"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js-2.1.5.tgz_1465753347588_0.32690723775886", "host": "packages-16-east.internal.npmjs.com"}}, "2.1.6": {"name": "chart.js", "version": "2.1.6", "license": "MIT", "_id": "chart.js@2.1.6", "maintainers": [{"name": "etimberg", "email": "<EMAIL>"}, {"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}], "homepage": "http://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "spm": {"main": "Chart.js"}, "dist": {"shasum": "9bf42d47bbb3cc702756ef0a496e7c443a1f6aa5", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-2.1.6.tgz", "integrity": "sha512-1K7DMCFm+Oee1q65xtWFiJvsH2BCunO/tHIqHIXSvXPF3uhjGKw2nmVUzuv1wfuPb0hE82ttHtEgTPpIsMETGg==", "signatures": [{"sig": "MEUCIQCBdNZQHbB/qv5pSPDVzv8XlaMKfctZEVD2Dpe1yQWOoQIgWz3OuxsVboxsQ3VSo7HGMhpoHj0KJSoGE8zWAqrlTY0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/chart.js", "_from": ".", "_shasum": "9bf42d47bbb3cc702756ef0a496e7c443a1f6aa5", "gitHead": "07662b158d829f395b2c97d732e60599b8b5c4e4", "scripts": {}, "_npmUser": {"name": "etimberg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "3.6.0", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "5.6.0", "dependencies": {"moment": "^2.10.6", "chartjs-color": "^2.0.0"}, "devDependencies": {"gulp": "3.9.x", "karma": "^0.12.37", "jquery": "^2.1.4", "semver": "^3.0.1", "jasmine": "^2.3.2", "gulp-umd": "~0.2.0", "inquirer": "^0.5.1", "watchify": "^3.7.0", "coveralls": "^2.11.6", "gulp-size": "~0.4.0", "gulp-util": "~2.2.x", "browserify": "^13.0.0", "gulp-karma": "0.0.4", "gulp-concat": "~2.1.x", "gulp-insert": "~0.5.0", "gulp-jshint": "~1.5.1", "gulp-uglify": "~0.2.x", "gulp-connect": "~2.0.5", "gulp-replace": "^0.4.0", "jasmine-core": "^2.3.4", "merge-stream": "^1.0.0", "karma-jasmine": "^0.3.6", "gulp-streamify": "^1.0.2", "jshint-stylish": "~2.1.0", "karma-coverage": "^0.5.1", "bundle-collapser": "^1.2.1", "karma-browserify": "^5.0.1", "browserify-istanbul": "^0.2.1", "gulp-html-validator": "^0.0.2", "vinyl-source-stream": "^1.1.0", "karma-chrome-launcher": "^0.2.0", "karma-firefox-launcher": "^0.1.6", "karma-jasmine-html-reporter": "^0.1.8"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js-2.1.6.tgz_1465947234982_0.11803890601731837", "host": "packages-12-west.internal.npmjs.com"}}, "2.2.0-rc.1": {"name": "chart.js", "version": "2.2.0-rc.1", "license": "MIT", "_id": "chart.js@2.2.0-rc.1", "maintainers": [{"name": "etimberg", "email": "<EMAIL>"}, {"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}], "homepage": "http://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "spm": {"main": "Chart.js"}, "dist": {"shasum": "4a869e48719323571776172d8a15dd22a375783d", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-2.2.0-rc.1.tgz", "integrity": "sha512-uQx97W1fTu+i1rH4I9ycBmxtXPbqJDxqGnmDXfaa5bCUGUUFJm8m4kK/Cmczhg2bXbdkqqylI+2h1EW5vOVVmw==", "signatures": [{"sig": "MEUCIHtglZaG2+yJ6elHfHE6ltMH4xElvMRNqKn2Vr2NauSeAiEAyUF4VyMqIAF16pseJV/ON8Z99qr/DPtltAaAdZhLYvc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/chart.js", "_from": ".", "_shasum": "4a869e48719323571776172d8a15dd22a375783d", "gitHead": "bfa37e7d68da7245d4f4da417173917791b88e62", "scripts": {}, "_npmUser": {"name": "etimberg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "3.6.0", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "5.6.0", "dependencies": {"moment": "^2.10.6", "chartjs-color": "^2.0.0"}, "devDependencies": {"gulp": "3.9.x", "karma": "^0.12.37", "jquery": "^2.1.4", "semver": "^3.0.1", "jasmine": "^2.3.2", "gulp-umd": "~0.2.0", "gulp-zip": "~3.2.0", "inquirer": "^0.5.1", "watchify": "^3.7.0", "coveralls": "^2.11.6", "gulp-size": "~0.4.0", "gulp-util": "~2.2.x", "browserify": "^13.0.0", "gulp-karma": "0.0.4", "gulp-concat": "~2.1.x", "gulp-insert": "~0.5.0", "gulp-jshint": "~1.5.1", "gulp-uglify": "~0.2.x", "gulp-connect": "~2.0.5", "gulp-replace": "^0.5.4", "jasmine-core": "^2.3.4", "merge-stream": "^1.0.0", "karma-jasmine": "^0.3.6", "gulp-streamify": "^1.0.2", "jshint-stylish": "~2.1.0", "karma-coverage": "^0.5.1", "bundle-collapser": "^1.2.1", "karma-browserify": "^5.0.1", "browserify-istanbul": "^0.2.1", "gulp-html-validator": "^0.0.2", "vinyl-source-stream": "^1.1.0", "karma-chrome-launcher": "^0.2.0", "karma-firefox-launcher": "^0.1.6", "karma-jasmine-html-reporter": "^0.1.8"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js-2.2.0-rc.1.tgz_1468454391362_0.9682344847824425", "host": "packages-12-west.internal.npmjs.com"}}, "2.2.0-rc.2": {"name": "chart.js", "version": "2.2.0-rc.2", "license": "MIT", "_id": "chart.js@2.2.0-rc.2", "maintainers": [{"name": "etimberg", "email": "<EMAIL>"}, {"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}], "homepage": "http://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "spm": {"main": "Chart.js"}, "dist": {"shasum": "6a405468967d64868e96309ce1cacedfc13c5b25", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-2.2.0-rc.2.tgz", "integrity": "sha512-DAhMiG8JOrwIvs3cKgx5MY9v2Ebt80v871CalQtYVkgyGJiUIm879eAcHXUI4sUEj6QE1TUNrI2C4WjTFbEnTw==", "signatures": [{"sig": "MEQCICGnycDLpfoAATtY/3+AYFKX/tx21K1hF8Y6No+0RDU8AiAe5VNgDTYnV74O5MwqKj9VV2VfScjzTCLWnX3K2ynIEQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/chart.js", "_from": ".", "_shasum": "6a405468967d64868e96309ce1cacedfc13c5b25", "gitHead": "e94d3c0730b86f9b2059d5077e6b1544295ec61a", "scripts": {}, "_npmUser": {"name": "etimberg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "3.8.3", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "5.10.1", "dependencies": {"moment": "^2.10.6", "chartjs-color": "^2.0.0"}, "devDependencies": {"gulp": "3.9.x", "karma": "^0.12.37", "jquery": "^2.1.4", "semver": "^3.0.1", "jasmine": "^2.3.2", "gulp-umd": "~0.2.0", "gulp-zip": "~3.2.0", "inquirer": "^0.5.1", "watchify": "^3.7.0", "coveralls": "^2.11.6", "gulp-size": "~0.4.0", "gulp-util": "~2.2.x", "browserify": "^13.0.0", "gulp-karma": "0.0.4", "gulp-concat": "~2.1.x", "gulp-insert": "~0.5.0", "gulp-jshint": "~1.5.1", "gulp-uglify": "~0.2.x", "gulp-connect": "~2.0.5", "gulp-replace": "^0.5.4", "jasmine-core": "^2.3.4", "merge-stream": "^1.0.0", "karma-jasmine": "^0.3.6", "gulp-streamify": "^1.0.2", "jshint-stylish": "~2.1.0", "karma-coverage": "^0.5.1", "bundle-collapser": "^1.2.1", "karma-browserify": "^5.0.1", "browserify-istanbul": "^0.2.1", "gulp-html-validator": "^0.0.2", "vinyl-source-stream": "^1.1.0", "karma-chrome-launcher": "^0.2.0", "karma-firefox-launcher": "^0.1.6", "karma-jasmine-html-reporter": "^0.1.8"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js-2.2.0-rc.2.tgz_1468969301512_0.8383547724224627", "host": "packages-12-west.internal.npmjs.com"}}, "2.2.0": {"name": "chart.js", "version": "2.2.0", "license": "MIT", "_id": "chart.js@2.2.0", "maintainers": [{"name": "etimberg", "email": "<EMAIL>"}, {"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}], "homepage": "http://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "spm": {"main": "Chart.js"}, "dist": {"shasum": "5f3f94b76fd07c84d7fe21490b1f4c10c645454e", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-2.2.0.tgz", "integrity": "sha512-sfVg1AnxViF4Q4EWURaxyIjGmsWJ6OI5JFxQelwNS+28hmEl0dbLQx1B7GwATmJKl5SDbGQv/s7AbUbpHPxm4A==", "signatures": [{"sig": "MEYCIQDHybw1rEJoZmnrDobixRDzYXm7gnyAZ3tbDMGEifkWLQIhAJ+spH1XMbJ3p779aGYMAsrWMyhlW9v/v7PRc4Y2X6Mf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/chart.js", "_from": ".", "_shasum": "5f3f94b76fd07c84d7fe21490b1f4c10c645454e", "gitHead": "b92387c845d8565c54aa811bd4cf7c784a7db7d0", "scripts": {}, "_npmUser": {"name": "etimberg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "3.8.3", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "5.10.1", "dependencies": {"moment": "^2.10.6", "chartjs-color": "^2.0.0"}, "devDependencies": {"gulp": "3.9.x", "karma": "^0.12.37", "jquery": "^2.1.4", "semver": "^3.0.1", "jasmine": "^2.3.2", "gulp-umd": "~0.2.0", "gulp-zip": "~3.2.0", "inquirer": "^0.5.1", "watchify": "^3.7.0", "coveralls": "^2.11.6", "gulp-size": "~0.4.0", "gulp-util": "~2.2.x", "browserify": "^13.0.0", "gulp-karma": "0.0.4", "gulp-concat": "~2.1.x", "gulp-insert": "~0.5.0", "gulp-jshint": "~1.5.1", "gulp-uglify": "~0.2.x", "gulp-connect": "~2.0.5", "gulp-replace": "^0.5.4", "jasmine-core": "^2.3.4", "merge-stream": "^1.0.0", "karma-jasmine": "^0.3.6", "gulp-streamify": "^1.0.2", "jshint-stylish": "~2.1.0", "karma-coverage": "^0.5.1", "bundle-collapser": "^1.2.1", "karma-browserify": "^5.0.1", "browserify-istanbul": "^0.2.1", "gulp-html-validator": "^0.0.2", "vinyl-source-stream": "^1.1.0", "karma-chrome-launcher": "^0.2.0", "karma-firefox-launcher": "^0.1.6", "karma-jasmine-html-reporter": "^0.1.8"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js-2.2.0.tgz_1469745135993_0.7723691903520375", "host": "packages-16-east.internal.npmjs.com"}}, "2.2.1": {"name": "chart.js", "version": "2.2.1", "license": "MIT", "_id": "chart.js@2.2.1", "maintainers": [{"name": "etimberg", "email": "<EMAIL>"}, {"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}], "homepage": "http://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "spm": {"main": "Chart.js"}, "dist": {"shasum": "11b1ff32a7a9c7aa9fc68fb97e3261b846b174c0", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-2.2.1.tgz", "integrity": "sha512-RLxKoSJ25RgqHlHcu2JzOIwMW+10xxI3PDnjv3xdk5Pod7qwIhJS4DC99a0Kiq/z6tRyaRSdSbW11KBV+3Pw+w==", "signatures": [{"sig": "MEYCIQDFOWekfcF8BrW9aY2K0HFKzfD3L94loI5qigIVvwHZNwIhANwjutIMtJlSdk/6FNaZ9OI2kfg4Jv4pNUitx9DXaKxC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/chart.js", "_from": ".", "_shasum": "11b1ff32a7a9c7aa9fc68fb97e3261b846b174c0", "gitHead": "ab66146013a6b7c687630b414ba8731a6f25337c", "scripts": {}, "_npmUser": {"name": "etimberg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "3.8.3", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "5.10.1", "dependencies": {"moment": "^2.10.6", "chartjs-color": "^2.0.0"}, "devDependencies": {"gulp": "3.9.x", "karma": "^0.12.37", "jquery": "^2.1.4", "semver": "^3.0.1", "jasmine": "^2.3.2", "gulp-umd": "~0.2.0", "gulp-zip": "~3.2.0", "inquirer": "^0.5.1", "watchify": "^3.7.0", "coveralls": "^2.11.6", "gulp-size": "~0.4.0", "gulp-util": "~2.2.x", "browserify": "^13.0.0", "gulp-karma": "0.0.4", "gulp-concat": "~2.1.x", "gulp-insert": "~0.5.0", "gulp-jshint": "~1.5.1", "gulp-uglify": "~0.2.x", "gulp-connect": "~2.0.5", "gulp-replace": "^0.5.4", "jasmine-core": "^2.3.4", "merge-stream": "^1.0.0", "karma-jasmine": "^0.3.6", "gulp-streamify": "^1.0.2", "jshint-stylish": "~2.1.0", "karma-coverage": "^0.5.1", "bundle-collapser": "^1.2.1", "karma-browserify": "^5.0.1", "browserify-istanbul": "^0.2.1", "gulp-html-validator": "^0.0.2", "vinyl-source-stream": "^1.1.0", "karma-chrome-launcher": "^0.2.0", "karma-firefox-launcher": "^0.1.6", "karma-jasmine-html-reporter": "^0.1.8"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js-2.2.1.tgz_1469898651593_0.5174298433121294", "host": "packages-12-west.internal.npmjs.com"}}, "2.2.2": {"name": "chart.js", "version": "2.2.2", "license": "MIT", "_id": "chart.js@2.2.2", "maintainers": [{"name": "etimberg", "email": "<EMAIL>"}, {"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}], "homepage": "http://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "spm": {"main": "Chart.js"}, "dist": {"shasum": "1f27b10a40a4ce941c03d3619ab2419dec4cae4f", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-2.2.2.tgz", "integrity": "sha512-zioc3A8/GFMoY6OKbxN3khsKxmso0B9cOUrDV9ZfGF7j4XoSLo5d+5ha2vMoXVlRA2MT2A/lXs0wQbMtqAjWpQ==", "signatures": [{"sig": "MEUCIBGGnXA1op6xkigsUxCbvaDOrFq3IoQ4QL0gmXhqsClpAiEA2NELSyACGCuqNkSgZjuNw4JCD9Jr9Zaf3mLppZi3cKE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/chart.js", "_from": ".", "_shasum": "1f27b10a40a4ce941c03d3619ab2419dec4cae4f", "gitHead": "0ebe388611a8d394750aa6dba5634db26e378742", "scripts": {}, "_npmUser": {"name": "etimberg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "3.8.3", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "5.10.1", "dependencies": {"moment": "^2.10.6", "chartjs-color": "^2.0.0"}, "devDependencies": {"gulp": "3.9.x", "karma": "^0.12.37", "jquery": "^2.1.4", "semver": "^3.0.1", "jasmine": "^2.3.2", "gulp-umd": "~0.2.0", "gulp-zip": "~3.2.0", "inquirer": "^0.5.1", "watchify": "^3.7.0", "coveralls": "^2.11.6", "gulp-size": "~0.4.0", "gulp-util": "~2.2.x", "browserify": "^13.0.0", "gulp-karma": "0.0.4", "gulp-concat": "~2.1.x", "gulp-insert": "~0.5.0", "gulp-jshint": "~1.5.1", "gulp-uglify": "~0.2.x", "gulp-connect": "~2.0.5", "gulp-replace": "^0.5.4", "jasmine-core": "^2.3.4", "merge-stream": "^1.0.0", "karma-jasmine": "^0.3.6", "gulp-streamify": "^1.0.2", "jshint-stylish": "~2.1.0", "karma-coverage": "^0.5.1", "bundle-collapser": "^1.2.1", "karma-browserify": "^5.0.1", "browserify-istanbul": "^0.2.1", "gulp-html-validator": "^0.0.2", "vinyl-source-stream": "^1.1.0", "karma-chrome-launcher": "^0.2.0", "karma-firefox-launcher": "^0.1.6", "karma-jasmine-html-reporter": "^0.1.8"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js-2.2.2.tgz_1472301634865_0.1258494914509356", "host": "packages-16-east.internal.npmjs.com"}}, "2.3.0-rc.1": {"name": "chart.js", "version": "2.3.0-rc.1", "license": "MIT", "_id": "chart.js@2.3.0-rc.1", "maintainers": [{"name": "chartjs-ci", "email": "<EMAIL>"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}], "homepage": "http://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "spm": {"main": "Chart.js"}, "dist": {"shasum": "016e1c9313a70e72263e707b6f57a14f77116c3d", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-2.3.0-rc.1.tgz", "integrity": "sha512-5Z3ipsmw1JN/47bAuri3aHbr7Xq51STeFuwmmyNFWidgAFUq5/DH8rxY4bOyrKPPNoKCLPdq+jsN+OH3ka4cnQ==", "signatures": [{"sig": "MEYCIQC4umsJaQJwdl6zcih9v66B1fRrTlL29yu/fgO/ZW9afwIhAJOQC7jmfz1+jq6xDmAOFJjqlKB6CGYjAs8CDqnJ9YRU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/chart.js", "_from": ".", "_shasum": "016e1c9313a70e72263e707b6f57a14f77116c3d", "gitHead": "ded5fcc1596d4e0c7f25cef3b071b62bd0a409e1", "scripts": {}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "3.8.3", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "5.10.1", "dependencies": {"moment": "^2.10.6", "chartjs-color": "^2.0.0"}, "devDependencies": {"gulp": "3.9.x", "karma": "^0.12.37", "jasmine": "^2.3.2", "gulp-zip": "~3.2.0", "coveralls": "^2.11.6", "gulp-file": "^0.3.0", "gulp-size": "~0.4.0", "gulp-util": "~2.2.x", "browserify": "^13.0.0", "gulp-karma": "0.0.4", "gulp-concat": "~2.1.x", "gulp-eslint": "^2.0.0", "gulp-insert": "~0.5.0", "gulp-uglify": "~0.2.x", "gulp-connect": "~2.0.5", "gulp-replace": "^0.5.4", "jasmine-core": "^2.3.4", "merge-stream": "^1.0.0", "karma-jasmine": "^0.3.6", "gulp-streamify": "^1.0.2", "karma-coverage": "^0.5.1", "bundle-collapser": "^1.2.1", "karma-browserify": "^5.0.1", "browserify-istanbul": "^0.2.1", "gulp-html-validator": "^0.0.2", "vinyl-source-stream": "^1.1.0", "karma-chrome-launcher": "^0.2.0", "karma-firefox-launcher": "^0.1.6", "karma-jasmine-html-reporter": "^0.1.8"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js-2.3.0-rc.1.tgz_1474485759470_0.3284878972917795", "host": "packages-16-east.internal.npmjs.com"}}, "2.3.0": {"name": "chart.js", "version": "2.3.0", "license": "MIT", "_id": "chart.js@2.3.0", "maintainers": [{"name": "chartjs-ci", "email": "<EMAIL>"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}], "homepage": "http://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "spm": {"main": "Chart.js"}, "dist": {"shasum": "40460e48e2c417c05fc3325cd84f7b000dc7d7d6", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-2.3.0.tgz", "integrity": "sha512-LwJ6j1FNneojxFYewnz9QDQyjV++KN2s/Lgm0eipDUaKV3Fj5jOA3xtJg7AUGFcbhsYB4+Kn16c1bXwRxbOXow==", "signatures": [{"sig": "MEUCIQCvADaO7RfzHcHudI3QZPsYADITDwcUL2NzDl8bY190swIgBm6AbkFRfHf0pCCuIqWXnwk0qPMoIPDL7tzuZHGvc/E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/chart.js", "_from": ".", "_shasum": "40460e48e2c417c05fc3325cd84f7b000dc7d7d6", "gitHead": "d2035c7a15b8c3b3d92f9fdb54df40fd57259013", "scripts": {}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "3.8.3", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "5.10.1", "dependencies": {"moment": "^2.10.6", "chartjs-color": "^2.0.0"}, "devDependencies": {"gulp": "3.9.x", "karma": "^0.12.37", "jasmine": "^2.3.2", "gulp-zip": "~3.2.0", "coveralls": "^2.11.6", "gulp-file": "^0.3.0", "gulp-size": "~0.4.0", "gulp-util": "~2.2.x", "browserify": "^13.0.0", "gulp-karma": "0.0.4", "gulp-concat": "~2.1.x", "gulp-eslint": "^2.0.0", "gulp-insert": "~0.5.0", "gulp-uglify": "~0.2.x", "gulp-connect": "~2.0.5", "gulp-replace": "^0.5.4", "jasmine-core": "^2.3.4", "merge-stream": "^1.0.0", "karma-jasmine": "^0.3.6", "gulp-streamify": "^1.0.2", "karma-coverage": "^0.5.1", "bundle-collapser": "^1.2.1", "karma-browserify": "^5.0.1", "browserify-istanbul": "^0.2.1", "gulp-html-validator": "^0.0.2", "vinyl-source-stream": "^1.1.0", "karma-chrome-launcher": "^0.2.0", "karma-firefox-launcher": "^0.1.6", "karma-jasmine-html-reporter": "^0.1.8"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js-2.3.0.tgz_1474571261057_0.3575261142104864", "host": "packages-12-west.internal.npmjs.com"}}, "2.4.0": {"name": "chart.js", "version": "2.4.0", "license": "MIT", "_id": "chart.js@2.4.0", "maintainers": [{"name": "chartjs-ci", "email": "<EMAIL>"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}], "homepage": "http://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "spm": {"main": "Chart.js"}, "dist": {"shasum": "44198073f0f43e5e16662e108420d92652a3c9a3", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-2.4.0.tgz", "integrity": "sha512-FIar+GJb1GZV4+YHxrePpJWpuMPlouUPpNCOxIMmUQrlshmgqeRdQT97XzGSkNmheqZQkrZno9mjzNtsOlphIA==", "signatures": [{"sig": "MEQCIEhsYfRaNnhk6jQ5PTAqigPoo+5nPgNRXUO5yGnd350iAiAkLu45lsQ5a/KTxeHBYkhK9pRcXC3bbQREp/0Bmrsvgg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/chart.js", "_from": ".", "_shasum": "44198073f0f43e5e16662e108420d92652a3c9a3", "gitHead": "18291ea3c5c0620f9f1951a0f04b1484940a7492", "scripts": {}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "3.8.3", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "5.10.1", "dependencies": {"moment": "^2.10.6", "chartjs-color": "^2.0.0"}, "devDependencies": {"gulp": "3.9.x", "karma": "^0.12.37", "yargs": "^5.0.0", "jasmine": "^2.3.2", "gulp-zip": "~3.2.0", "watchify": "^3.7.0", "coveralls": "^2.11.6", "gulp-file": "^0.3.0", "gulp-size": "~0.4.0", "gulp-util": "~2.2.x", "browserify": "^13.0.0", "gulp-karma": "0.0.4", "gulp-concat": "~2.1.x", "gulp-eslint": "^3.0.0", "gulp-insert": "~0.5.0", "gulp-uglify": "~0.2.x", "gulp-connect": "~2.0.5", "gulp-replace": "^0.5.4", "jasmine-core": "^2.3.4", "merge-stream": "^1.0.0", "karma-jasmine": "^0.3.6", "gulp-streamify": "^1.0.2", "karma-coverage": "^0.5.1", "bundle-collapser": "^1.2.1", "karma-browserify": "^5.0.1", "browserify-istanbul": "^0.2.1", "gulp-html-validator": "^0.0.2", "vinyl-source-stream": "^1.1.0", "karma-chrome-launcher": "^0.2.0", "karma-firefox-launcher": "^0.1.6", "karma-jasmine-html-reporter": "^0.1.8"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js-2.4.0.tgz_1478973679655_0.1027508876286447", "host": "packages-12-west.internal.npmjs.com"}}, "2.5.0": {"name": "chart.js", "version": "2.5.0", "license": "MIT", "_id": "chart.js@2.5.0", "maintainers": [{"name": "chartjs-ci", "email": "<EMAIL>"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}], "homepage": "http://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "spm": {"main": "Chart.js"}, "dist": {"shasum": "fe6e751a893769f56e72bee5ad91207e1c592957", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-2.5.0.tgz", "integrity": "sha512-R1I4/287bSmpGKJ1DiBcbQJ/WUUjXOmibD8/mOzhzr8UHIEpABjOkNQcnL987WXEiF+yII0jUf//o6mSZCReMA==", "signatures": [{"sig": "MEUCIQCkCn//w7lTIfwNhlzizggZpI3c9GO5+i/DkMDOD2tzZgIgMHnwIjdi3t20vfaNr99HRV7h0c5FGuWvrXaBXpNTYFg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/chart.js", "_from": ".", "_shasum": "fe6e751a893769f56e72bee5ad91207e1c592957", "gitHead": "06467f50b74875d54bac4793ef82f410c633cf8a", "scripts": {}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "3.8.3", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "5.10.1", "dependencies": {"moment": "^2.10.6", "chartjs-color": "^2.0.0"}, "devDependencies": {"gulp": "3.9.x", "karma": "^0.12.37", "yargs": "^5.0.0", "jasmine": "^2.3.2", "gulp-zip": "~3.2.0", "watchify": "^3.7.0", "coveralls": "^2.11.6", "gulp-file": "^0.3.0", "gulp-size": "~0.4.0", "gulp-util": "~2.2.x", "browserify": "^13.0.0", "gulp-karma": "0.0.4", "gulp-concat": "~2.1.x", "gulp-eslint": "^3.0.0", "gulp-insert": "~0.5.0", "gulp-uglify": "~2.0.x", "gulp-connect": "~2.0.5", "gulp-replace": "^0.5.4", "jasmine-core": "^2.3.4", "merge-stream": "^1.0.0", "karma-jasmine": "^0.3.6", "gulp-streamify": "^1.0.2", "karma-coverage": "^0.5.1", "bundle-collapser": "^1.2.1", "karma-browserify": "^5.0.1", "browserify-istanbul": "^0.2.1", "gulp-html-validator": "^0.0.2", "vinyl-source-stream": "^1.1.0", "karma-chrome-launcher": "^0.2.0", "karma-firefox-launcher": "^0.1.6", "karma-jasmine-html-reporter": "^0.1.8"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js-2.5.0.tgz_1486583551400_0.04586766008287668", "host": "packages-18-east.internal.npmjs.com"}}, "2.6.0": {"name": "chart.js", "version": "2.6.0", "license": "MIT", "_id": "chart.js@2.6.0", "maintainers": [{"name": "chartjs-ci", "email": "<EMAIL>"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}], "homepage": "http://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "spm": {"main": "Chart.js"}, "dist": {"shasum": "308f9a4b0bfed5a154c14f5deb1d9470d22abe71", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-2.6.0.tgz", "integrity": "sha512-rDKea6rpT/g+zOjR1NAwYYLKWVeD4VayQc2Dtp36OI69BASfcxPHhtjQmIEdib3JR1chz0IvD0+AV66ps04lrw==", "signatures": [{"sig": "MEYCIQCkxrnrngK3QrZ1cq70yMXi5fDiCbYxH0Spre4z75vpYgIhAKjjFtJMxyOnEqMmilrB1mctNDwZdEV4hUhprQ0n+o74", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/chart.js", "_from": ".", "_shasum": "308f9a4b0bfed5a154c14f5deb1d9470d22abe71", "gitHead": "28cb390c4102fc34391289d955d342281cac0e85", "scripts": {}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "3.8.3", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "5.10.1", "dependencies": {"moment": "^2.10.6", "chartjs-color": "^2.1.0"}, "devDependencies": {"gulp": "3.9.x", "karma": "^1.5.0", "yargs": "^5.0.0", "jasmine": "^2.5.0", "gulp-zip": "~3.2.0", "watchify": "^3.7.0", "coveralls": "^2.11.6", "gulp-file": "^0.3.0", "gulp-size": "~0.4.0", "gulp-util": "~2.2.x", "browserify": "^13.0.0", "pixelmatch": "^4.0.2", "gitbook-cli": "^2.3.0", "gulp-concat": "~2.1.x", "gulp-eslint": "^3.0.0", "gulp-insert": "~0.5.0", "gulp-uglify": "~2.0.x", "gulp-connect": "~2.0.5", "gulp-replace": "^0.5.4", "jasmine-core": "^2.5.0", "merge-stream": "^1.0.0", "karma-jasmine": "^1.1.0", "gulp-streamify": "^1.0.2", "karma-coverage": "^1.1.0", "bundle-collapser": "^1.2.1", "karma-browserify": "^5.1.0", "browserify-istanbul": "^0.2.1", "gulp-html-validator": "^0.0.2", "vinyl-source-stream": "^1.1.0", "child-process-promise": "^2.2.0", "karma-chrome-launcher": "^2.0.0", "karma-firefox-launcher": "^1.0.0", "karma-jasmine-html-reporter": "^0.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js-2.6.0.tgz_1495721246600_0.32316544698551297", "host": "s3://npm-registry-packages"}}, "2.7.0": {"name": "chart.js", "version": "2.7.0", "license": "MIT", "_id": "chart.js@2.7.0", "maintainers": [{"name": "chartjs-ci", "email": "<EMAIL>"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "nnnick", "email": "<EMAIL>"}], "homepage": "http://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "spm": {"main": "Chart.js"}, "dist": {"shasum": "330dbbee0c66199eb715f60fbf8ca4029609a529", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-2.7.0.tgz", "integrity": "sha512-SuVuIBwWBbMOFHnAkmHONSePcb/eF2u1zw+bgWv9Z70wsqouOsIr+rHkrjOTlvDnokznUIJSc/jaQ9Lv2j/IAg==", "signatures": [{"sig": "MEYCIQD+IyIiDQcIjyLLA41l74bVAB2CcyXKPtXHTohdMS2MLgIhAJnVZb+JTEmYMRlhLIyNZJGclw81Ro+9Lx37LU6rS2eU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/chart.js", "_from": ".", "_shasum": "330dbbee0c66199eb715f60fbf8ca4029609a529", "gitHead": "3043a58605d08c7e16ceb548596fae8efc666350", "scripts": {}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "6.11.3", "dependencies": {"moment": "~2.18.0", "chartjs-color": "~2.2.0"}, "devDependencies": {"gulp": "3.9.x", "karma": "^1.7.0", "yargs": "^8.0.1", "jasmine": "^2.6.0", "gulp-zip": "~4.0.0", "watchify": "^3.9.0", "coveralls": "^2.13.1", "gulp-file": "^0.3.0", "gulp-size": "~2.1.0", "gulp-util": "~3.0.x", "browserify": "^14.3.0", "pixelmatch": "^4.0.2", "gitbook-cli": "^2.3.0", "gulp-concat": "~2.6.x", "gulp-eslint": "^3.0.1", "gulp-insert": "~0.5.0", "gulp-uglify": "~3.0.x", "gulp-connect": "~5.0.0", "gulp-replace": "^0.5.4", "jasmine-core": "^2.6.2", "merge-stream": "^1.0.1", "karma-jasmine": "^1.1.0", "gulp-streamify": "^1.0.2", "karma-coverage": "^1.1.1", "bundle-collapser": "^1.2.1", "karma-browserify": "^5.1.1", "browserify-istanbul": "^2.0.0", "gulp-html-validator": "^0.0.5", "vinyl-source-stream": "^1.1.0", "child-process-promise": "^2.2.1", "karma-chrome-launcher": "^2.1.1", "karma-firefox-launcher": "^1.0.1", "karma-jasmine-html-reporter": "^0.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js-2.7.0.tgz_1505065045633_0.5998017601668835", "host": "s3://npm-registry-packages"}}, "2.7.1": {"name": "chart.js", "version": "2.7.1", "license": "MIT", "_id": "chart.js@2.7.1", "maintainers": [{"name": "chartjs-ci", "email": "<EMAIL>"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "nnnick", "email": "<EMAIL>"}], "homepage": "http://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "spm": {"main": "Chart.js"}, "dist": {"shasum": "ae90b4aa4ff1f02decd6b1a2a8dabfd73c9f9886", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-2.7.1.tgz", "integrity": "sha512-pX1oQAY86MiuyZ2hY593Acbl4MLHKrBBhhmZ1YqSadzQbbsBE2rnd6WISoHjIsdf0WDeC0hbePYCz2ZxkV8L+g==", "signatures": [{"sig": "MEYCIQDRg/5o+/2iDFWodXYySZT85VfC8puTJ5bRETi+1UAPlgIhANJzdaAy87dq6eX7nWkGr8s2igdgYoMvidIiyekziYME", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/chart.js", "gitHead": "0fead21939b92c15093c1b7d5ee2627fb5900fff", "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "5.1.0", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "6.11.5", "dependencies": {"moment": "~2.18.0", "chartjs-color": "~2.2.0"}, "devDependencies": {"gulp": "3.9.x", "karma": "^1.7.0", "yargs": "^8.0.1", "jasmine": "^2.6.0", "gulp-zip": "~4.0.0", "watchify": "^3.9.0", "coveralls": "^2.13.1", "gulp-file": "^0.3.0", "gulp-size": "~2.1.0", "gulp-util": "~3.0.x", "browserify": "^14.3.0", "pixelmatch": "^4.0.2", "gitbook-cli": "^2.3.0", "gulp-concat": "~2.6.x", "gulp-eslint": "^3.0.1", "gulp-insert": "~0.5.0", "gulp-uglify": "~3.0.x", "gulp-connect": "~5.0.0", "gulp-replace": "^0.5.4", "jasmine-core": "^2.6.2", "merge-stream": "^1.0.1", "karma-jasmine": "^1.1.0", "gulp-streamify": "^1.0.2", "karma-coverage": "^1.1.1", "bundle-collapser": "^1.2.1", "karma-browserify": "^5.1.1", "browserify-istanbul": "^2.0.0", "gulp-html-validator": "^0.0.5", "vinyl-source-stream": "^1.1.0", "child-process-promise": "^2.2.1", "karma-chrome-launcher": "^2.1.1", "karma-firefox-launcher": "^1.0.1", "karma-jasmine-html-reporter": "^0.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js-2.7.1.tgz_1509203440574_0.7151783984154463", "host": "s3://npm-registry-packages"}}, "2.7.2": {"name": "chart.js", "version": "2.7.2", "license": "MIT", "_id": "chart.js@2.7.2", "maintainers": [{"name": "chartjs-ci", "email": "<EMAIL>"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}], "homepage": "http://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "spm": {"main": "Chart.js"}, "dist": {"shasum": "3c9fde4dc5b95608211bdefeda7e5d33dffa5714", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-2.7.2.tgz", "fileCount": 367, "integrity": "sha512-90wl3V9xRZ8tnMvMlpcW+0Yg13BelsGS9P9t0ClaDxv/hdypHDr/YAGf+728m11P5ljwyB0ZHfPKCapZFqSqYA==", "signatures": [{"sig": "MEYCIQDIOY1TivqCOKa9+NgvrwkBqw4/MXabnJx5phSDNlrkJwIhAJmg0tTTbJlYT7gmybweSwAL0MmnjSVxyfolduc3OQr1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7007870}, "main": "src/chart.js", "gitHead": "98f104cdd03617f1300b417b3d60c23d4e3e3403", "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "5.1.0", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "6.13.0", "dependencies": {"moment": "^2.10.2", "chartjs-color": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"gulp": "3.9.x", "karma": "^1.7.1", "yargs": "^9.0.1", "eslint": "^4.9.0", "jasmine": "^2.8.0", "gulp-zip": "~4.0.0", "watchify": "^3.9.0", "coveralls": "^3.0.0", "gulp-file": "^0.3.0", "gulp-size": "~2.1.0", "gulp-util": "~3.0.x", "browserify": "^14.5.0", "pixelmatch": "^4.0.2", "gitbook-cli": "^2.3.2", "gulp-concat": "~2.6.x", "gulp-eslint": "^4.0.0", "gulp-insert": "~0.5.0", "gulp-uglify": "~3.0.x", "gulp-connect": "~5.0.0", "gulp-replace": "^0.6.1", "jasmine-core": "^2.8.0", "merge-stream": "^1.0.1", "gulp-htmllint": "^0.0.15", "karma-jasmine": "^1.1.0", "gulp-streamify": "^1.0.2", "karma-coverage": "^1.1.1", "bundle-collapser": "^1.3.0", "karma-browserify": "^5.1.1", "eslint-plugin-html": "^4.0.2", "browserify-istanbul": "^3.0.1", "vinyl-source-stream": "^1.1.0", "child-process-promise": "^2.2.1", "eslint-config-chartjs": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-firefox-launcher": "^1.0.1", "karma-jasmine-html-reporter": "^0.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_2.7.2_1519941109590_0.06426160345378729", "host": "s3://npm-registry-packages"}}, "2.7.3": {"name": "chart.js", "version": "2.7.3", "license": "MIT", "_id": "chart.js@2.7.3", "maintainers": [{"name": "chartjs-ci", "email": "<EMAIL>"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}], "homepage": "http://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "spm": {"main": "Chart.js"}, "dist": {"shasum": "cdb61618830bf216dc887e2f7b1b3c228b73c57e", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-2.7.3.tgz", "fileCount": 403, "integrity": "sha512-3+7k/DbR92m6BsMUYP6M0dMsMVZpMnwkUyNSAbqolHKsbIzH2Q4LWVEHHYq7v0fmEV8whXE0DrjANulw9j2K5g==", "signatures": [{"sig": "MEUCIH7lGtpfOvGFs1I0lx4pOq9tFEhkOjozyDudteTbdf1WAiEA34MJfgiECGHXS6UpbQ4aUwUivxs0KsmwR0zDPBenbZY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7251869, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbxMyUCRA9TVsSAnZWagAAlMcP/0e7/Ckzq86VtC66MMln\nIkcFcG1kzmDS43jvkIZR+Hw54kcrABV/QuyAZRcU4hDY15KBLT6aK02Q8ifc\nGbkGWPHgc8emAo+wCzEPMDyoebS8xdeBLDh/95wWe7kEHWJ9sBuw5PC1kiJV\n1E2WlyRVNZpFJKghh3LLMjgFekL3AEE3vYOmxXolLQS7+Tr0bk+Ov4lxJpEo\nTIxcgS6mDblXl4q0dbU3s0VPp5CujW09CnrsObfnXn49XimBBaiI5B7NwOP3\ny7lhpOkj+3NfUb6qx95eD+mTA1rpy/EjqO2CvIOum+3U9dBQk9pMYfEw1j+v\nKAuNZGbeQedcEEn8lbg9WlCaUcpNFzxnrWwio4J1NwomfY6xotnZHV2YJUVb\nX3vx5YLlLxUW9iQhseHlxeKwOeysRmHotPoArZ8voA9ijWDtTj1Whp/M+xpg\n5Xv5ftafF2mlxjov0T3sxZ4GPZ2orPdNuXNFKDrfuPsdl0PLx11QbcQKOZXb\nde0xl6RSVEwrrtrz8WuqIGR3VAfSJSm5PTp2CNbXEgYkiO/nx4Wy2HYruUBx\nReaZJilqoVfErb/MkxSF+WZvfkftrgzN3J+tP9u74ERu5F5/Bpe8spWFzzHl\nPcF2zrMDJUuGzOKNWK/VWlV6lBhljkFcDiPyG1IARGT5k7XxyrF2T/CHHAYd\n/mRl\r\n=ZaV4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/chart.js", "gitHead": "924a66bae23f7e938531024951f7251d46785473", "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "5.1.0", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "6.14.4", "dependencies": {"moment": "^2.10.2", "chartjs-color": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"gulp": "3.9.x", "karma": "^1.7.1", "yargs": "^9.0.1", "eslint": "^4.9.0", "jasmine": "^2.8.0", "gulp-zip": "~4.0.0", "watchify": "^3.9.0", "coveralls": "^3.0.0", "gulp-file": "^0.3.0", "gulp-size": "~2.1.0", "gulp-util": "~3.0.x", "browserify": "^14.5.0", "pixelmatch": "^4.0.2", "gitbook-cli": "^2.3.2", "gulp-concat": "~2.6.x", "gulp-eslint": "^4.0.0", "gulp-insert": "~0.5.0", "gulp-uglify": "~3.0.x", "gulp-connect": "~5.0.0", "gulp-replace": "^0.6.1", "jasmine-core": "^2.8.0", "merge-stream": "^1.0.1", "gulp-htmllint": "^0.0.15", "karma-jasmine": "^1.1.0", "gulp-streamify": "^1.0.2", "karma-coverage": "^1.1.1", "bundle-collapser": "^1.3.0", "karma-browserify": "^5.1.1", "eslint-plugin-html": "^4.0.2", "browserify-istanbul": "^3.0.1", "vinyl-source-stream": "^1.1.0", "child-process-promise": "^2.2.1", "eslint-config-chartjs": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-firefox-launcher": "^1.0.1", "karma-jasmine-html-reporter": "^0.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_2.7.3_1539624083181_0.3388303155944554", "host": "s3://npm-registry-packages"}}, "2.8.0-rc.1": {"name": "chart.js", "version": "2.8.0-rc.1", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@2.8.0-rc.1", "maintainers": [{"name": "chartjs-ci", "email": "<EMAIL>"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "a3a37f7f7939d333dc629eb598ebad72c6523b13", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-2.8.0-rc.1.tgz", "fileCount": 11, "integrity": "sha512-2MwJGDIMVWlYO2eJkQatPuultUbEX1AdN/HcQKoY3DpF53nE72eSJ+SAm0owByg057TqPHlZIWmGg8kFDIGF5A==", "signatures": [{"sig": "MEYCIQCDl3tycOPB+/es6g2PVWWD7fY03F4ft0vLkFBPwa/AAAIhAIED9NSSs69qtWtUlh6WyBwT728DVLsqh0FNpngFObeM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1315518, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcfRqhCRA9TVsSAnZWagAAzcsQAJY4Lq35ca7N+VQ1Xe4j\nDdUA+JIZnGgE70xVZJshw51w4Te6xJPtuCXuXIMQRBmYggUuPbdSEoDaB6pg\n/yywcsMW/22QXu/PThtMRwLZXCrKk8438yNJDkFawwbeX0rouXAuiSER0EA4\nGkj2/64Vno5vp7jvJh2V4plk+L+RkzP6OoNnwQy2ekJWoSH4scQnpTZuHfFZ\nZUbciBgF79CAzjh4zMzt2rLsqxWZTglBmEs33SEBcvhiaDtlzUt49Gttf32q\nG7poI6LPkqvlNTlxgVyGSLwjUqfY7DjNUjqgoOxjO5USAlpRG6ew8jBU5qsW\nKqhM5MBB+8/FRnyUPQ3qMkSYwKCn0CQVjggsXdVRXgqhiVVnG/YPlY++LPz/\n5ptWI5bNP5gMzvZ8dlPzKvRvYwDwcssn02e7mNsmsAKnMyihviqtfpgYn3xL\nZonnBAFtEvcE978rDLBaAM7XGtfS5MJrk4xfLmp0Y5pszCtkVBIcsWHzPdOV\n0c7qMZq3+xmcaM2UL9bnHEgnlzO/KNpPF/owoAGkGEi++avtyMfOxc5s49wV\nZZGbAxo1GHCvNKf3Tld3Euuh5+daBwOnkbLRTSdFU0e3CIf3Xc6haDFCdryp\nqwVee4yQunq3e98YAwYnDcVuguz+MswbyeeQlUz2Ai5nkvuWOJx+QwItLUEk\ncVRB\r\n=F1mZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/Chart.js", "unpkg": "dist/Chart.min.js", "gitHead": "ab8cfa00f23724cd8e18c312e6497db800c196fd", "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/Chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "5.1.0", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "10.15.2", "dependencies": {"moment": "^2.10.2", "chartjs-color": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"gulp": "^4.0.0", "karma": "^4.0.0", "yargs": "^12.0.5", "eslint": "^5.9.0", "rollup": "^0.67.4", "jasmine": "^3.3.0", "gulp-zip": "^4.2.0", "clean-css": "^4.2.1", "coveralls": "^3.0.0", "gulp-file": "^0.4.0", "gulp-size": "^3.0.0", "pixelmatch": "^4.0.2", "gitbook-cli": "^2.3.2", "gulp-eslint": "^5.0.0", "gulp-terser": "^1.1.6", "gulp-replace": "^1.0.0", "jasmine-core": "^3.3.0", "merge-stream": "^1.0.1", "gulp-htmllint": "^0.0.16", "karma-jasmine": "^2.0.1", "gulp-streamify": "^1.0.2", "karma-coverage": "^1.1.1", "eslint-plugin-html": "^5.0.0", "rollup-plugin-terser": "^3.0.0", "eslint-config-chartjs": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-firefox-launcher": "^1.0.1", "rollup-plugin-commonjs": "^9.2.0", "rollup-plugin-istanbul": "^2.0.1", "karma-rollup-preprocessor": "^6.1.1", "rollup-plugin-node-resolve": "^3.4.0", "karma-jasmine-html-reporter": "^1.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_2.8.0-rc.1_1551702689285_0.9527494519427062", "host": "s3://npm-registry-packages"}}, "2.8.0": {"name": "chart.js", "version": "2.8.0", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@2.8.0", "maintainers": [{"name": "chartjs-ci", "email": "<EMAIL>"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "b703b10d0f4ec5079eaefdcd6ca32dc8f826e0e9", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-2.8.0.tgz", "fileCount": 11, "integrity": "sha512-Di3wUL4BFvqI5FB5K26aQ+hvWh8wnP9A3DWGvXHVkO13D3DSnaSsdZx29cXlEsYKVkn1E2az+ZYFS4t0zi8x0w==", "signatures": [{"sig": "MEUCICHwpnwrYASSIH5Hzn29F+wJ2cwFDGmxYUFgJOmOaCFWAiEAlVcsiicQpD6LEdVD4hSTaBL3o3llsSurPHBT9+33UK8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1317536, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcilJTCRA9TVsSAnZWagAA4woQAJYO2JZjjXnHyZfK0L5w\nloi37zReieFiLU0PKQ/nxwzQngCfzwLqucghAbFpVe7prIgGCO8qGa/t2nDj\no2/miv4w2YTXVdtoxvxbJU3lrW3V2nvRaAZFSAa5gYuzdk6sI9QRLusu58rf\nNMpiPRZtuGRcAt1sfMRRfsDxWX0iOFTnPMdR73AxdG4mjaYgG+YgodUrRWqK\nGXG4oGTFJ3koFQsVJhG9+3b53uJtQPfgpO4KDJVlV2xKMj8u06Gu1+rpuYPg\nFR6nxjj+W8j+/XE4tX+vG+4PR8arKqfHFiNgTW4g1XnfDi8ULI8F7C6M6SCd\n852egEDoGvbBfqGTiDHo99xMr72B9p8FXSlhaAIQF6uDSRP454UmNnEYzGN2\nUpcdGQ5sql25DnpUQjFNFe2gKt7nowOyaDSq86wZ8f1p4q9vOwrEbyqsbCjA\nVtLY4F4+GGAcSYZ5uurCpa4C4Es7ODAJt3gbLyHyFYMM7jyYACspnWtPCHjD\ncNUX2hqjcFUvPAAKntOSheez5c5tVVh1npdMe0jM4/Gx4msL6wQzHuAz1oQb\nQNDjT0hF5+6sswDia1rxbBJQI8eCqBUnq7pQx76tLvsOJVglGb+GNYETMLSh\nsCi0KQmXcnzQUC0MzRNUgEozgmI1kpInsG8N/OleoSxfeUUSZxc4K4ghv7pn\no3cp\r\n=kiFo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/Chart.js", "unpkg": "dist/Chart.min.js", "gitHead": "947d8a7ccfbfc76dd9d384ea75436fa4a7aeefb1", "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/Chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "5.1.0", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "10.15.3", "dependencies": {"moment": "^2.10.2", "chartjs-color": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"gulp": "^4.0.0", "karma": "^4.0.0", "yargs": "^12.0.5", "eslint": "^5.9.0", "rollup": "^0.67.4", "jasmine": "^3.3.0", "gulp-zip": "^4.2.0", "clean-css": "^4.2.1", "coveralls": "^3.0.0", "gulp-file": "^0.4.0", "gulp-size": "^3.0.0", "pixelmatch": "^4.0.2", "gitbook-cli": "^2.3.2", "gulp-eslint": "^5.0.0", "gulp-terser": "^1.1.6", "gulp-replace": "^1.0.0", "jasmine-core": "^3.3.0", "merge-stream": "^1.0.1", "gulp-htmllint": "^0.0.16", "karma-jasmine": "^2.0.1", "gulp-streamify": "^1.0.2", "karma-coverage": "^1.1.1", "eslint-plugin-html": "^5.0.0", "rollup-plugin-terser": "^3.0.0", "eslint-config-chartjs": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-firefox-launcher": "^1.0.1", "rollup-plugin-commonjs": "^9.2.0", "rollup-plugin-istanbul": "^2.0.1", "karma-rollup-preprocessor": "^6.1.1", "rollup-plugin-node-resolve": "^3.4.0", "karma-jasmine-html-reporter": "^1.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_2.8.0_1552568914436_0.6983722647040749", "host": "s3://npm-registry-packages"}}, "2.9.0": {"name": "chart.js", "version": "2.9.0", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@2.9.0", "maintainers": [{"name": "chartjs-ci", "email": "<EMAIL>"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "ebd700719922ba4963c84a4792f7af88e3a4840c", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-2.9.0.tgz", "fileCount": 11, "integrity": "sha512-qgbY2tObinNst84qlWYiNTvszJSKoyoYiOjqOztUaW88N+Xk7QFJ9PasO+w0G1+NkxJzKzun7kUSZd7oWLCIDQ==", "signatures": [{"sig": "MEYCIQCsAYp94tJimpafsacYR+19tKqR0ifa1oK+exe8qybHuwIhAKq4vAsxdRMkKfNEph3Cod6p3jRNFFrjMzjbM5Iz41wM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1384854, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJds5UGCRA9TVsSAnZWagAA0PUP+QEV+s0jAhHbEhR9d1PX\nMSDWHCiF0/HC9y+aDl4fahKIXOnKrvJH1EmNifN3Z/x88p/oJxvdIyYUNIqo\nX0HPhC00yTp9Rfi8nKuARJGk2mJm/zpPtrXilIhW3X6xbL46/1z8X7rdygZG\nqJfaHlXDz6upIj4EmSQQc1nvSB4y9eE5kQfpZ47Jou5P+dJppgyxHrVZaGmz\nsG8uW+U2LLFdhCD0VQQHDzdRXAizSxPTvlfIrBMzPCbPFivuGwhwjgh++Z<PERSON>\nyZ3sJZju/97CPrHyXi05qXqE+XoAtudOGBKKTPZERT/KhAwpYaQcP00iopFR\nNof2nZ/cxQWADryacqOGwV190qQax8qDBcLDCQ95vNYn1j7y8GwOClAVskzI\nRlj8To4ZBHsGRy/MDeKTr/3/Tzjc8Lqoamza5lJyqCgibYJrRqlO08kC/LNp\nzjfT2oGzIPZAa+CFGvVHN6t5pLfyrUgRuvW6u6BqHiC/+vdv3EudSR5KlIqt\nKCNV3iLNAekiLttCcF/3zre5QwT2+gFtX5LQdoHcaCOSNm2CuYSxEv+wyuh8\nJ0eaRPl27nuJ2Tkw4j5ZBwI3OjfwFmce+uqRA8P+/RDl3fb71GIiW0drgb66\na2LWtc5j/ue92wnQ4qXU6FIgKO5fgNZ2l33DZG9bgDzSNJ7je0b+rebfsh+H\nDk26\r\n=wxqJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/Chart.js", "unpkg": "dist/Chart.min.js", "gitHead": "b81dd0728d5ad0f0aa84b3708755f84897cefd43", "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/Chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "5.1.0", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "12.13.0", "dependencies": {"moment": "^2.10.2", "chartjs-color": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"gulp": "^4.0.0", "karma": "^4.0.0", "yargs": "^12.0.5", "eslint": "^5.9.0", "rollup": "^1.0.0", "jasmine": "^3.3.0", "gulp-zip": "^4.2.0", "clean-css": "^4.2.1", "coveralls": "^3.0.0", "gulp-file": "^0.4.0", "gulp-size": "^3.0.0", "pixelmatch": "^4.0.2", "gitbook-cli": "^2.3.2", "gulp-eslint": "^5.0.0", "gulp-terser": "^1.1.6", "gulp-replace": "^1.0.0", "jasmine-core": "^3.3.0", "merge-stream": "^1.0.1", "gulp-htmllint": "^0.0.16", "karma-jasmine": "^2.0.1", "gulp-streamify": "^1.0.2", "karma-coverage": "^1.1.1", "eslint-plugin-html": "^5.0.0", "rollup-plugin-terser": "^5.0.0", "eslint-config-chartjs": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-firefox-launcher": "^1.0.1", "rollup-plugin-commonjs": "^10.0.0", "rollup-plugin-istanbul": "^2.0.1", "karma-rollup-preprocessor": "^7.0.0", "rollup-plugin-node-resolve": "^5.0.0", "karma-jasmine-html-reporter": "^1.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_2.9.0_1572050181588_0.33334074371477485", "host": "s3://npm-registry-packages"}}, "2.9.1": {"name": "chart.js", "version": "2.9.1", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@2.9.1", "maintainers": [{"name": "chartjs-ci", "email": "<EMAIL>"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "e98893a810d40d1c0a016e6872a13fa5faedcdf8", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-2.9.1.tgz", "fileCount": 11, "integrity": "sha512-DA5dFt0Bz79oz56ezmrwmZqj0hXGs+i9VbCFOcHqbwrHIGv7RI4YqninJKNIAC0qa29WBI9qYTN7LzULlOeunA==", "signatures": [{"sig": "MEUCIQDxsuFXQPEPZZShW3Yoi945p70OIsY53fIJmZTVo+3LUQIgcSYjbhkMJpIuIDPMkXHY9sW/RpeaCK8CeFiwNRPpMx0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1385118, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdte6FCRA9TVsSAnZWagAA/8YP/A/XUWm8u+AhdlHQI89W\nM0xYpVWyhcUyHX5dJrbSiLlA22ZYqn56dtiQRmUwxdOAlQ9WzI7SfTjEw+jE\n7XgOIj8tVP85fLhUDdGf8pgLJ4zP+zzHmRZnCFqj7MEOjU4e+289F8FNH+Kt\nke934glvMAvhe6FFbqhUUz5+DiR3n65k13UceVzCVysS9UlvOPkKOfxlgczv\njVYCSgPcO/8dY/qqdolr3xYTaC+jKl27D8oTysh8O4PRutgoi1X77hANhSdZ\ntu8SnWBzHp1cNR3qdAPNe7nxVbOWpn26w4lyBC5G2SJRBDVdRK+h2PKvhwAG\nj0hHmTiP2U3kz1MPpx+zYvjNvBAid58GkxOTqOGpwBTDSYxaiyz+CaUEOwjk\n/5LkKD+i2O1IAesaNskLQHueJgnKdgVcOUEtFbc8EjTnTO9rRT2+wI/zVcHI\n9wiZ9xiDKiz4MgsYoSs8sHewaSyOFIOlxCIYcmpwFIH57QY8MxZDtFWgPeuu\nN4HxGtAfzJ7ma62W0SBF6DKpqGsxKd78uy0rt1WUkWOgQxtbdhY0WpVALdGz\ngAfaW0RY3uPzJxODQSoHFaoKybNE4m6NabW9ls9KU6zSy5amg6NicjkCEBaI\nxxJ75RjPGhPJPtuSlKpXDCtrTAiZQje3vq/2I8hyzNclczlVcgNMZ7ERo5tR\nlc0i\r\n=h3TS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/Chart.js", "unpkg": "dist/Chart.min.js", "gitHead": "f1d12dc1499cddd709b6cb9939b4aad55398c16a", "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/Chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "5.1.0", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "12.13.0", "dependencies": {"moment": "^2.10.2", "chartjs-color": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"gulp": "^4.0.0", "karma": "^4.0.0", "yargs": "^12.0.5", "eslint": "^5.9.0", "rollup": "^1.0.0", "jasmine": "^3.3.0", "gulp-zip": "^4.2.0", "clean-css": "^4.2.1", "coveralls": "^3.0.0", "gulp-file": "^0.4.0", "gulp-size": "^3.0.0", "pixelmatch": "^4.0.2", "gitbook-cli": "^2.3.2", "gulp-eslint": "^5.0.0", "gulp-terser": "^1.1.6", "gulp-replace": "^1.0.0", "jasmine-core": "^3.3.0", "merge-stream": "^1.0.1", "gulp-htmllint": "^0.0.16", "karma-jasmine": "^2.0.1", "gulp-streamify": "^1.0.2", "karma-coverage": "^1.1.1", "eslint-plugin-html": "^5.0.0", "rollup-plugin-terser": "^5.0.0", "eslint-config-chartjs": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-firefox-launcher": "^1.0.1", "rollup-plugin-commonjs": "^10.0.0", "rollup-plugin-istanbul": "^2.0.1", "karma-rollup-preprocessor": "^7.0.0", "rollup-plugin-node-resolve": "^5.0.0", "karma-jasmine-html-reporter": "^1.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_2.9.1_1572204165025_0.37555153943078823", "host": "s3://npm-registry-packages"}}, "2.9.2": {"name": "chart.js", "version": "2.9.2", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@2.9.2", "maintainers": [{"name": "chartjs-ci", "email": "<EMAIL>"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "5f7397f2fc33ca406836dbaed3cc39943bbb9f80", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-2.9.2.tgz", "fileCount": 11, "integrity": "sha512-AagP9h27gU7hhx8F64BOFpNZGV0R1Pz1nhsi0M1+KLhtniX6ElqLl0z0obKSiuGMl9tcRe6ZhruCGCJWmH6snQ==", "signatures": [{"sig": "MEYCIQCDqLzr9W3fRSvdDYTupUxkAP+nA62LYgyvO7BGF0X9+wIhAILpX7+ona2Aedzx696mKslWnUBQ3ovWueoE2ff5fWbO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1413874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdvdnaCRA9TVsSAnZWagAAF0wP/A7CNQ3yVLn0WnzTBvIE\nyjIjcx7lq9yUpaAgihKLAKyYsGsG5bWU+/JbmTSdx91dvG6i0bHl18nuyiXa\nJPKXNlZ1ASKARk/hy4Ti8C7yD6jjBbfAuum6TlU6TSG5l0xJJhfBgGuSgrEh\n1d2X0XT9NAB8udfH0QVRAPfQfd9Aw+78GrhLBNZFSmNSN6AfHxGXrAowO4T9\nuiuGLOss85grfx6LUcmcM3GjjKCl9QvQg7C0cIzi81EGBnwwYfA3YEQlMfZT\neMmVJ4JzbQmHowdi4LtODyzW28L1foXPnZrVBi5eRwtk7cjjxSGzvz0wNHtz\nBXd+PIM7MT+PAlY8+8Wr3+i1fcbo9SEeCOG9YigEF1dy5wnyFLJn7w40H19a\nCewIuK5Ek/SOI+gqSZIMqZp+7lSSOe5arPOwFIByV82mBupZqvUiQKckdwJa\nzBEVSCoCk1qc4R7Lt2D4LET4sC4/qblv5BFPVcrZUWAo1XjTZI0zc62OOnZf\nYqxtdcGtNyj0N5o3DsuOGhhhrOswmqUotD/a/zTJQMfFHFlmYakJ7KF0Pydm\n3fbxZDK12Xd5lIpGFU5hQY2WcItYR4ZsTO5hftKI4X9EcgYItKB6a0Eco39V\ny32wnPM0A6ktAPTPUH3EZzmdb5X+L40F7ekhU9B9GjUD5wJpyuaqH2NDeHvE\nPUZJ\r\n=KvEU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/Chart.js", "unpkg": "dist/Chart.min.js", "gitHead": "8521776076612b28c69385cafa28ccd8b7670eb7", "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/Chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "5.1.0", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "12.13.0", "dependencies": {"moment": "^2.10.2", "chartjs-color": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"gulp": "^4.0.0", "karma": "^4.0.0", "yargs": "^12.0.5", "eslint": "^5.9.0", "rollup": "^1.0.0", "jasmine": "^3.3.0", "gulp-zip": "^4.2.0", "clean-css": "^4.2.1", "coveralls": "^3.0.0", "gulp-file": "^0.4.0", "gulp-size": "^3.0.0", "pixelmatch": "^4.0.2", "gitbook-cli": "^2.3.2", "gulp-eslint": "^5.0.0", "gulp-terser": "^1.1.6", "gulp-replace": "^1.0.0", "jasmine-core": "^3.3.0", "merge-stream": "^1.0.1", "gulp-htmllint": "^0.0.16", "karma-jasmine": "^2.0.1", "gulp-streamify": "^1.0.2", "karma-coverage": "^1.1.1", "eslint-plugin-html": "^5.0.0", "rollup-plugin-terser": "^5.0.0", "eslint-config-chartjs": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-firefox-launcher": "^1.0.1", "rollup-plugin-commonjs": "^10.0.0", "rollup-plugin-istanbul": "^2.0.1", "karma-rollup-preprocessor": "^7.0.0", "rollup-plugin-node-resolve": "^5.0.0", "karma-jasmine-html-reporter": "^1.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_2.9.2_1572723161765_0.6973087231530484", "host": "s3://npm-registry-packages"}}, "2.9.3": {"name": "chart.js", "version": "2.9.3", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@2.9.3", "maintainers": [{"name": "chartjs-ci", "email": "<EMAIL>"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "ae3884114dafd381bc600f5b35a189138aac1ef7", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-2.9.3.tgz", "fileCount": 11, "integrity": "sha512-+2jlOobSk52c1VU6fzkh3UwqHMdSlgH1xFv9FKMqHiNCpXsGPQa/+81AFa+i3jZ253Mq9aAycPwDjnn1XbRNNw==", "signatures": [{"sig": "MEUCIQCVzytWZY32LadpzhEsZI6rT7LuzdbSANgDWn5cvx4VPQIgHltBavPrPIDAS4jq6ZeCOlWTikGdDg19BPC1DCb5yvE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1413894, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdzaAvCRA9TVsSAnZWagAAKQgP/1q7IAfITfipk0hOy9wb\n9pHuRCS3E2eofz0iJboAHnwayALUJoCPgVQs4InbNM8Wovs6n+fNnUprO4Dj\nWbtuM6S5nHlMp1+7cWyueTOOG88R17qYUQvkcoLNpnn8iagGjCgqpx8gUuHC\n9qMaETbekfyuBw8/XNkX0E7F66TBjFPhdxsUHRSCmsPmTap5Goke9uzFpd3G\nkMtHT8lC5W7eiQZHzT2KJVBkkuXZuWDPBMiz0ZATfgWtuSK25S0qPSDEabwy\nOJh//ynW6IsGAEPtrnn9XIXCK2Sc78DOxBNXVcbmzB8GPFCcjVzVvj5C6/Gg\nVT8DlOYiyJS7ei5RPWvdMphhiSCy4WLhYVJE/NJUl41N2K/g+jEVu1u7hL58\nov+hyAIflYDpdXjryyOSBhqKqS118A3Il3+14+YYbEjUnZ1uEE1SWZfxRSjV\nMBZJLFWOY/r0lv4qIyexOyVqWjPmHDvG/vOQhKVZkV1DEpdqqVGaUgQ/cK8B\nRLn9Hx7mYhhRRqGP7MXJ4GroQsSSNr8DE4MvIo32ipAwROxRevrGkKBm5cyz\n6yG+xvTFP2CeGC/B3UZDaHhg2EH/iAPEAl37FxWUNRKrkPcITVOWeldKGinK\nUodBrzjmJgKP+qayFyJX3+o8JwP+J/zEH0thCBj/jhMH4xq1SclHBEsVwjxi\nxqVb\r\n=gnpm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/Chart.js", "unpkg": "dist/Chart.min.js", "gitHead": "06f73dc3590084b2c464bf08189c7aee2b6b92d2", "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/Chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "5.1.0", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "12.13.0", "dependencies": {"moment": "^2.10.2", "chartjs-color": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"gulp": "^4.0.0", "karma": "^4.0.0", "yargs": "^12.0.5", "eslint": "^5.9.0", "rollup": "^1.0.0", "jasmine": "^3.3.0", "gulp-zip": "^4.2.0", "clean-css": "^4.2.1", "coveralls": "^3.0.0", "gulp-file": "^0.4.0", "gulp-size": "^3.0.0", "pixelmatch": "^4.0.2", "gitbook-cli": "^2.3.2", "gulp-eslint": "^5.0.0", "gulp-terser": "^1.1.6", "gulp-replace": "^1.0.0", "jasmine-core": "^3.3.0", "merge-stream": "^1.0.1", "gulp-htmllint": "^0.0.16", "karma-jasmine": "^2.0.1", "gulp-streamify": "^1.0.2", "karma-coverage": "^1.1.1", "eslint-plugin-html": "^5.0.0", "rollup-plugin-terser": "^5.0.0", "eslint-config-chartjs": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-firefox-launcher": "^1.0.1", "rollup-plugin-commonjs": "^10.0.0", "rollup-plugin-istanbul": "^2.0.1", "karma-rollup-preprocessor": "^7.0.0", "rollup-plugin-node-resolve": "^5.0.0", "karma-jasmine-html-reporter": "^1.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_2.9.3_1573756974455_0.0355498067005422", "host": "s3://npm-registry-packages"}}, "3.0.0-alpha": {"name": "chart.js", "version": "3.0.0-alpha", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.0.0-alpha", "maintainers": [{"name": "chartjs-ci", "email": "<EMAIL>"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "d82e3f7c882b8e321ad05bc1833f983b2b0b630b", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.0.0-alpha.tgz", "fileCount": 9, "integrity": "sha512-9jbL1IsG9+1oq01GMKbeJ/257O46nVjQuYWEanpo0MuCr18cXMfkrzBTwdbl0VTOkIvWLJJcU5Cmhdc546k2bA==", "signatures": [{"sig": "MEQCIAT0iYkurwFZbQDjZOFp5Necp1T+hwoe8DBaUDK2x7LBAiAGmgEKYXpBXi44QAbXa1L5/bzcGFD5zotGEPzmClPKwA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1083020, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeaYmACRA9TVsSAnZWagAACV0P/2grG6Gko59EI8s4zSDK\nwCNEgybMAnGcvrCti1TLcMGH/5cppJxumgxIUw5IA8gvLqFHo90UfZga+Vlv\nDm4kCONE3lLc2ieU1ImAVhCxVcVzNELpRA/69h/Ye98pxlPNV30/xjbx7z5P\nogUUCAwqfEhmWNTTlhchZn1WZfI0ekqGt3Sp9liQ93QfrmJ8XJM97Z6hUEEI\ndqkbz80xlAz9zJ8bVQ2qbbKdMJassrZav/gBQ4sNydLSPR8k/VWuL6lGBEuf\nJt4Q3gkBpZMHkCVZMdUfamfZj8Un5gZVrrlZ/Ohy5CCtUx/2CCELX6N3J2Qi\nQzv8GQds6pnMH8xZGE7GclZxCk7aW/pNr8nFZalezdiLel2CJf27JIqspeNm\nvO0hEGrwkpNaT9eBv+NwP6VgJpEG5M0+kJHCt8kRCT8iIqiOH/QpALRjkfKz\nRKWHYbFmUamKmSZrpqQBW3wrveRk1UJ1v2Huy302m1FZPcxDbD5vVA1X9XXa\nXqW7rL95qo9ohs1P15wstvpj/zSvbZdT40UpL49/CHIIsFKo6cTFLlHASc9i\ngkWhruf4h3qN9AEt6ckQcUMY70HGhDKsFOhEwMCOt0tftpjeFMHFUvPu2EWD\naw8YDue4DpYggHfjnPzb7MaEAldZRweWrurwLctkVU+Oe07dUzEZaD0trEjH\n2waS\r\n=NjVA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/Chart.js", "unpkg": "dist/Chart.min.js", "module": "dist/Chart.esm.js", "gitHead": "a0342fe2088886a159505f97fbc5d230b23ee24a", "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/Chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "5.1.0", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"@kurkle/color": "^0.1.6"}, "_hasShrinkwrap": false, "devDependencies": {"gulp": "^4.0.0", "karma": "^4.0.0", "yargs": "^14.2.2", "eslint": "^6.8.0", "moment": "^2.10.2", "rollup": "^1.31.1", "jasmine": "^3.3.0", "typedoc": "^0.16.10", "gulp-zip": "^5.0.0", "coveralls": "^3.0.9", "gulp-file": "^0.4.0", "gulp-size": "^3.0.0", "pixelmatch": "^5.0.0", "typescript": "^3.8.2", "@babel/core": "^7.8.4", "gitbook-cli": "^2.3.2", "gulp-eslint": "^6.0.0", "gulp-terser": "^1.1.6", "gulp-replace": "^1.0.0", "gulp-typedoc": "^2.2.4", "jasmine-core": "^3.3.0", "merge-stream": "^1.0.1", "gulp-htmllint": "^0.0.16", "karma-jasmine": "^2.0.1", "gulp-streamify": "^1.0.2", "karma-coverage": "^2.0.0", "gulp-typescript": "^6.0.0-alpha.1", "@babel/preset-env": "^7.8.4", "eslint-plugin-html": "^6.0.0", "@rollup/plugin-json": "^4.0.2", "karma-edge-launcher": "^0.4.2", "rollup-plugin-babel": "^4.3.3", "eslint-config-esnext": "^4.0.0", "rollup-plugin-terser": "^5.2.0", "babel-plugin-istanbul": "^6.0.0", "eslint-config-chartjs": "^0.2.0", "karma-chrome-launcher": "^3.0.0", "rollup-plugin-cleanup": "^3.1.1", "chartjs-adapter-moment": "^0.1.1", "karma-firefox-launcher": "^1.3.0", "@rollup/plugin-commonjs": "^11.0.2", "resize-observer-polyfill": "^1.5.1", "karma-rollup-preprocessor": "^7.0.3", "babel-preset-es2015-rollup": "^3.0.0", "@rollup/plugin-node-resolve": "^7.1.1", "karma-jasmine-html-reporter": "^1.5.2", "karma-safari-private-launcher": "^1.0.0", "rollup-plugin-web-worker-loader": "^0.8.1", "@babel/plugin-transform-object-assign": "^7.8.3", "@babel/plugin-proposal-class-properties": "^7.8.3"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.0.0-alpha_1583974783644_0.9757682690136422", "host": "s3://npm-registry-packages"}}, "3.0.0-alpha.2": {"name": "chart.js", "version": "3.0.0-alpha.2", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.0.0-alpha.2", "maintainers": [{"name": "chartjs-ci", "email": "<EMAIL>"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "445d8bbd727fc92815384a6a3d3de15477c7eefa", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.0.0-alpha.2.tgz", "fileCount": 20, "integrity": "sha512-NMXuzoMorJPbMwnggXCXzqb82IHJ9fnQD2mNMTstGvZr9dhAo5/37l8TLIeemOow6uK4HMWu99hDXQno/2pkVg==", "signatures": [{"sig": "MEYCIQCgz9QLBru8/uF7QIumGEJiLX2HXVApHIHFyxU8WdTuDgIhAJG8WcXZ92z+SJ6NXT6Vdxb/hd79Z/ltctKfX6wASC0Z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 868243, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfEhMcCRA9TVsSAnZWagAANAgP/1emU37prsBa7Be667tl\nKCVsuVG1dIkE45+QfWZEUf9YwAHSep+knQY4Brwk3Io8Yw1dUU+MRNepPq1Y\n5FL/arY54ekeYqzyHBeDVqMvYuVFYGGQ7/KKIcpggCQazFtTI+QoNOJ9kdvX\npI6hSwk1LwVdQKVDj19A1qwndaloSQ78CGszJzxcdyb5U+u98tzHp0dLYpZB\nv4GfXZZsDevc1hJvezXfheD4enUhFVvQMjDoSPLTWOiK+6OcyyGbr6zYMoVX\nxYuUXFBNSacf5o4h/xP3qMYUK8sORaDq0PiiRYKTPnuYeueFQeg7M4k1Ffn1\n/GSXzAMUGNvpKlBnsvg3kxKn7X580+oWUWiEY+VxMZGp3nsIJOWPJQkTojSt\n6X2awNUU39V4l1D1uG53CDguTooFbnn7vgU3ZN0MrTl60NxVnJGTaqu0ojeS\nJgwv1tpPrEBvWpwXoZOqF81LzQXUw2ZIFR8ZlGntOPjOHXSoauw49bji5TR8\nQBbUF22fG+BC7mCBA1jietFlFlYSrciJNqX6VrKh4fydj6fDUblKbVUkSGpF\nwo09q0xToYWQ4hKThyUSFeNwlwlPqNSJMatw+ZlhL5KDBwZ1bCV/6pTGjU8R\nUDTU8w4vCLqaz4mTCZ91zZMwIMwCIOptsR7SY7KrLJUBRVLBu7kiZddehTQS\njMyx\r\n=PuYF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "2472066a4a6094ba05e95cd137735df66633b7fb", "scripts": {"dev": "karma start ---auto-watch --no-single-run --browsers chrome --grep", "docs": "cd docs && npm install && npm run build && mkdir -p ../dist && cp -r build ../dist/docs", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --no-auto-watch --single-run --coverage --grep", "build": "rollup -c", "lint-js": "eslint samples/**/*.html samples/**/*.js src/**/*.js test/**/*.js", "typedoc": "npx typedoc", "lint-tsc": "tsc", "autobuild": "rollup -c -w"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "12.18.2", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"glob": "^7.1.6", "karma": "^5.0.9", "yargs": "^15.3.1", "eslint": "^7.2.0", "moment": "^2.26.0", "rollup": "^2.15.0", "jasmine": "^3.5.0", "typedoc": "^0.17.7", "coveralls": "^3.1.0", "cross-env": "^7.0.2", "pixelmatch": "^5.2.0", "typescript": "^3.9.5", "@babel/core": "^7.10.2", "concurrently": "^5.2.0", "jasmine-core": "^3.5.0", "@kurkle/color": "^0.1.9", "karma-jasmine": "^3.3.1", "karma-coverage": "^2.0.2", "@babel/preset-env": "^7.10.2", "eslint-plugin-html": "^6.0.2", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-babel": "^4.4.0", "eslint-config-esnext": "^4.1.0", "rollup-plugin-terser": "^6.1.0", "@rollup/plugin-inject": "^4.0.2", "babel-plugin-istanbul": "^6.0.0", "eslint-config-chartjs": "^0.2.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.1.1", "chartjs-adapter-moment": "^0.1.1", "karma-firefox-launcher": "^1.3.0", "@rollup/plugin-commonjs": "^13.0.0", "resize-observer-polyfill": "^1.5.1", "karma-rollup-preprocessor": "^7.0.5", "babel-preset-es2015-rollup": "^3.0.0", "@rollup/plugin-node-resolve": "^8.0.1", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "rollup-plugin-web-worker-loader": "^1.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.0.0-alpha.2_1595020060380_0.13943088272885884", "host": "s3://npm-registry-packages"}}, "3.0.0-beta": {"name": "chart.js", "version": "3.0.0-beta", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.0.0-beta", "maintainers": [{"name": "chartjs-ci", "email": "<EMAIL>"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "fb73396bdd1e7d4b9fb6a69cee788dc2b32e3794", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.0.0-beta.tgz", "fileCount": 35, "integrity": "sha512-IX09wKPZZ1Cq3PFgBfybR9I/WUbHI5mFK4acNXxN4wrgFev8RT5ZSZqqfBjLDLz9ubPW6H7zDpM5mGuX7k6Mjw==", "signatures": [{"sig": "MEYCIQD90sVnN42+rxiaeTBw32lIU2szCkIx560bKrisLPmYIwIhAKnXOmNHHB3AgL9UZWPXX927RqEdo5n8JIbdMHL4T0uJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1010997, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfTm9tCRA9TVsSAnZWagAAhGAP/0Z7fOLjOSWkfocnTyAQ\n9uwHL0SVDFASPzrw0CPtOukOzq7LmORExa03VjLnAYgyYtGUNDEVQY1nusHY\nCO6NTvZK71d4rjWVaqDYgIWHbL21atjsGB2TUINA27Byd+I2BnV8ZNw2ynDk\n8aKljPuOAo7PmlEKYSK5y1OzumIpzL3pB4FzFNfJIqhBa/NaR82LijvGVYjz\nFYBm5c/ULMIsBwz8JaSaJrzIzCW6VFP6aZm00x098YCm0DNLbYYDxtxtn7MT\nJpdD5Thz0rl2HcxCuNiGQQ14+3r/SYxwaj56gRG9s6ZD7rayRJ08sBp+1OC3\nCFhppW7dEQWvfoudF8bhsbgrWEKEPeW596TmYeASPrgxce2JlLd1msnc5fqM\noSADqvMDz6PrvoA089t+cVWoaySObH/f3ktRSqb1rQPS+qaANqC2VXYQ7uRZ\ncfWP+2mHWzinkoPHL+to+6H0MeQ7PGIyk61cNV2SC9P51ZM24urFMbMSFa6a\nxBh4aqDoQ+ieKfygevOAgNisy1IefA3OCTiHQHtSuRGpy4BYsDlwnHFHGFb3\nTwHscwTEr/26NHL4fRPb5LIHrLLd6XHeHKVYGiX82ppCWTr3r+uUV2TeUWrI\nk1Jg15OS6ciPTS6cN6xVELIP9e+5No/vnJ0ZBjfZ7OfcyVshxj91jomDIPeS\nGSQW\r\n=16dG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "dist/chart.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "829067a82e84da9b8f1931d484b1fd67bee926bb", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "cd docs && npm install && npm run build", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "lint-js": "eslint samples/**/*.html samples/**/*.js src/**/*.js test/**/*.js", "typedoc": "npx typedoc", "lint-tsc": "tsc", "autobuild": "rollup -c -w"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.6", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "12.18.3", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"glob": "^7.1.6", "karma": "^5.1.1", "yargs": "^15.4.1", "eslint": "^7.6.0", "moment": "^2.27.0", "rollup": "^2.25.0", "jasmine": "^3.6.1", "typedoc": "^0.18.0", "coveralls": "^3.1.0", "cross-env": "^7.0.2", "pixelmatch": "^5.2.1", "typescript": "^3.9.7", "@babel/core": "^7.11.1", "concurrently": "^5.3.0", "jasmine-core": "^3.6.0", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "promise-polyfill": "^8.1.3", "@babel/preset-env": "^7.11.0", "rollup-plugin-dts": "^1.4.10", "eslint-plugin-html": "^6.0.3", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-babel": "^4.4.0", "eslint-config-esnext": "^4.1.0", "rollup-plugin-terser": "^7.0.0", "@rollup/plugin-inject": "^4.0.2", "babel-plugin-istanbul": "^6.0.0", "eslint-config-chartjs": "^0.2.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.1.1", "chartjs-adapter-moment": "^0.1.2", "karma-firefox-launcher": "^1.3.0", "@rollup/plugin-commonjs": "^15.0.0", "resize-observer-polyfill": "^1.5.1", "karma-rollup-preprocessor": "^7.0.5", "babel-preset-es2015-rollup": "^3.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@babel/plugin-transform-object-assign": "^7.10.4"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.0.0-beta_1598975852749_0.8161674891023407", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.2": {"name": "chart.js", "version": "3.0.0-beta.2", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.0.0-beta.2", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "52cb99f859e425143176e0960be75fe3a64be3a5", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.0.0-beta.2.tgz", "fileCount": 13, "integrity": "sha512-etQDEmCwdxVKpxd7VmPXCbC5eIu2FjT8yaKCWA4h8qNfX2QOw07pb5JtZxzsNlzVDb1plOw1uXlRNMIKToW6KQ==", "signatures": [{"sig": "MEQCIGUTTNjU6ubjjgEGpUca/aAIv765mLhoFJkQpvqUzkIgAiBhOZp7rrap0vhmhJwJcSuG4xgDYX5lgbbEPNbXwZAhZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 867524, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfdbdMCRA9TVsSAnZWagAA0ZgP/0kPY+DjZtUuR9qt2y/l\nfq2v/NQPFjX5F4VoRVCIc4zvENlbinZLn1zsM+QHMX+FMyNedF5Ypl0GTiHr\n0+x2ZOU6wCNCCYJi8Egt0i98ar741eyimtk0sSYp3TlMj11+Lrhzwxa3ICqJ\nFWJVJ5QGboOiyKxw4gedKqrP674/dFZBVtYp3SGKXXLvgBKgyZt6wAYr/uDp\n6jEcWxk3ushE04k+6AzrqWyb1UlfsttUOq63IXAq513I3Frb4SqpE3zPT9hl\nKWms8aGHF8ha0gioroTW4ZGDe8Ss61nNW5d9CsYwYO4Q5pLLuS6VWq+cQaKr\nOGyHgdIA2IoIluBLNBDxtYwyMleG/RXcNE+VblhWPTUqSFoovQlvVvieVJpH\nww0yep2Jfi+zgM2GGv57sb0Bd899C+EowGyDEM+N6uvIbbC45Qigu6bHPIP4\nW1tFlzP9RHSKYBBidexplxO8nktPTJmv2O+atyNxdFtCTtIh+C+VIm7qP6Xz\n060XuLT7NYjHBjfdr1VEbgM8hoFF/ymKBrGX+jt3sfXGW5qtDW8RoYC1kyOt\nc05r1hiahb7bdKoYCPn3YwosoLSl3HB4ItzzIv1ZXbfGYjN/ckBYUdga/xJb\n4fQnCexnGzVwvC4okgS2BDHMLl6zFDTLzRGmzCk2fEMU+5pXlnTniXtHwTIA\nysW+\r\n=+OOh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "dist/chart.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "35f08e73f2f2f79acc9e995ca18e49e80b9cd63a", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "cd docs && npm install && npm run build", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "lint-js": "eslint samples/**/*.html samples/**/*.js src/**/*.js test/**/*.js", "typedoc": "npx typedoc", "lint-tsc": "tsc", "autobuild": "rollup -c -w"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.6", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "12.18.4", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"glob": "^7.1.6", "karma": "^5.2.1", "yargs": "^15.4.1", "eslint": "^7.8.1", "moment": "^2.27.0", "rollup": "^2.26.10", "jasmine": "^3.6.1", "typedoc": "^0.19.0", "coveralls": "^3.1.0", "cross-env": "^7.0.2", "pixelmatch": "^5.2.1", "typescript": "^4.0.2", "@babel/core": "^7.11.6", "concurrently": "^5.3.0", "jasmine-core": "^3.6.0", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "promise-polyfill": "^8.1.3", "@babel/preset-env": "^7.11.5", "rollup-plugin-dts": "^1.4.12", "eslint-plugin-html": "^6.0.3", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-babel": "^4.4.0", "eslint-config-esnext": "^4.1.0", "rollup-plugin-terser": "^7.0.1", "@rollup/plugin-inject": "^4.0.2", "babel-plugin-istanbul": "^6.0.0", "eslint-config-chartjs": "^0.2.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.1.1", "chartjs-adapter-moment": "^0.1.2", "karma-firefox-launcher": "^1.3.0", "@rollup/plugin-commonjs": "^15.0.0", "resize-observer-polyfill": "^1.5.1", "karma-rollup-preprocessor": "^7.0.5", "babel-preset-es2015-rollup": "^3.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@babel/plugin-transform-object-assign": "^7.10.4"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.0.0-beta.2_1601550155461_0.19509926934549338", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.3": {"name": "chart.js", "version": "3.0.0-beta.3", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.0.0-beta.3", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "1fd26142c03ffa6a7b341f51a9a83c0f34569a23", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.0.0-beta.3.tgz", "fileCount": 15, "integrity": "sha512-Tcxcii6lewezR18S9lx4ckB2mIGJLsVevNnc0Mf3sjA3row6+vZS2EfSvs89TLQKbdQJTvWNMbwW/PnJXuzoNg==", "signatures": [{"sig": "MEUCIQCVVo5JjXjmYK4NfiokdIzHUXLp555592T/rZJBZowsOgIgOt3AXL9W9nbhY43d7bt6nhzGh5dGduCe8XNeNTe+JeU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1014909, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfddVTCRA9TVsSAnZWagAA7/AP/iqn4Yj+JC9dHuTKrbo4\ncqfsaQXvVk7h3qmJGSjFlTnuD5ZoEbWW9A3thZpt5svPF1hk5woMtHH8gyl3\nFhsWeWIxU3wHIULSNsNjQFE3zOkteEujNDaNnbQqrqrk6Xl3Iua8rKOo2r6q\nGXoWrgnoogwt3lxIE7lTlNGABFATkzh8+p+TjwFCXtuhEj4YiIptF5aZ6KHM\nBIaGhj5mgUbzyNHk886jcORAv1VRZe1qW2ep2ZunnaYBRodUvyesl8cg5/KR\nKTv+SwTQRNcTBBmlTTCcOymzJzz5x2h/EWTab0I7LUHcQLpXRpmfemHZqiZS\n7frwzmCVh+lgn0J2z1k5j0e7hGLV2XYI3j5sI+9ajltVb0w8QNLM1WWubt55\nNsze0on429VzqQ53iL0+dcS1Hd0p1vepfuKiKQ+wNui5ISh5SWMIGlNPLPbZ\nC9qYkVDyMXhKSk+YyXdpnUA9tAIUxzPusTrGW58/0sXYNGtHhcd2VXjcCL3g\njUGrM6Iv4kva0zmDugkbm2o8eZlMVLaa6kae/yFsFXT1zAXLQuSirOtWmIQA\nyIQxhmKKzwyEug2PWXXa+SiDABfkibk8CqUxApSM/Yj4qjAI1VUWqKfunpnF\nwvkligeohclH7FXeT+vIZBU6iNtAm2a9I5AjPoixrjC7spbgFs7cTADVdW7X\noK9L\r\n=gZWh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "dist/chart.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "9d0ec1a6df9fa3cb303d547513b3f1432b788079", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "cd docs && npm install && npm run build", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "lint-js": "eslint samples/**/*.html samples/**/*.js src/**/*.js test/**/*.js", "typedoc": "npx typedoc", "lint-tsc": "tsc", "autobuild": "rollup -c -w"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.6", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "12.18.4", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"glob": "^7.1.6", "karma": "^5.2.1", "yargs": "^15.4.1", "eslint": "^7.8.1", "moment": "^2.27.0", "rollup": "^2.26.10", "jasmine": "^3.6.1", "typedoc": "^0.19.0", "coveralls": "^3.1.0", "cross-env": "^7.0.2", "pixelmatch": "^5.2.1", "typescript": "^4.0.2", "@babel/core": "^7.11.6", "concurrently": "^5.3.0", "jasmine-core": "^3.6.0", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "promise-polyfill": "^8.1.3", "@babel/preset-env": "^7.11.5", "rollup-plugin-dts": "^1.4.12", "eslint-plugin-html": "^6.0.3", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-babel": "^4.4.0", "eslint-config-esnext": "^4.1.0", "rollup-plugin-terser": "^7.0.1", "@rollup/plugin-inject": "^4.0.2", "babel-plugin-istanbul": "^6.0.0", "eslint-config-chartjs": "^0.2.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.1.1", "chartjs-adapter-moment": "^0.1.2", "karma-firefox-launcher": "^1.3.0", "@rollup/plugin-commonjs": "^15.0.0", "resize-observer-polyfill": "^1.5.1", "karma-rollup-preprocessor": "^7.0.5", "babel-preset-es2015-rollup": "^3.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@babel/plugin-transform-object-assign": "^7.10.4"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.0.0-beta.3_1601557843331_0.27339253975505073", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.4": {"name": "chart.js", "version": "3.0.0-beta.4", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.0.0-beta.4", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "c0a39b33f3412d84562bfeb605be913f79e6dd2e", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.0.0-beta.4.tgz", "fileCount": 15, "integrity": "sha512-aW5s5gFIZyIiTEZANC+DZKLLcVi5Sp3fo7EKKJau89mzPBOVjg3Rv9VOKmLOWx5zMFuDyBBAMylNrr3MSiO2Wg==", "signatures": [{"sig": "MEQCIGlXIjSUvzrH5fFby2LFPC69XicyO6gavaF6fHX779iAAiAfjHlHT91THMJrYPj8groB9I9mz8ejLoHSm5tadbkWzQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1031844, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiLppCRA9TVsSAnZWagAAVEEQAJ2Mx5VUVpzS9uSzftez\nAE/opmyq/l1T0TUqZaHR7VAla9X32lGM1Z6R3ityWl1h5FK6A7VpTFGVbky5\n/koRABuZJAsGfszv0sD9d0mVbiSAsvD3K8xN3ZhVx/U6/hrUvOs3IxwTeABa\nrjPiy18JyEqzx2C4GX/XU4aQtIfaP8oazuCauLm244WLrcC3+FjQ9U+tlIK5\nYq3mm87B73XEk5TgXTxj9b6nn9vZyuqUCE24I97RO1trxNvzQogrOEZ8YbWh\nTatkaLLvHb5q5X9IpUutTJ6St7SMemJ5nTxAlwVOxZKs8CkuqEIKD3ve70MY\nmcmADUr1syY5sbC+REpECgpStEi47bS6rHzyqcCX/4paO5tdbDJqrvJEpyk1\nApyhuBtinkJ4RAD59KePyeNm8Y2oIQH20cWT1c8IxQsREkhfGhpU5kCQE9lG\n/yN73t0+FOplKcu2FZNHcKzLXFqWwBTMWrInUZ/XofGDm2gjX3tM9uIGfuNd\n9Ule8jZbhPTromY3FycMslRBBYK5qvb0lvFSqY7WF8VC9VNCMcNZkxQZZqLx\nnnYQok4+1SFOJDOSw4I1kzxs85ec0XbnTRACP5EpXrcSQ26c6g/xmawaLMk+\nAeKcxiOA0yGORnsfpcSPMZDQt5CxOo4IUSod+dCnFv8M3RQOQ9vVJTaP0ILP\nNC1e\r\n=pTGA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "dist/chart.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "64b45294861ca19c0713dec9f6854e71747571b0", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "cd docs && npm install && npm run build", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "lint-js": "eslint samples/**/*.html samples/**/*.js src/**/*.js test/**/*.js", "typedoc": "npx typedoc", "lint-tsc": "tsc", "autobuild": "rollup -c -w"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"glob": "^7.1.6", "karma": "^5.2.1", "yargs": "^15.4.1", "eslint": "^7.8.1", "moment": "^2.27.0", "rollup": "^2.26.10", "jasmine": "^3.6.1", "typedoc": "^0.19.0", "coveralls": "^3.1.0", "cross-env": "^7.0.2", "pixelmatch": "^5.2.1", "typescript": "^4.0.2", "@babel/core": "^7.11.6", "concurrently": "^5.3.0", "jasmine-core": "^3.6.0", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "promise-polyfill": "^8.1.3", "@babel/preset-env": "^7.11.5", "rollup-plugin-dts": "^1.4.12", "eslint-plugin-html": "^6.0.3", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-babel": "^4.4.0", "eslint-config-esnext": "^4.1.0", "rollup-plugin-terser": "^7.0.1", "@rollup/plugin-inject": "^4.0.2", "babel-plugin-istanbul": "^6.0.0", "eslint-config-chartjs": "^0.2.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.1.1", "chartjs-adapter-moment": "^0.1.2", "karma-firefox-launcher": "^1.3.0", "@rollup/plugin-commonjs": "^15.0.0", "resize-observer-polyfill": "^1.5.1", "karma-rollup-preprocessor": "^7.0.5", "babel-preset-es2015-rollup": "^3.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@babel/plugin-transform-object-assign": "^7.10.4"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.0.0-beta.4_1602796136495_0.826915356686226", "host": "s3://npm-registry-packages"}}, "2.9.4": {"name": "chart.js", "version": "2.9.4", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@2.9.4", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "0827f9563faffb2dc5c06562f8eb10337d5b9684", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-2.9.4.tgz", "fileCount": 11, "integrity": "sha512-B07aAzxcrikjAPyV+01j7BmOpxtQETxTSlQ26BEYJ+3iUkbNKaOJ/nDbT6JjyqYxseM0ON12COHYdU2cTIjC7A==", "signatures": [{"sig": "MEYCIQC3/A62+l8oglJqRKNSJmG+dgOo97/YArb+BeponSZtfAIhAOEZtkIkB+RnlERIbFTseyn0YKuGwDZx2a0lLD49gcmd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1444091, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfjIhlCRA9TVsSAnZWagAApiQP/iErclrw4NWitn2JuWDu\nTGV8+zdRVulrXUTmuu9enhpgzuUoKP5UhSjXkIeY6YGtE4eLvIOec4yh2Sct\ngIHL8HnlhoF3FlEKdSh72ODMxI7sErnJU4N8e+S8hlmVSZ0PC5lFR60ai1Yv\nTskZoKSGhn+uxsN6dyZ/PpGx1OsZWZv73Bc9KO/ffQjDgBFEI8zcSqatslf5\n1yTFA5HeGY6i3mioL1JBbao/Zsp2Z5dEq8Y8EZ2aSKpxtngIja8vEilFlt2t\n1w7c/rdMtij1LD2fBjJXHn3Ih+R7ZRSmPhgOFLf6xlNyOPubk/lDotNAj+aM\nMz5PbqNwOVmnXLaFvN0qGlLYBEQ0uJpTryfzjEqFz02Q6YudMn1XdcSHRcjI\nXQhSB9SWk4bMcah5JmaEq8ht3NOB4FHhV56OXl2HMDFidw1tZ4L+xuTd5Kuw\nz4eev2+oWg6wla5ynwaNHC23D2Gw0H22Y4q6TaXpdR/lXRF+zO7keMLAAH+e\nyhZeHaf15BXrIZLlKBa5V65CYhaCoPgLDCOAaTJtUMwvnjN2RT1hQ/O8SuWl\nrw6FmAkwETDir08mEAT2Y6z3lsqPWjcgKAlqY22mvmmnEN0L4hrShMoHV0cg\n9qLLq8hMbsITc1gqESJo+rZcRWapsZD8I6Dkh2/HTnSN0ceq8j3i1LLClFl8\nhBKx\r\n=IXh9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/Chart.js", "unpkg": "dist/Chart.min.js", "gitHead": "1d92605aa6c29add400c4c551413fc2306c15e8d", "_npmUser": {"name": "etimberg", "email": "<EMAIL>"}, "jsdelivr": "dist/Chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "11.13.0", "dependencies": {"moment": "^2.10.2", "chartjs-color": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"gulp": "^4.0.0", "karma": "^4.0.0", "yargs": "^12.0.5", "eslint": "^5.9.0", "rollup": "^1.0.0", "jasmine": "^3.3.0", "gulp-zip": "^4.2.0", "clean-css": "^4.2.1", "coveralls": "^3.0.0", "gulp-file": "^0.4.0", "gulp-size": "^3.0.0", "pixelmatch": "^4.0.2", "gitbook-cli": "^2.3.2", "gulp-eslint": "^5.0.0", "gulp-terser": "^1.1.6", "gulp-replace": "^1.0.0", "jasmine-core": "^3.3.0", "merge-stream": "^1.0.1", "gulp-htmllint": "^0.0.16", "karma-jasmine": "^2.0.1", "gulp-streamify": "^1.0.2", "karma-coverage": "^1.1.1", "eslint-plugin-html": "^5.0.0", "rollup-plugin-terser": "^5.0.0", "eslint-config-chartjs": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-firefox-launcher": "^1.0.1", "rollup-plugin-commonjs": "^10.0.0", "rollup-plugin-istanbul": "^2.0.1", "karma-rollup-preprocessor": "^7.0.0", "rollup-plugin-node-resolve": "^5.0.0", "karma-jasmine-html-reporter": "^1.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_2.9.4_1603045477171_0.9417086785721611", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.5": {"name": "chart.js", "version": "3.0.0-beta.5", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.0.0-beta.5", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "61247ccf55d65bfc34103c5e01605bd99e7f24d0", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.0.0-beta.5.tgz", "fileCount": 15, "integrity": "sha512-GTIxCseeE2JqnT/N/+IC8qf3Issm2YFihacwSx7uTkzUxugzSfuq8qrbKyKojlvvvER4ZrfanEmxINOy4+QuHQ==", "signatures": [{"sig": "MEQCIFxps0600E0l/Fxkm5wVtPKBvnGIYvtkBJTzB49G3kK6AiAsE/2AxbnCkUvEPW8TC3Jd2/dNSGs1g8aa2qnR8XR7tQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1053638, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfnrnACRA9TVsSAnZWagAAcZEP/0GyJMRRaaZDMV7OSiH0\nfaUUHyzk3N2PTUsAw0XPQt9iWSerhE4h0AjqibEAYDXDLunEIym31Cd9ffwF\nmUbLeBwaWpZocgTOQyb8mKfPNT7uSNX1+q/bEQplS5sFZEGUoCFu5/0EKELG\niNRpi3/s9+Y9jlA8ak0lYJy5dKxwI2vKMfBbBh7eZgo4vFO2/3aS/DfhmFYW\n+nV7v7DaupqOB/TEYnIVVsktr2Cq9wcCexrok5dLNCZuzbELnMdj0K87vlMZ\nCqRHwo4ByCs+cwlejzATAp+gbsxCjx+/Rg3MDLOsKaivSBRtnSiTPJd/+QZt\nhMBdWNBz0H9ArKw1P2FBfKErxT27vVE2pyPlOLjzTJqIsMNWAL9ecfJ4mlBr\nbsDYdMkmfitDDMDJGJnAmfZGbe6InB9aT2XUQMNVguCmjOdO1VupUB0AsZke\nz+0SoNLRk6767LUqPOruHYGWUSV/Nlb/RdUe/yyZpvXwh0WooeLH36+I6K8T\nsptLb6MtAixfS4RC/M+wxL7wEevUoue2MlCaEZUvrceYYemHv2/trxTLrxPI\nN5mBYRZuhY7e3R2FDpyLlCkAxesuQjKotYBWKlasci/c489Y4jZLxMvtI1E7\nV3IOk669CgefRu/7EOBn+b01wysUM0oKJOYCBWQfc22/AThdlDtR/cfR1sYX\nb6q4\r\n=8lE1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "dist/chart.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "4daf37e864872d7fe244f17f1820798523d9d043", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "cd docs && npm install && npm run build", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "lint-js": "eslint samples/**/*.html samples/**/*.js src/**/*.js test/**/*.js", "typedoc": "npx typedoc", "lint-tsc": "tsc", "autobuild": "rollup -c -w"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"glob": "^7.1.6", "karma": "^5.2.3", "yargs": "^15.4.1", "eslint": "^7.11.0", "moment": "^2.29.1", "rollup": "^2.32.0", "jasmine": "^3.6.2", "typedoc": "^0.19.2", "coveralls": "^3.1.0", "cross-env": "^7.0.2", "pixelmatch": "^5.2.1", "typescript": "^4.0.3", "@babel/core": "^7.12.3", "concurrently": "^5.3.0", "jasmine-core": "^3.6.0", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "promise-polyfill": "^8.1.3", "@babel/preset-env": "^7.12.1", "rollup-plugin-dts": "^1.4.13", "eslint-plugin-html": "^6.1.0", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-babel": "^4.4.0", "eslint-config-esnext": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^4.0.2", "babel-plugin-istanbul": "^6.0.0", "eslint-config-chartjs": "^0.2.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "chartjs-adapter-moment": "^0.1.2", "karma-firefox-launcher": "^1.3.0", "@rollup/plugin-commonjs": "^15.1.0", "resize-observer-polyfill": "^1.5.1", "karma-rollup-preprocessor": "^7.0.5", "babel-preset-es2015-rollup": "^3.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@babel/plugin-transform-object-assign": "^7.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.0.0-beta.5_1604237760446_0.46934992257676145", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.6": {"name": "chart.js", "version": "3.0.0-beta.6", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.0.0-beta.6", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "99d3604cecb8173f42e4b1d6b7c26a832e9c2784", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.0.0-beta.6.tgz", "fileCount": 15, "integrity": "sha512-Dq7veoux6ccaUHoA9Z5P5YvOqWWqLJcl3MNFZn+mYyyVqrtPbxXqWCzYwUlU7rovHcLy9FwoCSCGECcRN03FIg==", "signatures": [{"sig": "MEQCIHCnlnN/oVXLd3rllopmfKZbeT011WbcA1L/8IRDftACAiB1hOMaGgdwMI+T4mtJIsPrD1TOyrdZGucLejpbhbnZGg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1056834, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfqAzICRA9TVsSAnZWagAAnYIP/0s+bUqjtqlhhc+gml9l\nhLyiBkQrxftcOaEZVHl8zneoi48IvNqPViJjA+A0ylEhT+cXtae3OQ9tqK3L\nWyMZFd3h7PzCYKRxq3778u6DwPrUcjzBbZjpFzgAsT2qTZG4o5+rY6jXZxqd\n3ZzNuxO66Ua+eb3JxncL9X5LR3uacTrvUTsTxDfIvQEwmIWf8trv4oeokDzI\nzTdbBSdqNbQn6nvg4aaK18WzC1rZieB/Q4EVdzSzxkc0texhIninJWUgCFeY\ng7JEnheVx2qDR9+OtI/I6k2kBQPIsAykHYi0rfl8n4Wj/9inVLHnvRfWqG0D\n9CJ4JdqCdm1aEu0qYNM5Q41OplQfvwvNYoHl/qKubI55zJ9Pt3NFBTRQx6GK\nO8QtnwvhVepJ+T6BEqqRJ0Z4IwL+yYKiQkix4LpQ80Zw0o/rlAtFnG9s7h3s\n6S8ETm5iqjT6hTOM2KPdZF1vxNGLsyB7yr0kxjPYvlR4BMB/6No+1O8LSEs2\n/5Rqj/YyVS27oWCitlitzzOArs7bV/bHU8sKmctjXF8C24l9xnn91G0IQxHW\nm0IAGi3mDL+2CAuu7EHIr+PMn/kLeMvsic/WVKChIohRRjzc5pMr8g83TTiz\n2Aj31ugXanEskaNB2isMEJb2hI68qixX5jY4EzVkJCHo+VvQXZ8jUy+xR7rX\nUwrJ\r\n=A6nM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "dist/chart.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "7ad91817e7bd488c404a099c31b881b594267e3b", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "cd docs && npm install && npm run build", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "lint-js": "eslint samples/**/*.html samples/**/*.js src/**/*.js test/**/*.js", "typedoc": "npx typedoc", "lint-tsc": "tsc", "autobuild": "rollup -c -w"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"glob": "^7.1.6", "karma": "^5.2.3", "yargs": "^16.1.0", "eslint": "^7.12.1", "moment": "^2.29.1", "rollup": "^2.33.1", "jasmine": "^3.6.3", "typedoc": "^0.19.2", "coveralls": "^3.1.0", "cross-env": "^7.0.2", "pixelmatch": "^5.2.1", "typescript": "^4.0.5", "@babel/core": "^7.12.3", "concurrently": "^5.3.0", "jasmine-core": "^3.6.0", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "promise-polyfill": "^8.2.0", "@babel/preset-env": "^7.12.1", "rollup-plugin-dts": "^1.4.13", "eslint-plugin-html": "^6.1.0", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-babel": "^4.4.0", "eslint-config-esnext": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^4.0.2", "babel-plugin-istanbul": "^6.0.0", "eslint-config-chartjs": "^0.2.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "chartjs-adapter-moment": "^0.1.2", "karma-firefox-launcher": "^2.1.0", "@rollup/plugin-commonjs": "^16.0.0", "resize-observer-polyfill": "^1.5.1", "karma-rollup-preprocessor": "^7.0.5", "babel-preset-es2015-rollup": "^3.0.0", "@rollup/plugin-node-resolve": "^10.0.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@babel/plugin-transform-object-assign": "^7.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.0.0-beta.6_1604848839656_0.25398944912414323", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.7": {"name": "chart.js", "version": "3.0.0-beta.7", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.0.0-beta.7", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "ce11f437b555cc61b52ad24bf9505d08b4e09e6f", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.0.0-beta.7.tgz", "fileCount": 15, "integrity": "sha512-UL0HlWNRBvvzGPoh0WfmsiaLuYdgjLj9PtutfEjL43eJxxDPHWmsqROJT7x81oyF6TVrFs2uR5UEkt0GfhGxxw==", "signatures": [{"sig": "MEQCIBBqvFX7s+SlMSMtOwSe8HkbYiw6X/ZtXsr6yEj5QSTcAiBjtmS9BnlmDWoBNT2wlOCy37GlhZoupkqP47tDMkdYJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 960725, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfyqQmCRA9TVsSAnZWagAAtoQQAIwW5cFD4ITU215jITGK\nMQj9X/fU0x5JdK8cg35/lVi5605QKDufFK+P8yOGcGzN94X9LF5dMzSMaLBN\n8NnqVLs6sn23MRbMH+KFswWEU+dkwgBLXJDTxSLglD5eM3oBXc/gk5QTbjLQ\njLWp9Z2mvbTm1yH5vHqv/hEhCOBHYKEGO/TGuVqn/9TI4HSX7QCXqSDxeLgq\nV4RTDvZ3XzG71hZqip5MMnWS7wYAPF3oX5CiYFt4mmzV8M+j9ZoZJOjTKl5y\noyLzmxD6J31rz+ntisEnnb9qdjDWwOF1pdsQ+tw2390jQJao2aYEyg77FIJT\nkLngM6KdhrLHtIKPoRnreCXTtOxTlYTJL2JtsDKkZcSWo6UIUH74U0UO78Td\nWfZ44Tf6qY9h4S+YAzlQi7C6MR6QSm7cSLet4erJSo6yFJhgCFyxuHFWErE3\nQGkoSAUQy4zFOuUdabgnCc4BvLXFwATVoCg9/HRZJgopmtP2hOlxrDQXTdjw\nBibiubm5ChiNRqUcYR0bJB48WdWig88aQfX64TCDTzNHeHCXG9FV1Qm1C10w\niRz2kjTS7HvaKYZ/f7ZdFnPYghU0QJ2NwtTEqp/BlWH+mBGshhFW24nHXzHc\nwd/9+V+kfttVDdxUW6dd0NdqBazJtbYwUcsbd5uEMEVC6uk6DMZ08vm8qABF\nHWZ9\r\n=xY52\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "dist/chart.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "a7ff11654704afda4dcbfd69bb5c8a81a3861070", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "cd docs && npm install && npm run build", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "lint-js": "eslint samples/**/*.html samples/**/*.js src/**/*.js test/**/*.js", "typedoc": "npx typedoc", "lint-tsc": "tsc", "autobuild": "rollup -c -w"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "12.20.0", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"glob": "^7.1.6", "karma": "^5.2.3", "yargs": "^16.1.0", "eslint": "^7.13.0", "moment": "^2.29.1", "rollup": "^2.33.1", "jasmine": "^3.6.3", "typedoc": "^0.19.2", "coveralls": "^3.1.0", "cross-env": "^7.0.2", "pixelmatch": "^5.2.1", "typescript": "^4.0.5", "concurrently": "^5.3.0", "jasmine-core": "^3.6.0", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "eslint-plugin-es": "^3.0.1", "rollup-plugin-dts": "^1.4.13", "eslint-plugin-html": "^6.1.0", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^4.0.2", "eslint-config-chartjs": "^0.2.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "chartjs-adapter-moment": "^0.1.2", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^2.0.1", "@rollup/plugin-commonjs": "^16.0.0", "karma-rollup-preprocessor": "^7.0.5", "@rollup/plugin-node-resolve": "^10.0.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.0.0-beta.7_1607115813556_0.232861260115667", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.8": {"name": "chart.js", "version": "3.0.0-beta.8", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.0.0-beta.8", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "6121be8a6cd6d8b2a3942c8a7fbfdc64344d3474", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.0.0-beta.8.tgz", "fileCount": 14, "integrity": "sha512-Zdh8ZUGb44ZXYQhfPSZJiTaAE9AEar5ZRJ7YRFDJLZa+g7e6tTfKCojD+AL4DSs5CREEAktMvv4zD/D0lET1VA==", "signatures": [{"sig": "MEYCIQDmmObnxfF44RYTl4g9sMtkhONcJK26ybSwsRXMzfMdHQIhAJTqb4hPyJxPRIhxIKAhaHD+6HvX2jEf2HK8GPbuc4qA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 959672, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf/u3qCRA9TVsSAnZWagAAufAP/0ROkdfby1SX3VhOtQib\nsxR5Q8DmmPp+iLHMIUmnapwa7wpco8ZUkKfqybeUbOsYkFAsiRoRgzfaLXOU\n/CMRiUYA5J5mPqwTUPZO6xcIyQLtMHyyuACCLEa2LpPnMWZZ6j71S41nSyvX\nL9KvH9m35zCfzLxWeAJvVaskm1D32kFGK2i5y7T3sWzg+BqM4WcoZb/Hx0i+\nM9qTGEmYo3QURRbW8t1JbLtno7wAlXC//8lm1kZDwjgV115CBsGcvP+4V+cg\ndh7Futf1TdlAkf+bvgMKXypDGO0APM2TDNOeT2aekWEFf5RkAg8zxB1dXpQ5\n48R5ON+qJ1Yn7JZHICbpyAnN/X47F4D/AFB1iOrUnMU9ckDIVXG93qHkG8+e\nHu3sF99YE5R/0wl9GUL30gglQTYtClGnLWUkVfvy2qFDebw/m4N/4igga4uA\nGp9zYVW2O+CA/j1oz3fCBjRHPuTNHf//Sc0HD0qa8ZoXbNHurvZ259oEbUm1\n2473jp1TOz884YrMlNwfSMy3ISuKjYLYSMlgZrGZqkwgLaWMN9Lr2Vl9x7+E\nrgabVje9yiUJFxAIgYnM844KRQfaRrn/y52aYTUkW3Fn7fs71Uyp/4EgU/S9\nplcxhKNbPo022rG1MfIkGzcldyJDMvEqK6geIxWSG71uZQsoEFqQQ38E+na4\nmyZX\r\n=XSMF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "dist/chart.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "ad941813b660a8420f56e58288eaaaa0d9ef33a3", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "cd docs && npm install && npm run build", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "lint-js": "eslint samples/**/*.html samples/**/*.js src/**/*.js test/**/*.js", "typedoc": "npx typedoc", "lint-tsc": "tsc", "autobuild": "rollup -c -w", "lint-types": "eslint types/**/*.d.ts"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "12.20.0", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"glob": "^7.1.6", "karma": "^5.2.3", "yargs": "^16.2.0", "eslint": "^7.16.0", "moment": "^2.29.1", "rollup": "^2.35.1", "jasmine": "^3.6.3", "typedoc": "^0.20.9", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "pixelmatch": "^5.2.1", "typescript": "^4.1.3", "concurrently": "^5.3.0", "jasmine-core": "^3.6.0", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "eslint-plugin-es": "^4.1.0", "rollup-plugin-dts": "^2.0.1", "chartjs-test-utils": "^0.1.1", "eslint-plugin-html": "^6.1.1", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^4.0.2", "eslint-config-chartjs": "^0.2.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "chartjs-adapter-moment": "^0.1.2", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^2.0.1", "@rollup/plugin-commonjs": "^17.0.0", "@typescript-eslint/parser": "^4.11.1", "karma-rollup-preprocessor": "^7.0.5", "@rollup/plugin-node-resolve": "^11.0.1", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@typescript-eslint/eslint-plugin": "^4.11.1"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.0.0-beta.8_1610542569519_0.852895263878563", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.9": {"name": "chart.js", "version": "3.0.0-beta.9", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.0.0-beta.9", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "dc43e678c3610eb45689dba64df2196b1f1c48d9", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.0.0-beta.9.tgz", "fileCount": 14, "integrity": "sha512-<PERSON><PERSON><PERSON><PERSON>+URWwgFGlGg2YFBWfZG4KaoiX3VEIjwt/uQgRG4sNBcsl95+spw/xkaVV6vWKVKBG3bDaIWIQ3v6Qf83jA==", "signatures": [{"sig": "MEUCIHsNqMpKrryiPHEcMm55klzYij/HLys5tFnNQJRli7EEAiEA+YIWtJgRMb5lA27R7D/9ja6O/1JL3PUmgZ71tuvv53Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 959179, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgAz0JCRA9TVsSAnZWagAA0bQP/jIEfmWavRxObhvTm/YX\nZWO5kSBwJZC5BhQ1EbUhd6jDX4vMUHx4TXRXP5p5LPpCyHC8/1qu30fffNKg\nZ2n247dZJAWy+oyRUz/zkcbdDAXfQvWgj4pIyDa9otin9PWVY3V2TEJ4qxDW\n1FCwJZu0v3//BMc9YLHLVuLPYroM9jcTbMn497m8Fst7Lt/wDzaUPgHeHuoF\nQlSRJBueygVWOXXqYRg62HJZXUdqsDmugfMLXeC2r2jIiCinbKEnv8rTSQbc\nva/Hx1t3v5iF2yb3J5vtxMqTqW2VeBxB9g5habNGb/wYTF2xxiK9AzNOTO1x\nWYDiM4fPKnBmKk3IPXqYuyj8Uztidt4pifMYUoeCUO+7Y/VJXH+eiEndnPqO\nbQraVweZ6/LjrB1dxDnXj7F/VTkI3SbljsnUTKexgpLIbPqUv90By0hYTu0a\ntlDJzfxXZDxYQzHGR+XPgJXkKaQg6egjdTSFE7ITm1QIBWlwZBLt69N77Oox\nShzb1aT1RJOpE7uDeObqL8YcAmrFRnqy7bTgDR7EUPeXnEiFLd+SYOy4FZVP\nQ4gtPS71Zz2YxV7Wh6krtgfOsVdIPoSAPZF1/hyepSAGCabX6MXuafwq7fSH\nfa9FksRBThrSxQfmUYdtjA5Mk3Rn+l7Znxw51KDwWCITyMRsMCivMxmp9ANB\nEEKf\r\n=m4W7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "dist/chart.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "eb88909e95e1c7e96343c096c259fb089dcff637", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "cd docs && npm install && npm run build", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "lint-js": "eslint samples/**/*.html samples/**/*.js src/**/*.js test/**/*.js", "typedoc": "npx typedoc", "lint-tsc": "tsc", "autobuild": "rollup -c -w", "lint-types": "eslint types/**/*.d.ts"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "12.20.1", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"glob": "^7.1.6", "karma": "^5.2.3", "yargs": "^16.2.0", "eslint": "^7.16.0", "moment": "^2.29.1", "rollup": "^2.35.1", "jasmine": "^3.6.3", "typedoc": "^0.20.9", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "pixelmatch": "^5.2.1", "typescript": "^4.1.3", "concurrently": "^5.3.0", "jasmine-core": "^3.6.0", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "eslint-plugin-es": "^4.1.0", "rollup-plugin-dts": "^2.0.1", "chartjs-test-utils": "^0.1.1", "eslint-plugin-html": "^6.1.1", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^4.0.2", "eslint-config-chartjs": "^0.2.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "chartjs-adapter-moment": "^0.1.2", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^2.0.1", "@rollup/plugin-commonjs": "^17.0.0", "@typescript-eslint/parser": "^4.11.1", "karma-rollup-preprocessor": "^7.0.5", "@rollup/plugin-node-resolve": "^11.0.1", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@typescript-eslint/eslint-plugin": "^4.11.1"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.0.0-beta.9_1610824969264_0.480422343255267", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.10": {"name": "chart.js", "version": "3.0.0-beta.10", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.0.0-beta.10", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "d4ebbd40939d18e1677e036733f31276ef24d835", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.0.0-beta.10.tgz", "fileCount": 14, "integrity": "sha512-zPK5c7jHB3YAxICluB4QDbgOSN6s8LUvPjdvDwRBgwtbTGKe1DvyVCUIVBSXGh30pe6L3s6q4jur9A1PEd7/YA==", "signatures": [{"sig": "MEQCIFDB57jGG4sOdPDx6oZF241mlCW4NSvMFFnwh5a72vu+AiAjkb6ypfJKcD9UttHeDZf3Y4uVQrCjB51uYEazqeTGPg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 967556, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGHdECRA9TVsSAnZWagAASj4QAJlb4mfrCGfvjElYTazM\nGUupgbHxeKMsEfTy2VH1P9i5R/LHmTavaLsgK7uCAkKojHDyG0uzzlbkNsdT\n7eJdQXUW+S8KeuAOsN1qhkXT5ZwcRpgqfFaPY6bGsqRHuuM8EZJ7Dy8TpK+D\nmsRVreNQJnztp+w73b2PqHnKxonMZnTfa516XjTeYMDbCN+OVrYbXyoQ1i3a\nyadsZmBPl3UB2IoWgL5zfC7sTAZWomL+HAoVh+BZrCLr4dIyp5L+ucm6oudz\nVhYx659eCsySzFmdqu6C1OlW/Ryaby3H+bR1OGieGnJguBH5VCazqlxhIe39\n5/jfiEkK9atIf63T+eYSzxt85SSZE7tjbx6i1TSXIzeK9mKEDj6YV7ZddRqf\nHGlK1G2g1vIRTk4zdLWsOqUzN0tFWn675PaZoKPj4C2w/eYH+AQogb+SfWtU\njBP+0GJum9X3F+hmg6mvQoQG8ySR/DDjHWD/SApV5nDs4Y+CWXFIqqgqNK+t\nQZiVDijeD9e2Gpiz/f1nh6PSYUi9CKU4C5theQIRy0NmRnOO0Hv0vqFfhZF4\n0mJ5vj7orGRCmSdTVlcn8pElmTGoElwJMGYsu3zR/3VWuBQVSDmwON36yX6Z\n9rMTZsTsKhwHlnlfdZDvd/KswRFdIh8uO71RNsbMalGwBSPITRyGly75aNEa\n05Z+\r\n=oBRF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "dist/chart.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "650956b2e1ec8a9591135476cc61f64ddaad7f12", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "cd docs && npm install && npm run build", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "lint-js": "eslint samples/**/*.html samples/**/*.js src/**/*.js test/**/*.js", "typedoc": "npx typedoc", "lint-tsc": "tsc", "autobuild": "rollup -c -w", "lint-types": "eslint types/**/*.d.ts"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "12.20.1", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"glob": "^7.1.6", "karma": "^5.2.3", "yargs": "^16.2.0", "eslint": "^7.16.0", "moment": "^2.29.1", "rollup": "^2.35.1", "jasmine": "^3.6.3", "typedoc": "^0.20.9", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "pixelmatch": "^5.2.1", "typescript": "^4.1.3", "concurrently": "^5.3.0", "jasmine-core": "^3.6.0", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "eslint-plugin-es": "^4.1.0", "rollup-plugin-dts": "^2.0.1", "chartjs-test-utils": "^0.1.2", "eslint-plugin-html": "^6.1.1", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^4.0.2", "eslint-config-chartjs": "^0.2.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "chartjs-adapter-moment": "^0.1.2", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^2.0.1", "@rollup/plugin-commonjs": "^17.0.0", "@typescript-eslint/parser": "^4.11.1", "karma-rollup-preprocessor": "^7.0.5", "@rollup/plugin-node-resolve": "^11.0.1", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@typescript-eslint/eslint-plugin": "^4.11.1"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.0.0-beta.10_1612216131581_0.27951358746654664", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.11": {"name": "chart.js", "version": "3.0.0-beta.11", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.0.0-beta.11", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "0ce0ae746e9cf33a5773e5be28bd980eb792a347", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.0.0-beta.11.tgz", "fileCount": 15, "integrity": "sha512-PHSOFGXm3XMsI2P78xcgRZEBaYkPMJNVwaPrwOGT1xdSf79PsQ6PfA1ntnvl1HaX2qFPQr/pNfRLe5igcT2AGw==", "signatures": [{"sig": "MEUCIQCxtq7DKJavq7i992VBQw85N/+DSgqUaIku8e9GwHydqwIgapL7aI2Tak28jPsGDvN9Wt0w5yYnEj/a+UQWyqD2oUQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1033858, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgMSfXCRA9TVsSAnZWagAAlVYQAIpDEoctB1uUdnt8DIwR\nxT6YhIkNnRcdaUmOiw8ghDhjqbOsrrmjVTbH5IJHX+XkcFTXAFssT49ySicQ\n45l0+LnEU6LcNuzLYgP0MSPYGp9XdjwB91f5R52IbPbqfL9XZIwXTtzefIs8\nV/lO2wLAcUJm7yzWUUIq1N0mbBq5hnNfKqVMaWYXfq3wcAbEw/nCfnVCyLkw\n84OlNuMdfW3ewgKPqKRK6zF3zCCzh+V1v41HihhKQwy42IZ/FRvUZWMDExBx\ngsKD7sqsl1TeRql1BHOEcqfC0IyzFuWOr1y3Yv7lOYtcbcKUQXUt5eWqTMls\n5kNdnlHSJr3MxqMFQ54OrqNh/Lfcopu+jslgCgN3w9AaBJnH+EQI7duFiYSp\nFBAGHnnK0VLJN/B4UjmYoiPaBBKtDR9pLXpdLdS8ApeuU0QB0ySEzNvxw/je\nnnLR74T4p48G3OXdvYr1VYAXwFRW4V8MOiuuGJ6q05+7b9BjpoO6oU3+3wWq\n1Nd6xrqiYBjact6jux41AfLoaaemyAjwt1yrV1AMsZSAWqun9uLN9hYSbV7J\n6p04sncV+AdC5AIX79+dzOhRrG/nbGDex0b72rCbCGQp4zmqsXqzpijZgXnt\njgF84bXDLPXer51pI8WcSPLXYx08dVhsUVv0P0SYHffyoYf15nidxNlDhBIS\n135M\r\n=Cjxx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "dist/chart.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "d63542cd706c062cfc0b939277711d7b04a0cb03", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "cd docs && npm install && npm run build", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "lint-js": "eslint samples/**/*.html samples/**/*.js src/**/*.js test/**/*.js", "lint-md": "markdownlint-cli2 \"**/*.md\" \"#**/node_modules\"", "typedoc": "npx typedoc", "lint-tsc": "tsc", "autobuild": "rollup -c -w", "lint-types": "eslint types/**/*.d.ts"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "12.20.1", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"glob": "^7.1.6", "karma": "^5.2.3", "yargs": "^16.2.0", "eslint": "^7.16.0", "moment": "^2.29.1", "rollup": "^2.35.1", "jasmine": "^3.6.3", "typedoc": "^0.20.25", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "pixelmatch": "^5.2.1", "typescript": "^4.1.3", "concurrently": "^5.3.0", "jasmine-core": "^3.6.0", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "eslint-plugin-es": "^4.1.0", "markdownlint-cli2": "0.0.13", "rollup-plugin-dts": "^2.0.1", "chartjs-test-utils": "^0.2.1", "eslint-plugin-html": "^6.1.1", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^4.0.2", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "chartjs-adapter-moment": "^0.1.2", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^2.0.1", "@rollup/plugin-commonjs": "^17.0.0", "@typescript-eslint/parser": "^4.11.1", "karma-rollup-preprocessor": "^7.0.5", "@rollup/plugin-node-resolve": "^11.0.1", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@typescript-eslint/eslint-plugin": "^4.11.1"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.0.0-beta.11_1613834199104_0.7082811848966444", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.12": {"name": "chart.js", "version": "3.0.0-beta.12", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.0.0-beta.12", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "3aa42194b65fc4671ed29b7f4b21b1792b6d57f6", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.0.0-beta.12.tgz", "fileCount": 15, "integrity": "sha512-+VT+7hEfJMhjtpudOkRpafolWM6YcoXXK+1Kgujy9n06Fzd/w//AYyQfRog/b1cQRe11iMx2UA+UdxlsmJjCnA==", "signatures": [{"sig": "MEQCICeQSXpxedUu7Tv1fpjvhkK/fOzWU/+CRs5+CtTrx6muAiBWN908BGGKNUJEIcov6o4hJ2Oqf/K6l+iDr/sp9IGcJQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1044410, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgOpIVCRA9TVsSAnZWagAAlDgP/ie/JARcDIwUQL0NM4n6\nTx+w43ngUWujWVIh7GB0okBso3YpEETwJfpfGEjycVU32mLxj8UHxUWXrE6b\n/2JtFETalWCW60h6Aug2cdKFf1mvZAxpTvKqh5FOmcNNKYiTprRlKLepOb2V\n3JqPKXkTPL5sOKmd+xvbdgdyRT8etNO5cp/6P3NLaBhy2Vf6hqzQmTcq9Qe0\n92Hbo8vuDCBENfgqWxRi19ui5ZG3Lq0ajX0QqTSnlxfjHg0J+rvMKznbFTps\nN5lytdjY34uXqPahiyPUFTf2kpacnmKdF/ararDcyeZ3sVm15j91QKftRhTG\nlfruXA7RqZMo3UJ7U4ioEnLE7a2MvnUQQwgDts6WdiAXp0B5CWvw8y8TTBmZ\nKs4XwCBbDx6GrVLY7/l4oDTcZS6Z2IZSzVWj6hSQzFZlllC5Fye82kuWYgNV\nbyFn6bQWP5Z6nTeH7BCFiC198dONo9N8FFF14NheLw7emxmqjF0Pny2DB/pd\n/xG1yCuwMkQRd//FAHLoNBz2cF5Ln0tHJA7rDqZJjlo6Tt9QgDdB7VQwRdgE\nFu8j7aYgADu8q0kRS7ZlbelTpntVsizgMYlJBQr8j+jv581aA/rwqczK9Ycc\nWdSnYB3h0vaXUUUCDgEps/kZM4iVqRFPDp2FKamnQHK65cTaf6YG+wC9NbdS\nApzm\r\n=xmyv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "dist/chart.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "f23807f743d8565be436ee2d1413d28d461c96fe", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "cd docs && npm install && npm run build", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "lint-js": "eslint \"samples/**/*.html\" \"samples/**/*.js\" \"src/**/*.js\" \"test/**/*.js\"", "lint-md": "markdownlint-cli2 \"**/*.md\" \"**/*.mdx\" \"#**/node_modules\"", "typedoc": "npx typedoc", "lint-tsc": "tsc", "autobuild": "rollup -c -w", "lint-types": "eslint \"types/**/*.ts\" && tsc -p types/tests/"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "12.20.2", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"glob": "^7.1.6", "karma": "^6.1.1", "yargs": "^16.2.0", "eslint": "^7.20.0", "moment": "^2.29.1", "rollup": "^2.39.0", "jasmine": "^3.6.4", "typedoc": "^0.20.27", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "pixelmatch": "^5.2.1", "typescript": "^4.1.5", "concurrently": "^6.0.0", "jasmine-core": "^3.6.0", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "eslint-plugin-es": "^4.1.0", "markdownlint-cli2": "0.0.14", "rollup-plugin-dts": "^2.0.1", "chartjs-test-utils": "^0.2.2", "eslint-plugin-html": "^6.1.1", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^4.0.2", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "chartjs-adapter-moment": "^0.1.2", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^3.0.0", "@rollup/plugin-commonjs": "^17.1.0", "@typescript-eslint/parser": "^4.15.1", "karma-rollup-preprocessor": "^7.0.5", "@rollup/plugin-node-resolve": "^11.2.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@typescript-eslint/eslint-plugin": "^4.15.1"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.0.0-beta.12_1614451221353_0.4085007644192953", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.13": {"name": "chart.js", "version": "3.0.0-beta.13", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.0.0-beta.13", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "0cbcb379c75ad293aa0f1929907d9a6787760d1e", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.0.0-beta.13.tgz", "fileCount": 19, "integrity": "sha512-AYWrxh/pPwjStIrbmmowacab01vsp7oZrvghzQrexUn6jBSj+iix9jcRx/sxDHJHfEgG+azG1BkULtDuSXdLaw==", "signatures": [{"sig": "MEYCIQCGYUNOHyphx8A1mVLG25LpGSPDFaaKRKJMLKFJXNimagIhAJtGOO7tpBfY+Be6ki7X5VCrjdytXW8os1LXiOmXuIIC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1049789, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgQ6ZTCRA9TVsSAnZWagAA5qIP/25I5IU7JH0o1QSUormf\nuUU+nQadFn//A84mTonp6yGkURdw5EitgNgSjSQMNuQLp43em8qQ/Fq2UiYc\nNkQlid96gLzX9rzGbmBt7KaNbZmrkCZNd3Tnvcj3h0anxGevMgC0cBxh5ChD\n5HLNyeD+e8xl1RRtsp+rU6/kzM/UukHW+y+FShjALwBC/p7keoMlPemU9HTT\nxWDj0PXmBIfmOQK/s6UX5gxKUQdhm/z1J1wldnl4WMlJvkrWBLSTZrF+AvTI\nZzNrk7fWktDznOrEewa0A0USHwHeabsvwbjuRp6Om35t5bpX9wUGcjmWegJL\nglb4b/8nQkzer+lTRLFd3nVSbjPL0/LRHU0JcNVeGZBwzVvb8SUb3aPl0yG6\n33NjNeTqS505r3xambjTJKRCrkoWv1bvUVJWcPB//Rg7W9KiOtlgUT1s+G1h\nJDyTEUqcEQNa6Nmvj1Ymp65Ru4mge1LSLOoXkDhUGrvAo4myHb0SE3LCChgc\nFIT7vA9BX+SvL/kxN/msIfSJTIgaBqvANsqK0tO/6+zC/VzdN95bJ0+FMUhj\n0RU3M+AGumcHqiZZv8TK+B/Y89AUwzaWP9ddodkZMOuC/vOYDL2hM0NpQ06F\n9LG0fYKlCTHdknNNdn6XLjPLdmiyd6iS5QH0rjE2hk+9HDqpS6SS9m8BORUC\nTqBv\r\n=Ioge\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "dist/chart.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "1e6a3fb8e69f5665142f64c712ab5d6c4ba7efea", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "cd docs && npm install && npm run build", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "lint-js": "eslint \"samples/**/*.html\" \"samples/**/*.js\" \"src/**/*.js\" \"test/**/*.js\"", "lint-md": "markdownlint-cli2 \"**/*.md\" \"**/*.mdx\" \"#**/node_modules\"", "typedoc": "npx typedoc", "lint-tsc": "tsc", "autobuild": "rollup -c -w", "lint-types": "eslint \"types/**/*.ts\" && tsc -p types/tests/"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "12.21.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"glob": "^7.1.6", "karma": "^6.1.1", "yargs": "^16.2.0", "eslint": "^7.20.0", "moment": "^2.29.1", "rollup": "^2.39.0", "jasmine": "^3.6.4", "typedoc": "^0.20.27", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "pixelmatch": "^5.2.1", "typescript": "^4.1.5", "concurrently": "^6.0.0", "jasmine-core": "^3.6.0", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "eslint-plugin-es": "^4.1.0", "markdownlint-cli2": "0.0.14", "rollup-plugin-dts": "^2.0.1", "chartjs-test-utils": "^0.2.2", "eslint-plugin-html": "^6.1.1", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^4.0.2", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "chartjs-adapter-moment": "^0.1.2", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^3.0.0", "@rollup/plugin-commonjs": "^17.1.0", "@typescript-eslint/parser": "^4.15.1", "karma-rollup-preprocessor": "^7.0.5", "@rollup/plugin-node-resolve": "^11.2.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@typescript-eslint/eslint-plugin": "^4.15.1"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.0.0-beta.13_1615046226587_0.44256738473590196", "host": "s3://npm-registry-packages"}}, "3.0.0-beta.14": {"name": "chart.js", "version": "3.0.0-beta.14", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.0.0-beta.14", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "7b8e2016bc882a96279f4295058f7669b7da699d", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.0.0-beta.14.tgz", "fileCount": 19, "integrity": "sha512-YLPNkWgErPOHz3VllTUnuQh6Bxg5wsuxEBH6lqbScwnV73OzNeqViCMDSyuVJe3qW9wV7YJ8HrGIOaFg/ziEkw==", "signatures": [{"sig": "MEYCIQCY/Jwn+mwL6ll9zQUZWD9rwHt9BfiFcJPrNmmmbJmTuAIhAPyOPMpN2SLCuAFZPopbU+47QU1w/2XumD2MNUkhVrb4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1062028, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgUSM9CRA9TVsSAnZWagAAG3wP/jN+oL6SQtLBsUY9ZUSm\neUHC7zH7S1lQgSF5DBGL5A0TBqlAn38CIPkRZ70oCHaB6+BS/NQu9AG6ZOZY\njsxRodCgHYndaKXiF1HNjNuAfP3/DKiiTjr56mFA4aDFGCXoG8B+L0ZhfzbS\n3/STj1rCPQAmf/0TpSoVdoukZ3wl03HvdETnjgFBSky9X8+kxZfhS+T1lvcl\nX4Y8dGRmTfPJkzzBrkMxXMWdYmW9gtdT69FkV+CzAU9tPYWONRINzG9Wxojd\nxYjPs2jmmB4r//X1pHYQy0EJ1x58E+hBK5dx5FHTsUwzKFZYSmtGXcUQYKPn\n2C3DCuuotHlLxD5/5Kya+J9upBCwiLrZLYVg9vVQ4DMcpJNTsQfq2lwFQ9XC\nEpwBgse5V6ckxf5NKY26CjAiMG7XsGegpdIqF1Ruizb4JB867hj/FhfFWbp+\nxPnQwYrUhaGXNGTtv8HNsFbq5Yjy2IKvIr62uhzL3V+ztOdUQrcsIt1lv8F7\no2vEOuX/hD7hs/cjhm5k/wF6VQbJUXH4IPNKiP6FiiLmcr0zm0B4LlRQ6mie\nq9xlp0Ez9JGi+PiXtk9xLG66zqiPzglroW47+c1De5Z0LvoO+PWEnaEJXy9t\nsmasZzYmbRE2UfNhpQPRKUjV1zkWW9edbslqzdT4pSTSa0ASOsz7lSw3lZjX\nSjKx\r\n=Beur\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "dist/chart.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "b6bb24813a94e4b654e7d9e21554467ea9e8d0fe", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "cd docs && npm install && npm run build", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "dev:ff": "karma start --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"samples/**/*.html\" \"samples/**/*.js\" \"src/**/*.js\" \"test/**/*.js\"", "lint-md": "markdownlint-cli2 \"**/*.md\" \"**/*.mdx\" \"#**/node_modules\"", "typedoc": "npx typedoc", "lint-tsc": "tsc", "autobuild": "rollup -c -w", "lint-types": "eslint \"types/**/*.ts\" && tsc -p types/tests/"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "12.21.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"glob": "^7.1.6", "karma": "^6.2.0", "yargs": "^16.2.0", "eslint": "^7.22.0", "moment": "^2.29.1", "rollup": "^2.41.4", "jasmine": "^3.6.4", "typedoc": "^0.20.32", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "pixelmatch": "^5.2.1", "typescript": "^4.1.5", "concurrently": "^6.0.0", "jasmine-core": "^3.6.0", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "eslint-plugin-es": "^4.1.0", "markdownlint-cli2": "0.0.14", "rollup-plugin-dts": "^3.0.1", "chartjs-test-utils": "^0.2.2", "eslint-plugin-html": "^6.1.2", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^4.0.2", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "chartjs-adapter-moment": "^0.1.2", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^3.0.0", "@rollup/plugin-commonjs": "^17.1.0", "@typescript-eslint/parser": "^4.18.0", "karma-rollup-preprocessor": "^7.0.7", "@rollup/plugin-node-resolve": "^11.2.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@typescript-eslint/eslint-plugin": "^4.18.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.0.0-beta.14_1615930173178_0.378971339117238", "host": "s3://npm-registry-packages"}}, "3.0.0-rc": {"name": "chart.js", "version": "3.0.0-rc", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.0.0-rc", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "ee2b25cd0d8d1986a6fda872379840f2adce08df", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.0.0-rc.tgz", "fileCount": 19, "integrity": "sha512-Q3zdByYSHV1BhEyb2FDIQ5YxKQQJr3UtBH3DDQ39tsO/9Dme1EPxS0Iaay+Y5aArcaZj0lG73RlaGr/KH0dyJg==", "signatures": [{"sig": "MEUCIQDzVhDoXxB3EyQrVuFuSB44tl+i1/qO33PilQ6mQEbXcQIgPHmlt2sI54IWNQOPfkIM6+/kpL0+vbo8yO2SlTTu5No=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1060867, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgU8iVCRA9TVsSAnZWagAArUIP/353Mv6ZAy9iopF/Oh28\nIBezv1eHIKyHANJcSFkeJdYde+zAAzRGeUyqGe8wgSEfpEH5knsMDFQncV3Q\nVB6ByaLym1YLb2ftnYfi7uQaIZXBSGzmOXj9GiegS/uxVmcZZW2PAqVAQdBK\nfzR9bKwGFx5qK6/Vs+tJJc3ARiqyxqrlnowE/sSBMVfdlugNl9Ws0XN8xfMm\nYRBqXVQAlcQPyQ3FZVo3DgdkX+qIrhF3Y0HqchTXmaRBty/N8PeGdEFEHcjW\nSbciMUuBijzOoFoVu1atKqqQ4Qpz+zXbhbvWsV9caawicSfJ1cTTkseesq6c\nojrP+cMSRQeM38sMtdbi2JYBthQ2XcmK8kwlHrvdfRjRAskZC9CoSjUUKy3p\nr4m2KGh1o9DwYanOO9H5u3kBU0VQC0AftVvsCdRtLUV7barCX+TlG/hwX3p+\nkkP7YtUeiOTEOH4u1qcxL85NS+RqnMXVuj5YU6Jca6feeiKGt+3p+8EacfYi\nac2pFsOl8aCWKRyZmQeCvp3FJlMb50X3YGYcV5uklTrQkZeaN9PE3REP0hRx\nfo7j2+B1sXshToyVQEXo3ABRoPVkzsDbstpmgV3AMXrxxXM4xEEeTlMre2kF\nD3ZrTmKJheTQdPxVYhP2Q1yUCX/bIEULzUZPRDKw4/XFCEJmO5uWqZYLpGhh\nF4kO\r\n=XziD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "dist/chart.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "0b4eef54dde2cebd4c7c221f0a397a733710c1d2", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "cd docs && npm install && npm run build", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "dev:ff": "karma start --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"samples/**/*.html\" \"samples/**/*.js\" \"src/**/*.js\" \"test/**/*.js\"", "lint-md": "markdownlint-cli2 \"**/*.md\" \"**/*.mdx\" \"#**/node_modules\"", "typedoc": "npx typedoc", "lint-tsc": "tsc", "autobuild": "rollup -c -w", "lint-types": "eslint \"types/**/*.ts\" && tsc -p types/tests/"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "12.21.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"glob": "^7.1.6", "karma": "^6.2.0", "yargs": "^16.2.0", "eslint": "^7.22.0", "moment": "^2.29.1", "rollup": "^2.41.4", "jasmine": "^3.6.4", "typedoc": "^0.20.32", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "pixelmatch": "^5.2.1", "typescript": "^4.1.5", "concurrently": "^6.0.0", "jasmine-core": "^3.6.0", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "eslint-plugin-es": "^4.1.0", "markdownlint-cli2": "0.0.14", "rollup-plugin-dts": "^3.0.1", "chartjs-test-utils": "^0.2.2", "eslint-plugin-html": "^6.1.2", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^4.0.2", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "chartjs-adapter-moment": "^0.1.2", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^3.0.0", "@rollup/plugin-commonjs": "^17.1.0", "@typescript-eslint/parser": "^4.18.0", "karma-rollup-preprocessor": "^7.0.7", "@rollup/plugin-node-resolve": "^11.2.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@typescript-eslint/eslint-plugin": "^4.18.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.0.0-rc_1616103572941_0.3781985642598453", "host": "s3://npm-registry-packages"}}, "3.0.0-rc.2": {"name": "chart.js", "version": "3.0.0-rc.2", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.0.0-rc.2", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "2899079f074945f0100f4e15d8d86138f5919118", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.0.0-rc.2.tgz", "fileCount": 19, "integrity": "sha512-j/URLMDPYdJRcDft4bVtbeA48qDjitXNcAMGXhDzI+yenvgTyKe+TJvz0ISrqHvsk826w0BpaTTm9wmo4nSbUA==", "signatures": [{"sig": "MEUCIQDbodKc8fzNLeCETfkGSI8K0F1HdxPrHiZsxibqaeM0hAIgUIU3M5M0Aqau8VIfj6ZU/YfI1ZlcmPJmKDBuoIHKJEc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1061734, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgV20zCRA9TVsSAnZWagAAmc8P/3vEwbg9bSELK7qyxBEL\ngiycjvQsxAIl2VJBpxccxUO2KFMIO7ihZaY1IP//KGpf1Q010qxc5OFCtHx5\nepTGuoGxYKTsSeL2QIfH0BdCMMHw15PkNCDSlc5arZNQ1QzgMKpmWTe9d1C/\ntiUlJxvRTre1jKQ5RCpDGOS9FpHGC8F0CcG6eJwzXR/pF/64bzJBqfhYFhKi\n5pDNhGfAOoT9CZamyIfj4EvkOfw1FOV1B5JoWtOLAOI8D4GHyYe0kv4PqUg7\nMgXOpQ1TZ5kT+KWGOn3v5bGyiLaO5ZsHfAK6ox3aCTST+uDhMf/af+ltLPi2\n0fdGMAK8EILnMYM7lu8/92ENXXg2sf+vnFMqAlYy4g3g0NHsHmLw25+YGzQD\nSGBh9plTrRXu7QK8wngZXkzv7J8dNke4kUK6BfFKFvulLVmqMPV3mNvdS2w5\nEUuEST4NtibgNaz0sI/CKsoHOydcBtci5/1rT5OiLzdKZy/V7jzAlWQpNwmd\np8G+9pRc9uzlC7qOV8wuWj8nwHcJIcWxlyN06yoC55f921tlRmIeS7d4wMW+\nITiMuDqZ1Zms+vMFKbEhtntSOaTh5OJEsnhpXzYTpONNHd0ZCphJw6FHxv6Z\npunIDKYRoq+Uk1mJ4iD1FYkGrgYr84/IpgWZjM+VW7edfjCqC36QucJI66g0\nZKKQ\r\n=e8EQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "dist/chart.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "d79b5a3d60bba39a285f4eb878839dee2b4027da", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "cd docs && npm install && npm run build", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "dev:ff": "karma start --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"samples/**/*.html\" \"samples/**/*.js\" \"src/**/*.js\" \"test/**/*.js\"", "lint-md": "markdownlint-cli2 \"**/*.md\" \"**/*.mdx\" \"#**/node_modules\"", "typedoc": "npx typedoc", "lint-tsc": "tsc", "autobuild": "rollup -c -w", "lint-types": "eslint \"types/**/*.ts\" && tsc -p types/tests/"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "12.21.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"glob": "^7.1.6", "karma": "^6.2.0", "yargs": "^16.2.0", "eslint": "^7.22.0", "moment": "^2.29.1", "rollup": "^2.41.4", "jasmine": "^3.6.4", "typedoc": "^0.20.32", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "pixelmatch": "^5.2.1", "typescript": "^4.1.5", "concurrently": "^6.0.0", "jasmine-core": "^3.6.0", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "eslint-plugin-es": "^4.1.0", "markdownlint-cli2": "0.0.14", "rollup-plugin-dts": "^3.0.1", "chartjs-test-utils": "^0.2.2", "eslint-plugin-html": "^6.1.2", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^4.0.2", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "chartjs-adapter-moment": "^0.1.2", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^3.0.0", "@rollup/plugin-commonjs": "^17.1.0", "@typescript-eslint/parser": "^4.18.0", "karma-rollup-preprocessor": "^7.0.7", "@rollup/plugin-node-resolve": "^11.2.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@typescript-eslint/eslint-plugin": "^4.18.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.0.0-rc.2_1616342323055_0.13873586726430354", "host": "s3://npm-registry-packages"}}, "3.0.0-rc.3": {"name": "chart.js", "version": "3.0.0-rc.3", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.0.0-rc.3", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "4a5027757a9ea04a95be12e44c3b1250da1462ec", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.0.0-rc.3.tgz", "fileCount": 19, "integrity": "sha512-i1zYk6Kx7ya6aDIR8yxYTPlmPsEEZ8wUnbSpFx7dTin+9y3YnL07aOrGEBOfqlQcMugA+IwpnHW55ru66B86BA==", "signatures": [{"sig": "MEUCIBEL7amIX54Mql6EP+z4bhieTc8BXwV1utJlChVJCoTwAiEA6GbeunfuhKxME7xqOddN+DqY6duDGnNbbrFkOSG+jnc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1062397, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWk5VCRA9TVsSAnZWagAAJigQAKK9OPjrVC0g3guttOka\nMkkjybxr2lBohfaXmKNWjcoi24/RjE5kluxIWY6fblLmSVt1Vx+iucYVgNTN\nBov7a/8ABs/x0k5Cy3jSOIjiTh53sB2kgxldSv0GmYpBia+zVKXW5IrLGHjP\np5AXy4wYxz1r/2Agc2cSgxQ1GJKVpWvNGXIYpgB3TJ2J/x2N1Q5dIOIuTygN\nJ0qbkm6pfifCIYFjo2/AB0HWTWPUYBlgHaAF5m9ZXVio08WuY6HQKNz0ES4m\nQWLGLxzX9OFejP6seS66ep5LuxO5Bo92V4O6o/YtP5sE3C+7up5JDSoagHeI\naLJd5M1wcG82m5t9AymkQtnt2+04988Z3Unm9/wWKqCHHG+A0ueHkMTWWrWO\nOwRAi/C4ba2+tDQpjL6eeuJJUOQM7S4OwjAQxaKG6jYpYblBa+mn3limrTvT\nH6oBaYk3rl55hA5j7y3fsjcwHEC1whxzsPfMo2u97xWilrpsgz9QsrKUYM0z\nqrmCVARvtr4xtY6hADnEQKf5zM9JmHvyIIWB8xWU0TFbBLSWvqEeVAu4aVqn\nYIIokhBN9y1/0I2pX583oNdwed15u9YPwFhPRXFIU7fuu+8gpzx46oPevv0Y\nfrpzuWnSY+zsc+CqHdTsYkL7bAUU0hH79d0SCQShkqwyl3gxCrDo9PK7ljxW\nx/II\r\n=jCkD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "dist/chart.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "bbf298f4614c058e2ec86329566a56bfcd8bc685", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "cd docs && npm install && npm run build", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "dev:ff": "karma start --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"samples/**/*.html\" \"samples/**/*.js\" \"src/**/*.js\" \"test/**/*.js\"", "lint-md": "markdownlint-cli2 \"**/*.md\" \"**/*.mdx\" \"#**/node_modules\"", "typedoc": "npx typedoc", "lint-tsc": "tsc", "autobuild": "rollup -c -w", "lint-types": "eslint \"types/**/*.ts\" && tsc -p types/tests/"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "12.21.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"glob": "^7.1.6", "karma": "^6.2.0", "yargs": "^16.2.0", "eslint": "^7.22.0", "moment": "^2.29.1", "rollup": "^2.41.4", "jasmine": "^3.6.4", "typedoc": "^0.20.32", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "pixelmatch": "^5.2.1", "typescript": "^4.1.5", "concurrently": "^6.0.0", "jasmine-core": "^3.6.0", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "eslint-plugin-es": "^4.1.0", "markdownlint-cli2": "0.0.14", "rollup-plugin-dts": "^3.0.1", "chartjs-test-utils": "^0.2.2", "eslint-plugin-html": "^6.1.2", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^4.0.2", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "chartjs-adapter-moment": "^0.1.2", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-istanbul": "^3.0.0", "@rollup/plugin-commonjs": "^17.1.0", "@typescript-eslint/parser": "^4.18.0", "karma-rollup-preprocessor": "^7.0.7", "@rollup/plugin-node-resolve": "^11.2.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@typescript-eslint/eslint-plugin": "^4.18.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.0.0-rc.3_1616531028408_0.709013980978044", "host": "s3://npm-registry-packages"}}, "3.0.0-rc.4": {"name": "chart.js", "version": "3.0.0-rc.4", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.0.0-rc.4", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "9864b14a3590720e74f6bd4e565c22826046bd8a", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.0.0-rc.4.tgz", "fileCount": 40, "integrity": "sha512-xbXGlUHkFm1W1ooHG8ELr28CtRQgl05JeH0W15O0PypoqMAEPPVY6Dy80I4tLnf3uPrw552PyLKMhHEGg9B3pw==", "signatures": [{"sig": "MEYCIQCXh3Ptf9TIR0j6x7tFj8lHUYGbZMrjh+XWjsiyySUDlAIhANuPBn9RQ1D9IjWyPyki6le9dO2ghiNu+oWGeKudESwQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1055345, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXOOrCRA9TVsSAnZWagAAoqMP/RtsREu4XMjSBL34PQ+3\n05KVePnSJdtrKEpIUBYKVtnFpK8S3yj7Pu4NyUkE9ShcHCMnYyjuD5ySjQ6Y\nNjphf0kboxqr2TN9nd38N+puY65+s5eiQA7s08xnAXOjmxy/Bmkywyx6xS4G\nbAJvi9qMLEQP+wrU3RKUA58LXH8zYUut8TidYuusRuJpqTP8whjUQmD2wD3n\nGXy76r+iCgOtfxPQu1j6Hhsy9JCh8VsH+z7PVhHz9sj23Ez08Wr4k64SyFI7\nYeNgA8sw129QbgtBP0owxhOXeHLz4aVaWLpqBTZ0pezzYI5XZz4/S0ljuQn2\nkkkCZK44ZsHtOFjK1jxywi8n42tdWPZ5IhEzg1sX4q9KxPTD2dcQXqlD0WFw\n3zQGLDJuly+/cG3wrAKwZph+q16PF25lzSbLrY8uc0W85jBYReRTcZdip1XF\nurqPpt6dL/ptlD6qtFQqYocb1aAzfzHTCJvlhweWncRpc/LmJUsjk7cYe5nl\ntQVBwj1Bzlo1XQMWqzc82vWByWoLVJQXQhEZzfrjGkhYWeSB6Y4Zv6jSpGra\n/RpuGohGW3mPX/2cpzxQRuQ0y0rhSpgqlvUa+gHfiXQiv9ptca3rmBgRbZtJ\nAjwtuhy7/DwAv10JXCFJPkXZKyx+6HSwg1S1jn0lUHzhZE47BTS9i5M9kjB5\nMIT/\r\n=aScU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "types/chart.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "499a71d4eb660f4b728f6d462dcd6c36db282057", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "cd docs && npm install && npm run build", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "dev:ff": "karma start --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"samples/**/*.html\" \"samples/**/*.js\" \"src/**/*.js\" \"test/**/*.js\"", "lint-md": "markdownlint-cli2 \"**/*.md\" \"**/*.mdx\" \"#**/node_modules\"", "typedoc": "npx typedoc", "lint-tsc": "tsc", "autobuild": "rollup -c -w", "lint-types": "eslint \"types/**/*.ts\" && tsc -p types/tests/"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "12.21.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"glob": "^7.1.6", "karma": "^6.2.0", "yargs": "^16.2.0", "eslint": "^7.22.0", "moment": "^2.29.1", "rollup": "^2.41.4", "jasmine": "^3.6.4", "typedoc": "^0.20.32", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "pixelmatch": "^5.2.1", "typescript": "^4.1.5", "concurrently": "^6.0.0", "jasmine-core": "^3.6.0", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "eslint-plugin-es": "^4.1.0", "markdownlint-cli2": "0.0.14", "chartjs-test-utils": "^0.2.2", "eslint-plugin-html": "^6.1.2", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^4.0.2", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "chartjs-adapter-moment": "^0.1.2", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-istanbul": "^3.0.0", "@rollup/plugin-commonjs": "^17.1.0", "@typescript-eslint/parser": "^4.18.0", "karma-rollup-preprocessor": "^7.0.7", "@rollup/plugin-node-resolve": "^11.2.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@typescript-eslint/eslint-plugin": "^4.18.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.0.0-rc.4_1616700331242_0.014115757598364365", "host": "s3://npm-registry-packages"}}, "3.0.0-rc.5": {"name": "chart.js", "version": "3.0.0-rc.5", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.0.0-rc.5", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "41dd15117f4638916f8f72f23c6b10389aafeb44", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.0.0-rc.5.tgz", "fileCount": 40, "integrity": "sha512-qQ77IwyiZchVMKZQwXjrXhLuopbqUWGVzywWS2w6heS0f/blebnTADA2RfR5WntcSp31GkuMtJnpHYW//93Mog==", "signatures": [{"sig": "MEQCICeLDydikFd8yOJ5TI3XF9FvenGOUkNjiJEZrMJGVocXAiB/RQymJ+KJ60wL50oPyeBhC7e8G4HJ9IFK7/kLtR7RPQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1055345, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXQsQCRA9TVsSAnZWagAAvTAP/1FFzB5gM04ValaCQuRf\niF8Fu6LHUR3sBjF3Gc/LSiauUp7d3tBKxYUtsnPrNe30FLMzvgkFRutjNjXr\n1hGJpdE2uz7NhdxCB8YKA2DwlGP3+8vZxSzSw2Q2tmjX2e7GuVMuTl+nBjRa\nrGh6qbBzRC6z82yGoHI2YY70GuEcwxZ+A6aV2kA0d6bQCok0KqVSfSz2KfGo\nWu4QC/w7BWebpl8wvEBwIAThT/QdAYbZSvfyfUD3xpVD/opM536kIlF1Rw8h\nT8vomGts4SpfM1KVG+P42iNO1WN6MxwFGWAWBgvEoqW5wlfCYdwkr9dDeXQ0\nR5wE5MGfjJfkoo1qtr76dTTIxaNUFnn6AUgzmEI8d3qAUC0T9/oqFVx0b+XA\n2oBatoTuQS34idtNTNFmbZN4CWbOnwVWz9jAVvY+95LzObyfchbF5u53Cgq9\nBF7fbClJqI2UFadJGfRpNDKBcpBIXshzEYuof9NFDugDeoewo2BMzPV1O5q9\nb6FfUCgHrndaz2NkIDqD4x89Rt+9gDE5PLQLjnPRDL7pA0iFJdQl5bAKm196\nSiC5T8ewi6xz8JhKuT0xyrdIUgv3N5GRlcYC+5UCEHnBNR4kCzsDXVTIlMbm\nJkeGx3nqyqCKHaSC4gtXCJFmEfQKmHc+E2oEjvH8uclK9MOjkhfZv2Ueoi/C\neE3B\r\n=Qf4l\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "types/index.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "04d67aaf8cddd8c6ebe4caf1e0b1a3ff41dd7f5f", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "cd docs && npm install && npm run build", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "dev:ff": "karma start --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"samples/**/*.html\" \"samples/**/*.js\" \"src/**/*.js\" \"test/**/*.js\"", "lint-md": "markdownlint-cli2 \"**/*.md\" \"**/*.mdx\" \"#**/node_modules\"", "typedoc": "npx typedoc", "lint-tsc": "tsc", "autobuild": "rollup -c -w", "lint-types": "eslint \"types/**/*.ts\" && tsc -p types/tests/"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "12.21.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"glob": "^7.1.6", "karma": "^6.2.0", "yargs": "^16.2.0", "eslint": "^7.22.0", "moment": "^2.29.1", "rollup": "^2.41.4", "jasmine": "^3.6.4", "typedoc": "^0.20.32", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "pixelmatch": "^5.2.1", "typescript": "^4.1.5", "concurrently": "^6.0.0", "jasmine-core": "^3.6.0", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "eslint-plugin-es": "^4.1.0", "markdownlint-cli2": "0.0.14", "chartjs-test-utils": "^0.2.2", "eslint-plugin-html": "^6.1.2", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^4.0.2", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "chartjs-adapter-moment": "^0.1.2", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-istanbul": "^3.0.0", "@rollup/plugin-commonjs": "^17.1.0", "@typescript-eslint/parser": "^4.18.0", "karma-rollup-preprocessor": "^7.0.7", "@rollup/plugin-node-resolve": "^11.2.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@typescript-eslint/eslint-plugin": "^4.18.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.0.0-rc.5_1616710415796_0.9840803503782021", "host": "s3://npm-registry-packages"}}, "3.0.0-rc.6": {"name": "chart.js", "version": "3.0.0-rc.6", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.0.0-rc.6", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "c4f4b6fac5166ec195a18b5d56a2a29b1368b2c2", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.0.0-rc.6.tgz", "fileCount": 40, "integrity": "sha512-wukLA6MG0uA+EOuMxo5E1ClXyNSJAbzACqGRKDMeggpMSLYvL3IEgXiwWKv/zmAtx2mhD4AEFlhfmzm2i4LVGQ==", "signatures": [{"sig": "MEYCIQDdvwjlxy6hM26e0R9ziZUWVmgYxAWnXA3PDAvsDdqxGAIhAOEq7oDMepWTsa0+b/tUtg4NhY06amcKBl5Ppy5Rp11K", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1055878, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgX6bVCRA9TVsSAnZWagAAC6wQAI8pBDCjYVVlSxWKBXhs\nxwIEI4r2k6ECqgB55StjrTrEPePaaSM9S6Rj+2rI0NlJCL3/M/7D6k1uAIrG\nOYxNIg1ycm01Zk4Ak+5B8AZUDCrvqqj968GDuYf9EFZqCuHCj69H8NSMMKWi\nJSMpP9GZEAPddA38cmFOZ9iNxlG52LXmUlcn8L/XF5NC39uDer8mjjTQ2DKx\n7228TLiwiL3Nu0nJr6wN8cep8bPWy1HUAOVF6R1ttaG6QYgc6TJktsj6Q2DV\nom3Mt42Tu3apGWSeM5uOx2BSR4y45JnBcyBKzqChxaHc6kW5kazLFaFGsx2C\nOAMshrQJDVW5Spvky1+C0KePbLReq6gQF5Hb6B6d2gxpcPFKWQhuJZbKPd6/\nDYcSdDNV0RSr+nbBM5Ezjg7FLjGRWpjzad3snMDBYV764YhBRFDnl6k1Z0/I\nxDlg8IV4NXi3F161rPjYaFH8MUetN7/7hqM9U3GnpgzL84uphU2nvmVuqxo9\ns/5nsTMyzQxxcIhnDarjCf+s7fs6sXcjH0FO2E2CpoCuiVKxBVGqjnz/CA66\nxP/RaBFVP7QUhFygiA1g4arap/KO0rbJD57qRfcyVZlsw9YQJisx0WPHvZKf\nXhtLoK0WT2NKzzLZjYZEFsuLO4yIJpEofdK2oVY0xfzAyEbcEtyQSx6ZsWXB\nWAXf\r\n=0lh7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "types/index.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "c76ee82461b145879358b074bce537c125ecf68f", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "cd docs && npm install && npm run build", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "dev:ff": "karma start --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"samples/**/*.html\" \"samples/**/*.js\" \"src/**/*.js\" \"test/**/*.js\"", "lint-md": "markdownlint-cli2 \"**/*.md\" \"**/*.mdx\" \"#**/node_modules\"", "typedoc": "npx typedoc", "lint-tsc": "tsc", "autobuild": "rollup -c -w", "lint-types": "eslint \"types/**/*.ts\" && tsc -p types/tests/"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "12.21.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"glob": "^7.1.6", "karma": "^6.2.0", "yargs": "^16.2.0", "eslint": "^7.22.0", "moment": "^2.29.1", "rollup": "^2.41.4", "jasmine": "^3.6.4", "typedoc": "^0.20.32", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "pixelmatch": "^5.2.1", "typescript": "^4.1.5", "concurrently": "^6.0.0", "jasmine-core": "^3.6.0", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "eslint-plugin-es": "^4.1.0", "markdownlint-cli2": "0.0.14", "chartjs-test-utils": "^0.2.2", "eslint-plugin-html": "^6.1.2", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^4.0.2", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "chartjs-adapter-moment": "^0.1.2", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-istanbul": "^3.0.0", "@rollup/plugin-commonjs": "^17.1.0", "@typescript-eslint/parser": "^4.18.0", "karma-rollup-preprocessor": "^7.0.7", "@rollup/plugin-node-resolve": "^11.2.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@typescript-eslint/eslint-plugin": "^4.18.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.0.0-rc.6_1616881363844_0.7872433919906014", "host": "s3://npm-registry-packages"}}, "3.0.0-rc.7": {"name": "chart.js", "version": "3.0.0-rc.7", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.0.0-rc.7", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "4a14c7a9d3abeb280dc994282f79f52e56cc8404", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.0.0-rc.7.tgz", "fileCount": 40, "integrity": "sha512-k0JlkgLG8ZELkSglhw/7tb9kUMPlVt6TS4tAeAD4pjOzwykCUjgcnJhbxsDZ1K3KSbsUDWsctUmK7ubvMLaITA==", "signatures": [{"sig": "MEQCIDVVeryczI1Zw3l5BuKcaRtO9FkTV42+lfp1v872AhhlAiBQj5zQs0AG+ZLd57sQdLYjPEOYdfQl0E9kukivrbxFPA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1060005, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZx/sCRA9TVsSAnZWagAA5ZsP/1yn9NUZtQak1co1VqaL\n4CzvY6rIq22uLGW71XJujS1iBdBU2jCDuHYwGXOhk25vzGBe0TBuTPhf3BGx\nbIK3xgdObyan3ZGF/M0QohkNcIUfar+pGiZ0C+SSbL+lJIVdjcUlRkJifkp5\n1NjwVjn71HjEK3caBKY0CVTZAjH7EnRYGGKWHX6F/lo/lIVPw/RqnafOHLkI\nnMtbDQYCpQLNf+FPpalsbAvH9M5IGSwEl0Kd2VHuor90daOSwSf0CtSrz8Tv\nk7KHpOGLO72ZqtvhZBlZKCjJdTWJBOSKvZFqiJKUcu9RPVijZP04fdidf3tm\nmAvlqaywNyEMI6Jw+MqlU27pIbr04o31hc/5TvFk5twcrxbd/uskRUXYZwHi\nKEZWz9fslwtkC6iUxnKe6LsZcN89Ih0IWkSKpAW7KuXYzExllR9CrZrDCRqM\nNRpqTTrfo8kzAxhVQYyW6kPzosfisMCmCepvHM2+n3oFXX6SKP8Wq7wVbA5J\nilVZ6k8QHkutRdWysoeIl8bdceU7DcA13QxyTX3rXoNYy0MDiqiCXCmUdHD7\nizuqQ9vOUrtpUN5QsJLCHl1hb/8fT40GE7kYaZOIGlqLzWhoOXk9y7DEnrfH\ne1DFTDREgJe5yXmNFhdkFWlg7Hr59WL0XL+nN12Rz7AnJGdU/fuRBky/PqFT\nsuoO\r\n=b1NT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "types/index.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "eef5c1ebf3646637eca87dee93e6a30ae9c15959", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "npm run build && vuepress build docs --no-cache", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "dev:ff": "karma start --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.js\" \"test/**/*.js\" \"docs/**/*.js\"", "lint-md": "eslint \"**/*.md\"", "docs:dev": "npm run build && vuepress dev docs --no-cache", "lint-tsc": "tsc", "autobuild": "rollup -c -w", "lint-types": "eslint \"types/**/*.ts\" && tsc -p types/tests/"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "14.16.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"glob": "^7.1.6", "karma": "^6.2.0", "luxon": "^1.26.0", "yargs": "^16.2.0", "eslint": "^7.22.0", "moment": "^2.29.1", "rollup": "^2.41.4", "jasmine": "^3.6.4", "typedoc": "^0.20.32", "vuepress": "^1.8.2", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "pixelmatch": "^5.2.1", "typescript": "^4.1.5", "concurrently": "^6.0.0", "jasmine-core": "^3.6.0", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.2.2", "eslint-plugin-html": "^6.1.2", "vue-tabs-component": "^1.5.0", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "markdown-it-include": "^2.0.0", "rollup-plugin-terser": "^7.0.2", "vuepress-plugin-tabs": "^0.3.0", "@rollup/plugin-inject": "^4.0.2", "chartjs-adapter-luxon": "^1.0.0-beta.2", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "chartjs-adapter-moment": "^0.1.2", "eslint-plugin-markdown": "^2.0.0", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-istanbul": "^3.0.0", "vuepress-theme-chartjs": "^0.2.0", "@rollup/plugin-commonjs": "^17.1.0", "typedoc-plugin-markdown": "^3.6.0", "vuepress-plugin-typedoc": "^0.6.0", "vuepress-plugin-redirect": "^1.2.5", "@typescript-eslint/parser": "^4.18.0", "karma-rollup-preprocessor": "^7.0.7", "@rollup/plugin-node-resolve": "^11.2.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@typescript-eslint/eslint-plugin": "^4.18.0", "@vuepress/plugin-google-analytics": "1.8.2"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.0.0-rc.7_1617371116114_0.6268383993741786", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "chart.js", "version": "3.0.0", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.0.0", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "9fcc32c793227b83420224b50bbe3d5577160c61", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.0.0.tgz", "fileCount": 40, "integrity": "sha512-TfbChPDU3iPvFGvkrzUPYpb/TxLoq9V3gjkL9odRkVqtEfp0saBHCHtCX4iKcQgRdb5xBSZUMKLUAf0PWabTvg==", "signatures": [{"sig": "MEUCIQDnPCuiRN4S3OpRwg29CaAsKmo0AOc7De4eGXeolcGr0gIgNAx+apEPZ0W49GJnf04s+Unrf3wLMmPAlea4x7JN2HY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1059296, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZ2wiCRA9TVsSAnZWagAA4QcP/AyTMHfxVui9RapVJEQX\nzB9Ltq4NjGESVy8mxN/YYO0EVstdlPPPJpf4fMj76wJ333EEITJjrM0/zKup\nil34Xw+R764eAANvODa2UZarZKngzYtBF1Vs9Pr8+lDreV8j5S151emMBvCx\nNDcLCQNAf9zGAIWbRyFzd+kJ5AECEX57v3TXE0K2B/CLf5YyMha/kIb2XweM\nfTw5JL4ESdkGMaUahIq1clxVw8Lz21NUw5fK47USDOcVuxHzFQyYL9F7dy4/\naXZ+lcfJ49KD36xCORd2OrHVRMLj3dmHqlzrdu0i9KnnMFI6eSELVd6hPy/x\nuKcwkIQQG/pPm4vJMsXBhAIi5izKk5YXAKvBx7Hzu67EhYfo0bL5ZCJhNBl1\nrs3sZxzr3b0rKQiCLt8DYuBHd8WqybFViI25EKDfwHddrCm59EhfEhcV818V\nnB9hxKMwhA9RRB1tnD1TiDEGKCcTIAafQ64l/hBgaj7yWuLIsbv6UT9aXeAQ\n+77YQd6TrlIRPPp82UuRkxiF+lfwiR2HLO0z5y75WYGDcqpNwHZG+l9+5/R1\n1jxM1+8Dcofz6B8p/kpF02CsptkhEnIlsI0sm3JRIFuAaAcSYvc3wSm3VVBY\ndJZzpsdSC3xtLCJOvAqSexY/0he/3ztFeL/l0pGvet4jwFsp/bGKiNGE2R74\n2tWV\r\n=1gBy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "types/index.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "8780e15c73c96db58034466e47174eb21b724857", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "npm run build && vuepress build docs --no-cache", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "dev:ff": "karma start --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.js\" \"test/**/*.js\" \"docs/**/*.js\"", "lint-md": "eslint \"**/*.md\"", "docs:dev": "npm run build && vuepress dev docs --no-cache", "lint-tsc": "tsc", "autobuild": "rollup -c -w", "lint-types": "eslint \"types/**/*.ts\" && tsc -p types/tests/"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "14.16.0", "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "karma": "^6.2.0", "luxon": "^1.26.0", "yargs": "^16.2.0", "eslint": "^7.22.0", "moment": "^2.29.1", "rollup": "^2.41.4", "jasmine": "^3.6.4", "typedoc": "^0.20.32", "vuepress": "^1.8.2", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "pixelmatch": "^5.2.1", "typescript": "^4.1.5", "concurrently": "^6.0.0", "jasmine-core": "^3.6.0", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.2.2", "eslint-plugin-html": "^6.1.2", "vue-tabs-component": "^1.5.0", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "markdown-it-include": "^2.0.0", "rollup-plugin-terser": "^7.0.2", "vuepress-plugin-tabs": "^0.3.0", "@rollup/plugin-inject": "^4.0.2", "chartjs-adapter-luxon": "^1.0.0-beta.2", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "chartjs-adapter-moment": "^0.1.2", "eslint-plugin-markdown": "^2.0.0", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-istanbul": "^3.0.0", "vuepress-theme-chartjs": "^0.2.0", "@rollup/plugin-commonjs": "^17.1.0", "typedoc-plugin-markdown": "^3.6.0", "vuepress-plugin-typedoc": "^0.6.0", "vuepress-plugin-redirect": "^1.2.5", "@typescript-eslint/parser": "^4.18.0", "karma-rollup-preprocessor": "^7.0.7", "vuepress-plugin-flexsearch": "^0.1.0", "@rollup/plugin-node-resolve": "^11.2.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@vuepress/plugin-html-redirect": "^0.1.2", "@typescript-eslint/eslint-plugin": "^4.18.0", "@vuepress/plugin-google-analytics": "1.8.2"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.0.0_1617390626243_0.9908612191677102", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "chart.js", "version": "3.0.1", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.0.1", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "5b2a405ba47b30782bc59289f96d206865be2f6a", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.0.1.tgz", "fileCount": 40, "integrity": "sha512-GR62zMZX8lRCr4Nq60aQ84QpsUYrpm9NWECR+Qu2pouzXqPI4zWHUF2MG/azXnBGn85cHR6tkv7cgY3lX5u9SA==", "signatures": [{"sig": "MEUCIQC/JdSHVsJdbOL8F06PqivvbaPMAxrnvUX4KACcz1QwmAIgLmC91kVaV2hStygXAezFI3AlILSQ2HiOv5Ea4GcDtUE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1059418, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZ4AeCRA9TVsSAnZWagAAkiQQAISSHBPOardtONRidwsm\nyQmAtV5vm1sZDhDTNbjlPQk3chRNfglcQD6BBsl0mG6fcgM2ul0gHkVODCAg\nMsR6I0t1eBtdz1o1yS2XBufs1hAXPfywuYgXk2o9VgZqKIc8NI0qEqa9juBj\nwYXzSTIR8amPHh7uTvawgf3DUAMuAECnB319xT9zICjUdMIbS1r/duwf16pV\nG1I7CXtnj6ZliVP3MgY5/xyp8ycaIcl3n+uv+fcy5k1donXh0JJUbo4tMwQH\nBYOfvgsOnxLHYEhpwI13zsMfeb6eQKOe4Vr4JSld7aiJIHWgl0ZEHyO5OCz1\nnJ4Z25duXXU63OOuU1ZENJpdFB+7uIB8/FASCxdAjIMpvIKuwUH+OSj1vV45\nJ81wJbnicXtwiaa6u1JPnoYx4SuQZQVKlM3zItKa6OhvMh6qZceOvmw2aWva\nz0S6MmQg0iln9RbV31aYdJwAVN+jr73hlQ14nUnSAO2B+3iP512hjJJDkZAE\nDiXHvMCTXVKVPC0kgZw2j4SY6Afa/aEp4uWPJ3oZ1fAB/1qFF0ROpihCUbBs\nxDAVsdzr3DnniiCvZSkSRpVIiid4RihUlhfFjGo0z4HNNXLzXPJQdZqj9PtK\nYr/iSew9znY2BiSW2954N74fOPmD0PKcKgLkB0WFDNBor6rLasJvf8Wyq5E7\n0WVK\r\n=4nSb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "types/index.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "98e890cc7b29b582de4320ff56f38e04807f7409", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "npm run build && vuepress build docs --no-cache", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "dev:ff": "karma start --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.js\" \"test/**/*.js\" \"docs/**/*.js\"", "lint-md": "eslint \"**/*.md\"", "docs:dev": "npm run build && vuepress dev docs --no-cache", "lint-tsc": "tsc", "autobuild": "rollup -c -w", "lint-types": "eslint \"types/**/*.ts\" && tsc -p types/tests/"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "14.16.0", "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "karma": "^6.2.0", "luxon": "^1.26.0", "yargs": "^16.2.0", "eslint": "^7.22.0", "moment": "^2.29.1", "rollup": "^2.41.4", "jasmine": "^3.6.4", "typedoc": "^0.20.32", "vuepress": "^1.8.2", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "pixelmatch": "^5.2.1", "typescript": "^4.1.5", "concurrently": "^6.0.0", "jasmine-core": "^3.6.0", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.2.2", "eslint-plugin-html": "^6.1.2", "vue-tabs-component": "^1.5.0", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "markdown-it-include": "^2.0.0", "rollup-plugin-terser": "^7.0.2", "vuepress-plugin-tabs": "^0.3.0", "@rollup/plugin-inject": "^4.0.2", "chartjs-adapter-luxon": "^1.0.0-beta.2", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "chartjs-adapter-moment": "^0.1.2", "eslint-plugin-markdown": "^2.0.0", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-istanbul": "^3.0.0", "vuepress-theme-chartjs": "^0.2.0", "@rollup/plugin-commonjs": "^17.1.0", "typedoc-plugin-markdown": "^3.6.0", "vuepress-plugin-typedoc": "^0.6.0", "vuepress-plugin-redirect": "^1.2.5", "@typescript-eslint/parser": "^4.18.0", "karma-rollup-preprocessor": "^7.0.7", "vuepress-plugin-flexsearch": "^0.1.0", "@rollup/plugin-node-resolve": "^11.2.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@vuepress/plugin-html-redirect": "^0.1.2", "@typescript-eslint/eslint-plugin": "^4.18.0", "@vuepress/plugin-google-analytics": "1.8.2"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.0.1_1617395741748_0.8404103603926119", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "chart.js", "version": "3.0.2", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.0.2", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "5893b0548714445b5190cbda9ac307357a56a0c7", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.0.2.tgz", "fileCount": 40, "integrity": "sha512-DR0GmFSlxcFJp/w//ZmbxSduAkH/AqwxoiZxK97KHnWZf6gvsKWS3160WvNMMHYvzW9OXqGWjPjVh1Qu+xDabg==", "signatures": [{"sig": "MEUCIQD8MRhGoPaJF3GprAMqbgVcz5We53BFq7OY8eRkyGkl9QIgVnPubjpkDjuhVdXXclu46vhL7TBdW3Pdez3vnEeL7q8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1059772, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgacFaCRA9TVsSAnZWagAA1WIP/RKTPepm/PEQIO+a/yqk\njwmoWCWOWPWkzCdBRfgLhECy731hvZ1sOCWyZE2RHaz3fhLttw7DeEXVfGI/\nYfm/HavCazJ/WDvS2Q49Zhc8T1/quNMarQOk6+9niNWI2cGNmPyF2u5cu2dx\ngmxvt3BgzGo5pw2XdbEYFVpSGFfDAh27UtfT3NWQ/jnrMPIQ5V+slBsAs9eN\ndabxayQJsaJ7gs452G/cZVx5gFkMJFTYhvW6tWil0JJ4ace76Ti5tDYtQi4B\nZZnP/2YQDazl2qqQ2SwWB/WH72Skwh6+2y+F+9lQZdmMLQpqbfQpSSKmGJbV\ndljRHkiHZ4uQXvP7fsvUuC/uLRoAt7mX0NphMBLkUqxY7H/zmaUClsiXamMV\no221qn7jbqqSRoZVNu8sgYyeNvG6KWNS/kYY5HshhJSsADS4SSPA5aDvAxhY\nssC2gf8a5Dh4lltItf3Fr2wD4w8w1YfKEIkLo8cbiACSNSITA9Ose+pscP66\nu7pMsPro3TMTnB/BcsS76+FGqVfDRFIXKAsdj/UN9ArNKC3nArOYJmJ4JqZF\nwYBwQIX+6go4wIMeY1fI19dT1C6gMiRXpRlwxlRCkH4sbVixM4VVbBo5UMHk\nYSD2GfkOenjgBeXHKQa4P52Houmad2D1gYi8gkgdYILAhZjc5mwUHGhgm8sa\n0Y70\r\n=7J3A\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "types/index.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "954fc3ccf54d4e8cfa7cc091e8d8c083020ed477", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "npm run build && vuepress build docs --no-cache", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "dev:ff": "karma start --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.js\" \"test/**/*.js\" \"docs/**/*.js\"", "lint-md": "eslint \"**/*.md\"", "docs:dev": "npm run build && vuepress dev docs --no-cache", "lint-tsc": "tsc", "autobuild": "rollup -c -w", "lint-types": "eslint \"types/**/*.ts\" && tsc -p types/tests/"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "14.16.0", "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "karma": "^6.2.0", "luxon": "^1.26.0", "yargs": "^16.2.0", "eslint": "^7.22.0", "moment": "^2.29.1", "rollup": "^2.41.4", "jasmine": "^3.6.4", "typedoc": "^0.20.32", "vuepress": "^1.8.2", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "pixelmatch": "^5.2.1", "typescript": "^4.1.5", "concurrently": "^6.0.0", "jasmine-core": "^3.6.0", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.2.2", "eslint-plugin-html": "^6.1.2", "vue-tabs-component": "^1.5.0", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "markdown-it-include": "^2.0.0", "rollup-plugin-terser": "^7.0.2", "vuepress-plugin-tabs": "^0.3.0", "@rollup/plugin-inject": "^4.0.2", "chartjs-adapter-luxon": "^1.0.0-beta.2", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "chartjs-adapter-moment": "^0.1.2", "eslint-plugin-markdown": "^2.0.0", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-istanbul": "^3.0.0", "vuepress-theme-chartjs": "^0.2.0", "@rollup/plugin-commonjs": "^17.1.0", "typedoc-plugin-markdown": "^3.6.0", "vuepress-plugin-typedoc": "^0.6.0", "vuepress-plugin-redirect": "^1.2.5", "@typescript-eslint/parser": "^4.18.0", "karma-rollup-preprocessor": "^7.0.7", "vuepress-plugin-flexsearch": "^0.1.0", "@rollup/plugin-node-resolve": "^11.2.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@vuepress/plugin-html-redirect": "^0.1.2", "@typescript-eslint/eslint-plugin": "^4.18.0", "@vuepress/plugin-google-analytics": "1.8.2"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.0.2_1617543513537_0.5209615265213385", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "chart.js", "version": "3.1.0", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.1.0", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "b99cfe712fa0059134a4ad3a3515135fbb20bcea", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.1.0.tgz", "fileCount": 40, "integrity": "sha512-b<PERSON><PERSON>i2VbC4fqZXlLbK7LKVvmG9crjoG9anfp96utZLyIGPuCx+YN+5/HDXy98QGt3lf74T8gKUPISUZL222tDJQ==", "signatures": [{"sig": "MEQCIHHwwEJO3Nm2hb9ekSADxwYQo4X4SoA4VSuJ1d8667SMAiBbyzag4uH5UdnCG5/8xeJz94/KSw3v/21ITOLs6wi4lg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1081428, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgcgj5CRA9TVsSAnZWagAADs4P/2jGRWHJGkoeILiRC31A\nFFQKExpgzsUVLmLDDhqxsxkW92cneXgWQN4zGDukWFgF7wGVcKfZ4XT7Xh/J\nskhErBMmhkVWn6D4q4S7xWekL7wa7oAWg8W9CnsnwMWp7XR+oWsG5OSwRKGb\n/M3Nsxa1TiYT6zk9N+24oQliveXfP7CLLp8y6ut/rK8Jwaz/yrhmqDNcWT1G\nuCiHhUw+BXGCds7PphJLebrq8fQtxMy7sqodZ0WuPWb3ENU21OCZw1jo+eeu\nhgtv4uShcoUd4ynimt/k/r7dusfJ+U6UTgqYKuWji4bpC46lcMvhlASgUUQo\nCnkEGi1H6wPD4qMTRlc/Bv6R1ZdatDJlxf3sutAGeWqrCMtyFwPoutczDTEh\nOLf9cb3cpvYAFkePQ4+Ty8ZrUl85Gd0cMpPcAhHEmye+9jAU7GCVtSs2l2UY\nVK0QzG+ptJgbrpEIHv2eEsNx2XeznI/dU5Qft/AVNi1QpF6n1143yDbJd5FX\n5GjMsWYvqw0291ehK72emH23ElBp6QcgSILIDgmU5m0vCVC/KOltybeK+GXg\nizpa5JFuo1i6auj/iImXhP3Skw+i/VKm00Bb+yavimt9u8TZAs4ljQNl7s0H\nUUHs5D2Jsg6OT+9REadN6hEsy0lb4eues9dJQOvK9mzwbI93Jso03IwdbpHT\n2EgL\r\n=UuWg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "types/index.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "981eff55538aaaa822c5343ba2a324d9b54003fb", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "npm run build && vuepress build docs --no-cache", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "dev:ff": "karma start --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.js\" \"test/**/*.js\" \"docs/**/*.js\"", "lint-md": "eslint \"**/*.md\"", "docs:dev": "npm run build && vuepress dev docs --no-cache", "lint-tsc": "tsc", "autobuild": "rollup -c -w", "lint-types": "eslint \"types/**/*.ts\" && tsc -p types/tests/"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "14.16.0", "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "karma": "^6.3.2", "luxon": "^1.26.0", "yargs": "^16.2.0", "eslint": "^7.23.0", "moment": "^2.29.1", "rollup": "^2.44.0", "jasmine": "^3.7.0", "typedoc": "^0.20.35", "vuepress": "^1.8.2", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "pixelmatch": "^5.2.1", "typescript": "~4.1.0", "concurrently": "^6.0.1", "jasmine-core": "^3.7.1", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.2.2", "eslint-plugin-html": "^6.1.2", "vue-tabs-component": "^1.5.0", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "markdown-it-include": "^2.0.0", "rollup-plugin-terser": "^7.0.2", "vuepress-plugin-tabs": "^0.3.0", "@rollup/plugin-inject": "^4.0.2", "chartjs-adapter-luxon": "^1.0.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^2.0.1", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-istanbul": "^3.0.0", "vuepress-theme-chartjs": "^0.2.0", "@rollup/plugin-commonjs": "^18.0.0", "typedoc-plugin-markdown": "^3.6.1", "vuepress-plugin-typedoc": "^0.6.0", "vuepress-plugin-redirect": "^1.2.5", "@typescript-eslint/parser": "^4.21.0", "karma-rollup-preprocessor": "^7.0.7", "vuepress-plugin-flexsearch": "^0.1.0", "@rollup/plugin-node-resolve": "^11.2.1", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@vuepress/plugin-html-redirect": "^0.1.2", "@typescript-eslint/eslint-plugin": "^4.21.0", "@vuepress/plugin-google-analytics": "1.8.2", "@simonbrunel/vuepress-plugin-versions": "^0.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.1.0_1618086136841_0.22612020335784533", "host": "s3://npm-registry-packages"}}, "3.1.1": {"name": "chart.js", "version": "3.1.1", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.1.1", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "2cdbda7fccea532313332fe822f0cae268f24cf3", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.1.1.tgz", "fileCount": 40, "integrity": "sha512-ghNJersc9VD9MECwa5bL8gqvCkndW6RSCicdEHL9lIriNtXwKawlSmwo+u6KNXLYT2+f24GdFPBoynKW3ke4MQ==", "signatures": [{"sig": "MEUCIQCVdVos/fePuaUw80RDgq2PBCSTnpjt4t1tH2sblHDKmgIgZJvOZF5FGeiHYd2TqneCEbyd0pIHQ9u1cx6mjC9K8Ig=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1083322, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJges7hCRA9TVsSAnZWagAABWkP+wd3+gLjyAw6fgH5jc0W\nONOQKHHhiW/Do2Dg3Z4eMuxcciIn/xh6gDILqs7FDmkhECyAWdHGXUPSvhV6\nw5c9xiSj2GVBn62WB4bgTRHsDyVNvmP3uJGNoAHlPNHvNEynRhOKbX0OFK3Y\ns69TjL4lZuby2pRf6Ju8Ykk68sXWDfHZrRmJW6rW0i2BuseL3k/HXMDJJugx\nrDr/984hTrZmz4ZCAlcWnPz7ZSVrZodYI/Wi4yxtTYxfmulvCCLNkRbyGLcL\nvU2kQ1NosxT5I6w38fEzN9T9ImdBDDoPifjg49HHiRrMCQZkc1Wq5E4Z8HgY\nZji32d0m0RKNBGv/uTvaBOEHCHNr7MK52leydgDEZRLvjs2PZLKWnZUBElMO\nt9MXm62aJ/sy+LRMGkyKnFW6yBmSpVOsFAzdBn7M4cgKmG1Tmw25aSMHN+QB\ncQwvomFqunfwyrHGAK1rIF5ay7dp/26Smzt0/t7zQHqRRfqqn95io+NgT+Zi\nWhoFVJOKMtymBn7NWHBb74icyNlvoxUajQJd9B/XcqXNmneWZBGw0bQSIJTA\nc4QswzzCvt0IWZJB01WDE6KPUQRMKZDHqDUxM6uKsBKlymfmBDuhcnm9nC7H\nFlWtFvT6EIhQDGwFsI6J3+pmjXnNQ9k3R6lyrZh3QwdUbDsW1bCDMLA3MPI4\nWJuF\r\n=5sa1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "types/index.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "c56cdedeceed3d60c35187dbcf64608f6b3ceb3f", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "npm run build && vuepress build docs --no-cache", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "dev:ff": "karma start --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.js\" \"test/**/*.js\" \"docs/**/*.js\"", "lint-md": "eslint \"**/*.md\"", "docs:dev": "npm run build && vuepress dev docs --no-cache", "lint-tsc": "tsc", "autobuild": "rollup -c -w", "lint-types": "eslint \"types/**/*.ts\" && tsc -p types/tests/"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "14.16.1", "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "karma": "^6.3.2", "luxon": "^1.26.0", "yargs": "^16.2.0", "eslint": "^7.23.0", "moment": "^2.29.1", "rollup": "^2.44.0", "jasmine": "^3.7.0", "typedoc": "^0.20.35", "vuepress": "^1.8.2", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "pixelmatch": "^5.2.1", "typescript": "~4.1.0", "concurrently": "^6.0.1", "jasmine-core": "^3.7.1", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.2.2", "eslint-plugin-html": "^6.1.2", "vue-tabs-component": "^1.5.0", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "markdown-it-include": "^2.0.0", "rollup-plugin-terser": "^7.0.2", "vuepress-plugin-tabs": "^0.3.0", "@rollup/plugin-inject": "^4.0.2", "chartjs-adapter-luxon": "^1.0.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^2.0.1", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-istanbul": "^3.0.0", "vuepress-theme-chartjs": "^0.2.0", "@rollup/plugin-commonjs": "^18.0.0", "typedoc-plugin-markdown": "^3.6.1", "vuepress-plugin-typedoc": "^0.6.0", "vuepress-plugin-redirect": "^1.2.5", "@typescript-eslint/parser": "^4.21.0", "karma-rollup-preprocessor": "^7.0.7", "vuepress-plugin-flexsearch": "^0.1.0", "@rollup/plugin-node-resolve": "^11.2.1", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@vuepress/plugin-html-redirect": "^0.1.2", "@typescript-eslint/eslint-plugin": "^4.21.0", "@vuepress/plugin-google-analytics": "1.8.2", "@simonbrunel/vuepress-plugin-versions": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.1.1_1618661089223_0.3245730429099889", "host": "s3://npm-registry-packages"}}, "3.2.0": {"name": "chart.js", "version": "3.2.0", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.2.0", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "3d0a4b62b6bee428f8547db6aa68c792b130c260", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.2.0.tgz", "fileCount": 40, "integrity": "sha512-Ml3R47TvOPW6gQ6T8mg/uPvyOASPpPVVF6xb7ZyHkek1c6kJIT5ScT559afXoDf6uwtpDR2BpCommkA5KT8ODg==", "signatures": [{"sig": "MEUCIFaXoAXs4NAqytZQ/d/SrR6WGn59ya+kN4X7my+NKcBlAiEA5fTA43fSIjdYSN+N8ySWOW9g5r1dTqn7EYl5+bGcTsc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1091647, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJghH44CRA9TVsSAnZWagAAIOgP/RkF2jyLW2+1t01dEb9K\nAyLNBUrWIPs8AzIJjET0iijNTvAmLj3jWT0iLhQytY/k2NnNSKmV0Da09sdQ\nCJbjc0sNEbnY2oimwIGLvCRIDVGP7yHJZpbYFRUGFJMKm5SNCQri4zyid25t\nbGlxx097pz2Fjc3kefFZZw2r54IBoIJsOGlzNENV2Qs1CI51sLSPZZr5LgZr\nXRf3i22OgCY+JYGai9ho0m/XeB7DCyQKSthmlJafngYF80qVQm2ThjV+/eZf\nFSiex28kUjl798FBWKMBxAGrXD/d9APVYKwFuH0isrBpRoJqN91x6duGTvH+\n1awnVvJBISeQHV8PTjLX93DUsn2vQLu6g4GG/Y+yMwaZ2RHOhgVJROd/AdZv\nYaWh9mQIL+0CQ/Biy+49FiVvlK9S2OanNtltaX5c+A7qClDYCKZ9yLE/H92o\nPorWcqX3KeMtQSZy3yqF+C03MCdh12z+IDy5+N9BukEkE3HS7b0wjaQvxEuV\nv988LbJb9i3x8+sk3LbgyeiveUnkaeT6GUzClzHYH6TI0N3aN0lHH/c8SnVh\nXK6Paj7xaxRq+s66l1o6H5f6gumXFU1tbkTAilDwuUIZtKziIgpGkUVvAqtq\n3Ns1tEV37SelR/cZBR2dL/NiEG3UPsioUTS7ko5miybrl3lVBb1/ztGQ3l/K\ncD3U\r\n=959h\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "types/index.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "0f1d07a1cd4948cd8a417ae7438618430a82f287", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "npm run build && vuepress build docs --no-cache", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "dev:ff": "karma start --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.js\" \"test/**/*.js\" \"docs/**/*.js\"", "lint-md": "eslint \"**/*.md\"", "docs:dev": "npm run build && vuepress dev docs --no-cache", "lint-tsc": "tsc", "autobuild": "rollup -c -w", "lint-types": "eslint \"types/**/*.ts\" && tsc -p types/tests/"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "14.16.1", "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "karma": "^6.3.2", "luxon": "^1.26.0", "yargs": "^16.2.0", "eslint": "^7.23.0", "moment": "^2.29.1", "rollup": "^2.44.0", "jasmine": "^3.7.0", "typedoc": "^0.20.35", "vuepress": "^1.8.2", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "pixelmatch": "^5.2.1", "typescript": "~4.1.0", "concurrently": "^6.0.1", "jasmine-core": "^3.7.1", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.2.2", "eslint-plugin-html": "^6.1.2", "vue-tabs-component": "^1.5.0", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "markdown-it-include": "^2.0.0", "rollup-plugin-terser": "^7.0.2", "vuepress-plugin-tabs": "^0.3.0", "@rollup/plugin-inject": "^4.0.2", "chartjs-adapter-luxon": "^1.0.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^2.0.1", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-istanbul": "^3.0.0", "vuepress-theme-chartjs": "^0.2.0", "@rollup/plugin-commonjs": "^18.0.0", "typedoc-plugin-markdown": "^3.6.1", "vuepress-plugin-typedoc": "^0.6.0", "vuepress-plugin-redirect": "^1.2.5", "@typescript-eslint/parser": "^4.21.0", "karma-rollup-preprocessor": "^7.0.7", "vuepress-plugin-flexsearch": "^0.1.0", "@rollup/plugin-node-resolve": "^11.2.1", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@vuepress/plugin-html-redirect": "^0.1.2", "@typescript-eslint/eslint-plugin": "^4.21.0", "@vuepress/plugin-google-analytics": "1.8.2", "@simonbrunel/vuepress-plugin-versions": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.2.0_1619295800298_0.42009604325437366", "host": "s3://npm-registry-packages"}}, "3.2.1": {"name": "chart.js", "version": "3.2.1", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.2.1", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "1a17d6a88cef324ef711949e227eb51d6c4c26d3", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.2.1.tgz", "fileCount": 40, "integrity": "sha512-XsNDf3854RGZkLCt+5vWAXGAtUdKP2nhfikLGZqud6G4CvRE2ts64TIxTTfspOin2kEZvPgomE29E6oU02dYjQ==", "signatures": [{"sig": "MEUCIHIuAlvCj7ri6AkSUQ5hokppgeHBZYjYQ2t7QmQV83qZAiEA8obfwY3lTGeDFleXwmOhFpeZKdvtaTGV1vdtjgRnjZ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1093815, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgjZsnCRA9TVsSAnZWagAA7T4P/3iBY8Je06xk9u3aX0DD\nd5fohhukGNTtp6dULkdPWD0WNo5LjMEb6+VXn4JGS8y2hs3/YOz/YoasFY0U\nOMCQ5PZKKbRUgJvq3h3AzCfAUWE7aOj9ylHKl1sethqZGtJGBzr+wK4mMB9g\nIfGAXfQCXWorcWVHMBGyWNRAoHPAd3kMaqn5qYOKeHBk7Olo6wIRiRipkMX2\nxfctrPzQ27TpcSG0zpdXn40AoT6kiVzGQnhYhDWw9hoxJIuE6tf1wRSJaTNP\nmxQdaIXDpYCk3Pehrr/24wklpLMWV7QrZO+l8p2tAeUQTP7rdNCLAyqnf5Id\n8cDzVHd2c++VnNAmNN/MjZFq6TPgcQ+bfPdEXy9glqaafmTLa6/Gx6JDA2G0\nF1L+p/Cpqy9e/wjV55gK6qqLl7uaAGp3NhHJH9SX7jMQBZpNJ9wOAj2GoXcS\nKUa9eoDmyYoyQcH+ssrVn1qU1gae42Hlic5PMe/NheEP6BwWXFk29jlsebQc\ndjcyNfq4cVaqd+TxHzp/Dke6vd3PIhri/1r/KPWl3LbtsmcFW7rPIDr+sDj0\nGNKrr+1SsgUemCQFj3q/4ocljJ+YfMdEnpwNMe1Rllbb+wD6fHmC5omIdZZW\nk2/6RQm/8MwyEPlTtk5YU/GbGLmX3FKDpQYD8dWKGv/HeVQrpaXkj9uRrE+s\n1fjO\r\n=lIrZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "types/index.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "938865f67d35331e00b21a39825b607aa660fa14", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "npm run build && vuepress build docs --no-cache", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "dev:ff": "karma start --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.js\" \"test/**/*.js\" \"docs/**/*.js\"", "lint-md": "eslint \"**/*.md\"", "docs:dev": "npm run build && vuepress dev docs --no-cache", "lint-tsc": "tsc", "autobuild": "rollup -c -w", "lint-types": "eslint \"types/**/*.ts\" && tsc -p types/tests/"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "14.16.1", "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "karma": "^6.3.2", "luxon": "^1.26.0", "yargs": "^16.2.0", "eslint": "^7.23.0", "moment": "^2.29.1", "rollup": "^2.44.0", "jasmine": "^3.7.0", "typedoc": "^0.20.35", "vuepress": "^1.8.2", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "pixelmatch": "^5.2.1", "typescript": "~4.1.0", "concurrently": "^6.0.1", "jasmine-core": "^3.7.1", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.2.2", "eslint-plugin-html": "^6.1.2", "vue-tabs-component": "^1.5.0", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "markdown-it-include": "^2.0.0", "rollup-plugin-terser": "^7.0.2", "vuepress-plugin-tabs": "^0.3.0", "@rollup/plugin-inject": "^4.0.2", "chartjs-adapter-luxon": "^1.0.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^2.0.1", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-istanbul": "^3.0.0", "vuepress-theme-chartjs": "^0.2.0", "@rollup/plugin-commonjs": "^18.0.0", "typedoc-plugin-markdown": "^3.6.1", "vuepress-plugin-typedoc": "^0.6.0", "vuepress-plugin-redirect": "^1.2.5", "@typescript-eslint/parser": "^4.21.0", "karma-rollup-preprocessor": "^7.0.7", "vuepress-plugin-flexsearch": "^0.1.0", "@rollup/plugin-node-resolve": "^11.2.1", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@vuepress/plugin-html-redirect": "^0.1.2", "@typescript-eslint/eslint-plugin": "^4.21.0", "@vuepress/plugin-google-analytics": "1.8.2", "@simonbrunel/vuepress-plugin-versions": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.2.1_1619893031015_0.27353664802399935", "host": "s3://npm-registry-packages"}}, "3.3.0": {"name": "chart.js", "version": "3.3.0", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.3.0", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "c5be3807cf902b19bfe166ef853d786f66a53f8b", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.3.0.tgz", "fileCount": 40, "integrity": "sha512-3pO3kL2st1Lzhw+HTGvDwcNYWustHwvApM/v7b2td6CrthyEygbPoXaN/iGZfUpfuovZfJ4dy129RCr39BR1Bg==", "signatures": [{"sig": "MEYCIQDnesRHsWOPCQQ/3SQyiC1Zug5YtOFne/cYAFNRHBxmlwIhAIJ4SEl4zex+2MWQVO3RmZoj9ZMjfndQtRGzOF/jqOdJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1104021, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgq5nVCRA9TVsSAnZWagAAZFEP/iLHYiD2fZZITo2f39IK\nbyYKpIjPC00KKR9BM5BR/ylMvLL160c8qPYwHPoIqIvjN8uONkEtAaHUp3em\nrXifl6pWQJ6xA/EvhcO9mZ7MWLHDt14FFS5xDe192rc9w9r6InwM4OCYczO6\nbvjGqDcdL0go41qrYn6nbIBFV4wbnU3U4jLCzkUVOUBrblm3vZIeE0iT75pO\nVIrxSILbypP0jNjSf0ypCf2ijaYOzBjyuruKMrR91dR+6NyBgvIsoMPi57D5\nwLWzDtU5HG2VHXaSmQiMOBNn/JpKsTw3iFyjwE9hPPYMHRdo6sZNwAxXzI7b\nU20aG81OlAVvoOfSQqDh+lvdfR0t4kYdSKpUB18D+hDAW/e4WdMuyZJwBePj\nNv3p5RpQpuFKFAKh/2xYWzEh5hofKJWaC+F9SrqPeYBzseL1VDXReODWk0s+\nt0mLE4qGIVCrH6ei6z8ux8XyL5wNaoqFTx2S0BE7iccBE1VNbUXwD2yafCQT\nmofuqwWwv/h2o911x1vzf7hzIZq/DjAscolYGIeCfZYyhs4jbGC4Zmzjqio+\n7XXY/eubBLrg1GOkzh54ytOPsNWAz3ZLrX4wxrFeEAFf2D0ukKzb5mcuD7Ln\nFB8+HRwWZHgtpOKTs6yIcjPpATuHrxVGw8X8Tqn0YyxLjzHE5Tj5c+0lR8RH\nmqFH\r\n=zaMf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "types/index.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "0d6fa55714cda8d2107451e4e5759bf9379a6710", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "npm run build && vuepress build docs --no-cache", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "dev:ff": "karma start --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.js\" \"test/**/*.js\" \"docs/**/*.js\"", "lint-md": "eslint \"**/*.md\"", "docs:dev": "npm run build && vuepress dev docs --no-cache", "lint-tsc": "tsc", "autobuild": "rollup -c -w", "lint-types": "eslint \"types/**/*.ts\" && tsc -p types/tests/"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "14.17.0", "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "karma": "^6.3.2", "luxon": "^1.26.0", "yargs": "^17.0.1", "eslint": "^7.23.0", "moment": "^2.29.1", "rollup": "^2.44.0", "jasmine": "^3.7.0", "typedoc": "^0.20.35", "vuepress": "^1.8.2", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "pixelmatch": "^5.2.1", "typescript": "~4.1.0", "concurrently": "^6.0.1", "jasmine-core": "^3.7.1", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.3.0", "eslint-plugin-html": "^6.1.2", "vue-tabs-component": "^1.5.0", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "markdown-it-include": "^2.0.0", "rollup-plugin-terser": "^7.0.2", "vuepress-plugin-tabs": "^0.3.0", "@rollup/plugin-inject": "^4.0.2", "chartjs-adapter-luxon": "^1.0.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^2.1.0", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-istanbul": "^3.0.0", "vuepress-theme-chartjs": "^0.2.0", "@rollup/plugin-commonjs": "^19.0.0", "typedoc-plugin-markdown": "^3.6.1", "vuepress-plugin-typedoc": "^0.7.0", "vuepress-plugin-redirect": "^1.2.5", "@typescript-eslint/parser": "^4.21.0", "karma-rollup-preprocessor": "^7.0.7", "vuepress-plugin-flexsearch": "^0.1.0", "@rollup/plugin-node-resolve": "^13.0.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@vuepress/plugin-html-redirect": "^0.1.2", "@typescript-eslint/eslint-plugin": "^4.21.0", "@vuepress/plugin-google-analytics": "1.8.2", "@simonbrunel/vuepress-plugin-versions": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.3.0_1621858772769_0.8199262073852185", "host": "s3://npm-registry-packages"}}, "3.3.1": {"name": "chart.js", "version": "3.3.1", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.3.1", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "714cf466806e927d81caeb8df65b926f096c7c6a", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.3.1.tgz", "fileCount": 40, "integrity": "sha512-yApN5uxxHnzH3ohLe9ZfSqs8JBgrfpTRJcWBhkm0G0vZzSBc74X2LipOQ1h+w64/3rmUidQjOoUqRFNfkHxbcA==", "signatures": [{"sig": "MEYCIQCqZhcWGxP3u7237xOTdR+w3YpE1w5tVLmRWvhm5wf+kAIhAM5McMo8yl0lcVlDmo2lG3Xzr6zu82XD6JiiuL5vehKf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1104638, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgsrW2CRA9TVsSAnZWagAAKo0QAJFd2D9mcarxUGCsL91g\nJVAVLP7u+G3Bsh741h7K3+rHtLqFAobfVxP/OK6ibx+Qkc8CkmD5DfId589G\n+5jQt7iPhLYvqrxReK7vAtI0J2f9emTRqRzZmlzoJ2UxufI7IRfkhyHlFFVz\nqmRBsHSe8t2ieeYHiqhNOfJqWEDR61XKDmwH1WAqLnVNhJyTCcTVNiJyNGwo\nVSEPCTBhw+6JtU1k3jygkd0MDRMYJxseIBBFrXTdgZ3BI28mJOZ2Q27ocVs5\nKMN/XUBNsODAjUQAB6kpo5X+54V7fMy/lbEfPLb7WmMN40aKGFla/CoUwFbg\nFuDX8+gNi03CLasj/4sM0pSG8N5G2SVui078q/gCqTJRl2zEViDKxSvOu9Bn\nyWe6NIBVhfLk8hsY/3d1mIRunTGC3QUnDlZNEoqKGZjrHvgb+oi6DzmpfiZS\n/+glq+HNJRkGOk7N6Ff5g48YRU6gpYWJ2w7yA5EHRpwXTiDcaAlGMDfL3ELR\nER27uIm9gcy3ej3oo0uyEIcjvaRcfUvUh/YwGYBB7XPumooajWokiUYQ/ilB\nEFIvdw2ALZsqC+6mBmGnlgcWamIi8htMs7yB599xW2S0stwzxcD/C6SwFJX/\neIAOpktizvAzfOINGzEVNeXmyQ1c9S8kxxb17lXaXY+ZOzWi/2jCZ29QMK9t\niKx6\r\n=zu1Z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "types/index.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "5082d13d5aed19c75cb539bd81f94e0f953eed72", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "npm run build && vuepress build docs --no-cache", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "dev:ff": "karma start --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.js\" \"test/**/*.js\" \"docs/**/*.js\"", "lint-md": "eslint \"**/*.md\"", "docs:dev": "npm run build && vuepress dev docs --no-cache", "lint-tsc": "tsc", "autobuild": "rollup -c -w", "lint-types": "eslint \"types/**/*.ts\" && tsc -p types/tests/"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "14.17.0", "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "karma": "^6.3.2", "luxon": "^1.26.0", "yargs": "^17.0.1", "eslint": "^7.23.0", "moment": "^2.29.1", "rollup": "^2.44.0", "jasmine": "^3.7.0", "typedoc": "^0.20.35", "vuepress": "^1.8.2", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "pixelmatch": "^5.2.1", "typescript": "~4.1.0", "concurrently": "^6.0.1", "jasmine-core": "^3.7.1", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.3.0", "eslint-plugin-html": "^6.1.2", "vue-tabs-component": "^1.5.0", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "markdown-it-include": "^2.0.0", "rollup-plugin-terser": "^7.0.2", "vuepress-plugin-tabs": "^0.3.0", "@rollup/plugin-inject": "^4.0.2", "chartjs-adapter-luxon": "^1.0.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^2.1.0", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-istanbul": "^3.0.0", "vuepress-theme-chartjs": "^0.2.0", "@rollup/plugin-commonjs": "^19.0.0", "typedoc-plugin-markdown": "^3.6.1", "vuepress-plugin-typedoc": "^0.7.0", "vuepress-plugin-redirect": "^1.2.5", "@typescript-eslint/parser": "^4.21.0", "karma-rollup-preprocessor": "^7.0.7", "vuepress-plugin-flexsearch": "^0.1.0", "@rollup/plugin-node-resolve": "^13.0.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@vuepress/plugin-html-redirect": "^0.1.2", "@typescript-eslint/eslint-plugin": "^4.21.0", "@vuepress/plugin-google-analytics": "1.8.2", "@simonbrunel/vuepress-plugin-versions": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.3.1_1622324662010_0.2613730872314386", "host": "s3://npm-registry-packages"}}, "3.3.2": {"name": "chart.js", "version": "3.3.2", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.3.2", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "667f3a0b6371b9719d8949c04a5bcbaec0d8c615", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.3.2.tgz", "fileCount": 40, "integrity": "sha512-H0hSO7xqTIrwxoACqnSoNromEMfXvfuVnrbuSt2TuXfBDDofbnto4zuZlRtRvC73/b37q3wGAWZyUU41QPvNbA==", "signatures": [{"sig": "MEUCIF2CDYq4Tnie+pZ4Tlga6hib9iI3r760A983IXdCvsXwAiEAly7o0ce9uuDxJsxYs80WpdsJR0I5uTLdDr7bIYssbmc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1104137, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgs+OMCRA9TVsSAnZWagAAH98QAKEb+PTxi6DLUPjp46eY\ns1vrDAyqI3Ui1wLCiUPyxNjbDOnXFOxl1xOEP3uhvoESfcjfdz+vnxgR+Utn\ntheTd8ZdFsopbG0DJHMmxm07nkXq3QzhECWp0a5LQeN+cXs2dsW8R2ZsRUFV\nJmzzYmBwKqpml+UHmDHTP7H6pgqZ/imvTgqu2KCpZoeaK6FvxUByE4LmQ7Wp\nSEZ/Ibn4o8TbpJE3e9tpocew8/qpn3xVNI8tuM7SF0fVZhCD9NPm6Fn4fv8T\nzpUrPRh0R+Fq2z8KsRvuS2OaUHfu8i8qC4EvJdBlliy5E+S0JKwv+2xlfsvb\nX/dTgBCfc2SCF5jlMI4U4CeFeyEQS5yZDDxdMwG+Uhq0gWMhGY8LEa4JGvfP\n3SnmeaIs/hhvvzAhYb1ghkGhd1PPdDdGmVYYyjuzmcHI+4Z6HZERgULCjP0K\njbLlL+a73oo7KpwqVIgmvtpX6Tj4Oenoiv4OSKXqg4rRiZ50rwZYL8S9KRGD\nGLHdfcZ5vPi0+9gSQAdcQEHJmFF0/GooFxSDrMBkQ3ZwsqOxHIvWK0do+Qgr\nyvEnrBVme77jFzr/iJsRKVjypFuXfinEOgAd4a8yL2wmLyLfl4ux3K4yxTPk\niZYW8inv6U3S+QUgdN5HHaz+qQ9r//7CDqF3+Y2lPdrruRJKXZv0LMuC0fLg\nBJof\r\n=T1G4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "types/index.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "b9389f8e065e74ccceb59e0ef29164841e78351f", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "npm run build && vuepress build docs --no-cache", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "dev:ff": "karma start --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.js\" \"test/**/*.js\" \"docs/**/*.js\"", "lint-md": "eslint \"**/*.md\"", "docs:dev": "npm run build && vuepress dev docs --no-cache", "lint-tsc": "tsc", "autobuild": "rollup -c -w", "lint-types": "eslint \"types/**/*.ts\" && tsc -p types/tests/"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "14.17.0", "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "karma": "^6.3.2", "luxon": "^1.26.0", "yargs": "^17.0.1", "eslint": "^7.23.0", "moment": "^2.29.1", "rollup": "^2.44.0", "jasmine": "^3.7.0", "typedoc": "^0.20.35", "vuepress": "^1.8.2", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "pixelmatch": "^5.2.1", "typescript": "~4.1.0", "concurrently": "^6.0.1", "jasmine-core": "^3.7.1", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.3.0", "eslint-plugin-html": "^6.1.2", "vue-tabs-component": "^1.5.0", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "markdown-it-include": "^2.0.0", "rollup-plugin-terser": "^7.0.2", "vuepress-plugin-tabs": "^0.3.0", "@rollup/plugin-inject": "^4.0.2", "chartjs-adapter-luxon": "^1.0.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^2.1.0", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-istanbul": "^3.0.0", "vuepress-theme-chartjs": "^0.2.0", "@rollup/plugin-commonjs": "^19.0.0", "typedoc-plugin-markdown": "^3.6.1", "vuepress-plugin-typedoc": "^0.7.0", "vuepress-plugin-redirect": "^1.2.5", "@typescript-eslint/parser": "^4.21.0", "karma-rollup-preprocessor": "^7.0.7", "vuepress-plugin-flexsearch": "^0.1.0", "@rollup/plugin-node-resolve": "^13.0.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@vuepress/plugin-html-redirect": "^0.1.2", "@typescript-eslint/eslint-plugin": "^4.21.0", "@vuepress/plugin-google-analytics": "1.8.2", "@simonbrunel/vuepress-plugin-versions": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.3.2_1622401932211_0.44964960451703906", "host": "s3://npm-registry-packages"}}, "3.4.0": {"name": "chart.js", "version": "3.4.0", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.4.0", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "4fb2a750225fcc1b387221422f5d4260b55b4579", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.4.0.tgz", "fileCount": 40, "integrity": "sha512-mJsRm2apQm5mwz2OgYqGNG4erZh/qljcRZkWSa0kLkFr3UC3e1wKRMgnIh6WdhUrNu0w/JT9PkjLyylqEqHXEQ==", "signatures": [{"sig": "MEUCIQCJxfE85Ef/+b3a1zd7VAR8gN/vd5ci1LFlU/ycwofwlgIgRs4cq/XR8yNlFN/olWH0tH+qOJYI7Xp5QdP6eEEXUbQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1112405, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg11SHCRA9TVsSAnZWagAA6wQQAIusSffIivXZaMMZeDAi\nPwog75/g+F4iX/8JhM/x5T3AnMmDmTigeRRPGPHm3wffnRUjJrBjKF1esvqT\nMot8oIUe5D6KHVaCoIU56vmA6DkZ76LHk7tsA1wbXOtypCm5IsvwwDfhZYRR\nJWKi4qbuHX49CHcFq9/XRc4P0XpQLf0Pju89/L9SmU8uKvFlEDwX6g3ul+22\n8ITZjypUQk0qMw0DVTfPBQEVssynVo26AsW9qcxMjKtmcNjXfIbteeTbwqG0\npT04xJa0m1rS6HBZf/tiLXdeSdajASbabDPYXNUogNwhIon2ygpWKcguefW1\n6rvhPoPOXvIILk0kUICQgl4r/3FV3di4USTZzfx6Ph1u5mpGGq7UXPyN/n98\ndrlZQF1kOA30J//cvdOZfy36Ay2SVWiSauzYFUfEu+SK12AnFxse/RORRVNa\noK3KULzDJOk5S3B8rOTotsVAxpXlf/TQletETqvi/WKfUUxgkDAc57duQ14A\n1uC+2YD2LtYG263qbE+2h2E0snACRya70mpNYO+WXkpq9B0+itdULEQjjjf3\no9ocCF0I+9oTfUafPxTPsU6M7d+U8o+o3dJ5lCruCTetVzvKbND58LU/PNk/\nItHmaNPK0PpPjDBVlNJcuPFZM/K5VT9ky83mr23UOdt4szG6YbrpSFaaM7tN\nl2vF\r\n=mpdF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "types/index.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "4bdb57df8c53fa3f43042c3c19e717fd76cdeaec", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "npm run build && vuepress build docs --no-cache", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "dev:ff": "karma start --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.js\" \"test/**/*.js\" \"docs/**/*.js\"", "lint-md": "eslint \"**/*.md\"", "docs:dev": "npm run build && vuepress dev docs --no-cache", "lint-tsc": "tsc", "autobuild": "rollup -c -w", "lint-types": "eslint \"types/**/*.ts\" && tsc -p types/tests/"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "14.17.0", "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "karma": "^6.3.2", "luxon": "^1.26.0", "yargs": "^17.0.1", "eslint": "^7.23.0", "moment": "^2.29.1", "rollup": "^2.44.0", "jasmine": "^3.7.0", "typedoc": "^0.20.35", "vuepress": "^1.8.2", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "pixelmatch": "^5.2.1", "typescript": "~4.1.0", "concurrently": "^6.0.1", "jasmine-core": "^3.7.1", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.3.0", "eslint-plugin-html": "^6.1.2", "vue-tabs-component": "^1.5.0", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "markdown-it-include": "^2.0.0", "rollup-plugin-terser": "^7.0.2", "vuepress-plugin-tabs": "^0.3.0", "@rollup/plugin-inject": "^4.0.2", "chartjs-adapter-luxon": "^1.0.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^2.1.0", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-istanbul": "^3.0.0", "vuepress-theme-chartjs": "^0.2.0", "@rollup/plugin-commonjs": "^19.0.0", "typedoc-plugin-markdown": "^3.6.1", "vuepress-plugin-typedoc": "^0.7.0", "vuepress-plugin-redirect": "^1.2.5", "@typescript-eslint/parser": "^4.21.0", "karma-rollup-preprocessor": "^7.0.7", "vuepress-plugin-code-copy": "^1.0.6", "vuepress-plugin-flexsearch": "^0.1.0", "@rollup/plugin-node-resolve": "^13.0.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@vuepress/plugin-html-redirect": "^0.1.2", "@typescript-eslint/eslint-plugin": "^4.21.0", "@vuepress/plugin-google-analytics": "1.8.2", "@simonbrunel/vuepress-plugin-versions": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.4.0_1624724615193_0.3603448816249655", "host": "s3://npm-registry-packages"}}, "3.4.1": {"name": "chart.js", "version": "3.4.1", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.4.1", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "ff3b2b2a04a37b83618b4a6399a5f87ccc0f1e8a", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.4.1.tgz", "fileCount": 40, "integrity": "sha512-0R4mL7WiBcYoazIhrzSYnWcOw6RmrRn7Q4nKZNsBQZCBrlkZKodQbfeojCCo8eETPRCs1ZNTsAcZhIfyhyP61g==", "signatures": [{"sig": "MEUCIQCizgbVD+xAytENb7zT4TxZHM9Uz95X69xeW6xSKPaMyQIgfUmpYIRUgrAu/5TY7bAjygpBsogpK5pEWl2ozQHWFlE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1115087, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg4Z3dCRA9TVsSAnZWagAA06gP/jLjSecSVF0JlzgUTJ9P\nItEwv2rPnEJCnE0oUv6n5I/7NkeQjYULrYH88UP9XUyJYEcHb2+j/L2QxYr/\nGuKwzTIOxHpj3f95lopVVjwrft8Af7czFXucAlQPAsVfU5czYpkwV55NriRN\nlMxdHJh5H5/yJ5eNYf4d5/faaA+ycyA3t5jF65JNfKL7rfNu5dZygv5vBIMy\nziVmJrNT7ByrCWPMZ3ksUwjz70I0IrHJfQo5XDwxhpT2iVQ1C2vME1TdN02n\nxHqnA2MJ6hzMpEPup3cOVJT+FbmEoWqEEpliLeIgU4sq8GWLDyQtcLM+goOn\nEVOzHj7U9HWWOtEBOoC8+VznjdItye7SIzO86EvNG1lDEiafV9QBRtWwpFPC\n4kxTWNlGFN/TxTu+ZW4KRdayBRnrNy8qwe4a4L34hZUvWvxuhMT8p0+kCEvt\nI4KOhoHvMv6XDvqs9pNfNJiU1WZ/hlyAhBsQ2MEeUzzedPjGT7RwtN3BWMQe\n3b9BSbReu5sb4orBZcup6WuTGXDTYld5O2WqqsPdzL4D/UZ7PhTleDMZWMDO\n4wj1nz47Z26lgRHvteV7lW+amEFYmeeYlcT5kgyAa2Ru5T0Gy3lkqBO4hTNx\nopX1JRPv8Q+An7Z2YsEyG0atU2ozRN+bTySIeQQg+E4JU7c51vWrBxefx5OG\nir+k\r\n=bJUq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "types/index.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "dac71011314a118a33554ca0fe3220ade2cda798", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "npm run build && vuepress build docs --no-cache", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "dev:ff": "karma start --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.js\" \"test/**/*.js\" \"docs/**/*.js\"", "lint-md": "eslint \"**/*.md\"", "docs:dev": "npm run build && vuepress dev docs --no-cache", "lint-tsc": "tsc", "autobuild": "rollup -c -w", "lint-types": "eslint \"types/**/*.ts\" && tsc -p types/tests/"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "14.17.1", "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "karma": "^6.3.2", "luxon": "^1.26.0", "yargs": "^17.0.1", "eslint": "^7.23.0", "moment": "^2.29.1", "rollup": "^2.44.0", "jasmine": "^3.7.0", "typedoc": "^0.21.2", "vuepress": "^1.8.2", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "pixelmatch": "^5.2.1", "typescript": "~4.1.0", "concurrently": "^6.0.1", "jasmine-core": "^3.7.1", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.3.0", "eslint-plugin-html": "^6.1.2", "vue-tabs-component": "^1.5.0", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "markdown-it-include": "^2.0.0", "rollup-plugin-terser": "^7.0.2", "vuepress-plugin-tabs": "^0.3.0", "@rollup/plugin-inject": "^4.0.2", "chartjs-adapter-luxon": "^1.0.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^2.1.0", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-istanbul": "^3.0.0", "vuepress-theme-chartjs": "^0.2.0", "@rollup/plugin-commonjs": "^19.0.0", "typedoc-plugin-markdown": "^3.6.1", "vuepress-plugin-typedoc": "^0.8.1", "vuepress-plugin-redirect": "^1.2.5", "@typescript-eslint/parser": "^4.21.0", "karma-rollup-preprocessor": "^7.0.7", "vuepress-plugin-code-copy": "^1.0.6", "vuepress-plugin-flexsearch": "^0.2.0", "@rollup/plugin-node-resolve": "^13.0.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@vuepress/plugin-html-redirect": "^0.1.2", "@typescript-eslint/eslint-plugin": "^4.21.0", "@vuepress/plugin-google-analytics": "1.8.2", "@simonbrunel/vuepress-plugin-versions": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.4.1_1625398748399_0.6487733548351202", "host": "s3://npm-registry-packages"}}, "3.5.0": {"name": "chart.js", "version": "3.5.0", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.5.0", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "6eb075332d4ebbbb20a94e5a07a234052ed6c4fb", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.5.0.tgz", "fileCount": 40, "integrity": "sha512-J1a4EAb1Gi/KbhwDRmoovHTRuqT8qdF0kZ4XgwxpGethJHUdDrkqyPYwke0a+BuvSeUxPf8Cos6AX2AB8H8GLA==", "signatures": [{"sig": "MEUCIQDymgTXq05ZHP+eixKdOgxRNwGOhSgcBwmsoyO4jSrK3wIgNrxPKt3e9oE95Kh9AqwXz1H9nKMAQG9W9GYoX/074zc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1130370, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg/VQOCRA9TVsSAnZWagAAtVIQAIljFa5ZcrwosSBY4kvE\nSSIsfHQRMkrQql94Til6fZ5QluJd0jU+WeqTri+b5+2ca3QWOjct2ABeZR7e\n7jnE/BhynFVXUtxn/xExd2qhJQ8QPWymzbdZ12kxjLT43yHWaeIx+6E+rCNK\nB22ZDhnFqdTIo8c0+WpOB+EXPE0jhj+Bjeo+CZ0pWzAvjkDYyzxzx+bsORrO\nMR2+VO3UssXE7Tw0MJIOWiWXOzpreD0d0EOFamPUkfWlSGRxO75WdJV0oUoo\nnR3NfkXfimzpsox++xgPCPXTnWQb1TFxSTWU1LbSSPZKDKnbOoouYEPCXSs7\nthOpl0sm+E8Oin19TtrPGrogA5Xj2K+ftAl5kGbelBcLTR/FF1C+BuRWDQU2\nUe2N3TPNQzvdZfz6KBgsxhCs007fdW11v66Pf49ao28YsAI/qtKDc9lHQaeX\nWG+93oLDYH+VFN7ZBRDLtlQFfm+ak6nMflWkixeC+AzEoxv9E/My6yNgBI5y\ng04i/w2cg0AwNKCqLtNpY/B+by7N3eVTjdQg/YWCHBxxkxW8c0SflZuNKpJf\n41V1yQrmErzrNFd3b2ozMmSQIpjPHLMIoLDprGC+TyebZf/v+TefKdbpCwXk\nzvxzPi5yv3sPfyGKo5aSd2FX0dfumcnsAYMig9W75oRWvaic7iSwSxxMmfiw\nUOMX\r\n=OEpZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "types/index.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "a8337a32c976393e4648de633dcc49cdec2760b3", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "npm run build && vuepress build docs --no-cache", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "dev:ff": "karma start --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.js\" \"test/**/*.js\" \"docs/**/*.js\"", "lint-md": "eslint \"**/*.md\"", "docs:dev": "npm run build && vuepress dev docs --no-cache", "lint-tsc": "tsc", "autobuild": "rollup -c -w", "lint-types": "eslint \"types/**/*.ts\" && tsc -p types/tests/"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "14.17.3", "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "karma": "^6.3.2", "luxon": "^1.26.0", "yargs": "^17.0.1", "eslint": "^7.23.0", "moment": "^2.29.1", "rollup": "^2.44.0", "jasmine": "^3.7.0", "typedoc": "^0.21.2", "vuepress": "^1.8.2", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "pixelmatch": "^5.2.1", "typescript": "^4.3.5", "concurrently": "^6.0.1", "jasmine-core": "^3.7.1", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.3.0", "eslint-plugin-html": "^6.1.2", "vue-tabs-component": "^1.5.0", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "markdown-it-include": "^2.0.0", "rollup-plugin-terser": "^7.0.2", "vuepress-plugin-tabs": "^0.3.0", "@rollup/plugin-inject": "^4.0.2", "chartjs-adapter-luxon": "^1.0.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^2.1.0", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-istanbul": "^3.0.0", "vuepress-theme-chartjs": "^0.2.0", "@rollup/plugin-commonjs": "^19.0.0", "typedoc-plugin-markdown": "^3.6.1", "vuepress-plugin-typedoc": "^0.8.1", "vuepress-plugin-redirect": "^1.2.5", "@typescript-eslint/parser": "^4.21.0", "karma-rollup-preprocessor": "^7.0.7", "vuepress-plugin-code-copy": "^1.0.6", "vuepress-plugin-flexsearch": "^0.2.0", "@rollup/plugin-node-resolve": "^13.0.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@vuepress/plugin-html-redirect": "^0.1.2", "@typescript-eslint/eslint-plugin": "^4.21.0", "@vuepress/plugin-google-analytics": "1.8.2", "@simonbrunel/vuepress-plugin-versions": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.5.0_1627214862620_0.09785155365117748", "host": "s3://npm-registry-packages"}}, "3.5.1": {"name": "chart.js", "version": "3.5.1", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.5.1", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "73e24d23a4134a70ccdb5e79a917f156b6f3644a", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.5.1.tgz", "fileCount": 40, "integrity": "sha512-m5kzt72I1WQ9LILwQC4syla/LD/N413RYv2Dx2nnTkRS9iv/ey1xLTt0DnPc/eWV4zI+BgEgDYBIzbQhZHc/PQ==", "signatures": [{"sig": "MEUCICnb/88jZ0Uqqdq5vOeOyz2okL8khGzWO5XQLGU1WFu+AiEApuhkv2dLVyxYmvL3XtU6bXbiiwXkC4JXs+W8juW0JLI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1131260, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhHPwnCRA9TVsSAnZWagAAGdQQAIi4MK1yhYyvTBhFIM8b\nqpMW9opNPM4EmB3aVZuxJZFmxmHdEggeF7N1Cib8O1vWGCfjeAGyIxiS9WuL\nOTkAV0T6ZofLfJSTr8dz/TVX5SisDZtiCPq2EYYCM4xLleo7u9YWmN3AVncd\nxWgcsLQklH85ZJeGm7bmhiNnH/vdQTHv1DFUOSSg/FaV3iP7aHG94wvvDwF1\nsCsqdfHra2FsmzY+9kKyDCiCyyo+goS7l2R/GLuJfL/jpNEJ76pMYrKhkS+o\nSJKhbUZW5P50A7f0Rudh+kUGkc/vuslezF1K9K79iumgNA1Z3VhUsXIjAhk+\ncFJK7xV3Pzgp0I/GyByeWhjP6l9pEHVWTWN0yKasQTDBJkLpSY/1qBUJDx7E\nWZus92h04VnZ0+JNT8LkoWbIuDfPM01R3uiVR+1MKoM7e4uJLg7P8sBnD2eV\nExhRfYiPMnDTkhe+bGJ78nsOrGNmutxQ9mJl5GA6ptAM265ikeoCsE/63EMF\nQdkOvEBkl/9VsiCepmZHvE1qiQq+oZB5yXS0nfGPbCO8yKUmanhk8jmfTyvz\n2ZUte6gg/OLdKPY77eQ7k9JRSpFdOv5vJBeIr3vMiRY2guDdF3ujEDTZkV16\nm1j0/SLCFQyQPAi/aU7gf9EDiU2dJzb4phk+wtXxopqzTquiCWzk1Hsg/zm5\nLJGM\r\n=j/pw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "types/index.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "4bf42f4e8edf60c5f8cd6f31132a3686dd457cde", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "npm run build && vuepress build docs --no-cache", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "dev:ff": "karma start --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.js\" \"test/**/*.js\" \"docs/**/*.js\"", "lint-md": "eslint \"**/*.md\"", "docs:dev": "npm run build && vuepress dev docs --no-cache", "lint-tsc": "tsc", "autobuild": "rollup -c -w", "lint-types": "eslint \"types/**/*.ts\" && tsc -p types/tests/"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.14", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "14.17.4", "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.6", "karma": "^6.3.2", "luxon": "^1.26.0", "yargs": "^17.0.1", "eslint": "^7.23.0", "moment": "^2.29.1", "rollup": "^2.44.0", "jasmine": "^3.7.0", "typedoc": "^0.21.2", "vuepress": "^1.8.2", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "pixelmatch": "^5.2.1", "typescript": "^4.3.5", "concurrently": "^6.0.1", "jasmine-core": "^3.7.1", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.3.0", "eslint-plugin-html": "^6.1.2", "vue-tabs-component": "^1.5.0", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "markdown-it-include": "^2.0.0", "rollup-plugin-terser": "^7.0.2", "vuepress-plugin-tabs": "^0.3.0", "@rollup/plugin-inject": "^4.0.2", "chartjs-adapter-luxon": "^1.0.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^2.1.0", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-istanbul": "^3.0.0", "vuepress-theme-chartjs": "^0.2.0", "@rollup/plugin-commonjs": "^19.0.0", "typedoc-plugin-markdown": "^3.6.1", "vuepress-plugin-typedoc": "^0.8.1", "vuepress-plugin-redirect": "^1.2.5", "@typescript-eslint/parser": "^4.21.0", "karma-rollup-preprocessor": "^7.0.7", "vuepress-plugin-code-copy": "^1.0.6", "vuepress-plugin-flexsearch": "^0.2.0", "@rollup/plugin-node-resolve": "^13.0.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@vuepress/plugin-html-redirect": "^0.1.2", "@typescript-eslint/eslint-plugin": "^4.21.0", "@vuepress/plugin-google-analytics": "1.8.2", "@simonbrunel/vuepress-plugin-versions": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.5.1_1629289511378_0.3598760604467337", "host": "s3://npm-registry-packages"}}, "3.6.0": {"name": "chart.js", "version": "3.6.0", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.6.0", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "a87fce8431d4e7c5523d721f487f53aada1e42fe", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.6.0.tgz", "fileCount": 40, "integrity": "sha512-iOzzDKePL+bj+ccIsVAgWQehCXv8xOKGbaU2fO/myivH736zcx535PGJzQGanvcSGVOqX6yuLZsN3ygcQ35UgQ==", "signatures": [{"sig": "MEUCIEJfXvKJP0VVum/Sv9nl53tNy7jEINcNvkTALHV3777yAiEAu+C7Lo7BWxCmS10tfXfIY3caPoHJZiTOLq048VbHU5w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1141308}, "main": "dist/chart.js", "types": "types/index.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "40e88ba45c656497652030769995ff54c8fc1053", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "npm run build && vuepress build docs --no-cache", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "dev:ff": "karma start --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.js\" \"test/**/*.js\" \"docs/**/*.js\"", "lint-md": "eslint \"**/*.md\"", "docs:dev": "npm run build && vuepress dev docs --no-cache", "lint-tsc": "tsc", "autobuild": "rollup -c -w", "lint-types": "eslint \"types/**/*.ts\" && node -r esm types/tests/autogen.js && tsc -p types/tests/"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "14.18.1", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "glob": "^7.1.6", "karma": "^6.3.2", "luxon": "^1.26.0", "yargs": "^17.0.1", "eslint": "^7.23.0", "moment": "^2.29.1", "rollup": "^2.44.0", "jasmine": "^3.7.0", "typedoc": "^0.21.2", "vuepress": "^1.8.2", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "pixelmatch": "^5.2.1", "typescript": "^4.3.5", "concurrently": "^6.0.1", "jasmine-core": "^3.7.1", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.3.1", "eslint-plugin-html": "^6.1.2", "vue-tabs-component": "^1.5.0", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "markdown-it-include": "^2.0.0", "rollup-plugin-terser": "^7.0.2", "vuepress-plugin-tabs": "^0.3.0", "@rollup/plugin-inject": "^4.0.2", "chartjs-adapter-luxon": "^1.0.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^2.1.0", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-istanbul": "^3.0.0", "vuepress-theme-chartjs": "^0.2.0", "@rollup/plugin-commonjs": "^19.0.0", "typedoc-plugin-markdown": "^3.6.1", "vuepress-plugin-typedoc": "^0.8.1", "vuepress-plugin-redirect": "^1.2.5", "@typescript-eslint/parser": "^4.21.0", "karma-rollup-preprocessor": "^7.0.7", "vuepress-plugin-code-copy": "^1.0.6", "vuepress-plugin-flexsearch": "^0.2.0", "@rollup/plugin-node-resolve": "^13.0.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@vuepress/plugin-html-redirect": "^0.1.2", "@typescript-eslint/eslint-plugin": "^4.21.0", "@vuepress/plugin-google-analytics": "1.8.2", "@simonbrunel/vuepress-plugin-versions": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.6.0_1635012580691_0.9194598546590604", "host": "s3://npm-registry-packages"}}, "3.6.1": {"name": "chart.js", "version": "3.6.1", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.6.1", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "edcb6584b8d810306f655f89538c00a4f440c58e", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.6.1.tgz", "fileCount": 40, "integrity": "sha512-AycnixR0I325Fp3bqQ7wRJbkIJPwz/9IZtUBvdBWMjK5+nKCy6FZ3VejkDTtB9udePEXNt1UYoGTsNL49JoIbg==", "signatures": [{"sig": "MEYCIQCg659eoSCSqyfHFPr3aUhM+meu03f2HjfVgIappGZHhwIhAMpelmYyvdIxF9856LQUBs1IA5ifCzn5AyalEFfCVC8g", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1149910, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpm6VCRA9TVsSAnZWagAAnfUP/RW75kPliCuvZxCRyG1h\nipUu6+WlU/IHQ46CXFSyEgM0MfjCybsIqETQY4LqVvEQHl2PTOj1gkqLzpv4\nvhOtvqtmbWDTr2wTH9BPKbqvZUmue+iOSfLk7Njo3ZBSVRa5J6jcMB655ctj\nWp2mKBckMvcZCM5qFWME+5rMwZj7JWCo4NKc8SgjGIDntngCFdBprlHjQDl7\n7bNmiOTE3dtabwqwXShJdCt2vDZS8e9IGIfjsQMJ8xDGyucLBmNc7xLcd4Fn\nACZwoNqxKu+GyMM7QYoEB//cf/6JxrGcXpmqTfJTFAMWFWrU/AAY8iiwkVTR\nS95Ixv3OYhDEmq6JzUxmH9Bx7jVT5MVYf2SWZAF9xhZs4TKDbOBLl5yDe8zf\n8vUBMKLKnQw1r7/QukSOKc0Eh4sKbvRDyuNFSo5Bc+3FWFcBVfCHA5WkNTcQ\n+bsrclUH5OAtH8uE59R/F7fGnjktMdCiFVCcj2qW6D8EEBukGtTe8cpMcNMr\nemX1nUcht7+t1CtVQY5bTVjFTz3W2SNBkppN1Vj5p9hKkEUSg70U4tDuuDRG\nSvFW77exwUsfp9Lv63bGBtt30MMFBlZyksYtjMXbZ9p2CNkI8WBC/SBKD5fK\novApfvvn4o5nMsPVMwVMsjo0uGddpN3e8GygCz9tJoWLPef6M1eWLPP24hTO\ntdAi\r\n=FwBq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "types/index.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "688cd470e9864f3dffaf2cad8dbf90b5f8a9dc3a", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "npm run build && vuepress build docs --no-cache", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "dev:ff": "karma start --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.js\" \"test/**/*.js\" \"docs/**/*.js\"", "lint-md": "eslint \"**/*.md\"", "docs:dev": "npm run build && vuepress dev docs --no-cache", "lint-tsc": "tsc", "autobuild": "rollup -c -w", "lint-types": "eslint \"types/**/*.ts\" && node -r esm types/tests/autogen.js && tsc -p types/tests/"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "14.18.1", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "glob": "^7.1.6", "karma": "^6.3.2", "luxon": "^1.26.0", "yargs": "^17.0.1", "eslint": "^7.23.0", "moment": "^2.29.1", "rollup": "^2.44.0", "jasmine": "^3.7.0", "typedoc": "^0.21.2", "vuepress": "^1.8.2", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "pixelmatch": "^5.2.1", "typescript": "^4.3.5", "concurrently": "^6.0.1", "jasmine-core": "^3.7.1", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.3.1", "eslint-plugin-html": "^6.1.2", "vue-tabs-component": "^1.5.0", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "markdown-it-include": "^2.0.0", "rollup-plugin-terser": "^7.0.2", "vuepress-plugin-tabs": "^0.3.0", "@rollup/plugin-inject": "^4.0.2", "chartjs-adapter-luxon": "^1.0.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^2.1.0", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-istanbul": "^3.0.0", "vuepress-theme-chartjs": "^0.2.0", "@rollup/plugin-commonjs": "^19.0.0", "typedoc-plugin-markdown": "^3.6.1", "vuepress-plugin-typedoc": "^0.8.1", "vuepress-plugin-redirect": "^1.2.5", "@typescript-eslint/parser": "^4.21.0", "karma-rollup-preprocessor": "^7.0.7", "vuepress-plugin-code-copy": "^1.0.6", "vuepress-plugin-flexsearch": "^0.2.0", "@rollup/plugin-node-resolve": "^13.0.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@vuepress/plugin-html-redirect": "^0.1.2", "@typescript-eslint/eslint-plugin": "^4.21.0", "@vuepress/plugin-google-analytics": "1.8.2", "@simonbrunel/vuepress-plugin-versions": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.6.1_1638297237374_0.984249487884578", "host": "s3://npm-registry-packages"}}, "3.6.2": {"name": "chart.js", "version": "3.6.2", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.6.2", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "47342c551f688ffdda2cd53b534cb7e461ecec33", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.6.2.tgz", "fileCount": 40, "integrity": "sha512-Xz7f/fgtVltfQYWq0zL1Xbv7N2inpG+B54p3D5FSvpCdy3sM+oZhbqa42eNuYXltaVvajgX5UpKCU2GeeJIgxg==", "signatures": [{"sig": "MEYCIQCvfx5yJlSYTwL9jvdW9RNPJWhCEcS3GAzT9uTewkxZNgIhAPG9auGUkxmIjyIioFi4V7D+btq/EsCmFTnfQaAcwrCU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1149959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhrMOkCRA9TVsSAnZWagAAWYwQAInTNXFG/InJ/nfd+gJI\nLVJ2HKfb9qlO3qi82L0Cevv3HQnehwek1lnxPKp158q8gZjXiA4/+raTHUS7\nySbimjTujoZjUYkX0yyjxjiYVadz3LFLXV5L6CvKbuBfFoTRXBTYACh1+ZQV\n+hFsB6FgAF84CD7h3S9YEEj3utJUrLBJhf5ldZYEyS3nLmU4TfcxGVRFZjXg\nzC7x1o9lc96tQzQvn00I5VFRne+UwVqs4xE2YMmpVxMXcqPgOf8Qe/GTle5F\ndRNgY2N5WVmd6p6cV40bp1OhJpNxA8BSDqDnoRj7n3QXJLn4ACfOayIdUrFT\nFXXeKtRysYjfolB5a4IKdcC6ZsfCBcq3J3ILwxNbj+t5vkGBx4dNnSyAf9a5\n+EEY3BpqHLOGdnO1KGV3eCQsObMOGz9TE2P4b/uo+dZf5eZ+VLapqexI0Sq3\nye+tscVUUN+S4u7e3FiG3AfPjpbVtZm18kwAmLIkphpvNPU979YB8Hn9r/2D\niI7jI44LASGnya7FwayIfpn63Vsf447BP8Kxtod2RYuhGoAC5gH3JrylB7RT\n6PlZB6NYLwSGamEtxhjfMGqQhga8RAg3qyQ7Gmc1dUxQviPs1TegeTQBJDrE\n7DC0yZtZRcrn3dvqX8L2gqw8NO7GulSmiuibB/T+nGPHTWO9KBGwY4OVio27\ntut1\r\n=qLw4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "types/index.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "9fa26f9247419e61e0aa527c34aec3c74273acb2", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "npm run build && vuepress build docs --no-cache", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "dev:ff": "karma start --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.js\" \"test/**/*.js\" \"docs/**/*.js\"", "lint-md": "eslint \"**/*.md\"", "docs:dev": "npm run build && vuepress dev docs --no-cache", "lint-tsc": "tsc", "autobuild": "rollup -c -w", "lint-types": "eslint \"types/**/*.ts\" && node -r esm types/tests/autogen.js && tsc -p types/tests/"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "14.18.1", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "glob": "^7.1.6", "karma": "^6.3.2", "luxon": "^1.26.0", "yargs": "^17.0.1", "eslint": "^7.23.0", "moment": "^2.29.1", "rollup": "^2.44.0", "jasmine": "^3.7.0", "typedoc": "^0.21.2", "vuepress": "^1.8.2", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "pixelmatch": "^5.2.1", "typescript": "^4.3.5", "concurrently": "^6.0.1", "jasmine-core": "^3.7.1", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.3.1", "eslint-plugin-html": "^6.1.2", "vue-tabs-component": "^1.5.0", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "markdown-it-include": "^2.0.0", "rollup-plugin-terser": "^7.0.2", "vuepress-plugin-tabs": "^0.3.0", "@rollup/plugin-inject": "^4.0.2", "chartjs-adapter-luxon": "^1.0.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^2.1.0", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-istanbul": "^3.0.0", "vuepress-theme-chartjs": "^0.2.0", "@rollup/plugin-commonjs": "^19.0.0", "typedoc-plugin-markdown": "^3.6.1", "vuepress-plugin-typedoc": "^0.8.1", "vuepress-plugin-redirect": "^1.2.5", "@typescript-eslint/parser": "^4.21.0", "karma-rollup-preprocessor": "^7.0.7", "vuepress-plugin-code-copy": "^1.0.6", "vuepress-plugin-flexsearch": "^0.2.0", "@rollup/plugin-node-resolve": "^13.0.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@vuepress/plugin-html-redirect": "^0.1.2", "@typescript-eslint/eslint-plugin": "^4.21.0", "@vuepress/plugin-google-analytics": "1.8.2", "@simonbrunel/vuepress-plugin-versions": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.6.2_1638712227892_0.6631798612651523", "host": "s3://npm-registry-packages"}}, "3.7.0": {"name": "chart.js", "version": "3.7.0", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.7.0", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "7a19c93035341df801d613993c2170a1fcf1d882", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.7.0.tgz", "fileCount": 40, "integrity": "sha512-31gVuqqKp3lDIFmzpKIrBeum4OpZsQjSIAqlOpgjosHDJZlULtvwLEZKtEhIAZc7JMPaHlYMys40Qy9Mf+1AAg==", "signatures": [{"sig": "MEYCIQC6buWRL4Mz9AKc3aw5J1VhBP8gzh11RES4PNgC5xFawAIhAMjDV2WOD3fVz/oZC5qOG4Wxj6X3ZMtnK2eldIFKliSu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1156584, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhxMk5CRA9TVsSAnZWagAA1G0P/0Ul17CrBK8uc34yua0J\nqD22+S9uZJIY6aOU9vUJ/Kp5G5cf9Lv9b7acpNAjw5u+Orzf84jt61h4Sc9X\nFeDoWYwA0GzpfQmPE3Xm0tP/U7gELTEF8NzPTme4SPxwydP7u5cmGMmUNil6\nTqN/DUAdtG7IdR2xzFD/yKdbZazuZPK/eKW1j/uhN3qNVsRkZl+x87QAf/KP\nhQjIQeNCf9qd7ggzPcxY8mWQWyPebw3q0smbUO3j5FF5B45bFCpkeUSpDPzc\n3XsUAjMvUGG8hvJ4RooWJFQdnOOJK0ZjAT+9Sdh8c5Mj8uiwIq8zOsrlwTWE\noooLtpVKPHkNq9tzMIlEqbtJ60CQ/SpXB2zV+pPH4/Fn97xBWBtmnF7AKvBi\nt/2LIqPxV+PrH8Nppt1K/U+PV6vqKje4djckiD5Z96mxeOSx9Wll6Hi0BkNW\nnSK+32xYFKQVOrHDEtsZRocXPMNtXBXNAJDMaBnM3YO6kfPPIyF/ctmS2vtF\n+h5CehllbGznUEN7f0pSGNW3elyV0uTWeTHQCxRTq6Q/QSNAFwa0PZqAtYIj\nlhl3uaTQtmZuYhbpJ2KDVP5Xdx5EK6T4evyzCPha85Q5Q6GMG5wTZ+Jn+SJE\nkYhya7v5xXUj42FR8gZT3fUdCLGxJpw26TsApkEblHisv958gJS3ZQwPbYk8\nDJKD\r\n=pfZZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "types/index.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "a5b46a859c6398618ec4787222b35bdd09b9f834", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "npm run build && vuepress build docs --no-cache", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "dev:ff": "karma start --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.js\" \"test/**/*.js\" \"docs/**/*.js\"", "lint-md": "eslint \"**/*.md\"", "docs:dev": "npm run build && vuepress dev docs --no-cache", "lint-tsc": "tsc", "autobuild": "rollup -c -w", "lint-types": "eslint \"types/**/*.ts\" && node -r esm types/tests/autogen.js && tsc -p types/tests/"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "14.18.2", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "glob": "^7.1.6", "karma": "^6.3.2", "luxon": "^2.2.0", "yargs": "^17.0.1", "eslint": "^8.5.0", "moment": "^2.29.1", "rollup": "^2.44.0", "jasmine": "^3.7.0", "typedoc": "^0.22.10", "vuepress": "^1.8.2", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "pixelmatch": "^5.2.1", "typescript": "^4.3.5", "concurrently": "^6.0.1", "jasmine-core": "^3.7.1", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "moment-timezone": "^0.5.34", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.3.1", "eslint-plugin-html": "^6.1.2", "vue-tabs-component": "^1.5.0", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "markdown-it-include": "^2.0.0", "rollup-plugin-terser": "^7.0.2", "vuepress-plugin-tabs": "^0.3.0", "@rollup/plugin-inject": "^4.0.2", "chartjs-adapter-luxon": "^1.0.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "@types/offscreencanvas": "^2019.6.4", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^2.2.1", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-istanbul": "^3.0.0", "vuepress-theme-chartjs": "^0.2.0", "@rollup/plugin-commonjs": "^21.0.1", "typedoc-plugin-markdown": "^3.6.1", "vuepress-plugin-typedoc": "^0.10.0", "vuepress-plugin-redirect": "^1.2.5", "@typescript-eslint/parser": "^5.8.0", "karma-rollup-preprocessor": "^7.0.7", "vuepress-plugin-code-copy": "^1.0.6", "vuepress-plugin-flexsearch": "^0.3.0", "@rollup/plugin-node-resolve": "^13.0.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@vuepress/plugin-html-redirect": "^0.1.2", "@typescript-eslint/eslint-plugin": "^5.8.0", "@vuepress/plugin-google-analytics": "^1.8.3", "@simonbrunel/vuepress-plugin-versions": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.7.0_1640286521686_0.753830985079466", "host": "s3://npm-registry-packages"}}, "3.7.1": {"name": "chart.js", "version": "3.7.1", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.7.1", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "0516f690c6a8680c6c707e31a4c1807a6f400ada", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.7.1.tgz", "fileCount": 40, "integrity": "sha512-8knRegQLFnPQAheZV8MjxIXc5gQEfDFD897BJgv/klO/vtIyFFmgMXrNfgrXpbTr/XbTturxRgxIXx/Y+ASJBA==", "signatures": [{"sig": "MEUCICQOUrjpxxzQnqu74ha7eZNZHh5oanuaTm0BVWQA/cMoAiEAweoLAAoX7kf7LMTGl9DQVOM+YR9SLHEx4kG2U2+w9NA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1158083, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiB8ofCRA9TVsSAnZWagAA560P/innMR7ttjHuyPUhECsb\nubPP7GIC2vExe+njFKnH/2AJVmccNkY2YpcsvzxQAD9mmvyovOR0vn5QIqcS\n8VWD848/tO/xZJ9A9X2OJsXI1mDm6tzCYhihpT7mDboHp4swvnHPTOCd6TT0\nHyk4YOloeg9pF7poL2zr3XhKkrIaymRGEJH2mbT+jqxDjU7ocsYXEAgOj5TZ\nsYzLNlKizhGfY3GQXgbRaFfzU2zbYPnNoBDr9WoNejDQU5f6uiyEXupgtReH\ntL1qLHn2cWldJib8BgdP2GcD6hLijDtG1t7cIPjfP17pmJ01UoUnTFVwujk1\nsLzGQKIzGFLkTD6U/4wpnSziiqOlpw/RRJ9i6rTjsp19cmsf2xgZceq0GTpm\n3LfEX3sbz7A4DLE9tOjjsey6QhDP8A4sjNdxh5VtsVsePZSa1M23pHktrmB5\nb1/HLCOkGf9XVhaX1nUxfB4Fi/0cscF+7ZqLeU3K0ZeWxbxITTyu+C806oFA\nLVGUZ/X0w5fCBEzAD7+itUc9LuVyNlVQ6A3u/bbv7usUW95dm4AnXD1W8sDu\niCkK+Ns64vsLyhQZG3vdl3V5sDMhOG9MiYs1IGfA2+PeJSqgG2YDbu6sklfU\ndjCFDG3+BPqvkoHOu521GPfzxtqtD6qRhysikMGOczGX+QehPUPkAzC7liaP\nktD4\r\n=xO9J\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "types/index.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "7b621d725d1711b99783dc5f1f53fa1261ec7f07", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "npm run build && vuepress build docs --no-cache", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "dev:ff": "karma start --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.js\" \"test/**/*.js\" \"docs/**/*.js\"", "lint-md": "eslint \"**/*.md\"", "docs:dev": "npm run build && vuepress dev docs --no-cache", "lint-tsc": "tsc", "autobuild": "rollup -c -w", "lint-types": "eslint \"types/**/*.ts\" && node -r esm types/tests/autogen.js && tsc -p types/tests/"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "14.19.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "glob": "^7.1.6", "karma": "^6.3.2", "luxon": "^2.2.0", "yargs": "^17.0.1", "eslint": "^8.5.0", "moment": "^2.29.1", "rollup": "^2.44.0", "jasmine": "^3.7.0", "typedoc": "^0.22.10", "vuepress": "^1.8.2", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "pixelmatch": "^5.2.1", "typescript": "^4.3.5", "concurrently": "^6.0.1", "jasmine-core": "^3.7.1", "@kurkle/color": "^0.1.9", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "moment-timezone": "^0.5.34", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.3.1", "eslint-plugin-html": "^6.1.2", "vue-tabs-component": "^1.5.0", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "markdown-it-include": "^2.0.0", "rollup-plugin-terser": "^7.0.2", "vuepress-plugin-tabs": "^0.3.0", "@rollup/plugin-inject": "^4.0.2", "chartjs-adapter-luxon": "^1.0.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "@types/offscreencanvas": "^2019.6.4", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^2.2.1", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-istanbul": "^3.0.0", "vuepress-theme-chartjs": "^0.2.0", "@rollup/plugin-commonjs": "^21.0.1", "typedoc-plugin-markdown": "^3.6.1", "vuepress-plugin-typedoc": "^0.10.0", "vuepress-plugin-redirect": "^1.2.5", "@typescript-eslint/parser": "^5.8.0", "karma-rollup-preprocessor": "^7.0.7", "vuepress-plugin-code-copy": "^1.0.6", "vuepress-plugin-flexsearch": "^0.3.0", "@rollup/plugin-node-resolve": "^13.0.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@vuepress/plugin-html-redirect": "^0.1.2", "@typescript-eslint/eslint-plugin": "^5.8.0", "@vuepress/plugin-google-analytics": "^1.8.3", "@simonbrunel/vuepress-plugin-versions": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.7.1_1644677663796_0.6769686803815675", "host": "s3://npm-registry-packages"}}, "3.8.0": {"name": "chart.js", "version": "3.8.0", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.8.0", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "c6c14c457b9dc3ce7f1514a59e9b262afd6f1a94", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.8.0.tgz", "fileCount": 40, "integrity": "sha512-cr8xhrXjLIXVLOBZPkBZVF6NDeiVIrPLHcMhnON7UufudL+CNeRrD+wpYanswlm8NpudMdrt3CHoLMQMxJhHRg==", "signatures": [{"sig": "MEQCIEZ/E9KvHeh+aNyDFP3W6zkimtpJIU3mpwWhXk0ia4RtAiBucKhBuXwttNEUy5xBoEsroRvN0AF5mrYkeg06lCIimQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1171857, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijjofACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrAiw/5ALmpVUcUs6+mXtEiUHMgMuC9IKhkCSSAjeU1qOIO04wYUzvd\r\nmwhzt5sMUgtcWuoDyQ3zFtQKHjpNUQQRSPexWPyLJ4n73ZtHcqVyotsRs52z\r\n9Lupc1XXR7P3tENeBqJ2JqY9QoMQ4Kxzh0o1NJ/+oVPKivVr4bfc88iX1fy2\r\nSCOaxou/ELPioES/h0ssNrnq2QaffNQFsvATaqSUD7yEz2L8GOYlURH8gDwM\r\ncEzpGiQKlD8wMryYzcqtPx6uZQdiVBdwzNgvHdQ7A83nw5rfjsKYnctVztp1\r\nl12X18yb6YFAmcOWMxDN+EUNtD0Qf/x7fADvdwMO5hRjy6Tme1mrAsN46IyS\r\n3PItvOUdMl2KtLjX7Q6I5GRR0ll9bBtYBudby1xIo2uS1KWzHVR/g2ohVu+5\r\n1dqYTheMHEXwjq2U3jfdiHagnca4VVf2otxLHwiuZ/Cq4/zZMwMDHPyujW/d\r\n3lCgDzi+TiD9jel1utXOmVFdy6IrHiVGSQ/2g52vPfLWCv2S4PXSDmVqg3VM\r\ns+Odhi1FweAI0lx2VXoEDgUrkoJI4OPCUbnI73H1piC49KBK3bbMQ8fygjx7\r\nohsmTvhNDsrYRu6KTL05dk/H6ZvTPlTAtVTpvnB9gDWtakckRUmlXKl6FrBO\r\nyKl8/4lwZ7P9OeQEWTqA70DMFzJ9I3xIHiw=\r\n=3UQa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "types/index.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "1a1151b122c481d814850a001de1b2d0468fce4e", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "npm run build && vuepress build docs --no-cache", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "build": "rollup -c", "dev:ff": "karma start --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.js\" \"test/**/*.js\" \"docs/**/*.js\"", "lint-md": "eslint \"**/*.md\"", "docs:dev": "npm run build && vuepress dev docs --no-cache", "lint-tsc": "tsc", "autobuild": "rollup -c -w", "lint-types": "eslint \"types/**/*.ts\" && node -r esm types/tests/autogen.js && tsc -p types/tests/"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "6.14.17", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "14.19.2", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "glob": "^7.1.6", "karma": "^6.3.2", "luxon": "^2.2.0", "yargs": "^17.0.1", "eslint": "^8.5.0", "moment": "^2.29.1", "rollup": "^2.44.0", "jasmine": "^3.7.0", "typedoc": "^0.22.10", "vuepress": "^1.8.2", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "pixelmatch": "^5.2.1", "typescript": "^4.3.5", "concurrently": "^6.0.1", "jasmine-core": "^3.7.1", "@kurkle/color": "^0.2.1", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "moment-timezone": "^0.5.34", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.4.0", "eslint-plugin-html": "^6.1.2", "vue-tabs-component": "^1.5.0", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "markdown-it-include": "^2.0.0", "rollup-plugin-terser": "^7.0.2", "vuepress-plugin-tabs": "^0.3.0", "@rollup/plugin-inject": "^4.0.2", "chartjs-adapter-luxon": "^1.0.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "@types/offscreencanvas": "^2019.6.4", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^2.2.1", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-istanbul": "^3.0.0", "vuepress-theme-chartjs": "^0.2.0", "@rollup/plugin-commonjs": "^21.0.1", "typedoc-plugin-markdown": "^3.6.1", "vuepress-plugin-typedoc": "^0.10.0", "vuepress-plugin-redirect": "^1.2.5", "@typescript-eslint/parser": "^5.8.0", "karma-rollup-preprocessor": "^7.0.7", "vuepress-plugin-code-copy": "^1.0.6", "vuepress-plugin-flexsearch": "^0.3.0", "@rollup/plugin-node-resolve": "^13.0.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@vuepress/plugin-html-redirect": "^0.1.2", "@typescript-eslint/eslint-plugin": "^5.8.0", "@vuepress/plugin-google-analytics": "^1.8.3", "@simonbrunel/vuepress-plugin-versions": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.8.0_1653488159637_0.49950847361326933", "host": "s3://npm-registry-packages"}}, "3.8.1": {"name": "chart.js", "version": "3.8.1", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.8.1", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "b208c6b3a21f2daa1f9a8fbb95484fecad91150f", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.8.1.tgz", "fileCount": 38, "integrity": "sha512-vGrPYJlvp6F2x/uF2PD+7+skzPCrMhJ8cpQJBDLPz98xNmzlz7cbo1N9bvbKffYfbHSnb1hA1UnTGxwCnW8DWQ==", "signatures": [{"sig": "MEQCIBjvjAymXO8R6HhJswY2Q0Io3uSOE15nDr4WSZnhdNbLAiBrBy9R3Hx+KnafYrDUrkkYh2CzytVBt+Jx7B7rj9ne0g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1174521, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi3WopACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpvTw/+P2mCAsJ52XyFO2mdTfGIH0QUnMYwpjYI9nvh2DYfXwT7oW3B\r\nMPbcW3I6SK8/TvrkNmyUHKukpBpD2kqaNR8a9mFeORsN/GAj1tm+lC3RVbvA\r\nmXMIFE7DM98upaAKE2Zie1Vlc0IxXJQ9JSlf6QuL8lcNTRJWRfGMhDLnroty\r\nLMGsRdO093Ug4JfyXbZnIGAxiKEmSsc20RZpAy9BSijAiH35NwmJ1faCjs7i\r\ncq2RSgJjibjpGbMLUnNpGlAeC6kkEuAZuCxGUM1ecSlx6gJxrTZX1sm9sH0h\r\nTOp830qb6FU6BAlSLou73BiT66jch2xhH/k0E5pOqBBy14eMGk3xX36utokB\r\njEfwkimH1Tn58JuPvg8Dj/8pNNYPbgQqcJ5noU+QBqyyuFGo7YETV0fgvDpw\r\nmeHx+QStHSza+TKzKXauQLUE7rLAVpsAWyQ1960U05gFMxlg0i+K6Yp9+IWs\r\n61FmcHh7RIo6McFSsYmXC1ux2SyxqUKz2DIGnr4BWwtqHMEUHvo7S9qz/zGO\r\nGQgXxnB79EY5Yi0bBDD2rzZi0Q80hOqDT8zoEErri0nbkuGclyZGcY5sWDuP\r\nsG+KD3LO2rA5Z/8stYcSjwrS8Jpjqjr4PsXl79v54Ro7nWELRhJoecff4z1J\r\naxTs3LL3zXX9xnmY7zc1p6G7CChIf26DU4o=\r\n=QFZU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "types/index.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "aa4507d22241163b238e95020d25113f9bd33f22", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "npm run build && vuepress build docs --no-cache", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && npm run test-ci", "build": "rollup -c", "dev:ff": "karma start --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.js\" \"test/**/*.js\" \"docs/**/*.js\"", "lint-md": "eslint \"**/*.md\"", "test-ci": "cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "docs:dev": "npm run build && vuepress dev docs --no-cache", "lint-tsc": "tsc", "autobuild": "rollup -c -w", "lint-types": "eslint \"types/**/*.ts\" && node -r esm types/tests/autogen.js && tsc -p types/tests/"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "16.16.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "glob": "^7.1.6", "karma": "^6.3.2", "luxon": "^2.2.0", "yargs": "^17.0.1", "eslint": "^8.5.0", "moment": "^2.29.1", "rollup": "^2.44.0", "jasmine": "^3.7.0", "typedoc": "^0.22.10", "vuepress": "^1.8.2", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "pixelmatch": "^5.2.1", "typescript": "^4.3.5", "concurrently": "^6.0.1", "jasmine-core": "^3.7.1", "@kurkle/color": "^0.2.1", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "moment-timezone": "^0.5.34", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.4.0", "eslint-plugin-html": "^6.1.2", "vue-tabs-component": "^1.5.0", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "markdown-it-include": "^2.0.0", "rollup-plugin-terser": "^7.0.2", "vuepress-plugin-tabs": "^0.3.0", "@rollup/plugin-inject": "^4.0.2", "chartjs-adapter-luxon": "^1.0.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "@types/offscreencanvas": "^2019.6.4", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^2.2.1", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-istanbul": "^3.0.0", "vuepress-theme-chartjs": "^0.2.0", "@rollup/plugin-commonjs": "^21.0.1", "typedoc-plugin-markdown": "^3.6.1", "vuepress-plugin-typedoc": "^0.10.0", "vuepress-plugin-redirect": "^1.2.5", "@typescript-eslint/parser": "^5.8.0", "karma-rollup-preprocessor": "^7.0.7", "vuepress-plugin-code-copy": "^1.0.6", "vuepress-plugin-flexsearch": "^0.3.0", "@rollup/plugin-node-resolve": "^13.0.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@vuepress/plugin-html-redirect": "^0.1.2", "@typescript-eslint/eslint-plugin": "^5.8.0", "@vuepress/plugin-google-analytics": "^1.8.3", "@simonbrunel/vuepress-plugin-versions": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.8.1_1658677801523_0.3402736241554123", "host": "s3://npm-registry-packages"}}, "3.8.2": {"name": "chart.js", "version": "3.8.2", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.8.2", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "e3ebb88f7072780eec4183a788a990f4a58ba7a1", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.8.2.tgz", "fileCount": 40, "integrity": "sha512-7rqSlHWMUKFyBDOJvmFGW2lxULtcwaPLegDjX/Nu5j6QybY+GCiQkEY+6cqHw62S5tcwXMD8Y+H5OBGoR7d+ZQ==", "signatures": [{"sig": "MEQCIHtqUp6nFM0W+3iJyZU0UbUxWSfqjuQrzdDvHUsVp7YDAiBSWZabtP3AKGpA0jY5W+h/o/SC5NdLEoHu1KQSMjfsdg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1174939, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi3ZS0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrtjw//a3RTkbqEBTd81BpppUIiwbraUxnO84SHg0r0VSheVYVJVxix\r\nQ2iBEtJgNhd6x1TIlgOms6zonFJTv9JvgLLmyW02GV5lRsFJzFGQTss21QL5\r\njJRGneKoSf4xJSHyVssMjW6BC82M3TYYK7ragJw2kY+Bt83X0NZlxeKYiRGd\r\nxG/uGDB3/5q9kkWdwqYSkdHuv5u50ExoL+45EmzLZUOW6FM47oLNQrFrbSbM\r\nRHV6QPBRUZCG609SN/5YkiOG0SeQdHCpGHbAqOivEyIByJlvXWWbRcEMuXYE\r\n1E/NhxrVEvlGp3+1AaGG4P+5ZkLB4Xw6v9mPo6ZqrsIAc9W+7fhFxO64WkGi\r\nISq/Y9stWBsMnPlGaeuYSFGAwEXS4YvhOWkboIiqDP2eTKhKrfpitNcbGvx/\r\ncICKCyDgOZ+yiWAoCf5ucPSJNpadh3diwmf7TIz1yS8h6H+Q5HCL4TFybRfd\r\nqc5khd3Q+rxHLMfb24NbIvVPK2C/wnxUn0xLEKqshQh6/zKoGr6rBJkxTecy\r\nnGSSXqZ+HjAZCBkMBuzg7jZwZFo8IqWAG4oDMGyLa+rxjvsPdDO7GjPV8xqG\r\nNNpbu9la+GF8dGjxwNekQ5kfrGN4F9Adqx88cCmxPkdqDrlVNX5QdFcU9hvJ\r\ng6ov3A7Goop5i/PW+PsP3+sXnEbjKvQx1s0=\r\n=BE7m\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "types/index.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.esm.js", "gitHead": "26e70b7608e821ffef51fe25cfeace6c87560b41", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "npm run build && vuepress build docs --no-cache", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && npm run test-ci", "build": "rollup -c", "dev:ff": "karma start --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.js\" \"test/**/*.js\" \"docs/**/*.js\"", "lint-md": "eslint \"**/*.md\"", "test-ci": "cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "docs:dev": "npm run build && vuepress dev docs --no-cache", "lint-tsc": "tsc", "autobuild": "rollup -c -w", "lint-types": "eslint \"types/**/*.ts\" && node -r esm types/tests/autogen.js && tsc -p types/tests/"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "16.16.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "glob": "^7.1.6", "karma": "^6.3.2", "luxon": "^2.2.0", "yargs": "^17.0.1", "eslint": "^8.5.0", "moment": "^2.29.1", "rollup": "^2.44.0", "jasmine": "^3.7.0", "typedoc": "^0.22.10", "vuepress": "^1.8.2", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "pixelmatch": "^5.2.1", "typescript": "^4.3.5", "concurrently": "^6.0.1", "jasmine-core": "^3.7.1", "@kurkle/color": "^0.2.1", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "moment-timezone": "^0.5.34", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.4.0", "eslint-plugin-html": "^6.1.2", "vue-tabs-component": "^1.5.0", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "markdown-it-include": "^2.0.0", "rollup-plugin-terser": "^7.0.2", "vuepress-plugin-tabs": "^0.3.0", "@rollup/plugin-inject": "^4.0.2", "chartjs-adapter-luxon": "^1.0.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "@types/offscreencanvas": "^2019.6.4", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^2.2.1", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-istanbul": "^3.0.0", "vuepress-theme-chartjs": "^0.2.0", "@rollup/plugin-commonjs": "^21.0.1", "typedoc-plugin-markdown": "^3.6.1", "vuepress-plugin-typedoc": "^0.10.0", "vuepress-plugin-redirect": "^1.2.5", "@typescript-eslint/parser": "^5.8.0", "karma-rollup-preprocessor": "^7.0.7", "vuepress-plugin-code-copy": "^1.0.6", "vuepress-plugin-flexsearch": "^0.3.0", "@rollup/plugin-node-resolve": "^13.0.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@vuepress/plugin-html-redirect": "^0.1.2", "@typescript-eslint/eslint-plugin": "^5.8.0", "@vuepress/plugin-google-analytics": "^1.8.3", "@simonbrunel/vuepress-plugin-versions": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.8.2_1658688692463_0.3255637985091753", "host": "s3://npm-registry-packages"}}, "3.9.0": {"name": "chart.js", "version": "3.9.0", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.9.0", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "9ad30ec53e53d2ddfe58c11cf306a77d285d8ad9", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.9.0.tgz", "fileCount": 615, "integrity": "sha512-SHOC6eCTe3dXe6owLHnO56xTLBbVeMHskkAlgPPVkd6xa0mv3EPhH5azRLg1cucYFsp4UloH/4NjSG0oOIFJtw==", "signatures": [{"sig": "MEUCIQDwWHXmFsy8l4r17gRlJYf78ucAKvwx/m+sC/nH0PKoTAIgH5SgvxXh3pt3QCsCRb0G1N7GFWtx2wgjVvovVkyQ/mo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21078899, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi6SEZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoCOA/9GwL+iLYgF9nmeJ6GAtxud4eJdQXaBO8DBqcuqAyNntNefL0j\r\nvGslw1KnDPRJMTEASUSOZE73qcmZTva4FaQ0Kut5NqW5vxgclbsb0gU8vHwK\r\n1HF2Opg6h2qpr91dGZQfqo5OH/V7+/pmPksxcvf2LIVNSYPbZxye9CPAoYZT\r\nzG5muZXVJOyK53so6GfgfOu+tGNL2XecyxJH2m/Gy4IRFXokobmh/5N0aBb4\r\noYj+dAjzcLV0IZcZnzjEQTGo2xL51dbdUBLjbYvqXF3IYu+QUXfYFrXwMcRv\r\nQ8zO1hmbbHATELagEW6MU+jd3K9DPwMKMPknYaZwml6LGKZNhETFjb+L5fe4\r\nAITafznuUArhAPEAS6bfSz6ECuH19pBQk0kXcDgLzJKmYKCzYgzkrJk0xOvF\r\njZYecQP4bJNGoAGn915sRA1bBAvFMoaZ2YFZZv3EboPkqvvAH3D+chYkJZWn\r\nYEy9Gv0XPwN0eoq4mmqT0OkrUEoeyNanZ1oRGtmJvO6a17K4OwX+kMHv+I6H\r\n2vZuAaesGP/RgbToX9y1FpX2kfp8oOIviwCxNsjxvBBdyaUxs7IaXjGfAJJq\r\nvSryi3P9nC9vyfJDkDcQdPM5L94paXsd3q1sHnbsd5SoIv/4C7yORq4Gnd4V\r\n0AYQK2TE2H+8VzeQp73uBTJSXXxpL1iqNWw=\r\n=2jWb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "types/index.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.mjs", "gitHead": "7079a1caac1bdf10bdec89969d9283a90ceac808", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "npm run build && vuepress build docs --no-cache", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && npm run test-ci", "build": "rollup -c", "dev:ff": "karma start --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.js\" \"test/**/*.js\" \"docs/**/*.js\"", "lint-md": "eslint \"**/*.md\"", "test-ci": "cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "docs:dev": "npm run build && vuepress dev docs --no-cache", "lint-tsc": "tsc", "autobuild": "rollup -c -w", "lint-types": "eslint \"types/**/*.ts\" && node -r esm types/tests/autogen.js && tsc -p types/tests/"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "16.16.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "glob": "^7.1.6", "karma": "^6.3.2", "luxon": "^2.2.0", "yargs": "^17.0.1", "eslint": "^8.5.0", "moment": "^2.29.1", "rollup": "^2.44.0", "jasmine": "^3.7.0", "typedoc": "^0.22.10", "vuepress": "^1.8.2", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "pixelmatch": "^5.2.1", "typescript": "^4.3.5", "concurrently": "^6.0.1", "jasmine-core": "^3.7.1", "@kurkle/color": "^0.2.1", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "moment-timezone": "^0.5.34", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.4.0", "eslint-plugin-html": "^6.1.2", "vue-tabs-component": "^1.5.0", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "markdown-it-include": "^2.0.0", "rollup-plugin-terser": "^7.0.2", "vuepress-plugin-tabs": "^0.3.0", "@rollup/plugin-inject": "^4.0.2", "chartjs-adapter-luxon": "^1.0.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "@types/offscreencanvas": "^2019.6.4", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^2.2.1", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-istanbul": "^3.0.0", "vuepress-theme-chartjs": "^0.2.0", "@rollup/plugin-commonjs": "^21.0.1", "typedoc-plugin-markdown": "^3.6.1", "vuepress-plugin-typedoc": "^0.10.0", "vuepress-plugin-redirect": "^1.2.5", "@typescript-eslint/parser": "^5.8.0", "karma-rollup-preprocessor": "^7.0.7", "vuepress-plugin-code-copy": "^1.0.6", "vuepress-plugin-flexsearch": "^0.3.0", "@rollup/plugin-node-resolve": "^13.0.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@vuepress/plugin-html-redirect": "^0.1.2", "@typescript-eslint/eslint-plugin": "^5.8.0", "@vuepress/plugin-google-analytics": "^1.8.3", "@simonbrunel/vuepress-plugin-versions": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.9.0_1659445529110_0.3678348018891453", "host": "s3://npm-registry-packages"}}, "3.9.1": {"name": "chart.js", "version": "3.9.1", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@3.9.1", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "3abf2c775169c4c71217a107163ac708515924b8", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-3.9.1.tgz", "fileCount": 615, "integrity": "sha512-Ro2JbLmvg83gXF5F4sniaQ+lTbSv18E+TIf2cOeiH1Iqd2PGFOtem+DUufMZsCJwFE7ywPOpfXFBwRTGq7dh6w==", "signatures": [{"sig": "MEYCIQCW3BoKrHyZljkIVm589cJvyi/HV7BtZDiqurHDPQNldwIhAKKy0XmI2rMiQuHbTrwnRFvDS4FovGmxd9hTD1P3kv9I", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21078923, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi6m9SACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoxJw//dUOHq804ZYqtymfBhdhIFu9gVaDoeGffhILqbw+u4u7qbsrf\r\nUm89M5jVZO8OzWsQmeLgIKQxfuhcnIA2syNWKxEH36/3ypzZuLD6F5D3ZDgR\r\nOZ0nDMDicmCzkCc1amO8OVAdA+WOtW6ZY0djDKlGUZiSJ2WR0Jj/J0BuH1Ky\r\nuzz1nwC9sFitP0bFSe/bdzCSroHwGrBY1YlfdjJsTqVtkrPrw0ecS3yyK2il\r\nMKaFypzDdyckviZ4UodsD3KLP11AyGjUmR9+dstsbdsgsfNjqXaCtwAurtUj\r\nAt+0BUS0yZ68cb4i3asadVSmkBkLX/LPDY+QoieHm9gzYE6/lS8/r7bV/sUQ\r\ndp9UQ2SVrmb+oX9IpUsIQ+t+HAWFLjQHhxckF4UKkGzTl7a7yP5EEpu0qPJn\r\nXQtSVthKc4IdTJ9J4WoVMkGpJe6Q37tQIzltMoNFMvDXAaxq1bJ4v4oSugLY\r\nlvrPTt3Tff50B5iSHu2u/SQnIvlJAtYpi2D7uGbjD70Fdnx1o42SdadWp6/V\r\ncxfo4ug9YZhB8ibZWj1Sulert1hxxlbV9Boz2P7GpRjnHZbu8RsiG0BRd6xK\r\nc5JT1sY3z0wFzeqaeCfwNzhaPw9vRYAO656AKNttsvQSpnLtyBl4Ho7i3a1N\r\n0QGqy/LhvRM4/hUUkKwQuTNt+XKLFujGRNE=\r\n=HUYj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/chart.js", "types": "types/index.esm.d.ts", "unpkg": "dist/chart.min.js", "module": "dist/chart.mjs", "gitHead": "5ea4b3adffcea61c7ec256ac3976927c5619c17a", "scripts": {"dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "docs": "npm run build && vuepress build docs --no-cache", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && npm run test-ci", "build": "rollup -c", "dev:ff": "karma start --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.js\" \"test/**/*.js\" \"docs/**/*.js\"", "lint-md": "eslint \"**/*.md\"", "test-ci": "cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep", "docs:dev": "npm run build && vuepress dev docs --no-cache", "lint-tsc": "tsc", "autobuild": "rollup -c -w", "lint-types": "eslint \"types/**/*.ts\" && node -r esm types/tests/autogen.js && tsc -p types/tests/"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "dist/chart.min.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "_nodeVersion": "16.16.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "glob": "^7.1.6", "karma": "^6.3.2", "luxon": "^2.2.0", "yargs": "^17.0.1", "eslint": "^8.5.0", "moment": "^2.29.1", "rollup": "^2.44.0", "jasmine": "^3.7.0", "typedoc": "^0.22.10", "vuepress": "^1.8.2", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "pixelmatch": "^5.2.1", "typescript": "^4.3.5", "concurrently": "^6.0.1", "jasmine-core": "^3.7.1", "@kurkle/color": "^0.2.1", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "moment-timezone": "^0.5.34", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.4.0", "eslint-plugin-html": "^6.1.2", "vue-tabs-component": "^1.5.0", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "markdown-it-include": "^2.0.0", "rollup-plugin-terser": "^7.0.2", "vuepress-plugin-tabs": "^0.3.0", "@rollup/plugin-inject": "^4.0.2", "chartjs-adapter-luxon": "^1.0.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "@types/offscreencanvas": "^2019.6.4", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^2.2.1", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-istanbul": "^3.0.0", "vuepress-theme-chartjs": "^0.2.0", "@rollup/plugin-commonjs": "^21.0.1", "typedoc-plugin-markdown": "^3.6.1", "vuepress-plugin-typedoc": "^0.10.0", "vuepress-plugin-redirect": "^1.2.5", "@typescript-eslint/parser": "^5.8.0", "karma-rollup-preprocessor": "^7.0.7", "vuepress-plugin-code-copy": "^1.0.6", "vuepress-plugin-flexsearch": "^0.3.0", "@rollup/plugin-node-resolve": "^13.0.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@vuepress/plugin-html-redirect": "^0.1.2", "@typescript-eslint/eslint-plugin": "^5.8.0", "@vuepress/plugin-google-analytics": "^1.8.3", "@simonbrunel/vuepress-plugin-versions": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_3.9.1_1659531090582_0.12426911394312823", "host": "s3://npm-registry-packages"}}, "4.0.0-alpha.2": {"name": "chart.js", "version": "4.0.0-alpha.2", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@4.0.0-alpha.2", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "da8e2dd5669f91bd0a42a683a9a736b319b5a76a", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-4.0.0-alpha.2.tgz", "fileCount": 550, "integrity": "sha512-zQWWelV2aRRR2pdMT8mYlqMA9v168ja469JQG1i1+laXaHB9MzlqAHf+JqiPyOsKnpTRsClcqH3KJcGKJFhqDg==", "signatures": [{"sig": "MEYCIQDYSYVBop3hRKt4W3WOUGe6AP6YATwOgCJ23kXcqN5bbwIhAK+0DgmiiDFBzB/T6m1Sco6gg5iwABabWXZw/QXPO+lC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22619933, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRvXJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrr5hAAlxOhyxw1HdSuQzXr8vr2iy1haobkKf3Izca0NMAvcnnE1oGq\r\n7bXL07ZHO6clmVI9+qMGWVUWfuzwautHF6tZH3pf/hrG1b9r8pIz/YX1m/Ym\r\nLiL8HLlE+iKXvTr9nbc+HZKxGG4r9yYtRGQkPWq4FAdDefzuvkrxYbWyl8ze\r\nbnZOY1Y7GTcHgTf3AR4DkXW4Cnpl6/Rx/960jSxzn5uu33M6ExJde3BNA4iG\r\nMhMXAOTUouiU6G7U520HDtDV1KUHPNXHKRm69wljDjxE0oe3P3LyzstrSGcy\r\nuuZWP0NvV4++pBE5A25T1zmr/mQsO2MOhwTm4NvC2/70nSdlXtOFZED8tNs6\r\n2ft8849nVmH1tZIP3cpCXWF4y5KQ8Z6rsR5/NFUOZQj5diKpMAtaFcV5zOqu\r\niAq0tmI9NX8Ju7/RxXhmsj2EuwT9ZaLvTdoFVT12pNibOQPKt/mjywyYFdZQ\r\naqYYjnQNJQuH/7qkSXp9wOfD2xqYP1F3RT2UG23l2ecdNfrYG6GVPOsDHppB\r\nt1WF4utAxb/fCXI362ZrmqwzHSQEbCOwHqzX8/VaHMgiZZZMSJkELEDRo1cv\r\n8oJgu1RSnVC6hw/CT0oAl9OjZs5nH0YD0LxIM0hUM76zNGzolH1spZAccGEG\r\ntXHpSczluqTQkl5JgNx1AXO7DXIyKQThFiA=\r\n=bg8H\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/chart.js", "pnpm": {"overrides": {"html-entities": "1.4.0"}, "peerDependencyRules": {"ignoreMissing": ["chart.js"]}}, "type": "module", "types": "./dist/types.d.ts", "unpkg": "./dist/chart.umd.js", "engines": {"pnpm": "^7.0.0"}, "exports": {".": {"types": "./dist/types.d.ts", "import": "./dist/chart.js"}, "./auto": {"types": "./auto/auto.d.ts", "import": "./auto/auto.js"}, "./helpers": {"types": "./helpers/helpers.d.ts", "import": "./helpers/helpers.js"}}, "gitHead": "c849b7bc1d974c3e760c69afb32aaa0f9201aba4", "scripts": {"dev": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers chrome --grep", "docs": "pnpm run build && pnpm --filter \"./docs/**\" build", "lint": "concurrently \"pnpm:lint-*\"", "test": "pnpm lint && pnpm test-ci", "build": "tsc --noEmit && rollup -c", "dev:ff": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.{js,ts}\" \"test/**/*.js\" \"docs/**/*.js\"", "lint-md": "eslint \"**/*.md\"", "test-ci": "concurrently \"pnpm:test-ci-*\"", "docs:dev": "pnpm run build && pnpm --filter \"./docs/**\" dev", "autobuild": "rollup -c -w", "test-size": "size-limit", "lint-types": "eslint \"types/**/*.ts\" && pnpm build && node types/tests/autogen.js && tsc -p types/tests/", "test-ci-karma": "cross-env NODE_ENV=test karma start ./karma.conf.cjs --auto-watch --single-run --coverage --grep", "test-ci-integration": "pnpm --filter \"./test/integration/**\" test"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "./dist/chart.umd.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "sideEffects": ["./auto/auto.js", "./dist/chart.umd.js"], "_nodeVersion": "16.17.1", "_hasShrinkwrap": false, "packageManager": "pnpm@7.9.0", "readmeFilename": "README.md", "devDependencies": {"esm": "^3.2.25", "glob": "^8.0.3", "karma": "^6.3.2", "luxon": "^3.0.1", "yargs": "^17.5.1", "eslint": "^8.21.0", "moment": "^2.29.4", "rollup": "^2.77.2", "jasmine": "^3.7.0", "@swc/core": "^1.2.224", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "pixelmatch": "^5.3.0", "size-limit": "^8.0.0", "typescript": "^4.7.4", "concurrently": "^7.3.0", "jasmine-core": "^3.7.1", "@kurkle/color": "^0.2.1", "@types/estree": "^1.0.0", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "moment-timezone": "^0.5.34", "eslint-plugin-es": "^4.1.0", "rollup-plugin-dts": "^4.2.2", "chartjs-test-utils": "^0.4.0", "eslint-plugin-html": "^7.1.0", "rollup-plugin-swc3": "^0.3.0", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^4.0.4", "chartjs-adapter-luxon": "^1.2.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "@types/offscreencanvas": "^2019.7.0", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^3.0.0", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^3.0.0", "@rollup/plugin-commonjs": "^22.0.2", "@typescript-eslint/parser": "^5.32.0", "karma-rollup-preprocessor": "7.0.7", "@size-limit/preset-big-lib": "^8.0.0", "@rollup/plugin-node-resolve": "^13.3.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.32.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_4.0.0-alpha.2_1665594825467_0.7931972770371924", "host": "s3://npm-registry-packages"}}, "4.0.0-alpha.3": {"name": "chart.js", "version": "4.0.0-alpha.3", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@4.0.0-alpha.3", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "3453fbd142bb080f3b587188e97165bedcd72f9c", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-4.0.0-alpha.3.tgz", "fileCount": 550, "integrity": "sha512-iuRQgWS3DxwOnTxoyMi60KOUXnBx2Z9u7rLIuxwMNmga4JaTpAgvub/29Pwa2RjL4v4K92Fx8dJvToPBW2MJ3Q==", "signatures": [{"sig": "MEQCIH7YxAdNjTb4P22rpuY9T0SM7W5jCTJMsCRalZhfwcQqAiBxCygOAnj2NdDlMzVWnyRwAL0JwRXEsxiLrzWqG5JxfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22663379, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjViqfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqEQQ//Wp4Ee96u/S3OEiYOh5+K+rQK7YhGhFWGpzFmc7dX6NSP2Q0K\r\nhv/qMM3Pd3bcCsm3bD0XJFI+XPx2ln7iu7EM9KTBICq9q/3DYJuyYw2liVkK\r\nsIEemisd/Fdet+wHC+h2TzAg82HDbeuEmYD/ETdwtp/SOV1uIdqqArgs2AtN\r\nMdStBuvb7qVyzT6QZOMkakYak0ApdF41LjiK0jHMhUtWNBrfr1kR4nwrFbUA\r\n/yRUDVoTNjfBxi3z2E1rFZ54D+LkmC9HPl1NPsPsnujQr7cCMf4/4GrG1aS9\r\nKRekjN/DMZObXt9h4qyjzUtQFqtyVXQfFoEiKrgBuqP3oAt0LglKG2L8WEKl\r\nsjyIkAnihT39O99XzGVuX7ElJawpyyTu5p724qvhR4RHjWUd5/ZeupdA4WFv\r\np12libU0iv7MDaCx+WDQixLcVgA+/3yz5lEDTrG7Fk9eVV7naclZJ6S84cMl\r\nkMEnEk0mIGlxSTXnGnnC6sc0n9wpRVvhwyuz+wcupy7E0uket8aogHOBOTPl\r\n3cBFx7zO6sKYeR5jTGbVgpoi5Zd3cyJdvAGZa/nezR3GhaJwuPztK4eWbKAD\r\nuLrOiKfT4qUcAHKiN4V0AImS6Ddj8Dosc8R3AHaJSrgKov9Hrniem2kCX5d+\r\nsu0MOfVV4Yj1h0kKXAMeOiTLRURi9bnHG84=\r\n=rdJ6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/chart.js", "pnpm": {"overrides": {"html-entities": "1.4.0"}, "peerDependencyRules": {"ignoreMissing": ["chart.js"]}}, "type": "module", "types": "./dist/types.d.ts", "unpkg": "./dist/chart.umd.js", "engines": {"pnpm": "^7.0.0"}, "exports": {".": {"types": "./dist/types.d.ts", "import": "./dist/chart.js"}, "./auto": {"types": "./auto/auto.d.ts", "import": "./auto/auto.js"}, "./helpers": {"types": "./helpers/helpers.d.ts", "import": "./helpers/helpers.js"}}, "gitHead": "355755900444e3e90c224efc178888b1431169c9", "scripts": {"dev": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers chrome --grep", "docs": "pnpm run build && pnpm --filter \"./docs/**\" build", "lint": "concurrently \"pnpm:lint-*\"", "test": "pnpm lint && pnpm test-ci", "build": "tsc --noEmit && rollup -c", "dev:ff": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.{js,ts}\" \"test/**/*.js\" \"docs/**/*.js\"", "lint-md": "eslint \"**/*.md\"", "test-ci": "concurrently \"pnpm:test-ci-*\"", "docs:dev": "pnpm run build && pnpm --filter \"./docs/**\" dev", "autobuild": "rollup -c -w", "test-size": "size-limit", "lint-types": "eslint \"types/**/*.ts\" && pnpm build && node types/tests/autogen.js && tsc -p types/tests/", "test-ci-karma": "cross-env NODE_ENV=test karma start ./karma.conf.cjs --auto-watch --single-run --coverage --grep", "test-ci-integration": "pnpm --filter \"./test/integration/**\" test"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "./dist/chart.umd.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "sideEffects": ["./auto/auto.js", "./dist/chart.umd.js"], "_nodeVersion": "16.18.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.9.0", "readmeFilename": "README.md", "devDependencies": {"esm": "^3.2.25", "glob": "^8.0.3", "karma": "^6.3.2", "luxon": "^3.0.1", "yargs": "^17.5.1", "eslint": "^8.21.0", "moment": "^2.29.4", "rollup": "^2.77.2", "jasmine": "^3.7.0", "@swc/core": "^1.2.224", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "pixelmatch": "^5.3.0", "size-limit": "^8.0.0", "typescript": "^4.7.4", "concurrently": "^7.3.0", "jasmine-core": "^3.7.1", "@kurkle/color": "^0.2.1", "@types/estree": "^1.0.0", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "moment-timezone": "^0.5.34", "eslint-plugin-es": "^4.1.0", "rollup-plugin-dts": "^4.2.2", "chartjs-test-utils": "^0.4.0", "eslint-plugin-html": "^7.1.0", "rollup-plugin-swc3": "^0.3.0", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^4.0.4", "chartjs-adapter-luxon": "^1.2.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "@types/offscreencanvas": "^2019.7.0", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^3.0.0", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^3.0.0", "@rollup/plugin-commonjs": "^22.0.2", "@typescript-eslint/parser": "^5.32.0", "karma-rollup-preprocessor": "7.0.7", "@size-limit/preset-big-lib": "^8.0.0", "@rollup/plugin-node-resolve": "^13.3.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.32.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_4.0.0-alpha.3_1666591390811_0.9537764967379665", "host": "s3://npm-registry-packages"}}, "4.0.0-release": {"name": "chart.js", "version": "4.0.0-release", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@4.0.0-release", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "88d7faa36302ebbbd63f2ed0372ce16a8203b401", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-4.0.0-release.tgz", "fileCount": 20, "integrity": "sha512-RqiYlvc/DN38pqJdmGoQnBqvHtWNLF0wlC8k+/5vZtL50Td2JhifsQVKxGf9XcuY9HpnEoNYxM9D4+86w70nkA==", "signatures": [{"sig": "MEQCIFAH4uX9InueONh2ZUQLYh8X/xxTzmj6aBHw+Gt19EMgAiAKxrsZfGoEUIfooq4YRHnh7XYcmfnjHyG+qLuF++KrDQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3155572, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjcsc/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpXZA/+JRnVy2WM/U9ghj274NkuLZvWgl1L9C9alzTYDjnQJjIUFd6i\r\noyZD69dt+kps/KXROsGXjk/8CMb3jzHAkzX+55eDdcc1WNzHOvIqrGO5O0v7\r\n354nNBaW9SyjLdHGoiwsIeWjaXj97mFqZ4RhEi0YQjDtvReSsxhbS9BURP/W\r\nVq18xbsKsWnLDANzlzicnffPgfrpFtjtJnQPR9rHRrnebycDRuYjwxM4XIzm\r\n8Hv1w1b3fPqSjE/kwep4AAHa/nCld1EF50/J0G1goWleDOPt7FNQEUZ69Xkt\r\nSSmJBnSheJ9GtZnF6q5FrLDdRrRmNqpB11B5ahODDm6JFZf5YMg4XokS8E0q\r\nSSFxXzvK+lGhDu5/xREFC0e5uKPtDnGTP21+QMEY2ygwQcpqysexezQtCPxm\r\nxPn744rkhUePBBlUDnEpwO2yUJtMQbLQOxErQGeE5R4V73rykPOipWYWZeUR\r\nPUl90mikEJV0eaGF1GMJ3jgmkMZrFQfLlzTToEeHLt54Vo2Za8YqRK2WrZ3i\r\n/RGKedA9rRkgTip4yo7Af/a4yahwkG1qVLFYM4/w+tFAPwlbeR/PQQVpFfLn\r\nTiBrXqcEbqnd5JYoyUi55FnTQCMOB1oJTs7AywAK3eJikoe8Z1biFkcgcS9y\r\nn6d/wTHX25utk/CjwfIIvg8ZVX3LE4P2T6M=\r\n=fnYA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/chart.js", "pnpm": {"overrides": {"html-entities": "1.4.0"}, "peerDependencyRules": {"ignoreMissing": ["chart.js"]}}, "type": "module", "types": "./dist/types.d.ts", "unpkg": "./dist/chart.umd.js", "engines": {"pnpm": "^7.0.0"}, "exports": {".": {"types": "./dist/types.d.ts", "import": "./dist/chart.js"}, "./auto": {"types": "./auto/auto.d.ts", "import": "./auto/auto.js"}, "./helpers": {"types": "./helpers/helpers.d.ts", "import": "./helpers/helpers.js"}}, "gitHead": "11f3cf6361facf33c2d06e703f05c3af3c069611", "scripts": {"dev": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers chrome --grep", "docs": "pnpm run build && pnpm --filter \"./docs/**\" build", "lint": "concurrently \"pnpm:lint-*\"", "test": "pnpm lint && pnpm test-ci", "build": "tsc --noEmit && rollup -c", "dev:ff": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.{js,ts}\" \"test/**/*.js\" \"docs/**/*.js\"", "lint-md": "eslint \"**/*.md\"", "test-ci": "concurrently \"pnpm:test-ci-*\"", "docs:dev": "pnpm run build && pnpm --filter \"./docs/**\" dev", "autobuild": "rollup -c -w", "test-size": "size-limit", "lint-types": "eslint \"types/**/*.ts\" && pnpm build && node types/tests/autogen.js && tsc -p types/tests/", "test-ci-karma": "cross-env NODE_ENV=test karma start ./karma.conf.cjs --auto-watch --single-run --coverage --grep", "test-ci-integration": "pnpm --filter \"./test/integration/**\" test"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "./dist/chart.umd.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "sideEffects": ["./auto/auto.js", "./dist/chart.umd.js"], "_nodeVersion": "16.18.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.9.0", "readmeFilename": "README.md", "devDependencies": {"esm": "^3.2.25", "glob": "^8.0.3", "karma": "^6.3.2", "luxon": "^3.0.1", "yargs": "^17.5.1", "eslint": "^8.21.0", "moment": "^2.29.4", "rollup": "^2.77.2", "jasmine": "^3.7.0", "@swc/core": "^1.2.224", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "pixelmatch": "^5.3.0", "size-limit": "^8.0.0", "typescript": "^4.7.4", "concurrently": "^7.3.0", "jasmine-core": "^3.7.1", "@kurkle/color": "^0.2.1", "@types/estree": "^1.0.0", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "moment-timezone": "^0.5.34", "eslint-plugin-es": "^4.1.0", "rollup-plugin-dts": "^4.2.2", "chartjs-test-utils": "^0.4.0", "eslint-plugin-html": "^7.1.0", "rollup-plugin-swc3": "^0.3.0", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^4.0.4", "chartjs-adapter-luxon": "^1.2.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "@types/offscreencanvas": "^2019.7.0", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^3.0.0", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^3.0.0", "@rollup/plugin-commonjs": "^22.0.2", "@typescript-eslint/parser": "^5.32.0", "karma-rollup-preprocessor": "7.0.7", "@size-limit/preset-big-lib": "^8.0.0", "@rollup/plugin-node-resolve": "^13.3.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.32.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_4.0.0-release_1668466494968_0.23100940096387612", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "chart.js", "version": "4.0.1", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@4.0.1", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "93d5d50ac222a5b3b6ac7488e82e1553ac031592", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-4.0.1.tgz", "fileCount": 20, "integrity": "sha512-5/8/9eBivwBZK81mKvmIwTb2Pmw4D/5h1RK9fBWZLLZ8mCJ+kfYNmV9rMrGoa5Hgy2/wVDBMLSUDudul2/9ihA==", "signatures": [{"sig": "MEUCIQCFEic2pqIQ336wCOhYsmnR8TWIxjuVFICgCxUOwqL5vgIgVqrh0wxLf8GXADfwbG6hYbNR2q4rP806YoHJhQYWM1c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3155516, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjcs+TACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqbeQ//QjSojEiPsKycXMBuux+n4fRf0igzMxDzlm+WPuvt059ahVGb\r\nlcITBYD/YSooW/6RZLgA9834k8a7jRK2mk2KSKS6PpjUQ2q879ImjeC7qIGX\r\nD8OIEN24yy6SFlW83Sfqwi3bWGpzrSGHxagK2kklHujakEuEKIJB9TVYuQvN\r\nJRMH1bbnvVhHRDzg8qVlb0Cb5fW3FCeE3UFiqkncVDZ7ubUzO7IogFnaK5U5\r\n+1VXIs5UH+gEx2ax0XFlv5bdzvLfUkTSNh8ofJSnKHuSAFn0+33RktpNxRr2\r\nnBB/KBdchiX6AR3MeZ+p7ELbaLoX83641qq4IlxFB7F6rZsWvW20xcMvAu4y\r\nrEyPegfucs6mvvwLLsUNffLnXW2NJlP6AG10OewVKZX4JOCzWefQm7tNDBhq\r\nMVIG+LNEzqzePLU80H0Ch0RLbD1NcS9nd0SFnORO0vQjCyFvhkWlcS/RUzAw\r\nDxZXMpsKGNf05q/EOtNHc2v16Z2+7aPLqeVs8R2BmNMC3HGyOdZ0HfvnxTHi\r\nkl2dah51cNLIg//9jfSpFpm126ug2PYHwbEAFm+pWffSnnyYC4ERc2nkP+V1\r\n9lbHgqeGojxFuAsk2401Nz8RHnh6St5ZVwvZwZMYTi8i8Y2/MPkWnQBPjvk/\r\n0bjASL/tQgUQnRVm8J1TiNcbTLVxGax7qtk=\r\n=VmX0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/chart.js", "pnpm": {"overrides": {"html-entities": "1.4.0"}, "peerDependencyRules": {"ignoreMissing": ["chart.js"]}}, "type": "module", "types": "./dist/types.d.ts", "unpkg": "./dist/chart.umd.js", "engines": {"pnpm": "^7.0.0"}, "exports": {".": {"types": "./dist/types.d.ts", "import": "./dist/chart.js"}, "./auto": {"types": "./auto/auto.d.ts", "import": "./auto/auto.js"}, "./helpers": {"types": "./helpers/helpers.d.ts", "import": "./helpers/helpers.js"}}, "gitHead": "ae1365e1ed64af6c66f43ba1ab06d0b8cf0f0feb", "scripts": {"dev": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers chrome --grep", "docs": "pnpm run build && pnpm --filter \"./docs/**\" build", "lint": "concurrently \"pnpm:lint-*\"", "test": "pnpm lint && pnpm test-ci", "build": "tsc --noEmit && rollup -c", "dev:ff": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.{js,ts}\" \"test/**/*.js\" \"docs/**/*.js\"", "lint-md": "eslint \"**/*.md\"", "test-ci": "concurrently \"pnpm:test-ci-*\"", "docs:dev": "pnpm run build && pnpm --filter \"./docs/**\" dev", "autobuild": "rollup -c -w", "test-size": "size-limit", "lint-types": "eslint \"types/**/*.ts\" && pnpm build && node types/tests/autogen.js && tsc -p types/tests/", "test-ci-karma": "cross-env NODE_ENV=test karma start ./karma.conf.cjs --auto-watch --single-run --coverage --grep", "test-ci-integration": "pnpm --filter \"./test/integration/**\" test"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "./dist/chart.umd.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "sideEffects": ["./auto/auto.js", "./dist/chart.umd.js"], "_nodeVersion": "16.18.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.9.0", "devDependencies": {"esm": "^3.2.25", "glob": "^8.0.3", "karma": "^6.3.2", "luxon": "^3.0.1", "yargs": "^17.5.1", "eslint": "^8.21.0", "moment": "^2.29.4", "rollup": "^2.77.2", "jasmine": "^3.7.0", "@swc/core": "^1.2.224", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "pixelmatch": "^5.3.0", "size-limit": "^8.0.0", "typescript": "^4.7.4", "concurrently": "^7.3.0", "jasmine-core": "^3.7.1", "@kurkle/color": "^0.2.1", "@types/estree": "^1.0.0", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "moment-timezone": "^0.5.34", "eslint-plugin-es": "^4.1.0", "rollup-plugin-dts": "^4.2.2", "chartjs-test-utils": "^0.4.0", "eslint-plugin-html": "^7.1.0", "rollup-plugin-swc3": "^0.3.0", "@rollup/plugin-json": "^4.1.0", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^4.0.4", "chartjs-adapter-luxon": "^1.2.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "@types/offscreencanvas": "^2019.7.0", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^3.0.0", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^3.0.0", "@rollup/plugin-commonjs": "^22.0.2", "@typescript-eslint/parser": "^5.32.0", "karma-rollup-preprocessor": "7.0.7", "@size-limit/preset-big-lib": "^8.0.0", "@rollup/plugin-node-resolve": "^13.3.0", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.32.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_4.0.1_1668468627426_0.49814500518137494", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "chart.js", "version": "4.1.0", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@4.1.0", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "171af17142afdb5d43859676f8aad4751b70024c", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-4.1.0.tgz", "fileCount": 117, "integrity": "sha512-Xta4vonV3e1pwFGLTyUMqXk2aH03D6253DSt52pdS7247u2SpIpQb6kCewxNgx1JfeLBUnjaxHYbHpOv6w78Og==", "signatures": [{"sig": "MEUCIQCVgDVEmDmcfkh6fv0hi7shZMCMhllQLG1D0kponLvAuQIgYDhdAP6uWHLJQ24F5Nh6GhQ9MGf/KVUeC8IH5Qpy7GA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4870133, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjnBYWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoo3hAAguC7TZ1HaD29/nzoIGoXkNRZCsCA6fXNGoT2rniBJ0h5QEAq\r\npJa2J8885JX/vBnclb9viRmRvl4nMcTKONlhjwwGFk8za1+71sWtMKnxLIW6\r\no/tbvYTvvwOc5Yh4SylQVupHxGUrpkw2wdTOyIKegX5gb97oQtJlMTPRuIT0\r\nwGSvc+uQleGL9FA44fDOYgC+p2t0AEz6+EixGLO+BHzDeRIN3nlp7bRG5lcC\r\nA4Dcv4M0cTDs87Hx7eE4ILftdrm8QyWBfbu6koJhy8Df7oekv6E+Z5YOjq50\r\n3Ew+AW2n1TBvUa3qBbhZWH/FWPUihC+WPgQucffLRW1rz7C9a6Lzt0m3jfZJ\r\n+e+S2+HvqaWEYDVqFAyfWf9ezv2cMnzi/jj+PbZ2mHR1Mf2fl7b91DiKo8dJ\r\noI/LuWdEabsoJVFzrg2zpmvaNa/cc/KZEJeP2IAs7kovxP251uYWLuBXkT+q\r\nGjF7haL0xVjUKC84HmNrYvK8EsswWROrPPw/KicSZE6k/FAchm6d97uosc5G\r\nDCCNKZeQ5uDpPXNP0AoA0p61G8L0MSrvoA1qhhdhBUxGPeTo/A+8LhNHkmlv\r\nyG6B4HVwT1Wm1qPTIZLlPSq7VfB6jKffgx3tHJPbOZaxPEaKBIQRcLozZcaP\r\nwhUrHZHmojaoCblBmqJikEiOBCU/fIrrJw4=\r\n=duSu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/chart.js", "pnpm": {"overrides": {"html-entities": "1.4.0"}, "peerDependencyRules": {"ignoreMissing": ["chart.js"]}}, "type": "module", "types": "./dist/types.d.ts", "unpkg": "./dist/chart.umd.js", "engines": {"pnpm": "^7.0.0"}, "exports": {".": {"types": "./dist/types.d.ts", "import": "./dist/chart.js", "require": "./dist/chart.cjs"}, "./auto": {"types": "./auto/auto.d.ts", "import": "./auto/auto.js", "require": "./auto/auto.cjs"}, "./helpers": {"types": "./helpers/helpers.d.ts", "import": "./helpers/helpers.js", "require": "./helpers/helpers.cjs"}}, "gitHead": "ae264e14e797d67af68de90224ae88496223396f", "scripts": {"dev": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers chrome --grep", "docs": "pnpm run build && pnpm --filter \"./docs/**\" build", "lint": "concurrently \"pnpm:lint-*\"", "test": "pnpm lint && pnpm test-ci", "build": "rollup -c && pnpm emitDeclarations", "dev:ff": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.{js,ts}\" \"test/**/*.js\" \"docs/**/*.js\" --cache", "lint-md": "eslint \"**/*.md\" --cache", "test-ci": "concurrently \"pnpm:test-ci-*\"", "docs:dev": "pnpm run build && pnpm --filter \"./docs/**\" dev", "autobuild": "rollup -c -w", "test-size": "size-limit", "lint-types": "eslint \"types/**/*.ts\" --cache && pnpm build && node types/tests/autogen.js && tsc -p types/tests/", "test-ci-karma": "cross-env NODE_ENV=test karma start ./karma.conf.cjs --auto-watch --single-run --coverage --grep", "emitDeclarations": "tsc --emitDeclarationOnly", "test-ci-integration": "pnpm --filter \"./test/integration/**\" test"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "./dist/chart.umd.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "sideEffects": ["./auto/auto.js", "./dist/chart.umd.js"], "_nodeVersion": "16.18.1", "dependencies": {"@kurkle/color": "^0.3.0"}, "_hasShrinkwrap": false, "packageManager": "pnpm@7.9.0", "devDependencies": {"esm": "^3.2.25", "glob": "^8.0.3", "karma": "^6.3.2", "luxon": "^3.0.1", "yargs": "^17.5.1", "eslint": "^8.21.0", "moment": "^2.29.4", "rollup": "^3.3.0", "jasmine": "^3.7.0", "@swc/core": "^1.3.18", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "pixelmatch": "^5.3.0", "size-limit": "^8.0.0", "typescript": "^4.7.4", "concurrently": "^7.3.0", "jasmine-core": "^3.7.1", "@types/estree": "^1.0.0", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "moment-timezone": "^0.5.34", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.4.0", "eslint-plugin-html": "^7.1.0", "rollup-plugin-swc3": "^0.7.0", "@rollup/plugin-json": "^5.0.1", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^5.0.2", "chartjs-adapter-luxon": "^1.2.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "@types/offscreencanvas": "^2019.7.0", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^3.0.0", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^4.0.0", "@rollup/plugin-commonjs": "^23.0.2", "@typescript-eslint/parser": "^5.32.0", "karma-rollup-preprocessor": "7.0.7", "@size-limit/preset-big-lib": "^8.0.0", "@rollup/plugin-node-resolve": "^15.0.1", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.32.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_4.1.0_1671173653754_0.7182362528355803", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "chart.js", "version": "4.1.1", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@4.1.1", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "9bd96ddaa444205ec55dd103f713f65530e9a2df", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-4.1.1.tgz", "fileCount": 127, "integrity": "sha512-P0pCosNXp+LR8zO/QTkZKT6Hb7p0DPFtypEeVOf+6x06hX13NIb75R0DXUA4Ksx/+48chDQKtCCmRCviQRTqsA==", "signatures": [{"sig": "MEYCIQCT+4haXlKB07tvdKCuzjz2SyFa38Z+dT10Q5iHsGBWDQIhALZKtVwdVZadwqBacWTGj2J3CD3I4A+2ekia6STCAq4S", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4990162, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjnetpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqDpw/+Nspl58l/wBNx0RPaqsTQ9A0rTra9521/icPTkGgXJ52iix77\r\n8bNrBGLPkX9y/6wJ+HfQLYP2RUi45CRPFgYm+oqsaxYc8Xqh5SIRZIKWd5e4\r\nfV447hlig5/Ks5ILZlVqlWIvPS8niVnKtE7+ks4AxKLS5Sie+BSsqwUiol4b\r\n9W6y8zdEOnTbvteyl1cYQbdaRmBdI93q5BGdkTtOAgoAnkMkQovJAvds9eo4\r\nLA/Fd/zZ9Uq4mSq1aOYJ5c7BLlwspjhbOvNlbJOEgwn61ivy6NkqZ6/lXGKj\r\nHZjYwbbgktVmptKLPODwWWw+oeiiCkAjd3Azijg0k9/DWqpCDan3DaJvjPiS\r\nvzj8kpNIEUeipuHQe3lVi1FSmb14QVEFMgizjpoAf0aIkSbyLDXn6gU4Injv\r\n2ZOHVwVuS8rilD2Q/dYF+vX1wUJdnjPpR5AlxuPfopIPJU1uZPJksXYWtvu0\r\nXAYOnW/heUNM0Zneu0dlITNuanDJg8U8PZhd53rsm52GDzB8DiqcZnzu1/Ct\r\nzn2y+LTxBxgJyyFz63+1yhGfTonDYyDRplf2LcgveAzjLpndQfF/6I40xWLx\r\nKdsAC9wed5faOEzhsuOoSNTVMVxAH1vaPzh/QZ4UusWP0m6kdTAMvjgsrtnR\r\nwtSpbR3erDabDmbJ7qj8CUkDLveVs5OSq/I=\r\n=I3qd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/chart.js", "pnpm": {"overrides": {"html-entities": "1.4.0"}, "peerDependencyRules": {"ignoreMissing": ["chart.js"]}}, "type": "module", "types": "./dist/types.d.ts", "unpkg": "./dist/chart.umd.js", "engines": {"pnpm": "^7.0.0"}, "exports": {".": {"types": "./dist/types.d.ts", "import": "./dist/chart.js", "require": "./dist/chart.cjs"}, "./auto": {"types": "./auto/auto.d.ts", "import": "./auto/auto.js", "require": "./auto/auto.cjs"}, "./helpers": {"types": "./helpers/helpers.d.ts", "import": "./helpers/helpers.js", "require": "./helpers/helpers.cjs"}}, "gitHead": "810ceb7741fb2360ba21ea3b9c7fd2af0ce615ae", "scripts": {"dev": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers chrome --grep", "docs": "pnpm run build && pnpm --filter \"./docs/**\" build", "lint": "concurrently \"pnpm:lint-*\"", "test": "pnpm lint && pnpm test-ci", "build": "rollup -c && pnpm emitDeclarations", "dev:ff": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.{js,ts}\" \"test/**/*.js\" \"docs/**/*.js\" --cache", "lint-md": "eslint \"**/*.md\" --cache", "test-ci": "concurrently \"pnpm:test-ci-*\"", "docs:dev": "pnpm run build && pnpm --filter \"./docs/**\" dev", "autobuild": "rollup -c -w", "test-size": "size-limit", "lint-types": "pnpm build && node test/types/autogen.js && tsc -p test/types", "test-ci-karma": "cross-env NODE_ENV=test karma start ./karma.conf.cjs --auto-watch --single-run --coverage --grep", "emitDeclarations": "tsc --emitDeclarationOnly && cp -R ./src/types/ ./dist/types/", "test-ci-integration": "pnpm --filter \"./test/integration/**\" test"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "./dist/chart.umd.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "sideEffects": ["./auto/auto.js", "./auto/auto.cjs", "./dist/chart.umd.js"], "_nodeVersion": "16.18.1", "dependencies": {"@kurkle/color": "^0.3.0"}, "_hasShrinkwrap": false, "packageManager": "pnpm@7.9.0", "devDependencies": {"esm": "^3.2.25", "glob": "^8.0.3", "karma": "^6.3.2", "luxon": "^3.0.1", "yargs": "^17.5.1", "eslint": "^8.21.0", "moment": "^2.29.4", "rollup": "^3.3.0", "jasmine": "^3.7.0", "@swc/core": "^1.3.18", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "pixelmatch": "^5.3.0", "size-limit": "^8.0.0", "typescript": "^4.7.4", "concurrently": "^7.3.0", "jasmine-core": "^3.7.1", "@types/estree": "^1.0.0", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "moment-timezone": "^0.5.34", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.4.0", "eslint-plugin-html": "^7.1.0", "rollup-plugin-swc3": "^0.7.0", "@rollup/plugin-json": "^5.0.1", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^5.0.2", "chartjs-adapter-luxon": "^1.2.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "@types/offscreencanvas": "^2019.7.0", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^3.0.0", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^4.0.0", "@rollup/plugin-commonjs": "^23.0.2", "@typescript-eslint/parser": "^5.32.0", "karma-rollup-preprocessor": "7.0.7", "@size-limit/preset-big-lib": "^8.0.0", "@rollup/plugin-node-resolve": "^15.0.1", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.32.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_4.1.1_1671293801005_0.9982811508096032", "host": "s3://npm-registry-packages"}}, "4.1.2": {"name": "chart.js", "version": "4.1.2", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@4.1.2", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "04e3ba63dbc36a2af21caf46ae31940514cb644a", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-4.1.2.tgz", "fileCount": 117, "integrity": "sha512-9L1w6WLPq6ztiWVVOYtDtpo0CUsBKDWPrUEdwChAyzczaikqeSwNKEv3QpJ7EO4ICcLSi6UDVhgvcnUhRJidRA==", "signatures": [{"sig": "MEQCIHGGS3gasjd79F3QOHrqACMgqiznxEf6vvmE+Uz+DKj1AiBfuFiZzmhAeqy1K/keYLaN5bGHJbeNbo8T/P1CGxsvrQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4869454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtvcjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqUSw//TF5OsYvWsYq38nZB0ctm97LY+McuyfmR2lymSVswuSgcnK0F\r\nCWtF8zqbKpOYjbNH5//zHZWJrZZ2T78BV6gXZrIhKddKdj+CoqAEx+kbbRbu\r\nasuHo7EtA7TPQ94F4/eOjCvoeDVVAqVFi3rO55pj/FL8GLgM0JCqo3N9e0E5\r\nU83auSxJ8YoSv69bLEQ7aYUC+KVZwFmk40HoOSDiTLnl1CGytSPxMZpIUvH3\r\noGo0sYDyLLQ4RrS7xhaCeaRsXbzi27WY4klV8bP/THZS4k07kzO2KTTDrusz\r\nYwnFypcZYJwoiVlKpVunyBz+dUDR91FQr1QujSzZARyZChQgXdcx+NRnuOI9\r\nAWuGfHQVck0OnfqDSRNsrbpu5cCxcFG0fNPPQW7BMSAiMm6AKc207Q8TL4ao\r\nbs+bnnhhk27zOxMJFoXDbElxVFiVyXuaxFEJj+y4MRdgGjKp2249ofbQ2HFS\r\n9MNxRc98Fg9ovHpO6zavgEOauE8y8N4HSMTUBpAbuQ7KzV2V0uRUZsJVWmzd\r\ncKmJcbKIfdSKPL11WAJne2FjOVvs9ujGIJqrxIp67EfE8/0lZ3/Jy0fDqZVX\r\nFJu57hNT0PVJn4itjWxx89m8KQg/8k9cNo+O3V0Qkllj+B6lIra9KqCfg5F0\r\nP5uc+HyFL27AtyJDC/tRE/lhyrB00CpEhYQ=\r\n=+tKC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/chart.cjs", "pnpm": {"overrides": {"html-entities": "1.4.0"}, "peerDependencyRules": {"ignoreMissing": ["chart.js"]}}, "type": "module", "types": "./dist/types.d.ts", "unpkg": "./dist/chart.umd.js", "engines": {"pnpm": "^7.0.0"}, "exports": {".": {"types": "./dist/types.d.ts", "import": "./dist/chart.js", "require": "./dist/chart.cjs"}, "./auto": {"types": "./auto/auto.d.ts", "import": "./auto/auto.js", "require": "./auto/auto.cjs"}, "./helpers": {"types": "./helpers/helpers.d.ts", "import": "./helpers/helpers.js", "require": "./helpers/helpers.cjs"}}, "gitHead": "b51c52336624a24fe1ec814dc47f9a4cb7dd6a8a", "scripts": {"dev": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers chrome --grep", "docs": "pnpm run build && pnpm --filter \"./docs/**\" build", "lint": "concurrently \"pnpm:lint-*\"", "test": "pnpm lint && pnpm test-ci", "build": "rollup -c && pnpm emitDeclarations", "dev:ff": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.{js,ts}\" \"test/**/*.js\" \"docs/**/*.js\" --cache", "lint-md": "eslint \"**/*.md\" --cache", "test-ci": "concurrently \"pnpm:test-ci-*\"", "docs:dev": "pnpm run build && pnpm --filter \"./docs/**\" dev", "autobuild": "rollup -c -w", "test-size": "size-limit", "lint-types": "pnpm build && node test/types/autogen.js && tsc -p test/types", "test-ci-karma": "cross-env NODE_ENV=test karma start ./karma.conf.cjs --auto-watch --single-run --coverage --grep", "copyDeclarations": "node -e \"fs.cpSync('./src/types/', './dist/types/', {recursive:true})\"", "emitDeclarations": "tsc --emitDeclarationOnly && pnpm copyDeclarations", "test-ci-integration": "pnpm --filter \"./test/integration/**\" test"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "./dist/chart.umd.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "sideEffects": ["./auto/auto.js", "./auto/auto.cjs", "./dist/chart.umd.js"], "_nodeVersion": "16.18.1", "dependencies": {"@kurkle/color": "^0.3.0"}, "_hasShrinkwrap": false, "packageManager": "pnpm@7.9.0", "devDependencies": {"esm": "^3.2.25", "glob": "^8.0.3", "karma": "^6.3.2", "luxon": "^3.0.1", "yargs": "^17.5.1", "eslint": "^8.21.0", "moment": "^2.29.4", "rollup": "^3.3.0", "jasmine": "^3.7.0", "@swc/core": "^1.3.18", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "pixelmatch": "^5.3.0", "size-limit": "^8.0.0", "typescript": "^4.7.4", "concurrently": "^7.3.0", "jasmine-core": "^3.7.1", "@types/estree": "^1.0.0", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "moment-timezone": "^0.5.34", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.4.0", "eslint-plugin-html": "^7.1.0", "rollup-plugin-swc3": "^0.7.0", "@rollup/plugin-json": "^5.0.1", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^5.0.2", "chartjs-adapter-luxon": "^1.2.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "@types/offscreencanvas": "^2019.7.0", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^3.0.0", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^4.0.0", "@rollup/plugin-commonjs": "^23.0.2", "@typescript-eslint/parser": "^5.32.0", "karma-rollup-preprocessor": "7.0.7", "@size-limit/preset-big-lib": "^8.0.0", "@rollup/plugin-node-resolve": "^15.0.1", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.32.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_4.1.2_1672935202818_0.285070718551925", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "chart.js", "version": "4.2.0", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@4.2.0", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "dd281b2ce890bff32f3e249cf2972a1e74bc032c", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-4.2.0.tgz", "fileCount": 117, "integrity": "sha512-wbtcV+QKeH0F7gQZaCJEIpsNriFheacouJQTVIjITi3eQA8bTlIBoknz0+dgV79aeKLNMAX+nDslIVE/nJ3rzA==", "signatures": [{"sig": "MEUCIQDFQXOQshO0iP2FWgAyKtfgMS1/U8j+7CEtjzL2G2ZZrwIgawICpNVjR990sW3XTMQyHC1r+hRjwdMxivxfqL6s6do=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4874473, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjx/riACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpQdg//T4XeGx6Te4ctRVWu/6ph89ewD9dXv3atJwcpLC3VciW0xtcF\r\nAHfajxXO6XUr2E0jekgMwHB1B0EqkIltEtwPSa99ddmjEyJI6BYShvAe1feM\r\nGQKYZuBCIcF1V8KT0tcQ8hBHrT4NGUnx9sLn7Ah1C5XTdXLB0gj0M45UekaH\r\nkAJcHYX13z8lHzRIdWS3QxhmOjIZ64Tw6uHfS1ZO9RoqDkqpnZI09GwaovPt\r\nyUXavorJMetCqpAuUMt1zdUX4bO+omlkbrQV5mrlNCMayQQuYPT4Z09i2r+Q\r\nQpJuaWiFqml1yQ6tiF/IpmqMkVBYI/IOnN+6AOJ3Ao4Ld/HQddFBwAL3Gu27\r\njcxKR7tT93X1CR9vo5dYpbsr1ccBud6y03bqCVbJy7/tmLka/8LOGeEwSYxO\r\n+bEgX06gSmJAcXBWAQDZKU8w0VddMX6L7Dimxkl+xMs2SZolhz7UHgFOM0Lv\r\ne6dTGV7bJpCwQavwG+7OZJBn0lE4Ef63JN7IAOa8skYuCJFdmqwmF3voSHg/\r\nB3Sk29saHU+4fJ9ph8f1RgELBlpZXuViYLLb1NJ5hxXAOeS0lgnmKg2AeSaw\r\ne01gvxYVIYEA5OYg6Ye+W8JG7qXQMsGYfaFWoyAq48agJhmu8c3OQntFtXND\r\nAJQme+aQqEIj5BM/fQuyXPqIdwNWav5ZDdU=\r\n=Aeoi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/chart.cjs", "pnpm": {"overrides": {"html-entities": "1.4.0"}, "peerDependencyRules": {"ignoreMissing": ["chart.js"]}}, "type": "module", "types": "./dist/types.d.ts", "unpkg": "./dist/chart.umd.js", "module": "./dist/chart.js", "engines": {"pnpm": "^7.0.0"}, "exports": {".": {"types": "./dist/types.d.ts", "import": "./dist/chart.js", "require": "./dist/chart.cjs"}, "./auto": {"types": "./auto/auto.d.ts", "import": "./auto/auto.js", "require": "./auto/auto.cjs"}, "./helpers": {"types": "./helpers/helpers.d.ts", "import": "./helpers/helpers.js", "require": "./helpers/helpers.cjs"}}, "gitHead": "1d7dfadbadd03ad4b2accadb63201ddf22f9a353", "scripts": {"dev": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers chrome --grep", "docs": "pnpm run build && pnpm --filter \"./docs/**\" build", "lint": "concurrently \"pnpm:lint-*\"", "test": "pnpm lint && pnpm test-ci", "build": "rollup -c && pnpm emitDeclarations", "dev:ff": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.{js,ts}\" \"test/**/*.js\" \"docs/**/*.js\" --cache", "lint-md": "eslint \"**/*.md\" --cache", "test-ci": "concurrently \"pnpm:test-ci-*\"", "docs:dev": "pnpm run build && pnpm --filter \"./docs/**\" dev", "autobuild": "rollup -c -w", "test-size": "size-limit", "lint-types": "pnpm build && node test/types/autogen.js && tsc -p test/types", "test-ci-karma": "cross-env NODE_ENV=test karma start ./karma.conf.cjs --auto-watch --single-run --coverage --grep", "copyDeclarations": "node -e \"fs.cpSync('./src/types/', './dist/types/', {recursive:true})\"", "emitDeclarations": "tsc --emitDeclarationOnly && pnpm copyDeclarations", "test-ci-integration": "pnpm --filter \"./test/integration/**\" test"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "./dist/chart.umd.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "sideEffects": ["./auto/auto.js", "./auto/auto.cjs", "./dist/chart.umd.js"], "_nodeVersion": "16.19.0", "dependencies": {"@kurkle/color": "^0.3.0"}, "_hasShrinkwrap": false, "packageManager": "pnpm@7.9.0", "devDependencies": {"esm": "^3.2.25", "glob": "^8.0.3", "karma": "^6.3.2", "luxon": "^3.0.1", "yargs": "^17.5.1", "eslint": "^8.21.0", "moment": "^2.29.4", "rollup": "^3.3.0", "jasmine": "^3.7.0", "@swc/core": "^1.3.18", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "pixelmatch": "^5.3.0", "size-limit": "^8.0.0", "typescript": "^4.7.4", "concurrently": "^7.3.0", "jasmine-core": "^3.7.1", "@types/estree": "^1.0.0", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "moment-timezone": "^0.5.34", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.4.0", "eslint-plugin-html": "^7.1.0", "rollup-plugin-swc3": "^0.7.0", "@rollup/plugin-json": "^5.0.1", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^5.0.2", "chartjs-adapter-luxon": "^1.2.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "@types/offscreencanvas": "^2019.7.0", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^3.0.0", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^4.0.0", "@rollup/plugin-commonjs": "^23.0.2", "@typescript-eslint/parser": "^5.32.0", "karma-rollup-preprocessor": "7.0.7", "@size-limit/preset-big-lib": "^8.0.0", "@rollup/plugin-node-resolve": "^15.0.1", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.32.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_4.2.0_1674050273709_0.9604451329300874", "host": "s3://npm-registry-packages"}}, "4.2.1": {"name": "chart.js", "version": "4.2.1", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@4.2.1", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "d2bd5c98e9a0ae35408975b638f40513b067ba1d", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-4.2.1.tgz", "fileCount": 117, "integrity": "sha512-6YbpQ0nt3NovAgOzbkSSeeAQu/3za1319dPUQTXn9WcOpywM8rGKxJHrhS8V8xEkAlk8YhEfjbuAPfUyp6jIsw==", "signatures": [{"sig": "MEUCIEvoHmrs/T9GlxwqNJp2usMDwbWOiLlm6Ly6tjmUWyduAiEAzjE/ub7bCdhz2DHnjBi7ORjCiFD3Rr0OgF4l3kery1g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4877393, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5ek2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpZ/A//VrM+fXusmgtAMYOmZZEtPwWozGUgyomt5840r/XIYQ8PzvsO\r\nM0uJpHq+rpI+I1dNGZjsltUazZolYF5Ty0FE246TAokBCkWa4iS6FA+8hBZ+\r\nzXCSuIF0xlKL/KQzFNXa3FxbAQFtJxe69GzVtZ5KF/FEQleetOGxr2nEaiIM\r\n3pQC4uN75ek2N8uVKjAjFDNGVksyarzSVJy3Jf/ehoxwSjXrl7TBSvOIvJc6\r\nYqFPQ0sEJtobLHL+/llhZZEe46Yr+0cSnkLppqWIUZLpl5eEVhaQrn+roD9s\r\nJfghZ2hti703rvy8Nf0zBqFw3tI3jgXnE2dCX0zTyYPK3CEYwfrUlNv6oyPH\r\nGtWrJ1Lz1HxZR5OfIuwvWlLTkTPHqrSnqxze5D0Q9ThVpRXFXk3yzhW/Fo2L\r\nacL+Gk3Ok2cQ+qEn+JK47G7IB4j2zn0TT4gexJZMI+QO3abghe92Fg/dCKT5\r\nTuJkVcpKV8ShlyBG60Jwg62TneLe5s8xNPGsNH3Xw/56WKwSwZ8Xc7ZL5che\r\nOi4rwtX5RpvKevYymo9ejsBVmZOuxEdPclUGE+Nzs5CDLSlRcHWTFimH19ZY\r\n12iO8QLC2RprBdfDs0eqodOhTPb8s380fuDR9ZgGPncDVGs49hHY5ZaxhvoG\r\nIbTgCqmD2doAPraHoFJOeFg86X6brg3cDQg=\r\n=7GUy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/chart.cjs", "pnpm": {"overrides": {"html-entities": "1.4.0"}, "peerDependencyRules": {"ignoreMissing": ["chart.js"]}}, "type": "module", "types": "./dist/types.d.ts", "unpkg": "./dist/chart.umd.js", "module": "./dist/chart.js", "engines": {"pnpm": "^7.0.0"}, "exports": {".": {"types": "./dist/types.d.ts", "import": "./dist/chart.js", "require": "./dist/chart.cjs"}, "./auto": {"types": "./auto/auto.d.ts", "import": "./auto/auto.js", "require": "./auto/auto.cjs"}, "./helpers": {"types": "./helpers/helpers.d.ts", "import": "./helpers/helpers.js", "require": "./helpers/helpers.cjs"}}, "gitHead": "cfefb3d47fe9e99a591d02c359f8492fc9bacf17", "scripts": {"dev": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers chrome --grep", "docs": "pnpm run build && pnpm --filter \"./docs/**\" build", "lint": "concurrently \"pnpm:lint-*\"", "test": "pnpm lint && pnpm test-ci", "build": "rollup -c && pnpm emitDeclarations", "dev:ff": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.{js,ts}\" \"test/**/*.js\" \"docs/**/*.js\" --cache", "lint-md": "eslint \"**/*.md\" --cache", "test-ci": "concurrently \"pnpm:test-ci-*\"", "docs:dev": "pnpm run build && pnpm --filter \"./docs/**\" dev", "autobuild": "rollup -c -w", "test-size": "size-limit", "lint-types": "pnpm build && node test/types/autogen.js && tsc -p test/types", "test-ci-karma": "cross-env NODE_ENV=test karma start ./karma.conf.cjs --auto-watch --single-run --coverage --grep", "copyDeclarations": "node -e \"fs.cpSync('./src/types/', './dist/types/', {recursive:true})\"", "emitDeclarations": "tsc --emitDeclarationOnly && pnpm copyDeclarations", "test-ci-integration": "pnpm --filter \"./test/integration/**\" test"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "./dist/chart.umd.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "sideEffects": ["./auto/auto.js", "./auto/auto.cjs", "./dist/chart.umd.js"], "_nodeVersion": "16.19.0", "dependencies": {"@kurkle/color": "^0.3.0"}, "_hasShrinkwrap": false, "packageManager": "pnpm@7.9.0", "devDependencies": {"esm": "^3.2.25", "glob": "^8.0.3", "karma": "^6.3.2", "luxon": "^3.0.1", "yargs": "^17.5.1", "eslint": "^8.21.0", "moment": "^2.29.4", "rollup": "^3.3.0", "jasmine": "^3.7.0", "@swc/core": "^1.3.18", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "pixelmatch": "^5.3.0", "size-limit": "^8.0.0", "typescript": "^4.7.4", "concurrently": "^7.3.0", "jasmine-core": "^3.7.1", "@types/estree": "^1.0.0", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "moment-timezone": "^0.5.34", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.4.0", "eslint-plugin-html": "^7.1.0", "rollup-plugin-swc3": "^0.7.0", "@rollup/plugin-json": "^5.0.1", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^5.0.2", "chartjs-adapter-luxon": "^1.2.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "@types/offscreencanvas": "^2019.7.0", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^3.0.0", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^4.0.0", "@rollup/plugin-commonjs": "^23.0.2", "@typescript-eslint/parser": "^5.32.0", "karma-rollup-preprocessor": "7.0.7", "@size-limit/preset-big-lib": "^8.0.0", "@rollup/plugin-node-resolve": "^15.0.1", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.32.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_4.2.1_1676011830149_0.642469179387388", "host": "s3://npm-registry-packages"}}, "4.3.0": {"name": "chart.js", "version": "4.3.0", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@4.3.0", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "ac363030ab3fec572850d2d872956f32a46326a1", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-4.3.0.tgz", "fileCount": 114, "integrity": "sha512-ynG0E79xGfMaV2xAHdbhwiPLczxnNNnasrmPEXriXsPJGjmhOBYzFVEsB65w2qMDz+CaBJJuJD0inE/ab/h36g==", "signatures": [{"sig": "MEQCIHnYX2emxKTic+Cbuyb7vI7o76Enrm92IqN9TE1Z/u2JAiBwboItOrQ9Ma5hcsDYhy+9Q/aznesxSAY3Q+n9UoRbHg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4912380, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkS2Z1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr3IQ//QzjywUtemySy2EHyarGX9wUSRHC7bwz+PdgfJH+Mebfd1x6K\r\nm5xrA1sBGW/n/YGsYDx3pTKSJwmDK9mtQ8JsxloLNwQjDX334q2e/w8XeR88\r\nPD+bZCB/qqj5TYIAbSVphidE8do6NoCN86NSSXwUQOSihDUKtowhQQhCy6gE\r\n9JSnYTdbx6Cy1QA2yKlLLuJTu8mYpRc+nR3S2FY4Bmn7D0X5J2ZwlHdNQPcX\r\nBmzPVpZZMJsGmehycN4nWSdL08HtabfivQfH+IMVZ1mjF5HQ3gcHndwqcFxv\r\n2fkyI+IxKvPLqjOW88dx7cUdX2vBI7Oue6lo1Di2/389+KG48GkE8bU6ax6P\r\nqZuq5m9rmpGNvHrMgbwEjZwkuWb0ohuHZw4NCfEbY2yMD7xRxks2kmbisUIC\r\nYQKB2J+3s8NWs40hY9Zt2VM+JjYaFFKwtbHNKpjkmwnLSNVTfj59Oyf8s5sT\r\nux0iGi70jO8xknv0dgyTCw4YHFHkucvxB54npMKBcWWE3agjQkvqfenxVszC\r\nUeU8SJHvXE/84J9lfq+iuU4DrPhZp/SOTo1FlAR9inz67E9T3U9tobrYjP+U\r\nPCOhuAKV4kEcqloxK/2gSj0edqX1+EL1bDT8MnYE24ItGk5+jjXMLt7RvEGm\r\nCemfOCZaeem7sC9eWdszfD69Ub3EUcWiHWk=\r\n=K8bu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/chart.cjs", "pnpm": {"overrides": {"html-entities": "1.4.0"}, "peerDependencyRules": {"ignoreMissing": ["chart.js"]}}, "type": "module", "types": "./dist/types.d.ts", "unpkg": "./dist/chart.umd.js", "module": "./dist/chart.js", "engines": {"pnpm": ">=7"}, "exports": {".": {"types": "./dist/types.d.ts", "import": "./dist/chart.js", "require": "./dist/chart.cjs"}, "./auto": {"types": "./auto/auto.d.ts", "import": "./auto/auto.js", "require": "./auto/auto.cjs"}, "./helpers": {"types": "./helpers/helpers.d.ts", "import": "./helpers/helpers.js", "require": "./helpers/helpers.cjs"}}, "gitHead": "83ba8fd77689645d0f915a6c8b4881b882be5276", "scripts": {"dev": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers chrome --grep", "docs": "pnpm run build && pnpm --filter \"./docs/**\" build", "lint": "concurrently \"pnpm:lint-*\"", "test": "pnpm lint && pnpm test-ci", "build": "rollup -c && pnpm emitDeclarations", "dev:ff": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.{js,ts}\" \"test/**/*.js\" \"docs/**/*.js\" --cache", "lint-md": "eslint \"**/*.md\" --cache", "test-ci": "concurrently \"pnpm:test-ci-*\"", "docs:dev": "pnpm run build && pnpm --filter \"./docs/**\" dev", "autobuild": "rollup -c -w", "test-size": "size-limit", "lint-types": "pnpm build && node test/types/autogen.js && tsc -p test/types", "test-ci-karma": "cross-env NODE_ENV=test karma start ./karma.conf.cjs --auto-watch --single-run --coverage --grep", "copyDeclarations": "node -e \"fs.cpSync('./src/types/', './dist/types/', {recursive:true})\"", "emitDeclarations": "tsc --emitDeclarationOnly && pnpm copyDeclarations", "test-ci-integration": "pnpm --filter \"./test/integration/**\" test"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "./dist/chart.umd.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "sideEffects": ["./auto/auto.js", "./auto/auto.cjs", "./dist/chart.umd.js"], "_nodeVersion": "16.20.0", "dependencies": {"@kurkle/color": "^0.3.0"}, "_hasShrinkwrap": false, "packageManager": "pnpm@7.9.0", "devDependencies": {"esm": "^3.2.25", "glob": "^8.0.3", "karma": "^6.3.2", "luxon": "^3.0.1", "yargs": "^17.5.1", "eslint": "^8.21.0", "moment": "^2.29.4", "rollup": "^3.3.0", "jasmine": "^3.7.0", "@swc/core": "^1.3.18", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "pixelmatch": "^5.3.0", "size-limit": "^8.0.0", "typescript": "^4.7.4", "concurrently": "^7.3.0", "jasmine-core": "^3.7.1", "@types/estree": "^1.0.0", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "moment-timezone": "^0.5.34", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.4.0", "eslint-plugin-html": "^7.1.0", "rollup-plugin-swc3": "^0.7.0", "@rollup/plugin-json": "^5.0.1", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^5.0.2", "chartjs-adapter-luxon": "^1.2.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "@types/offscreencanvas": "^2019.7.0", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^3.0.0", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^4.0.0", "@rollup/plugin-commonjs": "^23.0.2", "@typescript-eslint/parser": "^5.32.0", "karma-rollup-preprocessor": "7.0.7", "@size-limit/preset-big-lib": "^8.0.0", "@rollup/plugin-node-resolve": "^15.0.1", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.32.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_4.3.0_1682663029135_0.6736958989925237", "host": "s3://npm-registry-packages"}}, "4.3.1": {"name": "chart.js", "version": "4.3.1", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@4.3.1", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "b83ea014ff9f45e43bfd98ef2bdaba2a9aed8a16", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-4.3.1.tgz", "fileCount": 114, "integrity": "sha512-QHuISG3hTJ0ftq0I0f5jqH9mNVO9bqG8P+zvMOVslgKajQVvFEX7QAhYNJ+QEmw+uYTwo8XpTimaB82oeTWjxw==", "signatures": [{"sig": "MEYCIQCWSaRuNI54ZlMsB9pU5oJ9RGj+31fo47wW9polcpJv5QIhAPl1mxqnC8gf1+IysVGf91zwNdBr9OZFAAYPAP3V3Smt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4920933}, "main": "./dist/chart.cjs", "pnpm": {"overrides": {"html-entities": "1.4.0"}, "peerDependencyRules": {"ignoreMissing": ["chart.js"]}}, "type": "module", "types": "./dist/types.d.ts", "unpkg": "./dist/chart.umd.js", "module": "./dist/chart.js", "engines": {"pnpm": ">=7"}, "exports": {".": {"types": "./dist/types.d.ts", "import": "./dist/chart.js", "require": "./dist/chart.cjs"}, "./auto": {"types": "./auto/auto.d.ts", "import": "./auto/auto.js", "require": "./auto/auto.cjs"}, "./helpers": {"types": "./helpers/helpers.d.ts", "import": "./helpers/helpers.js", "require": "./helpers/helpers.cjs"}}, "gitHead": "e7b8164fcc07ee591e6c261959185912fdddf5d7", "scripts": {"dev": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers chrome --grep", "docs": "pnpm run build && pnpm --filter \"./docs/**\" build", "lint": "concurrently \"pnpm:lint-*\"", "test": "pnpm lint && pnpm test-ci", "build": "rollup -c && pnpm emitDeclarations", "dev:ff": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.{js,ts}\" \"test/**/*.js\" \"docs/**/*.js\" --cache", "lint-md": "eslint \"**/*.md\" --cache", "test-ci": "concurrently \"pnpm:test-ci-*\"", "docs:dev": "pnpm run build && pnpm --filter \"./docs/**\" dev", "autobuild": "rollup -c -w", "test-size": "size-limit", "lint-types": "pnpm build && node test/types/autogen.js && tsc -p test/types", "test-ci-karma": "cross-env NODE_ENV=test karma start ./karma.conf.cjs --auto-watch --single-run --coverage --grep", "copyDeclarations": "node -e \"fs.cpSync('./src/types/', './dist/types/', {recursive:true})\"", "emitDeclarations": "tsc --emitDeclarationOnly && pnpm copyDeclarations", "test-ci-integration": "pnpm --filter \"./test/integration/**\" test"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "./dist/chart.umd.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "sideEffects": ["./auto/auto.js", "./auto/auto.cjs", "./dist/chart.umd.js"], "_nodeVersion": "16.20.1", "dependencies": {"@kurkle/color": "^0.3.0"}, "_hasShrinkwrap": false, "packageManager": "pnpm@7.9.0", "devDependencies": {"esm": "^3.2.25", "glob": "^8.0.3", "karma": "^6.3.2", "luxon": "^3.0.1", "yargs": "^17.5.1", "eslint": "^8.21.0", "moment": "^2.29.4", "rollup": "^3.3.0", "jasmine": "^3.7.0", "@swc/core": "^1.3.18", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "pixelmatch": "^5.3.0", "size-limit": "^8.0.0", "typescript": "^4.7.4", "concurrently": "^7.3.0", "jasmine-core": "^3.7.1", "@types/estree": "^1.0.0", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "moment-timezone": "^0.5.34", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.4.0", "eslint-plugin-html": "^7.1.0", "rollup-plugin-swc3": "^0.7.0", "@rollup/plugin-json": "^5.0.1", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^5.0.2", "chartjs-adapter-luxon": "^1.2.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "@types/offscreencanvas": "^2019.7.0", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^3.0.0", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^4.0.0", "@rollup/plugin-commonjs": "^23.0.2", "@typescript-eslint/parser": "^5.32.0", "karma-rollup-preprocessor": "7.0.7", "@size-limit/preset-big-lib": "^8.0.0", "@rollup/plugin-node-resolve": "^15.0.1", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.32.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_4.3.1_1690228199253_0.2735972794716577", "host": "s3://npm-registry-packages"}}, "4.3.2": {"name": "chart.js", "version": "4.3.2", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@4.3.2", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "904ebe0376eb368a659a92d2050df47336847e4c", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-4.3.2.tgz", "fileCount": 114, "integrity": "sha512-pvQNyFOY1QmbmIr8oDORL16/FFivfxj8V26VFpFilMo4cNvkV5WXLJetDio365pd9gKUHGdirUTbqJfw8tr+Dg==", "signatures": [{"sig": "MEQCIH39TQHtQotJQSjXkPLiPiCSFYm1KFseAwLCN1geusmfAiB7u0XEx4acwlgB2jr6GSuXwMe4XmeWCmLuogF/AYP/NA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4921299}, "main": "./dist/chart.cjs", "pnpm": {"overrides": {"html-entities": "1.4.0"}, "peerDependencyRules": {"ignoreMissing": ["chart.js"]}}, "type": "module", "types": "./dist/types.d.ts", "unpkg": "./dist/chart.umd.js", "module": "./dist/chart.js", "engines": {"pnpm": ">=7"}, "exports": {".": {"types": "./dist/types.d.ts", "import": "./dist/chart.js", "require": "./dist/chart.cjs"}, "./auto": {"types": "./auto/auto.d.ts", "import": "./auto/auto.js", "require": "./auto/auto.cjs"}, "./helpers": {"types": "./helpers/helpers.d.ts", "import": "./helpers/helpers.js", "require": "./helpers/helpers.cjs"}}, "gitHead": "c2feec5c31f75d0341d9eef924c1cc78af85c0c1", "scripts": {"dev": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers chrome --grep", "docs": "pnpm run build && pnpm --filter \"./docs/**\" build", "lint": "concurrently \"pnpm:lint-*\"", "test": "pnpm lint && pnpm test-ci", "build": "rollup -c && pnpm emitDeclarations", "dev:ff": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.{js,ts}\" \"test/**/*.js\" \"docs/**/*.js\" --cache", "lint-md": "eslint \"**/*.md\" --cache", "test-ci": "concurrently \"pnpm:test-ci-*\"", "docs:dev": "pnpm run build && pnpm --filter \"./docs/**\" dev", "autobuild": "rollup -c -w", "test-size": "size-limit", "lint-types": "pnpm build && node test/types/autogen.js && tsc -p test/types", "test-ci-karma": "cross-env NODE_ENV=test karma start ./karma.conf.cjs --auto-watch --single-run --coverage --grep", "copyDeclarations": "node -e \"fs.cpSync('./src/types/', './dist/types/', {recursive:true})\"", "emitDeclarations": "tsc --emitDeclarationOnly && pnpm copyDeclarations", "test-ci-integration": "pnpm --filter \"./test/integration/**\" test"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "./dist/chart.umd.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "sideEffects": ["./auto/auto.js", "./auto/auto.cjs", "./dist/chart.umd.js"], "_nodeVersion": "16.20.1", "dependencies": {"@kurkle/color": "^0.3.0"}, "_hasShrinkwrap": false, "packageManager": "pnpm@7.9.0", "devDependencies": {"esm": "^3.2.25", "glob": "^8.0.3", "karma": "^6.3.2", "luxon": "^3.0.1", "yargs": "^17.5.1", "eslint": "^8.21.0", "moment": "^2.29.4", "rollup": "^3.3.0", "jasmine": "^3.7.0", "@swc/core": "^1.3.18", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "pixelmatch": "^5.3.0", "size-limit": "^8.0.0", "typescript": "^4.7.4", "concurrently": "^7.3.0", "jasmine-core": "^3.7.1", "@types/estree": "^1.0.0", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "moment-timezone": "^0.5.34", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.4.0", "eslint-plugin-html": "^7.1.0", "rollup-plugin-swc3": "^0.7.0", "@rollup/plugin-json": "^5.0.1", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^5.0.2", "chartjs-adapter-luxon": "^1.2.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "@types/offscreencanvas": "^2019.7.0", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^3.0.0", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^4.0.0", "@rollup/plugin-commonjs": "^23.0.2", "@typescript-eslint/parser": "^5.32.0", "karma-rollup-preprocessor": "7.0.7", "@size-limit/preset-big-lib": "^8.0.0", "@rollup/plugin-node-resolve": "^15.0.1", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.32.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_4.3.2_1690386218243_0.24183290150878056", "host": "s3://npm-registry-packages"}}, "4.3.3": {"name": "chart.js", "version": "4.3.3", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@4.3.3", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "dcd98afadf9fcfa5219e72ace5912092ea48fd36", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-4.3.3.tgz", "fileCount": 114, "integrity": "sha512-aTk7pBw+x6sQYhon/NR3ikfUJuym/LdgpTlgZRe2PaEhjUMKBKyNaFCMVRAyTEWYFNO7qRu7iQVqOw/OqzxZxQ==", "signatures": [{"sig": "MEUCIQDgHQZ8SBAE0VU8RJnWUKkHPFF8JrqUTVYnZ4mprNBAlgIgfP3UieOhFIQlHX2qP+2owbRc6bBQK9D13EdkV15tiXA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4920545}, "main": "./dist/chart.cjs", "pnpm": {"overrides": {"html-entities": "1.4.0"}, "peerDependencyRules": {"ignoreMissing": ["chart.js"]}}, "type": "module", "types": "./dist/types.d.ts", "unpkg": "./dist/chart.umd.js", "module": "./dist/chart.js", "engines": {"pnpm": ">=7"}, "exports": {".": {"types": "./dist/types.d.ts", "import": "./dist/chart.js", "require": "./dist/chart.cjs"}, "./auto": {"types": "./auto/auto.d.ts", "import": "./auto/auto.js", "require": "./auto/auto.cjs"}, "./helpers": {"types": "./helpers/helpers.d.ts", "import": "./helpers/helpers.js", "require": "./helpers/helpers.cjs"}}, "gitHead": "095a9849f4e2c9a9acc134d107050b6d374fa1b8", "scripts": {"dev": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers chrome --grep", "docs": "pnpm run build && pnpm --filter \"./docs/**\" build", "lint": "concurrently \"pnpm:lint-*\"", "test": "pnpm lint && pnpm test-ci", "build": "rollup -c && pnpm emitDeclarations", "dev:ff": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.{js,ts}\" \"test/**/*.js\" \"docs/**/*.js\" --cache", "lint-md": "eslint \"**/*.md\" --cache", "test-ci": "concurrently \"pnpm:test-ci-*\"", "docs:dev": "pnpm run build && pnpm --filter \"./docs/**\" dev", "autobuild": "rollup -c -w", "test-size": "size-limit", "lint-types": "pnpm build && node test/types/autogen.js && tsc -p test/types", "test-ci-karma": "cross-env NODE_ENV=test karma start ./karma.conf.cjs --auto-watch --single-run --coverage --grep", "copyDeclarations": "node -e \"fs.cpSync('./src/types/', './dist/types/', {recursive:true})\"", "emitDeclarations": "tsc --emitDeclarationOnly && pnpm copyDeclarations", "test-ci-integration": "pnpm --filter \"./test/integration/**\" test"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "./dist/chart.umd.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "sideEffects": ["./auto/auto.js", "./auto/auto.cjs", "./dist/chart.umd.js"], "_nodeVersion": "16.20.1", "dependencies": {"@kurkle/color": "^0.3.0"}, "_hasShrinkwrap": false, "packageManager": "pnpm@7.9.0", "devDependencies": {"esm": "^3.2.25", "glob": "^8.0.3", "karma": "^6.3.2", "luxon": "^3.0.1", "yargs": "^17.5.1", "eslint": "^8.21.0", "moment": "^2.29.4", "rollup": "^3.3.0", "jasmine": "^3.7.0", "@swc/core": "^1.3.18", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "pixelmatch": "^5.3.0", "size-limit": "^8.0.0", "typescript": "^4.7.4", "concurrently": "^7.3.0", "jasmine-core": "^3.7.1", "@types/estree": "^1.0.0", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "moment-timezone": "^0.5.34", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.4.0", "eslint-plugin-html": "^7.1.0", "rollup-plugin-swc3": "^0.7.0", "@rollup/plugin-json": "^5.0.1", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^5.0.2", "chartjs-adapter-luxon": "^1.2.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "@types/offscreencanvas": "^2019.7.0", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^3.0.0", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^4.0.0", "@rollup/plugin-commonjs": "^23.0.2", "@typescript-eslint/parser": "^5.32.0", "karma-rollup-preprocessor": "7.0.7", "@size-limit/preset-big-lib": "^8.0.0", "@rollup/plugin-node-resolve": "^15.0.1", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.32.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_4.3.3_1691018130784_0.7715765720075884", "host": "s3://npm-registry-packages"}}, "4.4.0": {"name": "chart.js", "version": "4.4.0", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@4.4.0", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "df843fdd9ec6bd88d7f07e2b95348d221bd2698c", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-4.4.0.tgz", "fileCount": 114, "integrity": "sha512-vQEj6d+z0dcsKLlQvbKIMYFHd3t8W/7L2vfJIbYcfyPcRx92CsHqECpueN8qVGNlKyDcr5wBrYAYKnfu/9Q1hQ==", "signatures": [{"sig": "MEUCIFPSj2ROg1GQz+HHfWpy4qjhJ6/ONFGRDVM7k1nWkrBBAiEAh5cffY9t9v/L4evebkTM5VgDhlwTNXbDXNfphQTPtjw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4923246}, "main": "./dist/chart.cjs", "pnpm": {"overrides": {"html-entities": "1.4.0"}, "peerDependencyRules": {"ignoreMissing": ["chart.js"]}}, "type": "module", "types": "./dist/types.d.ts", "unpkg": "./dist/chart.umd.js", "module": "./dist/chart.js", "engines": {"pnpm": ">=7"}, "exports": {".": {"types": "./dist/types.d.ts", "import": "./dist/chart.js", "require": "./dist/chart.cjs"}, "./auto": {"types": "./auto/auto.d.ts", "import": "./auto/auto.js", "require": "./auto/auto.cjs"}, "./helpers": {"types": "./helpers/helpers.d.ts", "import": "./helpers/helpers.js", "require": "./helpers/helpers.cjs"}}, "gitHead": "7ccd4a2d1463b0c5082de2538857554d9428d8d3", "scripts": {"dev": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers chrome --grep", "docs": "pnpm run build && pnpm --filter \"./docs/**\" build", "lint": "concurrently \"pnpm:lint-*\"", "test": "pnpm lint && pnpm test-ci", "build": "rollup -c && pnpm emitDeclarations", "dev:ff": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.{js,ts}\" \"test/**/*.js\" \"docs/**/*.js\" --cache", "lint-md": "eslint \"**/*.md\" --cache", "test-ci": "concurrently \"pnpm:test-ci-*\"", "docs:dev": "pnpm run build && pnpm --filter \"./docs/**\" dev", "autobuild": "rollup -c -w", "test-size": "size-limit", "lint-types": "pnpm build && node test/types/autogen.js && tsc -p test/types", "test-ci-karma": "cross-env NODE_ENV=test karma start ./karma.conf.cjs --auto-watch --single-run --coverage --grep", "copyDeclarations": "node -e \"fs.cpSync('./src/types/', './dist/types/', {recursive:true})\"", "emitDeclarations": "tsc --emitDeclarationOnly && pnpm copyDeclarations", "test-ci-integration": "pnpm --filter \"./test/integration/**\" test"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "./dist/chart.umd.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "sideEffects": ["./auto/auto.js", "./auto/auto.cjs", "./dist/chart.umd.js"], "_nodeVersion": "16.20.2", "dependencies": {"@kurkle/color": "^0.3.0"}, "_hasShrinkwrap": false, "packageManager": "pnpm@7.9.0", "devDependencies": {"esm": "^3.2.25", "glob": "^8.0.3", "karma": "^6.3.2", "luxon": "^3.0.1", "yargs": "^17.5.1", "eslint": "^8.21.0", "moment": "^2.29.4", "rollup": "^3.3.0", "jasmine": "^3.7.0", "@swc/core": "^1.3.18", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "pixelmatch": "^5.3.0", "size-limit": "^8.0.0", "typescript": "^4.7.4", "concurrently": "^7.3.0", "jasmine-core": "^3.7.1", "@types/estree": "^1.0.0", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "moment-timezone": "^0.5.34", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.4.0", "eslint-plugin-html": "^7.1.0", "rollup-plugin-swc3": "^0.7.0", "@rollup/plugin-json": "^5.0.1", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^5.0.2", "chartjs-adapter-luxon": "^1.2.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "@types/offscreencanvas": "^2019.7.0", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^3.0.0", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^4.0.0", "@rollup/plugin-commonjs": "^23.0.2", "@typescript-eslint/parser": "^5.32.0", "karma-rollup-preprocessor": "7.0.7", "@size-limit/preset-big-lib": "^8.0.0", "@rollup/plugin-node-resolve": "^15.0.1", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.32.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_4.4.0_1692881224649_0.7153205471578967", "host": "s3://npm-registry-packages"}}, "4.4.1": {"name": "chart.js", "version": "4.4.1", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@4.4.1", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "ac5dc0e69a7758909158a96fe80ce43b3bb96a9f", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-4.4.1.tgz", "fileCount": 114, "integrity": "sha512-C74QN1bxwV1v2PEujhmKjOZ7iUM4w6BWs23Md/6aOZZSlwMzeCIDGuZay++rBgChYru7/+QFeoQW0fQoP534Dg==", "signatures": [{"sig": "MEUCIQCzsPr3Kfw63PFvCyp7DY8asjJ0Xl4DltsdGztMCuV4TQIgL1AvD5w+5FE7cgUjipyZiLjWbSTpERV4jMikMs4tS98=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4926991}, "main": "./dist/chart.cjs", "pnpm": {"overrides": {"html-entities": "1.4.0"}, "peerDependencyRules": {"ignoreMissing": ["chart.js"]}}, "type": "module", "types": "./dist/types.d.ts", "unpkg": "./dist/chart.umd.js", "module": "./dist/chart.js", "engines": {"pnpm": ">=7"}, "exports": {".": {"types": "./dist/types.d.ts", "import": "./dist/chart.js", "require": "./dist/chart.cjs"}, "./auto": {"types": "./auto/auto.d.ts", "import": "./auto/auto.js", "require": "./auto/auto.cjs"}, "./helpers": {"types": "./helpers/helpers.d.ts", "import": "./helpers/helpers.js", "require": "./helpers/helpers.cjs"}}, "gitHead": "ac53fd282ee1a35512b532bb10bca9d74e2f8e41", "scripts": {"dev": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers chrome --grep", "docs": "pnpm run build && pnpm --filter \"./docs/**\" build", "lint": "concurrently \"pnpm:lint-*\"", "test": "pnpm lint && pnpm test-ci", "build": "rollup -c && pnpm emitDeclarations", "dev:ff": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.{js,ts}\" \"test/**/*.js\" \"docs/**/*.js\" --cache", "lint-md": "eslint \"**/*.md\" --cache", "test-ci": "concurrently \"pnpm:test-ci-*\"", "docs:dev": "pnpm run build && pnpm --filter \"./docs/**\" dev", "autobuild": "rollup -c -w", "lint-types": "pnpm build && node test/types/autogen.js && tsc -p test/types", "test-ci-karma": "cross-env NODE_ENV=test karma start ./karma.conf.cjs --auto-watch --single-run --coverage --grep", "copyDeclarations": "node -e \"fs.cpSync('./src/types/', './dist/types/', {recursive:true})\"", "emitDeclarations": "tsc --emitDeclarationOnly && pnpm copyDeclarations", "test-ci-integration": "pnpm --filter \"./test/integration/**\" test"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "./dist/chart.umd.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "sideEffects": ["./auto/auto.js", "./auto/auto.cjs", "./dist/chart.umd.js"], "_nodeVersion": "16.20.2", "dependencies": {"@kurkle/color": "^0.3.0"}, "_hasShrinkwrap": false, "packageManager": "pnpm@7.9.0", "devDependencies": {"esm": "^3.2.25", "glob": "^8.0.3", "karma": "^6.3.2", "luxon": "^3.0.1", "yargs": "^17.5.1", "eslint": "^8.21.0", "moment": "^2.29.4", "rollup": "^3.3.0", "jasmine": "^3.7.0", "@swc/core": "^1.3.18", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "pixelmatch": "^5.3.0", "typescript": "^4.7.4", "concurrently": "^7.3.0", "jasmine-core": "^3.7.1", "@types/estree": "^1.0.0", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "moment-timezone": "^0.5.34", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.4.0", "eslint-plugin-html": "^7.1.0", "rollup-plugin-swc3": "^0.7.0", "@rollup/plugin-json": "^5.0.1", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^5.0.2", "chartjs-adapter-luxon": "^1.2.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "@types/offscreencanvas": "^2019.7.0", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^3.0.0", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^4.0.0", "@rollup/plugin-commonjs": "^23.0.2", "@typescript-eslint/parser": "^5.32.0", "karma-rollup-preprocessor": "7.0.7", "@rollup/plugin-node-resolve": "^15.0.1", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.32.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_4.4.1_1701718179637_0.565356110203286", "host": "s3://npm-registry-packages"}}, "4.4.2": {"name": "chart.js", "version": "4.4.2", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@4.4.2", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "95962fa6430828ed325a480cc2d5f2b4e385ac31", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-4.4.2.tgz", "fileCount": 114, "integrity": "sha512-6GD7iKwFpP5kbSD4MeRRRlTnQvxfQREy36uEtm1hzHzcOqwWx0YEHuspuoNlslu+nciLIB7fjjsHkUv/FzFcOg==", "signatures": [{"sig": "MEQCICaq0Rvq3cYUDvlt43YT0BOg3+b2wAUZcHJjMjkNN4SPAiBGqmX61rW2aGHLkKvq2JQAfqE7d9aTbzCe5u12SMMTvA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4928981}, "main": "./dist/chart.cjs", "pnpm": {"overrides": {"html-entities": "1.4.0"}, "peerDependencyRules": {"ignoreMissing": ["chart.js"]}}, "type": "module", "types": "./dist/types.d.ts", "unpkg": "./dist/chart.umd.js", "module": "./dist/chart.js", "engines": {"pnpm": ">=8"}, "exports": {".": {"types": "./dist/types.d.ts", "import": "./dist/chart.js", "require": "./dist/chart.cjs"}, "./auto": {"types": "./auto/auto.d.ts", "import": "./auto/auto.js", "require": "./auto/auto.cjs"}, "./helpers": {"types": "./helpers/helpers.d.ts", "import": "./helpers/helpers.js", "require": "./helpers/helpers.cjs"}}, "gitHead": "7736ea32da0a93f89d20d59861dd8821a2802447", "scripts": {"dev": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers chrome --grep", "docs": "pnpm run build && pnpm --filter \"./docs/**\" build", "lint": "concurrently \"pnpm:lint-*\"", "test": "pnpm lint && pnpm test-ci", "build": "rollup -c && pnpm emitDeclarations", "dev:ff": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.{js,ts}\" \"test/**/*.js\" \"docs/**/*.js\" --cache", "lint-md": "eslint \"**/*.md\" --cache", "test-ci": "concurrently \"pnpm:test-ci-*\"", "docs:dev": "pnpm run build && pnpm --filter \"./docs/**\" dev", "autobuild": "rollup -c -w", "lint-types": "pnpm build && node test/types/autogen.js && tsc -p test/types", "test-ci-karma": "cross-env NODE_ENV=test karma start ./karma.conf.cjs --auto-watch --single-run --coverage --grep", "copyDeclarations": "node -e \"fs.cpSync('./src/types/', './dist/types/', {recursive:true})\"", "emitDeclarations": "tsc --emitDeclarationOnly && pnpm copyDeclarations", "test-ci-integration": "pnpm --filter \"./test/integration/**\" test"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "./dist/chart.umd.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "sideEffects": ["./auto/auto.js", "./auto/auto.cjs", "./dist/chart.umd.js"], "_nodeVersion": "16.20.2", "dependencies": {"@kurkle/color": "^0.3.0"}, "_hasShrinkwrap": false, "packageManager": "pnpm@8.13.0", "devDependencies": {"esm": "^3.2.25", "glob": "^8.0.3", "karma": "^6.3.2", "luxon": "^3.0.1", "yargs": "^17.5.1", "eslint": "^8.21.0", "moment": "^2.29.4", "rollup": "^3.3.0", "jasmine": "^3.7.0", "@swc/core": "^1.3.18", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "pixelmatch": "^5.3.0", "typescript": "^4.7.4", "concurrently": "^7.3.0", "jasmine-core": "^3.7.1", "@types/estree": "^1.0.0", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "moment-timezone": "^0.5.34", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.4.0", "eslint-plugin-html": "^7.1.0", "rollup-plugin-swc3": "^0.7.0", "@rollup/plugin-json": "^5.0.1", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^5.0.2", "chartjs-adapter-luxon": "^1.2.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "@types/offscreencanvas": "^2019.7.0", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^3.0.0", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^4.0.0", "@rollup/plugin-commonjs": "^23.0.2", "@typescript-eslint/parser": "^5.32.0", "karma-rollup-preprocessor": "7.0.7", "@rollup/plugin-node-resolve": "^15.0.1", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.32.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_4.4.2_1709139222568_0.7775583541096549", "host": "s3://npm-registry-packages"}}, "4.4.3": {"name": "chart.js", "version": "4.4.3", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@4.4.3", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "3b2e11e7010fefa99b07d0349236f5098e5226ad", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-4.4.3.tgz", "fileCount": 114, "integrity": "sha512-qK1gkGSRYcJzqrrzdR6a+I0vQ4/R+SoODXyAjscQ/4mzuNzySaMCd+hyVxitSY1+L2fjPD1Gbn+ibNqRmwQeLw==", "signatures": [{"sig": "MEUCIHEQlB/EHRXl+zxBdq8h5+FTLF/wy8ikLDw6fXkoEIPrAiEAjKPnB0D2CdDNJEM0vhSpBmI31+u0snhMqElekSA6khI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4931881}, "main": "./dist/chart.cjs", "pnpm": {"overrides": {"html-entities": "1.4.0"}, "peerDependencyRules": {"ignoreMissing": ["chart.js"]}}, "type": "module", "types": "./dist/types.d.ts", "unpkg": "./dist/chart.umd.js", "module": "./dist/chart.js", "engines": {"pnpm": ">=8"}, "exports": {".": {"types": "./dist/types.d.ts", "import": "./dist/chart.js", "require": "./dist/chart.cjs"}, "./auto": {"types": "./auto/auto.d.ts", "import": "./auto/auto.js", "require": "./auto/auto.cjs"}, "./helpers": {"types": "./helpers/helpers.d.ts", "import": "./helpers/helpers.js", "require": "./helpers/helpers.cjs"}}, "gitHead": "3d0801299283cb6cb8e777e0cd45e6172cc43966", "scripts": {"dev": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers chrome --grep", "docs": "pnpm run build && pnpm --filter \"./docs/**\" build", "lint": "concurrently \"pnpm:lint-*\"", "test": "pnpm lint && pnpm test-ci", "build": "rollup -c && pnpm emitDeclarations", "dev:ff": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.{js,ts}\" \"test/**/*.js\" \"docs/**/*.js\" --cache", "lint-md": "eslint \"**/*.md\" --cache", "test-ci": "concurrently \"pnpm:test-ci-*\"", "docs:dev": "pnpm run build && pnpm --filter \"./docs/**\" dev", "autobuild": "rollup -c -w", "lint-types": "pnpm build && node test/types/autogen.js && tsc -p test/types", "test-ci-karma": "cross-env NODE_ENV=test karma start ./karma.conf.cjs --auto-watch --single-run --coverage --grep", "copyDeclarations": "node -e \"fs.cpSync('./src/types/', './dist/types/', {recursive:true})\"", "emitDeclarations": "tsc --emitDeclarationOnly && pnpm copyDeclarations", "test-ci-integration": "pnpm --filter \"./test/integration/**\" test"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "./dist/chart.umd.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "sideEffects": ["./auto/auto.js", "./auto/auto.cjs", "./dist/chart.umd.js"], "_nodeVersion": "16.20.2", "dependencies": {"@kurkle/color": "^0.3.0"}, "_hasShrinkwrap": false, "packageManager": "pnpm@8.13.0", "devDependencies": {"esm": "^3.2.25", "glob": "^8.0.3", "karma": "^6.3.2", "luxon": "^3.0.1", "yargs": "^17.5.1", "eslint": "^8.21.0", "moment": "^2.29.4", "rollup": "^3.3.0", "jasmine": "^3.7.0", "@swc/core": "^1.3.18", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "pixelmatch": "^5.3.0", "typescript": "^4.7.4", "concurrently": "^7.3.0", "jasmine-core": "^3.7.1", "@types/estree": "^1.0.0", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "moment-timezone": "^0.5.34", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.4.0", "eslint-plugin-html": "^7.1.0", "rollup-plugin-swc3": "^0.7.0", "@rollup/plugin-json": "^5.0.1", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^5.0.2", "chartjs-adapter-luxon": "^1.2.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "@types/offscreencanvas": "^2019.7.0", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^3.0.0", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^4.0.0", "@rollup/plugin-commonjs": "^23.0.2", "@typescript-eslint/parser": "^5.32.0", "karma-rollup-preprocessor": "7.0.7", "@rollup/plugin-node-resolve": "^15.0.1", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.32.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_4.4.3_1715950573538_0.16005036536149175", "host": "s3://npm-registry-packages"}}, "4.4.4": {"name": "chart.js", "version": "4.4.4", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@4.4.4", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "b682d2e7249f7a0cbb1b1d31c840266ae9db64b7", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-4.4.4.tgz", "fileCount": 114, "integrity": "sha512-emICKGBABnxhMjUjlYRR12PmOXhJ2eJjEHL2/dZlWjxRAZT1D8xplLFq5M0tMQK8ja+wBS/tuVEJB5C6r7VxJA==", "signatures": [{"sig": "MEYCIQCREOkiUmC/pGM41k1T/18ZFU3YnwACuIGPzbQzyJFEdQIhAOlWrJPrT+6ch1ZUfivPvFY69kKEmREMkzJHcmJ1TlGq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4935356}, "main": "./dist/chart.cjs", "pnpm": {"overrides": {"html-entities": "1.4.0"}, "peerDependencyRules": {"ignoreMissing": ["chart.js"]}}, "type": "module", "types": "./dist/types.d.ts", "unpkg": "./dist/chart.umd.js", "module": "./dist/chart.js", "engines": {"pnpm": ">=8"}, "exports": {".": {"types": "./dist/types.d.ts", "import": "./dist/chart.js", "require": "./dist/chart.cjs"}, "./auto": {"types": "./auto/auto.d.ts", "import": "./auto/auto.js", "require": "./auto/auto.cjs"}, "./helpers": {"types": "./helpers/helpers.d.ts", "import": "./helpers/helpers.js", "require": "./helpers/helpers.cjs"}}, "gitHead": "dd554e0d87fc525c2a4efcecead9cc3c280936f4", "scripts": {"dev": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers chrome --grep", "docs": "pnpm run build && pnpm --filter \"./docs/**\" build", "lint": "concurrently \"pnpm:lint-*\"", "test": "pnpm lint && pnpm test-ci", "build": "rollup -c && pnpm emitDeclarations", "dev:ff": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.{js,ts}\" \"test/**/*.js\" \"docs/**/*.js\" --cache", "lint-md": "eslint \"**/*.md\" --cache", "test-ci": "concurrently \"pnpm:test-ci-*\"", "docs:dev": "pnpm run build && pnpm --filter \"./docs/**\" dev", "autobuild": "rollup -c -w", "lint-types": "pnpm build && node test/types/autogen.js && tsc -p test/types", "test-ci-karma": "cross-env NODE_ENV=test karma start ./karma.conf.cjs --auto-watch --single-run --coverage --grep", "copyDeclarations": "node -e \"fs.cpSync('./src/types/', './dist/types/', {recursive:true})\"", "emitDeclarations": "tsc --emitDeclarationOnly && pnpm copyDeclarations", "test-ci-integration": "pnpm --filter \"./test/integration/**\" test"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "./dist/chart.umd.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "sideEffects": ["./auto/auto.js", "./auto/auto.cjs", "./dist/chart.umd.js"], "_nodeVersion": "16.20.2", "dependencies": {"@kurkle/color": "^0.3.0"}, "_hasShrinkwrap": false, "packageManager": "pnpm@8.13.0", "devDependencies": {"esm": "^3.2.25", "glob": "^8.0.3", "karma": "^6.3.2", "luxon": "^3.0.1", "yargs": "^17.5.1", "eslint": "^8.21.0", "moment": "^2.29.4", "rollup": "^3.3.0", "jasmine": "^3.7.0", "@swc/core": "^1.3.18", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "pixelmatch": "^5.3.0", "typescript": "^4.7.4", "concurrently": "^7.3.0", "jasmine-core": "^3.7.1", "@types/estree": "^1.0.0", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "moment-timezone": "^0.5.34", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.4.0", "eslint-plugin-html": "^7.1.0", "rollup-plugin-swc3": "^0.7.0", "@rollup/plugin-json": "^5.0.1", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^5.0.2", "chartjs-adapter-luxon": "^1.2.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "@types/offscreencanvas": "^2019.7.0", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^3.0.0", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^4.0.0", "@rollup/plugin-commonjs": "^23.0.2", "@typescript-eslint/parser": "^5.32.0", "karma-rollup-preprocessor": "7.0.7", "@rollup/plugin-node-resolve": "^15.0.1", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.32.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_4.4.4_1724186055772_0.7498542659905663", "host": "s3://npm-registry-packages"}}, "4.4.5": {"name": "chart.js", "version": "4.4.5", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@4.4.5", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "6acd3dd74759e6e1e42275572eaedad3918221d1", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-4.4.5.tgz", "fileCount": 114, "integrity": "sha512-CVVjg1RYTJV9OCC8WeJPMx8gsV8K6WIyIEQUE3ui4AR9Hfgls9URri6Ja3hyMVBbTF8Q2KFa19PE815gWcWhng==", "signatures": [{"sig": "MEUCIAWESCHsEv2Z75TzbCYaPC8p3vxCgVTWWZKNVV3W8dAAAiEA5Ds0VzlRiQzIqwv2ylpdlGzQw7uNucFpw68qO2j9+6E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4938325}, "main": "./dist/chart.cjs", "pnpm": {"overrides": {"html-entities": "1.4.0"}, "peerDependencyRules": {"ignoreMissing": ["chart.js"]}}, "type": "module", "types": "./dist/types.d.ts", "unpkg": "./dist/chart.umd.js", "module": "./dist/chart.js", "engines": {"pnpm": ">=8"}, "exports": {".": {"types": "./dist/types.d.ts", "import": "./dist/chart.js", "require": "./dist/chart.cjs"}, "./auto": {"types": "./auto/auto.d.ts", "import": "./auto/auto.js", "require": "./auto/auto.cjs"}, "./helpers": {"types": "./helpers/helpers.d.ts", "import": "./helpers/helpers.js", "require": "./helpers/helpers.cjs"}}, "gitHead": "7af71f507754deb4a3abf8ce0e0b95d0cca560c5", "scripts": {"dev": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers chrome --grep", "docs": "pnpm run build && pnpm --filter \"./docs/**\" build", "lint": "concurrently \"pnpm:lint-*\"", "test": "pnpm lint && pnpm test-ci", "build": "rollup -c && pnpm emitDeclarations", "dev:ff": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.{js,ts}\" \"test/**/*.js\" \"docs/**/*.js\" --cache", "lint-md": "eslint \"**/*.md\" --cache", "test-ci": "concurrently \"pnpm:test-ci-*\"", "docs:dev": "pnpm run build && pnpm --filter \"./docs/**\" dev", "autobuild": "rollup -c -w", "lint-types": "pnpm build && node test/types/autogen.js && tsc -p test/types", "test-ci-karma": "cross-env NODE_ENV=test karma start ./karma.conf.cjs --auto-watch --single-run --coverage --grep", "copyDeclarations": "node -e \"fs.cpSync('./src/types/', './dist/types/', {recursive:true})\"", "emitDeclarations": "tsc --emitDeclarationOnly && pnpm copyDeclarations", "test-ci-integration": "pnpm --filter \"./test/integration/**\" test"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "./dist/chart.umd.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "sideEffects": ["./auto/auto.js", "./auto/auto.cjs", "./dist/chart.umd.js"], "_nodeVersion": "16.20.2", "dependencies": {"@kurkle/color": "^0.3.0"}, "_hasShrinkwrap": false, "packageManager": "pnpm@8.13.0", "devDependencies": {"esm": "^3.2.25", "glob": "^8.0.3", "karma": "^6.3.2", "luxon": "^3.0.1", "yargs": "^17.5.1", "eslint": "^8.21.0", "moment": "^2.29.4", "rollup": "^3.3.0", "jasmine": "^3.7.0", "@swc/core": "^1.3.18", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "pixelmatch": "^5.3.0", "typescript": "^4.7.4", "concurrently": "^7.3.0", "jasmine-core": "^3.7.1", "@types/estree": "^1.0.0", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "moment-timezone": "^0.5.34", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.4.0", "eslint-plugin-html": "^7.1.0", "rollup-plugin-swc3": "^0.7.0", "@rollup/plugin-json": "^5.0.1", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^5.0.2", "chartjs-adapter-luxon": "^1.2.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "@types/offscreencanvas": "^2019.7.0", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^3.0.0", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^4.0.0", "@rollup/plugin-commonjs": "^23.0.2", "@typescript-eslint/parser": "^5.32.0", "karma-rollup-preprocessor": "7.0.7", "@rollup/plugin-node-resolve": "^15.0.1", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.32.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_4.4.5_1729018726258_0.28120656970893565", "host": "s3://npm-registry-packages"}}, "4.4.6": {"name": "chart.js", "version": "4.4.6", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@4.4.6", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "da39b84ca752298270d4c0519675c7659936abec", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-4.4.6.tgz", "fileCount": 114, "integrity": "sha512-8Y406zevUPbbIBA/HRk33khEmQPk5+cxeflWE/2rx1NJsjVWMPw/9mSP9rxHP5eqi6LNoPBVMfZHxbwLSgldYA==", "signatures": [{"sig": "MEUCIEAqsAYE8m5iPCJKJ1F3JyRJQLNk7FdbxwjXjFtzShAEAiEAoTWmGqJlGS32Bd/TuwComaO7CpLwFToBTLAGWOj7aQk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4939273}, "main": "./dist/chart.cjs", "pnpm": {"overrides": {"html-entities": "1.4.0"}, "peerDependencyRules": {"ignoreMissing": ["chart.js"]}}, "type": "module", "types": "./dist/types.d.ts", "unpkg": "./dist/chart.umd.js", "module": "./dist/chart.js", "engines": {"pnpm": ">=8"}, "exports": {".": {"types": "./dist/types.d.ts", "import": "./dist/chart.js", "require": "./dist/chart.cjs"}, "./auto": {"types": "./auto/auto.d.ts", "import": "./auto/auto.js", "require": "./auto/auto.cjs"}, "./helpers": {"types": "./helpers/helpers.d.ts", "import": "./helpers/helpers.js", "require": "./helpers/helpers.cjs"}}, "gitHead": "47245a7f36ea581ff996f454b72859437715b2a8", "scripts": {"dev": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers chrome --grep", "docs": "pnpm run build && pnpm --filter \"./docs/**\" build", "lint": "concurrently \"pnpm:lint-*\"", "test": "pnpm lint && pnpm test-ci", "build": "rollup -c && pnpm emitDeclarations", "dev:ff": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.{js,ts}\" \"test/**/*.js\" \"docs/**/*.js\" --cache", "lint-md": "eslint \"**/*.md\" --cache", "test-ci": "concurrently \"pnpm:test-ci-*\"", "docs:dev": "pnpm run build && pnpm --filter \"./docs/**\" dev", "autobuild": "rollup -c -w", "lint-types": "pnpm build && node test/types/autogen.js && tsc -p test/types", "test-ci-karma": "cross-env NODE_ENV=test karma start ./karma.conf.cjs --auto-watch --single-run --coverage --grep", "copyDeclarations": "node -e \"fs.cpSync('./src/types/', './dist/types/', {recursive:true})\"", "emitDeclarations": "tsc --emitDeclarationOnly && pnpm copyDeclarations", "test-ci-integration": "pnpm --filter \"./test/integration/**\" test"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "./dist/chart.umd.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "sideEffects": ["./auto/auto.js", "./auto/auto.cjs", "./dist/chart.umd.js"], "_nodeVersion": "16.20.2", "dependencies": {"@kurkle/color": "^0.3.0"}, "_hasShrinkwrap": false, "packageManager": "pnpm@8.13.0", "devDependencies": {"esm": "^3.2.25", "glob": "^8.0.3", "karma": "^6.3.2", "luxon": "^3.0.1", "yargs": "^17.5.1", "eslint": "^8.21.0", "moment": "^2.29.4", "rollup": "^3.3.0", "jasmine": "^3.7.0", "@swc/core": "^1.3.18", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "pixelmatch": "^5.3.0", "typescript": "^4.7.4", "concurrently": "^7.3.0", "jasmine-core": "^3.7.1", "@types/estree": "^1.0.0", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "moment-timezone": "^0.5.34", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.4.0", "eslint-plugin-html": "^7.1.0", "rollup-plugin-swc3": "^0.7.0", "@rollup/plugin-json": "^5.0.1", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^5.0.2", "chartjs-adapter-luxon": "^1.2.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "@types/offscreencanvas": "^2019.7.0", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^3.0.0", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^4.0.0", "@rollup/plugin-commonjs": "^23.0.2", "@typescript-eslint/parser": "^5.32.0", "karma-rollup-preprocessor": "7.0.7", "@rollup/plugin-node-resolve": "^15.0.1", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.32.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_4.4.6_1730123022326_0.8729332484259167", "host": "s3://npm-registry-packages"}}, "4.4.7": {"name": "chart.js", "version": "4.4.7", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@4.4.7", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "7a01ee0b4dac3c03f2ab0589af888db296d896fa", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-4.4.7.tgz", "fileCount": 114, "integrity": "sha512-pwkcKfdzTMAU/+jNosKhNL2bHtJc/sSmYgVbuGTEDhzkrhmyihmP7vUc/5ZK9WopidMDHNe3Wm7jOd/WhuHWuw==", "signatures": [{"sig": "MEQCIDufGVWBw8ttnPIFmQWnuHAs6oDUvslm41oNe+WRdRqVAiA1RW+G8ioFGNE1pnv/6jxSVTAH/EGbJU9obf7yy4Zt9Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4939252}, "main": "./dist/chart.cjs", "pnpm": {"overrides": {"html-entities": "1.4.0"}, "peerDependencyRules": {"ignoreMissing": ["chart.js"]}}, "type": "module", "types": "./dist/types.d.ts", "unpkg": "./dist/chart.umd.js", "module": "./dist/chart.js", "engines": {"pnpm": ">=8"}, "exports": {".": {"types": "./dist/types.d.ts", "import": "./dist/chart.js", "require": "./dist/chart.cjs"}, "./auto": {"types": "./auto/auto.d.ts", "import": "./auto/auto.js", "require": "./auto/auto.cjs"}, "./helpers": {"types": "./helpers/helpers.d.ts", "import": "./helpers/helpers.js", "require": "./helpers/helpers.cjs"}}, "gitHead": "57b5c5b78fb2d8504f556bef6e4177735d9929ea", "scripts": {"dev": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers chrome --grep", "docs": "pnpm run build && pnpm --filter \"./docs/**\" build", "lint": "concurrently \"pnpm:lint-*\"", "test": "pnpm lint && pnpm test-ci", "build": "rollup -c && pnpm emitDeclarations", "dev:ff": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.{js,ts}\" \"test/**/*.js\" \"docs/**/*.js\" --cache", "lint-md": "eslint \"**/*.md\" --cache", "test-ci": "concurrently \"pnpm:test-ci-*\"", "docs:dev": "pnpm run build && pnpm --filter \"./docs/**\" dev", "autobuild": "rollup -c -w", "lint-types": "pnpm build && node test/types/autogen.js && tsc -p test/types", "test-ci-karma": "cross-env NODE_ENV=test karma start ./karma.conf.cjs --auto-watch --single-run --coverage --grep", "copyDeclarations": "node -e \"fs.cpSync('./src/types/', './dist/types/', {recursive:true})\"", "emitDeclarations": "tsc --emitDeclarationOnly && pnpm copyDeclarations", "test-ci-integration": "pnpm --filter \"./test/integration/**\" test"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "./dist/chart.umd.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "sideEffects": ["./auto/auto.js", "./auto/auto.cjs", "./dist/chart.umd.js"], "_nodeVersion": "16.20.2", "dependencies": {"@kurkle/color": "^0.3.0"}, "_hasShrinkwrap": false, "packageManager": "pnpm@8.13.0", "devDependencies": {"esm": "^3.2.25", "glob": "^8.0.3", "karma": "^6.3.2", "luxon": "^3.0.1", "yargs": "^17.5.1", "eslint": "^8.21.0", "moment": "^2.29.4", "rollup": "^3.3.0", "jasmine": "^3.7.0", "@swc/core": "^1.3.18", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "pixelmatch": "^5.3.0", "typescript": "^4.7.4", "concurrently": "^7.3.0", "jasmine-core": "^3.7.1", "@types/estree": "^1.0.0", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "moment-timezone": "^0.5.34", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.4.0", "eslint-plugin-html": "^7.1.0", "rollup-plugin-swc3": "^0.7.0", "@rollup/plugin-json": "^5.0.1", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^5.0.2", "chartjs-adapter-luxon": "^1.2.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "@types/offscreencanvas": "^2019.7.0", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^3.0.0", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^4.0.0", "@rollup/plugin-commonjs": "^23.0.2", "@typescript-eslint/parser": "^5.32.0", "karma-rollup-preprocessor": "7.0.7", "@rollup/plugin-node-resolve": "^15.0.1", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.32.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_4.4.7_1733071665842_0.39804323475569015", "host": "s3://npm-registry-packages"}}, "4.4.8": {"name": "chart.js", "version": "4.4.8", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "license": "MIT", "_id": "chart.js@4.4.8", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "homepage": "https://www.chartjs.org", "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "dist": {"shasum": "54645b638e9d585099bc16b892947b5e6cd2a552", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-4.4.8.tgz", "fileCount": 114, "integrity": "sha512-IkGZlVpXP+83QpMm4uxEiGqSI7jFizwVtF3+n5Pc3k7sMO+tkd0qxh2OzLhenM0K80xtmAONWGBn082EiBQSDA==", "signatures": [{"sig": "MEUCIG1AgkVv+jRbM2VW1SW9Y2t5lWGlsDKCPT0rdQ8q0B3CAiEAs1ahXsCya2BDfxPRjLcwvaNtkccCNkl7Lz6IrXrtbqA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4951561}, "main": "./dist/chart.cjs", "pnpm": {"overrides": {"html-entities": "1.4.0"}, "peerDependencyRules": {"ignoreMissing": ["chart.js"]}}, "type": "module", "types": "./dist/types.d.ts", "unpkg": "./dist/chart.umd.js", "module": "./dist/chart.js", "engines": {"pnpm": ">=8"}, "exports": {".": {"types": "./dist/types.d.ts", "import": "./dist/chart.js", "require": "./dist/chart.cjs"}, "./auto": {"types": "./auto/auto.d.ts", "import": "./auto/auto.js", "require": "./auto/auto.cjs"}, "./helpers": {"types": "./helpers/helpers.d.ts", "import": "./helpers/helpers.js", "require": "./helpers/helpers.cjs"}}, "gitHead": "32c8032e3f8e0c91edfb50cadbd612da88fc26cd", "scripts": {"dev": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers chrome --grep", "docs": "pnpm run build && pnpm --filter \"./docs/**\" build", "lint": "concurrently \"pnpm:lint-*\"", "test": "pnpm lint && pnpm test-ci", "build": "rollup -c && pnpm emitDeclarations", "dev:ff": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers firefox --grep", "lint-js": "eslint \"src/**/*.{js,ts}\" \"test/**/*.js\" \"docs/**/*.js\" --cache", "lint-md": "eslint \"**/*.md\" --cache", "test-ci": "concurrently \"pnpm:test-ci-*\"", "docs:dev": "pnpm run build && pnpm --filter \"./docs/**\" dev", "autobuild": "rollup -c -w", "lint-types": "pnpm build && node test/types/autogen.js && tsc -p test/types", "test-ci-karma": "cross-env NODE_ENV=test karma start ./karma.conf.cjs --auto-watch --single-run --coverage --grep", "copyDeclarations": "node -e \"fs.cpSync('./src/types/', './dist/types/', {recursive:true})\"", "emitDeclarations": "tsc --emitDeclarationOnly && pnpm copyDeclarations", "test-ci-integration": "pnpm --filter \"./test/integration/**\" test"}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "jsdelivr": "./dist/chart.umd.js", "repository": {"url": "git+https://github.com/chartjs/Chart.js.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "Simple HTML5 charts using the canvas element.", "directories": {}, "sideEffects": ["./auto/auto.js", "./auto/auto.cjs", "./dist/chart.umd.js"], "_nodeVersion": "16.20.2", "dependencies": {"@kurkle/color": "^0.3.0"}, "_hasShrinkwrap": false, "packageManager": "pnpm@8.13.0", "devDependencies": {"esm": "^3.2.25", "glob": "^8.0.3", "karma": "^6.3.2", "luxon": "^3.0.1", "yargs": "^17.5.1", "eslint": "^8.21.0", "moment": "^2.29.4", "rollup": "^3.3.0", "jasmine": "^3.7.0", "@swc/core": "^1.3.18", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "pixelmatch": "^5.3.0", "typescript": "^4.7.4", "concurrently": "^7.3.0", "jasmine-core": "^3.7.1", "@types/estree": "^1.0.0", "karma-jasmine": "^4.0.1", "karma-coverage": "^2.0.3", "moment-timezone": "^0.5.34", "eslint-plugin-es": "^4.1.0", "chartjs-test-utils": "^0.4.0", "eslint-plugin-html": "^7.1.0", "rollup-plugin-swc3": "^0.7.0", "@rollup/plugin-json": "^5.0.1", "karma-edge-launcher": "^0.4.2", "karma-spec-reporter": "0.0.32", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^5.0.2", "chartjs-adapter-luxon": "^1.2.0", "eslint-config-chartjs": "^0.3.0", "karma-chrome-launcher": "^3.1.0", "rollup-plugin-cleanup": "^3.2.1", "@types/offscreencanvas": "^2019.7.0", "chartjs-adapter-moment": "^1.0.0", "eslint-plugin-markdown": "^3.0.0", "karma-firefox-launcher": "^2.1.0", "rollup-plugin-istanbul": "^4.0.0", "@rollup/plugin-commonjs": "^23.0.2", "@typescript-eslint/parser": "^5.32.0", "karma-rollup-preprocessor": "7.0.7", "@rollup/plugin-node-resolve": "^15.0.1", "karma-jasmine-html-reporter": "^1.5.4", "karma-safari-private-launcher": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.32.0"}, "_npmOperationalInternal": {"tmp": "tmp/chart.js_4.4.8_1739980106236_0.6235103742353374", "host": "s3://npm-registry-packages-npm-production"}}, "4.4.9": {"name": "chart.js", "homepage": "https://www.chartjs.org", "description": "Simple HTML5 charts using the canvas element.", "version": "4.4.9", "license": "MIT", "type": "module", "sideEffects": ["./auto/auto.js", "./auto/auto.cjs", "./dist/chart.umd.js"], "jsdelivr": "./dist/chart.umd.js", "unpkg": "./dist/chart.umd.js", "main": "./dist/chart.cjs", "module": "./dist/chart.js", "exports": {".": {"types": "./dist/types.d.ts", "import": "./dist/chart.js", "require": "./dist/chart.cjs"}, "./auto": {"types": "./auto/auto.d.ts", "import": "./auto/auto.js", "require": "./auto/auto.cjs"}, "./helpers": {"types": "./helpers/helpers.d.ts", "import": "./helpers/helpers.js", "require": "./helpers/helpers.cjs"}}, "types": "./dist/types.d.ts", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "repository": {"type": "git", "url": "git+https://github.com/chartjs/Chart.js.git"}, "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "scripts": {"autobuild": "rollup -c -w", "copyDeclarations": "node -e \"fs.cpSync('./src/types/', './dist/types/', {recursive:true})\"", "emitDeclarations": "tsc --emitDeclarationOnly && pnpm copyDeclarations", "build": "rollup -c && pnpm emitDeclarations", "dev": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers chrome --grep", "dev:ff": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers firefox --grep", "docs": "pnpm run build && pnpm --filter \"./docs/**\" build", "docs:dev": "pnpm run build && pnpm --filter \"./docs/**\" dev", "lint-js": "eslint \"src/**/*.{js,ts}\" \"test/**/*.js\" \"docs/**/*.js\" --cache", "lint-md": "eslint \"**/*.md\" --cache", "lint-types": "pnpm build && node test/types/autogen.js && tsc -p test/types", "lint": "concurrently \"pnpm:lint-*\"", "test": "pnpm lint && pnpm test-ci", "test-ci": "concurrently \"pnpm:test-ci-*\"", "test-ci-karma": "cross-env NODE_ENV=test karma start ./karma.conf.cjs --auto-watch --single-run --coverage --grep", "test-ci-integration": "pnpm --filter \"./test/integration/**\" test"}, "dependencies": {"@kurkle/color": "^0.3.0"}, "devDependencies": {"@rollup/plugin-commonjs": "^23.0.2", "@rollup/plugin-inject": "^5.0.2", "@rollup/plugin-json": "^5.0.1", "@rollup/plugin-node-resolve": "^15.0.1", "@swc/core": "^1.3.18", "@types/estree": "^1.0.0", "@types/offscreencanvas": "^2019.7.0", "@typescript-eslint/eslint-plugin": "^5.32.0", "@typescript-eslint/parser": "^5.32.0", "chartjs-adapter-luxon": "^1.2.0", "chartjs-adapter-moment": "^1.0.0", "chartjs-test-utils": "^0.4.0", "concurrently": "^7.3.0", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "eslint": "^8.21.0", "eslint-config-chartjs": "^0.3.0", "eslint-plugin-es": "^4.1.0", "eslint-plugin-html": "^7.1.0", "eslint-plugin-markdown": "^3.0.0", "esm": "^3.2.25", "glob": "^8.0.3", "jasmine": "^3.7.0", "jasmine-core": "^3.7.1", "karma": "^6.3.2", "karma-chrome-launcher": "^3.1.0", "karma-coverage": "^2.0.3", "karma-edge-launcher": "^0.4.2", "karma-firefox-launcher": "^2.1.0", "karma-jasmine": "^4.0.1", "karma-jasmine-html-reporter": "^1.5.4", "karma-rollup-preprocessor": "7.0.7", "karma-safari-private-launcher": "^1.0.0", "karma-spec-reporter": "0.0.32", "luxon": "^3.0.1", "moment": "^2.29.4", "moment-timezone": "^0.5.34", "pixelmatch": "^5.3.0", "rollup": "^3.3.0", "rollup-plugin-cleanup": "^3.2.1", "rollup-plugin-istanbul": "^4.0.0", "rollup-plugin-swc3": "^0.7.0", "rollup-plugin-terser": "^7.0.2", "typescript": "^4.7.4", "yargs": "^17.5.1"}, "engines": {"pnpm": ">=8"}, "packageManager": "pnpm@8.13.0", "pnpm": {"overrides": {"html-entities": "1.4.0"}, "peerDependencyRules": {"ignoreMissing": ["chart.js"]}}, "gitHead": "817bec0439b0e0666687deef2aefc435ff9710db", "_id": "chart.js@4.4.9", "_nodeVersion": "16.20.2", "_npmVersion": "8.19.4", "dist": {"integrity": "sha512-EyZ9wWKgpAU0fLJ43YAEIF8sr5F2W3LqbS40ZJyHIner2lY14ufqv2VMp69MAiZ2rpwxEUxEhIH/0U3xyRynxg==", "shasum": "602e2fc2462f0f7bb7b255eaa1b51f56a43a1362", "tarball": "https://registry.npmjs.org/chart.js/-/chart.js-4.4.9.tgz", "fileCount": 115, "unpackedSize": 4961506, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQC25/2sLhbq0gp1LdfLf6ABYU1EBGte0cV1I/a4VldEdQIgJPkoWVxkjK3YcEhOlOtGiJSf8cTnlVQjBhIRkgiuNVQ="}]}, "_npmUser": {"name": "chartjs-ci", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/chart.js_4.4.9_1744723610410_0.6733140448630366"}, "_hasShrinkwrap": false}}, "time": {"created": "2014-07-08T22:30:18.422Z", "modified": "2025-04-15T13:26:50.826Z", "1.0.1-beta.2": "2014-07-08T22:30:18.422Z", "1.0.1": "2015-01-07T23:02:00.560Z", "1.0.2": "2015-03-10T11:05:42.353Z", "1.1.0": "2016-04-01T16:08:46.729Z", "1.1.1": "2016-04-05T22:54:41.361Z", "2.0.0": "2016-04-09T12:09:03.183Z", "2.0.1": "2016-04-16T19:26:57.739Z", "2.0.2": "2016-04-16T22:46:13.415Z", "2.1.0": "2016-05-03T23:20:31.548Z", "2.1.1": "2016-05-07T12:19:09.993Z", "2.1.2": "2016-05-08T15:31:42.865Z", "2.1.3": "2016-05-12T22:47:32.090Z", "2.1.4": "2016-05-26T23:00:21.670Z", "2.1.5": "2016-06-12T17:42:30.584Z", "2.1.6": "2016-06-14T23:33:57.765Z", "2.2.0-rc.1": "2016-07-13T23:59:51.916Z", "2.2.0-rc.2": "2016-07-19T23:01:43.899Z", "2.2.0": "2016-07-28T22:32:18.749Z", "2.2.1": "2016-07-30T17:10:53.638Z", "2.2.2": "2016-08-27T12:40:35.906Z", "2.3.0-rc.1": "2016-09-21T19:22:40.132Z", "2.3.0": "2016-09-22T19:07:43.347Z", "2.4.0": "2016-11-12T18:01:22.024Z", "2.5.0": "2017-02-08T19:52:32.099Z", "2.6.0": "2017-05-25T14:07:28.372Z", "2.7.0": "2017-09-10T17:37:27.724Z", "2.7.1": "2017-10-28T15:10:42.608Z", "2.7.2": "2018-03-01T21:51:49.699Z", "2.7.3": "2018-10-15T17:21:23.545Z", "2.8.0-rc.1": "2019-03-04T12:31:29.465Z", "2.8.0": "2019-03-14T13:08:34.607Z", "2.9.0": "2019-10-26T00:36:21.788Z", "2.9.1": "2019-10-27T19:22:45.195Z", "2.9.2": "2019-11-02T19:32:41.934Z", "2.9.3": "2019-11-14T18:42:54.703Z", "3.0.0-alpha": "2020-03-12T00:59:43.787Z", "3.0.0-alpha.2": "2020-07-17T21:07:40.534Z", "3.0.0-beta": "2020-09-01T15:57:32.933Z", "3.0.0-beta.2": "2020-10-01T11:02:35.678Z", "3.0.0-beta.3": "2020-10-01T13:10:43.528Z", "3.0.0-beta.4": "2020-10-15T21:08:56.699Z", "2.9.4": "2020-10-18T18:24:37.338Z", "3.0.0-beta.5": "2020-11-01T13:36:00.651Z", "3.0.0-beta.6": "2020-11-08T15:20:39.831Z", "3.0.0-beta.7": "2020-12-04T21:03:33.830Z", "3.0.0-beta.8": "2021-01-13T12:56:09.709Z", "3.0.0-beta.9": "2021-01-16T19:22:49.475Z", "3.0.0-beta.10": "2021-02-01T21:48:51.778Z", "3.0.0-beta.11": "2021-02-20T15:16:39.258Z", "3.0.0-beta.12": "2021-02-27T18:40:21.466Z", "3.0.0-beta.13": "2021-03-06T15:57:06.776Z", "3.0.0-beta.14": "2021-03-16T21:29:33.423Z", "3.0.0-rc": "2021-03-18T21:39:33.146Z", "3.0.0-rc.2": "2021-03-21T15:58:43.240Z", "3.0.0-rc.3": "2021-03-23T20:23:48.565Z", "3.0.0-rc.4": "2021-03-25T19:25:31.427Z", "3.0.0-rc.5": "2021-03-25T22:13:35.992Z", "3.0.0-rc.6": "2021-03-27T21:42:44.004Z", "3.0.0-rc.7": "2021-04-02T13:45:16.341Z", "3.0.0": "2021-04-02T19:10:26.505Z", "3.0.1": "2021-04-02T20:35:41.856Z", "3.0.2": "2021-04-04T13:38:33.720Z", "3.1.0": "2021-04-10T20:22:16.988Z", "3.1.1": "2021-04-17T12:04:49.443Z", "3.2.0": "2021-04-24T20:23:20.496Z", "3.2.1": "2021-05-01T18:17:11.249Z", "3.3.0": "2021-05-24T12:19:32.944Z", "3.3.1": "2021-05-29T21:44:22.250Z", "3.3.2": "2021-05-30T19:12:12.370Z", "3.4.0": "2021-06-26T16:23:35.398Z", "3.4.1": "2021-07-04T11:39:08.780Z", "3.5.0": "2021-07-25T12:07:42.811Z", "3.5.1": "2021-08-18T12:25:11.563Z", "3.6.0": "2021-10-23T18:09:40.959Z", "3.6.1": "2021-11-30T18:33:57.681Z", "3.6.2": "2021-12-05T13:50:28.033Z", "3.7.0": "2021-12-23T19:08:41.862Z", "3.7.1": "2022-02-12T14:54:23.965Z", "3.8.0": "2022-05-25T14:15:59.892Z", "3.8.1": "2022-07-24T15:50:01.915Z", "3.8.2": "2022-07-24T18:51:32.737Z", "3.9.0": "2022-08-02T13:05:29.395Z", "3.9.1": "2022-08-03T12:51:30.873Z", "4.0.0-alpha.2": "2022-10-12T17:13:45.786Z", "4.0.0-alpha.3": "2022-10-24T06:03:11.096Z", "4.0.0-release": "2022-11-14T22:54:55.251Z", "4.0.1": "2022-11-14T23:30:27.673Z", "4.1.0": "2022-12-16T06:54:14.059Z", "4.1.1": "2022-12-17T16:16:41.270Z", "4.1.2": "2023-01-05T16:13:23.118Z", "4.2.0": "2023-01-18T13:57:53.996Z", "4.2.1": "2023-02-10T06:50:30.400Z", "4.3.0": "2023-04-28T06:23:49.409Z", "4.3.1": "2023-07-24T19:49:59.551Z", "4.3.2": "2023-07-26T15:43:38.483Z", "4.3.3": "2023-08-02T23:15:31.140Z", "4.4.0": "2023-08-24T12:47:04.927Z", "4.4.1": "2023-12-04T19:29:39.996Z", "4.4.2": "2024-02-28T16:53:42.784Z", "4.4.3": "2024-05-17T12:56:13.736Z", "4.4.4": "2024-08-20T20:34:16.010Z", "4.4.5": "2024-10-15T18:58:46.538Z", "4.4.6": "2024-10-28T13:43:42.509Z", "4.4.7": "2024-12-01T16:47:46.119Z", "4.4.8": "2025-02-19T15:48:26.514Z", "4.4.9": "2025-04-15T13:26:50.673Z"}, "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "license": "MIT", "homepage": "https://www.chartjs.org", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "repository": {"type": "git", "url": "git+https://github.com/chartjs/Chart.js.git"}, "description": "Simple HTML5 charts using the canvas element.", "maintainers": [{"name": "nnnick", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "tanner<PERSON><PERSON>@gmail.com"}, {"name": "etimberg", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chartjs-ci", "email": "<EMAIL>"}], "readme": "<p align=\"center\">\n  <a href=\"https://www.chartjs.org/\" target=\"_blank\">\n    <img src=\"https://www.chartjs.org/media/logo-title.svg\" alt=\"https://www.chartjs.org/\"><br/>\n  </a>\n    Simple yet flexible JavaScript charting for designers & developers\n</p>\n\n<p align=\"center\">\n    <a href=\"https://www.chartjs.org/docs/latest/getting-started/installation.html\"><img src=\"https://img.shields.io/github/release/chartjs/Chart.js.svg?style=flat-square&maxAge=600\" alt=\"Downloads\"></a>\n    <a href=\"https://github.com/chartjs/Chart.js/actions?query=workflow%3ACI+branch%3Amaster\"><img alt=\"GitHub Workflow Status\" src=\"https://img.shields.io/github/actions/workflow/status/chartjs/Chart.js/ci.yml?branch=master&style=flat-square\"></a>\n    <a href=\"https://coveralls.io/github/chartjs/Chart.js?branch=master\"><img src=\"https://img.shields.io/coveralls/chartjs/Chart.js.svg?style=flat-square&maxAge=600\" alt=\"Coverage\"></a>\n    <a href=\"https://github.com/chartjs/awesome\"><img src=\"https://awesome.re/badge-flat2.svg\" alt=\"Awesome\"></a>\n    <a href=\"https://discord.gg/HxEguTK6av\"><img src=\"https://img.shields.io/badge/discord-chartjs-blue?style=flat-square&maxAge=3600\" alt=\"Discord\"></a>\n</p>\n\n## Documentation\n\nAll the links point to the new version 4 of the lib.\n\n* [Introduction](https://www.chartjs.org/docs/latest/)\n* [Getting Started](https://www.chartjs.org/docs/latest/getting-started/index)\n* [General](https://www.chartjs.org/docs/latest/general/data-structures)\n* [Configuration](https://www.chartjs.org/docs/latest/configuration/index)\n* [Charts](https://www.chartjs.org/docs/latest/charts/line)\n* [Axes](https://www.chartjs.org/docs/latest/axes/index)\n* [Developers](https://www.chartjs.org/docs/latest/developers/index)\n* [Popular Extensions](https://github.com/chartjs/awesome)\n* [Samples](https://www.chartjs.org/samples/)\n\nIn case you are looking for an older version of the docs, you will have to specify the specific version in the url like this: [https://www.chartjs.org/docs/2.9.4/](https://www.chartjs.org/docs/2.9.4/)\n\n## Contributing\n\nInstructions on building and testing Chart.js can be found in [the documentation](https://www.chartjs.org/docs/master/developers/contributing.html#building-and-testing). Before submitting an issue or a pull request, please take a moment to look over the [contributing guidelines](https://www.chartjs.org/docs/master/developers/contributing) first. For support, please post questions on [Stack Overflow](https://stackoverflow.com/questions/tagged/chart.js) with the `chart.js` tag.\n\n## License\n\nChart.js is available under the [MIT license](LICENSE.md).\n", "readmeFilename": "README.md", "users": {"xch": true, "lmhs": true, "nazy": true, "phntm": true, "timdp": true, "yurii": true, "bumsuk": true, "figroc": true, "itcorp": true, "klaemo": true, "moueza": true, "rexpan": true, "yeming": true, "yuch4n": true, "zivlit": true, "ajoseph": true, "filipve": true, "juanf03": true, "kkho595": true, "pmuellr": true, "ungurys": true, "ayad0net": true, "edloidas": true, "qddegtya": true, "razor164": true, "sappharx": true, "theyssen": true, "abuelwafa": true, "brezinajn": true, "edmondnow": true, "geofftech": true, "guzgarcia": true, "jonalport": true, "npmmurali": true, "streaper2": true, "agamlarage": true, "cfleschhut": true, "chartjs-ci": true, "dccunni171": true, "onursimsek": true, "princetoad": true, "quantalabs": true, "rocket0191": true, "salvationz": true, "bogdanvlviv": true, "flumpus-dev": true, "hongbo-miao": true, "jimenezmm-p": true, "marcovossen": true, "ninabreznik": true, "sammyteahan": true, "simonbrunel": true, "fagnerlima91": true, "warcrydoggie": true, "wesleylhandy": true, "chinawolf_wyp": true, "markthethomas": true, "piyushmakhija": true, "tranceyos2419": true, "thatwasawkward": true, "astratyandmitry": true, "matthew-campbell": true, "omkar.sheral.1989": true}}