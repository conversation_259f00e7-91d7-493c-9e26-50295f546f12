{"_id": "ini", "_rev": "107-60543002d7e8b27cf981a5571ba6b3a0", "name": "ini", "dist-tags": {"latest": "5.0.0"}, "versions": {"1.0.0": {"name": "ini", "version": "1.0.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "ini@1.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "a17171a4fc9149e26c7951e28db44b1ffab40676", "tarball": "https://registry.npmjs.org/ini/-/ini-1.0.0.tgz", "integrity": "sha512-99r6fMZgTw+eSjigzfSKxuT9eQC7mxjX6iJ+O49/Hr+YmSgvVBfFzkPqSkrKG5jyQ/fiJVFcIZ1ck7NnoDwstA==", "signatures": [{"sig": "MEUCIC42K0Sc9ctoPPWxiAyEdcIKw//CgRVVlMkxaNrWcRAmAiEAxXk2u1jqkFgMjVwKuhsFI+bxiG6xoXvud+GO+bO01gg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "ini.js", "engines": {"node": "*"}, "scripts": {"test": "tap test/*.js"}, "deprecated": "Please update to ini >=1.3.6 to avoid a prototype pollution issue", "repository": {"url": "git://github.com/isaacs/ini.git", "type": "git"}, "_npmVersion": "1.0.22", "description": "An ini encoder/decoder for node", "directories": {}, "_nodeVersion": "v0.4.11-pre", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/ini/1.0.0/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"tap": "~0.0.9"}, "_engineSupported": true}, "1.0.1": {"name": "ini", "version": "1.0.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "ini@1.0.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "09de7da168015db47155b981bc18821e7ee4a1f6", "tarball": "https://registry.npmjs.org/ini/-/ini-1.0.1.tgz", "integrity": "sha512-BjulF9Obt8duZyu0fYB/9og65m05op2r1xdX67tAOOC2/j9DWO4G1A+rZk3ON4Usb06UZBgOGVKVLv1dDobVuw==", "signatures": [{"sig": "MEUCICbLZyAEW1zb2Ur1GiDkKpJ9u4e5Qmfnv9JwZ5NAh/XqAiEA9rXvoLJbV4KB8R27H7hRMP5QAV4ZQ2eu+G1Wgqso53s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "ini.js", "engines": {"node": "*"}, "scripts": {"test": "tap test/*.js"}, "deprecated": "Please update to ini >=1.3.6 to avoid a prototype pollution issue", "repository": {"url": "git://github.com/isaacs/ini.git", "type": "git"}, "_npmVersion": "1.0.28-pre-DEV-UNSTABLE", "description": "An ini encoder/decoder for node", "directories": {}, "_nodeVersion": "v0.5.7-pre", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/ini/1.0.1/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"tap": "~0.0.9"}, "_engineSupported": true}, "1.0.2": {"name": "ini", "version": "1.0.2", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "ini@1.0.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "f240a2e4af74cf012440983b1718210fb75a325c", "tarball": "https://registry.npmjs.org/ini/-/ini-1.0.2.tgz", "integrity": "sha512-NoE7lyha5vATUCzfmugJ6p64OU7/qGNTfu85h/o1IBmmjm1HHSiBuNORqe2ZZawSH9KHlCh0uzfYgFp6DKGqjQ==", "signatures": [{"sig": "MEUCIFKSTEsxpuFHiY3yPnrglPS5zZKqcXmSp8jn/TRw7699AiEAxxMp6Z+PaCRI8Le5OuGWI89UntnQ+oFJkJ+FtviXoiQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "ini.js", "engines": {"node": "*"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to ini >=1.3.6 to avoid a prototype pollution issue", "repository": {"url": "git://github.com/isaacs/ini.git", "type": "git"}, "_npmVersion": "1.1.0-beta-10", "description": "An ini encoder/decoder for node", "directories": {}, "_nodeVersion": "v0.6.7-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"tap": "~0.0.9"}, "_engineSupported": true}, "1.0.3": {"name": "ini", "version": "1.0.3", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "ini@1.0.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "a15e830255035b0c4540c1a14b35b6bfff0f00f0", "tarball": "https://registry.npmjs.org/ini/-/ini-1.0.3.tgz", "integrity": "sha512-PaSDIwRyNH8arFf9/HhghoyRQjDpct/HZJMdXuz04AXadrUeKXI0xDf96H/fh5qaI3WujO0COSU7Qxh4P3D+TQ==", "signatures": [{"sig": "MEUCIQDVuJP8PBTHtpIxjSsPhUq0tDJUok/el/6XzWIe9gVxZgIgRkhr3zuIZi6wKAN+Ftvc6uyR39fBDNrrav1/f76vGzQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "ini.js", "engines": {"node": "*"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to ini >=1.3.6 to avoid a prototype pollution issue", "repository": {"url": "git://github.com/isaacs/ini.git", "type": "git"}, "_npmVersion": "1.1.48", "description": "An ini encoder/decoder for node", "directories": {}, "dependencies": {}, "devDependencies": {"tap": "~0.0.9"}}, "1.0.4": {"name": "ini", "version": "1.0.4", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "ini@1.0.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "da7aee1f450192be7405be1f719683c7574527c1", "tarball": "https://registry.npmjs.org/ini/-/ini-1.0.4.tgz", "integrity": "sha512-Gs8060NWgpMRsBEH1WWXDG1+l4qKT4ImoO0hpZGyYqAnqTosTn6JfO84yhus78s6RHjOpFBjCYBIHKbT4WVEGQ==", "signatures": [{"sig": "MEUCIQDfs5R+TfoI5ZyiByq4RWBcj1zo3Q6bE2/7Qq01LqAuQgIgReruHM50EaDJwj4vAmpqM71AAfjyEhgNFIxx2OrtfSA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "ini.js", "engines": {"node": "*"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "", "email": "<EMAIL>"}, "deprecated": "Please update to ini >=1.3.6 to avoid a prototype pollution issue", "repository": {"url": "git://github.com/isaacs/ini.git", "type": "git"}, "_npmVersion": "1.1.48", "description": "An ini encoder/decoder for node", "directories": {}, "dependencies": {}, "devDependencies": {"tap": "~0.0.9"}}, "1.0.5": {"name": "ini", "version": "1.0.5", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "ini@1.0.5", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "4066aca7252b2783c592decf925500eb7cbf5da6", "tarball": "https://registry.npmjs.org/ini/-/ini-1.0.5.tgz", "integrity": "sha512-JDeFPovNri2M1P7BwAGR81wYvYDQflGcF9XduZdMZu5fFCvO0VH6x4Ybsk++9xpwTKy9HtRdT9A5icSV/t0a6Q==", "signatures": [{"sig": "MEUCICV2GUAPV83DNgrdAXNKUWeYTyH2X1ntBRKdAKTfacdpAiEAtw62ua1AI3uFZHL9ghNw86e2N+XnrRujrA9OCo5ZcW0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "ini.js", "engines": {"node": "*"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to ini >=1.3.6 to avoid a prototype pollution issue", "repository": {"url": "git://github.com/isaacs/ini.git", "type": "git"}, "_npmVersion": "1.1.62", "description": "An ini encoder/decoder for node", "directories": {}, "dependencies": {}, "devDependencies": {"tap": "~0.0.9"}}, "1.1.0": {"name": "ini", "version": "1.1.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "ini@1.1.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "4e808c2ce144c6c1788918e034d6797bc6cf6281", "tarball": "https://registry.npmjs.org/ini/-/ini-1.1.0.tgz", "integrity": "sha512-B6L/jfyFRcG2dqKiHggWnfby52Iy07iabE4F6srQAr/OmVKBRE5uU+B5MQ+nQ7NiYnjz93gENh1GhqHzpDgHgA==", "signatures": [{"sig": "MEUCIH4Z6ypHPM5NEYhTgJYF2YVJuFzwvoY+d2GFHWYECo9hAiEA5Z4t064N+FFM+Duo+dG08chdWgOKP67bwI3e2LfG0Hw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "ini.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to ini >=1.3.6 to avoid a prototype pollution issue", "repository": {"url": "git://github.com/isaacs/ini.git", "type": "git"}, "_npmVersion": "1.2.2", "description": "An ini encoder/decoder for node", "directories": {}, "dependencies": {}, "devDependencies": {"tap": "~0.0.9"}}, "1.2.0": {"name": "ini", "version": "1.2.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "ini@1.2.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/ini", "bugs": {"url": "https://github.com/isaacs/ini/issues"}, "dist": {"shasum": "2cc36789605809930722e793ae13ac835e623ac6", "tarball": "https://registry.npmjs.org/ini/-/ini-1.2.0.tgz", "integrity": "sha512-Ev4t2OsQR0w7R86ZUv3At1FGYcxGQk/eMmzhVHIbHNBFVtO6fQVuXgjBlh2oLEawUVyIDr9VCghAn/81za5SBw==", "signatures": [{"sig": "MEQCIB1lLkKdFmhaqSdq22D6J1V6JulRsinv5ecrqMN4JsgbAiA+xefOxBzV0kNMEKcOau/jx6Wh1+WjZQdu5Stj5qFj6A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "ini.js", "_from": ".", "_shasum": "2cc36789605809930722e793ae13ac835e623ac6", "engines": {"node": "*"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to ini >=1.3.6 to avoid a prototype pollution issue", "repository": {"url": "git://github.com/isaacs/ini.git", "type": "git"}, "_npmVersion": "1.4.10", "description": "An ini encoder/decoder for node", "directories": {}, "dependencies": {}, "devDependencies": {"tap": "~0.4.0"}}, "1.2.1": {"name": "ini", "version": "1.2.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "ini@1.2.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/ini", "bugs": {"url": "https://github.com/isaacs/ini/issues"}, "dist": {"shasum": "7f774e2f22752cd1dacbf9c63323df2a164ebca3", "tarball": "https://registry.npmjs.org/ini/-/ini-1.2.1.tgz", "integrity": "sha512-PPRGV0RPXb9U748Lxc17NPoSXcsXaglLchPRwpXSGnUnp+aSVPyxwDod4BX1WDLovubZWGmezFyBwy4FwQOLCQ==", "signatures": [{"sig": "MEQCIFq0rGMQlG85mTgbAzrib6cMeH0UsLmkxYm8LXTvL3AEAiAKPUpK1f5Q6Us34+un0g7eAaryrG4J/m0xiSqW0NiBxw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "ini.js", "_from": ".", "_shasum": "7f774e2f22752cd1dacbf9c63323df2a164ebca3", "engines": {"node": "*"}, "gitHead": "13498ce1ba5a6a20cd77ed2b55de0e714786f70c", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to ini >=1.3.6 to avoid a prototype pollution issue", "repository": {"url": "git://github.com/isaacs/ini.git", "type": "git"}, "_npmVersion": "1.4.11", "description": "An ini encoder/decoder for node", "directories": {}, "dependencies": {}, "devDependencies": {"tap": "~0.4.0"}}, "1.3.0": {"name": "ini", "version": "1.3.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "ini@1.3.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/ini", "bugs": {"url": "https://github.com/isaacs/ini/issues"}, "dist": {"shasum": "625483e56c643a7721014c76604d3353f44bd429", "tarball": "https://registry.npmjs.org/ini/-/ini-1.3.0.tgz", "integrity": "sha512-6tyfJkFAmQV64x9Li007PNZgNxqRywig4Rv8PDfzU9kgDBmURaIvjSL2wKyx45LPSIXsvDkFGbOMqMHC1PsORA==", "signatures": [{"sig": "MEQCIBKR49bUrJ/HIZtZZL3ivJpZQhAf9u6F67wbAvRAUzEnAiBEynNl/1xdwHpB7RjL3zM17rE1fF3TjZ3ATkqhVKn56A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "ini.js", "_from": ".", "_shasum": "625483e56c643a7721014c76604d3353f44bd429", "engines": {"node": "*"}, "gitHead": "6c314944d0201f3199e1189aeb5687d0aaf1c575", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to ini >=1.3.6 to avoid a prototype pollution issue", "repository": {"url": "git://github.com/isaacs/ini.git", "type": "git"}, "_npmVersion": "2.0.0", "description": "An ini encoder/decoder for node", "directories": {}, "dependencies": {}, "devDependencies": {"tap": "~0.4.0"}}, "1.3.1": {"name": "ini", "version": "1.3.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "ini@1.3.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/ini", "bugs": {"url": "https://github.com/isaacs/ini/issues"}, "dist": {"shasum": "cae65f770d728f242fe082dce4bb624ae957dcd0", "tarball": "https://registry.npmjs.org/ini/-/ini-1.3.1.tgz", "integrity": "sha512-03tWrJfLmAJYUAhNOTxKLm/DLcsdy4Exh+FFzbese4n/iMu0vY1YvkJRH/kPNY0CDOw0hTDfq2ODj+PVxcHt4g==", "signatures": [{"sig": "MEYCIQDe2MIcZEQeMhyn6m/ZMlsyrjAvP1DvDZiduuOhGDJYVQIhAJ7PnJSSJNC+jOtKLW8KawpI82wN0Y33I6oNOuQpyi77", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "ini.js", "_from": ".", "_shasum": "cae65f770d728f242fe082dce4bb624ae957dcd0", "engines": {"node": "*"}, "gitHead": "f473c0caab23f0f09fbaf2d8f67abc57382e368f", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to ini >=1.3.6 to avoid a prototype pollution issue", "repository": {"url": "git://github.com/isaacs/ini.git", "type": "git"}, "_npmVersion": "2.1.9", "description": "An ini encoder/decoder for node", "directories": {}, "_nodeVersion": "0.10.16", "dependencies": {}, "devDependencies": {"tap": "~0.4.0"}}, "1.3.2": {"name": "ini", "version": "1.3.2", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "ini@1.3.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/ini", "bugs": {"url": "https://github.com/isaacs/ini/issues"}, "dist": {"shasum": "9ebf4a44daf9d89acd07aab9f89a083d887f6dec", "tarball": "https://registry.npmjs.org/ini/-/ini-1.3.2.tgz", "integrity": "sha512-MY7ajXs4KbH53Elm7p71YfO63NgoF8RdofcAtExPlDFIy2MJMvLIGW4lv6VCuPYytXeMd+bZvB++SthUVzsztA==", "signatures": [{"sig": "MEYCIQDj4iQO7dA9YTCh/yBYvLHv61ZLk31dXtVRookH+lo8xwIhAM7XeKpcalJkR1pYLdDiaomk5lnt2zt+3tOgHt6+Debf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "ini.js", "_from": ".", "_shasum": "9ebf4a44daf9d89acd07aab9f89a083d887f6dec", "engines": {"node": "*"}, "gitHead": "bbe4a8bb09afa58f724c04ce43a49037cabeadfb", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to ini >=1.3.6 to avoid a prototype pollution issue", "repository": {"url": "git://github.com/isaacs/ini.git", "type": "git"}, "_npmVersion": "2.1.9", "description": "An ini encoder/decoder for node", "directories": {}, "_nodeVersion": "0.10.16", "dependencies": {}, "devDependencies": {"tap": "~0.4.0"}}, "1.3.3": {"name": "ini", "version": "1.3.3", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "ini@1.3.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/ini", "bugs": {"url": "https://github.com/isaacs/ini/issues"}, "dist": {"shasum": "c07e34aef1de06aff21d413b458e52b21533a11e", "tarball": "https://registry.npmjs.org/ini/-/ini-1.3.3.tgz", "integrity": "sha512-Qp/z/qr17safqtf5pLnFkZrNxPjmp8zWjE4o1P1+QnMI6APlzdbmOiszIUJDeOupOI4jf65cRJNFcOFaumhTlw==", "signatures": [{"sig": "MEUCIBLP8Wa7x/wsu+piFwkTVOjWPSUH9X/xUBP7sD7GgGGlAiEAwfK1H8RWBpfhysfalIU//KYS3zpFlr4wBidX5qidn5k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "ini.js", "_from": ".", "_shasum": "c07e34aef1de06aff21d413b458e52b21533a11e", "engines": {"node": "*"}, "gitHead": "566268f1fb8dd3c0f7d968091de7b7fb2b97b483", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to ini >=1.3.6 to avoid a prototype pollution issue", "repository": {"url": "git://github.com/isaacs/ini.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "An ini encoder/decoder for node", "directories": {}, "_nodeVersion": "1.1.0", "dependencies": {}, "devDependencies": {"tap": "~0.4.0"}}, "1.3.4": {"name": "ini", "version": "1.3.4", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "ini@1.3.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/ini#readme", "bugs": {"url": "https://github.com/isaacs/ini/issues"}, "dist": {"shasum": "0537cb79daf59b59a1a517dff706c86ec039162e", "tarball": "https://registry.npmjs.org/ini/-/ini-1.3.4.tgz", "integrity": "sha512-VUA7WAWNCWfm6/8f9kAb8Y6iGBWnmCfgFS5dTrv2C38LLm1KUmpY388mCVCJCsMKQomvOQ1oW8/edXdChd9ZXQ==", "signatures": [{"sig": "MEYCIQC7tiwI6F4kyJiiS+sS3ZlUXD6SgYNLgd6A7MVqe4THSQIhAJWu9skXWXjntvQRo1Y6IeRTboulTDTS9KBoPeTxKwd0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "ini.js", "_from": ".", "files": ["ini.js"], "_shasum": "0537cb79daf59b59a1a517dff706c86ec039162e", "engines": {"node": "*"}, "gitHead": "4a3001abc4c608e51add9f1d2b2cadf02b8e6dea", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to ini >=1.3.6 to avoid a prototype pollution issue", "repository": {"url": "git://github.com/isaacs/ini.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "An ini encoder/decoder for node", "directories": {}, "_nodeVersion": "2.0.1", "dependencies": {}, "devDependencies": {"tap": "^1.2.0"}}, "1.3.5": {"name": "ini", "version": "1.3.5", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "ini@1.3.5", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/ini#readme", "bugs": {"url": "https://github.com/isaacs/ini/issues"}, "dist": {"shasum": "eee25f56db1c9ec6085e0c22778083f596abf927", "tarball": "https://registry.npmjs.org/ini/-/ini-1.3.5.tgz", "integrity": "sha512-RZY5huIKCMRWDUqZlEi72f/lmXKMvuszcMBduliQ3nnWbx9X/ZBQO7DijMEYS9EhHBb2qacRUMtC7svLwe0lcw==", "signatures": [{"sig": "MEUCIFp7b3BMVoI+HaFQEhlWAXUmVu5jLYFFJ8dENwboRmklAiEA7qeLcWt6dL9nzZosF15ykYGrgrc//3/DGVAcFX2Cs5k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "ini.js", "files": ["ini.js"], "engines": {"node": "*"}, "gitHead": "738eca59d77d8cfdddf5c477c17a0d8f8fbfe0fd", "scripts": {"test": "tap test/*.js --100 -J", "pretest": "standard ini.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Please update to ini >=1.3.6 to avoid a prototype pollution issue", "repository": {"url": "git://github.com/isaacs/ini.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "An ini encoder/decoder for node", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {}, "devDependencies": {"tap": "^10.7.3 || 11", "standard": "^10.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/ini-1.3.5.tgz_1511302634290_0.6889052151236683", "host": "s3://npm-registry-packages"}}, "1.3.6": {"name": "ini", "version": "1.3.6", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "ini@1.3.6", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/ini#readme", "bugs": {"url": "https://github.com/isaacs/ini/issues"}, "dist": {"shasum": "f1c46a2a93a253e7b3905115e74d527cd23061a1", "tarball": "https://registry.npmjs.org/ini/-/ini-1.3.6.tgz", "fileCount": 4, "integrity": "sha512-IZUoxEjNjubzrmvzZU4lKP7OnYmX72XRl3sqkfJhBKweKi5rnGi5+IUdlj/H1M+Ip5JQ1WzaDMOBRY90Ajc5jg==", "signatures": [{"sig": "MEUCIHp7ZRCTjqx8KxeFFYhQiXIddd8ej2YQePvThm62PmtjAiEAlAMsPug+ar8mco/A/9MD/l5G4YVLxM5FsUGmQEqDtp8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9152, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf0BAtCRA9TVsSAnZWagAA8l4QAIE5GsCVftJRiwr/EG8V\nyPIblrtOz+CWwPTiRRsXYc3A6vxrZyMcbLzcJUbLGMhUCK7PJiVt/aub9OmO\n0Ns5iUuTL1Wm8nGLI7u/+b/RWkES2RIak+OuVSdEspC4Qog1KX6jeuUj9IQi\nEYD5TcaNMOlTZ5zBE4BC3cIn54YjQXUSTrg5XX45bp/bLCM7ZrJUeSjKbBp0\ncWPA0ioN1GbT7lKoxZd2q0by5Df+yU1XDiyTIxwcsj99i/3y05n4Mzyhpaso\nthcIP1E9qnhK3F+J+UcPidw1h3YmkowN48b37JOlX78Q3snbLGOPd71N1+18\nhotCwsmBS0+OSuSiV0bfC2zZWXL+YSIKy+Er29DOEkA6fH5sZfUVaBmW68b4\nAACJZprXdqepIHx1I3RWTtKpFZGh393/kloXtHFCvoBb9tqQl44bn6bYq5rH\nk4r4cBZtwKmcv1s3hBQb6fjYhayz4Hb35bcWrncAmixq3jG4bLxJWDWAhweu\npIpe2A3rHVGvr9bC/N0f4acqdiW825wK+t3Gw2fjF+lj2ngnBGS1QqXUxv8v\nz7KBj/fN59V9E9mrJclJFC4Ai5tsa0rtC6wSHxonD3d2mm6QiEUalIG7fPeh\nNPTrR+uvQB7URUhC+X3xDVW9aWyqxJipqpMFUul17OXlAPYsNEPYHVoAygMh\noKDv\r\n=G3ig\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "ini.js", "engines": {"node": "*"}, "gitHead": "2da90391ef70db41d10f013e3a87f9a8c5d01a72", "scripts": {"test": "tap test/*.js --100 -J", "pretest": "standard ini.js", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/ini.git", "type": "git"}, "_npmVersion": "7.1.0", "description": "An ini encoder/decoder for node", "directories": {}, "_nodeVersion": "15.3.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^10.7.3 || 11", "standard": "^10.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/ini_1.3.6_1607471149083_0.4236268576272688", "host": "s3://npm-registry-packages"}}, "1.3.7": {"name": "ini", "version": "1.3.7", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "ini@1.3.7", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/ini#readme", "bugs": {"url": "https://github.com/isaacs/ini/issues"}, "dist": {"shasum": "a09363e1911972ea16d7a8851005d84cf09a9a84", "tarball": "https://registry.npmjs.org/ini/-/ini-1.3.7.tgz", "fileCount": 4, "integrity": "sha512-iKpRpXP+CrP2jyrxvg1kMUpXDyRUFDWurxbnVT1vQPx+Wz9uCYsMIqYuSBLV+PAaZG/d7kRLKRFc9oDMsH+mFQ==", "signatures": [{"sig": "MEQCIF2/PFtx21RJNFYnPJFrATKRcJXSDopH9AzQ293qbCijAiAwB8NKECnQA/jfklj6a8VD6QzywcF19OmzyMoc1BiLkQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9389, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf0Vq3CRA9TVsSAnZWagAAhJcP/2gjFvkLz7euhu8yQOuz\n7zLG/WToJra12ECuDWAr/8Qmyq26sWCqI1/vlzU58ufqgtfwW8PRPQkHUyx9\n3KQOMtHaRIBjFvGGERRnLDjSEPZSw3YVmZeYagfCsvyNB4IRqY4tjOK8dtOd\ni+imG0AUHd/ZBzFhqYN+ZjfdouyT8oHTQj3es1evQdWMP3yHDnnL0ChI7Wnb\n9g+5TAUK5PPJZP9OPPCDBSZ0vtX19p9O34gy+OBDwNlo+jGL8RuQ6MgqsEmH\npjOYnEVQFCQYsZy9W2voxmhf111+FI3S660zStJwGFvOWkjsI8uOR+W+J0yN\n/ioBVbMc3gSUWqi0bujDyS4X+l83oSSyI9NTvJHLIy18liG6J+c1nFdgSM5p\nlWwsaeXmUxGTM9vLrPl/EzcZqDhEDfSV4N8s3bf81GGXHqLNTOsbUdfXh2+m\nam0OJPKtDmNG0KLGA5CCHO5u3NJSYM35w0NhCIAoNpzYo0HNrp9hH+Z6F78n\ntGnLOlYKznGG9gH7UbADtkHx6Nf9doCscbrbrKlp8AI5bQN3Fa3UFOFuz7B3\nCrY0wmwmL5/FpNtgKYLgdNClee/2ahwW76woJ5M2IcHZdwnkS3cUiXPM/440\nXtEXq9xhYpYpEIeHjpLguutGO8O5NVzK72F9MnbY4b3+yzQ1HCEIbXVOZrq5\nggeU\r\n=ibzr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "ini.js", "gitHead": "c74c8af35f32b801a7e82a8309eab792a95932f6", "scripts": {"lint": "npm run eslint -- ini.js test/*.js", "test": "tap", "eslint": "eslint", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/ini.git", "type": "git"}, "_npmVersion": "7.1.1", "description": "An ini encoder/decoder for node", "directories": {}, "_nodeVersion": "15.3.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "14", "eslint": "^7.9.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/ini_1.3.7_1607555766552_0.033866035842485154", "host": "s3://npm-registry-packages"}}, "1.3.8": {"name": "ini", "version": "1.3.8", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "ini@1.3.8", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/ini#readme", "bugs": {"url": "https://github.com/isaacs/ini/issues"}, "dist": {"shasum": "a29da425b48806f34767a4efce397269af28432c", "tarball": "https://registry.npmjs.org/ini/-/ini-1.3.8.tgz", "fileCount": 4, "integrity": "sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew==", "signatures": [{"sig": "MEUCIQCNn5qh0UVxtt5cDLOojt5YTYEK8775LcmaFWD1NkDBNAIgaXdrm+lT+9OO53Rmvw4ivNNwllC/P8Jdoyeemtpqfi8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9304, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf089FCRA9TVsSAnZWagAAhiUP/2p9rtYlqZRuGB0YSy7L\nfVJuBNhvCpv2RnGDFKdOku2o6RJgVqTzFOkY43jZvZyaMVMdob/a8JoL1c35\nQjkuXRhJs7auxq4hkzpz0NNowXK4Of5JPdHf6FtRJO75Lr9KJTnulIbN9W4F\n57UEXZlifwcBucClwFas3R9J3HZhZJCV6c2edAX1djvfwYiJSgRK5cO1gyXS\n0Xiul1EFROhutQPE4XgVgh2IMIKIrgfsXsoKSgpS2t9Ftwb90ymUQddX+oe9\n4WyqMnJhcq0TWlKq0uGOpu5STvcilrNgz+FRPSfNGwkpGoy9R9ps9UEl1FC2\nb0tBIQOPjiLYOhsWnD9AxVTWtFvU01QIEXZTIbFfftLIAGF4nzpoLCgAs4td\nACzGwCmd16OQMHu4zOIrCu6OcZWz5cP2wWTvVL+UlsR/uoYs9N3Fy8tntCsS\ny1nLEdLb1bBCON5royWm44VufWyq1uHKXNWsc5/OHRenKJGtb4pjBNdnLGVI\nz0g79K9ncT3nhakWjjwrPxxoRlMuId08pD+N3fwhGRYu38swPluLWDRsoOmI\nrnyi4QYnDRjvcjDFzclHCPK/Z4DQgMJUCCluFpDnPuXUsvfH/kkenFVCjVve\n0nRA6SfRlR8jpUruXgvF05m4AA9zO+1262FpzfXAK7UjILKi4ifXi34NzGus\nZn8i\r\n=SzNt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "ini.js", "gitHead": "a2c5da86604bc2238fe393c5ff083bf23a9910eb", "scripts": {"lint": "npm run eslint -- ini.js test/*.js", "test": "tap", "eslint": "eslint", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/ini.git", "type": "git"}, "_npmVersion": "7.1.1", "description": "An ini encoder/decoder for node", "directories": {}, "_nodeVersion": "15.3.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "14", "eslint": "^7.9.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/ini_1.3.8_1607716677084_0.35125949482591934", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "ini", "version": "2.0.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "ini@2.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/ini#readme", "bugs": {"url": "https://github.com/isaacs/ini/issues"}, "dist": {"shasum": "e5fd556ecdd5726be978fa1001862eacb0a94bc5", "tarball": "https://registry.npmjs.org/ini/-/ini-2.0.0.tgz", "fileCount": 4, "integrity": "sha512-7PnF4oN3CvZF23ADhA5wRaYEQpJ8qygSkbtTXWBeXWXmEVRXK+1ITciHWwHhsjv1TmW0MgacIv6hEi5pX5NQdA==", "signatures": [{"sig": "MEYCIQDTFUt/l4ehAGeDP6NKhxxQl8oZdZTRwRgxj5V5ohK5VgIhAOTWQYo2MoEbTKjG6uOhFD8zwg3JOPzbZU3Feco8W/Jo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9474, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2vx/CRA9TVsSAnZWagAASdIQAJmiC52XG/zhC2og0qTC\n8EjXq+6RpGwnkDJHggXyeb3qESAUsjUV+P/TvXODTUE+qZbG8ei+Z6xs4JZY\n5Jv20RcienIY5TCLbP4guxvDWtQO8NwG6q4N0Cq5QdbK3brX1rHL2a1ASjv/\nlHNF0sDpuOze6zqAZ800tSxMz83Y2CFEHin+iTXSvka070cpi40p5s7BHVIg\nconepgumI2NJjRnG367pjB6KTW79/BtwH0WeDqzMjHT0q+yVnUq4oyb+pdNZ\ncqdKu700Op2/SbJJQqT8uZdUOLzROLJUOQDYNVxbvQVvPAjBxVjzCex9Ouyv\nhh6xmU4/Iue58DbHlSWA+M1jHlyyLNnoPr2Y5B+NM2wLRRxw2kzUfvl05wSy\nfF2KfW5Xagb3WyXy7vA+pJpR07bgS94DPngrxfiR3korZo9dOTWUX7W1BQgz\nSc2CXtMIlNNGjN6LB/z2I0WecSkkobcs0bUJICOiYL0VoePiCQpO2BUFZs9g\netmcjhxAWJBREp2P3ougytAlvc3RR6SMMuIRGtK2UOaR1Wwz72BCmG4TZUYn\nHQWx8aH/oh3w/lLWEGFPQ4kkgfR9rgBaflUWw7cY6euvwIGhSTBTwNZYywVD\nmZ+2FCZvfaYxzzKmrXEN0D1X2l3whCafQtTNOF408YQILORJ8ATEf/Q5ww62\nlEp/\r\n=sqQf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "ini.js", "engines": {"node": ">=10"}, "gitHead": "052294959bf60e7d53dec21df2d8328a550829f0", "scripts": {"lint": "npm run eslint -- ini.js test/*.js", "test": "tap", "eslint": "eslint", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/ini.git", "type": "git"}, "_npmVersion": "7.1.1", "description": "An ini encoder/decoder for node", "directories": {}, "_nodeVersion": "15.3.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "14", "eslint": "^7.9.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/ini_2.0.0_1607728022734_0.34424619639478404", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "ini", "version": "3.0.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "ini@3.0.0", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/ini#readme", "bugs": {"url": "https://github.com/npm/ini/issues"}, "dist": {"shasum": "2f6de95006923aa75feed8894f5686165adc08f1", "tarball": "https://registry.npmjs.org/ini/-/ini-3.0.0.tgz", "fileCount": 4, "integrity": "sha512-TxYQaeNW/N8ymDvwAxPyRbhMBtnEwuvaTYpOQkFx1nSeusgezHniEc/l35Vo4iCq/mMiTJbpD7oYxN98hFlfmw==", "signatures": [{"sig": "MEUCIBGscJUf4d1r2lA/OpFvkQMVTOrX1Sybv5m67zK/hVXZAiEAip0XB+TAzvz3UgreNX7wfGkTsrA41c5eRwcyiK8F8/A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9889, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTHg1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr/ZRAAjB+v4VGefKRdJDzz1sImimr5MUt026z4fmc+Yw8v87CVd0jM\r\ntlXa7xXxTEe0TVrkOysnYXqTb2D3AeyC0pjYQ9FuoTZ+dM/HsAHSOtL8Wb3Y\r\nfHinZLlWUNns7dEUHk/xY1pnaq0r4EB3NzNaIUOSLJYlpszfsrAPXk2T9G9G\r\nCDvaawP2ISNMNZvVkoAoDeznkwDF53/E2s16zkYIJD0V3pJUIrUkZ+HM2M5y\r\nicRxbm+SYuaIw0lHO6jjrDGa/yMD2cD5SkpghE0objA17nsytmUGRSWMiBJi\r\nH9gqjCSgqE7ecfb5r0W94OLqBYdJ6Unzn+Zhp0ijQuBrG7/m6LGHGA5nABMu\r\nakhzT5LZxRlEx1ZQGVd0mDNYCy6Q/ubASFG36V/4Fzx4xNcFakj82l1h2riy\r\ngdxjPY2byAinlBIlSPMa2C/H/Zxw+o3Ubm6f5q9JZyu1nootiwF4trNlf3pT\r\nMJ2NNHrdpKtfkF/PSdFijYQpTNR2ZAp9oLe3VtjmeBw7gkkSJLJw83b5MY0G\r\nGtg8+YnKvBNEUT+9bLwx1sSYd7oRaB6rcjDhYJ9UbvtTzzUS/Xf0Ju7aywSM\r\nOfwk3iqEZ4mYLOnB3wZflosnJY+0Tab3zdM8tD/57cYqFR/HEzY2HGEEkznM\r\nvCjt5Pa9or6FehBr9Zz9rLw0Cnxc0/55DC8=\r\n=R4wx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/ini.js", "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "gitHead": "e24ffefd5923fda25210f0c120abc6bb6f8b6989", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "eslint": "eslint", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/ini.git", "type": "git"}, "_npmVersion": "8.6.0", "description": "An ini encoder/decoder for node", "directories": {}, "templateOSS": {"version": "3.2.2", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "16.14.2", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "3.2.2", "@npmcli/eslint-config": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/ini_3.0.0_1649178677463_0.6964541682126153", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "ini", "version": "3.0.1", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "ini@3.0.1", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/ini#readme", "bugs": {"url": "https://github.com/npm/ini/issues"}, "dist": {"shasum": "c76ec81007875bc44d544ff7a11a55d12294102d", "tarball": "https://registry.npmjs.org/ini/-/ini-3.0.1.tgz", "fileCount": 4, "integrity": "sha512-it4HyVAUTKBc6m8e1iXWvXSTdndF7HbdN713+kvLrymxTaU4AUBWrJ4vEooP+V7fexnVD3LKcBshjGGPefSMUQ==", "signatures": [{"sig": "MEUCIBMNrzpNjvEQLdZ+1PLqpMzMmW8eIbXPEtV7heDwg0riAiEA+MkXiT4k+p6aa6kXpwpRLJ22NDH8OkL20IVqKMHxu34=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjA7tjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpSbA/9H1JHODvIUfqMMvA9XDBgeBaRdTOID7uWFagJL4sremtZElch\r\nobIxi2kdHttVbGSOwqCOall8LDzW/1CrbFYy6wnMGugBQ1emReXBHwoNp0sC\r\nA+MjdUAsy3cN1Ool1SGsP3JZremuDTBhaZ0jnCV/eXg/Nia3K+w3kf6YHO60\r\nRw/2QD5a31UY4iCiuiyRouxl1m/1ZK6v8iPfUeqY85pxIHegCDs9nHFnvHz/\r\nwcw2CKpBZnuZg7caL9PDKhGO4Mz1W7TpuhziWAZJCWbdi/AN72J2d9+flDrb\r\ns9IW66N9KK8zuGi+4YY71QxYq7Y0H8pirotSZ/n5bxs4f6G9O/nVlj8xec2J\r\nNUbpoiAerAwDE7JrApleORh+AWJ0ZwRVoL2lNFzFpSlUfBANWhBD9LV7NDzy\r\nI8l1Wyfi5tE4eng9BtC1jrW12Hnjz6VbHjTKXD6fXrIyHfTpd320B2SN2wWM\r\nayG12JojIQbzkLqqBORWj/za1812xbjpdwhD7yCaZLFNJbLPdBRGXHjj2+k3\r\ntMhQ7tGWZ71ywYyJvbCG3fgj68ocFYpdPul5GR95cZeNe36Iow0tfvRbkpyy\r\nhnYyV2S/pArLJmNgvAXyJu55NjoA42lIMW4kJ47wst6bNCNzWrW20ec29BzW\r\naAP1pXvj0IjHvoBA9eXAiauJnSMJLD19sBo=\r\n=5+lz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/ini.js", "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "gitHead": "153678b91f53c1827c1e8e089f721fe7dcc33cd4", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "eslint": "eslint", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/ini.git", "type": "git"}, "_npmVersion": "8.18.0", "description": "An ini encoder/decoder for node", "directories": {}, "templateOSS": {"version": "3.5.0", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.6.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/ini_3.0.1_1661188963684_0.7584805035178581", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "ini", "version": "4.0.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "ini@4.0.0", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/ini#readme", "bugs": {"url": "https://github.com/npm/ini/issues"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}, "dist": {"shasum": "35b4b0ba3bb9a3feb8c50dbf92fb1671efda88eb", "tarball": "https://registry.npmjs.org/ini/-/ini-4.0.0.tgz", "fileCount": 4, "integrity": "sha512-t0ikzf5qkSFqRl1e6ejKBe+Tk2bsQd8ivEkcisyGXsku2t8NvXZ1Y3RRz5vxrDgOrTBOi13CvGsVoI5wVpd7xg==", "signatures": [{"sig": "MEQCHzzcvO82KnvxAW4Btlof+Ok1bkMIZKGxTUagKXcwTJkCIQDq46skKokn3t5fQoWZbVDLb/4zsVnmMBdLso6nW4xHlA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9879, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkGf69ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrsWhAAj5gC1lMARAZt4G3qUdcvXm9hzrJmC+zgj8qnG6lY5MYB6/fB\r\n2KPBD3TzL8xX1rsiYznIP3uCDfUSs0CKFPukQPXIfxGVEUlB2REstFZ1GLT8\r\nWMfPMEFpTV4OCJXIuz9ZGieCNEH50O7YRCojjkMS4pPqrNjMFec4Y3obdfUF\r\nEbHKUVbnrhIzBbzp7ruTc3kD6+n/K1g34NgKnRDVUD1UVSA34IH2obTgKIy+\r\nSDKG2fKMZIGiBK0MoRz/aFi1l5Bk1uomPsQXXB7kj1pS5Qvd4B5NQVu91uez\r\nk6QQjSfZxEimTq8LFrwE6rkJH0eUz3y2UhuKwKz3TM9qVWyI/dPDKFcOuoYv\r\n1AKJWaH1ehuSZze4Dfq040VRSXBYVJ/S6Ub3oAoKFfpjDE5rWTEC5dWdeneN\r\nyz5flTO3HKvABQq7LmcEgWwbbBFv6xrrSOyMCfvxf+CqdiwacBeBaIs5+eCY\r\nONh5/aYcOmqOQ8kNGEiUkcykHKIKyI9Yp+GHmyyY/5zgTqDv8hth6KVPQDsd\r\neFt9QFJzPnRXxVOoqBPbfL/dw3r/qIHH210r91cMJ+2/EOSqIHdfKrBbT/Xf\r\nkiCN33t5UKGVJUIP+RxJ5G8R26rVmzHCq5WP67ZBdhP8DB0JYfEzlr7fTF2q\r\nzPllbaCtniCM6aQ6i8oSKwJ+QvTbG5AZIlM=\r\n=55Nq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/ini.js", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "eslint": "eslint", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/ini.git", "type": "git"}, "_npmVersion": "9.6.1", "description": "An ini encoder/decoder for node", "directories": {}, "templateOSS": {"version": "4.12.0", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.13.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "4.12.0", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ini_4.0.0_1679425212849_0.032673454992361295", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "ini", "version": "4.1.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "ini@4.1.0", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/ini#readme", "bugs": {"url": "https://github.com/npm/ini/issues"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}, "dist": {"shasum": "3bca65a0ae224f07f8f8b3392d8c94a7f1bb007b", "tarball": "https://registry.npmjs.org/ini/-/ini-4.1.0.tgz", "fileCount": 4, "integrity": "sha512-HLR38RSF2iulAzc3I/sma4CoYxQP844rPYCNfzGDOHqa/YqVlwuuZgBx6M50/X8dKgzk0cm1qRg3+47mK2N+cQ==", "signatures": [{"sig": "MEUCIEUIAwdG2MBXRm1rD3BuOfhvM/5WT1hrlvnPIjasRYB6AiEA7D8LTa6TSg7Ks8y3lP7+m/HA2XoYaNbU03IpSrFz0qs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12533, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkOD+hACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq++g//Rkz7zbgMcdifbbsd+IiUQ79g89cBQ9GOdeZNy1mO1mwsKk8O\r\nmIZXhudEv212lNSEjgy0U9CtmNf5Co4MrlSep6s47EHsYrUMGlD1/f7qI3pQ\r\nrn09a11CGF8XuTvM5XRNAHk9M6qhyBMAE9fRoFIRVLEOsK9VGxSLsr5Z9J+u\r\nwyU0dc9i9zsoY0j+cYb2sLDj2mZhCG42RaU9LrZuV+mLdRxKwvDU8/rPP7PZ\r\nTJhDJ38BsifKcFst/turiD3K3Q2rzI6NM0OjOCt3X+ehEHpqowgasYWCi5NS\r\nKXd4focalJFgggb5LRrrhXpDLAkAfdU1woqwPVEQTaxr05pEwRAihSIF+u1B\r\nnhWuZgqPv7IK2MoD44SqClo4ciCp1CpQBpx7FsgSAscLDcxkkJ7Tcx8/KSMb\r\nAGytHo2tae1/1reyuBX2OHNQOoEzNTjxLJP513dIzxWiMIIxmTtRTCdK/fK5\r\nMa+SZK3QOJbFChGsLZXB/CInfoZmZZIjV9xFaDXMEuLH5hFOuxALeATWE8Ud\r\niTAK3eAV6JDSaRKMF18BvEm43s0QPoLzTe9xaSMnnAJiWrggW2w9/w3ICcYy\r\nfXOowZoJi2wGn6K3LpxQWA9R2gWigzMZFphYgZQjtdk1IH+nFlsH1rfIRy2d\r\nZZWMO8Y+zPypQ/aFHSaaYgWZOdx/K941EVM=\r\n=DBrV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/ini.js", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "gitHead": "5bb5478f216118c5fe6af01096ab991c6c505217", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "eslint": "eslint", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/ini.git", "type": "git"}, "_npmVersion": "9.6.4", "description": "An ini encoder/decoder for node", "directories": {}, "templateOSS": {"publish": "true", "version": "4.13.0", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.15.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "4.13.0", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ini_4.1.0_1681407904854_0.43968429877600124", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "ini", "version": "4.1.1", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "ini@4.1.1", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/ini#readme", "bugs": {"url": "https://github.com/npm/ini/issues"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}, "dist": {"shasum": "d95b3d843b1e906e56d6747d5447904ff50ce7a1", "tarball": "https://registry.npmjs.org/ini/-/ini-4.1.1.tgz", "fileCount": 4, "integrity": "sha512-QQnnxNyfvmHFIsj7gkPcYymR8Jdw/o7mp5ZFihxn6h8Ci6fh3Dx4E1gPjpQEpIuPo9XVNY/ZUwh4BPMjGyL01g==", "signatures": [{"sig": "MEQCIHE+VwRLuyVqOmHg7OHnrTmJAjkbZqHUkuvf9/abzOqHAiBK7aH75qb+t4YW1ugqtGAu/MIChPEUN2VfvrEWJTPjkg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/ini@4.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 12660}, "main": "lib/ini.js", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "gitHead": "a695507a673b761834d46bf3b2d52b0ec85f356c", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "eslint": "eslint", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/ini.git", "type": "git"}, "_npmVersion": "9.6.6", "description": "An ini encoder/decoder for node", "directories": {}, "templateOSS": {"publish": "true", "version": "4.15.1", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.16.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "4.15.1", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ini_4.1.1_1684259926077_0.9889371031196983", "host": "s3://npm-registry-packages"}}, "4.1.2": {"name": "ini", "version": "4.1.2", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "ini@4.1.2", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/ini#readme", "bugs": {"url": "https://github.com/npm/ini/issues"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}, "dist": {"shasum": "7f646dbd9caea595e61f88ef60bfff8b01f8130a", "tarball": "https://registry.npmjs.org/ini/-/ini-4.1.2.tgz", "fileCount": 4, "integrity": "sha512-AMB1mvwR1pyBFY/nSevUX6y8nJWS63/SzUKD3JyQn97s4xgIdgQPT75IRouIiBAN4yLQBUShNYVW0+UG25daCw==", "signatures": [{"sig": "MEQCIEiQKEJZs98hbGNTezscZ0P0ZlFT+KHp24HzDptU4He8AiBrbbrhVP18UDBfhPJi4sd0K23mkyjJDaaTB6I7bxvU5g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/ini@4.1.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 12096}, "main": "lib/ini.js", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "gitHead": "02392651e9086ce7ae2c11902ad47ba2686ce4e0", "scripts": {"lint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "snap": "tap", "test": "tap", "eslint": "eslint", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/ini.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "An ini encoder/decoder for node", "directories": {}, "templateOSS": {"publish": "true", "version": "4.21.3", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "20.11.1", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "4.21.3", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ini_4.1.2_1709575244229_0.057635420944684324", "host": "s3://npm-registry-packages"}}, "4.1.3": {"name": "ini", "version": "4.1.3", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "ini@4.1.3", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/ini#readme", "bugs": {"url": "https://github.com/npm/ini/issues"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}, "dist": {"shasum": "4c359675a6071a46985eb39b14e4a2c0ec98a795", "tarball": "https://registry.npmjs.org/ini/-/ini-4.1.3.tgz", "fileCount": 4, "integrity": "sha512-X7rqawQBvfdjS10YU1y1YVreA3SsLrW9dX2CewP2EbBJM4ypVNLDkO5y04gejPwKIY9lR+7r9gn3rFPt/kmWFg==", "signatures": [{"sig": "MEUCIQCGVTlQkHmknR1bg2C9kTn5kjUuJDIfYaN97UduSrzVpAIgM4VtbxzhlgHY3GjNZQqrEDQCDe5c5qNoquZVdIxwXzI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/ini@4.1.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 12132}, "main": "lib/ini.js", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "gitHead": "a0c72fe9e335a3f949d734fb5ef13371a850bbe3", "scripts": {"lint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "snap": "tap", "test": "tap", "eslint": "eslint", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/ini.git", "type": "git"}, "_npmVersion": "10.8.0", "description": "An ini encoder/decoder for node", "directories": {}, "templateOSS": {"publish": "true", "version": "4.22.0", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "22.2.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "4.22.0", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ini_4.1.3_1716393574658_0.6864466019419737", "host": "s3://npm-registry-packages"}}, "5.0.0": {"author": {"name": "GitHub Inc."}, "name": "ini", "description": "An ini encoder/decoder for node", "version": "5.0.0", "repository": {"type": "git", "url": "git+https://github.com/npm/ini.git"}, "main": "lib/ini.js", "scripts": {"eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "lint": "npm run eslint", "lintfix": "npm run eslint -- --fix", "test": "tap", "snap": "tap", "posttest": "npm run lint", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force"}, "devDependencies": {"@npmcli/eslint-config": "^5.0.0", "@npmcli/template-oss": "4.23.3", "tap": "^16.0.1"}, "license": "ISC", "engines": {"node": "^18.17.0 || >=20.5.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.23.3", "publish": "true"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}, "_id": "ini@5.0.0", "gitHead": "63c421ecbbcb6c78b61aeb870099928ee78f8452", "bugs": {"url": "https://github.com/npm/ini/issues"}, "homepage": "https://github.com/npm/ini#readme", "_nodeVersion": "22.8.0", "_npmVersion": "10.8.3", "dist": {"integrity": "sha512-+N0ngpO3e7cRUWOJAS7qw0IZIVc6XPrW4MlFBdD066F2L4k1L6ker3hLqSq7iXxU5tgS4WGkIUElWn5vogAEnw==", "shasum": "a7a4615339843d9a8ccc2d85c9d81cf93ffbc638", "tarball": "https://registry.npmjs.org/ini/-/ini-5.0.0.tgz", "fileCount": 4, "unpackedSize": 12130, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/ini@5.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCH5f5dHw2ZeYC4pdV9GNf/Tfmdiw0EkO/ykHQZgBaGnAIgKz5qFnQ71uO5sye4pa0dFzBZJWfjEVCv2ZsiQppSCVQ="}]}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hashtagchris", "email": "<EMAIL>"}, {"name": "reggi", "email": "<EMAIL>"}, {"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ini_5.0.0_1725486618370_0.6249060924840131"}, "_hasShrinkwrap": false}}, "time": {"created": "2011-08-07T07:05:10.072Z", "modified": "2024-09-04T21:50:18.982Z", "1.0.0": "2011-08-07T07:05:12.514Z", "1.0.1": "2011-09-13T17:03:01.369Z", "1.0.2": "2012-01-07T22:46:24.188Z", "1.0.3": "2012-08-13T18:38:27.785Z", "1.0.4": "2012-08-14T23:12:24.868Z", "1.0.5": "2012-10-04T17:34:10.486Z", "1.1.0": "2013-01-23T01:57:20.617Z", "1.2.0": "2014-05-13T00:37:51.844Z", "1.2.1": "2014-05-23T05:52:36.003Z", "1.3.0": "2014-09-16T07:06:18.088Z", "1.3.1": "2014-11-25T10:34:41.287Z", "1.3.2": "2014-11-25T10:39:06.761Z", "1.3.3": "2015-02-11T00:01:32.150Z", "1.3.4": "2015-06-06T16:47:03.941Z", "1.3.5": "2017-11-21T22:17:14.391Z", "1.3.6": "2020-12-08T23:45:49.209Z", "1.3.7": "2020-12-09T23:16:06.664Z", "1.3.8": "2020-12-11T19:57:57.208Z", "2.0.0": "2020-12-11T23:07:02.892Z", "3.0.0": "2022-04-05T17:11:17.789Z", "3.0.1": "2022-08-22T17:22:43.830Z", "4.0.0": "2023-03-21T19:00:13.031Z", "4.1.0": "2023-04-13T17:45:05.058Z", "4.1.1": "2023-05-16T17:58:46.240Z", "4.1.2": "2024-03-04T18:00:44.409Z", "4.1.3": "2024-05-22T15:59:34.831Z", "5.0.0": "2024-09-04T21:50:18.492Z"}, "bugs": {"url": "https://github.com/npm/ini/issues"}, "author": {"name": "GitHub Inc."}, "license": "ISC", "homepage": "https://github.com/npm/ini#readme", "repository": {"type": "git", "url": "git+https://github.com/npm/ini.git"}, "description": "An ini encoder/decoder for node", "maintainers": [{"name": "hashtagchris", "email": "<EMAIL>"}, {"name": "reggi", "email": "<EMAIL>"}, {"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}], "readme": "\nAn INI format parser & serializer.\n\n## Note\n\n-   Sections are treated as nested objects.\n\n-   Section-less items are treated as globals.\n\n## Usage\n\nConsider an INI file such as the following:\n\n```ini\n; This comment is being ignored\nscope = global\n\n[database]\nuser = dbuser\npassword = dbpassword\ndatabase = use_this_database\n\n[paths.default]\ndatadir = /var/lib/data\narray[] = first value\narray[] = second value\narray[] = third value\n```\n\nYou can **read**, **modify** and **write** it like so:\n\n```js\nimport { writeFile , readFile } from 'node:fs/promises'\nimport { stringify , parse } from 'ini'\n\n//  Read INI file as text\n\nlet text = await readFile(`./Original.ini`,{\n    encoding : 'utf-8'\n})\n\n//  Parse text data to object\n\nconst config = parse(text)\n\n//  Modify data object\n\nconfig.scope = 'local'\nconfig.database.database = 'use_another_database'\nconfig.paths.default.tmpdir = '/tmp'\ndelete config.paths.default.datadir\nconfig.paths.default.array.push('fourth value')\n\n//  Stringify data object\n\ntext = stringify(config,{ \n    section : 'section' \n})\n\n//  Write INI file as text\n\nawait writeFile(`./Modified.ini`,text)\n```\n\nThe written file will contain the following:\n\n```ini\n[section]\nscope=local\n[section.database]\nuser=dbuser\npassword=dbpassword\ndatabase=use_another_database\n[section.paths.default]\ntmpdir=/tmp\narray[]=first value\narray[]=second value\narray[]=third value\narray[]=fourth value\n```\n\n## API\n\n### Parse\n\nAttempts to turn the given INI string into a nested data object.\n\n```js\n// You can also use `decode`\nconst object = parse(`<INI Text>`) \n```\n\n### Stringify\n\nEncodes the given data object as an INI formatted string.\n\n```js\n// You can also use `encode`\nstringify(object,{\n\n    /**\n     *  Whether to insert spaces before & after `=`\n     * \n     *  Disabled by default to have better \n     *  compatibility with old picky parsers.\n     */\n\n    whitespace : false ,\n\n    /**\n     *  Whether to align the `=` character for each section.\n     *  -> Also enables the `whitespace` option\n     */\n\n    align : false ,\n\n    /**\n     *  Identifier to use for global items \n     *  and to prepend to all other sections.\n     */\n\n    section ,\n\n    /**\n     *  Whether to sort all sections & their keys alphabetically.\n     */\n\n    sort : false ,\n\n    /**\n     *  Whether to insert a newline after each section header.\n     * \n     *  The TOSHIBA & FlashAir parser require this format. \n     */\n\n    newline : false ,\n\n    /**\n     *  Which platforms line-endings should be used.\n     * \n     *  win32 -> CR+LF\n     *  other -> LF\n     * \n     *  Default is the current platform\n     */\n\n    platform ,\n\n    /**\n     *  Whether to append `[]` to array keys.\n     * \n     *  Some parsers treat duplicate names by themselves as arrays\n     */\n\n    bracketedArray : true\n\n})\n```\n\n*For backwards compatibility any string passed as the*  \n*options parameter is treated as the `section` option.*\n\n```js\nstringify(object,'section')\n```\n\n### Un / Escape\n\nTurn the given string into a safe to  \nuse key or value in your INI file.\n\n```js\nsafe(`\"unsafe string\"`) // -> \\\"unsafe string\\\"\n```\n\nOr reverse the process with:\n\n```js\nunsafe(`\\\\\"safe string\\\\\"`) // -> \"safe string\"\n```\n", "readmeFilename": "README.md", "users": {"xuu": true, "bret": true, "usex": true, "andyd": true, "haeck": true, "yikuo": true, "daizch": true, "kastor": true, "lgomez": true, "pandao": true, "paragi": true, "vin974": true, "keenwon": true, "kontrax": true, "dercoder": true, "kyleaziz": true, "thomasjo": true, "tmurngon": true, "tosbodes": true, "vzg03566": true, "yashprit": true, "edosrecki": true, "evanyeung": true, "fdsuprema": true, "fgribreau": true, "heartnett": true, "justjavac": true, "subfuzion": true, "zhenzhong": true, "iamstarkov": true, "kankungyip": true, "raycharles": true, "davidnyhuis": true, "demian_dark": true, "flumpus-dev": true, "jinyistudio": true, "ragingsmurf": true, "battlemidget": true, "zhenguo.zhao": true, "danielpavelic": true, "jacob-beltran": true, "markthethomas": true, "scottfreecode": true, "shrimpseaweed": true, "akashdeep-singh": true}}