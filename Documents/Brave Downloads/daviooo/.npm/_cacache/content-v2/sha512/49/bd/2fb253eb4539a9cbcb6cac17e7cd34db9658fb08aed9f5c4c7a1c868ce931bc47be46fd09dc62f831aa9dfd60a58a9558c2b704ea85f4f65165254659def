{"name": "undici", "dist-tags": {"test": "5.24.0-test.6", "next": "7.0.0-alpha.10", "five": "5.29.0", "latest": "7.5.0", "six": "6.21.2"}, "versions": {"0.1.0": {"name": "undici", "version": "0.1.0", "dependencies": {"fastq": "^1.6.0", "retimer": "^1.1.0", "syncthrough": "^0.5.0", "end-of-stream": "^1.4.1", "http-parser-js": "^0.4.13"}, "devDependencies": {"tap": "^12.0.0", "snazzy": "^7.1.1", "standard": "^11.0.1", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "readable-stream": "^2.3.6"}, "dist": {"shasum": "609b7e29b8e678216c5822baf74cea454f640e64", "tarball": "https://registry.npmjs.org/undici/-/undici-0.1.0.tgz", "fileCount": 19, "integrity": "sha512-mk5o9H1ia0u/4UQiSV7pYO1IEKJRDw9iV8IiqbtHypuT9MuXJowh1wmGeRs8Ix4K17WN+e6dIzgtnjhUJSMcgg==", "signatures": [{"sig": "MEUCIHqOhCATLCoCITs6+66KY0i+A21Ld7CLEdAh0GwM7+FnAiEAvpugaQ8xpmeci68ltzhsB1SSkiqbGpJqyoDzHb6Koqs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42664, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbWgQ0CRA9TVsSAnZWagAA7LEP/R9sy89fS0+RcZY+7kwm\nq9gKa6qqUqengtsPzKqmEL3ZGzqqkTTxIabEJqBrrZUjyl6eBoJsEKiGYP1F\nIlcdY84tvRe8XUI0jC1kbYnjZXoJNmfaCX4MhmTJs6jioVrPbMR0MRuYjRFl\n26ORNPuq++4MIFHsVXl1YVbOg1DG0KPyLGcIL3ZGfsup40YTyDoFcGb/XOYw\nu1p7e+gx/PSOxlXxOtBDnjdXAzLG01RxAD49HbkWIYvLUQ/U24goVlFOpg/J\nFZhsVdYpyfyPB4YheW4JG+2F9BHXoNCieR90vZgcuGXJ3mjBi5ChiPr/DoEW\nG5DD4TKpdNEukmTVj3to6P9OTipFvjIBepIk9I+fS839Rma0OkEZ0NAcymbZ\nhm5+5eUbKL/R4zYew/jZVoKrfXFgDYRPqtRNBSVRZTHymVI7+I3TWfccu5t/\ngsEFe3gEsULC+cOU2ZBv3led2BaBi+0oOGMPshmNj3TGiydhnjZKbj7jwqYC\nGhCKOAp7H55nk3KMbraDwDcQpFz6SblxFVD1MHTcJojvWqb/Lfcew8xUzgbA\n28bALmLIEoGz34dFXcjrmMiHPRHx72GZQiQJumrFVhjXExZ0DCAS30sMyqBC\nGbcfWIPRBAuRdIbTSj3oTZLq4/ut2efV1YczDzRuKQJ1goWHpM8LnH6ATCMk\nKKHA\r\n=ThIa\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0": {"name": "undici", "version": "0.2.0", "dependencies": {"fastq": "^1.6.0", "retimer": "^1.1.0", "syncthrough": "^0.5.0", "end-of-stream": "^1.4.1", "http-parser-js": "^0.4.13"}, "devDependencies": {"tap": "^12.0.0", "snazzy": "^7.1.1", "standard": "^11.0.1", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "readable-stream": "^2.3.6"}, "dist": {"shasum": "615da9ea58a099a719e41adae8fef50de31afb12", "tarball": "https://registry.npmjs.org/undici/-/undici-0.2.0.tgz", "fileCount": 20, "integrity": "sha512-C4LNsOZmCcAX+min5q8uCtruHb4D3s5bF46tdqFgc2iu8dn2ipJ7i52nZSn64vlh84j/Muw8muQ7JNEhAhCsnA==", "signatures": [{"sig": "MEQCIEo9quhlcEqio7aDKWu6ihsIqJfnLVAIAwSN6S/Ny0pCAiA5to+SUlMz/G38GiLsewE5G24sulIuWZv2IXcUi8n1wA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49281, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbWyTACRA9TVsSAnZWagAANC4QAIAokB+QxENpIhRhXDUQ\n49AzeABr0/shT1kLss5rUIiQQ3l9OFhClGuXnH1klElozRppsQQIigqxMROX\n4nBV7b28BaMoCvmsoZKAVu57cZtJG4kgh4SeVcKqsK2N+SifP75JDlvxJawf\n6ziYbKfHQkoWQ6Apdo38CHjEhO4+R4K4ZsPJHRoo6Ri43nMrW/Pb2lafg2DP\n9zwAK06IdGuraaLzegAwvg+bsc9ml1c/dnkxfBHYZuiuveYYYQGMkm1RNreL\n1a7DyuakZf1+tpojfbAa9ofpSOGBWC+ui/x0FTOHp1jZiIHt/Uj2alMjGhfV\n7Ds1WGOWUFV4pcIhkNXyyIdxea/tQoM8tf/jSwGJLx6KEtUUuuBtAj2QQsWD\nBJvOgUuAwfr79YBH6MfOnUkGIHedwolSCZ/C1Z7sFMNgEZbfsHyjdcYL07Rq\nnzRmpOF98gV5qCz1OsLGL96AdY+uxurfTF3YrjBPXCSXTXFtzht4RkuUzIcV\nCKAwCjuT6daNoYLR7M6r+VELdUHQoSefJLbjyTFX0/9XUlwFpSrek1sqUPb5\n2rvOmkaxIUIwSPUAvdxy6w8vRKZSDr35j7VgIGOM1k3hNPtQ27zhX5vNhunJ\nBiVIJpIRB0yOxz5kiD6SXyuorEaKK4jv8ECHkLEyRveuYDVWdOg+Ub5HA/RT\nIOL3\r\n=BnTp\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.0": {"name": "undici", "version": "0.3.0", "dependencies": {"fastq": "^1.6.0", "retimer": "^1.1.0", "syncthrough": "^0.5.0", "end-of-stream": "^1.4.1", "http-parser-js": "^0.4.13"}, "devDependencies": {"tap": "^12.0.0", "snazzy": "^7.1.1", "standard": "^11.0.1", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "readable-stream": "^2.3.6"}, "dist": {"shasum": "1dc9335099dce88a88e94f1d09683245c271ba10", "tarball": "https://registry.npmjs.org/undici/-/undici-0.3.0.tgz", "fileCount": 20, "integrity": "sha512-ABw3i0Bsytu1PsB1GlPR7bVlmLWL2Qs9U4DpGHgPXkTqvUcARWg0FVWvukPq1jsWhyAj2MGb1u6sDSHgUIJ2FQ==", "signatures": [{"sig": "MEUCIQCdeCRYF6VY9fb2DwvAgSieblu1OolXrRheTMeBc1OAtQIgQZpuB2TK8hHifqoztjYi3VQMxqk8IO3wV4vTNBT5/Mc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53818, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbXVlZCRA9TVsSAnZWagAA1zwP/3Mz+R4ea9+kUy8pAmVR\n6X+3ZvPZ5ts0+cpMOPFjWNEcwWAqdUlmNTXYdoKw84kRFKZBxM8RUKi8j5fa\noHZdUm+tBg1rA15LsGno+QqaB52AlastI8FcqOwaDTC8Hk+09L9XdZ3YCGKz\nSt/uNlWTPvRMytrw5oIz0H1RzfX2YQgp2cWvjQlK10QO4w3WiTM2T3DtvTiW\nGiDkizy83lA53doZWS7FWWwjcIjZMRjI+5vuZ2fuJBaFvH0vQMWP4QyBH2dI\nwNIwtpy8l1EE7RNGBQtEjTpEF+HmS7Q0+yhTW+6WHeuG3yFsCWJ379dKHNH9\npBKuDu66nKtHrtVx8OgViLrao91cpWtye55l7LWaac6g0cbnc9iALch0AiCY\n4lftEYNTeW1UixYVNKhMOdsEm3+g8xcT0pxPS9MNzUuB6XybrUIlR6mJSvid\nX6zU9JbtnB6NwDPRnN0ZiBqiltKQehmsKje2YamlXJfs04kRdOSBpFm1C2zG\n2mVnzoQhdYYOm2onA2lug8e7Ob4Fd2mqoT5sCtOjA/qygEej+1td04+mMfsw\nWY1pckDXfjKiW+AUUU7Pqcsxz3kDucLJy0LmKcQX2DVe+qZd+5UJzzl4O3c/\nsOfmJvtRPffD3chIBds9kHVBcfpe1T8q0FDLd2fhFWuxK9SYdRqiL2G2dD1X\n/V1k\r\n=lYro\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.1": {"name": "undici", "version": "0.3.1", "dependencies": {"fastq": "^1.6.0", "retimer": "^1.1.0", "syncthrough": "^0.5.0", "end-of-stream": "^1.4.1", "http-parser-js": "^0.4.13", "readable-stream": "^2.3.6"}, "devDependencies": {"tap": "^12.0.0", "snazzy": "^7.1.1", "standard": "^11.0.1", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1"}, "dist": {"shasum": "2329ea3700cc0c0b8f0941c9e2f9e67049355fe7", "tarball": "https://registry.npmjs.org/undici/-/undici-0.3.1.tgz", "fileCount": 20, "integrity": "sha512-vYpNKbvFczbPL44nbwhXH+fGIoFtpbrcP6JfjELKKHNo96x+Miq4GoGCIJYzky+28mS2rYa/bl43UAlqVL8pyA==", "signatures": [{"sig": "MEQCIHZXUlCF0SGeANQwDSfKH9aYIHpJLW9VjVgjG98bGvrVAiB+BEJLW5jhSNsLBxaOWiUoGDWE3j4nf4+vEp3hl2JDfA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53938, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbKwECRA9TVsSAnZWagAAK1EQAIk56PI4E2mEcVc2+KUo\neeOGpL8EzYREpxu8WTIG5OFFr9og4NHBhMrK235+/3sc4NfLYUPIsI07narZ\nr+I+FJexARDBnxakVaS95RuEafRXkjnbLTVlKBfPiQFp8M/06gfKLfN+lv6F\nhae/n9B9+kSK4euD6oX1KdL3ju1lK41o2dYqMs71A0+9TyLuxaVxxQIvZAyp\ny2qYf9KVS8bP7dKUUX/n4cTLJAzMQ4yrptWr2FIqnJvXF3bu2i+FnPQa6wFe\nALbBWECFhaSVDHppg9qFvKxy/P2nsUE5UWu8HB+C7ofX1UoRE40REjZIwR0r\nWWPcD0UzcSKzY7qCrePNDO6XwQwAMekTx9wvPguy12IsMZq5B+xeMqQLQhy6\nQLwkb1nrdJ6D68iwKjTQhiaCclvJ4X4Gm/U/qOo+eFPM17fXgOUCfH6Sxl93\noyo4gNzj7y+nWYp3/2PnBc86yI1lw8CPKTO06FY0f4k6vKFqWl1x0FdpAbfS\nqpjCssMBq94RfHAxMaEaNQSp8RKqJ1OC94Bul/CYx8kuE2RU05sYB6bAAAne\naYwjqTj8Ew7OlkTF3ZOYoRBw4qmlTQVujOAyksyBxXh0xNYotzGf532isH9+\njFp2IV+BxekGHN0qxi9DdHzLWN+2dIxhFYKCM+ONDNdJjyTnqfFCop3GQF26\nMlm6\r\n=8wdv\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.2": {"name": "undici", "version": "0.3.2", "dependencies": {"fastq": "^1.6.0", "retimer": "^1.1.0", "syncthrough": "^0.5.0", "end-of-stream": "^1.4.1", "http-parser-js": "^0.4.13", "readable-stream": "^2.3.6"}, "devDependencies": {"tap": "^12.0.0", "snazzy": "^7.1.1", "standard": "^11.0.1", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1"}, "dist": {"shasum": "e9cd64847e411084aa4efd1fd5b403eb621270e0", "tarball": "https://registry.npmjs.org/undici/-/undici-0.3.2.tgz", "fileCount": 20, "integrity": "sha512-uUzAGLjFMOVx+sIKP5znbZfuQh4ZonjVkVOTt26QikLkcDNxFx5HAsSOeytEQScEqa9fHHZduvFWYeAjgMIApQ==", "signatures": [{"sig": "MEUCIQCtiFoKSIKvUJHcx/WZZOhVfQOeGcvRuy4OBoSe6velrQIgYdVaPvq+yoepHiyd0jGZEipFP4DsI1XQctnf1uudQ8A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53997, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbLTUCRA9TVsSAnZWagAASo4QAId2311P0lnWpQSuEBjx\nAhvmPwscAi5aYDbqhe1HHkMIy8toCKQ9WAJAC4p71QLwng6rj2ANnki7tgft\n9WQjJ4D3Vtgkx2+6mcJo2afFAQe2d/d5QwJIa33fDKQSIotJq2Z3Srrs4R41\nh+/hf3euoprgCxIqrpQyEWayyrEJImh+28BOv+o9kC01RtNBkj6Y6QFblTMc\nNN3NERa5X15BSSksJyYuMkIUW6frOFdzuPxsye8vT9KwuA+FGvbjPJRlJM8L\nZQ8Me8q0QPO3dvXuspsoONEW0RrSCy9ZVd7S2Kw4gcH877F9ix85jKxpUr7y\no9b3jYrG9K8dF/3FRKPVDhXwf8dESruA6Np3LkN5V68cPDWm4aXvMKL1AEZ5\ns9MhvACj/6WDtKmz4Dn/ZrbXqoEAyuFTT81Ko4P0PFz57JpMovXdReGr0hSm\nBtkn2/8lMin/ml7Gd9hxIIUOTl5WB6xcknqcPykgrZT+xrMuLBFzJ1d6aDdL\nUQ3HNUBLBgiFwkP6ZcrkTeHQ9urICDmEoUMg+fLPfUb0K7Tt3h2V2bi9C/hE\nAacfNxQkONOsEkPuzlIQNSTxdetsE9DXMZgG9Qh11bD70IAs56QACHSRGnhJ\nuRiT5tqjSpr7+UaeIVTFXYft3l+UvsyNP1N9bLvI/8cvx+Ui3NvebYY8sA9x\nlqvJ\r\n=NeQX\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.3": {"name": "undici", "version": "0.3.3", "dependencies": {"fastq": "^1.6.0", "retimer": "^1.1.0", "syncthrough": "^0.5.0", "end-of-stream": "^1.4.1", "http-parser-js": "^0.4.13", "readable-stream": "^2.3.6"}, "devDependencies": {"tap": "^12.0.0", "snazzy": "^7.1.1", "standard": "^11.0.1", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1"}, "dist": {"shasum": "b6cf74db5c1c0b3453d540a48827cd2f59bf2f5c", "tarball": "https://registry.npmjs.org/undici/-/undici-0.3.3.tgz", "fileCount": 20, "integrity": "sha512-7Csw1jrsNESNQhepDlTTv0dSuTkFEfM5p6rD/Ovgwdje6S/5wUyX2MydIGIKKnYNvuFgcFGbif13TGcKyouQPA==", "signatures": [{"sig": "MEUCIAQV+1Kxj1Gl/xQkmLU5cHxJzOEw2ZrmUkrXhDdn5MQWAiEA78zHXqfEzmYO7LXETS7J8w13OaPUnNHjNBi4x4whsfE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54941, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbq47mCRA9TVsSAnZWagAAG3EP/0NtLUomPYRXGkAlTl4O\noId149htjlV6hQvclhWbC74qPVEy5CMgtLQnlRQNWm+ZgLCuxkCaUVo29oDe\nKpGap+G4SlXyVlibMZCrcJDGCfSi4jfvVoz6NfvsGFe8s5pczyKKyV3zU//w\nziherxOnAT/86KCxfQbGgy7/T5yM0a6MobOncnfdbyrcnHO9DNOhpGqZ9q1D\n6xkJ02l9EjDRmgnh77td8ui7XeWMC/yo/tXUt8f/9XTHFpdxNqktMPiSKd9i\nrKG/mNF82gfmK22rTM6HhrjedHnKnxyBRpEba2+4RN04ZR9qSAK1fD3ZVi7k\n16RJd1AkYpA0YhNf6OWGjijErY7UJPzfB1W96oOjXmEkb4XGuBskXDT4BJsY\nWZR2tXmGFyldrHt0ZMNEz1W8lGZOehwCzYLVb0Ldyz04gepq0zyGrhhyiuR3\nBSUWaA0Ar3LqMuUd7CGtL2Si+fLw6Dm9ppwQsSnsfFfq0yAneAE1W6b0SKUZ\n4gxRolVDXsyP2sQ7erVN0xnRCL/WGDUasiB2r1PaIsmZ0hvJ6TdEJ7HtnLZ8\nAGoo2ewOj0hCLJtGD+VEXdhBYXDN6BjX18ltQk3mD0b0W5qOUZxi74UYucMK\nCt44y9aYLB4G+G2ha1lds1XUp7hMmyGIqhrmFPFBU6lbduYU88dSMH5gDyzt\n3T9U\r\n=MrPi\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.4.0": {"name": "undici", "version": "0.4.0", "dependencies": {"fastq": "^1.6.0", "retimer": "^1.1.0", "syncthrough": "^0.5.0", "end-of-stream": "^1.4.1", "http-parser-js": "^0.4.13", "readable-stream": "^2.3.6"}, "devDependencies": {"tap": "^12.0.0", "snazzy": "^7.1.1", "standard": "^11.0.1", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1"}, "dist": {"shasum": "f1ef89e8e5a4a7d282ed90df1336a58bb14c7a4d", "tarball": "https://registry.npmjs.org/undici/-/undici-0.4.0.tgz", "fileCount": 20, "integrity": "sha512-5S64X43hM2bSUFY00+ji5hcvuxtXAy/ssx826rsJbx6P0gFNKUksb8rxiJWggig+AiNLLtQhfgcaq3T6V5Wcxw==", "signatures": [{"sig": "MEUCIQCrK6ovn/pInyqvKv+77ngSJNn4YtRvH2v4+gyHNw1RfgIgYvRkLcxJT4gmeqDahi5qejPEHJ4uY+IpPMXLcXrmmNQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56442, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcp77wCRA9TVsSAnZWagAAsJgQAJm6Z00418pHMNjcVbkg\ncPd+/L8k8eyZ8RAMX0+DREDCRNmAckC+K30CJpzeYeDVGw/U1nIYTbQhFhgv\nApLHeY5e/xlXxbP33L7H0tlMSQz0SXP2oksO4oYIdi/8Qqjya6xyq0tDB+ny\nwfEFRYMOUszkpJNNq9t3RHEOPjfzbwcvUA0iNqLs3nhykr+LaSw/3W1740WE\nvRoJvb5ObTBV93rmpVPJfP3mMZ+y8y+Yq37qLAB30b3c06vl55RfUsa3vkhW\nWv/eD1Gn6fYvhgqOS1HSVTUxNSI0l5BEJSkae/W5+g3W0B03cjVRW/JqkUdh\nnrGxqaskPVKXcDHe2bhhGm7Ja3kEDIgaZfFd/Cq0ezzvpqx3SCzFxTsoyA50\nE8BbZG8R9tq/OulHJQontOeEa/wH8I23zS2Dbk6tC+O59NBHlD8nv+VF7++N\nD2pJRcoXUa/YrqZ+q1LQ6SC+u2sxvCXMRk3WcqnF8NEbx+8x8thHRvyOIqXs\nlGkYNtX7fDlQ3h/Ytr5xgRLnkbr9tKhwCkDCUSE+/WByZG9P9zkZ1FZWRDW1\n9t/5eSUNHmQqLBsRZcw5nZUUUUAkoC52nkovR4LjytwgqM2N3ZkEKJmyQQg/\nwExW+rpvXMVg+roOPFBwGOB18sN41jQMTKMt1ctWyskYUo36GWSA6F4Mj0HU\nHqO5\r\n=a/LU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.5.0": {"name": "undici", "version": "0.5.0", "dependencies": {"fastq": "^1.6.0", "retimer": "^2.0.0", "syncthrough": "^0.5.0", "end-of-stream": "^1.4.1", "http-parser-js": "^0.5.2", "readable-stream": "^3.0.0"}, "devDependencies": {"tap": "^14.0.0", "snazzy": "^8.0.0", "standard": "^14.0.0", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1"}, "dist": {"shasum": "7dcc7eb3bc2d636f0a6aab4ebcdd1224aaa3faac", "tarball": "https://registry.npmjs.org/undici/-/undici-0.5.0.tgz", "fileCount": 20, "integrity": "sha512-Mpc96q9Mh/rJq8Xmf1m29MqFHdW72CW6iTOsP5NS+2wymgmSk/cGDplB3EHz4lb9j48QEBhyKIlKWwXd9yU6Og==", "signatures": [{"sig": "MEUCIQDM1JD3XKX2d6aUgGScf9oQRVzXpakQqLNpCGk3NDX5AwIgUg6kJkCYQ8rYU0TubaMk6NTfYSusV8qK2rzP7X0FT2M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60072, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeIfvTCRA9TVsSAnZWagAAMOwP/i8jvynSsSNeNUa5E8/1\nlx2sHeCSF+IJKCvBI+84uLA0ehiJrwR5HX0kApODghBt84pzBimXSBik4s79\nYyRBhvfMdNI2QEeYhA+WK2hXSNRD1yKj0a8f4cNPOBSmDtjFkQqkkgjtoOfj\nyGerH2vD23qQCx7PA7AsWxDxGuz2bJjUG0coxOL6arhJ1JIyBNeH7RsJffBz\nNMEh53RkgZFLZpbTl8flWZFmt1mek4KKPlDTWSKDJofV5vycMaZLU+DK80Mf\n1U46apiy3lH1ID5578fIWkoE/OjdLJEUMMu5wl8olmfzc6RzZ1OqoEQpukvM\n79ErLX+eMp1gI0lHBhlBHjXq/HI5h3qdjVXdPL1tFjj3hs3+UNgFa4xZefPA\nS9uvj/qvmkhjwxNp807504bhiZT5hsyyxWaf992K1ljtSFzTr+zimniCOk7z\nRhIY4gTMS/TpLYGsQXFTF6boXWcatb0S1XGc5/s3cx3xqWKnxRtC0dyBmIQl\n7oC611UP8p1XJv4NIpYqUY9rpHzrI4X16iNVsPv+mWZclG1f8XKkFN1drpi2\ncAdAaBsEIgBuT1ZZQ6ulW8gnc16+ogCX7Fw+DoL2CHAf13gR+Ei6V0CtNr7d\ngV4x8zvYWGcAoSOa9dakHUFlTH598z7esq2SsOb6tEUKqWKt+Je1NgqI24X6\nhTtY\r\n=QccQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "undici", "version": "1.0.0", "dependencies": {"http-parser-js": "^0.5.2"}, "devDependencies": {"tap": "^14.0.0", "snazzy": "^8.0.0", "standard": "^14.0.0", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "7668663425607ad8c0f4a9993b1bbe175a5acb76", "tarball": "https://registry.npmjs.org/undici/-/undici-1.0.0.tgz", "fileCount": 32, "integrity": "sha512-TFeRq8Pz7+qW69m7EtK6rBPedr0xQ5sXN5VJi6M1wpAJvqn0Wylr5SZ0qrJn46DMy39QjmgpUhEDCDOMFk26xw==", "signatures": [{"sig": "MEUCIQD3Yg9/mKWtKxHP7QdbBs6JgPP2QzJDNffiZm8QstTyNQIgObTSLDBHssMD9cIZrAXGcQn6Xe4JtHme99FKi/O/xs8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191000, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe2SC8CRA9TVsSAnZWagAAD2UP/06OVmWJxdpgQpL/Ocrn\nFlACDMd2Q3/7YHyPcTwGgD/4DT2DwW0AApqQy79pn//sMnQDot9uc5L7heyA\nw6rw8Wf91C9IuPOD9V6LkPGrCcabxoBiXzOaJwpMxlvGdOmSfiLRPepqbnJh\nwidSEhVgC88H/njVsWCW556xD8GQz2vyg+axp7h931al5wd2cawx05QzBfV3\nBVaZUvRCrpnU/R7svIjzRURvN7ou1kP63tH4KO8vRHyCQ/iz4MEdWiEZYuIC\nuvPxPixpR9/DVRLOSa+6SsDVovqvmZ+Qk2uIu6bvS1XA4KsSpZebpcfXEYK2\neCMjOl3gxrNFVCcyQKnEl3tzpHUMD80Yac3omHLYDTqEPSLZI8IDwE+MxMaI\nSSBnUlokgWn3vkoacnOORbrWcOOtNR+wjZ85+35L6JgNcr7yMGMdPv+qEyb1\nN6SC43m1yCeH1pDwi+CUy9arKRAgPQ0PHHEarc5pNCq0JmogUM6eF1R0lRhE\nWkAKllY2VceQl1b9oucBXTkjKq8mpNmc149MRPUOJZzkuBzPEwZzC63+ZCro\npA0JpLOmoTKd2Po8dazI6N7q2PxluNQXXGG5gDHEgOWLYkLjVVECQSSUzZkQ\npL93Qvkl6RzjKLrMq1A/AVG2PsLPn/8L+Zt3FFifPob7Q7dZ5W4zIgyjSMbQ\nZcR4\r\n=ezgb\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1": {"name": "undici", "version": "1.0.1", "dependencies": {"http-parser-js": "^0.5.2"}, "devDependencies": {"tap": "^14.0.0", "snazzy": "^8.0.0", "standard": "^14.0.0", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "28641965d21130eb7f13120182cb6313f1e8efea", "tarball": "https://registry.npmjs.org/undici/-/undici-1.0.1.tgz", "fileCount": 32, "integrity": "sha512-VWg5dNEd/yMpCrzlBFW8H/ZF+V11blLZqad674cSZ+4HWNXyRS79zmeGxPdq/D0OH0X/qizME9ifMJOP0XjJNw==", "signatures": [{"sig": "MEUCIHBd3yEU84OgqVtgmQqKAC0BVOjBJhwQCUeKJAdakmv7AiEAka76KQdmHyClGILEDxyPmp9nEDpR31v2Ezlike1NIpU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 193491, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe2/9VCRA9TVsSAnZWagAAX6YP/RCIV9taMHcGp2YzWa9H\nQPgsvxmZgJKVThRY5/2lNxH842WYZChYjF3AzrgCD3r7MqrKmTnN9yNUUzqz\nkHzQ9RkdV65P0KN/dJUSw+LI7P84qLItppruR1qe7ADrzCWBMaMz7wBN/b12\n25mPY9fCOPGYDGU88Ie3D7N5LsMeQRXNgBpRveSBmitIFLnAl6wL1zLKZNyn\nNkb0fHSCqFLf/sNHbQravq0IS8cFYti24vfHE+KS3DEXZYLCaPGzbbpUA/l9\n3CyBWcOj+bW17ww91upHpdTstSbU2vVZfJrRx7nQnF3Lw1bisFCPU0eSVl9w\nA0qqKgK2W5YBEwCp0hxmUqp+0kGS/K/2H1MVWYU1Yark/IODSNT2A5Z/ogTD\nG/SLhwLpoKaEvR6exAYj+ZSCGd/gnWRosT4Plfsg0fKlesA3eQXYVuTFymX9\nQCXp0T8uVdDLjqugBpMMe+Ky+m4TGlZiZQ2DvUd7ZVeDPiAyy4DzE8AE+qO2\ngbnQaqOLwDWqVNZ9IVExqbTG+iqKbZJx/jjYyX2PuvRPU3OuyhBhnTnngyzr\nr3xSST07AS0axvvQzQokhnoIWR0qqb0H5PQlEP3/y/Kj31UlWEdqatRm6vPo\nApxJabOGEaWdVCHknlmO54zkA3O7RwafrlQd5H2Dsuv/0HfFwKErqRI6wFEz\nQltd\r\n=cLWT\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2": {"name": "undici", "version": "1.0.2", "dependencies": {"http-parser-js": "^0.5.2"}, "devDependencies": {"tap": "^14.0.0", "snazzy": "^8.0.0", "standard": "^14.0.0", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "0487656636bf93f76df23aee96bc0d214ae20ff9", "tarball": "https://registry.npmjs.org/undici/-/undici-1.0.2.tgz", "fileCount": 32, "integrity": "sha512-MCJm0pkzv/fLdVSSK0Lo+5NZOWbs8ZHWrmxrlbOAXrxlGRUj7+FPw3N/V5BO56juYxxfubWsZC57Jluzpl6gWA==", "signatures": [{"sig": "MEUCIQD5+dYsHos1P0nbjlupIT2Bg2gtqLAtaIHzUQzqzJDxbQIgUyzeY2SmajO7fzOJrdYAIKMwbpkuvn8eZep8thNrPq8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 194374, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe5QksCRA9TVsSAnZWagAA9FwQAJsxKh/+sZ+J5fDLq+gW\n9Ksmok2/rMoVqe1E3Q2C/7BUztfKx4u4JNccLi22ktGMaMJgDx/HIerhax64\nLhwBOW5a/BEEjbHFZGa18fy2qfWddIIgj+/e/RgaZzH0STPM2iXatqE1luVt\nTbl5CkbfEM705LB3LBDOHIVZQdadXPtoY/JLmxz2M6UlKajblFrsEJPZS2Gq\nKRvjAjKcsjyOv1TBSCFL6UEdyvGoBaPuTi/ZE8Q3hC3JDqYDE8wfRRSMm7mU\nDkoX81BPgk9dXPRudjENEyS/XnLZZsS/9aqh5OtFsVV245qspKbt/r9wlgf5\nNxv7glUCy+nmIP+UMZWKUbW7UAc7mRY//cXtqmnWzG+l7K1GOYYgi9pjduBX\n98jZ0euIiM6gTr92qLbMG3+7pf0DcPtISqiFOx9OObe5+dpa6s7+zlNR+w7C\nG8reHpLKGaekkfXVYEKAuGQFQvmu0fjvteiV7Kt9rCY3Cbb0aHHgT1sX0keQ\nKmtoBu3Lez7HrJDATQkqCtGHAiWy26owFnlRHNx6/YeUNqPw8nTWHzZZSy9V\nbD6cbzPMUJ9zw490BEWz1rnAGD9WiDPQ2QO8jO/dW8/3RM8shJAZKU5IBt1y\neBNZeQ7NGaGbD+lbW7AMkBkulQir/+XJTnXdo3BTlJqRYS4ch3fXeGUf9KEN\n3nwS\r\n=nHe4\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.3": {"name": "undici", "version": "1.0.3", "dependencies": {"http-parser-js": "^0.5.2"}, "devDependencies": {"tap": "^14.0.0", "snazzy": "^8.0.0", "standard": "^14.0.0", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "8585615d36b7ab40ab946a41b76bcff2d1ac36f7", "tarball": "https://registry.npmjs.org/undici/-/undici-1.0.3.tgz", "fileCount": 33, "integrity": "sha512-zLI6kvRUJBSXpd3rwwl/f86W7w2fOAMI9lkiO04f7mkzT2ty4+GaVeQfDJyI/t54dQtekxJGlg4H2lr7ZvJc4Q==", "signatures": [{"sig": "MEUCICOnx1pc0aNyaO0cppMVnCCGm0EBlGmGGsMlZ8vxD8ecAiEAsIsbvPKfIhTCzVvx7JXXZBoy8ev8MXqrt29rAWXYTo8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 199707, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe527WCRA9TVsSAnZWagAAlDMP/jg+WClfxI+6lArSiSa6\nzHecx6UHZoOdSWBfFbbLZzyi177LCY3LqQCCfrhXQs2CmwA1ZCBhGtaNXdg/\nmkLfob8VGy6/3B4nJt22IVvVwl6/Cm3EnrhYJ7v5PkHS0Y7uusftdlLDqmVv\n8wPHFq7UT1PJJJC1o+27LD/qCwpbBjxoNCaV8wOkRNBdb6NmNr27nlKkw9lt\npaA4SOQot+FscvK+LmnNcYCAKYwO/vRorxDOH2q18FBWTndxGhevw6LusurM\nQei2YdoLHjUvfv7cCgeLkffnSmx56vxggirAQf7Y8vzV00iv2rizjVzbgGfT\ngAfZhD0W8pWQqjPwiZPbvBNXmc6heV3+uNq9gFroS7Fnt62z9TW8x8bGT9Wg\nqzWxZ4B76QK6RNk7kzyWhZsy61Ayu4ki+e3dZOpOCdjTk8vC9QIT8nuRXJKR\n1vqtR3FKwfLmVV1Hms3gqGCVnKpcu8kXKij/yT6hs33g0Q58TGBZ4iuNXWcU\n57ob2kM+psR6RorWH0AnySCy6XpMOHatycA4l1+1R2tQbJOGgYT2/2WRpG/8\n6d/uRdWLmp9d4AlqXbz0z9xKArVUlDE5sj1qZorxN5ahi2Sb4+yPGHRLuLgk\nwh6g4ksJU/UYfVGFboJL+k6bj+/l1xhCF/sogLekMElwgQ/nlottm2HUBwu4\nNVVe\r\n=goOj\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.0": {"name": "undici", "version": "1.1.0", "devDependencies": {"tap": "^14.0.0", "snazzy": "^8.0.0", "standard": "^14.0.0", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "d0bd66d61dc0a67963af070ae22862316a73e5ed", "tarball": "https://registry.npmjs.org/undici/-/undici-1.1.0.tgz", "fileCount": 36, "integrity": "sha512-wi4zaKmxyaMCOMucf3DFC05YEqtDiCjrXMfPiJlvuQyQeUgeAUHZLm+K7DBhC7lfni6/g+flNoYrzMbtraA4sQ==", "signatures": [{"sig": "MEUCIBnw54ixnWkJUIAoITtvHJsoW0N64ubjlQg5e/m/nA7wAiEAk58tANUm00RkIp5adHGQRawZUPLIlZU7bPJgrhDyCok=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 210214, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfAs3eCRA9TVsSAnZWagAAUngP+watlOwV9D5ZdkmAwcXj\nAwk+cYWfcA8e2ZN/qqpIkKTxyQveE0iBPrIj9c7i9qyfeU0oayd/CJLcQrED\nkxzl2vgfKcVvddD3XD8BCEsVJfV5G4DHHz7g0RCpT7/Ed0uGS7RsDS0Hi9qq\nST2ek1w6lCAytfBWmkboPaKdBe+3I3R6f+4r6rSi+QpmeRXnLnwYUkY/tFTS\n+HaHxPBLgmJuAF9ORWvFEIwEWEgHo1x8hn6BKUzhUjCo70t/4o8It4ZLnTXk\nNVLjqRF76AUFMr7mqhfobYiHVMSHbVrvJ+NrgRdNTiSdKYhS2dNZ03kfnOjn\nE690OPOvXgjWPescyueups5X9/ohI2JJvG2VGMIDteRE1+MywoWmzDsh6fgm\noeM/Ya93gnd8jdobGrY7TxKsqicLK61kvlLkEJTgRB95yCt8jVGAiGPHCBDK\nTiQCo2ow1Nc7qQ6GtNFk9P1E8bQukNbxCuGhqUKY0p7OlrGETO90qDKzB6Ci\nFfRCDVR6YW8xkgaz4e0+AuEhuQOpKkNXlzBDTC+9L8+BTlS9slhA++MHCDAV\nzxkNi48i3MM8Ah9YPZkjbp5aFnjmewb1kx66/MWyM4z8bTKB+YJDWpakewUg\ntobS1QvcxFoyHka07rCNLod3MsMRuuiqX0SfrtTBTSp54RpUmiaCk8D5wwBO\n+Y6q\r\n=r67w\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.0": {"name": "undici", "version": "1.2.0", "devDependencies": {"tap": "^14.0.0", "snazzy": "^8.0.0", "standard": "^14.0.0", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "109067046866f2036942f0d4a9ec94a0798b694e", "tarball": "https://registry.npmjs.org/undici/-/undici-1.2.0.tgz", "fileCount": 41, "integrity": "sha512-vzqLGUM0JqpRosuVs8vgUybggQk9jr4oVDxKAica8nyywskwf0cBK+vB5n2vEnoj1XqjjB0e/FcnbjcKb+KOiw==", "signatures": [{"sig": "MEUCIE/OuJdugc4rRJja+ui4gut7a10khDwNU6mo0CXFqfRwAiEA2XBXxFGxpWjenWIwPlCIaFFy5mA6pilkBYUeq4vnd9A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 225654, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfDaVOCRA9TVsSAnZWagAArlcQAJP2iK41IdyrWXlWvfBr\nIkNbyUXfRtTvXi36ToD2wWc6yVxBwPdn3LmS30q5piWV4tL35Kqamini+ch9\nqsQXnoAffmxFA42ihhJRjDpt21oz4ggPXHqyZYbNtIn+Dd3NK+0AVdN5ws5S\npg6oG2iu4rceRWtVLn+627m6PszYm0aU+DiQ1KZqnYVW95K7yl03EM+foPLi\nnNU0RFTs7v4bWWRd71z2077ReUOHbhmW/A3QYFd9L5evOMhHRwDxnRlrOlWi\njRE2gYjJaEzQK0pV0DQ+SSy3sewlhjf7ZnZSf92anYmVHe2S4el8SyFytcOg\nkFNpfRvWhl5Su6nsAKGL6Z6S2q3367bU+/piFwQEa//dqUuxFmlpdrh+fbjH\n5W8F1ZSsqUZuG8/KYUs/iOvSK0fLY8bf/VNEnbwMehUy6kg/RsyO5kfopra8\nANva/NnwQA6ZwvmCBTIfltOC06YQ48CnNStoMeUbbgDnWQCXtDCJaH0tKOEi\n+vymOJikc40WTZ1WdgP9jr0VUUc4GCqoUXwXtYHHiUWRjwu1Si128XNUBsS3\nO3BinLWwTwn8fgy3y9MUEUiD+6S0v8f46f6+c7QUibWpMtDLbmSWWhBzjNlt\nDzDRne0+YqujycRUPXU777HqkF+nVW/TURavL2aZGZzjuTQCeZKi6HafT8L/\nWwe5\r\n=uhwM\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.1": {"name": "undici", "version": "1.2.1", "devDependencies": {"tap": "^14.0.0", "snazzy": "^8.0.0", "standard": "^14.0.0", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "00ce56fd840d4c95b62cd6c42f336becef591072", "tarball": "https://registry.npmjs.org/undici/-/undici-1.2.1.tgz", "fileCount": 42, "integrity": "sha512-LA35eVZZS/sstKsbtPIjqETU+Mmm5QAJYWTeKDWilj+rApMfPf6p6RoyVWTtfX/M5o+/R96PlPm8tnR9z5y8RQ==", "signatures": [{"sig": "MEUCIFHZWHzROhHuw6HduPamTwIQR64UAZ/d4xeVMBrHJsOUAiEAgY3rGYChjbjpUA5hSpTwEZBLvPHhkouVCvU470Vu0kk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 226754, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfDbMcCRA9TVsSAnZWagAArAoP/3374scrnFcHPEIPpbZm\nYTJ86tM4NA7S8Aq5mimlFdzktn877idD9ksJ/d3CUtc3rUbTIRbks8uOjFnS\n3EzImLpkPHEFaIUXkFiS2Ha8rTThFf5ASNLQpPhEEC48ncoiJlxd5wdXCcLK\nzSscf6mBaSxnsUFLpqMg51MnCSxM0bxUeYKBo/3nj9llXaUGU/UIYcPl4yKl\nxb+pjwkg1BoxAGCxetPLJKc61Xwp/Jv6HIPM0NODx0cY+qKXGwOWUOECeVOy\n8GeQBxexkH4IdiMdk3yn/05bDl1IagvqMhe6/qz4TOLeQPfQfcqQ6srIQtnQ\n2G+qTzx4SxSeTPljRhfxtTLLojAedP0gcfUm6fT7Ww/h2AtCWEa3Qkyt/yrK\nmitfvV/IjgwI9mCj7GYNZgcwoj0f4VkgMaaIG0RrNQF2T5qXOJBvE1W6f/PY\nHYn7P9ZIE04d3kveZjvQShhrDxBszqMTFN66ssDItDe/EGYSgqRjwdP3zbam\nNzXJkQZeRcfd4+FMz9Uqm9nlak+7gXUMCUDevMPqHAmiSo+K1lE4DbCoA5+M\nwRgAsqqL4bZd6GjTEDEyI/WL/mcY7NIUD++icN4K8nJ5TDgRVRXMl+lzCpv9\nCm42UvKJPPOsBuz81LzCSMgUHLAiWpLQWY2bC0jK/xPMao2JQxM0ag1TRpry\nbEi+\r\n=x6pk\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.2": {"name": "undici", "version": "1.2.2", "devDependencies": {"tap": "^14.0.0", "snazzy": "^8.0.0", "standard": "^14.0.0", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "8305ebfc6668ae128cdb43f57a114f746cbd2339", "tarball": "https://registry.npmjs.org/undici/-/undici-1.2.2.tgz", "fileCount": 42, "integrity": "sha512-hp8AVHO4RfL20a57ziLcWtYuLo8pgP4YTnUTd6iI2GaE21BaFedcJUwWNVPFO2vT2vQcYKjdlF3nVASkWDu7FQ==", "signatures": [{"sig": "MEUCIQCcOTUMyLHsav2qboVjPVJCdjUeu+lv6/3Sw2hwzERG2AIgKsMPatToysxxkggdlsihpQhao3+Rvbb57sNg5QYGVQ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 231652, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfEJEmCRA9TVsSAnZWagAA7FkP/0DgOCARPLL17pcLquw+\n/l2iYQ+uN9+3v25rDOvhIM7wkJaL226UwTfsCyjEaqvz7PKVzOF/viHvEFOn\nSKy7htnaEvcbp11zn64nYmLMVSva7EX554jR3cDzLPwIFiYMOP4JRcXy15Ig\nrPCHoGLF9f7s5dxZdflael6/WkxhkcIF4740nBxXmjXj0cHDUd6rbklzCdrF\nReUDPHjL2fFi5ZKSRf2BE2gPjW7g95ZbFBwO+fryYD8SiElXZMAsicDpw4J9\npf9XHDRZNkJrRbm1lwCRQtSlzw+kYh//U3N5fzjQjmxxecnj/H1hBB6hnQMG\ndoytMeFLjkZg/2dsVA4Kn4hdYwYLy7ZBhnSLO9Dj+0FPQOmw8lnf/pnmALeI\nnOQrC6ln0kQ3ECiOWvJDVXTe88Bu2usiWu+j3q1j4CngEFX/w52VSXAMmHbv\nck78INLbnTQvxxVbs8RT7XSmFFZ121ZT6u+vqJzcYxuSQBPWpUnBDXcFDu6f\n3POnDV+tEqStM2pXYe9H3GbHWi6Q3Lf4GPY2S3Uvik2EaNTy3hkFb3/+ngPl\ngbUFil2XWFpzkrMPFBaTy1+VruaXWBm6yzfjlC39DK2v89VlD3/SGmYpUcbH\ni0mTcqRWQudwSC9v0So2K9mf3Us/ZoxEn70cYPsUrNE/yr1cuVHHzMM7lCEj\nixsu\r\n=qfKC\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.3": {"name": "undici", "version": "1.2.3", "devDependencies": {"tap": "^14.0.0", "snazzy": "^8.0.0", "standard": "^14.0.0", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "concurrently": "^5.2.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "abeda232fbc2204331344d192e4e80d2063b94fd", "tarball": "https://registry.npmjs.org/undici/-/undici-1.2.3.tgz", "fileCount": 46, "integrity": "sha512-D/CWAxMobqeUMWoI/CGKWPpIAPSIt+yBr4aIOs59Y0HRJom3Z2zUi6BllGAYAF9Jqp1taIzxxT8ZxgLkhVF6Qg==", "signatures": [{"sig": "MEQCIBUL5KOZ87BrD3J8jQGRyJYc8L6Gjs63Yt/otxOHGIAqAiAy73B9TCngZDkEpXm6C07Ze1iqdnHw6JFqYaUh3Nb0KA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 244871, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfHvsaCRA9TVsSAnZWagAAme8P/2OjvidTzNKsZOc9fsbM\nasD5Jh0jaULOweXgOI0C5MitDOeMJHEtSvi30goN0uu2J+T8pEPOPjI8n+QY\ni1lQ5nVNHCm6EBFXlRkXw9f3AWmE71zg/o6L6TIvw7CoQHOUGPLtPDp+u2bv\nwwUyfnsb6dl5P2ALOzKtxPi65sRtf4YO/DyYaIu6gqPuc67NdpAPq0Rp2pX3\nwd0JBhha70eJLyuxcOJnPIhjPjs+YYp58cNRC0aHwthXBGdECg5ZGGUverU5\ntnpweZ4UyHSM5XXQJcjfjd/5PvFV7WvSR+MKtbHlAJDF2XW45r99zH9FaXu9\nXFnvTNAQrUPks5HpYgRdF6CspswHT/ZEhqRdSEi+W04OsRPVXFEyEEcnFDbF\nwexNsIQw6LmMAbs9lo1HDDksXdaV1sDGd/K/jiBoKXHKo9HTWZzXc3TE8Gef\nDmI8UlVREFH17T5sLuCbC/LyB6nx22dEZTLNE6SazSqk1qUMzV2g5YO8TNyv\nShNqPUTBYALTR2pvUPqEN64rxO63SPgokIYUoPK5hb0oQjx+Xk2i/SYFmAF9\n+U7Q7hf4HgnYCSpFOYkR4aYSJVUz8j37A+S+VVYMkACxK6e0irTuBiuTMxT9\nr02LFYjgqQYUZHjcntxgPemPdYz0+8jIq4aXlnH8wIlPK1+vpPk1SBUiUYJM\nRe/H\r\n=sTLk\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.4": {"name": "undici", "version": "1.2.4", "devDependencies": {"tap": "^14.0.0", "snazzy": "^8.0.0", "standard": "^14.0.0", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "concurrently": "^5.2.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "accec680b8b4b04f0698e8df146425858a8bca99", "tarball": "https://registry.npmjs.org/undici/-/undici-1.2.4.tgz", "fileCount": 46, "integrity": "sha512-MMZItRlSk2+tOahjg6lWg9eeoACjJ7xL5PIY9hZ5HAtnQAwsvFOj4pLqwmTkNbmeuSSVZHfHcKMFUTbPQzQx7A==", "signatures": [{"sig": "MEUCIQCD16aUZ6Lp1lA/MI/vOHpCKByMkX+bGa05p81wOQTlrgIgIr1eZWBPOn7yHeE1WOFwh8xyY5pPLwNJO1etyL+FWpI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 250379, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfITh1CRA9TVsSAnZWagAAyPsP/iZ3XvPnR5C7Tp/MrJph\nbF+jyUZO+UfdiFVan18Kvo1QAX/P2xR/q65vCMWHLnsSuBNOMxcbwdiIlcH8\nLT3xKBSK4otbwsfdthdWGTV3yD31I5P5wk3A8lLniKyxCwMTZesaBl/UP1hU\nJeciad9sz81/WjlCIyg4TpvAdFSLOkRP/S2UdfRpr+2NU8wDfiDPsHey/9jV\nFYiNBDKIkCcy//raZjDmWOJ87R8kyu5KtLcQo5Vg4NwWlOTMZ35A7gn3/L7t\ntW1HmkK9D3EJ1YF4cypXsKL3Wo17aMKs0DYbm5N1sb6RpqkOvDOrFkDke3Nk\nzoQ+kBk4dJj6xNpYb8KhLCwTKyiynpS20EVl6xcNCfAhGM6ANx4Q1xaBECz+\n7rFKf9SiZvpy4iRQyEhLassTpQT/xB0W6q+ckI/W0WpXlK8Doc1j3kenbwR+\nF8+yZbmp+H57xZhYX4ZJP6VngnvIrzyqyT6ckfxA9GTkXkQYcfLbzucFxnvz\nBTKU9VTQGGFfxwlMhqJ6jxRWPCnzwg0DK/y57BEV0e1rsRrltlKsFKB74/Qp\nzbkxzpXeXw2BHEx3WBbDolcb1qCirvZSDI0soUkS4x4RAkZeISBob2J16lHc\nRirucsxwg8wafg2ycNJvyRfAVSkJTdqfiE0iFwZWyrNM/424/mscTLxEYkqv\nd/v0\r\n=zjFP\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.5": {"name": "undici", "version": "1.2.5", "devDependencies": {"tap": "^14.0.0", "snazzy": "^8.0.0", "standard": "^14.0.0", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "concurrently": "^5.2.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "b59b1186503ec5c240458ca64f72a3015609cf0e", "tarball": "https://registry.npmjs.org/undici/-/undici-1.2.5.tgz", "fileCount": 46, "integrity": "sha512-k0E4Q/1FeT/jKJTPbvPbyTLjkGivMgnAd7r0N29qSfgNYGAiyoGXFRPvP6mHsRPP0bMCqf6x6xia656VgUJrdQ==", "signatures": [{"sig": "MEQCIENjEbMtggDyiuTK4LxU7Xi6pEPmtip0Pb7HoQvfG5UxAiASD++a3xgrfkV6w7cFnsMNIelO43+m6DYJlJu57pXpwQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 250502, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIUNnCRA9TVsSAnZWagAA2aAP/iX01gqavacW6cPd2TuG\nmrTfeDTGP84oeULMi7nHRDGXulgfJ1J3Tq0dFc3gsKklhsNtYHjNk7LEy3t/\nsEWQDZ0ivnaj9ftptgxX3WxWcxDP9Trh8tJ5y3PNHdQNNc7z+krlxmfImef9\nZJJJ59ogEd6G8/RmSwD8NJCYLpHzJYlE08VyQLbAk7nhObA4B1wJQJetWkD1\nSyMELrSuE5j7MN5wrvPEw09WL1r4MHymsB7E6HuT04OCKIANY7U/LbQJ+f6k\nD7cXcsKvgiK6GSPCimuESDIWAu5cRDTdQBs8/BRD9KEfKzyCc7jCWDpo2e6w\nb94EwK3fGSsyyWFVMENmeragSnXMo/NmR0h45q5i/xVQsL+HSKHS+7GCS1RD\ngNP/ZWomw2DbJVT+XuoFNh9XmdTTirDxNhLr6Nu8+oV5rO2csq3r8nOqwSBv\nWFOVCxN5Ie2AxTXWuOacb48nOb/U4APj+M1qMgPJDB6jpMhM4RChCrTYKajv\nAoVWc+GJCSUtW01+RtDF+ObyF0tFdd0qLWwa6j8P2v+KTsPTmdQGNAwcoCdT\nSRo7OBMQtYztrvqmnlehHI1DVbynsp+6jM/Pg3WlIVNtmD0kcYs2okINSU1o\nfhTIgdnIWu6lnUycyV+tlqeIkSYOPUGdUK6B9Oqn/2+FQ722fHTBUin9eMFM\n2gg0\r\n=hl9H\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.6": {"name": "undici", "version": "1.2.6", "devDependencies": {"tap": "^14.10.8", "snazzy": "^8.0.0", "standard": "^14.3.4", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "concurrently": "^5.2.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "6a92bd61c94f4291b0064db47ec42de65384d4f1", "tarball": "https://registry.npmjs.org/undici/-/undici-1.2.6.tgz", "fileCount": 46, "integrity": "sha512-rQkWxT6nffFPy4/Pb0+0zAR0ngmlujfS+Gbhw5QBuB6gmQzalziophSg9jx+lOhKluaw7E1ZoypkG9CfRdJZcA==", "signatures": [{"sig": "MEUCICqGj/2aBKpBudHSY8fPWQtEP/zgmN9jtbgggw2sPQDTAiEAkkRftQjij1cjZaArAd03+LAFTJFiOxbATKYL4xiTNpI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 251654, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIcUbCRA9TVsSAnZWagAAmJkP/04abX4/QDlHZ3qYxnQM\nyn2/lYZKaQde+9hvGaDfVwGPZkDwRgEjs/4aT8VNVNhsPVCnZk0O8ZUQ6xLm\nvOMs+7gW8leyLu28BknWeX6A0vNWabwIDeYrbP5pYbza0LkKJn/XLML6h9G+\nvK0blQ+o0hzsEbbpeOqAWEslv0U1b/4lOBfoouARrvi0VvtkhsmLbNoHztLI\nPs3+wF0/MG7AGFFjTVKOIqKKyfRQBJNczflRBYQSElZjd/L8BKMm8VkcQg1R\nx2r8JomDIFrlAOeEOnYujnUFL8uoAetECF/HxXpvyazj05Eh9yo9VLvH1ZZ2\nF9VItex0+y8XlpJzeQKqTHTbUp/5p0jRHPagXlqgdyvEjDmXBBtJ3Nq3bH1b\nYZD95wbtyuV12y5QbFB7QSbp1Z9JdPy+Ys+2rwQHKSHqFxjIwCuUjkl7vgte\nfGtOae1lN26wFq2mzCT1+QzIuabwnB1O2Z9QnQNWD6YtGK0LEtVcUOuEofXL\nNLS8WmBa2ZQc599JH/vXhrLutP0F9+MilB7YaJk+6wy4ok6MiFMRKc6Z7udb\nn6ntwdqLEx4+BdOuiVtcodidJ9vByaOBEjKfjtjB/yNosqMJxygBPOgXDbcd\nnjGQ9EloZLsrxwEVvgsrocA1TgNuuF94qru9dqGJqCz8/Rd1+1pa29uP5KEI\nRUgN\r\n=+aZb\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.0": {"name": "undici", "version": "1.3.0", "devDependencies": {"tap": "^14.10.8", "snazzy": "^8.0.0", "standard": "^14.3.4", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "concurrently": "^5.2.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "bf50bd01e66713986dfcc06d865572a59559a57e", "tarball": "https://registry.npmjs.org/undici/-/undici-1.3.0.tgz", "fileCount": 52, "integrity": "sha512-JE5HzCCsZQzfTtgAVY9LUipmRv3/L+yzcTN6S3DSJWxk1E3RIgyVCIkYv9qV7B3OWu515wci37hhOLllVvGCyA==", "signatures": [{"sig": "MEYCIQD1feerw9kZ9ISIAQgEhDyYy+WAzLBka2PZdUcixiPtDQIhAN0e3jt7XXWy+AP9pHomKYmMOT3TWsq3bG5rTWoFavBv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 280478, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfMTrtCRA9TVsSAnZWagAASDcP/j34rnQmJ2CgiQlH9Jz5\n3kVfKl2VacYIJbQNaACjaaYxDRpxfL8KWSZ8p7Tzg4vDrc7Jft9upRI8H1i8\nPyGUnNx3YDmOludsThdqtfty/+SECdO0gnb9ds24b5xdcMM+VQ+F2+K9rpNo\n6345uMKFkYlVXNEs2mOC9Ld8Ufk3mhPn6BfXQTfqHxyEks10bZSqbMFFgYWV\nlq4lrJ0YFGcAiscMlQb6+JS3o5b0kcYV2ZGomtXm6Ot7CEhlBIcy/uDGVqvh\nfwiBgdPVerNk81vxZ4sXwwTcIKK3akMsSZfdIQDHZ1zQCnsfGAdglIxsPlcY\ne6qC6UEiqBzwipCBFsIKYCMeMpw+yAFcfgP2LuT9IYzLhNwFoM7P5lTlq79T\nRFEOdHPSk0xtZqBnwRyWEZrLY6tWrmATrTyvGX4DwPo3NQXCuvlJSs1Krve5\nw+RJIwSanccDi/ioOn2ryUFZGOvl4UaOdjWm+lZn+P4lZrVk0WY7iRAasnyi\n+qr7i63dqbN55L91tDwMROotAv86qx+Xd9avU+sbg8Na87BBdSPFco6HcWve\nE4/Q+iqfdSLCJ1blw8jFurpuFeEeMGUMzWZMBJohlqYpFPpQtTPONOaSMyoR\nI/E88nVYP1i1G/IJLHrUz9reB5PwcBB42CbiLRpwkprswPb45uaJMjBrnjhS\ntGxQ\r\n=Rvfu\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.1": {"name": "undici", "version": "1.3.1", "devDependencies": {"tap": "^14.10.8", "snazzy": "^8.0.0", "standard": "^14.3.4", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "concurrently": "^5.2.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "2acad2a91177983e4fd99a81cc9f83d0e36a5227", "tarball": "https://registry.npmjs.org/undici/-/undici-1.3.1.tgz", "fileCount": 53, "integrity": "sha512-NX7FzPHOrsIEHv3PDlVX4wzN1gB7WlCUzkcL2Y3rrEE4vKrlJ2Oyvn+QdACR5qpeCI7j3XtfDirGuZlkL2JDDw==", "signatures": [{"sig": "MEUCIBBSNAHMOdHg6HSICbCl1cPAR7qYmqSwF2aGZItpLXGsAiEA4cuEouVL3RUtVjshBsEX+HV0RA4YMLLQtrhN1Hq0WME=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 290035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfNBh/CRA9TVsSAnZWagAA1XAP/0OR1FueFu67+mfK7iKQ\n5tBk38coXETC+AI/IJkjDBjz+kZ4zPTRZpyx3qyheZt0sTEpDEou2bgOYqeL\n7KJawMOh8y2mQZi286mY0piJBH9dv1FIjBbCFUp98prHoySssa+rDu7jIlbh\nDZoJEoUTnF50odTMlbivAGRt5kFxd5yoyqrM6K1QrNJok0/VJr9yb2E1jMkI\nvuvQ6Mt9CF+T0Bfseo3IGzlkp/nH/yGjsLQoUHsXdXt/uoFE7IPFy9GzhCqf\nLUdEg9/SETjAziXrCVv2YDcDkOHvOTWgkFbdAybpuhpmpRRiIc3UZzEbDhs1\nq6UhL/hmouZ8waCRar55PiliLUDSZALfgHBPy4J1aqPei6AhXbE7FENPdq8f\nIl2rTWu4r+ckTbieyqWq7cP76w3SD4HRwUsRzOQIOvFUrHuDwTx7r9oFO725\nJEsuBrcXHuWaCcsjZcQrW2kaH+ku+Hljb3SG+bzFmwonCF8CbmpBdDBJ2Q2a\nWtA/rgTRouQpqHk8Ve8983Lz/QxigSk5K0iqg0V1AZEl/hzm4qcRMgyJRK0b\nhlCrEnA/0Rc+nWMlIS1LCbnMxABRn4bNzW2idCAL4WtOCirz8+xJOxRSWTE4\n468r6w5qB303TJ/gfHEzcczUoBae/RM3CPRg0uutJjSuZiHKXxFTaku11zpv\nZbY/\r\n=8Hle\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0": {"name": "undici", "version": "2.0.0", "devDependencies": {"tap": "^14.10.8", "jest": "^26.4.0", "snazzy": "^8.0.0", "standard": "^14.3.4", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "concurrently": "^5.2.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "2e9a17c6f509a191e3fd8ea7b1b2f9b89bbe3881", "tarball": "https://registry.npmjs.org/undici/-/undici-2.0.0.tgz", "fileCount": 65, "integrity": "sha512-AngLX6ZJQoRpypWWyLsKc8NInnephtMK9wJ7AIilkQhW602HeJC+ZSu6C+syZGuigOT35n2f0+XTjQeF3Ikyfg==", "signatures": [{"sig": "MEYCIQCHzAS4i0rUoXLZKp072GpCacJYHC5UsXZC9CItkqhitgIhAMCVPmfIzTz1PA9zYpiRl5dD1tarKABDxXoevwa9D451", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 335224, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfU66RCRA9TVsSAnZWagAANYQQAIBGrP5PmNcL37OW4SIK\nP15LpK1YLZUQeIMJ70wm2uPhBDqJionqsNuDS8L2Oq/Wz4CqlFWb6iV0NT5c\nYkBFCU7RBMc1bpFsIqICm2rBOKTrZEUVN4ec59rhCwLko7UkgSQrUUFvwrQw\n1UqRU87prkIYHh9UKXkbkloajqD17VtkU2pc5OTGblxgyWwnSD1KJZXe1h8y\nfM2Yj3G0Ps65g84vgQsv9NyIUxPZZzF7q67nyjCM7C3tjJpO1xXm1Uch4MbI\nvtLFNT0ZBVDXen3skOSlC3gE3NwxmtqRs2VmoinosdK+l90AkA4H6LTFo4Qy\nmECbAERb/CeJhSFiFZKrT/5HMkQkL/QApSLQUKvQg26f1BGd22jIShKavI4m\n/UIjTtdnBqY3m6MsLeQs6krAt6Fcf1ypz2RoT57usyc1LwWiTAlj8ZNKJIDn\nSslZAswMJF9LROhzdDX1PtnSMmvJmuVU7TFTFtIKAUonau3QlY8zgtzbcNbt\nGT1ytpsvlku7vMThLfscedB8HXugzaNFs7krpn5kPb9c7SKyd9bNwkDCgtoY\nbojeV439csN9ROOm1N2iXbkPG5hbJ2PtVb0Nc5024PvPlD622mjSLP4ZuMGf\nZ7sy0Ol6Eo8lTEUwGQ2N1SLGVzThSwo0/PtqqwAPUSkw2HysFkLT+AcsACv2\nXHe6\r\n=QiEh\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.1": {"name": "undici", "version": "2.0.1", "devDependencies": {"tap": "^14.10.8", "jest": "^26.4.0", "snazzy": "^8.0.0", "standard": "^14.3.4", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "concurrently": "^5.2.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "7e0b250145fd5d757c01777a1a25bdacc051dd18", "tarball": "https://registry.npmjs.org/undici/-/undici-2.0.1.tgz", "fileCount": 66, "integrity": "sha512-ZaweWYw/FVGnMdBoTfiG2cCKZCVFwIuO2Bg7TZFFFECjlVS6jPCQzqd/J6rCjh4zrVP4Kuuj5+mq+tU6GkoJsw==", "signatures": [{"sig": "MEUCIFLHnlwkPrbFDv3nhaBYouqdAIYp38+R4WmbB4Z9mGloAiEAqFut5RG7wypBpOIDezoRSKDJRGyYV3hJoingc5w80vw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 338924, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfXLDJCRA9TVsSAnZWagAAIFMP/R3rRdL74roN7NfHTvdY\nIM1hBpYwKVMlwu4TBda77Ytx9f5u+X6NXh1IJuHCijCMRWzuyBCJ/YH9Xqkp\nFWvsXt0kBGAHnmmELBa2giYNudfsUNiywF25usVEkwju8MsKzQpU0ADb+cAi\nVvpSazGxFnDrzh2UK+PDyCkt82Y8VewnJxrS+W6fd35p6UCBESpxIC0b3N67\n6Hbf/32gBx5ndWcPzdl8Sz7W2jW7jnlfuT/3EO4Pg5FBjL3k5VYIbFs6FL3g\nQjnFmNvwS0lbZS+2L8fsUSy5OALtP0zThHLqh5ujhsfx3JnW+nkIR+dMEcbd\nAMDrhrVCVPa9l1Jve/zen1I99DAYV3KM0TXgVCpF41JB5zvxPHJ+vihEkgSm\nPU+XRpVUV141yj3HmStlWvty7xDBv0k5Tw1Z7eLWj1DafH6w+M7O2O5SaDh5\nrzc49f7EBXCq/UVXgcDMe5Drf0AOfLMwPlOX/CQCtHVR4bgqakTejehzFums\nsCrSJKyLLKEYEoYuI5QSfGbdNd9q4ZcnaCcz0QvrBRlIiZ6AttHT0NUx7jV0\nejKG/i6y03CbbnGXBgoX6tZgWDDkoVyeS0fNZfvIkbgdtnrmYdOsXjsyZ3kI\nYb9jRBnqgDH+1AXq1T/5BeuRJlR8MweFd7NbWOcLjIVA0y751oX9S1QI5Acg\nhTmT\r\n=bxU1\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.2": {"name": "undici", "version": "2.0.2", "devDependencies": {"tap": "^14.10.8", "jest": "^26.4.0", "snazzy": "^8.0.0", "standard": "^14.3.4", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "concurrently": "^5.2.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "3d4c3bd0e313f2ab7bd961aac02b5ffe49ba3e98", "tarball": "https://registry.npmjs.org/undici/-/undici-2.0.2.tgz", "fileCount": 67, "integrity": "sha512-Y59LfXhVs6yoImWVjSIcN13M/OfIX9Hw+i1WBjQhcVwsJ+3fiaXdLfB5fvq1j9zjsyXNjpnji+pyJHYnAVuU1Q==", "signatures": [{"sig": "MEUCIGZxeMqUu3Y+kP3cpGBFRB8neSrbgIFir+lm88OO+r9zAiEAmCa5wwj+eCmtO5wrCKc0ZJ1yw34EtNaIpCao6vBz1jg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 341412, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfZUczCRA9TVsSAnZWagAAHz4QAIiV95ox9nliGanoQyAy\nd28giSwCIu7JrmqejS6uofUlybu92tKV4LI2DSboSntLmoeYiyMYnlDT/2tu\nf/xFvdMCRYNX1ST5N3spT7m+3h5lvLcs+Sm90aYqcCPY7u+yReIGqkuVwrgJ\n/vEIq79iuCWxsSxDOagaMzt0TZGaK3VKAT9dK17KNO1Swl1M2vpBjqJ8SNhH\n04HSQTl/CfX7w4Sryf+RZBD7h9OOp/td4zVPbrq/uljP8m4ICJD7Pa6f7CIO\nS8T0vjmNk3viiHbjg1XoQiTSLt7FjZIVbL2X1VimPG1l+8a8JvfO6Od1XX3m\nc5YfAfgQfn9TU9bFETWbbBnfiPrZ8Qnio/IYU5bM+rrHQdb/fAd6y9sFD5qW\noPHA6vqhYtPtMMZaB4DsjhE1lM5u8k/AmD1isS9BWFccaOGOLgcSfOPIl3BT\nesQJGUPRJqnR/QfilfiPrZH6G8Mgi+PzIHaf3wv+HrHAi+QqD7QJ/j/bc3UI\nZgVYd45r+ribagbLwKUUTjtlD8YDY8nDm000FCYixP64tZEWxr+0BRkJEX4o\n4DKdAczjtky1HFOErPQoXbi1KUnMltYEQR8oDiSiAmNesckq2CxdkrdChTvH\n3VdR79wmFZnTMQPwUqYUKVRDvEY3b3J3nUP0ZIFYwkqLoEJ+AWLdDb1vdhma\nGgUt\r\n=rbA+\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.3": {"name": "undici", "version": "2.0.3", "devDependencies": {"tap": "^14.10.8", "jest": "^26.4.0", "snazzy": "^8.0.0", "standard": "^14.3.4", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "concurrently": "^5.2.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "39f1372139bcbc7b518888a7bf98f4f825b6838c", "tarball": "https://registry.npmjs.org/undici/-/undici-2.0.3.tgz", "fileCount": 69, "integrity": "sha512-dYCOTNK7aZ2T4z91fA/uCD+gnm/lndvmXYBhOrzge19HHz2Ra/l0Y8xCOtUSnUwoTxyeXGJsUjwk6G2OBqozkQ==", "signatures": [{"sig": "MEUCIAFDPuzYGhmsc4mfZdUEERkIZsZeibWarDCIcSJ5IKL9AiEAqUNQ5rMtaDRVqwhmnEVeOMn+9HJNaFbYomvqFgUV8yY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 349704, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfa8A1CRA9TVsSAnZWagAA3oYP/A0yY+fBeW14BIKUceJ4\nc76RwAk2dtQdGV8/aJSNOL4uS2cnna7bI4k1izksLKZNrvs03dZYtVZm2S6s\n167G5Ms/vHScpiOiR4yQHB3DPtjwLjTmC+PmC0hdbvdS7Ux0JqimB98RIoSL\nwJEWZboqg6VmB0kR4ZQ65Qj35KYt+rxrBOKafVOfyvO/q6qCkrRbukyZ6alp\n4AgoAYmL1Xzi7mdcEe6MnQXOkK2dR2bPrUB7w3IJGd327eKhE7hx4jY4Cmws\nwurIq7ezZ0Ag6MKefyHg0AQBLA3/WhpjLoNtQVQ4xuwfZ5DZRMxCCyXvmWGs\nvH2loEbzYu0GhqyNtvagP5kMc3n47zIrypLMlUj1rahFXaqfAJnWiAem6HMb\n14yXwi4kkSRJceKhy0S7LwL8WgADfa0N3PE/RlxB1cidKnMmCxSTRsLy/V1J\nfBut2ECBt8VYpuEatGahvdx//1vXbpWfI/HwlbAy/OtUbHJQKc4MtCQwcdbW\nUModhjKVJzJx+n/8mfuEDDP4epu1OUMEfu8yE8f+upzahIDdPwC2DzL+kgmX\nQqbttoQ9yf0i2FA+xcF4cgbrS7RP83ceu3rNhnkSPNp8QmykUoI4RdSONq0e\nUejKHoSjPF6Sn6LK0uZxfgE12Kfl4iiGAhS5KcDk+z+24Me7rzzhgRguSasR\nWV41\r\n=U1xg\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.4": {"name": "undici", "version": "2.0.4", "devDependencies": {"tap": "^14.10.8", "jest": "^26.4.0", "snazzy": "^8.0.0", "standard": "^14.3.4", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "concurrently": "^5.2.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "c1557104a68b3d1be8bd74c2c99dc58aa6b693d9", "tarball": "https://registry.npmjs.org/undici/-/undici-2.0.4.tgz", "fileCount": 70, "integrity": "sha512-42t4JHYZv3eAx8bEVsR2826Vk8V5UkJM5BTqG8RTsItn+j8r5VRsr2kjZ8T/hwbYJaEK5KdfH/iKoUDQ+eYwZA==", "signatures": [{"sig": "MEUCIQCXqBgP9ggn50bwqOFe1JFOdX+mlRMpuZfSQXl6kCvcVQIgLm9DYr7Ut69+RKYDM6R47PLlS11wGouyMZPjMbv/g6g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 350518, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfa8EoCRA9TVsSAnZWagAAEhkP/RymaqyER7TPLQ9dq35b\n9cMzxn3SzsRoCWzUAl7WCQeCfMtAxVjkw+35gFeaXZYFi+8DtcXmZS+Qa7M0\nAq8b4SI/e270Bexwrfh96kOV5NHZO/k5f//AlttSrfkVSKWQBI7lx8VxmaD2\nvWpoidY9b+zpw0l8gm0IHrrkc+y0GKsUi/Kwr7h/Bmmn/qxaM8bu8NRU5YtN\n4LxUH32iLPrLNSJ7VBPJVzlqZEivhWWhyVfR5iFZPI1Jh5B3uF9i7HbVYsSf\nZwkOkevtQCsWH6mcumGcl7+1NlQM4UcD0mItNmtt/Xum0tArT1DkcKKNn3HB\nyOEQTvQ1iBKOhz2lXgiMsutHtCmXKuT1oR4hZU1bhuo2je//uAsBiH/CPxjA\n2XW7x80LBQfN9JQB/3VZ8F62pie5XOAXifj3mlIdX7ekXQns4eNUIU3yEjPh\nsVZhh2n6dfVgMXI/8rpebgdguu6fksYUTtXUQ33P10QtBHD3RTOluztbvtJR\nAceNtpGRk+FagSlcPgurYw++OARGbbDscisxRxGXO1rHXAqKCvdBEWHyeu8x\nhFrjVHvpbdOM2revSf2pek2lFRy082HIj+aF0ek7lRwiUinecSiud9bJfM8/\nkIOdtj3pE3G8Z2MtSJZqhlzap9LeP7QCsp1vuKd5g94Fy0LxCGT/RwpmIae1\nRx5R\r\n=YNAM\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.5": {"name": "undici", "version": "2.0.5", "devDependencies": {"tap": "^14.10.8", "jest": "^26.4.0", "snazzy": "^8.0.0", "standard": "^14.3.4", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "concurrently": "^5.2.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "b604e203184002d2ecf6524581ac994346700a09", "tarball": "https://registry.npmjs.org/undici/-/undici-2.0.5.tgz", "fileCount": 72, "integrity": "sha512-KluDT7X78oGS+/3bxwGE06e/4x4wbuK7TNmTMLPJNmEOkzrLGBMwAnWMxm3PukR9BnB7k20IzOpGjl90AltwFQ==", "signatures": [{"sig": "MEQCIDjwbGstEbq5619LbwmH0206QbG3rIo85D0/Jakb/t6aAiBVyH2qHP7VzBhPCKjtx3r3eUfuq6XN+mVECJF4ynTX9A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 355054, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfbQ+uCRA9TVsSAnZWagAA2bMP/j7HiHQ7TnLPUr5tt25J\nfhKWDF3FE2x49ycS4WVcz1DEfwc9LkT09QE/bxDk1QXvytUqkDxCxxbTqo8m\nB9RAANR7LiBws93i9L1+V3RcSItkOfzaFnyP7F3X18H+rpzqCxz0q/0lVDiY\n+/Z12euObd/9/h5Fn3q3P67JkSWoagbzLoICG3S96NiwwbsOMj99MEoIMCZ2\nb7OQ5QkFJMVCKMYz6gV7D9TJe5FO/J7cQneRmHgGadlC34piTxaylY5e7SG/\n3eBFpfX+I9REx0KjAr5pgOH6HI+btW223Ll2TZo1K7mVkWaB++D0GoDcC8mE\nv6VaVINehVN7uUHYErBsxXFjq5pRlRiLn/jQBQJITuP1wgQ2WxkSYf91bvhq\n38q4sQmbE8OoUbG5l+62ts0RAr0Kq9SJYvY1wbnBUwHwYzR1JAbZ1HkQGmGi\nOsTVV3a9vFYKnv0b8m6lniDKaRKzOCTA4s0vhXaoMCY4fu5xUzJ2JCfOEVVc\nm6YJUddAGpblAcvXG+9ayRmXneaTA9N7BJYmmMN/h5DabPr6BS1iHALVplXZ\nJLEtrucfz26KZ/eOmJ8JxhskGmjC7t/NG0z15S9AC6BZ0ifsRqCFRp2F3pqe\nzlDn2BS/QIdtba/4TBvSPICXz35F77VdUF/kJCYZh2DMo1b1o0Pb/1qVVeZr\nauth\r\n=4bzl\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.6": {"name": "undici", "version": "2.0.6", "devDependencies": {"tap": "^14.10.8", "jest": "^26.4.0", "snazzy": "^8.0.0", "standard": "^14.3.4", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "concurrently": "^5.2.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "d0d2851c95a2f80a1b6670c9e31f28dccce8e5fd", "tarball": "https://registry.npmjs.org/undici/-/undici-2.0.6.tgz", "fileCount": 72, "integrity": "sha512-oTa6d55jg0NZT6AI0gxAruksEkibEKnhc3oJPeH8rezcjv7fogc8x14A//Dc1KNcZtDZO39MCSWqSyr63QB3EQ==", "signatures": [{"sig": "MEYCIQCdJ33eqinADMCvAYcmKyn8099koPGEoBy8vyKWZ7LH6gIhAODMhmBfWCld9JdqELiZ1w+d+IMUpjWwLZ4wqowDrQqV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 355596, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJffh0lCRA9TVsSAnZWagAAXjsP/imqqsE+AUis/IhC+VwO\nrkZhatak+CnBv0XRFM/xED6auxKriVU5OmGDGQrFTz8MY97Bd+/O9I/vDHJ7\n+xVcBKK4Yqp+BMY0zO2bGMUBwttOlfW7wHFgLA/kEz6wstvtVr7433KKinZE\n/ANDMl4uCCSmMspu42eeEu0ZAHiRQ6TAW1ONs02uDmXRSXKaNpbQFe6UP6Bj\n4bShIBMoO34GNJULW9AD4rwBNwB5yKX8YeGcUqCaaT4T7X5L2gPrxJcpRqCt\nzo+7KKfbi3IQVWB+q1a2dNSJDDErTlsICUT2aOg8mmCY7NVYi3uBU5KqMja/\nP3l4tJ1xmSfeX9Qz9TTY3mnXRlsks6LxmV0mMFauqT0khmlfK0teMClnoi/7\ngZB9p9LS5FYKfbQoMQjQ/uQX3GnQhezglT5XNmPqSw8eMfYrYMj8NceLIGvp\nWCttH9B53+2W9Pm5wZs3DbqYiDK82JygLmLjcEmBDiFgngV3g4VRdR8grKle\n8YinwT92D1J8z7uVPsON6kIJ02AufRwqYbcF/cRPYHfx71DfQMi5yPMsn+O0\nPG8STS4gFVqJUBz5SJR1Rift9CFPijXbB5ReqyqqjS3Ox01i/HSoVs9k1TpI\nuhnY4QK2K/aF31PWEL9ERQfwB176gJOnQSyRtPig+eTiKlPClbnCw93Q7JbL\n8rXW\r\n=wXtR\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.7": {"name": "undici", "version": "2.0.7", "devDependencies": {"tap": "^14.10.8", "jest": "^26.4.0", "snazzy": "^8.0.0", "standard": "^14.3.4", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "concurrently": "^5.2.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "3804cffa4c64bc6ae71e6b0f4d85054ea6b0fb91", "tarball": "https://registry.npmjs.org/undici/-/undici-2.0.7.tgz", "fileCount": 72, "integrity": "sha512-3YoSJEva11i4iW+nUfo+r5EP+piSO667SU57hfNeW3kPG5ACl7IgHzhT+bT23j0v1lgs+vIHfxQfTGK32HEPIQ==", "signatures": [{"sig": "MEUCIQDeDCKVIngwxTmQRC0b37Mfs0n8hK+Z52/wKy+bhg+fEgIgBW6eQlQL32gjKJd2AQJ1ydnJnY/H0BItW17cewobaKc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 355804, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfgCQnCRA9TVsSAnZWagAAMucP/2Inmqz34xNxOjMXXuPH\njUJqD27XWgiWBUf79D3C1+pLenk3qka3cpPFjVzb751PpR5a6KDvbvEnmjBM\nnA9OhaEEnYxiaEBRye4pPLDq2rcXSqP0uhAauili1KAkgcPkV/m25hs6kdi6\nXhuRNwZq4Lxt2GBu+vHcvO1+Ws19qqUDYKI1WrdLrw1VAqQT43tNLmww1U3l\nG0O7mOopMY06MVyeGjir91VSs1wYGGTHC8XzijacRHoyjJ5kXs9PrBmK0wAY\n+ufVyVQKfRZXImZp14zZy3TKsBBCUUJXDAyWu8n+y6xiNpiq6nMlXlQpfApZ\nu2E7Jt2uj6S1UoORK1RBEuiyllV3iFa4aLp/OYK0xSl8PzaeZuZgSvGTFQ/L\ni+mn234ODqSHgn0sF7OfGo40gMlPjr0z4w0nfhQYWnUbnO2sjAi5EjZzJnWh\nkqNgrwdMmqOvl3ej/oFoFCO+SyI4WqOViN2IWv5uMPNHoBhadgE/Q2PvYTnI\nfzo2pCGclIV8XeAUdlC5g/JQ8o/5QLQU2gJ+0HkD3+EfyJTgyx0oHjWc5Twr\nfPXHaXMzhTDnvziT3tyzyMcB3OwUU3/p+V/X6f2epJasqr+xy0d8hMaSz2G6\n5do/LZ0lUuILh6/Vvx/MhAzGRzHGPm4PekKYx08CC5741kHaRFZfXZ9E4lre\nkPv1\r\n=DM2p\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.1.0": {"name": "undici", "version": "2.1.0", "devDependencies": {"tap": "^14.10.8", "jest": "^26.4.0", "snazzy": "^8.0.0", "standard": "^14.3.4", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "concurrently": "^5.2.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "b03c8befce8498372f0e5525d8ea50845f86a1b4", "tarball": "https://registry.npmjs.org/undici/-/undici-2.1.0.tgz", "fileCount": 73, "integrity": "sha512-4QOaapBV8JNs/mh3AA+72520OnFKNIJfA1c6Xq56NK51uI4zU3KpSQzrcBuJbMWu8ivaCj8hTcyDQefhEO4JdQ==", "signatures": [{"sig": "MEUCIAtydeNz4pGsBpaptUDiAcK15W6jG2xeNxYkxJcZeU/+AiEApE/RqdSBS2VpWqSVF3aKjF3v6edavWTt2QczzE5pYIM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 356052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfk2xQCRA9TVsSAnZWagAAIwUP/2UwPS0cuFe5V7l5QZXb\njVgOyN+wWB4lTmmuKHVU2y9ulERKm3zVKYcW1/zMSW/BLMzx8mXz7SWu3qWg\nxZxAxFuroN+E1xMgJXeGJ0ZKgjIzXt3O0tU09M2UOOx6aE/7oNmxEFlETCho\nlWCxAE+mwRPo3PgJW7y0DmbUIc0yiEUCWwjwvihfyytmrPALaq3SbDhkqnmh\ne+hVESiFGpzMHtil/g33nf+vaGdGAPM+QQBw0rmZ3STByQk344TrGCfIEt/A\njEWN2bas0FFqlYXXloaKTQZ33OTkgLGfbugLKPWN2pqhq0e3WVg85GmVG6PO\nVTaDAuj61VfXrA9p+XcSQao4XmDBiZbOgW9DMbklig8Y6NZVcl/B1ivwMgBp\nLrniUIf+hEX0MEq9OyT/AodJQefqNdXuN/LZrAN56UhLPAc/lw+t2g4/ixpn\n/xxXeJyPD+IH/WV1h8unGzoPGSV/PMAx6pgvKHNXviWHceFefFLZ0rHosIhx\nxGbDutu1tOc1x0oQdqU+7BNJJdZnFmK8ywd7QQIqSamZb9ASlJOOBOBP9SbY\nb8kx2Hwbloce0W4IEwT+hLJsEOcwkELts5IExivZ68IhLG9KBCaT1tKfSUDK\nquRSeW2COR5YlaCGMCIrMTa5z0DayOJJWp1urq/c+vY67XoHNbdPcNmJbiWM\nhhkY\r\n=OvlR\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.1.1": {"name": "undici", "version": "2.1.1", "devDependencies": {"tap": "^14.10.8", "jest": "^26.4.0", "snazzy": "^8.0.0", "standard": "^14.3.4", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "concurrently": "^5.2.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "4b7a938bd49fe5f45c5f5398aa4a3c5f2e0cd2d4", "tarball": "https://registry.npmjs.org/undici/-/undici-2.1.1.tgz", "fileCount": 75, "integrity": "sha512-4HVo2WQ0Mg98UwFKauN7UCqWtQcYZiApv9LeqUPzKQEZhZDnnz/PkM0B+1KU2ytFUSrUqlQZ7X0BqyQJskvNnA==", "signatures": [{"sig": "MEUCIEUmJcY4VHk2gHz4EaF0GGyLw8jyFi55rNf15z3jl3zFAiEArGwNwGb+xjTRcRWDxVMRkX7O1X3SrjtLUUQrLiy6g4M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 359911, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfoHicCRA9TVsSAnZWagAA10kQAJXDgW4YmUgT7M+LCwv8\n+TLPgE0G1Z+uEmuA8mHN34HFD8eSDzkO9kHZxoMQA8w8u8fC9hMqWZPBFz/D\nGDZgT2Wj6IGIypOzkMGjOXbjVWlzMpBGzDnJZNwjSqj9TDe9Yb4glES460OD\nUuBBi8+GU70TrcIwd4m3Hz8ESSVuU1vmFgCYb3QPBKz8AgdAQsUgrh8STJj1\nlDwNCutTTV9/Yn8y5whL7bDYQwwuFJ5qqN1+lmqTAthWc8dWEiS4IN+Nxu6M\nNjfTceqmQt+BY/zcbgEGOxL5prRjSSSdm9miOfm392jl3kcaiVDZzGpevv8z\nSX31f2HIOR+ap3oriHUMco5dl295AiWbCtuuoidOp/NK3gdVL1ivarPCZJPx\nxCixVATIFzBcH+OHexuXAEgNBjxfhGGlPAx7Jbh8kVHLg22B53yup9Z6dL0O\nuYUgNwxiGN1FIrX9h+phyyfNfc9qLT7b2yfeqpA1/SJ2YkEKbDgYPME2LNWf\nxIKYTBZ6IeUK9INKtQyXJCJgEzkPsV9V1RvFcurraGnHS2YioYp7kVko5qpU\n8EGHrcsip/m+MAORQfG+GXm6XemDZUm/sPDKeMx0azU4gHXQUyLfHKGaBEDf\nPjnYSExhSVBWgoN6u7DbMCPWGpc5IAOiEHk7xGmQhtV5Z2FpByE+aquDXqOI\nMmKO\r\n=u/4g\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.2.0": {"name": "undici", "version": "2.2.0", "devDependencies": {"tap": "^14.10.8", "tsd": "^0.13.1", "jest": "^26.4.0", "snazzy": "^8.0.0", "standard": "^14.3.4", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "@types/node": "^14.6.2", "concurrently": "^5.2.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "ffef206ffb44f0db1f4013d9c1fc5a89dddf8c9c", "tarball": "https://registry.npmjs.org/undici/-/undici-2.2.0.tgz", "fileCount": 83, "integrity": "sha512-pNts1nTVW1e9rOFGXdueH28FGYZz2TdeuQtJSzcto95bx7HQCegmJr+A+51fW+XU2ZNE4vZlzI7VnfKHuALitQ==", "signatures": [{"sig": "MEYCIQCenSU7yjqv5OnXCLghmaXJs7O1X7vNjP86m+0hbAEUFwIhALRSwTnk60tF+5pC5eyDKE4nJDH/JCkUymVmLgNaJcM6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 385301, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfrSOCCRA9TVsSAnZWagAAw4YP+wT41omC+DwrgTQWm9JM\ne40cQPJmxdGQ4JGQWltjku0wxCRULRTeTEO1F8OzRyIQDCWMFZprWD3A94rd\n4g2rathsvC1dR2MBHPL6HnZPalsBc+DtBDBWHxJWO4jMsKW5sAhs3oiD2CgV\nyLhORU5m/MiWFq8Rzgbj5UsbzOOYufZa+moLKvmsaGaBuzZn8wioNKfd4xd/\nABJJm6uJVjpdVkZH3Wa4pVj9naX26HOfb5V4qS9c3bu26Nb4i7BLnjqw4hKs\nt7z3zIdX3N7Vq3l1n2UQk2XaJFGNgc14xX/77fULJoFFLgeMyz6kJavFc5Bw\n1Yv+DXypakAPFXRWxgxuJHSMky6uunxOprlNOD9CRM8Ch52v9FbIahsjlR/X\naSvIR/ZOhizgd/ixzdWZgYPa48zmeg4dEVq3pvG4UEvZvaMQwXnCUFoRsi4E\nhJeknwybREeHdoPd1Y5KqGCr9LcoYCxCF+2gBq8A5quRmCKqu9DTlsmU3k/K\nqg2wQo2SY2l1G04rku6XL4ht23wMFZNUsJCDOjigsH6M62gjMMMrUlcsW6me\nMlR6UAzQRUmmSxq9MIY0rGamILxLnnnmuS44eEEfwOrgeLTNO8DKfJ4CJIzL\nBuaf+xRBjXexxL6s7qryg3zCTnd6kH47jaVrw1RIrecp3eu/wjvHvJEJLj7N\nBYA1\r\n=2NyG\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.2.1": {"name": "undici", "version": "2.2.1", "devDependencies": {"tap": "^14.10.8", "tsd": "^0.13.1", "jest": "^26.4.0", "snazzy": "^8.0.0", "standard": "^14.3.4", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "@types/node": "^14.6.2", "concurrently": "^5.2.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "7015c205530a3c71de867291d2196c50272a287b", "tarball": "https://registry.npmjs.org/undici/-/undici-2.2.1.tgz", "fileCount": 83, "integrity": "sha512-21sJmMvJOMsyt/2pQPgB5Ruvm2ADTTm34NHRy4kzfeW9uMO7gK2oN0f+5KaJCmoKGJb8KxdU6yWpW0SphFHadw==", "signatures": [{"sig": "MEQCIFXIV91QFeWvehTSYeH3SLzQIpWFfyGKQXnjiwuxgVsdAiAc4SJWatIQghrISVc5shEnAr2jUN9CazXe0FYXg2MLrw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 385808, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfzxseCRA9TVsSAnZWagAA9s8QAJPEZ2uqJyAx0Hc6S9HD\ntaUOAx9ZmOb3+aw93DS4LNMEFmNwheuae/bpHOydJnusnSVndPl0E+dYPS7m\nY6iBwoajDT9MU9xUdiYpth/dMPv5KRYNQn9DlQ0AIaIRnAPpwJCojxbEqL8T\nfOB9g3PmG6EsLyH0+CvfVq6UL964TX8Qz8BlY59Lx6MOLvwAIrZYvqCdpU9x\nNQGeNPB+eF93iWi1y8VLGauaydgUwn/QYmnMYjLDiyZVg0n0WemUos0J5Ue7\neaW4d0SEMvNqLOwRAJHgwWFVxo9f13vHop9rG0BUTXBGkWPKEU0taOZiZ+tD\nWRed2hdUo/DbHp1jlRlgwIrBU6KX1dudtGhPhOs42aY1ysXzPrQYACGDpfTb\nN1N/krnQlMN4rM0dUdXcNzTR0cog6pMs2IbpSSeIP5fgcyxw+JjiGuNZ/NOx\nkiYNtDS/CZ8mQ7zltcw+Bw8HLPyAwU4mEa7PL4lLO5kB1ahgXSJvLFSmBWjp\nTopqRd7M+JJFMkNek46yEoG2J3nBv5fGJOFBoURk70cxXeyfHq+Z1wxk6qhL\nE9HOUZ+2D3wOewUdU86WTmk+XRuuwyKzlaOzIsisX6YPJX0Vgp9HtSSGnCL2\nURv43lPm1eNN/Wm2MOWbW7VT4M6YR30zub4LNlwVbbASL7G4zxGWMmD562NH\nG8xO\r\n=2HZo\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.0.0": {"name": "undici", "version": "3.0.0", "devDependencies": {"tap": "^14.10.8", "tsd": "^0.13.1", "jest": "^26.4.0", "snazzy": "^8.0.0", "standard": "^14.3.4", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "@types/node": "^14.6.2", "concurrently": "^5.2.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "530d104ecb0b1a7b261f439294d1e79549bddf27", "tarball": "https://registry.npmjs.org/undici/-/undici-3.0.0.tgz", "fileCount": 82, "integrity": "sha512-qBIjzq7IZd7gQ7pf+JjyT3l3kZbsgpPlRqZRK7QawkLvdDY4TIk6kgOEoZFceM6mFBXM+1MROfr7bpi5O904sQ==", "signatures": [{"sig": "MEUCIQDVm3bVVs36iUgLhPTM170dzYgL46jlbaP/XS32gOFK/gIgdoO+vGfBxdEKEatIwqRQfD+HXvdZg4aWhtzJxkp42CE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 395798, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf294wCRA9TVsSAnZWagAAJtwP/iSw2bSeMyZdHdqJ7fh9\neyYbxvwadvj+PjonpBn+/lvEoPSTeToPsgdoZWRJelfSJVHhlcinbj0l3xVy\nBZ9ghd3ix58pFbZZek0o/zBF3WoJqe875dh5zo6kvrgYRh9uDeqhTR+AROmM\nQ0sPSz44D5cZu9qG1wZrU/RdWtLTL6LG/qQm1gWkkHUXmmF8+M3gdCG+8x4N\nLgt3fdvVe6NKylod+nBkrD6omzQz6SzCT5XUaIXMsGrjatVNgOi1ooiBBE/O\nldwsW8m2VNiwlGhEsHpoFppneZcj2GdRy/b06cuvD8HFklcTw88niXaFPxC3\nRe1/CUD57+qLD7gWLPWuADzekwZ+Jf/GQdhwARu9rKM3uh5ybj8rEnzsHzX+\nJIJcwupAE9xcIzpAjlnlY/tTxTySo6lW2W7Lc5cGqKxWs45cNFFO2CSgVxxD\nccFFW1hGrT4ncnd68dsBINfNA8s1ft8dTTM+bo3ErcaKfPFw1XBCLTrHkAAn\nnZzIuI6Ec+fSEolcf0vZf73ZOQBO0HqxQiGbmVwPwvW/1eW1G/FIlhnaqFO9\nIZtnjTXF/eKlr6MrtC6jpnT7NoECjt/v6sWCARgaGDFUW7Cmwwkq7EyTw8+Q\nqVLeiHfXOW+iD39bC+i7dH1IfYXdN3aV+z0bMQMJtoRmyCg7hev7KBPz7kHH\nOHbb\r\n=Qqzc\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.1.0": {"name": "undici", "version": "3.1.0", "devDependencies": {"tap": "^14.10.8", "tsd": "^0.13.1", "jest": "^26.4.0", "snazzy": "^8.0.0", "standard": "^14.3.4", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "@types/node": "^14.6.2", "concurrently": "^5.2.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "9f88afe471dfce068b59792b3695293a0679dc20", "tarball": "https://registry.npmjs.org/undici/-/undici-3.1.0.tgz", "fileCount": 82, "integrity": "sha512-Iom4aBVv6YEsiXDnd1MIDsyKSWaa+l/ZoJUlnFYbp+6f0rs4K0d1Ohb+A/Z2ZxSks/m+nk1rgmCcm84DM3THWA==", "signatures": [{"sig": "MEUCIBQXcWknfWIhyjKs90NCe8ZCLpMmFE2MVrzQ4y/SQq79AiEA88eh356C9v7lQbND5+vZCkJR+G7N7EmQolmP8lDQuXM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 399216, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf/XHqCRA9TVsSAnZWagAAKEEP/jjx/wKduT+Z3VC79636\nzX5Iu3pJmfWZEzyhGZyggOTPapHXTTI+texPw6sYM2aCtpv85pKbzTdsHoXv\noDXZW5Yue7cLRAn5HjVps4K6GTAR+TI1bhpL/PjegkYWZVvEvDaOlpO2Pmr/\nVcnDThp8MabG++8l0t3Mp7+KKz1g1YT763to/QOef4MgIvX/0clSfVqTTnGh\nHwIkmPMppMaP18WfiHpUe09RTY0MfD2SUP/6sqctfH24IYcvKdCe/gItgLGA\nrNCjMBmsxriR6+8m9L/nTi5zl1wVdpiIHUmch74lez3of+VMvaq2cf4+1zHb\nj+0Fp2Vbv48V8i+At8IWWHBNCNdMNMi8B9oBW+5VqJ/LJp7JNV/p2Q1nb4nV\nLK2EdwiyOStM9U5TUccSUs4rWvlkO8fo/ZJlPLxpc1kSMtq+Dl4Sq8WQUgMc\nHxVrNteoxn93N6sG9kvTMnH4BUXoUQvrXtN9c0WgStu21Dn4o+iryyKUJCgL\n49qjK7z/r8cSRMWlkBMgAYyW4LgCYu5g3I0KgvMS9t7OCxkpAWdXJX8sCwj3\n0F+OOatkWNzrUlBwVrw/j3dFD1OcXaSsMa5ksyK9YwrlQC82jJXZZZCzl6+g\nTb9V0lSsOkYTq5r1nXEHgcvmLVz2APbR5dOutPJBMyXqMkGMZ3j1mB7oMPiD\nfdJ0\r\n=nDNX\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.2.0": {"name": "undici", "version": "3.2.0", "devDependencies": {"tap": "^14.10.8", "tsd": "^0.13.1", "jest": "^26.4.0", "snazzy": "^8.0.0", "standard": "^14.3.4", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "@types/node": "^14.6.2", "concurrently": "^5.2.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "62ef2336f25d965f321ac799db784ebeb8313ece", "tarball": "https://registry.npmjs.org/undici/-/undici-3.2.0.tgz", "fileCount": 101, "integrity": "sha512-lBvV7jZirNtDbDmnDJTLbfFDJO6VDav76XRqILfeERlSnAWeYn5pAo6JdPc7OM55RiBZdsh8ucRG9TNfDrDnhg==", "signatures": [{"sig": "MEYCIQDs+uLPnaG3QlTM/RoT7RroFsCUmRA2zrUz1hNsw8xTHwIhAJttohLjY7FBg0nbcHjDu3RqwF7tZlNOkTkRijVwFnua", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 422029, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf/uoOCRA9TVsSAnZWagAAHasP/1VVsNNF3T6LdpjFf3l9\nWBOVQWITibxBLSzvVEiFMPaViqITAOvlRBIaY0VaFMgPyTjHB3tS8BYKEqej\nEAHQOY9QUP0EsKCvywR298U+1bzzqPeZ7AzYVzlZnyN4MCDZpBKZquDM2oLK\nq81OBQGx4I40H7RbXE9WqIpjvSdqj2oWcp5aRaPHLSiopHUaW+bkH8est8TV\nVbK6fXPuF6pnLjX4xVEaaiEtdCqCyj3+xrNdPyYaXHatz1TbuQGN2ZtwkAVb\nVyVHcIqnE2M2naeHV3fGK9GsZiDVM0F/MYUF1W31D0Z0ooT9ffo3Uiao1kmp\n94esQZwhFR9P5N6WTp5Er5xsIyF0us+Ew58rWd6BUGWYwjX1F7mDi62xkI2n\nIeX/EjfnkfmehTBo2MTOwSnA9BfG+168rujrwT6oZcm2t9kbCe7sAlqGflFn\nGQskkjhpyNANXLSKB/oyWnaBuhB3ous7iRXNIMT7+cURn7bkavXnNBpEHqXy\nX0Yr6VCz8DEQ1cpBQUUGGdPddiFdiG2qSew1b49C4/mCDyi4C2AOFTq543iU\nByVWHvdkVyP4vdCIB8Yad76XjBLwZHOGcTfONFa5CHUKdrOE/bCZ8+nR2hEO\nRbEYxiIz8ioMXTlFSRdTl8UcaunRTjL76URKgoJltr5FJ6xXKgSMBrmm1Ykd\n9pXW\r\n=ARxa\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.3.0": {"name": "undici", "version": "3.3.0", "devDependencies": {"tap": "^14.10.8", "tsd": "^0.13.1", "jest": "^26.4.0", "snazzy": "^8.0.0", "standard": "^14.3.4", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "@types/node": "^14.6.2", "concurrently": "^5.2.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "12c5eeb895212612f98a7a31f0911c4258002a39", "tarball": "https://registry.npmjs.org/undici/-/undici-3.3.0.tgz", "fileCount": 101, "integrity": "sha512-tZtXXLrX8i/tVmlx6BURTmCBGriz8CC3eQnPaysvOrMItWXBaWto53ulJj1wwyR90013Bqa1hI/gSqtvsUfu8A==", "signatures": [{"sig": "MEYCIQCgaBdehlrlKrZHWF9XCdZgO2n/PV+I8IqgP0ChAQTfdgIhANMFgF8iLCaW0dccUhOrwF8Udg+rOorSqM5pjxpN9iag", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 426962, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGm3YCRA9TVsSAnZWagAAwBkQAKS2T6WzzB1jflfiwXgp\nj6PoWOc/zg/+MdXSVZZYPU/L/+jUaChKvryZ/goxlRM6CLJ/xk9UcUrHOoI2\nyABdFO9IEzNEL1S59UVu9747mxMK3y4M8YTWFCi+mlpHtqKdFH4DWys9AR81\nvP0nawZRdtas8bdWfeinfKyDFfAnAPRHSE9FxgmJtyf0xizA7FPTScySqqok\nVoTj4Th08Ojmv7T+r+HqFa4pXQWE5k9NeACeeoEW+W9ABOURhQ03V5KRm3P1\nkLhLE9SS53LfYh4PH2XPmAm4c3WZcB2SFYN471+tgsL4HQEZk4jYGBImQBlT\nqf16f43H3vVaOqBKCRh3vjZ4FVUCK315rlXVjjG8OB/T8ftY+gJ38zMKkkfT\nPqG26m6CaupJBs8KtVi0+h2E0LuRHs+HjgoP0MaJafQN4NzxT1YUJZ3yuvGc\nb1khezcpY5ySE/YownAYvpzJGXwvKV363qBhU+euK+QwS4tXhLcBiteYt++o\nduWBljJWJr1nJF87IlYxAjW5MisPwUYAZNC6l8ng/sKih/z7mV/zLX9x9ARk\nHTNEQ7eExZjliXANVbY6nYOKzAgj5TpeDTqV2lTt6mrIvY188nicJ0dplDG8\nVS+Ej/U7eUMbQ+SHs7KtE9cHGcBn7nRXlLU/s+ZrsttALgGIRgaBIHUWlcLR\nIeId\r\n=Mf56\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.3.1": {"name": "undici", "version": "3.3.1", "devDependencies": {"tap": "^14.10.8", "tsd": "^0.13.1", "jest": "^26.4.0", "snazzy": "^8.0.0", "standard": "^14.3.4", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "@types/node": "^14.6.2", "concurrently": "^5.2.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "85bb37ae75e255867a54c61e648c27d0fce94b1c", "tarball": "https://registry.npmjs.org/undici/-/undici-3.3.1.tgz", "fileCount": 101, "integrity": "sha512-hatYbCN8wHzaAdADQXRGTxWLDVDIUfIY7oqiGbBtVla3p8tQshPs57NL4x96wD2x0i6qvxGlaFUpVh1n4N6lJQ==", "signatures": [{"sig": "MEUCIQCEqzR3HwFa6S9kX/PayGbxQXRlahVCIr3drfMnNWjYuQIgK6X/MLp/vEbROGkJ2fLVMbI/2l7gJ9Q89bgRqrX0Hxs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 426692, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgIO6UCRA9TVsSAnZWagAAehQP/i3B6IrD2dGW5nDxsYb1\nkkGDjGSw/1Y/cOyadz8Jf7mbpR7Obe3+nYz512cJ+jUcVA2KksljByLM2svP\nGpVqq9zEn9AVJUo4zpZHGoPrroiQDUFuHGoYNwHbVYtU8kLU5cKAlCAY0FWU\neF8mxn87rQ/N3hl1lCjh0sktKe+VE0dqXrNE8SGpzgYGdDJ23dF2hM51g5Tf\nBiEHl+ZK535JeITN93xp5VZs3NMW6hdnwFPdCTinaJLhxTvlOcWQbg/n8vIV\ndi4FVGtaUnTk5nZqiFq1wSqhD7M/X5CQuJH3nDATs6X/k8zW24H1/QPh/yDo\ndtZ4sOMZmlxUbLCLU/gUfzwwqAuhvnBpzCUN69zGWYVlWDoVwiydZMS/XoRU\n0fmmXoPxCUj7JREjgH2X+OuLxQGRwH+i+oPCv3P6z/2oNHXUAYZnRkottZAg\nz6McLJWsfiZlvL/UEwYvOeCYP1nMxr9ftjbL60zFpuNvUUS6O+mLH8DD8gnk\nh4+N+Tmfn9a/il6hMcoMRJdAtxYYhB/8C+xhvwHxSsufqJBJUtrOGvFAfuU1\nhHxxkoojyC3V1Qz39r5wHZXmK0R+16bHVhqsk5Do0mIeRJuHa9ZWg8WZmxfC\ndqnEC91dZTgEFILI6K3FCr31B8rml7tdFQR5Sx+C/ehU3YnWkpdUUVUQ4u91\nB+A8\r\n=e92z\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.3.2": {"name": "undici", "version": "3.3.2", "devDependencies": {"tap": "^14.10.8", "tsd": "^0.13.1", "jest": "^26.4.0", "snazzy": "^8.0.0", "standard": "^14.3.4", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "@types/node": "^14.6.2", "concurrently": "^5.2.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "7b9278dda75c459de28580e1f383ac65ed0348d1", "tarball": "https://registry.npmjs.org/undici/-/undici-3.3.2.tgz", "fileCount": 101, "integrity": "sha512-n5RNeMcjixcuNYcLnI+W045urmNJ03JGugRrKjQ6eKeUFimhMeuXgMWjC6/Ar1/cePh8V8n2Lttq+T+KvkynSQ==", "signatures": [{"sig": "MEYCIQC2TcVRER5LGZs/NrKSJ2FOvQNtGt1XjFDtoF51mwJ+4wIhANW0GTRMvA/z3SSeeF5cQe5O8zxl5HXBmTUOVnbU0h0x", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 428227, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgJ/cWCRA9TVsSAnZWagAAMhUQAITS9UpQOG4OCHqnOhPv\ngjua/64UAxdu4U7k5zZbmGt7V8oozTjnLZkT4QAbFRp280t4NXzKNrT7+O8e\n/DPtH7B7qHsqlrmLKVoB0nH2eC03RU2y869xAEAVo1K3SjZcwCvjJ0InSBfP\n8LC1brnYGw0QLHiaqU0vy/3pLfP5hEtUxpismg+Wt4eMxfoGZ+9jJvbes2qB\nkq5GynDcjOQT/JAF5FSdhN6gr+x8HKyx30boYjtxiBRWsdTJR5QVCxzAhe9u\njvX8vJR+cyJGmOmCQm6BlbuU2Io16WrTZ2hv/AKGvQPJli1sxAcQyyWCCXdp\nHLKOULxxU2dOaMVA9JX5NwKW/FObKRNQiHMVvymJAloy1+3lBy+PjD6aN2hL\ng6Cu0+wW4xnyqSbH53Nr4XhwRI6vP5j4LjyQfKJGDaLorHocBlJmX3RdGUb+\npgrFW52znzFselD9p20GnBW7TG3sQ5l5D3BGlGLqUX6PZZLQvj0ChyTKWEt/\nYCONqz4/2freTrHrecZg6FhW1tixHYYpyOQr9XxbptG/CWJgDmlGrh8Un8ME\niikILPkWDDOO1yCeaOSnVT5ld2oljWzqcZOYRmvs4USJMluOiN+JG7wgbUEL\nPMbCIDW7WgOQAPIGVEVryeIgc/LvqN5HnbW4Y8I8vquw57BsCY/eArwfNP0R\nNHRu\r\n=k9yY\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.3.3": {"name": "undici", "version": "3.3.3", "devDependencies": {"tap": "^14.10.8", "tsd": "^0.13.1", "jest": "^26.4.0", "snazzy": "^8.0.0", "standard": "^14.3.4", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "@types/node": "^14.6.2", "concurrently": "^5.2.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "a90a783a5605fd3d0e093624e261aae234646452", "tarball": "https://registry.npmjs.org/undici/-/undici-3.3.3.tgz", "fileCount": 101, "integrity": "sha512-JcC6p86DLPDne5vhm9nZ9N6hW/WPCtO8/NV+7YHS+x/mQ+NpWvtGxIt28ObBsySPec8FsabyiLPhmn7Htl9w3A==", "signatures": [{"sig": "MEQCIH/Mj81tQu2t2hUh7NBHDfzgLrWT9nBzu4fYTfnF55XdAiBiGKUAjDH9KLLoMVeUaYl9Uauy32Z9SdbuZhBISnWquQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 427525, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgJ/gfCRA9TVsSAnZWagAA7TgP/iLIcVx7d6PReDNq0QzQ\n0F0Gq3aKcbUiX+Ha8z8WtWSHkqE06igmCKz0AEdz2UHHRX5UEEoYC/fy3mQD\nnuxKARffMHgxthODGF5Gx06GxDL3jejnnaHFD99QDkDOZ2coOEQCamkDS94m\nUJAbdAbKh87iEx6mbFFMSpnFUH1lhe28k2NKIT/glZJfm12PDrkpi3C4Q1Op\ncg3H7439/Ac9zf9Qervxsk/+1egZDvTZetdagNQd7XOEgs2BMeYaO7V4YiTS\ndIkNt4JuhWTPfTkGF0mmzoFrhTaQ6SuupY4Qw/OaCm5FRF/rel0EyMHPR2XQ\nszCUmH1taEs/98kEsSAszjkCLmrFJ4zLWemLVSLp4r4PyTMy2dXrCVPfnKmz\nxkQVdHHT1irN1pu3aUm/sVr4VB27mIqycx0UKaqbpjN/aHnK6Ccee+Eyl5iX\ndyScKrv3Hr4XQ4NWq8RlwKkrbDpCacLtOgRNRxU/ksC7EaQLzpVPn6XYm6EI\nOpFQfwhqPvd4R8vqTK4cIo3ZduYsrFIOA8tQqzfBjR/Yl8AEuFm6pI9YzgVm\nb/lJXJRmtIzqFkBiuG+ers7/jVfMinIR9IG75Ndy3v51AuvVYcjCySDD7af/\nNLGmP5ErJglILzbrn9+9izIVsuUvJQTQax7azGXS+dpgJmG9te1E2wjKGqv/\nsAqD\r\n=xb4a\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.3.4": {"name": "undici", "version": "3.3.4", "devDependencies": {"tap": "^14.10.8", "tsd": "^0.13.1", "jest": "^26.4.0", "snazzy": "^8.0.0", "standard": "^14.3.4", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "@types/node": "^14.6.2", "concurrently": "^5.2.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "a52dab910cf4f46ce1a39377dd9049bb08656d2a", "tarball": "https://registry.npmjs.org/undici/-/undici-3.3.4.tgz", "fileCount": 88, "integrity": "sha512-9BAepS9jBqD9lLKO2UJ8vPZlIHNmVibkMt23c+qBQhpXpG11F6SH12ttYeKGfEiFaHyZ1wZlOIHED7TTZOCTGQ==", "signatures": [{"sig": "MEUCIAeigrIWYrE3vm7pU/WNRfB5/yaYy1qgw//sa++W4BFpAiEApS9kQPQ84MtdwbOEYIOyJijwrx/1U+KeWvAKqeMyejQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 428221, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgbvKrCRA9TVsSAnZWagAAn9MQAI+lXL2YcbjtsREytX6T\n4rYSV6YoP/6nMf8C/eNGGU3nJpiuSTzaix4snRpNUASzcT/1elUc8P3EZyna\n2c2WkBp6CDGGG8Mt4KQj8oroREdOw2vN0ZuK2G9DTF9AlWDfewTfkCCHST8K\nzraoGQQ4j/dP8AAwq9MVxN0mUV2t6gS3yzUGiq0/C3Ppk9dLn7XuYoRu6U43\n8bGzfnLbP15zDuspcHn2Ce0Rsn1W0D23qYstUw2mmOsnzDM/ZTlMl+rYLXED\nitJ4PtinNIjqTUyqWscgOuCigMFOcRSAV0vxxrmw9h9xG6jyZydRGhl8XPyw\nhzK54N0Qx2ikwrp3yZx8a0wHtzGRP5ajfSblPdNxW407LBQWiVwQ1vg3ffDc\n1zQpCr/C6zvnFRxdtuIv5BO/dTa6/sXsMqxgmzUPZHp9RPbmkn6wYDmTAeFh\n8TrrGYBIW4Lp1E6lQrUaJQZ+LYKp8IdE+7v+wy3+SIR21kremKkjruqSqRBe\n6rYUSM2Q4ahbFwafAfUIbArvE74d5AJ4nrERMBYB2UW43jqSBPBuz86pPDk5\nAcWoZvXYwRZoFqL6OKMqjTNg4EYDuh3n7fpx08gPPsbsMafybAFp0q414PG6\nASjkjixazdOTLiO+lJixyjt2QqveQmz3v5lrP5egW9aT/tryW6p4r4w949AN\nBRIX\r\n=RQoG\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.0.0-alpha.0": {"name": "undici", "version": "4.0.0-alpha.0", "devDependencies": {"tap": "^15.0.0", "tsd": "^0.14.0", "jest": "^26.4.0", "husky": "^6.0.0", "proxy": "^1.0.2", "sinon": "^10.0.0", "semver": "^7.3.5", "snazzy": "^9.0.0", "standard": "^16.0.3", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "@types/node": "^14.6.2", "docsify-cli": "^4.4.2", "concurrently": "^6.0.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "5184079af122b638c17187b5ff98c9f2d77b7455", "tarball": "https://registry.npmjs.org/undici/-/undici-4.0.0-alpha.0.tgz", "fileCount": 57, "integrity": "sha512-H2ATllblKpcEYLqR++zDeCoIwRl0VyenJP4ZveAJ2Glw2gtNQDlWaQa0vy1CJhO8Z4uYURzh0pWOdOAh44Z3Ow==", "signatures": [{"sig": "MEYCIQCSIGU5rMGqj9NYp3u2bxtA0YWX2oWdLIRwkJK6++dP4gIhAJTdDjt6nbi/dIH+BAAFPvKNYyaqFw/rBvub1Q1Iv54r", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 362025, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgc096CRA9TVsSAnZWagAAyFwP/1DxvbwoqoHIL9Rc2Rzm\nrbyIlZBAvHzNft14RuD+MvOh1C8jWnIj9cCvYhHNb/PURGVQHhr6AY61LZ47\nfxmBo5yr0P5EACIr/KeOxZyqkLg7dR++1lGxZWRZacS1DTsg0FMP2ebCiYuj\nMM+JL2VUYtO21F1lZmDDxD0i6S9BEXo8yu3qQR1J460WjfkY/BoqC05C5vWO\nASxVb7X9NoCEsWnaCR+3tcQsZyFlvoHZO3O9CQndGbvVrUxK3LLmVe/aHVWo\nwjAYCZtJgE3rykaDn8t9zrEnoXieKKDrTny5NVo7Ch505W1Wm5HoZ+5p5q1D\nPKHdYnykOdQm69yUGBcrsgD9hMvrqEMdX4J3q1dF/WhTCEQ5iQCMvib0SEaU\nx35b/HBR7aux5Pb95/i+l4Af0TpHenSRCb9nW4Bwev2Hpj5YVw/lfNLxT9E9\nUpwHA9B+Ks3gLr46n2PAMhMB1tYnG5+yMIfxxTxgigJi48hVPR5NQ31k7QUD\neDrbz15Ig2t2q3Fh40j958gFs/6cSqEq1DmZysK7bdyE0BWbPouQWKPMSY0R\ntJ6RVU9bd0Bzw0eCxBIadDE/IJHQSjxN9NKMbt5nF9AHaCGmJ9pLvmY1qt96\nAfQjcRwOEIAjsCVkOTtCwajUabyZH+QcKvnJY2mGcwQVvtSC/b4iewlchLSG\n4S3k\r\n=kOCV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.0.0-alpha.1": {"name": "undici", "version": "4.0.0-alpha.1", "devDependencies": {"tap": "^15.0.0", "tsd": "^0.14.0", "jest": "^26.4.0", "husky": "^6.0.0", "proxy": "^1.0.2", "sinon": "^10.0.0", "semver": "^7.3.5", "snazzy": "^9.0.0", "standard": "^16.0.3", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "@types/node": "^14.6.2", "docsify-cli": "^4.4.2", "concurrently": "^6.0.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "1a1760cd0903b13e3ab3962d6ea3406ff702301b", "tarball": "https://registry.npmjs.org/undici/-/undici-4.0.0-alpha.1.tgz", "fileCount": 57, "integrity": "sha512-L9b4tpoEpUFecyLcChKdxJF4MAPMSNpQIp7oxXQ+ubEQIwhJaj30774ONkGeLFUYsT8VmKl1ThHgE6oiGga0fA==", "signatures": [{"sig": "MEUCIAgffvfnpoBHVJbUTb6YcyT2SUfbAGagWDp82YpA11wsAiEAqCmCjRUZCvwVQ+5Of+Jdxt6RPOXKcwSGi4PSse4NdA0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 362259, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgdEnrCRA9TVsSAnZWagAARpsQAIMexxcdloTIRwlf8vFg\nUxD+4cHHON7olTJzP1SPsTKqVGK6/SMglCeFE4czrfUmFWC2hSlt4UA+RGYD\n3hKB4oBN5SFkmnvqHC4k2CrmDKMb3W5TK2IOHbr5uAyfZU2EkoCRxqXiQZt+\ntWnHF4p/bGeKdIYs0zfreSRSxkbOQ6WXA/u7ojuL0YX3+ua9ouyuDg3r+H1D\n2H7+KaRydSZBQSvVX9FR4wTNLusMK9c+fh9coAM37Mk9jj+WGLLpWj8zKEt+\n7gosJ/sCr1sr+wN7kNb0rBZsmaouULA+cvzb/233KPXMbTfjaOq9mtg3T+iD\nIC0Er3HDF6oz9BbcYUvhuZjz5DWn2rjbOCUkGyIOBpNwPL9/xW6MFAUy91k+\n6vAB6I18yb8lO0dtWm/8uBCqiJt1uVmpxeaBBaX/mH+FozVB9u5DaalSoIzn\nIpy5UIS0NdZv/aoC2BEShL3hIhgnosUbBdpEedZYI3/R4Gz922re4uSTSDYX\nJ4aDXg6mKyb6eyCWzZtKI98xIqVKCPG0YlLFiXUaLYbBNY9n10YvthCtINod\notcix87bcGsGd7dyCwehBTOg8C6GXXTXfHt/dthBfYjXOTX7pbfkPaLYT636\nD/s7QDZNK/AIEnwLLaPpUsf3vNF9802A579qu4/wixpfrHWbd5mOFMnKCbU2\nO+Wq\r\n=UVUY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.0.0-alpha.2": {"name": "undici", "version": "4.0.0-alpha.2", "devDependencies": {"tap": "^15.0.0", "tsd": "^0.14.0", "jest": "^26.4.0", "husky": "^6.0.0", "proxy": "^1.0.2", "sinon": "^10.0.0", "semver": "^7.3.5", "snazzy": "^9.0.0", "standard": "^16.0.3", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "@types/node": "^14.6.2", "docsify-cli": "^4.4.2", "concurrently": "^6.0.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "5aec99c938961acc1c460db3aeeeea7bfb6f3bf0", "tarball": "https://registry.npmjs.org/undici/-/undici-4.0.0-alpha.2.tgz", "fileCount": 57, "integrity": "sha512-pORlPCa6WHSHgYF3h3T5ZbGDwj8Rt+np7GQyppSZSt7Q1BMPVzp3484PcJyqCyx3HmtafMXdg7MkT+w8CnI99Q==", "signatures": [{"sig": "MEUCIHmxKp/rQtpObQ9diVLqhYsXsuhGaOIavKm6MFLbNKjtAiEA0HrXpwcwpmRYcGK/8iOvdjyRLApfPUa65g9YoSSBxE0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 362288, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgdGUGCRA9TVsSAnZWagAApGcP/Rhs0zlK2lvg57KqkNs7\nUCas8VTcYAPTmR9FGxEXP/ADtx/4EauU6mVXOUc+SZ0zO3/VZYtJDXfBrRBc\n6nUMva5ZT1Tm64V9fd9iHmF1i5GLJV9yPd+Q6yg5U8kIJ5WR+936dUCmbOhj\n6TVYZEGmYIheKvexrHoEA3+oNF48ZIOicGAJAkRaP9+GFjvFEa7rvWgV+ccU\n3a5W2t9jCqiNpC7tjnP42ckDWNdSjNZ0wu9rYerHn095jDzNm8QZ6L5dJ4xu\nSwq+cyIH4JNlMgOkrCuTXd+Dvby5/IYgs7YmTW/aOy7gHWdJqWA3HFiiGs9z\nxxVyPp3HWfOW+clQ98ImyzXCYIyop0dtLXCdFxftUdRad6GkZ2PCQMMCpw91\n2ruHUfnwkQRhSMbCxbAv+m/0QaYgaDgQ3wB9jUxn5hhTBq0SO7OWpVSQlMYK\nbX0fy0jsvlo7/LoCMMrtOpPNbbe+iGLVG3M9BVx9OVp7JvLM8N1sSe/tXWwY\ndImS9wY5iABypJArmwBZL+CYGOYdiGgLGVRzvK8IKtfCKCHw2lZfu04xUAwB\nMLP98pGYCgq5EaZToN3J8QAF5qM7nQ/vJa85H+d1Wdl+lAefBuSCP0diKv6n\nlItc3vTJrTVw7WuPo232xXw6MDchoReesYAJKQAM98wngeF4pnxuJM0xfXvb\nnK3Z\r\n=1hpp\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.0.0-alpha.3": {"name": "undici", "version": "4.0.0-alpha.3", "devDependencies": {"tap": "^15.0.0", "tsd": "^0.14.0", "jest": "^26.4.0", "husky": "^6.0.0", "proxy": "^1.0.2", "sinon": "^10.0.0", "semver": "^7.3.5", "snazzy": "^9.0.0", "standard": "^16.0.3", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "@types/node": "^14.6.2", "docsify-cli": "^4.4.2", "concurrently": "^6.0.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "0a821848d095ed84949c0e421e22b3930bcca47c", "tarball": "https://registry.npmjs.org/undici/-/undici-4.0.0-alpha.3.tgz", "fileCount": 57, "integrity": "sha512-X3bY0gxAfxxwwqc4JUtSz/xXmadVdIahgg2IgADL7bfHCKZeOD6tRUUGI7eOoEn6Q9M0gOwh84KBTW2yde/Riw==", "signatures": [{"sig": "MEQCIFDwI7NbgSqEtqWOVK6qh8YBx/pKUfEhCKDmKh9QJL3VAiBEWwehrEP6zeMDx7A6klWWkgqRXJfFyqcLtT4YHO9QTw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 362404, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgdXByCRA9TVsSAnZWagAAH9gP/RLZWbYHCLmzypUQdV22\nKpLrbOkgz+90wixLplix1DlE8gWQkVd1g0WRGP3ko4s96hPlIIxn4XrbcOx/\n8npTUALfSsUw6P050cf0MpT/gHlUhRDtEuKW4scGQt8Id1943o0RWp1yB9lt\nHusBuv/I04w97Um2B2AGeoiuAyhDl7Hj51kkg/4cClGIHQHmytf7jaQizz57\nhBqneuifhYI1XarEgnlHL/XsAYc1JK7wF1PM2HVl45E39ali+HgojIKnP1ih\nudkSM5MVwWIIwhBMG/k2BnCUFLNe9aR6PCLEftl3Z/q19oKsENmnDEOpsWKM\nXlfyGPSNi06d8OcyGqlHpv0E92JB754jh0ceB4/Ec31QPJlPZAOMcv0sfqWQ\nftUeie2p1ibRxnFllDZYFzzoz2FEJRARzy/wNxS2qaX02wTBGGakMdxnIuGQ\nd8ACVYfw5NSCUhgMGA9fmD/HhWd6gb+JFbrnzG5PtXjKTlNXlhZxhXxXZQ+I\nx5dQKA6fTCjjiWpNE0XCNipRigOq3iWRKqPPGAqtJoHQpXdrRnGIvXat0s35\nX6w/qjbJ8p+irmEejlZokoIL0l55IJeLtoSn+aQh2AOqhBvPaVE7iJZwtYWw\nLNv9EBpSXhXhWhDNHqBJkROSwpCy2ZFBROwUyd3vNnZk6vOPVwfL0mF2PWzs\nvY3N\r\n=vVtW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.0.0-alpha.4": {"name": "undici", "version": "4.0.0-alpha.4", "devDependencies": {"tap": "^15.0.0", "tsd": "^0.14.0", "jest": "^26.4.0", "husky": "^6.0.0", "proxy": "^1.0.2", "sinon": "^10.0.0", "semver": "^7.3.5", "snazzy": "^9.0.0", "standard": "^16.0.3", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "@types/node": "^14.14.39", "docsify-cli": "^4.4.2", "concurrently": "^6.0.2", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "564442eeaa22e69ae085e097d55a2bcadb53eb7a", "tarball": "https://registry.npmjs.org/undici/-/undici-4.0.0-alpha.4.tgz", "fileCount": 57, "integrity": "sha512-6dgCWrNiwYtw33Sv5jkfglFdAW84K5aOo+njsZnHciDito024RsWcvdw6lg9hIJ8VeObHiUvlHLhvp5kK0um6A==", "signatures": [{"sig": "MEYCIQDilNiHtt+3izhWBu/tnwS4DAkEGV0bU+0QacVMBEypogIhANDLUoRCFrNwkItCs95fJjSjuo+Y+NM395S0TSDvzls7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 363295, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgebm8CRA9TVsSAnZWagAApUcP/0Esh77fUiQTpJRyDqRo\nmMT6D82MUBWRbY4to013HUXBoHC26K0zkDyxZ/Ch4N7q1XxBNkV6pFHqt5iy\nzP1U7CoNGI99VJMh0cIGY9STzO4Ed0i/R4xxomwfyNeqjDbCJpa7zAV7RilM\ngg1sW+S53BmStNOv8QA6xPbIPycod+4NfvJ9LQRqx7+NsKiEYk3+zxQsVGKE\ncOduuIpavJnGaI9zi5CvgKD9lX77vtm5oeRiqBBuZpR7JtEHHC0w+JhbgjQs\nsQ4TnmwgZVgLSa6GQuBOu9o3MQZO9uIywyb691w+UM0KuLEmTf7H0+jH7AGq\n9IVG41mkURwymyEIDWXPBfHqoFv/5xoFgckTXy+DsXvo4EKcVANDzqZt1H5O\nvrmj7vh96rU0ZA/wA0OMplUcaQZQajwoIHEJG22anqWqv7lAhCBWPtJAx6K8\njXgj3lJ1OJiqN/1/DIeUeY1Sck+6crcTAj9fMODem9s5MppOSKViR5FrvL4A\n9g731k0D330sWDbw2rHa0RQ5b33ifjdbrmHVnH4pzQ/lJlHc5Fw6ri/S6rr2\np2MeLFLPEC4aBV/f9u2uaLZ0Cadx0NxEfW8tcYPEYqqZy/Up5hx/fujHYo2K\nKi6WOodvdA1cdNeghUDP6l/Rh12JiWSDCT9vVDwSh2orZIuSJm0jwjKxAWqy\nvW+j\r\n=hSYp\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "3.3.5": {"name": "undici", "version": "3.3.5", "devDependencies": {"tap": "^15.0.0", "tsd": "^0.13.1", "jest": "^26.4.0", "snazzy": "^8.0.0", "standard": "^14.3.4", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "@types/node": "^14.6.2", "concurrently": "^5.2.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "def08ab52286f6732305493e0421a3abf10b0867", "tarball": "https://registry.npmjs.org/undici/-/undici-3.3.5.tgz", "fileCount": 87, "integrity": "sha512-d8okKB9SxyOhK5qtpuDjJUHY2/pm9geMSMm2ZelJ4DuYyv5BGayggazlCn0BIKrYrwKQtwmUsXDbS+s05ff19w==", "signatures": [{"sig": "MEYCIQDuoozbf8lV1b8/4rxLXiyjX2f9llis+TNxlHL+Xx7JRwIhAKbNG199qV82F90PlMsmLr+eJO7aqfEEDZ0lHq9NXpNc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 424370, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgf9/wCRA9TVsSAnZWagAAU2IP/3OgbaSy1j3FuxhD07N8\ng+KiyZ/BQylSRz+8FCHAHqF+sq+QnBfbleP7pWs2yBlynxvImbg/EgO/wLOD\nyyUkNPigVBFSLWtBvl/7wvUD0BIQJThFMH3vRaEUyC1sHOXxy2IDr3LMhSPV\n8rVW6yCJIPqYH39H3ywndD+Or79A3bWSHge7Osd+sA6d21PNZVba6GtAKByM\nlTpU2KZrU/TR5+fRBnnZN+slOXSV7ZR0iXyNOa55nvwYNDdZrO4cXJeqeKfK\nc7MdHuSGnUOESexivTTZKvN6xzu5vHXpCaIPspi59hj+8DGGtX2SPa1/a4I1\nvQmPHqwRNgEPNz9xG8AELDLlkCsn9/gLMmkK5F7F6d+Kpw34a5cUaWVvr0dN\nr8lVr1wWyEZS/Wfc2hIEn/sBfCqh0zG7iT0JAfB1Rio9Q1TD00MTCwTqzGc8\nFX4lEM5Vj74Jox1KJn5Plm6I6wl7vx9Bvi0LSVu267U1NpMRXnfRpdI9OOGV\nUyb443xb3+wMg5MUHKfIgBVOwJtyE/8QcNwfQYySzgh2BUz+mV3CNtNWLX5w\nUNJfV7TXMqTjHfBjroEYbPpJHLYqt2K4utsja47XM2k1KPXiOqCZdsVbNEc+\nYyxOaGuEZg/QxANQPbIFkls85Fd9iLHY4QqOM4dw2tUMWc8k+StAKe69WJXt\n8YK4\r\n=orS3\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.3.6": {"name": "undici", "version": "3.3.6", "devDependencies": {"tap": "^15.0.0", "tsd": "^0.13.1", "jest": "^26.4.0", "snazzy": "^8.0.0", "standard": "^14.3.4", "benchmark": "^2.1.4", "https-pem": "^2.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "@types/node": "^14.6.2", "concurrently": "^5.2.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^6.0.1"}, "dist": {"shasum": "06d3b97b7eeff46bce6f8a71079c09f64dd59dc1", "tarball": "https://registry.npmjs.org/undici/-/undici-3.3.6.tgz", "fileCount": 87, "integrity": "sha512-/j3YTZ5AobMB4ZrTY72mzM54uFUX32v0R/JRW9G2vOyF1uSKYAx+WT8dMsAcRS13TOFISv094TxIyWYk+WEPsA==", "signatures": [{"sig": "MEQCIB1q2Y5YSmy/uKHcTzkiki4fK4zrxaKOFH82eFuuFPhxAiA5cAuFJF0jYCKxMXyHMmKkTFPnu1UMEVSCR62Mel76LA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 424973, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJghC6YCRA9TVsSAnZWagAAXm4P/21uue/ZVdAZfkkB2+v2\n7BXnMoQls4Vf8NrfoUsMdHgF5pVtnKWpnNJqGSqq62ryWqqK+iVpL3Q/mMaG\nnCGRad8/27K8vGoAKcjyTtwxrYreLe6XARnS3QOO2WH1KvA+HtT1/nI0d0aS\nrh/l/YiLOhrFuDArH8gzljLdJUgI3t7aJUzfLNznVXVhOKo9/jl2KeG1/gch\nKiGcDowiCMmWvynNhjwW1M9PsouQb4ycW7ELkOHEd0DghifEg4n5OWsT5UNR\nMB6WUFbdz5D603I9ArRbKhpjUHy4GTV6wbwlEL6fivPgbTfnsG4oQTE779Hj\nomq3xArJKs/GbH18ftPenll4VjNGHuD9rF/sv57OKAca3P9sgpSoGGZtrZAM\n1NukmtoAY1o2/LwhbAhqszVE09IW5YXv1hPZ93RLsRjpZYhQ/xyDkMZZOmHX\nQHSdpmyIAf5xgAEyRc+DLt45Qc0zPwFypT+Z+7LKOUmHz8AgQHdukTPvurF2\nLecHbeejpe6jxg+IOXUtg7uY4SVM29XnCfBiyhVgaXdHL76KS3Yobf7MpGJB\nXJF9DRgi7EANvNabViovsi4fRR/hu3pTONFI2eh6MIaMCb50hEm5pmcvUDNM\n0AbsA58XI1ghxzKQECdG0DOJXQcgpAuDOxeOybJWD/f96Toy7mSkNYg5GrZD\nUAMl\r\n=wRq4\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.0.0-alpha.5": {"name": "undici", "version": "4.0.0-alpha.5", "devDependencies": {"tap": "^15.0.0", "tsd": "^0.14.0", "jest": "^26.4.0", "husky": "^6.0.0", "proxy": "^1.0.2", "sinon": "^10.0.0", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^5.3.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "@types/node": "^14.14.39", "docsify-cli": "^4.4.2", "concurrently": "^6.0.2", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "c2839a0c77e809a0b8040a589a0a3b4db7451f13", "tarball": "https://registry.npmjs.org/undici/-/undici-4.0.0-alpha.5.tgz", "fileCount": 59, "integrity": "sha512-5KEvUmg7UuWfzPVwkdT1lzQs6SDeF/UYUVr6psoX5RNamy5v3rusZVH7Lb+B8cpwR216sdAaCXlbGgdh7jFIHQ==", "signatures": [{"sig": "MEUCIQDyasmJC2jZYCokBgmTWD1F9H0Cyct6pZ9i6wcln4mXRwIgRfaXm2Zt/A58T7rRaa4SRrWYGKXUx99tjqsFNaih5F4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 411556, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgiD64CRA9TVsSAnZWagAARZUP/iO5zUZFrSQsYf6x6cnQ\n/23VcGBPm/WYReVmDtTeMOzc5vv3Ipp19KzvoHNLf/LizBlEhTYctlLLvpXq\nyutZS4Tc6Iohi/9cDmSFbj1JmWdcdT8FItIz9W8xjP3DYyLzfm/mcFt1WAxS\nXs2kq1vL1h0kGWyjuCCeS/bSDtAjk4GtnRsg0j2vNjb3rPRTToGP/AHUf5Tt\nZ2Zzmv07+lkZFPWbkSkVMPQQW0/xJwGeydR+U/hi9CSGbMM4EKuytvzuTf79\nb2A8r77tMUiEGMRgB9K6EiGaV4lj+15wxTq5z9630M5dHAlaBYRWSluOD0I3\nvlGTlaoQ5aIbJYy1Is9eexQcB+h+nTXCIL43J4AFZEkZZs4oGwOuQQQ4aLQd\nVcfJKuRl8uyFQdUt7GnEn3ji0kgCz+3SuUYyxOQCFZcyJYLd/24Uzx8wh1y0\nx927Hjx/rdPHEGAKfaKkWq4dcYm+jW+3Ub87I9JHnjyHI4/iM9aVMxW3C3U0\nfyVL+X93YqzHvG7CxH315nOEx0n+IieXcHvgLJdbtSaSJjzqpJrYXg8g+N4y\nP10rRSPGnchPTkaOEYWKUTlkeHltR3b+mM8LHqncOv0i+4J/5Shd6MLGqcCT\nN0NxfXFykaxZf+Uc6yaQr/YXf92i3fuDBQszNQ+gkAxGT6rQjHkZ+v2e3Sc4\n9QNv\r\n=Vp17\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.0.0-rc.1": {"name": "undici", "version": "4.0.0-rc.1", "devDependencies": {"tap": "^15.0.0", "tsd": "^0.14.0", "jest": "^26.4.0", "husky": "^6.0.0", "proxy": "^1.0.2", "sinon": "^10.0.0", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^5.3.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "@types/node": "^14.14.39", "docsify-cli": "^4.4.2", "concurrently": "^6.0.2", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "4cccc68deae62221730a545b722f897fa962813d", "tarball": "https://registry.npmjs.org/undici/-/undici-4.0.0-rc.1.tgz", "fileCount": 60, "integrity": "sha512-Py8rZu2+o8TRBge6N1GHK2lQ6yg1Xil3bIoXMUAjdzV7oAxu+NDcxcDGQioTxu98W+vdd8oLuQF2m1dwfAA8Tw==", "signatures": [{"sig": "MEUCIBQ73+99jD+sxf8v/tqxUNevZHnVKoGn9iWWT5cUzCEFAiEA9TDJFRsO2c07CVVZIYTMs3rCqmBq34EBhXEIQ+Uu1jk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 411871, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgiY1tCRA9TVsSAnZWagAApQYP/1nHyn/DrC1wLvnJk11l\nu4mImnS1yL17WV5KRpd8ekDH05id6mNLhOHpSVI/7BzZsQKESzIxi3WHbUza\nTfOWKrlzSVF85xQhFmS4u9wKhsdOxbdFWp+zBFf699Af996j/aMoxSXV+oz3\nTsBApogHPzEh3FTQ/nRnckTmrHOYVudKK8gDqRae5J0IVyr2jp9gPwtbfyYU\nv8mAoZ+QmGN3n4HTgeKVGUPxNfAufRLh5ie9A4GChP7sKU7zeVzODFC1MxnJ\nun+UUaNFt9t5NL2iSBBOD1GTLmBOIEOlWkdnQP2TrPkRHmWwevZYHKY/wdtq\nOOg56/hwY5nK8VbuLUdnc8b73GFpkQRQTv2NntgGhILcQzGYCxWve/tZHj/z\nf18MaACj0hmJXJJQ2iNHf+TtZdyXkVY2P+/dPAoASoUIc4rf+aJ5Kn4WUexp\n540oQm9pVxYTXwKPuxqiwwdvFPel9Z79jZavLm4f6CNP9DVePVL3kKPDCtdE\nMJWchcp2E6DncEP/21s8iN6XOPrsy8n6j77R5BMAnKFELTYkKVlykXIcBabN\nvzpQoxoDSmz1aSLSWRBOB7s0UHiEIE2gqEYBRD4Jie/kbcBju7+NuCyiLBD5\nf8ApkFxQP62Ag8NzNBmL4O8DEY/N6SFTbAl5Gpf/j9A5HyKAxEJnCxbci8cA\nm/m6\r\n=xk06\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.0.0-rc.2": {"name": "undici", "version": "4.0.0-rc.2", "devDependencies": {"tap": "^15.0.0", "tsd": "^0.14.0", "jest": "^26.4.0", "husky": "^6.0.0", "proxy": "^1.0.2", "sinon": "^10.0.0", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^5.3.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "@types/node": "^14.14.39", "docsify-cli": "^4.4.2", "concurrently": "^6.0.2", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "50d363f7f9c32fd39cebee4621e520d6174acd9a", "tarball": "https://registry.npmjs.org/undici/-/undici-4.0.0-rc.2.tgz", "fileCount": 60, "integrity": "sha512-f5mo74GoVOxEMbGvf55VavS95Z8TW3NH5ItbYCohnDZdDT9G+K0hiaRK1IBrcZi5wnbvaA7oWtEPRxofwhg7hg==", "signatures": [{"sig": "MEYCIQCcf38VvSDaYOE3UzyK1AsSyXiY/zrFMUVDRFG3o34O5AIhAJ3V8GlkJGsZzQyhgCOJWkKqyCzyIlGiIuda46gYUExl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 411153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkAtKCRA9TVsSAnZWagAAxm4QAIh+IYRe7LZ00tpd/iOu\nJHjOn6+s6BcAh1scEUhPfQx4BvSIB7YVmGLYVVxYspr8R9PPUDkmOoY9w0Yf\nb7xEHZjEJGFemSxn6QCDrYS5O1+c0scQgCd5NYQPN57j4YpXMOlKVpWMu53z\nDYK8+4/QwOKFF2H8+XGcLZYa+lLYWa3A6IuVN6TELDGfiSgDS32z7a38Arm5\n6Z8GhBmPRgg4I8+InX9mFqlViqmpywXRjCSjR/oglQYA8ryTZql8/rt3DpCT\n3xeKDzqcE5KgciI4wmSxHufGhl2qN5BoyXZgVC3jL5ySTEQ0/2GnnYj0FcRq\n6cOi944ZrkIULafaVX2X28fo0TL0GjyzcyFs9eDECD/GsRgjo0qG0DO3iiKU\np7JT3UDC9PflPFjpxEHEMfPfTe6xQpGLv3QlYDHdvA5M4pwNP0dWzK41+nPj\nnuALh6hn+sYJHA1BhpA2Ya7/t7qPkmU9cTo5BHa0g5UEBtadNF8gOl5m+OWS\n49Y79TNaRh1ug2Ad+CsoILX8VPGYHuQ6DcTqCcrOtW1VIpzOoQlzFUh50mDP\nJ7Cfd2i5q0QVsS74m2BPOg5fUQIto5wI8Q2rq/2DgcRYoeiOar7Fb7DJNCo7\n56eAXsgEN8eTPIQGNFBnIl2mjl+eVWCxiUCdvtSCYXcIA6tOBpDbqHhjAJQn\nykeF\r\n=JJl8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.0.0-rc.3": {"name": "undici", "version": "4.0.0-rc.3", "devDependencies": {"tap": "^15.0.0", "tsd": "^0.14.0", "jest": "^26.4.0", "husky": "^6.0.0", "proxy": "^1.0.2", "sinon": "^10.0.0", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^5.3.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.0.1", "@types/node": "^14.14.39", "docsify-cli": "^4.4.2", "concurrently": "^6.0.2", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "567eca6daf8e5d163c318660f56311194455d2e8", "tarball": "https://registry.npmjs.org/undici/-/undici-4.0.0-rc.3.tgz", "fileCount": 60, "integrity": "sha512-pMRKr5mOTwmgmQPI2tVbcM1FHnKc5hiJP8lC1v3ks+BFd10oTxcakTnEqxbxspl+ZpD+5kc3PoAp5uw3p/M3Ng==", "signatures": [{"sig": "MEUCIBQUeVojlFb1YMWc2uJ2+TZPYVzf70V+9U2YjeRcIaLrAiEAuLuCTQjalwYY2VEnFzRcyZforrNUUkagdDfwu5NZdLI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 411935, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkl+ACRA9TVsSAnZWagAAzGAP/jDN+HfD9bz1727isyzX\nuH54fbPjZ/idhYsE1BibwH0hVCvQjjqZSortCVP1z1Kbd7meO22jBsJmzt7u\nPyVNQV461zn8jlrkOMx9LKu8o2TO8wQ9hnNR+Pl113czQRUISg1Q5lpSIE5o\n8mkcIiIBEd0ZI3Ujcpf4TVyEU3U+UkYpfVmMiSzpiGP91YTtnZTyaS7lL4x5\nAOGN8qgYF3ouqJFxap1AgcytBidQjpqQCGr1vlgk6GXtZGjhIlXDSe/mL03E\nGxfiVjVJLFf8FPHg3qZB4XXYSmdDt5iu3Dd1z0naF70BHke5AIwQ/olmQgTJ\nwTQvmp7cRx4sJz+bEhysHy1uXUpiMluV7S9c8pIiIkBBgsQtaz6X4RmOCmHY\n7iyaOEsMoH6UT5LtcsbYeNDa4XB8eKzq/Dc0ig8EkyWwkdH6TsUbpb7qQKyG\nRuQl0iDUYUo1RNLVgTcSE7hucXYJugXPR93GaWbCC2VSn4p17DTaWD/TQvV3\np338ugYrAl8nQbaN3exUKcooiEiQpsif7oKEU9M0ZOIn3MJNeCmcz9IRsmJ0\ntTDpwBIKy2eYbBwbmpisWCgePIG+7KuqDFjseBo+Ob01OfacOF6leaM5qOFD\niYOnishk4fZoju7Mrh/giWxR/uns1+VE1xxhIdhvfXdtwIQJWNFdTzAThZsf\nObWp\r\n=6ge3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.0.0-rc.4": {"name": "undici", "version": "4.0.0-rc.4", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.14.0", "jest": "^26.6.3", "husky": "^6.0.0", "proxy": "^1.0.2", "sinon": "^10.0.0", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^5.3.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^15.0.2", "docsify-cli": "^4.4.3", "concurrently": "^6.1.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "c7dff7dc24a00e288635f7e3daf4aa0b47cf6ca9", "tarball": "https://registry.npmjs.org/undici/-/undici-4.0.0-rc.4.tgz", "fileCount": 63, "integrity": "sha512-vYr2ySnTM9RMW0QhKUims0/60ErroZZkC1E4sAzQXzR8j0DIXvKCWdI2FV7oC7JT4lNrB7sbDxeqIzElslMIgw==", "signatures": [{"sig": "MEYCIQCfzd//zIQCEPFgBUjnYmyipVLLyk3zYLcYAzaAC4kyjgIhAKc+3vJIw3yJnz/5Ayv8Flq0ibS8gDLM9VNWAkFlRiT9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 417173, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgm4LOCRA9TVsSAnZWagAAn5wP/R5PXl/ELXYww4iCf+f7\nnjvTFIdAbm66jma7WG7MbvweONt+Wl65yG2XSW3VgBKK4XR0bxIckckI1Tuy\nmPoiZIP669hd9Z5EfBdF9GLrHFCl9CnapqCkAS6NXZtIovh52RHER+EHvdna\nK7pF1+/QhYgaG4Hv6NmeQUtOcGo3jkeO52EfL+y/hC8owVhmK0cG0tYB7XHQ\ntmG7CY7qt3a06Nz6bsKogW+mpBwQxb+We9pXdv/nBvXUW/WAPXNJtmAQtKyV\n/ql5KMo7mf1V6UHGhRH4ZMg+TDqgh9+4Ios+2dtMfS6cBizg8F71r3uzn5RS\nrqrHvJHUUQxp28LJqOewSCv5J2VbVaft4FoVW/FnDNYPVLIdY1v2ANO53WWL\nlepTiTIE+r5cyuYxIVZ3Zwv/X0U7dWmGGZSVdV6++w6VI9XQAd7kcGy7xx5H\n5nw/D4QBGnwCTEtE1V7Vozr4jBli6mO4BvY4iS1S2HcfZwMnUdW6rdZfHVnt\n+kTqklQ1wpwVxAk1xjlV2aS5FY3cZrCRPJcrmdVvh/V6CARG+eeeeLvetVMs\noVlFxjvM4P35qnG+Np9goyVDi4uQqECjQxv9olh+kYbNN831mO5yYRsfQLqX\nT6oBiR0MGOBJA1QyhIZ5rLafZxbdW+iYLdNxiVeb7a+JK8fFXICtQP1PUs44\n/iQd\r\n=yAXC\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.0.0-rc.5": {"name": "undici", "version": "4.0.0-rc.5", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.14.0", "jest": "^26.6.3", "husky": "^6.0.0", "proxy": "^1.0.2", "sinon": "^10.0.0", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^5.3.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^15.0.2", "docsify-cli": "^4.4.3", "concurrently": "^6.1.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "b81569e57eda0a74c80e6817ca3956116ba31fc8", "tarball": "https://registry.npmjs.org/undici/-/undici-4.0.0-rc.5.tgz", "fileCount": 64, "integrity": "sha512-zVLGa6lB43cAX08tPJQj0Sg+KNaQ936HzWMLCsHoFgIvwIy8bBNXi16JTHcouMy+XnWNpyL1VMOvZEwSq5XuGw==", "signatures": [{"sig": "MEUCIQCSUxz9bbH1ZKzVWSrTDzZP91oWCk/wc/GhY1p/RTo0sgIgd+bznl2W8MGbo0BMutEoSvgIiYdCEBEbp0VEDAa13BY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 418989, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgtMduCRA9TVsSAnZWagAAfTUP/192U3/gz7l8kxlGolUc\ncAvppH1uep/LDIBCSon0TXpXz73OvdV5dgalaJDIdwXMBltroPSgFKAHfJxw\n35jqoVqvH8bL+ujeiO2WBpxEPAqX8OdD+9Q7voDUMwTh+EWeJ12KFwHMIH7/\n5HiOPbBNNTgL85aU6o/P333N4vkF1l5at+1R6HXDxhmpt6btlnFNw7PsdtHV\nTm/LqueieI77/6rIuch5DD5600AKxaSovKGuo3KNBATfqB8vYvwf9APJ3mno\nQ1XfM8SrsfAkFUlNjlgo+bu/te2uXrs73IezDvBqiO8bXxgcqaRfI9Xb6Mvg\nfUZrBMYRYABmWiZugDbb5G5OhMtMEiMfelXRoXkkGzFfjdKf7RU8s23drexm\nB2euEK4ATPyL9Mx9ITYxcDMIeNgPFd9kh9epmofKVmcFfvTkijQlZz9aBA2n\ni3TVqrnMVTE7hM1YPeZVghcNFJf3imlP9xAwjWb9BznRAL6NXvwXAD62/vVJ\nln4NuvarWVoW1C+VQUQvPMhh9EltwlBANoBEjlfItYD2fSVN1tVelK75NCBV\nXee6QrnQrTlC989movsbjy0Uhcaq/nYTUzOlMFdDz343Qj9ClmZP1x8WHPfL\nG0i+iNiQXznJzRF9hAbCFTMO4jEu5S6Hl8iVuz8JZ7uYjKhxFypaEMsLW/AL\nk2Dk\r\n=MioE\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.0.0-rc.6": {"name": "undici", "version": "4.0.0-rc.6", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.14.0", "jest": "^26.6.3", "husky": "^6.0.0", "proxy": "^1.0.2", "sinon": "^10.0.0", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^5.3.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^15.0.2", "docsify-cli": "^4.4.3", "concurrently": "^6.1.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "91519983264c9f5139c7a1d386025aedff1da570", "tarball": "https://registry.npmjs.org/undici/-/undici-4.0.0-rc.6.tgz", "fileCount": 64, "integrity": "sha512-rlYbL0AnFGuojvoB4zTa0+vxSJLK/S4PPVWokRROH6AV4suvdK89jqGXkh3nfcG8b1Dls+Mso3BGSBp2YPRxXQ==", "signatures": [{"sig": "MEYCIQC3n5wvDqr2QHtnKrLHrHO2yvfpyRILVF3yA7R5yvvPogIhANbeuxSREqktBiGSxTFsAKvlHuMQeYmS3zkH3ZjvAZ/+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 419224, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgvyCBCRA9TVsSAnZWagAAh+EP/1fEd15aQ2A/X0Kd2oIa\nsaAvydzrtUmQ2jxn/ioJUXFgUpuNWWa7IBBoJw6M/S27NEx9Klz5FWPwWJHy\nkTGA7bB1/Hpb+Ruz5u10awhftZh4suTcru327cfHFEV5CT2BXoSbMPV22VQ8\nF6cT3iMZDtCs+P1gjcT3zi9iWwD/dJ98KatDuaTmJb4mwrtPeaKNkoOYu+BE\nB7Uwr6ZRG3ga7i47XjsIDLWzaPdMXWmlfg4seq2/fWiVim+o0r/5fZtAhIRx\nQTjiph5kKm63jA7mjf7Ye35NfUecPK5hyJL+AaPN8nu9wQUn0IlUqHTO9pLO\nFLPizyW9qO4r0+ONsy4mgRtDNsW5k/6uH1O4hCM70YXJ5JYTcf5BG5tl/LjY\nDHBX51VVGxJwuy+VjlGc2xni1SlHeqo0p6eTuP4iy6ykOuv/qs34y/nTPulk\ngboaMbYyjJ5749H0pGMuJW5hydqNNpYOevKuqySYNAepQkYG1zyd7sxKah41\nyU8O6OV+lzmCKyFWfX2mJ5ShqisLWZD1IGJVROWFAfJ00sfw7EutjtaU8GCI\nUwlzLcwd+MNVaxSdMvaS/QrSN4YOGsbkTWsMxyeFANGUHUmn+Tl4Gbaj8Tg0\nrclykhGZE52nDMGUeZtzROTZ2kodqVaLfHj9lgBNM5fUXyKRW/o9ZD3Txwgn\n4Frg\r\n=/XE3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.0.0-rc.7": {"name": "undici", "version": "4.0.0-rc.7", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "jest": "^26.6.3", "husky": "^6.0.0", "proxy": "^1.0.2", "sinon": "^10.0.0", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^5.3.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^15.0.2", "docsify-cli": "^4.4.3", "concurrently": "^6.1.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "81759adc2a83c49bfeedd56a8c9695f10e1fb83f", "tarball": "https://registry.npmjs.org/undici/-/undici-4.0.0-rc.7.tgz", "fileCount": 64, "integrity": "sha512-8vhF9REAH/O+eGh1K942bTznr47JOZHlHMTh9/5XiJvcBk3GqV2qzeRPxwDfAeHNWjm1BZ4EsSgAotHV2EOxeA==", "signatures": [{"sig": "MEQCIExyKOMOvVKYg8RlMfbp3UnO1XlTAuBQAl5TThAjsdGsAiAp96GEyvHK7luXhYmYpvAKNP27CMWtUcpn84HaCH7Wjg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 419921, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgvyEoCRA9TVsSAnZWagAAsrcP/3U5CjjRjiREXwUe3BF9\nZLJcs+h5SbsN/JFomRplgSs89rKFOIeUsiQ75V9ggg5ulyLpXulfiu9LFfuO\n59XNjlk4NdghchBZWZXeqvZe5W+b4f2dirPXuJU1mcOYncLa/MNqqmp3cC1v\n37L3WcWlBM3LXI9C7RrxUTxvOK+TvKBBmsgQ7IF7z9+w7knNpvCC0AgCgq2A\n8fLfRh0tzWjbvhOi3K8Qz+Lnh+h8I6lIH2GBbo0eUwaLESII5m39PRZEmtfF\n7hKnpan7DVyqi2sIFeQqKSW9fqVouZsMb/q1/o+gym0O8XgMBUKKV4JUSHZl\nEkHwjoiIaVX6F4GdmXOv3fPLFumi0fM7LDbDQ/MC6ZRQy7oHkFSgp4rCPU/4\nwIlr81ct0YFXmAd8GacLz3gZv3lNmkvHL/HsWmADh1dYKWB+SW68YmI00eZu\nGxvcMrQyrKmymWmeQvd+G4B0WocLnJr74MmL43C8x1yGHt/gnkoWeBxoHd6v\n3jclsV2JO60m5czXHpWf28HG9RUPUljVqhbYrzsUw5fkLf0zzFZv/Ds1LAGI\n6SX7+HK6rgZ2xDUgFjN4AGDBccydR7c4C4MYn/FnIPhNm62FJoYz43cc4BRF\nrBLWYsFpVcWAsP7wuyZ2Tyeu4RYTExKhc2ghAwvCZBst9RvRUBpXvCuAXWLM\nUovX\r\n=Zfn9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.0.0-rc.8": {"name": "undici", "version": "4.0.0-rc.8", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "jest": "^26.6.3", "husky": "^6.0.0", "proxy": "^1.0.2", "sinon": "^10.0.0", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^5.3.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^15.0.2", "docsify-cli": "^4.4.3", "concurrently": "^6.1.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "c244e2d6bdfc2b8b747717d19f40be9168f060b5", "tarball": "https://registry.npmjs.org/undici/-/undici-4.0.0-rc.8.tgz", "fileCount": 64, "integrity": "sha512-Zbt7WG99Dna4RpBD0m+hJOlYc9UDGO2Lc7a7RMl1Xr8fV2VyzDMm8Hd96hhbAjq9LEaaiNjwFqfCLbK6UjMLUw==", "signatures": [{"sig": "MEUCIQDqe7a/MY7N0aTgvJaRBBv+l2ok249wiQstVYsJdQvAtAIgFDGKyd6Y5t00BADP5ItYnT8dqR2o05Cf2UMaDMmfmu0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 420415, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgyaUCCRA9TVsSAnZWagAAZC4P/iqCV/c4XPWtbgG2gg1C\nxLLShbspK80hg9Xzcag/86TI/CtLbUZugm4HFDST5hzDjibh05hW33uc82wV\nFap1ZxAiAmFo45WZ1jVeoDj9j+hnK2xtCJd3F5vkv66oJsg8X9+wDpYLDSjh\nLDxo7ypVLLFL2kDSn3XqkFXyQr3sgsN9pCVdEFmOP9YCxPsM/0p74uIWwnzD\ndoN0DDlc2/ZhJ8aHpg137VjoShyxeKX2tpdeybP8DLh528MOZeUgHJeoeS/3\nPE6DiwVFfDonpOeLJCekG7/BiAl6zKu5xEHgX4JGx9LllEBt+APhqri67P/c\n6ZWeZ0OrFq/k6WatG4GVyDVjq58YS8RWOWKg/qX32LXPLktJ0BrTLYFsNy2+\niC6C97ppjFpjpQTU0X8dnG7PG1w7IOJizsHFxc5goXdbmJp+FedPYmDNHvSk\nRGR+c5r/yK92FVp0VjJFZ5Gk5xiq3/0iQiH8aoD3YLJpHlNP/AysSF3aBsMh\n5cDjVijvGVSxV8V/pnnt5y+D4iyHMmG+1PUqmqzniXoM5qu8/xK0LVLEDkjJ\n9Du5tN0FshokaF0H21TDCS+8lj9D2ln/YFO8i48Qak2mwKLzRGcQE0Ptmyaa\nN+Qq3M0FkyRQnLp37Y6iVL6sgkQxO7AUHldRANg2AV8RaIwjkQPvWcvKv6zO\nUDHe\r\n=XQUA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.0.0": {"name": "undici", "version": "4.0.0", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "jest": "^26.6.3", "husky": "^6.0.0", "proxy": "^1.0.2", "sinon": "^10.0.0", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^5.3.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^15.0.2", "docsify-cli": "^4.4.3", "concurrently": "^6.1.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "0794fe33ac06af978a93e62749a17012d5386643", "tarball": "https://registry.npmjs.org/undici/-/undici-4.0.0.tgz", "fileCount": 64, "integrity": "sha512-bVXZKTcQPECj2yYWgVWk1tDw3T24Bzfbp5H5bivcVCpklpqoGvJ842mCA54bExFzuCZcIPrwsVJXWt215/qRIg==", "signatures": [{"sig": "MEQCIF3Iu1eUxbc2s+G+bIm7JBtxJExhSI24jw8i6KLfQucgAiB7Zi55b9aqcRZvmJMBNJaPRNg+u7GsBznDJxChYz6lpg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 420410, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgybXkCRA9TVsSAnZWagAAXtsP/3KhO3H62qyOB4faqldw\nrGWmJHZNT1A6cs0LYyQniet9EBGERZCMe5pD1BqtCanaJxBOwitBYiY5T4Kd\nFutnZ2lkboNRINCnYTYW9Y/FFG2zSIzP2ESRiBA9H/bAC7oZiZ6garuj9jaY\n3yA1n4WZPHemUnCR/RXjuTT0YWaharM+CtR5zuK+zHkOP8P8+8OVFxUmmcMM\nScGcOBrA7l4w9HQJCsQ0A8TAST3rnu/pqbqDF3mwpkvxNI1SqXoKft7CAqxr\nXhWrPnMYbA2IJ2z6BPdeW+MhusXH5ujcCwWpo/msLhfxq1HNHAysNsUsPaID\nPEZcUoFRPjxyj9s8VJn/hYmOec0azbNL6z38Iq5mrSCtvvuv9be12+reQnbj\na6mHp0Q7tan+uG5QryY/ffVJ1KZua9ZG8z0PV0pIoAkF5xC3knlAu8VuPq0y\n+hx8d4xq139fP3fDHPYY5Pz6xM9DhxXTh8pTeYiIm5vlaHSCM6B1WS41o4zT\nZcUBsoCrRaqPmf48yj7XOhqITyt3r5Ten4vrNtDK8g684j8EVJyJegoUsOUw\np+PtrmSJX0fn3zmPLXUzndDN37ob6jg+12pb8yKTRKcgcTJ5Ceq/3ABwKAbG\nC5d8H8itoUouWLDeucwB4pikVRDQlMy1QubILKo6UIBuravubripw/eGK/4U\naiK6\r\n=cc8S\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.1.0": {"name": "undici", "version": "4.1.0", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "jest": "^27.0.5", "husky": "^6.0.0", "proxy": "^1.0.2", "sinon": "^11.1.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^5.3.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^15.0.2", "docsify-cli": "^4.4.2", "concurrently": "^6.1.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "1e1f83e5064c67eddd53707d7819ccfad6c208a4", "tarball": "https://registry.npmjs.org/undici/-/undici-4.1.0.tgz", "fileCount": 64, "integrity": "sha512-Y1e/pSDLtNT4nXSQc4C7eiqoiV7pq8QmsvD7/6cbUuUER0MewR3xwBswYiHYWqVI4FcUptjHixrdqW4xrmo4uA==", "signatures": [{"sig": "MEYCIQDObzRPyHftKh5GY4VpwRuJw5rIzNLhg4IjFPIR/IsOVgIhANph1/9p9/kGhhx52JoJmHakfNttSni5ecANnocJfKSB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 426806, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg21/tCRA9TVsSAnZWagAAgXwP/iGbrxuncGvFK50SV2rr\nAAktXNfXUwZMG6uqv4mDzpYMeNilCAYuT3hqS0u37BP0APwq0seqzlZ2Ijgc\nNsb/Rd/IruYnYQCyk3vyHcGa3OuOA5YP88DiMGP7rom6BIxL/i4xUHYn9vec\nsV2gZKdN3lQCOiKu9t96YzuUgTE+DaaPyza5EOIL9hHiRDGyop1mUyHzNKqa\nLBVVnr8out24cKYb+7SxbviIZq2auTEc/Lr4gbCggg10J+PFeWlAeewatkPF\nv5oGVXpZUN/Qpw+EuEnzG3dBT1gkBsv5p82+9DbeAN7+C1hcyqtrNg6amSUE\ncSqt47OieSzlG6ELAC8E15TjDmywhf5HUgRUKMyGSAjRD9eUrwEYI1A+MO6y\n4Oalbe/EhJpWTxZzFDEmq5j0O7Z4Vf8eRC5dVfkRsuTbzNBnXK6HaPhjM2IB\nTrn6dii8vRj8D8hwDwC1+B2HC9hABBPmfOeUdXVR3GJSlI7OAMCArFgJ0Ay2\nZTF5sctXfVnyz7Eng44eedL2EY4qcrvzbYKyJE/hH5xx2UU9ZC1NUESleDWg\n3hlqKaV2p/2krbfaZPj2SkOH8yWcSz+rndgyp8SpPFJ9Z9vKE/7IpO2NCLny\nuGnZDgfwm3j+UVnahkzXVw7t9vMVbJHIdfTk6FZ4hfVq/qu4ryk0F9mmQ08r\nveUo\r\n=1SmS\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.1.1": {"name": "undici", "version": "4.1.1", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "jest": "^27.0.5", "husky": "^6.0.0", "proxy": "^1.0.2", "sinon": "^11.1.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^5.3.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^15.0.2", "docsify-cli": "^4.4.2", "concurrently": "^6.1.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "f573dbc6bc5ed86256f34690ed905124525048d9", "tarball": "https://registry.npmjs.org/undici/-/undici-4.1.1.tgz", "fileCount": 64, "integrity": "sha512-aqdRMkuNEkVHrAE7tce+FzF86DUcfbuv1nv4j3BNqx6l8sP4/6urJS6crf/wBEhotvP6YDaoqzZS6hYgjAt6BQ==", "signatures": [{"sig": "MEQCIEn9SRSE2vbVx+MzZhOHYxmVKGmJ8Bfl7mMtAUGAdS8vAiBuELMb0/qlWmhdtgtj++/O/7kDnqaUcyw3u6bi5nx0FA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 427858, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg6/TmCRA9TVsSAnZWagAAvo4P/35lLqdzKpCbsMbqtC48\npVVhWpZh8+rCWH2wEgonMlrE9xY/+aYuqQgKZW6IgwB7THrWQbDS5CAuOz5f\nj7VFmUDmjtk+xRq9hLVCcSTcVI7aRXrDEZWLbc74xWCm2Z68XrhNv8/CqDt7\np3QAdYCa5l7d7FWHxY0p4b2fwTb9U6SnIt95quhCO8d6eUiGEVByA4bhF8jG\n1mv3mGUU4kKg/2LhnN8J5ucmhmNnZ790qS+2HHkJYf55y0B3iATFMtfzagxD\nPXr6yaopQMzuXz7lFkmL82rnmp7MnAkIdJRRpXHWjKVzQiwKRoq9W0G/DoJ0\nuzcASmo98xBjWZ392lIVwLI90UqpjmigN6ZcelFCT5OgBJAS9cecC03Q8EwU\nTUUm8nii9GvkHUMIUA/RhT25u1/uJ43RQS9c0nRC/o4R1z2IyvSdWfDaenj6\nUYBMPf5cDGxOQ8HnlImtHsFttH9bRg/JHji91+VtnX7ubTpQNJASakud7sOm\nlRq9zx/+MnxBmEog1j5ibJ9r+7IvQeEQFDNe0xlwxmtL6Tz3pMY8KPSqqygt\nKcQuK9iW+MB/Xgt8NY6BMZq9oU4cSXJ02obSj7xy5qUHtND+meMl7g3lsu75\nrlh5gr+XHLkUA1O+wNR2/JZq+opwwN/aGqqsi2qSkcm9ulkl10zklu0wqGVP\nUtuc\r\n=9u6A\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.2.0": {"name": "undici", "version": "4.2.0", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "jest": "^27.0.5", "husky": "^6.0.0", "proxy": "^1.0.2", "sinon": "^11.1.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^5.3.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^15.0.2", "docsify-cli": "^4.4.2", "concurrently": "^6.1.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "3781e566807cc28c2cc7ab929e9d41b8866791c7", "tarball": "https://registry.npmjs.org/undici/-/undici-4.2.0.tgz", "fileCount": 64, "integrity": "sha512-DhvvOjqFDANXg8E/7ropGXidmhx/lD55YViDIKrGZOHD7iQjRwJ8x89x1t0cE/Nk0grhFe2K84hn1dd2dl387g==", "signatures": [{"sig": "MEQCIAjfiOOnmmsB/7wf5iZHlrauUYqzMuqpfLEiP4b0EfmFAiA6uhQz0FPWlspgpS2cjVU48xTRdCxa9CtqtcCIDTZwow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 428340, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg9XpnCRA9TVsSAnZWagAA9o0P/jdREM6egV0vUKCSMf0G\nYVaskX0eb6S+uhGJUicC+xGnTRZOiOvQyN6lkRFjUXXtNiebK7nPIm9KBbIS\nhvM44W/S81p7mgp5dAE6GlGeFOK2UcUpvPs/6YW/FNn6FnbbFdukjhMvNOCV\nBRXO06mnqFSupcexvKfPrU/MVyYRexm2iphTtIEa65C30poO6kEgItHQHcZX\nwrTHeCnh7+BvunK7f08BpWsbh7vH6GGZRJ/zXM+fM72o8YJm+iEvGhs3/FEl\nmS+SPgdJv1AHpTpimaqs4VbQX9dJXu4o7G5jY6FawjSwjD25V0ce92wfHUkJ\n22+yefWlA+5vVlR7NxPC/K02Y7Pp+Ag6ht/ABGxykQYGynzdyjzl/w8wQq2w\nL/avFnUVL0Qhy58y1EZYx8LaR1NgxwbDKa9dNlgmjvaS0HbD+XjL21Fe5Aal\n3w8XXSoo3le2JR0QaOq44c18o4Y4eL8aDmfRA+7rLBdTNJvzc5MVjpfmuGNp\nl0RKSSg8dmh6rqsNDFIsYXZvnUvjlFz175vzEdpg6F+vY1NZ0RE9FYjlL+4S\nGPFO4yUGQznfGSABndWzzaxcGvFMItcN/TiLFYWYkPeqWoJWCZoH111a5s1C\n2sbJURzVfZ2zXW9kU5hZrURmqM2EVxMGCi92HECEy7bq8JZbPEh1zGWAqk2/\n/fY1\r\n=hjIm\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.2.1": {"name": "undici", "version": "4.2.1", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "jest": "^27.0.5", "husky": "^6.0.0", "proxy": "^1.0.2", "sinon": "^11.1.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^5.3.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^15.0.2", "docsify-cli": "^4.4.2", "concurrently": "^6.1.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "551cf694464e85b0853432bce7cd387837727ce6", "tarball": "https://registry.npmjs.org/undici/-/undici-4.2.1.tgz", "fileCount": 65, "integrity": "sha512-sv9G3KLhF8hhrlkCjQerFJBRS4ZIc6RH9foCkuZSgsEapAj9OENQA2TONt3nGJ++f/6NRWeXzAFbQOfjaZVZrA==", "signatures": [{"sig": "MEUCIQCSOI8vztOwSRh1kI8/dQE6nPLvb/o6JiuvegNsElmSLQIgCcyJsHY7jAlR5dWnFcJ2mBb4rITD4pBWaNn7ydJTZ6I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 430829, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg9XrnCRA9TVsSAnZWagAA7wIQAIYhpKaAr18r+MWMpyLZ\nAku9iJ5yZmnpqDDBHzK/2zH+6rP+YlmSYwoO5mo4N5drGzYmNGb2z+9sUgBe\nqPkqxF9HB7pIlHle72Lyvd3n2unwV04tnjvt3iqYqasz2GElfsyQWoCML7oN\n/5NvGLX0Y5USSCzfLLVD+Khmi6t2Pk9jWkcwYtCiOuezn9qAasu/VNoDGd7o\nwg1lon+xCwO8XmN9RIZVNrZBujzzdLjCXxGxfMhw/epBsHMZmwgxnYhX9bdL\nbExPcIhPa6XVsNpFfZSm8kOP9QCXrNWdJns0JVm16SSuG1mvqgdNRVe01quv\nI9uKMhCELVHm+0r6cSavAnOKq+mysMF07FbYbVQQcxF3RSM2PR3g2v8N1pdH\nW9pKuXJPvsamlEyqt09ioxH1w6chW6eIfV2wWadDMYLXtSUsmwwpwJZk+2v3\nmlvd2vdo/Q7HxhFOr8oEa/WNJ08vaI46hh9ghh1kOC3VG/enH6MO+Ek94GS0\n8loDeYjMTMrZeAh4zxuOIkq62/rXrObTakfMv7Uw9eLcbJl353wQSG2U/qSh\nw+wK+uVBnmxAw5VIeRkojYIHrksM+nAeuMWSheekRveHSDQu3uVcBOBOGa6j\nw7gdekWXd75DVbb2hNLQOxcXG0TLWaDlJEMtz0hlNYcdzNFvKCxy5LUDsyTk\nBIOU\r\n=QAfM\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.2.2": {"name": "undici", "version": "4.2.2", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "jest": "^27.0.5", "husky": "^6.0.0", "proxy": "^1.0.2", "sinon": "^11.1.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^5.3.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^15.0.2", "docsify-cli": "^4.4.2", "concurrently": "^6.1.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "cde98830799191bd4df033ff8658de7d3557a82a", "tarball": "https://registry.npmjs.org/undici/-/undici-4.2.2.tgz", "fileCount": 65, "integrity": "sha512-WeLqJE/N66VjFeNawJkWHGB1qBGL3VHeimAEKhOC7lbeCf7p6daUVYUHYFeTo0V1EDlxdzqpzykImadFHjuXgA==", "signatures": [{"sig": "MEQCIBZUgoqidora0ZoHTTBo5Hlzx6wA8a88knFPxLxI35HBAiBLLmfxE42Yod0u4wao8Q11shVzYhNLUj94AGc4lYuEpA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 431005, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg+bSDCRA9TVsSAnZWagAAlqkP/jidbX78dn6VSfy38fgA\nkm/tMch52lGHdtESII+JGC8SO1JCuUSoWJl8u5EDxBDJsjuip/UI4Y3uGQgH\nJh1hwoJK2WHsN6OaFY9DLNs+OURulm+dt39jij2H82OilrEOJ2WogBoWA1KF\nSI21xRwKLWWN+mKFS8kOQpfGInKoZLEYfvEGP7RSQolwufRXhZ5H4s/P9Sx/\nVwLszi7ltO6gHB9vo8I84FQtspu1LesBHUIYEye7Ws+dJWKe1y0TwnYbsma2\nnWqYe1Z4I3gS5nD/83lmkVjSoC+1p7NzfoMgjEn9Z9y66JphfpzvPghQwOEM\nO0+9D9VHSKwZwXOJwOK7JVkTU4QtG2i0woqM19gSQgqNQ7scrsVmGqJ4S/My\nCSTrRryKyEAW8Yuggh89SjDNZOs3rnng4tQXGl/VwWE+LryGqOAe5lUmCmh3\nSSwQlu44pU2OIvcXSXLpz/AgGR/p/OPPmK1c8c1ZhV7nNEMamzIrI0TIb2rW\nOCjINtRn5DHX/kghiP9XhmyG/AGsm4osSwlnzcrWnMx9RayiLiFPjLQKUVwy\nRkj2T5GHYfqyb0odHYypg1a/d8dYgJ63c2wIy7qFIpVzeHLOADus9nVpQ+Gt\n4zBzPogufbadzT51ryxyYXJpQYjp8gaDLZyJzMYxZ6FxwdIFESLZhzLgVzbX\nJ5J7\r\n=Z+1C\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.3.0": {"name": "undici", "version": "4.3.0", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "jest": "^27.0.5", "husky": "^6.0.0", "proxy": "^1.0.2", "sinon": "^11.1.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^5.3.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^15.0.2", "docsify-cli": "^4.4.2", "concurrently": "^6.1.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "5e008ffe4ee5b5a15aa7fc1a24389a8b399981ca", "tarball": "https://registry.npmjs.org/undici/-/undici-4.3.0.tgz", "fileCount": 68, "integrity": "sha512-MK5uUNt3/MvCOUFIieAJOrX12+3jbkEJrm0M2vsK+Lehh3fdrnSeprx6xHmqJnm860N6191xGc52rQma7bdFYg==", "signatures": [{"sig": "MEQCID2GypdyB191IT6NYaaG85sXddG9oubUTT9tMn5ajD5ZAiBAHuAGh2b5vo97icVZUAl6dH4u5ULwm8Vq2NmbhdgCfA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 443807, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhBWjPCRA9TVsSAnZWagAAkr4P/0GKA6+x60DLTS3JWxsw\ncPhFeZ7ajv6NzPmXjb1hkirxMXKj85XOEe5nwt4YumEngNRJFPdV4Z/lbHVM\nHpvpQ6zZIDjO6qTwJjLXXWK8wBiX2Ec2gScnfL7s0a/qvl8ZPyV13mgiEpRO\nDwL2cQ8sp2IBBr/NJY8Un6Vxmr8Eza505F/GjgnxJ02ew2W2QR/TKClYA22F\nVTYQryOQx1cm8t5t4sBaJidGABXS/msGlfk29SOni9R9NQxpMBfrbd7nBARi\n/5oxboG8BUElCDhDaPGR/f8TT8i/edl0zsedcMD2vbcMZpxmXKQNhz3Ia6cF\nJlYoLHM+pnQ/q5tD3DcHPiN9O6QpNtlXpbDwDDgsxnCoJofs72slG354BQ0Q\nP3GJFhKa1eJG3Y3AslZP6W+BYIu5Cx/s/TEYWVTxdl8Z2UFr3heGw6EGHIL0\nPN9986HvE7brqHgbNazTmWtXwjP63iThMeyzBnmBQ1KfvxvUhq+szF5BceM5\n+p9qpNUhSj7OAjmd2PzSvPCudB3lkzRvYD5AiQY5JDE4r7JBuoMBpKf1WV6S\nXcpNBHTCUdkfsgJ4955f8yf8spDZEGLHDu9cAEe4R2qcyiK3axjTKvrWRgPe\nlw3xje0S5Qrrh3/pZjgV8orQmcUXAk3MmdXBJ1JFDdJhFRGDiHTYLRW9HUTf\n0Jpn\r\n=BQcN\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.3.1": {"name": "undici", "version": "4.3.1", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "jest": "^27.0.5", "husky": "^6.0.0", "proxy": "^1.0.2", "sinon": "^11.1.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^5.3.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^15.0.2", "docsify-cli": "^4.4.2", "concurrently": "^6.1.0", "abort-controller": "^3.0.0", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "f576638a3d6e157210a87c02b006236243c86780", "tarball": "https://registry.npmjs.org/undici/-/undici-4.3.1.tgz", "fileCount": 68, "integrity": "sha512-Oy61gPuXBE4Em/1t8QeKOSHGSqbPHLW1iRbCztbyHuKusz5j7d7b4Z9Xi8b7yFqHyCNe57SSDjBgmSUjfPCymw==", "signatures": [{"sig": "MEUCIEEJAQhsXES52NZbtoInc5THz0wqETOeoSCrZfTU1M4gAiEAmecIGJArjwUsRJeatJQ9Q/1bzFosfAYXLeglWD8STg4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 442292, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhBXk4CRA9TVsSAnZWagAAaUIP/AoCzLD056sr1FTHh6u5\nHv9Q5gy6Aub8RVNu+pNzB1RkZc5y91JZbJEDFBGLbxzqY0SeJ3P0x2VS4Nsl\nIjwcIGrhcTFe6BqqHn/J1dMPYBQf3Qt7jFWDw7g9hg0q0dR8K/PALAEVFstM\nlhx7gO1Mpn4y/49UjOJi2PXBIUDTr4+QeNeD7vYzze/wmpf1+Crf1O5XI2gB\nQUD8Wz2JAp3Xq2NGbDQf/EkrUTJsZhAfjo0oMp8nDDG1geWKK00ciLsBCmt/\nB/VZXRfcxEQWcPZ6Jn/9tfcQyWinrt8Znl7CnZdtWDPc3fpheCHM/R0CrZbm\nUcD3HP2KqRZmnNV+ioss8Ljyw3aVRpJyhEPhop9hlNLt5FJFq+9dLSuSDRqh\ndDVq2VL/2X3RmwNFOgG22MAxGsdxc98NV5sgUKSGWSEmKMyTLeypqwgc8XrG\nANh+PbL/ooHArjDSXqiuRY/rqclo6rTV5W87Usj6RTc+Dwfb8Z1l5N2JTf3W\nFt43A4tta3UGEif+7H0lFfpKsBh/DR2zawKatXKE8ASNfQDetCLsufmK+jHY\nLtbMjNZ26tD5wSIpB3ZH6tY7rCzcZRwIxzUOv430KNscWkwX9I8uxrL8451+\nq80GwaVJJEubwKcXBL80ca9SEfCbG2huH9Koj6Q/T/L0v3DXzMvJmVg6lns2\nUk70\r\n=IL+7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.4.1": {"name": "undici", "version": "4.4.1", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.0.5", "delay": "^5.0.0", "husky": "^6.0.0", "mocha": "^9.0.3", "proxy": "^1.0.2", "sinon": "^11.1.1", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^5.3.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^15.0.2", "chai-string": "^1.5.0", "docsify-cli": "^4.4.2", "concurrently": "^6.1.0", "chai-iterator": "^3.0.2", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "832a64e593e3f068e87a9b8f3c3037f7f48b7cfe", "tarball": "https://registry.npmjs.org/undici/-/undici-4.4.1.tgz", "fileCount": 77, "integrity": "sha512-diVUJ5KcFxF5STCYC6oarfFa5UrIMEbY99Zt9IAXbQusrelH2btUFQwZtZyN+jLXIkFMTQoWPprZEIhYJmSDFw==", "signatures": [{"sig": "MEYCIQCZJdQUdhzdrxxxcTep2hOwgTbagt3wHdoq2/DkOM2vpAIhANE5IhjhiBB1ZuQH5ePizG4WvIXl9Q/TzSOItYY67Njw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 557127, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhFQwFCRA9TVsSAnZWagAA1CQP/3UDx69pLqGy4qoOjqVe\n5Hasz7iZ0jZS3GCmfSMxfir/nv913blmgKjdvnZdjlp3f2JVVrwaoSvABVW5\nstHzgINx0lXMbI/F8mNQ924hgZEWS5iniHx8UgQDP2wJNdqiauRmaKbMm8WQ\nsrPplNVbsAFXLqfee+P14W7GR4N0j01qmXfxrIEX8ITfRAWgDJgb2cNfP9Zd\nxj+FoS8/Xqzsl4oiJUhVZOUffFoMHMqBD6uRcJtpMDIb4Ty8t+TKoFZNzFTN\n1HZw4V/oPMncpoUUAE5L3Ucy6KiimARTaayNRsSRMFCGY89BUpe2lRbbYAcb\nP9La9d63WtSNC19DvFPSlWGLr2STsemmTuWs8fy1m9TMrktKZyXomwKdZUxv\n04Z4WFD9wx3OnKPWccaNzF8yf255Dm5d3kfnhIHgQgeLb5IX/G4OeyjgEO/D\nXFOcDgjSBsxw65mVI9jY9+IdO/Yoc8C4gqJgEggO4ZtSrNmKXG0JHw6f4QxV\nfVz0MuZbPw1+oJTwfS52+74Um5Sylr2wCAlRukt/fcupqdSL0sZ+ptkH/gMQ\n05mDFmNZvsio/+Yf4Am/2BlHSJKxypoEKwmievFn5Z0FQl9XUK2xRIAz9bhn\nQJQMT3z59tjt53tqlr6FTtXS95lsCOhc/hQpXR353adPTI94dqdF9NBV2h4S\neaUB\r\n=zkv5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.4.2": {"name": "undici", "version": "4.4.2", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.0.5", "delay": "^5.0.0", "husky": "^6.0.0", "mocha": "^9.0.3", "proxy": "^1.0.2", "sinon": "^11.1.1", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^5.3.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^15.0.2", "chai-string": "^1.5.0", "docsify-cli": "^4.4.2", "concurrently": "^6.1.0", "chai-iterator": "^3.0.2", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "2c02d5642f24f1d94e86958caf45c3b9ddc07fad", "tarball": "https://registry.npmjs.org/undici/-/undici-4.4.2.tgz", "fileCount": 77, "integrity": "sha512-ATdfIjCZgbF2DL/IX7/iURr+UfJA8QJS62GzjPkO1rHdIdwNjo35U9XvfEZ6O60KSQNQFrhokD7oJcExQumDtQ==", "signatures": [{"sig": "MEYCIQDRlG4oVj0KRQCjeN8xC3N96IdxCj11sHbR6qnOO62B5wIhAKZRYJPmOhUurnqBX6Dtazwqj4AzUuCHQs2XNO27jEi/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 567818, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhFqijCRA9TVsSAnZWagAABxQP/A7hfgs0yQbhifMkkEuW\nYYQWrM5JxDSMNl3mr3rqa21azhv3i4tSs23DdTp0GlikBeBMOP4UMoQmMnsv\nKiO3fehi1tAh3nIFMYshrPq5g8fZEAvxuiJmo/BNAqtSrBW6YkUCftDCu9q3\n+tKEi2bqPD0nd4fReNJMrj9qi63I2mVNPaINHrdiAfxppPCBfnXWillcB/p9\n2hMJas3u7CiktmWirtBdUCvSok00J3b8btyg+xv3dksDsI4UQo4RSp118Eu9\nJNBd0hOKYQ1yKD/ws6dpDA8FnphxpW+tQR9xNJKQx31Kzx7RE1KQTTnEQJPa\n6J0A4lEXQtWdNVv1UeRj+J31FSH8uZBGBR0tQ6nTMyHMBYHiqymWCmoDzm29\nwd0z5XOtQPfjZLmM5RsHHBplb+Cwr0FRRLnbXEbJjwu7Jp7VtR0h+dMqm0WV\nidAoRBBbM2w6T4Th0iJjV0YxW3Jv8YQOB0PXGKKfyXcQTf1vKFCTJEZLQmhx\nYX0VRfmtmyGEEaaKd75dXDOj8B59mnaYQKWuqewmNoP3Pqtw79YZtguhIfv5\ndwslomB4K7g0zCnGCAfYzPPWtMIpjSW+l3f+aglIseHAaSL6Brcq1KAeQizf\n2c2cSaiBxGiX/L7w6XXQW4jmdxh15RnxaySUvFqzudb8YyyC9QtEq8eHCb3W\npRox\r\n=24gY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.4.3": {"name": "undici", "version": "4.4.3", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.0.5", "delay": "^5.0.0", "husky": "^6.0.0", "mocha": "^9.0.3", "proxy": "^1.0.2", "sinon": "^11.1.1", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^5.3.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^15.0.2", "chai-string": "^1.5.0", "docsify-cli": "^4.4.2", "concurrently": "^6.1.0", "chai-iterator": "^3.0.2", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "77a86225b307406cdce31bce24df2afabff4ee5c", "tarball": "https://registry.npmjs.org/undici/-/undici-4.4.3.tgz", "fileCount": 77, "integrity": "sha512-afb4+kNpEFoEgWsyjAr8UfnjMXF8tcWqPkjaclk6nPf9DVxU1xWKGeMBcIAu/aSmvT79nXpAHOrDdbmrBZRVcg==", "signatures": [{"sig": "MEUCIQCyXrwtybNhAwfYtaA02Wjoi09Zr+7DrhPCjl6H9FBfJQIgHEdhBB4XMyrFwFXfFlSknQhxmp00SecszooqlhmFLP8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 580136, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhG6NLCRA9TVsSAnZWagAA76kP/2lwvjfGplKQRekaw/5q\n+sh2K6ZzoLynhd8xOr4Q5VLNkFMgM2dfxLGpXQokPXO4LtDSWybx2IsN5ncr\nCtdbK0trJuCH7jEQ55n5pUrI10E2II8nVfMxF9jwjd6WVdD8BQVeTuZUH9JF\nFnxyHy2A7ORNEK8AVg/XcockfOfFWqKyl6mrmIP3Pblu+mbbNtzcxnbRvL69\nOT+F49ud2X9b6HEaqDF6Ar1Y64dP0k9YLNnWVNg5KwPcEgGJ64mhbk9+XyO1\nWHdqTjEItuhbQFn+/fdPwIocz0NYEZeXhaguMh0a3otKr6IHHG02FZY3Vjmq\noXLsTnUX8g4DmVjszEeRNrGSsZT/JFeXbKC3KK9RPf+pIe+mJGURYki8pXl+\nb9yb+FTpYGMfdc4eViTthrmXa+s2W43Hgo3naOMJe7fKizDsKtEymHbT+72Z\nsAE++FwxuLza17CwDOe8LnNvDnr3P2dGCzvAS7t8hx+Hc/zdXmmS9yV4NROv\nkZat60WbWpxjlab4ZehSFY23edpqzz6w1yPYhkbO5XZBB51aybq/1pANXB+K\nWYN86eb7Uj6GQfilNElvZEVtkP/qKcimLivblb574S9jO5mNATCXjeDZmgyA\niqjegfPgOgAq6epXLif8YaJye4da8XGOxYkKbQ9+Ein6ISydGKsaLMBZBNd1\ngppV\r\n=mdNM\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.4.4": {"name": "undici", "version": "4.4.4", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.0.5", "delay": "^5.0.0", "husky": "^6.0.0", "mocha": "^9.0.3", "proxy": "^1.0.2", "sinon": "^11.1.1", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^5.3.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^15.0.2", "chai-string": "^1.5.0", "docsify-cli": "^4.4.2", "concurrently": "^6.1.0", "chai-iterator": "^3.0.2", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "291152b99d364848919526c79a6f46d75f4427a7", "tarball": "https://registry.npmjs.org/undici/-/undici-4.4.4.tgz", "fileCount": 79, "integrity": "sha512-UTVdW5XyG5PAufwBxCm6Faw9fXgExEXIDArAVigsJloMC8y3HQypSkZpWaNqCJvV1wP07iK8L1OdiZfv5yC2oA==", "signatures": [{"sig": "MEUCIG1gOzjJWGTN4PW4IPOvythE/Wk6EyJaACiuKqm92htPAiEA03C74EJxvwkjkP6UHtsJP6TehkJ6/Ag/vD/4Eqj1deo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 592366, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhG7yCCRA9TVsSAnZWagAA1AUP/RWgKSe/RhwlS2vG4+WH\nymdDard1CS7NTg2gy3LN3QMaP29KevaEecbawqosAM/3QdcQyFAGqKI9xu4C\ncuVqtdpT/mUAtsFS7GtH5Yl5KnLBU8r3KP2wemX71UIPfOXATMqU7o+VCMwn\n0kXxyourX8YqnPGUOkfDGUmAW2k7diHD54zIOQQr57x+69TMbV2d5qdRo0XS\nfkyqqYjBWE+DCgQxWc07hrP4zn4J1yj0qCSlJtCI69KHZP/45H7Fb+8geuIB\neLfF02y1uTCoTZY0LY4/DMe9yOGn6LgAhVpZrEAGouhnfN353di+uskg+6/N\nx6/sV67/wyizcU0mcWH+bPbiLULHVPAEGceqrVGpE3q8H/PMIVTyXV2bnvNW\nkvgkYLVLHSNqoWHxT1muMBwfL29aGPjks47pFnRlFaBiJ8RuU8uSSn4zcnCv\nxaxTBN5UlNQ1qdieCASKONFIKKl3E3toywICRy0qTXYM7nUr8mh/RPdVzGEY\nMHTF8g9GXnU5BJuMMaTjmuNmkk8Gx1AgVmyJew4R8wh4bvRcicD0SVII7MuW\notCwYxOoztieM93GyrWMyTCP4UbGpzcjew9Kz8Og6EyBj1vrgS/BnBybGKDm\nGpPC0Aw9I4U6huf/7YuAhdXCBJ1HMpw3xkTC+/kg8jgzZwFjDS/QoYH/HuC/\nKhxY\r\n=05dN\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.4.5": {"name": "undici", "version": "4.4.5", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.0.6", "delay": "^5.0.0", "husky": "^7.0.1", "mocha": "^9.0.3", "proxy": "^1.0.2", "sinon": "^11.1.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^16.6.1", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^6.2.1", "chai-iterator": "^3.0.2", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "6273807c0b839d2e033e44472c39532238743867", "tarball": "https://registry.npmjs.org/undici/-/undici-4.4.5.tgz", "fileCount": 79, "integrity": "sha512-g4PtQrFE2+WkBaL7P4ethZDhkFh8fS5UnN/L+NLtfLXkGGi7asZR+s08qKVR54KZSytD6RQscGTlOwbElGRNyw==", "signatures": [{"sig": "MEUCIBzknqtaHlej2oCXSpW6xEQYBvLci6mXV+Ekiy3/60ftAiEA/3mSosKRc40LihLHwvEMM36DeacFLT4RdZosjOOchUQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 590871, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhHlmuCRA9TVsSAnZWagAAI10QAIUcexIFhGfo1rd8BNgU\nsdwij7gVjIiuMQObtJAodk4rnM/k4MFqaFilagfTcJmoYjlFsCkGMSR5u9pH\nd1q+Q717WHgRDOYUoOmyvrD2zrgjJIuoEdpJdDqmuukqiFAzpl/Wnv9Ui2mr\nCgupNmtYdkgpTt12HI5Lrav1oqkWYc/O7M/5/YbjGaqy2OuEcDOcSvsx8WAW\nb9rdk+FAQnG2fDhiySC5lEONgMdHdHKXgDsu+9wd0PR+38uaRE+N20GPHW/X\n/p7eeyY7NKVBZG0v38JalRqOJqRdY/G/Hh5exVK/k/G6CZuIbAkAJhQsKge/\nnHZkFzDI/6jtNGpUchMupsn4uZ5uGjAuMXFfkHEV4rUe6tyynuvMNhGuQDPj\nwdgZtF03JWHTninGiUPTsPCJ9mMkeNDdi2pP7FEPsX8YqPyjTTYOm1TddHsN\nTnxH8Au0PcrUvmHbSVjqmpB8CuEJP7TDOQ03BAkqniHilmZ+n1v+Y2DrP75V\nrdx2/96mBQxtKs0vI6oQNXjvpjryz8jtML4/ePtDehsOLbJov/kp6wWGsVQf\n4I2LgCqFmuZDiZMlzu8DBkJAKGjiREH+pl2NuqSUMQmxE4mY38kLFGIU7gty\nn1UNnqVjiGbX8PcgAsxwGns1PipjCQiScsZo4wOgrt8JsO/kt/OUocdKh4iy\n5hyB\r\n=U03D\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.4.6": {"name": "undici", "version": "4.4.6", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.0.6", "delay": "^5.0.0", "husky": "^7.0.1", "mocha": "^9.0.3", "proxy": "^1.0.2", "sinon": "^11.1.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^16.6.1", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^6.2.1", "chai-iterator": "^3.0.2", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "628f1902db7857a369da4a0547a91bac1f957474", "tarball": "https://registry.npmjs.org/undici/-/undici-4.4.6.tgz", "fileCount": 79, "integrity": "sha512-NxnFqJouVi+R7wC7TtIeIP9AeBIy1ZHwnUHtmDFyXhtKNFQ2pjxFZdz3yYBKEWDTPQ5D2ipYxtovOi1tuU6s4w==", "signatures": [{"sig": "MEQCICRq3muX1Nugh0nxG8sHEZIy1c19sMfqaoRhvtAkbRD7AiBYJ/4gaypCuwLygKMVAlxrB810clOSJj9WviAFrJHrXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 591480, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhH7X0CRA9TVsSAnZWagAAJi0P/AlksUgngjhz8eB+kYFA\n5g1hAYLT2MZv5fS7LASp8sNYXMCZzOm0nSnaPYyi8C+BVcs4J+W907lln51o\nZItIUkiFcdjzgWdw/sPDsgAdYsEaJ9wvDP5r4oQajULEUg2kTGu+QAXfMb8/\n8Xq1x5ty4OJI3Biup4vxNypvwD+WuiKL/GH/XEnov+yQRgtRAcLaTzGVM8uy\nXS+MnOVUrNzVzYVE/G2QqJ1QvnBX5E7cdihZRl4UDEWNiHxGuZz5/ozWds2x\nusPKnoxN9yAuI7skaFwHuNcZXvbxUIRkjfVrsQNHkQNzMtR1UJ6sAVJmraQw\nIseWPvECEAqWxpwxn9ZjBowYHCiCin4Xi8Aa9dKpxhWJULcV2cucb6qi4ijt\nyU3uHDXqGGh0csMZL97x+vq/flLDn8OP0OU4vQcKC9kdpGAPi6dtr/6rfKB0\na9imO/8tCm+NB92IItsbkgIdzXyZdW1RwnRG2OOtE5wRNkRR/HlAS2SiiR5N\n/zvMYpzBRnyDAy4BVwp9c1IqNGi1Rd7AOXqkYEA+ZK1caUlCdD2DyPra+WeX\ne4Wayrnu+jqZywSAuOMYl8HuTCLDgUNQrkPwHIZ096/Nk0ag6V89Ml6dF+5J\n1YGSrGwmVGRUZO4L5OOqgGi+LF20GU43Gpbfm2f8cqmDzwKAd9I4z4na2YR5\nMG6Z\r\n=E9Hf\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.4.7": {"name": "undici", "version": "4.4.7", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.0.6", "delay": "^5.0.0", "husky": "^7.0.1", "mocha": "^9.0.3", "proxy": "^1.0.2", "sinon": "^11.1.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^16.6.1", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^6.2.1", "chai-iterator": "^3.0.2", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "3fbc1eb2d01d76950168c92e5b0d22d307589958", "tarball": "https://registry.npmjs.org/undici/-/undici-4.4.7.tgz", "fileCount": 79, "integrity": "sha512-41YDu0wuKPhvd2oPDHRe0ufai70O8nOyL6vgpWkv1DUPTwOx59GhZVRvZwinBLAiKJHta/91gSb7wmrDghuJIw==", "signatures": [{"sig": "MEUCIBVjdOdXy8SZ1MpVOTjEhRPcqJsqrKhuWqe0iEtamAirAiEA3avyl30+Iho2jzVA8VG5jxD+453QY+qLBHqX05kLj/E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 591425, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhJJfoCRA9TVsSAnZWagAAU0AP/0PuhE3t3qTbvyrV8twi\nXZ47d3mEPiMGj2bDA2sEeT8z/VYyU+JolIHnOvYf/JY1mX161RJJw98tizRU\nNgVrq+jPJ9/nZ43NLy6uw+dnUstG7ndNl0DZFFt2X7xitqJ92qwWixtRwnP2\naWFUzV2PtArz+MFKiiV+trEqPsIFkf0VTserao4a27qELeHM11mn6h9aoOKN\n6hSb8lrppSjJxbPStiiwYOkPjU+dUWgTvYT+JYDi/ZtlquWHCL4aTS3+Bhp6\niUOAid0CnP2xdgP29H9JVMDHXgl3t6E/VzLPqChDDQfMfVaI+SUk5eyy5E19\nH0io6lUOzV/k9IRtbavXiS1pbpe5ap0iinKB1yyCesx7kVtTLW5o1I4zShd8\nC4Mt7hkbJg5n2U92LSc8vYL9iYzBz2sJO8ZY8cyCLij2ASjIrOaaktccTztD\npSpkXlJFT2qehMgIRXRx18gTiDhJ2Y5O1qv+IIvJTvChGpH8qCCKp/NoYmJl\nwo2W1RtbuVrOQh+bSEg4j4R9t7oKJrCGNBS7rdHqdzxbuU7kd/anrKXPqgK+\nzP67Pa4FeV7YAxyNOj4GspATYSdpJ4kyuR//Gf/M/fEypDWTprinbZJ4CaKJ\nvbARJ6Hw0j4qXDqn/Kqep3mcLaDqg1AxrXLlfaeKR741UUzY9NIFZEZ3YOr/\nozyq\r\n=ybzD\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.5.0": {"name": "undici", "version": "4.5.0", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.0.6", "delay": "^5.0.0", "husky": "^7.0.1", "mocha": "^9.0.3", "proxy": "^1.0.2", "sinon": "^11.1.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^16.6.1", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^6.2.1", "chai-iterator": "^3.0.2", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "87d42cfe2c363fb14f278b0a0043b436ee2b5dd7", "tarball": "https://registry.npmjs.org/undici/-/undici-4.5.0.tgz", "fileCount": 82, "integrity": "sha512-N2e4eDS+rv6qljPP+RMlTZXSS4O6V9LuyIvDT9btweBOPMjUAe5k+u4JfJdbGe8WFjJMhqmOu6dR8tSY0XqNgw==", "signatures": [{"sig": "MEYCIQC4hwjq2E+dMFNEr6V4bwBov32/XwqnPSwaRFdzH9XtJwIhANRrHXRqD5gty1vByBm9J5awoRm7uXiNu6DmSGxiXj6J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 604680, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhJ1TKCRA9TVsSAnZWagAAgJoP/i0M+aWALcv9qQErK0+4\nM5UQyIfiNaFsAUCeGp9jFp2QfBvicWQFRf3g6TqVSiU9gZezy3G8xH/mC5bm\nZtulR2P6+WecK6ObYmD1vqo7YurvDB9FefPG0zfYjYAPMXgOctotPwsks6Sy\n2SR3IlYc75eZG5gmJG+QI6PR33jNKys3RGooNU2BZL2u+dTj4m7axcRb0paQ\nkj1A+1xpvCObK/q7410j9ld9gAT5bUYc4J9vhYu7gir6S37WuU4GS3JWF/Xx\nornM6V9v/C6qxyJL+7Vd9EnS+Mz2ziKXhIheEiMK1y11zhaH8WWjTlwVijlW\nI8+ST6T3j4pOL2eYWBit2GPgYo0A6nUw2ESFphD3rwdVTEuTgCAPr9lwFnTS\na3GGaOqc52xsfyodQGQuNe9cXwjYVeleS2PcIBWR5UzEK9gDdMkU/ylXUecX\n/J4hTBXpQg6gyKXPhdQZYOnHOoWO7cJENS/WroyU4zlCTocjCsXuUaX51zRZ\nQokljZiN4nfk0NRYHFiI9fCMgmuF5Yb+26+rdyeDU+JbN4FjfuvJUSnB5mRR\nAVHOklRE1vQBl9DsKWPR/g8LCkdVk3IPWe7n8+MAnfuLnE0FWPBoqIxR4+BD\n17Z8F0FJHokA6WkzjVqA35gYc/2tq/DDwqtbj4Rbu9acdZRzOV5chVtADcCa\nGNE2\r\n=0i9A\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.5.1": {"name": "undici", "version": "4.5.1", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.0.6", "delay": "^5.0.0", "husky": "^7.0.1", "mocha": "^9.0.3", "proxy": "^1.0.2", "sinon": "^11.1.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^16.6.1", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^6.2.1", "chai-iterator": "^3.0.2", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "482456da326b06220fcbd4db3eacc2fba1f2d05d", "tarball": "https://registry.npmjs.org/undici/-/undici-4.5.1.tgz", "fileCount": 82, "integrity": "sha512-1Kmphp4SMwVbSauz9xH4gxt0m3sLGs5qRHs/XYgjeO3bNSt6hspDZqMhM8+ETu9ynB5bq9e6mnwcDz+NVCQ3UQ==", "signatures": [{"sig": "MEUCIQDBcrD8ka9e2oaxERsBa/77ePdukhiyKuqsPWVmDtJr3AIgeM3dX5MflaiuZOiKQBzFxKirI1TXYO7AlQpjRDebt2s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 604948, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhKkp6CRA9TVsSAnZWagAAqsAP/2+pG/2KuOUVrPIXQkE7\naUo82IsAunzIe+OHmmr+3+DO0ULQ7dMjuBArLAp7N2aIxMeu4QWs91dR0yj5\nXsRA8x0HmtunDwBspFXG9AKe/Jpgfg536zNZtf8qGrdiwbwd7KpCTGkan9y9\nawe8vWriGXN3c27rbFkFozF84wMpPwWAX7nmGeo6BQxCLmGksJedDK6woEEa\nHRA7QFj0CrAsFnRhi1+BEFqwLHma0a/LTGxuPyQ1n9YJgkI4l942Ntmj98k+\nXTyCaNFXctKahOLtsbMqjWs9HHiWP0jcGdKt6cmqWBKqjp+qHhQpUBq/mBgc\n0p7fSNB0ShkyKqr5xvC2Zq+CEVOAljjAK/MsohgOxsBj8lX7JWHhmthAaPhT\nEKyoUf2Tz+aN8B8swRs0/MRYs9q5puQMwM3fNUHb1ndoqYGNnVjiF7YUzM7/\nPQQQDAY9FC0LEYiBGiKfimtT17LoiYxMbuYAeKUrd1D9C9Qvw+Frc++VVwiH\nDo8V5XsgRBewmTjKK8jQn/BF/0gSzMcwzOtUd4e5sI6AxExiXp+bqeIfzQTF\nQDw1Odz3qc4XcJ6UVJ6yhx8Ig8hATRPU5VzC1xFi22EnIra5SOoB2VF3Tfpr\n0BwOw6y7KQNGUayxcvhx5JVQfb/2QsSt1k+M4/ezJx8Mlv3h9mM/hGPXqGzr\nv99g\r\n=s3CB\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.6.0": {"name": "undici", "version": "4.6.0", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.2.0", "delay": "^5.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "proxy": "^1.0.2", "sinon": "^11.1.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^16.9.1", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^6.2.1", "chai-iterator": "^3.0.2", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "d1dacc067070ff94bb6e609bcb1c7a3ba7a01128", "tarball": "https://registry.npmjs.org/undici/-/undici-4.6.0.tgz", "fileCount": 83, "integrity": "sha512-KjpmnKG8+WfmRlY5X3mEVASUdhxzdy4KdHFXDw+kChco8XaXAttBcPZDCJ8YlC/2WihWx+LYpF+pCJUhN71Ffg==", "signatures": [{"sig": "MEQCIFOCdqZUb0yLbtKZtpEJxuzq7QYSt9zeg74xNN5xTuOxAiB/Xj+iqFGc+2VypjeorhE15Uo6qWQnmLh1ykD5wVLDgw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 610744, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQclpCRA9TVsSAnZWagAAIVoP/Ri21WGgHqbnQhkShonx\n5dYer3Qc47LkFsXOr0JbWegPv/2rqt8CRqT32kRRJ/G8OeEWwcnaP7opdFe3\nwJQGxFmjvENDJWY9r5CPhWCY/ZYWHVXoHoHqQl36aYMx0U9sQOmVikb39ExE\nyoSU6fSEe1z3d4Lr1pdvltP4pG/f4g6V9KOncM49GbrN/pKdsqFc2foprEnK\nc6owNuWCLZS4I4JymGtoUuPVGEhcA1jZATnPrFU8YCsso6PMXU6xT14aHGCO\nc/yJCD2IkTlshgkQO86ozpMQPvzAM04qEFGzs8ShPblXKrOWnW86VzfpQ7Pj\nFga2tapgJfNMgcSEGchiRSVl/Rw71AbZo2c1/frr5wt8Bew6YNDDahSg6Bh2\nYP/so3fS/CWBCIzmZORrqRfLpmHW9dvm5L/RS4Ja9O0HScLhN1FGfUBEl2Qf\nWhtwfDlP46WTMGqnIzZkuA6yiaDMvF8iG5EM/KY3iMmBK284P3UxQ3NEQ9SC\n5Qya3SSYY2ZKlHrDsEu3l5MnwuckQWPM8qbGvw7OO7ujMliv36wjkBPK4YMf\nxbBre+YPpp8Bdzc4wZqtCnuyx/ZcmoxLRCqeYRtk7ec7UfI6FIu1bYpIK5d4\n7hDZFjSDtMyRW1IVEOEoEqSc2dotxfADY+GUuxa3RV5vLBNrRMh99ubFnv4t\nqRfK\r\n=GnWW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.7.0": {"name": "undici", "version": "4.7.0", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.2.0", "delay": "^5.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "proxy": "^1.0.2", "sinon": "^11.1.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^16.9.1", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^6.2.1", "chai-iterator": "^3.0.2", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "3bda286d67bf45d0ab1b94ca6c84e546dcb3b0d4", "tarball": "https://registry.npmjs.org/undici/-/undici-4.7.0.tgz", "fileCount": 83, "integrity": "sha512-O1q+/EIs4g0HnVMH8colei3qODGiYBLpavWYv3kI+JazBBsBIndnZfUqZ2MEfPJ12H9d56yVdwZG1/nV/xcoSQ==", "signatures": [{"sig": "MEYCIQCoS3HBl3AzUcT3RehXdZB43wAxqglM/H/m+Z3b5Q2a0QIhAIiKjjA5fsLHNNlhmoGoAsbqdOarsdmOicxniCZJz04k", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 615834}, "engines": {"node": ">=12.18"}}, "4.7.1": {"name": "undici", "version": "4.7.1", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.2.0", "delay": "^5.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "proxy": "^1.0.2", "sinon": "^11.1.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^16.9.1", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^6.2.1", "chai-iterator": "^3.0.2", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "e3aea016caa174fd0b05a01398f73084f34798d3", "tarball": "https://registry.npmjs.org/undici/-/undici-4.7.1.tgz", "fileCount": 99, "integrity": "sha512-5Vub7YlWErSgv+7UE4yCyqRbKLdP+Xo92AR9XWG5BrQvLSMXLtYZa/RxYwYnsOpaLNHeivswkYKGxZOC3Dfw4g==", "signatures": [{"sig": "MEYCIQD4e1Y03xIL3gS5kIFFUYJsGUu3cZ+5wiswIs4QojH0lQIhANb0XR5uUuKcrDeGUDAbCzZFBTPYcKeQ9uola0CBXRWi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 618619}, "engines": {"node": ">=12.18"}}, "4.7.2": {"name": "undici", "version": "4.7.2", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.2.0", "delay": "^5.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "proxy": "^1.0.2", "sinon": "^11.1.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^16.9.1", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^6.2.1", "chai-iterator": "^3.0.2", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "7889da2a2b5bae03330ca9eb310a66200d9e92d1", "tarball": "https://registry.npmjs.org/undici/-/undici-4.7.2.tgz", "fileCount": 84, "integrity": "sha512-kEW5A5JpmnzjMi09XL++3Y1lKG2rnInJuRIsYHD4DwlJFUX2EFehMBaaeDlSys/ht4nwMhKnnoWEycuRQ8GJPw==", "signatures": [{"sig": "MEUCIQDP4y6FQiqo4G8Mi7avtBnpKa3sGQ2lQAM/v4taYHRDEwIgdlM+eLAkDYSweB9EbSzgDzkPlds57BkBuvDc9mE0kYQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 620199}, "engines": {"node": ">=12.18"}}, "4.7.3": {"name": "undici", "version": "4.7.3", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.2.0", "delay": "^5.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "proxy": "^1.0.2", "sinon": "^11.1.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^16.9.1", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^6.2.1", "chai-iterator": "^3.0.2", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "211c87e8eb40f69a391867c6813bd86786253236", "tarball": "https://registry.npmjs.org/undici/-/undici-4.7.3.tgz", "fileCount": 84, "integrity": "sha512-ecY0KLuZ3EVbiR+Z2kpxh3V3tLGhkxuNv5jhIYskZXO8KgpwcFxqScIMs6JO523VFnfRnN2Fm6yhLxc+BQvezw==", "signatures": [{"sig": "MEUCIAIheWd4LBsNi6pMfQQuklzDqFgLalQ/cz3sCXGMn/CzAiEAtWpFafwHfj5cyb8a7QeKWsqT0KnMD059i+wxuyQP4BY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 620199}, "engines": {"node": ">=12.18"}}, "4.8.0": {"name": "undici", "version": "4.8.0", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.2.0", "delay": "^5.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "proxy": "^1.0.2", "sinon": "^11.1.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^16.9.1", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^6.2.1", "chai-iterator": "^3.0.2", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "17d84f902b210a2ee5edc2f8bcc2df2ed7f1c4a7", "tarball": "https://registry.npmjs.org/undici/-/undici-4.8.0.tgz", "fileCount": 87, "integrity": "sha512-+Jl<PERSON><PERSON>o5bJ9lMtElHMN2LwFtHqg3ovelCsuS6R4vCIJ/Js3NbP4OE3XOLOQjj9pitXJePi2mr12cteLqcbylpxQ==", "signatures": [{"sig": "MEQCIFxTMMCOzzJUYx+wsykHWhNlCiOzQi9JV75ZvQ3F5q5bAiAnvyD3Fs0x1Y3uB8/RL3BrBBA3/pp+Md+zit/Zc1K+aw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 626930}, "engines": {"node": ">=12.18"}}, "4.8.1": {"name": "undici", "version": "4.8.1", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.2.0", "delay": "^5.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "proxy": "^1.0.2", "sinon": "^11.1.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^16.9.1", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^6.2.1", "chai-iterator": "^3.0.2", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "37b6e94e497f7851f3220858acfe04dc48dccd05", "tarball": "https://registry.npmjs.org/undici/-/undici-4.8.1.tgz", "fileCount": 87, "integrity": "sha512-0HYfUmepa5euTv3ewj9OqJDiBrZrBEr4o8HPdNHeWOsPCLLXZSBVovW+D3D1LoFeGXhe5Bm2V/e7YJatUjeWfQ==", "signatures": [{"sig": "MEYCIQDfw1ayJjfcnZdt0ITk2H6mTIcU11sYzHWdK3aBA0wzrwIhAMIJES8qrHxVXH3+IrEyb+Xswvl5S34uxafPDopCpMAX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 626979}, "engines": {"node": ">=12.18"}}, "4.8.2": {"name": "undici", "version": "4.8.2", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.2.0", "delay": "^5.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "proxy": "^1.0.2", "sinon": "^11.1.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^16.9.1", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^6.2.1", "chai-iterator": "^3.0.2", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "a0687ac933084edaec76ee7a07a14ecd0da9daa1", "tarball": "https://registry.npmjs.org/undici/-/undici-4.8.2.tgz", "fileCount": 105, "integrity": "sha512-wSQI+QbuPZ8ZRCGZ9fs+h2+J4tLaaM5j/28Xq/Q/zAOSy8/QGkHpszpVGSwpgb4ZyQ34rAIEd5fMKf8PGQaiUg==", "signatures": [{"sig": "MEUCIQDmDly6JQ71sOEIkCt8QsU5RP1XvVvouet3QU9WzyDQngIgT/DC6Uulm3Hmk54topwQqE89iYf4WgHhl2CNDAeYhHM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 631576}, "engines": {"node": ">=12.18"}}, "4.9.0": {"name": "undici", "version": "4.9.0", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.2.0", "delay": "^5.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "proxy": "^1.0.2", "sinon": "^11.1.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^16.9.1", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^6.2.1", "chai-iterator": "^3.0.2", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "ce783d244dad2d2f8720e0c80c4562139a271ce7", "tarball": "https://registry.npmjs.org/undici/-/undici-4.9.0.tgz", "fileCount": 90, "integrity": "sha512-J7V96AAPPwvdukl10dLNfz2x47j8C80Tg74t8PMR1FF6xrrthbJ7XQiCNXW8c7Hzv/aMudP9Kx8LshD4ZhpQww==", "signatures": [{"sig": "MEYCIQCAPiBSyCa1CQ1gqonQY7ELezKPsjIZJeooOpIL7SFnEwIhAKm47VW968UtV7UVMtdVW7ReJ+As9JlfqOMvpMRaLimw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 631580}, "engines": {"node": ">=12.18"}}, "4.9.1": {"name": "undici", "version": "4.9.1", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.2.0", "delay": "^5.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "proxy": "^1.0.2", "sinon": "^11.1.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^16.9.1", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^6.2.1", "chai-iterator": "^3.0.2", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "bfe8db72b108b45fb554223056b110f3bf52906a", "tarball": "https://registry.npmjs.org/undici/-/undici-4.9.1.tgz", "fileCount": 90, "integrity": "sha512-2idBVoUuxp5ByT15DDs3BjmU278D9gr6gDh3tNwOMw8TiHfueq+qHdhHHj8vNIi1UTTmn1cxL2VF3ONry+eS2w==", "signatures": [{"sig": "MEUCIG7g1JyTYeaIIxubRR0gb1DDlAKd0+6mi/DfhS/sASx+AiEAqWJd2zWww8tkXZVYqw89kwXTFTPXtHWKMfdESAwA7DE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 632207}, "engines": {"node": ">=12.18"}}, "4.9.2": {"name": "undici", "version": "4.9.2", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.2.0", "delay": "^5.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "proxy": "^1.0.2", "sinon": "^11.1.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^16.9.1", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^6.2.1", "chai-iterator": "^3.0.2", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "d562d63e4641bdf2b84286e332be6decb4fa4bf1", "tarball": "https://registry.npmjs.org/undici/-/undici-4.9.2.tgz", "fileCount": 90, "integrity": "sha512-O8D4fp0cT7/bOMe7XE2oc0PaBCyc0GsahzQ+nMSeWW0o0+EB2ykXYNhU0kY2nKP589zzS91pLj979NtJrBRwFQ==", "signatures": [{"sig": "MEYCIQDdJrEW65seoZfP7XrqYUG7fBN4C+sTYivGgkLWuHS9IQIhAM2u1giSqvBUTIv/ralFzMAuNqUWBjNwwLgYW8lyyDAH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 632164}, "engines": {"node": ">=12.18"}}, "4.9.3": {"name": "undici", "version": "4.9.3", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.2.0", "delay": "^5.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "proxy": "^1.0.2", "sinon": "^11.1.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^16.9.1", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^6.2.1", "chai-iterator": "^3.0.2", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "c3c09bf3437976f714b3c123058a74d40d03bc3f", "tarball": "https://registry.npmjs.org/undici/-/undici-4.9.3.tgz", "fileCount": 90, "integrity": "sha512-9WbSy4Y2At64gXTOclOjkEgkVrQj09DoH3sgyUYP6e8XCMU/VN8UbjIl+Syd084JgLnlYslgJ0jFM+3hx1ClvQ==", "signatures": [{"sig": "MEUCIBdcADB0POYEeP8HCeHpPJIMBN4Uw46lLCuWsqyDu392AiEA0lH6roqznOEzFOr/g4cPebhMlrc33DfiuAJ6IAmgeNM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 631965}, "engines": {"node": ">=12.18"}}, "4.9.4": {"name": "undici", "version": "4.9.4", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.2.0", "delay": "^5.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "proxy": "^1.0.2", "sinon": "^11.1.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^16.9.1", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^6.2.1", "chai-iterator": "^3.0.2", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "0c22fc33723f773fc05920188cf38b4c7a582c86", "tarball": "https://registry.npmjs.org/undici/-/undici-4.9.4.tgz", "fileCount": 90, "integrity": "sha512-hX/UJnbWu44gBKp9EFwH6fzAVb/QdkSevEu3bCjZWnVN5kPfyQFa7QxRMC+Tqj4LlNbMdn4r/Ly8/QlZ3kksWA==", "signatures": [{"sig": "MEUCIHncFsPaZbkJFUy72/JbBvWRi1G9t9LzAoHbpksfC7Z3AiEAkr2sqptOg1GwDF4lZUxH2ZIMUWNPaUiY+WUUR8lmzr4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 631997}, "engines": {"node": ">=12.18"}}, "4.9.5": {"name": "undici", "version": "4.9.5", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.2.0", "delay": "^5.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "proxy": "^1.0.2", "sinon": "^11.1.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^16.9.1", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^6.2.1", "chai-iterator": "^3.0.2", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "6531b6b2587c2c42d77c0dded83d058a328775f8", "tarball": "https://registry.npmjs.org/undici/-/undici-4.9.5.tgz", "fileCount": 90, "integrity": "sha512-t59IFVYiMnFThboJL9izqwsDEfSbZDPZ/8iCYBCkEFLy63x9m4YaNt0E+r5+X993syC9M0W/ksusZC9YuAamMg==", "signatures": [{"sig": "MEUCIQC99tdiQ5rNU9S1tAhqifd86+XJgYJa3bfEgkIO9Z6iqwIgQmdSKux7LfTNHttDXlSqbMxgxOAWgS6VqphPFFgvOFk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 632048}, "engines": {"node": ">=12.18"}}, "4.10.0": {"name": "undici", "version": "4.10.0", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.2.0", "delay": "^5.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "proxy": "^1.0.2", "sinon": "^11.1.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^16.9.1", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^6.2.1", "chai-iterator": "^3.0.2", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "f2684de9cbe2ab0a85a477ce0ec59b739be4438d", "tarball": "https://registry.npmjs.org/undici/-/undici-4.10.0.tgz", "fileCount": 90, "integrity": "sha512-W7NwyQzfiSYIZ+maOvz/u9tPFyAYhstF9XhtohK7txvD1jg41oatcHVq6Zls81LhzGvh4BN5WpQGuMiT4IipoA==", "signatures": [{"sig": "MEUCIQC2hFWVcrrnO+RoP6Cp2BYNioSLCzbqi4zVo/0Z2/aIzQIgBQs4VSlu2B2P/K8y1EbA6QkiWM4ld5d7DoWVXQaNLjE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 633664}, "engines": {"node": ">=12.18"}}, "4.10.1": {"name": "undici", "version": "4.10.1", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.2.0", "delay": "^5.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "proxy": "^1.0.2", "sinon": "^11.1.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^16.9.1", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^6.2.1", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "cec2a30cdd770b03b35247f42b7b580da5fba581", "tarball": "https://registry.npmjs.org/undici/-/undici-4.10.1.tgz", "fileCount": 90, "integrity": "sha512-0OFdslhVPCD+vy4+zcvcz+RzV9mBCsBf9ijxr8lCv10M8X4rnAaepXTzv+AfB/9OI5z4uXzkf8L7mB8MhJW8oQ==", "signatures": [{"sig": "MEUCICVy0mfq8gHrX0b0VOQmbOwXchNpTAy4LYtefqaEnICpAiEAtXHsqwyYaJWH3Wf6yQvlpWvbmXBrXgh+0KGyqW8bTJc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 634151, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhl29ECRA9TVsSAnZWagAAWl0P/Ar3mmcvm9npv+TmBS9P\nnqlY1N+gZJ+XVR6u64WsOFvC/UMKr8EPI64tQcK998I55kEUcuOjtbk6SnZJ\n78dubNRpYyCBtxnLDTBYwHtTHNcF0Qjf51ueC3PQY3ZpLTn5J7AnIdmFrqVO\nYaajl4jw30SV9wTAgDTyBBA+l4WcLP2r+ztZbMDcLHld5FVVQgcZoT7jLZk9\n0DIlUZMay7x6LjwViH7Rq9oYRy3PKqEpeygZbawqirs4xEp2YZgeUyp+wqWL\nNaM9PN4QpaXZcG2WZi++uPsRP/MGg9mweH+2atU6thSHXAhi2HhGqE2joyKN\nMTll9LZnyPxwmshnP1hNf6VUk5sAu3sH6+3GKPT9GJJVk67VHo7UnopGNieV\nG5wzT1irDfcLm1wp2jldglWMyf//uK68aHon+TZHGUmaiD4DgHE8WiwJOdwF\ncE1n0j0qJXVDwAnVjWUDV1SDRc1oJobjKqx2hx/DLvtByiB3iRFHDsSvDlnM\nuwJwEV+cxYr3epmQV/5uBAgMOoN3nC5uvqhCt6QivmYNAZSfE5sUfmggO9tC\nXtxrMRUq02Yd90WIwo9k2jJ+dm8ctzT+IxOK8HmfYsWAMUizvgx2UEJXoRMS\nO/t6DrdM3EB2KaogyMjTCArHEeIjELC8FeEr/A1FKhUqZ2qkux0cMuhc3Vb6\nqgve\r\n=8WtE\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.10.2": {"name": "undici", "version": "4.10.2", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.2.0", "delay": "^5.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "proxy": "^1.0.2", "sinon": "^11.1.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^16.9.1", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^6.2.1", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "27e360f2d4202ef98dfc1c8e13dcd329660a6d7c", "tarball": "https://registry.npmjs.org/undici/-/undici-4.10.2.tgz", "fileCount": 90, "integrity": "sha512-QoQH4PpV3dqJwr4h1HazggbB4f5CBknvYANjI9hxXCml+AAzLoh4HBkce0Jc0wW/pmVbrus8Gfeo8QounE+/9g==", "signatures": [{"sig": "MEUCIQCw7kO5/9Vjde1kAyamnti6eylGDrKvSAU/U6LGEkt3tAIgYGzRIf0jidVsVuPN85Y3bBw8Y3zqUf3NsmvmwVSdTkQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 634186, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhl+LnCRA9TVsSAnZWagAA0w0QAJfR0dHh1NhgJc4atV/D\nIQR1ePe+bCGHvwR6nuQIXlCLHGKEpoO1xfP7aemudevKrSRud0/5PLOWJ3Fv\njt6+Od0PZdxYIaAw9wGtXe6OBDXvotzQboRN3GH7QtsNe3+nxTz5SgxAksHC\nwedxZtgpIM4c+/VqFs7E9Qxl6+PzmtmjkITyqFf2pTLj1kUzV6ebPQAeVS4F\nP9u59wFBPNjEXS/tofyVcdHJm7LzpqgoVAOjmg7XWfldVkH3Atojp/lAj29h\nUOmKamZwq+IhP7E9iO8T4q0Log58VMkKz6e/yEHQa4uf+c+7pQEDjA9RpGsx\nQEpaq2tftot6AAtUxSEWKF3h+HldePzc+utOUsr/ls9oTJKqA0LdsF6Mx4xx\nEACiHdIP+FSCtXIRGw4j4WMDe6sqrkeaZX2gL0wbTYUqu92SnaYZt3smvCk1\ndS2s32BSxjD0Mw/M6OGUq2rQC8Jex/8JyypMFCn9qQWlhMGH0PlIsbWExtD9\nhDT1jVRBjPzFT4cmTr305skCACBSOwQWbw7IU/kxVCZeoPu658Ji5dkY4F26\nU9YehG+sWQRuG/GrbBUt2CuwC2KQUzTIzdBeLmJ9HiRwd9Hw9YbPCSbK+sVy\nKQeiY8aogyW6vntQc5sqhKBx07OgN8hOS9VEPl8TI/EwBkm4fURcMZroRAMG\nX2rg\r\n=WdvW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.10.3": {"name": "undici", "version": "4.10.3", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.2.0", "delay": "^5.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "proxy": "^1.0.2", "sinon": "^11.1.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^16.9.1", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^6.2.1", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "7da6155025e9dbd676e3c7b72c32efef1ca3b3f4", "tarball": "https://registry.npmjs.org/undici/-/undici-4.10.3.tgz", "fileCount": 90, "integrity": "sha512-oMfhoSsFdu7ft+10gBpQ98gfIGT6qovXXRxYPOe07xfUJwpVTcFs0xvuAEpNqtObhf4HQWuMW5kWzaD768YS4Q==", "signatures": [{"sig": "MEYCIQCZuuvhKv0Qm5yIrp+mj+ocpm0YNXJ2Ncv8p2LCy4FzDgIhAO48fQ/ydpFljZTXLy3+XlUdyRA0Cw4z5zXKTON+GkW3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 639169, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhnggVCRA9TVsSAnZWagAASL0QAJTw+zZUK8RMIk8BTyeW\nFEQS3SNFqxoDNROB4Ao0TtYmrqlBG1oEYVZP/Ik6mWCkeEPK9R7wD+EeAnni\na0XhrVxHCWfDVCGziBknSsEGyUwKXOsZ+Aote5bxACT8uM+lG5E8t7EvEMxw\nkGHg8mHZ8rY20qmxjCMc+vktHT9w0PNmsdraGSy0zIiLKv6+gUdml4UapeaQ\nFgvYBn+bBd7KL1cPSsXIbVFZA21qlkQEwlQ/bQpTgaWgCoqoNgRNZkoeDLoq\nht640BiDOol8n9Hv2a8Zoe5Dz/0ZQ49SDk76pVDDuvW+ejKwmEd8Mek6X5iT\nHgC74IeGyEMrbFZ/yGHRUXe0SeM2uA91K7HCbMswOJmDJMG4xcv48QCJkA+5\nFGB1qGKtCMoSD6pDwnRQBNlvUlsdQRP0zk8ip88o6gpoGMAgYdtjF5DEMfAX\nsaomv4J0uxqYimB6a9JKHkZsol557s67d9BVYykaqyHQN1mpJplbxbwlo5cs\nq2cWC5sMnbiO3hsH6Npv+tJJ++Cn9Vdv6ZqycwuYvUb0EalBh1uV+4Y+doV8\nQxyyDrdCvKMKq6UVyRGLkn8bAMNMlLcXfGS+ZdNPLZ30eAgBif6vO91sViCB\n1OGVYeMNhUZzLrwwb06tUU1PCE7HK0ycF2p2CFvql5iy+KNZsvMZk7d6oROE\nT1ui\r\n=x6ns\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.10.4": {"name": "undici", "version": "4.10.4", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.2.0", "delay": "^5.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "proxy": "^1.0.2", "sinon": "^11.1.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^16.9.1", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^6.2.1", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "e1991c6e9415b984e001344cbf872d577710c648", "tarball": "https://registry.npmjs.org/undici/-/undici-4.10.4.tgz", "fileCount": 91, "integrity": "sha512-hpT3oKnZOZfOav5Gk+MzGPuiiQGtNPcMhZo6lf8+NCI8VNxMF/YakziTqhB0gij5m9LSEmNc5y+6vbeVu6ZhVA==", "signatures": [{"sig": "MEYCIQCgaDif45W8COyS2dbd85lMSPCE+k2nR0G6lqHJPjtGcAIhAIdrJJOQwNzSmeoOhyiLtRogcvneDRnq3mZjRZjU3o/f", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 639909, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhp9d1CRA9TVsSAnZWagAAbOwP/RQ1dcWLJKn4eqVPQiHB\npDdism5WIinqxu+QIuIw4RpYEPbZdd1BNOwL8eO8iyW8U9I8TmV3D8WHsyfs\nT/LoZKAcwELtNC1BioBiG9Sw95doaaQmRyt+KP9stpZP5i2bDE2Ecpjvx63V\nqNie4JsHPWK+VuUjZewIJEboND3i1ihbuIA9Y/JhwaAOw2onQwg8EjSmZWZa\nGPk7OzPeWVK6tfV/VLuopW4xHykRopNAJQMYh3xODin20Yq4PNFia2p+10t3\nIN+SFB/mOMDhrcu2v7MhlugiqGT8hoNY8/VIxKvDJJtquWqawnC3dcAKXo9d\nLxko1OOLSdvhjmNYFnl/4AW8NwciyIZNKNC1Q+GHsl1zyPxh8YhRHLZUB4Tw\n9xpNxT2CEx1KetGiK6/m3HhqjY+pF5J8qY7blKWuC010oVoeGo8yFZSv4d/B\nmIZjavS2TSVxNvV6tl/qDFAcs9m2txpXCwXm94egz7d1BPnp4G4FPjOx6aRH\nJjthUVzNRp9FaoGoiOpVJpitaFjB3V6bVpFuVKHOOsUjzY/sQ6kZ5GshKRX4\ncUT2JxiHh/cjwI4aThNCQMeTH9Y5WRdZdYwF18NjlpDDAUCNqINILHVcpGGZ\nhgHD2tjqbpI1HY9NlMQdC8wJLL4kodQwqAbBuEsYDl2PRD+Y87FqZ+onANxe\nHJGW\r\n=0kpE\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.11.0": {"name": "undici", "version": "4.11.0", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.2.0", "delay": "^5.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "proxy": "^1.0.2", "sinon": "^11.1.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^16.9.1", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^6.2.1", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "41fb4f944704d77e1c9fb472d40d2dbece64ccf2", "tarball": "https://registry.npmjs.org/undici/-/undici-4.11.0.tgz", "fileCount": 91, "integrity": "sha512-gofXRqAdm81rzaZgPbMf98qvrNGd3ptJ26+mCcF3EXoC817p//MtL8XcDpTvHUXxdW27rAM2jvTae+KyAchorw==", "signatures": [{"sig": "MEQCIFg79HYlCwVQuDRwMv3LSSNHNJhZCIdky9WNH9KlCEXQAiAyOORioD+4+6tUv35tCcLOUjeppHl8SwLGGta/R6mnOA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 640407, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqdcpCRA9TVsSAnZWagAACqkQAIZuA9G84XS6asaFveYO\nPuxLlVENtiolfDdwLs7Zn+tCxN1KWGMzmGerEbfmZrX2gSOgNVHnOHZQUM5o\nB8euTlSrn3YgL8Qm6k3NQIOBeTdNGhTREc8IH2EE2LVEdgs74gO2u/Nu2OFw\nGAa9morBvLNPl+jDWBGUwMmlUpu6KA2A4gfDA8dxAwqKyYXG8Vv7d01wAG7r\nCNSFElt2hRsug8btKfsVOhIB+hWsMsQb9BzOEZTUfkJTZmxcmIT/1bzIHL+e\nb7ARxidng/uhCkMhddGTHIrLW/DkjxKlXXrztcF1reZZoMh2dsAwG16yTeZS\n46sCknR1QAZZqezZmuW1QElyDOR8NUO9AL5Yr2fE97PfA8Bp0rksQMObD/bh\noJ7rMEzyPaNNenAhpzWpClSEvpKO+SdACEmjj1wIT1jKXNhPHKy8vXUKBW4g\nWBcsFB5Ae/AssXnnd8nh41JvyoTSN/LKMgkC3WnZHv3boeQbDnKgQIDv/Bee\ny12UFZ2p98xqQw6Gb8H1aI4H+klchgOGmPQXqIq21GA6YRLHW3vyAZXYKlUr\ndCplcoLLx0Rdh9gHl4TMjW+f393EdLpP7/ZMgky5mWoJRKv7DXMZfljrcsBy\nUcmvA6WZNgtB+xKi/K2JFKHaVoP5+SFr49R+G+WROI78te+gO3bYwVi/42ki\nmUsc\r\n=RC9Y\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.11.1": {"name": "undici", "version": "4.11.1", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.2.0", "delay": "^5.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "proxy": "^1.0.2", "sinon": "^11.1.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^16.9.1", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^6.2.1", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "36c429d5aee3cdc9d5f3788ef9c1666efca5c45b", "tarball": "https://registry.npmjs.org/undici/-/undici-4.11.1.tgz", "fileCount": 106, "integrity": "sha512-7oP3g5QpnPVz8qSwEygDkx7RH4TrGiThOiPooWiNAXrfUyH4jDmXVq9MidwwKV5YlpkZW66s10VoC8t2SvmPBQ==", "signatures": [{"sig": "MEUCIQD4jGVwu13H1UhURmtDw6Gh5wWVM3x4x8xYTQdXTL6EDwIgTv9EH+n3GjGqX2PkgzzSy0ZPGpqWca10cNAco0RXduw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 640399, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhr2aACRA9TVsSAnZWagAAmHIP/1YO37vx+7EUW0wX2YiV\nB+QihRE63Yq0Pwl0fpeAulPxlaqZGP3tRrtTco5req17SUigHj/m3Wwmkhyh\n1KbmADjfIog2Stt37EaRs6NCVsv2pNXKhTDNeNZVAyEwRpxjzk24IwmNcML+\nAJO5lmnKn2jZHKuvUx6mA1F3slJnD5eO6zI0d26FUDcGEBsErHZYF+fwSw3Q\nCxo9Kde6b49cNZmIM6DFH+zK1nuaUUhmr08t4/lxDGmD0EmUgpXivA6xOp7N\nl/M0+Qnxk/e9aljiiTO3UnXW+AqM61BByZKWKTCuqa5wtUVMnnBIYooy/RA2\nAb2j5K4dWzILQ0tjx2KljHBbVQqKUMPBh9lGKKiuJVfi28AGaFmLbRMui2Zu\n2OQ2ObmY1wsWQuj5loFWCwX9hycdGiX45YvQMQuXdvqTf4t6WJjBCsO5OqfX\n/UGx1kFOXIy0QQwhMk5oMYnGZYRLkM6eUefH2tdz/PonMb0CQkAMvWfZU1ha\nubpb417RpPNkgdrOFhpYXc01xN4aQCGNCh921GQLn2LIjP8Hp4ZNU+MRYENH\n+lqXpkA+n03HFX0cOLRnvmi1y3kTnjmkRF0jdkwwNAMPVDtqMsbMgJP2HWdp\nO7L07Cc3dYOQ7xGi/hCOHSvH0942BG5/n+igLPZMG8fhVrIBJ/JHfxK3I3qd\nGMXI\r\n=KZx6\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.11.2": {"name": "undici", "version": "4.11.2", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.2.0", "delay": "^5.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "proxy": "^1.0.2", "sinon": "^11.1.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^16.9.1", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^6.2.1", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "4aae7a4349bf0768c341dc74f78bf4e05123baeb", "tarball": "https://registry.npmjs.org/undici/-/undici-4.11.2.tgz", "fileCount": 106, "integrity": "sha512-u7GoXiNhFrjdS46w9Wa5ri/Be9LDQ3MJ88A+AT7kN5wZvSLEV4YuDmDIiVcA5YUfa6X33Qo6DK9F0xuTSRgE3A==", "signatures": [{"sig": "MEYCIQD26p8u6ML/jkU9mmZKAD/6O8WjNoTnHLKKTuojX9xIWwIhAMUxijBhZokHV7NT8qGwoBEfAS2HXuT58FnhtyIR1SCp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 640732, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhsNc+CRA9TVsSAnZWagAAlpIP/2KWIlVPFVk/Tq/NqJH8\nV//RRdPeT/XAAg3C2NZzoJjPQBPsRUJSt+nlcnwiDk+D0yqCd7EttYj7Bb/3\nemnBLecPtNckoEJfDg+ai8jXhQcN6Ng+57JV9gjN6vKpfKizqplJArMcGlHe\nt094oWTyc9JNypEWAyXHfgsZ/ClP+paC0b9rxE1lOGkykSO+9/8T/JCXT6At\nls/R2fpabTnfCHGpPPv3F80H3XYdnIsrWAA9kOR7R7/4Sxu1cpFneq7OCUBG\nn9Uk2nZ8IzuOLTwIGv7UPxikMTT5/KGT0FTgRpxKkpaw7rZbM1TwpS9d7Zh+\n39thZkcJaZVnZWN9TTe5u3FD6oVU9br9W5B1nu5yZDTU/Ps0ylHf91jRiX30\nkHn5O8yAbuvIsS8/W0i6j4DtbMGGULrTu0iUWbxzWvvOmBVtGsyYGl99Rlb5\nCbdjbVro9Widw/MWWHQwfxQdlwuhI9wHO7p3MHSskgU1seivWV18Mrjkp87K\nCNkL37eRfmLSfDmC9Q6MRShxfLg+My/icw+sZ/vqBB5yiKLYdl1tA0nKUahq\nrmjG3JjEnZ8jwzeKy1ybHS19WHiVJIpxFrZnLxJuSLyZ548EcjaU2h6X9bCc\nnRdm2UT9V/PQiXITv6AzQVxPtmudVCc/g1O7zHONNTbzV3q4LK+Z/yricVJL\ncqxZ\r\n=CI1/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.11.3": {"name": "undici", "version": "4.11.3", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.2.0", "delay": "^5.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "proxy": "^1.0.2", "sinon": "^11.1.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^16.9.1", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^6.2.1", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "c0b2423ee988b159331acc6880483906ff003817", "tarball": "https://registry.npmjs.org/undici/-/undici-4.11.3.tgz", "fileCount": 91, "integrity": "sha512-bLOCH2juB9gAkqE4a4zb+eMGhI8XD4SH1tF9Qu9J3Y8TA379Xxg7lWRMiDvc36pRWV1z9/HICv05fXkeeLw1Dg==", "signatures": [{"sig": "MEUCID5GYBWGpiIfseEm/i+FjK2049UYcY3aoSFsk/Os7lQ1AiEAtKLqYrHaokqk9loFvLOLvwNkwY4tmDJUfayEM3vuVhY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 640257, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhsQe+CRA9TVsSAnZWagAANNMP/i6N4FQcpmm20mUL0j0j\nBRdGvC25YhMDtTmRtMxYu/nibRwhmPbUKKvTO578KwNRpasoHBY/4becji/L\n8+bxDbyc4xbSFrpnPwereuzID9Vl/PzMNrQv0zxVrNH8D4wUxdlDvDPu7udn\nxET5lMqxP/ConPORGiw40y6mpZWE6qggDnrgB2rTG/p9mh8xxDDWaxItY4bv\n/UH/AHEoHeauTIUXlx8zez0aVI0kzTUAEV4FEj5PeOhJoCHMGztUHEMFJu4R\nu6zsUoo5oPDH2tStdbca4SLXWpPYThGAxaa9+uzbIMxj4ciu81ih8Xruu4JX\nZ40+Ktd+QfcVFDoeBIIc+fOjiSxiIe+jUFJqCnSWRK5wlheYVKPQU5Kltl6T\nvzMv2NJO1oASQDm8me4LC01uzL5ZC7v2V0EXICRpSZG75MAFXAPnDwdyHPjW\nGqYWcef/GHw/eGbUBbW1zJtQSataKJ3qRHzudmmgMEqBl+RkABmRGAtyoKwq\nVY6/YrCOzpNgN9/P+B4vB3MDkcFLtil2/q5emzt/V6DzJoT0zn84WTNZ2TZW\nf2/xU+mNLluNA28w+c9/OmNrhYqp20zKKxrml6THNOtpsQy7GXnWJqiLl2u3\njwF4osT24sjuoOj4rmBH/g+FASDREWYnIfYl7+perElyYGwv4W2JWtmTUcub\nY3yG\r\n=jw/8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.12.0": {"name": "undici", "version": "4.12.0", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.2.0", "delay": "^5.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "proxy": "^1.0.2", "sinon": "^11.1.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^16.9.1", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^6.2.1", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "89dfd99f0257c89a971b933b195188747864c730", "tarball": "https://registry.npmjs.org/undici/-/undici-4.12.0.tgz", "fileCount": 91, "integrity": "sha512-sJ4CyO3ZPaoxWpLQTJpH/gWD+tCIra2OJ9UPvrX1siyJkgh8NOAybRejJ/g2xHyOdAuoSE0lPRJwRl8AZSXYJQ==", "signatures": [{"sig": "MEQCIDRgngKoFx7qv3RyU9xRH8GL3xVcVjsfN0/jT4tZeQofAiBImENEaAumbbk4rwrznG/fti23F7nvsAWaqhWIFv91Dg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 642384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuEv9CRA9TVsSAnZWagAA7H4P/j6Ney/hvRrBZlm2DwnV\nmph3OFXg69RL3JBpy8ZN1/tih4wUNTX5PHRBMTTUx//UgJ1eAn+pO7wFvPm2\nl4lSjlj3/UVM+QplQ5YLvWhSm4PtGfMJXoeaDhxzlxgQO8YlzNJN4tjRzUjr\nah3XwsbilM9yuSaNB8MOBc3oYc0DtoTrLvuQRnW1XIji67wr8+PK3XeM5B2C\nVXeQ+Oo0nUWZCoiBEFolyfWbk8EtZSAqEPOPrPuBVrX7IrJzCskkW3Lo+soD\nqGKkRaMZ7WED84EDboZRf77cEe+zegABwtuaNBt2Ql6Ti34n/2xXU7i+oiRd\nyQhr58nU82SPyTUJqi7+MF5WKdfd+2HHjrOzlL0CJc4dxCksF2tBe/C9WqUn\nQv9dvqeViUEcfPEHMaApfwdJRJ9yTUB1lzXuwto7zYIVx3bHNCdH5Ul6FJah\nziBMJpysI19Bm1IGweLJVECdcbEjsmYVE13GCWK4g3jjHGhGyf/RFD8zNGxO\namknnDZqRpxD+wOeWlv8qxrX4wCE2Qdq7BJ5hUTXnrlcC8VmlZDhqZycR89V\n0p2J/3rDJXpym6LcyEsACLwQs8Onp8jX6+m9sPh0lXEdth6cvVT/ULA+OTMv\nZDJnyT6Hc2b60RWGRq2B0LWZCSiaaNa42/hrIH9ZywlzKuAp2pvbfqDGZXsl\nh27q\r\n=Va3h\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.12.1": {"name": "undici", "version": "4.12.1", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.2.0", "delay": "^5.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "proxy": "^1.0.2", "sinon": "^11.1.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^16.9.1", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^6.2.1", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "3a7b5fb12f835a96a65397dd94578464b08d1c27", "tarball": "https://registry.npmjs.org/undici/-/undici-4.12.1.tgz", "fileCount": 91, "integrity": "sha512-MSfap7YiQJqTPP12C11PFRs9raZuVicDbwsZHTjB0a8+SsCqt7KdUis54f373yf7ZFhJzAkGJLaKm0202OIxHg==", "signatures": [{"sig": "MEUCIQDjNoc797qKwUOZfasaDILYwKz4e24ExC0eR7zlpPwakwIgTWcYoDxbMAQC8xag/Kw5JhUtqTgOSPwVRVcxv670rMI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 642634, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhwwHoCRA9TVsSAnZWagAA+10P/24ZlHbjPHjocTo+ZsJC\n2fp3JbLHk5OHnnFggTtWizaAKsqKZEHFHbyntveKrl8noJqJ6WvtwRibtnhZ\nkAY6TOvUC6K0LnibLg44E630FaDG0CxZykPDUtVZ3KKP70mRdm409b8QBlrR\ntbvciHQwblWQPzrnCCKtwgDhOo8wJaEQeAtBLBOAX8S7hBsA7GWppH+aca9q\nRAficEC+xByctFyiMV9ijocABUiciwpJCLHDhUtywJBrYxOhEaKWWQhqRWTX\niBaMdhEhkuL4z1bp6dBgUrJ9q9JJvKA8aeYJds3d5O2yEcmG1G+Et6hI5men\nYqbTji0i2+JXyBF5m+V5zL61sFu2+fRkmZW6DwRYTaCw7mHHVbDBERqoAs4l\nLDwNG+qUWVHy3yc2X7G14NyBgnBqqM/ZO6M0VGhBJWZ92d02/NWKwNb9Noge\nHE5K/leoNPayZx/I7tPQ9V1euqs6KPqVcJ0m9fmmfpN4dB0sB2zzrO/9L/bh\nrJ22/kFlEqG90Z+zkQh8cZ9MaPb4bPkvpitd5TjWv9aD8iW3VbIsAkTTMT9X\ny56c80F2nbhVGE8NDfsPvYo0nRdpwLvtyZM1g+pvvdDXc9GrZZ0TlnViDQ7a\n9PzjeaI5NmAfR05BfbRo093AVxOi/NA19S198Y82V/zZwWQ8Y5Mu0wlI9c01\n0AES\r\n=eCu4\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.12.2": {"name": "undici", "version": "4.12.2", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.2.0", "delay": "^5.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "proxy": "^1.0.2", "sinon": "^11.1.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^16.9.1", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^6.2.1", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "f2fc50ca77a774ed8c0e7067c9361ee18a2f422b", "tarball": "https://registry.npmjs.org/undici/-/undici-4.12.2.tgz", "fileCount": 91, "integrity": "sha512-RZj6SbkQFs5O/pJCboGEo6l5DTCe3Zg4r/8Z/0/2qnIv08+s6zL4akohOPMYWKc3mzwv15WTvsfMWaafZcvYoQ==", "signatures": [{"sig": "MEUCIQD1MNOvrXkA3TSAwpeWhCTYsLhuDrUabpqrpyThVeilVQIgSMBi7fayPJC/0E/Vem2JSzuumUz/IdGhVTktuBS1/UA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 643534, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4DYmCRA9TVsSAnZWagAAOZMP/iZTwO0hSF8UbKzRC23h\nC5rvv/2SpLXKkyw9KOvPXuBm9miV8+2H3xOrp3ymBOEA1gHzbYHuVrP2XqJ9\nM/4uM++pKROB1IAG3z7I/WGTBRpMWpItIWLf34VjAbt3dYU+2ErMnztPriw8\ns/H/vSRxbAbNnxT2a5YdB6HCBYqTU0vBCpSxmJUKAa0sTY324mV1AdT+AgUK\njtJFOhZTSNZZpPgI0uDr/W9x6yLSLufy13QrW8SAY8VL+eyrkqipfOU8dexI\nmhUPefzfulwX4uw5W1JO5oQyhTBb17qWOxo2fSB93x06J75EsR2x73dNltaB\nkYMuQ6WakPBJMd818cWq6T3iLmfkuKJNiKOSGOsFPy+LLf0YU/UVy0jTNo1u\nxnY/ci00fERMDMQJiTtlYhlgFD3GGyi7Fex16IiCQtEU0Xx5TLWOBYz8B9qH\nPOXSQma2ZIe/OS7LzfaH65Y89ujSW78LJVs8y5gDi7iv4TdmbKRbtD+vI8mo\n+tLOl0JFZl2Bm8h4eEckLixD5hVYg+VhZz+70X3f3Us1jSWdBOXrHMdnHKir\nlMAw+6EEQ6ATeBw7XRlgUousPXGoh223cWMh651UHUseSWcy767ZGcVqVBRP\ntBTFxj4Ar3DfshQtby93cElU1p0Ngb5KIwiFOz8/xOuYFGBdf6K356MfF3Sk\nZaxP\r\n=0Fsh\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.13.0": {"name": "undici", "version": "4.13.0", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.2.0", "delay": "^5.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "proxy": "^1.0.2", "sinon": "^11.1.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "esbuild": "^0.14.14", "wait-on": "^6.0.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^16.9.1", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^6.2.1", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "7d10fe150c3241a6b3b0eba80eff59c9fb40f359", "tarball": "https://registry.npmjs.org/undici/-/undici-4.13.0.tgz", "fileCount": 94, "integrity": "sha512-8lk8S/f2V0VUNGf2scU2b+KI2JSzEQLdCyRNRF3XmHu+5jectlSDaPSBCXAHFaUlt1rzngzOBVDgJS9/Gue/KA==", "signatures": [{"sig": "MEYCIQCKA23dNylaGFPAonmBj6/dm1iTUBBNFNe6JjTHbEvfaQIhAKBxfVO9Wmkr192OybHOPriKSIdDLAyHcDFNs/jASKKL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 784427, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh9nL4CRA9TVsSAnZWagAA6qYP/1rJv+9IvlZqe8oGKpKR\nlqjsjrjbQ0BltBa5hFS4oBja53D0X/61u28NL3LfrWU74Z97zxYdDzywwsL3\n3pw3GP/S6QH7QvjyiDE9TFWQTxa+Z/3NnIiePeviC1Ng9t0mTs8OeNQ0VorG\nBSgPCUUQ4JfCGT5W23jQYwIOZ1BRo7suWZyjmabbuvZWuiRNzkXs7/s583ZH\n+flrrW3D5KCJO9oA4YCm2S42COpheyUyc1F+eXRbbye24fAhBd/SrktaZdfK\n4jnDmve8mSQsI+oqyWBQsF9F1SMblIS0amyAh8XtoBjjb/vDXtBwySNTNJj7\n83NwP44ehlpHfmQrGDuo7hV0/lC2X6Q4zmCiLkXPZSbFD5WsgrDmUAHwg8yU\nswaplJXYCK0kLTmoCwzg6WWgI+HAtm4SmtkgmmvZrP8gc94XZyN6Oveyx9aj\nz8lVCWg1DxV3WE01r8MOdDYXVdAKUZH7PULf7PTElcAXZLGe5QsZWr7zW5Ip\n5shZ1SLbeWHZMWfYaEYQ70or+B1xoPsZUrYjxova/4vAJx181IreDlOVBICO\n6Z7jSkjHsVgakJc466HdyulghcQGGSrl15N9AstxqXd1vEScaEOp4P/59MRk\np9EzNnFLzC7h46mTu4h341eM8u+GHO5+qWEkSNFMEeo3pRGgS5lh/1eh4MAF\ncbwY\r\n=6+xU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.14.0": {"name": "undici", "version": "4.14.0", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.2.0", "delay": "^5.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "proxy": "^1.0.2", "sinon": "^11.1.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "esbuild": "^0.14.14", "wait-on": "^6.0.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^16.9.1", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^6.2.1", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "49d091d723087d444f3d782a23f9b66f11735e80", "tarball": "https://registry.npmjs.org/undici/-/undici-4.14.0.tgz", "fileCount": 94, "integrity": "sha512-G3pVFFh7SB6icKux/brM+e/dzgBJDGQ8rn+cbyg8fwrbpk0C7pG4rzJSTqA7TGk95EMBLAfTg3mKJ2q3sLJyeQ==", "signatures": [{"sig": "MEQCIA91uK5ypTEkQJmcgrANUNjPW5pumEhoXqW+Of6ZWkGwAiBXf5+v+eIU7FgnG8ZZAgrjInk/ShXdlGouJTsa6ZbBfA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 791807, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBl/GCRA9TVsSAnZWagAAzw8P/1E2LuqsTHvvf3NrMtbD\npJURLBZRGafWM2rGkqZUL2q/wW81NmYcHM6PgYfTtk/Y/xre4TQg6Kzsp1Vu\n1JotqbJJtmeBlfRYho1Pn++3yS00dJgT3y+pq81Rg+J1gctViIPXdl1eveN6\n5p3RDh2r1LcfPyWcuxPKfholO4ridtGNMQp1cPK+EMI9XNIJfIgvDaHqfwJ5\n9QTt644WecXCXxtl5SBdQqaoVOyT+zdB7nArOauo1h4X0onqVFe3HSX7D+K2\nqUmSarg/9bg5zoFafchc716ab9OZU0sH5eAJw5A2TKbeHbeVipejH+VA0sTH\nyXCz/hMi0YiYwniT2T7fKeo7rEkzclE4K+Ae7ABOVq1g7OWA7c9ErQn4NLy2\nRBUo8dfuSRcmbalc4UidIRPKJTPpCPq9eDkz/s11dhAyychDOvRW99dF4tG8\nVx1tE3lE/wkjVfH9a1qmWwWVOVuzOymUzhQC6VHJZZQPQ1YbkyCMK6hgNfqC\nHsdVVuirx9hUBDt/8ny7mzNDi7hHd7jhYexPf/5JWKnfUD6zyUJrUySh1W6z\nQv6YLyzvWa4T7ogsbW+ITg11++fzff806mMadJBD3BQLalcHHcLiiSr5Ti32\njGUZla9wMs9Lp1xV2jE/45a7dQN7QcbW95IQp4OSS4NVmyT9sg3ncekncu5Y\nbsQY\r\n=4ZjR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.14.1": {"name": "undici", "version": "4.14.1", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.2.0", "delay": "^5.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "proxy": "^1.0.2", "sinon": "^11.1.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "esbuild": "^0.14.14", "wait-on": "^6.0.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^16.9.1", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^6.2.1", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "7633b143a8a10d6d63335e00511d071e8d52a1d9", "tarball": "https://registry.npmjs.org/undici/-/undici-4.14.1.tgz", "fileCount": 94, "integrity": "sha512-WJ+g+XqiZcATcBaUeluCajqy4pEDcQfK1vy+Fo+bC4/mqXI9IIQD/XWHLS70fkGUT6P52Drm7IFslO651OdLPQ==", "signatures": [{"sig": "MEYCIQCDCG8SB9dDKCSOIny/4quLggcWcquh7BgAEzDNnIXlCAIhAKbWNYWMFre7+EJWQPsDrreOcJKNSXmMJjoeJLUeTZ3Q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 791881, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBoouCRA9TVsSAnZWagAAruQP/0vBy1opa0X+uNpuLwS4\nS6C6A3i5nC8FQqGvoNIc5yBDmXd8meldbNZ3jWer0+7467t/lWAbyVE/8DOA\nUR6+BB6Nr+9JyCl4lVWzehf+/wptFKhpSvDTby3fkybKPuDVjGTzG9EUW5pT\nOOtfwPRwje9R3ER00yAKqgy9W7hd+JT63AsOuQHVEcRKNJ6ROOdu0QhyNn76\nkIfafXZhDOByjsTJxsBA+YzuwH6zs2mO8SX+lY68hozAS+lGB24G4dlxALVF\nXXqhyxFStWENZAL1XgnTh0hm9OUyMgDlIbvKaPQTkf8AuRQM/xZI/NnRhL4Q\nqwU19gouyTQIoXznNHJEDwOP+gsN4wYHvUAiiB+Or5Cj6wtBtPP6ThGRezwP\nRH9d70kkjO7MIxFkQXSGczP2jLsKG6levivzIksDVTQMRpxO4q9GlaRpNVpb\n0QIUZ3o+v14NY6djWcdySsK3cNVElJA/8mUBKOZbs8nOuy6uipSNHXPyB1zr\nwlFPs6lj85mF3JeiYM1T8H+VjY/KTty4hvA1UDwEVKTOT5LnoLDEUWz7B179\nWfGWraBuVFetNC1KCR6p6XKmt4/tBUVi41DoZ99aWFXRPHF5/IG/xmpEnQ1P\nMeibWPyr3Fpu/BchAMxSagrIso71QyGnMjNw650PCTSJtmkZyo1x30dhm2BK\nCtB0\r\n=hXK8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.15.0": {"name": "undici", "version": "4.15.0", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.2.0", "delay": "^5.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "proxy": "^1.0.2", "sinon": "^11.1.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "esbuild": "^0.14.14", "wait-on": "^6.0.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^16.9.1", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^6.2.1", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "507ec94bce46bec5c76e934938c50b825eda8258", "tarball": "https://registry.npmjs.org/undici/-/undici-4.15.0.tgz", "fileCount": 97, "integrity": "sha512-kHppwh/y49FLEXl/zYCCbGB0D3nrcWNBczNYCsDdNYzWPs80aQgfKic1PVkJEIc2YlR7m0Lf5i559zbr0AA7FQ==", "signatures": [{"sig": "MEUCIQDhSx1M+CSGepzk+LhvrlfiOsHgAfFXjBlW/DmfVlOOGQIgXRw3AZHqPU6EfciSY4jz7DhN2J5RcA164Jdr5b6bJkk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 799053, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiIjAuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr9rg/+JmwT1nvHyakC5ac+nb2yFMOTB5GOX7MdMjSV/1zPUXNtiLf4\r\nb5DLx03SBJp1ZL5T+lGm9J++YNT57iahTIvnd6iL40HXAFvDEDLdtpM2HQV4\r\n0EO9d2Bu0bG8bGyf7ceiBi8AKjgP/7wpP9XHeRgyktMtu0AShjwFBACRns+2\r\n4T9kcs9e6V8GGyGYo/gf7+jiDp1mJEkcDWmZc844ezW8VOt8LxDJc92w/z6e\r\n5/eCR4yGaZ4dOie086GAmo7CLW1tLpqSj0wE3Er+MBMp+42T5dKIWsM5ebD0\r\nooirguCAOZtIuC3IMjbbkGtn54V0Y54a3XZfLXccjyO3QyT0Ghv6l2Qz13Yx\r\nq/mMCb5H4jlE/7mT4R9XrzUFVBMJAHJApYMthlicT/Pl+B22+Nj3tK/EMkHC\r\nnNqOTh7NxTgdM+edO4Fr6wiZTsZPwD1Wny4ICuDL00OADX4iE7UbtFknwtSj\r\niLyuSpjv7NWgc5D/WBMTGzJllMhN8WYl+xFAzxHUGqSel6NvfxsyqB5N0Z0+\r\nW0n/SZ/KTOM2lIMa3RkNFaxRBLYQ47iHs1Fu+YwJ08uiDEKb68Hh8ITWFKcf\r\nZU2WGsCWcdoVYr0dJjoJlMpvPK6+4TqwLxhx8VuXmXAj+Z9xzC6+tRJR0ey2\r\nHPwVeZkJTzSrn+WTrpMfiV0rOZvAbtknQys=\r\n=2KeI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.15.1": {"name": "undici", "version": "4.15.1", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.2.0", "delay": "^5.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "proxy": "^1.0.2", "sinon": "^11.1.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^16.9.1", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^6.2.1", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "c2c0e75f232178f0e6781f6b46c81ccc15065f6e", "tarball": "https://registry.npmjs.org/undici/-/undici-4.15.1.tgz", "fileCount": 97, "integrity": "sha512-h8LJybhMKD09IyQZoQadNtIR/GmugVhTOVREunJrpV6RStriKBFdSVoFzEzTihwXi/27DIBO+Z0OGF+Mzfi0lA==", "signatures": [{"sig": "MEUCIQD6ez77e/M2OIvtlI3c2DSvXwpWHGG9NHHhrsIYnSdqDAIgV3gHfSeqI4eraNcRO5UNA6HO+J8fsjtxT+3BS8J2UgE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 799038, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiJhKlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo7WA/+IDS8CRaXCr+0hLGwPdIsKXNkzCj7ZZWYzoOjdCm8HT5ureE4\r\nokyIkIrnwHO3EGCRO/Rbw7zKnMUP9pQSkq1rk5+WN49itJTBI+kbSGF4mAbp\r\nz3PZ/5+h2LDRKcp5Ti1CIAW3y9aG2lRLGPhPkFtIePLbF2ICVHSxtakuZZHA\r\nFzaFjafeUfWGE/TdabZDKFV/fdYVN4dkncSn3EisKxhmCMLR+LmiaqvY0MJr\r\nzGWY5C1oyD47JCQXRaQgQJhFquGCQSTs/ms1sG8eKN29PnPuEWlB2U+rPHSQ\r\nge2FzJ9oc7RH+JpM58sX7cALBx463um1lMAW8B2l8oHt0dQohLh8RQa24gJf\r\noKXcTPPGEMVzuSa90mo3OuTSpyL8jMZPOrNE+SndJejWD3d6pp9pO32DJX9U\r\n7pZlTYUA/fARBtu08D5T6iTkIIjWMMJv0UNj3+b73PuCWhVgQG9nqAp0zd6i\r\nqPMQrUGG4DCTonIk+JTF7GLcC553SqFFYOlZv/IYsPqgiN/CRQiwTqjvvbiY\r\n9k3zllV/ZDbXyFMVSC+7kX+Mt2LmWmgo1bLI5miKgWHfr83FmUk6q+NEHHb3\r\ndq2kUmr4wx3Xy1tWiA5uJHp4nDSvsLO7MKRRdQct4TtgHEwx4jEUneTASokJ\r\nGgmgxxJmj/EFAKXZM0Lq6gIC25NzK5/kmRI=\r\n=jkQl\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "4.16.0": {"name": "undici", "version": "4.16.0", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.2.0", "delay": "^5.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "proxy": "^1.0.2", "sinon": "^11.1.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^16.9.1", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^6.2.1", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "469bb87b3b918818d3d7843d91a1d08da357d5ff", "tarball": "https://registry.npmjs.org/undici/-/undici-4.16.0.tgz", "fileCount": 97, "integrity": "sha512-tkZSECUYi+/T1i4u+4+lwZmQgLXd4BLGlrc7KZPcLIW7Jpq99+Xpc30ONv7nS6F5UNOxp/HBZSSL9MafUrvJbw==", "signatures": [{"sig": "MEYCIQCeiqpik7chtnE3VuHX6wx8HWnebMy7SSAX+H7YmK+rrAIhAOTKzSpggOHmeBJzmgIfGhMS6GD1seJ48YEMt/Ubon1k", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 800011, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiNKSKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrTKg//Ub4yTEujRM4FvDbE+speKOwRQvZHh3531hgrXsk8liPgDwvn\r\nBFKMbzp8fUhlzmVrYKR+/+y7Dns1XGYEG912HApUYKNvsUBRao9kiyijFOT1\r\n7UCix94rslDunNpsWfKzoXgpQrqEAqeDxQPd6hcZemwuXAS8MNGs+dvEteqx\r\nvKxj21mebN3hcFtH1XEHegujuUaqLYOgjSFnJAObUhAfsjdJ7pWhpIXAbVGZ\r\nM0oenQMxDNYIxeXw/mcFIRKl5eq3KzrwEx5NSm0MCMaeaOnjFqpR8K2+albT\r\nsBzxlWvkWrR8CEC/aIWhm5oE/Konad6PusBbfyYY5G1iwijNSso8qCj+B+Ns\r\n3z1W8ZNHqkViSfNizoR/ipaeVosJ2422Co/iQ8r2KBCkvQEcE9yq/ij+7UiZ\r\nIGTixRn13/QaK8xcxZVBtwRdT15LuUcSqAeQC27yYj8WPrdonqJehfzxwhIF\r\n4+w3aHNnoinRq5jOV59KSmSgn+TM51ZMr5RdyB0ZSM9PFcFIOUUhmdohx+ig\r\nN0oHj1H84pMHsb2wqXgM0ZlWni83wvJ5TqdEOc/xQaAX6PgXta+0OJXDUGp0\r\nO7xaEHvBQB7u2QAcZUWz+q37cktij5eNGjlk5zMSnzO2rq74Xti8d4qpiMyC\r\nLK6wUsYXFhhWc8O2JJ6hnRmZT/T07QFiPRU=\r\n=vtp6\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "5.0.0": {"name": "undici", "version": "5.0.0", "devDependencies": {"tap": "^15.0.9", "tsd": "^0.17.0", "chai": "^4.3.4", "jest": "^27.2.0", "delay": "^5.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "proxy": "^1.0.2", "sinon": "^11.1.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^16.0.3", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^16.9.1", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^6.2.1", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^7.0.5"}, "dist": {"shasum": "3c1e08c7f0df90c485d5d8dbb0517e11e34f2090", "tarball": "https://registry.npmjs.org/undici/-/undici-5.0.0.tgz", "fileCount": 98, "integrity": "sha512-VhUpiZ3No1DOPPQVQnsDZyfcbTTcHdcgWej1PdFnSvOeJmOVDgiOHkunJmBLfmjt4CqgPQddPVjSWW0dsTs5Yg==", "signatures": [{"sig": "MEYCIQCjgpdsSyHWIa0d2LPeCPFtdOoLvh4vPdfiTuYwnIFj1gIhAKujD65u2e/cETGt5AKnCJyNKPKpJe+SWQ8c50X4a10s", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 802139, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiQuaiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo6cRAAo0VsTyPJRV+GjPTuRhSqnMZ+8SpxUf+91gmmCk5jH5FWxKAo\r\nb/k4GoAxmen0Ud3n4I0rPBUhskusjKZx7Ll4mzwnNHPHjWH+hWuelLilvDD1\r\n+Nw+RR6fl+3+ER21NW/EqJvjNQCNhEgMBz3hkgdUFpwv8tHSv7HdU0Oz0SGE\r\nNhbYZqb5GyXw93AsQtOP7zAZdDuKVMftUVvYu7MBuB8YSGknblHRy3vz28CE\r\nMmqgK9s8dF3DVB9UKD606abqSohspR2VxA1sxEV7bj+H8W+JwOiusb+3R19O\r\nZpruSg1cgSWjgtPMhV9bactUm1ZLRS4nNkQc1NKuZ+A8pRFqvG3O62QrZaPG\r\nt4QCLov1nBD94CZN5noKoB6crohg53FjjK6+jOUulQAbsMB0SvIXHdVzKtK7\r\nRgPj42+M9ozPl+G9Brd2nvHODI+6ofSTAxCn8ZBY+Gj+pglZkXKyxIpvI21Q\r\nwN53QLqDMY47kxbWOPGO+SrCwO9gru/uHE2bdhH/vd+GiXF78QJln3WyqYXN\r\nyKk2e75TbF2U7CYzzpmkzcxoe00AQYJoi3hUhR6IVqemVrSDdWtU25/tX1Yu\r\n5ffIeZhxACsN94zTgTO6xTjW9OM8luZfbMDJfXWCB6EdXj7G5oS3yYpI1Vuz\r\nsAEkz+0qGTsnVlkz8d/iun/8oBo3WDgsRiY=\r\n=pYth\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "5.1.0": {"name": "undici", "version": "5.1.0", "devDependencies": {"tap": "^16.1.0", "tsd": "^0.20.0", "chai": "^4.3.4", "jest": "^28.0.1", "delay": "^5.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "proxy": "^1.0.2", "sinon": "^13.0.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^17.0.0", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^17.0.29", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^7.1.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^9.1.2"}, "dist": {"shasum": "923833e5b56912848ddc5c87dad3fc8c4d05127b", "tarball": "https://registry.npmjs.org/undici/-/undici-5.1.0.tgz", "fileCount": 100, "integrity": "sha512-S+Vy6GGLHmW0uNLyCOpma6qCEoX6Pw/Mga34UFeqNaMnfDMgeJhPdHwb9FDPC/gWnNhLxhE9Lis6yBDHtcJCqw==", "signatures": [{"sig": "MEUCIAnHXJnyhIuHlYGt8ovT+zrGc5GQT15PmDFA5GvtrQciAiEA7VCEul8xjc1d4m38L1B1o+o3YzIj1lqHsy6p6//9HFo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 815023, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJibqTwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrzPg//XcTbv+Pg5Pk5TVOJDg8hDPH9MFwNVFIjTiHGVsP1aH8vFthZ\r\nlUwwr3YCPyUrIgtS0YHu28+aB65N39LCNXsJXi4v+lAwimG4UpJcEazVDqKJ\r\nNHNy+XrIVKUhfOuPmxM8fspQsiHrO/eYJD4j2vjxXdu1qp9yMwGsPw+/KdQe\r\nX2Bdjvyowrddgxlgrhx+O2VXEX40VwLARVJOUNlQNH7JxQrlKZ7JogcCnP3H\r\ni5gltjmkbZswdZdNVcj8pYo2aFa56lewMRMiiSKv1Bu3y3BtvI1xNCrdaeKs\r\nKJ/nOrD5aLa9UGPjdDc+zml26QYpWBXn19H1WIE7SIh+tQS1wwmMUR6bgscf\r\noFab15nrRymHUTFcqUjSuWnX9f54aR7Mrp6FwV3JJl0ZBXMhH1VvTJVBjAPU\r\nOYsP1rQKV/eCQWQNx3M51sc/S6dSO0Jti2gBj4lBKCMjFmlOtHB4/EhbUWd4\r\ndMLFGEeb7ATLco67lHGTPtR9CORE7lPDRL6A3Gz+Rdpwq4wc8x0ylEFIKkEC\r\nqDCbJ5AU4IAwwoGhxnAuAowTMFEryG2DL1W5BkNV05EDM0+cgg+k3osaVkYN\r\n/5VJPQUMMA1sFGgQ+cOEO3YHohagzep+t41NQ2/knBU30vVXFtbeCsuqs4cP\r\nulBkmy7iq2/i1TUkIcht9HyBdQz4DAKHbhQ=\r\n=sN3z\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "5.1.1": {"name": "undici", "version": "5.1.1", "devDependencies": {"tap": "^16.1.0", "tsd": "^0.20.0", "chai": "^4.3.4", "jest": "^28.0.1", "delay": "^5.0.0", "husky": "^7.0.2", "mocha": "^9.1.1", "proxy": "^1.0.2", "sinon": "^13.0.2", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^17.0.0", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^0.8.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^17.0.29", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^7.1.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^9.1.2"}, "dist": {"shasum": "356427b0d1f032ca4cf85537b1e1694a52090438", "tarball": "https://registry.npmjs.org/undici/-/undici-5.1.1.tgz", "fileCount": 101, "integrity": "sha512-CmK9JzLSMGx+2msOao8LhkKn3J7eKo2M50v0KZQ2XbiHcGqLS1HiIj01ceIm3jbUYlspw/FTSb6nMdSNyvVyaQ==", "signatures": [{"sig": "MEQCIDXpTEebfvp0Y/YQhFOIxyxo+L3s0pqBbb5u6ONEUcyuAiBfF0E32YsNZue6T4nqstQqPCXVtClxae+/tTvB1N72aw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 815985, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJib4JxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqkeA/7BgR7gKlYxEjXfkUpnvMqQ8BU6zDR+tBTqfWnYx4AY3uNbbWc\r\nSoZ3i0IAEiLfuaKqOHOIV5MVvr8DLynmCfWJ13Q4LabRAJHNrWhxZW7DoHWH\r\nBzg1zYBnkDDiuWg5vMhLLBu32xI0lAlHXsvdOTTtLtgye5H6SvbgAUfGye/X\r\nm8ukanp4kr4rtHOgRpAc7wa6WvPQ8VwQIjQllv8ewLhTESEUeNtsT4AMFZHt\r\nh564wlRDv1FILsk/R4GK5MRE5BSqKllaaBcilrFMK0EgOAfiLXNe+Nwv7LZ9\r\n6mlPobkYK6hNnf2EF8YsazvvnAortDYG+y3mVOGvpH0yXmi3m9ule/xf4a18\r\nhK+XJDYr2KAcBHpxlB6pEsE1w5fPtWWgb+nxAEsn1JjXHKjdqEJCLTT1mdqi\r\ndlk5ocHRs69GPvyVXmWUryNCvYAFs7iJ5zq1pU0EUG8qMAz+hPOlSdu5ZiyT\r\nXG3qV5uQAIJNeocZshsrB1n3vr4oFBE4ajGQbfaGLjlGtF6tKNcS1fGA41bq\r\nClKTz2lkpZyuMLVNqd+LOWJaYiOSSHfgFS1oU5CAME8q/1Efmo3W7eBJC2m6\r\nZc2beeoYsAcOd5wbHbJim8St1HiQv/hYEYtXHCdeK5xroHzA239YXZbn9kgH\r\nHn+ICFEuNZSSbn1bP+UfUMyON1Qif3tm3vU=\r\n=lfRR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "5.2.0": {"name": "undici", "version": "5.2.0", "devDependencies": {"tap": "^16.1.0", "tsd": "^0.20.0", "chai": "^4.3.4", "jest": "^28.0.1", "delay": "^5.0.0", "husky": "^7.0.2", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^14.0.0", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^17.0.0", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^17.0.29", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^7.1.0", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^9.1.2"}, "dist": {"shasum": "18c5bd59f8f1b1ed8dcc9dca2f754c44ec994059", "tarball": "https://registry.npmjs.org/undici/-/undici-5.2.0.tgz", "fileCount": 102, "integrity": "sha512-XY6+NS3WH9b3TKOHeNz2CjR+qrVz/k4fO9g3etPpLozRvULoQmZ1+dk9JbIz40ehn27xzFk4jYVU2MU3Nle62A==", "signatures": [{"sig": "MEYCIQCtrqSGRra9l4GGPeq5XSJGx5pfaCP7m/DwypkecJWWewIhANDwPqZb7WtA5V9QGj4Et6otOCTaEWLiznwiqT3RxttS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 817209, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJie8upACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpQUw/9Htf+qm4laA9hNut6FBTzCAJdLXygpSMK+6BL5HiGAeIE50Se\r\n8JPRJ44r4CHkrhRDuvHvhxcu0Vay6eE1whGKE5GITzKdGM6kxBZcw45x1jK4\r\nEBHF8eGZ6jK7vHR0TMoBCGNmu/RJUoIZUqMAI6zL/B1yWxyXyy1zkAl+J5sY\r\nXTMiT2GWSXBCucZya6NMX3K9cpyU+sGe81dzU0VbFUfAoAeo8FNY/y6cCOIk\r\nR7mo0IUqQl/ew9LdemoyN6avr7ntxrCqWc4PvWRyHjADZXxhpO0eZGg08Ecl\r\n3e4y72NSuNPWKRv3OxistqufDtDduNKGxit4yHYASRwOUtpn9KL5rFVZXq3D\r\nzwl/bUxogIqqjVJkRpMaPBRBVrVquq5b8fVBukvE0yGHYw46OenvQadIIC6u\r\nhaPDD6OBvpNBpyHmDDEJSnRBPOqORk+fjYWrNDn0UiI+kje65klWsLL4WOYK\r\nxSF14ocM24//NE737iaKnsAkAi04p0clRuaToJhor934lCOJPfzjxyhM5fqX\r\nEFWHd5ukSxHhyvJmiDABUMbZ5Q8aS/IS/GSZyWPBM/hwNobtOomi0v+VNJHl\r\na2sFdgYYXY4LtgHqr9w/gGJW81r5hE1Sbnl2S9DPOjPx/5lQRTvpLTrxtOmQ\r\n0QTQDQ0Rb1e95kymaDHv7lgcUGshOdfOS2o=\r\n=Mjv5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "5.3.0": {"name": "undici", "version": "5.3.0", "devDependencies": {"tap": "^16.1.0", "tsd": "^0.20.0", "chai": "^4.3.4", "jest": "^28.0.1", "delay": "^5.0.0", "husky": "^7.0.2", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^14.0.0", "table": "^6.8.0", "busboy": "^0.3.1", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^17.0.0", "https-pem": "^2.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^17.0.29", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^7.1.0", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^9.1.2"}, "dist": {"shasum": "869d47bafa7f72ccaf8738258f0283bf3dd179ca", "tarball": "https://registry.npmjs.org/undici/-/undici-5.3.0.tgz", "fileCount": 103, "integrity": "sha512-8LxC/xmR2GCE4q1heE1sJxVnnf5S6yQ2dObvMFBBWkB8aQlaqNuWovgRFWRMB7KUdLPGZfOTTmUeeLEJYX56iQ==", "signatures": [{"sig": "MEYCIQC9QL0JWWddJdl6cwxA4xXCb995UXNhauOc0x9319ln3wIhALB8FOVNsfINA4C4TU0+IRGXTCkWMvQsQUEabRkFDhLB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 822360, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijOJ7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpQOg//ZPxNOTr/KE3wYxwzcniKKZ7YfJqIf6Kb/kKHfYoR1gMt1Dq8\r\n48EHsTb6oH/YDxLa+k2uHzaComHG3Tgpm8EC8KB9qYJG57UE0ef+4qo7Scf4\r\nt8Pg4kWcu6jPfpQhXKLswrIV6+9p5j5Q+OLp546GCbPR5r1fSXcvX4Vr4fHH\r\ntGvDO1c49nD/0qYY07zhNFtRfXy3VPfR3XvsWCh/sKv7pb2GY01UFLrfJY1F\r\nhWW0sraAt4D3QvzPi9yL9es3Z4HLMRafs7vU5DVf7M77/4a4wbfnjIE+95Zp\r\nmRH/gW7PQ7QX17vJI1VhHbYPnYaY0KFaYCn/Nem9Na8w6s8FFLbdKLD3Obwl\r\nY94zZM6uVnn5rLYQRX5SmZhL2MTMopivQQRolh+h7T+Kae4FqEacbz4RTjHy\r\n6YJlukV8euT3Tequeq7zzfqWWUSWalAWC31Wpnc60CVoRlqH4Vm1tLyq5DFa\r\nVxmBGYOsheadqq3yvxAE+EovSeJqSGconVzBsgLpwgQhL4UjiRX7xYhF44CI\r\nM9NhXBxRPgGurADASL4PMU5X4abZhKs7kp3cx5lU/NqltneJY02lp/UGNiDI\r\nBL+jJZ3gmXvUzLgaBTV4cr3QoWKbTEM60qQftlszaCxpp3i7ja9MHBgDr7U7\r\nJxNR2sPTNiUl8rl5crC1WBdEqBQAWIrd7U8=\r\n=spN6\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "5.4.0": {"name": "undici", "version": "5.4.0", "devDependencies": {"tap": "^16.1.0", "tsd": "^0.20.0", "chai": "^4.3.4", "jest": "^28.0.1", "delay": "^5.0.0", "husky": "^8.0.1", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^14.0.0", "table": "^6.8.0", "busboy": "^1.6.0", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^17.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^17.0.29", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^7.1.0", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^9.1.2"}, "dist": {"shasum": "c474fae02743d4788b96118d46008a24195024d2", "tarball": "https://registry.npmjs.org/undici/-/undici-5.4.0.tgz", "fileCount": 103, "integrity": "sha512-A1SRXysDg7J+mVP46jF+9cKANw0kptqSFZ8tGyL+HBiv0K1spjxPX8Z4EGu+Eu6pjClJUBdnUPlxrOafR668/g==", "signatures": [{"sig": "MEYCIQCzYILvqx8fBYA2ZvEMYWe8bgKJ0Re5RSTjIUzQXosKagIhAJ7HUSXCIEaYR+vkoI6z8qHNzRZlwQkIHw042YlogCst", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 821419, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildlmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrW8Q//ZHHThYnGjGsq+Kn7Hyg6mT2WfxqZ+ESVty9wjNarf/6W9++N\r\ncdCk2GWBUJeW5PWGqpl0d6tEDxa48yplKTPkHlIpXDnM2/0+He/x3k5sLiwe\r\nCIXXVanNQrj+KITxH7vZ9LG6QrIO7SuzyszJDkil6DqgoEn43UzooemKQ2/G\r\nslvIQ5k0ARNh3dMAcOmlxJWhkrmJQiOej/cQ+/xZgIqCjQP34ZnO8a8nt76k\r\nxfslATFR449C0XWFlG3A72fqIJ5i1mw0Bi6uN/UJyBQIaBwTLJP+563ITBT5\r\n9hgMxvK7yUul/VLLcz4zUyHWQL+Arr1YMpy/bKjXLw9FkXafoPZUJsD4HzXd\r\nRewEvzZrxsnVpoJMD/u3yWmsKyyLBSIEOHI7AQ1nyv6NzUsM8b3PtWyJ/3x8\r\nYE52oTWYt2qVNizmt8kq+C7L2gU0u/FGPrFX9+rEvTRWcYz5t857AgO/6Nkv\r\nOW6wMFrddSYAMEEDrP9XpPLR37AYyrxzsod1lL76OEOnUX5Q2JIpkgPwg8xE\r\ncCEWnvS2k7nnug+i4Fn3BdKnfEcYWk6N8JGEupBoUP7D1Ng7iofUP8AC0VAj\r\nLJEwacNCNpPQ8N+5NWPUrujl+YqOXWvS7DzpKgHGoZiAd7IVe89x+SHOmvR8\r\nV5kMuI790CWVDMNhkY5drIDY9bB7RjWfWRE=\r\n=dYpU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "5.5.0": {"name": "undici", "version": "5.5.0", "devDependencies": {"tap": "^16.1.0", "tsd": "^0.20.0", "chai": "^4.3.4", "jest": "^28.0.1", "delay": "^5.0.0", "husky": "^8.0.1", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^14.0.0", "table": "^6.8.0", "busboy": "^1.6.0", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^17.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^17.0.29", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^7.1.0", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^9.1.2"}, "dist": {"shasum": "8a23382a6f1477a63b4f619b9f9bba2f586030ab", "tarball": "https://registry.npmjs.org/undici/-/undici-5.5.0.tgz", "fileCount": 103, "integrity": "sha512-hZvEljo/ScU/nZ/x6/9XgPU7MNLzrR7MVLQBW185D2hmk1iJiwhaaqBG4AVhDHbH9zZegtnKY53814SabW8v7w==", "signatures": [{"sig": "MEYCIQCLjjRD4HFnFPIRbKytjO39xUW+ISZyJFPF8g23t+HbswIhAJWK+6tKQ7yIQZMaWUIWznPtFsUYb58rPQMHBNYV+Wy7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 822472, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipxF6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq0bQ//ZCpT6iztcQKGqproS/pnQ4EIhyLQrSFWJGDO5WyyL9kfGC2d\r\nC3m3PuMO8gD7JZ21j2yumypLbPW1WYHxHNBSYYqxPcqc7ChW52W7W6WtQpA0\r\nPyIQW5gXx4fVIbuJHfJFmuroxuW3G++7XUt3UR7wzjJ8zBazJ+MSCydjkXQW\r\nZNraxT7iNycDlrF3nwOWd2Iv2K3InldFieU0ZaE7IyZtUOlIefzamSDWMH/+\r\nVsJoTtQuMEpANSNbqrCdGSCkqqUsSw+OOqiNRP4iA1IWRk0aaPVf3RY5Z35e\r\neZI7Zah8qTadm2YT+rfVoCh8EeG54t8iNjrUUn0WOrnE1V/TITlAaezPBRLc\r\nSoY9UReOHYvHyqHlMyQVtYS0XauONTdVqygXF2hcm4d20v4N2IuOqsVoyDyd\r\nZi6PEvzCByvC9rAwCzk2hVO8ueHrJhDGxLImCer2BjZ4NOusdqu2lPX34Qq8\r\nhkgpzEK2u5KMcazIUaaEqPlSGU0rseZkJt8zLEcXJCTKBTooUXQybnl7n0R0\r\nfXL+zzqpchguVqtW+2R5mHAUQO//FY+PprMlg8UFCOwq30vMu6MC1UczkJJY\r\nQrieHJoKIJaA6xykThGmmO2jZeJvzzLhYV2ZjAfyFgevBGLt89ZcY7FMPPjx\r\n2SUIyuat2mTyca105/ijBRcHni8pbGxKEEE=\r\n=/vRb\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "5.5.1": {"name": "undici", "version": "5.5.1", "devDependencies": {"tap": "^16.1.0", "tsd": "^0.20.0", "chai": "^4.3.4", "jest": "^28.0.1", "delay": "^5.0.0", "husky": "^8.0.1", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^14.0.0", "table": "^6.8.0", "busboy": "^1.6.0", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^17.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^17.0.29", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^7.1.0", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^9.1.2"}, "dist": {"shasum": "baaf25844a99eaa0b22e1ef8d205bffe587c8f43", "tarball": "https://registry.npmjs.org/undici/-/undici-5.5.1.tgz", "fileCount": 103, "integrity": "sha512-<PERSON><PERSON><PERSON>PLf18HvlCbLSzCW0U00IMftKGI5udnjrQbC5D4P0Hodwffhv+iGfWuJwg16Y/TK11ZFK8i+BPVW2z/eAw==", "signatures": [{"sig": "MEUCIEBgLNHU5rVp1NOkSoZ36dfpYsLjVr1k2MqYQnxQ2RTJAiEA/yzKcbeIteZP10Pv1pRROuqFpK3JYQvBEq1NocAlnMs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 825478, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJip0yZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqvsA//blwM6kokCTzFxQ/0pJONJMfeakLv4TtL6pyT6+9giANxv8Uf\r\nJBhCJKdmeE+4lq6lpuE4U2NDQ7jrpsjddR7e19TYVYeIOAx6o65HZXHQhxnX\r\nwt8kRSf1ZoAo7qdcpxN3FqhuX/WepX2WrWcviiltyhqweptUoAA+I+lALJ/A\r\nTz0K2iq/z5ksffbHUWxExcmomsMrG7Wyeb28zhBWRzhlr6dFyUihKv/43VK1\r\nIhrxYtESrl0XuKbthB82419R3fgu3adeDDmBlgJpsaCEuzSgw+t4L9YXkPnh\r\n5+Wyu1McfQ9ZzBDHwxm59rmJf0Dp5SMlZiM0jAWpO02MNZ4fyoKQuK7S0Rli\r\niHpR7Y5l+2BFWM8NPE8JXNF/bDSyMacwbSNq58fh9GWXAOyYQlcc7vGHv0RA\r\ntBmVmaoS+zy6s+ojxNrldT5tow4TEI3ig7LGEFD/eHlIADgnttvQ3Clo8t/m\r\nlvC+vhfIW0NLJc42YZ2syvSUlicab8EsPtLkCYJC79SucLxRXmzXLbsrnmSe\r\nBNXvpYHWLNtJlK77kS6Cw/b/jKQuBZ+KXB9jqEeY/aTSmAZB5OmsCcs8ejr5\r\nQ1ZdhR3kuc7JoDBzAqLkZpsb4psvLpQnSAhetUFDtkcQgIDJVgPWxyPyzcTY\r\nwHTz/t/BdIKzJG/yevZxQEpA/sSLViy3G3A=\r\n=16g5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "5.6.0": {"name": "undici", "version": "5.6.0", "devDependencies": {"tap": "^16.1.0", "tsd": "^0.21.0", "chai": "^4.3.4", "jest": "^28.0.1", "delay": "^5.0.0", "husky": "^8.0.1", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^14.0.0", "table": "^6.8.0", "busboy": "^1.6.0", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^17.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^17.0.29", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^7.1.0", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^9.1.2"}, "dist": {"shasum": "3fd695d4454970bae3d151326ee4ab645b8d1962", "tarball": "https://registry.npmjs.org/undici/-/undici-5.6.0.tgz", "fileCount": 104, "integrity": "sha512-mc+8SY1fXubTrdx4CXDkeFFGV8lI3Tq4I/70U1V8Z6g4iscGII0uLO7CPnDt56bXEbvaKwo2T2+VrteWbZiXiQ==", "signatures": [{"sig": "MEQCIC6yIdOi36XBJJkPrwHXE8u65RD5yzyriFMeJA5Qr2gIAiBJ1NR8BKuzAFNWe3Sn1EAyRxYmENpII9Y+yP8jmGaekQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 858962, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivqS4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoO/w/7BJ2FuNC9YFbOlr+u7DTP5LCYQEjE0V7/iSSC8aEz9Fy125Vw\r\nNAYfsd3UnAtQs/Q6egGSbIOxdOqg+ZQXgag8Gn2jaTJcYWmZe/56lUD9rSSw\r\nGm5ng2L0GjqHrZ5uTTgmydiG0Qzb7/T6ukYAAy5u2if1qPk8M9Cqc8HC4xeP\r\n2e9s4WTuSDHpp2YBRI3VWSqxPlCYssMXDj1rsuHwHMZfP/JYrhPe4WgMo1tO\r\nsLEMkCOKyEnKBNsz8vxNE9+D6oacehlBlqEfgwL98ZW/EOxSjUMxqVXU32o4\r\nR/OpzBtlV2yekiTNQXG4DZpRIE+oBxVMogfZgKIo03dGQJNrUrxROs5Te5yq\r\naJRBBdInOoEADm+J5G8vz4zZpuGVLHKYSf3Eq0YFXccwzU1sPQ0F7VZH9zmj\r\n7Bjn6/DKFeumuAePE0OGHgfTXkHSsMRCNSWuKVm8aRphoIduTpltTRBqCZyI\r\nqNJ+J2gNWAvKZ/oQUfea+UHKMogObYwqnPrBHMPQ/PiT53b8ZKT3YPDwG0/E\r\nZgN54BbZL+q3ke88YX3yFV5//e60By1unEgg6V9Mbxd+iytuby6VhGT3FYfX\r\nH++chv6NopRDrFYQlM6v33iSt4xU/XFeTDZ/Zmmc3oIulSZYb0eG0RrM1DvS\r\nwy6MRriz/cP9tzkPdFltAwbxrdalFyzM5DM=\r\n=2SDV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "5.6.1": {"name": "undici", "version": "5.6.1", "devDependencies": {"tap": "^16.1.0", "tsd": "^0.22.0", "chai": "^4.3.4", "jest": "^28.0.1", "delay": "^5.0.0", "husky": "^8.0.1", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^14.0.0", "table": "^6.8.0", "busboy": "^1.6.0", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^17.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^17.0.29", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^7.1.0", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^9.1.2"}, "dist": {"shasum": "7e5c56d15032d06738358dea433f98130e2a0561", "tarball": "https://registry.npmjs.org/undici/-/undici-5.6.1.tgz", "fileCount": 104, "integrity": "sha512-yYVqywdCbNb99f/w045wqmv++WExXDjY0FEvLSB7QUZZH6izxrVkF4dJn1aimcvN0+WAhv75Gg7v6VJoqmRtJQ==", "signatures": [{"sig": "MEYCIQCYQT8i22/XOA6T2H8J1Dy9LniKVJne22ijYpCXNxPI2wIhALr8a7Kh7LsEyvCB62GAuteUIBt0b+8+MqyVZsQIe180", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 859383, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiyCgPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrdJw/+KQ8QeGG/VmIT0sZlQhH2hbVTX3U5Ay4RpENnH03Kv4/I6d+C\r\nS4/nrylXQvQXGhYL2Wt4gC8GhbjbbqE+44KJCR7lh/qkSAO9KKlhlLvCCCC1\r\nYhc5CfHKyNmHauFKtKaTDuGQQpjUkqV3+TJIsQdP08GqpTuCBnk2NI4l3b1k\r\nB+1ZSdAkSxwBkr+pMHltef/U+TpdErXU/MCE1bzEfdx7OhsX6DmJHvwrzzmb\r\nxNTrYZwrApDCT53yTPk9phRcT8X/4YRS90JSDc10e22+CyvGoRBYSuMe2pTC\r\nK6lDKvleVqm/tpOFC7LfrtYtrSIDwDEi1Ai5szqn6PcGHEPq5P4c7w29Gt5A\r\nzWFgwas+HUCl429F/3hAe/oS6zYNwHj/e74VF9bEH91i16MU4YYFU7hp2iDM\r\n+Gb+Scuqq/97EccHrOUx/pnv6kuGJ8IZ7gR2HSIm8tQKNW44thQAIXXgV6Jh\r\njt8ZbUTZUxyeWTBX0G32mN4sGIHIq8wCiaiZAWecwjokn4EuQL+PD5fa2ExI\r\n3+dpFohSYPWMOAiHbenpsg3C9c647NWKEWu3Nofjg6MHo3kyAwndtvLrINpF\r\ngoIK20NZxK1x+PyL3TmY56jr1rU0E6QFq4sUDz74C0hicp7LFr/G1J/WkExI\r\nuWMv0dvBC3ZqkQSKLT6jL8s0FeAUvHdyOCY=\r\n=ooF0\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "5.7.0": {"name": "undici", "version": "5.7.0", "devDependencies": {"tap": "^16.1.0", "tsd": "^0.22.0", "chai": "^4.3.4", "jest": "^28.0.1", "delay": "^5.0.0", "husky": "^8.0.1", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^14.0.0", "table": "^6.8.0", "busboy": "^1.6.0", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^17.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^17.0.29", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^7.1.0", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^9.1.2"}, "dist": {"shasum": "979f89229c01505573cb274d0e11ea8d82b4004f", "tarball": "https://registry.npmjs.org/undici/-/undici-5.7.0.tgz", "fileCount": 104, "integrity": "sha512-ORgxwDkiPS+gK2VxE7iyVeR7JliVn5DqhZ4LgQqYLBXsuK+lwOEmnJ66dhvlpLM0tC3fC7eYF1Bti2frbw2eAA==", "signatures": [{"sig": "MEYCIQD0pspXjS8ROXJvsyDnBArgZ1QrFAz4q8DMgAH+TpuVWgIhAPXiZ8OPW8TnFCSQvKpUj+QoWGaskNJDjCxeU9SefAag", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 863692, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJizTYDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrr5Q//du1H0lhdfVoZAb8fdFqHpiQG/TdHNZw75ZUBlx3b0ORQtgCX\r\nhlzOGJDZ7dh1JRGk1oY5LXFWuU0x+KqgrfG0Lxq2IZ+u4ukF27rrAiMvZL9k\r\n/5Ac0IYYMtHLhKd+q41iATaVpXyoAUt+l/BrjJJUhCXGDNE31PcMeVGYq735\r\nUpV5WWqnWd2/skWHu3F5zYw74mZSTS9OrgmzYbeShE4DclGfo8lAGSSDfX0S\r\nuSVSJ9zfn5eCr3I7W7gj7nXIOWutc8u59MTrmnnSnJY6paV5joXXUN3GoUYi\r\nzma5Q4Nit2iUFNgKAqCSLtuAGfrGmvpPQGBhi+NfJqolMQzxad4R6W9uCRWq\r\nFAImAR1VwHGeBerTHElq2dPl+Lnyg2LHuVOTL4HWGDd11s/s2uwnyAVgrVPH\r\n5wStMaWDoRJH4gLJJhX38M6KdFWP12i0FCzK1mcip9WzSYW7E/Tw4bmuWblB\r\nVRFLVKH4AEle0GEZoD0s/yTY/RgdO2i24Ec2lVNruXfpLHlP+3t0cV1uKWUZ\r\n6uJP9ip+DxmHyBQg83zHWx02mcd8VhnZM9HgqyoX9aL4D2hdBNQIs+g2uMG1\r\nxadWqlj7OY7izDvnSf2tf2nLNSZLWyNORhwnbi2vs66iEPGTSAtB/S+/XhlT\r\n52QYBkpqOuT/GSyoCmnKJcB6QPZrD2xtOQg=\r\n=MSeF\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "5.8.0": {"name": "undici", "version": "5.8.0", "devDependencies": {"tap": "^16.1.0", "tsd": "^0.22.0", "chai": "^4.3.4", "jest": "^28.0.1", "delay": "^5.0.0", "husky": "^8.0.1", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^14.0.0", "table": "^6.8.0", "busboy": "^1.6.0", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^17.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^17.0.29", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^7.1.0", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^9.1.2"}, "dist": {"shasum": "dec9a8ccd90e5a1d81d43c0eab6503146d649a4f", "tarball": "https://registry.npmjs.org/undici/-/undici-5.8.0.tgz", "fileCount": 104, "integrity": "sha512-1F7Vtcez5w/LwH2G2tGnFIihuWUlc58YidwLiCv+jR2Z50x0tNXpRRw7eOIJ+GvqCqIkg9SB7NWAJ/T9TLfv8Q==", "signatures": [{"sig": "MEUCIHpWfYAfBKoouKkR2ivjSIs8e4sM9+zFhI4vR63B08IpAiEAxeQah4PMz9/NxizGtEYL/DmI93HZm9U3/UCc8Yq/ykM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 868042, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1RqCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoUAhAAjFZZkj7XI6WShrjHT/poHZPtdRbwsu/vWPGFnVx3YjvxC6Oh\r\nrqBviHWMlwNL21zcG23nbgUOvoyxX8AUoDAZDCn6c5mdGZhvdHDE1278qm2y\r\nQo9BSDWYW9TmKuk7QBI3hfqca0ZvnA//FiZmtmNAenbLKXlUxvhSLGqUrB4t\r\n6m/z329F+CvpFLQoIR6dW/dJpOIyBaiFTtwJSuqU1V12GgBTtbKHEa4J3/sJ\r\nNy79uyO88lDeNLOfAzYnjahnF2twfslY5hVt6Ql1dw+gcRfHksV41GXJSyuU\r\nNoKTFG+yMppus2+RS9fqrD21M/89AzJ1x3PyUDqT08nNlCFpitIQamnTVipk\r\nFgwmIfE02Xv7vqZD/BSNaAFp6gUo1KgXBChYc1O6Kv5mSScKzWrMrWkHdD0T\r\no3p7F4BUGyGf+9gl3IxF3UNgpAFtlGswnWNKkTPkUiMVQB1tyx1s6NXhzUny\r\nhp+9mzTG1HMkOg6CtrAYwAU91NQWgM8a3+bIZQ1W/N7o5SKui/ywfB394lE9\r\nc3SW6sII0nLyvjQ7wG1o3bhLwkiw40uBIZiyLpkpvFO3e5OJbem1X0VbvuFK\r\nVTPy+KKQgyNvPKIYBU1ZH4B3z+LxWSWpztHEUdA7v9/+bjCrklmANb2j7GZF\r\nJ+5hcMsJ27inwRpTaltlsA0vfQV6Dxgc+uY=\r\n=ZkkI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "5.8.1": {"name": "undici", "version": "5.8.1", "devDependencies": {"tap": "^16.1.0", "tsd": "^0.22.0", "chai": "^4.3.4", "jest": "^28.0.1", "delay": "^5.0.0", "husky": "^8.0.1", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^14.0.0", "table": "^6.8.0", "busboy": "^1.6.0", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^17.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^17.0.29", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^7.1.0", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^9.1.2"}, "dist": {"shasum": "511d43ff6be02f84ec2513ae7f4b07c589319272", "tarball": "https://registry.npmjs.org/undici/-/undici-5.8.1.tgz", "fileCount": 105, "integrity": "sha512-iDRmWX4Zar/4A/t+1LrKQRm102zw2l9Wgat3LtTlTn8ykvMZmAmpq9tjyHEigx18FsY7IfATvyN3xSw9BDz0eA==", "signatures": [{"sig": "MEYCIQD/sqjzAl5e1ARNAQ86cCDcmuiX3CWmV1GHrhWUsYOnHAIhAL3HiE2onC4lGJaO4BvmZiAScVfphj22VL1NTYPav57R", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 871020, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi6oAwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqYJRAAoiYwpyS9gkq6J4yErafmPIViYoNDtB26tgM+2d8HxgXUhxCg\r\n2uZTzk9QgXLcs5In542n7SzO+2x5zs2J51jkitpNGv/1foKm7i3aF66ucF5w\r\n/ckKVrYZDT4HGIvJOE+VffiOqGzXgn5iFDQFd85uD6sZfWieYScQGiv0LxNI\r\n6ybEGIgAQQOCEMZrKmQVeD/WyillTiAxeVYyHTo/rJKrtuukMeI/MT2sJZLD\r\nXBir0axaFsvXFMAqa3tfXwhzJt3vmxzWU461FeOFhZqePXlRvRxhf/PhZhIM\r\napEQjjbfVgEK8DK3jZ42WFnmaiV3ZZ51QxHHnmKsRtYW0Pwn4+qhwGiILy/A\r\njv0YU26bBXohZisEUWO22l3wID3YaqwTV8BYN7eR9RR5ikUyleJYmqbGHLEG\r\ngBv7VFQZaV3/M+/ohrXY14Djr5yc6uMr4SNtcPQ2WhRG7fcM6iA19wclUFjP\r\nQfvW0mZtQ936qvopK0E9IOZhhm9thIWRGtrAh/Y2yBlzo/xQqEgMxX0t76TW\r\nzcubreVyVE1K7xe/5ZlCZivuMPOR7WERvPQ9sHmDS6u4HG0ghRZYZwcwyLdG\r\nMeSp49L4ZKOlLw6l8+Xid7+hfQClQNLpEUqvnuXY089yMn8Kk1g5/gl7pWad\r\n33NyoysFUVVScurl3uZNMtt6zkYPcShEfI0=\r\n=8v+X\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "5.8.2": {"name": "undici", "version": "5.8.2", "devDependencies": {"tap": "^16.1.0", "tsd": "^0.22.0", "chai": "^4.3.4", "jest": "^28.0.1", "delay": "^5.0.0", "husky": "^8.0.1", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^14.0.0", "table": "^6.8.0", "busboy": "^1.6.0", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^17.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^17.0.29", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^7.1.0", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^9.1.2"}, "dist": {"shasum": "071fc8a6a5d24db0ad510ad442f607d9b09d5eec", "tarball": "https://registry.npmjs.org/undici/-/undici-5.8.2.tgz", "fileCount": 105, "integrity": "sha512-3KLq3pXMS0Y4IELV045fTxqz04Nk9Ms7yfBBHum3yxsTR4XNn+ZCaUbf/mWitgYDAhsplQ0B1G4S5D345lMO3A==", "signatures": [{"sig": "MEYCIQDUlvd5fpmd0PrLcWrxkVmUt/NFrtFwOl1+O46salJKeAIhALnKqCayP+FrzZfI257EHVd7e8SrXl7/+cjFXwMVnAgq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 872198, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi8ipLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpJQhAAiI82V2A11zXpSwWDCbOTf5AQjuTofxwNHOAiVSFHRDb/o9io\r\nceOn617CBEufI1EkzHhp5raP+0zMI000Q6tBJWtybLvMQQXe/0IY4UGqCJR4\r\nERLtxQNDYfOY0r5A2RCcfJpENN/Hchhp6oyal6zxltijQvWmYjiribE4GQPS\r\njjOyiV906QLOWai3BJD2j5YlFoXi7HpMkmi9mMxhPtvlGLqvc1L2bY46Jaz1\r\ny8UevxDnfDh1LdMTV16QApD/ouiCdM+siZtyxXutfgSzXBT9CiwNTKyVSRKd\r\nqdDHZaGq4gUrgpIiT/Tiy90a2TGjl+Q7Tx+481bG+qi6hNatuzqmCCrsNuvu\r\nH6aNBm8SVNZ1opViZzmb+b/lAynvMhZ90E6xgS2u/ZQbWJ4aWJA+4RDaLXir\r\ngg86FanRYh6K9w4BoTsW8qO9Uga71ciIafU6UIgUistxpYNP4tG1ktV8i6xo\r\n5Wf00jwKnOnq0FnS2DxTgcksqLEtrXENADkJ6ie3gSUmxEoQJ6/0TFLlhgR5\r\niRO5IPu+OmsdNWv0VPLvzIGmiAPqeG56JXJxK6toz3SgTAN/gyO8MqKpqdeF\r\nRqX3orL+fZGBg8F7uRmciF709UkCNnYLuwOhugausjJB6yaAJ+vuK69bkoot\r\nHXGRfXm4TKhO6W4tiEDXixlVfhszXDhcd20=\r\n=6QoY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "5.9.0": {"name": "undici", "version": "5.9.0", "devDependencies": {"tap": "^16.1.0", "tsd": "^0.22.0", "chai": "^4.3.4", "jest": "^28.0.1", "delay": "^5.0.0", "husky": "^8.0.1", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^14.0.0", "table": "^6.8.0", "busboy": "^1.6.0", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^17.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^17.0.29", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^7.1.0", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^9.1.2"}, "dist": {"shasum": "09c78d16374241d63f8a7fb253f74a25bfe5247d", "tarball": "https://registry.npmjs.org/undici/-/undici-5.9.0.tgz", "fileCount": 120, "integrity": "sha512-6eyCNd9by1lVdGOGMu0njWlQxGEedmOANd9OZQkf8l2qtHM21aGXOG4vPjeCKziI3GxVXhiKZDdjuXUsBuDsZg==", "signatures": [{"sig": "MEQCIGPpad5aXWzCHrxO04xGDq4gqv7j9F8yn6ALJJlsAmQ5AiAV3wO8T1eY3R7VKD9XU9e7FJcCRRQPi5o+88bqsAbVyw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 874205, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi/QSxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp99w//S9SGUeLo2yCrLe7WJYafvncRxsU5HCUY8xNlVgaZy1JWx9yZ\r\n33Lg4l0rJWWRExZqRNZcLAUNKMsRkuk1Q6DFzRf9zZXZyjrosllybWE8BDbt\r\nStA15FFH3qBmGG6mdPNqbPJsTxi1g7a97cxn9Mv322IXWFfQml5tdzKUnZv/\r\nL94f6wfHDXKOddpNRy9INJM+hPx/PSdmcFQLZR/fQSl4PamvgBYg7qvi/pUQ\r\nn28L7nasyi+blUjh+V/y1eWhgbJkGkHQfS2nqpnrzhXy8fgoViXAmbzpU9dg\r\nvkvX1l0X2RyknaXtDp8iEdgUtnTfJ58BDA7YOvGz42NkMntYApimQ6Ua2hx2\r\n4hlwPF567kM8FuWEekEXpohINrOqe5LSMxwmheab916F1kvveSEYakwFE0tu\r\nBO4G+SQ/Vt/qIE/AY0GjMOXMI4I31JrkXa4XkPpbZejqcghFfW/o0I79fczr\r\n0VJi+VWy+nt09LiiJhWZpMbELfVgqKDeDUX3zDm5dbp7bOP1Go5wLwVKfYcQ\r\nrHgG4nVyrkNmkBG6LfsNUI/A2rNMpI42AQVCAaOuv/FCymUtNZaoZhBVjT+h\r\nqRrie52z0lbQnB7tPk9Fg4/CnpAGzjD9glRiCDoTi9HHSAaK9HGXQOmveviG\r\nO9sB8rY/5UyX3aelFbtDfrlJ2bLfBzFV72Y=\r\n=CTec\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "5.9.1": {"name": "undici", "version": "5.9.1", "devDependencies": {"tap": "^16.1.0", "tsd": "^0.22.0", "chai": "^4.3.4", "jest": "^28.0.1", "delay": "^5.0.0", "husky": "^8.0.1", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^14.0.0", "table": "^6.8.0", "busboy": "^1.6.0", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^17.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^17.0.29", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^7.1.0", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^9.1.2"}, "dist": {"shasum": "fc9fd85dd488f965f153314a63d9426a11f3360b", "tarball": "https://registry.npmjs.org/undici/-/undici-5.9.1.tgz", "fileCount": 105, "integrity": "sha512-6fB3a+SNnWEm4CJbgo0/CWR8RGcOCQP68SF4X0mxtYTq2VNN8T88NYrWVBAeSX+zb7bny2dx2iYhP3XHi00omg==", "signatures": [{"sig": "MEUCIErtSnTpTzV+0rK+fjrXhZQ1I3D/BnmBxeyh1Ostj59hAiEA8b6rihTgZVEJpnFcsSVumC6rFRzJeISWs/v0Cr+X94M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 874205, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi/QUvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoi9Q/+I8mb60j0ECvbmR8OBoZDg4GK6xmu3hKxVClNuS0bP4BZkEOJ\r\n5wiXg3+MhwESZP5H1mI83CHownFkXLEqRflQx9gUEjs42RKrxh5LKlcu8FzM\r\nA2qVZcOMJ3g6BcNYPCS6pCbWP1JWecF3fQytRB7Kjn/dltjEtTzioSnr0/+A\r\nC00tvG5qDQLJGyQmDhkvfgc11vuZSRK+AgKtZDcJqOWl29MY1WyUi/R4k5t4\r\n98ipmm3cczLUL+fE25zOl3uyTQuekMZduV6l96oi2U+Vhm1xi4lElMtakBrW\r\nHDJGKasohKcZ+0GnSaJKoCCwl0PmXUBYLd1+a3amy8bmW//W8Y+EiW9g7m2P\r\nDzcb7+dSm1vs4JeqVAMM8k3G0yZcF+sxZ52Df4k/ahffNCo6ol0HUwYz7hEz\r\nix+5cWB/eNp8Qu3IOzcFKXrLqhkObGxFWVwBLoJWl61MlR+EkQh/oVuuJJg6\r\ny1OSPkW4eZ04jba8NdwfAsxhZI0b7eukB+8dP/hdcLRpbcdreGPzlMumpsGA\r\ntuzSCB+VHPbWB++wBz/PDZYaFX42v3VGE+H1vQhjwmqfz7PJpmdV2kW/+FGs\r\nxJkV1747N53en1ddIwghmi8WIj11i7WHidLry+0Q6djoCTFfEaYXetyv5ohc\r\n/hyS22q2QnDOIP4EfW5/GoKdr+KWK/c009Y=\r\n=sXbR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "5.10.0": {"name": "undici", "version": "5.10.0", "devDependencies": {"tap": "^16.1.0", "tsd": "^0.22.0", "chai": "^4.3.4", "jest": "^28.0.1", "delay": "^5.0.0", "husky": "^8.0.1", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^14.0.0", "table": "^6.8.0", "busboy": "^1.6.0", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^17.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^17.0.29", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^7.1.0", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^9.1.2"}, "dist": {"shasum": "dd9391087a90ccfbd007568db458674232ebf014", "tarball": "https://registry.npmjs.org/undici/-/undici-5.10.0.tgz", "fileCount": 105, "integrity": "sha512-c8HsD3IbwmjjbLvoZuRI26TZic+TSEe8FPMLLOkN1AfYRhdjnKBU6yL+IwcSCbdZiX4e5t0lfMDLDCqj4Sq70g==", "signatures": [{"sig": "MEUCIHFYSFR3fdcSKydcy+7kACT4MhTwv4lHFL4xU+ZYIyBAAiEAho646KGFVIc/AcQ/g1VYcbgFigU/sSLsEGiULgWgUQ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 878480, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjBUOrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpDYQ//T49IjVhnL9+T1BtAduIxuNHWf6zmdtxTD5+vLGKMKjpvoAwp\r\ntHSz/HAEFpU2JMnh1O7SIYFZ4k1JpuXJ6y/UhkN+AGciFCFCUy2v1kdYkz5h\r\nsioRiH439lJajJ35g41JDjjF5KUTqpi+YHPPgQOJDKM0KQy+z8XurNKwqxs9\r\nxgfD1DsmUxpJ2J7LNDcAEuG+fdCfYOKXVYDZ3BkuJ0C+vOmsL7/UNfVDCIaN\r\nj790qyU/NpHfFlYs5yurE4qCn4f+NI9/qmxLJ2uLg4nnw1xiqIhElr3D4XYr\r\n0UNP6aGX9wLY36t6fhZ7h6YKDfP64s6Iq5/PggIkRV7LaknfO6sF5UiBmGDI\r\nNyju00iBF17oCyja528qwELyhC2pPMMpk/I3dJacuoLfy77xnqQJuO399E1X\r\nvyLRf2FjViiIaF0gQIdaUGqaMvcyQsJ7k3I8EJdvVa7Ga7/eJVF5w1hW50mh\r\nBbKKdZufYgtTaXHT7zvBPxTg0vwC0WsN3F2sM6dV175eKbJ7VSnh8jU12c3Y\r\nEoFhHS3aBuefCXdCDDIKv6yr7XrIDruJlTaKpaa9AgTXsUKKqrSWSpi0jIiK\r\nNfYFlpL6+KTz7VIPTuBDzh64q2aWwGBJahHJ8H5tZyYccWwpYbr7gJN1ZPEF\r\n1o8gWEDzEdW8kLJkymbdPf4R0lYASGKgnvs=\r\n=+NNu\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "5.11.0": {"name": "undici", "version": "5.11.0", "dependencies": {"busboy": "^1.6.0"}, "devDependencies": {"tap": "^16.1.0", "tsd": "^0.24.1", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^14.0.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^17.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^17.0.45", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^7.1.0", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^9.1.2"}, "dist": {"shasum": "1db25f285821828fc09d3804b9e2e934ae86fc13", "tarball": "https://registry.npmjs.org/undici/-/undici-5.11.0.tgz", "fileCount": 112, "integrity": "sha512-oWjWJHzFet0Ow4YZBkyiJwiK5vWqEYoH7BINzJAJOLedZ++JpAlCbUktW2GQ2DS2FpKmxD/JMtWUUWl1BtghGw==", "signatures": [{"sig": "MEUCIFiSkLL8mgahB2BRot+y7ZD1Ze0O/e/3BBAKti7c5aFhAiEAkN/TnLZD+rb0ZyxEbmG3KrKZL5MnOSGTJ9Ex760dt2w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 900220, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjOwMDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpOYRAAml4lnJ8pUyENxpC/80IMEbFwFSvjeo88TIELTuFpH0FNsWNE\r\n6vNMGOIXfDSI7BS26YJ9TYU7Ygv732Dgrrv/TVxDEv1WsGMCf3R88SzDJ0BP\r\no4ZV2e2BJaXCbF7mtlrV/Aj21zHlLEYZVV5PblHpObsI4bu+SdqsimS4oiTU\r\nQGwXuy9GiKd/qrDN+axtzNpc3oiao8mocmO4pnXvc0KwSBG8cQkxfXYlkk96\r\n+59oFjWrpqkFIfgkJ6Us32SHk8qZCstAzDoBcuvy2L3t/2yROY+xUkOKLGZc\r\nSWRmwoTxxMHIDMmlgsQsnkUpyAx2Y9XgGUPTSgHtssqq22eoBVnfzeTsyAkS\r\nq2d5Foz6pF62oud9YNN1r7eK4F8/ydEFbOtUWmMsMFbCPwohDrnD23euKkUd\r\n1omGCIQOW0n4A/mEiWI6VGiu8Hbca0gr61yD1/9Iyh2pnzrrFnIR/9RsDDPt\r\nriekGYW68+vY/7AC2DvZX3Um74CXjJeghktVWeY1hlvZDZ1kjed42+vrVThw\r\nGSTxjs8l5abj1dOWfpHD4Rq3pnUi6/06ihYHkPBGj+ZZs/RrhZ+w2svkqjIE\r\nRvmf3fxcQUYfQCG+K1wQ24PA4eb/fYMv3WpaDhstd+Zu40SFV9BW+YZhbCVQ\r\n1JLTU39za58p26xecbCJKm/FDGpBfrhj6YQ=\r\n=yOq9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "5.12.0": {"name": "undici", "version": "5.12.0", "dependencies": {"busboy": "^1.6.0"}, "devDependencies": {"tap": "^16.1.0", "tsd": "^0.24.1", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^14.0.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^17.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^7.1.0", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^9.1.2"}, "dist": {"shasum": "c758ffa704fbcd40d506e4948860ccaf4099f531", "tarball": "https://registry.npmjs.org/undici/-/undici-5.12.0.tgz", "fileCount": 118, "integrity": "sha512-zMLamCG62PGjd9HHMpo05bSLvvwWOZgGeiWlN/vlqu3+lRo3elxktVGEyLMX+IO7c2eflLjcW74AlkhEZm15mg==", "signatures": [{"sig": "MEUCIBnFQpKie4qBjqGrj8ZF7zPGgIrFnpUwcfcZGreBMfM/AiEAse2PvZouSs9f6pGDoBNQU4qN47/z9zRcmfpwxPV+71I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 999797, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjWp0uACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpvNxAAkljaOQJKeI4LNlwJZpbxCMLNZwgPm+6FFj8kWeQdFBUffyKl\r\nK7E0bK3JXfSrWm3VL3vyhHLOb6X4uCYcTjj3lXMd0yZQZxQHpAHAmIwpCbc+\r\nXD14dpHnU1iQgdYc6GO78Rn8iHo0ZH3ZjtL/FlW31smV6uemJy7pobDd8DuO\r\nGR4d5WJAFzaBbG9nwQVJLEsJCY2mm62tAyqn/SXspQxi8fd1EKsKL3aDDply\r\nznCABMA8700uMdr3VfVeHssDi7jFK79r+IyryC95Yz4VcEYzdEAXCmq8H2sa\r\nBpN+rtAOYuoIxor5rQfZ2H6W5NVW1Bc01NaU/UI6ehG2NUCp1UET20qddP+a\r\nr/GjplQhKXeQFz+QbjoMjerKs3VUr+5qgzM+F0DXgHanC0xVRkwEG6iYsnXx\r\nWa8Nwc4ZIO0KfUYSJktzjK5AFUl1IXV3VipGFbPcZ31sjnTxN0egVJhKvamR\r\nR+tvZwHOxXJBqW/HdqXB/7YnvLCBgoiE50il92wo9LHPyuEG+ebElaFnIWwo\r\nGUU/HzLffNc2T3XUytTQF2L0pzRff05Z8vrIOJ4fFSQZrlOFgtOCisRKDUF/\r\n4L9jUkGLvB9RihziIBmdjbqfElfgqqFVhGrXmfAPDn3Y/HOgSfemLZ+7ba17\r\nIwiKJsv55F9sItNdo1SEUVI+XYl4XN2grUM=\r\n=OZEC\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "5.13.0": {"name": "undici", "version": "5.13.0", "dependencies": {"busboy": "^1.6.0"}, "devDependencies": {"tap": "^16.1.0", "tsd": "^0.24.1", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^14.0.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^17.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^4.8.4", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^7.1.0", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^9.1.2"}, "dist": {"shasum": "56772fba89d8b25e39bddc8c26a438bd73ea69bb", "tarball": "https://registry.npmjs.org/undici/-/undici-5.13.0.tgz", "fileCount": 120, "integrity": "sha512-UDZKtwb2k7KRsK4SdXWG7ErXiL7yTGgLWvk2AXO1JMjgjh404nFo6tWSCM2xMpJwMPx3J8i/vfqEh1zOqvj82Q==", "signatures": [{"sig": "MEYCIQC83xFv6qXIWbvIUgqqeUO9K+S1wOJ5Q9aVkeTm9/ot8wIhALJKs7l1Yb+LW8TDieob2E0Vl21r0fVjtY1Us1HaW3YK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 969818, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjgKEdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrl/xAAgI1jOtHxRCRaMD5s0ZtTkIIQNuCKlMrZjWur/j6Ve7NmnrQe\r\ney8e9ssodZsLaJSmsA24oHcf1ykiQyzVRMGP6N/bGgOCoTt/UzP08v7Rw2gu\r\nAe6MxhMIzCXe9sqOSZMkpvmhTJ6rztGBwsrZnO4d++fxwzittXLzxuZV2Vxd\r\nd8DxPsa2DK7HBgNI23Bc1DHiQxhGxQF/Vf9o5HoOiU13sAOgg2iLXEcIUuYt\r\nDm2uefEa7i1T7LgPV4m2LL7KPjDnTTMhdm09bNP0hlUlX54nFZsun98AgV6n\r\nQUwqRP9rnEO+yPKlCep1WXPI6B9n30z4RsF8XtorBsSI/0yJfLKq6hZ36y3T\r\nzxLyBxzYEggsYSq/X2j86SHFoSJAN7+UKmtuluwCL109LAPbLWvWh7lqG7/B\r\nsl7EGBfb+8Vr+umXdAWFUX62wa9t67Z40QSPuHSZZwJQP4h19KJHg4G3dBzN\r\no+Uu8zBOSqszKLw+2B/9kLrMlEV/kvsLPdxFS66kghYrEFxI1rYX3YA5OygT\r\nEasFQ9bCYWjf4o+lpJlq5oS+5I/MAWtmOvApK+ywA/VSrHZZNd6n+1+guZSU\r\nMajfOuUi3lAXy6P30JbPjxSM3WhLpEacA3JvQiFbVkkHDn6TIqBpJm7uMupZ\r\nKm6Ol/LfAQ9RGb4f/SiRiFYBGRUHBz2oRu4=\r\n=w/SK\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "5.14.0": {"name": "undici", "version": "5.14.0", "dependencies": {"busboy": "^1.6.0"}, "devDependencies": {"tap": "^16.1.0", "tsd": "^0.25.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^15.0.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^17.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^4.8.4", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^7.1.0", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^9.1.2"}, "dist": {"shasum": "1169d0cdee06a4ffdd30810f6228d57998884d00", "tarball": "https://registry.npmjs.org/undici/-/undici-5.14.0.tgz", "fileCount": 120, "integrity": "sha512-yJlHYw6yXPPsuOH0x2Ib1Km61vu4hLiRRQoafs+WUgX1vO64vgnxiCEN9dpIrhZyHFsai3F0AEj4P9zy19enEQ==", "signatures": [{"sig": "MEUCIQD46eIqVpmgZEvYnIe4k+IOx78VnzWQajyAS4MortEyZwIgHXSzSDMiNuOleKjtAcxmXrC8RY5RdksfBuPGNiVrrKw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 974751, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkhQIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpZ6w/+MsbgAtc7G382m+xoOX8sLMZWKlOwS4WImFMoEmXHMKicJQxK\r\nCgF1TdhtkjB80tLHr/GoBuQZyDFZhpG/y2ijogxJW7xsvAk0v0mFjou7/9BL\r\nT/M4YFw3S9RyMsReKR2bGO/CVvgOVd6tMtVjSPPYLzzKy5aPKA4dFarKz0NI\r\nT3b+gr4JJRnmd1i9BvU+Oty6YNyWOUQCytCYCpknXnkFQbDaEBDWaHTFhQs8\r\ndHIvo2NxsrkkewZH5JDM8omlFICU9ZX7k1BnJw3Lff/+2SFenrd4VV2UeNmV\r\n5/DUNkCHcBXh3XznRV01mQCg5qFvCSEw6nGguY6FxwtAQoRmyGqzFr4Fxn/x\r\nzgGZCHQca5w1TAtS9WOL6K73OmxmSzlA5NH8vYcV1CC8f+3gErLe9g/l1ksS\r\n9lb+sC2bnSEXNtxut4idV/YtQn3iLVkkXPfcB080qiEZsXIk0rVeLtugvMdf\r\n2KJGZnZcpHHFuQ04jidPVyFXYQ1KWJBk3MM2DsSNR0iRnEM7kgC8Yzis+D+g\r\nb2aB7kR2ZobqzvpzCeSegz5TrhiZ2HcBPSFDMsJIGEOEXHj+MzCCsNnxd3xV\r\nJ0JlYi+9MuEChR9y+cSCymchQE0DPqZLSU4FPZ8/jJtiqZVP48AHnQtuiLgk\r\nov2y2X5hD2zb6o2ROvFbQrEq+8rK07bDWQ0=\r\n=tBml\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "5.15.0": {"name": "undici", "version": "5.15.0", "dependencies": {"busboy": "^1.6.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.25.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^15.0.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^17.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^4.8.4", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^7.1.0", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^10.0.2"}, "dist": {"shasum": "cb8437c43718673a8be59df0fdd4856ff6689283", "tarball": "https://registry.npmjs.org/undici/-/undici-5.15.0.tgz", "fileCount": 137, "integrity": "sha512-wCAZJDyjw9Myv+Ay62LAoB+hZLPW9SmKbQkbHIhMw/acKSlpn7WohdMUc/Vd4j1iSMBO0hWwU8mjB7a5p5bl8g==", "signatures": [{"sig": "MEYCIQCBuHYVgajOaTqeFvYysCurLmkiyIsi9/9FHXv6fqc5PAIhAMaqCZzyZqZoUynxSGqi3FSa0WWp3EJDN28vtqU+qCkM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1068495, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjvqjoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpioBAAmfb0U7nQkOHslt3Qctg9hGN7gVyuBTvJfwjuHFPkCHBw7sjI\r\nut1j3Qr0YqRez0Rlz+IMOLFiAOSNtCOxBaZ5WTbZJrhjsPhcDRF88wdErI0+\r\n/z1/V808EifjFkq+MY8YxrU4CL5OaheHfmNyIsWMaGsbqOacT6ORM9W64YQU\r\nSJ6vEu05s4qxaku0EeckEgzcg6tyhKA70s7PZctIHggqZFOJSs62GcIH5TS2\r\nllSK0eXHeUeoxVELGv5MmPiriKebvtfDqd5HLFrF3XXEz+vCjNlUiw0OkOBm\r\n7i5MVy8IDNRmbGNAwAc9EsPSp3Aa3UEocZUelO3lGEtdGl7f1oF5YO231A4f\r\nzzFxV/No4EAxLc6BeWTb5I0bG79i3Wiz0ww0Yt1O4DWJXIQ0HpTQUdYc0TCH\r\nMEZujp17HUWqnNDH4m4aVwWbS2B4AJL7o1SUq2rEjLLgXqTb1mw27CRFYr71\r\nCCvnS3MVe5a+EbsxZhb0w9EvP1hK85tVvGTY1N80+5jCKUpq7u108cGJQ1za\r\ne1VJZvXFf1cnbrXPfZGacRttRujLZdDQV5z4IqtaMlW1cV6gtqJcNMlZwkzp\r\nZ+Q5tPl/vKqiS1gdqrhioQ4P0CLIgbc4EI7G2sTMVZjWUnUri/cThzqMoYqa\r\nQbvE2fVLwXf8HlnUYrNMDN7c0VEVPriuHXE=\r\n=h/v6\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "5.15.1": {"name": "undici", "version": "5.15.1", "dependencies": {"busboy": "^1.6.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.25.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^15.0.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^17.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^4.8.4", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^7.1.0", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^10.0.2"}, "dist": {"shasum": "5292454b1441da486a80c0f3ada1e88f1765ff8d", "tarball": "https://registry.npmjs.org/undici/-/undici-5.15.1.tgz", "fileCount": 137, "integrity": "sha512-XLk8g0WAngdvFqTI+VKfBtM4YWXgdxkf1WezC771Es0Dd+Pm1KmNx8t93WTC+Hh9tnghmVxkclU1HN+j+CvIUA==", "signatures": [{"sig": "MEUCIFWMMgdqqqIsKnLTsfklrh4ZAqJDSMK/G0jYPPfyS/G6AiEA041VN9keMDbvX2v+CxWIkL4bkIbe5MH9NaLd01GbcM0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1068690, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjyS1xACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp1PA/9FRKs2ggkUg9pX4618/viWvKyv/YNtIbpNa3sKdafhajaofex\r\nDiRv6ZUtEpD4Ue5PyWwXUwMQveuGoQzFcvEuQPXH8km74503ML03lDY0oZKE\r\ncRjGGaldIHvKmq+NxXMcmxrujon7V8kySUQCNoo+g758NASf5BYiOJaBts6E\r\nPU3fhznG+GOUasvIspwPHw9QJzl6VJQJlHqM3HyUFVAN0moNPEm5miTyO8rV\r\nS4Agkx27ouLFyOGc/Mr6AOBTBxIqMnIuD77hhIJ6pnAq+wYmlLvqWE9T631M\r\n4tcgGtiF0VwE3FjZoQgseilRPfDItqKqVw+JgqkbKXk5uvN4r1rtuWllQPYo\r\nfaxSIuL6iNgNjL7vl04vbsftBtKQvzOPYXYSJhE7UTIUkDWdEfBOcnj6MIH2\r\nN09emiX8TZ7w/KTUyPDzjKZZaJAX/ySZerWQzBPUjivpmjmwxGEF0D8Gnn3Q\r\n8pA5m5cnBPm2AdaT0xGWbS0Y/Rwf8Ly5K1U9vp4Op46WW3E1Urg9BNjJCtyn\r\nfrgAtNqR5toxkhdaOAsOUPjl39ohuKpZhVFhaUs2QvZqJSMMf1iH7d8c8sRO\r\nNR6IEhzCKQ65umTR66tpFer2wGufuJq8Hb8vd1YyOJMob+coY7dTUqO+Le2s\r\nvREzI+02r7W7+6r3iO4od9vV5BFSOYrV2w4=\r\n=qtfy\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "5.15.2": {"name": "undici", "version": "5.15.2", "dependencies": {"busboy": "^1.6.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.25.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^15.0.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^4.8.4", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^7.1.0", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^10.0.2"}, "dist": {"shasum": "97ac24def6cd0e04d20edec2ed58c25969282c83", "tarball": "https://registry.npmjs.org/undici/-/undici-5.15.2.tgz", "fileCount": 137, "integrity": "sha512-JsN6meyJESMJaLqOuozstwrolSkLWlTIT4qpGoKepNdnZy7HJMPKB0v0Td/CepGgoiPpUSvVO9Nh/5N9QrmDgQ==", "signatures": [{"sig": "MEQCIHq7DiLwMxLCd0JoTgWIuPvpRUNHhmp5tsVt4Q7WsC/xAiACTg0Ok1ZCS04BuWL2JbiobDGbdtTT7g1fAWVrsFGL0g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1069418, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjzQF5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpP6BAAnzOgTREGBBqXqmdqMd5TZZYwUhFUu9XWzbbUNLK8xmZjQc/D\r\ndWHlc2jkqtxVrHHk89SAqrlU+ip8i9or7v/pEgBxhZNLaBO5vaCjFiLtXT8K\r\nePoqAWmhRXcs32te2FOw9pjpU+0jRl1DQTAO0W7c/ZFM/j7CLjRBaCNv0NqF\r\ntCy3Kj6wtGgqliX1kYR+laZWpqv6DkcTyg7ddGuQVnY2P9zduePHmp3qlDM2\r\nTPSMa67Hp12J5hMyi4887HqDH8aaPZEjVS7RbXECddWWBcQI8dNmdyebV8fB\r\n3Qk2q7NVPE6bEXIO82yGlfDi/7NY6Lf6gXE7qE7n8xgQI/iy+aUir0NUGJ6o\r\nHF+ihUvCfcMFp9JC8sBZ/PgTnicHGY1t3d+dZ17dDJCfQuIQ9ojpwl3CJXr/\r\nxlY50gubfHZmC0jonZdWmDeFEJqCDjSn1quq6hRSw/rpRpzKw34qYyEtT+w3\r\nkG8bC61xplwZxmrlE0mwQAbyPTsvAgi7M6W8pRdhdjLUFo2RMwwKmvhnnUbx\r\nn8hOfZeqrQ52tq+k55It4Y/ZZQph7/ov8GSwG8RRYceomt2gFwb5lNg4HZir\r\nbmNz4Huu5cF6oFzVYinmkKSghllkjcKeBEcxrIQVUmW9ycTuRe8h5447SrdV\r\nk1eL5530HQmVLSGUE8CczSwrLED0IUF3srA=\r\n=FMgt\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "5.16.0": {"name": "undici", "version": "5.16.0", "dependencies": {"busboy": "^1.6.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.25.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^15.0.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^4.8.4", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^7.1.0", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^10.0.2"}, "dist": {"shasum": "6b64f9b890de85489ac6332bd45ca67e4f7d9943", "tarball": "https://registry.npmjs.org/undici/-/undici-5.16.0.tgz", "fileCount": 137, "integrity": "sha512-KWBOXNv6VX+oJQhchXieUznEmnJMqgXMbs0xxH2t8q/FUAWSJvOSr/rMaZKnX5RIVq7JDn0JbP4BOnKG2SGXLQ==", "signatures": [{"sig": "MEQCIEdNOTPKEJ+C98H29kCYBWlbygipL9dkPPdyTNcPJny9AiAM+I6MrfH3DK8fhUh+UlGN4mBYm7qeEAzRQ8i2oXIQHw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1069513, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjzigDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmprJA/+JBteowL6P+0GJqEwskP+MYR24Lt+nY28LhU8wI9xfgrp1Rie\r\nDHPxEMZkaCo7i92MxU6EoyDYvYvvCtiC50A94gTmkJSnlFKVVQg0aaCyvmxR\r\nH35Xzm8gGCbn3+4Fg5cP3Y8qtnWxCEbGmDVJzERJrvUw/kpMv4LSq3qUd+Cv\r\nT/OdX6bSYvvzgRU/Afy7J9j2InSUQaXitFr3MoKTaW8UeoUfXgYJCm9OlOGR\r\nA9poWUYA2/4nRCNseuGUlomgDJIluuAaKpyoqFju/Fj7XOq/DoQUjrkMIvVf\r\nNq88L2/eYduAiavlh3xo0Dq7NspbZ+cBP+YPW9gUSG+LcSV8FGUXl258QEzk\r\nVAJW/F+R2a4teShVhQBUpG8QzuBXEgIbPlqTLzgOc+10sVNnJImcOctLFARi\r\nLi4RL2h24GrTMDlOFxdNUYiS1ZA5weSJ93HkTRojo4Ym1WXdqhjs6HsqrI8p\r\nHgGF+BdbcT+pC2wnyXiFJmMXPj9cQtpDFpoI2vfxKwxVcUvsd3vyM6AtAFjC\r\nCT9qgOsg36kMCVT4lLCQhQMFfGC6hp9GfoTTbleJINbVV1+Xv06apzKw93ZH\r\nB0P0HvRno0LDuTTlXS3BPEzia1LAFM9Ygs/6eWKHpXZBEftWTPCFHXCiNZqo\r\n4klE3ZEwTI+ZnJkw8pNhtxRsU8LRRZRRKBw=\r\n=Jd16\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "5.17.0": {"name": "undici", "version": "5.17.0", "dependencies": {"busboy": "^1.6.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.25.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^15.0.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^4.8.4", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^7.1.0", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^10.0.2"}, "dist": {"shasum": "a6c6c1c1ddb1452645003a510dfbd7eff02420ec", "tarball": "https://registry.npmjs.org/undici/-/undici-5.17.0.tgz", "fileCount": 140, "integrity": "sha512-XSmEUQS8cVL6uV9ZcrnkHY8ZCapFkYcJf4wAPAecOvUm9BTaXo0quzzODkmJ+3FV6GfgAAjSIpzm29lO077iog==", "signatures": [{"sig": "MEQCIH3ZkCBRxRXrjsqJDU65eY4m2sCpvg95+CDuw+BXAjZgAiB4f9jXZ9d+bBZgbarHKHIOy3mMWnfEwhen0JKBa49lGg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1073150, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3j4cACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrBgxAAmS+YlIO5SYFO70u3d2aQ8HTbyFbudBIExX6c9Lv7jR6LTaRh\r\nFT/SDZ4oFSARpX4s7M/Otji7H5O0R/hP9pKI7YyuWeJZvRcki+WPr2g9fOcO\r\neblpTEGrPfH2wQD8PW18mefka2txBqawxBWRz+flbTgAGNySELZhl8oZ7cmK\r\nB/rAOowpxdfUhP7ol92xt47/99oJajKl1hQ6ntJ9syDPn0ugLT4gFgbsKfVI\r\nbBxWdaactJDq94GYGcI8pzdDc4a8gevXcvtBulSIwNhztUjgMjoU0BnhnPei\r\na5F/GO9o1NUqSrSAOrnEGb7M0eUaIj1RltpBJ1MEl5q7Jwqmq5odMi9Mguie\r\nUUagq4z2wlEIsF0x1wBB8kf4FBZQgu0Z+pKjEGRuyu4AdfsadmLmSVO4IIJw\r\nGJ6OqFXdp1Dpjxy8IN6v4WkAqhT4CAProrYVDhxuwyCntTTCbFr++WG40Hyw\r\nAGbFtHMkwAXwZme2U0LQCNBZrxd7VYa/Ze8lBXYu9rJpd/ya9Vl0aCWk7wEc\r\nbVmWFXjw7a7tiABlziVCx4w5/FLuxQXy49VfvGS8MB25z3p0lb8C5smFQ2/D\r\nv0ifCYt3/fT7XVLFQWH51WIJzHX1Pujln9odropOjcurtMWGlcP4acufPZav\r\nWxfHqA2HwDorBwjtJndykHiz8LsvgycNImk=\r\n=rSYA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "5.17.1": {"name": "undici", "version": "5.17.1", "dependencies": {"busboy": "^1.6.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.25.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^15.0.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^4.8.4", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^7.1.0", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^10.0.2"}, "dist": {"shasum": "94fa53eb33d512be6c5e2bca58cbd3e6c2e26806", "tarball": "https://registry.npmjs.org/undici/-/undici-5.17.1.tgz", "fileCount": 140, "integrity": "sha512-qMwK+1tmJ6DsMlsl/AYsZXnkIraXmQzzBuPSiQXP6q0WvNO+dwfiBAhjZWmxxW4N0PS31fzB6ulDOUS6cv8oKw==", "signatures": [{"sig": "MEUCIHSJvZQC2oVqKQJqr1Q5FP+Z+/S1j7O96KBM5z8OJQK6AiEA8UL3WDG7X+msIxOB+7gpSz+N2eyn/9aPj1lt/Meqo2w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1073270, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3kbjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqRchAAmnIQjvlrMhJixF9s0JrIVFm5ax8R0DkSTEp0wSKGr9sBrg1Y\r\noaCYAbXHRAWq2F3a3oPRJJjozTkHo7b9HdNu3ydlMG+VUVpvr4RtVdCe8E8g\r\npuyLqf2z5SaKSSb8JSYVW4ZdiijcN4abZ8X3OgVKDCgm7DpedNS3iopOZ+h7\r\n+JE7Awtv0VMxLVfv1JlfzJXpYasVXD/RBT+6WR0Znon39MRslof26Hkzqjvk\r\nIiUkUBs/jGyDNinAkX7fnENHZxJQ7FulQZatGyp/gBWwk9a6BR7XfU8AP/kd\r\nsz2Fv98dC9U+woq5zQd8s4TfWKvFdQ5eivbQtJhcesJHdQC7l13kj0ss7lmz\r\n5JGtGNzJRDSbS4Ofbcgv1JmKGttARA0Aag7NlbCA3HU8CF+6DFPLPCacaYZA\r\nwL4i4X70mkJBgm1uunYK4733Ula7Nxyn0VeAiJZIWBuKZKGyhboU2WMMQP/G\r\ndkWioCqufwngF2xkdQz6VOKXT3GRRgI2ZbgxhWa+eNERdy4VP5BSwtOOv+5F\r\nIb3r9PLYYowEa1f2xZQeN94MJhmkDHx+mzEkYIqw/FnAiEbx/gAPQaulrWzt\r\n0xonSoK0IyD8yymCGwr3qPisNbHbfWvDwYavllmVAIumM8vI+1wlABDpZQTn\r\n0A+J7eZphxGpROXLjtRgkvEin23JB3zuh/0=\r\n=PNix\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "5.18.0": {"name": "undici", "version": "5.18.0", "dependencies": {"busboy": "^1.6.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.25.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^15.0.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^4.8.4", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^7.1.0", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^10.0.2"}, "dist": {"shasum": "e88a77a74d991a30701e9a6751e4193a26fabda9", "tarball": "https://registry.npmjs.org/undici/-/undici-5.18.0.tgz", "fileCount": 141, "integrity": "sha512-1iVwbhonhFytNdg0P4PqyIAXbdlVZVebtPDvuM36m66mRw4OGrCm2MYynJv/UENFLdP13J1nPVQzVE2zTs1OeA==", "signatures": [{"sig": "MEYCIQDDLUvThP9Dx1+mCMW5Ju3PMiI3Lh3RYFXwxFW4ngbTzAIhAJMtmvtbKLpRSfa4gXWLBXymA264uwpi5j5xYHLGowse", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1075540, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4KdvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpfHhAAol0R8RcPNtgDsG7s8rDc1kIV5pFsqFqKJe1/OSkV2n2uGlYm\r\nGPgy+w46NxB3YfOWli4AJTYfvsrcl0BCbO/otnjLseVvpcI1M3mwVpgwgNHy\r\nJRozq5gsOyfOC+vQOYT1mYZJGTRrrkg8Tj7caz/FTBH3G3YB4yQuj6MdVcKC\r\nS8V5UlwAidzp3wmHeIFDQck1ZgdQyoajlvZU4kK1BsuUXF9FNBdgL8Q6H5aS\r\nGDl8lvoO4T8b8T6qWEUQgHjNSwuKhRlTQLR8WbskRz/24TjnAqb3uhb5XRFz\r\nDLpiPj1XPd7zU7nmXuW30iU9F0qo8YouWoluwZ03lcUSoc+GICO3TRjFiC1u\r\nFrpAwp3O10L2NHR7vay6clTa9Oanq2C1ZlDhaRvo99g3FNcIrrUd3zbbUjhZ\r\nIkEdJZ7szv/kpyQ7wA7y7JGT6nRE0KzxemQwFHNHoglpv1BlXzbBR3VSl9/5\r\n5EWN8vd5Uj8mWE9AWSxhY5ANb92pgNiYYX4SZFxsmvrqfTI1nSVxzOZ26pa2\r\nwxW0jYgooY7fR3KLlBjMIFBGBvUTm/zBcelDU5aGpEHVxWnpMF0mMoCzK8qu\r\neyAj9eJfgskfDs0XpuSVooZAd0wnv1wMMxTgoMmCk/FHk/dQpxbMgniZJwUl\r\nBqMX3DS1zJl6+1Nt2rW49URIUq5s4AOpdu8=\r\n=SAST\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "5.19.0": {"name": "undici", "version": "5.19.0", "dependencies": {"busboy": "^1.6.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.25.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^15.0.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^4.8.4", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^7.1.0", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^10.0.2"}, "dist": {"shasum": "b80b3627c95e20e9ce02925ec64c4fc39fd93320", "tarball": "https://registry.npmjs.org/undici/-/undici-5.19.0.tgz", "fileCount": 141, "integrity": "sha512-7XnhXXW0/AMTx17x1T3OOYY14FGiXbkyE/itv4jY1v8CnSQBMXW3yZ/Ppt7YlOv1D63tm9N4g5Au1Abe+gwpkw==", "signatures": [{"sig": "MEQCIEVErKiVUih3xF+IN413Uu15LmxkTbUkWrUhCgXuyc9wAiBMystuaME/qUAixMxVf+G790wv5hM34LbenDVxYlaFFg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1078357, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6hBKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo3FA//Rygks7UDh1HsdMqfl4XHa4t921aN36xKbmFaj32Tz7tLGoN4\r\nspBBU78mD+9sPheqn9gLEwQYQbHY2i4LUhPCK4gMakywDXLqZVphmejRNznd\r\n9kEGHf9lI6kdSNEoPdwYfGYeX29VuXenw8PQGis2LL7E47rqualV0LygOm5W\r\nWL3pN2uS1ljH+8Qy7dZD9MalRlfenLpRdptJL3YE0qvPGUmT/ie9lmEmAvvR\r\n/In5R6aIOKu0ajAI7HuanbtSt5RbdmtS7E//RgcxADMCCsmTMv34htEGOi+5\r\nRup9lD4Fnj4SnerTN6HvJwyeeweLzTmfeu3vH/nlIJpROmL+rRDKbxrmlBV0\r\nXpM4Kv41J9kjOLXU7RazEi1vYpntndMvD2vCLDHigGZ7OS1v4/3Qux5lUfe0\r\nwsm2VivFlHlEBVOQLz4zVEmaK0+1arNvSpda9wXexNBC1Ak/CMKzsRyNTpv1\r\ntuKpH9g83dlFrAWdWvSVfHjP7rfwCdaXK27wceKXyxDW0CmGddqjVofR8Hvj\r\n1E6TGnVJzozRN8axhVUXPHjGeyDxFEIrtHtPjUYj7IIkbqJC+44kWu3YcIjh\r\nkFzo0azuipMUelMIn5paG3V/0D1p+EOlLmmjyKiZVYpHewlBHSoShmociGYF\r\nIFXACe6LrgCWnAIud5L2Fq33sZENTL0CzMc=\r\n=6Hc4\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "5.19.1": {"name": "undici", "version": "5.19.1", "dependencies": {"busboy": "^1.6.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.25.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^15.0.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "semver": "^7.3.5", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^4.8.4", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^7.1.0", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^10.0.2"}, "dist": {"shasum": "92b1fd3ab2c089b5a6bd3e579dcda8f1934ebf6d", "tarball": "https://registry.npmjs.org/undici/-/undici-5.19.1.tgz", "fileCount": 141, "integrity": "sha512-YiZ61LPIgY73E7syxCDxxa3LV2yl3sN8spnIuTct60boiiRaE1J8mNWHO8Im2Zi/sFrPusjLlmRPrsyraSqX6A==", "signatures": [{"sig": "MEUCIG0imvI+fbtdHV8acUBwcwiPO3MnmZCsWBTaa0z3f+dUAiEA2y6HhhgllMTDw3KkVgwNdPvC+hM4EVBWid9X14JDV/o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1078666, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6h6JACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpOqg//UuWHwHgVviGTxDFEGBIEUfMARKhaBIZC/W1Dbp/5bOCgTbrZ\r\nKljjmcqNUxHj6Rv3yG+RNfVqkfar6K2T9pD0sK+xwxpSL2ZGr4q2wXstYKOo\r\nV1WShe1JCZGJSYTzWuXYLtuhu5iCxOGPd+//R8NzgYY2ZIwHcO0or1jAKzzk\r\nfpA9w5dKGPgh7ZgoDi3lm21SHR708DJj2Ht1hnZpswDXp3iI0B59RZLvK6P5\r\nZFSQJhhmsHu9qX8QTxx/wBPc05p05VCTwsygtXGtJjQbH2QLxViRuYFk5rL4\r\nGFBzAREID6fPIPmQiKtXcEvHbYddmutYdHZ7DMDiNtQ039E5uLnRl82v2iF6\r\nLbCdkLvMeuY6z2I0Jfmn9wld+FA8wWLtsXlxfgAT1v1C3IQIK4p0a7s5zPFf\r\n9RCyHzF3Q0FIFNJCZxttq02G8E2e6xMvjg+c/+jJOwZGdCxUS1qqez+ENv7q\r\nonwXwc89X2p6LF40xF2hDL7jfYlOS79/ZCx1cibCGpc1B5KKSgkhBf3MzWq2\r\n7YBeZN3a4AxASxIUlJeai59Vkinz5O5BLS48xFhdm0boWyNiBYmLatvwIGoG\r\n/4oNVSbiTXdalBxrezT9sqShOLftAaBYeeJSnOooZLnA4iZHmsLdZ/Al+NE9\r\nIBpqxu9qjz7s5UrZBnCtaGwF8Edw5JG3ydI=\r\n=SXBR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "5.20.0": {"name": "undici", "version": "5.20.0", "dependencies": {"busboy": "^1.6.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.25.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^15.0.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^4.9.5", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^7.1.0", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^10.0.2"}, "dist": {"shasum": "6327462f5ce1d3646bcdac99da7317f455bcc263", "tarball": "https://registry.npmjs.org/undici/-/undici-5.20.0.tgz", "fileCount": 141, "integrity": "sha512-J3j60dYzuo6Eevbawwp1sdg16k5Tf768bxYK4TUJRH7cBM4kFCbf3mOnM/0E3vQYXvpxITbbWmBafaDbxLDz3g==", "signatures": [{"sig": "MEYCIQDy40RPwGKxagJaujMz0vZWPKtRVJGbayf/8pKpq6mGNQIhAO1voe7zHixXz0f0H+R9oO8tkh+ckO93sO6ZxeKU9KUp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1081120, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj8JPVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpdRRAAiVj8B6XOgWAG1kVOilwUKVskkvH9+y1rtUDJxRjuc7tkLA8J\r\nOh6aQfFWeygjxQhow+jg/Dvixq/LqknNl/5P41weM7LMfM+ZWuxbHiWLOVqy\r\nINI6FatBKmbK0hzg+v9pCI2uS/sC2AgwcWUX3XT3JR46/vV/3JN85Z0LStBK\r\nrtnKYTdzSTzq/wiXDq2JBUXzysXTrTjNfKqCKtAR8mLnGNCIEHFiL3gZTOHT\r\npLsTbGd/7rWRNEzTMg9f6Jzp6iX8FjTarSrEpFGE1+o8kpqWTr0+vBDxsnEN\r\nVbMidcRbr1rufomUdloHuEYw8Og848HRuogY4CLa7JXn6Gy4ZdJT/weyh5o6\r\nAez4GTF9YodOHkYLufWOYPCXX+uhYwLk8RTBkJ4vVv8ibF0l0gxRYw9Dezqg\r\nDXhSV6EZliBSM7RXqukJvtg8ckGztT/xp0OvN+e7SyL6ZKaDZtMmyNJuQb3V\r\n18sldjZ6zvrMk8vgtKv30Dctz57Ko1HLfHa98Kv4Kn845RbcEfNtSqQddjtS\r\nta5J+3nLeE7JIU//eUILFRDQWOsJwgPM4HHdqF9/szUkw3TMGOZCiBkxsxeG\r\nOoHHBoBz+4v3+WAAR9H21JTuTp5Z+aLLoeuvVIrb7fevLDXKRXYyYhVE692O\r\nsKGwI8pDI0g34/hY/2GpkQq5zd8WRWBu/Mc=\r\n=VWpy\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "5.21.0": {"name": "undici", "version": "5.21.0", "dependencies": {"busboy": "^1.6.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.25.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^21.1.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^15.0.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "snazzy": "^9.0.0", "wait-on": "^6.0.0", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^4.9.5", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^7.1.0", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^10.0.2"}, "dist": {"shasum": "b00dfc381f202565ab7f52023222ab862bb2494f", "tarball": "https://registry.npmjs.org/undici/-/undici-5.21.0.tgz", "fileCount": 141, "integrity": "sha512-H<PERSON>jK8l6a57b2ZGXOcUsI5NLfoTrfmbOl90ixJDl0AEFG4wgHNDQxtZy15/ZQp7HhjkpaGlp/eneMgtsu1dIlUA==", "signatures": [{"sig": "MEYCIQDevc91TYXzQqGuqoKXyntvVi8+HcN4FRJx/5XlGsKEFwIhALgaNJuD3ebum111yoXl16MwyfsHMXQk6SOOR7EYuMg7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1084672, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkDwffACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmohrw/8CkXNhb85J4hJPO1J/YosCvxeSUOZUq8ecz7/jLBxvJ+oa3u2\r\nP4fTLXEJfa4DB5f42YxnZznq+LlDAkixiJS7epkinuvRnA8Zr9a/SsBl690a\r\n+jgKMEBPcJSAu/dJHStx+gIWcmLwmjnUdqaMiRKCxMoWaeLxBBVT4wI7boP8\r\ne1J1B8DocKUnjvjBtFbLulBM6ol9emvwUO3a6ZU1Glbb2Wz4HjUqHim9tvMR\r\npoiYo2S5gVfD99iIA0fldqspMhJzgFCeolME7Ns4wCNukhoQYaPwQFASFgge\r\nXgrtyS8eewnbA2EcF1Wuwo2DCYLE23HVR1ESLX8OzK0QVvUSsAZpbrxESebK\r\nB8/4AV0pLGxXde2CeLlqfkIJ3zvn3NksEB+1po632lDdXzyw+gBdPTfGHTK8\r\nlsH+hNqCHKXTzt1h2d+kVRIq09crtObcX0qrnv/T6BA/Pg/88Rl+AXo4hWdM\r\nBUayuBLkHQf12fh1qx8V+0VyHOBvNY5XjMTNnzca8mCnPT6b/LwWb3sdrzxn\r\nWUaqjNNNj2afhF3CO9oxcGNDJiNPjEHtefICk61xC4EhzXtOtog9AQYudeXE\r\nipchN+ACGSrBHBK5DfTAdM0rZtwG8NOhcrhMoJBOgVCjZLdlg9KjBQR67KtU\r\nJ71ZLPJnx6DiYLsnit2uIl3vBg2dLxOA04c=\r\n=+vXY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "5.21.1": {"name": "undici", "version": "5.21.1", "dependencies": {"busboy": "^1.6.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.27.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^21.1.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^15.0.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "snazzy": "^9.0.0", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^10.0.2"}, "dist": {"shasum": "2a07d21793e433ca56c2a38e9fa3addb97fb901a", "tarball": "https://registry.npmjs.org/undici/-/undici-5.21.1.tgz", "fileCount": 141, "integrity": "sha512-JptL7wJkvuuZfevy0imEcM6P/Q40uI5msocbfrE6KguU6kz/pFk4L0A6//XbMIOq+FSIXxgDMAOgfeH+S/WFaA==", "signatures": [{"sig": "MEUCIDMpB9u92UJQxtfRDbxjanbRMObI/hcNRR4INxVlhxdKAiEA+bSie2aogJxdF94KgM24KT6XwIHWnNT46bqjXTGJbF0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1087160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkMZNzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrBtxAAgioO4JkNFFO2jOd7zQiEqu3/vXut8Cmd4yMJ6raDlEAu4QRw\r\n2CYE1Z/edWPIDp5Vzue6oZneQbN5yDKIE0mt5SIxTf/ZsSeIRa9z3PH/wKht\r\nZueC9euz+9luBfhHuspWN3ymCzm9k1dAsPLghqvHV6d/1/Q7ngna05mXD2rC\r\nkCFLTas9Zn/po4f7t0Rsbj08qcor3zOnZe5UokCAVPSFrYFTulRAdPhe4TZO\r\nxf2kqRAOzNF6aMtZtPT2aHzY9itSJT82jQsW2ang+UQLC+84K3iCIvnNknXN\r\n4HI7/YAYPq50BzLeJccg3c0J7MDYZy7KwF3+be369+SrUjuD59Ld9b7lTHy9\r\nIYkYrAbhv1lWh1GWckJ/gaIv+C5ecjZbKa5Um1ianBnU90hCrE/mrzUeVRz0\r\nLlWrwkCX6jX0ExOWkBOwP7hPRRVzmbC8cvIXY3moR6n1V41zdjYUeKsSBMyw\r\nPd0zOMIso5wWWAioofAHcJYQvIyEgRlBAJohHtrQ+zbuBE1naDg1wGKTRLI/\r\neI7HaTEWHkJi1WWLZqb5MnpVCq2ZLTaWbLal7+qo9vLJfLdElA4jQrLB5vpG\r\nm5ngn5LVhk0lGSxX3rXrpOU4rsE6qNNG2yUrlP6Yve/UEtt61otylEyfafM0\r\nvxgTMPshxk0CooGbFD2FIXvGlwnJDotV53o=\r\n=jGZa\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "5.21.2": {"name": "undici", "version": "5.21.2", "dependencies": {"busboy": "^1.6.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.27.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^21.1.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^15.0.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "snazzy": "^9.0.0", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^10.0.2"}, "dist": {"shasum": "329f628aaea3f1539a28b9325dccc72097d29acd", "tarball": "https://registry.npmjs.org/undici/-/undici-5.21.2.tgz", "fileCount": 141, "integrity": "sha512-f6pTQ9RF4DQtwoWSaC42P/NKlUjvezVvd9r155ohqkwFNRyBKM3f3pcty3ouusefNRyM25XhIQEbeQ46sZDJfQ==", "signatures": [{"sig": "MEUCIQCta2+isugpwgDO0J966NHufCxz5F9VvHyz+49F3UQyZAIgXSbSpOedNetPbtFsHcfg4U7Kz62kfwFbi4RpOGxGm0A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1087787, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkMkTJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp21BAAhttkCvj9tWaB9rD4UdIceQnddREQzfDLjTOQJgR+BKZ2vFDb\r\nSHCW6tUpLStmBVtENZJExkao8s++DdUhRqQYjVLXyh0x6w1tWruLD3Wyga7i\r\nyfmFypDqO7CSyf9RVYVFy7hj4XclyC4K6BVaNz00FGZZ9D+TlWcls+3lZZ2B\r\nSiNoNQAR8sofs1zFTmfii4PVuFeD2AQoJZpiW1Fy/ZMP+SzGVz8zaQg3FhX2\r\n7qjPcUnKb862A+0eoQOcrt7Cxj86NcSdVGUZFGF+Jyfmu8Eg+d98zgcj17n+\r\nOyPtQ4dZ5xtF95okTPPCHQynPmC8x55idrI0K44sU0jMujKf2C4FR45bdNl7\r\n/k35azDZlBH+m2liZBltu5Q6R3gVSINoFt38Ldl/FU119flfXOeMKog4YNAA\r\nMgdvpH1dakjVtuxGZnIYP7Cfewn96MexJVz7DNQciE1DS16NRGO3/CQPE1eR\r\nrPSI6BtuPnPioCIQxIuBUrOQRrtr8QKmPwEsLjIkHOKkZY2W9fpLSeoGsNwT\r\nhLX6DsQyvuMYzuFgnYX4pcNZslpeQYyv1SAQKw7iIF2rQiGuIUed+jQKVfRg\r\nfGNFXmLWPtKbHFfEDbvWg7xqAGtDwU+rprdQyT6Lw9SRRSclhRIste84y7ph\r\nggT3GbikzbqKTyJOviyTEcr7t2SvQpdRN0Q=\r\n=ki/k\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.18"}}, "5.22.0": {"name": "undici", "version": "5.22.0", "dependencies": {"busboy": "^1.6.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.28.1", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^21.1.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^15.0.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "snazzy": "^9.0.0", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^10.0.2"}, "dist": {"shasum": "5e205d82a5aecc003fc4388ccd3d2c6e8674a0ad", "tarball": "https://registry.npmjs.org/undici/-/undici-5.22.0.tgz", "fileCount": 142, "integrity": "sha512-fR9RXCc+6Dxav4P9VV/sp5w3eFiSdOjJYsbtWfd4s5L5C4ogyuVpdKIVHeW0vV1MloM65/f7W45nR9ZxwVdyiA==", "signatures": [{"sig": "MEQCIDqW0Q6utDr+mMQdRbUBJuwpACjkvWAc6NSYT0Jn32hzAiAX995q90zQm5X3DqoEQl6vGKo6oj8avN6vwdnfKr3aJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1088722, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkQVU4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqksg/+Mlz7tzp4uJy42NAxCLnVlNEaVPLbigEhgXFCOXHWhRXosjCr\r\ndJevnHbeNIAflSYDoiFsRrXE8RfnN/KjBavbyOVXIX4tRNY51wT0QTdsv41W\r\nfH8QrQxu38kXnFNtbUWZZOmhux6I5qqFZ4gYrX1nqpmleR1bxOAz+Bpy5DYo\r\nlMULBgPYktvagH/rx1lQ9MD6cdmrHuwBYgcZ7sXZEvsdTV7QH7UFZitYl3eE\r\nLRHXuNdE8VYJjd7fVsNUHRymcdkFq4XE/J+Cmf7PvMcdNwhsitX6BHSCaEi8\r\nB3eJrBYvE3zqcDXw4PMl7hD3Dho2qbIcz2ck3TUYUkmUuOhGyoZ+Z1bXSdfe\r\nNx80NEOgqcJAxqxKGld61K9q3cNAhj5kyhsJsqP7VlVBCeQFCzwgpRo4rMNq\r\nNLQullr5XelDrqfxNQ0QJfIbJ+ytaU33gVFQSP2+TFJ/PFY0eLZQiPRI63Qn\r\nBhJf6Bk3KauzaFjBc17Bq9kzXpkbpUo9KDsb6e84ZPi2riRDAE2EhJxHdcQy\r\nReU46yXN47sEkauQsvQEI0hLVTLJnyNDCZcEMRYZY/3zjKauxP44QZrsKsBM\r\n/eJtzw2zs/Bw8pFEWDeydLc6nLv1va7IxjNplKYqA1KJfMWeBGm0s56fUYzz\r\nw76C3aZyhhOzMjeY1MFJtY5C0RG+/t7TIHc=\r\n=lAP/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14.0"}}, "5.22.1": {"name": "undici", "version": "5.22.1", "dependencies": {"busboy": "^1.6.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.28.1", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^21.1.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^15.0.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "snazzy": "^9.0.0", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^10.0.2"}, "dist": {"shasum": "877d512effef2ac8be65e695f3586922e1a57d7b", "tarball": "https://registry.npmjs.org/undici/-/undici-5.22.1.tgz", "fileCount": 148, "integrity": "sha512-Ji2IJhFXZY0x/0tVBXeQwgPlLWw13GVzpsWPQ3rV50IFMMof2I55PZZxtm4P6iNq+L5znYN9nSTAq0ZyE6lSJw==", "signatures": [{"sig": "MEUCIQCtL1EbtU64UnabVntio9j8NMiDJeMD96p1Te4uxvrkbwIgbvXPbnKlBzbKnuiNSiZDEEON7uUqr4y8cPt8iEvd07E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1122723}, "engines": {"node": ">=14.0"}}, "5.23.0": {"name": "undici", "version": "5.23.0", "dependencies": {"busboy": "^1.6.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.28.1", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^22.1.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^15.0.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "snazzy": "^9.0.0", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^10.0.2"}, "dist": {"shasum": "e7bdb0ed42cebe7b7aca87ced53e6eaafb8f8ca0", "tarball": "https://registry.npmjs.org/undici/-/undici-5.23.0.tgz", "fileCount": 149, "integrity": "sha512-1D7w+fvRsqlQ9GscLBwcAJinqcZGHUKjbOmXdlE/v8BvEGXjeWAax+341q44EuTcHXXnfyKNbKRq4Lg7OzhMmg==", "signatures": [{"sig": "MEUCIAlNoL4mEXWOsCg2Awb92eERfcX224CMRxcbqH3ynqutAiEA29mUtRF7Syq7GiQLmIubwWCW+X4/OyerTNo7v4vmCU8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1122830}, "engines": {"node": ">=14.0"}}, "5.24.0": {"name": "undici", "version": "5.24.0", "dependencies": {"busboy": "^1.6.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.29.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^22.1.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^15.0.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "snazzy": "^9.0.0", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "6133630372894cfeb3c3dab13b4c23866bd344b5", "tarball": "https://registry.npmjs.org/undici/-/undici-5.24.0.tgz", "fileCount": 149, "integrity": "sha512-OKlckxBjFl0oXxcj9FU6oB8fDAaiRUq+D8jrFWGmOfI/gIyjk/IeS75LMzgYKUaeHzLUcYvf9bbJGSrUwTfwwQ==", "signatures": [{"sig": "MEQCIHC7VeDbobaU5jaAQ7Qs2TipRDYwZLimdPNVtvNTvTMFAiAk1ElDNGsiKFD5a1yL6tUbXaRK0uFKlsTzsAPNWri1lQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1141652}, "engines": {"node": ">=14.0"}}, "5.24.0-test.0": {"name": "undici", "version": "5.24.0-test.0", "dependencies": {"busboy": "^1.6.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.29.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^22.1.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^15.0.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "snazzy": "^9.0.0", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "a40c02d79c28ebc16aeb33589ecb6f683bd082d7", "tarball": "https://registry.npmjs.org/undici/-/undici-5.24.0-test.0.tgz", "fileCount": 149, "integrity": "sha512-/HY2w9ohBhX+eXsoAET8XAogZneKBgku+m1fS7fuk0uVmir7PKnRLCRZiN3UHyaWeNITao7EWa2EOaai6klsIg==", "signatures": [{"sig": "MEUCIHNNMwnWfG1wqTfi0Y1sFUGijJBK6Krf9nlUsurkgt+fAiEAkFA3jvVV1sKJja54bOA49aOdcBOUxLXFea24AIggdDA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1142308}, "engines": {"node": ">=14.0"}}, "5.24.0-test.1": {"name": "undici", "version": "5.24.0-test.1", "dependencies": {"busboy": "^1.6.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.29.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^22.1.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^15.0.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "snazzy": "^9.0.0", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "d016263c334530602afdf258ded1b5b6375278b5", "tarball": "https://registry.npmjs.org/undici/-/undici-5.24.0-test.1.tgz", "fileCount": 149, "integrity": "sha512-NvRRH7jgPPS22/lM7mln19wiSafOPwMyj2dj3ndvqNELou0gBXBFqN5Bo7ZFNSV5D7B5VZtUiJeobcw1yRchzA==", "signatures": [{"sig": "MEUCIQCjU2/pgi+n6hfm77/zWEf3XQIUM8GkwI0sfDVzj5+ShgIgGp+R/wHx0BLhiDZdNVLYaLL5CzUGXYUnKJhuayDGiwc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1142308}, "engines": {"node": ">=14.0"}}, "5.24.0-test.2": {"name": "undici", "version": "5.24.0-test.2", "dependencies": {"busboy": "^1.6.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.29.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^22.1.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^15.0.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "snazzy": "^9.0.0", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "789810fa7d10e248a737864655fbe5f380af2749", "tarball": "https://registry.npmjs.org/undici/-/undici-5.24.0-test.2.tgz", "fileCount": 149, "integrity": "sha512-9PCEAOkTWWtBPdeI9FxDg7c4/oUA1TwNHHI/81yWe/5PMLMDx06mZbuArjKiWClxKZcx2Hlb/jhMG2QVDbH4jA==", "signatures": [{"sig": "MEUCIFa8aXId0LC1HFIGzPQGmxsySMbxdoc6Dwl4hAbYXePyAiEA3phq1vFX85VEPIq8yfKVEjKaE3PnFpMHpJunJihqYcQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1142308}, "engines": {"node": ">=14.0"}}, "5.24.0-test.3": {"name": "undici", "version": "5.24.0-test.3", "dependencies": {"busboy": "^1.6.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.29.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^22.1.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^15.0.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "snazzy": "^9.0.0", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "c4292e458c30ee7186a3857ea9a1799cc4fb7f1e", "tarball": "https://registry.npmjs.org/undici/-/undici-5.24.0-test.3.tgz", "fileCount": 150, "integrity": "sha512-t5tyateCeBRUubwj2HzIXYHFhXyfLhhZFsCElf2tdjCnEbbvaDVi4ujNapo6Pf6R/HHMLBYoeeRjUVF8mUTfAg==", "signatures": [{"sig": "MEQCIFMTge6h5FfB6VAVlOZZYTdg0j7+IsU/X4WVoXh/G+OLAiBwi6tO/wHog19JpjMd4uzks2hnmxSXjYxsFxw87nyGmQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1142817}, "engines": {"node": ">=14.0"}}, "5.24.0-test.4": {"name": "undici", "version": "5.24.0-test.4", "dependencies": {"busboy": "^1.6.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.29.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^22.1.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^15.0.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "snazzy": "^9.0.0", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "b6c1b5f511b8aa323218b33e18042188cece3ef9", "tarball": "https://registry.npmjs.org/undici/-/undici-5.24.0-test.4.tgz", "fileCount": 150, "integrity": "sha512-sCYgkZsGM0uc/pRQFXi/CxM3TUzKqDMXAF9YEIkvaw1NzR77U8c0FH+kF44UFpENI4fWwTrwYmplaHhSp3kToA==", "signatures": [{"sig": "MEYCIQDWco3CHjGXt86B8ZkxknNnzIWSgKikOjA3XlNK2GneBwIhAOXbxrkeSmRrg7Gg1CEiQ8Jy85D7Ui6A9UQR2+NtQrQx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1142825}, "engines": {"node": ">=14.0"}}, "5.24.0-test.5": {"name": "undici", "version": "5.24.0-test.5", "dependencies": {"busboy": "^1.6.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.29.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^22.1.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^15.0.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "snazzy": "^9.0.0", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "dd2f37536e3e7e555038983e5423bc1a3b299ea5", "tarball": "https://registry.npmjs.org/undici/-/undici-5.24.0-test.5.tgz", "fileCount": 150, "integrity": "sha512-zmCWx6zc1UJzGrHCYefMJUPEt4Bn1AibMJRGNxeLmKosrhXiWeZl+sqgrQgBitU3YOfJkWf9ZglnwdbuWuc2og==", "signatures": [{"sig": "MEUCIATU3Js2p8A7/eNrBjJtz9vpmcSGtxcr3rEFWeIB+pxMAiEAz9l+VB0Y9wlRxmW3mky5+D+m33RYBRWKCb8Smbx5HXA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1142825}, "engines": {"node": ">=14.0"}}, "5.25.0": {"name": "undici", "version": "5.25.0", "dependencies": {"busboy": "^1.6.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.29.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^22.1.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^15.0.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "snazzy": "^9.0.0", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "618318268e1b496bd8ab370ded86745aab3f68e1", "tarball": "https://registry.npmjs.org/undici/-/undici-5.25.0.tgz", "fileCount": 149, "integrity": "sha512-DgU98Ll+r1ssO8PcGFWIfj6kie0ttV20DSyE/CVYDVeHvfwBwQbjlsIYJIwAoU1WRhGuEEbj+jgZqcKPco5vkQ==", "signatures": [{"sig": "MEUCIHp4zefdhaFd8qgksZaFXb9bjnuk0LG4f/ekhWjqlribAiEA/C9GE8M0dSqa1aoqFdR3sXxjtKcnPj2L0xVbyTOp18Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1142288}, "engines": {"node": ">=14.0"}}, "5.24.0-test.6": {"name": "undici", "version": "5.24.0-test.6", "dependencies": {"busboy": "^1.6.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.29.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^22.1.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^15.0.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "snazzy": "^9.0.0", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "0dfb862c06dacc43dddf366f8c6e181f5418b661", "tarball": "https://registry.npmjs.org/undici/-/undici-5.24.0-test.6.tgz", "fileCount": 151, "integrity": "sha512-L8ignPOV0Gkc9impEtMm07xzFnCVfocuzgE/aj9hICiD+zP3FJe32zp5C2xt9xIXedxHHWdW5lDQiCM6/NtdOQ==", "signatures": [{"sig": "MEUCIDEg9L8J1TgCZ5HvbgR4gCSF+IqQlHHnYkWnHBZf6fIRAiEAyt62lHIckgqTYH3ANBSxPo5JqPub3Ui2sQJCdAjJ56U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1143950}, "engines": {"node": ">=14.0"}}, "5.25.1": {"name": "undici", "version": "5.25.1", "dependencies": {"busboy": "^1.6.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.29.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^22.1.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^15.0.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "snazzy": "^9.0.0", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "3fa537eb89a6a0bb576b395f0f7bcdedf7b8336a", "tarball": "https://registry.npmjs.org/undici/-/undici-5.25.1.tgz", "fileCount": 152, "integrity": "sha512-nTw6b2G2OqP6btYPyghCgV4hSwjJlL/78FMJatVLCa3otj6PCOQSt6dVtYt82OtNqFz8XsnJ+vsXLADPXjPhqw==", "signatures": [{"sig": "MEQCIGWNEmVZVkwhfZ1oYxWL4BmIwFF/uZjDytop3BwAaH8XAiBKWiREGg4l+7kwnqlQ0C8yVeZdyIkFoD+xDiUYcWK8Lg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1144437}, "engines": {"node": ">=14.0"}}, "5.25.2": {"name": "undici", "version": "5.25.2", "dependencies": {"busboy": "^1.6.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.29.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^22.1.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^15.0.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "snazzy": "^9.0.0", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "17ddc3e8ab3c77e473ae1547f3f2917a05da2820", "tarball": "https://registry.npmjs.org/undici/-/undici-5.25.2.tgz", "fileCount": 152, "integrity": "sha512-tch8RbCfn1UUH1PeVCXva4V8gDpGAud/w0WubD6sHC46vYQ3KDxL+xv1A2UxK0N6jrVedutuPHxe1XIoqerwMw==", "signatures": [{"sig": "MEUCIHlCPT6+q2Sve0i6nyj91OqF2I1QW45eYyTUrX95jUTyAiEAsOMKpva/o7RO09DDlfMFLVMS6vvcM5b/4fac35OcVrc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1144659}, "engines": {"node": ">=14.0"}}, "5.25.3": {"name": "undici", "version": "5.25.3", "dependencies": {"@fastify/busboy": "^2.0.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.29.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^22.1.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^15.0.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "snazzy": "^9.0.0", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "568c89a4bd773424f992642a2d6ddae7116dc749", "tarball": "https://registry.npmjs.org/undici/-/undici-5.25.3.tgz", "fileCount": 152, "integrity": "sha512-7lmhlz3K1+IKB6IUjkdzV2l0jKY8/0KguEMdEpzzXCug5pEGIp3DxUg0DEN65DrVoxHiRKpPORC/qzX+UglSkQ==", "signatures": [{"sig": "MEUCIQCb9NZEDmxKpz/ypbrSfxVihw5P+Iv7QLKkXCR8TqMieAIgcg53BLInlXygIhXRU8wDZwFKRSmVSYJL2Y72Qh5zefQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1144749}, "engines": {"node": ">=14.0"}}, "5.25.4": {"name": "undici", "version": "5.25.4", "dependencies": {"@fastify/busboy": "^2.0.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.29.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^22.1.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^15.0.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "snazzy": "^9.0.0", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "7d8ef81d94f84cd384986271e5e5599b6dff4296", "tarball": "https://registry.npmjs.org/undici/-/undici-5.25.4.tgz", "fileCount": 151, "integrity": "sha512-450yJxT29qKMf3aoudzFpIciqpx6Pji3hEWaXqXmanbXF58LTAGCKxcJjxMXWu3iG+Mudgo3ZUfDB6YDFd/dAw==", "signatures": [{"sig": "MEQCIFPC0PI9wVFnH2kF+BvFjQflARnAnbFaNn5IgVfDfjwRAiBJz+tdc+W0BL+nO15b+rKNm5Vw8z7vLqgfcS+3TdFt4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1143622}, "engines": {"node": ">=14.0"}}, "5.26.0": {"name": "undici", "version": "5.26.0", "dependencies": {"@fastify/busboy": "^2.0.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.29.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^22.1.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^16.1.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "semver": "^7.5.4", "snazzy": "^9.0.0", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "e3ea0843574b7abd90fa3240954ee82bf9c8fb3e", "tarball": "https://registry.npmjs.org/undici/-/undici-5.26.0.tgz", "fileCount": 151, "integrity": "sha512-MLqGMyaJk2ubSl7FrmWuV7ZOsYWmdF7gcBHDRxm4AR8NoodQhgy3vO/D1god79HoetxR0uAeVNB65yj2lNRQnQ==", "signatures": [{"sig": "MEUCIQD+2QDx2s4b8l4Jdxcr9cA+KpGz9NmWLTVcwejsQZ9hpgIgWmYV0wYlIOyRHHL7XnBnoBXbhriro2SnqnYg0JGU8TA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1144367}, "engines": {"node": ">=14.0"}}, "5.26.1": {"name": "undici", "version": "5.26.1", "dependencies": {"@fastify/busboy": "^2.0.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.29.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^22.1.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^16.1.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "semver": "^7.5.4", "snazzy": "^9.0.0", "esbuild": "^0.19.4", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "e4f36f2e79de7a35b35489327a06d844271b64e5", "tarball": "https://registry.npmjs.org/undici/-/undici-5.26.1.tgz", "fileCount": 151, "integrity": "sha512-M8fZNYGEcUCEp0s3t8my8wx1XnalnJZ0vcB27bZpllicFWdIzrbtWOPCtM8+7vEhPXqzw6y0CJZhXuJqkppe/g==", "signatures": [{"sig": "MEUCIHqCq3MrR8ytQZG4el+ofUsRjWvf97DbDQGJgAIwlCCRAiEA2MtHo9rzVAuV73T455hIQ6v22owPsUUdpq9HOU911YY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1144346}, "engines": {"node": ">=14.0"}}, "5.26.2": {"name": "undici", "version": "5.26.2", "dependencies": {"@fastify/busboy": "^2.0.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.29.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^22.1.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^16.1.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "semver": "^7.5.4", "snazzy": "^9.0.0", "esbuild": "^0.19.4", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "fa61bfe40f732540d15e58b0c1271872d8e3c995", "tarball": "https://registry.npmjs.org/undici/-/undici-5.26.2.tgz", "fileCount": 151, "integrity": "sha512-a4PDLQgLTPHVzOK+x3F79/M4GtyYPl+aX9AAK7aQxpwxDwCqkeZCScy7Gk5kWT3JtdFq1uhO3uZJdLtHI4dK9A==", "signatures": [{"sig": "MEQCIAvHBIYPaTtTRZ4nXrc+ffq/96wprmjF3hzTl/UA9t8QAiAcXSqXwZhdaFXK9fChvmE3YoxGfipDRMMQRPEj93z55w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1144517}, "engines": {"node": ">=14.0"}}, "5.26.3": {"name": "undici", "version": "5.26.3", "dependencies": {"@fastify/busboy": "^2.0.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.29.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^22.1.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^16.1.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "semver": "^7.5.4", "snazzy": "^9.0.0", "esbuild": "^0.19.4", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "ab3527b3d5bb25b12f898dfd22165d472dd71b79", "tarball": "https://registry.npmjs.org/undici/-/undici-5.26.3.tgz", "fileCount": 152, "integrity": "sha512-H7n2zmKEWgOllKkIUkLvFmsJQj062lSm3uA4EYApG8gLuiOM0/go9bIoC3HVaSnfg4xunowDE2i9p8drkXuvDw==", "signatures": [{"sig": "MEUCIQCv9EhRtbYMunfSB8s5al3AzdZYk/rm3GTNlZDcVaKnKAIgcWTfHGwM6nG83Z4vQEqCXTiVBYDOuitj/fOwS3Plc/0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1145093}, "engines": {"node": ">=14.0"}}, "5.26.4": {"name": "undici", "version": "5.26.4", "dependencies": {"@fastify/busboy": "^2.0.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.29.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^22.1.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^16.1.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "semver": "^7.5.4", "snazzy": "^9.0.0", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "dc861c35fb53ae025a173a790d984aa9b2e279a1", "tarball": "https://registry.npmjs.org/undici/-/undici-5.26.4.tgz", "fileCount": 151, "integrity": "sha512-OG+QOf0fTLtazL9P9X7yqWxQ+Z0395Wk6DSkyTxtaq3wQEjIroVe7Y4asCX/vcCxYpNGMnwz8F0qbRYUoaQVMw==", "signatures": [{"sig": "MEUCIQDJrmUk4VkNpF6c0dzBEvEheuJqu+m9DQsd1vIinilc/AIgPMpemKpejvbCW3fwHZtJaypJxhm05ZWW6CLLyS/HcFo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1144738}, "engines": {"node": ">=14.0"}}, "5.26.5": {"name": "undici", "version": "5.26.5", "dependencies": {"@fastify/busboy": "^2.0.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.29.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^22.1.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^16.1.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "semver": "^7.5.4", "snazzy": "^9.0.0", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "f6dc8c565e3cad8c4475b187f51a13e505092838", "tarball": "https://registry.npmjs.org/undici/-/undici-5.26.5.tgz", "fileCount": 151, "integrity": "sha512-cSb4bPFd5qgR7qr2jYAi0hlX9n5YKK2ONKkLFkxl+v/9BvC0sOpZjBHDBSXc5lWAf5ty9oZdRXytBIHzgUcerw==", "signatures": [{"sig": "MEUCIQD32pUz5ku7FvjgVm1pF5hF2zaFv+CVmzv9Lo6vblFDvQIgPU9QHoDm1TPQO/eB4C6HuxILFCWSQYjPAHFfN2+oqXM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1145330}, "engines": {"node": ">=14.0"}}, "5.27.0": {"name": "undici", "version": "5.27.0", "dependencies": {"@fastify/busboy": "^2.0.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.29.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^22.1.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^16.1.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "semver": "^7.5.4", "snazzy": "^9.0.0", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "789f2e40ce982b5507899abc2c2ddeb2712b4554", "tarball": "https://registry.npmjs.org/undici/-/undici-5.27.0.tgz", "fileCount": 151, "integrity": "sha512-l3ydWhlhOJzMVOYkymLykcRRXqbUaQriERtR70B9LzNkZ4bX52Fc8wbTDneMiwo8T+AemZXvXaTx+9o5ROxrXg==", "signatures": [{"sig": "MEUCIGN1GOtVr63BMkZZ2Q3xlcdLYUNuKseoOmxORFTrkrsxAiEArwq+DqZufS7lqyrVNJQE7ghsYoywRtTTYjBNmASrpk4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1146170}, "engines": {"node": ">=14.0"}}, "5.27.1": {"name": "undici", "version": "5.27.1", "dependencies": {"@fastify/busboy": "^2.0.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.29.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^22.1.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^16.1.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "semver": "^7.5.4", "snazzy": "^9.0.0", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "d4a0154aff919f9eab81567fd651d8752a5b5d17", "tarball": "https://registry.npmjs.org/undici/-/undici-5.27.1.tgz", "fileCount": 151, "integrity": "sha512-h0P6HVTlbcvF6wiX88+aoouMOuiKJ2TEGfcr+tEbr96OIizJaM4BhXJs/wxdDN/RD0F2yZCV/ylCVJy9PjQJIg==", "signatures": [{"sig": "MEYCIQCBk6NjHJswS3eUlyAb1NqkuzYKtZ9LPYUiJ/pmXNN+cwIhAPya4sdtj9g0WRsj54s0V31u9LCSNs/JA7nEZgvWtk7j", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1147223}, "engines": {"node": ">=14.0"}}, "5.27.2": {"name": "undici", "version": "5.27.2", "dependencies": {"@fastify/busboy": "^2.0.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.29.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^22.1.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^16.1.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "semver": "^7.5.4", "snazzy": "^9.0.0", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "a270c563aea5b46cc0df2550523638c95c5d4411", "tarball": "https://registry.npmjs.org/undici/-/undici-5.27.2.tgz", "fileCount": 151, "integrity": "sha512-iS857PdOEy/y3wlM3yRp+6SNQQ6xU0mmZcwRSriqk+et/cwWAtwmIGf6WkoDN2EK/AMdCO/dfXzIwi+rFMrjjQ==", "signatures": [{"sig": "MEYCIQCa3fPixXphGZWyFuya3rpJpq/6vqJXppRNPnhgwNKsJgIhAK5GLA1mN955q4bVgd8f4k62V4tOrLnLAjheCTx3Tk8J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1147261}, "engines": {"node": ">=14.0"}}, "5.28.0": {"name": "undici", "version": "5.28.0", "dependencies": {"@fastify/busboy": "^2.0.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.29.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^22.1.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^16.1.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "semver": "^7.5.4", "snazzy": "^9.0.0", "mockttp": "^3.9.2", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "09f6aa4a6f34de8996eec585fe4ceebaa9ef3f36", "tarball": "https://registry.npmjs.org/undici/-/undici-5.28.0.tgz", "fileCount": 154, "integrity": "sha512-gM12DkXhlAc5+/TPe60iy9P6ETgVfqTuRJ6aQ4w8RYu0MqKuXhaq3/b86GfzDQnNA3NUO6aUNdvevrKH59D0Nw==", "signatures": [{"sig": "MEUCIQDwxEFLZNqmb+bKxaPwywvwYgcgazH2Ap/g/1jydexeOQIgKjv2ZJf3+59Ak/SGlyp32xgQi+Li30wn+s68ZDp/uPg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1165598}, "engines": {"node": ">=14.0"}}, "5.28.1": {"name": "undici", "version": "5.28.1", "dependencies": {"@fastify/busboy": "^2.0.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.29.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^22.1.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^16.1.0", "table": "^6.8.0", "jsfuzz": "^1.0.15", "semver": "^7.5.4", "snazzy": "^9.0.0", "mockttp": "^3.9.2", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "1052d37bd1a2e8cf3e188d7caebff833fdc06fa7", "tarball": "https://registry.npmjs.org/undici/-/undici-5.28.1.tgz", "fileCount": 154, "integrity": "sha512-xcIIvj1LOQH9zAL54iWFkuDEaIVEjLrru7qRpa3GrEEHk6OBhb/LycuUY2m7VCcTuDeLziXCxobQVyKExyGeIA==", "signatures": [{"sig": "MEQCIGmUaxfMOyWvdR546h/JH42fEhp5r5zwv3urAY9FrWKpAiBUKeuPislIku2+VuMRfDACIo5ZSlITgaY67+RTqMadgA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1166386}, "engines": {"node": ">=14.0"}}, "5.28.2": {"name": "undici", "version": "5.28.2", "dependencies": {"@fastify/busboy": "^2.0.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.29.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^23.0.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^17.0.1", "table": "^6.8.0", "jsfuzz": "^1.0.15", "semver": "^7.5.4", "snazzy": "^9.0.0", "mockttp": "^3.9.2", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^6.0.3", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "fea200eac65fc7ecaff80a023d1a0543423b4c91", "tarball": "https://registry.npmjs.org/undici/-/undici-5.28.2.tgz", "fileCount": 154, "integrity": "sha512-wh1pHJHnUeQV5Xa8/kyQhO7WFa8M34l026L5P/+2TYiakvGy5Rdc8jWZVyG7ieht/0WgJLEd3kcU5gKx+6GC8w==", "signatures": [{"sig": "MEUCIQCLBu3TcQLvmIESYtsaVlrTTHu/EXsHSOiDbjIYylgeLQIgJ30MscztVtyOJ1AcxFHWHIw7jVWM0+5kSh/c+xL/zt0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1166818}, "engines": {"node": ">=14.0"}}, "6.0.0": {"name": "undici", "version": "6.0.0", "dependencies": {"@fastify/busboy": "^2.0.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.29.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^23.0.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^17.0.1", "table": "^6.8.0", "jsfuzz": "^1.0.15", "semver": "^7.5.4", "snazzy": "^9.0.0", "mockttp": "^3.9.2", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^6.0.3", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "e8275e084354fd009ce597174fac9b1dd114a3dc", "tarball": "https://registry.npmjs.org/undici/-/undici-6.0.0.tgz", "fileCount": 155, "integrity": "sha512-m3nzx/3ji00Y6wY9hAJTBOBdtcMj0SyxpHKd5kaxghTftjB3icbYaPXGeogxIVcm6FVvnAM9aB6wz9L8Sntt8w==", "signatures": [{"sig": "MEUCIQCHRGLGFl1ZByJMNtG0TA5GrrdLsxmszJzHcHYnha4D9AIgWrXP3yOKFd2rkxq8Sb//5+6Dc8gFdY0pkSj7585hgm4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1180002}, "engines": {"node": ">=18.0"}}, "6.0.1": {"name": "undici", "version": "6.0.1", "dependencies": {"@fastify/busboy": "^2.0.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.29.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^23.0.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^17.0.1", "table": "^6.8.0", "jsfuzz": "^1.0.15", "semver": "^7.5.4", "snazzy": "^9.0.0", "mockttp": "^3.9.2", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^6.0.3", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "385572addca36d1c2b280629cb694b726170027e", "tarball": "https://registry.npmjs.org/undici/-/undici-6.0.1.tgz", "fileCount": 155, "integrity": "sha512-eZFYQLeS9BiXpsU0cuFhCwfeda2MnC48EVmmOz/eCjsTgmyTdaHdVsPSC/kwC2GtW2e0uH0HIPbadf3/bRWSxw==", "signatures": [{"sig": "MEUCIQDlLFg1DvZEtTCofMp5m/lEMo4S4ljyLFM2O7Op7HpK6QIgBRWLF87Qv4L9s6TLrbvh7uKEZv2pWgHUs0L2fIqoNMM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1181067}, "engines": {"node": ">=18.0"}}, "6.1.0": {"name": "undici", "version": "6.1.0", "dependencies": {"@fastify/busboy": "^2.0.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.29.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^23.0.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^17.0.1", "table": "^6.8.0", "jsfuzz": "^1.0.15", "mitata": "^0.1.6", "semver": "^7.5.4", "snazzy": "^9.0.0", "mockttp": "^3.9.2", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^6.0.3", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "27af453b849c807ac5f9d7aa48bc542cce790513", "tarball": "https://registry.npmjs.org/undici/-/undici-6.1.0.tgz", "fileCount": 158, "integrity": "sha512-o2XGxMEhMDqOKMcZwSB9w0LbZcYDa/qgEH3oDQKMu28KnFMaSBR5+PjPd/NYyEgUX7u2Sk2DegClF7+5w7yyLw==", "signatures": [{"sig": "MEYCIQCMNAfSld+SUIgrU9IRnnZ7sJViiQsiKuFGDa0+m1p7BQIhAKN65+fJn9wZH2NG4IIBAdnnDbwlTh9S66d71fG7R/qX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1190138}, "engines": {"node": ">=18.0"}}, "6.2.0": {"name": "undici", "version": "6.2.0", "dependencies": {"@fastify/busboy": "^2.0.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.29.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^23.0.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^17.0.1", "table": "^6.8.0", "jsfuzz": "^1.0.15", "mitata": "^0.1.6", "semver": "^7.5.4", "snazzy": "^9.0.0", "mockttp": "^3.9.2", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^6.0.3", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "e2f1b41489ebfba503b0f505e0e6c2a0161e7816", "tarball": "https://registry.npmjs.org/undici/-/undici-6.2.0.tgz", "fileCount": 158, "integrity": "sha512-bglzaehBOLMe+BfK+Gv5Vpwpgdq2skaEBbSH92tcAv37wHHHTEGu43boyfTaHB+mwSo3VjyD/8d+ytPGeqsFIg==", "signatures": [{"sig": "MEYCIQCtpzscpNk1sXa/+85J6ZmL0TBs73D1r15CbVaobMKMgwIhALKVmaHWlfHP9XI7JNU08926OISVBkJihL8OQtnkQi1j", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1190098}, "engines": {"node": ">=18.0"}}, "6.2.1": {"name": "undici", "version": "6.2.1", "dependencies": {"@fastify/busboy": "^2.0.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.29.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^23.0.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^17.0.1", "table": "^6.8.0", "jsfuzz": "^1.0.15", "mitata": "^0.1.6", "semver": "^7.5.4", "snazzy": "^9.0.0", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^6.0.3", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "554293044619e065d986c37a4c92185c3bc02121", "tarball": "https://registry.npmjs.org/undici/-/undici-6.2.1.tgz", "fileCount": 158, "integrity": "sha512-7Wa9thEM6/LMnnKtxJHlc8SrTlDmxqJecgz1iy8KlsN0/iskQXOQCuPkrZLXbElPaSw5slFFyKIKXyJ3UtbApw==", "signatures": [{"sig": "MEUCICeZ5javpr5ygixsydBX1oj0L2Zsy4ahaR2R01NT6DlwAiEAmcWhctpSjq8nITQnpz6fhv4mcPjlUU5C66MpI53Uz0M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1189306}, "engines": {"node": ">=18.0"}}, "6.3.0": {"name": "undici", "version": "6.3.0", "dependencies": {"@fastify/busboy": "^2.0.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.30.1", "borp": "^0.5.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^23.0.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^17.0.1", "table": "^6.8.0", "jsfuzz": "^1.0.15", "mitata": "^0.1.6", "semver": "^7.5.4", "snazzy": "^9.0.0", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^6.0.3", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "ad105a56ec80fb74ecb3252e6dc27947bf69d275", "tarball": "https://registry.npmjs.org/undici/-/undici-6.3.0.tgz", "fileCount": 160, "integrity": "sha512-zkSMOXs2topAR1LF0PxAaNNvhdX4LYEcmRMJLMh3mjgfZpBtc/souXOp4aYiR5Q46HrBPA2/8DkEZhD3eNFE1Q==", "signatures": [{"sig": "MEUCIQDUKATXD7a8gpNG/CRGq/hCtNvUSoJGa/7cQl+Y61bVwAIgDMeffZXz2ZUSK69j07vuX5Q6BGW707oxTLvSSA6MEKo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1199666}, "engines": {"node": ">=18.0"}}, "6.4.0": {"name": "undici", "version": "6.4.0", "dependencies": {"@fastify/busboy": "^2.0.0"}, "devDependencies": {"ws": "^8.11.0", "got": "^14.0.0", "tap": "^16.1.0", "tsd": "^0.30.1", "borp": "^0.5.0", "chai": "^4.3.4", "jest": "^29.0.2", "axios": "^1.6.5", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^23.0.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^17.0.1", "table": "^6.8.0", "jsfuzz": "^1.0.15", "mitata": "^0.1.6", "semver": "^7.5.4", "snazzy": "^9.0.0", "request": "^2.88.2", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "node-fetch": "^3.3.2", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^6.0.3", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "7ca0c3f73e1034f3c79e566183b61bb55b1410ea", "tarball": "https://registry.npmjs.org/undici/-/undici-6.4.0.tgz", "fileCount": 161, "integrity": "sha512-wYaKgftNqf6Je7JQ51YzkEkEevzOgM7at5JytKO7BjaURQpERW8edQSMrr2xb+Yv4U8Yg47J24+lc9+NbeXMFA==", "signatures": [{"sig": "MEUCICe3rfoSKFMd3uPF7NuRMz6CVRHtA9UTwFrxlfHmmrIKAiEA8w5euRW0s+EEWC8Rjl2LgcyUYNNl0FVHNoqfoVwgids=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1203373}, "engines": {"node": ">=18.0"}}, "6.5.0": {"name": "undici", "version": "6.5.0", "dependencies": {"@fastify/busboy": "^2.0.0"}, "devDependencies": {"ws": "^8.11.0", "got": "^14.0.0", "tap": "^16.1.0", "tsd": "^0.30.1", "borp": "^0.5.0", "chai": "^4.3.4", "jest": "^29.0.2", "axios": "^1.6.5", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^24.0.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^17.0.1", "table": "^6.8.0", "jsfuzz": "^1.0.15", "mitata": "^0.1.6", "semver": "^7.5.4", "snazzy": "^9.0.0", "request": "^2.88.2", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "node-fetch": "^3.3.2", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^6.0.3", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "fc3b4f4255804bf56e72c01313ddbf75661aae5c", "tarball": "https://registry.npmjs.org/undici/-/undici-6.5.0.tgz", "fileCount": 166, "integrity": "sha512-/MUmPb2ptTvp1j7lPvdMSofMdqPxcOhAaKZi4k55sqm6XMeKI3n1dZJ5cnD4gLjpt2l7CIlthR1IXM59xKhpxw==", "signatures": [{"sig": "MEUCIQDbqTUz+66dsdrbK3Y3cU4bPCJSL9DcvEphvA9p2phIuQIgPdTIGnmzalfY1nnPuBPn5NJGQLiX7NbQrqzwltLWYT0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1233202}, "engines": {"node": ">=18.0"}}, "6.6.0": {"name": "undici", "version": "6.6.0", "dependencies": {"@fastify/busboy": "^2.0.0"}, "devDependencies": {"ws": "^8.11.0", "got": "^14.0.0", "tap": "^16.1.0", "tsd": "^0.30.1", "borp": "^0.5.0", "chai": "^4.3.4", "jest": "^29.0.2", "axios": "^1.6.5", "husky": "^9.0.7", "jsdom": "^24.0.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^17.0.1", "jsfuzz": "^1.0.15", "mitata": "^0.1.6", "snazzy": "^9.0.0", "request": "^2.88.2", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^2.0.2", "dns-packet": "^5.4.0", "node-fetch": "^3.3.2", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^6.0.3", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "a1d618347f4aef6dd23bec9cd2ca9f71209dabd2", "tarball": "https://registry.npmjs.org/undici/-/undici-6.6.0.tgz", "fileCount": 166, "integrity": "sha512-p8VvLAgnx6g9pydV0GG/kciSx3ZCq5PLeEU4yefjoZCc1HSeiMxbrFzYIZlgSMrX3l0CoTJ37C6edu13acE40A==", "signatures": [{"sig": "MEYCIQCGxEo8mzXLmNMFl5Kp4ubGdptxs6U9xew4w9R6xyqUlgIhAKlhZ7pjZIoIo+lNJ/LZuE/IV2iINebPyzdVhA3XTzGu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1240080}, "engines": {"node": ">=18.0"}}, "5.28.3": {"name": "undici", "version": "5.28.3", "dependencies": {"@fastify/busboy": "^2.0.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.29.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^23.0.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^17.0.1", "table": "^6.8.0", "jsfuzz": "^1.0.15", "semver": "^7.5.4", "snazzy": "^9.0.0", "mockttp": "^3.9.2", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^6.0.3", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "a731e0eff2c3fcfd41c1169a869062be222d1e5b", "tarball": "https://registry.npmjs.org/undici/-/undici-5.28.3.tgz", "fileCount": 154, "integrity": "sha512-3ItfzbrhDlINjaP0duwnNsKpDQk3acHI3gVJ1z4fmwMK31k5G9OVIAMLSIaP6w4FaGkaAkN6zaQO9LUvZ1t7VA==", "signatures": [{"sig": "MEQCIBKrqnwcpnLR08ETG3/sLMKG7VCF9fBq88junIdvcqN+AiBO/DfYbcBw/ey/h2Qs54TaBkF0gkeuGQ8uXDSZf3iHAA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1166940}, "engines": {"node": ">=14.0"}}, "6.6.1": {"name": "undici", "version": "6.6.1", "dependencies": {"@fastify/busboy": "^2.0.0"}, "devDependencies": {"ws": "^8.11.0", "got": "^14.0.0", "tap": "^16.1.0", "tsd": "^0.30.1", "borp": "^0.5.0", "chai": "^4.3.4", "jest": "^29.0.2", "axios": "^1.6.5", "husky": "^9.0.7", "jsdom": "^24.0.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^17.0.1", "jsfuzz": "^1.0.15", "mitata": "^0.1.8", "snazzy": "^9.0.0", "request": "^2.88.2", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^2.0.2", "dns-packet": "^5.4.0", "node-fetch": "^3.3.2", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^6.0.3", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "a22b93f8f67fb70b6c38a1702f37f309b5e4658b", "tarball": "https://registry.npmjs.org/undici/-/undici-6.6.1.tgz", "fileCount": 166, "integrity": "sha512-J0GaEp0ztu/grIE2Uq57AbK6TRb+bWbOlxu0POCzhFKA6LKbwSAev+hDQaQcgUUA9CPs8Ky+cauzTHnQrtAQEA==", "signatures": [{"sig": "MEUCIQCCsu8nTN1x2F8lC53WKX5vD5sco3yes1yK+7gsOswxGgIgD+nTYTLkwlEfaYgta/bHwNQHEKl7zcLJTYRfrNbzYjE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1240746}, "engines": {"node": ">=18.0"}}, "6.6.2": {"name": "undici", "version": "6.6.2", "dependencies": {"@fastify/busboy": "^2.0.0"}, "devDependencies": {"ws": "^8.11.0", "got": "^14.0.0", "tap": "^16.1.0", "tsd": "^0.30.1", "borp": "^0.9.1", "chai": "^4.3.4", "jest": "^29.0.2", "axios": "^1.6.5", "husky": "^9.0.7", "jsdom": "^24.0.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^17.0.1", "jsfuzz": "^1.0.15", "mitata": "^0.1.8", "snazzy": "^9.0.0", "request": "^2.88.2", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^2.0.2", "dns-packet": "^5.4.0", "node-fetch": "^3.3.2", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^6.0.3", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "8dce5ae54e8a3bc7140c2b2a0972b5fde9a88efb", "tarball": "https://registry.npmjs.org/undici/-/undici-6.6.2.tgz", "fileCount": 166, "integrity": "sha512-vSqvUE5skSxQJ5sztTZ/CdeJb1Wq0Hf44hlYMciqHghvz+K88U0l7D6u1VsndoFgskDcnU+nG3gYmMzJVzd9Qg==", "signatures": [{"sig": "MEUCIEeiMeMlq1vkD0+7PRRutGldgBzKbrZmtxd7/SCB2LJIAiEA2i/PC88Vfl/sToo+Sm/A1cbh6GI/BuYSdOk8nBk7lpo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1240961}, "engines": {"node": ">=18.0"}}, "6.7.0": {"name": "undici", "version": "6.7.0", "dependencies": {"@fastify/busboy": "^2.0.0"}, "devDependencies": {"c8": "^9.1.0", "ws": "^8.11.0", "tsd": "^0.30.1", "borp": "^0.9.1", "jest": "^29.0.2", "husky": "^9.0.7", "jsdom": "^24.0.0", "proxy": "^2.1.1", "jsfuzz": "^1.0.15", "snazzy": "^9.0.0", "standard": "^17.0.0", "cross-env": "^7.0.3", "form-data": "^4.0.0", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "typescript": "^5.0.2", "@types/node": "^18.0.3", "formdata-node": "^6.0.3", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "ab7eccb292c8e831c93564ac59f8323c0e8aa842", "tarball": "https://registry.npmjs.org/undici/-/undici-6.7.0.tgz", "fileCount": 167, "integrity": "sha512-IcWssIyDN1gk6Mcae44q04oRoWTKrW8OKz0effVK1xdWwAgMPnfpxhn9RXUSL5JlwSikO18R7Ibk7Nukz6kxWA==", "signatures": [{"sig": "MEUCIAsNGcOUZMNEp29T+qI8DousUyOSV5GY7IBVKdeNyYxsAiEAxf0rfzPnrZFOXoHhD/2PRnfuUxZQUqXrm8rdofi/nyc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1024443}, "engines": {"node": ">=18.0"}}, "6.7.1": {"name": "undici", "version": "6.7.1", "devDependencies": {"c8": "^9.1.0", "ws": "^8.11.0", "tsd": "^0.30.1", "borp": "^0.9.1", "jest": "^29.0.2", "husky": "^9.0.7", "jsdom": "^24.0.0", "proxy": "^2.1.1", "jsfuzz": "^1.0.15", "snazzy": "^9.0.0", "standard": "^17.0.0", "cross-env": "^7.0.3", "form-data": "^4.0.0", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "typescript": "^5.0.2", "@types/node": "^18.0.3", "formdata-node": "^6.0.3", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "3cb27222fd5d72c1b2058f4e18bf9b53dd933af8", "tarball": "https://registry.npmjs.org/undici/-/undici-6.7.1.tgz", "fileCount": 169, "integrity": "sha512-+Wtb9bAQw6HYWzCnxrPTMVEV3Q1QjYanI0E4q02ehReMuquQdLTEFEYbfs7hcImVYKcQkWSwT6buEmSVIiDDtQ==", "signatures": [{"sig": "MEYCIQCDSV9BwQrzXYAlTx3lPG8TCH+9Zb15gntN61Vwzs0hqAIhAKkKjowk8jbKzECAxi/pEb0+pcL0QSrPmPsHwiL28Ebh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1114723}, "engines": {"node": ">=18.0"}}, "6.8.0": {"name": "undici", "version": "6.8.0", "devDependencies": {"c8": "^9.1.0", "ws": "^8.11.0", "tsd": "^0.30.1", "borp": "^0.9.1", "jest": "^29.0.2", "husky": "^9.0.7", "jsdom": "^24.0.0", "proxy": "^2.1.1", "jsfuzz": "^1.0.15", "snazzy": "^9.0.0", "standard": "^17.0.0", "cross-env": "^7.0.3", "form-data": "^4.0.0", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "node-forge": "^1.3.1", "pre-commit": "^1.2.2", "typescript": "^5.0.2", "@types/node": "^18.0.3", "formdata-node": "^6.0.3", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "fb146f7e15645fd3453815e7b96e9b563856dfde", "tarball": "https://registry.npmjs.org/undici/-/undici-6.8.0.tgz", "fileCount": 169, "integrity": "sha512-22FP0QRSJDQO2PC+bMBVqvsZ3cNQwQnxCNq910N3eIIU4xgMVVpLbEEX7fCg7AalvijPwjlyk5ezenw9FqZfHQ==", "signatures": [{"sig": "MEQCIQCMnQCnRFlzP/P7waky2HZLz04ZCZjk844ddD8J8w2NHAIfPCIO3DG96+H5YiQEYHsx07AcKvp4Gwf6hJoIwJikhA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1117416}, "engines": {"node": ">=18.0"}}, "6.9.0": {"name": "undici", "version": "6.9.0", "devDependencies": {"c8": "^9.1.0", "ws": "^8.11.0", "tsd": "^0.30.1", "borp": "^0.9.1", "jest": "^29.0.2", "husky": "^9.0.7", "jsdom": "^24.0.0", "proxy": "^2.1.1", "jsfuzz": "^1.0.15", "snazzy": "^9.0.0", "standard": "^17.0.0", "cross-env": "^7.0.3", "form-data": "^4.0.0", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "node-forge": "^1.3.1", "pre-commit": "^1.2.2", "typescript": "^5.0.2", "@types/node": "^18.0.3", "formdata-node": "^6.0.3", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "281b3c8bf29cafa957e743ab2a710b586b01e66e", "tarball": "https://registry.npmjs.org/undici/-/undici-6.9.0.tgz", "fileCount": 171, "integrity": "sha512-XPWfXzJedevUziHwun70EKNvGnxv4CnfraFZ4f/JV01+fcvMYzHE26r/j8AY/9c/70nkN4B1zX7E2Oyuqwz4+Q==", "signatures": [{"sig": "MEQCIDpFqD+QLIMk/DTpYEJ4oM9gVNYIEKa5tqHK1CtKMi2ZAiAWCcQTRZLqP8Mm6KHSq8VKnKSyu6kWpd/HZOJYQmbz7Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1125121}, "engines": {"node": ">=18.0"}}, "6.10.0": {"name": "undici", "version": "6.10.0", "devDependencies": {"c8": "^9.1.0", "ws": "^8.11.0", "tsd": "^0.30.1", "borp": "^0.9.1", "jest": "^29.0.2", "husky": "^9.0.7", "jsdom": "^24.0.0", "proxy": "^2.1.1", "jsfuzz": "^1.0.15", "snazzy": "^9.0.0", "standard": "^17.0.0", "cross-env": "^7.0.3", "form-data": "^4.0.0", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "node-forge": "^1.3.1", "pre-commit": "^1.2.2", "typescript": "^5.0.2", "@types/node": "^18.0.3", "formdata-node": "^6.0.3", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "362b03ce58e47128faed27dbbb0576eee5179687", "tarball": "https://registry.npmjs.org/undici/-/undici-6.10.0.tgz", "fileCount": 171, "integrity": "sha512-/iVdJyzjLm8saPE22xrLgyjbW0NnkgfzJqJKvRcKuP+9XSnV9u1q/QZG7v9ixGyH1F4Fn+bE+LzyDfegg0bfgQ==", "signatures": [{"sig": "MEYCIQDsZ6LYh5mQQZ1/9RwEBtXb1fKwTNnaxJI8eYOk35fH8QIhALeFN5bs33QfSWqkSiGEdD0CONZSFe5y0o0VanyfjUK3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1126214}, "engines": {"node": ">=18.0"}}, "6.10.1": {"name": "undici", "version": "6.10.1", "devDependencies": {"c8": "^9.1.0", "ws": "^8.11.0", "tsd": "^0.30.1", "borp": "^0.9.1", "jest": "^29.0.2", "husky": "^9.0.7", "jsdom": "^24.0.0", "proxy": "^2.1.1", "jsfuzz": "^1.0.15", "snazzy": "^9.0.0", "standard": "^17.0.0", "cross-env": "^7.0.3", "form-data": "^4.0.0", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "node-forge": "^1.3.1", "pre-commit": "^1.2.2", "typescript": "^5.0.2", "@types/node": "^18.0.3", "formdata-node": "^6.0.3", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "772d1c9d448a4c3ee10294e7328d64e9e821086a", "tarball": "https://registry.npmjs.org/undici/-/undici-6.10.1.tgz", "fileCount": 172, "integrity": "sha512-kSzmWrOx3XBKTgPm4Tal8Hyl3yf+hzlA00SAf4goxv8LZYafKmS6gJD/7Fe5HH/DMNiFTRXvkwhLo7mUn5fuQQ==", "signatures": [{"sig": "MEYCIQC3PmFXRA2xpGqPOQpJ8klLc1AUs7AlC5lDee4rvkDIgQIhAKZ0t0FCju/q6MdVfSdvVR7/EvguUDZwPpD72hT1hWbm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1126474}, "engines": {"node": ">=18.0"}}, "6.10.2": {"name": "undici", "version": "6.10.2", "devDependencies": {"c8": "^9.1.0", "ws": "^8.11.0", "tsd": "^0.30.1", "borp": "^0.9.1", "jest": "^29.0.2", "husky": "^9.0.7", "jsdom": "^24.0.0", "proxy": "^2.1.1", "jsfuzz": "^1.0.15", "snazzy": "^9.0.0", "standard": "^17.0.0", "cross-env": "^7.0.3", "form-data": "^4.0.0", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "node-forge": "^1.3.1", "pre-commit": "^1.2.2", "typescript": "^5.0.2", "@types/node": "^18.0.3", "formdata-node": "^6.0.3", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "13fe0c099d0055ea16417bd3fef6ccd661210743", "tarball": "https://registry.npmjs.org/undici/-/undici-6.10.2.tgz", "fileCount": 172, "integrity": "sha512-HcVuBy7ACaDejIMdwCzAvO22OsiE6ir6ziTIr9kAE0vB+PheVe29ZvRN8p7FXCO2uZHTjEoUs5bPiFpuc/hwwQ==", "signatures": [{"sig": "MEQCICL2WuDLQ20nY5NStYUWSriiak7mKIGUmOa4JIHKVugrAiAPmHuUGvKEfZrHYs9Z72nTso7RAbt+MoAyN5RLPPIMvg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1127865}, "engines": {"node": ">=18.0"}}, "6.11.0": {"name": "undici", "version": "6.11.0", "devDependencies": {"c8": "^9.1.0", "ws": "^8.11.0", "tsd": "^0.31.0", "borp": "^0.10.0", "jest": "^29.0.2", "husky": "^9.0.7", "jsdom": "^24.0.0", "proxy": "^2.1.1", "jsfuzz": "^1.0.15", "snazzy": "^9.0.0", "standard": "^17.0.0", "cross-env": "^7.0.3", "form-data": "^4.0.0", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "node-forge": "^1.3.1", "pre-commit": "^1.2.2", "typescript": "^5.0.2", "@types/node": "^18.0.3", "formdata-node": "^6.0.3", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "e99ec2c46914cdd658870bf732662b04097782fb", "tarball": "https://registry.npmjs.org/undici/-/undici-6.11.0.tgz", "fileCount": 172, "integrity": "sha512-y4AOKcD36FXz+MY19yw8Emtbsdp8wBWTLln508+EJwqWezUheU5PSRbvIb+pQld7ZoOY8ruNwqSejSVGZUS/aA==", "signatures": [{"sig": "MEUCIQCYS096DLlvzi4CYxYnEAlhfXZzzAB5ft6eSplMB2xIQAIgbdd+xVV6+rNXiqG6PSxAdBMdI1th5AyrXCo0LcdtLLI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1128777}, "engines": {"node": ">=18.0"}}, "5.28.4": {"name": "undici", "version": "5.28.4", "dependencies": {"@fastify/busboy": "^2.0.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.29.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^23.0.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^17.0.1", "table": "^6.8.0", "jsfuzz": "^1.0.15", "semver": "^7.5.4", "snazzy": "^9.0.0", "mockttp": "^3.9.2", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "6b280408edb6a1a604a9b20340f45b422e373068", "tarball": "https://registry.npmjs.org/undici/-/undici-5.28.4.tgz", "fileCount": 155, "integrity": "sha512-72RFADWFqKmUb2hmmvNODKL3p9hcB6Gt2DOQMis1SEBaV6a4MH8soBvzg+95CYhCKPFedut2JY9bMfrDl9D23g==", "signatures": [{"sig": "MEUCIQD2SbzqIjuXmZ4DlbguCWCKq+YJPqTcxp+6Wmw4rsuHPAIgYE38d6YXRmhoLfMTCne86sr1yv8B5wnXJ++rqv2bqa8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1171978}, "engines": {"node": ">=14.0"}}, "6.11.1": {"name": "undici", "version": "6.11.1", "devDependencies": {"c8": "^9.1.0", "ws": "^8.11.0", "tsd": "^0.31.0", "borp": "^0.10.0", "jest": "^29.0.2", "husky": "^9.0.7", "jsdom": "^24.0.0", "proxy": "^2.1.1", "jsfuzz": "^1.0.15", "snazzy": "^9.0.0", "standard": "^17.0.0", "cross-env": "^7.0.3", "form-data": "^4.0.0", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "node-forge": "^1.3.1", "pre-commit": "^1.2.2", "typescript": "^5.0.2", "@types/node": "^18.0.3", "formdata-node": "^6.0.3", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "75ab573677885b421ca2e6f5f17ff1185b24c68d", "tarball": "https://registry.npmjs.org/undici/-/undici-6.11.1.tgz", "fileCount": 172, "integrity": "sha512-KyhzaLJnV1qa3BSHdj4AZ2ndqI0QWPxYzaIOio0WzcEJB9gvuysprJSLtpvc2D9mhR9jPDUk7xlJlZbH2KR5iw==", "signatures": [{"sig": "MEQCIGUlllqZSv9oqCARc0lY6BpwavhZxyI6x+4O9kTFfxqLAiBVWNIB5LLJNnhwRYrcmSV/jY/W0my1TQzSevlxwPaGQw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1130445}, "engines": {"node": ">=18.0"}}, "6.12.0": {"name": "undici", "version": "6.12.0", "devDependencies": {"c8": "^9.1.0", "ws": "^8.11.0", "tsd": "^0.31.0", "borp": "^0.10.0", "jest": "^29.0.2", "husky": "^9.0.7", "jsdom": "^24.0.0", "proxy": "^2.1.1", "snazzy": "^9.0.0", "standard": "^17.0.0", "cross-env": "^7.0.3", "form-data": "^4.0.0", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "pre-commit": "^1.2.2", "typescript": "^5.0.2", "@types/node": "^18.0.3", "formdata-node": "^6.0.3", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "396d4dbd2ea2351094c00be44655d489afe4fb8a", "tarball": "https://registry.npmjs.org/undici/-/undici-6.12.0.tgz", "fileCount": 172, "integrity": "sha512-d87yk8lqSFUYtR5fTFe2frpkMIrUEz+lgoJmhcL+J3StVl+8fj8ytE4lLnJOTPCE12YbumNGzf4LYsQyusdV5g==", "signatures": [{"sig": "MEUCICD31XIPM3L864139KWm4XV+IP6qIh/A5kCjsR2KTK/lAiEArjiD2k3oay1DbYfsrAO9SXuerI1hgBrw45QyFYDz3Wc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1133282}, "engines": {"node": ">=18.0"}}, "6.13.0": {"name": "undici", "version": "6.13.0", "devDependencies": {"c8": "^9.1.0", "ws": "^8.11.0", "tsd": "^0.31.0", "borp": "^0.10.0", "jest": "^29.0.2", "husky": "^9.0.7", "jsdom": "^24.0.0", "proxy": "^2.1.1", "snazzy": "^9.0.0", "standard": "^17.0.0", "cross-env": "^7.0.3", "form-data": "^4.0.0", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "pre-commit": "^1.2.2", "typescript": "^5.0.2", "@types/node": "^18.0.3", "formdata-node": "^6.0.3", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "7edbf4b7f3aac5f8a681d515151bf55cb3589d72", "tarball": "https://registry.npmjs.org/undici/-/undici-6.13.0.tgz", "fileCount": 172, "integrity": "sha512-Q2rtqmZWrbP8nePMq7mOJIN98M0fYvSgV89vwl/BQRT4mDOeY2GXZngfGpcBBhtky3woM7G24wZV3Q304Bv6cw==", "signatures": [{"sig": "MEUCIHWLDS9sTbcCBBFwX/AnHYKzWzbku2LfeC99jVF+Q6AvAiEAxUWSffnpiZC/iVHZ4VTnI1tuZbj8rx02vpbj2G7GAhg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/undici@6.13.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1132835}, "engines": {"node": ">=18.0"}}, "6.14.0": {"name": "undici", "version": "6.14.0", "devDependencies": {"c8": "^9.1.0", "ws": "^8.11.0", "tsd": "^0.31.0", "borp": "^0.11.0", "jest": "^29.0.2", "husky": "^9.0.7", "jsdom": "^24.0.0", "proxy": "^2.1.1", "sinon": "^17.0.1", "snazzy": "^9.0.0", "standard": "^17.0.0", "cross-env": "^7.0.3", "form-data": "^4.0.0", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "pre-commit": "^1.2.2", "typescript": "^5.0.2", "@types/node": "^18.0.3", "formdata-node": "^6.0.3", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "c176d7b1744793416f42de7609645a182b968dcc", "tarball": "https://registry.npmjs.org/undici/-/undici-6.14.0.tgz", "fileCount": 171, "integrity": "sha512-esJ/x2QU5boTG6thdA0o4qP3cv/oPx9mcQGcp8TAHI+ZBTa0EvM9Jiyp0ILdPGLGxs5HATTKrJqAK+YhrSFicg==", "signatures": [{"sig": "MEQCIFGPauFi85NbB1ldg+MEKCssnbk6vgKALfit5Gvof1rIAiAoWR/VpFn54Up9afgkfHqSZgzsVwWh/mTOAk4EZycGhQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/undici@6.14.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1106809}, "engines": {"node": ">=18.17"}}, "6.14.1": {"name": "undici", "version": "6.14.1", "devDependencies": {"c8": "^9.1.0", "ws": "^8.11.0", "tsd": "^0.31.0", "borp": "^0.12.0", "jest": "^29.0.2", "husky": "^9.0.7", "jsdom": "^24.0.0", "proxy": "^2.1.1", "sinon": "^17.0.1", "snazzy": "^9.0.0", "standard": "^17.0.0", "cross-env": "^7.0.3", "form-data": "^4.0.0", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "pre-commit": "^1.2.2", "typescript": "^5.0.2", "@types/node": "^18.0.3", "formdata-node": "^6.0.3", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "e3c9ce906c2e7dddd0d006d7c91a7d8aa1f2890a", "tarball": "https://registry.npmjs.org/undici/-/undici-6.14.1.tgz", "fileCount": 171, "integrity": "sha512-mAel3i4BsYhkeVPXeIPXVGPJKeBzqCieZYoFsbWfUzd68JmHByhc1Plit5WlylxXFaGpgkZB8mExlxnt+Q1p7A==", "signatures": [{"sig": "MEUCIQDI8T1KbGStePejaIQ15jpEY+HfXyj+d7u+SanPFktltQIgLkopCdoKfaHtiDTDEMKA5sj2PZjSVGz0eb4v4MZrueo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/undici@6.14.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1106842}, "engines": {"node": ">=18.17"}}, "6.15.0": {"name": "undici", "version": "6.15.0", "devDependencies": {"c8": "^9.1.0", "ws": "^8.11.0", "tsd": "^0.31.0", "borp": "^0.12.0", "jest": "^29.0.2", "husky": "^9.0.7", "jsdom": "^24.0.0", "proxy": "^2.1.1", "snazzy": "^9.0.0", "standard": "^17.0.0", "cross-env": "^7.0.3", "form-data": "^4.0.0", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "pre-commit": "^1.2.2", "typescript": "^5.0.2", "@types/node": "^18.0.3", "formdata-node": "^6.0.3", "@fastify/busboy": "2.1.1", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "ce97f0a0734439e65acc7059cd60824693987103", "tarball": "https://registry.npmjs.org/undici/-/undici-6.15.0.tgz", "fileCount": 171, "integrity": "sha512-VviMt2tlMg1BvQ0FKXxrz1eJuyrcISrL2sPfBf7ZskX/FCEc/7LeThQaoygsMJpNqrATWQIsRVx+1Dpe4jaYuQ==", "signatures": [{"sig": "MEUCIH7ggYN8mxwc9Dd8OJx7kSWH3xTFt15vJcOzLOI8K1EBAiEAhXhh7uoULy4OZAtI51Ih8uVfJRpWF5kixohoIrgqugA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/undici@6.15.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1112970}, "engines": {"node": ">=18.17"}}, "6.16.0": {"name": "undici", "version": "6.16.0", "devDependencies": {"c8": "^9.1.0", "ws": "^8.11.0", "tsd": "^0.31.0", "borp": "^0.13.0", "jest": "^29.0.2", "husky": "^9.0.7", "jsdom": "^24.0.0", "proxy": "^2.1.1", "snazzy": "^9.0.0", "standard": "^17.0.0", "cross-env": "^7.0.3", "form-data": "^4.0.0", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "pre-commit": "^1.2.2", "typescript": "^5.0.2", "@types/node": "^18.0.3", "formdata-node": "^6.0.3", "@fastify/busboy": "2.1.1", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "3b2e3dc617c6bd51b6885f15d58c172cc8b6fc40", "tarball": "https://registry.npmjs.org/undici/-/undici-6.16.0.tgz", "fileCount": 171, "integrity": "sha512-HQfVddOTb5PJtfLnJ1Px8bNGyIg/z7WTj1hjUSna1Itsv59Oca9JdclIU08ToNqvWWXjFLRzc9rqjnpfw5UWcQ==", "signatures": [{"sig": "MEUCIDIOACcKa7upIqNd5d+RVyTyq4oBTtFeSCSDwZn/DOajAiEAu57eUkr3Uh+u7fQE7sRylN3vK8MixZSEOER436eIT2s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/undici@6.16.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1116952}, "engines": {"node": ">=18.17"}}, "6.16.1": {"name": "undici", "version": "6.16.1", "devDependencies": {"c8": "^9.1.0", "ws": "^8.11.0", "tsd": "^0.31.0", "borp": "^0.13.0", "jest": "^29.0.2", "husky": "^9.0.7", "jsdom": "^24.0.0", "proxy": "^2.1.1", "snazzy": "^9.0.0", "standard": "^17.0.0", "cross-env": "^7.0.3", "form-data": "^4.0.0", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "pre-commit": "^1.2.2", "typescript": "^5.0.2", "@types/node": "^18.0.3", "formdata-node": "^6.0.3", "@fastify/busboy": "2.1.1", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "ff8f49c56e5a8629f92ad2ce00d4841b9619b19e", "tarball": "https://registry.npmjs.org/undici/-/undici-6.16.1.tgz", "fileCount": 171, "integrity": "sha512-NeNiTT7ixpeiL1qOIU/xTVpHpVP0svmI6PwoCKaMGaI5AsHOaRdwqU/f7Fi9eyU4u03nd5U/BC8wmRMnS9nqoA==", "signatures": [{"sig": "MEQCIAu6ziKPxGkN4kOVxYeQAF4ejFe8kH8WORViso4HZI3YAiBlT0RlJ+4y1g3qxID25LWXzMZs3m/dk1yfxVZ//jU2dg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/undici@6.16.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1117399}, "engines": {"node": ">=18.17"}}, "6.17.0": {"name": "undici", "version": "6.17.0", "devDependencies": {"c8": "^9.1.0", "ws": "^8.11.0", "tsd": "^0.31.0", "borp": "^0.13.0", "jest": "^29.0.2", "husky": "^9.0.7", "jsdom": "^24.0.0", "proxy": "^2.1.1", "snazzy": "^9.0.0", "standard": "^17.0.0", "cross-env": "^7.0.3", "form-data": "^4.0.0", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "pre-commit": "^1.2.2", "typescript": "^5.0.2", "@types/node": "^18.0.3", "formdata-node": "^6.0.3", "@fastify/busboy": "2.1.1", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "893d47b4027cff5409f11eb95d23196383ce9723", "tarball": "https://registry.npmjs.org/undici/-/undici-6.17.0.tgz", "fileCount": 172, "integrity": "sha512-fs13QiDjPIzJ7gFAOal9CSG0c92rT2xw6MuMUJ4H30Eg5GCauLWYCCZA1tInjd6M4y+JZjVCCFr9pFpbhcC64w==", "signatures": [{"sig": "MEUCIQCtUXvIe8Gy1iRCSNJITmIIsj7RwkXeV9hOBe08kWzymQIgUMso3jPf8PNRRlKNIQsbcjfGShHVlI5HOSqQBHLbRSI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/undici@6.17.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1123875}, "engines": {"node": ">=18.17"}}, "6.18.0": {"name": "undici", "version": "6.18.0", "devDependencies": {"c8": "^9.1.0", "ws": "^8.11.0", "tsd": "^0.31.0", "borp": "^0.13.0", "jest": "^29.0.2", "husky": "^9.0.7", "jsdom": "^24.0.0", "proxy": "^2.1.1", "snazzy": "^9.0.0", "standard": "^17.0.0", "cross-env": "^7.0.3", "form-data": "^4.0.0", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "pre-commit": "^1.2.2", "typescript": "^5.0.2", "@types/node": "^18.0.3", "formdata-node": "^6.0.3", "@fastify/busboy": "2.1.1", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "cea3b87182f1ef13c226e2f29b862e6d43004fa3", "tarball": "https://registry.npmjs.org/undici/-/undici-6.18.0.tgz", "fileCount": 174, "integrity": "sha512-nT8jjv/fE9Et1ilR6QoW8ingRTY2Pp4l2RUrdzV5Yz35RJDrtPc1DXvuNqcpsJSGIRHFdt3YKKktTzJA6r0fTA==", "signatures": [{"sig": "MEQCIB4Wl7lr4yN8NuCWJxOEmHj4khZPjg3f8y+xOA9J5ExYAiA6Jdc5tM1A7IHEOTABMkLRvAKK1/mQDHs9huaQX63+JQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/undici@6.18.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1130383}, "engines": {"node": ">=18.17"}}, "6.18.1": {"name": "undici", "version": "6.18.1", "devDependencies": {"c8": "^9.1.0", "ws": "^8.11.0", "tsd": "^0.31.0", "borp": "^0.13.0", "jest": "^29.0.2", "husky": "^9.0.7", "jsdom": "^24.0.0", "proxy": "^2.1.1", "snazzy": "^9.0.0", "standard": "^17.0.0", "cross-env": "^7.0.3", "form-data": "^4.0.0", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "pre-commit": "^1.2.2", "typescript": "^5.0.2", "@types/node": "^18.0.3", "formdata-node": "^6.0.3", "@fastify/busboy": "2.1.1", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "8390af4c4bed00fc32cb5f77f1c5e03e3271b8f2", "tarball": "https://registry.npmjs.org/undici/-/undici-6.18.1.tgz", "fileCount": 174, "integrity": "sha512-/0BWqR8rJNRysS5lqVmfc7eeOErcOP4tZpATVjJOojjHZ71gSYVAtFhEmadcIjwMIUehh5NFyKGsXCnXIajtbA==", "signatures": [{"sig": "MEYCIQDYY/tIybLs9uwdgGXB7k/uQB6OB/mY98Q1S4+juJLDuQIhANc9Ymaae2CHbhi8w8dAOwaMuVYhvGGcbvNkSiIUeAkg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/undici@6.18.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1131188}, "engines": {"node": ">=18.17"}}, "6.18.2": {"name": "undici", "version": "6.18.2", "devDependencies": {"c8": "^9.1.0", "ws": "^8.11.0", "tsd": "^0.31.0", "borp": "^0.14.0", "jest": "^29.0.2", "husky": "^9.0.7", "jsdom": "^24.0.0", "proxy": "^2.1.1", "snazzy": "^9.0.0", "standard": "^17.0.0", "cross-env": "^7.0.3", "form-data": "^4.0.0", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "pre-commit": "^1.2.2", "typescript": "^5.0.2", "@types/node": "^18.0.3", "formdata-node": "^6.0.3", "@fastify/busboy": "2.1.1", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "f662a5dc33cf654fc412a9912e5a07b138d75c97", "tarball": "https://registry.npmjs.org/undici/-/undici-6.18.2.tgz", "fileCount": 174, "integrity": "sha512-o/MQLTwRm9IVhOqhZ0NQ9oXax1ygPjw6Vs+Vq/4QRjbOAC3B1GCHy7TYxxbExKlb7bzDRzt9vBWU6BDz0RFfYg==", "signatures": [{"sig": "MEYCIQCFmt8BUfkHLKBzaAW+aKeBHPgBLqyeTUAhDeshQj/yFAIhAKBd4gLPg17WNP7ao/TWYIULs2QNoMdT9qNRt1AcaEsJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/undici@6.18.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1132226}, "engines": {"node": ">=18.17"}}, "6.19.0": {"name": "undici", "version": "6.19.0", "devDependencies": {"c8": "^10.0.0", "ws": "^8.11.0", "tsd": "^0.31.0", "borp": "^0.15.0", "jest": "^29.0.2", "husky": "^9.0.7", "jsdom": "^24.0.0", "proxy": "^2.1.1", "snazzy": "^9.0.0", "standard": "^17.0.0", "cross-env": "^7.0.3", "form-data": "^4.0.0", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "pre-commit": "^1.2.2", "typescript": "^5.0.2", "@types/node": "^18.0.3", "formdata-node": "^6.0.3", "@fastify/busboy": "2.1.1", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "99f6e7ab4e4116dbbedf4e734e8c267f926f20a4", "tarball": "https://registry.npmjs.org/undici/-/undici-6.19.0.tgz", "fileCount": 174, "integrity": "sha512-9gGwbSLgYMjp4r6M5P9bhqhx1E+RyUIHqZE0r7BmrRoqroJUG6xlVu5TXH9DnwmCPLkcaVNrcYtxUE9d3InnyQ==", "signatures": [{"sig": "MEYCIQCyKlkYs/mGqB8y8rMio+9s8l9AOlsdZZXEGnbpQ7lODgIhAN6UZubO+6w+VWGNxIHHm1515VIax50puddZediN/mxS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1132832}, "engines": {"node": ">=18.17"}}, "6.19.1": {"name": "undici", "version": "6.19.1", "devDependencies": {"c8": "^10.0.0", "ws": "^8.11.0", "tsd": "^0.31.0", "borp": "^0.15.0", "jest": "^29.0.2", "husky": "^9.0.7", "jsdom": "^24.0.0", "proxy": "^2.1.1", "snazzy": "^9.0.0", "standard": "^17.0.0", "cross-env": "^7.0.3", "form-data": "^4.0.0", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "pre-commit": "^1.2.2", "typescript": "^5.0.2", "@types/node": "^18.0.3", "formdata-node": "^6.0.3", "@fastify/busboy": "2.1.1", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "07177c8ced83db5b463ecc84e1eecd54c5158a8e", "tarball": "https://registry.npmjs.org/undici/-/undici-6.19.1.tgz", "fileCount": 174, "integrity": "sha512-m9QbEf5+YWXYycRHQtE22hTmRv2R6IDpBVR9UuHKvrDZJxrpgqnKkdV5inOdFskVxz3DmcKhDY/B1sE+ShhopQ==", "signatures": [{"sig": "MEYCIQD1Ro9EDwJ/1G7/t6fhPy5HYhzkbGV84sCtmHFJASBbegIhAIssARvF4bNYt6nX7HMCTb1w5CjYHVquROm+crHA/16k", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/undici@6.19.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1133046}, "engines": {"node": ">=18.17"}}, "6.19.2": {"name": "undici", "version": "6.19.2", "devDependencies": {"c8": "^10.0.0", "ws": "^8.11.0", "tsd": "^0.31.0", "borp": "^0.15.0", "jest": "^29.0.2", "husky": "^9.0.7", "jsdom": "^24.0.0", "proxy": "^2.1.1", "snazzy": "^9.0.0", "standard": "^17.0.0", "cross-env": "^7.0.3", "form-data": "^4.0.0", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "pre-commit": "^1.2.2", "typescript": "^5.0.2", "@types/node": "^18.0.3", "formdata-node": "^6.0.3", "@fastify/busboy": "2.1.1", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "231bc5de78d0dafb6260cf454b294576c2f3cd31", "tarball": "https://registry.npmjs.org/undici/-/undici-6.19.2.tgz", "fileCount": 174, "integrity": "sha512-J<PERSON>jKqIauur3Q6biAtHJ564e3bWa8VvT+7cSiOJHFbX4Erv6CLGDpg8z+Fmg/1OI/47RA+GI2QZaF48SSaLvyBA==", "signatures": [{"sig": "MEQCIBLkR/+LDQW3daaig8hG4306omtZp4QtNuJ+VjIjHE7cAiAbdmL9vOcsXAXnIxLP/8CTpZfFj/2fMHj8INYk4sWP4A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/undici@6.19.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1132327}, "engines": {"node": ">=18.17"}}, "6.19.3": {"name": "undici", "version": "6.19.3", "devDependencies": {"c8": "^10.0.0", "ws": "^8.11.0", "tsd": "^0.31.0", "borp": "^0.15.0", "jest": "^29.0.2", "husky": "^9.0.7", "jsdom": "^24.0.0", "proxy": "^2.1.1", "snazzy": "^9.0.0", "standard": "^17.0.0", "cross-env": "^7.0.3", "form-data": "^4.0.0", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "pre-commit": "^1.2.2", "typescript": "^5.0.2", "@types/node": "^18.0.3", "formdata-node": "^6.0.3", "@fastify/busboy": "2.1.1", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "07712c2200d9aa6dbd613eb7b356903b7ba30efe", "tarball": "https://registry.npmjs.org/undici/-/undici-6.19.3.tgz", "fileCount": 174, "integrity": "sha512-xWvTmUYvfiKATSKntAVRpPO5bfRcrG9FpiHI916j8dK8nUMjeM0uE8dx7ftKoPUQ2RtRi0KMDL10/03orVgTMA==", "signatures": [{"sig": "MEYCIQDuj//TgUE43QV1Fr7rSgfU6IiJYEfhl88xHgduxkN1JAIhALemsENV6bB/z7Adtem6PnI9i8gLrTML8QFzUl3RT6VZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1132327}, "engines": {"node": ">=18.17"}}, "6.19.4": {"name": "undici", "version": "6.19.4", "devDependencies": {"c8": "^10.0.0", "ws": "^8.11.0", "tsd": "^0.31.0", "borp": "^0.15.0", "jest": "^29.0.2", "husky": "^9.0.7", "jsdom": "^24.0.0", "proxy": "^2.1.1", "snazzy": "^9.0.0", "standard": "^17.0.0", "cross-env": "^7.0.3", "form-data": "^4.0.0", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "pre-commit": "^1.2.2", "typescript": "^5.0.2", "@types/node": "^18.0.3", "formdata-node": "^6.0.3", "@fastify/busboy": "2.1.1", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "5ec3b191699a1678ee0aa9ed14e443a682d0f7a8", "tarball": "https://registry.npmjs.org/undici/-/undici-6.19.4.tgz", "fileCount": 174, "integrity": "sha512-i3uaEUwNdkRq2qtTRRJb13moW5HWqviu7Vl7oYRYz++uPtGHJj+x7TGjcEuwS5Mt2P4nA0U9dhIX3DdB6JGY0g==", "signatures": [{"sig": "MEUCIQDt/Ccjp2HgLddAXy4bwOg+2ZZoVGRk0h9qgtwWACns9gIgclXHtkJXEx8gK57wd0RAepCjJZdq6EQSEOgfN/OQcro=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1132328}, "engines": {"node": ">=18.17"}}, "6.19.5": {"name": "undici", "version": "6.19.5", "devDependencies": {"c8": "^10.0.0", "ws": "^8.11.0", "tsd": "^0.31.0", "borp": "^0.15.0", "jest": "^29.0.2", "husky": "^9.0.7", "jsdom": "^24.0.0", "proxy": "^2.1.1", "snazzy": "^9.0.0", "standard": "^17.0.0", "cross-env": "^7.0.3", "form-data": "^4.0.0", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "pre-commit": "^1.2.2", "typescript": "^5.0.2", "@types/node": "^18.0.3", "formdata-node": "^6.0.3", "@fastify/busboy": "2.1.1", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "5829101361b583b53206e81579f4df71c56d6be8", "tarball": "https://registry.npmjs.org/undici/-/undici-6.19.5.tgz", "fileCount": 174, "integrity": "sha512-LryC15SWzqQsREHIOUybavaIHF5IoL0dJ9aWWxL/PgT1KfqAW5225FZpDUFlt9xiDMS2/S7DOKhFWA7RLksWdg==", "signatures": [{"sig": "MEYCIQD6IoOu4RpI5oebgIaSDFy9NCmbhP6k0r+P3frdBG8B1QIhAIhj6NBWw3af9evuzBmhR3GrYqbkmXmm2NWH9ewo9jO0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1132328}, "engines": {"node": ">=18.17"}}, "6.19.6": {"name": "undici", "version": "6.19.6", "devDependencies": {"c8": "^10.0.0", "ws": "^8.11.0", "tsd": "^0.31.0", "borp": "^0.15.0", "jest": "^29.0.2", "husky": "^9.0.7", "jsdom": "^24.0.0", "proxy": "^2.1.1", "snazzy": "^9.0.0", "standard": "^17.0.0", "cross-env": "^7.0.3", "form-data": "^4.0.0", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "pre-commit": "^1.2.2", "typescript": "^5.0.2", "@types/node": "^18.0.3", "formdata-node": "^6.0.3", "@fastify/busboy": "2.1.1", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "f6143f1fb2c329d541e381bcdf171a5074171ae7", "tarball": "https://registry.npmjs.org/undici/-/undici-6.19.6.tgz", "fileCount": 174, "integrity": "sha512-KfINKY5js30ub8NAGQlUyxldk2NTvNkyKBnJtMpSVCk8fqrPpUDEvDkHV6t+lTHCv9NwbUcHU3Jnm2ohE01G+Q==", "signatures": [{"sig": "MEQCIFHwaf9gz81Iu6gHKpyd9SVOQS4Ae8W+TiqGKkFF2AjcAiBnekD4XfBPProM3bYJZPnAhdedwsFukMI/PsfJwvSTeA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1132893}, "engines": {"node": ">=18.17"}}, "6.19.7": {"name": "undici", "version": "6.19.7", "devDependencies": {"c8": "^10.0.0", "ws": "^8.11.0", "tsd": "^0.31.0", "borp": "^0.15.0", "jest": "^29.0.2", "husky": "^9.0.7", "jsdom": "^24.0.0", "proxy": "^2.1.1", "snazzy": "^9.0.0", "standard": "^17.0.0", "cross-env": "^7.0.3", "form-data": "^4.0.0", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "pre-commit": "^1.2.2", "typescript": "^5.0.2", "@types/node": "^18.0.3", "formdata-node": "^6.0.3", "@fastify/busboy": "2.1.1", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "7d4cf26dc689838aa8b6753a3c5c4288fc1e0216", "tarball": "https://registry.npmjs.org/undici/-/undici-6.19.7.tgz", "fileCount": 174, "integrity": "sha512-HR3W/bMGPSr90i8AAp2C4DM3wChFdJPLrWYpIS++LxS8K+W535qftjt+4MyjNYHeWabMj1nvtmLIi7l++iq91A==", "signatures": [{"sig": "MEUCIQCqgGZNHlHoVdP2OHwXKuiS94jcw7hK0SfOFM7oL6LAyQIgORUYACxOcJE/aJSM4KU5arGXK3wRwLSEngPmOQkk/PM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1132893}, "engines": {"node": ">=18.17"}}, "6.19.8": {"name": "undici", "version": "6.19.8", "devDependencies": {"c8": "^10.0.0", "ws": "^8.11.0", "tsd": "^0.31.0", "borp": "^0.15.0", "jest": "^29.0.2", "husky": "^9.0.7", "jsdom": "^24.0.0", "proxy": "^2.1.1", "snazzy": "^9.0.0", "standard": "^17.0.0", "cross-env": "^7.0.3", "form-data": "^4.0.0", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "pre-commit": "^1.2.2", "typescript": "^5.0.2", "@types/node": "^18.0.3", "formdata-node": "^6.0.3", "@fastify/busboy": "2.1.1", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "002d7c8a28f8cc3a44ff33c3d4be4d85e15d40e1", "tarball": "https://registry.npmjs.org/undici/-/undici-6.19.8.tgz", "fileCount": 174, "integrity": "sha512-U8uCCl2x9TK3WANvmBavymRzxbfFYG+tAu+fgx3zxQy3qdagQqBLwJVrdyO1TBfUXvfKveMKJZhpvUYoOjM+4g==", "signatures": [{"sig": "MEYCIQDPisTykrDLOTgZbFS+NjPaKXoWi0LiBXqhyHJQbBhbHwIhAO6nRj+Xc7keKrm94r5SSYagHMNofLCmFfcBmUiYaUtE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1133362}, "engines": {"node": ">=18.17"}}, "7.0.0-alpha.1": {"name": "undici", "version": "7.0.0-alpha.1", "devDependencies": {"c8": "^10.0.0", "ws": "^8.11.0", "tsd": "^0.31.0", "borp": "^0.17.0", "jest": "^29.0.2", "husky": "^9.0.7", "proxy": "^2.1.1", "eslint": "^9.9.0", "cross-env": "^7.0.3", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "pre-commit": "^1.2.2", "typescript": "^5.0.2", "@types/node": "~18.17.19", "neostandard": "^0.11.2", "@fastify/busboy": "3.0.0", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "077a32f1d41520aa0c14a1b22362a86526cc8102", "tarball": "https://registry.npmjs.org/undici/-/undici-7.0.0-alpha.1.tgz", "fileCount": 169, "integrity": "sha512-IPd/A8cDzAkgN35aSXbUECgQlqClat584CaVjcDzCyNdgEe4OaX231XcW1n7DVeJg812UpmzQQWFfC65/nW2Fw==", "signatures": [{"sig": "MEUCIAZ9xgXVlSkgwQsUS65Y5DM+O3KbHvc3XU+Ub8dnpOI3AiEA03avx8cI9vWIUlPSHzvsA299zB+R5B7F8FkmuAQBULo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1145511}, "engines": {"node": ">=18.17"}}, "6.20.0": {"name": "undici", "version": "6.20.0", "devDependencies": {"c8": "^10.0.0", "ws": "^8.11.0", "tsd": "^0.31.0", "borp": "^0.15.0", "jest": "^29.0.2", "husky": "^9.0.7", "jsdom": "^24.0.0", "proxy": "^2.1.1", "snazzy": "^9.0.0", "standard": "^17.0.0", "cross-env": "^7.0.3", "form-data": "^4.0.0", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "pre-commit": "^1.2.2", "typescript": "^5.0.2", "@types/node": "~18.19.50", "formdata-node": "^6.0.3", "@fastify/busboy": "2.1.1", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "3b94d967693759ea625a3b78b2097213f30405a1", "tarball": "https://registry.npmjs.org/undici/-/undici-6.20.0.tgz", "fileCount": 175, "integrity": "sha512-AITZfPuxubm31Sx0vr8bteSalEbs9wQb/BOBi9FPlD9Qpd6HxZ4Q0+hI742jBhkPb4RT2v5MQzaW5VhRVyj+9A==", "signatures": [{"sig": "MEUCIQC5Agv//VD545cZ15JbEaTATIAJ+XadMdM8AjloP+70xgIgMYE0pmGgKdyIWpdlztQrckEJSDMuzAFsiCo7aiDIYB0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1154555}, "engines": {"node": ">=18.17"}}, "7.0.0-alpha.2": {"name": "undici", "version": "7.0.0-alpha.2", "devDependencies": {"c8": "^10.0.0", "ws": "^8.11.0", "tsd": "^0.31.2", "borp": "^0.17.0", "jest": "^29.0.2", "husky": "^9.0.7", "proxy": "^2.1.1", "eslint": "^9.9.0", "esbuild": "^0.24.0", "cross-env": "^7.0.3", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "typescript": "^5.6.2", "@types/node": "^18.19.50", "neostandard": "^0.11.2", "@fastify/busboy": "3.0.0", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "046c98d43ac42081c1b2b27c41d2892c424aa5ef", "tarball": "https://registry.npmjs.org/undici/-/undici-7.0.0-alpha.2.tgz", "fileCount": 169, "integrity": "sha512-4Xn6pwsp6U8F9U7qdDhDwcHPvjcfwt3gbswRg30xgmkQ4Bpfbi6J1OrjTe8WIUVecbcGWOZMPAt6crf0PLliUw==", "signatures": [{"sig": "MEUCIQC8eq3eSVKe7OWzSvJBZH5YDXr7+tMvXiYUPSo6ohl1ggIgSCYSPN/cOVoGKMgS8GaUgoYInMl7JcyTLgxXGnkQJqs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1205517}, "engines": {"node": ">=18.17"}}, "6.20.1": {"name": "undici", "version": "6.20.1", "devDependencies": {"c8": "^10.0.0", "ws": "^8.11.0", "tsd": "^0.31.0", "borp": "^0.15.0", "jest": "^29.0.2", "husky": "^9.0.7", "jsdom": "^24.0.0", "proxy": "^2.1.1", "snazzy": "^9.0.0", "standard": "^17.0.0", "cross-env": "^7.0.3", "form-data": "^4.0.0", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "pre-commit": "^1.2.2", "typescript": "^5.0.2", "@types/node": "~18.19.50", "formdata-node": "^6.0.3", "@fastify/busboy": "2.1.1", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "fbb87b1e2b69d963ff2d5410a40ffb4c9e81b621", "tarball": "https://registry.npmjs.org/undici/-/undici-6.20.1.tgz", "fileCount": 175, "integrity": "sha512-AjQF1QsmqfJys+LXfGTNum+qw4S88CojRInG/6t31W/1fk6G59s92bnAvGz5Cmur+kQv2SURXEvvudLmbrE8QA==", "signatures": [{"sig": "MEUCIQDZyLC5wOoDF6wDbpXTO7z1PJm/iAbKx+pOt8o3daDg5QIgRGuiiUUr7VfF9QXUJ1twAl4GVl/stSiLmP6YmIaxzvA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1157483}, "engines": {"node": ">=18.17"}}, "7.0.0-alpha.3": {"name": "undici", "version": "7.0.0-alpha.3", "devDependencies": {"c8": "^10.0.0", "ws": "^8.11.0", "tsd": "^0.31.2", "borp": "^0.18.0", "jest": "^29.0.2", "husky": "^9.0.7", "proxy": "^2.1.1", "eslint": "^9.9.0", "esbuild": "^0.24.0", "cross-env": "^7.0.3", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "typescript": "^5.6.2", "@types/node": "^18.19.50", "neostandard": "^0.11.2", "@fastify/busboy": "3.0.0", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "f17aa36e81fcdd8de35af0587a74d9c4ee1a0309", "tarball": "https://registry.npmjs.org/undici/-/undici-7.0.0-alpha.3.tgz", "fileCount": 176, "integrity": "sha512-VccnrEkFeiQjILjljH2GazArfC3Ujds04YcF3azoSte/fqsMsLb2x/Nh2Z4P/RGQao6AcNw+wwvNiJlcg3dQfg==", "signatures": [{"sig": "MEUCIQC9HYhcAtG9TUyHnFnR6PdcjM/hO/79fNZzcGVCZYR5pgIgBjO+Sb7vFcKyO5c3WDYB2amucF5tyVN5RFGjc0GKnks=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1256818}, "engines": {"node": ">=18.17"}}, "6.21.0": {"name": "undici", "version": "6.21.0", "devDependencies": {"c8": "^10.0.0", "ws": "^8.11.0", "tsd": "^0.31.0", "borp": "^0.15.0", "jest": "^29.0.2", "husky": "^9.0.7", "jsdom": "^24.0.0", "proxy": "^2.1.1", "snazzy": "^9.0.0", "standard": "^17.0.0", "cross-env": "^7.0.3", "form-data": "^4.0.0", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "pre-commit": "^1.2.2", "typescript": "^5.0.2", "@types/node": "~18.19.50", "formdata-node": "^6.0.3", "@fastify/busboy": "2.1.1", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "4b3d3afaef984e07b48e7620c34ed8a285ed4cd4", "tarball": "https://registry.npmjs.org/undici/-/undici-6.21.0.tgz", "fileCount": 175, "integrity": "sha512-BUgJXc752Kou3oOIuU1i+yZZypyZRqNPW0vqoMPl8VaoalSfeR0D8/t4iAS3yirs79SSMTxTag+ZC86uswv+Cw==", "signatures": [{"sig": "MEUCICNYb9IVnnl2wLEGOITQnLoTCqQsYyoMJJreGv3wc8YwAiEAgJMnMnJA0U35KIu7eSObOdbyEWSBaFxCS4PY0bCvC3M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1158357}, "engines": {"node": ">=18.17"}}, "7.0.0-alpha.4": {"name": "undici", "version": "7.0.0-alpha.4", "devDependencies": {"c8": "^10.0.0", "ws": "^8.11.0", "tsd": "^0.31.2", "borp": "^0.18.0", "jest": "^29.0.2", "husky": "^9.0.7", "proxy": "^2.1.1", "eslint": "^9.9.0", "esbuild": "^0.24.0", "cross-env": "^7.0.3", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "typescript": "^5.6.2", "@types/node": "^18.19.50", "neostandard": "^0.11.2", "@fastify/busboy": "3.0.0", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^12.0.0"}, "dist": {"shasum": "84ac1858806b3fd7758583d66f076fb8db30a5f4", "tarball": "https://registry.npmjs.org/undici/-/undici-7.0.0-alpha.4.tgz", "fileCount": 176, "integrity": "sha512-uffdZ20Ds8E6PlR06tePEQ+MRfg/tWg5eSrrO1vXnWnubTaR7+unFbc3Y9HqCH0gGAMXFJfQ63yd96OMDhEhww==", "signatures": [{"sig": "MEUCIEgpAPAqet4lZ7stiNBkO4jex0ps9BkesHINII3GX6okAiEAkyCrvgiV1J+LC2tFxNkEnSjq9qzKhpKPyfR1+dfWB6g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1260649}, "engines": {"node": ">=18.17"}}, "7.0.0-alpha.5": {"name": "undici", "version": "7.0.0-alpha.5", "devDependencies": {"c8": "^10.0.0", "ws": "^8.11.0", "tsd": "^0.31.2", "borp": "^0.18.0", "jest": "^29.0.2", "husky": "^9.0.7", "proxy": "^2.1.1", "eslint": "^9.9.0", "esbuild": "^0.24.0", "cross-env": "^7.0.3", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "typescript": "^5.6.2", "@types/node": "^18.19.50", "neostandard": "^0.11.2", "@fastify/busboy": "3.0.0", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^12.0.0"}, "dist": {"shasum": "0103e2ea36a6d2d824766a1a7d8fee78455890d3", "tarball": "https://registry.npmjs.org/undici/-/undici-7.0.0-alpha.5.tgz", "fileCount": 176, "integrity": "sha512-kuSlXXFaDLV9nM7x+Z+QDsO/pioIwse584YOjPw7aVNIp9pLYpo2QmK3q2OBqirp951QEbiz8uTxfjEN+z80mw==", "signatures": [{"sig": "MEQCIEd2h0ZCpy2fvAzq5Y9afuJl8V3TTfSL/AGDzU8cH50vAiBt+pffpu+oWBcMIo/5ztiQAK8oqAspE2Z5STNbf3tgfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1264218}, "engines": {"node": ">=18.17"}}, "7.0.0-alpha.6": {"name": "undici", "version": "7.0.0-alpha.6", "devDependencies": {"c8": "^10.0.0", "ws": "^8.11.0", "tsd": "^0.31.2", "borp": "^0.18.0", "jest": "^29.0.2", "husky": "^9.0.7", "proxy": "^2.1.1", "eslint": "^9.9.0", "esbuild": "^0.24.0", "cross-env": "^7.0.3", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "typescript": "^5.6.2", "@types/node": "^18.19.50", "neostandard": "^0.11.2", "@fastify/busboy": "3.0.0", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^12.0.0"}, "dist": {"shasum": "087b1b643cb280552510d1b4518f89dc0e3f9e37", "tarball": "https://registry.npmjs.org/undici/-/undici-7.0.0-alpha.6.tgz", "fileCount": 176, "integrity": "sha512-NNAU7KiklOoq5AvlHskrreJzoG8RUMK6lRwNGzGSVAQ1r7sbmAXoFrQuu/UfQUyscIz+BzLhKazfAB2cgwCjVw==", "signatures": [{"sig": "MEUCICt2iqS0hwKmijIEuZe+R+1PTmUL6qcDyUrq3lbov00PAiEA0+ruOXl8Csbx7mRArJd4Dp1X35M/b5nf98SUa1NISzI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1263304}, "engines": {"node": ">=18.17"}}, "7.0.0-alpha.7": {"name": "undici", "version": "7.0.0-alpha.7", "devDependencies": {"c8": "^10.0.0", "ws": "^8.11.0", "tsd": "^0.31.2", "borp": "^0.18.0", "jest": "^29.0.2", "husky": "^9.0.7", "proxy": "^2.1.1", "eslint": "^9.9.0", "esbuild": "^0.24.0", "cross-env": "^7.0.3", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "typescript": "^5.6.2", "@types/node": "^18.19.50", "neostandard": "^0.11.2", "@fastify/busboy": "3.0.0", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^12.0.0"}, "dist": {"shasum": "9c448e149536f650b0eda99b145f1a9f8f016a74", "tarball": "https://registry.npmjs.org/undici/-/undici-7.0.0-alpha.7.tgz", "fileCount": 179, "integrity": "sha512-hpdWJGzjwfEHI1vEZk2yk1yvlrk+nzN/EebhV7I2uZPtz1PPJ1kEVSN1eZd/FQNw1SNPrfMVa8hAtaYhkHxkfw==", "signatures": [{"sig": "MEYCIQCnRSiXrHyCk0JWCuijdQaA7ezBBUaVDNTK1U3gc0B80gIhAMDtQimccN+7L7obQOWr9erCvNRlKNXdwyNnSchPDmOY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1281162}, "engines": {"node": ">=18.17"}}, "7.0.0-alpha.8": {"name": "undici", "version": "7.0.0-alpha.8", "devDependencies": {"c8": "^10.0.0", "ws": "^8.11.0", "tsd": "^0.31.2", "borp": "^0.19.0", "jest": "^29.0.2", "husky": "^9.0.7", "proxy": "^2.1.1", "eslint": "^9.9.0", "esbuild": "^0.24.0", "cross-env": "^7.0.3", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "typescript": "^5.6.2", "@types/node": "^18.19.50", "neostandard": "^0.11.2", "@fastify/busboy": "3.0.0", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^12.0.0"}, "dist": {"shasum": "d351926b42f86f9e7ebb7255fd7eae6a706ff2bb", "tarball": "https://registry.npmjs.org/undici/-/undici-7.0.0-alpha.8.tgz", "fileCount": 179, "integrity": "sha512-WOZzOl0wXtpqNXlgxoWn0HRQXq51xZKlkjtWfUE4YEXXF/0PnA1UabCMK6ge9oJx3tr0ROZ2akVW22BnKJIH8A==", "signatures": [{"sig": "MEYCIQDQT/Bmk1qVA3sAmOAcfR3n/Gwu8iqkOCDGDgyCy70i9AIhAM6tlqDqVGuYRBCN0GUQD9zk8mykKfdiitZqd45E7zyR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1287202}, "engines": {"node": ">=20.18.1"}}, "7.0.0-alpha.9": {"name": "undici", "version": "7.0.0-alpha.9", "devDependencies": {"c8": "^10.0.0", "ws": "^8.11.0", "tsd": "^0.31.2", "borp": "^0.19.0", "jest": "^29.0.2", "husky": "^9.0.7", "proxy": "^2.1.1", "eslint": "^9.9.0", "esbuild": "^0.24.0", "cross-env": "^7.0.3", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "typescript": "^5.6.2", "@types/node": "^18.19.50", "neostandard": "^0.11.2", "@fastify/busboy": "3.0.0", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^12.0.0"}, "dist": {"shasum": "de9dec7cfde2cd3a77c8009b784278f7875f7ead", "tarball": "https://registry.npmjs.org/undici/-/undici-7.0.0-alpha.9.tgz", "fileCount": 179, "integrity": "sha512-0q/zMoklmktKHftEDz8EPsy2traPnhZGgh50Y9Ozhd2/wnZ9iNtmITrIADLh09CPbyyYqwMjxCr+PDF5dtLrNQ==", "signatures": [{"sig": "MEUCIQDjzDGR//hCdilL4S+2hPGZLF9mwkr/7d7ADlzbATLGEQIgPoxDKYtAe13c/gV+0ctHUxD4uNpDP3A9Qat8k9myEBY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1281068}, "engines": {"node": ">=20.18.1"}}, "7.0.0-alpha.10": {"name": "undici", "version": "7.0.0-alpha.10", "devDependencies": {"c8": "^10.0.0", "ws": "^8.11.0", "tsd": "^0.31.2", "borp": "^0.19.0", "jest": "^29.0.2", "husky": "^9.0.7", "proxy": "^2.1.1", "eslint": "^9.9.0", "esbuild": "^0.24.0", "cross-env": "^7.0.3", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "typescript": "^5.6.2", "@types/node": "^18.19.50", "neostandard": "^0.11.2", "@fastify/busboy": "3.0.0", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^12.0.0"}, "dist": {"shasum": "d7e15346904a6a149e47f72a309860fe534cd906", "tarball": "https://registry.npmjs.org/undici/-/undici-7.0.0-alpha.10.tgz", "fileCount": 179, "integrity": "sha512-6rru/3d3rbrkqELtVp2U4N2sUnZ+49ty20ZTMy1RTl+LU5EYF0BxmwMHIEdC+obVTT9SbUOqSeBmQkkax2SynQ==", "signatures": [{"sig": "MEQCIHvDnkH0Mtf4a1+2SGGIwGOH9qW+1WjIjDC2y/e69rNUAiBbWArvB/FHsUsxtCwpGNokonKxvVeVAgvfN8CW8LnHIA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1281701}, "engines": {"node": ">=20.18.1"}}, "7.0.0": {"name": "undici", "version": "7.0.0", "devDependencies": {"c8": "^10.0.0", "ws": "^8.11.0", "tsd": "^0.31.2", "borp": "^0.19.0", "jest": "^29.0.2", "husky": "^9.0.7", "proxy": "^2.1.1", "eslint": "^9.9.0", "esbuild": "^0.24.0", "cross-env": "^7.0.3", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "typescript": "^5.6.2", "@types/node": "^18.19.50", "neostandard": "^0.11.2", "@fastify/busboy": "3.0.0", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^12.0.0"}, "dist": {"shasum": "3697f99304ea625d804ae087a635291a65affcbb", "tarball": "https://registry.npmjs.org/undici/-/undici-7.0.0.tgz", "fileCount": 179, "integrity": "sha512-c4xi3kWnQJrb7h2q8aJYKvUzmz7boCgz1cUCC6OwdeM5Tr2P0hDuthr2iut4ggqsz+Cnh20U/LoTzbKIdDS/Nw==", "signatures": [{"sig": "MEQCIEXI5IORr4bL0JGEvTwJgO0jr+9T5mlz0+pppJ37ZtgwAiB8EETNYTPhRvQeilx84kqjQIW6EAI8iRsyH7T/aCzLCg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1281692}, "engines": {"node": ">=20.18.1"}}, "7.1.0": {"name": "undici", "version": "7.1.0", "devDependencies": {"c8": "^10.0.0", "ws": "^8.11.0", "tsd": "^0.31.2", "borp": "^0.19.0", "jest": "^29.0.2", "husky": "^9.0.7", "proxy": "^2.1.1", "eslint": "^9.9.0", "esbuild": "^0.24.0", "cross-env": "^7.0.3", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "typescript": "^5.6.2", "@types/node": "^18.19.50", "neostandard": "^0.11.2", "@fastify/busboy": "3.0.0", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^12.0.0"}, "dist": {"shasum": "38efe485eef81f02d2a112121284ab15d5c5b905", "tarball": "https://registry.npmjs.org/undici/-/undici-7.1.0.tgz", "fileCount": 179, "integrity": "sha512-3+mdX2R31khuLCm2mKExSlMdJsfol7bJkIMH80tdXA74W34rT1jKemUTlYR7WY3TqsV4wfOgpatWmmB2Jl1+5g==", "signatures": [{"sig": "MEYCIQCdwi4Mm52aem+U4DhRUFUFA/cLEVMf4q80wEWfPzy9tAIhAMcWV71oUP62AKLbbwW/5gtyfu0VuzcQSLZjgWSj34bU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/undici@7.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1284981}, "engines": {"node": ">=20.18.1"}}, "7.1.1": {"name": "undici", "version": "7.1.1", "devDependencies": {"c8": "^10.0.0", "ws": "^8.11.0", "tsd": "^0.31.2", "borp": "^0.19.0", "jest": "^29.0.2", "husky": "^9.0.7", "proxy": "^2.1.1", "eslint": "^9.9.0", "esbuild": "^0.24.0", "cross-env": "^7.0.3", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "typescript": "^5.6.2", "@types/node": "^18.19.50", "neostandard": "^0.12.0", "@fastify/busboy": "3.1.0", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^12.0.0"}, "dist": {"shasum": "f11eceeaaaa34ff8a28da31b68b0b4a8d75562f0", "tarball": "https://registry.npmjs.org/undici/-/undici-7.1.1.tgz", "fileCount": 179, "integrity": "sha512-WZkQ6eH9f5ZT93gaIffsbUaDpBwjbpvmMbfaEhOnbdUneurTESeRxwPGwjI28mRFESH3W3e8Togijh37ptOQqA==", "signatures": [{"sig": "MEUCIQCmz5pt4ByDcl72a6fJ5msRrYMdLO8Ao6ZQs0/EAfYjpgIgdp2aJNX5yFn/RFLD1l9pRa0Tm+JblnT4E/8SRySOHpM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/undici@7.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1285902}, "engines": {"node": ">=20.18.1"}}, "7.2.0": {"name": "undici", "version": "7.2.0", "devDependencies": {"c8": "^10.0.0", "ws": "^8.11.0", "tsd": "^0.31.2", "borp": "^0.19.0", "jest": "^29.0.2", "husky": "^9.0.7", "proxy": "^2.1.1", "eslint": "^9.9.0", "esbuild": "^0.24.0", "cross-env": "^7.0.3", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "typescript": "^5.6.2", "@types/node": "^18.19.50", "neostandard": "^0.12.0", "@fastify/busboy": "3.1.0", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^12.0.0"}, "dist": {"shasum": "446219e2a57cfcbdc2f135fcfa6dc662b3cae169", "tarball": "https://registry.npmjs.org/undici/-/undici-7.2.0.tgz", "fileCount": 180, "integrity": "sha512-klt+0S55GBViA9nsq48/NSCo4YX5mjydjypxD7UmHh/brMu8h/Mhd/F7qAeoH2NOO8SDTk6kjnTFc4WpzmfYpQ==", "signatures": [{"sig": "MEYCIQC+rZIQF0/4dyXYmJBzDBTiwX9J//HNndIkmbNZcOpIlQIhAPz+4Sk5wKxPB+wsS10LKnSNtoQI4M0lH9KONBcZEM1X", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1295065}, "engines": {"node": ">=20.18.1"}}, "7.2.1": {"name": "undici", "version": "7.2.1", "devDependencies": {"c8": "^10.0.0", "ws": "^8.11.0", "tsd": "^0.31.2", "borp": "^0.19.0", "jest": "^29.0.2", "husky": "^9.0.7", "proxy": "^2.1.1", "eslint": "^9.9.0", "esbuild": "^0.24.0", "cross-env": "^7.0.3", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "typescript": "^5.6.2", "@types/node": "^18.19.50", "neostandard": "^0.12.0", "@fastify/busboy": "3.1.1", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^12.0.0"}, "dist": {"shasum": "897dc535f78bee62217efc0c5f50ea6b5f078b64", "tarball": "https://registry.npmjs.org/undici/-/undici-7.2.1.tgz", "fileCount": 180, "integrity": "sha512-U2k0XHLJfaciARRxDcqTk2AZQsGXerHzdvfCZcy1hNhSf5KCAF4jIQQxL+apQviOekhRFPqED6Of5/+LcUSLzQ==", "signatures": [{"sig": "MEYCIQCLJsBpBmcmVFgOz92Ri0Nhf672HNm07zimbXqBUmrz3gIhANSLWT83D/qTbm1aKFtPK2jxNoAmBQhMpxGqRJyRuwha", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/undici@7.2.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1296302}, "engines": {"node": ">=20.18.1"}}, "7.2.2": {"name": "undici", "version": "7.2.2", "devDependencies": {"c8": "^10.0.0", "ws": "^8.11.0", "tsd": "^0.31.2", "borp": "^0.19.0", "jest": "^29.0.2", "husky": "^9.0.7", "proxy": "^2.1.1", "eslint": "^9.9.0", "esbuild": "^0.24.0", "cross-env": "^7.0.3", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "typescript": "^5.6.2", "@types/node": "^18.19.50", "neostandard": "^0.12.0", "@fastify/busboy": "3.1.1", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^12.0.0"}, "dist": {"shasum": "d5cef5ee22deb9ad4ef4a3c5937b6d0a833b5628", "tarball": "https://registry.npmjs.org/undici/-/undici-7.2.2.tgz", "fileCount": 180, "integrity": "sha512-j/M0BQelSQHcq2Fhc1fUMszXtLx+RsZR5IkXx07knZMICLTzRzsW0tbTI9e9N40RftPUkFP8j4qOjKJa5aTCzw==", "signatures": [{"sig": "MEYCIQDtN/BYjluMgfMD7Qco3gDI8zCUJfAdjHs65eykB7UggQIhAOFTM0rKdx9yn1VIjLZ/h3iK18tyCSO26CN48qa2g7VI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/undici@7.2.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1297712}, "engines": {"node": ">=20.18.1"}}, "7.2.3": {"name": "undici", "version": "7.2.3", "devDependencies": {"c8": "^10.0.0", "ws": "^8.11.0", "tsd": "^0.31.2", "borp": "^0.19.0", "jest": "^29.0.2", "husky": "^9.0.7", "proxy": "^2.1.1", "eslint": "^9.9.0", "esbuild": "^0.24.0", "cross-env": "^7.0.3", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "typescript": "^5.6.2", "@types/node": "^18.19.50", "neostandard": "^0.12.0", "@fastify/busboy": "3.1.1", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^12.0.0"}, "dist": {"shasum": "e3e8ff2ddee1c3ab624b3a4f275093cff4b2fc77", "tarball": "https://registry.npmjs.org/undici/-/undici-7.2.3.tgz", "fileCount": 180, "integrity": "sha512-2oSLHaDalSt2/O/wHA9M+/ZPAOcU2yrSP/cdBYJ+YxZskiPYDSqHbysLSlD7gq3JMqOoJI5O31RVU3BxX/MnAA==", "signatures": [{"sig": "MEQCIAc/H1Gk1WNtMJWv/+ojJOCFhY6ViSjddOB2z7EkHR0EAiAZU2KLKJstvXLwk/uVPrN7CLCdibxiEkAU6Sp4CMViCw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/undici@7.2.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1297856}, "engines": {"node": ">=20.18.1"}}, "6.21.1": {"name": "undici", "version": "6.21.1", "devDependencies": {"c8": "^10.0.0", "ws": "^8.11.0", "tsd": "^0.31.0", "borp": "^0.15.0", "jest": "^29.0.2", "husky": "^9.0.7", "jsdom": "^24.0.0", "proxy": "^2.1.1", "snazzy": "^9.0.0", "standard": "^17.0.0", "cross-env": "^7.0.3", "form-data": "^4.0.0", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "pre-commit": "^1.2.2", "typescript": "^5.0.2", "@types/node": "~18.19.50", "formdata-node": "^6.0.3", "@fastify/busboy": "2.1.1", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "336025a14162e6837e44ad7b819b35b6c6af0e05", "tarball": "https://registry.npmjs.org/undici/-/undici-6.21.1.tgz", "fileCount": 176, "integrity": "sha512-q/1rj5D0/zayJB2FraXdaWxbhWiNKDvu8naDT2dl1yTlvJp4BLtOcp2a5BvgGNQpYYJzau7tf1WgKv3b+7mqpQ==", "signatures": [{"sig": "MEUCIQDLld1yt6tIYslGD61Ni7O5oYeb/JIaei130dV3alLmyQIgIsLN4/YYn+ZBBfbYNQGu7Srp0CdpAAEH+fAj+fz2IIc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1171389}, "engines": {"node": ">=18.17"}}, "5.28.5": {"name": "undici", "version": "5.28.5", "dependencies": {"@fastify/busboy": "^2.0.0"}, "devDependencies": {"ws": "^8.11.0", "tap": "^16.1.0", "tsd": "^0.29.0", "chai": "^4.3.4", "jest": "^29.0.2", "delay": "^5.0.0", "husky": "^8.0.1", "jsdom": "^23.0.0", "mocha": "^10.0.0", "proxy": "^1.0.2", "sinon": "^17.0.1", "table": "^6.8.0", "jsfuzz": "^1.0.15", "semver": "^7.5.4", "snazzy": "^9.0.0", "mockttp": "^3.9.2", "wait-on": "^7.0.1", "standard": "^17.0.0", "form-data": "^4.0.0", "https-pem": "^3.0.0", "p-timeout": "^3.2.0", "cronometro": "^1.0.5", "dns-packet": "^5.4.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "typescript": "^5.0.2", "@types/node": "^18.0.3", "chai-string": "^1.5.0", "docsify-cli": "^4.4.3", "atomic-sleep": "^1.0.0", "concurrently": "^8.0.1", "import-fresh": "^3.3.0", "chai-iterator": "^3.0.2", "formdata-node": "^4.3.1", "abort-controller": "^3.0.0", "chai-as-promised": "^7.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "b2b94b6bf8f1d919bc5a6f31f2c01deb02e54d4b", "tarball": "https://registry.npmjs.org/undici/-/undici-5.28.5.tgz", "fileCount": 155, "integrity": "sha512-zICwjrDrcrUE0pyyJc1I2QzBkLM8FINsgOrt6WjA+BgajVq9Nxu2PbFFXUrAggLfDXlZGZBVZYw7WNV5KiBiBA==", "signatures": [{"sig": "MEUCIQDRb33Gm5FgexSqBOAvyNU6r0wYLE1jbKOSnVcBzQ2vIgIgCvZoVLp5o0P1WAE2HUOIO4DE1P/xMrEnubFCD0FTISM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1172122}, "engines": {"node": ">=14.0"}}, "7.3.0": {"name": "undici", "version": "7.3.0", "devDependencies": {"c8": "^10.0.0", "ws": "^8.11.0", "tsd": "^0.31.2", "borp": "^0.19.0", "jest": "^29.0.2", "husky": "^9.0.7", "proxy": "^2.1.1", "eslint": "^9.9.0", "esbuild": "^0.24.0", "cross-env": "^7.0.3", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "typescript": "^5.6.2", "@types/node": "^18.19.50", "neostandard": "^0.12.0", "@fastify/busboy": "3.1.1", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^12.0.0"}, "dist": {"shasum": "87e48cc9728f3d09bf7b34635e9b63886873ac3e", "tarball": "https://registry.npmjs.org/undici/-/undici-7.3.0.tgz", "fileCount": 180, "integrity": "sha512-Qy96NND4Dou5jKoSJ2gm8ax8AJM/Ey9o9mz7KN1bb9GP+G0l20Zw8afxTnY2f4b7hmhn/z8aC2kfArVQlAhFBw==", "signatures": [{"sig": "MEQCHxzQsls8mqjT84y9kG6PB08WFmlH8k2Av0ANsBay2qYCIQDF+vfUMrL0Js5idoIfspUp/it1EAHaZd4pf/lVxTI0zg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/undici@7.3.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1298016}, "engines": {"node": ">=20.18.1"}}, "7.4.0": {"name": "undici", "version": "7.4.0", "devDependencies": {"c8": "^10.0.0", "ws": "^8.11.0", "tsd": "^0.31.2", "borp": "^0.19.0", "jest": "^29.0.2", "husky": "^9.0.7", "proxy": "^2.1.1", "eslint": "^9.9.0", "esbuild": "^0.24.0", "cross-env": "^7.0.3", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "typescript": "^5.6.2", "@types/node": "^18.19.50", "neostandard": "^0.12.0", "@fastify/busboy": "3.1.1", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^12.0.0"}, "dist": {"shasum": "a2606aa5ceeeaac0ddcc00937586c3bc06bfafdc", "tarball": "https://registry.npmjs.org/undici/-/undici-7.4.0.tgz", "fileCount": 180, "integrity": "sha512-PUQM3/es3noM24oUn10u3kNNap0AbxESOmnssmW+dOi9yGwlUSi5nTNYl3bNbTkWOF8YZDkx2tCmj9OtQ3iGGw==", "signatures": [{"sig": "MEUCIQC+roZ/nRoJSfgTg8h7Bw7/Oq/bW5cqAvSJAtvcPvkq1wIgCBuf1Auug2bdXF843dn0sk3iVB+bRb9uaxjQTmlzvdc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/undici@7.4.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1298557}, "engines": {"node": ">=20.18.1"}}, "7.5.0": {"name": "undici", "version": "7.5.0", "devDependencies": {"c8": "^10.0.0", "ws": "^8.11.0", "tsd": "^0.31.2", "borp": "^0.19.0", "jest": "^29.0.2", "husky": "^9.0.7", "proxy": "^2.1.1", "eslint": "^9.9.0", "esbuild": "^0.24.0", "cross-env": "^7.0.3", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "typescript": "^5.6.2", "@types/node": "^18.19.50", "neostandard": "^0.12.0", "@fastify/busboy": "3.1.1", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^12.0.0"}, "dist": {"shasum": "51db4d33e0556ea9ca0ea28ccae7448dd8c23ac9", "tarball": "https://registry.npmjs.org/undici/-/undici-7.5.0.tgz", "fileCount": 184, "integrity": "sha512-NFQG741e8mJ0fLQk90xKxFdaSM7z4+IQpAgsFI36bCDY9Z2+aXXZjVy2uUksMouWfMI9+w5ejOq5zYYTBCQJDQ==", "signatures": [{"sig": "MEMCIBCys6pHctw7mbmPPvAIcy6ym/OHaveJpYiCScijDDPjAh8xIsEh/sIHizirG57mMeXNYeM3s8NUR9e/td6+0KoE", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/undici@7.5.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 1327781}, "engines": {"node": ">=20.18.1"}}, "6.21.2": {"name": "undici", "version": "6.21.2", "devDependencies": {"c8": "^10.0.0", "ws": "^8.11.0", "tsd": "^0.31.0", "borp": "^0.15.0", "jest": "^29.0.2", "husky": "^9.0.7", "jsdom": "^24.0.0", "proxy": "^2.1.1", "snazzy": "^9.0.0", "standard": "^17.0.0", "cross-env": "^7.0.3", "form-data": "^4.0.0", "https-pem": "^3.0.0", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "node-forge": "^1.3.1", "pre-commit": "^1.2.2", "typescript": "^5.0.2", "@types/node": "~18.19.50", "formdata-node": "^6.0.3", "@fastify/busboy": "2.1.1", "abort-controller": "^3.0.0", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0"}, "dist": {"shasum": "49c5884e8f9039c65a89ee9018ef3c8e2f1f4928", "tarball": "https://registry.npmjs.org/undici/-/undici-6.21.2.tgz", "fileCount": 176, "integrity": "sha512-uROZWze0R0itiAKVPsYhFov9LxrPMHLMEQFszeI2gCN6bnIIZ8twzBCJcN2LJrBBLfrP0t1FW0g+JmKVl8Vk1g==", "signatures": [{"sig": "MEUCIQDIX1MIega+j2Yp4ehTVeBAePFgMBo0QusPG7xcmb6RXQIgMjp4tnVa8nau4fKwjCQe1gAP9RAmctsvn6zjPCbH/UY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1172055}, "engines": {"node": ">=18.17"}}, "5.29.0": {"name": "undici", "version": "5.29.0", "dependencies": {"@fastify/busboy": "^2.0.0"}, "devDependencies": {"@sinonjs/fake-timers": "^11.1.0", "@types/node": "^18.0.3", "abort-controller": "^3.0.0", "atomic-sleep": "^1.0.0", "chai": "^4.3.4", "chai-as-promised": "^7.1.1", "chai-iterator": "^3.0.2", "chai-string": "^1.5.0", "concurrently": "^8.0.1", "cronometro": "^1.0.5", "delay": "^5.0.0", "dns-packet": "^5.4.0", "docsify-cli": "^4.4.3", "form-data": "^4.0.0", "formdata-node": "^4.3.1", "https-pem": "^3.0.0", "husky": "^8.0.1", "import-fresh": "^3.3.0", "jest": "^29.0.2", "jsdom": "^23.0.0", "jsfuzz": "^1.0.15", "mocha": "^10.0.0", "mockttp": "^3.9.2", "p-timeout": "^3.2.0", "pre-commit": "^1.2.2", "proxy": "^1.0.2", "proxyquire": "^2.1.3", "semver": "^7.5.4", "sinon": "^17.0.1", "snazzy": "^9.0.0", "standard": "^17.0.0", "table": "^6.8.0", "tap": "^16.1.0", "tsd": "^0.29.0", "typescript": "^5.0.2", "wait-on": "^7.0.1", "ws": "^8.11.0"}, "dist": {"integrity": "sha512-raqeBD6NQK4SkWhQzeYKd1KmIG6dllBOTt55Rmkt4HtI9mwdWtJljnrXjAFUBLTSN67HWrOIZ3EPF4kjUw80Bg==", "shasum": "419595449ae3f2cdcba3580a2e8903399bd1f5a3", "tarball": "https://registry.npmjs.org/undici/-/undici-5.29.0.tgz", "fileCount": 155, "unpackedSize": 1172246, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIGz+2RpmqNQOcGz7XqrJipYfGl994gkUK+P0tqa5maDwAiEA+zntdAkraqaM6IYIZklrV2XYmW48YQbveBI2JZYvc6g="}]}, "engines": {"node": ">=14.0"}}}, "modified": "2025-03-19T18:00:34.251Z"}