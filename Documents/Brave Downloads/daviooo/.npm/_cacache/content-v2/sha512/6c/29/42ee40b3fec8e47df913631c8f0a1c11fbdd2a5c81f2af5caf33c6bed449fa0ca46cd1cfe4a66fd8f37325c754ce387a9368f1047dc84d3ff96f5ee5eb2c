{"_id": "is-symbol", "_rev": "14-35870bceb7789fa37a67cbfe949d0225", "name": "is-symbol", "dist-tags": {"latest": "1.1.1"}, "versions": {"1.0.0": {"name": "is-symbol", "version": "1.0.0", "keywords": ["symbol", "es6", "is", "Symbol"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "is-symbol@1.0.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/is-symbol", "bugs": {"url": "https://github.com/ljharb/is-symbol/issues"}, "dist": {"shasum": "75934ad042fb1675c52f687f2cb47d66d5c45af2", "tarball": "https://registry.npmjs.org/is-symbol/-/is-symbol-1.0.0.tgz", "integrity": "sha512-/1ooZyx/fQyWnXs2BuaDrqI3xzgoUliYEtkuSQPcqDXXN0vAjoldS4CoboVy15Iyf8B7RD+/0VyVNKvmCHMZMg==", "signatures": [{"sig": "MEUCID29ZPIBvFkn+IYuRoLE682uWsSklDNqNvPriVD3m1ECAiEAi7qYhk2oeL75OumxH0BO8yu0NKwmkMD4Yf+5XiyiInc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "75934ad042fb1675c52f687f2cb47d66d5c45af2", "engines": {"node": ">= 0.4"}, "gitHead": "86c2f7d882ebfbe5b2d3d8b874e59f5db1cd22cb", "scripts": {"lint": "jscs *.js */*.js", "test": "npm run lint && node --es-staging --harmony test/index.js && npm run security", "coverage": "covert test/index.js", "security": "nsp package", "coverage:quiet": "covert test/index.js --quiet"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/is-symbol.git", "type": "git"}, "_npmVersion": "2.3.0", "description": "Determine if a function is an ES6 generator function or not.", "directories": {}, "_nodeVersion": "1.0.4", "dependencies": {}, "devDependencies": {"nsp": "~1.0.0", "jscs": "~1.10.0", "tape": "~3.4.0", "covert": "1.0.0", "semver": "~4.2.0"}}, "1.0.1": {"name": "is-symbol", "version": "1.0.1", "keywords": ["symbol", "es6", "is", "Symbol"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "is-symbol@1.0.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/is-symbol", "bugs": {"url": "https://github.com/ljharb/is-symbol/issues"}, "dist": {"shasum": "3cc59f00025194b6ab2e38dbae6689256b660572", "tarball": "https://registry.npmjs.org/is-symbol/-/is-symbol-1.0.1.tgz", "integrity": "sha512-Z1cLAG7dXM1vJv8mAGjA+XteO0YzYPwD473+qPAWKycNZxwCH/I56QIohKGYCZSB7j6tajrxi/FvOpAfqGhhRQ==", "signatures": [{"sig": "MEUCIQDbJZL1AXHbSysYHIfD+dIK20ADMovt8mAk5cFswPDJKQIgEZQBmM1dcWyalMGBzxSRvk4zgazx7lNRWgcSQfSB/90=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "3cc59f00025194b6ab2e38dbae6689256b660572", "engines": {"node": ">= 0.4"}, "gitHead": "5bbd991ff41a459a941d205de65d533cc6c3cd8c", "scripts": {"lint": "jscs *.js */*.js", "test": "npm run lint && node --es-staging --harmony test/index.js && npm run security", "coverage": "covert test/index.js", "security": "nsp package", "coverage:quiet": "covert test/index.js --quiet"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/is-symbol.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "Determine if a value is an ES6 Symbol or not.", "directories": {}, "dependencies": {}, "devDependencies": {"nsp": "~1.0.0", "jscs": "~1.10.0", "tape": "~3.4.0", "covert": "1.0.0", "semver": "~4.2.0"}}, "1.0.2": {"name": "is-symbol", "version": "1.0.2", "keywords": ["symbol", "es6", "is", "Symbol"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "is-symbol@1.0.2", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/is-symbol#readme", "bugs": {"url": "https://github.com/ljharb/is-symbol/issues"}, "dist": {"shasum": "a055f6ae57192caee329e7a860118b497a950f38", "tarball": "https://registry.npmjs.org/is-symbol/-/is-symbol-1.0.2.tgz", "fileCount": 13, "integrity": "sha512-HS8bZ9ox60yCJLH9snBpIwv9pYUAkcuLhSA1oero1UB5y9aiQpRA8y2ex945AOtCZL1lJDeIk3G5LthswI46Lw==", "signatures": [{"sig": "MEQCICEOsd9CxGSj3izerGaFpe90TbJsambs8kdGfE0XcB1oAiAb8+4PDLdD5gCWgo5IpVvwQgm+o5idVNTTobb1mSUzTg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23337, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbpDtaCRA9TVsSAnZWagAA9jMQAIEIRC5OduDYW1t7iLkI\n5RJTupgMQPmC/oXFX801sLvVQ4HlcUkWj4Qg8iY1B8tjWtWYiiMGnwvnH6ew\ncA0bSWtZoMGsduOSeIE8NuRIYKIoqbVVr+RPr1wQAlMA0lqVffoWmKmTyD7+\nS51VvVm6KHU7c+FV+roS2fgkElDnM4Ua0x7/0qWeA4C0xoTa51Q+bHg8wQVx\n3flwQXc2jH2PHmHO3TkmgomblIi0NPfD7naiZam3AWgmKBZgCEqLEP6wG149\n7Ct1uaaHZd/9UFmNr+Azg2bLRDi4YqR3GBiB3jj6YsqYAyvkBpudkLrdJdwN\nlOL8JRb4h0B+Ftih4NayQNbKSXMxUwiIfmh+DiAiDv5yVJ+Z444iIafeRyBl\nNdignBobmoa3mYm0M/hT5ZDD7EqZLR/aiu79v161H1RzOGh1kYtaPfSa/0bX\nT5gIzoZdO9HmFdgUHvCaUg5Pc+emccTeiI4oW0vDEu4TpwEpN8nUJb4Nf9Nq\n07cBt5o/XzKbd8kNxe404Nmfo8FoBgc1I7ewZobbxHeZdfc0UVk8mvK0ZdrK\nakdExSu6TFq5eshWXRNtYFarkHe99oaFzZthEu3ZHHT5L+5o2LNwp6wovssH\nP4LNpMXyNB/rkcaxoIDYdujTYYqHDh8w5qUyFzE5ANEnAFEn0A0hu8svXyoP\nv/0z\r\n=nc9A\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "gitHead": "9769e1bdc9a265e3f69cc0fab2002991bf0999b2", "scripts": {"jscs": "jscs *.js */*.js", "lint": "npm run jscs && npm run eslint", "test": "npm run tests-only", "eslint": "eslint *.js */*.js", "pretest": "npm run lint", "coverage": "covert test", "posttest": "npm run security", "security": "nsp check", "prepublish": "safe-publish-latest", "tests-only": "node --es-staging --harmony test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/is-symbol.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Determine if a value is an ES6 Symbol or not.", "directories": {}, "_nodeVersion": "10.11.0", "dependencies": {"has-symbols": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nsp": "^3.2.1", "jscs": "^3.0.7", "tape": "^4.9.0", "covert": "^1.1.0", "eslint": "^4.19.1", "semver": "^5.5.0", "object-inspect": "^1.6.0", "safe-publish-latest": "^1.1.2", "@ljharb/eslint-config": "^12.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/is-symbol_1.0.2_1537489754249_0.5751165752963447", "host": "s3://npm-registry-packages"}}, "1.0.3": {"name": "is-symbol", "version": "1.0.3", "keywords": ["symbol", "es6", "is", "Symbol"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-symbol@1.0.3", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-symbol#readme", "bugs": {"url": "https://github.com/inspect-js/is-symbol/issues"}, "dist": {"shasum": "38e1014b9e6329be0de9d24a414fd7441ec61937", "tarball": "https://registry.npmjs.org/is-symbol/-/is-symbol-1.0.3.tgz", "fileCount": 13, "integrity": "sha512-OwijhaRSgqvhm/0ZdAcXNZt9lYdKFpcRDT5ULUuYXPoT794UNOdU+gpT6Rzo7b4V2HUl/op6GqY894AZwv9faQ==", "signatures": [{"sig": "MEUCICJBQe2Y60KvNCLzT5WxA5gZ2zzypFVTegGleJJvx+rZAiEA4TrGPeo2EcYB3qcLSVRvVrZYSkxWXMczAF1xf9SoSGk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd1drhCRA9TVsSAnZWagAA+dMP/it5bVrCSip2D6SrsOhP\n2omcv+riWONUT2mhdthVYtABZhnL+MDyQOa4COYAXFocacST50tpTGX5QlKE\namv1ZNFtmnHvnBsa/XrjwjHCe8JFzYyeEUaa9gTJ8r6cEMrXs07sGyDi4gWy\nxQ7VQPq07lXXIoDmI7AaX+V6yYqSFH5JDwqRbPyvT1fqr/IznGCn+1WmKdCY\n7vtRd3tR8VmwRev5YvYzWSn8tqlrVzMMqG+q8JLNDawix1i1UMQB/N/NMJ5s\nz2jHkr2JUObEE6n16Zq8qac914uX7d0ylyvS/CScJIsVgbQ/UaOYqtKOt2VN\nFSX1CedVUuATMcDzxAoL39nOIVRg4X6eS6xi2UL0l6TstdV20Mx7HmGou9VF\nce4a6e5h8C5VC9M/dPEmOiXY2qEJaAxFCPEuE+PBCV0TjOnkP5gPf4yyaJ/F\n27UFbmxBvG97Lu3zkc9TW3qRiWfsui3DFVNgcHPbS+wIsCDEDK/gCGMU1657\nNH/Qwjg8OXMbcFmASpY9PkVJZFLB/aBRtMCSXcY+8pCWvNxPrruyWfrVd84W\nMpPf2mSu8vx05IeUM6oYkq6s5/EVMeHUl4V+8pm1y8prtWT04V+E8XNQo6tU\nNeFn3IaZmHXOMvPDJ7RMEcrcWDWtWt9tYyHGeBapwprMUFEq3ujSlwEbauB5\np/Tz\r\n=OpBp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "f42e0be5f676e6c82c83c9cad70f1fbb3b81c8ca", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "coverage": "covert test", "posttest": "npx aud", "prepublish": "safe-publish-latest", "tests-only": "node --es-staging --harmony test", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/inspect-js/is-symbol.git", "type": "git"}, "_npmVersion": "6.12.1", "description": "Determine if a value is an ES6 Symbol or not.", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"has-symbols": "^1.0.1"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"tape": "^4.11.0", "covert": "^1.1.1", "eslint": "^6.6.0", "semver": "^6.3.0", "auto-changelog": "^1.16.2", "object-inspect": "^1.7.0", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^15.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/is-symbol_1.0.3_1574296288922_0.5948718360504404", "host": "s3://npm-registry-packages"}}, "1.0.4": {"name": "is-symbol", "version": "1.0.4", "keywords": ["symbol", "es6", "is", "Symbol"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-symbol@1.0.4", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-symbol#readme", "bugs": {"url": "https://github.com/inspect-js/is-symbol/issues"}, "dist": {"shasum": "a6dac93b635b063ca6872236de88910a57af139c", "tarball": "https://registry.npmjs.org/is-symbol/-/is-symbol-1.0.4.tgz", "fileCount": 11, "integrity": "sha512-C/CPBqKWnvdcxqIARxyOh4v1UUEOCHpgDa0WYgpKDFMszcrPcffg5uhwSgPCLD2WWxmq6isisz87tzT01tuGhg==", "signatures": [{"sig": "MEQCIC9iqpVkhVuCO+879EuiFTzZv6BDe/NYz4DTxF8CseGmAiBVWGwPq+LWaMivKAEwGmBUjsQA+nBmLHNCgzNvjyqJXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22031, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgluKLCRA9TVsSAnZWagAApjYP+QHI60WHlwBK3tUNILAo\nsw1GFkDqV3dl6Nv6ubTsma8BvPPVj7n8ghEmElt0TBmdbNfUqM3jo5BqUWdV\nba/aqh4yXFK/n4P4V/+RVNyQTTN2mYe4g7ZXI23qBncgesWkiNZKTfevEJDT\nS6usePOf172ClhulqSyqji9Aj5NxRHMYPedLZuEoqKIiU0BVpk+ecGFNml9Z\n7u70oFCnxoHzOuuHWMHsO27VJ+weGxpPGuI0O+nEc5Wn72Fvc+OQ4gJTgJA9\n44gBpGbfWJc9cG4GmJdnDNC6rqpEvd21u4Wmd2eobR7JMuKyJDikA20ceBjb\n5STE85S7Kr/LCM7fHYc3nYr5lc+5VrF4alF5BrynvrJdsWczcMd4nA221FHC\ncEvRKtnrGwh+2Yx4EBhEEGnhei3vj1Va9p/FvMKCOL14oIq8SZy+wi6WBY0k\nlx9bDn5L//k63ZJHfj5PxyLM6QQBYmTPmilHh/zKq2Mpcf+jsoqXtV5YVwKZ\nv96E/zwrCxbWt2uA2+E6WrmeCsJuspGOCQfxCV5qZkxdB/kqNUPMWmwV4jMz\nIEL0FxSoD3PBwwz2qBCWZ9Sxtv+YI18JiUvaZyDxxJ618xn9QsH5YJ7QrMXG\ndl3KnPWb2dnVs2nRMjLh+s5i5tc8MkLUXKkFY3iZdJIorZ+q6kZVremaXVTS\n5ISf\r\n=Ag//\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "962084d30f3a2a83dff90d59504d03b85ffa5c17", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "npx aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/inspect-js/is-symbol.git", "type": "git"}, "_npmVersion": "7.10.0", "description": "Determine if a value is an ES6 Symbol or not.", "directories": {}, "_nodeVersion": "16.0.0", "dependencies": {"has-symbols": "^1.0.2"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^1.1.5", "nyc": "^10.3.2", "tape": "^5.2.2", "eslint": "^7.26.0", "auto-changelog": "^2.2.1", "object-inspect": "^1.10.3", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-symbol_1.0.4_1620501131081_0.753784298906609", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "is-symbol", "version": "1.1.0", "keywords": ["symbol", "es6", "is", "Symbol"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-symbol@1.1.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-symbol#readme", "bugs": {"url": "https://github.com/inspect-js/is-symbol/issues"}, "dist": {"shasum": "ae993830a56d4781886d39f9f0a46b3e89b7b60b", "tarball": "https://registry.npmjs.org/is-symbol/-/is-symbol-1.1.0.tgz", "fileCount": 12, "integrity": "sha512-qS8KkNNXUZ/I+nX6QT8ZS1/Yx0A444yhzdTKxCzKkNjQ9sHErBxJnJAgh+f5YhusYECEcjo4XcyH87hn6+ks0A==", "signatures": [{"sig": "MEYCIQDsJzFxNz/i7W1EyI/kT0YJSf5i3n1bMFegXAVcDmjxhgIhALyeTckvzLmTA5EoMXYr7GbieUfXJcosr7j1WNSqiLNR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26312}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "2b9dc79b5373d90df133f80d91d571d6c4e1ef4f", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "tsc -p . && attw -P", "posttest": "npx npm@'>=10.2' audit --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/inspect-js/is-symbol.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Determine if a value is an ES6 Symbol or not.", "directories": {}, "_nodeVersion": "23.3.0", "dependencies": {"call-bind": "^1.0.7", "has-symbols": "^1.0.3", "safe-regex-test": "^1.0.3"}, "publishConfig": {"ignore": [".github/workflows", ".nvmrc"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"nyc": "^10.3.2", "tape": "^5.9.0", "eslint": "=8.8.0", "encoding": "^0.1.13", "for-each": "^0.3.3", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "next", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "object-inspect": "^1.13.3", "@types/for-each": "^0.3.3", "has-tostringtag": "^1.0.2", "@ljharb/tsconfig": "^0.2.0", "@types/call-bind": "^1.0.5", "es-value-fixtures": "^1.5.0", "@types/has-symbols": "^1.0.2", "safe-publish-latest": "^2.0.0", "@arethetypeswrong/cli": "^0.17.0", "@ljharb/eslint-config": "^21.1.1", "@types/object-inspect": "^1.13.0", "@types/safe-regex-test": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/is-symbol_1.1.0_1733164988267_0.38662109580626325", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "is-symbol", "version": "1.1.1", "description": "Determine if a value is an ES6 Symbol or not.", "main": "index.js", "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "npx npm@'>=10.2' audit --production", "lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && attw -P", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/inspect-js/is-symbol.git"}, "keywords": ["symbol", "es6", "is", "Symbol"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/is-symbol/issues"}, "dependencies": {"call-bound": "^1.0.2", "has-symbols": "^1.1.0", "safe-regex-test": "^1.1.0"}, "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/for-each": "^0.3.3", "@types/object-inspect": "^1.13.0", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "es-value-fixtures": "^1.5.0", "eslint": "=8.8.0", "for-each": "^0.3.3", "has-tostringtag": "^1.0.2", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.3", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows", ".nvmrc"]}, "_id": "is-symbol@1.1.1", "gitHead": "db70b773b88a5b43995bea9540d0398773170ed8", "types": "./index.d.ts", "homepage": "https://github.com/inspect-js/is-symbol#readme", "_nodeVersion": "23.4.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-9gGx6GTtCQM73BgmHQXfDmLtfjjTUDSyoxTCbp5WtoixAhfgsDirWIcVQ/IHpvI5Vgd5i/J5F7B9cN/WlVbC/w==", "shasum": "f47761279f532e2b05a7024a7506dbbedacd0634", "tarball": "https://registry.npmjs.org/is-symbol/-/is-symbol-1.1.1.tgz", "fileCount": 12, "unpackedSize": 27042, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCa9b1PFHZT462l8AHWJ+Pv5bqcTBhSzlqRH/fC1SVhHQIgAoGQmpRZkV6a2rVtfOq/U8aRPhpvh20tF1yY3M8fkio="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/is-symbol_1.1.1_1734075461496_0.10986614940712225"}, "_hasShrinkwrap": false}}, "time": {"created": "2015-01-24T22:13:24.626Z", "modified": "2024-12-13T07:37:42.245Z", "1.0.0": "2015-01-24T22:13:24.626Z", "1.0.1": "2015-01-26T09:47:09.285Z", "1.0.2": "2018-09-21T00:29:14.405Z", "1.0.3": "2019-11-21T00:31:29.035Z", "1.0.4": "2021-05-08T19:12:11.304Z", "1.1.0": "2024-12-02T18:43:08.492Z", "1.1.1": "2024-12-13T07:37:41.707Z"}, "bugs": {"url": "https://github.com/inspect-js/is-symbol/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/inspect-js/is-symbol#readme", "keywords": ["symbol", "es6", "is", "Symbol"], "repository": {"type": "git", "url": "git://github.com/inspect-js/is-symbol.git"}, "description": "Determine if a value is an ES6 Symbol or not.", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "readme": "# is-symbol <sup>[![Version Badge][2]][1]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![dependency status][5]][6]\n[![dev dependency status][7]][8]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][11]][1]\n\nIs this an ES6 Symbol value?\n\n## Example\n\n```js\nvar isSymbol = require('is-symbol');\nassert(!isSymbol(function () {}));\nassert(!isSymbol(null));\nassert(!isSymbol(function* () { yield 42; return Infinity; });\n\nassert(isSymbol(Symbol.iterator));\nassert(isSymbol(Symbol('foo')));\nassert(isSymbol(Symbol.for('foo')));\nassert(isSymbol(Object(Symbol('foo'))));\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n[1]: https://npmjs.org/package/is-symbol\n[2]: https://versionbadg.es/inspect-js/is-symbol.svg\n[5]: https://david-dm.org/inspect-js/is-symbol.svg\n[6]: https://david-dm.org/inspect-js/is-symbol\n[7]: https://david-dm.org/inspect-js/is-symbol/dev-status.svg\n[8]: https://david-dm.org/inspect-js/is-symbol#info=devDependencies\n[11]: https://nodei.co/npm/is-symbol.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/is-symbol.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/is-symbol.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=is-symbol\n[codecov-image]: https://codecov.io/gh/inspect-js/is-symbol/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/inspect-js/is-symbol/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/inspect-js/is-symbol\n[actions-url]: https://github.com/inspect-js/is-symbol/actions\n", "readmeFilename": "README.md"}