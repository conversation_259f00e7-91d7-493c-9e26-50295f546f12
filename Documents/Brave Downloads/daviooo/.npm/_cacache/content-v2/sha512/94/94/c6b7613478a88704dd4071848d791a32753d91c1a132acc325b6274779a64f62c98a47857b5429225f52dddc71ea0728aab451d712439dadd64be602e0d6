{"_id": "uuid", "_rev": "461-649b9a8ef9a83810b632cc89de667e53", "name": "uuid", "dist-tags": {"latest": "11.1.0"}, "versions": {"0.0.1": {"name": "uuid", "version": "0.0.1", "author": {"name": "<PERSON><PERSON>"}, "_id": "uuid@0.0.1", "dist": {"shasum": "5b43a6840d25914b5a76a0664d71a51601ddec79", "tarball": "https://registry.npmjs.org/uuid/-/uuid-0.0.1.tgz", "integrity": "sha512-x3aIUBw/J5WMm+mfHLh5b7OelhczIY5/wr/b6JapW/SYdU4Yy7mW8AQ6vxecnRjy/qqe14mLV5vdA3c+4QCO/w==", "signatures": [{"sig": "MEUCIQCBH/OYMwdf/bhNLOnu7RN7FWR+xiRXSnjYCb9i79w44wIgPKNS2UlGKszZ04Kg4BfI1oLcscXAaz0j9MJNxflgWLw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/default/uuid", "engine": ["node >=0.1.103"], "engines": {"node": "*"}, "scripts": {"preinstall": "node-waf configure && node-waf build"}, "deprecated": "Please upgrade to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "repository": {"url": "http://bitbucket.org/nikhilm/uuidjs", "type": "hg"}, "_npmVersion": "0.2.7-2", "description": "Simple libuuid bindings to allow UUIDs to be generated from JS.", "directories": {}, "_nodeVersion": "v0.3.1-pre", "_nodeSupported": true}, "0.0.2": {"name": "uuid", "version": "0.0.2", "author": {"name": "<PERSON><PERSON>"}, "_id": "uuid@0.0.2", "dist": {"bin": {"0.4-sunos-5.11": {"shasum": "2ff8d977261ddadfd1446cee661ab87863659e45", "tarball": "http://registry.npmjs.org/uuid/-/uuid-0.0.2-0.4-sunos-5.11.tgz"}}, "shasum": "3171f2c4f58895b8b307692a335fb2349ddf6733", "tarball": "https://registry.npmjs.org/uuid/-/uuid-0.0.2.tgz", "integrity": "sha512-3h/4V/B5W+7FmanZTk1bQMDDoNstFk/2xy0W2W1s1WX8NPU2Sgrfi3GXZQvhqVZZiQAA7A7uUgOB4xzy0ngraA==", "signatures": [{"sig": "MEYCIQD1IQ4S7SggRuiiTxjJVzzfkHVzcPpPso/A+N++TgXqAgIhAP+LCzmHoT6q26ofm8BifbpLqynMNyNtpYHPSz7covx/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/default/uuid", "engine": ["node >=0.1.103"], "engines": {"node": "*"}, "scripts": {"preinstall": "node-waf configure && node-waf build"}, "deprecated": "Please upgrade to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "repository": {"url": "http://bitbucket.org/nikhilm/uuidjs", "type": "hg"}, "_npmVersion": "0.2.7-2", "description": "Simple libuuid bindings to allow UUIDs to be generated from JS.", "directories": {}, "_nodeVersion": "v0.3.1-pre", "_nodeSupported": true}, "1.4.0": {"name": "uuid", "version": "1.4.0", "keywords": ["uuid", "guid", "rfc4122"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "uuid@1.4.0", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "tim-smart", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "lib": ".", "url": "http://github.com/broofa/node-uuid", "dist": {"shasum": "d0d3b84ab56902e99ff952f2a17aa3986d44d36f", "tarball": "https://registry.npmjs.org/uuid/-/uuid-1.4.0.tgz", "integrity": "sha512-IzR48RgxTHa2bbD4KtzkfO11HrwBBpN536a3D1NRBBNKMCMbHjGHQRzhCuS1cMMptTagWTLIMVYCG5SP0UyEfg==", "signatures": [{"sig": "MEYCIQDphxshQcuL4XhLpEmBDKZcZvcgMOG7E9UXZTmirKi7bQIhAIiXlZ2xCnZhpsMmbD4dc3SgKm4zB5Wt09SUSUkO7jcF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./uuid.js", "_npmUser": {"name": "tim-smart", "email": "<EMAIL>"}, "deprecated": "Please upgrade to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "repository": {"url": "https://github.com/broofa/node-uuid.git", "type": "git"}, "_npmVersion": "1.1.66", "description": "Rigorous implementation of RFC4122 (v1 and v4) UUIDs.", "directories": {}}, "1.4.1": {"name": "uuid", "version": "1.4.1", "keywords": ["uuid", "guid", "rfc4122"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "uuid@1.4.1", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "tim-smart", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "a337828580d426e375b8ee11bd2bf901a596e0b8", "tarball": "https://registry.npmjs.org/uuid/-/uuid-1.4.1.tgz", "integrity": "sha512-VvxWRJy+jqowMX1wClasj2BIORh82/X3wkRNNpXDOh1tUxmVAbdEWRUM+yRVg30a+XBmf+duDVtMgvRiuGq0qw==", "signatures": [{"sig": "MEYCIQDGPgY/8s7EsZsplI7wUj9zkdvyAiFBNx8EMgYI37OuuQIhAPjpphlukLKOEBmORh7gaKymp9hSVj6dxl46neHEGac2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./uuid.js", "_from": ".", "browser": {"./rng.js": "./rng-browser.js"}, "scripts": {"test": "mocha test/test.js"}, "_npmUser": {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "harness": "mocha-tdd", "browsers": ["ie6..latest", "firefox/3.6..latest", "chrome/22..latest", "safari/5.1..latest"]}, "deprecated": "Please upgrade to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "repository": {"url": "https://github.com/shtylman/node-uuid.git", "type": "git"}, "_npmVersion": "1.2.11", "description": "Rigorous implementation of RFC4122 (v1 and v4) UUIDs.", "directories": {}, "devDependencies": {"mocha": "1.8.0"}}, "1.4.2": {"name": "uuid", "version": "1.4.2", "keywords": ["uuid", "guid", "rfc4122"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "uuid@1.4.2", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "tim-smart", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/shtylman/node-uuid", "bugs": {"url": "https://github.com/shtylman/node-uuid/issues"}, "dist": {"shasum": "453019f686966a6df83cdc5244e7c990ecc332fc", "tarball": "https://registry.npmjs.org/uuid/-/uuid-1.4.2.tgz", "integrity": "sha512-woV5Ei+GBJyrqMXt0mJ9p8/I+47LYKp/4urH76FNTMjl22EhLPz1tNrQufTsrFf/PYV/7ctSZYAK7fKPWQKg+Q==", "signatures": [{"sig": "MEQCIEzUTJA7B8PA2IbGadktJSU+HmCPu279aLDzPDr+5ZifAiArxPr9DAfvq+4Y+cA1zjnGMb2FD/j6UtrMiVyYubb5AA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./uuid.js", "_from": ".", "_shasum": "453019f686966a6df83cdc5244e7c990ecc332fc", "browser": {"./rng.js": "./rng-browser.js", "./buffer.js": "./buffer-browser.js"}, "gitHead": "688730efe3ec3ab7c12c6b92db2aa2826a50ed14", "scripts": {"test": "mocha test/test.js"}, "_npmUser": {"name": "vvo", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "harness": "mocha-tdd", "browsers": ["ie6..latest", "firefox/3.6..latest", "chrome/22..latest", "safari/5.1..latest"]}, "deprecated": "Please upgrade to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "repository": {"url": "https://github.com/shtylman/node-uuid.git", "type": "git"}, "_npmVersion": "2.0.0", "description": "Rigorous implementation of RFC4122 (v1 and v4) UUIDs.", "directories": {}, "devDependencies": {"mocha": "1.8.0"}}, "2.0.0": {"name": "uuid", "version": "2.0.0", "keywords": ["uuid", "guid", "rfc4122"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "uuid@2.0.0", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "tim-smart", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/shtylman/node-uuid", "bugs": {"url": "https://github.com/shtylman/node-uuid/issues"}, "dist": {"shasum": "377ab4417736dba5ce379ff0a0c1a539921ebb74", "tarball": "https://registry.npmjs.org/uuid/-/uuid-2.0.0.tgz", "integrity": "sha512-MgCjmgHKiEVlRQ24qLbInOkKOrg1g8VhoXlzFHWY5dXfT/HLfcUomFyoQPIpp7YZ3ymtteUJBYhcYISFmmnsHw==", "signatures": [{"sig": "MEYCIQCd80jdLlS0JaoA9GQYpc/J2PNQ1x2BfrhCSmDTv8DkvwIhAPBr4w2SNVwKjeFrw3TYtCYSIlCji/5RlMwSgXDgROLy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./uuid.js", "_from": ".", "_shasum": "377ab4417736dba5ce379ff0a0c1a539921ebb74", "browser": {"./rng.js": "./rng-browser.js"}, "gitHead": "3c007a5748d6fcbd0916ea8b6d18539958972932", "scripts": {"test": "mocha test/test.js"}, "_npmUser": {"name": "vvo", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "harness": "mocha-tdd", "browsers": ["ie6..latest", "firefox/3.6..latest", "chrome/22..latest", "safari/5.1..latest"]}, "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "repository": {"url": "https://github.com/shtylman/node-uuid.git", "type": "git"}, "_npmVersion": "2.0.0", "description": "Rigorous implementation of RFC4122 (v1 and v4) UUIDs.", "directories": {}, "devDependencies": {"mocha": "1.8.0"}}, "2.0.1": {"name": "uuid", "version": "2.0.1", "keywords": ["uuid", "guid", "rfc4122"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "uuid@2.0.1", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "tim-smart", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/shtylman/node-uuid", "bugs": {"url": "https://github.com/shtylman/node-uuid/issues"}, "dist": {"shasum": "c2a30dedb3e535d72ccf82e343941a50ba8533ac", "tarball": "https://registry.npmjs.org/uuid/-/uuid-2.0.1.tgz", "integrity": "sha512-nWg9+Oa3qD2CQzHIP4qKUqwNfzKn8P0LtFhotaCTFchsV7ZfDhAybeip/HZVeMIpZi9JgY1E3nUlwaCmZT1sEg==", "signatures": [{"sig": "MEYCIQC+RbHqOIrw5jch5eIF2wZ6dJn7iwEtFB213kSmjR2nnQIhANNVG6MBAl7ce2sImkZvEahj1M4vS1K1iZr6lT69DfZI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./uuid.js", "_from": ".", "_shasum": "c2a30dedb3e535d72ccf82e343941a50ba8533ac", "browser": {"./rng.js": "./rng-browser.js"}, "gitHead": "ddaf90942095f26ee8c1961b4346f093b3e7eb5b", "scripts": {"test": "mocha test/test.js"}, "_npmUser": {"name": "vvo", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "harness": "mocha-tdd", "browsers": ["ie6..latest", "firefox/3.6..latest", "chrome/22..latest", "safari/5.1..latest"]}, "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "repository": {"url": "https://github.com/shtylman/node-uuid.git", "type": "git"}, "_npmVersion": "2.0.0", "description": "Rigorous implementation of RFC4122 (v1 and v4) UUIDs.", "directories": {}, "devDependencies": {"mocha": "1.8.0"}}, "2.0.2": {"name": "uuid", "version": "2.0.2", "keywords": ["uuid", "guid", "rfc4122"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "uuid@2.0.2", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "tim-smart", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/shtylman/node-uuid#readme", "bugs": {"url": "https://github.com/shtylman/node-uuid/issues"}, "dist": {"shasum": "48bd5698f0677e3c7901a1c46ef15b1643794726", "tarball": "https://registry.npmjs.org/uuid/-/uuid-2.0.2.tgz", "integrity": "sha512-BooSif/UQWXwaQme+4z32duvmtUUz/nlHsyGrrSCgsGf6snMrp9q/n1nGHwQzU12kaCeceODmAiRZA8TCK06jA==", "signatures": [{"sig": "MEQCIHzzSD5cFI/bZ7LZWR2x6Y5ob8g4kek37JC/3t6MtnnYAiAHmHCbiiqkks6nXFZZBb0KnM2dpNeFhTgPwWgOuzIAjA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./uuid.js", "_from": ".", "_shasum": "48bd5698f0677e3c7901a1c46ef15b1643794726", "browser": {"./rng.js": "./rng-browser.js"}, "gitHead": "6e95855ff4b79881aa95c5502478314adc6719dc", "scripts": {"test": "mocha test/test.js"}, "_npmUser": {"name": "vvo", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "harness": "mocha-tdd", "browsers": ["ie6..latest", "firefox/3.6..latest", "chrome/22..latest", "safari/5.1..latest"]}, "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "repository": {"url": "git+https://github.com/shtylman/node-uuid.git", "type": "git"}, "_npmVersion": "2.15.2", "description": "Rigorous implementation of RFC4122 (v1 and v4) UUIDs.", "directories": {}, "_nodeVersion": "5.9.1", "devDependencies": {"mocha": "1.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/uuid-2.0.2.tgz_1460533041436_0.07706069457344711", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.3": {"name": "uuid", "version": "2.0.3", "keywords": ["uuid", "guid", "rfc4122"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "uuid@2.0.3", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "tim-smart", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/defunctzombie/node-uuid#readme", "bugs": {"url": "https://github.com/defunctzombie/node-uuid/issues"}, "dist": {"shasum": "67e2e863797215530dff318e5bf9dcebfd47b21a", "tarball": "https://registry.npmjs.org/uuid/-/uuid-2.0.3.tgz", "integrity": "sha512-FULf7fayPdpASncVy4DLh3xydlXEJJpvIELjYjNeQWYUZ9pclcpvCZSr2gkmN2FrrGcI7G/cJsIEwk5/8vfXpg==", "signatures": [{"sig": "MEUCIQCL2ujhY5VO83wMf65KONzxZ9Gv0SEYCLX49+BD3e6cjwIgKF3ja8+IHLoxkTF88HrygIa+MdIvfyDuQZJzUY86aZQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./uuid.js", "_from": ".", "_shasum": "67e2e863797215530dff318e5bf9dcebfd47b21a", "browser": {"./rng.js": "./rng-browser.js"}, "gitHead": "3f44acd0e722e965c14af816e2f658361a6b15f9", "scripts": {"test": "mocha test/test.js"}, "_npmUser": {"name": "defunctzombie", "email": "<EMAIL>"}, "testling": {"files": "test/*.js", "harness": "mocha-tdd", "browsers": ["ie6..latest", "firefox/3.6..latest", "chrome/22..latest", "safari/5.1..latest"]}, "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "repository": {"url": "git+https://github.com/defunctzombie/node-uuid.git", "type": "git"}, "_npmVersion": "3.8.9", "description": "Rigorous implementation of RFC4122 (v1 and v4) UUIDs.", "directories": {}, "_nodeVersion": "6.2.0", "devDependencies": {"mocha": "1.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/uuid-2.0.3.tgz_1474232617862_0.6578061426989734", "host": "packages-12-west.internal.npmjs.com"}}, "3.0.0": {"name": "uuid", "version": "3.0.0", "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "_id": "uuid@3.0.0", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "tim-smart", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kelektiv/node-uuid#readme", "bugs": {"url": "https://github.com/kelektiv/node-uuid/issues"}, "bin": {"uuid": "./bin/uuid"}, "dist": {"shasum": "6728fc0459c450d796a99c31837569bdf672d728", "tarball": "https://registry.npmjs.org/uuid/-/uuid-3.0.0.tgz", "integrity": "sha512-rqE1LoOVLv3QrZMjb4NkF5UWlkurCfPyItVnFPNKDDGkHw4dQUdE4zMcLqx28+0Kcf3+bnUk4PisaiRJT4aiaQ==", "signatures": [{"sig": "MEQCIECZiYO/0a0dU1onAlLliDR6TCXZyhvPTBWIoiopzKzcAiBMa9RVCF7K3J7goNPNcElB2keP5F7UZAmvyUqATmaX2Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./uuid.js", "_from": ".", "_shasum": "6728fc0459c450d796a99c31837569bdf672d728", "browser": {"./lib/rng.js": "./lib/rng-browser.js"}, "gitHead": "923fe4a7893c2057b608c0c74743ad5512599072", "scripts": {"test": "mocha test/test.js"}, "_npmUser": {"name": "defunctzombie", "email": "<EMAIL>"}, "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "repository": {"url": "git+https://github.com/kelektiv/node-uuid.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "RFC4122 (v1 and v4) generator", "directories": {}, "_nodeVersion": "6.7.0", "devDependencies": {"mocha": "3.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/uuid-3.0.0.tgz_1479448535568_0.9578766466584057", "host": "packages-18-east.internal.npmjs.com"}}, "3.0.1": {"name": "uuid", "version": "3.0.1", "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "_id": "uuid@3.0.1", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kelektiv/node-uuid#readme", "bugs": {"url": "https://github.com/kelektiv/node-uuid/issues"}, "bin": {"uuid": "./bin/uuid"}, "dist": {"shasum": "6544bba2dfda8c1cf17e629a3a305e2bb1fee6c1", "tarball": "https://registry.npmjs.org/uuid/-/uuid-3.0.1.tgz", "integrity": "sha512-tyhM7iisckwwmyHVFcjTzISz/R1ss/bRudNgHFYsgeu7j4JbhRvjE+Hbcpr9y5xh+b+HxeFjuToDT4i9kQNrtA==", "signatures": [{"sig": "MEYCIQCW7k8Jo30nhFQOdhmvhcANJL+gxbfSahTliBAKeBtkWgIhAKsDlHuPurAxguGBTu1Pg8y2bObl7VKJedHHWamXI//4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "6544bba2dfda8c1cf17e629a3a305e2bb1fee6c1", "browser": {"./lib/rng.js": "./lib/rng-browser.js"}, "gitHead": "374de826de71d8997f71b4641f65552e48956ced", "scripts": {"test": "mocha test/test.js"}, "_npmUser": {"name": "defunctzombie", "email": "<EMAIL>"}, "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "repository": {"url": "git+https://github.com/kelektiv/node-uuid.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "RFC4122 (v1 and v4) generator", "directories": {}, "_nodeVersion": "6.7.0", "devDependencies": {"mocha": "3.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/uuid-3.0.1.tgz_1480403886767_0.2584113120101392", "host": "packages-12-west.internal.npmjs.com"}}, "3.1.0": {"name": "uuid", "version": "3.1.0", "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "_id": "uuid@3.1.0", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kelektiv/node-uuid#readme", "bugs": {"url": "https://github.com/kelektiv/node-uuid/issues"}, "bin": {"uuid": "./bin/uuid"}, "dist": {"shasum": "3dd3d3e790abc24d7b0d3a034ffababe28ebbc04", "tarball": "https://registry.npmjs.org/uuid/-/uuid-3.1.0.tgz", "integrity": "sha512-DIWtzUkw04M4k3bf1IcpS2tngXEL26YUD2M0tMDUpnUrz2hgzUBlD55a4FjdLGPvfHxS6uluGWvaVEqgBcVa+g==", "signatures": [{"sig": "MEUCIQDHvTmx2CLchJzwlxayiHWEIH3U6odSPQZoZR1/I3HIsQIgJwtjBLGzn0VdcD2NQyYT8SuDkxU+5XOTF/r95Vy6fEc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "browser": {"./lib/rng.js": "./lib/rng-browser.js", "./lib/sha1.js": "./lib/sha1-browser.js"}, "gitHead": "c50ac88f098ecfbff9a940816c8e6825ffd7e05a", "scripts": {"test": "mocha test/test.js"}, "_npmUser": {"name": "broofa", "email": "<EMAIL>"}, "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "repository": {"url": "git+https://github.com/kelektiv/node-uuid.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "RFC4122 (v1, v4, and v5) UUIDs", "directories": {}, "_nodeVersion": "7.10.0", "devDependencies": {"mocha": "3.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/uuid-3.1.0.tgz_1497635691778_0.6424044836312532", "host": "s3://npm-registry-packages"}}, "3.2.0": {"name": "uuid", "version": "3.2.0", "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "_id": "uuid@3.2.0", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kelektiv/node-uuid#readme", "bugs": {"url": "https://github.com/kelektiv/node-uuid/issues"}, "bin": {"uuid": "./bin/uuid"}, "dist": {"shasum": "19a63e22b3b32a0ba23984a4f384836465e24949", "tarball": "https://registry.npmjs.org/uuid/-/uuid-3.2.0.tgz", "integrity": "sha512-qC0vMFX6q6ee8JaoTF2Om1uL8KAV1ATUjVaHRxLiPJkIsp8JZl6ZjG0MIB+twZFLbi1vXj30rqj4zlqYiOS9xg==", "signatures": [{"sig": "MEUCIQCIjmQ5Rnw+x+5I5xRFDUrpAN7G9KZFwaC+pMY3QWgoZgIgBJpZgttKkDKQ9KIagxs7x8di73iKVzacBIHEcJiScLw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "browser": {"./lib/md5.js": "./lib/md5-browser.js", "./lib/rng.js": "./lib/rng-browser.js", "./lib/sha1.js": "./lib/sha1-browser.js"}, "gitHead": "c0d44fda8abeac5eb1444f03a135f8a6353854da", "scripts": {"md": "runmd --watch --output=README.md README_js.md", "test": "mocha test/test.js", "prepare": "runmd --output=README.md README_js.md", "release": "standard-version"}, "_npmUser": {"name": "broofa", "email": "<EMAIL>"}, "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "repository": {"url": "git+https://github.com/kelektiv/node-uuid.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "RFC4122 (v1, v4, and v5) UUIDs", "directories": {}, "_nodeVersion": "7.10.1", "dependencies": {}, "devDependencies": {"mocha": "3.1.2", "runmd": "1.0.1", "eslint": "4.5.0", "standard-version": "4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/uuid-3.2.0.tgz_1516113890512_0.8254034700803459", "host": "s3://npm-registry-packages"}}, "3.2.1": {"name": "uuid", "version": "3.2.1", "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "_id": "uuid@3.2.1", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kelektiv/node-uuid#readme", "bugs": {"url": "https://github.com/kelektiv/node-uuid/issues"}, "bin": {"uuid": "./bin/uuid"}, "dist": {"shasum": "12c528bb9d58d0b9265d9a2f6f0fe8be17ff1f14", "tarball": "https://registry.npmjs.org/uuid/-/uuid-3.2.1.tgz", "integrity": "sha512-jZnMwlb9Iku/O3smGWvZhauCf6cvvpKi4BKRiliS3cxnI+Gz9j5MEpTz2UFuXiKPJocb7gnsLHwiS05ige5BEA==", "signatures": [{"sig": "MEUCIQClBCsV3pD6yvLgviye5VlbJj5SXjRREEIhYsBxnwyRDAIgXalnZy9mRWZ++bns2tds6LMNA1UK9hicf8ML6os4q7k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "browser": {"./lib/md5.js": "./lib/md5-browser.js", "./lib/rng.js": "./lib/rng-browser.js", "./lib/sha1.js": "./lib/sha1-browser.js"}, "gitHead": "ce7d3176fc927ab83b4e29b8d5a37b7243ce3360", "scripts": {"md": "runmd --watch --output=README.md README_js.md", "test": "mocha test/test.js", "prepare": "runmd --output=README.md README_js.md", "release": "standard-version"}, "_npmUser": {"name": "broofa", "email": "<EMAIL>"}, "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "repository": {"url": "git+https://github.com/kelektiv/node-uuid.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "RFC4122 (v1, v4, and v5) UUIDs", "directories": {}, "_nodeVersion": "7.10.1", "dependencies": {}, "devDependencies": {"mocha": "3.1.2", "runmd": "1.0.1", "eslint": "4.5.0", "standard-version": "4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/uuid-3.2.1.tgz_1516124697652_0.3172094284091145", "host": "s3://npm-registry-packages"}}, "3.3.0": {"name": "uuid", "version": "3.3.0", "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "_id": "uuid@3.3.0", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kelektiv/node-uuid#readme", "bugs": {"url": "https://github.com/kelektiv/node-uuid/issues"}, "bin": {"uuid": "./bin/uuid"}, "dist": {"shasum": "b237147804881d7b86f40a7ff8f590f15c37de32", "tarball": "https://registry.npmjs.org/uuid/-/uuid-3.3.0.tgz", "fileCount": 21, "integrity": "sha512-ijO9N2xY/YaOqQ5yz5c4sy2ZjWmA6AR6zASb/gdpeKZ8+948CxwfMW9RrKVk5may6ev8c0/Xguu32e2Llelpqw==", "signatures": [{"sig": "MEUCIDs6gd+UESKBONyc0AZr2W/e7smAg2PJSdGS77pEClqkAiEAynrxQAuB2ALbGrEHk2ErCK0tjTOvLCiDfnmqpW6VbTc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44057, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbMj5bCRA9TVsSAnZWagAAoowP/05xAG0lM80CctZLOyEd\nkUJj8ihe+AkwLCMa+7HgJpbc/NiNhJtjN0I9peEEXoGMrg+IZOehcUabm3pN\nUz29450CXc1mEeEWjTBHfE0fZOWGVSHrbVhKuqZByS/Gq4W9hHOMQcRbeDOM\nHUg2Cg1BD9YEJgCdWbMAZHkWmE36FJ7UaaRcZNn1qhzj5KlNqft8tRAxt0n5\nsQ099IitF+d8rMYxDHS23GCAEHDcwM0rolyQJio3mMmFi5GDdYoDybNCR/Jx\nyETA3f3PVdybkfPuCwufwfFH9YDBPHo0tCikpi9Sz4sKXxdDo7lxXToP5+ai\nujTl5AB+oEKtbjLIugrISDfaHRcRGV/z0lnoXJOl+i/GM9FCkoxwA6r+HnOX\ner8HwUFSDrd0YV37EJMKLPgWLqdJTpZHCZjuU5b9RhbAujp5gGpUO1iITgJC\nJ/WcMkTaaGcHteJuSV39UV4tG0B3fHaC9/eHoF/jrsoJGRfZhbKECMXcTTLM\n6+FNvIA3PGE+onKyPutX2ROCciL4bxS6Rih6cU4pbOUV5SmpbPFkW6XX3UY5\n53K64Qjuv1wNCNS2WzYBnU/HGqwgcg/Pefdi7A+n6Z87/2fxfEqQf24xjOk4\naa/VCC/3mCnVzTMeSa1ckCQSoQNh/f05wtZ7qf90PGx4NFwzT5CgNDnhovER\nyl2Z\r\n=lu2+\r\n-----END PGP SIGNATURE-----\r\n"}, "browser": {"./lib/md5.js": "./lib/md5-browser.js", "./lib/rng.js": "./lib/rng-browser.js", "./lib/sha1.js": "./lib/sha1-browser.js"}, "gitHead": "1cb9826260b46352db19ccbfb36dfdbc323c35c9", "scripts": {"md": "runmd --watch --output=README.md README_js.md", "test": "mocha test/test.js", "prepare": "runmd --output=README.md README_js.md", "release": "standard-version", "commitmsg": "commitlint -E GIT_PARAMS"}, "_npmUser": {"name": "broofa", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "repository": {"url": "git+https://github.com/kelektiv/node-uuid.git", "type": "git"}, "_npmVersion": "6.0.0", "description": "RFC4122 (v1, v4, and v5) UUIDs", "directories": {}, "_nodeVersion": "8.11.1", "_hasShrinkwrap": false, "devDependencies": {"husky": "0.14.3", "mocha": "5.2.0", "runmd": "1.0.1", "eslint": "4.19.1", "@commitlint/cli": "7.0.0", "standard-version": "4.4.0", "@commitlint/config-conventional": "7.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/uuid_3.3.0_1530019419356_0.9632179204070574", "host": "s3://npm-registry-packages"}}, "3.3.2": {"name": "uuid", "version": "3.3.2", "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "_id": "uuid@3.3.2", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kelektiv/node-uuid#readme", "bugs": {"url": "https://github.com/kelektiv/node-uuid/issues"}, "bin": {"uuid": "./bin/uuid"}, "dist": {"shasum": "1b4af4955eb3077c501c23872fc6513811587131", "tarball": "https://registry.npmjs.org/uuid/-/uuid-3.3.2.tgz", "fileCount": 21, "integrity": "sha512-yXJmeNaw3DnnKAOKJE51sL/ZaYfWJRl1pK9dr19YFCu0ObS231AB1/LbqTKRAQ5kw8A90rA6fr4riOUpTZvQZA==", "signatures": [{"sig": "MEUCIGzp+lfrn4ozcY3DNpdZOR57g9DG17uNy5jtH629WIiqAiEAoJMJz6bhcioSGQjR5D6LZypvYtTfpEIFlNZL6x8/goM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43599, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbNVMdCRA9TVsSAnZWagAAHMAP/3dazKIzx5o8pB3uqIOa\nUj3OOWemMXXKN6m/ncbq23zi69Vi0WRpYwC8vbRiD+NwXdnV5yP5fesAoGjw\ngttCujsby2Q96xqGDmnpxF//+OZC2/fPt/EBVtvlZM/KSpevRoDBF03ihcFL\nGyAFS7i0FvRJskP2xp+72Z+6C6A24OvW7BB+WGJcTh8z7QbStTPDQmjam1jM\n9lkez4SbOpFd4Hpca1yFM8HRb6UGUFo+UdIoylbaeKnUDBzCmtv1LwtkL77I\noMB8AyHna8iUjv43l08EODev+G08dBQcrL2K83WMp+ztAHwi8KOryxTLKgUV\nvZwaObtCH77QSnraFF5Vq1onGw+FTckQlP0zS37e5IKSRv0GV/t3E1aSkemt\ndDU2qURkFLyVuGSV8utAMWvwTDA6CEsXeEs49ZEYtZWKeEKc1dXxx0ovVMBV\nxGFgmTnP+pYXUCnqrXlJfkfEtBvGhU6N4KmfBz8tAWjfafYFZng9pQxF04vh\nMgHOy7upIMtDKZCyCGW+C81L24cdggmOVGfLHIpMLbMpR05cS1gXqHGe1OBy\nY5SP34YgMjhRnyfvb1LlcDJEjJvruzx46UP/D9JDBmjhLTJSDxwQuN+UrjPx\n9WhQEv/P3CvSlHVpfd+NF6b0vwM7IoiWbxMwF3z9jZpNU7o+3pySZrni3aMC\nHEgb\r\n=qIAF\r\n-----END PGP SIGNATURE-----\r\n"}, "browser": {"./lib/md5.js": "./lib/md5-browser.js", "./lib/rng.js": "./lib/rng-browser.js", "./lib/sha1.js": "./lib/sha1-browser.js"}, "gitHead": "fe4ae79c55af2c7cbf2bb39d3bcb6716d5367091", "scripts": {"md": "runmd --watch --output=README.md README_js.md", "test": "mocha test/test.js", "prepare": "runmd --output=README.md README_js.md", "release": "standard-version", "commitmsg": "commitlint -E GIT_PARAMS"}, "_npmUser": {"name": "broofa", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "repository": {"url": "git+https://github.com/kelektiv/node-uuid.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "RFC4122 (v1, v4, and v5) UUIDs", "directories": {}, "_nodeVersion": "10.5.0", "_hasShrinkwrap": false, "devDependencies": {"husky": "0.14.3", "mocha": "5.2.0", "runmd": "1.0.1", "eslint": "4.19.1", "@commitlint/cli": "7.0.0", "standard-version": "4.4.0", "@commitlint/config-conventional": "7.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/uuid_3.3.2_1530221341707_0.5404045638057293", "host": "s3://npm-registry-packages"}}, "3.3.3": {"name": "uuid", "version": "3.3.3", "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "_id": "uuid@3.3.3", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/kelektiv/node-uuid#readme", "bugs": {"url": "https://github.com/kelektiv/node-uuid/issues"}, "bin": {"uuid": "./bin/uuid"}, "dist": {"shasum": "4568f0216e78760ee1dbf3a4d2cf53e224112866", "tarball": "https://registry.npmjs.org/uuid/-/uuid-3.3.3.tgz", "fileCount": 19, "integrity": "sha512-pW0No1RGHgzlpHJO1nsVrHKpOEIxkGg1xB+v0ZmdNH5OAeAwzAVrCnI2/6Mtx+Uys6iaylxa+D3g4j63IKKjSQ==", "signatures": [{"sig": "MEUCIQCdiKgMKeIQZbxEHTpmOAI6yGO4NhMPjPfJDOmeOKZw9QIgRmH0bjXJIwsj2pvSKGCaSvUVviVMgMKxvMDgZ9u/XNg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34683, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdWqOBCRA9TVsSAnZWagAA1b0P/30Jwx5qz0EAfdMRewwZ\nNaClKkF9nznrtjK84p4vrf+/7nlXfclQtZO65pcVSgXYQsqUTadATY/nHIdV\nC/TYyrgk0fjS5TkLzMs8268S5iO+ruCAyCXA27JqVyfFGxlPZRTneukR71R8\n7Htg3MnZMM5REIM7xckj1maZpi+SNEfvbNNY9Je4bKnAWNX1ezznicfpEaT7\nXQkoOakMKked+CGJhTEXdwKBn9rBKjNr74DMgXiIlEnL6pn7vhNu10zTY1cp\njbsdkOptkWsQBUMzDfkjpBJc+xnji80SSekfZ0iLt5Qak4fYzUcnbZM2XCaC\nvJEz2cLYlbhJox3K92/ikwsBZTGaXtwB96DQbJJ8zDvx7DtTq53tSF1qTgTf\ni+Nm8HDRGbGMkTseJm0sm83NYPQY4RQbiR4bcJNlQQ9ZLWCJSdfudaHkKOU9\nPyFW0aP1LT3qzitpAvHDV3B1fnQEvBJplmVOdm5F0qnTXEmgekjfQMGc5GMG\nDt42B9Vjxs2oVQszMKuG1K+ouXk4JcIQMXYYZA9JfpEmAocHfgam0Y03MZ2J\nb5+7YmeqIZu7n3PwVKTdsLQLPMmibtg6lCC+Akfq1ytuDWVPREIS7JM5Bts7\ngTiRG8rSPeNpLetCN25Ke02xbZDJof3dTJZxWu2vSTeczrh2BFqLjshy/5r4\ny3+j\r\n=wnZc\r\n-----END PGP SIGNATURE-----\r\n"}, "browser": {"./lib/md5.js": "./lib/md5-browser.js", "./lib/rng.js": "./lib/rng-browser.js", "./lib/sha1.js": "./lib/sha1-browser.js"}, "gitHead": "ab33c8a77df180de1a12fede423490b3c2de9d07", "scripts": {"md": "runmd --watch --output=README.md README_js.md", "test": "mocha test/test.js", "prepare": "runmd --output=README.md README_js.md", "release": "standard-version", "commitmsg": "commitlint -E HUSKY_GIT_PARAMS"}, "_npmUser": {"name": "broofa", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "repository": {"url": "git+https://github.com/kelektiv/node-uuid.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "RFC4122 (v1, v4, and v5) UUIDs", "directories": {}, "_nodeVersion": "8.15.1", "_hasShrinkwrap": false, "devDependencies": {"husky": "3.0.4", "mocha": "6.2.0", "runmd": "1.2.1", "eslint": "6.2.0", "@commitlint/cli": "8.1.0", "standard-version": "7.0.0", "@commitlint/config-conventional": "8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/uuid_3.3.3_1566221184938_0.9358725731017152", "host": "s3://npm-registry-packages"}}, "3.4.0": {"name": "uuid", "version": "3.4.0", "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "_id": "uuid@3.4.0", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/uuidjs/uuid#readme", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "bin": {"uuid": "./bin/uuid"}, "dist": {"shasum": "b23e4358afa8a202fe7a100af1f5f883f02007ee", "tarball": "https://registry.npmjs.org/uuid/-/uuid-3.4.0.tgz", "fileCount": 19, "integrity": "sha512-HjSDRw6gZE5JMggctHBcjVak08+KEVhSIiDzFnT9S9aegmp85S/bReBVTb4QTFaRNptJ9kuYaNhnbNEOkbKb/A==", "signatures": [{"sig": "MEUCIB3lNgQ7KQZkVcdWdJzevvb8bQQfG/LcAeGEowNFZwntAiEA8VSKkOxvxVbnJSL57iyyAamxsH6Noy4+xlyyUnZvsRk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34269, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeINAiCRA9TVsSAnZWagAAOHsP/RVFM7OJamQHALP58acz\nQHrh9S8dhiix0+OGPfzJjPDnB/ver8y3weYkOtTXVU6qempJOgVZnigeR2yZ\nH00hUco5UxkK0j1eg1afyGoeso4jjNBtpVkyPnCb2Cg0A8l0g8I6zzb3LByf\nwv74IPlhjXlYyZsIlYwan5q4w5nRsf6+xdV4qdYXquxf05hCYgt3pWV4tSe6\nE2Y+WWZSVpYBpLyY1p7IJBk6xAYmwF1M8D+G0weLJCoOMa41fvhRsd2E84iW\nJYUDCKn+NfMoN4RaphGSBcpQD3POgE4UaI8D/QpFYUpF2bCQDFVakfqYsmtv\nsdCILUlRdhWmOJuoDc5GNk6iD83rtaqu+nwdNsBUal6iF+/8L1E8aCJ0VleI\nr5gLMm/kSl14lFue3iPkUIadoDu4rclpuPs2tiOIxH0Dnt9FHMw69uUr50O1\noCTLM2da2bZ9Vm9UxyIWEdx1TOFtiL4pWfcoQUQcRMDQ3wyoSmv/3ryfd+zl\n3HGwOM5w5zqOOs0JUizf90CRWvL48VUnaeKxL6xcS3OqbJmq7bDmfM2bAdJE\nI0g73oAf9/Ouch1l9Bt1mcZACtUeBYPSvlvN1u0jLzTqPP2VxBWboqZl291P\npR2Nqk3JDmcvY4HYl1oZ7wITiiiaARlTfNY4z2U5v8/f+QAtgwO/SPfHMXJL\nECdn\r\n=rDnh\r\n-----END PGP SIGNATURE-----\r\n"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "browser": {"./lib/md5.js": "./lib/md5-browser.js", "./lib/rng.js": "./lib/rng-browser.js", "./lib/sha1.js": "./lib/sha1-browser.js"}, "gitHead": "3df73a98f07c0a38a94bcaf1ecde0e384dc3b126", "scripts": {"md": "runmd --watch --output=README.md README_js.md", "lint": "eslint .", "test": "npm run lint && mocha test/test.js", "prepare": "runmd --output=README.md README_js.md", "release": "standard-version"}, "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "repository": {"url": "git+https://github.com/uuidjs/uuid.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "RFC4122 (v1, v4, and v5) UUIDs", "directories": {}, "_nodeVersion": "12.13.0", "_hasShrinkwrap": false, "devDependencies": {"husky": "~3.0.5", "mocha": "6.2.0", "runmd": "1.2.1", "eslint": "~6.4.0", "@commitlint/cli": "~8.2.0", "standard-version": "7.0.0", "@commitlint/config-conventional": "~8.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/uuid_3.4.0_1579208738423_0.4772515028870088", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.0": {"name": "uuid", "version": "7.0.0-beta.0", "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "_id": "uuid@7.0.0-beta.0", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/uuidjs/uuid#readme", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "7f2bd91cf896f8f3d8d911d995edd7d66d4c2651", "tarball": "https://registry.npmjs.org/uuid/-/uuid-7.0.0-beta.0.tgz", "fileCount": 36, "integrity": "sha512-Am22LVM3UXB0DTzQAeDSsZwP5eyqjIhmff330hqkxGvIxX8RRrUYLtKJ0eYxiBgjeQdUaMONpBZbJachMShxBw==", "signatures": [{"sig": "MEYCIQDEK8JeminS5kvK+FXpwP9qLnE/Pij9kySPBNdZsTysIAIhAOlWZ9zLNvwc4qgCDpk5FRxUnkQgtz5Q1MuincsNk5Nv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68693, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeSngmCRA9TVsSAnZWagAAbkwP/RXJ5KOgqWONL6GrOdrx\nRRzSvg0VlcF8bj18EAg4ZEzWjgyCtLjAKfnjlQkfj1twaxlrrgbHBiYhEzME\nPr/8tUXn6/Hix13r7Zrkw+kJdISFz2acM6u0sgGJZ+ooxgXtwP/Xs2BVVk4k\n5u5cEN9GpyPxUvrDDgE8BPTDoQ1MxTU+4DWRcb57VIki26O44vvNYZGZJteR\nD/+P/oqHWNZ0hy01Ed5yXB3V/u92E3yc7FPKgGTLmmJjAdCRNifBjcEoQT51\n3/RlPkXB/yTza6fwaiVl+DA7FypppthNbo90drJqboS5iE4B/6ZwApSRrnKf\nWnKQDFWl/XWpNjtga+TC5VjN/KQbptheM3Xus0m9KNvmHUna/5svGJBkptqV\n3TSruCViqBaML1yxD1wTiArGacVakAbZRKSbz9XunOMIf8Qq6/unnBIRQDEi\njXbi+tHmGlJ9GzGn/Z4LXf7zwPPZV796LxIfq+TUsEQVrszJ1SRTsUkZ8oMg\nWOz4DwsI6DDU3KDBNiq+FqSfULRF/TwfrcybkfZDNyfXWbFPgk6bF583fN5e\n9l2TNs30eBsoCJiaLkCGnf4oHhUSMJhzTQoV8YyU/zwq+n04jNmP9S2PnkaW\nHZ+NAiAxMdi/y/3hEiaZAaEAQYhu3VVvjjeu6WU18OApWmF6E6idnLyb+A5V\nfXdA\r\n=UYmC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "module": "dist/esm-browser/index.js", "gitHead": "522e44c055871493303ec0158dac75d7ce1eba5b", "scripts": {"ci": "npm run lint && npm run test && npm run prettier:check && npm run docs:diff && npm run bundlewatch", "md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v12' ) && ( npm run build && runmd --output=README.md README_js.md )", "lint": "npm run eslint:check && npm run prettier:check", "test": "BABEL_ENV=commonjs node --throw-deprecation node_modules/.bin/jest test/unit/", "build": "./scripts/build.sh", "pretest": "npm run build", "release": "standard-version --no-verify", "docs:diff": "( node --version | grep -vq 'v12' ) || ( npm run docs && git diff --quiet README.md )", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "bundlewatch": "( node --version | grep -vq 'v12' ) || ( npm run pretest:browser && CI_REPO_OWNER=uuidjs CI_REPO_NAME=uuid CI_COMMIT_SHA=$GITHUB_SHA CI_BRANCH=${GITHUB_REF##refs/heads/} bundlewatch --config bundlewatch.config.json )", "eslint:check": "eslint src/ test/ examples/ *.js", "prettier:fix": "prettier --ignore-path .prettierignore --write '**/*.{js,jsx,json,md}'", "test:browser": "BABEL_ENV=commonjs jest --forceExit --verbose test/browser/${BROWSER:-}*", "prettier:check": "prettier --ignore-path .prettierignore --check '**/*.{js,jsx,json,md}'", "pretest:browser": "npm run build && npm-run-all --parallel examples:**", "examples:browser-rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:browser-webpack:build": "cd examples/browser-webpack && npm install && npm run build", "examples:browser-esmodules:build": "cd examples/browser-esmodules && npm install && npm run build"}, "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/uuidjs/uuid.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "RFC4122 (v1, v4, and v5) UUIDs", "directories": {}, "lint-staged": {"*.{js,jsx}": ["eslint --fix"], "*.{js,jsx,json,md}": ["prettier --write"]}, "sideEffects": false, "_nodeVersion": "12.15.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"esm": "3.2.25", "jest": "24.9.0", "husky": "3.0.9", "runmd": "1.3.2", "eslint": "6.8.0", "rollup": "1.30.0", "prettier": "1.19.1", "@babel/cli": "7.8.3", "@babel/core": "7.8.3", "bundlewatch": "0.2.5", "http-server": "0.12.1", "lint-staged": "10.0.1", "npm-run-all": "4.1.5", "babel-eslint": "10.0.3", "@commitlint/cli": "8.3.5", "standard-version": "7.0.1", "@babel/preset-env": "7.8.3", "browserstack-local": "1.4.4", "selenium-webdriver": "3.6.0", "rollup-plugin-terser": "5.2.0", "eslint-config-prettier": "6.9.0", "eslint-plugin-prettier": "3.1.2", "@commitlint/config-conventional": "8.3.4", "babel-plugin-add-module-exports": "1.0.2"}, "standard-version": {"scripts": {"postcommit": "npm run build", "postchangelog": "prettier --write CHANGELOG.md"}}, "_npmOperationalInternal": {"tmp": "tmp/uuid_7.0.0-beta.0_1581938725845_0.7896196061229703", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "uuid", "version": "7.0.0", "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "_id": "uuid@7.0.0", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/uuidjs/uuid#readme", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "1833d4b9ce50b732bfaa271f9cb74e974d303c79", "tarball": "https://registry.npmjs.org/uuid/-/uuid-7.0.0.tgz", "fileCount": 36, "integrity": "sha512-LNUrNsXdI/fUsypJbWM8Jt4DgQdFAZh41p9C7WE9Cn+CULOEkoG2lgQyH68v3wnIy5K3fN4jdSt270K6IFA3MQ==", "signatures": [{"sig": "MEUCIQCpLKr2RXUFiUE2tGO+xCOQWt+BWDfRDk8e69A2rICJfAIgPBT8TE/hb/tbpmVPNUYCjZKdkXvC5rsRb5m9MsqLWsw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72408, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeU85jCRA9TVsSAnZWagAAQQkP/AlIM9UlpC8m86bmakLZ\nMxFzqPOYzXwQwpm2CkjwlYzdUWxqyvPiaobBeVhP3ieMW3aD2f19+3dAwpiw\ne9G9DZrQfGMFyzXFI/wRPBspzSWe6lP+n3J6IJoienzeXkV/3oeL0IBcAvr0\n4U7g7WW+C3deUyik8uO59fB3ITRAai/BX151MNeBmp0WB8S64FMTgUstesaq\ndlsKAcFNnN075DJEzTiIVm72h7bK2A5DTrkriQspVtYaobe0NA6zLovMLjFW\nJP84qZTPAcVfxRzd/5qVpUbHKyR5NLCraBWzDw/K64nd1HS05WAWKbSNebEM\nqPrs3VV6LBVtbXRqmT+7vedFr27g8f046NCMUQb0d0InxIt9/YXp0zaa79il\nBRnYvHaNmYXy4k0u4uxSsfxfLLZ7imtLIQ9nhkqVNlWYp4LjUlZLw1cBHsZx\npZ1HQ8G8TtnXEU/6GMtCZ6hnwCtq+4Bm4s4dgxzpGexOJIpYTK4+0rtmOEgR\ntY0aR5K27N7Lm2Zo88vZUqjbcQmuKEFhX5onVuPZBGg8qTXqOsasyi6T9jJf\n3YovQYwl/TdqRv27AUWS4ZhryG6byurIOKETENv/DC6ybYa1Lvy9t77Qor6a\nTwR3+IJ3S9RP0jtC59gMoUpH078XWBEN+4gkX8sSDEewZXv3Y6u2lR7hUqEy\nQKnT\r\n=SAzF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "module": "dist/esm-browser/index.js", "gitHead": "63e5702d7629303a5193c4dd31825376f240f888", "scripts": {"ci": "npm run lint && npm run test && npm run prettier:check && npm run docs:diff && npm run bundlewatch", "md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v12' ) && ( npm run build && runmd --output=README.md README_js.md )", "lint": "npm run eslint:check && npm run prettier:check", "test": "BABEL_ENV=commonjs node --throw-deprecation node_modules/.bin/jest test/unit/", "build": "./scripts/build.sh", "pretest": "npm run build", "release": "standard-version --no-verify", "docs:diff": "( node --version | grep -vq 'v12' ) || ( npm run docs && git diff --quiet README.md )", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "bundlewatch": "( node --version | grep -vq 'v12' ) || ( npm run pretest:browser && CI_REPO_OWNER=uuidjs CI_REPO_NAME=uuid CI_COMMIT_SHA=$GITHUB_SHA CI_BRANCH=${GITHUB_REF##refs/heads/} bundlewatch --config bundlewatch.config.json )", "eslint:check": "eslint src/ test/ examples/ *.js", "prettier:fix": "prettier --ignore-path .prettierignore --write '**/*.{js,jsx,json,md}'", "test:browser": "BABEL_ENV=commonjs jest --forceExit --verbose test/browser/${BROWSER:-}*", "prettier:check": "prettier --ignore-path .prettierignore --check '**/*.{js,jsx,json,md}'", "pretest:browser": "npm run build && npm-run-all --parallel examples:**", "examples:browser-rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:browser-webpack:build": "cd examples/browser-webpack && npm install && npm run build", "examples:browser-esmodules:build": "cd examples/browser-esmodules && npm install && npm run build"}, "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/uuidjs/uuid.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "RFC4122 (v1, v4, and v5) UUIDs", "directories": {}, "lint-staged": {"*.{js,jsx}": ["eslint --fix"], "*.{js,jsx,json,md}": ["prettier --write"]}, "sideEffects": false, "_nodeVersion": "12.16.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "3.2.25", "jest": "25.1.0", "husky": "3.0.9", "runmd": "1.3.2", "eslint": "6.8.0", "rollup": "1.31.1", "prettier": "1.19.1", "@babel/cli": "7.8.4", "@babel/core": "7.8.4", "bundlewatch": "0.2.5", "http-server": "0.12.1", "lint-staged": "10.0.7", "npm-run-all": "4.1.5", "babel-eslint": "10.0.3", "@commitlint/cli": "8.3.5", "standard-version": "7.1.0", "@babel/preset-env": "7.8.4", "browserstack-local": "1.4.5", "selenium-webdriver": "3.6.0", "rollup-plugin-terser": "5.2.0", "eslint-config-prettier": "6.10.0", "eslint-plugin-prettier": "3.1.2", "@commitlint/config-conventional": "8.3.4", "babel-plugin-add-module-exports": "1.0.2"}, "standard-version": {"scripts": {"postcommit": "npm run build", "postchangelog": "prettier --write CHANGELOG.md"}}, "_npmOperationalInternal": {"tmp": "tmp/uuid_7.0.0_1582550626542_0.483157796354976", "host": "s3://npm-registry-packages"}}, "7.0.1": {"name": "uuid", "version": "7.0.1", "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "_id": "uuid@7.0.1", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/uuidjs/uuid#readme", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "95ed6ff3d8c881cbf85f0f05cc3915ef994818ef", "tarball": "https://registry.npmjs.org/uuid/-/uuid-7.0.1.tgz", "fileCount": 49, "integrity": "sha512-yqjRXZzSJm9Dbl84H2VDHpM3zMjzSJQ+hn6C4zqd5ilW+7P4ZmLEEqwho9LjP+tGuZlF4xrHQXT0h9QZUS/pWA==", "signatures": [{"sig": "MEUCIEt0azVUyp5SIuYfZDPQ69YE32hdEnpvoupa1n+dKMbgAiEA0vGngmyvYdlQqf23euVhMr0OGQEpjZ6y64fICveJNK4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 91125, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeVYL8CRA9TVsSAnZWagAAR8UP/iKsHTGRssBfDvB2I+MD\nvActHY6aC3wf+7nxLIyfA8OVfZCt8Q/bUB+FLKgqPPkp/MKFVkvEE3aidWX+\nD9HnZm3J2/b/+iH7l+3u2J/L+Bb369p7Q6Aje0EJksO6MEA/S/1egvLxMEd/\nYi3Q2/93eZOCt5ogroCUQ9vtKxLSJRwvoHICJN04DCw96OYe+GUTB6VOQFld\n5kx+ctksSaZmXYtmmWyHyHe8z2T2GoAdWZSg4LsB7nHGVcmio8rKqJmbdesU\n0esYjkGHhZszrB4/pgNSDl4PmMIpgTtfY5moE0tvaKlgVsUBQ4G1K1bo4LzW\nAtGBwgqtf2ystzXw9AU1SXS4l3vuhdRypHJCqvEZxnLliQeCaeMtNDoGBLed\n2aZe14zcTnyem5RgOgKax5cWJWj9o/zIl2aiAG8B2pPt+bml0Z/e+dok6MRp\nMqiqqa0SqeIrVicahyWzJ+6LSssaaqhLPc171g9lv7M3ddod4910E1XxfVLU\nxCDWYn/mgoBjs/0UqcE3RIkPdOJnLGbmvT2sr/grDRBVl9aY6lL84S8h8aZx\nLdmWlpRxPjt7GWCSIg8oZgVw4B5p+qp0Th2JYSK0xYFkvj4mOi39H7tN2UCA\nAukUAz4q83b4cyJIOjVP8nR4GXt1ae+MUdNSpuTJns4n/aH/fE20SU8svkxu\nrsp9\r\n=Hfuo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "module": "dist/esm-node/index.js", "browser": {"./dist/md5.js": "./dist/md5-browser.js", "./dist/rng.js": "./dist/rng-browser.js", "./dist/sha1.js": "./dist/sha1-browser.js", "./dist/esm-node/index.js": "./dist/esm-browser/index.js"}, "gitHead": "3f78220564ff2abcb06cbf5ea173513a0e0cba43", "scripts": {"ci": "npm run lint && npm run test && npm run prettier:check && npm run docs:diff && npm run bundlewatch", "md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v12' ) && ( npm run build && runmd --output=README.md README_js.md )", "lint": "npm run eslint:check && npm run prettier:check", "test": "BABEL_ENV=commonjs node --throw-deprecation node_modules/.bin/jest test/unit/", "build": "./scripts/build.sh", "pretest": "npm run build", "release": "standard-version --no-verify", "docs:diff": "( node --version | grep -vq 'v12' ) || ( npm run docs && git diff --quiet README.md )", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "bundlewatch": "( node --version | grep -vq 'v12' ) || ( npm run pretest:browser && CI_REPO_OWNER=uuidjs CI_REPO_NAME=uuid CI_COMMIT_SHA=$GITHUB_SHA CI_BRANCH=${GITHUB_REF##refs/heads/} bundlewatch --config bundlewatch.config.json )", "eslint:check": "eslint src/ test/ examples/ *.js", "prettier:fix": "prettier --ignore-path .prettierignore --write '**/*.{js,jsx,json,md}'", "test:browser": "BABEL_ENV=commonjs jest --forceExit --verbose test/browser/${BROWSER:-}*", "prettier:check": "prettier --ignore-path .prettierignore --check '**/*.{js,jsx,json,md}'", "pretest:browser": "npm run build && npm-run-all --parallel examples:**", "examples:browser-rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:browser-webpack:build": "cd examples/browser-webpack && npm install && npm run build", "examples:browser-esmodules:build": "cd examples/browser-esmodules && npm install && npm run build"}, "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/uuidjs/uuid.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "RFC4122 (v1, v4, and v5) UUIDs", "directories": {}, "lint-staged": {"*.{js,jsx}": ["eslint --fix"], "*.{js,jsx,json,md}": ["prettier --write"]}, "sideEffects": false, "_nodeVersion": "12.16.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "3.2.25", "jest": "25.1.0", "husky": "3.0.9", "runmd": "1.3.2", "eslint": "6.8.0", "rollup": "1.31.1", "prettier": "1.19.1", "@babel/cli": "7.8.4", "@babel/core": "7.8.4", "bundlewatch": "0.2.5", "http-server": "0.12.1", "lint-staged": "10.0.7", "npm-run-all": "4.1.5", "babel-eslint": "10.0.3", "@commitlint/cli": "8.3.5", "standard-version": "7.1.0", "@babel/preset-env": "7.8.4", "browserstack-local": "1.4.5", "selenium-webdriver": "3.6.0", "rollup-plugin-terser": "5.2.0", "eslint-config-prettier": "6.10.0", "eslint-plugin-prettier": "3.1.2", "@rollup/plugin-node-resolve": "7.1.1", "@commitlint/config-conventional": "8.3.4", "babel-plugin-add-module-exports": "1.0.2"}, "standard-version": {"scripts": {"postcommit": "npm run build", "postchangelog": "prettier --write CHANGELOG.md"}}, "_npmOperationalInternal": {"tmp": "tmp/uuid_7.0.1_1582662395609_0.09015471273563103", "host": "s3://npm-registry-packages"}}, "7.0.2-beta.0": {"name": "uuid", "version": "7.0.2-beta.0", "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "_id": "uuid@7.0.2-beta.0", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/uuidjs/uuid#readme", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "8cc5d456c79a9dcb527108a799639cbf2e764bab", "tarball": "https://registry.npmjs.org/uuid/-/uuid-7.0.2-beta.0.tgz", "fileCount": 49, "integrity": "sha512-nWN1O0baX9+xv8DxMl6O5ERk0R/4xAuCkosXBNB68m1PReciNwzBoO9xzf8wX4HrXpfbcu9Mpo5mmgLcsDTZlA==", "signatures": [{"sig": "MEYCIQC4/RXXIVroLLrt7JIwsDWRVU/RV3w6ZhK4uPYzW4OGqAIhAPy/0+kzMucTPT0DgX9MOIqIpa5K9SXBowjXiyg9Gh88", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 91761, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeXW0MCRA9TVsSAnZWagAAvn8QAJHGY0rSQicG6809E5rG\nkOMu13oLNhO27M98xi5uOak2eYG3hsKqDKnngsUDqKj7wbWWyKAEN3kbhOfS\nF97vJaf+DbxQtnbDL5YZSMtYYmOAjpTsAkYP2vnRc2ovWUXRwR0f2O5Q5idX\nHwRLIpIUUsYEwFKq8Re9oTJnLGcQWLWmIdv3lEaujX9fsEInC8hb5aFZGrJJ\nx4HA5hcNcJK6gZt4G6wAqbgqKDvouR73IDOYyFST3G6VwlXD/ZA7Gx6KGqHu\nu+okHWuKybJwK7xJzoa8qs0gdXTmqSc5a6oQGKWFj6C4oa22EMaprDTn27xv\npYf4phKKEKr2UWyhZPVfdW4oeAtinetxM6gV6vLt2f6MQizXekUibzJRzLn+\n9B8bXeEiYOXWK8RZKwPlDl20fOXdYgLjHq/uo7DCh4FOq43nf7q+1eS+r6oo\n21dYXEFvwXYgHtF2JkqfXjN0xfhyt2+JSVEiYH6DJUDC/QRPYVEeitKa6zDK\nyWBNUHP8CidgLUG4Kj3wjQKYqjoMXZNdE0QJpVZZLMj+CjfvqQSPXcLjyc8g\nhGI6B7HTRZVyqt6GyhTVM4L+lwZh79N6flLcWtKYPAv5jSTrGOFAIwWvN2Sh\nVDZJYABAVS0APfqhLV/k1U2h2qZCLWvBcBpoStr09+A77JY67DcUOzxCS2tx\nX9f0\r\n=YJwA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "module": "dist/esm-node/index.js", "browser": {"./dist/md5.js": "./dist/md5-browser.js", "./dist/rng.js": "./dist/rng-browser.js", "./dist/sha1.js": "./dist/sha1-browser.js", "./dist/esm-node/index.js": "./dist/esm-browser/index.js"}, "gitHead": "0da6efbc703a93236087ff55cd9087d2dcdff155", "scripts": {"ci": "npm run lint && npm run test && npm run prettier:check && npm run docs:diff && npm run bundlewatch", "md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v12' ) && ( npm run build && runmd --output=README.md README_js.md )", "lint": "npm run eslint:check && npm run prettier:check", "test": "BABEL_ENV=commonjs node --throw-deprecation node_modules/.bin/jest test/unit/", "build": "./scripts/build.sh", "pretest": "npm run build", "release": "standard-version --no-verify", "docs:diff": "( node --version | grep -vq 'v12' ) || ( npm run docs && git diff --quiet README.md )", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "bundlewatch": "( node --version | grep -vq 'v12' ) || ( npm run pretest:browser && CI_REPO_OWNER=uuidjs CI_REPO_NAME=uuid CI_COMMIT_SHA=$GITHUB_SHA CI_BRANCH=${GITHUB_REF##refs/heads/} bundlewatch --config bundlewatch.config.json )", "eslint:check": "eslint src/ test/ examples/ *.js", "prettier:fix": "prettier --ignore-path .prettierignore --write '**/*.{js,jsx,json,md}'", "test:browser": "BABEL_ENV=commonjs jest --forceExit --verbose test/browser/${BROWSER:-}*", "prettier:check": "prettier --ignore-path .prettierignore --check '**/*.{js,jsx,json,md}'", "pretest:browser": "npm run build && npm-run-all --parallel examples:**", "examples:browser-rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:browser-webpack:build": "cd examples/browser-webpack && npm install && npm run build", "examples:browser-esmodules:build": "cd examples/browser-esmodules && npm install && npm run build"}, "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/uuidjs/uuid.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "RFC4122 (v1, v4, and v5) UUIDs", "directories": {}, "lint-staged": {"*.{js,jsx}": ["eslint --fix"], "*.{js,jsx,json,md}": ["prettier --write"]}, "sideEffects": false, "_nodeVersion": "12.16.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"esm": "3.2.25", "jest": "25.1.0", "husky": "3.0.9", "runmd": "1.3.2", "eslint": "6.8.0", "rollup": "1.31.1", "prettier": "1.19.1", "@babel/cli": "7.8.4", "@babel/core": "7.8.4", "bundlewatch": "0.2.5", "http-server": "0.12.1", "lint-staged": "10.0.7", "npm-run-all": "4.1.5", "babel-eslint": "10.0.3", "@commitlint/cli": "8.3.5", "standard-version": "7.1.0", "@babel/preset-env": "7.8.4", "browserstack-local": "1.4.5", "selenium-webdriver": "3.6.0", "rollup-plugin-terser": "5.2.0", "eslint-config-prettier": "6.10.0", "eslint-plugin-prettier": "3.1.2", "@rollup/plugin-node-resolve": "7.1.1", "@commitlint/config-conventional": "8.3.4", "babel-plugin-add-module-exports": "1.0.2"}, "standard-version": {"scripts": {"postcommit": "npm run build", "postchangelog": "prettier --write CHANGELOG.md"}}, "_npmOperationalInternal": {"tmp": "tmp/uuid_7.0.2-beta.0_1583181068234_0.11125894339297981", "host": "s3://npm-registry-packages"}}, "7.0.2": {"name": "uuid", "version": "7.0.2", "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "_id": "uuid@7.0.2", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/uuidjs/uuid#readme", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "7ff5c203467e91f5e0d85cfcbaaf7d2ebbca9be6", "tarball": "https://registry.npmjs.org/uuid/-/uuid-7.0.2.tgz", "fileCount": 49, "integrity": "sha512-vy9V/+pKG+5ZTYKf+VcphF5Oc6EFiu3W8Nv3P3zIh0EqVI80ZxOzuPfe9EHjkFNvf8+xuTHVeei4Drydlx4zjw==", "signatures": [{"sig": "MEYCIQDQ0Uss9lZaPMJ6XBRR+DcCzUVXIJW+CgUFbjVOtS6+7gIhALhC5uKN5LLq+0/AQmSNN+zeM5KgtJ4PBTMYZpNoYm7t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 92084, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeX6O9CRA9TVsSAnZWagAAjlYQAJ5hKsHQkBAKkf1NzVvv\nQ+/w/d3YJgcfrlrDFe2jqbGqooI74L6ueVPlJs4LIKUCpVvhZIvY4yGHI2hY\nYgzFhbffp2i6E8DGKOvrVvKR6+MFh97vAcOzI2ZUAzOtea++arEJLmxcFUxr\n9VXnY7j1GztqULh0p0XPfAXFkCzm/lm/hDTnBMr0A82qGs0u1PoA5haJl2si\nNA7bFMYQX5nXYyRi/sFfLC1aPiyGDoWs5fGFKRjMSsn7gA+oiI83mpWZ5WVn\nKq28bNNDc+aqe3yG9aT7veXXv3aeUnHmEbP8u6gpswh7QG2h+gNmxvDriovy\n08BxDuR89SPwKIzOb3UF/gk6wPSCpMHZ46xqTX8nVjnEfdvZDMQnzpGCIfGz\n8++uhxIFRbAKg+tvCgClS3fFqxZ27Ai1zNBbnNfH/+XjdH+idOZbK5yglWGr\nIAnYkstMmAsSF+dS1Betk+dnHfgUKJHd2zGU8USulhWblvSW3YIpeXNaIQzB\nzWKP9a47Yp+6B33HcqhRWOSnkhj7xsoL0LEhcrYhC8Zk1KSS5uzqucLpnNfG\nDyVzlxj/BUaNUVVsP3HuayAWtkmsVOZm3O6aKXog7IX4keIw1CCcfDM6bnaD\nzd++5iyGEFfgENa7Fl7LKxAQy2Y4Olcy/aUn3S4ht9CF7GGXvuOc9fNwf2QU\nrjjb\r\n=Xkrr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "module": "dist/esm-node/index.js", "browser": {"./dist/md5.js": "./dist/md5-browser.js", "./dist/rng.js": "./dist/rng-browser.js", "./dist/sha1.js": "./dist/sha1-browser.js", "./dist/esm-node/index.js": "./dist/esm-browser/index.js"}, "gitHead": "4b61be05c840ba4d6fadf89cc8d4e1bbba7b9b1a", "scripts": {"ci": "npm run lint && npm run test && npm run prettier:check && npm run docs:diff && npm run bundlewatch", "md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v12' ) && ( npm run build && runmd --output=README.md README_js.md )", "lint": "npm run eslint:check && npm run prettier:check", "test": "BABEL_ENV=commonjs node --throw-deprecation node_modules/.bin/jest test/unit/", "build": "./scripts/build.sh", "pretest": "npm run build", "release": "standard-version --no-verify", "docs:diff": "( node --version | grep -vq 'v12' ) || ( npm run docs && git diff --quiet README.md )", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "bundlewatch": "( node --version | grep -vq 'v12' ) || ( npm run pretest:browser && CI_REPO_OWNER=uuidjs CI_REPO_NAME=uuid CI_COMMIT_SHA=$GITHUB_SHA CI_BRANCH=${GITHUB_REF##refs/heads/} bundlewatch --config bundlewatch.config.json )", "eslint:check": "eslint src/ test/ examples/ *.js", "prettier:fix": "prettier --ignore-path .prettierignore --write '**/*.{js,jsx,json,md}'", "test:browser": "BABEL_ENV=commonjs jest --forceExit --verbose test/browser/${BROWSER:-}*", "prettier:check": "prettier --ignore-path .prettierignore --check '**/*.{js,jsx,json,md}'", "pretest:browser": "npm run build && npm-run-all --parallel examples:**", "examples:browser-rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:browser-webpack:build": "cd examples/browser-webpack && npm install && npm run build", "examples:browser-esmodules:build": "cd examples/browser-esmodules && npm install && npm run build"}, "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/uuidjs/uuid.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "RFC4122 (v1, v4, and v5) UUIDs", "directories": {}, "lint-staged": {"*.{js,jsx}": ["eslint --fix"], "*.{js,jsx,json,md}": ["prettier --write"]}, "sideEffects": false, "_nodeVersion": "12.16.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "3.2.25", "jest": "25.1.0", "husky": "3.0.9", "runmd": "1.3.2", "eslint": "6.8.0", "rollup": "1.31.1", "prettier": "1.19.1", "@babel/cli": "7.8.4", "@babel/core": "7.8.4", "bundlewatch": "0.2.5", "http-server": "0.12.1", "lint-staged": "10.0.7", "npm-run-all": "4.1.5", "babel-eslint": "10.0.3", "@commitlint/cli": "8.3.5", "standard-version": "7.1.0", "@babel/preset-env": "7.8.4", "browserstack-local": "1.4.5", "selenium-webdriver": "3.6.0", "rollup-plugin-terser": "5.2.0", "eslint-config-prettier": "6.10.0", "eslint-plugin-prettier": "3.1.2", "@rollup/plugin-node-resolve": "7.1.1", "@commitlint/config-conventional": "8.3.4", "babel-plugin-add-module-exports": "1.0.2"}, "standard-version": {"scripts": {"postcommit": "npm run build", "postchangelog": "prettier --write CHANGELOG.md"}}, "_npmOperationalInternal": {"tmp": "tmp/uuid_7.0.2_1583326140504_0.43396107706675835", "host": "s3://npm-registry-packages"}}, "7.0.3": {"name": "uuid", "version": "7.0.3", "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "_id": "uuid@7.0.3", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/uuidjs/uuid#readme", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "c5c9f2c8cf25dc0a372c4df1441c41f5bd0c680b", "tarball": "https://registry.npmjs.org/uuid/-/uuid-7.0.3.tgz", "fileCount": 50, "integrity": "sha512-DPSke0pXhTZgoF/d+WSt2QaKMCFSfx7QegxEWT+JOuHF5aWrKEn0G+ztjuJg/gG8/ItK+rbPCD/yNv8yyih6Cg==", "signatures": [{"sig": "MEQCIB8rpyV5wxjOGrmNtRkM/yt650w956eEas/2kBWtqdc0AiAMzo7dnEqhT0kFl40Rmitw4twBZLoWnCuUMhaagi2lyg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 92933, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeg5zlCRA9TVsSAnZWagAA67UP/0v7FNDUajl++p6xis/E\nyX/iTGa9FhCdz0usy/6trk20kx8RqF4Li0NrQ50r5GOCfFqFYgxbaweJIJM8\nZytCMDxfM4v/gVDwms92hV7BIVWcq+kPcReW8aDliUAWPIx33HnpCkPya7lG\nYbDv6vto4JJNggasmNF9dt5BTu4oof4q2t6VDXC1tMVHQ9SbsQo+0rpiTPDZ\nq+0py4XaNMKx0v1WH81pghxWr+z1AKn2pdx+24E4J4WUn/cA1Z0D7nN+HaTb\nckPlMx4yFJZDxT+EqdtQOHp7kxGht9Xk4xJD5bpUDyLcuurhQeNLLYiI/ynO\n4Txd8Tdby5INbAp9NCHMvzGmLQiZrfOLz54s2l6At3oQfDYShljIGouSgWtI\nktp7A1e5woBLk5KHTYssvDNZmFRBiL743rFGzFF+ZZ7eWT66aGqzDu52wpKF\nRcKidJp7jOgRHA3LruF9byaab6ApmO5tnNIF3qxG1DaAYIOCpHawjgcf+xAx\nDcF0cZy1jUnnOlziH3AwPfs99LlERHJViLXWReX/9jqzN9tp1R7BJQ6nIxjM\ngXp5IEjIe8r68jTbCyDMiwbfsW3//5qrSCvsOfAdQIcZBSeCWsbSblspABtS\nbM7ysSh6srvHNTj2q/zQm2zHxuGyl5beJrhP5n3jUM9IWOU1UYlhfa6nNV7T\nb7H7\r\n=76Y4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "module": "dist/esm-node/index.js", "browser": {"./dist/md5.js": "./dist/md5-browser.js", "./dist/rng.js": "./dist/rng-browser.js", "./dist/sha1.js": "./dist/sha1-browser.js", "./dist/esm-node/index.js": "./dist/esm-browser/index.js"}, "gitHead": "4fcd881246f102239fa386f958ec0e5f83b53bbe", "scripts": {"ci": "npm run lint && npm run test && npm run prettier:check && npm run docs:diff && npm run bundlewatch", "md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v12' ) && ( npm run build && runmd --output=README.md README_js.md )", "lint": "npm run eslint:check && npm run prettier:check", "test": "BABEL_ENV=commonjs node --throw-deprecation node_modules/.bin/jest test/unit/", "build": "./scripts/build.sh", "pretest": "npm run build", "release": "standard-version --no-verify", "docs:diff": "( node --version | grep -vq 'v12' ) || ( npm run docs && git diff --quiet README.md )", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "bundlewatch": "( node --version | grep -vq 'v12' ) || ( npm run pretest:browser && bundlewatch --config bundlewatch.config.json )", "eslint:check": "eslint src/ test/ examples/ *.js", "prettier:fix": "prettier --ignore-path .prettierignore --write '**/*.{js,jsx,json,md}'", "test:browser": "wdio run ./wdio.conf.js", "prettier:check": "prettier --ignore-path .prettierignore --check '**/*.{js,jsx,json,md}'", "pretest:browser": "npm run build && npm-run-all --parallel examples:**", "examples:browser-rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:browser-webpack:build": "cd examples/browser-webpack && npm install && npm run build", "examples:browser-esmodules:build": "cd examples/browser-esmodules && npm install && npm run build"}, "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/uuidjs/uuid.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "RFC4122 (v1, v4, and v5) UUIDs", "directories": {}, "lint-staged": {"*.{js,jsx}": ["eslint --fix"], "*.{js,jsx,json,md}": ["prettier --write"]}, "sideEffects": false, "_nodeVersion": "12.16.1", "_hasShrinkwrap": false, "devDependencies": {"jest": "25.1.0", "husky": "3.0.9", "runmd": "1.3.2", "eslint": "6.8.0", "rollup": "1.32.0", "prettier": "1.19.1", "@wdio/cli": "5.18.7", "@babel/cli": "7.8.4", "@wdio/sync": "5.18.7", "@babel/core": "7.8.7", "bundlewatch": "0.2.6", "lint-staged": "10.0.8", "npm-run-all": "4.1.5", "babel-eslint": "10.1.0", "@commitlint/cli": "8.3.5", "standard-version": "7.1.0", "@babel/preset-env": "7.8.7", "@wdio/local-runner": "5.18.7", "@wdio/spec-reporter": "5.18.7", "rollup-plugin-terser": "5.2.0", "eslint-config-prettier": "6.10.0", "eslint-plugin-prettier": "3.1.2", "@wdio/jasmine-framework": "5.18.6", "@wdio/browserstack-service": "5.18.7", "@rollup/plugin-node-resolve": "7.1.1", "@wdio/static-server-service": "5.16.10", "@commitlint/config-conventional": "8.3.4", "babel-plugin-add-module-exports": "1.0.2"}, "standard-version": {"scripts": {"postcommit": "npm run build", "postchangelog": "prettier --write CHANGELOG.md"}}, "_npmOperationalInternal": {"tmp": "tmp/uuid_7.0.3_1585683684668_0.7417149445973905", "host": "s3://npm-registry-packages"}}, "8.0.0-beta.0": {"name": "uuid", "version": "8.0.0-beta.0", "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "_id": "uuid@8.0.0-beta.0", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/uuidjs/uuid#readme", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "93e7d8e269022a2fa2027d6a77d4129de877fadb", "tarball": "https://registry.npmjs.org/uuid/-/uuid-8.0.0-beta.0.tgz", "fileCount": 46, "integrity": "sha512-Ql2iMiWxJYtI3biUynCFc1J1XS6rOWhv8zN60bWh0hHwJsYZQ4jM0Z2614qS6cyPRRDOu6NVz+cLmCLt6G8eMw==", "signatures": [{"sig": "MEQCICuUBoR999ZZB0a2nYVCVrXTksDpYWGy5Gxd5wKbynR6AiBwMWaNfn9httMgXgP5drSCss27fWXzTtBnF3eyxHqq2Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 91304, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqeHhCRA9TVsSAnZWagAAnpkP/0679ao/BXaHII9VD9bV\noDN88fiLKeGq75k2kv10eszV2vmnkkkQI9k3609kcDSdZhamDEg5080eJpwO\nEhTBNyhYefqRiYj6Z69iw3VI3mfdRXaOtl1sFzECZVDQLX5Gd8xLhbOVlzwc\nWZyKrn58plFTuN6o6rhlZb7uWUZ11nTqW7et8Dp2b8qLXjDc6SopAQZt2TpM\neLfpe+eJoFrGHaKSUtJobXuh9gsgGttR3mk7Jnx4Py/dbgeXM5FIQlou8DzI\n+8xuud/e3IFDc/E85eLNUtJPmvDtMOxKAsr1X+/s1CxDVT2GHaw+myNFH6Gk\nk6JUmiWPwwVXt0GOQI9kgDfYEOdCtIQSt7CbjJjcjF7Ta8tlrDnqJKQCCgFl\nhXu6paimY4qgFW61/zbE3WleYMb9ktCAlrgWJtQa6Yj4LaVh+lTaPxKcgQCH\nXJAS8J4ZvR5+ixoKenX3qXogw56Z3kM3ucrXiY3iECxqOkAl1/Bl1FyJ6MN/\n7ZkcrfhZ1QFXKu2he6GmdoHUQ8N6g+33gnTYwlj2f6JsY4E1DRAC0ja08HIl\nCY9x8s6LyH+IfrJvRn1UarefQMPaYRXHadhbzjA8csPf2THV5L5zOjOQ21My\nQzu806y7mDIV2X8u37q1v6rrS6G8xvPnUpuNAUrwLL3dI12XxM3PA71xQXkZ\nw8fo\r\n=qoi5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "module": "./dist/esm-node/index.js", "browser": {"./dist/md5.js": "./dist/md5-browser.js", "./dist/rng.js": "./dist/rng-browser.js", "./dist/sha1.js": "./dist/sha1-browser.js", "./dist/esm-node/index.js": "./dist/esm-browser/index.js"}, "exports": {"import": "./wrapper.mjs", "require": "./dist/index.js"}, "gitHead": "99d65afdc42b98377842756c0743bc38dcb03570", "scripts": {"md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v12' ) && ( npm run build && runmd --output=README.md README_js.md )", "lint": "npm run eslint:check && npm run prettier:check", "test": "BABEL_ENV=commonjs node --throw-deprecation node_modules/.bin/jest test/unit/", "build": "./scripts/build.sh", "prepack": "npm run build", "pretest": "[ -n $CI ] || npm run build", "release": "standard-version --no-verify", "docs:diff": "npm run docs && git diff --quiet README.md", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "eslint:check": "eslint src/ test/ examples/ *.js", "pretest:node": "npm run build", "prettier:fix": "prettier --ignore-path .prettierignore --write '**/*.{js,jsx,json,md}'", "test:browser": "wdio run ./wdio.conf.js", "prettier:check": "prettier --ignore-path .prettierignore --check '**/*.{js,jsx,json,md}'", "pretest:browser": "npm run build && npm-run-all --parallel examples:browser:**", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build"}, "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/uuidjs/uuid.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "RFC4122 (v1, v4, and v5) UUIDs", "directories": {}, "lint-staged": {"*.{js,jsx}": ["eslint --fix"], "*.{js,jsx,json,md}": ["prettier --write"]}, "sideEffects": false, "_nodeVersion": "12.16.2", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "25.3.0", "husky": "4.2.5", "runmd": "1.3.2", "eslint": "6.8.0", "rollup": "2.6.1", "prettier": "2.0.4", "@wdio/cli": "6.0.15", "@babel/cli": "7.8.4", "@wdio/sync": "6.0.15", "@babel/core": "7.9.0", "bundlewatch": "0.2.6", "lint-staged": "10.1.3", "npm-run-all": "4.1.5", "babel-eslint": "10.1.0", "@commitlint/cli": "8.3.5", "standard-version": "7.1.0", "@babel/preset-env": "7.9.5", "@wdio/local-runner": "6.0.15", "@wdio/spec-reporter": "6.0.14", "rollup-plugin-terser": "5.3.0", "eslint-config-prettier": "6.10.1", "eslint-plugin-prettier": "3.1.3", "@wdio/jasmine-framework": "6.0.15", "@wdio/browserstack-service": "6.0.12", "@rollup/plugin-node-resolve": "7.1.3", "@wdio/static-server-service": "6.0.13", "@commitlint/config-conventional": "8.3.4"}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "_npmOperationalInternal": {"tmp": "tmp/uuid_8.0.0-beta.0_1588191712623_0.4997152339873798", "host": "s3://npm-registry-packages"}}, "8.0.0": {"name": "uuid", "version": "8.0.0", "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "_id": "uuid@8.0.0", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/uuidjs/uuid#readme", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "bc6ccf91b5ff0ac07bbcdbf1c7c4e150db4dbb6c", "tarball": "https://registry.npmjs.org/uuid/-/uuid-8.0.0.tgz", "fileCount": 46, "integrity": "sha512-jOXGuXZAWdsTH7eZLtyXMqUb9EcWMGZNbL9YcGBJl4MH4nrxHmZJhEHvyLFrkxo+28uLb/NYRcStH48fnD0Vzw==", "signatures": [{"sig": "MEUCIC9KbaApQziNdTgtI4QGAv44v4cjpjvnqvPxBQqnSduTAiEAoOLPPlKD4rb1IvF2Niza2PAJl6wiiwZY75Dkll1mhLY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 92955, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqeazCRA9TVsSAnZWagAAXeAP/Rtdc7+Dt5TYs8hZ4NHM\nM/RTUFQsLzjfu6Z5Y8oFVNtRYKaAA0VrWPO6YSYs1RTF1JLRujkGrVw/rn6g\nVK7XwfJna4YS3iOLcg8r8UtNF+dSNcWNcxDF3s9c74WdU6Y4uNzx36prO738\n3XOUluCw2G82AzlY+/D7FZcMhjnDa42ZIU78NQ7NSeUBUQyLfVa+acSAALSi\nmX79a77iF/m+AVfLasb5WjYHM87IFi+HEC48ZYJN1p8rE2fRPUReZtjh7ppZ\nwCLEyyIDTagS9/1bjrP/MCzvGKoBTHl8hELRKMWnZ/aL0QFuHYd7XjFNnILN\nnoTn/B/ZRheZdywH9dh2XlyyoGEOH6UnfsBT/QWCyYrnLTRCYkBLeVUioOZr\ncvDK5Dbb1flkkbUZTfzR9bXdLhsq25c76f1DDRbz1oW3HrhrPI9BXanF3ikR\n6nfKz4vJUgU71bQevXIXjG3QTWlxWODWsb7RPfDK2XpndpBwwn1ztdMBbWBE\nhxcpzCuwfz4PU6xB930GjEw/eN3QbUL/2Ocq22TFupl5hyebg77Gm+nhENNm\nA0rpZ+6XYicFUflDFIiOyPZzu4cKup5esxfC+tJNCYaAUF0jQGmg1BdnjNqQ\n1Ifm08U/3yvft8JG6RkEWFoXqQDk6Qw+AuLfOO3YU/6Vc4Zjyp9hATZ8WvWF\nKjwJ\r\n=Hsnx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "module": "./dist/esm-node/index.js", "browser": {"./dist/md5.js": "./dist/md5-browser.js", "./dist/rng.js": "./dist/rng-browser.js", "./dist/sha1.js": "./dist/sha1-browser.js", "./dist/esm-node/index.js": "./dist/esm-browser/index.js"}, "exports": {"import": "./wrapper.mjs", "require": "./dist/index.js"}, "gitHead": "a7d3d517382490eda6dd21158e5413261c45ccdd", "scripts": {"md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v12' ) && ( npm run build && runmd --output=README.md README_js.md )", "lint": "npm run eslint:check && npm run prettier:check", "test": "BABEL_ENV=commonjs node --throw-deprecation node_modules/.bin/jest test/unit/", "build": "./scripts/build.sh", "prepack": "npm run build", "pretest": "[ -n $CI ] || npm run build", "release": "standard-version --no-verify", "docs:diff": "npm run docs && git diff --quiet README.md", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "eslint:check": "eslint src/ test/ examples/ *.js", "pretest:node": "npm run build", "prettier:fix": "prettier --ignore-path .prettierignore --write '**/*.{js,jsx,json,md}'", "test:browser": "wdio run ./wdio.conf.js", "prettier:check": "prettier --ignore-path .prettierignore --check '**/*.{js,jsx,json,md}'", "pretest:browser": "npm run build && npm-run-all --parallel examples:browser:**", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build"}, "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/uuidjs/uuid.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "RFC4122 (v1, v4, and v5) UUIDs", "directories": {}, "lint-staged": {"*.{js,jsx}": ["eslint --fix"], "*.{js,jsx,json,md}": ["prettier --write"]}, "sideEffects": false, "_nodeVersion": "12.16.2", "_hasShrinkwrap": false, "devDependencies": {"jest": "25.3.0", "husky": "4.2.5", "runmd": "1.3.2", "eslint": "6.8.0", "rollup": "2.6.1", "prettier": "2.0.4", "@wdio/cli": "6.0.15", "@babel/cli": "7.8.4", "@wdio/sync": "6.0.15", "@babel/core": "7.9.0", "bundlewatch": "0.2.6", "lint-staged": "10.1.3", "npm-run-all": "4.1.5", "babel-eslint": "10.1.0", "@commitlint/cli": "8.3.5", "standard-version": "7.1.0", "@babel/preset-env": "7.9.5", "@wdio/local-runner": "6.0.15", "@wdio/spec-reporter": "6.0.14", "rollup-plugin-terser": "5.3.0", "eslint-config-prettier": "6.10.1", "eslint-plugin-prettier": "3.1.3", "@wdio/jasmine-framework": "6.0.15", "@wdio/browserstack-service": "6.0.12", "@rollup/plugin-node-resolve": "7.1.3", "@wdio/static-server-service": "6.0.13", "@commitlint/config-conventional": "8.3.4"}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "_npmOperationalInternal": {"tmp": "tmp/uuid_8.0.0_1588192946684_0.9321919222541137", "host": "s3://npm-registry-packages"}}, "8.1.0": {"name": "uuid", "version": "8.1.0", "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "_id": "uuid@8.1.0", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/uuidjs/uuid#readme", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "6f1536eb43249f473abc6bd58ff983da1ca30d8d", "tarball": "https://registry.npmjs.org/uuid/-/uuid-8.1.0.tgz", "fileCount": 46, "integrity": "sha512-CI18flHDznR0lq54xBycOVmphdCYnQLKn8abKn7PXUiKUGdEd+/l9LWNJmugXel4hXq7S+RMNl34ecyC9TntWg==", "signatures": [{"sig": "MEYCIQDC/U2XCtN00jXHeDxaH/rwmhAVkEZlb7zWwopLdOX17QIhAKjCYvj14h55VEUc239fuJb3m8qbkAZMAwgP9hCieDiC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 95154, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJexXx2CRA9TVsSAnZWagAAabsP/RriwLU9HvZ0+fc/giqA\nSM/YFCU3fUv7SM7fSOnWBvkTIy3NgsqmCL0v5VsnffVZ5CZtCeF8gFQ9qWFx\ngwiZCautPzGGedJ8sP1yFNkR8QPHofjEk/e+j4CV5WY+i2FU0n3JvBehMcfT\nhTpCWSD42PG4x+HZ161Mtl9DxzXxXgGS+xw+X/kOa9m2cB8DjhPD2G+8C7ia\nmGNut2ul0py+lzsBQnWcA/fBlRZ7mR0aF7I7jGFcCt7721boIPVhyqQ/ihHq\neZI7UKwG3ofh8kHvO0wBZ9Mt/XplXFlzsBWY+gry4a2oJc+g/4AEmyVB6dQ3\neWvhCmueXSR0J4SPl2Husu9IAOofb0vwLj5VM9SUhbQAY95TD8947qHB6fFc\nBBh7OSrPjvfmYbE3Q03v8jvq1yahlBDqXD/ZVuvCVymMsFAg3mC0tai7kNbk\nbPlw+IiBt0S7xW5rmOtaLx4j4hw9YD+BtOJlgN0djbZT6qLNeBvqmYFTWE/v\nWuqUAfpEZFmnNcm5RuxxKh30WMq4Etspuag0PZ0HlIMIEZ+XOzwMD7yWM5dZ\n2pueDC5Bbq2yr0YNdTAff9s1YKdGt33ot8ugTzXdmlJu6n5e5WLr2EKb+3iD\nnLcwRHkbab2rrNPpHLfdQxsFW277oCbOxhfhMO2piwJ+wx30jD9anaYumqI5\nhbEG\r\n=3jU2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "module": "./dist/esm-node/index.js", "browser": {"./dist/md5.js": "./dist/md5-browser.js", "./dist/rng.js": "./dist/rng-browser.js", "./dist/sha1.js": "./dist/sha1-browser.js", "./dist/esm-node/index.js": "./dist/esm-browser/index.js"}, "exports": {".": {"import": "./wrapper.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json"}, "gitHead": "4124ec75f032da7b405b870b68219cda8495297f", "scripts": {"md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v12' ) && ( npm run build && runmd --output=README.md README_js.md )", "lint": "npm run eslint:check && npm run prettier:check", "test": "BABEL_ENV=commonjs node --throw-deprecation node_modules/.bin/jest test/unit/", "build": "./scripts/build.sh", "prepack": "npm run build", "pretest": "[ -n $CI ] || npm run build", "release": "standard-version --no-verify", "docs:diff": "npm run docs && git diff --quiet README.md", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "eslint:check": "eslint src/ test/ examples/ *.js", "pretest:node": "npm run build", "prettier:fix": "prettier --ignore-path .prettierignore --write '**/*.{js,jsx,json,md}'", "test:browser": "wdio run ./wdio.conf.js", "prettier:check": "prettier --ignore-path .prettierignore --check '**/*.{js,jsx,json,md}'", "test:benchmark": "cd examples/benchmark && npm install && npm test", "pretest:browser": "npm run build && npm-run-all --parallel examples:browser:**", "pretest:benchmark": "npm run build", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build"}, "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/uuidjs/uuid.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "RFC4122 (v1, v4, and v5) UUIDs", "directories": {}, "lint-staged": {"*.{js,jsx}": ["eslint --fix"], "*.{js,jsx,json,md}": ["prettier --write"]}, "sideEffects": false, "_nodeVersion": "12.16.3", "_hasShrinkwrap": false, "devDependencies": {"jest": "25.3.0", "husky": "4.2.5", "runmd": "1.3.2", "eslint": "6.8.0", "rollup": "2.6.1", "prettier": "2.0.4", "@wdio/cli": "6.0.15", "@babel/cli": "7.8.4", "@wdio/sync": "6.0.15", "@babel/core": "7.9.0", "bundlewatch": "0.2.6", "lint-staged": "10.1.3", "npm-run-all": "4.1.5", "babel-eslint": "10.1.0", "@commitlint/cli": "8.3.5", "standard-version": "7.1.0", "@babel/preset-env": "7.9.5", "@wdio/local-runner": "6.0.15", "eslint-plugin-node": "11.1.0", "@wdio/spec-reporter": "6.0.14", "eslint-plugin-import": "2.20.2", "rollup-plugin-terser": "5.3.0", "eslint-plugin-promise": "4.2.1", "eslint-config-prettier": "6.10.1", "eslint-config-standard": "14.1.1", "eslint-plugin-prettier": "3.1.3", "eslint-plugin-standard": "4.0.1", "@wdio/jasmine-framework": "6.0.15", "@wdio/browserstack-service": "6.0.12", "@rollup/plugin-node-resolve": "7.1.3", "@wdio/static-server-service": "6.0.13", "@commitlint/config-conventional": "8.3.4"}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "_npmOperationalInternal": {"tmp": "tmp/uuid_8.1.0_1590000758059_0.9915391136867249", "host": "s3://npm-registry-packages"}}, "8.2.0-beta.0": {"name": "uuid", "version": "8.2.0-beta.0", "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "_id": "uuid@8.2.0-beta.0", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/uuidjs/uuid#readme", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "bbab71018e75e556381986bbaaf849648d0665c5", "tarball": "https://registry.npmjs.org/uuid/-/uuid-8.2.0-beta.0.tgz", "fileCount": 46, "integrity": "sha512-hwW39cDgHKtopHIRUBDMrGIpPTWOYEid17+nr9uQcuijDGMLZ9NgBZOFOfgn/UISN+3NVbXpvytwDR8WlU7RrQ==", "signatures": [{"sig": "MEUCIQC5owbHi9Ang+ySmVMChgsxBvp1/kMPUNgXmF/bvlBq2AIgPD/AI55R9r8vSc/K09Vr6FO9OAu0UBgV78dZ2GF9Z5U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 95945, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8bGCCRA9TVsSAnZWagAAdrQP/22jrv7ITic0g4+xXc1W\n0DFiX/wf2FRbebPCrSwgXDX0DprjToyNMOOPIvOSSv8aveUYxOTLvBUEWIDA\nwWegdJT3d+cRHgWW6ipWO8lHFEMWmil0OJXfbL5QbEQDilcfConc3JIVybh+\ngActhVsquZhD1kC1W/m3hg48jT1ku3ferfhbNVLheYjyKdzHrw4Zp+cpU9AV\nlFqKWebIj6j7+BgWVP0HDoxV9BuRE4hPtk8Gy2I6Ns3jQHHMKydjCXbk9/UE\nKYh/R5wkAimrTJiOFP7KD6YkiRnrI1TLGpOoEwfgZdh4D53JFv6dzh3Aoa2M\nSsbd+a5vY7PxtfcPbm2ljDeTfKHld/srCWZVQHoeRnq5iGbXcrNz0TPjdKE8\ndUJhGM5QR4P9xB8o/n4Rsx58xnnLcWnVjOE+cPoGRiYTAsM6/i8QIeCSe3t3\nKr+qOyk+bMAGxrnriCo4O66C4fZiFcjQftYVPc2LgzvOGWvvCSGUgfuY5H4B\nqfOBMXyfQYEMSh4Io6sYDVGMdvJcuMsXHkzz08+hqyrWWgFoy0G8tFa4JqQG\nsDttZp20egk/VsszFnbUFFZjN5mK9YzoYq4xtv+VV9jPvawSQO6UZxw4WZ6h\nLW52Su/iDTaXV3EXvzKVtP2kLIDBS+fgVtIccKmGiIDtqC63PfCgZEqOt88T\nCjMB\r\n=Qdr4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "module": "./dist/esm-node/index.js", "browser": {"./dist/md5.js": "./dist/md5-browser.js", "./dist/rng.js": "./dist/rng-browser.js", "./dist/sha1.js": "./dist/sha1-browser.js", "./dist/esm-node/index.js": "./dist/esm-browser/index.js"}, "exports": {".": {"node": {"import": "./wrapper.mjs", "module": "./dist/esm-node/index.js", "require": "./dist/index.js"}, "default": "./dist/esm-browser/index.js"}, "./package.json": "./package.json"}, "gitHead": "e5075c871ad571b7e08a300b855e5a308b021aef", "scripts": {"md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v12' ) && ( npm run build && runmd --output=README.md README_js.md )", "lint": "npm run eslint:check && npm run prettier:check", "test": "BABEL_ENV=commonjs node --throw-deprecation node_modules/.bin/jest test/unit/", "build": "./scripts/build.sh", "prepack": "npm run build", "pretest": "[ -n $CI ] || npm run build", "release": "standard-version --no-verify", "docs:diff": "npm run docs && git diff --quiet README.md", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "eslint:check": "eslint src/ test/ examples/ *.js", "pretest:node": "npm run build", "prettier:fix": "prettier --ignore-path .prettierignore --write '**/*.{js,jsx,json,md}'", "test:browser": "wdio run ./wdio.conf.js", "prettier:check": "prettier --ignore-path .prettierignore --check '**/*.{js,jsx,json,md}'", "test:benchmark": "cd examples/benchmark && npm install && npm test", "pretest:browser": "npm run build && npm-run-all --parallel examples:browser:**", "pretest:benchmark": "npm run build", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build"}, "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/uuidjs/uuid.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "RFC4122 (v1, v4, and v5) UUIDs", "directories": {}, "lint-staged": {"*.{js,jsx}": ["eslint --fix"], "*.{js,jsx,json,md}": ["prettier --write"]}, "sideEffects": false, "_nodeVersion": "12.18.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "25.5.4", "husky": "4.2.5", "runmd": "1.3.2", "eslint": "7.3.0", "rollup": "2.18.0", "prettier": "2.0.5", "@wdio/cli": "6.1.20", "@babel/cli": "7.10.3", "@wdio/sync": "6.1.14", "@babel/core": "7.10.3", "bundlewatch": "0.2.7", "lint-staged": "10.2.11", "npm-run-all": "4.1.5", "babel-eslint": "10.1.0", "@commitlint/cli": "9.0.1", "standard-version": "8.0.0", "@babel/preset-env": "7.10.3", "@wdio/local-runner": "6.1.20", "eslint-plugin-node": "11.1.0", "@wdio/spec-reporter": "6.1.14", "eslint-plugin-import": "2.21.2", "rollup-plugin-terser": "6.1.0", "eslint-plugin-promise": "4.2.1", "eslint-config-prettier": "6.11.0", "eslint-config-standard": "14.1.1", "eslint-plugin-prettier": "3.1.4", "eslint-plugin-standard": "4.0.1", "@wdio/jasmine-framework": "6.1.17", "@wdio/browserstack-service": "6.1.15", "@rollup/plugin-node-resolve": "8.0.1", "@wdio/static-server-service": "6.1.14", "@commitlint/config-conventional": "9.0.1"}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "_npmOperationalInternal": {"tmp": "tmp/uuid_8.2.0-beta.0_1592897922131_0.21824069661026968", "host": "s3://npm-registry-packages"}}, "8.2.0": {"name": "uuid", "version": "8.2.0", "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "_id": "uuid@8.2.0", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/uuidjs/uuid#readme", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "cb10dd6b118e2dada7d0cd9730ba7417c93d920e", "tarball": "https://registry.npmjs.org/uuid/-/uuid-8.2.0.tgz", "fileCount": 46, "integrity": "sha512-CYpGiFTUrmI6OBMkAdjSDM0k5h8SkkiTP4WAjQgDgNB1S3Ou9VBEvr6q0Kv2H1mMk7IWfxYGpMH5sd5AvcIV2Q==", "signatures": [{"sig": "MEYCIQCPd2R0iAx0ALTVzGNyY6T8KZ2gc9CKSeFB2EEGug6xNwIhAJXelXmuyKvAas8x78nL/D08D5oEYDSAZnP1rYo4YzeM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97285, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8mxuCRA9TVsSAnZWagAARZsQAJz+kpmfR+I/GNkijZ0M\nmZWvdB5CuNKL/w0w4Lz7KPL43fpPSpWzmMs4uS49fXddbFPNUjO9tAYtWjD+\nuhvvWLjPcg8LNBnlWZJGLYIqjqexvERyrm+vPntComY60qwAxx2v+ZCIAeYj\nBKVXw2zUX0y1ICQWIvVpfUXXT5Zgl40CFEOSaBhewbWQuckED6g0rmlp4UTi\npNHIcrQxh/zzQjiPdQEMOf8jxMa3nbPwW62mAeIjgNiQCsqTi+9JwYDmJWaw\n2zKQu77TReMdv4IsULhgTiTXkH5LWkt+kethQhzZt5hMamRIrnAp1D6MyWoJ\nWHZYja+V54wN7lvAv2LXRfsLOEWK/7/sj2VenobnelbmhmOFKn3PrQPC9bVM\nFz9zQYuc6FpY+xHrM4kmWKuNsHcO38xgB7trsT22vWRJa930H8mjboXm2zNB\nq85115NCbZNrX9Q3vWtwPAkEXxtL3XP4h0AQcq37u2Ok7f/a7dt5+BHdOqd0\n/vazeDTgVifwKLVUCshvsZlCVD7Yc8hw16m43I4g9HlinPFXACTdLON5t0zA\nfus/e3SE2KQrw4AnzuFzF3hM2uiTWGp8agfJ6RqF2DFAFKrswA9oz7QJjT6k\n/pQhrcu9tCW0bnKYD3nT9258szQpiILve+VEUzwrlU+VR3g9nH5ef9SOOQkC\nn1aP\r\n=mMth\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "module": "./dist/esm-node/index.js", "browser": {"./dist/md5.js": "./dist/md5-browser.js", "./dist/rng.js": "./dist/rng-browser.js", "./dist/sha1.js": "./dist/sha1-browser.js", "./dist/esm-node/index.js": "./dist/esm-browser/index.js"}, "exports": {".": {"node": {"import": "./wrapper.mjs", "module": "./dist/esm-node/index.js", "require": "./dist/index.js"}, "default": "./dist/esm-browser/index.js"}, "./package.json": "./package.json"}, "gitHead": "b51f172b22580dabd382861c1a6316173244f51b", "scripts": {"md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v12' ) && ( npm run build && runmd --output=README.md README_js.md )", "lint": "npm run eslint:check && npm run prettier:check", "test": "BABEL_ENV=commonjs node --throw-deprecation node_modules/.bin/jest test/unit/", "build": "./scripts/build.sh", "prepack": "npm run build", "pretest": "[ -n $CI ] || npm run build", "release": "standard-version --no-verify", "docs:diff": "npm run docs && git diff --quiet README.md", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "eslint:check": "eslint src/ test/ examples/ *.js", "pretest:node": "npm run build", "prettier:fix": "prettier --ignore-path .prettierignore --write '**/*.{js,jsx,json,md}'", "test:browser": "wdio run ./wdio.conf.js", "prettier:check": "prettier --ignore-path .prettierignore --check '**/*.{js,jsx,json,md}'", "test:benchmark": "cd examples/benchmark && npm install && npm test", "pretest:browser": "npm run build && npm-run-all --parallel examples:browser:**", "pretest:benchmark": "npm run build", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build"}, "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/uuidjs/uuid.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "RFC4122 (v1, v4, and v5) UUIDs", "directories": {}, "lint-staged": {"*.{js,jsx}": ["eslint --fix"], "*.{js,jsx,json,md}": ["prettier --write"]}, "sideEffects": false, "_nodeVersion": "12.18.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "25.5.4", "husky": "4.2.5", "runmd": "1.3.2", "eslint": "7.3.0", "rollup": "2.18.0", "prettier": "2.0.5", "@wdio/cli": "6.1.20", "@babel/cli": "7.10.3", "@wdio/sync": "6.1.14", "@babel/core": "7.10.3", "bundlewatch": "0.2.7", "lint-staged": "10.2.11", "npm-run-all": "4.1.5", "babel-eslint": "10.1.0", "@commitlint/cli": "9.0.1", "standard-version": "8.0.0", "@babel/preset-env": "7.10.3", "@wdio/local-runner": "6.1.20", "eslint-plugin-node": "11.1.0", "@wdio/spec-reporter": "6.1.14", "eslint-plugin-import": "2.21.2", "rollup-plugin-terser": "6.1.0", "eslint-plugin-promise": "4.2.1", "eslint-config-prettier": "6.11.0", "eslint-config-standard": "14.1.1", "eslint-plugin-prettier": "3.1.4", "eslint-plugin-standard": "4.0.1", "@wdio/jasmine-framework": "6.1.17", "@wdio/browserstack-service": "6.1.15", "@rollup/plugin-node-resolve": "8.0.1", "@wdio/static-server-service": "6.1.14", "@commitlint/config-conventional": "9.0.1"}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "_npmOperationalInternal": {"tmp": "tmp/uuid_8.2.0_1592945774107_0.25456791127041134", "host": "s3://npm-registry-packages"}}, "8.3.0-beta.0": {"name": "uuid", "version": "8.3.0-beta.0", "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "_id": "uuid@8.3.0-beta.0", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/uuidjs/uuid#readme", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "fbe17ffc7b6214d36a52c89f9e19c82dcc0f294a", "tarball": "https://registry.npmjs.org/uuid/-/uuid-8.3.0-beta.0.tgz", "fileCount": 66, "integrity": "sha512-YX12mJFOtrnE7o7GIbtYIoTrRN+5DTKLJXiUMnucohXeBPY0UYIaK2HrteuarIrWrjNvZ7FROqXMRNPKQz8wMg==", "signatures": [{"sig": "MEUCIQCY4tELxAUmyUTtnM7eZnQ95HW5XWbnDdUNaAiS0UJ8TAIgBHP9wTozaFYBy8Z4CkEUYANwbFcQE0gyzyfqiDJO8iA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112220, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfGL2qCRA9TVsSAnZWagAAqYsP/iD/kd3ajMto1FoCWAiT\n4B4tJRVvY6XYxtnYSukB3C2elXkcb8qWDE60nDRq/PWqBNuKDlXnXxpgGE4C\nJcfY1i+XDP4BxTUM1FxJGhrx6uNTun5zLJnBS02oUxe4LCIeuWus+160ETtQ\ndaWsEglcDQ6yiInKkFGMHb0FdRVPJofjylbxfj/dAVrgHvnCi9obUSw/m8ua\nmrVskeJ8U/X93H4rKen4QDDGyseB3tHqkX2GhrZqdOj3zmX4QAdonBmSyTny\n97YJ5xSQ/vFTEXVAPmbjrp6M5OAGnXS3YYNl3nTUhbsrs2POgSrWCLiucwX7\n1S576Rj2PmMP1PZ6lomFd3HafAlfz9ftGii98bZrG1aWZl06K3jZahYi53tn\nAknpoKggfk/4I0UpeX5/FiO4klZcR1/ggSC2PaJCcrIalYbCdBkzcWCfzXEh\nqlYRzR3S+nqOJqL5KJmSWgZsJVXMAAmx07wyXUHyBoPUj+oitxCN0ueANTUN\nzEibvxMUtj80iUv9FfN0eQUjnyIF4n0+8yz8L6yf4zK14LyaKr16bZq/iNSM\nOVgFp5jhqcFMGnFVaheX74N6h0VezVBOr8YB75pvZGbVGR7FMg3nBLaC76A/\nQtDObF08Z096NaZZhjiZGziMj8UWwnLVPg5EXedSqZPCqclU6Jsow1MT6QHX\n7jEI\r\n=YaPl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "module": "./dist/esm-node/index.js", "browser": {"./dist/md5.js": "./dist/md5-browser.js", "./dist/rng.js": "./dist/rng-browser.js", "./dist/sha1.js": "./dist/sha1-browser.js", "./dist/esm-node/index.js": "./dist/esm-browser/index.js"}, "exports": {".": {"node": {"import": "./wrapper.mjs", "module": "./dist/esm-node/index.js", "require": "./dist/index.js"}, "default": "./dist/esm-browser/index.js"}, "./package.json": "./package.json"}, "gitHead": "d6c185161b80f277ff03fc151e0f4bfb848a4570", "scripts": {"md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v12' ) && ( npm run build && runmd --output=README.md README_js.md )", "lint": "npm run eslint:check && npm run prettier:check", "test": "BABEL_ENV=commonjs node --throw-deprecation node_modules/.bin/jest test/unit/", "build": "./scripts/build.sh", "prepack": "npm run build", "pretest": "[ -n $CI ] || npm run build", "release": "standard-version --no-verify", "docs:diff": "npm run docs && git diff --quiet README.md", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "eslint:check": "eslint src/ test/ examples/ *.js", "pretest:node": "npm run build", "prettier:fix": "prettier --ignore-path .prettierignore --write '**/*.{js,jsx,json,md}'", "test:browser": "wdio run ./wdio.conf.js", "prettier:check": "prettier --ignore-path .prettierignore --check '**/*.{js,jsx,json,md}'", "test:benchmark": "cd examples/benchmark && npm install && npm test", "pretest:browser": "npm run build && npm-run-all --parallel examples:browser:**", "pretest:benchmark": "npm run build", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build"}, "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/uuidjs/uuid.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "RFC4122 (v1, v4, and v5) UUIDs", "directories": {}, "lint-staged": {"*.{js,jsx}": ["eslint --fix"], "*.{js,jsx,json,md}": ["prettier --write"]}, "sideEffects": false, "_nodeVersion": "12.18.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "25.5.4", "husky": "4.2.5", "runmd": "1.3.2", "eslint": "7.3.0", "rollup": "2.18.0", "prettier": "2.0.5", "@wdio/cli": "6.1.20", "@babel/cli": "7.10.3", "@wdio/sync": "6.1.14", "@babel/core": "7.10.3", "bundlewatch": "0.2.7", "lint-staged": "10.2.11", "npm-run-all": "4.1.5", "random-seed": "0.3.0", "babel-eslint": "10.1.0", "@commitlint/cli": "9.1.2", "standard-version": "8.0.2", "@babel/preset-env": "7.10.3", "@wdio/local-runner": "6.1.20", "eslint-plugin-node": "11.1.0", "@wdio/spec-reporter": "6.1.14", "eslint-plugin-import": "2.21.2", "rollup-plugin-terser": "6.1.0", "eslint-plugin-promise": "4.2.1", "eslint-config-prettier": "6.11.0", "eslint-config-standard": "14.1.1", "eslint-plugin-prettier": "3.1.4", "eslint-plugin-standard": "4.0.1", "@wdio/jasmine-framework": "6.1.17", "@wdio/browserstack-service": "6.1.15", "@rollup/plugin-node-resolve": "8.0.1", "@wdio/static-server-service": "6.1.14", "@commitlint/config-conventional": "9.0.1"}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "_npmOperationalInternal": {"tmp": "tmp/uuid_8.3.0-beta.0_1595456937747_0.8708052138920852", "host": "s3://npm-registry-packages"}}, "8.3.0": {"name": "uuid", "version": "8.3.0", "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "_id": "uuid@8.3.0", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/uuidjs/uuid#readme", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "ab738085ca22dc9a8c92725e459b1d507df5d6ea", "tarball": "https://registry.npmjs.org/uuid/-/uuid-8.3.0.tgz", "fileCount": 66, "integrity": "sha512-fX6Z5o4m6XsXBdli9g7DtWgAx+osMsRRZFKma1mIUsLCz6vRvv+pz5VNbyu9UEDzpMWulZfvpgb/cmDXVulYFQ==", "signatures": [{"sig": "MEUCIQCNFGDHbOcxZ53L6dQUhHtZHc/5FF6/fIadt4jyo6jFMgIgblvzqZQDW3ZNsIm8l2S6kXx5h9KlzHd/GDMi3EphMz0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfHySQCRA9TVsSAnZWagAABs0P/jASs5mpvF0tYjja886Z\nK7hAuenEWArgOaP5Ec7kDuwTmvq7m0sDVM630EI+oobCitzyvuN2WsjSbF6K\nobylxJb0OE24W9zy1qCIzMZdsWMWj5K9I48ZowBHUcnIRS02UbWBndNJf7d/\nyCOvP75r9O+tYSoqKdj6hQjGr+5/wQk4WX1u8EI5RT0Z33hoBZ4geFsg8XBW\nlrjKjUaePNjBFwlJxO1osSXd/H2pWEFy9olflx9owJHdscwvz5FNawTSmxaF\n55pYI7kZc3KgQBb8x/d6sYzSC0HcQ87EnZCnlm8AU4cdZQNkak9Q1MDVG9fg\n+GvFVDvrzmx77HxylrsA00HrXK2iJnwKl7rABm7BJmtNGH3FuuKAyBUaMlLl\n//7UwUqh7w0Gu2TjJpNqTkVOzRxwbrvasQEDWPjfZqYKCjpUhM2AHIkGBom6\no3qTej2yW0XcTixgQR4hGhNcyEsFNka3SVUcpek8elwFrrOMpXbnCF5pe+f9\nVwjn3Dg3Ld5vS4KNATeCg90G54MRlkHcajFutLKmmsgYrakqIdS8rbGDA0wi\n/KNYzTVW34HyIUozC4lSEIbYJluob/SqvAxQW9PLH0c/O+OcWj4jF4gHcGzU\n1oMIZvEOSp3eMPc5p9bzzqT2EAx7o5KD2LUfdYCPXQ/pnRoYNRbxHjeU7ys4\n+7pE\r\n=Cc7C\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "module": "./dist/esm-node/index.js", "browser": {"./dist/md5.js": "./dist/md5-browser.js", "./dist/rng.js": "./dist/rng-browser.js", "./dist/sha1.js": "./dist/sha1-browser.js", "./dist/esm-node/index.js": "./dist/esm-browser/index.js"}, "exports": {".": {"node": {"import": "./wrapper.mjs", "module": "./dist/esm-node/index.js", "require": "./dist/index.js"}, "default": "./dist/esm-browser/index.js"}, "./package.json": "./package.json"}, "gitHead": "a91f78d9e593b8a9af1cfebdf33b771b82c42475", "scripts": {"md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v12' ) && ( npm run build && runmd --output=README.md README_js.md )", "lint": "npm run eslint:check && npm run prettier:check", "test": "BABEL_ENV=commonjs node --throw-deprecation node_modules/.bin/jest test/unit/", "build": "./scripts/build.sh", "prepack": "npm run build", "pretest": "[ -n $CI ] || npm run build", "release": "standard-version --no-verify", "docs:diff": "npm run docs && git diff --quiet README.md", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "eslint:check": "eslint src/ test/ examples/ *.js", "pretest:node": "npm run build", "prettier:fix": "prettier --ignore-path .prettierignore --write '**/*.{js,jsx,json,md}'", "test:browser": "wdio run ./wdio.conf.js", "prettier:check": "prettier --ignore-path .prettierignore --check '**/*.{js,jsx,json,md}'", "test:benchmark": "cd examples/benchmark && npm install && npm test", "pretest:browser": "npm run build && npm-run-all --parallel examples:browser:**", "pretest:benchmark": "npm run build", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build"}, "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/uuidjs/uuid.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "RFC4122 (v1, v4, and v5) UUIDs", "directories": {}, "lint-staged": {"*.{js,jsx}": ["eslint --fix"], "*.{js,jsx,json,md}": ["prettier --write"]}, "sideEffects": false, "_nodeVersion": "12.18.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "25.5.4", "husky": "4.2.5", "runmd": "1.3.2", "eslint": "7.3.0", "rollup": "2.18.0", "prettier": "2.0.5", "@wdio/cli": "6.1.20", "@babel/cli": "7.10.3", "@wdio/sync": "6.1.14", "@babel/core": "7.10.3", "bundlewatch": "0.2.7", "lint-staged": "10.2.11", "npm-run-all": "4.1.5", "random-seed": "0.3.0", "babel-eslint": "10.1.0", "@commitlint/cli": "9.1.2", "standard-version": "8.0.2", "@babel/preset-env": "7.10.3", "@wdio/local-runner": "6.1.20", "eslint-plugin-node": "11.1.0", "@wdio/spec-reporter": "6.1.14", "eslint-plugin-import": "2.21.2", "rollup-plugin-terser": "6.1.0", "eslint-plugin-promise": "4.2.1", "eslint-config-prettier": "6.11.0", "eslint-config-standard": "14.1.1", "eslint-plugin-prettier": "3.1.4", "eslint-plugin-standard": "4.0.1", "@wdio/jasmine-framework": "6.1.17", "@wdio/browserstack-service": "6.1.15", "@rollup/plugin-node-resolve": "8.0.1", "@wdio/static-server-service": "6.1.14", "@commitlint/config-conventional": "9.0.1"}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "_npmOperationalInternal": {"tmp": "tmp/uuid_8.3.0_1595876496032_0.44563664352850596", "host": "s3://npm-registry-packages"}}, "8.3.1": {"name": "uuid", "version": "8.3.1", "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "_id": "uuid@8.3.1", "maintainers": [{"name": "vvo", "email": "<EMAIL>"}, {"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/uuidjs/uuid#readme", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "2ba2e6ca000da60fce5a196954ab241131e05a31", "tarball": "https://registry.npmjs.org/uuid/-/uuid-8.3.1.tgz", "fileCount": 66, "integrity": "sha512-FOmRr+FmWEIG8uhZv6C2bTgEVXsHk08kE7mPlrBbEe+c3r9pjceVPgupIfNIhc4yx55H69OXANrUaSuu9eInKg==", "signatures": [{"sig": "MEUCIQC+WYJRHHHyxeGtLWPj90JE3EKJo/r2VrnXEnU4wEsq9wIgDeyRdLAm3l/Vw/j53Z9hSqEscPD8C5x/7LJip+Ws4ks=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 114950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfee5mCRA9TVsSAnZWagAAP8AP/RlQNqqmCN+O+fGNB+/W\n3JIWNWGPhLXMAiHDUrLxEJsCyuoG4F5fm3ouesRKUiChdptAGWKPElsuPiU2\nSONN3lZclwU8K/zc8jwde4B/4Si+UaHdtTZa8+66bseHOM0/QKNRcUVCDEk7\ndP2Y/E9yhsX4Q5f69YicVCZqBSOy+wPDjr1CHNxFJuGCZzYHu7GjBExSHE66\nrSVqsWIhIoUl1vaZP8YqlwnWQRWmbene8Muzz7EW6zM9NsXh3q1SKS2nCjZd\n/r3i/Ci4UzU7VZIiZNzUNNRwhA+474Anrq60QGS+N4ovgbfCVIxKhGQib4Rx\nFCq5LvjLR/9oH1nbM3GxpHLUz64jmVlmu0+Zj3paQNodzUK4WGN18VM1jvO5\nGMdfvGUnHiZduOpmpiXUFthapbp5js0/O8sNDaMr2rYlayz1IUUTDW1XeftE\nQ7c7SYdOvO/KGqjDTNebilJOQ4hDILbmcwZF9t1SXxmgSbpfw/2iJYzbb8LG\nVOZi/Ie1yCDdzX4qqS+/lGGI9TvkVmOYSat7gQ7NlCo3sy5fExdFDK+ewmwM\n+tNw/v1DDWaOtUqYBdgF7Vb8lj0piDy7+PH2/CvJBjdUbja4Ihv6mcuybJPw\nmKlI5EshBeaZTdtxSHcgecuAUHFCf2Z29VHpexUOrkZvi1GJzVCYFMxXX/7r\nUfDb\r\n=dV+3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "module": "./dist/esm-node/index.js", "browser": {"./dist/md5.js": "./dist/md5-browser.js", "./dist/rng.js": "./dist/rng-browser.js", "./dist/sha1.js": "./dist/sha1-browser.js", "./dist/esm-node/index.js": "./dist/esm-browser/index.js"}, "exports": {".": {"node": {"import": "./wrapper.mjs", "module": "./dist/esm-node/index.js", "require": "./dist/index.js"}, "default": "./dist/esm-browser/index.js"}, "./package.json": "./package.json"}, "gitHead": "252ebcfb33b2d056963a530fc02067ae3de6095a", "scripts": {"md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v12' ) && ( npm run build && runmd --output=README.md README_js.md )", "lint": "npm run eslint:check && npm run prettier:check", "test": "BABEL_ENV=commonjs node --throw-deprecation node_modules/.bin/jest test/unit/", "build": "./scripts/build.sh", "prepack": "npm run build", "pretest": "[ -n $CI ] || npm run build", "release": "standard-version --no-verify", "docs:diff": "npm run docs && git diff --quiet README.md", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "eslint:check": "eslint src/ test/ examples/ *.js", "pretest:node": "npm run build", "prettier:fix": "prettier --ignore-path .prettierignore --write '**/*.{js,jsx,json,md}'", "test:browser": "wdio run ./wdio.conf.js", "prettier:check": "prettier --ignore-path .prettierignore --check '**/*.{js,jsx,json,md}'", "test:benchmark": "cd examples/benchmark && npm install && npm test", "pretest:browser": "optional-dev-dependency && npm run build && npm-run-all --parallel examples:browser:**", "pretest:benchmark": "npm run build", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build"}, "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/uuidjs/uuid.git", "type": "git"}, "_npmVersion": "6.14.6", "description": "RFC4122 (v1, v4, and v5) UUIDs", "directories": {}, "lint-staged": {"*.{js,jsx}": ["eslint --fix"], "*.{js,jsx,json,md}": ["prettier --write"]}, "sideEffects": false, "_nodeVersion": "12.18.4", "_hasShrinkwrap": false, "devDependencies": {"jest": "25.5.4", "husky": "4.3.0", "runmd": "1.3.2", "eslint": "7.10.0", "rollup": "2.28.2", "prettier": "2.1.2", "@babel/cli": "7.11.6", "@babel/core": "7.11.6", "bundlewatch": "0.3.1", "lint-staged": "10.4.0", "npm-run-all": "4.1.5", "random-seed": "0.3.0", "babel-eslint": "10.1.0", "@commitlint/cli": "11.0.0", "standard-version": "9.0.0", "@babel/preset-env": "7.11.5", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.22.1", "rollup-plugin-terser": "7.0.2", "eslint-plugin-promise": "4.2.1", "eslint-config-prettier": "6.12.0", "eslint-config-standard": "14.1.1", "eslint-plugin-prettier": "3.1.4", "eslint-plugin-standard": "4.0.1", "optional-dev-dependency": "2.0.1", "@rollup/plugin-node-resolve": "9.0.0", "@commitlint/config-conventional": "11.0.0"}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "_npmOperationalInternal": {"tmp": "tmp/uuid_8.3.1_1601826406124_0.8839671601486778", "host": "s3://npm-registry-packages"}, "optionalDevDependencies": {"@wdio/cli": "6.4.0", "@wdio/sync": "6.4.0", "@wdio/local-runner": "6.4.0", "@wdio/spec-reporter": "6.4.0", "@wdio/jasmine-framework": "6.4.0", "@wdio/browserstack-service": "6.4.0", "@wdio/static-server-service": "6.4.0"}}, "8.3.2-beta.0": {"name": "uuid", "version": "8.3.2-beta.0", "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "_id": "uuid@8.3.2-beta.0", "maintainers": [{"name": "vvo", "email": "<EMAIL>"}, {"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/uuidjs/uuid#readme", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "ecd8091258ce05be1dfb1fa7330481aaf90509a4", "tarball": "https://registry.npmjs.org/uuid/-/uuid-8.3.2-beta.0.tgz", "fileCount": 66, "integrity": "sha512-V2GewaGescJgTw3WDBz3xC6is44S1eCavBX6Kjou1+yLjHnMWA4rxfV1cmGTw2HMGO91AO0+8DvvQyRzWyu2iA==", "signatures": [{"sig": "MEYCIQCaMg4u1Ea79FgKD5O8f9LTotO6XbXzsYsYvNMOWiH35wIhAKznhNt6ndneH5qCcNSFaapUBi0upJCDzuBHGGr7FFDc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 115267, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfuRG4CRA9TVsSAnZWagAArWkP/A4ky90XZaB05E3M+XxL\nXm2wSelV84WssDZTlcmtcH6cfDJhhVvoRRGLjtLK4n3uvlO25zFpBnvcsVLz\nrJmXWkZnev/R5jBKig917EQKSY3xbelYFff+Jp+rAfQsTkHSx4Di3yIhlNkk\n9ow62Rnf0iKTw/LsjY47IXsiPaADt8I+9pbtGA2qY25fxBL6NU+ES4kxM4v0\n1kbCpNSSfak+7W36vgMJEeUwF/RIom8KlYLvqwHCMyNfcboQxgP3SRMEYUKQ\n6+hvY1iPPqBpElRoWNb5F+Z8CTsWpqHEGNh6M8ki1Nqd4ZmciUMea/EqgGno\nX/pPnXX+Eo/NZsb6PtnuymvBJpXsiTmyJdNyLldKi/6iaIzjrofANY8zasNs\nqxd32egI5voyxJ7k9TgkL2oy5XSvF1VCkuPWh7BAI8KihSwglt/KuQopDUZC\nlonzHQ72L/dwMkEoMzj8l8MW3A5BUtnf/NlWphcP/XdU6jrQ2B/KfggLoJBo\nLNuupLAIhLIRifO5y1QB6ngoSv4hoWbPT7ONhAljybJcrcPbKxU9NsOTBN/f\nYV8KQWgE/SKx7h+Xe7VopWBwr/S6VAXjdcJuQraqlv4zb2mUV+TUslTUb2fN\nTNCBf3x2yFdvT9L7Xr3YIbSb2Jxq86BzdrsM0kBOqx+qW9bNUFyPX8YU+Czf\nouYF\r\n=4xPE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "module": "./dist/esm-node/index.js", "browser": {"./dist/md5.js": "./dist/md5-browser.js", "./dist/rng.js": "./dist/rng-browser.js", "./dist/sha1.js": "./dist/sha1-browser.js", "./dist/esm-node/index.js": "./dist/esm-browser/index.js"}, "exports": {".": {"node": {"import": "./wrapper.mjs", "module": "./dist/esm-node/index.js", "require": "./dist/index.js"}, "default": "./dist/esm-browser/index.js"}, "./package.json": "./package.json"}, "gitHead": "334ef62c330d92f8ca376d09087e8ee9abc5cc12", "scripts": {"md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v12' ) && ( npm run build && runmd --output=README.md README_js.md )", "lint": "npm run eslint:check && npm run prettier:check", "test": "BABEL_ENV=commonjs node --throw-deprecation node_modules/.bin/jest test/unit/", "build": "./scripts/build.sh", "prepack": "npm run build", "pretest": "[ -n $CI ] || npm run build", "release": "standard-version --no-verify", "docs:diff": "npm run docs && git diff --quiet README.md", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "eslint:check": "eslint src/ test/ examples/ *.js", "pretest:node": "npm run build", "prettier:fix": "prettier --ignore-path .prettierignore --write '**/*.{js,jsx,json,md}'", "test:browser": "wdio run ./wdio.conf.js", "prettier:check": "prettier --ignore-path .prettierignore --check '**/*.{js,jsx,json,md}'", "test:benchmark": "cd examples/benchmark && npm install && npm test", "pretest:browser": "optional-dev-dependency && npm run build && npm-run-all --parallel examples:browser:**", "pretest:benchmark": "npm run build", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build"}, "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/uuidjs/uuid.git", "type": "git"}, "_npmVersion": "6.14.6", "description": "RFC4122 (v1, v4, and v5) UUIDs", "directories": {}, "lint-staged": {"*.{js,jsx}": ["eslint --fix"], "*.{js,jsx,json,md}": ["prettier --write"]}, "sideEffects": false, "_nodeVersion": "12.18.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "25.5.4", "husky": "4.3.0", "runmd": "1.3.2", "eslint": "7.10.0", "rollup": "2.28.2", "prettier": "2.1.2", "@babel/cli": "7.11.6", "@babel/core": "7.11.6", "bundlewatch": "0.3.1", "lint-staged": "10.4.0", "npm-run-all": "4.1.5", "random-seed": "0.3.0", "babel-eslint": "10.1.0", "@commitlint/cli": "11.0.0", "standard-version": "9.0.0", "@babel/preset-env": "7.11.5", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.22.1", "rollup-plugin-terser": "7.0.2", "eslint-plugin-promise": "4.2.1", "eslint-config-prettier": "6.12.0", "eslint-config-standard": "14.1.1", "eslint-plugin-prettier": "3.1.4", "eslint-plugin-standard": "4.0.1", "optional-dev-dependency": "2.0.1", "@rollup/plugin-node-resolve": "9.0.0", "@commitlint/config-conventional": "11.0.0"}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "_npmOperationalInternal": {"tmp": "tmp/uuid_8.3.2-beta.0_1605964215452_0.17989725484763142", "host": "s3://npm-registry-packages"}, "optionalDevDependencies": {"@wdio/cli": "6.4.0", "@wdio/sync": "6.4.0", "@wdio/local-runner": "6.4.0", "@wdio/spec-reporter": "6.4.0", "@wdio/jasmine-framework": "6.4.0", "@wdio/browserstack-service": "6.4.0", "@wdio/static-server-service": "6.4.0"}}, "8.3.2": {"name": "uuid", "version": "8.3.2", "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "_id": "uuid@8.3.2", "maintainers": [{"name": "vvo", "email": "<EMAIL>"}, {"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/uuidjs/uuid#readme", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "80d5b5ced271bb9af6c445f21a1a04c606cefbe2", "tarball": "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz", "fileCount": 66, "integrity": "sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==", "signatures": [{"sig": "MEYCIQDoIGLM2QHJtoVrBPsbbJcg2h2KwYvA1wDXmB9fI/0ZWgIhAI7TzATj9BbUwfvE5uIZqDRKCbmQ91wpyujY019QKegA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 116098, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfz+RMCRA9TVsSAnZWagAAa6AP+wR9Y2qbJF3GrA17E3aE\nw+Lt1lhp7AW2Nid8rcFEO9umfSvAuN4PX6SX5yJI+aznhmt33GQOGDhvfFzh\nwCJTbeMKjw31BcZVd0jQ4yZKDOl5hw95r7qLE3aJkhukfmaEx/RmtefQrRoi\nFw0gIzdnWehJ3lIOhqEYlXKzQS1BJv1UukwImMptGVhUyTIxPz3MRP2TRG89\nTDIn7Bss4cQblEyScNyiG+k2P4lFo3zhDJZb8UVjtmTD+iaWwKqJ+Dll/10E\nWllr/JUhU15GnegxPEqIdGwfVyUtyFGhQKTJqJErlhCTKrNmacYHuYl71cR6\n2H01asQTBGBDuUSj+SV/b9o5di5LzFmB0jXG9FId1tG6zlOPrKBksO9V7vJl\nQR0jUQJd5kXkNqcjmTJaJHfPsmpJkA5RSf3/o4INMgrqLemgtGXad0tsgmK2\nahxQ/ONGUpEBV6D0D08fmoqG75/nnvXOOLWin22bnwtJq/W+J+pqirxzzVte\nRmBAZIgJERjwhtNup/qhD0LuUwJXrDwuoZhu1GbDDRUyTSar/uB2ZTbfF/GV\nmCLzoTSZzAE64cz1R2mBl0E1BX6p60rcPRNH/AVRsl/18V5IMQS0lK6BnEZN\npRVbgLgwdCLQxAAtw0/XKa+FU3cyttyv/udG3Zpv+Gk4nYVNLl8TBcuxvFPG\n/zXt\r\n=IPIq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "module": "./dist/esm-node/index.js", "browser": {"./dist/md5.js": "./dist/md5-browser.js", "./dist/rng.js": "./dist/rng-browser.js", "./dist/sha1.js": "./dist/sha1-browser.js", "./dist/esm-node/index.js": "./dist/esm-browser/index.js"}, "exports": {".": {"node": {"import": "./wrapper.mjs", "module": "./dist/esm-node/index.js", "require": "./dist/index.js"}, "default": "./dist/esm-browser/index.js"}, "./package.json": "./package.json"}, "gitHead": "ed3240154759b748f6a3b7d545f3b10759ee4ba7", "scripts": {"md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v12' ) && ( npm run build && runmd --output=README.md README_js.md )", "lint": "npm run eslint:check && npm run prettier:check", "test": "BABEL_ENV=commonjs node --throw-deprecation node_modules/.bin/jest test/unit/", "build": "./scripts/build.sh", "prepack": "npm run build", "pretest": "[ -n $CI ] || npm run build", "release": "standard-version --no-verify", "docs:diff": "npm run docs && git diff --quiet README.md", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "eslint:check": "eslint src/ test/ examples/ *.js", "pretest:node": "npm run build", "prettier:fix": "prettier --ignore-path .prettierignore --write '**/*.{js,jsx,json,md}'", "test:browser": "wdio run ./wdio.conf.js", "prettier:check": "prettier --ignore-path .prettierignore --check '**/*.{js,jsx,json,md}'", "test:benchmark": "cd examples/benchmark && npm install && npm test", "pretest:browser": "optional-dev-dependency && npm run build && npm-run-all --parallel examples:browser:**", "pretest:benchmark": "npm run build", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build"}, "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/uuidjs/uuid.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "RFC4122 (v1, v4, and v5) UUIDs", "directories": {}, "lint-staged": {"*.{js,jsx}": ["eslint --fix"], "*.{js,jsx,json,md}": ["prettier --write"]}, "sideEffects": false, "_nodeVersion": "12.19.1", "_hasShrinkwrap": false, "devDependencies": {"jest": "25.5.4", "husky": "4.3.0", "runmd": "1.3.2", "eslint": "7.10.0", "rollup": "2.28.2", "prettier": "2.1.2", "@babel/cli": "7.11.6", "@babel/core": "7.11.6", "bundlewatch": "0.3.1", "lint-staged": "10.4.0", "npm-run-all": "4.1.5", "random-seed": "0.3.0", "babel-eslint": "10.1.0", "@commitlint/cli": "11.0.0", "standard-version": "9.0.0", "@babel/preset-env": "7.11.5", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.22.1", "rollup-plugin-terser": "7.0.2", "eslint-plugin-promise": "4.2.1", "eslint-config-prettier": "6.12.0", "eslint-config-standard": "14.1.1", "eslint-plugin-prettier": "3.1.4", "eslint-plugin-standard": "4.0.1", "optional-dev-dependency": "2.0.1", "@rollup/plugin-node-resolve": "9.0.0", "@commitlint/config-conventional": "11.0.0"}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "_npmOperationalInternal": {"tmp": "tmp/uuid_8.3.2_1607459915862_0.45108061870390803", "host": "s3://npm-registry-packages"}, "optionalDevDependencies": {"@wdio/cli": "6.4.0", "@wdio/sync": "6.4.0", "@wdio/local-runner": "6.4.0", "@wdio/spec-reporter": "6.4.0", "@wdio/jasmine-framework": "6.4.0", "@wdio/browserstack-service": "6.4.0", "@wdio/static-server-service": "6.4.0"}}, "9.0.0-beta.0": {"name": "uuid", "version": "9.0.0-beta.0", "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "_id": "uuid@9.0.0-beta.0", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/uuidjs/uuid#readme", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "164a23bd9bc422462a1274e64d34ab7a8dc3f31b", "tarball": "https://registry.npmjs.org/uuid/-/uuid-9.0.0-beta.0.tgz", "fileCount": 76, "integrity": "sha512-FYW2Ry9thUvDKQKekvKvQhGifh6X4FYAkbN56sYD6l4Zh8EG3GyIiqRKEq9UuIEPO/I1u/grfQxlRjTgnLxvMw==", "signatures": [{"sig": "MEQCIDnMHDyBT0Gs8Tq9un0ZtOBm1BkO+JxBU3YULBBgj/B4AiAE9OrxsVoz7zg4nl1bPgsQmgFkbNL/7HG3MM8o0Gx2xA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 119295, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi7O0UACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmodMhAAo6mAI1wYveeyPvV4CUL8q5w1SZ66SW4xERBGaUTHj4DaxW/S\r\ntVSVJ/px+4yfCoIDb6mVN95Gvo37Q4QezE+BALzymMVdKSnlRr9e38qXfqHG\r\nb3v/MaLOYyG6zNEHVUVJN00X87PEIFMv6llJh624ghMFFn/3WwBG3ZMeQ0Qe\r\nLWdKBnENr6A7Dw51n9T9wGY7dKWs4fc2BkytMGIpH1jfYhV3S6M6LthLKOFM\r\nsGMGEagSm/3dxXwARnCRUUJWtTuQV+BlDEogq/VQCCrKcM7QwqMmf/FWZXBm\r\nCLqKqur0whw5sY3flmQ5Z66jtIQFWiYerrgl6NW+huQ0BhxXL5E95zcGfbTk\r\nrVz/iUeW3XBW99oq63+yaSS+pIuHV46XIU44w1i7BkxUOHGbnIU7LG3vwsaE\r\n+ZU3zDwIal32It/P3xL7wMht56Bo6iRZL4PMpWBoOVWJicEOOX/dmVjfuBpK\r\nNcfpG38aEBq2PBXs16UZ6NoLa07m2aEytzWMzvZhHQeqyciBUaRDI0FeCj/c\r\nq0pj/yYIfP/YAmcOv7R0EZWRkVgBsV89U564X9T7IuN7AbrSajlQB5MW5Lm/\r\nQfqEu561+nqyaDEmZfGT5YHwiJH/0ZuYD235Lu4kKRaNJY9IPTsaTxIH7l5G\r\nW0YmHAu+1hRvCTWC3dn4xLqDwtUVsDT3ba0=\r\n=+yYu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "module": "./dist/esm-node/index.js", "browser": {"./dist/md5.js": "./dist/md5-browser.js", "./dist/rng.js": "./dist/rng-browser.js", "./dist/sha1.js": "./dist/sha1-browser.js", "./dist/native.js": "./dist/native-browser.js", "./dist/esm-node/index.js": "./dist/esm-browser/index.js"}, "exports": {".": {"node": {"import": "./wrapper.mjs", "module": "./dist/esm-node/index.js", "require": "./dist/index.js"}, "browser": {"import": "./dist/esm-browser/index.js", "require": "./dist/commonjs-browser/index.js"}, "default": "./dist/esm-browser/index.js"}, "./package.json": "./package.json"}, "gitHead": "ee8a9c1edab8d6d6cc81cbb51416b87753fd0ed5", "scripts": {"md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v16' ) && ( npm run build && runmd --output=README.md README_js.md )", "lint": "npm run eslint:check && npm run prettier:check", "test": "BABEL_ENV=commonjsNode node --throw-deprecation node_modules/.bin/jest test/unit/", "build": "./scripts/build.sh", "prepack": "npm run build", "prepare": "cd $( git rev-parse --show-toplevel ) && husky install", "pretest": "[ -n $CI ] || npm run build", "release": "standard-version --no-verify", "docs:diff": "npm run docs && git diff --quiet README.md", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "eslint:check": "eslint src/ test/ examples/ *.js", "pretest:node": "npm run build", "prettier:fix": "prettier --write '**/*.{js,jsx,json,md}'", "test:browser": "wdio run ./wdio.conf.js", "prettier:check": "prettier --check '**/*.{js,jsx,json,md}'", "test:benchmark": "cd examples/benchmark && npm install && npm test", "pretest:browser": "optional-dev-dependency && npm run build && npm-run-all --parallel examples:browser:**", "pretest:benchmark": "npm run build", "examples:node:jest:test": "cd examples/node-jest && npm install && npm test", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build"}, "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/uuidjs/uuid.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "RFC4122 (v1, v4, and v5) UUIDs", "directories": {}, "lint-staged": {"*.{js,jsx}": ["eslint --fix"], "*.{js,jsx,json,md}": ["prettier --write"]}, "sideEffects": false, "_nodeVersion": "16.14.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "28.1.3", "husky": "8.0.1", "runmd": "1.3.6", "eslint": "8.21.0", "prettier": "2.7.1", "@babel/cli": "7.18.10", "@babel/core": "7.18.10", "bundlewatch": "0.3.3", "lint-staged": "13.0.3", "npm-run-all": "4.1.5", "random-seed": "0.3.0", "@commitlint/cli": "17.0.3", "standard-version": "9.5.0", "@babel/preset-env": "7.18.10", "eslint-plugin-node": "11.1.0", "@babel/eslint-parser": "7.18.9", "eslint-plugin-import": "2.26.0", "eslint-plugin-promise": "6.0.0", "eslint-config-prettier": "8.5.0", "eslint-config-standard": "17.0.0", "eslint-plugin-prettier": "4.2.1", "optional-dev-dependency": "2.0.1", "@commitlint/config-conventional": "17.0.3"}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "_npmOperationalInternal": {"tmp": "tmp/uuid_9.0.0-beta.0_1659694356059_0.631985123339952", "host": "s3://npm-registry-packages"}, "optionalDevDependencies": {"@wdio/cli": "7.16.10", "@wdio/local-runner": "7.16.10", "@wdio/spec-reporter": "7.16.9", "@wdio/jasmine-framework": "7.16.6", "@wdio/browserstack-service": "7.16.10", "@wdio/static-server-service": "7.16.6"}}, "9.0.0": {"name": "uuid", "version": "9.0.0", "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "_id": "uuid@9.0.0", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/uuidjs/uuid#readme", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "592f550650024a38ceb0c562f2f6aa435761efb5", "tarball": "https://registry.npmjs.org/uuid/-/uuid-9.0.0.tgz", "fileCount": 76, "integrity": "sha512-MXcSTerfPa4uqyzStbRoTgt5XIe3x5+42+q1sDuy3R5MDk66URdLMOZe5aPX/SQd+kuYAh0FdP/pO28IkQyTeg==", "signatures": [{"sig": "MEYCIQDmKbDr6WecVwMEbEwyKBiUaPniMBvoQeVW4/TgnB4XzwIhAPnKsWjySkMnuquSAkRxJl8MsKPm3S8so3Z19XguF1NJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 122687, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFlYqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo9Ig//av6W4HOYtB3r5tW8Ie10yCvrJIAs8DqTN0hTvmuDvepR+iQM\r\ngEd/T/yL1UoWLE789CMNbsq4rz/LAhDzES52rjevFhE4VPC9P1ZqMvwLnQnS\r\n4BSsDUoJ58VoyCH+oRRR5YiBu/BJBHfGsiAgaIrEyq4hliC9FXs5CZCebJpd\r\nU/E2JX/G9yF5OR/UO5vuHRX7AORQh6WTnsT6qaCJK325vzA8POvMR3TDowAF\r\nltyDq8nlYDeDLEh84Qvv7QSJuQAQ3i1qBVPoXyuTTKS6w8vl+m1wUrxkEblJ\r\nVlxdjGvwlgq7MFL/pkiKgtaaK8ccQcqHn8wEJjqR/i9OpQmC2jFr9qeVrG1A\r\naJy6N8OTSd1iI4P6AprE8VnhXj/OZnZUR6p9oDfwGZAuiMjK3pSvEKXiaQqz\r\n31VRwq2mp+7Kq4AnIbRBkFWKm3L3c9lTa1ltNAW02585x9jxLPABMxd7vLip\r\n4VANifKwtfuTVcEwDGO8kdFbyeuiNRxf8igL041KWbINzQRsb/ElZg3jk//u\r\nFjZBMviIf9CK1IOCO1gX4YoEj4pFvwGkuuFPL145jfyKiGr/4UMw2/OjCJOo\r\no35Xf4yc4YUloK4D3auxXtbTn8jKq8cIxsLo3pITudQc7zWaYs8HkrRca3A/\r\nbbcDTamFyh736XTsJR3P9Cb6BT3uGWJ0uPc=\r\n=Ju3Q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "module": "./dist/esm-node/index.js", "browser": {"./dist/md5.js": "./dist/md5-browser.js", "./dist/rng.js": "./dist/rng-browser.js", "./dist/sha1.js": "./dist/sha1-browser.js", "./dist/native.js": "./dist/native-browser.js", "./dist/esm-node/index.js": "./dist/esm-browser/index.js"}, "exports": {".": {"node": {"import": "./wrapper.mjs", "module": "./dist/esm-node/index.js", "require": "./dist/index.js"}, "browser": {"import": "./dist/esm-browser/index.js", "require": "./dist/commonjs-browser/index.js"}, "default": "./dist/esm-browser/index.js"}, "./package.json": "./package.json"}, "gitHead": "4cf24c018cead5ebe48cb4da232b57a2345d9fb5", "scripts": {"md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v16' ) && ( npm run build && runmd --output=README.md README_js.md )", "lint": "npm run eslint:check && npm run prettier:check", "test": "BABEL_ENV=commonjsNode node --throw-deprecation node_modules/.bin/jest test/unit/", "build": "./scripts/build.sh", "prepack": "npm run build", "prepare": "cd $( git rev-parse --show-toplevel ) && husky install", "pretest": "[ -n $CI ] || npm run build", "release": "standard-version --no-verify", "docs:diff": "npm run docs && git diff --quiet README.md", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "eslint:check": "eslint src/ test/ examples/ *.js", "pretest:node": "npm run build", "prettier:fix": "prettier --write '**/*.{js,jsx,json,md}'", "test:browser": "wdio run ./wdio.conf.js", "prettier:check": "prettier --check '**/*.{js,jsx,json,md}'", "test:benchmark": "cd examples/benchmark && npm install && npm test", "pretest:browser": "optional-dev-dependency && npm run build && npm-run-all --parallel examples:browser:**", "pretest:benchmark": "npm run build", "examples:node:jest:test": "cd examples/node-jest && npm install && npm test", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build"}, "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/uuidjs/uuid.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "RFC4122 (v1, v4, and v5) UUIDs", "directories": {}, "lint-staged": {"*.{js,jsx}": ["eslint --fix"], "*.{js,jsx,json,md}": ["prettier --write"]}, "sideEffects": false, "_nodeVersion": "16.14.1", "_hasShrinkwrap": false, "devDependencies": {"jest": "28.1.3", "husky": "8.0.1", "runmd": "1.3.6", "eslint": "8.21.0", "prettier": "2.7.1", "@babel/cli": "7.18.10", "@babel/core": "7.18.10", "bundlewatch": "0.3.3", "lint-staged": "13.0.3", "npm-run-all": "4.1.5", "random-seed": "0.3.0", "@commitlint/cli": "17.0.3", "standard-version": "9.5.0", "@babel/preset-env": "7.18.10", "eslint-plugin-node": "11.1.0", "@babel/eslint-parser": "7.18.9", "eslint-plugin-import": "2.26.0", "eslint-plugin-promise": "6.0.0", "eslint-config-prettier": "8.5.0", "eslint-config-standard": "17.0.0", "eslint-plugin-prettier": "4.2.1", "optional-dev-dependency": "2.0.1", "@commitlint/config-conventional": "17.0.3"}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "_npmOperationalInternal": {"tmp": "tmp/uuid_9.0.0_1662408234545_0.8259576626529794", "host": "s3://npm-registry-packages"}, "optionalDevDependencies": {"@wdio/cli": "7.16.10", "@wdio/local-runner": "7.16.10", "@wdio/spec-reporter": "7.16.9", "@wdio/jasmine-framework": "7.16.6", "@wdio/browserstack-service": "7.16.10", "@wdio/static-server-service": "7.16.6"}}, "9.0.1": {"name": "uuid", "version": "9.0.1", "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "_id": "uuid@9.0.1", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/uuidjs/uuid#readme", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "e188d4c8853cc722220392c424cd637f32293f30", "tarball": "https://registry.npmjs.org/uuid/-/uuid-9.0.1.tgz", "fileCount": 76, "integrity": "sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==", "signatures": [{"sig": "MEYCIQDvscJxv7mXEiTE+Ykp+FxcDL4/XiNCg4qDG+EPH07gaQIhAPzoirDjELJCA3SqLrbV0nJFhheyCg+AGW6BJ2QolPKT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 123288}, "main": "./dist/index.js", "module": "./dist/esm-node/index.js", "browser": {"./dist/md5.js": "./dist/md5-browser.js", "./dist/rng.js": "./dist/rng-browser.js", "./dist/sha1.js": "./dist/sha1-browser.js", "./dist/native.js": "./dist/native-browser.js", "./dist/esm-node/index.js": "./dist/esm-browser/index.js"}, "exports": {".": {"node": {"import": "./wrapper.mjs", "module": "./dist/esm-node/index.js", "require": "./dist/index.js"}, "browser": {"import": "./dist/esm-browser/index.js", "require": "./dist/commonjs-browser/index.js"}, "default": "./dist/esm-browser/index.js"}, "./package.json": "./package.json"}, "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "gitHead": "ca1d39d58a6308d5311bcb356a931aa818ec0ded", "scripts": {"md": "runmd --watch --output=README.md README_js.md", "docs": "( node --version | grep -q 'v18' ) && ( npm run build && npx runmd --output=README.md README_js.md )", "lint": "npm run eslint:check && npm run prettier:check", "test": "BABEL_ENV=commonjsNode node --throw-deprecation node_modules/.bin/jest test/unit/", "build": "./scripts/build.sh", "prepack": "npm run build", "prepare": "cd $( git rev-parse --show-toplevel ) && husky install", "pretest": "[ -n $CI ] || npm run build", "release": "standard-version --no-verify", "docs:diff": "npm run docs && git diff --quiet README.md", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "eslint:check": "eslint src/ test/ examples/ *.js", "pretest:node": "npm run build", "prettier:fix": "prettier --write '**/*.{js,jsx,json,md}'", "test:browser": "wdio run ./wdio.conf.js", "prettier:check": "prettier --check '**/*.{js,jsx,json,md}'", "test:benchmark": "cd examples/benchmark && npm install && npm test", "pretest:browser": "optional-dev-dependency && npm run build && npm-run-all --parallel examples:browser:**", "pretest:benchmark": "npm run build", "examples:node:jest:test": "cd examples/node-jest && npm install && npm test", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build"}, "_npmUser": {"name": "ctavan", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/uuidjs/uuid.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "RFC4122 (v1, v4, and v5) UUIDs", "directories": {}, "lint-staged": {"*.{js,jsx}": ["eslint --fix"], "*.{js,jsx,json,md}": ["prettier --write"]}, "sideEffects": false, "_nodeVersion": "16.20.2", "_hasShrinkwrap": false, "devDependencies": {"jest": "28.1.3", "husky": "8.0.1", "runmd": "1.3.9", "eslint": "8.21.0", "prettier": "2.7.1", "@babel/cli": "7.18.10", "@babel/core": "7.18.10", "bundlewatch": "0.3.3", "lint-staged": "13.0.3", "npm-run-all": "4.1.5", "random-seed": "0.3.0", "@commitlint/cli": "17.0.3", "standard-version": "9.5.0", "@babel/preset-env": "7.18.10", "eslint-plugin-node": "11.1.0", "@babel/eslint-parser": "7.18.9", "eslint-plugin-import": "2.26.0", "eslint-plugin-promise": "6.0.0", "eslint-config-prettier": "8.5.0", "eslint-config-standard": "17.0.0", "eslint-plugin-prettier": "4.2.1", "optional-dev-dependency": "2.0.1", "@commitlint/config-conventional": "17.0.3"}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "_npmOperationalInternal": {"tmp": "tmp/uuid_9.0.1_1694508995051_0.21952707353933043", "host": "s3://npm-registry-packages"}, "optionalDevDependencies": {"@wdio/cli": "7.16.10", "@wdio/local-runner": "7.16.10", "@wdio/spec-reporter": "7.16.9", "@wdio/jasmine-framework": "7.16.6", "@wdio/browserstack-service": "7.16.10", "@wdio/static-server-service": "7.16.6"}}, "10.0.0": {"name": "uuid", "version": "10.0.0", "keywords": ["uuid", "guid", "rfc4122", "rfc9562"], "license": "MIT", "_id": "uuid@10.0.0", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/uuidjs/uuid#readme", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "bin": {"uuid": "dist/bin/uuid"}, "dist": {"shasum": "5a95aa454e6e002725c79055fd42aaba30ca6294", "tarball": "https://registry.npmjs.org/uuid/-/uuid-10.0.0.tgz", "fileCount": 96, "integrity": "sha512-8XkAphELsDnEGrDxUOHB3RGvXz6TeuYSGEZBOjtTtPm2lwhGBjLgOzLHB63IUWfBpNucQjND6d3AOudO+H3RWQ==", "signatures": [{"sig": "MEYCIQCL55oFsbUUreujw9RQl+8DxybBLO+ccjIG4q3U5SV8ngIhAPV/TPJWjej3tuO1uwAw8Y/DWwpB/uu/aDvKTJWH/QUA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 168173}, "main": "./dist/index.js", "module": "./dist/esm-node/index.js", "browser": {"./dist/md5.js": "./dist/md5-browser.js", "./dist/rng.js": "./dist/rng-browser.js", "./dist/sha1.js": "./dist/sha1-browser.js", "./dist/native.js": "./dist/native-browser.js", "./dist/esm-node/index.js": "./dist/esm-browser/index.js"}, "exports": {".": {"node": {"import": "./wrapper.mjs", "module": "./dist/esm-node/index.js", "require": "./dist/index.js"}, "browser": {"import": "./dist/esm-browser/index.js", "require": "./dist/commonjs-browser/index.js"}, "default": "./dist/esm-browser/index.js"}, "./package.json": "./package.json"}, "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "gitHead": "5388bbb03bbb426ade0bb32ad0b0f6a2e8d69042", "scripts": {"md": "runmd --watch --output=README.md README_js.md", "docs": "npm run build && npx runmd --output=README.md README_js.md", "lint": "npm run eslint:check && npm run prettier:check", "test": "BABEL_ENV=commonjsNode node --throw-deprecation node_modules/.bin/jest test/unit/", "build": "./scripts/build.sh", "prepack": "npm run build", "prepare": "husky install", "pretest": "npm run build", "release": "standard-version --no-verify", "docs:diff": "npm run docs && git diff --quiet README.md", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "eslint:fix": "eslint --fix src/ test/ examples/ *.js", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "eslint:check": "eslint src/ test/ examples/ *.js", "pretest:node": "npm run build", "prettier:fix": "prettier --write .", "test:browser": "wdio run ./wdio.conf.js", "test:matching": "BABEL_ENV=commonjsNode node --throw-deprecation node_modules/.bin/jest test/unit/ -t", "prettier:check": "prettier --check .", "test:benchmark": "cd examples/benchmark && HUSKY=0 npm install && npm test", "pretest:browser": "optional-dev-dependency && npm run build && npm-run-all --parallel examples:browser:**", "pretest:benchmark": "npm run build", "examples:node:jest:test": "cd examples/node-jest && npm install && npm test", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build"}, "_npmUser": {"name": "broofa", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/uuidjs/uuid.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "RFC9562 UUIDs", "directories": {}, "lint-staged": {"*": ["prettier --no-error-on-unmatched-pattern --write"], "*.{js,jsx}": ["eslint --no-error-on-unmatched-pattern --fix"]}, "sideEffects": false, "_nodeVersion": "20.12.2", "_hasShrinkwrap": false, "packageManager": "npm@10.8.1+sha256.b8807aebb9656758e2872fa6e7c564b506aa2faa9297439a478d471d2fe32483", "devDependencies": {"jest": "29.7.0", "husky": "9.0.11", "runmd": "1.3.9", "eslint": "9.4.0", "globals": "15.3.0", "prettier": "3.3.0", "@wdio/cli": "7.16.10", "@babel/cli": "7.24.6", "@babel/core": "7.24.6", "bundlewatch": "0.3.3", "lint-staged": "15.2.5", "neostandard": "0.5.1", "npm-run-all": "4.1.5", "random-seed": "0.3.0", "@commitlint/cli": "19.3.0", "standard-version": "9.5.0", "@babel/preset-env": "7.24.6", "@wdio/local-runner": "7.16.10", "@wdio/spec-reporter": "7.16.9", "@babel/eslint-parser": "7.24.6", "eslint-plugin-prettier": "5.1.3", "@wdio/jasmine-framework": "7.16.6", "optional-dev-dependency": "2.0.1", "@wdio/browserstack-service": "7.16.10", "@wdio/static-server-service": "7.16.6", "@commitlint/config-conventional": "19.2.2", "@babel/plugin-syntax-import-attributes": "7.24.6"}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "_npmOperationalInternal": {"tmp": "tmp/uuid_10.0.0_1717940524111_0.5936782976152961", "host": "s3://npm-registry-packages"}, "optionalDevDependencies": {"@wdio/cli": "7.16.10", "@wdio/local-runner": "7.16.10", "@wdio/spec-reporter": "7.16.9", "@wdio/jasmine-framework": "7.16.6", "@wdio/browserstack-service": "7.16.10", "@wdio/static-server-service": "7.16.6"}}, "11.0.0-0": {"name": "uuid", "version": "11.0.0-0", "keywords": ["uuid", "guid", "rfc4122", "rfc9562"], "license": "MIT", "_id": "uuid@11.0.0-0", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/uuidjs/uuid#readme", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "bin": {"uuid": "dist/esm/bin/uuid"}, "dist": {"shasum": "b9d151889aa116aceaa21c1a1a5e0d44a7fcbccc", "tarball": "https://registry.npmjs.org/uuid/-/uuid-11.0.0-0.tgz", "fileCount": 281, "integrity": "sha512-gPhXpKFuxFX0BvpbLtzvYQf+aqKWDGL0mpjrIg6k/DgG/VrOdZ4+RbmSeP89UVLsgGxecQ2n7aE6OESwYYnCpg==", "signatures": [{"sig": "MEQCIG9krhIVIlTZAM4Q/Pxl0+PAxhUUbWG1s6akuBOFRI9zAiAOVUs0qUkJyIaA1zuGIVOjkdjgCdz1ir71sLs7CkSSMA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 650868}, "main": "./dist/cjs/index.js", "type": "module", "types": "./dist/cjs/index.d.ts", "module": "./dist/esm/index.js", "exports": {".": {"node": {"import": "./wrapper.mjs", "module": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}, "browser": {"import": "./dist/esm-browser/index.js", "require": "./dist/cjs-browser/index.js"}, "default": "./dist/esm-browser/index.js"}, "./package.json": "./package.json"}, "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "gitHead": "f7fd0bd544ee3dc4fe08d1c9e98ffee682ed73c4", "scripts": {"md": "runmd --watch --output=README.md README_js.md", "docs": "npm run build && npx runmd --output=README.md README_js.md", "lint": "npm run eslint:check && npm run prettier:check", "test": "node --test --enable-source-maps dist/esm/test", "build": "./scripts/build.sh", "prepack": "npm run build", "prepare": "husky", "pretest": "npm run build", "release": "standard-version --no-verify", "docs:diff": "npm run docs && git diff --quiet README.md", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "eslint:fix": "eslint --fix src/ test/ examples/ *.[jt]s", "test:watch": "node --test --enable-source-maps --watch dist/esm/test", "build:watch": "tsc --watch -p tsconfig.esm.json", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "eslint:check": "eslint src/ test/ examples/ *.[jt]s", "pretest:node": "npm run build", "prettier:fix": "prettier --write .", "test:browser": "wdio run ./wdio.conf.js", "prettier:check": "prettier --check .", "test:benchmark": "cd examples/benchmark && HUSKY=0 npm install && npm test", "pretest:browser": "optional-dev-dependency && npm run build && npm-run-all --parallel examples:browser:**", "pretest:benchmark": "npm run build", "examples:node:jest:test": "cd examples/node-jest && npm install && npm test", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build"}, "_npmUser": {"name": "broofa", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/uuidjs/uuid.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "RFC9562 UUIDs", "directories": {}, "lint-staged": {"*": ["prettier --no-error-on-unmatched-pattern --write"], "*.{js,jsx}": ["eslint --no-error-on-unmatched-pattern --fix"]}, "sideEffects": false, "_nodeVersion": "20.16.0", "_hasShrinkwrap": false, "packageManager": "npm@10.8.2+sha256.c8c61ba0fa0ab3b5120efd5ba97fdaf0e0b495eef647a97c4413919eda0a878b", "readmeFilename": "README.md", "devDependencies": {"jest": "29.7.0", "husky": "9.1.1", "runmd": "1.3.9", "eslint": "9.7.0", "globals": "15.8.0", "prettier": "3.3.3", "@wdio/cli": "9.0.9", "@eslint/js": "9.7.0", "typescript": "5.5.3", "bundlewatch": "0.3.3", "lint-staged": "15.2.7", "neostandard": "0.11.1", "npm-run-all": "4.1.5", "@commitlint/cli": "19.3.0", "standard-version": "9.5.0", "@types/eslint__js": "8.42.3", "typescript-eslint": "8.0.0-alpha.30", "@wdio/local-runner": "9.0.9", "@wdio/spec-reporter": "9.0.8", "@babel/eslint-parser": "7.24.8", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "@wdio/jasmine-framework": "9.0.9", "optional-dev-dependency": "2.0.1", "@wdio/browserstack-service": "9.0.9", "@wdio/static-server-service": "9.0.8", "@commitlint/config-conventional": "19.2.2"}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "_npmOperationalInternal": {"tmp": "tmp/uuid_11.0.0-0_1725570506587_0.7037416393141083", "host": "s3://npm-registry-packages"}, "optionalDevDependencies": {"@wdio/cli": "9.0.9", "@wdio/local-runner": "9.0.9", "@wdio/spec-reporter": "9.0.8", "@wdio/jasmine-framework": "9.0.9", "@wdio/browserstack-service": "9.0.9", "@wdio/static-server-service": "9.0.8"}}, "11.0.0": {"name": "uuid", "version": "11.0.0", "keywords": ["uuid", "guid", "rfc4122", "rfc9562"], "license": "MIT", "_id": "uuid@11.0.0", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/uuidjs/uuid#readme", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "bin": {"uuid": "dist/esm/bin/uuid"}, "dist": {"shasum": "01c1f5492ed10ad2c0fba1ae1f6d542e6b568d0c", "tarball": "https://registry.npmjs.org/uuid/-/uuid-11.0.0.tgz", "fileCount": 281, "integrity": "sha512-iE8Fa5fgBY4rN5GvNUJ8TSwO1QG7TzdPfhrJczf6XJ6mZUxh/GX433N70fCiJL9h8EKP5ayEIo0Q6EBQGWHFqA==", "signatures": [{"sig": "MEYCIQCWrOJWNatNXcL1pMUOznnkHDsGU1CRtzT36hSfM0rCVwIhAMDFbImN0yh7LmQzMyhwTfRdAAgreWUl2qtrBvuEjlEL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 667676}, "main": "./dist/cjs/index.js", "type": "module", "types": "./dist/cjs/index.d.ts", "module": "./dist/esm/index.js", "exports": {".": {"node": {"import": "./wrapper.mjs", "module": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}, "browser": {"import": "./dist/esm-browser/index.js", "require": "./dist/cjs-browser/index.js"}, "default": "./dist/esm-browser/index.js"}, "./package.json": "./package.json"}, "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "gitHead": "964be22f812c89ee2e1c1fb335eea3b2163f45b3", "scripts": {"md": "runmd --watch --output=README.md README_js.md", "docs": "npm run build && npx runmd --output=README.md README_js.md", "lint": "npm run eslint:check && npm run prettier:check", "test": "node --test --enable-source-maps dist/esm/test/*.js", "build": "./scripts/build.sh", "prepack": "npm run build", "prepare": "husky", "pretest": "npm run build", "release": "standard-version --no-verify", "docs:diff": "npm run docs && git diff --quiet README.md", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "eslint:fix": "eslint --fix src/ test/ examples/ *.[jt]s", "test:watch": "node --test --enable-source-maps --watch dist/esm/test/*.js", "build:watch": "tsc --watch -p tsconfig.esm.json", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "eslint:check": "eslint src/ test/ examples/ *.[jt]s", "pretest:node": "npm run build", "prettier:fix": "prettier --write .", "test:browser": "wdio run ./wdio.conf.js", "prettier:check": "prettier --check .", "test:benchmark": "cd examples/benchmark && HUSKY=0 npm install && npm test", "pretest:browser": "optional-dev-dependency && npm run build && npm-run-all --parallel examples:browser:**", "pretest:benchmark": "npm run build", "examples:node:jest:test": "cd examples/node-jest && npm install && npm test", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build"}, "_npmUser": {"name": "broofa", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/uuidjs/uuid.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "RFC9562 UUIDs", "directories": {}, "lint-staged": {"*": ["prettier --no-error-on-unmatched-pattern --write"], "*.{js,jsx}": ["eslint --no-error-on-unmatched-pattern --fix"]}, "sideEffects": false, "_nodeVersion": "20.18.0", "_hasShrinkwrap": false, "packageManager": "npm@10.9.0", "devDependencies": {"jest": "29.7.0", "husky": "9.1.6", "runmd": "1.3.9", "eslint": "9.13.0", "globals": "15.11.0", "prettier": "3.3.3", "@eslint/js": "9.13.0", "typescript": "5.6.3", "bundlewatch": "0.4.0", "lint-staged": "15.2.10", "neostandard": "0.11.7", "npm-run-all": "4.1.5", "release-please": "16.14.3", "@commitlint/cli": "19.5.0", "standard-version": "9.5.0", "@types/eslint__js": "8.42.3", "typescript-eslint": "8.11.0", "@babel/eslint-parser": "7.25.9", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "optional-dev-dependency": "2.0.1", "@commitlint/config-conventional": "19.5.0"}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "_npmOperationalInternal": {"tmp": "tmp/uuid_11.0.0_1730039713404_0.7280415691476416", "host": "s3://npm-registry-packages"}, "optionalDevDependencies": {"@wdio/cli": "9.2.1", "@wdio/local-runner": "9.2.1", "@wdio/spec-reporter": "9.1.3", "@wdio/jasmine-framework": "9.2.1", "@wdio/browserstack-service": "9.2.1", "@wdio/static-server-service": "9.1.3"}}, "11.0.1": {"name": "uuid", "version": "11.0.1", "keywords": ["uuid", "guid", "rfc4122", "rfc9562"], "license": "MIT", "_id": "uuid@11.0.1", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/uuidjs/uuid#readme", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "bin": {"uuid": "dist/esm/bin/uuid"}, "dist": {"shasum": "a527e188c4c11a7ff5d139e59f229a9f90440669", "tarball": "https://registry.npmjs.org/uuid/-/uuid-11.0.1.tgz", "fileCount": 281, "integrity": "sha512-wt9UB5EcLhnboy1UvA1mvGPXkIIrHSu+3FmUksARfdVw9tuPf3CH/CohxO0Su1ApoKAeT6BVzAJIvjTuQVSmuQ==", "signatures": [{"sig": "MEUCIBRJbGueTPd1gtkU1IoPh1dEhkFvJvWNEVfHXseVOiulAiEAl4LyNbDMXqgaJmZynfQpT5txlVbn5fYtKCGMCaGKHy0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 650767}, "main": "./dist/cjs/index.js", "type": "module", "types": "./dist/cjs/index.d.ts", "module": "./dist/esm/index.js", "browser": {"./dist/cjs/index.js": "./dist/cjs-browser/index.js", "./dist/esm/index.js": "./dist/esm-browser/index.js"}, "exports": {".": {"node": {"import": "./wrapper.mjs", "module": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}, "browser": {"import": "./dist/esm-browser/index.js", "require": "./dist/cjs-browser/index.js"}, "default": "./dist/esm-browser/index.js"}, "./package.json": "./package.json"}, "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "gitHead": "50dc0cee4b5879f1b77698b0103f38aac902939d", "scripts": {"md": "runmd --watch --output=README.md README_js.md", "docs": "npm run build && npx runmd --output=README.md README_js.md", "lint": "npm run eslint:check && npm run prettier:check", "test": "node --test --enable-source-maps dist/esm/test/*.js", "build": "./scripts/build.sh", "prepack": "npm run build", "prepare": "husky", "pretest": "npm run build", "release": "standard-version --no-verify", "docs:diff": "npm run docs && git diff --quiet README.md", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "eslint:fix": "eslint --fix src/ test/ examples/ *.[jt]s", "test:watch": "node --test --enable-source-maps --watch dist/esm/test/*.js", "build:watch": "tsc --watch -p tsconfig.esm.json", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "eslint:check": "eslint src/ test/ examples/ *.[jt]s", "pretest:node": "npm run build", "prettier:fix": "prettier --write .", "test:browser": "wdio run ./wdio.conf.js", "prettier:check": "prettier --check .", "test:benchmark": "cd examples/benchmark && HUSKY=0 npm install && npm test", "pretest:browser": "optional-dev-dependency && npm run build && npm-run-all --parallel examples:browser:**", "pretest:benchmark": "npm run build", "examples:node:jest:test": "cd examples/node-jest && npm install && npm test", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build"}, "_npmUser": {"name": "broofa", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/uuidjs/uuid.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "RFC9562 UUIDs", "directories": {}, "lint-staged": {"*": ["prettier --no-error-on-unmatched-pattern --write"], "*.{js,jsx}": ["eslint --no-error-on-unmatched-pattern --fix"]}, "sideEffects": false, "_nodeVersion": "20.16.0", "_hasShrinkwrap": false, "packageManager": "npm@10.9.0", "devDependencies": {"jest": "29.7.0", "husky": "9.1.6", "runmd": "1.3.9", "eslint": "9.13.0", "globals": "15.11.0", "prettier": "3.3.3", "@eslint/js": "9.13.0", "typescript": "5.6.3", "bundlewatch": "0.4.0", "lint-staged": "15.2.10", "neostandard": "0.11.7", "npm-run-all": "4.1.5", "release-please": "16.14.3", "@commitlint/cli": "19.5.0", "standard-version": "9.5.0", "@types/eslint__js": "8.42.3", "typescript-eslint": "8.11.0", "@babel/eslint-parser": "7.25.9", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "optional-dev-dependency": "2.0.1", "@commitlint/config-conventional": "19.5.0"}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "_npmOperationalInternal": {"tmp": "tmp/uuid_11.0.1_1730065224431_0.0730012896207195", "host": "s3://npm-registry-packages"}, "optionalDevDependencies": {"@wdio/cli": "9.2.1", "@wdio/local-runner": "9.2.1", "@wdio/spec-reporter": "9.1.3", "@wdio/jasmine-framework": "9.2.1", "@wdio/browserstack-service": "9.2.1", "@wdio/static-server-service": "9.1.3"}}, "11.0.2": {"name": "uuid", "version": "11.0.2", "keywords": ["uuid", "guid", "rfc4122", "rfc9562"], "license": "MIT", "_id": "uuid@11.0.2", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/uuidjs/uuid#readme", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "bin": {"uuid": "dist/esm/bin/uuid"}, "dist": {"shasum": "a8d68ba7347d051e7ea716cc8dcbbab634d66875", "tarball": "https://registry.npmjs.org/uuid/-/uuid-11.0.2.tgz", "fileCount": 280, "integrity": "sha512-14FfcOJmqdjbBPdDjFQyk/SdT4NySW4eM0zcG+HqbHP5jzuH56xO3J1DGhgs/cEMCfwYi3HQI1gnTO62iaG+tQ==", "signatures": [{"sig": "MEYCIQDF728NgEqz+WQIJbzy741TEBqKjasQ9TbpEEOQahTgYQIhAIJTpCpkp5opLTipKoL1yFxnb2EHYn/lJ/EokNGg1jGf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 650519}, "main": "./dist/cjs/index.js", "type": "module", "types": "./dist/cjs/index.d.ts", "module": "./dist/esm/index.js", "browser": {"./dist/cjs/index.js": "./dist/cjs-browser/index.js", "./dist/esm/index.js": "./dist/esm-browser/index.js"}, "exports": {".": {"node": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}, "browser": {"import": "./dist/esm-browser/index.js", "require": "./dist/cjs-browser/index.js"}, "default": "./dist/esm-browser/index.js"}, "./package.json": "./package.json"}, "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "gitHead": "36f2369af6479a46db1eb4fdffb73cbbf6108cf4", "scripts": {"md": "runmd --watch --output=README.md README_js.md", "docs": "npm run build && npx runmd --output=README.md README_js.md", "lint": "npm run eslint:check && npm run prettier:check", "test": "node --test --enable-source-maps dist/esm/test/*.js", "build": "./scripts/build.sh", "prepack": "npm run build", "prepare": "husky", "pretest": "npm run build", "release": "standard-version --no-verify", "docs:diff": "npm run docs && git diff --quiet README.md", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "eslint:fix": "eslint --fix src/ test/ examples/ *.[jt]s", "test:watch": "node --test --enable-source-maps --watch dist/esm/test/*.js", "build:watch": "tsc --watch -p tsconfig.esm.json", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "eslint:check": "eslint src/ test/ examples/ *.[jt]s", "pretest:node": "npm run build", "prettier:fix": "prettier --write .", "test:browser": "wdio run ./wdio.conf.js", "prettier:check": "prettier --check .", "test:benchmark": "cd examples/benchmark && HUSKY=0 npm install && npm test", "pretest:browser": "optional-dev-dependency && npm run build && npm-run-all --parallel examples:browser:**", "pretest:benchmark": "npm run build", "examples:node:jest:test": "cd examples/node-jest && npm install && npm test", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build"}, "_npmUser": {"name": "broofa", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/uuidjs/uuid.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "RFC9562 UUIDs", "directories": {}, "lint-staged": {"*": ["prettier --no-error-on-unmatched-pattern --write"], "*.{js,jsx}": ["eslint --no-error-on-unmatched-pattern --fix"]}, "sideEffects": false, "_nodeVersion": "20.18.0", "_hasShrinkwrap": false, "packageManager": "npm@10.9.0", "devDependencies": {"jest": "29.7.0", "husky": "9.1.6", "runmd": "1.3.9", "eslint": "9.13.0", "globals": "15.11.0", "prettier": "3.3.3", "@eslint/js": "9.13.0", "typescript": "5.6.3", "bundlewatch": "0.4.0", "lint-staged": "15.2.10", "neostandard": "0.11.7", "npm-run-all": "4.1.5", "release-please": "16.14.3", "@commitlint/cli": "19.5.0", "standard-version": "9.5.0", "@types/eslint__js": "8.42.3", "typescript-eslint": "8.11.0", "@babel/eslint-parser": "7.25.9", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "optional-dev-dependency": "2.0.1", "@commitlint/config-conventional": "19.5.0"}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "_npmOperationalInternal": {"tmp": "tmp/uuid_11.0.2_1730136431873_0.18215081892152507", "host": "s3://npm-registry-packages"}, "optionalDevDependencies": {"@wdio/cli": "9.2.1", "@wdio/local-runner": "9.2.1", "@wdio/spec-reporter": "9.1.3", "@wdio/jasmine-framework": "9.2.1", "@wdio/browserstack-service": "9.2.1", "@wdio/static-server-service": "9.1.3"}}, "11.0.3": {"name": "uuid", "version": "11.0.3", "keywords": ["uuid", "guid", "rfc4122", "rfc9562"], "license": "MIT", "_id": "uuid@11.0.3", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/uuidjs/uuid#readme", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "bin": {"uuid": "dist/esm/bin/uuid"}, "dist": {"shasum": "248451cac9d1a4a4128033e765d137e2b2c49a3d", "tarball": "https://registry.npmjs.org/uuid/-/uuid-11.0.3.tgz", "fileCount": 280, "integrity": "sha512-d0z310fCWv5dJwnX1Y/MncBAqGMKEzlBb1AOf7z9K8ALnd0utBX/msg/fA0+sbyN1ihbMsLhrBlnl1ak7Wa0rg==", "signatures": [{"sig": "MEUCIQDlhgS+bpevg+LIumEE6aJc+nlUQbPIQoXIIaxgS1W0FgIgaiuAjF9FIkiT1PKAYtCESbbz2IsTcMH2F98bsW4F/PE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 293890}, "main": "./dist/cjs/index.js", "type": "module", "types": "./dist/cjs/index.d.ts", "module": "./dist/esm/index.js", "browser": {"./dist/cjs/index.js": "./dist/cjs-browser/index.js", "./dist/esm/index.js": "./dist/esm-browser/index.js"}, "exports": {".": {"node": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}, "browser": {"import": "./dist/esm-browser/index.js", "require": "./dist/cjs-browser/index.js"}, "default": "./dist/esm-browser/index.js"}, "./package.json": "./package.json"}, "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "gitHead": "1370497eecb5c4a570da3d76aa1b47b86448470f", "scripts": {"md": "runmd --watch --output=README.md README_js.md", "docs": "npm run build && npx runmd --output=README.md README_js.md", "lint": "npm run eslint:check && npm run prettier:check", "test": "node --test --enable-source-maps dist/esm/test/*.js", "build": "./scripts/build.sh", "prepack": "npm run build", "prepare": "husky", "pretest": "npm run build", "release": "standard-version --no-verify", "docs:diff": "npm run docs && git diff --quiet README.md", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "eslint:fix": "eslint --fix src/ test/ examples/ *.[jt]s", "test:watch": "node --test --enable-source-maps --watch dist/esm/test/*.js", "build:watch": "tsc --watch -p tsconfig.esm.json", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "eslint:check": "eslint src/ test/ examples/ *.[jt]s", "pretest:node": "npm run build", "prettier:fix": "prettier --write .", "test:browser": "wdio run ./wdio.conf.js", "prettier:check": "prettier --check .", "test:benchmark": "cd examples/benchmark && HUSKY=0 npm install && npm test", "pretest:browser": "./scripts/iodd && npm run build && npm-run-all --parallel examples:browser:**", "pretest:benchmark": "npm run build", "examples:node:jest:test": "cd examples/node-jest && npm install && npm test", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build"}, "_npmUser": {"name": "broofa", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/uuidjs/uuid.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "RFC9562 UUIDs", "directories": {}, "lint-staged": {"*": ["prettier --no-error-on-unmatched-pattern --write"], "*.{js,jsx}": ["eslint --no-error-on-unmatched-pattern --fix"]}, "sideEffects": false, "_nodeVersion": "20.18.0", "_hasShrinkwrap": false, "packageManager": "npm@10.9.0", "devDependencies": {"jest": "29.7.0", "husky": "9.1.6", "runmd": "1.3.9", "eslint": "9.13.0", "globals": "15.11.0", "prettier": "3.3.3", "commander": "12.1.0", "@eslint/js": "9.13.0", "typescript": "5.6.3", "bundlewatch": "0.4.0", "lint-staged": "15.2.10", "neostandard": "0.11.7", "npm-run-all": "4.1.5", "release-please": "16.14.3", "@commitlint/cli": "19.5.0", "standard-version": "9.5.0", "@types/eslint__js": "8.42.3", "typescript-eslint": "8.11.0", "@babel/eslint-parser": "7.25.9", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "@commitlint/config-conventional": "19.5.0"}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "_npmOperationalInternal": {"tmp": "tmp/uuid_11.0.3_1731273954060_0.9488239716412279", "host": "s3://npm-registry-packages"}, "optionalDevDependencies": {"@wdio/cli": "9.2.1", "@wdio/local-runner": "9.2.1", "@wdio/spec-reporter": "9.1.3", "@wdio/jasmine-framework": "9.2.1", "@wdio/browserstack-service": "9.2.1", "@wdio/static-server-service": "9.1.3"}}, "11.0.4": {"name": "uuid", "version": "11.0.4", "keywords": ["uuid", "guid", "rfc4122", "rfc9562"], "license": "MIT", "_id": "uuid@11.0.4", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/uuidjs/uuid#readme", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "bin": {"uuid": "dist/esm/bin/uuid"}, "dist": {"shasum": "37943977894ef806d2919a7ca3f89d6e23c60bac", "tarball": "https://registry.npmjs.org/uuid/-/uuid-11.0.4.tgz", "fileCount": 190, "integrity": "sha512-IzL6VtTTYcAhA/oghbFJ1Dkmqev+FpQWnCBaKq/gUluLxliWvO8DPFWfIviRmYbtaavtSQe4WBL++rFjdcGWEg==", "signatures": [{"sig": "MEYCIQD73ZGLsRD8FEQs09uxY7bdiLKinaBl8TpN1QaBqDH6ywIhANBgX8TG4FpbgQK3LTRin7rhkx/XWDeFbxeetZ5P2DXC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 131611}, "main": "./dist/cjs/index.js", "type": "module", "types": "./dist/cjs/index.d.ts", "module": "./dist/esm/index.js", "browser": {"./dist/cjs/index.js": "./dist/cjs-browser/index.js", "./dist/esm/index.js": "./dist/esm-browser/index.js"}, "exports": {".": {"node": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}, "browser": {"import": "./dist/esm-browser/index.js", "require": "./dist/cjs-browser/index.js"}, "default": "./dist/esm-browser/index.js"}, "./package.json": "./package.json"}, "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "gitHead": "050cd5b9df5aa73097a1677b9e7c3482eb4367fc", "scripts": {"md": "runmd --watch --output=README.md README_js.md", "docs": "npm run build && npx runmd --output=README.md README_js.md", "lint": "npm run eslint:check && npm run prettier:check", "test": "node --test --enable-source-maps dist/esm/test/*.js", "build": "./scripts/build.sh", "prepack": "npm run build", "prepare": "husky", "pretest": "npm run build", "release": "standard-version --no-verify", "docs:diff": "npm run docs && git diff --quiet README.md", "test:node": "npm-run-all --parallel examples:node:**", "test:pack": "./scripts/testpack.sh", "eslint:fix": "eslint --fix src/ test/ examples/ *.[jt]s", "test:watch": "node --test --enable-source-maps --watch dist/esm/test/*.js", "build:watch": "tsc --watch -p tsconfig.esm.json", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "eslint:check": "eslint src/ test/ examples/ *.[jt]s", "pretest:node": "npm run build", "prettier:fix": "prettier --write .", "test:browser": "wdio run ./wdio.conf.js", "prettier:check": "prettier --check .", "test:benchmark": "cd examples/benchmark && HUSKY=0 npm install && npm test", "pretest:browser": "./scripts/iodd && npm run build && npm-run-all --parallel examples:browser:**", "pretest:benchmark": "npm run build", "examples:node:jest:test": "cd examples/node-jest && npm install && npm test", "examples:node:commonjs:test": "cd examples/node-commonjs && npm install && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm install && npm test", "examples:browser:rollup:build": "cd examples/browser-rollup && npm install && npm run build", "examples:browser:webpack:build": "cd examples/browser-webpack && npm install && npm run build"}, "_npmUser": {"name": "broofa", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/uuidjs/uuid.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "RFC9562 UUIDs", "directories": {}, "lint-staged": {"*": ["prettier --no-error-on-unmatched-pattern --write"], "*.{js,jsx}": ["eslint --no-error-on-unmatched-pattern --fix"]}, "sideEffects": false, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "packageManager": "npm@11.0.0", "devDependencies": {"jest": "29.7.0", "husky": "9.1.7", "runmd": "1.4.1", "eslint": "9.17.0", "globals": "15.14.0", "prettier": "3.4.2", "commander": "12.1.0", "@eslint/js": "9.17.0", "typescript": "5.7.2", "bundlewatch": "0.4.0", "lint-staged": "15.2.11", "neostandard": "0.12.0", "npm-run-all": "4.1.5", "release-please": "16.15.0", "@commitlint/cli": "19.6.1", "standard-version": "9.5.0", "@types/eslint__js": "8.42.3", "typescript-eslint": "8.18.2", "@babel/eslint-parser": "7.25.9", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "@commitlint/config-conventional": "19.6.0"}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "_npmOperationalInternal": {"tmp": "tmp/uuid_11.0.4_1736090212294_0.85428434872553", "host": "s3://npm-registry-packages-npm-production"}, "optionalDevDependencies": {"@wdio/cli": "9.2.1", "@wdio/local-runner": "9.2.1", "@wdio/spec-reporter": "9.1.3", "@wdio/jasmine-framework": "9.2.1", "@wdio/browserstack-service": "9.2.1", "@wdio/static-server-service": "9.1.3"}}, "11.0.5": {"name": "uuid", "version": "11.0.5", "keywords": ["uuid", "guid", "rfc4122", "rfc9562"], "license": "MIT", "_id": "uuid@11.0.5", "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/uuidjs/uuid#readme", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "bin": {"uuid": "dist/esm/bin/uuid"}, "dist": {"shasum": "07b46bdfa6310c92c3fb3953a8720f170427fc62", "tarball": "https://registry.npmjs.org/uuid/-/uuid-11.0.5.tgz", "fileCount": 190, "integrity": "sha512-508e6IcKLrhxKdBbcA2b4KQZlLVp2+J5UwQ6F7Drckkc5N9ZJwFa4TgWtsww9UG8fGHbm6gbV19TdM5pQ4GaIA==", "signatures": [{"sig": "MEUCIQDlJZBK51EKTUbNwOmydZJHMg+rMDbwD5oTQxWZu4nXTAIgdhSo9LABv0rnkX14aqTWCnDrPGbUAFacYTf91fkNuVs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 131959}, "main": "./dist/cjs/index.js", "type": "module", "types": "./dist/cjs/index.d.ts", "module": "./dist/esm/index.js", "browser": {"./dist/cjs/index.js": "./dist/cjs-browser/index.js", "./dist/esm/index.js": "./dist/esm-browser/index.js"}, "exports": {".": {"node": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}, "browser": {"import": "./dist/esm-browser/index.js", "require": "./dist/cjs-browser/index.js"}, "default": "./dist/esm-browser/index.js"}, "./package.json": "./package.json"}, "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "gitHead": "46ada3cbc4acdc907dd6924eaedcc2c53dc6095a", "scripts": {"md": "runmd --watch --output=README.md README_js.md", "docs": "npm run build && npx runmd --output=README.md README_js.md", "lint": "npm run eslint:check && npm run prettier:check", "test": "node --test --enable-source-maps dist/esm/test/*.js", "build": "./scripts/build.sh", "prepack": "npm run build -- --no-pack", "prepare": "husky", "pretest": "npm run build", "release": "standard-version --no-verify", "docs:diff": "npm run docs && git diff --quiet README.md", "test:node": "npm-run-all --parallel examples:node:**", "eslint:fix": "eslint --fix src/ test/ examples/ *.[jt]s", "test:watch": "node --test --enable-source-maps --watch dist/esm/test/*.js", "build:watch": "tsc --watch -p tsconfig.esm.json", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "eslint:check": "eslint src/ test/ examples/ *.[jt]s", "pretest:node": "npm run build", "prettier:fix": "prettier --write .", "test:browser": "wdio run ./wdio.conf.js", "prepublishOnly": "npm run build", "prettier:check": "prettier --check .", "test:benchmark": "cd examples/benchmark && npm test", "pretest:browser": "./scripts/iodd && npm run build && npm-run-all --parallel examples:browser:**", "pretest:benchmark": "npm run build", "examples:node:jest:test": "cd examples/node-jest && npm test", "examples:node:commonjs:test": "cd examples/node-commonjs && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm test", "examples:browser:rollup:build": "cd examples/browser-rollup && npm run build", "examples:node:typescript:test": "cd examples/typescript && npm test", "examples:browser:webpack:build": "cd examples/browser-webpack && npm run build"}, "_npmUser": {"name": "broofa", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/uuidjs/uuid.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "RFC9562 UUIDs", "directories": {}, "lint-staged": {"*": ["prettier --no-error-on-unmatched-pattern --write"], "*.{js,jsx}": ["eslint --no-error-on-unmatched-pattern --fix"]}, "sideEffects": false, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "packageManager": "npm@11.0.0", "devDependencies": {"jest": "29.7.0", "husky": "9.1.7", "runmd": "1.4.1", "eslint": "9.17.0", "globals": "15.14.0", "prettier": "3.4.2", "commander": "12.1.0", "@eslint/js": "9.17.0", "typescript": "5.0.4", "bundlewatch": "0.4.0", "lint-staged": "15.2.11", "neostandard": "0.12.0", "npm-run-all": "4.1.5", "release-please": "16.15.0", "@commitlint/cli": "19.6.1", "standard-version": "9.5.0", "@types/eslint__js": "8.42.3", "typescript-eslint": "8.18.2", "@babel/eslint-parser": "7.25.9", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "@commitlint/config-conventional": "19.6.0"}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "_npmOperationalInternal": {"tmp": "tmp/uuid_11.0.5_1736462418706_0.8467112808117361", "host": "s3://npm-registry-packages-npm-production"}, "optionalDevDependencies": {"@wdio/cli": "9.2.1", "@wdio/local-runner": "9.2.1", "@wdio/spec-reporter": "9.1.3", "@wdio/jasmine-framework": "9.2.1", "@wdio/browserstack-service": "9.2.1", "@wdio/static-server-service": "9.1.3"}}, "11.1.0": {"name": "uuid", "version": "11.1.0", "description": "RFC9562 UUIDs", "type": "module", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "commitlint": {"extends": ["@commitlint/config-conventional"]}, "keywords": ["uuid", "guid", "rfc4122", "rfc9562"], "license": "MIT", "bin": {"uuid": "dist/esm/bin/uuid"}, "sideEffects": false, "main": "./dist/cjs/index.js", "exports": {".": {"node": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}, "browser": {"import": "./dist/esm-browser/index.js", "require": "./dist/cjs-browser/index.js"}, "default": "./dist/esm-browser/index.js"}, "./package.json": "./package.json"}, "module": "./dist/esm/index.js", "browser": {"./dist/esm/index.js": "./dist/esm-browser/index.js", "./dist/cjs/index.js": "./dist/cjs-browser/index.js"}, "devDependencies": {"@babel/eslint-parser": "7.25.9", "@commitlint/cli": "19.6.1", "@commitlint/config-conventional": "19.6.0", "@eslint/js": "9.17.0", "@types/eslint__js": "8.42.3", "bundlewatch": "0.4.0", "commander": "12.1.0", "eslint": "9.17.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "globals": "15.14.0", "husky": "9.1.7", "jest": "29.7.0", "lint-staged": "15.2.11", "neostandard": "0.12.0", "npm-run-all": "4.1.5", "prettier": "3.4.2", "release-please": "16.15.0", "runmd": "1.4.1", "standard-version": "9.5.0", "typescript": "5.0.4", "typescript-eslint": "8.18.2"}, "optionalDevDependencies": {"@wdio/browserstack-service": "9.2.1", "@wdio/cli": "9.2.1", "@wdio/jasmine-framework": "9.2.1", "@wdio/local-runner": "9.2.1", "@wdio/spec-reporter": "9.1.3", "@wdio/static-server-service": "9.1.3"}, "scripts": {"build": "./scripts/build.sh", "build:watch": "tsc --watch -p tsconfig.esm.json", "bundlewatch": "npm run pretest:browser && bundlewatch --config bundlewatch.config.json", "docs:diff": "npm run docs && git diff --quiet README.md", "docs": "npm run build && npx runmd --output=README.md README_js.md", "eslint:check": "eslint src/ test/ examples/ *.[jt]s", "eslint:fix": "eslint --fix src/ test/ examples/ *.[jt]s", "examples:browser:rollup:build": "cd examples/browser-rollup && npm run build", "examples:browser:webpack:build": "cd examples/browser-webpack && npm run build", "examples:node:commonjs:test": "cd examples/node-commonjs && npm test", "examples:node:esmodules:test": "cd examples/node-esmodules && npm test", "examples:node:jest:test": "cd examples/node-jest && npm test", "examples:node:typescript:test": "cd examples/typescript && npm test", "lint": "npm run eslint:check && npm run prettier:check", "md": "runmd --watch --output=README.md README_js.md", "prepack": "npm run build -- --no-pack", "prepare": "husky", "prepublishOnly": "npm run build", "pretest:benchmark": "npm run build", "pretest:browser": "./scripts/iodd && npm run build && npm-run-all --parallel examples:browser:**", "pretest:node": "npm run build", "pretest": "npm run build", "prettier:check": "prettier --check .", "prettier:fix": "prettier --write .", "release": "standard-version --no-verify", "test:benchmark": "cd examples/benchmark && npm test", "test:browser": "wdio run ./wdio.conf.js", "test:node": "npm-run-all --parallel examples:node:**", "test:watch": "node --test --enable-source-maps --watch dist/esm/test/*.js", "test": "node --test --enable-source-maps dist/esm/test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/uuidjs/uuid.git"}, "lint-staged": {"*": ["prettier --no-error-on-unmatched-pattern --write"], "*.{js,jsx}": ["eslint --no-error-on-unmatched-pattern --fix"]}, "standard-version": {"scripts": {"postchangelog": "prettier --write CHANGELOG.md"}}, "packageManager": "npm@11.0.0", "_id": "uuid@11.1.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "3d1eba06be81fb3a02e16d06ef6fe959c9bb5c5c", "types": "./dist/cjs/index.d.ts", "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "homepage": "https://github.com/uuidjs/uuid#readme", "_nodeVersion": "20.18.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-0/A9rDy9P7cJ+8w1c9WD9V//9Wj15Ce2MPz8Ri6032usz+NfePxx5AcN3bN+r6ZL6jEo066/yNYB3tn4pQEx+A==", "shasum": "9549028be1753bb934fc96e2bca09bb4105ae912", "tarball": "https://registry.npmjs.org/uuid/-/uuid-11.1.0.tgz", "fileCount": 190, "unpackedSize": 132894, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIAwmSSeNa1LZOnX1xHZTmKjAbNtJrVs4X6AGfFtdU6TgAiEA0QQOK2FF4KY5Qv7/xBIx19YeJ8hWbpvdw1M3Q8YSROw="}]}, "_npmUser": {"name": "broofa", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/uuid_11.1.0_1739988971419_0.4427353848167064"}, "_hasShrinkwrap": false}}, "time": {"created": "2011-03-31T08:12:51.801Z", "modified": "2025-02-19T18:16:11.787Z", "0.0.1": "2011-03-31T08:12:51.801Z", "0.0.2": "2011-03-31T08:12:51.801Z", "1.4.0": "2013-02-19T22:28:10.376Z", "1.4.1": "2013-03-14T05:25:04.735Z", "1.4.2": "2014-09-25T09:03:34.083Z", "2.0.0": "2014-09-29T09:03:24.612Z", "2.0.1": "2014-09-29T09:16:55.228Z", "2.0.2": "2016-04-13T07:37:23.788Z", "2.0.3": "2016-09-18T21:03:38.127Z", "3.0.0": "2016-11-18T05:55:37.542Z", "3.0.1": "2016-11-29T07:18:07.016Z", "3.1.0": "2017-06-16T17:54:51.877Z", "3.2.0": "2018-01-16T14:44:50.639Z", "3.2.1": "2018-01-16T17:44:57.871Z", "3.3.0": "2018-06-26T13:23:39.426Z", "3.3.2": "2018-06-28T21:29:01.853Z", "3.3.3": "2019-08-19T13:26:25.072Z", "3.4.0": "2020-01-16T21:05:38.628Z", "7.0.0-beta.0": "2020-02-17T11:25:25.965Z", "7.0.0": "2020-02-24T13:23:46.712Z", "7.0.1": "2020-02-25T20:26:35.800Z", "7.0.2-beta.0": "2020-03-02T20:31:08.402Z", "7.0.2": "2020-03-04T12:49:00.666Z", "7.0.3": "2020-03-31T19:41:24.836Z", "8.0.0-beta.0": "2020-04-29T20:21:52.783Z", "8.0.0": "2020-04-29T20:42:26.823Z", "8.1.0": "2020-05-20T18:52:38.248Z", "8.2.0-beta.0": "2020-06-23T07:38:42.240Z", "8.2.0": "2020-06-23T20:56:14.285Z", "8.3.0-beta.0": "2020-07-22T22:28:57.880Z", "8.3.0": "2020-07-27T19:01:36.217Z", "8.3.1": "2020-10-04T15:46:46.276Z", "8.3.2-beta.0": "2020-11-21T13:10:15.605Z", "8.3.2": "2020-12-08T20:38:36.233Z", "9.0.0-beta.0": "2022-08-05T10:12:36.295Z", "9.0.0": "2022-09-05T20:03:54.869Z", "9.0.1": "2023-09-12T08:56:35.205Z", "10.0.0": "2024-06-09T13:42:04.366Z", "11.0.0-0": "2024-09-05T21:08:26.805Z", "11.0.0": "2024-10-27T14:35:13.696Z", "11.0.1": "2024-10-27T21:40:24.728Z", "11.0.2": "2024-10-28T17:27:12.069Z", "11.0.3": "2024-11-10T21:25:54.278Z", "11.0.4": "2025-01-05T15:16:52.451Z", "11.0.5": "2025-01-09T22:40:18.873Z", "11.1.0": "2025-02-19T18:16:11.602Z"}, "bugs": {"url": "https://github.com/uuidjs/uuid/issues"}, "license": "MIT", "homepage": "https://github.com/uuidjs/uuid#readme", "keywords": ["uuid", "guid", "rfc4122", "rfc9562"], "repository": {"type": "git", "url": "git+https://github.com/uuidjs/uuid.git"}, "description": "RFC9562 UUIDs", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "ctavan", "email": "<EMAIL>"}], "readme": "<!--\n  -- This file is auto-generated from README_js.md. Changes should be made there.\n  -->\n\n# uuid [![CI](https://github.com/uuidjs/uuid/workflows/CI/badge.svg)](https://github.com/uuidjs/uuid/actions?query=workflow%3ACI) [![Browser](https://github.com/uuidjs/uuid/workflows/Browser/badge.svg)](https://github.com/uuidjs/uuid/actions/workflows/browser.yml)\n\nFor the creation of [RFC9562](https://www.rfc-editor.org/rfc/rfc9562.html) (formerly [RFC4122](https://www.rfc-editor.org/rfc/rfc4122.html)) UUIDs\n\n- **Complete** - Support for all RFC9562 UUID versions\n- **Cross-platform** - Support for...\n  - ESM & Common JS\n  - [Typescript](#support)\n  - [Chrome, Safari, Firefox, and Edge](#support)\n  - [NodeJS](#support)\n  - [React Native / Expo](#react-native--expo)\n- **Secure** - Uses modern `crypto` API for random values\n- **Compact** - Zero-dependency, [tree-shakable](https://developer.mozilla.org/en-US/docs/Glossary/Tree_shaking)\n- **CLI** - [`uuid` command line](#command-line) utility\n\n<!-- prettier-ignore -->\n> [!NOTE]\n> `uuid@11` is now available:  See the [CHANGELOG](./CHANGELOG.md) for details. TL;DR:\n> * TypeScript support is now included  (remove `@types/uuid` from your dependencies)\n> * Subtle changes to how the `options` arg is interpreted for `v1()`, `v6()`, and `v7()`. [See details](#options-handling-for-timestamp-uuids)\n> * Binary UUIDs are now `Uint8Array`s.  (May impact callers of `parse()`, `stringify()`,  or that pass an `option#buf` argument to `v1()`-`v7()`.)\n\n## Quickstart\n\n**1. Install**\n\n```shell\nnpm install uuid\n```\n\n**2. Create a UUID**\n\nESM-syntax (must use named exports):\n\n```javascript\nimport { v4 as uuidv4 } from 'uuid';\nuuidv4(); // ⇨ '9b1deb4d-3b7d-4bad-9bdd-2b0d7b3dcb6d'\n```\n\n... CommonJS:\n\n```javascript\nconst { v4: uuidv4 } = require('uuid');\nuuidv4(); // ⇨ '1b9d6bcd-bbfd-4b2d-9b5d-ab8dfbbd4bed'\n```\n\nFor timestamp UUIDs, namespace UUIDs, and other options read on ...\n\n## API Summary\n\n|  |  |  |\n| --- | --- | --- |\n| [`uuid.NIL`](#uuidnil) | The nil UUID string (all zeros) | New in `uuid@8.3` |\n| [`uuid.MAX`](#uuidmax) | The max UUID string (all ones) | New in `uuid@9.1` |\n| [`uuid.parse()`](#uuidparsestr) | Convert UUID string to array of bytes | New in `uuid@8.3` |\n| [`uuid.stringify()`](#uuidstringifyarr-offset) | Convert array of bytes to UUID string | New in `uuid@8.3` |\n| [`uuid.v1()`](#uuidv1options-buffer-offset) | Create a version 1 (timestamp) UUID |  |\n| [`uuid.v1ToV6()`](#uuidv1tov6uuid) | Create a version 6 UUID from a version 1 UUID | New in `uuid@10` |\n| [`uuid.v3()`](#uuidv3name-namespace-buffer-offset) | Create a version 3 (namespace w/ MD5) UUID |  |\n| [`uuid.v4()`](#uuidv4options-buffer-offset) | Create a version 4 (random) UUID |  |\n| [`uuid.v5()`](#uuidv5name-namespace-buffer-offset) | Create a version 5 (namespace w/ SHA-1) UUID |  |\n| [`uuid.v6()`](#uuidv6options-buffer-offset) | Create a version 6 (timestamp, reordered) UUID | New in `uuid@10` |\n| [`uuid.v6ToV1()`](#uuidv6tov1uuid) | Create a version 1 UUID from a version 6 UUID | New in `uuid@10` |\n| [`uuid.v7()`](#uuidv7options-buffer-offset) | Create a version 7 (Unix Epoch time-based) UUID | New in `uuid@10` |\n| ~~[`uuid.v8()`](#uuidv8)~~ | \"Intentionally left blank\" |  |\n| [`uuid.validate()`](#uuidvalidatestr) | Test a string to see if it is a valid UUID | New in `uuid@8.3` |\n| [`uuid.version()`](#uuidversionstr) | Detect RFC version of a UUID | New in `uuid@8.3` |\n\n## API\n\n### uuid.NIL\n\nThe nil UUID string (all zeros).\n\nExample:\n\n```javascript\nimport { NIL as NIL_UUID } from 'uuid';\n\nNIL_UUID; // ⇨ '00000000-0000-0000-0000-000000000000'\n```\n\n### uuid.MAX\n\nThe max UUID string (all ones).\n\nExample:\n\n```javascript\nimport { MAX as MAX_UUID } from 'uuid';\n\nMAX_UUID; // ⇨ 'ffffffff-ffff-ffff-ffff-ffffffffffff'\n```\n\n### uuid.parse(str)\n\nConvert UUID string to array of bytes\n\n|           |                                          |\n| --------- | ---------------------------------------- |\n| `str`     | A valid UUID `String`                    |\n| _returns_ | `Uint8Array[16]`                         |\n| _throws_  | `TypeError` if `str` is not a valid UUID |\n\n<!-- prettier-ignore -->\n> [!NOTE]\n> Ordering of values in the byte arrays used by `parse()` and `stringify()` follows the left &Rarr; right order of hex-pairs in UUID strings. As shown in the example below.\n\nExample:\n\n```javascript\nimport { parse as uuidParse } from 'uuid';\n\n// Parse a UUID\nuuidParse('6ec0bd7f-11c0-43da-975e-2a8ad9ebae0b'); // ⇨\n// Uint8Array(16) [\n//   110, 192, 189, 127,  17,\n//   192,  67, 218, 151,  94,\n//    42, 138, 217, 235, 174,\n//    11\n// ]\n```\n\n### uuid.stringify(arr[, offset])\n\nConvert array of bytes to UUID string\n\n|                |                                                                              |\n| -------------- | ---------------------------------------------------------------------------- |\n| `arr`          | `Array`-like collection of 16 values (starting from `offset`) between 0-255. |\n| [`offset` = 0] | `Number` Starting index in the Array                                         |\n| _returns_      | `String`                                                                     |\n| _throws_       | `TypeError` if a valid UUID string cannot be generated                       |\n\n<!-- prettier-ignore -->\n> [!NOTE]\n> Ordering of values in the byte arrays used by `parse()` and `stringify()` follows the left &Rarr; right order of hex-pairs in UUID strings. As shown in the example below.\n\nExample:\n\n```javascript\nimport { stringify as uuidStringify } from 'uuid';\n\nconst uuidBytes = Uint8Array.of(\n  0x6e,\n  0xc0,\n  0xbd,\n  0x7f,\n  0x11,\n  0xc0,\n  0x43,\n  0xda,\n  0x97,\n  0x5e,\n  0x2a,\n  0x8a,\n  0xd9,\n  0xeb,\n  0xae,\n  0x0b\n);\n\nuuidStringify(uuidBytes); // ⇨ '6ec0bd7f-11c0-43da-975e-2a8ad9ebae0b'\n```\n\n### uuid.v1([options[, buffer[, offset]]])\n\nCreate an RFC version 1 (timestamp) UUID\n\n|  |  |\n| --- | --- |\n| [`options`] | `Object` with one or more of the following properties: |\n| [`options.node = (random)` ] | RFC \"node\" field as an `Array[6]` of byte values (per 4.1.6) |\n| [`options.clockseq = (random)`] | RFC \"clock sequence\" as a `Number` between 0 - 0x3fff |\n| [`options.msecs = (current time)`] | RFC \"timestamp\" field (`Number` of milliseconds, unix epoch) |\n| [`options.nsecs = 0`] | RFC \"timestamp\" field (`Number` of nanoseconds to add to `msecs`, should be 0-10,000) |\n| [`options.random = (random)`] | `Array` of 16 random bytes (0-255) used to generate other fields, above |\n| [`options.rng`] | Alternative to `options.random`, a `Function` that returns an `Array` of 16 random bytes (0-255) |\n| [`buffer`] | `Uint8Array` or `Uint8Array` subtype (e.g. Node.js `Buffer`). If provided, binary UUID is written into the array, starting at `offset` |\n| [`offset` = 0] | `Number` Index to start writing UUID bytes in `buffer` |\n| _returns_ | UUID `String` if no `buffer` is specified, otherwise returns `buffer` |\n| _throws_ | `Error` if more than 10M UUIDs/sec are requested |\n\n<!-- prettier-ignore -->\n> [!NOTE]\n> The default [node id](https://datatracker.ietf.org/doc/html/rfc9562#section-5.1) (the last 12 digits in the UUID) is generated once, randomly, on process startup, and then remains unchanged for the duration of the process.\n\n<!-- prettier-ignore -->\n> [!NOTE]\n> `options.random` and `options.rng` are only meaningful on the very first call to `v1()`, where they may be passed to initialize the internal `node` and `clockseq` fields.\n\nExample:\n\n```javascript\nimport { v1 as uuidv1 } from 'uuid';\n\nuuidv1(); // ⇨ '2c5ea4c0-4067-11e9-9bdd-2b0d7b3dcb6d'\n```\n\nExample using `options`:\n\n```javascript\nimport { v1 as uuidv1 } from 'uuid';\n\nconst options = {\n  node: Uint8Array.of(0x01, 0x23, 0x45, 0x67, 0x89, 0xab),\n  clockseq: 0x1234,\n  msecs: new Date('2011-11-01').getTime(),\n  nsecs: 5678,\n};\nuuidv1(options); // ⇨ '710b962e-041c-11e1-9234-0123456789ab'\n```\n\n### uuid.v1ToV6(uuid)\n\nConvert a UUID from version 1 to version 6\n\n```javascript\nimport { v1ToV6 } from 'uuid';\n\nv1ToV6('92f62d9e-22c4-11ef-97e9-325096b39f47'); // ⇨ '1ef22c49-2f62-6d9e-97e9-325096b39f47'\n```\n\n### uuid.v3(name, namespace[, buffer[, offset]])\n\nCreate an RFC version 3 (namespace w/ MD5) UUID\n\nAPI is identical to `v5()`, but uses \"v3\" instead.\n\n<!-- prettier-ignore -->\n> [!IMPORTANT]\n> Per the RFC, \"_If backward compatibility is not an issue, SHA-1 [Version 5] is preferred_.\"\n\n### uuid.v4([options[, buffer[, offset]]])\n\nCreate an RFC version 4 (random) UUID\n\n|  |  |\n| --- | --- |\n| [`options`] | `Object` with one or more of the following properties: |\n| [`options.random`] | `Array` of 16 random bytes (0-255) |\n| [`options.rng`] | Alternative to `options.random`, a `Function` that returns an `Array` of 16 random bytes (0-255) |\n| [`buffer`] | `Uint8Array` or `Uint8Array` subtype (e.g. Node.js `Buffer`). If provided, binary UUID is written into the array, starting at `offset` |\n| [`offset` = 0] | `Number` Index to start writing UUID bytes in `buffer` |\n| _returns_ | UUID `String` if no `buffer` is specified, otherwise returns `buffer` |\n\nExample:\n\n```javascript\nimport { v4 as uuidv4 } from 'uuid';\n\nuuidv4(); // ⇨ '9b1deb4d-3b7d-4bad-9bdd-2b0d7b3dcb6d'\n```\n\nExample using predefined `random` values:\n\n```javascript\nimport { v4 as uuidv4 } from 'uuid';\n\nconst v4options = {\n  random: Uint8Array.of(\n    0x10,\n    0x91,\n    0x56,\n    0xbe,\n    0xc4,\n    0xfb,\n    0xc1,\n    0xea,\n    0x71,\n    0xb4,\n    0xef,\n    0xe1,\n    0x67,\n    0x1c,\n    0x58,\n    0x36\n  ),\n};\nuuidv4(v4options); // ⇨ '109156be-c4fb-41ea-b1b4-efe1671c5836'\n```\n\n### uuid.v5(name, namespace[, buffer[, offset]])\n\nCreate an RFC version 5 (namespace w/ SHA-1) UUID\n\n|  |  |\n| --- | --- |\n| `name` | `String \\| Array` |\n| `namespace` | `String \\| Array[16]` Namespace UUID |\n| [`buffer`] | `Uint8Array` or `Uint8Array` subtype (e.g. Node.js `Buffer`). If provided, binary UUID is written into the array, starting at `offset` |\n| [`offset` = 0] | `Number` Index to start writing UUID bytes in `buffer` |\n| _returns_ | UUID `String` if no `buffer` is specified, otherwise returns `buffer` |\n\n<!-- prettier-ignore -->\n> [!NOTE]\n> The RFC `DNS` and `URL` namespaces are available as `v5.DNS` and `v5.URL`.\n\nExample with custom namespace:\n\n```javascript\nimport { v5 as uuidv5 } from 'uuid';\n\n// Define a custom namespace.  Readers, create your own using something like\n// https://www.uuidgenerator.net/\nconst MY_NAMESPACE = '1b671a64-40d5-491e-99b0-da01ff1f3341';\n\nuuidv5('Hello, World!', MY_NAMESPACE); // ⇨ '630eb68f-e0fa-5ecc-887a-7c7a62614681'\n```\n\nExample with RFC `URL` namespace:\n\n```javascript\nimport { v5 as uuidv5 } from 'uuid';\n\nuuidv5('https://www.w3.org/', uuidv5.URL); // ⇨ 'c106a26a-21bb-5538-8bf2-57095d1976c1'\n```\n\n### uuid.v6([options[, buffer[, offset]]])\n\nCreate an RFC version 6 (timestamp, reordered) UUID\n\nThis method takes the same arguments as uuid.v1().\n\n```javascript\nimport { v6 as uuidv6 } from 'uuid';\n\nuuidv6(); // ⇨ '1e940672-c5ea-64c0-9b5d-ab8dfbbd4bed'\n```\n\nExample using `options`:\n\n```javascript\nimport { v6 as uuidv6 } from 'uuid';\n\nconst options = {\n  node: [0x01, 0x23, 0x45, 0x67, 0x89, 0xab],\n  clockseq: 0x1234,\n  msecs: new Date('2011-11-01').getTime(),\n  nsecs: 5678,\n};\nuuidv6(options); // ⇨ '1e1041c7-10b9-662e-9234-0123456789ab'\n```\n\n### uuid.v6ToV1(uuid)\n\nConvert a UUID from version 6 to version 1\n\n```javascript\nimport { v6ToV1 } from 'uuid';\n\nv6ToV1('1ef22c49-2f62-6d9e-97e9-325096b39f47'); // ⇨ '92f62d9e-22c4-11ef-97e9-325096b39f47'\n```\n\n### uuid.v7([options[, buffer[, offset]]])\n\nCreate an RFC version 7 (random) UUID\n\n|  |  |\n| --- | --- |\n| [`options`] | `Object` with one or more of the following properties: |\n| [`options.msecs = (current time)`] | RFC \"timestamp\" field (`Number` of milliseconds, unix epoch) |\n| [`options.random = (random)`] | `Array` of 16 random bytes (0-255) used to generate other fields, above |\n| [`options.rng`] | Alternative to `options.random`, a `Function` that returns an `Array` of 16 random bytes (0-255) |\n| [`options.seq = (random)`] | 32-bit sequence `Number` between 0 - 0xffffffff. This may be provided to help ensure uniqueness for UUIDs generated within the same millisecond time interval. Default = random value. |\n| [`buffer`] | `Uint8Array` or `Uint8Array` subtype (e.g. Node.js `Buffer`). If provided, binary UUID is written into the array, starting at `offset` |\n| [`offset` = 0] | `Number` Index to start writing UUID bytes in `buffer` |\n| _returns_ | UUID `String` if no `buffer` is specified, otherwise returns `buffer` |\n\nExample:\n\n```javascript\nimport { v7 as uuidv7 } from 'uuid';\n\nuuidv7(); // ⇨ '01695553-c90c-705a-b56d-778dfbbd4bed'\n```\n\n### ~~uuid.v8()~~\n\n**_\"Intentionally left blank\"_**\n\n<!-- prettier-ignore -->\n> [!NOTE]\n> Version 8 (experimental) UUIDs are \"[for experimental or vendor-specific use cases](https://www.rfc-editor.org/rfc/rfc9562.html#name-uuid-version-8)\".  The RFC does not define a creation algorithm for them, which is why this package does not offer a `v8()` method.  The `validate()` and `version()` methods do work with such UUIDs, however.\n\n### uuid.validate(str)\n\nTest a string to see if it is a valid UUID\n\n|           |                                                     |\n| --------- | --------------------------------------------------- |\n| `str`     | `String` to validate                                |\n| _returns_ | `true` if string is a valid UUID, `false` otherwise |\n\nExample:\n\n```javascript\nimport { validate as uuidValidate } from 'uuid';\n\nuuidValidate('not a UUID'); // ⇨ false\nuuidValidate('6ec0bd7f-11c0-43da-975e-2a8ad9ebae0b'); // ⇨ true\n```\n\nUsing `validate` and `version` together it is possible to do per-version validation, e.g. validate for only v4 UUIds.\n\n```javascript\nimport { version as uuidVersion } from 'uuid';\nimport { validate as uuidValidate } from 'uuid';\n\nfunction uuidValidateV4(uuid) {\n  return uuidValidate(uuid) && uuidVersion(uuid) === 4;\n}\n\nconst v1Uuid = 'd9428888-122b-11e1-b85c-61cd3cbb3210';\nconst v4Uuid = '109156be-c4fb-41ea-b1b4-efe1671c5836';\n\nuuidValidateV4(v4Uuid); // ⇨ true\nuuidValidateV4(v1Uuid); // ⇨ false\n```\n\n### uuid.version(str)\n\nDetect RFC version of a UUID\n\n|           |                                          |\n| --------- | ---------------------------------------- |\n| `str`     | A valid UUID `String`                    |\n| _returns_ | `Number` The RFC version of the UUID     |\n| _throws_  | `TypeError` if `str` is not a valid UUID |\n\nExample:\n\n```javascript\nimport { version as uuidVersion } from 'uuid';\n\nuuidVersion('45637ec4-c85f-11ea-87d0-0242ac130003'); // ⇨ 1\nuuidVersion('6ec0bd7f-11c0-43da-975e-2a8ad9ebae0b'); // ⇨ 4\n```\n\n<!-- prettier-ignore -->\n> [!NOTE]\n> This method returns `0` for the `NIL` UUID, and `15` for the `MAX` UUID.\n\n## Command Line\n\nUUIDs can be generated from the command line using `uuid`.\n\n```shell\n$ npx uuid\nddeb27fb-d9a0-4624-be4d-4615062daed4\n```\n\nThe default is to generate version 4 UUIDS, however the other versions are supported. Type `uuid --help` for details:\n\n```shell\n$ npx uuid --help\n\nUsage:\n  uuid\n  uuid v1\n  uuid v3 <name> <namespace uuid>\n  uuid v4\n  uuid v5 <name> <namespace uuid>\n  uuid v7\n  uuid --help\n\nNote: <namespace uuid> may be \"URL\" or \"DNS\" to use the corresponding UUIDs\ndefined by RFC9562\n```\n\n## `options` Handling for Timestamp UUIDs\n\nPrior to `uuid@11`, it was possible for `options` state to interfere with the internal state used to ensure uniqueness of timestamp-based UUIDs (the `v1()`, `v6()`, and `v7()` methods). Starting with `uuid@11`, this issue has been addressed by using the presence of the `options` argument as a flag to select between two possible behaviors:\n\n- Without `options`: Internal state is utilized to improve UUID uniqueness.\n- With `options`: Internal state is **NOT** used and, instead, appropriate defaults are applied as needed.\n\n## Support\n\n**Browsers**: `uuid` [builds are tested](/uuidjs/uuid/blob/main/wdio.conf.js) against the latest version of desktop Chrome, Safari, Firefox, and Edge. Mobile versions of these same browsers are expected to work but aren't currently tested.\n\n**Node**: `uuid` [builds are tested](https://github.com/uuidjs/uuid/blob/main/.github/workflows/ci.yml#L26-L27) against node ([LTS releases](https://github.com/nodejs/Release)), plus one prior. E.g. `node@18` is in maintainence mode, and `node@22` is the current LTS release. So `uuid` supports `node@16`-`node@22`.\n\n**Typescript**: TS versions released within the past two years are supported. [source](https://github.com/microsoft/TypeScript/issues/49088#issuecomment-2468723715)\n\n## Known issues\n\n<!-- This header is referenced as an anchor in src/rng-browser.ts -->\n\n### \"getRandomValues() not supported\"\n\nThis error occurs in environments where the standard [`crypto.getRandomValues()`](https://developer.mozilla.org/en-US/docs/Web/API/Crypto/getRandomValues) API is not supported. This issue can be resolved by adding an appropriate polyfill:\n\n#### React Native / Expo\n\n1. Install [`react-native-get-random-values`](https://github.com/LinusU/react-native-get-random-values#readme)\n1. Import it _before_ `uuid`. Since `uuid` might also appear as a transitive dependency of some other imports it's safest to just import `react-native-get-random-values` as the very first thing in your entry point:\n\n```javascript\nimport 'react-native-get-random-values';\nimport { v4 as uuidv4 } from 'uuid';\n```\n\n---\n\nMarkdown generated from [README_js.md](README_js.md) by <a href=\"https://github.com/broofa/runmd\"><image height=\"13\" src=\"https://camo.githubusercontent.com/5c7c603cd1e6a43370b0a5063d457e0dabb74cf317adc7baba183acb686ee8d0/687474703a2f2f692e696d6775722e636f6d2f634a4b6f3662552e706e67\" /></a>\n", "readmeFilename": "README.md", "users": {"po": true, "ash": true, "jk6": true, "luk": true, "tjg": true, "dwqs": true, "jtrh": true, "jueb": true, "keyn": true, "lych": true, "neo1": true, "smd4": true, "tztz": true, "vwal": true, "wayn": true, "xufz": true, "ysk8": true, "adamk": true, "arefm": true, "ashco": true, "bsara": true, "etsit": true, "fedor": true, "flozz": true, "holly": true, "i3fox": true, "kikna": true, "kvrao": true, "leapm": true, "leota": true, "metaa": true, "pospi": true, "r3nya": true, "rojo2": true, "segen": true, "tdevm": true, "xrush": true, "yikuo": true, "yswon": true, "456wyc": true, "akarem": true, "apopek": true, "bpatel": true, "broofa": true, "chilts": true, "ckaatz": true, "crwnvr": true, "daizch": true, "dkblay": true, "dtunes": true, "ijidau": true, "isayme": true, "itcorp": true, "kaufmo": true, "lestad": true, "matsgm": true, "mirkoj": true, "nhz.io": true, "nuwaio": true, "orkisz": true, "praxiq": true, "procom": true, "quafoo": true, "rumkin": true, "satoru": true, "shrike": true, "someok": true, "vcboom": true, "vjenks": true, "wangxb": true, "zhoutk": true, "ziflex": true, "zpoons": true, "antanst": true, "asaupup": true, "astesio": true, "atomgao": true, "biao166": true, "cblumer": true, "epdplus": true, "ezeikel": true, "ferrari": true, "flxa888": true, "gdibble": true, "gollojs": true, "gpuente": true, "guurgle": true, "jez9999": true, "jybleau": true, "kkho595": true, "laoshaw": true, "lex_nel": true, "lgl1993": true, "marcker": true, "nohomey": true, "palsson": true, "restuta": true, "rikstam": true, "rparris": true, "sachacr": true, "sammade": true, "sgvinci": true, "simonja": true, "tmypawa": true, "wenbing": true, "writech": true, "xfloops": true, "xtx1130": true, "yakumat": true, "zand3rs": true, "ahvonenj": true, "appleboy": true, "artjacob": true, "bapinney": true, "bart1208": true, "chaofeis": true, "danielye": true, "dzhou777": true, "faraoman": true, "freebird": true, "geooogle": true, "greatyou": true, "hecto932": true, "hugovila": true, "jaxelson": true, "jlagunas": true, "jlmorgan": true, "jonathas": true, "koulmomo": true, "krabello": true, "lifecube": true, "lionel86": true, "maxblock": true, "mhaidarh": true, "moimikey": true, "pddivine": true, "philipjc": true, "potentii": true, "ralphkay": true, "razor164": true, "rochejul": true, "shiva127": true, "shtylman": true, "somerayg": true, "techmuch": true, "tmurngon": true, "vchouhan": true, "wandyezj": true, "wfcookie": true, "wozhizui": true, "yash3492": true, "yashprit": true, "zhyq0826": true, "zuojiang": true, "abuelwafa": true, "alexcoady": true, "awhmandan": true, "bluelover": true, "caiofossa": true, "chrisyipw": true, "danharper": true, "davidrlee": true, "dheerajvs": true, "edwardxyt": true, "erincinci": true, "evanyeung": true, "gavaxiang": true, "guzgarcia": true, "haihepeng": true, "heartnett": true, "isenricho": true, "jakedalus": true, "jamiechoi": true, "jesusgoku": true, "max_devjs": true, "mikestaub": true, "nigel0913": true, "ninozhang": true, "nomemires": true, "papasavva": true, "reyronald": true, "sasquatch": true, "snowdream": true, "sternelee": true, "stretchgz": true, "sunkeysun": true, "thomask33": true, "tjfwalker": true, "tomgao365": true, "whathejoe": true, "wolfram77": true, "xxsnake28": true, "afollestad": true, "aitorllj93": true, "ashish.npm": true, "avivharuzi": true, "benburwell": true, "blackbunny": true, "cainwatson": true, "clarenceho": true, "cognivator": true, "cschmitz81": true, "esilva2902": true, "harumambur": true, "iainhallam": true, "ianpaschal": true, "jakecadams": true, "joelwallis": true, "johntbales": true, "juananto11": true, "junjiansyu": true, "justforuse": true, "kankungyip": true, "kavyababu7": true, "leon740727": true, "leonardorb": true, "luffy84217": true, "lwgojustgo": true, "monolithed": true, "nickleefly": true, "piecioshka": true, "qqqppp9998": true, "raycharles": true, "rocket0191": true, "selenasong": true, "shadowlong": true, "shipengyan": true, "sonhuytran": true, "sourcesoft": true, "thomas.han": true, "wenhsiaoyi": true, "winjeysong": true, "albertofdzm": true, "andfaulkner": true, "appsparkler": true, "cbetancourt": true, "davidnyhuis": true, "eserozvataf": true, "fearnbuster": true, "fengmiaosen": true, "flumpus-dev": true, "ganeshkbhat": true, "glektarssza": true, "he313572052": true, "icognivator": true, "jamesbedont": true, "jochemstoel": true, "kodekracker": true, "marloncouto": true, "maufrontier": true, "mlohscheidt": true, "monsterkodi": true, "octetstream": true, "reggiezhang": true, "scotchulous": true, "soulchainer": true, "swedendrift": true, "vparaskevas": true, "wangnan0610": true, "xinwangwang": true, "abhijitkalta": true, "brentonhouse": true, "gildasdubois": true, "grantcarthew": true, "huangshijuan": true, "infernocloud": true, "ivan.marquez": true, "jasonpearson": true, "justdomepaul": true, "leland-kwong": true, "marlberm2014": true, "martinspinks": true, "partsunknown": true, "pavel.zubkou": true, "processbrain": true, "shekharreddy": true, "stevenvachon": true, "sundaycrafts": true, "superchenney": true, "walexstevens": true, "warcrydoggie": true, "yourhoneysky": true, "augiethornton": true, "defunctzombie": true, "diegorbaquero": true, "donggw2030521": true, "ferchoriverar": true, "gamersdelight": true, "gzg1500521074": true, "jasonbbelcher": true, "jian263994241": true, "jonaslomholdt": true, "markthethomas": true, "miadzadfallah": true, "nonemoticoner": true, "parkerproject": true, "pixelventures": true, "serge-nikitin": true, "vivek.vikhere": true, "arnold-almeida": true, "bigglesatlarge": true, "blade254353074": true, "karzanosman984": true, "leonardothibes": true, "matteospampani": true, "shahabkhalvati": true, "jarrodhroberson": true, "marcobiedermann": true, "mauriciolauffer": true, "subinvarghesein": true, "carlosvillademor": true, "horrorandtropics": true, "scott.m.sarsfield": true, "obsessiveprogrammer": true, "programmer.severson": true}}