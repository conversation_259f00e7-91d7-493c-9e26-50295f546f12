{"_id": "abbrev", "_rev": "94-e0056d4e7495f3b86b2d38408d69b1f7", "name": "abbrev", "dist-tags": {"latest": "3.0.0"}, "versions": {"1.0.3": {"name": "abbrev", "version": "1.0.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "abbrev@1.0.3", "dist": {"shasum": "aa049c967f999222aa42e14434f0c562ef468241", "tarball": "https://registry.npmjs.org/abbrev/-/abbrev-1.0.3.tgz", "integrity": "sha512-s07HMJf6O5iTLVDx9cH7c9VbOdrmzxE+AzWz9CPi94zVNBQQA3jIwIZKTrHQj4dGR1T/MdwMnVJzSpjaVEXtXw==", "signatures": [{"sig": "MEQCIDlF6Ltnr/5LgQz5cjkY5aM2mNUf/BiKTKHI/ZJDLAc/AiA3BvijGqkrIZJOycNFdK6ZKNaQPJbmDjWWJHQPoqwoiw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/abbrev.js", "engines": {"node": "*"}, "scripts": {"test": "node lib/abbrev.js"}, "repository": {"url": "git://github.com/isaacs/abbrev-js.git", "type": "git"}, "_npmVersion": "1.0.0rc7", "description": "Like ruby's abbrev module, but in js", "directories": {}, "_nodeVersion": "v0.5.0-pre", "_defaultsLoaded": true, "_engineSupported": true}, "1.0.4": {"name": "abbrev", "version": "1.0.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/abbrev-js/raw/master/LICENSE", "type": "MIT"}, "_id": "abbrev@1.0.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "bd55ae5e413ba1722ee4caba1f6ea10414a59ecd", "tarball": "https://registry.npmjs.org/abbrev/-/abbrev-1.0.4.tgz", "integrity": "sha512-xoV1ALZYWdMEsoOSazVe4J2/0Tim1ZPKvz2xvmpxjpErBkF5o7VQWivVr8VxlAhOdesjvKHKm62l0gNEeL4+2A==", "signatures": [{"sig": "MEYCIQDi/088hpDVO68Y72BPylTdoc7LngWxXAxh5d5rai69WgIhAPLJIpQE7JBZdX/Tx/PCA7Ms/jE5+74yFwRfBxeSFPG2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/abbrev.js", "scripts": {"test": "node lib/abbrev.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/isaacs/abbrev-js", "type": "git"}, "_npmVersion": "1.1.70", "description": "Like ruby's abbrev module, but in js", "directories": {}}, "1.0.5": {"name": "abbrev", "version": "1.0.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/abbrev-js/raw/master/LICENSE", "type": "MIT"}, "_id": "abbrev@1.0.5", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/abbrev-js", "bugs": {"url": "https://github.com/isaacs/abbrev-js/issues"}, "dist": {"shasum": "5d8257bd9ebe435e698b2fa431afde4fe7b10b03", "tarball": "https://registry.npmjs.org/abbrev/-/abbrev-1.0.5.tgz", "integrity": "sha512-Sg+CLYf4W/aL/aN6jF7KJ7U8NLK0Dlewx93tRLjB2G6MPlqwWJYN+pypKISr0sbzIfSJVCkn6tYlgKBM41RYpA==", "signatures": [{"sig": "MEQCIBNcSQl5zCY0Dedv6eVr4F9PhXCwlP4mEOei8l9d3wWzAiBTOJwrvYh7Y7ffGN274ZVPYCaBU41gK3A+LWqZ2irYfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "abbrev.js", "_from": ".", "_shasum": "5d8257bd9ebe435e698b2fa431afde4fe7b10b03", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/isaacs/abbrev-js", "type": "git"}, "_npmVersion": "1.4.7", "description": "Like ruby's abbrev module, but in js", "directories": {}}, "1.0.6": {"name": "abbrev", "version": "1.0.6", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "abbrev@1.0.6", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/abbrev-js#readme", "bugs": {"url": "https://github.com/isaacs/abbrev-js/issues"}, "dist": {"shasum": "b6d632b859b3fa2d6f7e4b195472461b9e32dc30", "tarball": "https://registry.npmjs.org/abbrev/-/abbrev-1.0.6.tgz", "integrity": "sha512-7+0vBaPMygAKGRDelipd+cGQPprUGb9ZEw3zINnbKuXwrUV9bYiak9BS/4iAIA+mUgBeGYcFxJYGSM3PpPFtDQ==", "signatures": [{"sig": "MEUCIHakDLwl9D+85TaHTNmIApzIpSiBIEq2RSKlD7TtZibCAiEAypuftz4JUGzO00kzDyHVNx4NUFPPsNmUFP06kJQy4Ms=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "abbrev.js", "_from": ".", "_shasum": "b6d632b859b3fa2d6f7e4b195472461b9e32dc30", "gitHead": "648a6735d9c5a7a04885e3ada49eed4db36181c2", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/isaacs/abbrev-js.git", "type": "git"}, "_npmVersion": "2.10.0", "description": "Like ruby's abbrev module, but in js", "directories": {}, "_nodeVersion": "2.0.1"}, "1.0.7": {"name": "abbrev", "version": "1.0.7", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "abbrev@1.0.7", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/abbrev-js#readme", "bugs": {"url": "https://github.com/isaacs/abbrev-js/issues"}, "dist": {"shasum": "5b6035b2ee9d4fb5cf859f08a9be81b208491843", "tarball": "https://registry.npmjs.org/abbrev/-/abbrev-1.0.7.tgz", "integrity": "sha512-eJTPIs0mc8P5gniSqIq74DCfeFiBp1CqIdkhWvso16Xed4BlQ6WyfmuNueOka9VXIcrnmm4AEdYuayjNo1EHIg==", "signatures": [{"sig": "MEQCIB2NOWET42tVVgidADLthOmOTsUQUocjjhOifX5AXW98AiBsXSslIwrlywzeoGLNWE1rdj/SHxKOqY03kzVygRIA0A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "abbrev.js", "_from": ".", "_shasum": "5b6035b2ee9d4fb5cf859f08a9be81b208491843", "gitHead": "821d09ce7da33627f91bbd8ed631497ed6f760c2", "scripts": {"test": "tap test.js --cov"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/isaacs/abbrev-js.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "Like ruby's abbrev module, but in js", "directories": {}, "_nodeVersion": "2.0.1", "devDependencies": {"tap": "^1.2.0"}}, "1.0.9": {"name": "abbrev", "version": "1.0.9", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "abbrev@1.0.9", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/abbrev-js#readme", "bugs": {"url": "https://github.com/isaacs/abbrev-js/issues"}, "dist": {"shasum": "91b4792588a7738c25f35dd6f63752a2f8776135", "tarball": "https://registry.npmjs.org/abbrev/-/abbrev-1.0.9.tgz", "integrity": "sha512-L<PERSON>yx4aLEC3x6T0UguF6YILf+ntvmOaWsVfENmIW0E9H09vKlLDGelMjjSm0jkDHALj8A8quZ/HapKNigzwge+Q==", "signatures": [{"sig": "MEYCIQDUg+vqiAngAHsH1YfKCUAPXFEStglMYxctm8H7Otf63gIhAOZQryeK1WNbgX1Ly6hay6o7Av7GnW0MMCpy9gRV/6qN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "abbrev.js", "_from": ".", "files": ["abbrev.js"], "_shasum": "91b4792588a7738c25f35dd6f63752a2f8776135", "gitHead": "c386cd9dbb1d8d7581718c54d4ba944cc9298d6f", "scripts": {"test": "tap test.js --cov"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/isaacs/abbrev-js.git", "type": "git"}, "_npmVersion": "3.9.1", "description": "Like ruby's abbrev module, but in js", "directories": {}, "_nodeVersion": "4.4.4", "devDependencies": {"tap": "^5.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/abbrev-1.0.9.tgz_1466016055839_0.7825860097073019", "host": "packages-16-east.internal.npmjs.com"}}, "1.1.0": {"name": "abbrev", "version": "1.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "abbrev@1.1.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/abbrev-js#readme", "bugs": {"url": "https://github.com/isaacs/abbrev-js/issues"}, "dist": {"shasum": "d0554c2256636e2f56e7c2e5ad183f859428d81f", "tarball": "https://registry.npmjs.org/abbrev/-/abbrev-1.1.0.tgz", "integrity": "sha512-c92Vmq5hfBgXyoUaHqF8P5+7THGjvxAlB64tm3PiFSAcDww34ndmrlSOd3AUaBZoutDwX0dHz9nUUFoD1jEw0Q==", "signatures": [{"sig": "MEUCIFFlsjSpD0ftl/t00zD7CoeKGxmNhhOb0R1Xh3Arc3MhAiEA2qtWq/uYahYF5UUHfJLHmEj2KAF4Dp/mYiQjZPRARMo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "abbrev.js", "_from": ".", "files": ["abbrev.js"], "_shasum": "d0554c2256636e2f56e7c2e5ad183f859428d81f", "gitHead": "7136d4d95449dc44115d4f78b80ec907724f64e0", "scripts": {"test": "tap test.js --100", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/isaacs/abbrev-js.git", "type": "git"}, "_npmVersion": "4.3.0", "description": "Like ruby's abbrev module, but in js", "directories": {}, "_nodeVersion": "8.0.0-pre", "devDependencies": {"tap": "^10.1"}, "_npmOperationalInternal": {"tmp": "tmp/abbrev-1.1.0.tgz_1487054000015_0.9229173036292195", "host": "packages-12-west.internal.npmjs.com"}}, "1.1.1": {"name": "abbrev", "version": "1.1.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "abbrev@1.1.1", "maintainers": [{"name": "gabra", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/abbrev-js#readme", "bugs": {"url": "https://github.com/isaacs/abbrev-js/issues"}, "dist": {"shasum": "f8f2c887ad10bf67f634f005b6987fed3179aac8", "tarball": "https://registry.npmjs.org/abbrev/-/abbrev-1.1.1.tgz", "integrity": "sha512-nne9/IiQ/hzIhY6pdDnbBtz7DjPTKrY00P/zvPSm5pOFkl6xuGrGnXn/VtTNNfNtAfZ9/1RtehkszU9qcTii0Q==", "signatures": [{"sig": "MEYCIQDvQCH2XtwIWIVnBSH4P51+UstW+ybuYvlEWwSQoGW7fgIhAJleZ3eJj+NTABBRNuW2xhR8pQwFRPSd9cFjP/aS3RrE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "abbrev.js", "files": ["abbrev.js"], "gitHead": "a9ee72ebc8fe3975f1b0c7aeb3a8f2a806a432eb", "scripts": {"test": "tap test.js --100", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/isaacs/abbrev-js.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "Like ruby's abbrev module, but in js", "directories": {}, "_nodeVersion": "8.5.0", "devDependencies": {"tap": "^10.1"}, "_npmOperationalInternal": {"tmp": "tmp/abbrev-1.1.1.tgz_1506566833068_0.05750026390887797", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "abbrev", "version": "2.0.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "abbrev@2.0.0", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/abbrev-js#readme", "bugs": {"url": "https://github.com/npm/abbrev-js/issues"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}, "dist": {"shasum": "cf59829b8b4f03f89dda2771cb7f3653828c89bf", "tarball": "https://registry.npmjs.org/abbrev/-/abbrev-2.0.0.tgz", "fileCount": 4, "integrity": "sha512-6/mh1E2u2YgEsCHdY0Yx5oW+61gZU+1vXaoiHHrpKeuRNNgFvS+/jrwHiQhB5apAf5oB7UB7E19ol2R2LKH8hQ==", "signatures": [{"sig": "MEQCIAqmMUR8ZhXSrjGlpIqySNBD9Pa236Qja5gsIWoqGhRaAiBss1eUkBZQkv4pCtZXOXp5D1pZDUNnz0y/bz7oDuH3uQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4827, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjYUvRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqmHg//TaKWbk0RyOVd76RxaU48TKH2GwNGZqy4pxlrLKUFedm49eG6\r\nlmZm0XN3FmnCMBfj/aQ+vf4IF/NKaZpHpYbcq3wlfOdEeDisKauxheH0Hia+\r\n8KDwi7kPod8WVFt3GfrYzKscZybn4xTMkLMDe3CKRa6tzQeJUm8je8a8eRD2\r\nS8iTV7GjKPCePsS5mxakvZ7pigDFsjPAM2NzpUksiP1SS3dVVNnJoG+i4Lcg\r\nSjc7PdIszYpsk1L/SWG6Habsg7bembsTAS2a+Kl2pveU/Xp/UReEf2B+DKtp\r\nWs5O691i6xVeMICVNJFF525xPpq1kLYPknsx+SObk/b9tOhdByvUPi4ig5UZ\r\nXT3JZhv0S2pZgYXdW9KA086Jrln0X4yoHXHjfx44JLURNIjOT5wAw+Y/IRCg\r\ntg/vMwX187IfB8qG3LRrVNbxXxIl9FMCj/YBtW4TmNh/T0SDyItNSkresswh\r\nSTdNg2cE8jglnKk2/G2aKX/UnaJ5COzMOdUtF91oVjrOBZQ7HN7otS8ccdXe\r\nIGH14mamCJOx0OBS2D47gO+bpiyNJVhaOaQX1Q3QU9CHhY5DMb6PPzooMiGG\r\nGBgWlTSwzZSMJ4N1XK2thLTHmJMncQTdUWTEG9e9IGHG6Tcc40C/wjbWuocn\r\n5u2Lbqn50mw/xdtk0ZJUl0rsJoSOgu2RXVw=\r\n=u41A\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "gitHead": "6eaa998b4291757a34d55d815290314c4776a30a", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/abbrev-js.git", "type": "git"}, "_npmVersion": "9.0.1", "description": "Like ruby's abbrev module, but in js", "directories": {}, "templateOSS": {"version": "4.8.0", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.12.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.3.0", "@npmcli/template-oss": "4.8.0", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/abbrev_2.0.0_1667320785391_0.08801030116430786", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "abbrev", "version": "3.0.0", "description": "Like ruby's abbrev module, but in js", "author": {"name": "GitHub Inc."}, "main": "lib/index.js", "scripts": {"test": "tap", "lint": "npm run eslint", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run eslint -- --fix", "snap": "tap", "posttest": "npm run lint", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "repository": {"type": "git", "url": "git+https://github.com/npm/abbrev-js.git"}, "license": "ISC", "devDependencies": {"@npmcli/eslint-config": "^5.0.0", "@npmcli/template-oss": "4.23.3", "tap": "^16.3.0"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": "^18.17.0 || >=20.5.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.23.3", "publish": true}, "_id": "abbrev@3.0.0", "gitHead": "89e72e322083708922f259dc5f3635237527c41e", "bugs": {"url": "https://github.com/npm/abbrev-js/issues"}, "homepage": "https://github.com/npm/abbrev-js#readme", "_nodeVersion": "22.9.0", "_npmVersion": "10.8.3", "dist": {"integrity": "sha512-+/kfrslGQ7TNV2ecmQwMJj/B65g5KVq1/L3SGVZ3tCYGqlzFuFCGBZJtMP99wH3NpEUyAjn0zPdPUg0D+DwrOA==", "shasum": "c29a6337e167ac61a84b41b80461b29c5c271a27", "tarball": "https://registry.npmjs.org/abbrev/-/abbrev-3.0.0.tgz", "fileCount": 4, "unpackedSize": 4897, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/abbrev@3.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDlbGvGlGAD7SWzkgrlTWH2/YR/vy/HJmmsvNCONQaPDQIgEtgNm0m2mLoBk+NHNRuuUH7UANu1T9ie99I+aCGNl54="}]}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hashtagchris", "email": "<EMAIL>"}, {"name": "reggi", "email": "<EMAIL>"}, {"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/abbrev_3.0.0_1727204515937_0.4472956498597629"}, "_hasShrinkwrap": false}}, "time": {"created": "2011-03-21T22:21:11.183Z", "modified": "2024-09-24T19:01:56.766Z", "1.0.3": "2011-03-21T22:21:11.183Z", "1.0.1": "2011-03-21T22:21:11.183Z", "1.0.2": "2011-03-21T22:21:11.183Z", "1.0.3-1": "2011-03-24T23:01:19.581Z", "1.0.4": "2013-01-09T00:01:24.135Z", "1.0.5": "2014-04-17T20:09:12.523Z", "1.0.6": "2015-05-21T00:58:16.778Z", "1.0.7": "2015-05-30T22:57:54.685Z", "1.0.9": "2016-06-15T18:41:01.215Z", "1.1.0": "2017-02-14T06:33:20.235Z", "1.1.1": "2017-09-28T02:47:13.220Z", "2.0.0": "2022-11-01T16:39:45.574Z", "3.0.0": "2024-09-24T19:01:56.100Z"}, "bugs": {"url": "https://github.com/npm/abbrev-js/issues"}, "author": {"name": "GitHub Inc."}, "license": "ISC", "homepage": "https://github.com/npm/abbrev-js#readme", "repository": {"type": "git", "url": "git+https://github.com/npm/abbrev-js.git"}, "description": "Like ruby's abbrev module, but in js", "maintainers": [{"name": "hashtagchris", "email": "<EMAIL>"}, {"name": "reggi", "email": "<EMAIL>"}, {"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}], "readme": "# abbrev-js\n\nJust like [ruby's Abbrev](http://apidock.com/ruby/Abbrev).\n\nUsage:\n\n    var abbrev = require(\"abbrev\");\n    abbrev(\"foo\", \"fool\", \"folding\", \"flop\");\n    \n    // returns:\n    { fl: 'flop'\n    , flo: 'flop'\n    , flop: 'flop'\n    , fol: 'folding'\n    , fold: 'folding'\n    , foldi: 'folding'\n    , foldin: 'folding'\n    , folding: 'folding'\n    , foo: 'foo'\n    , fool: 'fool'\n    }\n\nThis is handy for command-line scripts, or other cases where you want to be able to accept shorthands.\n", "readmeFilename": "README.md", "users": {"detj": true, "d-band": true, "isaacs": true, "leesei": true, "monjer": true, "ryanve": true, "ceejbot": true, "npm-www": true, "ruanyu1": true, "bcowgi11": true, "leodutra": true, "tdmalone": true, "jessaustin": true, "flumpus-dev": true, "tunnckocore": true, "floriannagel": true, "jian263994241": true}}