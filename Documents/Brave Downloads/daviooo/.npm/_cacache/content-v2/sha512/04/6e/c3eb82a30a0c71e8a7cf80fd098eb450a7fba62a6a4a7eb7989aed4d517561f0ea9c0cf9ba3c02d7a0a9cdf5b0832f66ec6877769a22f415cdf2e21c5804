{"source": "4w+8SsipueNh/bgqOO1prvOOK+5OCWkd8Oq3eJhA7x/O86KEzGdvB/adsg/FKBO8mRDP2GgnL+i3NOtIu6OmSA==", "name": "@tufjs/models", "dependency": "minimatch", "title": "Depends on vulnerable versions of minimatch", "url": null, "severity": "low", "versions": ["1.0.0-alpha.1", "1.0.0", "1.0.1", "1.0.2", "1.0.3", "1.0.4", "2.0.0", "2.0.1", "3.0.0", "3.0.1"], "vulnerableVersions": ["1.0.0", "1.0.1", "1.0.2", "1.0.3", "1.0.4", "2.0.0", "2.0.1", "3.0.0", "3.0.1"], "cwe": ["CWE-400"], "cvss": {"score": 3.1, "vectorString": "CVSS:3.1/AV:N/AC:H/PR:L/UI:N/S:U/C:N/I:N/A:L"}, "range": "*", "id": "aYHfJkyBZD4AqqQtu1JejjKQMpM6tqTzrz2cIGIiH9nC+hrfdngRS56ihJ4xwDpLsp8T3ikGwbTVtWWOMqopNg=="}