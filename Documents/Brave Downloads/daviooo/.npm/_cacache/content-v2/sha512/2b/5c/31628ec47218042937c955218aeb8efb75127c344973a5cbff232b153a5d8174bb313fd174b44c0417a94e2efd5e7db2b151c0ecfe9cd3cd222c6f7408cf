{"_id": "dot-prop", "_rev": "57-ce9cba301ae4f7c817960da2f521bc5b", "name": "dot-prop", "description": "Get, set, or delete a property from a nested object using a dot path", "dist-tags": {"latest": "9.0.0", "legacy": "4.2.1"}, "versions": {"1.0.0": {"name": "dot-prop", "version": "1.0.0", "description": "Get a property from a nested object using a dot path", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/dot-prop"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js"}, "files": ["index.js"], "keywords": ["obj", "object", "prop", "property", "dot", "path", "get", "access"], "devDependencies": {"ava": "0.0.4"}, "gitHead": "49293242e30040a5e2a6b1379a841f8825984b7e", "bugs": {"url": "https://github.com/sindresorhus/dot-prop/issues"}, "homepage": "https://github.com/sindresorhus/dot-prop", "_id": "dot-prop@1.0.0", "_shasum": "63338710356aefecd03cbf426411e8e2742bfac5", "_from": ".", "_npmVersion": "2.1.16", "_nodeVersion": "0.10.32", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "63338710356aefecd03cbf426411e8e2742bfac5", "tarball": "https://registry.npmjs.org/dot-prop/-/dot-prop-1.0.0.tgz", "integrity": "sha512-JLo8r8EABxXO6We534GvYrMvKzZdsirzt3uC3/Q1NjOcY8NiyuggAZDtRN6F4j9d1aWKMS0rdA6irLFUiyDLCg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCOjrF5T+tPt5WLktwNl3ovlT1kIFhFZLH7UsXcxDS3pwIgX4R69U/lcDhoocEEMNyFv5TiDrAedMvxEjCGSpOBDyw="}]}, "directories": {}}, "1.0.1": {"name": "dot-prop", "version": "1.0.1", "description": "Get a property from a nested object using a dot path", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/dot-prop"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js"}, "files": ["index.js"], "keywords": ["obj", "object", "prop", "property", "dot", "path", "get", "access"], "devDependencies": {"ava": "0.0.4"}, "gitHead": "d6bcff06ba32485cc6b2be901f70c0abc49920b5", "bugs": {"url": "https://github.com/sindresorhus/dot-prop/issues"}, "homepage": "https://github.com/sindresorhus/dot-prop", "_id": "dot-prop@1.0.1", "_shasum": "bff7c4ec7108efb3c8cb5b87f4b1426821b32139", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "bff7c4ec7108efb3c8cb5b87f4b1426821b32139", "tarball": "https://registry.npmjs.org/dot-prop/-/dot-prop-1.0.1.tgz", "integrity": "sha512-RcmiVI5SnoBDWnEzbSN24tvP6OACcOBQOKeLRDpHzhbuJEL7g+fxsQRX6v8r+PWuMz5u7S6CZBT0slKYX4950A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAfgnqTDbbeCgXfn1ZrSfKsgK2w1u3soz7tsq7Msq+UYAiEAiY1QENiiRs8RkYZAjt7osBnMv49kvycUXPto5PDI/Tk="}]}, "directories": {}}, "2.0.0": {"name": "dot-prop", "version": "2.0.0", "description": "Get or set a property from a nested object using a dot path", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/dot-prop"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js"}, "files": ["index.js"], "keywords": ["obj", "object", "prop", "property", "dot", "path", "get", "access", "notation"], "devDependencies": {"ava": "0.0.4"}, "gitHead": "dca46707135af1ec7155cf953a8bea7145728ff1", "bugs": {"url": "https://github.com/sindresorhus/dot-prop/issues"}, "homepage": "https://github.com/sindresorhus/dot-prop", "_id": "dot-prop@2.0.0", "_shasum": "2f753317b5cd3a038bbee7bba865bdc7ca1b3ba8", "_from": ".", "_npmVersion": "2.7.4", "_nodeVersion": "0.12.2", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "2f753317b5cd3a038bbee7bba865bdc7ca1b3ba8", "tarball": "https://registry.npmjs.org/dot-prop/-/dot-prop-2.0.0.tgz", "integrity": "sha512-JjDfiZb/w1COfOzrS8oi4FpuPedebRufstH4pKEzF9Zc4ZCJmVOROFpR6sTDLNrGWX5NCpdWFdLPm2JbymKD+g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF57VWrLbKYiHdEW+38LaFCCnMqpVJ1tkzgDQ5AHnA+uAiBQV+jC8XQCLNe5yDd1xrIAq7x01K0xkRYlrSg92b7VhA=="}]}, "directories": {}}, "2.1.0": {"name": "dot-prop", "version": "2.1.0", "description": "Get or set a property from a nested object using a dot path", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/dot-prop"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js"}, "files": ["index.js"], "keywords": ["obj", "object", "prop", "property", "dot", "path", "get", "access", "notation"], "devDependencies": {"ava": "0.0.4"}, "gitHead": "c91819235c4c2afb394b2d7b1a2df4605c7e2282", "bugs": {"url": "https://github.com/sindresorhus/dot-prop/issues"}, "homepage": "https://github.com/sindresorhus/dot-prop", "_id": "dot-prop@2.1.0", "_shasum": "6bd199d80792d2323a2b7eb8175f4b32d76a7e72", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "6bd199d80792d2323a2b7eb8175f4b32d76a7e72", "tarball": "https://registry.npmjs.org/dot-prop/-/dot-prop-2.1.0.tgz", "integrity": "sha512-ZrQHU2Oq0PbVb5lu5YitVJnpfC5HfWo+0lQnDK5TO4ipbzC1EiEYh/qDCu4j+7v6MDBgBfwTdFiznMSNPZXD7Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHfEny0Ld6vOx3Sg8+7J69HaH06+5kN5LmLxY20jXKQlAiEA3HHv0aUPjpD9r3PffctAh7KbiHH//JOpe/FCwhwREfM="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "2.2.0": {"name": "dot-prop", "version": "2.2.0", "description": "Get or set a property from a nested object using a dot path", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/dot-prop"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js"}, "files": ["index.js"], "keywords": ["obj", "object", "prop", "property", "dot", "path", "get", "access", "notation", "dotty"], "devDependencies": {"ava": "0.0.4"}, "gitHead": "04e07dda49c69a407fb033e9a107acd718afd841", "bugs": {"url": "https://github.com/sindresorhus/dot-prop/issues"}, "homepage": "https://github.com/sindresorhus/dot-prop", "_id": "dot-prop@2.2.0", "_shasum": "2adc26ec8fb4913282c074ec2eb189781394e89d", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "2adc26ec8fb4913282c074ec2eb189781394e89d", "tarball": "https://registry.npmjs.org/dot-prop/-/dot-prop-2.2.0.tgz", "integrity": "sha512-Is/FX1u/821N4JcanC2lG1M3jx4kZpCgUSNwG+C9/yGFmJ8OJXMSBree279LO6iEwWlmobttiCJJFFzUTt0lfQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDmjmkTYnfsCuYyfjA/8qV8GzBpwTkmktlJrnIaGjjlsgIhANOzcc0i6YX07ZEoZrqHcucoG4tiA1JaWbj82efc3OpL"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "2.3.0": {"name": "dot-prop", "version": "2.3.0", "description": "Get, set, or delete a property from a nested object using a dot path", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/dot-prop"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "ava && xo"}, "files": ["index.js"], "keywords": ["obj", "object", "prop", "property", "dot", "path", "get", "set", "delete", "del", "access", "notation", "dotty"], "dependencies": {"is-obj": "^1.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "21870dfacd2c4b1f47937d4503b605fc6c8eeb84", "bugs": {"url": "https://github.com/sindresorhus/dot-prop/issues"}, "homepage": "https://github.com/sindresorhus/dot-prop", "_id": "dot-prop@2.3.0", "_shasum": "271fc1eff8c6302b65bf1b4659324e1f36725013", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.3.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "271fc1eff8c6302b65bf1b4659324e1f36725013", "tarball": "https://registry.npmjs.org/dot-prop/-/dot-prop-2.3.0.tgz", "integrity": "sha512-tIJzD25avslSbjQYVIKPGofjPYGWMIoR5BLlU1AyW5XpWntlFR8tInsbjuzQnftbExZdtAfHuSPhkSpCsdfgMA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCtisqXS7jl0N8cCS3YTkcBSsaDobxIleUnW2VbSYO7EAIgJ57onXwYvZWND3P8BH4eBKmKOjMyKH5jL8lAX4pZlHY="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-5-east.internal.npmjs.com", "tmp": "tmp/dot-prop-2.3.0.tgz_1456390089590_0.9987524345051497"}, "directories": {}}, "2.4.0": {"name": "dot-prop", "version": "2.4.0", "description": "Get, set, or delete a property from a nested object using a dot path", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/dot-prop"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "ava && xo", "bench": "matcha bench.js"}, "files": ["index.js"], "keywords": ["obj", "object", "prop", "property", "dot", "path", "get", "set", "delete", "del", "access", "notation", "dotty"], "dependencies": {"is-obj": "^1.0.0"}, "devDependencies": {"ava": "*", "matcha": "^0.6.1", "xo": "*"}, "gitHead": "c06a56f902db988af75da9955512556542d9fbe4", "bugs": {"url": "https://github.com/sindresorhus/dot-prop/issues"}, "homepage": "https://github.com/sindresorhus/dot-prop", "_id": "dot-prop@2.4.0", "_shasum": "848e28f7f1d50740c6747ab3cb07670462b6f89c", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.3.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "848e28f7f1d50740c6747ab3cb07670462b6f89c", "tarball": "https://registry.npmjs.org/dot-prop/-/dot-prop-2.4.0.tgz", "integrity": "sha512-wAg7VK6GV/tLH/Lkuqt/uMn6hUDJpWYt+0mAcaGL6nxlNZMbF+0nKu+HgfKVYGbGn/l8I4BzkCHA916OCFgdpg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDJvAGsKMcY7brTI6ne2Ak+MRKjvSCfbd5qCRrnBqoxYwIgE/4eorAXz5jUbcKI4oSRHF5aeWgEa8Rxz6ogc4GvNyM="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/dot-prop-2.4.0.tgz_1456910059548_0.3175659680273384"}, "directories": {}}, "3.0.0": {"name": "dot-prop", "version": "3.0.0", "description": "Get, set, or delete a property from a nested object using a dot path", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/dot-prop.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava", "bench": "matcha bench.js"}, "files": ["index.js"], "keywords": ["obj", "object", "prop", "property", "dot", "path", "get", "set", "delete", "del", "access", "notation", "dotty"], "dependencies": {"is-obj": "^1.0.0"}, "devDependencies": {"ava": "*", "matcha": "^0.7.0", "xo": "*"}, "gitHead": "e50dd5becf66fda1e2307506eeb3162c0f9f3bb8", "bugs": {"url": "https://github.com/sindresorhus/dot-prop/issues"}, "homepage": "https://github.com/sindresorhus/dot-prop#readme", "_id": "dot-prop@3.0.0", "_shasum": "1b708af094a49c9a0e7dbcad790aba539dac1177", "_from": ".", "_npmVersion": "2.15.0", "_nodeVersion": "4.4.2", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "1b708af094a49c9a0e7dbcad790aba539dac1177", "tarball": "https://registry.npmjs.org/dot-prop/-/dot-prop-3.0.0.tgz", "integrity": "sha512-k4ELWeEU3uCcwub7+dWydqQBRjAjkV9L33HjVRG5Xo2QybI6ja/v+4W73SRi8ubCqJz0l9XsTP1NbewfyqaSlw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE95IZcDtFG7uHJiDSxQFBpNZW4WqQbkDYn2qTLfdY9zAiBVX0iLDbf4pLD9H12NnoPQb/J+MVLQTwLKl+z8M2RFbQ=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/dot-prop-3.0.0.tgz_1463682431324_0.5166875969152898"}, "directories": {}}, "4.0.0": {"name": "dot-prop", "version": "4.0.0", "description": "Get, set, or delete a property from a nested object using a dot path", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/dot-prop.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava", "bench": "matcha bench.js"}, "files": ["index.js"], "keywords": ["obj", "object", "prop", "property", "dot", "path", "get", "set", "delete", "del", "access", "notation", "dotty"], "dependencies": {"is-obj": "^1.0.0"}, "devDependencies": {"ava": "*", "matcha": "^0.7.0", "xo": "*"}, "xo": {"esnext": true}, "gitHead": "e03af43093bb2d44dcac99426767619c9aa2e773", "bugs": {"url": "https://github.com/sindresorhus/dot-prop/issues"}, "homepage": "https://github.com/sindresorhus/dot-prop#readme", "_id": "dot-prop@4.0.0", "_shasum": "9cbe8b4c47a12033d8c4e20be33db9a8f80bd188", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.4.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "9cbe8b4c47a12033d8c4e20be33db9a8f80bd188", "tarball": "https://registry.npmjs.org/dot-prop/-/dot-prop-4.0.0.tgz", "integrity": "sha512-201z9XS801i7+Wjd882omY1ohXjs+JqjMgWoHjlZJKVkPh2SJNk3e90hxdd8BK4EDg42g+Wdu2G1cADp0MTl6A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCtKQUM9kxhPYDd/UJIX+bugGh492gcskOaVLe9Rl0nswIgH7WBUjwyxyUrkdQBSRiR0Po/PymZH1qc61Hd55Rb8IU="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/dot-prop-4.0.0.tgz_1473005808069_0.715334446169436"}, "directories": {}}, "4.1.0": {"name": "dot-prop", "version": "4.1.0", "description": "Get, set, or delete a property from a nested object using a dot path", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/dot-prop.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava", "bench": "matcha bench.js"}, "files": ["index.js"], "keywords": ["obj", "object", "prop", "property", "dot", "path", "get", "set", "delete", "del", "access", "notation", "dotty"], "dependencies": {"is-obj": "^1.0.0"}, "devDependencies": {"ava": "*", "matcha": "^0.7.0", "xo": "*"}, "xo": {"esnext": true}, "gitHead": "d49fb11519d5cbb7974ec3d3b5b57719f3c17514", "bugs": {"url": "https://github.com/sindresorhus/dot-prop/issues"}, "homepage": "https://github.com/sindresorhus/dot-prop#readme", "_id": "dot-prop@4.1.0", "_shasum": "eb29eac57dfa31fda1edef50ea462ee3d38ff3ab", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "7.2.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "eb29eac57dfa31fda1edef50ea462ee3d38ff3ab", "tarball": "https://registry.npmjs.org/dot-prop/-/dot-prop-4.1.0.tgz", "integrity": "sha512-BNePUcWcC/5tptTk3XpN6A5IeGnhFTlKQSIskjRDH0VJBdOAKSe7vhHWgBJ+IMEHAXRhybq3bdFnvgdyXyqCvQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCDMMAx81w6ehghKdDtVXULmZ216h4sRkYyi1l0mUfurQIgPM2o6EpYWY0smev13WPSNgnA8MDRfk0ZJMUHTJsInGU="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/dot-prop-4.1.0.tgz_1480265156334_0.7113023274578154"}, "directories": {}}, "4.1.1": {"name": "dot-prop", "version": "4.1.1", "description": "Get, set, or delete a property from a nested object using a dot path", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/dot-prop.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava", "bench": "matcha bench.js"}, "files": ["index.js"], "keywords": ["obj", "object", "prop", "property", "dot", "path", "get", "set", "delete", "del", "access", "notation", "dotty"], "dependencies": {"is-obj": "^1.0.0"}, "devDependencies": {"ava": "*", "matcha": "^0.7.0", "xo": "*"}, "xo": {"esnext": true}, "gitHead": "49f0809db1201f2cf13735de4f3631191a692658", "bugs": {"url": "https://github.com/sindresorhus/dot-prop/issues"}, "homepage": "https://github.com/sindresorhus/dot-prop#readme", "_id": "dot-prop@4.1.1", "_shasum": "a8493f0b7b5eeec82525b5c7587fa7de7ca859c1", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.6.2", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "a8493f0b7b5eeec82525b5c7587fa7de7ca859c1", "tarball": "https://registry.npmjs.org/dot-prop/-/dot-prop-4.1.1.tgz", "integrity": "sha512-gARbzkoggBXmE+b7QC5j4PdSNiBRAcjbLBjlYpzFOOG2zlW+iSkzs6mTbRVg6hsLVA3LuUcZGm9nAu2e9Q4GNQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBYqKqlbjksHN3e3T6qpN9kBzmsFImySHrmJgtpQUszxAiEA+9HDGV32zJnMLX7Uxbl3Am+T1ncMWZfITRpyBVm3PNs="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/dot-prop-4.1.1.tgz_1487442351691_0.9279007718432695"}, "directories": {}}, "4.2.0": {"name": "dot-prop", "version": "4.2.0", "description": "Get, set, or delete a property from a nested object using a dot path", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/dot-prop.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava", "bench": "matcha bench.js"}, "files": ["index.js"], "keywords": ["obj", "object", "prop", "property", "dot", "path", "get", "set", "delete", "del", "access", "notation", "dotty"], "dependencies": {"is-obj": "^1.0.0"}, "devDependencies": {"ava": "*", "matcha": "^0.7.0", "xo": "*"}, "xo": {"esnext": true}, "gitHead": "70f7ed8e5dfe72c726bebe9cef5c85c1fb3cd767", "bugs": {"url": "https://github.com/sindresorhus/dot-prop/issues"}, "homepage": "https://github.com/sindresorhus/dot-prop#readme", "_id": "dot-prop@4.2.0", "_npmVersion": "5.3.0", "_nodeVersion": "8.2.1", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-tUMXrxlExSW6U2EXiiKGSBVdYgtV8qlHL+C10TsW4PURY/ic+eaysnSkwB4kA/mBlCyy/IKDJ+Lc3wbWeaXtuQ==", "shasum": "1f19e0c2e1aa0e32797c49799f2837ac6af69c57", "tarball": "https://registry.npmjs.org/dot-prop/-/dot-prop-4.2.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBE6ET6CCsaPDE0XUKi5SSiz+o25B7BWNyB3BaLwxqsTAiEAqx00fdmNi/jCDx2EXaodBLgt9jtKb/gNs0kgxqdrz3w="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dot-prop-4.2.0.tgz_1500926929238_0.7274610283784568"}, "directories": {}}, "5.0.0": {"name": "dot-prop", "version": "5.0.0", "description": "Get, set, or delete a property from a nested object using a dot path", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/dot-prop.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd", "bench": "node bench.js"}, "keywords": ["object", "prop", "property", "dot", "path", "get", "set", "delete", "access", "notation", "dotty"], "dependencies": {"is-obj": "^1.0.0"}, "devDependencies": {"ava": "^1.4.1", "benchmark": "^2.1.4", "tsd": "^0.7.2", "xo": "^0.24.0"}, "gitHead": "3de4456a52fa1bb4b6a06c74e8053e4c40ac0f34", "bugs": {"url": "https://github.com/sindresorhus/dot-prop/issues"}, "homepage": "https://github.com/sindresorhus/dot-prop#readme", "_id": "dot-prop@5.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.1", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-RTmaF2jx3nOBO2GvtFqjnDLycjFUMqt+2pwRx7JVYa81lDauoj9aNkyrJI2ikR58FbBIchiIlRiGG+muLJ4oHQ==", "shasum": "64b7968af349c3a9f966aa12658dbd5829f6b953", "tarball": "https://registry.npmjs.org/dot-prop/-/dot-prop-5.0.0.tgz", "fileCount": 5, "unpackedSize": 8377, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcqMpNCRA9TVsSAnZWagAAmLIP/Rz+2xKrq9RuGMVQBISb\npGOLP456AS7ETuwFwQWn4k6U1IypiMZCPuOZk5LojGYcztTn/8H+aOgO7RE2\nUNzreGqoDpNG2DxxsctGZbmjhy1elp6BKG5dlfxwUdhqbZkZeFz9Lsbvrs3g\nHj9wikmXXBUMeW1NEMonFd/6RWa04hnZx3RaA1RSVXhUFKPC5qW8vl13qa0M\nyL8idUOiQ7Jg1II1FG9M00ucDXMMW45e6m8KXntixysNIBB54gfD9BDM8HlH\n5/GF1AYkRtyupz721HbZrrM5zpq9a7+hhg8qpFiqnYyJ/T05zCTkYcoS7V8M\nW3PaMmcNWoTDBh8BrkVUwkqo9yp1Jm90S4uFfdNR6XVXCR4wwfDrDu7KROnn\n2YFB4+fs9+p80hG3j1IYfA1DSmfgxnf6Mo9qbF1H1//q2onCykq4MCp4EsoW\n7+nZ5HjdL3vwz8NDtUZB9xKSopqsFH6SHyF7iD/J+W02oArT+fIv2IcV69r7\nKhS5ktXUUEXCtBv9HcXEaWAioFM4L3ldvxZInexS3LC6qYjL+dQDcEM0RYiM\nMKHC9Gaz1QXBnzzKQS34eUCC8Z2TAdfL4QTZIw7CMjSPbiDtQAVgrPnbFgr0\nitgjV3bY/aKoSWrnsBMpA9BUx4XesV+6Mr62shHApGdw+wGyGM1guklsN3nB\nFmLY\r\n=bKMK\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE8RoOAJVkrl8Y2pJO+nWFVu6eJcOK7rRcmBlQr9zdRGAiAgfW4qS4Gqpc1JtNJy8k7OwJ8LIQemYRTKm2075OPiwg=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dot-prop_5.0.0_1554565708132_0.6366489959099464"}, "_hasShrinkwrap": false}, "5.0.1": {"name": "dot-prop", "version": "5.0.1", "description": "Get, set, or delete a property from a nested object using a dot path", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/dot-prop.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd", "bench": "node bench.js"}, "keywords": ["object", "prop", "property", "dot", "path", "get", "set", "delete", "access", "notation", "dotty"], "dependencies": {"is-obj": "^1.0.0"}, "devDependencies": {"ava": "^1.4.1", "benchmark": "^2.1.4", "tsd": "^0.7.2", "xo": "^0.24.0"}, "gitHead": "e7b1edff89e80d83521b3b84fae5ecd223ee8b22", "bugs": {"url": "https://github.com/sindresorhus/dot-prop/issues"}, "homepage": "https://github.com/sindresorhus/dot-prop#readme", "_id": "dot-prop@5.0.1", "_nodeVersion": "8.16.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-5BKAm3KC6kgHOm5WqekMXd0HHRhilE0aJzBuOqtrPo2RDS2FTaOeuF5CEsHvZ8ETfjC9N3BufL6sxEMV8w/IIA==", "shasum": "e26c283bd1f10aab01e782371a751fb52cc3cb1c", "tarball": "https://registry.npmjs.org/dot-prop/-/dot-prop-5.0.1.tgz", "fileCount": 5, "unpackedSize": 8724, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc+g8CCRA9TVsSAnZWagAAWBgP/iw7mPMLnHw0vZEVXruj\n7A4RPMF0nUyJkR/wgyL8uvlvv54sEN4hHjmBNsGpNQFTAD2OmdWgQHkqJIcs\n6GdnH2mrG1qeJJaoTBLtROOMZnMPOTp1jH+iJyIAFITa86RXmT7OXAqGXTas\nJ7OV+ndQXH/akkS+JL839wWZhuKLKEW8oWHjte+ZaFUcr9ZxBjq3McqJWo9v\nIm7ZRqjt1Lb7qbPshJcKpauM0XZq+JuoUUbh/+XYcM8n6b7hqnd7PKEDD/7s\nF2tPKazliA4c5dZSw8Ko5l7WuED2hQ4hvMrIpPYkAmvdvF5AtvvQMW9Cd05e\nbt4TJ6YEQvb0HQhTLn+0XbqPALvwUXZeYeSjhVPhORUPBuG6xvBazDGWZ+1k\n0QnqMTJF+Lv6e2rVCHNxQiBiBp8AK5Fg6E+iP5Q49Yio/IlXBjyHXrSYO8VI\nRM76YDJIDF/ZKwmji7dLs3FwSRgkn+nVmiqA+qWlMPxj7Pp8xyUnxzkGBGrA\nXgp4HVg3U57Sa68RUzUgUKfGSbdLmmFM/ZjzJCl32PNIAn4EFStV5VUs9FVh\npBe5PQ6JLjqdA7HKlvsSHCE48DtzFjA9OvfuG0aFJVIrOPp9TxJPHFc2GGnC\n+pUY+qiiVvPdpVyEt+w2twU1tbBcqqczIW/tdVfkdQonXMbNZ3acspsizFg+\nlz7M\r\n=GjvL\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDQcidvuvPunG+9BH44IOIGC3PYuBanpnVykWrgL4MczwIhAPPiLeMUtXpl3/y8l7LxbbsShUVzvIAU5T3Bce7qMcjC"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dot-prop_5.0.1_1559891714147_0.8264957384453273"}, "_hasShrinkwrap": false}, "5.1.0": {"name": "dot-prop", "version": "5.1.0", "description": "Get, set, or delete a property from a nested object using a dot path", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/dot-prop.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd", "bench": "node bench.js"}, "keywords": ["object", "prop", "property", "dot", "path", "get", "set", "delete", "access", "notation", "dotty"], "dependencies": {"is-obj": "^2.0.0"}, "devDependencies": {"ava": "^2.1.0", "benchmark": "^2.1.4", "tsd": "^0.7.2", "xo": "^0.24.0"}, "gitHead": "b8b7124920b909bd829d689242324607e41fddc1", "bugs": {"url": "https://github.com/sindresorhus/dot-prop/issues"}, "homepage": "https://github.com/sindresorhus/dot-prop#readme", "_id": "dot-prop@5.1.0", "_nodeVersion": "8.16.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-n1oC6NBF+KM9oVXtjmen4Yo7HyAVWV2UUl50dCYJdw2924K6dX9bf9TTTWaKtYlRn0FEtxG27KS80ayVLixxJA==", "shasum": "bdd8c986a77b83e3fca524e53786df916cabbd8a", "tarball": "https://registry.npmjs.org/dot-prop/-/dot-prop-5.1.0.tgz", "fileCount": 5, "unpackedSize": 8724, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc/94UCRA9TVsSAnZWagAAmhIP/jsMPdAZnv5X0CyPEppZ\nHqQS+wdthFvJNIrE0E24O7TN784vdhUtV96HTnKE4fR2es3YdMNxtNw0r9Wx\naPoUe41ByLojme3mcRqXPvuQi3CxkAWpFBf63UW74Vksdn2VugTHphFnC3hJ\ntHMhb97ar0d6Ci2Bv32AHrRRd2Q038+cVc962HyqmhEOVQGKu8+OjeGCk3zG\n00R5c0wLGlJR6HuzVndba5OprgEK+kgv65BBjcr+wY8fnfcMpyCwpOCS9vLr\nDtTsvo2jwgJwy1BCrX6KnM3+R+zWXmKYA+/VYhwezKH1jGdw4SpgLQf43EVf\nindW8ZADzNJXqMRz0L4pDyHCLcU1Db07sn/ei2f938JMdeow7k5GkE3RfxN7\npdM7O212norEmRwczwaoSXe7jAbyY4hFFZqxMSNTxpzkFuxv8bcjsBbjgFJl\nTRbhPF1YeA8Tn8hev98nq55uEV1AcPKRR8LD1e2iDGbNGaPqDvHFAsw5vHsJ\naIeWq1taFL1XETTBDA+0fcgaQeZDZ5EFYmFAV/v5WhNFqi2ItAMdrHqSv61Z\nu21kUrne0MvUokj8jN4MAMnj2GOSW0YXenmXmbhA1bbLbzbaPqWR7E6nihiC\nfbQX97bfDwGQggTd7MbzqjptsedkpqaIShjaGUV8moGf8RTmhcBRg353FhG5\nS6tr\r\n=IBhd\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD3ll60sbqVtV9z3ETjSpNOcKy7akNPi2I7sG4rf5uvzQIgYHBN9Bru7UY2hthB37fjRslxrrEs+EV6RHpr73PBXKA="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dot-prop_5.1.0_1560272404118_0.561897408858369"}, "_hasShrinkwrap": false}, "5.1.1": {"name": "dot-prop", "version": "5.1.1", "description": "Get, set, or delete a property from a nested object using a dot path", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/dot-prop.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd", "bench": "node bench.js"}, "keywords": ["object", "prop", "property", "dot", "path", "get", "set", "delete", "access", "notation", "dotty"], "dependencies": {"is-obj": "^2.0.0"}, "devDependencies": {"ava": "^2.1.0", "benchmark": "^2.1.4", "tsd": "^0.7.2", "xo": "^0.24.0"}, "gitHead": "0efd03e2bffa6da4ebd69a3e5d43d6656b9b518e", "bugs": {"url": "https://github.com/sindresorhus/dot-prop/issues"}, "homepage": "https://github.com/sindresorhus/dot-prop#readme", "_id": "dot-prop@5.1.1", "_nodeVersion": "10.16.3", "_npmVersion": "6.12.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-QCHI6Lkf+9fJMpwfAFsTvbiSh6ujoPmhCLiDvD/n4dGtLvHfhuBwPdN6z2x4YSOwwtTcLoO/LP70xELWGF/JVA==", "shasum": "85783b39f2a54e04ae1981489a0ef2b9719bbd7d", "tarball": "https://registry.npmjs.org/dot-prop/-/dot-prop-5.1.1.tgz", "fileCount": 5, "unpackedSize": 9244, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdsBfvCRA9TVsSAnZWagAAGKYP/1h6fWreCd8PvK9GQuE/\nFOk0hnItLakwyFbgJSM2dzMbeZWGjJ42QJAL/vqbuNpCEVUs9LOVorabe74J\nRIU3oc5Pop/UknVKUOWyi37f3rwFA9CGiXVHKm2Nz28hBiIUpweLzWpXaUOx\nB5s5LxvCNCRpV4f62Zj9xO/fA0ru+ZFfKF/b29l/UdWWNHqXl4caNU4kZCDS\nUH98LmnYyLbC6pDmmQYt1gHF7PuNUsenRlOA/Aqx1D/AyXfmla2dM0sYlTfu\n8GptdAlHVDPAszJEeDNhkmgkuXEvOmCr+gAs6N961DV0ztCB/FOfIptJun4S\nXO28DUpx0kDqHmiihMEJTA9GLYBFJG5CpUx1zlLgg3jwYj+WBG043VRfDsP1\ndpxkuvr6KT58Na+Iouq9XjIKxAHeE30uZ904+feTCmDZhgbr3QIoP1NrKjk8\nNYSYGkYI/GFjlYBaNQhzoTLib7hENKuC6PCupeGcL64eDpKsFTpHMgYOmawH\n2ZpdXA8gt+L1e/H5YA6vdrL2cp0hDjxRZAO/6ct9g4AnXDaykto11oiV88eZ\nlAEIHmftz7iEuGSfpFa7/ZWdLuPYgSW4QJQtauVKgcDFigsHvXuSClo7iFB2\nEoKE7hwmaYi6gpXvFggKo4yluNF8uUW3b7qNnfH1KcC0QFdgMyUa0zgCiOUn\nY39V\r\n=M2Wp\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCSJO1VEzlBtdyHVBqRVQS0l2tqpIG5RcFg/eMJ+n3G8wIgTmP4jXyx62UN8p51HduZwz/jSfOnQGGi7lIlKfVFU4c="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dot-prop_5.1.1_1571821550659_0.5116922757574385"}, "_hasShrinkwrap": false}, "5.2.0": {"name": "dot-prop", "version": "5.2.0", "description": "Get, set, or delete a property from a nested object using a dot path", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/dot-prop.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd", "bench": "node bench.js"}, "keywords": ["object", "prop", "property", "dot", "path", "get", "set", "delete", "access", "notation", "dotty"], "dependencies": {"is-obj": "^2.0.0"}, "devDependencies": {"ava": "^2.1.0", "benchmark": "^2.1.4", "tsd": "^0.7.2", "xo": "^0.25.3"}, "gitHead": "282e984eed35b62321966f21b8db96ab70221ebc", "bugs": {"url": "https://github.com/sindresorhus/dot-prop/issues"}, "homepage": "https://github.com/sindresorhus/dot-prop#readme", "_id": "dot-prop@5.2.0", "_nodeVersion": "12.13.0", "_npmVersion": "6.12.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-uEUyaDKoSQ1M4Oq8l45hSE26SnTxL6snNnqvK/VWx5wJhmff5z0FUVJDKDanor/6w3kzE3i7XZOk+7wC0EXr1A==", "shasum": "c34ecc29556dc45f1f4c22697b6f4904e0cc4fcb", "tarball": "https://registry.npmjs.org/dot-prop/-/dot-prop-5.2.0.tgz", "fileCount": 5, "unpackedSize": 9418, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdvEhiCRA9TVsSAnZWagAAcO0P/0tJ/8bYe7BQyvw5cHbq\ncrz3BqSeNZGPt4QobxvDG+BQ4+nS6aloJhD5dvAKVRU42Pf1p55pP3cs0Gem\nk9ZtC/n+uYgslbx/TKcFaVvp0hEjTDQgyMAHd7EKjRCa+xQKLbhy6bplYWu0\nYhCpqEmm7wrAYoNogoGmbhVZg42B/WEE9RcIBTAdfGJv8ufZh5aqwhqHtwcv\n7YJFOcMH2ZIlMWLyqYmHEk83aT8WCoYSfCnwNbIHldvSHJCblkZEz6ub9b5F\nsucbh5T0YPJ2wTOSfAJpLeiYx3hStdGSfo1Jg02ErEK+G0vMjNJBxme/bULU\njRk8tpu1EOJx/j4A6OYh4nPcu6XGmjewznMdJbXMYCmucjHnitpFkrmgBEA2\nyuJhN5FyWmQmoPuDgeGv5XT1VKErY6LNeT814pqmmew0kGmUD6mVLQyv3PL4\nUseoi152ncyDPfw5gQ8Yza5h4h3RfK/udp8umjrQaX3G30Pz9yGi0uHboX5A\nXtZDr4eQbLV5SgDUtkzHQD1SyomOODEwah8H6zMmnDPG8rMhHjbHHK2wmPYj\nVTGt2JDpgMs3MbcO1xOzYxBSRMmjKEYioqaIt0DW2xmkJsiGS7FQR3yk9TRR\n/bUFDs+Pf0gNeY8m4KvkAhY9c1eTVohGmZUFlbEJK4SOnuH23tCv4F0W6NuA\nOma3\r\n=hnmr\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC/6adQwA/G9apyzEhsGrd72FJQRkMyctx2XAhW84GHgAiBVnCNRuMjZjOBwda0Zwa31qkGbqlSagiTu0vsxofs8Bg=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dot-prop_5.2.0_1572620385525_0.4753573366381234"}, "_hasShrinkwrap": false}, "4.2.1": {"name": "dot-prop", "version": "4.2.1", "description": "Get, set, or delete a property from a nested object using a dot path", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/dot-prop.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava", "bench": "matcha bench.js"}, "keywords": ["obj", "object", "prop", "property", "dot", "path", "get", "set", "delete", "del", "access", "notation", "dotty"], "dependencies": {"is-obj": "^1.0.0"}, "devDependencies": {"ava": "1.4.1", "matcha": "^0.7.0", "xo": "0.24.0"}, "xo": {"esnext": true}, "readme": "# dot-prop [![Build Status](https://travis-ci.org/sindresorhus/dot-prop.svg?branch=master)](https://travis-ci.org/sindresorhus/dot-prop)\n\n> Get, set, or delete a property from a nested object using a dot path\n\n\n## Install\n\n```\n$ npm install --save dot-prop\n```\n\n\n## Usage\n\n```js\nconst dotProp = require('dot-prop');\n\n// getter\ndotProp.get({foo: {bar: 'unicorn'}}, 'foo.bar');\n//=> 'unicorn'\n\ndotProp.get({foo: {bar: 'a'}}, 'foo.notDefined.deep');\n//=> undefined\n\ndotProp.get({foo: {bar: 'a'}}, 'foo.notDefined.deep', 'default value');\n//=> 'default value'\n\ndotProp.get({foo: {'dot.dot': 'unicorn'}}, 'foo.dot\\\\.dot');\n//=> 'unicorn'\n\n// setter\nconst obj = {foo: {bar: 'a'}};\ndotProp.set(obj, 'foo.bar', 'b');\nconsole.log(obj);\n//=> {foo: {bar: 'b'}}\n\nconst foo = dotProp.set({}, 'foo.bar', 'c');\nconsole.log(foo);\n//=> {foo: {bar: 'c'}}\n\ndotProp.set(obj, 'foo.baz', 'x');\nconsole.log(obj);\n//=> {foo: {bar: 'b', baz: 'x'}}\n\n// has\ndotProp.has({foo: {bar: 'unicorn'}}, 'foo.bar');\n//=> true\n\n// deleter\nconst obj = {foo: {bar: 'a'}};\ndotProp.delete(obj, 'foo.bar');\nconsole.log(obj);\n//=> {foo: {}}\n\nobj.foo.bar = {x: 'y', y: 'x'};\ndotProp.delete(obj, 'foo.bar.x');\nconsole.log(obj);\n//=> {foo: {bar: {y: 'x'}}}\n```\n\n\n## API\n\n### get(obj, path, [defaultValue])\n\n### set(obj, path, value)\n\nReturns the object.\n\n### has(obj, path)\n\n### delete(obj, path)\n\n#### obj\n\nType: `Object`\n\nObject to get, set, or delete the `path` value.\n\n#### path\n\nType: `string`\n\nPath of the property in the object, using `.` to separate each nested key.\n\nUse `\\\\.` if you have a `.` in the key.\n\nThe following path components are invalid and results in `undefined` being returned: `__proto__`, `prototype`, `constructor`.\n\n#### value\n\nType: `any`\n\nValue to set at `path`.\n\n#### defaultValue\n\nType: `any`\n\nDefault value.\n\n\n## License\n\nMIT © [Sindre Sorhus](https://sindresorhus.com)\n", "readmeFilename": "readme.md", "gitHead": "c914124f418f55edea27928e89c94d931babe587", "bugs": {"url": "https://github.com/sindresorhus/dot-prop/issues"}, "homepage": "https://github.com/sindresorhus/dot-prop#readme", "_id": "dot-prop@4.2.1", "_nodeVersion": "6.17.1", "_npmVersion": "6.14.7", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-l0p4+mIuJIua0mhxGoh4a+iNL9bmeK5DvnSVQa6T0OhrVmaEa1XScX5Etc673FePCJOArq/4Pa2cLGODUWTPOQ==", "shasum": "45884194a71fc2cda71cbb4bceb3a4dd2f433ba4", "tarball": "https://registry.npmjs.org/dot-prop/-/dot-prop-4.2.1.tgz", "fileCount": 4, "unpackedSize": 6350, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfORTACRA9TVsSAnZWagAAL6IP/i46qLJ/TkMIJnf0nEMN\nNGrdGCFn0dsKCZ6J3MGa/iyBaryU5VbgUhJGwM3Fb5Q2nMh/4WVo1GCSq72p\nVSJQTdEWH2xn6Yx45tuByLU1QB7dekMraNr0eHzP6n4DNRzPCo1+x22c4UBt\nWt7ftZFbRvtM2h62+cbYBMhXLfUVOIdJ0/ZXjmW3edJ4OiPVjtLPoSHbADhj\nm7L8gttaovoz4fKz2Upv7lYiS9Wa8u8VldP+WdeqoRHyEHCXYtG3W3tpDpPu\n0zXSVE1Zds//NryPinBaRqHjgc/RqH72q0ZMSip+k5g0DL8euPj3O7K28PzC\nI/pn1/UDLeqDrQGLm8uXiVEejR0rIDxeuTDz9jgmyFqSI0Hjhq6422iDZ2H/\nau3KFWjQbQtNt5YSCC2oXwhTGl7eUBixMVqkpihQzFmnlMS6jSe6JRmuzh6f\nFBwIHC9vLMWHzKMatIpYt/pHJUtFkjiz0I4RneGzSmerxyJF9Az7br9uz+VT\nhcsf/v2TwAPeAzS+vFg8iL4sbnF5V/bq4FHPolJnElMQh/GucHxxPtVlPP1i\niSJwZh506QkfpUVT2yTvtSNMcOdKVkQLrmY8njyILu0YI8T+qLdoKr1uM6IN\njO1JXqQ1mvzIgqhq8dGqsLBNPmHF7VOMOcGnQAKRUiA21mucv2EF2zHoGAvd\nDnno\r\n=Q7Yg\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDmcQ58u3+w2UXu8MF18OncS7HUT71PwTuC5ojlA37BEgIgRsRqKU3hzWJz8d+aX6zURdwvN9EO4jVeO4HWOjjekIo="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dot-prop_4.2.1_1597576383498_0.5643396673277659"}, "_hasShrinkwrap": false}, "5.3.0": {"name": "dot-prop", "version": "5.3.0", "description": "Get, set, or delete a property from a nested object using a dot path", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/dot-prop.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd", "bench": "node bench.js"}, "keywords": ["object", "prop", "property", "dot", "path", "get", "set", "delete", "access", "notation", "dotty"], "dependencies": {"is-obj": "^2.0.0"}, "devDependencies": {"ava": "^2.1.0", "benchmark": "^2.1.4", "tsd": "^0.7.2", "xo": "^0.25.3"}, "gitHead": "614e74abdfb49d59637155e77ea651af8f7988e3", "bugs": {"url": "https://github.com/sindresorhus/dot-prop/issues"}, "homepage": "https://github.com/sindresorhus/dot-prop#readme", "_id": "dot-prop@5.3.0", "_nodeVersion": "10.22.0", "_npmVersion": "6.14.7", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-QM8q3zDe58hqUqjraQOmzZ1LIH9SWQJTlEKCH4kJ2oQvLZk7RbQXvtDM2XEq3fwkV9CCvvH4LA0AV+ogFsBM2Q==", "shasum": "90ccce708cd9cd82cc4dc8c3ddd9abdd55b20e88", "tarball": "https://registry.npmjs.org/dot-prop/-/dot-prop-5.3.0.tgz", "fileCount": 5, "unpackedSize": 9607, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfVO7aCRA9TVsSAnZWagAALOwQAICNxVTmIYxPXfiZSKIj\nX5JNZDFma1TuYrSWoxOYH6GPf2LgWnHskpbwtnn3Y/vypXwdqZymUay+HgDa\nci8/tr/K1M3GcNimd2NC07K62dJC31vIinEnG8wpTErBx4fP5h1JGYNvX5uk\nn1p+FZsw/DpXhSO5cnmOZPsoIaRNWAPgWDPHucv6m/WfeaBvFA8ioQ4iqa9W\nSZvOdbNyRahvg98mmIPRGpztd8Rw2HeUzalVeKbLmf2Rh1IZp+9qt5MTsbvz\nDFqmemAF6eiVCIzCyVDNfXeb2bmeWRsAAGPNQroc+y4LXB4DLBg/S1XM9Pxr\nqLluxP6HKjSZO2JXxQ9O6ewnSB4PteWFch8T/Ktet347SxUim7R8BdVsoZ7A\nztR5SRsxFgvjBB8y2p63IzOawn/MqrvpLRXuXIwY/a9vdYtIkP9VrXIWmIYl\nJcBUnqI/mrq5GzzXzDt4K9nOhQDdQOglb1vxVeBaBwhYLzSP+gWI4Y9pmwRF\nJ6ApF4v5e1cVTkhBc4qeJJN0/f799i1CFhHSoqQDHDzPwncnJOpfoFogZmxq\n/WDR3CEwZ4xA8IToFNmW0U1mBgShHv4JbnBRMGaWQ6OovdnRk2SQ8SA/Hc17\nEeFrG/+ao6zvl3CKvpTYxR+zjxE40Hc0tOyRSjtGd4lN1Q5QdL3qiqIUZPFC\nMaOU\r\n=j5wd\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDu6VoBK3NmGKjS554KoIzpDmEwjWWuSjbe51SjSRCZiQIgRtzBgyH7eaTdp7gVCFc729Za5gCi/c4Qf8HO0327zgs="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dot-prop_5.3.0_1599401690231_0.3119363844218699"}, "_hasShrinkwrap": false}, "6.0.0": {"name": "dot-prop", "version": "6.0.0", "description": "Get, set, or delete a property from a nested object using a dot path", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/dot-prop.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava && tsd", "bench": "node bench.js"}, "keywords": ["object", "prop", "property", "dot", "path", "get", "set", "delete", "access", "notation", "dotty"], "dependencies": {"is-obj": "^2.0.0"}, "devDependencies": {"ava": "^2.1.0", "benchmark": "^2.1.4", "tsd": "^0.13.1", "xo": "^0.33.1"}, "xo": {"rules": {"@typescript-eslint/method-signature-style": "off"}}, "gitHead": "0f8abf46dc35ea806ad046a3f1512455077752b2", "bugs": {"url": "https://github.com/sindresorhus/dot-prop/issues"}, "homepage": "https://github.com/sindresorhus/dot-prop#readme", "_id": "dot-prop@6.0.0", "_nodeVersion": "14.11.0", "_npmVersion": "6.14.8", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-xCbB8IN3IT+tdgoEPOnJmYTNJDrygGFOmiQEiVa5eAD+JEB1vTgMNhVGRnN5Eex/6amck7cdcrixb1qN9Go+GQ==", "shasum": "bd579fd704d970981c4b05de591db648959f2ebb", "tarball": "https://registry.npmjs.org/dot-prop/-/dot-prop-6.0.0.tgz", "fileCount": 5, "unpackedSize": 10123, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJffi28CRA9TVsSAnZWagAASMcP/AsHbn2zlb1LJONcdUoE\njjWVOx4fnBSDYLf+5Txqu5p790VT8dWZLgYUXiPukEHmQEpRLk2NzcCFl5VW\nXPi7l3dz7YFqwhWBE0koAjYNHmiCAaSY1lVv2ESVRQYfSAmEOMBlCaR2xGCE\nxxAqVeQ19pYk3WlyKE5Yqv0fAJW8FEzrxwlWX5n1kvviQxz657V3knt8LG/7\nA4R2TfnbTIaDsTtNUvpnHK6aPgTijrNuA1ksVRjAQIj5Id3mnhGrU0Pw5cq1\nX7HDQFeASXn5PIGmTNl+Hjq+BBzYgIj7BHO+0MCP/Fg46982yF62eLjeUb1+\nlt189A3qeGLYtR8PBelSLQvomheT6iqXjQxXz62JY6z73kdmzY49pLvHGykb\nmGYkB9ng4/QZQMHMd0WeLe+wUoxcOEaDIZnbpr46Fvvqba/tyOKhpMIaE4pM\nwbl7/9AMSkYlpIlz0TA3b2XNMaKXfJy/FPld/mP792V1FT4KbD1l36y2x0+m\nPjiLBwywg4iAtjfCkWJgpaW0wy585kBmHByDI/OsCbwfsPKEwleDl1+xp4Fg\nGx2hO+yZlcRxJPcacgY643S0K18wv2QKxk7psJ79PUxjia2tCNY2+kGmfnLT\nuNWl2EzU77F2u+y/3mxqAaATNbh/9WsWyXkeAY7+z96aazV8DauUSSSXlcd4\n/xVI\r\n=Dxhz\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBdwpYbqTxbA+rqF0QygEQm022G96iAmxGPuZMoC39d0AiEAhMsp2xSWj6crICVTC6Ubx+YPev4kzHvjwz4hY2SpI3s="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dot-prop_6.0.0_1602104764275_0.07480599647640318"}, "_hasShrinkwrap": false}, "6.0.1": {"name": "dot-prop", "version": "6.0.1", "description": "Get, set, or delete a property from a nested object using a dot path", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/dot-prop.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava && tsd", "bench": "node bench.js"}, "keywords": ["object", "prop", "property", "dot", "path", "get", "set", "delete", "access", "notation", "dotty"], "dependencies": {"is-obj": "^2.0.0"}, "devDependencies": {"ava": "^2.1.0", "benchmark": "^2.1.4", "tsd": "^0.13.1", "xo": "^0.33.1"}, "xo": {"rules": {"@typescript-eslint/method-signature-style": "off"}}, "gitHead": "badea8a415fe6d53d88711bb586c2b9966bfd9db", "bugs": {"url": "https://github.com/sindresorhus/dot-prop/issues"}, "homepage": "https://github.com/sindresorhus/dot-prop#readme", "_id": "dot-prop@6.0.1", "_nodeVersion": "15.1.0", "_npmVersion": "6.14.8", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-tE7ztYzXHIeyvc7N+hR3oi7FIbf/NIjVP9hmAt3yMXzrQ072/fpjGLx2GxNxGxUl5V73MEqYzioOMoVhGMJ5cA==", "shasum": "fc26b3cf142b9e59b74dbd39ed66ce620c681083", "tarball": "https://registry.npmjs.org/dot-prop/-/dot-prop-6.0.1.tgz", "fileCount": 5, "unpackedSize": 10154, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJftkIlCRA9TVsSAnZWagAAvqQP/A4gwwjvv71LFjac6e0m\nyloHdAxxlvxk8awKyCZaW9DdMfIZITsHeovvln5K4c0HxHSbdfSpn9bcp4vT\nxOh0XNCNZNUjbDtzQZOx1aVDhjRuutgHTp7p72Ot3sR/2yPzzSltLVzjFmD2\nSkmQ3HIjk1dtppahT1wsrvOMIikh9NtP4mQhMygUemhzd7+vXB/PBeH2s1ZP\nRuo/V+/G4XMZmqC+cJdQYTV+3U0ZkbA3u9nNyMba2wgloqsLLchSdcICNft1\nhxjYys1SfsBSIeBh3n595B3xDgYlX7CgwSLxE8trErX4T1wKIk2RuJQrjRIs\n+Nxl3sSw32oOqPASapFmUZozFW+JTyEppyDJqK+/Ho4TAz5cjXozlojcFes5\nDxTHWjHTMKV2vI2EbiDRRi2HwvdYn/ZScvu/kGiKUMlHKLUOPlntFjAmogZ2\n5sSqa/vdJ3hC3a3S526JiB1WC7GIYll8YghoRRPc9IWqC2jRyC2/EURy77DU\nLGIutzbw3//VVA7N4Os/c515GSpysqN3K+ZDGVuEtvgWrSaRRDdFtljRH8/O\nl30VBfNTEZGj1RhmfPlavrVwgBrhIqtknG/EIVlNIJLWLsbFYIUl+GQYb/1B\nTyuqo8iG+F6SrE4IfUGU7QcweSvlEM1F/b8c7zl9TxFju0AxGybDHYiZxG+8\nSDVs\r\n=BLGM\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCIL621ICQ6hfXJoqPVonXWfXGm7EQ+EkOGpOt54rGtmQIhAKkgMgCdQyulPW5EVH9y9WHIrDnAhGVRTjM3jc3HFLmQ"}]}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dot-prop_6.0.1_1605780004657_0.40248611333264805"}, "_hasShrinkwrap": false}, "7.0.0": {"name": "dot-prop", "version": "7.0.0", "description": "Get, set, or delete a property from a nested object using a dot path", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/dot-prop.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd", "bench": "node benchmark.js"}, "keywords": ["object", "prop", "property", "dot", "path", "get", "set", "delete", "access", "notation", "dotty"], "dependencies": {"type-fest": "^2.10.0"}, "devDependencies": {"ava": "^4.0.1", "benchmark": "^2.1.4", "tsd": "^0.19.1", "xo": "^0.47.0"}, "types": "./index.d.ts", "gitHead": "aa1be1eca4307079e1cb271c16238df8d39cbe53", "bugs": {"url": "https://github.com/sindresorhus/dot-prop/issues"}, "homepage": "https://github.com/sindresorhus/dot-prop#readme", "_id": "dot-prop@7.0.0", "_nodeVersion": "12.22.1", "_npmVersion": "8.3.2", "dist": {"integrity": "sha512-GD9mHUsKiACS+U6Vqq8V1q6uR+7/5dQB+iQUyCkT0hKSEtX3hPcswUjYTyAGUuovvJsosBrCYmtjkCgi1gRLyQ==", "shasum": "3939ec2f37f445b729f8769075d3a7888ccc3680", "tarball": "https://registry.npmjs.org/dot-prop/-/dot-prop-7.0.0.tgz", "fileCount": 5, "unpackedSize": 13572, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6oMpCRA9TVsSAnZWagAAe88P/iiZvLeEhIU3ITeibvlb\nluKrKbX1/ATkMYvGa460Un+nV1KBvbkYDvRlmS+TEyYkZoKMqyH0eIFvOVfM\n1GFqTd9l4QNK4/+Yi1l81JIfQgEnu3Xqi0dE6EeGx+9BJapqmb4AHVFbPfVy\n6jesM3eBVKC02DcamCoAWqd4sLCG53ySAR05+evxQ59xUf2BS+UMIqG7bfGS\nzLJlWtErWvdU1fvT8BudGRulk6ycBTdPQ/1Ru36UMN+R51oNuWuXS3U8KoWq\n3kJhDviAcNFaIWYUTGW8t+BaB9LALynjaZoxBstpHyp69VJ/dPjkguJ+HOr5\nbXaXtShJ2jbNU49cqGrJC6yLMzxU5vrvx4lXZJofNVIByQW15qw/Or1TtDl4\nRNLHuAeiFYOUhWqj0E2lqVQ5TV4UlbYMrF+fnRNSY/EQxbOuaHokQUE4xTwP\nz62KiVKBKKLvZF8yYW6b8hUWALPcT3c5GS+udT/wd9VxdQBzixopOn2+rFxz\nirTn40VngUdHRUgcXj3YZxdj2MgNJI790Y1mlGjlsDUngKitdsTPB9LD8gV/\n9Cv+JT06AEuJyyL8ogxooVdnDGwd8/lJckM5o7K6yAiENpIxJE6Jjrzpiw04\nMpS3pdifUjd+U/ZeMT0hCXp06Z/m3VJ+IdlLMzFdA6KCfsBjceSdew8+JPGi\nMM7R\r\n=PBpG\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDSzT5Up6/T5Tzx65cdM8fzk5LcWjEa3tUWfMIqn6HMswIhAJYIT4AJNzDeI+OU9EZEEnP8Qt4b20zp3OXCof9RK+E+"}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dot-prop_7.0.0_1642758953211_0.6112951624043297"}, "_hasShrinkwrap": false}, "7.1.0": {"name": "dot-prop", "version": "7.1.0", "description": "Get, set, or delete a property from a nested object using a dot path", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/dot-prop.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd", "bench": "node benchmark.js"}, "keywords": ["object", "prop", "property", "dot", "path", "get", "set", "delete", "access", "notation", "dotty"], "dependencies": {"type-fest": "^2.10.0"}, "devDependencies": {"ava": "^4.0.1", "benchmark": "^2.1.4", "tsd": "^0.19.1", "xo": "^0.47.0"}, "types": "./index.d.ts", "gitHead": "0289e193f002f55608087c8d9322256bff59e7d2", "bugs": {"url": "https://github.com/sindresorhus/dot-prop/issues"}, "homepage": "https://github.com/sindresorhus/dot-prop#readme", "_id": "dot-prop@7.1.0", "_nodeVersion": "16.13.2", "_npmVersion": "8.3.2", "dist": {"integrity": "sha512-1ilqdDBRrzHCU/ch0pDyXnGLpnXxmXIml0ozStvPu0v/R5/XknCR3ROXdmXAq4n9zUsH1hDFIhAj5eia+u8Jvg==", "shasum": "b0a279f0f503a158fa19516aecc9a173138cb228", "tarball": "https://registry.npmjs.org/dot-prop/-/dot-prop-7.1.0.tgz", "fileCount": 5, "unpackedSize": 14617, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh67NsCRA9TVsSAnZWagAAo6sP/32OmTFCGbX20bCO1B45\nk5rZRz9UW+kIL3r5R+2sP08479GIdvIbcynEpuuldmxa7Yy06QL5sJHQ0uFt\nzj4zAUKSLJWqARnpZo1RyDKANKFfnacZERFoFKQkTgW54l4O+Ep7ViVbFbOK\nnD6iupNb8hfnVANCVsz4uD+aKjS5X9kUzjzzryb9Q2Cyar5vKcVw4VJragYS\nWPtl8qkHyzFgne8Q2ZZjsXiZDmZsJ8hhbj7SqUHz8g0s1wUK9VFLj1+Wl9Mx\nlX3fY2bEi5xCj1KtKdZkmol0xb6TwSCfLYPJXHf/Jn98TbFbKfvClXPEsCJg\nUURvhXI5QZsJlzvk825A1jK9CVLlAXcoFOZ30jLkA5Ao3fzFG1OZvh4NWkK/\nIFxKpHMDo7upD8skab9yCVbLCyjkMVYVufuurjZl/mPqJxh3El6TmyzVuJ8N\npt45+sbYLICyfYGA1SyBxpeh5+jQvh+aE+D1GWkj+01IAhMcXAPpn9HHkTyA\nWFNXUmji2Q9r9t9coMNHFg/sbYx9II5PywFOkFObSJ5Usu57CXHk7A7Cibgt\nzwIif5it5qp0ECqUxM84eyZwvx8kbfMg5mbNfAaqTkBLoULSg2pMqGwkIbsB\nf4FsrzzEp+1K/BP2SSCsnz6zp171vmD6senfNvEuEc+pyi7kmMTq8mvJqUzF\nc3Px\r\n=ybAs\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQChQE0CPgq4HkvQFjBYnZdI3mKI6O+urMMKSwZstdcSowIhAI+Zs8Z4lyRR41bmQeG1hoEOcT0H+4kJzfOvM4i3oXrE"}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dot-prop_7.1.0_1642836844136_0.6740433859472563"}, "_hasShrinkwrap": false}, "7.1.1": {"name": "dot-prop", "version": "7.1.1", "description": "Get, set, or delete a property from a nested object using a dot path", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/dot-prop.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd", "bench": "node benchmark.js"}, "keywords": ["object", "prop", "property", "dot", "path", "get", "set", "delete", "access", "notation", "dotty"], "dependencies": {"type-fest": "^2.10.0"}, "devDependencies": {"ava": "^4.0.1", "benchmark": "^2.1.4", "tsd": "^0.19.1", "xo": "^0.47.0"}, "types": "./index.d.ts", "gitHead": "bf3c2a63bf35bbdcb43b6eba3c1e677167b48f11", "bugs": {"url": "https://github.com/sindresorhus/dot-prop/issues"}, "homepage": "https://github.com/sindresorhus/dot-prop#readme", "_id": "dot-prop@7.1.1", "_nodeVersion": "16.13.2", "_npmVersion": "8.3.2", "dist": {"integrity": "sha512-1Rux9sph1ofNp/Bi3+zzW7XGz7Kbz+GJMKw8ncks+zS9o/zQ/+xs1livKWfQcBLYYZjEpbpzf3RrIecwb5afFQ==", "shasum": "15b7247eb35f0720bc82e17abcc40ad42ef1b30e", "tarball": "https://registry.npmjs.org/dot-prop/-/dot-prop-7.1.1.tgz", "fileCount": 5, "unpackedSize": 14619, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7EFCCRA9TVsSAnZWagAA1BYP+werOTH1/jvqH/m4cvwv\n+WpTpCQlEbWu/LehNA+V6tHtD72zRm8IQpOG7R2HTDG1uhzyaXhba8JZ+i1H\nIbb93VOC8ksi+ZMp0j0lRe12GF8EnLioIKDlt/n81UkyY//63CLW4WjyB8o0\nn7k6ZwqH+23p0I2OQJT4LzRlB0VrDLlCXgwWIASPxbYGvl2/OWgTJHQHllzn\nDdIC8DViEh3WuelECx3tzzj5eBkt5LQoBpbMs8N82NQQJeExw/LzupIfLpQF\nLicQ6H/+HEt1SS5js87vsbVomshPvzZzoe3HfN9Mi4qxmn973wrdDT4CqSj9\nJxrdng3zdaf8jSKPUCTqt1/xyPqUvdG16/ZTAkcLPAssMtg+vgDkqFEB39w2\nB4yTTvj2uu0ghOLw2kbIP07fwhhXISQM6K8Y2fiy/eTs/7akCU05i+zpOYqj\nmwJDeQ9vCA8CCL8TuIYI8pm5hLuYyFgCaOFSz/P75K8cLaTNAt12mWVFLooZ\nryDOXgJSBqlSEFtC2xnT6REcAlsvIIG5BfUhHFn2n+asU8tr18vY28XTItbl\nf3EpmLcQ1yMwwxhWhEWmU2FBtYHFF8XyWNpUzx/68MGy2YFA2u1C6b4F2qKq\ngIksYrWkshB8EiEzNBfJ01xCnZTcIMQlcofKop3zOV3UI5vzEd0uRtTmLu2E\nkBnc\r\n=tQd+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCQ2DKzfs/WfOnYCb4nRFUtsWjfvO1SwwvpG1OlcahukQIgMAHG2y6oB2FGm9qmxHBORGU3Bau120j1bu1tOrvZjLk="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dot-prop_7.1.1_1642873154138_0.8109496694470641"}, "_hasShrinkwrap": false}, "7.2.0": {"name": "dot-prop", "version": "7.2.0", "description": "Get, set, or delete a property from a nested object using a dot path", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/dot-prop.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsc", "bench": "node benchmark.js"}, "keywords": ["object", "prop", "property", "dot", "path", "get", "set", "delete", "access", "notation", "dotty"], "dependencies": {"type-fest": "^2.11.2"}, "devDependencies": {"ava": "^4.0.1", "benchmark": "^2.1.4", "expect-type": "^0.13.0", "typescript": "^4.5.5", "xo": "^0.48.0"}, "types": "./index.d.ts", "gitHead": "aff8acb5076257d14dd5f9cfefbbe5526b81aa04", "bugs": {"url": "https://github.com/sindresorhus/dot-prop/issues"}, "homepage": "https://github.com/sindresorhus/dot-prop#readme", "_id": "dot-prop@7.2.0", "_nodeVersion": "14.17.5", "_npmVersion": "8.3.2", "dist": {"integrity": "sha512-Ol/IPXUARn9CSbkrdV4VJo7uCy1I3VuSiWCaFSg+8BdUOzF9n3jefIpcgAydvUZbTdEBZs2vEiTiS9m61ssiDA==", "shasum": "468172a3529779814d21a779c1ba2f6d76609809", "tarball": "https://registry.npmjs.org/dot-prop/-/dot-prop-7.2.0.tgz", "fileCount": 5, "unpackedSize": 16680, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDcUACRA9TVsSAnZWagAAmToQAIAOp5Guh+oQXzf2rkWb\nGR7I2chhOv8uGCe3JSK+jtjXus3WZ7zS5kk6/tNMhf2Fg9kV+Hlg83sNnYOo\nvfTPuXZE3tQiBHunfX9KDCCDah3u1i1mTRIIPr7P+qxy1Ts5HojLHtcop2q3\nkDOWLa0eC8Uh/jsxWIZwTjUlctE62ct4+Ctnth/jTUNVcwmYvHT2aFNpBo5g\npc7yobU9bZZhTnPhRfoADX5mKFUvWjnR4FqJOlCabtSS8RfRtUmR3k0gO0fz\nvWdQm1955sEELL5OD2zlygSGNp7ydCMS6YNEW9s3Za0jBj4cG8C7RuS1r+JO\ntLjb/7S0hTLJTysLa7l7R0dSDqo2FpNSTyZ72qjeUi44yvkK8HlehK9KRy0F\nRXAjH+qCxv+up52tf3s1m/QM4U8JHLhDF3x48W5wdw1hexti2EY8AYXtxvfp\niAZNil/206Hk92E0YglsmEE6BRRdd1xGIRwgL/HLZsA8swy1F9iqP1iBNVaF\nRP+cxlbqZukh1IdI9lxeGKi4qe37JPJBMsR81E1Y0SsXzTGHU0W2cd+tQp3Q\nwP1JJAqyVUMTPH0qG+QAc03Bor1yYGXxco7QdDpa+ImkAGhsObxZ7aAAkHP+\nPlEDZRWLl/voTevzSPwrW6Ny1hgvD1ruQVJuoLjyLxznc2E3KKL+iD0v3vKW\nvytd\r\n=orhB\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDtULYouw+fz4jHEhw/+Jr84dSI6Oa375gUQIXAvaEU9wIgTaezV7oAJqKzefcvhLjbXKVEE6XiCkK0Q9JW0Tj0Jqw="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dot-prop_7.2.0_1645069567912_0.1744364434258252"}, "_hasShrinkwrap": false}, "8.0.0": {"name": "dot-prop", "version": "8.0.0", "description": "Get, set, or delete a property from a nested object using a dot path", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/dot-prop.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=16"}, "scripts": {"test": "xo && ava && tsc", "bench": "node benchmark.js"}, "keywords": ["object", "prop", "property", "dot", "path", "get", "set", "delete", "access", "notation", "dotty"], "dependencies": {"type-fest": "^3.8.0"}, "devDependencies": {"ava": "^5.2.0", "benchmark": "^2.1.4", "expect-type": "^0.15.0", "typescript": "^5.0.4", "xo": "^0.54.1"}, "types": "./index.d.ts", "gitHead": "bf6224b074cd46e7a384f987062c31e8c688eb90", "bugs": {"url": "https://github.com/sindresorhus/dot-prop/issues"}, "homepage": "https://github.com/sindresorhus/dot-prop#readme", "_id": "dot-prop@8.0.0", "_nodeVersion": "16.16.0", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-XHcoBL9YPvqIz6K9m9TLf9+6Iyf2ix6yYN+sZ4AI8JPg+8XQpm05V6qzPFZYzyuHfr496TqKlhzHuEvW4ME7Pw==", "shasum": "bfd2dcfd1b0e836c961b033d840a2918736490d5", "tarball": "https://registry.npmjs.org/dot-prop/-/dot-prop-8.0.0.tgz", "fileCount": 5, "unpackedSize": 16646, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICe8OZpAEUYcIxnn+1q/fzBB4Aq81LbPFKDVwFgAKgNeAiAQkFzuJXY2uslFq1xqvMAIq1R7pr5XKVtdlBuhr5qg8A=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkQ4FGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrcmRAAodkVdoBfsCBoCvkoVpQuyzYgKg42/jHGxmlFzG04/O14xmAf\r\nIzlWdtGrQrBJ9Plq5jD6hUfiHhjBfeCFU8tSpj8/qvS8Oc2t2RKkh2VWXGzm\r\ngCpfO7x8D8Ga0jSCuUJQwH+f7Tx9TUJ2bBT9dv5lEuj7MX4S+0laMo6ED1ZD\r\nE3qZmOFhDhYKglDSysSw/V3jHspU7Iu6urGjm0+U2pYpmvCLyoFa4GVmz8dF\r\nnPpXk+zUGsPs9iLU3IeY24A8DvSNSYP+to9QcIGxgsv22YaSpEF3Mf9qDmV0\r\nfTp1n/YKATQddUuyQrl09lQIz4ELkclC9Df5MENuhzbceclUylL6sItvx2nU\r\nxuflpihnuEZGD1s6DNZLyjQtmykuf4fTpfsKJAFJNIQJf1YzPIB0XhwbgrT8\r\n8mtj3iMAWEJ3z6XhfMoU1L2uxyLHum8TMDzjXDBm0WSqJIm7bi1Hp9ypW4I1\r\nxERpzXCBVwvqkIQ21+2OteDj0Y5GoyCmUjp2ROODwTjk8uGXsJH+ZrzGVuAu\r\nOH2/NIiIzEnNK9csqIfz6wBJ6MEGLg/u9N14ddxyp9YfFTSRLjdEEzMDwPcN\r\nCv1A4oyLyJmI1ibwJ/bdf3nfGeGXMTlKNomANXNXqorw+NU7iU6ysWfkjr0s\r\n7VTt6WWdYoHMp9NUT1d6BuApwqoyK60NzWU=\r\n=/iqF\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dot-prop_8.0.0_1682145606239_0.34851274544028077"}, "_hasShrinkwrap": false}, "8.0.1": {"name": "dot-prop", "version": "8.0.1", "description": "Get, set, or delete a property from a nested object using a dot path", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/dot-prop.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=16"}, "scripts": {"test": "xo && ava && tsc", "bench": "node benchmark.js"}, "keywords": ["object", "prop", "property", "dot", "path", "get", "set", "delete", "access", "notation", "dotty"], "dependencies": {"type-fest": "^3.8.0"}, "devDependencies": {"ava": "^5.2.0", "benchmark": "^2.1.4", "expect-type": "^0.15.0", "typescript": "^5.0.4", "xo": "^0.54.1"}, "types": "./index.d.ts", "gitHead": "7251fb6ce2226bf9b192d21fd6e7678a4ac2d063", "bugs": {"url": "https://github.com/sindresorhus/dot-prop/issues"}, "homepage": "https://github.com/sindresorhus/dot-prop#readme", "_id": "dot-prop@8.0.1", "_nodeVersion": "16.20.0", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-iGSfpEt8JjvdDFBrRplg5faL1dWBF6ae+vd02QF9CLP7SaOA8CFBgbBfVVZ1aNYK2dfXDJS3KK6qJzd/b5QyLQ==", "shasum": "5c1552816b2a311e1721e8e54f02732770211381", "tarball": "https://registry.npmjs.org/dot-prop/-/dot-prop-8.0.1.tgz", "fileCount": 5, "unpackedSize": 16718, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBMJoFwqBZk+hTa/3pw+89OizlLXsHkbbvmlWAw+tOiKAiB1/6oRffep4E+OMRXNC3O52pCpDtN+wtCzwmvCgrHfjA=="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dot-prop_8.0.1_1688062473948_0.006410909325073488"}, "_hasShrinkwrap": false}, "8.0.2": {"name": "dot-prop", "version": "8.0.2", "description": "Get, set, or delete a property from a nested object using a dot path", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/dot-prop.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=16"}, "scripts": {"test": "xo && ava && tsc", "bench": "node benchmark.js"}, "keywords": ["object", "prop", "property", "dot", "path", "get", "set", "delete", "access", "notation", "dotty"], "dependencies": {"type-fest": "^3.8.0"}, "devDependencies": {"ava": "^5.2.0", "benchmark": "^2.1.4", "expect-type": "^0.15.0", "typescript": "^5.0.4", "xo": "^0.54.1"}, "types": "./index.d.ts", "gitHead": "3dd188c8ae7e5a5fe01a38d13cb7bcd5e038d28e", "bugs": {"url": "https://github.com/sindresorhus/dot-prop/issues"}, "homepage": "https://github.com/sindresorhus/dot-prop#readme", "_id": "dot-prop@8.0.2", "_nodeVersion": "16.20.0", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-xaBe6ZT4DHPkg0k4Ytbvn5xoxgpG0jOS1dYxSOwAHPuNLjP3/OzN0gH55SrLqpx8cBfSaVt91lXYkApjb+nYdQ==", "shasum": "afda6866610684dd155a96538f8efcdf78a27f18", "tarball": "https://registry.npmjs.org/dot-prop/-/dot-prop-8.0.2.tgz", "fileCount": 5, "unpackedSize": 16825, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDTMxgYqU/Fo1ajeEZgtB5sf92ali7Y4i5CtvyBQeciqAIgDlEjHYkEiVWNktU04+Ew74nPk4pJ30dHN/AeNf0i3R4="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dot-prop_8.0.2_1689686943567_0.8270773326948548"}, "_hasShrinkwrap": false}, "9.0.0": {"name": "dot-prop", "version": "9.0.0", "description": "Get, set, or delete a property from a nested object using a dot path", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/dot-prop.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsc", "bench": "node benchmark.js"}, "keywords": ["object", "prop", "property", "dot", "path", "get", "set", "delete", "access", "notation", "dotty"], "dependencies": {"type-fest": "^4.18.2"}, "devDependencies": {"ava": "^6.1.3", "benchmark": "^2.1.4", "expect-type": "^0.19.0", "typescript": "^5.4.5", "xo": "^0.58.0"}, "_id": "dot-prop@9.0.0", "gitHead": "35eda392c035264bed686463ff625a750f6a82df", "types": "./index.d.ts", "bugs": {"url": "https://github.com/sindresorhus/dot-prop/issues"}, "homepage": "https://github.com/sindresorhus/dot-prop#readme", "_nodeVersion": "20.12.2", "_npmVersion": "10.6.0", "dist": {"integrity": "sha512-1gxPBJpI/pcjQhKgIU91II6Wkay+dLcN3M6rf2uwP8hRur3HtQXjVrdAK3sjC0piaEuxzMwjXChcETiJl47lAQ==", "shasum": "bae5982fe6dc6b8fddb92efef4f2ddff26779e92", "tarball": "https://registry.npmjs.org/dot-prop/-/dot-prop-9.0.0.tgz", "fileCount": 5, "unpackedSize": 16897, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDIBy/TbZhu3lkIyHl+lD0d+Xes2qTFGYoTLnM2qlFJpAiAm1euzh5LBnXHCZaVW7wOjj7xEJIutwuoIy4Jc892KyA=="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dot-prop_9.0.0_1715286694326_0.2765758420428339"}, "_hasShrinkwrap": false}}, "readme": "# dot-prop\n\n> Get, set, or delete a property from a nested object using a dot path\n\n## Install\n\n```sh\nnpm install dot-prop\n```\n\n## Usage\n\n```js\nimport {getProperty, setProperty, hasProperty, deleteProperty} from 'dot-prop';\n\n// Getter\ngetProperty({foo: {bar: 'unicorn'}}, 'foo.bar');\n//=> 'unicorn'\n\ngetProperty({foo: {bar: 'a'}}, 'foo.notDefined.deep');\n//=> undefined\n\ngetProperty({foo: {bar: 'a'}}, 'foo.notDefined.deep', 'default value');\n//=> 'default value'\n\ngetProperty({foo: {'dot.dot': 'unicorn'}}, 'foo.dot\\\\.dot');\n//=> 'unicorn'\n\ngetProperty({foo: [{bar: 'unicorn'}]}, 'foo[0].bar');\n//=> 'unicorn'\n\n// Setter\nconst object = {foo: {bar: 'a'}};\nsetProperty(object, 'foo.bar', 'b');\nconsole.log(object);\n//=> {foo: {bar: 'b'}}\n\nconst foo = setProperty({}, 'foo.bar', 'c');\nconsole.log(foo);\n//=> {foo: {bar: 'c'}}\n\nsetProperty(object, 'foo.baz', 'x');\nconsole.log(object);\n//=> {foo: {bar: 'b', baz: 'x'}}\n\nsetProperty(object, 'foo.biz[0]', 'a');\nconsole.log(object);\n//=> {foo: {bar: 'b', baz: 'x', biz: ['a']}}\n\n// Has\nhasProperty({foo: {bar: 'unicorn'}}, 'foo.bar');\n//=> true\n\n// Deleter\nconst object = {foo: {bar: 'a'}};\ndeleteProperty(object, 'foo.bar');\nconsole.log(object);\n//=> {foo: {}}\n\nobject.foo.bar = {x: 'y', y: 'x'};\ndeleteProperty(object, 'foo.bar.x');\nconsole.log(object);\n//=> {foo: {bar: {y: 'x'}}}\n```\n\n## API\n\n### getProperty(object, path, defaultValue?)\n\nGet the value of the property at the given path.\n\nReturns the value if any.\n\n### setProperty(object, path, value)\n\nSet the property at the given path to the given value.\n\nReturns the object.\n\n### hasProperty(object, path)\n\nCheck whether the property at the given path exists.\n\nReturns a boolean.\n\n### deleteProperty(object, path)\n\nDelete the property at the given path.\n\nReturns a boolean of whether the property existed before being deleted.\n\n### escapePath(path)\n\nEscape special characters in a path. Useful for sanitizing user input.\n\n```js\nimport {getProperty, escapePath} from 'dot-prop';\n\nconst object = {\n\tfoo: {\n\t\tbar: '👸🏻 You found me Mario!',\n\t},\n\t'foo.bar' : '🍄 The princess is in another castle!',\n};\nconst escapedPath = escapePath('foo.bar');\n\nconsole.log(getProperty(object, escapedPath));\n//=> '🍄 The princess is in another castle!'\n```\n\n### deepKeys(object)\n\nReturns an array of every path. Non-empty plain objects and arrays are deeply recursed and are not themselves included.\n\nThis can be useful to help flatten an object for an API that only accepts key-value pairs or for a tagged template literal.\n\n```js\nimport {getProperty, deepKeys} from 'dot-prop';\n\nconst user = {\n\tname: {\n\t\tfirst: 'Richie',\n\t\tlast: 'Bendall',\n\t},\n\tactiveTasks: [],\n\tcurrentProject: null\n};\n\nfor (const property of deepKeys(user)) {\n\tconsole.log(`${property}: ${getProperty(user, property)}`);\n\t//=> name.first: Richie\n\t//=> name.last: Bendall\n\t//=> activeTasks: []\n\t//=> currentProject: null\n}\n```\n\nSparse arrays are supported. In general, [avoid using sparse arrays](https://github.com/sindresorhus/dot-prop/issues/109#issuecomment-1614819869).\n\n#### object\n\nType: `object | array`\n\nObject or array to get, set, or delete the `path` value.\n\nYou are allowed to pass in `undefined` as the object to the `get` and `has` functions.\n\n#### path\n\nType: `string`\n\nPath of the property in the object, using `.` to separate each nested key.\n\nUse `\\\\.` if you have a `.` in the key.\n\nThe following path components are invalid and results in `undefined` being returned: `__proto__`, `prototype`, `constructor`.\n\n#### value\n\nType: `unknown`\n\nValue to set at `path`.\n\n#### defaultValue\n\nType: `unknown`\n\nDefault value.\n", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2024-05-09T20:31:34.697Z", "created": "2015-01-08T06:19:18.574Z", "1.0.0": "2015-01-08T06:19:18.574Z", "1.0.1": "2015-01-24T07:27:06.884Z", "2.0.0": "2015-04-28T15:33:37.795Z", "2.1.0": "2015-06-04T12:54:25.273Z", "2.2.0": "2015-06-16T17:22:16.000Z", "2.3.0": "2016-02-25T08:48:11.743Z", "2.4.0": "2016-03-02T09:14:20.082Z", "3.0.0": "2016-05-19T18:27:16.011Z", "4.0.0": "2016-09-04T16:16:51.651Z", "4.1.0": "2016-11-27T16:45:58.307Z", "4.1.1": "2017-02-18T18:25:51.926Z", "4.2.0": "2017-07-24T20:08:50.144Z", "5.0.0": "2019-04-06T15:48:28.290Z", "5.0.1": "2019-06-07T07:15:14.421Z", "5.1.0": "2019-06-11T17:00:04.463Z", "5.1.1": "2019-10-23T09:05:50.892Z", "5.2.0": "2019-11-01T14:59:45.673Z", "4.2.1": "2020-08-16T11:13:03.636Z", "5.3.0": "2020-09-06T14:14:50.395Z", "6.0.0": "2020-10-07T21:06:04.374Z", "6.0.1": "2020-11-19T10:00:04.839Z", "7.0.0": "2022-01-21T09:55:53.337Z", "7.1.0": "2022-01-22T07:34:04.346Z", "7.1.1": "2022-01-22T17:39:14.269Z", "7.2.0": "2022-02-17T03:46:08.069Z", "8.0.0": "2023-04-22T06:40:06.480Z", "8.0.1": "2023-06-29T18:14:34.167Z", "8.0.2": "2023-07-18T13:29:03.736Z", "9.0.0": "2024-05-09T20:31:34.528Z"}, "homepage": "https://github.com/sindresorhus/dot-prop#readme", "keywords": ["object", "prop", "property", "dot", "path", "get", "set", "delete", "access", "notation", "dotty"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/dot-prop.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/dot-prop/issues"}, "license": "MIT", "readmeFilename": "readme.md", "users": {"roryrjb": true, "gmaclennan": true, "akiva": true, "steel1990": true, "ubenzer": true, "rocket0191": true, "tur-nr": true, "itonyyo": true, "mysticatea": true, "kostya.fokin": true, "raybenefield": true, "lamansky": true, "isayme": true, "evocateur": true, "flumpus-dev": true}}