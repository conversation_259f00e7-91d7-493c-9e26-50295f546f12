{"_id": "unique-string", "_rev": "9-76f7fa14432caaa2f8c1192180f217ae", "name": "unique-string", "description": "Generate a unique random string", "dist-tags": {"latest": "3.0.0"}, "versions": {"1.0.0": {"name": "unique-string", "version": "1.0.0", "description": "Generate a unique random string", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/unique-string.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["unique", "string", "random", "uniq", "str", "rand", "text", "id", "identifier", "slug", "hex"], "dependencies": {"crypto-random-string": "^1.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}, "xo": {"esnext": true}, "gitHead": "157a6b74fb44cf5d4ee2aa579502b967edbf4203", "bugs": {"url": "https://github.com/sindresorhus/unique-string/issues"}, "homepage": "https://github.com/sindresorhus/unique-string#readme", "_id": "unique-string@1.0.0", "_shasum": "9e1057cca851abb93398f8b33ae187b99caec11a", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.6.1", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "9e1057cca851abb93398f8b33ae187b99caec11a", "tarball": "https://registry.npmjs.org/unique-string/-/unique-string-1.0.0.tgz", "integrity": "sha512-ODgiYu03y5g76A1I9Gt0/chLCzQjvzDy7DsZGsLOE/1MrF6wriEskSncj1+/C58Xk/kPZDppSctDybCwOSaGAg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB2sLNxeKFooj+oyev6V+XErwstli5ri5ZVO1WRTmPvrAiEAgLqMLYYIwYfbDiE0PYQYMLi1kvrvTxK80lmi6v+H56w="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/unique-string-1.0.0.tgz_1479097654250_0.7648377516306937"}, "directories": {}}, "2.0.0": {"name": "unique-string", "version": "2.0.0", "description": "Generate a unique random string", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/unique-string.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["unique", "string", "random", "text", "id", "identifier", "slug", "hex"], "dependencies": {"crypto-random-string": "^2.0.0"}, "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "gitHead": "036b5ee327292edc400e7147d96905bab9323a47", "bugs": {"url": "https://github.com/sindresorhus/unique-string/issues"}, "homepage": "https://github.com/sindresorhus/unique-string#readme", "_id": "unique-string@2.0.0", "_nodeVersion": "10.15.3", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-uNaeirEPvpZWSgzwsPGtU2zVSTrn/8L5q/IexZmH0eH6SA73CmAA5U4GwORTxQAZs95TAXLNqeLoPPNO5gZfWg==", "shasum": "****************************************", "tarball": "https://registry.npmjs.org/unique-string/-/unique-string-2.0.0.tgz", "fileCount": 5, "unpackedSize": 2880, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcxnr/CRA9TVsSAnZWagAAfUEP/119Yi2ZjrO5dAXlz4Re\n1It3BqAbVA3NUGqF12Xy4X6ZlnxX9IcIMH7YaqfhTPP4jLxaN7SdTqseM+JL\neN9X5KUANBWfLMImjnM4Jd2XIqVW6iLYp/lBMYNwhV6MdJ3fijjtdt0uiMKL\npTd0ofMaSl8ylNSqqWOiR++iMdnq/cKxs8s06sYwZNd2LcRKMBGFHahscmAh\n9hGBLlw3Z/zdHrez21plOWiV6uIIdVoea6ZqfiELk8mGKcp4DBe7CEnIzucz\nEExD806ExfOQZKZ4uc9ya0dkkYkMhXBPO47LNZaBlXnmlC75Uc0StejLMoWm\nowYrWIGMCKUp3j+oY3mn7sP8WMhMJx8G6Rewaw2Sbo9+GmuNg+Suh4qyHSln\nCSB2/R6DZUgs/EM2IDmVUFu0Uk8hszPKqUD1PTHtcZ/AIGZeqXnTnGlgZ/vS\nNoqITwBJowiQ/DIFo6zHRidxM1aKtZ/yxMzlqySEPnn81WFd9UByJK20h0bp\nIQ9YnLMMgIEtghlhMrd3Cw80hcHQupk6AryGLVxZoBPv+H+ybBFhSWYBpMY+\nl90d9u7++McwpBrHkPp1iZSWxIbs9uWVBDbgfrqMwvzQ0QaCskvhfD60Zi3L\nL95lFoVgaZd92H0mfpgRMiaGxpS7bxfVHJneZW/RinRIYfP+7jUI0efEExDt\nSGB2\r\n=ju7h\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGQIrGtHg18pgcQl0iGZ6HqEyaE4PSfMf6ye47VEfvsaAiEAsEBxlFb76cu+3cApkm1ICwLnHnJwASiP9Tpbk4zTijE="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/unique-string_2.0.0_1556511486648_0.6876253780135282"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "unique-string", "version": "3.0.0", "description": "Generate a unique random string", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/unique-string.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["unique", "string", "random", "text", "id", "identifier", "slug", "hex"], "dependencies": {"crypto-random-string": "^4.0.0"}, "devDependencies": {"ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.38.2"}, "gitHead": "bbb5d66aa474cab942dfa5b412f74d9ac68b976d", "bugs": {"url": "https://github.com/sindresorhus/unique-string/issues"}, "homepage": "https://github.com/sindresorhus/unique-string#readme", "_id": "unique-string@3.0.0", "_nodeVersion": "12.20.1", "_npmVersion": "6.14.10", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-VGXBUVwxKMBUznyffQweQABPRRW1vHZAbadFZud4pLFAqRGvv/96vafgjWFqzourzr8YonlQiPgH0YCJfawoGQ==", "shasum": "84a1c377aff5fd7a8bc6b55d8244b2bd90d75b9a", "tarball": "https://registry.npmjs.org/unique-string/-/unique-string-3.0.0.tgz", "fileCount": 5, "unpackedSize": 2789, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgashkCRA9TVsSAnZWagAAq9UQAJs0EYP/SydswzVyrGRk\nXX1nLWkSkfApJzC3rBwMXpioYo1Zc4PFRG9fkY8UiF9tXDqByWxyVp9Zp+/A\nPB/RgnR0p7B/uEww/aeN+9yUd3CWM0+A5vdDy4Og2usjzRQ680Q23Vwf8EfE\ng5JTZ03md2/KBydILRT0Jmctms3IQ8kM9dEViiiTopxQBfCXmjAbHQxPxV+e\n3YQFn8kASco3uj+eBRWqINV0+lKhXSc1pvwsXt/UGv0SMbWg4bLegg8ZlH/y\nS6BC8fNcWeqMceOgELco0gWuaL2UovypJXqoZTOWesJ71TcidRCmdMHTYYZW\nnGwrwSmyL9bgnSbH6WlLXZkDKrHe3fwDaV0V3UVKnDefOMM84VuDrEs2G0R4\nEGwn5gtCWv97BvdiaCMYREKKWDblcdBreZ8HxhTAxUGQ4218AGDqkuMgEl7Z\nsxAEV+X0gep1yPBogro1ytklQBp1JwR0xhMzbAzYlwSWbRREVMaMvTYDlUjs\nL+nDEQ4YFi954io+FMK7/BdeEMG84i4d5mukiwmcrUCsd3tlDMx4QhjVm6kZ\nOxRieY/oh4JAWOeRs5fvBHYs5alMCtilADPtTecCrqU83rLH6CaO0Yej0SmH\n71qP78hWHdTtjyfGzru2FzQfnYp91sVvrbDBWOenlPRdysZU1JGfJTcoUn1J\nK0pv\r\n=lm4g\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDXNKbim0k4ZqBbeiyoDznuS9UloGHrUOFoUKhxYkzWowIhAOFBtg5Vt8fOPqvQIXtJtQv/hI4gisj7tfqkrcsXIaOe"}]}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/unique-string_3.0.0_1617610851777_0.36686542678682876"}, "_hasShrinkwrap": false}}, "readme": "# unique-string\n\n> Generate a unique random string\n\n## Install\n\n```\n$ npm install unique-string\n```\n\n## Usage\n\n```js\nimport uniqueString from 'unique-string';\n\nuniqueString();\n//=> 'b4de2a49c8ffa3fbee04446f045483b2'\n```\n\n## API\n\n### uniqueString()\n\nReturns a 32 character unique string. Matches the length of MD5, which is [unique enough](https://stackoverflow.com/a/2444336/64949) for non-crypto purposes.\n", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2022-06-28T04:42:35.323Z", "created": "2016-11-14T04:27:36.128Z", "1.0.0": "2016-11-14T04:27:36.128Z", "2.0.0": "2019-04-29T04:18:06.804Z", "3.0.0": "2021-04-05T08:20:51.892Z"}, "homepage": "https://github.com/sindresorhus/unique-string#readme", "keywords": ["unique", "string", "random", "text", "id", "identifier", "slug", "hex"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/unique-string.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/unique-string/issues"}, "license": "MIT", "readmeFilename": "readme.md", "users": {"rocket0191": true, "rethinkflash": true, "gzg1500521074": true}}