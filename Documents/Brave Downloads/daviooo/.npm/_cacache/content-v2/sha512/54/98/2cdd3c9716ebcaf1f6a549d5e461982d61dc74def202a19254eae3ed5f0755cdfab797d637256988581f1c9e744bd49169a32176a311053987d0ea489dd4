{"_id": "utile", "_rev": "61-64055eede2c6f60950cefba9621457dd", "name": "utile", "description": "A drop-in replacement for `util` with some additional advantageous functions", "dist-tags": {"latest": "0.3.0"}, "versions": {"0.0.1": {"name": "utile", "description": "Advantageous functions for programming in javascript and node.js", "version": "0.0.1", "author": {"name": "Nodejitsu Inc", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/flatiron/utile.git"}, "dependencies": {"async": "0.1.x", "mkdirp": "0.x.x", "ncp": "0.2.x", "rimraf": "1.x.x"}, "devDependencies": {"vows": "0.5.x"}, "main": "./lib/index", "engines": {"node": ">= 0.4.0"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "_id": "utile@0.0.1", "_engineSupported": true, "_npmVersion": "1.0.103", "_nodeVersion": "v0.4.11", "_defaultsLoaded": true, "dist": {"shasum": "b7694fd2ac4a799dc0d9612c499a04a5eda92632", "tarball": "https://registry.npmjs.org/utile/-/utile-0.0.1.tgz", "integrity": "sha512-pcidNtaUCg8FCFwjcuOKbpQyzt81SYgeSmTwDJFKztiTNba4ryGHIVH/ybY2Wd6oFOS2R9KMwrobBbe7i/wWag==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEl87xL96SNSd49BR0MLU6hvvy0e2pSPmFw7re8PTCadAiBItEfaZtq4kEOnNrA01z7iZBNXHI3cunk6yrRlpQpP0g=="}]}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "directories": {}}, "0.0.2": {"name": "utile", "description": "Advantageous functions for programming in javascript and node.js", "version": "0.0.2", "author": {"name": "Nodejitsu Inc", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/flatiron/utile.git"}, "dependencies": {"async": "0.1.x", "mkdirp": "0.x.x", "ncp": "0.2.x", "rimraf": "1.x.x"}, "devDependencies": {"vows": "0.5.x"}, "main": "./lib/index", "engines": {"node": ">= 0.4.0"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "_id": "utile@0.0.2", "_engineSupported": true, "_npmVersion": "1.0.103", "_nodeVersion": "v0.4.11", "_defaultsLoaded": true, "dist": {"shasum": "bc0017ea9b28d2c6b08c311c793ff4174048778f", "tarball": "https://registry.npmjs.org/utile/-/utile-0.0.2.tgz", "integrity": "sha512-AA4AxzwuWCCa9tkAop3tyEJjWFxPphOmALA3phB2duN+HcEMVL7bGT9PaT8GjhpFmjmUe0wRRUxPrPWQOknrWA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB9UD7Wi7ediFOb3lC7fPhKnX9gBVHgyOxlsFB5+FCE0AiBaDpvsIUytkS0X3sB2SZHv6ZnV7iU6WxwM0XiFFAzpRQ=="}]}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "directories": {}}, "0.0.3": {"name": "utile", "description": "A drop-in replacement for `util` with some additional advantageous functions", "version": "0.0.3", "author": {"name": "Nodejitsu Inc", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/flatiron/utile.git"}, "dependencies": {"async": "0.1.x", "mkdirp": "0.x.x", "ncp": "0.2.x", "rimraf": "1.x.x"}, "devDependencies": {"vows": "0.5.x"}, "main": "./lib/index", "engines": {"node": ">= 0.4.0"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "_id": "utile@0.0.3", "_engineSupported": true, "_npmVersion": "1.0.103", "_nodeVersion": "v0.4.11", "_defaultsLoaded": true, "dist": {"shasum": "c89a7a1e9bb64dd77defd5ff33aa08043a98474e", "tarball": "https://registry.npmjs.org/utile/-/utile-0.0.3.tgz", "integrity": "sha512-Zx+kFhe7iSnoECKeCB78Eue9UQYuRSB3PiKRRD8WcWSuKd/4NYlOe2Hrw7kqBKTDKkOPe+YVNFzW5W26U5thHQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHylpiffim+RNK1lhg61B+Fj2JRFYGq9DRqWDksxwOUqAiEA/jiatvMF8zFh9szfD+JMqBg7Joh3b5GaoQ+6ZsskPBE="}]}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "directories": {}}, "0.0.4": {"name": "utile", "description": "A drop-in replacement for `util` with some additional advantageous functions", "version": "0.0.4", "author": {"name": "Nodejitsu Inc", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/flatiron/utile.git"}, "dependencies": {"async": "0.1.x", "mkdirp": "0.x.x", "ncp": "0.2.x", "rimraf": "1.x.x"}, "devDependencies": {"vows": "0.5.x"}, "main": "./lib/index", "engines": {"node": ">= 0.4.0"}, "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "_id": "utile@0.0.4", "_engineSupported": true, "_npmVersion": "1.0.103", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"shasum": "f4761e345ef0c5778d81cf8fd118c5da47b19a52", "tarball": "https://registry.npmjs.org/utile/-/utile-0.0.4.tgz", "integrity": "sha512-Z7kpv4GEQ+kGHk/Ws/13IfFMRUlfwrb3HjJQ1UGSICMHBismt92KJF2BBNvKHLe5aeUzbt5d9UuyA7rgbHSjBA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGggM0q4hQE1tImPef0Tew5xjOD12WiCE8b9jt/wxS0fAiEAzvpFkKodrMr4ljynZOuawtxJ7+nHUPVD+MJjCL+/maI="}]}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "dominictarr", "email": "<EMAIL>"}], "directories": {}}, "0.0.5": {"name": "utile", "description": "A drop-in replacement for `util` with some additional advantageous functions", "version": "0.0.5", "author": {"name": "Nodejitsu Inc", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/flatiron/utile.git"}, "dependencies": {"async": "0.1.x", "mkdirp": "0.x.x", "ncp": "0.2.x", "rimraf": "1.x.x"}, "devDependencies": {"vows": "0.5.x"}, "main": "./lib/index", "engines": {"node": ">= 0.4.0"}, "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "_id": "utile@0.0.5", "_engineSupported": true, "_npmVersion": "1.0.103", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"shasum": "e054818913263e4b29ebbff071bad3440dadb8f9", "tarball": "https://registry.npmjs.org/utile/-/utile-0.0.5.tgz", "integrity": "sha512-Rcgj67Wotdbvak/Yx2LqiRdkzv5DwNF/DgXFHBXLXf3FntjU3cYqNEB1CfPiudY9iVHLi/6LugdcbD+EMjGecQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDqme+MlwPOcnBHd3gFcVNZ6naZYNerrw7pVKWpGltYIgIgHgcxwgmZ3JX1kiSMtg7Wl25TNT+/URpsHbr3rseDr/g="}]}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "dominictarr", "email": "<EMAIL>"}], "directories": {}}, "0.0.6": {"name": "utile", "description": "A drop-in replacement for `util` with some additional advantageous functions", "version": "0.0.6", "author": {"name": "Nodejitsu Inc", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/flatiron/utile.git"}, "dependencies": {"async": "0.1.x", "mkdirp": "0.x.x", "ncp": "0.2.x", "rimraf": "1.x.x"}, "devDependencies": {"vows": "0.5.x"}, "main": "./lib/index", "engines": {"node": ">= 0.4.0"}, "_npmUser": {"name": "dominictarr", "email": "<EMAIL>"}, "_id": "utile@0.0.6", "_engineSupported": true, "_npmVersion": "1.0.103", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"shasum": "f7b39d920cee957b64773ee41f979328431883f3", "tarball": "https://registry.npmjs.org/utile/-/utile-0.0.6.tgz", "integrity": "sha512-OiiwyxSorUb64MVkNH9W7uLLVVu4tnBEHLmg3fddTdEIp5oEMCvm4bnyb2pxnVuj/px+h4dtqSb7F555l7C7tg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDUJA2OWPbDfOCDZwg6uoyTPMgxGwVo/3OPj0z41EOKtAIhAIn4c9THJaTvKzpm82GUjDqjCC7D86jdRht0bKMvr7kJ"}]}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "dominictarr", "email": "<EMAIL>"}], "directories": {}}, "0.0.8": {"name": "utile", "description": "A drop-in replacement for `util` with some additional advantageous functions", "version": "0.0.8", "author": {"name": "Nodejitsu Inc", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/flatiron/utile.git"}, "dependencies": {"async": "0.1.x", "mkdirp": "0.x.x", "ncp": "0.2.x", "rimraf": "1.x.x"}, "devDependencies": {"vows": "0.5.x"}, "scripts": {"test": "vows --spec --isolate"}, "main": "./lib/index", "engines": {"node": ">= 0.4.0"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "_id": "utile@0.0.8", "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"shasum": "edddb801ceeb16a25461e0c16b1fc16c668b1234", "tarball": "https://registry.npmjs.org/utile/-/utile-0.0.8.tgz", "integrity": "sha512-IjvgRdMFPwE5EG/OMKmSA+ohzoOJbCO/xoTwEY9IAhtKDog6y4QOAj7SFWU24kD6AGgm8NUSRwgh6oTO1Sy9Fw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCplT9cr1QiQAhF6dLpmIjib0ddxG3oMyf6zSuouX3hKgIgQ9qHzBh+ENvWxoGHZuuDHbrfnnrXJm8STVUwP+xMrio="}]}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "directories": {}}, "0.0.9": {"name": "utile", "description": "A drop-in replacement for `util` with some additional advantageous functions", "version": "0.0.9", "author": {"name": "Nodejitsu Inc", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/flatiron/utile.git"}, "dependencies": {"async": "0.1.x", "mkdirp": "0.x.x", "ncp": "0.2.x", "rimraf": "1.x.x"}, "devDependencies": {"vows": "0.5.x"}, "scripts": {"test": "vows --spec --isolate"}, "main": "./lib/index", "engines": {"node": ">= 0.4.0"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "_id": "utile@0.0.9", "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"shasum": "8212910c45f45b9412a2562c214bb2211146d81e", "tarball": "https://registry.npmjs.org/utile/-/utile-0.0.9.tgz", "integrity": "sha512-nEuSe4+UV50pb8ONN2esnTGzD+CtoxISPE2Nb272eeWyG8+Gdtrr2WySQo49jBVcEAaX2mldKHq285lnKHzxhA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHl2sitvxI29Yb35ruRuivpnK33PxtqxV6Gh+dsXKvX4AiEAtHe8W6jUZ8irHve3BzCI90ZIXfbz0Dy5TmmtX9tLfqc="}]}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "directories": {}}, "0.0.10": {"name": "utile", "description": "A drop-in replacement for `util` with some additional advantageous functions", "version": "0.0.10", "author": {"name": "Nodejitsu Inc", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/flatiron/utile.git"}, "dependencies": {"async": "0.1.x", "mkdirp": "0.x.x", "ncp": "0.2.x", "rimraf": "1.x.x"}, "devDependencies": {"vows": "0.6.x"}, "scripts": {"test": "vows --spec --isolate"}, "main": "./lib/index", "engines": {"node": ">= 0.4.0"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "_id": "utile@0.0.10", "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"shasum": "a45345b6e90d679c48c8d215ab724e3ee3dafc18", "tarball": "https://registry.npmjs.org/utile/-/utile-0.0.10.tgz", "integrity": "sha512-orR19o6KeKEWTu1QLPDNTIv37Nfr+LWTkZ2ZTLdbqBsLeYiLNc25K4Fx7VcLt/82t7yqequUYAGXn3x+aNz9wg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGVaSYCNYk62LpqqtgZs7Mh7dDB7/Spg/bpeWJdH8huSAiEAkRAFrbK2HSv0DYz4Oc/G7UwK8n/GzVddeOKalSbg9pM="}]}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "directories": {}}, "0.1.0": {"name": "utile", "description": "A drop-in replacement for `util` with some additional advantageous functions", "version": "0.1.0", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/flatiron/utile.git"}, "dependencies": {"async": "0.1.x", "mkdirp": "0.x.x", "ncp": "0.2.x", "rimraf": "1.x.x"}, "devDependencies": {"vows": "0.6.x"}, "scripts": {"test": "vows --spec --isolate"}, "main": "./lib/index", "engines": {"node": ">= 0.6.4"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "_id": "utile@0.1.0", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.2", "_nodeVersion": "v0.6.10", "_defaultsLoaded": true, "dist": {"shasum": "6aea68476399168de42153f36636077edc78f158", "tarball": "https://registry.npmjs.org/utile/-/utile-0.1.0.tgz", "integrity": "sha512-0O3suh0X54FCkKnG8FmtMRldcCx/LP98Fiqw2U/YwtJEWddpgt/2038zlSJOX/Ryu0G8bh4YJh0EZw+CklSWyw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFL8QUExIt1XJKXXeM3+ljj4c+yOUF9VUxMUeg8yxwGvAiEAmAW80Ihgpqgv3UJZNBKxKbOzX/Xx1NN8wagEZH1kH4g="}]}, "directories": {}}, "0.1.1": {"name": "utile", "description": "A drop-in replacement for `util` with some additional advantageous functions", "version": "0.1.1", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/flatiron/utile.git"}, "dependencies": {"async": "0.1.x", "i": "0.3.x", "mkdirp": "0.x.x", "ncp": "0.2.x", "rimraf": "1.x.x"}, "devDependencies": {"vows": "0.6.x"}, "scripts": {"test": "vows --spec --isolate"}, "main": "./lib/index", "engines": {"node": ">= 0.6.4"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "_id": "utile@0.1.1", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.2", "_nodeVersion": "v0.6.18", "_defaultsLoaded": true, "dist": {"shasum": "fd6c8c20541053bc43fe8b36e5098043de3d45ba", "tarball": "https://registry.npmjs.org/utile/-/utile-0.1.1.tgz", "integrity": "sha512-+GtvQtrpipWw1M5DBh27EiukH8Qd38x43iGllFVxv3dDr7PkhkBdMG9QizNfDyWWLAPmz6Mvlz9t5jXNF3RYww==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEEzYM9cY8EDSMRmiUEPmXFoB7e30po8vjE5yUUm+MOQAiA0bJ9dRaSf3oEh0sxCM8Ib9z0q7Y8O2msu6uosKQzhcg=="}]}, "directories": {}}, "0.1.2": {"name": "utile", "description": "A drop-in replacement for `util` with some additional advantageous functions", "version": "0.1.2", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/flatiron/utile.git"}, "dependencies": {"async": "0.1.x", "deep-equal": "*", "i": "0.3.x", "mkdirp": "0.x.x", "ncp": "0.2.x", "rimraf": "1.x.x"}, "devDependencies": {"vows": "0.6.x"}, "scripts": {"test": "vows --spec"}, "main": "./lib/index", "engines": {"node": ">= 0.6.4"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "_id": "utile@0.1.2", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.2", "_nodeVersion": "v0.6.18", "_defaultsLoaded": true, "dist": {"shasum": "fbad3181778608325172e16ffbbbafd125704441", "tarball": "https://registry.npmjs.org/utile/-/utile-0.1.2.tgz", "integrity": "sha512-aMX80L5t1a5wXLUNi6skIVnfWqdb8iORHMjFTkKz0KAYHFXs8fuL1PP0fwl40rjpJdlU6vPJImtBO7qcoO/kCQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC2d9TQeY3KPfn/EEJoqisY4/1x8u4gaJERJ384IlB7PAiAF3ljHpmEZGMOS1rO6p8xOEodLzhNtxNk1NcivJotz7Q=="}]}, "directories": {}}, "0.1.3": {"name": "utile", "description": "A drop-in replacement for `util` with some additional advantageous functions", "version": "0.1.3", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/flatiron/utile.git"}, "dependencies": {"async": "0.1.x", "deep-equal": "*", "i": "0.3.x", "mkdirp": "0.x.x", "ncp": "0.2.x", "rimraf": "1.x.x"}, "devDependencies": {"vows": "0.6.x"}, "scripts": {"test": "vows --spec"}, "main": "./lib/index", "engines": {"node": ">= 0.6.4"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "_id": "utile@0.1.3", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.2", "_nodeVersion": "v0.8.1", "_defaultsLoaded": true, "dist": {"shasum": "12595deb17434645d79b04cf6878567e1ae29a46", "tarball": "https://registry.npmjs.org/utile/-/utile-0.1.3.tgz", "integrity": "sha512-26U/bu7ICclXUYdpZgmsK0+BCaPXDUddUBGsLZEMOkmERk+oRqnuPdyYbDzEUce22VVFkCyH7HphPLyo8Q9vUg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBoSst5fDIgMpvfOi9Bq6y5mCy4npimX6mGdigKy4KGkAiAI2unLDDAVORvkto7IP+Tpn88B/NovmEmTxRqpmLqs3g=="}]}, "directories": {}}, "0.1.5": {"name": "utile", "description": "A drop-in replacement for `util` with some additional advantageous functions", "version": "0.1.5", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/flatiron/utile.git"}, "dependencies": {"async": "0.1.x", "deep-equal": "*", "i": "0.3.x", "mkdirp": "0.x.x", "ncp": "0.2.x", "rimraf": "1.x.x"}, "devDependencies": {"vows": "0.6.x"}, "scripts": {"test": "vows --spec"}, "main": "./lib/index", "engines": {"node": ">= 0.6.4"}, "_id": "utile@0.1.5", "dist": {"shasum": "3762399a9304cb966b9166f48edc01dd61cdf6a8", "tarball": "https://registry.npmjs.org/utile/-/utile-0.1.5.tgz", "integrity": "sha512-wTP8hJ58nEMPmAo8lPUpCFUdQH/45FRjH4E7mnmgJ4nagZhtSrPaf0nEDBnpnawjr8Mk00wF5mfOeRj4lIXrIg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCMwro2Vef8HFxg1Jg0i57mpwPtMdBukxAdK9eL6TxIxwIhANio4JUsQV/ekDEegYC7YrRY+DFyxWys4vnPuiTCorWu"}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "directories": {}}, "0.1.6": {"name": "utile", "description": "A drop-in replacement for `util` with some additional advantageous functions", "version": "0.1.6", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/flatiron/utile.git"}, "dependencies": {"async": "0.1.x", "deep-equal": "*", "i": "0.3.x", "mkdirp": "0.x.x", "ncp": "0.2.x", "rimraf": "1.x.x"}, "devDependencies": {"vows": "0.6.x"}, "scripts": {"test": "vows --spec"}, "main": "./lib/index", "engines": {"node": ">= 0.6.4"}, "_id": "utile@0.1.6", "dist": {"shasum": "9ae4e796a96311463abe6b1890e4c6ef7aa8053e", "tarball": "https://registry.npmjs.org/utile/-/utile-0.1.6.tgz", "integrity": "sha512-+nvDADlHZOtUA2ksZHSVerwvHGvl9ZQw9i9Lw5OIMeh7KCbywszjokqpwTrseUQo6LB/7nDCFS6cPzapuXKabg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDEmBrF8WMuxBYQRCYzmWwETo07m1X4Q04N7E/DWkq7PwIhAOdiNXsZSlD1390h8Sl8EYMF2l642m9fgvhUxdAxV9ta"}]}, "_npmVersion": "1.1.63", "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "directories": {}}, "0.1.7": {"name": "utile", "description": "A drop-in replacement for `util` with some additional advantageous functions", "version": "0.1.7", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/flatiron/utile.git"}, "dependencies": {"async": "0.1.x", "deep-equal": "*", "i": "0.3.x", "mkdirp": "0.x.x", "ncp": "0.2.x", "rimraf": "1.x.x"}, "devDependencies": {"vows": "0.6.x"}, "scripts": {"test": "vows --spec"}, "main": "./lib/index", "engines": {"node": ">= 0.6.4"}, "_id": "utile@0.1.7", "dist": {"shasum": "55db180d54475339fd6dd9e2d14a4c0b52624b69", "tarball": "https://registry.npmjs.org/utile/-/utile-0.1.7.tgz", "integrity": "sha512-ODuep9+cqmpklRDjEPS1JtY27+zg3MUfeWf8/NOVIJeMJB2nZBOemPXnIeseVkFcvGq9euiIMkS4HiUAEIFbow==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBg8yAP7Q6E+TIYjDpIOCbV9Peb79GAQQvzyRfFxcTsHAiAVo9C17JmofXTeJsmsX12OpzX6PRTi6jhi796Zj4fbcw=="}]}, "_npmVersion": "1.1.70", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}}, "0.2.0": {"name": "utile", "description": "A drop-in replacement for `util` with some additional advantageous functions", "version": "0.2.0", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/flatiron/utile.git"}, "dependencies": {"async": "0.1.x", "deep-equal": "*", "i": "0.3.x", "mkdirp": "0.x.x", "ncp": "0.2.x", "rimraf": "2.x.x"}, "devDependencies": {"vows": "0.6.x"}, "scripts": {"test": "vows --spec"}, "main": "./lib/index", "engines": {"node": ">= 0.6.4"}, "_id": "utile@0.2.0", "dist": {"shasum": "91a2423ca2eb3322390e211ee3d71cf4fa193aea", "tarball": "https://registry.npmjs.org/utile/-/utile-0.2.0.tgz", "integrity": "sha512-BdR5Ty5wc+CDGVp2Zoowjm7RcZ4NXqOjKwKYl4qbxe/J8Wh7s9Ym9vSfhplG985Z7SkCrgxI62sb/jxRljHfKw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHNtll9LMFiglDNku+F80mMWeeb2Q0nzumfecUOKvOR7AiEA5bmFfmr0KN+i0LaCWgJBly0ZI5MBtyvEDvOGiSV5YXk="}]}, "_from": ".", "_npmVersion": "1.2.13", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}}, "0.2.1": {"name": "utile", "description": "A drop-in replacement for `util` with some additional advantageous functions", "version": "0.2.1", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/flatiron/utile.git"}, "dependencies": {"async": "~0.2.9", "deep-equal": "*", "i": "0.3.x", "mkdirp": "0.x.x", "ncp": "0.4.x", "rimraf": "2.x.x"}, "devDependencies": {"vows": "0.7.x"}, "scripts": {"test": "vows --spec"}, "main": "./lib/index", "engines": {"node": ">= 0.6.4"}, "bugs": {"url": "https://github.com/flatiron/utile/issues"}, "_id": "utile@0.2.1", "dist": {"shasum": "930c88e99098d6220834c356cbd9a770522d90d7", "tarball": "https://registry.npmjs.org/utile/-/utile-0.2.1.tgz", "integrity": "sha512-ltfvuCJNa/JFOhKBBiQ9qDyyFwLstoMMO1ru0Yg/Mcl8dp1Z3IBaL7n+5dHpyma+d3lCogkgBQnWKtGxzNyqhg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCwb3uvKe3w+tXhsMDtPT/XoD3OofxKf7uSxNoFjvLEdQIhANJJ0CCbbSxZRvUSEOTq6IHqQ5lnRlXws80515IqDF5u"}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}}, "0.3.0": {"name": "utile", "description": "A drop-in replacement for `util` with some additional advantageous functions", "version": "0.3.0", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/flatiron/utile.git"}, "dependencies": {"async": "~0.9.0", "deep-equal": "~0.2.1", "i": "0.3.x", "mkdirp": "0.x.x", "ncp": "1.0.x", "rimraf": "2.x.x"}, "devDependencies": {"vows": "0.8.x"}, "scripts": {"test": "vows --spec"}, "main": "./lib/index", "engines": {"node": ">= 0.8.0"}, "gitHead": "6aafee124fb76d92f9c56d46b8574d6b7606ac4b", "bugs": {"url": "https://github.com/flatiron/utile/issues"}, "homepage": "https://github.com/flatiron/utile", "_id": "utile@0.3.0", "_shasum": "1352c340eb820e4d8ddba039a4fbfaa32ed4ef3a", "_from": ".", "_npmVersion": "2.1.9", "_nodeVersion": "0.10.33", "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "dist": {"shasum": "1352c340eb820e4d8ddba039a4fbfaa32ed4ef3a", "tarball": "https://registry.npmjs.org/utile/-/utile-0.3.0.tgz", "integrity": "sha512-KaciY16ate/pJ7BAwBpVcfQlgJT02WRivIv8DlCX1cvg6WxaNEXHcdqazuS9fQ7PUoU5CH2UeY3wkqq16wRiWg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQClqfzdXxy0fwV/ci40VC01ktn7BlYghCKp6I8K5b8ExwIgARWAQlrycAMIY112mwtlt76QOei7IvZs7p+UiFzXQQI="}]}}}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "time": {"modified": "2022-06-28T07:15:30.285Z", "created": "2011-10-31T21:22:38.147Z", "0.0.1": "2011-10-31T21:22:39.264Z", "0.0.2": "2011-10-31T21:32:41.377Z", "0.0.3": "2011-11-01T08:51:33.164Z", "0.0.4": "2011-11-09T01:26:32.712Z", "0.0.5": "2011-11-09T12:44:51.865Z", "0.0.6": "2011-11-13T00:18:43.028Z", "0.0.8": "2011-11-26T00:50:46.574Z", "0.0.9": "2011-11-29T23:59:23.542Z", "0.0.10": "2011-12-08T23:46:38.402Z", "0.1.0": "2012-05-23T14:04:33.987Z", "0.1.1": "2012-06-07T19:44:09.573Z", "0.1.2": "2012-06-26T23:15:43.519Z", "0.1.3": "2012-07-25T07:44:09.589Z", "0.1.5": "2012-09-21T07:29:51.400Z", "0.1.6": "2012-11-12T17:12:28.172Z", "0.1.7": "2013-01-13T13:35:50.069Z", "0.2.0": "2013-03-17T11:30:46.384Z", "0.2.1": "2013-12-02T06:33:58.185Z", "0.3.0": "2014-12-07T23:07:24.301Z"}, "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "http://github.com/flatiron/utile.git"}, "users": {"blakmatrix": true, "fgribreau": true, "pid": true, "brentonhouse": true, "bojand": true, "dm7": true}, "bugs": {"url": "https://github.com/flatiron/utile/issues"}, "readme": "# utile [![Build Status](https://secure.travis-ci.org/flatiron/utile.png)](http://travis-ci.org/flatiron/utile)\n\nA drop-in replacement for `util` with some additional advantageous functions\n\n## Motivation\nJavascript is definitely a \"batteries not included language\" when compared to languages like Ruby or Python. Node.js has a simple utility library which exposes some basic (but important) functionality:\n\n```\n$ node\n> var util = require('util');\n> util.\n(...)\n\nutil.debug                 util.error                 util.exec                  util.inherits              util.inspect\nutil.log                   util.p                     util.print                 util.pump                  util.puts\n```\n\nWhen one considers their own utility library, why ever bother requiring `util` again? That is the approach taken by this module. To compare:\n\n```\n$ node\n> var utile = require('./lib')\n> utile.\n(...)\n\nutile.async                 utile.capitalize            utile.clone                 utile.cpr                   utile.createPath            utile.debug\nutile.each                  utile.error                 utile.exec                  utile.file                  utile.filter                utile.find\nutile.inherits              utile.log                   utile.mixin                 utile.mkdirp                utile.p                     utile.path\nutile.print                 utile.pump                  utile.puts                  utile.randomString          utile.requireDir            uile.requireDirLazy\nutile.rimraf\n```\n\nAs you can see all of the original methods from `util` are there, but there are several new methods specific to `utile`. A note about implementation: _no node.js native modules are modified by utile, it simply copies those methods._\n\n## Methods\nThe `utile` modules exposes some simple utility methods:\n\n* `.each(obj, iterator)`: Iterate over the keys of an object.\n* `.mixin(target [source0, source1, ...])`: Copies enumerable properties from `source0 ... sourceN` onto `target` and returns the resulting object.\n* `.clone(obj)`: Shallow clones the specified object.\n* `.capitalize(str)`: Capitalizes the specified `str`.\n* `.randomString(length)`: randomString returns a pseudo-random ASCII string (subset) the return value is a string of length ⌈bits/6⌉ of characters from the base64 alphabet.\n* `.filter(obj, test)`: return an object with the properties that `test` returns true on.\n* `.args(arguments)`: Converts function arguments into actual array with special `callback`, `cb`, `array`, and `last` properties. Also supports *optional* argument contracts. See [the example](https://github.com/flatiron/utile/blob/master/examples/utile-args.js) for more details.\n* `.requireDir(directory)`: Requires all files and directories from `directory`, returning an object with keys being filenames (without trailing `.js`) and respective values being return values of `require(filename)`.\n* `.requireDirLazy(directory)`: Lazily requires all files and directories from `directory`, returning an object with keys being filenames (without trailing `.js`) and respective values (getters) being return values of `require(filename)`.\n* `.format([string] text, [array] formats, [array] replacements)`: Replace `formats` in `text` with `replacements`. This will fall back to the original `util.format` command if it is called improperly.\n\n## Packaged Dependencies\nIn addition to the methods that are built-in, utile includes a number of commonly used dependencies to reduce the number of includes in your package.json. These modules _are not eagerly loaded to be respectful of startup time,_ but instead are lazy-loaded getters on the `utile` object\n\n* `.async`: [Async utilities for node and the browser][0]\n* `.inflect`: [Customizable inflections for node.js][6]\n* `.mkdirp`: [Recursively mkdir, like mkdir -p, but in node.js][1]\n* `.rimraf`: [A rm -rf util for nodejs][2]\n* `.cpr`: [Asynchronous recursive file copying with Node.js][3]\n\n## Installation\n\n### Installing npm (node package manager)\n```\n  curl http://npmjs.org/install.sh | sh\n```\n\n### Installing utile\n```\n  [sudo] npm install utile\n```\n\n## Tests\nAll tests are written with [vows][4] and should be run with [npm][5]:\n\n``` bash\n  $ npm test\n```\n\n#### Author: [Charlie Robbins](http://github.com/indexzero)\n#### Contributors: [Dominic Tarr](http://github.com/dominictarr), [Marak Squires](https://github.com/marak)\n#### License: MIT\n\n[0]: https://github.com/caolan/async\n[1]: https://github.com/substack/node-mkdirp\n[2]: https://github.com/isaacs/rimraf\n[3]: https://github.com/avianflu/ncp\n[4]: https://vowsjs.org\n[5]: https://npmjs.org\n[6]: https://github.com/pksunkara/inflect\n", "readmeFilename": "README.md", "homepage": "https://github.com/flatiron/utile"}