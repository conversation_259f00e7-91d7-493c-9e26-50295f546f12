{"_id": "@mapbox/node-pre-gyp", "_rev": "26-********************************", "name": "@mapbox/node-pre-gyp", "dist-tags": {"alpha": "1.0.0-alpha1", "latest": "2.0.0", "dev": "1.1.0-dev.1", "next": "2.0.0-rc.0"}, "versions": {"1.0.0-alpha1": {"name": "@mapbox/node-pre-gyp", "version": "1.0.0-alpha1", "keywords": ["native", "addon", "module", "c", "c++", "bindings", "binary"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@mapbox/node-pre-gyp@1.0.0-alpha1", "maintainers": [{"name": "mapbox-npm-01", "email": "<EMAIL>"}, {"name": "mapbox-npm-02", "email": "<EMAIL>"}, {"name": "mapbox-npm-07", "email": "<EMAIL>"}, {"name": "mapbox-npm-03", "email": "<EMAIL>"}, {"name": "mapbox-npm-04", "email": "<EMAIL>"}, {"name": "mapbox-npm-09", "email": "<EMAIL>"}, {"name": "mapbox-npm-05", "email": "<EMAIL>"}, {"name": "mapbox-npm-06", "email": "<EMAIL>"}, {"name": "mapbox-npm-08", "email": "<EMAIL>"}, {"name": "mapbox-npm-advanced-actions", "email": "<EMAIL>"}, {"name": "mapbox-npm-ci", "email": "<EMAIL>"}, {"name": "mapbox-npm", "email": "<EMAIL>"}, {"name": "mapbox-admin", "email": "<EMAIL>"}, {"name": "mapbox-machine-user", "email": "<EMAIL>"}], "homepage": "https://github.com/mapbox/node-pre-gyp#readme", "bugs": {"url": "https://github.com/mapbox/node-pre-gyp/issues"}, "bin": {"node-pre-gyp": "bin/node-pre-gyp"}, "nyc": {"all": true, "exclude": ["test/**"], "skip-full": false}, "dist": {"shasum": "be3428f9f98f6838ee4a6e7e59e03980e81d13bb", "tarball": "https://registry.npmjs.org/@mapbox/node-pre-gyp/-/node-pre-gyp-1.0.0-alpha1.tgz", "fileCount": 31, "integrity": "sha512-MCeM5c7mWKJpQiYX2ovwlyCJYOCVosiQ4yo60RHaoldi+M3IJiotr+pGElhO7fHZNkQ5Ds5CogHt6/e8R7obYQ==", "signatures": [{"sig": "MEUCIQDLi9xznkyme8wJrYzdllGFmtjO13ZQy6Zp4uSOh70rAQIgEdw90qjQxHCriEXjfgi7ED3GjvQ9qyDFyJ9tKNzIs9A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160560, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgCZrNCRA9TVsSAnZWagAAKT4QAJKV9icH3xxiaJmB7wY6\n4pPyMDwAg+0UZMdgL0RPkgmZd0Y1iSBMocgTTAvBPqf8OIO9dPcodUdLdATt\nEgGaRHwTJE15LGZwQxFireoTIG72+deahdGIcVgKgBu3QUo9hyR9P39pKCes\nJDOqoq0larnkt5BGCARPtdvsP26XTKxkzMt7gM434kvphElNW/H+cm1P5V7D\nEmMTe41o6QQPQnbCPC8f5TTJNRUPD/AfMbXHG9kxXHpovyJuRfFnpGNH14b0\nuFUDZNn3GDutVQ1UuJDT444S41Sm1rFP8JHIwIJGHJHaPaa+9V6noJrZAM/D\nfOSW6v7uwZM8Vfa2DV2c1Uo5+qrFFsWpngBUnLc14sdDPdG0Hu2bdlenQSAx\nAB++uBFgJSC0ef0rTa6yRurGKtPJAwcLXbnIprbfFy6b7IjMCcoSE41/ljiA\nbe7cYlVZ+vSF4ZnCJsK/qGpxhNkeAFZWvlVZkDByHKodsGI3423B6CLoPJlf\nYVAFcCNkZEWtwauf/Gt1AH+CAEbo44I4NuMZNCgNHqule6D16yuOnxKIgdHU\nU5Kx2PpSOADPSD1i0gNhAQk2mLCDhdTMEdoWOJfDYwkw66N/R+Tbhw1MHRI+\nuZ19NyQh1NC2YjSEYT20HDS87eFebjg8R8MDxpJylOOD9iUDvXBFASZ8k58F\nsCyG\r\n=LnoF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/node-pre-gyp.js", "gitHead": "40be814a89144ee0d4d6d603e58e51539deba781", "scripts": {"fix": "npm run lint -- --fix", "lint": "eslint bin/node-pre-gyp lib/*js lib/util/*js test/*js scripts/*js", "test": "tape test/*test.js", "coverage": "nyc --all --include index.js --include lib/ npm test", "upload-coverage": "nyc report --reporter json && codecov --clear --flags=unit --file=./coverage/coverage-final.json", "update-crosswalk": "node scripts/abi_crosswalk.js"}, "_npmUser": {"name": "mapbox-npm-07", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mapbox/node-pre-gyp.git", "type": "git"}, "_npmVersion": "6.14.6", "description": "Node.js native addon binary install tool", "directories": {}, "_nodeVersion": "10.22.1", "dependencies": {"rc": "^1.2.8", "tar": "^6.1.0", "nopt": "^5.0.0", "mkdirp": "^0.5.5", "needle": "^2.6.0", "npmlog": "^4.1.2", "rimraf": "^3.0.2", "semver": "^7.3.4", "detect-libc": "^1.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "nock": "^12.0.3", "tape": "^5.1.1", "eslint": "^7.18.0", "aws-sdk": "^2.829.0", "codecov": "^3.8.1", "mock-aws-s3": "^4.0.1", "node-addon-api": "^3.1.0", "eslint-plugin-node": "^11.1.0", "@mapbox/cloudfriend": "^4.6.0", "@mapbox/eslint-config-mapbox": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-pre-gyp_1.0.0-alpha1_1611242188708_0.5986204038761558", "host": "s3://npm-registry-packages"}}, "1.0.0-alpha2": {"name": "@mapbox/node-pre-gyp", "version": "1.0.0-alpha2", "keywords": ["native", "addon", "module", "c", "c++", "bindings", "binary"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@mapbox/node-pre-gyp@1.0.0-alpha2", "maintainers": [{"name": "mapbox-npm-01", "email": "<EMAIL>"}, {"name": "mapbox-npm-02", "email": "<EMAIL>"}, {"name": "mapbox-npm-07", "email": "<EMAIL>"}, {"name": "mapbox-npm-03", "email": "<EMAIL>"}, {"name": "mapbox-npm-04", "email": "<EMAIL>"}, {"name": "mapbox-npm-09", "email": "<EMAIL>"}, {"name": "mapbox-npm-05", "email": "<EMAIL>"}, {"name": "mapbox-npm-06", "email": "<EMAIL>"}, {"name": "mapbox-npm-08", "email": "<EMAIL>"}, {"name": "mapbox-npm-advanced-actions", "email": "<EMAIL>"}, {"name": "mapbox-npm-ci", "email": "<EMAIL>"}, {"name": "mapbox-npm", "email": "<EMAIL>"}, {"name": "mapbox-admin", "email": "<EMAIL>"}, {"name": "mapbox-machine-user", "email": "<EMAIL>"}], "homepage": "https://github.com/mapbox/node-pre-gyp#readme", "bugs": {"url": "https://github.com/mapbox/node-pre-gyp/issues"}, "bin": {"node-pre-gyp": "bin/node-pre-gyp"}, "nyc": {"all": true, "exclude": ["test/**"], "skip-full": false}, "dist": {"shasum": "03d2a92ba0fca446057b6a51a1444b1cceff8efe", "tarball": "https://registry.npmjs.org/@mapbox/node-pre-gyp/-/node-pre-gyp-1.0.0-alpha2.tgz", "fileCount": 31, "integrity": "sha512-zz/Hke4QPWhq2xXOO1YaGZbun4+1j6H5zNgaof8yMAccPGLm0tEZuKmbgNpTOux2Dvsx5sq0e345+LVgL3m29w==", "signatures": [{"sig": "MEUCIQCq1QRwHmkLQ8G6qGxUG65ixaIPS6mOIsXywrLy4FN19QIgfm5GOvyp33Bvdy33EDpjq8DgjZF7AQJ7J/QbwRZ7jL4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160560, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgCZ1OCRA9TVsSAnZWagAAZJ4P/172kRLlt+J0Vz6TwXB+\nz9FGnK47pHhftFqD+z/nkJ13f7iQHGY0sx/f2VD7cZ75tMAwvBzI7MrALoC/\nJ/bQx/sA566OQpettNheF9R4Qq/01sk7LVtXgwvZfhlTtNxkYQtE1bCvrsGV\nX1qUMgqW+aaEsnT+NIaaggh76J+gj4Fh1at76OGnec78XYnyKMjn2uqqw0V+\nsmppqw7HkTUWL8PGYHyvIFuU7x0eArZnUs6rNM2gHWPb6rHTlu6t7iwTmHu7\n20zd2K2HrHL3oRGV5cj2OsUqboG43Rg3XzxQwBkMtDkAsoQDk6+1ooB/InEX\nHDf8NR/PO+dZDdE0pRqdykXVhRXfsFblxIWFq090AA3LonjfJX2qVuOMEcyZ\nEBuUkgggPIRa48KX2/Ynlz2KaSrcOhGL/y+1SgzA4Z/wt/RpKF/C6/bcXlsm\nzqqVrhceDpTuz9UEyEGo5m1slIUFoQLso6xfJ9SuQmVxjH7HwsR2HwDizVO6\neZe0IHaMqT8wbi42NIvBcGKj7/L4X/6BKlaRFxBmWPPG8xUG+p1dmOn1M2LJ\n7jHaxzg7tEbYTuV6+FtLou3WHxH18XEwBHOR5aLKVVY1eXWGuAoGiXQMSNuZ\nwYxycpWYhW8KQUnER77XcRlP/qY0KVYeeJL+WT+3sFPjF4kfDC9ZQrViu1zK\nXLOr\r\n=pjrS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/node-pre-gyp.js", "gitHead": "39ebad26f32c2a410d2dda3643e307f65d80341e", "scripts": {"fix": "npm run lint -- --fix", "lint": "eslint bin/node-pre-gyp lib/*js lib/util/*js test/*js scripts/*js", "test": "tape test/*test.js", "coverage": "nyc --all --include index.js --include lib/ npm test", "upload-coverage": "nyc report --reporter json && codecov --clear --flags=unit --file=./coverage/coverage-final.json", "update-crosswalk": "node scripts/abi_crosswalk.js"}, "_npmUser": {"name": "mapbox-npm-01", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mapbox/node-pre-gyp.git", "type": "git"}, "_npmVersion": "6.14.6", "description": "Node.js native addon binary install tool", "directories": {}, "_nodeVersion": "10.22.1", "dependencies": {"rc": "^1.2.8", "tar": "^6.1.0", "nopt": "^5.0.0", "mkdirp": "^0.5.5", "needle": "^2.6.0", "npmlog": "^4.1.2", "rimraf": "^3.0.2", "semver": "^7.3.4", "detect-libc": "^1.0.3"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^15.1.0", "nock": "^12.0.3", "tape": "^5.1.1", "eslint": "^7.18.0", "aws-sdk": "^2.829.0", "codecov": "^3.8.1", "mock-aws-s3": "^4.0.1", "node-addon-api": "^3.1.0", "eslint-plugin-node": "^11.1.0", "@mapbox/cloudfriend": "^4.6.0", "@mapbox/eslint-config-mapbox": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-pre-gyp_1.0.0-alpha2_1611242830334_0.7433535105206137", "host": "s3://npm-registry-packages"}}, "1.0.0-alpha3": {"name": "@mapbox/node-pre-gyp", "version": "1.0.0-alpha3", "keywords": ["native", "addon", "module", "c", "c++", "bindings", "binary"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@mapbox/node-pre-gyp@1.0.0-alpha3", "maintainers": [{"name": "mapbox-npm-01", "email": "<EMAIL>"}, {"name": "mapbox-npm-02", "email": "<EMAIL>"}, {"name": "mapbox-npm-07", "email": "<EMAIL>"}, {"name": "mapbox-npm-03", "email": "<EMAIL>"}, {"name": "mapbox-npm-04", "email": "<EMAIL>"}, {"name": "mapbox-npm-09", "email": "<EMAIL>"}, {"name": "mapbox-npm-05", "email": "<EMAIL>"}, {"name": "mapbox-npm-06", "email": "<EMAIL>"}, {"name": "mapbox-npm-08", "email": "<EMAIL>"}, {"name": "mapbox-npm-advanced-actions", "email": "<EMAIL>"}, {"name": "mapbox-npm-ci", "email": "<EMAIL>"}, {"name": "mapbox-npm", "email": "<EMAIL>"}, {"name": "mapbox-admin", "email": "<EMAIL>"}, {"name": "mapbox-machine-user", "email": "<EMAIL>"}], "homepage": "https://github.com/mapbox/node-pre-gyp#readme", "bugs": {"url": "https://github.com/mapbox/node-pre-gyp/issues"}, "bin": {"node-pre-gyp": "bin/node-pre-gyp"}, "nyc": {"all": true, "exclude": ["test/**"], "skip-full": false}, "dist": {"shasum": "c31666a8977564c6bb1b3445ef640177b913136f", "tarball": "https://registry.npmjs.org/@mapbox/node-pre-gyp/-/node-pre-gyp-1.0.0-alpha3.tgz", "fileCount": 31, "integrity": "sha512-spN4K7juzTzLGJUv2utxEwG5UpxTqh0WLQHLNPD4cOjnYUcsfrgyMHaS4Ut1Dy2mlzBAzW018nMvaGJYnrtHXQ==", "signatures": [{"sig": "MEQCIAH+7wEZI2Aa6eu2uH4duJ4inTvlNkPsqxbHi8Ocaq8yAiAG2TNTXkUfPC3MAylR1O7xZ7AbrgMZkm12ad4WePKC1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160560, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgDy4eCRA9TVsSAnZWagAAuFcP/A74j3oSSFpKrlxe7XeC\nLWWHvj6ZXUdOxKUK6t+NFJZT48gD9JnRrLEy/p7D/3ceoFd5aBm0ByvrbCHE\nhkMsd+uRVcBro/25RTVqC+HSbyke0ARcN0bitGYFAJGJnl3bcdbxkddq5haZ\nkgNQhK3H/X8nVtas/4WJq8CJxyv6lOHjfy3n8eu15SR0vwXsXhvD1YG5bQWE\nhusXIBmZV4WV9GFdM8F3xsbbRCV0dU7FuTQuEq0AafKS83xBnDfEXbAIhvmM\njBR4brPhT7BswG9EJ6p/PLW7x3/JcqMZLUJwryqpLMgLYYx+We213zI25vRW\nCqYU75dugYoGhrOsgKsR7w8EMZ3Qoh1o7VefLax7FXnw/HIxbeLQQy5hpZM/\nYGE5qqk3+sWFr1sHXKtU7EDNGQwDZCk8gq7laPOtEAGiLP4FkWo3T76bFDtG\nC5So8JW+DEXkNnesrUcLgjhp9u/JmDthwVYre8ur7+ns7hDPJtvp3xPNN9/F\nJnipweYRFKrGqbA9FUpae1tLcoGI8JDeH+wfqasY3IJE/pdP5JZOs6qEJN+L\nw1D4QJrsANMOI21iEWF6ybhTJV7/lrwBJcQRu/q6wmadFDjOrJBc/gnObeud\nuw1M6QNhI7OsFxEXzOZLXAjjOmerzt11qLnxFPCQ7KD6u54ARMqae/OZDeib\n2sqg\r\n=VtwL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/node-pre-gyp.js", "gitHead": "c1bc1e8e41b31157fd90c35a537e438025dc0631", "scripts": {"fix": "npm run lint -- --fix", "lint": "eslint bin/node-pre-gyp lib/*js lib/util/*js test/*js scripts/*js", "test": "tape test/*test.js", "coverage": "nyc --all --include index.js --include lib/ npm test", "upload-coverage": "nyc report --reporter json && codecov --clear --flags=unit --file=./coverage/coverage-final.json", "update-crosswalk": "node scripts/abi_crosswalk.js"}, "_npmUser": {"name": "mapbox-npm-03", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mapbox/node-pre-gyp.git", "type": "git"}, "_npmVersion": "6.14.6", "description": "Node.js native addon binary install tool", "directories": {}, "_nodeVersion": "10.22.1", "dependencies": {"rc": "^1.2.8", "tar": "^6.1.0", "nopt": "^5.0.0", "mkdirp": "^0.5.5", "needle": "^2.6.0", "npmlog": "^4.1.2", "rimraf": "^3.0.2", "semver": "^7.3.4", "detect-libc": "^1.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "nock": "^12.0.3", "tape": "^5.1.1", "eslint": "^7.18.0", "aws-sdk": "^2.829.0", "codecov": "^3.8.1", "mock-aws-s3": "^4.0.1", "node-addon-api": "^3.1.0", "eslint-plugin-node": "^11.1.0", "@mapbox/cloudfriend": "^4.6.0", "@mapbox/eslint-config-mapbox": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-pre-gyp_1.0.0-alpha3_1611607582379_0.871035560196691", "host": "s3://npm-registry-packages"}}, "1.0.0-alpha4": {"name": "@mapbox/node-pre-gyp", "version": "1.0.0-alpha4", "keywords": ["native", "addon", "module", "c", "c++", "bindings", "binary"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@mapbox/node-pre-gyp@1.0.0-alpha4", "maintainers": [{"name": "mapbox-npm-01", "email": "<EMAIL>"}, {"name": "mapbox-npm-02", "email": "<EMAIL>"}, {"name": "mapbox-npm-07", "email": "<EMAIL>"}, {"name": "mapbox-npm-03", "email": "<EMAIL>"}, {"name": "mapbox-npm-04", "email": "<EMAIL>"}, {"name": "mapbox-npm-09", "email": "<EMAIL>"}, {"name": "mapbox-npm-05", "email": "<EMAIL>"}, {"name": "mapbox-npm-06", "email": "<EMAIL>"}, {"name": "mapbox-npm-08", "email": "<EMAIL>"}, {"name": "mapbox-npm-advanced-actions", "email": "<EMAIL>"}, {"name": "mapbox-npm-ci", "email": "<EMAIL>"}, {"name": "mapbox-npm", "email": "<EMAIL>"}, {"name": "mapbox-admin", "email": "<EMAIL>"}, {"name": "mapbox-machine-user", "email": "<EMAIL>"}], "homepage": "https://github.com/mapbox/node-pre-gyp#readme", "bugs": {"url": "https://github.com/mapbox/node-pre-gyp/issues"}, "bin": {"node-pre-gyp": "bin/node-pre-gyp"}, "nyc": {"all": true, "exclude": ["test/**"], "skip-full": false}, "dist": {"shasum": "35f5eea52c36ddfb5fa8bf3898c5ee1ede9521d1", "tarball": "https://registry.npmjs.org/@mapbox/node-pre-gyp/-/node-pre-gyp-1.0.0-alpha4.tgz", "fileCount": 31, "integrity": "sha512-VLL5ilVuo9EtA6W5KVLSH1CkoKd8QdUGRoFqZX2sFByi2O3t+6xpv/QxddU2zS+fT63BSVaHtJPL56T/aKPuAw==", "signatures": [{"sig": "MEUCIHFLt3lmvTjVSBlgiWgB1xr3mLV0XFyvNpdctEXcRrlrAiEA5ypoQISvnlfNKLew0+QG56T/mKlLngN0gda1R2eXkis=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160319, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgIWpzCRA9TVsSAnZWagAAXxwP/jc9nI/C9y8ODlNciQyq\ng3zCUf35oLzqAKXkTi2TzkX1SKc8EGVv96tpnrNymHU/kgCLJrw2D2SLbKdb\nFIuN2HE/ZZVVIRny/yoynO/NZDBoRZPWETVQbUIeXBHRjM4wM3th/wF6EFSU\ncEvh+QMir+UHi+0fUpHqsoDMSVju+S4uRQA0elbVqIcdO0wLhyUeErlwAhr8\nOQGNXAOpD8o580VIgRhBbn81YpH6ltpmr86HyiUlHZvwzJ/5fC1r6M5s85UP\ntCG+iT1lfwqSLg9D6Fl9TJ08j97nSpFf5wutCmlr+G+qFltPmnopxJNhqBTb\nMUfqjbj4YNG1zugke8HAQ77S0SVx6GbfyhBSS6vZTwfpbgSufoSlBfgaE5cw\n+O0JIrjzrZie1KVIwJM6vP92S1Sm/bzl+m4L0ZwlJPjz7WmNTUFpU1kiK0T0\nZuou9eEw72mcQ0JUkP50uJW+onpQHXnafWAhm6rtmEuWDaZIFOXzE3ukX8Hn\nPh9zOhG+H8eRhdLzpl0zPERIWqsXf3j70w4LdbHo2SprW7mJzF/m2O+dsa8c\n+ELqadaTEMPiRb+C7X0vCbiTS4xAhzOsiIji7zH0U8ncNvIKHFyd8UAgxpUE\n2w9pma2pk3wNozYygxri4mP1ERI7aiNedryYZm5dl8fNrW7pQ6rR/J8wZAdV\n3HnS\r\n=idCW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/node-pre-gyp.js", "gitHead": "3ba8321c9d7229377af44d79f87d4904b95d6f22", "scripts": {"fix": "npm run lint -- --fix", "lint": "eslint bin/node-pre-gyp lib/*js lib/util/*js test/*js scripts/*js", "test": "tape test/*test.js", "coverage": "nyc --all --include index.js --include lib/ npm test", "upload-coverage": "nyc report --reporter json && codecov --clear --flags=unit --file=./coverage/coverage-final.json", "update-crosswalk": "node scripts/abi_crosswalk.js"}, "_npmUser": {"name": "mapbox-npm-08", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mapbox/node-pre-gyp.git", "type": "git"}, "_npmVersion": "6.14.6", "description": "Node.js native addon binary install tool", "directories": {}, "_nodeVersion": "10.22.1", "dependencies": {"tar": "^6.1.0", "nopt": "^5.0.0", "mkdirp": "^0.5.5", "needle": "^2.6.0", "npmlog": "^4.1.2", "rimraf": "^3.0.2", "semver": "^7.3.4", "detect-libc": "^1.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "nock": "^12.0.3", "tape": "^5.1.1", "eslint": "^7.18.0", "aws-sdk": "^2.829.0", "codecov": "^3.8.1", "mock-aws-s3": "^4.0.1", "node-addon-api": "^3.1.0", "eslint-plugin-node": "^11.1.0", "@mapbox/cloudfriend": "^4.6.0", "@mapbox/eslint-config-mapbox": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-pre-gyp_1.0.0-alpha4_1612802674690_0.9959561890797894", "host": "s3://npm-registry-packages"}}, "1.0.0-alpha5": {"name": "@mapbox/node-pre-gyp", "version": "1.0.0-alpha5", "keywords": ["native", "addon", "module", "c", "c++", "bindings", "binary"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@mapbox/node-pre-gyp@1.0.0-alpha5", "maintainers": [{"name": "mapbox-npm-01", "email": "<EMAIL>"}, {"name": "mapbox-npm-02", "email": "<EMAIL>"}, {"name": "mapbox-npm-07", "email": "<EMAIL>"}, {"name": "mapbox-npm-03", "email": "<EMAIL>"}, {"name": "mapbox-npm-04", "email": "<EMAIL>"}, {"name": "mapbox-npm-09", "email": "<EMAIL>"}, {"name": "mapbox-npm-05", "email": "<EMAIL>"}, {"name": "mapbox-npm-06", "email": "<EMAIL>"}, {"name": "mapbox-npm-08", "email": "<EMAIL>"}, {"name": "mapbox-npm-advanced-actions", "email": "<EMAIL>"}, {"name": "mapbox-npm-ci", "email": "<EMAIL>"}, {"name": "mapbox-npm", "email": "<EMAIL>"}, {"name": "mapbox-admin", "email": "<EMAIL>"}, {"name": "mapbox-machine-user", "email": "<EMAIL>"}], "homepage": "https://github.com/mapbox/node-pre-gyp#readme", "bugs": {"url": "https://github.com/mapbox/node-pre-gyp/issues"}, "bin": {"node-pre-gyp": "bin/node-pre-gyp"}, "nyc": {"all": true, "exclude": ["test/**"], "skip-full": false}, "dist": {"shasum": "1341478b1e7b4ac502620271e102bff26d6da101", "tarball": "https://registry.npmjs.org/@mapbox/node-pre-gyp/-/node-pre-gyp-1.0.0-alpha5.tgz", "fileCount": 31, "integrity": "sha512-ONvbc8nVEoM+EAD8o+FWsFHCWaMlC4IBqqPmpiDDf04BCB5LxX4FNiJagsLtSMZW7+bdMPbTqJQSjXzp5Ui2bw==", "signatures": [{"sig": "MEQCIEpncjtSdQ5gcep/ame5elzzYbs3e76/Faj+QkoG/U5mAiBXomykBG9mnPFHlVJRIbXNCcCoeUkUWuzcUkLB1/kc/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 159403, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgIgT1CRA9TVsSAnZWagAAH64P/jjdP97ZQAU9L10aJLCJ\nTF4lGl/GT561tfeisMLAKaioIzq7wTYD1zqt80bKhjZkt3vK2uQsqvKT3r5C\nUSd3wviVRkfLFCa0XzB+nCsSPcSYUiE8GxJlQudXpZcNOFCL8JfvDKhwUrIQ\nJ2KTNYGGrBm3BShaz7u9d4eszsVCywjAtrkTyf0789N4A6s46ov1AvY5Vlst\n8kRpLp+ldJ2eRwoBpnhnBjjmJO0LS47tuPypOuM928z05ww5sPuHDXNJ5f1r\ngUPpWJWlrXAJTBmF7TI7Ikrv2Mr9P4vH1VToW+e8spGr9+Z4MSWv44wHPpox\n1Sj08zKyRcqw2mjZF2xYCcOgxmbKZyYw8m+4YKDYEdN9nH65ncrZfNyOWxAa\nCLGf34yBIdqYzE168ueLtJUjU68QhGKfOLJcYIzjSX5+ehlkfDz8YioCjM0X\n7B6PAuFQv5xHlOugSGmwWIoyT/yaw5bOnn18qUNNx/b6WR5kdZ8QkMwrZmqi\n+i+fZrllZxLGn/A3Q86rMINE1AYORxNSDj8BfJcK2E24aMyKaL5BfVPtEF05\nMgnFA/W/euf6+MoBDqgpPD2cE80L3kx2QV4E8jNZktwuT9tr3rEG8LXIMqnG\nVn/z77zBUy07AaBGjPcAccF9Z+WslFP6Sb86ERsidIdjY7v/4PepfhO7FPpp\nMpWT\r\n=2BLV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/node-pre-gyp.js", "gitHead": "d8c86427e7e8a9018635e60e9fd79b4b609c29de", "scripts": {"fix": "npm run lint -- --fix", "lint": "eslint bin/node-pre-gyp lib/*js lib/util/*js test/*js scripts/*js", "test": "tape test/*test.js", "coverage": "nyc --all --include index.js --include lib/ npm test", "upload-coverage": "nyc report --reporter json && codecov --clear --flags=unit --file=./coverage/coverage-final.json", "update-crosswalk": "node scripts/abi_crosswalk.js"}, "_npmUser": {"name": "mapbox-npm-03", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mapbox/node-pre-gyp.git", "type": "git"}, "_npmVersion": "6.14.6", "description": "Node.js native addon binary install tool", "directories": {}, "_nodeVersion": "10.22.1", "dependencies": {"tar": "^6.1.0", "nopt": "^5.0.0", "mkdirp": "^0.5.5", "npmlog": "^4.1.2", "rimraf": "^3.0.2", "semver": "^7.3.4", "node-fetch": "^2.6.1", "detect-libc": "^1.0.3", "http-proxy-agent": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "nock": "^12.0.3", "tape": "^5.1.1", "eslint": "^7.18.0", "aws-sdk": "^2.829.0", "codecov": "^3.8.1", "action-walk": "^2.2.0", "mock-aws-s3": "^4.0.1", "node-addon-api": "^3.1.0", "eslint-plugin-node": "^11.1.0", "@mapbox/cloudfriend": "^4.6.0", "@mapbox/eslint-config-mapbox": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-pre-gyp_1.0.0-alpha5_1612842229316_0.8779895728234091", "host": "s3://npm-registry-packages"}}, "1.0.0-alpha6": {"name": "@mapbox/node-pre-gyp", "version": "1.0.0-alpha6", "keywords": ["native", "addon", "module", "c", "c++", "bindings", "binary"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@mapbox/node-pre-gyp@1.0.0-alpha6", "maintainers": [{"name": "mapbox-npm-01", "email": "<EMAIL>"}, {"name": "mapbox-npm-02", "email": "<EMAIL>"}, {"name": "mapbox-npm-07", "email": "<EMAIL>"}, {"name": "mapbox-npm-03", "email": "<EMAIL>"}, {"name": "mapbox-npm-04", "email": "<EMAIL>"}, {"name": "mapbox-npm-09", "email": "<EMAIL>"}, {"name": "mapbox-npm-05", "email": "<EMAIL>"}, {"name": "mapbox-npm-06", "email": "<EMAIL>"}, {"name": "mapbox-npm-08", "email": "<EMAIL>"}, {"name": "mapbox-npm-advanced-actions", "email": "<EMAIL>"}, {"name": "mapbox-npm-ci", "email": "<EMAIL>"}, {"name": "mapbox-npm", "email": "<EMAIL>"}, {"name": "mapbox-admin", "email": "<EMAIL>"}, {"name": "mapbox-machine-user", "email": "<EMAIL>"}], "homepage": "https://github.com/mapbox/node-pre-gyp#readme", "bugs": {"url": "https://github.com/mapbox/node-pre-gyp/issues"}, "bin": {"node-pre-gyp": "bin/node-pre-gyp"}, "nyc": {"all": true, "exclude": ["test/**"], "skip-full": false}, "dist": {"shasum": "98f3e8545facba0ce169b7c6fd00f10af58f342a", "tarball": "https://registry.npmjs.org/@mapbox/node-pre-gyp/-/node-pre-gyp-1.0.0-alpha6.tgz", "fileCount": 31, "integrity": "sha512-H6BXD3nSIzE6sjF5ErlQAp8mSh3eVJXvb22O3jTxOPAk/42ZoDFBBjg5mZlVEyihPX/S4PmWUOBvarv0oG4hLg==", "signatures": [{"sig": "MEYCIQCMQic25Mtwxs710LgW4cKFYu6IFm0J+liUQZ4+89yaNwIhAMtgDw1zgxTSSGzTkq9qSs7/TyS6pPSF1COWJ6xnVzL7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 159360, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgIhaICRA9TVsSAnZWagAAgV0P/1znmEWEEPJwOh3buLWI\nvIB8BjAwpEvhR2DRiSvq8Rn3wXPnrGobjbd6fra75jEE7whsX52fYEbyGDKP\n9d5SQTJ09dsjVDY08RTVv+KZmVxJTDE/DhLIgk7z0eFqKyDy45PBjDFLX49t\n7fgzeJkTaWVwXrMGWa1DPcWzfdoyKImJg0dXMF5bbT4vopc/elnih8DnSU6x\n/jIBe0nWani5pU33FUIhonJkvkLVW1jt0jUykQIFdCgvEBRkYhBV6PYBzv+o\nFMbxkqgKcItO/4CuJx6kfy9qvpNkPy8SF4b0v7g4Od/EPp7tTRlKsNQQsRMs\n29bw2AYqO9ayYfz3FJzZrr6dnRtnzmg9FpaWMdAy4EsIeoVflLhT+56YMgYE\nbCCQT+KQmPK8HbcCVmx5EfZjoX/SXCOQjkv9lUXsM0qXX2RqLDmgnDlq+Q9b\n+N5pZUA6xIZ04RW5T7w9jSZytN7BIi3ONztf6dyTmJfiOU+a5YL1+65MTtCx\npE4Si0NVwpI0HfrwdP8GB2rUjipWNAPhNSjr3TZ67ptARCSMr0y4nONoxIcH\nWmO0tFmV7zrUNgEy6uxYzF0WSFXymxOSR3dayzroSfPrhAk/1SGUclJLEZMw\n5rGoym9g/JDzZ6AH2/MUWIoeMbUokVdRQMKgYKNuDkm4uiaTWMcTrNeVUDM3\n4sw7\r\n=EVXc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/node-pre-gyp.js", "gitHead": "50a07bb7239fc9d0c36fc6fc07a025bd624961c8", "scripts": {"fix": "npm run lint -- --fix", "lint": "eslint bin/node-pre-gyp lib/*js lib/util/*js test/*js scripts/*js", "test": "tape test/*test.js", "coverage": "nyc --all --include index.js --include lib/ npm test", "upload-coverage": "nyc report --reporter json && codecov --clear --flags=unit --file=./coverage/coverage-final.json", "update-crosswalk": "node scripts/abi_crosswalk.js"}, "_npmUser": {"name": "mapbox-npm-03", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mapbox/node-pre-gyp.git", "type": "git"}, "_npmVersion": "6.14.6", "description": "Node.js native addon binary install tool", "directories": {}, "_nodeVersion": "10.22.1", "dependencies": {"tar": "^6.1.0", "nopt": "^5.0.0", "mkdirp": "^1.0.4", "npmlog": "^4.1.2", "rimraf": "^3.0.2", "semver": "^7.3.4", "node-fetch": "^2.6.1", "detect-libc": "^1.0.3", "http-proxy-agent": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "nock": "^12.0.3", "tape": "^5.1.1", "eslint": "^7.18.0", "aws-sdk": "^2.840.0", "codecov": "^3.8.1", "action-walk": "^2.2.0", "mock-aws-s3": "^4.0.1", "node-addon-api": "^3.1.0", "eslint-plugin-node": "^11.1.0", "@mapbox/cloudfriend": "^4.6.0", "@mapbox/eslint-config-mapbox": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-pre-gyp_1.0.0-alpha6_1612846727566_0.810613317204476", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "@mapbox/node-pre-gyp", "version": "1.0.0", "keywords": ["native", "addon", "module", "c", "c++", "bindings", "binary"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@mapbox/node-pre-gyp@1.0.0", "maintainers": [{"name": "mapbox-npm-01", "email": "<EMAIL>"}, {"name": "mapbox-npm-02", "email": "<EMAIL>"}, {"name": "mapbox-npm-07", "email": "<EMAIL>"}, {"name": "mapbox-npm-03", "email": "<EMAIL>"}, {"name": "mapbox-npm-04", "email": "<EMAIL>"}, {"name": "mapbox-npm-09", "email": "<EMAIL>"}, {"name": "mapbox-npm-05", "email": "<EMAIL>"}, {"name": "mapbox-npm-06", "email": "<EMAIL>"}, {"name": "mapbox-npm-08", "email": "<EMAIL>"}, {"name": "mapbox-npm-advanced-actions", "email": "<EMAIL>"}, {"name": "mapbox-npm-ci", "email": "<EMAIL>"}, {"name": "mapbox-npm", "email": "<EMAIL>"}, {"name": "mapbox-admin", "email": "<EMAIL>"}, {"name": "mapbox-machine-user", "email": "<EMAIL>"}], "homepage": "https://github.com/mapbox/node-pre-gyp#readme", "bugs": {"url": "https://github.com/mapbox/node-pre-gyp/issues"}, "bin": {"node-pre-gyp": "bin/node-pre-gyp"}, "nyc": {"all": true, "exclude": ["test/**"], "skip-full": false}, "dist": {"shasum": "2b809e701da0f6729b47fe78ad4b9dc187a7d2e5", "tarball": "https://registry.npmjs.org/@mapbox/node-pre-gyp/-/node-pre-gyp-1.0.0.tgz", "fileCount": 31, "integrity": "sha512-mEaiD1CURETR/dBIiJAwz0M0Q0mH3gCW4pPMaIlNt97mdzYUVeqGcTJSamgJpS6Tg4tBHDrOJpjdh5fJTLnyNQ==", "signatures": [{"sig": "MEQCIDXcJp8zzRjjxmIb2hj+kVGUvwMcWNngdEk2adgGl93XAiAuhyFhYtxKFSG78Jwkx9f0tzbVwut8ii2+IdfnXUO7Pg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 159822, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgIr8UCRA9TVsSAnZWagAAbnEP/Rsi0AuRAD5/SicfNCKp\nZhkLL1fSEUNRirNzVsY1dhHplMJLYdGqaTWxlZhThBQsalb+f4mQOvG1AADm\nJmT28PeB0gp6SdwfYCbxk7G2He1lnpNm1eGruw1oTmyjyokSn/q0kTb94cSb\ntel0MPD+iG86irVwKDT9TVH3h17l9fQUkRkM7Il1HGDOjsza8jg0wrCkz1Gc\nkAXvFMKlsCSuqyjXVx5Hfoh5m/jL7/u53jGMybIKgG8FZwztubkmeTx6xWkp\nt8CUM/zKdEVdnD/bsX06G29swXcNV6Ll7+Fyz1BtaJJjn1jtrpx/oiyF++Rb\nXVVrh9Ufp65eVscx/HUUTfTbJGEu90W4hZmuf4oCuz/i9zPXXQ9x4asbum65\nuBTDocVwuedL0l/od+eEIEkF03vTw/AaOj3NOBmSwHDrfXyys49F/w6USPko\n6ob+y/IBLBquigI/FtR7MuaZPbAWHYxA/QCw22pvEIGfpoKTMzHNp5sc942G\nW/y0+gopH/knkBR9kLBnml6wiqlcdZM2E8plGLg9qbQj0EppdVNPtJ8WF7bx\nr/8uECJFe7VYNBzRYUrQNifyDk+P7siABjMt0z9sUQRe562Ab5Udqo2iB5EP\npmP9D4hcyRdWuBCzUbMvDFSqo2MLy49ThLJ6Z5pq6NSILB/QcBr5llKKfmFh\nNt+t\r\n=/Ig3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/node-pre-gyp.js", "gitHead": "22404cbd1c2bcf615cf4085d21db58511006c3b4", "scripts": {"fix": "npm run lint -- --fix", "lint": "eslint bin/node-pre-gyp lib/*js lib/util/*js test/*js scripts/*js", "test": "tape test/*test.js", "coverage": "nyc --all --include index.js --include lib/ npm test", "upload-coverage": "nyc report --reporter json && codecov --clear --flags=unit --file=./coverage/coverage-final.json", "update-crosswalk": "node scripts/abi_crosswalk.js"}, "_npmUser": {"name": "mapbox-npm-03", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mapbox/node-pre-gyp.git", "type": "git"}, "_npmVersion": "6.14.6", "description": "Node.js native addon binary install tool", "directories": {}, "_nodeVersion": "10.22.1", "dependencies": {"tar": "^6.1.0", "nopt": "^5.0.0", "mkdirp": "^1.0.4", "npmlog": "^4.1.2", "rimraf": "^3.0.2", "semver": "^7.3.4", "node-fetch": "^2.6.1", "detect-libc": "^1.0.3", "http-proxy-agent": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "nock": "^12.0.3", "tape": "^5.1.1", "eslint": "^7.18.0", "aws-sdk": "^2.840.0", "codecov": "^3.8.1", "action-walk": "^2.2.0", "mock-aws-s3": "^4.0.1", "node-addon-api": "^3.1.0", "eslint-plugin-node": "^11.1.0", "@mapbox/cloudfriend": "^4.6.0", "@mapbox/eslint-config-mapbox": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-pre-gyp_1.0.0_1612889875516_0.6687705952656322", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "@mapbox/node-pre-gyp", "version": "1.0.1", "keywords": ["native", "addon", "module", "c", "c++", "bindings", "binary"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@mapbox/node-pre-gyp@1.0.1", "maintainers": [{"name": "mapbox-npm-01", "email": "<EMAIL>"}, {"name": "mapbox-npm-02", "email": "<EMAIL>"}, {"name": "mapbox-npm-07", "email": "<EMAIL>"}, {"name": "mapbox-npm-03", "email": "<EMAIL>"}, {"name": "mapbox-npm-04", "email": "<EMAIL>"}, {"name": "mapbox-npm-09", "email": "<EMAIL>"}, {"name": "mapbox-npm-05", "email": "<EMAIL>"}, {"name": "mapbox-npm-06", "email": "<EMAIL>"}, {"name": "mapbox-npm-08", "email": "<EMAIL>"}, {"name": "mapbox-npm-advanced-actions", "email": "<EMAIL>"}, {"name": "mapbox-npm-ci", "email": "<EMAIL>"}, {"name": "mapbox-npm", "email": "<EMAIL>"}, {"name": "mapbox-admin", "email": "<EMAIL>"}, {"name": "mapbox-machine-user", "email": "<EMAIL>"}], "homepage": "https://github.com/mapbox/node-pre-gyp#readme", "bugs": {"url": "https://github.com/mapbox/node-pre-gyp/issues"}, "bin": {"node-pre-gyp": "bin/node-pre-gyp"}, "nyc": {"all": true, "exclude": ["test/**"], "skip-full": false}, "dist": {"shasum": "1b23a8decb5e6356b04770d586067d2bff2703dd", "tarball": "https://registry.npmjs.org/@mapbox/node-pre-gyp/-/node-pre-gyp-1.0.1.tgz", "fileCount": 31, "integrity": "sha512-CUBdThIZMoLEQQxACwhLsPg/puxBca0abTH3ixuvBQkhjJ80Hdp99jmVjxFCOa52/tZqN9d70IbGUf+OuKDHGA==", "signatures": [{"sig": "MEYCIQDz9nBSiE/9Cc/DcHsaoDF0MJAdkHRkl3HcKfIAc/QllgIhAN5NCbJ7Q6vCXuUHLd6/+RLv4U0dsI6jdCd2JBjnqdbB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 161571, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgRqS0CRA9TVsSAnZWagAAmLcP/RSySD/YS3VvwuhN9Q9L\n7ykKfJa9uMOXXR4eBJ/aidn0JwQ0e4CHYBVkDoTINqk08WQIUQQ11Ldh3mKC\nJRAFeEtwC8/GgwnGFX4E9EgBASIDUtXAXA/NF21uIS9QeMUE+k/+OGxtJwq2\ntv0lLaNP2DAyFmz93xfZrDuaYx85x1SPs3YaX5D6jbUncGIJg9Ugob3yoFoo\n01wP3l0c8vLSDVwAsMEdQnJGH3gFcHHl7+GGvvCSn8szmODgr7Bsxpuv5MSK\nmPEaOP4duBphCecJbOkDwG7fdakmfeq4gH+36P/U+05TbR23ydEYXUzyoW3W\nxiJQXO+2vxi3ZXMdWNiSJfWk4SUKmISblkh45ybx1l5twe89nsW4v/xmsb5y\nJJM8EhRJU6oWBhgLgrMP/nErMzlsGfYTIUHAWtKEpLjM9R3ixK34UFtI5cKk\nhsoYpmbI+ejHijR0DEK7ppyiUWGZcgBoHzFnoFPi5CZYma2BetQX5HJLD3/B\nIfh3Fp/MGfdtlxMs8e/uKchBiRCFARdAEzGM55U+GHh3rjdwxc6mZZbFq5OP\nbyeFcM7e6dTkXgY/pyM/r5mtGRl5nwBxkQ8s6Z3UcVg7qzQb4jA4VHvc9Y0d\nfDUtaAm9OiE+eRjgIAizPaYbHztejVozruEtRvYLxr/u4lYC/hD3NF7uqoBc\n5D5N\r\n=bWa2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/node-pre-gyp.js", "gitHead": "f9b39484f17955d83cdab42c178a600467fe96bd", "scripts": {"fix": "npm run lint -- --fix", "lint": "eslint bin/node-pre-gyp lib/*js lib/util/*js test/*js scripts/*js", "test": "tape test/*test.js", "coverage": "nyc --all --include index.js --include lib/ npm test", "upload-coverage": "nyc report --reporter json && codecov --clear --flags=unit --file=./coverage/coverage-final.json", "update-crosswalk": "node scripts/abi_crosswalk.js"}, "_npmUser": {"name": "mapbox-npm-06", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mapbox/node-pre-gyp.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Node.js native addon binary install tool", "directories": {}, "_nodeVersion": "14.16.0", "dependencies": {"tar": "^6.1.0", "nopt": "^5.0.0", "npmlog": "^4.1.2", "rimraf": "^3.0.2", "semver": "^7.3.4", "make-dir": "^3.1.0", "node-fetch": "^2.6.1", "detect-libc": "^1.0.3", "http-proxy-agent": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "nock": "^12.0.3", "tape": "^5.1.1", "eslint": "^7.18.0", "aws-sdk": "^2.840.0", "codecov": "^3.8.1", "action-walk": "^2.2.0", "mock-aws-s3": "^4.0.1", "node-addon-api": "^3.1.0", "eslint-plugin-node": "^11.1.0", "@mapbox/cloudfriend": "^4.6.0", "@mapbox/eslint-config-mapbox": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-pre-gyp_1.0.1_1615242420168_0.18558068005994777", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "@mapbox/node-pre-gyp", "version": "1.0.2", "keywords": ["native", "addon", "module", "c", "c++", "bindings", "binary"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@mapbox/node-pre-gyp@1.0.2", "maintainers": [{"name": "mapbox-npm-01", "email": "<EMAIL>"}, {"name": "mapbox-npm-02", "email": "<EMAIL>"}, {"name": "mapbox-npm-07", "email": "<EMAIL>"}, {"name": "mapbox-npm-03", "email": "<EMAIL>"}, {"name": "mapbox-npm-04", "email": "<EMAIL>"}, {"name": "mapbox-npm-09", "email": "<EMAIL>"}, {"name": "mapbox-npm-05", "email": "<EMAIL>"}, {"name": "mapbox-npm-06", "email": "<EMAIL>"}, {"name": "mapbox-npm-08", "email": "<EMAIL>"}, {"name": "mapbox-npm-advanced-actions", "email": "<EMAIL>"}, {"name": "mapbox-npm-ci", "email": "<EMAIL>"}, {"name": "mapbox-npm", "email": "<EMAIL>"}, {"name": "mapbox-admin", "email": "<EMAIL>"}, {"name": "mapbox-machine-user", "email": "<EMAIL>"}], "homepage": "https://github.com/mapbox/node-pre-gyp#readme", "bugs": {"url": "https://github.com/mapbox/node-pre-gyp/issues"}, "bin": {"node-pre-gyp": "bin/node-pre-gyp"}, "nyc": {"all": true, "exclude": ["test/**"], "skip-full": false}, "dist": {"shasum": "c5f9261ba86afcc7085a8250ee9fa222b0238be9", "tarball": "https://registry.npmjs.org/@mapbox/node-pre-gyp/-/node-pre-gyp-1.0.2.tgz", "fileCount": 31, "integrity": "sha512-5FRuyoOeDBR7tR+zj+pH52j1uQ2Cyp01umBcvFOw+Q6zs6gqN7FWz/k6cbaxKAxsjBuGTyMMG8aKgt+B9PX4cQ==", "signatures": [{"sig": "MEUCIQCY0Znu9bmArUHfEq+zYExxh7yrO9kJjZoaXvNRDxvhcAIgRMp0VixXjNJg3T+BWk+BoyNnYPCQbVUs0dDKo92M+Lg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 161153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgYkOYCRA9TVsSAnZWagAAxJgP/RKZLJqOLB4SKm2gG4TM\npd4TbbFVpzQoWkdFyxOe2jcZDkP679VOYztPSYxaMWIM1RNOxBaJRQWMqV4+\nC90rDkPp4+vBU6GBcOEd55Ui9YtEM3NIBYlAys6bVTNZ0THg1X7HEkZhxtxm\nMHXhNhcWlEpZy92QY3NKJmVQlwVrg/WhJRfoEQHAACRdL4O1UHSN8lidUcFP\ncFTMYEYFv33MlIJ0GHn14/zkCsvA0NCf5Tl33A/SYcAMWpBlHCmnoFXCsNiq\nmQTpVKkRuJnzWqcE9gE2Hpq1N8iEYdDA6sRMTEf6frDsTGHImieGq4Ilw6Js\nnneyrIv32mVNJI5JTRPuiUuU1vjC1eteHq6WrdDUe7QopDlGjqNZSIsdLePi\n3SlKGUw1g7N4/S1o2V0sZamifJs0DlYfvH4idf5zJH8qsBkDDgBY5/bFSrku\nyn6kkaOGOh+nXVTnilvzr/VtmUxTigHyD+OYGuWPIi3X5pXH99l5yEZi1fDF\n6RTuChFSgJ9GvmYx+4QaKCKxAMQemTdf1nxk+VxxlLI8k7xJbwXPyqTIMHHi\n6nF/3azdjoBq5tON12MyPt7Ff/r4mBVl/K7X5Vjd9kgBJ/jR07vTGysaA7Os\ncrYU0cFO6rHXYxDHuQoNgieaMC5PI3/uGgBv8EcoFYql1CfPvi3r6HbLtz7H\nCSMh\r\n=5bza\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/node-pre-gyp.js", "gitHead": "7da93d8fd5c1924557884fb4273925d9411b1df4", "scripts": {"fix": "npm run lint -- --fix", "lint": "eslint bin/node-pre-gyp lib/*js lib/util/*js test/*js scripts/*js", "test": "tape test/*test.js", "coverage": "nyc --all --include index.js --include lib/ npm test", "upload-coverage": "nyc report --reporter json && codecov --clear --flags=unit --file=./coverage/coverage-final.json", "update-crosswalk": "node scripts/abi_crosswalk.js"}, "_npmUser": {"name": "mapbox-npm-09", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mapbox/node-pre-gyp.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Node.js native addon binary install tool", "directories": {}, "_nodeVersion": "14.16.0", "dependencies": {"tar": "^6.1.0", "nopt": "^5.0.0", "npmlog": "^4.1.2", "rimraf": "^3.0.2", "semver": "^7.3.4", "make-dir": "^3.1.0", "node-fetch": "^2.6.1", "detect-libc": "^1.0.3", "https-proxy-agent": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "nock": "^12.0.3", "tape": "^5.2.2", "eslint": "^7.18.0", "tar-fs": "^2.1.1", "aws-sdk": "^2.840.0", "codecov": "^3.8.1", "action-walk": "^2.2.0", "mock-aws-s3": "^4.0.1", "node-addon-api": "^3.1.0", "eslint-plugin-node": "^11.1.0", "@mapbox/cloudfriend": "^4.6.0", "@mapbox/eslint-config-mapbox": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-pre-gyp_1.0.2_1617052567693_0.06537195548160346", "host": "s3://npm-registry-packages"}}, "1.0.3": {"name": "@mapbox/node-pre-gyp", "version": "1.0.3", "keywords": ["native", "addon", "module", "c", "c++", "bindings", "binary"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@mapbox/node-pre-gyp@1.0.3", "maintainers": [{"name": "mapbox-npm-01", "email": "<EMAIL>"}, {"name": "mapbox-npm-02", "email": "<EMAIL>"}, {"name": "mapbox-npm-07", "email": "<EMAIL>"}, {"name": "mapbox-npm-03", "email": "<EMAIL>"}, {"name": "mapbox-npm-04", "email": "<EMAIL>"}, {"name": "mapbox-npm-09", "email": "<EMAIL>"}, {"name": "mapbox-npm-05", "email": "<EMAIL>"}, {"name": "mapbox-npm-06", "email": "<EMAIL>"}, {"name": "mapbox-npm-08", "email": "<EMAIL>"}, {"name": "mapbox-npm-advanced-actions", "email": "<EMAIL>"}, {"name": "mapbox-npm-ci", "email": "<EMAIL>"}, {"name": "mapbox-npm", "email": "<EMAIL>"}, {"name": "mapbox-admin", "email": "<EMAIL>"}, {"name": "mapbox-machine-user", "email": "<EMAIL>"}], "homepage": "https://github.com/mapbox/node-pre-gyp#readme", "bugs": {"url": "https://github.com/mapbox/node-pre-gyp/issues"}, "bin": {"node-pre-gyp": "bin/node-pre-gyp"}, "nyc": {"all": true, "exclude": ["test/**"], "skip-full": false}, "dist": {"shasum": "c740c23ec1007b9278d4c28f767b6e843a88c3d3", "tarball": "https://registry.npmjs.org/@mapbox/node-pre-gyp/-/node-pre-gyp-1.0.3.tgz", "fileCount": 31, "integrity": "sha512-9dTIfQW8HVCxLku5QrJ/ysS/b2MdYngs9+/oPrOTLvp3TrggdANYVW2h8FGJGDf0J7MYfp44W+c90cVJx+ASuA==", "signatures": [{"sig": "MEUCIQCDnNEkSaSSvQ2m+1rWXttYv0SGbiZthseGqsk2A7pudwIgOGT5AqOyl0Xvuo08iq6XD0M/jElDiZvCoIntpxLlOn0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 162826, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJga5uKCRA9TVsSAnZWagAAvXwP/2Cf8afCJtgUIf+VrOT2\neFPlRg1HEm7NKEeGO6dwdXIj52AtX1ytxHcdOwsZckxeB0uKswCg5aIhVitE\nctSGn9UQImsHRisWeEMGfeZEBjU/GrajiKJ1FLARNkfPa+by9FfQ08FDnryu\ndfSdxeUR9cfhaCwBHo9OeIX1vxz73p+E59Sq9yi5ayp8DKCH3JeZIy5yXeJK\npI+0+2Vso1yNFFwAvXO5ezU+HV8zi9ASTBnZBunylIU/sdUivSrNaNJDqgme\nwqj1ofuTWutr3t+p5Cjzi3b814JHihVdeUYHMZfjXj9qhVapDOMzZJk0ztOw\nGl4FNKw3cy32d0JqV/9RSgL3fKBFL7MsCca8z6M5PvXSgQIRtR9Vk2RKk4fW\nv3Rm8ei60Lr8Sb5t22+Bodo757n4rlIOo1F1x3QqQCA1xT3aE9LscPwNtTXm\nQiwUbpVTDKDz4Uh5F62Gu8axIuXXLAcd8RqYO8EMw1zCzvDAv5MjESDNZqce\nah7PPiAGjzoj2PCNAdlDFkU6KL0OfGCc7UZwi8YRxIxH3hajNXf6LxgQhhn1\nWJoWK9a3N/5NHDI0d9b1X06rLU2OUkaKpegi7Aw5uOEIE+gVVJ8us8DPfFjO\n2IyubU1zQRiLjl5kFUsZe2mhp7DNwC6l5IxeD8YhWqjV5UKavggE4ib3/iYM\n5pPs\r\n=uYIa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/node-pre-gyp.js", "gitHead": "26fac61a03e74b577604db3d0ab696ba22443578", "scripts": {"fix": "npm run lint -- --fix", "lint": "eslint bin/node-pre-gyp lib/*js lib/util/*js test/*js scripts/*js", "test": "tape test/*test.js", "coverage": "nyc --all --include index.js --include lib/ npm test", "upload-coverage": "nyc report --reporter json && codecov --clear --flags=unit --file=./coverage/coverage-final.json", "update-crosswalk": "node scripts/abi_crosswalk.js"}, "_npmUser": {"name": "mapbox-npm-09", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mapbox/node-pre-gyp.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Node.js native addon binary install tool", "directories": {}, "_nodeVersion": "14.16.0", "dependencies": {"tar": "^6.1.0", "nopt": "^5.0.0", "npmlog": "^4.1.2", "rimraf": "^3.0.2", "semver": "^7.3.4", "make-dir": "^3.1.0", "node-fetch": "^2.6.1", "detect-libc": "^1.0.3", "https-proxy-agent": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "nock": "^12.0.3", "tape": "^5.2.2", "eslint": "^7.18.0", "tar-fs": "^2.1.1", "aws-sdk": "^2.840.0", "codecov": "^3.8.1", "action-walk": "^2.2.0", "mock-aws-s3": "^4.0.1", "node-addon-api": "^3.1.0", "eslint-plugin-node": "^11.1.0", "@mapbox/cloudfriend": "^4.6.0", "@mapbox/eslint-config-mapbox": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-pre-gyp_1.0.3_1617664906030_0.21658364555599108", "host": "s3://npm-registry-packages"}}, "1.0.4": {"name": "@mapbox/node-pre-gyp", "version": "1.0.4", "keywords": ["native", "addon", "module", "c", "c++", "bindings", "binary"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@mapbox/node-pre-gyp@1.0.4", "maintainers": [{"name": "mapbox-npm-01", "email": "<EMAIL>"}, {"name": "mapbox-npm-02", "email": "<EMAIL>"}, {"name": "mapbox-npm-07", "email": "<EMAIL>"}, {"name": "mapbox-npm-03", "email": "<EMAIL>"}, {"name": "mapbox-npm-04", "email": "<EMAIL>"}, {"name": "mapbox-npm-09", "email": "<EMAIL>"}, {"name": "mapbox-npm-05", "email": "<EMAIL>"}, {"name": "mapbox-npm-06", "email": "<EMAIL>"}, {"name": "mapbox-npm-08", "email": "<EMAIL>"}, {"name": "mapbox-npm-advanced-actions", "email": "<EMAIL>"}, {"name": "mapbox-npm-ci", "email": "<EMAIL>"}, {"name": "mapbox-npm", "email": "<EMAIL>"}, {"name": "mapbox-admin", "email": "<EMAIL>"}, {"name": "mapbox-machine-user", "email": "<EMAIL>"}], "homepage": "https://github.com/mapbox/node-pre-gyp#readme", "bugs": {"url": "https://github.com/mapbox/node-pre-gyp/issues"}, "bin": {"node-pre-gyp": "bin/node-pre-gyp"}, "nyc": {"all": true, "exclude": ["test/**"], "skip-full": false}, "dist": {"shasum": "6c76e7a40138eac39e1a4dc869a083e43e236c00", "tarball": "https://registry.npmjs.org/@mapbox/node-pre-gyp/-/node-pre-gyp-1.0.4.tgz", "fileCount": 31, "integrity": "sha512-M669Qo4nRT7iDmQEjQYC7RU8Z6dpz9UmSbkJ1OFEja3uevCdLKh7IZZki7L1TZj02kRyl82snXFY8QqkyfowrQ==", "signatures": [{"sig": "MEUCIEZBT0dRfJ9tpOjIrF12elCmAB++x39/QSLTRjkRGdOhAiEAxIoxlFiV5UXLhDaCSwUCei2MpDVdk9XsqoPYPI2BToc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163338, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgf1lxCRA9TVsSAnZWagAAq54P/1ifdeymWo6QysrkEdbd\nK5H2GAwntkgO4ZjjfB0yQLhuw/Fel/737eDYKFzgRsai44Lmuof033sY7mSD\n41PR3ZW4pcyflyEJmMRHHry/GVp/HNPcxm0KVwfa1ZnL4d5kieoso6jx90XA\nrCxgvOdsy+UJB3gpGl5rfFRRS7AoxtnNXyUx3wD/NBrLL/s8UmOBlaWGMKUU\nlMoe0djjW6WImAg50igNIvKi7c94RwHWLbp9ms+/bZ/DHB/ZRvb6rvEpBS/r\n4G11V+7F/5I881mhuKk0qgyHvCHJIR3vzS3XHzmk8lOIPj4gUFsoBRAqb6Zu\nCRR3I69ehzETWWleNTNwVvly0uyjLqSQzJ/9zg2/4JqxVwWLMStHIfyumAT2\nATmP/gcO2TeByT9n/bBMw2ifPRg3Da7deb02UOtm8YYtMmP68SJGU8q50I9/\nhidHErAiUrv9KtExw+cgT/IbiQ8hGLL3lwB0aOXKyJzk/OfjPLV8gLKHhatk\ntRRQmCzlLo9IIlS1QlJw9XcbPwWEaRVmwvN0wbHXnyKoLy85rE3D9YyZ3GPX\ngeU5x8ssE2K1DYgtfqFUkw8RXyLgDel9X7wki612VLIF2kBoGUnzMjGR2O/T\nqOV9tZCuqsuWQ1Bq+5RXOnB5JnpcEVnceEonjC87mwamtC5m5dBuqcWS6R4U\nncP9\r\n=U3FN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/node-pre-gyp.js", "gitHead": "18fd1175f0fd6f6b0fd2fbc4091d7f4550230529", "scripts": {"fix": "npm run lint -- --fix", "lint": "eslint bin/node-pre-gyp lib/*js lib/util/*js test/*js scripts/*js", "test": "tape test/*test.js", "coverage": "nyc --all --include index.js --include lib/ npm test", "upload-coverage": "nyc report --reporter json && codecov --clear --flags=unit --file=./coverage/coverage-final.json", "update-crosswalk": "node scripts/abi_crosswalk.js"}, "_npmUser": {"name": "mapbox-npm-03", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mapbox/node-pre-gyp.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Node.js native addon binary install tool", "directories": {}, "_nodeVersion": "14.16.0", "dependencies": {"tar": "^6.1.0", "nopt": "^5.0.0", "npmlog": "^4.1.2", "rimraf": "^3.0.2", "semver": "^7.3.4", "make-dir": "^3.1.0", "node-fetch": "^2.6.1", "detect-libc": "^1.0.3", "https-proxy-agent": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "nock": "^12.0.3", "tape": "^5.2.2", "eslint": "^7.18.0", "tar-fs": "^2.1.1", "aws-sdk": "^2.840.0", "codecov": "^3.8.1", "action-walk": "^2.2.0", "mock-aws-s3": "^4.0.1", "node-addon-api": "^3.1.0", "eslint-plugin-node": "^11.1.0", "@mapbox/cloudfriend": "^4.6.0", "@mapbox/eslint-config-mapbox": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-pre-gyp_1.0.4_1618958704634_0.25253640492435236", "host": "s3://npm-registry-packages"}}, "1.0.5": {"name": "@mapbox/node-pre-gyp", "version": "1.0.5", "keywords": ["native", "addon", "module", "c", "c++", "bindings", "binary"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@mapbox/node-pre-gyp@1.0.5", "maintainers": [{"name": "mapbox-npm-01", "email": "<EMAIL>"}, {"name": "mapbox-npm-02", "email": "<EMAIL>"}, {"name": "mapbox-npm-07", "email": "<EMAIL>"}, {"name": "mapbox-npm-03", "email": "<EMAIL>"}, {"name": "mapbox-npm-04", "email": "<EMAIL>"}, {"name": "mapbox-npm-09", "email": "<EMAIL>"}, {"name": "mapbox-npm-05", "email": "<EMAIL>"}, {"name": "mapbox-npm-06", "email": "<EMAIL>"}, {"name": "mapbox-npm-08", "email": "<EMAIL>"}, {"name": "mapbox-npm-advanced-actions", "email": "<EMAIL>"}, {"name": "mapbox-npm-ci", "email": "<EMAIL>"}, {"name": "mapbox-npm", "email": "<EMAIL>"}, {"name": "mapbox-admin", "email": "<EMAIL>"}, {"name": "mapbox-machine-user", "email": "<EMAIL>"}], "homepage": "https://github.com/mapbox/node-pre-gyp#readme", "bugs": {"url": "https://github.com/mapbox/node-pre-gyp/issues"}, "bin": {"node-pre-gyp": "bin/node-pre-gyp"}, "nyc": {"all": true, "exclude": ["test/**"], "skip-full": false}, "dist": {"shasum": "2a0b32fcb416fb3f2250fd24cb2a81421a4f5950", "tarball": "https://registry.npmjs.org/@mapbox/node-pre-gyp/-/node-pre-gyp-1.0.5.tgz", "fileCount": 31, "integrity": "sha512-4srsKPXWlIxp5Vbqz5uLfBN+du2fJChBoYn/f2h991WLdk7jUvcSk/McVLSv/X+xQIPI8eGD5GjrnygdyHnhPA==", "signatures": [{"sig": "MEUCIGGqjxytXNCES+IJbRzxhJf8u6ga60d+yBxrubrSqXjoAiEA/tGa281nLtc47AjBffTpS9OF/fWljVNE2W9ZdU9LVV8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163552, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgmgz4CRA9TVsSAnZWagAA1S8P/1m3zw4pLAmpTwtc0avw\n0180WAcIt9eyH4Rrcw3wQe973hZzGj1Kpzv+qAHTfYeinS86Zubi6kzaPk68\nATztLd344zlGOMCw1Ce7wwYjGTmoz0tHaHjIxvqt9xTssc1FxLE2BKzzeptG\nRfEMZ4xM+K4cpRyUC0xAe3g9wVAN3/Rtf1mAX3t+FoTag3yA7isDAVxJB7sN\nkmcKA1IE3gTc4fRax7wIUAw+It7/3nUSWVnARTGCC+cKrJrIlKVMhoDHul+1\nxXCsarZguSJouGA1WJNQeoP9w/iSISlEyYgFdIu74TyRiAL91giR9IICrjkn\niInOIEwfEyGTJbEN+QG4LtYXFprl0AEL2xtoOtQQBubMIO8pyqGwT2hUm21V\nsuh3J4y5CKs0j4vDi3mj+0wTVhUpzZ7Xdce84Oo9ycXgqNOgr3SjErQURWRT\n/Jx7Bpzaf+g2+eO5KLDDui5A9uBp6TOxG619BwHkzP3FCbT7xDPS8XWpZum1\n7THOv90wTL/LrH8yZDdLnh5xHVkoD6tzkUnFfO4oV3VLVBcAfw4OC/nN2jcq\nMf6t3vSyZii6/cxLh4W92/FRvvSzF0xg3nnSw6yZ92qN/uT+vqGPCFgXid25\nnhxPhBZ2mPnR3xV0HTZ2qGqZP8Ucw5fsbmQ7D1D9rAfe1F/R/miGVGTg1naI\nKMVF\r\n=FTgu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/node-pre-gyp.js", "gitHead": "9267e525efba4e4dc20829d5bb4b25dcee116a2d", "scripts": {"fix": "npm run lint -- --fix", "lint": "eslint bin/node-pre-gyp lib/*js lib/util/*js test/*js scripts/*js", "test": "tape test/*test.js", "coverage": "nyc --all --include index.js --include lib/ npm test", "upload-coverage": "nyc report --reporter json && codecov --clear --flags=unit --file=./coverage/coverage-final.json", "update-crosswalk": "node scripts/abi_crosswalk.js"}, "_npmUser": {"name": "mapbox-npm-07", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mapbox/node-pre-gyp.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Node.js native addon binary install tool", "directories": {}, "_nodeVersion": "14.16.0", "dependencies": {"tar": "^6.1.0", "nopt": "^5.0.0", "npmlog": "^4.1.2", "rimraf": "^3.0.2", "semver": "^7.3.4", "make-dir": "^3.1.0", "node-fetch": "^2.6.1", "detect-libc": "^1.0.3", "https-proxy-agent": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "nock": "^12.0.3", "tape": "^5.2.2", "eslint": "^7.18.0", "tar-fs": "^2.1.1", "aws-sdk": "^2.840.0", "codecov": "^3.8.1", "action-walk": "^2.2.0", "mock-aws-s3": "^4.0.1", "node-addon-api": "^3.1.0", "eslint-plugin-node": "^11.1.0", "@mapbox/cloudfriend": "^4.6.0", "@mapbox/eslint-config-mapbox": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-pre-gyp_1.0.5_1620708599876_0.08171106611935786", "host": "s3://npm-registry-packages"}}, "1.0.6": {"name": "@mapbox/node-pre-gyp", "version": "1.0.6", "keywords": ["native", "addon", "module", "c", "c++", "bindings", "binary"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@mapbox/node-pre-gyp@1.0.6", "maintainers": [{"name": "mapbox-npm-01", "email": "<EMAIL>"}, {"name": "mapbox-npm-02", "email": "<EMAIL>"}, {"name": "mapbox-npm-07", "email": "<EMAIL>"}, {"name": "mapbox-npm-03", "email": "<EMAIL>"}, {"name": "mapbox-npm-04", "email": "<EMAIL>"}, {"name": "mapbox-npm-09", "email": "<EMAIL>"}, {"name": "mapbox-npm-05", "email": "<EMAIL>"}, {"name": "mapbox-npm-06", "email": "<EMAIL>"}, {"name": "mapbox-npm-08", "email": "<EMAIL>"}, {"name": "mapbox-npm-advanced-actions", "email": "<EMAIL>"}, {"name": "mapbox-npm-ci", "email": "<EMAIL>"}, {"name": "mapbox-npm", "email": "<EMAIL>"}, {"name": "mapbox-admin", "email": "<EMAIL>"}, {"name": "mapbox-machine-user", "email": "<EMAIL>"}], "homepage": "https://github.com/mapbox/node-pre-gyp#readme", "bugs": {"url": "https://github.com/mapbox/node-pre-gyp/issues"}, "bin": {"node-pre-gyp": "bin/node-pre-gyp"}, "nyc": {"all": true, "exclude": ["test/**"], "skip-full": false}, "dist": {"shasum": "f859d601a210537e27530f363028cde56e0cf962", "tarball": "https://registry.npmjs.org/@mapbox/node-pre-gyp/-/node-pre-gyp-1.0.6.tgz", "fileCount": 31, "integrity": "sha512-qK1ECws8UxuPqOA8F5LFD90vyVU33W7N3hGfgsOVfrJaRVc8McC3JClTDHpeSbL9CBrOHly/4GsNPAvIgNZE+g==", "signatures": [{"sig": "MEQCIBlF5efBZ7F/t1k43OW1jbnOffDwXnfNxL/WPEd1EtkBAiBEYYSgqIpRqEqcJxaKYGHoFwAK3A2ppT5O/Ua8ZnPsjQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 165841}, "main": "./lib/node-pre-gyp.js", "gitHead": "4f5b5139c57dbd539eb19de573a0e9ddee5fc795", "scripts": {"fix": "npm run lint -- --fix", "lint": "eslint bin/node-pre-gyp lib/*js lib/util/*js test/*js scripts/*js", "test": "tape test/*test.js", "coverage": "nyc --all --include index.js --include lib/ npm test", "upload-coverage": "nyc report --reporter json && codecov --clear --flags=unit --file=./coverage/coverage-final.json", "update-crosswalk": "node scripts/abi_crosswalk.js"}, "_npmUser": {"name": "mapbox-npm-02", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mapbox/node-pre-gyp.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Node.js native addon binary install tool", "directories": {}, "_nodeVersion": "16.12.0", "dependencies": {"tar": "^6.1.11", "nopt": "^5.0.0", "npmlog": "^5.0.1", "rimraf": "^3.0.2", "semver": "^7.3.5", "make-dir": "^3.1.0", "node-fetch": "^2.6.5", "detect-libc": "^1.0.3", "https-proxy-agent": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "nock": "^12.0.3", "tape": "^5.3.1", "eslint": "^7.32.0", "tar-fs": "^2.1.1", "aws-sdk": "^2.1012.0", "codecov": "^3.8.3", "action-walk": "^2.2.0", "mock-aws-s3": "^4.0.2", "node-addon-api": "^4.2.0", "eslint-plugin-node": "^11.1.0", "@mapbox/cloudfriend": "^5.1.0", "@mapbox/eslint-config-mapbox": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-pre-gyp_1.0.6_1634844541382_0.7896580224775611", "host": "s3://npm-registry-packages"}}, "1.0.7": {"name": "@mapbox/node-pre-gyp", "version": "1.0.7", "keywords": ["native", "addon", "module", "c", "c++", "bindings", "binary"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@mapbox/node-pre-gyp@1.0.7", "maintainers": [{"name": "mapbox-npm-01", "email": "<EMAIL>"}, {"name": "mapbox-npm-02", "email": "<EMAIL>"}, {"name": "mapbox-npm-07", "email": "<EMAIL>"}, {"name": "mapbox-npm-03", "email": "<EMAIL>"}, {"name": "mapbox-npm-04", "email": "<EMAIL>"}, {"name": "mapbox-npm-09", "email": "<EMAIL>"}, {"name": "mapbox-npm-05", "email": "<EMAIL>"}, {"name": "mapbox-npm-06", "email": "<EMAIL>"}, {"name": "mapbox-npm-08", "email": "<EMAIL>"}, {"name": "mapbox-npm-advanced-actions", "email": "<EMAIL>"}, {"name": "mapbox-npm-ci", "email": "<EMAIL>"}, {"name": "mapbox-npm", "email": "<EMAIL>"}, {"name": "mapbox-admin", "email": "<EMAIL>"}, {"name": "mapbox-machine-user", "email": "<EMAIL>"}], "homepage": "https://github.com/mapbox/node-pre-gyp#readme", "bugs": {"url": "https://github.com/mapbox/node-pre-gyp/issues"}, "bin": {"node-pre-gyp": "bin/node-pre-gyp"}, "nyc": {"all": true, "exclude": ["test/**"], "skip-full": false}, "dist": {"shasum": "a26919cac6595662703330d1820a0ca206f45521", "tarball": "https://registry.npmjs.org/@mapbox/node-pre-gyp/-/node-pre-gyp-1.0.7.tgz", "fileCount": 31, "integrity": "sha512-PplSvl4pJ5N3BkVjAdDzpPhVUPdC73JgttkR+LnBx2OORC1GCQsBjUeEuipf9uOaAM1SbxcdZFfR3KDTKm2S0A==", "signatures": [{"sig": "MEYCIQDjWbMxyLny8z6sSDnZjq4dQyGah8pQPuUvknsGC90t5gIhAIknmyyY1W89tY/r6Ro5TCROu3f8JAf5c7Xw+I8D6O19", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 166162, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhk//fCRA9TVsSAnZWagAAqAwP/AqiH9+04AZJHmpMXP4X\nUltXc164E6I+7yb4W2ZlvqKehFfNhVBVSn8JrkNBB8zfDQ+WOmQL+TcYHkME\nLr2UAVx9bOHrm5PwXnYrJkHRjXXX7AQkdSNPC7eAVL902g0Q3PlqpMvDPrs0\nT7tzbkopqV6HPcxFdZLuwqT17eHoLjW/9PBG0Ep53KiPbGQL5e7xvrP2jtAz\nS14TXU+J2c3AXaO2qvGFcUbZqtONFCzyyJZXq0iLxDZy2Y6GARP/he0ID19a\njuj81yCuwaSyb8cGQLJhdwQRxRHDnW1bqAD+YLUyL0pPyeMabQx/hzCi1JL5\nTRn1cC8EoVYxJ1qvDyrx66xiLgFtuWslTr/e9KA+6ystz+0BXA5emVM3uQs9\nTsEMkB93jUXBpU1UzO6bjCVvEHk09Zf78/pXG7zfwwlTMPYO9S7cA9wFpEtL\nlK0+C2yPU2CHciSuU72ckK6EQhZs2+lTYHHEcevKe39v7Alg9yXgBz4pOF2Z\n6rWeGNIvRvXxO6iwrf9gJVnELsmPzo0WQcFAqcggUCtJpLuYiDBBulhmZAXb\noMFTYOD8JQQb9I9Gr/Np26wr48CHzuUpR5BdWMdmfOTtRY4QgXm/6oZUXnGu\nup9UGYfWfFCvTmWBoz31ph9jpBF/eXodcLxbZ5nUSSlZSyrD7nt6j9b9Geik\nXjJy\r\n=T7XU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/node-pre-gyp.js", "gitHead": "b361cf7f3e05b963d4fc69692d6506508ada4fbb", "scripts": {"fix": "npm run lint -- --fix", "lint": "eslint bin/node-pre-gyp lib/*js lib/util/*js test/*js scripts/*js", "test": "tape test/*test.js", "coverage": "nyc --all --include index.js --include lib/ npm test", "upload-coverage": "nyc report --reporter json && codecov --clear --flags=unit --file=./coverage/coverage-final.json", "update-crosswalk": "node scripts/abi_crosswalk.js"}, "_npmUser": {"name": "mapbox-npm-02", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mapbox/node-pre-gyp.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Node.js native addon binary install tool", "directories": {}, "_nodeVersion": "14.18.1", "dependencies": {"tar": "^6.1.11", "nopt": "^5.0.0", "npmlog": "^6.0.0", "rimraf": "^3.0.2", "semver": "^7.3.5", "make-dir": "^3.1.0", "node-fetch": "^2.6.5", "detect-libc": "^1.0.3", "https-proxy-agent": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "nock": "^12.0.3", "tape": "^5.3.1", "eslint": "^7.32.0", "tar-fs": "^2.1.1", "aws-sdk": "^2.1012.0", "codecov": "^3.8.3", "action-walk": "^2.2.0", "mock-aws-s3": "^4.0.2", "node-addon-api": "^4.2.0", "eslint-plugin-node": "^11.1.0", "@mapbox/cloudfriend": "^5.1.0", "@mapbox/eslint-config-mapbox": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-pre-gyp_1.0.7_1637089247125_0.6868175034776898", "host": "s3://npm-registry-packages"}}, "1.0.8": {"name": "@mapbox/node-pre-gyp", "version": "1.0.8", "keywords": ["native", "addon", "module", "c", "c++", "bindings", "binary"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@mapbox/node-pre-gyp@1.0.8", "maintainers": [{"name": "mapbox-npm-01", "email": "<EMAIL>"}, {"name": "mapbox-npm-02", "email": "<EMAIL>"}, {"name": "mapbox-npm-07", "email": "<EMAIL>"}, {"name": "mapbox-npm-03", "email": "<EMAIL>"}, {"name": "mapbox-npm-04", "email": "<EMAIL>"}, {"name": "mapbox-npm-09", "email": "<EMAIL>"}, {"name": "mapbox-npm-05", "email": "<EMAIL>"}, {"name": "mapbox-npm-06", "email": "<EMAIL>"}, {"name": "mapbox-npm-08", "email": "<EMAIL>"}, {"name": "mapbox-npm-advanced-actions", "email": "<EMAIL>"}, {"name": "mapbox-npm-ci", "email": "<EMAIL>"}, {"name": "mapbox-npm", "email": "<EMAIL>"}, {"name": "mapbox-admin", "email": "<EMAIL>"}, {"name": "mapbox-machine-user", "email": "<EMAIL>"}], "homepage": "https://github.com/mapbox/node-pre-gyp#readme", "bugs": {"url": "https://github.com/mapbox/node-pre-gyp/issues"}, "bin": {"node-pre-gyp": "bin/node-pre-gyp"}, "nyc": {"all": true, "exclude": ["test/**"], "skip-full": false}, "dist": {"shasum": "****************************************", "tarball": "https://registry.npmjs.org/@mapbox/node-pre-gyp/-/node-pre-gyp-1.0.8.tgz", "fileCount": 31, "integrity": "sha512-CMGKi28CF+qlbXh26hDe6NxCd7amqeAzEqnS6IHeO6LoaKyM/n+Xw3HT1COdq8cuioOdlKdqn/hCmqPUOMOywg==", "signatures": [{"sig": "MEUCIDefW+v++A8uotlU1FBplGdkFGbIMVAcNXIcQ/BiSb4WAiEAjrB30bvSzve7E/xcaXs7lB+NIuM8t8OZhbtChG0KtSI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 166249, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhu7KcCRA9TVsSAnZWagAAc5oP/jlxD89J648FlGbgWBKp\n6JGmY41fScsAiXnO93wyTaWolmJh+Xv7h3Yig+jfz9LIcpz8JtBCTJEuVTUX\n1WQAZh+i9jLqnFjkSkazoJWJVEcivdrO5isAPBuIYoqqiMo1SJKem47FVJTR\nuf0Iy3Eeq29aZkybHJlQCAJL+M+X+O324QBLCfBroAWH/bXklHra+vCaijcU\nUwGMbDAtBoqeK7Mwz8ESrhLKI2yT9r+YdPfKG72T78oYG0zkyyDOyyxannbf\nze4vW51jNCZa5d//nG+QDVlJuoZ33kCwcgSc98E1fsy6vxljoM7kbQeCbnkf\nPkP0yConL/4QgGOqAH5zHs+TziYfOh4+bbxYPdoC7lZfdjaa0/dFsws906RJ\nH88GAo8WLhoKc3d6I4tRLdD6HbPSqECULvJWN+RwWOW+7suGKTptyO3nHVyt\nw10grccA/KZs2CVHeJrwXACXAMjEuD6UphEhW9YFexQgJleAAjEyNNXy5UYh\nkVs+YEXrXnZRaYuAWD4RI0ykuN4llh30hMwC511Kx1dPj+CsopbfqRbif784\nP4P29H4fwQUn70WNWvIbD6k4RBt+XXvaEOQbo8uQqok5fTb0y/Ne4M60A5jk\nBpbvyuvvyMljn79oZTKOPzdyD2v7vQIWD2k0Rttw51+b3X58Pu4+np1VKpdg\nXXwP\r\n=qBDT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/node-pre-gyp.js", "gitHead": "40a83f20a762668c41153eaeef8574373f70fe30", "scripts": {"fix": "npm run lint -- --fix", "lint": "eslint bin/node-pre-gyp lib/*js lib/util/*js test/*js scripts/*js", "test": "tape test/*test.js", "coverage": "nyc --all --include index.js --include lib/ npm test", "upload-coverage": "nyc report --reporter json && codecov --clear --flags=unit --file=./coverage/coverage-final.json", "update-crosswalk": "node scripts/abi_crosswalk.js"}, "_npmUser": {"name": "mapbox-npm-08", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mapbox/node-pre-gyp.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Node.js native addon binary install tool", "directories": {}, "_nodeVersion": "14.18.1", "dependencies": {"tar": "^6.1.11", "nopt": "^5.0.0", "npmlog": "^5.0.1", "rimraf": "^3.0.2", "semver": "^7.3.5", "make-dir": "^3.1.0", "node-fetch": "^2.6.5", "detect-libc": "^1.0.3", "https-proxy-agent": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "nock": "^12.0.3", "tape": "^5.3.1", "eslint": "^7.32.0", "tar-fs": "^2.1.1", "aws-sdk": "^2.1012.0", "codecov": "^3.8.3", "mock-aws-s3": "^4.0.2", "node-addon-api": "^4.2.0", "eslint-plugin-node": "^11.1.0", "@mapbox/cloudfriend": "^5.1.0", "@mapbox/eslint-config-mapbox": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-pre-gyp_1.0.8_1639690907827_0.28480156408405155", "host": "s3://npm-registry-packages"}}, "1.0.9": {"name": "@mapbox/node-pre-gyp", "version": "1.0.9", "keywords": ["native", "addon", "module", "c", "c++", "bindings", "binary"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@mapbox/node-pre-gyp@1.0.9", "maintainers": [{"name": "mapbox-npm-01", "email": "<EMAIL>"}, {"name": "mapbox-npm-02", "email": "<EMAIL>"}, {"name": "mapbox-npm-07", "email": "<EMAIL>"}, {"name": "mapbox-npm-03", "email": "<EMAIL>"}, {"name": "mapbox-npm-04", "email": "<EMAIL>"}, {"name": "mapbox-npm-09", "email": "<EMAIL>"}, {"name": "mapbox-npm-05", "email": "<EMAIL>"}, {"name": "mapbox-npm-06", "email": "<EMAIL>"}, {"name": "mapbox-npm-08", "email": "<EMAIL>"}, {"name": "mapbox-npm-advanced-actions", "email": "<EMAIL>"}, {"name": "mapbox-npm-ci", "email": "<EMAIL>"}, {"name": "mapbox-npm", "email": "<EMAIL>"}, {"name": "mapbox-admin", "email": "<EMAIL>"}, {"name": "mapbox-machine-user", "email": "<EMAIL>"}], "homepage": "https://github.com/mapbox/node-pre-gyp#readme", "bugs": {"url": "https://github.com/mapbox/node-pre-gyp/issues"}, "bin": {"node-pre-gyp": "bin/node-pre-gyp"}, "nyc": {"all": true, "exclude": ["test/**"], "skip-full": false}, "dist": {"shasum": "09a8781a3a036151cdebbe8719d6f8b25d4058bc", "tarball": "https://registry.npmjs.org/@mapbox/node-pre-gyp/-/node-pre-gyp-1.0.9.tgz", "fileCount": 31, "integrity": "sha512-aDF3S3rK9Q2gey/WAttUlISduDItz5BU3306M9Eyv6/oS40aMprnopshtlKTykxRNIBEZuRMaZAnbrQ4QtKGyw==", "signatures": [{"sig": "MEYCIQCfAanuI9khzMVFS4JdnnsNp1Yronm/xB9hDiVNYqsktgIhAKsOPAsYw2fU0Gf89OBJc2M4SH4W24TRD5fUI2iI3YzG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 166447, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiQuTTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqQ2A/+MxvhCqlX06ce3bovJxcoMMTP6Yep5RPUu62KksYRcsWPKlyM\r\n4gkHPflRPX5NjvYHaQV1iVDemGSn727Ts4LUr6kja8YnW1LcAcf8Qp3qecaw\r\nILZhnACxhzSbTXISG2RbVOOe8MlrFQCVuvUH55vvb11K7Y2k7clGl+gdhz9c\r\n+7RxMC5edaOM2+S9kyWYeT59Cr46dqs5+9HQXXy9JUlOZiJf9dRFcY1VVGw+\r\nQjLd7Pp38vPN5Mg/QRcsIc5bPgmJ6Wh3PD7A5bKo3dwUl18DxNv61j05TxBw\r\nJsTaPbNx/Y2dY2ZKik/RBJutrNeX38od733ZhoRfktZznSrz2lNsaW5gJLub\r\nHbahTqefhYGnsdbdGp5BYtB1YqdHMz6VJbgsIXxpHdKjSkR9RRa5TSaVtgAc\r\nJT56UKEfJug1U+wA+kzGaojS3aa6dSlAq+JYcGn6oS2S2UDX/qi2PQYNH2n3\r\nWNKaBXcN43hu1KcrgLcunsE+F5zTV/Yzpqu58Z2qKHnMlMTWFucuQXzyfwI6\r\ny5bu3FyWrFtnYOWp4TNVz4EFUWX3UbUSkZDY72LkScmbybvog2x1cDl/PD/F\r\noQZun2ozCvGY413JJz5NTZf9vRPMcjlKDFSGAOBkZbnrEGxbB+BgW1KblW3n\r\n0WxzX4O+3oJ24EGoQDhq1dpoF8KHkwgG6GY=\r\n=VftV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/node-pre-gyp.js", "gitHead": "2d92dc838acb3409b68bec9f152bd130cebe8bbb", "scripts": {"fix": "npm run lint -- --fix", "lint": "eslint bin/node-pre-gyp lib/*js lib/util/*js test/*js scripts/*js", "test": "tape test/*test.js", "coverage": "nyc --all --include index.js --include lib/ npm test", "upload-coverage": "nyc report --reporter json && codecov --clear --flags=unit --file=./coverage/coverage-final.json", "update-crosswalk": "node scripts/abi_crosswalk.js"}, "_npmUser": {"name": "mapbox-npm-09", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mapbox/node-pre-gyp.git", "type": "git"}, "_npmVersion": "8.3.1", "description": "Node.js native addon binary install tool", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"tar": "^6.1.11", "nopt": "^5.0.0", "npmlog": "^5.0.1", "rimraf": "^3.0.2", "semver": "^7.3.5", "make-dir": "^3.1.0", "node-fetch": "^2.6.7", "detect-libc": "^2.0.0", "https-proxy-agent": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "nock": "^12.0.3", "tape": "^5.5.2", "eslint": "^7.32.0", "tar-fs": "^2.1.1", "aws-sdk": "^2.1087.0", "codecov": "^3.8.3", "mock-aws-s3": "^4.0.2", "node-addon-api": "^4.3.0", "eslint-plugin-node": "^11.1.0", "@mapbox/cloudfriend": "^5.1.0", "@mapbox/eslint-config-mapbox": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-pre-gyp_1.0.9_1648551122827_0.6088410616061308", "host": "s3://npm-registry-packages"}}, "1.0.10": {"name": "@mapbox/node-pre-gyp", "version": "1.0.10", "keywords": ["native", "addon", "module", "c", "c++", "bindings", "binary"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@mapbox/node-pre-gyp@1.0.10", "maintainers": [{"name": "mapbox-npm-01", "email": "<EMAIL>"}, {"name": "mapbox-npm-02", "email": "<EMAIL>"}, {"name": "mapbox-npm-07", "email": "<EMAIL>"}, {"name": "mapbox-npm-03", "email": "<EMAIL>"}, {"name": "mapbox-npm-04", "email": "<EMAIL>"}, {"name": "mapbox-npm-09", "email": "<EMAIL>"}, {"name": "mapbox-npm-05", "email": "<EMAIL>"}, {"name": "mapbox-npm-06", "email": "<EMAIL>"}, {"name": "mapbox-npm-08", "email": "<EMAIL>"}, {"name": "mapbox-npm-advanced-actions", "email": "<EMAIL>"}, {"name": "mapbox-npm-ci", "email": "<EMAIL>"}, {"name": "mapbox-npm", "email": "<EMAIL>"}, {"name": "mapbox-admin", "email": "<EMAIL>"}, {"name": "mapbox-machine-user", "email": "<EMAIL>"}], "homepage": "https://github.com/mapbox/node-pre-gyp#readme", "bugs": {"url": "https://github.com/mapbox/node-pre-gyp/issues"}, "bin": {"node-pre-gyp": "bin/node-pre-gyp"}, "nyc": {"all": true, "exclude": ["test/**"], "skip-full": false}, "dist": {"shasum": "8e6735ccebbb1581e5a7e652244cadc8a844d03c", "tarball": "https://registry.npmjs.org/@mapbox/node-pre-gyp/-/node-pre-gyp-1.0.10.tgz", "fileCount": 31, "integrity": "sha512-4ySo4CjzStuprMwk35H5pPbkymjv1SF3jGLj6rAHp/xT/RF7TL7bd9CTm1xDY49K2qF7jmR/g7k+SkLETP6opA==", "signatures": [{"sig": "MEQCIHrln56C0CWuj37t/lJGIs96MrYp+DxlnXK74UIZi5cxAiBg03ntGBbl5vgr8ejw/6120rrQzbFbIz7L8JI+OFq+IQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 166631, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjF4ilACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmreWRAAnM4C94YB8kDRwJ7BDiQZdPKyFcEfaVTv0Yv4S9+hTRBSJ3BX\r\nd9ytpUZZYH5I9gtnkz7EsJD6JpbgIcZh5sQe9D9O+ehl6r3um3ujwiYxFuXM\r\nB7VMDIeQPEEB8ooMkPgIkB76Yh97UmV1RVGAoltvtwTZUQQrwhSZtQVAIbiR\r\nkhN9aGPHQIsJQXymbOvLbA/pQmAQ/TKq4xN9ZcSz2OBQfXpqNuWuG6OFx7Pe\r\nC1yLsMLEf8zfALIWwSj11AaD1zK1/WRVAJ2huxt5ZeMLEXUlvO/fiX+z246G\r\nAQC18Hot5M0nIjU6zoBIeeYi3uPWoZHupQroaY7qOEu6JdYKSEJbgUHtnsW9\r\n52iXKcseThRTPO+Nb5esJWsrdl7w9jmMKnhIBy5lLJgZQ8tNdZf0icKeRYLP\r\nERj1hlxTDB42v+sZzNfTOfseBkR/UCgnDJzpFVleCJ/cnU4Z/uuYjDDb/hYs\r\nG68b5TtPRRdHY/9Cc3tEO7X2BvYEqNjlC2TAZBsQVeTJndpjl+jGZ5Y7mPMG\r\nSMYAx5HpmrOVuJuwePqrIDxJVRYxEkWVp35nToA9vOfM2lfg6tFTtekfVar1\r\n0IwilVVmzD4uLxenYnll2/9nRPvISffVQtt+5qC4vRhJAm+kOdGDiqxa8463\r\nSRiv7AiN1B02EJPcyfMS/253p0NY4J/FKdQ=\r\n=xg2J\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/node-pre-gyp.js", "gitHead": "c2a746bc67df928ea5c8bb77b59daadfe41a0a59", "scripts": {"fix": "npm run lint -- --fix", "lint": "eslint bin/node-pre-gyp lib/*js lib/util/*js test/*js scripts/*js", "test": "tape test/*test.js", "coverage": "nyc --all --include index.js --include lib/ npm test", "upload-coverage": "nyc report --reporter json && codecov --clear --flags=unit --file=./coverage/coverage-final.json", "update-crosswalk": "node scripts/abi_crosswalk.js"}, "_npmUser": {"name": "mapbox-npm-08", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mapbox/node-pre-gyp.git", "type": "git"}, "_npmVersion": "6.14.17", "description": "Node.js native addon binary install tool", "directories": {}, "_nodeVersion": "14.20.0", "dependencies": {"tar": "^6.1.11", "nopt": "^5.0.0", "npmlog": "^5.0.1", "rimraf": "^3.0.2", "semver": "^7.3.5", "make-dir": "^3.1.0", "node-fetch": "^2.6.7", "detect-libc": "^2.0.0", "https-proxy-agent": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "nock": "^12.0.3", "tape": "^5.5.2", "eslint": "^7.32.0", "tar-fs": "^2.1.1", "aws-sdk": "^2.1087.0", "codecov": "^3.8.3", "mock-aws-s3": "^4.0.2", "node-addon-api": "^4.3.0", "eslint-plugin-node": "^11.1.0", "@mapbox/cloudfriend": "^5.1.0", "@mapbox/eslint-config-mapbox": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-pre-gyp_1.0.10_1662486693379_0.44124758539187736", "host": "s3://npm-registry-packages"}}, "1.0.11-dev.1": {"name": "@mapbox/node-pre-gyp", "version": "1.0.11-dev.1", "keywords": ["native", "addon", "module", "c", "c++", "bindings", "binary"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@mapbox/node-pre-gyp@1.0.11-dev.1", "maintainers": [{"name": "mbx-npm-ci-production", "email": "<EMAIL>"}, {"name": "mbx-npm-ci-staging", "email": "<EMAIL>"}, {"name": "mbx-npm-advanced-actions-production", "email": "<EMAIL>"}, {"name": "mbx-npm-advanced-actions-staging", "email": "<EMAIL>"}, {"name": "mbx-npm-09-production", "email": "<EMAIL>"}, {"name": "mbx-npm-08-production", "email": "<EMAIL>"}, {"name": "mbx-npm-07-production", "email": "<EMAIL>"}, {"name": "mbx-npm-06-production", "email": "<EMAIL>"}, {"name": "mbx-npm-05-production", "email": "<EMAIL>"}, {"name": "mbx-npm-04-production", "email": "<EMAIL>"}, {"name": "mbx-npm-03-production", "email": "<EMAIL>"}, {"name": "mbx-npm-02-production", "email": "<EMAIL>"}, {"name": "mbx-npm-01-production", "email": "<EMAIL>"}, {"name": "mbx-npm-02-staging", "email": "<EMAIL>"}, {"name": "mapbox-npm-01", "email": "<EMAIL>"}, {"name": "mapbox-npm-02", "email": "<EMAIL>"}, {"name": "mapbox-npm-07", "email": "<EMAIL>"}, {"name": "mapbox-npm-03", "email": "<EMAIL>"}, {"name": "mapbox-npm-04", "email": "<EMAIL>"}, {"name": "mapbox-npm-09", "email": "<EMAIL>"}, {"name": "mapbox-npm-05", "email": "<EMAIL>"}, {"name": "mapbox-npm-06", "email": "<EMAIL>"}, {"name": "mapbox-npm-08", "email": "<EMAIL>"}, {"name": "mapbox-npm-advanced-actions", "email": "<EMAIL>"}, {"name": "mapbox-npm-ci", "email": "<EMAIL>"}, {"name": "mapbox-npm", "email": "<EMAIL>"}, {"name": "mapbox-admin", "email": "<EMAIL>"}, {"name": "mapbox-machine-user", "email": "<EMAIL>"}], "homepage": "https://github.com/mapbox/node-pre-gyp#readme", "bugs": {"url": "https://github.com/mapbox/node-pre-gyp/issues"}, "bin": {"node-pre-gyp": "bin/node-pre-gyp"}, "nyc": {"all": true, "exclude": ["test/**"], "skip-full": false}, "dist": {"shasum": "f148f713d3661e990f7a0855697085ceec34d7dd", "tarball": "https://registry.npmjs.org/@mapbox/node-pre-gyp/-/node-pre-gyp-1.0.11-dev.1.tgz", "fileCount": 32, "integrity": "sha512-zJeHwhtzuuHRPipgw2jYBs5vEwSfTgAcmiLtoLci00PgwVkcgVmXUCunhXzwN+Qe43CtATt7osgeB2IFaVkesQ==", "signatures": [{"sig": "MEYCIQCD9+eO9sM2Uorvsw2BMurgtc42Q0n8lfpPLrXzXe9PhAIhAOWqEHB6Rf3LKDJradxM9HMQWXrSlhHKNQUTmwWFj8MQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 169524}, "main": "./lib/node-pre-gyp.js", "gitHead": "35fd391b3e1def2dc9116b4c82a920ffbdf2d04c", "scripts": {"fix": "npm run lint -- --fix", "lint": "eslint bin/node-pre-gyp lib/*js lib/util/*js test/*js scripts/*js", "test": "tape test/*test.js", "coverage": "nyc --all --include index.js --include lib/ npm test", "upload-coverage": "nyc report --reporter json && codecov --clear --flags=unit --file=./coverage/coverage-final.json", "update-crosswalk": "node scripts/abi_crosswalk.js"}, "_npmUser": {"name": "mapbox-npm-03", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mapbox/node-pre-gyp.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "Node.js native addon binary install tool", "directories": {}, "_nodeVersion": "16.20.0", "dependencies": {"tar": "^6.1.11", "nopt": "^5.0.0", "npmlog": "^5.0.1", "rimraf": "^3.0.2", "semver": "^7.3.5", "make-dir": "^3.1.0", "node-fetch": "^2.6.7", "detect-libc": "^2.0.0", "https-proxy-agent": "^5.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^15.1.0", "nock": "^12.0.3", "tape": "^5.5.2", "eslint": "^7.32.0", "tar-fs": "^2.1.1", "aws-sdk": "^2.1087.0", "codecov": "^3.8.3", "mock-aws-s3": "^4.0.2", "node-addon-api": "^4.3.0", "eslint-plugin-node": "^11.1.0", "@mapbox/cloudfriend": "^5.1.0", "@mapbox/eslint-config-mapbox": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-pre-gyp_1.0.11-dev.1_1689329767755_0.8024282207794771", "host": "s3://npm-registry-packages"}}, "1.0.11": {"name": "@mapbox/node-pre-gyp", "version": "1.0.11", "keywords": ["native", "addon", "module", "c", "c++", "bindings", "binary"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@mapbox/node-pre-gyp@1.0.11", "maintainers": [{"name": "mbx-npm-ci-production", "email": "<EMAIL>"}, {"name": "mbx-npm-ci-staging", "email": "<EMAIL>"}, {"name": "mbx-npm-advanced-actions-production", "email": "<EMAIL>"}, {"name": "mbx-npm-advanced-actions-staging", "email": "<EMAIL>"}, {"name": "mbx-npm-09-production", "email": "<EMAIL>"}, {"name": "mbx-npm-08-production", "email": "<EMAIL>"}, {"name": "mbx-npm-07-production", "email": "<EMAIL>"}, {"name": "mbx-npm-06-production", "email": "<EMAIL>"}, {"name": "mbx-npm-05-production", "email": "<EMAIL>"}, {"name": "mbx-npm-04-production", "email": "<EMAIL>"}, {"name": "mbx-npm-03-production", "email": "<EMAIL>"}, {"name": "mbx-npm-02-production", "email": "<EMAIL>"}, {"name": "mbx-npm-01-production", "email": "<EMAIL>"}, {"name": "mbx-npm-02-staging", "email": "<EMAIL>"}, {"name": "mapbox-npm-01", "email": "<EMAIL>"}, {"name": "mapbox-npm-02", "email": "<EMAIL>"}, {"name": "mapbox-npm-07", "email": "<EMAIL>"}, {"name": "mapbox-npm-03", "email": "<EMAIL>"}, {"name": "mapbox-npm-04", "email": "<EMAIL>"}, {"name": "mapbox-npm-09", "email": "<EMAIL>"}, {"name": "mapbox-npm-05", "email": "<EMAIL>"}, {"name": "mapbox-npm-06", "email": "<EMAIL>"}, {"name": "mapbox-npm-08", "email": "<EMAIL>"}, {"name": "mapbox-npm-advanced-actions", "email": "<EMAIL>"}, {"name": "mapbox-npm-ci", "email": "<EMAIL>"}, {"name": "mapbox-npm", "email": "<EMAIL>"}, {"name": "mapbox-admin", "email": "<EMAIL>"}, {"name": "mapbox-machine-user", "email": "<EMAIL>"}], "homepage": "https://github.com/mapbox/node-pre-gyp#readme", "bugs": {"url": "https://github.com/mapbox/node-pre-gyp/issues"}, "bin": {"node-pre-gyp": "bin/node-pre-gyp"}, "nyc": {"all": true, "exclude": ["test/**"], "skip-full": false}, "dist": {"shasum": "417db42b7f5323d79e93b34a6d7a2a12c0df43fa", "tarball": "https://registry.npmjs.org/@mapbox/node-pre-gyp/-/node-pre-gyp-1.0.11.tgz", "fileCount": 32, "integrity": "sha512-Yhlar6v9WQgUp/He7BdgzOz8lqMQ8sU+jkCq7Wx8Myc5YFJLbEe7lgui/V7G1qB1DJykHSGwreceSaD60Y0PUQ==", "signatures": [{"sig": "MEQCIEXAkH6QkbLNZ76BBX/dR5dmTl5tibRs1KLuxjsCLPhaAiBoKZHhUQHFplNeILBOM8kEhOTEQRyQV/VH4NYatplmcg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 169518}, "main": "./lib/node-pre-gyp.js", "gitHead": "a74f5e367c0d71033620aa0112e7baf7f3515b9d", "scripts": {"fix": "npm run lint -- --fix", "lint": "eslint bin/node-pre-gyp lib/*js lib/util/*js test/*js scripts/*js", "test": "tape test/*test.js", "coverage": "nyc --all --include index.js --include lib/ npm test", "upload-coverage": "nyc report --reporter json && codecov --clear --flags=unit --file=./coverage/coverage-final.json", "update-crosswalk": "node scripts/abi_crosswalk.js"}, "_npmUser": {"name": "mapbox-npm-01", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mapbox/node-pre-gyp.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "Node.js native addon binary install tool", "directories": {}, "_nodeVersion": "16.20.0", "dependencies": {"tar": "^6.1.11", "nopt": "^5.0.0", "npmlog": "^5.0.1", "rimraf": "^3.0.2", "semver": "^7.3.5", "make-dir": "^3.1.0", "node-fetch": "^2.6.7", "detect-libc": "^2.0.0", "https-proxy-agent": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "nock": "^12.0.3", "tape": "^5.5.2", "eslint": "^7.32.0", "tar-fs": "^2.1.1", "aws-sdk": "^2.1087.0", "codecov": "^3.8.3", "mock-aws-s3": "^4.0.2", "node-addon-api": "^4.3.0", "eslint-plugin-node": "^11.1.0", "@mapbox/cloudfriend": "^5.1.0", "@mapbox/eslint-config-mapbox": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-pre-gyp_1.0.11_1689340185287_0.4348364784661325", "host": "s3://npm-registry-packages"}}, "1.1.0-dev.1": {"name": "@mapbox/node-pre-gyp", "version": "1.1.0-dev.1", "keywords": ["native", "addon", "module", "c", "c++", "bindings", "binary"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@mapbox/node-pre-gyp@1.1.0-dev.1", "maintainers": [{"name": "mbx-npm-ci-production", "email": "<EMAIL>"}, {"name": "mbx-npm-ci-staging", "email": "<EMAIL>"}, {"name": "mbx-npm-advanced-actions-production", "email": "<EMAIL>"}, {"name": "mbx-npm-advanced-actions-staging", "email": "<EMAIL>"}, {"name": "mbx-npm-09-production", "email": "<EMAIL>"}, {"name": "mbx-npm-08-production", "email": "<EMAIL>"}, {"name": "mbx-npm-07-production", "email": "<EMAIL>"}, {"name": "mbx-npm-06-production", "email": "<EMAIL>"}, {"name": "mbx-npm-05-production", "email": "<EMAIL>"}, {"name": "mbx-npm-04-production", "email": "<EMAIL>"}, {"name": "mbx-npm-03-production", "email": "<EMAIL>"}, {"name": "mbx-npm-02-production", "email": "<EMAIL>"}, {"name": "mbx-npm-01-production", "email": "<EMAIL>"}, {"name": "mbx-npm-02-staging", "email": "<EMAIL>"}, {"name": "mapbox-npm-01", "email": "<EMAIL>"}, {"name": "mapbox-npm-02", "email": "<EMAIL>"}, {"name": "mapbox-npm-07", "email": "<EMAIL>"}, {"name": "mapbox-npm-03", "email": "<EMAIL>"}, {"name": "mapbox-npm-04", "email": "<EMAIL>"}, {"name": "mapbox-npm-09", "email": "<EMAIL>"}, {"name": "mapbox-npm-05", "email": "<EMAIL>"}, {"name": "mapbox-npm-06", "email": "<EMAIL>"}, {"name": "mapbox-npm-08", "email": "<EMAIL>"}, {"name": "mapbox-npm-advanced-actions", "email": "<EMAIL>"}, {"name": "mapbox-npm-ci", "email": "<EMAIL>"}, {"name": "mapbox-npm", "email": "<EMAIL>"}, {"name": "mapbox-admin", "email": "<EMAIL>"}, {"name": "mapbox-machine-user", "email": "<EMAIL>"}], "homepage": "https://github.com/mapbox/node-pre-gyp#readme", "bugs": {"url": "https://github.com/mapbox/node-pre-gyp/issues"}, "bin": {"node-pre-gyp": "bin/node-pre-gyp"}, "nyc": {"all": true, "exclude": ["test/**"], "skip-full": false}, "dist": {"shasum": "5e11fac46bcaee90e367a686ca4520d0ef2cf5a7", "tarball": "https://registry.npmjs.org/@mapbox/node-pre-gyp/-/node-pre-gyp-1.1.0-dev.1.tgz", "fileCount": 34, "integrity": "sha512-kz5SX7aidRmrRTr+9wCXG9qAEudxPohJX2Pg/+Wah+BkdmKL6omXofIvNlb7pOqXZkSY2pLMZAK3zdyBY5Tsag==", "signatures": [{"sig": "MEUCIA6mcNKJdi9hIpbR6/DdzPNnQscxXVvMy9A+Cch5MMfAAiEAvKWSGihSzy6AiHCw1qsqgeCNNOZLrJnrCPMcG/jSyQk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 179752}, "main": "./lib/node-pre-gyp.js", "engines": {"node": ">=18"}, "gitHead": "996b31b06800d384e893c1108b7e8796cb97cd8f", "scripts": {"fix": "npm run lint -- --fix", "lint": "eslint bin/node-pre-gyp lib/*js lib/util/*js test/*js scripts/*js", "test": "tape test/*test.js", "coverage": "nyc --all --include index.js --include lib/ npm test", "upload-coverage": "nyc report --reporter json && codecov --clear --flags=unit --file=./coverage/coverage-final.json", "update-crosswalk": "node scripts/abi_crosswalk.js"}, "_npmUser": {"name": "mapbox-npm-04", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mapbox/node-pre-gyp.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Node.js native addon binary install tool", "directories": {}, "_nodeVersion": "20.12.0", "dependencies": {"tar": "^7.4.0", "nopt": "^7.2.1", "npmlog": "^7.0.1", "rimraf": "^5.0.5", "semver": "^7.3.5", "make-dir": "^3.1.0", "node-fetch": "^2.6.7", "detect-libc": "^2.0.0", "https-proxy-agent": "^5.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^15.1.0", "nock": "^12.0.3", "tape": "^5.5.2", "eslint": "^8.57.0", "tar-fs": "^2.1.1", "aws-sdk": "^2.1087.0", "codecov": "^3.8.3", "mock-aws-s3": "^4.0.2", "node-addon-api": "^4.3.0", "eslint-plugin-n": "^17.9.0", "@mapbox/cloudfriend": "^5.1.0", "@mapbox/eslint-config-mapbox": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-pre-gyp_1.1.0-dev.1_1720025613411_0.5250870403640475", "host": "s3://npm-registry-packages"}}, "2.0.0-rc.0": {"name": "@mapbox/node-pre-gyp", "version": "2.0.0-rc.0", "keywords": ["native", "addon", "module", "c", "c++", "bindings", "binary"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@mapbox/node-pre-gyp@2.0.0-rc.0", "maintainers": [{"name": "mapbox-npm-01", "email": "<EMAIL>"}, {"name": "mapbox-npm-02", "email": "<EMAIL>"}, {"name": "mapbox-npm-07", "email": "<EMAIL>"}, {"name": "mapbox-npm-03", "email": "<EMAIL>"}, {"name": "mapbox-npm-04", "email": "<EMAIL>"}, {"name": "mapbox-npm-09", "email": "<EMAIL>"}, {"name": "mapbox-npm-05", "email": "<EMAIL>"}, {"name": "mapbox-npm-06", "email": "<EMAIL>"}, {"name": "mapbox-npm-08", "email": "<EMAIL>"}, {"name": "mapbox-npm-advanced-actions", "email": "<EMAIL>"}, {"name": "mapbox-npm-ci", "email": "<EMAIL>"}, {"name": "mapbox-npm", "email": "<EMAIL>"}, {"name": "mapbox-admin", "email": "<EMAIL>"}, {"name": "mapbox-machine-user", "email": "<EMAIL>"}, {"name": "mbx-npm-ci-staging", "email": "<EMAIL>"}, {"name": "mbx-npm-ci-production", "email": "<EMAIL>"}, {"name": "mbx-npm-01-production", "email": "<EMAIL>"}, {"name": "mbx-npm-02-production", "email": "<EMAIL>"}, {"name": "mbx-npm-03-production", "email": "<EMAIL>"}, {"name": "mbx-npm-04-production", "email": "<EMAIL>"}, {"name": "mbx-npm-05-production", "email": "<EMAIL>"}, {"name": "mbx-npm-06-production", "email": "<EMAIL>"}, {"name": "mbx-npm-07-production", "email": "<EMAIL>"}, {"name": "mbx-npm-08-production", "email": "<EMAIL>"}, {"name": "mbx-npm-09-production", "email": "<EMAIL>"}, {"name": "mbx-npm-02-staging", "email": "<EMAIL>"}, {"name": "mbx-npm-advanced-actions-staging", "email": "<EMAIL>"}, {"name": "mbx-npm-advanced-actions-production", "email": "<EMAIL>"}], "homepage": "https://github.com/mapbox/node-pre-gyp#readme", "bugs": {"url": "https://github.com/mapbox/node-pre-gyp/issues"}, "bin": {"node-pre-gyp": "bin/node-pre-gyp"}, "nyc": {"all": true, "exclude": ["test/**"], "skip-full": false}, "dist": {"shasum": "4390439d79e30bba0a4dccb230723e359505c8b7", "tarball": "https://registry.npmjs.org/@mapbox/node-pre-gyp/-/node-pre-gyp-2.0.0-rc.0.tgz", "fileCount": 39, "integrity": "sha512-nhSMNprz3WmeRvd8iUs5JqkKr0Ncx46JtPxM3AhXes84XpSJfmIwKeWXRpsr53S7kqPkQfPhzrMFUxSNb23qSA==", "signatures": [{"sig": "MEUCIQCFurHOub8Mwo2VO1SK0sSPKsbz4xDqpTr3yjuxGdDmyAIgQwpC5D0THe7CmHghSv+QUtvSQ4PFM6OTkvYTyOYh4QI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 187024}, "main": "./lib/node-pre-gyp.js", "engines": {"node": ">=18"}, "gitHead": "bd7cf6921c81870647a27405f83558b91e9496f6", "scripts": {"fix": "npm run lint -- --fix", "lint": "eslint bin/node-pre-gyp lib/*js lib/util/*js test/*js scripts/*js", "test": "tape test/*test.js", "coverage": "nyc --all --include index.js --include lib/ npm test", "upload-coverage": "nyc report --reporter json && codecov --clear --flags=unit --file=./coverage/coverage-final.json", "update-crosswalk": "node scripts/abi_crosswalk.js"}, "_npmUser": {"name": "mapbox-admin", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mapbox/node-pre-gyp.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Node.js native addon binary install tool", "directories": {}, "_nodeVersion": "22.11.0", "dependencies": {"tar": "^7.4.0", "nopt": "^8.0.0", "semver": "^7.5.3", "consola": "^3.2.3", "node-fetch": "^2.6.7", "detect-libc": "^2.0.0", "https-proxy-agent": "^7.0.5"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^17.0.0", "nock": "^13.5.4", "tape": "^5.5.2", "eslint": "^8.57.0", "tar-fs": "^3.0.6", "aws-sdk": "^2.1087.0", "codecov": "^3.8.3", "mock-aws-s3": "^4.0.2", "node-addon-api": "^8.1.0", "eslint-plugin-n": "^17.9.0", "@mapbox/cloudfriend": "^8.1.0", "@mapbox/eslint-config-mapbox": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-pre-gyp_2.0.0-rc.0_1733697959309_0.7029325632442183", "host": "s3://npm-registry-packages-npm-production"}}, "2.0.0": {"name": "@mapbox/node-pre-gyp", "description": "Node.js native addon binary install tool", "version": "2.0.0", "keywords": ["native", "addon", "module", "c", "c++", "bindings", "binary"], "license": "BSD-3-<PERSON><PERSON>", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/mapbox/node-pre-gyp.git"}, "bin": {"node-pre-gyp": "bin/node-pre-gyp"}, "main": "./lib/node-pre-gyp.js", "engines": {"node": ">=18"}, "dependencies": {"consola": "^3.2.3", "detect-libc": "^2.0.0", "https-proxy-agent": "^7.0.5", "node-fetch": "^2.6.7", "nopt": "^8.0.0", "semver": "^7.5.3", "tar": "^7.4.0"}, "devDependencies": {"@mapbox/cloudfriend": "^8.1.0", "@mapbox/eslint-config-mapbox": "^5.0.1", "aws-sdk": "^2.1087.0", "codecov": "^3.8.3", "eslint": "^8.57.0", "eslint-plugin-n": "^17.9.0", "mock-aws-s3": "^4.0.2", "nock": "^13.5.4", "node-addon-api": "^8.1.0", "nyc": "^17.0.0", "tape": "^5.5.2", "tar-fs": "^3.0.6"}, "nyc": {"all": true, "skip-full": false, "exclude": ["test/**"]}, "scripts": {"coverage": "nyc --all --include index.js --include lib/ npm test", "upload-coverage": "nyc report --reporter json && codecov --clear --flags=unit --file=./coverage/coverage-final.json", "lint": "eslint bin/node-pre-gyp lib/*js lib/util/*js test/*js scripts/*js", "fix": "npm run lint -- --fix", "update-crosswalk": "node scripts/abi_crosswalk.js", "test": "tape test/*test.js"}, "_id": "@mapbox/node-pre-gyp@2.0.0", "gitHead": "a541932680034f5de9e7365ef8d9a0d7a11cc1a9", "bugs": {"url": "https://github.com/mapbox/node-pre-gyp/issues"}, "homepage": "https://github.com/mapbox/node-pre-gyp#readme", "_nodeVersion": "22.13.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-llMXd39jtP0HpQLVI37Bf1m2ADlEb35GYSh1SDSLsBhR+5iCxiNGlT31yqbNtVHygHAtMy6dWFERpU2JgufhPg==", "shasum": "16d1d9049c0218820da81a12ae084e7fe67790d1", "tarball": "https://registry.npmjs.org/@mapbox/node-pre-gyp/-/node-pre-gyp-2.0.0.tgz", "fileCount": 39, "unpackedSize": 187474, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQC/8tikdn1iQl50FufYAjT5Gtq4kr70UMBp+Q2MTqIrUwIhAMgODC3+14t0MdFjV3WBMFA0ZNmHzLN4M8WV5okOwj/4"}]}, "_npmUser": {"name": "mapbox-admin", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mapbox-npm-01", "email": "<EMAIL>"}, {"name": "mapbox-npm-02", "email": "<EMAIL>"}, {"name": "mapbox-npm-07", "email": "<EMAIL>"}, {"name": "mapbox-npm-03", "email": "<EMAIL>"}, {"name": "mapbox-npm-04", "email": "<EMAIL>"}, {"name": "mapbox-npm-09", "email": "<EMAIL>"}, {"name": "mapbox-npm-05", "email": "<EMAIL>"}, {"name": "mapbox-npm-06", "email": "<EMAIL>"}, {"name": "mapbox-npm-08", "email": "<EMAIL>"}, {"name": "mapbox-npm-advanced-actions", "email": "<EMAIL>"}, {"name": "mapbox-npm-ci", "email": "<EMAIL>"}, {"name": "mapbox-npm", "email": "<EMAIL>"}, {"name": "mapbox-admin", "email": "<EMAIL>"}, {"name": "mapbox-machine-user", "email": "<EMAIL>"}, {"name": "mbx-npm-ci-staging", "email": "<EMAIL>"}, {"name": "mbx-npm-ci-production", "email": "<EMAIL>"}, {"name": "mbx-npm-01-production", "email": "<EMAIL>"}, {"name": "mbx-npm-02-production", "email": "<EMAIL>"}, {"name": "mbx-npm-03-production", "email": "<EMAIL>"}, {"name": "mbx-npm-04-production", "email": "<EMAIL>"}, {"name": "mbx-npm-05-production", "email": "<EMAIL>"}, {"name": "mbx-npm-06-production", "email": "<EMAIL>"}, {"name": "mbx-npm-07-production", "email": "<EMAIL>"}, {"name": "mbx-npm-08-production", "email": "<EMAIL>"}, {"name": "mbx-npm-09-production", "email": "<EMAIL>"}, {"name": "mbx-npm-02-staging", "email": "<EMAIL>"}, {"name": "mbx-npm-advanced-actions-staging", "email": "<EMAIL>"}, {"name": "mbx-npm-advanced-actions-production", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/node-pre-gyp_2.0.0_1737846439641_0.2907221346155129"}, "_hasShrinkwrap": false}}, "time": {"created": "2021-01-21T15:16:28.708Z", "modified": "2025-01-25T23:07:20.526Z", "1.0.0-alpha1": "2021-01-21T15:16:28.894Z", "1.0.0-alpha2": "2021-01-21T15:27:10.464Z", "1.0.0-alpha3": "2021-01-25T20:46:22.529Z", "1.0.0-alpha4": "2021-02-08T16:44:34.903Z", "1.0.0-alpha5": "2021-02-09T03:43:49.450Z", "1.0.0-alpha6": "2021-02-09T04:58:47.723Z", "1.0.0": "2021-02-09T16:57:55.710Z", "1.0.1": "2021-03-08T22:27:00.404Z", "1.0.2": "2021-03-29T21:16:07.862Z", "1.0.3": "2021-04-05T23:21:46.158Z", "1.0.4": "2021-04-20T22:45:04.792Z", "1.0.5": "2021-05-11T04:50:00.100Z", "1.0.6": "2021-10-21T19:29:01.731Z", "1.0.7": "2021-11-16T19:00:47.305Z", "1.0.8": "2021-12-16T21:41:48.024Z", "1.0.9": "2022-03-29T10:52:02.980Z", "1.0.10": "2022-09-06T17:51:33.525Z", "1.0.11-dev.1": "2023-07-14T10:16:08.083Z", "1.0.11": "2023-07-14T13:09:45.532Z", "1.1.0-dev.1": "2024-07-03T16:53:33.689Z", "2.0.0-rc.0": "2024-12-08T22:45:59.540Z", "2.0.0": "2025-01-25T23:07:19.846Z"}, "bugs": {"url": "https://github.com/mapbox/node-pre-gyp/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "homepage": "https://github.com/mapbox/node-pre-gyp#readme", "keywords": ["native", "addon", "module", "c", "c++", "bindings", "binary"], "repository": {"type": "git", "url": "git://github.com/mapbox/node-pre-gyp.git"}, "description": "Node.js native addon binary install tool", "maintainers": [{"name": "mapbox-npm-01", "email": "<EMAIL>"}, {"name": "mapbox-npm-02", "email": "<EMAIL>"}, {"name": "mapbox-npm-07", "email": "<EMAIL>"}, {"name": "mapbox-npm-03", "email": "<EMAIL>"}, {"name": "mapbox-npm-04", "email": "<EMAIL>"}, {"name": "mapbox-npm-09", "email": "<EMAIL>"}, {"name": "mapbox-npm-05", "email": "<EMAIL>"}, {"name": "mapbox-npm-06", "email": "<EMAIL>"}, {"name": "mapbox-npm-08", "email": "<EMAIL>"}, {"name": "mapbox-npm-advanced-actions", "email": "<EMAIL>"}, {"name": "mapbox-npm-ci", "email": "<EMAIL>"}, {"name": "mapbox-npm", "email": "<EMAIL>"}, {"name": "mapbox-admin", "email": "<EMAIL>"}, {"name": "mapbox-machine-user", "email": "<EMAIL>"}, {"name": "mbx-npm-ci-staging", "email": "<EMAIL>"}, {"name": "mbx-npm-ci-production", "email": "<EMAIL>"}, {"name": "mbx-npm-01-production", "email": "<EMAIL>"}, {"name": "mbx-npm-02-production", "email": "<EMAIL>"}, {"name": "mbx-npm-03-production", "email": "<EMAIL>"}, {"name": "mbx-npm-04-production", "email": "<EMAIL>"}, {"name": "mbx-npm-05-production", "email": "<EMAIL>"}, {"name": "mbx-npm-06-production", "email": "<EMAIL>"}, {"name": "mbx-npm-07-production", "email": "<EMAIL>"}, {"name": "mbx-npm-08-production", "email": "<EMAIL>"}, {"name": "mbx-npm-09-production", "email": "<EMAIL>"}, {"name": "mbx-npm-02-staging", "email": "<EMAIL>"}, {"name": "mbx-npm-advanced-actions-staging", "email": "<EMAIL>"}, {"name": "mbx-npm-advanced-actions-production", "email": "<EMAIL>"}], "readme": "# @mapbox/node-pre-gyp\n\n#### @mapbox/node-pre-gyp makes it easy to publish and install Node.js C++ addons from binaries\n\n[![Build status](https://ci.appveyor.com/api/projects/status/3nxewb425y83c0gv)](https://ci.appveyor.com/project/Mapbox/node-pre-gyp)\n\n`@mapbox/node-pre-gyp` stands between [npm](https://github.com/npm/npm) and [node-gyp](https://github.com/Tootallnate/node-gyp) and offers a cross-platform method of binary deployment.\n\n### Special note on previous package\n\nOn Feb 9th, 2021 `@mapbox/node-pre-gyp@1.0.0` was [released](./CHANGELOG.md). Older, unscoped versions that are not part of the `@mapbox` org are deprecated and only `@mapbox/node-pre-gyp` will see updates going forward. To upgrade to the new package do:\n\n```\nnpm uninstall node-pre-gyp --save\nnpm install @mapbox/node-pre-gyp --save\n```\n\n### Features\n\n - A command line tool called `node-pre-gyp` that can install your package's C++ module from a binary.\n - A variety of developer targeted commands for packaging, testing, and publishing binaries.\n - A JavaScript module that can dynamically require your installed binary: `require('@mapbox/node-pre-gyp').find`\n\nFor a hello world example of a module packaged with `node-pre-gyp` see <https://github.com/springmeyer/node-addon-example> and [the wiki ](https://github.com/mapbox/node-pre-gyp/wiki/Modules-using-node-pre-gyp) for real world examples.\n\n## Credits\n\n - The module is modeled after [node-gyp](https://github.com/Tootallnate/node-gyp) by [@Tootallnate](https://github.com/Tootallnate)\n - Motivation for initial development came from [@ErisDS](https://github.com/ErisDS) and the [Ghost Project](https://github.com/TryGhost/Ghost).\n - Development is sponsored by [Mapbox](https://www.mapbox.com/)\n\n## FAQ\n\nSee the [Frequently Ask Questions](https://github.com/mapbox/node-pre-gyp/wiki/FAQ).\n\n## Depends\n\nWe will attempt to track the [Node.js release schedule](https://github.com/nodejs/release#release-schedule) and will regularly retire support for versions that have reached EOL.\n\n - v2: Node.js >= 18.x (unreleased)\n - v1: Node.js >= 8.x\n\n## Install\n\n`node-pre-gyp` is designed to be installed as a local dependency of your Node.js C++ addon and accessed like:\n\n    ./node_modules/.bin/node-pre-gyp --help\n\nBut you can also install it globally:\n\n    npm install @mapbox/node-pre-gyp -g\n\n## Usage\n\n### Commands\n\nView all possible commands:\n\n    node-pre-gyp --help\n\n- clean - Remove the entire folder containing the compiled .node module\n- install - Install pre-built binary for module\n- reinstall - Run \"clean\" and \"install\" at once\n- build - Compile the module by dispatching to node-gyp or nw-gyp\n- rebuild - Run \"clean\" and \"build\" at once\n- package - Pack binary into tarball\n- testpackage - Test that the staged package is valid\n- publish - Publish pre-built binary\n- unpublish - Unpublish pre-built binary\n- info - Fetch info on published binaries\n\nYou can also chain commands:\n\n    node-pre-gyp clean build unpublish publish info\n\n### Options\n\nOptions include:\n\n - `-C/--directory`: run the command in this directory\n - `--build-from-source`: build from source instead of using pre-built binary\n - `--update-binary`: reinstall by replacing previously installed local binary with remote binary\n - `--runtime=node-webkit`: customize the runtime: `node`, `electron` and `node-webkit` are the valid options\n - `--fallback-to-build`: fallback to building from source if pre-built binary is not available\n - `--target=0.4.0`: Pass the target node or node-webkit version to compile against\n - `--target_arch=ia32`: Pass the target arch and override the host `arch`. Any value that is [supported by Node.js](https://nodejs.org/api/os.html#osarch) is valid.\n - `--target_platform=win32`: Pass the target platform and override the host `platform`. Valid values are `linux`, `darwin`, `win32`, `sunos`, `freebsd`, `openbsd`, and `aix`.\n\nBoth `--build-from-source` and `--fallback-to-build` can be passed alone or they can provide values. You can pass `--fallback-to-build=false` to override the option as declared in package.json. In addition to being able to pass `--build-from-source` you can also pass `--build-from-source=myapp` where `myapp` is the name of your module.\n\nFor example: `npm install --build-from-source=myapp`. This is useful if:\n\n - `myapp` is referenced in the package.json of a larger app and therefore `myapp` is being installed as a dependency with `npm install`.\n - The larger app also depends on other modules installed with `node-pre-gyp`\n - You only want to trigger a source compile for `myapp` and the other modules.\n\n### Configuring\n\nThis is a guide to configuring your module to use node-pre-gyp.\n\n#### 1) Add new entries to your `package.json`\n\n - Add `@mapbox/node-pre-gyp` to `dependencies`\n - Add `aws-sdk` as a `devDependency`\n - Add a custom `install` script\n - Declare a `binary` object\n\nThis looks like:\n\n```js\n    \"dependencies\"  : {\n      \"@mapbox/node-pre-gyp\": \"1.x\"\n    },\n    \"devDependencies\": {\n      \"aws-sdk\": \"2.x\"\n    }\n    \"scripts\": {\n        \"install\": \"node-pre-gyp install --fallback-to-build\"\n    },\n    \"binary\": {\n        \"module_name\": \"your_module\",\n        \"module_path\": \"./lib/binding/\",\n        \"host\": \"https://your_module.s3-us-west-1.amazonaws.com\"\n    }\n```\n\nFor a full example see [node-addon-examples's package.json](https://github.com/springmeyer/node-addon-example/blob/master/package.json).\n\nLet's break this down:\n\n - Dependencies need to list `node-pre-gyp`\n - Your devDependencies should list `aws-sdk` so that you can run `node-pre-gyp publish` locally or a CI system. We recommend using `devDependencies` only since `aws-sdk` is large and not needed for `node-pre-gyp install` since it only uses http to fetch binaries\n - Your `scripts` section should override the `install` target with `\"install\": \"node-pre-gyp install --fallback-to-build\"`. This allows node-pre-gyp to be used instead of the default npm behavior of always source compiling with `node-gyp` directly.\n - Your package.json should contain a `binary` section describing key properties you provide to allow node-pre-gyp to package optimally. They are detailed below.\n\nNote: in the past we recommended putting `@mapbox/node-pre-gyp` in the `bundledDependencies`, but we no longer recommend this. In the past there were npm bugs (with node versions 0.10.x) that could lead to node-pre-gyp not being available at the right time during install (unless we bundled). This should no longer be the case. Also, for a time we recommended using `\"preinstall\": \"npm install @mapbox/node-pre-gyp\"` as an alternative method to avoid needing to bundle. But this did not behave predictably across all npm versions - see https://github.com/mapbox/node-pre-gyp/issues/260 for the details. So we do not recommend using `preinstall` to install `@mapbox/node-pre-gyp`. More history on this at https://github.com/strongloop/fsevents/issues/157#issuecomment-265545908.\n\n##### The `binary` object has three required properties\n\n###### module_name\n\nThe name of your native node module. This value must:\n\n - Match the name passed to [the NODE_MODULE macro](http://nodejs.org/api/addons.html#addons_hello_world)\n - Must be a valid C variable name (e.g. it cannot contain `-`)\n - Should not include the `.node` extension.\n\n###### module_path\n\nThe location your native module is placed after a build. This should be an empty directory without other Javascript files. This entire directory will be packaged in the binary tarball. When installing from a remote package this directory will be overwritten with the contents of the tarball.\n\nNote: This property supports variables based on [Versioning](#versioning).\n\n###### host\n\nA url to the remote location where you've published tarball binaries (must be `https` not `http`).\n\nIt is highly recommended that you use Amazon S3. The reasons are:\n\n  - Various node-pre-gyp commands like `publish` and `info` only work with an S3 host.\n  - S3 is a very solid hosting platform for distributing large files.\n  - We provide detail documentation for using [S3 hosting](#s3-hosting) with node-pre-gyp.\n\nWhy then not require S3? Because while some applications using node-pre-gyp need to distribute binaries as large as 20-30 MB, others might have very small binaries and might wish to store them in a GitHub repo. This is not recommended, but if an author really wants to host in a non-S3 location then it should be possible.\n\nIt should also be mentioned that there is an optional and entirely separate npm module called [node-pre-gyp-github](https://github.com/bchr02/node-pre-gyp-github) which is intended to complement node-pre-gyp and be installed along with it. It provides the ability to store and publish your binaries within your repositories GitHub Releases if you would rather not use S3 directly. Installation and usage instructions can be found [here](https://github.com/bchr02/node-pre-gyp-github), but the basic premise is that instead of using the ```node-pre-gyp publish``` command you would use ```node-pre-gyp-github publish```.\n\n##### The `binary` object other optional S3 properties\n\nIf you are not using a standard s3 path like `bucket_name.s3(.-)region.amazonaws.com`, you might get an error on `publish` because node-pre-gyp extracts the region and bucket from the `host` url. For example, you may have an on-premises s3-compatible storage  server, or may have configured a specific dns redirecting to an s3  endpoint. In these cases, you can explicitly set the `region` and `bucket` properties to tell node-pre-gyp to use these values instead of guessing from the `host` property. The following values can be used in the `binary` section:\n\n###### host\n\nThe url to the remote server root location (must be `https` not `http`).\n\n###### bucket\n\nThe bucket name where your tarball binaries should be located.\n\n###### region\n\nYour S3 server region.\n\n###### s3ForcePathStyle\n\nSet `s3ForcePathStyle` to true if the endpoint url should not be prefixed with the bucket name. If false (default), the server endpoint would be  constructed as `bucket_name.your_server.com`.\n\n##### The `binary` object has optional properties\n\n###### remote_path\n\nIt **is recommended** that you customize this property. This is an extra path to use for publishing and finding remote tarballs. The default value for `remote_path` is `\"\"` meaning that if you do not provide it then all packages will be published at the base of the `host`. It is recommended to provide a value like `./{name}/v{version}` to help organize remote packages in the case that you choose to publish multiple node addons to the same `host`.\n\nNote: This property supports variables based on [Versioning](#versioning).\n\n###### package_name\n\nIt is **not recommended** to override this property unless you are also overriding the `remote_path`. This is the versioned name of the remote tarball containing the binary `.node` module and any supporting files you've placed inside the `module_path` directory. Unless you specify `package_name` in your `package.json` then it defaults to `{module_name}-v{version}-{node_abi}-{platform}-{arch}.tar.gz` which allows your binary to work across node versions, platforms, and architectures. If you are using `remote_path` that is also versioned by `./{module_name}/v{version}` then you could remove these variables from the `package_name` and just use: `{node_abi}-{platform}-{arch}.tar.gz`. Then your remote tarball will be looked up at, for example, `https://example.com/your-module/v0.1.0/node-v11-linux-x64.tar.gz`.\n\nAvoiding the version of your module in the `package_name` and instead only embedding in a directory name can be useful when you want to make a quick tag of your module that does not change any C++ code. In this case you can just copy binaries to the new version behind the scenes like:\n\n```sh\naws s3 sync --acl public-read s3://mapbox-node-binary/sqlite3/v3.0.3/ s3://mapbox-node-binary/sqlite3/v3.0.4/\n```\n\nNote: This property supports variables based on [Versioning](#versioning).\n\n#### 2) Add a new target to binding.gyp\n\n`node-pre-gyp` calls out to `node-gyp` to compile the module and passes variables along like [module_name](#module_name) and [module_path](#module_path).\n\nA new target must be added to `binding.gyp` that moves the compiled `.node` module from `./build/Release/module_name.node` into the directory specified by `module_path`.\n\nAdd a target like this at the end of your `targets` list:\n\n```js\n    {\n      \"target_name\": \"action_after_build\",\n      \"type\": \"none\",\n      \"dependencies\": [ \"<(module_name)\" ],\n      \"copies\": [\n        {\n          \"files\": [ \"<(PRODUCT_DIR)/<(module_name).node\" ],\n          \"destination\": \"<(module_path)\"\n        }\n      ]\n    }\n```\n\nFor a full example see [node-addon-example's binding.gyp](https://github.com/springmeyer/node-addon-example/blob/2ff60a8ded7f042864ad21db00c3a5a06cf47075/binding.gyp).\n\n#### 3) Dynamically require your `.node`\n\nInside the main js file that requires your addon module you are likely currently doing:\n\n```js\nvar binding = require('../build/Release/binding.node');\n```\n\nor:\n\n```js\nvar bindings = require('./bindings')\n```\n\nChange those lines to:\n\n```js\nvar binary = require('@mapbox/node-pre-gyp');\nvar path = require('path');\nvar binding_path = binary.find(path.resolve(path.join(__dirname,'./package.json')));\nvar binding = require(binding_path);\n```\n\nFor a full example see [node-addon-example's index.js](https://github.com/springmeyer/node-addon-example/blob/2ff60a8ded7f042864ad21db00c3a5a06cf47075/index.js#L1-L4)\n\n#### 4) Build and package your app\n\nNow build your module from source:\n\n    npm install --build-from-source\n\nThe `--build-from-source` tells `node-pre-gyp` to not look for a remote package and instead dispatch to node-gyp to build.\n\nNow `node-pre-gyp` should now also be installed as a local dependency so the command line tool it offers can be found at `./node_modules/.bin/node-pre-gyp`.\n\n#### 5) Test\n\nNow `npm test` should work just as it did before.\n\n#### 6) Publish the tarball\n\nThen package your app:\n\n    ./node_modules/.bin/node-pre-gyp package\n\nOnce packaged, now you can publish:\n\n    ./node_modules/.bin/node-pre-gyp publish\n\nCurrently the `publish` command pushes your binary to S3. This requires:\n\n - You have installed `aws-sdk` with `npm install aws-sdk`\n - You have created a bucket already.\n - The `host` points to an S3 http or https endpoint.\n - You have configured node-pre-gyp to read your S3 credentials (see [S3 hosting](#s3-hosting) for details).\n\nYou can also host your binaries elsewhere. To do this requires:\n\n - You manually publish the binary created by the `package` command to an `https` endpoint\n - Ensure that the `host` value points to your custom `https` endpoint.\n\n#### 7) Automate builds\n\nNow you need to publish builds for all the platforms and node versions you wish to support. This is best automated.\n\n - See [Appveyor Automation](#appveyor-automation) for how to auto-publish builds on Windows.\n - See [Travis Automation](#travis-automation) for how to auto-publish builds on OS X and Linux.\n\n#### 8) You're done!\n\nNow publish your module to the npm registry. Users will now be able to install your module from a binary.\n\nWhat will happen is this:\n\n1. `npm install <your package>` will pull from the npm registry\n2. npm will run the `install` script which will call out to `node-pre-gyp`\n3. `node-pre-gyp` will fetch the binary `.node` module and unpack in the right place\n4. Assuming that all worked, you are done\n\nIf a a binary was not available for a given platform and `--fallback-to-build` was used then `node-gyp rebuild` will be called to try to source compile the module.\n\n#### 9) One more option\n\nIt may be that you want to work with two s3 buckets, one for staging and one for production; this\narrangement makes it less likely to accidentally overwrite a production binary. It also allows the production\nenvironment to have more restrictive permissions than staging while still enabling publishing when\ndeveloping and testing.\n\nThe binary.host property can be set at execution time. In order to do so all of the following conditions\nmust be true.\n\n- binary.host is falsey or not present\n- binary.staging_host is not empty\n- binary.production_host is not empty\n\nIf any of these checks fail then the operation will not perform execution time determination of the s3 target.\n\nIf the command being executed is either \"publish\" or \"unpublish\" then the default is set to `binary.staging_host`. In all other cases\nthe default is `binary.production_host`.\n\nThe command-line options `--s3_host=staging` or `--s3_host=production` override the default. If `s3_host`\nis present and not `staging` or `production` an exception is thrown.\n\nThis allows installing from staging by specifying `--s3_host=staging`. And it requires specifying\n`--s3_option=production` in order to publish to, or unpublish from, production, making accidental errors less likely.\n\n## Node-API Considerations\n\n[Node-API](https://nodejs.org/api/n-api.html#n_api_node_api), which was previously known as N-API, is an ABI-stable alternative to previous technologies such as [nan](https://github.com/nodejs/nan) which are tied to a specific Node runtime engine. Node-API is Node runtime engine agnostic and guarantees modules created today will continue to run, without changes, into the future.\n\nUsing `node-pre-gyp` with Node-API projects requires a handful of additional configuration values and imposes some additional requirements.\n\nThe most significant difference is that an Node-API module can be coded to target multiple  Node-API versions. Therefore, an Node-API module must declare in its `package.json` file which Node-API versions the module is designed to run against. In addition, since multiple builds may be required for a single module, path and file names must be specified in way that avoids naming conflicts.\n\n### The `napi_versions` array property\n\nA Node-API module must declare in its `package.json` file, the Node-API versions the module is intended to support. This is accomplished by including an `napi-versions` array property in the `binary` object. For example:\n\n```js\n\"binary\": {\n    \"module_name\": \"your_module\",\n    \"module_path\": \"your_module_path\",\n    \"host\": \"https://your_bucket.s3-us-west-1.amazonaws.com\",\n    \"napi_versions\": [1,3]\n  }\n```\n\nIf the `napi_versions` array property is *not* present, `node-pre-gyp` operates as it always has. Including the `napi_versions` array property instructs `node-pre-gyp` that this is a Node-API module build.\n\nWhen the `napi_versions` array property is present, `node-pre-gyp` fires off multiple operations, one for each of the Node-API versions in the array. In the example above, two operations are initiated, one for Node-API version 1 and second for Node-API version 3. How this version number is communicated is described next.\n\n### The `napi_build_version` value\n\nFor each of the Node-API module operations `node-pre-gyp` initiates, it ensures that the `napi_build_version` is set appropriately.\n\nThis value is of importance in two areas:\n\n1. The C/C++ code which needs to know against which Node-API version it should compile.\n2. `node-pre-gyp` itself which must assign appropriate path and file names to avoid collisions.\n\n### Defining `NAPI_VERSION` for the C/C++ code\n\nThe `napi_build_version` value is communicated to the C/C++ code by adding this code to the `binding.gyp` file:\n\n```\n\"defines\": [\n    \"NAPI_VERSION=<(napi_build_version)\",\n]\n```\n\nThis ensures that `NAPI_VERSION`, an integer value, is declared appropriately to the C/C++ code for each build.\n\n> Note that earlier versions of this document recommended defining the symbol `NAPI_BUILD_VERSION`. `NAPI_VERSION` is preferred because it used by the Node-API C/C++ headers to configure the specific Node-API versions being requested.\n\n### Path and file naming requirements in `package.json`\n\nSince `node-pre-gyp` fires off multiple operations for each request, it is essential that path and file names be created in such a way as to avoid collisions. This is accomplished by imposing additional path and file naming requirements.\n\nSpecifically, when performing Node-API builds, the `{napi_build_version}` text configuration value  *must* be present in the `module_path` property. In addition, the `{napi_build_version}` text configuration value  *must* be present in either the `remote_path` or `package_name` property. (No problem if it's in both.)\n\nHere's an example:\n\n```js\n\"binary\": {\n    \"module_name\": \"your_module\",\n    \"module_path\": \"./lib/binding/napi-v{napi_build_version}\",\n    \"remote_path\": \"./{module_name}/v{version}/{configuration}/\",\n    \"package_name\": \"{platform}-{arch}-napi-v{napi_build_version}.tar.gz\",\n    \"host\": \"https://your_bucket.s3-us-west-1.amazonaws.com\",\n    \"napi_versions\": [1,3]\n  }\n```\n\n## Supporting both Node-API and NAN builds\n\nYou may have a legacy native add-on that you wish to continue supporting for those versions of Node that do not support Node-API, as you add Node-API support for later Node versions. This can be accomplished by specifying the `node_napi_label` configuration value in the package.json `binary.package_name` property.\n\nPlacing the configuration value `node_napi_label` in the package.json `binary.package_name` property instructs `node-pre-gyp` to build all viable Node-API binaries supported by the current Node instance. If the current Node instance does not support Node-API, `node-pre-gyp` will request a traditional, non-Node-API build.\n\nThe configuration value `node_napi_label` is set by `node-pre-gyp` to the type of build created, `napi` or `node`, and the version number. For Node-API builds, the string contains the Node-API version nad has values like `napi-v3`. For traditional, non-Node-API builds, the string contains the ABI version with values like `node-v46`.\n\nHere's how the `binary` configuration above might be changed to support both Node-API and NAN builds:\n\n```js\n\"binary\": {\n    \"module_name\": \"your_module\",\n    \"module_path\": \"./lib/binding/{node_napi_label}\",\n    \"remote_path\": \"./{module_name}/v{version}/{configuration}/\",\n    \"package_name\": \"{platform}-{arch}-{node_napi_label}.tar.gz\",\n    \"host\": \"https://your_bucket.s3-us-west-1.amazonaws.com\",\n    \"napi_versions\": [1,3]\n  }\n```\n\nThe C/C++ symbol `NAPI_VERSION` can be used to distinguish Node-API and non-Node-API builds. The value of `NAPI_VERSION` is set to the integer Node-API version for Node-API builds and is set to `0` for non-Node-API builds.\n\nFor example:\n\n```C\n#if NAPI_VERSION\n// Node-API code goes here\n#else\n// NAN code goes here\n#endif\n```\n\n### Two additional configuration values\n\nThe following two configuration values, which were implemented in previous versions of `node-pre-gyp`, continue to exist, but have been replaced by the `node_napi_label` configuration value described above.\n\n1. `napi_version` If Node-API is supported by the currently executing Node instance, this value is the Node-API version number supported by Node. If Node-API is not supported, this value is an empty string.\n\n2. `node_abi_napi` If the value returned for `napi_version` is non empty, this value is `'napi'`. If the value returned for `napi_version` is empty, this value is the value returned for `node_abi`.\n\nThese values are present for use in the `binding.gyp` file and may be used as `{napi_version}` and `{node_abi_napi}` for text substitution in the `binary` properties of the `package.json` file.\n\n## S3 Hosting\n\nYou can host wherever you choose but S3 is cheap, `node-pre-gyp publish` expects it, and S3 can be integrated well with [Travis.ci](http://travis-ci.org) to automate builds for OS X and Ubuntu, and with [Appveyor](http://appveyor.com) to automate builds for Windows. Here is an approach to do this:\n\nFirst, get setup locally and test the workflow:\n\n#### 1) Create an S3 bucket\n\nAnd have your **key** and **secret key** ready for writing to the bucket.\n\nIt is recommended to create a IAM user with a policy that only gives permissions to the specific bucket you plan to publish to. This can be done in the [IAM console](https://console.aws.amazon.com/iam/) by: 1) adding a new user, 2) choosing `Attach User Policy`, 3) Using the `Policy Generator`, 4) selecting `Amazon S3` for the service, 5) adding the actions: `DeleteObject`, `GetObject`, `GetObjectAcl`, `ListBucket`, `HeadBucket`, `PutObject`, `PutObjectAcl`, 6) adding an ARN of `arn:aws:s3:::bucket/*` (replacing `bucket` with your bucket name), and finally 7) clicking `Add Statement` and saving the policy. It should generate a policy like:\n\n```js\n{\n    \"Version\": \"2012-10-17\",\n    \"Statement\": [\n        {\n            \"Sid\": \"objects\",\n            \"Effect\": \"Allow\",\n            \"Action\": [\n                \"s3:PutObject\",\n                \"s3:GetObjectAcl\",\n                \"s3:GetObject\",\n                \"s3:DeleteObject\",\n                \"s3:PutObjectAcl\"\n            ],\n            \"Resource\": \"arn:aws:s3:::your-bucket-name/*\"\n        },\n        {\n            \"Sid\": \"bucket\",\n            \"Effect\": \"Allow\",\n            \"Action\": \"s3:ListBucket\",\n            \"Resource\": \"arn:aws:s3:::your-bucket-name\"\n        },\n        {\n            \"Sid\": \"buckets\",\n            \"Effect\": \"Allow\",\n            \"Action\": \"s3:HeadBucket\",\n            \"Resource\": \"*\"\n        }\n    ]\n}\n```\n\n#### 2) Install node-pre-gyp\n\nEither install it globally:\n\n    npm install node-pre-gyp -g\n\nOr put the local version on your PATH\n\n    export PATH=`pwd`/node_modules/.bin/:$PATH\n\n#### 3) Configure AWS credentials\n\nIt is recommended to configure the AWS JS SDK v2 used internally by `node-pre-gyp` by setting these environment variables:\n\n- AWS_ACCESS_KEY_ID\n- AWS_SECRET_ACCESS_KEY\n\nBut also you can also use the `Shared Config File` mentioned [in the AWS JS SDK v2 docs](https://docs.aws.amazon.com/sdk-for-javascript/v2/developer-guide/configuring-the-jssdk.html)\n\n#### 4) Package and publish your build\n\nInstall the `aws-sdk`:\n\n    npm install aws-sdk\n\nThen publish:\n\n    node-pre-gyp package publish\n\nNote: if you hit an error like `Hostname/IP doesn't match certificate's altnames` it may mean that you need to provide the `region` option in your config.\n\n## Appveyor Automation\n\n[Appveyor](http://www.appveyor.com/) can build binaries and publish the results per commit and supports:\n\n - Windows Visual Studio 2013 and related compilers\n - Both 64 bit (x64) and 32 bit (x86) build configurations\n - Multiple Node.js versions\n\nFor an example of doing this see [node-sqlite3's appveyor.yml](https://github.com/mapbox/node-sqlite3/blob/master/appveyor.yml).\n\nBelow is a guide to getting set up:\n\n#### 1) Create a free Appveyor account\n\nGo to https://ci.appveyor.com/signup/free and sign in with your GitHub account.\n\n#### 2) Create a new project\n\nGo to https://ci.appveyor.com/projects/new and select the GitHub repo for your module\n\n#### 3) Add appveyor.yml and push it\n\nOnce you have committed an `appveyor.yml` ([appveyor.yml reference](http://www.appveyor.com/docs/appveyor-yml)) to your GitHub repo and pushed it AppVeyor should automatically start building your project.\n\n#### 4) Create secure variables\n\nEncrypt your S3 AWS keys by going to <https://ci.appveyor.com/tools/encrypt> and hitting the `encrypt` button.\n\nThen paste the result into your `appveyor.yml`\n\n```yml\nenvironment:\n  AWS_ACCESS_KEY_ID:\n    secure: Dn9HKdLNYvDgPdQOzRq/DqZ/MPhjknRHB1o+/lVU8MA=\n  AWS_SECRET_ACCESS_KEY:\n    secure: W1rwNoSnOku1r+28gnoufO8UA8iWADmL1LiiwH9IOkIVhDTNGdGPJqAlLjNqwLnL\n```\n\nNOTE: keys are per account but not per repo (this is difference than Travis where keys are per repo but not related to the account used to encrypt them).\n\n#### 5) Hook up publishing\n\nJust put `node-pre-gyp package publish` in your `appveyor.yml` after `npm install`.\n\n#### 6) Publish when you want\n\nYou might wish to publish binaries only on a specific commit. To do this you could borrow from the [Travis CI idea of commit keywords](http://about.travis-ci.org/docs/user/how-to-skip-a-build/) and add special handling for commit messages with `[publish binary]`:\n\n    SET CM=%APPVEYOR_REPO_COMMIT_MESSAGE%\n    if not \"%CM%\" == \"%CM:[publish binary]=%\" node-pre-gyp --msvs_version=2013 publish\n\nIf your commit message contains special characters (e.g. `&`) this method might fail. An alternative is to use PowerShell, which gives you additional possibilities, like ignoring case by using `ToLower()`:\n\n    ps: if($env:APPVEYOR_REPO_COMMIT_MESSAGE.ToLower().Contains('[publish binary]')) { node-pre-gyp --msvs_version=2013 publish }\n\nRemember this publishing is not the same as `npm publish`. We're just talking about the binary module here and not your entire npm package.\n\n## Travis Automation\n\n[Travis](https://travis-ci.org/) can push to S3 after a successful build and supports both:\n\n - Ubuntu Precise and OS X (64 bit)\n - Multiple Node.js versions\n\nFor an example of doing this see [node-add-example's .travis.yml](https://github.com/springmeyer/node-addon-example/blob/2ff60a8ded7f042864ad21db00c3a5a06cf47075/.travis.yml).\n\nNote: if you need 32 bit binaries, this can be done from a 64 bit Travis machine. See [the node-sqlite3 scripts for an example of doing this](https://github.com/mapbox/node-sqlite3/blob/bae122aa6a2b8a45f6b717fab24e207740e32b5d/scripts/build_against_node.sh#L54-L74).\n\nBelow is a guide to getting set up:\n\n#### 1) Install the Travis gem\n\n    gem install travis\n\n#### 2) Create secure variables\n\nMake sure you run this command from within the directory of your module.\n\nUse `travis-encrypt` like:\n\n    travis encrypt AWS_ACCESS_KEY_ID=${node_pre_gyp_accessKeyId}\n    travis encrypt AWS_SECRET_ACCESS_KEY=${node_pre_gyp_secretAccessKey}\n\nThen put those values in your `.travis.yml` like:\n\n```yaml\nenv:\n  global:\n    - secure: F+sEL/v56CzHqmCSSES4pEyC9NeQlkoR0Gs/ZuZxX1ytrj8SKtp3MKqBj7zhIclSdXBz4Ev966Da5ctmcTd410p0b240MV6BVOkLUtkjZJyErMBOkeb8n8yVfSoeMx8RiIhBmIvEn+rlQq+bSFis61/JkE9rxsjkGRZi14hHr4M=\n    - secure: o2nkUQIiABD139XS6L8pxq3XO5gch27hvm/gOdV+dzNKc/s2KomVPWcOyXNxtJGhtecAkABzaW8KHDDi5QL1kNEFx6BxFVMLO8rjFPsMVaBG9Ks6JiDQkkmrGNcnVdxI/6EKTLHTH5WLsz8+J7caDBzvKbEfTux5EamEhxIWgrI=\n```\n\nMore details on Travis encryption at http://about.travis-ci.org/docs/user/encryption-keys/.\n\n#### 3) Hook up publishing\n\nJust put `node-pre-gyp package publish` in your `.travis.yml` after `npm install`.\n\n##### OS X publishing\n\nIf you want binaries for OS X in addition to linux you can enable [multi-os for Travis](http://docs.travis-ci.com/user/multi-os/#Setting-.travis.yml)\n\nUse a configuration like:\n\n```yml\n\nlanguage: cpp\n\nos:\n- linux\n- osx\n\nenv:\n  matrix:\n    - NODE_VERSION=\"4\"\n    - NODE_VERSION=\"6\"\n\nbefore_install:\n- rm -rf ~/.nvm/ && git clone --depth 1 https://github.com/creationix/nvm.git ~/.nvm\n- source ~/.nvm/nvm.sh\n- nvm install $NODE_VERSION\n- nvm use $NODE_VERSION\n```\n\nSee [Travis OS X Gotchas](#travis-os-x-gotchas) for why we replace `language: node_js` and `node_js:` sections with `language: cpp` and a custom matrix.\n\nAlso create platform specific sections for any deps that need install. For example if you need libpng:\n\n```yml\n- if [ $(uname -s) == 'Linux' ]; then apt-get install libpng-dev; fi;\n- if [ $(uname -s) == 'Darwin' ]; then brew install libpng; fi;\n```\n\nFor detailed multi-OS examples see [node-mapnik](https://github.com/mapnik/node-mapnik/blob/master/.travis.yml) and [node-sqlite3](https://github.com/mapbox/node-sqlite3/blob/master/.travis.yml).\n\n##### Travis OS X Gotchas\n\nFirst, unlike the Travis Linux machines, the OS X machines do not put `node-pre-gyp` on PATH by default. To do so you will need to:\n\n```sh\nexport PATH=$(pwd)/node_modules/.bin:${PATH}\n```\n\nSecond, the OS X machines do not support using a matrix for installing different Node.js versions. So you need to bootstrap the installation of Node.js in a cross platform way.\n\nBy doing:\n\n```yml\nenv:\n  matrix:\n    - NODE_VERSION=\"4\"\n    - NODE_VERSION=\"6\"\n\nbefore_install:\n - rm -rf ~/.nvm/ && git clone --depth 1 https://github.com/creationix/nvm.git ~/.nvm\n - source ~/.nvm/nvm.sh\n - nvm install $NODE_VERSION\n - nvm use $NODE_VERSION\n```\n\nYou can easily recreate the previous behavior of this matrix:\n\n```yml\nnode_js:\n  - \"4\"\n  - \"6\"\n```\n\n#### 4) Publish when you want\n\nYou might wish to publish binaries only on a specific commit. To do this you could borrow from the [Travis CI idea of commit keywords](http://about.travis-ci.org/docs/user/how-to-skip-a-build/) and add special handling for commit messages with `[publish binary]`:\n\n    COMMIT_MESSAGE=$(git log --format=%B --no-merges -n 1 | tr -d '\\n')\n    if [[ ${COMMIT_MESSAGE} =~ \"[publish binary]\" ]]; then node-pre-gyp publish; fi;\n\nThen you can trigger new binaries to be built like:\n\n    git commit -a -m \"[publish binary]\"\n\nOr, if you don't have any changes to make simply run:\n\n    git commit --allow-empty -m \"[publish binary]\"\n\nWARNING: if you are working in a pull request and publishing binaries from there then you will want to avoid double publishing when Travis CI builds both the `push` and `pr`. You only want to run the publish on the `push` commit. See https://github.com/Project-OSRM/node-osrm/blob/8eb837abe2e2e30e595093d16e5354bc5c573575/scripts/is_pr_merge.sh which is called from https://github.com/Project-OSRM/node-osrm/blob/8eb837abe2e2e30e595093d16e5354bc5c573575/scripts/publish.sh for an example of how to do this.\n\nRemember this publishing is not the same as `npm publish`. We're just talking about the binary module here and not your entire npm package. To automate the publishing of your entire package to npm on Travis see http://about.travis-ci.org/docs/user/deployment/npm/\n\n# Versioning\n\nThe `binary` properties of `module_path`, `remote_path`, and `package_name` support variable substitution. The strings are evaluated by `node-pre-gyp` depending on your system and any custom build flags you passed.\n\n - `node_abi`: The node C++ `ABI` number. This value is available in Javascript as `process.versions.modules` as of [`>= v0.10.4 >= v0.11.7`](https://github.com/joyent/node/commit/ccabd4a6fa8a6eb79d29bc3bbe9fe2b6531c2d8e) and in C++ as the `NODE_MODULE_VERSION` define much earlier. For versions of Node before this was available we fallback to the V8 major and minor version.\n - `platform` matches node's `process.platform` like `linux`, `darwin`, and `win32` unless the user passed the `--target_platform` option to override.\n - `arch` matches node's `process.arch` like `x64` or `ia32` unless the user passes the `--target_arch` option to override.\n - `libc` matches `require('detect-libc').family` like `glibc` or `musl` unless the user passes the `--target_libc` option to override.\n - `configuration` - Either 'Release' or 'Debug' depending on if `--debug` is passed during the build.\n - `module_name` - the `binary.module_name` attribute from `package.json`.\n - `version` - the semver `version` value for your module from `package.json` (NOTE: ignores the `semver.build` property).\n - `major`, `minor`, `patch`, and `prelease` match the individual semver values for your module's `version`\n - `build` - the sevmer `build` value. For example it would be `this.that` if your package.json `version` was `v1.0.0+this.that`\n - `prerelease` - the semver `prerelease` value. For example it would be `alpha.beta` if your package.json `version` was `v1.0.0-alpha.beta`\n\n\nThe options are visible in the code at <https://github.com/mapbox/node-pre-gyp/blob/612b7bca2604508d881e1187614870ba19a7f0c5/lib/util/versioning.js#L114-L127>\n\n# Download binary files from a mirror\n\nS3 is broken in China for the well known reason.\n\nUsing the `npm` config argument: `--{module_name}_binary_host_mirror` can download binary files through a mirror, `-` in `module_name` will be replaced with `_`.\n\ne.g.: Install [v8-profiler](https://www.npmjs.com/package/v8-profiler) from `npm`.\n\n```bash\n$ npm install v8-profiler --profiler_binary_host_mirror=https://npm.taobao.org/mirrors/node-inspector/\n```\n\ne.g.: Install [canvas-prebuilt](https://www.npmjs.com/package/canvas-prebuilt) from `npm`.\n\n```bash\n$ npm install canvas-prebuilt --canvas_prebuilt_binary_host_mirror=https://npm.taobao.org/mirrors/canvas-prebuilt/\n```\n", "readmeFilename": "README.md"}