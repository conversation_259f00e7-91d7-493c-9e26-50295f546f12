{"_id": "revalidator", "_rev": "46-d349f8d0749a581329d714ba37cfc582", "name": "revalidator", "description": "A cross-browser / node.js validator powered by JSON Schema", "dist-tags": {"latest": "0.3.1", "stable": "0.1.1"}, "versions": {"0.1.0": {"name": "revalidator", "version": "0.1.0", "description": "A cross-browser / node.js validator used by resourceful", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/flatiron/revalidator.git"}, "devDependencies": {"vows": "0.5.x >=0.5.11"}, "main": "./lib/revalidator", "scripts": {"test": "vows test/*-test.js --spec"}, "engines": {"node": ">= 0.4.0"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "_id": "revalidator@0.1.0", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.103", "_nodeVersion": "v0.4.11", "_defaultsLoaded": true, "dist": {"shasum": "f747bca0006d8cd1c6c852bac4c4c4fc58162693", "tarball": "https://registry.npmjs.org/revalidator/-/revalidator-0.1.0.tgz", "integrity": "sha512-zYcc2BOcg1KDQTBw+w9nC6cNWJk5yF7gwJUhDnYGnBcO4RhrHj84owsPH6HIkKeAuSf7kwOedtZ2i81inphv/w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCKOSPonxE3sS0pONF6uA+iwD+MD2EwxyS+r4bBB6WwcQIgbP1c2/zqdpbsNrYmvCIchzS3vSZ68UHo+OJA1+8HNrM="}]}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "directories": {}}, "0.1.1": {"name": "revalidator", "version": "0.1.1", "description": "A cross-browser / node.js validator used by resourceful", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/flatiron/revalidator.git"}, "devDependencies": {"vows": "0.6.x"}, "main": "./lib/revalidator", "scripts": {"test": "vows test/*-test.js --spec"}, "engines": {"node": ">= 0.4.0"}, "_npmUser": {"name": "fedor.indutny", "email": "<EMAIL>"}, "_id": "revalidator@0.1.1", "dependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.0-2", "_nodeVersion": "v0.7.8-pre", "_defaultsLoaded": true, "dist": {"shasum": "bae9edfc19183c55e5c33b82bd2065f25d939c6c", "tarball": "https://registry.npmjs.org/revalidator/-/revalidator-0.1.1.tgz", "integrity": "sha512-ziFpvgJzMSjHY0UmthzGwn9CEFYw3Ie9hBuoMCmkcC5tqo7QVY/1MdU5dsGd4IPuSM2+wVypdhmqjFQuRLy4vg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCiuSOJx9thFYA/7nBjEhkTL/1EehreVpgWTa8qtVA0dQIhAIITENZIe/4pcdTFclR5tYxZh61Kw71dyVRnWmHFmxiH"}]}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "fedor.indutny", "email": "<EMAIL>"}, {"name": "marak", "email": "<EMAIL>"}], "directories": {}}, "0.1.2": {"name": "revalidator", "version": "0.1.2", "description": "A cross-browser / node.js validator used by resourceful", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "fedor.indutny", "email": "<EMAIL>"}, {"name": "marak", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/flatiron/revalidator.git"}, "devDependencies": {"vows": "0.6.x"}, "main": "./lib/revalidator", "scripts": {"test": "vows test/*-test.js --spec"}, "engines": {"node": ">= 0.4.0"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "_id": "revalidator@0.1.2", "dependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.2", "_nodeVersion": "v0.6.18", "_defaultsLoaded": true, "dist": {"shasum": "31d5bd93c9145bedd5227e2edee316d9f4914aeb", "tarball": "https://registry.npmjs.org/revalidator/-/revalidator-0.1.2.tgz", "integrity": "sha512-gv/DMVhd36PMwxPEBGNFZ48eJVXAP+7jGcxRTRRV9wOHFKaE1OWVSGmQpq1U0REvEaG6Q/VzPwK4QG+RkyWZXg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFxMPivSEH5fFn6VKX2iEvSFI0oKqUbMXCmIAHpA8QfqAiEAtAze7JkhGG1p5Ph1D1UNzYdjHhqoBcNIFVaqtHvNUlc="}]}, "directories": {}}, "0.1.3": {"name": "revalidator", "version": "0.1.3", "description": "A cross-browser / node.js validator used by resourceful", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "fedor.indutny", "email": "<EMAIL>"}, {"name": "marak", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "josh.hol<PERSON>@gmail.com"}], "repository": {"type": "git", "url": "http://github.com/flatiron/revalidator.git"}, "devDependencies": {"vows": "0.6.x"}, "main": "./lib/revalidator", "scripts": {"test": "vows test/*-test.js --spec"}, "engines": {"node": ">= 0.4.0"}, "_id": "revalidator@0.1.3", "dist": {"shasum": "8cd436b2af59f548d07adca2587a7e5fb2b931d5", "tarball": "https://registry.npmjs.org/revalidator/-/revalidator-0.1.3.tgz", "integrity": "sha512-ZFTKbIS5u4dN6reYTFgeORjTSYqPIwee6M2MFbGDiY1ZJX3XqzlNV5TaZgwtZ2HIxNSBbLYMeDKQT89tVe84BQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHBAKapXxyyaC1kjmSxJjSHxcbKKyCiyJ9clm2gSp4J5AiEA2+2Ho+sdIPaWv7V0A+tbJpkH69lhOrJz1wrfaV2wYEI="}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "josh.hol<PERSON>@gmail.com"}, "directories": {}}, "0.1.5": {"name": "revalidator", "version": "0.1.5", "description": "A cross-browser / node.js validator used by resourceful", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "fedor.indutny", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "josh.hol<PERSON>@gmail.com"}], "repository": {"type": "git", "url": "http://github.com/flatiron/revalidator.git"}, "devDependencies": {"vows": "0.6.x"}, "main": "./lib/revalidator", "scripts": {"test": "vows test/*-test.js --spec"}, "engines": {"node": ">= 0.4.0"}, "_id": "revalidator@0.1.5", "dist": {"shasum": "205bc02e4186e63e82a0837498f29ba287be3861", "tarball": "https://registry.npmjs.org/revalidator/-/revalidator-0.1.5.tgz", "integrity": "sha512-FZaXG5R3KMpBaBTNbcgP2LmlRc9SFytu07PnyXNdZZl4GqQZkEKXDG6aIvUNBMZzEUJI3NXflbih+ANHdhDOZw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHEgzIg3SifkEvgAEIrg5+sL65zIiSf9T1BCcW8U0ZCBAiB8cch4tNEe+ZdZzJRq4a5/OzMhoynTkEPOtyr/tbLenw=="}]}, "_npmVersion": "1.1.63", "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "directories": {}}, "0.1.6": {"name": "revalidator", "version": "0.1.6", "description": "A cross-browser / node.js validator used by resourceful", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "fedor.indutny", "email": "<EMAIL>"}, {"name": "julianduque", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/flatiron/revalidator.git"}, "devDependencies": {"vows": "0.7.0"}, "main": "./lib/revalidator", "scripts": {"test": "vows test/*-test.js --spec"}, "engines": {"node": ">= 0.4.0"}, "bugs": {"url": "https://github.com/flatiron/revalidator/issues"}, "homepage": "https://github.com/flatiron/revalidator", "_id": "revalidator@0.1.6", "dist": {"shasum": "0af2bab68d7d0e766fc4249660ebc2be28b594ba", "tarball": "https://registry.npmjs.org/revalidator/-/revalidator-0.1.6.tgz", "integrity": "sha512-X2OIGpnI5gCIomqTBy3FTnfiioNSyE2dBZ6TiNREYif1UrR6CL/wwMiOxnOLVotO9ZnoSjCiy4R5Kum3vDsP0g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDyIOo1cSfUE7vM5v6DbABs4xMMFO6mhdJMMimJtUzXGQIgFyQHSKnkXsZUi6BOD+awRQVknDEzfE4BmxOwzJ0KHnQ="}]}, "_from": ".", "_npmVersion": "1.3.17", "_npmUser": {"name": "julianduque", "email": "<EMAIL>"}, "directories": {}}, "0.1.7": {"name": "revalidator", "version": "0.1.7", "description": "A cross-browser / node.js validator used by resourceful", "license": "Apache 2.0", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "fedor.indutny", "email": "<EMAIL>"}, {"name": "julianduque", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/flatiron/revalidator.git"}, "devDependencies": {"vows": "0.7.0"}, "main": "./lib/revalidator", "scripts": {"test": "vows test/*-test.js --spec"}, "engines": {"node": ">= 0.4.0"}, "bugs": {"url": "https://github.com/flatiron/revalidator/issues"}, "homepage": "https://github.com/flatiron/revalidator", "_id": "revalidator@0.1.7", "dist": {"shasum": "f0b68b01bc5c5e0e9e6d864154d7098f24fec1db", "tarball": "https://registry.npmjs.org/revalidator/-/revalidator-0.1.7.tgz", "integrity": "sha512-92QXh4+qG32AXaAafpJ95Nem9eqewMoTQ5IginCH+29KJhULugYIucOdNJ7rRO6NooVOQsVWWhZn4jKsz5FnqQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICYHX3Q5jL6W2hqWpCfmRev5chGZsJ6ZWehtdg010xfzAiEAj9pmoBwzit7QYw4QTEEFx99gEXlvXvWygHGeDms4hJQ="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "julianduque", "email": "<EMAIL>"}, "directories": {}}, "0.1.8": {"name": "revalidator", "version": "0.1.8", "description": "A cross-browser / node.js validator used by resourceful", "license": "Apache 2.0", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "fedor.indutny", "email": "<EMAIL>"}, {"name": "julianduque", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/flatiron/revalidator.git"}, "devDependencies": {"vows": "0.7.0"}, "main": "./lib/revalidator", "scripts": {"test": "vows test/*-test.js --spec"}, "engines": {"node": ">= 0.4.0"}, "bugs": {"url": "https://github.com/flatiron/revalidator/issues"}, "homepage": "https://github.com/flatiron/revalidator", "_id": "revalidator@0.1.8", "_shasum": "fece61bfa0c1b52a206bd6b18198184bdd523a3b", "_from": ".", "_npmVersion": "1.4.10", "_npmUser": {"name": "julianduque", "email": "<EMAIL>"}, "dist": {"shasum": "fece61bfa0c1b52a206bd6b18198184bdd523a3b", "tarball": "https://registry.npmjs.org/revalidator/-/revalidator-0.1.8.tgz", "integrity": "sha512-xcBILK2pA9oh4SiinPEZfhP8HfrB/ha+a2fTMyl7Om2WjlDVrOQy99N2MXXlUHqGJz4qEu2duXxHJjDWuK/0xg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICaK7ohKHXAzmuI0I6Eex+lnAdg0DDfPcYtSiDOXn0zsAiBbbuEFEqW/QS6+942ISm/etHTFl3GylQTknb5jkgdrGg=="}]}, "directories": {}}, "0.2.0": {"name": "revalidator", "version": "0.2.0", "description": "A cross-browser / node.js validator used by resourceful", "license": "Apache 2.0", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "fedor.indutny", "email": "<EMAIL>"}, {"name": "julianduque", "email": "<EMAIL>"}, {"name": "swa<PERSON>e", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/flatiron/revalidator.git"}, "devDependencies": {"vows": "0.7.0"}, "main": "./lib/revalidator", "scripts": {"test": "vows test/*-test.js --spec"}, "engines": {"node": ">= 0.4.0"}, "bugs": {"url": "https://github.com/flatiron/revalidator/issues"}, "homepage": "https://github.com/flatiron/revalidator", "_id": "revalidator@0.2.0", "_shasum": "a565bda4bb62d3a637766a288e3335e4f5e7faf5", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "swa<PERSON>e", "email": "<EMAIL>"}, "dist": {"shasum": "a565bda4bb62d3a637766a288e3335e4f5e7faf5", "tarball": "https://registry.npmjs.org/revalidator/-/revalidator-0.2.0.tgz", "integrity": "sha512-YZkv353lZjFc7muHzkql5WnD43VLKEapmQSUl3gseiDzRwcikAjzb+1venv3erdoHYUgeB5I2sZbUctIcZyOxg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEawVNzStaU/OpIJRNMQzGuuUaIkgq2FWfvI+UNO4i6SAiBWWyhlqrSTM+u1KwYGnpu7dLIRLx3PnHCu8x1iUHayFg=="}]}, "directories": {}}, "0.3.0": {"name": "revalidator", "version": "0.3.0", "description": "A cross-browser / node.js validator used by resourceful", "license": "Apache 2.0", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "fedor.indutny", "email": "<EMAIL>"}, {"name": "julianduque", "email": "<EMAIL>"}, {"name": "swa<PERSON>e", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/flatiron/revalidator.git"}, "devDependencies": {"vows": "0.7.0"}, "main": "./lib/revalidator", "scripts": {"test": "vows test/*-test.js --spec"}, "engines": {"node": ">= 0.4.0"}, "gitHead": "b0402567ced2335e464968727a282644c294bb1a", "bugs": {"url": "https://github.com/flatiron/revalidator/issues"}, "homepage": "https://github.com/flatiron/revalidator", "_id": "revalidator@0.3.0", "_shasum": "47154c50e34e875ce93de1eb89365a08b4fdebf5", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "dist": {"shasum": "47154c50e34e875ce93de1eb89365a08b4fdebf5", "tarball": "https://registry.npmjs.org/revalidator/-/revalidator-0.3.0.tgz", "integrity": "sha512-YS/UYUlFZXFxgFi2xHA7Tpt1yJrAbmCOGGb6dGxjeLjwwNJb/1OQ1V8eQdgPnMYryRiDPgCW5RMx7Z1IlSAfMw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBIwNCg1C7x7NGIeda+a/lLmBQ+28uhtYGE9bLRlNFwDAiA3O7IM5HglmEm4150cb3MDA/BkhnIZaouVj4Ts+BPA+Q=="}]}, "directories": {}}, "0.3.1": {"name": "revalidator", "version": "0.3.1", "description": "A cross-browser / node.js validator powered by JSON Schema", "license": "Apache 2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "fedor.indutny", "email": "<EMAIL>"}, {"name": "swa<PERSON>e", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/flatiron/revalidator.git"}, "devDependencies": {"vows": "0.8.x"}, "main": "./lib/revalidator", "scripts": {"test": "vows test/*-test.js --spec"}, "engines": {"node": ">= 0.8.0"}, "gitHead": "76cabc41759b769cb097ef9fc9156031ca49fec3", "bugs": {"url": "https://github.com/flatiron/revalidator/issues"}, "homepage": "https://github.com/flatiron/revalidator", "_id": "revalidator@0.3.1", "_shasum": "ff2cc4cf7cc7c6385ac710178276e6dbcd03762f", "_from": ".", "_npmVersion": "2.1.9", "_nodeVersion": "0.10.33", "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "dist": {"shasum": "ff2cc4cf7cc7c6385ac710178276e6dbcd03762f", "tarball": "https://registry.npmjs.org/revalidator/-/revalidator-0.3.1.tgz", "integrity": "sha512-orq+Nw+V5pDpQwGEuN2n1AgJ+0A8WqhFHKt5KgkxfAowUKgO1CWV32IR3TNB4g9/FX3gJt9qBJO8DYlwonnB0Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCFnkGzEFeeQx8aLiQiLO6hWkfcLjNGMhWXmwcKBDNT7wIgR7MtgaTOTptVrFoFlrWUM2Zmkv7BCh75ItLArq1iaNo="}]}, "directories": {}}}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "fedor.indutny", "email": "<EMAIL>"}, {"name": "swa<PERSON>e", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "time": {"modified": "2023-07-12T19:09:34.918Z", "created": "2011-11-09T15:47:07.865Z", "0.1.0": "2011-11-09T15:47:09.385Z", "0.1.1": "2012-04-16T07:30:12.930Z", "0.1.2": "2012-06-27T06:10:45.429Z", "0.1.3": "2012-10-17T22:14:10.478Z", "0.1.5": "2012-11-12T17:10:42.342Z", "0.1.6": "2013-12-13T19:47:33.284Z", "0.1.7": "2014-04-21T17:58:31.473Z", "0.1.8": "2014-05-14T03:43:12.886Z", "0.2.0": "2014-06-29T20:01:57.838Z", "0.3.0": "2014-10-28T21:53:12.631Z", "0.3.1": "2014-12-08T00:10:33.771Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "http://github.com/flatiron/revalidator.git"}, "users": {"stdarg": true, "tehdb": true, "maobean": true, "christopherstyles": true, "preco21": true, "nkrules": true, "dm7": true, "71emj1": true, "flumpus-dev": true}, "readme": "# revalidator [![Build Status](https://secure.travis-ci.org/flatiron/revalidator.png)](http://travis-ci.org/flatiron/revalidator)\n\nA cross-browser / node.js validator with [JSONSchema](http://tools.ietf.org/html/draft-zyp-json-schema-04) compatibility as the primary goal.\n\n## Example\nThe core of `revalidator` is simple and succinct: `revalidator.validate(obj, schema)`:\n\n``` js\n  var revalidator = require('revalidator');\n\n  console.dir(revalidator.validate(someObject, {\n    properties: {\n      url: {\n        description: 'the url the object should be stored at',\n        type: 'string',\n        pattern: '^/[^#%&*{}\\\\:<>?\\/+]+$',\n        required: true\n      },\n      challenge: {\n        description: 'a means of protecting data (insufficient for production, used as example)',\n        type: 'string',\n        minLength: 5\n      },\n      body: {\n        description: 'what to store at the url',\n        type: 'any',\n        default: null\n      }\n    }\n  }));\n```\n\nThis will return with a value indicating if the `obj` conforms to the `schema`. If it does not, a descriptive object will be returned containing the errors encountered with validation.\n\n``` js\n  {\n    valid: true // or false\n    errors: [/* Array of errors if valid is false */]\n  }\n```\n\nIn the browser, the validation function is exposed on `window.validate` by simply including `revalidator.js`.\n\n## Installation\n\n### Installing npm (node package manager)\n``` bash\n  $ curl http://npmjs.org/install.sh | sh\n```\n\n### Installing revalidator\n``` bash\n  $ [sudo] npm install revalidator\n```\n\n## Usage\n\n`revalidator` takes json-schema as input to validate objects.\n\n### revalidator.validate (obj, schema, options)\n\nThis will return with a value indicating if the `obj` conforms to the `schema`. If it does not, a descriptive object will be returned containing the errors encountered with validation.\n\n``` js\n{\n  valid: true // or false\n  errors: [/* Array of errors if valid is false */]\n}\n```\n\n#### Available Options\n\n* __validateFormats__: Enforce format constraints (_default true_)\n* __validateFormatsStrict__: When `validateFormats` is _true_ treat unrecognized formats as validation errors (_default false_)\n* __validateFormatExtensions__: When `validateFormats` is _true_ also validate formats defined in `validate.formatExtensions` (_default true_)\n* __additionalProperties__: When `additionalProperties` is _true_ allow additional unvisited properties on the object. (_default true_)\n* __cast__: Enforce casting of some types (for integers/numbers are only supported) when it's possible, e.g. `\"42\" => 42`, but `\"forty2\" => \"forty2\"` for the `integer` type.\n\n### Schema\nFor a property an `value` is that which is given as input for validation where as an `expected value` is the value of the below fields\n\n#### required\nIf true, the value should not be undefined\n\n```js\n{ required: true }\n```\n\n#### allowEmpty\nIf false, the value must not be an empty string\n\n```js\n{ allowEmpty: false }\n```\n\n#### type\nThe `type of value` should be equal to the expected value\n\n```js\n{ type: 'string' }\n{ type: 'number' }\n{ type: 'integer' }\n{ type: 'array' }\n{ type: 'boolean' }\n{ type: 'object' }\n{ type: 'null' }\n{ type: 'any' }\n{ type: ['boolean', 'string'] }\n```\n\n#### pattern\nThe expected value regex needs to be satisfied by the value\n\n```js\n{ pattern: /^[a-z]+$/ }\n```\n\n#### maxLength\nThe length of value must be greater than or equal to expected value\n\n```js\n{ maxLength: 8 }\n```\n\n#### minLength\nThe length of value must be lesser than or equal to expected value\n\n```js\n{ minLength: 8 }\n```\n\n#### minimum\nValue must be greater than or equal to the expected value\n\n```js\n{ minimum: 10 }\n```\n\n#### maximum\nValue must be lesser than or equal to the expected value\n\n```js\n{ maximum: 10 }\n```\n\n#### allowEmpty\nValue may not be empty\n\n```js\n{ allowEmpty: false }\n```\n\n#### exclusiveMinimum\nValue must be greater than expected value\n\n```js\n{ exclusiveMinimum: 9 }\n```\n\n#### exclusiveMaximum\nValue must be lesser than expected value\n\n```js\n{ exclusiveMaximum: 11 }\n```\n\n#### divisibleBy\nValue must be divisible by expected value\n\n```js\n{ divisibleBy: 5 }\n{ divisibleBy: 0.5 }\n```\n\n#### minItems\nValue must contain more than expected number of items\n\n```js\n{ minItems: 2 }\n```\n\n#### maxItems\nValue must contain fewer than expected number of items\n\n```js\n{ maxItems: 5 }\n```\n\n#### uniqueItems\nValue must hold a unique set of values\n\n```js\n{ uniqueItems: true }\n```\n\n#### enum\nValue must be present in the array of expected values\n\n```js\n{ enum: ['month', 'year'] }\n```\n\n#### format\nValue must be a valid format\n\n```js\n{ format: 'url' }\n{ format: 'email' }\n{ format: 'ip-address' }\n{ format: 'ipv6' }\n{ format: 'date-time' }\n{ format: 'date' }\n{ format: 'time' }\n{ format: 'color' }\n{ format: 'host-name' }\n{ format: 'utc-millisec' }\n{ format: 'regex' }\n```\n\n#### conform\nValue must conform to constraint denoted by expected value\n\n```js\n{ conform: function (v) {\n    if (v%3==1) return true;\n    return false;\n  }\n}\n```\n\n#### dependencies\nValue is valid only if the dependent value is valid\n\n```js\n{\n  town: { required: true, dependencies: 'country' },\n  country: { maxLength: 3, required: true }\n}\n```\n\n### Nested Schema\nWe also allow nested schema\n\n```js\n{\n  properties: {\n    title: {\n      type: 'string',\n      maxLength: 140,\n      required: true\n    },\n    author: {\n      type: 'object',\n      required: true,\n      properties: {\n        name: {\n          type: 'string',\n          required: true\n        },\n        email: {\n          type: 'string',\n          format: 'email'\n        }\n      }\n    }\n  }\n}\n```\n\n### Custom Messages\nWe also allow custom messages for different constraints\n\n```js\n{\n  type: 'string',\n  format: 'url'\n  messages: {\n    type: 'Not a string type',\n    format: 'Expected format is a url'\n  }\n```\n\n```js\n{\n  conform: function () { ... },\n  message: 'This can be used as a global message'\n}\n```\n\n## Tests\nAll tests are written with [vows][0] and should be run with [npm][1]:\n\n``` bash\n  $ npm test\n```\n\n#### Author: [Charlie Robbins](https://github.com/indexzero), [Alexis Sellier](http://cloudhead.io)\n#### Contributors: [Fedor Indutny](http://github.com/indutny), [Bradley Meck](http://github.com/bmeck), [Laurie Harper](http://laurie.holoweb.net/), [Martijn Swaagman](http://www.martijnswaagman.nl)\n#### License: Apache 2.0\n\n[0]: http://vowsjs.org\n[1]: http://npmjs.org\n", "homepage": "https://github.com/flatiron/revalidator", "bugs": {"url": "https://github.com/flatiron/revalidator/issues"}, "license": "Apache 2.0", "readmeFilename": "README.md"}