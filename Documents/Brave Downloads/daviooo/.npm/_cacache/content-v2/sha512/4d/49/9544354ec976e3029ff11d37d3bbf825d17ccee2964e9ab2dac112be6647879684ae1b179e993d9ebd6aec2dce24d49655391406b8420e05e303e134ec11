{"_id": "mimic-response", "_rev": "9-6910676cbce41e2aa259b3a1bf399d50", "name": "mimic-response", "description": "Mimic a Node.js HTTP response stream", "dist-tags": {"latest": "4.0.0"}, "versions": {"1.0.0": {"name": "mimic-response", "version": "1.0.0", "description": "Mimic a Node.js HTTP response stream", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/mimic-response.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["mimic", "response", "stream", "http", "https", "request", "get", "core"], "devDependencies": {"ava": "*", "create-test-server": "^0.1.0", "pify": "^3.0.0", "xo": "*"}, "gitHead": "44a0267b744d0006b89f69fafc468ed6abbb612b", "bugs": {"url": "https://github.com/sindresorhus/mimic-response/issues"}, "homepage": "https://github.com/sindresorhus/mimic-response#readme", "_id": "mimic-response@1.0.0", "_shasum": "df3d3652a73fded6b9b0b24146e6fd052353458e", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.8.3", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "df3d3652a73fded6b9b0b24146e6fd052353458e", "tarball": "https://registry.npmjs.org/mimic-response/-/mimic-response-1.0.0.tgz", "integrity": "sha512-<PERSON>yxh+TsqCmcTqEqpGdDSRuqt044C42ppSMT/6f4valegHIStgaCH31fvW4ZKL+SH3/eH4hqHDT2LMPf+93shDQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAHbibIpvt1XBLFSrhjIZdryCr47yWzxO2jDa9FYAlr2AiEA8tPKjxnFek6DJc90EBfbu/UzyXqLWMJSrd71NgUJ5TM="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mimic-response-1.0.0.tgz_1496425560359_0.6087012456264347"}, "directories": {}}, "1.0.1": {"name": "mimic-response", "version": "1.0.1", "description": "Mimic a Node.js HTTP response stream", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/mimic-response.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["mimic", "response", "stream", "http", "https", "request", "get", "core"], "devDependencies": {"ava": "*", "create-test-server": "^0.1.0", "pify": "^3.0.0", "xo": "*"}, "gitHead": "687b4544afddca3d3e9e61a455ca5efe0e27851e", "bugs": {"url": "https://github.com/sindresorhus/mimic-response/issues"}, "homepage": "https://github.com/sindresorhus/mimic-response#readme", "_id": "mimic-response@1.0.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-j5EctnkH7amfV/q5Hgmoal1g2QHFJRraOtmx0JpIqkxhBhI/lJSl1nMpQ45hVarwNETOoWEimndZ4QK0RHxuxQ==", "shasum": "4923538878eef42063cb8a3e3b0798781487ab1b", "tarball": "https://registry.npmjs.org/mimic-response/-/mimic-response-1.0.1.tgz", "fileCount": 4, "unpackedSize": 3584, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbRekTCRA9TVsSAnZWagAA3jYQAIIPznt0xQK2f0PlPwFp\nPGY3XFk5wsB6jA0+n4BaeLD3NdQ2zN2wP/ydMnLCY4KbcMhcsRNMapq7C5+J\n28aUasU7ljgoD2HH892XTSH3TlDze9ExbFFXNphlF+p4o+rKVD1xlk6abvSX\n04kgFTIcpL5oBztEPLNEDLWh48Z++hLNcsDcQk+6Mrw69N2Tbl8bJBjnPSnf\nnKAeZe8PqRFKzTXkl1bNiE+jLNp07pX/sUkA/6QBxisk+IcadW5uFZG0GtzZ\neAE7kJdp3yT64flZn8KmD5oHY4UmwyECA4CWEuyfau4ROBlqMEfr4U/1slTf\nuscm7iTx1/6x+/T4PSgytvQxqFx/iJ4FZCQrW8Vb807kntobpnYHU0Yj/AfE\nPuHVIiSvrpE+xUjyaMxA2/A/yBA46NSza3sPBd+rWmJJvY5CjdShIksu66Hx\nfra4BXMu4GcZlNgXJEwbj/jkY8xuVFMyr1YDkkGKQ30A6BHdEObayAxpSSpN\nOpVE0CXeiFgxiL+yPP+oEdStpT9VcUF0Jh1pGsSN6hOGPMYqJqVSs+mhAwkx\nUEkmu9zUOK4WxieYPRKBpvCkewe738Di0tc6d9nI8TCx/5ie4jE7ZTtI0s1L\nNkWmxtDXXD11LWLSn2R2DCSapjSzcbH/00EZIFg6BuuOzb0wQRM5JyYzY9B1\nhQ5F\r\n=+pqq\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCh7hDatfK+YX5IcTU2cC2g6G/FxcW726aaPqPwIP5DIAIhAOLKoxf6NpMZKcjcGQlX5yDwRkqPAmHrteGnLozd/2Pt"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mimic-response_1.0.1_1531308307178_0.4318628271748308"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "mimic-response", "version": "2.0.0", "description": "Mimic a Node.js HTTP response stream", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/mimic-response.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava"}, "keywords": ["mimic", "response", "stream", "http", "https", "request", "get", "core"], "devDependencies": {"ava": "^1.1.0", "create-test-server": "^2.4.0", "pify": "^4.0.1", "xo": "^0.24.0"}, "gitHead": "bffa6bf1a91dc8bbede268948fdfa16af214f504", "bugs": {"url": "https://github.com/sindresorhus/mimic-response/issues"}, "homepage": "https://github.com/sindresorhus/mimic-response#readme", "_id": "mimic-response@2.0.0", "_npmVersion": "6.5.0", "_nodeVersion": "10.13.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-8ilDoEapqA4uQ3TwS0jakGONKXVJqpy+RpM+3b7pLdOjghCrEiGp9SRkFbUHAmZW9vdnrENWHjaweIoTIJExSQ==", "shasum": "996a51c60adf12cb8a87d7fb8ef24c2f3d5ebb46", "tarball": "https://registry.npmjs.org/mimic-response/-/mimic-response-2.0.0.tgz", "fileCount": 4, "unpackedSize": 3596, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcRE4aCRA9TVsSAnZWagAA2DEP/3fTjr0sQOf98GM1H6s+\nR5gk2+ABDSFs98EY79aJc4LfBMgmH7udH7hl1uZTZIyXaB5zjiAWvQ8435wp\nmQb9u3NN9CA9LTq2S8eFvCfkPVWrDRQG1fD/xhj15Srj38evVEuHH40RsTDE\nvCloJUnFdEL76hPp/+SY89pCKppp7iTfDhICTqGjDvDXS/TDhflY9y1qT1Js\nx+iKPV8K2a1ttT4XVHofop7mAPyT5cmNIn1TRT5dUSrQi2QYQ+AwnTFN96Cy\nCPLwobKTl+YubOkUEIcO9y4SHxMQGodqGUO/KQiiU5wDOOPDEbBIXIW7r8Kh\nqjxJ9gTnnX7JEifmG5KurjyMo5HIDuYLIY58V4zmxolo4rHJk8DIwIhV0DN6\na1OLjgnv7L9W8v+BnF/eu4d5+lWnsoYolX0fQKpuxiK13phXL9FyjGswBug0\nQqVViMXz7tRFqKG8uBO+ARX3eYN43/+SBJYAuf1SsCHMmLuQS+mcbnrbE18J\nYi8Zs/W7d6njyrYdr19a0nu0kXNxtwMmzt0ceBl3jowgSv1KxLbRCz2gcVHU\nr3KdHIDkfKgKrlzi4/pbBrmKo389MRxSP5qK8kesb5dq1/3uD1zWxwiSPcQb\ntW1JRH2CQKufIg12nHpjqASz06QHD/0Gj4eYMaB9QLdu5uu0x3uMnk/KLiLs\nwjSG\r\n=Qhei\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAcg03DS6ACJOGx0T56egLgDJobMkzCS2pvUQVlI0Y7QAiBveOBvm6+CU0uff0M5se6SsNTGwmTpzz+o6aYA9nxRJw=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mimic-response_2.0.0_1547980314105_0.26240773097676384"}, "_hasShrinkwrap": false}, "2.1.0": {"name": "mimic-response", "version": "2.1.0", "description": "Mimic a Node.js HTTP response stream", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/mimic-response.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["mimic", "response", "stream", "http", "https", "request", "get", "core"], "devDependencies": {"@sindresorhus/tsconfig": "^0.3.0", "@types/node": "^12.0.0", "ava": "^1.1.0", "create-test-server": "^2.4.0", "pify": "^4.0.1", "tsd": "^0.7.3", "xo": "^0.24.0"}, "gitHead": "94bd5a359bad199d801188473e48cb024c6f31ca", "bugs": {"url": "https://github.com/sindresorhus/mimic-response/issues"}, "homepage": "https://github.com/sindresorhus/mimic-response#readme", "_id": "mimic-response@2.1.0", "_nodeVersion": "10.18.1", "_npmVersion": "6.13.4", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-wXqjST+SLt7R009ySCglWBCFpjUygmCIfD790/kVbiGmUgfYGuB14PiTd5DwVxSV4NcYHjzMkoj5LjQZwTQLEA==", "shasum": "d13763d35f613d09ec37ebb30bac0469c0ee8f43", "tarball": "https://registry.npmjs.org/mimic-response/-/mimic-response-2.1.0.tgz", "fileCount": 5, "unpackedSize": 4734, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeSjebCRA9TVsSAnZWagAALe8P/iFbctdjOJSaAm/zcobz\nbVfPgaosbkybakVYwKS3t0xRWOsMvo47ZafI405J0OiT+B6xrp+zeFxg5Ww+\nbF70Te4CubPA/8RmhEHAypBzt2SbqxhVkUpZbyzF7crAdgC3kzJGpB/ZcJbb\nrmh1rUuQbdGjyIhgYondVCL3eR5DvYiMIlfhEFTtOeqCU7Wj4C26rZ9oCMu3\n8dwLoXG8fqsdFG1cqBFpZqmKvpEQG0fITcle1LsMfeGt6xqmaPC3Cis5Jf5f\nj4HjT7CgCKIQiEa/78dOqA1rxUEMvx72wzpeIUUQrcEUC0XLmWIHW4I036sK\nSUOhJ0dTJAEo8Y3PG9mUY639WVLe9oUiYslavX/kVl5kNsSOpA3ZRCRQm0fE\nf0RqFp7vW6/Amv0QqNYuUXhf/TR3lJeQ6sqvdQZbNW1bJfSLd42occxBN0AK\nJZ5VNC75kW/wqJeFg+ckSOOcGJyHhoxZj420qNB+aNu58DSGs+kBJ/FEcUDR\nhgeFs4KwzCKRfRHETe9Nhv0EOQG5yddTcuuRx8D070Nr82Ofq2QitUoDmz3k\nIqVVyVLf6fLJndAj2adI+4jlEZTF8e14B7I8Rh3LwiI5Yhihft7Qkf4v6Obu\nSVp4xqg2Y63nTx6Ev1Y3MvFONrGcLdEjSnr9VbzDwKNBoOXnlSzt6vEIfJr6\nsx9j\r\n=hu6u\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEqKSfiuiQ03jBVkRME0jsP0P7r1q1i4dqLS92s7ReeSAiAX9d47L4+rFLFfoa12GlXccPnaLGN9whx1vYLDWf9pGQ=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mimic-response_2.1.0_1581922202617_0.29954897440523287"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "mimic-response", "version": "3.0.0", "description": "Mimic a Node.js HTTP response stream", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/mimic-response.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["mimic", "response", "stream", "http", "https", "request", "get", "core"], "devDependencies": {"@types/node": "^14.0.1", "ava": "^2.4.0", "create-test-server": "^2.4.0", "p-event": "^4.1.0", "pify": "^5.0.0", "tsd": "^0.11.0", "xo": "^0.30.0"}, "gitHead": "a198eb503208b023193512b7bf31a491915b774b", "bugs": {"url": "https://github.com/sindresorhus/mimic-response/issues"}, "homepage": "https://github.com/sindresorhus/mimic-response#readme", "_id": "mimic-response@3.0.0", "_nodeVersion": "12.16.3", "_npmVersion": "6.14.4", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-vE8dqYbdBFxcvS5KofMTGZ9I8CA3m00em4jCAruysVt9KfS9lJnSPD47oBfPhZ/rYB1hyY027nEPifrQePfMig==", "shasum": "b7cc6aa6aaec31e6b9b2f17488b258ade8e7aecf", "tarball": "https://registry.npmjs.org/mimic-response/-/mimic-response-3.0.0.tgz", "fileCount": 5, "unpackedSize": 6145, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJevDJ9CRA9TVsSAnZWagAA5P8QAJcXmo9iupBw4LH7YIIX\nOnEYEWh1PU0MCQcmVhJPcspDEDWKHNj/TIY754++fLwPqox8gvBInwV2Pv8F\nSbP+0jb+qmFN/0vAreiY8tdjICyV5ITtHdbwQbl0dK7gKN3TFAhsCwTwngCt\nXo/YqlOfQzw55wNaLMiuZM2DKOvc3k5tJYYrJhIvqPee4OdsieuMx1nuYjYf\nQ9WbPFPGedD41I3kViy4865T8nOsHTGVQ2RJXjYShartio3zPASS4Es7qkI9\nEF9E03yuIYqXqMqE12Gt0U3ou0dF5ocPWUSZuH66Pfy35QDKOAtb5nqmwAG8\nLJBdCGZPSmw1KvNmSNEpV6AV7iSRCTdIiApVfmi0rYO2zqd2MOhkeBzb+oYg\nXPaHnw5lMQdORZMTveqRDkjnBBVXlcdKY3tBBBKCSQcSz9gN1FYtUni0riih\n4YwBo3cRTNKWUvcTZ1wwZ4L8t4U8yffeIRPyuCAze+pzVJfk+2KpILD7yQBC\nSVs81Eyf7ZC0or0EGdPebTkdcnvww3oxeh+Sdyzbwbg7hcY5pPMc6y/llhRu\nYMhAUBCn6sQcumVuAoMxYZ/5ifKy8XzsTCa+l1mkHjB5HMEaPLhleCz+JpHM\nGnsnkZujjAa14wLGe3s0sacSronvLhGw7KZ+OE0kYgGtNYRNrj0pHj92RKaf\nEC6I\r\n=a/Vk\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBAgs6uB6/nBHi4ZZSmbXLuSOootcnXCu4XXSofVSnfMAiEAuzETWGk16/lsogXhcqshhVkGsLxCs6riaOWi6kob0N4="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mimic-response_3.0.0_1589391996948_0.08494827744204869"}, "_hasShrinkwrap": false}, "3.1.0": {"name": "mimic-response", "version": "3.1.0", "description": "Mimic a Node.js HTTP response stream", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/mimic-response.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["mimic", "response", "stream", "http", "https", "request", "get", "core"], "devDependencies": {"@types/node": "^14.0.1", "ava": "^2.4.0", "create-test-server": "^2.4.0", "p-event": "^4.1.0", "pify": "^5.0.0", "tsd": "^0.11.0", "xo": "^0.30.0"}, "gitHead": "d431140fc079b922aa8c998bee0e85b1ffe74e48", "bugs": {"url": "https://github.com/sindresorhus/mimic-response/issues"}, "homepage": "https://github.com/sindresorhus/mimic-response#readme", "_id": "mimic-response@3.1.0", "_nodeVersion": "10.20.1", "_npmVersion": "6.14.4", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-z0yWI+4FDrrweS8Zmt4Ej5HdJmky15+L2e6Wgn3+iK5fWzb6T3fhNFq2+MeTRb064c6Wr4N/wv0DzQTjNzHNGQ==", "shasum": "2d1d59af9c1b129815accc2c46a022a5ce1fa3c9", "tarball": "https://registry.npmjs.org/mimic-response/-/mimic-response-3.1.0.tgz", "fileCount": 5, "unpackedSize": 6003, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJevY5nCRA9TVsSAnZWagAAC4IQAIxgEDcbkQo/76G9pZXp\n0dsJaCkvS+RdEHrhClmN7Ih/d2/+kpNT4k9PYnxLt6zZUxs6eACMyf1rhHG3\n5dr+x/2828a6If1dgSZm+/mwvLlkkQea6VVV4un2BPYDOeNZO1ADubLobjFQ\nxAbijioNyiRQpbVnXdQlzJq1gFGLpWBggmPnEayuuGtxJm11V7FWRosLIX69\nIkV9+37s/lmwh0lPsGuxjrNlyKBSWM/RoMV4eMwDW4V3Gg5n3YBQIoi4ck08\nxhr+wRcgFt7RnIZRNwYmBbpEjJWqzIcgQ+O/zU0fbU23U6AVeTjQ3uICYfKC\n7Hn4IHk9G5MXxJp9+oJFrEcK0kwDeEKtu2nS7utNcLtg1wvIeUP80Mn7U+Ra\n62G2e/sjGbrfRvdSszIW1UGl6vnphl6JnoQtOA+Rvb1x18ZEG4I3rSrK+W1f\nAJmpPxL7siPoXrZW9ie+07EL0U5Z3wYfT9hNNkw0aliKdRuucFI/LrAzub0f\nf0Y553w8Miy+ZILtS0EUwroMG96ZOPRgaBlNJr6ixQjkXPb7YfiAPEPAVgri\n0ALQ6B0TRavzqqKiP23h872pV2kEsr+6rtaOI2HSxlvn+tCguqV2FxM21QSQ\nPcwOZbzNyA+VmMqnQ9cK+ql2Ycgc9UucWHYzcVAkgUf/ogz2xMOLSiocZg7H\nZmKS\r\n=n58q\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDKPM6KSzWXVczQHYUEi5uoDP5REuZ2DXF/MfM8Jp027AiEA1YsaB5UvS+aOSfsuDaTcrGo2RulrIBTq87GAQmz2c18="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mimic-response_3.1.0_1589481062774_0.7721786284338883"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "mimic-response", "version": "4.0.0", "description": "Mimic a Node.js HTTP response stream", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/mimic-response.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["mimic", "response", "stream", "http", "https", "request", "get", "core"], "devDependencies": {"@types/node": "^16.4.13", "ava": "^3.15.0", "create-test-server": "^3.0.1", "p-event": "^4.2.0", "pify": "^5.0.0", "tsd": "^0.17.0", "xo": "^0.44.0"}, "gitHead": "2cb991bc4801a88e0cff37768d41379fabaa8eef", "bugs": {"url": "https://github.com/sindresorhus/mimic-response/issues"}, "homepage": "https://github.com/sindresorhus/mimic-response#readme", "_id": "mimic-response@4.0.0", "_nodeVersion": "16.2.0", "_npmVersion": "7.10.0", "dist": {"integrity": "sha512-e5ISH9xMYU0DzrT+jl8q2ze9D6eWBto+I8CNpe+VI+K2J/F/k3PdkdTdz4wvGVH4NTpo+NRYTVIuMQEMMcsLqg==", "shasum": "35468b19e7c75d10f5165ea25e75a5ceea7cf70f", "tarball": "https://registry.npmjs.org/mimic-response/-/mimic-response-4.0.0.tgz", "fileCount": 5, "unpackedSize": 6201, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhE9+TCRA9TVsSAnZWagAA4UoP/3I+dhcbYLxgwla4hIDA\n2zoMS8GnnAJvsJHiO5WHUM1pRO/Fff+efIlMD1kCsu2kQsu5zrLmc+nqUkaC\nqLRzmGWStdCk6xiqJ+c6Vby2xMdZOmI/wohE46T/1XFngIjkpEMJM4xsnkIS\nKuXWpkymvK6SpHe6D2RqgTVmXzRuzwYV9TFMzLt9dMGEck/ETQt8LDQUO8nO\nqTR7V126KKnm7UMLnlbr833zchloqvXad84EBGSoJWqin6KMi+8HVSm+bXPu\nVyTF5ZjIuMwUu5uZO8qc0vx0iPjs4ixLCPduJM2Xj6DITAFHo7AlRy94a+LL\npR/5LfFKJXVCTqxIIYajO+oQ9IRK8PGs2yOJzMB6v5EaE59hNZ/dQyUleZra\nZ+K124rViRgeMZQwcTRGTWtnMuIqLuuhR7RWjoF31SNuvykH9U5hzbn8diGt\nXwzlThQyDvvJ/B6x3KTtmOwG5cBQMZZRIaiUk9wNTdLIYRZoB7l6N0tgrE0k\n7h1Sehd2c94dcUn1jtrZXw7k9GUUHg9MLEw0ItCWw7XiyN8LzdGPbZ/+MpH6\nvjAilvHy9kseszwbkY6Dldw6yyCcau6aBG7blBbqOZ5ezBCIJKPRW7YW5z/h\nGlZ75+wjrW6QkpXY12RhG0JubCUvu9Ad268gkYAn7pUAU8/pzHeuO6E4ZGJX\nvmqF\r\n=01/Y\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD/cSPcQTDLLSY1iCuXDp9FX9ok0TgeKl2KMeh3Yrpy5AIgLdDN+7earqBD7dPgs16aafC2c6BsuF08nYNs2csW3iQ="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mimic-response_4.0.0_1628692371184_0.7582342499964367"}, "_hasShrinkwrap": false}}, "readme": "# mimic-response\n\n> Mimic a [Node.js HTTP response stream](https://nodejs.org/api/http.html#http_class_http_incomingmessage)\n\n## Install\n\n```\n$ npm install mimic-response\n```\n\n## Usage\n\n```js\nimport {PassThrough as PassThroughStream} from 'node:stream';\nimport mimicResponse from 'mimic-response';\n\nconst responseStream = getHttpResponseStream();\nconst myStream = new PassThroughStream();\n\nmimicResponse(responseStream, myStream);\n\nconsole.log(myStream.statusCode);\n//=> 200\n```\n\n## API\n\n### mimicResponse(from, to)\n\n**Note #1:** The `from.destroy(error)` function is not proxied. You have to call it manually:\n\n```js\nimport {PassThrough as PassThroughStream} from 'node:stream';\nimport mimicResponse from 'mimic-response';\n\nconst responseStream = getHttpResponseStream();\n\nconst myStream = new PassThroughStream({\n\tdestroy(error, callback) {\n\t\tresponseStream.destroy();\n\n\t\tcallback(error);\n\t}\n});\n\nmyStream.destroy();\n```\n\nPlease note that `myStream` and `responseStream` never throw. The error is passed to the request instead.\n\n#### from\n\nType: `Stream`\n\n[Node.js HTTP response stream.](https://nodejs.org/api/http.html#http_class_http_incomingmessage)\n\n#### to\n\nType: `Stream`\n\nAny stream.\n\n## Related\n\n- [mimic-fn](https://github.com/sindresorhus/mimic-fn) - Make a function mimic another one\n- [clone-response](https://github.com/lukechilds/clone-response) - Clone a Node.js response stream\n\n---\n\n<div align=\"center\">\n\t<b>\n\t\t<a href=\"https://tidelift.com/subscription/pkg/npm-mimic-response?utm_source=npm-mimic-response&utm_medium=referral&utm_campaign=readme\">Get professional support for this package with a Tidelift subscription</a>\n\t</b>\n\t<br>\n\t<sub>\n\t\tTidelift helps make open source sustainable for maintainers while giving companies<br>assurances about security, maintenance, and licensing for their dependencies.\n\t</sub>\n</div>\n", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2022-06-19T19:59:36.358Z", "created": "2017-06-02T17:46:00.485Z", "1.0.0": "2017-06-02T17:46:00.485Z", "1.0.1": "2018-07-11T11:25:07.279Z", "2.0.0": "2019-01-20T10:31:54.203Z", "2.1.0": "2020-02-17T06:50:02.733Z", "3.0.0": "2020-05-13T17:46:37.063Z", "3.1.0": "2020-05-14T18:31:02.911Z", "4.0.0": "2021-08-11T14:32:51.305Z"}, "homepage": "https://github.com/sindresorhus/mimic-response#readme", "keywords": ["mimic", "response", "stream", "http", "https", "request", "get", "core"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/mimic-response.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/mimic-response/issues"}, "license": "MIT", "readmeFilename": "readme.md"}