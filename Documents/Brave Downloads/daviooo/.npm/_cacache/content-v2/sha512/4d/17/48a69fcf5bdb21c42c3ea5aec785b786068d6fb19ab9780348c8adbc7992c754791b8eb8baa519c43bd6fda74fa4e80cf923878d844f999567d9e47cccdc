{"_id": "num-or-not", "_rev": "4-9f0df3027608e355b02e1a0bf4268ba8", "name": "num-or-not", "description": "Check for a valid Number value.", "dist-tags": {"latest": "1.0.1"}, "versions": {"1.0.0": {"name": "num-or-not", "version": "1.0.0", "description": "Check for a valid Number value.", "main": "index.js", "scripts": {"test": "ava"}, "repository": {"type": "git", "url": "git+https://github.com/radiovisual/num-or-not.git"}, "keywords": ["Number", "num", "valid", "validate", "check", "undefined", "verify"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://numetriclabs.com"}, "license": "MIT", "bugs": {"url": "https://github.com/radiovisual/num-or-not/issues"}, "homepage": "https://github.com/radiovisual/num-or-not#readme", "devDependencies": {"ava": "^0.7.0"}, "dependencies": {"trim": "0.0.1"}, "gitHead": "7992ac0042dd4fd6c3c2eb1aadc3d27894fbc51a", "_id": "num-or-not@1.0.0", "_shasum": "05ad9cccc60a272ffa002ed0f8736e11fb568d4b", "_from": ".", "_npmVersion": "3.3.6", "_nodeVersion": "5.0.0", "_npmUser": {"name": "radiovisual", "email": "<EMAIL>"}, "dist": {"shasum": "05ad9cccc60a272ffa002ed0f8736e11fb568d4b", "tarball": "https://registry.npmjs.org/num-or-not/-/num-or-not-1.0.0.tgz", "integrity": "sha512-Nnv5e2Z/kOCHyqg8c+F2v6Qk8g1POAqkwYi58S2GXc9kDNKLL3BzPXkF+cugeyGSLCF5t82wJ1lulYDdAplApw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDGgKBotM/OaT1abycffC1NQfFM+ZxprK6VgSRxgc0AZgIgeBa3YZNaopv29L+vsJhu3uGYnAvF/UGyuv3hSEl7/ug="}]}, "maintainers": [{"name": "radiovisual", "email": "<EMAIL>"}]}, "1.0.1": {"name": "num-or-not", "version": "1.0.1", "description": "Check for a valid Number value.", "main": "index.js", "scripts": {"test": "ava"}, "repository": {"type": "git", "url": "git+https://github.com/radiovisual/num-or-not.git"}, "keywords": ["Number", "num", "valid", "validate", "check", "undefined", "verify"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://numetriclabs.com"}, "license": "MIT", "bugs": {"url": "https://github.com/radiovisual/num-or-not/issues"}, "homepage": "https://github.com/radiovisual/num-or-not#readme", "devDependencies": {"ava": "^0.7.0"}, "dependencies": {"trim": "0.0.1"}, "gitHead": "2d4b72d5df17e398646484bc171e423b6e844b25", "_id": "num-or-not@1.0.1", "_shasum": "e0433d7d469bd3547def26d8595c12b9caf90a4a", "_from": ".", "_npmVersion": "3.3.6", "_nodeVersion": "5.0.0", "_npmUser": {"name": "radiovisual", "email": "<EMAIL>"}, "dist": {"shasum": "e0433d7d469bd3547def26d8595c12b9caf90a4a", "tarball": "https://registry.npmjs.org/num-or-not/-/num-or-not-1.0.1.tgz", "integrity": "sha512-IMyEpYE7hBjD/fKvZu7/jhy05scXUYy0KXOoKVjoFNU6di56wpGjCok3SoC9k51993v9N7GSLPf+9PuWq220cg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDIlvtmGZw796pRMN/DArz+ng76iMvkH91BkWcXy8+O9wIgBK0lTvTPm7X+Klm+BTTzSDhHoMw/KvIZR9+TitAg9YM="}]}, "maintainers": [{"name": "radiovisual", "email": "<EMAIL>"}]}}, "readme": "# num-or-not [![Build Status](https://travis-ci.org/radiovisual/num-or-not.svg)](https://travis-ci.org/radiovisual/num-or-not)\n> Check for a valid Number value.\n\nNumbers, and values that can be cast or evaluated to numbers will return `true`.\n\nAll other values will return `false`.\n\n## Installation\n\n```\n$ npm install --save num-or-not\n```\n\n## Usage\n\n```js\nconst isNumber = require('num-or-not');\n\n// Integers\nisNumber(5);            // => true\nisNumber(-5);           // => true\nisNumber('5');          // => true\nisNumber('-5');         // => true\n\n// Floats (Decimal & Comma)\nisNumber(5.0);          // => true\nisNumber('5.0');        // => true\nisNumber('5,0');        // => true\n\n// Octal (ES6)\nisNumber(0o144);        // => true\nisNumber(0O144);        // => true\nisNumber('0o144');      // => true\nisNumber('0O144');      // => true\n\n// Binary (ES6)\nisNumber(0b0);          // => true\nisNumber('0b0');        // => true\n   \n// Hexadecimal\nisNumber(0xFF);         // => true    \nisNumber('0xFF');       // => true\n\n// Exponential\nisNumber(6e3);          // => true\nisNumber(12e-2);        // => true\nisNumber('6e3');        // => true\nisNumber('12e-2');      // => true\n\n// Currency Values\nisNumber('12,456,789'); // => true\nisNumber('45,678.123'); // => true\n\n// \"Evaluates to Int\" values\nisNumber(+'');          // => true\nisNumber(+[]);          // => true\nisNumber(+[0]);         // => true\nisNumber(+true);        // => true\nisNumber(+new Date);    // => true\n```\n\n## Known Limitations\n\n-  These binary and octal formats are **only supported in ES6** environments:\n - Binary: `0b0`\n - Octal: `0o144` & `0O144`\n \n- In order to support numbers with commas or decimals (currencies, Dewey Decimal System, etc), some unique values can pass as valid numbers (see [Issue #1](https://github.com/radiovisual/num-or-not/issues/1)):\n - `1,2,3.4.5`\n - `1,2,3,4.5`\n \n- Open a pull request or issue if you think we need tighter control over these limitations.\n \n \n## Why?\n\nWhy do we need another open source number validator in Javascript? **We don't**, but I wanted one to help keep me accountable of the data type nuances in JS.\n \n## License\n\nMIT @ [Michael Wuergler](http://numetriclabs.com/)", "maintainers": [{"name": "radiovisual", "email": "<EMAIL>"}], "time": {"modified": "2022-06-22T13:12:35.702Z", "created": "2015-12-04T09:49:23.204Z", "1.0.0": "2015-12-04T09:49:23.204Z", "1.0.1": "2015-12-10T12:14:13.330Z"}, "homepage": "https://github.com/radiovisual/num-or-not#readme", "keywords": ["Number", "num", "valid", "validate", "check", "undefined", "verify"], "repository": {"type": "git", "url": "git+https://github.com/radiovisual/num-or-not.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://numetriclabs.com"}, "bugs": {"url": "https://github.com/radiovisual/num-or-not/issues"}, "license": "MIT", "readmeFilename": "README.md"}