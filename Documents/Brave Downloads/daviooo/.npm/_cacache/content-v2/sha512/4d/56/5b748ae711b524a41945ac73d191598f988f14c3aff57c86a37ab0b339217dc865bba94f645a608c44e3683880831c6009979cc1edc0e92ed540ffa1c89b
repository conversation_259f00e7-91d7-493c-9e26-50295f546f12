{"_id": "@types/http-cache-semantics", "_rev": "370-e4cc750c06e355d253f45a42e9c7a623", "name": "@types/http-cache-semantics", "dist-tags": {"ts2.0": "4.0.0", "ts2.1": "4.0.0", "ts2.2": "4.0.0", "ts2.3": "4.0.0", "ts2.4": "4.0.0", "ts2.5": "4.0.0", "ts2.6": "4.0.0", "ts2.7": "4.0.0", "ts2.8": "4.0.0", "ts2.9": "4.0.0", "ts3.0": "4.0.0", "ts3.1": "4.0.0", "ts3.2": "4.0.0", "ts3.3": "4.0.0", "ts3.4": "4.0.0", "ts3.5": "4.0.0", "ts3.6": "4.0.1", "ts3.7": "4.0.1", "ts3.8": "4.0.1", "ts3.9": "4.0.1", "ts4.0": "4.0.1", "ts4.1": "4.0.1", "ts4.2": "4.0.1", "ts4.3": "4.0.1", "ts4.4": "4.0.1", "ts5.7": "4.0.4", "ts5.6": "4.0.4", "latest": "4.0.4", "ts4.5": "4.0.4", "ts4.6": "4.0.4", "ts4.7": "4.0.4", "ts4.8": "4.0.4", "ts4.9": "4.0.4", "ts5.0": "4.0.4", "ts5.1": "4.0.4", "ts5.2": "4.0.4", "ts5.3": "4.0.4", "ts5.4": "4.0.4", "ts5.5": "4.0.4", "ts5.8": "4.0.4"}, "versions": {"4.0.0": {"name": "@types/http-cache-semantics", "version": "4.0.0", "license": "MIT", "_id": "@types/http-cache-semantics@4.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "9140779736aa2655635ee756e2467d787cfe8a2a", "tarball": "https://registry.npmjs.org/@types/http-cache-semantics/-/http-cache-semantics-4.0.0.tgz", "fileCount": 4, "integrity": "sha512-c3Xy026kOF7QOTn00hbIllV1dLR9hG9NkSrLQgCVs8NF6sBU+VGWjD3wLPhmh1TYAc7ugCFsvHYMN4VcBN1U1A==", "signatures": [{"sig": "MEYCIQCXID1B6ts56jODJhubhUqAC5MPsNwzvQjTyst5kINEsQIhAO42t8KvPD9g8p/oqk8ij0f7mOLrE6d406KBOzAQxSbo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9308, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcUfIICRA9TVsSAnZWagAAsMsP/Rk+b67vVYtdd3anjHWj\n8ZCG/BcRZgDOqJm0pSamA4bGkLNQ+fhgBmQCqoSuFIc/UZlX+oFP8yKw1eUC\nMjjw1I1f4OXUpbv4DADkDlvO//naMZaY+5j+bKKrt1FMLibxTtnUTczvtynM\nrHCdJ65w1+vGW/IB9y6km2EOztjD8E9Iw+Awn22zKtJEyAJn8iua+6EpUIEE\n0GjP5wSEcCyZvhNjS4R2JW+AEuUx+cNHPYIaQZ3xqKq+wnArUu3sWiObbbFQ\n/sZ4arD9kevovi3uVPVWo5BncSgYBGSNBhUYFBvj0vhQSDBwOYc04x80soZ4\nelcJ1PF9CrW4OsHmAi7LESDZtcIfUiyKcAHxpLhK1Nuf0tEuuQ7fWu51plD0\n2tMriVxPeu/I4jQxejQgslYBUP+c/5+DWMi5huqYkcwaIlb5kiu5zNwG3/s2\nMAXfxDJk8CfkblhgtZIYLqQRG+CYkEwiLfMxpVotGhX3Xe7i+nknMzWew+Qk\nIym40tFJhbaRQZCJJXIri2tFv0IhNSTOa9Z4E35hgNVEFQ1XiXRrYnT724P6\nkh25QFOpjekSpiwVXA2L5W/hQX1+KZS4ktiGd+2+tNJz7mhPjn0KcAtbl5YM\nYrNBPYi27eMu0pIhzCXPuUNTnnh2HVwDrx167wpiZutlJIaNVPEBJIFLqibd\n84By\r\n=RPp4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for http-cache-semantics", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/http-cache-semantics_4.0.0_1548874248017_0.816693482322266", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "0f2f0f2e4444736e9747a8b7b3cd04c9064067e0181263cfb85337511ae13a35"}, "4.0.1": {"name": "@types/http-cache-semantics", "version": "4.0.1", "license": "MIT", "_id": "@types/http-cache-semantics@4.0.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/http-cache-semantics", "dist": {"shasum": "0ea7b61496902b95890dc4c3a116b60cb8dae812", "tarball": "https://registry.npmjs.org/@types/http-cache-semantics/-/http-cache-semantics-4.0.1.tgz", "fileCount": 4, "integrity": "sha512-SZs7ekbP8CN0txVG2xVRH6EgKmEm31BOxA07vkFaETzZz1xh+********************************+iRPQ==", "signatures": [{"sig": "MEUCIQDdSdtzBGiv/oNlN+5gMi34tJaCCokpNsqynNj9HA2pIwIgV7FhawQyib8+oDHP5pXRS1ID1hDLmbdA9ubvF91T2CU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9555, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5M0ECRA9TVsSAnZWagAA838P/RRssdc/3T11ExCkKjHg\ng/qjQwyaC3kiDTC6CoOsefw8ZnJG1IBFhQPkyoFJRaJz8sH0d3mKA1P1ggLj\npleC8vVrPPOAlRkAKQkoOYXifcLs+gc1tbCnhjB4sGf9Zc3o6YqxNKCVSj3Y\nK2rf/MDFwm71CEmyvYHqu8uXf5eGg8iH1kAKYdobTiQJuh78Cmtf7DixrTU3\nOJpFl73T/VoqSvGqC+IHU+DNkDSoui9QWySLcVaQyrQGfQr9R0CoxIR9Gn0o\nnyaLISQB9c+9+k/ajwJLXvwWkL3+UszqYHq7OBdwX5y0KQFdctVlD/kLAQMa\nYEoKf7nJk6FLxwkGRj4D8qTUSU3Wmi+CyiSrYE2jed4PlW7DQBDDdM8r2DcT\n4sz+q+UqmFoAUe+mLzIHVYHQggfyS/GccBqVaM7cx+mV4HWjU4SF7GuGmhwm\n+jmeM+Je7+YRExiZDYiifTvhqZtOV4IFZ1Rnu8msK1PWj1+KBQTqk1yRL3S7\nTacR7bm7SNIDJBhQUjGiPr4FeMTJ+cyVY37+LXPAfUNvyN6zUp2KXNbQ/2qu\ne1HnRsbWsYqmcmPfPj2PprMq7wlgMLRzsoDRQfiIHV18EHzAJ8xEGGEtGN5N\npjBOrNQaWJo1XELmKOp0CwFrBVvanQw8KMFIExopQJtTVNmgfHSy7orkNSkK\na7oA\r\n=gqIb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/http-cache-semantics"}, "description": "TypeScript definitions for http-cache-semantics", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/http-cache-semantics_4.0.1_1625607428120_0.18961487532327936", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "9ecb3137d8c0ede7c06f5d90c7d4759e560a26effb8846bc51a99b63f03dd2d1"}, "4.0.2": {"name": "@types/http-cache-semantics", "version": "4.0.2", "license": "MIT", "_id": "@types/http-cache-semantics@4.0.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/http-cache-semantics", "dist": {"shasum": "abe102d06ccda1efdf0ed98c10ccf7f36a785a41", "tarball": "https://registry.npmjs.org/@types/http-cache-semantics/-/http-cache-semantics-4.0.2.tgz", "fileCount": 5, "integrity": "sha512-FD+nQWA2zJjh4L9+pFXqWOi0Hs1ryBCfI+985NjluQ1p8EYtoLvjLOKidXBtZ4/IcxDX4o8/E8qDS3540tNliw==", "signatures": [{"sig": "MEYCIQCoeeH9SX/fJ4lAXnZCDqPxFye+oZE8bxbGiMEH9PXdlwIhAMIaOUjLWx8xfQh/kmUjfRcoKapLfvGpIE2hIgD6Eoc6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9556}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/http-cache-semantics"}, "description": "TypeScript definitions for http-cache-semantics", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/http-cache-semantics_4.0.2_1694852420267_0.6654036978643445", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "df6036962b0d372d0ea7f321dd50e4d14789e9818e808b2af19e3fe1c744681f"}, "4.0.3": {"name": "@types/http-cache-semantics", "version": "4.0.3", "license": "MIT", "_id": "@types/http-cache-semantics@4.0.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/http-cache-semantics", "dist": {"shasum": "a3ff232bf7d5c55f38e4e45693eda2ebb545794d", "tarball": "https://registry.npmjs.org/@types/http-cache-semantics/-/http-cache-semantics-4.0.3.tgz", "fileCount": 5, "integrity": "sha512-V46MYLFp08Wf2mmaBhvgjStM3tPa+2GAdy/iqoX+noX1//zje2x4XmrIU0cAwyClATsTmahbtoQ2EwP7I5WSiA==", "signatures": [{"sig": "MEYCIQDRa5KS/Xi5X8Tt97B4HfCflVG/Z4vcHlBp4awY2V0hCgIhAMk4y+NnE8EYNNHPrHa8yKD8gLDarl7Wr9d089p/NosH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9278}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/http-cache-semantics"}, "description": "TypeScript definitions for http-cache-semantics", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/http-cache-semantics_4.0.3_1697604812041_0.13555318026807583", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ed5da3b0a2ecd83696b12f414482994a992c25a83a78b1e8f2a657fc93a7926d"}, "4.0.4": {"name": "@types/http-cache-semantics", "version": "4.0.4", "license": "MIT", "_id": "@types/http-cache-semantics@4.0.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/http-cache-semantics", "dist": {"shasum": "b979ebad3919799c979b17c72621c0bc0a31c6c4", "tarball": "https://registry.npmjs.org/@types/http-cache-semantics/-/http-cache-semantics-4.0.4.tgz", "fileCount": 5, "integrity": "sha512-1m0bIFVc7eJWyve9S0RnuRgcQqF/Xd5QsUZAZeQFr1Q3/p9JWoQQEqmVy+DPTNpGXwhgIetAoYF8JSc33q29QA==", "signatures": [{"sig": "MEUCIGIFhlZ7mDDg4ZsEkrY4g3MlW4+oT0iFK/Kb0R0PI7IJAiEAtgBuAEk00P8y9cfANRoxuI0QsOqGVHBJKSDWeS7XQuo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9278}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/http-cache-semantics"}, "description": "TypeScript definitions for http-cache-semantics", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/http-cache-semantics_4.0.4_1699342474415_0.30209725197783777", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "6cf8e230d4a5ae72d31765a8facf404307c59791befc65343d177843c7bbae91"}}, "time": {"created": "2019-01-30T18:50:47.841Z", "modified": "2024-11-11T08:57:52.600Z", "4.0.0": "2019-01-30T18:50:48.136Z", "4.0.1": "2021-07-06T21:37:08.225Z", "4.0.2": "2023-09-16T08:20:20.542Z", "4.0.3": "2023-10-18T04:53:32.275Z", "4.0.4": "2023-11-07T07:34:34.671Z"}, "license": "MIT", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/http-cache-semantics", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/http-cache-semantics"}, "description": "TypeScript definitions for http-cache-semantics", "contributors": [{"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "maintainers": [{"name": "types", "email": "<EMAIL>"}], "readme": "[object Object]", "readmeFilename": ""}