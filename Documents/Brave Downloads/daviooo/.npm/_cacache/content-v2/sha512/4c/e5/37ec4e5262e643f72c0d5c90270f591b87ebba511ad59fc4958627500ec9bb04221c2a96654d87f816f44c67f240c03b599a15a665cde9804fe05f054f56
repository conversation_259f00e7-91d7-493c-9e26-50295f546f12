{"_id": "cs<PERSON><PERSON><PERSON><PERSON>", "_rev": "196-10d4b1ba5bce99be1f8625333cd25578", "name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "dist-tags": {"latest": "2.0.10"}, "versions": {"0.1.0": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "version": "0.1.0", "dependencies": {"express": "3.2.6", "csv": "0.3.3"}, "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.1.0", "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.1.0-alpha-6", "_nodeVersion": "v0.8.8", "_defaultsLoaded": true, "dist": {"shasum": "a547b63e7fc42680e697f73c9bd4d2db3a7cc50c", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.1.0.tgz", "integrity": "sha512-nc/B5fh1448vPmQdchUA3mG5h+Ip7bAcINFC5bvgasQQKZMlwCbsw8TF8rvzWqzipXAHd7pTfg+Z9GoSuKxApA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCI01Ln8Broy+j4V3YP9sv89cRV9Aam//GIq9zis+sjDwIgLnjIokfPuZNycGUl4uNanGrofb66HMCbtA0gTrOBiQ8="}]}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "directories": {}}, "0.1.1": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "version": "0.1.1", "dependencies": {"express": "3.2.6", "csv": "0.3.3"}, "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.1.1", "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.1.0-alpha-6", "_nodeVersion": "v0.8.8", "_defaultsLoaded": true, "dist": {"shasum": "4d0bd288161dae4945b4214d9972a01c55717f60", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.1.1.tgz", "integrity": "sha512-efx7ACb2wen/9tv8Vp/Afcf+x5Yh8KXquvrXZykx2LUmzm9J/B3tWluGqCn5ExaNzT+4ErC9kx+IeYlR6aU++A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDB3mmbwZ4uX2xIWB/ThrP4T+lnIzjgXUBTSnJ3uFCMdAiBTFgc4CcMc6TghclN+JybEl9WSAy8d0RmOTfHuYRBcXg=="}]}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "directories": {}}, "0.1.2": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "version": "0.1.2", "dependencies": {"express": "3.2.6", "csv": "0.3.3"}, "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.1.2", "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.1.0-alpha-6", "_nodeVersion": "v0.8.8", "_defaultsLoaded": true, "dist": {"shasum": "9b892adff517a1a6daea7a71b1ef19ad57fac9e2", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.1.2.tgz", "integrity": "sha512-yWjWyFhpy7k95IJiPXFHX+R1PYxFyGprAPYRWZHATeu6UHpCgKcEDORHWpZ5MpKxp9XDp7mMYIpPZniZySJPtQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDE+XaTvrtyWu7F4GADIXIdyqfPRSjME7sybgNtBlREnwIgOhr4Spt+LQInoxsxGI+OOHggt00erSVNryazZAj6eRc="}]}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "directories": {}}, "0.1.3": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"name": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "git://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "version": "0.1.3", "dependencies": {"express": "3.2.6", "csv": "0.3.3"}, "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.1.3", "devDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.1.0-alpha-6", "_nodeVersion": "v0.8.25", "_defaultsLoaded": true, "dist": {"shasum": "4d0f78ca115229b56509eea8fe9559945ab1192d", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.1.3.tgz", "integrity": "sha512-5NStLryGcHVjUoNNb7TCLEwOOzJMhzTWUWVLbGllIMsKZ5ro9XkhyRBoevhm+tWDXulmP0u8O6YN7QAgQK/QLw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFtlvCJQO7a6v2AaFWCBFMFnMdh1KPdpcXylxtORnjpsAiBPA6H7cvx3Xzc1UBulE46Pwqs7X5J1AZK224j4qSrRPw=="}]}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "directories": {}}, "0.1.4": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "version": "0.1.4", "dependencies": {"express": "3.2.6", "csv": "0.3.3"}, "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.1.4", "dist": {"shasum": "9063c0e00a78ee58de48a5815533713fda2d2393", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.1.4.tgz", "integrity": "sha512-rgNRglJbP/L78ofAold+29OXZveZl/ODwcT+sSkQODkwfVmTUNKF0i1Q9j9DlPekbqx+fCUNxoedLor2D2Lx+Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDYZt5DGktj2XsmDg8aJexQ+igKg7X5xbjXjE8bH0E5mQIgNi6kUqe1iS+jMOTtJiDAXGcEwJpJDiPD2uIZz3IuSQM="}]}, "_from": ".", "_npmVersion": "1.3.4", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "directories": {}}, "0.1.5": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "version": "0.1.5", "dependencies": {"express": "3.4.4", "csv": "0.3.6"}, "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.1.5", "dist": {"shasum": "6d51b22c0580fbae2d763c0f02efa66d23d80a44", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.1.5.tgz", "integrity": "sha512-qUHtBiBux+m59PSn/egs209QNWQKGiIomwVaOZXf9vhKZzTLOUqZilHTIp8tAuAC8sh4T3FSxw5hlIvc1UkMRA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDa+8ojEN/BLBRkmEDVBICKF+0Vfr/9BRrvZ+G0tyBZawIhAKleHbBaAmMiMpx1bwmLhct2YRMsqnALkNxP3kKy7TKr"}]}, "_from": ".", "_npmVersion": "1.3.4", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "directories": {}}, "0.1.6": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "version": "0.1.6", "dependencies": {"express": "3.4.4", "csv": "0.3.6"}, "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.1.6", "dist": {"shasum": "6e53388508269c7037634e9a506e3e06e35da231", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.1.6.tgz", "integrity": "sha512-S5yMndGsUEGdv4wWhguG0F8vxHRrQI3JUL3w9aOJ4OwxSLuGmp7S+rhobT065NdY6uJQyyVIwbpcrVMlzl6Hrg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAt8NBdlc7waI8W5DHAj5OryjUBS9R9Iz+ujGoWCTnMWAiEAw3RVKVfX/LNOMarKyR+KNFixFNbfc8uNPkkIb6zNJIM="}]}, "_from": ".", "_npmVersion": "1.3.4", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "directories": {}}, "0.1.7": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "version": "0.1.7", "dependencies": {"express": "3.4.4", "csv": "0.3.6"}, "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.1.7", "dist": {"shasum": "51b3d2502c01a12d1407464627c5ba41be84a0ce", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.1.7.tgz", "integrity": "sha512-YKRSG2uW+gXMezO0rIAssfoJjP4OeyRxajc+joNUfv9Z1LV+Z5Ad3+yqswzWb2l54obzDOUuT+K6d1x6MyoHKw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC/fXRuipzQEJqlkGvk72bH4MnN0rogMU6W7fWRfkjTUAiBgET0duFY2XAs0zYO6utWalvOhiIEaU9K/T6ywHB5+gQ=="}]}, "_from": ".", "_npmVersion": "1.3.4", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "directories": {}}, "0.2.0": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "version": "0.2.0", "dependencies": {"express": "3.4.4", "csv": "0.3.6"}, "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.2.0", "dist": {"shasum": "1d0f50f2200137cbfbb4064e49521e5d2f6fed74", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.2.0.tgz", "integrity": "sha512-3zQj5Hm2WSEKCC9lIHDF4qi0KMiyNQeYLcB+lk/My//YVDi1XXgXagRk/dokqN7FLLqjURWh38uxxwc2kjilvQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIERYOQ0Rd+bmK0ZteeFRNKWqSEfbtRvUgfFs4OVZ3MEjAiAwmR10WbZFu1rb9yZzk+i5DUGUr9rhjxQzlzDy9DyyKA=="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "directories": {}}, "0.2.1": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "version": "0.2.1", "dependencies": {"express": "3.4.4", "csv": "0.3.6"}, "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.2.1", "dist": {"shasum": "fff307ff7251179004275e34991131eaef511aed", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.2.1.tgz", "integrity": "sha512-enexJLE4H/ZJX27vIz6BsFRS86KR2yFpjz0q8ldighV30yTjjDLs7Y8IOKXGcDbNE6svF7e51cxaUg7fWDKRqg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGUGmuKbqiDb62lYOMUQHgM9HHEpuTH9XFfMf3Km7h/RAiAucuYlBMPmqJoLsya92kUwTOhT5nkPKxBqkRtjnn15dw=="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "directories": {}}, "0.2.2": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "version": "0.2.2", "dependencies": {"express": "3.4.4", "csv": "0.3.6"}, "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.2.2", "dist": {"shasum": "72c7de02cdd7c1a8477d6f2ec0390eb856d8bf46", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.2.2.tgz", "integrity": "sha512-MriMr7mai+7BOJifcFCyFO1krquVNYBXkvv++Tz64JRAHmUA7t8fTjFdjNOAL8agvRJgsDpp1tpbC20plO1jAg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCNTBfRMjs8AUXInxl7kj1TF1gFgeH+3TxNtrvRr7byugIgR/Vh7tt10FNPhi56n44cGiXJdhxkNMlojl2X1ZkFy6Y="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "directories": {}}, "0.3.0": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "version": "0.3.0", "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.3.0", "dist": {"shasum": "9540095f00a86352efee55bc34420b7f7b8883c7", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.3.0.tgz", "integrity": "sha512-ZHZsMTQJUyNCoZ1EResEQZBn1Bdfjib3N/bb+3Un/3cW1uLyDvEJkjbib/7IJMy+q2xf8ziKVSoK8aObIIa8RA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDqIghSB9BhvINhWokMX4AZ3bw7rq4A+uFmAPwa1kN+ZgIhAJKWTfOOuId1ZUQ/IvUTtSWjQFJTAzj7/ZrlP7VN2JIN"}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "directories": {}}, "0.3.1": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "version": "0.3.1", "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.3.1", "dist": {"shasum": "7a4f29392a3c0afd52c37fadf57e62f4c908c9e8", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.3.1.tgz", "integrity": "sha512-VFh1GBzQAE2m3gnGFzsZJscUImOn+fevWvm381PdhJnKLEY1MDUVQPjtkpcncrHzceXRGK98n6tbY5Zyw2rrPQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICT5/axUGMlLAz3G6pZixr9r9UNyv9dGATmfCub3HGMYAiBFOJTzMCUgbZOnSUv6mn3tV1VGb+L0cxk8gHHfJHgEnw=="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "directories": {}}, "0.3.2": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "version": "0.3.2", "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.3.2", "dist": {"shasum": "a8aa396b9bd414c217509c2405929439384c930a", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.3.2.tgz", "integrity": "sha512-Pt8xYGb7CGWwsp8FSMK2zpFRDiXx1Wb0zNBhLHoVouVv+YYajcXLynb9hyV0KUAQGHtR9H5tgIVQ8EBalmsPCQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDg4SGMu/bNcxFmxD5KRktztZK42h72X7efNaQSGUY7PAiEAnNIvdjXA/Kdx+qBtVT7/bbZ/8EL93CgCDnvsUsURFKk="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "directories": {}}, "0.3.3": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "version": "0.3.3", "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.3.3", "dist": {"shasum": "7edb30ddf86566752248666adc61f6445241435f", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.3.3.tgz", "integrity": "sha512-nnJEzMJP0G9E3ch5YHwML9ZBAS18DNwDm3TaQu5kFy1L4Qjlpf3ev0CT8gUp75m05aJJLOnsNB9Dd2O+KPt0IA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGfQAXOoYP9vbKm3lxYshMYsGtjybs9m+IcUpdIBT35dAiEAonFu+4+59fDnDu7jtnMSpnNe1GlyEIzdIx8T965mOSc="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "directories": {}}, "0.3.4": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "version": "0.3.4", "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.3.4", "dist": {"shasum": "b79fc61bda220c28a0150ab338ee52b223b4c2ab", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.3.4.tgz", "integrity": "sha512-bKpymVFx9RAudWgg4iRFurYDTZKgEROY2v/+gWuiA2N6W1nPwtibGB3y0ypBm2XcW/jND1HrFEQl80FXmY+BsA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICj4oG7UxZ8ehKp/9qgg2iqyr+BvriEfIBOPfWOKEaJ4AiEAho3Db+eIlRSuCAHouYCMb/VRVIIBnwDjd9+2KNhnPyA="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "directories": {}}, "0.3.5": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "version": "0.3.5", "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.3.5", "dist": {"shasum": "63e3400f2b1633a9ec5ec3f79225eca3c34cc3ca", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.3.5.tgz", "integrity": "sha512-GOTLh6ncilg3ORV2gZEYg5+Gb6r/J4BpHCvrIf05V2dRSR/zMSfdvjvT9bRcONi/Tb5UWrlrKw4t6HVIulPXoQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCzEXXxhReO+zFQVtepkYR/sKTZsDsnRS65Knc/t9jhBgIgRDu1y0RX7VdqeFaUeF4jG5XrsnrCJhh/xL1o1zeL+rQ="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "directories": {}}, "0.3.6": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "version": "0.3.6", "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.3.6", "dist": {"shasum": "a03fdacd412e5e866f9b1963ee92f90cdc52f124", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.3.6.tgz", "integrity": "sha512-Fw2DWpoDkqfUkR95ToOWYDiS7IXYpNI/U+JKp5YVfAkT7v2vbbLWHqxM46pUvz0DaTcJDXd2VkeCInl63UeaVA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCkqabsGWb8I6jKr8ryPjEqS9Y69MNiXlFYHB+2DMKmGAIgUuMNeCmVoKTVBqDuSRtFFMavmuYGtwawDHMQ5fqd+xY="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "directories": {}}, "0.3.7": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "version": "0.3.7", "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.3.7", "_shasum": "2d28a981abbc09c41723c489bbe5b39153ca93cd", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "2d28a981abbc09c41723c489bbe5b39153ca93cd", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.3.7.tgz", "integrity": "sha512-mobOijcOXG4ia9H1WghA1hYquDq5FJuxQwCF6QfGfZoYXRQiqeACafDg6W47HKoSuUgglaAnNxY4hIxt1vQusw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDkSziS02aAXMQ3VGLKd8+xck10FHmCjiIqVT7kekcwYAiEArk3FK1/pyXms+46ugLmC5sebYVuWppqzxEFeqGpOcX8="}]}, "directories": {}}, "0.3.8": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "version": "0.3.8", "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": [{"type": "MIT", "url": "https://github.com/Keyang/node-csvtojson/blob/master/LICENSE"}], "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.3.8", "_shasum": "f2baafac96ad920452266d093c3a6705613b74c6", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "f2baafac96ad920452266d093c3a6705613b74c6", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.3.8.tgz", "integrity": "sha512-k3c1Bzb5rdu+7irqaIGtE8wyKJEUHiKactZSJyPAqmNh6474t1TwfekYNpok1fUHwRAXuyU1pmU2xWMZpaFG/w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDTi2/0xHkhW88pgiCr1Btpy93SLBAlxrv1ihwVq9aHKQIgerwWnjM7TYMpRY/rQeIJc4yFVTNx8cZkTePEl5SwvlY="}]}, "directories": {}}, "0.3.9": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "version": "0.3.9", "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": [{"type": "MIT", "url": "https://github.com/Keyang/node-csvtojson/blob/master/LICENSE"}], "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.3.9", "_shasum": "b5f264ebb3af06a1293137406aae0245dd0f51e1", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "b5f264ebb3af06a1293137406aae0245dd0f51e1", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.3.9.tgz", "integrity": "sha512-fSblb4X0wslE1d0ucSVVGHvVvEwSZGYtoA0If30l2aOTAUXyHhSrjq68e2gQOjWKRcx6fsZftrsHCSp6w8YeHw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDoC5OmCst1NMqSjyOOkG7Y4cXuLVwDJTtNtI3YD1bhUAIhAL9Yp1M+BvlG3PuN4sHsfpOdunNRNfnwsMVcXyMtobH+"}]}, "directories": {}}, "0.3.10": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "version": "0.3.10", "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": [{"type": "MIT", "url": "https://github.com/Keyang/node-csvtojson/blob/master/LICENSE"}], "engines": {"node": ">=0.10"}, "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.3.10", "dist": {"shasum": "96d92267ee9be0f5d05c00eaeeb35484828e7b83", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.3.10.tgz", "integrity": "sha512-GkH9UYJgSJvO89Bx1HUkTZfgi0ZEs9CX8wOBkoL8TjzPhbvZHFzyXOBjA4QuRq3FH1S9j644q/+YLkQrRjjnmw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCLT118jrOHpvFujcj7AM5EqZZncCEfEXU8bfZV1I9dKAIgPC8UbnkCQmFaxdEeGYykitWd9ySrJ985wpm8sWqSBiE="}]}, "_from": ".", "_npmVersion": "1.3.2", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "directories": {}}, "0.3.11": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "version": "0.3.11", "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": [{"type": "MIT", "url": "https://github.com/Keyang/node-csvtojson/blob/master/LICENSE"}], "engines": {"node": ">=0.10"}, "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.3.11", "_shasum": "0d1079d4a0ebf6f4058b5ae236b3958a12164990", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "0d1079d4a0ebf6f4058b5ae236b3958a12164990", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.3.11.tgz", "integrity": "sha512-7G0UnB3R0S/GRxw8DSIklanu0PRY5NtpgipZz1YxFNh1eRXL13wc2HSeS2jbHp5e0LcIkpr11/tGuDAvoB7bwg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDO8GDQIqH0M813yMrh1rsEBjLiPCReORl8xY2BhkoVqwIhAOebjqdDE5kO5bQQJuM1Ye8GiqaDEc7bGwUwn58jNidm"}]}, "directories": {}}, "0.3.12": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "version": "0.3.12", "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": [{"type": "MIT", "url": "https://github.com/Keyang/node-csvtojson/blob/master/LICENSE"}], "engines": {"node": ">=0.10"}, "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.3.12", "_shasum": "338071e4518f9c397011074c042ce30a2ec9a182", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "338071e4518f9c397011074c042ce30a2ec9a182", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.3.12.tgz", "integrity": "sha512-C67+NhAFdvP/jptXhSn76cRKSIeEELkoU39Q0tur30BHGOErIB2C68f9nJKlh/FWQXl+91UHQsFLPmH36F65iQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCPznJepJQcpsGuGn0lAHtbNBP3l5kdKNFxvqT7k3xH/wIhAMnWIakMySPSuGTeAzNddEGJHxbSzPbN5kzKL0H4DWuy"}]}, "directories": {}}, "0.3.13": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "version": "0.3.13", "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": [{"type": "MIT", "url": "https://github.com/Keyang/node-csvtojson/blob/master/LICENSE"}], "engines": {"node": ">=0.10"}, "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.3.13", "_shasum": "5d5f29e5849142bdff58afd63619e5800af73875", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "5d5f29e5849142bdff58afd63619e5800af73875", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.3.13.tgz", "integrity": "sha512-52dQ8MA/x2eyH1rNOb7jHQ4NsApzDiBRdZH5GonhPAe4DVDjP15BbJMplQh1DV/XQaydrXhcaQmS3dBZw/H+Zg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAmC+GC1rWBF8rFtBYTZ5R4Detwl7fFYGAI+fpG+da42AiAdPQnGtL5jahrnXH5jes7x1eIX/63ntUgoWluV31syLw=="}]}, "directories": {}}, "0.3.14": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "version": "0.3.14", "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": [{"type": "MIT", "url": "https://github.com/Keyang/node-csvtojson/blob/master/LICENSE"}], "engines": {"node": ">=0.10"}, "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.3.14", "_shasum": "2b357f9abbaf95c13a9c5b3ac34ff914de57294a", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "2b357f9abbaf95c13a9c5b3ac34ff914de57294a", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.3.14.tgz", "integrity": "sha512-RUKbYzPpkYZSBAj+VqWLg8OR8sNlwU/jlyN9Gh85PdV4uhbCb2vDYkBj0y67ZooxfgSJR9/Uma2G9c7QC71E+g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCF4y8GjGjShFpsuUJmkxHSFqaMjXQucyyTxIynI6AeHgIhAOdwwqVRHDCbYgpajksKQLswObOoKOA/Uxy98FKiQziz"}]}, "directories": {}}, "0.3.15": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "version": "0.3.15", "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": [{"type": "MIT", "url": "https://github.com/Keyang/node-csvtojson/blob/master/LICENSE"}], "engines": {"node": ">=0.10"}, "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.3.15", "_shasum": "67816f32efe5b47337c1f48822e0dcc85bad0b49", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "67816f32efe5b47337c1f48822e0dcc85bad0b49", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.3.15.tgz", "integrity": "sha512-oo0TSL4li6I7TsIj3q+l3dKovH6HH0OnUkt/8euPDD0Azj4amiGRlr2zQ+ZUrkXTB4WsVYy3FsnhuVD6FjbaDw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDZn3ofITFtzcG2f+18D5Zu6Wt6a8q/swUm7KVUGQGOiAiBl7BuNsM9Vpb0QVkAyeY/Vm8OUyi2uQ3mnjuNgDWye/w=="}]}, "directories": {}}, "0.3.16": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "version": "0.3.16", "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": [{"type": "MIT", "url": "https://github.com/Keyang/node-csvtojson/blob/master/LICENSE"}], "engines": {"node": ">=0.10"}, "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.3.16", "_shasum": "c7a5b84d0bf68c444f0dfd99ac480788f51cfb76", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "c7a5b84d0bf68c444f0dfd99ac480788f51cfb76", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.3.16.tgz", "integrity": "sha512-9Tn+8Zxx+GZGsZ3pkt7EdhyMi/8jbB8WrGkCZpAGbLYUazZmFuEyNJsWJnfbAagLaajgxgtWf8TRwmM3Zjp5PA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCt80bmmJYUYICH2SMXtYFNgrzhHlh2PhJN7axbGxan3QIgMb74RwSnWgCis60VdTwVYZtQbjz26tvc2QdpteXY7xo="}]}, "directories": {}}, "0.3.17": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "version": "0.3.17", "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": [{"type": "MIT", "url": "https://github.com/Keyang/node-csvtojson/blob/master/LICENSE"}], "engines": {"node": ">=0.10"}, "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.3.17", "_shasum": "b3c5f15aa59598199bbe637212cfc997ddba5530", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "b3c5f15aa59598199bbe637212cfc997ddba5530", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.3.17.tgz", "integrity": "sha512-Q7z/sUMd2wpmUnkwZPbIakcZCOmFZoNmzr44eFuMsSf2Pox+PrI3RLcBR8yXyfNwUHI+sJOz4kT/Lvm+FedypQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCR31rG0C0MOrVkpdJb3No0AvN8PYpZaGMP+6KmFiwQnAIgFFEzlvB03To2PClwXZaPAPqMCHo/hwGl+P7ZxgIGhwA="}]}, "directories": {}}, "0.3.18": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "version": "0.3.18", "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": [{"type": "MIT", "url": "https://github.com/Keyang/node-csvtojson/blob/master/LICENSE"}], "engines": {"node": ">=0.10"}, "gitHead": "cb0f164be8c00606bfa0c37efc5a7f6dda04eb51", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.3.18", "scripts": {}, "_shasum": "b29269db3df87fcd62670acd9c709c57ca04c475", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "b29269db3df87fcd62670acd9c709c57ca04c475", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.3.18.tgz", "integrity": "sha512-KfCBUUZlRiyU7n+IN6PyY5lB9Xn5yDCdBrpKUFb+SpgEjdX0jO8a+u5y82IxfZRm6YAFFaBOvuRsU8Yi7ROsdQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGxdiJX/VO/iDYmbCjEcq779+nkj7Hfs+HYH1Swvi1XNAiEA1yoOfGp7eip34DZ+3cSoLuCwaLdpt1z74zdi8bMS0W8="}]}, "directories": {}}, "0.3.19": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "version": "0.3.19", "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": [{"type": "MIT", "url": "https://github.com/Keyang/node-csvtojson/blob/master/LICENSE"}], "engines": {"node": ">=0.10"}, "gitHead": "8e6744461bc0ffc4a24738be082db3308c6028b5", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.3.19", "scripts": {}, "_shasum": "f8ada7f925b00d08bba0c36a7b76c63aabecb15e", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "f8ada7f925b00d08bba0c36a7b76c63aabecb15e", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.3.19.tgz", "integrity": "sha512-OJE3SLyfEnjtkxO2kRC1h/vzBGKWh20Op6ZYqQ+ikY8loDZiWUDPtTXhxCbp/HzlCJSFQ4OELt51Y673u43Lbw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID6lpzqmU2OIE0VqC0F9Xd+2n9XDR0zfBTS2bFAaYpcEAiEA7jwzYNQ/KVmDfxjhUGrQfEKcVuFV2R95PChaaPhaDuI="}]}, "directories": {}}, "0.3.20": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "version": "0.3.20", "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": [{"type": "MIT", "url": "https://github.com/Keyang/node-csvtojson/blob/master/LICENSE"}], "engines": {"node": ">=0.10"}, "gitHead": "8023c232886bb43cb1ac4291e1ca40a2813248eb", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.3.20", "scripts": {}, "_shasum": "946696550ed4b2ad06b932a02f7178386aa1f938", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "946696550ed4b2ad06b932a02f7178386aa1f938", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.3.20.tgz", "integrity": "sha512-Z7VSNyBD3u5zXnMTL1JeW8iyJ/jHHkmE3mSir6CV3jrs2DHeqGjc+QD8gcg/CaXIlrLQcfsddna4W+kBxJGDJQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDF2yKwMxRaQHFlj5069+Qg<PERSON><PERSON><PERSON><PERSON><PERSON>K1CP/hYvx0jGqtRAiBgcfjj7D4MOHVJW6+9iGqGx6LhD5MCNOseXOLQC2K1sw=="}]}, "directories": {}}, "0.3.21": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "version": "0.3.21", "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": [{"type": "MIT", "url": "https://github.com/Keyang/node-csvtojson/blob/master/LICENSE"}], "engines": {"node": ">=0.10"}, "gitHead": "2121aa68acd722c69f54aa708d8c50c78385e53d", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.3.21", "scripts": {}, "_shasum": "800ecfd06619c257fc8880f8ae922d5dea83fe60", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "800ecfd06619c257fc8880f8ae922d5dea83fe60", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.3.21.tgz", "integrity": "sha512-4i5o5ntbG3iQdNhp45Ywbjb0mPMe1viIx5E/IFdDr6nd9DzJj9ad7tJtCdddEH1HZ62c7v8O7nB55jGMDxkiIw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH1/aMWMAFFQtAwuEgD2qTgGfaizMYlW4CvHzYTb+ONJAiBSdZbEhVRykk3a+KPUBLVwSPsuwveJ+l9TSUIuGyBr/A=="}]}, "directories": {}}, "0.4.0": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "t3do<PERSON>@gmail.com"}], "version": "0.4.0", "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": [{"type": "MIT", "url": "https://github.com/Keyang/node-csvtojson/blob/master/LICENSE"}], "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.9.1", "grunt-contrib-watch": "^0.6.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "mocha": "^2.2.5"}, "dependencies": {"async": "^1.2.1", "underscore": "^1.8.3"}, "gitHead": "191ed80271317fedbe5780f91efe47704bdd97ef", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.4.0", "scripts": {}, "_shasum": "db7ef8467c989a7a70153b7b55b554b97f7b7ec2", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "db7ef8467c989a7a70153b7b55b554b97f7b7ec2", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.4.0.tgz", "integrity": "sha512-mn7K1YdHgIL2PZwkQMlMr09XvuSG+/rkR4507kylW5hKV41ZnTN1YSuzcfMF46gP3iKqz7C3kSZT824L30GRGg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEFPozbvOWpEqaKAhwtXx9Pb7wS2Z9BfKGHcEjXDhAccAiA8nNUzJ57HR/dQ8faG5XpIElqY6usAFvq44PdnTf1epA=="}]}, "directories": {}}, "0.4.1": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "t3do<PERSON>@gmail.com"}], "version": "0.4.1", "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": [{"type": "MIT", "url": "https://github.com/Keyang/node-csvtojson/blob/master/LICENSE"}], "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.9.1", "grunt-contrib-watch": "^0.6.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "mocha": "^2.2.5"}, "dependencies": {"async": "^1.2.1", "underscore": "^1.8.3"}, "gitHead": "ba6aeaecf23367071a65923b1fe737c9478731e6", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.4.1", "scripts": {}, "_shasum": "9959f4eafc489c5d817a4bf5cbf9a1ce91462dba", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "9959f4eafc489c5d817a4bf5cbf9a1ce91462dba", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.4.1.tgz", "integrity": "sha512-UVM994J1tvik/wOzd7HzCyaO3OiYfjEuL38whZRUbVj/zcYPeOWm3cnsoWIduFiDkiGeT6/ueNyg68b+9Z5J6w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCrNgfLvcpjbcNUE78MjakLAwPl/7trOSZ9bG5KssaI2AIhANU439oNDp+Yx1PQscCsSw9q9ZSL/8C3K5pIhJvLKv4q"}]}, "directories": {}}, "0.4.2": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "t3do<PERSON>@gmail.com"}], "version": "0.4.2", "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": [{"type": "MIT", "url": "https://github.com/Keyang/node-csvtojson/blob/master/LICENSE"}], "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.9.1", "grunt-contrib-watch": "^0.6.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "mocha": "^2.2.5"}, "dependencies": {"async": "^1.2.1", "underscore": "^1.8.3"}, "gitHead": "ba6aeaecf23367071a65923b1fe737c9478731e6", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.4.2", "scripts": {}, "_shasum": "a8b0c6c478bc690ad5e56c3d6ae9bc4d9624e253", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "a8b0c6c478bc690ad5e56c3d6ae9bc4d9624e253", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.4.2.tgz", "integrity": "sha512-RKRWebambKu0d+3OtCb2whEKdojhmQyclvXEwB8xN6lyuPU5AE//Z4GA+IeUSu75W5KWkG8kSFl+2BwrO3FBdQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICsHf2e2snFK91A/yu95tEL6A+rJZYFG2BraGHyb1wDLAiA8yuKukZ5i7JecwWWd1WPQgPmpnwK46CRnAIGQD6+M0g=="}]}, "directories": {}}, "0.4.3": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "t3do<PERSON>@gmail.com"}], "version": "0.4.3", "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": [{"type": "MIT", "url": "https://github.com/Keyang/node-csvtojson/blob/master/LICENSE"}], "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.9.1", "grunt-contrib-watch": "^0.6.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "mocha": "^2.2.5"}, "dependencies": {"async": "^1.2.1", "underscore": "^1.8.3"}, "gitHead": "f9033f7f22aee1ccf2fd971ae886d2c4cd13e2aa", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.4.3", "scripts": {}, "_shasum": "1b5868bae2aa31c56f8947276f960ed149539d34", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "1b5868bae2aa31c56f8947276f960ed149539d34", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.4.3.tgz", "integrity": "sha512-hCYeMxKL0d9ng8Wa4PlrqqmeDdLrmeoD9xNLUXKiEaggTdorqGr7Hx8lcW0SYEL5n7jkVcgSjVcsDg0Ke5UTKg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCYDwsce/LT1nNMe75tIhKlZIghKxPDEg3gp4Jg6PEFlgIhAPXaYMd94wUkthb0mg6j4XWsUxGKhwkun3HBEC12au1J"}]}, "directories": {}}, "0.4.4": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "t3do<PERSON>@gmail.com"}], "version": "0.4.4", "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": [{"type": "MIT", "url": "https://github.com/Keyang/node-csvtojson/blob/master/LICENSE"}], "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.9.1", "grunt-contrib-watch": "^0.6.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "mocha": "^2.2.5"}, "dependencies": {"async": "^1.2.1", "underscore": "^1.8.3"}, "gitHead": "593c9bf5b5b424f8d36036f2bd28edc932804c21", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.4.4", "scripts": {}, "_shasum": "6d04186d32eed82dff64f2243c438b68c1f88f0d", "_from": ".", "_npmVersion": "2.9.1", "_nodeVersion": "0.12.3", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "6d04186d32eed82dff64f2243c438b68c1f88f0d", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.4.4.tgz", "integrity": "sha512-rRuuaOntBgHVcNHSlb982a/L5iwtOR7Yv4/v0/VbeeMSQ4LdPyxcYoL4QVbPMpQ5Y54ohTflDPghplo90g/Nkw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDO83Mvs6/2p47RNJBgB4XZB0/RjWKuo0Hjdx2h18XE3QIhAKzC7ElWuRijL5cNQyvuv5lVSG19UhOwaRDC+ajxto7N"}]}, "directories": {}}, "0.4.5": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "t3do<PERSON>@gmail.com"}], "version": "0.4.5", "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": [{"type": "MIT", "url": "https://github.com/Keyang/node-csvtojson/blob/master/LICENSE"}], "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.9.1", "grunt-contrib-watch": "^0.6.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "mocha": "^2.2.5"}, "dependencies": {"async": "^1.2.1", "underscore": "^1.8.3"}, "gitHead": "16ba2237e0bd96d6e3773e4c4d6e36c70efa620e", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.4.5", "scripts": {}, "_shasum": "9b211ee7460aefb4db349c00e13063f9a3e4f980", "_from": ".", "_npmVersion": "2.9.1", "_nodeVersion": "0.12.3", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "9b211ee7460aefb4db349c00e13063f9a3e4f980", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.4.5.tgz", "integrity": "sha512-iBMuY+f5NWcqcz7Bwon7KLILwo+dzNoRfHkOvKe+DStBe7zWxwhZUq5Z18QTKbcK/aJFz5Yim7sXZWX2i9L9eg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDSpAQMFMAgGZErlGis02gNeH7yALjoxtqw2dF1iUzOkgIhAMKrt0oMd1pP4r2oDO8hPYqnHvA+Em+bj7kSaoTWFcVm"}]}, "directories": {}}, "0.4.6": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "t3do<PERSON>@gmail.com"}], "version": "0.4.6", "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": [{"type": "MIT", "url": "https://github.com/Keyang/node-csvtojson/blob/master/LICENSE"}], "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.9.1", "grunt-contrib-watch": "^0.6.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "mocha": "^2.2.5"}, "dependencies": {"async": "^1.2.1"}, "gitHead": "e0961d4685884dbb4cecf18dca9d89cc1a2a8678", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.4.6", "scripts": {}, "_shasum": "790846cf19af9a6e52d5066a58338c3cc1a39e35", "_from": ".", "_npmVersion": "2.9.1", "_nodeVersion": "0.12.3", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "790846cf19af9a6e52d5066a58338c3cc1a39e35", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.4.6.tgz", "integrity": "sha512-vUkRrbVX3F4tvzRW9swpGy38n/tN/IGBobpKGBPZNwQzCmkxnAuDVTjtzvDfjSeKlujubnRzV+DQ0mRn5CJ01Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH+SVJ4Bd51Nkl/rFehavDriuVEG2SMzgRj/tM1rk3kSAiA0Y1cThjvECyLQBJSlzIDZJd+nXz1c7Wkcr99peJtTCw=="}]}, "directories": {}}, "0.4.7": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "t3do<PERSON>@gmail.com"}], "version": "0.4.7", "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": [{"type": "MIT", "url": "https://github.com/Keyang/node-csvtojson/blob/master/LICENSE"}], "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.9.1", "grunt-contrib-watch": "^0.6.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "mocha": "^2.2.5"}, "dependencies": {"async": "^1.2.1"}, "gitHead": "9bd6bd42b903f3df26d74e58bae135d6cd57452d", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.4.7", "scripts": {}, "_shasum": "91bca94fca74194ecc5c43c8025c23e922e72e5c", "_from": ".", "_npmVersion": "2.9.1", "_nodeVersion": "0.12.3", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "91bca94fca74194ecc5c43c8025c23e922e72e5c", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.4.7.tgz", "integrity": "sha512-aVeGur8H3AVA/H5fzxO0ieccjejul/apYAgUt8qxsD7I3octyRzQJO1n5GyKkWv/XG7/m7S7itN5qknPuUNpxQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCxwEycXHqP7m7nt+J14O5ARCEDcLz0QcI+9NDJtw5KWgIhAOxzGkPwpbJ0pzz3NB8zuGkINz2rhKXDFvIG34Nw5vbG"}]}, "directories": {}}, "0.4.8": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "t3do<PERSON>@gmail.com"}], "version": "0.4.8", "keywords": ["csv", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": [{"type": "MIT", "url": "https://github.com/Keyang/node-csvtojson/blob/master/LICENSE"}], "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.9.1", "grunt-contrib-watch": "^0.6.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "mocha": "^2.2.5"}, "dependencies": {"async": "^1.2.1"}, "gitHead": "9aecae91fd60104a5959cac31c5665f213a634d7", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.4.8", "scripts": {}, "_shasum": "3a83a89425fd63fed68a7bf5f7913afea9c10667", "_from": ".", "_npmVersion": "2.9.1", "_nodeVersion": "0.12.3", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "3a83a89425fd63fed68a7bf5f7913afea9c10667", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.4.8.tgz", "integrity": "sha512-f36n7rt34/NTP23R1P10WWrDuP9qHhNqPeWIkFpPLKOsrNeFvhvHbg0uU8TyTBHeT5ZDyx5Gl7Zq8jENzehvVA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAMEzHVGXebzDWp2r9cbTFdqaD25yD//F6d8fqeSGyLNAiAIJKS517tJXaJn9uIV1//FUhg6Xjy8bK+9GhYk0xKT2w=="}]}, "directories": {}}, "0.5.0": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "t3do<PERSON>@gmail.com"}], "version": "0.5.0", "keywords": ["csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": [{"type": "MIT", "url": "https://github.com/Keyang/node-csvtojson/blob/master/LICENSE"}], "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.9.1", "grunt-contrib-watch": "^0.6.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "mocha": "^2.2.5"}, "dependencies": {"async": "^1.2.1"}, "gitHead": "971dc33cccd79682d8d78b7235d2776bec8dbf36", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.5.0", "scripts": {}, "_shasum": "28481a9ece423bdb9f4e8fde8e058fd6f85b3ae8", "_from": ".", "_npmVersion": "2.9.1", "_nodeVersion": "0.12.3", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "28481a9ece423bdb9f4e8fde8e058fd6f85b3ae8", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.5.0.tgz", "integrity": "sha512-/7HguqNBqhGnp8NZfea6Ob+yogsr3RAxpuRh+LYSPk2Yc0ud5WdDGKyI5MDndIgOqELc7yrmfo528+7JHxFVvg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCxrKM8iqj1NJbHubuidBlmnjQzalR8PCDIy8xmaQcdFQIgJF630JccD8o8X2o5yZSkX3HkLVYctpgrrzI/jZthW1o="}]}, "directories": {}}, "0.5.1": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "t3do<PERSON>@gmail.com"}], "version": "0.5.1", "keywords": ["csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": [{"type": "MIT", "url": "https://github.com/Keyang/node-csvtojson/blob/master/LICENSE"}], "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.9.1", "grunt-contrib-watch": "^0.6.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "mocha": "^2.2.5"}, "dependencies": {"async": "^1.2.1"}, "gitHead": "c44a39c8236e310dbf0acf4c1b8f4efdd31d0147", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.5.1", "scripts": {}, "_shasum": "2b03354e7049c80026d1559b7ac3bc9f19dedd3c", "_from": ".", "_npmVersion": "2.9.1", "_nodeVersion": "0.12.3", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "2b03354e7049c80026d1559b7ac3bc9f19dedd3c", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.5.1.tgz", "integrity": "sha512-ZKM2DCLpby4z1wTR7RCvvjMRP6udxRkgW7/sc57kv/AuvPeUJ3c3sY7+rQrPJSTZDZbDJyYmOmq75lrFcEej9g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC+bUwjRcZdrRlmqhpoOQu8D5wjTeRf2jOi6l3wVwTQ+QIgBzacQBpG48IFSSA5+XSBT3ClRoHBU+gyOKtY32ypjYs="}]}, "directories": {}}, "0.5.2": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "t3do<PERSON>@gmail.com"}], "version": "0.5.2", "keywords": ["csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "convert", "parser", "exntendible", "plugin"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": [{"type": "MIT", "url": "https://github.com/Keyang/node-csvtojson/blob/master/LICENSE"}], "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-browserify": "^4.0.1", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.11.0", "grunt-contrib-watch": "^0.6.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "load-grunt-tasks": "^3.4.0", "mocha": "^2.2.5"}, "dependencies": {"async": "^1.2.1"}, "gitHead": "d0a0e665103609df923dbcc8470b44a834ba666e", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.5.2", "scripts": {}, "_shasum": "9c7035adcaf00430d641f5a3b4f68df74a11a216", "_from": ".", "_npmVersion": "2.9.1", "_nodeVersion": "0.12.3", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "9c7035adcaf00430d641f5a3b4f68df74a11a216", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.5.2.tgz", "integrity": "sha512-A8Xg8nMIk3bEDm19ewS3VHsCy6oGiFSeMnRc7X1r+kqk2rwCzJA6wXWJiXgYr/Dh3IvmPlP2fUS8fL30pdePXg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAxnj3MIog5GCj5hQ2RX9dHK47F8QTPdCopdTtlzbUqvAiEAo1QDBVh4rQg9QZXUX0MHksflrF1M2LJ15rzAenkdPXQ="}]}, "_npmOperationalInternal": {"host": "packages-9-west.internal.npmjs.com", "tmp": "tmp/csvtojson-0.5.2.tgz_1454364810816_0.9924596352502704"}, "directories": {}}, "0.5.3": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "t3do<PERSON>@gmail.com"}], "version": "0.5.3", "keywords": ["csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "csv to json", "csv convert", "<PERSON><PERSON><PERSON>", "convert csv to json", "csv-json"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": [{"type": "MIT", "url": "https://github.com/Keyang/node-csvtojson/blob/master/LICENSE"}], "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-browserify": "^4.0.1", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.11.0", "grunt-contrib-watch": "^0.6.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "load-grunt-tasks": "^3.4.0", "mocha": "^2.2.5"}, "dependencies": {"async": "^1.2.1"}, "gitHead": "d0a0e665103609df923dbcc8470b44a834ba666e", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.5.3", "scripts": {}, "_shasum": "9dbe140c1274e15e18170d21e6fa107bf7d60eca", "_from": ".", "_npmVersion": "2.9.1", "_nodeVersion": "0.12.3", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "9dbe140c1274e15e18170d21e6fa107bf7d60eca", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.5.3.tgz", "integrity": "sha512-2IwecwklUvnEwl1hS+Ftg0J5kbp4SXPe8uMtDAaW7wih+PsB5WpcPltdMFajh/yneF/S3RorcnIOZsG07Wg/pg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA2z+jrYTjni2BQQ5jvbU8F2s5yF6c37Wxgwj+0ifYmPAiEA/DbGgcm215qmuJ1HdQjRt7MRf2qHa4oq2hUqAidcoqI="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/csvtojson-0.5.3.tgz_1459281718894_0.3823724954854697"}, "directories": {}}, "0.5.4": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "t3do<PERSON>@gmail.com"}], "version": "0.5.4", "keywords": ["csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "csv to json", "csv convert", "<PERSON><PERSON><PERSON>", "convert csv to json", "csv-json"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": [{"type": "MIT", "url": "https://github.com/Keyang/node-csvtojson/blob/master/LICENSE"}], "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-browserify": "^4.0.1", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.11.0", "grunt-contrib-watch": "^0.6.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "load-grunt-tasks": "^3.4.0", "mocha": "^2.2.5"}, "dependencies": {"async": "^1.2.1", "csv-string": "^2.3.0"}, "gitHead": "4c44166caa37a3280c962ff8a21c96e2f66a53f9", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.5.4", "scripts": {}, "_shasum": "2609fd4024f8d8b4a7cbe0382d671bd638010a6c", "_from": ".", "_npmVersion": "2.9.1", "_nodeVersion": "0.12.3", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "2609fd4024f8d8b4a7cbe0382d671bd638010a6c", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.5.4.tgz", "integrity": "sha512-rJi9uGUNQrID2hH5Kuv9eZGYgZmsvPqoj98h2daXFV6y+2lcyZW7VCgcUx/WjPDpj1r3v//dVmimS5n0F3bsHA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDoivoG7XApyL7U+corW74VFx0NOWFQPHBE2OY9o6A7hAiEA97dn41iakwcdt4ptrLZsHof/TiFGyP0+yNCsA1bwapI="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/csvtojson-0.5.4.tgz_1461975652058_0.18837703624740243"}, "directories": {}}, "0.5.5": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "t3do<PERSON>@gmail.com"}], "version": "0.5.5", "keywords": ["csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "csv to json", "csv convert", "<PERSON><PERSON><PERSON>", "convert csv to json", "csv-json"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": [{"type": "MIT", "url": "https://github.com/Keyang/node-csvtojson/blob/master/LICENSE"}], "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-browserify": "^4.0.1", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.11.0", "grunt-contrib-watch": "^0.6.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "load-grunt-tasks": "^3.4.0", "mocha": "^2.2.5"}, "dependencies": {"async": "^1.2.1"}, "gitHead": "a6fea750de773d334f7ce1cd7c650290071f7634", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.5.5", "scripts": {}, "_shasum": "f867c702ba9860d99b640f6c914725b84a4db3d4", "_from": ".", "_npmVersion": "2.9.1", "_nodeVersion": "0.12.3", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "f867c702ba9860d99b640f6c914725b84a4db3d4", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.5.5.tgz", "integrity": "sha512-rqJJFF8WRxdm8kgAl895n4NEkLqEVwvgIkbxFqslLpX3udhdLKpZ3CWPndBBv01+TuWayg+O//c2fSCu3PnhOw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDRE++8eA4cpAyuSGd5xK6qHGnsPRVbOaGVy78OBRmkdwIgIN89OUWaPrhmR7XARad5Q+PdMqTY1zqOaFB8+i36iPM="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/csvtojson-0.5.5.tgz_1462211157684_0.6548055647872388"}, "directories": {}}, "0.5.6": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "git+https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "t3do<PERSON>@gmail.com"}], "version": "0.5.6", "keywords": ["csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "csv to json", "csv convert", "<PERSON><PERSON><PERSON>", "convert csv to json", "csv-json"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": "MIT", "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-browserify": "^4.0.1", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.11.0", "grunt-contrib-watch": "^0.6.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "load-grunt-tasks": "^3.4.0", "mocha": "^2.2.5"}, "dependencies": {"async": "^1.2.1"}, "gitHead": "5ba278870e87c71c6f3e7a8bc441b83856692579", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.5.6", "scripts": {}, "_shasum": "39dd9afd74cdc9c54f9ce4e0b5efaaac745c2b34", "_from": ".", "_npmVersion": "3.8.8", "_nodeVersion": "0.12.3", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "39dd9afd74cdc9c54f9ce4e0b5efaaac745c2b34", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.5.6.tgz", "integrity": "sha512-QZkfjWedt1ac7iqPxVByDHHhGpQYwgN7wCEl1viwJy16a6Icqjt7x003sPLE+xaSvvCkWLPnBZfVI/wDa+6FLg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDHvpZmhvzE8hV2+VMVIx9y8Ogj/Lf7954U1g1tom5CpAIgK25nDLPVio+c83x9U2uZAy5pRy/G51YGz1CRdN7VkFE="}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/csvto<PERSON>son-0.5.6.tgz_1462482499541_0.5136111513711512"}, "directories": {}}, "0.5.7": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "git+https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "t3do<PERSON>@gmail.com"}], "version": "0.5.7", "keywords": ["csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "csv to json", "csv convert", "<PERSON><PERSON><PERSON>", "convert csv to json", "csv-json"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": "MIT", "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-browserify": "^4.0.1", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.11.0", "grunt-contrib-watch": "^0.6.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "load-grunt-tasks": "^3.4.0", "mocha": "^2.2.5"}, "dependencies": {"async": "^1.2.1"}, "gitHead": "6b2df10a14fd5a157f4889efee6294aa2753f6ec", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.5.7", "scripts": {}, "_shasum": "d0f0e7bc725e3b5e2f1e9c58bc0394e9e7c7b28c", "_from": ".", "_npmVersion": "3.8.8", "_nodeVersion": "0.12.3", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "d0f0e7bc725e3b5e2f1e9c58bc0394e9e7c7b28c", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.5.7.tgz", "integrity": "sha512-OBnEkmovsbR5EWwyxdN+lf6TYUJ0W5pPHWi5innNDKV3STJnFM/kYprn5iPUxcktMA0Bt3v/qKHax0pCwAqHZA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDvLntfADZaX3o6DkgyEBtF7Bz7Q/QPbHfuahEoTWBvRwIhALrapabefgPnbJUoR5TAuk6VwcgYNJe+x+l97lvd6V8E"}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/csvtojson-0.5.7.tgz_1462774027791_0.6950585811864585"}, "directories": {}}, "0.5.8": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "git+https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "t3do<PERSON>@gmail.com"}], "version": "0.5.8", "keywords": ["csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "csv to json", "csv convert", "<PERSON><PERSON><PERSON>", "convert csv to json", "csv-json"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": "MIT", "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-browserify": "^4.0.1", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.11.0", "grunt-contrib-watch": "^0.6.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "load-grunt-tasks": "^3.4.0", "mocha": "^2.2.5"}, "dependencies": {"async": "^1.2.1"}, "gitHead": "1f6f9683875cfd854c1179fe9373b792e4969bed", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.5.8", "scripts": {}, "_shasum": "3ad30e808eb5b0f5be1f016e375d28d980259d4a", "_from": ".", "_npmVersion": "3.8.8", "_nodeVersion": "0.12.3", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "3ad30e808eb5b0f5be1f016e375d28d980259d4a", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.5.8.tgz", "integrity": "sha512-P<PERSON>SR5rR4KxUxQB5wVE2WGCXDxgBwBp0+QMb3cqpa19a6lCdEiUJfboP6KseswcB/EOA8hoXs/j0Cz3cKFhM0Zg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCX77b4K9z15uEdQQZWcACsGORGeoR7saofyQfXL58azgIgGvsThBpzUlv3wfeRFqOdzWc+4ZtVpq+0lRRW5yXx4AU="}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/csvtojson-0.5.8.tgz_1462795210155_0.5157540368381888"}, "directories": {}}, "0.5.9": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "git+https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "t3do<PERSON>@gmail.com"}], "version": "0.5.9", "keywords": ["csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "csv to json", "csv convert", "<PERSON><PERSON><PERSON>", "convert csv to json", "csv-json"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": "MIT", "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-browserify": "^4.0.1", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.11.0", "grunt-contrib-watch": "^0.6.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "load-grunt-tasks": "^3.4.0", "mocha": "^2.2.5"}, "dependencies": {"async": "^1.2.1"}, "gitHead": "bc4a38927065ee51707f704475d14bb6109c8b9f", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.5.9", "scripts": {}, "_shasum": "b02797fc4c2d68bd7b2ce870253cc8577a72a5d8", "_from": ".", "_npmVersion": "3.8.8", "_nodeVersion": "0.12.3", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "b02797fc4c2d68bd7b2ce870253cc8577a72a5d8", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.5.9.tgz", "integrity": "sha512-N02g4cIuKi0foXaXCu3f88hSpmOILf8+wrFFtG8sd5ByjxG9/RO3+SGvV5VZVVgVyUrS8+bAIYDe0G9STSj3jg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC+3A2yeHKcjR6iofifHsOOzollMQtYHHYI/fgnfOEocAIhAJIBAlEAHvg0+8wA6HB7a/wk2V3GSU2/i8M7pKZRCIpD"}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/csvtojson-0.5.9.tgz_1462835386646_0.119871461763978"}, "directories": {}}, "0.5.10": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "git+https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "t3do<PERSON>@gmail.com"}], "version": "0.5.10", "keywords": ["csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "csv to json", "csv convert", "<PERSON><PERSON><PERSON>", "convert csv to json", "csv-json"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": "MIT", "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-browserify": "^4.0.1", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.11.0", "grunt-contrib-watch": "^0.6.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "load-grunt-tasks": "^3.4.0", "mocha": "^2.4.5"}, "dependencies": {"async": "^1.2.1"}, "scripts": {"test": "mocha ./test -R spec"}, "gitHead": "fa5987b564ba6759a9dc44ec6c4e6be1a5fa3f7d", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.5.10", "_shasum": "ec19325f7234401d75bd55dc63a4907dcfeef2a0", "_from": ".", "_npmVersion": "3.8.8", "_nodeVersion": "0.12.3", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "ec19325f7234401d75bd55dc63a4907dcfeef2a0", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.5.10.tgz", "integrity": "sha512-d7Fr5JMMkQM4e3sVhR0SBsdFKQFY7j3zNm45zzaSBZzezygu+ifEkVMefTowBSm9rHr4ZnxjoYXLAnZS71i4Dw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDzdpyX9ZJUK8rbiUgwhIxZzuiTpwSJKy/JLl+ay3QAXgIhALdle+eSv0u9ynVgNJ+HsvAEWHBgHnNMqL5DxFbatLvN"}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/csvtojson-0.5.10.tgz_1462873406008_0.21225596964359283"}, "directories": {}}, "0.5.11": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "git+https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "t3do<PERSON>@gmail.com"}], "version": "0.5.11", "keywords": ["csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "csv to json", "csv convert", "<PERSON><PERSON><PERSON>", "convert csv to json", "csv-json"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": "MIT", "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-browserify": "^4.0.1", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.11.0", "grunt-contrib-watch": "^0.6.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "load-grunt-tasks": "^3.4.0", "mocha": "^2.4.5"}, "dependencies": {"async": "^1.2.1"}, "scripts": {"test": "mocha ./test -R spec"}, "gitHead": "e96aa3bbdbe519c43a3d1bca1edce84d1e52d312", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.5.11", "_shasum": "e6f8bb83b9b896166052f4a853cf4831e4896017", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "6.0.0", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "e6f8bb83b9b896166052f4a853cf4831e4896017", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.5.11.tgz", "integrity": "sha512-5dmS9XalGv+XFPuqzHVkpc9ETnY5k552YbOd0mA2F9IB4uSzq+bP5bxjFEvxUn6f5D0AUsxR0ZOAGcY+6W2/CQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDaMp3wn/6mdJqxwnVD1cphb3f28SJdhQo4apJ7SxeYYQIgNOI6VpdzGbOaOdqJfWD1J67g3r+8pwbSVvjgqTd4MT8="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/csvtojson-0.5.11.tgz_1463380452261_0.6874866266734898"}, "directories": {}}, "0.5.12": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "git+https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "t3do<PERSON>@gmail.com"}], "version": "0.5.12", "keywords": ["csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "csv to json", "csv convert", "<PERSON><PERSON><PERSON>", "convert csv to json", "csv-json"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": "MIT", "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-browserify": "^4.0.1", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.11.0", "grunt-contrib-watch": "^0.6.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "load-grunt-tasks": "^3.4.0", "mocha": "^2.4.5"}, "dependencies": {"async": "^1.2.1"}, "scripts": {"test": "mocha ./test -R spec"}, "gitHead": "af30fbe2fbcb6d95d66d50af226a1d46d9d6755d", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.5.12", "_shasum": "32cbbfe8faa4c78ba1fbc21a54a9dfaa2f9552ad", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "6.0.0", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "32cbbfe8faa4c78ba1fbc21a54a9dfaa2f9552ad", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.5.12.tgz", "integrity": "sha512-CJ0GSVTgvHQmV2QYEyiIDW5EKKtvAlA1wmMURYKAz70dECV0lHPAtexUm9cZnYzwwO4fPEFQWU7LBwRbhQeqQQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFBkJ3deeHqnTZGKJfX5ayUmqKxFKnDxHAiHtAxMhMqRAiEAn86RZaacu7EukovrVL0fMpR2aAPUBk6xC68Xw43iAxk="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/csvtojson-0.5.12.tgz_1463561176317_0.7109342887997627"}, "directories": {}}, "0.5.13": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "t3do<PERSON>@gmail.com"}], "version": "0.5.13", "keywords": ["csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "csv to json", "csv convert", "<PERSON><PERSON><PERSON>", "convert csv to json", "csv-json"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": "MIT", "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-browserify": "^4.0.1", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.11.0", "grunt-contrib-watch": "^0.6.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "load-grunt-tasks": "^3.4.0", "mocha": "^2.4.5"}, "dependencies": {"async": "^1.2.1", "minimist": "^1.2.0"}, "scripts": {"test": "mocha ./test -R spec"}, "gitHead": "5b9f35e75439e1872006a5245bb6764d19b471c8", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.5.13", "_shasum": "1530cbaeaaea30a56d56b162d7f933a65b8dcd2a", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "1530cbaeaaea30a56d56b162d7f933a65b8dcd2a", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.5.13.tgz", "integrity": "sha512-lpMCDfjst1pO/IrPWS9FPrtqDAgJOijjRWsx5HcoqwGcx2JV+cdjO87oIdU0z8kXXSjqzR1CTyGfqovZBNxP/w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA7GwWl/niHadEYOipslXDmbyMI06aWlphqbM1zLc6uvAiAR7FFRbJIqvvo5lpfVxu4V0Fk85KgjXxTrh4EovfBkUg=="}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/csvtojson-0.5.13.tgz_1464884117270_0.09195693279616535"}, "directories": {}}, "0.5.14": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "t3do<PERSON>@gmail.com"}], "version": "0.5.14", "keywords": ["csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "csv to json", "csv convert", "<PERSON><PERSON><PERSON>", "convert csv to json", "csv-json"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": "MIT", "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-browserify": "^4.0.1", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.11.0", "grunt-contrib-watch": "^0.6.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "load-grunt-tasks": "^3.4.0", "mocha": "^2.4.5"}, "dependencies": {"async": "^1.2.1", "minimist": "^1.2.0"}, "scripts": {"test": "mocha ./test -R spec"}, "gitHead": "4dbe119fb801675ae0192383c5d549c1fae3a293", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@0.5.14", "_shasum": "0f00ff128580c3a95b7d0227fee3dd962ee61f1f", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "0f00ff128580c3a95b7d0227fee3dd962ee61f1f", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-0.5.14.tgz", "integrity": "sha512-CLr5VXot4caVjOm9CSCk33K5Ll1B6HYUmNzQO0HbgasB0duB28PMHGxe/CMrnPbWpXeMDHbAXJ1n55ZSJ73wgA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCFYEPfZFL4c4q/paMDs2NJTbiR2lGugzu6Ubacy7w+MQIgFCThwE6szOzhdDrCixJ37ThQyPYXnG1MeCsRPWxUQz8="}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/csvtojson-0.5.14.tgz_1464885045218_0.6934054642915726"}, "directories": {}}, "1.0.0": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "t3do<PERSON>@gmail.com"}], "version": "1.0.0", "keywords": ["csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "csv to json", "csv convert", "<PERSON><PERSON><PERSON>", "convert csv to json", "csv-json"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": "MIT", "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-browserify": "^4.0.1", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.11.0", "grunt-contrib-watch": "^0.6.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "load-grunt-tasks": "^3.4.0", "mocha": "^2.4.5"}, "dependencies": {"async": "^1.2.1", "minimist": "^1.2.0"}, "scripts": {"test": "mocha ./test -R spec"}, "gitHead": "ef17f9b979bed4cea21ea3164094ae7f48dfa41d", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@1.0.0", "_shasum": "bc97dd312ce07fbdcd07a2fafb1256a5bd93b576", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "bc97dd312ce07fbdcd07a2fafb1256a5bd93b576", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-1.0.0.tgz", "integrity": "sha512-K3yAqdZM+GTd2bxl/zet8f7YO9sfnVWWA8KiOC7zC8jrwne5HZczPkVj1ZMcZ1QddqpNL7uYUwUVJiosw0WrWw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCUVq8y/RWHl2Y4IxUZ2uF8ibB1cl2mOFGWeSZB18r8ZwIgNM4EbEb+OC1YkH4dPmejBndEP50+/nksGsJOMCSlzqU="}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/csvtojson-1.0.0.tgz_1466688921583_0.1949790909420699"}, "directories": {}}, "1.0.1": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "t3do<PERSON>@gmail.com"}], "version": "1.0.1", "keywords": ["csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "csv to json", "csv convert", "<PERSON><PERSON><PERSON>", "convert csv to json", "csv-json"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": "MIT", "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-browserify": "^4.0.1", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.11.0", "grunt-contrib-watch": "^0.6.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "load-grunt-tasks": "^3.4.0", "mocha": "^2.4.5"}, "dependencies": {"async": "^1.2.1", "minimist": "^1.2.0"}, "scripts": {"test": "mocha ./test -R spec"}, "gitHead": "af7eda540a2ca9db569d3498943a43be54b93ca4", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@1.0.1", "_shasum": "7dd865c9537774aa39fa55cdaca22b775b3edc57", "_from": ".", "_npmVersion": "3.9.3", "_nodeVersion": "6.2.1", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "7dd865c9537774aa39fa55cdaca22b775b3edc57", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-1.0.1.tgz", "integrity": "sha512-HhTE18vbu5J3yRXGW6xKH45wLEqZaLY8w5jTI+Ay1kk6e98CdeFZnV9EfWaW2iQRpv8v3tOzSynydF1p17GaiQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICIqDYDjsLWKHIOdZMHYfTiG1MkpsHb5VwO5UojO/mlyAiBRdMicWJvlwonk2+xxxkweAhaAqcMDEmMOu0nWJwwaVQ=="}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/csvtojson-1.0.1.tgz_1473606322118_0.8112658211030066"}, "directories": {}}, "1.0.2": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "t3do<PERSON>@gmail.com"}], "version": "1.0.2", "keywords": ["csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "csv to json", "csv convert", "<PERSON><PERSON><PERSON>", "convert csv to json", "csv-json"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": "MIT", "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-browserify": "^4.0.1", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.11.0", "grunt-contrib-watch": "^0.6.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "load-grunt-tasks": "^3.4.0", "mocha": "^2.4.5"}, "dependencies": {"async": "^1.2.1", "minimist": "^1.2.0"}, "scripts": {"test": "mocha ./test -R spec"}, "gitHead": "459f64604f876668a19dbdd6394207b7a895d516", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@1.0.2", "_shasum": "0af9ead8f5c2880b032d9cd5a3fd46d396ab69bf", "_from": ".", "_npmVersion": "2.9.1", "_nodeVersion": "0.12.3", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "0af9ead8f5c2880b032d9cd5a3fd46d396ab69bf", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-1.0.2.tgz", "integrity": "sha512-ocYuwcmhKz9VcEEPjxyA9fbxpsua+JlNiOBAO5MXmcJH5ocvyzVE2E+IB4iie+8OOk3Bf8vGxu5aK7bLRCTzgw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDzQRy1KqnDmH48546P2ya7Z0jxvMy/bn1BlwFZGXs9vAIhAPWWwLL5gy3+eQP2tewyqPb11WAgZzpk8XQDgKzu83kh"}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/csvtojson-1.0.2.tgz_1473668178552_0.8281425153836608"}, "directories": {}}, "1.0.3": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "t3do<PERSON>@gmail.com"}], "version": "1.0.3", "keywords": ["csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "csv to json", "csv convert", "<PERSON><PERSON><PERSON>", "convert csv to json", "csv-json"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": "MIT", "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-browserify": "^4.0.1", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.11.0", "grunt-contrib-watch": "^0.6.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "load-grunt-tasks": "^3.4.0", "mocha": "^2.4.5"}, "dependencies": {"async": "^1.2.1", "minimist": "^1.2.0"}, "scripts": {"test": "mocha ./test -R spec"}, "gitHead": "13133534df956f48f67f50aaf7d85da934adedd5", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@1.0.3", "_shasum": "a11a5894ac41bfacff99accc4aee7a0b8fce8f9f", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.3", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "a11a5894ac41bfacff99accc4aee7a0b8fce8f9f", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-1.0.3.tgz", "integrity": "sha512-qtq8G+PgX8WeU+m7n5uo2IRY/KeBFvrUzT7PPdRw70ieQkWd06UvbUDDueS/4TH3ZMWufp3KVC7LJYV3V3S7FQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD0XpwUBa9DzFA4dufspvp5lGAM5M/NBj1dmJ33LM4lwAIhANpnPNz81YarWvDkCY4Q/OisQX/Bb/vWXXAdl2Cf/rLQ"}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/csvtojson-1.0.3.tgz_1475841081554_0.6829365522135049"}, "directories": {}}, "1.1.0": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "t3do<PERSON>@gmail.com"}], "version": "1.1.0", "keywords": ["csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "csv to json", "csv convert", "<PERSON><PERSON><PERSON>", "convert csv to json", "csv-json"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": "MIT", "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-browserify": "^4.0.1", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.11.0", "grunt-contrib-watch": "^0.6.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "load-grunt-tasks": "^3.4.0", "mocha": "^2.4.5"}, "dependencies": {"minimist": "^1.2.0"}, "scripts": {"test": "mocha ./test -R spec && CSV_WORKER=3 mocha ./test -R spec", "test-debug": "mocha debug ./test -R spec"}, "gitHead": "9f8b5c640fce3bb2dbb479ac25bf873dc788276a", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@1.1.0", "_shasum": "d7a5eba20cdf869abe3204ae2db2cfe5a0e5646e", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "d7a5eba20cdf869abe3204ae2db2cfe5a0e5646e", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-1.1.0.tgz", "integrity": "sha512-SywD3lSCSc559pLLuk1+OKZPiWyyZtDxI1IbSnBizmFIRc2U9+jnb4UCns+6K2cwt/X+NoznTr2nYY03OA+CbA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDh4FzMkIYN13Vxq29VNcwlEUNThXBgNny4vp5DaQuWXAIhAM3Wi4IZSd4Dut00izseE8D+sKRb84dlUfQt+NjpC4+0"}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/csvtojson-1.1.0.tgz_1482615214821_0.4618251761421561"}, "directories": {}}, "1.1.1": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "t3do<PERSON>@gmail.com"}], "version": "1.1.1", "keywords": ["csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "csv to json", "csv convert", "<PERSON><PERSON><PERSON>", "convert csv to json", "csv-json"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": "MIT", "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-browserify": "^4.0.1", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.11.0", "grunt-contrib-watch": "^0.6.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "load-grunt-tasks": "^3.4.0", "mocha": "^2.4.5", "minimist": "^1.2.0"}, "dependencies": {"lodash": "^4.17.3"}, "scripts": {"test": "mocha ./test -R spec", "test-debug": "mocha debug ./test -R spec", "test-all": "mocha  ./test -R spec && CSV_WORKER=3 mocha ./test -R spec "}, "gitHead": "e3801c2046d8253ec91b51170b9f26b78f62f6a1", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@1.1.1", "_shasum": "3bf600f6346c9190d1b0a415e50315821ff9c8c6", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "3bf600f6346c9190d1b0a415e50315821ff9c8c6", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-1.1.1.tgz", "integrity": "sha512-53fI84wVH9Wt2soYbM221xS27riuX3ZwOrpWv4A+7uTGmwUqe/A9vxAL7a5+v8LKPhr+lszk4Ec8Ioz4JrJNfg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDX1gjPw6DGczfMXn/inXy13JVsekRLE3SJh/uHTeYABwIhAO453RYIPR4uPFKss419eAjibQxuhD/KFOIsT1mWFvl1"}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/csvtojson-1.1.1.tgz_1483125842847_0.4444062823895365"}, "directories": {}}, "1.1.2": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "t3do<PERSON>@gmail.com"}], "version": "1.1.2", "keywords": ["csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "csv to json", "csv convert", "<PERSON><PERSON><PERSON>", "convert csv to json", "csv-json"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": "MIT", "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-browserify": "^4.0.1", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.11.0", "grunt-contrib-watch": "^0.6.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "load-grunt-tasks": "^3.4.0", "mocha": "^2.4.5", "minimist": "^1.2.0"}, "dependencies": {"lodash": "^4.17.3"}, "scripts": {"test": "mocha ./test -R spec", "test-debug": "mocha debug ./test -R spec", "test-all": "mocha  ./test -R spec && CSV_WORKER=3 mocha ./test -R spec "}, "gitHead": "12ea0ad69bb89b328b52e2f85f1a1f1ddbb6638b", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@1.1.2", "_shasum": "76de49935334be5f1961783e39d3f423fc730afc", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.3.2", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "76de49935334be5f1961783e39d3f423fc730afc", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-1.1.2.tgz", "integrity": "sha512-QBz0iFUovTm3xLazGMyibE3fGotWT07hfC6NFupTt66K2PbJEa8c+sWr50As+OAU7DH7jQCsdrDNzW8Auw1b2w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCXvtnBtbUSaZ1JLyhdO2N+jBE7/gIDMVFe92xKZdcYTgIhAPQnYW0u4mvrcLKJs3HmbB1axKlE2YXDkAHQSPHJbeBn"}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/csvtojson-1.1.2.tgz_1483657411222_0.14609552919864655"}, "directories": {}}, "1.1.3": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "t3do<PERSON>@gmail.com"}], "version": "1.1.3", "keywords": ["csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "csv to json", "csv convert", "<PERSON><PERSON><PERSON>", "convert csv to json", "csv-json"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": "MIT", "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-browserify": "^4.0.1", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.11.0", "grunt-contrib-watch": "^0.6.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "load-grunt-tasks": "^3.4.0", "mocha": "^2.4.5", "minimist": "^1.2.0"}, "dependencies": {"lodash": "^4.17.3"}, "scripts": {"test": "mocha ./test -R spec", "test-debug": "mocha debug ./test -R spec", "test-all": "mocha  ./test -R spec && CSV_WORKER=3 mocha ./test -R spec "}, "gitHead": "263353a611104299f633271ed8b7285607b808b0", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@1.1.3", "_shasum": "c6ab434d127475728e2bc97d7c4d037b3494a328", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "c6ab434d127475728e2bc97d7c4d037b3494a328", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-1.1.3.tgz", "integrity": "sha512-9NlP0sQEh2X2xJNfHk6j/fz88aYQrxhqCWlRxniXeKavMpdhh9uyUVLCIBKaOFz4kjhObeX+W6elQw/CUjKHCQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEeUkaf8fVAsb4CBtgnsrSJnqyFxTuCBBHKc9BzhancPAiBS8rqkykFIUko5CP0blJdl6dL4DauhioRMyqZsEAPxsA=="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/csvtojson-1.1.3.tgz_1485207347947_0.5914052443113178"}, "directories": {}}, "1.1.4": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "http://keyangxiang.com/blog/csv2json/", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "git+https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dcohenb"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/trangtungn"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/atufkas"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/fenichelar"}, {"name": "<PERSON>", "url": "https://github.com/blakeblackshear"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/roodboi"}, {"url": "https://github.com/markwithers"}, {"name": "<PERSON>", "url": "https://github.com/colarob"}, {"name": "<PERSON>", "url": "https://github.com/jessicagood"}, {"url": "https://github.com/jondayft"}, {"name": "<PERSON>", "url": "https://github.com/thegreatsunra"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Jimi<PERSON>ord"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Off76"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/Keyang"}, {"name": "Ionică Bizău", "email": "<EMAIL>", "url": "https://github.com/IonicaBizau"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/slang800"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/t3dodson"}, {"name": "<PERSON>", "url": "https://github.com/jef<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/amilajack"}, {"name": "Zsolt R<PERSON>", "url": "https://github.com/molnarzs"}], "version": "1.1.4", "keywords": ["csv", "csv parser", "parse csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "csv to json", "csv convert", "<PERSON><PERSON><PERSON>", "convert csv to json", "csv-json"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": "MIT", "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-browserify": "^4.0.1", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.11.0", "grunt-contrib-watch": "^0.6.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "load-grunt-tasks": "^3.4.0", "mocha": "^2.4.5", "minimist": "^1.2.0"}, "dependencies": {"lodash": "^4.17.3"}, "scripts": {"test": "mocha ./test -R spec", "test-debug": "mocha debug ./test -R spec", "test-all": "mocha  ./test -R spec && CSV_WORKER=3 mocha ./test -R spec "}, "gitHead": "590d5580348ede58e916277d8167f3f6e20f4219", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@1.1.4", "_shasum": "8deafcd6e78dac01812a34ed022670e16d402fe7", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "8deafcd6e78dac01812a34ed022670e16d402fe7", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-1.1.4.tgz", "integrity": "sha512-fFe9/sEJWyDZNfkQiKtq5E5BgC6d2A5EgZh2uvMlNl10ptkrUrf/3hPhuLSb6WjQdRh4VwRqmUYw4rY3dVGOoQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD8rovyV40EzFqXU41eu1OV/w7bBmai5H1jCZGiaccD9AIhAN0kkZi1i+CorLrwf1HDB4HuT6h0wgIy8bVnc8RLX036"}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/csvtojson-1.1.4.tgz_1486145326766_0.2835744589101523"}, "directories": {}}, "1.1.5": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Keyang/node-csvtojson", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dcohenb"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/trangtungn"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/atufkas"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/fenichelar"}, {"name": "<PERSON>", "url": "https://github.com/blakeblackshear"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/roodboi"}, {"url": "https://github.com/markwithers"}, {"name": "<PERSON>", "url": "https://github.com/colarob"}, {"name": "<PERSON>", "url": "https://github.com/jessicagood"}, {"url": "https://github.com/jondayft"}, {"name": "<PERSON>", "url": "https://github.com/thegreatsunra"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Jimi<PERSON>ord"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Off76"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/Keyang"}, {"name": "Ionică Bizău", "email": "<EMAIL>", "url": "https://github.com/IonicaBizau"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/slang800"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/t3dodson"}, {"name": "<PERSON>", "url": "https://github.com/jef<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/amilajack"}, {"name": "Zsolt R<PERSON>", "url": "https://github.com/molnarzs"}], "version": "1.1.5", "keywords": ["csv", "csv parser", "parse csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "csv to json", "csv convert", "<PERSON><PERSON><PERSON>", "convert csv to json", "csv-json"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": "MIT", "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-browserify": "^4.0.1", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.11.0", "grunt-contrib-watch": "^0.6.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "load-grunt-tasks": "^3.4.0", "mocha": "^2.4.5", "minimist": "^1.2.0"}, "dependencies": {"lodash": "^4.17.3"}, "scripts": {"test": "mocha ./test -R spec", "test-debug": "mocha debug ./test -R spec", "test-all": "mocha  ./test -R spec && CSV_WORKER=3 mocha ./test -R spec "}, "gitHead": "28bdd726d5d04bd7f3823e04c4698e53f2e26d76", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@1.1.5", "_shasum": "c0b7d34dd3b5e098dd97d9686de659d0b9a98254", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "c0b7d34dd3b5e098dd97d9686de659d0b9a98254", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-1.1.5.tgz", "integrity": "sha512-/HA9UWN9OrNJMc2G7j4dVEKkoSIQmkSTWuD6d5ZwXNEOvfdkK9+AOqWtZ72thV9usn19TrV02gJ6blXE/a9sIQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA+GaKS9kjzYIxCtiK69/lBovrD0Ekzl6yVscrrbLQOUAiAw/ZU/BotrGdr2hu6aSZiMB81lV267EdmekkRrswlgGQ=="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/csvtojson-1.1.5.tgz_1493837373719_0.3876660733949393"}, "directories": {}}, "1.1.6": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Keyang/node-csvtojson", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "git+https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dcohenb"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/trangtungn"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/atufkas"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/fenichelar"}, {"name": "<PERSON>", "url": "https://github.com/blakeblackshear"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/roodboi"}, {"url": "https://github.com/markwithers"}, {"name": "<PERSON>", "url": "https://github.com/colarob"}, {"name": "<PERSON>", "url": "https://github.com/jessicagood"}, {"url": "https://github.com/jondayft"}, {"name": "<PERSON>", "url": "https://github.com/thegreatsunra"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Jimi<PERSON>ord"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Off76"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/Keyang"}, {"name": "Ionică Bizău", "email": "<EMAIL>", "url": "https://github.com/IonicaBizau"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/slang800"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/t3dodson"}, {"name": "<PERSON>", "url": "https://github.com/jef<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/amilajack"}, {"name": "Zsolt R<PERSON>", "url": "https://github.com/molnarzs"}], "version": "1.1.6", "keywords": ["csv", "csv parser", "parse csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "csv to json", "csv convert", "<PERSON><PERSON><PERSON>", "convert csv to json", "csv-json"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": "MIT", "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-browserify": "^4.0.1", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.11.0", "grunt-contrib-watch": "^0.6.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "load-grunt-tasks": "^3.4.0", "mocha": "^2.4.5", "minimist": "^1.2.0"}, "dependencies": {"lodash": "^4.17.3", "strip-bom": "1.0.0"}, "scripts": {"test": "mocha ./test -R spec", "test-debug": "mocha debug ./test -R spec", "test-all": "mocha  ./test -R spec && CSV_WORKER=3 mocha ./test -R spec "}, "gitHead": "bd563764bd5bbb7a50503ca78b5fd0a6452d1412", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@1.1.6", "_shasum": "b67accbff21a47312ce5d9253f5e50b0fc09eb18", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.3", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "dist": {"shasum": "b67accbff21a47312ce5d9253f5e50b0fc09eb18", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-1.1.6.tgz", "integrity": "sha512-0Qxs52gKNIoaMsRRkT2CvGRpAd0oalRJhjVcv1jj876wPhWfuu+aalj4fjEd95UiBhXXfjrHeuUMqJnt1s2P9A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCHc8+H3UjJ3olrMYzeuhVQ/2MRPizQEnYsH3MM8dAkRQIgYrTRzO+IkIC+J9RgyxhZm0BI63KnGrLpTRx5ny9/NsU="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csvtojson-1.1.6.tgz_1495801951389_0.1832716551143676"}, "directories": {}}, "1.1.7": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Keyang/node-csvtojson", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/dcohenb"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/trangtungn"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/atufkas"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/fenichelar"}, {"name": "<PERSON>", "url": "https://github.com/blakeblackshear"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/roodboi"}, {"url": "https://github.com/markwithers"}, {"name": "<PERSON>", "url": "https://github.com/colarob"}, {"name": "<PERSON>", "url": "https://github.com/jessicagood"}, {"url": "https://github.com/jondayft"}, {"name": "<PERSON>", "url": "https://github.com/thegreatsunra"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Jimi<PERSON>ord"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Off76"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/Keyang"}, {"name": "Ionică Bizău", "email": "<EMAIL>", "url": "https://github.com/IonicaBizau"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/slang800"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/t3dodson"}, {"name": "<PERSON>", "url": "https://github.com/jef<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/amilajack"}, {"name": "Zsolt R<PERSON>", "url": "https://github.com/molnarzs"}], "version": "1.1.7", "keywords": ["csv", "csv parser", "parse csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "csv to json", "csv convert", "<PERSON><PERSON><PERSON>", "convert csv to json", "csv-json"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": "MIT", "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-browserify": "^4.0.1", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.11.0", "grunt-contrib-watch": "^0.6.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "load-grunt-tasks": "^3.4.0", "minimist": "^1.2.0", "mocha": "^2.4.5", "request": "^2.81.0"}, "dependencies": {"lodash": "^4.17.3", "strip-bom": "1.0.0"}, "scripts": {"test": "mocha ./test -R spec", "test-debug": "mocha debug ./test -R spec", "test-all": "mocha  ./test -R spec && CSV_WORKER=3 mocha ./test -R spec "}, "gitHead": "f861267ced7bcd4d0db597d9c6e471692f9a5d6e", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@1.1.7", "_shasum": "17ded07aec0d8c01f2d6f6871cfa36ab88b149b1", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.10.2", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "dist": {"shasum": "17ded07aec0d8c01f2d6f6871cfa36ab88b149b1", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-1.1.7.tgz", "integrity": "sha512-YAX6bx6Cq1los/Eq6R5qgwFPtFu9dx/IcYnxAlG7qbI/1ESGxheb9P3VZEyTM49iS9Ta+Rdow1PmAASRaMnd4Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID7DHXyZJsX6LdXX/RpVRnpW5NfUb30EcWhPX7ImVFe1AiBby3cAV2ZdKf9bjMxW3dmu1ES7P2faV7jfE2lCP+KszA=="}]}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csvtojson-1.1.7.tgz_1497994144445_0.7482593941967934"}, "directories": {}}, "1.1.8": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Keyang/node-csvtojson", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "git+https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/thegreatsunra"}, {"name": "<PERSON>", "url": "https://github.com/jessicagood"}, {"url": "https://github.com/nbelakovski"}, {"name": "<PERSON>", "url": "https://github.com/colarob"}, {"name": "<PERSON>", "url": "https://github.com/roodboi"}, {"name": "<PERSON>", "url": "https://github.com/JoseExposito"}, {"name": "<PERSON>", "url": "https://github.com/dcohenb"}, {"name": "<PERSON>", "url": "https://github.com/richardpringle"}, {"name": "<PERSON>", "url": "https://github.com/bertyhell"}, {"url": "https://github.com/jondayft"}, {"name": "<PERSON>", "url": "https://github.com/brucejo75"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Jimi<PERSON>ord"}, {"name": "<PERSON>", "url": "https://github.com/fenichelar"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Off76"}, {"name": "<PERSON>", "url": "https://github.com/blakeblackshear"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/amilajack"}, {"name": "Zsolt R<PERSON>", "url": "https://github.com/molnarzs"}, {"name": "Ionică Bizău", "url": "<PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kakts"}, {"url": "https://github.com/markwithers"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/trangtungn"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Keyang"}, {"name": "<PERSON>", "url": "https://github.com/jef<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/slang800"}, {"name": "<PERSON>", "url": "https://github.com/atufkas"}, {"name": "<PERSON>", "url": "https://github.com/ronkorving"}], "version": "1.1.8", "keywords": ["csv", "csv parser", "parse csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "csv to json", "csv convert", "<PERSON><PERSON><PERSON>", "convert csv to json", "csv-json"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": "MIT", "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-browserify": "^4.0.1", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.11.0", "grunt-contrib-watch": "^0.6.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "load-grunt-tasks": "^3.4.0", "minimist": "^1.2.0", "mocha": "^2.4.5", "request": "^2.81.0"}, "dependencies": {"lodash": "^4.17.3", "strip-bom": "1.0.0"}, "scripts": {"test": "mocha ./test -R spec", "test-debug": "mocha debug ./test -R spec", "test-all": "mocha  ./test -R spec && CSV_WORKER=3 mocha ./test -R spec "}, "browser": {"child_process": false}, "gitHead": "d4dd7d555ce00c19426eee2fa6ba37e0d36b2048", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@1.1.8", "_shasum": "e64e5e98e65fde7e6fd01436cb5de0ac7d1dda70", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "6.10.2", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "dist": {"shasum": "e64e5e98e65fde7e6fd01436cb5de0ac7d1dda70", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-1.1.8.tgz", "integrity": "sha512-CrvX0Mvwx2a7ZHSjpLyb/dSqNhRnPJNN6kk9tYYn/u99K5WlQRzTKAxN7ZEpIBkTjSKrOyQ0HSvWferAUy60Vw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBCedMRCpjhxTSDfD1F1AYnz9Cx8bPZ37bHudCtDVN8FAiEA/viQHiMO1X6sHGS8dO5AEpBkf2TVAZtwlrXdsBYZqkM="}]}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csvtojson-1.1.8.tgz_1506798900662_0.9468136681243777"}, "directories": {}}, "1.1.9": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Keyang/node-csvtojson", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "git+https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/thegreatsunra"}, {"name": "<PERSON>", "url": "https://github.com/jessicagood"}, {"url": "https://github.com/nbelakovski"}, {"name": "<PERSON>", "url": "https://github.com/colarob"}, {"name": "<PERSON>", "url": "https://github.com/roodboi"}, {"name": "<PERSON>", "url": "https://github.com/JoseExposito"}, {"name": "<PERSON>", "url": "https://github.com/dcohenb"}, {"name": "<PERSON>", "url": "https://github.com/richardpringle"}, {"name": "<PERSON>", "url": "https://github.com/bertyhell"}, {"url": "https://github.com/jondayft"}, {"name": "<PERSON>", "url": "https://github.com/brucejo75"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Jimi<PERSON>ord"}, {"name": "<PERSON>", "url": "https://github.com/fenichelar"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Off76"}, {"name": "<PERSON>", "url": "https://github.com/blakeblackshear"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/amilajack"}, {"name": "Zsolt R<PERSON>", "url": "https://github.com/molnarzs"}, {"name": "Ionică Bizău", "url": "<PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kakts"}, {"url": "https://github.com/markwithers"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/trangtungn"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Keyang"}, {"name": "<PERSON>", "url": "https://github.com/jef<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/slang800"}, {"name": "<PERSON>", "url": "https://github.com/atufkas"}, {"name": "<PERSON>", "url": "https://github.com/ronkorving"}], "version": "1.1.9", "keywords": ["csv", "csv parser", "parse csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "csv to json", "csv convert", "<PERSON><PERSON><PERSON>", "convert csv to json", "csv-json"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": "MIT", "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-browserify": "^4.0.1", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.11.0", "grunt-contrib-watch": "^0.6.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "load-grunt-tasks": "^3.4.0", "minimist": "^1.2.0", "mocha": "^2.4.5", "request": "^2.81.0"}, "dependencies": {"lodash": "^4.17.3", "strip-bom": "1.0.0"}, "scripts": {"test": "mocha ./test -R spec", "test-debug": "mocha debug ./test -R spec", "test-all": "mocha  ./test -R spec && CSV_WORKER=3 mocha ./test -R spec "}, "browser": {"child_process": false, "fs": false}, "gitHead": "34df32c3c27cefbc941214514a74f23cc24f449d", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@1.1.9", "_shasum": "e641ae72f7bc2fa3f9aaf127e021fc89447c1cd1", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "6.10.2", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "dist": {"shasum": "e641ae72f7bc2fa3f9aaf127e021fc89447c1cd1", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-1.1.9.tgz", "integrity": "sha512-WeldFETgGxAgLIPeyYv9f1dx59HYOZ7CQlTwIVgiq9q5dLngWaaDRJMEhZklZHsHlptRmtVZI83K4vqFVypMjw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCRDqHxbaTtcXZiz/WxgXiXk7vGRm1z9Q7+oi8l2jdkXgIgBE5Qb/L/8Chrpte9HfdE44I6i11Z5t0y+XuD5ogLFt4="}]}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csvtojson-1.1.9.tgz_1507334527405_0.49919763556681573"}, "directories": {}}, "1.1.11": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Keyang/node-csvtojson", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "git+https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/thegreatsunra"}, {"name": "<PERSON>", "url": "https://github.com/jessicagood"}, {"url": "https://github.com/nbelakovski"}, {"name": "<PERSON>", "url": "https://github.com/colarob"}, {"name": "<PERSON>", "url": "https://github.com/roodboi"}, {"name": "<PERSON>", "url": "https://github.com/JoseExposito"}, {"name": "<PERSON>", "url": "https://github.com/dcohenb"}, {"name": "<PERSON>", "url": "https://github.com/richardpringle"}, {"name": "<PERSON>", "url": "https://github.com/bertyhell"}, {"url": "https://github.com/jondayft"}, {"name": "<PERSON>", "url": "https://github.com/brucejo75"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Jimi<PERSON>ord"}, {"name": "<PERSON>", "url": "https://github.com/fenichelar"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Off76"}, {"name": "<PERSON>", "url": "https://github.com/blakeblackshear"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/amilajack"}, {"name": "Zsolt R<PERSON>", "url": "https://github.com/molnarzs"}, {"name": "Ionică Bizău", "url": "<PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kakts"}, {"url": "https://github.com/markwithers"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/trangtungn"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Keyang"}, {"name": "<PERSON>", "url": "https://github.com/jef<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/slang800"}, {"name": "<PERSON>", "url": "https://github.com/atufkas"}, {"name": "<PERSON>", "url": "https://github.com/ronkorving"}], "version": "1.1.11", "keywords": ["csv", "csv parser", "parse csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "csv to json", "csv convert", "<PERSON><PERSON><PERSON>", "convert csv to json", "csv-json"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": "MIT", "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-browserify": "^4.0.1", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.11.0", "grunt-contrib-watch": "^1.0.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "load-grunt-tasks": "^3.4.0", "minimist": "^1.2.0", "mocha": "^5.1.1", "request": "^2.81.0", "sinon": "^3.2.3"}, "dependencies": {"lodash": "^4.17.3", "strip-bom": "3.0.0"}, "scripts": {"test": "mocha ./test -R spec", "test-debug": "mocha debug ./test -R spec", "test-all": "mocha  ./test -R spec && CSV_WORKER=3 mocha ./test -R spec "}, "browser": {"child_process": false, "fs": false}, "gitHead": "26082420b57abf28c156e3cb0b5a69b6cb2b6fa2", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@1.1.11", "_npmVersion": "5.6.0", "_nodeVersion": "6.9.5", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-EO5TlG/u0yuGQ5uZgs1PvIFbAcW1HtdABNP5lZiEly7dWdkuzWh3ocEeIaz68hIA+bI8i/oCKaEh9EoS8PSUZQ==", "shasum": "5af90637ef82d8e52e24bc2acab2daf430862c20", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-1.1.11.tgz", "fileCount": 96, "unpackedSize": 1649843, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8Y8WCRA9TVsSAnZWagAApyQP/3KezIj+WACvcQnZeqqJ\n9KaWp7Bn24qQfEwXR/syUFGALhAGcFWdigNktJkDZeDwOqW73uS+dVURjTbs\nxZuls5Kvl/yje0fL20WnFaHxAj+unamx5jMkTa3ZcOUths3DlMM3+vU6mJxR\nBWJL4TdYg05cygNIuaa5BjmexyNSt0lWs/Pk7kUfWXpBWibQCCXcatlyAwZ1\ncnknfztLmzMXTuRx5QdPOQZZcHZqLUDuWTRXJ3rsl3R2Lx5BXqM4lOlWqimV\nUumQBIQPM196MspkV6tDkKHDZ8gHRqJV0HK57DBn39t1AAdKld/Eizedy0gY\n/8iMzV2Y9XOEzD1ii0zMe9Nue7KGYIC+90fmMgbn8J1BGKuoPyQBw22ZaT6H\nHh0Gy9jsSH6oU1d5rxmXDSNPgWcno/CKgtgBx9lNJ8tGxn+OxI3s7EjHnVYX\n/BLKvrrTe895Hnke/Mkk2bbtD/dLBTMz/1+ClauBFbkAhhICs2l3pyaMALpD\nx/+qokjmnz+2r3jc/xSerBlavKS4RSg/D5YwctOru45VPByXkVIDhhMzkAMT\nztGljGQdm8xuMQbpDoGhxQn8P18tQs1OA9TfgojAE75BckPjuBA3eDl92FgY\nwQ+V/68sZOX/HDqgXtE/VtJLa6r+IkJiNr1LfHQB8ILacOFf5pNWiZ5G6mun\noPO3\r\n=r6wv\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCDPPshUhCq9q7/WWlKNBpJQf2Fh/kj4DChSDMBEh3ngAIhAMjUkX+fMVVjWkBX4nGgu3Xp5n8hc2SnhErRe0w/YHX+"}]}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csvtojson_1.1.11_1525780244987_0.23575524751548205"}, "_hasShrinkwrap": false}, "1.1.12": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Keyang/node-csvtojson", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "git+https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/thegreatsunra"}, {"name": "<PERSON>", "url": "https://github.com/jessicagood"}, {"url": "https://github.com/nbelakovski"}, {"name": "<PERSON>", "url": "https://github.com/colarob"}, {"name": "<PERSON>", "url": "https://github.com/roodboi"}, {"name": "<PERSON>", "url": "https://github.com/JoseExposito"}, {"name": "<PERSON>", "url": "https://github.com/dcohenb"}, {"name": "<PERSON>", "url": "https://github.com/richardpringle"}, {"name": "<PERSON>", "url": "https://github.com/bertyhell"}, {"url": "https://github.com/jondayft"}, {"name": "<PERSON>", "url": "https://github.com/brucejo75"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Jimi<PERSON>ord"}, {"name": "<PERSON>", "url": "https://github.com/fenichelar"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Off76"}, {"name": "<PERSON>", "url": "https://github.com/blakeblackshear"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/amilajack"}, {"name": "Zsolt R<PERSON>", "url": "https://github.com/molnarzs"}, {"name": "Ionică Bizău", "url": "<PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kakts"}, {"url": "https://github.com/markwithers"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/trangtungn"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Keyang"}, {"name": "<PERSON>", "url": "https://github.com/jef<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/slang800"}, {"name": "<PERSON>", "url": "https://github.com/atufkas"}, {"name": "<PERSON>", "url": "https://github.com/ronkorving"}], "version": "1.1.12", "keywords": ["csv", "csv parser", "parse csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "csv to json", "csv convert", "<PERSON><PERSON><PERSON>", "convert csv to json", "csv-json"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": "MIT", "engines": {"node": ">=0.10"}, "devDependencies": {"grunt": "^0.4.5", "grunt-browserify": "^4.0.1", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-uglify": "^0.11.0", "grunt-contrib-watch": "^1.0.1", "grunt-git": "^0.3.5", "grunt-madge": "0.0.6", "grunt-mocha-test": "^0.12.7", "grunt-newer": "^1.1.0", "imgur": "^0.1.5", "load-grunt-tasks": "^3.4.0", "minimist": "^1.2.0", "mocha": "^5.1.1", "request": "^2.81.0", "sinon": "^3.2.3"}, "dependencies": {"lodash": "^4.17.3", "strip-bom": "^2.0.0"}, "scripts": {"test": "mocha ./test -R spec", "test-debug": "mocha debug ./test -R spec", "test-all": "mocha  ./test -R spec && CSV_WORKER=3 mocha ./test -R spec "}, "browser": {"child_process": false, "fs": false}, "gitHead": "7353c39118dcc5b1e73739029349b01254772a29", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@1.1.12", "_npmVersion": "5.8.0", "_nodeVersion": "6.9.1", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-gJg1II2uXh8H6XP7L1YzX/6H8rrUbSTiEg4SaI6/pHOcnZovwAR3Rmm9TizSsGQBBXl1pb/JJPlaImV2YIuMrg==", "shasum": "3872eef9588080f74c222ffe53cfdd416f9bb7d5", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-1.1.12.tgz", "fileCount": 96, "unpackedSize": 1649764, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8gXkCRA9TVsSAnZWagAAZm8P/0Om3irxdQ15Hcs/3lZa\nbsBMuIcOF8hz5V2sSNdZWIsflRENbuuoQmjEmcfpDf2OZ5IIN/6qRfulXngz\non5nGFVl2RgeOQU+TqBY2F0hlBcacWPvsSPCHrRY1eW/TFlwTjeM4soYKKS1\nQTw+t9fLotrfqDe4bksaihdjDJvo0c38dMQ3oVqqumzv5aTGl/Q3epgGraqK\nn+96CJUcyoFGzNI9BZOsvlV8ReJdwK7Cbkj9rkFiKBZO8kU883pXOu6hTnYq\n7BAdGWtbndmybmpvrW9rXuRWqiykLUHBYJWBAo8D04KPwF9HLwsfnxRe3bNx\nPBliggVBDtMxGWZMtUqa394BFEj0z+ihmxP2Yr/eCqxN6QOW3jrHTnPVQtCz\nb18C04MjQLIbbaVxnV85rD4imsNOuHaFNqqyRYQlu7yyD5f6EYzjAG/Go1r0\n+s6bg4EWtpb4SmAzV7nhDgyWM9OaPNDpkv93uv7CfhZznUeXZUmCNvnDetIu\nk/fF5MBOkuTBCcVbVbDAruSidVSHLwfDHYAdgpkZLtTluwkeCBz7dlE6et+a\neQ/gmVa/ird8DRlkM79Fj+8MOC3ifnbPdPpUYu7w2M4XBw67iAIvQOlFUVwm\nI5lRPFHvHrLmm5eBYFxpOcxuHskYM27cOO0iPfciT3KrKLWpTOkrcB/PawTZ\n80oE\r\n=3KIE\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE0ZB5/+GLVd5YfhDudC0I/LzI9stp/vEmch89/8rjldAiAt2DwK1lNXV5xmfpgEwc0496DwLtjktBbKeWMyu4RO+A=="}]}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csvtojson_1.1.12_1525810658854_0.7754625668942554"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Keyang/node-csvtojson", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "git+https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/thegreatsunra"}, {"name": "<PERSON>", "url": "https://github.com/jessicagood"}, {"url": "https://github.com/nbelakovski"}, {"name": "<PERSON>", "url": "https://github.com/colarob"}, {"name": "<PERSON>", "url": "https://github.com/roodboi"}, {"name": "<PERSON>", "url": "https://github.com/JoseExposito"}, {"name": "<PERSON>", "url": "https://github.com/dcohenb"}, {"name": "<PERSON>", "url": "https://github.com/richardpringle"}, {"name": "<PERSON>", "url": "https://github.com/bertyhell"}, {"url": "https://github.com/jondayft"}, {"name": "<PERSON>", "url": "https://github.com/brucejo75"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Jimi<PERSON>ord"}, {"name": "<PERSON>", "url": "https://github.com/fenichelar"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Off76"}, {"name": "<PERSON>", "url": "https://github.com/blakeblackshear"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/amilajack"}, {"name": "Zsolt R<PERSON>", "url": "https://github.com/molnarzs"}, {"name": "Ionică Bizău", "url": "<PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kakts"}, {"url": "https://github.com/markwithers"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/trangtungn"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Keyang"}, {"name": "<PERSON>", "url": "https://github.com/jef<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/slang800"}, {"name": "<PERSON>", "url": "https://github.com/atufkas"}, {"name": "<PERSON>", "url": "https://github.com/ronkorving"}], "version": "2.0.0", "keywords": ["csv", "csv parser", "parse csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "csv to json", "csv convert", "<PERSON><PERSON><PERSON>", "convert csv to json", "csv-json"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": "MIT", "engines": {"node": ">=4.0.0"}, "devDependencies": {"@types/bluebird": "^3.5.20", "@types/mocha": "^5.2.0", "@types/node": "^10.0.1", "coveralls": "^3.0.1", "minimist": "^1.2.0", "mocha": "^5.1.1", "nyc": "^11.7.3", "sinon": "^3.2.3", "ts-node": "^6.0.3", "typescript": "^2.8.3"}, "dependencies": {"bluebird": "^3.5.1", "lodash": "^4.17.3", "strip-bom": "^2.0.0"}, "nyc": {"extension": [".ts", ".tsx"], "include": ["./src/**/*.ts"], "all": true}, "scripts": {"dev": "tsc -w", "build": "rm -Rf ./v2 && tsc", "test": "TS_NODE_CACHE=false mocha -r ts-node/register src/**/*.test.ts ./test/*.ts -R spec", "travis": "nyc --reporter lcov mocha -r ts-node/register src/**/*.test.ts ./test/*.ts -R spec", "test:debug": "mocha debug -r ts-node/register src/**/*.test.ts ./test/*.ts -R spec", "test-all": "mocha  ./test -R spec && CSV_WORKER=3 mocha ./test -R spec ", "test:unit": "mocha -r ts-node/register src/**/*.test.ts", "test:all:debug": "mocha debug ./testNew -R spec", "coverage": "nyc --reporter html  mocha -r ts-node/register src/**/*.test.ts ./test/*.ts -R spec", "coveralls": "cat ./coverage/lcov.info | ./node_modules/.bin/coveralls"}, "browser": {"child_process": false, "fs": false}, "main": "./v2/index.js", "gitHead": "4ca7f404e6919aaaca9988db4b25c4a7aefae83a", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@2.0.0", "_shasum": "688e866540814bed6d9a1648ce31bd3b8a4d570f", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.14.2", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "dist": {"shasum": "688e866540814bed6d9a1648ce31bd3b8a4d570f", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-2.0.0.tgz", "fileCount": 365, "unpackedSize": 8187461, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbAdsdCRA9TVsSAnZWagAAWuYP/RdMTRikk5OZIu8o0sge\neKbe1kfYC24Ey1vLR2jdBqEm2SdFC9oK0HFkYxkow1d5H51mlHxxQ+/wrRh7\nemf6INHTNi2MF2et5TdkfLJFPIV1lYIDG8+Otq5TyOAzvnh2bsO3XpekoeZI\n8EsrflcXlP/2ARA9sjpyzeut3M1NgP9eU2My4hnKg9weGvfy9KBuYqBW4mkH\nJBG8W/G35BZhBX5zOp27nGo8RiVZgVsXNMt7Xaq9vazby3w1Tw+z1G2cn0Yd\nOAVM0rNa/IavjggbXL6Qg8NVotR2n/o1afm6cK3d8gXysNCqBONeNGGNOjXK\nlknrn0JUJCjVMjeZjgMm+dXrsclW3EtpUH3K9dBXfPU7pMWbNRYJ17R03o6u\nSnznd8EWAPR86L0n7GXZ36BsKPtu3LSrf2LWi/Pz+x9/UycqtPRAXvVcbEvb\ng9fRoAGq1a+zmzOfCIMjy7PMTvs/hMTbQxQ5RBML+kj0bs1YP7E82InpfnQ3\nZfWOYozDTkoJ8NmWCKUcPXtkvG1yl3iHRV0V6ytTITW4XwMi/aFL4fCp7qDk\nb3cJPcbt7KpYJvB8xjrRX8whoHEUwQvVwW12xlUOrIQGljSK9HxGqU6BUvRJ\nU1EhqxDHSU1wiXOUekwtGQ6aAdNwptJ+ANpyelt/avHrU+gmfQnl/CxMTHjx\nmbUa\r\n=I47D\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-Zt7Ux/pKR5u+R+x+zVvPUupuouYct7heGLNfGcADfU7OA5Dg4y2hvpiEH0sOwpdYhcmjbiH7S7b/K+CcZJzgNQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDM3ZRnq7Fd+FXPnCNIxLVP/D0jxJzrmKBsBlQAmIZhUAIhAOKCdZm190PqatzumcH3dwgZsL13CLQTOul5CcvXCgMM"}]}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csvtojson_2.0.0_1526848283443_0.06452361991458755"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Keyang/node-csvtojson", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "git+https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/thegreatsunra"}, {"name": "<PERSON>", "url": "https://github.com/jessicagood"}, {"url": "https://github.com/nbelakovski"}, {"name": "<PERSON>", "url": "https://github.com/colarob"}, {"name": "<PERSON>", "url": "https://github.com/roodboi"}, {"name": "<PERSON>", "url": "https://github.com/JoseExposito"}, {"name": "<PERSON>", "url": "https://github.com/dcohenb"}, {"name": "<PERSON>", "url": "https://github.com/richardpringle"}, {"name": "<PERSON>", "url": "https://github.com/bertyhell"}, {"url": "https://github.com/jondayft"}, {"name": "<PERSON>", "url": "https://github.com/brucejo75"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Jimi<PERSON>ord"}, {"name": "<PERSON>", "url": "https://github.com/fenichelar"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Off76"}, {"name": "<PERSON>", "url": "https://github.com/blakeblackshear"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/amilajack"}, {"name": "Zsolt R<PERSON>", "url": "https://github.com/molnarzs"}, {"name": "Ionică Bizău", "url": "<PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kakts"}, {"url": "https://github.com/markwithers"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/trangtungn"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Keyang"}, {"name": "<PERSON>", "url": "https://github.com/jef<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/slang800"}, {"name": "<PERSON>", "url": "https://github.com/atufkas"}, {"name": "<PERSON>", "url": "https://github.com/ronkorving"}], "version": "2.0.1", "keywords": ["csv", "csv parser", "parse csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "csv to json", "csv convert", "<PERSON><PERSON><PERSON>", "convert csv to json", "csv-json"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": "MIT", "engines": {"node": ">=4.0.0"}, "devDependencies": {"@types/bluebird": "^3.5.20", "@types/mocha": "^5.2.0", "@types/node": "^10.0.1", "coveralls": "^3.0.1", "minimist": "^1.2.0", "mocha": "^5.1.1", "nyc": "^11.7.3", "sinon": "^3.2.3", "ts-node": "^6.0.3", "typescript": "^2.8.3"}, "dependencies": {"bluebird": "^3.5.1", "lodash": "^4.17.3", "strip-bom": "^2.0.0"}, "nyc": {"extension": [".ts", ".tsx"], "include": ["./src/**/*.ts"], "all": true}, "scripts": {"dev": "tsc -w", "build": "rm -Rf ./v2 && tsc", "test": "TS_NODE_CACHE=false mocha -r ts-node/register src/**/*.test.ts ./test/*.ts -R spec", "travis": "nyc --reporter lcov mocha -r ts-node/register src/**/*.test.ts ./test/*.ts -R spec", "test:debug": "mocha debug -r ts-node/register src/**/*.test.ts ./test/*.ts -R spec", "test-all": "mocha  ./test -R spec && CSV_WORKER=3 mocha ./test -R spec ", "test:unit": "mocha -r ts-node/register src/**/*.test.ts", "test:all:debug": "mocha debug ./testNew -R spec", "coverage": "nyc --reporter html  mocha -r ts-node/register src/**/*.test.ts ./test/*.ts -R spec", "coveralls": "cat ./coverage/lcov.info | ./node_modules/.bin/coveralls"}, "browser": {"child_process": false, "fs": false}, "main": "./v2/index.js", "gitHead": "313584e74efb5a89a6531bbe9d2d34e277a8b655", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@2.0.1", "_shasum": "a9246e954e6043da293670343e401c68f8ea1148", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.14.2", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "dist": {"shasum": "a9246e954e6043da293670343e401c68f8ea1148", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-2.0.1.tgz", "fileCount": 365, "unpackedSize": 8187399, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbDWMECRA9TVsSAnZWagAAvBEP/2na6xi30ordzt7cA/ev\nhO1S1k6kwM7JHEsu3hJ0yq2GNg0QSbNqGOspH5pTUfCEsV8qbkfKKG2Sekt7\nrftv2TIODurpIhSwyoHY+V7XWE2SDWLC7u0WukO3njaA+T167Dm6IOIIWhVw\nVQlXthviWmJw2a6IhOM4BgDo5p3NmGseazpCxjYVZsXnDU6qU5HRk5OPDQGu\n3nmH7c4swq57xSXq4Ox3UrzttCcWwCf3NpXJkuX0uF06qYzP8iAuIeiRYWiR\nw4mtm1Bbv1HMcaOxAe9zjj3YJJ8IOc3PSYt39ryGdcTAAVGdyhTTtkUGT6d4\nsCcPn9MyBKsCWkKzadEQwUA9MaSTyXy8w+/y67Rdoz6P6Xq50Xy+8YCzYUXx\nOQjknZXh7H27FdY9/3ZfdmE+71l0zsQd4F0nx7P+wWnj9ClXFbh5nRiRG6wI\nOqA/m3ik9JbTp5KKYt2B/JzWhGHr37jqGjgcySPbJ3WLKPMUsVand2Bv9O2e\niS7QS5SM9pR5z40VQdb1Zr6vvYcG9LT3yRGImKVeOHmejuGuxTySB1x7VXGd\nodcVs4HUucRvVeJ5qGFaaX+vDpwEPGC1ZVWasrO6AuO0W97mBU1BB3kpFgjE\nMIhO4iTcmShmzH3oB0DlUzoRXe5LOf4EOAuduE5hqEFzCeYLudzmCLHYkCxb\nF4sn\r\n=qlvY\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-o7tHIvePiao5s0A7i+z8ry4CEFV+6RrbuheC2s4V+NTlowus7gXpNM043FRJSgl5l4Im+bfsl+zmy4d5BTBaXg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCOvCRM6NNhvFlgUoreX+XNKDIx7dOgrVfRndnQyy5+IAIhAIjr3NK7P2jAGRP3wuEq2m04w8ce9j4U4J9tckDL/LRo"}]}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csvtojson_2.0.1_1527603971149_0.42690138504050656"}, "_hasShrinkwrap": false}, "2.0.2": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Keyang/node-csvtojson", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "git+https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/thegreatsunra"}, {"name": "<PERSON>", "url": "https://github.com/jessicagood"}, {"url": "https://github.com/nbelakovski"}, {"name": "<PERSON>", "url": "https://github.com/colarob"}, {"name": "<PERSON>", "url": "https://github.com/roodboi"}, {"name": "<PERSON>", "url": "https://github.com/JoseExposito"}, {"name": "<PERSON>", "url": "https://github.com/dcohenb"}, {"name": "<PERSON>", "url": "https://github.com/richardpringle"}, {"name": "<PERSON>", "url": "https://github.com/bertyhell"}, {"url": "https://github.com/jondayft"}, {"name": "<PERSON>", "url": "https://github.com/brucejo75"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Jimi<PERSON>ord"}, {"name": "<PERSON>", "url": "https://github.com/fenichelar"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Off76"}, {"name": "<PERSON>", "url": "https://github.com/blakeblackshear"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/amilajack"}, {"name": "Zsolt R<PERSON>", "url": "https://github.com/molnarzs"}, {"name": "Ionică Bizău", "url": "<PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kakts"}, {"url": "https://github.com/markwithers"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/trangtungn"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Keyang"}, {"name": "<PERSON>", "url": "https://github.com/jef<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/slang800"}, {"name": "<PERSON>", "url": "https://github.com/atufkas"}, {"name": "<PERSON>", "url": "https://github.com/ronkorving"}], "version": "2.0.2", "keywords": ["csv", "csv parser", "parse csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "csv to json", "csv convert", "<PERSON><PERSON><PERSON>", "convert csv to json", "csv-json"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": "MIT", "engines": {"node": ">=4.0.0"}, "devDependencies": {"@types/bluebird": "^3.5.20", "@types/mocha": "^5.2.0", "@types/node": "^10.0.1", "coveralls": "^3.0.1", "minimist": "^1.2.0", "mocha": "^5.1.1", "nyc": "^11.7.3", "sinon": "^3.2.3", "ts-node": "^6.0.3", "typescript": "^2.8.3"}, "dependencies": {"bluebird": "^3.5.1", "lodash": "^4.17.3", "strip-bom": "^2.0.0"}, "nyc": {"extension": [".ts", ".tsx"], "include": ["./src/**/*.ts"], "all": true}, "scripts": {"dev": "tsc -w", "build": "rm -Rf ./v2 && tsc", "test": "TS_NODE_CACHE=false mocha -r ts-node/register src/**/*.test.ts ./test/*.ts -R spec", "travis": "nyc --reporter lcov mocha -r ts-node/register src/**/*.test.ts ./test/*.ts -R spec", "test:debug": "mocha debug -r ts-node/register src/**/*.test.ts ./test/*.ts -R spec", "test-all": "mocha  ./test -R spec && CSV_WORKER=3 mocha ./test -R spec ", "test:unit": "mocha -r ts-node/register src/**/*.test.ts", "test:all:debug": "mocha debug ./testNew -R spec", "coverage": "nyc --reporter html  mocha -r ts-node/register src/**/*.test.ts ./test/*.ts -R spec", "coveralls": "cat ./coverage/lcov.info | ./node_modules/.bin/coveralls"}, "browser": {"child_process": false, "fs": false}, "main": "./v2/index.js", "gitHead": "29ae4c5a5ba3b1eafdd3e3984c51b2ae927f7b90", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@2.0.2", "_shasum": "720fa4d79fd18711e3a361f2cf113803ac2b2d8f", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.14.2", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "dist": {"shasum": "720fa4d79fd18711e3a361f2cf113803ac2b2d8f", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-2.0.2.tgz", "fileCount": 365, "unpackedSize": 8187399, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbDWNgCRA9TVsSAnZWagAACwEP/iZo3YsX3g7vrlEL/nKi\nhn072C9TFPN2iCw5VwlH5AljOqbJjIuXBzIWkPZ1IKo6oo0jHzVcMYgrEMIa\nfEIYxN2Ktx0uLQJ6nClRJNtrPQAeUQF0KrD6ghRjS9T330aINexaThH5KFth\nyKKXea2j/wnE301pDE55r8XsEceKhg5kx3T7sT9UQ82veqKTOtg3fziRJtAM\nuvcTukIUCvQOvHMe0X6XdTiFLIJ/fqpV3n1BUc4CcG1REcuWaymaKAoU37rT\nhNi+ya0WuFBC5fFZeDtBE4ogw/G02puuGe8WOPAXmS/OrAIy2lLrz3Ewj+VA\nqK/52JaWoTJTGTGVQ5aUmyFJAIIyqspY5LancLOr8QmImX5Mf6oSIJR09E6d\nbvGsn00XJRkncQNTmpOdOob9SSauc0OxEGIK26kfa15eoK9hkmX12aXi6wex\nh7uafuM/dmQij2KZPiL01mUXwHp7qkR9p6tTX7iA9KAC+c8whjNpwij2jvb7\nYalMlveWCZM3pD2zbD5WbdYhMn2Cq0K6K5+j9qIswSrUCAT6SLlMdvKIlcBF\nB+He1Sv0WgzjoKxkQu5cN1UmBAN/fYZUMAgSU3o1uy7ACyrHHzriEZwcENoQ\nTrtc0FBardadFBCEFQNzVsi8MujKBYtafAEvpns8txdthw2kZL9UCImV7Ada\n3nKi\r\n=aVvk\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-ornJMVCQh+SJJ84M4zl2oUOVNbC2j39GOkWk/qWEWDOF3a2rGFsyXl3KiVlDfMulaETlHoQufvKQz9fPEjHP5g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBmLnjkwDS0XghedX7s0zTLjGaxJCA8VW63BYma/AnmUAiAQL5K14P5GKNcUuWMAEyc33MOQaj7+1jNjsAjGkiSgng=="}]}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csvtojson_2.0.2_1527604062307_0.29307110137134584"}, "_hasShrinkwrap": false}, "2.0.3": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Keyang/node-csvtojson", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "git+https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/thegreatsunra"}, {"name": "<PERSON>", "url": "https://github.com/jessicagood"}, {"url": "https://github.com/nbelakovski"}, {"name": "<PERSON>", "url": "https://github.com/colarob"}, {"name": "<PERSON>", "url": "https://github.com/roodboi"}, {"name": "<PERSON>", "url": "https://github.com/JoseExposito"}, {"name": "<PERSON>", "url": "https://github.com/dcohenb"}, {"name": "<PERSON>", "url": "https://github.com/richardpringle"}, {"name": "<PERSON>", "url": "https://github.com/bertyhell"}, {"url": "https://github.com/jondayft"}, {"name": "<PERSON>", "url": "https://github.com/brucejo75"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Jimi<PERSON>ord"}, {"name": "<PERSON>", "url": "https://github.com/fenichelar"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Off76"}, {"name": "<PERSON>", "url": "https://github.com/blakeblackshear"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/amilajack"}, {"name": "Zsolt R<PERSON>", "url": "https://github.com/molnarzs"}, {"name": "Ionică Bizău", "url": "<PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kakts"}, {"url": "https://github.com/markwithers"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/trangtungn"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Keyang"}, {"name": "<PERSON>", "url": "https://github.com/jef<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/slang800"}, {"name": "<PERSON>", "url": "https://github.com/atufkas"}, {"name": "<PERSON>", "url": "https://github.com/ronkorving"}], "version": "2.0.3", "keywords": ["csv", "csv parser", "parse csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "csv to json", "csv convert", "<PERSON><PERSON><PERSON>", "convert csv to json", "csv-json"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": "MIT", "engines": {"node": ">=4.0.0"}, "devDependencies": {"@types/bluebird": "^3.5.20", "@types/mocha": "^5.2.0", "@types/node": "^10.0.1", "coveralls": "^3.0.1", "minimist": "^1.2.0", "mocha": "^5.1.1", "nyc": "^11.7.3", "sinon": "^3.2.3", "ts-node": "^6.0.3", "typescript": "^2.8.3"}, "dependencies": {"bluebird": "^3.5.1", "lodash": "^4.17.3", "strip-bom": "^2.0.0"}, "nyc": {"extension": [".ts", ".tsx"], "include": ["./src/**/*.ts"], "all": true}, "scripts": {"dev": "tsc -w", "build": "rm -Rf ./v2 && tsc", "test": "TS_NODE_CACHE=false mocha -r ts-node/register src/**/*.test.ts ./test/*.ts -R spec", "travis": "nyc --reporter lcov mocha -r ts-node/register src/**/*.test.ts ./test/*.ts -R spec", "test:debug": "mocha debug -r ts-node/register src/**/*.test.ts ./test/*.ts -R spec", "test-all": "mocha  ./test -R spec && CSV_WORKER=3 mocha ./test -R spec ", "test:unit": "mocha -r ts-node/register src/**/*.test.ts", "test:all:debug": "mocha debug ./testNew -R spec", "coverage": "nyc --reporter html  mocha -r ts-node/register src/**/*.test.ts ./test/*.ts -R spec", "coveralls": "cat ./coverage/lcov.info | ./node_modules/.bin/coveralls"}, "browser": {"child_process": false, "fs": false}, "main": "./v2/index.js", "gitHead": "36e03aeef10e15b167690efc9e4b53f9c499c36e", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@2.0.3", "_shasum": "1332e803634530dfb891a2f304df1244713c4958", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.14.2", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "dist": {"shasum": "1332e803634530dfb891a2f304df1244713c4958", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-2.0.3.tgz", "fileCount": 365, "unpackedSize": 8187437, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbDWQ6CRA9TVsSAnZWagAAot8QAKC3Iyb9kdc2kyQ4VzAa\nRIb5tLlsjM6to5FrJS2K32Own6U+lFAIFDrTv/4nBHBwm27mONBp2iiczVvn\nQcKCtSqed/HFPiM/6NsTHXc6reAwyAT2Dw1kLah5MrDE0o5+04vrc9D6ESR/\nfj56VN1T9ZlqO4GGtpogmvX3khUzamcMNPEdlhwrwuclwr1EdxrhUm5x1lOt\nqDnj0i+C1/jYaIwaFfs+Z4X4EUJM2qI14FDEP0FCX3pmxXjoF3Pn9fsc6W9Z\n/Gic32be/KyWdaqierCwz5YJQ4tb4i/1zOcMMmDaSB+00N9gLv2shLdo1fN5\nWzzTjJUBSdt5Zz6/lWiOfzGSd/fbP8C57Tde6k0IEm3l7svEphu+4O2eAbTb\nZNT8ZBtPj2AzUXgMJ8ZmXMJF5cP1LD/fe6zW2lqc5Uz6Ke4pThG5yQgZCuza\njrRllZeQuOGe42tbq27xeQrBvXJsbWAmCHu/20HHwYpsU+1yd6e/O5zmSZ0a\noBiOws4bQw2S8qGDjauhok7L2DRCb1Lqbfs2w3PWTwH3SBXQRNl7QU+PbHnm\nhAATDa8vlYT4p6PO92rkdoH40ycrvna8kVXhitncN+4EO0xv3ESg4LEO12WC\nTbpMCoDoG9nq8e5fGZujOWErk4/7e2X+z46A2z77c2fSmC+ewWIozoxaspY5\nxgdQ\r\n=Sc6z\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-OmQ9Dpg/CHTHwSsSvqF0fTIWgrFMhpVdKuTsm4FjVXezgTD3GiMNWqyLQjgBFTua9Nf+UdGOksao+jvENVzVBA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFrsT5jsJtZI1ekwPne1UVqc7WpthGnGgdFrxAHW0cbbAiBm+74PXlvt8VyekVmNvrHnEIhju67PQXJfQcDQfP4RsQ=="}]}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csvtojson_2.0.3_1527604281516_0.5405001337707809"}, "_hasShrinkwrap": false}, "2.0.4": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Keyang/node-csvtojson", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "git+https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/thegreatsunra"}, {"name": "<PERSON>", "url": "https://github.com/jessicagood"}, {"url": "https://github.com/nbelakovski"}, {"name": "<PERSON>", "url": "https://github.com/colarob"}, {"name": "<PERSON>", "url": "https://github.com/roodboi"}, {"name": "<PERSON>", "url": "https://github.com/JoseExposito"}, {"name": "<PERSON>", "url": "https://github.com/dcohenb"}, {"name": "<PERSON>", "url": "https://github.com/richardpringle"}, {"name": "<PERSON>", "url": "https://github.com/bertyhell"}, {"url": "https://github.com/jondayft"}, {"name": "<PERSON>", "url": "https://github.com/brucejo75"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Jimi<PERSON>ord"}, {"name": "<PERSON>", "url": "https://github.com/fenichelar"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Off76"}, {"name": "<PERSON>", "url": "https://github.com/blakeblackshear"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/amilajack"}, {"name": "Zsolt R<PERSON>", "url": "https://github.com/molnarzs"}, {"name": "Ionică Bizău", "url": "<PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kakts"}, {"url": "https://github.com/markwithers"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/trangtungn"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Keyang"}, {"name": "<PERSON>", "url": "https://github.com/jef<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/slang800"}, {"name": "<PERSON>", "url": "https://github.com/atufkas"}, {"name": "<PERSON>", "url": "https://github.com/ronkorving"}], "version": "2.0.4", "keywords": ["csv", "csv parser", "parse csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "csv to json", "csv convert", "<PERSON><PERSON><PERSON>", "convert csv to json", "csv-json"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": "MIT", "engines": {"node": ">=4.0.0"}, "devDependencies": {"@types/bluebird": "^3.5.20", "@types/mocha": "^5.2.0", "@types/node": "^10.0.1", "coveralls": "^3.0.1", "minimist": "^1.2.0", "mocha": "^5.1.1", "nyc": "^11.7.3", "sinon": "^3.2.3", "ts-node": "^6.0.3", "typescript": "^2.8.3"}, "dependencies": {"bluebird": "^3.5.1", "lodash": "^4.17.3", "strip-bom": "^2.0.0"}, "nyc": {"extension": [".ts", ".tsx"], "include": ["./src/**/*.ts"], "all": true}, "scripts": {"dev": "tsc -w", "build": "rm -Rf ./v2 && tsc", "test": "rm -Rf .ts-node && TS_NODE_CACHE_DIRECTORY=.ts-node mocha -r ts-node/register src/**/*.test.ts ./test/*.ts -R spec", "travis": "nyc --reporter lcov mocha -r ts-node/register src/**/*.test.ts ./test/*.ts -R spec", "test:debug": "mocha debug -r ts-node/register src/**/*.test.ts ./test/*.ts -R spec", "test-all": "mocha  ./test -R spec && CSV_WORKER=3 mocha ./test -R spec ", "test:unit": "mocha -r ts-node/register src/**/*.test.ts", "test:all:debug": "mocha debug ./testNew -R spec", "coverage": "nyc --reporter html  mocha -r ts-node/register src/**/*.test.ts ./test/*.ts -R spec", "coveralls": "cat ./coverage/lcov.info | ./node_modules/.bin/coveralls"}, "browser": {"child_process": false, "fs": false}, "main": "./v2/index.js", "gitHead": "8e444d154bb688d61b3552d3d55b7fc4a9d08a5a", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@2.0.4", "_shasum": "f2fee7cd5057f01acc1620c85f3cf4d19c9305b0", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.14.2", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "dist": {"shasum": "f2fee7cd5057f01acc1620c85f3cf4d19c9305b0", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-2.0.4.tgz", "fileCount": 388, "unpackedSize": 8593191, "integrity": "sha512-Ah<PERSON>++JKHBoOmrgPQ0syxYtZ4U4WmfsQ6wdjMIQQH4qKSD52PK+xG26Qs8s0K3GIPS67sJAJ/4/SiLOfU0ABYhw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGX/IpFwLGWE3unXcwiI0piOdL+7/LHZSU2ZcLh5ybA2AiEAxA0z7Ji/6BVqAJcglw3rtF+e4I6WNQibC0uJzN3FSPU="}]}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csvtojson_2.0.4_1529317556073_0.6566892415838583"}, "_hasShrinkwrap": false}, "2.0.6": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Keyang/node-csvtojson", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "git+https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/thegreatsunra"}, {"name": "<PERSON>", "url": "https://github.com/jessicagood"}, {"url": "https://github.com/nbelakovski"}, {"name": "<PERSON>", "url": "https://github.com/colarob"}, {"name": "<PERSON>", "url": "https://github.com/roodboi"}, {"name": "<PERSON>", "url": "https://github.com/JoseExposito"}, {"name": "<PERSON>", "url": "https://github.com/dcohenb"}, {"name": "<PERSON>", "url": "https://github.com/richardpringle"}, {"name": "<PERSON>", "url": "https://github.com/bertyhell"}, {"url": "https://github.com/jondayft"}, {"name": "<PERSON>", "url": "https://github.com/brucejo75"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Jimi<PERSON>ord"}, {"name": "<PERSON>", "url": "https://github.com/fenichelar"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Off76"}, {"name": "<PERSON>", "url": "https://github.com/blakeblackshear"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/amilajack"}, {"name": "Zsolt R<PERSON>", "url": "https://github.com/molnarzs"}, {"name": "Ionică Bizău", "url": "<PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kakts"}, {"url": "https://github.com/markwithers"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/trangtungn"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Keyang"}, {"name": "<PERSON>", "url": "https://github.com/jef<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/slang800"}, {"name": "<PERSON>", "url": "https://github.com/atufkas"}, {"name": "<PERSON>", "url": "https://github.com/ronkorving"}], "version": "2.0.6", "keywords": ["csv", "csv parser", "parse csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "csv to json", "csv convert", "<PERSON><PERSON><PERSON>", "convert csv to json", "csv-json"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": "MIT", "engines": {"node": ">=4.0.0"}, "devDependencies": {"@types/bluebird": "^3.5.20", "@types/mocha": "^5.2.0", "@types/node": "^10.0.1", "coveralls": "^3.0.1", "minimist": "^1.2.0", "mocha": "^5.1.1", "nyc": "^11.7.3", "sinon": "^3.2.3", "ts-node": "^6.0.3", "typescript": "^2.8.3"}, "dependencies": {"bluebird": "^3.5.1", "lodash": "^4.17.3", "strip-bom": "^2.0.0"}, "nyc": {"extension": [".ts", ".tsx"], "include": ["./src/**/*.ts"], "all": true}, "scripts": {"dev": "tsc -w", "build": "rm -Rf ./v2 && tsc", "test": "rm -Rf .ts-node && TS_NODE_CACHE_DIRECTORY=.ts-node mocha -r ts-node/register src/**/*.test.ts ./test/*.ts -R spec", "travis": "nyc --reporter lcov mocha -r ts-node/register src/**/*.test.ts ./test/*.ts -R spec", "test:debug": "mocha debug -r ts-node/register src/**/*.test.ts ./test/*.ts -R spec", "test:function": "mocha -r ts-node/register test/**/*.ts", "test-all": "mocha  ./test -R spec && CSV_WORKER=3 mocha ./test -R spec ", "test:unit": "mocha -r ts-node/register src/**/*.ts", "test:all:debug": "mocha debug ./testNew -R spec", "coverage": "nyc --reporter html  mocha -r ts-node/register src/**/*.test.ts ./test/*.ts -R spec", "coveralls": "cat ./coverage/lcov.info | ./node_modules/.bin/coveralls"}, "browser": {"child_process": false, "fs": false}, "main": "./v2/index.js", "gitHead": "2de58369d0744bb7d36eddf23013706965e6fcef", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@2.0.6", "_shasum": "75ebb770c4e6dac80364e445e7021116af288073", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.14.2", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "dist": {"shasum": "75ebb770c4e6dac80364e445e7021116af288073", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-2.0.6.tgz", "fileCount": 389, "unpackedSize": 8606158, "integrity": "sha512-7G5C7C8v6r7s2ju3Cd9dVsk8WVgnyCenSyOL4UxABXfC+03WLwuj9G85D5uOOQ88KaDo+0tUAB4yCo0B6NoK6A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDl+CFwCkNzAq6DG9c1wKx+C2ug3zCWmPXmDqdciUi5BQIhAJWukhk9ApZJfdP1NJI+GJambLpeRGpjZwIGqqGlt6H+"}]}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csvtojson_2.0.6_1531323839698_0.2794895686085286"}, "_hasShrinkwrap": false}, "2.0.7": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Keyang/node-csvtojson", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "git+https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/thegreatsunra"}, {"name": "<PERSON>", "url": "https://github.com/jessicagood"}, {"url": "https://github.com/nbelakovski"}, {"name": "<PERSON>", "url": "https://github.com/colarob"}, {"name": "<PERSON>", "url": "https://github.com/roodboi"}, {"name": "<PERSON>", "url": "https://github.com/JoseExposito"}, {"name": "<PERSON>", "url": "https://github.com/dcohenb"}, {"name": "<PERSON>", "url": "https://github.com/richardpringle"}, {"name": "<PERSON>", "url": "https://github.com/bertyhell"}, {"url": "https://github.com/jondayft"}, {"name": "<PERSON>", "url": "https://github.com/brucejo75"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Jimi<PERSON>ord"}, {"name": "<PERSON>", "url": "https://github.com/fenichelar"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Off76"}, {"name": "<PERSON>", "url": "https://github.com/blakeblackshear"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/amilajack"}, {"name": "Zsolt R<PERSON>", "url": "https://github.com/molnarzs"}, {"name": "Ionică Bizău", "url": "<PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kakts"}, {"url": "https://github.com/markwithers"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/trangtungn"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Keyang"}, {"name": "<PERSON>", "url": "https://github.com/jef<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/slang800"}, {"name": "<PERSON>", "url": "https://github.com/atufkas"}, {"name": "<PERSON>", "url": "https://github.com/ronkorving"}], "version": "2.0.7", "keywords": ["csv", "csv parser", "parse csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "csv to json", "csv convert", "<PERSON><PERSON><PERSON>", "convert csv to json", "csv-json"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": "MIT", "engines": {"node": ">=4.0.0"}, "devDependencies": {"@types/bluebird": "^3.5.20", "@types/mocha": "^5.2.0", "@types/node": "^10.0.1", "coveralls": "^3.0.1", "minimist": "^1.2.0", "mocha": "^5.1.1", "nyc": "^11.7.3", "sinon": "^3.2.3", "ts-node": "^6.0.3", "typescript": "^2.8.3"}, "dependencies": {"bluebird": "^3.5.1", "lodash": "^4.17.3", "readable-stream": "^2.3.6", "strip-bom": "^2.0.0"}, "nyc": {"extension": [".ts", ".tsx"], "include": ["./src/**/*.ts"], "all": true}, "scripts": {"dev": "tsc -w", "build": "rm -Rf ./v2 && tsc", "test": "rm -Rf .ts-node && TS_NODE_CACHE_DIRECTORY=.ts-node mocha -r ts-node/register src/**/*.test.ts ./test/*.ts -R spec", "travis": "nyc --reporter lcov mocha -r ts-node/register src/**/*.test.ts ./test/*.ts -R spec", "test:debug": "mocha debug -r ts-node/register src/**/*.test.ts ./test/*.ts -R spec", "test:function": "mocha -r ts-node/register test/**/*.ts", "test-all": "mocha  ./test -R spec && CSV_WORKER=3 mocha ./test -R spec ", "test:unit": "mocha -r ts-node/register src/**/*.ts", "test:all:debug": "mocha debug ./testNew -R spec", "coverage": "nyc --reporter html  mocha -r ts-node/register src/**/*.test.ts ./test/*.ts -R spec", "coveralls": "cat ./coverage/lcov.info | ./node_modules/.bin/coveralls"}, "browser": {"child_process": false, "fs": false}, "main": "./v2/index.js", "gitHead": "0e428a0f08fade5133fec7e88b5510d86550a346", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@2.0.7", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.2", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-UnYMLwOMY/+njjrut7KoolqPLxlhHpzYCEH89DbTuQlwSQnLomeBstWxe0mzZ6/gB65Yxr/2faeXv3g9lmYHfw==", "shasum": "0f55dd140559d7a61a98d6a4e6d81968ef1995f0", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-2.0.7.tgz", "fileCount": 388, "unpackedSize": 8314834, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbVs7fCRA9TVsSAnZWagAAeusP/i/PVlakuv8jGWCpL89D\nUWjsnl+f4+fX0/LgCb3SwRiTSnW9a/v6jI/sw+2rApZ1jlzug9kZ0ATxNftS\nbbC+Jf5+gQ17WED9Bn7DjgXhUydU5QjA3wWWwmIG8G2YeH1Ejtuvp26XA+Ol\nn81ZITcwJUHkARI0xixpSb1w1Lb3fiPvWouJxPrVZL23QTPbIm2kZms8A3yt\nvfHLyQfxTDX2xsGG+KKbzSHV55PQvzsaeAXmLU9RILrAK35//17N2HD3ZTuc\n7KdshGjwzXyn+9wDvjmwIXFf1ZVWj6lZ6b7vuixbbXcQCfU4Yoh2FX+D+Z8J\n4DQqLmxxr300LyYnK4afBMhlELfsvLzXF0jsNCf15wYyuz6f9iFyzsViKUr4\n4YVjNi2aLFDEs/fvQ/v91fyhj3FqCYVAl7MHHZcNixjtRxFf7bLs2q2c653O\nH5NhYgrljMRtNG6/tW6aFuK3rWZDz8eMsGdEtMRUg/Ncd/eY7dSZTuckrH8L\nu8GtRkLSVLRjVZmnNB2OvX0ANnAVFXxR44b7hYkOzar3XdedA0rKOjYCgq5C\nyuFHMEPJ8BBj2JlP4/2qg9LhK2UcfgKJrRRl6CoOdW1AnCksWiA+jYPjE2To\nd/IF1jaG+dF58Etw+quOx9u1Im8zlTdor7cnGnZVWpnYNqv1rEpBEXqVldGu\ntzmq\r\n=Mwdf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICTnFDkIB8LilWGa1+SFjqlP7Ha90z4wrq9gE60Cz3LKAiAKKVqQ6KHXx3yJcH0HhxoGNw46qMWQ4RD8QKAoFxFy/g=="}]}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csvtojson_2.0.7_1532415711003_0.620957910406561"}, "_hasShrinkwrap": false}, "2.0.8": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Keyang/node-csvtojson", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "git+https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/thegreatsunra"}, {"name": "<PERSON>", "url": "https://github.com/jessicagood"}, {"url": "https://github.com/nbelakovski"}, {"name": "<PERSON>", "url": "https://github.com/colarob"}, {"name": "<PERSON>", "url": "https://github.com/roodboi"}, {"name": "<PERSON>", "url": "https://github.com/JoseExposito"}, {"name": "<PERSON>", "url": "https://github.com/dcohenb"}, {"name": "<PERSON>", "url": "https://github.com/richardpringle"}, {"name": "<PERSON>", "url": "https://github.com/bertyhell"}, {"url": "https://github.com/jondayft"}, {"name": "<PERSON>", "url": "https://github.com/brucejo75"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Jimi<PERSON>ord"}, {"name": "<PERSON>", "url": "https://github.com/fenichelar"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Off76"}, {"name": "<PERSON>", "url": "https://github.com/blakeblackshear"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/amilajack"}, {"name": "Zsolt R<PERSON>", "url": "https://github.com/molnarzs"}, {"name": "Ionică Bizău", "url": "<PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kakts"}, {"url": "https://github.com/markwithers"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/trangtungn"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Keyang"}, {"name": "<PERSON>", "url": "https://github.com/jef<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/slang800"}, {"name": "<PERSON>", "url": "https://github.com/atufkas"}, {"name": "<PERSON>", "url": "https://github.com/ronkorving"}], "version": "2.0.8", "keywords": ["csv", "csv parser", "parse csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "csv to json", "csv convert", "<PERSON><PERSON><PERSON>", "convert csv to json", "csv-json"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": "MIT", "engines": {"node": ">=4.0.0"}, "devDependencies": {"@types/bluebird": "^3.5.20", "@types/mocha": "^5.2.0", "@types/node": "^10.0.1", "babel-plugin-syntax-dynamic-import": "^6.18.0", "coveralls": "^3.0.1", "minimist": "^1.2.0", "mocha": "^5.1.1", "nyc": "^11.7.3", "sinon": "^3.2.3", "ts-node": "^6.0.3", "typescript": "^2.8.3", "uglifyjs-webpack-plugin": "^1.2.7", "webpack": "^4.16.4", "webpack-cli": "^3.1.0"}, "dependencies": {"bluebird": "^3.5.1", "lodash": "^4.17.3", "strip-bom": "^2.0.0"}, "nyc": {"extension": [".ts", ".tsx"], "include": ["./src/**/*.ts"], "all": true}, "scripts": {"dev": "tsc -w", "build": "rm -Rf ./v2 && tsc && npm run build:browser", "build:browser": "webpack --config ./webpack.config.js", "test": "rm -Rf .ts-node && TS_NODE_CACHE_DIRECTORY=.ts-node mocha -r ts-node/register src/**/*.test.ts ./test/*.ts -R spec", "travis": "nyc --reporter lcov mocha -r ts-node/register src/**/*.test.ts ./test/*.ts -R spec", "test:debug": "mocha debug -r ts-node/register src/**/*.test.ts ./test/*.ts -R spec", "test:function": "mocha -r ts-node/register test/**/*.ts", "test-all": "mocha  ./test -R spec && CSV_WORKER=3 mocha ./test -R spec ", "test:unit": "mocha -r ts-node/register src/**/*.ts", "test:all:debug": "mocha debug ./testNew -R spec", "coverage": "nyc --reporter html  mocha -r ts-node/register src/**/*.test.ts ./test/*.ts -R spec", "coveralls": "cat ./coverage/lcov.info | ./node_modules/.bin/coveralls"}, "browser": "./browser/browser.js", "main": "./v2/index.js", "gitHead": "ff908866088d7f91eb994d4f97cf883da5948991", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@2.0.8", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.2", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-DC6YFtsJiA7t/Yz+KjzT6GXuKtU/5gRbbl7HJqvDVVir+dxdw2/1EgwfgJdnsvUT7lOnON5DvGftKuYWX1nMOQ==", "shasum": "d889f19576b2b33ead235490d2e5c9791481e8d3", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-2.0.8.tgz", "fileCount": 389, "unpackedSize": 8484180, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZF8HCRA9TVsSAnZWagAAzvcP/j/bx4CKB6DICY7R3bGL\nEg3uKxaXGR6Fj+AZ9mVOjYkpVGok7g2+UAtu72n1Rdx8P1esjmKUpVm2ZA/9\n6ZryREXHgnd2XH3enHPNxYU4JS4f46bodqCbn6tEydodP5nvxKJC1nG1wl64\nPE0WgcmvuMkZhRGox6UoL5ju3ssbIfU21jHtFwlE8G8AoQ8DmKfxNjLnBI9W\naWPnO1W0W6IBbe42ujPMmyOvl+ypSiqDKZhYs3GmxiBwvBNagkk7TNzVqejy\n4AUWQLrvN/OnJvTZRqV4iscm7WEdtlnq2B2A79J1qcN/5VEZUn0DyMroXNn2\nCSnZ+vD+eMz7oaZJJiEbiEd5AkY0dp5BguZDVQqYshkcy6LHpqXH3h+STBcJ\nD+tw6sPTwTxmpDh4j4noUa/hUvO5q7nyv5iSMSA7V/mc0XNF7Ep0CvlS/IfD\nrwAMFM8CVt8DvKgToAwULxt9lvhDszsHd24vgxztH0srVgvD2+NO1kU6vELo\nrztC27xNt/syeaDD23Urae5RnvWB0VTARjOEJdlSaWNuXQst/B61Ln4I1fWi\n3QYmLpxCKHbQ1XUWk3V//W3WOXZOqh8srlSvBMR9RKKpdt4TXJj2AaSsXmvm\nSfFLvCUIDd/8igSLMoqNl6yVjukAoDT3BMK/VFco5Avh1nAIB6SPQ9s45nS2\narAl\r\n=PbYt\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEMCIH75H7Xpjv+Pr+x5cdKMn2/egpX9pv80ioDpnxI7UUp9Ah8WLlPEQ8SuFMYsq+AvEskj1bq5pOJpZZKfg9Xu/Jrb"}]}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csvtojson_2.0.8_1533304582657_0.9899272635046785"}, "_hasShrinkwrap": false}, "2.0.10": {"name": "cs<PERSON><PERSON><PERSON><PERSON>", "description": "A tool concentrating on converting csv data to JSON with customised parser supporting", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Keyang/node-csvtojson", "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "repository": {"type": "git", "url": "git+https://github.com/Keyang/node-csvtojson.git"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/thegreatsunra"}, {"name": "<PERSON>", "url": "https://github.com/jessicagood"}, {"url": "https://github.com/nbelakovski"}, {"name": "<PERSON>", "url": "https://github.com/colarob"}, {"name": "<PERSON>", "url": "https://github.com/roodboi"}, {"name": "<PERSON>", "url": "https://github.com/JoseExposito"}, {"name": "<PERSON>", "url": "https://github.com/dcohenb"}, {"name": "<PERSON>", "url": "https://github.com/richardpringle"}, {"name": "<PERSON>", "url": "https://github.com/bertyhell"}, {"url": "https://github.com/jondayft"}, {"name": "<PERSON>", "url": "https://github.com/brucejo75"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Jimi<PERSON>ord"}, {"name": "<PERSON>", "url": "https://github.com/fenichelar"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Off76"}, {"name": "<PERSON>", "url": "https://github.com/blakeblackshear"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/amilajack"}, {"name": "Zsolt R<PERSON>", "url": "https://github.com/molnarzs"}, {"name": "Ionică Bizău", "url": "<PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kakts"}, {"url": "https://github.com/markwithers"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/trangtungn"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Keyang"}, {"name": "<PERSON>", "url": "https://github.com/jef<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/slang800"}, {"name": "<PERSON>", "url": "https://github.com/atufkas"}, {"name": "<PERSON>", "url": "https://github.com/ronkorving"}], "version": "2.0.10", "keywords": ["csv", "csv parser", "parse csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "csv to json", "csv convert", "<PERSON><PERSON><PERSON>", "convert csv to json", "csv-json"], "bin": {"csvtojson": "./bin/csvtojson"}, "license": "MIT", "engines": {"node": ">=4.0.0"}, "devDependencies": {"@types/bluebird": "^3.5.20", "@types/mocha": "^5.2.0", "@types/node": "^10.0.1", "babel-plugin-syntax-dynamic-import": "^6.18.0", "coveralls": "^3.0.1", "minimist": "^1.2.0", "mocha": "^5.1.1", "nyc": "^11.7.3", "sinon": "^3.2.3", "ts-node": "^6.0.3", "typescript": "^2.8.3", "uglifyjs-webpack-plugin": "^1.2.7", "webpack": "^4.16.4", "webpack-cli": "^3.1.0"}, "dependencies": {"bluebird": "^3.5.1", "lodash": "^4.17.3", "strip-bom": "^2.0.0"}, "nyc": {"extension": [".ts", ".tsx"], "include": ["./src/**/*.ts"], "all": true}, "scripts": {"dev": "tsc -w", "build": "rm -Rf ./v2 && tsc && npm run build:browser && npm run build:browser:window", "build:browser": "webpack --config ./webpack.config.js", "build:browser:window": "webpack --config ./webpack.config.js --output-library-target=window --output-library=csv --output-filename=csvtojson.min.js", "test": "rm -Rf .ts-node && TS_NODE_CACHE_DIRECTORY=.ts-node mocha -r ts-node/register src/**/*.test.ts ./test/*.ts -R spec", "travis": "nyc --reporter lcov mocha -r ts-node/register src/**/*.test.ts ./test/*.ts -R spec", "test:debug": "mocha debug -r ts-node/register src/**/*.test.ts ./test/*.ts -R spec", "test:function": "mocha -r ts-node/register test/**/*.ts", "test-all": "mocha  ./test -R spec && CSV_WORKER=3 mocha ./test -R spec ", "test:unit": "mocha -r ts-node/register src/**/*.ts", "test:all:debug": "mocha debug ./testNew -R spec", "coverage": "nyc --reporter html  mocha -r ts-node/register src/**/*.test.ts ./test/*.ts -R spec", "coveralls": "cat ./coverage/lcov.info | ./node_modules/.bin/coveralls"}, "browser": "./browser/browser.js", "main": "./v2/index.js", "gitHead": "abf5f59dce172d78adc3c62f4e9ff5b356e3565c", "_id": "cs<PERSON><PERSON><PERSON><PERSON>@2.0.10", "_npmVersion": "6.4.1", "_nodeVersion": "8.15.0", "_npmUser": {"name": "keyang", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-lUWFxGKyhraKCW8Qghz6Z0f2l/PqB1W3AO0HKJzGIQ5JRSlR651ekJDiGJbBT4sRNNv5ddnSGVEnsxP9XRCVpQ==", "shasum": "11e7242cc630da54efce7958a45f443210357574", "tarball": "https://registry.npmjs.org/csvtojson/-/csvtojson-2.0.10.tgz", "fileCount": 393, "unpackedSize": 8685360, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdE/eYCRA9TVsSAnZWagAAEy0P/jfydh0ugz06pxZKuIed\nCkVued8SLlcjOYHUTeaI+YD/IsVnaJ9yZI7dgrKMcclJwOkHVzCFg+xq8tiE\nhrG6UH2Hg5qD7StEHtgTQM0/xuRDcNxvVvOIdPEok6L+++eyZlQ5eg0q2Qoi\n1FlnW5YRPCiwUGtbPG9+rFopWOHIYmrXqiXR2gjJhunhthNp19sZ/No714Yy\nVW218RG9gRO44atpgXruFTetSzxueTObwL7b+7vU5t6WSNIl3MKUXn4AIjwM\na3lJUjRmsQCblQ+d4XHRM6EIHmBj57q65dKG/oXTpSBdvXYhdcMlgMELeuMS\nSHCWc81/s6xZYZHmYnsHN/wWOr/9rIZahILwQ3B2Txl0JtMf5Ux14ZFrhMjV\nVLvUorNK9SGxdfW3bXfYyHSb63oX+kjsYKmrTICrJmjZCN9apPc5j9TH52C+\nvPFVQs1koRT+vBj4ul9pvPuOj36YWs3/eEFw5a5CiPYvCFaNVUwd8Gh7b3vy\nWNhGJb6pvhp963JGj1o1MKc9FVYWcrC+dUdEpg+4H8XOqBc90/aI2eV5HJXF\nSs2/cT25LaCNr7HDHYeRcrsrujB1DswjqXLaQgbC83NJu/KEkoZ3pDvcRe6+\n62bfQ+BjNXHm/XJTLJoHTWvPe5f6L+yB5TtYgS5vXq7/m9iSfGoDH1Q2TRu+\nSjbK\r\n=Tsvs\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCabde/4DewFZGswQ+Y8zDzxFLW3pPLJdgxiNEx8yojgAIhAP72cDbftdvsEa9BnKXgn/r0gpXLrIbdgeA28wmyvBTl"}]}, "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/csvtojson_2.0.10_1561589655455_0.5651841098256085"}, "_hasShrinkwrap": false}}, "readme": "[![Build Status](https://travis-ci.org/Keyang/node-csvtojson.svg?branch=master)](https://travis-ci.org/Keyang/node-csvtojson)\n[![Coverage Status](https://coveralls.io/repos/github/Keyang/node-csvtojson/badge.svg?branch=master)](https://coveralls.io/github/Keyang/node-csvtojson?branch=master)\n[![OpenCollective](https://opencollective.com/csvtojson/backers/badge.svg)](#backers) \n[![OpenCollective](https://opencollective.com/csvtojson/sponsors/badge.svg)](#sponsors)\n\n# CSVTOJSON\n\n`csvtojson` module is a comprehensive nodejs csv parser to convert csv to json or column arrays. It can be used as node.js library / command line tool / or in browser. Below are some features:\n\n*  Strictly follow CSV definition [RF4180](https://www.loc.gov/preservation/digital/formats/fdd/fdd000323.shtml)\n*  Work with millions of lines of CSV data\n*  Provide comprehensive parsing parameters\n*  Provide out of box CSV parsing tool for Command Line\n*  Blazing fast -- [Focus on performance](https://github.com/Keyang/csvbench)\n*  Give flexibility to developer with 'pre-defined' helpers\n*  Allow async / streaming parsing\n*  Provide a csv parser for both Node.JS and browsers\n*  Easy to use API\n\n\n# csvtojson online \n\n[Here](http://keyangxiang.com/csvtojson/) is a free online csv to json convert service utilizing latest `csvtojson` module.\n\n# Upgrade to V2\n\n`csvtojson` has released version `2.0.0`. \n* To upgrade to v2, please follow [upgrading guide](https://github.com/Keyang/node-csvtojson/blob/master/docs/csvtojson-v2.md)\n* If you are looking for documentation for `v1`, open [this page](https://github.com/Keyang/node-csvtojson/blob/master/docs/readme.v1.md)\n\nIt is still able to use v1 with `csvtojson@2.0.0`\n\n```js\n// v1\nconst csvtojsonV1=require(\"csvtojson/v1\");\n// v2\nconst csvtojsonV2=require(\"csvtojson\");\nconst csvtojsonV2=require(\"csvtojson/v2\");\n\n```\n\n# Menu\n\n* [Quick Start](#quick-start)\n* [API](#api)\n* [Browser Usage](#browser-usage)\n* [Contribution](#contribution)\n\n# Quick Start\n\n* [As Library](#library)\n* [As Command Line Tool](#command-line-usage)\n\n## Library\n\n### Installation\n\n```\nnpm i --save csvtojson\n```\n\n### From CSV File to JSON Array\n\n```js\n/** csv file\na,b,c\n1,2,3\n4,5,6\n*/\nconst csvFilePath='<path to csv file>'\nconst csv=require('csvtojson')\ncsv()\n.fromFile(csvFilePath)\n.then((jsonObj)=>{\n\tconsole.log(jsonObj);\n\t/**\n\t * [\n\t * \t{a:\"1\", b:\"2\", c:\"3\"},\n\t * \t{a:\"4\", b:\"5\". c:\"6\"}\n\t * ]\n\t */ \n})\n\n// Async / await usage\nconst jsonArray=await csv().fromFile(csvFilePath);\n\n```\n\n### From CSV String to CSV Row\n\n```js\n/**\ncsvStr:\n1,2,3\n4,5,6\n7,8,9\n*/\nconst csv=require('csvtojson')\ncsv({\n\tnoheader:true,\n\toutput: \"csv\"\n})\n.fromString(csvStr)\n.then((csvRow)=>{ \n\tconsole.log(csvRow) // => [[\"1\",\"2\",\"3\"], [\"4\",\"5\",\"6\"], [\"7\",\"8\",\"9\"]]\n})\n\n```\n\n\n### Asynchronously process each line from csv url\n\n```js\nconst request=require('request')\nconst csv=require('csvtojson')\n\ncsv()\n.fromStream(request.get('http://mywebsite.com/mycsvfile.csv'))\n.subscribe((json)=>{\n\treturn new Promise((resolve,reject)=>{\n\t\t// long operation for each json e.g. transform / write into database.\n\t})\n},onError,onComplete);\n\n```\n\n### Convert to CSV lines\n\n```js\n/**\ncsvStr:\na,b,c\n1,2,3\n4,5,6\n*/\n\nconst csv=require('csvtojson')\ncsv({output:\"line\"})\n.fromString(csvStr)\n.subscribe((csvLine)=>{ \n\t// csvLine =>  \"1,2,3\" and \"4,5,6\"\n})\n```\n\n### Use Stream\n\n```js\nconst csv=require('csvtojson');\n\nconst readStream=require('fs').createReadStream(csvFilePath);\n\nconst writeStream=request.put('http://mysite.com/obj.json');\n\nreadStream.pipe(csv()).pipe(writeStream);\n\n```\n\nTo find more detailed usage, please see [API](#api) section\n\n## Command Line Usage\n\n### Installation\n\n```\n$ npm i -g csvtojson\n```\n\n### Usage\n\n\n```\n$ csvtojson [options] <csv file path>\n```\n\n### Example\n\nConvert csv file and save result to json file:\n\n```\n$ csvtojson source.csv > converted.json\n```\n\nPipe in csv data:\n\n```\n$ cat ./source.csv | csvtojson > converted.json\n```\n\nPrint Help:\n\n```\n$ csvtojson\n```\n\n# API\n\n* [Parameters](#parameters)\n* [Asynchronous Result Process](#asynchronous-result-process)\n* [Events](#events)\n* [Hook / Transform](#hook--transform)\n* [Nested JSON Structure](#nested-json-structure)\n* [Header Row](#header-row)\n* [Column Parser](#column-parser)\n\n\n## Parameters\n\n`require('csvtojson')` returns a constructor function which takes 2 arguments:\n\n1. Parser parameters\n2. Stream options\n\n```js\nconst csv=require('csvtojson')\nconst converter=csv(parserParameters, streamOptions)\n```\nBoth arguments are optional.\n\nFor `Stream Options` please read [Stream Option](https://nodejs.org/api/stream.html#stream_new_stream_transform_options) from Node.JS\n\n`parserParameters` is a JSON object like:\n\n```js\nconst converter=csv({\n\tnoheader:true,\n\ttrim:true,\n})\n```\nFollowing parameters are supported:\n\n* **output**: The format to be converted to. \"json\" (default) -- convert csv to json. \"csv\" -- convert csv to csv row array. \"line\" -- convert csv to csv line string  \n* **delimiter**: delimiter used for separating columns. Use \"auto\" if delimiter is unknown in advance, in this case, delimiter will be auto-detected (by best attempt). Use an array to give a list of potential delimiters e.g. [\",\",\"|\",\"$\"]. default: \",\"\n* **quote**: If a column contains delimiter, it is able to use quote character to surround the column content. e.g. \"hello, world\" won't be split into two columns while parsing. Set to \"off\" will ignore all quotes. default: \" (double quote)\n* **trim**: Indicate if parser trim off spaces surrounding column content. e.g. \"  content  \" will be trimmed to \"content\". Default: true\n* **checkType**: This parameter turns on and off whether check field type. Default is false. (The default is `true` if version < 1.1.4)\n* **ignoreEmpty**: Ignore the empty value in CSV columns. If a column value is not given, set this to true to skip them. Default: false.\n* **fork (experimental)**: Fork another process to parse the CSV stream. It is effective if many concurrent parsing sessions for large csv files. Default: false\n* **noheader**:Indicating csv data has no header row and first row is data row. Default is false. See [header row](#header-row)\n* **headers**: An array to specify the headers of CSV data. If --noheader is false, this value will override CSV header row. Default: null. Example: [\"my field\",\"name\"]. See [header row](#header-row)\n* **flatKeys**: Don't interpret dots (.) and square brackets in header fields as nested object or array identifiers at all (treat them like regular characters for JSON field identifiers). Default: false.\n* **maxRowLength**: the max character a csv row could have. 0 means infinite. If max number exceeded, parser will emit \"error\" of \"row_exceed\". if a possibly corrupted csv data provided, give it a number like 65535 so the parser won't consume memory. default: 0\n* **checkColumn**: whether check column number of a row is the same as headers. If column number mismatched headers number, an error of \"mismatched_column\" will be emitted.. default: false\n* **eol**: End of line character. If omitted, parser will attempt to retrieve it from the first chunks of CSV data.\n* **escape**: escape character used in quoted column. Default is double quote (\") according to RFC4108. Change to back slash (\\\\) or other chars for your own case.\n* **includeColumns**: This parameter instructs the parser to include only those columns as specified by the regular expression. Example: /(name|age)/ will parse and include columns whose header contains \"name\" or \"age\"\n* **ignoreColumns**: This parameter instructs the parser to ignore columns as specified by the regular expression. Example: /(name|age)/ will ignore columns whose header contains \"name\" or \"age\"\n* **colParser**: Allows override parsing logic for a specific column. It accepts a JSON object with fields like: `headName: <String | Function | ColParser>` . e.g. {field1:'number'} will use built-in number parser to convert value of the `field1` column to number. For more information See [details below](#column-parser)\n* **alwaysSplitAtEOL**: Always interpret each line (as defined by `eol` like `\\n`) as a row. This will prevent `eol` characters from being used within a row (even inside a quoted field). Default is false. Change to true if you are confident no inline line breaks (like line break in a cell which has multi line text).\n* **nullObject**: How to parse if a csv cell contains \"null\". Default false will keep \"null\" as string. Change to true if a null object is needed.\n* **downstreamFormat**: Option to set what JSON array format is needed by downstream. \"line\" is also called ndjson format. This format will write lines of JSON (without square brackets and commas) to downstream. \"array\" will write complete JSON array string to downstream (suitable for file writable stream etc). Default \"line\"\n* **needEmitAll**: Parser will build JSON result is `.then` is called (or await is used). If this is not desired, set this to false. Default is true. \nAll parameters can be used in Command Line tool.\n\n## Asynchronous Result Process\n\nSince `v2.0.0`, asynchronous processing has been fully supported.\n\ne.g. Process each JSON result asynchronously.\n\n```js\ncsv().fromFile(csvFile)\n.subscribe((json)=>{\n\treturn new Promise((resolve,reject)=>{\n\t\t// Async operation on the json\n\t\t// don't forget to call resolve and reject\n\t})\n})\n```\nFor more details please read:\n\n* [Add Promise and Async / Await support](https://github.com/Keyang/node-csvtojson/blob/master/docs/csvtojson-v2.md#add-promise-and-async--await-support)\n* [Add asynchronous line by line processing support](https://github.com/Keyang/node-csvtojson/blob/master/docs/csvtojson-v2.md#add-asynchronous-line-by-line-processing-support)\n* [Async Hooks Support](https://github.com/Keyang/node-csvtojson/blob/master/docs/csvtojson-v2.md#async-hooks-support)\n\n\n## Events\n\n`Converter` class defined a series of events.\n\n### header\n\n`header` event is emitted for each CSV file once. It passes an array object which contains the names of the header row.\n\n```js\nconst csv=require('csvtojson')\ncsv()\n.on('header',(header)=>{\n\t//header=> [header1, header2, header3]\n})\n```\n\n`header` is always an array of strings without types.\n\n### data\n\n`data` event is emitted for each parsed CSV line. It passes buffer of stringified JSON in [ndjson format](http://ndjson.org/) unless `objectMode` is set true in stream option.\n\n```js\nconst csv=require('csvtojson')\ncsv()\n.on('data',(data)=>{\n\t//data is a buffer object\n\tconst jsonStr= data.toString('utf8')\n})\n```\n\n### error\n`error` event is emitted if any errors happened during parsing.\n\n```js\nconst csv=require('csvtojson')\ncsv()\n.on('error',(err)=>{\n\tconsole.log(err)\n})\n```\n\nNote that if `error` being emitted, the process will stop as node.js will automatically `unpipe()` upper-stream and chained down-stream<sup>1</sup>. This will cause `end` event never being emitted because `end` event is only emitted when all data being consumed <sup>2</sup>. If need to know when parsing finished, use `done` event instead of `end`.\n\n1. [Node.JS Readable Stream](https://github.com/nodejs/node/blob/master/lib/_stream_readable.js#L572-L583)\n2. [Writable end Event](https://nodejs.org/api/stream.html#stream_event_end)\n\n### done\n\n`done` event is emitted either after parsing successfully finished or any error happens. This indicates the processor has stopped.\n\n```js\nconst csv=require('csvtojson')\ncsv()\n.on('done',(error)=>{\n\t//do some stuff\n})\n```\n\nif any error during parsing, it will be passed in callback.\n\n## Hook & Transform\n\n### Raw CSV Data Hook\n\nthe hook -- `preRawData` will be called with csv string passed to parser.\n\n```js\nconst csv=require('csvtojson')\n// synchronouse\ncsv()\n.preRawData((csvRawData)=>{\n\tvar newData=csvRawData.replace('some value','another value');\n\treturn newData;\n})\n\n// asynchronous\ncsv()\n.preRawData((csvRawData)=>{\n\treturn new Promise((resolve,reject)=>{\n\t\tvar newData=csvRawData.replace('some value','another value');\n\t\tresolve(newData);\n\t})\n\t\n})\n```\n\n### CSV File Line Hook\n\nThe function is called each time a file line has been parsed in csv stream. The `lineIdx` is the file line number in the file starting with 0. \n\n```js\nconst csv=require('csvtojson')\n// synchronouse\ncsv()\n.preFileLine((fileLineString, lineIdx)=>{\n\tif (lineIdx === 2){\n\t\treturn fileLineString.replace('some value','another value')\n\t}\n\treturn fileLineString\n})\n\n// asynchronous\ncsv()\n.preFileLine((fileLineString, lineIdx)=>{\n\treturn new Promise((resolve,reject)=>{\n\t\t\t// async function processing the data.\n\t})\n\t\n\t\n})\n```\n\n\n\n### Result transform\n\nTo transform result that is sent to downstream, use `.subscribe` method for each json populated.\n\n```js\nconst csv=require('csvtojson')\ncsv()\n.subscribe((jsonObj,index)=>{\n\tjsonObj.myNewKey='some value'\n\t// OR asynchronously\n\treturn new Promise((resolve,reject)=>{\n\t\tjsonObj.myNewKey='some value';\n\t\tresolve();\n\t})\n})\n.on('data',(jsonObj)=>{\n\tconsole.log(jsonObj.myNewKey) // some value\n});\n```\n\n\n## Nested JSON Structure\n\n`csvtojson` is able to convert csv line to a nested JSON by correctly defining its csv header row. This is default out-of-box feature.\n\nHere is an example. Original CSV:\n\n```csv\nfieldA.title, fieldA.children.0.name, fieldA.children.0.id,fieldA.children.1.name, fieldA.children.1.employee.0.name,fieldA.children.1.employee.1.name, fieldA.address.0,fieldA.address.1, description\nFood Factory, Oscar, 0023, Tikka, Tim, Joe, 3 Lame Road, Grantstown, A fresh new food factory\nKindom Garden, Ceil, 54, Pillow, Amst, Tom, 24 Shaker Street, HelloTown, Awesome castle\n\n```\nThe data above contains nested JSON including nested array of JSON objects and plain texts.\n\nUsing csvtojson to convert, the result would be like:\n\n```json\n[{\n    \"fieldA\": {\n        \"title\": \"Food Factory\",\n        \"children\": [{\n            \"name\": \"Oscar\",\n            \"id\": \"0023\"\n        }, {\n            \"name\": \"Tikka\",\n            \"employee\": [{\n                \"name\": \"Tim\"\n            }, {\n                \"name\": \"Joe\"\n            }]\n        }],\n        \"address\": [\"3 Lame Road\", \"Grantstown\"]\n    },\n    \"description\": \"A fresh new food factory\"\n}, {\n    \"fieldA\": {\n        \"title\": \"Kindom Garden\",\n        \"children\": [{\n            \"name\": \"Ceil\",\n            \"id\": \"54\"\n        }, {\n            \"name\": \"Pillow\",\n            \"employee\": [{\n                \"name\": \"Amst\"\n            }, {\n                \"name\": \"Tom\"\n            }]\n        }],\n        \"address\": [\"24 Shaker Street\", \"HelloTown\"]\n    },\n    \"description\": \"Awesome castle\"\n}]\n```\n\n### Flat Keys\n\nIn order to not produce nested JSON, simply set `flatKeys:true` in parameters.\n\n```js\n/**\ncsvStr:\na.b,a.c\n1,2\n*/\ncsv({flatKeys:true})\n.fromString(csvStr)\n.subscribe((jsonObj)=>{\n\t//{\"a.b\":1,\"a.c\":2}  rather than  {\"a\":{\"b\":1,\"c\":2}}\n});\n\n```\n\n## Header Row\n\n`csvtojson` uses csv header row as generator of JSON keys. However, it does not require the csv source containing a header row. There are 4 ways to define header rows:\n\n1. First row of csv source. Use first row of csv source as header row. This is default.\n2. If first row of csv source is header row but it is incorrect and need to be replaced. Use `headers:[]` and `noheader:false` parameters.\n3. If original csv source has no header row but the header definition can be defined. Use `headers:[]` and `noheader:true` parameters.\n4. If original csv source has no header row and the header definition is unknown. Use `noheader:true`. This will automatically add `fieldN` header to csv cells\n\n\n### Example\n\n```js\n// replace header row (first row) from original source with 'header1, header2'\ncsv({\n\tnoheader: false,\n\theaders: ['header1','header2']\n})\n\n// original source has no header row. add 'field1' 'field2' ... 'fieldN' as csv header\ncsv({\n\tnoheader: true\n})\n\n// original source has no header row. use 'header1' 'header2' as its header row\ncsv({\n\tnoheader: true,\n\theaders: ['header1','header2']\n})\n\n```\n\n## Column Parser\n\n`Column Parser` allows writing a custom parser for a column in CSV data. \n\n**What is Column Parser**\n\nWhen `csvtojson` walks through csv data, it converts value in a cell to something else. For example, if `checkType` is `true`, `csvtojson` will attempt to find a proper type parser according to the cell value. That is, if cell value is \"5\", a `numberParser` will be used and all value under that column will use the `numberParser` to transform data.\n\n### Built-in parsers\n\nThere are currently following built-in parser:\n\n* string: Convert value to string\n* number: Convert value to number\n* omit: omit the whole column\n\nThis will override types infered from `checkType:true` parameter. More built-in parsers will be added as requested in [issues page](https://github.com/Keyang/node-csvtojson/issues).\n\nExample:\n\n```js\n/*csv string\ncolumn1,column2\nhello,1234\n*/\ncsv({\n\tcolParser:{\n\t\t\"column1\":\"omit\",\n\t\t\"column2\":\"string\",\n\t},\n\tcheckType:true\n})\n.fromString(csvString)\n.subscribe((jsonObj)=>{\n\t//jsonObj: {column2:\"1234\"}\n})\n```\n\n### Custom parsers function\n\nSometimes, developers want to define custom parser. It is able to pass a function to specific column in `colParser`.\n\nExample:\n\n```js\n/*csv data\nname, birthday\nJoe, 1970-01-01\n*/\ncsv({\n\tcolParser:{\n\t\t\"birthday\":function(item, head, resultRow, row , colIdx){\n\t\t\t/*\n\t\t\t\titem - \"1970-01-01\"\n\t\t\t\thead - \"birthday\"\n\t\t\t\tresultRow - {name:\"Joe\"}\n\t\t\t\trow - [\"Joe\",\"1970-01-01\"]\n\t\t\t\tcolIdx - 1\n\t\t\t*/\n\t\t\treturn new Date(item);\n\t\t}\n\t}\n})\n```\n\nAbove example will convert `birthday` column into a js `Date` object.\n\nThe returned value will be used in result JSON object. Returning `undefined` will not change result JSON object. \n\n### Flat key column\n\nIt is also able to mark a column as `flat`:\n\n```js\n\n/*csv string\nperson.comment,person.number\nhello,1234\n*/\ncsv({\n\tcolParser:{\n\t\t\"person.number\":{\n\t\t\tflat:true,\n\t\t\tcellParser: \"number\" // string or a function \n\t\t}\n\t}\n})\n.fromString(csvString)\n.subscribe((jsonObj)=>{\n\t//jsonObj: {\"person.number\":1234,\"person\":{\"comment\":\"hello\"}}\n})\n```\n\n# Contribution\n\nVery much appreciate any types of donation and support. \n\n## Code\n\n`csvtojson` follows github convention for contributions. Here are some steps:\n\n1. Fork the repo to your github account\n2. Checkout code from your github repo to your local machine.\n3. Make code changes and don't forget add related tests.\n4. Run `npm test` locally before pushing code back.\n5. Create a [Pull Request](https://help.github.com/articles/creating-a-pull-request/) on github.\n6. Code review and merge\n7. Changes will be published to NPM within next version.\n\nThanks all the [contributors](https://github.com/Keyang/node-csvtojson/graphs/contributors)\n\n## Backers\n\nThank you to all our backers! [[Become a backer](https://opencollective.com/csvtojson#backer)]\n\n[![OpenCollective](https://opencollective.com/csvtojson/backers.svg?width=890)](https://opencollective.com/csvtojson#backer)\n\n## Sponsors\n\nThank you to all our sponsors! (please ask your company to also support this open source project by [becoming a sponsor](https://opencollective.com/csvtojson#sponsor))\n\n## Paypal \n\n[![donate](https://www.paypalobjects.com/en_US/i/btn/btn_donate_SM.gif)](https://www.paypal.com/cgi-bin/webscr?cmd=_s-xclick&hosted_button_id=DUBQLRPJADJFQ)\n\n# Browser Usage\n\nTo use `csvtojson` in browser is quite simple. There are two ways:\n\n**1. Embed script directly into script tag**\n\nThere is a pre-built script located in `browser/csvtojson.min.js`. Simply include that file in a `script` tag in `index.html` page:\n\n```html\n<script src=\"node_modules/csvtojson/browser/csvtojson.min.js\"></script>\n<!-- or use cdn -->\n<script src=\"https://cdn.rawgit.com/Keyang/node-csvtojson/d41f44aa/browser/csvtojson.min.js\"></script>\n```\nthen use a global `csv` function\n```html \n<script>\ncsv({\n\toutput: \"csv\"\n})\n.fromString(\"a,b,c\\n1,2,3\")\n.then(function(result){\n\n})\n</script>\n```\n\n\n\n**2. Use webpack or browserify**\n\nIf a module packager is preferred, just simply `require(\"csvtojson\")`:\n\n```js\nvar csv=require(\"csvtojson\");\n\n// or with import\nimport * as csv from \"csvtojson\";\n\n//then use csv as normal\n```\n", "maintainers": [{"name": "keyang", "email": "<EMAIL>"}], "time": {"modified": "2022-06-14T05:23:29.721Z", "created": "2013-06-17T18:33:45.139Z", "0.1.0": "2013-06-17T18:33:46.939Z", "0.1.1": "2013-06-18T16:55:27.398Z", "0.1.2": "2013-06-19T13:09:56.621Z", "0.1.3": "2013-07-17T11:27:22.884Z", "0.1.4": "2013-07-30T20:41:17.183Z", "0.1.5": "2013-11-10T22:55:48.618Z", "0.1.6": "2014-02-03T21:17:07.584Z", "0.1.7": "2014-02-04T12:09:10.440Z", "0.2.0": "2014-03-19T10:18:35.555Z", "0.2.1": "2014-03-19T10:22:06.985Z", "0.2.2": "2014-03-19T11:41:26.125Z", "0.3.0": "2014-04-18T12:49:38.768Z", "0.3.1": "2014-04-18T13:07:59.993Z", "0.3.2": "2014-04-20T23:44:20.892Z", "0.3.3": "2014-04-21T10:59:32.834Z", "0.3.4": "2014-04-23T20:04:31.046Z", "0.3.5": "2014-04-29T21:06:41.238Z", "0.3.6": "2014-05-17T14:29:15.702Z", "0.3.7": "2014-05-18T13:38:56.722Z", "0.3.8": "2014-06-04T20:02:19.821Z", "0.3.9": "2014-06-10T23:21:49.711Z", "0.3.10": "2014-06-15T20:12:14.025Z", "0.3.11": "2014-06-18T21:51:15.942Z", "0.3.12": "2014-07-24T09:45:15.652Z", "0.3.13": "2014-08-13T19:26:44.098Z", "0.3.14": "2014-10-24T19:06:50.823Z", "0.3.15": "2014-11-06T17:44:57.309Z", "0.3.16": "2014-11-06T18:04:00.644Z", "0.3.17": "2014-11-27T12:18:08.670Z", "0.3.18": "2015-02-10T19:13:46.723Z", "0.3.19": "2015-03-10T21:37:34.441Z", "0.3.20": "2015-04-06T12:20:55.341Z", "0.3.21": "2015-04-08T21:32:50.807Z", "0.4.0": "2015-06-30T21:14:31.929Z", "0.4.1": "2015-07-20T10:14:14.009Z", "0.4.2": "2015-08-06T15:03:15.589Z", "0.4.3": "2015-09-23T21:25:11.257Z", "0.4.4": "2015-11-02T14:31:56.932Z", "0.4.5": "2015-11-03T11:33:42.729Z", "0.4.6": "2015-12-02T22:51:16.384Z", "0.4.7": "2015-12-10T15:18:08.007Z", "0.4.8": "2015-12-12T21:00:49.493Z", "0.5.0": "2015-12-18T23:32:20.894Z", "0.5.1": "2016-01-05T04:26:43.808Z", "0.5.2": "2016-02-01T22:13:33.555Z", "0.5.3": "2016-03-29T20:02:01.402Z", "0.5.4": "2016-04-30T00:20:54.899Z", "0.5.5": "2016-05-02T17:46:00.138Z", "0.5.6": "2016-05-05T21:08:21.097Z", "0.5.7": "2016-05-09T06:07:10.885Z", "0.5.8": "2016-05-09T12:00:11.396Z", "0.5.9": "2016-05-09T23:09:47.993Z", "0.5.10": "2016-05-10T09:43:27.346Z", "0.5.11": "2016-05-16T06:34:14.834Z", "0.5.12": "2016-05-18T08:46:18.871Z", "0.5.13": "2016-06-02T16:15:18.893Z", "0.5.14": "2016-06-02T16:30:46.575Z", "1.0.0": "2016-06-23T13:35:23.905Z", "1.0.1": "2016-09-11T15:05:23.901Z", "1.0.2": "2016-09-12T08:16:20.765Z", "1.0.3": "2016-10-07T11:51:24.008Z", "1.1.0": "2016-12-24T21:33:37.374Z", "1.1.1": "2016-12-30T19:24:05.082Z", "1.1.2": "2017-01-05T23:03:32.116Z", "1.1.3": "2017-01-23T21:35:50.192Z", "1.1.4": "2017-02-03T18:08:49.081Z", "1.1.5": "2017-05-03T18:49:36.146Z", "1.1.6": "2017-05-26T12:32:32.982Z", "1.1.7": "2017-06-20T21:29:06.374Z", "1.1.8": "2017-09-30T19:15:02.276Z", "1.1.9": "2017-10-07T00:02:09.147Z", "1.1.10": "2018-05-08T11:16:07.515Z", "1.1.11": "2018-05-08T11:50:45.053Z", "1.1.12": "2018-05-08T20:17:38.935Z", "2.0.0": "2018-05-20T20:31:23.595Z", "2.0.1": "2018-05-29T14:26:11.411Z", "2.0.2": "2018-05-29T14:27:42.488Z", "2.0.3": "2018-05-29T14:31:21.679Z", "2.0.4": "2018-06-18T10:25:56.356Z", "2.0.5": "2018-07-11T15:12:41.195Z", "2.0.6": "2018-07-11T15:43:59.850Z", "2.0.7": "2018-07-24T07:01:51.182Z", "2.0.8": "2018-08-03T13:56:22.759Z", "2.0.9": "2019-06-26T22:38:00.453Z", "2.0.10": "2019-06-26T22:54:15.731Z"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/Keyang/node-csvtojson.git"}, "readmeFilename": "readme.md", "homepage": "https://github.com/Keyang/node-csvtojson", "keywords": ["csv", "csv parser", "parse csv", "cs<PERSON><PERSON><PERSON><PERSON>", "json", "csv to json", "csv convert", "<PERSON><PERSON><PERSON>", "convert csv to json", "csv-json"], "contributors": [{"name": "<PERSON>", "url": "https://github.com/thegreatsunra"}, {"name": "<PERSON>", "url": "https://github.com/jessicagood"}, {"url": "https://github.com/nbelakovski"}, {"name": "<PERSON>", "url": "https://github.com/colarob"}, {"name": "<PERSON>", "url": "https://github.com/roodboi"}, {"name": "<PERSON>", "url": "https://github.com/JoseExposito"}, {"name": "<PERSON>", "url": "https://github.com/dcohenb"}, {"name": "<PERSON>", "url": "https://github.com/richardpringle"}, {"name": "<PERSON>", "url": "https://github.com/bertyhell"}, {"url": "https://github.com/jondayft"}, {"name": "<PERSON>", "url": "https://github.com/brucejo75"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Jimi<PERSON>ord"}, {"name": "<PERSON>", "url": "https://github.com/fenichelar"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Off76"}, {"name": "<PERSON>", "url": "https://github.com/blakeblackshear"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/amilajack"}, {"name": "Zsolt R<PERSON>", "url": "https://github.com/molnarzs"}, {"name": "Ionică Bizău", "url": "<PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kakts"}, {"url": "https://github.com/markwithers"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/trangtungn"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Keyang"}, {"name": "<PERSON>", "url": "https://github.com/jef<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/slang800"}, {"name": "<PERSON>", "url": "https://github.com/atufkas"}, {"name": "<PERSON>", "url": "https://github.com/ronkorving"}], "bugs": {"url": "https://github.com/Keyang/node-csvtojson/issues"}, "license": "MIT", "users": {"pawerda": true, "rakeshkatti": true, "tann81": true, "tobybellwood": true, "colepacak": true, "ahmedfarooki": true, "ryuchihoon": true, "whatsamoorefor": true, "devg": true, "jehoshua02": true, "lwhiteley": true, "jmharrington11": true, "jacobswain": true, "jonnyirving": true, "cjwilburn": true, "brandiatmuhkuh": true, "elussich": true, "pruettti": true, "koulmomo": true, "tkdn": true, "jeltok": true, "existenzial": true, "thamkrish": true, "ymk": true, "joaquin.briceno": true, "isaacvitor": true, "tdreitz": true, "mingzhangyang": true, "gerst20051": true, "hibrahimsafak": true, "padurets": true, "w01fgang": true, "rkopylkov": true, "ivan.marquez": true, "conzi": true, "alin.alexa": true, "pcooney10": true, "rocket0191": true, "dralc": true, "sako73": true, "gujarabh": true, "coolhanddev": true, "michaelermer": true, "gavatron": true, "vud00": true, "alvis": true, "kent1": true, "restuta": true, "dpjayasekara": true, "davidfg": true, "yfujimot": true, "thangakumar": true, "vchouhan": true, "thomasleveil": true, "appastair": true, "tomoko": true, "sdgcwoods": true, "rogeriotadim": true, "alaindresse": true, "judlup": true, "kaycee": true, "hkb06542": true}}