{"_id": "is-set", "_rev": "12-084abd9946ad403973f092295cc6dc08", "name": "is-set", "description": "Is this value a JS Set? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "dist-tags": {"latest": "2.0.3"}, "versions": {"1.0.0": {"name": "is-set", "version": "1.0.0", "description": "Easily check if a specified object is an ES6 Set", "keywords": ["set", "sets", "typeof", "type checking", "check", "es6", "harmony"], "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "files": ["index.js"], "repository": {"type": "git", "url": "https://github.com/arthurvr/is-set"}, "scripts": {"test": "node test/test.js"}, "devDependencies": {"ava": "^0.0.4"}, "engines": {"node": ">=0.12.0"}, "gitHead": "116e901813c75fd25e724a88fa7b24ec7d8045d2", "bugs": {"url": "https://github.com/arthurvr/is-set/issues"}, "homepage": "https://github.com/arthurvr/is-set", "_id": "is-set@1.0.0", "_shasum": "da5f93e39f083bbb4efd2ebf9528aed87ae70028", "_from": ".", "_npmVersion": "2.4.1", "_nodeVersion": "1.1.0", "_npmUser": {"name": "art<PERSON><PERSON>r", "email": "<EMAIL>"}, "maintainers": [{"name": "art<PERSON><PERSON>r", "email": "<EMAIL>"}], "dist": {"shasum": "da5f93e39f083bbb4efd2ebf9528aed87ae70028", "tarball": "https://registry.npmjs.org/is-set/-/is-set-1.0.0.tgz", "integrity": "sha512-ATLe+7aHQOJ7cZyTbAYskspiwiuFdrOIMiWbIPJ7Iz8gm27hW869Bj6wTWK7w46FdYkB4TYAj+MujH7khLQBkQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCYutMWafOUIl3FPMe/CldvgsgJlKf5MxybRSiJ9BasiAIgTFqMuQqPyTzBh/VnQRP0M2H1A5zs/OobsHmuuQQzq2U="}]}, "directories": {}}, "2.0.0": {"name": "is-set", "version": "2.0.0", "description": "Is this value a JS Set? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "main": "index.js", "scripts": {"version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublish": "safe-publish-latest", "pretest": "npm run lint", "lint": "eslint .", "tests-only": "node test", "posttests-only": "node -e \"require('es5-shim'); require('es6-shim'); require('./test');\"", "test": "npm run tests-only", "posttest": "npx aud"}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/is-set.git"}, "keywords": ["map", "set", "collection", "is", "robust"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/is-set/issues"}, "homepage": "https://github.com/inspect-js/is-set#readme", "devDependencies": {"@ljharb/eslint-config": "^15.0.1", "auto-changelog": "^1.16.2", "es5-shim": "^4.5.13", "es6-shim": "^0.35.5", "eslint": "^6.6.0", "for-each": "^0.3.3", "object-inspect": "^1.7.0", "safe-publish-latest": "^1.1.4", "tape": "^4.11.0"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "gitHead": "b90cab7e75ee3e000c87ea88710ea8c926c6d100", "_id": "is-set@2.0.0", "_nodeVersion": "13.1.0", "_npmVersion": "6.12.1", "dist": {"integrity": "sha512-So5/xwRDzU3X7kOt2vpvrsj/Asx5E7Q5IyX6itksB96FJgyydSe9tFwfCq7IZ8URDS4h45FhNgfENToTgBfmgw==", "shasum": "ae93342b1de5560c720b4b71599abc799d183cf4", "tarball": "https://registry.npmjs.org/is-set/-/is-set-2.0.0.tgz", "fileCount": 9, "unpackedSize": 8756, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdyxTBCRA9TVsSAnZWagAAtHYQAIJThKJEfwMHzp4TZk8F\n43xl66oo84lJzXHTY2+0fzT3mXZZTkysQIItJ0aMesUCcKBO4eu/A/ZzK8JP\n2U3BPR3pDZoMYgJuy7EX9M30H6Qv/GGj7pfeH9y3ZmYPsZHZrujHblsumt3p\nGgS+zJlf320TcEuOwII+hDZTNQnzMotOiP8bgDoKLJaeHjO8k5lhZKUt7b9N\ni/M7IYxKshiUzO8A8+/M3y3H3WM4ZvzEUvYvpq2zNqbEH/HROKTUuCC0Z8Ej\nwfd8EJ6/1u6+a4304WrUsFm1EDGj72WLv5FcDzkD/yP5+c0PROMK8sI8iCOK\nnvPDd1XQA8EMNE6cjy2D6oNJffzx/I0z53SVe8JBhraGZEenV+TFMD74Nnd/\ng4S++caQ4vecEy/jfuL6un4NZ4hlEdzs6j27aZ7oo081p7WSWn3QAvzKzAbs\nznKPdvtdCpdRFZkFZl31GqiVREgNd1QR6WAv8kpzAtC51t/+wZCcSFoZDtlE\nn063aMWaWXSyU4dfjtF4xBuB1e9p+xcNMREnBcAU3jXm2AiRHXGTX5JQ+tl+\nl2I5/ON/flPPnaj8hF57w9MeOBpVV1JPbD8BRda8P4UtC9vSnXR6a42SW9HJ\nDjdO7uxClR1Hjf70EEGGRjobhgukSyBDYFR1mu8ZfyN4R2g0VbU97c0IDdV8\nOS8n\r\n=rz9W\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHGjJaQJza2LiWmxnmmGzcZLlKkmYdCYTiPoZ11iwTi7AiA/g4biMwXd8rzusMjWIgn6jSpnSKERqg1IyxhkUMB+pg=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "lj<PERSON>b"}], "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-set_2.0.0_1573590208684_0.8370407865245786"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "is-set", "version": "2.0.1", "description": "Is this value a JS Set? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "main": "index.js", "scripts": {"version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublish": "safe-publish-latest", "pretest": "npm run lint", "lint": "eslint .", "tests-only": "node test", "posttests-only": "node -e \"require('es5-shim'); require('es6-shim'); require('./test');\"", "test": "npm run tests-only", "posttest": "npx aud"}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/is-set.git"}, "keywords": ["map", "set", "collection", "is", "robust"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/is-set/issues"}, "homepage": "https://github.com/inspect-js/is-set#readme", "devDependencies": {"@ljharb/eslint-config": "^15.0.2", "auto-changelog": "^1.16.2", "es5-shim": "^4.5.13", "es6-shim": "^0.35.5", "eslint": "^6.7.2", "for-each": "^0.3.3", "object-inspect": "^1.7.0", "safe-publish-latest": "^1.1.4", "tape": "^4.12.0"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "gitHead": "07581a60194b428f276f560d96cb8f73b4ab1db3", "_id": "is-set@2.0.1", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.1", "dist": {"integrity": "sha512-eJEzOtVyenDs1TMzSQ3kU3K+E0GUS9sno+F0OBT97xsgcJsF9nXMBtkT9/kut5JEpM7oL7X/0qxR17K3mcwIAA==", "shasum": "d1604afdab1724986d30091575f54945da7e5f43", "tarball": "https://registry.npmjs.org/is-set/-/is-set-2.0.1.tgz", "fileCount": 10, "unpackedSize": 9866, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd+UDKCRA9TVsSAnZWagAAzU0P/RfiGa6u8ghKt2SJsQan\nHVbPTgHE7q+ltdKFPk33mRGF8Jb8qpODAFxYOQF0R3ZvxykauZGJRJRUbbYy\nw1SfPozE6ukI5zm2sM93GU2etx69Igs4zxWkDVu5nMMd2Gt6n2miX/t+rN1R\ngtDrGONjtDrNfL2xRiOPCNGVj6lMJn2mHG7cT6Z/xBh0cARueQ0tzTMw3lzQ\nE+PrYPt3M6I1dLgt0/QRnG0OaeboUe9yyN+ps4+3O+AMqHaOKfdgb62FwR9G\nOJYWTBSnUjJvAVPd3++t5xoYiZeoQjv9B+5oi+F/71EmJ2xrrvaonMX7es+z\nEVL13QixqqFjLD1sHtv+pE4FE8e4pp/avJxXSMvMLPVyHV+lDLozpmNyJnsn\nyrNvy5e2dNw8ZDrkgkKMRJ3gs0OpK4zdbFsVtAUJbs5vJQncCNzYcKzxmVIu\n1AMAuHN/sL/C9m7dxyZLSYPx1czFGc0LJRAFcPVdsCiZRQj6mKWKRtRUiKUj\nGP1VVkIISGmsLk4AMV/pOJwRjrYr78vqM+75Inb2sp3cIbx5ATFf+QJdWHCS\ngEYOa2+7OqSC1qM6WUnLe6zs6oCAcjXtFq0U9dFJfKph0YVIZwfntVyELEGJ\nym63ETJK1O5qgaHkDxUcQe+CZyd4JJAyvUigXbn2MHyIoO7Ssa1dcpTmvAi7\ndUKy\r\n=FDJK\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICvnatxzRfsEe8IKuGwKmz6dWE1ie6oPufYwe3amVg62AiEA29lvsawAqkJLhrCwGqlOkPne6Xogd0a1IuonbpS982M="}]}, "maintainers": [{"email": "<EMAIL>", "name": "lj<PERSON>b"}], "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-set_2.0.1_1576616138132_0.35866745926426513"}, "_hasShrinkwrap": false}, "2.0.2": {"name": "is-set", "version": "2.0.2", "description": "Is this value a JS Set? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "main": "index.js", "scripts": {"version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublish": "safe-publish-latest", "pretest": "npm run lint", "lint": "eslint .", "tests-only": "nyc tape 'test/**/*.js'", "tests:shims": "nyc tape --require=es5-shim --require=es5-shim 'test/**/*.js'", "tests:corejs": "nyc tape --require=core-js 'test/**/*.js'", "test": "npm run tests-only && npm run tests:shims && npm run tests:corejs", "posttest": "npx aud --production"}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/is-set.git"}, "keywords": ["map", "set", "collection", "is", "robust"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/is-set/issues"}, "homepage": "https://github.com/inspect-js/is-set#readme", "devDependencies": {"@ljharb/eslint-config": "^17.3.0", "aud": "^1.1.3", "auto-changelog": "^2.2.1", "core-js": "^2.6.12", "es5-shim": "^4.5.14", "es6-shim": "^0.35.6", "eslint": "^7.15.0", "for-each": "^0.3.3", "nyc": "^10.3.2", "object-inspect": "^1.9.0", "safe-publish-latest": "^1.1.4", "tape": "^5.0.1"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "gitHead": "fb6026d7dd4d50288d6dbeb6c53165ade89cc568", "_id": "is-set@2.0.2", "_nodeVersion": "14.15.1", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-+2cnTEZeY5z/iXGbLhPrOAaK/Mau5k5eXq9j14CpRTftq0pAJu2MwVRSZhyZWBzx3o6X795Lz6Bpb6R0GKf37g==", "shasum": "90755fa4c2562dc1c5d4024760d6119b94ca18ec", "tarball": "https://registry.npmjs.org/is-set/-/is-set-2.0.2.tgz", "fileCount": 12, "unpackedSize": 12290, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf1otWCRA9TVsSAnZWagAAn3wP/RsZQ/nnY6zfcDimkGX+\nJkjn1vHbY5ZUMUJUVttoJWmUYft3X3EmW+UDyg/tREw6l9VBTmG02UHevTDG\nRhMouLScv6GFUMF23g5rQqjt0VkIxYVTvbwXJMl2JcgbNdWrYCv6rFE46mLY\nm49fYZn1ZxDwejzjM1m/eK/sh4rAz1bIJJKM8zn3vgouMeOohIoGgmgbQAD3\nx3ETFVqqDkIV0SxwalTLib18qJz4QQJ/NUhIxhT3e9u27b+OTiZXHUo0RZUs\nddFW969CfiLrtF+FTCGK3ZWIhOJfvViF4I0hxvEj4vcUaEHlLxDgZ0sX4tpf\nWVbH7zn1l/UIu3sXTiwZGDO+MvSUaTMBD0y5P69DCio16fMWuxfbC4r+7O+6\nX1XZAx/featEx8cUM/k6gOXfyTlfpgKqricXQnINURudvHRK4bZtE7YLM66P\nTUTtSmq8Mm8szBCHKmcfudCdqhxx/STNxlO1hy2Akm434Qp2LAktHH+1l7st\niuqCCyoxAo7VXOXYx9xsyxetdHzspbbE2MTu87fztp3EnFjPaLnEt4Acj2Ff\nF5jt/2OK2pcvsSHji3oSmcnzkHlFth7uv1XO6uErLIqKNXaOu9vhxyiLSw16\n8ZJP0bXU/nbHdNhBBHpO2lPvDZ57CZ1mujp17JGUSAPm1Blvf9eo/ynBmc/C\n+SYJ\r\n=EhDQ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICef0WGhAPpfuHKPAshxND7gQFEq6t1SkWsOaoHcdyQQAiAC/4lm70fhlVXSxRpvYRwXP0K+ZaMCnX5663rxBjZ7Gw=="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-set_2.0.2_1607895893536_0.8619604098051108"}, "_hasShrinkwrap": false}, "2.0.3": {"name": "is-set", "version": "2.0.3", "description": "Is this value a JS Set? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "main": "index.js", "sideEffects": false, "scripts": {"version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run lint", "lint": "eslint --ext=.js,.mjs .", "postlint": "tsc -p . && attw -P", "tests-only": "nyc tape 'test/**/*.js'", "tests:shims": "nyc tape --require=es5-shim --require=es5-shim 'test/**/*.js'", "tests:corejs": "nyc tape --require=core-js 'test/**/*.js'", "test": "npm run tests-only && npm run tests:shims && npm run tests:corejs", "posttest": "npx aud --production"}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/is-set.git"}, "keywords": ["map", "set", "collection", "is", "robust"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/is-set/issues"}, "homepage": "https://github.com/inspect-js/is-set#readme", "devDependencies": {"@arethetypeswrong/cli": "^0.15.0", "@ljharb/eslint-config": "^21.1.0", "@types/for-each": "^0.3.3", "@types/object-inspect": "^1.8.4", "@types/tape": "^5.6.4", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "core-js": "^2.6.12", "es5-shim": "^4.6.7", "es6-shim": "^0.35.8", "eslint": "=8.8.0", "for-each": "^0.3.3", "in-publish": "^2.0.1", "nyc": "^10.3.2", "object-inspect": "^1.13.1", "safe-publish-latest": "^2.0.0", "tape": "^5.7.5", "typescript": "next"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "engines": {"node": ">= 0.4"}, "_id": "is-set@2.0.3", "gitHead": "18c4595689e70093194d2064f80469e36c6b068b", "types": "./index.d.ts", "_nodeVersion": "21.7.0", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-iPAjerrse27/ygGLxw+EBR9agv9Y6uLeYVJMu+QNCoouJ1/1ri0mGrcWpfCqFZuzzx3WjtwxG098X+n4OuRkPg==", "shasum": "8ab209ea424608141372ded6e0cb200ef1d9d01d", "tarball": "https://registry.npmjs.org/is-set/-/is-set-2.0.3.tgz", "fileCount": 13, "unpackedSize": 19707, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAmET1ZqIyvEHuM7W/C8Ysfgfkew1FukFJ/hnH3Hlx/mAiEAsk97kqHaDRcJvPVUFDGokL3WbqofG/VNiliUlVJzU8Q="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-set_2.0.3_1709929102999_0.6872523944215372"}, "_hasShrinkwrap": false}}, "readme": "# is-set <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\nIs this value a JS Set? This module works cross-realm/iframe, and despite ES6 @@toStringTag.\n\n## Example\n\n```js\nvar isSet = require('is-set');\nassert(!isSet(function () {}));\nassert(!isSet(null));\nassert(!isSet(function* () { yield 42; return Infinity; });\nassert(!isSet(Symbol('foo')));\nassert(!isSet(1n));\nassert(!isSet(Object(1n)));\n\nassert(!isSet(new Map()));\nassert(!isSet(new WeakSet()));\nassert(!isSet(new WeakMap()));\n\nassert(isSet(new Set()));\n\nclass MySet extends Set {}\nassert(isSet(new MySet()));\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n[package-url]: https://npmjs.org/package/is-set\n[npm-version-svg]: https://versionbadg.es/inspect-js/is-set.svg\n[deps-svg]: https://david-dm.org/inspect-js/is-set.svg\n[deps-url]: https://david-dm.org/inspect-js/is-set\n[dev-deps-svg]: https://david-dm.org/inspect-js/is-set/dev-status.svg\n[dev-deps-url]: https://david-dm.org/inspect-js/is-set#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/is-set.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/is-set.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/is-set.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=is-set\n[codecov-image]: https://codecov.io/gh/inspect-js/is-set/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/inspect-js/is-set/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/inspect-js/is-set\n[actions-url]: https://github.com/inspect-js/is-set/actions\n", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "time": {"modified": "2024-03-08T20:18:23.626Z", "created": "2015-02-18T11:22:30.744Z", "1.0.0": "2015-02-18T11:22:30.744Z", "2.0.0": "2019-11-12T20:23:28.781Z", "2.0.1": "2019-12-17T20:55:38.219Z", "2.0.2": "2020-12-13T21:44:53.655Z", "2.0.3": "2024-03-08T20:18:23.172Z"}, "homepage": "https://github.com/inspect-js/is-set#readme", "keywords": ["map", "set", "collection", "is", "robust"], "repository": {"type": "git", "url": "git+https://github.com/inspect-js/is-set.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/inspect-js/is-set/issues"}, "license": "MIT", "readmeFilename": "README.md"}