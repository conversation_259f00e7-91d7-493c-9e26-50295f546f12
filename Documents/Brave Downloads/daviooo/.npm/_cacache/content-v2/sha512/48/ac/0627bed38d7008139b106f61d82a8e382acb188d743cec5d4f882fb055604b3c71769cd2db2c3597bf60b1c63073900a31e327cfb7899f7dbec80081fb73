{"_id": "finalhandler", "_rev": "91-62de987e312cbae8596a16e86bfa2949", "name": "finalhandler", "dist-tags": {"latest": "2.1.0"}, "versions": {"0.0.0": {"name": "finalhandler", "version": "0.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "finalhandler@0.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/finalhandler", "bugs": {"url": "https://github.com/expressjs/finalhandler/issues"}, "dist": {"shasum": "1dcd03de37b283d0593b47a327535c9490d5b246", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-0.0.0.tgz", "integrity": "sha512-fruIHIE9W5akKLIs9P2XnFtdnhUFyn9gw1G2DvkSq5q4tjLjk/hp5w38sziMjSCR93sEnXNECEnkm5HDT0YACQ==", "signatures": [{"sig": "MEQCIC/LJHjSu8CbdU/0wRSbHH/k5NhjjoeLhEWXpsC/LBGOAiBQmPSQLxknjQRo/Mjc39d4Jbw3KokOsYzcHrDDEVVz1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "1dcd03de37b283d0593b47a327535c9490d5b246", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/finalhandler", "type": "git"}, "_npmVersion": "1.4.9", "description": "Node.js final http responder", "directories": {}, "dependencies": {"debug": "1.0.0", "escape-html": "1.0.1"}, "devDependencies": {"mocha": "~1.20.1", "should": "~4.0.1", "istanbul": "0.2.10", "supertest": "~0.13.0"}}, "0.0.1": {"name": "finalhandler", "version": "0.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "finalhandler@0.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/finalhandler", "bugs": {"url": "https://github.com/expressjs/finalhandler/issues"}, "dist": {"shasum": "624429d98b41ab1538b21b97086a74f23b07fcd6", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-0.0.1.tgz", "integrity": "sha512-LYcUgLBvsQTI0X/WHy9BshCekc8HTluyXtb90E8dNLhpPllqBOO6x+s+aBI1pFRltF7KEdTCXX0MpJqHGk1x1g==", "signatures": [{"sig": "MEYCIQCXL1jeJmEkXi4VyVLhDGxMx0G3P7KrIn6emHlkdNpOsQIhANA1GrrgDAqIIPSn1Z9AZuvg6Hb63WlwaBJaU2R3npO2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/finalhandler", "type": "git"}, "_npmVersion": "1.4.3", "description": "Node.js final http responder", "directories": {}, "dependencies": {"debug": "1.0.2", "escape-html": "1.0.1"}, "devDependencies": {"mocha": "~1.20.1", "should": "~4.0.1", "istanbul": "0.2.10", "supertest": "~0.13.0"}}, "0.0.2": {"name": "finalhandler", "version": "0.0.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "finalhandler@0.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/finalhandler", "bugs": {"url": "https://github.com/expressjs/finalhandler/issues"}, "dist": {"shasum": "0603d875ee87d567a266692815cc8ad44fcceeda", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-0.0.2.tgz", "integrity": "sha512-SbpQfvWVwWEBlPTQyaM9gs0D5404ENTC0x2jzbb7t+P+EOD/cBlWjAAvfozIQYtOepUuNkxoLNLCK9/kS29f4w==", "signatures": [{"sig": "MEQCIBM0Eu6fUgk0LT83TGxlUTW1PxKSwZbTZVw4U8dACKA2AiBq5cDwV7RR4zJG4ulzCr23xm7msNg3gxalrQijz/ofAg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/finalhandler", "type": "git"}, "_npmVersion": "1.4.3", "description": "Node.js final http responder", "directories": {}, "dependencies": {"debug": "1.0.2", "escape-html": "1.0.1"}, "devDependencies": {"mocha": "~1.20.1", "should": "~4.0.1", "istanbul": "0.2.10", "supertest": "~0.13.0"}}, "0.0.3": {"name": "finalhandler", "version": "0.0.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "finalhandler@0.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/finalhandler", "bugs": {"url": "https://github.com/expressjs/finalhandler/issues"}, "dist": {"shasum": "5a86b7bc4dca3d1275eb0532c81ee81d747504df", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-0.0.3.tgz", "integrity": "sha512-/fqgssseNfnD8Y77HWyJKQ+1xbKu7bZl2LXfhFjkgeGg91WRMMO9GN1KKL53NnIG9g1H2Xq3iKrZkuIcAmjd0A==", "signatures": [{"sig": "MEQCIGjbMFMgHo2d3oq1RWcZLhfs3JYaqjId7y7B09ejRVZVAiANCxkhQcTc9NQ/73RK3SKVCj+mthHMbbRhZ0GT4/pnkA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/finalhandler", "type": "git"}, "_npmVersion": "1.4.3", "description": "Node.js final http responder", "directories": {}, "dependencies": {"debug": "1.0.3", "escape-html": "1.0.1"}, "devDependencies": {"mocha": "~1.20.1", "should": "~4.0.1", "istanbul": "0.3.0", "supertest": "~0.13.0"}}, "0.1.0": {"name": "finalhandler", "version": "0.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "finalhandler@0.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/finalhandler", "bugs": {"url": "https://github.com/expressjs/finalhandler/issues"}, "dist": {"shasum": "da05bbc4f5f4a30c84ce1d91f3c154007c4e9daa", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-0.1.0.tgz", "integrity": "sha512-VxIedmyMyIZh8ol/AeWrgVwzDqYVO5wqOcXNuQC0olCvWDgvN7+QyVKHWoZyplbZ82j5p7BynpekYybNSmTjww==", "signatures": [{"sig": "MEQCIEK01t/9/t6sh5vn7Icj70niRCfv9LnAYDST0DES+/LNAiA5mRfS0B31gzy6WFW0drBJoxCkXjfoSleyX4xPn4DDDg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/finalhandler", "type": "git"}, "_npmVersion": "1.4.3", "description": "Node.js final http responder", "directories": {}, "dependencies": {"debug": "1.0.4", "escape-html": "1.0.1"}, "devDependencies": {"mocha": "~1.20.1", "should": "~4.0.1", "istanbul": "0.3.0", "supertest": "~0.13.0", "readable-stream": "~1.0.27"}}, "0.2.0": {"name": "finalhandler", "version": "0.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "finalhandler@0.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/finalhandler", "bugs": {"url": "https://github.com/pillarjs/finalhandler/issues"}, "dist": {"shasum": "794082424b17f6a4b2a0eda39f9db6948ee4be8d", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-0.2.0.tgz", "integrity": "sha512-+8V22UTsucJNTi5IcGzTzdYwtESxkyEJ/ipGSVzbGvbqSmmjnNGvrzm/8Uu1FqJT6d6DgXkuzgfefkE2Fl7Hnw==", "signatures": [{"sig": "MEUCIQDuE9i/mBtAftJRW5/1zOgnkiYeSWOSIDhsS0SEV+9kkwIgGzfQkaU3Fj0Q1gdyRpFvQMqq1xeJ5MouJgHTA+6H8NE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "794082424b17f6a4b2a0eda39f9db6948ee4be8d", "engines": {"node": ">= 0.8.0"}, "gitHead": "0e5d26695b1ab248823366018f09c058a4eaf59b", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/pillarjs/finalhandler", "type": "git"}, "_npmVersion": "1.4.21", "description": "Node.js final http responder", "directories": {}, "dependencies": {"debug": "~2.0.0", "escape-html": "1.0.1"}, "devDependencies": {"mocha": "~1.21.4", "should": "~4.0.1", "istanbul": "0.3.0", "supertest": "~0.13.0", "readable-stream": "~1.0.27"}}, "0.3.0": {"name": "finalhandler", "version": "0.3.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "finalhandler@0.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/finalhandler", "bugs": {"url": "https://github.com/pillarjs/finalhandler/issues"}, "dist": {"shasum": "581afe4d28da13491487f1c0ef9c29ef883e6e59", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-0.3.0.tgz", "integrity": "sha512-fBTSSrzaAUVdlsJWPOpwSZL2GLPWsPzbhro+ujUTPQJIJ8suaOnPp8Bg7/3b4Ofs7/NuLSgcPNueazgqxQqeuw==", "signatures": [{"sig": "MEUCIQD/YRahjmSX5pfjrfTTZTSLfx0xhEg7/30o8nXXI/eTbAIgLcFa9wXwELhzk+VtxiwMQ06TwDOgl0E4uDXI5hmSGY0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "581afe4d28da13491487f1c0ef9c29ef883e6e59", "engines": {"node": ">= 0.8"}, "gitHead": "8cc4b0afde5ab1fdb1f42bcdf80fd1d9dd4e1528", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/pillarjs/finalhandler", "type": "git"}, "_npmVersion": "1.4.21", "description": "Node.js final http responder", "directories": {}, "dependencies": {"debug": "~2.0.0", "escape-html": "1.0.1", "on-finished": "~2.1.0"}, "devDependencies": {"mocha": "~1.21.4", "should": "~4.0.1", "istanbul": "0.3.2", "supertest": "~0.13.0", "readable-stream": "~1.0.27"}}, "0.3.1": {"name": "finalhandler", "version": "0.3.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "finalhandler@0.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/finalhandler", "bugs": {"url": "https://github.com/pillarjs/finalhandler/issues"}, "dist": {"shasum": "ffda7643228678c6b088c89421a8381663961808", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-0.3.1.tgz", "integrity": "sha512-2wQ95Ql93ncJ8QmACP5gtSdP2nN7GlaOiLUhmhoxrRootI05zP/ux5fd5CeYDx6WJg6pdOZQNv6wO1G2UjKMTw==", "signatures": [{"sig": "MEUCIQCLq9Es4tPGHFVXYEmJZecPf+aTmct+TNwyfCoea5srvwIgXUihzBU5ifC0hnIXJIbl/sYxHLQU0RHxOhGDWjYsKj8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "ffda7643228678c6b088c89421a8381663961808", "engines": {"node": ">= 0.8"}, "gitHead": "d7ad6d8d66316d88774171f606b78f386eadce85", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/pillarjs/finalhandler", "type": "git"}, "_npmVersion": "1.4.21", "description": "Node.js final http responder", "directories": {}, "dependencies": {"debug": "~2.1.0", "escape-html": "1.0.1", "on-finished": "~2.1.0"}, "devDependencies": {"mocha": "~1.21.5", "should": "~4.0.1", "istanbul": "0.3.2", "supertest": "~0.14.0", "readable-stream": "~1.0.33"}}, "0.3.2": {"name": "finalhandler", "version": "0.3.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "finalhandler@0.3.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/finalhandler", "bugs": {"url": "https://github.com/pillarjs/finalhandler/issues"}, "dist": {"shasum": "7b389b0fd3647a6f90bd564e22624bf8a4a77fb5", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-0.3.2.tgz", "integrity": "sha512-+V+86srh2q2ebx2iD1qG4J5GULZtZLujK+g6g9Rtwe1sKVekOIRyJaWOKyWoiUMLpI30JPlp1C8QH3ucLGmKoA==", "signatures": [{"sig": "MEQCIDHQworiGvokroGAl54lVv+nL/xjHHO1lozTj9pSdcV/AiBoExJQA5iXoXmpb/s/e6OcDZ5DSYnKBhJM6SsYT9+q/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "7b389b0fd3647a6f90bd564e22624bf8a4a77fb5", "engines": {"node": ">= 0.8"}, "gitHead": "1dc292faf576fade3b0218caab39060f4da5fe9c", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/pillarjs/finalhandler", "type": "git"}, "_npmVersion": "1.4.21", "description": "Node.js final http responder", "directories": {}, "dependencies": {"debug": "~2.1.0", "escape-html": "1.0.1", "on-finished": "~2.1.1"}, "devDependencies": {"mocha": "~2.0.0", "should": "~4.1.0", "istanbul": "0.3.2", "supertest": "~0.14.0", "readable-stream": "~1.0.33"}}, "0.3.3": {"name": "finalhandler", "version": "0.3.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "finalhandler@0.3.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/finalhandler", "bugs": {"url": "https://github.com/pillarjs/finalhandler/issues"}, "dist": {"shasum": "b1a09aa1e6a607b3541669b09bcb727f460cd426", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-0.3.3.tgz", "integrity": "sha512-2Os1f3WIa2papf/sQ10h3NKjI+iadgTUIb2MfIUFcKFZgRSmQtBbYdhKct9wkWjoJA6+zIgIMDFtk4Fd0qKhdg==", "signatures": [{"sig": "MEYCIQDaYPLoOwZWwXgzTt4f+2S8EmpO2JC27B3ZVJDwQc9QkwIhAKRjd/FcNDheA4c4UQ6mr5hZmul2NIDXUpIuNc59N43X", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "b1a09aa1e6a607b3541669b09bcb727f460cd426", "engines": {"node": ">= 0.8"}, "gitHead": "dfce5042f996ba93ac85b9282e6d1cae1561acc6", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/pillarjs/finalhandler", "type": "git"}, "_npmVersion": "1.4.28", "description": "Node.js final http responder", "directories": {}, "dependencies": {"debug": "~2.1.1", "escape-html": "1.0.1", "on-finished": "~2.2.0"}, "devDependencies": {"mocha": "~2.1.0", "istanbul": "0.3.5", "supertest": "~0.15.0", "readable-stream": "~1.0.33"}}, "0.3.4": {"name": "finalhandler", "version": "0.3.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "finalhandler@0.3.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/finalhandler", "bugs": {"url": "https://github.com/pillarjs/finalhandler/issues"}, "dist": {"shasum": "4787d3573d079ae8b07536f26b0b911ebaf2a2ac", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-0.3.4.tgz", "integrity": "sha512-fg2FhK0sQAeb3EcYej2OPhczPIqmMGDHi8C+Ri3UPgslMJnmvwVkw6hFW3xUDt4aQowDwZd+BXGBTHDzMWYDyw==", "signatures": [{"sig": "MEYCIQD5HLdWTKji06D3CNebBFo41cp+jDpkmKq6BtYuVgbC+QIhAKytt//Mmm2Ab9+SVrtxq1+vRBO+/zMJZwYwKuOSf0Pb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "4787d3573d079ae8b07536f26b0b911ebaf2a2ac", "engines": {"node": ">= 0.8"}, "gitHead": "63e18603c11effcacc06676f6fefbf270795459a", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/pillarjs/finalhandler", "type": "git"}, "_npmVersion": "1.4.28", "description": "Node.js final http responder", "directories": {}, "dependencies": {"debug": "~2.1.3", "escape-html": "1.0.1", "on-finished": "~2.2.0"}, "devDependencies": {"mocha": "~2.2.1", "istanbul": "0.3.8", "supertest": "~0.15.0", "readable-stream": "~1.0.33"}}, "0.3.5": {"name": "finalhandler", "version": "0.3.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "finalhandler@0.3.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/finalhandler", "bugs": {"url": "https://github.com/pillarjs/finalhandler/issues"}, "dist": {"shasum": "0d1bf5dfcb5f77d073a402aabe5f7a8a90413721", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-0.3.5.tgz", "integrity": "sha512-<PERSON><PERSON><PERSON><PERSON>htjEciScXeJYEc5Exx26KGrL1T1Bvs3mITNk2mLoKaKspC6j1WSl9neb8R0hnzRs42Kp8YEGTOT3cuHA==", "signatures": [{"sig": "MEQCIH4EMl6xQun3XhfcEv0LvVQrI75AW+/p10I4fMjLsN7oAiB2qH3WXyDO8SbeiNua8aZDSKLbfidVCuKGd07olEPR4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "0d1bf5dfcb5f77d073a402aabe5f7a8a90413721", "engines": {"node": ">= 0.8"}, "gitHead": "618ff86e01ca5b8eb2f204ba1f322f65896c1455", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/pillarjs/finalhandler", "type": "git"}, "_npmVersion": "1.4.28", "description": "Node.js final http responder", "directories": {}, "dependencies": {"debug": "~2.1.3", "escape-html": "1.0.1", "on-finished": "~2.2.1"}, "devDependencies": {"mocha": "~2.2.4", "istanbul": "0.3.9", "supertest": "~0.15.0", "readable-stream": "~1.0.33"}}, "0.3.6": {"name": "finalhandler", "version": "0.3.6", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "finalhandler@0.3.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/finalhandler", "bugs": {"url": "https://github.com/pillarjs/finalhandler/issues"}, "dist": {"shasum": "daf9c4161b1b06e001466b1411dfdb6973be138b", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-0.3.6.tgz", "integrity": "sha512-yVJsDXswFVohBY1qO3p8rhTNMcsZav+s30+2PlrFAeBzzbIgVg1214pHymmSP++KSrr6FXH5+RQItsGEeLK6+A==", "signatures": [{"sig": "MEYCIQDUudYeb0vfjlx6xEr9i6SIkrZMxC0ttK2U8b26U/oAJQIhAP+j5+1XnTot48nkXhC/f3hubDMcRI8n6dpJK+z2QBcl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "daf9c4161b1b06e001466b1411dfdb6973be138b", "engines": {"node": ">= 0.8"}, "gitHead": "10c8b938d00acdd5a2bdc6fbd912fb24ffd9f328", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/pillarjs/finalhandler", "type": "git"}, "_npmVersion": "1.4.28", "description": "Node.js final http responder", "directories": {}, "dependencies": {"debug": "~2.2.0", "escape-html": "1.0.1", "on-finished": "~2.2.1"}, "devDependencies": {"mocha": "~2.2.4", "istanbul": "0.3.9", "supertest": "~0.15.0", "readable-stream": "~1.0.33"}}, "0.4.0": {"name": "finalhandler", "version": "0.4.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "finalhandler@0.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/finalhandler", "bugs": {"url": "https://github.com/pillarjs/finalhandler/issues"}, "dist": {"shasum": "965a52d9e8d05d2b857548541fb89b53a2497d9b", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-0.4.0.tgz", "integrity": "sha512-jJU2WE88OqUvwAIf/1K2G2fTdKKZ8LvSwYQyFFekDcmBnBmht38enbcmErnA7iNZktcEo/o2JAHYbe1QDOAgaA==", "signatures": [{"sig": "MEQCID9hPvVABpRZCy8NdCKapEa/nnSxv0U+VmVenRczfQdoAiBWumsooRcOZlciYDUnPIT8Au1dUERQ6GBvimDyj012kw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "965a52d9e8d05d2b857548541fb89b53a2497d9b", "engines": {"node": ">= 0.8"}, "gitHead": "fe4e4de9ebb0f3831493ad75119ee6ba40542853", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/pillarjs/finalhandler", "type": "git"}, "_npmVersion": "1.4.28", "description": "Node.js final http responder", "directories": {}, "dependencies": {"debug": "~2.2.0", "unpipe": "~1.0.0", "escape-html": "1.0.2", "on-finished": "~2.3.0"}, "devDependencies": {"mocha": "2.2.5", "istanbul": "0.3.15", "supertest": "1.0.1", "readable-stream": "2.0.0"}}, "0.4.1": {"name": "finalhandler", "version": "0.4.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "finalhandler@0.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/finalhandler", "bugs": {"url": "https://github.com/pillarjs/finalhandler/issues"}, "dist": {"shasum": "85a17c6c59a94717d262d61230d4b0ebe3d4a14d", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-0.4.1.tgz", "integrity": "sha512-+AkanbaabSCYrDcrU+TcA/8SEyMDAN7mjE6GC71GAlvYDXM4wzUsRqLLS2qPtWecIlkX5+MMZGd2RyxO3yBOfg==", "signatures": [{"sig": "MEQCICzgjIGNPPu1wBsH+Ms1cEsuAGxfHt9HpFy2sbRzMYLMAiBjDepogcXPtxyYk3tv0YqLJKUyq7Bte0WB4skFsTAXEQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "85a17c6c59a94717d262d61230d4b0ebe3d4a14d", "engines": {"node": ">= 0.8"}, "gitHead": "ac2036774059eb93dbac8475580e52433204d4d4", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/pillarjs/finalhandler", "type": "git"}, "_npmVersion": "1.4.28", "description": "Node.js final http responder", "directories": {}, "dependencies": {"debug": "~2.2.0", "unpipe": "~1.0.0", "escape-html": "~1.0.3", "on-finished": "~2.3.0"}, "devDependencies": {"mocha": "2.3.4", "istanbul": "0.4.1", "supertest": "1.1.0", "readable-stream": "2.0.4"}}, "0.5.0": {"name": "finalhandler", "version": "0.5.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "finalhandler@0.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/finalhandler", "bugs": {"url": "https://github.com/pillarjs/finalhandler/issues"}, "dist": {"shasum": "e9508abece9b6dba871a6942a1d7911b91911ac7", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-0.5.0.tgz", "integrity": "sha512-KCwi04Tss2Qa+3NQkU3/4lBYXfHYunl3YM0rDJPxhdZ1qjlGvf/BilX2g7vm/qkHUMs5MncaD9f/VTdYN95iig==", "signatures": [{"sig": "MEQCIHyemDv5lbOv8O46B2C790RTUI2BtsCLiMKcp2+s1t4QAiBKtY5j6VVHx8cR+FzVlZliZJRnzVPlzyAdWibMrWKM5A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "e9508abece9b6dba871a6942a1d7911b91911ac7", "engines": {"node": ">= 0.8"}, "gitHead": "15cc543eb87dd0e2f29e931d86816a6eb348c573", "scripts": {"lint": "eslint **/*.js", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/pillarjs/finalhandler", "type": "git"}, "_npmVersion": "1.4.28", "description": "Node.js final http responder", "directories": {}, "dependencies": {"debug": "~2.2.0", "unpipe": "~1.0.0", "statuses": "~1.3.0", "escape-html": "~1.0.3", "on-finished": "~2.3.0"}, "devDependencies": {"mocha": "2.5.3", "eslint": "2.12.0", "istanbul": "0.4.3", "supertest": "1.1.0", "readable-stream": "2.1.2", "eslint-plugin-promise": "1.3.2", "eslint-config-standard": "5.3.1", "eslint-plugin-standard": "1.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/finalhandler-0.5.0.tgz_1466028655505_0.19758180482313037", "host": "packages-12-west.internal.npmjs.com"}}, "0.5.1": {"name": "finalhandler", "version": "0.5.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "finalhandler@0.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/finalhandler", "bugs": {"url": "https://github.com/pillarjs/finalhandler/issues"}, "dist": {"shasum": "2c400d8d4530935bc232549c5fa385ec07de6fcd", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-0.5.1.tgz", "integrity": "sha512-PYuh1UzGGCOoRvrbGYCq6memvx41rxMCr+0XT9NtiIWqGG7hbCBcPMBRQoi5sMZDzTOxwiuv7/gwPtrDOz76CQ==", "signatures": [{"sig": "MEUCIAZ3I+2exzCHGhlP9rfqnKaKxvO8kBuRFg8zGN+Qq3PDAiEA4VUpyEpcKHTfgFhnFMUZIZuzhDzKj5yRsCwEC/YFZbc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "2c400d8d4530935bc232549c5fa385ec07de6fcd", "engines": {"node": ">= 0.8"}, "gitHead": "ae6137a81049eecb2d57341b1a9c4efed46a25da", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/pillarjs/finalhandler", "type": "git"}, "_npmVersion": "1.4.28", "description": "Node.js final http responder", "directories": {}, "dependencies": {"debug": "~2.2.0", "unpipe": "~1.0.0", "statuses": "~1.3.1", "escape-html": "~1.0.3", "on-finished": "~2.3.0"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.10.0", "istanbul": "0.4.5", "supertest": "1.1.0", "readable-stream": "2.1.2", "eslint-plugin-promise": "3.3.2", "eslint-config-standard": "6.2.1", "eslint-plugin-markdown": "1.0.0-beta.3", "eslint-plugin-standard": "2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/finalhandler-0.5.1.tgz_1479018213560_0.8304649770725518", "host": "packages-12-west.internal.npmjs.com"}}, "1.0.0": {"name": "finalhandler", "version": "1.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "finalhandler@1.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/finalhandler#readme", "bugs": {"url": "https://github.com/pillarjs/finalhandler/issues"}, "dist": {"shasum": "b5691c2c0912092f18ac23e9416bde5cd7dc6755", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.0.0.tgz", "integrity": "sha512-vqD6y4OKWX0ColC1GxaH6HiIYIZouvOdlvBjY/P4t7GreZHRuK1HkzW26zx3OtZ0PV5j4yNYQgrqBdrY+suCIw==", "signatures": [{"sig": "MEUCIGg7KYJTkCBLZjEe7OaH/yTsTdPn0aILjbPzKqqH23otAiEAtL6sm7exlHgouUGJdL1c6ml+IZb4vDUM5V1GbV7UB9c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "b5691c2c0912092f18ac23e9416bde5cd7dc6755", "engines": {"node": ">= 0.8"}, "gitHead": "6e024b1139202f69a537884ea755a0bf1bb72d69", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/finalhandler.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Node.js final http responder", "directories": {}, "_nodeVersion": "4.7.3", "dependencies": {"debug": "2.6.1", "unpipe": "~1.0.0", "parseurl": "~1.3.1", "statuses": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3", "on-finished": "~2.3.0"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.15.0", "istanbul": "0.4.5", "supertest": "1.1.0", "readable-stream": "2.1.2", "eslint-plugin-promise": "3.3.2", "eslint-config-standard": "6.2.1", "eslint-plugin-markdown": "1.0.0-beta.3", "eslint-plugin-standard": "2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/finalhandler-1.0.0.tgz_1487228805174_0.0024696807377040386", "host": "packages-18-east.internal.npmjs.com"}}, "1.0.1": {"name": "finalhandler", "version": "1.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "finalhandler@1.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/finalhandler#readme", "bugs": {"url": "https://github.com/pillarjs/finalhandler/issues"}, "dist": {"shasum": "bcd15d1689c0e5ed729b6f7f541a6df984117db8", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.0.1.tgz", "integrity": "sha512-Pr9a4afq6d5JBCdDGMwuGMM0HSt1ZRfl7+X2PAltwiz9oGA+wfi+TPgkcW8YwTbVT1OBESQ6i0XlPbNep58jwA==", "signatures": [{"sig": "MEUCIQDn8hwgnXnuLagj1/0B+0KEkla8qtl33G1P5VFnIP0vSAIgL+0pL1GtmXGCjjWC+2tkPxhXi8nCpAw8yR5+L3hwkXo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "bcd15d1689c0e5ed729b6f7f541a6df984117db8", "engines": {"node": ">= 0.8"}, "gitHead": "7643136085e8c178902a93d6ef43ad42cd3936f1", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/finalhandler.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Node.js final http responder", "directories": {}, "_nodeVersion": "4.7.3", "dependencies": {"debug": "2.6.3", "unpipe": "~1.0.0", "parseurl": "~1.3.1", "statuses": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3", "on-finished": "~2.3.0"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.18.0", "istanbul": "0.4.5", "supertest": "1.1.0", "readable-stream": "2.1.2", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "7.1.0", "eslint-plugin-markdown": "1.0.0-beta.4", "eslint-plugin-standard": "2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/finalhandler-1.0.1.tgz_1490162581058_0.40150946634821594", "host": "packages-18-east.internal.npmjs.com"}}, "1.0.2": {"name": "finalhandler", "version": "1.0.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "finalhandler@1.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/finalhandler#readme", "bugs": {"url": "https://github.com/pillarjs/finalhandler/issues"}, "dist": {"shasum": "d0e36f9dbc557f2de14423df6261889e9d60c93a", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.0.2.tgz", "integrity": "sha512-pJN03HWitZ0/S24+UcMTLamRkQEauALQSR7e35HQVcRsnYR5qoDaFG5v96jZazOof4B1MKYtV8Pgn34UI0hP2w==", "signatures": [{"sig": "MEYCIQDklSM8DUrS0a+4wrIwxRikI4QSJeUhRvItgNuAuyXffAIhAPkcN1W5h1HNQ1J+thJccNuuxMITQeMhIr4hguFDfGKK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "d0e36f9dbc557f2de14423df6261889e9d60c93a", "engines": {"node": ">= 0.8"}, "gitHead": "fdc51081ce2747d28855f4d6ac9d418379f509ed", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/finalhandler.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Node.js final http responder", "directories": {}, "_nodeVersion": "4.7.3", "dependencies": {"debug": "2.6.4", "unpipe": "~1.0.0", "parseurl": "~1.3.1", "statuses": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3", "on-finished": "~2.3.0"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "safe-buffer": "5.0.1", "readable-stream": "2.2.9", "eslint-plugin-node": "4.2.2", "eslint-plugin-import": "2.2.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.4", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/finalhandler-1.0.2.tgz_1492903233024_0.34017785592004657", "host": "packages-18-east.internal.npmjs.com"}}, "1.0.3": {"name": "finalhandler", "version": "1.0.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "finalhandler@1.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/finalhandler#readme", "bugs": {"url": "https://github.com/pillarjs/finalhandler/issues"}, "dist": {"shasum": "ef47e77950e999780e86022a560e3217e0d0cc89", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.0.3.tgz", "integrity": "sha512-Crb5GWw1cfFnZlZ3LJYAQCMbrfsKoV7+DNSvCQpwbybd+8tkrjHad5JuCJhunq8gJ80AqqsvbWxQSDBYxkr5tw==", "signatures": [{"sig": "MEUCIDjnKCTf6RDQzQpFhftdrjmj4yrKzmRjteCc2xA1R3uEAiEAhJ9JsZTJBFP1O9qRc0OAI9LxURHQjgDN5/cor6hRmqQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "ef47e77950e999780e86022a560e3217e0d0cc89", "engines": {"node": ">= 0.8"}, "gitHead": "0425ae63bf44a661354baf6b37eebb01909cd78d", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/finalhandler.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Node.js final http responder", "directories": {}, "_nodeVersion": "6.10.3", "dependencies": {"debug": "2.6.7", "unpipe": "~1.0.0", "parseurl": "~1.3.1", "statuses": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3", "on-finished": "~2.3.0"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "safe-buffer": "5.0.1", "readable-stream": "2.2.9", "eslint-plugin-node": "4.2.2", "eslint-plugin-import": "2.2.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/finalhandler-1.0.3.tgz_1494997503461_0.08480599173344672", "host": "packages-12-west.internal.npmjs.com"}}, "1.0.4": {"name": "finalhandler", "version": "1.0.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "finalhandler@1.0.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/finalhandler#readme", "bugs": {"url": "https://github.com/pillarjs/finalhandler/issues"}, "dist": {"shasum": "18574f2e7c4b98b8ae3b230c21f201f31bdb3fb7", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.0.4.tgz", "integrity": "sha512-16l/r8RgzlXKmFOhZpHBztvye+lAhC5SU7hXavnerC9UfZqZxxXl3BzL8MhffPT3kF61lj9Oav2LKEzh0ei7tg==", "signatures": [{"sig": "MEUCIQCsEFmttWP8fX8zo46iooBt7LKKCytIcZZXcJt9tkmFlQIgMUrJ9S7FIhRRMVKn6420qTg9rhhueNQfRNQcuYPWEc4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["LICENSE", "HISTORY.md", "index.js"], "engines": {"node": ">= 0.8"}, "gitHead": "85049f83c5eca2ce6f41700ab3ea7b1bfc64e18f", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/finalhandler.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Node.js final http responder", "directories": {}, "_nodeVersion": "6.11.1", "dependencies": {"debug": "2.6.8", "unpipe": "~1.0.0", "parseurl": "~1.3.1", "statuses": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3", "on-finished": "~2.3.0"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "safe-buffer": "5.1.1", "readable-stream": "2.3.3", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/finalhandler-1.0.4.tgz_1501819287831_0.5680490005761385", "host": "s3://npm-registry-packages"}}, "1.0.5": {"name": "finalhandler", "version": "1.0.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "finalhandler@1.0.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/finalhandler#readme", "bugs": {"url": "https://github.com/pillarjs/finalhandler/issues"}, "dist": {"shasum": "a701303d257a1bc82fea547a33e5ae89531723df", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.0.5.tgz", "integrity": "sha512-Zy7Wo3xyTfMqIbJIqSgyqozMAY8GySij0h9c4sIghfBgPeBfQPqcTY4zhAAfHC9zCaU2lwWybWKmoJb+sQNwmQ==", "signatures": [{"sig": "MEQCIHGHk0Z6WxtHUwCuilzLMykpGiV3EZvBr/hshQwaxpopAiAaceCZOxvaWuwMerjsOXq9x5FgE+PCEwKh3Gd8Eeh06A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "a701303d257a1bc82fea547a33e5ae89531723df", "engines": {"node": ">= 0.8"}, "gitHead": "56992d0cb79e0c6575e7ec02bffa244d999d1fd3", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/finalhandler.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Node.js final http responder", "directories": {}, "_nodeVersion": "6.11.1", "dependencies": {"debug": "2.6.8", "unpipe": "~1.0.0", "parseurl": "~1.3.2", "statuses": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3", "on-finished": "~2.3.0"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "safe-buffer": "5.1.1", "readable-stream": "2.3.3", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/finalhandler-1.0.5.tgz_1505531025359_0.3480313152540475", "host": "s3://npm-registry-packages"}}, "1.0.6": {"name": "finalhandler", "version": "1.0.6", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "finalhandler@1.0.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/finalhandler#readme", "bugs": {"url": "https://github.com/pillarjs/finalhandler/issues"}, "dist": {"shasum": "007aea33d1a4d3e42017f624848ad58d212f814f", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.0.6.tgz", "integrity": "sha512-immlyyYCPWG2tajlYBhZ6cjLAv1QAclU8tKS0d27ZtPqm/+iddy16GT3xLExg+V4lIETLpPwaYQAlZHNE//dPA==", "signatures": [{"sig": "MEUCIQDK80WReNkc1w9GXp1jcJbcOP/t/3N5gzG1fwVmF1/YkgIgX+42fUOw//hoR2LP2w3CUnkusf5U34is7wnptQoxQx0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "007aea33d1a4d3e42017f624848ad58d212f814f", "engines": {"node": ">= 0.8"}, "gitHead": "ed4c24d4d7f78b3136ca7d0e7215541cb921a980", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/finalhandler.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Node.js final http responder", "directories": {}, "_nodeVersion": "6.11.1", "dependencies": {"debug": "2.6.9", "unpipe": "~1.0.0", "parseurl": "~1.3.2", "statuses": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3", "on-finished": "~2.3.0"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "safe-buffer": "5.1.1", "readable-stream": "2.3.3", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/finalhandler-1.0.6.tgz_1506104827410_0.847819667076692", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "finalhandler", "version": "1.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "finalhandler@1.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/finalhandler#readme", "bugs": {"url": "https://github.com/pillarjs/finalhandler/issues"}, "dist": {"shasum": "ce0b6855b45853e791b2fcc680046d88253dd7f5", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.1.0.tgz", "integrity": "sha512-ejnvM9ZXYzp6PUPUyQBMBf0Co5VX2gr5H2VQe2Ui2jWXNlxv+PYZo8wpAymJNJdLsG1R4p+M4aynF8KuoUEwRw==", "signatures": [{"sig": "MEQCIG9NYuP2lJ+pMrQcOLGIOIg/ojLDY6+e43iNghsXTZ6TAiBVwoC1qa/lwLb+Rzz6svD5RLzoLkZizBgmYEe6FpqkgQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "ce0b6855b45853e791b2fcc680046d88253dd7f5", "engines": {"node": ">= 0.8"}, "gitHead": "a49efb83a3363d895f8c2a4cad07ccfc9e90b8ef", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/finalhandler.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Node.js final http responder", "directories": {}, "_nodeVersion": "6.11.1", "dependencies": {"debug": "2.6.9", "unpipe": "~1.0.0", "parseurl": "~1.3.2", "statuses": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3", "on-finished": "~2.3.0"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "safe-buffer": "5.1.1", "readable-stream": "2.3.3", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/finalhandler-1.1.0.tgz_1506311584388_0.4006447312422097", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "finalhandler", "version": "1.1.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "finalhandler@1.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/finalhandler#readme", "bugs": {"url": "https://github.com/pillarjs/finalhandler/issues"}, "dist": {"shasum": "eebf4ed840079c83f4249038c9d703008301b105", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.1.1.tgz", "fileCount": 5, "integrity": "sha512-Y1GUDo39ez4aHAw7MysnUD5JzYX+WaIj8I57kO3aEPT1fFRL4sr7mjei97FgnwhAyyzRYmQZaTHb2+9uZ1dPtg==", "signatures": [{"sig": "MEUCIQD9yaevjcuUnb2pQtBngoijq7qzxl87p/EW02hq4PaTGAIgLb3qeypiLlVDgexkIiVizRKMzaxJA5ePbZDOdZMpaW4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16905}, "files": ["LICENSE", "HISTORY.md", "index.js"], "engines": {"node": ">= 0.8"}, "gitHead": "024f493418f62a59592a98f07b23b265092c1006", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/finalhandler.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Node.js final http responder", "directories": {}, "_nodeVersion": "6.13.0", "dependencies": {"debug": "2.6.9", "unpipe": "~1.0.0", "parseurl": "~1.3.2", "statuses": "~1.4.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "~2.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "2.5.3", "eslint": "4.18.1", "istanbul": "0.4.5", "supertest": "1.1.0", "safe-buffer": "5.1.1", "readable-stream": "2.3.4", "eslint-plugin-node": "6.0.1", "eslint-plugin-import": "2.9.0", "eslint-plugin-promise": "3.6.0", "eslint-config-standard": "11.0.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/finalhandler_1.1.1_1520357803698_0.8904166922156158", "host": "s3://npm-registry-packages"}}, "1.1.2": {"name": "finalhandler", "version": "1.1.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "finalhandler@1.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/finalhandler#readme", "bugs": {"url": "https://github.com/pillarjs/finalhandler/issues"}, "dist": {"shasum": "b7e7d000ffd11938d0fdb053506f6ebabe9f587d", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.1.2.tgz", "fileCount": 5, "integrity": "sha512-aAWcW57uxVNrQZqFXjITpW3sIUQmHGG3qSb9mUah9MgMC4NeWhNOlNjXEYq3HjRAvL6arUviZGGJsBg6z0zsWA==", "signatures": [{"sig": "MEUCIHQ0EqIEi2ZujhmHkPulCwWGYcTAKAHr/GnmM1q8zG2KAiEAsIs1p36gdXV0VYeiszruvI24Ay7qQB5p/BRxWmJ3QBk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17043, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc1NxFCRA9TVsSAnZWagAA5lsP/ikRb6COPldBamNBvpJ9\nRhs/yK6Xs0xWScOozeN+eYycCG1ybDJaCZ7Pw1dkjpfSVB6gIuV+o1DxycnA\n3Fp+IPyJ4W3hHgWUL+1KuA5ajYbrH22ezf+DnmU3G04Xj3qpEZeeYI3hQ0IQ\nBXWjiCwmn2CEB8HYyAuvgEgsH8crqkzRlaLMb4I57TAKk0JKwZd+4r4t2WlM\n8sI83C7in0rOWkjUpJWBuR7hwSGqygKf8i0cIrOdA3ilf8EjmjELrA5mIga/\norpYoarZRenzzyyRRfG3H3Sryblavh6lajTwqwh+CxmeQJLv+gJTERLvrUc2\n2Rjmj2Iu3Z2h+D6f9qWdsPRXqE91KU0X0fDtBNjibOMncX8GtpG332SmN0Fd\nXs1E/Fcu2GlOgoJfD5mKA2OJ3GN9YlFfc7uvXuaB491/i9J/Zde548Tdn3im\ncxN8ULRL3GLHHbxONXX9Q49mdQxmHsJDPe6xN7RQwxirLZKlZEXJjl82x1kJ\nJQ0wHcxsuZiJmUTje64W5ETFBI1qN/rJiFN8HQTEkUeXpnI/Kl4ktzefhwPF\njlgPQ8BLiCeHAM4cCweDGUTUVelo6RQaN/ljpPivvd1l8DU+1R4guvjjDWcr\nb2eE7W0pup5R/PAJe61LTA38pRuurq4kR0lLfPWvOQvhPuSZ8WbDP0JNBf/U\noAB7\r\n=2zSq\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}, "gitHead": "15e78cab32ecbd4993d1575a065963b238336df9", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/finalhandler.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Node.js final http responder", "directories": {}, "_nodeVersion": "8.16.0", "dependencies": {"debug": "2.6.9", "unpipe": "~1.0.0", "parseurl": "~1.3.3", "statuses": "~1.5.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "~2.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "6.1.4", "eslint": "5.16.0", "istanbul": "0.4.5", "supertest": "4.0.2", "safe-buffer": "5.1.2", "readable-stream": "2.3.6", "eslint-plugin-node": "8.0.1", "eslint-plugin-import": "2.17.2", "eslint-plugin-promise": "4.1.1", "eslint-config-standard": "12.0.0", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-standard": "4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/finalhandler_1.1.2_1557453893383_0.5703759185655743", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "finalhandler", "version": "1.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "finalhandler@1.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/finalhandler#readme", "bugs": {"url": "https://github.com/pillarjs/finalhandler/issues"}, "dist": {"shasum": "7d23fe5731b207b4640e4fcd00aec1f9207a7b32", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.2.0.tgz", "fileCount": 6, "integrity": "sha512-5uXcUVftlQMFnWC9qu/svkWv3GTd2PfUhK/3PLkYNAe7FbqJMt3515HaxE6eRL74GdsriiwujiawdaB1BpEISg==", "signatures": [{"sig": "MEUCIFPj6xPah9aAusx764/n8Uzbjg6HDuLmdds0gdjM2t/sAiEAiA7wlztmwdbDRrC2ImXm47JxpC7burCNug8YKiCxj60=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18642, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiOnBcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp7QA//eTRG65RFRRWecfz6OMAXeYJBjrqGRLlCwjTpcMvlB2txpGML\r\nDt5RXXQspjfNjkJoA3Fx7W8+/v/CZz3YoJzkNdrOda9ia7KfTWxf2XICDUvF\r\n2y1lbwcW/M3FiKNTRnKC8CZtF5+TlfDhzWpfnMi1yOGQ2AftiE1QUN9KfpA7\r\nNBr0KJZeURaiFJ7oFIIw9QZaIhxqMp5X/Fmk+zoJ/paeWs22UvU4wxJA4YeZ\r\nSLSTz98aHUUoAl3MfDH8oYWi/Xwv5x3kHQXt6kNtfct1QUEEeDL3fynjdZK2\r\n100pDboUQBq0A/5Qp6Zy7x2aVass9faYJmx1Hog7N/TcOQKHHWiccWcqQcZw\r\nq66+642gUKOaVIs49Fk38qYkLoW3bGNRcHmhc67PrUBvHv3UFQhut9U9OlbO\r\neIymoxlobcjTCXE1YKJJp9rrXQobs6nF8WNzUpX9IiKw+Vy30ASggYQZj3m6\r\nIlLxmjGhOb1irF21crbjhoKr4hGIheC6MJ6d8ClCWik1eX5UJ/TlpkMWf3mX\r\nkiFkdaXlF+QJHQvqvK79kzMpDQ373wJzVCDTMTrh3zwaph9EXz/AHP8Hptai\r\nr0YJ5X6AzR6oNaVzvt4C5bvf0+ULeIgAbdGiRSEDVIitJVauHONHM539UdCY\r\n5UWYqxWu7MtsB2ijEtn8YY2+juTEJSHtwRM=\r\n=jvxr\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}, "gitHead": "ea685c187cb9b18e96cfbc42f23d265d8a41dc23", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/finalhandler.git", "type": "git"}, "_npmVersion": "8.3.1", "description": "Node.js final http responder", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"debug": "2.6.9", "unpipe": "~1.0.0", "parseurl": "~1.3.3", "statuses": "2.0.1", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "2.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "9.2.2", "eslint": "7.32.0", "supertest": "6.2.2", "safe-buffer": "5.2.1", "readable-stream": "2.3.6", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/finalhandler_1.2.0_1647997020243_0.1963700739078198", "host": "s3://npm-registry-packages"}}, "1.2.1": {"name": "finalhandler", "version": "1.2.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "finalhandler@1.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/finalhandler#readme", "bugs": {"url": "https://github.com/pillarjs/finalhandler/issues"}, "dist": {"shasum": "57c1ca3c6b41d6c20206bf5c76450a1b9e027ec1", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.2.1.tgz", "fileCount": 6, "integrity": "sha512-NpHDfiu6jURpO56pYkM6DEvnBEA9jNrwj4v05Vjs5hmdqEB2/kRA3wugct7BMyqYydjN+kWunMhtTn+itVmxpA==", "signatures": [{"sig": "MEUCIQDH+1tIlQvt/jZLq5lp1kouZnENxHH/xhQQFRm3G04x2AIgHE2XYr2/TmhBYMqjpeJ2jfUCXYHRqMqCxJdZ44rnjIA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18837}, "engines": {"node": ">= 0.8"}, "gitHead": "45cf67c481ae87ce522cf33bd768cb237f84dbe6", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --check-leaks test/", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-inspect": "mocha --reporter spec --inspect --inspect-brk test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/finalhandler.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Node.js final http responder", "directories": {}, "_nodeVersion": "22.2.0", "dependencies": {"debug": "2.6.9", "unpipe": "~1.0.0", "parseurl": "~1.3.3", "statuses": "2.0.1", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "2.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "10.0.0", "eslint": "7.32.0", "supertest": "6.2.4", "safe-buffer": "5.2.1", "readable-stream": "2.3.6", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.26.0", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/finalhandler_1.2.1_1725300751822_0.8947976432939122", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "finalhandler", "version": "2.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "finalhandler@2.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/finalhandler#readme", "bugs": {"url": "https://github.com/pillarjs/finalhandler/issues"}, "dist": {"shasum": "9d3c79156dfa798069db7de7dd53bc37546f564b", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-2.0.0.tgz", "fileCount": 6, "integrity": "sha512-MX6Zo2adDViYh+GcxxB1dpO43eypOGUOL12rLCOTMQv/DfIbpSJUy4oQIIZhVZkH9e+bZWKMon0XHFEju16tkQ==", "signatures": [{"sig": "MEUCIQDzcDSnxF3JRk+uG9HRZM3MYomorBkLdeV0JXz6SIkVKgIgeSMdI52GlIJG62KLJ45FGewBDD3w7q/Gvif1fqFMZko=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18997}, "engines": {"node": ">= 0.8"}, "gitHead": "42a0a2a14ff37fe0bd6413c5986f86fe7a1b2e7e", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --check-leaks test/", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-inspect": "mocha --reporter spec --inspect --inspect-brk test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/finalhandler.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Node.js final http responder", "directories": {}, "_nodeVersion": "22.2.0", "dependencies": {"debug": "2.6.9", "unpipe": "~1.0.0", "parseurl": "~1.3.3", "statuses": "2.0.1", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "2.4.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "15.1.0", "mocha": "10.0.0", "eslint": "7.32.0", "supertest": "6.2.4", "safe-buffer": "5.2.1", "readable-stream": "2.3.6", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.26.0", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/finalhandler_2.0.0_1725301873112_0.9452583062223616", "host": "s3://npm-registry-packages"}}, "1.3.0": {"name": "finalhandler", "version": "1.3.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "finalhandler@1.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/finalhandler#readme", "bugs": {"url": "https://github.com/pillarjs/finalhandler/issues"}, "dist": {"shasum": "f378d7aeae27cda454081088f5075edf662b215d", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.3.0.tgz", "fileCount": 6, "integrity": "sha512-bmwQPHFq/qiWp9CbNbCQU73klT+i5qwP/0tah3MGHp26vUt2YV4WkdtXRqOZo+H+4m38k8epFHOvO4BRuAuohw==", "signatures": [{"sig": "MEQCIF2BiHgQgF4/KgW6sszADLbBOocAp5nIkPz8IZJ7T1bkAiAC1cbNZR3lwzMKTzPg2noy5GhAJYGnpHckn7ed4Y301g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18966}, "engines": {"node": ">= 0.8"}, "gitHead": "b7a22a305ef508e165f16701b2bd45bce64de4cf", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --check-leaks test/", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-inspect": "mocha --reporter spec --inspect --inspect-brk test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/finalhandler.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Node.js final http responder", "directories": {}, "_nodeVersion": "22.2.0", "dependencies": {"debug": "2.6.9", "unpipe": "~1.0.0", "parseurl": "~1.3.3", "statuses": "2.0.1", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "2.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "10.0.0", "eslint": "7.32.0", "supertest": "6.2.4", "safe-buffer": "5.2.1", "readable-stream": "2.3.6", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.26.0", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/finalhandler_1.3.0_1725379029529_0.11871161481668624", "host": "s3://npm-registry-packages"}}, "1.3.1": {"name": "finalhandler", "version": "1.3.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "finalhandler@1.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/finalhandler#readme", "bugs": {"url": "https://github.com/pillarjs/finalhandler/issues"}, "dist": {"shasum": "0c575f1d1d324ddd1da35ad7ece3df7d19088019", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.3.1.tgz", "fileCount": 6, "integrity": "sha512-6BN9trH7bp3qvnrRyzsBz+g3lZxTNZTbVO2EV1CS0WIcDbawYVdYvGflME/9QP0h0pYlCDBCTjYa9nZzMDpyxQ==", "signatures": [{"sig": "MEQCIE+EO/mk2fLe3N7K3OOcWPOFd6nwxaSD8Of20x9DmgJTAiAiaTR92i7LmC4vR59PUGWFDc7zfYEG//Cx25uyMLsuFQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19034}, "engines": {"node": ">= 0.8"}, "gitHead": "fbbe1da05b22331a24e7eb8d891b447dfd8f7cf1", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --check-leaks test/", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-inspect": "mocha --reporter spec --inspect --inspect-brk test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/finalhandler.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Node.js final http responder", "directories": {}, "_nodeVersion": "22.2.0", "dependencies": {"debug": "2.6.9", "unpipe": "~1.0.0", "parseurl": "~1.3.3", "statuses": "2.0.1", "encodeurl": "~2.0.0", "escape-html": "~1.0.3", "on-finished": "2.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "10.0.0", "eslint": "7.32.0", "supertest": "6.2.4", "safe-buffer": "5.2.1", "readable-stream": "2.3.6", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.26.0", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/finalhandler_1.3.1_1726084302565_0.9367007895358868", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "finalhandler", "version": "2.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "finalhandler@2.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, {"name": "ulisesgascon", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/finalhandler#readme", "bugs": {"url": "https://github.com/pillarjs/finalhandler/issues"}, "dist": {"shasum": "72306373aa89d05a8242ed569ed86a1bff7c561f", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-2.1.0.tgz", "fileCount": 5, "integrity": "sha512-/t88Ty3d5JWQbWYgaOGCCYfXRwV1+be02WqYYlL6h0lEiUAMPM8o8qKGO01YIkOHzka2up08wvgYD0mDiI+q3Q==", "signatures": [{"sig": "MEQCIH8/+8yXa5lkOzPItu2ckGzsiv2m9TfoY7mBHAQfIGrXAiAFvw3jnh8xpTSmOhcXEX3i7oxnhLmx9KdqgHFOhP1pzw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17525}, "engines": {"node": ">= 0.8"}, "gitHead": "e08969ba56c81659bdc8b1cb68b1508ccf6b6ac2", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --check-leaks test/", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-inspect": "mocha --reporter spec --inspect --inspect-brk test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/finalhandler.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Node.js final http responder", "directories": {}, "_nodeVersion": "23.5.0", "dependencies": {"debug": "^4.4.0", "parseurl": "^1.3.3", "statuses": "^2.0.1", "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "on-finished": "^2.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^17.1.0", "mocha": "^11.0.1", "eslint": "7.32.0", "supertest": "^7.0.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.26.0", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/finalhandler_2.1.0_1741186180467_0.26754933624506627", "host": "s3://npm-registry-packages-npm-production"}}}, "time": {"created": "2014-06-06T02:46:23.972Z", "modified": "2025-03-05T14:51:07.880Z", "0.0.0": "2014-06-06T02:46:23.972Z", "0.0.1": "2014-06-17T03:16:02.504Z", "0.0.2": "2014-06-19T22:45:27.016Z", "0.0.3": "2014-07-12T02:55:27.900Z", "0.1.0": "2014-07-17T00:46:33.245Z", "0.2.0": "2014-09-04T02:47:49.096Z", "0.3.0": "2014-09-18T08:05:47.995Z", "0.3.1": "2014-10-17T03:04:23.148Z", "0.3.2": "2014-10-23T03:43:37.882Z", "0.3.3": "2015-01-02T03:41:17.238Z", "0.3.4": "2015-03-16T01:50:44.350Z", "0.3.5": "2015-04-23T02:25:52.619Z", "0.3.6": "2015-05-12T03:53:24.990Z", "0.4.0": "2015-06-14T21:15:57.149Z", "0.4.1": "2015-12-02T18:07:45.449Z", "0.5.0": "2016-06-15T22:10:57.784Z", "0.5.1": "2016-11-13T06:23:35.753Z", "1.0.0": "2017-02-16T07:06:45.834Z", "1.0.1": "2017-03-22T06:03:01.761Z", "1.0.2": "2017-04-22T23:20:33.617Z", "1.0.3": "2017-05-17T05:05:05.352Z", "1.0.4": "2017-08-04T04:01:28.872Z", "1.0.5": "2017-09-16T03:03:46.811Z", "1.0.6": "2017-09-22T18:27:08.637Z", "1.1.0": "2017-09-25T03:53:05.677Z", "1.1.1": "2018-03-06T17:36:44.075Z", "1.1.2": "2019-05-10T02:04:53.493Z", "1.2.0": "2022-03-23T00:57:00.583Z", "1.2.1": "2024-09-02T18:12:31.989Z", "2.0.0": "2024-09-02T18:31:13.244Z", "1.3.0": "2024-09-03T15:57:09.667Z", "1.3.1": "2024-09-11T19:51:42.759Z", "2.1.0": "2025-03-05T14:49:40.649Z"}, "bugs": {"url": "https://github.com/pillarjs/finalhandler/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/pillarjs/finalhandler#readme", "repository": {"url": "git+https://github.com/pillarjs/finalhandler.git", "type": "git"}, "description": "Node.js final http responder", "maintainers": [{"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, {"name": "ulisesgascon", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# finalhandler\n\n[![NPM Version][npm-image]][npm-url]\n[![NPM Downloads][downloads-image]][downloads-url]\n[![Node.js Version][node-image]][node-url]\n[![Build Status][github-actions-ci-image]][github-actions-ci-url]\n[![Test Coverage][coveralls-image]][coveralls-url]\n\nNode.js function to invoke as the final step to respond to HTTP request.\n\n## Installation\n\nThis is a [Node.js](https://nodejs.org/en/) module available through the\n[npm registry](https://www.npmjs.com/). Installation is done using the\n[`npm install` command](https://docs.npmjs.com/getting-started/installing-npm-packages-locally):\n\n```sh\n$ npm install finalhandler\n```\n\n## API\n\n```js\nvar finalhandler = require('finalhandler')\n```\n\n### finalhandler(req, res, [options])\n\nReturns function to be invoked as the final step for the given `req` and `res`.\nThis function is to be invoked as `fn(err)`. If `err` is falsy, the handler will\nwrite out a 404 response to the `res`. If it is truthy, an error response will\nbe written out to the `res` or `res` will be terminated if a response has already\nstarted.\n\nWhen an error is written, the following information is added to the response:\n\n  * The `res.statusCode` is set from `err.status` (or `err.statusCode`). If\n    this value is outside the 4xx or 5xx range, it will be set to 500.\n  * The `res.statusMessage` is set according to the status code.\n  * The body will be the HTML of the status code message if `env` is\n    `'production'`, otherwise will be `err.stack`.\n  * Any headers specified in an `err.headers` object.\n\nThe final handler will also unpipe anything from `req` when it is invoked.\n\n#### options.env\n\nBy default, the environment is determined by `NODE_ENV` variable, but it can be\noverridden by this option.\n\n#### options.onerror\n\nProvide a function to be called with the `err` when it exists. Can be used for\nwriting errors to a central location without excessive function generation. Called\nas `onerror(err, req, res)`.\n\n## Examples\n\n### always 404\n\n```js\nvar finalhandler = require('finalhandler')\nvar http = require('http')\n\nvar server = http.createServer(function (req, res) {\n  var done = finalhandler(req, res)\n  done()\n})\n\nserver.listen(3000)\n```\n\n### perform simple action\n\n```js\nvar finalhandler = require('finalhandler')\nvar fs = require('fs')\nvar http = require('http')\n\nvar server = http.createServer(function (req, res) {\n  var done = finalhandler(req, res)\n\n  fs.readFile('index.html', function (err, buf) {\n    if (err) return done(err)\n    res.setHeader('Content-Type', 'text/html')\n    res.end(buf)\n  })\n})\n\nserver.listen(3000)\n```\n\n### use with middleware-style functions\n\n```js\nvar finalhandler = require('finalhandler')\nvar http = require('http')\nvar serveStatic = require('serve-static')\n\nvar serve = serveStatic('public')\n\nvar server = http.createServer(function (req, res) {\n  var done = finalhandler(req, res)\n  serve(req, res, done)\n})\n\nserver.listen(3000)\n```\n\n### keep log of all errors\n\n```js\nvar finalhandler = require('finalhandler')\nvar fs = require('fs')\nvar http = require('http')\n\nvar server = http.createServer(function (req, res) {\n  var done = finalhandler(req, res, { onerror: logerror })\n\n  fs.readFile('index.html', function (err, buf) {\n    if (err) return done(err)\n    res.setHeader('Content-Type', 'text/html')\n    res.end(buf)\n  })\n})\n\nserver.listen(3000)\n\nfunction logerror (err) {\n  console.error(err.stack || err.toString())\n}\n```\n\n## License\n\n[MIT](LICENSE)\n\n[npm-image]: https://img.shields.io/npm/v/finalhandler.svg\n[npm-url]: https://npmjs.org/package/finalhandler\n[node-image]: https://img.shields.io/node/v/finalhandler.svg\n[node-url]: https://nodejs.org/en/download\n[coveralls-image]: https://img.shields.io/coveralls/pillarjs/finalhandler.svg\n[coveralls-url]: https://coveralls.io/r/pillarjs/finalhandler?branch=master\n[downloads-image]: https://img.shields.io/npm/dm/finalhandler.svg\n[downloads-url]: https://npmjs.org/package/finalhandler\n[github-actions-ci-image]: https://github.com/pillarjs/finalhandler/actions/workflows/ci.yml/badge.svg\n[github-actions-ci-url]: https://github.com/pillarjs/finalhandler/actions/workflows/ci.yml\n", "readmeFilename": "README.md", "users": {"nex": true, "akiva": true, "eyson": true, "daizch": true, "monjer": true, "quafoo": true, "ziflex": true, "chaoliu": true, "kistoryg": true, "leonzhao": true, "jetthiago": true, "larrychen": true, "mojaray2k": true, "tampham47": true, "princetoad": true, "qqqppp9998": true, "ridermansb": true, "simplyianm": true, "wangnan0610": true}}