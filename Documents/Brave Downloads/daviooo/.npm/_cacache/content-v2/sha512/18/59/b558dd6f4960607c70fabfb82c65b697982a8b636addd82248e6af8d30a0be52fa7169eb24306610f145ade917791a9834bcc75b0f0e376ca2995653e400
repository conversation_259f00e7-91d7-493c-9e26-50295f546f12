{"_id": "is-date-object", "_rev": "15-8ff11ac1e7df2848ab5b2119bad91ab9", "name": "is-date-object", "dist-tags": {"latest": "1.1.0"}, "versions": {"1.0.0": {"name": "is-date-object", "version": "1.0.0", "keywords": ["Date", "ES6", "toStringTag", "@@toStringTag", "Date object"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "is-date-object@1.0.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/is-date-object", "bugs": {"url": "https://github.com/ljharb/is-date-object/issues"}, "dist": {"shasum": "0510d9e2831904c731b69b54a6fdc41a1b48bc17", "tarball": "https://registry.npmjs.org/is-date-object/-/is-date-object-1.0.0.tgz", "integrity": "sha512-gQSVHmbHatDOe13X6hIFAxI9bZFtffQZA9qFMiZjsN2N5zRQB7Pycc6QbfpabNP2DH/H6nTlcPzaveU890P9IQ==", "signatures": [{"sig": "MEYCIQC6vYeMBv9WfrTGvsPzskxu/++/XB0Qv1EyD+8Az7P50AIhAP9IrJ01zWYq6g60y5Wmj87GqycyroLmkD/0TOfVHzd8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "0510d9e2831904c731b69b54a6fdc41a1b48bc17", "engines": {"node": ">= 0.4"}, "gitHead": "8442bb71a894289ac2ff31b4abe65488fb63bb3a", "scripts": {"jscs": "jscs test.js *.js", "lint": "npm run jscs && npm run eslint", "test": "npm run lint && node --harmony --es-staging test.js && npm run security", "eslint": "eslint test.js *.js", "eccheck": "editorconfig-tools check *.js **/*.js > /dev/null", "coverage": "covert test.js", "security": "nsp package", "coverage-quiet": "covert test.js --quiet"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/is-date-object.git", "type": "git"}, "_npmVersion": "2.3.0", "description": "Is this value a JS Date object? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "directories": {}, "_nodeVersion": "1.0.4", "dependencies": {}, "devDependencies": {"is": "~2.2.0", "nsp": "~1.0.0", "jscs": "~1.10.0", "tape": "~3.4.0", "covert": "1.0.0", "eslint": "~0.12.0", "semver": "~4.2.0", "foreach": "~2.0.5", "indexof": "~0.0.1", "editorconfig-tools": "~0.0.1"}}, "1.0.1": {"name": "is-date-object", "version": "1.0.1", "keywords": ["Date", "ES6", "toStringTag", "@@toStringTag", "Date object"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "is-date-object@1.0.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/is-date-object#readme", "bugs": {"url": "https://github.com/ljharb/is-date-object/issues"}, "dist": {"shasum": "9aa20eb6aeebbff77fbd33e74ca01b33581d3a16", "tarball": "https://registry.npmjs.org/is-date-object/-/is-date-object-1.0.1.tgz", "integrity": "sha512-P5rExV1phPi42ppoMWy7V63N3i173RY921l4JJ7zonMSxK+OWGPj76GD+cUKUb68l4vQXcJp2SsG+r/A4ABVzg==", "signatures": [{"sig": "MEQCIEfP0HcRxl2EV4J6FkDL+yYUEToVEgkHhHwqJyH29Hz3AiB/4AQ7zlz5/jrRdGQZLCg2vK+xQBq94uqn5CYiVLUL1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "9aa20eb6aeebbff77fbd33e74ca01b33581d3a16", "engines": {"node": ">= 0.4"}, "gitHead": "b03d8e939fec4f139f135a01ff2a3229a424e8fd", "scripts": {"jscs": "jscs test.js *.js", "lint": "npm run jscs && npm run eslint", "test": "npm run lint && node --harmony --es-staging test.js && npm run security", "eslint": "eslint test.js *.js", "coverage": "covert test.js", "security": "nsp package", "coverage-quiet": "covert test.js --quiet"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/is-date-object.git", "type": "git"}, "_npmVersion": "2.14.4", "description": "Is this value a JS Date object? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "directories": {}, "_nodeVersion": "4.1.1", "dependencies": {}, "devDependencies": {"is": "^3.1.0", "nsp": "^1.1.0", "jscs": "^2.1.1", "tape": "^4.2.0", "covert": "^1.1.0", "eslint": "^1.5.1", "semver": "^5.0.3", "foreach": "^2.0.5", "indexof": "^0.0.1", "@ljharb/eslint-config": "^1.2.0"}}, "1.0.2": {"name": "is-date-object", "version": "1.0.2", "keywords": ["Date", "ES6", "toStringTag", "@@toStringTag", "Date object"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "is-date-object@1.0.2", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/is-date-object#readme", "bugs": {"url": "https://github.com/ljharb/is-date-object/issues"}, "dist": {"shasum": "bda736f2cd8fd06d32844e7743bfa7494c3bfd7e", "tarball": "https://registry.npmjs.org/is-date-object/-/is-date-object-1.0.2.tgz", "fileCount": 11, "integrity": "sha512-USlDT524woQ08aoZFzh3/Z6ch9Y/EWXEHQ/AaRN0SkKq4t2Jw2R2339tSXmwuVoY7LLlBCbOIlx2myP/L5zk0g==", "signatures": [{"sig": "MEYCIQD2vaMCETFVeWeQ2XAk+YjyZIp2tlH7wvTLZRNpsvryeQIhAPkWBkeaUBND9uFGeFubLw1Dx9G0C3DZgy5Ag4lWMD34", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20447, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd+voMCRA9TVsSAnZWagAArB4P/RW9EG79tl03oKyvQoC2\nx2qQwdsHGiqiOopfUokCFm6nM9dCeJ/Zzt+66eY5HRw7RtKrZdB2FjpeXkT6\n3rnLUaUe9DKNhqmXxRFSiU8bXEf4JBSEyCG6u98PMCksTe4o1sU0mm9uLbIn\nuX/il+J7PzlVPskFOEcE4AIvva2czLrezLjtEiPGtXfThUnOf+GO99NlfQgt\nC9NTIFF1XWHhBO/ehSQ4wHiUAxVffVaN01eP8z87tXrprZbVYjvfy2pIdfqm\nWC2nuPbuEALD7G5WgM9vwsPkkZMiQ6FtXk/vk765MnKQxCCSpeV5YvkKX4xV\n7fR1MBkfdI2cWmLwPPEvLzpCJ28Bah6XnFsxlG3K48JRSKPph43ZBfxuSj5j\nPDhBDyar6TU4TPMM3lgOd8LZmt7foXAbmKGr0oydvx0E14m/2tEOWwOELDNm\nAjYbVdo0f88fiMCALgMxSGgTryIwPqQ7YEQAjthHRS/Kz1iGkyVGM+LxNX2K\nSXbfwnyJLsbNqWwY4fOLdM7Ot/bzK130SzQNrPpc1eJKbHKjBGz5rWIfbUrv\nhHQy1y4eTF5NNQ/rBHRX2gRnOt7nOypUQvapS3RjP9pngJsJ5+ybmSxYL2RP\neb/WCDN2YBU5Bt3JQ8WO8ZZ0MpxlsvWX/pQ/jfXXHvEVTiv69BXO8eaEHc9G\nYGI5\r\n=vpR6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "e7e895d71f6820547627ce21a719632c17f3700f", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "coverage": "covert test/index.js", "posttest": "npx aud", "prepublish": "safe-publish-latest", "tests-only": "node --harmony --es-staging test", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/is-date-object.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Is this value a JS Date object? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "directories": {}, "_nodeVersion": "13.5.0", "dependencies": {}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"is": "^3.3.0", "tape": "^4.12.0", "covert": "^1.1.1", "eslint": "^6.7.2", "foreach": "^2.0.5", "indexof": "^0.0.1", "auto-changelog": "^1.16.2", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^15.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/is-date-object_1.0.2_1576729099723_0.5447399920772513", "host": "s3://npm-registry-packages"}}, "1.0.3": {"name": "is-date-object", "version": "1.0.3", "keywords": ["Date", "ES6", "toStringTag", "@@toStringTag", "Date object"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "is-date-object@1.0.3", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-date-object#readme", "bugs": {"url": "https://github.com/inspect-js/is-date-object/issues"}, "dist": {"shasum": "4c0802ae9c8097939ea8001eaae3c502f3dbe72f", "tarball": "https://registry.npmjs.org/is-date-object/-/is-date-object-1.0.3.tgz", "fileCount": 12, "integrity": "sha512-tDp<PERSON><PERSON>nNcy2Yw3lNSepK3Wdw1RnXLcIVienz6Ou631Acl15cJyRWK4dgA1vCmOEgIbtOV0W7MHg+AR2Gdg1NXQ==", "signatures": [{"sig": "MEUCIB4gpudJMzJsuuM0+Z35LF7wj85o1jS0St4oNWfzBQJyAiEA5ZM+QukR83zNHIJyOHzbKNRdErUUg8T5VNGkf55ddMQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23666, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkxtiCRA9TVsSAnZWagAA4aAQAJY52tUQZMUY7FfeVBfo\n4Atn0ltSfxIAnhYzeQFxxuz7DerfDfRw4+Za+UiM62OsjNAhFqNsca/qZmNI\n3Hx8Dy7mmte8C1d9haKkNeOf/boqqb3g/EyUeuRQCVvlCsOT4mbgVp69EkGX\nMMtsEJb5hMb05gC2EclC6A6lKD+Yk7VfjI3zJ8NREdxb+hIcgvM0lzS9nAZY\nZMCDfE7n3UHoyfD0n+pDNQTv4kJC9rNaOPSupJuyGQTHzkTHjSNTePAtpDUq\nxH9mLHYwha9NsuJxN2v0LvVrVWzQodHGJ8CtKmdhdgMNaGdVBa84FdpEv15q\nER5Gt9uCmRP6fycdyshBQjs0ry1xYKTbc5YrzNrBY/ZpPGMCIUEre+pkQm5f\nTXXze6+mYi8G/g5QGRJX0eBRovOB0CTPLPJDMc+VIMJFVx4XFmnNN83pEXg7\nGdEY43IqafPbN+DqzKA/lKCu5aL7bl9EKCpTsXCjLWvi/Jp9FGYsd/2g7Lh5\nppPtuxf4utaIlDN82KnY/JVO+EaocE9r2GQ+MhE54u7d57pVoFpvTAsxAgtb\nI9KKuT0l35eLoCA40Tgf6l8pIOmf5sBu4DkwUavDBRUXf+CwKd9i3MSiAuPM\nwtbMWTCjjdm9rhi97Dj+ZNk8MLBR039GfMLaCXoRVT9ewVumL39r0Joerz9H\n77uu\r\n=zDbq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "1a6c7cd1ff9277fc284b65592cd25ee557f14e00", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/inspect-js/is-date-object.git", "type": "git"}, "_npmVersion": "7.10.0", "description": "Is this value a JS Date object? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "directories": {}, "_nodeVersion": "16.0.0", "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"is": "^3.3.0", "aud": "^1.1.5", "nyc": "^10.3.2", "tape": "^5.2.2", "eslint": "^7.25.0", "foreach": "^2.0.5", "indexof": "^0.0.1", "auto-changelog": "^2.2.1", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-date-object_1.0.3_1620253538326_0.22696927411038126", "host": "s3://npm-registry-packages"}}, "1.0.4": {"name": "is-date-object", "version": "1.0.4", "keywords": ["Date", "ES6", "toStringTag", "@@toStringTag", "Date object"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "is-date-object@1.0.4", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-date-object#readme", "bugs": {"url": "https://github.com/inspect-js/is-date-object/issues"}, "dist": {"shasum": "550cfcc03afada05eea3dd30981c7b09551f73e5", "tarball": "https://registry.npmjs.org/is-date-object/-/is-date-object-1.0.4.tgz", "fileCount": 12, "integrity": "sha512-/b4ZVsG7Z5XVtIxs/h9W8nvfLgSAyKYdtGWQLbqy6jA1icmgjf8WCoTKgeS4wy5tYaPePouzFMANbnj94c2Z+A==", "signatures": [{"sig": "MEQCIDXZY3iaRF8Ql3KUGZi5mDuv/eZ5NuBR6FB/6xqX/lu+AiBECJLVZEuLbEe0DPl8AoWt9KYDoiW1BOhvC9HmzlIUSA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24343, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgliCHCRA9TVsSAnZWagAAylIP/1WgjXaRq1sIiGQhV+Mu\nPMcEI+oVyUGCx1jQ9BU8WVtlUvbAAcGV7AACFCMdYtJk7HzEt4O7tZjaSzad\nkmjmWUj+EnFGhmWQw19NGB1KK8yenKeZdt2I8L+F8+hznmR5Ze6oWDnsAaX3\nuIt1a3LglUSUIHswyXKS4CpZ0omadBQ+sTGO63wEOxP399F7CNMc3qM8rm1I\nKovEHpXnmkczag3QADTfRVjhZAdq8VC89JchN1qWHSYQ3uABTlQ+IV+Rm9lD\nuRDkgQkJlUPYDu9I+bbLGGD+z1t1YFiernydC/nZjSJokAME+X7yVG4SfmQ1\nKcmgqxNL+WE8Apf8qIZ5tTNLSG1grk8xhJA1AG0fUpdyury0NhsZtXQSLnG8\ndJvHRVROQl7wY0oVBgPXYoGCisdGou9LJzzR8fnr85babE3hGwKlX7paCruX\nTsJmqxfxP8rJ0Rm1tf5yqCV+5/sjhLd4/MUO+dLBdRAXvCQvkgTKy1mUib5l\n8cB/wn2L5mdLJ7Y3aSl57XL5as4X/3+KlYyuYMRV8jOk0H6qlalbDSt7UH5T\nxg94cLbGV/Xi6Fa9JaaUi/G1qOo4pojwTgQTz1I0QQKs4Vh9pV3vMlR4t+4r\n9PFUj1FBz9pXR2FSUhpzOACsFnJAfQ0ZGGACgpUQ8u01qDsTbaIViSZVcHcp\nthZZ\r\n=KrHf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "109b0b23e2fb510cd6b0cbf08d24e5f9ea3528c3", "scripts": {"lint": "eslint .", "test": "npm run tests-only && npm run test:corejs", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "test:corejs": "nyc tape test-corejs.js", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/inspect-js/is-date-object.git", "type": "git"}, "_npmVersion": "7.10.0", "description": "Is this value a JS Date object? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "directories": {}, "_nodeVersion": "16.0.0", "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"is": "^3.3.0", "aud": "^1.1.5", "nyc": "^10.3.2", "tape": "^5.2.2", "eslint": "^7.26.0", "core-js": "^3.12.0", "foreach": "^2.0.5", "indexof": "^0.0.1", "auto-changelog": "^2.2.1", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-date-object_1.0.4_1620451463248_0.8424227028571616", "host": "s3://npm-registry-packages"}}, "1.0.5": {"name": "is-date-object", "version": "1.0.5", "keywords": ["Date", "ES6", "toStringTag", "@@toStringTag", "Date object"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "is-date-object@1.0.5", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-date-object#readme", "bugs": {"url": "https://github.com/inspect-js/is-date-object/issues"}, "dist": {"shasum": "0841d5536e724c25597bf6ea62e1bd38298df31f", "tarball": "https://registry.npmjs.org/is-date-object/-/is-date-object-1.0.5.tgz", "fileCount": 11, "integrity": "sha512-9YQaSxsAiSwcvS33MBk3wTCVnWK+HhF8VZR2jRxehM16QcVOdHqPn4VPHmRK4lSr38n9JriurInLcP90xsYNfQ==", "signatures": [{"sig": "MEQCIEhS/6srTi4R/SAG82JHtZLLhUVSYTTORat0X9cu/Z9FAiAVv6omiyXZZSReq+aHMSvSP/U87UqXxVa3IIpW4w5wWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20793, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhDFafCRA9TVsSAnZWagAAWicP/1R0Rf7aq/zKMKTW6okI\nPK7iV6QSxBYJdoY0ZLLpNvncRYm2TJFjbhgIazi9Tm8Cy/6bVdFwF53rzlZg\nahiCJWVqyeQ/04y94+KGu0OR8HXmiKed+J64QS4SQ6AahsUE05Ipkd/6NIUs\ngqmRqiGZW0YzfQ35JoqSyhWFtxppG+HDAbUM+iPDxNCmBdtRnmsXb0ou/6Iy\n1MtusIyH3V6sHtTqlZem/w0ZVgpYGWT8MVnVAVA9GjbVza2vFGRnLrFjvMrm\nZprGcHl/t1k1gtl683TETMhHeaBJxEHq4L7fGDBAxUsuMfBhQTiSwCjGF0y8\nJ+nj5xftJWLJ9blc3+9t23nzJCvhgYI7/AZom7G3TwWvfdTNcSPdTTax+pJ8\nQaT66kLQnwLxmANkFTHHZ3TU63L//4o0KTGyUr7cw7aPKJ6y/xAhZiaAqNcJ\nF/5NnpV7rh8BSbv+GFEjuAvo+W3HGOBCc40fes9G47FHT46ayfuzq8oLvkSs\nTEyGr/DGQR/byFo/NnJw+lX5SzrlSBl94VwXa09NSCqxtrJEMFintgMrPnuB\no0HYv+CAmAPGcFbyf/ul3Qf2KdWh+lMcdzIVsZGAf8TQlt5+JarW3OzhfiBi\nn65nJg+ZNizFqj/0MFD/l5VMQM0MLUkXd/xlwLY2ZTQmxsSllEiU/3xVW4WA\ne8lI\r\n=yxhK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "61eab012a36b7b5141f7e7dd09a6cdd11008668f", "scripts": {"lint": "eslint .", "test": "npm run tests-only && npm run test:corejs", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "test:corejs": "nyc tape test-corejs.js", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/inspect-js/is-date-object.git", "type": "git"}, "_npmVersion": "7.19.1", "description": "Is this value a JS Date object? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "directories": {}, "_nodeVersion": "16.6.0", "dependencies": {"has-tostringtag": "^1.0.0"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"is": "^3.3.0", "aud": "^1.1.5", "nyc": "^10.3.2", "tape": "^5.3.0", "eslint": "^7.32.0", "core-js": "^3.12.0", "foreach": "^2.0.5", "indexof": "^0.0.1", "auto-changelog": "^2.3.0", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-date-object_1.0.5_1628198559216_0.8308505305234615", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "is-date-object", "version": "1.1.0", "author": {"name": "<PERSON>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "description": "Is this value a JS Date object? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "license": "MIT", "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "sideEffects": false, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run lint", "test": "npm run tests-only && npm run test:corejs", "tests-only": "nyc tape 'test/**/*.js'", "test:corejs": "nyc tape test-corejs.js", "posttest": "npx npm@'>= 10.2' audit --production", "lint": "eslint --ext=js,mjs .", "postlint": "tsc && attw -P", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/inspect-js/is-date-object.git"}, "keywords": ["Date", "ES6", "toStringTag", "@@toStringTag", "Date object"], "dependencies": {"call-bound": "^1.0.2", "has-tostringtag": "^1.0.2"}, "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/core-js": "^2.5.8", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "core-js": "^3.39.0", "encoding": "^0.1.13", "eslint": "=8.8.0", "in-publish": "^2.0.1", "indexof": "^0.0.1", "is": "^3.3.0", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "^5.8.0-dev.20241212"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows", "test-corejs.js"]}, "_id": "is-date-object@1.1.0", "gitHead": "a22f8d7d60cbddb809da08e4b47ed510b40a2dd9", "types": "./index.d.ts", "bugs": {"url": "https://github.com/inspect-js/is-date-object/issues"}, "homepage": "https://github.com/inspect-js/is-date-object#readme", "_nodeVersion": "23.4.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-PwwhEakHVKTdRNVOw+/Gyh0+MzlCl4R6qKvkhuvLtPMggI1WAHt9sOwZxQLSGpUaDnrdyDsomoRgNnCfKNSXXg==", "shasum": "ad85541996fc7aa8b2729701d27b7319f95d82f7", "tarball": "https://registry.npmjs.org/is-date-object/-/is-date-object-1.1.0.tgz", "fileCount": 12, "unpackedSize": 24172, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFOFgyeZuf7oGp3domcAID4Yz+uoC04ao8jDqazKyfnnAiB8rkpf9tdSuYtSWPTxmYek0WJveaWtVzTuw6mANbw2uA=="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/is-date-object_1.1.0_1734048247957_0.9644553309927046"}, "_hasShrinkwrap": false}}, "time": {"created": "2015-01-28T22:24:27.244Z", "modified": "2024-12-13T00:04:08.342Z", "1.0.0": "2015-01-28T22:24:27.244Z", "1.0.1": "2015-09-27T14:34:57.369Z", "1.0.2": "2019-12-19T04:18:19.845Z", "1.0.3": "2021-05-05T22:25:38.583Z", "1.0.4": "2021-05-08T05:24:23.361Z", "1.0.5": "2021-08-05T21:22:39.367Z", "1.1.0": "2024-12-13T00:04:08.145Z"}, "bugs": {"url": "https://github.com/inspect-js/is-date-object/issues"}, "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/inspect-js/is-date-object#readme", "keywords": ["Date", "ES6", "toStringTag", "@@toStringTag", "Date object"], "repository": {"type": "git", "url": "git://github.com/inspect-js/is-date-object.git"}, "description": "Is this value a JS Date object? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "readme": "# is-date-object <sup>[![Version Badge][2]][1]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![dependency status][5]][6]\n[![dev dependency status][7]][8]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][11]][1]\n\nIs this value a JS Date object? This module works cross-realm/iframe, and despite ES6 @@toStringTag.\n\n## Example\n\n```js\nvar isDate = require('is-date-object');\nvar assert = require('assert');\n\nassert.notOk(isDate(undefined));\nassert.notOk(isDate(null));\nassert.notOk(isDate(false));\nassert.notOk(isDate(true));\nassert.notOk(isDate(42));\nassert.notOk(isDate('foo'));\nassert.notOk(isDate(function () {}));\nassert.notOk(isDate([]));\nassert.notOk(isDate({}));\nassert.notOk(isDate(/a/g));\nassert.notOk(isDate(new RegExp('a', 'g')));\n\nassert.ok(isDate(new Date()));\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n[1]: https://npmjs.org/package/is-date-object\n[2]: https://versionbadg.es/inspect-js/is-date-object.svg\n[5]: https://david-dm.org/inspect-js/is-date-object.svg\n[6]: https://david-dm.org/inspect-js/is-date-object\n[7]: https://david-dm.org/inspect-js/is-date-object/dev-status.svg\n[8]: https://david-dm.org/inspect-js/is-date-object#info=devDependencies\n[11]: https://nodei.co/npm/is-date-object.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/is-date-object.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/is-date-object.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=is-date-object\n[codecov-image]: https://codecov.io/gh/inspect-js/is-date-object/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/inspect-js/is-date-object/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/inspect-js/is-date-object\n[actions-url]: https://github.com/inspect-js/is-date-object/actions\n", "readmeFilename": "README.md", "users": {"illuminator": true, "ognjen.jevremovic": true}}