{"_id": "is-boolean-object", "_rev": "13-98b4dace827a9021dafe59d2747c3c64", "name": "is-boolean-object", "dist-tags": {"latest": "1.2.2"}, "versions": {"1.0.0": {"name": "is-boolean-object", "version": "1.0.0", "keywords": ["Boolean", "ES6", "toStringTag", "@@toStringTag", "Boolean object", "true", "false", "is-boolean"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "is-boolean-object@1.0.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/is-boolean-object#readme", "bugs": {"url": "https://github.com/ljharb/is-boolean-object/issues"}, "dist": {"shasum": "98f8b28030684219a95f375cfbd88ce3405dff93", "tarball": "https://registry.npmjs.org/is-boolean-object/-/is-boolean-object-1.0.0.tgz", "integrity": "sha512-/J8bOrl0lo9um26WW+h0wd7u+2JF+fwYFlGpksurIEzqkkjzEu8SNY7uKMfYbgji3jNIl532fsew9dN+6UN1sA==", "signatures": [{"sig": "MEUCIBPCYXMCKEOqPQXUnE3YDbIPTuwOrRZ5eBMiTqr7NY9hAiEA+4cWez3yHwzu7ZKYLhNqJ314paGMW+NnD2SadIQlXa0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "98f8b28030684219a95f375cfbd88ce3405dff93", "engines": {"node": ">= 0.4"}, "gitHead": "85ba7e53d6e1f019743264b601c5ea8cd99a1c8f", "scripts": {"jscs": "jscs test.js *.js", "lint": "npm run jscs && npm run eslint", "test": "npm run lint && node --harmony --es-staging test.js && npm run security", "eslint": "eslint test.js *.js", "eccheck": "editorconfig-tools check *.js **/*.js > /dev/null", "coverage": "covert test.js", "security": "nsp package", "coverage-quiet": "covert test.js --quiet"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/is-boolean-object.git", "type": "git"}, "_npmVersion": "2.8.3", "description": "Is this value a JS Boolean? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "directories": {}, "_nodeVersion": "1.8.1", "dependencies": {}, "devDependencies": {"is": "^3.0.1", "nsp": "^1.0.1", "jscs": "^1.12.0", "tape": "^4.0.0", "covert": "^1.0.1", "eslint": "^0.20.0", "semver": "^4.3.3", "foreach": "^2.0.5", "indexof": "^0.0.1", "replace": "^0.3.0", "editorconfig-tools": "^0.1.1"}}, "1.0.1": {"name": "is-boolean-object", "version": "1.0.1", "keywords": ["Boolean", "ES6", "toStringTag", "@@toStringTag", "Boolean object", "true", "false", "is-boolean"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-boolean-object@1.0.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/is-boolean-object#readme", "bugs": {"url": "https://github.com/ljharb/is-boolean-object/issues"}, "dist": {"shasum": "10edc0900dd127697a92f6f9807c7617d68ac48e", "tarball": "https://registry.npmjs.org/is-boolean-object/-/is-boolean-object-1.0.1.tgz", "fileCount": 10, "integrity": "sha512-TqZuVwa/sppcrhUCAYkGBk7w0yxfQQnxq28fjkO53tnK9FQXmdwz2JS5+GjsWQ6RByES1K40nI+yDic5c9/aAQ==", "signatures": [{"sig": "MEYCIQDXJlyprysecuo81seb5O0OX/tLQwCtIpyQ0IcS2NqZRAIhAMBkk5GP9PiiYw3Yvwr6tfpcqn99afsyw+q9U/Wsq9bu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17141, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd+8JCCRA9TVsSAnZWagAAP6oQAKKS5/ys2qTIqbqC7Ein\naYRuLUXXIJm53W8Dy3OQRFnhfra94NVxOpMcNCjawnGwFtolYdp8xSdXxB+j\nFEKMA4BjTksQMQz8qMNt/QrIOY4qN+Vcja9qBuK37ntMxT9Wo6w0VRVNYux5\nmGFYMJvA8ilWDjQPfQn36rkl0UpxT48yNErKrNY4+hsgqOukM4oOIDsItvcM\njjgN7SCI4L4FOCJoNHoTtIkpo9oTXllMK+vi5z0vwLGS43DIQjcyrRAw7mBn\ngzOApIXZ86Tv+R4tQJGTdDQyy2zFFPmKrTvlB4V9lcbyU9xdp/R03htcmIUl\nlr75MkfqAYI/iBua+0ro/Dlncwv4+j6PE4NfR/x+uHXiY95IzTkqoAbC3qmG\n7LCtUcNZAmePK8reGjFBCS/JF/Rg1zXq2xNscnikCu5C4CN4j1GgnA5lbeMF\nAsYJVMQbQMbGeZm39ixWfBhtlMh6vV0lXNrSp6IkclbcPNd/6QrKwcdw6X3d\nf2WHA07v8TRVVrfZB3Z1eASJO9RB3Zln6yleL4UDIucyMvDkNY1lNod+7kW7\n9Bgb+esGub22MYeL/qusze1y/SkChN1KKNqR7y37q5W9GI7drJ5yBxDdhr8/\n7UrPcL30BDmFnZo0t6cJL2So4FNM9dRRFc+kB3JgbNEr2ATvdhqTHK5KD0Ir\ngjA3\r\n=smg0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "a818fc43d7338b44a4b13e3a501a5c2b3fac5fac", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "eccheck": "eclint check *.js **/*.js > /dev/null", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "coverage": "covert test/index.js", "posttest": "npx aud --production", "prepublish": "safe-publish-latest", "tests-only": "node --harmony --es-staging test", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/is-boolean-object.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Is this value a JS Boolean? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "directories": {}, "_nodeVersion": "13.5.0", "dependencies": {}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"is": "^3.3.0", "tape": "^4.12.0", "covert": "^1.1.1", "eclint": "^2.8.1", "eslint": "^6.7.2", "foreach": "^2.0.5", "indexof": "^0.0.1", "auto-changelog": "^1.16.2", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^15.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/is-boolean-object_1.0.1_1576780354117_0.6465466096641279", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "is-boolean-object", "version": "1.1.0", "keywords": ["Boolean", "ES6", "toStringTag", "@@toStringTag", "Boolean object", "true", "false", "is-boolean"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-boolean-object@1.1.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/is-boolean-object#readme", "bugs": {"url": "https://github.com/ljharb/is-boolean-object/issues"}, "dist": {"shasum": "e2aaad3a3a8fca34c28f6eee135b156ed2587ff0", "tarball": "https://registry.npmjs.org/is-boolean-object/-/is-boolean-object-1.1.0.tgz", "fileCount": 18, "integrity": "sha512-a7Uprx8UtD+HWdyYwnD1+ExtTgqQtD2k/1yJgtXP6wnMm8byhkoTZRl+95LLThpzNZJ5aEvi46cdH+ayMFRwmA==", "signatures": [{"sig": "MEUCIQCN82e2qElGjU9/LzIHJczimNudV4vA090U02ttBwiX2wIgK1LfBRlmbex8CWvbk8jmCibcJ4J78/gbcVS+9cnErbk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26376, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfzDR1CRA9TVsSAnZWagAAH5oP/iXfYQQSY3QJ3ps340Ga\nD2zOTpjqX96Dr0QuM0+9pPrgXgSjmNJVlq3EW2BR+Q3zlOzK8vjwy3UR0phz\nOZHWfcv34z1tejWbwBgRYQubRCir8yE3vC5N0QmgpKfnAF9vwno8BPR8wTpJ\n4m4DtxLUU8Ao2YCmsldGg04DaK2gDMQAqOiSwWETf4gMlH9WP44Y6j/TLx3e\nbg47bLLler/DhBYEa5MJU9I/AQ8Nm3A2/ISdHzrDumkmkY1gqQmoUM7/Q+iC\nXbwxZjevWETL3Y0BsRIbYcd8mC5RYPjazOY60aH/fCwUbRPuwXBxwvRxP1TB\nvzvozXc2rb7Rq6nARVAFNc4Rq3XGFt4+aSlpHSNeiP+1SuOCupH4jNUwpTaU\nUYVYYV5k8TlTD+UzR9t3neQhvWaUbUzo5syn6YuskmBrW8W/pwdxTI1pjP7r\nugL/+ERqfIutbeX/r7q1w+aILPByBUDismjgAGri2EjO1u3RDWJnwXA7MF1/\nEYmwnxUorCZLto+ztqjyHQgBE8zE+RJ1G7rVvrXec2Hxt4aBcbDuwImkyb6x\nitTlQIZcw0M99OCWh40Df2kPRk/4dK0bQYCHg3jqsfUCRDKKfore+h0jQiog\nJphfIusUzcni8hzMyGsznWdw2nhSlXFS+iDDJyGvAHSc7OUTRt+FmBYxJv5v\nlKKq\r\n=L/Qd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "089381cfb58a8f7ff8766028784a04134593859f", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only && npm run test:harmony", "eccheck": "eclint check $(git ls-files)", "prelint": "npm run eccheck", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "safe-publish-latest", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "test:harmony": "node --harmony --es-staging test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/is-boolean-object.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Is this value a JS Boolean? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "directories": {}, "_nodeVersion": "14.15.1", "dependencies": {"call-bind": "^1.0.0"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"is": "^3.3.0", "aud": "^1.1.3", "nyc": "^10.3.2", "tape": "^5.0.1", "eclint": "^2.8.1", "eslint": "^7.15.0", "foreach": "^2.0.5", "indexof": "^0.0.1", "auto-changelog": "^2.2.1", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-boolean-object_1.1.0_1607218292666_0.5773708800605015", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "is-boolean-object", "version": "1.1.1", "keywords": ["Boolean", "ES6", "toStringTag", "@@toStringTag", "Boolean object", "true", "false", "is-boolean"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-boolean-object@1.1.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-boolean-object#readme", "bugs": {"url": "https://github.com/inspect-js/is-boolean-object/issues"}, "dist": {"shasum": "3c0878f035cb821228d350d2e1e36719716a3de8", "tarball": "https://registry.npmjs.org/is-boolean-object/-/is-boolean-object-1.1.1.tgz", "fileCount": 11, "integrity": "sha512-bXdQWkECBUIAcCkeH1unwJLIpZYaa5VvuygSyS/c2lf719mTKZDU5UdDRlpd01UjADgmW8RfqaP+mRaVPdr/Ng==", "signatures": [{"sig": "MEYCIQCQzto8gO7L89YRIPs3wvbQbwx3kRSr8BcII8JtS9RZEgIhAIDxeY1nCyRyeejngnwGZXTJOVbiLx2AnsCRVH+cRS3j", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21634, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJglcYwCRA9TVsSAnZWagAA8zwP/jBHEQ/44EeKoHSIc0bn\npdyYaBscn24kTq5hnThKcIX7bo21E2ataFuXhWlZSFEvXMv2tqhDGiz8oRPP\nlObBY4RDUoc5NS1LtqrePYNbEfsv1/uA/Z2a12MOgwhpVnuP1dxU9KJVRItn\n1bbK9chlYiAG7+aGt8Chj9b8MUBquZ2fUieW1SNpiAIN9us5sazfDkgUpIdy\nfxjAbIOMYHoWWwJAf/Yxz67Lfd/9fdRnf1tQ5mgLXQp15v12t+sxmIcsIk4W\nm2rWFogKYc+i5RpRr8S/RQu8UgNYxFF05wH5rb2FVk4iYQTYOoBXgYdUzVM2\nyIwYrsnJAis73icz2TbileQ9yEOfWz3Qm0au6DxYt0Y5sxqAxbsqwLIcvjfs\nxv9fTmSluf93DVUi7WCV1oYN7j312z11+c5jNuN+sObFkyvL4TqxGqV5nKEe\nsiROVx5ew3uwuvUiv4bMm6JDQpUx1N7gcK/P1wlSBL68ztB/c7WsdljuWP6X\n+oGHYuJNn+hNdFvonb5K5W44VZmHb478GcMIPdcxOE9TEyO9Qv8yw1QtGdzH\nkio1SEDI63AyIcXTF4WiSOFxzgGFtbxkWX9u2AIlXF1/wE/Z3XBSuEhByiSu\nx7CX7Gj6b1qgl5PoI9pTBE5WsGBw/vtWO3g00yI0wJnwTvpiHS8mYipGOBQV\nFsPP\r\n=58f6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "8313fe5c7d2cb089ab85453782ce5a6bd87ead3c", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only && npm run test:harmony && npm run test:corejs", "eccheck": "eclint check $(git ls-files)", "prelint": "npm run eccheck", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "test:corejs": "nyc tape test-corejs.js", "test:harmony": "node --harmony --es-staging test", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/inspect-js/is-boolean-object.git", "type": "git"}, "_npmVersion": "7.10.0", "description": "Is this value a JS Boolean? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "directories": {}, "_nodeVersion": "16.0.0", "dependencies": {"call-bind": "^1.0.2"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"is": "^3.3.0", "aud": "^1.1.5", "nyc": "^10.3.2", "tape": "^5.2.2", "eclint": "^2.8.1", "eslint": "^7.25.0", "core-js": "^3.12.0", "foreach": "^2.0.5", "indexof": "^0.0.1", "auto-changelog": "^2.2.1", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-boolean-object_1.1.1_1620428336150_0.7342080735517047", "host": "s3://npm-registry-packages"}}, "1.1.2": {"name": "is-boolean-object", "version": "1.1.2", "keywords": ["Boolean", "ES6", "toStringTag", "@@toStringTag", "Boolean object", "true", "false", "is-boolean"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-boolean-object@1.1.2", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-boolean-object#readme", "bugs": {"url": "https://github.com/inspect-js/is-boolean-object/issues"}, "dist": {"shasum": "5c6dc200246dd9321ae4b885a114bb1f75f63719", "tarball": "https://registry.npmjs.org/is-boolean-object/-/is-boolean-object-1.1.2.tgz", "fileCount": 11, "integrity": "sha512-gDYaKHJmnj4aWxyj6YHyXVpdQawtVLHU5cb+eztPGczf6cjuTdwve5ZIEfgXqH4e57An1D1AKf8CZ3kYrQRqYA==", "signatures": [{"sig": "MEYCIQC05hQuuJdjOcYvL6OkWfn7+2JHmXLYtDHJ9VE1b7I8OwIhAJyJ1syNNrWVp7dmg6e85R6l6JqelVVJAmu61AVvaI1z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22120, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhDHdMCRA9TVsSAnZWagAAxogP/2KdfUtkd76ju4qEy5LK\n35Rdq4QCAH2HLWVtzGsxFwrSnPnoePNgz8oQ5bXAIpnub460guvG8IMGdmqa\neOAnj9kAoVM4fhCib1JA9Dg1bF8WUVRVcKKNPW2riQxB/T8ZLIixCWkZyhG4\n8WC10DdtTFEaLzKbOE3GGnveo/S7BrXbSNf071uD27QSX7SPhWF71y2cZzmw\nB9aftqpM/dmUUiQH/Q2tfDf2GvTb4wyk54qQHrT8AhxDzFbdpv9EvOWLD2XD\nyGJXhvhC9Hc7OX/1OrwmEt44vlf0quV43jPGQyENNYyWaDyALVkNa049jWgx\nvwN3XPa8y+xE2efbA4DE0U/M3PADKvrx+b7R7WBdVg9sLUy/thcgAdFunMhi\neZWITtHMhxIdzIbXKnvZFgo2JS1Jq1zyasAz7faU+jNKfX+Rwd1nidCqZsWe\nBJUFdFukolBu6YTLTrTtGqcMCyNxPWXQkpGsvNnI94tRCsxS9fEwKztl006D\nLVLhZEfXIIeXVXTKngTC8tisC+3Vvnw9qqfyu+m/roHGYrP5VPxMBw4TJ4Zo\nhGzpc296CSxdzR2LrSntqEfWt44J9kQ1kSteCVNax+fMzAnU0Uv3xpPhpPBC\nClH/MatUwhm3iclwPdlCvHvK1fUkI2cqCRyvRN4JEzpfmx40oNx+CmyT+i8S\nYImB\r\n=jvdD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "586b4a72c8d6973e5416610a2eaca65c1fb6da3d", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only && npm run test:harmony && npm run test:corejs", "eccheck": "eclint check $(git ls-files)", "prelint": "npm run eccheck", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "test:corejs": "nyc tape test-corejs.js", "test:harmony": "node --harmony --es-staging test", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/inspect-js/is-boolean-object.git", "type": "git"}, "_npmVersion": "7.19.1", "description": "Is this value a JS Boolean? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "directories": {}, "_nodeVersion": "16.6.0", "dependencies": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"is": "^3.3.0", "aud": "^1.1.5", "nyc": "^10.3.2", "tape": "^5.3.0", "eclint": "^2.8.1", "eslint": "^7.32.0", "core-js": "^3.16.0", "foreach": "^2.0.5", "indexof": "^0.0.1", "auto-changelog": "^2.3.0", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-boolean-object_1.1.2_1628206924435_0.046846461052316934", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "is-boolean-object", "version": "1.2.0", "keywords": ["Boolean", "ES6", "toStringTag", "@@toStringTag", "Boolean object", "true", "false", "is-boolean"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-boolean-object@1.2.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-boolean-object#readme", "bugs": {"url": "https://github.com/inspect-js/is-boolean-object/issues"}, "dist": {"shasum": "9743641e80a62c094b5941c5bb791d66a88e497a", "tarball": "https://registry.npmjs.org/is-boolean-object/-/is-boolean-object-1.2.0.tgz", "fileCount": 12, "integrity": "sha512-kR5g0+dXf/+kXnqI+lu0URKYPKgICtHGGNCDSB10AaUFj3o/HkB3u7WfpRBJGFopxxY0oH3ux7ZsDjLtK7xqvw==", "signatures": [{"sig": "MEUCIFoCNcNPrVT9bIqML0mleP7wpuWea+kjM9et0OIMULXzAiEAuGoKt3XIIbOfpJ6xjTMxESg8T6kP5NkZUklMgP/ehO0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25210}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "380f974b2bfb0fc0a9cf24c07987dfc5ad7b0f31", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only && npm run test:harmony && npm run test:corejs", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "tsc -p . && attw -P", "posttest": "npx npm@'>=10.2' audit --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "test:corejs": "nyc tape test-corejs.js", "test:harmony": "node --harmony --es-staging test", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/inspect-js/is-boolean-object.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Is this value a JS Boolean? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "directories": {}, "_nodeVersion": "23.3.0", "dependencies": {"call-bind": "^1.0.7", "has-tostringtag": "^1.0.2"}, "publishConfig": {"ignore": [".github/workflows", "test-corejs.js"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"is": "^3.3.0", "nyc": "^10.3.2", "tape": "^5.9.0", "eclint": "^2.8.1", "eslint": "=8.8.0", "core-js": "^3.39.0", "indexof": "^0.0.1", "encoding": "^0.1.13", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "next", "@types/tape": "^5.6.5", "@types/core-js": "^2.5.8", "auto-changelog": "^2.5.0", "@ljharb/tsconfig": "^0.2.0", "@types/call-bind": "^1.0.5", "safe-publish-latest": "^2.0.0", "@arethetypeswrong/cli": "^0.17.0", "@ljharb/eslint-config": "^21.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/is-boolean-object_1.2.0_1733121937335_0.38283455051363635", "host": "s3://npm-registry-packages"}}, "1.2.1": {"name": "is-boolean-object", "version": "1.2.1", "keywords": ["Boolean", "ES6", "toStringTag", "@@toStringTag", "Boolean object", "true", "false", "is-boolean"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-boolean-object@1.2.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-boolean-object#readme", "bugs": {"url": "https://github.com/inspect-js/is-boolean-object/issues"}, "dist": {"shasum": "c20d0c654be05da4fbc23c562635c019e93daf89", "tarball": "https://registry.npmjs.org/is-boolean-object/-/is-boolean-object-1.2.1.tgz", "fileCount": 12, "integrity": "sha512-l9qO6eFlUETHtuihLcYOaLKByJ1f+N4kthcU9YjHy3N+B3hWv0y/2Nd0mu/7lTFnRQHTrSdXF50HQ3bl5fEnng==", "signatures": [{"sig": "MEUCIQDEZMZxNI5bs/WKgBjWxmjnyJYDHGwqzjVYj06PF9ymWwIgUfgr4d7+lUfRbtiL2fQyNM76YpParfUfX1mET7MKRtA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25390}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "377d73415b31b5ebc43168a6f5a2ae297ddb4701", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only && npm run test:harmony && npm run test:corejs", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "tsc -p . && attw -P", "posttest": "npx npm@'>=10.2' audit --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "test:corejs": "nyc tape test-corejs.js", "test:harmony": "node --harmony --es-staging test", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/inspect-js/is-boolean-object.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Is this value a JS Boolean? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "directories": {}, "_nodeVersion": "23.4.0", "dependencies": {"call-bound": "^1.0.2", "has-tostringtag": "^1.0.2"}, "publishConfig": {"ignore": [".github/workflows", "test-corejs.js"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"nyc": "^10.3.2", "tape": "^5.9.0", "eclint": "^2.8.1", "eslint": "=8.8.0", "core-js": "^3.39.0", "encoding": "^0.1.13", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "next", "@types/tape": "^5.6.5", "@types/core-js": "^2.5.8", "auto-changelog": "^2.5.0", "@ljharb/tsconfig": "^0.2.0", "safe-publish-latest": "^2.0.0", "@arethetypeswrong/cli": "^0.17.0", "@ljharb/eslint-config": "^21.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/is-boolean-object_1.2.1_1734074929100_0.27585964606782776", "host": "s3://npm-registry-packages-npm-production"}}, "1.2.2": {"name": "is-boolean-object", "version": "1.2.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "description": "Is this value a JS Boolean? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "license": "MIT", "main": "index.js", "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run lint", "test": "npm run tests-only && npm run test:harmony && npm run test:corejs", "tests-only": "nyc tape 'test/**/*.js'", "test:harmony": "node --harmony --es-staging test", "test:corejs": "nyc tape test-corejs.js", "posttest": "npx npm@'>=10.2' audit --production", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && attw -P", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/inspect-js/is-boolean-object.git"}, "keywords": ["Boolean", "ES6", "toStringTag", "@@toStringTag", "Boolean object", "true", "false", "is-boolean"], "dependencies": {"call-bound": "^1.0.3", "has-tostringtag": "^1.0.2"}, "devDependencies": {"@arethetypeswrong/cli": "^0.17.3", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.3", "@types/core-js": "^2.5.8", "@types/object-inspect": "^1.13.0", "@types/tape": "^5.8.1", "auto-changelog": "^2.5.0", "core-js": "^3.40.0", "eclint": "^2.8.1", "encoding": "^0.1.13", "eslint": "=8.8.0", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.4", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows", "test-corejs.js"]}, "_id": "is-boolean-object@1.2.2", "gitHead": "a17a5a3b37ef995e94161dc77568359446392cfb", "types": "./index.d.ts", "bugs": {"url": "https://github.com/inspect-js/is-boolean-object/issues"}, "homepage": "https://github.com/inspect-js/is-boolean-object#readme", "_nodeVersion": "23.6.1", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-wa56o2/ElJMYqjCjGkXri7it5FbebW5usLw/nPmCMs5DeZ7eziSYZhSmPRn0txqeW4LnAmQQU7FgqLpsEFKM4A==", "shasum": "7067f47709809a393c71ff5bb3e135d8a9215d9e", "tarball": "https://registry.npmjs.org/is-boolean-object/-/is-boolean-object-1.2.2.tgz", "fileCount": 12, "unpackedSize": 26486, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCDvFMcqelZ4fN+KIQXgysnRFk75YkFQZY454+dASItGQIhANOoc8P/Qlr2Ks75pxSGq7Za9nLnsDR7O7Yt0irtakqs"}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/is-boolean-object_1.2.2_1738723691877_0.9116973161167243"}, "_hasShrinkwrap": false}}, "time": {"created": "2015-04-28T18:17:41.329Z", "modified": "2025-02-05T02:48:12.320Z", "1.0.0": "2015-04-28T18:17:41.329Z", "1.0.1": "2019-12-19T18:32:34.278Z", "1.1.0": "2020-12-06T01:31:32.796Z", "1.1.1": "2021-05-07T22:58:56.305Z", "1.1.2": "2021-08-05T23:42:04.586Z", "1.2.0": "2024-12-02T06:45:37.535Z", "1.2.1": "2024-12-13T07:28:49.298Z", "1.2.2": "2025-02-05T02:48:12.118Z"}, "bugs": {"url": "https://github.com/inspect-js/is-boolean-object/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/inspect-js/is-boolean-object#readme", "keywords": ["Boolean", "ES6", "toStringTag", "@@toStringTag", "Boolean object", "true", "false", "is-boolean"], "repository": {"type": "git", "url": "git://github.com/inspect-js/is-boolean-object.git"}, "description": "Is this value a JS Boolean? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "readme": "# is-boolean-object <sup>[![Version Badge][2]][1]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![dependency status][5]][6]\n[![dev dependency status][7]][8]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][11]][1]\n\nIs this value a JS Boolean? This module works cross-realm/iframe, and despite ES6 @@toStringTag.\n\n## Example\n\n```js\nvar isBoolean = require('is-boolean-object');\nvar assert = require('assert');\n\nassert.notOk(isBoolean(undefined));\nassert.notOk(isBoolean(null));\nassert.notOk(isBoolean('foo'));\nassert.notOk(isBoolean(function () {}));\nassert.notOk(isBoolean([]));\nassert.notOk(isBoolean({}));\nassert.notOk(isBoolean(/a/g));\nassert.notOk(isBoolean(new RegExp('a', 'g')));\nassert.notOk(isBoolean(new Date()));\nassert.notOk(isBoolean(42));\nassert.notOk(isBoolean(NaN));\nassert.notOk(isBoolean(Infinity));\n\nassert.ok(isBoolean(new Boolean(42)));\nassert.ok(isBoolean(false));\nassert.ok(isBoolean(Object(false)));\nassert.ok(isBoolean(true));\nassert.ok(isBoolean(Object(true)));\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n[1]: https://npmjs.org/package/is-boolean-object\n[2]: https://versionbadg.es/inspect-js/is-boolean-object.svg\n[5]: https://david-dm.org/inspect-js/is-boolean-object.svg\n[6]: https://david-dm.org/inspect-js/is-boolean-object\n[7]: https://david-dm.org/inspect-js/is-boolean-object/dev-status.svg\n[8]: https://david-dm.org/inspect-js/is-boolean-object#info=devDependencies\n[11]: https://nodei.co/npm/is-boolean-object.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/is-boolean-object.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/is-boolean-object.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=is-boolean-object\n[codecov-image]: https://codecov.io/gh/inspect-js/is-boolean-object/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/inspect-js/is-boolean-object/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/inspect-js/is-boolean-object\n[actions-url]: https://github.com/inspect-js/is-boolean-object/actions\n", "readmeFilename": "README.md"}