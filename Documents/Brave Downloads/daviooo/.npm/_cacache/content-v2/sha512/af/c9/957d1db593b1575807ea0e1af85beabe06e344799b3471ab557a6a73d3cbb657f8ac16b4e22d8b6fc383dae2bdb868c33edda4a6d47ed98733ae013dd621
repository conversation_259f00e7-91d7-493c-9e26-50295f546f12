{"_id": "router", "_rev": "169-b5e012209024d332420d5d2e4cfcde1b", "name": "router", "dist-tags": {"latest": "2.2.0", "next": "2.1.0"}, "versions": {"0.2.1": {"name": "router", "version": "0.2.1", "_id": "router@0.2.1", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "cfeedbf9f79944d8ff320e600eb4461b453b5d2e", "tarball": "https://registry.npmjs.org/router/-/router-0.2.1.tgz", "integrity": "sha512-TapbzTOax8ns/7cO1XFtI41EYWE/iZUvvgtnk8PmbNI7JzefTV9MT8zg2o0hDFzp1m9qfXxZkUqS3EeF2VJuhA==", "signatures": [{"sig": "MEUCIDZkSnXxBjQlbyRArIe5GZHr5mtIZz5bLC2InEg9ns/+AiEAwylrNkojUFgGZhLVbGAgFOP8MebFxCi7ARzBHR6iMlw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "files": [""], "engines": {"node": "*"}, "_npmVersion": "0.3.18", "description": "A lean and mean web router", "directories": {}, "_nodeVersion": "v0.4.8", "dependencies": {"common": ">=0.1.0"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.2.2": {"name": "router", "version": "0.2.2", "_id": "router@0.2.2", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "db2b1fd1de97ee3ffaeebda26c526007230d84a0", "tarball": "https://registry.npmjs.org/router/-/router-0.2.2.tgz", "integrity": "sha512-dh<PERSON>t2aysPDKc8wmC3hn7yg4qupAOZACsakAiXXqT1uqDqJDnCfuNf5JrlqkKwSMwVg4lB40Hh0FoQQnABYu2A==", "signatures": [{"sig": "MEQCIHd/T81GZnWxsv14azb28mTOVvNSh1eJ2Du0qid1Yo0vAiALMuvdn/SVEiuaRtim3biI1v+WtkCba7i8u3ZMG6DvZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "files": [""], "engines": {"node": "*"}, "_npmVersion": "0.3.18", "description": "A lean and mean web router", "directories": {}, "_nodeVersion": "v0.4.8", "dependencies": {"common": ">=0.1.0"}, "_defaultsLoaded": true, "_engineSupported": true}, "0.2.3": {"name": "router", "version": "0.2.3", "_id": "router@0.2.3", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "4dd60f7b5cd5762c749133af89f33b92533277f8", "tarball": "https://registry.npmjs.org/router/-/router-0.2.3.tgz", "integrity": "sha512-wKGWRG2f8vjXh/EY87BC3obx1cUF5Wn+FHnAN4f5KseU+Yq77bZB922guiBRzbVkUj62Ysa1/722BNj8GI8agw==", "signatures": [{"sig": "MEQCIC1VJrLbT2uWkqB3+jC5vB0qcrI2Iv+x8iaNBic/8wVEAiAD7vh962X2aCDPrg980jy3YjrHtaZ+RrpMk9j/grdlCQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "scripts": {}, "_npmVersion": "1.0.14", "description": "A lean and mean web router", "directories": {}, "_nodeVersion": "v0.4.9", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/router/0.2.3/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"common": ">=0.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.2.4": {"name": "router", "version": "0.2.4", "_id": "router@0.2.4", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "afbd2b1721fe588930fc4e4f27c2871b56d259cb", "tarball": "https://registry.npmjs.org/router/-/router-0.2.4.tgz", "integrity": "sha512-5no3GyaCCDeCd5FYrKfYh2Iwl32QNt0Bsx+5RtTKA8/Xfz5xpT/hoU+RSrAxNEW8A86maITUV4I847kQxQ3/xQ==", "signatures": [{"sig": "MEUCIQDbRQOBeeBLOYabSlSYNUH3aMPwARmBkote6XygG0bUqAIgHUqju+YfbRJDdrpAciJbzL0t0e6QE6QbY8IQXEqAp8k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "scripts": {}, "_npmVersion": "1.0.15", "description": "A lean and mean web router", "directories": {}, "_nodeVersion": "v0.4.9", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/router/0.2.4/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"common": ">=0.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.2.5": {"name": "router", "version": "0.2.5", "_id": "router@0.2.5", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "840f4830e8b8e98f39f509b514dbd69d71e1a385", "tarball": "https://registry.npmjs.org/router/-/router-0.2.5.tgz", "integrity": "sha512-8jsFdKBUugEC5iStwOY9sgDcJlINmCc425Ye9oCzmSFJ6O+H2c3YZ6Gql9LFTljEqN7oYUi0MKJMhTv7xolIcw==", "signatures": [{"sig": "MEUCIQD1gLz1Fox+2WtCxA30t7OnbFm3AY7Dht51sfzwc6r+cwIgUqn5Je2ZXA0/smdS5jUOHc6X54ufdRB0QnpGtOOFzcg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "scripts": {}, "_npmVersion": "1.0.15", "description": "A lean and mean web router", "directories": {}, "_nodeVersion": "v0.4.9", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/router/0.2.5/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"common": ">=0.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.2.6": {"name": "router", "version": "0.2.6", "_id": "router@0.2.6", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "38ab252af8a74f73d29cc4a60e58511fb7aeb2fd", "tarball": "https://registry.npmjs.org/router/-/router-0.2.6.tgz", "integrity": "sha512-J2qgLNmQKNWH4joi8FLnaQE4z+QEK53xj5FPDCJbpK62ZgDTcqGKsOHsSz8s2ggDKOGvppylEF3y6zBKKBFW8g==", "signatures": [{"sig": "MEYCIQClD8EJRBqPYEbAlWSNiN3WZn54bzqgqRW+sc5kPVgZ7wIhAOG0W3dWQo1IZgWiZJUpLTKd5ym9r8WM0vmVJc9XZUEV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "scripts": {}, "_npmVersion": "1.0.15", "description": "A lean and mean web router", "directories": {}, "_nodeVersion": "v0.4.9", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/router/0.2.6/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"common": ">=0.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.2.8": {"name": "router", "version": "0.2.8", "_id": "router@0.2.8", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "6321600584949274df8b2ab8ad0cad865115e972", "tarball": "https://registry.npmjs.org/router/-/router-0.2.8.tgz", "integrity": "sha512-DXSWEvNtWyoLJKKIWs0N0T5qHDtwqOgv2/rYx3DVlaZEYCnupyN8x3h04rSl3u3euAbEpgUvRLqwITvfTpvhqw==", "signatures": [{"sig": "MEQCIBDuOX6aqoy2n+w4bytw8OG68Fx2/xOoUUcIFC4rs5BkAiBMXr4LH9UUZF2dEB78w/V7QLMiD3FmFEUNCNRSjHZGhw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "scripts": {}, "_npmVersion": "1.0.15", "description": "A lean and mean web router", "directories": {}, "_nodeVersion": "v0.4.9", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/router/0.2.8/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"common": ">=0.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.3.0": {"name": "router", "version": "0.3.0", "_id": "router@0.3.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "68db398fa6c3fa9df959b7ff9abe3a619f671e5c", "tarball": "https://registry.npmjs.org/router/-/router-0.3.0.tgz", "integrity": "sha512-uEh2KbKcD5e0yFomg/BvYis5b206i+YZze4enf6wB8PaMSMK0IGToT48U6TlHd9KGxCphCiGbgW85ZJnYryOxQ==", "signatures": [{"sig": "MEUCIBgIyM+lM6Lsc6ybwzhCe4yMbpbtHSWDuUODNNnXww8jAiEA3LRbWSEHJAx528OVZ7TFPsjEvyxGZA6JFLqXNZO5yPo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "scripts": {}, "_npmVersion": "1.0.15", "description": "A lean and mean web router", "directories": {}, "_nodeVersion": "v0.4.9", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/router/0.3.0/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"common": ">=0.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.3.1": {"name": "router", "version": "0.3.1", "_id": "router@0.3.1", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "cbd7879ed87f666606b086198aad367d0dc7dee8", "tarball": "https://registry.npmjs.org/router/-/router-0.3.1.tgz", "integrity": "sha512-bKvKUY4WddherZMcUwbf6c8D9q71uYNHI+jXtsORKCfPiQSTy7QhvcpRQHwR3CIbLGWXepxCuPvYAxYoIjwbCA==", "signatures": [{"sig": "MEYCIQCR+z6YaBf5EUMUqPl9aTAJS89AgnLYUYtrhKxajNZf6AIhALOWIjrOn3J254a5ZVwZY8626M6ktMpSVRwWqVM0mftF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "scripts": {}, "_npmVersion": "1.0.15", "description": "A lean and mean web router", "directories": {}, "_nodeVersion": "v0.4.9", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/router/0.3.1/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"common": ">=0.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.3.2": {"name": "router", "version": "0.3.2", "_id": "router@0.3.2", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "5f38aeacc9cc9bdf1da8871c6b189682b64f6c5a", "tarball": "https://registry.npmjs.org/router/-/router-0.3.2.tgz", "integrity": "sha512-HmnJ3t72RGtbN34slxlMrHC6v4QrVu2sccH8HkdIx19hLHSQhpgM9yBbZhsym66xImuGAYK5hHhqIK+rwEiUwg==", "signatures": [{"sig": "MEUCIDXeAkcq1b8SesE0q6N1+NvY<PERSON>eooypp/bo/rVV9fSmxvAiEAsCqwGTj4pdBPfOI0vsQDa+eP8ZM3yIjM+TyQJ7JQIm0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "scripts": {}, "_npmVersion": "1.0.15", "description": "A lean and mean web router", "directories": {}, "_nodeVersion": "v0.4.9", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/router/0.3.2/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"common": ">=0.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.4.0": {"name": "router", "version": "0.4.0", "_id": "router@0.4.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "3b493fc7ada42b11d3dfc73f21e451959416fd58", "tarball": "https://registry.npmjs.org/router/-/router-0.4.0.tgz", "integrity": "sha512-ctNb5Tm3Y71be5oAdW4GuB0P238wQJ69O7FyaK2q2UvYJIBVFtJi23qeCDb/b4aWlD/IVVTsInqwU3/J6765Yg==", "signatures": [{"sig": "MEUCIDU8hGcM7ZGLRc9OF6pGmr1NTec55LKBrFRYEwCrODcUAiEA69RDG+dAzeDkMeiq/U3ywzc8MDK4DmiVBRFPfCWmW9w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "scripts": {}, "_npmVersion": "1.0.15", "description": "A lean and mean web router", "directories": {}, "_nodeVersion": "v0.4.9", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/router/0.4.0/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"common": ">=0.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.4.1": {"name": "router", "version": "0.4.1", "_id": "router@0.4.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "e12dbc88497fd3db639b4dd50d667925fa739cf9", "tarball": "https://registry.npmjs.org/router/-/router-0.4.1.tgz", "integrity": "sha512-g0Arc+6sMbKHQJJgOsjcrZ7NRZs+YchDHDmzHap8b7DIAe8TZxnIHvwJVZZn97ws/a3jkR0SKPMZ1ilzKtfHpg==", "signatures": [{"sig": "MEYCIQDiUDVHHYs3Kw6BN7FzGIIQ+jhKA82Ew3QlN2L4CKLEQwIhAIMxc6jxIE01D5VPjYLmL7IdiEGnJu1iaJZUEF3/j9Fx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "scripts": {}, "_npmVersion": "1.0.15", "description": "A lean and mean web router", "directories": {}, "_nodeVersion": "v0.4.9", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/router/0.4.1/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"common": ">=0.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.4.2": {"name": "router", "version": "0.4.2", "_id": "router@0.4.2", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "6fa65ed4d7c871e3be692cea4347024fcc39e95a", "tarball": "https://registry.npmjs.org/router/-/router-0.4.2.tgz", "integrity": "sha512-Worhqo+/0di6VqXFVYy2HYxZo5PSyRAa0x68JSSeamDz76dvw+r+uEtsJjZReRxfNBjXpW3Yvyw2uGzktR54Qg==", "signatures": [{"sig": "MEYCIQDPIVOdM/AVqyYLShDzpn7oF90NIDBd8EIXdEPrwtX3kgIhAJJ1N5ykXzbZs5VYTXQGRDP/I4LADhtZzNzWp4yve+lV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "scripts": {}, "_npmVersion": "1.0.23", "description": "A lean and mean web router", "directories": {}, "_nodeVersion": "v0.5.9", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/router/0.4.2/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"common": ">=0.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.4.3": {"name": "router", "version": "0.4.3", "_id": "router@0.4.3", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "90c3327f3bc6b761d466f3e757f1f0b2f9e6a2ba", "tarball": "https://registry.npmjs.org/router/-/router-0.4.3.tgz", "integrity": "sha512-8/Ra/17S1Bect20zIhdUTKDyRAB1U5UgiLmSuhr9AmDBj2NuIbTh2K8Bi1Cbm7GLil8LkHMhVF/uWEwlG6jUrw==", "signatures": [{"sig": "MEUCIFb88AvKaNLSGg66IkarCYmxYzdB24gyWcJEkvcqEycwAiEAiN/o2iHWA/rWUpjeN5BPsRsbs4f5JBOmfY+uNPulE1o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.0.104", "description": "A lean and mean web router", "directories": {}, "_nodeVersion": "v0.6.1", "dependencies": {"common": ">=0.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.4.4": {"name": "router", "version": "0.4.4", "_id": "router@0.4.4", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "45e61b84d2e962116f6d6e9767a43289161d081d", "tarball": "https://registry.npmjs.org/router/-/router-0.4.4.tgz", "integrity": "sha512-cn3xvfyJ3n46GCibilPCO+J/RYWjaD2+7ci+VwMeqSNzZXwz+A1vhuHV7RvEtQrebo/62loopNS6OUfW3YueMg==", "signatures": [{"sig": "MEUCIG1Pvik3gxLm45Ne1IbENIjEZxLVeYDWgKK3TuUNBYqQAiEArmgzWN3hzT1J1F5cASjMtHswBjlRRB0P81xkk8e/lTY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.0.104", "description": "A lean and mean web router", "directories": {}, "_nodeVersion": "v0.6.1", "dependencies": {"common": ">=0.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.4.5": {"name": "router", "version": "0.4.5", "_id": "router@0.4.5", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "9163bc62841165018c3c44a97f5afc21b7edde66", "tarball": "https://registry.npmjs.org/router/-/router-0.4.5.tgz", "integrity": "sha512-LBNlUZnJBnFvE9IbFWrRn8/Hse54JTNTm4vtaIVGeKHMTunQe63c62T5TasJuRWDfW+ABHRsEfE0OnSvkXXLlg==", "signatures": [{"sig": "MEQCIH99kstLe6QBpsWqJVjqINcyrXOSClugvZeo77FroOsYAiAwMaMfiz971BUbTi6+7Kny5AMgmNi9HrL7QN2XwWNEQw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.1.0-beta-10", "description": "A lean and mean web router", "directories": {}, "_nodeVersion": "v0.6.7", "dependencies": {"common": ">=0.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.4.6": {"name": "router", "version": "0.4.6", "_id": "router@0.4.6", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "1a7057e1fcaa21bc2ce275baa1f24f8a8fc8056c", "tarball": "https://registry.npmjs.org/router/-/router-0.4.6.tgz", "integrity": "sha512-aJN4IOoQyPsPy1dRAqxPrfW+JDU9Uz1uQJCMY0O2VZ3xOEr39Lu9oj5WtCY+t0hQTNxayUd0EHn49VZsVSeWTg==", "signatures": [{"sig": "MEICICtjS29Q8UXJFt4lMrhNdAs9OaHf1IAZF7NWmsvspL5SAh57r0rGRCKtdqnSlyUVYKT//oZqndh960GKcNjH0QU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.1.0-2", "description": "A lean and mean web router", "directories": {}, "_nodeVersion": "v0.6.8", "dependencies": {"common": ">=0.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.4.8": {"name": "router", "version": "0.4.8", "_id": "router@0.4.8", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "efca070a61e31db6e65ffbe9214648da121551ad", "tarball": "https://registry.npmjs.org/router/-/router-0.4.8.tgz", "integrity": "sha512-0w2mhRFkKvsy+de/XRtbuSCPWEeQz3xFN/dhT3f3FT6GcKMjpYaAhG2vHh7jUk8xIbmKt583Dg0wogBvTw93Vg==", "signatures": [{"sig": "MEUCIANBHTkE088+zVJPeWyrTCFhWfrT+RdlIN1EAXwxAtsaAiEAmz1bji7coMUtrXgCBjSnX1QVZ68LDynUWS/Bs8oj7KU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.1.1", "description": "A lean and mean web router", "directories": {}, "_nodeVersion": "v0.6.11", "dependencies": {"common": ">=0.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.4.9": {"name": "router", "version": "0.4.9", "_id": "router@0.4.9", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "126301ae9708bd749b022dcb667e4e06a873c08a", "tarball": "https://registry.npmjs.org/router/-/router-0.4.9.tgz", "integrity": "sha512-n321QV/Zl3f3Dk/+MwzqxPDvktzbIUMPYyBKYqR7l0jxR5ZL9IPaih7j9pnsVj9eTGf9/dNU17TONzp9DXbuCg==", "signatures": [{"sig": "MEUCIAa4Xq/4g6Y0LwJLDcqZ4j33VDXxykYEqlk0jqbK0j7cAiEAvD4k0WcM/II+ogsv9qNtXHC66ZvS4w1L2DgcMfSQbpU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.1.1", "description": "A lean and mean web router", "directories": {}, "_nodeVersion": "v0.6.11", "dependencies": {"common": ">=0.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.4.10": {"name": "router", "version": "0.4.10", "_id": "router@0.4.10", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "14402bce9c7d1222014659a22be169d672de79d7", "tarball": "https://registry.npmjs.org/router/-/router-0.4.10.tgz", "integrity": "sha512-v6wiaFfXSVhZRCbwUFisC5JD6l9nPeMInRL8ufX0J1uoDgVFeLHzUrorVk45OMN6C6+qFyG/JJHDNcdqMyncLg==", "signatures": [{"sig": "MEUCIAuE/NjXBVs4guE1hfW3CiwqnODkgNw04nrrhlN0wiTFAiEAkpZUTvx83iLLssBtbNmd6kveYlOmpzhBogLFKPbVQsY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.1.1", "description": "A lean and mean web router", "directories": {}, "_nodeVersion": "v0.6.11", "dependencies": {"common": ">=0.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.4.11": {"name": "router", "version": "0.4.11", "_id": "router@0.4.11", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "01c09ee5cc1c6400233da53485afaa3807798d87", "tarball": "https://registry.npmjs.org/router/-/router-0.4.11.tgz", "integrity": "sha512-N0jffw/6gLmclMyZDM5GO24hiKuuASQaKGL5w0T2dTrKwHfnEWa7dv9QnbPE0uW26b9DC11G7S432jrIkrdDVw==", "signatures": [{"sig": "MEUCIQDQfi0PucrNED6q1cK611dPe9zXrdm5LpaVmXVYHJMKngIgd9n2W5sFKc3EQRBCPOcGzi2tOF5NNENmBpYJZineN5U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.1.4", "description": "A lean and mean web router", "directories": {}, "_nodeVersion": "v0.6.12", "dependencies": {"common": ">=0.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.5.0": {"name": "router", "version": "0.5.0", "_id": "router@0.5.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "9c4f8521770cf330875a88d20f19101213a3500d", "tarball": "https://registry.npmjs.org/router/-/router-0.5.0.tgz", "integrity": "sha512-TS+vIKQ0hz1Ayd3Imcd32YpJIOn9PUqzearewRJpC41Z9Sy6IVfY+OWqypKleFZh8wiUxcH40l+YReFiUgSirQ==", "signatures": [{"sig": "MEYCIQDGfImkVL2LqeIRnnDem0bMqykLzZCWS3tGZe/ysGo+aQIhAL7UjKycJEjHjlEmso6hFPak/MsW4tDLFB/LxMaGqEE2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.1.4", "description": "A lean and mean web router", "directories": {}, "_nodeVersion": "v0.6.12", "dependencies": {"common": ">=0.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.5.1": {"name": "router", "version": "0.5.1", "_id": "router@0.5.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "36dd667cbd6ac35f28940071aa811f0c96b72b35", "tarball": "https://registry.npmjs.org/router/-/router-0.5.1.tgz", "integrity": "sha512-<PERSON><PERSON>hljBpcIHK3pENHRxalKRZ2W3xXQ7Q3EHaNzVoODjPp5dda2nsGQVQv3VWxGb15xM1ntSY9UKHCIKg+cqYvQ==", "signatures": [{"sig": "MEQCIFqFXCIZEqhF3H20dwyrNbU/9TSyYUyuHaCEmP2xlFWwAiByVUUSo+/uFAe1/1UL411KsZUq5Ajt1dchSWBDq8Mt7w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.1.9", "description": "A lean and mean web router", "directories": {}, "_nodeVersion": "v0.6.13", "dependencies": {"common": ">=0.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.5.2": {"name": "router", "version": "0.5.2", "_id": "router@0.5.2", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "b666a2b63f27cbf43eaecc3d305ebe19f595a9c3", "tarball": "https://registry.npmjs.org/router/-/router-0.5.2.tgz", "integrity": "sha512-jjxZQIt3QfoBM+oer0iMmhh4L5eZ12Nnu9Lrf/1R9iOlWEW7qsiG3G92OQbzph3tmWkhJz3aL1VjQjS9DT5gpQ==", "signatures": [{"sig": "MEYCIQDLh5hVtUeXdELDOJmWM3nYkX1+ejFq4MSDh4rEe5uJFwIhALb2kHOw+/k7RhBxBk53ZrhXt8nQEMSjv/69abMMNyvu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.1.9", "description": "A lean and mean web router", "directories": {}, "_nodeVersion": "v0.6.13", "dependencies": {"common": ">=0.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.5.3": {"name": "router", "version": "0.5.3", "_id": "router@0.5.3", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "82c61739beddb10d4276730ec9fe3ea5b615befc", "tarball": "https://registry.npmjs.org/router/-/router-0.5.3.tgz", "integrity": "sha512-JqkOCPxSnG4ilf96eUlMXidkNBPo77JbEXWdNpc7Of5hncnJ4DuG2zyLepp5nH0mSodRbGSiIz7FbNqMa3INbQ==", "signatures": [{"sig": "MEYCIQCCnazC265ivKDpOIFl6YA8kjGCSCsEM8tq+rP2HPRWAQIhAISPfvErcqEde1RbG1Ua7wTUtEurZ2XJ/AjpI+FF7uPo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.1.9", "description": "A lean and mean web router", "directories": {}, "_nodeVersion": "v0.6.13", "dependencies": {"common": ">=0.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.5.4": {"name": "router", "version": "0.5.4", "_id": "router@0.5.4", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "733f6a41972aba1884e13ba0dfed572b62911957", "tarball": "https://registry.npmjs.org/router/-/router-0.5.4.tgz", "integrity": "sha512-J09b3+Vrdup7H06LS3wJB/DgDF7eTc8qEZA7Fz8Gh3fCYYn65W00LQ/i+G7fgVa/0BDys3YFzVhUee0eOsQ/kQ==", "signatures": [{"sig": "MEUCIBdhf5QKmcNVuELE4F137rb7+rD6jd2hMwhwCClBqC+yAiEAszauIaISO0BtXykPCAozyHap+nCGVW5gpdx++YovJqU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.1.9", "description": "A lean and mean web router", "directories": {}, "_nodeVersion": "v0.6.13", "dependencies": {"common": ">=0.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.5.5": {"name": "router", "version": "0.5.5", "_id": "router@0.5.5", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "2ec5c255607b08549119afeba60cef8b89eee7fc", "tarball": "https://registry.npmjs.org/router/-/router-0.5.5.tgz", "integrity": "sha512-0JgLhQ2ySGZYUOkHd0uazi7QTcCvwAyzgktqcmXMsTybKk1QBRqlXhs8SAvWQJrwspQDODgb9+4HRcdDk3X9XQ==", "signatures": [{"sig": "MEQCIEfC3t4a2ZiklO8l2SRaubpWsf3/eeURKfB3hgCWBgPNAiBziA5Cs7HkffrKGAWCqPBP9/dSY51La+bvDDjMrC70EQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.1.9", "description": "A lean and mean web router", "directories": {}, "_nodeVersion": "v0.6.13", "dependencies": {"common": ">=0.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.5.6": {"name": "router", "version": "0.5.6", "_id": "router@0.5.6", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "7779e5e2c93c94ddd008526c9d2cf499f82a415c", "tarball": "https://registry.npmjs.org/router/-/router-0.5.6.tgz", "integrity": "sha512-g6556T8kGdZbrbFhZTjjL9znbk2+RLyW/TWIDNibLJlD/DuapcmwjNebvS7PtYMmcoXVjfSMlB2UabK5H/I5kg==", "signatures": [{"sig": "MEUCIQCmpngNSpxBF9zy8EOoTHcWYfRx0WSaeqV8N8To/Hsy3wIgE4Rt6Pbxmf3IOL+DTbIkRTqByVvzXHK31g6PU3SXD0I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.1.9", "description": "A lean and mean web router", "directories": {}, "_nodeVersion": "v0.6.13", "dependencies": {"common": ">=0.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.5.7": {"name": "router", "version": "0.5.7", "_id": "router@0.5.7", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "57d085550d42fbaf92da20ba04ec70c751776675", "tarball": "https://registry.npmjs.org/router/-/router-0.5.7.tgz", "integrity": "sha512-9d+nfMNyw9kDJn5RdfMoeIkQM4JNqsUrGgwzZdH6oWW3yIsG/o/Ja4x+spqEuwvys7Zb37vaBIgK3nxMve5S5g==", "signatures": [{"sig": "MEYCIQD3lPjCdAWI5MSWpR7Buz8FdnCcrMgoPafgLOo5etK/oAIhAKWwGD8JxG7/uuxLTvoHYp/QyoKeQBPNhYbSXp2svRMM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.1.9", "description": "A lean and mean web router", "directories": {}, "_nodeVersion": "v0.6.13", "dependencies": {"common": ">=0.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.5.9": {"name": "router", "version": "0.5.9", "_id": "router@0.5.9", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "82c466148a2b299a29c7ccb893e05bd6fcf2f8b6", "tarball": "https://registry.npmjs.org/router/-/router-0.5.9.tgz", "integrity": "sha512-WeNx6zRdlEv7mg9nWu7eyC4GoYWB3LzbvQO6cZIDB7kBMzIHWXfms79k06SMq5pzLgYQC5y1KmsCVut8nWlSQw==", "signatures": [{"sig": "MEYCIQCSb4cBg4izct/8iFN6QAYizljjGmpjYmmxgfrkz/sYMgIhAP2q0yReFZK38r3Ix9ABh2/4sVSO2Osn2/cCkrL/B+xm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.1.9", "description": "A lean and mean web router", "directories": {}, "_nodeVersion": "v0.6.13", "dependencies": {"common": ">=0.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.5.10": {"name": "router", "version": "0.5.10", "_id": "router@0.5.10", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "8b7b37e1f14c01f5bc2f50dd1b3388d59bc32d88", "tarball": "https://registry.npmjs.org/router/-/router-0.5.10.tgz", "integrity": "sha512-UB2QF+EMi89NqisBHJIAOJ3Kp1LqkxntrNaYbIlNRtdcLGwXJfMdMzhemYh7KaT1aBG/R+QPWsNPa1OKI4IHyw==", "signatures": [{"sig": "MEUCIFcJbvPxB08eM9Ht/i9fMLeSKnEL0FHwIKI4AUCNknqVAiEA9MdMNKXTesCuE3F3NjPZTU0Erx4Ytbp2foG3jHElIgM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.1.9", "description": "A lean and mean web router", "directories": {}, "_nodeVersion": "v0.6.13", "dependencies": {"common": ">=0.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.5.11": {"name": "router", "version": "0.5.11", "_id": "router@0.5.11", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "8a88994f54ae8e2c601ed668aa5202fbbadc5770", "tarball": "https://registry.npmjs.org/router/-/router-0.5.11.tgz", "integrity": "sha512-5UF4eAw/+rrSbkdqK+PzGccTj+RVGK5xOJlKBoC+IOndiV9IAmcHdXFxIa9WFfBhkH+a9Phd0MXzp2R+QWoWJQ==", "signatures": [{"sig": "MEYCIQCagQsskPdmYvuHP8I897UQaE6ICDF8TfT/tLgqNmoIIwIhALrgBycuWw4UOLdMUlKCZkPuduvZTbW5jj7xbQDvNRTk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.1.9", "description": "A lean and mean web router", "directories": {}, "_nodeVersion": "v0.6.13", "dependencies": {"common": ">=0.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.5.13": {"name": "router", "version": "0.5.13", "_id": "router@0.5.13", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "9139bb396e5eb20e6c8eca41c2884e6de7250ebb", "tarball": "https://registry.npmjs.org/router/-/router-0.5.13.tgz", "integrity": "sha512-aYKf8vemSXzPAFoXR4VqYOL2tly4oy09YrO44I4rNbD7kPLOtLiroUIPyEMPIjyGFEr3Bi7xLzmV60vJ5dNc6Q==", "signatures": [{"sig": "MEUCICXCOg5FjGMYz55rWOzsFuN9NABAxpM+TkNeoGMqWZafAiEA5KoP5G6Kop5TmpKXllmBFtQvvgAN2fMHzAqCO78pYKM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.1.9", "description": "A lean and mean web router", "directories": {}, "_nodeVersion": "v0.6.13", "dependencies": {"common": ">=0.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.5.14": {"name": "router", "version": "0.5.14", "_id": "router@0.5.14", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "3c8041ccfd263b3a20bd18a2a5b90e635013d9ab", "tarball": "https://registry.npmjs.org/router/-/router-0.5.14.tgz", "integrity": "sha512-hwtqY2JKzyv25BK64n+sFv8QxDtA/+1HHQmCWuwjWOkIOi2UMud8WHazA4t0Pkzprbd++7kYYHdgsxDr2auHmw==", "signatures": [{"sig": "MEUCIH6qRTjnrCE258FxQTYdzXZHCnCUI4JT1Ngq/YiCV/RbAiEAi+lc6nv+0DmaDsEwKCdnqtbtQFrYEl7Vf++x6lWnZEg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.1.12", "description": "A lean and mean web router", "directories": {}, "_nodeVersion": "v0.6.14", "dependencies": {"common": ">=0.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.5.15": {"name": "router", "version": "0.5.15", "_id": "router@0.5.15", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "850033deeb809d1d5390cd1239e82cc254c60c6f", "tarball": "https://registry.npmjs.org/router/-/router-0.5.15.tgz", "integrity": "sha512-U5r7ka2qT+i9QoybKcSwrcPcaZaucdRE+H/5/F1w3A3Uzo95wQVgV2Bew7nLWda53uVbgOfWg1CYxIISo6OkoQ==", "signatures": [{"sig": "MEQCID1sDyAoB8f/Res22jgUwYKVUttfRjqE9IuW5An8B5mZAiACIXxBOzRrAE+vywGRqTzd58MlnRJX8J2Rn7O1S6BJeg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.1.12", "description": "A lean and mean web router", "directories": {}, "_nodeVersion": "v0.6.14", "dependencies": {"common": ">=0.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.5.16": {"name": "router", "version": "0.5.16", "_id": "router@0.5.16", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "372c0ed1c967fa034c487ac11027db2dee4ac540", "tarball": "https://registry.npmjs.org/router/-/router-0.5.16.tgz", "integrity": "sha512-I4jHPpPn0IruKbSQSOyxXxtcgub/m6iKYahxgdK3aKjdPZf6gUqnTRN7geuAK7KYvjHxuKJcq4JD1T5JnFd9aQ==", "signatures": [{"sig": "MEYCIQCQDpZuRD7hM9bxfJvXIGqKpZ4e8Yz2obuwUn/Lu/Bo3gIhALC/VmT1yYtcAVLmiKqkT39C3HE/Uf/U0ary7R3LMx84", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.1.12", "description": "A lean and mean web router", "directories": {}, "_nodeVersion": "v0.6.14", "dependencies": {"common": ">=0.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.5.17": {"name": "router", "version": "0.5.17", "_id": "router@0.5.17", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "efa064a0e2b9b44bf7baf3c372dc6f52570f9c6d", "tarball": "https://registry.npmjs.org/router/-/router-0.5.17.tgz", "integrity": "sha512-yW82Qu8Of1/YDYAEApFgg57fTelNMC0MS0SBo4twAJ5k87sevNlpAiuLHXwDxFeHQY5T1SH2jsZbZBYEO/XxyA==", "signatures": [{"sig": "MEUCIQCTG3cfvvPPudhXoWObOakezA4Pm55FBF0/Rqyre6jxwgIgGGXwMtSOGPWj6p/qEif/tm7rrL/zo7Md05DGkNKuHUg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.1.12", "description": "A lean and mean web router", "directories": {}, "_nodeVersion": "v0.6.14", "dependencies": {"common": ">=0.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.5.18": {"name": "router", "version": "0.5.18", "_id": "router@0.5.18", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "0fdd8601d17f515aa264e6d6a54156d0f0eab422", "tarball": "https://registry.npmjs.org/router/-/router-0.5.18.tgz", "integrity": "sha512-JY<PERSON>eh1LPqs5gjGGxSuM20Ex6+uHoawmEAwiZ6m2/OgIG3WfFX/BucyhMajWhII8UM0nX5OLoIdJ0VtSPo9s3PQ==", "signatures": [{"sig": "MEYCIQDVH72e8bhLKjTcVeQ8T9IeR2BIVzYlGqScwHN/i6Za3AIhAPlDusXX5AFXX98evhL4IQlhMz5yNjPd0hUlVLsCGtSe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.1.16", "description": "A lean and mean web router", "directories": {}, "_nodeVersion": "v0.6.15", "dependencies": {"common": ">=0.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.5.19": {"name": "router", "version": "0.5.19", "_id": "router@0.5.19", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "c6a247fdf5668890f8ef0da2265c4e51a351135f", "tarball": "https://registry.npmjs.org/router/-/router-0.5.19.tgz", "integrity": "sha512-o1TEBzoXpgQHzn5dBkGe9Dz7OhJeF2KE2fw/Iyuj136Fb6NdkTdgz9yf/kFAM5fuR5LmaUJk0u4lm9z5o4Xwgg==", "signatures": [{"sig": "MEYCIQCVV2izpIioeTA/L4h0RGzEaRoNJzrZAAYV+UsMTcAj0wIhAISIpHnnOf1WM06NxetC6ZSLsLT9MD5uqA9+9jFwlZtw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": "*"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.1.19", "description": "A lean and mean web router", "directories": {}, "_nodeVersion": "v0.6.16", "dependencies": {"common": ">=0.1.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.6.0": {"name": "router", "version": "0.6.0", "keywords": ["connect", "middleware", "router", "route", "http"], "_id": "router@0.6.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "f50782254782ef7cdebd6efe4ce9c63eb3a0ec23", "tarball": "https://registry.npmjs.org/router/-/router-0.6.0.tgz", "integrity": "sha512-rHramhTPOnFX2k4pRdMhyebI0sH8v22qo/sBVoxRcJgBaUG1KCqirlhR5R9mygGPW7MQlsA4UhuRxtEXtRuIpQ==", "signatures": [{"sig": "MEYCIQDuOAud7hw97ICUnTIgE1yF59DIeedGY96nTNC5om1KDwIhAOQLFZE3aKqmm9cqFwxIMoseFsNmwZRZWWgcAYWbBBmX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/gett/router.git", "type": "git"}, "_npmVersion": "1.1.21", "description": "A lean and mean http router", "directories": {}, "_nodeVersion": "v0.6.18", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.6.1": {"name": "router", "version": "0.6.1", "keywords": ["connect", "middleware", "router", "route", "http"], "_id": "router@0.6.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "1b724503370129c081c69be4f7fdc3d06c781839", "tarball": "https://registry.npmjs.org/router/-/router-0.6.1.tgz", "integrity": "sha512-eUKuKOmuajIaNFB/ZvHQfypuUcly6u46Ph5MWGlWUUsIIzGwYlYY59nynrkAkfaaTGK+PysIho7a5F3OT4BonQ==", "signatures": [{"sig": "MEQCICYLU/EQFrFSWDCt1STtSSfjh3+A6HHgKgSdKW+TWE1tAiAproQ3RuceO0n0LmM5Y7TENrgO3SstY0Ub5v9bUGhH/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/gett/router.git", "type": "git"}, "_npmVersion": "1.1.59", "description": "A lean and mean http router", "directories": {}, "dependencies": {}}, "0.6.2": {"name": "router", "version": "0.6.2", "keywords": ["connect", "middleware", "router", "route", "http"], "_id": "router@0.6.2", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "6f04063a2d04eba3303a1bbc6765eef63037cf3d", "tarball": "https://registry.npmjs.org/router/-/router-0.6.2.tgz", "integrity": "sha512-tTMBCMVSP+sfSQyBbFrUuVPfxfKBhy6Hciz/SttYRxKteFqjPljrH2GqXoPSCOeubSgxPDRAbQOCvQzp2hNTOA==", "signatures": [{"sig": "MEUCIDD3XyZAwBmV6+11X7HGJZ191KIzF8B3fSv2mJM00dLAAiEAuzHdvKq6zSOngLVhCvz7Z5xKR3a+iTA4aKCyOfaRaqw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/gett/router.git", "type": "git"}, "_npmVersion": "1.1.59", "description": "A lean and mean http router", "directories": {}, "dependencies": {}}, "1.0.0-beta.1": {"name": "router", "version": "1.0.0-beta.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "router@1.0.0-beta.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/router", "bugs": {"url": "https://github.com/pillarjs/router/issues"}, "dist": {"shasum": "09408d588a4a3938187217250913d7f6365546e0", "tarball": "https://registry.npmjs.org/router/-/router-1.0.0-beta.1.tgz", "integrity": "sha512-TfoYv0Bz5yEFUeW6CevhaN/vw0RClf0oYNR45JC75xUPNwdpM++Dai7ymaH3xQKii6ZS/9ocEaiXf95RlICLnQ==", "signatures": [{"sig": "MEUCIE1GjPZ0x8zAeOEhTQHw6fJA26RcHZwMByKlNREf1tCUAiEAnzPWVGE0GQJbvTLZTRSrzSqsUbzHn5HOmUdJQZvKsuk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "09408d588a4a3938187217250913d7f6365546e0", "engines": {"node": ">= 0.8"}, "gitHead": "21d1ade2f7b7431eebc7076e2bf79c32d7996a4d", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/pillarjs/router", "type": "git"}, "_npmVersion": "1.4.21", "description": "Simple middleware-style router", "directories": {}, "dependencies": {"debug": "~2.1.0", "methods": "~1.1.0", "parseurl": "~1.3.0", "utils-merge": "1.0.0", "path-to-regexp": "0.1.3"}, "devDependencies": {"after": "0.8.1", "mocha": "~2.0.1", "istanbul": "0.3.2", "supertest": "~0.14.0", "finalhandler": "0.3.2"}}, "1.0.0-beta.2": {"name": "router", "version": "1.0.0-beta.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "router@1.0.0-beta.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/router", "bugs": {"url": "https://github.com/pillarjs/router/issues"}, "dist": {"shasum": "a7db7dc53ae2dada4391e1743337887c35f90198", "tarball": "https://registry.npmjs.org/router/-/router-1.0.0-beta.2.tgz", "integrity": "sha512-J61sIAazD4GawygsrYkg8ma5O0A5XbORmwpNruPSvtEBHyydAwUr7YVW973X2XHP2Amp3i9j/4Gtr/7IU4eZmg==", "signatures": [{"sig": "MEQCIE9g+EIhB8+q9oH5d42F5Gt6iZzaQfmvt40GPz7RIEi2AiA1yMtmWbturlKV8CR0ysFVgflOSXt4raK4U6DfPIsTxQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "a7db7dc53ae2dada4391e1743337887c35f90198", "engines": {"node": ">= 0.8"}, "gitHead": "11f7e5a12947dbb0c384ea6bf24721e15d155a6e", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/pillarjs/router", "type": "git"}, "_npmVersion": "1.4.21", "description": "Simple middleware-style router", "directories": {}, "dependencies": {"debug": "~2.1.0", "methods": "~1.1.0", "parseurl": "~1.3.0", "utils-merge": "1.0.0", "path-to-regexp": "0.1.3"}, "devDependencies": {"after": "0.8.1", "mocha": "~2.0.1", "istanbul": "0.3.2", "supertest": "~0.14.0", "finalhandler": "0.3.2"}}, "1.0.0-beta.3": {"name": "router", "version": "1.0.0-beta.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "router@1.0.0-beta.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/router", "bugs": {"url": "https://github.com/pillarjs/router/issues"}, "dist": {"shasum": "767237015ed18be869ad9b467e1e0899f1718bb2", "tarball": "https://registry.npmjs.org/router/-/router-1.0.0-beta.3.tgz", "integrity": "sha512-3FullsOQi0baWnABhaNg/cCdygfQo0kNDdR5vmb1TUqTy3bikt32X6rTl8+92PuVptZzl/62D2vUMIL80Bn5jg==", "signatures": [{"sig": "MEUCIFmb5QunHlXvX7JumRkBG6kX9uTKxYyLlgxzCltQsCVPAiEAgECsHRq4/kwxPgmdL8qgcVb4XLYeKMw3XE3dCoHJj80=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "767237015ed18be869ad9b467e1e0899f1718bb2", "engines": {"node": ">= 0.8"}, "gitHead": "da00dbddd1bbb1e024c5166a99fbe1aa3e250ae8", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/pillarjs/router", "type": "git"}, "_npmVersion": "1.4.28", "description": "Simple middleware-style router", "directories": {}, "dependencies": {"debug": "~2.1.1", "methods": "~1.1.1", "parseurl": "~1.3.0", "utils-merge": "1.0.0", "path-to-regexp": "0.1.3"}, "devDependencies": {"after": "0.8.1", "mocha": "~2.1.0", "istanbul": "0.3.5", "supertest": "~0.15.0", "finalhandler": "0.3.3"}}, "1.0.0": {"name": "router", "version": "1.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "router@1.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/router", "bugs": {"url": "https://github.com/pillarjs/router/issues"}, "dist": {"shasum": "2481269324deb88a528079540a1b2b7d33351c6f", "tarball": "https://registry.npmjs.org/router/-/router-1.0.0.tgz", "integrity": "sha512-ebAxF5nkBn1mizy1+J7fTKNEf5bwat8DiKDKDFIFJxQBLfbbqGwiX3awoo9qAYz8PBcXI7B4aIFaUhrCqnG/ig==", "signatures": [{"sig": "MEUCIA4fj9yQ5Fj9JNM5FKt/tYohTlMafsGzCrRtIcyOAut1AiEAi1eAg8gOcgUkT3sm+7FWDmOZJxawOEiwi4EPYmw/fKU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "2481269324deb88a528079540a1b2b7d33351c6f", "engines": {"node": ">= 0.8"}, "gitHead": "348c58ba0723cea7aee2df5874096c644f99925d", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/pillarjs/router", "type": "git"}, "_npmVersion": "1.4.28", "description": "Simple middleware-style router", "directories": {}, "dependencies": {"debug": "~2.1.1", "methods": "~1.1.1", "parseurl": "~1.3.0", "utils-merge": "1.0.0", "array-flatten": "1.0.2", "path-to-regexp": "0.1.3"}, "devDependencies": {"after": "0.8.1", "mocha": "~2.1.0", "istanbul": "0.3.5", "supertest": "~0.15.0", "finalhandler": "0.3.3"}}, "1.1.0": {"name": "router", "version": "1.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "router@1.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/router", "bugs": {"url": "https://github.com/pillarjs/router/issues"}, "dist": {"shasum": "6a70f1c45775cf8aa5c132da35ef86b63221e331", "tarball": "https://registry.npmjs.org/router/-/router-1.1.0.tgz", "integrity": "sha512-UgS+XUQpL9/mHwWvba8wU3FXBSYci+NzZklhri1hIzz9BZw8rv8oXtBG87irJZRQhFtMc08gqtIOwnHYjxwrYw==", "signatures": [{"sig": "MEUCIFZKeaXbRH3SIB7ywbXzpvrCPGRm6dwslDuFjZMJsGYwAiEAxnETRNP1dgldU+UFSkueDNjr8TJRfXfUAjaIxXU6l6c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "6a70f1c45775cf8aa5c132da35ef86b63221e331", "engines": {"node": ">= 0.8"}, "gitHead": "99893af6a44073c0d3b47220e3343803a80ccd98", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/pillarjs/router", "type": "git"}, "_npmVersion": "1.4.28", "description": "Simple middleware-style router", "directories": {}, "dependencies": {"debug": "~2.1.3", "methods": "~1.1.1", "parseurl": "~1.3.0", "utils-merge": "1.0.0", "array-flatten": "1.0.2", "path-to-regexp": "0.1.3", "setprototypeof": "1.0.0"}, "devDependencies": {"after": "0.8.1", "mocha": "~2.2.4", "istanbul": "0.3.9", "supertest": "~0.15.0", "finalhandler": "0.3.5"}}, "1.1.1": {"name": "router", "version": "1.1.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "router@1.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/router", "bugs": {"url": "https://github.com/pillarjs/router/issues"}, "dist": {"shasum": "55b46564d5ad099cc2802b45775b69acfc7d8faa", "tarball": "https://registry.npmjs.org/router/-/router-1.1.1.tgz", "integrity": "sha512-bIR8pdZ7qKcxjqalO47B9YcFbNBSoudHzhmCQDXoanfLMTYH+ZjeRNlY8h6Bqzaba0//+N3bSrx5gTl0gqQ2HQ==", "signatures": [{"sig": "MEUCIA8ss0/KurfarmxF+BcSRRB5SRWB2Timmo5LL9uEs3atAiEA+JMyIl5ZmbwhxkM2sKPuMW+VLHPEwplKcOm3qb48hv4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "55b46564d5ad099cc2802b45775b69acfc7d8faa", "engines": {"node": ">= 0.8"}, "gitHead": "12ef8ea0bfd0eb287d493a44447f2d1ccf90182a", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/pillarjs/router", "type": "git"}, "_npmVersion": "1.4.28", "description": "Simple middleware-style router", "directories": {}, "dependencies": {"debug": "~2.2.0", "methods": "~1.1.1", "parseurl": "~1.3.0", "utils-merge": "1.0.0", "array-flatten": "1.1.0", "path-to-regexp": "0.1.3", "setprototypeof": "1.0.0"}, "devDependencies": {"after": "0.8.1", "mocha": "2.2.5", "istanbul": "0.3.9", "supertest": "1.0.1", "finalhandler": "0.3.6"}}, "1.1.2": {"name": "router", "version": "1.1.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "router@1.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/router", "bugs": {"url": "https://github.com/pillarjs/router/issues"}, "dist": {"shasum": "04e66e25174a2f0e7a8d568cf2b49581758396fc", "tarball": "https://registry.npmjs.org/router/-/router-1.1.2.tgz", "integrity": "sha512-05ySEEpuR5Ilny8nvO49oUZdrnlsKkneU2cLC0fKLOrI4h4F2jwVezCXBwggTw+9/Yuw4bE5ChfjsNTCggicig==", "signatures": [{"sig": "MEYCIQCduFTcZPU/O86XkJe8IfY5mzyec+5fUyvo100QQ/fsZAIhAI/uICSZln2RRf32x+99rMHt/inIDkucNt2XmNBvv2dR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "04e66e25174a2f0e7a8d568cf2b49581758396fc", "engines": {"node": ">= 0.8"}, "gitHead": "a1b58648ee78dd44be709f64f45cd7b9e8e555e2", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/pillarjs/router", "type": "git"}, "_npmVersion": "1.4.28", "description": "Simple middleware-style router", "directories": {}, "dependencies": {"debug": "~2.2.0", "methods": "~1.1.1", "parseurl": "~1.3.0", "utils-merge": "1.0.0", "array-flatten": "1.1.0", "path-to-regexp": "0.1.6", "setprototypeof": "1.0.0"}, "devDependencies": {"after": "0.8.1", "mocha": "2.2.5", "istanbul": "0.3.17", "supertest": "1.0.1", "finalhandler": "0.4.0"}}, "1.1.3": {"name": "router", "version": "1.1.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "router@1.1.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/router", "bugs": {"url": "https://github.com/pillarjs/router/issues"}, "dist": {"shasum": "2106b620b8f6850adb8dc8721dd5fee6190f85f6", "tarball": "https://registry.npmjs.org/router/-/router-1.1.3.tgz", "integrity": "sha512-J5buPndqiHfB89DnpkZ9xPrBzzWPhc5qjs1iL559EnmPBJ7/WrOlsehLSnYdD7VOx890Ouri/NuMPFr46N+SaQ==", "signatures": [{"sig": "MEQCICI3xPA5lZ1Uf5P4h6+2EgFcmhUeEu9MWhoDn47oJwomAiBRI05ltn76wytzQDWnzEfY76YS+wTOgvkSC9pKti8kJQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "2106b620b8f6850adb8dc8721dd5fee6190f85f6", "engines": {"node": ">= 0.8"}, "gitHead": "6bae01f1e9de52445b0f353b720214ca798b3089", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/pillarjs/router", "type": "git"}, "_npmVersion": "1.4.28", "description": "Simple middleware-style router", "directories": {}, "dependencies": {"debug": "~2.2.0", "methods": "~1.1.1", "parseurl": "~1.3.0", "utils-merge": "1.0.0", "array-flatten": "1.1.1", "path-to-regexp": "0.1.7", "setprototypeof": "1.0.0"}, "devDependencies": {"after": "0.8.1", "mocha": "2.2.5", "istanbul": "0.3.17", "supertest": "1.0.1", "finalhandler": "0.4.0"}}, "1.1.4": {"name": "router", "version": "1.1.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "router@1.1.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/router", "bugs": {"url": "https://github.com/pillarjs/router/issues"}, "dist": {"shasum": "5d449dde9d6e0ad5c3f53369064baf7798834a97", "tarball": "https://registry.npmjs.org/router/-/router-1.1.4.tgz", "integrity": "sha512-PPnsli3AEYKv08saPMB03TPoP+FDmnrXXSgr8N+i4YPWyp9JN0/gx1irKqSZJFiYcQ6F3F9XX1InqxOu91N/VA==", "signatures": [{"sig": "MEYCIQCZ3urhqKyf2mxQvxoJPo09X0oh5njzaZ4b90p+CubBngIhAMwXsd4BhjZmgQWpGPxxnrKA4aDutUYqdP7zO83WDtHU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "5d449dde9d6e0ad5c3f53369064baf7798834a97", "engines": {"node": ">= 0.8"}, "gitHead": "d3a598d3afec5bf55d683f25887bbbf8fd65e4f8", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/pillarjs/router", "type": "git"}, "_npmVersion": "1.4.28", "description": "Simple middleware-style router", "directories": {}, "dependencies": {"debug": "~2.2.0", "methods": "~1.1.2", "parseurl": "~1.3.1", "utils-merge": "1.0.0", "array-flatten": "2.0.0", "path-to-regexp": "0.1.7", "setprototypeof": "1.0.0"}, "devDependencies": {"after": "0.8.1", "mocha": "2.3.4", "istanbul": "0.4.2", "supertest": "1.1.0", "finalhandler": "0.4.1"}}, "1.1.5": {"name": "router", "version": "1.1.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "router@1.1.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/router#readme", "bugs": {"url": "https://github.com/pillarjs/router/issues"}, "dist": {"shasum": "c9c6935201b30ac1f227ada6af86e8cea6515387", "tarball": "https://registry.npmjs.org/router/-/router-1.1.5.tgz", "integrity": "sha512-p0l/vfNVavaB7DXh3BxJv3NMZi3QKEiIYDnLVyOV5Bo/7Uo3YYk3z65T2IJHG7cfC9QV8UanGhHLOIfPoS9A5g==", "signatures": [{"sig": "MEUCIQCKZMrjIBeKqGAXXVyHzzECATrG7P/LgCfNhMQc21428AIgGNWrLB7pNap8jyjpnYyLn0BMaDWlOuYhSWhz/GJd8MM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "c9c6935201b30ac1f227ada6af86e8cea6515387", "engines": {"node": ">= 0.8"}, "gitHead": "ed661a2b77d07dfd297dc6698ccf7d490ea41bcf", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/router.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "Simple middleware-style router", "directories": {}, "_nodeVersion": "4.6.1", "dependencies": {"debug": "~2.2.0", "methods": "~1.1.2", "parseurl": "~1.3.1", "utils-merge": "1.0.0", "array-flatten": "2.1.1", "path-to-regexp": "0.1.7", "setprototypeof": "1.0.2"}, "devDependencies": {"after": "0.8.2", "mocha": "2.5.3", "istanbul": "0.4.5", "supertest": "1.1.0", "finalhandler": "0.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/router-1.1.5.tgz_1485653619199_0.8075110947247595", "host": "packages-18-east.internal.npmjs.com"}}, "1.2.0": {"name": "router", "version": "1.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "router@1.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/router#readme", "bugs": {"url": "https://github.com/pillarjs/router/issues"}, "dist": {"shasum": "e93d4b8b9e4d2f727d24f699622cf419aa310ecd", "tarball": "https://registry.npmjs.org/router/-/router-1.2.0.tgz", "integrity": "sha512-kgQLxBSLnRU6vv2u2CzyUm0merU2kDYdWO8scUABfMdzR9DiyS2eXOCzjrPF1/yuGHaUazLBMG4Yk4RdRE5NHA==", "signatures": [{"sig": "MEUCIQDUnmTf5SQDGy3srkopybc+sQ8EeWwXfTKAKP88P484MwIgTDsKsc/BRbhSQzCt/oDNuwEj6zFftSqFAJQyoDDaSdQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "e93d4b8b9e4d2f727d24f699622cf419aa310ecd", "engines": {"node": ">= 0.8"}, "gitHead": "f4d2b2aec52128a2fe67f5603fdc92dd3db0bf6b", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/router.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Simple middleware-style router", "directories": {}, "_nodeVersion": "4.7.3", "dependencies": {"debug": "2.6.1", "methods": "~1.1.2", "parseurl": "~1.3.1", "utils-merge": "1.0.0", "array-flatten": "2.1.1", "path-to-regexp": "0.1.7", "setprototypeof": "1.0.2"}, "devDependencies": {"after": "0.8.2", "mocha": "2.5.3", "istanbul": "0.4.5", "supertest": "1.1.0", "finalhandler": "1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/router-1.2.0.tgz_1487400158381_0.19565443880856037", "host": "packages-12-west.internal.npmjs.com"}}, "1.3.0": {"name": "router", "version": "1.3.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "router@1.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/router#readme", "bugs": {"url": "https://github.com/pillarjs/router/issues"}, "dist": {"shasum": "15b24075c1de4a3d3f39808c5d7344a1564417c8", "tarball": "https://registry.npmjs.org/router/-/router-1.3.0.tgz", "integrity": "sha512-eiQAdStRFLXbMhAkXB/XMkWE1IV+hcZZkt64VizXHHBbFlkG7eJSihAWSOacSmgsLxzAWpULEMiVTJEaSJor0g==", "signatures": [{"sig": "MEUCIChnl6ZF985dC4JEBnmOEF427ichsvg91uhvtwGv3RPyAiEA9orkCgfEXJHjUi9zhFDmazYY0VUcP5bUjfdsS20NDMY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "15b24075c1de4a3d3f39808c5d7344a1564417c8", "engines": {"node": ">= 0.8"}, "gitHead": "89aacfa4b74279b4f09632d7dc90b1ef61e91dcc", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/router.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Simple middleware-style router", "directories": {}, "_nodeVersion": "4.7.3", "dependencies": {"debug": "2.6.1", "methods": "~1.1.2", "parseurl": "~1.3.1", "utils-merge": "1.0.0", "array-flatten": "2.1.1", "path-to-regexp": "0.1.7", "setprototypeof": "1.0.3"}, "devDependencies": {"after": "0.8.2", "mocha": "2.5.3", "istanbul": "0.4.5", "supertest": "1.1.0", "finalhandler": "1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/router-1.3.0.tgz_1488092428036_0.5783074989449233", "host": "packages-12-west.internal.npmjs.com"}}, "1.3.1": {"name": "router", "version": "1.3.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "router@1.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/router#readme", "bugs": {"url": "https://github.com/pillarjs/router/issues"}, "dist": {"shasum": "e59ef64fafc22194a196986834d887058d76af47", "tarball": "https://registry.npmjs.org/router/-/router-1.3.1.tgz", "integrity": "sha512-oO9FwnYPrOcPBWoMmoFxUi863PsBWVjcXys6Af88Z8juqKCVgQOPQr9YchKT9skUrSyVy62gUdCYdYB4FnDX9w==", "signatures": [{"sig": "MEUCIQCCrYaAF1iQrxwfdOh0LyTeDAH8aBY32BAZEJBeEUU4VwIgaeyCe1l/7yw8W3jW4g5SvFxoh057EyfMVWd8YLjt/8I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "e59ef64fafc22194a196986834d887058d76af47", "engines": {"node": ">= 0.8"}, "gitHead": "0806cac2ea83cfc1532daf7a47efe90ee0b3bda2", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/router.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Simple middleware-style router", "directories": {}, "_nodeVersion": "6.10.3", "dependencies": {"debug": "2.6.8", "methods": "~1.1.2", "parseurl": "~1.3.1", "utils-merge": "1.0.0", "array-flatten": "2.1.1", "path-to-regexp": "0.1.7", "setprototypeof": "1.0.3"}, "devDependencies": {"after": "0.8.2", "mocha": "2.5.3", "istanbul": "0.4.5", "supertest": "1.1.0", "finalhandler": "1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/router-1.3.1.tgz_1495255090237_0.12299346318468451", "host": "s3://npm-registry-packages"}}, "1.3.2": {"name": "router", "version": "1.3.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "router@1.3.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/router#readme", "bugs": {"url": "https://github.com/pillarjs/router/issues"}, "dist": {"shasum": "bfaa16888a5283d5ee40d999da7a9fa15296a60c", "tarball": "https://registry.npmjs.org/router/-/router-1.3.2.tgz", "integrity": "sha512-HyiHMDfHAmKe+k3fiHjiM33fFl9KITVhCyR6qqSPYyv308hghR7k5LapZlAUuYjkcnx1q36lZ/Ab/i3GrlVqTQ==", "signatures": [{"sig": "MEUCIDS8RzfY+BJx7iu4JnD/a1RKyh+3Fz45h4GxbMza4P10AiEAqRRjFBjWQ0sFuifPDFl9DoKkWLLUsta3RwF2DnB4Bto=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "bfaa16888a5283d5ee40d999da7a9fa15296a60c", "engines": {"node": ">= 0.8"}, "gitHead": "0c68537febfb955d8cfa5fa6ea7452bbecb5049c", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/router.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Simple middleware-style router", "directories": {}, "_nodeVersion": "6.11.1", "dependencies": {"debug": "2.6.9", "methods": "~1.1.2", "parseurl": "~1.3.2", "utils-merge": "1.0.1", "array-flatten": "2.1.1", "path-to-regexp": "0.1.7", "setprototypeof": "1.1.0"}, "devDependencies": {"after": "0.8.2", "mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "finalhandler": "1.0.6", "eslint-plugin-markdown": "1.0.0-beta.6"}, "_npmOperationalInternal": {"tmp": "tmp/router-1.3.2.tgz_1506303196824_0.8141703868750483", "host": "s3://npm-registry-packages"}}, "1.3.3": {"name": "router", "version": "1.3.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "router@1.3.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/router#readme", "bugs": {"url": "https://github.com/pillarjs/router/issues"}, "dist": {"shasum": "c142f6b5ea4d6b3359022ca95b6580bd217f89cf", "tarball": "https://registry.npmjs.org/router/-/router-1.3.3.tgz", "fileCount": 7, "integrity": "sha512-5OicpuzMYzAHngq8W1P6MtRn7sHZziZh5ppNMiOJ67beMIv1nPsJ0h5eeIDsvRguzxhqGdzJGn0aprerS2cVYw==", "signatures": [{"sig": "MEYCIQCVC11GL633l+US1aAPZDqL3fRG8vbLsK38xz67HchGmQIhAKZTwLPakyjQoi5bHqMvEOryRr5KpNC3uoe83sLTJ7jo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40132}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "c142f6b5ea4d6b3359022ca95b6580bd217f89cf", "engines": {"node": ">= 0.8"}, "gitHead": "51c56e61b40ab486a936df7d6b09db006a3e9159", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/router.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Simple middleware-style router", "directories": {}, "_nodeVersion": "6.14.3", "dependencies": {"debug": "2.6.9", "methods": "~1.1.2", "parseurl": "~1.3.2", "utils-merge": "1.0.1", "array-flatten": "2.1.1", "path-to-regexp": "0.1.7", "setprototypeof": "1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"after": "0.8.2", "mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "finalhandler": "1.1.1", "eslint-plugin-markdown": "1.0.0-beta.6"}, "_npmOperationalInternal": {"tmp": "tmp/router_1.3.3_1530892911300_0.6552788619024841", "host": "s3://npm-registry-packages"}}, "2.0.0-alpha.1": {"name": "router", "version": "2.0.0-alpha.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "router@2.0.0-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/router#readme", "bugs": {"url": "https://github.com/pillarjs/router/issues"}, "dist": {"shasum": "9188213b972215e03ef830e0ac77837870085f6d", "tarball": "https://registry.npmjs.org/router/-/router-2.0.0-alpha.1.tgz", "fileCount": 7, "integrity": "sha512-fz/T/qLkJM6RTtbqGqA1+uZ88ejqJoPyKeJAeXPYjebA7HzV/UyflH4gXWqW/Y6SERnp4kDwNARjqy6se3PcOw==", "signatures": [{"sig": "MEYCIQDhzJKeH5WlXctyg1Esc0dgbdtRz5tso9rw0aZiO/5BegIhAPKOmuePO03+Pz09FCmfIfzy2dhQ75Vkncg2B3Das9Sr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41241, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbW1GMCRA9TVsSAnZWagAASbMP+wQ5Ih2aYtiCDmJjG2Ye\nTkTM82g196aoikalOsqTC7lL7+G6RSfXMUoVPM5+eEASl5i+SrgHeM/FJgOo\n7M2NZMHadOZ71jtq//Jbgpk7PIifZW6f0WOuWXMHCTS0vINKTrdGoLa37BSY\nTiJN+LP03Aqdn6ASAQtDLFGbBLqED/UnpsAEc/OQZmO3NfOo9hUwzhTsQ7sj\n4l/bZtsGj8amVmoJFVAEFbmaf+I8uZSoILzYe9RD2ZJ7vBw4ZcHBM/uu7tUX\n2/Un/AOTlwDzCOvo1LBFV9nieStVVbXs75XvX9Lm+A6feNEdg27Uz4U5AOJT\nmsc3CwdTqIEm3aPEsPcf7tmO44EHsnrdXldVAGGaF9krSfcrk/0k6D3eQlAG\nl5/EyWodu0OcrNyhEk2KoxBnRtvGE1BHru/P5MRYGU3xri/wKboL4ipQSyas\nB1b7keUhHM0Q7y0Tl+1NVMPDJ++2KF4YtJLWtEhNmD6H2nFr1vJ6TNvbEIFR\nVJue7YQR150LAph8UkTXDkm9xaLYr7LcdF+t1I/qCtkzI2J5Z9Yjb8ExAiF5\nUHwzrDHHWhZWxxjH314IS7OLLGpdm4YU949TfoTzPR0UlrPmAzD9jMMQgn9S\nhRg8MIrvR2OxszWk8xQ/KFp/CyBq0RmBm9/VG77PXHfP2E4SmGNbW5Sf1irT\nJt2F\r\n=TNo9\r\n-----END PGP SIGNATURE-----\r\n"}, "files": ["lib/", "LICENSE", "HISTORY.md", "README.md", "index.js"], "engines": {"node": ">= 0.10"}, "gitHead": "dbaa56ece659bb4c03f5e9f58826b82254ef9b60", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "nyc --reporter=text npm test", "test-travis": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/router.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Simple middleware-style router", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"debug": "3.1.0", "methods": "~1.1.2", "parseurl": "~1.3.2", "utils-merge": "1.0.1", "array-flatten": "2.1.1", "path-to-regexp": "0.1.7", "setprototypeof": "1.1.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "10.3.2", "after": "0.8.2", "mocha": "3.5.3", "eslint": "3.19.0", "supertest": "1.2.0", "finalhandler": "1.1.1", "eslint-plugin-markdown": "1.0.0-beta.6"}, "_npmOperationalInternal": {"tmp": "tmp/router_2.0.0-alpha.1_1532711308330_0.6289800034086013", "host": "s3://npm-registry-packages"}}, "1.3.4": {"name": "router", "version": "1.3.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "router@1.3.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/router#readme", "bugs": {"url": "https://github.com/pillarjs/router/issues"}, "dist": {"shasum": "f4e4a1648eada2e16fd622fc389c90daf7307ed8", "tarball": "https://registry.npmjs.org/router/-/router-1.3.4.tgz", "fileCount": 7, "integrity": "sha512-a5uZFwgKExBZzh4U5jujU05DkImy9/ub8omiU7RlZlNnSF07tjvNJTOgHdZOjKDeUZqlkpz8CjDoz5SY3kimOA==", "signatures": [{"sig": "MEQCIAUs4JRG3gRRWZ3Ppbh+JjhOPvEtoRaBT1CmKiRvGWf/AiAsfd8UEs7aQrkFnOkw/QuSEvwAFfE3EBSjcVt80wdtpg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40383, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeK6WyCRA9TVsSAnZWagAADOwP/j57/YdH+NEGZ/0sIA3K\nkTf61GLSGAcRWZHZa2KO9q0C757BxfBMhxnzI7tHFWQZGsvkoouMnalnYvz3\n1GjpNI6/Am9zx72Wm/SVzD6ycvlcIZhPaNltowcLKlyushew9o9LyvZNYmaB\neE7oJDgYDasPfhm6lsnd0O6HQeZBmiTYXJoeBvm+/HBRhw0/HAu/+wyQ4uVm\nVsqbnkm4P0RaRQIruQSDiLJIETW4N6QcUWPjY3JGKVb1Cc056+1L/AKwR4yM\nukM+SZKeNpp1gR69y9qm1PBsiu1ZTT7QmnT/iyn3wwHV+odO8X6OUYy+qL70\nWeIfSVeBdbcWNOS5k/m6Yg36DI0td+qarctktbtkO9ppFXTiy2aJSg8HSuce\nuBOrE/wF/nMq8mCHkZkjohAdkm4fqQAikvWxRuBTC3PN98ycSlH7YPLej86/\nAiWVvTt5jKM94s0BpDyQessJ7RI2uMuqPqRIlJeALs37X93JPN8aQMbgU+GT\nyaX2x8EXOQb8yx2flLuCpsEma6BLBaX3h0kWhpJA3+jKzaWrRouixgS0EIug\nhdaW7CQGx5CiNSOz4anroTupbs+hYwBGI7X2Mq2h07nQ9jgc4NsjHf89pbb0\ngfa6ZtbknpwG23n2BW/foAI1dxXdqEnTfXGPR+N/JxDBhICayMV3akQBRzkK\n2KSu\r\n=0/vW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}, "gitHead": "0bb4608c8a8cd9fb5a142e4fc86d45c3df97c5d5", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/router.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "Simple middleware-style router", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"debug": "2.6.9", "methods": "~1.1.2", "parseurl": "~1.3.3", "utils-merge": "1.0.1", "array-flatten": "3.0.0", "path-to-regexp": "0.1.7", "setprototypeof": "1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"after": "0.8.2", "mocha": "7.0.0", "eslint": "6.8.0", "istanbul": "0.4.5", "supertest": "4.0.2", "safe-buffer": "5.2.0", "finalhandler": "1.1.2", "eslint-plugin-markdown": "1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/router_1.3.4_1579918770194_0.8870196914581736", "host": "s3://npm-registry-packages"}}, "1.3.5": {"name": "router", "version": "1.3.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "router@1.3.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/router#readme", "bugs": {"url": "https://github.com/pillarjs/router/issues"}, "dist": {"shasum": "cb2f47f74fd99a77fb3bc01cc947f46b79b1790f", "tarball": "https://registry.npmjs.org/router/-/router-1.3.5.tgz", "fileCount": 7, "integrity": "sha512-kozCJZUhuSJ5VcLhSb3F8fsmGXy+8HaDbKCAerR1G6tq3mnMZFMuSohbFvGv1c5oMFipijDjRZuuN/Sq5nMf3g==", "signatures": [{"sig": "MEYCIQCkhWlGxczc9HU6wXaVQYYpu8rVFPkYIf/Eia2n63DBaAIhAMvul13FROdu8giIF5sgg2Y9DSDdcYnzEJHED1MBY9vW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40694, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJees5dCRA9TVsSAnZWagAAmQgQAIXes8ZpZ2GfUzORyU8J\nyLaRsShOpLXpMr84lEAzmaVmwM74g4E+/cZnXuctbo/SeGqH4NaWHS073Kq3\nesXfUcaDcQ+I5SI701FbP1tD8BLP68dcST/iaXI7oCXpkX65opHd3I6jqk8X\nR7u45qYQUMqoBDLOzgbi8vLjEvA87WlJeWkLKS2s4mOeUt9Quu/rG7oChNHw\n4k8gGablyIfGJJhO4ihANzRWc9vM4ziEVjsF/oddWo97qGNmyXwnpQEcxr7M\nQtS/whp7LH2oMD+2np7OF8B01G8m4XC9nwrgg3HTARsq7BCJNjjCSh2APvrh\ns2MG0H4B52J7377NKSKyEsnipY74uwn8K+h9Lu2z+UwsPNfCpKr7c/LDeLWq\nG0+NPpBJUkQo9wqHr1ugLNJipTO1C7lBVckzdYQIPrOLDEVScTXohIk+sAIU\nGVEbxAt3O0MAhssjqN0XCjOFCJYHRBcQUv2qSBKNW4N3UrueBmyI8F89/i79\n5+buBzSq6psjzXxaxgoEI/SJ/XsNsd5JqJewZCJl5HuSxDZzwDa+YUEZsBlq\nUFYBmrgHlsBq5DghLsBDhNyusNF5Gr1IiHAt/zNZa16fnA3tV7YoH0ewxkLE\n+VOmp3OPkZiDNjesUCHyLHJl9BP2B9EMTMYXjowcDEzLOzZLFbjf9qL/h0Wb\nxHC0\r\n=9mbA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}, "gitHead": "fb3c003951191ced20bc88c2e3bbc5082daed2fd", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/router.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "Simple middleware-style router", "directories": {}, "_nodeVersion": "13.11.0", "dependencies": {"debug": "2.6.9", "methods": "~1.1.2", "parseurl": "~1.3.3", "utils-merge": "1.0.1", "array-flatten": "3.0.0", "path-to-regexp": "0.1.7", "setprototypeof": "1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"after": "0.8.2", "mocha": "7.1.1", "eslint": "6.8.0", "istanbul": "0.4.5", "supertest": "4.0.2", "safe-buffer": "5.2.0", "finalhandler": "1.1.2", "eslint-plugin-markdown": "1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/router_1.3.5_1585106525246_0.10484875046054332", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.1": {"name": "router", "version": "2.0.0-beta.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "router@2.0.0-beta.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/router#readme", "bugs": {"url": "https://github.com/pillarjs/router/issues"}, "dist": {"shasum": "86fb03143cee259497d8b658c7f13b1d20049331", "tarball": "https://registry.npmjs.org/router/-/router-2.0.0-beta.1.tgz", "fileCount": 7, "integrity": "sha512-GLoYgkhAGAiwVda5nt6Qd4+5RAPuQ4WIYLlZ+mxfYICI+22gnIB3eCfmhgV8+uJNPS1/39DOYi/vdrrz0/ouKA==", "signatures": [{"sig": "MEUCIA2gWRDrIOU55pN9OQzD9gz/YJSMkaKtGnizAyMT3XizAiEA3h80Fay407rkDQMGrdr//zCtJ8NsBBGx7E6ijDVNYBA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42756, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJegXDpCRA9TVsSAnZWagAAUvsP/2kGqm3T/eERRfWP1c6S\nsneJ8G26dBKJrfotm1XQCb+GEOJmmCtLnZAdhkQaWY7TWvEvcVVxqUDQpB/I\nw2fP5aivQNcrHeAJWzSK50N+mq+PgbMpBf/9632T5aVdJeRJlsh/R02YsQUL\n5yBxWyvcwYpbFWwEVw2paXE2JKZc//p8ROZTXMwH7SzBO6Qge6I9wwBclLG0\nDNmjiJN8/qCPQ46munAvakuKgS78D5JSN18STy/0vhC/vvjWUrsn3LNnpjAg\nQr4cpAMKHhjaCZjf4rNNNEv1CPvFuvOK91LmcLo7D/8iyMuYaBcYQfauzNfy\nLYW/XO3jxL6sbJ9PopJolomV1oApR/MuEI0IO9gKB8O5b/2hfKFLxebmRmlg\n8XHppcfHGONLgT564cHW8tcf8H8vcpOhJbb5RF654FUv/fI3Hpmk4C9UW+D2\nzR3ziseItvxaRw6EabXJmnS9Go04I8mrsKj7qociu1Le6sPYkgFXtiPAN+2Q\nuxVnehWssOUnDkWuakbPjWtVpYBGu4jm+XwM/+1lMtrRMa/8N9vde14UGP0V\nyu7mlcpycLAp7m2dnSl6V5Cg6r3MNhAzntBEFKQf6TsUgrcUTjwjZNfQ/JqY\nYDNF0y0NH6OeTU3IgBZ2lz3y2nsnqTtZKRxRmX3WXSgO/1npGKAyX5XoWw5z\np0LO\r\n=6srl\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.10"}, "gitHead": "ca507eebd99820ab13c623df629e64b7707c0dbe", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=text npm test", "test-travis": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/router.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "Simple middleware-style router", "directories": {}, "_nodeVersion": "13.11.0", "dependencies": {"methods": "~1.1.2", "parseurl": "~1.3.3", "utils-merge": "1.0.1", "array-flatten": "3.0.0", "path-to-regexp": "3.2.0", "setprototypeof": "1.2.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "15.0.0", "after": "0.8.2", "mocha": "7.1.1", "eslint": "6.8.0", "supertest": "4.0.2", "safe-buffer": "5.2.0", "finalhandler": "1.1.2", "eslint-plugin-node": "11.0.0", "eslint-plugin-import": "2.20.1", "eslint-plugin-promise": "4.2.1", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "1.0.2", "eslint-plugin-standard": "4.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/router_2.0.0-beta.1_1585541353172_0.8292698290458826", "host": "s3://npm-registry-packages"}}, "1.3.6": {"name": "router", "version": "1.3.6", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "router@1.3.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/router#readme", "bugs": {"url": "https://github.com/pillarjs/router/issues"}, "dist": {"shasum": "35302dc8d69f75b00797d618d92d6c9fb35604bf", "tarball": "https://registry.npmjs.org/router/-/router-1.3.6.tgz", "fileCount": 7, "integrity": "sha512-gsjhou+LFApzkIP8VDrouG6Z4pqkeF11n3o5orlwPPvPTl0x7c+dbF71itKOhDoFHygmc3N3uqm55Uq/gIDUwg==", "signatures": [{"sig": "MEYCIQD6GYsnpdAPmIGfBpGoHQ+ad4X0uxO2w6EWXN9civYpmAIhAPTrQUSDgv2w6ziDc46xYKm13RrI7+J+tIw6NtqkJuoh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41082, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhkveICRA9TVsSAnZWagAAPX8QAJKtNNqK5NkDqVTfPm+5\nh/obiGpTUyHYMeB8c6o0KhCy6JTvyJsPX2Tv+tPVjHtEYgp4tVhAbjuBWBbk\n8LW+f3GqLk7vw8i4PWV+hEHLktFanrjSIeWg6qOg4pfqNaLSIXLHZ2yRq/Ie\nxMHdKceBSxqiLj0FPgaYV3dg2+5o053ZmBDRx4NxZXUo+VJambySrBKKURSa\na/wss5eAGkufna1GHvW5LLMhCGgPs7s4lzfddu2sX6mC58OfBo4zoWohrNOg\nATGGYZcEgtEFMvlAmHNVJ9k3Qb/7CRsZhWzPUD8LHpsTj1f1aI4w9PEH399A\nS3XnjPRSQzWZSOwP8KSNJdqQhoS94FO77Sty1cg2FsjmrYFW8f4wp04g6sQO\nOv1BRwl74EhrW5ej+WACENVuRjOqM7DUVkwKh45K2nliUNJgwKnZ8tpwsG4R\nMW21Ks7JKL+oKny7dUSvAqRwE+4eSG0Iv4pmNBzZTSvyYEHGgl80I5QTpb5S\n4AMwfNdy0FbEuCBRiL7/6x+5W2nF+YMis1fyuEvHyYx6R2VIbkX9wqhjNF+U\ngBCNdbYCFBbLu3rgNncSkUKOxivHb1UiptYSpZYQzUnqlKIJXN+jo6pZrfFj\nU3CeiP7SDrbGWgYr4eBseErlE176p/jR5AZgMIiSdfKMz3PGNpL91DR1y9ny\nL07K\r\n=0Uyr\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}, "gitHead": "3b8351859859c259aa0c8b1174693b82aab04d35", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/router.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Simple middleware-style router", "directories": {}, "_nodeVersion": "16.7.0", "dependencies": {"debug": "2.6.9", "methods": "~1.1.2", "parseurl": "~1.3.3", "utils-merge": "1.0.1", "array-flatten": "3.0.0", "path-to-regexp": "0.1.7", "setprototypeof": "1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "after": "0.8.2", "mocha": "9.1.3", "eslint": "7.32.0", "supertest": "6.1.6", "safe-buffer": "5.2.1", "finalhandler": "1.1.2", "eslint-plugin-markdown": "2.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/router_1.3.6_1637021576412_0.9385150989825257", "host": "s3://npm-registry-packages"}}, "1.3.7": {"name": "router", "version": "1.3.7", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "router@1.3.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/router#readme", "bugs": {"url": "https://github.com/pillarjs/router/issues"}, "dist": {"shasum": "7d68cef5558febfd3438a23de07a0f8b92b873f1", "tarball": "https://registry.npmjs.org/router/-/router-1.3.7.tgz", "fileCount": 8, "integrity": "sha512-bYnD9Vv2287+g3AIll2kHITLtHV5+fldq6hVzaul9RbdGme77mvBY/1cO+ahsgstA2RI6DSg/j4W1TYHm4Lz4g==", "signatures": [{"sig": "MEQCICCMCcifYxZxrWviqN1dQPQBWb31Q0OpaSXkt5xRIsswAiA3DBy6nfuUw7WM93f90kw7VPWq1sFXJepHmsaDbiA2JA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42394, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiasZOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrZQw/+NRQYbXblRZBHJiv57DDXJXDCrFVaJrenLMvwvG3OuFkcN6u+\r\nTrDc22bfgTYstRLFJWFZpdOuP2HW1rQIfdgyqpMqbyTgFTbop/4Y4Djex2WT\r\nqQqzaI51j5Q3zqZb5eFHo6vsCK4t4lD/v7rKKFNeDl2fls85tnyrllC7aXnZ\r\n5D7qC5ojhqpV1gKQuvE+hC02Wugr/M+trsxp1t163nchdF0li8gSzXaIYUj7\r\n1XDk67vtEkIhJ2AUrWyGGggj6ByfcuHOfVIEQlc2OJT7dYxs9VyJ+3u7Gcfc\r\nDUXIcBojUdWblppGiDXW49b0Bipc1ZXl0NNTJ6KnZHzg1+dacYzQD7CrIPT8\r\nsAGsQqZgfHoLq8/p+i++SERjzjKCI3F2OwcoCxksD9mXofrJV+890YYP01ff\r\nPZM8HKDrn10lWxdmj0m0GLd6owqdf8s2M2RX15AmYp9jm0v8TU2JqNF9Nfcl\r\n4Br7wZoXxafgE04AHcgX5wFFIlQrfQ/WXozAA6kgOOl2f9SuuNtUMkRz4YLO\r\n2SO8d7N5Wqyk5NusaupLnmH8Wl67+dg6BSsY0wJitCM15f/6U6ck4JUkVetI\r\ncAYrpL7t9894uywpnJUdwhrRwgapOggyDWWiGAMXB6nI3IKNsRemopVPB3Q8\r\nblskTrZ9P6wIZvsraQnCZyEDXsI/BQeHBbc=\r\n=HdIr\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}, "gitHead": "41f61cbe76ba4a5f4815f78db44991ea84781274", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/router.git", "type": "git"}, "_npmVersion": "8.3.1", "description": "Simple middleware-style router", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"debug": "2.6.9", "methods": "~1.1.2", "parseurl": "~1.3.3", "utils-merge": "1.0.1", "array-flatten": "3.0.0", "path-to-regexp": "0.1.7", "setprototypeof": "1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "after": "0.8.2", "mocha": "9.2.2", "eslint": "7.32.0", "supertest": "6.2.3", "safe-buffer": "5.2.1", "finalhandler": "1.2.0", "eslint-plugin-markdown": "2.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/router_1.3.7_1651164749902_0.08260849886757815", "host": "s3://npm-registry-packages"}}, "1.3.8": {"name": "router", "version": "1.3.8", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "router@1.3.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/router#readme", "bugs": {"url": "https://github.com/pillarjs/router/issues"}, "dist": {"shasum": "1509614ae1fbc67139a728481c54b057ecfb04bf", "tarball": "https://registry.npmjs.org/router/-/router-1.3.8.tgz", "fileCount": 8, "integrity": "sha512-461UFH44NtSfIlS83PUg2N7OZo86BC/kB3dY77gJdsODsBhhw7+2uE0tzTINxrY9CahCUVk1VhpWCA5i1yoIEg==", "signatures": [{"sig": "MEUCIQDLRqBQjyImxESVmecKj6VSM95Dm/C5jxgLcOWVZHI3zAIgYN9Js6gI3sMjM9gUTSk1zqLhVxHYFaDaupDkFN1ytmE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42576, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+Oj+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr5rA/8CVSD54Dcv4fk5WJX0suAZv6MkIpp+tNHePbtRRrNhOWapbnC\r\nTpgvjCJhO9dk/btSFb8EC3lp/xA4i+FupOFBWyizUZ0dEe8lgm50iHsWaxZX\r\ntOBT9domcXjESnyQ4KRsqWP7HjdP8nXcYaP2twtBnllXvVPvDVl2eZsMtWCC\r\ncXFRO5OF2RZi4WmiCPWURQYyhjGjv7RWQsPzMcDziQ00hhg3RpinyN40xFAH\r\nNxzX2BKblG4bakqBVHiT2HxL7vJih8309AMMUh8f6k4J7F9evXu4iN2RDp4n\r\nhPeygIjmIgTKBnLjwSHOK6ZppLaDaL5+TkHpgqmk+wkueqE7G2EzSdQ1NxI3\r\nte6byGzEDxwuqiUu2rjvAUSiN3bEDtGi48/3JpoQvTSRpBEZ8ftcIPSXiiXC\r\nVFSWXPKkX3Z39r0gXsIwbU7TITMSWITsszLmx8i7KDpqNuLuuNmJXgh2WQA3\r\nyTSGXkxAZd14zLHTNVdowt5Xtd7RSB3v/jei8+xTuwFwBW/d+hB0zblXyGLt\r\ntpl256FQHIoZ7U7c/OXljtQmAxaWfak3NPIwrbT2z8K1MXWfIHINPmhIFI3G\r\nnEwnSvVyu0jEdrPE4FduFn/R+r2RAv72am2bBTzz4VuUPVKn1pZzZ7qjNs3V\r\nBFrGkXjOsAUQSxKbgz5PfW3jVjwgEG0tQgQ=\r\n=hwTT\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}, "gitHead": "86a5f765838d863419c75e80a4f1dda9759657b7", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/router.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Simple middleware-style router", "directories": {}, "_nodeVersion": "18.13.0", "dependencies": {"debug": "2.6.9", "methods": "~1.1.2", "parseurl": "~1.3.3", "utils-merge": "1.0.1", "array-flatten": "3.0.0", "path-to-regexp": "0.1.7", "setprototypeof": "1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "after": "0.8.2", "mocha": "10.2.0", "eslint": "8.34.0", "supertest": "6.3.3", "safe-buffer": "5.2.1", "finalhandler": "1.2.0", "eslint-plugin-markdown": "3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/router_1.3.8_1677256958476_0.15605555404618832", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.2": {"name": "router", "version": "2.0.0-beta.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "router@2.0.0-beta.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/router#readme", "bugs": {"url": "https://github.com/pillarjs/router/issues"}, "dist": {"shasum": "c8da30dea93ecb628d28116b63ad83f5021b07af", "tarball": "https://registry.npmjs.org/router/-/router-2.0.0-beta.2.tgz", "fileCount": 8, "integrity": "sha512-ascmzrv4IAB64SpWzFwYOA+jz6PaUbrzHLPsQrPjQ3uQTL2qlhwY9S2sRvvBMgUISQptQG457jcWWcWqtwrbag==", "signatures": [{"sig": "MEYCIQDUNisKjg+JyycL5t09h53voKY18dn0kJOnNbWQfOSICwIhAI0JJW8osen4Lgxxfw9UWnLjOe2wjTe3/tfGFIBgx8Ii", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45395}, "engines": {"node": ">= 0.10"}, "gitHead": "e14430a106a41af06af83808abf34ade49631347", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/router.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Simple middleware-style router", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"methods": "~1.1.2", "parseurl": "~1.3.3", "is-promise": "4.0.0", "utils-merge": "1.0.1", "array-flatten": "3.0.0", "path-to-regexp": "3.2.0", "setprototypeof": "1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "after": "0.8.2", "mocha": "10.2.0", "eslint": "8.34.0", "supertest": "6.3.3", "safe-buffer": "5.2.1", "finalhandler": "1.2.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.26.0", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "3.0.0", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/router_2.0.0-beta.2_1710986922776_0.12187152196770912", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "router", "version": "2.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "router@2.0.0", "maintainers": [{"name": "ulisesgascon", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/router#readme", "bugs": {"url": "https://github.com/pillarjs/router/issues"}, "dist": {"shasum": "8692720b95de83876870d7bc638dd3c7e1ae8a27", "tarball": "https://registry.npmjs.org/router/-/router-2.0.0.tgz", "fileCount": 8, "integrity": "sha512-dIM5zVoG8xhC6rnSN8uoAgFARwTE7BQs8YwHEvK0VCmfxQXMaOuA1uiR1IPwsW7JyK5iTt7Od/TC9StasS2NPQ==", "signatures": [{"sig": "MEUCIHZezsl+yqHP/HwqjEelVWZJWu4MwlGPDNnrpHWHq1e8AiEAgLZ/8VnwOpJlC2G29EQAYeLA4T6wt53DIcmY1k9IwCo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46769}, "engines": {"node": ">= 0.10"}, "gitHead": "2e7fb67ad1b0c1cd2d9eb35c2244439c5c57891a", "scripts": {"lint": "standard", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=text npm test", "test:debug": "mocha --reporter spec --bail --check-leaks test/ --inspect --inspect-brk"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/router.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Simple middleware-style router", "directories": {}, "_nodeVersion": "22.2.0", "dependencies": {"methods": "~1.1.2", "parseurl": "~1.3.3", "is-promise": "4.0.0", "utils-merge": "1.0.1", "array-flatten": "3.0.0", "path-to-regexp": "^8.0.0", "setprototypeof": "1.2.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "15.1.0", "after": "0.8.2", "mocha": "10.2.0", "standard": "^17.1.0", "supertest": "6.3.3", "run-series": "^1.1.9", "safe-buffer": "5.2.1", "finalhandler": "1.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/router_2.0.0_1725921898357_0.5125115733991521", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "router", "version": "2.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "router@2.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/router#readme", "bugs": {"url": "https://github.com/pillarjs/router/issues"}, "dist": {"shasum": "f256ca2365afb4d386ba4f7a9ee0aa0827c962fa", "tarball": "https://registry.npmjs.org/router/-/router-2.1.0.tgz", "fileCount": 8, "integrity": "sha512-/m/NSLxeYEgWNtyC+WtNHCF7jbGxOibVWKnn+1Psff4dJGOfoXP+MuC/f2CwSmyiHdOIzYnYFp4W6GxWfekaLA==", "signatures": [{"sig": "MEUCIAekpFsXVMWYYfZVL8KU/91mXaP/pgAMLNZi+kYKpHw+AiEAxTbjJ0CwKm17NajdR/N8PybaUOmrYBPP7fHOeQXqGi8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 47097}, "engines": {"node": ">= 18"}, "gitHead": "4f1b458dae8a2f0486b8adbcfc971189e4a441dc", "scripts": {"lint": "standard", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=text npm test", "test:debug": "mocha --reporter spec --bail --check-leaks test/ --inspect --inspect-brk"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/router.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Simple middleware-style router", "directories": {}, "_nodeVersion": "23.5.0", "dependencies": {"parseurl": "^1.3.3", "is-promise": "^4.0.0", "path-to-regexp": "^8.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "15.1.0", "after": "0.8.2", "mocha": "10.2.0", "standard": "^17.1.0", "supertest": "6.3.3", "run-series": "^1.1.9", "safe-buffer": "5.2.1", "finalhandler": "1.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/router_2.1.0_1739217653600_0.4804023893598961", "host": "s3://npm-registry-packages-npm-production"}}, "2.2.0": {"name": "router", "description": "Simple middleware-style router", "version": "2.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/pillarjs/router.git"}, "dependencies": {"debug": "^4.4.0", "depd": "^2.0.0", "is-promise": "^4.0.0", "parseurl": "^1.3.3", "path-to-regexp": "^8.0.0"}, "devDependencies": {"finalhandler": "^2.1.0", "mocha": "10.2.0", "nyc": "15.1.0", "run-series": "^1.1.9", "standard": "^17.1.0", "supertest": "6.3.3"}, "engines": {"node": ">= 18"}, "scripts": {"lint": "standard", "test": "mocha --reporter spec --bail --check-leaks test/", "test:debug": "mocha --reporter spec --bail --check-leaks test/ --inspect --inspect-brk", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md"}, "_id": "router@2.2.0", "gitHead": "e6d6b609fc355e558174ccd5b1db646f739fe88c", "bugs": {"url": "https://github.com/pillarjs/router/issues"}, "homepage": "https://github.com/pillarjs/router#readme", "_nodeVersion": "22.10.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-nLTrUKm2UyiL7rlhapu/Zl45FwNgkZGaCpZbIHajDYgwlJCOzLSk+cIPAnsEqV955GjILJnKbdQC1nVPz+gAYQ==", "shasum": "019be620b711c87641167cc79b99090f00b146ef", "tarball": "https://registry.npmjs.org/router/-/router-2.2.0.tgz", "fileCount": 7, "unpackedSize": 46851, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIG1z9CfKLZPzyVBKnAK3covJDJ0/O73YnA4SwsesneLYAiEA22tGWJxiFBxRTEU/ot8+oZqloPqUb+g9IoNvUZR6eGM="}]}, "_npmUser": {"name": "ulisesgascon", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, {"name": "ulisesgascon", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/router_2.2.0_1743035898326_0.3199944387338467"}, "_hasShrinkwrap": false}}, "time": {"created": "2011-06-23T11:01:08.263Z", "modified": "2025-03-27T00:38:18.812Z", "0.2.1": "2011-06-23T11:01:08.696Z", "0.2.2": "2011-06-23T12:11:21.379Z", "0.2.3": "2011-07-03T14:51:16.521Z", "0.2.4": "2011-07-14T08:20:04.205Z", "0.2.5": "2011-07-14T08:25:58.919Z", "0.2.6": "2011-07-14T16:20:53.355Z", "0.2.8": "2011-07-14T17:31:37.258Z", "0.3.0": "2011-07-14T18:15:34.088Z", "0.3.1": "2011-07-19T12:26:28.343Z", "0.3.2": "2011-07-25T11:52:09.864Z", "0.4.0": "2011-07-26T13:01:13.237Z", "0.4.1": "2011-08-01T12:44:59.626Z", "0.4.2": "2011-10-22T15:11:16.220Z", "0.4.3": "2011-11-23T15:16:19.689Z", "0.4.4": "2011-11-23T15:25:49.827Z", "0.4.5": "2012-01-18T12:16:52.006Z", "0.4.6": "2012-01-30T22:54:33.080Z", "0.4.8": "2012-02-22T13:07:08.409Z", "0.4.9": "2012-02-23T23:48:03.429Z", "0.4.10": "2012-02-24T00:01:19.436Z", "0.4.11": "2012-03-10T21:36:53.275Z", "0.5.0": "2012-03-17T21:55:08.261Z", "0.5.1": "2012-03-19T12:02:00.524Z", "0.5.2": "2012-03-19T21:39:00.041Z", "0.5.3": "2012-03-20T22:32:52.647Z", "0.5.4": "2012-03-21T15:07:44.820Z", "0.5.5": "2012-03-21T15:29:32.570Z", "0.5.6": "2012-03-21T20:10:49.756Z", "0.5.7": "2012-03-21T23:07:31.159Z", "0.5.9": "2012-03-22T01:03:27.218Z", "0.5.10": "2012-03-22T10:27:44.246Z", "0.5.11": "2012-03-22T10:43:31.927Z", "0.5.13": "2012-03-22T11:30:16.463Z", "0.5.14": "2012-03-25T12:27:27.663Z", "0.5.15": "2012-03-25T16:19:24.298Z", "0.5.16": "2012-03-25T16:26:12.081Z", "0.5.17": "2012-03-27T12:02:14.603Z", "0.5.18": "2012-04-16T19:36:12.546Z", "0.5.19": "2012-05-05T19:11:23.090Z", "0.6.0": "2012-05-26T13:17:23.206Z", "0.6.1": "2012-08-29T12:25:07.652Z", "0.6.2": "2012-08-29T12:33:03.897Z", "1.0.0-beta.1": "2014-11-17T06:05:31.300Z", "1.0.0-beta.2": "2014-11-20T05:36:36.696Z", "1.0.0-beta.3": "2015-01-12T06:37:00.944Z", "1.0.0": "2015-01-14T07:03:06.931Z", "1.1.0": "2015-04-23T02:58:58.048Z", "1.1.1": "2015-05-26T05:15:38.985Z", "1.1.2": "2015-07-07T03:29:08.412Z", "1.1.3": "2015-08-03T05:22:24.209Z", "1.1.4": "2016-01-22T04:03:10.750Z", "1.1.5": "2017-01-29T01:33:39.920Z", "1.2.0": "2017-02-18T06:42:40.452Z", "1.3.0": "2017-02-26T07:00:30.229Z", "1.3.1": "2017-05-20T04:38:11.304Z", "1.3.2": "2017-09-25T01:33:18.041Z", "1.3.3": "2018-07-06T16:01:51.389Z", "2.0.0-alpha.1": "2018-07-27T17:08:28.426Z", "1.3.4": "2020-01-25T02:19:30.302Z", "1.3.5": "2020-03-25T03:22:05.407Z", "2.0.0-beta.1": "2020-03-30T04:09:13.415Z", "1.3.6": "2021-11-16T00:12:56.606Z", "1.3.7": "2022-04-28T16:52:30.072Z", "1.3.8": "2023-02-24T16:42:38.637Z", "2.0.0-beta.2": "2024-03-21T02:08:42.946Z", "2.0.0": "2024-09-09T22:44:58.529Z", "2.1.0": "2025-02-10T20:00:53.824Z", "2.2.0": "2025-03-27T00:38:18.615Z"}, "bugs": {"url": "https://github.com/pillarjs/router/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/pillarjs/router#readme", "repository": {"type": "git", "url": "git+https://github.com/pillarjs/router.git"}, "description": "Simple middleware-style router", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "maintainers": [{"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, {"name": "ulisesgascon", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# router\n\n[![NPM Version][npm-image]][npm-url]\n[![NPM Downloads][downloads-image]][downloads-url]\n[![Node.js Version][node-version-image]][node-version-url]\n[![Build Status][ci-image]][ci-url]\n[![Test Coverage][coveralls-image]][coveralls-url]\n\nSimple middleware-style router\n\n## Installation\n\nThis is a [Node.js](https://nodejs.org/en/) module available through the\n[npm registry](https://www.npmjs.com/). Installation is done using the\n[`npm install` command](https://docs.npmjs.com/getting-started/installing-npm-packages-locally):\n\n```bash\n$ npm install router\n```\n\n## API\n\n```js\nvar finalhandler = require('finalhandler')\nvar http = require('http')\nvar Router = require('router')\n\nvar router = Router()\nrouter.get('/', function (req, res) {\n  res.setHeader('Content-Type', 'text/plain; charset=utf-8')\n  res.end('Hello World!')\n})\n\nvar server = http.createServer(function (req, res) {\n  router(req, res, finalhandler(req, res))\n})\n\nserver.listen(3000)\n```\n\nThis module is currently an extracted version from the Express project,\nbut with the main change being it can be used with a plain `http.createServer`\nobject or other web frameworks by removing Express-specific API calls.\n\n## Router(options)\n\nOptions\n\n- `strict`        - When `false` trailing slashes are optional (default: `false`)\n- `caseSensitive` - When `true` the routing will be case sensitive. (default: `false`)\n- `mergeParams`   - When `true` any `req.params` passed to the router will be\n  merged into the router's `req.params`. (default: `false`) ([example](#example-using-mergeparams))\n\nReturns a function with the signature `router(req, res, callback)` where\n`callback([err])` must be provided to handle errors and fall-through from\nnot handling requests.\n\n### router.use([path], ...middleware)\n\nUse the given [middleware function](#middleware) for all http methods on the\ngiven `path`, defaulting to the root path.\n\n`router` does not automatically see `use` as a handler. As such, it will not\nconsider it one for handling `OPTIONS` requests.\n\n* Note: If a `path` is specified, that `path` is stripped from the start of\n  `req.url`.\n\n<!-- eslint-disable no-undef -->\n\n```js\nrouter.use(function (req, res, next) {\n  // do your things\n\n  // continue to the next middleware\n  // the request will stall if this is not called\n  next()\n\n  // note: you should NOT call `next` if you have begun writing to the response\n})\n```\n\n[Middleware](#middleware) can themselves use `next('router')` at any time to\nexit the current router instance completely, invoking the top-level callback.\n\n### router\\[method](path, ...[middleware], handler)\n\nThe [http methods](https://github.com/jshttp/methods/blob/master/index.js) provide\nthe routing functionality in `router`.\n\nMethod middleware and handlers follow usual [middleware](#middleware) behavior,\nexcept they will only be called when the method and path match the request.\n\n<!-- eslint-disable no-undef -->\n\n```js\n// handle a `GET` request\nrouter.get('/', function (req, res) {\n  res.setHeader('Content-Type', 'text/plain; charset=utf-8')\n  res.end('Hello World!')\n})\n```\n\n[Middleware](#middleware) given before the handler have one additional trick,\nthey may invoke `next('route')`. Calling `next('route')` bypasses the remaining\nmiddleware and the handler mounted for this route, passing the request to the\nnext route suitable for handling this request.\n\nRoute handlers and middleware can themselves use `next('router')` at any time\nto exit the current router instance completely, invoking the top-level callback.\n\n### router.param(name, param_middleware)\n\nMaps the specified path parameter `name` to a specialized param-capturing middleware.\n\nThis function positions the middleware in the same stack as `.use`.\n\nThe function can optionally return a `Promise` object. If a `Promise` object\nis returned from the function, the router will attach an `onRejected` callback\nusing `.then`. If the promise is rejected, `next` will be called with the\nrejected value, or an error if the value is falsy.\n\nParameter mapping is used to provide pre-conditions to routes\nwhich use normalized placeholders. For example a _:user_id_ parameter\ncould automatically load a user's information from the database without\nany additional code:\n\n<!-- eslint-disable no-undef -->\n\n```js\nrouter.param('user_id', function (req, res, next, id) {\n  User.find(id, function (err, user) {\n    if (err) {\n      return next(err)\n    } else if (!user) {\n      return next(new Error('failed to load user'))\n    }\n    req.user = user\n\n    // continue processing the request\n    next()\n  })\n})\n```\n\n### router.route(path)\n\nCreates an instance of a single `Route` for the given `path`.\n(See `Router.Route` below)\n\nRoutes can be used to handle http `methods` with their own, optional middleware.\n\nUsing `router.route(path)` is a recommended approach to avoiding duplicate\nroute naming and thus typo errors.\n\n<!-- eslint-disable no-undef, no-unused-vars -->\n\n```js\nvar api = router.route('/api/')\n```\n\n## Router.Route(path)\n\nRepresents a single route as an instance that can be used to handle http\n`methods` with it's own, optional middleware.\n\n### route\\[method](handler)\n\nThese are functions which you can directly call on a route to register a new\n`handler` for the `method` on the route.\n\n<!-- eslint-disable no-undef -->\n\n```js\n// handle a `GET` request\nvar status = router.route('/status')\n\nstatus.get(function (req, res) {\n  res.setHeader('Content-Type', 'text/plain; charset=utf-8')\n  res.end('All Systems Green!')\n})\n```\n\n### route.all(handler)\n\nAdds a handler for all HTTP methods to this route.\n\nThe handler can behave like middleware and call `next` to continue processing\nrather than responding.\n\n<!-- eslint-disable no-undef -->\n\n```js\nrouter.route('/')\n  .all(function (req, res, next) {\n    next()\n  })\n  .all(checkSomething)\n  .get(function (req, res) {\n    res.setHeader('Content-Type', 'text/plain; charset=utf-8')\n    res.end('Hello World!')\n  })\n```\n\n## Middleware\n\nMiddleware (and method handlers) are functions that follow specific function\nparameters and have defined behavior when used with `router`. The most common\nformat is with three parameters - \"req\", \"res\" and \"next\".\n\n- `req`  - This is a [HTTP incoming message](https://nodejs.org/api/http.html#http_http_incomingmessage) instance.\n- `res`  - This is a [HTTP server response](https://nodejs.org/api/http.html#http_class_http_serverresponse) instance.\n- `next` - Calling this function that tells `router` to proceed to the next matching middleware or method handler. It accepts an error as the first argument.\n\nThe function can optionally return a `Promise` object. If a `Promise` object\nis returned from the function, the router will attach an `onRejected` callback\nusing `.then`. If the promise is rejected, `next` will be called with the\nrejected value, or an error if the value is falsy.\n\nMiddleware and method handlers can also be defined with four arguments. When\nthe function has four parameters defined, the first argument is an error and\nsubsequent arguments remain, becoming - \"err\", \"req\", \"res\", \"next\". These\nfunctions are \"error handling middleware\", and can be used for handling\nerrors that occurred in previous handlers (E.g. from calling `next(err)`).\nThis is most used when you want to define arbitrary rendering of errors.\n\n<!-- eslint-disable no-undef -->\n\n```js\nrouter.get('/error_route', function (req, res, next) {\n  return next(new Error('Bad Request'))\n})\n\nrouter.use(function (err, req, res, next) {\n  res.end(err.message) //= > \"Bad Request\"\n})\n```\n\nError handling middleware will **only** be invoked when an error was given. As\nlong as the error is in the pipeline, normal middleware and handlers will be\nbypassed - only error handling middleware will be invoked with an error.\n\n## Examples\n\n```js\n// import our modules\nvar http = require('http')\nvar Router = require('router')\nvar finalhandler = require('finalhandler')\nvar compression = require('compression')\nvar bodyParser = require('body-parser')\n\n// store our message to display\nvar message = 'Hello World!'\n\n// initialize the router & server and add a final callback.\nvar router = Router()\nvar server = http.createServer(function onRequest (req, res) {\n  router(req, res, finalhandler(req, res))\n})\n\n// use some middleware and compress all outgoing responses\nrouter.use(compression())\n\n// handle `GET` requests to `/message`\nrouter.get('/message', function (req, res) {\n  res.statusCode = 200\n  res.setHeader('Content-Type', 'text/plain; charset=utf-8')\n  res.end(message + '\\n')\n})\n\n// create and mount a new router for our API\nvar api = Router()\nrouter.use('/api/', api)\n\n// add a body parsing middleware to our API\napi.use(bodyParser.json())\n\n// handle `PATCH` requests to `/api/set-message`\napi.patch('/set-message', function (req, res) {\n  if (req.body.value) {\n    message = req.body.value\n\n    res.statusCode = 200\n    res.setHeader('Content-Type', 'text/plain; charset=utf-8')\n    res.end(message + '\\n')\n  } else {\n    res.statusCode = 400\n    res.setHeader('Content-Type', 'text/plain; charset=utf-8')\n    res.end('Invalid API Syntax\\n')\n  }\n})\n\n// make our http server listen to connections\nserver.listen(8080)\n```\n\nYou can get the message by running this command in your terminal,\n or navigating to `127.0.0.1:8080` in a web browser.\n```bash\ncurl http://127.0.0.1:8080\n```\n\nYou can set the message by sending it a `PATCH` request via this command:\n```bash\ncurl http://127.0.0.1:8080/api/set-message -X PATCH -H \"Content-Type: application/json\" -d '{\"value\":\"Cats!\"}'\n```\n\n### Example using mergeParams\n\n```js\nvar http = require('http')\nvar Router = require('router')\nvar finalhandler = require('finalhandler')\n\n// this example is about the mergeParams option\nvar opts = { mergeParams: true }\n\n// make a router with out special options\nvar router = Router(opts)\nvar server = http.createServer(function onRequest (req, res) {\n  // set something to be passed into the router\n  req.params = { type: 'kitten' }\n\n  router(req, res, finalhandler(req, res))\n})\n\nrouter.get('/', function (req, res) {\n  res.statusCode = 200\n  res.setHeader('Content-Type', 'text/plain; charset=utf-8')\n\n  // with respond with the the params that were passed in\n  res.end(req.params.type + '\\n')\n})\n\n// make another router with our options\nvar handler = Router(opts)\n\n// mount our new router to a route that accepts a param\nrouter.use('/:path', handler)\n\nhandler.get('/', function (req, res) {\n  res.statusCode = 200\n  res.setHeader('Content-Type', 'text/plain; charset=utf-8')\n\n  // will respond with the param of the router's parent route\n  res.end(req.params.path + '\\n')\n})\n\n// make our http server listen to connections\nserver.listen(8080)\n```\n\nNow you can get the type, or what path you are requesting:\n```bash\ncurl http://127.0.0.1:8080\n> kitten\ncurl http://127.0.0.1:8080/such_path\n> such_path\n```\n\n### Example of advanced `.route()` usage\n\nThis example shows how to implement routes where there is a custom\nhandler to execute when the path matched, but no methods matched.\nWithout any special handling, this would be treated as just a\ngeneric non-match by `router` (which typically results in a 404),\nbut with a custom handler, a `405 Method Not Allowed` can be sent.\n\n```js\nvar http = require('http')\nvar finalhandler = require('finalhandler')\nvar Router = require('router')\n\n// create the router and server\nvar router = new Router()\nvar server = http.createServer(function onRequest (req, res) {\n  router(req, res, finalhandler(req, res))\n})\n\n// register a route and add all methods\nrouter.route('/pet/:id')\n  .get(function (req, res) {\n    // this is GET /pet/:id\n    res.setHeader('Content-Type', 'application/json')\n    res.end(JSON.stringify({ name: 'tobi' }))\n  })\n  .delete(function (req, res) {\n    // this is DELETE /pet/:id\n    res.end()\n  })\n  .all(function (req, res) {\n    // this is called for all other methods not\n    // defined above for /pet/:id\n    res.statusCode = 405\n    res.end()\n  })\n\n// make our http server listen to connections\nserver.listen(8080)\n```\n\n## License\n\n[MIT](LICENSE)\n\n[ci-image]: https://badgen.net/github/checks/pillarjs/router/master?label=ci\n[ci-url]: https://github.com/pillarjs/router/actions/workflows/ci.yml\n[npm-image]: https://img.shields.io/npm/v/router.svg\n[npm-url]: https://npmjs.org/package/router\n[node-version-image]: https://img.shields.io/node/v/router.svg\n[node-version-url]: http://nodejs.org/download/\n[coveralls-image]: https://img.shields.io/coveralls/pillarjs/router/master.svg\n[coveralls-url]: https://coveralls.io/r/pillarjs/router?branch=master\n[downloads-image]: https://img.shields.io/npm/dm/router.svg\n[downloads-url]: https://npmjs.org/package/router\n", "readmeFilename": "README.md", "users": {"vbv": true, "cdll": true, "n370": true, "zeke": true, "akiva": true, "ashco": true, "eyson": true, "imd92": true, "quafoo": true, "temasm": true, "jupiter": true, "kaemiin": true, "o7s.will": true, "aredridel": true, "jetthiago": true, "papasavva": true, "sergiodxa": true, "dabdullahy": true, "giussa_dan": true, "leizongmin": true, "rocket0191": true, "andreaslacza": true, "davidbwaters": true, "zerosandones": true, "josephdavisco": true, "roboterhund87": true, "scottfreecode": true, "bradleybossard": true, "hyokosdeveloper": true}}