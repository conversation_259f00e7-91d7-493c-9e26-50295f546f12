{"_id": "http-cache-semantics", "_rev": "31-d12695cb1fc088aaee00c82f2544097b", "name": "http-cache-semantics", "description": "Parses Cache-Control and other headers. Helps building correct HTTP caches and proxies", "dist-tags": {"latest": "4.1.1"}, "versions": {"1.0.0": {"name": "http-cache-semantics", "version": "1.0.0", "description": "Parses Cache-Control headers and friends", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/pornel/http-cache-semantics.git"}, "scripts": {"test": "mocha"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://kornel.ski/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"mocha": "^2.4.5"}, "gitHead": "c07f89dfffbe3f452baa12c30ba84ad4bac7a176", "bugs": {"url": "https://github.com/pornel/http-cache-semantics/issues"}, "homepage": "https://github.com/pornel/http-cache-semantics#readme", "_id": "http-cache-semantics@1.0.0", "_shasum": "c9f9238f3e4aec9fc4b85140dd1b86975069e5d6", "_from": ".", "_npmVersion": "3.9.3", "_nodeVersion": "6.2.0", "_npmUser": {"name": "kornel", "email": "<EMAIL>"}, "dist": {"shasum": "c9f9238f3e4aec9fc4b85140dd1b86975069e5d6", "tarball": "https://registry.npmjs.org/http-cache-semantics/-/http-cache-semantics-1.0.0.tgz", "integrity": "sha512-YG5oFYOJ7BozRq1HqRgUt37qQnnD9sUMmyv7+iTizfQo8lxnj4rgQc4nfSxMCeGScSaBVucwzIBjVGDB/6p2oQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID6VWwzFzG1uuSCJYEdt0W+Xlw/OhQpYmv28V15yto6mAiAPCfAmlH/o3AdWewjP7vXL2Xo3llwMwZB12N0de/ve9w=="}]}, "maintainers": [{"name": "kornel", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/http-cache-semantics-1.0.0.tgz_1464686300215_0.3538409436587244"}, "directories": {}}, "2.0.0": {"name": "http-cache-semantics", "version": "2.0.0", "description": "Parses Cache-Control headers and friends", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/pornel/http-cache-semantics.git"}, "scripts": {"test": "mocha"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://kornel.ski/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"mocha": "^2.4.5"}, "gitHead": "5f6f0837ec608622a875fb9167a32b3e2f7d9e53", "bugs": {"url": "https://github.com/pornel/http-cache-semantics/issues"}, "homepage": "https://github.com/pornel/http-cache-semantics#readme", "_id": "http-cache-semantics@2.0.0", "_shasum": "8852f4a5049d0e80e566bffb645f57d37900162e", "_from": ".", "_npmVersion": "3.9.3", "_nodeVersion": "6.2.0", "_npmUser": {"name": "kornel", "email": "<EMAIL>"}, "dist": {"shasum": "8852f4a5049d0e80e566bffb645f57d37900162e", "tarball": "https://registry.npmjs.org/http-cache-semantics/-/http-cache-semantics-2.0.0.tgz", "integrity": "sha512-N7xTqNoLe5lLsqjmENuc8ij86GbLbTPFxe2Gvo4Q0tLG0avsBORgiPhdaIYd1wputaEhwYRUIAMemE0tlECrdA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICDz6zaMu2CbLrjXPvSjKnzhujkyiVVRhIEBvrOhvbAEAiEAi4U/gQISUdFN7mbihbkMZdPGn7UYq/LUvtq/jrgfQbU="}]}, "maintainers": [{"name": "kornel", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/http-cache-semantics-2.0.0.tgz_1464695015964_0.36678630392998457"}, "directories": {}}, "3.0.0": {"name": "http-cache-semantics", "version": "3.0.0", "description": "Parses Cache-Control headers and friends", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/pornel/http-cache-semantics.git"}, "scripts": {"test": "mocha"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://kornel.ski/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"mocha": "^2.4.5"}, "gitHead": "90ab2420ed6fbd739d5dbeef5de4fc698510cade", "bugs": {"url": "https://github.com/pornel/http-cache-semantics/issues"}, "homepage": "https://github.com/pornel/http-cache-semantics#readme", "_id": "http-cache-semantics@3.0.0", "_shasum": "aac9e9b024350356e4bafddb7df10423680fdedd", "_from": ".", "_npmVersion": "3.9.3", "_nodeVersion": "6.2.0", "_npmUser": {"name": "kornel", "email": "<EMAIL>"}, "dist": {"shasum": "aac9e9b024350356e4bafddb7df10423680fdedd", "tarball": "https://registry.npmjs.org/http-cache-semantics/-/http-cache-semantics-3.0.0.tgz", "integrity": "sha512-A0Kd6lnDsFOzxYa6V4Wu+1fECW/K+IYV/zivye7WYnWJQbfne7fkqQFiut33vHn8ZV5uC/UTdgUiPYxloaJJ4A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF7uxrZBcr/aVOdXlMCeTmBN+EZf1W/Pk31wuglnv49lAiEAmqfjBURcAZBB+ccAv1pUtLrosdF4Vltpf5RvCLAYzME="}]}, "maintainers": [{"name": "kornel", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/http-cache-semantics-3.0.0.tgz_1464711128962_0.14006854826584458"}, "directories": {}}, "3.1.0": {"name": "http-cache-semantics", "version": "3.1.0", "description": "Parses Cache-Control headers and friends", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/pornel/http-cache-semantics.git"}, "scripts": {"test": "mocha"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://kornel.ski/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"mocha": "^2.4.5"}, "gitHead": "a20a6d47d6229981d9ebede0a0b141d3b09ce5e3", "bugs": {"url": "https://github.com/pornel/http-cache-semantics/issues"}, "homepage": "https://github.com/pornel/http-cache-semantics#readme", "_id": "http-cache-semantics@3.1.0", "_shasum": "a6809724811910664c6666292159c81908bf3918", "_from": ".", "_npmVersion": "3.9.3", "_nodeVersion": "6.2.0", "_npmUser": {"name": "kornel", "email": "<EMAIL>"}, "dist": {"shasum": "a6809724811910664c6666292159c81908bf3918", "tarball": "https://registry.npmjs.org/http-cache-semantics/-/http-cache-semantics-3.1.0.tgz", "integrity": "sha512-Up3SbTBhVljDpJv/+NYv2uMuQyllzgISTdIwvGJEOlPGNdFi04yFnEJocoP899E1b5lcVyKmRNas4WkbBRB19A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDjSs/jgtq6zjxiZDnk59oSYP34IGqXPPjMxuckw0r4AgIgO2BkKRO/Rsep2jqOI6x00XDO+ZHPe2pDKU/6XsZHGQM="}]}, "maintainers": [{"name": "kornel", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/http-cache-semantics-3.1.0.tgz_1464737064618_0.49684207886457443"}, "directories": {}}, "3.2.0": {"name": "http-cache-semantics", "version": "3.2.0", "description": "Parses Cache-Control headers and friends", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/pornel/http-cache-semantics.git"}, "scripts": {"test": "mocha"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://kornel.ski/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"mocha": "^2.4.5"}, "gitHead": "172071cc0bbbece2dc7ac4556ee4242dfc8e2798", "bugs": {"url": "https://github.com/pornel/http-cache-semantics/issues"}, "homepage": "https://github.com/pornel/http-cache-semantics#readme", "_id": "http-cache-semantics@3.2.0", "_shasum": "ca6bdafedfe84b8ac7561d9a9a069415da69a6f1", "_from": ".", "_npmVersion": "3.9.3", "_nodeVersion": "6.2.0", "_npmUser": {"name": "kornel", "email": "<EMAIL>"}, "dist": {"shasum": "ca6bdafedfe84b8ac7561d9a9a069415da69a6f1", "tarball": "https://registry.npmjs.org/http-cache-semantics/-/http-cache-semantics-3.2.0.tgz", "integrity": "sha512-y3t6nEIt6GsJVZM4VEcAd1+Pz59YKayv3+do6Q0yo/4TNIW3gmi1H6/dHoYCHaHA0fpTBxSn6GqRnbvG1SHXNQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAmzRVyuzWKk/K4rZ7Unn8Ke0sBwo7SOjWNdiwOFmp7cAiEA+cCvODxfVI3yymefk/jCcXKOh+vWeqex6/lhH8NW5ck="}]}, "maintainers": [{"name": "kornel", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/http-cache-semantics-3.2.0.tgz_1465131131473_0.7571820064913481"}, "directories": {}}, "3.3.0": {"name": "http-cache-semantics", "version": "3.3.0", "description": "Parses Cache-Control headers and friends", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/pornel/http-cache-semantics.git"}, "scripts": {"test": "mocha"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://kornel.ski/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"mocha": "^2.4.5"}, "gitHead": "a8d20c96ef12bb2374bd0995752e3a72eff388f7", "bugs": {"url": "https://github.com/pornel/http-cache-semantics/issues"}, "homepage": "https://github.com/pornel/http-cache-semantics#readme", "_id": "http-cache-semantics@3.3.0", "_shasum": "a88e57092b8bf57830a3546a091499bcc30f39d9", "_from": ".", "_npmVersion": "4.0.5", "_nodeVersion": "7.2.1", "_npmUser": {"name": "kornel", "email": "<EMAIL>"}, "dist": {"shasum": "a88e57092b8bf57830a3546a091499bcc30f39d9", "tarball": "https://registry.npmjs.org/http-cache-semantics/-/http-cache-semantics-3.3.0.tgz", "integrity": "sha512-nqZFVId0D/bLYwdvQuQ16fu4UmLLFzPuhd/KWyT+1F6Y86c25wZXCv59DFllSDydgM9Jfq8Bhr99tkVPK5T4Bg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD1YL2oHsQoBncz8M8zASItlHY8sOr4PfG+V9EjEGtEWAIgOUymRr+0NcxCg/6XoTJ0PocqBXWanoBwm2+Bc+gpaeY="}]}, "maintainers": [{"name": "kornel", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/http-cache-semantics-3.3.0.tgz_1481158350707_0.8113677150104195"}, "directories": {}}, "3.3.1": {"name": "http-cache-semantics", "version": "3.3.1", "description": "Parses Cache-Control headers and friends", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/pornel/http-cache-semantics.git"}, "scripts": {"test": "mocha"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://kornel.ski/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"mocha": "^2.4.5"}, "gitHead": "6bf9c9fe908bdfbded1d750190cba051f8b9e580", "bugs": {"url": "https://github.com/pornel/http-cache-semantics/issues"}, "homepage": "https://github.com/pornel/http-cache-semantics#readme", "_id": "http-cache-semantics@3.3.1", "_shasum": "6d66768eefc6770e24cb67d623523037db40a7c9", "_from": ".", "_npmVersion": "4.0.5", "_nodeVersion": "7.2.1", "_npmUser": {"name": "kornel", "email": "<EMAIL>"}, "dist": {"shasum": "6d66768eefc6770e24cb67d623523037db40a7c9", "tarball": "https://registry.npmjs.org/http-cache-semantics/-/http-cache-semantics-3.3.1.tgz", "integrity": "sha512-TrE6EMPKguXDQxQMVnWvYVMOVx7KtODzye1DcH2zza3Y/iDY5YVlSusHhQAAprwd7bIAdoUF55w7ng6qRrTxzg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDTsJYB0Qaxkz1oLMTjV2m1VY3GgT8aYn4zCyrgL2xuggIhAJ4U5B1Q3JA/CpJGOJ1udiGdYlFlIvKBh0EEREeUezCj"}]}, "maintainers": [{"name": "kornel", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/http-cache-semantics-3.3.1.tgz_1481197440058_0.32048448571003973"}, "directories": {}}, "3.3.2": {"name": "http-cache-semantics", "version": "3.3.2", "description": "Parses Cache-Control headers and friends", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/pornel/http-cache-semantics.git"}, "scripts": {"test": "mocha"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://kornel.ski/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"mocha": "^2.4.5"}, "gitHead": "ed1b3f38f46a50b72f642ce2b63d0f4f0b54c431", "bugs": {"url": "https://github.com/pornel/http-cache-semantics/issues"}, "homepage": "https://github.com/pornel/http-cache-semantics#readme", "_id": "http-cache-semantics@3.3.2", "_shasum": "7e7ad369228813be47b1497434b360d76a48d3fe", "_from": ".", "_npmVersion": "4.0.5", "_nodeVersion": "7.2.1", "_npmUser": {"name": "kornel", "email": "<EMAIL>"}, "dist": {"shasum": "7e7ad369228813be47b1497434b360d76a48d3fe", "tarball": "https://registry.npmjs.org/http-cache-semantics/-/http-cache-semantics-3.3.2.tgz", "integrity": "sha512-yKnYBVRaslVRzq0pKPTmb5YtASw8wbmo/8E8LhoRky8OmvUtMqh78g0QwZ5vTaggkqkeU3mgDgPrXc/3NAgjmg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDyrt9x9wICIbT3UJQBktiZmWUKfV0BNSErjaVAnARkbAiEAh1nEQpBGYeur0GW8btcos8Ytl25tcN0WDysmQZGJlFg="}]}, "maintainers": [{"name": "kornel", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/http-cache-semantics-3.3.2.tgz_1481198496191_0.9340760365594178"}, "directories": {}}, "3.3.3": {"name": "http-cache-semantics", "version": "3.3.3", "description": "Parses Cache-Control headers and friends", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/pornel/http-cache-semantics.git"}, "scripts": {"test": "mocha"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://kornel.ski/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"mocha": "^2.4.5"}, "gitHead": "c9fe4786a6d497caf3dd55e0e4c49608e4bd263f", "bugs": {"url": "https://github.com/pornel/http-cache-semantics/issues"}, "homepage": "https://github.com/pornel/http-cache-semantics#readme", "_id": "http-cache-semantics@3.3.3", "_shasum": "f0b8a549e7259bd3990886b14bcff902b2f8c731", "_from": ".", "_npmVersion": "4.0.5", "_nodeVersion": "7.2.1", "_npmUser": {"name": "kornel", "email": "<EMAIL>"}, "dist": {"shasum": "f0b8a549e7259bd3990886b14bcff902b2f8c731", "tarball": "https://registry.npmjs.org/http-cache-semantics/-/http-cache-semantics-3.3.3.tgz", "integrity": "sha512-LHX2S9eVwRNlQauQYgOhQ4xBG6sPp7YGWHYsHSNV94dgSJ7RxYCO1CDvl+JdeQ3V2XE1FKoq+qVH3Hz6k6KIWw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDegr7tIpuV5xHv38rzwr5YwrXH39xa/0hYtgPl2zaCUAiA3D30k4xKiXszA4JecxYqxXeCYnSDwkK4o4o8AcR/nFw=="}]}, "maintainers": [{"name": "kornel", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/http-cache-semantics-3.3.3.tgz_1481204455631_0.7725293333642185"}, "directories": {}}, "3.4.0": {"name": "http-cache-semantics", "version": "3.4.0", "description": "Parses Cache-Control headers and friends", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/pornel/http-cache-semantics.git"}, "scripts": {"test": "mocha"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://kornel.ski/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"mocha": "^2.4.5"}, "gitHead": "97018be7a9deba05f3f2df4bd47a64d81d69d3f3", "bugs": {"url": "https://github.com/pornel/http-cache-semantics/issues"}, "homepage": "https://github.com/pornel/http-cache-semantics#readme", "_id": "http-cache-semantics@3.4.0", "_shasum": "e6b31771a6172640b97c5b9cecd38a071385f96e", "_from": ".", "_npmVersion": "4.0.5", "_nodeVersion": "7.2.1", "_npmUser": {"name": "kornel", "email": "<EMAIL>"}, "dist": {"shasum": "e6b31771a6172640b97c5b9cecd38a071385f96e", "tarball": "https://registry.npmjs.org/http-cache-semantics/-/http-cache-semantics-3.4.0.tgz", "integrity": "sha512-IgjF6wFoUCRIhU7vD4zxuEFOzCta17PAvAiAkoim6sVY6+Injtw7FcMr0LhurvXlgxrjoR+KdXtW76TkqoJANw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDy8gFsrqO78Oyh5WB+7+uZYoHx2wfOl2eye/Fq0O9r9AIhAI13M7+GLGLE57ECgQq4nBbVr/sMMdp8qJiLaYJV7Dcc"}]}, "maintainers": [{"name": "kornel", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/http-cache-semantics-3.4.0.tgz_1481307511204_0.3514719402883202"}, "directories": {}}, "3.5.0": {"name": "http-cache-semantics", "version": "3.5.0", "description": "Parses Cache-Control and other headers. Helps building correct HTTP caches and proxies", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/pornel/http-cache-semantics.git"}, "scripts": {"test": "mocha"}, "files": ["index.js", "test"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://kornel.ski/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"mocha": "^3.2.0"}, "gitHead": "76d0429ae9454610bc22b3f3346d151c65f07a45", "bugs": {"url": "https://github.com/pornel/http-cache-semantics/issues"}, "homepage": "https://github.com/pornel/http-cache-semantics#readme", "_id": "http-cache-semantics@3.5.0", "_shasum": "ccdb954be509e386e301766ad89aa041161b7b14", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.2", "_npmUser": {"name": "kornel", "email": "<EMAIL>"}, "dist": {"shasum": "ccdb954be509e386e301766ad89aa041161b7b14", "tarball": "https://registry.npmjs.org/http-cache-semantics/-/http-cache-semantics-3.5.0.tgz", "integrity": "sha512-xPV+K6HcE6apwcMgAFrcfDyx2xQSWRb4ZRMko4tQ+saZqOoCCy/zB63eHaH+C0e+Z/5O2Hp537wx87HhFV9F3A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDDlhHsvjII2MXyrWwKTCTWS08FNsLA7rS8LZ52ALaa7QIgPhW4GE35se3FMpVqyvl4bp90wsx9LxFjdaDiMQtvBrQ="}]}, "maintainers": [{"name": "kornel", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/http-cache-semantics-3.5.0.tgz_1490127671615_0.8024379580747336"}, "directories": {}}, "3.5.1": {"name": "http-cache-semantics", "version": "3.5.1", "description": "Parses Cache-Control and other headers. Helps building correct HTTP caches and proxies", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/pornel/http-cache-semantics.git"}, "scripts": {"test": "mocha"}, "files": ["index.js", "test"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://kornel.ski/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"mocha": "^3.2.0"}, "gitHead": "dc41aa22677858a6ebcb76c7bdb4ac74bc180b78", "bugs": {"url": "https://github.com/pornel/http-cache-semantics/issues"}, "homepage": "https://github.com/pornel/http-cache-semantics#readme", "_id": "http-cache-semantics@3.5.1", "_shasum": "6b91e9f183671db99e4506fb41f12da9e89da679", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.2", "_npmUser": {"name": "kornel", "email": "<EMAIL>"}, "dist": {"shasum": "6b91e9f183671db99e4506fb41f12da9e89da679", "tarball": "https://registry.npmjs.org/http-cache-semantics/-/http-cache-semantics-3.5.1.tgz", "integrity": "sha512-5LwRvYJFru82+5PTBA9/V4HcVMcDm21L0YPOkp6BocL5cwWKtuuxPxFSrOSJ99jopCLQlOlH0+sm8Y2KV/kSsQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC5Rcdj7/7KS11Lbs4AtiB1Rl29bv0K177J9X85T9fDZAiEAuOsc3i9RGC7QaV7qbceBuv8IgZiNStajCd5B37ENw0Y="}]}, "maintainers": [{"name": "kornel", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/http-cache-semantics-3.5.1.tgz_1490130739290_0.172557424986735"}, "directories": {}}, "3.6.0": {"name": "http-cache-semantics", "version": "3.6.0", "description": "Parses Cache-Control and other headers. Helps building correct HTTP caches and proxies", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/pornel/http-cache-semantics.git"}, "scripts": {"test": "mocha"}, "files": ["index.js", "test"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://kornel.ski/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"mocha": "^3.2.0"}, "gitHead": "50211bd99f1af44fb784e2994a65963ea85ab7e6", "bugs": {"url": "https://github.com/pornel/http-cache-semantics/issues"}, "homepage": "https://github.com/pornel/http-cache-semantics#readme", "_id": "http-cache-semantics@3.6.0", "_shasum": "bacbc1697b53b5c9381c4ed226a60f57cac4cee2", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.3", "_npmUser": {"name": "kornel", "email": "<EMAIL>"}, "dist": {"shasum": "bacbc1697b53b5c9381c4ed226a60f57cac4cee2", "tarball": "https://registry.npmjs.org/http-cache-semantics/-/http-cache-semantics-3.6.0.tgz", "integrity": "sha512-WQ++x5agkxmlfnl4sJoX9WhT93MNM739i4JSTPbpH+cCYA3OzKM8o/ow9RWv3zXgXRHdxkSTvKbPAYyUR+NDlA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDsqL2Ye7Y4dvzpuFcYqfQMVCiPOE8ikCy8cQvS+s8W2AIhALBJ2KYSMDRkuCo+eqop+ah/FtKh0zSgTey8bkXJHSTy"}]}, "maintainers": [{"name": "kornel", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/http-cache-semantics-3.6.0.tgz_1490274995099_0.3009884252678603"}, "directories": {}}, "3.6.1": {"name": "http-cache-semantics", "version": "3.6.1", "description": "Parses Cache-Control and other headers. Helps building correct HTTP caches and proxies", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/pornel/http-cache-semantics.git"}, "scripts": {"test": "mocha"}, "files": ["index.js", "test"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://kornel.ski/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"mocha": "^3.2.0"}, "gitHead": "0642cae85bfcc17bcd01ab099b7499273c3a1cba", "bugs": {"url": "https://github.com/pornel/http-cache-semantics/issues"}, "homepage": "https://github.com/pornel/http-cache-semantics#readme", "_id": "http-cache-semantics@3.6.1", "_shasum": "9d10aa3d70d8b91fb31dd0d8b2903d97e1045d3d", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.3", "_npmUser": {"name": "kornel", "email": "<EMAIL>"}, "dist": {"shasum": "9d10aa3d70d8b91fb31dd0d8b2903d97e1045d3d", "tarball": "https://registry.npmjs.org/http-cache-semantics/-/http-cache-semantics-3.6.1.tgz", "integrity": "sha512-SePGiU+jK91vGI4CdDABjQ9/6KcHQr8L5vljIBiL28ZfWznj6ZTPlSOfwh6GlsoTQYFpLQ4lldMTPzT+Pg9big==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDB9qrDI+XnBTsRGkdx/r86RFDdUQdKPBx7xV6J8wanMAIhAPR8M60feX7ixXJSZHU5tuj30H4MNATbRaUPeSpHmtOb"}]}, "maintainers": [{"name": "kornel", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/http-cache-semantics-3.6.1.tgz_1490289700449_0.7550254117231816"}, "directories": {}}, "3.7.0": {"name": "http-cache-semantics", "version": "3.7.0", "description": "Parses Cache-Control and other headers. Helps building correct HTTP caches and proxies", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/pornel/http-cache-semantics.git"}, "scripts": {"test": "mocha"}, "files": ["index.js", "test"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://kornel.ski/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"mocha": "^3.2.0"}, "gitHead": "6a371e24106dca87fc65847e2b514a1eecbbba21", "bugs": {"url": "https://github.com/pornel/http-cache-semantics/issues"}, "homepage": "https://github.com/pornel/http-cache-semantics#readme", "_id": "http-cache-semantics@3.7.0", "_shasum": "d7b0e325f791c4f44d385574cbc3e6fbb883f7d2", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.4", "_npmUser": {"name": "kornel", "email": "<EMAIL>"}, "dist": {"shasum": "d7b0e325f791c4f44d385574cbc3e6fbb883f7d2", "tarball": "https://registry.npmjs.org/http-cache-semantics/-/http-cache-semantics-3.7.0.tgz", "integrity": "sha512-ElUFlFZtoB3sTregxQ7aNadZKeFCofwXZIrbZtcQasbKPXQurNuFqU2riL0Cz73lx+IrUBNo7KweTObN+oso3A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIETY1YjT0HERLxEGKLCbZxGmoCbOykz0VsgGxF9VFx3zAiEAogUq7u11ruUm3sy02zE0Z7/HEDNJeQx9efMsipDiLh0="}]}, "maintainers": [{"name": "kornel", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/http-cache-semantics-3.7.0.tgz_1491067251788_0.7663479491602629"}, "directories": {}}, "3.7.1": {"name": "http-cache-semantics", "version": "3.7.1", "description": "Parses Cache-Control and other headers. Helps building correct HTTP caches and proxies", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/pornel/http-cache-semantics.git"}, "scripts": {"test": "mocha"}, "files": ["index.js", "test"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://kornel.ski/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"mocha": "^3.2.0"}, "gitHead": "eb1b53504b20010932274d4dba7265535337b62d", "bugs": {"url": "https://github.com/pornel/http-cache-semantics/issues"}, "homepage": "https://github.com/pornel/http-cache-semantics#readme", "_id": "http-cache-semantics@3.7.1", "_shasum": "1419405bb48ae5ba709ee554e657ff9caaf2f940", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.7.4", "_npmUser": {"name": "kornel", "email": "<EMAIL>"}, "dist": {"shasum": "1419405bb48ae5ba709ee554e657ff9caaf2f940", "tarball": "https://registry.npmjs.org/http-cache-semantics/-/http-cache-semantics-3.7.1.tgz", "integrity": "sha512-ev6T7BQpGGydPXyazmZ6jGOaXpTcDQi2Az4oUeq3HOxRcf3tjGS1jRtBU8zoQ+ZrAsnXfK0wtTqzo8d/TbKGew==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQClZCnmQCrShaZ4B9UAeXxRBSz7UeaYdYQxeFfbpHlP6gIgPo+PBtPNV7pYMujaA4DvNW9jKgiydCmcB78BLWfgNaY="}]}, "maintainers": [{"name": "kornel", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/http-cache-semantics-3.7.1.tgz_1491130080653_0.9454772174358368"}, "directories": {}}, "3.7.3": {"name": "http-cache-semantics", "version": "3.7.3", "description": "Parses Cache-Control and other headers. Helps building correct HTTP caches and proxies", "repository": {"type": "git", "url": "git+https://github.com/pornel/http-cache-semantics.git"}, "main": "node4/index.js", "scripts": {"compile": "babel -d node4/ index.js; babel -d node4/test test", "prepublish": "npm run compile", "test": "npm run compile; mocha node4/test"}, "files": ["node4/index.js", "index.js", "test"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://kornel.ski/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"babel-cli": "^6.24.0", "babel-preset-env": "^1.3.2", "mocha": "^3.2.0"}, "gitHead": "612aaff9dec6d62816d0ff68b05c8d83c7f5cfa5", "bugs": {"url": "https://github.com/pornel/http-cache-semantics/issues"}, "homepage": "https://github.com/pornel/http-cache-semantics#readme", "_id": "http-cache-semantics@3.7.3", "_shasum": "2f35c532ecd29f1e5413b9af833b724a3c6f7f72", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.8.0", "_npmUser": {"name": "kornel", "email": "<EMAIL>"}, "dist": {"shasum": "2f35c532ecd29f1e5413b9af833b724a3c6f7f72", "tarball": "https://registry.npmjs.org/http-cache-semantics/-/http-cache-semantics-3.7.3.tgz", "integrity": "sha512-OUh7WWLxe9wzlisiDVNwclT/hKU1+wl4zYhPHoYoLmGMc0rsNb10ZrVr1gaG6m343kl6zVlCKBWqtheN5dEyaw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFrOGK0mSpT927/h+OLY3oT6QBbdQfbRrISncASSXrsxAiAN2bMy/43b9xpEtOv7iTalpqZmA0ufN4cU5QK1F1g2KQ=="}]}, "maintainers": [{"name": "kornel", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/http-cache-semantics-3.7.3.tgz_1491737812591_0.4302996997721493"}, "directories": {}}, "3.8.0": {"name": "http-cache-semantics", "version": "3.8.0", "description": "Parses Cache-Control and other headers. Helps building correct HTTP caches and proxies", "repository": {"type": "git", "url": "git+https://github.com/pornel/http-cache-semantics.git"}, "main": "node4/index.js", "scripts": {"compile": "babel -d node4/ index.js; babel -d node4/test test", "prepublish": "npm run compile", "test": "npm run compile; mocha node4/test"}, "files": ["node4/index.js", "index.js", "test"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://kornel.ski/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"babel-cli": "^6.24.1", "babel-preset-env": "^1.5.2", "mocha": "^3.4.2"}, "gitHead": "03f882d6320c273dc701144f58de18fb6e7d9b37", "bugs": {"url": "https://github.com/pornel/http-cache-semantics/issues"}, "homepage": "https://github.com/pornel/http-cache-semantics#readme", "_id": "http-cache-semantics@3.8.0", "_npmVersion": "5.4.2", "_nodeVersion": "8.6.0", "_npmUser": {"name": "kornel", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-HGQFfBdru2fj/dwPn1oLx1fy6QMPeTAD1yzKcxD4l5biw+5QVaui/ehCqxaitoKJC/vHMLKv3Yd+nTlxboOJig==", "shasum": "1e3ce248730e189ac692a6697b9e3fdea2ff8da3", "tarball": "https://registry.npmjs.org/http-cache-semantics/-/http-cache-semantics-3.8.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDstYF0/mGt++IYNNqX23REvBBgAa67VgM3igaKV1E5bQIhANruh1ukvlL1euYaXP9VrOJEuN0MzODiFh9jkxgUSLYh"}]}, "maintainers": [{"name": "kornel", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-cache-semantics-3.8.0.tgz_1507818807218_0.11429840419441462"}, "directories": {}}, "3.8.1": {"name": "http-cache-semantics", "version": "3.8.1", "description": "Parses Cache-Control and other headers. Helps building correct HTTP caches and proxies", "repository": {"type": "git", "url": "git+https://github.com/pornel/http-cache-semantics.git"}, "main": "node4/index.js", "scripts": {"compile": "babel -d node4/ index.js; babel -d node4/test test", "prepublish": "npm run compile", "test": "npm run compile; mocha node4/test"}, "files": ["node4/index.js"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://kornel.ski/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"babel-cli": "^6.24.1", "babel-preset-env": "^1.6.1", "mocha": "^3.4.2"}, "gitHead": "adfd587c6bb047d44bdd655102f5d7eac43c09ab", "bugs": {"url": "https://github.com/pornel/http-cache-semantics/issues"}, "homepage": "https://github.com/pornel/http-cache-semantics#readme", "_id": "http-cache-semantics@3.8.1", "_npmVersion": "5.5.1", "_nodeVersion": "8.8.1", "_npmUser": {"name": "kornel", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-5ai2iksyV8ZXmnZhHH4rWPoxxistEexSi5936zIQ1bnNTW5VnA85B6P/VpXiRM017IgRvb2kKo1a//y+0wSp3w==", "shasum": "39b0e16add9b605bf0a9ef3d9daaf4843b4cacd2", "tarball": "https://registry.npmjs.org/http-cache-semantics/-/http-cache-semantics-3.8.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD5Hewr2AiUCtlwgqyfIJ1k9/DAuneHP+TdnOCEnk93xgIgOOQsoZUZoIX/8O+afjvJcXTfFn4zC9MzOQsZpAuHI2Y="}]}, "maintainers": [{"name": "kornel", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-cache-semantics-3.8.1.tgz_1512132678976_0.06533534894697368"}, "directories": {}}, "4.0.0": {"name": "http-cache-semantics", "version": "4.0.0", "description": "Parses Cache-Control and other headers. Helps building correct HTTP caches and proxies", "repository": {"type": "git", "url": "git+https://github.com/kornelski/http-cache-semantics.git"}, "main": "index.js", "scripts": {"test": "mocha"}, "files": ["index.js"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://kornel.ski/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"mocha": "^5.1.0"}, "gitHead": "050e2e46821f1a3778d8152e5865c8e72a992adb", "bugs": {"url": "https://github.com/kornelski/http-cache-semantics/issues"}, "homepage": "https://github.com/kornelski/http-cache-semantics#readme", "_id": "http-cache-semantics@4.0.0", "_npmVersion": "5.6.0", "_nodeVersion": "9.11.1", "_npmUser": {"name": "kornel", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-NtexGRtaV5z3ZUX78W9UDTOJPBdpqms6RmwQXmOhHws7CuQK3cqIoQtnmeqi1VvVD6u6eMMRL0sKE9BCZXTDWQ==", "shasum": "2d0069a73c36c80e3297bc3a0cadd669b78a69ce", "tarball": "https://registry.npmjs.org/http-cache-semantics/-/http-cache-semantics-4.0.0.tgz", "fileCount": 3, "unpackedSize": 31010, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa2195CRA9TVsSAnZWagAAVwgP/iCMlOox7b3Yq01QoT0r\nUw4pB+UpZCvEnL+BiXIWdJqbSkGbXQkrOv4KcwIHo307eN5W3A1B7kwoR4SE\naZzohfUnAAnsT3UyUTVNsVMIVKfntV5OdtYRlIDZ9HBUGZ3z0j9QkqoHvUzF\n0Hc+odH7CGNVLEaFKCOKn50XXhkcRxaw1lJYEIZJNz9U9W8XAA8pGMma33l6\ngAel7ipDkwO4rYrmsGb+JZkQcpogb1jy1RdaDlNduNtDDrwN1lc/P/Hw5G6q\nxSadvFicQTOj/C55n+fPYoCFEqLf4sWbIIG3r9oe4+4Zm43uWUuh6Np/DIOX\neuX0I4om7iIOpsKczbSOdpOryGLiis71pb9IfOq3cfAGRxe5RULbuv/UHaO6\nLkbtYSGijLdXNS9L2zGGLzTXMrTDDG4ETvOPVDy4va48mk6Kz1Gd88sJUA+W\n1H6HFsPGi3fwm4CItR3eL28qSbJz5OY6Xib8bnUTpZ0pgIp8n1YjSTroakd0\nHIwJSaO6CLigGDwqEsz6K3nZkSYN6HZgJ1fCy7FEA+rAS4/Rr4bu34FKi4Rt\nMj11kWUmlqD/41lP5LxlXORlGF7Gj0C4Wbbcx92fMDfELDwT4DsFP9oX8Sen\nNaLMvatsJQk6JEVasKdtQxTxuL0umpMtSYd5ajkGeZmHytIS5/b8bsjCtari\nYYR1\r\n=mOpf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDulridXss9RbczLJ2kPETrb6mMU3iJnAgAJqTGIwe9swIgGOnvN9TEvlQdL4htdoAXMREDiBCVKnIMc/bs5vbU9gs="}]}, "maintainers": [{"name": "kornel", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-cache-semantics_4.0.0_1524326264290_0.30099987209809664"}, "_hasShrinkwrap": false}, "4.0.1": {"name": "http-cache-semantics", "version": "4.0.1", "description": "Parses Cache-Control and other headers. Helps building correct HTTP caches and proxies", "repository": {"type": "git", "url": "git+https://github.com/kornelski/http-cache-semantics.git"}, "main": "index.js", "scripts": {"test": "mocha"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://kornel.ski/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"mocha": "^5.1.0"}, "gitHead": "4b81cac299fddc05cab22a84c636ff0095c67c68", "bugs": {"url": "https://github.com/kornelski/http-cache-semantics/issues"}, "homepage": "https://github.com/kornelski/http-cache-semantics#readme", "_id": "http-cache-semantics@4.0.1", "_npmVersion": "6.4.1", "_nodeVersion": "11.1.0", "_npmUser": {"name": "kornel", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-OO/9K7uFN30qwAKvslzmCTbimZ/uRjtdN5S50vvWLwUKqFuZj0n96XyCzF5tHRHEO/Q4JYC01hv41gkX06gmHA==", "shasum": "6c2ef57e22090b177828708a52eaeae9d1d63e1b", "tarball": "https://registry.npmjs.org/http-cache-semantics/-/http-cache-semantics-4.0.1.tgz", "fileCount": 4, "unpackedSize": 32526, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb/SP0CRA9TVsSAnZWagAALiAP+wdeQjQ4BZ+NC5x7Kp26\nctPgdVjh575yYGyPhtInKJxVgaHJm6oHdhn6b7yEzRVfpPUdcsh3rCYq6UcW\ncv/p+Zr7hQRAII7LH6qr4yBPrE7qn0jfHAcwZVUDUxHzZrXb5Bye4xTNKYek\nUIRbUvlBP/egqJhC3lQyyDBm4UwR/XyzYZaB/y0kNrpKew2S+L4fUeeTrb9q\n8d5ey+kbx6KZOmidhypIXgIKikJOy98Bf+qFolQ7KkCpyevgIUojktkqZjxQ\nOdi5i8qoQUxGdM+PZT3HvxOjqfT0F0BtTXZxSv3/V0XOGTEkVYKCDG84EuXV\noPL54wkwtQJWVQR5C0UBv/hlpoQFkE5MLf5II6QbeyF0fRy6cs7yAk0uLq7Y\nulGCptnTcnKWYSLm3j+qBaVkqRd6JPVCyv3EBwILPJxqELVQDCIBXZ94TNtn\nsz75TER7h3z+8Yj945Ujirgq0XOt8i/hhYaf9o/If/ZHV+UOLGFnPmP9xpnJ\ndC6wBduEHi+UW8Xe/WiIhOR0thuIjIZJD3i651HIml4SEaA2APSE1F0fiA/Z\nf0av/Wh98cm6nhPb4lvhGyj2TDn/Eo2AYLXNhxggKrhknFaQoTvm6L5A90Qg\niSwzFjPa+YVb8oJr4UfK19M28qH4AVRB5ctghTc7pqN3wQ07Cn04g05nkqtE\nT/IF\r\n=TOZa\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICblYrFmLrkKGRXSuxye6l8b66tcTpfUXPhashGeZ7shAiEA+gFuGolx0tiCy/OBYEt+fdP+T2baZFo7z9OCk9RAGOM="}]}, "maintainers": [{"name": "kornel", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-cache-semantics_4.0.1_1543316467694_0.7577118055584853"}, "_hasShrinkwrap": false}, "4.0.2": {"name": "http-cache-semantics", "version": "4.0.2", "description": "Parses Cache-Control and other headers. Helps building correct HTTP caches and proxies", "repository": {"type": "git", "url": "git+https://github.com/kornelski/http-cache-semantics.git"}, "main": "index.js", "scripts": {"test": "mocha"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://kornel.ski/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"mocha": "^5.1.0"}, "gitHead": "d5c28b7a20a32419c973485a81c886b8c60ee1d0", "bugs": {"url": "https://github.com/kornelski/http-cache-semantics/issues"}, "homepage": "https://github.com/kornelski/http-cache-semantics#readme", "_id": "http-cache-semantics@4.0.2", "_npmVersion": "6.5.0", "_nodeVersion": "11.5.0", "_npmUser": {"name": "kornel", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-laeSTWIkuFa6lUgZAt+ic9RwOSEwbi9VDQNcCvMFO4sZiDc2Ha8DaZVCJnfpLLQCcS8rvCnIWYmz0POLxt7Dew==", "shasum": "5144bcaaace7d06cfdfbab9948102b11cf9ae90b", "tarball": "https://registry.npmjs.org/http-cache-semantics/-/http-cache-semantics-4.0.2.tgz", "fileCount": 4, "unpackedSize": 32845, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcNf/SCRA9TVsSAnZWagAAetMP/jbicp5p7Gu0Q1Yz6Hk0\nt8RX+kW080CkFmGYdpXYHnSl9H6Bjfl636Vp9b2FYpa/fUyztD5Up4PL99W5\n25L5wX/mQ7/r52VvpQccoPtuhjNlm2/9WnssmT5CuussIlwD75v80UGMeEBm\ntQjPJ3Wc4P+/q0seqHY8QfUq2MuQ8aTAvULNIe5K29KdhErTP9UBwC6GIkL0\n7WOa7w7F09zYF+XglWU1IGGywWsC5ni9wkWwClZDIrslnzQ/eI0qMG93IzZD\n0QqgOqq76kR+8+BxRdzUnxSjWsr7TtrISCGRhVPgZnUA7c1kL5zwov3rqwZr\nltQwQsJfsoxB1Hp2oDZfPImlFk6kBPcNzq0EThiJmgR2pLy0y1ONRa+npw13\nJ+j1vuKRqSy58eBhTHIU4Ucmd5cMkzbV5XTMfLGzqOioWGcnn8yUtRoY5lB5\nTH1GBIkEHyte2RQabZMvG4JduHt6tbWaXwmACmEzOy8jtID9qF39IppeEtm2\nr2ub2YxlyJ3qyqjdEbldEBtgpE1jXgc47J+v3A5mZ1w+S/tA9CVkSbiPoalV\nOVmqhnNecvu6WLBLx2TYmCPNr8j6o9Ti6h6PNyyzMR3BkermEueyttEg+Lw6\ntUu4H2QJ3txOwDweDP2DoH6+7VJ3522cVUu/mkVpOfMpVhaEauH0SsIoOzPs\nT63T\r\n=IW82\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAKKVQp4AbCw6mdjMdU3TD78l6mSNtLntzNMTaMEjQIOAiEA07wT9JCHMl7y0N9dn6f1y88H4MEsH5CrJSNi22hKmxI="}]}, "maintainers": [{"name": "kornel", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-cache-semantics_4.0.2_1547042769756_0.5403384213284901"}, "_hasShrinkwrap": false}, "4.0.3": {"name": "http-cache-semantics", "version": "4.0.3", "description": "Parses Cache-Control and other headers. Helps building correct HTTP caches and proxies", "repository": {"type": "git", "url": "git+https://github.com/kornelski/http-cache-semantics.git"}, "main": "index.js", "scripts": {"test": "mocha"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://kornel.ski/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"eslint": "^5.13.0", "eslint-plugin-prettier": "^3.0.1", "husky": "^0.14.3", "lint-staged": "^8.1.3", "mocha": "^5.1.0", "prettier": "^1.14.3", "prettier-eslint-cli": "^4.7.1"}, "gitHead": "55f30367b1bdaae8cb4963fa27b187232e2177df", "bugs": {"url": "https://github.com/kornelski/http-cache-semantics/issues"}, "homepage": "https://github.com/kornelski/http-cache-semantics#readme", "_id": "http-cache-semantics@4.0.3", "_nodeVersion": "11.8.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-TcIMG3qeVLgDr1TEd2XvHaTnMPwYQUQMIBLy+5pLSDKYFc7UIqj39w8EGzZkaxoLv/l2K8HaI0t5AVA+YYgUew==", "shasum": "495704773277eeef6e43f9ab2c2c7d259dda25c5", "tarball": "https://registry.npmjs.org/http-cache-semantics/-/http-cache-semantics-4.0.3.tgz", "fileCount": 4, "unpackedSize": 34782, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcYBekCRA9TVsSAnZWagAAQhUP/0k7JbEfBIcpBeH1T2Ar\n0mwvV0MRR2JU3pb8VnED00PgH4NnzjtSD+csbqvNsaQPW3Rbf+vU1MKLXwjE\nuwxPazMiyhtAvLRbQ4AoZuhSI0jU1OOTlxEJ2lf1DRLYNbuL4hha5+CLzAQu\n88D2IMDFeAy8FBwh4Ly1D+RmvoKVvW5PMcx8LQVNSEdKQLxz4i/f/4sKTlCb\nM2ZNCPFcFwDG3FCseOzvEvWHAcDopAsvEe7FlUx91xnc83yV6J6f7zq/iuqA\nF3HheCJOoW2CtlA1dzMpVDBKHLqHkikwAqFJiynb2wmR5z3ftvP/x5TKIQdR\nunW0Qm23lr411n+1qbPEwB+TtSkE3gw74mgTufKu2eeYDxVFTBTQqWQDBI2k\nAlsEktNCighnPvDtZTyj4iO2sxU2ARIpC+gM4hm5Jj45Du3S2wfXotzZg9aH\njKhSYAlxel531VmNiNUMIyKxcZ0aFZxjp/DinDK09lJzKiT9coW9hxjU+F8c\nts3742BJstA1n0HkL19BZbJc2deCozMRcFNlu4KRut3dIqGplH4Zk+NlIZdZ\ncL6HXo9czy3Suo83G0katAgmYYXXmDFBOmtl/FM9f1QmHGp1+sZ5iWXG3Tl/\nnBn1XMfKbXUy00Bmde/Ci+VMilNjnBu26hXgQvwrcZc2KoHOZHDH5ArfVcdF\n5mkN\r\n=Hp5l\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICo1/hwmGk1B9/tGsDwQs8Qu38isAVal08mwk49hJM3AAiEA4wJCrIIldY6TMp4QOPoZt+YJarGg1wdl9YhGetK9R08="}]}, "maintainers": [{"name": "kornel", "email": "<EMAIL>"}], "_npmUser": {"name": "kornel", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-cache-semantics_4.0.3_1549801379519_0.2502497462785793"}, "_hasShrinkwrap": false}, "4.0.4": {"name": "http-cache-semantics", "version": "4.0.4", "description": "Parses Cache-Control and other headers. Helps building correct HTTP caches and proxies", "repository": {"type": "git", "url": "git+https://github.com/kornelski/http-cache-semantics.git"}, "main": "index.js", "scripts": {"test": "mocha"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://kornel.ski/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"eslint": "^5.13.0", "eslint-plugin-prettier": "^3.0.1", "husky": "^0.14.3", "lint-staged": "^8.1.3", "mocha": "^5.1.0", "prettier": "^1.14.3", "prettier-eslint-cli": "^4.7.1"}, "gitHead": "84cc9a8dd1f5c16e86aa2c82766b30e404385583", "bugs": {"url": "https://github.com/kornelski/http-cache-semantics/issues"}, "homepage": "https://github.com/kornelski/http-cache-semantics#readme", "_id": "http-cache-semantics@4.0.4", "_nodeVersion": "13.8.0", "_npmVersion": "6.13.7", "dist": {"integrity": "sha512-Z2EICWNJou7Tr9Bd2M2UqDJq3A9F2ePG9w3lIpjoyuSyXFP9QbniJVu3XQYytuw5ebmG7dXSXO9PgAjJG8DDKA==", "shasum": "13eeb612424bb113d52172c28a13109c46fa85d7", "tarball": "https://registry.npmjs.org/http-cache-semantics/-/http-cache-semantics-4.0.4.tgz", "fileCount": 4, "unpackedSize": 34810, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeQdYoCRA9TVsSAnZWagAAwKkP/RhDB2PKVqOrP/A4hhAQ\nAzmTOV3jAOQIXOFfK4AB47JtELdqRBMozFfXdvSqX6lXxYRwYiGXCar8oDST\nqpGNMu5xy4tZC83OsFAYlzK5OMK+7sHINWQ+X2ty4+ZgDTDx1WF3Y2zpI5jG\nu8ACS5i3J/fIrsgnLUQvuMBhuNGmg31IrNHGlt8ZyGKTuC9ZpnwCwS5cUu8v\n+uXiwBtdLDQyWMhJZItND+iuhbiLVICzoCnaKY/SamVIXaczg55tCcMI00G2\ns1y0JkO9zcVFEkjTzIB1G1ivf+wGRL12ChPyzdenxcESd2heRlAEBOxHDwuK\nalbUHWdOZZl2KaZc8WH3D396FZNfQNWVK0jyooNBKr3/jWrA8oPmY6+tH7KW\nGmkUNkwKk/dA8MjY9GmG8zZs29VXfLMO9YwQSaviVRfoV+ruUqye30fimiSk\n3qo1I4VjIhzAa3R5v16TnWOUx/spgMrQJYyTPYfp/hTOQeQs1itY1rVME1ui\nSLb/6itSAdzVvPxIpUDMD7N9mQOL09CuvDhAGOYgOaYJym78JTCLzp/Fc3MK\nV8B6hmOhJWd0/s0kV0C5nqWixvZqkJ2HsJuVwd70LMrXwfDyRWQYEkkY1nWJ\n94b3SeZLhlPap1IJrijsDZF292JI1JPy7KBS9K9VL5J1ZF+5HyipfcGAczz5\nC7o1\r\n=CK+n\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDBvMTyOtO5BFLxvzNGCzND5hLpHmol0D4u9FI5f8F1egIgDCqimRQQKYLvACWZDfrmrWFoDWKY5/BGbMlvDQVpACE="}]}, "maintainers": [{"name": "kornel", "email": "<EMAIL>"}], "_npmUser": {"name": "kornel", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-cache-semantics_4.0.4_1581372967915_0.9108080304325978"}, "_hasShrinkwrap": false}, "4.1.0": {"name": "http-cache-semantics", "version": "4.1.0", "description": "Parses Cache-Control and other headers. Helps building correct HTTP caches and proxies", "repository": {"type": "git", "url": "git+https://github.com/kornelski/http-cache-semantics.git"}, "main": "index.js", "scripts": {"test": "mocha"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://kornel.ski/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"eslint": "^5.13.0", "eslint-plugin-prettier": "^3.0.1", "husky": "^0.14.3", "lint-staged": "^8.1.3", "mocha": "^5.1.0", "prettier": "^1.14.3", "prettier-eslint-cli": "^4.7.1"}, "gitHead": "ed83aec75be817967cdac2663907d060fdc6adc3", "bugs": {"url": "https://github.com/kornelski/http-cache-semantics/issues"}, "homepage": "https://github.com/kornelski/http-cache-semantics#readme", "_id": "http-cache-semantics@4.1.0", "_nodeVersion": "13.8.0", "_npmVersion": "6.13.7", "dist": {"integrity": "sha512-carPklcUh7ROWRK7Cv27RPtdhYhUsela/ue5/jKzjegVvXDqM2ILE9Q2BGn9JZJh1g87cp56su/FgQSzcWS8cQ==", "shasum": "49e91c5cbf36c9b94bcfcd71c23d5249ec74e390", "tarball": "https://registry.npmjs.org/http-cache-semantics/-/http-cache-semantics-4.1.0.tgz", "fileCount": 4, "unpackedSize": 36180, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeXEtPCRA9TVsSAnZWagAAzA8P/A3TCoWR9tWontLwY1Ea\njchpeX/hZAmzO91LrruXgQ0A5a7RH72q/nGQDxnCR6o/3ukv08sAb+H3jG1p\nxqz0euvGSBipxQXFei+V5lkxmK0rDZX9/Q46wXSE/Ja2ZoGeQAMHmsDydFN5\n8Z3Ebqx3Fehx5++tgy/qn1eqUw5YOfuC4J96PXeamqbEkJmrMWywJ74fi+6c\nU2QRK6UlN6eiAmMpvOgviOEjAPYuKs7a2wyckBTwlMNaBRxgzi/gsJjnNLbV\nxrOnxOWiCkNpv2xqN/tbueBz8JzDGCrXY2nTrLBeeEY3QwIkXGnAbpbZ1jfE\n7w42nmQYkW1e+BCxiezUWoQp+Gl16sREcTlCke2cuokED34shNf2mweNWgUz\n4mH+9zPc9mcc5aE6Odk/QKscbe1p0gFAhJ8QyrA8oKk3CZApyR5ZNcR/lxjr\nN602I5IA+JMsAxQzV4ohHEs0gkK+0M00GqP0fEpoon7o0qdZgTW/LhZ1bw+7\n41intFH10aXPn4mmPsMXN1svMzC8V6mx8ZqMal2YsKufMb3xOaGddGV6WaVE\nGr9iSkFXbtIAmbCMN0vs2qT8dB6gWFTkWj9wqGxS5HsFuE/dEy84bPeSlgli\ncSWDov6PNMUEkbTcPhHMbsDqN+GkQhrNB2FSLA1p6exLzikTttQkjtDyKfVR\n9MV/\r\n=1tny\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDx/oVZc5L4YTCjuuf+auSDvzeLKp47NgOxJdWghXGtdAiAnTHNRJp+ESI6n3LixIU3mP6hbazLFcyRM8bWHHJthlQ=="}]}, "maintainers": [{"name": "kornel", "email": "<EMAIL>"}], "_npmUser": {"name": "kornel", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-cache-semantics_4.1.0_1583106895000_0.12597475887904586"}, "_hasShrinkwrap": false}, "4.1.1": {"name": "http-cache-semantics", "version": "4.1.1", "description": "Parses Cache-Control and other headers. Helps building correct HTTP caches and proxies", "repository": {"type": "git", "url": "git+https://github.com/kornelski/http-cache-semantics.git"}, "main": "index.js", "scripts": {"test": "mocha"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://kornel.ski/"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"mocha": "^10.0"}, "gitHead": "24496504352199caf360d1b4d4a01efdc8a7249e", "bugs": {"url": "https://github.com/kornelski/http-cache-semantics/issues"}, "homepage": "https://github.com/kornelski/http-cache-semantics#readme", "_id": "http-cache-semantics@4.1.1", "_nodeVersion": "19.4.0", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-er295DKPVsV82j5kw1Gjt+ADA/XYHsajl82cGNQG2eyoPkvgUhX+nDIyelzhIWbbsXP39EHcI6l5tYs2FYqYXQ==", "shasum": "abe02fcb2985460bf0323be664436ec3476a6d5a", "tarball": "https://registry.npmjs.org/http-cache-semantics/-/http-cache-semantics-4.1.1.tgz", "fileCount": 4, "unpackedSize": 35938, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGygvN3Se0zxQ/mTxHNc2vU4yMZ9gjqZ5Eg074xkMbAVAiEAlQ2uBmUo2WnAkHmlZ9RWL4swb0m5SqfSyDDgvOwZIcE="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0yjnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq7VxAAkpq0uXaMxwzz6Wmq1wgvIbx80vTxTRni9wrPU/0YAJHtsU/R\r\nUcZ/ypDzvJaSSrdjUXZcWhb/Md6Gfzq89AA4aqo71pzRTEMAu/9LFAU/NFRR\r\nWc9RrzC9LyRM+Sn6qaCQ2pBPyyqo8O2hVtbuWaHuL6XNk0HpCNkBfhEJsKM5\r\n49vZYiGEXvmSlHY2VQ0GSbNcvLyIQ28pjC0mOcdJ/l4OUqSzcXpxmYPzoNLq\r\nL7yVEKCpdykEt2INBA+G9Px3ixmc6HsCme6z967gC33dKnOYKtewCquHhMYo\r\n6dublLQ8ulGSkJTnknqC3dx5yzh2I4dAe2TLFFrDfpN3jLShaHhjkJTQ3omc\r\nukNmNU41dyJyLj/IwXvcyYy4Ec6dB59F/ctnEB6Fg8xES3AYrYSF1bqV4D39\r\ncRS8k+N4bq5l+2v7yuOnkHb+qAAgCBcC3qsYHGwwXPaXCKRwxhErqOrQCBl9\r\nmICS9tm8Ft6+y17DW7Mzr2lhdwiHJAepUxpVgSPf3JOSwdrAYrNU/KW4b58B\r\nJxUPeS9KXfCzjxhXNVt5o7r7hyobkNU1UzpapO408C4Sl50GyRxXYL27r7Ps\r\nhodtirKJSkzYSxamQmwrrQ9AdD9QYKEXIxJ8UX1TN9tSa1Xpysbe+Ggt6Tob\r\nV+CE74/nWjnTlfg39VSdnYPU1eXCyZTJmzw=\r\n=hHfC\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "kornel", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "kornel", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-cache-semantics_4.1.1_1674782951771_0.*****************"}, "_hasShrinkwrap": false}}, "readme": "# Can I cache this? [![Build Status](https://travis-ci.org/kornelski/http-cache-semantics.svg?branch=master)](https://travis-ci.org/kornelski/http-cache-semantics)\n\n`CachePolicy` tells when responses can be reused from a cache, taking into account [HTTP RFC 7234](http://httpwg.org/specs/rfc7234.html) rules for user agents and shared caches.\nIt also implements [RFC 5861](https://tools.ietf.org/html/rfc5861), implementing `stale-if-error` and `stale-while-revalidate`.\nIt's aware of many tricky details such as the `Vary` header, proxy revalidation, and authenticated responses.\n\n## Usage\n\nCacheability of an HTTP response depends on how it was requested, so both `request` and `response` are required to create the policy.\n\n```js\nconst policy = new CachePolicy(request, response, options);\n\nif (!policy.storable()) {\n    // throw the response away, it's not usable at all\n    return;\n}\n\n// Cache the data AND the policy object in your cache\n// (this is pseudocode, roll your own cache (lru-cache package works))\nletsPretendThisIsSomeCache.set(\n    request.url,\n    { policy, response },\n    policy.timeToLive()\n);\n```\n\n```js\n// And later, when you receive a new request:\nconst { policy, response } = letsPretendThisIsSomeCache.get(newRequest.url);\n\n// It's not enough that it exists in the cache, it has to match the new request, too:\nif (policy && policy.satisfiesWithoutRevalidation(newRequest)) {\n    // OK, the previous response can be used to respond to the `newRequest`.\n    // Response headers have to be updated, e.g. to add Age and remove uncacheable headers.\n    response.headers = policy.responseHeaders();\n    return response;\n}\n```\n\nIt may be surprising, but it's not enough for an HTTP response to be [fresh](#yo-fresh) to satisfy a request. It may need to match request headers specified in `Vary`. Even a matching fresh response may still not be usable if the new request restricted cacheability, etc.\n\nThe key method is `satisfiesWithoutRevalidation(newRequest)`, which checks whether the `newRequest` is compatible with the original request and whether all caching conditions are met.\n\n### Constructor options\n\nRequest and response must have a `headers` property with all header names in lower case. `url`, `status` and `method` are optional (defaults are any URL, status `200`, and `GET` method).\n\n```js\nconst request = {\n    url: '/',\n    method: 'GET',\n    headers: {\n        accept: '*/*',\n    },\n};\n\nconst response = {\n    status: 200,\n    headers: {\n        'cache-control': 'public, max-age=7234',\n    },\n};\n\nconst options = {\n    shared: true,\n    cacheHeuristic: 0.1,\n    immutableMinTimeToLive: 24 * 3600 * 1000, // 24h\n    ignoreCargoCult: false,\n};\n```\n\nIf `options.shared` is `true` (default), then the response is evaluated from a perspective of a shared cache (i.e. `private` is not cacheable and `s-maxage` is respected). If `options.shared` is `false`, then the response is evaluated from a perspective of a single-user cache (i.e. `private` is cacheable and `s-maxage` is ignored). `shared: true` is recommended for HTTP clients.\n\n`options.cacheHeuristic` is a fraction of response's age that is used as a fallback cache duration. The default is 0.1 (10%), e.g. if a file hasn't been modified for 100 days, it'll be cached for 100\\*0.1 = 10 days.\n\n`options.immutableMinTimeToLive` is a number of milliseconds to assume as the default time to cache responses with `Cache-Control: immutable`. Note that [per RFC](http://httpwg.org/http-extensions/immutable.html) these can become stale, so `max-age` still overrides the default.\n\nIf `options.ignoreCargoCult` is true, common anti-cache directives will be completely ignored if the non-standard `pre-check` and `post-check` directives are present. These two useless directives are most commonly found in bad StackOverflow answers and PHP's \"session limiter\" defaults.\n\n### `storable()`\n\nReturns `true` if the response can be stored in a cache. If it's `false` then you MUST NOT store either the request or the response.\n\n### `satisfiesWithoutRevalidation(newRequest)`\n\nThis is the most important method. Use this method to check whether the cached response is still fresh in the context of the new request.\n\nIf it returns `true`, then the given `request` matches the original response this cache policy has been created with, and the response can be reused without contacting the server. Note that the old response can't be returned without being updated, see `responseHeaders()`.\n\nIf it returns `false`, then the response may not be matching at all (e.g. it's for a different URL or method), or may require to be refreshed first (see `revalidationHeaders()`).\n\n### `responseHeaders()`\n\nReturns updated, filtered set of response headers to return to clients receiving the cached response. This function is necessary, because proxies MUST always remove hop-by-hop headers (such as `TE` and `Connection`) and update response's `Age` to avoid doubling cache time.\n\n```js\ncachedResponse.headers = cachePolicy.responseHeaders(cachedResponse);\n```\n\n### `timeToLive()`\n\nReturns approximate time in _milliseconds_ until the response becomes stale (i.e. not fresh).\n\nAfter that time (when `timeToLive() <= 0`) the response might not be usable without revalidation. However, there are exceptions, e.g. a client can explicitly allow stale responses, so always check with `satisfiesWithoutRevalidation()`.\n`stale-if-error` and `stale-while-revalidate` extend the time to live of the cache, that can still be used if stale.\n\n### `toObject()`/`fromObject(json)`\n\nChances are you'll want to store the `CachePolicy` object along with the cached response. `obj = policy.toObject()` gives a plain JSON-serializable object. `policy = CachePolicy.fromObject(obj)` creates an instance from it.\n\n### Refreshing stale cache (revalidation)\n\nWhen a cached response has expired, it can be made fresh again by making a request to the origin server. The server may respond with status 304 (Not Modified) without sending the response body again, saving bandwidth.\n\nThe following methods help perform the update efficiently and correctly.\n\n#### `revalidationHeaders(newRequest)`\n\nReturns updated, filtered set of request headers to send to the origin server to check if the cached response can be reused. These headers allow the origin server to return status 304 indicating the response is still fresh. All headers unrelated to caching are passed through as-is.\n\nUse this method when updating cache from the origin server.\n\n```js\nupdateRequest.headers = cachePolicy.revalidationHeaders(updateRequest);\n```\n\n#### `revalidatedPolicy(revalidationRequest, revalidationResponse)`\n\nUse this method to update the cache after receiving a new response from the origin server. It returns an object with two keys:\n\n-   `policy` — A new `CachePolicy` with HTTP headers updated from `revalidationResponse`. You can always replace the old cached `CachePolicy` with the new one.\n-   `modified` — Boolean indicating whether the response body has changed.\n    -   If `false`, then a valid 304 Not Modified response has been received, and you can reuse the old cached response body. This is also affected by `stale-if-error`.\n    -   If `true`, you should use new response's body (if present), or make another request to the origin server without any conditional headers (i.e. don't use `revalidationHeaders()` this time) to get the new resource.\n\n```js\n// When serving requests from cache:\nconst { oldPolicy, oldResponse } = letsPretendThisIsSomeCache.get(\n    newRequest.url\n);\n\nif (!oldPolicy.satisfiesWithoutRevalidation(newRequest)) {\n    // Change the request to ask the origin server if the cached response can be used\n    newRequest.headers = oldPolicy.revalidationHeaders(newRequest);\n\n    // Send request to the origin server. The server may respond with status 304\n    const newResponse = await makeRequest(newRequest);\n\n    // Create updated policy and combined response from the old and new data\n    const { policy, modified } = oldPolicy.revalidatedPolicy(\n        newRequest,\n        newResponse\n    );\n    const response = modified ? newResponse : oldResponse;\n\n    // Update the cache with the newer/fresher response\n    letsPretendThisIsSomeCache.set(\n        newRequest.url,\n        { policy, response },\n        policy.timeToLive()\n    );\n\n    // And proceed returning cached response as usual\n    response.headers = policy.responseHeaders();\n    return response;\n}\n```\n\n# Yo, FRESH\n\n![satisfiesWithoutRevalidation](fresh.jpg)\n\n## Used by\n\n-   [ImageOptim API](https://imageoptim.com/api), [make-fetch-happen](https://github.com/zkat/make-fetch-happen), [cacheable-request](https://www.npmjs.com/package/cacheable-request) ([got](https://www.npmjs.com/package/got)), [npm/registry-fetch](https://github.com/npm/registry-fetch), [etc.](https://github.com/kornelski/http-cache-semantics/network/dependents)\n\n## Implemented\n\n-   `Cache-Control` response header with all the quirks.\n-   `Expires` with check for bad clocks.\n-   `Pragma` response header.\n-   `Age` response header.\n-   `Vary` response header.\n-   Default cacheability of statuses and methods.\n-   Requests for stale data.\n-   Filtering of hop-by-hop headers.\n-   Basic revalidation request\n-   `stale-if-error`\n\n## Unimplemented\n\n-   Merging of range requests, `If-Range` (but correctly supports them as non-cacheable)\n-   Revalidation of multiple representations\n\n### Trusting server `Date`\n\nPer the RFC, the cache should take into account the time between server-supplied `Date` and the time it received the response. The RFC-mandated behavior creates two problems:\n\n * Servers with incorrectly set timezone may add several hours to cache age (or more, if the clock is completely wrong).\n * Even reasonably correct clocks may be off by a couple of seconds, breaking `max-age=1` trick (which is useful for reverse proxies on high-traffic servers).\n\nPrevious versions of this library had an option to ignore the server date if it was \"too inaccurate\". To support the `max-age=1` trick the library also has to ignore dates that pretty accurate. There's no point of having an option to trust dates that are only a bit inaccurate, so this library won't trust any server dates. `max-age` will be interpreted from the time the response has been received, not from when it has been sent. This will affect only [RFC 1149 networks](https://tools.ietf.org/html/rfc1149).\n", "maintainers": [{"name": "kornel", "email": "<EMAIL>"}], "time": {"modified": "2023-01-27T01:29:12.047Z", "created": "2016-05-31T09:18:22.371Z", "1.0.0": "2016-05-31T09:18:22.371Z", "2.0.0": "2016-05-31T11:43:36.739Z", "3.0.0": "2016-05-31T16:12:09.950Z", "3.1.0": "2016-05-31T23:24:25.659Z", "3.2.0": "2016-06-05T12:52:13.188Z", "3.3.0": "2016-12-08T00:52:31.311Z", "3.3.1": "2016-12-08T11:44:00.624Z", "3.3.2": "2016-12-08T12:01:36.963Z", "3.3.3": "2016-12-08T13:40:57.678Z", "3.4.0": "2016-12-09T18:18:31.748Z", "3.5.0": "2017-03-21T20:21:13.644Z", "3.5.1": "2017-03-21T21:12:19.930Z", "3.6.0": "2017-03-23T13:16:37.192Z", "3.6.1": "2017-03-23T17:21:42.476Z", "3.7.0": "2017-04-01T17:20:53.954Z", "3.7.1": "2017-04-02T10:48:02.423Z", "3.7.3": "2017-04-09T11:36:54.801Z", "3.8.0": "2017-10-12T14:33:28.299Z", "3.8.1": "2017-12-01T12:51:19.985Z", "4.0.0": "2018-04-21T15:57:44.396Z", "4.0.1": "2018-11-27T11:01:07.864Z", "4.0.2": "2019-01-09T14:06:10.008Z", "4.0.3": "2019-02-10T12:22:59.672Z", "4.0.4": "2020-02-10T22:16:08.024Z", "4.1.0": "2020-03-01T23:54:55.142Z", "4.1.1": "2023-01-27T01:29:11.933Z"}, "homepage": "https://github.com/kornelski/http-cache-semantics#readme", "repository": {"type": "git", "url": "git+https://github.com/kornelski/http-cache-semantics.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://kornel.ski/"}, "bugs": {"url": "https://github.com/kornelski/http-cache-semantics/issues"}, "license": "BSD-2-<PERSON><PERSON>", "readmeFilename": "README.md", "users": {"kornel": true, "shanewholloway": true}}