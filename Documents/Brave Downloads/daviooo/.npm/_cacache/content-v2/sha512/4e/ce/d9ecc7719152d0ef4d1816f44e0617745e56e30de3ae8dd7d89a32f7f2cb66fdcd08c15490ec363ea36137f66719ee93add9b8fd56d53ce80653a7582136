{"_id": "is-typedarray", "_rev": "6-ae85c85ced40df89a3f1b0d97b2940bf", "name": "is-typedarray", "description": "Detect whether or not an object is a Typed Array", "dist-tags": {"latest": "1.0.0"}, "versions": {"0.0.0": {"name": "is-typedarray", "version": "0.0.0", "description": "Detect whether or not an object is a Typed Array", "main": "index.js", "scripts": {"test": "node test"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://hughsk.io/"}, "license": "MIT", "dependencies": {}, "devDependencies": {"tape": "^2.13.1"}, "repository": {"type": "git", "url": "git://github.com/hughsk/is-typedarray.git"}, "keywords": ["typed", "array", "detect", "is", "util"], "bugs": {"url": "https://github.com/hughsk/is-typedarray/issues"}, "homepage": "https://github.com/hughsk/is-typedarray", "_id": "is-typedarray@0.0.0", "_shasum": "9e5c50a8bf17b3051b48c5e5b678440821f756f4", "_from": ".", "_npmVersion": "1.4.10", "_npmUser": {"name": "hughsk", "email": "<EMAIL>"}, "maintainers": [{"name": "hughsk", "email": "<EMAIL>"}], "dist": {"shasum": "9e5c50a8bf17b3051b48c5e5b678440821f756f4", "tarball": "https://registry.npmjs.org/is-typedarray/-/is-typedarray-0.0.0.tgz", "integrity": "sha512-961qlEuQeQ6Xtxae9NFX5Ir6e5aE8XcBbqjqG6+UoZ2DFNeMZGbcoJ4+yhWDsWdromKXQzESat/XGFW5aykCyg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGQ/rGLjLo+jPGjdTY/uSAQmu8ZfbfQlGutmaarIn16VAiBGSv+7T6Rz9K1gliPr+6IAeFP43jE6r9eyTlTt/BAEHQ=="}]}}, "1.0.0": {"name": "is-typedarray", "version": "1.0.0", "description": "Detect whether or not an object is a Typed Array", "main": "index.js", "scripts": {"test": "node test"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://hughsk.io/"}, "license": "MIT", "dependencies": {}, "devDependencies": {"tape": "^2.13.1"}, "repository": {"type": "git", "url": "git://github.com/hughsk/is-typedarray.git"}, "keywords": ["typed", "array", "detect", "is", "util"], "bugs": {"url": "https://github.com/hughsk/is-typedarray/issues"}, "homepage": "https://github.com/hughsk/is-typedarray", "gitHead": "0617cfa871686cf541af62b144f130488f44f6fe", "_id": "is-typedarray@1.0.0", "_shasum": "e479c80858df0c1b11ddda6940f96011fcda4a9a", "_from": ".", "_npmVersion": "2.7.5", "_nodeVersion": "0.10.36", "_npmUser": {"name": "hughsk", "email": "<EMAIL>"}, "dist": {"shasum": "e479c80858df0c1b11ddda6940f96011fcda4a9a", "tarball": "https://registry.npmjs.org/is-typedarray/-/is-typedarray-1.0.0.tgz", "integrity": "sha512-cyA56iCMHAh5CdzjJIa4aohJyeO1YbwLi3Jc35MmRU6poroFjIGZzUzupGiRPOjgHg9TLu43xbpwXk523fMxKA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD4rYc38nb6XMTDJ6NP6NxzUMfNmVtB1ZxN+h6ELgPIdwIhAN4rA+ye8n7FdyR3kwNdjDpIdcLbjW6s0av1K7hAvQKL"}]}, "maintainers": [{"name": "hughsk", "email": "<EMAIL>"}]}}, "readme": "# is-typedarray [![locked](http://badges.github.io/stability-badges/dist/locked.svg)](http://github.com/badges/stability-badges)\n\nDetect whether or not an object is a\n[Typed Array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Typed_arrays).\n\n## Usage\n\n[![NPM](https://nodei.co/npm/is-typedarray.png)](https://nodei.co/npm/is-typedarray/)\n\n### isTypedArray(array)\n\nReturns `true` when array is a Typed Array, and `false` when it is not.\n\n## License\n\nMIT. See [LICENSE.md](http://github.com/hughsk/is-typedarray/blob/master/LICENSE.md) for details.\n", "maintainers": [{"name": "hughsk", "email": "<EMAIL>"}], "time": {"modified": "2022-06-19T02:51:55.380Z", "created": "2014-06-01T19:10:47.674Z", "0.0.0": "2014-06-01T19:10:47.674Z", "1.0.0": "2015-05-17T01:03:39.627Z"}, "homepage": "https://github.com/hughsk/is-typedarray", "keywords": ["typed", "array", "detect", "is", "util"], "repository": {"type": "git", "url": "git://github.com/hughsk/is-typedarray.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://hughsk.io/"}, "bugs": {"url": "https://github.com/hughsk/is-typedarray/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"anoubis": true}}