{"_id": "unpipe", "_rev": "5-849790a164f03a428808d7de8b3e853d", "name": "unpipe", "description": "Unpipe a stream from all destinations", "dist-tags": {"latest": "1.0.0"}, "versions": {"1.0.0": {"name": "unpipe", "description": "Unpipe a stream from all destinations", "version": "1.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/stream-utils/unpipe"}, "devDependencies": {"istanbul": "0.3.15", "mocha": "2.2.5", "readable-stream": "1.1.13"}, "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "engines": {"node": ">= 0.8"}, "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "gitHead": "d2df901c06487430e78dca62b6edb8bb2fc5e99d", "bugs": {"url": "https://github.com/stream-utils/unpipe/issues"}, "homepage": "https://github.com/stream-utils/unpipe", "_id": "unpipe@1.0.0", "_shasum": "b2bf4ee8514aae6165b4817829d21b2ef49904ec", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "b2bf4ee8514aae6165b4817829d21b2ef49904ec", "tarball": "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz", "integrity": "sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCpHTDHxnotr5w+1aU3oaIHKAxwWRTz9kK5GCVBEr+AVQIgKFywOVOCTQZagzU6GZnJ7osVKEcYBRle7WxR5cEns6U="}]}}}, "readme": "# unpipe\n\n[![NPM Version][npm-image]][npm-url]\n[![NPM Downloads][downloads-image]][downloads-url]\n[![Node.js Version][node-image]][node-url]\n[![Build Status][travis-image]][travis-url]\n[![Test Coverage][coveralls-image]][coveralls-url]\n\nUnpipe a stream from all destinations.\n\n## Installation\n\n```sh\n$ npm install unpipe\n```\n\n## API\n\n```js\nvar unpipe = require('unpipe')\n```\n\n### unpipe(stream)\n\nUnpipes all destinations from a given stream. With stream 2+, this is\nequivalent to `stream.unpipe()`. When used with streams 1 style streams\n(typically Node.js 0.8 and below), this module attempts to undo the\nactions done in `stream.pipe(dest)`.\n\n## License\n\n[MIT](LICENSE)\n\n[npm-image]: https://img.shields.io/npm/v/unpipe.svg\n[npm-url]: https://npmjs.org/package/unpipe\n[node-image]: https://img.shields.io/node/v/unpipe.svg\n[node-url]: http://nodejs.org/download/\n[travis-image]: https://img.shields.io/travis/stream-utils/unpipe.svg\n[travis-url]: https://travis-ci.org/stream-utils/unpipe\n[coveralls-image]: https://img.shields.io/coveralls/stream-utils/unpipe.svg\n[coveralls-url]: https://coveralls.io/r/stream-utils/unpipe?branch=master\n[downloads-image]: https://img.shields.io/npm/dm/unpipe.svg\n[downloads-url]: https://npmjs.org/package/unpipe\n", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2022-06-28T05:14:29.958Z", "created": "2015-06-14T20:30:19.934Z", "1.0.0": "2015-06-14T20:30:19.934Z"}, "homepage": "https://github.com/stream-utils/unpipe", "repository": {"type": "git", "url": "https://github.com/stream-utils/unpipe"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/stream-utils/unpipe/issues"}, "license": "MIT", "readmeFilename": "README.md"}