{"_id": "github-from-package", "_rev": "7-db6ea0489161a92c6352c2c8d64c843f", "name": "github-from-package", "description": "return the github url from a package.json file", "dist-tags": {"latest": "0.0.0"}, "versions": {"0.0.0": {"name": "github-from-package", "version": "0.0.0", "description": "return the github url from a package.json file", "main": "index.js", "devDependencies": {"tap": "~0.3.0", "tape": "~0.1.5"}, "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git://github.com/substack/github-from-package.git"}, "homepage": "https://github.com/substack/github-from-package", "keywords": ["github", "package.json", "npm", "repository"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "readme": "# github-from-package\n\nreturn the github url from a package.json file\n\n[![build status](https://secure.travis-ci.org/substack/github-from-package.png)](http://travis-ci.org/substack/github-from-package)\n\n# example\n\nFor the `./package.json` file:\n\n``` json\n{\n  \"name\": \"beep-boop\",\n  \"version\": \"1.2.3\",\n  \"repository\" : {\n    \"type\" : \"git\",\n    \"url\": \"**************:substack/beep-boop.git\"\n  }\n}\n```\n\n``` js\nvar github = require('github-from-package');\nvar url = github(require('./package.json'));\nconsole.log(url);\n```\n\n```\nhttps://github.com/substack/beep-boop\n```\n\n# methods\n\n``` js\nvar github = require('github-from-package')\n```\n\n## var url = github(pkg)\n\nReturn the most likely github url from the package.json contents `pkg`. If no\ngithub url can be determined, return `undefined`.\n\n# install\n\nWith [npm](https://npmjs.org) do:\n\n```\nnpm install github-from-package\n```\n\n# license\n\nMIT\n", "_id": "github-from-package@0.0.0", "dist": {"shasum": "97fb5d96bfde8973313f20e8288ef9a167fa64ce", "tarball": "https://registry.npmjs.org/github-from-package/-/github-from-package-0.0.0.tgz", "integrity": "sha512-SyHy3T1v2NUXn29OsWdxmK6RwHD+vkj3v8en8AOBZ1wBQ/hCAQ5bAQTD02kW4W9tUp/3Qh6J8r9EvntiyCmOOw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFDWAiT2bZ2qpllWbLr8p0Z1mhzms7BPrRdWiC2IeIHQAiBgDnTTFfOEsEYktJ9WGYDc24K5zOtiiRQ9Sg3xis/Zew=="}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}}, "readme": "# github-from-package\n\nreturn the github url from a package.json file\n\n[![build status](https://secure.travis-ci.org/substack/github-from-package.png)](http://travis-ci.org/substack/github-from-package)\n\n# example\n\nFor the `./package.json` file:\n\n``` json\n{\n  \"name\": \"beep-boop\",\n  \"version\": \"1.2.3\",\n  \"repository\" : {\n    \"type\" : \"git\",\n    \"url\": \"**************:substack/beep-boop.git\"\n  }\n}\n```\n\n``` js\nvar github = require('github-from-package');\nvar url = github(require('./package.json'));\nconsole.log(url);\n```\n\n```\nhttps://github.com/substack/beep-boop\n```\n\n# methods\n\n``` js\nvar github = require('github-from-package')\n```\n\n## var url = github(pkg)\n\nReturn the most likely github url from the package.json contents `pkg`. If no\ngithub url can be determined, return `undefined`.\n\n# install\n\nWith [npm](https://npmjs.org) do:\n\n```\nnpm install github-from-package\n```\n\n# license\n\nMIT\n", "maintainers": [{"email": "<EMAIL>", "name": "noperson<PERSON><PERSON><PERSON>"}], "time": {"modified": "2022-11-11T06:35:20.853Z", "created": "2012-12-29T11:12:51.043Z", "0.0.0": "2012-12-29T11:12:52.069Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "repository": {"type": "git", "url": "git://github.com/substack/github-from-package.git"}}