{"name": "tuf-js", "dist-tags": {"latest": "3.0.1"}, "versions": {"0.0.0": {"name": "tuf-js", "version": "0.0.0", "dependencies": {"minimatch": "^5.1.0", "make-fetch-happen": "^11.0.1"}, "devDependencies": {"jest": "^28.1.3", "nock": "^13.2.9", "eslint": "^8.28.0", "ts-jest": "^28.0.8", "prettier": "^2.8.0", "typescript": "^4.9.3", "@types/jest": "^28.1.8", "@types/node": "^18.11.9", "http-server": "^14.1.1", "@tsconfig/node12": "^1.0.11", "@types/minimatch": "^5.1.2", "@types/lodash.isequal": "^4.5.6", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "@types/make-fetch-happen": "^10.0.1", "@typescript-eslint/parser": "^5.45.0", "@typescript-eslint/eslint-plugin": "^5.45.0"}, "dist": {"shasum": "7a5d94fb734ebf62f4428da2bc77ebe3a6b43587", "tarball": "https://registry.npmjs.org/tuf-js/-/tuf-js-0.0.0.tgz", "fileCount": 75, "integrity": "sha512-KUhXothXO0tl8LkeKgCrtkurLJbWZ+lL8OpgrgbW2JoCs7/KaQ76zMYIIRoRg6PXZi/Uarpcyha45eqRaprJFA==", "signatures": [{"sig": "MEQCIE1rWiUfJcgdI6DNWhuNCM/2X1Oa4AeBhPQno96GaRZaAiBcWVdLIkl5gOmyibZ8BHc2vo5s9mn+StryHsYEflv7Bg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 197186, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjiPrvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrZpA/+PQBbjhRSa3sfe0j8gASMsdJlqqo38+ByUmbGucN2AKJWfnAo\r\nimki9qrbLdZ8a21qAaBz7CnQCp/S2GzzyysFk/vgK8sw3sRK7SMU7WJ/Eu/6\r\nEBKmWo6EwZWxmZ/fFcQKV3zMBzdfNLjC6v9vbyELXQcyLJk2lBWduhoA30gn\r\nslwrQNKAp+hHrKPB3ZD+HQ/Xd/N5FojoTOFLP6g+LoYcIZKz9Vauo7E2BKmo\r\nMeY4LHL00dlEE1BUiByyKUtIMIXeWNzHiSVjzU0djUuaj1TO2UmezBBlYv+H\r\nYjkAXZ3HX25DicEaJ0cDS5scsY5GjXdNVnTZCxWi+3Tm4f7CRIg7ft69Om8p\r\nfU+l98q7fmdAcSUr1pN+BLXAMi72wfWEC/6/s0FRdD/HlpCs846wnpV9Ttcj\r\nCCtotjRfpOGj4vR+M/ffLF3D60HwB5q5/lmHB1Z1meeN4AMmyrSSaq1g046L\r\nSZLCBFzivagS/++xuV3BAPECV4vlIdq4fVhyBJuTjqgj9LcI9wI5Zqfh3bPK\r\n1RAa3qVkX/Sqr6ICUuGrgcV4U72Amu52zzwSIKmmQLqZyGckTiZI5HgGbYX1\r\nRoHSe9+h07DHgp+7Rzhq+f/w1ic/HnCNyPTRMkWCveZ9Q7M6yVVlzorYyA7i\r\nnHBRdM16EZYTzC/hYY6/xv+KdHAR6dTH24o=\r\n=aKwt\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.1-alpha.1": {"name": "tuf-js", "version": "0.0.1-alpha.1", "dependencies": {"minimatch": "^5.1.1", "make-fetch-happen": "^11.0.1"}, "devDependencies": {"jest": "^28.1.3", "nock": "^13.2.9", "eslint": "^8.28.0", "ts-jest": "^28.0.8", "prettier": "^2.8.0", "typescript": "^4.9.3", "@types/jest": "^28.1.8", "@types/node": "^18.11.10", "http-server": "^14.1.1", "@tsconfig/node12": "^1.0.11", "@types/minimatch": "^5.1.2", "@types/lodash.isequal": "^4.5.6", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "@types/make-fetch-happen": "^10.0.1", "@typescript-eslint/parser": "^5.45.0", "@typescript-eslint/eslint-plugin": "^5.45.0"}, "dist": {"shasum": "d6432740259c50e052c4375939885607dfbd89d0", "tarball": "https://registry.npmjs.org/tuf-js/-/tuf-js-0.0.1-alpha.1.tgz", "fileCount": 55, "integrity": "sha512-5FQgmIiHTVXL3EWCcDnX2CFbcxtrNFo9FP+UAMpZ1Cbkd/liOFA1weckKN3IiecrhzRo/LE7YGvc6CESqZsf0g==", "signatures": [{"sig": "MEUCIQCDD9oGodXKnjDCcXoVUeusS3z+1d086/R5Adj4+JkKYwIgVC68607OQ62twR9jVnAsFlSXPmOPcyiwaBiloSijGck=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97753, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjjkl2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqcOhAAmSjGqgs0QKahogWNMz0hWmatlRG4v4/wXsuQ4SMMCwex9GMi\r\nw87RgJHRmyyhQzz2vFpUp/f9ff9CEDUpaK/L7wgCFHELUUzSPBcSFBZOlTEm\r\nYxSiul6oMhPub1opDwuI3c5RD3ebszGcqRf51vvFlouKY0kw2Saivm+vkqo9\r\nhrEkE8BslWIY+ecCPyhmKS/hdffIQr6h7t/Nx2YDnv/3LiphlEeA4dYoi8sS\r\n7M4HASCjimaIqrZav223ohshPuexm0LpskE2/q+dM7Kx/13PKFcN13LwxQeh\r\nXEbxIqeCI9HS5j/O/+poATj0U5aiXP5kRGSAh/7iZYl+4BTdm3wS8oLQIlLr\r\nWNQPBquBDPNK48xblA9I3gdSjqDBda4qKG63vopPn+rPk57vjio33d9YtEzn\r\nOSB+mGQodCHufJTja5TE1U6HrNG9oaPDmsv9CM4fAHcuYOQXkUD9NhYc0OLJ\r\n7FwoCbaACEddvJXyrJXu7KjG+/4LQMxxhYYkRv5TlKWVulQ7MOFBri2op3GQ\r\nB/1hmPPSTUsFWvDdtnXCCMf3d5YW8R14L3Vf2c1pFxTHRyRqtf6u5EOqdKwF\r\nHBKqz+IqH62hiM+VRLNL5CBzS5OBstWOrd0RcI2AJnx9gQtTBPnEJOZ0PrV/\r\n1DgVfMfb3SW/VHvnbOe/CKCHo8Bx09KEfX0=\r\n=1imq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.1-alpha.2": {"name": "tuf-js", "version": "0.0.1-alpha.2", "dependencies": {"minimatch": "^5.1.1", "make-fetch-happen": "^11.0.1"}, "devDependencies": {"jest": "^28.1.3", "nock": "^13.2.9", "eslint": "^8.28.0", "ts-jest": "^28.0.8", "prettier": "^2.8.0", "typescript": "^4.9.3", "@types/jest": "^28.1.8", "@types/node": "^18.11.10", "http-server": "^14.1.1", "@tsconfig/node12": "^1.0.11", "@types/minimatch": "^5.1.2", "@types/lodash.isequal": "^4.5.6", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "@types/make-fetch-happen": "^10.0.1", "@typescript-eslint/parser": "^5.45.0", "@typescript-eslint/eslint-plugin": "^5.45.0"}, "dist": {"shasum": "b0b41907401bce4476924093c7fb794bfadab045", "tarball": "https://registry.npmjs.org/tuf-js/-/tuf-js-0.0.1-alpha.2.tgz", "fileCount": 55, "integrity": "sha512-ggqn9SLSHJdKKk6jUiLYb2Kgo/LglzkEj8GpLqr92jpb0dGh0kktShnRHeWzdSyr78SxjMbcJx1o+N/OHfXAOw==", "signatures": [{"sig": "MEUCIQD97aGdHfHe000/cPw7x+hgIguxkQGL1uAAAH/OBmPUfwIgF2d7gRHKKKwi7ufyKz44oNIvh3Nrka7z/qRcbMRKD04=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97681, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkk2GACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr6NBAAkafP1pF/sIT5b1ja2a2s58Qk4+fk/LLMQTzcgJ3bBMDzwlpM\r\n8PP55HhBrTyZRoO+rCrcZjR2YNEH2w8KCXJaUVD8o6/RRnKYf/ao9o6q0U0B\r\nmzJK7I4fB1Gw19jmM+AhVGw7Wh5IxldG5EgqL3sxoaIYSMJZrYmOyvnwPiU6\r\nWv5I6yLLATkMd1OyQsdjS7u9lw7freIwra6LWqcp9xs0dnLGxbNRItBO+5FL\r\nhd0xUojcrxb4e7a19qExvEVOZQaJxtWsxy1AmKItQ4qbgkPo0eDG9pgp8M+H\r\n3BNRDw8fJqY00oGYG8H44uM4Bi3P+jQIuORwTqqxeP9myE802wGd4JcsOlaD\r\nbMPKJK9f6IqYV0qeT29eHwI7vif+WXIDAoegd/7vWOTyaviEVw+AHqiuk64f\r\nJekfwe1rGjcBuPQu4x6ckPZWWYIDChuFPkbGlKW40SLFBEUCd7OjVmEX12P3\r\nt40HjClcGzuZJX6yCnAmTrJdc146NC8pWq5Og8mgH/kxIRRvV//unTXvI1Jx\r\nfaAxuq3uzz4GjYlBClKLzq76wPLz5tJIztsbARoRkV6pNFw3giwhXA7jqOxW\r\nA2TtJpiDPmzmj0cVoTYW4jqqbpaMBIB6hBPDdF6jE0lWmh4BFq8i/7+jan8t\r\naUIwWcz9R1pJ9j2PV6DVyI7bDGnfNecQNnQ=\r\n=YQhI\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.1-alpha.3": {"name": "tuf-js", "version": "0.0.1-alpha.3", "dependencies": {"minimatch": "^5.1.1", "make-fetch-happen": "^11.0.1"}, "devDependencies": {"jest": "^28.1.3", "nock": "^13.2.9", "eslint": "^8.28.0", "ts-jest": "^28.0.8", "prettier": "^2.8.0", "typescript": "^4.9.3", "@types/jest": "^28.1.8", "@types/node": "^18.11.10", "http-server": "^14.1.1", "@tsconfig/node12": "^1.0.11", "@types/minimatch": "^5.1.2", "@types/lodash.isequal": "^4.5.6", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "@types/make-fetch-happen": "^10.0.1", "@typescript-eslint/parser": "^5.45.0", "@typescript-eslint/eslint-plugin": "^5.45.0"}, "dist": {"shasum": "1eab49e6465ff61e8604b42c668fa1c81169ede6", "tarball": "https://registry.npmjs.org/tuf-js/-/tuf-js-0.0.1-alpha.3.tgz", "fileCount": 55, "integrity": "sha512-6A9kseGjrFia/XD3gf7INh+hXonConAHoqDB7k2/sNX/yhlx2xotRIg0O86dxXDLpBEJCazVrXERlgdLEff0PA==", "signatures": [{"sig": "MEUCIEUY4T9WZHHf0XV/tbldTbhZzkwT1mLc10K144t7eOqbAiEArZAZCGZByJWUpv8FS1k+t1NykMhIULBo8X2n51/fO+Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tuf-js@0.0.1-alpha.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 97681, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkk8SACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrI3RAAhdBlOPm+yfdM8Gp5ZHXh/mrkJvl913RIqMtCVHwqy2TGuK/v\r\no2/Jn+dT9umfTuuR1SSOscHZN6l88VtORVX7p+6is5ZPt4uDNfNUm09W6p8O\r\nRVuEAsWg32ikJRqnIKSXkuDO6EYvLZflYMqESZVLzQvVyLtWVbMMIL1lhImH\r\nTft5rhyWLd9NS4EZZOnAKs66gAL3eg5lnDGBzOCNRQze1JA/26fFjWGrg20X\r\natsnTOlGdXPGqEyB3qb4KntJ4QQGgFW3dFt+c6LvHdJNdoCJIVAIm5x3GTIs\r\nPEMcx6Rq9RQehTZKYFfg8e5lzG7m6PZmlY1ogFXuNWd2tfkyVbps3K2pBpRS\r\nyY0OJC0IzrHZ0sP7/8xEryO2TbUQV8SqchR6zZb+DtzBnwqfHI2+CcfZTGuj\r\nrh69jA/QDZiYJVWXC7jMNys8de5wId990fLZtgK/c6j4F5vetCafVTeY4fAj\r\nWM3Txu7yphc03wiSHcexOAhvs9CtgMZq7w0iZAuvAAnIoLUrInkMzleXRV4t\r\ncMncifa7tlRLHQH2ZHQAUEb1iwrtR09V0BsmI8EAjQWrvMihJ/8XGIbaPCm5\r\nmDj1oY66STIX2WqP4DwsbzKW7z39HZ/QkOX9MfcSNbuTQsO0xUCPZsofsQBs\r\nUni4Ys2kpuopF8yBZfDMTT8hgyRTJwW9Tus=\r\n=IkVh\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0": {"name": "tuf-js", "version": "0.1.0", "dependencies": {"minimatch": "^6.1.0", "make-fetch-happen": "^11.0.1"}, "devDependencies": {"jest": "^28.1.3", "nock": "^13.2.9", "eslint": "^8.28.0", "ts-jest": "^28.0.8", "prettier": "^2.8.0", "typescript": "^4.9.3", "@types/jest": "^28.1.8", "@types/node": "^18.11.10", "http-server": "^14.1.1", "@tsconfig/node14": "^1.0.3", "@types/minimatch": "^5.1.2", "@types/lodash.isequal": "^4.5.6", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "@types/make-fetch-happen": "^10.0.1", "@typescript-eslint/parser": "^5.45.0", "@typescript-eslint/eslint-plugin": "^5.45.0"}, "dist": {"shasum": "a82e44a09f01286f0d0ab192f1a03cea0a32c568", "tarball": "https://registry.npmjs.org/tuf-js/-/tuf-js-0.1.0.tgz", "fileCount": 55, "integrity": "sha512-C6tZQRfOoRPja7tflK2dRjkxGZFG9DnpSqSIwfYfBB8L6izYVQ3k5VhIodmoDdgbyJdg8l/Gh5fgs+vPMdOXKw==", "signatures": [{"sig": "MEYCIQC2s0dIVQNZuuL41aDcQHCYtnLVPeC1c7fw/y0nanxOWAIhAPazd6YX20nxGMv9rCucqmoWb4z4sRjSa4G3oQ1EHQgJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 116231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjyECwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrcWw//YyrztTABozBfTJzuTq+cWNR/+YfvveNxo+dzD5zx6V1PKlAf\r\nltLmQ2ra6bGgXYhX2S7qhoAJ65TozN65TJ9d4QNKrzZnaVgiUSWIpfdsBzYg\r\ng/rasyOzHwVTIbJ3TXw1tFikLtlJnA48gayijKJDF7zVsqk7zkNPNFGCU8so\r\nMfrB23gHjFZktG2tBE2Dx/BM8B7LUYnIOj/XNrDuyqidd12dmrSstYsczsRw\r\nsCZ9oLpYANT/qghUXFTQPcADiqG0pONJ0m43n/I2Q/n8YQemaj3/BfmizLlr\r\nVw0pBHTS3VgTR0oAige18vQzI81U5gaTHYMfs+M4+a+F1M76w8cKSGikxFV4\r\n8HbAcDf9zic32mOrpbThsB0e2nHbGI2jz7WpsQpZ7/N7ord6DzcGvE9IokCv\r\n+SwMjOgkyF74dVYTWNaprXwOZNfkvnNlHFVTDBcGOuNPOp4l9HmZSTlxO44i\r\nfBJkp26hLWke+1JANl7dWWQrwDVkDoKyuQMQOL1CyrBRwOQoXzQcvp5OyZjs\r\n6J/cYSeEo/sgHhwgBm6u6rReyrkxZ47ROQYRG+H4lySFIzv+c7u7dxzSr2H8\r\nQVguKNbjgW/tcq605gJ3RBLsvKOXrmdblygY3jw9C2NPssnWZzsCRXevxYlx\r\nc8d3JMQMaQvcL5NASjsdGnuteMLQE7KWpQQ=\r\n=ZUy9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "0.2.0": {"name": "tuf-js", "version": "0.2.0", "dependencies": {"minimatch": "^6.1.0", "make-fetch-happen": "^11.0.1"}, "devDependencies": {"jest": "^28.1.3", "nock": "^13.2.9", "eslint": "^8.28.0", "ts-jest": "^28.0.8", "prettier": "^2.8.0", "typescript": "^4.9.3", "@types/jest": "^28.1.8", "@types/node": "^18.11.10", "http-server": "^14.1.1", "@tsconfig/node14": "^1.0.3", "@types/minimatch": "^5.1.2", "@types/lodash.isequal": "^4.5.6", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "@types/make-fetch-happen": "^10.0.1", "@typescript-eslint/parser": "^5.45.0", "@typescript-eslint/eslint-plugin": "^5.45.0"}, "dist": {"shasum": "af22f452d57c9ab74d6028c080269f96c9fb0247", "tarball": "https://registry.npmjs.org/tuf-js/-/tuf-js-0.2.0.tgz", "fileCount": 55, "integrity": "sha512-IeI78DtMA0LKiN6fFbC2V+wuvQkRcUIFvMVIytqUoWpsYG1W6Y773DBlnuca+CVjVCHRimyvItPODD8grfxtwg==", "signatures": [{"sig": "MEUCIQCGiUv804VXLSoT03k1yZ1IZ0q7a6sU9IxDrXFoGLl27AIgAfyfaTYYLdCzuvJ+hl5NG2UT3GpXB1kTPwhGBVNSUoo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 116616, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj1D7eACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoL4g/9EgMepqNogpVCYlIYn4BYek0jfAT1/K7ua59P/T3Tza0sVatW\r\nlD8uxCv3bTZsrWT627ZCzzUJ4kkg+CKB1zQhgc/LaP5HcYPwZjs5CIZwSTc5\r\n/W8A3j8ODCYp4CHtgLpJ4AmlLaX5WXZ3GIPJjNlYmS5FOtUfp8q0r1FBUpUV\r\nKMeZ+M1SG1X63c0/GblmgROr4uvNmK/sOCVPBiaUO4KZcpFd01Bcmw10r7SD\r\nDqpn9jE5B6nj6PzReuzIvOtyOvHJbz2fh3ETroH+zZ5Wffdr7kM56mw+mM1j\r\nSQKaoCT0dzxfgDHX6SVmBbeBuUS/rWS4y+XGPPU+kXNjk1KwFuRs0PexGi0G\r\neT2dW4zM0NGutudRpW98jhRUvOJWfIC2bdPRiAPz7awPzSXYnx96E13VPijN\r\nSrUgWfPXn0cy6Fo4y0+Txpr9jGCtKAkvQ+e/pmuPokh4K+aDXdHZi6s1PeLY\r\nvq6Ru4bXMevXfaNBZoDXAb3mU9oPnZ+ndww/OUsUpklOEtTyHCyB79IsOxrC\r\nCQ/SMC5bPMCLS1f9uNNyiqBAy2wVj+wTHcjEUlzbznpX9mRB+5SEIczNvXLw\r\nkO0GrgWdrpcfDJqea49PSq/oaVGhrGCQrTqDt9/DNCYh/hv8pxgdC8PKcJAi\r\nrP/h6VQa3CXgeN1lABX9AyoonyTsOxxBgzA=\r\n=PXQy\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "1.0.0-beta.1": {"name": "tuf-js", "version": "1.0.0-beta.1", "dependencies": {"minimatch": "^6.1.0", "make-fetch-happen": "^11.0.1"}, "devDependencies": {"jest": "^28.1.3", "nock": "^13.2.9", "eslint": "^8.28.0", "ts-jest": "^28.0.8", "prettier": "^2.8.0", "typescript": "^4.9.3", "@types/jest": "^28.1.8", "@types/node": "^18.11.10", "http-server": "^14.1.1", "@tsconfig/node14": "^1.0.3", "@types/minimatch": "^5.1.2", "@types/lodash.isequal": "^4.5.6", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "@types/make-fetch-happen": "^10.0.1", "@typescript-eslint/parser": "^5.45.0", "@typescript-eslint/eslint-plugin": "^5.45.0"}, "dist": {"shasum": "f64b883095682e3306706b863a05c898f36d14b5", "tarball": "https://registry.npmjs.org/tuf-js/-/tuf-js-1.0.0-beta.1.tgz", "fileCount": 55, "integrity": "sha512-eSvLn1fNHBeJLtT6DmFlQizASMSxIq3pPUxXq/MdbY8cdT6cr5AlJoYiLCAnGhHs3cNQdH6KnRyMxQ1fVoo70w==", "signatures": [{"sig": "MEUCIQD+lOK6XXHN2ptWOijqof8ulOl21kIUv66XjbrS+GgbHAIgLhkz006e5YDbp8Jy7YX2rdJRmVGVUUyj2DJPyHFG/5Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 117006, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4YUVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr13RAAhduGnXKuFJ4SSWQ/t9CbeP3bvM+Dw4pEGj3k+K590m0gHjAp\r\ntOjgCUPiJ8J173hd1JsG8fW56NYy4F5Fuk7vP8gZtCzKf1Pg1/kRmhzJGvZ2\r\nDh3unPKmtZt0q/LHYPhTRO5BOsSAeWUr5NT+wvg2ASbHMLYk57EvSvVPDzW0\r\nJDrV+5TyLHby/7a6TEq24Fut5jrYi0kgLAlzKTPQcZ7rpBPFSx4rd3PWnfT0\r\nSLjf52bpj6e1Ju/HQIh8KmUaduWl424OFpAP8xqth2J3yhmkzRRNOW+9kDWz\r\nhp4qUh41idSSm8CFwYFPPwt+br8YZJmj/E6N4VUkbJ/T52UAyN9ZzEbGJ0N1\r\nitGQkvXU7x4YGApSgcF53039IJuQ/fDjIVvvAf86xyg6xg1dBX4HJZ25vRr2\r\nCAw/hv+InhxcfLi3ASwmIv1OnRDX9b4JStEOnuLcdQrJbSsU2wwrzzrT2HAQ\r\nuIZIx5NEH5iWe56HoD17aaGVFK+ABpxjo9gMQJhvssrkmh0FejHhA8nuzfu5\r\n1TUAgKR9C8JP3jfQi7NkIZVU2iJa2OWluDCM4Eijr1PKXBKu7Na5Ag/xTA5O\r\nn6NpYCsJg9I4L7+fdvSi/AaX7lzBynapBup6igqt1k6pM+hYaqvohZEFrRW7\r\nEC1oKaMhtWtTRmmh2G344SviMWF8v9JZSwg=\r\n=orhY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "1.0.0": {"name": "tuf-js", "version": "1.0.0", "dependencies": {"minimatch": "^6.1.0", "make-fetch-happen": "^11.0.1"}, "devDependencies": {"jest": "^28.1.3", "nock": "^13.2.9", "eslint": "^8.28.0", "ts-jest": "^28.0.8", "prettier": "^2.8.0", "typescript": "^4.9.3", "@types/jest": "^28.1.8", "@types/node": "^18.11.10", "http-server": "^14.1.1", "@tsconfig/node14": "^1.0.3", "@types/minimatch": "^5.1.2", "@types/lodash.isequal": "^4.5.6", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "@types/make-fetch-happen": "^10.0.1", "@typescript-eslint/parser": "^5.45.0", "@typescript-eslint/eslint-plugin": "^5.45.0"}, "dist": {"shasum": "c89daaf69b348081a86e9f766151dfd7ce0f5078", "tarball": "https://registry.npmjs.org/tuf-js/-/tuf-js-1.0.0.tgz", "fileCount": 55, "integrity": "sha512-1dxsQwESDzACJjTdYHQ4wJ1f/of7jALWKfJEHSBWUQB/5UTJUx9SW6GHXp4mZ1KvdBRJCpGjssoPFGi4hvw8/A==", "signatures": [{"sig": "MEYCIQDgGQeY2QLkLuoO9YxOqFZ+a6zYuaZpXhc77kUfdCUXDQIhAJp/vV+9Xg1bfM5YlTvKIH9agUEOu5T76+tQaHY2vZyO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tuf-js@1.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 116933, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5S+SACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqMjxAApAaXqLBlFOyrbaIvqF9qap5kuK/GW1H379jS3hhiL14+vlGQ\r\nBpSnPFvf6b9OPPxvjKf4lPJTjFGM9lFm79sfIlZKlPSFPZv9yONgejxWJyGT\r\nRBy4AvHZKPTjt1jOAC/gpfy+2WZD2YhG3KUvCu6rrUXqluCPH3cHT8RgIDyy\r\nRwvyyJLGf9kFCi06LFIYLkWfhnPRTK0KG5iNDTqeB71nlByY5ciDbzr/a7nV\r\nYeAij+TvYqIRWGimCsxxwkcSNbZhQn2mo1+C+eAidPaTiAW+dFmj6sC56ocs\r\n5emA45dJGItVASZPvfWH8ogqlkqKnn5i1pjKDUFoo20DAJ/bgKDdlfNSwLZ/\r\njfCBXGbKpFE4A15hUkAmr3XokMjAM1pi/qh8aAlC9LX55eHVzoT7RUzpXJNz\r\nWvaW1UsFUvH3NtpekqwlkgcsBqw5qOrlgnoXlfwtY+joXIFE9XPIlWfgGu/z\r\nSjIFiy1h7Y6yBjCjTXWFQGOYnZXJlYzKjmrKbxW2dQ4x0UgVA8FOygy0SthL\r\ntv86da8Z3wVLjsphCCvWdkw/5akKyUUhhKaT/uDHLsffJJQJs5qHkifnQlfj\r\n0LOOTIffYomzSWH7RquU4AsgQzzlMr2VdhFmqR0ILw+QM6VxQ/o6Wtt81Xdx\r\nrl4MW2aAjy7tqs2stXE7w49IOTesEXnh/ps=\r\n=iHMA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "1.1.0": {"name": "tuf-js", "version": "1.1.0", "dependencies": {"@tufjs/models": "1.0.0", "make-fetch-happen": "^11.0.1"}, "devDependencies": {"nock": "^13.2.9", "typescript": "^4.9.5", "@types/node": "^18.14.1", "@tufjs/repo-mock": "1.0.0", "@types/make-fetch-happen": "^10.0.1"}, "dist": {"shasum": "25dd680bce9e3819e1fe4640d85186f862efb827", "tarball": "https://registry.npmjs.org/tuf-js/-/tuf-js-1.1.0.tgz", "fileCount": 17, "integrity": "sha512-Tsqlm419OAlrkCE6rsf1WuPvww44vfK1ZHz+Uq9Mpq5JiV5qnJ9LLItvsbM9OipIIeSG3rydVBS4BmD40ts2uA==", "signatures": [{"sig": "MEUCIQCM21HlGgLsoc8u5FbIHPfjFk4hiOCoYezPszKYC5NlTwIgUrNlKc1IyWvZszVjWomqxJ8CvYr4d+ofsXopUFhhPfk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tuf-js@1.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 37561, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkARf+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrBEw/+KhlbAEjKXSu8blMlgmQO0tAs1onZXzMgwHTJwcEnupzYq6CG\r\nkZukfBHOj0RlMSzGAgwGifdYlYsSfd/VBEjE8Ox9359TluzLbQ1qo5vIRRud\r\nTX8qkjy3lSN9FBo7fwmuvwWhLPR6UwJk/o/gQbciPSs+xBfXcnzyoNSBCYfa\r\np8SQjhlVaRReP5FOCYwNws2fsCVMnM6PcSJbl/7rPyc2va5CW2/F4A1xpDot\r\ndHSyI2+PCVhVIWyi5+FhRwiFUPZCZJfTZ+C5etbvDM6UbrGClXlUUu/V2DVo\r\nB+09Vost1c+0wtMahEt/7Hsi9ydxcUN21KOXnTVlSCSX+hb12ZQdWywBnJhU\r\nfyJ1u7xX2pChg0kqnd4XIIW8ZgXG42l3TmwjTOn8mCEBYOS8Y6scsTyXbkar\r\nDQjMo8ei2NEP85vpazSiQya9e6W7x/nSIlp1is5w41GdooXlIM+LBJnfvfV1\r\nji/xHHYXGcaY4QwyMgGFFwXZFFzorgDU6AkCf8G3f1ocJT/W+qap3jlpIF7+\r\nQJdhu+dDTWdIzkXtMdkUnsmT1RlQWJSI9HX33sfUAarCyn8hU18l+PNyxzcF\r\nX4K7Kl6eMvvoKwVlNM/+3uj55G0St8rBh0GN0DIoo4GCL3aoNQKj8X3btK/c\r\nYGNh+L8OiUITHuySU8+SyU7wi+sEH2fXTCo=\r\n=rYkK\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "1.1.1": {"name": "tuf-js", "version": "1.1.1", "dependencies": {"@tufjs/models": "1.0.0", "make-fetch-happen": "^11.0.1"}, "devDependencies": {"nock": "^13.2.9", "typescript": "^4.9.5", "@types/node": "^18.14.5", "@tufjs/repo-mock": "1.0.0", "@types/make-fetch-happen": "^10.0.1"}, "dist": {"shasum": "54d3aef2b57a19100789cb72e6295b0fbe583935", "tarball": "https://registry.npmjs.org/tuf-js/-/tuf-js-1.1.1.tgz", "fileCount": 17, "integrity": "sha512-WTp382/PR96k0dI4GD5RdiRhgOU0rAC7+lnoih/5pZg3cyb3aNMqDozleEEWwyfT3+FOg7Qz9JU3n6A44tLSHw==", "signatures": [{"sig": "MEYCIQDnmkt+j/RXLnDQiMY5veLd9IKI2YDLxAtEPqSK4ddYJgIhAKGygDSKJS1rFq5MO4Ea9eBbSdvfB6LdxL6O5my4QjFs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tuf-js@1.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 37785, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkAlT2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq82w//W/5vGvfGEVF+lV1L9xqnlIBO4JwoGdqe66x2WrPPRsM49Dtk\r\n+7cxpvhlGogWG9e3G3CFkoduAOhxR1Oanp0I9hsW/CB/n8JZFw/+uxeIk/5a\r\nzYl5Q5MoTj8gz1Md5ACXaD2WpTzw7oGEi3mAUqcWT5a3X11UHZYBrOX3oYMq\r\nWN29dvYs2YKTrAXs/dZeq6XkL/+03XtGzx9J75/fGj5kn3+Y6zJXG1qXkiOz\r\nr622zcuLAaId3K6Rkc0ckqqjZFknNtnFSoHgC9ZSO9zf0c1F5GSFiSi0K/QT\r\njjKcVqEXBGZfcyVNKYlsOzR7e806qyJH3oGwS8VWbNppUVKjjCrJo9X0nu9d\r\n0x7WK9JTpTv9hp/O5bdPegNjs8JIcxBmGwd0lxmlJf7ONBC9l0opIqCrWQYw\r\nvxV74JY/Nv/hn/2q9B24bLM0RW3sR+mMCxiNP0a87THs8JVGXFjeN8YGcBn6\r\n9fsq5EbIh7KCKDBmmEcpJB5uieqT+ppBa0XWB5OqT0MFVyI5oT3CkwZMVhev\r\nEckP6J2ZLBTBTxtFbDwuw2ETMXIPYzAEMv32hC8Uz6RVS96oPe0p2KIjR4yD\r\nmzcoRybZj1BSB9bzQ3vWFEwvXJe+0iaPR6EaZA67huN5XUz1fOr1doZpIxb6\r\nbp1eaNzZkK6UCVPlEvnufHQ2mCXqa5WTMj0=\r\n=nN7F\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "1.1.2": {"name": "tuf-js", "version": "1.1.2", "dependencies": {"@tufjs/models": "1.0.1", "make-fetch-happen": "^11.0.1"}, "devDependencies": {"nock": "^13.2.9", "typescript": "^4.9.5", "@types/node": "^18.15.3", "@tufjs/repo-mock": "1.0.1", "@types/make-fetch-happen": "^10.0.1"}, "dist": {"shasum": "09ca04a89783b739e67dd796f6562e3940628aea", "tarball": "https://registry.npmjs.org/tuf-js/-/tuf-js-1.1.2.tgz", "fileCount": 17, "integrity": "sha512-gBfbnS6khluxjvoFCpRV0fhWT265xNfpiNXOcBX0Ze6HGbPhe93UG5V5DdKcgm/aXsMadnY76l/h6j63GmJS5g==", "signatures": [{"sig": "MEYCIQDD84t9d2si7rX/EQATTGsAiVaAuZqO/LbQZ7DsS5ggegIhAMh9yljPf8hXKveE47Bnp3GxTj4aceoM+41nzxvxwPd3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tuf-js@1.1.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 37795, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkGhf1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoVOA//WoLd0y3kgFsNQjynF+iO10fCFJpzB9ViHN++gmvKzO5OZ60b\r\nymRWb5/abhtRa0JU/Tp5TUkAPAR38BfLFMgtk6oZ8lX/I6BvvKyMKSN6PH/e\r\niUYy6Yue25s0h4krMZRWw+QwVI08VAiapHO1Gks4KC1u73Im6p/xXKAqPfvz\r\nAFOMd/4ht/+Bup1LX2of0v8JfF+n2oupXoaSZyHua27LB+nNdmMkp9hZh3Rd\r\nSmq1veNdnnkffAajkB2sakdQiGROkv3WkEMP7TFs81HoGI8xzAM6lesgpjae\r\n8n4qqYSefDqPaBTOMDhjWy0ULjrVdAuJhVssHTAbrUsJG75npTQPZLFRh7dW\r\n0c+c0q+ahBorlEyBeKHAq2qHtBnxjW4ei2a16P8EEsKUoEoQA1LbhMdfZpEw\r\nWxv4/e/cjA4Y9gbaGU3hpZN1UGJm2gk8hz+aN1bVS6/mFMpUkDiT79E1OTIG\r\n1je9jvZ6FHPTalfO39c+/YfoKliwK++U1P/8dpdyupbyhNB2UhyJxwGvswcV\r\nmWLoSkf9dF+XSnaKMnnfI2/0UOMvKZbPz9w83UaSxov0EUapvz+oY4QfI6Q6\r\nNLxQULbOgKFSSTs9RRLwT22ZN3b4zijT3TuP3gJZLUD8ZFvWYessQzdVBlPI\r\n8lrXJbk/cYUuBaeRoTfOwnsLsmW6qQlXkFk=\r\n=nom9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "1.1.3": {"name": "tuf-js", "version": "1.1.3", "dependencies": {"@tufjs/models": "1.0.2", "make-fetch-happen": "^11.0.1"}, "devDependencies": {"nock": "^13.2.9", "typescript": "^5.0.4", "@types/node": "^18.15.11", "@tufjs/repo-mock": "1.0.2", "@types/make-fetch-happen": "^10.0.1"}, "dist": {"shasum": "db3aada70fbf91fd9def9ad255645eaf81309f69", "tarball": "https://registry.npmjs.org/tuf-js/-/tuf-js-1.1.3.tgz", "fileCount": 19, "integrity": "sha512-jGYi5nG/kqgfTFQSdoN6PW9eIn+XRZqdXku+fSwNk6UpWIsWaV7pzAqPgFr85edOPhoyJDyBqCS+DCnHroMvrw==", "signatures": [{"sig": "MEUCIFrczdFJzBEyLYe0StbZAtlx4CfKH1l9PVacnaO4FyRDAiEA491upq0euwsKpkZFQDIj83jkKoSrX1dH9SVTJxVPfZo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tuf-js@1.1.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 38588, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkNbwKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmowBRAAhijfs5vB55lXipCc3X4OAWdtm67A1yfRJtxb1qrMX58kgrq9\r\nwMiasSh+joL7gZdirZgLsJXuVNJk/RVlTB/1+cGTzepxzsUnMfm3LbCfMHTu\r\n9Gyd5QLsL2hbYeHOECcZjmj/hzJX+unXCoQVHr5WWoDVkPxPC8YRdyIE0XgE\r\nK/74EWZF41wU7u7Y3ECPUtSEln9+IZBRa9kZSsaQdgu4jhs3BzvABHuCTWge\r\n32zQoiQsDy3qykTEki2Q6Dxp7Z+W2jK+4oJTNGGttfwg+WuJEELbSXy9PSLP\r\nGP9stpW3ySvd2084W94fCn7YlO8wv+w+SuTnYRTf1xMHLDGCFfYnrXJh9/we\r\njdd+B65XvnVMrTpeH4ssrFiVs/cNbDL+B2N5CGLoWxJACO0GvBWhLJIZASPN\r\n+AZCVbti8rz11ZZAMNL6Ti8IPs77iNaesjUvgWKL0db8MwCDfU/+dmNcHZEp\r\n1dmbxeQsLSpaJNcHlqCxzKfCJuOLpkTFUoejtYMGSbmYF1cSwFdR9oCrGxik\r\nxM3g2rWSp2auQU3HCYOAj/CI48HTAnQJlqo1WrHtaWOYM2xCqzkKW/fqbKUq\r\nJaUai9F6Q6JHpsZiB4EbPphmQK+9ls5FcZkNGtVAq+5AogUXUwNS0RJWN8gL\r\noITeObQU9ucjFKNydsHgt7FJ9j0/PKwjkuc=\r\n=RAae\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "1.1.4": {"name": "tuf-js", "version": "1.1.4", "dependencies": {"@tufjs/models": "1.0.3", "make-fetch-happen": "^11.0.1"}, "devDependencies": {"nock": "^13.2.9", "typescript": "^5.0.4", "@types/node": "^18.15.11", "@tufjs/repo-mock": "1.1.1", "@types/make-fetch-happen": "^10.0.1"}, "dist": {"shasum": "e85a936b16859c7fae23e5f040bc0f7b559b3192", "tarball": "https://registry.npmjs.org/tuf-js/-/tuf-js-1.1.4.tgz", "fileCount": 19, "integrity": "sha512-Lw2JRM3HTYhEtQJM2Th3aNCPbnXirtWMl065BawwmM2pX6XStH/ZO9e8T2hh0zk/HUa+1i6j+Lv6eDitKTau6A==", "signatures": [{"sig": "MEUCIQDHfoHYmCrgQvrppUzE48Ymf2y8MfXDZWuOM7hM7xmTawIgUwvn5YRCft9903sBqAJ1bXO5KzpOH9mKnKn71vJD73w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tuf-js@1.1.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 38588, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPuhNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrSaQ/9EEhNFxPdZM9cYf9sjzCOny42xF9MorXcKoA9PW7ium0rH2/Q\r\nzVTsF00USVLGeuddIXJAlz600AXvLJvKNspZbrcVYGHl/fmcPAGivHsJRbcC\r\nmJQQalCelOX5G120laj0STNmPG5A6vi4kizpibyB3/pDjh6Bm+xdIbDAsRxb\r\n0N+d0+N5bh2SDoeuL0EZ60pkvr1GSBtSNvB919tYb/gOiO90+FQrX2RbNaJg\r\nwbR6e9CUhhCZ6hYAzCApTmqHrrEUXe0jSSLkM6sO/d9/LEUN/BxSJK6fErlr\r\n5P6qRfxVPtfXxF7zY39YNRsWmfl3N8gnCN2+IJTz0aetqs1vFOO1DPOj7Wfg\r\nmHgeOvJHPAGy7D358bQdIec66V0FM38hB7WctmwZg0m13+mVLNk3cCRDyLUW\r\npmqG2zc79rXcmdlH0lfKZFDyjsFWk0ApJ3s3eJc0Xiw3qYM0vfanpLblsq9J\r\ngN6IbyeSE2l+pG2G4Xbnj0kuJSgumcHaj4WurrF5iYBgaUYQB3rZeHGqJTiX\r\nc0VzGCI2+ZK13XlbhITdpmhM8jtzYYJ/cei8mq+fhv/MPxK+Gz0l3C78OaPP\r\n37msJzF79iNtyfc6FP1yPS+sVEGntRC/CquF6dotojAPsFj26B1FP8iD/YMf\r\nKxlXxx99tURJ3aVNRe0b+gW3ZhXwU10hcxQ=\r\n=nAbG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "1.1.5": {"name": "tuf-js", "version": "1.1.5", "dependencies": {"@tufjs/models": "1.0.4", "make-fetch-happen": "^11.1.0"}, "devDependencies": {"nock": "^13.3.1", "typescript": "^5.0.4", "@types/node": "^18.16.3", "@tufjs/repo-mock": "1.3.1", "@types/make-fetch-happen": "^10.0.1"}, "dist": {"shasum": "ad82a18c5db42f142d2d2e15d6d25655e30c03c3", "tarball": "https://registry.npmjs.org/tuf-js/-/tuf-js-1.1.5.tgz", "fileCount": 19, "integrity": "sha512-inqodgxdsmuxrtQVbu6tPNgRKWD1Boy3VB6GO7KczJZpAHiTukwhSzXUSzvDcw5pE2Jo8ua+e1ykpHv7VdPVlQ==", "signatures": [{"sig": "MEYCIQD7W3GAC8qykHW2UuJtP+ncH1tNQnr1iKF2HUgMYAVe6wIhALB6FekeSkDByWZ8//9MRLWDZuRBmv3ogTYiikri1ydl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tuf-js@1.1.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 38587, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkUp3/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq1hRAAojDfK3JMKslRZPtqxHSh2VWJsc2acy1d8f365mE4+VaU9Ibt\r\nI5L1Z2+Jqyv+pRVyUoj89IR8sFpxIDD210IAIhbFEc0HDV9it/nu6/RXIb4q\r\n2LHHRfPNoxuVQlFROxutjXIQ1CS7cFz4Ggs9bT/qTpXtMPvurcUOcr6OIiU9\r\n9RscF96nGXkHLn8bZp9LDIqYCcr8xL9XNZ+Q7+vCbBLHGlUoST+/EVNdHKFj\r\npkwCuj1MTWRwD7TAmk9hSa1crzecX6jOhBEzv8Ae/D1QLwEVnksCOM4DWHzd\r\n9apXl+HInveieP5FVGqVqZFJWjeJSJuLE6kABNwttRNO7MMSmBvmh7PNztF3\r\nYMz/7qQ+jEGZzCJwOKH4rQ6VdL6EjLK5DG51S/6daR0l50AcQ5plZAf9zike\r\nItVCvNDvKqvXjPrbZqwRQhk0WtETDzVKI0y0TpOYAGPOA/ysHJFT0Kgx7kio\r\nm0nm2z1xUx6Q+2Jho6bd2djrSk88Uh3Xuuo5PcMhbQKJLLo9zQPVwqgjzNKS\r\n72CRz0VORCW/8q2JPbCA4ZqDPK5SykOt3oBbR43WiMjZPvR9+Vjtt/2PPy2y\r\nj6qzQPc5oG4MIpfA8b58IFDneZo1yIkDSP0cC/72T5mluGPeYoXdJKdz6r/M\r\nAsZl6ee9kugkm4ai+TwgBJqeffjx5ak/Y5U=\r\n=v18M\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "1.1.6": {"name": "tuf-js", "version": "1.1.6", "dependencies": {"debug": "^4.3.4", "@tufjs/models": "1.0.4", "make-fetch-happen": "^11.1.0"}, "devDependencies": {"nock": "^13.3.1", "typescript": "^5.0.4", "@types/node": "^20.1.1", "@types/debug": "^4.1.7", "@tufjs/repo-mock": "1.3.1", "@types/make-fetch-happen": "^10.0.1"}, "dist": {"shasum": "ad3e7a20237b83b51c2a8f9d1ddf093279a10fc2", "tarball": "https://registry.npmjs.org/tuf-js/-/tuf-js-1.1.6.tgz", "fileCount": 19, "integrity": "sha512-CXwFVIsXGbVY4vFiWF7TJKWmlKJAT8TWkH4RmiohJRcDJInix++F0dznDmoVbtJNzZ8yLprKUG4YrDIhv3nBMg==", "signatures": [{"sig": "MEUCIH0AsPmGq+8dT717cECkPXtawPXPN1jbGjMUlnTRvKqLAiEAh4HBKIDwxBB0hhMKtxth2SRCC4J0IWIn8aWSRV4PdP0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tuf-js@1.1.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 39116}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "1.1.7": {"name": "tuf-js", "version": "1.1.7", "dependencies": {"debug": "^4.3.4", "@tufjs/models": "1.0.4", "make-fetch-happen": "^11.1.1"}, "devDependencies": {"nock": "^13.3.1", "typescript": "^5.1.3", "@types/node": "^20.2.5", "@types/debug": "^4.1.8", "@tufjs/repo-mock": "1.3.1", "@types/make-fetch-happen": "^10.0.1"}, "dist": {"shasum": "21b7ae92a9373015be77dfe0cb282a80ec3bbe43", "tarball": "https://registry.npmjs.org/tuf-js/-/tuf-js-1.1.7.tgz", "fileCount": 19, "integrity": "sha512-i3P9Kgw3ytjELUfpuKVDNBJvk4u5bXL6gskv572mcevPbSKCV3zt3djhmlEQ65yERjIbOSncy7U4cQJaB1CBCg==", "signatures": [{"sig": "MEYCIQDtnec7OXbpnFLExgzfnZL37c6yahuBrvEskVbNwdeLaQIhAIEhkvzYwwbhqIrbi5xXssohLjxRRMdos6R6ueGmIMBH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tuf-js@1.1.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 39258}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "2.0.0": {"name": "tuf-js", "version": "2.0.0", "dependencies": {"debug": "^4.3.4", "@tufjs/models": "2.0.0", "make-fetch-happen": "^13.0.0"}, "devDependencies": {"@types/debug": "^4.1.8", "@tufjs/repo-mock": "2.0.0", "@types/make-fetch-happen": "^10.0.1"}, "dist": {"shasum": "65c782ee7f8a01de5ac03648d575ea530bd6738d", "tarball": "https://registry.npmjs.org/tuf-js/-/tuf-js-2.0.0.tgz", "fileCount": 19, "integrity": "sha512-Oq6w0MMFihvxCM0o733TIeLeuUrDuaVaOEUVXrQtq/J6YXoUmQU84JcAftJcDkxDkuTZ9jumZN7Dh7VlyNaeWA==", "signatures": [{"sig": "MEUCIEYlCkCecoIyuvXBrrFSxrBAqCIabG8WWQcRIO4psjGHAiEAs9HVVuJs0rv0fJhGqPgOkuA9P3RctrWsfx6rcRzFj5Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tuf-js@2.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 39422}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "2.1.0": {"name": "tuf-js", "version": "2.1.0", "dependencies": {"debug": "^4.3.4", "@tufjs/models": "2.0.0", "make-fetch-happen": "^13.0.0"}, "devDependencies": {"@types/debug": "^4.1.8", "@tufjs/repo-mock": "2.0.0", "@types/make-fetch-happen": "^10.0.1"}, "dist": {"shasum": "87aa36d5a166e7522f1e2050eb502a3a9b0bde72", "tarball": "https://registry.npmjs.org/tuf-js/-/tuf-js-2.1.0.tgz", "fileCount": 19, "integrity": "sha512-eD7YPPjVlMzdggrOeE8zwoegUaG/rt6Bt3jwoQPunRiNVzgcCE009UDFJKJjG+Gk9wFu6W/Vi+P5d/5QpdD9jA==", "signatures": [{"sig": "MEYCIQCVNiCvUTTU6JWnexsLLgpYzW4KdOzGFgwvkATp01gI4QIhAPqDk2LCbRPPwQD9a7LsYj1gKPHBDxpl+yzCJSaXw5Vd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tuf-js@2.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 39478}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "2.2.0": {"name": "tuf-js", "version": "2.2.0", "dependencies": {"debug": "^4.3.4", "@tufjs/models": "2.0.0", "make-fetch-happen": "^13.0.0"}, "devDependencies": {"@types/debug": "^4.1.12", "@tufjs/repo-mock": "2.0.0", "@types/make-fetch-happen": "^10.0.4"}, "dist": {"shasum": "4daaa8620ba7545501d04dfa933c98abbcc959b9", "tarball": "https://registry.npmjs.org/tuf-js/-/tuf-js-2.2.0.tgz", "fileCount": 19, "integrity": "sha512-ZSDngmP1z6zw+FIkIBjvOp/II/mIub/O7Pp12j1WNsiCpg5R5wAc//i555bBQsE44O94btLt0xM/Zr2LQjwdCg==", "signatures": [{"sig": "MEUCIQDZLlyNjEVrllV0Oslt0lsHkGABayRmm1bzZXAwLFGkvgIgDQuROsAQsddekLTzNGmPTmmV3Bmgvg5KWrDHztXKHGE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tuf-js@2.2.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 40562}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "2.2.1": {"name": "tuf-js", "version": "2.2.1", "dependencies": {"debug": "^4.3.4", "@tufjs/models": "2.0.1", "make-fetch-happen": "^13.0.1"}, "devDependencies": {"@types/debug": "^4.1.12", "@tufjs/repo-mock": "2.0.1", "@types/make-fetch-happen": "^10.0.4"}, "dist": {"shasum": "fdd8794b644af1a75c7aaa2b197ddffeb2911b56", "tarball": "https://registry.npmjs.org/tuf-js/-/tuf-js-2.2.1.tgz", "fileCount": 19, "integrity": "sha512-GwIJau9XaA8nLVbUXsN3IlFi7WmQ48gBUrl3FTkkL/XLu/POhBzfmX9hd33FNMX1qAsfl6ozO1iMmW9NC8YniA==", "signatures": [{"sig": "MEUCIB3WuXWNZTih3Ckkw7CzMaAxqP+kP1XkigEuwhKzgmHhAiEA47vnmqFlztdXeFTm5bxKMndoG7VtvlkOqPTXH97cfQ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tuf-js@2.2.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 40562}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "3.0.0": {"name": "tuf-js", "version": "3.0.0", "dependencies": {"debug": "^4.3.6", "@tufjs/models": "3.0.0", "make-fetch-happen": "^14.0.1"}, "devDependencies": {"@types/debug": "^4.1.12", "@tufjs/repo-mock": "2.0.2", "@types/make-fetch-happen": "^10.0.4"}, "dist": {"shasum": "8df087ca1692ca7b85cc97f29d5e56bd705a880f", "tarball": "https://registry.npmjs.org/tuf-js/-/tuf-js-3.0.0.tgz", "fileCount": 19, "integrity": "sha512-SSlvee6lutwG9bEoqK/6XDtRJ0tvzgx49CzLNLDKukdTzL+Oo7ZenTe1muTgE2O5OODzwzNaVNXbVCjIKn0e0Q==", "signatures": [{"sig": "MEYCIQD2JM1iiqyCV6crJcnfPS7dsQxwrQ8rXiYfn5aTo9bkewIhAIUqj83H6K41SNUpNCIchWVrLgariLQ4UUsCYEtcLxwt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tuf-js@3.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 40365}, "engines": {"node": "^18.17.0 || >=20.5.0"}}, "3.0.1": {"name": "tuf-js", "version": "3.0.1", "dependencies": {"@tufjs/models": "3.0.1", "debug": "^4.3.6", "make-fetch-happen": "^14.0.1"}, "devDependencies": {"@tufjs/repo-mock": "3.0.1", "@types/debug": "^4.1.12", "@types/make-fetch-happen": "^10.0.4"}, "dist": {"integrity": "sha512-+68OP1ZzSF84rTckf3FA95vJ1Zlx/uaXyiiKyPd1pA4rZNkpEvDAKmsu1xUSmbF/chCRYgZ6UZkDwC7PmzmAyA==", "shasum": "e3f07ed3d8e87afaa70607bd1ef801d5c1f57177", "tarball": "https://registry.npmjs.org/tuf-js/-/tuf-js-3.0.1.tgz", "fileCount": 19, "unpackedSize": 40803, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tuf-js@3.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF38tHKRq10KiCA+v+KI8Ba24HQVrk8xfq2EkSSm1xhOAiEAln+h2c7t8yCk745Bg9cwJMyX9v/dHVl0KZwXuyRnPg0="}]}, "engines": {"node": "^18.17.0 || >=20.5.0"}}}, "modified": "2024-10-14T15:37:35.984Z"}