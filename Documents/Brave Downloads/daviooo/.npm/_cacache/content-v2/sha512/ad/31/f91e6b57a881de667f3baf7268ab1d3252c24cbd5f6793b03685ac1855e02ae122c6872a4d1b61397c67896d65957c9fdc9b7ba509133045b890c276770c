{"source": 1086872, "name": "safe-eval", "dependency": "safe-eval", "title": "Sandbox Breakout / Arbitrary Code Execution in safe-eval", "url": "https://github.com/advisories/GHSA-9pcf-h8q9-63f6", "severity": "high", "versions": ["0.0.0", "0.1.0", "0.2.0", "0.3.0", "0.4.0", "0.4.1"], "vulnerableVersions": ["0.0.0", "0.1.0", "0.2.0", "0.3.0", "0.4.0", "0.4.1"], "cwe": [], "cvss": {"score": 0, "vectorString": null}, "range": ">=0.0.0", "id": "G0v+HWez1LlN2lhEVpa7RxbM+2I5G5K7oXCYkCEjNv16qyxKLB51lKTMXmJZdSTalSmHpMte1YXaRkUo1exjgw=="}