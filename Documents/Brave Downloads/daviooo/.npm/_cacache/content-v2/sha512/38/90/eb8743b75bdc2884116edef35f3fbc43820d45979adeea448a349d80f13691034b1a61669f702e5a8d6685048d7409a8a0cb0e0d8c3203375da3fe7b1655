{"_id": "chartjs-node-canvas", "_rev": "46-eeb2fd7e8fe7022ef6fdfcec0b67a5eb", "name": "chartjs-node-canvas", "dist-tags": {"latest": "5.0.0"}, "versions": {"1.0.0": {"name": "chartjs-node-canvas", "version": "1.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@1.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "e82ca6a458135ab42b3044334db830ac7c44497c", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-1.0.0.tgz", "fileCount": 19, "integrity": "sha512-V17GeBxQwH1CHgudFWwNm1u27NaHtuOyWnuciL1xtMlKkOgzqCGJDIW1MYLmQSSGboH9AZulszl2E1DtEywivA==", "signatures": [{"sig": "MEUCIH1GsX0o0JnMizBE1RTE2/9YzqFhp3WzPD4Qr2TUR2A/AiEA1s2zXJt9ksUO+dyebIRCvyh8W6zfna8Dh27Q7pHWnZg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28805}, "main": "dist/index.js", "gitHead": "4e5218ddd1db28c50815638c52007676d5c2f984", "scripts": {"test": "mocha --opts ./mocha.opts dist/**/*.spec.js", "build": "tsc", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha --opts ./mocha.opts dist/**/*.spec.js", "watch-test": "mocha --watch --opts ./mocha.opts dist/**/*.spec.js", "watch-build": "tsc --watch"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "9.7.1", "dependencies": {"fresh-require": "^1.0.3", "canvas-prebuilt": "^2.0.0-alpha.5"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.0.4", "tslint": "^5.9.1", "chart.js": "^2.7.2", "typescript": "^2.7.2", "@types/node": "^9.4.7", "@types/mocha": "^2.2.48", "tslint-divid": "^1.3.0", "@types/chart.js": "^2.7.10", "tslint-immutable": "^4.5.2", "source-map-support": "^0.5.4", "tslint-eslint-rules": "^5.1.0"}, "peerDependencies": {"chart.js": "^2.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_1.0.0_1521127654370_0.14569816030451377", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "chartjs-node-canvas", "version": "1.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@1.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "6f9f89464a523f4200a929622f64eb3a18a61558", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-1.1.0.tgz", "fileCount": 13, "integrity": "sha512-Wx8SAfdVlBtsiqhfRHFNVdGxOAlmDlX9hH9k7eCw9q4pbkJ0FYXTm9mHxFLSuieUJI2R3S/X4QIJLwIxBTHZkQ==", "signatures": [{"sig": "MEUCIQDErePaLlF3k0dSCZ+xAbpnTz4gnn/mXoWrQtoAubAPRAIgdQhLAtQKiBXAwqnS1sO5azKS9acO2pzHrMejb5TuBbg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19227}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "gitHead": "4e5218ddd1db28c50815638c52007676d5c2f984", "scripts": {"test": "mocha --opts ./mocha.opts dist/**/*.spec.js", "build": "tsc", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha --opts ./mocha.opts dist/**/*.spec.js", "watch-test": "mocha --watch --opts ./mocha.opts dist/**/*.spec.js", "watch-build": "tsc --watch"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "9.7.1", "dependencies": {"fresh-require": "^1.0.3", "canvas-prebuilt": "^2.0.0-alpha.5"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.0.4", "tslint": "^5.9.1", "chart.js": "^2.7.2", "typescript": "^2.7.2", "@types/node": "^9.4.7", "@types/mocha": "^2.2.48", "tslint-divid": "^1.3.0", "@types/chart.js": "^2.7.10", "tslint-immutable": "^4.5.2", "source-map-support": "^0.5.4", "tslint-eslint-rules": "^5.1.0"}, "peerDependencies": {"chart.js": "^2.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_1.1.0_1521129247412_0.8548492463298496", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "chartjs-node-canvas", "version": "1.1.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@1.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "8fc84b4a4bf1f35f11dc52fa8b5f26973cb8b7ad", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-1.1.1.tgz", "fileCount": 19, "integrity": "sha512-lQm1wscRA7UCY8dgEKY3I3BSKoZpVpGAqSae8q3/6343FQtKu1eQDW8fSQjaFP9wZipcddu33T8a/wfu9mE40A==", "signatures": [{"sig": "MEYCIQDNNfB2/mQq2vul1bOw2Gf0grlXkZ4b4EzLE13z5SpKdgIhAM9f71YKFg1bp2q72+XPM0AWtdrCXu6NE8mdGh7DQuUr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28839}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "gitHead": "a20f22a9d5c37d3b31cc87c7da6beb49dc59e551", "scripts": {"test": "mocha --opts ./mocha.opts dist/**/*.spec.js", "build": "tsc", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha --opts ./mocha.opts dist/**/*.spec.js", "watch-test": "mocha --watch --opts ./mocha.opts dist/**/*.spec.js", "watch-build": "tsc --watch"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "9.7.1", "dependencies": {"fresh-require": "^1.0.3", "canvas-prebuilt": "^2.0.0-alpha.5"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.0.4", "tslint": "^5.9.1", "chart.js": "^2.7.2", "typescript": "^2.7.2", "@types/node": "^9.4.7", "@types/mocha": "^2.2.48", "tslint-divid": "^1.3.0", "@types/chart.js": "^2.7.10", "tslint-immutable": "^4.5.2", "source-map-support": "^0.5.4", "tslint-eslint-rules": "^5.1.0"}, "peerDependencies": {"chart.js": "^2.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_1.1.1_1521129457701_0.3288854437267934", "host": "s3://npm-registry-packages"}}, "1.1.2": {"name": "chartjs-node-canvas", "version": "1.1.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@1.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "daf5c6d2fbcad4da3806ace4aa926eeea5e23507", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-1.1.2.tgz", "fileCount": 19, "integrity": "sha512-qmWUXxbgYS75q4KllZOVJhRD1QjD88MdGesULUahZWlc3ze/IMZ52tN8x8pI5j7d+WWmhBjgJRyT0R1sCyQi4A==", "signatures": [{"sig": "MEQCIFF5XPrKNs5NsNxvzq1Jeetuecb35qmWIVQE1+tEGE4pAiA6mVA2AGPbSUX3Ulna6c3ZQrYl9lafosVBqa2n2fbTiA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32732}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "gitHead": "415b3542fc6d70493955bd45692ac57bb373bd08", "scripts": {"test": "mocha --opts ./mocha.opts dist/**/*.spec.js", "build": "tsc", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha --opts ./mocha.opts dist/**/*.spec.js", "watch-test": "mocha --watch --opts ./mocha.opts dist/**/*.spec.js", "watch-build": "tsc --watch"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "9.7.1", "dependencies": {"fresh-require": "^1.0.3", "canvas-prebuilt": "^2.0.0-alpha.5"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.0.4", "tslint": "^5.9.1", "chart.js": "^2.7.2", "typescript": "^2.7.2", "@types/node": "^9.4.7", "@types/mocha": "^2.2.48", "tslint-divid": "^1.3.0", "@types/chart.js": "^2.7.10", "tslint-immutable": "^4.5.2", "source-map-support": "^0.5.4", "tslint-eslint-rules": "^5.1.0"}, "peerDependencies": {"chart.js": "^2.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_1.1.2_1521986365755_0.6887608280600499", "host": "s3://npm-registry-packages"}}, "1.1.3": {"name": "chartjs-node-canvas", "version": "1.1.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@1.1.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "26e7881d161072a381c5702a69e25c36f57ca9ee", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-1.1.3.tgz", "fileCount": 19, "integrity": "sha512-4BEwqWfr68LgEJs8oRVxWzauCVr9Jdt7v5Wdtqne91C4Ns1irXvemlRjyAymgc0vH5oUQ8kjHbtAwCO37N2iuQ==", "signatures": [{"sig": "MEYCIQDNV4OUbauLZ3VdsaRFp34EUImgrqmWwooCVRdDnrLtNgIhAK3TQmWZUljKotUMScvXeZLVejC6JUn3tkW4JLIt8t9K", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32731}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "gitHead": "d9aa02d262302ee8816b876e207a4a05e5a02423", "scripts": {"test": "mocha --opts ./mocha.opts dist/**/*.spec.js", "build": "tsc", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha --opts ./mocha.opts dist/**/*.spec.js", "watch-test": "mocha --watch --opts ./mocha.opts dist/**/*.spec.js", "watch-build": "tsc --watch"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "9.9.0", "dependencies": {"fresh-require": "^1.0.3", "canvas-prebuilt": "2.0.0-alpha.5"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.0.4", "tslint": "^5.9.1", "chart.js": "^2.7.2", "typescript": "^2.7.2", "@types/node": "^9.4.7", "@types/mocha": "^2.2.48", "tslint-divid": "^1.3.0", "@types/chart.js": "^2.7.10", "tslint-immutable": "^4.5.2", "source-map-support": "^0.5.4", "tslint-eslint-rules": "^5.1.0"}, "peerDependencies": {"chart.js": "^2.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_1.1.3_1521988351398_0.8142758812247803", "host": "s3://npm-registry-packages"}}, "1.1.5": {"name": "chartjs-node-canvas", "version": "1.1.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@1.1.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "a17eb8253ee425970f9efc2b32c0e84dac0ace1f", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-1.1.5.tgz", "fileCount": 19, "integrity": "sha512-ZYGg0bQuxNVEpUEY1LVqlK0IUCFb6cpBRjMy/USfK58253EnHZDSPlbwze4wAd3y0Yot0DxLDVVlOm5+3EIIjw==", "signatures": [{"sig": "MEQCIHHsg0GxyVioW97CAYO0Sl4nsj285+CwwguSPal7QgleAiBPSsZL1maTMmJ8oWJtrph6ytv1qZ/AVmmsi40d5ahO8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28758, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa6yndCRA9TVsSAnZWagAAMl8P/iGZx13IaKGglIQre05N\n2OMdM3g/yeadlu0zX1nVlw6hQl1TMYVAkqj3m9/8qAEuT0ZIEQhnblSfLqr2\ntdtLu0P6ogEnYP29A7Pz8j80SXihTNFWgJqcaP70F8+bMoBYCJn326VJNmVJ\nI2LZbeIWdBOCEe5tiKf7Fob4sGZacQD4+Qj2K/qYKE/9UFC+dGArUWsFdiBq\nC3ccCYkhm+GBvQbJWFnwcayKqURG57Yhqk+M3+0BayJGVRQnvHmR7yr8xaJN\na6Zt50H270WEUXo1MZLmHfFTdnSE0oSUopy22e2SEMe5CxYXwh+k2RBQzz1C\nEYViLcEB2MtrWSWwy6Udu6OOUJAzVnxxiqRslhnfHPG3YuaDl9I9xDTJAGpr\nsQgLOoGPuH4RFUPV6dj2pca3OSDBUl4OtYSzvxD5ZlMeASjtnEuvBkL/k+Lo\nn5dsIZXY9eJBpPFAD42jj1b2G6sdTq/lNNW7jm1l9xaxoD4euDQcQbFGWB2O\nVX+mnCVKIWSIlkLPgaMziKOy1jLIp69dkS6cBNpWGyx09G/gmYYizpbge3tv\nwpLTyE5deHMZAQ58d0BqJOO5los8YghHvvjS1hYqR4691WudGa7hXnvBNvRg\nrqWM81SfOeZCQMjV0NWz4EFubPfwlyDCMfOTSfoUO7IYja2rBwqAm73K2OrZ\nieyY\r\n=RKKw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "gitHead": "12a2212d72e780c8b9554cb2d987787eb0132867", "scripts": {"test": "mocha --opts ./mocha.opts dist/**/*.spec.js", "build": "tsc", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha --opts ./mocha.opts dist/**/*.spec.js", "watch-test": "mocha --watch --opts ./mocha.opts dist/**/*.spec.js", "watch-build": "tsc --watch"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "9.11.1", "dependencies": {"fresh-require": "^1.0.3", "canvas-prebuilt": "^2.0.0-alpha.12"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.1.1", "tslint": "^5.9.1", "chart.js": "^2.7.2", "typescript": "^2.8.3", "@types/node": "^9.4.7", "@types/mocha": "^5.2.0", "tslint-divid": "^1.3.0", "@types/chart.js": "^2.7.10", "tslint-immutable": "^4.5.2", "source-map-support": "^0.5.5", "tslint-eslint-rules": "^5.1.0"}, "peerDependencies": {"chart.js": "^2.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_1.1.5_1525361116602_0.9870854244080107", "host": "s3://npm-registry-packages"}}, "1.1.6": {"name": "chartjs-node-canvas", "version": "1.1.6", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@1.1.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "93aad0559cc462146ef84ca8113b8a19d58bff05", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-1.1.6.tgz", "fileCount": 19, "integrity": "sha512-c6spu+lz1mlZzfIipJps/TewIioQlL3hzr2dkOaFgxA4qEpJlM5K68zoAqBcBiwWPE58dbkBOcWjH+79bH7h2w==", "signatures": [{"sig": "MEUCIQCc3ucekAONXCY8PDaQMxRVFrCLwBJExWCaxi7ohaG5qQIgEJszqFM975pu+uXqqslDpnK/Rblq6Muzsdwug/1EMJo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa/qgzCRA9TVsSAnZWagAAb6gP/0FGK3wp6A7LaTUC9kNE\nFYzOciIZ+MCgUv43dzFLfQysoeV0XIvzj02OD2jm9YY0pOs4VnnZYoqacsPu\nMKVAEjQijIa+LtRGDmVckKq7/kVGkYJc4Pr3+y7EVtR8QrZuSp8eR98+ecAL\nz/uRAH4T/T0v2gZTr6DBQQQQezhTX3fLXsfy2Bwace1Fc9esF2REFbCggAFN\nU7MkIchY3SuvRZFxpVu+465H4gDyLx8oz3IpZQssn56EC8Tc+6X4obbn9IyK\nLqnkoeDTQ267Ii5/xvf9Qv96W1K618pec3IfCNgnNZI5QJ1/oljG4fwmBXaP\nfggdWMdBlE7sXYmNLu7BLVJAH+wN9ZL5WifxSkieMpuVP7letPepT3Y+wXA5\nPxFVO/gxgiE4euSySIJLN/c6gTOj4Qrg3l4+D4AllC7VV7XP/aGhoTgEN2oM\ngNQmmQdylQcGUUsQhJJfswkzxVKYsGcuuyr2F4y/EvnPWLn/XM4V7L+eP185\nhT+o3sspNU6tH7eGsP/yFIaU/cYIhWTHmqvY4Hq6SwFZHy/dJS7u4YKqUrxa\nb2pn7GiOQki62oYak5ON1F+JmIJQXFJeabHsvLT3A9kM3SDOTMYS+iSHoxZ6\nZNVLWSs3J6ui/3tkY9AKaJRtNHGQHw0QFVuf3u6za+6VB+gXTI6h3qacx0dT\nc9DV\r\n=BYFN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "gitHead": "9706bf41158f03f46c54d00ada01a4c3ded23410", "scripts": {"test": "mocha --opts ./mocha.opts dist/**/*.spec.js", "build": "tsc", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha --opts ./mocha.opts dist/**/*.spec.js", "watch-test": "mocha --watch --opts ./mocha.opts dist/**/*.spec.js", "watch-build": "tsc --watch"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "9.11.1", "dependencies": {"fresh-require": "^1.0.3", "canvas-prebuilt": "^2.0.0-alpha.12"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.1.1", "tslint": "^5.9.1", "chart.js": "^2.7.2", "typescript": "^2.8.3", "@types/node": "^9.4.7", "@types/mocha": "^5.2.0", "tslint-divid": "^1.3.0", "@types/chart.js": "^2.7.10", "tslint-immutable": "^4.5.2", "source-map-support": "^0.5.5", "tslint-eslint-rules": "^5.1.0"}, "peerDependencies": {"chart.js": "^2.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_1.1.6_1526638643424_0.3575080241678219", "host": "s3://npm-registry-packages"}}, "1.1.8": {"name": "chartjs-node-canvas", "version": "1.1.8", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@1.1.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "8eed793ae2414e8c8e9103d12416304e56002bcd", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-1.1.8.tgz", "fileCount": 8, "integrity": "sha512-bhrTrQI10vNxyNjut/ERAjOvB+0TvpAFNCv3gWpROQEKrVcbFiM/y+t903+R3rPVmO0DwT9UO0w7ahSV6muo3A==", "signatures": [{"sig": "MEMCHwpQreyhceIo7b6delWj7EpsY5kk8QsuvieLPg4a6FMCIC0ubtzqvLByNlSDtwNObSa56kH1xpd0AVEb8Da/JwOV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11501, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcCSOSCRA9TVsSAnZWagAAl1YP/jnAlspB3xXQDsOhG8Zb\nCoEXzCNYQ3JjccU4KS2sSg4XGekeAkSK3S4HDA0rTssa2q9xglkCE5vU8u8Z\n1MnSJsBZGnZY3zZteoJQaHiaDViCy6d9z5djECUXrlFpX1qNGSGcI89mkxQC\nrrveIW39Ty4F3ND2pmRvQ67ba7shOWERep/pzdsKkn+OQTFvk1b+PMH8BMMk\nTEVcfF1owv/snzdfiIDfifLIqlE5gvv7XiUv/I2yMf/VL9nqe4hB7uXDVfTu\n5LfkerJp3dFjPAD5X19O38mQ0SvzWmlQEsUF169NQl6/avsgIG6viijr3xOI\n4BISrswIYHA7aev/IS09GGSptyfmOM+1sjRwuMt3GmBclWtCZFlVlDSbfHkg\nQSjBhIQTtalBJJquPiDmjje+BDsBvs1W4tOhmEN7hL32oyjzqbLDUaoXFQ9/\nn2tPqQ+cr33P/VrV4By+pinFtVo+PTcEIMomgnnK8DMIv5WXR392Sf98WyC5\nCLGZHPvHvjzlDFEQiuY8/YY18sWIgxD/B2MSaV9EdQz4WWEP84zPNe2ui2c4\np3r4Z64bzfxq9OqSgZsBvwoAeln8PBTLsQXKQPGQbnik5fPgeQzLHFny2A3G\nrXO4lVfVbRUqjWLiOD/XUXNQGMxYw46nW7ShaqHDflv4RJR1U32AskuD5/8S\nKNPJ\r\n=vPId\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "gitHead": "2286106dcef9a3d0fc6ecc454dd0ac0a8fe2a803", "scripts": {"test": "mocha --opts ./mocha.opts dist/**/*.spec.js", "build": "tsc", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha --opts ./mocha.opts dist/**/*.spec.js", "watch-test": "mocha --watch --opts ./mocha.opts dist/**/*.spec.js", "watch-build": "tsc --watch"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "10.13.0", "dependencies": {"fresh-require": "^1.0.3", "canvas-prebuilt": "^2.0.0-alpha.12"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "tslint": "^5.11.0", "chart.js": "^2.7.3", "typescript": "^3.2.1", "@types/node": "^9.6.40", "@types/mocha": "^5.2.5", "tslint-divid": "^1.3.0", "@types/chart.js": "^2.7.40", "tslint-immutable": "^4.9.1", "source-map-support": "^0.5.9", "tslint-eslint-rules": "^5.4.0"}, "peerDependencies": {"chart.js": "^2.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_1.1.8_1544102801599_0.35402614067647287", "host": "s3://npm-registry-packages"}}, "1.1.9": {"name": "chartjs-node-canvas", "version": "1.1.9", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@1.1.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "2f406c557f047fe2e51dcb13836d0ce89aa822e4", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-1.1.9.tgz", "fileCount": 8, "integrity": "sha512-Wos7PIBsu7MrMUQphQCzulEKDVHzz1C3iFABegbsYBSR6OapYKnOxVmC/EylF/LBCiUQUK0CSp5PCuS5MQdX8g==", "signatures": [{"sig": "MEUCIEAliFwGNqeNcVfbq2CnM2QJA6Ux4wFnlqHeXsjRpPLCAiEA9RcpDbKtaLkl8weTvSxqo0HJlF6GghXRkEsG+42cI8Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11501, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcCSRDCRA9TVsSAnZWagAAhBMQAIRMsSPX/TEnlkhHiDNt\nu6B54HWhjy+97078SBkBpfLxIP/tg3IaLFwazFA6dlDQH2TeBNEx2n7lLkSL\nwNnrVGyNz5/SINgSp5p+iz7eKYhbFmzf6D9pM9PaoAeAeBq0uHOhXTJS9+kA\nVvhQnVFKHMztm3zbwwo/0HK0/64tTuQvF+bZHwUFPCJHpnxh/5aXwdPLBTqJ\nBR9tqfO5LZc1FTZIkNjTc8tl/EB1KPkOz4Hb/2CPRNlmoMYpayzDdNmUf1CC\nhmmDimkgFRUbKd80SJIcjPdQYpGWsp9h7j4e9Bdi87tsV3pcUyFAIxM7oHro\nzp3rEWcX9QFf43v8IgI00Ov2D0hfAcdkd1cFDcdCEWo04hOzgRgBs+mbBmOM\nMwyPj54n6RcpN8XO57czZsk5D99DiSy+gx6PXUsOeVP/o18DWCLS6qUSZPW5\nlAZNNlQ/YcQOfaallRyCJFqOTKunxEUMUangAnIq4C4Ic5l0qWBDV1jxz97Z\nxuj3Mn7yVvL+AurNlDnGsDqpUcmaqTjvqeKOZnZLp4ptkcayxh2TM8QnRVSd\nT8UZJgnBHyo8yfjLvcgiwGkTA8MmuuSttyH4HOqjr00ODVoVpRG35ReDVu2z\nQYTVJNbl/H2M5Xx+l10Fi1HUj/tB7ZAv4bBu1m4oXQa3ddmmNpywxRUWmNVN\nHlXs\r\n=gKNf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "gitHead": "455b7fbefdaa76a0e6c6ce1a4f7bd0d2d4f32151", "scripts": {"test": "mocha --opts ./mocha.opts dist/**/*.spec.js", "build": "tsc", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha --opts ./mocha.opts dist/**/*.spec.js", "watch-test": "mocha --watch --opts ./mocha.opts dist/**/*.spec.js", "watch-build": "tsc --watch"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "10.13.0", "dependencies": {"fresh-require": "^1.0.3", "canvas-prebuilt": "^2.0.0-alpha.12"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "tslint": "^5.11.0", "chart.js": "^2.7.3", "typescript": "^3.2.1", "@types/node": "^9.6.40", "@types/mocha": "^5.2.5", "tslint-divid": "^1.3.0", "@types/chart.js": "^2.7.40", "tslint-immutable": "^4.9.1", "source-map-support": "^0.5.9", "tslint-eslint-rules": "^5.4.0"}, "peerDependencies": {"chart.js": "^2.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_1.1.9_1544102978856_0.48816826029764426", "host": "s3://npm-registry-packages"}}, "1.1.10": {"name": "chartjs-node-canvas", "version": "1.1.10", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@1.1.10", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "55f1c6b5a757cb16e0229ad1a9f03bc803417d97", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-1.1.10.tgz", "fileCount": 8, "integrity": "sha512-uI5rEDUgPecLcxQ2MyxC1BUZzqagcczbMUFATkJ25d1xtx1IkvH1s+lvTAPEUvV8ZuhmzzHkpaUNozPgpxhQ2Q==", "signatures": [{"sig": "MEYCIQDyqiPJnWRB/i13vu22gKv59fhIHFP2pJ81HnZf+9g6XAIhANkklYTggDkqGDyuT9pBZO67it5KyuTGkVpdYQiRyWld", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15842, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcCSwPCRA9TVsSAnZWagAAH4kP/RD68Dpctjg3NOsvB9Kf\n2XUbxl9UBlQNRLYbh/5au6TFkfDGKyroIzFPRHmwCIvu0Oxmf3Eq6xXnP/wd\nU5hS+pUFev6NHdOkzWEfgP9Rfctd8K8p0KLjThZBaQ8mc64oCnbpzhINRtDb\nVE5LYuP9Qi5icvyWBlnVQx6m35TznsN+IuSijm9dPaksExBKnSSbO/Fl4RGi\nFwChKmZO9zGfHpSUEJWPlBNwsSrA7AmaxNRGFPiHzCN2+7RVQ/CwxC24rqrh\n1t72UtggkcfUmalZq61T+yiGFws5QybxlNbmCjbbb0jsVAs1GeNpn/T7mw/y\nWYc+3X6fUmahxzpuz50P8rMhdPmYFea4IPV114hoLDbGbtmeU3UnhiDJKkO8\n4y/NotdYNUlq1mDv7FKC1UbeQW900ujqR/pU2E+uYBXeEQK6kUSRGStXYiaF\nJwfbLYGHrRucu8zr0SuxvzcUJxTIn9Hi/hSwQNce+q8Bl/CpfVMTJPhvJlHj\nxDlosOrdcnoOy5Sn7mMnoZ33PNbvIVZ2ktexNL4A3OHc9iEpzj4QOv2buHHW\nYNwZT5ALYG0Bx0Wg8la5phDpVl2B7T8NP7vb310RsIztZsfoK4yZkyqLmuLz\nndKOu2Y0HVEzGU905WMDjw15wRSwe3Zn8eXesgph/5Fjbtb/l/DBdI8MH0cg\n7KAi\r\n=Wtd1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "gitHead": "b2413571163e27e6e2ec674b2f12cdb5463189e0", "scripts": {"test": "mocha --opts ./mocha.opts dist/**/*.spec.js", "build": "tsc", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha --opts ./mocha.opts dist/**/*.spec.js", "watch-test": "mocha --watch --opts ./mocha.opts dist/**/*.spec.js", "watch-build": "tsc --watch"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "10.13.0", "dependencies": {"fresh-require": "^1.0.3", "canvas-prebuilt": "^2.0.0-alpha.12"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "tslint": "^5.11.0", "chart.js": "^2.7.3", "typescript": "^3.2.1", "@types/node": "^9.6.40", "@types/mocha": "^5.2.5", "tslint-divid": "^1.3.0", "@types/chart.js": "^2.7.40", "tslint-immutable": "^4.9.1", "source-map-support": "^0.5.9", "tslint-eslint-rules": "^5.4.0"}, "peerDependencies": {"chart.js": "^2.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_1.1.10_1544104975045_0.8588873389203857", "host": "s3://npm-registry-packages"}}, "1.1.11": {"name": "chartjs-node-canvas", "version": "1.1.11", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@1.1.11", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "4d955aee31c00d2565f0a564142e46dcf18f1177", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-1.1.11.tgz", "fileCount": 8, "integrity": "sha512-oxz9SULg5o80UO64qB0qGEbPjfAkOHcKfE4XiEWtEwthPkdf1cRBNBjxy3G6dJ0bBf/Uq08QVxbp5DUWkoAJUg==", "signatures": [{"sig": "MEQCIAU6KUM5frdkCGVRYpne1QINvvXN/JXIwCpLaIcvQflbAiAxz44+Akm4Sn7kOjppWlrBsfGM4KmMcd8MShQY91bulg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15894, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcDhrJCRA9TVsSAnZWagAApgoQAIdQkERO4cYCvoOeU8w1\n2TB9aS533nLrhgo9VPrpDp/FWpnX6pn7MwX26cyr6inEoKsdiTQ6acH85n4w\nya0j419nU/oG5vKwuPxpZkg4T0f5mfi39iQ+BmugqOOhlF8YrDTgihlLpCJV\nJwhJL3brE+dnq0i5Qp1WygKmYZHKBAH2/CtXXo9pnGwLUGYOclwPF9NLRyQ3\n7IjRi+fe1mUqObbjU5P8SeuDCYooc+HHxlt2JzTtJGx5OnkdliB4ZT0sZMTR\nFCwx1pOyPge9WDhKgUHnGG/IAFCi0XvltfID6as30vIgrti6GSXldf95//5B\n73O18JBglrMshkgp85h5JkFhREASqOONipIpR6HCv7F/nvehPRGMtKKDchgD\nRQUCWe4A0B6tfU4m6/H3esCS56VAYPsNHPqjLXqn8HbPGXSfzICNRBuwrpmw\nXu2qECqzAFoLZSTFCC2G/GcJIlN5F7e/OUZEsPTU5wT/qWtcsS9uaZswJ3PJ\n75NSM6SDZ7zcYPUyuTDFNFJDExtHCkzRxZwsQIiXirMJOvPFvTJNaOiBs/F+\nafHqQSbLdAI8ZIgWlKH35gHZriJ4bSOhYY7t6QiUYjNr2IndYWuieCC/vw7V\nRSScTe/N2PXKnysC4tFnrXWS99H5rawCjEKUUjrC9oYFDBxChorNCfCF1wIk\n0rvQ\r\n=/N04\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "gitHead": "3a5b49c79d27d6e4f64a107bf72229c9a5f9154c", "scripts": {"test": "mocha --opts ./mocha.opts dist/**/*.spec.js", "build": "tsc", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha --opts ./mocha.opts dist/**/*.spec.js", "watch-test": "mocha --watch --opts ./mocha.opts dist/**/*.spec.js", "watch-build": "tsc --watch"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "10.13.0", "dependencies": {"fresh-require": "^1.0.3", "canvas-prebuilt": "^2.0.0-alpha.12"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "tslint": "^5.11.0", "chart.js": "^2.7.3", "typescript": "^3.2.1", "@types/node": "^9.6.40", "@types/mocha": "^5.2.5", "tslint-divid": "^1.3.0", "@types/chart.js": "^2.7.40", "tslint-immutable": "^4.9.1", "source-map-support": "^0.5.9", "tslint-eslint-rules": "^5.4.0"}, "peerDependencies": {"chart.js": "^2.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_1.1.11_1544428233225_0.42514417282187456", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "chartjs-node-canvas", "version": "2.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@2.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "a8558686f143f25979cac182d547a7231296f78c", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-2.0.0.tgz", "fileCount": 8, "integrity": "sha512-mKO0RgfslBO4gzoDc+hG3kP0xi2Q2Z16ww0/C+xQb5PxmoxlZg6J9gzRmg50Hfwlr9+0wmTgJZiXPfQ30EgTxw==", "signatures": [{"sig": "MEUCIQCvynDMkXqEx/fre65VPHu8NXiI2UIkK28x85jT1JhvCgIgGA6hMRiHRspQIPqJ496g9uwO6Wdcrbtl3CsWlbPnQV0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15828, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcZDQGCRA9TVsSAnZWagAARNsQAIKS1c1HgUCPmACY4urj\nnK/rkxENI6d+5w8/ZgmjsQ9xcmF4s5V9Fw/go1KunFsWkRKAzt4wq19qDR1w\nRxlfI45x2PcfOyXdGMBW1qGLKpJ6VZ4CPTHPvEI4DWrUtvoSimxlvqZYMbu+\nqE3e+dL25zFhfvWWBJwKmyrKwDmzYFta1te7AVPLsx0MBlRCym7l9Hl2TgfN\nBmzmmTDqAkyOftxo9kuVcXP6D479GIREcT8WgqsgvsWFof8p24pWq7XxJ+3k\nMFtsQiCmY1WMJ2BxrN3zOMb/arijakSm4Wn2LWNmnoBhSvLhKw1MrXze0SEv\nF6QhU2tUc6E5sO7kveSR/Kao4h3Y7VobjYvekQZuO5aJXYpoie0JpOjBn2Xd\nl5yDAA4g0LB6QvWAksmqvRjWUSlh6yK2/f8dCduX19w9TGjRhrSLa5ASgxLw\nID+YSg+gA7jPhNWA+X225Gm1ZB2xEcLqnlNVqVo8u5aAUy+3kUReTjK0MmfO\nn9xi/BHK0Wko4swUaZJ1WXz7l7YQ4QbZ+LuppUt7LGPX+uY2c3I73lTGxNns\ntl1wAaVH+NrLrfN8h0haslykHNXSC7H1b7/D+/qjsl5k3Gd29nCi+HfrvG8T\nAMKZR0s9axNzofmciKy2JwmHHwa4aYUovQIlrYGXv1kfrEedYWNe15uRYVwI\nOr2U\r\n=qumK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "gitHead": "3c77244daac978877be1d8db1145a5ea6d6097ea", "scripts": {"test": "mocha --opts ./mocha.opts dist/**/*.spec.js", "build": "tsc", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha --opts ./mocha.opts dist/**/*.spec.js", "watch-test": "mocha --watch --opts ./mocha.opts dist/**/*.spec.js", "watch-build": "tsc --watch"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "10.13.0", "dependencies": {"canvas": "^2.3.1", "fresh-require": "^1.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "tslint": "^5.12.1", "chart.js": "^2.7.3", "typescript": "^3.3.3", "@types/node": "^9.6.42", "@types/mocha": "^5.2.5", "tslint-divid": "^1.3.0", "@types/chart.js": "^2.7.40", "tslint-immutable": "^5.1.2", "source-map-support": "^0.5.10", "tslint-eslint-rules": "^5.4.0"}, "peerDependencies": {"chart.js": "^2.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_2.0.0_1550070789290_0.38424576481340034", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "chartjs-node-canvas", "version": "2.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@2.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "80e7a7c98205fb13f4c8589cfb0b7904d65b12d3", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-2.0.1.tgz", "fileCount": 8, "integrity": "sha512-5NcXgZZemH50kayZdBqWd/cV3fJE0DQ43zqkv4VSbOJ6kvY/vDmdk2xQVUNHG6gVr1s5xzi/pKNaeRIL154L5Q==", "signatures": [{"sig": "MEQCICJ1YU7emgPi+pn5QTzlDRdHSA/CVjmwrr4okVIYR/A6AiA2LWJ/xBWr6rsA9rRpssQ7LBqM2YPkUb3eYRiKt+xy0Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16179, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJca8G3CRA9TVsSAnZWagAAbgAP/A6e0+/TFHpw0Uu8yfaH\nQF1mkRFnhbQMOi5WSTKROID+oLwSjNERV2uk1WCQ6Woza8x78cSFtictNO+d\nZhC+9HZDNMoGgmEHZEyr7W69Hw7UhU3CL2CDM+92JEkf6nUaDwMDC+lAnX2a\nwEQQ7Upru5k1BdXmY/cO1bzz1Mi9slNFGPFKm9ho1JqNsYmDBq0not4m9jLw\nHXvupb4aW+jJKBuAUQ9DPhavX3kjnJsvk0EYs6f1tmEr1hjjN4EEgDyfyaHV\n9w94diFGN9wr7Imcew+FMmWt59Zv/EfJu3QtxjjjlOG8iILS47es1WcCQ6UL\nvJy4LI1WXDhZaA8msevTn3bsL/W1lz93KrqG2pLbsWnK1a8rzFH/O737PxXW\nT1cJVwo9kdQsK/gEsw9RDP0FfvYdkM3ZNtXzFr609kKXerN71Q7BuM9HALrs\ndERz3hyI82mlSv6/muURghiDIklFox10a82303Ui1DXO7l11GewtkSU14mSW\nilAh4FXAsZa+4DlKVBZtpTEJsGK4dTJ5urFxBlLI69ygbSk2ypJzHD0e4oRH\n2BJ5A610FwRV1BWx8eC6xyZw5M1gHCD2tlTnl91se0CRN3SAxlgns9I+i8al\n4pA1fi+xs7TNyvkUVdZmIQcpTjfCzhGHKQE0aeoWpVuB3RB+19om/ZbhDhmG\ngWrO\r\n=JlPe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "gitHead": "7cd7d7ff1a6babf2b668aa2938684468e3739a4d", "scripts": {"test": "mocha --opts ./mocha.opts dist/**/*.spec.js", "build": "tsc", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha --opts ./mocha.opts dist/**/*.spec.js", "watch-test": "mocha --watch --opts ./mocha.opts dist/**/*.spec.js", "watch-build": "tsc --watch"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "10.13.0", "dependencies": {"canvas": "^2.3.1", "fresh-require": "^1.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "tslint": "^5.12.1", "chart.js": "^2.7.3", "typescript": "^3.3.3", "@types/node": "^9.6.42", "@types/mocha": "^5.2.5", "tslint-divid": "^1.3.0", "@types/chart.js": "^2.7.40", "tslint-immutable": "^5.1.2", "source-map-support": "^0.5.10", "tslint-eslint-rules": "^5.4.0"}, "peerDependencies": {"chart.js": "^2.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_2.0.1_1550565815027_0.7686380329933067", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "chartjs-node-canvas", "version": "2.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@2.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "d1706e4bbde61d3da01ec8ed64beadf266f943d7", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-2.1.0.tgz", "fileCount": 11, "integrity": "sha512-Y5m8sWfvlrBGyJA1mKdPDiUt2Ve0j67JILm3A0YaKwLTqvRDDXOolZonBzud/XW1ax9HhjxLlGm26rFDncJUCg==", "signatures": [{"sig": "MEUCIQD6uyLmWrV+5cYowOuS15eFPOGqLsnzHfUwfGw9vS2UsAIgWrTP+pqkD1N7M4J1f6IHLTsMEvgJoK03I9bmYhC2njA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25329, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcm3s1CRA9TVsSAnZWagAAPYQP/RspBe0I14aQwZddZP1q\nQJMBbQBK/ngCc4ROVGCciP7a7Btw2qCPvkd3tmfTuncjCBEdz/W93q3FQySj\nEnNI8jS97sQA78vHrhcZVwvaHx8wIpX2pnm1rhKTAk4hgAF336IIUuONxhSK\nvP4ToXIseI1Tls8kcbMR5o/O211rK7mb4/bGPUeuZ0Cn2vLEiuxGHZEgmq0S\nSzdRXfZeTlwEnJanJs+kicdMaEcFw6g2lE0jCKQ1WfPxEvN3MEwcitvjMEz/\nvcu53ZCZhJzN3fno53NSxKA5iN6ftJ0Qp2V2kOCvGkA3I71hap9YBJ5es54D\nNS5W9NT+kfRfrFMPz4sxFPhMu+ieSTiuo9mOx67O/LSlHlxlQYmYWYLN6osk\npgm2mu3DLVeLZz5qL2var1al0QKTvw1keMASr1c1ya4XlWEtSedE4oXiPOTn\n3qGz5AKYdgXs24c6zwnXtds/x/WEYTrUL+YoURKTtl4l82hNyjEWHPVR1zdL\nJuWiLQKRL7T8h1owyhQuzEdUA/l0haTKEjAvfXmhvwSqoIuDO/zdE0DKLupC\n+2nakR5T816ZUKNjYxU82DOb154R3Yrrg3YenMTy/fFSlap47Pyfud3tbHX0\nzgO22iuRglH27OMSMUoccb/hpEs9UZS9KUrSHN7SlIuFxEG8+MP5PUciTsPI\nQtkG\r\n=yzUF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "gitHead": "7eb589198e2661f5112cfcc39ef2579e09622b8a", "scripts": {"docs": "jsdoc2md dist/index.js > API.md", "test": "mocha --opts ./mocha.opts dist/**/*.spec.js", "build": "tsc", "release": "release-it", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha --opts ./mocha.opts dist/**/*.spec.js", "watch-test": "mocha --watch --opts ./mocha.opts dist/**/*.spec.js", "watch-build": "tsc --watch"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"canvas": "^2.4.1", "fresh-require": "^1.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "tslint": "^5.14.0", "chart.js": "^2.8.0", "release-it": "^10.3.1", "typescript": "^3.3.4000", "@types/node": "^11.12.0", "@types/mocha": "^5.2.6", "tslint-divid": "^1.3.0", "@types/chart.js": "^2.7.50", "tslint-immutable": "^5.5.2", "jsdoc-to-markdown": "^4.0.1", "source-map-support": "^0.5.11", "tslint-eslint-rules": "^5.4.0"}, "peerDependencies": {"chart.js": "^2.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_2.1.0_1553693491665_0.9794954001455087", "host": "s3://npm-registry-packages"}}, "2.2.0": {"name": "chartjs-node-canvas", "version": "2.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@2.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "8f6af1fcd3914bd2ad9bb4a9e9760e686f090404", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-2.2.0.tgz", "fileCount": 9, "integrity": "sha512-e0u4HHOVai066qbRr5L1WcB75u7HINLa9rg4rPtisiTfh1sP0OOrH1I5e/8DUpoeTE1h+DJZ7tJbKXVOSaN9kw==", "signatures": [{"sig": "MEUCIQDXZCHOXFXAbVeUYyqQPudPtodfRivfHj8zxxHE/lP9rwIgDoIURg4xX58Ob+8AblsLpGLGuyvcIRmadWIGaCrLJ/4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24962, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpGXlCRA9TVsSAnZWagAAWmUP/RTJAE41X7NElUFAEbB9\n2geaXAyxLtDEAMZPLiZl1YM1YIvd42hUNNi6zB+F9DQrQRMRGa9e+NxArs9B\n7v73bzhdY1Yb4CVJNLuLQ85PunfKdNhWB0P/lzuOq97tXgAjYaXxNkAKzBlQ\nArkZQWyJFqStrjgEnrC+06M2WcbrbcHD9gs22CgGy2cVR4n7cgiPXLyQf5NV\nHvs/GyED36R7LEllv8URxboCnRrZT1E9m8XcpNIm1kaAQUcume5M3zD4kKTg\nkOwstYL1193ohOJmyvdMJ61PDGkcMyqg7oAkyA85/k6JN3z64eUVGtTZlYjy\ncch1LYkKo6FyXetoBLiS8QbqG9jur37O+9+W7daK6+Lqcj1OcX9kaGkiTDaj\neUc35qc+muWXjqQyMzt/gj7aZz1b/IuGwZxHwsvGNvn97PumCvyRJcDoVOaV\n4v4q3Zyrr7AI77Qd0C4chFm2GKoIOzn3HPWeqB+d8o3iEbXvwrY7RymDse+0\nuTYA1H9qVwQk5shn2clJIUNF6BWVzoOjNw/ZFMlImj4GpWVtxKZGiTJ0AwLu\ngpfl5kDbeCK0W6QmElAOy0KqfcWAv1PJznIanZOgb/n2/cGrfPvYDBPHGKc6\nOddNrjYswF7DKlta4pp4Up3i9GirASZJyvEDaS0UcQQQxJulz8wrN8AF3GED\n+8up\r\n=mkee\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "gitHead": "147a63c59f65e02e3108e3faeb39055d7693159a", "scripts": {"docs": "jsdoc2md dist/index.js > API.md", "test": "mocha --opts ./mocha.opts dist/**/*.spec.js", "build": "tsc", "release": "release-it", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha --opts ./mocha.opts dist/**/*.spec.js", "watch-test": "mocha --watch --opts ./mocha.opts dist/**/*.spec.js", "watch-build": "tsc --watch"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "10.13.0", "dependencies": {"canvas": "^2.4.1", "fresh-require": "^1.0.3", "chartjs-plugin-datalabels": "^0.6.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "tslint": "^5.14.0", "chart.js": "^2.8.0", "release-it": "^10.3.1", "typescript": "^3.3.4000", "@types/node": "^11.12.0", "@types/mocha": "^5.2.6", "tslint-divid": "^1.3.0", "@types/chart.js": "^2.7.50", "tslint-immutable": "^5.5.2", "jsdoc-to-markdown": "^4.0.1", "source-map-support": "^0.5.11", "tslint-eslint-rules": "^5.4.0"}, "peerDependencies": {"chart.js": "^2.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_2.2.0_1554277861012_0.7269061169478068", "host": "s3://npm-registry-packages"}}, "2.2.1": {"name": "chartjs-node-canvas", "version": "2.2.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@2.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "ad43daf45d00ebf19c8753c4d9350b793ff11e4d", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-2.2.1.tgz", "fileCount": 11, "integrity": "sha512-SD9h5MvNkGdZaM81aD1fgVNwM+Oi/fYq+8RKb3+1DE0oXWiEw8/T9iFEfczgINwj9udsGsVC0NjkorKvihs3PA==", "signatures": [{"sig": "MEQCIDwSRXT+qH9/xIAzITKFVRlOrOX11zRTgLIcKUwci1URAiBb6HtxZHOk/mMJ1AimkxSsMrXkAoDh/SIUi7i9kKSTpA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70980, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpHlFCRA9TVsSAnZWagAAucMP/0cJBu5gCJxr095D2z9G\nr7CLLhnLwAaOPPerP76LmHRyMZr49PYczgdPDvX5FbzcqPBzJ21N6FAlBbJX\nisuESqq5EzPkpNfPCzdy9IB4ctCmKmPKRJ+ufqJJmYmplftLvCdHbkZFgss1\n73l6Ec2I0AFLj6JjDV4VNzXbw01Xrj0FDgU9fXUJKhs0kCIryGOukOSKGUQO\ns0MxLJL3hjb2bq78ChyX1lGrK4Jgp6eGteAcitHIWUcUjg2VrzjoQvgfJuPN\ndO5sLlpMI4/lbMtht0uQfwRGfyCtEkAgfkXnVDXhLV70kTyYm96XFFFN4bVc\nPAceI/ITVrZBfCUuL/vSfMGzyKdLttrQqPYTLvL9OaLsGltq+7RySk84kCAr\nO+g5w7NUbZWi6whE82MXhw7MUEcRhHhW3s2sMR7+8BOndA8WFiK9TojqZzag\nW9MbBwHMn7GSqI3NdzXx6KFXZW5M6hMilBWNi/l2T5cEH98XVFJkfZAFtnC5\nE3+szeh+lIVodyke8fDxr2/GF1bkiSWAP+arq/HAYZFg4YkJf6GqYZX5kIlo\n6cdfgd7AZuvMKcnwasJn0DTnzm4SLkJIVGmxZy1iiZKOhp7zCEZlMaEkQ3KK\nDqLto7hnYluLpl7mu267/QP0Til10feFjsICcnCuAeuHkrEY/c7rvAz6FoHz\n6ZmH\r\n=6s8k\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "gitHead": "dff0e5a2b7b946e5130cd340c259fad81df94f01", "scripts": {"docs": "jsdoc2md dist/index.js > API.md", "test": "mocha --opts ./mocha.opts dist/**/*.spec.js", "build": "tsc", "release": "release-it", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha --opts ./mocha.opts dist/**/*.spec.js", "watch-test": "mocha --watch --opts ./mocha.opts dist/**/*.spec.js", "watch-build": "tsc --watch"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "10.13.0", "dependencies": {"canvas": "^2.4.1", "fresh-require": "^1.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "tslint": "^5.14.0", "chart.js": "^2.8.0", "release-it": "^10.3.1", "typescript": "^3.3.4000", "@types/node": "^11.12.0", "@types/mocha": "^5.2.6", "tslint-divid": "^1.3.0", "@types/chart.js": "^2.7.50", "tslint-immutable": "^5.5.2", "jsdoc-to-markdown": "^4.0.1", "source-map-support": "^0.5.11", "tslint-eslint-rules": "^5.4.0", "chartjs-plugin-annotation": "^0.5.7", "chartjs-plugin-datalabels": "^0.6.0"}, "peerDependencies": {"chart.js": "^2.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_2.2.1_1554282821006_0.8596339536583772", "host": "s3://npm-registry-packages"}}, "2.2.2": {"name": "chartjs-node-canvas", "version": "2.2.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@2.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "5c9df0c2fe41e21859f7af2dc63bcab30ba5fcb8", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-2.2.2.tgz", "fileCount": 12, "integrity": "sha512-oxQ8CnSgMbUDCS2YSxTCZ5EHBS9oBKfrvJUqOFpPK7blNlpEOCc/nSmjxfT995yKEbR2BXmkwsUfDRCn0Iqlhw==", "signatures": [{"sig": "MEUCID7YC76MrtcTf30ywB++vUTaEo+Opn3tLVDkY42+vRAxAiEA0nE3bGvXiBZriSJ5NI+sV9g2DbHTGIoC4obvijEocik=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 186373, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJctEfiCRA9TVsSAnZWagAAN0wP/1OGg5GwlZf7PA6iiJQP\nD15xFlBSeG8HI6gMkmT9JPzRr6dDdiejuMkwm/WolBrHPZ1vXa6gCJ4G8t52\n2PCw4xlGsFGfTHOAQFGSEUcfkpfuWt3jYUBjcRMv4gz0lzYikU79Oz83tjgL\naFUswR2zjWgRVgZ+Oocc9z1kg/7YDYba1nCg2G2e6gy8S4isQLxegD+kL3zd\nYR/6MRN9rB4xTirSCXzfREBMFZ3PkGb6Ec1R3WMEzcAn7v+uh7dE5hzqmdQ7\nJvt5/5MPjvDJVBh3+0GUAPwXsH4JmYbf4ZOJVo+b46wGcTw52R4qC/d9bpGI\n/uUvGG366tjPd67bmWRX6cevBe7pD0Z5nBcTPv2chX0K+cF4TepQovCuNm99\nyxdWCWqBERW2dg+0QqkhvC3SjE8v8tja+VZDLYKWG1W/9nb8HU84X8HMEQ47\ndNFc0926nEaWaFQyPDEG3mJ/salkVJUIA+k2dr+w76UGbmSFCtNXfbB8TOZs\n5P60A6h/tNlsgoua1AEFMHU2oxxWPmT6LcGdeUGuDsu5rNmUkxHESMOLsd9Z\nejVxabajta84mbEfyquQepuEIeWl+frXynzqkiv7N5OVktqPeRoHRlJgQaOY\nmqyF5Ah/wjRHLYhHNT559vCCXY9Ch//D1aDojvt6nMQqWNQLkFO4bHAwHW/g\nOMsY\r\n=q5l7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "gitHead": "cee331247531968dd56df8771a39b59296e84364", "scripts": {"docs": "jsdoc2md dist/index.js > API.md", "test": "mocha dist/**/*.spec.js", "build": "tsc", "release": "release-it", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha dist/**/*.spec.js", "watch-test": "mocha --watch dist/**/*.spec.js", "watch-build": "tsc --watch"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"canvas": "^2.4.1", "fresh-require": "^1.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.0.2", "tslint": "^5.15.0", "chart.js": "^2.8.0", "release-it": "^10.4.0", "typescript": "^3.4.1", "@types/node": "^11.13.0", "@types/mocha": "^5.2.6", "tslint-divid": "^1.3.0", "node-memwatch": "^1.0.1", "@types/chart.js": "^2.7.50", "tslint-immutable": "^5.5.2", "jsdoc-to-markdown": "^4.0.1", "source-map-support": "^0.5.11", "tslint-eslint-rules": "^5.4.0", "@types/node-memwatch": "^1.0.0", "chartjs-plugin-annotation": "^0.5.7", "chartjs-plugin-datalabels": "^0.6.0"}, "peerDependencies": {"chart.js": "^2.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_2.2.2_1555318753562_0.60546002703704", "host": "s3://npm-registry-packages"}}, "2.2.3": {"name": "chartjs-node-canvas", "version": "2.2.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@2.2.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "4e66d91b1125924145dc7862379777a345cc3e58", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-2.2.3.tgz", "fileCount": 16, "integrity": "sha512-Gvj2wZCZsSL0/hVXt/COsEdMAqJvKRZAyG5bJHaRAlyyPGb5Ud3MbqQ2EaL5fmTabO39n6ESRVgWvcfUcbDY0Q==", "signatures": [{"sig": "MEQCIAVMRkev/lATXBnCrxlYNML3tIaK/TCkbBiA/BDqLUFiAiAiFpb3gpv9YuKuRKeMtTh75knwekIaXDg+dmokQmP6nQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 188216, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJctIEeCRA9TVsSAnZWagAAf4AQAIE0frrb2yhReWFxiWrg\nrcHy8SobJD7c3H5pf/tpAZBQKLwZGPNCOCxRLqfL2ZvEJ4S8uNCxMkUZ2nmo\nMr0Yx7Im7Ov0IYhQrr03qvsvUDhDdy3LUYX3TQ0m6OA+LtfmHyIUlD8U3cZX\nTYjZGFK+5IHi1c8WYvJUyWaAs9KrE2bIFvBehPVdPgWPWU+oKrj8LwJ9U0x3\ncPFoMJmaIvsB2alMn4aodKdnG372rLY4n8DbhgU6o96SOEjFDVVFwJIV5+Pl\np74+flD499oVjtbWe+y3PRPqW5Wy2aR2DPN+t0iCkqLt2VItMzy7FS/B2vJ7\n/kzZYnztiAQ57g2x4fhOgyWn/jA1BVLWZLId+mof5jri54ffRvzDTBjL2PIN\nNqsj/sugRoCrWesClt9b++mD1bmoCmWmeRU0whGKmQ+o6HIkW5/BA531sHHD\njlcWQJYveTbQxGaPfyzOQXnyTGlNBE4hR1c5f75TN+IRH2BAitrcUYzbVtaP\nRNqBh9LEinQkDKk3DLGdch0mM6IXlvl5gD0bGPUv28zD8KZAu8KOdkI2/rok\nJZRx1T0qZO/GqicceYbwEh8WttVag+fphB/HfZTHMIQkKDILUxAF9TNXXIyZ\nCGB9+LH8+mycm5sHzBns8You2SEb9k9wDA2o50CVkVVcCsL1deYprU9PRFUs\nFn9W\r\n=TCCQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "gitHead": "a7d3d963441167ca8706ecac38306bebeee173a0", "scripts": {"docs": "jsdoc2md dist/index.js > API.md", "test": "mocha dist/**/*.spec.js", "build": "tsc", "release": "release-it", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha dist/**/*.spec.js", "watch-test": "mocha --watch dist/**/*.spec.js", "watch-build": "tsc --watch"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"canvas": "^2.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.1.3", "tslint": "^5.15.0", "chart.js": "^2.8.0", "release-it": "^10.4.2", "typescript": "^3.4.3", "@types/node": "^11.13.4", "@types/mocha": "^5.2.6", "tslint-divid": "^1.3.0", "node-memwatch": "^1.0.1", "@types/chart.js": "^2.7.51", "tslint-immutable": "^5.5.2", "jsdoc-to-markdown": "^4.0.1", "source-map-support": "^0.5.12", "tslint-eslint-rules": "^5.4.0", "@types/node-memwatch": "^1.0.0", "chartjs-plugin-annotation": "^0.5.7", "chartjs-plugin-datalabels": "^0.6.0"}, "peerDependencies": {"chart.js": "^2.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_2.2.3_1555333406039_0.2668940342382471", "host": "s3://npm-registry-packages"}}, "2.2.4": {"name": "chartjs-node-canvas", "version": "2.2.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@2.2.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "53acc0aec3f2cf6a8eb71b1fca210fe64ea136ce", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-2.2.4.tgz", "fileCount": 16, "integrity": "sha512-FkhY/xVroAdWcBoZ6mmZPSIjlzkLgB9wNaBQdhqCyIiTo0Fjnz8iAlqhLpclaUuteihhXUMtUNjetVY/MkLN9g==", "signatures": [{"sig": "MEUCIBSAdVXrrhg6gslGwwH3NMo0IOmr4xavUWwdbUvzh0zOAiEAyxATIgHo5RgW+SZ7GrrGqS3a9e3wzyt76cH8BKlIh/o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153238, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJctIpyCRA9TVsSAnZWagAA6RQQAJYdzWJBmWYDOyXWiy1n\n39dIvX+aodg7wv72B+/EpzGi9/UnrJG7MM7fVLpEZkYKdfL5zlbZAQzC2hpv\nbSzM/VV0CosfVoNn6tt+6EbYyN2s4lAo01fMRTv8KuGe/DTJ8l/wcrYCHZHv\nZ3UzgNZiPmrLCnUubtSlbPluBl4PmPyVClKs2Ij7S99uxsjhGPDwkqu0Cm3s\n0DwdQ/Ip+YHwxSEdi+pUCLtE5pSje7BJOme8N3nMUCN/l+fQLbpBYSztuePa\nMvgOfV5ubTtB0umMGTYbT6PFap0j8+xGhsBLriIvB47MNPxWKMywXNR7F3ys\nE+UEKvxlV7+eXdPfEeer4N4MWJ/49G65OH4F2sPsaaTpdWDFK8Y/cGctm4Ad\n08rWWqd1Bq3xc/2f/TVPqokKNuxq3G1aMaWAYXSvU7PHqDPA1nlASXwR1sU/\n0KftBayY1wjQQuB63fseTf5h3eqUHohd2ZFtZEILJbJZ+D+bVkYhDBy2Dggf\nOThEm0XQrrMkO9vGoWHrbWIN5N1kUH7xUmMvkGx99mc5ZphVy1zis1Ug5nOx\nJWM4Ah0YnUcTllAvLz+iQIRboI88VtpKkA/4JthdMmUxyMiqqcF4jz6L/fL/\nUBklBOI+eN8KoOK7VlYo/Kxl74oqDcXJv0ZT035TVwdmkiCuJO+Ru3+oQCyk\nao1G\r\n=/6qo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "gitHead": "8eaf9aa0d7ce2789c75f6469af3eb89d37c5be79", "scripts": {"docs": "jsdoc2md dist/index.js > API.md", "test": "mocha dist/**/*.spec.js", "build": "tsc", "release": "release-it", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha dist/**/*.spec.js", "watch-test": "mocha --watch dist/**/*.spec.js", "watch-build": "tsc --watch", "build-and-release": "npm run-script build && npm run-script docs && npm run-script release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "10.13.0", "dependencies": {"canvas": "^2.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.1.3", "tslint": "^5.15.0", "chart.js": "^2.8.0", "release-it": "^10.4.2", "typescript": "^3.4.3", "@types/node": "^11.13.4", "@types/mocha": "^5.2.6", "tslint-divid": "^1.3.0", "node-memwatch": "^1.0.1", "@types/chart.js": "^2.7.51", "tslint-immutable": "^5.5.2", "jsdoc-to-markdown": "^4.0.1", "source-map-support": "^0.5.12", "tslint-eslint-rules": "^5.4.0", "@types/node-memwatch": "^1.0.0", "chartjs-plugin-annotation": "^0.5.7", "chartjs-plugin-datalabels": "^0.6.0"}, "peerDependencies": {"chart.js": "^2.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_2.2.4_1555335794046_0.9482457054691038", "host": "s3://npm-registry-packages"}}, "2.3.0": {"name": "chartjs-node-canvas", "version": "2.3.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@2.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "4eb837e30183b59e6d69e53cbc52e6f8fbb6dc08", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-2.3.0.tgz", "fileCount": 16, "integrity": "sha512-Ls6VP259CVKFvyPshTpNNfoAtbG9MrM9Ygd0c+0zxG771g4FFbN07CFJvAlQEo4CXyJ3ber1T6RWQ3HGfmVgtw==", "signatures": [{"sig": "MEUCIQCDtaL4m2IPXB7ma9+T7j4jM7F8xWD7ag6HJVSzViUf4wIgLoIxwsFA4BFlHC8U+ndtZrauRcnyBLJCB7O0pFH4Nuk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 194522, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcwxGfCRA9TVsSAnZWagAA2JYP/RlWkcl3OYk+ArWQIZZG\nhqv4E/LGR8DQWcYmNiPaLd32QTzA6kbQFWLaVhDYSYbIjbpn34XyrmbUxwDd\n7ZcbQk+dJOs5vbPid6gG8f8T6yeI842eQKEmBYYr1urYvpNyZLlljSMvMH1m\nfHkMnv63mpSfRbXs6EphdTu1rZzLlmro1p8M17FyF9j01WsKtDfwT3Tfz5dH\nbww49hBia57to+bVazqFUIYAZ3zCyP94bA2FeEPcIVzlkowYMY1ZPMIcQoF8\no5p9Ojgz5x0jm+JHrUOWAY0PnH1DFCj4LiWodCgd+rFRCl9vbi00rEgYSNwa\nglXkxiqwPEKukQPcfJWm2gzfrw4MbdjUSHseBxlX0Q0pcQcANS48lrL/nlmN\nyatGPFl+eGXEPk4bCOCBzjSWyWy2fAAXFRoP9ABNuiCD+uY1ZmQKHgVU/avH\noJQDN/Mus1E+rKrAHnpy67WZcNx9wXYTW4wwQufXi6lWUgT2hZZ9aQwrWgot\nn8COG76SmMiq1B2e2z9mGQEoQQ3CJaZXW815ZQBynAt3W5tA4qRumJplULJ5\nS251fbiSFyIFqa9JcLRewumSoYZaM+aNNZLDojdGm26fxM75ObdLnZQp29B3\nbaKuOVcb3NsM7Ydep3lN8N/JB006D2i6wV7kZNvOKDV2m05cOLaR4SUsUkW7\nKzQJ\r\n=Gd7w\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "gitHead": "3b038c7aed3c564cbd08cd6f88c60c71f99526ef", "scripts": {"docs": "jsdoc2md dist/index.js > API.md", "test": "mocha dist/**/*.spec.js", "build": "tsc", "release": "release-it", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha dist/**/*.spec.js", "watch-test": "mocha --watch dist/**/*.spec.js", "watch-build": "tsc --watch", "build-and-release": "npm run-script build && npm run-script docs && npm run-script release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"canvas": "^2.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.1.3", "tslint": "^5.15.0", "chart.js": "^2.8.0", "release-it": "^10.4.2", "typescript": "^3.4.3", "@types/node": "^11.13.4", "@types/mocha": "^5.2.6", "tslint-divid": "^1.3.0", "node-memwatch": "^1.0.1", "@types/chart.js": "^2.7.51", "tslint-immutable": "^5.5.2", "jsdoc-to-markdown": "^4.0.1", "source-map-support": "^0.5.12", "tslint-eslint-rules": "^5.4.0", "@types/node-memwatch": "^1.0.0", "chartjs-plugin-annotation": "^0.5.7", "chartjs-plugin-datalabels": "^0.6.0"}, "peerDependencies": {"chart.js": "^2.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_2.3.0_1556287902902_0.1911659715237206", "host": "s3://npm-registry-packages"}}, "2.4.0": {"name": "chartjs-node-canvas", "version": "2.4.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@2.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "5068c86cf7a2f49192c0b5f8c35ce8026771ff43", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-2.4.0.tgz", "fileCount": 18, "integrity": "sha512-wzKwaHBCUynQDG60YMFDQicN9ceneldMCHfGIIllcbCeG9unPY9XmlQYa3oyhtHST0hJRikqUrZGKCSe5hPjkQ==", "signatures": [{"sig": "MEYCIQCCcn3QsBIxn63ivexhuxZ8I4zQt+h+L0bTZUFNCYbDvwIhANkIL1vmv3F/LnWr3Cv8HiziompHm8I18Ea//b03i05D", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 235970, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc1X01CRA9TVsSAnZWagAA/oYP/j3JtKn59JwvovqXGp2U\nqukCosJltfU8UXWmOx061Ns1KYNL0w8wbvcI9crDa9nuKg8SLbN4G6jKZy1E\ndiy3/PWZXP2Sdn8nDsgx+8ahuMCaG5zJB+LIiUHzRRiw22NPmlA7OssrAqLU\n/bUD2M40CcLqxCsALK4vcn1f7cGJqpfwN4IYCfRgI4ky51+RO0DgfmnhdmzC\nEgrIrrg8bwPNTP3U4aIz/Ck6bnNFmOZSR7N3b8CfaEoLC0FdL2RYv7EV2RMx\nMY4PtPAVTlMtSaGaUlsR1osU51DrTasipaSzAVMp2Hjc2Bpn3Y/9CISUK3fX\nHpXUpx+1ulYp9oq/IN4A0acRNbi2DEnEDPtJJzT9XxePOcnHjU42w/3n2hkt\no8RyOAQEtNi8csUKrhZCeDXU+AphSbGIfgq5pR7rx8ua/eCMDW+jyPufDBlR\nyzVx03mYNXWCbbeSkbWtxPMXeVr4StzPu/dK3HGEP1MBHmumrCStIuYNMvwR\ngz9iPY7WZRQ8PHffElNkpxnf82pjRMZfuB78S0NlNAtpA5SXCElm3TgZXO/D\nLFiXiSamb7RSu5/UbeJDeLN7YQyOuLmbL0LByRab9kuNO1gqR8FSpHDhxkij\n2DSm97wWkxC0lcQZOnODlHaBPcMLhSZ73nifL/xnDPKfdWu8nFVemqyD28s0\nWLSg\r\n=mCwX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "gitHead": "8b3a83ac2627bf658fd79f4a260a228fb8d938aa", "scripts": {"docs": "jsdoc2md dist/index.js > API.md", "test": "npm run-script test-unit && npm run-script test-e2e", "build": "tsc", "release": "release-it", "test-e2e": "mocha dist/**/*.e2e.spec.js", "test-unit": "mocha dist/**/*.spec.js --exclude dist/**/*.e2e.spec.js", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha dist/**/*.spec.js", "watch-test": "mocha --watch dist/**/*.spec.js", "watch-build": "tsc --watch", "build-and-release": "npm run-script build && npm run-script docs && npm run-script release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"canvas": "^2.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.1.3", "tslint": "^5.15.0", "chart.js": "^2.8.0", "release-it": "^10.4.2", "typescript": "^3.4.3", "@types/node": "^11.13.4", "@types/mocha": "^5.2.6", "tslint-divid": "^1.3.0", "node-memwatch": "^1.0.1", "@types/chart.js": "^2.7.51", "tslint-immutable": "^5.5.2", "jsdoc-to-markdown": "^4.0.1", "source-map-support": "^0.5.12", "tslint-eslint-rules": "^5.4.0", "@types/node-memwatch": "^1.0.0", "chartjs-plugin-annotation": "^0.5.7", "chartjs-plugin-datalabels": "^0.6.0"}, "peerDependencies": {"chart.js": "^2.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_2.4.0_1557495092756_0.5483070180207787", "host": "s3://npm-registry-packages"}}, "2.4.1": {"name": "chartjs-node-canvas", "version": "2.4.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@2.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "e62a565517a7bc888bdd2683df2e7603f1d626e0", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-2.4.1.tgz", "fileCount": 25, "integrity": "sha512-axIq5SrXDV+U48zr3Ri1q0c7qwSnlyHmbREtCKTMSf7EFMyL+Rwv8zwMsmslgzzH/TFRu9qzV4JniMQfGpI6fw==", "signatures": [{"sig": "MEUCIDQY8eygG/StzQyvKDEMv68JHWi9c+JzbfhAx2oKVGEvAiEAwU8Wx0wgwpSphx00RjN11VBd/rTff7qV+1wBt27TIWs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 278333, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd0+q8CRA9TVsSAnZWagAAQ0wQAIRBEuG/VdCmM+b4YT13\neAp7Hik2loBwQuulpkFGE1zSa2qY7/+tAouFU6C/JoU+Bl5mpHHOPGb+bzUr\ncDi1yEo+rBcR/Z5J0pZ5HfKWiiA7YrWvPbROkuQKsHMaUQcqiOm90Y2zGVtx\nnzi66ZRLXss1uEuvpHSLPgHzQJU0npcJvT0DFcieXjjNwDqXQLPI1lXL3Tvp\n7eMW4UC1iTvG3e1JhNM8jLKAvnTV9ZDGCoG7ljTX+BPFUjQ6YfNkMH341DMU\n67Rp8ojqrTx6unQa+GMiuk6zXKpxW/NI+F6ucg7rXWjGHmmotpXw9E8Dq8im\nhQ6ZDxmXfBAYW8CEB7t9vZsN3HzVxANlvOSZp2ipCfolVJd6IwmBcelcP83l\nffDgrlyELofhg6H39CmNfoCyuIA9OejpybX/RGzqOBNVLJAFUy9WN9Gn6lHh\n783d1sh9QJVoi9FJz3MSxm/lc7UlsxvvYGZ0av+/R6OKQH014MMqI2cFK/7x\nP+7StVNv63UyhA06X5lUVrxC/1vomhsOOeHcD71ri9qwFGuGVERZe0z+1RVk\noGe4O7CdE9z5Nq51BA4BgytiSL/7qr2fONKlJihsTADMWnZXB9AkGHhfJAuS\nHCSM2sDASlPXZirCz4/PG9jLJ8e7cIbfvjQ+OaYM2XOJLEQGoQw8q5fw80pa\nwkfT\r\n=bPU0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "engines": {"npm": "6.7.0", "node": "11.14.0"}, "gitHead": "733f447984281d36c8cdc45ee21aea7787a1b1f8", "scripts": {"docs": "jsdoc2md dist/index.js > API.md", "test": "npm run-script test-unit && npm run-script test-e2e", "build": "tsc", "release": "release-it", "test-e2e": "mocha dist/**/*.e2e.spec.js", "test-unit": "mocha dist/**/*.spec.js --exclude dist/**/*.e2e.spec.js", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha dist/**/*.spec.js", "watch-test": "mocha --watch dist/**/*.spec.js", "watch-build": "tsc --watch", "build-and-release": "npm run-script build && npm run-script docs && npm run-script release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"canvas": "^2.4.1"}, "engineStrict": false, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.2.2", "tslint": "^5.20.1", "chart.js": "^2.8.0", "release-it": "^10.4.2", "typescript": "^3.7.2", "@types/node": "^11.15.2", "@types/mocha": "^5.2.7", "tslint-divid": "^1.3.0", "node-memwatch": "^1.0.1", "@types/chart.js": "^2.7.51", "tslint-immutable": "^5.5.2", "jsdoc-to-markdown": "^4.0.1", "source-map-support": "^0.5.16", "tslint-eslint-rules": "^5.4.0", "@types/node-memwatch": "^1.0.0", "chartjs-plugin-annotation": "^0.5.7", "chartjs-plugin-datalabels": "^0.6.0"}, "peerDependencies": {"chart.js": "^2.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_2.4.1_1574169276321_0.4543667959960027", "host": "s3://npm-registry-packages"}}, "2.4.2": {"name": "chartjs-node-canvas", "version": "2.4.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@2.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "4b5c2052c137b1b61c2721a75f92d9b0d9f18531", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-2.4.2.tgz", "fileCount": 30, "integrity": "sha512-DTi9X/LdAmLjiD+Qw4NWL2SYmEmPv8eGti39Peh4vRnWVNBNIFV5/nBDbihiSiMZPpEKi8Uovd6PS8KnQAm6ug==", "signatures": [{"sig": "MEUCIG8OhX6ahy33sqc713F5AtAH38vT7nbGG7cbiFq2D8vsAiEAhZomRVa5tlMZW1UqaHP4mX0ynGfQsV8oDlyYXLUpZcs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 300649, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeMYUdCRA9TVsSAnZWagAAUNoQAKIZFpwQLMpQdW+YEXWg\njGdHZqG/+VzFR0HaxHQOzpqhRhqVhLxpboceiE2dHSzl5glTznNQAouIoEmM\nooYxxXtNp10R1phr0SMnnmfw9U5gpNBGJz6KeE8Yv+x8CLtAQHTRRzyBEQbt\nROUqmHoj6xyY7HnYkhqhyC7RGHSgqeqZwt1jcL8ONQYiX4IsHJOIWKXBpUwE\n9AZWINiDZpH7q3iAczVvMDyyikjVM4xRbGI3fyK2BqX3ILXF7VDbF0VVeYwW\n7jGvCQDkBbCqj0YNMCgTkbDv0MWzOqFJmgp33ofkNTvzpwpy4bb7BWl9g2Hm\nKCDwePnqY/4xnispM3e128JXC6Oo2196K5J10xPluO7hZCmhqI2UTxPwYMZN\nO8h7SYvA9g4rXwFnrpr7A5r0XM5KCUITXytZTVdseetzIp9wDLALIcl6glsD\nN4W2XR9PXfy4XaFxBWNveE64z9QG6WfvJblGExprzz1y+TVg5lGBKtF/pQ1X\nIxgQT0jwOKB+m1+HEMsmZIX+R+AQB9OBGFN0b2Gm1NKlAl25+vQEViOup5is\nUgYCuz3egUB4r9R/e5VSpN9qmQoe8oEnH+Jwaj+joodwiw1KdUEIn993BSlg\n/Xx2Jio6TocwCJ5MYrLcAypyrV4FhlzhUnrWFG61aUb4m7h4PBMVRygiuKLE\n2mt3\r\n=Talt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "engines": {"npm": "6.13.4", "node": "12.14.1"}, "gitHead": "123133e046506cfd726d0afdb892f011766270c8", "scripts": {"docs": "jsdoc2md dist/index.js > API.md", "lint": "tslint -p ./tsconfig.json", "test": "npm run-script test-unit && npm run-script test-e2e", "build": "tsc", "release": "release-it", "test-e2e": "mocha dist/**/*.e2e.spec.js", "test-unit": "mocha --exclude dist/**/*.e2e.spec.js dist/**/*.spec.js", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha dist/**/*.spec.js", "watch-test": "mocha --watch dist/**/*.spec.js", "watch-build": "tsc --watch", "build-and-release": "npm run-script build && npm run-script docs && npm run-script release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"canvas": "^2.6.1"}, "engineStrict": false, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^7.0.1", "pngjs": "^3.4.0", "tslint": "^6.0.0", "wtfnode": "^0.8.1", "chart.js": "^2.9.3", "release-it": "^12.4.3", "typescript": "^3.7.5", "@types/node": "^12.12.25", "@types/mocha": "^5.2.7", "tslint-divid": "^1.3.0", "@types/chart.js": "^2.9.11", "node-resemble-js": "^0.2.0", "tslint-immutable": "^6.0.1", "jsdoc-to-markdown": "^5.0.3", "source-map-support": "^0.5.16", "tslint-eslint-rules": "^5.4.0", "chartjs-plugin-annotation": "^0.5.7", "chartjs-plugin-datalabels": "^0.7.0"}, "peerDependencies": {"chart.js": "^2.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_2.4.2_1580303645050_0.6811035589015435", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "chartjs-node-canvas", "version": "3.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@3.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "22644419ab2186a5e9c73588909e79710da07402", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-3.0.0.tgz", "fileCount": 44, "integrity": "sha512-T19LgC+ImO39jV3oYNapXFlXn/emR/WxjVGo/T1H4f2OsH8N98c6HNKwDJYbsp84p8srDf5y5FYilvic23jDPA==", "signatures": [{"sig": "MEUCIAtKisN6GPBcAciJEGXAMz8Tu8EHvSX5vZ2yC6WdM0MNAiEA8XLbHtjfi6iVvgY77t++R0L0mKggyTmo30hk9Nt350o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4321550, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeQrYNCRA9TVsSAnZWagAAkOkQAJsLLHu108bmk8Q9cqtZ\nIpwaSYlLvolzdHan+7swWpdA8fGLNng/vq4N3BjrI1ahDyfBDAx4HTQCqJ3Q\n2JDyGo1FHkf54sPwZkBSC9WuPca0DfHywAiQ/9/KcLGgIxBG9kNxHWV16Eb4\n0bqyLZeUGABryoidGvrF1hUad3HuW2Np7OQYj3dPoMCUadUOGfw1y2fPOsYi\noUdBNv7MesU9mcRW1wSLAagZ1KUUQVEFMh0iz4sHYkrUanjUKv5+qoDb4gru\ngnm0gozEJLtVCmjJAHMRVzmmpaXUYRRgTUCRyZCHPbdRyswnEPrv6ZriViYS\nGyU10M6MgKWSzB6D2daTCX6qBylZnNvta8ZlDHiOThCpnVYuaacnvgecmxfu\n3VWlATiqEKpGVOzSvntvPhTESYo0hstFXjTXcFzIvymfvEL/yA9NUIBPCUB+\ndpiaPyNDJPljuX/KWUGBOGw9obLHRxMkYzEz0zyhYyFqgo9xhpK7eAS+pY7Y\nKfcPD1helV/ZAzgivz9mkGjBzAsk/nL2/9vjFYeH/hzrETfNh9X/EgJEcu5b\nNbntUjzvvNRV49cH/6enM2uX1JzHoUqwdUMvhjm6JJd5VmWaM/LoOGzKsS+O\nKFNeQ04Yq+GWIVvPEIglYtepJ75/jS3u+G5/KgNjlnqonRgRLhN+HA52TtPn\nPm1M\r\n=oZzE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "engines": {"npm": "6.13.4", "node": "12.14.1"}, "gitHead": "fc8563a4a94cedf282ba65a84d7ca6b0e2bc2221", "scripts": {"docs": "jsdoc2md dist/index.js > API.md", "lint": "tslint -p ./tsconfig.json", "test": "c8 --all mocha dist/**/*.spec.js", "build": "tsc", "clean": "clean-dest -s ./src -d ./dist --file-map ./scripts/clean-dest --permanent --verbose", "release": "release-it", "test-e2e": "mocha dist/**/*.e2e.spec.js", "test-unit": "mocha --exclude dist/**/*.e2e.spec.js dist/**/*.spec.js", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha dist/**/*.spec.js", "watch-test": "nodemon --watch ./dist -e js --exec npm run-script test", "watch-build": "tsc --watch", "watch-clean": "nodemon --watch ./src -e ts --exec npm run-script clean", "build-and-release": "npm run-script build && npm run-script docs && npm run-script release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"tslib": "^1.10.0", "canvas": "^2.6.1"}, "engineStrict": false, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.0.1", "mocha": "^7.0.1", "pngjs": "^3.4.0", "tslint": "^6.0.0", "nodemon": "^2.0.2", "wtfnode": "^0.8.1", "chart.js": "^2.9.3", "clean-dest": "^1.3.0", "release-it": "^12.4.3", "typescript": "^3.7.5", "@types/node": "^12.12.26", "@types/mocha": "^5.2.7", "tslint-divid": "^1.3.0", "@types/chart.js": "^2.9.11", "node-resemble-js": "^0.2.0", "tslint-immutable": "^6.0.1", "jsdoc-to-markdown": "^5.0.3", "source-map-support": "^0.5.16", "tslint-eslint-rules": "^5.4.0", "chartjs-plugin-annotation": "^0.5.7", "chartjs-plugin-datalabels": "^0.7.0"}, "peerDependencies": {"chart.js": "^2.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_3.0.0_1581430284805_0.9573023118055821", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "chartjs-node-canvas", "version": "3.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@3.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "aeca20fa19a9025a90b941b192395d294bcbc695", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-3.0.1.tgz", "fileCount": 45, "integrity": "sha512-n8K3VtrvMvEV6bGB10D4ihFWuL30V2oreqENjunt7o9x1NaurQyv35U5i+FEal4fenBdE1shWDBkQ/tJ/g5MCQ==", "signatures": [{"sig": "MEYCIQCASUJaEAdBgtqPdp/sfo72v+Sp1Y82vz/7CofBs8jvkQIhAMw6Eh1D0wmhDe5GxGBx/1QBTBvUUyyJW9DiODpBFyu+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4321641, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeRQSUCRA9TVsSAnZWagAAFh4P/RK1sdowbo8QEb5tN3gK\nJUPIO82PYeOkov8Q9Vzpz/0BE235vmDDG2y3K8bl31OfF7yB78uBcHHmbP/Y\nG3OMsShoGH58bZWJenTA7fpQCT/cBQwrezLsmT+JTDTXC3o3SOvhMSAQVHs6\n3f4IGXjWxuyhJ6qmu3UAe4iCxsTVbYvK4SAVi869jqW6p6qneeuRcMqUVwXT\n0VurSPtxaCvulYace45Mxzd1pkws2jD0JEDMS2iR8D7t4bLdsrSTYvQwoYt2\n5AKHYZDSVMroyeRm9w+dXj17QpK9Idevp0cmsN1xfCrTxNhrN0p7tOh2gN99\n5HVW6QPThqsR6GiSB1KM+0U0358ezlDrGa1TEE64QKc/RGPtDv/8D/08PmIh\nZe3HzrWjXWJT4Q3ZWOMagx3Qn/vEKE0lcizpwz6qSRK+74wP+uE+T5GJlQx0\ng/lurP7B9VpDF6Z10brNCzay5JN40fwPbcn5VlJRYIlkPhlYZvwIlwsV+XFc\nlK7MW/l+HZHaXxDE4ZK7Fzo7hM+7PfgSV1qtMDh99XhvvvBzG7lpaYVUwO1B\nlBDhZtnN6rWv9b49ElFKrm1IxDqBC2HANtRmjR8TTkUuvJlXXfiATUiiNQI0\nMtq/bodGkyu8jFFAQy9dxNCqrzhWCGjwPtyeiScP1H5ifYk1wgZoCCHs+iOw\nyKKP\r\n=Zp9l\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "engines": {"npm": "6.13.4", "node": "12.14.1"}, "gitHead": "e380858b6e4ae0c2677adb579e3ba445fe7071b6", "scripts": {"docs": "jsdoc2md dist/index.js > API.md", "lint": "tslint -p ./tsconfig.json", "test": "c8 --all mocha dist/**/*.spec.js", "build": "tsc", "clean": "clean-dest -s ./src -d ./dist --file-map ./scripts/clean-dest --permanent --verbose", "release": "release-it", "test-e2e": "mocha dist/**/*.e2e.spec.js", "test-unit": "mocha --exclude dist/**/*.e2e.spec.js dist/**/*.spec.js", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha dist/**/*.spec.js", "watch-test": "nodemon --watch ./dist -e js --exec npm run-script test", "watch-build": "tsc --watch", "watch-clean": "nodemon --watch ./src -e ts --exec npm run-script clean", "build-and-release": "npm run-script build && npm run-script docs && npm run-script release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"tslib": "^1.10.0", "canvas": "^2.6.1"}, "engineStrict": false, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.0.1", "mocha": "^7.0.1", "pngjs": "^3.4.0", "tslint": "^6.0.0", "nodemon": "^2.0.2", "wtfnode": "^0.8.1", "chart.js": "^2.9.3", "clean-dest": "^1.3.0", "release-it": "^12.4.3", "typescript": "^3.7.5", "@types/node": "^12.12.26", "@types/mocha": "^5.2.7", "tslint-divid": "^1.3.0", "@types/chart.js": "^2.9.11", "node-resemble-js": "^0.2.0", "tslint-immutable": "^6.0.1", "jsdoc-to-markdown": "^5.0.3", "source-map-support": "^0.5.16", "tslint-eslint-rules": "^5.4.0", "chartjs-plugin-annotation": "^0.5.7", "chartjs-plugin-datalabels": "^0.7.0"}, "peerDependencies": {"chart.js": "^2.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_3.0.1_1581581460177_0.7874567557240881", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "chartjs-node-canvas", "version": "3.0.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@3.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "f8037880e84df5b7b102813287a79e306bba90b4", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-3.0.2.tgz", "fileCount": 14, "integrity": "sha512-ggCkxbnBelMnkWMXC/8iNjs1nEzALSs02L3SjR9VGakIn3N0gbKgyMu3aOTtgcDxI3Vjq0UgTPv8DaFdNpjM2A==", "signatures": [{"sig": "MEYCIQCgPyQvIK9EWB/i05mCjc/u78Fu80nSLK407Zz7TTKaegIhAMK1zGV4hvBHF+gDELd3xfwDfvQJk2xiLKL/SWR9uBUr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3948235, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeSkiYCRA9TVsSAnZWagAA+ZgP/iuX1ZncrDPq4ru/rrPL\nHFmYQEUQVDl2BdZCAB+pPZX3p+YY4L6GRyXC/hgJCqVcSegI7663NjT7h2px\n7SxmRfoxUtXMvA/UC8toNeF0OtrNpDUYVnzvDCIa89sZU70/EuqPy31s7/T4\nxLtRecODWG24fkyBzAlkoSHQLXjH+i8ND4r7N5gyO9DCDD0rdzOEQaCNVPrs\no0LYzfsyYxa8+Fi0Nn4Zc0M5OJlzqVaV/f4yHa5SA2zLVYlNOy1qo8vKLjSa\nM093pdHGrFI1JBZecIr6wbee+1IAluIFtaBqINTr05q6Jrwn4ObOkvf8VzsF\nBspkKWvyUv2NEGc1bJUY/fw6G1d9yEGrPDFSLmnB8wLAy60IhHbT2E/HJedQ\nWkWAGEiud50ZoR8DchF7ToVJocqNmdkS+g2SCkB/MKrI2W6o7nDeuuT5IKF9\nqfcIBbA+wz1z3tNKQlRv85IH92qw54eRh1tc6g9iMyPXa2pyn1trPKyTZxq9\nf4ukroEZTQDqjmMVHzetfujOP9C5DuzigN+mEf3jbgkttLiRjov6wkab77WH\nlmREYBzRSXMrHi37HCVAHNLESWpAWvKT2YBp3ylCuOlRhyElUykV5L4fXX1s\n0EH29vSjXZTNx/pTR1gW4pwJUQtSKDAaYxTV6qt32SQMNW3fKI/Zd2/j8xtd\njGEZ\r\n=CEBR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "engines": {"npm": "6.13.4", "node": "12.14.1"}, "gitHead": "fd9c87571348b9a55774d9c8c66c4c77b3a5258a", "scripts": {"docs": "jsdoc2md dist/index.js > API.md", "lint": "tslint -p ./tsconfig.json", "test": "c8 --all mocha dist/**/*.spec.js", "build": "tsc", "clean": "clean-dest -s ./src -d ./dist --file-map ./scripts/clean-dest --permanent --verbose", "release": "release-it", "test-e2e": "mocha dist/**/*.e2e.spec.js", "test-unit": "mocha --exclude dist/**/*.e2e.spec.js dist/**/*.spec.js", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha dist/**/*.spec.js", "watch-test": "nodemon --watch ./dist -e js --exec npm run-script test", "watch-build": "tsc --watch", "watch-clean": "nodemon --watch ./src -e ts --exec npm run-script clean", "build-and-release": "npm run-script build && npm run-script docs && npm run-script release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"tslib": "^1.10.0", "canvas": "^2.6.1"}, "engineStrict": false, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.0.1", "mocha": "^7.0.1", "pngjs": "^3.4.0", "tslint": "^6.0.0", "nodemon": "^2.0.2", "wtfnode": "^0.8.1", "chart.js": "^2.9.3", "clean-dest": "^1.3.0", "release-it": "^12.4.3", "typescript": "^3.7.5", "@types/node": "^12.12.26", "@types/mocha": "^5.2.7", "tslint-divid": "^1.3.0", "@types/chart.js": "^2.9.11", "node-resemble-js": "^0.2.0", "tslint-immutable": "^6.0.1", "jsdoc-to-markdown": "^5.0.3", "source-map-support": "^0.5.16", "tslint-eslint-rules": "^5.4.0", "chartjs-plugin-annotation": "^0.5.7", "chartjs-plugin-datalabels": "^0.7.0"}, "peerDependencies": {"chart.js": "^2.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_3.0.2_1581926551475_0.7759448346237274", "host": "s3://npm-registry-packages"}}, "3.0.3": {"name": "chartjs-node-canvas", "version": "3.0.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@3.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "c83d9cd6a066e0401260118135bb8cceb2376a57", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-3.0.3.tgz", "fileCount": 13, "integrity": "sha512-w3PzBpWUJ1xxOdpilZ9+jEbn8WV/nKBZxbhDwkT9qczcTU8CJefDZ+f7vYY5qeuIFX8VlUpaM+XrkHXE+aICYg==", "signatures": [{"sig": "MEUCIHIkTu2ZEsbpvHMlJ3+HRUVGDMc+F78HqYasDilJULLxAiEAuJ3NP0XwO5vvjp04lO/ZrtvoioP9jGgg+OnduDuaLDc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44462, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeSk3vCRA9TVsSAnZWagAANaIP/0yT8+d+pbuNI46IJ3jM\n524zxBorZGBvtUHXSg9NQr4MkzS6YeKeiyKG81EajA41dXrAPSl0ygDq3mBF\nCJ371rvQWq2BMTfd0Q4JOHhkuYicLOFXZz9rg6xKzqVGiR7PCduUkjgln4mL\nyhgzesm75Uil8o/hcg0bJM2FRCi6JT+XyqkQfRaPfg0N0Qe3a3DERczpg49I\ns8Vd6es6rbiUNBmiDfwHVk/1iychK8LprX7xPs2qtFgfPryDLF+IqhSMldO/\nUgKYLSKjStleUyGqyjlWiUWKJN76M3ldK9PuTXLrDplnZ6wb2dU7mmNsOQYE\nsaIKUodXpDBh8pUNI/ULd2K/lItqyjh+xCsYHPVJkh1ElkpAY8LOH0lS0/l8\nkE276Y/P/dAti1RKC9Ueq9cCaEYnwiFeHG89uqeZavKOdHmhbnNjFKiXWrby\nThimObKZLaK8gMfVS/T3tGx12bS2C7hFo9wU74cx83Lt9NhFkrjy5Q98hMUe\nmkuuvukgDfyEQCuXjA985yjZzelaIgHhLkVc+VFg1SFxbJV5XRgo+2vQjp1h\nhykafkIwc4jdHcoxQF7t56OkJxO5tKVsy524WjhNs6Ky98S0MxHhCq5iOfAz\nQbC/nDBSiBskfsA/j+3QI9l0/NBeSLAxmGVx12A7QZQYnz86sruE4s0iEF+F\nnfdp\r\n=/sPk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "engines": {"npm": "6.13.4", "node": "12.14.1"}, "gitHead": "11cd7612d998ea013891636d39887627a7490ede", "scripts": {"docs": "jsdoc2md dist/index.js > API.md", "lint": "tslint -p ./tsconfig.json", "test": "c8 --all mocha dist/**/*.spec.js", "build": "tsc", "clean": "clean-dest -s ./src -d ./dist --file-map ./scripts/clean-dest --permanent --verbose", "release": "release-it", "test-e2e": "mocha dist/**/*.e2e.spec.js", "test-unit": "mocha --exclude dist/**/*.e2e.spec.js dist/**/*.spec.js", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha dist/**/*.spec.js", "watch-test": "nodemon --watch ./dist -e js --exec npm run-script test", "watch-build": "tsc --watch", "watch-clean": "nodemon --watch ./src -e ts --exec npm run-script clean", "build-and-release": "npm run-script build && npm run-script docs && npm run-script release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"tslib": "^1.10.0", "canvas": "^2.6.1"}, "engineStrict": false, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.0.1", "mocha": "^7.0.1", "pngjs": "^3.4.0", "tslint": "^6.0.0", "nodemon": "^2.0.2", "wtfnode": "^0.8.1", "chart.js": "^2.9.3", "clean-dest": "^1.3.0", "release-it": "^12.4.3", "typescript": "^3.7.5", "@types/node": "^12.12.26", "@types/mocha": "^5.2.7", "tslint-divid": "^1.3.0", "@types/chart.js": "^2.9.11", "node-resemble-js": "^0.2.0", "tslint-immutable": "^6.0.1", "jsdoc-to-markdown": "^5.0.3", "source-map-support": "^0.5.16", "tslint-eslint-rules": "^5.4.0", "chartjs-plugin-annotation": "^0.5.7", "chartjs-plugin-datalabels": "^0.7.0"}, "peerDependencies": {"chart.js": "^2.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_3.0.3_1581927919305_0.4598556838733392", "host": "s3://npm-registry-packages"}}, "3.0.4": {"name": "chartjs-node-canvas", "version": "3.0.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@3.0.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "20bd241882e76592fdd537100c0f91528f3f70d5", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-3.0.4.tgz", "fileCount": 13, "integrity": "sha512-mAZYkgmInIq1zYERJV84aFSbqf90rgLOIbMwgEGmEerNY6+RBaTCq5ft/bM27FaV+hlMK9zl2ig3eLv51kKN8w==", "signatures": [{"sig": "MEUCIHpmtvzMfuNojASLsDxa0msgXcvE5BzUKJCbeTdp1yM3AiEAgQ9I1HXvwwWXhEzHWy0PHK85HLorY3brxvs+xy6BkLY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44938, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeVih2CRA9TVsSAnZWagAAkA0P/REARmEgPgEcMgTigtZ2\nfDT5gCLexK7h+vel6Ls3LzpyO7TCari5IdDrRIaxYQNSTslYGBlTSb98ygnh\nzEVuaqmFCRRxUSTxcnEz57sFa28aQO+slANJmFzI209DZfbRIXzE3OCAhLyt\nrzVxrVCqYuwdbQmn4lhXHkWw+dYnJrW6cqM7QeA+F2Xz/4FziSIEv1ABuDlW\nHP7afr3HeziSoAfNXY7QSy4WpCzoS9+dymbK3yO1I/xl451Pd+EK9DnMw9UH\nLZYzJYB1BBl1KzTJ3URl8GOh0Ac9u5b2HkMj05v0jyl9rLvM3y0YzXDCJTjU\natTkcl2CfJ77h4ydMy89gGaMX8xw/VwNbPTwYxug8lU94KCkdbu4U9Mg8yGN\n9GM1ppis0NXr79faWYwR6p8YYP5b3G5yKrtvt54eYe772X+XHtbo/02pzUu6\noi4GaQm1SKbBXT4akj3vp0KvyhCxxwUEzcuhDeVocqIQP/kSZhEl/jWoeJYo\n19F3y35e52N+1123WRVXsZZTcVZvAiO0EEnYcSvSGv8YaDHNJybkW3C5slOL\nQmu0++7+UpUQiHxIkx3vm8UMvNHxTVqs2v0gX4Ej9KouD7q3bjpUBCWC0nfp\ndENY+bxkAsfObP8tqpSBHAQ+wdlx0XvinpdBGa5gpb7JvhV1qYi1vlzFwhOV\nDM+5\r\n=yyvk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index", "types": "./dist/index.d.ts", "engines": {"node": "10.x 11.x 12.x 13.x"}, "gitHead": "e113e4f8dd3ff2890f65aeb9005a8743a5cd83e8", "scripts": {"nvm": "cat .nvmrc | nvm use", "docs": "jsdoc2md dist/index.js > API.md", "lint": "tslint -p ./tsconfig.json", "test": "c8 --all mocha dist/**/*.spec.js", "build": "tsc", "clean": "clean-dest -s ./src -d ./dist --file-map ./scripts/clean-dest --permanent --verbose", "test-e2e": "mocha dist/**/*.e2e.spec.js", "test-unit": "mocha --exclude dist/**/*.e2e.spec.js dist/**/*.spec.js", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha dist/**/*.spec.js", "watch-test": "nodemon --watch ./dist -e js --exec npm run-script test", "watch-build": "tsc --watch", "watch-clean": "nodemon --watch ./src -e ts --exec npm run-script clean", "build-and-release": "npm run-script build && npm run-script docs && npm run-script release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "11.15.0", "dependencies": {"tslib": "^1.10.0", "canvas": "^2.6.1"}, "engineStrict": true, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.0.1", "mocha": "^7.0.1", "pngjs": "^3.4.0", "tslint": "^6.0.0", "nodemon": "^2.0.2", "wtfnode": "^0.8.1", "chart.js": "^2.9.3", "clean-dest": "^1.3.0", "ts-std-lib": "^1.0.1", "typescript": "^3.7.5", "@types/node": "^12.12.26", "@types/mocha": "^5.2.7", "tslint-divid": "^1.3.0", "@types/chart.js": "^2.9.11", "node-resemble-js": "^0.2.0", "tslint-immutable": "^6.0.1", "jsdoc-to-markdown": "^5.0.3", "source-map-support": "^0.5.16", "tslint-eslint-rules": "^5.4.0", "chartjs-plugin-annotation": "^0.5.7", "chartjs-plugin-datalabels": "^0.7.0"}, "peerDependencies": {"chart.js": "^2.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_3.0.4_1582704757656_0.012484136069792706", "host": "s3://npm-registry-packages"}}, "3.0.5": {"name": "chartjs-node-canvas", "version": "3.0.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@3.0.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "aee4e0fe4700e3585f68b0e6575c4d993e945209", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-3.0.5.tgz", "fileCount": 13, "integrity": "sha512-Cozwv1Hw4Cncj9Y2mSKsGGXqHtbjRiSZAwJO+A7ghRP0Uhj659a22xmaKP/dS3ODXLt8O1U6GTfHdkPE7EmJIw==", "signatures": [{"sig": "MEUCICXoRiBrx0hQsndq45bp2uyOHy+RvxD+Wl8THiSYsVa+AiEAvy2dFPz1ocsXsZxfJcV6a5jpA4sXc23hMIxM9DfOeHc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44908, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeVl5MCRA9TVsSAnZWagAAuVkP/08TejRhinSpAONARzLe\nHMiJPmyV4TcwYT8A8wO2ss6vOzQoWS5zqEq71kUtH4IsZimprb/FRVowBpj5\nC6ty52ZoaPe0otmWM+xeMtIZj1aAf19hnRSTnriV8jNCzubPs0PVNlZ8m+Mo\nbtUpShkc5EJtl7q88MqD0bOe/CX4I/zK4e23ZYUrONQEve//g1kkPmagc6Q0\neBBgOvki9NKYKJaRNgJtdnt8xQK3w1UvDg8W4ZEPGqj8VQ1+LEXAxqzF7PR9\nJinErh0yNpibbgt2Sb9DAYqjDbWjSPTv6b1spmpiMjC5/1dvOksUgAPIRt90\nZ9XGJA8Z8VxG03VE24t8takU4KfoFtayc3h6RhJMmDS7vIeFvqSWdgZF58vP\nt7t71khAnNyHFgQiP0kDirysNNz98o6mWDCYZYlJ/I88xpj+66gVjvZql1Eg\ndpmurxAj5V63hu8RUkVlsp4VfCqo1w1Rw0DITz6r5Kg3Rndi7I5NKpdwEATv\n2YF5G+fLZlPVFAoQ11mWoAMvynnvZcdfROB8zEFf+RvGo5FNSrkD0J+vzDa4\nzrG8NxRZA5zyu2/oy5vEJT8KsSpU9mwac8IZe9v/+89daPH8/1kDUGPxdoRV\nzF5sRrYLDpaL/Rct8BKfY/XAXzzTeNw7EWPiHuLCyvKf9NL/krlVXhcO0kP3\nNJ5t\r\n=g3Ho\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index", "types": "./dist/index.d.ts", "gitHead": "b7a1bfe244b6714ee5c3e9b016a3e98bb27834aa", "scripts": {"nvm": "cat .nvmrc | nvm use", "docs": "jsdoc2md dist/index.js > API.md", "lint": "tslint -p ./tsconfig.json", "test": "c8 --all mocha dist/**/*.spec.js", "build": "tsc", "clean": "clean-dest -s ./src -d ./dist --file-map ./scripts/clean-dest --permanent --verbose", "test-e2e": "mocha dist/**/*.e2e.spec.js", "test-unit": "mocha --exclude dist/**/*.e2e.spec.js dist/**/*.spec.js", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha dist/**/*.spec.js", "watch-test": "nodemon --watch ./dist -e js --exec npm run-script test", "watch-build": "tsc --watch", "watch-clean": "nodemon --watch ./src -e ts --exec npm run-script clean", "package-size": "node ./scripts/package-size", "build-and-release": "npm run-script build && npm run-script docs && npm run-script release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "13.9.0", "dependencies": {"tslib": "^1.10.0", "canvas": "^2.6.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.0.1", "mocha": "^7.0.1", "pngjs": "^3.4.0", "tslint": "^6.0.0", "nodemon": "^2.0.2", "wtfnode": "^0.8.1", "chart.js": "^2.9.3", "clean-dest": "^1.3.0", "ts-std-lib": "^1.0.1", "typescript": "^3.7.5", "@types/node": "^12.12.26", "@types/mocha": "^5.2.7", "tslint-divid": "^1.3.0", "@types/chart.js": "^2.9.11", "node-resemble-js": "^0.2.0", "tslint-immutable": "^6.0.1", "jsdoc-to-markdown": "^5.0.3", "source-map-support": "^0.5.16", "tslint-eslint-rules": "^5.4.0", "chartjs-plugin-annotation": "^0.5.7", "chartjs-plugin-datalabels": "^0.7.0"}, "peerDependencies": {"chart.js": "^2.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_3.0.5_1582718540046_0.7255636495798994", "host": "s3://npm-registry-packages"}}, "3.0.6": {"name": "chartjs-node-canvas", "version": "3.0.6", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@3.0.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "45cd61616113ffddb78affa9e74b03ab3dbd6af8", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-3.0.6.tgz", "fileCount": 13, "integrity": "sha512-Pm+CelDDrRZ9z/VPOJqphsopY4A7nm+a2ZzUr928d6/jrjDCE7V0eZqxevF3/vGA2EA/CIPKxPzowOkx4GdHMA==", "signatures": [{"sig": "MEUCIDXrS+QxYz/Kp38vDSfHpFPacpEzZnY4/9Aj0sF/I4S2AiEA0IrOA3HPScXsf5zDnkw5jGwhW9L4O0Gvk80K2hDvBzI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44953, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeVmRgCRA9TVsSAnZWagAAwkcP/3UYGsKhlxoKhJDAyQFV\n0zGhDiI3MrXQu3fC/dlHCTbxZyy8JyX7X8DUJ5DNfpwXZLLbSJdVcVBJVDeJ\nHSBrP+KdBNgUIgrtq8Stzky1ArgqaGYJZt+338T42PBR9PKV7dVz12rHlDvT\n3X1QcYnownb1nroVKSve6E7eZXxo7iODxKYz+5koXBK3RCYw8haKVJePlFZJ\noDPxuk+BK86QLftzbuKido/wdZR8EeUA4w0m+kNcD6/PG5zZw43oNe2w4wYl\nyS8xZMQqe6AUNU1gdTL8gQksr6gtqFMVk6d8hMsGlnd5NHmzGzSCpyOY5gVL\ntmMtQqkB6kNlg+KFLsC1AJsyCBnnsTqXDcTHBCejyXiKfzSiWMSG4ynGwlyU\nBLF/kPs9xCA25TXrvc6BYawIZunnPDhDVaL17eouvD92ueS/Hc6m7yWjqwN+\nkGyr57f4tcLTpgcdiCzPzDKF9jKS+iJgnRJoVEcu4+g5hLqzi51RtM0kNlS2\nQW4vPaDnZdokzKfCXqWunNvD6dUVxC0w3zmSr/cFr8sXPWkeBbEp8ScbBPuL\ntArnZV6FzcFITKywEfR4BGLvroaAfd+uDZJ3tYrfEU0dLMRbqJtBZuZz52OZ\nKU7ruEcSw90rY0n0BKhnGh7QmNexS0x25RP8qVkynOuDUrxwX9WWhhd+Xrmq\npnt+\r\n=1mRq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index", "types": "./dist/index.d.ts", "gitHead": "8eacc3031dfd0973ca3d81839f071f3db3952673", "scripts": {"nvm": "cat .nvmrc | nvm use", "docs": "jsdoc2md dist/index.js > API.md", "lint": "tslint -p ./tsconfig.json", "test": "c8 --all mocha dist/**/*.spec.js", "build": "tsc", "clean": "clean-dest -s ./src -d ./dist --file-map ./scripts/clean-dest --permanent --verbose", "test-e2e": "mocha dist/**/*.e2e.spec.js", "test-unit": "mocha --exclude dist/**/*.e2e.spec.js dist/**/*.spec.js", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha dist/**/*.spec.js", "watch-test": "nodemon --watch ./dist -e js --exec npm run-script test", "watch-build": "tsc --watch", "watch-clean": "nodemon --watch ./src -e ts --exec npm run-script clean", "package-size": "node ./scripts/package-size", "build-and-release": "npm run-script build && npm run-script docs && npm run-script release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "13.9.0", "dependencies": {"tslib": "^1.10.0", "canvas": "^2.6.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.0.1", "mocha": "^7.0.1", "pngjs": "^3.4.0", "tslint": "^6.0.0", "nodemon": "^2.0.2", "wtfnode": "^0.8.1", "chart.js": "^2.9.3", "clean-dest": "^1.3.0", "ts-std-lib": "^1.0.1", "typescript": "^3.7.5", "@types/node": "^12.12.26", "@types/mocha": "^5.2.7", "tslint-divid": "^1.3.0", "@types/chart.js": "^2.9.11", "node-resemble-js": "^0.2.0", "tslint-immutable": "^6.0.1", "jsdoc-to-markdown": "^5.0.3", "source-map-support": "^0.5.16", "tslint-eslint-rules": "^5.4.0", "chartjs-plugin-annotation": "^0.5.7", "chartjs-plugin-datalabels": "^0.7.0"}, "peerDependencies": {"chart.js": "^2.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_3.0.6_1582720096469_0.5745605322786129", "host": "s3://npm-registry-packages"}}, "3.0.7": {"name": "chartjs-node-canvas", "version": "3.0.7", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@3.0.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "32d044c19fc6373adce92706c4ee34397906274f", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-3.0.7.tgz", "fileCount": 14, "integrity": "sha512-xlQ+O7YVInjYb/1N+caACwTt4rsSJepSWGnTlhz1pDXYJ4MxEKsf+2mY6rb7U7Lg2DVdsFK/kKZkJUhiXOrHbQ==", "signatures": [{"sig": "MEYCIQCrKeDgHT8uMNlN0Yq4odSy+JC2w6fMWvQpYSGNTBmUTAIhAJ+Gw/C1O7AeYwB/tMJmIAPULl2tQiIViUrg/RZ5jezh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49253, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgEInQCRA9TVsSAnZWagAAooMQAJOOdYyy1017oiPsBgW0\nQondxQOZ7WLSapugsH1YstqNK1HXPs9XSqyV2IVPWhKu0cPSOMmBo0N5/6rA\n3LG/L7Mdy5WChRZ6PdwyW2Ixmg/5P06PzKkA/AJ9ICeVBh0SPz5ECirYSilb\ngdh5yzGHdcLdy/RcWrBJ8BEfhpEVHnyB1sfp+QRoe5/o/D6RwRo6MBts18/S\ns1e33wglpRDxkgw64XmeDxoqPwRPXZqZkMwRBUWRslsB8VEkauaPRkCcsGJK\nPpQSWTK8GiyAkHzAtkKcyju1cDImI2KRsogS3xZhCrcuVTIpvDA6tTGSMzR7\nNb61k/9tmwvElVN0jXXW7G0PBIMvpls9xAqC9Li9OHtCC8hleQzqFZJSG2JM\nkJMDgGolKGrQ/zUg1KXH0XojJ2HxRDQGX4KWhBx1bltFoFyl/ag5n+TaffWp\n5AWFtJ1uoQAiB2yegfzNFZEpeJpKcgXMflWqpjJG9u+7CC8r9BpGToVx9Gg0\nhO8z/YPQrd59Ywh47znwVUOZDtjLqKzZPE4TZn3ZM1b8cOPlCqh61QIYwiR1\nBvHID+UK2/KM7NilEQwJvz0hP9kjuoedOgtB7svfRa57q/nfxk9uDsdJd703\nrGLQiSUtfwI1RETKrZqnNqsCQJ9ZZP72n5K83G330iDnX+CfydMUp4zqlbuw\naxmJ\r\n=lKpl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index", "types": "./dist/index.d.ts", "gitHead": "f2f31e8fa3d553c7129771c20d3e7162a8a2ecaa", "scripts": {"nvm": "cat .nvmrc | nvm use", "docs": "jsdoc2md dist/index.js > API.md", "lint": "tslint -p ./tsconfig.json", "test": "c8 --all mocha dist/**/*.spec.js", "build": "tsc", "clean": "clean-dest -s ./src -d ./dist --file-map ./scripts/clean-dest --permanent --verbose", "test-e2e": "mocha dist/**/*.e2e.spec.js", "test-unit": "mocha --exclude dist/**/*.e2e.spec.js dist/**/*.spec.js", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha dist/**/*.spec.js", "watch-test": "nodemon --watch ./dist -e js --exec npm run-script test", "watch-build": "tsc --watch", "watch-clean": "nodemon --watch ./src -e ts --exec npm run-script clean", "package-size": "node ./scripts/package-size", "build-and-release": "npm run-script build && npm run-script docs && npm run-script release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"tslib": "^1.14.1", "canvas": "^2.6.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.4.0", "mocha": "^7.2.0", "pngjs": "^3.4.0", "tslint": "^6.1.3", "nodemon": "^2.0.7", "wtfnode": "^0.8.4", "chart.js": "^2.9.4", "clean-dest": "^1.3.3", "release-it": "^14.2.2", "ts-std-lib": "^1.2.1", "typescript": "^3.9.7", "@types/node": "^12.19.15", "@types/mocha": "^7.0.2", "tslint-divid": "^1.3.0", "@types/chart.js": "^2.9.30", "node-resemble-js": "^0.2.0", "tslint-immutable": "^6.0.1", "jsdoc-to-markdown": "^5.0.3", "source-map-support": "^0.5.19", "tslint-eslint-rules": "^5.4.0", "chartjs-plugin-crosshair": "^1.1.6", "chartjs-plugin-annotation": "^0.5.7", "chartjs-plugin-datalabels": "^0.7.0", "chartjs-plugin-piechart-outlabels": "^0.1.4"}, "peerDependencies": {"chart.js": "^2.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_3.0.7_1611696591757_0.2942876134135759", "host": "s3://npm-registry-packages"}}, "3.0.8": {"name": "chartjs-node-canvas", "version": "3.0.8", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@3.0.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "084f363b98d6adf0b66ab932d463a0b297de4efa", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-3.0.8.tgz", "fileCount": 14, "integrity": "sha512-THZxcoPBLL4iRCcGJjBC4YOaavNN4k6hq+7tyojDAkp0cmPCXHuoSB7G/Y/PXrYhAtQ4EkcMENRrN/zizWZHmw==", "signatures": [{"sig": "MEQCIHnK8HsPfaA1HMSySHXTt2lnBF0KtiA8irfvKsYotl/MAiAlauFrZS9UXoX1ModLu7SI+AxTI+NJk+EqQW3x/zpHMw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49253, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgEIoYCRA9TVsSAnZWagAAuMkP/jj/MeRnPT9hHbih9SyL\n3dBdzZJeD1pFN809s+ZPr1Qein0mvQhfNmJAg1dXReaJgO5ovqzSs5oTiOTP\nhT3wnnfRMgpK8wCFAs5nxw6s1HgOALik+uxaydR1EHaDsJ3Fc8UZFtPcO2bE\njo5Q7p2IbNxdc2RUQhhEXIK5g2sxl8lbhQsi+cv8mdiInQ7S32VHlBPo7rMJ\nikT2XYJeOboN9ImJ+LvSaJAkJ6FFo33cGCNKwi70Fc6ToNzHtuZ6YVrAj/zT\nInWFOhYrzItT8qTB29PWJIcwUj1h6pxGA7Qxo7qtquCZRDB6O6n6giJ+JFQF\nvNGCIBnQovyS1B18yv1CoZY7V1mIsm2+MpyYUTawhSsqRrvRj9+5TFDbl79n\nZyA0pyDmqEAcB2jJVQrPXXFvHp6AW/kn/bwqS9DL+TGpZjG5hqLbpLiHR3Ie\ner8vBy08sppCsUKyfb1kr6nH9iH1F7Rt3nlpYXuoHHJZ6TBwqz+V3khuDtJt\nqbS+hyiOZntrf+bbi3rNa5MeUzjzODfx1yPznfIcWIJQ9FqVOLsi+rh22FwF\nGt2f8G6HIllEA4gXK50MxjBvsVauNrBwfyXvI6l5jFuw3f+KavPOqLRCnUWD\npqEMZ9CWSvaRZHJvJmkIhlNs/h3PnNwW+0B4/5nzNUVYvGrizJXtOemJfFIt\n/uA0\r\n=EJaq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index", "types": "./dist/index.d.ts", "gitHead": "374152c24c27336945e8db17044e988201ad2bf3", "scripts": {"nvm": "cat .nvmrc | nvm use", "docs": "jsdoc2md dist/index.js > API.md", "lint": "tslint -p ./tsconfig.json", "test": "c8 --all mocha dist/**/*.spec.js", "build": "tsc", "clean": "clean-dest -s ./src -d ./dist --file-map ./scripts/clean-dest --permanent --verbose", "test-e2e": "mocha dist/**/*.e2e.spec.js", "test-unit": "mocha --exclude dist/**/*.e2e.spec.js dist/**/*.spec.js", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha dist/**/*.spec.js", "watch-test": "nodemon --watch ./dist -e js --exec npm run-script test", "watch-build": "tsc --watch", "watch-clean": "nodemon --watch ./src -e ts --exec npm run-script clean", "package-size": "node ./scripts/package-size", "build-and-release": "npm run-script build && npm run-script docs && npm run-script release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"tslib": "^1.14.1", "canvas": "^2.6.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.4.0", "mocha": "^7.2.0", "pngjs": "^3.4.0", "tslint": "^6.1.3", "nodemon": "^2.0.7", "wtfnode": "^0.8.4", "chart.js": "^2.9.4", "clean-dest": "^1.3.3", "release-it": "^14.2.2", "ts-std-lib": "^1.2.1", "typescript": "^3.9.7", "@types/node": "^12.19.15", "@types/mocha": "^7.0.2", "tslint-divid": "^1.3.0", "@types/chart.js": "^2.9.30", "node-resemble-js": "^0.2.0", "tslint-immutable": "^6.0.1", "jsdoc-to-markdown": "^5.0.3", "source-map-support": "^0.5.19", "tslint-eslint-rules": "^5.4.0", "chartjs-plugin-crosshair": "^1.1.6", "chartjs-plugin-annotation": "^0.5.7", "chartjs-plugin-datalabels": "^0.7.0", "chartjs-plugin-piechart-outlabels": "^0.1.4"}, "peerDependencies": {"chart.js": "^2.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_3.0.8_1611696664341_0.09536869409353299", "host": "s3://npm-registry-packages"}}, "3.0.9": {"name": "chartjs-node-canvas", "version": "3.0.9", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@3.0.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "063659bacd8128042f6844de9dc14d583bd7eab1", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-3.0.9.tgz", "fileCount": 14, "integrity": "sha512-FfGvIBYYou4xUKiLfQKYp2sYO3xFiEZJe5j9snVoY/nbJX0jBlLySA4ufnt2dSpLJigfVgwwmthnO2PdyhSmhQ==", "signatures": [{"sig": "MEUCIQC+lqw4Yc6FUtU4oDGXzdg9MI0vKh/Grp2n47Go6/RBNgIgcV0Y8Av26tZ3gPSV5+psQJaciL5TQSQDOneHPg75+I4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49396, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGb/8CRA9TVsSAnZWagAArboP/RvL2bzjDqPSxq9LxqBL\noUnftakHm1WgxxPMaJwdgHFY3bz6KJ4XeXtT6LPHW+a5ZNetKk/xqjNoRQTv\nBihyZdnfSeJhus4FTvHnBxbwdTuV5deplMNzAuDq+xOo9wSA0HrAMIscbcTh\n/bHBpqm7ydcFf7YASEHpZHdDtnd67wbHCoX8yZf0pDGynQ4bs9ZQI7PDHV1Y\nzp+nPZWcch7cmYa9JPgeFZMW5JT1pP9jrVncPzoCroiOeFBPf4IrvMnf2J+c\nnce9B+FfUxqpGNFZjpz8Ci/JWAlO+V+MmRuX805+2xD8kmD+m64fvH/EiBRu\nrAERdnZIq6B3/s1FCa7xoifoudARBQQRYxfBWK8os8o1NDVGKtW1TYU0l5no\nWoUhs12OruD2Nna7xyeYcYus1Ap1VC+zQv1HbLVsMWUF0t3pu/q6MPdzlCdH\njqYyoR8L6OJtbb9fa1Zp5lTiBPooBWASogwfamLaDZEQgDt3tNOIcl6LqKCn\nRyRPONwULpYzgYSTcwtNKXtcSBGrk3Q63dwtnNCAwk2lqbn1lb2tkyTcToTg\ny1Gy0s78FOvu9cU9SFTOcxHf07DjQ5Hmcl65g/9jTVjhuETM1oXMgM9V6AOn\nUiEMeTZx5wZPn9XNDT8P7vHLGqVJOsFzT4yujqIWxzhw1zOh84iQxcIKQAlj\nc8/u\r\n=4T4y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index", "types": "./dist/index.d.ts", "gitHead": "aee6167406ac7c9001661f9aed5fd5706ee30fa2", "scripts": {"nvm": "cat .nvmrc | nvm use", "docs": "jsdoc2md dist/index.js > API.md", "lint": "tslint -p ./tsconfig.json", "test": "c8 --all mocha dist/**/*.spec.js", "build": "tsc", "clean": "clean-dest -s ./src -d ./dist --file-map ./scripts/clean-dest --permanent --verbose", "test-e2e": "mocha dist/**/*.e2e.spec.js", "test-unit": "mocha --exclude dist/**/*.e2e.spec.js dist/**/*.spec.js", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha dist/**/*.spec.js", "watch-test": "nodemon --watch ./dist -e js --exec npm run-script test", "watch-build": "tsc --watch", "watch-clean": "nodemon --watch ./src -e ts --exec npm run-script clean", "package-size": "node ./scripts/package-size", "build-and-release": "npm run-script build && npm run-script docs && npm run-script release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"tslib": "^1.14.1", "canvas": "^2.6.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.4.0", "mocha": "^7.2.0", "pngjs": "^3.4.0", "tslint": "^6.1.3", "nodemon": "^2.0.7", "wtfnode": "^0.8.4", "chart.js": "^2.9.4", "clean-dest": "^1.3.3", "release-it": "^14.2.2", "ts-std-lib": "^1.2.1", "typescript": "^3.9.7", "@types/node": "^12.19.15", "@types/mocha": "^7.0.2", "tslint-divid": "^1.3.0", "@types/chart.js": "^2.9.30", "node-resemble-js": "^0.2.0", "tslint-immutable": "^6.0.1", "jsdoc-to-markdown": "^5.0.3", "source-map-support": "^0.5.19", "tslint-eslint-rules": "^5.4.0", "chartjs-plugin-crosshair": "^1.1.6", "chartjs-plugin-annotation": "^0.5.7", "chartjs-plugin-datalabels": "^0.7.0", "chartjs-plugin-piechart-outlabels": "^0.1.4"}, "peerDependencies": {"chart.js": "^2.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_3.0.9_1612300284006_0.8635836791770168", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "chartjs-node-canvas", "version": "3.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@3.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "b06d16276c92567b291015a78dcff0c6eea40a30", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-3.1.0.tgz", "fileCount": 14, "integrity": "sha512-yL5hdb8dnhknBuUkz4CGL/rFSNudd8eKmKUsgVfrK5Fy8VswDI+DEfT4t2L/8H8sMRkzpAl42pEbERIghv3b0w==", "signatures": [{"sig": "MEUCIQC/nidgdtAJ/Tnu5hGWubMScKhSyg21bSLgddu54R/3GAIgW9uahcHok33NhQPa/7QcW1aZWaq/55JbhwQLba+Fda4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50577, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGcPeCRA9TVsSAnZWagAADykQAJnC5YA+YOuqQv5nIclz\nZ9ivTkyd4RKmWy79DhJ19sCG65C7U1sWDOjDXn3NWf8Yg+uL+5tqAZGDxsOB\n1tci/nfrausrWF4onQgPMjRl9ZuljVhgVKDQhX9VRQwcfFTLdb/qOkuHMeka\nWr3Q2io9Yzx+YsMshunWdrg3SK02AHKO6slfC7/mC1FpUz/qYJLXQo3J0fWY\n2eGgKYypTPqPerGqqV3uedpkX1MGlCvzdeSnRy32QBt5Q0OqcAeJnChGy8zi\nL5WDjd133RHBQjE7CY5Y+NHAYAolKC4JQNNCDurpgG6gUZPCLl9OT1JnMlB7\nYH3gDNCRFQBTVUEiX9u11kq71nW2KT0XVfEUkEHSCE5lPtt+RUEeqUl/JMqA\nVZ8mvGyMqlVWxivGrRBvRoqAY7iAG5WXdycA1vPkjzFqRuQrvDA4PmyIwUrF\n6HvUuNqPaGXy/Hlphfbwdm74Eaj+N/YEJ3jbiWkZOqK/KveGkp0LRt8bqm5N\n4QQtERz5iuWBPj4qbY9EbnDHHIy3iAoN7pFRTCIHK/ivRCxoXtaGwhNAvatB\nd/CjVvg1aQfS5anwgBpUc7C3C7T6Tc0emF+UEZQiFiOAGhGNBzViKHOEJkgR\nZNHvXZQQBv3A+U7Yu0/yGlzOWdFsnxd8vCwKvTcIKwfCbGIRuZycpKwAtUmv\nRAeD\r\n=Lf/J\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index", "types": "./dist/index.d.ts", "gitHead": "2b2a188c8aff150a609bec77ca10de6ce3315128", "scripts": {"nvm": "cat .nvmrc | nvm use", "docs": "jsdoc2md dist/index.js > API.md", "lint": "tslint -p ./tsconfig.json", "test": "c8 --all mocha dist/**/*.spec.js", "build": "tsc", "clean": "clean-dest -s ./src -d ./dist --file-map ./scripts/clean-dest --permanent --verbose", "test-e2e": "mocha dist/**/*.e2e.spec.js", "test-unit": "mocha --exclude dist/**/*.e2e.spec.js dist/**/*.spec.js", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha dist/**/*.spec.js", "watch-test": "nodemon --watch ./dist -e js --exec npm run-script test", "watch-build": "tsc --watch", "watch-clean": "nodemon --watch ./src -e ts --exec npm run-script clean", "package-size": "node ./scripts/package-size", "build-and-release": "npm run-script build && npm run-script docs && npm run-script release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"tslib": "^1.14.1", "canvas": "^2.6.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.4.0", "mocha": "^7.2.0", "pngjs": "^3.4.0", "tslint": "^6.1.3", "nodemon": "^2.0.7", "wtfnode": "^0.8.4", "chart.js": "^2.9.4", "clean-dest": "^1.3.3", "release-it": "^14.2.2", "ts-std-lib": "^1.2.1", "typescript": "^3.9.7", "@types/node": "^12.19.15", "@types/mocha": "^7.0.2", "tslint-divid": "^1.3.0", "@types/chart.js": "^2.9.30", "node-resemble-js": "^0.2.0", "tslint-immutable": "^6.0.1", "jsdoc-to-markdown": "^5.0.3", "source-map-support": "^0.5.19", "tslint-eslint-rules": "^5.4.0", "chartjs-plugin-crosshair": "^1.1.6", "chartjs-plugin-annotation": "^0.5.7", "chartjs-plugin-datalabels": "^0.7.0", "chartjs-plugin-piechart-outlabels": "^0.1.4"}, "peerDependencies": {"chart.js": "^2.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_3.1.0_1612301278349_0.8211289360342544", "host": "s3://npm-registry-packages"}}, "3.1.1": {"name": "chartjs-node-canvas", "version": "3.1.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@3.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "6316b8a393199f58c03337217185fdbef314fed6", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-3.1.1.tgz", "fileCount": 22, "integrity": "sha512-Thjo1WLagp2QCzxPmYcfwdgVNMHQpa0987ue5mlp9pvyr8BCkDvNusmauiukWEmi66l4J0NRA0BwFASTOKm3lg==", "signatures": [{"sig": "MEUCIC1zlfafhlOHc3eqibJGTUKJ2m/i/fPevQtV7kat0KF7AiEAt1ccqia4M9qNRvGhvkV7TdIrGROAFLqTw2o0koOE8g4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69917, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgpAo1CRA9TVsSAnZWagAAo2YP/jnZgkjGpwjg2HI5Vh9T\n6Bk2PHGVV2wb9mmLrqyjKPZHc/gYUpMLp93K8iRg1GKVxAXYz7vFscOayg/x\nPzasrbam/RHKDExF6Amjaed28zwFt7rtvuyELP0GG1piNbqgcq5VxqBFUH15\nSJBxjMB2G6RojbEfFKpyr7A82i9BXNMuDQOw/Z9FnK/kO8AI+Vi7jqx/RO0a\nDYlijAmvamzmzzMa20UjoEOyzpbZr3uHclZccEbZ6hxsq0D+6V8PBChEAYTD\ngzmi6d3yVrhm8SMoXqZOr50RjH6up1N6xOZT1coHn9IA916nJ4ZnC2ZlFN4S\n/657RkfMewtyhUY+kRQcVdSta5/TJpSLw+ttGy9I2nHBbAkbSkzugmJTrxlC\nvrYG2nlGVorgpnAC+7T1PJyhf093qjJu/0ViRaaPUSWYp9UtieMaS5q5kVYI\neub7JTZjjNb6LIgSXiqFn/OTsUoI3GmNgBf2azCzbRlWngmQQ/ZLKd4vgqk5\ncxgRC0QaDKLZbCCp73nqutNQ+JMH1mB8WLHqrLR3Zm5vBGkA+dsqYSlIa3/m\n0NGjGz9NS5wMwunrPGQCKDieRQvq/y4a0XnCQOw17Ie/HE1VhtUvoLwJz+nM\nFjxeF1Gtxy4+VC9e5KA/Qce7b/DBJbRnV2X/KMsJ9fh7mdBhfg28p0M8K0Tx\n+LQp\r\n=Gy3D\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index", "types": "./dist/index.d.ts", "gitHead": "f38d0e3de0dbc8e17627d30fd2160affd89139b8", "scripts": {"nvm": "cat .nvmrc | nvm use", "docs": "jsdoc2md dist/index.js > API.md", "lint": "tslint -p ./tsconfig.json", "test": "c8 --all mocha dist/**/*.spec.js", "build": "tsc", "clean": "clean-dest -s ./src -d ./dist --file-map ./scripts/clean-dest --permanent --verbose", "test-e2e": "mocha dist/**/*.e2e.spec.js", "test-unit": "mocha --exclude dist/**/*.e2e.spec.js dist/**/*.spec.js", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha dist/**/*.spec.js", "watch-test": "nodemon --watch ./dist -e js --exec npm run-script test", "watch-build": "tsc --watch", "watch-clean": "nodemon --watch ./src -e ts --exec npm run-script clean", "package-size": "node ./scripts/package-size"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"tslib": "^1.14.1", "canvas": "^2.6.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.4.0", "mocha": "^7.2.0", "pngjs": "^3.4.0", "tslint": "^6.1.3", "nodemon": "^2.0.7", "wtfnode": "^0.8.4", "chart.js": "^2.9.4", "clean-dest": "^1.3.3", "release-it": "^14.2.2", "ts-std-lib": "^1.2.1", "typescript": "^3.9.7", "@types/node": "^12.19.15", "@types/mocha": "^7.0.2", "tslint-divid": "^1.3.0", "@types/chart.js": "^2.9.30", "node-resemble-js": "^0.2.0", "tslint-immutable": "^6.0.1", "jsdoc-to-markdown": "^5.0.3", "source-map-support": "^0.5.19", "tslint-eslint-rules": "^5.4.0", "chartjs-plugin-crosshair": "^1.1.6", "chartjs-plugin-annotation": "^0.5.7", "chartjs-plugin-datalabels": "^0.7.0", "chartjs-plugin-piechart-outlabels": "^0.1.4"}, "peerDependencies": {"chart.js": "^2.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_3.1.1_1621363253228_0.9783121981931782", "host": "s3://npm-registry-packages"}}, "3.2.0": {"name": "chartjs-node-canvas", "version": "3.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@3.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "876deff999e3be1a3c182b30c9bafd9dfdd00901", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-3.2.0.tgz", "fileCount": 22, "integrity": "sha512-MT0K28VG8BXwCdh5BdRXNw4nzJWKzcN3t/xgTr8zJ8M6uOXl7hRdtIW8rEUcylkLED8LGsnJmSkcnqlhPy841g==", "signatures": [{"sig": "MEQCIAzHCSapIkKvlXXBYXmt2TzKJB3pOC5LuKj/grs9kNbHAiACtE1RqnA49GGlRRC0FWVpBOGAvxDOSICZpboApEXbbw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69977, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgpAuvCRA9TVsSAnZWagAAY3gP/RFzj2lXDxoulMLDFa2A\n3cxNL5Mt0NT9/kB7eLRlim2EmNlrHZv/6BhZks3er/KmxFKA0cu4hHiYIttr\ndyE+29BbXGl9y4+4OS1IgW0sEzyo6pPoMN+6BurBskVEspOvp50XwWrQMB6o\nZ2lOn4x0YZZui3GdMdaPwlehgPuBvZyDkCu+afuOohgHeVqRwmFj0sYiNen8\nIfSuBfPsnN73kiTQ9ljcp6R55Cu4Jio2tALGfaBh5LX5zPdyYD5t40jrRFwP\n+KQFbeoAEKjIdMS/uVO4atkCMrLEka16NkLemNKVVdBm58ZD3TIgmGprYp7x\nMTDj0/F3k7fKRLy0bhdOeGhAwZ7I+yDD/AzAWMHRAF9473Uhy8TF7l3rOP1I\nVllpqOpH3jKyfAUJ0MNg1l+gFtGDQpDUHaWlUAW9BFW0RHHUI8nILwDazNQ6\nax/T4bW2NDAXp4r3nD2MxBpbNtzEzGeDkG8dTU8XABNCMVFWZ6F9rTInEMRN\nRFHxN01ABkIiCBQrxp4CaSNyqcUOpbNXq7kuBxyuNHeE6/IlK5+0w7gWy4F0\nUQikxTRkPGV2KSlMKuL4vUv4KLuevPiDaFPOqUykf7+xXXTZDAnz7CK8CIlL\nuaLMZ6dFWGjxuvD30y3+NHtRNku2WVK00hviSI1et5VgePyECsbFzMRvpaeQ\nNSZI\r\n=1Q4Y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index", "types": "./dist/index.d.ts", "gitHead": "3d26e1427af5c5874640f416d79829d352d8b28a", "scripts": {"nvm": "cat .nvmrc | nvm use", "docs": "jsdoc2md dist/index.js > API.md", "lint": "tslint -p ./tsconfig.json", "test": "c8 --all mocha dist/**/*.spec.js", "build": "tsc", "clean": "clean-dest -s ./src -d ./dist --file-map ./scripts/clean-dest --permanent --verbose", "test-e2e": "mocha dist/**/*.e2e.spec.js", "test-unit": "mocha --exclude dist/**/*.e2e.spec.js dist/**/*.spec.js", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha dist/**/*.spec.js", "watch-test": "nodemon --watch ./dist -e js --exec npm run-script test", "watch-build": "tsc --watch", "watch-clean": "nodemon --watch ./src -e ts --exec npm run-script clean", "package-size": "node ./scripts/package-size"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"tslib": "^1.14.1", "canvas": "^2.6.1"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.4.0", "mocha": "^7.2.0", "pngjs": "^3.4.0", "tslint": "^6.1.3", "nodemon": "^2.0.7", "wtfnode": "^0.8.4", "chart.js": "^2.9.4", "clean-dest": "^1.3.3", "release-it": "^14.2.2", "ts-std-lib": "^1.2.1", "typescript": "^3.9.7", "@types/node": "^12.19.15", "@types/mocha": "^7.0.2", "tslint-divid": "^1.3.0", "@types/chart.js": "^2.9.30", "node-resemble-js": "^0.2.0", "tslint-immutable": "^6.0.1", "jsdoc-to-markdown": "^5.0.3", "source-map-support": "^0.5.19", "tslint-eslint-rules": "^5.4.0", "chartjs-plugin-crosshair": "^1.1.6", "chartjs-plugin-annotation": "^0.5.7", "chartjs-plugin-datalabels": "^0.7.0", "chartjs-plugin-piechart-outlabels": "^0.1.4"}, "peerDependencies": {"chart.js": "^2.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_3.2.0_1621363631153_0.7490672594811485", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "chartjs-node-canvas", "version": "4.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@4.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "a00c2e00936d5eb64de19f1f3bb19b8cd9e2755a", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-4.0.0.tgz", "fileCount": 14, "integrity": "sha512-tOQbbGzaUb1QoNKi5fPHco8Cq1ZH9ModEwykBJeIM7xdfLBiyrkNBblwGGmaaw2MYM0IreIomPtZbAQ/V3xEmg==", "signatures": [{"sig": "MEYCIQCR1PVue6JoNVa7Wnvdg34cZNfUUN5qFX/WzVvkRmkkTQIhALovTjG8ULAjlasyNxuo+lRHIElwN1eVR19PGr+VI19N", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50788}, "main": "./dist/index", "types": "./dist/index.d.ts", "gitHead": "8110dcbb4cc2810e0ed15ec19607ea7667179c78", "scripts": {"nvm": "cat .nvmrc | nvm use", "docs": "jsdoc2md dist/index.js > API.md", "lint": "tslint -p ./tsconfig.json", "test": "c8 --all mocha dist/**/*.spec.js", "build": "tsc", "clean": "clean-dest -s ./src -d ./dist --file-map ./scripts/clean-dest --permanent --verbose && echo TODO: Delete .tsbuildinfo?", "test-e2e": "mocha dist/**/*.e2e.spec.js", "test-unit": "mocha --exclude dist/**/*.e2e.spec.js dist/**/*.spec.js", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha dist/**/*.spec.js", "watch-test": "nodemon --watch ./dist -e js --exec npm run-script test", "watch-build": "tsc --watch", "watch-clean": "nodemon --watch ./src -e ts --exec npm run-script clean", "package-size": "node ./scripts/package-size"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "14.18.0", "dependencies": {"tslib": "^2.3.1", "canvas": "^2.8.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.10.0", "mocha": "^7.2.0", "tslint": "^6.1.3", "nodemon": "^2.0.13", "wtfnode": "^0.9.1", "chart.js": "^3.5.1", "clean-dest": "^1.3.3", "release-it": "^14.11.6", "resemblejs": "^4.0.0", "ts-std-lib": "^1.2.1", "typescript": "^4.4.3", "@types/node": "^16.10.4", "@types/mocha": "^7.0.2", "tslint-divid": "^1.3.0", "tslint-immutable": "^6.0.1", "@types/resemblejs": "^3.2.1", "jsdoc-to-markdown": "^5.0.3", "source-map-support": "^0.5.20", "tslint-eslint-rules": "^5.4.0", "chartjs-plugin-crosshair": "^1.2.0", "chartjs-plugin-annotation": "^1.0.2", "chartjs-plugin-datalabels": "^2.0.0", "chartjs-plugin-piechart-outlabels": "^0.1.4"}, "peerDependencies": {"chart.js": "^3.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_4.0.0_1634063012884_0.09736743741039255", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "chartjs-node-canvas", "version": "4.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@4.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "f35f3a8b1da7b6c21bf9965a60279525ae6d2eef", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-4.0.1.tgz", "fileCount": 14, "integrity": "sha512-SwGD65bnvpkZ86dyC1NwLuj4n6T/WVsf5VlAUJB6WAZ5ac6/6Frbxw+U9f7QBhO6Uc1LMcyj1nUZ4AUh0OCShQ==", "signatures": [{"sig": "MEQCIGJ8mFCcs+yLjLo5MuWwa5mideJ3exb/AVCjZ6/EqHXYAiAjVJVl75tE1AUYJiI2LbvbXdr6tcUYXP/vLIYCaENaGg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48446}, "main": "./dist/index", "types": "./dist/index.d.ts", "gitHead": "4160fd8928f47050ee1994d6ceabf39497d389fe", "scripts": {"nvm": "cat .nvmrc | nvm use", "docs": "jsdoc2md dist/index.js > API.md", "lint": "tslint -p ./tsconfig.json", "test": "c8 --all mocha dist/**/*.spec.js", "build": "tsc", "clean": "clean-dest -s ./src -d ./dist --file-map ./scripts/clean-dest --permanent --verbose && echo TODO: Delete .tsbuildinfo?", "test-e2e": "mocha dist/**/*.e2e.spec.js", "test-unit": "mocha --exclude dist/**/*.e2e.spec.js dist/**/*.spec.js", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha dist/**/*.spec.js", "watch-test": "nodemon --watch ./dist -e js --exec npm run-script test", "watch-build": "tsc --watch", "watch-clean": "nodemon --watch ./src -e ts --exec npm run-script clean", "package-size": "node ./scripts/package-size"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "14.18.0", "dependencies": {"tslib": "^2.3.1", "canvas": "^2.8.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.10.0", "mocha": "^7.2.0", "tslint": "^6.1.3", "nodemon": "^2.0.13", "wtfnode": "^0.9.1", "chart.js": "^3.5.1", "clean-dest": "^1.3.3", "release-it": "^14.11.6", "resemblejs": "^4.0.0", "ts-std-lib": "^1.2.1", "typescript": "^4.4.3", "@types/node": "^16.10.4", "@types/mocha": "^7.0.2", "tslint-divid": "^1.3.0", "tslint-immutable": "^6.0.1", "@types/resemblejs": "^3.2.1", "jsdoc-to-markdown": "^5.0.3", "source-map-support": "^0.5.20", "tslint-eslint-rules": "^5.4.0", "chartjs-plugin-crosshair": "^1.2.0", "chartjs-plugin-annotation": "^1.0.2", "chartjs-plugin-datalabels": "^2.0.0", "chartjs-plugin-piechart-outlabels": "^0.1.4"}, "peerDependencies": {"chart.js": "^3.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_4.0.1_1634063441449_0.7693826849384624", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "chartjs-node-canvas", "version": "4.1.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@4.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "d54e228ef8c8b024bc0e7a1ca7eb542c20690480", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-4.1.1.tgz", "fileCount": 23, "integrity": "sha512-uvh06rbe25ykOK6V1oM50AsVPKqYMp8JywteqP9UfiZB6fvS/QYOl1BhWqnUyrcq4apaVIjoOizkh59Wo+lF2A==", "signatures": [{"sig": "MEQCIBwQVSdxZVaEqwQK2TncdnKcP8NN0v1V4AJqqowqHAHcAiAPgdJf4aIcpfOkfZj6Mqk/MkSPh9qFdMNgz7/O+ZXwwA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67052}, "main": "./dist/index", "types": "./dist/index.d.ts", "gitHead": "ac2726fbe3e121643468ad1aecd380f02545137a", "scripts": {"nvm": "cat .nvmrc | nvm use", "docs": "jsdoc2md dist/index.js > API.md", "lint": "tslint -p ./tsconfig.json", "test": "c8 --all mocha dist/**/*.spec.js", "build": "tsc", "clean": "clean-dest -s ./src -d ./dist --file-map ./scripts/clean-dest --permanent --verbose && echo TODO: Delete .tsbuildinfo?", "test-e2e": "mocha dist/**/*.e2e.spec.js", "test-unit": "mocha --exclude dist/**/*.e2e.spec.js dist/**/*.spec.js", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha dist/**/*.spec.js", "watch-test": "nodemon --watch ./dist -e js --exec npm run-script test", "watch-build": "tsc --watch", "watch-clean": "nodemon --watch ./src -e ts --exec npm run-script clean", "package-size": "node ./scripts/package-size"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "14.18.0", "dependencies": {"tslib": "^2.3.1", "canvas": "^2.8.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.10.0", "mocha": "^7.2.0", "tslint": "^6.1.3", "nodemon": "^2.0.13", "wtfnode": "^0.9.1", "chart.js": "^3.5.1", "clean-dest": "^1.3.3", "release-it": "^14.11.6", "resemblejs": "^4.0.0", "ts-std-lib": "^1.2.1", "typescript": "^4.4.4", "@types/node": "^16.10.4", "@types/mocha": "^7.0.2", "tslint-divid": "^1.3.0", "tslint-immutable": "^6.0.1", "jsdoc-to-markdown": "^5.0.3", "source-map-support": "^0.5.20", "tslint-eslint-rules": "^5.4.0", "@types/offscreencanvas": "^2019.6.4", "chartjs-plugin-crosshair": "^1.2.0", "chartjs-plugin-annotation": "^1.0.2", "chartjs-plugin-datalabels": "^2.0.0", "chartjs-plugin-piechart-outlabels": "^0.1.4"}, "peerDependencies": {"chart.js": "^3.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_4.1.1_1634510420442_0.410677863915462", "host": "s3://npm-registry-packages"}}, "4.1.3": {"name": "chartjs-node-canvas", "version": "4.1.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@4.1.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "f45b8dd05451f8f81e20a8895a43f30032fa3a28", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-4.1.3.tgz", "fileCount": 23, "integrity": "sha512-aK86n8csYO9sJ5QcuZk5LrVJbY8lrWrnyI94WWgGYapDcTddnAHifA5RKYTybTntjNl0VOQnsy71NVKiumJIPw==", "signatures": [{"sig": "MEYCIQC8Yr5ioqL5QC13c3l3IxU78QQkVJ0NkI+2qpJHLv5iOgIhAO3Qz7WtPHsM8VeAkuKcljel35nKpmg1oOZFN1Kp4tY6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67720}, "main": "./dist/index", "types": "./dist/index.d.ts", "gitHead": "2df5b56be9bb7f950b2b8b5f759518f425b06ffd", "scripts": {"nvm": "cat .nvmrc | nvm use", "docs": "jsdoc2md dist/index.js > API.md", "lint": "tslint -p ./tsconfig.json", "test": "c8 --all mocha dist/**/*.spec.js", "build": "tsc", "clean": "clean-dest -s ./src -d ./dist --file-map ./scripts/clean-dest --permanent --verbose && echo TODO: Delete .tsbuildinfo?", "test-e2e": "mocha dist/**/*.e2e.spec.js", "test-unit": "mocha --exclude dist/**/*.e2e.spec.js dist/**/*.spec.js", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha dist/**/*.spec.js", "watch-test": "nodemon --watch ./dist -e js --exec npm run-script test", "watch-build": "tsc --watch", "watch-clean": "nodemon --watch ./src -e ts --exec npm run-script clean", "package-size": "node ./scripts/package-size"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "14.18.0", "dependencies": {"tslib": "^2.3.1", "canvas": "^2.8.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.10.0", "mocha": "^7.2.0", "tslint": "^6.1.3", "nodemon": "^2.0.13", "wtfnode": "^0.9.1", "chart.js": "^3.5.1", "clean-dest": "^1.3.3", "release-it": "^14.11.6", "resemblejs": "^4.0.0", "ts-std-lib": "^1.2.1", "typescript": "^4.4.4", "@types/node": "^16.10.4", "@types/mocha": "^7.0.2", "tslint-divid": "^1.3.0", "tslint-immutable": "^6.0.1", "jsdoc-to-markdown": "^5.0.3", "source-map-support": "^0.5.20", "tslint-eslint-rules": "^5.4.0", "@types/offscreencanvas": "^2019.6.4", "chartjs-plugin-crosshair": "^1.2.0", "chartjs-plugin-annotation": "^1.0.2", "chartjs-plugin-datalabels": "^2.0.0", "chartjs-plugin-piechart-outlabels": "^0.1.4"}, "peerDependencies": {"chart.js": "^3.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_4.1.3_1634514150733_0.8983498254743352", "host": "s3://npm-registry-packages"}}, "4.1.4": {"name": "chartjs-node-canvas", "version": "4.1.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@4.1.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "6266b0d6f020675479dc593b24fe236c6472f01f", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-4.1.4.tgz", "fileCount": 23, "integrity": "sha512-YMR4VfClpKaiKQ3/RIwU2pxtyalGlX60xXLeyL5sOcn/otM5HuMzBhynDavpqVlLXSfr4CRx6jbmwggLoWl3LA==", "signatures": [{"sig": "MEUCIQDso+zxxv6uFwhkUmkJCRIqmncbOgFup9QYA9BkBSmBcwIgXydze5xa9sAv1eioqswz1Y9MCJgNbZdi4rAxY5DsUgA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68660}, "main": "./dist/index", "types": "./dist/index.d.ts", "gitHead": "71bdda5e8623593930ef15a44515e279c65d259f", "scripts": {"nvm": "cat .nvmrc | nvm use", "docs": "jsdoc2md dist/index.js > API.md", "lint": "tslint -p ./tsconfig.json", "test": "c8 --all mocha dist/**/*.spec.js", "build": "tsc", "clean": "clean-dest -s ./src -d ./dist --file-map ./scripts/clean-dest --permanent --verbose && echo TODO: Delete .tsbuildinfo?", "test-e2e": "mocha dist/**/*.e2e.spec.js", "test-unit": "mocha --exclude dist/**/*.e2e.spec.js dist/**/*.spec.js", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha dist/**/*.spec.js", "watch-test": "nodemon --watch ./dist -e js --exec npm run-script test", "watch-build": "tsc --watch", "watch-clean": "nodemon --watch ./src -e ts --exec npm run-script clean", "package-size": "node ./scripts/package-size"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "14.18.0", "dependencies": {"tslib": "^2.3.1", "canvas": "^2.8.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.10.0", "mocha": "^7.2.0", "tslint": "^6.1.3", "nodemon": "^2.0.13", "wtfnode": "^0.9.1", "chart.js": "^3.5.1", "clean-dest": "^1.3.3", "release-it": "^14.11.6", "resemblejs": "^4.0.0", "ts-std-lib": "^1.2.1", "typescript": "^4.4.4", "@types/node": "^16.10.4", "@types/mocha": "^7.0.2", "tslint-divid": "^1.3.0", "tslint-immutable": "^6.0.1", "jsdoc-to-markdown": "^5.0.3", "source-map-support": "^0.5.20", "tslint-eslint-rules": "^5.4.0", "@types/offscreencanvas": "^2019.6.4", "chartjs-plugin-crosshair": "^1.2.0", "chartjs-plugin-annotation": "^1.0.2", "chartjs-plugin-datalabels": "^2.0.0", "chartjs-plugin-piechart-outlabels": "^0.1.4"}, "peerDependencies": {"chart.js": "^3.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_4.1.4_1634760700350_0.8850602034125532", "host": "s3://npm-registry-packages"}}, "4.1.5": {"name": "chartjs-node-canvas", "version": "4.1.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@4.1.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "04e09a96c06d294257ecfe705e28e31b84e132c4", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-4.1.5.tgz", "fileCount": 23, "integrity": "sha512-cCtaXZdBXdY2WVglsQu3oX5+vTrKLZgB/3hzYuRVR9DI6mGRoVv0Q8IHHUMyZXkv9SippJAW9tP5RgIrilZ3Xw==", "signatures": [{"sig": "MEQCIAaG6QzXwiWTCt70HkCNqRAO8/kzt5iC6aVaGylThDHZAiA87SfIfuchg1m/z3re40/XpRvqfxXhQxcdbRXvzPKovw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68660, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhn9d5CRA9TVsSAnZWagAASEEQAJzT7NfACsiGQ6RAyC1f\nHpFiEvNn85CDsbKZVWCKS9rxTsbGPxC2qesHCB55xAE8YhEoOK5Sfo7oGYGn\njyP/JJOSumHU/IAhTkRju262TUT9P+CVySdofMD/3s46O5Z53mO3ZJtlxu57\nQj7KzdgAyzcbUDVJYKEhfLfIKXEs7Pt+VeU/6O+1NHIPzAaU6FwxKS7RAwKw\nANwl5sIbF3jZeAYt0qErMmFMWuFmGILHVl3DjmzB5AMxPsWqAT2BH5qD7CpB\nGr4TlSCgfEqpHm6xDFJ0wptBKUd0KRCbGaI6BWmMLLmQ0DrvtH3FiTLKm8Qc\nfHwm3G/QO1buVlmPBJDnS7mYIbG6zJhcG/O1ixIM2ir5Fh+bwaNIyKpvu75C\n1Fl5NuLldxSzSbT/hNA7lEPEUUB+s8xnsa2h2gd6KKVdHBE6EPrVxBhIhlAo\nFdce9RiivY15C8ahLBHyI37UdQj+pzWB0/59NdSuxpFIHALmRswMDFCHr3MF\nPl4Hc+DLO0EZXb8LN70QLMi+41lDFeeKjlMfOEdr1r57JHOVweokcW+XizOt\n3qkkWvhQqKkyN3jEVsyOEvVQLWqnxC7rFb4a8IOSscVLJJpj/lCNjPkwqOM9\n9mElLfLlUqbVNIhC0HasOiXvaEBIJ0jeQ2RZQ0ypNJJQvTT2mbeVoi32PPHL\nXHpK\r\n=XniO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index", "types": "./dist/index.d.ts", "gitHead": "d4ecad05b5a0d3cffcbc89cabeec6920d7a230c1", "scripts": {"nvm": "cat .nvmrc | nvm use", "docs": "jsdoc2md dist/index.js > API.md", "lint": "tslint -p ./tsconfig.json", "test": "c8 --all mocha dist/**/*.spec.js", "build": "tsc", "clean": "clean-dest -s ./src -d ./dist --file-map ./scripts/clean-dest --permanent --verbose && echo TODO: Delete .tsbuildinfo?", "test-e2e": "mocha dist/**/*.e2e.spec.js", "test-unit": "mocha --exclude dist/**/*.e2e.spec.js dist/**/*.spec.js", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha dist/**/*.spec.js", "watch-test": "nodemon --watch ./dist -e js --exec npm run-script test", "watch-build": "tsc --watch", "watch-clean": "nodemon --watch ./src -e ts --exec npm run-script clean", "package-size": "node ./scripts/package-size"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"tslib": "^2.3.1", "canvas": "^2.8.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.10.0", "mocha": "^7.2.0", "tslint": "^6.1.3", "nodemon": "^2.0.13", "wtfnode": "^0.9.1", "chart.js": "^3.5.1", "clean-dest": "^1.3.3", "release-it": "^14.11.6", "resemblejs": "^4.0.0", "ts-std-lib": "^1.2.1", "typescript": "^4.4.4", "@types/node": "^16.10.4", "@types/mocha": "^7.0.2", "tslint-divid": "^1.3.0", "tslint-immutable": "^6.0.1", "jsdoc-to-markdown": "^5.0.3", "source-map-support": "^0.5.20", "tslint-eslint-rules": "^5.4.0", "@types/offscreencanvas": "^2019.6.4", "chartjs-plugin-crosshair": "^1.2.0", "chartjs-plugin-annotation": "^1.0.2", "chartjs-plugin-datalabels": "^2.0.0", "chartjs-plugin-piechart-outlabels": "^0.1.4"}, "peerDependencies": {"chart.js": "^3.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_4.1.5_1637865337568_0.3829816927566507", "host": "s3://npm-registry-packages"}}, "4.1.6": {"name": "chartjs-node-canvas", "version": "4.1.6", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "chartjs-node-canvas@4.1.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "dist": {"shasum": "034b0428a8accfa4963dd681497f00f900fefc81", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-4.1.6.tgz", "fileCount": 23, "integrity": "sha512-UQJbPWrvqB/FoLclGA9BaLQmZbzSYlujF4w8NZd6Xzb+sqgACBb2owDX6m7ifCXLjUW5Nz0Qx0qqrTtQkkSoYw==", "signatures": [{"sig": "MEUCIAaCniL3lB0cojXME/+9ESCgmmnfBKXi2LPFmsbC/hpOAiEA/Iozmu2PeAWAvvHadX5gvCjurMnwKVqLl2TZouVGtH0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68626, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh505cCRA9TVsSAnZWagAA8GkP/0Bc88GjVh0qq6wTAdlN\nB/KxI4iIrKDeioH/oeozoirk5YPb5qNddZWjYRr3eVPHvlhaTgMw/qgG1YIT\nyfUZIOIe2PWGy+gzsZ0kc78eNPL3G2nAXWvdzOTf7wXsl8WB+BjCTlJxpUpr\nSn4v+8M/RsFlh5BIHLaj+GNDKK4vstsc3FX+nBPTj97cyhyziYrutHkg4M1i\nokT77ELHt6SIZTXVNx1QhHWeZVszgV+ZAo84Yb6LRiH9vTdBIqn/whv6i2vS\nueyEvDgtY3ODhjtLnwJ+Rbyc0V4X3Y+hlOYK9ZboB+v76UsLefTB3T0X/UTP\nc3FzEGMFDiLkTEOzOA5OXzNiOfSg9S7sLp+HHAxZQ8OdcDjwIJPR9czSDkcU\nT5UXa33NmgeYZtoaV3sZzAan25ijfL3Psrj+ZjUGN8br1toWKYdqTyoQD+4N\nf07HD9StRVuXJVSaIwv6lgykSAnvoEOGyy7uBun8RCQhZRcgwFswJXEGlGN9\nRVGRrntipjFspwYAphPFeU9Tk3Y8piVHsv57kRlMu775/hz7RHQVa6v+CHp3\nogTGlml4vrS2Mkuq97zbcUBXEQGn9J8GdzJtrif9q4buxbfnN6BGJsEQnrv8\nvJYz6nuWYOqjBGK6hPXZcv4pCF447YcVT7mk1Ay85RUxJpHDCryx0QOAvsVQ\nZMlA\r\n=Duel\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index", "types": "./dist/index.d.ts", "gitHead": "e78af54806bb3fad5c5b98c9e40a5f747b06bca5", "scripts": {"nvm": "cat .nvmrc | nvm use", "docs": "jsdoc2md dist/index.js > API.md", "lint": "tslint -p ./tsconfig.json", "test": "c8 --all mocha dist/**/*.spec.js", "build": "tsc", "clean": "clean-dest -s ./src -d ./dist --file-map ./scripts/clean-dest --permanent --verbose && echo TODO: Delete .tsbuildinfo?", "test-e2e": "mocha dist/**/*.e2e.spec.js", "test-unit": "mocha --exclude dist/**/*.e2e.spec.js dist/**/*.spec.js", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha dist/**/*.spec.js", "watch-test": "nodemon --watch ./dist -e js --exec npm run-script test", "watch-build": "tsc --watch", "watch-clean": "nodemon --watch ./src -e ts --exec npm run-script clean", "package-size": "node ./scripts/package-size"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "A node renderer for Chart.js using canvas.", "directories": {}, "_nodeVersion": "16.13.2", "dependencies": {"tslib": "^2.3.1", "canvas": "^2.8.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.10.0", "mocha": "^7.2.0", "tslint": "^6.1.3", "nodemon": "^2.0.13", "wtfnode": "^0.9.1", "chart.js": "^3.5.1", "clean-dest": "^1.3.3", "release-it": "^14.11.6", "resemblejs": "^4.0.0", "ts-std-lib": "^1.2.1", "typescript": "^4.4.4", "@types/node": "^16.10.4", "@types/mocha": "^7.0.2", "tslint-divid": "^1.3.0", "tslint-immutable": "^6.0.1", "jsdoc-to-markdown": "^5.0.3", "source-map-support": "^0.5.20", "tslint-eslint-rules": "^5.4.0", "@types/offscreencanvas": "^2019.6.4", "chartjs-plugin-crosshair": "^1.2.0", "chartjs-plugin-annotation": "^1.0.2", "chartjs-plugin-datalabels": "^2.0.0", "chartjs-plugin-piechart-outlabels": "^0.1.4"}, "peerDependencies": {"chart.js": "^3.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/chartjs-node-canvas_4.1.6_1642548827965_0.21688532558386098", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "chartjs-node-canvas", "version": "5.0.0", "description": "A node renderer for Chart.js using canvas.", "main": "./dist/index", "type": "commonjs", "types": "./dist/index.d.ts", "scripts": {"nvm": "cat .nvmrc | nvm use", "build": "tsc", "clean": "clean-dest -s ./src -d ./dist --file-map ./scripts/clean-dest --permanent --verbose && echo TODO: Delete .tsbuildinfo?", "lint": "echo TODO: Add linting", "test": "c8 --all mocha dist/**/*.spec.js", "test-unit": "mocha --exclude dist/**/*.e2e.spec.js dist/**/*.spec.js", "test-e2e": "mocha dist/**/*.e2e.spec.js", "package-size": "node ./scripts/package-size", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha dist/**/*.spec.js", "watch-build": "tsc --watch", "watch-clean": "nodemon --watch ./src -e ts --exec npm run-script clean", "watch-test": "nodemon --watch ./dist -e js --exec npm run-script test", "docs": "jsdoc2md dist/index.js > API.md"}, "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "repository": {"type": "git", "url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git"}, "license": "MIT", "dependencies": {"canvas": "^3.1.0", "tslib": "^2.8.1"}, "peerDependencies": {"chart.js": "^4.4.8"}, "devDependencies": {"@types/mocha": "^7.0.2", "@types/node": "^16.10.4", "@types/offscreencanvas": "^2019.7.3", "c8": "^7.10.0", "chart.js": "^4.4.8", "chartjs-plugin-annotation": "^3.1.0", "chartjs-plugin-crosshair": "^2.0.0", "chartjs-plugin-datalabels": "^2.2.0", "clean-dest": "^1.3.3", "gifencoder": "^2.0.1", "jsdoc-to-markdown": "^5.0.3", "mocha": "^7.2.0", "nodemon": "^2.0.13", "release-it": "^14.11.6", "resemblejs": "^4.0.0", "source-map-support": "^0.5.20", "ts-std-lib": "^1.2.2", "typescript": "^5.7.3", "wtfnode": "^0.9.1"}, "_id": "chartjs-node-canvas@5.0.0", "gitHead": "b30cce4104855f2709acf698219397ec9cb3ac2a", "_nodeVersion": "21.7.3", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-+Lc5phRWjb+UxAIiQpKgvOaG6Mw276YQx2jl2BrxoUtI3A4RYTZuGM5Dq+s4ReYmCY42WEPSR6viF3lDSTxpvw==", "shasum": "cf44df02088ec316585b83580c7696541560729d", "tarball": "https://registry.npmjs.org/chartjs-node-canvas/-/chartjs-node-canvas-5.0.0.tgz", "fileCount": 38, "unpackedSize": 82039, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQC9HYJkiXSbMhgxtmoEGeXnOIoCkyGa/daPwLyA3zDQhAIhANFNxDnQ0JnOYa5IGhUmw8hFmInv1wWPQpxtB+m/4SUP"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/chartjs-node-canvas_5.0.0_1742848031441_0.9722281663070826"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-03-15T15:27:34.370Z", "modified": "2025-03-24T20:27:11.883Z", "1.0.0": "2018-03-15T15:27:34.501Z", "1.1.0": "2018-03-15T15:54:07.485Z", "1.1.1": "2018-03-15T15:57:37.820Z", "1.1.2": "2018-03-25T13:59:25.826Z", "1.1.3": "2018-03-25T14:32:31.443Z", "1.1.5": "2018-05-03T15:25:16.738Z", "1.1.6": "2018-05-18T10:17:23.532Z", "1.1.8": "2018-12-06T13:26:41.742Z", "1.1.9": "2018-12-06T13:29:39.112Z", "1.1.10": "2018-12-06T14:02:55.224Z", "1.1.11": "2018-12-10T07:50:33.377Z", "2.0.0": "2019-02-13T15:13:09.428Z", "2.0.1": "2019-02-19T08:43:35.211Z", "2.1.0": "2019-03-27T13:31:31.808Z", "2.2.0": "2019-04-03T07:51:01.171Z", "2.2.1": "2019-04-03T09:13:41.176Z", "2.2.2": "2019-04-15T08:59:13.655Z", "2.2.3": "2019-04-15T13:03:26.174Z", "2.2.4": "2019-04-15T13:43:14.180Z", "2.3.0": "2019-04-26T14:11:43.012Z", "2.4.0": "2019-05-10T13:31:32.883Z", "2.4.1": "2019-11-19T13:14:36.582Z", "2.4.2": "2020-01-29T13:14:05.248Z", "3.0.0": "2020-02-11T14:11:24.954Z", "3.0.1": "2020-02-13T08:11:00.365Z", "3.0.2": "2020-02-17T08:02:31.772Z", "3.0.3": "2020-02-17T08:25:19.500Z", "3.0.4": "2020-02-26T08:12:37.844Z", "3.0.5": "2020-02-26T12:02:20.156Z", "3.0.6": "2020-02-26T12:28:16.616Z", "3.0.7": "2021-01-26T21:29:51.970Z", "3.0.8": "2021-01-26T21:31:04.489Z", "3.0.9": "2021-02-02T21:11:24.126Z", "3.1.0": "2021-02-02T21:27:58.493Z", "3.1.1": "2021-05-18T18:40:53.374Z", "3.2.0": "2021-05-18T18:47:11.304Z", "4.0.0": "2021-10-12T18:23:33.041Z", "4.0.1": "2021-10-12T18:30:41.598Z", "4.1.1": "2021-10-17T22:40:20.594Z", "4.1.3": "2021-10-17T23:42:30.896Z", "4.1.4": "2021-10-20T20:11:40.522Z", "4.1.5": "2021-11-25T18:35:37.708Z", "4.1.6": "2022-01-18T23:33:48.124Z", "5.0.0": "2025-03-24T20:27:11.663Z"}, "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "repository": {"type": "git", "url": "git+https://github.com/SeanSobey/ChartjsNodeCanvas.git"}, "description": "A node renderer for Chart.js using canvas.", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "README.md", "readmeFilename": ""}