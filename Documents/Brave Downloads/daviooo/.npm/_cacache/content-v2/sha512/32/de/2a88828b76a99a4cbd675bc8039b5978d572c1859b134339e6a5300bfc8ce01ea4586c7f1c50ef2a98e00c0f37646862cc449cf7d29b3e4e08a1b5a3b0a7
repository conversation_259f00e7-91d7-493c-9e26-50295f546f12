{"_id": "es-errors", "_rev": "4-df089f891c962764c4960eba2190445c", "name": "es-errors", "dist-tags": {"latest": "1.3.0"}, "versions": {"1.0.0": {"name": "es-errors", "version": "1.0.0", "description": "A simple cache for a few of the JS Error constructors.", "main": "index.js", "exports": {"./range": "./range.js", "./syntax": "./syntax.js", "./type": "./type.js", "./package.json": "./package.json"}, "sideEffects": false, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "posttest": "aud --production", "prelint": "evalmd README.md", "lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git' | grep -v dist/)", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/es-errors.git"}, "keywords": ["javascript", "ecmascript", "error", "typeerror", "syntaxerror", "rangeerror"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/es-errors/issues"}, "homepage": "https://github.com/ljharb/es-errors#readme", "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "@types/tape": "^5.6.4", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "eclint": "^2.8.1", "eslint": "^8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.7.4", "typescript": "next"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "engines": {"node": ">= 0.4"}, "_id": "es-errors@1.0.0", "gitHead": "5835d8c23d2f619428b0ef0bd05ef08ecb72e728", "_nodeVersion": "21.6.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-yHV74THqMJUyFKkHyN7hyENcEZM3Dj2a2IrdClY+IT4BFQHkIVwlh8s6uZfjsFydMdNHv0F5mWgAA3ajFbsvVQ==", "shasum": "1936450fb8cff7bffb969335d0e55dfca7279aab", "tarball": "https://registry.npmjs.org/es-errors/-/es-errors-1.0.0.tgz", "fileCount": 14, "unpackedSize": 10370, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCYM4klVhIBx+i7N9nTQ2S6/NLapA18SUy1BFX2Xu14MgIgT7LVJy/cN98YDlzUlR5/p0fVMefGKh+UNxU/73K34vM="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/es-errors_1.0.0_1706982425537_0.8078971843530083"}, "_hasShrinkwrap": false}, "1.1.0": {"name": "es-errors", "version": "1.1.0", "description": "A simple cache for a few of the JS Error constructors.", "main": "index.js", "exports": {".": "./index.js", "./range": "./range.js", "./syntax": "./syntax.js", "./type": "./type.js", "./package.json": "./package.json"}, "sideEffects": false, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "posttest": "aud --production", "prelint": "evalmd README.md", "lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git' | grep -v dist/)", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/es-errors.git"}, "keywords": ["javascript", "ecmascript", "error", "typeerror", "syntaxerror", "rangeerror"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/es-errors/issues"}, "homepage": "https://github.com/ljharb/es-errors#readme", "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "@types/tape": "^5.6.4", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "eclint": "^2.8.1", "eslint": "^8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.7.4", "typescript": "next"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "engines": {"node": ">= 0.4"}, "_id": "es-errors@1.1.0", "gitHead": "0d313ff157891c52a9058ad32f0781826714c024", "types": "./index.d.ts", "_nodeVersion": "21.6.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-ka/z/Hxav2YGgkzSwOp1ugbUk6fgIX5gI69PfRHCvODD+LuVOnV1jHPBWXBNPZqX0O900p2I+IdM9sEbac0BNA==", "shasum": "acecff0a2153327941db643567274bffa880aaf6", "tarball": "https://registry.npmjs.org/es-errors/-/es-errors-1.1.0.tgz", "fileCount": 16, "unpackedSize": 10841, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDFwy7X9wWvPJGKyIlg03rnLovdBOiJ5Zsd9Yiy007rCAiAX7fdrKHVy4Nz/UUSOaTSw9khqnNxXJjd8/TBFmW930g=="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/es-errors_1.1.0_1707116315017_0.369220664386797"}, "_hasShrinkwrap": false}, "1.2.0": {"name": "es-errors", "version": "1.2.0", "description": "A simple cache for a few of the JS Error constructors.", "main": "index.js", "exports": {".": "./index.js", "./range": "./range.js", "./syntax": "./syntax.js", "./type": "./type.js", "./package.json": "./package.json"}, "sideEffects": false, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "posttest": "aud --production", "prelint": "evalmd README.md", "lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git' | grep -v dist/)", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/es-errors.git"}, "keywords": ["javascript", "ecmascript", "error", "typeerror", "syntaxerror", "rangeerror"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/es-errors/issues"}, "homepage": "https://github.com/ljharb/es-errors#readme", "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "@types/tape": "^5.6.4", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "eclint": "^2.8.1", "eslint": "^8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.7.4", "typescript": "next"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "engines": {"node": ">= 0.4"}, "_id": "es-errors@1.2.0", "gitHead": "af93ecb57181ff073a54ecbf98ce44eaf67ebe16", "types": "./index.d.ts", "_nodeVersion": "21.6.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-RK1K0AOWJaoi12wgFsZn9egKajnMZCS842+ieXUnOykJo8tes2jeAYTEgrLsgtMpGQry8GleQF+ySbhQuDtpOA==", "shasum": "5eb0e7072b870a59a9a3fe4c073f53e0dfe16fc4", "tarball": "https://registry.npmjs.org/es-errors/-/es-errors-1.2.0.tgz", "fileCount": 18, "unpackedSize": 11363, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGXDRxfxAXGyzETslBM27SVjyW0xw6qZ3DA5cODirLpvAiBdcBlVmcISwbjrv1RVUpbD4VrdchHc9g4FJ74Y5IBHww=="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/es-errors_1.2.0_1707118443629_0.911362692049142"}, "_hasShrinkwrap": false}, "1.2.1": {"name": "es-errors", "version": "1.2.1", "description": "A simple cache for a few of the JS Error constructors.", "main": "index.js", "exports": {".": "./index.js", "./range": "./range.js", "./ref": "./ref.js", "./syntax": "./syntax.js", "./type": "./type.js", "./package.json": "./package.json"}, "sideEffects": false, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "posttest": "aud --production", "prelint": "evalmd README.md", "lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git' | grep -v dist/)", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/es-errors.git"}, "keywords": ["javascript", "ecmascript", "error", "typeerror", "syntaxerror", "rangeerror"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/es-errors/issues"}, "homepage": "https://github.com/ljharb/es-errors#readme", "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "@types/tape": "^5.6.4", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "eclint": "^2.8.1", "eslint": "^8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.7.4", "typescript": "next"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "engines": {"node": ">= 0.4"}, "_id": "es-errors@1.2.1", "gitHead": "060e1a50cc1002e2f527ccc62a5ba6fe1fcfe382", "types": "./index.d.ts", "_nodeVersion": "21.6.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-pxzkrX6Jf/KtJH+qn7qyy8XQjNLwcLiU2gL6+gozhYXKBE8DoTaeCn3Tobb324QI6Fe3L8FuaVUMUaWtnSCRRw==", "shasum": "84e6f247b599ad90c3c6a5a9aa29f0d76e431586", "tarball": "https://registry.npmjs.org/es-errors/-/es-errors-1.2.1.tgz", "fileCount": 18, "unpackedSize": 11620, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID8/vFN3EJsy5tLcYx0CCRsVqtdAKXnw07Z8HniCiipeAiEA+e+rvMLc7mJeO/8UoptmPkmDIJSnD3Y/Z9C6mV30sls="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/es-errors_1.2.1_1707118542227_0.****************"}, "_hasShrinkwrap": false}, "1.3.0": {"name": "es-errors", "version": "1.3.0", "description": "A simple cache for a few of the JS Error constructors.", "main": "index.js", "exports": {".": "./index.js", "./eval": "./eval.js", "./range": "./range.js", "./ref": "./ref.js", "./syntax": "./syntax.js", "./type": "./type.js", "./uri": "./uri.js", "./package.json": "./package.json"}, "sideEffects": false, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "posttest": "aud --production", "prelint": "evalmd README.md", "lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git' | grep -v dist/)", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/es-errors.git"}, "keywords": ["javascript", "ecmascript", "error", "typeerror", "syntaxerror", "rangeerror"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/es-errors/issues"}, "homepage": "https://github.com/ljharb/es-errors#readme", "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "@types/tape": "^5.6.4", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "eclint": "^2.8.1", "eslint": "^8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.7.4", "typescript": "next"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "engines": {"node": ">= 0.4"}, "_id": "es-errors@1.3.0", "gitHead": "5599cf0ebe75cc3685445f83ca80207a357fdb12", "types": "./index.d.ts", "_nodeVersion": "21.6.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==", "shasum": "05f75a25dab98e4fb1dcd5e1472c0546d5057c8f", "tarball": "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz", "fileCount": 22, "unpackedSize": 12324, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB9oyZ1iz6ddiQ0hSHcteY+wIu25XXiptEEQhkkx3bYGAiBWZenaYPP6g7e44zJTox9VjnaLBcI86WMLI6ZYGtQXXA=="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/es-errors_1.3.0_1707120351218_0.3446157096886493"}, "_hasShrinkwrap": false}}, "time": {"created": "2024-02-03T17:47:05.536Z", "1.0.0": "2024-02-03T17:47:05.695Z", "modified": "2024-02-05T08:05:51.669Z", "1.1.0": "2024-02-05T06:58:35.184Z", "1.2.0": "2024-02-05T07:34:03.799Z", "1.2.1": "2024-02-05T07:35:42.428Z", "1.3.0": "2024-02-05T08:05:51.479Z"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "description": "A simple cache for a few of the JS Error constructors.", "homepage": "https://github.com/ljharb/es-errors#readme", "keywords": ["javascript", "ecmascript", "error", "typeerror", "syntaxerror", "rangeerror"], "repository": {"type": "git", "url": "git+https://github.com/ljharb/es-errors.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/ljharb/es-errors/issues"}, "license": "MIT", "readme": "# es-errors <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\nA simple cache for a few of the JS Error constructors.\n\n## Example\n\n```js\nconst assert = require('assert');\n\nconst Base = require('es-errors');\nconst Eval = require('es-errors/eval');\nconst Range = require('es-errors/range');\nconst Ref = require('es-errors/ref');\nconst Syntax = require('es-errors/syntax');\nconst Type = require('es-errors/type');\nconst URI = require('es-errors/uri');\n\nassert.equal(Base, Error);\nassert.equal(Eval, EvalError);\nassert.equal(Range, RangeError);\nassert.equal(Ref, ReferenceError);\nassert.equal(Syntax, SyntaxError);\nassert.equal(Type, TypeError);\nassert.equal(URI, URIError);\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n## Security\n\nPlease email [@ljharb](https://github.com/ljharb) or see https://tidelift.com/security if you have a potential security vulnerability to report.\n\n[package-url]: https://npmjs.org/package/es-errors\n[npm-version-svg]: https://versionbadg.es/ljharb/es-errors.svg\n[deps-svg]: https://david-dm.org/ljharb/es-errors.svg\n[deps-url]: https://david-dm.org/ljharb/es-errors\n[dev-deps-svg]: https://david-dm.org/ljharb/es-errors/dev-status.svg\n[dev-deps-url]: https://david-dm.org/ljharb/es-errors#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/es-errors.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/es-errors.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/es-errors.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=es-errors\n[codecov-image]: https://codecov.io/gh/ljharb/es-errors/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/ljharb/es-errors/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/ljharb/es-errors\n[actions-url]: https://github.com/ljharb/es-errors/actions\n", "readmeFilename": "README.md"}