{"_id": "is-shared-array-buffer", "_rev": "5-43c737f0ad4797231f36a2740d5e769f", "name": "is-shared-array-buffer", "dist-tags": {"latest": "1.0.4"}, "versions": {"1.0.0": {"name": "is-shared-array-buffer", "version": "1.0.0", "keywords": ["javascript", "ecmascript", "is", "sharedarraybuffer", "shared", "array", "buffer"], "author": {"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-shared-array-buffer@1.0.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-shared-array-buffer#readme", "bugs": {"url": "https://github.com/inspect-js/is-shared-array-buffer/issues"}, "dist": {"shasum": "680348bad562af4b988a0fa78c6b2da6218fd9f4", "tarball": "https://registry.npmjs.org/is-shared-array-buffer/-/is-shared-array-buffer-1.0.0.tgz", "fileCount": 10, "integrity": "sha512-k7ZgRR04d3e/XDDizDZICiI5vIBt2SMLMNHkikFdpkDlIpSg7SLZL7Rz8FRK/8mRwkpRQV4SarZhn4xxiyI9Ug==", "signatures": [{"sig": "MEQCIEMHNlkFoUCMWQ+0MgY3hmEtqNDgVu3WqmsR+rMcaRkjAiBAlp7v11x0mVLn4CkRjKq791BY7GZmRAj+fYgDFxjtQA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8951, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgQXnLCRA9TVsSAnZWagAAe+cP/1e2LTA7Kweyk9A8puPn\nxoM9D2OrkY18Frjytp3aOSRNXB3igfpnu40owcLYr0Y2RwCxS2uWE4Qz7nLk\nVS1vqMOYc/3k3W0Q6w9kYOAbhY6ASzdVQ0o193NkY48frLGv49yZ+m8Xu0/Q\nhYNJG6v6jpRJ422QUa4nUElbnsziRBzE0b4p/HCOa1XoPag2/8pA+vKk4twY\nLC8hPwgbelrV/rxJF6Tyq0TmdWZSIjmzi2TO6oDs1LBQ7QjhSVK4SjuNSgZ0\nnHcMdJhSQ/M2UJrNNtswuh2jIdAOBuzCyvtQ9GpU6Df613CPpO2AihXwEeao\n8awyJyH/JR+xrfUtbJP9Pkk2PSS33ScBO2UdOh7L0qlYsQ8+pI+p87k8Dbbd\nAB69xPYyD9Pn1Wn01MW+Pq0wyCe96ta49572YsHXBy1E2QzGP57nXn8xUi00\nRP6WHJ4r3RDwGPrM66IrBcwOvzkf3Z22b96XxCsId42rymgGBZkIaR8GC38H\n/n4zAbB7z/XjkLHtXpNwhjNUn0dv/KqvQrRUlcGvVInQx/aPL3XDeRkTBL3i\nttLk0D23v1dgyJEoWiHb2SnMJb/LBJoBYDPCIalGMInlx9hKDhngEQARHmkG\nPHeuinJS6/kFXW9qrpu2rbh8oJd0mlnae1nnzJ+3iAAxCIIddSf1vpakirZv\n7QP6\r\n=833a\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "627539ca0d70d4581737a0e07c76dba23e62a142", "scripts": {"lint": "eslint --ext=.js,.mjs .", "test": "npm run tests-only --", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspect-js/is-shared-array-buffer.git", "type": "git"}, "_npmVersion": "7.5.6", "description": "Is this value a JS SharedArrayBuffer?", "directories": {}, "_nodeVersion": "15.10.0", "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^1.1.4", "nyc": "^10.3.2", "tape": "^5.2.2", "eslint": "^7.21.0", "for-each": "^0.3.3", "auto-changelog": "^2.2.1", "object-inspect": "^1.9.0", "es-value-fixtures": "^1.2.1", "@ljharb/eslint-config": "^17.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/is-shared-array-buffer_1.0.0_1614903754788_0.7984358586456366", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "is-shared-array-buffer", "version": "1.0.1", "keywords": ["javascript", "ecmascript", "is", "sharedarraybuffer", "shared", "array", "buffer"], "author": {"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-shared-array-buffer@1.0.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-shared-array-buffer#readme", "bugs": {"url": "https://github.com/inspect-js/is-shared-array-buffer/issues"}, "dist": {"shasum": "97b0c85fbdacb59c9c446fe653b82cf2b5b7cfe6", "tarball": "https://registry.npmjs.org/is-shared-array-buffer/-/is-shared-array-buffer-1.0.1.tgz", "fileCount": 10, "integrity": "sha512-IU0NmyknYZN0rChcKhRO1X8LYz5Isj/Fsqh8NJOSf+N/hCOTwy29F32Ik7a+QszE63IdvmwdTPDd6cZ5pg4cwA==", "signatures": [{"sig": "MEUCIQC5f4Yp7XyQi+BdAE3LIKAep2bHN05EuxqLMCmGhKykiQIgBHGbXkdiP8wo0uHpZz/ja3iQF/8i5NL1ZhEJTZJQPAQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgQXoDCRA9TVsSAnZWagAAgT8P/1aWhWPay2YABEW6hfYR\nbzsEhzPl4hYH24ox4SE3kKzOVjI0m6/rZ8lNXYUsMLRBBLXjObKs2NAVrTa3\n+Sh55p2rq+nFolVXSSOPVJz4+GdZAfE6IKNRLdi5Vt24TTkNc4CRUYjLfQ1b\n24j1K5GTyhot9SK39h2i+SJWX+PFRZDiJfBKWacjd28OlwhFvjd6MTr3g7Xn\njSbh/jvh/iAOGxTiz7gjxS2qKu6whRgZ1xLp+K7SvuXGDcJb85USyxguCnPw\nrejTOV5QEFXgFU0pbA0NYdJJH9NQsazWxC5WNaBaH1/Lhq/ScUHEN6SU6sc8\n8GoI5Uj4x45gE61qcVdPaP7lUMmndT17nHtAYcQ/z8z+Ut9T1t913Kj0nao4\nyuHN6hP4qf/zvBnCX2+KDuqSlP3cAF3q/sOAAyu4SOwchG85Z6G4lE5lV1nd\nPV4asFVdWDZa72Cy/E6c0Ke42xeccAeflRU+BLg7eBekTRqJZrt7ERNU4K5u\nfu5P7RoC0oki3aBB2RvQA6UnQ5aEQGYILi+LUlbNgRy0ywcGos7cm8WIRdcc\nkJLW6paftc/R8Nut+X5mJeevZ3uwBaycE3uzbnL35/RwREt8I9t8lbGjA9of\nu03qCTu5Bxb9hTDFJLmka7eQBG+lzkRe7HKzSrrYb8aOmprMrlu8sKnOsAkw\n+dz9\r\n=wi3+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "ecf0918b796aa7b731e92b596657f5e256901f1a", "scripts": {"lint": "eslint --ext=.js,.mjs .", "test": "npm run tests-only --", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspect-js/is-shared-array-buffer.git", "type": "git"}, "_npmVersion": "7.5.6", "description": "Is this value a JS SharedArrayBuffer?", "directories": {}, "_nodeVersion": "15.10.0", "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^1.1.4", "nyc": "^10.3.2", "tape": "^5.2.2", "eslint": "^7.21.0", "for-each": "^0.3.3", "auto-changelog": "^2.2.1", "object-inspect": "^1.9.0", "es-value-fixtures": "^1.2.1", "@ljharb/eslint-config": "^17.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/is-shared-array-buffer_1.0.1_1614903811399_0.26423357095816535", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "is-shared-array-buffer", "version": "1.0.2", "keywords": ["javascript", "ecmascript", "is", "sharedarraybuffer", "shared", "array", "buffer"], "author": {"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-shared-array-buffer@1.0.2", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-shared-array-buffer#readme", "bugs": {"url": "https://github.com/inspect-js/is-shared-array-buffer/issues"}, "dist": {"shasum": "8f259c573b60b6a32d4058a1a07430c0a7344c79", "tarball": "https://registry.npmjs.org/is-shared-array-buffer/-/is-shared-array-buffer-1.0.2.tgz", "fileCount": 10, "integrity": "sha512-sqN2UDu1/0y6uvXyStCOzyhAjCSlHceFoMKJW8W9EU9cvic/QdsZ0kEU93HEy3IUEFZIiH/3w+AH/UQbPHNdhA==", "signatures": [{"sig": "MEQCIFJHAnp/EXeudHX0rysXkaC5FloBKlPf3fUaIOrZ1he1AiAB6EY5+SxUuWmI0ln7qAxeO4lsGphdgbl2t416Ifkw+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11886, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiR9RQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqdJA//e5/qpTvr0Ku6KvDp03ncIECNp+YLtmMzEoAiTOTQKQrCiYOf\r\n1zhxK2sdZUEi8Ma1DHHyiRuP4HzOXfyyNrWHU2d1Av28ng7npg6ZwVl88Uau\r\n/nLlJXy1qLObaOB+3RAhnNhy4F0x/dQeq6uTX64spOTFV6mhHbucoL8qhCE0\r\n595tQowAaz/PoPhQJ6RvdLnh8wCY/4jE+Pnm1LL6po5JVx8o/FEKXp3fmDPv\r\npnb/OaXsuIQtCw+14J/8cz49eriMIJKYii0GzGpiFq+G318i+YOqE+Y5N/Xd\r\nBA4iC/580ZN5lGYFd1J2ZXj1GdDaSslnzXwN9FDID3yjJEzJedKtrsN7NT8r\r\nEbNrYdlQrfIuHzy2Fke6Z00IM/v9uqHuf9c1Un3No2U8HmlsiKPEbvT2CCtr\r\nq6UF0QdgA3LMUi2BcsD4B5kI/AhYSOCbOQHA4u7bDZDdHwMk/YpyZ5YbaM4u\r\nUJdE+Bmn1IFcM/79u7+i8jzaOG1qkGOhHqh92mQ8ghk2IfbdEwmjlkqmsZm2\r\nULZOFFwtgSgboxuO6ZG2yy7/dxnhiTKbJ/hFUUCSwESK3MEW6MxX9WjvWnDK\r\n2B/PK5egDrw7AIK5lyIuymo1WqA4EEQWfusx1XB0NrYEFxCF7JUjXWSakeN0\r\nfsLcz+LOXeJ+yImZ/y9Wvg+2fU4okkDDY9U=\r\n=fh/C\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "0a8c0a3a53fa882f24f4e378b8c9ed30af35e0e4", "scripts": {"lint": "eslint --ext=.js,.mjs .", "test": "npm run tests-only --", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspect-js/is-shared-array-buffer.git", "type": "git"}, "_npmVersion": "8.6.0", "description": "Is this value a JS SharedArrayBuffer?", "directories": {}, "_nodeVersion": "17.8.0", "dependencies": {"call-bind": "^1.0.2"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.0", "nyc": "^10.3.2", "tape": "^5.5.2", "eslint": "=8.8.0", "for-each": "^0.3.3", "auto-changelog": "^2.4.0", "object-inspect": "^1.12.0", "es-value-fixtures": "^1.2.1", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^20.2.3"}, "_npmOperationalInternal": {"tmp": "tmp/is-shared-array-buffer_1.0.2_1648874576264_0.39821211744790985", "host": "s3://npm-registry-packages"}}, "1.0.3": {"name": "is-shared-array-buffer", "version": "1.0.3", "keywords": ["javascript", "ecmascript", "is", "sharedarraybuffer", "shared", "array", "buffer"], "author": {"url": "http://ljharb.codes", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-shared-array-buffer@1.0.3", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-shared-array-buffer#readme", "bugs": {"url": "https://github.com/inspect-js/is-shared-array-buffer/issues"}, "dist": {"shasum": "1237f1cba059cdb62431d378dcc37d9680181688", "tarball": "https://registry.npmjs.org/is-shared-array-buffer/-/is-shared-array-buffer-1.0.3.tgz", "fileCount": 11, "integrity": "sha512-nA2hv5XIhLR3uVzDDfCIknerhx8XUKnstuOERPNNIinXG7v9u+ohXF67vxm4TPTEPU6lm61ZkwP3c9PCB97rhg==", "signatures": [{"sig": "MEQCIA4e13MviANYnEukVJztdu3yek5hZKRHJbzsXwcf3Qx5AiBWP5pIvbUvBG8eqqa89nvEyPV4XkViMvFMVYJ7qB7KYw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18711}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "93ecae39e6b6e9a51f5b0ea1332915f22599baf2", "scripts": {"lint": "eslint --ext=.js,.mjs .", "test": "npm run tests-only --", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "tsc -p .", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspect-js/is-shared-array-buffer.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Is this value a JS SharedArrayBuffer?", "directories": {}, "sideEffects": false, "_nodeVersion": "21.6.2", "dependencies": {"call-bind": "^1.0.7"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.4", "nyc": "^10.3.2", "tape": "^5.7.5", "eslint": "=8.8.0", "for-each": "^0.3.3", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "next", "@types/node": "^20.11.19", "@types/tape": "^5.6.4", "auto-changelog": "^2.4.0", "object-inspect": "^1.13.1", "@types/for-each": "^0.3.3", "@types/call-bind": "^1.0.5", "es-value-fixtures": "^1.4.2", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.1.0", "@types/object-inspect": "^1.8.4", "available-typed-arrays": "^1.0.7", "@types/es-value-fixtures": "^1.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/is-shared-array-buffer_1.0.3_1708499432237_0.*****************", "host": "s3://npm-registry-packages"}}, "1.0.4": {"name": "is-shared-array-buffer", "version": "1.0.4", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "description": "Is this value a JS SharedArrayBuffer?", "license": "MIT", "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "sideEffects": false, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "lint": "eslint --ext=.js,.mjs .", "postlint": "tsc -p . && attw -P", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only --", "posttest": "npx npm@'>= 10.2' audit --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/is-shared-array-buffer.git"}, "keywords": ["javascript", "ecmascript", "is", "sharedarraybuffer", "shared", "array", "buffer"], "bugs": {"url": "https://github.com/inspect-js/is-shared-array-buffer/issues"}, "homepage": "https://github.com/inspect-js/is-shared-array-buffer#readme", "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/es-value-fixtures": "^1.4.4", "@types/for-each": "^0.3.3", "@types/node": "^20.17.10", "@types/object-inspect": "^1.13.0", "@types/tape": "^5.7.0", "auto-changelog": "^2.5.0", "available-typed-arrays": "^1.0.7", "encoding": "^0.1.13", "es-value-fixtures": "^1.5.0", "eslint": "=8.8.0", "for-each": "^0.3.3", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.3", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "dependencies": {"call-bound": "^1.0.3"}, "publishConfig": {"ignore": [".github/workflows"]}, "engines": {"node": ">= 0.4"}, "_id": "is-shared-array-buffer@1.0.4", "gitHead": "ded256fbfa78fdc7954959671930e71417f4fa7c", "types": "./index.d.ts", "_nodeVersion": "23.4.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-ISWac8drv4ZGfwKl5slpHG9OwPNty4jOWPRIhBpxOoD+hqITiwuipOQ2bNthAzwA3B4fIjO4Nln74N0S9byq8A==", "shasum": "9b67844bd9b7f246ba0708c3a93e34269c774f6f", "tarball": "https://registry.npmjs.org/is-shared-array-buffer/-/is-shared-array-buffer-1.0.4.tgz", "fileCount": 11, "unpackedSize": 17117, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFRwDHAxZpl0/Z92XIF4/LJYE+hGPadYl5h93+yvD08CAiEAisrPyi7+gOMQW3MvyrEN73Wi1suefCfVCEn6ISwq+fc="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/is-shared-array-buffer_1.0.4_1734539276273_0.05339346634983633"}, "_hasShrinkwrap": false}}, "time": {"created": "2021-03-05T00:22:34.788Z", "modified": "2024-12-18T16:27:56.620Z", "1.0.0": "2021-03-05T00:22:34.909Z", "1.0.1": "2021-03-05T00:23:31.533Z", "1.0.2": "2022-04-02T04:42:56.440Z", "1.0.3": "2024-02-21T07:10:32.429Z", "1.0.4": "2024-12-18T16:27:56.450Z"}, "bugs": {"url": "https://github.com/inspect-js/is-shared-array-buffer/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "license": "MIT", "homepage": "https://github.com/inspect-js/is-shared-array-buffer#readme", "keywords": ["javascript", "ecmascript", "is", "sharedarraybuffer", "shared", "array", "buffer"], "repository": {"type": "git", "url": "git+https://github.com/inspect-js/is-shared-array-buffer.git"}, "description": "Is this value a JS SharedArrayBuffer?", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "readme": "# is-shared-array-buffer <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\nIs this value a JS SharedArrayBuffer? This module works cross-realm/iframe, does not depend on `instanceof` or mutable properties, and despite ES6 Symbol.toStringTag.\n\n## Example\n\n```js\nvar assert = require('assert');\nvar isSharedArrayBuffer = require('is-shared-array-buffer');\n\nassert(!isSharedArrayBuffer(function () {}));\nassert(!isSharedArrayBuffer(null));\nassert(!isSharedArrayBuffer(function* () { yield 42; return Infinity; });\nassert(!isSharedArrayBuffer(Symbol('foo')));\nassert(!isSharedArrayBuffer(1n));\nassert(!isSharedArrayBuffer(Object(1n)));\n\nassert(!isSharedArrayBuffer(new Set()));\nassert(!isSharedArrayBuffer(new WeakSet()));\nassert(!isSharedArrayBuffer(new Map()));\nassert(!isSharedArrayBuffer(new WeakMap()));\nassert(!isSharedArrayBuffer(new WeakRef({})));\nassert(!isSharedArrayBuffer(new FinalizationRegistry(() => {})));\nassert(!isSharedArrayBuffer(new ArrayBuffer()));\n\nassert(isSharedArrayBuffer(new SharedArrayBuffer()));\n\nclass MySharedArrayBuffer extends SharedArrayBuffer {}\nassert(isSharedArrayBuffer(new MySharedArrayBuffer()));\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n[package-url]: https://npmjs.org/package/is-shared-array-buffer\n[npm-version-svg]: https://versionbadg.es/inspect-js/is-shared-array-buffer.svg\n[deps-svg]: https://david-dm.org/inspect-js/is-shared-array-buffer.svg\n[deps-url]: https://david-dm.org/inspect-js/is-shared-array-buffer\n[dev-deps-svg]: https://david-dm.org/inspect-js/is-shared-array-buffer/dev-status.svg\n[dev-deps-url]: https://david-dm.org/inspect-js/is-shared-array-buffer#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/is-shared-array-buffer.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/is-shared-array-buffer.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/is-shared-array-buffer.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=is-shared-array-buffer\n[codecov-image]: https://codecov.io/gh/inspect-js/is-shared-array-buffer/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/inspect-js/is-shared-array-buffer/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/inspect-js/is-shared-array-buffer\n[actions-url]: https://github.com/inspect-js/is-shared-array-buffer/actions\n", "readmeFilename": "README.md"}