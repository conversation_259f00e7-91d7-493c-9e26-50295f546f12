{"name": "@npmcli/map-workspaces", "dist-tags": {"latest": "4.0.2"}, "versions": {"0.0.0-pre.0": {"name": "@npmcli/map-workspaces", "version": "0.0.0-pre.0", "dependencies": {"glob": "^7.1.6", "read-package-json-fast": "^1.1.3"}, "devDependencies": {"tap": "^14.10.6", "standard": "^14.3.1", "require-inject": "^1.4.4"}, "dist": {"shasum": "4885f3ce32722a3ccdf9a6b48502b83dee6cd79a", "tarball": "https://registry.npmjs.org/@npmcli/map-workspaces/-/map-workspaces-0.0.0-pre.0.tgz", "fileCount": 5, "integrity": "sha512-Z8x4zZjjnakKU+zau/Xd8lZNn8IbYvioN0YeDv8mqzKGv/qjSCfpe1fixHPj2ZknWrE09tl/IilXBuSyVpk/YA==", "signatures": [{"sig": "MEUCIAOOJ30mdL3EkbGEZR0U1kTBn2F9Qke/j7FlwAu8VCsEAiEAt2g31cAwhxOUuc6P8PYuzPuXbkqPBWqxgbWqlu4BHzg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6023, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJealzMCRA9TVsSAnZWagAA5HsQAIhSFSmyVF+1cNi6QrS1\n0qcm9r4LzvSyFic+MI2M/yOyGo0ebm0pIlM5jnaHOAH5XzAvznolYOvU+6Zu\nvrGdRvq5ffzuuTUZ9n4gi0q5DGq9uwkshsqdbfq7L20uQE11QMJ8k8TAe1CP\ntoNvGnXGWP798fSdsgLyTmf30+xNpGiWeBYl3FP0h7OkHsBp1ZP/vLQ+H29Z\nMCJz1TXRkgNd/8sWiTAD/qtH1aZRJNRc7Va+eWFYNEmxdDHn5LhE2MLSg2ip\nPQoqteoXB7jLANjjfXKzx21QoYafMJXswO2Dfhm5uqdhJw8SrC/zlFufO8pE\nfwJ59BFeT3t+YwKelKOYmQWh3ttJVUIh5d/o7YtUJmn7Xg1Jpx7kJK9+o0sb\nUHUWJDgtjFeBuo5jT5aObLNttBBHtrB9BTNDAXZRgs1GZdOVHSpW9YxaGnu5\n6bf5Gc83FMvT/a6SQkbCeUWK7+UIiq59DoGPDM6k1mk6B3f9N3VdFWqhmERO\nbNwJ398X9jcVeFsZdrwROVgV+LmQdHYwX/C+r0uk36raIzBK4UNT4kZNP5sO\nLjvdX+Fj55T31eY8qTVXw3w/IzBvT+WNeDpPd3hdhPqTO/H7lP5+fbDKmCOY\njgC7OjVymUQQyEyuktjQ/1+T459TSFhPcGNG9XhR/JV5JQLXy4c6GskfBkbW\nGHw/\r\n=aVnj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.0-pre.1": {"name": "@npmcli/map-workspaces", "version": "0.0.0-pre.1", "dependencies": {"glob": "^7.1.6", "minimatch": "^3.0.4", "read-package-json-fast": "^1.1.3", "@npmcli/name-from-folder": "^1.0.1"}, "devDependencies": {"tap": "^14.10.7", "standard": "^14.3.1", "require-inject": "^1.4.4"}, "dist": {"shasum": "b2c38d9a78bf38e799f66d14453e7c9b72928132", "tarball": "https://registry.npmjs.org/@npmcli/map-workspaces/-/map-workspaces-0.0.0-pre.1.tgz", "fileCount": 5, "integrity": "sha512-IovEVdr17hW/Stt0kpPjz1r0ZxRX3RGah7ww3tQpi5NtyOapJwbUffWuWETyQkOjud5soC45mnjOOBtfTggtng==", "signatures": [{"sig": "MEUCIElE3GStev5GWpSy9EUueqUHEBBO29WYyTnGp3tMytFcAiEAwamzHqmhjA4p8Qzh1VQ0Lf74iOuHK09i9okjAKbebAU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8607, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJehj6KCRA9TVsSAnZWagAAQIsP/209IdkGHnKC/4K+NFht\n9JS1My+ETi6PHX5oV3rZ8WXLRUp/5c//CgaPg3KBQCb13LNTkc1tAWkOWAxv\n2bIiHTCQswFdnJ9TmwTjwfDFSkvQ8DnvTgPLpOLIWWTbM/IsQzopm1vgt0VW\nqX7xEI9O3ATPoXnU8SsxBQ7ctSdsjpZpSILrM1e5yDM/sl906pksek8yFld/\nnEHjvFee87lqrEZ9yLy9oPgPx4hZpuXaRavoV33ZpDCqm8u+ICWF0Kh4Se3T\nO5dSMfSg5oNQvjME0OhWLRbGCmePkMzDIlXR+pStu/wN+5j+CTy2X2QsHBit\nARmHzfoAXFqDXy9czAfGtCr3OORGgdruUfhI+heMKyAW0f02mdqe0r9kv5pH\nGS2JWc+DQOWXK3Y+OG7yFK4VeLkYk6UnFI8I8vwF+9GaGYVqFFvvzPqiVQGm\nbmlezd4z0qB507lUNFY4r5NGAhKa+52NcerbXmj+W1zi6FhQZ8EoOl9tOXSM\nJSsNyfFWmRGROWvYCUfzJb2QAyWKMSvbP7l0BhFxkkOyGuzhVAxcPSSSK9yZ\nnXyqVSg4/gUpEHnLlpSt2jvodBj71WZaSJPrsXxPHaMDL7KknWskTj6P7eS9\nm8WFcQmbGP8ad/JmI5UTP7ZI6woFTknlgL33e1Maue+NxL8tGvdKTbZsTWZl\nOv0m\r\n=h08p\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "@npmcli/map-workspaces", "version": "1.0.0", "dependencies": {"glob": "^7.1.6", "minimatch": "^3.0.4", "read-package-json-fast": "^1.2.1", "@npmcli/name-from-folder": "^1.0.1"}, "devDependencies": {"tap": "^14.10.8", "standard": "^14.3.4", "require-inject": "^1.4.4"}, "dist": {"shasum": "ff942d4b8570b71b989e541dc7bce77081981515", "tarball": "https://registry.npmjs.org/@npmcli/map-workspaces/-/map-workspaces-1.0.0.tgz", "fileCount": 5, "integrity": "sha512-eM2bpK/deqDIz0wgQraUpv4YlYynIGm/uoRhH/RFt0z9zhQwFC9snXqCPbRSjA5R5NHkHI0VqgIPjDygqLbWYw==", "signatures": [{"sig": "MEQCIHYNbylPMqHQHTEJx5m8piaEnWPHQiEVFQgRhyCTIBgFAiBiOcAF84aD9yayh5sU1RNqY+AV9A3TtfgORGkcrS+CHQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8671, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfdI/BCRA9TVsSAnZWagAApuoP/jrKzhtYZI0/dDCifONN\noFr3klSlFfW6rmG/dEfVz5iyVSUq9bfn9iqEPUOufkae3KLU32wt4qdrc4ts\ngsb3Vpe3/lPm+rCaCOrTcIW3L/bqNEdHM6KQLB1Ifiu5ccWXg2Vnv4AvQImY\nEn8FgK3BQWVu9fLMo7izrIM4fINNzVb1QNPAkQ5yMAieLq4XBSJ3m+T9ntWu\n7tJ6CJXm70rzuRTlABIqGFwK3Y+vrR/mHtAdHdEeSy5UZEAGJPvd1P2vd1kY\nnHuV2vTWf9TFlgDM9OqRrIBF7T97IygvdXRVulOBOA2UBAS/R8QyBPdmE3k+\n9DcI8/oNuQ2yWKNyjYiF77yngddOBboG8q24YHbXJnj24alWjt390NfYb4TP\nsZDCt+ijQ1yzm/BoTyONtQDdwLqXsn0/ykpb2aaO+/7mH360kfxla9aGXlax\nCt39DorQQVy3L+tti/0EBzadO0g30PKsoFIOUImjTmb5/jZiQ08t8b4rMGv5\n8AtzsAOeIv5eUWpWo6gkJez41pbVCDCzeaI/2OYjDS5hAcgK+6WqbCs3MKeQ\nvz6cn+l4zGxKpOzSFiwq7u0DbzU1W6wFFOOgb47x8yVcWPOtgENCTz35Fr+B\noZDereUviNloQXCgXjIuZXitbJ0tkoH5lMs6qsV0lW7baXDvp0risKR83v6a\nrzKL\r\n=FC+S\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "1.0.1": {"name": "@npmcli/map-workspaces", "version": "1.0.1", "dependencies": {"glob": "^7.1.6", "minimatch": "^3.0.4", "read-package-json-fast": "^1.2.1", "@npmcli/name-from-folder": "^1.0.1"}, "devDependencies": {"tap": "^14.10.8", "standard": "^14.3.4", "require-inject": "^1.4.4"}, "dist": {"shasum": "fbbb7d7dfa650f04b8842df94bbdae26041b60fa", "tarball": "https://registry.npmjs.org/@npmcli/map-workspaces/-/map-workspaces-1.0.1.tgz", "fileCount": 5, "integrity": "sha512-w6mVyJ2ngWV7O7f9zPjLrQzRDv7leFmNLVnewNuhouV1MYxXz61DXn2ja3AQj6xlnIp9Z/0GdV0/Ut14eVT8Vw==", "signatures": [{"sig": "MEUCIQDE9pmmOOPXVCJA6XIhWt9KJkNeu+OFCFkJmYf72hQY8gIgaarQkw8G7pt2t10l48ydilOzRosYjzmBCkTna1WHR9U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8726, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfdKFNCRA9TVsSAnZWagAAO2cP/0Xs8fmaDOaMtHoQnCC+\nn+K+Ks/PslmdL1X1daRsHc63heUPKVJ731EZNlwM20eJKQRFUP3ECH0k928m\nZgBcdNyJ6B/wHnnIYZmfZYoBWVvkCzAIuMSKfmJda7qFztkGXPzbOirCL7yC\nXY04nDcoXnEKq482+XDLM5xLX2gt0wRkZJhJ4IiGWozOSJyz6gcvHq0tEPPx\nmgwVLkxhenT6wIfh79Xc8rIwqpmuGDQzHGSSEJP7BZclahtbfCvLcbdvhF7v\nRF/ZQZRWqaUXwpwQgaiBWk9iTqZOkqE1p90rLf3DfzPjiD+Vl3HKPNugKc/U\nK878Lnk4vXMvc3ynVOL6jK2HmLzpRvXBvTH7dPmUjyHgGcLWb8UbkXggO8qs\n+0VfxlVPjGJMfvxIq2NPrq6yYtIJkgzMXRTJLN/Xwl5FZ4TLj9hxO1Bsv/Qn\nlCzFN0jVs/k4gRhhezY6tQYnbXTrz9q8iNDANu9K9D3WaqVHEc0RL3HC/oc1\nXHeDuoG7J5T3O7UeFVOvV0P8TD8mznKK6N0OUfh9/L1wqfYB9FBB/R5Xriiz\nXzWnfHgdwjb99bsxQhvyyERjywo7ZozXDX7fV+gb43YoyprERLZ7RrMUyrkQ\nj9Cfimj3KdpmSGNtYsFZioWknGnm+6NgeXhdP/9QNnbDENnaH566GBUyXSDg\nG7O1\r\n=7TPu\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "1.0.2": {"name": "@npmcli/map-workspaces", "version": "1.0.2", "dependencies": {"glob": "^7.1.6", "minimatch": "^3.0.4", "read-package-json-fast": "^2.0.1", "@npmcli/name-from-folder": "^1.0.1"}, "devDependencies": {"tap": "^14.10.8", "standard": "^14.3.4", "require-inject": "^1.4.4"}, "dist": {"shasum": "77f2400470cdc1d61731ba34a85c577ade1616b9", "tarball": "https://registry.npmjs.org/@npmcli/map-workspaces/-/map-workspaces-1.0.2.tgz", "fileCount": 5, "integrity": "sha512-12nBSZ0EI/jRtCCjjQXF+1Twvj+ecxtBXFRomrTXR0xWn8oppc/OM53aPpPG5EnQWrKAAnOS/hEIATm6kxKz/A==", "signatures": [{"sig": "MEYCIQCMc5ByLj6i2t9232gZ42GGkfa9s5mhz1IW42SgrtlbeAIhAIrzwMM2PG+aX+Jex8vH/jG9lR2uvTERs78ZR2AmlMe8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8726, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgHWb8CRA9TVsSAnZWagAAUb8QAJpSdmR9Aw2JauZODkeO\nMar4+F/ZJgWfOtMF5UnxSTPiBcMjzaQIChfWBMLKE7G7u1KM+qi9Wt3C+ojE\n5NQKuNS9Zw3LG8JRdIPzNNi4Ccd0LqaIcffbL3/95Tw2AdqJaRQxFuVoiP8g\nQTSxpOv3vTyRDTIaukEFH/ylFjv/y8oy25fAhf4ySBfXzX4SvXgPRddyCjXj\nu94zw/X1huLhTgNJIy3mzBEkgGS4MwyhFH9VWzXkXhNxJXEMr6Zk6jlGDyXy\nO50F2p8/bmvP1EGC4wZaNs7m/KILF91OUAM07oXeKvF7N3Ve1E33QaEuS1cw\nIBiCFT/NBLpqnNqiIjIDRUw+RHkEPwBXnzLBVpB9lCcfG8PYQ/HXGnukggSr\nQRq+2uIk+Qo/FsDGgmsBhgk80k4Z8CYkuAXNTEIUoMIaOMkCZuvOw95aPXsf\niEpx1dYKWdGunBEoAhV9SVi6iaY9ubrOLGFFRo/6Nw8BXV1VNQL0k+vnZe82\nTiWowdx1DNZ9vIOoD23v2V7WE22jHZD0CzBEHkFB73rpqBZ6uxmQgN72z8YU\njGZ7wgwJDIUNfwjBrW+rAYvMre6ohutOeogYXq8nGfDjXxy2q7n+HYKtVbxR\nu+b8zpEjxqcPc7TPlsIymfE8F6fFNE/X7RkJuXH1K1m7Ru/fBxtfRkqgz6g5\nVYcX\r\n=YvUu\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "1.0.3": {"name": "@npmcli/map-workspaces", "version": "1.0.3", "dependencies": {"glob": "^7.1.6", "minimatch": "^3.0.4", "read-package-json-fast": "^2.0.1", "@npmcli/name-from-folder": "^1.0.1"}, "devDependencies": {"tap": "^14.10.8", "standard": "^14.3.4", "require-inject": "^1.4.4"}, "dist": {"shasum": "6072a0794762cf8f572e6080fa66d1bbefa991d5", "tarball": "https://registry.npmjs.org/@npmcli/map-workspaces/-/map-workspaces-1.0.3.tgz", "fileCount": 5, "integrity": "sha512-SdlRlOoQw4WKD4vtb/n5gUkobEABYBEOo8fRE4L8CtBkyWDSvIrReTfKvQ/Jc/LQqDaaZ5iv1iMSQzKCUr1n1A==", "signatures": [{"sig": "MEUCIFZqXSCfmrdWB85vqVAZxJslE1jixTkUexamMzs44+IjAiEAkPePHygXsQahvRONNhPv5cto0OW+ngtfyIYx/r8/mGI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8758, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgJfwuCRA9TVsSAnZWagAArkYQAJ35WDR3rSV6+5nne++x\n5U2qmJVW9MR+caNPM3PjyYNPKLpddZi8L5aNQNO96RPtIit8yYTARzYYLyEQ\n3vK00wVVnIqCzANLgd/ax79FU9moga9bGGtaP5mYt2rBn67WlPqGuK82kDD9\n5UozKw2Lnh1QT8ouTOCgytY9UnP7/V2HmCXP03iN8WwGgi0MRO7AWjGuPC2t\nWzAefza/ZH01jSBinDT8+0FfV4COl6J4aPWtDXEezktqrM4iJygxcYDf4m/S\nUd3vRr6iSXza+8OMPD6ncQxvLQeOQGaBL/JFe9F/aJWahmMZdkhAiWRbNHi2\ndteS+5JpCc1+pFVXRLmCqn/KXabks2Z48mBxzmvT01eek2MXMFRuimH/QUFa\nTZWs99LOwfv3EPMjI610jsxYn2fHFnuAxP4zGlSAFRMYlXHAKfufUxRODNPS\nmbtaJaJl7YCVPdJW/Fw/7+93oNnAn+pQ6HpdfDSwAZoGbWNXsXl+6LOEY2y+\nxTIwC1nWEolfxgXPd2REYR2DxYIwWpBLSU8AH/nRqKhiBK56zYdkuZ4IVndS\nlySXRBNSsknBh8WEmxTxUEuhp1htKipqEfUWmFm+KUqFTHAw3Lj/xAT/Tbpb\nRBn37uZ4jktWDZ9XIrH7KlRX8bUdXL4x+9xzw5mapdQAenFab91joTvpgSuA\nW4Kq\r\n=ySzS\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "1.0.4": {"name": "@npmcli/map-workspaces", "version": "1.0.4", "dependencies": {"glob": "^7.1.6", "minimatch": "^3.0.4", "read-package-json-fast": "^2.0.1", "@npmcli/name-from-folder": "^1.0.1"}, "devDependencies": {"tap": "^14.10.8", "standard": "^14.3.4", "require-inject": "^1.4.4"}, "dist": {"shasum": "915708b55afa25e20bc2c14a766c124c2c5d4cab", "tarball": "https://registry.npmjs.org/@npmcli/map-workspaces/-/map-workspaces-1.0.4.tgz", "fileCount": 4, "integrity": "sha512-wVR8QxhyXsFcD/cORtJwGQodeeaDf0OxcHie8ema4VgFeqwYkFsDPnSrIRSytX8xR6nKPAH89WnwTcaU608b/Q==", "signatures": [{"sig": "MEQCIDsrIxNRbpcMs8XUo6d2Knr3z5c/WN9PhtAjE7PZFgycAiAbIrtiHngfylef2eSQJuRc9cr5XNcBXwIq8/LkPJ3dnQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8898, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhFAlJCRA9TVsSAnZWagAAc0UQAIu5uufRpqDZp2FQHn1s\nHP2WG6zkgs+U8iXIGNUcaHyl913tfNfP7IrEDdHCd/oe2ijDUprRwh1sgDPx\neeM2QyCB3e/4YYYDul7nycoMjRnLp+oTrGvgjfEzUdaXbYj+EJXzpW7Ulqa6\naLF41k7cAFG6UD8V39gDBpIsvdRfm1HUXBrRarB24dm34xhiINa0Wb/Ryq2r\nXj4Is294V3QoePXK4SQlRYfjNr0us6gwMviIUdfbxbpNk8/mK1UYT6y0DxzL\nfD0eQmsqk3ONTp4mcschK0S/5t9iPnYvjZJHzrZKssaPgQCZRLHbZsuxlv6k\nC5pJUc77+6dTIx788K1kCt7CvgxhY/txDQqJ52XUBcHHsqBao1NiED2MmCqL\nCrRfEU6xPnI5369JdNKtkalanBw1SmlKH0r6jDQqBD1X7gma4pYtcCjrYl45\nYd7Ur+WldqVpVn1+6OMf4bxuZ9SgFSgTh40HVFgp5rgly9YFPsfRD/I/iEAU\nXs40XNtO4bg+tvvp8coj7NyFzPuOfW+9H3JeE/s6kK+FZ80IuUG5i2dDhoyf\nvmCtKY9JG85lIOyjmR5Zv+S6r5Yy87l+JVOH2Qlg6j/+woz09ihYMqhg+hzC\nCJoEsQw6qNKviAvHw3gRTUiuvCENVVqCaDxBkicwKAfD9j+nAwTOSbpIeP9f\n/ywi\r\n=vR7i\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "2.0.0": {"name": "@npmcli/map-workspaces", "version": "2.0.0", "dependencies": {"glob": "^7.1.6", "minimatch": "^3.0.4", "read-package-json-fast": "^2.0.1", "@npmcli/name-from-folder": "^1.0.1"}, "devDependencies": {"tap": "^15.0.10", "eslint": "^8.0.0", "require-inject": "^1.4.4", "@npmcli/template-oss": "^2.0.0"}, "dist": {"shasum": "e342efbbdd0dad1bba5d7723b674ca668bf8ac5a", "tarball": "https://registry.npmjs.org/@npmcli/map-workspaces/-/map-workspaces-2.0.0.tgz", "fileCount": 4, "integrity": "sha512-QBJfpCY1NOAkkW3lFfru9VTdqvMB2TN0/vrevl5xBCv5Fi0XDVcA6rqqSau4Ysi4Iw3fBzyXV7hzyTBDfadf7g==", "signatures": [{"sig": "MEYCIQDlrNo5cmJf/nCQD3p/xYOfwFxnLjFwxaE3+1vcGSJo2AIhAPgicEaJt8kwenoZXAWjVijE2eULQNoUzQ1ErN/eTgE6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8969}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "2.0.1": {"name": "@npmcli/map-workspaces", "version": "2.0.1", "dependencies": {"glob": "^7.2.0", "minimatch": "^5.0.0", "read-package-json-fast": "^2.0.3", "@npmcli/name-from-folder": "^1.0.1"}, "devDependencies": {"tap": "^15.1.6", "eslint": "^8.9.0", "@npmcli/template-oss": "^2.7.1"}, "dist": {"shasum": "da8b4d2e1f4cef30efcc81e425bd11a9bf5489f2", "tarball": "https://registry.npmjs.org/@npmcli/map-workspaces/-/map-workspaces-2.0.1.tgz", "fileCount": 4, "integrity": "sha512-awwkB/tSWWaCD8F0IbawBdmoPFlbXMaEPN9LyTuJcyJz404/QhB4B/vhQntpk6uxOAkM+bxR7qWMJghYg0tcYQ==", "signatures": [{"sig": "MEUCIGZHx0BbKXVvYX/WVkvTigvlV89DhmdO0r5cwm/1bWOuAiEAi9OUOA6zQly/sy7vAy304jgLvEhe0SCAh0HqxFgo/Fg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9043, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDoJgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoFpw/9HAUcNUwIWOmWCx8dWGxU0yOQiClzY5k8hoPQlEkurX4zSJyt\r\nGBFRUtfem1umAgrBzD6M6ql0qHBZzODVluWSoJHPnq6chDJrJrf9gtoQnjtK\r\nCZZa1pJ8aHBILResNRjuTCse01qYJ9noWN9I02kSlMsKQzVHjoLK+rfB899r\r\n+iuzfGTs4cYgrY27ynFQmgaGVhkVowjuVNQwt4T5rcxNdZPlN/Dlkzp4zI8A\r\nMFp4FU0qPRM0R9ngxMT6ri7pqxvhoQTPvXYj+2LelQqjocvg4PmUJswbXWIK\r\nTg09MazjrQ4sdjNy0mvPekMStAVeLOZpkPrgLreD22v9+e4J+5nNz81aShXF\r\nTuOEq0YocfXqmFOOMQS1nqMgwKnkpUKUFso3Vz7rWkaHJTdJgf3fZ6dg9Otc\r\nRlib8IQLPHVdoticIIblyRxHo3obxPlh0j4Y2STbVrfpoObvAT7J1lYg77Z2\r\n9fjnkBPsgbehvG0VdbFbeoySsGiMPcnW0TPTSlcmlvykPZgB8ZCEMH1NAfAd\r\nVNoWO75hQNbauvOdo8aNviDfBerWIlHM+k7hF2bpR89oVuz5j2qj5lWyanpm\r\nQIb2f14bKviaC9e16UYVc2JJgz0PPkmfIrl5nPDi7j5y99wJEPPy8RgrEdMI\r\nB9k25gSiS2ULF7w7+yp15qEXZtL3n7CcGgw=\r\n=ITjt\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "2.0.2": {"name": "@npmcli/map-workspaces", "version": "2.0.2", "dependencies": {"glob": "^7.2.0", "minimatch": "^5.0.1", "read-package-json-fast": "^2.0.3", "@npmcli/name-from-folder": "^1.0.1"}, "devDependencies": {"tap": "^15.1.6", "eslint": "^8.10.0", "@npmcli/template-oss": "^2.9.2"}, "dist": {"shasum": "dfc87ced615afeb98a081da2aa9bba072bf6712d", "tarball": "https://registry.npmjs.org/@npmcli/map-workspaces/-/map-workspaces-2.0.2.tgz", "fileCount": 4, "integrity": "sha512-ED54EslGsHFWBPN5x8JAOszuWywuoXYSi9E3HQRsgVkWnqsdTBJDSM4IFMRwmmBUbCHAxmP3wGLu1WMm4fhrOw==", "signatures": [{"sig": "MEYCIQDGrmBU6FJsBnaEFEB+0JUYQ9rpmoCpcjFDokLWOTCAtgIhAOfNTLORiLJAGGGJVHxGS28xzbtjEq2okrNqRyZZjPGm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9557, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiKjy3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqu4BAAhuZmVP6VD1EXVgK9uPeAtz12Xl+HoP/9wQrqfNZjHt+oLSRr\r\nGr865iLluHddjE0gn/8T42jOvn80QkJwQmAmPlWIWQ9YgNlmSlAYLDurKtmn\r\nQ66fpfM0LFvj3YbXVTPvkpvZSsc14xwbZ3LWQSwOylEhbygDOsweN6KE75hU\r\nzOO45FEjKwbLdoX+sIdj6AacyjHb73T8pp1817ZoI1nrWnRvzgjrqgL91w1x\r\ny2JQVrfarfVb8lXaKF06Llk3xj4eCXxTO5DZfa2ADg8bKnEdigaI+Bcy4/jv\r\nRbrQjpeeMkaSfdzkt4K8dwsiCZOA1AE5E6fLv66buAS8x29h9B0RT/V1Q4g5\r\nDHBljvYo2/vxAUC5iz1mL8lubIfDYsZmSCSCc3VHxH9Mc5IQUCFapWd9zaZg\r\nnkz+RC87xW6FOZAC/IsNpQ9IFBUXyAfMGz/AQfnYrDR63vdvss9ygfQ2vL+8\r\n+4aGOFSyvKqVUlw6f3Py9GFwa2h980DV6JdmB+CfErfi5pilKMyJaHU5MC75\r\nxNZCDrUxJ5oWYiEnHHvDY4ZzNh8L8CX4y64oHrbwrquyPgHnlrzhTCFnZE9L\r\np9Tl5ZfT7wRqhrVVYNnseoQJewao22Ady4Ea9WSSus+VwGXfj9j8P9Cfv4vl\r\nw0p7DOUaD4konXrdmfmSsLRf7eRVPxlA9a8=\r\n=6xTs\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "2.0.3": {"name": "@npmcli/map-workspaces", "version": "2.0.3", "dependencies": {"glob": "^8.0.1", "minimatch": "^5.0.1", "read-package-json-fast": "^2.0.3", "@npmcli/name-from-folder": "^1.0.1"}, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "3.4.1", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "2d3c75119ee53246e9aa75bc469a55281cd5f08f", "tarball": "https://registry.npmjs.org/@npmcli/map-workspaces/-/map-workspaces-2.0.3.tgz", "fileCount": 4, "integrity": "sha512-X6suAun5QyupNM8iHkNPh0AHdRC2rb1W+MTdMvvA/2ixgmqZwlq5cGUBgmKHUHT2LgrkKJMAXbfAoTxOigpK8Q==", "signatures": [{"sig": "MEQCIGQ4XIrn8wEL49vK1160/sZhH3qB9i7jfLowx2vluDnEAiA9iuzkBuH/sKHBtqx4bltEWDcBsBp28FpQBDFZRUQbAw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9739, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiYHijACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpuxg/9Hpg4P3humuvjHmqql1T4lsNrYPZ6LOpaHjic/iKw2sugiuI7\r\naSZfPapePIhBG77aPfCuisdAT6Gw3Gvu6CBPwneTdfTWIeFjqAbTkcPIPmag\r\n8m8kSC5n6aLlAWeHP1kVauka5pwfvGHl0rYMq/EJR/5fNyKVhou80JpFP6QF\r\nacc//oBcS6s77TCIBUiJJr577wyTbcYYTO0d6jraPUMMbe7oCC1DdXqoD/Rw\r\nZtv0BjWANt2moSDdyn5jv/ToTFreTLhFPSU5R7V3feqdgy9zVOe7SuTwg811\r\n8FDtq9XCSP+qrJEInfYXG9V3nwT2z8HviQj6qVpx9Vqrv9DaetFOvfu5Exvj\r\n1IbHHVlxHkYEHnwyuBT0T8l+SApufjHAI4sHqs0C/5sfozprLcmE24uW6LMM\r\nz5tTtMkW6iFpeYo9iUFlQv2tRxUIwGbfOeGmhgOmzckw+ImBNLGgB+uN6/ku\r\nEwl+3z+4ruTsbaP9ioNkQWnJEsa7cDcSj/twuH0wBmkE5LdwSUyIFYJROubx\r\nFtdvqut1iKkelHtgB8GokwVC0cMltX7ByWuq8Fwk6E2ugTdR2kWdGsxeQZo4\r\ni8och+vN5hhGcsjeojHJyrPebqgoDHayvfkANHoebZpOd4nAD3WuuUZx4rw/\r\n16D5aR7KeQ9gEzGENVljuCiReWx3OWRdrOI=\r\n=3865\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "2.0.4": {"name": "@npmcli/map-workspaces", "version": "2.0.4", "dependencies": {"glob": "^8.0.1", "minimatch": "^5.0.1", "read-package-json-fast": "^2.0.3", "@npmcli/name-from-folder": "^1.0.1"}, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "9e5e8ab655215a262aefabf139782b894e0504fc", "tarball": "https://registry.npmjs.org/@npmcli/map-workspaces/-/map-workspaces-2.0.4.tgz", "fileCount": 4, "integrity": "sha512-bMo0aAfwhVwqoVM5UzX1DJnlvVvzDCHae821jv48L1EsrYwfOZChlqWYXEtto/+BkBXetPbEWgau++/brh4oVg==", "signatures": [{"sig": "MEUCIQD68VFMoyyFszNwNHQLBpPv4yFQhjm0rUMpu2dZUJA1rAIgEihGIHsWbMDNf7jZe0JEZMcgPw9yeBUeLptfuvWf4E0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9738, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi4atoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo9aw/7BdGnCnOSgNJag86clmvwKyPOWo628ooeSsumYPfMvUNe+EM5\r\neEBp8/zZl1JmTz4NyKzKq2OOLL2raSDe/SAqyq9RC8CD2PAtW19milwpTex3\r\n1jplAOBwPIJeH9WoVfRYH4yD/w+QuDOQmTVVrz2nO/hWMdzdA9L5SvWZaJLU\r\nELft58cSbuDlrkvH1oW0mmHtbTXXAE69+WToW3XCbeFybzrQGZUTsW9n5iDE\r\nZOY3Q96+gXVe7ZLmzNUctoXUq8NpO5WBiALtq0b6NjvaE59jgrQLi0JmI/SU\r\nGFukUOKdoGnTcF/T/NfQHrfg/J8CbE3FZbUCWD/rw7RLBImJrdvQYIPwL1bp\r\ntKq5iwTgFI2A2wiG0uPL+fHjuG142zER/LThe/RMw9VFzvzCX5ZxXHS7Vztd\r\nXZZK6Fn8q3Icwfgowl+AfypcSvbIxKsGhEhMad6aQoZYwzts1aLT3o0rh1Sx\r\nvwMXAeVV6k/xD7dp/pbEsE9QtsjlGfUPljtuhhIm0ued9mdM3Gop3oVRve24\r\n4WjNzUafRrKpM3fnAqLG8Kzau2dLXVIF0mRRBlbBevIu5ZFB1rdUdhwW7jT1\r\nBKWVkgXoQTazGeF5ouSdTv/BVye38MIA4nL/doPThFmQLWTrEPF/dUXfN134\r\nm4M3NSMhQoFlJ6oP9ygNHqRSlj6p1HLd564=\r\n=28jz\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "3.0.0": {"name": "@npmcli/map-workspaces", "version": "3.0.0", "dependencies": {"glob": "^8.0.1", "minimatch": "^5.0.1", "read-package-json-fast": "^3.0.0", "@npmcli/name-from-folder": "^1.0.1"}, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "4.5.1", "@npmcli/eslint-config": "^3.0.1"}, "dist": {"shasum": "7863da57470cdd7fda76d807567d7925a642ddb3", "tarball": "https://registry.npmjs.org/@npmcli/map-workspaces/-/map-workspaces-3.0.0.tgz", "fileCount": 4, "integrity": "sha512-aaEDwQ+fUH80iNYSDAcKv9lxIFWsgGkLjIPZENyep75hKeAk2CfSbCAZ6IHDDrVlNybvvNmlFjPap6GdTz9cCw==", "signatures": [{"sig": "MEQCIHeBgx5XOMAe5ngJuh254jK6yBY1acDWaUD5/3ISwecDAiBe0lL8t51QBmmaWh39z8dW/GhgL8mcTHeVy5USKRTUEw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9695, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSPIzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrYKg//VeKK334QZC5hkUrmmMsdCyHuHTGZs6FBSqZQ1SK0W0XPGwJU\r\nG6laI9rtbTMwJwy7u2Ch+yxptCaAH4CJeTPVI9cOuV6ARG4dsTzUPN8ESLrI\r\nOEP4wT+jumvvCBn+DPYsKBcokOh/usKuJNTG+7Yj6teHZ2xPpdATvins4oHl\r\nJloR6g56csrrTbf9Ovwz3ErUMSqfl+m8ukyPylxVMoUxw8Qqq7rsTVUnXOfV\r\nFuGqwcFcPxSo9RER6Bo8OdtTlm09DJ6tsuttWIokcmT7NipMh/rVG1DV7nQX\r\n/D3uip5iw7T8ObBB8fujRqNimeOBNTczS0dxm9IAUGpJTNrwqtrVRDn6DYAf\r\nvJSS6e3t4Jh99MPY+iQ0alXYjhHelMcRP0Uxw/RLN7gOYDlc9CWVFwJaRhQK\r\nOnjHJ3aoR8HLPjLI0khZvt7ahiFjRXBPmXe/t4TLEYuqAG32AJf7w6H32tnV\r\no3NnkQQzubqriivs4OJ6kFtDJInx0uMycszDkL1TDuP8SbpENcNgCZqAu0IY\r\n6WBTDIOdjU0eOoHv2ZhsITSmgAziGoIcsZvU50x0dZbL1rb+dDfn/4zFEpMy\r\ngI5CJPhhnIDhnnhsqt1eVg62fVkAbnbLkjIlrHKzrYerwnb3x+JDrVlRJfX0\r\nWQHoVyp0WllVvRf1nBIZ8fZESfZVbOR9hRQ=\r\n=6PRp\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "3.0.1": {"name": "@npmcli/map-workspaces", "version": "3.0.1", "dependencies": {"glob": "^8.0.1", "minimatch": "^5.0.1", "read-package-json-fast": "^3.0.0", "@npmcli/name-from-folder": "^2.0.0"}, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "4.11.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "558941a715361ef51d6ad990cacb1af8990a7136", "tarball": "https://registry.npmjs.org/@npmcli/map-workspaces/-/map-workspaces-3.0.1.tgz", "fileCount": 4, "integrity": "sha512-QXwE2p5zRTP6X8Irgf/swYwwdQEalSA1GBm0IGE/86R5EJbUGgKMOP0kOjaJWJxaWPkSqyhM8N50SPxFHTfkNg==", "signatures": [{"sig": "MEUCIQCtF/pug6KmlPzCCXjBALDFTsy6hgKKLCM2Vb1XMDwGqwIgIp6m6z5qKpwomVyQdnKIBpiymLKvNQeClicyuLn+CeE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9697, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjm3pVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmryBA/9E6eUDhtV8tHiyiUsA53WopvpgjujbbUqwNmaXqO5cBTr1cXm\r\nxWUBxYEx2h6IRMZ5aqGWcmv6m8UlneE/2QS1FuLM59hh15NtzRe33mS1A9Qs\r\nMAvGmmosJeQBO7scODEHhupb8B4Qq9rwzgIhe8zbNbaodgilVwxbZyPy3Qwm\r\nn5Na0TYFmYx2VymJ4vImpteRRzZflf+9EvZjIK6OQEnq6+F2mRrakmg1c/He\r\nyhS/orq5sAbyIIhMcKTxVaVGLoSa3Le2ixJ3SKZcVtEa+N0Cku26YsiPFTiG\r\n05zl+4JhNwCcRtp/2vB/i00XlBDuHWhp0bJR51g6CDCPPd/zLDujCWxNShpv\r\nx+bScF5kSGwJI5ygwCRqqXlClvHXcgkG1FH9vsJbLagh45jDReJyacF5EyLZ\r\nsRQS8oPHjNE/vs00Z3yUnif6cx/hh5FAHOU6uiUdGRUqHqJee/GigWbWOZDM\r\nQB/jh4XEqJaSvD+i2oMnScQ4sgQjRbCGrAIH1N3CiEXoDvZHndkP8yWfZ6pT\r\nCCnbtCnzl1XPkSUg+98P4Q0rwFkPfE8bdpIr2gkWWvjnQoNTq6FG6lc0riQu\r\n1DDCVHWCCoQtvFHtGf8zHGb5s5rNrrBvfP1j+G+aNNKmhdy2mHGETglbheoe\r\nYR4RMxCrMwy91m5keXFQMaXeDtbt4SXLeHY=\r\n=npT1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "3.0.2": {"name": "@npmcli/map-workspaces", "version": "3.0.2", "dependencies": {"glob": "^8.0.1", "minimatch": "^6.1.6", "read-package-json-fast": "^3.0.0", "@npmcli/name-from-folder": "^2.0.0"}, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "4.11.3", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "aa3f9ebccb213073d6d6bf743047272108c29f06", "tarball": "https://registry.npmjs.org/@npmcli/map-workspaces/-/map-workspaces-3.0.2.tgz", "fileCount": 4, "integrity": "sha512-bCEC4PG7HbadtAYkW/TTUVNEOSr5Dhfmv6yGLgByJgCvdCqq7teq09cjvJ1LhzJU/euWjvYMcQxsfj7yDD2ikg==", "signatures": [{"sig": "MEUCIHRjFtrTwNYcmmC9eLFaMNMagswWEXQqC8wwm0b3TN4/AiEA+wWhjYbJf9W6TjOGHFacyqeB0+OAHPNMCWKBFNk2csM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9697, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4ZJNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo0AhAAhYVBnOFlOiLiHXCiwnwHxm9WTbsdBYFLe031sBd8lVQFpcNT\r\nHiLS1xpyR+ANOWxbfS5qLkJ9niBKf8Vmxyj7otZ8PYkpJ1K/m6MQkYdys8DH\r\nQf04ZVw/icgmLu8kQKUA/T8z642ROUlSYPMPI2886TLV9CNae2qAWYNd7eQK\r\n7ztck2b5czT1Sn3XI/XuV0f0oKU7M9PmjIsc6XqzfXzBatTOUtOHfr8wUc45\r\nFtJdUCujQNaqsxyY+CxWKA5GE7GpPmye6xtJk6sEhkMnf8WJSNwM/okJydMx\r\n7+OXfxHkMH4f8upuoyHdB5GtwJI3gXxkXy/JbMWS83hkkoUwgTtVlyBIao/f\r\nOvg/V+sncuolV4uVfd/UDGwmeAi1TAd+Op0ls/oPOLleD1FbyZDNxj4zmaWX\r\ni446WZj9J/cQos46XEW9nCp/MV1rqLnAkEHUR1ZdLf7d5ccKbETxSpjIuocr\r\nC8y++14BoNdKqfch4VC9SWY9Xg62TwQIR/UCKxsBG1s0ZHzgIicsbds9D5bh\r\nFJDaExNT5F273t8huqs1ZkMNo0owvpwyGQdpnXZNnxrWuuEFeSR6qsPjcTPI\r\nD1aY6tZjj7gYYtQ8t5IF9BrDv8/DdDTttqGjaQhI4/ptfBgTISlGm65tfZZD\r\nmla948SwYd/nsL7NMMptAlGh8qJW9zekbpc=\r\n=AbmR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "3.0.3": {"name": "@npmcli/map-workspaces", "version": "3.0.3", "dependencies": {"glob": "^9.3.1", "minimatch": "^7.4.2", "read-package-json-fast": "^3.0.0", "@npmcli/name-from-folder": "^2.0.0"}, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "4.12.0", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "476944b63cd1f65bf83c6fdc7f4ca7be56906b1f", "tarball": "https://registry.npmjs.org/@npmcli/map-workspaces/-/map-workspaces-3.0.3.tgz", "fileCount": 4, "integrity": "sha512-HlCvFuTzw4UNoKyZdqiNrln+qMF71QJkxy2dsusV8QQdoa89e2TF4dATCzBxbl4zzRzdDoWWyP5ADVrNAH9cRQ==", "signatures": [{"sig": "MEQCIHTwX/ttqybf5KVVn4PtrqPvVjVP4udrEscGnVQ8HmFAAiAvTZzGcKFcJGP0aUNheIrNL8NR2jOGVn0pBZ1pxk5eeg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9722}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "3.0.4": {"name": "@npmcli/map-workspaces", "version": "3.0.4", "dependencies": {"glob": "^10.2.2", "minimatch": "^9.0.0", "read-package-json-fast": "^3.0.0", "@npmcli/name-from-folder": "^2.0.0"}, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "4.14.1", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "15ad7d854292e484f7ba04bc30187a8320dba799", "tarball": "https://registry.npmjs.org/@npmcli/map-workspaces/-/map-workspaces-3.0.4.tgz", "fileCount": 4, "integrity": "sha512-Z0TbvXkRbacjFFLpVpV0e2mheCh+WzQpcqL+4xp49uNJOxOnIAPZyXtUxZ5Qn3QBTGKA11Exjd9a5411rBrhDg==", "signatures": [{"sig": "MEYCIQCxwkiOW5cHhAAvsYW1CaZOgSowCdemWwIC31ndQq0FJAIhALUbhGHfHzQxtugI8UCXpQyd2xip8V74CL1yYLAKrNA+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2fmap-workspaces@3.0.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 9754, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkSscKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpK/w/+MVXV5LS8gRrmBKdYhsiAJw8mKCNOIPYkviNMTgmDb/aaB57k\r\nTCnAoWZAJbbd+5V1BkmHQBHWy8A64ZKfC2uxr020KG5TjxTkp5FbhOFem8KN\r\nCANBDn7PAFICd1372dj5yjP9PTvYAcah4yb1nsJpDm7PCpK+d42Br8WAjsz4\r\n/VNozKtcSM9o3hBX1z+MtmYr479EeCCUVk5OXnBhXIT0E+dgRq22fIu3Xr09\r\nCfu/IGzZNFOwaVkHvJWHrVf4SDqT/uiPyPGmDJ1uWYyJrhS9goGxIP9Tu9VP\r\n018nxb1BupqgpvWIJxM4Lb6z0pDpCY18Icaam9uXIzSsZg+RaETxDb4sWLlI\r\nL2KNE2WYb3GeSkD0up/z2JvzA3Rh5zm/KblvdlO2MOLvgXovhouDAF0rZ62i\r\nMzXS9w3j85FclIVx3sXaO70BuB+ejYCYhw+gsfsmGz+73Q0kK6SIkIZrhvIj\r\ncJtbFAH2M9nduoalca9WePjKLqx0UqBMtYX81PJSfJpGgghn5Vv5NiggwnOL\r\nBNdNdDezRwQvIpKWmnH9GonJA/P0WfQTdxbiHTTu/I6jJQouNDlV5DedOJ0V\r\n5RAf4RamTboP2/eQfRgqJpXS04YPHmWvm8czlosSgKic6IKj4Wwb5srdAS9Y\r\nMwmOufzyoksJSLcxIXvPjuidSUrPovKAjrY=\r\n=CLb4\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "3.0.5": {"name": "@npmcli/map-workspaces", "version": "3.0.5", "dependencies": {"glob": "^10.2.2", "minimatch": "^9.0.0", "read-package-json-fast": "^3.0.0", "@npmcli/name-from-folder": "^2.0.0"}, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "4.21.3", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "3362b6509809ea47c2ac4e09ff99f5590b04fb6f", "tarball": "https://registry.npmjs.org/@npmcli/map-workspaces/-/map-workspaces-3.0.5.tgz", "fileCount": 4, "integrity": "sha512-M2HnOHm/DIjbln+Wox5WW9C3nkciFY+fBuQILPyuU6/gbflpMnXN6LbLcJi5IrUeZhcBrPeNhJeaBgc/dwngBw==", "signatures": [{"sig": "MEQCIGmDasp5VVfC49uWrMsXDIf2D48lin2G/e+vPsErmfgyAiBtDnzwOGkwWVDpPWGQOhuTQwliElCdpuWuDERQUMdtnQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2fmap-workspaces@3.0.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11198}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "3.0.6": {"name": "@npmcli/map-workspaces", "version": "3.0.6", "dependencies": {"glob": "^10.2.2", "minimatch": "^9.0.0", "read-package-json-fast": "^3.0.0", "@npmcli/name-from-folder": "^2.0.0"}, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "4.21.3", "@npmcli/eslint-config": "^4.0.0"}, "dist": {"shasum": "27dc06c20c35ef01e45a08909cab9cb3da08cea6", "tarball": "https://registry.npmjs.org/@npmcli/map-workspaces/-/map-workspaces-3.0.6.tgz", "fileCount": 4, "integrity": "sha512-tkYs0OYnzQm6iIRdfy+LcLBjcKuQCeE5YLb8KnrIlutJfheNaPvPpgoFEyEFgbjzl5PLZ3IA/BWAwRU0eHuQDA==", "signatures": [{"sig": "MEUCIQDcpxXiv3xRodoQAD9YVUtBKZlanchh6LsHkTN2pGm9uQIgToGtZamJPirkT5SrvmQ5rjX5CZ1fUUjVwJct+4E6vsc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2fmap-workspaces@3.0.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11207}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "4.0.0": {"name": "@npmcli/map-workspaces", "version": "4.0.0", "dependencies": {"glob": "^10.2.2", "minimatch": "^9.0.0", "@npmcli/package-json": "^5.2.0", "@npmcli/name-from-folder": "^3.0.0"}, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "4.23.3", "@npmcli/eslint-config": "^5.0.0"}, "dist": {"shasum": "9bb610fc8d00785e69080589c4f9af2ff06fa57b", "tarball": "https://registry.npmjs.org/@npmcli/map-workspaces/-/map-workspaces-4.0.0.tgz", "fileCount": 4, "integrity": "sha512-v+bP25IwiHwmGDGLg/gOyUyKalbjdYDwd/WdYvz7vGhlReSnFDCmoabfqEISqGIXSFQQM/WnFu21ca6SkqPnOg==", "signatures": [{"sig": "MEYCIQClt9xZ7h9ZROSogq6I5HmtPkQ8LXIpFtWjyIDw8koUhAIhAINck0KmihLFBfkNWvonPZWFmtXQvzr4mqhnWn/1XBx5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2fmap-workspaces@4.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 10940}, "engines": {"node": "^18.17.0 || >=20.5.0"}}, "4.0.1": {"name": "@npmcli/map-workspaces", "version": "4.0.1", "dependencies": {"glob": "^10.2.2", "minimatch": "^9.0.0", "@npmcli/package-json": "^6.0.0", "@npmcli/name-from-folder": "^3.0.0"}, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "4.23.3", "@npmcli/eslint-config": "^5.0.0"}, "dist": {"shasum": "ff1a7d6f643264617c0769ac0f36e507743d5a81", "tarball": "https://registry.npmjs.org/@npmcli/map-workspaces/-/map-workspaces-4.0.1.tgz", "fileCount": 4, "integrity": "sha512-g5H8ljH7Z+4T1ASsfcL09gZl4YGw6M4GbjzPt6HgE+pCRSKC4nlNc4nY75zshi88eEHcdoh3Q8XgWFkGKoVOPw==", "signatures": [{"sig": "MEQCIBI1ERN9VE+/z86CCFYQG0slnm5Iv8Vx4DX8Fz9aAhUbAiABNdKaDyLuBJrQ9w2AhZ0wSDdaRQj1hzvocBR6zUU3OQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2fmap-workspaces@4.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 10940}, "engines": {"node": "^18.17.0 || >=20.5.0"}}, "4.0.2": {"name": "@npmcli/map-workspaces", "version": "4.0.2", "dependencies": {"glob": "^10.2.2", "minimatch": "^9.0.0", "@npmcli/package-json": "^6.0.0", "@npmcli/name-from-folder": "^3.0.0"}, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "4.23.4", "@npmcli/eslint-config": "^5.0.0"}, "dist": {"shasum": "d02c5508bf55624f60aaa58fe413748a5c773802", "tarball": "https://registry.npmjs.org/@npmcli/map-workspaces/-/map-workspaces-4.0.2.tgz", "fileCount": 4, "integrity": "sha512-mnuMuibEbkaBTYj9HQ3dMe6L0ylYW+s/gfz7tBDMFY/la0w9Kf44P9aLn4/+/t3aTR3YUHKoT6XQL9rlicIe3Q==", "signatures": [{"sig": "MEUCIFLOR3i/Oz0q9FuSa4K4LdH/qxjrn3XlaHL505zGom0+AiEA/kq6CimSSOYx1hfVEAbWOGflAoNEflyt2JuMt0gAX7U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@npmcli%2fmap-workspaces@4.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 10966}, "engines": {"node": "^18.17.0 || >=20.5.0"}}}, "modified": "2025-05-14T20:03:20.536Z"}