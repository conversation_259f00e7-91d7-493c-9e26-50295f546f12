{"_id": "concat-map", "_rev": "15-60df2eb6ac2bf61fd5195f753003c9ab", "name": "concat-map", "description": "concatenative mapdashery", "dist-tags": {"latest": "0.0.2"}, "versions": {"0.0.0": {"name": "concat-map", "description": "concatenative mapdashery", "version": "0.0.0", "repository": {"type": "git", "url": "git://github.com/substack/node-concat-map.git"}, "main": "index.js", "keywords": ["concat", "map", "functional", "higher-order"], "directories": {"example": "example", "test": "test"}, "scripts": {"test": "tap test/*.js"}, "devDependencies": {"tap": "~0.2.5"}, "engines": {"node": ">=0.4.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "_id": "concat-map@0.0.0", "dependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.19", "_nodeVersion": "v0.6.11", "_defaultsLoaded": true, "dist": {"shasum": "604be9c2afb6dc9ba8182e3ff294fdd48e238e6d", "tarball": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.0.tgz", "integrity": "sha512-w53m3Q7KbL6gVnOoDvkA21Ho4G4YkikBfhX4qX/aLViZ0FrCQwFcA6Fiv3aBtnNUd6b8MR2KIli9I9RRG6ujow==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQChhdaWNNzdWch7y3nMoIP3xSd0u96Zn9Lp27cweI3IPQIhAKBmLxSNlWyspz7L4Q57pM8zVpb4FnEL7Rl+696tW7AO"}]}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}]}, "0.0.1": {"name": "concat-map", "description": "concatenative mapdashery", "version": "0.0.1", "repository": {"type": "git", "url": "git://github.com/substack/node-concat-map.git"}, "main": "index.js", "keywords": ["concat", "concatMap", "map", "functional", "higher-order"], "directories": {"example": "example", "test": "test"}, "scripts": {"test": "tape test/*.js"}, "devDependencies": {"tape": "~2.4.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "testling": {"files": "test/*.js", "browsers": {"ie": [6, 7, 8, 9], "ff": [3.5, 10, 15], "chrome": [10, 22], "safari": [5.1], "opera": [12]}}, "bugs": {"url": "https://github.com/substack/node-concat-map/issues"}, "homepage": "https://github.com/substack/node-concat-map", "_id": "concat-map@0.0.1", "dist": {"shasum": "d8a96bd77fd68df7793a73036a3ba0d5405d477b", "tarball": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFE1dLaDH74qdfcVe+NrAWhI69S5OkTt0Kl0nuw0SdvPAiEA3VVG9Mk5FAY//r4V5Y9annPnv0R898ie0+9NE9Yq0rs="}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}]}, "0.0.2": {"name": "concat-map", "description": "concatenative mapdashery", "version": "0.0.2", "repository": {"type": "git", "url": "git://github.com/ljharb/concat-map.git"}, "main": "index.js", "keywords": ["concat", "concatMap", "map", "functional", "higher-order"], "directories": {"example": "example", "test": "test"}, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "lint": "eslint --ext=js,mjs .", "pretest": "npm run lint", "tests-only": "tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "devDependencies": {"@ljharb/eslint-config": "^21.0.0", "aud": "^2.0.1", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "in-publish": "^2.0.1", "npmignore": "^0.3.0", "safe-publish-latest": "^2.0.0", "tape": "^5.6.1"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "testling": {"files": "test/*.js", "browsers": {"ie": [6, 7, 8, 9], "ff": [3.5, 10, 15], "chrome": [10, 22], "safari": [5.1], "opera": [12]}}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "gitHead": "8c91c7c37eb89cf47bddfc4b4f7aac813fa11abe", "bugs": {"url": "https://github.com/ljharb/concat-map/issues"}, "homepage": "https://github.com/ljharb/concat-map#readme", "_id": "concat-map@0.0.2", "_nodeVersion": "18.10.0", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-xGo3rY/AH8WxqkYSvsNV9yB79sN2oNVOXnmSMYLx28CVFBsXiEFCkcf6WIiWjk5VWKr4/1fV+KG5I4442xE2hg==", "shasum": "f497c5b65881435aa6860a55eb541f1601f63394", "tarball": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.2.tgz", "fileCount": 9, "unpackedSize": 11454, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDSYCJeKkpso2D9dzzVUCNKXGBTBFEeNcp14t08vPpoAAiEAiJRax4kCtLHSCQEUBYfZXnMoGhKM2QbUHq4TiP8DQmU="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRuwyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqo1g/+MGYt6Aetoj2EK3cpTPdgO+dKXbkaOOCU4amtrp7OG/ViIU0f\r\nEWJBhDrdSbMItq3qfHe6HWCAnkboz4nG2Gosi+bxrYW/KPeEDw18Tjse/DUG\r\nIbv2eib9Wt4gl5eDKn+2+1L6VVDkYyY/ti0jiqR+XX1tG9x/Dpb6h8vzch08\r\nsc/y6IfHElfwiIlq3BGqlrl9lmQ9q/ZHAKjFsFKgHvMCwUuh8dr6KBMPCgL0\r\nI3MSJhxkmS/KWnlgBPfhmgh+OrXBB3X7bujdyoKS3wDQWwjMBzvWDbhQIdg5\r\ne0szmRXaGMgLnCXllkViqEAv0SxeXGP2hsAIeBp9iHpQL3ZOpZQIkeCgoujn\r\nqLo9AltK6yqvQS0ubfJxXYHNQKBIztDXg/gcWGtI4Pc5wUx6EJLVBSPvmCOs\r\nSV2E2JS9lLIxXGye/CYwQFHP43MeK8pd1tU5IeytQsVWyrLuOib/4Ztv6mQG\r\nog7JAvOtNWIXypv+zJlQ8Dy7+lT35YjiwwQd1Sp8m8Edg8r7s6syCTviaicK\r\nHX/1z4AUUdPKWKXSR6sXPN3CwNs4yG+8Z/GHYBMtckhuwByqu2eLXiVIRytc\r\nzj1Ka8G5ZpQhhJiV/prBuUMbU+JfjsH80HgiZUUmBPNmyO9VQTne2D5W7P9x\r\n2tmkXT2B3ZrP10QRkXUK4gqEcnt4/w/GbDI=\r\n=Xgn2\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "noperson<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/concat-map_0.0.2_1665592369896_0.6483951265901502"}, "_hasShrinkwrap": false}}, "readme": "# concat-map <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\nConcatenative mapdashery.\n\n## example\n\n``` js\nvar concatMap = require('concat-map');\nvar xs = [ 1, 2, 3, 4, 5, 6 ];\nvar ys = concatMap(xs, function (x) {\n    return x % 2 ? [ x - 0.1, x, x + 0.1 ] : [];\n});\nconsole.dir(ys);\n```\n\n\n```js\n[ 0.9, 1, 1.1, 2.9, 3, 3.1, 4.9, 5, 5.1 ]\n```\n\n## methods\n\n```js\nvar concatMap = require('concat-map')\n```\n\nconcatMap(xs, fn)\n-----------------\n\nReturn an array of concatenated elements by calling `fn(x, i)` for each element\n`x` and each index `i` in the array `xs`.\n\nWhen `fn(x, i)` returns an array, its result will be concatenated with the\nresult array. If `fn(x, i)` returns anything else, that value will be pushed\nonto the end of the result array.\n\n## install\n\nWith [npm](http://npmjs.org) do:\n\n```sh\nnpm install concat-map\n```\n\n## license\n\nMIT\n\n## notes\n\nThis module was written while sitting high above the ground in a tree.\n\n[package-url]: https://npmjs.org/package/concat-map\n[npm-version-svg]: https://versionbadg.es/ljharb/concat-map.svg\n[deps-svg]: https://david-dm.org/ljharb/concat-map.svg\n[deps-url]: https://david-dm.org/ljharb/concat-map\n[dev-deps-svg]: https://david-dm.org/ljharb/concat-map/dev-status.svg\n[dev-deps-url]: https://david-dm.org/ljharb/concat-map#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/concat-map.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/concat-map.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/concat-map.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=concat-map\n[codecov-image]: https://codecov.io/gh/ljharb/concat-map/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/ljharb/concat-map/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/ljharb/concat-map\n[actions-url]: https://github.com/ljharb/concat-map/actions\n", "maintainers": [{"email": "<EMAIL>", "name": "lj<PERSON>b"}], "time": {"modified": "2023-06-22T16:31:35.118Z", "created": "2012-06-08T04:31:26.867Z", "0.0.0": "2012-06-08T04:31:28.515Z", "0.0.1": "2014-01-30T03:06:35.982Z", "0.0.2": "2022-10-12T16:32:50.075Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "repository": {"type": "git", "url": "git://github.com/ljharb/concat-map.git"}, "readmeFilename": "README.md", "users": {"markthethomas": true, "klap-webdevelopment": true, "wenbing": true, "flumpus-dev": true}, "homepage": "https://github.com/ljharb/concat-map#readme", "keywords": ["concat", "concatMap", "map", "functional", "higher-order"], "bugs": {"url": "https://github.com/ljharb/concat-map/issues"}, "license": "MIT"}