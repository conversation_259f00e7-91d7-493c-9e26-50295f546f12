{"_id": "is-bigint", "_rev": "8-de1bc8e40b216af3792d48d89c4b9b46", "name": "is-bigint", "dist-tags": {"latest": "1.1.0"}, "versions": {"1.0.0": {"name": "is-bigint", "version": "1.0.0", "keywords": ["bigint", "es", "integer", "is"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-bigint@1.0.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/is-bigint#readme", "bugs": {"url": "https://github.com/ljharb/is-bigint/issues"}, "dist": {"shasum": "73da8c33208d00f130e9b5e15d23eac9215601c4", "tarball": "https://registry.npmjs.org/is-bigint/-/is-bigint-1.0.0.tgz", "fileCount": 10, "integrity": "sha512-t5mGUXC/xRheCK431ylNiSkGGpBp8bHENBcENTkDT6ppwPzEVxNGZRvgvmOEfbWkFhA7D2GEuE2mmQTr78sl2g==", "signatures": [{"sig": "MEQCIGyLZ1sj3zOv19DxxD48vpJa+H/XXDcXEeXBs3i8qlCRAiAw3E2cMI4UW/aNK9SPqeOFv7sDRW0WhZVRd7z90S6yKg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14015, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbpF9dCRA9TVsSAnZWagAAIKcP/iNBRFAiLHjwpOUZRY50\nllWRzfeBMbV4cy8UDROXEE1GLj7xOMLBhO4t4sVPo5wjqcMjf+0WXJP3ZFqn\nLBitqQ+/hBSsOracGg9M9UYWxi2jcWJJBIYEhTXdoiEgmoG0YHqrhbQzIi6c\nvE0AG+y5U/0GwLFy/yhHYbOdmOD+YgCcBFeVI/hdgwuzc48KzWLLThOpeFBN\nmnHNVqA+nXuPNfWIEEDJLeh39oFA6k+PB5rjYtWIOyvLhyi2sjzikxHkE6ht\n0F8DesD6VJEMngqdlyBgWiz/HQ+378jkx2+2/74BZLyfw/pR0dDn2ZfJC2Jo\nDnX+yW5HGX0wpDza+JVBk9/qeAdWyaDVkIcRBAg7E2FhL8u+4zqZZLsSUyjW\nptYtRLGXAsyuCIjG8SeEwEAkSBjhJnmdvzCVV7M4l2cIJrnB//5Oq2EtOq2d\nseEIAWKiCGGi6N0ZE6iHcC08YIsYYmBFq09AIAHZtDwoVCfB79AM//DGA56m\nFUzZ7LgH+JYZHPdG9xQOFKCDfBz7mRHRt/6uX0rVeNUhAMNb9qdbbV0NzQsi\n54pvJA3o09bZhKsFIhLEeZS9SzWiWKEA8TGoBTXyxchpOR+sH7UuVdMkUv3Z\ncMV/SHG4AjpzOzGYTQb8WxfcZrQPavDgqnF/Lm7lwgxdnbKrm25GX91XXRD1\njl5R\r\n=MhP0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "3b0fdc23f050de211667d69ca168e33ad0e56b9d", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "pretest": "npm run lint", "prepublish": "safe-publish-latest", "tests-only": "node test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/is-bigint.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Is this value an ES BigInt?", "directories": {}, "_nodeVersion": "10.11.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.9.1", "eslint": "^5.6.0", "has-symbols": "^1.0.0", "object-inspect": "^1.6.0", "safe-publish-latest": "^1.1.2", "@ljharb/eslint-config": "^13.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-bigint_1.0.0_1537498972993_0.9980297650840038", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "is-bigint", "version": "1.0.1", "keywords": ["bigint", "es", "integer", "is"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-bigint@1.0.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/is-bigint#readme", "bugs": {"url": "https://github.com/ljharb/is-bigint/issues"}, "dist": {"shasum": "6923051dfcbc764278540b9ce0e6b3213aa5ebc2", "tarball": "https://registry.npmjs.org/is-bigint/-/is-bigint-1.0.1.tgz", "fileCount": 17, "integrity": "sha512-J0ELF4yHFxHy0cmSxZuheDOz2luOdVvqjwmEcj8H/L1JHeuEDSDbeRP+Dk9kFVk5RTFzbucJ2Kb9F7ixY2QaCg==", "signatures": [{"sig": "MEYCIQCEszs/F0I8UoReQEjo7vtdzZMJvqrbvycvuK4ZOwFC0gIhAPYpoMH9QykdqDQsXSnwxThdo1WIzyOpsOdAV8zDlfCW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17360, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfxXYfCRA9TVsSAnZWagAAYf4QAIpN3zr6G63e8qUeb7xc\nb1kNp5bOdyEMHexa8B510DFwxtIoqKcHyBW7SVcQPbIHBAjXhVqyTCIEUi3r\niUH1ySAnelQR7Z/zh73On+VgI6U0KnLZtSWs/OW2v8vnLi0QbOKY1oMY9kEV\nclH4K5X8keLnXjjCK4eBg8HIcRjeJUlh0zqmtfGYJ4PpoO/6mU/ZBGGKRo0h\n14pkQWQn8YR6+cXCVq7zt1LL1h1FK13859jVc6JUKe9v8aAY85yPr8N2gmRe\nz8BtD6+s8KdhfUBuXETPFgZcojdQqqmI8/Qg8ia6WY4j2JVNWMCkCZ9fes5O\nX6k39i0fbNB13oAcgNoiLPARKKBK/JVEagE37C2EyuSVDz02bL8IVPBKA27z\nixI/XBYkY3e3gx8mieCyoEpAFI7Qj+iy3YvoZV431zWAgYcVXKuqqvSn5lMP\nNIGiW/+KClOf5RGCo5wYXBmcJcmvQUFZSFOAnY1Ce9SpTtEfIXCtpPtecr5b\n4Aq1lFQ+ngAXtpENHK9TyZlNzV3ZmMakCOYI6PDIt96vQ0vWjIetHq/rsHs+\nJOII3HdDJBWI4xdSxaEM1crq/ppFoqC+iJS1fwbfX1nG5Fro3s3gD+8lY83q\nBNhGntgf/UxM/v0p8XEXeKfAywr7NgtFxrgCpmlnPVegWjMrDa+bWAzAzqUy\nC/jM\r\n=rRM0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "b5ad9572b0202912902467e1ba21dd4eb3ba40e6", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "safe-publish-latest", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/is-bigint.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Is this value an ES BigInt?", "directories": {}, "_nodeVersion": "14.15.1", "dependencies": {}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^1.1.3", "nyc": "^10.3.2", "tape": "^5.0.1", "eslint": "^7.14.0", "has-symbols": "^1.0.1", "auto-changelog": "^2.2.1", "object-inspect": "^1.8.0", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-bigint_1.0.1_1606776350933_0.26927683703781624", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "is-bigint", "version": "1.0.2", "keywords": ["bigint", "es", "integer", "is"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-bigint@1.0.2", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-bigint#readme", "bugs": {"url": "https://github.com/inspect-js/is-bigint/issues"}, "dist": {"shasum": "ffb381442503235ad245ea89e45b3dbff040ee5a", "tarball": "https://registry.npmjs.org/is-bigint/-/is-bigint-1.0.2.tgz", "fileCount": 11, "integrity": "sha512-0JV5+SOCQkIdzjBK9buARcV804Ddu7A0Qet6sHi3FimE9ne6m4BGQZfRn+NZiXbBk4F4XmHfDZIipLj9pX8dSA==", "signatures": [{"sig": "MEYCIQDuuZUnrGFfXbKCz/olTHEnhCTw2Tmo0hIPmfLw7MSEVQIhANzyQH/EH8dD4rjPz4hgTvCZ1k84AACCPaOy8X3qEqrz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13114, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkbKBCRA9TVsSAnZWagAAhv8P/0ratxsNY1bQo7jDFCQ4\n8lBtgpPqEgwrgFil2f8hRczYwKyE5t3U0vS+18QK0wmFcJg2APnkEV/zmges\neiAtcrn92aOf8ngW/a0NtDRz0LYV7QAV3syQ598NebU2lE6c68BAW8Fnev2M\nd2cnF6pkt4NpBvX0DyXNBcFQkd1MakueGovFsvbd3hodoh0TIxpIEQkBvR1g\nNCG6vyxR8fIN3uDSJwpFkG0/qftF/KvUqLN1Z1dAsK9ddyNy+CCwpT5h0o7D\nnc4J6IxuFdV9CC17rSKuHkl2SNFwasA93nUw+mbsWb38OfjqkE+bL1d11xEA\nwWetK0dHjUsJ1beZEEII32vDWwsxdOwuuUi1QPfhXskKZXKdYWW791dP//s8\nc0lhaQCLbrGvs8jOaN1JL7Kimb6KkyKNp8e1QIITCSDab6y4Z47A/GUy37q0\nDiY3fA1mBHyCP4fwKxEozYF5FszdOckqDEnQIIIzd74v1SYL02VQLRg0YliA\nLB5egZ7IlpCTVWmkVKj/0qkiLuzMQThCrZvmNwr/sMJIuRm7/LCVPP1cu0Gh\n7UulvTDQqBaLloNwrV0UPMibkk1MeuRTxAi6uqn+2hroVlSldUV7cDQkzjUt\nIl+479e+/+3r7cxXrPRITyWKGZWleH6LWd2QhBsX50depcuz4M+B93DFNKFG\n8GIh\r\n=atwP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "643e419876c0a433045db020abc38a639c4bcbec", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspect-js/is-bigint.git", "type": "git"}, "_npmVersion": "7.10.0", "description": "Is this value an ES BigInt?", "directories": {}, "_nodeVersion": "16.0.0", "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^1.1.5", "nyc": "^10.3.2", "tape": "^5.2.2", "eslint": "^7.25.0", "has-symbols": "^1.0.2", "auto-changelog": "^2.2.1", "object-inspect": "^1.10.2", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-bigint_1.0.2_1620161153208_0.9407842484031812", "host": "s3://npm-registry-packages"}}, "1.0.3": {"name": "is-bigint", "version": "1.0.3", "keywords": ["bigint", "es", "integer", "is"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-bigint@1.0.3", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-bigint#readme", "bugs": {"url": "https://github.com/inspect-js/is-bigint/issues"}, "dist": {"shasum": "fc9d9e364210480675653ddaea0518528d49a581", "tarball": "https://registry.npmjs.org/is-bigint/-/is-bigint-1.0.3.tgz", "fileCount": 11, "integrity": "sha512-ZU538ajmYJmzysE5yU4Y7uIrPQ2j704u+hXFiIPQExpqzzUbpe5jCPdTfmz7jXRxZdvjY3KZ3ZNenoXQovX+Dg==", "signatures": [{"sig": "MEYCIQDebtSpGbSAbHSZi8S3hx9N9neO3JLWH9WZAPSd6kgtagIhAKqOEwBYCGNzDP+UsJQ/e0SaPQHmRhmm+6ZZEmo0CiNq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13816, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhDWwfCRA9TVsSAnZWagAAKPYP/A/q44ZYtH8NtmGSqfNg\nznosczkxN+M4+flzKNxpBruivZcyoWukl0OGD7WXobUOloJhxOvu58eg6MG5\n7RBcgvugj+ZVI7WwB0lTb2GTTqbhNEaDbRMEtN9CgKlzJDnZMgQoHQydeUSu\nhlnK84AgpNw7qirM3NWKOrl5B6y3sP4GjYOghNIr4OQe5hKe7ce7fpBcFeXo\n8F+Tpbud5WjeNTzKsORz/sFmXEorKK/sKBY5FAfjh5b9Obh21QW2saUx3i9D\nNvbqN7PN/3fIn1/YbRqWrC1Xwh1bMkUQjCe6sYDzP4phix9Vg6YcxONJ5qeF\n5PSOzh7rt1dZ8EBFgX+JPvNbjq65uKBXj9mOxqC/bq0am3ZAbFhC1cBuZH4C\nFmkzDZsklzCduwHtRrq7qiIjob07ARW7+/k9O9fg1hcGWoFyXxE+jXFFsFPv\nCOQQdzl7eKbQvCPl+136esGTTqMfej2bqLhYnjClTmxC9HntBNv2B45R4AyA\nkHXGtdF2WtsuF+q9FQbhdNBhZFMZ0FGxZs2JR/+vnP4CITkpySF0RaYrVC9K\nxUlMTyJUHCV07P85onWR3tWI3EOn9ssSAfYRRd8+iYrYP4yjKP9yM4QgCSb2\nU+YEpQip1bIcJZfc1VzuiRRazkVfJg2ftWs5CjuWn3r9FQ0uH8W2Ci0S4lFY\nFgak\r\n=GhWZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "e0e521789e0a6acaa28bc52f7067b88bc32457d4", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspect-js/is-bigint.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Is this value an ES BigInt?", "directories": {}, "_nodeVersion": "16.6.1", "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^1.1.5", "nyc": "^10.3.2", "tape": "^5.3.0", "eslint": "^7.32.0", "has-bigints": "^1.0.1", "has-symbols": "^1.0.2", "auto-changelog": "^2.3.0", "object-inspect": "^1.11.0", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-bigint_1.0.3_1628269598824_0.1650569909297812", "host": "s3://npm-registry-packages"}}, "1.0.4": {"name": "is-bigint", "version": "1.0.4", "keywords": ["bigint", "es", "integer", "is"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-bigint@1.0.4", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-bigint#readme", "bugs": {"url": "https://github.com/inspect-js/is-bigint/issues"}, "dist": {"shasum": "08147a1875bc2b32005d41ccd8291dffc6691df3", "tarball": "https://registry.npmjs.org/is-bigint/-/is-bigint-1.0.4.tgz", "fileCount": 10, "integrity": "sha512-zB9CruMamjym81i2JZ3UMn54PKGsQzsJeo6xvN3HJJ4CAsQNB6iRutp2To77OfCNuoxspsIhzaPoO1zyCEhFOg==", "signatures": [{"sig": "MEYCIQC7UDfUHNJXORBz2N2i+qazDKJ0B+nLmMZU0o0N3XHtTAIhAJI+yACE6t2ZbuGQfhA87ksDbwahyDVA+F3rD/0w+gy0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14781, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhFLbPCRA9TVsSAnZWagAAX6gP+gIhMM9IVg3jPEKn0WW4\ntJLAw9euIpLLpEloVPdGhtzt5ctYu402/AuBAycHcfL77Ea0OYQ31Xbw44u8\n7KMrRHCvQXQOe3Ke+lhbAcCa1uPbSSTzMZ8Bm7D6s5VYlutn+96b3TR/Pcb6\nTSiRh+mnnGTWpxJcdMNvaepfnTQZQC1zBZY3XkUus4yDOev2Y8p58XJVmVWn\n5rVLoEs7Cn9WhtGDMp2H9LCG8VnqBSc/4/+rJj3q2+MRgcpFAufZMdSdtcbm\nl6UAqqgqNLnaozdUyj/8ee37K63VSOQUZ7uRywqELAmGnWUxQ+eZ++N8bjMT\nrBGlrusgKluwKI3gOgA7l/Qx9tJxxI3gps34n7D+u+lTHIMVbpHPyEdCucG4\nOGwEkh1p4rGihEMyVEONEIXmFdBXJZlUT0gx1PSUFppiAt7irPBzAutAjgmi\nzVDWFdleUqYMYviH9lf8EeU3yiYpi6ZQwGeeviKcsGsyrFppyEgK7YaGgGIs\nhMO/xwuPLoIHX23bGHQ2rmIKL1jEzRDKJgC5payvXqlPleghCmkNpUb8pZo0\nFD3nSPf4djT0MKwLiBbPYviC0CnGV81ehRsXXGQHO6tvVmpMQW/OD8Vujkla\n/7PzMSy5EQ3mboa8RKPIrTnb+wCxi6xXCQzqZPtLeFYAUu7VijVsEmfQf5RK\nZ5xF\r\n=Ew8E\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "726750ea98291eaf3ac6aae247f638eb3a94d343", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspect-js/is-bigint.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Is this value an ES BigInt?", "directories": {}, "_nodeVersion": "16.6.1", "dependencies": {"has-bigints": "^1.0.1"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^1.1.5", "nyc": "^10.3.2", "tape": "^5.3.1", "eslint": "^7.32.0", "has-symbols": "^1.0.2", "auto-changelog": "^2.3.0", "object-inspect": "^1.11.0", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-bigint_1.0.4_1628747470915_0.7417934055665594", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "is-bigint", "version": "1.1.0", "description": "Is this value an ES BigInt?", "main": "index.js", "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "lint": "eslint .", "postlint": "tsc -p . && attw -P", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "npx npm@'>=10.2' audit --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/is-bigint.git"}, "keywords": ["bigint", "es", "integer", "is"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/is-bigint/issues"}, "homepage": "https://github.com/inspect-js/is-bigint#readme", "dependencies": {"has-bigints": "^1.0.2"}, "devDependencies": {"@arethetypeswrong/cli": "^0.17.0", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.0", "@types/for-each": "^0.3.3", "@types/object-inspect": "^1.13.0", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "es-value-fixtures": "^1.5.0", "eslint": "=8.8.0", "for-each": "^0.3.3", "has-tostringtag": "^1.0.2", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.3", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "engines": {"node": ">= 0.4"}, "_id": "is-bigint@1.1.0", "gitHead": "a9153e8408499e6531db1db8ccaea27c7e64fdb1", "types": "./index.d.ts", "_nodeVersion": "23.3.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-n4ZT37wG78iz03xPRKJrHTdZbe3IicyucEtdRsV5yglwc3GyUfbAfpSeD0FJ41NbUNSt5wbhqfp1fS+BgnvDFQ==", "shasum": "dda7a3445df57a42583db4228682eba7c4170672", "tarball": "https://registry.npmjs.org/is-bigint/-/is-bigint-1.1.0.tgz", "fileCount": 11, "unpackedSize": 17895, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBpiqrYLggEWb1IeLYUQg777nTllYLuz0RRr6/CCPA83AiEAk0NztEYD6AMooZAAmzq1FYlt6r9XvwAGZDb0fOwAKe8="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-bigint_1.1.0_1733164977934_0.9992967935355623"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-09-21T03:02:52.992Z", "modified": "2024-12-02T18:42:58.383Z", "1.0.0": "2018-09-21T03:02:53.146Z", "1.0.1": "2020-11-30T22:45:51.050Z", "1.0.2": "2021-05-04T20:45:53.325Z", "1.0.3": "2021-08-06T17:06:39.008Z", "1.0.4": "2021-08-12T05:51:11.120Z", "1.1.0": "2024-12-02T18:42:58.216Z"}, "bugs": {"url": "https://github.com/inspect-js/is-bigint/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/inspect-js/is-bigint#readme", "keywords": ["bigint", "es", "integer", "is"], "repository": {"type": "git", "url": "git+https://github.com/inspect-js/is-bigint.git"}, "description": "Is this value an ES BigInt?", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "readme": "# is-bigint <sup>[![Version Badge][2]][1]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![dependency status][5]][6]\n[![dev dependency status][7]][8]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][11]][1]\n\nIs this an ES BigInt value?\n\n## Example\n\n```js\nvar isBigInt = require('is-bigint');\nassert(!isBigInt(function () {}));\nassert(!isBigInt(null));\nassert(!isBigInt(function* () { yield 42; return Infinity; });\nassert(!isBigInt(Symbol('foo')));\n\nassert(isBigInt(1n));\nassert(isBigInt(Object(1n)));\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n[1]: https://npmjs.org/package/is-bigint\n[2]: https://versionbadg.es/inspect-js/is-bigint.svg\n[5]: https://david-dm.org/inspect-js/is-bigint.svg\n[6]: https://david-dm.org/inspect-js/is-bigint\n[7]: https://david-dm.org/inspect-js/is-bigint/dev-status.svg\n[8]: https://david-dm.org/inspect-js/is-bigint#info=devDependencies\n[11]: https://nodei.co/npm/is-bigint.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/is-bigint.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/is-bigint.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=is-bigint\n[codecov-image]: https://codecov.io/gh/inspect-js/is-bigint/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/inspect-js/is-bigint/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/inspect-js/is-bigint\n[actions-url]: https://github.com/inspect-js/is-bigint/actions\n", "readmeFilename": "README.md"}