{"_id": "prompt", "_rev": "269-12c9ce0fbd88e4aa51dcc03b88d73a40", "name": "prompt", "description": "A beautiful command-line prompt for node.js", "dist-tags": {"latest": "1.3.0"}, "versions": {"0.0.1": {"name": "prompt", "version": "0.0.1", "description": "Prompt the user of command line scripts while running.", "modules": {"index": "./prompt.js"}, "repository": {"type": "git", "url": "http://github.com/jes<PERSON><PERSON><PERSON><PERSON>/node-prompt.git"}, "author": {"name": "<PERSON>", "email": "josh.hol<PERSON>@gmail.com", "url": "http://jesusabdullah.github.com"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "http://catonmat.net"}], "engine": ["node >=0.1.100"], "_id": "prompt@0.0.1", "engines": {"node": "*"}, "_nodeSupported": true, "_npmVersion": "0.2.7-2", "_nodeVersion": "v0.3.1-pre", "dist": {"tarball": "https://registry.npmjs.org/prompt/-/prompt-0.0.1.tgz", "shasum": "9d814ca17b6e133b96a6648d631e7c20e45f9b2d", "integrity": "sha512-ldJ77a6gzzJ8xp8UEu5Xq8oKmGNGBLdV1sXhK9sfK8LiVrys4zI61BmV5ZTuzJ5r7rgLpxh7lo0ebWsZk1oaOQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC5LvDKBRnRp/Gqm+VYjzROa72hy8aJ7PosO/7LMEqlMQIgbA+6bcXoDYCzYP1m4dECXCEcPkeTGu8eAGh7b1YxwTc="}]}, "directories": {}}, "0.0.2": {"name": "prompt", "version": "0.0.2", "description": "Prompt the user of command line scripts while running.", "modules": {"index": "./prompt.js"}, "repository": {"type": "git", "url": "http://github.com/jes<PERSON><PERSON><PERSON><PERSON>/node-prompt.git"}, "author": {"name": "<PERSON>", "email": "josh.hol<PERSON>@gmail.com", "url": "http://jesusabdullah.github.com"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "http://catonmat.net"}], "engine": ["node >=0.1.100"], "_id": "prompt@0.0.2", "engines": {"node": "*"}, "_nodeSupported": true, "_npmVersion": "0.2.7-2", "_nodeVersion": "v0.3.1-pre", "dist": {"tarball": "https://registry.npmjs.org/prompt/-/prompt-0.0.2.tgz", "shasum": "d4a4ad8cc71c163b012292e0cdc9183fb76485af", "integrity": "sha512-hCewM3pvoHi1zocS8Tdd/T4xDsGcbo17ZiG09v70Pkt3sINQnF25BIGJnzf3C3lmfqCh0TFN/u8GWsRZ/SgbFA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCF3A70+i2p3+GkPm+/UIJFv9PFddDS0OmsqQNUJvDMzAIgPLoPxzW0DFyCioQRqI6sa0PMDvibEuARrlJWVuC4A/8="}]}, "directories": {}}, "0.0.3": {"name": "prompt", "version": "0.0.3", "description": "Prompt the user of command line scripts while running.", "main": "./prompt.js", "repository": {"type": "git", "url": "git://github.com/jes<PERSON><PERSON><PERSON><PERSON>/node-prompt.git"}, "author": {"name": "<PERSON>", "email": "josh.hol<PERSON>@gmail.com", "url": "http://jesusabdullah.github.com"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "http://catonmat.net"}], "engine": ["node >=0.1.100"], "_id": "prompt@0.0.3", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "0.3.15", "_nodeVersion": "v0.5.0-pre", "directories": {}, "files": [""], "_defaultsLoaded": true, "dist": {"shasum": "2eaaedfb91d1b92cf65892ddf400a22f038414e2", "tarball": "https://registry.npmjs.org/prompt/-/prompt-0.0.3.tgz", "integrity": "sha512-wy45wnl8N7VmaODn0inW7eTpwj738SxX3lPvtJmR3wWKTtjEI4fLOwdzvj6uEEVqjAF+vWzc0g1thK8EEgU0Mg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBZWybbKWLWmKrZBsRSz6qNTtsVBgFVr5abJsa9uocgkAiBbaFIIvy9RTi5I8GnCAqhpyHdDv64xYmBMFin5T4OPwA=="}]}}, "0.0.4": {"name": "prompt", "version": "0.0.4", "description": "Prompt the user of command line scripts while running.", "main": "./prompt.js", "repository": {"type": "git", "url": "git://github.com/jes<PERSON><PERSON><PERSON><PERSON>/node-prompt.git"}, "author": {"name": "<PERSON>", "email": "josh.hol<PERSON>@gmail.com", "url": "http://jesusabdullah.github.com"}, "contributors": [{"name": "<PERSON><PERSON>", "url": "http://catonmat.net"}, {"name": "<PERSON><PERSON> 'El Dios'", "url": "http://lele.amicofigo.com/"}], "engine": ["node >=0.1.100"], "dependencies": {}, "devDependencies": {}, "_id": "prompt@0.0.4", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.3", "_nodeVersion": "v0.5.0-pre", "_defaultsLoaded": true, "dist": {"shasum": "1522ef6678bfd554717d06aa6c8ec4b3b202f5fc", "tarball": "https://registry.npmjs.org/prompt/-/prompt-0.0.4.tgz", "integrity": "sha512-wU27jv4z4d3TrD2TGJ7zwEQMDlJgVQYQJQeBlGT6wuwCicfrc5LIyLIThU5W4Z/1TpnP/OHqkXWRkRbLqfm8hg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH8nHrUkL13f5/wTchKKLu6yFRHEKukEfYMT/0AAaqRmAiEAwFM/hEumVmfLoFXQZEIqlLsKLuUU62ZkP8MrHFCZAms="}]}, "scripts": {}, "directories": {}}, "0.1.0": {"name": "prompt", "description": "A beautiful command-line prompt for node.js", "version": "0.1.0", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "josh.hol<PERSON>@gmail.com"}], "repository": {"type": "git", "url": "git://github.com/nodejitsu/node-prompt.git"}, "dependencies": {"async": "0.1.x", "colors": "0.x.x", "winston": "0.2.x"}, "main": "./lib/prompt", "scripts": {"test": "vows test/*-test.js --spec"}, "engines": {"node": ">= 0.4.0"}, "devDependencies": {}, "_id": "prompt@0.1.0", "_engineSupported": true, "_npmVersion": "1.0.6", "_nodeVersion": "v0.4.8", "_defaultsLoaded": true, "dist": {"shasum": "a4a70d70b2fca1ceb57202474a7d854745b0c03c", "tarball": "https://registry.npmjs.org/prompt/-/prompt-0.1.0.tgz", "integrity": "sha512-JNCsNFT1Yf7pQdnkFYbLMD6rVWxvG4Ern0UAjVKJzjMkz1ql2t1qiwWJEVPTg3xJl+s0Mr577lbPtwLP+ghn6A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAqX2AkpoTF2PJmI6mlL2BSeSc/hmaAjZdGykhqVKkY0AiEA1Wc5+Mqyh896mrge8VuNCG8a39nVAhcCbE4zFHTwTM8="}]}, "directories": {}}, "0.1.1": {"name": "prompt", "description": "A beautiful command-line prompt for node.js", "version": "0.1.1", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "josh.hol<PERSON>@gmail.com"}], "repository": {"type": "git", "url": "git://github.com/nodejitsu/node-prompt.git"}, "dependencies": {"async": "0.1.x", "colors": "0.x.x", "winston": "0.3.x"}, "main": "./lib/prompt", "scripts": {"test": "vows test/*-test.js --spec"}, "engines": {"node": ">= 0.4.0"}, "_id": "prompt@0.1.1", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.10", "_nodeVersion": "v0.4.8", "_defaultsLoaded": true, "dist": {"shasum": "a806a80cb434e3e5a7856b00540ed7be54af4cbf", "tarball": "https://registry.npmjs.org/prompt/-/prompt-0.1.1.tgz", "integrity": "sha512-+Wvs8ER25bMcoV/YHKCv4aLXhODcXnffMGDPL48GFvT9V1SCXdXIZLi64p8GrR8kaSAquq2cEyGXLr0PmVji2g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCqYkLZ0bM5YapmyckJPSdLauTnc1trLAQKfJVLe6mXfwIgDIZNq4yIY7oKFPsCDP9+bPfLpX5DN+oAT0JDgAB+pgY="}]}, "directories": {}}, "0.1.2": {"name": "prompt", "description": "A beautiful command-line prompt for node.js", "version": "0.1.2", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "josh.hol<PERSON>@gmail.com"}], "repository": {"type": "git", "url": "git://github.com/nodejitsu/node-prompt.git"}, "dependencies": {"async": "0.1.x", "colors": "0.x.x", "winston": "0.3.x"}, "main": "./lib/prompt", "scripts": {"test": "vows test/*-test.js --spec"}, "engines": {"node": ">= 0.4.0"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/prompt/0.1.2/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "prompt@0.1.2", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.13", "_nodeVersion": "v0.4.8", "_defaultsLoaded": true, "dist": {"shasum": "ae104a308644e60d8a60732182f833b7cc49d38c", "tarball": "https://registry.npmjs.org/prompt/-/prompt-0.1.2.tgz", "integrity": "sha512-HIfFSangl6Xap/fNBDzzTBZqJVOEc/12UXonGCQzVfJtbS+dZdWMLfBMqzHcvNTAPa9yJEV+eu1V036WtYeO4w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD8uSfPOPP3uASpa+3kka/c6FR7DqTZ4Q8gin4K3FmfgQIhAOBAK1c/LXiwl1JARPaKr+UO+wK7Tr0K0Arm7W0coG2x"}]}, "directories": {}}, "0.1.3": {"name": "prompt", "description": "A beautiful command-line prompt for node.js", "version": "0.1.3", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "josh.hol<PERSON>@gmail.com"}], "repository": {"type": "git", "url": "git://github.com/nodejitsu/node-prompt.git"}, "dependencies": {"async": "0.1.x", "colors": "0.x.x", "pkginfo": "0.x.x", "winston": "0.3.x"}, "main": "./lib/prompt", "scripts": {"test": "vows test/prompt-test.js --spec"}, "engines": {"node": ">= 0.4.0"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/prompt/0.1.3/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "prompt@0.1.3", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.13", "_nodeVersion": "v0.4.8", "_defaultsLoaded": true, "dist": {"shasum": "2dec5ce9818ff01311fc49a0b9cac64da30f68ca", "tarball": "https://registry.npmjs.org/prompt/-/prompt-0.1.3.tgz", "integrity": "sha512-jg2A09hnxQKaGtewm8IOVvzsbwlGVwGQ6y53RAjxKtVOfkjvlYiE6s4m1GQKP6CR16VHTkSF/vEut1wjMtlQrQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDp2DbdP3OcnqhArNA6XIp3kzzCi3MFvS3hHKxJfMbuowIhAP0eA3YBF1H9wp4oa8zebDCZKRFsefHPzA42Kw+5d6sY"}]}, "directories": {}}, "0.1.4": {"name": "prompt", "description": "A beautiful command-line prompt for node.js", "version": "0.1.4", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "josh.hol<PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/nodejitsu/node-prompt.git"}, "dependencies": {"async": "0.1.x", "colors": "0.x.x", "pkginfo": "0.x.x", "winston": "0.3.x"}, "main": "./lib/prompt", "scripts": {"test": "vows test/prompt-test.js --spec"}, "engines": {"node": ">= 0.4.0"}, "_npmJsonOpts": {"file": "/home/<USER>/.npm/prompt/0.1.4/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "prompt@0.1.4", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.22", "_nodeVersion": "v0.4.9", "_defaultsLoaded": true, "dist": {"shasum": "1bfb57453a5dd2227cd9f798b089f6d783addd28", "tarball": "https://registry.npmjs.org/prompt/-/prompt-0.1.4.tgz", "integrity": "sha512-reojSUSOh+u4tBU5shHXQm4tIKNdtn2AKDVGQ3gioO44Pbg8LuelSYgQyK7pY8pa0carKB5RqUa6oONzHrg7Yw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDkXPkPGASuEDIFSwxjT87HYnlYCDycAg/AzkDgQgo2hAiEA5d+IreS/+2Rttt/1vDhnQrGLmgpoQA6eINNI36WL2Do="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "josh.hol<PERSON>@gmail.com"}, {"name": "indexzero", "email": "<EMAIL>"}], "directories": {}}, "0.1.5": {"name": "prompt", "description": "A beautiful command-line prompt for node.js", "version": "0.1.5", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "josh.hol<PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/nodejitsu/node-prompt.git"}, "dependencies": {"async": "0.1.x", "colors": "0.x.x", "pkginfo": "0.x.x", "winston": "0.3.x"}, "main": "./lib/prompt", "scripts": {"test": "vows test/prompt-test.js --spec"}, "engines": {"node": ">= 0.4.0"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/prompt/0.1.5/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "prompt@0.1.5", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.23", "_nodeVersion": "v0.4.10", "_defaultsLoaded": true, "dist": {"shasum": "dad553e3b2fea8cc38fd16c891404821c4d212b8", "tarball": "https://registry.npmjs.org/prompt/-/prompt-0.1.5.tgz", "integrity": "sha512-8Hv4J6PzFjoZxATvb4nzY3X+e43d1XxX10CSSiRikpAu0SnUhhG8VLDxSNUH62KKWvOgVb/UyfJqDXsKM5pLlg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEKkaleS6nhZDc7hbPc/jKHBvF1jatIxvHO4dxSL40pvAiARrvtEVR1q1z8bpdZvN1UeEUxXvoT8xrURSfmJ/IT3gw=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "josh.hol<PERSON>@gmail.com"}, {"name": "indexzero", "email": "<EMAIL>"}], "directories": {}}, "0.1.7": {"name": "prompt", "description": "A beautiful command-line prompt for node.js", "version": "0.1.7", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "josh.hol<PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/nodejitsu/node-prompt.git"}, "dependencies": {"async": "0.1.x", "colors": "0.x.x", "pkginfo": "0.x.x", "winston": "0.3.x"}, "main": "./lib/prompt", "scripts": {"test": "vows test/prompt-test.js --spec"}, "engines": {"node": ">= 0.4.0"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/prompt/0.1.7/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "prompt@0.1.7", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.23", "_nodeVersion": "v0.4.10", "_defaultsLoaded": true, "dist": {"shasum": "6841eacfe8374892427a06e0dc6c3447764a8b06", "tarball": "https://registry.npmjs.org/prompt/-/prompt-0.1.7.tgz", "integrity": "sha512-6TX9+6GagsMhQT/qw3eUDoXj7CvCzTrPrJD/7W5K84CSJ3L0baYGnMpXmk5f5CrQwK+dcabTXgAcUth2Wo16kA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB4EWNCk32QdIZUPQ1IZQA7kwtjfUNmO7OfOVkDOpzHIAiEAjUhqZcpkrd7BlT2cMoevHzhkdGvj06EWy2Q/JikE2H0="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "josh.hol<PERSON>@gmail.com"}, {"name": "indexzero", "email": "<EMAIL>"}], "directories": {}}, "0.1.8": {"name": "prompt", "description": "A beautiful command-line prompt for node.js", "version": "0.1.8", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "josh.hol<PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/nodejitsu/node-prompt.git"}, "dependencies": {"async": "0.1.x", "colors": "0.x.x", "pkginfo": "0.x.x", "winston": "0.5.x"}, "main": "./lib/prompt", "scripts": {"test": "vows test/prompt-test.js --spec"}, "engines": {"node": ">= 0.4.0"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/prompt/0.1.8/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "prompt@0.1.8", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.23", "_nodeVersion": "v0.4.11", "_defaultsLoaded": true, "dist": {"shasum": "2ef4f72517f916020b99a309626390f6d176a1e5", "tarball": "https://registry.npmjs.org/prompt/-/prompt-0.1.8.tgz", "integrity": "sha512-20YG7FoSSYarF8uDuoDAGdBA/KbysBgOmhSkG+Jce5aKqk/+QtaK+ORu06ST8IElidVHhCyEB3c1AWMsOmbIVA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD1DyCxsXlr2sjJVPmFGymgqnEskNOD1OLibhR1SrjgZgIgXUCxluzHA8TUQouoW1XqSuWjqDgVZ+eIOhtnAMczYfc="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "josh.hol<PERSON>@gmail.com"}, {"name": "indexzero", "email": "<EMAIL>"}], "directories": {}}, "0.1.9": {"name": "prompt", "description": "A beautiful command-line prompt for node.js", "version": "0.1.9", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "josh.hol<PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/nodejitsu/node-prompt.git"}, "dependencies": {"async": "0.1.x", "colors": "0.x.x", "pkginfo": "0.x.x", "winston": "0.5.x"}, "main": "./lib/prompt", "scripts": {"test": "vows test/prompt-test.js --spec"}, "engines": {"node": ">= 0.4.0"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/prompt/0.1.9/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "prompt@0.1.9", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.23", "_nodeVersion": "v0.4.11", "_defaultsLoaded": true, "dist": {"shasum": "6eae83a38c116dfe6118f2603db0461c942936d6", "tarball": "https://registry.npmjs.org/prompt/-/prompt-0.1.9.tgz", "integrity": "sha512-NHWbakaLxfwr0NIyMGaWR71UL0BJRTAT/3DBF1CiGoXcL0P11sg1meqECvn4l8GFDGBRvK4BRK/1LlveqIVTeA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDtY/MonwOfPGRh+0yQVBJR936oN6CcjLU5n2XzjVKq4AIgVz8ogI+veNIiQLCXQZOXMooQvu3PEE4AZugN7yEnGkU="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "josh.hol<PERSON>@gmail.com"}, {"name": "indexzero", "email": "<EMAIL>"}], "directories": {}}, "0.1.10": {"name": "prompt", "description": "A beautiful command-line prompt for node.js", "version": "0.1.10", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "josh.hol<PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/nodejitsu/node-prompt.git"}, "dependencies": {"async": "0.1.x", "colors": "0.x.x", "pkginfo": "0.x.x", "winston": "0.5.x"}, "main": "./lib/prompt", "scripts": {"test": "vows test/prompt-test.js --spec"}, "engines": {"node": ">= 0.4.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "josh.hol<PERSON>@gmail.com"}, "_id": "prompt@0.1.10", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.101", "_nodeVersion": "v0.4.13-pre", "_defaultsLoaded": true, "dist": {"shasum": "a9108958c53be72173fefe4e8f7aa7e17fbf965e", "tarball": "https://registry.npmjs.org/prompt/-/prompt-0.1.10.tgz", "integrity": "sha512-ffvQkMN/obdk381G7a2THj41YijJ7t+4DFOVKIyjKkHXYOsdu5TMyLFE+Bl+pjGVZdhwn0CpqVOOoScMkbfrgQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDZSZwIptukFdIWSalUtk2ELCm+GtnQWdv0VSUC2/7z6AiBH1hUIKrf/ilzrbBwQZh7tdVRHWpL8uFMeRGcU33ziog=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "josh.hol<PERSON>@gmail.com"}, {"name": "indexzero", "email": "<EMAIL>"}], "directories": {}}, "0.1.11": {"name": "prompt", "description": "A beautiful command-line prompt for node.js", "version": "0.1.11", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "josh.hol<PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/nodejitsu/node-prompt.git"}, "dependencies": {"async": "0.1.x", "colors": "0.x.x", "pkginfo": "0.x.x", "winston": "0.5.x"}, "devDependencies": {"vows": "0.6.x"}, "main": "./lib/prompt", "scripts": {"test": "vows test/prompt-test.js --spec"}, "engines": {"node": ">= 0.4.0"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "_id": "prompt@0.1.11", "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"shasum": "eac1bb9730a4aefd8f458380aa8d0b381fe6bbed", "tarball": "https://registry.npmjs.org/prompt/-/prompt-0.1.11.tgz", "integrity": "sha512-Hkr16W2q5avL0M3xF4zVimcngIWKVfa1YtKoqKoDYLojtMU4Hz/Ce7kICZS1cw/7Rt7JiGCMTRSkRzRRhT8TOQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD2+dFmw8bKpQ2S/MInWzAxlzd+mruWs+6i1BMmqu7gugIhAJz3nbEcMfecFTz/PseOL53GLawdlNyf9tNZllm7D8lu"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "josh.hol<PERSON>@gmail.com"}, {"name": "indexzero", "email": "<EMAIL>"}], "directories": {}}, "0.1.12": {"name": "prompt", "description": "A beautiful command-line prompt for node.js", "version": "0.1.12", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "josh.hol<PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/flatiron/prompt.git"}, "dependencies": {"async": "0.1.x", "colors": "0.x.x", "pkginfo": "0.x.x", "winston": "0.5.x"}, "devDependencies": {"vows": "0.6.x"}, "main": "./lib/prompt", "scripts": {"test": "vows test/prompt-test.js --spec", "test-all": "vows --spec"}, "engines": {"node": ">= 0.4.0"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "_id": "prompt@0.1.12", "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.6.6", "_defaultsLoaded": true, "dist": {"shasum": "d3114e4fb985ac66eaa35586dcb7b3fb3b27bfc6", "tarball": "https://registry.npmjs.org/prompt/-/prompt-0.1.12.tgz", "integrity": "sha512-xBp7idwXO6tda/j0ucpcw610VIv8oNAJKRWm1Xtf/QxLilkeMZV6BCd2p4QNeOgXk6w2cT5cFqmKHdFLRNczZA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDr3H+azwc5kpyQDv6VokshWz8/9gYTubPVG5HscOKKywIhAMdS61HBo2qN2q56dsKphUMDpPKdro93HmuRo93DhRl0"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "josh.hol<PERSON>@gmail.com"}, {"name": "indexzero", "email": "<EMAIL>"}], "directories": {}}, "0.2.0": {"name": "prompt", "description": "A beautiful command-line prompt for node.js", "version": "0.2.0", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "josh.hol<PERSON>@gmail.com"}, {"name": "indexzero", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/flatiron/prompt.git"}, "dependencies": {"utile": "0.1.x", "colors": "0.x.x", "pkginfo": "0.x.x", "winston": "0.5.x", "revalidator": "0.1.x"}, "devDependencies": {"vows": "0.6.x"}, "main": "./lib/prompt", "scripts": {"test": "vows test/prompt-test.js --spec", "test-all": "vows --spec"}, "engines": {"node": ">= 0.4.0"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "_id": "prompt@0.2.0", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.2", "_nodeVersion": "v0.6.18", "_defaultsLoaded": true, "dist": {"shasum": "ba04d352fffa89c61a5ea4abe0892dcda1f6d843", "tarball": "https://registry.npmjs.org/prompt/-/prompt-0.2.0.tgz", "integrity": "sha512-53pEZCbq6X3bvUpwPQGtZaI/Z31+KkpZHstIODu66iqAI1S5dzPuEV0JhUgSjUKFD5Ld9LPfpGNjZ5U1NtYF3A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICaS+L2JdXn22eMMPHaJeQryuM3oFCu8galUoKohYpgzAiEAkswfhW/uSI9nUPSMBNM3HuHV7kzh1mW7/2c9CC57PEw="}]}, "directories": {}}, "0.2.1": {"name": "prompt", "description": "A beautiful command-line prompt for node.js", "version": "0.2.1", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "josh.hol<PERSON>@gmail.com"}, {"name": "indexzero", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/flatiron/prompt.git"}, "dependencies": {"utile": "0.1.x", "colors": "0.x.x", "pkginfo": "0.x.x", "winston": "0.6.x", "revalidator": "0.1.x"}, "devDependencies": {"vows": "0.6.x"}, "main": "./lib/prompt", "scripts": {"test": "vows test/prompt-test.js --spec", "test-all": "vows --spec"}, "engines": {"node": ">= 0.4.0"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "_id": "prompt@0.2.1", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.2", "_nodeVersion": "v0.6.18", "_defaultsLoaded": true, "dist": {"shasum": "a391febda287c97c527f8b4dc3a5faed1c7ad961", "tarball": "https://registry.npmjs.org/prompt/-/prompt-0.2.1.tgz", "integrity": "sha512-HTqCMaVQXu7SzcaF1PJJxPMgTR3My8a6ZZTH5wHzi0X+fotszg9A0Ra6ZDCFX1i/0q9QCS2rMmljVOBGksYsaQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCyygnw8wB3K+8GdbG9Ttf6EfgA/y1WmVpQYQbx48KD1wIhAK5hG736qqS+YGvdnwYM+9n3PliqOHT+X60yDO1j7VmE"}]}, "directories": {}}, "0.2.2": {"name": "prompt", "description": "A beautiful command-line prompt for node.js", "version": "0.2.2", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "josh.hol<PERSON>@gmail.com"}, {"name": "indexzero", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/flatiron/prompt.git"}, "dependencies": {"pkginfo": "0.x.x", "read": "https://github.com/indexzero/read/tarball/refactor-optional-streams", "revalidator": "0.1.x", "utile": "0.1.x", "winston": "0.6.x"}, "devDependencies": {"vows": "0.6.x"}, "main": "./lib/prompt", "scripts": {"test": "vows test/prompt-test.js --spec", "test-all": "vows --spec"}, "engines": {"node": ">= 0.6.6"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "_id": "prompt@0.2.2", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.2", "_nodeVersion": "v0.8.1", "_defaultsLoaded": true, "dist": {"shasum": "bb8c8b998c444bf8b79ace018b44d169c1d55920", "tarball": "https://registry.npmjs.org/prompt/-/prompt-0.2.2.tgz", "integrity": "sha512-VixgBzOhiETrpqaQzWXQGXzZRsM2VK+Vu3zDm97N9arhADd6dz10wyxUC8mHhkwDeiPJqU7Dj+cUOLJ6sf5kEQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCztZxvP2Xfe6mlh8pDu9FmB/5BXJjkMIcHJsvSoyq1lAIgVOfydBpCsq8l6Q/GOwIdkOAChivVfdDz5HysNtRWrs4="}]}, "directories": {}}, "0.2.3": {"name": "prompt", "description": "A beautiful command-line prompt for node.js", "version": "0.2.3", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "josh.hol<PERSON>@gmail.com"}, {"name": "indexzero", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/flatiron/prompt.git"}, "dependencies": {"pkginfo": "0.x.x", "read": "0.1.x", "revalidator": "0.1.x", "utile": "0.1.x", "winston": "0.6.x"}, "devDependencies": {"vows": "0.6.x"}, "main": "./lib/prompt", "scripts": {"test": "vows test/prompt-test.js --spec", "test-all": "vows --spec"}, "engines": {"node": ">= 0.6.6"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "_id": "prompt@0.2.3", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.2", "_nodeVersion": "v0.8.1", "_defaultsLoaded": true, "dist": {"shasum": "eebdabba772b93a79dce761fd93763f962227d9f", "tarball": "https://registry.npmjs.org/prompt/-/prompt-0.2.3.tgz", "integrity": "sha512-XX1b/KB5h4dGR42mz3UJv32AzaYupgHJk7QCKuNTxBcBjabkDmLfGkrg64V7vrGM81JVOwJ/SrXtxPgRGudjQA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC47U2gf+fRWKWly5ZNm+FocXNCde3pmIj/B+gIzSZ2MgIhAOTITbXBHq/pOejo03WvO5xk+ZPmWRfl0okfkzl0301S"}]}, "directories": {}}, "0.2.4": {"name": "prompt", "description": "A beautiful command-line prompt for node.js", "version": "0.2.4", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "josh.hol<PERSON>@gmail.com"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/flatiron/prompt.git"}, "dependencies": {"pkginfo": "0.x.x", "read": "1.0.x", "revalidator": "0.1.x", "utile": "0.1.x", "winston": "0.6.x"}, "devDependencies": {"vows": "0.6.x"}, "main": "./lib/prompt", "scripts": {"test": "vows test/prompt-test.js --spec", "test-all": "vows --spec"}, "engines": {"node": ">= 0.6.6"}, "_id": "prompt@0.2.4", "dist": {"shasum": "fc399928145a12f4bac043a6cbcbab8816451d86", "tarball": "https://registry.npmjs.org/prompt/-/prompt-0.2.4.tgz", "integrity": "sha512-Ukx7rDGyMp9pXWp1rujBs85EnFDyqGqKqj9m4znc/yECPVcYHCzOOr0AB1tesTQqFSl6JhL7v+03m14+jFYl3g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID3Pq+pT/mrs3SgfSOI0ENQhD/UM8KJPMbwUYJW7GgisAiB+v0kj/3utGthiFCn+UJmSOLx88Y3WFWsujLehiB4iiw=="}]}, "directories": {}}, "0.2.5": {"name": "prompt", "description": "A beautiful command-line prompt for node.js", "version": "0.2.5", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "josh.hol<PERSON>@gmail.com"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/flatiron/prompt.git"}, "dependencies": {"pkginfo": "0.x.x", "read": "1.0.x", "revalidator": "0.1.x", "utile": "0.1.x", "winston": "0.6.x"}, "devDependencies": {"vows": "0.6.x"}, "main": "./lib/prompt", "scripts": {"test": "vows test/prompt-test.js --spec", "test-all": "vows --spec"}, "engines": {"node": ">= 0.6.6"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "_id": "prompt@0.2.5", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.2", "_nodeVersion": "v0.8.6", "_defaultsLoaded": true, "dist": {"shasum": "3b4d5a83c0f785e250dd86b81c7452df998cb4e2", "tarball": "https://registry.npmjs.org/prompt/-/prompt-0.2.5.tgz", "integrity": "sha512-0I4mjWwhvODaMlV73Ct6v3VOLd8WRb4qbz0ofEcna06UH4IuHcqhjjuEj5QVdG+sWOUzJHgM0UwxKdmBrdtFxA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCd18i9V6TfSvKzYa9ISXFPekRy8CAdAg9twffe65FRwAIgH7hc1Gl8iIBSmwhvSNJBgSeSsOhST5M9WX/c4+xoEP0="}]}, "directories": {}}, "0.2.6": {"name": "prompt", "description": "A beautiful command-line prompt for node.js", "version": "0.2.6", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "josh.hol<PERSON>@gmail.com"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fedor.indutny", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/flatiron/prompt.git"}, "dependencies": {"pkginfo": "0.x.x", "read": "1.0.x", "revalidator": "0.1.x", "utile": "0.1.x", "winston": "0.6.x"}, "devDependencies": {"vows": "0.6.x"}, "main": "./lib/prompt", "scripts": {"test": "vows test/prompt-test.js --spec", "test-all": "vows --spec"}, "engines": {"node": ">= 0.6.6"}, "_id": "prompt@0.2.6", "dist": {"shasum": "1136549ea0a848c9dff8ef3acf3f71c9a94d8465", "tarball": "https://registry.npmjs.org/prompt/-/prompt-0.2.6.tgz", "integrity": "sha512-Je83QY6HzaIvBR92bAIIF+cialXj36Lq8Ou3FArSLPVmDTXTbDbKHWnUIBwAH6JNsZcHrRF6lkOZQqu++XIakQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFfu1dXFPIITMbb0cnybNyQAj0qIsuEZ0g1js8isAlxZAiEArpWyjiWFYuIiobaUGyUD7GfPvHO6b/ZRe0F/k28yr+E="}]}, "directories": {}}, "0.2.7": {"name": "prompt", "description": "A beautiful command-line prompt for node.js", "version": "0.2.7", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "josh.hol<PERSON>@gmail.com"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fedor.indutny", "email": "<EMAIL>"}, {"name": "marak", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/flatiron/prompt.git"}, "dependencies": {"pkginfo": "0.x.x", "read": "1.0.x", "revalidator": "0.1.x", "utile": "0.1.x", "winston": "0.6.x"}, "devDependencies": {"vows": "0.6.x"}, "main": "./lib/prompt", "scripts": {"test": "vows test/prompt-test.js --spec", "test-all": "vows --spec"}, "engines": {"node": ">= 0.6.6"}, "_id": "prompt@0.2.7", "dist": {"shasum": "a4aa2f04dbbfa9eee4d658d59e5f32bde10dc01d", "tarball": "https://registry.npmjs.org/prompt/-/prompt-0.2.7.tgz", "integrity": "sha512-rPrsSgaBkzyJcfmGh9Y03uHRPwlJcsgx1tkgjX7YdjaXL1CwZCJzteVd0ytizyz5oC0ZPVu7Emcq520xjv03Nw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC+l1WkBD2oCUfO2PpiHm3HP0ebwTpwoIeV3zNyVTN0AQIhAK0e78/pBkfbWu3GFtPL9fDfzUwZTOB4GZqRyD9CjB9a"}]}, "directories": {}}, "0.2.8": {"name": "prompt", "description": "A beautiful command-line prompt for node.js", "version": "0.2.8", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "josh.hol<PERSON>@gmail.com"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fedor.indutny", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/flatiron/prompt.git"}, "dependencies": {"pkginfo": "0.x.x", "read": "1.0.x", "revalidator": "0.1.x", "utile": "0.1.x", "winston": "0.6.x"}, "devDependencies": {"vows": "0.6.x"}, "main": "./lib/prompt", "scripts": {"test": "vows test/prompt-test.js --spec", "test-all": "vows --spec"}, "engines": {"node": ">= 0.6.6"}, "_id": "prompt@0.2.8", "dist": {"shasum": "012bfe09f036c4e6520425a8101efc89a5c86430", "tarball": "https://registry.npmjs.org/prompt/-/prompt-0.2.8.tgz", "integrity": "sha512-O47TwIkfkxbmULX9UJLd5UN/4FrQ1WedUKzwAphi/siboGaecoirc16NPbhPTa9LJmENlniy68PQfFe9iCR21g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFjc+b5QzMIVfzzaQUcDJM3Xkw4N24Ej0sXGOQVwr6JdAiA2o2hBwaMNv4YKCQl2ImWWJwTr7/t9NIBZF8L30Dwlzg=="}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "josh.hol<PERSON>@gmail.com"}, "directories": {}}, "0.2.9": {"name": "prompt", "description": "A beautiful command-line prompt for node.js", "version": "0.2.9", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fedor.indutny", "email": "<EMAIL>"}, {"name": "avianflu", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/flatiron/prompt.git"}, "dependencies": {"pkginfo": "0.x.x", "read": "1.0.x", "revalidator": "0.1.x", "utile": "0.1.x", "winston": "0.6.x"}, "devDependencies": {"vows": "0.7.0"}, "main": "./lib/prompt", "scripts": {"test": "vows test/prompt-test.js --spec", "test-all": "vows --spec"}, "engines": {"node": ">= 0.6.6"}, "_id": "prompt@0.2.9", "dist": {"shasum": "fdd01e3f9654d0c44fbb8671f8d3f6ca009e3c16", "tarball": "https://registry.npmjs.org/prompt/-/prompt-0.2.9.tgz", "integrity": "sha512-zzMfxK+IdU9exp+QFFnJmyYqcNWap+ANL/YmsJ+ynyMRa6DATYXapAFs/0VYcUbgXAEjtnGyz4glWTr8vGZ3Xw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFGj0ZFCuogWel+KQY3iGxjTgWYuLUzbTZH1rUwAtz26AiEA9Y5/TCipiEs5SmPEs36JiP8LsOZcr2G32UIwhyY2n0U="}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "avianflu", "email": "<EMAIL>"}, "directories": {}}, "0.2.10": {"name": "prompt", "description": "A beautiful command-line prompt for node.js", "version": "0.2.10", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fedor.indutny", "email": "<EMAIL>"}, {"name": "avianflu", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/flatiron/prompt.git"}, "dependencies": {"pkginfo": "0.x.x", "read": "1.0.x", "revalidator": "0.1.x", "utile": "0.2.x", "winston": "0.6.x"}, "devDependencies": {"vows": "0.7.0"}, "main": "./lib/prompt", "scripts": {"test": "vows test/prompt-test.js --spec", "test-all": "vows --spec"}, "engines": {"node": ">= 0.6.6"}, "bugs": {"url": "https://github.com/flatiron/prompt/issues"}, "_id": "prompt@0.2.10", "dist": {"shasum": "862de19335443efb35500b9acc3085797f0d7f90", "tarball": "https://registry.npmjs.org/prompt/-/prompt-0.2.10.tgz", "integrity": "sha512-W20ej8tLUZXx30R2TMKmcM0oLU62/n/Ul+ErWkVoGwwVM5ExY27OCuByBWAjLR5xMPXGIVH7ufZ+ZcPK9cAeMQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICnr2JEkLwEa9dfRDe/fdkGbKruNVwIAYORGIuraFx/1AiBLneIsuynoND3++SRJldgCwFqTddJM7uFPwT3ehfdXkg=="}]}, "_from": ".", "_npmVersion": "1.2.32", "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "directories": {}}, "0.2.11": {"name": "prompt", "description": "A beautiful command-line prompt for node.js", "version": "0.2.11", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fedor.indutny", "email": "<EMAIL>"}, {"name": "avianflu", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/flatiron/prompt.git"}, "dependencies": {"pkginfo": "0.x.x", "read": "1.0.x", "revalidator": "0.1.x", "utile": "0.2.x", "winston": "0.6.x"}, "devDependencies": {"vows": "0.7.0"}, "main": "./lib/prompt", "scripts": {"test": "vows test/prompt-test.js --spec", "test-all": "vows --spec"}, "engines": {"node": ">= 0.6.6"}, "bugs": {"url": "https://github.com/flatiron/prompt/issues"}, "_id": "prompt@0.2.11", "dist": {"shasum": "26d455af4b7fac15291dfcdddf2400328c1fa446", "tarball": "https://registry.npmjs.org/prompt/-/prompt-0.2.11.tgz", "integrity": "sha512-q/FNVobXWw4QcFlsRLanDwkb7gkiRWV6r1ZezRAT8ltYRTHH3IiypyEN8AcSvJ5Vk1MVqCU1B7J6F8wUzPUafA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDfXir0lluXI79kXbME3/djw9zszlzDnzhzrW1v2jrnYAiBjFEBI+KG9nfT7aJOQqeQ9H1x19O8pPUEO0JkDjgSqsQ=="}]}, "_from": ".", "_npmVersion": "1.2.32", "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "directories": {}}, "0.2.12": {"name": "prompt", "description": "A beautiful command-line prompt for node.js", "version": "0.2.12", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fedor.indutny", "email": "<EMAIL>"}, {"name": "avianflu", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/flatiron/prompt.git"}, "dependencies": {"pkginfo": "0.x.x", "read": "1.0.x", "revalidator": "0.1.x", "utile": "0.2.x", "winston": "0.6.x"}, "devDependencies": {"vows": "0.7.0"}, "main": "./lib/prompt", "scripts": {"test": "vows test/prompt-test.js --spec", "test-all": "vows --spec"}, "engines": {"node": ">= 0.6.6"}, "bugs": {"url": "https://github.com/flatiron/prompt/issues"}, "_id": "prompt@0.2.12", "dist": {"shasum": "3ed2d13f1921c69126c0447ae14c83fd481e6119", "tarball": "https://registry.npmjs.org/prompt/-/prompt-0.2.12.tgz", "integrity": "sha512-v9mhPUWTJpZS+jYAZPyDmKlvrUZnOKomq/ctL563lTH9laBuJ5NiQ+HzZ+RZGCjwram05LSL25ERoAn+r3k30Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICIJv/UIiQiXb0a/jiH/+RMIfbhYsWUTm64QP7tP60h/AiEAn0qcJEE3hkc3Vmm/L2SCZqBUdWDDTFBYXab1iDF92aY="}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "directories": {}}, "0.2.13": {"name": "prompt", "description": "A beautiful command-line prompt for node.js", "version": "0.2.13", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fedor.indutny", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/flatiron/prompt.git"}, "dependencies": {"pkginfo": "0.x.x", "read": "1.0.x", "revalidator": "0.1.x", "utile": "0.2.x", "winston": "0.6.x"}, "devDependencies": {"vows": "0.7.0"}, "main": "./lib/prompt", "scripts": {"test": "vows test/prompt-test.js --spec", "test-all": "vows --spec"}, "engines": {"node": ">= 0.6.6"}, "gitHead": "fc694eea7c5c0b1b97f4faa074526f0eb5e967f0", "bugs": {"url": "https://github.com/flatiron/prompt/issues"}, "homepage": "https://github.com/flatiron/prompt", "_id": "prompt@0.2.13", "_shasum": "58c1dab30cf3aab470b658511b121ec9d4ccafaa", "_from": ".", "_npmVersion": "1.4.13", "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "dist": {"shasum": "58c1dab30cf3aab470b658511b121ec9d4ccafaa", "tarball": "https://registry.npmjs.org/prompt/-/prompt-0.2.13.tgz", "integrity": "sha512-uP8rdHDusddzSWR0rT/P8GlVbhMVjrZefoY3eEWed8bJ3UIZ4wAmFaNn+P9YgGzHGFq8Azx6ifz7EJYffJOZZA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGdC9TCKJgH0P+2EXctYMKdJO5Y2S+36TvBX+TcimPziAiEA32978liASkJP+n5lvSHcG2FcPX+K+J0zvv4PWN46BDY="}]}, "directories": {}}, "0.2.14": {"name": "prompt", "description": "A beautiful command-line prompt for node.js", "version": "0.2.14", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fedor.indutny", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/flatiron/prompt.git"}, "dependencies": {"pkginfo": "0.x.x", "read": "1.0.x", "revalidator": "0.1.x", "utile": "0.2.x", "winston": "0.8.x"}, "devDependencies": {"vows": "0.7.0"}, "main": "./lib/prompt", "scripts": {"test": "vows test/prompt-test.js --spec", "test-all": "vows --spec"}, "engines": {"node": ">= 0.6.6"}, "gitHead": "e1d3df66acfe9de33a573bef1c0a1b18d18cc698", "bugs": {"url": "https://github.com/flatiron/prompt/issues"}, "homepage": "https://github.com/flatiron/prompt", "_id": "prompt@0.2.14", "_shasum": "57754f64f543fd7b0845707c818ece618f05ffdc", "_from": ".", "_npmVersion": "1.4.23", "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "dist": {"shasum": "57754f64f543fd7b0845707c818ece618f05ffdc", "tarball": "https://registry.npmjs.org/prompt/-/prompt-0.2.14.tgz", "integrity": "sha512-jDK5yEbAakJmNm+260gZG1+PuzX3jT5Jy0VZAUGrrW9RQ1JEWEDEVNnhO70mL3+U5r6bSJo02xsE34wOS/LnrA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCYOqhH0Oc03CjYb00kp+8PPBPoWvxbPqjl+nEnj9CSZAIhAL8fsbokydVGyZlJS2kRIxMbXZVZziYqUD/sLTL8J7L7"}]}, "directories": {}}, "0.3.0": {"name": "prompt", "description": "A beautiful command-line prompt for node.js", "version": "0.3.0", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "fedor.indutny", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/flatiron/prompt.git"}, "dependencies": {"pkginfo": "0.x.x", "read": "1.0.x", "revalidator": "0.1.x", "utile": "0.2.x", "winston": "0.8.x"}, "devDependencies": {"vows": "0.7.0"}, "main": "./lib/prompt", "scripts": {"test": "vows test/prompt-test.js --spec", "test-all": "vows --spec"}, "license": "MIT", "engines": {"node": ">= 0.6.6"}, "gitHead": "df2c14007961d7df7037c191a9d4ac347d287db1", "bugs": {"url": "https://github.com/flatiron/prompt/issues"}, "homepage": "https://github.com/flatiron/prompt#readme", "_id": "prompt@0.3.0", "_shasum": "93a4bd453b2ee5317b3c2bb666b20c1ceb454138", "_from": ".", "_npmVersion": "2.14.14", "_nodeVersion": "0.10.41", "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "dist": {"shasum": "93a4bd453b2ee5317b3c2bb666b20c1ceb454138", "tarball": "https://registry.npmjs.org/prompt/-/prompt-0.3.0.tgz", "integrity": "sha512-Kn9pAozxalZneP8RJIirzIne10pdi4OJNpDNZGpkYratCwrmHcoK7mUmNLuJzqQQZ9nJbhHGU9Mzvcyrpfp+0w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCADzmhZYVSHoTdUvzGNT0PWP96HjHfET7MOPgndQlekQIhAPRQV4YAKE49Fk/ak3UnlQFccP3CWtlSBSFMROtnSdUi"}]}, "_npmOperationalInternal": {"host": "packages-9-west.internal.npmjs.com", "tmp": "tmp/prompt-0.3.0.tgz_1454882480611_0.3379449872300029"}, "directories": {}}, "1.0.0": {"name": "prompt", "version": "1.0.0", "description": "A beautiful command-line prompt for node.js", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "fedor.indutny", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/flatiron/prompt.git"}, "dependencies": {"colors": "^1.1.2", "pkginfo": "0.x.x", "read": "1.0.x", "revalidator": "0.1.x", "utile": "0.3.x", "winston": "2.1.x"}, "devDependencies": {"vows": "0.7.0"}, "main": "./lib/prompt", "scripts": {"test": "vows test/prompt-test.js --spec", "test-all": "vows --spec"}, "license": "MIT", "engines": {"node": ">= 0.6.6"}, "gitHead": "11f2a2b5123a55c5edd9f11e07f4190e6cf61907", "bugs": {"url": "https://github.com/flatiron/prompt/issues"}, "homepage": "https://github.com/flatiron/prompt#readme", "_id": "prompt@1.0.0", "_shasum": "8e57123c396ab988897fb327fd3aedc3e735e4fe", "_from": ".", "_npmVersion": "2.14.1", "_nodeVersion": "4.2.2", "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "dist": {"shasum": "8e57123c396ab988897fb327fd3aedc3e735e4fe", "tarball": "https://registry.npmjs.org/prompt/-/prompt-1.0.0.tgz", "integrity": "sha512-SIliATETjBHvX5c2h8xhjFP0GmGie58sdq7utvoMIv1qkcow3O/NLy21ME35D3uCMYYf/ZASPzG6kFTKXZ8Yxg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDnx6ctob21daUfTV5lEXPsvqJBPArPZ0Se7O7p5AoFOgIhAMFuNpB7fj897yVG48Flg+5k7i/kXan+VOa66G059me9"}]}, "_npmOperationalInternal": {"host": "packages-5-east.internal.npmjs.com", "tmp": "tmp/prompt-1.0.0.tgz_1455093788545_0.33723675599321723"}, "directories": {}}, "1.1.0": {"name": "prompt", "version": "1.1.0", "description": "A beautiful command-line prompt for node.js", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "fedor.indutny", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "caub", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/flatiron/prompt.git"}, "keywords": ["prompt", "command-line", "customize", "validation"], "dependencies": {"colors": "^1.1.2", "read": "1.0.x", "revalidator": "0.1.x", "utile": "0.3.x", "winston": "2.x"}, "devDependencies": {"vows": "0.7.0"}, "main": "./lib/prompt", "scripts": {"test": "vows test/prompt-test.js --spec", "test-all": "vows --spec"}, "license": "MIT", "engines": {"node": ">= 0.6.6"}, "gitHead": "c071b85b9a28e3fb561d53d1d541469c8fe6ec1e", "bugs": {"url": "https://github.com/flatiron/prompt/issues"}, "homepage": "https://github.com/flatiron/prompt#readme", "_id": "prompt@1.1.0", "_nodeVersion": "14.15.2", "_npmVersion": "6.14.10", "dist": {"integrity": "sha512-ec1vUPXCplDBDUVD8uPa3XGA+OzLrO40Vxv3F1uxoiZGkZhdctlK2JotcHq5X6ExjocDOGwGdCSXloGNyU5L1Q==", "shasum": "7ae829c6d39bbc6f9b1927f9861e9ac074744167", "tarball": "https://registry.npmjs.org/prompt/-/prompt-1.1.0.tgz", "fileCount": 29, "unpackedSize": 136211, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf4NsqCRA9TVsSAnZWagAAZJ0P/A+5TVQS/2ZcjFa3qbXY\nrgRuXwK4/q5YoYWeHjUFxsDQZ/+WXhcOzCWaW2sSg1ZvA2m8dKW1KUiyCjv6\naUuPbjvKNBx3kR8VguTrVBn4ruUGJr0osH65tdcBBQT8GLGsLBcR4rRilB1S\nLS9waAQm3U3Jv5NR/lemVdXB466l/c4ioGLySDmHaTM9IY9H1JoCO3aeEw8G\n0pl2wlZrrVDHjtUVMW3E3V50fJLLQRMhIL8yTnwSg6LazTezo7NK6v29WjsU\nYHm5n7NhGAEzFhwi9VqbGIrxheVPMhz205dE4SChpPc3PqC/78oocB8NEMO8\n44Z5E0pdI/mi/hUSSxUYyzLOvLpnka+iR7Eak8ONzWZgAf6SJ/N4DWJZX3hG\nCjE3lyljxM24O+XuCJQlkeBu50zooP7pimxKvm5XLlwCNiWM/PQTQXd4Prq9\nlYW6oOcfyXd7W0EKmQHbWTWdoLLUppFSNr//Q1givO8LEo3OyiFuqIk6PUOI\n7jcdQgMNm9lr8CqdbXctHjsPiqxGUZGBD0gzLifqToeQgTq8Mi0tZ1nagoXa\nshbXLuYMNjkRJ4Ywl5e3thuaIvuwojs7lJ2YT0eCeVDg/euf8TbY6OTjdd0W\nb/hk2DI3wU3Wotis5xCz884vJhxGZ7vlVzOfDBh4aDDV8LWC8uI+dqFcjnNL\nWPdR\r\n=kbGW\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEFQn68TSgMowuDYjLqgNpZjLZiqqvBZW+IJE8QTNf1rAiBQ1GWEBD9IsunC02pJfZEvysK6aaUKC915K4D6mDnQmA=="}]}, "_npmUser": {"name": "caub", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompt_1.1.0_1608571689595_0.015586211911432724"}, "_hasShrinkwrap": false}, "1.2.0": {"name": "prompt", "version": "1.2.0", "description": "A beautiful command-line prompt for node.js", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "fedor.indutny", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "caub", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/flatiron/prompt.git"}, "keywords": ["prompt", "command-line", "customize", "validation"], "dependencies": {"async": "~0.9.0", "colors": "^1.1.2", "read": "1.0.x", "revalidator": "0.1.x", "winston": "2.x"}, "devDependencies": {"eslint": "^7.32.0", "vows": "^0.7.0"}, "main": "./lib/prompt", "scripts": {"test": "vows test/prompt-test.js --spec", "test-all": "vows --spec"}, "license": "MIT", "engines": {"node": ">= 0.6.6"}, "gitHead": "fbf6dac88400b9d66e9928186feb2be49b80098e", "bugs": {"url": "https://github.com/flatiron/prompt/issues"}, "homepage": "https://github.com/flatiron/prompt#readme", "_id": "prompt@1.2.0", "_nodeVersion": "16.7.0", "_npmVersion": "7.20.3", "dist": {"integrity": "sha512-iGerYRpRUg5ZyC+FJ/25G5PUKuWAGRjW1uOlhX7Pi3O5YygdK6R+KEaBjRbHSkU5vfS5PZCltSPZdDtUYwRCZA==", "shasum": "5d8f1d9011766bac07abde510dbf4338d87b4f02", "tarball": "https://registry.npmjs.org/prompt/-/prompt-1.2.0.tgz", "fileCount": 30, "unpackedSize": 136830, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhJncICRA9TVsSAnZWagAAU4oP/1n69jSAUWDo/EuFhahd\n6J5FLcK0TmVpHtA4Ywhe/7+dOXCjb4M39WElYkyuH5KLVV+7RCBX1UYfAMNS\nj3/EjGiroBCOMdSKmbHTXX5P4hnJOFaqxjhf8eVYom+ueoBe4M97QHKs0zOP\nVdVel1OAoyjqQiBW53LtHsbUeg9QJkM5ZBSXX0dzipOOwds53F7ppPIWiFKS\nx2w3cdFUc3YPfcvfPzTX2LonypjA+jH8rNdEVynEaJK7WXjZ+32/X5qq3HYv\nQo5ehDiU0lhybuEl7r41AAE5iVPzFOoOLnM4UWT5yQIQu5iaiJFlAXyzIA1s\nsT1eqUMqru+ajna9o/zJehnuS6cuUrK152ekhvqr8UlxhVHGhsYjvQMj+yAf\n4n4afvbC42bVvm+Sfo7xpOzLbGCBRVEdu0fkf9hyJk6M0umSjWsjA/fP+oYQ\nOclJsAapzOjAEDT3sW8p5QRQUTToWUN4daRJ8TDApH3VHl2l0zcLEsF4Ys+R\n5TEfUySi6k6fNnwa7NdjBaEd+41GgzIBXpO5Q7PLzvaTqBUcoqBb6+kJ/cZt\npWw0+80VLmzIkzoZzhdar7NEYvlKz/XGoUGZR77OsPkbCtQXLrNBoqGoOBCL\nfE9usD5V568y/X0KH4cwJx2hogefi9bD9O182+NNNH2Dhy/VGGMBaWtNmejC\nL71M\r\n=e+V7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD3GBoa8hPuC4omZ47dddQJiG2x1GZbFw/pnjNMx+z8KgIgGUQqWx5htkX4Ty6cC2g3+CsOTd0X2TUsBiyp6jza988="}]}, "_npmUser": {"name": "caub", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompt_1.2.0_1629910792222_0.27885809746319445"}, "_hasShrinkwrap": false}, "1.2.1": {"name": "prompt", "version": "1.2.1", "description": "A beautiful command-line prompt for node.js", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "fedor.indutny", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "caub", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/flatiron/prompt.git"}, "keywords": ["prompt", "command-line", "customize", "validation"], "dependencies": {"async": "~0.9.0", "colors": "1.4.0", "read": "1.0.x", "revalidator": "0.1.x", "winston": "2.x"}, "devDependencies": {"eslint": "^7.32.0", "vows": "^0.7.0"}, "main": "./lib/prompt", "scripts": {"test": "vows test/prompt-test.js --spec", "test-all": "vows --spec"}, "license": "MIT", "engines": {"node": ">= 0.6.6"}, "gitHead": "62c2707e9a399bf4d7fac6a38246cce6012020d2", "bugs": {"url": "https://github.com/flatiron/prompt/issues"}, "homepage": "https://github.com/flatiron/prompt#readme", "_id": "prompt@1.2.1", "_nodeVersion": "16.13.1", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-B4+2QeNDn5Cdp4kK2iOwV8qvrWpiPKlZKI9ZKkPl0C9KgeMW6DyWWqhqHiFq9vZf6zTniv+rYalK0ZlgktSwiw==", "shasum": "49f46f17aacbbf501786fc6f3a30d99075c846c9", "tarball": "https://registry.npmjs.org/prompt/-/prompt-1.2.1.tgz", "fileCount": 30, "unpackedSize": 136944, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3BT3CRA9TVsSAnZWagAAhNUP/iSBQx0LuUpFgyhOoyR8\n2+0dAdGFeBuOtoN/7Ty4Cyk4rh3/4av/dyAecojTyDIJoPZtUQOJgW8TNvwC\nR0nx7rexhhTOS7WyNiGEXXeIlfRAMOtY1zRL15BWAAQffmdq9uLfGgehS8Z0\nEA6u1jPf3tHD0MiFY9vnFGv2melmnLDn2gzInTlwr1dJ/3FhMGOSq64VuTJa\nFQLNvxtF2r86ZzU8KdCYTRMyOFc0ED7JWTsPWfJA5grSPKtd4wrgGvWVorLT\n3oZuoUFLAon6P/YpyWff2hyW+Z6jv1bOV5HOwaQufd1TKGpTR+ns+cMbyRIS\nENZseSiynhFT2IF23O6YBgtWcicL1or0HSaori0Bwh8K61Daylt5r7lPHk3E\nIsOfkjnwN2DhwYkVtHTMzRm1Dk1vwNKWvfvJusav/UhbpaByEu171YCqksl8\njMlyAz9muJmesW/kuBymd3GhBfco0HM09lWjrCWmN7tez2EvY2iLi8Xz+jhC\nEOYy19QZ8i3L44nKCdYEI8DfsfARVtsDbRR0SBSZbE+7Rexfp/E+weQymHhy\nRUfvU84eHhl+l1kW7ALEiblbpIWl0Acvk3u3WExQDgEjM7bWAcByYEKFSIfq\nLSjogseGI4bzbpFcfk/1QLOKaMYjEB5Uk7RDibAx01NAkXusXZg264LF8fqz\ndXnA\r\n=+v/k\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCEPxEOUEYexnDUqkBy4ATpgkUoptMkBhrDZRq4AusjvgIhAOeTNtmrWuMFiGxgpVJXCRAHIv9fVrqrJpkLU4hqEkk/"}]}, "_npmUser": {"name": "caub", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompt_1.2.1_1641813239538_0.013426870356619602"}, "_hasShrinkwrap": false}, "1.2.2": {"name": "prompt", "version": "1.2.2", "description": "A beautiful command-line prompt for node.js", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "fedor.indutny", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "caub", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/flatiron/prompt.git"}, "keywords": ["prompt", "command-line", "customize", "validation"], "dependencies": {"async": "~0.9.0", "@colors/colors": "1.5.0", "read": "1.0.x", "revalidator": "0.1.x", "winston": "2.x"}, "devDependencies": {"eslint": "^7.32.0", "vows": "^0.7.0"}, "main": "./lib/prompt", "scripts": {"test": "vows test/prompt-test.js --spec", "test-all": "vows --spec"}, "license": "MIT", "engines": {"node": ">= 0.6.6"}, "gitHead": "85502f50e8095b16d725a6698d756ec2c4edc81c", "bugs": {"url": "https://github.com/flatiron/prompt/issues"}, "homepage": "https://github.com/flatiron/prompt#readme", "_id": "prompt@1.2.2", "_nodeVersion": "16.14.0", "_npmVersion": "8.3.1", "dist": {"integrity": "sha512-XNXhNv3PUHJDcDkISpCwSJxtw9Bor4FZnlMUDW64N/KCPdxhfVlpD5+YUXI/Z8a9QWmOhs9KSiVtR8nzPS0BYA==", "shasum": "b624fcf53aa6c8c5637e009c193ef69eee45dbe0", "tarball": "https://registry.npmjs.org/prompt/-/prompt-1.2.2.tgz", "fileCount": 30, "unpackedSize": 136975, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDoerACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrX4Q/9HPd785paL5lZfNN87Dfpe0klGcS8481Ew0OH5VGhQq0GY3Z/\r\nivJQGgoon9h303780yakjZFAIuah2TvjMntYFcZKl01Jhwk9gZDkzeQh5Wal\r\nREY6slc49s88BuHnVTcjXg7PcsWuCQFStB0W25aO8PUzxr7myIbNL9FKigg8\r\n0PMEGXgwZSkuUiNS7hyvsgBgVSeU5weUjMiwSTE3aemtQKzkA6z/6d8DHGvc\r\nzuBbtaGeqemtQt72HWdJ5IDHTwaLMVQaoPe0RSgR3+3gGfnHNn1PZslrHF4f\r\noN+yXd3lNRcXE9r9JTfeGnWdh4YTZaVlFJerr2zy9vM1lZlwOxETGiftiu1D\r\nYi4BJWC3AOa+eQdf6Y2fqHqFZMlUuYkIJD6oqFTCKiLow1o2YMteF2psos+8\r\nsfxWy6QAgXURUYw7ylD7JczAEeoaY/yDfsOaUpcmNBjDgg22j76J9TNXnT7H\r\nsFBd4ZjfCSBzv+LKbhcXeouFTDqUMAJwWrbmhCYu74jlb3ukWn0ZvXxFgMUj\r\ncpY4+ifb6KxGbHUTaxz8A24GH1Rqt3GHK4YjBK6bONHshjGLXSIOBDupoyFK\r\neHFZ6mc9FvAglAaVQh3ipYRXxYaJBoTPK7ldVBUSFsjqCu2fUQYxO6FyVoRn\r\nIxCQVFWPW8SzQVQxd8DaCh3CA8I532BZlp4=\r\n=POEj\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDhuUi15sjTMnc6bU0daD1DshG73ldU5cCShYAjbjoxgQIgbcHWh5YIIFgqHNjaKSl54zBkB7b5P+4O1JEsV3Oq8Yc="}]}, "_npmUser": {"name": "caub", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompt_1.2.2_1645119403467_0.8958750795875225"}, "_hasShrinkwrap": false}, "1.3.0": {"name": "prompt", "version": "1.3.0", "description": "A beautiful command-line prompt for node.js", "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "maintainers": [{"name": "fedor.indutny", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "caub", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/flatiron/prompt.git"}, "keywords": ["prompt", "command-line", "customize", "validation"], "dependencies": {"@colors/colors": "1.5.0", "async": "3.2.3", "read": "1.0.x", "revalidator": "0.1.x", "winston": "2.x"}, "devDependencies": {"eslint": "^7.32.0", "vows": "^0.7.0"}, "main": "./lib/prompt", "scripts": {"test": "vows test/prompt-test.js --spec", "test-all": "vows --spec"}, "license": "MIT", "engines": {"node": ">= 6.0.0"}, "gitHead": "caa988ee15ec6cfbf61e3e8fccae5f4c0e640e99", "bugs": {"url": "https://github.com/flatiron/prompt/issues"}, "homepage": "https://github.com/flatiron/prompt#readme", "_id": "prompt@1.3.0", "_nodeVersion": "16.14.2", "_npmVersion": "8.5.0", "dist": {"integrity": "sha512-ZkaRWtaLBZl7KKAKndKYUL8WqNT+cQHKRZnT4RYYms48jQkFw3rrBL+/N5K/KtdEveHkxs982MX2BkDKub2ZMg==", "shasum": "b1f6d47cb1b6beed4f0660b470f5d3ec157ad7ce", "tarball": "https://registry.npmjs.org/prompt/-/prompt-1.3.0.tgz", "fileCount": 30, "unpackedSize": 137036, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDeAKAy93POpAeWIkC4oWJHw5cq3flN9hboXSSZOBfcAAiEA9WlWQ81xcI4NogQiDbtOuZAeL7gSKXo2Z4/yGWgVyfs="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVEW0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoCQhAAlACm+NkAxG/DPKZl20pnJaM5Cy6SfT9Dfq32J2MxP6J4Z2VB\r\n3uz+ib2Bw7aeh1rShSQeKppt7uoQcmS/yeeHtpc7b/SDDmusfsv3YFBIuA/N\r\nTw+KCLBSeuT+zEi4uTd/+nlhneluU4/8G1Bcr9Ve3sQB6Fga6Lri1r2ZAEee\r\nHcbHAgvZpl4rAFIBfqKNS+ZBXOdYxFBMotxAHAmNb58snNMbkF8zHUsRNa1b\r\neiqRyokYzJSbpi2oxm11kFBDdLVg90i35CVJdDwqaF6S1wPQc/wZTxWaNVgo\r\ncXMpIEG01LcI396/Pitu/g207w0P8CVvnUwkUij6HeEccpuFYQ1/UfI3ynui\r\nI26Kb9oQA8YNhpUldluuegY1MKTY3HznUnI2soFDRF98sBN1ObYO8rDKuQ2D\r\n+SfnC+Amb6rX1QUby9kfbwTKlDt0GYt3oubpE7F+Zjhl0nKc5LQJlzjWAmqA\r\nN4S4+lfy4EM7JfG7OiD0XqtM7WWL/MapJN/ZF84GJgUnMc6NJVONG848I4wK\r\nRfKbotWVI1ggGOmXwvhPYIC4SkSQ0a8fyzYb/WIQQpd1jPB3B4Sgv7orLtsF\r\nYvqdzFfXIowZKYMzEWg6zIFBECjV2KwAYLokyaa66/eh23m4ATCSVDmkCLGb\r\nSjT+IigaGNVXNYIdPki/ya6u1evp6hg/thk=\r\n=bmSi\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "caub", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompt_1.3.0_1649690036501_0.160031304379193"}, "_hasShrinkwrap": false}}, "maintainers": [{"name": "fedor.indutny", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "bradleymeck", "email": "<EMAIL>"}, {"name": "caub", "email": "<EMAIL>"}], "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/flatiron/prompt.git"}, "time": {"modified": "2023-05-27T02:27:20.742Z", "created": "2011-03-18T15:06:40.016Z", "0.0.1": "2011-03-18T15:06:40.016Z", "0.0.2": "2011-03-18T15:06:40.016Z", "0.0.3": "2011-03-18T15:06:40.016Z", "0.0.4": "2011-05-12T20:17:03.854Z", "0.1.0": "2011-05-30T06:07:01.561Z", "0.1.1": "2011-06-08T03:18:31.595Z", "0.1.2": "2011-06-22T04:44:10.998Z", "0.1.3": "2011-06-29T18:26:45.199Z", "0.1.4": "2011-08-09T01:55:30.907Z", "0.1.5": "2011-08-10T03:47:55.099Z", "0.1.7": "2011-08-22T00:35:01.001Z", "0.1.8": "2011-09-12T17:46:31.781Z", "0.1.9": "2011-09-26T00:06:28.170Z", "0.1.10": "2011-10-25T19:50:43.191Z", "0.1.11": "2011-12-05T23:19:19.802Z", "0.1.12": "2012-01-04T05:04:02.889Z", "0.2.0": "2012-06-16T05:30:30.310Z", "0.2.1": "2012-06-16T15:38:43.289Z", "0.2.2": "2012-07-08T08:57:48.776Z", "0.2.3": "2012-07-27T06:07:26.623Z", "0.2.4": "2012-08-02T21:07:05.907Z", "0.2.5": "2012-08-12T00:49:28.589Z", "0.2.6": "2012-08-12T16:31:54.285Z", "0.2.7": "2012-08-30T19:45:32.655Z", "0.2.8": "2012-10-21T23:23:02.750Z", "0.2.9": "2012-12-19T06:01:40.279Z", "0.2.10": "2013-07-02T23:31:38.018Z", "0.2.11": "2013-07-02T23:55:26.014Z", "0.2.12": "2013-12-09T14:19:37.876Z", "0.2.13": "2014-05-25T21:41:35.731Z", "0.2.14": "2014-09-15T21:10:27.854Z", "0.3.0": "2016-02-07T22:01:23.256Z", "1.0.0": "2016-02-10T08:43:10.147Z", "1.1.0": "2020-12-21T17:28:09.763Z", "1.2.0": "2021-08-25T16:59:52.435Z", "1.2.1": "2022-01-10T11:13:59.717Z", "1.2.2": "2022-02-17T17:36:43.670Z", "1.3.0": "2022-04-11T15:13:56.650Z"}, "users": {"blakmatrix": true, "fgribreau": true, "leesei": true, "zeke": true, "spekkionu": true, "themiddleman": true, "morishitter": true, "jimnox": true, "pixel67": true, "nikunjchapadia": true, "kahboom": true, "omrilotan": true, "mr.raindrop": true, "louxiaojian": true, "tapsboy": true, "alfonsovinti": true, "coachshea": true, "fill": true, "codeshrew": true, "manolodd": true, "womjoy": true, "vrfrnco": true, "robbschiller": true, "tcauduro": true, "ab": true, "jordanskole": true, "jprempeh": true, "pnevares": true, "hellstad": true, "datawhore": true, "blackoperat": true, "mrmartineau": true, "chrisbernal": true, "jasoncmcg": true, "hatelove": true, "titouandk": true, "jesusgoku": true, "gyaresu": true, "phoenix-xsy": true, "dyedgreen": true, "davidchase": true, "moimikey": true, "danielaron": true, "jmjanzen": true, "amazonov": true, "tianyk": true, "shriek": true, "eneko89": true, "jfmercer": true, "kontrax": true, "demoive": true, "davequick": true, "maxime1992": true, "gamr": true, "schnittstabil": true, "hal9zillion": true, "papiro": true, "nyx": true, "antanst": true, "stany": true, "larnera": true, "tatumcreative": true, "guananddu": true, "muxa": true, "bojand": true, "saravananr": true, "jbob": true, "rbartoli": true, "vamakoda": true, "entropy-lion": true, "rojo2": true, "benjaminaaron": true, "philipphoh": true, "alphatr": true, "monjer": true, "dbaran": true, "geosmina": true, "rbecheras": true, "abhisekp": true, "52u": true, "elviopita": true, "rocksynth": true, "huina.gu": true, "xinwangwang": true, "ddkothari": true, "lestad": true, "ferrari": true, "eshaanmathur": true, "bigdoods": true, "nogirev": true, "samersm": true, "rocket0191": true, "razr9": true, "i-erokhin": true, "aliorouji": true, "goatandsheep": true, "galenandrew": true, "szymex73": true, "ariadiprana": true, "wgerven": true, "emyann": true, "qddegtya": true, "ahvonenj": true, "abdul": true, "olonam": true, "vinbhatt": true, "santi8ago8": true, "nohomey": true, "guzgarcia": true, "zguillez": true, "iuykza": true, "shuoshubao": true, "ga1989": true, "giussa_dan": true, "jpfilevich": true, "ackerapple": true, "chinjon": true, "sean-oneal": true, "xiongwilee": true, "nickchow": true, "danielknaust": true, "jmsherry": true, "igasho": true, "papasavva": true, "rpnna": true, "zhaojunbest": true, "alanerzhao": true, "millercl": true, "pddivine": true, "axelrindle": true, "heartnett": true, "zousandian": true, "shanewholloway": true, "mykhpl": true, "ungurys": true, "imhu91": true, "pacoelayudante": true, "usex": true, "madsummer": true, "dm7": true, "thomasleveil": true, "neaker15668": true, "netoperatorwibby": true, "soenkekluth": true, "71emj1": true, "isayme": true, "darrentorpey": true, "joe223": true, "bouchezb": true, "jream": true, "wolfram77": true, "memoramirez": true, "ajwarreniii": true, "rafaesc92": true, "noita": true, "flumpus-dev": true}, "readme": "# prompt [![Build Status](https://secure.travis-ci.org/flatiron/prompt.svg)](http://travis-ci.org/flatiron/prompt) [![Npm package version](https://img.shields.io/npm/v/prompt.svg?maxAge=2592000)](https://npmjs.com/package/prompt)\n\n\nA beautiful command-line prompt for node.js\n\n## Features\n\n* prompts the user for input\n* supports validation and defaults\n* hides passwords\n\n## Usage\nUsing prompt is relatively straight forward. There are two core methods you should be aware of: `prompt.get()` and `prompt.addProperties()`. Their methods take strings representing property names in addition to objects for complex property validation (and more). There are a number of [examples][0] that you should examine for detailed usage.\n\n### Getting Basic Prompt Information\nGetting started with `prompt` is easy. Lets take a look at `examples/simple-prompt.js`:\n\n``` js\n  var prompt = require('prompt');\n\n  //\n  // Start the prompt\n  //\n  prompt.start();\n\n  //\n  // Get two properties from the user: username and email\n  //\n  prompt.get(['username', 'email'], function (err, result) {\n    //\n    // Log the results.\n    //\n    console.log('Command-line input received:');\n    console.log('  username: ' + result.username);\n    console.log('  email: ' + result.email);\n  });\n```\n\nThis will result in the following command-line output:\n\n```\n  $ node examples/simple-prompt.js\n  prompt: username: some-user\n  prompt: email: <EMAIL>\n  Command-line input received:\n    username: some-user\n    email: <EMAIL>\n```\n\nIf no callback is passed to `prompt.get(schema)`, then it returns a `Promise`, so you can also write:\n```js\nconst {username, email} = await prompt.get(['username', 'email']);\n```\n\n\n### Prompting with Validation, Default Values, and More (Complex Properties)\nIn addition to prompting the user with simple string prompts, there is a robust API for getting and validating complex information from a command-line prompt. Here's a quick sample:\n\n``` js\n  var schema = {\n    properties: {\n      name: {\n        pattern: /^[a-zA-Z\\s\\-]+$/,\n        message: 'Name must be only letters, spaces, or dashes',\n        required: true\n      },\n      password: {\n        hidden: true\n      }\n    }\n  };\n\n  //\n  // Start the prompt\n  //\n  prompt.start();\n\n  //\n  // Get two properties from the user: name, password\n  //\n  prompt.get(schema, function (err, result) {\n    //\n    // Log the results.\n    //\n    console.log('Command-line input received:');\n    console.log('  name: ' + result.name);\n    console.log('  password: ' + result.password);\n  });\n```\n\nPretty easy right? The output from the above script is:\n\n```\n  $ node examples/property-prompt.js\n  prompt: name: nodejitsu000\n  error:  Invalid input for name\n  error:  Name must be only letters, spaces, or dashes\n  prompt: name: Nodejitsu Inc\n  prompt: password:\n  Command-line input received:\n    name: Nodejitsu Inc\n    password: some-password\n```\n\n## Valid Property Settings\n`prompt` understands JSON-schema with a few extra parameters and uses [revalidator](https://github.com/flatiron/revalidator) for validation.\n\nHere's an overview of the properties that may be used for validation and prompting controls:\n\n``` js\n  {\n    description: 'Enter your password',     // Prompt displayed to the user. If not supplied name will be used.\n    type: 'string',                 // Specify the type of input to expect.\n    pattern: /^\\w+$/,                  // Regular expression that input must be valid against.\n    message: 'Password must be letters', // Warning message to display if validation fails.\n    hidden: true,                        // If true, characters entered will either not be output to console or will be outputed using the `replace` string.\n    replace: '*',                        // If `hidden` is set it will replace each hidden character with the specified string.\n    default: 'lamepassword',             // Default value to use if no value is entered.\n    required: true                        // If true, value entered must be non-empty.\n    before: function(value) { return 'v' + value; } // Runs before node-prompt callbacks. It modifies user's input\n  }\n```\n\nAlternatives to `pattern` include `format` and `conform`, as documented in [revalidator](https://github.com/flatiron/revalidator).\n\nSupported types are `string`, `boolean`, `number`, `integer`, `array`\n\nUsing `type: 'boolean'` accepts case insensitive values 'true', 't', 'false', 'f'\n\nUsing `type: 'array'` has some special cases.\n\n- `description` will not work in the schema if `type: 'array'` is defined.\n- `maxItems` takes precedence over `minItems`.\n- Arrays that do not have `maxItems` defined will require users to `SIGINT` (`^C`) before the array is ended.\n- If `SIGINT` (`^C`) is triggered before `minItems` is met, a validation error will appear. This will require users to `SIGEOF` (`^D`) to end the input.\n\nFor more information on things such as `maxItems` and `minItems`, refer to the [revalidator](https://github.com/flatiron/revalidator) repository.\n\n### Alternate Validation API:\n\nPrompt, in addition to iterating over JSON-Schema properties, will also happily iterate over an array of validation objects given an extra 'name' property:\n\n```js\n  var prompt = require('../lib/prompt');\n\n  //\n  // Start the prompt\n  //\n  prompt.start();\n\n  //\n  // Get two properties from the user: username and password\n  //\n  prompt.get([{\n      name: 'username',\n      required: true\n    }, {\n      name: 'password',\n      hidden: true,\n      conform: function (value) {\n        return true;\n      }\n    }], function (err, result) {\n    //\n    // Log the results.\n    //\n    console.log('Command-line input received:');\n    console.log('  username: ' + result.username);\n    console.log('  password: ' + result.password);\n  });\n```\n\n### Backward Compatibility\n\nNote that, while this structure is similar to that used by prompt 0.1.x, that the object properties use the same names as in JSON-Schema. prompt 0.2.x is backward compatible with prompt 0.1.x except for asynchronous validation.\n\n### Skipping Prompts\n\nSometimes power users may wish to skip prompts and specify all data as command line options.\nif a value is set as a property of `prompt.override` prompt will use that instead of\nprompting the user.\n\n``` js\n  //prompt-override.js\n\n  var prompt = require('prompt'),\n      optimist = require('optimist')\n\n  //\n  // set the overrides\n  //\n  prompt.override = optimist.argv\n\n  //\n  // Start the prompt\n  //\n  prompt.start();\n\n  //\n  // Get two properties from the user: username and email\n  //\n  prompt.get(['username', 'email'], function (err, result) {\n    //\n    // Log the results.\n    //\n    console.log('Command-line input received:');\n    console.log('  username: ' + result.username);\n    console.log('  email: ' + result.email);\n  })\n\n  //: node prompt-override.js --username USER --email EMAIL\n```\n\nIt is also possible to skip prompts dynamically based on previous prompts.\nIf an `ask` method is added, prompt will use it to determine if the prompt should be displayed.\nIf `ask` returns true the prompt is displayed. otherwise, the default value or empty string are used.\n\n``` js\n  var schema = {\n    properties: {\n      proxy: {\n        description: 'Proxy url',\n      },\n      proxyCredentials: {\n        description: 'Proxy credentials',\n        ask: function() {\n          // only ask for proxy credentials if a proxy was set\n          return prompt.history('proxy').value > 0;\n        }\n      }\n    }\n  };\n\n  //\n  // Start the prompt\n  //\n  prompt.start();\n\n  //\n  // Get one or two properties from the user, depending on\n  // what the user answered for proxy\n  //\n  prompt.get(schema, function (err, result) {\n    //\n    // Log the results.\n    //\n    console.log('Command-line input received:');\n    console.log('  proxy: ' + result.proxy);\n    console.log('  credentials: ' + result.proxyCredentials);\n  });\n```\n\n\n### Adding Properties to an Object\nA common use-case for prompting users for data from the command-line is to extend or create a configuration object that is passed onto the entry-point method for your CLI tool. `prompt` exposes a convenience method for doing just this:\n\n``` js\n  var obj = {\n    password: 'lamepassword',\n    mindset: 'NY'\n  }\n\n  //\n  // Log the initial object.\n  //\n  console.log('Initial object to be extended:');\n  console.dir(obj);\n\n  //\n  // Add two properties to the empty object: username and email\n  //\n  prompt.addProperties(obj, ['username', 'email'], function (err) {\n    //\n    // Log the results.\n    //\n    console.log('Updated object received:');\n    console.dir(obj);\n  });\n```\n\n### Prompt history\nYou can use the `prompt.history()` method to get access to previous prompt input.\n\n``` js\n  prompt.get([{\n    name: 'name',\n    description: 'Your name',\n    type: 'string',\n    required: true\n  }, {\n    name: 'surname',\n    description: 'Your surname',\n    type: 'string',\n    required: true,\n    message: 'Please dont use the demo credentials',\n    conform: function(surname) {\n      var name = prompt.history('name').value;\n      return (name !== 'John' || surname !== 'Smith');\n    }\n  }], function(err, results) {\n    console.log(results);\n  });\n```\n\n## Customizing your prompt\nAside from changing `property.message`, you can also change `prompt.message`\nand `prompt.delimiter` to change the appearance of your prompt.\n\nThe basic structure of a prompt is this:\n\n``` js\nprompt.message + prompt.delimiter + property.message + prompt.delimiter;\n```\n\nThe default `prompt.message` is \"prompt,\" the default `prompt.delimiter` is\n\": \", and the default `property.message` is `property.name`.\nChanging these allows you to customize the appearance of your prompts! In\naddition, prompt supports ANSI color codes via the\n[colors module](https://github.com/DABH/colors.js) for custom colors. For a\nvery colorful example:\n\n``` js\n  var prompt = require(\"prompt\");\n  var colors = require(\"@colors/colors/safe\");\n  //\n  // Setting these properties customizes the prompt.\n  //\n  prompt.message = colors.rainbow(\"Question!\");\n  prompt.delimiter = colors.green(\"><\");\n\n  prompt.start();\n\n  prompt.get({\n    properties: {\n      name: {\n        description: colors.magenta(\"What is your name?\")\n      }\n    }\n  }, function (err, result) {\n    console.log(colors.cyan(\"You said your name is: \" + result.name));\n  });\n```\n\nIf you don't want colors, you can set\n\n```js\nvar prompt = require('prompt');\n\nprompt.colors = false;\n```\n\n## Integration with streamlinejs\n\nWhen integrating prompt with projects using streamlinejs such as the following\n\n```\nprompt.start();\nfunction test_prompt(_){\n    console.log(prompt.get(loadDataValues(), _).output);\n}\ntest_prompt(_);\n```\n\nThis will work, however the process is then stuck with a stdin stream still open. If you setup the traditional way (with callback) such as this\n\n ```\nprompt.start();\nfunction test_prompt(){\n    prompt.get(loadDataValues(), function(err, data){\n        console.log(data.output);\n    });\n}\ntest_prompt();\n```\nThis works and ends correctly.\n\nTo resolve this we have added a new method to prompt, which will stop the stdin stream\n\n```\n//\n// ### function stop ()\n// Stops input coming in from stdin\n//\nprompt.stop = function () {\n    if (prompt.stopped || !prompt.started) {\n        return;\n    }\n\n    stdin.destroy();\n    prompt.emit('stop');\n    prompt.stopped = true;\n    prompt.started = false;\n    prompt.paused = false;\n    return prompt;\n}\n```\n\nAnd you can find an example in the example folder `examples/prompt-streamline.js`\n\n```\n/*\n * prompt-streamline._js: Example of how to use prompt with streamlinejs.\n *\n * calling syntax: _node prompt-streamline._js\n *\n */\nvar prompt = require('../lib/prompt');\n\nfunction getSampleData(){\n    return [\n        {\n            name: 'username',\n            message: 'Enter a username'\n        }\n    ];\n};\n\n//\n// Start the prompt\n//\nprompt.start();\n\nfunction get_username_prompt(_){\n    console.log(prompt.get(getSampleData(), _).username);\n}\n\nget_username_prompt(_);\n\n//\n// Clean the prompt\n//\nprompt.stop();\n```\n\n## Disabling prompt's built-in SIGINT handling\n\nBy default, prompt prompt binds a process-killing event handler to the SIGINT event (CTRL+C). This allows easily exiting from prompts, but can prevent an app from executing other event handlers when an interrupt is received. In order to override this default behavior, pass a `{noHandleSIGINT: true}` option into `prompt.start`.\n\n``` js\n  //\n  // Disable prompt's built-in SIGINT handling:\n  //\n  prompt.start({noHandleSIGINT: true});\n  \n  process.on('SIGINT', function() {\n    console.log(\"This will execute when you hit CTRL+C\");\n    process.exit();\n  });\n```\n\n\n## Installation\n\n``` bash\n  $ [sudo] npm install prompt\n```\n\n## Running tests\n\n``` bash\n  $ npm test\n```\n\n#### License: MIT\n#### Author: [Charlie Robbins](http://github.com/indexzero)\n#### Contributors: [Josh Holbrook](http://github.com/jesusabdullah), [Pavan Kumar Sunkara](http://github.com/pksunkara)\n\n[0]: https://github.com/flatiron/prompt/tree/master/examples\n", "readmeFilename": "README.md", "homepage": "https://github.com/flatiron/prompt#readme", "bugs": {"url": "https://github.com/flatiron/prompt/issues"}, "license": "MIT", "keywords": ["prompt", "command-line", "customize", "validation"]}