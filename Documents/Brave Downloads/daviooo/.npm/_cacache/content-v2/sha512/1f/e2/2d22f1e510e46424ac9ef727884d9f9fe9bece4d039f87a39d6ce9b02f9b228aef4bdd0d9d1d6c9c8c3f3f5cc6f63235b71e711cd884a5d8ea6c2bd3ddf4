{"source": 1091728, "name": "safe-eval", "dependency": "safe-eval", "title": "safe-eval vulnerable to Prototype Pollution via the safeEval function", "url": "https://github.com/advisories/GHSA-hcg3-56jf-x4vh", "severity": "critical", "versions": ["0.0.0", "0.1.0", "0.2.0", "0.3.0", "0.4.0", "0.4.1"], "vulnerableVersions": ["0.0.0", "0.1.0", "0.2.0", "0.3.0", "0.4.0", "0.4.1"], "cwe": ["CWE-1321"], "cvss": {"score": 10, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:H"}, "range": "<=0.4.2", "id": "eSDYK2xWW/J75kZ4N7scmBfwE5SXWIl4cIftRuILwYN5YUW9G09xdxppz5xvyDCT0Q51YMypbOCzbAHqsg30bQ=="}