{"_id": "winston", "_rev": "772-9c8cd746b25e5837c20a866810db162f", "name": "winston", "dist-tags": {"2.x-latest": "2.4.7", "3.x-latest": "3.9.0", "next": "3.9.0", "latest": "3.17.0"}, "versions": {"0.2.11": {"name": "winston", "version": "0.2.11", "keywords": ["logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "winston@0.2.11", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "596c131ac552a2194b37ba622ded9f887131599c", "tarball": "https://registry.npmjs.org/winston/-/winston-0.2.11.tgz", "integrity": "sha512-3VgIqZ0UyRWIMxC5wRLmKZVt/Z5xBtHvip22Mvwl9c4NVgDXcQwwKKnevFKnpOtZ9ABelrigvxrhbUiLtie/7A==", "signatures": [{"sig": "MEQCIEAcXvZjj0r0CavxDFoa9Cx9FbN7kpjk5G1eaTaDF15MAiBzt8QDA0numrhL8ubsziSSlPUjmxshiBb6+19at/dSig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "engines": {"node": ">= 0.4.0"}, "scripts": {"test": "vows test/*-test.js --spec"}, "repository": {"url": "git://github.com/indexzero/winston.git", "type": "git"}, "_npmVersion": "1.0.6", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "v0.4.8", "dependencies": {"eyes": "0.1.x", "colors": "0.x.x", "loggly": "0.3.x", "mongodb": "0.9.x", "riak-js": ">= 0.4.0rc2"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x"}, "_engineSupported": true}, "0.3.3": {"name": "winston", "version": "0.3.3", "keywords": ["logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "winston@0.3.3", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Marak Squires", "email": "<EMAIL>"}], "dist": {"shasum": "e4b38e7117e5cc91dc632a2338efbbbdcdf72034", "tarball": "https://registry.npmjs.org/winston/-/winston-0.3.3.tgz", "integrity": "sha512-cwJZ/rA7QTpyp8iSjRsh5Lq1uiqq6wGcRgLOiTQKCc3AvAKK5HEL/yU42ZAwEI8c9iqkvj/52H4kRIEk5r90Jw==", "signatures": [{"sig": "MEYCIQC6/CeoRQ0PncRbgeXazJPQTk7Oh04Oivln1JtDmPmonwIhAK5/Ajmc8QAeYMij6FKX8lXZUWioRnQ9cGf1bnDBDjPU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "engines": {"node": ">= 0.4.0"}, "scripts": {"test": "vows test/*-test.js --spec"}, "repository": {"url": "git://github.com/indexzero/winston.git", "type": "git"}, "_npmVersion": "1.0.18", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "v0.4.10", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/winston/0.3.3/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"eyes": "0.1.x", "colors": "0.x.x", "loggly": "0.3.x", "pkginfo": "0.2.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x"}, "_engineSupported": true}, "0.3.4": {"name": "winston", "version": "0.3.4", "keywords": ["logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "winston@0.3.4", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Marak Squires", "email": "<EMAIL>"}], "dist": {"shasum": "bade1b45c944731970d8df9fb1d1927b0fbcf697", "tarball": "https://registry.npmjs.org/winston/-/winston-0.3.4.tgz", "integrity": "sha512-BMjuzleceo/OJETycArsqbyqusbjs4WrIOYIwRbE/AmD9lRkFNFXD+GLg7KhgS3KZrHMuYqTG7CynitLIClWuQ==", "signatures": [{"sig": "MEYCIQCcVOW/6cwVMA+as1a2M+YbWl741wQbFw19A9g7SeFM7gIhANRgFb/fS8JsbH+Eh0UgX/jsQxNDYxKVy/ABAt7RNqbt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "engines": {"node": ">= 0.4.0"}, "scripts": {"test": "vows test/*-test.js --spec"}, "repository": {"url": "git://github.com/indexzero/winston.git", "type": "git"}, "_npmVersion": "1.0.22", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "v0.4.10", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/winston/0.3.4/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"eyes": "0.1.x", "colors": "0.x.x", "loggly": "0.3.x", "pkginfo": "0.2.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x"}, "_engineSupported": true}, "0.3.5": {"name": "winston", "version": "0.3.5", "keywords": ["logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "winston@0.3.5", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Marak Squires", "email": "<EMAIL>"}], "dist": {"shasum": "04d3fc80b7049872c065ee8a912c0fd3f5199668", "tarball": "https://registry.npmjs.org/winston/-/winston-0.3.5.tgz", "integrity": "sha512-6OYQJfNfBnWqvhkzmJJF1y5d8vXK1MHPHjKfXNZqLFBDNAZ5VQtQcWZZauah0usNA4ZO25kGzxWsjsAtLt5+CQ==", "signatures": [{"sig": "MEUCICqqnR4qCKR7VC4Lli9i2E3wuEcEPCAvzmIAhD7XVrbKAiEAr28ass1uOvzJC8zbuo1o5pQ5uBXUBHILNxuWELWGf5k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "engines": {"node": ">= 0.4.0"}, "scripts": {"test": "vows test/*-test.js --spec"}, "repository": {"url": "git://github.com/indexzero/winston.git", "type": "git"}, "_npmVersion": "1.0.23", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "v0.4.10", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/winston/0.3.5/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"eyes": "0.1.x", "colors": "0.x.x", "loggly": "0.3.x", "pkginfo": "0.2.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x"}, "_engineSupported": true}, "0.4.0": {"name": "winston", "version": "0.4.0", "keywords": ["logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "winston@0.4.0", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Marak Squires", "email": "<EMAIL>"}], "dist": {"shasum": "c66bffffbb94b0c5d5173871fc26aa485be01a28", "tarball": "https://registry.npmjs.org/winston/-/winston-0.4.0.tgz", "integrity": "sha512-PzmPycSI01GgxeRuWH68vZo554x7f3vdqqAOLW2zqCeQ0FIFgjB947PMEQrqHRbX7lzl2z9pElxCQ+6Nwk3vnw==", "signatures": [{"sig": "MEUCIEuy2XJqvQLVgjqwtFWfvd5QNTdobp6pDrLdDQ/LpBZ6AiEAt7Y98k3WCVA9HspQSOisXmDN1CkyLh0HWZiiqy+CJqY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "engines": {"node": ">= 0.4.0"}, "scripts": {"test": "vows test/*-test.js --spec"}, "repository": {"url": "git://github.com/indexzero/winston.git", "type": "git"}, "_npmVersion": "1.0.23", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "v0.4.10", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/winston/0.4.0/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"eyes": "0.1.x", "async": "0.1.x", "colors": "0.x.x", "loggly": "0.3.x >=0.3.7", "pkginfo": "0.2.x", "stack-trace": "0.0.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x >=0.5.11"}, "_engineSupported": true}, "0.4.1": {"name": "winston", "version": "0.4.1", "keywords": ["logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "winston@0.4.1", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Marak Squires", "email": "<EMAIL>"}], "dist": {"shasum": "ed47572fc787a266d4c65fcf14151e0298907b95", "tarball": "https://registry.npmjs.org/winston/-/winston-0.4.1.tgz", "integrity": "sha512-9LSVDAAIrmSmKNisZ++4DtvKw2szvVDJq1gcJrsMm2rmovwzc0o3wFSEcP89DlpXFQeeVSepUWlXwASbiC4mHQ==", "signatures": [{"sig": "MEUCIA0PSh6Zcivn1NS/1Z1O+H0JZDuiJcOCSjqRFY3RL2E4AiEAvfEX9M9wmrejdZG5VpM/jzeETtUIeEAJ5o5+vq14KZo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "engines": {"node": ">= 0.4.0"}, "scripts": {"test": "vows test/*-test.js --spec --isolate"}, "repository": {"url": "git://github.com/indexzero/winston.git", "type": "git"}, "_npmVersion": "1.0.23", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "v0.4.11", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/winston/0.4.1/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"eyes": "0.1.x", "async": "0.1.x", "colors": "0.x.x", "loggly": "0.3.x >=0.3.7", "pkginfo": "0.2.x", "stack-trace": "0.0.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x >=0.5.11"}, "_engineSupported": true}, "0.5.0": {"name": "winston", "version": "0.5.0", "keywords": ["logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "winston@0.5.0", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Marak Squires", "email": "<EMAIL>"}], "dist": {"shasum": "38269506eaf446ecb8808295ea1d3da05010ab82", "tarball": "https://registry.npmjs.org/winston/-/winston-0.5.0.tgz", "integrity": "sha512-6yA819NEwMcPcNl7pvDpD35uuCgN8hEzo2XFY6zgntk7SKXWLMdlGvna8YTZ7AywUdEAvtEpoc8IRojeSaGbFg==", "signatures": [{"sig": "MEUCIA29S1NRHK35GCo/uEBgKxYx8V4RWlKujN8O9JJ8M49TAiEA/e9lY9stbaQSFYloRk+otPLFKHn0+jJ0G4ZEOgRdDT0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "engines": {"node": ">= 0.4.0"}, "scripts": {"test": "vows --spec --isolate"}, "repository": {"url": "git://github.com/indexzero/winston.git", "type": "git"}, "_npmVersion": "1.0.23", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "v0.4.11", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/winston/0.5.0/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"eyes": "0.1.x", "async": "0.1.x", "colors": "0.x.x", "loggly": "0.3.x >=0.3.7", "pkginfo": "0.2.x", "stack-trace": "0.0.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x >=0.5.11"}, "_engineSupported": true}, "0.5.1": {"name": "winston", "version": "0.5.1", "keywords": ["logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "winston@0.5.1", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Marak Squires", "email": "<EMAIL>"}], "dist": {"shasum": "2b2f40cdbf469b60f1885cc549757c4b9973e5ea", "tarball": "https://registry.npmjs.org/winston/-/winston-0.5.1.tgz", "integrity": "sha512-V6m7gbUe7ECL/dRGgFL6DFSdLrkbbJ33adEghboueYq2B17XmvJW6xBYyLEOxWH5aKT2MnVSOkOiXEU+l4vZRA==", "signatures": [{"sig": "MEUCIQDI6xKWSGu4GUEQiJEStWzE1F9IY+y5kx7vVsPcqrmnLAIgH/KJmAYpK/Vlz1fyhCDcUA8UWF3NnlPgrdZtH9nuBtA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "engines": {"node": ">= 0.4.0"}, "scripts": {"test": "vows --spec --isolate"}, "repository": {"url": "git://github.com/indexzero/winston.git", "type": "git"}, "_npmVersion": "1.0.23", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "v0.4.11", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/winston/0.5.1/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"eyes": "0.1.x", "async": "0.1.x", "colors": "0.x.x", "loggly": "0.3.x >=0.3.7", "pkginfo": "0.2.x", "stack-trace": "0.0.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x >=0.5.11"}, "_engineSupported": true}, "0.5.2": {"name": "winston", "version": "0.5.2", "keywords": ["logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "winston@0.5.2", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Marak Squires", "email": "<EMAIL>"}], "dist": {"shasum": "5adcbf0a8a872e3b3584a11b1173765329b00765", "tarball": "https://registry.npmjs.org/winston/-/winston-0.5.2.tgz", "integrity": "sha512-H/o0ky+LwKBE0s7icyYTdInRMEe0R0UKDq/xNYk2l+RQsUVJWmC0hUuVylfbz8KsIDt3DvbtTN5tfgD69ELHqA==", "signatures": [{"sig": "MEUCIQCtf+K15g0jbu0ijTlLHz3jAhOGLFO/Oxtq/0H/0pryaQIgVF5SKkftaLJctXsgDSalTyTX43q86fSrbxd2bvz1J64=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "engines": {"node": ">= 0.4.0"}, "scripts": {"test": "vows --spec --isolate"}, "repository": {"url": "git://github.com/indexzero/winston.git", "type": "git"}, "_npmVersion": "1.0.23", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "v0.4.11", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/winston/0.5.2/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"eyes": "0.1.x", "async": "0.1.x", "colors": "0.x.x", "loggly": "0.3.x >=0.3.7", "pkginfo": "0.2.x", "stack-trace": "0.0.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x >=0.5.11"}, "_engineSupported": true}, "0.5.3": {"name": "winston", "version": "0.5.3", "keywords": ["logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "winston@0.5.3", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Marak Squires", "email": "<EMAIL>"}], "dist": {"shasum": "2b40e4eaacfe76b27e0c0436e626aa820da5990e", "tarball": "https://registry.npmjs.org/winston/-/winston-0.5.3.tgz", "integrity": "sha512-BcOHhxxUVtAFjoCgtmnrLxrQEWy7VYWCPpCYYbRDysr8ezGg0fKyEZg2ONk6GUCmOcEfMEa4ezbtlYwMxThBig==", "signatures": [{"sig": "MEUCIBQRcuXQj/LC4gfBjErgRbb/3rJrVSe01bAwHLbb8BCMAiEA0VxP1sBHhayTNFfB/Ohs34kgEkO2QsXI25hm2ApGuRA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "engines": {"node": ">= 0.4.0"}, "scripts": {"test": "vows --spec --isolate"}, "repository": {"url": "git://github.com/indexzero/winston.git", "type": "git"}, "_npmVersion": "1.0.23", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "v0.4.11", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/winston/0.5.3/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"eyes": "0.1.x", "async": "0.1.x", "colors": "0.x.x", "loggly": "0.3.x >=0.3.7", "pkginfo": "0.2.x", "stack-trace": "0.0.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x >=0.5.11"}, "_engineSupported": true}, "0.5.4": {"name": "winston", "version": "0.5.4", "keywords": ["logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "winston@0.5.4", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Marak Squires", "email": "<EMAIL>"}], "dist": {"shasum": "e6d5fbfe6549a8a3e39900ad68967bc74c1c1586", "tarball": "https://registry.npmjs.org/winston/-/winston-0.5.4.tgz", "integrity": "sha512-6cMMG/mg8IZ+3jFVjGzMIZdEaWT1JMRbfa5Dn+tC57h1l6LHX4xA//NkuCJOcrJnd4F0IpVEiN2xRDzDVrlQKA==", "signatures": [{"sig": "MEQCIDYuumeKa6Ld6XsXunwJW5cabp5Cm6OBeM7ffj7qldk0AiAIhSQ/P0SvriBbqh+qBISA3tTyYS4lQNhw+l6xcHog5A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "engines": {"node": ">= 0.4.0"}, "scripts": {"test": "vows --spec --isolate"}, "repository": {"url": "git://github.com/indexzero/winston.git", "type": "git"}, "_npmVersion": "1.0.23", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "v0.4.11", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/winston/0.5.4/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"eyes": "0.1.x", "async": "0.1.x", "colors": "0.x.x", "loggly": "0.3.x >=0.3.7", "pkginfo": "0.2.x", "stack-trace": "0.0.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x >=0.5.11"}, "_engineSupported": true}, "0.5.5": {"name": "winston", "version": "0.5.5", "keywords": ["logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "winston@0.5.5", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Marak Squires", "email": "<EMAIL>"}], "dist": {"shasum": "f4f859ea6d7cb38cb2f4496b4fa5ea12050e5c92", "tarball": "https://registry.npmjs.org/winston/-/winston-0.5.5.tgz", "integrity": "sha512-eESNGyY9eYBp2J9bJHSoFTlCY8zQgadV+bfnalEAc3ASUhYM/UbtejcWfrO7K5+w1L8Mfd+KfzJs8G+HwirubQ==", "signatures": [{"sig": "MEUCIFcEnyesS/ueYFeZtAVOMEe2/6xdTF4EkD7XkXmVPrK5AiEAsSmvEyHPWr2lvKtdM3oicUVLBxyAYiUPO86Y9oL0Myg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "engines": {"node": ">= 0.4.0"}, "scripts": {"test": "vows --spec --isolate"}, "repository": {"url": "git://github.com/indexzero/winston.git", "type": "git"}, "_npmVersion": "1.0.23", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "v0.4.11", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/winston/0.5.5/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"eyes": "0.1.x", "async": "0.1.x", "colors": "0.x.x", "loggly": "0.3.x >=0.3.7", "pkginfo": "0.2.x", "stack-trace": "0.0.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x >=0.5.11"}, "_engineSupported": true}, "0.5.6": {"name": "winston", "version": "0.5.6", "keywords": ["logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "winston@0.5.6", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Marak Squires", "email": "<EMAIL>"}], "dist": {"shasum": "0f4bae04ebe00c8bfd0a351797c3d2ce0416d033", "tarball": "https://registry.npmjs.org/winston/-/winston-0.5.6.tgz", "integrity": "sha512-Yh/TQ6pEqgmiZe3sMUpfEc30a8sc17i4era5PaFTnXgNsziamSUCNR3BnIsEqWfMP/Xnf8LmnUvC8KWn6CwkPA==", "signatures": [{"sig": "MEUCIQCxOW/nee/g7XeVyqM4kRBAm6V/wZLXsAvfKRJ/8z2GCAIgCOCBsRUkWkjVXx2GZxWmoR8xGdiJ4JNIT35tFJEdYGs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "engines": {"node": ">= 0.4.0"}, "scripts": {"test": "vows --spec --isolate"}, "repository": {"url": "git://github.com/indexzero/winston.git", "type": "git"}, "_npmVersion": "1.0.23", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "v0.4.11", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/winston/0.5.6/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"eyes": "0.1.x", "async": "0.1.x", "colors": "0.x.x", "loggly": "0.3.x >=0.3.7", "pkginfo": "0.2.x", "stack-trace": "0.0.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x >=0.5.11"}, "_engineSupported": true}, "0.5.7": {"name": "winston", "version": "0.5.7", "keywords": ["logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "winston@0.5.7", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Marak Squires", "email": "<EMAIL>"}], "dist": {"shasum": "50597e0e6860eef9f9716a709b2857aab817e51c", "tarball": "https://registry.npmjs.org/winston/-/winston-0.5.7.tgz", "integrity": "sha512-ZhmkVYdUkBKTBzmPxsWGNQexMnfExWZM4LZOviHh2gr4GUn6CQ2LYdgrzrVh5v48rkDMJK49rOLCHm0Y+YgmJw==", "signatures": [{"sig": "MEYCIQD2k0f3uUiwRlBLPUCyZDAD3WakBRwJLlIWIVTOkmDO3QIhAKvTNjc1BJkygmWsQhG0VNfL3bGb0AsHDu28pUm8+xIa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "engines": {"node": ">= 0.4.0"}, "scripts": {"test": "vows --spec --isolate"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/indexzero/winston.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {"eyes": "0.1.x", "async": "0.1.x", "colors": "0.x.x", "loggly": "0.3.x >=0.3.7", "pkginfo": "0.2.x", "stack-trace": "0.0.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x >=0.5.11"}, "_engineSupported": true}, "0.5.8": {"name": "winston", "version": "0.5.8", "keywords": ["logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "winston@0.5.8", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Marak Squires", "email": "<EMAIL>"}], "dist": {"shasum": "8a2f068613f25975d810d7f5dd45df141d67edb9", "tarball": "https://registry.npmjs.org/winston/-/winston-0.5.8.tgz", "integrity": "sha512-Y2y7xatLBR1HHjjLwM/qS/D1+vjrJTSd2ArJAPreIz4QtQev71QaBbkGwQfRKSVuIDYrwqD7eXRkblvx/P3Q1Q==", "signatures": [{"sig": "MEUCIQCNq2M2iz1VyU5r5AKfgr6QTzkcl6caFP8QWUUgnUIh1QIgTLEZYnfuy7P11h0MjsAni2brh+de+Laf9eJYkHtL7nM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "engines": {"node": ">= 0.4.0"}, "scripts": {"test": "vows --spec --isolate"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/indexzero/winston.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {"eyes": "0.1.x", "async": "0.1.x", "colors": "0.x.x", "loggly": "0.3.x >=0.3.7", "pkginfo": "0.2.x", "stack-trace": "0.0.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x >=0.5.11"}, "_engineSupported": true}, "0.5.9": {"name": "winston", "version": "0.5.9", "keywords": ["logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "winston@0.5.9", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Marak Squires", "email": "<EMAIL>"}], "dist": {"shasum": "3160f86b2298781599ef061bac742752e7cc3c3a", "tarball": "https://registry.npmjs.org/winston/-/winston-0.5.9.tgz", "integrity": "sha512-119PYc6LbdytuXFG8n3axcu+FgPrxYlNwtatPPNXhN98z1qyyCsTGFChFfVNVKRoQk2eOlFwzTfI+eVtFegQMw==", "signatures": [{"sig": "MEYCIQDo34d+zcSUa0lwU35BC7dZpKF/PZKoHE/aXM8OcJx4sAIhAOAGLRK4rd2CgXil3rlVSVrDyLyO5UIYCrLrieoB9SGw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "engines": {"node": ">= 0.4.0"}, "scripts": {"test": "vows --spec --isolate"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/indexzero/winston.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {"eyes": "0.1.x", "async": "0.1.x", "colors": "0.x.x", "loggly": "0.3.x >=0.3.7", "pkginfo": "0.2.x", "stack-trace": "0.0.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.5.x >=0.5.11"}, "_engineSupported": true}, "0.5.10": {"name": "winston", "version": "0.5.10", "keywords": ["logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "winston@0.5.10", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Marak Squires", "email": "<EMAIL>"}], "dist": {"shasum": "34167c79a07d2757f11407f766150acf97de9b70", "tarball": "https://registry.npmjs.org/winston/-/winston-0.5.10.tgz", "integrity": "sha512-jFs86FQE2LyP/hL5/8oUV3kjB52eXVtU/EkdHl6rEvHhE7Xp5LmGApRuwWKYHJ0BOdb+CxFw+bROG7gfHNKlmA==", "signatures": [{"sig": "MEUCIQCHvEQTJrpPXH70fCMrPT/6hdOWK9g0ZhM50NPJKjh3MQIgJ+yX3PKHf9RDBZMvhp2MOcN2TH6V6qf+QDG25V3nPJo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "engines": {"node": ">= 0.4.0"}, "scripts": {"test": "vows --spec --isolate"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/flatiron/winston.git", "type": "git"}, "_npmVersion": "1.1.1", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "v0.6.10", "dependencies": {"eyes": "0.1.x", "async": "0.1.x", "colors": "0.x.x", "loggly": "0.3.x >=0.3.7", "pkginfo": "0.2.x", "stack-trace": "0.0.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.6.x"}, "_engineSupported": true, "optionalDependencies": {}}, "0.5.11": {"name": "winston", "version": "0.5.11", "keywords": ["logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "winston@0.5.11", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "marak", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Marak Squires", "email": "<EMAIL>"}], "dist": {"shasum": "9d84ead981a497a92ddf76616137abef661c414f", "tarball": "https://registry.npmjs.org/winston/-/winston-0.5.11.tgz", "integrity": "sha512-FMT2tIMlKTEU5v3fHDKH2yI2vyTp6vh+6hxWmjYhkrLzZyoA+Jb/Eswg3bD+oKLFfms66TP6hCfYYVbZHpRJWQ==", "signatures": [{"sig": "MEUCIBu9wvB2ZCogWT1Blp3BLM2h+x9T//pvzzXYF3TS9z0pAiEA5XJWUys6UCLf6stILvrwjb7G8hdFUHFybd8R+IeE7GQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "engines": {"node": ">= 0.4.0"}, "scripts": {"test": "vows --spec --isolate"}, "_npmUser": {"name": "marak", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/flatiron/winston.git", "type": "git"}, "_npmVersion": "1.1.10", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "v0.6.12", "dependencies": {"eyes": "0.1.x", "async": "0.1.x", "colors": "0.x.x", "loggly": "0.3.x >=0.3.7", "pkginfo": "0.2.x", "stack-trace": "0.0.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.6.x"}, "_engineSupported": true, "optionalDependencies": {}}, "0.6.1": {"name": "winston", "version": "0.6.1", "keywords": ["logging", "sysadmin", "tools"], "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "_id": "winston@0.6.1", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "marak", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "22bfc4c995e307bf61603c0df5f3abbb319f7495", "tarball": "https://registry.npmjs.org/winston/-/winston-0.6.1.tgz", "integrity": "sha512-SqKbXjLNxxN6tIZvhxyTsISppDT/LQ1GI3xENDkFoEEz7jU+tDK27OhTALP+LHQmbUKEnYZvEX1B17unddnt2w==", "signatures": [{"sig": "MEUCIQClaBpvlwqRK5u8HmiL2GrBfQW0p6Ybqzn46/00uD7s4QIgadcKpCVCcYe5D7GHsL0/Br4iBZR6tErHrZ/aqfaT900=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "engines": {"node": ">= 0.4.0"}, "scripts": {"test": "vows --spec --isolate"}, "_npmUser": {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/flatiron/winston.git", "type": "git"}, "_npmVersion": "1.1.21", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "v0.6.18", "dependencies": {"eyes": "0.1.x", "async": "0.1.x", "cycle": "1.0.x", "colors": "0.x.x", "pkginfo": "0.2.x", "request": "2.9.x", "stack-trace": "0.0.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.6.x"}, "_engineSupported": true, "optionalDependencies": {}}, "0.6.2": {"name": "winston", "version": "0.6.2", "keywords": ["logging", "sysadmin", "tools"], "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "_id": "winston@0.6.2", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "marak", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "4144fe2586cdc19a612bf8c035590132c9064bd2", "tarball": "https://registry.npmjs.org/winston/-/winston-0.6.2.tgz", "integrity": "sha512-BzHNq8X415XGFkGPT+ACKTj95ghSAbR4eThAtKg4OC4PYVn5FLIdTETYUv76f4QxUcG1ps6yqnbO1K/81hGIzQ==", "signatures": [{"sig": "MEUCIELxDuf+Jj3LOgio/zHBA81hTc5Yhsd22pCCKQV9qQQwAiEAg0bINo6OZC/qS+dPThEx6h1Lut1ErowLzivegbFW7cU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "engines": {"node": ">= 0.4.0"}, "scripts": {"test": "vows --spec --isolate"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/flatiron/winston.git", "type": "git"}, "_npmVersion": "1.1.2", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "v0.8.1", "dependencies": {"eyes": "0.1.x", "async": "0.1.x", "cycle": "1.0.x", "colors": "0.x.x", "pkginfo": "0.2.x", "request": "2.9.x", "stack-trace": "0.0.x"}, "_defaultsLoaded": true, "devDependencies": {"vows": "0.6.x"}, "_engineSupported": true, "optionalDependencies": {}}, "0.7.0": {"name": "winston", "version": "0.7.0", "keywords": ["logging", "sysadmin", "tools"], "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "_id": "winston@0.7.0", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "V1", "email": "<EMAIL>"}], "dist": {"shasum": "6dc5c097897df706ea4eb555adcfd08c050b15e6", "tarball": "https://registry.npmjs.org/winston/-/winston-0.7.0.tgz", "integrity": "sha512-r8dnWWjL5UB3RrCLud7gNKUPibeaUdhi8TWBaaEd4vYsh+Bjjpv58fmgStSau0QLlnFOM+7I6/ujM3mnQGqMIQ==", "signatures": [{"sig": "MEQCIG8rYmvfiVrr9OPneitHLmwP8JFzHJzHhBNb6bhVj5Q7AiAi5mj+MVDoCt5EKSgapl4zm59PrdwLO6pnQkYqJyS+Kg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "_from": ".", "engines": {"node": ">= 0.6.0"}, "scripts": {"test": "vows --spec --isolate"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/flatiron/winston.git", "type": "git"}, "_npmVersion": "1.2.14", "description": "A multi-transport async logging library for Node.js", "directories": {}, "dependencies": {"eyes": "0.1.x", "async": "0.2.x", "cycle": "1.0.x", "colors": "0.6.x", "pkginfo": "0.3.x", "request": "2.16.x", "stack-trace": "0.0.x"}, "devDependencies": {"vows": "0.7.x"}}, "0.7.1": {"name": "winston", "version": "0.7.1", "keywords": ["logging", "sysadmin", "tools"], "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "_id": "winston@0.7.1", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "V1", "email": "<EMAIL>"}], "dist": {"shasum": "e291ab24eddbf79ea40ff532619277a0d30b0eb3", "tarball": "https://registry.npmjs.org/winston/-/winston-0.7.1.tgz", "integrity": "sha512-A6PehZ52klhIcxRuNB6md+oDdeSlfidZhn2qkQ/fZ+WCkusaUlQtfLKQz9PxnU64mpKJrNogqxlvOV/xTuysuw==", "signatures": [{"sig": "MEQCIHsiIDWWut9+CYAPKfS59D94MmO0UchT7Gvnrg7W1fzYAiB3/57Ucm6bXBipNL280nq+vqXNU4UbhVk58DNNzZ8hiw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "_from": ".", "engines": {"node": ">= 0.6.0"}, "scripts": {"test": "vows --spec --isolate"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/flatiron/winston.git", "type": "git"}, "_npmVersion": "1.2.14", "description": "A multi-transport async logging library for Node.js", "directories": {}, "dependencies": {"eyes": "0.1.x", "async": "0.2.x", "cycle": "1.0.x", "colors": "0.6.x", "pkginfo": "0.3.x", "request": "2.16.x", "stack-trace": "0.0.x"}, "devDependencies": {"vows": "0.7.x"}}, "0.7.2": {"name": "winston", "version": "0.7.2", "keywords": ["logging", "sysadmin", "tools"], "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "_id": "winston@0.7.2", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "V1", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/flatiron/winston/issues"}, "dist": {"shasum": "2570ae1aa1d8a9401e8d5a88362e1cf936550ceb", "tarball": "https://registry.npmjs.org/winston/-/winston-0.7.2.tgz", "integrity": "sha512-N2ri62PVDMs1MaMThDD9rhCVGeFIpWg1ek4c913TlbwSRJRnIfbcogXV+x/WkPtXAErO9AWl5+SuoUFw6sr2ig==", "signatures": [{"sig": "MEUCIBVrwGSD3SVWVSi/VBqTs0QtAo7UWbq5S869RFGpnNUoAiEAkmMF9+HNtgbNieBJat7pR8xr7YmjrDh/TXZen8NctL8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "_from": ".", "engines": {"node": ">= 0.6.0"}, "scripts": {"test": "vows --spec --isolate"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/flatiron/winston.git", "type": "git"}, "_npmVersion": "1.2.24", "description": "A multi-transport async logging library for Node.js", "directories": {}, "dependencies": {"eyes": "0.1.x", "async": "0.2.x", "cycle": "1.0.x", "colors": "0.6.x", "pkginfo": "0.3.x", "request": "2.16.x", "stack-trace": "0.0.x"}, "devDependencies": {"vows": "0.7.x"}}, "0.7.3": {"name": "winston", "version": "0.7.3", "keywords": ["logging", "sysadmin", "tools"], "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "_id": "winston@0.7.3", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "V1", "email": "<EMAIL>"}], "homepage": "https://github.com/flatiron/winston", "bugs": {"url": "https://github.com/flatiron/winston/issues"}, "dist": {"shasum": "7ae313ba73fcdc2ecb4aa2f9cd446e8298677266", "tarball": "https://registry.npmjs.org/winston/-/winston-0.7.3.tgz", "integrity": "sha512-iVTT8tf9YnTyfZX+aEUj2fl6WBRet7za6vdjMeyF8SA80Vii2rreM5XH+5qmpBV9uJGj8jz8BozvTDcroVq/eA==", "signatures": [{"sig": "MEYCIQDOfmTkQqnq4CrvEGi2aM4OOqV68J0BCA9XzKni6t+W7wIhAIV0kjIzgOeIWi7qm1WuA+tcUF5VWa0GFfl+IwTg8KcX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "_from": ".", "engines": {"node": ">= 0.6.0"}, "scripts": {"test": "vows --spec --isolate"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/flatiron/winston/master/LICENSE", "type": "MIT"}], "repository": {"url": "https://github.com/flatiron/winston.git", "type": "git"}, "_npmVersion": "1.4.4", "description": "A multi-transport async logging library for Node.js", "directories": {}, "dependencies": {"eyes": "0.1.x", "async": "0.2.x", "cycle": "1.0.x", "colors": "0.6.x", "pkginfo": "0.3.x", "request": "2.16.x", "stack-trace": "0.0.x"}, "devDependencies": {"vows": "0.7.x"}}, "0.8.0": {"name": "winston", "version": "0.8.0", "keywords": ["logging", "sysadmin", "tools"], "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "_id": "winston@0.8.0", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "V1", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/flatiron/winston", "bugs": {"url": "https://github.com/flatiron/winston/issues"}, "dist": {"shasum": "61d0830fa699706212206b0a2b5ca69a93043668", "tarball": "https://registry.npmjs.org/winston/-/winston-0.8.0.tgz", "integrity": "sha512-BoFzn3FEOWlq+1rDbDrbD093E3IRqukS8DYiqtY4vblIFR+5MSGUstAU228MGJa0vodiqm/iU2c8OGw6Iorx1g==", "signatures": [{"sig": "MEYCIQCl98bwxeR6G6gNGTWs/VNiO1gkJslMeoQpUXguJOaSSAIhANkjbN5EE7BHbuRwlOFaN1Ee+pkIIQeIeFlYJfgGC+co", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "_from": ".", "_shasum": "61d0830fa699706212206b0a2b5ca69a93043668", "engines": {"node": ">= 0.6.0"}, "gitHead": "5ff2673ea37f0122436248968f1e886a14f42c77", "scripts": {"test": "vows --spec --isolate"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/flatiron/winston/master/LICENSE", "type": "MIT"}], "repository": {"url": "https://github.com/flatiron/winston.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "A multi-transport async logging library for Node.js", "directories": {}, "dependencies": {"eyes": "0.1.x", "async": "0.2.x", "cycle": "1.0.x", "colors": "0.6.x", "pkginfo": "0.3.x", "stack-trace": "0.0.x"}, "devDependencies": {"vows": "0.7.x"}}, "0.8.1": {"name": "winston", "version": "0.8.1", "keywords": ["logging", "sysadmin", "tools"], "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "_id": "winston@0.8.1", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "V1", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/flatiron/winston", "bugs": {"url": "https://github.com/flatiron/winston/issues"}, "dist": {"shasum": "86bc9ec6c02aefe5c6dfdb88f3aff1b19d629216", "tarball": "https://registry.npmjs.org/winston/-/winston-0.8.1.tgz", "integrity": "sha512-t4NMQyaXPnHByq76CL8I3L8xz+OX5Clh/rHoUQxhZ1Q5ArU0Rb136ZZuNmsRMgFs/JWU4h3grwVfgNU9F4T3jQ==", "signatures": [{"sig": "MEYCIQCjxDrVwB6CAWhg3o8txFsZsZveokhNVH6Im6KxuKzwmQIhALKiLsgoSyx0J4TTL8scleqax6FR1+RPg3dOz0tVRC4F", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "_from": ".", "_shasum": "86bc9ec6c02aefe5c6dfdb88f3aff1b19d629216", "engines": {"node": ">= 0.6.0"}, "gitHead": "733d86f8b2e0ad4962343a18335a08c7ca53241e", "scripts": {"test": "vows --spec --isolate"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/flatiron/winston/master/LICENSE", "type": "MIT"}], "repository": {"url": "https://github.com/flatiron/winston.git", "type": "git"}, "_npmVersion": "2.0.2", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "0.10.32", "dependencies": {"eyes": "0.1.x", "async": "0.2.x", "cycle": "1.0.x", "colors": "0.6.x", "pkginfo": "0.3.x", "stack-trace": "0.0.x"}, "devDependencies": {"vows": "0.7.x"}}, "0.8.2": {"name": "winston", "version": "0.8.2", "keywords": ["logging", "sysadmin", "tools"], "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "_id": "winston@0.8.2", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "V1", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/flatiron/winston", "bugs": {"url": "https://github.com/flatiron/winston/issues"}, "dist": {"shasum": "1af036705152aa7ea62b60c11df4d21614e845b8", "tarball": "https://registry.npmjs.org/winston/-/winston-0.8.2.tgz", "integrity": "sha512-ljtYgS6dcYZu6H4rPWFHftvfVqiFWLFqMT0xQtZ8YQQQSQTTAP7vEOj7DkQWD0AaHB7mNIRQNRy23zHrEYM3rw==", "signatures": [{"sig": "MEYCIQDrdhdRXx1qtrRRHc7waPMtlFmHESF5gsgp7KlCBCA1YwIhAMapm4ptb2jqJZ5/c3vZtfxrTMrLKKt2dQY6HAOkniO+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "_from": ".", "_shasum": "1af036705152aa7ea62b60c11df4d21614e845b8", "engines": {"node": ">= 0.6.0"}, "gitHead": "6281760fa743144c23f62e3bb3b9e793ef1633b7", "scripts": {"test": "vows --spec --isolate"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/flatiron/winston/master/LICENSE", "type": "MIT"}], "repository": {"url": "https://github.com/flatiron/winston.git", "type": "git"}, "_npmVersion": "2.1.6", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "0.10.32", "dependencies": {"eyes": "0.1.x", "async": "0.2.x", "cycle": "1.0.x", "colors": "0.6.x", "pkginfo": "0.3.x", "isstream": "0.1.x", "stack-trace": "0.0.x"}, "devDependencies": {"vows": "0.7.x"}}, "0.8.3": {"name": "winston", "version": "0.8.3", "keywords": ["logging", "sysadmin", "tools"], "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "_id": "winston@0.8.3", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "V1", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}], "homepage": "https://github.com/flatiron/winston", "bugs": {"url": "https://github.com/flatiron/winston/issues"}, "dist": {"shasum": "64b6abf4cd01adcaefd5009393b1d8e8bec19db0", "tarball": "https://registry.npmjs.org/winston/-/winston-0.8.3.tgz", "integrity": "sha512-fPoamsHq8leJ62D1M9V/f15mjQ1UHe4+7j1wpAT3fqgA5JqhJkk4aIfPEjfMTI9x6ZTjaLOpMAjluLtmgO5b6g==", "signatures": [{"sig": "MEUCIH03B3fqTsIG2fMKvSzsmrjoculpXVh8Y6S7UbegOE9pAiEA3pNrVDHFapueYwwK0UD2Onn/Gsau7i5UhFre4m1mIeE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "_from": ".", "_shasum": "64b6abf4cd01adcaefd5009393b1d8e8bec19db0", "engines": {"node": ">= 0.6.0"}, "gitHead": "d12b2688d7d82557a5f5d490217e3c516bb661cb", "scripts": {"test": "vows --spec --isolate"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/flatiron/winston/master/LICENSE", "type": "MIT"}], "repository": {"url": "https://github.com/flatiron/winston.git", "type": "git"}, "_npmVersion": "2.1.6", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "0.10.32", "dependencies": {"eyes": "0.1.x", "async": "0.2.x", "cycle": "1.0.x", "colors": "0.6.x", "pkginfo": "0.3.x", "isstream": "0.1.x", "stack-trace": "0.0.x"}, "devDependencies": {"vows": "0.7.x"}}, "0.9.0": {"name": "winston", "version": "0.9.0", "keywords": ["logging", "sysadmin", "tools"], "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "_id": "winston@0.9.0", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "pose", "email": "alberto<PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/flatiron/winston", "bugs": {"url": "https://github.com/flatiron/winston/issues"}, "dist": {"shasum": "b5726e6c42291e305e36286ce7ae9f3b74a527a8", "tarball": "https://registry.npmjs.org/winston/-/winston-0.9.0.tgz", "integrity": "sha512-n+eQBWjdyOmLECo03RFrz7NBym7BUUok0BYEgoGtPmHFNM0nIyJtgpME5tiK+XMogxxNdozx4xcMNapKWI5H4A==", "signatures": [{"sig": "MEQCIAjMgPV3lPo1OSKP90h4m6HNy6qqxO+eUSD8edFla1TQAiAPwLFDZS1xI+FzDGgHL2hbOBrCxRjuRrg3Hr+qeKPTdQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "_from": ".", "_shasum": "b5726e6c42291e305e36286ce7ae9f3b74a527a8", "engines": {"node": ">= 0.8.0"}, "gitHead": "bcd8b0d3fb5063aa9416e5a6edcb371ca34efd6e", "scripts": {"test": "vows --spec --isolate"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/flatiron/winston/master/LICENSE", "type": "MIT"}], "repository": {"url": "https://github.com/flatiron/winston.git", "type": "git"}, "_npmVersion": "2.2.0", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "0.10.33", "dependencies": {"eyes": "0.1.x", "async": "0.9.x", "cycle": "1.0.x", "colors": "1.0.x", "pkginfo": "0.3.x", "isstream": "0.1.x", "stack-trace": "0.0.x"}, "devDependencies": {"hock": "1.x.x", "vows": "0.7.x", "std-mocks": "~1.0.0"}}, "1.0.0": {"name": "winston", "version": "1.0.0", "keywords": ["logging", "sysadmin", "tools"], "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "_id": "winston@1.0.0", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "pose", "email": "alberto<PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "30e36e0041fc0a864b0029565719e4dc41d026a4", "tarball": "https://registry.npmjs.org/winston/-/winston-1.0.0.tgz", "integrity": "sha512-LAzO0CWQnvqRQKk4gW7UmqyYrmmpKySt6nG1/24OeIm5gxaI/J12aRD9gYYxb0j1ZCCCkC4VcHD62OQ8kkUo2w==", "signatures": [{"sig": "MEUCIQC1+YZ1LWPZf2/T5PcyjvSCobxnJgQBzTFeEhXK9nZTpQIgY6lGxd2oKCUnCvnImI4lwh6hPmlXlxgNVVoCvJ+Q0AQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "_from": ".", "_shasum": "30e36e0041fc0a864b0029565719e4dc41d026a4", "engines": {"node": ">= 0.8.0"}, "gitHead": "e915acb2cb0819ae6669703938e887ff4c6f6a20", "scripts": {"test": "vows --spec --isolate"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.github.com/winstonjs/winston/master/LICENSE", "type": "MIT"}], "repository": {"url": "https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "2.6.1", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "0.10.36", "dependencies": {"eyes": "0.1.x", "async": "0.9.x", "cycle": "1.0.x", "colors": "1.0.x", "pkginfo": "0.3.x", "isstream": "0.1.x", "stack-trace": "0.0.x"}, "devDependencies": {"hock": "1.x.x", "vows": "0.7.x", "std-mocks": "~1.0.0"}}, "1.0.1": {"name": "winston", "version": "1.0.1", "keywords": ["logging", "sysadmin", "tools"], "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@1.0.1", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "pose", "email": "alberto<PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "4c6f5a1167ebc516ac29b76e4eadb873c15289a4", "tarball": "https://registry.npmjs.org/winston/-/winston-1.0.1.tgz", "integrity": "sha512-MDfzOMhdpt0EJHNd7FInsIoNZak7v4c5a8dPH5OnxkKwvs0qq75HG99e7uPIGRQmOnPJOHXnPiatXIbhKNvBZA==", "signatures": [{"sig": "MEUCIQC5zwKJWdnBQqWxPO8KBFYCtbMu0h1IRSxL1Mri009+RQIgMAJts9t96iJoqYd1eCbsez1GUk8QttI/aVmiUgDp1tE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "_from": ".", "_shasum": "4c6f5a1167ebc516ac29b76e4eadb873c15289a4", "engines": {"node": ">= 0.8.0"}, "gitHead": "cc5d04dd1139cd9d7f567feb511fb08dacf738dd", "scripts": {"test": "vows --spec --isolate"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "2.7.5", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "0.10.38", "dependencies": {"eyes": "0.1.x", "async": "0.9.x", "cycle": "1.0.x", "colors": "1.0.x", "pkginfo": "0.3.x", "isstream": "0.1.x", "stack-trace": "0.0.x"}, "devDependencies": {"hock": "1.x.x", "vows": "0.7.x", "std-mocks": "~1.0.0"}}, "1.0.2": {"name": "winston", "version": "1.0.2", "keywords": ["logging", "sysadmin", "tools"], "author": {"name": "Nodejitsu Inc.", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@1.0.2", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "pose", "email": "alberto<PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "351c58e2323f8a4ca29a45195aa9aa3b4c35d76f", "tarball": "https://registry.npmjs.org/winston/-/winston-1.0.2.tgz", "integrity": "sha512-BLxJH3KCgJ2paj2xKYTQLpxdKr9URPDDDLJnRVcbud7izT+m8Xzt5Rod6mnNgEcfT0fRvhEy2Cj3cEnnQpa6qA==", "signatures": [{"sig": "MEUCIQDnyVHPn2pY/vsC1wEvx9VnVha7QQ1LGD+qnurj7lCQdgIgAONrgkpguSRFFBzPKKtJAOadhsFi5MQZ8GsT4weFYJI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "_from": ".", "_shasum": "351c58e2323f8a4ca29a45195aa9aa3b4c35d76f", "engines": {"node": ">= 0.8.0"}, "gitHead": "1223d420f72cbdc064a94c98cc09a97db5866e7a", "scripts": {"test": "vows --spec --isolate"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "2.14.5", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"eyes": "0.1.x", "async": "~1.0.0", "cycle": "1.0.x", "colors": "1.0.x", "pkginfo": "0.3.x", "isstream": "0.1.x", "stack-trace": "0.0.x"}, "devDependencies": {"hock": "1.x.x", "vows": "0.7.x", "std-mocks": "~1.0.0"}}, "1.1.0": {"name": "winston", "version": "1.1.0", "keywords": ["logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@1.1.0", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "pose", "email": "alberto<PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "8e86ff561ccea5007711712159c21ed057209011", "tarball": "https://registry.npmjs.org/winston/-/winston-1.1.0.tgz", "integrity": "sha512-VD9ha6emrdDB+lxdM1Tu+ZnScaFN9CRJd4JB93zhL4VIOUcT8PbPUk6tTJllXqXQNF/uYBqn4wpl+kSNAGjJgA==", "signatures": [{"sig": "MEYCIQDHXj6vRdIWDfIjoUjDDXANMxAIT9LpezbTfGuJGr4tiwIhAPILcQ9gc3RnRM4BmJ2UUEkauXdJbim9mALuHOBjsuSU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "_from": ".", "_shasum": "8e86ff561ccea5007711712159c21ed057209011", "engines": {"node": ">= 0.8.0"}, "gitHead": "fb12093fa769fafc58971d3315c1096e54b40b03", "scripts": {"test": "vows --spec --isolate"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "2.14.1", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "0.10.38", "dependencies": {"eyes": "0.1.x", "async": "~1.0.0", "cycle": "1.0.x", "colors": "1.0.x", "pkginfo": "0.3.x", "isstream": "0.1.x", "stack-trace": "0.0.x"}, "devDependencies": {"hock": "1.x.x", "vows": "0.7.x", "std-mocks": "~1.0.0"}}, "1.1.1": {"name": "winston", "version": "1.1.1", "keywords": ["logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@1.1.1", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "pose", "email": "alberto<PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "9f1887afb92cc0f23459d20de2a185b5ae11a9b0", "tarball": "https://registry.npmjs.org/winston/-/winston-1.1.1.tgz", "integrity": "sha512-As8EqbXJVpi0DBEkuuAa6SbN0rFxbgFRebB5WRjlB5k6Ws7RzfAEjgoczr8c3G+qpa7P6ubWTjzdNakbChgXCw==", "signatures": [{"sig": "MEQCICyu2GhZdRTkqsNe0wmWhDC1dljbW79g+Rad2Mg8njMXAiBlVR43h/0Dky2Egwp71n2TjK5ToCXB8x5a/KXFQvy4wg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "_from": ".", "_shasum": "9f1887afb92cc0f23459d20de2a185b5ae11a9b0", "engines": {"node": ">= 0.8.0"}, "gitHead": "f3d0c820bbcc32877a0dc7c31c629fb6a44c5cf4", "scripts": {"test": "vows --spec --isolate"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "4.2.1", "dependencies": {"eyes": "0.1.x", "async": "~1.0.0", "cycle": "1.0.x", "colors": "1.0.x", "pkginfo": "0.3.x", "isstream": "0.1.x", "stack-trace": "0.0.x"}, "devDependencies": {"hock": "1.x.x", "vows": "0.7.x", "std-mocks": "~1.0.0"}}, "1.1.2": {"name": "winston", "version": "1.1.2", "keywords": ["logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@1.1.2", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "pose", "email": "alberto<PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "68edd769ff79d4f9528cf0e5d80021aade67480c", "tarball": "https://registry.npmjs.org/winston/-/winston-1.1.2.tgz", "integrity": "sha512-rl9hA8se2gjdYI6nP1f+kjjSCFCZrObIJB/eXOcMdzWxxcYp7exyc5Bs248fwLT+wHA/+aK0VtBlPHL8qO0T0w==", "signatures": [{"sig": "MEUCIQC0XESUgCDmgsHpD4M7fbPYeWr+2nlGAJgWtYAA+czs6gIgD6pE0DGX9ZrgEaAT9GMbgdxcyVacIQLMybDTzDMbcN8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "_from": ".", "_shasum": "68edd769ff79d4f9528cf0e5d80021aade67480c", "engines": {"node": ">= 0.8.0"}, "gitHead": "2064bfe3b0f3da77c798d248a1609dbc24ff077e", "scripts": {"test": "vows --spec --isolate"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "2.14.5", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"eyes": "0.1.x", "async": "~1.0.0", "cycle": "1.0.x", "colors": "1.0.x", "pkginfo": "0.3.x", "isstream": "0.1.x", "stack-trace": "0.0.x"}, "devDependencies": {"hock": "1.x.x", "vows": "0.7.x", "std-mocks": "~1.0.0"}}, "2.0.0": {"name": "winston", "version": "2.0.0", "keywords": ["winston", "logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@2.0.0", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "pose", "email": "alberto<PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "d3ad5dc2db5ce716ff1825b9283cbfbb6f481aee", "tarball": "https://registry.npmjs.org/winston/-/winston-2.0.0.tgz", "integrity": "sha512-dO5+7yZeOWTakUbO1p8tpuM1ZgYPgkVMJUxX0i1RqaXn5UUBWimEfKk0dRLJcndcHYLpFc6PN+kGHekJReFE1w==", "signatures": [{"sig": "MEUCIC773nJokmrBW4SRD46j2rDZ5iJwYfdtNSgxNeldMwoHAiEA3nJsYnx89esaxI9wCNBvcvgy8BtxWzBSxcTErUD10Kk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "_from": ".", "_shasum": "d3ad5dc2db5ce716ff1825b9283cbfbb6f481aee", "engines": {"node": ">= 0.8.0"}, "gitHead": "020409b656230914337081f89db684fb6346f408", "scripts": {"test": "vows --spec --isolate"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "2.14.5", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"eyes": "0.1.x", "async": "~1.0.0", "cycle": "1.0.x", "colors": "1.0.x", "pkginfo": "0.3.x", "isstream": "0.1.x", "stack-trace": "0.0.x"}, "devDependencies": {"hock": "1.x.x", "vows": "0.7.x", "std-mocks": "~1.0.0"}}, "2.0.1": {"name": "winston", "version": "2.0.1", "keywords": ["winston", "logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@2.0.1", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "pose", "email": "alberto<PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "999d682648f0b24ae4a3096d774409c5bb86d87c", "tarball": "https://registry.npmjs.org/winston/-/winston-2.0.1.tgz", "integrity": "sha512-EtHJpOXpKUAZEwZvLFNTdUrdIaXxOHl1LYtVfk1uAkJvv90R87Tley002aOjjNG2wPCftcunjIjIZ1SPSH6tOg==", "signatures": [{"sig": "MEUCIQDtn6C71rsL5w+YIbMvf/WfUcXdRvQSBJEXE1ThDWy7WAIgYqiIKhUcr22SskrGpkDghTONXb8LLrt+yAkUuW62XPI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "_from": ".", "_shasum": "999d682648f0b24ae4a3096d774409c5bb86d87c", "engines": {"node": ">= 0.8.0"}, "gitHead": "8a549b720888c9166ffd4e0f09ac25f93e337aa2", "scripts": {"test": "vows --spec --isolate"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "2.14.1", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "0.10.38", "dependencies": {"eyes": "0.1.x", "async": "~1.0.0", "cycle": "1.0.x", "colors": "1.0.x", "pkginfo": "0.3.x", "isstream": "0.1.x", "stack-trace": "0.0.x"}, "devDependencies": {"hock": "1.x.x", "vows": "0.7.x", "std-mocks": "~1.0.0"}}, "2.1.0": {"name": "winston", "version": "2.1.0", "keywords": ["winston", "logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@2.1.0", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "pose", "email": "alberto<PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "34688215cc8dbb784838b9aa626e73aee44fe4b6", "tarball": "https://registry.npmjs.org/winston/-/winston-2.1.0.tgz", "integrity": "sha512-GMZw5KoUASLrnEyCIYreSwn0WfTyLPpRiYIDh4kte3aRGOfSt2T70nrQ4A47Eq/ifG2f4LBNvOOMSiI+UkPLIA==", "signatures": [{"sig": "MEYCIQDOxtmiTesCt+f1DkGttzFB9vcWXORer/d2ITMhYvR0RwIhALgnI5Ft5OSQW1FUEsh0Y6zDG9N3VbT8mfws6u5arXel", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "_from": ".", "_shasum": "34688215cc8dbb784838b9aa626e73aee44fe4b6", "engines": {"node": ">= 0.8.0"}, "gitHead": "edbf0d2c6c57c6712ffce772f16bd1a2124e68f3", "scripts": {"test": "vows --spec --isolate"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "2.14.5", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"eyes": "0.1.x", "async": "~1.0.0", "cycle": "1.0.x", "colors": "1.0.x", "pkginfo": "0.3.x", "isstream": "0.1.x", "stack-trace": "0.0.x"}, "devDependencies": {"hock": "1.x.x", "vows": "0.7.x", "std-mocks": "~1.0.0"}}, "2.1.1": {"name": "winston", "version": "2.1.1", "keywords": ["winston", "logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@2.1.1", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "pose", "email": "alberto<PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "3c9349d196207fd1bdff9d4bc43ef72510e3a12e", "tarball": "https://registry.npmjs.org/winston/-/winston-2.1.1.tgz", "integrity": "sha512-CPXrr+LD3DBeCEAnhPYS7DYbdq8kwhnkrVY7Px0vEROil9iZWaz0VHZHg41pNcUJc+1/PDm2obR1Lb2QGto1ZQ==", "signatures": [{"sig": "MEQCIDTHR/NoitfkvWgxY9iM/82BGW+pzIyOxyOjdkxz12svAiBfkj1uZVFNBAUA/XtIO9DHlh/O49o9J2kKBxnSHX4rsA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "_from": ".", "_shasum": "3c9349d196207fd1bdff9d4bc43ef72510e3a12e", "engines": {"node": ">= 0.8.0"}, "gitHead": "69171ed1679a290e947f22a83b044b3980be3565", "scripts": {"test": "vows --spec --isolate"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "2.14.5", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "4.2.2", "dependencies": {"eyes": "0.1.x", "async": "~1.0.0", "cycle": "1.0.x", "colors": "1.0.x", "pkginfo": "0.3.x", "isstream": "0.1.x", "stack-trace": "0.0.x"}, "devDependencies": {"hock": "1.x.x", "vows": "0.7.x", "std-mocks": "~1.0.0", "cross-spawn-async": "^2.0.0"}}, "2.2.0": {"name": "winston", "version": "2.2.0", "keywords": ["winston", "logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@2.2.0", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "pose", "email": "alberto<PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "2c853dd87ab552a8e8485d72cbbf9a2286f029b7", "tarball": "https://registry.npmjs.org/winston/-/winston-2.2.0.tgz", "integrity": "sha512-uWxlDhugkqGPbxxn3aWL0VZe3b3Ht8J0s1TzqYyt6TgC84xgUs2e47u2v6SYZGAaRrTZzuCYkdOEuL9KrfYuJQ==", "signatures": [{"sig": "MEUCIQD0j35+RFHBDiT1XzCtDbCpDtKc4mvVDjfNTqM9Gf+nDAIgNHCZMMCm4mtbvXW3xj/pT5U1wOoCOIE3aAR/wGrEtWI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "_from": ".", "_shasum": "2c853dd87ab552a8e8485d72cbbf9a2286f029b7", "engines": {"node": ">= 0.10.0"}, "gitHead": "b44062612f63211d047c67050fef5b7624682fb6", "scripts": {"test": "vows --spec --isolate"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "3.7.3", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "4.2.2", "dependencies": {"eyes": "0.1.x", "async": "~1.0.0", "cycle": "1.0.x", "colors": "1.0.x", "pkginfo": "0.3.x", "isstream": "0.1.x", "stack-trace": "0.0.x"}, "devDependencies": {"hock": "1.x.x", "vows": "0.7.x", "std-mocks": "~1.0.0", "cross-spawn-async": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/winston-2.2.0.tgz_1456439000097_0.023458356503397226", "host": "packages-9-west.internal.npmjs.com"}}, "2.3.0": {"name": "winston", "version": "2.3.0", "keywords": ["winston", "logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@2.3.0", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "pose", "email": "alberto<PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "207faaab6fccf3fe493743dd2b03dbafc7ceb78c", "tarball": "https://registry.npmjs.org/winston/-/winston-2.3.0.tgz", "integrity": "sha512-N5MZhX8DdPk9jx+HMuUtBya0jn5wFhUv7P74pXwf4GdUr+adwUK738uMsjyjmyFgx6nQC9o9BELw3fnX9fsLPw==", "signatures": [{"sig": "MEUCIC8Y6gOhZR0vFOKiJ6CG8cQsNeEF4LZC7KLJbUO/MA8KAiEAvVBBomd6Ef5Ascj8sH6QtYlBVe/RA5ztz5kuC3HEn5s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "_from": ".", "_shasum": "207faaab6fccf3fe493743dd2b03dbafc7ceb78c", "engines": {"node": ">= 0.10.0"}, "gitHead": "739d8023c87183261dcae6ecc92c5b87a5eac961", "scripts": {"test": "vows --spec --isolate"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "3.9.0", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "4.4.4", "dependencies": {"eyes": "0.1.x", "async": "~1.0.0", "cycle": "1.0.x", "colors": "1.0.x", "isstream": "0.1.x", "stack-trace": "0.0.x"}, "devDependencies": {"hock": "1.x.x", "vows": "0.7.x", "std-mocks": "~1.0.0", "cross-spawn-async": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/winston-2.3.0.tgz_1478060833954_0.4984084344469011", "host": "packages-12-west.internal.npmjs.com"}}, "2.3.1": {"name": "winston", "version": "2.3.1", "keywords": ["winston", "logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@2.3.1", "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "pose", "email": "alberto<PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "0b48420d978c01804cf0230b648861598225a119", "tarball": "https://registry.npmjs.org/winston/-/winston-2.3.1.tgz", "integrity": "sha512-ghY5UimtTug92mUZeNZXvfXy7E4Lo6q4DIAuiHi6LWaU+kcZvUsqKXXtXYaqOTbzqs0zbcz58Tw9w8VE3YuXCA==", "signatures": [{"sig": "MEUCIHX/WX+BL8x4kChh1iGvv8BvyXg5Ifd7aZunRERSNgFVAiEAuxdDqzzr6L6khSlw8aKEddwWpZnBW9MFbEZlqiVTDXg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "_from": ".", "_shasum": "0b48420d978c01804cf0230b648861598225a119", "engines": {"node": ">= 0.10.0"}, "gitHead": "fba37b44f7875ba7c460df81fad27d6a941ed213", "scripts": {"test": "vows --spec --isolate"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "3.10.5", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "4.7.2", "dependencies": {"eyes": "0.1.x", "async": "~1.0.0", "cycle": "1.0.x", "colors": "1.0.x", "isstream": "0.1.x", "stack-trace": "0.0.x"}, "devDependencies": {"hock": "1.x.x", "vows": "0.7.x", "std-mocks": "~1.0.0", "cross-spawn-async": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/winston-2.3.1.tgz_1484937497945_0.36467634653672576", "host": "packages-18-east.internal.npmjs.com"}}, "2.4.0": {"name": "winston", "version": "2.4.0", "keywords": ["winston", "logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@2.4.0", "maintainers": [{"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "pose", "email": "alberto<PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "808050b93d52661ed9fb6c26b3f0c826708b0aee", "tarball": "https://registry.npmjs.org/winston/-/winston-2.4.0.tgz", "integrity": "sha512-4/DotzcMp5ilGEC0KrZkpZ0pCHuUrVWmM41qAT5zAa5nNaBAyc1MQBPTVjcajiYf1D6b+CHjziYNY2Mi6Svv2g==", "signatures": [{"sig": "MEYCIQCtC7HQwnj7a1ABq1Bjnrv0/iahzjcp2vN2hDG6GaULLwIhANssUBy7wGNAnDfILEREd46318oazLUXMLmlHW4Oyl/S", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "_from": ".", "_shasum": "808050b93d52661ed9fb6c26b3f0c826708b0aee", "engines": {"node": ">= 0.10.0"}, "gitHead": "ffe883e4c8e467e543eda2e0673370f176bd5eb7", "scripts": {"test": "vows --spec --isolate"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "6.10.0", "dependencies": {"eyes": "0.1.x", "async": "~1.0.0", "cycle": "1.0.x", "colors": "1.0.x", "isstream": "0.1.x", "stack-trace": "0.0.x"}, "devDependencies": {"hock": "1.x.x", "vows": "0.7.x", "std-mocks": "~1.0.0", "cross-spawn-async": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/winston-2.4.0.tgz_1506920353218_0.3485619300045073", "host": "s3://npm-registry-packages"}}, "3.0.0-rc0": {"name": "winston", "version": "3.0.0-rc0", "keywords": ["winston", "logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@3.0.0-rc0", "maintainers": [{"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "pose", "email": "alberto<PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "08ee0b1304cd671f1708ea360ffeeb4135d6dcf3", "tarball": "https://registry.npmjs.org/winston/-/winston-3.0.0-rc0.tgz", "integrity": "sha512-OxZxVs4WO0cseapp+6soIi6TCw/toyOMhWTZP2UnZ/5AiOhCHT657z65vEcWK5Pt8ekMmViw1Kz9d0lJw3XNEQ==", "signatures": [{"sig": "MEYCIQCJ1wbD+B/tApZmbaQjmFRkUt8tFdrZBkQGzSqR6kERLwIhAMYGE33lkr27OriBoSkndKCd/UfQrjJtXb+a+XyuWgwn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "_from": ".", "_shasum": "08ee0b1304cd671f1708ea360ffeeb4135d6dcf3", "engines": {"node": ">= 4.2.2"}, "gitHead": "76b7f01ffd0af496e94d53b5688c914ddbef05f5", "scripts": {"lint": "populist lib/*.js lib/winston/*.js lib/winston/**/*.js", "test": "nyc mocha test/*.test.js test/**/*.test.js", "report": "nyc report --reporter=lcov", "pretest": "npm run lint"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "6.10.0", "dependencies": {"async": "^1.0.0", "logform": "^1.2.1", "isstream": "0.1.x", "one-time": "0.0.4", "diagnostics": "^1.0.1", "stack-trace": "0.0.x", "triple-beam": "^1.0.1", "winston-transport": "^3.0.1"}, "devDependencies": {"nyc": "^11.2.1", "hock": "^1.3.2", "mocha": "^3.2.0", "assume": "^1.5.1", "colors": "~1.1.2", "split2": "^2.1.1", "through2": "^2.0.0", "std-mocks": "^1.0.0", "winston-compat": "^0.0.1", "cross-spawn-async": "^2.0.0", "eslint-config-populist": "^4.1.0", "abstract-winston-transport": ">= 0.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/winston-3.0.0-rc0.tgz_1506921434934_0.27894210652448237", "host": "s3://npm-registry-packages"}}, "3.0.0-rc1": {"name": "winston", "version": "3.0.0-rc1", "keywords": ["winston", "logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@3.0.0-rc1", "maintainers": [{"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "pose", "email": "alberto<PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "982bc0ad4ef5c53000ca68036d78a3deaa28cac5", "tarball": "https://registry.npmjs.org/winston/-/winston-3.0.0-rc1.tgz", "integrity": "sha512-aNtKirnK2UEe5v56SK0TIEr5ylyYdXyjAaIJXZTk21UlNx7FQclTkVU2T1ZzMtdDM+Rk2b7vrI/e/4n8U84XaQ==", "signatures": [{"sig": "MEQCID4IsLeUIhtknDdkRiZw0lar3ON9Aha4HUGpcrmYf2sJAiA+/RacwVk1G3squHArzY/FdjkkRzWgdzN0uggWfULNBQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/winston", "engines": {"node": ">= 4.2.2"}, "gitHead": "7e44c93940af60bdc9bba71d465821629ca1366f", "scripts": {"lint": "populist lib/*.js lib/winston/*.js lib/winston/**/*.js", "test": "nyc mocha test/*.test.js test/**/*.test.js", "report": "nyc report --reporter=lcov", "pretest": "npm run lint"}, "_npmUser": {"name": "jcrugzz", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "8.6.0", "dependencies": {"async": "^1.0.0", "logform": "^1.2.1", "isstream": "0.1.x", "one-time": "0.0.4", "diagnostics": "^1.0.1", "stack-trace": "0.0.x", "triple-beam": "^1.0.1", "winston-transport": "^3.0.1"}, "devDependencies": {"nyc": "^11.2.1", "hock": "^1.3.2", "mocha": "^3.2.0", "assume": "^1.5.1", "colors": "~1.1.2", "split2": "^2.1.1", "through2": "^2.0.0", "std-mocks": "^1.0.0", "winston-compat": "^0.0.1", "cross-spawn-async": "^2.0.0", "eslint-config-populist": "^4.1.0", "abstract-winston-transport": ">= 0.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/winston-3.0.0-rc1.tgz_1508444238403_0.7883252075407654", "host": "s3://npm-registry-packages"}}, "2.4.1": {"name": "winston", "version": "2.4.1", "keywords": ["winston", "logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@2.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "pose", "email": "alberto<PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "a3a9265105564263c6785b4583b8c8aca26fded6", "tarball": "https://registry.npmjs.org/winston/-/winston-2.4.1.tgz", "fileCount": 31, "integrity": "sha512-k/+Dkzd39ZdyJHYkuaYmf4ff+7j+sCIy73UCOWHYA67/WXU+FF/Y6PF28j+Vy7qNRPHWO+dR+/+zkoQWPimPqg==", "signatures": [{"sig": "MEUCIQC7cDFFjYUpmcMPZjFYoTzUurmUyJ+QcaHIgJINcJ348QIgfYmNnPnpz56DyqBce7OlCUyTJMNPcDOoUvmPc/iqZTo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 172485}, "main": "./lib/winston", "engines": {"node": ">= 0.10.0"}, "gitHead": "ea9cd55ad47bc9cbaabcbd47a0d134c2e3f6de28", "scripts": {"test": "vows --spec --isolate"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"eyes": "0.1.x", "async": "~1.0.0", "cycle": "1.0.x", "colors": "1.0.x", "isstream": "0.1.x", "stack-trace": "0.0.x"}, "_hasShrinkwrap": false, "devDependencies": {"hock": "1.x.x", "vows": "0.7.x", "std-mocks": "~1.0.0", "cross-spawn-async": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/winston_2.4.1_1520622437251_0.3264411258734665", "host": "s3://npm-registry-packages"}}, "3.0.0-rc2": {"name": "winston", "version": "3.0.0-rc2", "keywords": ["winston", "logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@3.0.0-rc2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "pose", "email": "alberto<PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "c30d043b20822c5f6da4797242108b1c5860a3a2", "tarball": "https://registry.npmjs.org/winston/-/winston-3.0.0-rc2.tgz", "fileCount": 30, "integrity": "sha512-wkyf9OzpX6gWJEb8zw1NM6pR2mz2CbrH6ziqDPoVWDDapzFhfJ417N43nSG4aS/ZOC8PZfCvYz3hE5gbEQwGJg==", "signatures": [{"sig": "MEUCIQDr/+OWvw4YxiVP8dZkhQ/hvY7EX83NJJftgQShglVjGwIgU2fTbbrrhaqAFdMGAqMnUZ+vAKQD5QcXqJoNjdSbJtQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 142343}, "main": "./lib/winston", "engines": {"node": ">= 4.2.2"}, "gitHead": "1e0b96a175551f27e2b2aef4771cca9480757640", "scripts": {"lint": "populist lib/*.js lib/winston/*.js lib/winston/**/*.js", "test": "nyc mocha test/*.test.js test/**/*.test.js", "report": "nyc report --reporter=lcov", "pretest": "npm run lint"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"async": "^2.6.0", "logform": "^1.2.1", "isstream": "0.1.x", "one-time": "0.0.4", "diagnostics": "^1.0.1", "stack-trace": "0.0.x", "triple-beam": "^1.0.1", "winston-transport": "^3.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.2.1", "hock": "^1.3.2", "mocha": "^3.2.0", "assume": "^1.5.1", "colors": ">= 1.2.0-rc0", "split2": "^2.1.1", "through2": "^2.0.0", "std-mocks": "^1.0.0", "winston-compat": "^0.0.1", "cross-spawn-async": "^2.0.0", "eslint-config-populist": "^4.1.0", "abstract-winston-transport": ">= 0.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/winston_3.0.0-rc2_1520623084959_0.7783665522283425", "host": "s3://npm-registry-packages"}}, "3.0.0-rc3": {"name": "winston", "version": "3.0.0-rc3", "keywords": ["winston", "logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@3.0.0-rc3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "pose", "email": "alberto<PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "dd31ebbd76e35968dcf5f130e57473329534904e", "tarball": "https://registry.npmjs.org/winston/-/winston-3.0.0-rc3.tgz", "fileCount": 30, "integrity": "sha512-KPvs53IB5m6wMHAm4j5DgL8MkgfXZKjwNqzTr3IA8SijHTXeN6l8ebyR24rlCSVotuqtXhooFHvYLbA3fAViSw==", "signatures": [{"sig": "MEYCIQCT/W50934tyGD2rdAiQ+DiIyMAbytOCfY/1iu1HY9KwQIhAKQahUugZ35fe1N/ylYHts2fbDG+OYdVgyF4M+EDmEYT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 143167}, "main": "./lib/winston", "engines": {"node": ">= 4.2.2"}, "gitHead": "ae55f5baf71ebeb8dcd8e5bbff8494faa66f27af", "scripts": {"lint": "populist lib/*.js lib/winston/*.js lib/winston/**/*.js", "test": "nyc mocha test/*.test.js test/**/*.test.js", "report": "nyc report --reporter=lcov", "pretest": "npm run lint"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"async": "^2.6.0", "logform": "^1.2.1", "isstream": "0.1.x", "one-time": "0.0.4", "diagnostics": "^1.0.1", "stack-trace": "0.0.x", "triple-beam": "^1.0.1", "winston-transport": "^3.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.2.1", "hock": "^1.3.2", "mocha": "^3.2.0", "assume": "^1.5.1", "colors": "^1.2.0", "split2": "^2.1.1", "through2": "^2.0.0", "std-mocks": "^1.0.0", "winston-compat": "^0.0.1", "cross-spawn-async": "^2.0.0", "eslint-config-populist": "^4.1.0", "abstract-winston-transport": ">= 0.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/winston_3.0.0-rc3_1521223179923_0.21623720019923054", "host": "s3://npm-registry-packages"}}, "3.0.0-rc4": {"name": "winston", "version": "3.0.0-rc4", "keywords": ["winston", "logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@3.0.0-rc4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "pose", "email": "alberto<PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "2e34e05b1130bae677c79b3ace993d091e678c5f", "tarball": "https://registry.npmjs.org/winston/-/winston-3.0.0-rc4.tgz", "fileCount": 30, "integrity": "sha512-wyLu9Uky/uB6F6VqVyodYMevBigxlX0qTIbQhD39+G/7XiMf4G4oX9meMMDJNTvCdZyUUUFXOidnLW5yR3oedQ==", "signatures": [{"sig": "MEQCIHakiqiSAVLFTz8ArxjLcuJEkp2rImLspmwBNiH7MRstAiAqeJT6djKXre3a+qPJD/aRtMz+SBq7eXNnSkqrXM2xHQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 145093}, "main": "./lib/winston", "engines": {"node": ">= 4.2.2"}, "gitHead": "a2521ab12b9cf940d1c0b96f19c7b12aa162e671", "scripts": {"lint": "populist lib/*.js lib/winston/*.js lib/winston/**/*.js", "test": "nyc mocha test/*.test.js test/**/*.test.js", "report": "nyc report --reporter=lcov", "pretest": "npm run lint"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "5.8.0", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"async": "^2.6.0", "logform": "^1.4.1", "one-time": "0.0.4", "is-stream": "^1.1.0", "diagnostics": "^1.0.1", "stack-trace": "0.0.x", "triple-beam": "^1.0.1", "winston-transport": "^3.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.2.1", "hock": "^1.3.2", "mocha": "^3.2.0", "assume": "^1.5.1", "colors": "^1.2.0", "split2": "^2.1.1", "through2": "^2.0.0", "std-mocks": "^1.0.0", "winston-compat": "^0.0.1", "cross-spawn-async": "^2.0.0", "eslint-config-populist": "^4.1.0", "abstract-winston-transport": ">= 0.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/winston_3.0.0-rc4_1523035013459_0.1832622552601204", "host": "s3://npm-registry-packages"}}, "2.4.2": {"name": "winston", "version": "2.4.2", "keywords": ["winston", "logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@2.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "pose", "email": "alberto<PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "3ca01f763116fc48db61053b7544e750431f8db0", "tarball": "https://registry.npmjs.org/winston/-/winston-2.4.2.tgz", "fileCount": 36, "integrity": "sha512-4S/Ad4ZfSNl8OccCLxnJmNISWcm2joa6Q0YGDxlxMzH0fgSwWsjMt+SmlNwCqdpaPg3ev1HKkMBsIiXeSUwpbA==", "signatures": [{"sig": "MEYCIQDokvp5sYKWxiy0tGvPCjwTX4WoJKtjgdmI7QEm2hV1zQIhAKEkatXwX+Xo11Uo5DDCDp3hgpJsPoc+6ag7xt/qm+pr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 175387, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa2htsCRA9TVsSAnZWagAAqPwP+wa4pALvTCdbCD5G2C78\nq1qQN3RzkM7hpD9Aq0XqjX+mXjC6VbDgZcNcihXps9zmhT4cCtG/lQCoIJwK\n+/9yHEiMcJPmP0L4kNBVRbC8tASNm6lpYhrtn6PN2giA2xFadbwjtZzi6Nhb\nXnickaoO7W7s20OfGZDh7OvivBhXWntQHyZorM8x/iXUO7NvNMtRH3bdnXr3\nKBTYgSYDyA1fwLijZbqksrk0j0tVX3P70HYaEjaY0LUwzKJzsBgxJJYsNdSY\nlmGcp7Yvt4Y5l0ZKJMnQUkOpxWmQ24jrFGg3CBSocHtwaCnGd1eYrzYdtzcM\nd5LZKCWRbtaRA16ItKwNhw4Foew3L+2emjJV6fTiJWFHOOccg0CtRUT4ofJT\ndY7yV8rcgFuMvE+cgbVw7UxS2JBXEcDPtBZrNjbZa94PNlYak2REKdVpQRLp\nApaxXJ6FOm0n8qXs4MB+cM1yWbVl028k7z+JqbHsdkfS6c+W0gHnR7Y90eFi\nkfgBrLKGqdgoL8ENjWi08YMAUdbEaYPhP5LA3oHU8glK8shQ3C9OEG86Yc0R\nxJ58LP3VfrVelS3+VP7MML+UgtIJqJnsIy8Xp67Rp3qzw9VjZStfGAzeF3L0\nkz3sK68v+39p6+uc7pEGFL0UsIHlF1bP5GcFftkbES0Ble+Nry9iaf2akIvx\n6AT+\r\n=qFsT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/winston", "engines": {"node": ">= 0.10.0"}, "gitHead": "d9304b89af29edc9e90d466aee67dc6be3d82cfb", "scripts": {"test": "vows --spec --isolate"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "5.8.0", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"eyes": "0.1.x", "async": "~1.0.0", "cycle": "1.0.x", "colors": "1.0.x", "isstream": "0.1.x", "stack-trace": "0.0.x"}, "_hasShrinkwrap": false, "devDependencies": {"hock": "1.x.x", "vows": "0.7.x", "std-mocks": "~1.0.0", "cross-spawn-async": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/winston_2.4.2_1524243306014_0.7326485527894178", "host": "s3://npm-registry-packages"}}, "3.0.0-rc5": {"name": "winston", "version": "3.0.0-rc5", "keywords": ["winston", "logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@3.0.0-rc5", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "pose", "email": "alberto<PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "bc383d32b0e774d387a66e77290fe78766468f34", "tarball": "https://registry.npmjs.org/winston/-/winston-3.0.0-rc5.tgz", "fileCount": 36, "integrity": "sha512-BRYS7jsNkfLAqGu4dZW3kp6CmqiWKrComvfsIMYdsnpPre7g8BIw63nWRyX69vxyLYvZdszcEyxJkJmILXd/pA==", "signatures": [{"sig": "MEUCIHW8UZuzy8Nhq2ULP+qqHyWcHu0H7vRc7pLO1n9ZW4ggAiEAt2SVgvSQUlgNB6G0Wp/t8mmx/bBrkNIFORE65U5CWlw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 148880, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa2kMUCRA9TVsSAnZWagAAY+cP/3wY1LaKXW1fwXmqpDNe\nBCz2l1h7d5Oc1v3WThuYgjwbPikDDKjbedIfWA0SYHGnMQf4yDEGSRlyndaM\noYrLxRByJTgB54adVxB0MJ0rs+r+LeM5iR1wf/lVj6hhhQ3rBa826/BIHguU\ngG3jrqH8ZgxIfdi4j0Ry2w9NlGbGPDR0I6aDeqmeWx9sUGNNwkna7a/H4/Fy\n2wLmvWdPnR1d3Mwc5JVamOgHmOXwrwHm2gULnrtwT009ZVyrWgGmM0Q0kjsS\nM1Vli8epKiuUl6Rh63gUeXeX4GpNqMNf6cuWG7DfDG0VTQaAC2H7C3tEdaGn\nVz8TPl/lvzdbIg993taEE71CUOg2r8AkzTDeP/+QGvGX/XT+jLII0iIge6UI\nMMcHgUVdHQmXFQZre0oYpHE2biNsJRHa63xdshNy7+L1Sqy4etRDSwFvhrPm\n0TCteoJ+7E7jH7AEBeS4UjC4Q08OE82Evw39kQdDP3ij4usVb2QI6E+6LncH\nOO8skDcbwviHMhNCn0NYzxOT9b4jOSS9s+e4tSZBuYcH+zr6R99E6B8fl6UY\nAQSvaTyN9NoGQMNW2snX3ihJNVbP4C5uwCQ1u4Mql/gb8G/dyfC7lrh1G5MD\nox6cwa6DtbkSoyoCsRWVJPHRcz8NgBwyci+JFYeewD43TEOfylemPnKaawyA\nHXlN\r\n=JXWf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/winston", "engines": {"node": ">= 4.2.2"}, "gitHead": "1146e645df303e9645521322019f8d99ed66c18c", "scripts": {"lint": "populist lib/*.js lib/winston/*.js lib/winston/**/*.js", "test": "nyc mocha test/*.test.js test/**/*.test.js", "report": "nyc report --reporter=lcov", "pretest": "npm run lint"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "5.8.0", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"async": "^2.6.0", "logform": "^1.4.1", "one-time": "0.0.4", "is-stream": "^1.1.0", "diagnostics": "^1.0.1", "stack-trace": "0.0.x", "triple-beam": "^1.0.1", "winston-transport": "^3.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.2.1", "hock": "^1.3.2", "mocha": "^3.2.0", "assume": "^2.0.1", "colors": "^1.2.0", "split2": "^2.1.1", "through2": "^2.0.0", "std-mocks": "^1.0.0", "winston-compat": "^0.1.1", "cross-spawn-async": "^2.0.0", "eslint-config-populist": "^4.1.0", "abstract-winston-transport": ">= 0.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/winston_3.0.0-rc5_1524253458426_0.901113116870903", "host": "s3://npm-registry-packages"}}, "3.0.0-rc6": {"name": "winston", "version": "3.0.0-rc6", "keywords": ["winston", "logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@3.0.0-rc6", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "pose", "email": "alberto<PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "f6a0417be6cc2fe5c9a64fbb698e8da674cdf26a", "tarball": "https://registry.npmjs.org/winston/-/winston-3.0.0-rc6.tgz", "fileCount": 33, "integrity": "sha512-4QwLccPbU/aJMA+j6uYTQ8TobwLKPWhpceJZwz1lkQ+wYv7bxGyAHIdbfEOrxsKHD8zRCcgCXPbbpV3BDRD1rw==", "signatures": [{"sig": "MEUCIBbjZZ9zaBmiYjJU2F3+WSG0iH5Ougv+0w/NuLsy9BK9AiEAksiBA3e6sW9URJwe6n1jQV4KxD7fJpzx6iCq+yGI0II=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 167966, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbDj4lCRA9TVsSAnZWagAAS64P/3CTTzEmWAX51so5QXcw\nGLsW4Da281Nb6YiEsg+4wGIVm2aJ3Um2PsRbLmlvBBHCAOHKE2FxW2uZ2Exy\nchQfcOiCTwwT0cROGfdYMK1bXkLAHW6jdMhGNYxoKOr8Dyb+uXZYoYUsodLJ\n02vJX3BLXxnThD7QWgJOewbouBuxEfobonmG8vJ76qoUZ1BoMkINrShodhA+\nYGJsSXDoCqU2ge9Ff/wQmtpcVU7QFOmftJ/LVW2esBvuU1yoC8XOugqfXQjb\nudHyOKajymwIK8g7WbEujTzy/oY93dikg0kNUTZyhFYAS5mTT7NmQLRNhOfw\n1nZSh1mn2oEIA0mz/h3jitomeNLZWVqbWSC8Apu6nYwiPKqzI8pnO8kAorEJ\nX1hEARiDT33KuSy4l9igAAu77YcZbJl52tJpjoZi1JPUcB2wga/fy8kMa8in\nah7oyzFoVC8GmGGlTppCIYbgoyiEqHPT7TuIERQ1DYG7ZA3plGItPCZpawLy\nFwV9pDSe0YduC+3vIeKspw5BCP0Ms0vba/lilcaPwqob+1Wmig0xpp41l4Mi\nHXPQVKSUMPczQe9nXb5/GVKOeN1Y7SUQqDglH5jbmqwdR3MpUs/DgS6qXH8E\nNeKGfBNAR4OHXviPdIwH8aqL2HVk7I6Uv6+/gVFbY5toQ+ldb/z5Vok7sz8K\nUsiO\r\n=t0cn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/winston", "engines": {"node": ">= 6.4.0"}, "gitHead": "9971268017ab18710367bca22b211a998a08a118", "scripts": {"lint": "populist lib/*.js lib/winston/*.js lib/winston/**/*.js", "test": "nyc --reporter=text --reporter lcov npm run test:mocha", "pretest": "npm run lint", "test:mocha": "mocha test/*.test.js test/**/*.test.js"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "5.10.0", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "8.6.0", "dependencies": {"async": "^2.6.0", "logform": "^1.7.0", "one-time": "0.0.4", "is-stream": "^1.1.0", "diagnostics": "^1.0.1", "stack-trace": "0.0.x", "triple-beam": "^1.3.0", "winston-transport": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.2.1", "hock": "^1.3.2", "mocha": "^3.2.0", "assume": "^2.0.1", "colors": "^1.2.0", "rimraf": "^2.6.2", "split2": "^2.1.1", "through2": "^2.0.0", "std-mocks": "^1.0.0", "@types/node": "^9.6.6", "winston-compat": "^0.1.1", "cross-spawn-async": "^2.0.0", "eslint-config-populist": "^4.1.0", "abstract-winston-transport": ">= 0.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/winston_3.0.0-rc6_1527660066136_0.0013855148606916057", "host": "s3://npm-registry-packages"}}, "2.4.3": {"name": "winston", "version": "2.4.3", "keywords": ["winston", "logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@2.4.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "pose", "email": "alberto<PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "7a9fdab371b6d3d9b63a592947846d856948c517", "tarball": "https://registry.npmjs.org/winston/-/winston-2.4.3.tgz", "fileCount": 3557, "integrity": "sha512-G<PERSON><PERSON>uysPz2pxYAVJD2NPsDLP5Z79SDEzPm9/j4tCjkF/n89iBNGBMJcR+dMUqxgPNgoSs6fVygPi+Vl2oxIpBuw==", "signatures": [{"sig": "MEUCIQDVFME5SqwNlNS2w39PeJ5iST3rskNIzJlijGW9iAASzgIgF6+JHBa9lEZB1ukWFFUhT5zrmfBGwQ+/kGGk0roHkZo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5985515, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbH5gBCRA9TVsSAnZWagAAjBkQAJmMB+VBw/VEif40VXa1\nH3ANivrsZgJ177AImlFbJACULaKxravnHI0wnS1rLxcQJqc5fWLamWy4Fxgr\nyg+xZ2gtoAdll12BB1rkrvKZsjvutG9f6KbYmy9N7bHKpuojespzb/lSienz\nd8YMCxfNj/jGv1hPg+uWVUVR/7bkHOErrSTbu5fR467n3n9Uvqja67rGElXk\n8BFtDJLMzYUjGnTo9epHnhHswJ+g0YCy3uSu8c9lrWdMjPLyjeWPE04mZ2Mz\nNg1vyKMCianZ6n0vwm//Ip1XKIfo2wJ6WoR1xhnhxXnW9maNEDfSSlHPjXkM\njH9iEr9PoUfXUEleo18DmoS33iLa6ItOdWx1JO46aki5p/OoliZbLdZlAUFr\nSIdQ4qZ5HdERu9gxElZbRUGiRofBobQLilgDPViiE2pkiYAB1xXo4D4nToxX\nUZU06piIHFPhIqAL1520dUlIrpIXqSd7wUu9dcLiTPx/PwejAy4F+8aLib2g\nqxbh2Rf8nYj6W4cxGf6E8Z1jlM9QRhzd3g0WEj9fqsgy7XKLG7wrVNKCR3JN\nasvppN62sspVgW3/zHRx9vma6QexOYYAUlAa44MXMQfNb37S71sBcSzIYPmi\ndARkNp7Q9arDg+Hz6hP2ZQ370qGtiAmCoRJttcAMqiqaxa6ku4J8G7MsvplA\nQK1L\r\n=KLNW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/winston", "engines": {"node": ">= 0.10.0"}, "gitHead": "dc74db60b8d46475fce04bab1e0c31abe5201e09", "scripts": {"test": "vows --spec --isolate"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "5.10.0", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "10.3.0", "dependencies": {"eyes": "0.1.x", "async": "~1.0.0", "cycle": "1.0.x", "colors": "1.0.x", "isstream": "0.1.x", "stack-trace": "0.0.x"}, "_hasShrinkwrap": false, "devDependencies": {"hock": "1.x.x", "vows": "0.7.x", "std-mocks": "~1.0.0", "cross-spawn-async": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/winston_2.4.3_1528797184091_0.5220518102634675", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "winston", "version": "3.0.0", "keywords": ["winston", "logger", "logging", "logs", "sysadmin", "bunyan", "pino", "loglevel", "tools", "json", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@3.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "pose", "email": "alberto<PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "1f0b24a96586798bcf0cd149fb07ed47cb01a1b2", "tarball": "https://registry.npmjs.org/winston/-/winston-3.0.0.tgz", "fileCount": 70, "integrity": "sha512-7QyfOo1PM5zGL6qma6NIeQQMh71FBg/8fhkSAePqtf5YEi6t+UrPDcUuHhuuUasgso49ccvMEsmqr0GBG2qaMQ==", "signatures": [{"sig": "MEUCIDgHYZJ7qkNtG2r1iOFrQmtOHY2g4FwdlTAaEafAiovTAiEAzOJNqXsyrenQf7ytpuBYYhO6mSziicn6KZWjFJVajj4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 611998, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbH/VgCRA9TVsSAnZWagAAmMYP/1Bz9tGx9E+c18763TdH\nxqtMo5FFauSdwEWsRaYVXVZDWnjlWektf/tbD1kjdEM5Vx+CN/yKmL4hnnx1\nNI6nFaIpbqCh+Q5ZpqPMYxzXRYq+iHvK5Rs194O+8YHubIjnW16n7XuQf7go\ngSXbfF0bKmkdQ0lIQ9jveKcVDLck6Xl3P19aEfo+wVLzN+4Sr5RySjoP2ecm\nxmU7razcHiK5magTpPdVrD8zF1CZeFELm3hPNNaO43iVZtvJclxbMqp7F5LG\nW1GxfMAbCAchrfymzFF2VzCx1++f2HEhZYXCAg6CgV1Vp4SHX8fChKuXemHF\nHdx4XfCccwZWDFdlVtrnz5CcJC1Jx0o37FyfSzX7MAFZ3cGvB6zxvJAqP8Jy\nPT5BOSYfdVRCgi5QObfJykHrP5SJDUB8uslFRljcwNcY7FEgo+NP9uMIuh/l\nbe0HAESm5YxXe9ZanEjEfzf8G3Bsy5Sac9m5zxTWW69LKHrAZkF2pVpFb5Z1\nFTsgnOHa6Ab50FMsJlSj8RhRBuFRXiDCJDLQPzBbuQ1tUfJO+KrtWLdyeHre\nlNE+w2gY8F3BPWkh4yODrB9FVvyvdBnG1qW5+fM1C9hmkYVTyNcqHmGrnuhM\nWbZvaismFNhPMoO22HFCrOtLlWkaU5IWfdTA1bRWi0KjBcH44g3sr8pnv8y+\nIohh\r\n=0S99\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/winston", "engines": {"node": ">= 6.4.0"}, "gitHead": "8be746b4fba623c7167420c887ee9cf3d4147664", "scripts": {"lint": "populist lib/*.js lib/winston/*.js lib/winston/**/*.js", "test": "nyc --reporter=text --reporter lcov npm run test:mocha", "pretest": "npm run lint", "test:mocha": "mocha test/*.test.js test/**/*.test.js"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "A logger for just about everything.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"async": "^2.6.0", "logform": "^1.9.0", "one-time": "0.0.4", "is-stream": "^1.1.0", "diagnostics": "^1.0.1", "stack-trace": "0.0.x", "triple-beam": "^1.3.0", "readable-stream": "^2.3.6", "winston-transport": "^4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.2.1", "hock": "^1.3.2", "mocha": "^3.2.0", "assume": "^2.0.1", "colors": "^1.2.0", "rimraf": "^2.6.2", "split2": "^2.1.1", "through2": "^2.0.0", "std-mocks": "^1.0.0", "@types/node": "^9.6.6", "winston-compat": "^0.1.1", "cross-spawn-async": "^2.0.0", "eslint-config-populist": "^4.1.0", "abstract-winston-transport": ">= 0.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/winston_3.0.0_1528821086783_0.3629993700749554", "host": "s3://npm-registry-packages"}}, "2.4.4": {"name": "winston", "version": "2.4.4", "keywords": ["winston", "logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@2.4.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "pose", "email": "alberto<PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "a01e4d1d0a103cf4eada6fc1f886b3110d71c34b", "tarball": "https://registry.npmjs.org/winston/-/winston-2.4.4.tgz", "fileCount": 32, "integrity": "sha512-NBo2Pepn4hK4V01UfcWcDlmiVTs7VTB1h7bgnB0rgP146bYhMxX0ypCz3lBOfNxCO4Zuek7yeT+y/zM1OfMw4Q==", "signatures": [{"sig": "MEQCIAs4GquE5h50K6LrspFrVdqyZnjP2MlWv9XP215zKJwVAiBkrEWXhN80CvQ+Qpd/opca8DN27nG2ahSnN4YzAvld2g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 189862, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfIKACRA9TVsSAnZWagAALMsP/2Ua2w4cNGpHS2Y7ju3v\n5GR/ylqqVvZIiZyWBMV4bXNIHYnEY3U+EBv9knr7/lJqvNNiD9dQl8mx7b9R\ndLVeaLb3p1xCq5yfJcbx2fJ20x8g1dgqoVGVLl90+NsHHUr6HHLbryDVPjDM\nlt/xQflSjUOtYZV/o7h0/cUanFtKnOt18nWxepLG13dYyyegIlsAqs91zGN4\nbsaj0US7c2PWAH9bTQaTrzquNwUfOK/teTCEdn1kcUrPWMDi7cxveSqwLje4\n5vqpXKqs1T474uyAvM28onpmfFtA5wTyGkUOQoa9wW5nh3pI/ky1li2fgdI3\ncULGXTQcprDncArwDEuc+qwHUKv8Fusdipj1YZ5qkmi/u14GlzqzUQXb6N+N\nqmBE0cgRaH5H7hskKv69bFKeFiapZ43T9Hw8DosQbC7ci2VW0mYXy/VFiPQD\nOVyijqFs9be74t7WcOB3WmnKZsVQUxHlrMiYRllYySmNSk16LlpSVk+tYEcu\nXmScIAbXZLvMy0aFzq8KlQDDelJEOAQdQ9Jm3C7/TPhU9vb9yeQRWARMJpIe\n2aFZSXXbEu+j1IuAyfF6rUmMLpy7XCHWAil87UN69vdHydw2r4wqxCur9494\nD91rw1rA4ziFUcljNdE0tns76sy7lCb8n77Hb78ocqyGAvCUUJAwG4zFkd1f\n5//V\r\n=k7FV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/winston", "types": "./index.d.ts", "engines": {"node": ">= 0.10.0"}, "gitHead": "67c44ffe1f9db9305a1bc562e0af1a0332588b15", "scripts": {"test": "vows --spec --isolate"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "10.3.0", "dependencies": {"eyes": "0.1.x", "async": "~1.0.0", "cycle": "1.0.x", "colors": "1.0.x", "isstream": "0.1.x", "stack-trace": "0.0.x"}, "_hasShrinkwrap": false, "devDependencies": {"hock": "1.x.x", "vows": "0.7.x", "std-mocks": "~1.0.0", "cross-spawn-async": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/winston_2.4.4_1534886527464_0.7641525117081251", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "winston", "version": "3.0.1", "keywords": ["winston", "logger", "logging", "logs", "sysadmin", "bunyan", "pino", "loglevel", "tools", "json", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@3.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "pose", "email": "alberto<PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "ac2196df24bd5ffa7217ebf287eede455f0f2de5", "tarball": "https://registry.npmjs.org/winston/-/winston-3.0.1.tgz", "fileCount": 33, "integrity": "sha512-Qp0hlqi7rR59K8CyEu8+CLNM9vjJjfq8zf90RkS/osirjdMPeGlqYcZXcGNVvghFUr7ztW1U1FXAcolBNs6T4Q==", "signatures": [{"sig": "MEYCIQDkZDtT/Y2tt7OW9fxBOiIhWVC9L9ANg12jkLLNiJYAIwIhAJdpiua9xIgdBKu3yCr6XckHFaet8YUFdR1bsNonAFQe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 166181, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbjpEcCRA9TVsSAnZWagAADr4P+QFinKE/pZLF9kSnVGQd\nmIE94hILgioDKYDLGZ0wChUjmvuPlXAp4oZe6nDx1AFwZnPayDcbtYYSldaW\njIo5M93Dr9N3HFgV53o9Z/4CkyAhUWfuBMEVKzN8vEOUseXs4O8YPvvVhQTx\nnITd6jZoECu+Li7WgPaULw1knIaUa1H0j3tyGe9apdw7g9Ud3uaFJlAc+5Uj\nowCHOyMaXKEwV3a6fw3/oClpZG/R8L23EgSiRV1hq0tBXMSP/eX7+VunpnV4\nlVGTOJUoYRmHQll3LmusqHkueh2fq12jiVAg2l1wMdYzC1/UXeomxCbIz6d/\nhgMe6KgYS/LbJut3iG3YuvMNyh9KXJ6O+4nloprx9hkuaGzb6DtV/uoC7n1w\nYczN9XU0vHdxQ2SVDkl+4xSBZEQ2jZkPC5bchcTbuEEGVCqV2E2BGVhFD/vG\n9UtvcUzeYTY3iE9UxwAsmgwUXvFhJU+PCG8+wsGHUjg9XBvp+QxRSh70YSnv\nI+FWdYS5MufarjapDqGLjue5PoRjMnBzVW89gfh9d9QzhwPJwBxENNoeqJMq\nmm07szGCliKLUGi5pryuRf2+MwccQEIITZqjS7ktmHlnhVsZ9w+M1UbowDbf\nFccMBtjuNdhzioEXLzPT1Zzo3vm2uZMOIP/rkGGm4c/iJ1hjus4Zeb5Fx8kT\nMVqZ\r\n=zJfA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/winston", "engines": {"node": ">= 6.4.0"}, "gitHead": "d0a70d3b2f4492d9375a1351a191480a364cc28c", "scripts": {"lint": "populist lib/*.js lib/winston/*.js lib/winston/**/*.js", "test": "nyc --reporter=text --reporter lcov npm run test:mocha", "pretest": "npm run lint", "test:mocha": "mocha test/*.test.js test/**/*.test.js"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "A logger for just about everything.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"async": "^2.6.0", "logform": "^1.9.0", "one-time": "0.0.4", "is-stream": "^1.1.0", "diagnostics": "^1.0.1", "stack-trace": "0.0.x", "triple-beam": "^1.3.0", "readable-stream": "^2.3.6", "winston-transport": "^4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.2.1", "hock": "^1.3.2", "mocha": "^3.2.0", "assume": "^2.0.1", "colors": "^1.2.0", "rimraf": "^2.6.2", "split2": "^2.1.1", "through2": "^2.0.0", "std-mocks": "^1.0.0", "@types/node": "^9.6.6", "winston-compat": "^0.1.1", "cross-spawn-async": "^2.0.0", "eslint-config-populist": "^4.1.0", "abstract-winston-transport": ">= 0.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/winston_3.0.1_1536069915681_0.515383666810193", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "winston", "version": "3.1.0", "keywords": ["winston", "logger", "logging", "logs", "sysadmin", "bunyan", "pino", "loglevel", "tools", "json", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@3.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "pose", "email": "alberto<PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "80724376aef164e024f316100d5b178d78ac5331", "tarball": "https://registry.npmjs.org/winston/-/winston-3.1.0.tgz", "fileCount": 33, "integrity": "sha512-FsQfEE+8YIEeuZEYhHDk5cILo1HOcWkGwvoidLrDgPog0r4bser1lEIOco2dN9zpDJ1M88hfDgZvxe5z4xNcwg==", "signatures": [{"sig": "MEYCIQCVAwnH9jaFMKc+p/Bn+6HiVvg0z9wPKZcBJFzss9ESFgIhAMY2dkEE2urW66akOP2YZmBHiH5t6C8ECBBuGQyp9+dw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 169945, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbjpHDCRA9TVsSAnZWagAA/y4P/iNRGRsyG/Lwu9AvVSF/\niunubPBHVd5lNph2Qu+caFQyJKcDdsEuIJJG6+ellpBBNhSxwa9KYbKdysha\nm9izU1dtyeDzbHiVHDvVTknO8f1anScHkW+u6zv05X4FYYfo6Vqls8fDwieD\nhTxPmmz1eIECNgfQsVh4XiDgU/dNNh2u96uYyfrNNhxAxEU94sJICAe+XCqX\niE7ARqIz5PAN5i0SBHPeSBZ1TuxzbroPVH+q8IpOwQ+G02BQTMzTP/CX9Axq\nG1+5P35zGbD+g9l/7C4+iMxbAB1zfz4EpAKuDYeySNM1Y91fleWglQooRfvh\nFZ3tofhNDB5fXjw9TRwB8t64GjJQ1ifihadMN5P+jwX8oZwdhd0mbn27u51m\nfO9RQ/iRzrXEgWGQOPtiwb2EH05s+G2iboiWS6YixDNgivo+Tr7tcjI2HO7w\nIFOJfIaCZdYBTfttF7HVEiwFHTiWX5etsMG2DhUsTDf+O4TcUlNBOg+/V3NM\nHU2NLjJkCHSh9/JSFNnnoXh4BVeAWoZr8MfKzwj9E+1gJMYA4ULbDm9vFCzW\n8vNmpgcxtYQQF62gmJXbdqdm1IHrhCjJu+W2qHJDhYCQUadnUJHm1ZH7pbSY\nUlbRq7oGGZYI5Jdoe0z73A5YDvO/vjCUqcQHH496hkXpkuKDnI2XFPEGThXs\np8kl\r\n=z1yK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/winston", "types": "./index.d.ts", "engines": {"node": ">= 6.4.0"}, "gitHead": "9eda806b3d0d95ce127cae12e9df45c2570bd253", "scripts": {"lint": "populist lib/*.js lib/winston/*.js lib/winston/**/*.js", "test": "nyc --reporter=text --reporter lcov npm run test:mocha", "pretest": "npm run lint", "test:mocha": "mocha test/*.test.js test/**/*.test.js --exit"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "A logger for just about everything.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"async": "^2.6.0", "logform": "^1.9.1", "one-time": "0.0.4", "is-stream": "^1.1.0", "diagnostics": "^1.1.1", "stack-trace": "0.0.x", "triple-beam": "^1.3.0", "readable-stream": "^2.3.6", "winston-transport": "^4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.0.1", "hock": "^1.3.3", "mocha": "^5.2.0", "assume": "^2.1.0", "colors": "^1.3.2", "rimraf": "^2.6.2", "split2": "^3.0.0", "through2": "^2.0.0", "std-mocks": "^1.0.0", "@types/node": "^10.9.3", "winston-compat": "^0.1.1", "cross-spawn-async": "^2.0.0", "eslint-config-populist": "^4.1.0", "abstract-winston-transport": ">= 0.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/winston_3.1.0_1536070082953_0.9601566695222759", "host": "s3://npm-registry-packages"}}, "3.2.0": {"name": "winston", "version": "3.2.0", "keywords": ["winston", "logger", "logging", "logs", "sysadmin", "bunyan", "pino", "loglevel", "tools", "json", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@3.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "pose", "email": "alberto<PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "f42735b194a425526025d3ae721246c796172f24", "tarball": "https://registry.npmjs.org/winston/-/winston-3.2.0.tgz", "fileCount": 51, "integrity": "sha512-r2e2ufodByh8U1infSXNLViN7ekqVRoSkcJgpS6AzAyKve0uiUkeQq0kxdSDr8bwaM1rGXprvvoC1B+ocy5L0w==", "signatures": [{"sig": "MEYCIQDtYBAskSdsfTJqDrKkxk5nAsu+xnCA8PUzPlZmuYzpxgIhAP9Fjon+OZPhjqktR4eOlzCexl6kHEFBQ93YpQNptplT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 304548, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcTLmhCRA9TVsSAnZWagAAsL8QAJrJmOwspEPAtoncdKPB\njAEG9EMzpkKazl68/yLJib+zRz/JDqaH4qMWzCzbZbs3H0fcSki3rJR11hqs\nhqJ6Lj5ruUEsmYe3S4jzAS1Asdnst8HwRNDVgT3EeGXxuHy3HEBP0ju+CG/1\ncG9cicOmrzuRhd2xJ6kIQ7yPmXJ8tQ0s05n8AkonUBN3Ug2R6av1ER3TafB9\nbfGKmaQ/mBpz419z6n7MjIRLbOq05d2encdSEBLBEAn/p0bsYtiQ1ScHzskE\n1aU2rXxuIpPNdYKvZ2kQdFqSzJN1UGL2QhyJr5fYGl8R0ZB77AYR/9ZOewXo\nP8y8rwr44IR0A0jTqaMnlSGIMhDzjjeuNCwcG2d3D0jjkvzsbFvj1xyy4JhT\n041+DzV/RVPQvW1coUqnwRgAk/9aWcqdwivnwKAjYq7H9Vw6kQsnnQm66S8Y\nXdxGoDzAcq5Jz6/l9Mi/zD6YlLB2RjFWWSz9CkpbyP4Pv0eOCFYw1xyoFsVl\nzJh/tc6wkVPd+yxTbJrnyjeB9YCX4scqfyAE0LGBUyLFolvycl34CN0FJruE\n/Eg4ANLlKPcBlbUm8lizDn21z3aYLkR89FAWgQ7+eiNJ3FOlE0kcOByEzYWw\nCVRHExjk+D6U5GRO1z/MdtJzE+9wOfzKmaZ41sDYVGvR07JpxbztJCfADAHu\njq31\r\n=ATOG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/winston", "types": "./index.d.ts", "browser": "./dist/winston", "engines": {"node": ">= 6.4.0"}, "gitHead": "fe76c66d5fdf4524b6e310b77e64e28dca8bf675", "scripts": {"lint": "populist lib/*.js lib/winston/*.js lib/winston/**/*.js", "test": "nyc --reporter=text --reporter lcov npm run test:mocha", "build": "rimraf dist && babel lib -d dist", "pretest": "npm run lint", "test:mocha": "mocha test/*.test.js test/**/*.test.js --exit", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "A logger for just about everything.", "directories": {}, "_nodeVersion": "10.15.0", "dependencies": {"async": "^2.6.1", "logform": "^2.1.0", "one-time": "0.0.4", "is-stream": "^1.1.0", "diagnostics": "^1.1.1", "stack-trace": "0.0.x", "triple-beam": "^1.3.0", "readable-stream": "^3.1.1", "winston-transport": "^4.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.1.0", "hock": "^1.3.3", "mocha": "^5.2.0", "assume": "^2.1.0", "colors": "^1.3.3", "rimraf": "^2.6.3", "split2": "^3.1.0", "through2": "^3.0.0", "std-mocks": "^1.0.1", "@babel/cli": "^7.2.3", "@babel/core": "^7.2.2", "@types/node": "^10.12.18", "winston-compat": "^0.1.4", "@babel/preset-env": "^7.2.3", "cross-spawn-async": "^2.2.5", "eslint-config-populist": "^4.1.0", "abstract-winston-transport": ">= 0.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/winston_3.2.0_1548532128487_0.3182652549505156", "host": "s3://npm-registry-packages"}}, "3.2.1": {"name": "winston", "version": "3.2.1", "keywords": ["winston", "logger", "logging", "logs", "sysadmin", "bunyan", "pino", "loglevel", "tools", "json", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@3.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "pose", "email": "alberto<PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "63061377976c73584028be2490a1846055f77f07", "tarball": "https://registry.npmjs.org/winston/-/winston-3.2.1.tgz", "fileCount": 51, "integrity": "sha512-zU6vgnS9dAWCEKg/QYigd6cgMVVNwyTzKs81XZtTFuRwJOcDdBg7AU0mXVyNbs7O5RH2zdv+BdNZUlx7mXPuOw==", "signatures": [{"sig": "MEYCIQDWCtebsniBsKJQO08pvJxbIYLOqF7/ZNsXFO2ACNdM1wIhAO3EfGO7MYaebvjsLtH+gt052IUWC4LG9sz3Ma0h7xMg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 305100, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcUKOsCRA9TVsSAnZWagAAkJEP/ideEmSeWCrhHt690NJV\nY4nM4M75zS78j9gGKfzqB/Wet390K3OGc7UZyCjb/GpwmCnFWlQ75LnDKfQv\nrlAvU9OXJo1Zd/7gAkE+2Uhlzeg3KkkcpG97tblcPXcWgJIil6NkViFlg0LP\n+Xpbj0LIjwBOyoi927TyED0r9x7wbPnnoTiCxdkYQNi1q40D+1xW2rVYg2oO\neHF3FxGMlzKjh6aRIDhS4oK3gX8QE9HNyQsR74P1oy501S6YGruIbq4SvZHS\ns6KRhyviuM7JfQCjpfa9mXYH75sZYrzNyEDYgi3tgQx+SKwH50MaySfbHnoH\nme0HYREwey5TMDmCYeY3XehSQqygGbGjT1pIjruLbFdyouDqJXI7IJeztnEg\ncs0d/OihRXli+K2/RGKkaIiKWAxoG8cml9z8wlJgTmYb6SqZa5KmvF2Wvpzz\n1x/mg674P/8WZ1ZYAA5RTwhYuBTzlMx2qrOsvjBjO5Q7PSzAV7pMIoyFefdE\nVLR5G/3u28HESTEyfiEhEcgQ75qJNjdmpDiuz6DPE99pyMY4/rVQTUhJJRXo\nerUHugmEh0lC1d0tueEW26ONXPwlromsJZOvyi8E7oofGkXddF9qZ6JaBejf\nEef820R0yMfkIPYx9cQkRmjOZ3xS/uWksOtFJgpxJmE8RQI1cwhgpjDC83Fs\n7vOB\r\n=sRVW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/winston", "types": "./index.d.ts", "browser": "./dist/winston", "engines": {"node": ">= 6.4.0"}, "gitHead": "49ccdb6604ecce590eda2915b130970ee0f1b6a3", "scripts": {"lint": "populist lib/*.js lib/winston/*.js lib/winston/**/*.js", "test": "nyc --reporter=text --reporter lcov npm run test:mocha", "build": "rimraf dist && babel lib -d dist", "pretest": "npm run lint", "test:mocha": "mocha test/*.test.js test/**/*.test.js --exit", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "A logger for just about everything.", "directories": {}, "_nodeVersion": "10.15.0", "dependencies": {"async": "^2.6.1", "logform": "^2.1.1", "one-time": "0.0.4", "is-stream": "^1.1.0", "diagnostics": "^1.1.1", "stack-trace": "0.0.x", "triple-beam": "^1.3.0", "readable-stream": "^3.1.1", "winston-transport": "^4.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.1.0", "hock": "^1.3.3", "mocha": "^5.2.0", "assume": "^2.1.0", "colors": "^1.3.3", "rimraf": "^2.6.3", "split2": "^3.1.0", "through2": "^3.0.0", "std-mocks": "^1.0.1", "@babel/cli": "^7.2.3", "@babel/core": "^7.2.2", "@types/node": "^10.12.19", "winston-compat": "^0.1.4", "@babel/preset-env": "^7.3.1", "cross-spawn-async": "^2.2.5", "eslint-config-populist": "^4.2.0", "abstract-winston-transport": ">= 0.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/winston_3.2.1_1548788651547_0.18299038067022044", "host": "s3://npm-registry-packages"}}, "3.3.0": {"name": "winston", "version": "3.3.0", "keywords": ["winston", "logger", "logging", "logs", "sysadmin", "bunyan", "pino", "loglevel", "tools", "json", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@3.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "dabh", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "pose", "email": "alberto<PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "89a5c20a647a9186ad456b7c5a561bb887eae305", "tarball": "https://registry.npmjs.org/winston/-/winston-3.3.0.tgz", "fileCount": 39, "integrity": "sha512-S8qAM7Nhuqr6THMt5xcO49xgbSpjoNIm+fOoxAp6FOJd5nBRayVZ3jAwKrWYQ3KS/glXqOd+0xGCYvMMNaL2RQ==", "signatures": [{"sig": "MEUCIBY0ydBCh5vYh8KXhnU2PmT9TkHkddwEiSgp0GRHMnP0AiEA7ruPBbFF2lQDEu+0gtzE5HdvzokkW8Rf5bzFRa5WHTs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 285575, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7x/GCRA9TVsSAnZWagAAVtIP/A+hfBfp8VE8HuhuHPtT\nUxapHs07rmCxewnPGT2d1GpKkP/838IE01z76SqdC2zmRXgH4ZI+cicLsCcs\ncikLuoc1SmcqPOnKuYb88HAtNLcwue8cos3trQmnMKttwGWS6Ygrghilb8Vv\nsbZaUoF+cYXSv1VvS6dYBQvhaRcxaiN1EjgYRb4ib2tWLLGlyzLcQLsFtXrV\nIJ4/Lg/4VtcMRJWitpEjqzZt6Yco/tR8xxxzSXJMYM+eIwYC6xYMV/zb/Wa2\nyjSV9G/OoHlYG9Z05NFK3pD1V9yGaDQst9U4i6XKFRdcQsCYCAdlt2Cn+0qg\ngn5zVsY0WbI0szGtEDEHrFBbzdHO0VuSeiWdP4eH3H9TffCUKwQNID0QdVyU\n2W/QGyzZrEAAMF1tbkXSSaMcovv4QaVZmZR/Oqxzu4S//QdyN3JjerlBGI+U\nNpUZxSW9IcUdOfQQTPn/ga3VYKHbkKg9GUM0wNduRvkrWP8w8wExB/pRAvxy\nfok4cCq3AtycfgMk/3mbVyI9wDk9q8nnby411255zRti7KsTMTPNMXRS/AoK\nGgTYp1DM17JvRuNGGZ4EmSeDA1bZ8Rkz0xKFA+OZtymiJnGSd0asp+cs8yuk\nD4klHhJyP1YXKWopeNY6oWNTOHAgOYxsCVc4VegSeQmK0Lx29cdzvdastZAR\nSfOC\r\n=8zjH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/winston", "types": "./index.d.ts", "browser": "./dist/winston", "engines": {"node": ">= 6.4.0"}, "gitHead": "b47d5d5def8ef00603f864bfcfbe643ab9ef2ce0", "scripts": {"lint": "populist lib/*.js lib/winston/*.js lib/winston/**/*.js", "test": "nyc --reporter=text --reporter lcov npm run test:mocha", "build": "rimraf dist && babel lib -d dist", "pretest": "npm run lint", "test:mocha": "mocha test/*.test.js test/**/*.test.js --exit", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "dabh", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "A logger for just about everything.", "directories": {}, "_nodeVersion": "12.18.0", "dependencies": {"async": "^3.1.0", "logform": "^2.2.0", "one-time": "^1.0.0", "is-stream": "^2.0.0", "diagnostics": "^2.0.2", "stack-trace": "0.0.x", "triple-beam": "^1.3.0", "readable-stream": "^3.4.0", "winston-transport": "^4.4.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "hock": "^1.4.1", "mocha": "^8.0.1", "assume": "^2.2.0", "colors": "^1.4.0", "rimraf": "^3.0.2", "split2": "^3.1.1", "through2": "^3.0.1", "std-mocks": "^1.0.1", "@babel/cli": "^7.10.3", "@babel/core": "^7.10.3", "@types/node": "^14.0.13", "winston-compat": "^0.1.5", "@babel/preset-env": "^7.10.3", "cross-spawn-async": "^2.2.5", "eslint-config-populist": "^4.2.0", "abstract-winston-transport": "^0.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/winston_3.3.0_1592729541628_0.39883282746747417", "host": "s3://npm-registry-packages"}}, "2.4.5": {"name": "winston", "version": "2.4.5", "keywords": ["winston", "logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@2.4.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "dabh", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "pose", "email": "alberto<PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "f2e431d56154c4ea765545fc1003bd340c95b59a", "tarball": "https://registry.npmjs.org/winston/-/winston-2.4.5.tgz", "fileCount": 32, "integrity": "sha512-TWoamHt5yYvsMarGlGEQE59SbJHqGsZV8/lwC+iCcGeAe0vUaOh+Lv6SYM17ouzC/a/LB1/hz/7sxFBtlu1l4A==", "signatures": [{"sig": "MEYCIQC+XgsBatlvp/mkNhFun19iW1ChDdlv92sSsct5ghGmFwIhAN/UGjrByzLKvCRCxLBhfE/YGoAp7+wxMhmNpOTDbcI3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 190049, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8E0zCRA9TVsSAnZWagAAcIUP/jjPSaKkIwsXDd5X93kX\ndr6nq7h1KyXtqrVv1SOoQ/RZ1mfG65Rr7O4NiHCHSp3qzbIbSjSwDXVZrY5M\nqDIknyRWYg5ZsxdM+wg80R68EwvuShO61BDY2WJOqq/5f4SJtvsFOo8B5qJR\nVsoWLiMMIee+eqoiuPq+9LsZDlfcVacPK1blMry9vxeCGRCsGIxvGzU2V786\n4cKLSNdFnFXpwUpU7GN5h/ISU0NIJ4ioIEmtTlhwHJTCBk7vwBAafHNWDuJZ\nUXm2XvEemeFHQIRVJi1SkXUrhK7JCtWA6O4E5mVB08qSLLiTkgV+oSRgj1eQ\nlJ7b5OF1/42gVPvnpqIsaSQQgpTtd6+0DAM790UrCXTT/CEp3d2WQWNSFjq7\n7z/9GruHtw7PQ9CVJPxWSWTLtgoNOh/Z94ferjj0p7IFxVkYD22hbDmWE0Xf\n4W4+01CM+4qNfw79Jxzln9ZDXwkeOmxtTr5eYNueDlAYaeFZTbphCNvM8ynf\nav4vaefpvpQkUuah265JKowyTs7emBB8ouxkkgtiPd6cbYS8IzTG1VJQKQox\nyeznXoklvJayUFDKO0n4YtKI4QubEOvrJMonn9csc8G06BDPjeEtMNPbyFSZ\npClBEOmpvdPaTODJmbprRsxfE7YOZ8Rs0xf1uAxyH+mitL8Wl2yVneCXag68\nkH56\r\n=Oo/G\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/winston", "types": "./index.d.ts", "engines": {"node": ">= 0.10.0"}, "gitHead": "52060d686ec54ac089ab5fdae219cb1549aed925", "scripts": {"test": "vows --dot-matrix --isolate"}, "_npmUser": {"name": "dabh", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "12.18.0", "dependencies": {"eyes": "0.1.x", "async": "~1.0.0", "cycle": "1.0.x", "colors": "1.0.x", "isstream": "0.1.x", "stack-trace": "0.0.x"}, "_hasShrinkwrap": false, "devDependencies": {"hock": "1.x.x", "vows": "0.7.x", "std-mocks": "~1.0.0", "cross-spawn-async": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/winston_2.4.5_1592806706622_0.7366886828848345", "host": "s3://npm-registry-packages"}}, "3.3.1": {"name": "winston", "version": "3.3.1", "keywords": ["winston", "logger", "logging", "logs", "sysadmin", "bunyan", "pino", "loglevel", "tools", "json", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@3.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "dabh", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "pose", "email": "alberto<PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "866764c2af1e4bba860091e2692545f55ea98003", "tarball": "https://registry.npmjs.org/winston/-/winston-3.3.1.tgz", "fileCount": 39, "integrity": "sha512-ijjJtGl8tqQzftLysZn0jQBwa1VjyIrgysvk9tJJczk88oXmXZ5z6CvSFcQ69FfXONINxgIVfx3lqwjK57Hfsg==", "signatures": [{"sig": "MEYCIQCKXGqhBBdfkhqLEZJ6y9x+iWYshNe2Ob9UTEdi0HUIbgIhAJg7TAktMSd309A0ssnjROE6kZD69+26Fy6BWLAkYRVe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 285846, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8E/KCRA9TVsSAnZWagAAtiQQAIuYmfWnIltUa/K3lkVU\npNIYxuIaSRoQ9asIrDFbPaJ0ILHI9qCyko0UUcMPqgLmcbFArPL2hbjisuHB\ngd4BynNr7DDGB5Xw5kwPPL3AXJYTDf1MARZStu7AW28Rk8bRZL15mhhbbtgS\nHaCYBiq5OAX0xvNZuC/Al2cpLmDKCiV4sckDBZPxszRz/MD/7G5pAka8u2uq\nqf5o7ApIkSmNUkNKJpUsiyN8wU1FfW7pitm2sT49sDJD55bluTAx6ELJF0GT\n55tHryiKwwtbJd19eh3tCvt4JMNmsDI6W0q/6AF/kwQdpPj2qzbkVvMQDguZ\n5WxerChIpoNzaEpFnlHqFT3Uo9vLhrPNtlLtPAOFBasVf9c21habk5u1EOIq\nP61HDXk4CbYQnSzUzn/Crhx/i+/8q/5mVhdrKXhXD3zBJFFx63Tlku6U3PSG\nA5TRau7z2SuPE41Rf24Lfwa0W9qAxNRBaPsjGakyzv4BADUbL82DCqBZ8xmy\nfehmj0SKwnFHlaUaxTlbGk/ixvXC6MB0vQawmD9eTnbRwI1aESSdsPCpH2xt\nTyQmprgZgKPMOCFnRgzKxTAAeWUmbGiZCOK05o59nikvz7sD0+necegFfewG\ngFIXJnoY9emyn9GFX8NNTPdR9H75RbuqU6xagJFuBBoxXH9GOc6BIoskehMv\nzVAw\r\n=Zx+2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/winston", "types": "./index.d.ts", "browser": "./dist/winston", "engines": {"node": ">= 6.4.0"}, "gitHead": "e364ddcf993ac2dad733025188922aed82ef0a4d", "scripts": {"lint": "populist lib/*.js lib/winston/*.js lib/winston/**/*.js", "test": "nyc --reporter=text --reporter lcov npm run test:mocha", "build": "rimraf dist && babel lib -d dist", "pretest": "npm run lint", "test:mocha": "mocha test/*.test.js test/**/*.test.js --exit", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "dabh", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "A logger for just about everything.", "directories": {}, "_nodeVersion": "12.18.0", "dependencies": {"async": "^3.1.0", "logform": "^2.2.0", "one-time": "^1.0.0", "is-stream": "^2.0.0", "diagnostics": "github:DABH/diagnostics#master", "stack-trace": "0.0.x", "triple-beam": "^1.3.0", "readable-stream": "^3.4.0", "winston-transport": "^4.4.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "hock": "^1.4.1", "mocha": "^8.0.1", "assume": "^2.2.0", "colors": "^1.4.0", "rimraf": "^3.0.2", "split2": "^3.1.1", "through2": "^3.0.1", "std-mocks": "^1.0.1", "@babel/cli": "^7.10.3", "@babel/core": "^7.10.3", "@types/node": "^14.0.13", "winston-compat": "^0.1.5", "@babel/preset-env": "^7.10.3", "cross-spawn-async": "^2.2.5", "eslint-config-populist": "^4.2.0", "abstract-winston-transport": "^0.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/winston_3.3.1_1592807369938_0.21342705038594323", "host": "s3://npm-registry-packages"}}, "3.3.2": {"name": "winston", "version": "3.3.2", "keywords": ["winston", "logger", "logging", "logs", "sysadmin", "bunyan", "pino", "loglevel", "tools", "json", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@3.3.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "dabh", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "pose", "email": "alberto<PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "943773ea8e1c2353e088acd64a952c7809ac076e", "tarball": "https://registry.npmjs.org/winston/-/winston-3.3.2.tgz", "fileCount": 39, "integrity": "sha512-vTOrUZlyQPS8VpCcQ1JT8BumDAUe4awCHZ9nmGgO7LqkV4atj0dKa5suA7Trf7QKtBszE2yUs9d8744Kz9j4jQ==", "signatures": [{"sig": "MEQCIHzZGt1od2CO8t/EM8idL1RJHGrh+F2uwpgUdapWCKPFAiAtS+VDuVRTZaF3kgak9xmLDHPnm0RJl0L3VqatpgWriQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 285988, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8PArCRA9TVsSAnZWagAACawP/0FkUKDq+BoPMYf56gCg\nOVSBLpkFKqIm9O7+nIsghdm0fsVVXRe6lunWSgURmPHNf+HlLou8vkuK9Q8M\n6hxakun3sV8MW9CwUmq+PDRSxCvpiU0zM6yCWto4Z4iv3j70g0+LToTqblI9\nVetUtrFxyJWpXsnOQxdH0Mm2ZbR+cmy4vdkIb1Mjsqu5CPvm2XdoE8/8Cj83\nmz8c5L4wv3x6NlzZTruuYlML15ymeBfL+7yZ5c8r2RPoSQZKFg06YZDMAyPw\n36863rXuahaelG6Y4U+Z2OCCwvPeRMVqYSYVGtw7QV47O5s2o16Oq1n37IdA\nd89/EETSFRdQctE+WXdgywqkc5zKhnPkrDojSoMLBuwn/NBZEYGdEkFSd2cm\nYgdRFU9Q41yu5jyIeDSGZuJJ+tuAO6CcGRV24xAnjEZ2XV70hkRP9UGAtFjm\nUcgEcrklrlvUuJZje5ONg8lHsP39XdRmPFs+tS+ep7wCnb1rPe77IH47tz4t\nWPu5FYYtW4Ifsiqaw8+x18G2u94VZbydadGRGclsuv40oufkq9uybTSvWUsF\nbRq/RJm3odT3Wn4ZGnb+wiPf+PdQVZoLd3WfGvZWKQzVH/im5U0ILeD8DZLK\nmraAnRBWVicSkFewhXT4NX/cD6d7b7oUXubilaWRiG+21u3P7IMvK/kzA9sg\ng1Oa\r\n=XJW1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/winston", "types": "./index.d.ts", "browser": "./dist/winston", "engines": {"node": ">= 6.4.0"}, "gitHead": "5c8da2d4cd751523551125b96d05028a84af416f", "scripts": {"lint": "populist lib/*.js lib/winston/*.js lib/winston/**/*.js", "test": "nyc --reporter=text --reporter lcov npm run test:mocha", "build": "rimraf dist && babel lib -d dist", "pretest": "npm run lint", "test:mocha": "mocha test/*.test.js test/**/*.test.js --exit", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "dabh", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "A logger for just about everything.", "directories": {}, "_nodeVersion": "12.18.0", "dependencies": {"async": "^3.1.0", "logform": "^2.2.0", "one-time": "^1.0.0", "is-stream": "^2.0.0", "stack-trace": "0.0.x", "triple-beam": "^1.3.0", "readable-stream": "^3.4.0", "@dabh/diagnostics": "^2.0.2", "winston-transport": "^4.4.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "hock": "^1.4.1", "mocha": "^8.0.1", "assume": "^2.2.0", "colors": "^1.4.0", "rimraf": "^3.0.2", "split2": "^3.1.1", "through2": "^3.0.1", "std-mocks": "^1.0.1", "@babel/cli": "^7.10.3", "@babel/core": "^7.10.3", "@types/node": "^14.0.13", "winston-compat": "^0.1.5", "@babel/preset-env": "^7.10.3", "cross-spawn-async": "^2.2.5", "eslint-config-populist": "^4.2.0", "abstract-winston-transport": "^0.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/winston_3.3.2_1592848426635_0.095504133169513", "host": "s3://npm-registry-packages"}}, "3.3.3": {"name": "winston", "version": "3.3.3", "keywords": ["winston", "logger", "logging", "logs", "sysadmin", "bunyan", "pino", "loglevel", "tools", "json", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@3.3.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "dabh", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "pose", "email": "alberto<PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "ae6172042cafb29786afa3d09c8ff833ab7c9170", "tarball": "https://registry.npmjs.org/winston/-/winston-3.3.3.tgz", "fileCount": 39, "integrity": "sha512-oEXTISQnC8VlSAKf1KYSSd7J6IWuRPQqDdo8eoRNaYKLvwSb5+79Z3Yi1lrl6KDpU6/VWaxpakDAtb1oQ4n9aw==", "signatures": [{"sig": "MEUCIQDv2ggFoo+vPh+naplBQnLMVE4wrVR7bsAHAacXnHuxzQIgUqRRI+PYwiGFkivtjenmQmhFhXqyP5zwxZ7l7lezN8k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 286022, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8oK9CRA9TVsSAnZWagAAfsMP/33tz03qXNNntvEgFkAI\nkV87FBOV0uPlpjim9BylSh6AZdU3MQXxzFXliWWafaDOiBo3m7/h7TfoUe+l\neZ08kL02+GIaEu+A+ykpiRStI8VpCG/9Ogz5O7ySigAbxuY1eFU/nRwiwZ+U\nelm+/iynnCDuG1oreTk0IQyWfeCPpaA7EqJFWfd/FSwi0ZXrXgd9et/KwON3\nyjWDHF6/nl1lj0fuVYCjxbXikmrf4sLqhp8ItL+BPej82rrGzU0CzYERBuYz\nWvWvpT/0ixMeayc6MQyY5ubLopjlGpfNpqmDp3HB1y1KDqDo0ZJtqzl9y6Bb\njnn1RFu8uP5caarKV41yKFe9nnnuK0HOpuIgvIKnLq46XVbXc15AGEuiARKA\n6mcSNaaoiCWf+cnkbnHaJlBFyotKzwbVQ8uMFiiTH3II3T/IBcLzigOe4j1n\nciZY9VVAEZeq5aBN3obFJWuHieTXu6fXEMrRoZ3/o97G2ignlteZlJeeOTVi\n8ncnYj2zsOBMtpoYfymwj31lhfp1b8dsNnOhZGeorScxhmRhwJd9wwA5btQX\n09lec1bV1UwQSy1cd6tuQ6UbN+7Bc9StBWDsD3kl1eO31IZhdGfayKTSF8Ha\naYMyac6sPRj8pTDRHHTjZkhfLYy3hnnkl4gp8U2B7mdSw040AuvaFAgWooCS\nknJ7\r\n=dpjt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/winston", "types": "./index.d.ts", "browser": "./dist/winston", "engines": {"node": ">= 6.4.0"}, "gitHead": "7b46dc8898e8d9a2f95771f1acecf4f2b6e4bc93", "scripts": {"lint": "populist lib/*.js lib/winston/*.js lib/winston/**/*.js", "test": "nyc --reporter=text --reporter lcov npm run test:mocha", "build": "rimraf dist && babel lib -d dist", "pretest": "npm run lint", "test:mocha": "mocha test/*.test.js test/**/*.test.js --exit", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "dabh", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "A logger for just about everything.", "directories": {}, "_nodeVersion": "12.18.0", "dependencies": {"async": "^3.1.0", "logform": "^2.2.0", "one-time": "^1.0.0", "is-stream": "^2.0.0", "stack-trace": "0.0.x", "triple-beam": "^1.3.0", "readable-stream": "^3.4.0", "@dabh/diagnostics": "^2.0.2", "winston-transport": "^4.4.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "hock": "^1.4.1", "mocha": "^8.0.1", "assume": "^2.2.0", "colors": "^1.4.0", "rimraf": "^3.0.2", "split2": "^3.1.1", "through2": "^3.0.1", "std-mocks": "^1.0.1", "@babel/cli": "^7.10.3", "@babel/core": "^7.10.3", "@types/node": "^14.0.13", "winston-compat": "^0.1.5", "@babel/preset-env": "^7.10.3", "cross-spawn-async": "^2.2.5", "eslint-config-populist": "^4.2.0", "abstract-winston-transport": "^0.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/winston_3.3.3_1592951484789_0.8563740930322852", "host": "s3://npm-registry-packages"}}, "3.3.4": {"name": "winston", "version": "3.3.4", "keywords": ["winston", "logger", "logging", "logs", "sysadmin", "bunyan", "pino", "loglevel", "tools", "json", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@3.3.4", "maintainers": [{"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dabh", "email": "<EMAIL>"}, {"name": "w-b-t", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "474099197a28c0f3f6240401e66961342b5dee0a", "tarball": "https://registry.npmjs.org/winston/-/winston-3.3.4.tgz", "fileCount": 38, "integrity": "sha512-zWJrfmqE+2IXtVJ125vxpA2m303TjwchLhfRbcnma7c76Qd4pv80JIp37l8uGnWbCoG4X6PMz3vAQeh+vH1CtA==", "signatures": [{"sig": "MEYCIQDiu7k2nnHTpvJpegaLQsRS2botuymYvNa2a2+WHQ1YJgIhANSexgNzP3lVWe9IKmkhi7Z3B0g4M6CCTepH1CL6kmJw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 251132, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh25tsCRA9TVsSAnZWagAASk8QAJBA28sMWGedB+j0DvdW\nEcAsYT60XyuktoYjJPzzOz/8noWzLFP7D1CMkjVQKyVGhFAdSSdzNgyxh4In\nWD7xL+QTGlbRssJVWmt74IbfcsXAY36dBCf6B1aODNiTsNFbJoMB1y+5M1tm\nY+MT/yRsWZxa8paZ32KuHifsk61aWV6GBSlTCaOBrK9rfiGdE7wAS/GeZMsb\nF/r9sbz7lahEKqGIJ4cGXWu8vSOvVFkSgpfWnduPi8i+l+IgmmX4RQkLpeAa\nACc3thU1XUkbskE8RpCkyr9pnMm0bV52fxhtC9lkMQMRL2PfA3+WO3knZ+G9\nfFfmYOPmuwy5TZxgM2qB8qpcrkAm4/fUpw4OFbhHZU9XExao2rSkFQKkSRwq\nCMLQYVOxSuzkCnozgOwHaJ9auLIn9oYFaOieeJ5fu04fKkGyECKsFYanG/Y3\niHiG/WtyqiHn0YVIGwnKhWPuvHJbYnjxEusF+Tg1yKhvFRtMnDwHKUq535Ir\nkEg8BIlWIdqI8E1PqeTq7ffwcMp6lcm7KrDEVvRSH0UQc0pDUjLz4BKK4ej9\n2KRuRMV2bRbctN7kj1BD8sdJrqStXwwwoXc6A538BQDQwCspPgFttE2vXQiG\nVU3InmVpsI1PGGaJpv/C0GjUYkpQyOj0r4hPrCWJDvjXmeAKgQkNZmEPdU/j\nb6WQ\r\n=mWzV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/winston", "types": "./index.d.ts", "browser": "./dist/winston", "engines": {"node": ">= 6.4.0"}, "gitHead": "038ae23b00136c1e01c18a137ed0339b720bbb31", "scripts": {"lint": "populist lib/*.js lib/winston/*.js lib/winston/**/*.js", "test": "nyc --reporter=text --reporter lcov npm run test:mocha", "build": "rimraf dist && babel lib -d dist", "pretest": "npm run lint", "test:mocha": "mocha test/*.test.js test/**/*.test.js --exit", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "dabh", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "A logger for just about everything.", "directories": {}, "_nodeVersion": "14.16.1", "dependencies": {"async": "^3.2.3", "logform": "^2.3.2", "one-time": "^1.0.0", "is-stream": "^2.0.0", "stack-trace": "0.0.x", "triple-beam": "^1.3.0", "readable-stream": "^3.4.0", "@dabh/diagnostics": "^2.0.2", "winston-transport": "^4.4.2"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "hock": "^1.4.1", "mocha": "^9.1.3", "assume": "^2.2.0", "colors": "1.4.0", "rimraf": "^3.0.2", "split2": "^4.1.0", "through2": "^4.0.2", "std-mocks": "^1.0.1", "@babel/cli": "^7.16.7", "@babel/core": "^7.16.7", "@types/node": "^16.11.12", "winston-compat": "^0.1.5", "@babel/preset-env": "^7.16.7", "cross-spawn-async": "^2.2.5", "eslint-config-populist": "^4.2.0", "abstract-winston-transport": "^0.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/winston_3.3.4_1641782124114_0.3929765266329768", "host": "s3://npm-registry-packages"}}, "3.4.0": {"name": "winston", "version": "3.4.0", "keywords": ["winston", "logger", "logging", "logs", "sysadmin", "bunyan", "pino", "loglevel", "tools", "json", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@3.4.0", "maintainers": [{"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dabh", "email": "<EMAIL>"}, {"name": "w-b-t", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "7080f24b02a0684f8a37f9d5c6afb1ac23e95b84", "tarball": "https://registry.npmjs.org/winston/-/winston-3.4.0.tgz", "fileCount": 38, "integrity": "sha512-FqilVj+5HKwCfIHQzMxrrd5tBIH10JTS3koFGbLVWBODjiIYq7zir08rFyBT4rrTYG/eaTqDcfSIbcjSM78YSw==", "signatures": [{"sig": "MEUCIG10H3m2iLgDTtGWIjTqRtNucnxyhsnhHaooCvRpsmDeAiEA7sMQJUXMnzx2TFMTKZGE/HnRR3Lknmp5Q4hY65ZtNTc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 255231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3LKFCRA9TVsSAnZWagAAJeAP/0HpLcJy7SVIwtjra6gB\nGKz4kEkRpwK+Sn3bAcSTPGTAQinCoWRfK8LJE1Cb2xYPoDA5AuTkGwfkYKzY\nQN4k/hpW09M6aFY0AyN473OB5jZSJIK9DDFtrs6FlIebDD8ZAm5JUAlv8TQ8\nDJnQMZ5wBX9ibBDiZickCknXPbcRqNi5nKePkWkQzAyLJqqqyue7hLvJgTgd\n7ignPO88F6jMceK/sGwxzIlxpgjH6QzoSqqoBOyaZNlDF9Lb4zBXAzJRM5/1\nUbninnYGt25ZojP8r/HJ0ze0nG7qM5ht2TgRtlFLJOpUHfk7uX+u5HSRvFFK\ndE4uLqBs0KSjeJNCWyJZnB9psiKN4nEyV6naLkSRpQD4zqhR3Xw29dBjaLHN\nPcmOJD0fBWAvwqyDWTxKk4tEveHfzt5x1a9JLZ2cI4ShTjrJ568RVQAT4L0r\nDjFg6T4AcbXRAGmK+aHcrI69AJ45+Kp5dLSOp+y+wZK1N4Egx0HdAM+VVLhh\n0FVNEVhyTuixWX8KTFRcf3IgTDaBGOduZMVEKp2DJTUvGkRlLXnNqTlT/0f4\ns5MLHg2vSlWKBoWi8sObd+ijXYMsTnDcg51MYvY1yuy3EimkIjQH0TZlsTGV\nXa3JVMjl8gJ0BRBS4SLEZNaUk0VWytAaUD0T1U+MM9QAv374767L+vzxd21N\nN84K\r\n=RoVf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/winston", "types": "./index.d.ts", "browser": "./dist/winston", "engines": {"node": ">= 6.4.0"}, "gitHead": "c5f6c5cc6f27d6e83533e90ff64814a69825ca9e", "scripts": {"lint": "populist lib/*.js lib/winston/*.js lib/winston/**/*.js", "test": "nyc --reporter=text --reporter lcov npm run test:mocha", "build": "rimraf dist && babel lib -d dist", "pretest": "npm run lint", "test:mocha": "mocha test/*.test.js test/**/*.test.js --exit", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "w-b-t", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "7.24.0", "description": "A logger for just about everything.", "directories": {}, "_nodeVersion": "16.10.0", "dependencies": {"async": "^3.2.3", "logform": "^2.3.2", "one-time": "^1.0.0", "is-stream": "^2.0.0", "stack-trace": "0.0.x", "triple-beam": "^1.3.0", "readable-stream": "^3.4.0", "@dabh/diagnostics": "^2.0.2", "winston-transport": "^4.4.2"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "hock": "^1.4.1", "mocha": "8.1.3", "assume": "^2.2.0", "colors": "1.4.0", "rimraf": "^3.0.2", "split2": "^4.1.0", "through2": "^4.0.2", "std-mocks": "^1.0.1", "@babel/cli": "^7.16.7", "@babel/core": "^7.16.7", "@types/node": "^16.11.12", "winston-compat": "^0.1.5", "@babel/preset-env": "^7.16.7", "cross-spawn-async": "^2.2.5", "eslint-config-populist": "^4.2.0", "abstract-winston-transport": "^0.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/winston_3.4.0_1641853573326_0.5828938557442973", "host": "s3://npm-registry-packages"}}, "3.5.0": {"name": "winston", "version": "3.5.0", "keywords": ["winston", "logger", "logging", "logs", "sysadmin", "bunyan", "pino", "loglevel", "tools", "json", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@3.5.0", "maintainers": [{"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dabh", "email": "<EMAIL>"}, {"name": "w-b-t", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "b1ef4dbc6d1a7c1b462650070f171abc7ce9eac0", "tarball": "https://registry.npmjs.org/winston/-/winston-3.5.0.tgz", "fileCount": 38, "integrity": "sha512-OQMbmLsIdVHvm2hSurrYZs+iZNIImXneYJ6pX7LseSMEq20HdTETXiNnNX3FDwN4LB/xDRZLF6JYOY+AI112Kw==", "signatures": [{"sig": "MEUCIQCIiOiU99DKUVhsyPkEYCRONfe1ey5eOwDYoPeBTe13JQIgDXUEMXi1J8lmjimi8+Py6OcI2PCQiEbInfIi64OYfHo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 264719, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8sf7CRA9TVsSAnZWagAA8NsP/jLDfp/JiIKTS8R+an5r\nJLGF8Wy2teAyxoBAi+RA3iNvhCMH18XS/5/TI5ReJnh6LGfMHtjCzDtmiytI\nhafIUdVqAEV+y+q53zY17hWeepyw3DAz3hR73Q3/wxYfugMjEOlX7CmAtLc0\nKu+0hXLJbVT2SE3Ixw3ANw5uM6DVHkbvlkDoAXkJnU7p8Z7qem417dVWG+4x\nvsqicVPnAJJvky6+NtkQVjxo4ZhHQ5PZMmgqqVUlLQ2nC50JpL4leITL2Jfj\nkRuHjccPtzGBICBMw9h1cx97F9G+W8yOr7Q7Lm+mwPk7z05cBTHiHAQfDIzG\nXjkK+cQAajr3F2uN5j+JQQAWIXZeDMBQLZgzIOuuQtqxwikr9zvMiQ6eliVM\ni+iyeR0WNPXEbT3EQHOxSR6YPFNqXYrgEkKY8YjYK4abEBaSuEMzLURZ2Mnj\nNk7IvCNXz17uZ9C0bttP1tFmidO5EgiTscACVAS8LevYa3HzAcQUc1tIfJxn\nPEXr9oj8/I7w51UaEwc96rvgNz1cYm1Yj3PW6aCM6CmhuswoOJ5kLB1RYyMe\n+N10bGY+xy/bwSnT7zWBv1GFn0SuE1gc1ebY8y/lQBFaJbmfhlRp+LXYWqpa\nMpOCt1vxXM+xm57jDtzD9Z46hY09wEml8t0ebRmQX+ZUSHS+iCa6J7pBBByn\nWmfv\r\n=sQbN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/winston", "types": "./index.d.ts", "browser": "./dist/winston", "engines": {"node": ">= 6.4.0"}, "gitHead": "237534aa237b05fb3c7742e9264e57b5b1a66e43", "scripts": {"lint": "populist lib/*.js lib/winston/*.js lib/winston/**/*.js", "test": "nyc --reporter=text --reporter lcov npm run test:mocha", "build": "rimraf dist && babel lib -d dist", "pretest": "npm run lint", "test:mocha": "mocha test/*.test.js test/**/*.test.js --exit", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "w-b-t", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "7.24.0", "description": "A logger for just about everything.", "directories": {}, "_nodeVersion": "16.10.0", "dependencies": {"async": "^3.2.3", "logform": "^2.3.2", "one-time": "^1.0.0", "is-stream": "^2.0.0", "stack-trace": "0.0.x", "triple-beam": "^1.3.0", "readable-stream": "^3.4.0", "@dabh/diagnostics": "^2.0.2", "winston-transport": "^4.4.2", "safe-stable-stringify": "^2.3.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "hock": "^1.4.1", "mocha": "8.1.3", "assume": "^2.2.0", "colors": "1.4.0", "rimraf": "^3.0.2", "split2": "^4.1.0", "through2": "^4.0.2", "std-mocks": "^1.0.1", "@babel/cli": "^7.16.7", "@babel/core": "^7.16.7", "@types/node": "^17.0.8", "winston-compat": "^0.1.5", "@babel/preset-env": "^7.16.7", "cross-spawn-async": "^2.2.5", "eslint-config-populist": "^4.2.0", "abstract-winston-transport": "^0.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/winston_3.5.0_1643300859331_0.09990449938330959", "host": "s3://npm-registry-packages"}}, "3.5.1": {"name": "winston", "version": "3.5.1", "keywords": ["winston", "logger", "logging", "logs", "sysadmin", "bunyan", "pino", "loglevel", "tools", "json", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@3.5.1", "maintainers": [{"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dabh", "email": "<EMAIL>"}, {"name": "w-b-t", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "b25cc899d015836dbf8c583dec8c4c4483a0da2e", "tarball": "https://registry.npmjs.org/winston/-/winston-3.5.1.tgz", "fileCount": 38, "integrity": "sha512-tbRtVy+vsSSCLcZq/8nXZaOie/S2tPXPFt4be/Q3vI/WtYwm7rrwidxVw2GRa38FIXcJ1kUM6MOZ9Jmnk3F3UA==", "signatures": [{"sig": "MEUCIQCf0SXWCdGsGqbwh76MuGWHP3vEW30Z4YyNhalhO3X7cAIgCJT67iPUHeo1tXJHzipRaZ1ErtErz/4+FkB2mJ8n46w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 264641, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+E1iCRA9TVsSAnZWagAAo/oQAJqKV7AgwbIB525ZXuKM\n85FLvIDGoc/8g9xssagAACTsV/VfE4gh2PsWlDq1oBvl77YgQ/vq7Cn7i81s\n++03UHvk5SAZ7LnIj8a59vyNQYJdJ06nqOs0M3ZYDm96SJvju/eMMnR1/SGZ\n9syrcDBzKTmjcInvgFWTUVZ8lRYhVUDxQiCERrmN+Y58reKnESYugwG1wl4j\nWD88R34G9CZ8Emh+FjxqFX+zWZrHXSaFQuPP/0MP0dNh0/69rVgalzWe9FYL\nyXSlPbBLpKEzhwo433/gv5LmeJ+sQSz16iFU8BWJdxTgBd8VGv99icU9Lrfv\n5ygPVWzzGP9RwLQJiH2eZ7a0T0572zMZlj6sj4pGWXsi8mg5/fIRJnY/h/Ot\nRnil0IXudqcyI6l2TETAg/jgJ8LXPkqIQcZqIiSo8uUQk+jpQX/Kv8IYP+WP\no7qh8+bybh/tQvPKxh8grIoF3a+vxv6oOlT06uXTrJr0S1JrKSSe/KXS2P3b\n10sS2IUXJ6JrZuCtj7e3yG9mWG9GOTIzpSaV6H0gWM5U+rM6278vbhHBCJ/1\np4xDUdwG1Jg9Nc6KVwRW2wua6qF8/1UY3RAFzeopRpAVRqK2mo9Tjk2Uexoc\n/OX1H6eKRaB4YYkdRavUOVoGXmLfEjGSUdJucoLS9prnpHu2/tfTvixSpd1u\no5TM\r\n=5kcg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/winston", "types": "./index.d.ts", "browser": "./dist/winston", "engines": {"node": ">= 6.4.0"}, "gitHead": "4c5f14866fb37f16592c205afc487d210b8295cc", "scripts": {"lint": "populist lib/*.js lib/winston/*.js lib/winston/**/*.js", "test": "nyc --reporter=text --reporter lcov npm run test:mocha", "build": "rimraf dist && babel lib -d dist", "pretest": "npm run lint", "test:mocha": "mocha test/*.test.js test/**/*.test.js --exit", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "w-b-t", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "7.24.0", "description": "A logger for just about everything.", "directories": {}, "_nodeVersion": "16.10.0", "dependencies": {"async": "^3.2.3", "logform": "^2.3.2", "one-time": "^1.0.0", "is-stream": "^2.0.0", "stack-trace": "0.0.x", "triple-beam": "^1.3.0", "readable-stream": "^3.4.0", "@dabh/diagnostics": "^2.0.2", "winston-transport": "^4.4.2", "safe-stable-stringify": "^2.3.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "hock": "^1.4.1", "mocha": "8.1.3", "assume": "^2.2.0", "colors": "1.4.0", "rimraf": "^3.0.2", "split2": "^4.1.0", "through2": "^4.0.2", "std-mocks": "^1.0.1", "@babel/cli": "^7.16.7", "@babel/core": "^7.16.7", "@types/node": "^17.0.8", "winston-compat": "^0.1.5", "@babel/preset-env": "^7.16.7", "cross-spawn-async": "^2.2.5", "eslint-config-populist": "^4.2.0", "abstract-winston-transport": "^0.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/winston_3.5.1_1643662690751_0.4607110243401149", "host": "s3://npm-registry-packages"}}, "3.6.0": {"name": "winston", "version": "3.6.0", "keywords": ["winston", "logger", "logging", "logs", "sysadmin", "bunyan", "pino", "loglevel", "tools", "json", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@3.6.0", "maintainers": [{"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dabh", "email": "<EMAIL>"}, {"name": "w-b-t", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "be32587a099a292b88c49fac6fa529d478d93fb6", "tarball": "https://registry.npmjs.org/winston/-/winston-3.6.0.tgz", "fileCount": 38, "integrity": "sha512-9j8T75p+bcN6D00sF/zjFVmPp+t8KMPB1MzbbzYjeN9VWxdsYnTB40TkbNUEXAmILEfChMvAMgidlX64OG3p6w==", "signatures": [{"sig": "MEQCIHVKbOY88QYMmSfJ2TD5FoYw16yErfZfbZ5oftJIu5KkAiASaSiEnennXk/eYLvrP3jpH+JdkzXMoTVC9PCWl7FcCQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 259273, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiCAZBCRA9TVsSAnZWagAARpgP+QBVgWdbWuL0HY8QmRu8\n3E+d8WSiTGYKUbgfedzIr4Cun4/AVIdr28CeZNbgHfuj9NtM8s8IF4Q3aT0Q\nq/lMvQzO3KiWIpIP5nTYAxpYK2DM3klGkw3U881wz9k5FPl16MvCdXRZxuuF\n+YJuw+O+XCBUJallJL4FJzWADxb9WnxMRaJ9hcb/acaVS6kaCFQxLqB9u7PK\n5XMrES2OuWKr6v0wWgyndHv6stoV30nfh+HnfeUBzOU4VXb3VGQ8trN+I1hR\nu13McKjK2uZkavwdVaRz5G7NQQYfOX7cvopQL86tQ7dqXqwaamqxNlapE5lc\nUmJyKlgTzuPBtef7UytvhRGWesJnybygG9T6g6mUem5zJqhSWo+u/+ZX8fls\nHDX1fWwJAcRst/ixusPq2GXdgAp9KSJjg8x10hGZhL1FB0zuTQ0TUHyit4Gh\n9TBukVCDNFS+27ORW6s//1fhqP+e53Ku3p8JYX5MgamjJKrL4ZYCEbpMrAYm\nsgLyO9nBpTxhWwI1uIKy9t2vGppYzbivF9l8izktPHSRbZRxgnozs78ZJVZS\nxpk34cFNpP2eXksxO+Tj8ctgjGFIEDCP1XWSx8IeUTmL1DDKCs8FczW0uH8A\nxO2am6E9jhMl2sIVy2jRGbhwc442VrzGnDmGymvhFyP7S/1vat0+0OQGBzdN\nXcps\r\n=d3gn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/winston", "types": "./index.d.ts", "browser": "./dist/winston", "engines": {"node": ">= 12.0.0"}, "gitHead": "b2fde9da5398f6129541454a9275d1243cc18b0b", "scripts": {"lint": "eslint lib/*.js lib/winston/*.js lib/winston/**/*.js --resolve-plugins-relative-to ./node_modules/@dabh/eslint-config-populist", "test": "mocha", "build": "rimraf dist && babel lib -d dist", "test:unit": "mocha test/unit", "test:coverage": "nyc npm run test:unit", "prepublishOnly": "npm run build", "test:integration": "mocha test/integration"}, "_npmUser": {"name": "dabh", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "8.4.1", "description": "A logger for just about everything.", "directories": {}, "_nodeVersion": "14.19.0", "dependencies": {"async": "^3.2.3", "logform": "^2.4.0", "one-time": "^1.0.0", "is-stream": "^2.0.0", "stack-trace": "0.0.x", "triple-beam": "^1.3.0", "readable-stream": "^3.4.0", "@dabh/diagnostics": "^2.0.2", "winston-transport": "^4.5.0", "safe-stable-stringify": "^2.3.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "hock": "^1.4.1", "mocha": "8.1.3", "assume": "^2.2.0", "eslint": "^8.9.0", "rimraf": "^3.0.2", "split2": "^4.1.0", "through2": "^4.0.2", "std-mocks": "^1.0.1", "@babel/cli": "^7.17.0", "@babel/core": "^7.17.2", "@types/node": "^17.0.17", "@colors/colors": "1.5.0", "winston-compat": "^0.1.5", "@babel/preset-env": "^7.16.7", "cross-spawn-async": "^2.2.5", "abstract-winston-transport": "^0.5.1", "@dabh/eslint-config-populist": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/winston_3.6.0_1644693056979_0.013177503736218066", "host": "s3://npm-registry-packages"}}, "3.7.1": {"name": "winston", "version": "3.7.1", "keywords": ["winston", "logger", "logging", "logs", "sysadmin", "bunyan", "pino", "loglevel", "tools", "json", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@3.7.1", "maintainers": [{"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dabh", "email": "<EMAIL>"}, {"name": "w-b-t", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "1c1914c8f03ba76d6d882bacc831bac0f423aa18", "tarball": "https://registry.npmjs.org/winston/-/winston-3.7.1.tgz", "fileCount": 38, "integrity": "sha512-qKLMQVWMOvY0h9H4BA8Sfh79+KdnKi8gsyCSvfQgc+6teSlq92j82WK+zAJc6fLbAA2jwQuqkANLpQeOFA4Kug==", "signatures": [{"sig": "MEYCIQCqtS12p7vfdaxtEXuQcsvv84onvfWept/XZajxdxHaXAIhAOF8lMSHOuCC0CL8GzUGBdQ35FxMs6XIGu00zSlbGcfy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 265437, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiSvxuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrTSg/+JSDzjk8WymWpProVSpzIdq/AsA2jKtGQnQLGhUU5LhhZ3twW\r\nuVxoGjS9kX5tQyVgICmCQNcISUwPUcddbiG4OydVvmCuYnv6R3KZvnVnvWaO\r\nDEbmLpVjBelDQWFpDwzhBc4VHYrsHO5hTifQ7mR2Q3p4S/+9jDkAOmiaYXgY\r\ncV7TeCootxJegjE/RdMq90iB4kkrovIBZB8Jyfwps/K0Eu5guN79AxkEUKcn\r\n0eKNzldIsexYsBIJY+9BxWkiazVoR/mmfuS0RDV8+65mNpe7rXv9TdBpPTkq\r\n/dVVIEKZgC+qTVxTn5W+PKZ2pca57mg3m6rCBYVbP3K3x1G9rKlw5riPszG0\r\n0Go2114YsIijL8qkZhOghk/ttMo2TrztxlO4HydH3VFU8Spr5Meg7koFKgxu\r\nwxcn7KEDW1hXbYsIU1FcWCUmPteClk/a45DV1kSlSH5DpGx0aRE88KoAE4bs\r\nTy+5Yt5+UZDOpHBz8pT/Gen6Js8W/foYGES979BbCLFFZL/CLNcc+BRVhb/M\r\nykBfpb9VTSoafswhsWC5RweH+tlmhJjTLrs4c1Vqi6h2SBBQvpEhCc2HKWml\r\n+V79TsOeHZL+3IdZz8l0mS9DE6UjSVbJze2zpRd7BDozKCzDyi494vHAXVIB\r\ngRDxnxxIHxh9VZo/ihFsiKzboyjcWkeYHS8=\r\n=vOF5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/winston", "types": "./index.d.ts", "browser": "./dist/winston", "engines": {"node": ">= 12.0.0"}, "gitHead": "68f595b961a92ff7728fbd9310fdcb26524776b8", "scripts": {"lint": "eslint lib/*.js lib/winston/*.js lib/winston/**/*.js --resolve-plugins-relative-to ./node_modules/@dabh/eslint-config-populist", "test": "mocha", "build": "rimraf dist && babel lib -d dist", "lint:fix": "npm run lint -- --fix", "test:unit": "mocha test/unit", "test:coverage": "nyc npm run test:unit", "prepublishOnly": "npm run build", "test:integration": "mocha test/integration"}, "_npmUser": {"name": "w-b-t", "email": "<EMAIL>"}, "deprecated": "Please use version 3.6.0 or >3.7.1 when available due to https://github.com/winstonjs/winston/issues/2103", "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "7.24.0", "description": "A logger for just about everything.", "directories": {}, "_nodeVersion": "16.10.0", "dependencies": {"async": "^3.2.3", "logform": "^2.4.0", "one-time": "^1.0.0", "is-stream": "^2.0.0", "stack-trace": "0.0.x", "triple-beam": "^1.3.0", "readable-stream": "^3.4.0", "@dabh/diagnostics": "^2.0.2", "winston-transport": "^4.5.0", "safe-stable-stringify": "^2.3.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "hock": "^1.4.1", "mocha": "8.1.3", "assume": "^2.2.0", "eslint": "^8.9.0", "rimraf": "^3.0.2", "split2": "^4.1.0", "through2": "^4.0.2", "std-mocks": "^1.0.1", "@babel/cli": "^7.17.0", "@babel/core": "^7.17.2", "@types/node": "^17.0.17", "@colors/colors": "1.5.0", "winston-compat": "^0.1.5", "@babel/preset-env": "^7.16.7", "cross-spawn-async": "^2.2.5", "abstract-winston-transport": "^0.5.1", "@dabh/eslint-config-populist": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/winston_3.7.1_1649081454561_0.10129682983426891", "host": "s3://npm-registry-packages"}}, "3.7.2": {"name": "winston", "version": "3.7.2", "keywords": ["winston", "logger", "logging", "logs", "sysadmin", "bunyan", "pino", "loglevel", "tools", "json", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@3.7.2", "maintainers": [{"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dabh", "email": "<EMAIL>"}, {"name": "w-b-t", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "95b4eeddbec902b3db1424932ac634f887c400b1", "tarball": "https://registry.npmjs.org/winston/-/winston-3.7.2.tgz", "fileCount": 38, "integrity": "sha512-QziIqtojHBoyzUOdQvQiar1DH0Xp9nF1A1y7NVy2DGEsz82SBDtOalS0ulTRGVT14xPX3WRWkCsdcJKqNflKng==", "signatures": [{"sig": "MEUCIQDWetZy/HOeIjjxjrnRy/BRRkgaHCGj8Jn+GwiS3r1V1gIgRoy5+yxeiMzgM7vbUOIoYXZPE+IWceXPJeK2/AN4ApM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 264817, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiS222ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq9Pw/8D46i5luehHGKYMuIGcDh+muT+66PCWY3f4GEEJi8MTu1wz69\r\n16CzZ+ENcBXjawOa0j6xQm6hTWhbYqhSXzMvpjtyq8z+NGfHCOszGV3W0dlY\r\nHyM72lPe75+J4vJnKBU7Xey+ytQdWcdUxYJgyZh7wYGcMqW81Q/YZXtIYoiB\r\nQeiMb7Ii6GeC1/Wg9iULKRUhJzeZxjFM4wg+/3aqh5cJ4y6de5o8nmu04NFK\r\nnUXgojfGKuIi2blFAbNpLTGzp2gG0INANuVgBu9CSn9Ik1u23rHueYnZ9qNK\r\nVMaKy3G6ItazaX8P+r1nxYL+uI5jo2exR+f45mQo+7d8lDyGCU4GrIV0ICdz\r\nARfvxLNoai4kufew6YuoRgvrrQ2knjIpXk5qPTj4RZsT0GWci8qWW9owB8z+\r\nhkWHS1WuNsFXVlTXtbTJq7RNkJOHvWEqjCo8J9zA6os9BF0o3iAm1SJNpmYX\r\nhM3S6/Iuc4pR4L4wyT8FIC7rXgr5/m3PMhXuPb2bSdEb0OAUewXdvdMiCTsd\r\nvWKzH6Atjb6rAzPFG66/lnLdmyORJXvbhA7T4lp2NnHBtMxVuY46OWoZ9Cy7\r\nZUpETaCkk2ZxhsgoijTnYlgd+1tIUf2kYj1/6RQaCOxB2Rwil04374wr0U8T\r\nxcM8yyTDgNi0lp8N1c+riqYQ8ORYopjT29c=\r\n=Nmpy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/winston", "types": "./index.d.ts", "browser": "./dist/winston", "engines": {"node": ">= 12.0.0"}, "gitHead": "7937f337dac50b4f134bbfa88f72bdd313ad0af5", "scripts": {"lint": "eslint lib/*.js lib/winston/*.js lib/winston/**/*.js --resolve-plugins-relative-to ./node_modules/@dabh/eslint-config-populist", "test": "mocha", "build": "rimraf dist && babel lib -d dist", "test:unit": "mocha test/unit", "test:coverage": "nyc npm run test:unit", "prepublishOnly": "npm run build", "test:integration": "mocha test/integration"}, "_npmUser": {"name": "w-b-t", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "7.24.0", "description": "A logger for just about everything.", "directories": {}, "_nodeVersion": "16.10.0", "dependencies": {"async": "^3.2.3", "logform": "^2.4.0", "one-time": "^1.0.0", "is-stream": "^2.0.0", "stack-trace": "0.0.x", "triple-beam": "^1.3.0", "readable-stream": "^3.4.0", "@dabh/diagnostics": "^2.0.2", "winston-transport": "^4.5.0", "safe-stable-stringify": "^2.3.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "hock": "^1.4.1", "mocha": "8.1.3", "assume": "^2.2.0", "eslint": "^8.9.0", "rimraf": "^3.0.2", "split2": "^4.1.0", "through2": "^4.0.2", "std-mocks": "^1.0.1", "@babel/cli": "^7.17.0", "@babel/core": "^7.17.2", "@types/node": "^17.0.17", "@colors/colors": "1.5.0", "winston-compat": "^0.1.5", "@babel/preset-env": "^7.16.7", "cross-spawn-async": "^2.2.5", "abstract-winston-transport": "^0.5.1", "@dabh/eslint-config-populist": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/winston_3.7.2_1649110454566_0.6239779598853763", "host": "s3://npm-registry-packages"}}, "2.4.6": {"name": "winston", "version": "2.4.6", "keywords": ["winston", "logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@2.4.6", "maintainers": [{"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dabh", "email": "<EMAIL>"}, {"name": "w-b-t", "email": "<EMAIL>"}, {"name": "maverick18722", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "da616f332928f70aac482f59b43d62228f29e478", "tarball": "https://registry.npmjs.org/winston/-/winston-2.4.6.tgz", "fileCount": 31, "integrity": "sha512-J5Zu4p0tojLde8mIOyDSsmLmcP8I3Z6wtwpTDHx1+hGcdhxcJaAmG4CFtagkb+NiN1M9Ek4b42pzMWqfc9jm8w==", "signatures": [{"sig": "MEUCIQD1BQhreSF8F5qxqAFXXs35Mgtbd+97ST2Wfr494v9nEgIgdnH32E4sZYowIhM+NYucAyRX8y2BAEdsGfE7Ocsp7TY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 175321, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiavyqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqbLw//acuHcYulwV7Aij5YL3xuXhNFTDJL6bOFteuUua0H6eZNbtsP\r\nQDlRMRmnKYf1XRu4YWjHSdla2I4RFck9cmx3fNO6eTaGOV/Ze4/uzWAOtPzW\r\nY/QqLAYzr1LPB/JR9SQb6nrFI4tuDQ+XwtOHatrMRh+YFL1I3r61PLm1bfag\r\npobIqH33/Yw9lKdG33xUIB9omnL0ri9E9r11a9UI17y3kDZ7coOc4R9ZhFxQ\r\n6rnQUSERjCGWXHCNpcdPXpPg55tR8EKn5VfJCE1t5SjTPKZ9cs9+rlU/wu+h\r\nF+WVdMsDCzlzUlxmpvBXMNEQUP/5ZeeGANGhVH5ixtbYJfwWnAIV0XUnz+/D\r\nmW+tichEqmGhGdfYr1gedG/LkhCZe/Skjj2DCOPZa/ti3d7CyGQWD+fS+sjn\r\ndMY4diHn3esQWSgG2b0IGHwNGxAglfACwbpbQtNp6m8SFvwN8YYG1VxSEKt+\r\nSbkThTA8O/YX2+hXf2eaMGXnitPSL1TFL5B90ScaUszguDeQ2NLHbfD/9Wgf\r\n+Oxa3TcRRvghNFIP5wumHd4hdZGM2cUxHUKKb8G4nTXLo95eBYD8OsqugOIy\r\niMz84+1E1qSKPygKK05VkvfpoXfFhmz+o9ejAaNwN0H2Fz5XJpQI0/KKvS8q\r\nwDytl35pw+X/uNEpB6y2sXb3M7rbj/z7abg=\r\n=l3hA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/winston", "types": "./index.d.ts", "engines": {"node": ">= 0.10.0"}, "gitHead": "d6d620f146ca4aa6618319861b5ab7bcfded2051", "scripts": {"test": "vows --dot-matrix --isolate"}, "_npmUser": {"name": "w-b-t", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "7.24.0", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "16.10.0", "dependencies": {"eyes": "0.1.x", "async": "^3.2.3", "cycle": "1.0.x", "colors": "1.0.x", "isstream": "0.1.x", "stack-trace": "0.0.x"}, "_hasShrinkwrap": false, "devDependencies": {"hock": "1.x.x", "vows": "0.7.x", "std-mocks": "~1.0.0", "cross-spawn-async": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/winston_2.4.6_1651178666199_0.06725225856693706", "host": "s3://npm-registry-packages"}}, "3.8.0": {"name": "winston", "version": "3.8.0", "keywords": ["winston", "logger", "logging", "logs", "sysadmin", "bunyan", "pino", "loglevel", "tools", "json", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@3.8.0", "maintainers": [{"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dabh", "email": "<EMAIL>"}, {"name": "w-b-t", "email": "<EMAIL>"}, {"name": "maverick18722", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "4fc8656829dcfdab3c38f558eab785eea38a5328", "tarball": "https://registry.npmjs.org/winston/-/winston-3.8.0.tgz", "fileCount": 38, "integrity": "sha512-Iix1w8rIq2kBDkGvclO0db2CVOHYVamCIkVWcUbs567G9i2pdB+gvqLgDgxx4B4HXHYD6U4Zybh6ojepUOqcFQ==", "signatures": [{"sig": "MEUCIBUWEl53jKViLYzeK96joszOmk/6FfWq59gyYft+xLeKAiEAhlLr6dndOTJew2zs81YkYQ3idCMSAIZyrTRiToacDzA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 265519, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJitIX8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq8Aw//cSnmzZXcCfSD0L1EqxInJ8xkwY3U77IZ+etbx+b1T63ouEw8\r\nzLj/xhKiL38a84BYBST5EcXODPKt0ggPNxI2x/06y8y8bE3i5Is27sNsNyhI\r\nrL8BNfc/iiaoqNUo+sv5ytvy5CJAS+ZnPKaxXPhpbhvhiCO7rnfC1FoKmIlC\r\nloaevxQ7IvgTdZ+6i8TxrTRqb2RnQloM3C0j5jwgRGnHE0hGbG84JxuT5Rjc\r\n8kaPU/NymFXLJZc0USq/UC3f33JSM5Qmj0WNiSuK85GawgQsjQXbdt+7zW1n\r\n/G2qDJgpclxJiPLTmhAraBPqk/XulbAaL9dvZ9qZBnqg3YVNBC1h8fRHYv6l\r\nGm3usPDKHL7LnPM7AlpZ84dp9krQLkN1JWopjJ8l8bzHjpsXNE8jgi0zeyGw\r\n399rXD1Vhowca2ots6FFjlV0GmwCpR9cuc1lLhsAk+YVYlXnADxaEV45FAXk\r\njJ55Mj7jIdzgLtzLZm8WymqBm7DHgCYzG2aTes5mvvMvXdfIWePeMgpM3V8N\r\nUmk0Jxj07k8zDl7CO61Xf3b+rGRkNcOoKP9WM+NXWkz1YsUMgZW8i9nYipDm\r\nRMExC3/p2ph+oLBJJvM/DPe19fBWC+SlTRqUWnDtsEGfDx2N7M7m+jFrcmSt\r\nBN7plZ9A53esnXaAoiW9xZ8sAEVdaaSAL4s=\r\n=hxjk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/winston", "types": "./index.d.ts", "browser": "./dist/winston", "engines": {"node": ">= 12.0.0"}, "gitHead": "5658ec3fa00d537fcdcefbe638fcde3598dc1bec", "scripts": {"lint": "eslint lib/*.js lib/winston/*.js lib/winston/**/*.js --resolve-plugins-relative-to ./node_modules/@dabh/eslint-config-populist", "test": "mocha", "build": "rimraf dist && babel lib -d dist", "test:unit": "mocha test/unit", "test:coverage": "nyc npm run test:unit", "prepublishOnly": "npm run build", "test:integration": "mocha test/integration"}, "_npmUser": {"name": "w-b-t", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "7.24.0", "description": "A logger for just about everything.", "directories": {}, "_nodeVersion": "16.10.0", "dependencies": {"async": "^3.2.3", "logform": "^2.4.0", "one-time": "^1.0.0", "is-stream": "^2.0.0", "stack-trace": "0.0.x", "triple-beam": "^1.3.0", "readable-stream": "^3.4.0", "@dabh/diagnostics": "^2.0.2", "winston-transport": "^4.5.0", "safe-stable-stringify": "^2.3.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "hock": "^1.4.1", "mocha": "8.1.3", "assume": "^2.2.0", "eslint": "^8.9.0", "rimraf": "^3.0.2", "split2": "^4.1.0", "through2": "^4.0.2", "std-mocks": "^1.0.1", "@babel/cli": "^7.17.0", "@babel/core": "^7.17.2", "@types/node": "^18.0.0", "@colors/colors": "1.5.0", "winston-compat": "^0.1.5", "@babel/preset-env": "^7.16.7", "cross-spawn-async": "^2.2.5", "abstract-winston-transport": "^0.5.1", "@dabh/eslint-config-populist": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/winston_3.8.0_1655997948063_0.1672182756374725", "host": "s3://npm-registry-packages"}}, "3.8.1": {"name": "winston", "version": "3.8.1", "keywords": ["winston", "logger", "logging", "logs", "sysadmin", "bunyan", "pino", "loglevel", "tools", "json", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@3.8.1", "maintainers": [{"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dabh", "email": "<EMAIL>"}, {"name": "w-b-t", "email": "<EMAIL>"}, {"name": "maverick18722", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "76f15b3478cde170b780234e0c4cf805c5a7fb57", "tarball": "https://registry.npmjs.org/winston/-/winston-3.8.1.tgz", "fileCount": 38, "integrity": "sha512-r+6YAiCR4uI3N8eQNOg8k3P3PqwAm20cLKlzVD9E66Ch39+LZC+VH1UKf9JemQj2B3QoUHfKD7Poewn0Pr3Y1w==", "signatures": [{"sig": "MEUCIQCEwVNezPKZKWONqfOnag0msjOXQXU22KfBUuDNUUr4twIgSvw9T1aV1afVITGGwMUFEWNwTyO5cVFFa5+Lcamp+GM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 265415, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivfpgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqgTw//QwNFqhLRMIMjFCEnqe7pHGWZHXbGCYbXCGVgNx4ucgRleqNd\r\niXLgu0udB87Ke+sr32KD8/GscwwE9knucLcfJqpAe4GWzrGHxX3RuK8QMiub\r\nhuzhfSXJFkPI1l6n1ycsZw6ZjnpcwCuVd3Ou5SQQ9CFrv/X5vzgCE486ycJI\r\nFG69CeArTIh6MBgBnVtRaJ2oVdbb/B+Ra+jjDIuu5E/Rl0VGauWIq3QBTZGX\r\nOkIZoGqo18pRmHWd5em5umnL/3vGyslHFoNusckTdrkM2WK4ApjruJ4S/GGP\r\nxVWj0S80YUa7mfsCMQkyBLgwJSBHdAuBsAHvbAH4GW2uVdncJeflJ2Jeub8T\r\nmVyrJkjekFFe9JIGaXUz2D4hjY2xPxyT2iPi/0BeCEkY3QZNtNf6NUodfLZY\r\n63VQIYCstTrYZ7HzNyNBhWTXzKxFVtWYwTFDXM+D+NnQadCBmyl+aC+XqDJy\r\nlEcBAmOCnpQ+1a6HoEWRFv6eGa7GMxIMBkE7EEpUdP7zppvZyPV+m3zlmPqn\r\nuhL2hRo0WGVk4ALtwOkZnO+AAwGTHwcvZm1JdzlJX/56Q7EWgMVTt4JJrIeq\r\nPuZGW9nB2rAVlgua+2o197ac1J2nBjsJSXwi83ExyaB3xFiD3g77HT6jABou\r\nlX665Ny7pDU6qNqZ6dCSqn/5/uAIUrOAeoE=\r\n=8T+Q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/winston", "types": "./index.d.ts", "browser": "./dist/winston", "engines": {"node": ">= 12.0.0"}, "gitHead": "3998df01449da35a60cf4b90730b91f4b2466bc5", "scripts": {"lint": "eslint lib/*.js lib/winston/*.js lib/winston/**/*.js --resolve-plugins-relative-to ./node_modules/@dabh/eslint-config-populist", "test": "mocha", "build": "rimraf dist && babel lib -d dist", "test:unit": "mocha test/unit", "test:coverage": "nyc npm run test:unit", "prepublishOnly": "npm run build", "test:integration": "mocha test/integration"}, "_npmUser": {"name": "w-b-t", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "8.13.0", "description": "A logger for just about everything.", "directories": {}, "_nodeVersion": "16.10.0", "dependencies": {"async": "^3.2.3", "logform": "^2.4.0", "one-time": "^1.0.0", "is-stream": "^2.0.0", "stack-trace": "0.0.x", "triple-beam": "^1.3.0", "readable-stream": "^3.4.0", "@dabh/diagnostics": "^2.0.2", "winston-transport": "^4.5.0", "safe-stable-stringify": "^2.3.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "hock": "^1.4.1", "mocha": "8.1.3", "assume": "^2.2.0", "eslint": "^8.9.0", "rimraf": "^3.0.2", "split2": "^4.1.0", "through2": "^4.0.2", "std-mocks": "^1.0.1", "@babel/cli": "^7.17.0", "@babel/core": "^7.17.2", "@types/node": "^18.0.0", "@colors/colors": "1.5.0", "winston-compat": "^0.1.5", "@babel/preset-env": "^7.16.7", "cross-spawn-async": "^2.2.5", "abstract-winston-transport": "^0.5.1", "@dabh/eslint-config-populist": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/winston_3.8.1_1656617568405_0.008152724527540123", "host": "s3://npm-registry-packages"}}, "3.8.2": {"name": "winston", "version": "3.8.2", "keywords": ["winston", "logger", "logging", "logs", "sysadmin", "bunyan", "pino", "loglevel", "tools", "json", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@3.8.2", "maintainers": [{"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dabh", "email": "<EMAIL>"}, {"name": "w-b-t", "email": "<EMAIL>"}, {"name": "maverick18722", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "56e16b34022eb4cff2638196d9646d7430fdad50", "tarball": "https://registry.npmjs.org/winston/-/winston-3.8.2.tgz", "fileCount": 38, "integrity": "sha512-MsE1gRx1m5jdTTO9Ld/vND4krP2To+lgDoMEHGGa4HIlAUyXJtfc7CxQcGXVyz2IBpw5hbFkj2b/AtUdQwyRew==", "signatures": [{"sig": "MEUCIQDZpZP1TMyJndiq62n9wqaQs7Nw+hbskPFCnYrY50qjngIgSw95YgZ8q0lCXT+JMXVpxoyvcfFPEw6Kaocw6dRrM9A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 265422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjGMUjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmofOg/9H7f73rjyTfM7Oj48NxJfcGDmvEsTCzpg9Okv+ZosUflFdPX4\r\nfg8BSfUyopx7KybBePKfOLWgLn6Kg8qrbiN1sXkGPLYmdTIY+oQF0yC7gkVs\r\n58YII2wt1zGa9aKxc2H7RJtGnkejmT9BX5zJjUtu1f3eeZDEf8GEIqw2iER2\r\nvDDGDlz/FGa4XfbZN93azIAekBBvDyl/cn911WUQr2qwJEKqrubIuR6cs8i2\r\n/F0O+PCablDPNliw3Z/zRigF/cFLJif9RNAfEXYKHD3ZobzbimikuYdTgwUE\r\n9HVJ3tRXvvVJRclFb9j31RIT9RId/p62EiD6Rr25UiWIcexLW8almVsUuaBh\r\nAelHHBZ17yegogWuvVV5lSnulGuk58SQTIAykb3rPXhRyOTbeRRu8EcNcw3B\r\nEyh2qfwhxu/zHRdJIDqakkDIViC4SmXdKwFHVmNGvLApKvT+bBxO2Cb5ENRv\r\nE2FmR3qeBuYZKdDJlEBRwhxGLAh8RCDHpHGs8PbY/ewrI2QCCYva5GG84ePC\r\n0cx21kS1rStokLWpB9b8C86iWzHEAbv49EpSWZqQ+/izjLrZ3jZpf8NsNULc\r\nre0YfFw3Ij8XWqX57/wDtt1NNbmDCyuS9iiKn0KkLZkQNmJyVHkRyifn54Ts\r\nEkTHe40oYVz/zvkMrPiPmxlAYempgVyU89A=\r\n=z/Ub\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/winston.js", "types": "./index.d.ts", "browser": "./dist/winston", "engines": {"node": ">= 12.0.0"}, "gitHead": "101e2f45057ae16835bcc2f35ca00e77dd004465", "scripts": {"lint": "eslint lib/*.js lib/winston/*.js lib/winston/**/*.js --resolve-plugins-relative-to ./node_modules/@dabh/eslint-config-populist", "test": "mocha", "build": "rimraf dist && babel lib -d dist", "test:unit": "mocha test/unit", "test:coverage": "nyc npm run test:unit", "prepublishOnly": "npm run build", "test:integration": "mocha test/integration"}, "_npmUser": {"name": "w-b-t", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "8.13.0", "description": "A logger for just about everything.", "directories": {}, "_nodeVersion": "16.10.0", "dependencies": {"async": "^3.2.3", "logform": "^2.4.0", "one-time": "^1.0.0", "is-stream": "^2.0.0", "stack-trace": "0.0.x", "triple-beam": "^1.3.0", "@colors/colors": "1.5.0", "readable-stream": "^3.4.0", "@dabh/diagnostics": "^2.0.2", "winston-transport": "^4.5.0", "safe-stable-stringify": "^2.3.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "hock": "^1.4.1", "mocha": "8.1.3", "assume": "^2.2.0", "eslint": "^8.9.0", "rimraf": "^3.0.2", "split2": "^4.1.0", "through2": "^4.0.2", "std-mocks": "^1.0.1", "@babel/cli": "^7.17.0", "@babel/core": "^7.17.2", "@types/node": "^18.0.0", "winston-compat": "^0.1.5", "@babel/preset-env": "^7.16.7", "cross-spawn-async": "^2.2.5", "abstract-winston-transport": "^0.5.1", "@dabh/eslint-config-populist": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/winston_3.8.2_1662567714846_0.5074635028198067", "host": "s3://npm-registry-packages"}}, "2.4.7": {"name": "winston", "version": "2.4.7", "keywords": ["winston", "logging", "sysadmin", "tools"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@2.4.7", "maintainers": [{"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dabh", "email": "<EMAIL>"}, {"name": "w-b-t", "email": "<EMAIL>"}, {"name": "maverick18722", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "5791fe08ea7e90db090f1cb31ef98f32531062f1", "tarball": "https://registry.npmjs.org/winston/-/winston-2.4.7.tgz", "fileCount": 31, "integrity": "sha512-vLB4BqzCKDnnZH9PHGoS2ycawueX4HLqENXQitvFHczhgW2vFpSOn31LZtVr1KU8YTw7DS4tM+cqyovxo8taVg==", "signatures": [{"sig": "MEYCIQCTzjsSc8tyUINWVGOnid+kKdPrNIN15m7ZEwUnHhf3ggIhAPtTlRjNFQzMsHmxpOeSlOuY1sGRV4oRWL0b8gw8+1X2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 175321, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjc8I4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqLjA//WZkXBmUB5ff2bI0A19EJ1cj3XWB2Ne8oItwK2f9a404jGO+Z\r\n7oGds8i8oZM6fhEpmocVNNTcu/VhyoCiNYDtdHKrANzU5KgBYu+meMAcEhHQ\r\nPhCu3Hm63HYQXt6u1vgsMQR/2y9HFPjy/4Cbum0YETnW+qv0saIWR4Q9yADM\r\nnRUzO/fIQ6NQK/0Qrcta/wMFxJsCil9RNjNIttJfgGAVGBGcX1cTtLHh5SGL\r\n3Z2ZeXX3WN4NPgIaRjO8ol4N2j93opoGrf8BYs00V/GELopFoxZVpd6qJFVw\r\noFZ7loCXUIljU667+knKJvEEN6kGsx/wdWcdC93kgjVnEE0dJ3EiSRwcMGgs\r\nVnswRrkZLyzOLGsHzCidDMsGyBbGPvQcu1wZbc1Ap851L6HeDGyEWeHjd3ua\r\nhaZwpuAeDBQM3rmiqSo6Ec2MgTy2j/ahzseDskJGZUdM3RCk9SrY4eTCjj85\r\nhufucxjbXqmlPdycvkRuWIC1kih28GeQEr92oHFLX5cUKC5hBO0eEpojvVlm\r\nKhB25kCeAsAyvn0Jy1ySUfFu5pTX9v2zCjquyRqjdL0bsePvIHgyHfLBCaat\r\nY19qEIkH969IOHkIv/Lf4FVMcv+OSV8ZFWREnyRWe4unnagfoahfa6bjg49N\r\nFs2DOrrK28e5/RJe0pZtTERnJfgPkeL+riU=\r\n=BZAQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/winston", "types": "./index.d.ts", "engines": {"node": ">= 0.10.0"}, "gitHead": "b8baf4c6797d652f882e61a8a3bd8d00875e5596", "scripts": {"test": "vows --dot-matrix --isolate"}, "_npmUser": {"name": "w-b-t", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "8.13.0", "description": "A multi-transport async logging library for Node.js", "directories": {}, "_nodeVersion": "16.10.0", "dependencies": {"eyes": "0.1.x", "async": "^2.6.4", "cycle": "1.0.x", "colors": "1.0.x", "isstream": "0.1.x", "stack-trace": "0.0.x"}, "_hasShrinkwrap": false, "devDependencies": {"hock": "1.x.x", "vows": "0.7.x", "std-mocks": "~1.0.0", "cross-spawn-async": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/winston_2.4.7_1668530744192_0.4441018142471205", "host": "s3://npm-registry-packages"}}, "3.9.0": {"name": "winston", "version": "3.9.0", "keywords": ["winston", "logger", "logging", "logs", "sysadmin", "bunyan", "pino", "loglevel", "tools", "json", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@3.9.0", "maintainers": [{"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dabh", "email": "<EMAIL>"}, {"name": "w-b-t", "email": "<EMAIL>"}, {"name": "maverick18722", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "2bbdeb8167a75fac6d9a0c6d002890cd908016c2", "tarball": "https://registry.npmjs.org/winston/-/winston-3.9.0.tgz", "fileCount": 38, "integrity": "sha512-jW51iW/X95BCW6MMtZWr2jKQBP4hV5bIDq9QrIjfDk6Q9QuxvTKEAlpUNAzP+HYHFFCeENhph16s0zEunu4uuQ==", "signatures": [{"sig": "MEUCIB81keWCweLL1wnyd0iPfJzDAP66drhj8X/QBNfezMOKAiEA2PbfDSsElbZpVc4i8+gRWHw8KjqXJ3FJG4HImLyIiZY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 272445}, "main": "./lib/winston.js", "types": "./index.d.ts", "browser": "./dist/winston", "engines": {"node": ">= 12.0.0"}, "gitHead": "0ed765097dd1f67c7bcaf7e6383f2a3a98e71d9e", "scripts": {"lint": "eslint lib/*.js lib/winston/*.js lib/winston/**/*.js --resolve-plugins-relative-to ./node_modules/@dabh/eslint-config-populist", "test": "mocha", "build": "rimraf dist && babel lib -d dist", "test:unit": "mocha test/unit", "test:coverage": "nyc npm run test:unit", "prepublishOnly": "npm run build", "test:integration": "mocha test/integration"}, "_npmUser": {"name": "w-b-t", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "8.13.0", "description": "A logger for just about everything.", "directories": {}, "_nodeVersion": "18.5.0", "dependencies": {"async": "^3.2.3", "logform": "^2.4.0", "one-time": "^1.0.0", "is-stream": "^2.0.0", "stack-trace": "0.0.x", "triple-beam": "^1.3.0", "@colors/colors": "1.5.0", "readable-stream": "^3.4.0", "@dabh/diagnostics": "^2.0.2", "winston-transport": "^4.5.0", "safe-stable-stringify": "^2.3.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "hock": "^1.4.1", "mocha": "8.1.3", "assume": "^2.2.0", "eslint": "^8.9.0", "rimraf": "^3.0.2", "split2": "^4.1.0", "through2": "^4.0.2", "std-mocks": "^1.0.1", "@babel/cli": "^7.17.0", "@babel/core": "^7.17.2", "@types/node": "^18.0.0", "winston-compat": "^0.1.5", "@babel/preset-env": "^7.16.7", "cross-spawn-async": "^2.2.5", "abstract-winston-transport": "^0.5.1", "@dabh/eslint-config-populist": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/winston_3.9.0_1685118248723_0.5972631655208298", "host": "s3://npm-registry-packages"}}, "3.10.0": {"name": "winston", "version": "3.10.0", "keywords": ["winston", "logger", "logging", "logs", "sysadmin", "bunyan", "pino", "loglevel", "tools", "json", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@3.10.0", "maintainers": [{"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dabh", "email": "<EMAIL>"}, {"name": "w-b-t", "email": "<EMAIL>"}, {"name": "maverick18722", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "d033cb7bd3ced026fed13bf9d92c55b903116803", "tarball": "https://registry.npmjs.org/winston/-/winston-3.10.0.tgz", "fileCount": 38, "integrity": "sha512-nT6SIDaE9B7ZRO0u3UvdrimG0HkB7dSTAgInQnNR2SOPJ4bvq5q79+pXLftKmP52lJGW15+H5MCK0nM9D3KB/g==", "signatures": [{"sig": "MEUCIG0OBDvjZ2l2wyu0KR1KaacjOlugJBl153IPKwg3v57WAiEA6eytEV2Ez1Ef07i3TJMwNMECLTqVN9FesBsp+ubAuXg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 268144}, "main": "./lib/winston.js", "types": "./index.d.ts", "browser": "./dist/winston", "engines": {"node": ">= 12.0.0"}, "gitHead": "19ac9d83bd00e82613d24acc6683a100a24c28dd", "scripts": {"lint": "eslint lib/*.js lib/winston/*.js lib/winston/**/*.js --resolve-plugins-relative-to ./node_modules/@dabh/eslint-config-populist", "test": "mocha", "build": "rimraf dist && babel lib -d dist", "test:unit": "mocha test/unit", "test:coverage": "nyc npm run test:unit", "prepublishOnly": "npm run build", "test:integration": "mocha test/integration"}, "_npmUser": {"name": "dabh", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "9.8.0", "description": "A logger for just about everything.", "directories": {}, "_nodeVersion": "16.18.0", "dependencies": {"async": "^3.2.3", "logform": "^2.4.0", "one-time": "^1.0.0", "is-stream": "^2.0.0", "stack-trace": "0.0.x", "triple-beam": "^1.3.0", "@colors/colors": "1.5.0", "readable-stream": "^3.4.0", "@dabh/diagnostics": "^2.0.2", "winston-transport": "^4.5.0", "safe-stable-stringify": "^2.3.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "hock": "^1.4.1", "mocha": "8.1.3", "assume": "^2.2.0", "eslint": "^8.9.0", "rimraf": "^3.0.2", "split2": "^4.1.0", "through2": "^4.0.2", "std-mocks": "^1.0.1", "@babel/cli": "^7.17.0", "@babel/core": "^7.17.2", "@types/node": "^20.3.1", "winston-compat": "^0.1.5", "@babel/preset-env": "^7.16.7", "cross-spawn-async": "^2.2.5", "abstract-winston-transport": "^0.5.1", "@dabh/eslint-config-populist": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/winston_3.10.0_1689021125720_0.07451708746421515", "host": "s3://npm-registry-packages"}}, "3.11.0": {"name": "winston", "version": "3.11.0", "keywords": ["winston", "logger", "logging", "logs", "sysadmin", "bunyan", "pino", "loglevel", "tools", "json", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@3.11.0", "maintainers": [{"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dabh", "email": "<EMAIL>"}, {"name": "w-b-t", "email": "<EMAIL>"}, {"name": "maverick18722", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "2d50b0a695a2758bb1c95279f0a88e858163ed91", "tarball": "https://registry.npmjs.org/winston/-/winston-3.11.0.tgz", "fileCount": 38, "integrity": "sha512-L3yR6/MzZAOl0DsysUXHVjOwv8mKZ71TrA/41EIduGpOOV5LQVodqN+QdQ6BS6PJ/RdIshZhq84P/fStEZkk7g==", "signatures": [{"sig": "MEYCIQDI4a/AwCAXT4bBNbLGH8ZlxgP7h1A9JXuEqBuhXF9MmQIhAO7/Hp0d2mER1y/+Te9aAux7uukdOBMGIMsLXf++qFLx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 268482}, "main": "./lib/winston.js", "types": "./index.d.ts", "browser": "./dist/winston", "engines": {"node": ">= 12.0.0"}, "gitHead": "1c8c65fd9b8a2762c8fb93b3c2e0a8730faf1a9c", "scripts": {"lint": "eslint lib/*.js lib/winston/*.js lib/winston/**/*.js --resolve-plugins-relative-to ./node_modules/@dabh/eslint-config-populist", "test": "mocha", "build": "rimraf dist && babel lib -d dist", "test:unit": "mocha test/unit", "test:coverage": "nyc npm run test:unit", "prepublishOnly": "npm run build", "test:integration": "mocha test/integration"}, "_npmUser": {"name": "dabh", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "9.8.0", "description": "A logger for just about everything.", "directories": {}, "_nodeVersion": "16.18.0", "dependencies": {"async": "^3.2.3", "logform": "^2.4.0", "one-time": "^1.0.0", "is-stream": "^2.0.0", "stack-trace": "0.0.x", "triple-beam": "^1.3.0", "@colors/colors": "^1.6.0", "readable-stream": "^3.4.0", "@dabh/diagnostics": "^2.0.2", "winston-transport": "^4.5.0", "safe-stable-stringify": "^2.3.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "hock": "^1.4.1", "mocha": "8.1.3", "assume": "^2.2.0", "eslint": "^8.9.0", "rimraf": "^5.0.5", "split2": "^4.1.0", "through2": "^4.0.2", "std-mocks": "^1.0.1", "@babel/cli": "^7.17.0", "@babel/core": "^7.17.2", "@types/node": "^20.3.1", "winston-compat": "^0.1.5", "@babel/preset-env": "^7.16.7", "cross-spawn-async": "^2.2.5", "abstract-winston-transport": "^0.5.1", "@dabh/eslint-config-populist": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/winston_3.11.0_1696708786311_0.29714874000508074", "host": "s3://npm-registry-packages"}}, "3.12.0": {"name": "winston", "version": "3.12.0", "keywords": ["winston", "logger", "logging", "logs", "sysadmin", "bunyan", "pino", "loglevel", "tools", "json", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@3.12.0", "maintainers": [{"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dabh", "email": "<EMAIL>"}, {"name": "w-b-t", "email": "<EMAIL>"}, {"name": "maverick18722", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "a5d965a41d3dc31be5408f8c66e927958846c0d0", "tarball": "https://registry.npmjs.org/winston/-/winston-3.12.0.tgz", "fileCount": 40, "integrity": "sha512-OwbxKaOlESDi01mC9rkM0dQqQt2I8DAUMRLZ/HpbwvDXm85IryEHgoogy5fziQy38PntgZsLlhAYHz//UPHZ5w==", "signatures": [{"sig": "MEYCIQCycxz1n5RefvO3KuTiO2PBvbszFWt8oOz1lvK5LrcqPwIhANCeBXry/WcPRrokAwJCz3/MoDAG4TPpdTD+/2XG37r/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 273514}, "main": "./lib/winston.js", "types": "./index.d.ts", "browser": "./dist/winston", "engines": {"node": ">= 12.0.0"}, "gitHead": "781686928016ec5cd9715a9434f58c795689f867", "scripts": {"lint": "eslint lib/*.js lib/winston/*.js lib/winston/**/*.js --resolve-plugins-relative-to ./node_modules/@dabh/eslint-config-populist", "test": "rimraf test/fixtures/logs/* && mocha", "build": "rimraf dist && babel lib -d dist", "test:unit": "mocha test/unit", "test:coverage": "nyc npm run test:unit", "prepublishOnly": "npm run build", "test:integration": "mocha test/integration"}, "_npmUser": {"name": "dabh", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "9.8.0", "description": "A logger for just about everything.", "directories": {}, "_nodeVersion": "16.18.0", "dependencies": {"async": "^3.2.3", "logform": "^2.4.0", "one-time": "^1.0.0", "is-stream": "^2.0.0", "stack-trace": "0.0.x", "triple-beam": "^1.3.0", "@colors/colors": "^1.6.0", "readable-stream": "^3.4.0", "@dabh/diagnostics": "^2.0.2", "winston-transport": "^4.7.0", "safe-stable-stringify": "^2.3.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "hock": "^1.4.1", "mocha": "^10.3.0", "assume": "^2.2.0", "eslint": "^8.57.0", "rimraf": "^5.0.5", "split2": "^4.1.0", "through2": "^4.0.2", "std-mocks": "^2.0.0", "@babel/cli": "^7.23.9", "@babel/core": "^7.24.0", "@types/node": "^20.11.24", "winston-compat": "^0.1.5", "@babel/preset-env": "^7.24.0", "cross-spawn-async": "^2.2.5", "abstract-winston-transport": "^0.5.1", "@dabh/eslint-config-populist": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/winston_3.12.0_1709516090757_0.8824891120534015", "host": "s3://npm-registry-packages"}}, "3.12.1": {"name": "winston", "version": "3.12.1", "keywords": ["winston", "logger", "logging", "logs", "sysadmin", "bunyan", "pino", "loglevel", "tools", "json", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@3.12.1", "maintainers": [{"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dabh", "email": "<EMAIL>"}, {"name": "w-b-t", "email": "<EMAIL>"}, {"name": "maverick18722", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "b4cef688031275c9e8ef86567586080f88c46bde", "tarball": "https://registry.npmjs.org/winston/-/winston-3.12.1.tgz", "fileCount": 40, "integrity": "sha512-Y<PERSON>ueVCaEX0mUj1n1wBDfLtyyMV4AENJMlcSTx8XWnr9hrzMx4xpyoEKBN9LmgB0O91lREjITM1xnLVYsCzKrmw==", "signatures": [{"sig": "MEUCIHr+PxYQmTWE0adfEfZOhXtqWmyrcwdWI8NfGx+lQUG6AiEA4Bjmoc32YK4o5LP3j/d01x2209mkcwOFoK20QkzAsNA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 273692}, "main": "./lib/winston.js", "types": "./index.d.ts", "browser": "./dist/winston", "engines": {"node": ">= 12.0.0"}, "gitHead": "b5eecf02f27df645f10bdf2e8f1c205fa2d6681b", "scripts": {"lint": "eslint lib/*.js lib/winston/*.js lib/winston/**/*.js --resolve-plugins-relative-to ./node_modules/@dabh/eslint-config-populist", "test": "rimraf test/fixtures/logs/* && mocha", "build": "rimraf dist && babel lib -d dist", "test:unit": "mocha test/unit", "test:coverage": "nyc npm run test:unit", "prepublishOnly": "npm run build", "test:integration": "mocha test/integration"}, "_npmUser": {"name": "dabh", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "A logger for just about everything.", "directories": {}, "_nodeVersion": "18.18.2", "dependencies": {"async": "^3.2.3", "logform": "^2.4.0", "one-time": "^1.0.0", "is-stream": "^2.0.0", "stack-trace": "0.0.x", "triple-beam": "^1.3.0", "@colors/colors": "^1.6.0", "readable-stream": "^3.4.0", "@dabh/diagnostics": "^2.0.2", "winston-transport": "^4.7.0", "safe-stable-stringify": "^2.3.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "hock": "^1.4.1", "mocha": "^10.3.0", "assume": "^2.2.0", "eslint": "^8.57.0", "rimraf": "^5.0.5", "split2": "^4.1.0", "through2": "^4.0.2", "std-mocks": "^2.0.0", "@babel/cli": "^7.23.9", "@babel/core": "^7.24.0", "@types/node": "^20.11.24", "winston-compat": "^0.1.5", "@babel/preset-env": "^7.24.0", "cross-spawn-async": "^2.2.5", "abstract-winston-transport": "^0.5.1", "@dabh/eslint-config-populist": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/winston_3.12.1_1711314470455_0.696360842441178", "host": "s3://npm-registry-packages"}}, "3.13.0": {"name": "winston", "version": "3.13.0", "keywords": ["winston", "logger", "logging", "logs", "sysadmin", "bunyan", "pino", "loglevel", "tools", "json", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@3.13.0", "maintainers": [{"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dabh", "email": "<EMAIL>"}, {"name": "w-b-t", "email": "<EMAIL>"}, {"name": "maverick18722", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "e76c0d722f78e04838158c61adc1287201de7ce3", "tarball": "https://registry.npmjs.org/winston/-/winston-3.13.0.tgz", "fileCount": 40, "integrity": "sha512-rwidmA1w3SE4j0E5MuIufFhyJPBDG7Nu71RkZor1p2+qHvJSZ9GYDA81AyleQcZbh/+V6HjeBdfnTZJm9rSeQQ==", "signatures": [{"sig": "MEQCIGqy3uLWebeT5+yX25h2waZx/lQFxkOvIEZdsWi0n29iAiBfIfV58Toa5K/owPH2KOTFZrlVjizcYWT8v+t+1/7XSw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274111}, "main": "./lib/winston.js", "types": "./index.d.ts", "browser": "./dist/winston", "engines": {"node": ">= 12.0.0"}, "gitHead": "c63a5adb422313d3cc8e173e2df92d7b90c1cf46", "scripts": {"lint": "eslint lib/*.js lib/winston/*.js lib/winston/**/*.js --resolve-plugins-relative-to ./node_modules/@dabh/eslint-config-populist", "test": "rimraf test/fixtures/logs/* && mocha", "build": "rimraf dist && babel lib -d dist", "test:unit": "mocha test/unit", "test:coverage": "nyc npm run test:unit", "prepublishOnly": "npm run build", "test:integration": "mocha test/integration"}, "_npmUser": {"name": "dabh", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "A logger for just about everything.", "directories": {}, "_nodeVersion": "18.18.2", "dependencies": {"async": "^3.2.3", "logform": "^2.4.0", "one-time": "^1.0.0", "is-stream": "^2.0.0", "stack-trace": "0.0.x", "triple-beam": "^1.3.0", "@colors/colors": "^1.6.0", "readable-stream": "^3.4.0", "@dabh/diagnostics": "^2.0.2", "winston-transport": "^4.7.0", "safe-stable-stringify": "^2.3.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "hock": "^1.4.1", "mocha": "^10.3.0", "assume": "^2.2.0", "eslint": "^8.57.0", "rimraf": "^5.0.5", "split2": "^4.1.0", "through2": "^4.0.2", "std-mocks": "^2.0.0", "@babel/cli": "^7.23.9", "@babel/core": "^7.24.0", "@types/node": "^20.11.24", "winston-compat": "^0.1.5", "@babel/preset-env": "^7.24.0", "cross-spawn-async": "^2.2.5", "abstract-winston-transport": "^0.5.1", "@dabh/eslint-config-populist": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/winston_3.13.0_1711315506813_0.8666752967392317", "host": "s3://npm-registry-packages"}}, "3.13.1": {"name": "winston", "version": "3.13.1", "keywords": ["winston", "logger", "logging", "logs", "sysadmin", "bunyan", "pino", "loglevel", "tools", "json", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@3.13.1", "maintainers": [{"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dabh", "email": "<EMAIL>"}, {"name": "w-b-t", "email": "<EMAIL>"}, {"name": "maverick18722", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "53ddadb9c2332eb12cff8306413b3480dc82b6c3", "tarball": "https://registry.npmjs.org/winston/-/winston-3.13.1.tgz", "fileCount": 40, "integrity": "sha512-SvZit7VFNvXRzbqGHsv5KSmgbEYR5EiQfDAL9gxYkRqa934Hnk++zze0wANKtMHcy/gI4W/3xmSDwlhf865WGw==", "signatures": [{"sig": "MEYCIQCEm4+y1PDDTsFjtqQRiLpA4c9XxkA4339siuAuYAhjsAIhAIdjngQQylsnTZY3kDpFZclZT6XOwvKxSLiePSmf0oZk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 269200}, "main": "./lib/winston.js", "types": "./index.d.ts", "browser": "./dist/winston", "engines": {"node": ">= 12.0.0"}, "gitHead": "7d7f9d34546f7bb7c0ef67a8b09ecb82a123bf0b", "scripts": {"lint": "eslint lib/*.js lib/winston/*.js lib/winston/**/*.js --resolve-plugins-relative-to ./node_modules/@dabh/eslint-config-populist", "test": "rimraf test/fixtures/logs/* && mocha", "build": "rimraf dist && babel lib -d dist", "test:unit": "mocha test/unit", "test:coverage": "nyc npm run test:unit", "prepublishOnly": "npm run build", "test:integration": "mocha test/integration"}, "_npmUser": {"name": "dabh", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "A logger for just about everything.", "directories": {}, "_nodeVersion": "18.18.2", "dependencies": {"async": "^3.2.3", "logform": "^2.6.0", "one-time": "^1.0.0", "is-stream": "^2.0.0", "stack-trace": "0.0.x", "triple-beam": "^1.3.0", "@colors/colors": "^1.6.0", "readable-stream": "^3.4.0", "@dabh/diagnostics": "^2.0.2", "winston-transport": "^4.7.0", "safe-stable-stringify": "^2.3.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "hock": "^1.4.1", "mocha": "^10.3.0", "assume": "^2.2.0", "eslint": "^8.57.0", "rimraf": "5.0.1", "split2": "^4.1.0", "through2": "^4.0.2", "std-mocks": "^2.0.0", "@babel/cli": "^7.23.9", "@babel/core": "^7.24.0", "@types/node": "^20.11.24", "winston-compat": "^0.1.5", "@babel/preset-env": "^7.24.0", "cross-spawn-async": "^2.2.5", "abstract-winston-transport": "^0.5.1", "@dabh/eslint-config-populist": "^4.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/winston_3.13.1_1720646023156_0.0387869075106444", "host": "s3://npm-registry-packages"}}, "3.14.0": {"name": "winston", "version": "3.14.0", "keywords": ["winston", "logger", "logging", "logs", "sysadmin", "bunyan", "pino", "loglevel", "tools", "json", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@3.14.0", "maintainers": [{"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dabh", "email": "<EMAIL>"}, {"name": "w-b-t", "email": "<EMAIL>"}, {"name": "maverick18722", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "589e5dd6b458ae4ef0a4dd0190a6c077488f1cee", "tarball": "https://registry.npmjs.org/winston/-/winston-3.14.0.tgz", "fileCount": 40, "integrity": "sha512-XEJvmKJglhTW2TgfpKdkpj0119Yn5AClR7LJ0rBNUQFx20mNQj3s1ukTA1i77q+YBaHYbcKtXpxgPqfdUPCIYA==", "signatures": [{"sig": "MEUCIB2bq9Pug9DK9ou+YZ0R1k6UP+GvCTnUa2bne08c05kuAiEArbzhDT6EvfDvdZ3TZn19KTcjDDvQvkReSZJkZF7Gaao=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 269299}, "main": "./lib/winston.js", "types": "./index.d.ts", "browser": "./dist/winston", "engines": {"node": ">= 12.0.0"}, "gitHead": "c4a15ec02394b6b1e7376fe2979cf1efa6a849f0", "scripts": {"lint": "eslint lib/*.js lib/winston/*.js lib/winston/**/*.js --resolve-plugins-relative-to ./node_modules/@dabh/eslint-config-populist", "test": "rimraf test/fixtures/logs/* && mocha", "build": "rimraf dist && babel lib -d dist", "test:unit": "mocha test/unit", "test:coverage": "nyc npm run test:unit", "prepublishOnly": "npm run build", "test:integration": "mocha test/integration"}, "_npmUser": {"name": "dabh", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A logger for just about everything.", "directories": {}, "_nodeVersion": "20.15.1", "dependencies": {"async": "^3.2.3", "logform": "^2.6.0", "one-time": "^1.0.0", "is-stream": "^2.0.0", "stack-trace": "0.0.x", "triple-beam": "^1.3.0", "@colors/colors": "^1.6.0", "readable-stream": "^3.4.0", "@dabh/diagnostics": "^2.0.2", "winston-transport": "^4.7.0", "safe-stable-stringify": "^2.3.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "hock": "^1.4.1", "mocha": "^10.3.0", "assume": "^2.2.0", "eslint": "^8.57.0", "rimraf": "5.0.1", "split2": "^4.1.0", "through2": "^4.0.2", "std-mocks": "^2.0.0", "@babel/cli": "^7.23.9", "@babel/core": "^7.24.0", "@types/node": "^20.11.24", "winston-compat": "^0.1.5", "@babel/preset-env": "^7.24.0", "cross-spawn-async": "^2.2.5", "abstract-winston-transport": "^0.5.1", "@dabh/eslint-config-populist": "^4.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/winston_3.14.0_1723086899182_0.4186650709458528", "host": "s3://npm-registry-packages"}}, "3.14.1": {"name": "winston", "version": "3.14.1", "keywords": ["winston", "logger", "logging", "logs", "sysadmin", "bunyan", "pino", "loglevel", "tools", "json", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@3.14.1", "maintainers": [{"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dabh", "email": "<EMAIL>"}, {"name": "w-b-t", "email": "<EMAIL>"}, {"name": "maverick18722", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "b296f2756e6b46d3b6faac5660d2af878fc3f666", "tarball": "https://registry.npmjs.org/winston/-/winston-3.14.1.tgz", "fileCount": 40, "integrity": "sha512-CJi4Il/msz8HkdDfXOMu+r5Au/oyEjFiOZzbX2d23hRLY0narGjqfE5lFlrT5hfYJhPtM8b85/GNFsxIML/RVA==", "signatures": [{"sig": "MEYCIQDwhqKEt3Ie78J86PGaGbZ7kG7UUv1sIARM5nB+cdUCkQIhANe3gfQ2lhs/vGSDSZ0JyamG/1Xl6yF5D3ZK7Lyr7s+G", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 271245}, "main": "./lib/winston.js", "types": "./index.d.ts", "browser": "./dist/winston", "engines": {"node": ">= 12.0.0"}, "gitHead": "b270c4ee8ad8dd7098cf45d64cdab4489512dc8d", "scripts": {"lint": "eslint lib/*.js lib/winston/*.js lib/winston/**/*.js --resolve-plugins-relative-to ./node_modules/@dabh/eslint-config-populist", "test": "rimraf test/fixtures/logs/* && mocha", "build": "rimraf dist && babel lib -d dist", "test:unit": "mocha test/unit", "test:coverage": "nyc npm run test:unit", "prepublishOnly": "npm run build", "test:integration": "mocha test/integration"}, "_npmUser": {"name": "dabh", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A logger for just about everything.", "directories": {}, "_nodeVersion": "20.15.1", "dependencies": {"async": "^3.2.3", "logform": "^2.6.0", "one-time": "^1.0.0", "is-stream": "^2.0.0", "stack-trace": "0.0.x", "triple-beam": "^1.3.0", "@colors/colors": "^1.6.0", "readable-stream": "^3.4.0", "@dabh/diagnostics": "^2.0.2", "winston-transport": "^4.7.0", "safe-stable-stringify": "^2.3.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "hock": "^1.4.1", "mocha": "^10.3.0", "assume": "^2.2.0", "eslint": "^8.57.0", "rimraf": "5.0.1", "split2": "^4.1.0", "through2": "^4.0.2", "std-mocks": "^2.0.0", "@babel/cli": "^7.23.9", "@babel/core": "^7.24.0", "@types/node": "^20.11.24", "winston-compat": "^0.1.5", "@babel/preset-env": "^7.24.0", "cross-spawn-async": "^2.2.5", "abstract-winston-transport": "^0.5.1", "@dabh/eslint-config-populist": "^4.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/winston_3.14.1_1723145650249_0.7446055586858491", "host": "s3://npm-registry-packages"}}, "3.14.2": {"name": "winston", "version": "3.14.2", "keywords": ["winston", "logger", "logging", "logs", "sysadmin", "bunyan", "pino", "loglevel", "tools", "json", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@3.14.2", "maintainers": [{"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dabh", "email": "<EMAIL>"}, {"name": "w-b-t", "email": "<EMAIL>"}, {"name": "maverick18722", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "94ce5fd26d374f563c969d12f0cd9c641065adab", "tarball": "https://registry.npmjs.org/winston/-/winston-3.14.2.tgz", "fileCount": 40, "integrity": "sha512-CO8cdpBB2yqzEf8v895L+GNKYJiEq8eKlHU38af3snQBQ+sdAIUepjMSguOIJC7ICbzm0ZI+Af2If4vIJrtmOg==", "signatures": [{"sig": "MEQCIFq1UnDttyUpDyNMflryL8sLd3AzG4mvv8G3Qg/yCeEeAiBemFoT9vldoK1zDju1I0sNcXbBpMH2Qlq65yonbTQa4A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 271035}, "main": "./lib/winston.js", "types": "./index.d.ts", "browser": "./dist/winston", "engines": {"node": ">= 12.0.0"}, "gitHead": "51a45515b64f700273c007768c680bc664008d04", "scripts": {"lint": "eslint lib/*.js lib/winston/*.js lib/winston/**/*.js --resolve-plugins-relative-to ./node_modules/@dabh/eslint-config-populist", "test": "rimraf test/fixtures/logs/* && mocha", "build": "rimraf dist && babel lib -d dist", "test:unit": "mocha test/unit", "test:coverage": "nyc npm run test:unit", "prepublishOnly": "npm run build", "test:integration": "mocha test/integration"}, "_npmUser": {"name": "dabh", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A logger for just about everything.", "directories": {}, "_nodeVersion": "20.15.1", "dependencies": {"async": "^3.2.3", "logform": "^2.6.0", "one-time": "^1.0.0", "is-stream": "^2.0.0", "stack-trace": "0.0.x", "triple-beam": "^1.3.0", "@colors/colors": "^1.6.0", "readable-stream": "^3.4.0", "@dabh/diagnostics": "^2.0.2", "winston-transport": "^4.7.0", "safe-stable-stringify": "^2.3.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "hock": "^1.4.1", "mocha": "^10.3.0", "assume": "^2.2.0", "eslint": "^8.57.0", "rimraf": "5.0.1", "split2": "^4.1.0", "through2": "^4.0.2", "std-mocks": "^2.0.0", "@babel/cli": "^7.23.9", "@babel/core": "^7.24.0", "@types/node": "^20.11.24", "winston-compat": "^0.1.5", "@babel/preset-env": "^7.24.0", "cross-spawn-async": "^2.2.5", "abstract-winston-transport": "^0.5.1", "@dabh/eslint-config-populist": "^4.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/winston_3.14.2_1723655591730_0.5015363383868205", "host": "s3://npm-registry-packages"}}, "3.15.0": {"name": "winston", "version": "3.15.0", "keywords": ["winston", "logger", "logging", "logs", "sysadmin", "bunyan", "pino", "loglevel", "tools", "json", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@3.15.0", "maintainers": [{"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dabh", "email": "<EMAIL>"}, {"name": "w-b-t", "email": "<EMAIL>"}, {"name": "maverick18722", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "4df7b70be091bc1a38a4f45b969fa79589b73ff5", "tarball": "https://registry.npmjs.org/winston/-/winston-3.15.0.tgz", "fileCount": 40, "integrity": "sha512-RhruH2Cj0bV0WgNL+lOfoUBI4DVfdUNjVnJGVovWZmrcKtrFTTRzgXYK2O9cymSGjrERCtaAeHwMNnUWXlwZow==", "signatures": [{"sig": "MEUCICggzx3DVrpYeI+6Bf5DEMWqVLkCu0osF2h3SSGrCEDlAiEA8CKP7G3THq+y8AyF18WHKVAX1HBqHZd+OaYCQQtIO1Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 270947}, "main": "./lib/winston.js", "types": "./index.d.ts", "browser": "./dist/winston", "engines": {"node": ">= 12.0.0"}, "gitHead": "195e55c7e7fc58914ae4967ea7b832c9e0ced930", "scripts": {"lint": "eslint lib/*.js lib/winston/*.js lib/winston/**/*.js --resolve-plugins-relative-to ./node_modules/@dabh/eslint-config-populist", "test": "rimraf test/fixtures/logs/* && mocha", "build": "rimraf dist && babel lib -d dist", "test:unit": "mocha test/unit", "test:coverage": "nyc npm run test:unit", "prepublishOnly": "npm run build", "test:integration": "mocha test/integration"}, "_npmUser": {"name": "dabh", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "A logger for just about everything.", "directories": {}, "_nodeVersion": "22.4.1", "dependencies": {"async": "^3.2.3", "logform": "^2.6.0", "one-time": "^1.0.0", "is-stream": "^2.0.0", "stack-trace": "0.0.x", "triple-beam": "^1.3.0", "@colors/colors": "^1.6.0", "readable-stream": "^3.4.0", "@dabh/diagnostics": "^2.0.2", "winston-transport": "^4.7.0", "safe-stable-stringify": "^2.3.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^17.1.0", "hock": "^1.4.1", "mocha": "^10.3.0", "assume": "^2.2.0", "eslint": "^8.57.0", "rimraf": "5.0.1", "split2": "^4.1.0", "through2": "^4.0.2", "std-mocks": "^2.0.0", "@babel/cli": "^7.23.9", "@babel/core": "^7.24.0", "@types/node": "^20.11.24", "winston-compat": "^0.1.5", "@babel/preset-env": "^7.24.0", "cross-spawn-async": "^2.2.5", "abstract-winston-transport": "^0.5.1", "@dabh/eslint-config-populist": "^4.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/winston_3.15.0_1728104225674_0.6921257041326974", "host": "s3://npm-registry-packages"}}, "3.16.0": {"name": "winston", "version": "3.16.0", "keywords": ["winston", "logger", "logging", "logs", "sysadmin", "bunyan", "pino", "loglevel", "tools", "json", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "winston@3.16.0", "maintainers": [{"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dabh", "email": "<EMAIL>"}, {"name": "w-b-t", "email": "<EMAIL>"}, {"name": "maverick18722", "email": "<EMAIL>"}], "homepage": "https://github.com/winstonjs/winston#readme", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "dist": {"shasum": "d11caabada87b7d4b59aba9a94b882121b773f9b", "tarball": "https://registry.npmjs.org/winston/-/winston-3.16.0.tgz", "fileCount": 40, "integrity": "sha512-xz7+cyGN5M+4CmmD4Npq1/4T+UZaz7HaeTlAruFUTjk79CNMq+P6H30vlE4z0qfqJ01VHYQwd7OZo03nYm/+lg==", "signatures": [{"sig": "MEUCIQCXr/UHzbvHxWeNGIVCJMTf0MWhMVwib+rJ6VOpriMspgIgSpCodOxNagSE6iXVTWTZP+Wqu3wg9qKIQ39mKA/ApZE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 271047}, "main": "./lib/winston.js", "types": "./index.d.ts", "browser": "./dist/winston", "engines": {"node": ">= 12.0.0"}, "gitHead": "06749a96115a0ed75783761e596754ab7ec9d16b", "scripts": {"lint": "eslint lib/*.js lib/winston/*.js lib/winston/**/*.js --resolve-plugins-relative-to ./node_modules/@dabh/eslint-config-populist", "test": "rimraf test/fixtures/logs/* && mocha", "build": "rimraf dist && babel lib -d dist", "test:unit": "mocha test/unit", "test:coverage": "nyc npm run test:unit", "prepublishOnly": "npm run build", "test:integration": "mocha test/integration"}, "_npmUser": {"name": "dabh", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/winstonjs/winston.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "A logger for just about everything.", "directories": {}, "_nodeVersion": "22.4.1", "dependencies": {"async": "^3.2.3", "logform": "^2.6.0", "one-time": "^1.0.0", "is-stream": "^2.0.0", "stack-trace": "0.0.x", "triple-beam": "^1.3.0", "@colors/colors": "^1.6.0", "readable-stream": "^3.4.0", "@dabh/diagnostics": "^2.0.2", "winston-transport": "^4.7.0", "safe-stable-stringify": "^2.3.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^17.1.0", "hock": "^1.4.1", "mocha": "^10.3.0", "assume": "^2.2.0", "eslint": "^8.57.0", "rimraf": "5.0.1", "split2": "^4.1.0", "through2": "^4.0.2", "std-mocks": "^2.0.0", "@babel/cli": "^7.23.9", "@babel/core": "^7.24.0", "@types/node": "^20.11.24", "winston-compat": "^0.1.5", "@babel/preset-env": "^7.24.0", "cross-spawn-async": "^2.2.5", "abstract-winston-transport": "^0.5.1", "@dabh/eslint-config-populist": "^4.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/winston_3.16.0_1730495681647_0.2884207250598687", "host": "s3://npm-registry-packages"}}, "3.17.0": {"name": "winston", "description": "A logger for just about everything.", "version": "3.17.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dabh", "email": "<EMAIL>"}, {"name": "w-b-t", "email": "<EMAIL>"}, {"name": "maverick18722", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/winstonjs/winston.git"}, "keywords": ["winston", "logger", "logging", "logs", "sysadmin", "bunyan", "pino", "loglevel", "tools", "json", "stream"], "dependencies": {"@dabh/diagnostics": "^2.0.2", "@colors/colors": "^1.6.0", "async": "^3.2.3", "is-stream": "^2.0.0", "logform": "^2.7.0", "one-time": "^1.0.0", "readable-stream": "^3.4.0", "safe-stable-stringify": "^2.3.1", "stack-trace": "0.0.x", "triple-beam": "^1.3.0", "winston-transport": "^4.9.0"}, "devDependencies": {"@babel/cli": "^7.23.9", "@babel/core": "^7.24.0", "@babel/preset-env": "^7.24.0", "@dabh/eslint-config-populist": "^4.4.0", "@types/node": "^20.11.24", "abstract-winston-transport": "^0.5.1", "assume": "^2.2.0", "cross-spawn-async": "^2.2.5", "eslint": "^8.57.0", "hock": "^1.4.1", "mocha": "^10.3.0", "nyc": "^17.1.0", "rimraf": "5.0.1", "split2": "^4.1.0", "std-mocks": "^2.0.0", "through2": "^4.0.2", "winston-compat": "^0.1.5"}, "main": "./lib/winston.js", "browser": "./dist/winston", "types": "./index.d.ts", "scripts": {"lint": "eslint lib/*.js lib/winston/*.js lib/winston/**/*.js --resolve-plugins-relative-to ./node_modules/@dabh/eslint-config-populist", "test": "rimraf test/fixtures/logs/* && mocha", "test:coverage": "nyc npm run test:unit", "test:unit": "mocha test/unit", "test:integration": "mocha test/integration", "build": "rimraf dist && babel lib -d dist", "prepublishOnly": "npm run build"}, "engines": {"node": ">= 12.0.0"}, "license": "MIT", "_id": "winston@3.17.0", "gitHead": "1b9cef8c7d76e4c15d47997d2c4199c31e9d277a", "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "homepage": "https://github.com/winstonjs/winston#readme", "_nodeVersion": "22.4.1", "_npmVersion": "10.8.1", "dist": {"integrity": "sha512-DLiFIXYC5fMPxaRg832S6F5mJYvePtmO5G9v9IgUFPhXm9/GkXarH/TUrBAVzhTCzAj9anE/+GjrgXp/54nOgw==", "shasum": "74b8665ce9b4ea7b29d0922cfccf852a08a11423", "tarball": "https://registry.npmjs.org/winston/-/winston-3.17.0.tgz", "fileCount": 40, "unpackedSize": 271047, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGtp1jqWHsLfi3DtLz2EMQe/NOExP2n+TJPAQB15uJ13AiA3tanTLKagJNHQglegp9pyRk0Wuj9SmyaR3GREbWfxqw=="}]}, "_npmUser": {"name": "dabh", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/winston_3.17.0_1731207575114_0.8378214508170119"}, "_hasShrinkwrap": false}}, "time": {"created": "2011-01-18T20:45:37.131Z", "modified": "2024-11-10T02:59:35.503Z", "0.1.0": "2011-01-18T20:45:38.390Z", "0.1.1": "2011-01-21T23:13:54.329Z", "0.1.2": "2011-01-23T08:06:43.393Z", "0.1.3": "2011-01-25T03:38:16.585Z", "0.1.4": "2011-01-29T18:39:11.805Z", "0.2.0": "2011-02-02T20:27:37.640Z", "0.2.1": "2011-02-07T06:01:46.325Z", "0.2.2": "2011-02-16T09:42:58.012Z", "0.2.3": "2011-02-18T00:56:53.038Z", "0.2.4": "2011-03-04T04:57:49.832Z", "0.2.5": "2011-03-05T20:47:10.687Z", "0.2.6": "2011-03-27T21:27:26.947Z", "0.2.7": "2011-04-10T01:08:45.046Z", "0.2.8": "2011-05-20T01:46:40.672Z", "0.2.9": "2011-05-20T02:32:21.372Z", "0.2.10": "2011-05-29T03:58:23.814Z", "0.2.11": "2011-05-30T02:03:04.360Z", "0.3.0": "2011-06-07T09:31:50.865Z", "0.3.1": "2011-06-08T07:31:24.555Z", "0.3.2": "2011-06-24T03:22:24.882Z", "0.3.3": "2011-07-24T05:51:41.205Z", "0.3.4": "2011-08-04T06:17:10.075Z", "0.3.5": "2011-08-09T14:59:56.544Z", "0.4.0": "2011-08-22T10:33:34.512Z", "0.4.1": "2011-09-11T05:00:42.302Z", "0.5.0": "2011-09-12T17:31:47.276Z", "0.5.1": "2011-09-13T09:35:25.860Z", "0.5.2": "2011-09-15T05:14:00.793Z", "0.5.3": "2011-09-23T19:27:10.866Z", "0.5.4": "2011-10-07T23:53:50.549Z", "0.5.5": "2011-10-09T19:29:17.799Z", "0.5.6": "2011-10-22T06:12:53.069Z", "0.5.7": "2011-11-20T20:53:45.416Z", "0.5.8": "2011-11-30T07:02:21.061Z", "0.5.9": "2011-12-02T09:44:54.777Z", "0.5.10": "2012-02-22T05:21:53.531Z", "0.5.11": "2012-03-23T23:59:42.494Z", "0.6.1": "2012-05-31T19:27:44.116Z", "0.6.2": "2012-07-08T07:02:22.663Z", "0.7.0": "2013-04-21T05:57:56.796Z", "0.7.1": "2013-04-21T10:39:21.275Z", "0.7.2": "2013-06-14T15:12:35.844Z", "0.7.3": "2014-03-25T05:31:53.165Z", "0.8.0": "2014-09-15T19:43:37.312Z", "0.8.1": "2014-10-06T20:37:40.790Z", "0.8.2": "2014-11-04T22:05:29.713Z", "0.8.3": "2014-11-04T23:45:58.648Z", "0.9.0": "2015-02-03T18:53:28.304Z", "1.0.0": "2015-04-07T23:02:53.645Z", "1.0.1": "2015-06-27T01:50:07.404Z", "1.0.2": "2015-09-25T22:01:50.066Z", "1.1.0": "2015-10-09T15:08:35.400Z", "1.1.1": "2015-10-18T17:26:00.110Z", "1.1.2": "2015-10-29T09:00:24.773Z", "2.0.0": "2015-10-30T01:30:42.064Z", "2.0.1": "2015-11-03T07:36:35.149Z", "2.1.0": "2015-11-04T06:01:39.648Z", "2.1.1": "2015-11-18T21:52:04.196Z", "2.2.0": "2016-02-25T22:23:20.901Z", "2.3.0": "2016-11-02T04:27:16.329Z", "2.3.1": "2017-01-20T18:38:19.965Z", "2.4.0": "2017-10-02T04:59:14.456Z", "3.0.0-rc0": "2017-10-02T05:17:16.169Z", "3.0.0-rc1": "2017-10-19T20:17:19.718Z", "2.4.1": "2018-03-09T19:07:17.315Z", "3.0.0-rc2": "2018-03-09T19:18:05.047Z", "3.0.0-rc3": "2018-03-16T17:59:40.002Z", "3.0.0-rc4": "2018-04-06T17:16:53.520Z", "2.4.2": "2018-04-20T16:55:06.628Z", "3.0.0-rc5": "2018-04-20T19:44:18.499Z", "3.0.0-rc6": "2018-05-30T06:01:07.464Z", "2.4.3": "2018-06-12T09:53:04.261Z", "3.0.0": "2018-06-12T16:31:26.893Z", "2.4.4": "2018-08-21T21:22:07.552Z", "3.0.1": "2018-09-04T14:05:15.969Z", "3.1.0": "2018-09-04T14:08:03.077Z", "3.2.0": "2019-01-26T19:48:48.618Z", "3.2.1": "2019-01-29T19:04:11.622Z", "3.3.0": "2020-06-21T08:52:21.841Z", "2.4.5": "2020-06-22T06:18:26.822Z", "3.3.1": "2020-06-22T06:29:30.106Z", "3.3.2": "2020-06-22T17:53:46.802Z", "3.3.3": "2020-06-23T22:31:24.942Z", "3.3.4": "2022-01-10T02:35:24.306Z", "3.4.0": "2022-01-10T22:26:13.717Z", "3.5.0": "2022-01-27T16:27:39.472Z", "3.5.1": "2022-01-31T20:58:10.976Z", "3.6.0": "2022-02-12T19:10:57.173Z", "3.7.1": "2022-04-04T14:10:54.719Z", "3.7.2": "2022-04-04T22:14:14.739Z", "2.4.6": "2022-04-28T20:44:26.386Z", "3.8.0": "2022-06-23T15:25:48.281Z", "3.8.1": "2022-06-30T19:32:48.601Z", "3.8.2": "2022-09-07T16:21:55.075Z", "2.4.7": "2022-11-15T16:45:44.373Z", "3.9.0": "2023-05-26T16:24:08.967Z", "3.10.0": "2023-07-10T20:32:05.941Z", "3.11.0": "2023-10-07T19:59:46.490Z", "3.12.0": "2024-03-04T01:34:50.931Z", "3.12.1": "2024-03-24T21:07:50.680Z", "3.13.0": "2024-03-24T21:25:06.995Z", "3.13.1": "2024-07-10T21:13:43.347Z", "3.14.0": "2024-08-08T03:14:59.354Z", "3.14.1": "2024-08-08T19:34:10.454Z", "3.14.2": "2024-08-14T17:13:11.910Z", "3.15.0": "2024-10-05T04:57:05.887Z", "3.16.0": "2024-11-01T21:14:41.879Z", "3.17.0": "2024-11-10T02:59:35.319Z"}, "bugs": {"url": "https://github.com/winstonjs/winston/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/winstonjs/winston#readme", "keywords": ["winston", "logger", "logging", "logs", "sysadmin", "bunyan", "pino", "loglevel", "tools", "json", "stream"], "repository": {"type": "git", "url": "git+https://github.com/winstonjs/winston.git"}, "description": "A logger for just about everything.", "maintainers": [{"name": "chjj", "email": "chjje<PERSON><PERSON>@gmail.com"}, {"name": "jcrugzz", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "indexzero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dabh", "email": "<EMAIL>"}, {"name": "w-b-t", "email": "<EMAIL>"}, {"name": "maverick18722", "email": "<EMAIL>"}], "readme": "# winston\n\nA logger for just about everything.\n\n[![Version npm](https://img.shields.io/npm/v/winston.svg?style=flat-square)](https://www.npmjs.com/package/winston)\n[![npm Downloads](https://img.shields.io/npm/dm/winston.svg?style=flat-square)](https://npmcharts.com/compare/winston?minimal=true)\n[![build status](https://github.com/winstonjs/winston/actions/workflows/ci.yml/badge.svg)](https://github.com/winstonjs/winston/actions/workflows/ci.yml)\n[![coverage status](https://coveralls.io/repos/github/winstonjs/winston/badge.svg?branch=master)](https://coveralls.io/github/winstonjs/winston?branch=master)\n\n[![NPM](https://nodei.co/npm/winston.png?downloads=true&downloadRank=true)](https://nodei.co/npm/winston/)\n\n## winston@3\n\nSee the [Upgrade Guide](UPGRADE-3.0.md) for more information. Bug reports and\nPRs welcome!\n\n## Looking for `winston@2.x` documentation?\n\nPlease note that the documentation below is for `winston@3`.\n[Read the `winston@2.x` documentation].\n\n## Motivation\n\n`winston` is designed to be a simple and universal logging library with\nsupport for multiple transports. A transport is essentially a storage device\nfor your logs. Each `winston` logger can have multiple transports (see:\n[Transports]) configured at different levels (see: [Logging levels]). For\nexample, one may want error logs to be stored in a persistent remote location\n(like a database), but all logs output to the console or a local file.\n\n`winston` aims to decouple parts of the logging process to make it more\nflexible and extensible. Attention is given to supporting flexibility in log\nformatting (see: [Formats]) & levels (see: [Using custom logging levels]), and\nensuring those APIs decoupled from the implementation of transport logging\n(i.e. how the logs are stored / indexed, see: [Adding Custom Transports]) to\nthe API that they exposed to the programmer.\n\n## Quick Start\n\nTL;DR? Check out the [quick start example][quick-example] in `./examples/`.\nThere are a number of other examples in [`./examples/*.js`][examples].\nDon't see an example you think should be there? Submit a pull request\nto add it!\n\n## Usage\n\nThe recommended way to use `winston` is to create your own logger. The\nsimplest way to do this is using `winston.createLogger`:\n\n``` js\nconst winston = require('winston');\n\nconst logger = winston.createLogger({\n  level: 'info',\n  format: winston.format.json(),\n  defaultMeta: { service: 'user-service' },\n  transports: [\n    //\n    // - Write all logs with importance level of `error` or higher to `error.log`\n    //   (i.e., error, fatal, but not other levels)\n    //\n    new winston.transports.File({ filename: 'error.log', level: 'error' }),\n    //\n    // - Write all logs with importance level of `info` or higher to `combined.log`\n    //   (i.e., fatal, error, warn, and info, but not trace)\n    //\n    new winston.transports.File({ filename: 'combined.log' }),\n  ],\n});\n\n//\n// If we're not in production then log to the `console` with the format:\n// `${info.level}: ${info.message} JSON.stringify({ ...rest }) `\n//\nif (process.env.NODE_ENV !== 'production') {\n  logger.add(new winston.transports.Console({\n    format: winston.format.simple(),\n  }));\n}\n```\n\nYou may also log directly via the default logger exposed by\n`require('winston')`, but this merely intended to be a convenient shared\nlogger to use throughout your application if you so choose.\nNote that the default logger doesn't have any transports by default.\nYou need add transports by yourself, and leaving the default logger without any\ntransports may produce a high memory usage issue.\n\n## Table of contents\n\n* [Motivation](#motivation)\n* [Quick Start](#quick-start)\n* [Usage](#usage)\n* [Table of Contents](#table-of-contents)\n* [Logging](#logging)\n  * [Creating your logger](#creating-your-own-logger)\n  * [Streams, `objectMode`, and `info` objects](#streams-objectmode-and-info-objects)\n* [Formats]\n  * [Combining formats](#combining-formats)\n  * [String interpolation](#string-interpolation)\n  * [Filtering `info` Objects](#filtering-info-objects)\n  * [Creating custom formats](#creating-custom-formats)\n* [Logging levels]\n  * [Using logging levels](#using-logging-levels)\n  * [Using custom logging levels](#using-custom-logging-levels)\n* [Transports]\n  * [Multiple transports of the same type](#multiple-transports-of-the-same-type)\n  * [Adding Custom Transports](#adding-custom-transports)\n  * [Common Transport options](#common-transport-options)\n* [Exceptions](#exceptions)\n  * [Handling Uncaught Exceptions with winston](#handling-uncaught-exceptions-with-winston)\n  * [To Exit or Not to Exit](#to-exit-or-not-to-exit)\n* [Rejections](#rejections)\n  * [Handling Uncaught Promise Rejections with winston](#handling-uncaught-promise-rejections-with-winston)\n* [Profiling](#profiling)\n* [Streaming Logs](#streaming-logs)\n* [Querying Logs](#querying-logs)\n* [Further Reading](#further-reading)\n  * [Using the default logger](#using-the-default-logger)\n  * [Awaiting logs to be written in `winston`](#awaiting-logs-to-be-written-in-winston)\n  * [Working with multiple Loggers in `winston`](#working-with-multiple-loggers-in-winston)\n  * [Routing Console transport messages to the console instead of stdout and stderr](#routing-console-transport-messages-to-the-console-instead-of-stdout-and-stderr)\n* [Installation](#installation)\n* [Run Tests](#run-tests)\n\n## Logging\n\nLogging levels in `winston` conform to the severity ordering specified by\n[RFC5424]: _severity of all levels is assumed to be numerically **ascending**\nfrom most important to least important._\n\n``` js\nconst levels = {\n  error: 0,\n  warn: 1,\n  info: 2,\n  http: 3,\n  verbose: 4,\n  debug: 5,\n  silly: 6\n};\n```\n\n### Creating your own Logger\nYou get started by creating a logger using `winston.createLogger`:\n\n``` js\nconst logger = winston.createLogger({\n  transports: [\n    new winston.transports.Console(),\n    new winston.transports.File({ filename: 'combined.log' })\n  ]\n});\n```\n\nA logger accepts the following parameters:\n\n| Name          | Default                     |  Description    |\n| ------------- | --------------------------- | --------------- |\n| `level`       | `'info'`                    | Log only if [`info.level`](#streams-objectmode-and-info-objects) is less than or equal to this level  |\n| `levels`      | `winston.config.npm.levels` | Levels (and colors) representing log priorities            |\n| `format`      | `winston.format.json`       | Formatting for `info` messages  (see: [Formats])           |\n| `transports`  | `[]` _(No transports)_      | Set of logging targets for `info` messages                 |\n| `exitOnError` | `true`                      | If false, handled exceptions will not cause `process.exit` |\n| `silent`      | `false`                     | If true, all logs are suppressed |\n\nThe levels provided to `createLogger` will be defined as convenience methods\non the `logger` returned.\n\n``` js\n//\n// Logging\n//\nlogger.log({\n  level: 'info',\n  message: 'Hello distributed log files!'\n});\n\nlogger.info('Hello again distributed logs');\n```\n\nYou can add or remove transports from the `logger` once it has been provided\nto you from `winston.createLogger`:\n\n``` js\nconst files = new winston.transports.File({ filename: 'combined.log' });\nconst console = new winston.transports.Console();\n\nlogger\n  .clear()          // Remove all transports\n  .add(console)     // Add console transport\n  .add(files)       // Add file transport\n  .remove(console); // Remove console transport\n```\n\nYou can also wholesale reconfigure a `winston.Logger` instance using the\n`configure` method:\n\n``` js\nconst logger = winston.createLogger({\n  level: 'info',\n  transports: [\n    new winston.transports.Console(),\n    new winston.transports.File({ filename: 'combined.log' })\n  ]\n});\n\n//\n// Replaces the previous transports with those in the\n// new configuration wholesale.\n//\nconst DailyRotateFile = require('winston-daily-rotate-file');\nlogger.configure({\n  level: 'verbose',\n  transports: [\n    new DailyRotateFile(opts)\n  ]\n});\n```\n\n### Creating child loggers\n\nYou can create child loggers from existing loggers to pass metadata overrides:\n\n``` js\nconst logger = winston.createLogger({\n  transports: [\n    new winston.transports.Console(),\n  ]\n});\n\nconst childLogger = logger.child({ requestId: '451' });\n```\n> `.child` is likely to be bugged if you're also extending the `Logger` class, due to some implementation details that make `this` keyword to point to unexpected things. Use with caution.\n\n### Streams, `objectMode`, and `info` objects\n\nIn `winston`, both `Logger` and `Transport` instances are treated as\n[`objectMode`](https://nodejs.org/api/stream.html#stream_object_mode)\nstreams that accept an `info` object.\n\nThe `info` parameter provided to a given format represents a single log\nmessage. The object itself is mutable. Every `info` must have at least the\n`level` and `message` properties:\n\n``` js\nconst info = {\n  level: 'info',                 // Level of the logging message\n  message: 'Hey! Log something?' // Descriptive message being logged.\n};\n```\n\nProperties **besides level and message** are considered as \"`meta`\". i.e.:\n\n``` js\nconst { level, message, ...meta } = info;\n```\n\nSeveral of the formats in `logform` itself add additional properties:\n\n| Property    | Format added by | Description |\n| ----------- | --------------- | ----------- |\n| `splat`     | `splat()`       | String interpolation splat for `%d %s`-style messages. |\n| `timestamp` | `timestamp()`   |  timestamp the message was received. |\n| `label`     | `label()`       | Custom label associated with each message. |\n| `ms`        | `ms()`          | Number of milliseconds since the previous log message. |\n\nAs a consumer you may add whatever properties you wish – _internal state is\nmaintained by `Symbol` properties:_\n\n- `Symbol.for('level')` _**(READ-ONLY)**:_ equal to `level` property.\n  **Is treated as immutable by all code.**\n- `Symbol.for('message'):` complete string message set by \"finalizing formats\":\n  - `json`\n  - `logstash`\n  - `printf`\n  - `prettyPrint`\n  - `simple`\n- `Symbol.for('splat')`: additional string interpolation arguments. _Used\n  exclusively by `splat()` format._\n\nThese Symbols are stored in another package: `triple-beam` so that all\nconsumers of `logform` can have the same Symbol reference. i.e.:\n\n``` js\nconst { LEVEL, MESSAGE, SPLAT } = require('triple-beam');\n\nconsole.log(LEVEL === Symbol.for('level'));\n// true\n\nconsole.log(MESSAGE === Symbol.for('message'));\n// true\n\nconsole.log(SPLAT === Symbol.for('splat'));\n// true\n```\n\n> **NOTE:** any `{ message }` property in a `meta` object provided will\n> automatically be concatenated to any `msg` already provided: For\n> example the below will concatenate 'world' onto 'hello':\n>\n> ``` js\n> logger.log('error', 'hello', { message: 'world' });\n> logger.info('hello', { message: 'world' });\n> ```\n\n## Formats\n\nFormats in `winston` can be accessed from `winston.format`. They are\nimplemented in [`logform`](https://github.com/winstonjs/logform), a separate\nmodule from `winston`. This allows flexibility when writing your own transports\nin case you wish to include a default format with your transport.\n\nIn modern versions of `node` template strings are very performant and are the\nrecommended way for doing most end-user formatting. If you want to bespoke\nformat your logs, `winston.format.printf` is for you:\n\n``` js\nconst { createLogger, format, transports } = require('winston');\nconst { combine, timestamp, label, printf } = format;\n\nconst myFormat = printf(({ level, message, label, timestamp }) => {\n  return `${timestamp} [${label}] ${level}: ${message}`;\n});\n\nconst logger = createLogger({\n  format: combine(\n    label({ label: 'right meow!' }),\n    timestamp(),\n    myFormat\n  ),\n  transports: [new transports.Console()]\n});\n```\n\nTo see what built-in formats are available and learn more about creating your\nown custom logging formats, see [`logform`][logform].\n\n### Combining formats\n\nAny number of formats may be combined into a single format using\n`format.combine`. Since `format.combine` takes no `opts`, as a convenience it\nreturns pre-created instance of the combined format.\n\n``` js\nconst { createLogger, format, transports } = require('winston');\nconst { combine, timestamp, label, prettyPrint } = format;\n\nconst logger = createLogger({\n  format: combine(\n    label({ label: 'right meow!' }),\n    timestamp(),\n    prettyPrint()\n  ),\n  transports: [new transports.Console()]\n})\n\nlogger.log({\n  level: 'info',\n  message: 'What time is the testing at?'\n});\n// Outputs:\n// { level: 'info',\n//   message: 'What time is the testing at?',\n//   label: 'right meow!',\n//   timestamp: '2017-09-30T03:57:26.875Z' }\n```\n\n### String interpolation\n\nThe `log` method provides the string interpolation using [util.format]. **It\nmust be enabled using `format.splat()`.**\n\nBelow is an example that defines a format with string interpolation of\nmessages using `format.splat` and then serializes the entire `info` message\nusing `format.simple`.\n\n``` js\nconst { createLogger, format, transports } = require('winston');\nconst logger = createLogger({\n  format: format.combine(\n    format.splat(),\n    format.simple()\n  ),\n  transports: [new transports.Console()]\n});\n\n// info: test message my string {}\nlogger.log('info', 'test message %s', 'my string');\n\n// info: test message 123 {}\nlogger.log('info', 'test message %d', 123);\n\n// info: test message first second {number: 123}\nlogger.log('info', 'test message %s, %s', 'first', 'second', { number: 123 });\n```\n\n### Filtering `info` Objects\n\nIf you wish to filter out a given `info` Object completely when logging then\nsimply return a falsey value.\n\n``` js\nconst { createLogger, format, transports } = require('winston');\n\n// Ignore log messages if they have { private: true }\nconst ignorePrivate = format((info, opts) => {\n  if (info.private) { return false; }\n  return info;\n});\n\nconst logger = createLogger({\n  format: format.combine(\n    ignorePrivate(),\n    format.json()\n  ),\n  transports: [new transports.Console()]\n});\n\n// Outputs: {\"level\":\"error\",\"message\":\"Public error to share\"}\nlogger.log({\n  level: 'error',\n  message: 'Public error to share'\n});\n\n// Messages with { private: true } will not be written when logged.\nlogger.log({\n  private: true,\n  level: 'error',\n  message: 'This is super secret - hide it.'\n});\n```\n\nUse of `format.combine` will respect any falsey values return and stop\nevaluation of later formats in the series. For example:\n\n``` js\nconst { format } = require('winston');\nconst { combine, timestamp, label } = format;\n\nconst willNeverThrow = format.combine(\n  format(info => { return false })(), // Ignores everything\n  format(info => { throw new Error('Never reached') })()\n);\n```\n\n### Creating custom formats\n\nFormats are prototypal objects (i.e. class instances) that define a single\nmethod: `transform(info, opts)` and return the mutated `info`:\n\n- `info`: an object representing the log message.\n- `opts`: setting specific to the current instance of the format.\n\nThey are expected to return one of two things:\n\n- **An `info` Object** representing the modified `info` argument. Object\nreferences need not be preserved if immutability is preferred. All current\nbuilt-in formats consider `info` mutable, but [immutablejs] is being\nconsidered for future releases.\n- **A falsey value** indicating that the `info` argument should be ignored by the\ncaller. (See: [Filtering `info` Objects](#filtering-info-objects)) below.\n\n`winston.format` is designed to be as simple as possible. To define a new\nformat, simply pass it a `transform(info, opts)` function to get a new\n`Format`.\n\nThe named `Format` returned can be used to create as many copies of the given\n`Format` as desired:\n\n``` js\nconst { format } = require('winston');\n\nconst volume = format((info, opts) => {\n  if (opts.yell) {\n    info.message = info.message.toUpperCase();\n  } else if (opts.whisper) {\n    info.message = info.message.toLowerCase();\n  }\n\n  return info;\n});\n\n// `volume` is now a function that returns instances of the format.\nconst scream = volume({ yell: true });\nconsole.dir(scream.transform({\n  level: 'info',\n  message: `sorry for making you YELL in your head!`\n}, scream.options));\n// {\n//   level: 'info'\n//   message: 'SORRY FOR MAKING YOU YELL IN YOUR HEAD!'\n// }\n\n// `volume` can be used multiple times to create different formats.\nconst whisper = volume({ whisper: true });\nconsole.dir(whisper.transform({\n  level: 'info',\n  message: `WHY ARE THEY MAKING US YELL SO MUCH!`\n}, whisper.options));\n// {\n//   level: 'info'\n//   message: 'why are they making us yell so much!'\n// }\n```\n\n## Logging Levels\n\nLogging levels in `winston` conform to the severity ordering specified by\n[RFC5424]: _severity of all levels is assumed to be numerically **ascending**\nfrom most important to least important._\n\nEach `level` is given a specific integer priority. The higher the priority the\nmore important the message is considered to be, and the lower the\ncorresponding integer priority.  For example, as specified exactly in RFC5424\nthe `syslog` levels are prioritized from 0 to 7 (highest to lowest).\n\n```js\n{\n  emerg: 0,\n  alert: 1,\n  crit: 2,\n  error: 3,\n  warning: 4,\n  notice: 5,\n  info: 6,\n  debug: 7\n}\n```\n\nSimilarly, `npm` logging levels are prioritized from 0 to 6 (highest to\nlowest):\n\n``` js\n{\n  error: 0,\n  warn: 1,\n  info: 2,\n  http: 3,\n  verbose: 4,\n  debug: 5,\n  silly: 6\n}\n```\n\nIf you do not explicitly define the levels that `winston` should use, the\n`npm` levels above will be used.\n\n### Using Logging Levels\n\nSetting the level for your logging message can be accomplished in one of two\nways. You can pass a string representing the logging level to the log() method\nor use the level specified methods defined on every winston Logger.\n\n``` js\n//\n// Any logger instance\n//\nlogger.log('silly', \"127.0.0.1 - there's no place like home\");\nlogger.log('debug', \"127.0.0.1 - there's no place like home\");\nlogger.log('verbose', \"127.0.0.1 - there's no place like home\");\nlogger.log('info', \"127.0.0.1 - there's no place like home\");\nlogger.log('warn', \"127.0.0.1 - there's no place like home\");\nlogger.log('error', \"127.0.0.1 - there's no place like home\");\nlogger.info(\"127.0.0.1 - there's no place like home\");\nlogger.warn(\"127.0.0.1 - there's no place like home\");\nlogger.error(\"127.0.0.1 - there's no place like home\");\n\n//\n// Default logger\n//\nwinston.log('info', \"127.0.0.1 - there's no place like home\");\nwinston.info(\"127.0.0.1 - there's no place like home\");\n```\n\n`winston` allows you to define a `level` property on each transport which\nspecifies the **maximum** level of messages that a transport should log. For\nexample, using the `syslog` levels you could log only `error` messages to the\nconsole and everything `info` and below to a file (which includes `error`\nmessages):\n\n``` js\nconst logger = winston.createLogger({\n  levels: winston.config.syslog.levels,\n  transports: [\n    new winston.transports.Console({ level: 'error' }),\n    new winston.transports.File({\n      filename: 'combined.log',\n      level: 'info'\n    })\n  ]\n});\n```\n\nYou may also dynamically change the log level of a transport:\n\n``` js\nconst transports = {\n  console: new winston.transports.Console({ level: 'warn' }),\n  file: new winston.transports.File({ filename: 'combined.log', level: 'error' })\n};\n\nconst logger = winston.createLogger({\n  transports: [\n    transports.console,\n    transports.file\n  ]\n});\n\nlogger.info('Will not be logged in either transport!');\ntransports.console.level = 'info';\ntransports.file.level = 'info';\nlogger.info('Will be logged in both transports!');\n```\n\n`winston` supports customizable logging levels, defaulting to npm style\nlogging levels. Levels must be specified at the time of creating your logger.\n\n### Using Custom Logging Levels\n\nIn addition to the predefined `npm`, `syslog`, and `cli` levels available in\n`winston`, you can also choose to define your own:\n\n``` js\nconst myCustomLevels = {\n  levels: {\n    foo: 0,\n    bar: 1,\n    baz: 2,\n    foobar: 3\n  },\n  colors: {\n    foo: 'blue',\n    bar: 'green',\n    baz: 'yellow',\n    foobar: 'red'\n  }\n};\n\nconst customLevelLogger = winston.createLogger({\n  levels: myCustomLevels.levels\n});\n\ncustomLevelLogger.foobar('some foobar level-ed message');\n```\n\nAlthough there is slight repetition in this data structure, it enables simple\nencapsulation if you do not want to have colors. If you do wish to have\ncolors, in addition to passing the levels to the Logger itself, you must make\nwinston aware of them:\n\n``` js\nwinston.addColors(myCustomLevels.colors);\n```\n\nThis enables loggers using the `colorize` formatter to appropriately color and style\nthe output of custom levels.\n\nAdditionally, you can also change background color and font style.\nFor example,\n``` js\nbaz: 'italic yellow',\nfoobar: 'bold red cyanBG'\n```\n\nPossible options are below.\n\n* Font styles: `bold`, `dim`, `italic`, `underline`, `inverse`, `hidden`,\n  `strikethrough`.\n\n* Font foreground colors: `black`, `red`, `green`, `yellow`, `blue`, `magenta`,\n  `cyan`, `white`, `gray`, `grey`.\n\n* Background colors: `blackBG`, `redBG`, `greenBG`, `yellowBG`, `blueBG`\n  `magentaBG`, `cyanBG`, `whiteBG`\n\n### Colorizing Standard logging levels\n\nTo colorize the standard logging level add\n```js\nwinston.format.combine(\n  winston.format.colorize(),\n  winston.format.simple()\n);\n```\nwhere `winston.format.simple()` is whatever other formatter you want to use.  The `colorize` formatter must come before any formatters adding text you wish to color.\n\n### Colorizing full log line when json formatting logs\n\nTo colorize the full log line with the json formatter you can apply the following\n\n```js\nwinston.format.combine(\n  winston.format.json(),\n  winston.format.colorize({ all: true })\n);\n```\n\n## Transports\n\nThere are several [core transports] included in  `winston`, which leverage the\nbuilt-in networking and file I/O offered by Node.js core. In addition, there\nare [additional transports] written by members of the community.\n\n## Multiple transports of the same type\n\nIt is possible to use multiple transports of the same type e.g.\n`winston.transports.File` when you construct the transport.\n\n``` js\nconst logger = winston.createLogger({\n  transports: [\n    new winston.transports.File({\n      filename: 'combined.log',\n      level: 'info'\n    }),\n    new winston.transports.File({\n      filename: 'errors.log',\n      level: 'error'\n    })\n  ]\n});\n```\n\nIf you later want to remove one of these transports you can do so by using the\ntransport itself. e.g.:\n\n``` js\nconst combinedLogs = logger.transports.find(transport => {\n  return transport.filename === 'combined.log'\n});\n\nlogger.remove(combinedLogs);\n```\n\n## Adding Custom Transports\n\nAdding a custom transport is easy. All you need to do is accept any options\nyou need, implement a log() method, and consume it with `winston`.\n\n``` js\nconst Transport = require('winston-transport');\nconst util = require('util');\n\n//\n// Inherit from `winston-transport` so you can take advantage\n// of the base functionality and `.exceptions.handle()`.\n//\nmodule.exports = class YourCustomTransport extends Transport {\n  constructor(opts) {\n    super(opts);\n    //\n    // Consume any custom options here. e.g.:\n    // - Connection information for databases\n    // - Authentication information for APIs (e.g. loggly, papertrail,\n    //   logentries, etc.).\n    //\n  }\n\n  log(info, callback) {\n    setImmediate(() => {\n      this.emit('logged', info);\n    });\n\n    // Perform the writing to the remote service\n    callback();\n  }\n};\n```\n\n## Common Transport options\n\nAs every transport inherits from [winston-transport], it's possible to set\na custom format and a custom log level on each transport separately:\n\n``` js\nconst logger = winston.createLogger({\n  transports: [\n    new winston.transports.File({\n      filename: 'error.log',\n      level: 'error',\n      format: winston.format.json()\n    }),\n    new winston.transports.Http({\n      level: 'warn',\n      format: winston.format.json()\n    }),\n    new winston.transports.Console({\n      level: 'info',\n      format: winston.format.combine(\n        winston.format.colorize(),\n        winston.format.simple()\n      )\n    })\n  ]\n});\n```\n\n## Exceptions\n\n### Handling Uncaught Exceptions with winston\n\nWith `winston`, it is possible to catch and log `uncaughtException` events\nfrom your process. With your own logger instance you can enable this behavior\nwhen it's created or later on in your applications lifecycle:\n\n``` js\nconst { createLogger, transports } = require('winston');\n\n// Enable exception handling when you create your logger.\nconst logger = createLogger({\n  transports: [\n    new transports.File({ filename: 'combined.log' })\n  ],\n  exceptionHandlers: [\n    new transports.File({ filename: 'exceptions.log' })\n  ]\n});\n\n// Or enable it later on by adding a transport or using `.exceptions.handle`\nconst logger = createLogger({\n  transports: [\n    new transports.File({ filename: 'combined.log' })\n  ]\n});\n\n// Call exceptions.handle with a transport to handle exceptions\nlogger.exceptions.handle(\n  new transports.File({ filename: 'exceptions.log' })\n);\n```\n\nIf you want to use this feature with the default logger, simply call\n`.exceptions.handle()` with a transport instance.\n\n``` js\n//\n// You can add a separate exception logger by passing it to `.exceptions.handle`\n//\nwinston.exceptions.handle(\n  new winston.transports.File({ filename: 'path/to/exceptions.log' })\n);\n\n//\n// Alternatively you can set `handleExceptions` to true when adding transports\n// to winston.\n//\nwinston.add(new winston.transports.File({\n  filename: 'path/to/combined.log',\n  handleExceptions: true\n}));\n```\n\n### To Exit or Not to Exit\n\nBy default, winston will exit after logging an uncaughtException. If this is\nnot the behavior you want, set `exitOnError = false`\n\n``` js\nconst logger = winston.createLogger({ exitOnError: false });\n\n//\n// or, like this:\n//\nlogger.exitOnError = false;\n```\n\nWhen working with custom logger instances, you can pass in separate transports\nto the `exceptionHandlers` property or set `handleExceptions` on any\ntransport.\n\n##### Example 1\n\n``` js\nconst logger = winston.createLogger({\n  transports: [\n    new winston.transports.File({ filename: 'path/to/combined.log' })\n  ],\n  exceptionHandlers: [\n    new winston.transports.File({ filename: 'path/to/exceptions.log' })\n  ]\n});\n```\n\n##### Example 2\n\n``` js\nconst logger = winston.createLogger({\n  transports: [\n    new winston.transports.Console({\n      handleExceptions: true\n    })\n  ],\n  exitOnError: false\n});\n```\n\nThe `exitOnError` option can also be a function to prevent exit on only\ncertain types of errors:\n\n``` js\nfunction ignoreEpipe(err) {\n  return err.code !== 'EPIPE';\n}\n\nconst logger = winston.createLogger({ exitOnError: ignoreEpipe });\n\n//\n// or, like this:\n//\nlogger.exitOnError = ignoreEpipe;\n```\n\n## Rejections\n\n### Handling Uncaught Promise Rejections with winston\n\nWith `winston`, it is possible to catch and log `unhandledRejection` events\nfrom your process. With your own logger instance you can enable this behavior\nwhen it's created or later on in your applications lifecycle:\n\n``` js\nconst { createLogger, transports } = require('winston');\n\n// Enable rejection handling when you create your logger.\nconst logger = createLogger({\n  transports: [\n    new transports.File({ filename: 'combined.log' })\n  ],\n  rejectionHandlers: [\n    new transports.File({ filename: 'rejections.log' })\n  ]\n});\n\n// Or enable it later on by adding a transport or using `.rejections.handle`\nconst logger = createLogger({\n  transports: [\n    new transports.File({ filename: 'combined.log' })\n  ]\n});\n\n// Call rejections.handle with a transport to handle rejections\nlogger.rejections.handle(\n  new transports.File({ filename: 'rejections.log' })\n);\n```\n\nIf you want to use this feature with the default logger, simply call\n`.rejections.handle()` with a transport instance.\n\n``` js\n//\n// You can add a separate rejection logger by passing it to `.rejections.handle`\n//\nwinston.rejections.handle(\n  new winston.transports.File({ filename: 'path/to/rejections.log' })\n);\n\n//\n// Alternatively you can set `handleRejections` to true when adding transports\n// to winston.\n//\nwinston.add(new winston.transports.File({\n  filename: 'path/to/combined.log',\n  handleRejections: true\n}));\n```\n\n## Profiling\n\nIn addition to logging messages and metadata, `winston` also has a simple\nprofiling mechanism implemented for any logger:\n\n``` js\n//\n// Start profile of 'test'\n//\nlogger.profile('test');\n\nsetTimeout(function () {\n  //\n  // Stop profile of 'test'. Logging will now take place:\n  //   '17 Jan 21:00:00 - info: test duration=1000ms'\n  //\n  logger.profile('test');\n}, 1000);\n```\n\nAlso you can start a timer and keep a reference that you can call `.done()`\non:\n\n``` js\n // Returns an object corresponding to a specific timing. When done\n // is called the timer will finish and log the duration. e.g.:\n //\n const profiler = logger.startTimer();\n setTimeout(function () {\n   profiler.done({ message: 'Logging message' });\n }, 1000);\n```\n\nAll profile messages are set to 'info' level by default, and both message and\nmetadata are optional.  For individual profile messages, you can override the default log level by supplying a metadata object with a `level` property:\n\n```js\nlogger.profile('test', { level: 'debug' });\n```\n\n## Querying Logs\n\n`winston` supports querying of logs with Loggly-like options. [See Loggly\nSearch API](https://www.loggly.com/docs/api-retrieving-data/). Specifically:\n`File`, `Couchdb`, `Redis`, `Loggly`, `Nssocket`, and `Http`.\n\n``` js\nconst options = {\n  from: new Date() - (24 * 60 * 60 * 1000),\n  until: new Date(),\n  limit: 10,\n  start: 0,\n  order: 'desc',\n  fields: ['message']\n};\n\n//\n// Find items logged between today and yesterday.\n//\nlogger.query(options, function (err, results) {\n  if (err) {\n    /* TODO: handle me */\n    throw err;\n  }\n\n  console.log(results);\n});\n```\n\n## Streaming Logs\nStreaming allows you to stream your logs back from your chosen transport.\n\n``` js\n//\n// Start at the end.\n//\nwinston.stream({ start: -1 }).on('log', function(log) {\n  console.log(log);\n});\n```\n\n## Further Reading\n\n### Using the Default Logger\n\nThe default logger is accessible through the `winston` module directly. Any\nmethod that you could call on an instance of a logger is available on the\ndefault logger:\n\n``` js\nconst winston = require('winston');\n\nwinston.log('info', 'Hello distributed log files!');\nwinston.info('Hello again distributed logs');\n\nwinston.level = 'debug';\nwinston.log('debug', 'Now my debug messages are written to console!');\n```\n\nBy default, no transports are set on the default logger. You must\nadd or remove transports via the `add()` and `remove()` methods:\n\n``` js\nconst files = new winston.transports.File({ filename: 'combined.log' });\nconst console = new winston.transports.Console();\n\nwinston.add(console);\nwinston.add(files);\nwinston.remove(console);\n```\n\nOr do it with one call to configure():\n\n``` js\nwinston.configure({\n  transports: [\n    new winston.transports.File({ filename: 'somefile.log' })\n  ]\n});\n```\n\nFor more documentation about working with each individual transport supported\nby `winston` see the [`winston` Transports](docs/transports.md) document.\n\n### Awaiting logs to be written in `winston`\n\nOften it is useful to wait for your logs to be written before exiting the\nprocess. Each instance of `winston.Logger` is also a [Node.js stream]. A\n`finish` event will be raised when all logs have flushed to all transports\nafter the stream has been ended.\n\n``` js\nconst transport = new winston.transports.Console();\nconst logger = winston.createLogger({\n  transports: [transport]\n});\n\nlogger.on('finish', function (info) {\n  // All `info` log messages has now been logged\n});\n\nlogger.info('CHILL WINSTON!', { seriously: true });\nlogger.end();\n```\n\nIt is also worth mentioning that the logger also emits an 'error' event\nif an error occurs within the logger itself which\nyou should handle or suppress if you don't want unhandled exceptions:\n\n``` js\n//\n// Handle errors originating in the logger itself\n//\nlogger.on('error', function (err) { /* Do Something */ });\n```\n\n### Working with multiple Loggers in winston\n\nOften in larger, more complex, applications it is necessary to have multiple\nlogger instances with different settings. Each logger is responsible for a\ndifferent feature area (or category). This is exposed in `winston` in two\nways: through `winston.loggers` and instances of `winston.Container`. In fact,\n`winston.loggers` is just a predefined instance of `winston.Container`:\n\n``` js\nconst winston = require('winston');\nconst { format } = winston;\nconst { combine, label, json } = format;\n\n//\n// Configure the logger for `category1`\n//\nwinston.loggers.add('category1', {\n  format: combine(\n    label({ label: 'category one' }),\n    json()\n  ),\n  transports: [\n    new winston.transports.Console({ level: 'silly' }),\n    new winston.transports.File({ filename: 'somefile.log' })\n  ]\n});\n\n//\n// Configure the logger for `category2`\n//\nwinston.loggers.add('category2', {\n  format: combine(\n    label({ label: 'category two' }),\n    json()\n  ),\n  transports: [\n    new winston.transports.Http({ host: 'localhost', port:8080 })\n  ]\n});\n```\n\nNow that your loggers are setup, you can require winston _in any file in your\napplication_ and access these pre-configured loggers:\n\n``` js\nconst winston = require('winston');\n\n//\n// Grab your preconfigured loggers\n//\nconst category1 = winston.loggers.get('category1');\nconst category2 = winston.loggers.get('category2');\n\ncategory1.info('logging to file and console transports');\ncategory2.info('logging to http transport');\n```\n\nIf you prefer to manage the `Container` yourself, you can simply instantiate one:\n\n``` js\nconst winston = require('winston');\nconst { format } = winston;\nconst { combine, label, json } = format;\n\nconst container = new winston.Container();\n\ncontainer.add('category1', {\n  format: combine(\n    label({ label: 'category one' }),\n    json()\n  ),\n  transports: [\n    new winston.transports.Console({ level: 'silly' }),\n    new winston.transports.File({ filename: 'somefile.log' })\n  ]\n});\n\nconst category1 = container.get('category1');\ncategory1.info('logging to file and console transports');\n```\n\n### Routing Console transport messages to the console instead of stdout and stderr\n\nBy default the `winston.transports.Console` transport sends messages to `stdout` and `stderr`. This\nis fine in most situations; however, there are some cases where this isn't desirable, including:\n\n- Debugging using VSCode and attaching to, rather than launching, a Node.js process\n- Writing JSON format messages in AWS Lambda\n- Logging during Jest tests with the `--silent` option\n\nTo make the transport log use `console.log()`, `console.warn()` and `console.error()`\ninstead, set the `forceConsole` option to `true`:\n\n```js\nconst logger = winston.createLogger({\n  level: 'info',\n  transports: [new winston.transports.Console({ forceConsole: true })]\n});\n```\n\n## Installation\n\n``` bash\nnpm install winston\n```\n\n``` bash\nyarn add winston\n```\n\n## Run Tests\n\nAll of the winston tests are written with [`mocha`][mocha], [`nyc`][nyc], and\n[`assume`][assume].  They can be run with `npm`.\n\n``` bash\nnpm test\n```\n\n#### Author: [Charlie Robbins]\n#### Contributors: [Jarrett Cruger], [David Hyde], [Chris Alderson]\n\n[Transports]: #transports\n[Logging levels]: #logging-levels\n[Formats]: #formats\n[Using custom logging levels]: #using-custom-logging-levels\n[Adding Custom Transports]: #adding-custom-transports\n[core transports]: docs/transports.md#winston-core\n[additional transports]: docs/transports.md#additional-transports\n\n[RFC5424]: https://tools.ietf.org/html/rfc5424\n[util.format]: https://nodejs.org/dist/latest/docs/api/util.html#util_util_format_format_args\n[mocha]: https://mochajs.org\n[nyc]: https://github.com/istanbuljs/nyc\n[assume]: https://github.com/bigpipe/assume\n[logform]: https://github.com/winstonjs/logform#readme\n[winston-transport]: https://github.com/winstonjs/winston-transport\n\n[Read the `winston@2.x` documentation]: https://github.com/winstonjs/winston/tree/2.x\n\n[quick-example]: https://github.com/winstonjs/winston/blob/master/examples/quick-start.js\n[examples]: https://github.com/winstonjs/winston/tree/master/examples\n\n[Charlie Robbins]: http://github.com/indexzero\n[Jarrett Cruger]: https://github.com/jcrugzz\n[David Hyde]: https://github.com/dabh\n[Chris Alderson]: https://github.com/chrisalderson", "readmeFilename": "README.md", "users": {"52u": true, "dm7": true, "hut": true, "jk0": true, "nex": true, "viz": true, "ymk": true, "boto": true, "dbck": true, "fill": true, "j3kz": true, "jueb": true, "meme": true, "mikl": true, "neo1": true, "nuer": true, "owaz": true, "shan": true, "tshm": true, "vasc": true, "vwal": true, "xtat": true, "arefm": true, "bhill": true, "brend": true, "dabin": true, "deryk": true, "dmitr": true, "fm-96": true, "hanhq": true, "havvy": true, "holly": true, "junos": true, "miloc": true, "ndxbn": true, "noste": true, "panlw": true, "r3nya": true, "roxnz": true, "russt": true, "sbskl": true, "sharp": true, "skozz": true, "sopov": true, "stany": true, "wdhif": true, "wujr5": true, "yswon": true, "zckrs": true, "zorak": true, "0x4c3p": true, "456wyc": true, "akarem": true, "alek-s": true, "alucky": true, "booyaa": true, "bpatel": true, "buzuli": true, "bvaccc": true, "chaowi": true, "colwob": true, "daizch": true, "dkblay": true, "efreak": true, "eins78": true, "emyann": true, "fenyot": true, "figroc": true, "formix": true, "gabeio": true, "gaveho": true, "gdbtek": true, "glebec": true, "godion": true, "inoder": true, "isa424": true, "itsakt": true, "itskdk": true, "jeseab": true, "jmkim9": true, "joe.li": true, "kenovi": true, "king.v": true, "knoja4": true, "kraihn": true, "kratam": true, "kricis": true, "leesei": true, "leshik": true, "lestad": true, "lfdo20": true, "m0dred": true, "manten": true, "mixvit": true, "mp2526": true, "mrbgit": true, "mrsarm": true, "nauhil": true, "noccer": true, "novalu": true, "paragi": true, "pigiuz": true, "progre": true, "quafoo": true, "robftw": true, "s3than": true, "seanjh": true, "sitnin": true, "summer": true, "tchcxp": true, "tcrowe": true, "techfe": true, "tedyhy": true, "tomi77": true, "vegera": true, "vutran": true, "xlaoyu": true, "xmwx38": true, "zerodi": true, "alfeo92": true, "algonzo": true, "anoubis": true, "azevedo": true, "banyudu": true, "chzhewl": true, "dac2205": true, "dnp1204": true, "donotor": true, "eludwig": true, "endsoul": true, "ferrari": true, "firefox": true, "forrert": true, "fredtma": true, "fxkraus": true, "good318": true, "hagb4rd": true, "hisplan": true, "itonyyo": true, "jcottam": true, "jcowgar": true, "jkrenge": true, "keybouh": true, "kimemin": true, "kontrax": true, "mrosata": true, "ngpixel": true, "nilz3ro": true, "pdedkov": true, "pdostal": true, "ryanlee": true, "serioga": true, "sgvinci": true, "sonanui": true, "spalger": true, "statico": true, "staydan": true, "strydom": true, "sunggun": true, "taskone": true, "tbear79": true, "tomchao": true, "tophsic": true, "ungurys": true, "webmato": true, "xfloops": true, "xtx1130": true, "yanghcc": true, "abhisekp": true, "adilibre": true, "akinjide": true, "amthenia": true, "bapinney": true, "behumble": true, "bouchezb": true, "buru1020": true, "byoigres": true, "cetincem": true, "damianof": true, "dgarlitt": true, "dgolovin": true, "dizlexik": true, "djviolin": true, "drossman": true, "elrolito": true, "elussich": true, "enriched": true, "gwilison": true, "harutsos": true, "heineiuo": true, "jdpagley": true, "jmsherry": true, "jonathas": true, "jsumners": true, "koalaylj": true, "konklone": true, "koskokos": true, "krzych93": true, "losymear": true, "macdaddy": true, "makenova": true, "masonwan": true, "mhaidarh": true, "mluberry": true, "moimikey": true, "nalindak": true, "nashi007": true, "nicknaso": true, "petecemi": true, "philosec": true, "rannmann": true, "redbe4rd": true, "savostin": true, "shaddyhm": true, "shahzaib": true, "shiva127": true, "sibawite": true, "sixertoy": true, "softwind": true, "stephn_r": true, "stoneren": true, "tdmalone": true, "tmurngon": true, "vchouhan": true, "waitfish": true, "wolfbyte": true, "wongulus": true, "xiaobing": true, "yash3492": true, "1two3code": true, "alexios3d": true, "amenadiel": true, "antixrist": true, "bethrezen": true, "cdelmoral": true, "cilindrox": true, "cunningdj": true, "davequick": true, "diwushi33": true, "dosevader": true, "dracochou": true, "erincinci": true, "evanyeung": true, "fgribreau": true, "henryfour": true, "iceriver2": true, "ishitcno1": true, "jedateach": true, "jhillacre": true, "jmatthews": true, "juriwiens": true, "kirikou93": true, "largepuma": true, "luiscauro": true, "madalozzo": true, "mariusc23": true, "mastayoda": true, "maxoumask": true, "maxwelldu": true, "megadrive": true, "mgesmundo": true, "mikedfunk": true, "mikemimik": true, "mikestaub": true, "mjurincic": true, "mstaessen": true, "nickeljew": true, "phpjsnerd": true, "pingjiang": true, "ramzesucr": true, "redstrike": true, "rosterloh": true, "ruyadorno": true, "rylan_yan": true, "samlaudev": true, "sasquatch": true, "shakakira": true, "shujianbu": true, "slmcassio": true, "spekkionu": true, "tonikhil1": true, "trewaters": true, "wukaidong": true, "xiaokaike": true, "zeroth007": true, "abdihaikal": true, "ajohnstone": true, "amdsouza92": true, "andreaspag": true, "andriecool": true, "anilcs0405": true, "aquiandres": true, "arjanaswal": true, "ashish.npm": true, "axelrindle": true, "bboulahdid": true, "blakmatrix": true, "bruinebeer": true, "cestrensem": true, "chinaqstar": true, "dhenderson": true, "dwayneford": true, "ericnelson": true, "errohitagg": true, "franksansc": true, "gerst20051": true, "haaaiiimmm": true, "incendiary": true, "isaacvitor": true, "john.pinch": true, "junjiansyu": true, "kappuccino": true, "land-melon": true, "langri-sha": true, "lewisbrown": true, "lijinghust": true, "lintungwei": true, "lwgojustgo": true, "manikantag": true, "manumartor": true, "markmedown": true, "misterioss": true, "monolithed": true, "mysticatea": true, "ocd_lionel": true, "princetoad": true, "raycharles": true, "roberkules": true, "rocket0191": true, "samirdamle": true, "seanpowell": true, "selenasong": true, "shreyawhiz": true, "stephenhuh": true, "tangweikun": true, "tomasgvivo": true, "tomjamescn": true, "wenhsiaoyi": true, "yasinaydin": true, "zhanghaili": true, "ziliwesley": true, "acollins-ts": true, "ahmed-dinar": true, "ahsanshafiq": true, "antonnguyen": true, "calldanfeng": true, "cedrickchee": true, "christtseng": true, "coolhanddev": true, "dselmanovic": true, "eldersantos": true, "fengbeijing": true, "flumpus-dev": true, "frontmoment": true, "garenyondem": true, "goulash1971": true, "he313572052": true, "highgravity": true, "jjeffwarner": true, "josealmeida": true, "kurt.pattyn": true, "lukicdarkoo": true, "m80126colin": true, "phoenix-xsy": true, "ragingsmurf": true, "raisiqueira": true, "scytalezero": true, "soenkekluth": true, "t.tiercelin": true, "thangakumar": true, "themadjoker": true, "vparaskevas": true, "wangnan0610": true, "ww522413622": true, "zhangskills": true, "zixinliango": true, "bobjohnson23": true, "danielknaust": true, "dominik.palo": true, "einfallstoll": true, "estliberitas": true, "grancalavera": true, "grantcarthew": true, "ivan.marquez": true, "ivangaravito": true, "martinhecher": true, "mateodurante": true, "mukundbhudia": true, "natterstefan": true, "nickeltobias": true, "orenschwartz": true, "prabhash1785": true, "processbrain": true, "shanemileham": true, "shaomingquan": true, "stevepsharpe": true, "sundaycrafts": true, "tobiasnickel": true, "wangyinchuan": true, "wfalkwallace": true, "windhamdavid": true, "anypossible.w": true, "diegorbaquero": true, "jasonwang1888": true, "jordan-carney": true, "kunalgaurav18": true, "liangtongzhuo": true, "markthethomas": true, "mdedirudianto": true, "michaelchance": true, "nonemoticoner": true, "parkerproject": true, "playthefallen": true, "bouncing-bison": true, "natarajanmca11": true, "sergeymakoveev": true, "suryasaripalli": true, "thebearingedge": true, "thecodeparadox": true, "troels.trvo.dk": true, "usingthesystem": true, "alexbaumgertner": true, "charlietango592": true, "hyokosdeveloper": true, "joaquin.briceno": true, "mihaislobozeanu": true, "pensierinmusica": true, "sanjaychaudhari": true, "satoshiyamamoto": true, "crashtheuniverse": true, "jfernandezgersol": true, "netoperatorwibby": true, "rahulraghavankklm": true, "scott.m.sarsfield": true, "obsessiveprogrammer": true}}