{"_id": "brace-expansion", "_rev": "36-7d4ba224c59232d5dcca7a8c7bc01ebd", "name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "dist-tags": {"latest": "4.0.0"}, "versions": {"0.0.0": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "0.0.0", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "main": "index.js", "scripts": {"test": "tape test/*.js"}, "dependencies": {"concat-map": "0.0.0", "balanced-match": "0.0.0"}, "devDependencies": {"tape": "~1.1.1"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_id": "brace-expansion@0.0.0", "dist": {"shasum": "b2142015e8ee12d4cdae2a23908d28d44c2baa9f", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-0.0.0.tgz", "integrity": "sha512-<PERSON><PERSON><PERSON>tiom0CcPQjWOvuqQsl/jP/GbJYO9oRJwJiZcB0f2e4PM3EAwoxAzTJBOcUJ0SSlKShb0wB5bkpzoH4YgbYg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDIz68OppfZj5bE9pvkPOiULQUHgRnY5X0txTOV7vNCFQIhAI4tRdbxB4npUxcuFaodGaFxxqwJMGQt0kUIqjs5WvNq"}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.0.0": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "1.0.0", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "main": "index.js", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh"}, "dependencies": {"balanced-match": "^0.2.0", "concat-map": "0.0.0"}, "devDependencies": {"tape": "~1.1.1"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "55329dcf69a61c2ea76320c5e87a56de48682c80", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_id": "brace-expansion@1.0.0", "_shasum": "a01656d12ebbbd067c8e935903f194ea5efee4ee", "_from": ".", "_npmVersion": "2.1.8", "_nodeVersion": "0.10.32", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "a01656d12ebbbd067c8e935903f194ea5efee4ee", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.0.0.tgz", "integrity": "sha512-lpqC6FxtM5XVWHdevRkMRPWSpsoLOWqurCALDPKm0VnLHf3DQ2rqFO8WBc6ierDnXeiMnCzwtDl6PgZrPY7xxA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHiiZuTN8rlOZuQfGyNVObHLXk06S8FCymzz59nrA6kIAiEAkQNqXm+yIrcqfrgeOfchnIebTgu7lM/7ohwc2jaPqnY="}]}, "directories": {}}, "1.0.1": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "1.0.1", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "main": "index.js", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh"}, "dependencies": {"balanced-match": "^0.2.0", "concat-map": "0.0.0"}, "devDependencies": {"tape": "~1.1.1"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "ceba9627f19c590feb7df404e1d6c41f8c01b93a", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_id": "brace-expansion@1.0.1", "_shasum": "817708d72ab27a8c312d25efababaea963439ed5", "_from": ".", "_npmVersion": "2.1.11", "_nodeVersion": "0.10.16", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "817708d72ab27a8c312d25efababaea963439ed5", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.0.1.tgz", "integrity": "sha512-agencL/m7vghsxEHLqdfg0cz3hHCEo46p+VCthmo2ldRTsmW7DANziRJnYCzGPT2Rc6OaYoNmiC9Fq/6laK8Lg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCGL4FbAvj1GETCGq8al+snilcC+LBgaWobxTbx8NWHZAIhANmwsSB+I/6UwWGG0pJTZ61b1BqcCFCycRpzUjMB5IUG"}]}, "directories": {}}, "1.1.0": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "1.1.0", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "main": "index.js", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh"}, "dependencies": {"balanced-match": "^0.2.0", "concat-map": "0.0.1"}, "devDependencies": {"tape": "^3.0.3"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "b5fa3b1c74e5e2dba2d0efa19b28335641bc1164", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_id": "brace-expansion@1.1.0", "_shasum": "c9b7d03c03f37bc704be100e522b40db8f6cfcd9", "_from": ".", "_npmVersion": "2.1.10", "_nodeVersion": "0.10.32", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "c9b7d03c03f37bc704be100e522b40db8f6cfcd9", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.0.tgz", "integrity": "sha512-jW1t9kL3kiXzovHnEgYNuYMnF+hHB1TlyK2wox32dPrWRvwNEJlXz3NdB5mdjFK1Pom22qVVvpGXN2hICWmvGw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDAPkyBXMMTJXxO2G60LgymQM/x1fVRSoTL+X3M2ijMQAIhAIy8QqTEZzxuJKSFpS2zCFxK5+XDyIaYWZeOlil5bFAa"}]}, "directories": {}}, "1.1.1": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "1.1.1", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "main": "index.js", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh"}, "dependencies": {"balanced-match": "^0.2.0", "concat-map": "0.0.1"}, "devDependencies": {"tape": "^3.0.3"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "f50da498166d76ea570cf3b30179f01f0f119612", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_id": "brace-expansion@1.1.1", "_shasum": "da5fb78aef4c44c9e4acf525064fb3208ebab045", "_from": ".", "_npmVersion": "2.6.1", "_nodeVersion": "0.10.36", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "da5fb78aef4c44c9e4acf525064fb3208ebab045", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.1.tgz", "integrity": "sha512-8sehXzl+5+hVq+azy8bdvi/vdY1DA0eKIM+k+wK4XqBAy3e0khAcxN+CMIf6QObpDLR4LXBBH8eRRR500WDidg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAMsztomnUx31iO0XaIv4hcdVeg9nUiL0BNflX2zT1mKAiEAnKJYmKMf1DIPlz3tsslDFKMUMZBV5K6i41Erb0rdfOE="}]}, "directories": {}}, "1.1.2": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "1.1.2", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "main": "index.js", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh"}, "dependencies": {"balanced-match": "^0.3.0", "concat-map": "0.0.1"}, "devDependencies": {"tape": "4.2.2"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "b03773a30fa516b1374945b68e9acb6253d595fa", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_id": "brace-expansion@1.1.2", "_shasum": "f21445d0488b658e2771efd870eff51df29f04ef", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.1", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "f21445d0488b658e2771efd870eff51df29f04ef", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.2.tgz", "integrity": "sha512-QY1LGlHZzEwE7NbolI6UYCtLE2zp0I49Cx7anmMGHjwPcb5E/fN/mk5i6oERkhhx78K/UPNEwLjLhHM3tZwjcw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGzXvMvui4rDxLgCCSSd5sHHHnPnkk+FFuMXmUmTPuasAiAI9w6HqWYhvFwjJ1Lt2kvx9juCc42nu86lNVjZVmiVBw=="}]}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "directories": {}}, "1.1.3": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "1.1.3", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "main": "index.js", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh"}, "dependencies": {"balanced-match": "^0.3.0", "concat-map": "0.0.1"}, "devDependencies": {"tape": "4.4.0"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "f0da1bb668e655f67b6b2d660c6e1c19e2a6f231", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_id": "brace-expansion@1.1.3", "_shasum": "46bff50115d47fc9ab89854abb87d98078a10991", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.5.0", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "46bff50115d47fc9ab89854abb87d98078a10991", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.3.tgz", "integrity": "sha512-JzSkuJYnfzmR0jZiCE/Nbw1I9/NL2Z2diIfhffu5Aq3nihHtfO8CNYcwxmAyTKYKWyte1b1vYBHMVhMbe+WZdw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDTtnEPVW6mkENQFogGyd+jwVVZk4fv5oQrv59tJZl+LQIhAIDBZRtfmnRz3bd3iBTboDlyyKBBzKcmMQRB4i13N8Aa"}]}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-6-west.internal.npmjs.com", "tmp": "tmp/brace-expansion-1.1.3.tgz_1455216688668_0.948847763473168"}, "directories": {}}, "1.1.4": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "1.1.4", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "main": "index.js", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh"}, "dependencies": {"balanced-match": "^0.4.1", "concat-map": "0.0.1"}, "devDependencies": {"tape": "4.5.1"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "1660b75d0bf03b022e7888b576cd5a4080692c1d", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_id": "brace-expansion@1.1.4", "_shasum": "464a204c77f482c085c2a36c456bbfbafb67a127", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "6.0.0", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "464a204c77f482c085c2a36c456bbfbafb67a127", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.4.tgz", "integrity": "sha512-wpJYpqGrDNnMWoi1GX8s8C4/SkHCuuLV0Sxlkvc4+rEBTNkUI2xLiUU3McR0b5dVw71Yw50l+sBGhusHNnjFnw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDv5gzTuC2pQtSUO2lUpdIu+EEkjv5yy57Xfhq9mKbJZAIgNucwl3w78pmRKhcaEEkqf8ALdEUhrSTClfu6fid9vy8="}]}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/brace-expansion-1.1.4.tgz_1462130058897_0.14984136167913675"}, "directories": {}}, "1.1.5": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "1.1.5", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "main": "index.js", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh"}, "dependencies": {"balanced-match": "^0.4.1", "concat-map": "0.0.1"}, "devDependencies": {"tape": "4.5.1"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "ff31acab078f1bb696ac4c55ca56ea24e6495fb6", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_id": "brace-expansion@1.1.5", "_shasum": "f5b4ad574e2cb7ccc1eb83e6fe79b8ecadf7a526", "_from": ".", "_npmVersion": "2.15.5", "_nodeVersion": "4.4.5", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "f5b4ad574e2cb7ccc1eb83e6fe79b8ecadf7a526", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.5.tgz", "integrity": "sha512-FtnR1B5L0wpwEeryoTeqAmxrybW2/7BI8lqG9WSk6FxHoPCg5O474xPgWWQkoS7wAilt97IWvz3hDOWtgqMNzg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDcdxKt/UDpxuuF6QUTSAj+Ndice1oRjJYdg0ZT4vFlxAiEA/qg6+kDz31bAvPhuTGit0IkXoFtpj5xgb9i8K7XPmOM="}]}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/brace-expansion-1.1.5.tgz_1465989660138_0.34528115345165133"}, "directories": {}}, "1.1.6": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "1.1.6", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "main": "index.js", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh"}, "dependencies": {"balanced-match": "^0.4.1", "concat-map": "0.0.1"}, "devDependencies": {"tape": "^4.6.0"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "791262fa06625e9c5594cde529a21d82086af5f2", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_id": "brace-expansion@1.1.6", "_shasum": "7197d7eaa9b87e648390ea61fc66c84427420df9", "_from": ".", "_npmVersion": "2.15.8", "_nodeVersion": "4.4.7", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "7197d7eaa9b87e648390ea61fc66c84427420df9", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.6.tgz", "integrity": "sha512-do+EUHPJZmz1wYWxOspwBMwgEqs0T5xSClPfYRwug3giEKZoiuMN9Ans1hjT8yZZ1Dkx1oaU4yRe540HKKHA0A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD32m58z3rzGaG1vElCS5FolKUXPn6odedg6Xfq9KZQOAIgWSBG2qBNxWBr+2EzNkySLFXLqC2Gj9ZQkHSOkPDUabM="}]}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/brace-expansion-1.1.6.tgz_1469047715600_0.9362958471756428"}, "directories": {}}, "1.1.7": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "1.1.7", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "main": "index.js", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh", "bench": "matcha test/perf/bench.js"}, "dependencies": {"balanced-match": "^0.4.1", "concat-map": "0.0.1"}, "devDependencies": {"matcha": "^0.7.0", "tape": "^4.6.0"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "892512024872ca7680554be90f6e8ce065053372", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_id": "brace-expansion@1.1.7", "_shasum": "3effc3c50e000531fb720eaff80f0ae8ef23cf59", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.8.0", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "3effc3c50e000531fb720eaff80f0ae8ef23cf59", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.7.tgz", "integrity": "sha512-ebXXDR1wKKxJNfTM872trAU5hpKduCkTN37ipoxsh5yibWq8FfxiobiHuVlPFkspSSNhrxbPHbM4kGyDGdJ5mg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDjfkyICBvvj8rQb/0E8LXObvB5Ip4Son+jWmF+agQUewIgYtJplpbk9QT8k8fK4+mvwW/2SG88zw7RyfanboCbDYs="}]}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/brace-expansion-1.1.7.tgz_1491552830231_0.7213963181711733"}, "directories": {}}, "1.1.8": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "1.1.8", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "main": "index.js", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh", "bench": "matcha test/perf/bench.js"}, "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}, "devDependencies": {"matcha": "^0.7.0", "tape": "^4.6.0"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "8f59e68bd5c915a0d624e8e39354e1ccf672edf6", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_id": "brace-expansion@1.1.8", "_shasum": "c07b211c7c952ec1f8efd51a77ef0d1d3990a292", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.8.0", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "c07b211c7c952ec1f8efd51a77ef0d1d3990a292", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.8.tgz", "integrity": "sha512-Dnfc9ROAPrkkeLIUweEbh7LFT9Mc53tO/bbM044rKjhgAEyIGKvKXg97PM/kRizZIfUHaROZIoeEaWao+Unzfw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCnJTT7JQLt62sEnsf0tHq2Bjs0s5hzFPLTKZ0ezxe48wIhAMjCqrWYo5zNLTOR2UuSzCxYcXppfgandM0w69ZeHWpY"}]}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/brace-expansion-1.1.8.tgz_1497251980593_0.6575565172825009"}, "directories": {}}, "1.1.9": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "1.1.9", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "main": "index.js", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh", "bench": "matcha test/perf/bench.js"}, "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}, "devDependencies": {"matcha": "^0.7.0", "tape": "^4.6.0"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "0f82dab6708f7c451e4a865b817057bc5a6b3c8e", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_id": "brace-expansion@1.1.9", "_npmVersion": "5.5.1", "_nodeVersion": "9.0.0", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-/+o3o6OV1cm3WKrO7U4wykU+ZICE6HiMEuravc2d03NIuM/VaRn5iMcoQ7NyxFXjvpmRICP2EER0YOnh4yIapA==", "shasum": "acdc7dde0e939fb3b32fe933336573e2a7dc2b7c", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.9.tgz", "fileCount": 3, "unpackedSize": 9867, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCAFhLoOH1TGldXfcUQuons91mJSbJrZN7qvWgErbY1lwIhALBOh4f4dcTZ4xMkCIZbI0YlooUneFTZMFuQiPvTFZVW"}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/brace-expansion_1.1.9_1518170016033_0.0827503901708313"}, "_hasShrinkwrap": false}, "1.1.10": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "1.1.10", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "main": "index.js", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh", "bench": "matcha test/perf/bench.js"}, "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}, "devDependencies": {"matcha": "^0.7.0", "tape": "^4.6.0"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "54a6176731eb223cd3dede1473190d885d6b3648", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_id": "brace-expansion@1.1.10", "_npmVersion": "5.5.1", "_nodeVersion": "9.0.0", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-u0KjSZq9NOEh36yRmKT/pIYOu0rpGAyUTeUmJgNd1K2tpAaUomh092TZ0fqbBGQc4hz85BVngAiB2mqekvQvIw==", "shasum": "5205cdf64c9798c180dc74b7bfc670c3974e6300", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.10.tgz", "fileCount": 4, "unpackedSize": 10964, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDli7mxJRbuSCfeiMcIL+s+gaQlXvfuResXwhPtt2QeKwIhAOXD8xbx/PBIDoeu5Oy4kLIhozwj20XbJgDGdsYvwrnj"}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/brace-expansion_1.1.10_1518210808996_0.14734749523785462"}, "_hasShrinkwrap": false}, "1.1.11": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "1.1.11", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "main": "index.js", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh", "bench": "matcha test/perf/bench.js"}, "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}, "devDependencies": {"matcha": "^0.7.0", "tape": "^4.6.0"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "01a21de7441549d26ac0c0a9ff91385d16e5c21c", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_id": "brace-expansion@1.1.11", "_npmVersion": "5.5.1", "_nodeVersion": "9.0.0", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==", "shasum": "3c7fcbf529d87226f3d2f52b966ff5271eb441dd", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz", "fileCount": 4, "unpackedSize": 11059, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC2I9J9tPlxp6j/HHQEZt6m3oGHr2r9mzmIpCuNqtxU8AIgEDoaUyizhrLzwPIwhskq7pIaySeBQHqkhwY/BQL5cCk="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/brace-expansion_1.1.11_1518248541320_0.33962849281003904"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "2.0.0", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "main": "index.js", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh", "bench": "matcha test/perf/bench.js"}, "dependencies": {"balanced-match": "^1.0.0"}, "devDependencies": {"matcha": "^0.7.0", "tape": "^4.6.0"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "0b6a022491103b806770bc037654744bef3e63be", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_id": "brace-expansion@2.0.0", "_nodeVersion": "10.19.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-A4GHY1GpcTnp+Elcwp1CbKHY6ZQwwVR7QdjZk4fPetEh7oNBfICu+eLvvVvTEMHgC+SGn+XiLAgGo0MnPPBGOg==", "shasum": "3b53b490c803c23a6a5d6c9c8b309879c37c7f98", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.0.tgz", "fileCount": 5, "unpackedSize": 11241, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfewZYCRA9TVsSAnZWagAAd70P/2QT8aQ9+pjZQwM2pk0Z\nB/jNeaDu5O0/Y06KZF3Pzcxl9SFVCWfEr+7WP5mqb+R+dbthggNppICoM2Tk\nmilkoIgrUecspuKsvnJ0qJRYDSktSwD1IgcY/V3Yr8jCW5J56tU5SiUozvuj\nl3od5svv9vsPilwIHnMoRS4p00La7dlKK6v6R9QgdIF300jd+F++5GmSZOmj\nRxQslhhmFcM0nxIrJ1Ku06Tino2o8E8R0XzBUZS42uexstrDk9DGTtQmjqUn\nvnR/KRlJVppSdOeQ5P0L1UjvDObub5XUdfRo4JnQDrPrDZMdItLZ8CeoEVPh\nIwBCNCBoeWxbbPgAr4QdYMTpyIidFpMDd2lhNB+UTibold67Of4tbOn5KcOG\nac1lCdmturxz0AkyxawmQDkelpLdnatWdBzwGmPDk/Nh6bCSR03iKEgT3oJI\nu+NtciBopPto2emV8eN6E9yvlpGz8b7qDxi7FgOSYvEZ4Vy7spRpj6mS8PYl\nXbkTFUaNDr1KIMHlvXjeYX1I0MfFeE5u1uWovNS+bRmPYqVo78kRxJmlEL/J\nsOGS+PEnPx2thCA9VU4IZ+uGn1dx5mR28xylmatytWU2o3kXF4clXVeosSOl\nZYBAoXlsicuQxhrLzwvkReVpfCYNYVLTRjjn1eLrrba+M+FxT8ULtj6Z5Zh+\nqlBk\r\n=GlK9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCr5ZL6FcHVP65o923WcJjCEbbjT/6loKJU+zYITXIhbQIgGEfe3/Y91JY20BO7ZulW1OEI8SlP/Xvlh6hngSAJcF4="}]}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/brace-expansion_2.0.0_1601898071832_0.5293279392460339"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "2.0.1", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "main": "index.js", "scripts": {"test": "tape test/*.js", "gentest": "bash test/generate.sh", "bench": "matcha test/perf/bench.js"}, "dependencies": {"balanced-match": "^1.0.0"}, "devDependencies": {"@c4312/matcha": "^1.3.1", "tape": "^4.6.0"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "b9c0e57027317a8d0a56a7ccee28fc478d847da2", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_id": "brace-expansion@2.0.1", "_nodeVersion": "14.15.5", "_npmVersion": "6.14.11", "dist": {"integrity": "sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==", "shasum": "1edc459e0f0c548486ecf9fc99f2221364b9a0ae", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.1.tgz", "fileCount": 5, "unpackedSize": 11486, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgM9lGCRA9TVsSAnZWagAAkYIQAIalRvvQqAOlBPALOfU+\nuIHTUBeNj/D6vRuqzPgWQVtUxRpdvXMI/aLxJx38aeZ6WgCvZWBQn3jItTEs\n3H2zWGue5+DAeWvBCqxSjdVV4ai+4EJuyS4+1D1qTm2syzT0aPdYRlhVMA/s\nOpiuPVHF1vqwSwPMCUXNW1sMi4N0qJzpAInYOCQ2NFUFZb5OssTqYQ1bzdl1\nRq/FtfkqOmz7OC/879lo3SCp+uvdXmkkQnSOGVU65HvzJp/NIvsFk5pHwo68\naRXefo/GRnqGFwFYOSqUUlBVjgEJYFdRVrYN+CNHK8iNJ6cphqz3EE1Edl1d\njT1SsFm9dJCqkfz5M/tW03vbMV88MYKhdDff5/Fugz4vcCAKfp+JcJolxUxz\nYXnB/xH/MsIEFIqwfDHYf+HFDZsZk7kJKm5JUciIV9CORiWtHz4d/y+4FYZM\n48okE1VAa5E7DVlGhTEUJUUt05JHztbm4EPklRd4/il61edoL516wp1XryxB\nSG3Jb+wLHH/ZHUQHpqrnBWvs68fxE8848EwiWIPKUk7pP/MtHdftjw2ouPSa\nD3EeHKJirZ3GAJqmwDy/vrOSB5/bQX82dGviV097AdPpnCAH6HJuaXBww+lN\nXgbxcuBiXlMuQAxpmQ578BOIGHnCo9EeauL2Ik9pHissAPUcpFCSUVZvXC3P\nJbt7\r\n=ayXm\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDgNFdV3ddgnkb39ucmGYRgdjxRfJEZcnAt+BXCAQaVPgIhAKW05a01tUGrzy0G/gZFOVqMptjiKbs9uy+dDpF0C75F"}]}, "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/brace-expansion_2.0.1_1614010693500_0.3082242768639887"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "3.0.0", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "exports": "./index.js", "type": "module", "scripts": {"test": "standard --fix && node --test", "gentest": "bash test/generate.sh", "bench": "matcha bench/bench.js"}, "dependencies": {"balanced-match": "^3.0.0"}, "devDependencies": {"@c4312/matcha": "^1.3.1", "standard": "^17.1.0"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "engines": {"node": ">= 18"}, "gitHead": "b01a637b0578a7c59acc7d8386f11f8d0710b512", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_id": "brace-expansion@3.0.0", "_nodeVersion": "20.3.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-P+6OwxY7i0tsp0Xdei2CjvVOQke51REB4c2d2wCckcMn6NBElNqLuzr6PsxFCdJ3i/cpGEkZ/Nng5I7ZkLo0CA==", "shasum": "2ba8d16a84bb3b440107587dae0fa59cf8672452", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-3.0.0.tgz", "fileCount": 7, "unpackedSize": 12214, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEC9uzfKooJ89Q8QLlD+tzLeFwFe/78sLtWLDO3ReypKAiBnVIsCDBixEed2GXe6+kCRV/O2pdWrYiYU+YT9S1FG4A=="}]}, "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/brace-expansion_3.0.0_1696685462916_0.3750340778742729"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "4.0.0", "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "exports": "./index.js", "type": "module", "scripts": {"test": "standard --fix && node --test", "gentest": "bash test/generate.sh", "bench": "matcha bench/bench.js"}, "dependencies": {"balanced-match": "^3.0.0"}, "devDependencies": {"@c4312/matcha": "^1.3.1", "standard": "^17.1.0"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "engines": {"node": ">= 18"}, "gitHead": "6a39bdddcf944374b475d99b0e8292d3727c7ebe", "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "_id": "brace-expansion@4.0.0", "_nodeVersion": "20.3.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-l/mOwLWs7BQIgOKrL46dIAbyCKvPV7YJPDspkuc88rHsZRlg3hptUGdU7Trv0VFP4d3xnSGBQrKu5ZvGB7UeIw==", "shasum": "bb24b89bf4d4b37d742acac89b65d1a32b379a81", "tarball": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-4.0.0.tgz", "fileCount": 8, "unpackedSize": 12770, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDrhNEy/hwZjHIlsHCYab0+IHgrz7kDfa1w6u/e+kx1EAIgOp8c3E2/Sn13tVi4fV1P31KM5fQX1SqJtVtpcVNa8gI="}]}, "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/brace-expansion_4.0.0_1709035002841_0.7308632197804894"}, "_hasShrinkwrap": false}}, "readme": "# brace-expansion\n\n[Brace expansion](https://www.gnu.org/software/bash/manual/html_node/Brace-Expansion.html),\nas known from sh/bash, in JavaScript.\n\n[![CI](https://github.com/juliangruber/brace-expansion/actions/workflows/ci.yml/badge.svg)](https://github.com/juliangruber/brace-expansion/actions/workflows/ci.yml)\n[![downloads](https://img.shields.io/npm/dm/brace-expansion.svg)](https://www.npmjs.org/package/brace-expansion)\n\n## Example\n\n```js\nimport expand from 'brace-expansion'\n\nexpand('file-{a,b,c}.jpg')\n// => ['file-a.jpg', 'file-b.jpg', 'file-c.jpg']\n\nexpand('-v{,,}')\n// => ['-v', '-v', '-v']\n\nexpand('file{0..2}.jpg')\n// => ['file0.jpg', 'file1.jpg', 'file2.jpg']\n\nexpand('file-{a..c}.jpg')\n// => ['file-a.jpg', 'file-b.jpg', 'file-c.jpg']\n\nexpand('file{2..0}.jpg')\n// => ['file2.jpg', 'file1.jpg', 'file0.jpg']\n\nexpand('file{0..4..2}.jpg')\n// => ['file0.jpg', 'file2.jpg', 'file4.jpg']\n\nexpand('file-{a..e..2}.jpg')\n// => ['file-a.jpg', 'file-c.jpg', 'file-e.jpg']\n\nexpand('file{00..10..5}.jpg')\n// => ['file00.jpg', 'file05.jpg', 'file10.jpg']\n\nexpand('{{A..C},{a..c}}')\n// => ['A', 'B', 'C', 'a', 'b', 'c']\n\nexpand('ppp{,config,oe{,conf}}')\n// => ['ppp', 'pppconfig', 'pppoe', 'pppoeconf']\n```\n\n## API\n\n```js\nimport expand from 'brace-expansion'\n```\n\n### const expanded = expand(str)\n\nReturn an array of all possible and valid expansions of `str`. If none are\nfound, `[str]` is returned.\n\nValid expansions are:\n\n```js\n/^(.*,)+(.+)?$/\n// {a,b,...}\n```\n\nA comma separated list of options, like `{a,b}` or `{a,{b,c}}` or `{,a,}`.\n\n```js\n/^-?\\d+\\.\\.-?\\d+(\\.\\.-?\\d+)?$/\n// {x..y[..incr]}\n```\n\nA numeric sequence from `x` to `y` inclusive, with optional increment.\nIf `x` or `y` start with a leading `0`, all the numbers will be padded\nto have equal length. Negative numbers and backwards iteration work too.\n\n```js\n/^-?\\d+\\.\\.-?\\d+(\\.\\.-?\\d+)?$/\n// {x..y[..incr]}\n```\n\nAn alphabetic sequence from `x` to `y` inclusive, with optional increment.\n`x` and `y` must be exactly one character, and if given, `incr` must be a\nnumber.\n\nFor compatibility reasons, the string `${` is not eligible for brace expansion.\n\n## Installation\n\nWith [npm](https://npmjs.org) do:\n\n```bash\nnpm install brace-expansion\n```\n\n## Contributors\n\n- [Julian Gruber](https://github.com/juliangruber)\n- [Isaac Z. Schlueter](https://github.com/isaacs)\n- [Haelwenn Monnier](https://github.com/lanodan)\n\n## Sponsors\n\nThis module is proudly supported by my [Sponsors](https://github.com/juliangruber/sponsors)!\n\nDo you want to support modules like this to improve their quality, stability and weigh in on new features? Then please consider donating to my [Patreon](https://www.patreon.com/juliangruber). Not sure how much of my modules you're using? Try [feross/thanks](https://github.com/feross/thanks)!\n\n## Security contact information\n\nTo report a security vulnerability, please use the\n[Tidelift security contact](https://tidelift.com/security).\nTidelift will coordinate the fix and disclosure.\n\n## License\n\n(MIT)\n\nCopyright (c) 2013 Julian Gruber &lt;<EMAIL>&gt;\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of\nthis software and associated documentation files (the \"Software\"), to deal in\nthe Software without restriction, including without limitation the rights to\nuse, copy, modify, merge, publish, distribute, sublicense, and/or sell copies\nof the Software, and to permit persons to whom the Software is furnished to do\nso, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "time": {"modified": "2024-02-27T11:56:43.413Z", "created": "2013-10-13T12:58:47.118Z", "0.0.0": "2013-10-13T12:58:50.153Z", "1.0.0": "2014-11-30T09:58:55.317Z", "1.0.1": "2014-12-03T07:58:39.708Z", "1.1.0": "2014-12-16T18:58:15.116Z", "1.1.1": "2015-09-27T21:58:47.098Z", "1.1.2": "2015-11-28T12:58:57.647Z", "1.1.3": "2016-02-11T18:51:31.874Z", "1.1.4": "2016-05-01T19:14:21.252Z", "1.1.5": "2016-06-15T11:21:03.644Z", "1.1.6": "2016-07-20T20:48:37.117Z", "1.1.7": "2017-04-07T08:13:51.907Z", "1.1.8": "2017-06-12T07:19:41.589Z", "1.1.9": "2018-02-09T09:53:36.709Z", "1.1.10": "2018-02-09T21:13:29.675Z", "1.1.11": "2018-02-10T07:42:22.313Z", "2.0.0": "2020-10-05T11:41:11.973Z", "2.0.1": "2021-02-22T16:18:13.617Z", "3.0.0": "2023-10-07T13:31:03.177Z", "4.0.0": "2024-02-27T11:56:43.001Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "repository": {"type": "git", "url": "git://github.com/juliangruber/brace-expansion.git"}, "homepage": "https://github.com/juliangruber/brace-expansion", "keywords": [], "bugs": {"url": "https://github.com/juliangruber/brace-expansion/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"fotooo": true, "i-erokhin": true, "scottfreecode": true, "shaomingquan": true, "sbruchmann": true, "flumpus-dev": true}}