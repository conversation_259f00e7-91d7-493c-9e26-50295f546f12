{"_id": "is-number-object", "_rev": "20-4055599da791182208236193b629ef7a", "name": "is-number-object", "dist-tags": {"latest": "1.1.1"}, "versions": {"1.0.0": {"name": "is-number-object", "version": "1.0.0", "keywords": ["Number", "ES6", "toStringTag", "@@toStringTag", "Number object"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "is-number-object@1.0.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/is-number-object", "bugs": {"url": "https://github.com/ljharb/is-number-object/issues"}, "dist": {"shasum": "dd62d5bb8d100df8e8d06ebf6700a13a7b18834a", "tarball": "https://registry.npmjs.org/is-number-object/-/is-number-object-1.0.0.tgz", "integrity": "sha512-L2maiMLwrybTseiFQkbt3RM7Koil5Pc6nmBVdbtgatRTIFerKqbHBsDmR84FvuuzL4ZY+zIyWBJrfoL6mZOfCw==", "signatures": [{"sig": "MEUCICPpv7PQi+M93GQB73CUR9OeL8GbT5m5AksseUn3zVCcAiEArClRYAuXrO7G9z0b8TcZ4XvEfsaxATKY2/MMoKgwql0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "dd62d5bb8d100df8e8d06ebf6700a13a7b18834a", "engines": {"node": ">= 0.4"}, "gitHead": "a1bb415f8e37b561092080a11446e5cf39b027d9", "scripts": {"jscs": "jscs test.js *.js", "lint": "npm run jscs && npm run eslint", "test": "npm run lint && node --harmony --es-staging test.js && npm run security", "eslint": "eslint test.js *.js", "eccheck": "editorconfig-tools check *.js **/*.js > /dev/null", "coverage": "covert test.js", "security": "nsp package", "coverage-quiet": "covert test.js --quiet"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/is-number-object.git", "type": "git"}, "_npmVersion": "2.3.0", "description": "Is this value a JS Number object? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "directories": {}, "_nodeVersion": "1.0.4", "dependencies": {}, "devDependencies": {"is": "~2.2.0", "nsp": "~1.0.0", "jscs": "~1.10.0", "tape": "~3.4.0", "covert": "1.0.0", "eslint": "~0.12.0", "semver": "~4.2.0", "foreach": "~2.0.5", "indexof": "~0.0.1", "editorconfig-tools": "~0.0.1"}}, "1.0.2": {"name": "is-number-object", "version": "1.0.2", "keywords": ["Number", "ES6", "toStringTag", "@@toStringTag", "Number object"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "is-number-object@1.0.2", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/is-number-object", "bugs": {"url": "https://github.com/ljharb/is-number-object/issues"}, "dist": {"shasum": "0041c5bcb812287f18cb224baf9819db54887168", "tarball": "https://registry.npmjs.org/is-number-object/-/is-number-object-1.0.2.tgz", "integrity": "sha512-4cFq0ufaJqWPtUhFXkIMU1jWp8ypmKq3043YOzNb5ZhrgzHZV0xz0epzIJtvSKpxaXuH8U7H3JhnGE5IsxsceA==", "signatures": [{"sig": "MEQCIEjbPChzzv1Aobjy/ktzwAFAk3AHgntbhR35zSgdNaleAiAd1NWBqmvGLB6v1/3vHcWvGTquxi4kNAqn4vPkiMMxvA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "0041c5bcb812287f18cb224baf9819db54887168", "engines": {"node": ">= 0.4"}, "gitHead": "43afda8a160cb45b3500810f8dc0705f4950f7ab", "scripts": {"jscs": "jscs test.js *.js", "lint": "npm run jscs && npm run eslint", "test": "npm run lint && node --harmony --es-staging test.js && npm run security", "eslint": "eslint test.js *.js", "eccheck": "editorconfig-tools check *.js **/*.js > /dev/null", "coverage": "covert test.js", "security": "nsp package", "coverage-quiet": "covert test.js --quiet"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/is-number-object.git", "type": "git"}, "_npmVersion": "2.3.0", "description": "Is this value a JS Number object? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "directories": {}, "_nodeVersion": "1.0.4", "dependencies": {}, "devDependencies": {"is": "~2.2.0", "nsp": "~1.0.0", "jscs": "~1.10.0", "tape": "~3.4.0", "covert": "1.0.0", "eslint": "~0.12.0", "semver": "~4.2.0", "foreach": "~2.0.5", "indexof": "~0.0.1", "editorconfig-tools": "~0.0.1"}}, "1.0.3": {"name": "is-number-object", "version": "1.0.3", "keywords": ["Number", "ES6", "toStringTag", "@@toStringTag", "Number object"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "is-number-object@1.0.3", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/is-number-object", "bugs": {"url": "https://github.com/ljharb/is-number-object/issues"}, "dist": {"shasum": "f265ab89a9f445034ef6aff15a8f00b00f551799", "tarball": "https://registry.npmjs.org/is-number-object/-/is-number-object-1.0.3.tgz", "integrity": "sha512-Iq4qVsj5dYzW3Er5QJFP3ARs+IU/kXPNUoceumYbZmUtk3gTxiDYI8mVs41bFmPeETWNOL4S7QIvi1ONtCRhaA==", "signatures": [{"sig": "MEUCIQCHYiBnWcui9Ks87+jL/HHHY7JaO2GwNdTNaiFD2WqX2AIgQhnwrDe7o7A/e6XC/2L3901YU864qtV4V/Db3uQmJao=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "f265ab89a9f445034ef6aff15a8f00b00f551799", "engines": {"node": ">= 0.4"}, "gitHead": "a9ea86bfff60e026294abfe72f5349fbab1cceef", "scripts": {"jscs": "jscs test.js *.js", "lint": "npm run jscs && npm run eslint", "test": "npm run lint && node --harmony --es-staging test.js && npm run security", "eslint": "eslint test.js *.js", "eccheck": "editorconfig-tools check *.js **/*.js > /dev/null", "coverage": "covert test.js", "security": "nsp package", "coverage-quiet": "covert test.js --quiet"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/ljharb/is-number-object.git", "type": "git"}, "_npmVersion": "2.3.0", "description": "Is this value a JS Number object? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "directories": {}, "_nodeVersion": "1.0.4", "dependencies": {}, "devDependencies": {"is": "~2.2.0", "nsp": "~1.0.0", "jscs": "~1.10.0", "tape": "~3.4.0", "covert": "1.0.0", "eslint": "~0.12.0", "semver": "~4.2.0", "foreach": "~2.0.5", "indexof": "~0.0.1", "editorconfig-tools": "~0.0.1"}}, "1.0.4": {"name": "is-number-object", "version": "1.0.4", "keywords": ["Number", "ES6", "toStringTag", "@@toStringTag", "Number object"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-number-object@1.0.4", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-number-object#readme", "bugs": {"url": "https://github.com/inspect-js/is-number-object/issues"}, "dist": {"shasum": "36ac95e741cf18b283fc1ddf5e83da798e3ec197", "tarball": "https://registry.npmjs.org/is-number-object/-/is-number-object-1.0.4.tgz", "fileCount": 10, "integrity": "sha512-zohwelOAur+5uXtk8O3GPQ1eAcu4ZX3UwxQhUlfFFMNpUd83gXgjbhJh6HmB6LUNV/ieOLQuDwJO3dWJosUeMw==", "signatures": [{"sig": "MEUCIAYWSOHD6wabEuNLXVcYUq3sus3e8pvj683IxS8/gI+kAiEAz/CJd7zf/7QDxdtGWdp2dNcXypT3MC7q1YFqBsxXtRY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16520, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd+v9dCRA9TVsSAnZWagAAsLwP/1v1N6Z+7veHrUGS5+0V\n7o+QbICAjqb7tJCmjR4bHwaFswm3AUMbcKgQNjbz5AHyeixjTTzMNtlavmGR\nn+sWpuU0XvrL0VlQrSwsmEcC703tzgmSD6Kgia/q/56IoZoIAs+kc08gAs9i\nz1ytifX5C5JDsaxOjXlxUkmbgL9xnviFEXy6IdAUy7wXki+/BN0VRj6l331B\n/G1bCt/fWH4klPkdTjD3LfHM86N8tOn/gxXjL7hzSlQpjdk2yfOdaSpjLRhK\nxmJB7P645RlSVvF9/b8FwW/uFPDbxdvU5am2s1orb/4GP7NPS46WK7r5n42W\nahhZ7MRhgu44nBfsEHSonR7tp9pAS7Z536SN7ulxpumHTK6qvMOUMHY0Icz3\n6+0GfuHIhRWpF3i2JffgjBQmVX1cCRRjGeC1NRNH4jFnMJ+bq/EnWoFkzKWN\nK5P/N7HgG3seHDxUc11j4/2rMFZHXgTM3G81ghSkT+p7A+7hGdNQqDSYm/be\npTfwSdY+2QkIc32okIgX0t/y6S69aqIqrS4SCAG0/L77YmsthnTAZFqg6Sqi\nCeVwlwqGsgtdA6a//fWcoPV5zKKjTWbIf1FTzb5jaJNpkyHLxomgsHLfrxof\nQMPoiAeAXmHdvMk31e+sogkPDWD+pe1Txii9sUEpay6rHaIOiu0AdjQuVhVG\nQbN1\r\n=65eB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "3662815bffdb718fc63495aee5e343161b80ac71", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "eccheck": "eclint check *.js **/*.js > /dev/null", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "coverage": "covert test/index.js", "posttest": "npx aud", "prepublish": "safe-publish-latest", "tests-only": "node --harmony --es-staging test", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/inspect-js/is-number-object.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Is this value a JS Number object? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "directories": {}, "_nodeVersion": "13.4.0", "dependencies": {}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"is": "^3.3.0", "tape": "^4.12.0", "covert": "^1.1.1", "eclint": "^2.8.1", "eslint": "^6.7.2", "foreach": "^2.0.5", "indexof": "^0.0.1", "has-symbols": "^1.0.1", "auto-changelog": "^1.16.2", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^15.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/is-number-object_1.0.4_1576730460902_0.23529564340630826", "host": "s3://npm-registry-packages"}}, "1.0.5": {"name": "is-number-object", "version": "1.0.5", "keywords": ["Number", "ES6", "toStringTag", "@@toStringTag", "Number object"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-number-object@1.0.5", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-number-object#readme", "bugs": {"url": "https://github.com/inspect-js/is-number-object/issues"}, "dist": {"shasum": "6edfaeed7950cff19afedce9fbfca9ee6dd289eb", "tarball": "https://registry.npmjs.org/is-number-object/-/is-number-object-1.0.5.tgz", "fileCount": 12, "integrity": "sha512-RU0lI/n95pMoUKu9v1BZP5MBcZuNSVJkMkAG2dJqC4z2GlkGUNeH68SuHuBKBD/XFe+LHZ+f9BKkLET60Niedw==", "signatures": [{"sig": "MEQCIGILXaLbCb3ScXNChLnXY6Tt6K5F4JziyfrLS7NdHNL+AiBDbxq1Lus6MQdjyhN0H+OLe80F/dDQTeGfdoBr/WkpWQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19922, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJglahMCRA9TVsSAnZWagAAZAsP/3hpIjr4U3TBQvbRGyHG\ncS8FOTqS+baPNrus/W4WiJLranLRp+uaESTvcEqyeqoM8we8EyMkink7wO3q\n7g6+GiC8I4pWMT3n6C82Ev0D3ZiZ++evuqCGWAKAdxMg2Pv/DCQiRdzBrB/g\n0iMpqxunPjsE80jhnJP3QQRtYY5eLwY0RInnVoip4mJVqbkL0hkYCkA243F5\nLh90k3FaZbKpUjKCrfe1gKxusHPhlWmHNx/TVaCbq0d2nLNGmolyn9cb3eTx\nB4Ygk+em2VvyGQnDCh8sAfG9ZbjQ701HDJIzaFIa2qN6WCtc7/jeq8O++ern\ncvi75E+lmc/NCzHnXl+QHuWzE8dmOecF+3t3qKhcZIxr3FB2CjdC2T0X4vRd\nKaXjkCkCsN5tJWPVhmNprLXTOxF0OdQo651EwIMtCjpIoaLFTR+pEBRiN3Fj\nZwFW/OF0HGB38B1s4Kk7ba32RpFWTIOjri5QRkqy/ero2VL44Lkjca6vk524\nE8vqU9tQamX19bS1Ps6pGS8/0dYQsqc4uvlt2aIfuB3z7sjaR1ET4uIur4w3\nWR4o8/b2iCNgnI1PzfFBM3DTln73b6huYMYOIexwlMjHRgs+mep79jB+nkCz\nA21CoWDYcwf2W9KxmYZRONJb+JjfQ0UlclhYs9VpNBO3Le3bIfx7+Cpvf4PX\nXHiy\r\n=5AeG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "1b8afebe79f7057f5596687fe46f502b27f5c6a4", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "eccheck": "eclint check *.js **/*.js > /dev/null", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "npx aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/inspect-js/is-number-object.git", "type": "git"}, "_npmVersion": "7.10.0", "description": "Is this value a JS Number object? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "directories": {}, "_nodeVersion": "16.0.0", "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"is": "^3.3.0", "aud": "^1.1.5", "nyc": "^10.3.2", "tape": "^5.2.2", "eclint": "^2.8.1", "eslint": "^7.25.0", "core-js": "^3.12.0", "foreach": "^2.0.5", "indexof": "^0.0.1", "has-symbols": "^1.0.2", "auto-changelog": "^2.2.1", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-number-object_1.0.5_1620420684242_0.3188214449355995", "host": "s3://npm-registry-packages"}}, "1.0.6": {"name": "is-number-object", "version": "1.0.6", "keywords": ["Number", "ES6", "toStringTag", "@@toStringTag", "Number object"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-number-object@1.0.6", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-number-object#readme", "bugs": {"url": "https://github.com/inspect-js/is-number-object/issues"}, "dist": {"shasum": "6a7aaf838c7f0686a50b4553f7e54a96494e89f0", "tarball": "https://registry.npmjs.org/is-number-object/-/is-number-object-1.0.6.tgz", "fileCount": 11, "integrity": "sha512-bEVOqiRcvo3zO1+G2lVMy+gkkEm9Yh7cDMRusKKu5ZJKPUYSJwICTKZrNKHA2EbSP0Tu0+6B/emsYNHZyn6K8g==", "signatures": [{"sig": "MEUCIQDg89lMwLQrahfNwPCTEOytpfDwulVaVoN6gfalwMhXXQIgJU1Jht4qbHCXKt9Toq7cNbxyXuXVYkatONgFef4sf4M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20691, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhDLDlCRA9TVsSAnZWagAAAi0P/3nV1X/HT4dr8Ht8B6/C\nL18hlF39glNubA432j4T2r8bZMl3jTW5qL2Q1oLaMkoxOPX801tDOHNvAz/W\nKEf7DFtwcICApwjyeWO2meGIm774mfz8GPjPzGcKs2NGg5ulznCFizH+vYKe\ni1M+pi9Q0L8uAvTu5NtLRT0zZq9fRUz6dujpRULWc3v2sYoRngc5cWjp0PFh\nfsMSEDX+cjlZpmjlGwfRFJpFDebs6dMf5CZWJ9uVo+1bP9k6kn2mZpy2xRUF\nfdJSxGHlGJbpiZIkLbd+mqBWGNzzsI+m9bhgauSk0c3eJ5zxYZ5RsuEP6OAk\nbXXebKNBX75Tyha5p+sfilRseM5JX9HcpE0ZTxgQJOi06FQEKgAAjeu/RAGI\nna9wtA+i2LjoOcIhYo1uNGunjjLMcsq39eDGBm+WQjFLd26W+KF+9FQk8DH6\n3IieOFjhze+ZcRHlkKE9EFor6Tut5fhuw8eVbuQRsGt+GvlOjD9x8Sh61s7X\n1JKkaPj1qdNEa65LG93F9PeVGoXAu0ZDWfNSTaST0FdTDbhqAiiT5pNeySo6\ncERhTCzpnyOkaficBPNpJuaZr057ulWTNkW5JRcMkqcm/95WwVYlnLXloIWZ\nksph8coFXLUY1t1eLFvNZqrxtocPzrmXKsmrLoPPpyigo8e8tDeVcfJvZFeo\n4bSa\r\n=B2tr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "1ef9fb21fae4dfc570167998bdc54cd49f758da1", "scripts": {"lint": "eslint .", "test": "npm run tests-only && npm run test:corejs", "eccheck": "eclint check *.js **/*.js > /dev/null", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "npx aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "test:corejs": "nyc tape test-corejs.js", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/inspect-js/is-number-object.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Is this value a JS Number object? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "directories": {}, "_nodeVersion": "16.6.1", "dependencies": {"has-tostringtag": "^1.0.0"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"is": "^3.3.0", "aud": "^1.1.5", "nyc": "^10.3.2", "tape": "^5.3.0", "eclint": "^2.8.1", "eslint": "^7.32.0", "core-js": "^3.16.0", "foreach": "^2.0.5", "indexof": "^0.0.1", "auto-changelog": "^2.3.0", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-number-object_1.0.6_1628221669119_0.9035766745974172", "host": "s3://npm-registry-packages"}}, "1.0.7": {"name": "is-number-object", "version": "1.0.7", "keywords": ["Number", "ES6", "toStringTag", "@@toStringTag", "Number object"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-number-object@1.0.7", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-number-object#readme", "bugs": {"url": "https://github.com/inspect-js/is-number-object/issues"}, "dist": {"shasum": "59d50ada4c45251784e9904f5246c742f07a42fc", "tarball": "https://registry.npmjs.org/is-number-object/-/is-number-object-1.0.7.tgz", "fileCount": 10, "integrity": "sha512-k1U0IRzLMo7ZlYIfzRu23Oh6MiIFasgpb9X76eqfFZAqwH44UI4KTBvBYIZ1dSL9ZzChTB9ShHfLkR4pdW5krQ==", "signatures": [{"sig": "MEQCIBEreGI/qTzrwxGPCUsjZTuimXtLzRSWO90JhbIJiC0aAiBFnlrBsbBnQJ1Cwp4rkc6bygzhJhFhD5TCllV4KUSb9A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22218, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiR9VZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmonZBAAhf1TanQphCCZloETV1lGrYgCdHareidYfpp4HWz24Ac2cRTB\r\nptx82JWfrA1GomRZAmNPd2B1P4ckg8IvIEZFxP1JW1oqfAkbqfuF0knU3m+w\r\nyYW7UiC3B7Qlz5OF2GUeNfxJqHTGomfiTAD+y0swlkaU6gn1e+XFX6o9mbZa\r\nkTJYFZuywKEIkaPStJX6bpJ7drgDs+A4PFxxPSqpAWoKG9aANHZJ441+pHqL\r\nSykO6pf6xDymvxgIRY1pdAtcEYjYm10lHqfKVF2cxiWKSHteKKjTXzONFQQW\r\nXhGgCf8bkiH4G5X9qraGQNow4XiHzy2/h1FM50Qn1LkaOpXRS/HvsF3rEXPH\r\nkGZuuF3dto78DU2qE6FvjZ1wOM2ZxZFTrFUPQup7uNIxpP6qoWUkCQ+AeDpu\r\ndJxqvhLumrLDGSAcC+eN7G5Wamc0QTQ5CUr2fxi8el7xp4bUniI0J+RXxPmY\r\nv1FGhbE8mQB3myvYnuc7KmTF6ZUn7l0eANVeRhcVkjwEuQ9lkDq1xRIURKUA\r\n4QK7QGLdCp2X+EPpwpwd8LZe4gk/hgQQ0Zg1tNENdoNNbYeLhrSONT3Nx4g9\r\nQ0M2VhvUVRjPMYzt9aQtK/ORhLBg2ZO9YMabc8sNu2f0G7HrWSOhvn5m95wn\r\nFZvY2KrOWe3Z17ArmkBm5U/JfG+J0XBudXo=\r\n=sZT4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "50c53ae827537cd9d951c87e9e6286339575d402", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only && npm run test:corejs", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "test:corejs": "nyc tape test-corejs.js", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/inspect-js/is-number-object.git", "type": "git"}, "_npmVersion": "8.6.0", "description": "Is this value a JS Number object? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "directories": {}, "_nodeVersion": "17.8.0", "dependencies": {"has-tostringtag": "^1.0.0"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"is": "^3.3.0", "aud": "^2.0.0", "nyc": "^10.3.2", "tape": "^5.5.2", "eclint": "^2.8.1", "eslint": "=8.8.0", "core-js": "^3.21.1", "foreach": "^2.0.5", "indexof": "^0.0.1", "auto-changelog": "^2.4.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^20.2.3"}, "_npmOperationalInternal": {"tmp": "tmp/is-number-object_1.0.7_1648874840951_0.8972943667560316", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "is-number-object", "version": "1.1.0", "keywords": ["Number", "ES6", "toStringTag", "@@toStringTag", "Number object"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-number-object@1.1.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-number-object#readme", "bugs": {"url": "https://github.com/inspect-js/is-number-object/issues"}, "dist": {"shasum": "5a867e9ecc3d294dda740d9f127835857af7eb05", "tarball": "https://registry.npmjs.org/is-number-object/-/is-number-object-1.1.0.tgz", "fileCount": 12, "integrity": "sha512-KVSZV0Dunv9DTPkhXwcZ3Q+tUc9TsaE1ZwX5J2WMvsSGS6Md8TFPun5uwh0yRdrNerI6vf/tbJxqSx4c1ZI1Lw==", "signatures": [{"sig": "MEYCIQDgox3Xrn0sBDyvraBu7U7YFzyZB2c6TQGXnAhiBIe9kAIhAL/lOTC7U6rv34Q0UNKSJi7DplOUVn9RXWTDhEjqQ0zx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24642}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "d90d4c5ccafe5695c3eb099a952d3d183d36faec", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only && npm run test:corejs", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "tsc -p . && attw -P", "posttest": "npx npm@'>=10.2' audit --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "test:corejs": "nyc tape test-corejs.js", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js"}, "repository": {"url": "git://github.com/inspect-js/is-number-object.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Is this value a JS Number object? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "directories": {}, "_nodeVersion": "23.3.0", "dependencies": {"call-bind": "^1.0.7", "has-tostringtag": "^1.0.2"}, "publishConfig": {"ignore": [".github/workflows", "test-corejs.js"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"is": "^3.3.0", "nyc": "^10.3.2", "tape": "^5.9.0", "eclint": "^2.8.1", "eslint": "=8.8.0", "core-js": "^3.39.0", "indexof": "^0.0.1", "encoding": "^0.1.13", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "next", "@types/tape": "^5.6.5", "@types/core-js": "^2.5.8", "auto-changelog": "^2.5.0", "@ljharb/tsconfig": "^0.2.0", "@types/call-bind": "^1.0.5", "safe-publish-latest": "^2.0.0", "@arethetypeswrong/cli": "^0.17.0", "@ljharb/eslint-config": "^21.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/is-number-object_1.1.0_1733105859766_0.7965188913225627", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "is-number-object", "version": "1.1.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "description": "Is this value a JS Number object? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "main": "index.js", "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test:corejs": "nyc tape test-corejs.js", "test": "npm run tests-only && npm run test:corejs", "posttest": "npx npm@'>=10.2' audit --production", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && attw -P", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/inspect-js/is-number-object.git"}, "keywords": ["Number", "ES6", "toStringTag", "@@toStringTag", "Number object"], "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/is-number-object/issues"}, "homepage": "https://github.com/inspect-js/is-number-object#readme", "dependencies": {"call-bound": "^1.0.3", "has-tostringtag": "^1.0.2"}, "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/core-js": "^2.5.8", "@types/tape": "^5.7.0", "auto-changelog": "^2.5.0", "core-js": "^3.39.0", "eclint": "^2.8.1", "encoding": "^0.1.13", "eslint": "=8.8.0", "in-publish": "^2.0.1", "indexof": "^0.0.1", "is": "^3.3.0", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "testling": {"files": "test/index.js"}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows", "test-corejs.js"]}, "_id": "is-number-object@1.1.1", "gitHead": "faf886f4703b8d38c1aef8a6117b9c6977eed1e5", "types": "./index.d.ts", "_nodeVersion": "23.4.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-lZhclumE1G6VYD8VHe35wFaIif+CTy5SJIi5+3y4psDgWu4wPDoBhF8NxUOinEc7pHgiTsT6MaBb92rKhhD+Xw==", "shasum": "144b21e95a1bc148205dcc2814a9134ec41b2541", "tarball": "https://registry.npmjs.org/is-number-object/-/is-number-object-1.1.1.tgz", "fileCount": 12, "unpackedSize": 25184, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIECenSrr2tj+wfQjdlWOsRz1cZfKvGojvcQ9HRHy7/7OAiANzVAIy7R7vA/uR8orVXjhrjACqBHy03Q/sggmA7mjGA=="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/is-number-object_1.1.1_1734317955924_0.996093085213557"}, "_hasShrinkwrap": false}}, "time": {"created": "2015-01-28T23:04:25.649Z", "modified": "2024-12-16T02:59:16.271Z", "1.0.0": "2015-01-28T23:04:25.649Z", "1.0.1": "2015-01-29T17:38:26.603Z", "1.0.2": "2015-01-29T18:32:59.449Z", "1.0.3": "2015-01-30T07:51:57.396Z", "1.0.4": "2019-12-19T04:41:01.068Z", "1.0.5": "2021-05-07T20:51:24.349Z", "1.0.6": "2021-08-06T03:47:49.248Z", "1.0.7": "2022-04-02T04:47:21.117Z", "1.1.0": "2024-12-02T02:17:40.055Z", "1.1.1": "2024-12-16T02:59:16.111Z"}, "bugs": {"url": "https://github.com/inspect-js/is-number-object/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/inspect-js/is-number-object#readme", "keywords": ["Number", "ES6", "toStringTag", "@@toStringTag", "Number object"], "repository": {"type": "git", "url": "git://github.com/inspect-js/is-number-object.git"}, "description": "Is this value a JS Number object? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "readme": "# is-number-object <sup>[![Version Badge][2]][1]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![dependency status][5]][6]\n[![dev dependency status][7]][8]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][11]][1]\n\nIs this value a JS Number object? This module works cross-realm/iframe, and despite ES6 @@toStringTag.\n\n## Example\n\n```js\nvar isNumber = require('is-number-object');\nvar assert = require('assert');\n\nassert.notOk(isNumber(undefined));\nassert.notOk(isNumber(null));\nassert.notOk(isNumber(false));\nassert.notOk(isNumber(true));\nassert.notOk(isNumber('foo'));\nassert.notOk(isNumber(function () {}));\nassert.notOk(isNumber([]));\nassert.notOk(isNumber({}));\nassert.notOk(isNumber(/a/g));\nassert.notOk(isNumber(new RegExp('a', 'g')));\nassert.notOk(isNumber(new Date()));\n\nassert.ok(isNumber(42));\nassert.ok(isNumber(NaN));\nassert.ok(isNumber(Infinity));\nassert.ok(isNumber(new Number(42)));\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n[1]: https://npmjs.org/package/is-number-object\n[2]: https://versionbadg.es/inspect-js/is-number-object.svg\n[5]: https://david-dm.org/inspect-js/is-number-object.svg\n[6]: https://david-dm.org/inspect-js/is-number-object\n[7]: https://david-dm.org/inspect-js/is-number-object/dev-status.svg\n[8]: https://david-dm.org/inspect-js/is-number-object#info=devDependencies\n[11]: https://nodei.co/npm/is-number-object.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/is-number-object.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/is-number-object.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=is-number-object\n[codecov-image]: https://codecov.io/gh/inspect-js/is-number-object/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/inspect-js/is-number-object/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/inspect-js/is-number-object\n[actions-url]: https://github.com/inspect-js/is-number-object/actions\n", "readmeFilename": "README.md"}