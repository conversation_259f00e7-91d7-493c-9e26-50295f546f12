{"_id": "fs-minipass", "_rev": "46-9aa84d23f20e92b7535dc512ae6fb000", "name": "fs-minipass", "description": "fs read and write streams based on minipass", "dist-tags": {"latest": "3.0.3"}, "versions": {"1.0.0": {"name": "fs-minipass", "version": "1.0.0", "main": "index.js", "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/fs-minipass.git"}, "bugs": {"url": "https://github.com/npm/fs-minipass/issues"}, "homepage": "https://github.com/npm/fs-minipass#readme", "description": "fs read and write streams based on minipass", "dependencies": {"minipass": "^2.2.1"}, "devDependencies": {"mutate-fs": "^2.0.1", "tap": "^10.7.2"}, "gitHead": "459e240fcd814de3967e7f2b9fdaf6b97acd5d46", "_id": "fs-minipass@1.0.0", "_npmVersion": "5.4.1", "_nodeVersion": "8.4.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Y37MeVmGgxMPPgdvSDcK/jI0Mi4RH+9JwVhvLYci/p8GmaE6xxEF0cWWmcmMLeT96W7Z3tILYghWF+/5gUBZIg==", "shasum": "87f65ec75db23fcc6e8e907b6b68622d1b8b4f0e", "tarball": "https://registry.npmjs.org/fs-minipass/-/fs-minipass-1.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEY7EJae2rrZN4l1A4sq6N55lfFKS7Tt2Lu9p9sifC/jAiBzoodkcDMVilkJT28KEiYSczqahQQGfwuNU6iBmxeiow=="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fs-minipass-1.0.0.tgz_1505105417453_0.1749916800763458"}, "directories": {}}, "1.1.0": {"name": "fs-minipass", "version": "1.1.0", "main": "index.js", "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/fs-minipass.git"}, "bugs": {"url": "https://github.com/npm/fs-minipass/issues"}, "homepage": "https://github.com/npm/fs-minipass#readme", "description": "fs read and write streams based on minipass", "dependencies": {"minipass": "^2.2.1"}, "devDependencies": {"mutate-fs": "^2.0.1", "tap": "^10.7.2"}, "gitHead": "978cabc23940cf721b3b6e8dbe2a9528235f9808", "_id": "fs-minipass@1.1.0", "_npmVersion": "5.4.1", "_nodeVersion": "8.4.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-YBq+xJBRSOmy8KH3daKAbXTCsEEcIgscFgxTXvkqP5qPCNV8erAvC3ww/CBbZPFTsHuMuFuYPYaxOmXBtqEyKQ==", "shasum": "c060489adaf55ad9af690df378fd2a1d22c754dc", "tarball": "https://registry.npmjs.org/fs-minipass/-/fs-minipass-1.1.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAYGI5CTWD/tATONC16v2S3sT/GaRCxM3MYr7xqouw9NAiBlzufg09uhN4EZvio1S6Spv44BYssxTmRmgQXMArsyng=="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fs-minipass-1.1.0.tgz_1505185388272_0.3607080793008208"}, "directories": {}}, "1.1.1": {"name": "fs-minipass", "version": "1.1.1", "main": "index.js", "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/fs-minipass.git"}, "bugs": {"url": "https://github.com/npm/fs-minipass/issues"}, "homepage": "https://github.com/npm/fs-minipass#readme", "description": "fs read and write streams based on minipass", "dependencies": {"minipass": "^2.2.1"}, "devDependencies": {"mutate-fs": "^2.0.1", "tap": "^10.7.2"}, "gitHead": "9d579c4c4e56668ccf4603d7901e8a5f79af678b", "_id": "fs-minipass@1.1.1", "_npmVersion": "5.4.1", "_nodeVersion": "8.4.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-sGXvMqPJpNQZnTMTQrqiOPD9pLj1hXwRBOtJv2Yl4Tsj7N8VdRhJU4PhAXF+PAQ0uvQQo8/0nZxjOc5oGewBrw==", "shasum": "950f318eb270bae23f9934fed8b2267780155a56", "tarball": "https://registry.npmjs.org/fs-minipass/-/fs-minipass-1.1.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC0+Ny6Q7NVm2L2zQDxjDrIXrQHtI4DrrlzTuZA+4kFkQIhAOU9F2SIEtmXxo8ZYTPyIuRFu5hGYgDxLUC73mEwcT0m"}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fs-minipass-1.1.1.tgz_1505193966534_0.24331881944090128"}, "directories": {}}, "1.1.2": {"name": "fs-minipass", "version": "1.1.2", "main": "index.js", "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/fs-minipass.git"}, "bugs": {"url": "https://github.com/npm/fs-minipass/issues"}, "homepage": "https://github.com/npm/fs-minipass#readme", "description": "fs read and write streams based on minipass", "dependencies": {"minipass": "^2.2.1"}, "devDependencies": {"mutate-fs": "^2.0.1", "tap": "^10.7.2"}, "gitHead": "679dfe3e95c40972d8bb50f82da24a284ac77925", "_id": "fs-minipass@1.1.2", "_npmVersion": "5.4.1", "_nodeVersion": "8.4.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-ZWBtCrDBbATuFYlDyHskpNVyQm1hJyj0kd4pJKHgyWHtjYSdqIekjq2w/S091Lm6+WArtK6xQrQYUedsGIhTnA==", "shasum": "66882e22e81a1a63e668a309fe9515306c75574f", "tarball": "https://registry.npmjs.org/fs-minipass/-/fs-minipass-1.1.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICTf0yqOcpARTbzBPsma0+9vYIthMgHQMHmTjiT29keEAiEAgvVDIczyhcrHM10h2N38IeLebTCRyiox/sAMjO8iCV0="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fs-minipass-1.1.2.tgz_1505194529825_0.3317506497260183"}, "directories": {}}, "1.2.0": {"name": "fs-minipass", "version": "1.2.0", "main": "index.js", "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/fs-minipass.git"}, "bugs": {"url": "https://github.com/npm/fs-minipass/issues"}, "homepage": "https://github.com/npm/fs-minipass#readme", "description": "fs read and write streams based on minipass", "dependencies": {"minipass": "^2.2.1"}, "devDependencies": {"mutate-fs": "^2.0.1", "tap": "^10.7.2"}, "gitHead": "5e70789fcec9ed007a2a8f1d54232eab72136d6e", "_id": "fs-minipass@1.2.0", "_npmVersion": "5.4.2", "_nodeVersion": "8.4.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-6XR5zJClATEymITUjjvnuQ7ISLDPX6b5Z7PldD7EjjzM8xMyJTAC4/a7AL/SAn2ysGSrsBWi6CeJfm9lJ+BbiA==", "shasum": "b5cfa375adb1bdb81672e3f2da7baeeca4edada1", "tarball": "https://registry.npmjs.org/fs-minipass/-/fs-minipass-1.2.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFy10x3kXO+QjnQDL2+QEqHB3DFr4vTwcjXdqeytX86wAiBtBnPsJs4QmjNiFIf+jm8F7XC7ke5r8UXmaymkB4thWg=="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fs-minipass-1.2.0.tgz_1505677629351_0.8495220621116459"}, "directories": {}}, "1.2.1": {"name": "fs-minipass", "version": "1.2.1", "main": "index.js", "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/fs-minipass.git"}, "bugs": {"url": "https://github.com/npm/fs-minipass/issues"}, "homepage": "https://github.com/npm/fs-minipass#readme", "description": "fs read and write streams based on minipass", "dependencies": {"minipass": "^2.2.1"}, "devDependencies": {"mutate-fs": "^2.0.1", "tap": "^10.7.2"}, "files": ["index.js"], "gitHead": "aeacd89da6297f887df31ec1b8f512943fd14df3", "_id": "fs-minipass@1.2.1", "_npmVersion": "5.4.2", "_nodeVersion": "8.5.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-rV9Yh1ndz2KH1poZUSgm2gF1WZd310dEFLPasmy/iQvjJf7vU4nBNk6arNTV1T44gxcpXV4h9678hz5zlvBaTg==", "shasum": "fc9eb3cbbb55f89734d6c4ac0471cc79a5f36085", "tarball": "https://registry.npmjs.org/fs-minipass/-/fs-minipass-1.2.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDMlsYs0MgBQbQZda3TO0U94d5tvyf3pY+HM7xllghh3AiEA2nozoWDjG73BySdu4X1dcMrly5q3UOAc+RvRc8zimcs="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fs-minipass-1.2.1.tgz_1506027393008_0.3910524039529264"}, "directories": {}}, "1.2.2": {"name": "fs-minipass", "version": "1.2.2", "main": "index.js", "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/fs-minipass.git"}, "bugs": {"url": "https://github.com/npm/fs-minipass/issues"}, "homepage": "https://github.com/npm/fs-minipass#readme", "description": "fs read and write streams based on minipass", "dependencies": {"minipass": "^2.2.1"}, "devDependencies": {"mutate-fs": "^2.0.1", "tap": "^10.7.2"}, "files": ["index.js"], "gitHead": "d92c102fc08604e7d6a98c5352e41690994c79ca", "_id": "fs-minipass@1.2.2", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-W8OwE0zljH+UPPhOHPbArJ3WX5sLUoXXahhrA/fz1S3q/qIoF9m0R6YoEa2g7Ye/R9jcuenJQF7FkkzI84tQLw==", "shasum": "67827a40c3333c4c390baa11366cfe0fcb0130c4", "tarball": "https://registry.npmjs.org/fs-minipass/-/fs-minipass-1.2.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCyvsfxmuyV+EWkyKnd4yj/NE5aeMetdAPuol0wrupFWQIhANA+H1owCRCxcZJbOO9wQ0Z8u/rvlg+rJCZaVVS8HxmG"}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fs-minipass-1.2.2.tgz_1510650727795_0.11070904578082263"}, "directories": {}}, "1.2.3": {"name": "fs-minipass", "version": "1.2.3", "main": "index.js", "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/fs-minipass.git"}, "bugs": {"url": "https://github.com/npm/fs-minipass/issues"}, "homepage": "https://github.com/npm/fs-minipass#readme", "description": "fs read and write streams based on minipass", "dependencies": {"minipass": "^2.2.1"}, "devDependencies": {"mutate-fs": "^2.0.1", "tap": "^10.7.2"}, "files": ["index.js"], "gitHead": "e91a395e3afcd0bf2cd541f0bce44ed7c3453c2e", "_id": "fs-minipass@1.2.3", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-u1pHCXDx+CElfM6CuIeHDTKvb1Ya9ZhsMk7xTHTh6zHSRLK6O0DTVBN+E3wg8fruxAFp4oE07owrrzQfDA0b5Q==", "shasum": "633ee214389dede91c4ec446a34891f964805973", "tarball": "https://registry.npmjs.org/fs-minipass/-/fs-minipass-1.2.3.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCWhTpzVRK3B/0//oUDpEfHKs5bappjG6gEkyMmq9RcEwIgGseFwQZ7RCGCsAUAySciYqUXPQbWe4PSxZJK4zsYrlQ="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fs-minipass-1.2.3.tgz_1510653626637_0.4699037205427885"}, "directories": {}}, "1.2.4": {"name": "fs-minipass", "version": "1.2.4", "main": "index.js", "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/fs-minipass.git"}, "bugs": {"url": "https://github.com/npm/fs-minipass/issues"}, "homepage": "https://github.com/npm/fs-minipass#readme", "description": "fs read and write streams based on minipass", "dependencies": {"minipass": "^2.2.1"}, "devDependencies": {"mutate-fs": "^2.0.1", "tap": "^10.7.2"}, "files": ["index.js"], "gitHead": "7b362fb8b0f9a2b5a2102a3829413afb91612ea3", "_id": "fs-minipass@1.2.4", "_npmVersion": "5.5.1", "_nodeVersion": "9.3.0", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Idk73Z37sEVewVcrv72MBhcAPIskPqw0wKQ6ljlHVxg5dMG8ZdaLDaElJpKjbS9RP32MIWljJcDj7V/JhMRlyg==", "shasum": "14d44873225456e9d0ea40c0139795b677b7114f", "tarball": "https://registry.npmjs.org/fs-minipass/-/fs-minipass-1.2.4.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD3y+DX4nrZRbsHu+8RebktzG0TfteJ7xGFOVPz1bkn7QIhAM0MWFaT9l/7HsGg1FKB2bx3/VkEEcvpwkNEmmLQwsCT"}]}, "maintainers": [{"email": "<EMAIL>", "name": "iarna"}, {"email": "<EMAIL>", "name": "isaacs"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fs-minipass-1.2.4.tgz_1515106362992_0.8212358288001269"}, "directories": {}}, "1.2.5": {"name": "fs-minipass", "version": "1.2.5", "main": "index.js", "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/fs-minipass.git"}, "bugs": {"url": "https://github.com/npm/fs-minipass/issues"}, "homepage": "https://github.com/npm/fs-minipass#readme", "description": "fs read and write streams based on minipass", "dependencies": {"minipass": "^2.2.1"}, "devDependencies": {"mutate-fs": "^2.0.1", "tap": "^10.7.2"}, "files": ["index.js"], "gitHead": "fceb43535c348c9cd1ad08edb6c188e04850c245", "_id": "fs-minipass@1.2.5", "_npmVersion": "5.6.0", "_nodeVersion": "4.8.4", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-JhBl0skXjUPCFH7x6x61gQxrKyXsxB5gcgePLZCwfyCGGsTISMoIeObbrvVeP6Xmyaudw4TT43qV2Gz+iyd2oQ==", "shasum": "06c277218454ec288df77ada54a03b8702aacb9d", "tarball": "https://registry.npmjs.org/fs-minipass/-/fs-minipass-1.2.5.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA55aqJPvganTX0KVlBikAAPtCgD14SfrWhyrGMxJRR0AiEA81XO77NXYtErXQRrWFw9Kq3RoQVbmlWDkKfGV5k4u2E="}]}, "maintainers": [{"email": "<EMAIL>", "name": "iarna"}, {"email": "<EMAIL>", "name": "isaacs"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fs-minipass-1.2.5.tgz_1515107144774_0.8260935051366687"}, "directories": {}}, "1.2.6": {"name": "fs-minipass", "version": "1.2.6", "main": "index.js", "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/fs-minipass.git"}, "bugs": {"url": "https://github.com/npm/fs-minipass/issues"}, "homepage": "https://github.com/npm/fs-minipass#readme", "description": "fs read and write streams based on minipass", "dependencies": {"minipass": "^2.2.1"}, "devDependencies": {"mutate-fs": "^2.0.1", "tap": "^13.1.9"}, "tap": {"check-coverage": true}, "gitHead": "f9a1d65d03d41e17b162e77eabd3c67e26ea070d", "_id": "fs-minipass@1.2.6", "_nodeVersion": "12.0.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-crhvyXcMejjv3Z5d2Fa9sf5xLYVCF5O1c71QxbVnbLsmYMBEvDAftewesN/HhY03YRoA7zOMxjNGrF5svGaaeQ==", "shasum": "2c5cc30ded81282bfe8a0d7c7c1853ddeb102c07", "tarball": "https://registry.npmjs.org/fs-minipass/-/fs-minipass-1.2.6.tgz", "fileCount": 4, "unpackedSize": 13052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc3E/JCRA9TVsSAnZWagAApvEP/00oEt3mUuE7hMjdVT19\nhqMoaRjRUnGad8/eEpNVhXjqZ5/bnLDlfNCIaBYlepp7Dv4AD2AWJc6O64Wi\ngCJ2rZQSXsXnlXv1yfNcdCpaaiwMzgciTDtbntsq672ane0JjYy4HJ9gqN8g\nrNgRWZ6/eeVhigB2tcKeZOcmpB10V28C33t0eUvQO3o0yoHW3iAeL+GOLaVH\n4D1oAOSLcWBPtk4MYQlvCJS45x+RZmvH8xqKwvSOvt5z61PBox89WfTfalYp\nFjS8LBu5OgxZUgfneRAxxQM82YkxbZTmhGTR0siam8XAWYzY/AD4h/X+6k87\n5fDeset5BwD2RJXOI9bKdUOyZ2cR02GGVAo0bWWDlE9LowihhO7hRm8AUf/Y\nIlgQpqgeWJ+AfOq6FRUzKdG0c+3vzQeLZLHBkCoHZB25Yzjg8rvdcd/ztka3\nSe70BiDB+n+GZWwTJXbbyzEXWiUfSagXXHyOr1zhoZ1CoOepsJfry1o6nQhq\nAWO5okI36odYUZRflrj8WTqXGQuzA619Mre9DPfY5jj5N9QB9kodBaXai3wz\nWmCSj0JF+XqEutUQenT6hrAG0KWGLlhKfFFCSdQDUePVw093GaU7MxfJFCo5\n/bM0xy8EGUKqGZVlBTuDtuwQ6wejcA7ZtuEDBOqxFSyi4/eTwaDmoWb77K/8\nwh8T\r\n=Zf69\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG2/fwbhlBX2ff7xiCRkN+o7kOyQkGSYA8qAatq2lQIlAiEA9ctFwsMKt9rkHdqFemRWEEnQYre4zdx+VxywzM27bHk="}]}, "maintainers": [{"email": "<EMAIL>", "name": "iarna"}, {"email": "<EMAIL>", "name": "isaacs"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fs-minipass_1.2.6_1557942217307_0.8853606533561724"}, "_hasShrinkwrap": false}, "1.2.7": {"name": "fs-minipass", "version": "1.2.7", "main": "index.js", "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/fs-minipass.git"}, "bugs": {"url": "https://github.com/npm/fs-minipass/issues"}, "homepage": "https://github.com/npm/fs-minipass#readme", "description": "fs read and write streams based on minipass", "dependencies": {"minipass": "^2.6.0"}, "devDependencies": {"mutate-fs": "^2.0.1", "tap": "^14.6.4"}, "tap": {"check-coverage": true}, "gitHead": "1e9c73ee0ea663d3b233591fbe37c47f969e1e5a", "_id": "fs-minipass@1.2.7", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"integrity": "sha512-GWSSJGFy4e9GUeCcbIkED+bgAoFyj7XF1mV8rma3QW4NIqX9Kyx79N/PF61H5udOV3aY1IaMLs6pGbH71nlCTA==", "shasum": "ccff8570841e7fe4265693da88936c55aed7f7c7", "tarball": "https://registry.npmjs.org/fs-minipass/-/fs-minipass-1.2.7.tgz", "fileCount": 4, "unpackedSize": 13079, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdf9vqCRA9TVsSAnZWagAAKqIP/ivLN8vOWhXzU55uIk89\nbqx+2VJ2fY+8rgjhtKvhWeQIznGDxTcYrShC4DZxxsyIK6vvP+YFkg9sWXLL\nZh3P8iS/BN/4UImhomoAB/0f/XKELdBrjCkvN0uy9/8CUIXl3WrrQzmwTrRX\n60XnKoDf1Jz92lsfC11hRli/itWz3wipeMZgNpzrt0SbqC6m3s2+CYF0iHFs\nirBeiE3/Ta0Mq1lR/nct22dTxUmzpMNrnkQ+BEl56taxjJSaKKzSxvjZPp5A\nOofGIJALA2nzWO09EpV3LsQYm79UQxN1ZXFugIo7AOohsTNL/I4j3LTPDCCh\n5gT9wTsmR45i+n6/bwrUZSer8hSylVM/gSsazZ4ZPjt6K65xYFHyAOJYzE6+\nsqLCS3v/CH9uD5eayCjcgIZ+cmg0iXoFQcOjYmVmEh8EmjF0As+4NM6S3kAv\n8ePNw2xshvH9gwYatT2qzIGHgg2EQxF1XmdT7KcW4XdcwnYrDyw+3QJU0EJf\n/BmZu7TKJPBVU2wT2ruHlCfV4KBYTsdpDoiaA5attiXyYLcGaeclzbf8HkHB\nL68EKvtmBge+oFJipwRWdrroTtbKsFgQjjkl02OLOHY4jLZ9xFrlhjgXdbRz\ncBy+azSqQanvdPfH7IOnJjdOm3xZj0G8seQQPE/VwTU/npv4PJcz4BiBhmh/\n9hXu\r\n=Mzq+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGzem4b3hzsJEtYhEe2IHbNyoi8/uQUle/5CmqUA2ZSJAiEA3d+umF3f9wGaNeXU2iKjAjGpdhANLgg2i9G3nAYNjjs="}]}, "maintainers": [{"email": "<EMAIL>", "name": "adam_baldwin"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "billatnpm"}, {"email": "<EMAIL>", "name": "clau<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "isaacs"}, {"email": "<EMAIL>", "name": "mike<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "ruyadorno"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fs-minipass_1.2.7_1568660457503_0.14890636250320233"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "fs-minipass", "version": "2.0.0", "main": "index.js", "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/fs-minipass.git"}, "bugs": {"url": "https://github.com/npm/fs-minipass/issues"}, "homepage": "https://github.com/npm/fs-minipass#readme", "description": "fs read and write streams based on minipass", "dependencies": {"minipass": "^3.0.0"}, "devDependencies": {"mutate-fs": "^2.0.1", "tap": "^14.6.4"}, "tap": {"check-coverage": true}, "engines": {"node": ">= 8"}, "gitHead": "0fe1b248aafde18b53a6905e72f491b94467cf2a", "_id": "fs-minipass@2.0.0", "_nodeVersion": "12.8.1", "_npmVersion": "6.12.0-next.0", "dist": {"integrity": "sha512-40Qz+LFXmd9tzYVnnBmZvFfvAADfUA14TXPK1s7IfElJTIZ97rA8w4Kin7Wt5JBrC3ShnnFJO/5vPjPEeJIq9A==", "shasum": "a6415edab02fae4b9e9230bc87ee2e4472003cd1", "tarball": "https://registry.npmjs.org/fs-minipass/-/fs-minipass-2.0.0.tgz", "fileCount": 4, "unpackedSize": 13118, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdkmO0CRA9TVsSAnZWagAAv0YP/3rAE4bCL/BTbmeg7dFc\n8jEbPGK4yRBBuPdpv8y9UFqTso61Jnu0AOADpegq78pPoh6s2Ndt83fCV01K\nTeWArCVjZEc7Ins196aWX8cIh3upZAxR/ThmIfLkHFA0MC0smm0vl7E5Jd//\n9xG6nTBGyMgfp7C8xmnPg8Pr78HARMznbpKUZZJEaP2YrhqiAydQJn2Yc+Wi\nqv/f00jZpQ0BoR0ibSalKO4DYwySwRI7fRSQRxqJo9ep1PLoHTIvmCiLpacW\nafBsz50ymPsI4ew8Z9HGCgIRx8/2FN9+8D21jHdP9/OJDhHipC8gd4CnZF3J\n8fG9MTjP1wfk8o5lFV0LUckNx4FLULHtMg2Wskm8dMLKk2jBllYYfTxYc/bB\nn/XnVdoPRX1k68MSAZgCKE9zHyc/mbJSXsW9TXU0FLKsZkFBYZ0OoRy87HWa\nYO+wg8gZgeRMZLT/7G3qYRLfKumEpiHQAMfWeOGpWuinf6q9jYl7VnVRu8rb\n5cd55vXMV9uMb4HVQmERHNg0tEc8mQPkyL+hh1UGacvSylNZRw8MMkSS3tLp\nsJeTb0J/bSMaml8Xr9fNV/PMnyqDOsmVPEvc5cXNoAPBNNFkOQJxbiCqyoTc\na6SjmHkr6jUtXwoFszs6cIN7m/NBBGtUZkxpgyKCVrC054HT8PgLaiwLmkFt\n6Iwe\r\n=V2fP\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDTyUm0ImRzT9jMp4pxZI7rRTA9/Jc4V/4KtPWPeas6gwIgHqodMd9KiJttjrS60J0fni+Ij4n+7UKwORLauSdm/ms="}]}, "maintainers": [{"email": "<EMAIL>", "name": "adam_baldwin"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "billatnpm"}, {"email": "<EMAIL>", "name": "clau<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "isaacs"}, {"email": "<EMAIL>", "name": "mike<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "ruyadorno"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fs-minipass_2.0.0_1569874867965_0.3936571403613629"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "fs-minipass", "version": "2.0.1", "main": "index.js", "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/fs-minipass.git"}, "bugs": {"url": "https://github.com/npm/fs-minipass/issues"}, "homepage": "https://github.com/npm/fs-minipass#readme", "description": "fs read and write streams based on minipass", "dependencies": {"minipass": "^3.0.0"}, "devDependencies": {"mutate-fs": "^2.0.1", "tap": "^14.6.4"}, "tap": {"check-coverage": true}, "engines": {"node": ">= 8"}, "gitHead": "4ffbd6de1e1f077d66fcf1e4606f8f46f4984933", "_id": "fs-minipass@2.0.1", "_nodeVersion": "13.6.0", "_npmVersion": "6.13.4", "_npmUser": {"name": "ruyadorno", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-22x6v7ceMyTfG8e4Y0YeudTuDNZEIR9pQoEVu1r5e6nLuMAbmrkTXlHADEHsdk2SHYM6opcRnKUbqB9bZ94D3g==", "shasum": "3e5bcc0f7b94507b0390e2c7d4a2516ca16a38d2", "tarball": "https://registry.npmjs.org/fs-minipass/-/fs-minipass-2.0.1.tgz", "fileCount": 4, "unpackedSize": 13214, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJfCGCRA9TVsSAnZWagAAu88P/R7pRu8nxK+eeFDvLvpg\nFhytSLYOyp314O8WTnBN+0LjDxkkeFpMySJNVIctNCPi4XZkwuiANoq+7jCr\nYmuTrQEYfKYJAOyFCXRYCYMByPkkd0ygjkMmvslfqmSf6ptoNyziDWm7whDR\nrOaV2ZGMT3MTOu7JldQQjvUCpkwaiSIV1jYtx+6i2yom4OIRDp9YGO3kwoRv\nTe2sJO4wpJnEwc3dboOPNLwMh/qurORkTEp1qiTWBra/rS14rb6Cphfsj+H9\nEbGL7pV4gLVQIuuqZ03urxeFZ5BDMDWGeNtX+RdQ3hUCFlnFdqxZfuZ+MIbw\nUcokI2mnCj4Z3+ElRz0qzTCMwuaZVVPCbsusU7WrxLxVRxwsozFGKLr7FVPH\nxEHjDdeGOnizgWSyp1CWLDnlZUoCdB67wRGIqWB6anvE1+Lik2PRd/52UHPP\nVJ/dx2dI91z4nQYM6gfIh1tRQfzFtb0Lfux8yCZmsvoIQsGCAQiqdZiQVme4\nPiIyJTgrD4o4tKDB5BXyMeyyxF1glUl7OVQluewa831ls8n2nSUwbZHcU92G\n2OpVtr+iyYDQk5Wkep91vyimlcwviOYkIKcNex5DADWs9+SIn4F9TTXrCN3T\noMLjIFhhyhSlMaWvdVlr7s4mMtVge8wYBiQoZTyaOl4LtuuKUlRuAcMcBki+\nozph\r\n=cQqe\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHva13SqHARMbZQclcEvPvgAzf0S7y4vAc/TT5ivh+GyAiEA6Jnl3tzjAGhglT6HEmP01PUdfAaPaf1IQaR6tYkThwk="}]}, "maintainers": [{"email": "<EMAIL>", "name": "adam_baldwin"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "clau<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "isaacs"}, {"email": "<EMAIL>", "name": "mike<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "ruyadorno"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fs-minipass_2.0.1_1579544709839_0.8668552111711036"}, "_hasShrinkwrap": false}, "2.1.0": {"name": "fs-minipass", "version": "2.1.0", "main": "index.js", "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "keywords": [], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/fs-minipass.git"}, "bugs": {"url": "https://github.com/npm/fs-minipass/issues"}, "homepage": "https://github.com/npm/fs-minipass#readme", "description": "fs read and write streams based on minipass", "dependencies": {"minipass": "^3.0.0"}, "devDependencies": {"mutate-fs": "^2.0.1", "tap": "^14.6.4"}, "tap": {"check-coverage": true}, "engines": {"node": ">= 8"}, "gitHead": "4995b5fd182fb95959ad5572dee5ccc2f31b5b21", "_id": "fs-minipass@2.1.0", "_nodeVersion": "13.4.0", "_npmVersion": "6.13.6", "dist": {"integrity": "sha512-V/JgOLFCS+R6Vcq0slCuaeWEdNC3ouDlJMNIsacH2VtALiu9mV4LPrHc5cDl8k5aw6J8jwgWWpiTo5RYhmIzvg==", "shasum": "7f5036fdbf12c63c169190cbe4199c852271f9fb", "tarball": "https://registry.npmjs.org/fs-minipass/-/fs-minipass-2.1.0.tgz", "fileCount": 4, "unpackedSize": 14089, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJzc8CRA9TVsSAnZWagAA394P/1iQTxpDUgo9PcXXEOf8\nzkw0nrrZ4dvg2FcBUePgJ6iDjY/e/p8wXBXBeCvaDVkULwjlMDMChv5aweS+\nS+hxZHpCe8f3QbTBlUGMQWgTrLrb3qXdHkBpv/uXuZD3eVzqJ8un/85aSc17\nnBJ6cTxbSft00ncApFRAh7fwZERBB8TWip+YSqjkv/5cSiiUwlRG/cCX3ogD\nMuqO083eOwdxonH/UgnGsI3Ijsdo0AqeEEjCYOCfFAnLtCdaDNC/7DqjnyrO\nwVAn0wuu0iAUXx3+3O/HgaAI6KR1qu16Zk9cB0vknd2SpxieqpV8vUNxfg8Y\n9fkdj5BkYenKsc5iqsdClYjnL533BEtd5+xu5/W8yXucQiiKedl06TGa39kj\nbXbXOY7C5LJiNe4BRQjVInBPhZytHQFl0StT9+A8SbCdFYjnZ6jFl4uKYVIe\nOHcjxfEx2gG40J5tRdL0YUM5L55ahgxS23l7hWL5sBOati7wWSP0IbnOa54i\n2qFnkek1tORJKneJ7g6p8XaZCNLY/5tV16Me+M0xJOxwhsNfJ+BvYode/B+/\nBm5uRXu/oW0k7WJ6s8n3GnzVCL8FiQdI7r7s74h745ftL5NXsVJo8d27kZEo\nIvumC4gilQ930J18VYtKlVEagVUOdXrU1zpAZ6tjCHmnMUB/VPE7FY4rFRXT\nHiAi\r\n=X3w7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIETjZE1EEDJqg6zfu44nfx6QmFlVznDQ3wnCJpEhn7eTAiAnIeoJR4uTnkUYIY8KLUi9riu9b41CCevmzEBz/hw71w=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "adam_baldwin"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "clau<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "isaacs"}, {"email": "<EMAIL>", "name": "mike<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "ruyadorno"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fs-minipass_2.1.0_1579628347683_0.459474590668985"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "fs-minipass", "version": "3.0.0", "main": "lib/index.js", "scripts": {"test": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "snap": "tap", "posttest": "npm run lint"}, "keywords": [], "author": {"name": "GitHub Inc."}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/fs-minipass.git"}, "bugs": {"url": "https://github.com/npm/fs-minipass/issues"}, "homepage": "https://github.com/npm/fs-minipass#readme", "description": "fs read and write streams based on minipass", "dependencies": {"minipass": "^4.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.1", "@npmcli/template-oss": "4.11.0", "mutate-fs": "^2.1.1", "tap": "^16.3.2"}, "tap": {"check-coverage": true, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.11.0"}, "gitHead": "dfdb274393f83d3f68b2171285d5c267b4a8602d", "_id": "fs-minipass@3.0.0", "_nodeVersion": "18.12.1", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-EUojgQaSPy6sxcqcZgQv6TVF6jiKvurji3AxhAivs/Ep4O1UpS8TusaxpybfFHZ2skRhLqzk6WR8nqNYIMMDeA==", "shasum": "8e6ed2b4e1ba44077cae69971393068a1bbeeed6", "tarball": "https://registry.npmjs.org/fs-minipass/-/fs-minipass-3.0.0.tgz", "fileCount": 4, "unpackedSize": 14347, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBG4b4ZDyIOf47I59NDzZ8gzr/V2uxSgA+c7ykOv7h+eAiAFKLgef3XYicFi+eA5aIiUX4jAGhGIn/ijEdVPfaBvGg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmQfrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo76w//UkfTeS3pQnn5oo0ZIciacGDCAyOG0yIpQ+Uj3+73MNnFEuRj\r\n83ii68SRAfOS9lSZeKF5G4n9E1j25oOfk0GmdFaI/uzqzN6hfllUvqM7LlLu\r\n1veXduYiFvmgsAtp2bECxgdmDILcz4freWlFJk4VjGj5me2NwHkhl7aybjLk\r\ndpWWniEX06ylBYyLoi6JV69ZquHh1av0RzGaUxlO4Es3x8jZRAgIg+Vjsgpr\r\nBpz3RxAxxopTdeQfpArK0zcsB6egni+qTvTtCopUgI2H5K3wqnFYSSWQMXjE\r\neAvsr+1GkBYhUrpb2C/QM92QCKicbhoJFGyWHCx6JMYZlzj5Vgl8miB/CF/c\r\n8aF12KxKCAi0JhmOETuOqDhd41KK7Ngq7snbiMr/xLeWza6JZVJhG2a1gRGj\r\ncif2H9Kr4mYFBTXSYa0q93QmoJoibfud/lyeRucs8CNCO/Fk8B2iA8Gs1fUD\r\nC/+CBdI1VWg+WDo2As26vJdlSnNh/8RpV/U4aB5Fn2uzA+njbQ1oAZqVTv8u\r\n1OIP68h5K+LRokk2zkof9Gl59/pzOiRFVDgRTaH64M1MCNy8a5UQXwJlg2md\r\ns1IOVP62lnAD2vLOkLRFxYrXcRhz9L0aDkHjR51VuqpXVDOcf6GLH9hJQgJH\r\n0ZEo+sJcIz/+WVJFKztx8TVbGSg/hQDImVE=\r\n=lOIv\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fs-minipass_3.0.0_1670973419654_0.1358387811897248"}, "_hasShrinkwrap": false}, "3.0.1": {"name": "fs-minipass", "version": "3.0.1", "main": "lib/index.js", "scripts": {"test": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "snap": "tap", "posttest": "npm run lint"}, "keywords": [], "author": {"name": "GitHub Inc."}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/fs-minipass.git"}, "bugs": {"url": "https://github.com/npm/fs-minipass/issues"}, "homepage": "https://github.com/npm/fs-minipass#readme", "description": "fs read and write streams based on minipass", "dependencies": {"minipass": "^4.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.1", "@npmcli/template-oss": "4.11.3", "mutate-fs": "^2.1.1", "tap": "^16.3.2"}, "tap": {"check-coverage": true, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.11.3"}, "gitHead": "e39aded796f01a22adc01ad41af495bf039541b3", "_id": "fs-minipass@3.0.1", "_nodeVersion": "16.16.0", "_npmVersion": "9.4.0", "dist": {"integrity": "sha512-MhaJDcFRTuLidHrIttu0RDGyyXs/IYHVmlcxfLAEFIWjc1vdLAkdwT7Ace2u7DbitWC0toKMl5eJZRYNVreIMw==", "shasum": "853809af15b6d03e27638d1ab6432e6b378b085d", "tarball": "https://registry.npmjs.org/fs-minipass/-/fs-minipass-3.0.1.tgz", "fileCount": 4, "unpackedSize": 14386, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFDRl0oFglMouCr0jS7YQSim/l6UMSiupBJhiq9jaKX/AiEAm8dIsO+nBL0XAwOI5Z6Vfhjvd2+aRmlsHFAxGqTHZ3M="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj1/tMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqnkw/8DthNkyGMN8HDx8W+8O92VBL05HN9gGDOqwqxGqBsmTjiFU8F\r\n7ESo0ff8/MtOlfO4bu2htLIp2y7ukq7eyTB9dDHOxPeOQHtL0kSNdmq1sgm9\r\n0YRfzJoKyW+uk9RT07PzsakXHtru9lj9LdSUI2eihmQ5VtAJWslGL/eRr3rR\r\nOG+E7zjbS3LUhDgN/w167Mbq5xP6tfLmOW+XzxzgiB0UrbvFqcLuPsxuJVTm\r\nn6hP6rTyMcYzpRkpRcq3ztIdwbrf/0C1Ds9pTH+lI7LkeNnPZcThZ9oUhTLe\r\n8oYVvmpltcgYQLJc2J4bnd2OeyA51ezeGCpBQupwlNZ9mWOujGkIY1UHwXpk\r\nHHPBYHKztgjX1FFcU7wPGpgfdWaw25BgQaX3QSMZzkiUVg6Mkmeq2IUl3juF\r\n/bHuKj3yRe6DDQR6gb+lxAyMf1/C61+vVA53QjR8mO7UfL5x5REBFdDEdyK9\r\n1pSm2obAe1Hi5+1Sr0GSWChgXL4p0O+ykMHy069A+GzEfFuCzQp6tByyNK87\r\nsqr3F4K2KE/jXmt6bsyxxszdNpdQtv4d/ffBzY0h6rcij4LjiHNBSxLa3NeH\r\nAEU5B2x5lE+BgwODFCqUO76VbchPsPwTw/SqMFaWPgeWJOIZmz7kMm2is7oP\r\ntjvrfLCv6ICBR6oRnXYD9iK/9XuuLVQJmTo=\r\n=dSGx\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "nlf", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fs-minipass_3.0.1_1675098956090_0.5243042960176207"}, "_hasShrinkwrap": false}, "3.0.2": {"name": "fs-minipass", "version": "3.0.2", "main": "lib/index.js", "scripts": {"test": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "snap": "tap", "posttest": "npm run lint"}, "keywords": [], "author": {"name": "GitHub Inc."}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/fs-minipass.git"}, "bugs": {"url": "https://github.com/npm/fs-minipass/issues"}, "homepage": "https://github.com/npm/fs-minipass#readme", "description": "fs read and write streams based on minipass", "dependencies": {"minipass": "^5.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.1", "@npmcli/template-oss": "4.14.1", "mutate-fs": "^2.1.1", "tap": "^16.3.2"}, "tap": {"check-coverage": true, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.14.1", "publish": "true"}, "gitHead": "3c7ba1ef5cd0cb2571c2729fb6e070ab8ee93b20", "_id": "fs-minipass@3.0.2", "_nodeVersion": "18.16.0", "_npmVersion": "9.6.5", "dist": {"integrity": "sha512-2GAfyfoaCDRrM6jaOS3UsBts8yJ55VioXdWcOL7dK9zdAuKT71+WBA4ifnNYqVjYv+4SsPxjK0JT4yIIn4cA/g==", "shasum": "5b383858efa8c1eb8c33b39e994f7e8555b8b3a3", "tarball": "https://registry.npmjs.org/fs-minipass/-/fs-minipass-3.0.2.tgz", "fileCount": 4, "unpackedSize": 14413, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/fs-minipass@3.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDZqXOPsRwepeEPGe94ZFCPsupu97SPuYxMHoLBL9WyPAiA3Wdd0YK4p2EXQBJhhIHIrPFb+1zbAJOUB3u79zKJH8A=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkSWqMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpJkg/9H5NvaNpeJ/YaQPofR8LWnsQzkoFbEVp5JxSvMSfa2CPO6s6e\r\nZXQMKnrXVGszWfbneYBkXwClZ/6mGmmBPrOami6gRoC6N7lZaYDw2dmeW11O\r\ncqhYX34pSfXPPpCPxLtvfEvfxOU5xFtnAXatVGVotv/MtKsyTm6hEu88FQpR\r\nzlCo3/uf+Z12fSIA05sJgL4IjPn123UdazKaS8oCRiiKx6AMPmnTEa+iTSam\r\nJqQNArSidAWcXxnz8ziloc1PE3AamxQbGYDYjzSVkFnazQveii6EmqJXPlE1\r\n9CIulJoTPrJM4H1wpx2rlLdueA3Ubgulwz7j/Hek/61W8HHUK7H3IhIgCZ7p\r\nyQ1FL7ecAVUbNudw0HnHAQNBR2imbg0cEHATWFLRseiPIwmNv+e148XqIpzj\r\nOw/TqMt34+2xiqEfDfY2kGJsBGTBW0Tl3RyfVNRItXrhytWOS6LcMy2wz2le\r\nfU25TUWkdpOxACfdvvK7TAPsRTmLwlZbKRO2QnZx6MPITllTgK1Yi1E9fLFv\r\ny+qQTO2cGdPN71fk4U2QDnXs9aDKrz7ssLDSaY1EIQvt49Ozbwi3ORRSaI3f\r\nnsKG2ut7PMHa6X6oFRbRn40rKvrjk5hchjIO0jn4itCdaagxLMOqFoUyOP23\r\nZmSUzNCzMHbI5f8I9VlE3gkDHFN66GAPDkU=\r\n=B85e\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fs-minipass_3.0.2_1682533004483_0.8682804961903066"}, "_hasShrinkwrap": false}, "3.0.3": {"name": "fs-minipass", "version": "3.0.3", "main": "lib/index.js", "scripts": {"test": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "snap": "tap", "posttest": "npm run lint"}, "keywords": [], "author": {"name": "GitHub Inc."}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/fs-minipass.git"}, "bugs": {"url": "https://github.com/npm/fs-minipass/issues"}, "homepage": "https://github.com/npm/fs-minipass#readme", "description": "fs read and write streams based on minipass", "dependencies": {"minipass": "^7.0.3"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.1", "@npmcli/template-oss": "4.18.0", "mutate-fs": "^2.1.1", "tap": "^16.3.2"}, "tap": {"check-coverage": true, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.18.0", "publish": "true"}, "_id": "fs-minipass@3.0.3", "gitHead": "8348d32797eadf1bad05fae1d8ba2af3da53cd44", "_nodeVersion": "18.17.0", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-XUBA9XClHbnJWSfBzjkm6RvPsyg3sryZt06BEQoXcF7EK/xpGaQYJgQKDJSUH5SGZ76Y7pFx1QBnXz09rU5Fbw==", "shasum": "79a85981c4dc120065e96f62086bf6f9dc26cc54", "tarball": "https://registry.npmjs.org/fs-minipass/-/fs-minipass-3.0.3.tgz", "fileCount": 4, "unpackedSize": 14413, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/fs-minipass@3.0.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD9zx1hl03IJIeWe9sdBFvux0IyXR6zf9Oguze7gejg5wIgTcY3Z3fPqn/3NAF6k5ixMQ1IssMYG446B4ZdiOMtTBA="}]}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fs-minipass_3.0.3_1692039587032_0.9236891197271018"}, "_hasShrinkwrap": false}}, "readme": "# fs-minipass\n\nFilesystem streams based on [minipass](http://npm.im/minipass).\n\n4 classes are exported:\n\n- ReadStream\n- ReadStreamSync\n- WriteStream\n- WriteStreamSync\n\nWhen using `ReadStreamSync`, all of the data is made available\nimmediately upon consuming the stream.  Nothing is buffered in memory\nwhen the stream is constructed.  If the stream is piped to a writer,\nthen it will synchronously `read()` and emit data into the writer as\nfast as the writer can consume it.  (That is, it will respect\nbackpressure.)  If you call `stream.read()` then it will read the\nentire file and return the contents.\n\nWhen using `WriteStreamSync`, every write is flushed to the file\nsynchronously.  If your writes all come in a single tick, then it'll\nwrite it all out in a single tick.  It's as synchronous as you are.\n\nThe async versions work much like their node builtin counterparts,\nwith the exception of introducing significantly less Stream machinery\noverhead.\n\n## USAGE\n\nIt's just streams, you pipe them or read() them or write() to them.\n\n```js\nconst fsm = require('fs-minipass')\nconst readStream = new fsm.ReadStream('file.txt')\nconst writeStream = new fsm.WriteStream('output.txt')\nwriteStream.write('some file header or whatever\\n')\nreadStream.pipe(writeStream)\n```\n\n## ReadStream(path, options)\n\nPath string is required, but somewhat irrelevant if an open file\ndescriptor is passed in as an option.\n\nOptions:\n\n- `fd` Pass in a numeric file descriptor, if the file is already open.\n- `readSize` The size of reads to do, defaults to 16MB\n- `size` The size of the file, if known.  Prevents zero-byte read()\n  call at the end.\n- `autoClose` Set to `false` to prevent the file descriptor from being\n  closed when the file is done being read.\n\n## WriteStream(path, options)\n\nPath string is required, but somewhat irrelevant if an open file\ndescriptor is passed in as an option.\n\nOptions:\n\n- `fd` Pass in a numeric file descriptor, if the file is already open.\n- `mode` The mode to create the file with. Defaults to `0o666`.\n- `start` The position in the file to start reading.  If not\n  specified, then the file will start writing at position zero, and be\n  truncated by default.\n- `autoClose` Set to `false` to prevent the file descriptor from being\n  closed when the stream is ended.\n- `flags` Flags to use when opening the file.  Irrelevant if `fd` is\n  passed in, since file won't be opened in that case.  Defaults to\n  `'a'` if a `pos` is specified, or `'w'` otherwise.\n", "maintainers": [{"email": "<EMAIL>", "name": "isaacs"}], "time": {"modified": "2024-04-20T21:33:59.695Z", "created": "2017-09-11T04:50:17.617Z", "1.0.0": "2017-09-11T04:50:17.617Z", "1.1.0": "2017-09-12T03:03:08.362Z", "1.1.1": "2017-09-12T05:26:06.635Z", "1.1.2": "2017-09-12T05:35:29.961Z", "1.2.0": "2017-09-17T19:47:09.505Z", "1.2.1": "2017-09-21T20:56:33.162Z", "1.2.2": "2017-11-14T09:12:07.989Z", "1.2.3": "2017-11-14T10:00:26.779Z", "1.2.4": "2018-01-04T22:52:43.169Z", "1.2.5": "2018-01-04T23:05:44.837Z", "1.2.6": "2019-05-15T17:43:37.410Z", "1.2.7": "2019-09-16T19:00:57.673Z", "2.0.0": "2019-09-30T20:21:08.085Z", "2.0.1": "2020-01-20T18:25:09.933Z", "2.1.0": "2020-01-21T17:39:07.791Z", "3.0.0": "2022-12-13T23:16:59.852Z", "3.0.1": "2023-01-30T17:15:56.273Z", "3.0.2": "2023-04-26T18:16:44.694Z", "3.0.3": "2023-08-14T18:59:47.341Z"}, "homepage": "https://github.com/npm/fs-minipass#readme", "keywords": [], "repository": {"type": "git", "url": "git+https://github.com/npm/fs-minipass.git"}, "author": {"name": "GitHub Inc."}, "bugs": {"url": "https://github.com/npm/fs-minipass/issues"}, "license": "ISC", "readmeFilename": "README.md"}