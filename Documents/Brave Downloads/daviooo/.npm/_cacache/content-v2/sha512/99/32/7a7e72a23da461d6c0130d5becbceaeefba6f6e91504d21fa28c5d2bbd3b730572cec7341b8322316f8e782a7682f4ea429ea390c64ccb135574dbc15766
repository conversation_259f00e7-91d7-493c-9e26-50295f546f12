{"_id": "shebang-regex", "_rev": "10-83fdd200435bd15f16b7cbd9add1e1a4", "name": "shebang-regex", "description": "Regular expression for matching a shebang line", "dist-tags": {"latest": "4.0.0"}, "versions": {"1.0.0": {"name": "shebang-regex", "version": "1.0.0", "description": "Regular expression for matching a shebang", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/shebang-regex"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js"}, "files": ["index.js"], "keywords": ["re", "regex", "regexp", "shebang", "match", "test"], "devDependencies": {"ava": "0.0.4"}, "gitHead": "cb774c70d5f569479ca997abf8ee7e558e617284", "bugs": {"url": "https://github.com/sindresorhus/shebang-regex/issues"}, "homepage": "https://github.com/sindresorhus/shebang-regex", "_id": "shebang-regex@1.0.0", "_shasum": "da42f49740c0b42db2ca9728571cb190c98efea3", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "da42f49740c0b42db2ca9728571cb190c98efea3", "tarball": "https://registry.npmjs.org/shebang-regex/-/shebang-regex-1.0.0.tgz", "integrity": "sha512-wpoSFAxys6b2a2wHZ1XpDSgD7N9iVjg29Ph9uV/uaP9Ex/KXlkTZTeddxDPSYQpgvzKLGJke2UU0AzoGCjNIvQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCE7dg6EIpMeBB4o/55QVmSiKAhCKAtxA2qZC9HbcmB0QIgaLs6xTt8hgFLOHUzyMrm2czRufJlc6l/e2XYPKZReZI="}]}, "directories": {}}, "2.0.0": {"name": "shebang-regex", "version": "2.0.0", "description": "Regular expression for matching a shebang line", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/shebang-regex"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["re", "regex", "regexp", "shebang", "match", "test", "line"], "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "62d96ada2264e959e28874b486de85a9593c69ae", "bugs": {"url": "https://github.com/sindresorhus/shebang-regex/issues"}, "homepage": "https://github.com/sindresorhus/shebang-regex", "_id": "shebang-regex@2.0.0", "_shasum": "f500bf6851b61356236167de2cc319b0fd7f0681", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.1", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "f500bf6851b61356236167de2cc319b0fd7f0681", "tarball": "https://registry.npmjs.org/shebang-regex/-/shebang-regex-2.0.0.tgz", "integrity": "sha512-qZ2//Zb9flLDxFym9xqhDyquM5WRdmuk/vviks/2cr9c5Vm+mX578TP8DwUc9fbfX58Tgeef2og1elYnkHli2w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEnxo7yRPdiXwa3IuQ0X+yax1Uey9WxeHKsMub5VlfzGAiEAwgkhhtuS9m4jH4Bd/gZm719nkTpjZ2oM5FDgatBouBw="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "3.0.0": {"name": "shebang-regex", "version": "3.0.0", "description": "Regular expression for matching a shebang line", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/shebang-regex.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["regex", "regexp", "shebang", "match", "test", "line"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "gitHead": "27a9a6f0f85c04b9c37a43cf7503fdb58f5ae8d6", "bugs": {"url": "https://github.com/sindresorhus/shebang-regex/issues"}, "homepage": "https://github.com/sindresorhus/shebang-regex#readme", "_id": "shebang-regex@3.0.0", "_nodeVersion": "10.15.3", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==", "shasum": "ae16f1644d873ecad843b0307b143362d4c42172", "tarball": "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz", "fileCount": 5, "unpackedSize": 2828, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcxDL7CRA9TVsSAnZWagAAWYAP/R5EbXIFL5aDT7Yavy5P\nrcZIWN4fQwwlFp/YdSocsX/lBlwgJ0axGZsoVxZ78y5TjJZbvJ+6YwYkUGuf\n4bCkNLWdnmI9xNdShULSc1gExlnH/+3ubiKoosm9hJ2z+5CFNB1OWmuxSIxw\nIo/6aSYhhzFmWDrwKkwB4fbb2SMMuyGnHCAq2TolakuSk75OJlHF7xRZWZ5k\nko/2f0CiSaP1OllRMBYWssHCAaEjdXqjOy1fCvoHgTw+aPYWZWnZSlKyQkzu\n20EAgEVhYQkiyZxNNCj40I6VTgr2mZnxTftZx49HwqfVs5qoU8DmCit+pMJd\nY+chiFfltBymUrF54j986kRUc/MkXNsWsRPFzzVg/9mOJAO+n9dW6rMcAM4f\nLbB7ZT5ah8PbgcXlUVCM4nEexLSFKFUhB9GATw/SSEgG/rTdd8sskIPb7lGj\nwMdqDvYh+oTc2r3r8Dl4lQRt/hrmHX3jk5Mf4de+IAPld5+9YDkqCcsev2w8\n44LoqmclULeuxcJPo2IA6K/gbKBmOjWjIuyBdV+8OX4xZFM4Eu9n1NTT7olT\nbGSIkVxASNiIqiatCMl5qqKnClqzY4F+bDVNe+KK6aaPvvV4ShktD4I682pH\nzULKVxadENPKGKhX/Y2xu7va9+GqbHTVwODuFH77n0AXf+BCirHFiKbGSqZg\nGT1V\r\n=FKXn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD9D5iHzHiq8RRcvBjhnVc1TDXKPStoeEAQrqtkEg9qXAIgPFNBElrbSUhCoAjEd/BRJo48gQudG1I1KkUMNo6x+/E="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/shebang-regex_3.0.0_1556361979130_0.30848524113197073"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "shebang-regex", "version": "4.0.0", "description": "Regular expression for matching a shebang line", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/shebang-regex.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["regex", "regexp", "shebang", "match", "test", "line"], "devDependencies": {"ava": "^3.15.0", "tsd": "^0.17.0", "xo": "^0.44.0"}, "gitHead": "a2e85dfd79f7c6d45b2d63ba4989d245e5f8b64a", "bugs": {"url": "https://github.com/sindresorhus/shebang-regex/issues"}, "homepage": "https://github.com/sindresorhus/shebang-regex#readme", "_id": "shebang-regex@4.0.0", "_nodeVersion": "12.22.1", "_npmVersion": "7.10.0", "dist": {"integrity": "sha512-YSKeSljCliLkWidW84GWL1HCguI0iEqhnBOLhrVXw/fN9he9ngekCy8zqJ1jXTPYmJ3Xkf3gLuNDVHQWdRqinw==", "shasum": "86b8202f10d28f4da056d4b905043128b3a6a0a7", "tarball": "https://registry.npmjs.org/shebang-regex/-/shebang-regex-4.0.0.tgz", "fileCount": 5, "unpackedSize": 3234, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhFvu9CRA9TVsSAnZWagAAyoEP/i+QYJYuSbsrBBAI6yzH\nT3YB0hRhb9HMouskHtGW8LspxwPfxJ5Addd88MU74IfBbnoqJHd6fknQ+Fcb\nP7750BXpLgHuWn4+QtShCAis0tcEzWv6zmI+MINm79lfyRKNkTyXfs1lYccA\nWhRWAa+UmnDHJvrzbFAiS+EIH1FpXgKudkshKEOq/O4Ndt5P+0HGe999Ug76\nKRf9Qe+yLTM5cgsfDMo8a5+EEOlJpRRzt8VdaCy59iL6vJGi07OC6QA12KNx\nMCbftEYkKElpiPTK7jq6NOu7dJmSUc4alHuJRrdb1wfKzMGm8oiLB+DV1006\nmQxtyLgYDA8HrPhFNdPy8b48kNGcO6UWQwsJSjlUnNLwOmMZucA0H0bkktQf\nKu/0ZvZpjIxhXC37xzKoBAYn8scOUhfSHWskCA+jTWYhg7QZ5CkFxPzB2D4S\nvliFgB60l41lsXx2kq7SAd1RcfyH3BU2yoRIxuGuS4nbqEXZaiML1fk1CyZB\nudvdI2nAFcYRccKxngL210awtMN7uctROx+r/rJI77Tq1vj3lrCnQHoMl7S2\nEwygufHlUE3o1FqsFFmMyj8OU/l/5obn1UNZLTaEpCiBfsyZVCJ/vJjdizCt\nHjYOA/AhqH0ObdUa6gH3nH1tbMPnHhtrCKvesKqBDQGK1145plOhj4u3vGli\nUIOw\r\n=8x2+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCS1APGcb5vmKETU2vN9GlwktxBS9hlbbfoKzD8tphGqAIhAO52DW2DVUwAFjeJUs5AgUiXT0+bGMhEjYK41wZbhcxF"}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/shebang-regex_4.0.0_1628896189815_0.08775966024726811"}, "_hasShrinkwrap": false}}, "readme": "# shebang-regex\n\n> Regular expression for matching a [shebang](https://en.wikipedia.org/wiki/Shebang_(Unix)) line\n\n## Install\n\n```\n$ npm install shebang-regex\n```\n\n## Usage\n\n```js\nimport shebangRegex from 'shebang-regex';\n\nconst string = '#!/usr/bin/env node\\nconsole.log(\"unicorns\");';\n\nshebangRegex.test(string);\n//=> true\n\nshebangRegex.exec(string)[0];\n//=> '#!/usr/bin/env node'\n\nshebangRegex.exec(string)[1];\n//=> '/usr/bin/env node'\n```\n\n---\n\n<div align=\"center\">\n\t<b>\n\t\t<a href=\"https://tidelift.com/subscription/pkg/npm-shebang-regex?utm_source=npm-shebang-regex&utm_medium=referral&utm_campaign=readme\">Get professional support for this package with a Tidelift subscription</a>\n\t</b>\n\t<br>\n\t<sub>\n\t\tTidelift helps make open source sustainable for maintainers while giving companies<br>assurances about security, maintenance, and licensing for their dependencies.\n\t</sub>\n</div>\n", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-06-16T22:40:59.596Z", "created": "2015-02-17T05:24:58.945Z", "1.0.0": "2015-02-17T05:24:58.945Z", "2.0.0": "2015-12-04T16:40:07.830Z", "3.0.0": "2019-04-27T10:46:19.256Z", "4.0.0": "2021-08-13T23:09:49.954Z"}, "homepage": "https://github.com/sindresorhus/shebang-regex#readme", "keywords": ["regex", "regexp", "shebang", "match", "test", "line"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/shebang-regex.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/shebang-regex/issues"}, "license": "MIT", "readmeFilename": "readme.md", "users": {"hualei": true, "flumpus-dev": true}}