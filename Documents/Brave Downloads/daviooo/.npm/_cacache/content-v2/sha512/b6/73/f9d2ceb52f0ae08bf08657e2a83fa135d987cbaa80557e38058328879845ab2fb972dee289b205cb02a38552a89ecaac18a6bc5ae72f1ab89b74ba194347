{"_id": "object-is", "_rev": "22-2e594567c73b6e74fb8f1a8d67376352", "name": "object-is", "description": "ES2015-compliant shim for Object.is - differentiates between -0 and +0", "dist-tags": {"latest": "1.1.6"}, "versions": {"0.0.0": {"name": "object-is", "version": "0.0.0", "description": "ES6-compliant shim for Object.is - differentiates between -0 and +0", "author": {"name": "<PERSON>"}, "license": "MIT", "main": "index.js", "scripts": {"test": "node test.js", "coverage": "covert test.js", "coverage-quiet": "covert test.js --quiet"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object-is.git"}, "bugs": {"url": "https://github.com/ljharb/object-is/issues"}, "homepage": "https://github.com/ljharb/object-is", "keywords": ["is", "Object.is", "equality", "sameValueZero", "ES6", "shim", "polyfill"], "dependencies": {}, "devDependencies": {"tape": "~2.4.2", "covert": "~0.3.1"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..12.0", "opera/15.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "_id": "object-is@0.0.0", "dist": {"shasum": "65adab52f96f7071047ab2edf8d5e0d4230dc1b4", "tarball": "https://registry.npmjs.org/object-is/-/object-is-0.0.0.tgz", "integrity": "sha512-EbL4FQ5e4LczPr4ztl70Km7Mj+gcM81lgWwx8uUbTZAyAGs+rS+ayEFZjbxOUvTZBj8vcszHeb+B/t7OG4jVdQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDsU/hK2z9AR8isOmL6ClrTgbRr47GCyx1uqDdyWkpcOAIgXHF1zO1rX7lykaRWTSM+/FwseyrXl642xpquzoqBxnA="}]}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "directories": {}}, "1.0.0": {"name": "object-is", "version": "1.0.0", "description": "ES6-compliant shim for Object.is - differentiates between -0 and +0", "author": {"name": "<PERSON>"}, "license": "MIT", "main": "index.js", "scripts": {"test": "node test.js && npm run coverage", "coverage": "covert test.js", "coverage-quiet": "covert test.js --quiet"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object-is.git"}, "bugs": {"url": "https://github.com/ljharb/object-is/issues"}, "homepage": "https://github.com/ljharb/object-is", "keywords": ["is", "Object.is", "equality", "sameValueZero", "ES6", "shim", "polyfill"], "dependencies": {}, "devDependencies": {"tape": "~2.13.1", "covert": "~0.4.0"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..12.0", "opera/15.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "gitHead": "8811835bff203cf0dc0dee1342beeb749ea63e10", "_id": "object-is@1.0.0", "_shasum": "c4d85931da0009435ec9825ddbe578a7ff82c001", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "dist": {"shasum": "c4d85931da0009435ec9825ddbe578a7ff82c001", "tarball": "https://registry.npmjs.org/object-is/-/object-is-1.0.0.tgz", "integrity": "sha512-j52zdcg3SccjQ4A6D2dp4yHonKKkeO7t4E7QuzTCbNY00WripFaizZMbV+MIqbA6joosVFDkwcpxE6rm4OhAaA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCFb0lQTwzxbBHC/T4UIVfFrLUXpJROxW67wzeIeUIP8gIhAMtVlC6tP4x6ZiRBxmgMhfekEc/xjNs9x6CejP1FZ7St"}]}, "directories": {}}, "1.0.1": {"name": "object-is", "version": "1.0.1", "description": "ES6-compliant shim for Object.is - differentiates between -0 and +0", "author": {"name": "<PERSON>"}, "license": "MIT", "main": "index.js", "scripts": {"test": "npm run lint && node test.js && npm run coverage-quiet", "coverage": "covert test.js", "coverage-quiet": "covert test.js --quiet", "lint": "jscs *.js"}, "repository": {"type": "git", "url": "git://github.com/ljharb/object-is.git"}, "bugs": {"url": "https://github.com/ljharb/object-is/issues"}, "homepage": "https://github.com/ljharb/object-is", "keywords": ["is", "Object.is", "equality", "sameValueZero", "ES6", "shim", "polyfill"], "dependencies": {}, "devDependencies": {"tape": "~2.14.0", "covert": "~1.0.0", "jscs": "~1.5.9"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..12.0", "opera/15.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "gitHead": "a51367e2c85f408211982ccb3ed2f1cc2da70d05", "_id": "object-is@1.0.1", "_shasum": "0aa60ec9989a0b3ed795cf4d06f62cf1ad6539b6", "_from": ".", "_npmVersion": "1.4.23", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "dist": {"shasum": "0aa60ec9989a0b3ed795cf4d06f62cf1ad6539b6", "tarball": "https://registry.npmjs.org/object-is/-/object-is-1.0.1.tgz", "integrity": "sha512-WY2d4Y9s39AGFRtDlJDyNHFHOTQ5MbFzYWt9dHNYn4P9zCR+wpCo1IqWd+xJVEX5aNhCFXzTptJ8H2kRIHWF3Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH3kzo8DoFgD8vZTcZBWQih1ix4/X1p9P3DdEYFSgU+pAiBh+sFp7D/oftMelLIXkfclKItN5TaLhzQ6CTeLZ1ehRw=="}]}, "directories": {}}, "1.0.2": {"name": "object-is", "version": "1.0.2", "description": "ES2015-compliant shim for Object.is - differentiates between -0 and +0", "author": {"name": "<PERSON>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "main": "index.js", "scripts": {"version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "pretest": "npm run lint", "tests-only": "node test", "test": "npm run tests-only", "posttest": "npx aud", "coverage": "covert test", "lint": "eslint ."}, "repository": {"type": "git", "url": "git://github.com/es-shims/object-is.git"}, "bugs": {"url": "https://github.com/es-shims/object-is/issues"}, "homepage": "https://github.com/es-shims/object-is", "keywords": ["is", "Object.is", "equality", "sameValueZero", "ES6", "ES2015", "shim", "polyfill"], "dependencies": {}, "devDependencies": {"@ljharb/eslint-config": "^15.0.2", "auto-changelog": "^1.16.2", "covert": "^1.1.1", "eslint": "^6.7.2", "has-symbols": "^1.0.1", "tape": "^4.11.0"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..12.0", "opera/15.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "gitHead": "082fdb0ca51a1a363d6fdb533f988f0fabe7d10a", "_id": "object-is@1.0.2", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.1", "dist": {"integrity": "sha512-Epah+btZd5wrrfjkJZq1AOB9O6OxUQto45hzFd7lXGrpHPGE0W1k+426yrZV+k6NJOzLNNW/nVsmZdIWsAqoOQ==", "shasum": "6b80eb84fe451498f65007982f035a5b445edec4", "tarball": "https://registry.npmjs.org/object-is/-/object-is-1.0.2.tgz", "fileCount": 9, "unpackedSize": 16498, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd9ytSCRA9TVsSAnZWagAA5koP/3OpM9HTrTeBXLXbtfBO\nWMzdrsFzD/67aiRs7jejkQqPHbUKc3l/gdB12zUSFGsFb3wP9LPTYB6dyQ94\nd8j6SuInoodlidjxrDrq2LD4LxKwcN0CW+iw8/DzwmxG0017KLLam/ykbPzQ\nvnI3PM8fMiUdXdwkUoqIr/louLr3SmyJ9fO1NcFGgNFG88gl/W8QOoa6Oz08\nOy3jjBdJSyVLvf5tl1yX1C+F0fn7F5Fhtt2AL0IBok+wTIHNWr8Oh1Mmo6jg\n+BGJEAtWRBYdyuUG+agL1JQ+crnoMjnrwJ57aAN6satjUFyH4UT2/i7Y8UNI\n4T1raOrZQiZNRSCh3DVfcr58xFjrPmlYSKbpcY04QMnkqQjewFK4QfR+Wq5p\nzbSnWug8oIiHZ0j7CWvI0VtSOQRF4gez4qnvgnufTHujPPoff8B17NTIwZgs\n/zRQyLaez0/tnXw8ayvjicdu0EQIZjF2Lcalten1D8TftE+lmdzgvx3KXDLj\nRQPQQD/v9O/dXU7cqi0mN7mTK8NgzSkzCCEVnmiIwVOxlxa31Q3ublkdcsSK\n7PPQJelesj0a9fju6Et+0Yl6meKImOYhyYI5nv+IBADHll4bNiTkwY+Gbgnc\n7+igavjjbJTfCu7qEQAuGgRseLxGLkk8FDPsZQ3pmhAiuXacc3UYsY00F4Ms\nmqCI\r\n=2AeM\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE6IvJFYB/FtUoCgs3tQdntdePSA5EArWiCLMkGs5JfLAiEAg1ADQs0jH/+5U6ky+dX+MZbP6ekhXvU179wyzklRqEc="}]}, "maintainers": [{"email": "<EMAIL>", "name": "es-shims-owner"}, {"email": "<EMAIL>", "name": "lj<PERSON>b"}, {"email": "<EMAIL>", "name": "substack"}], "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/object-is_1.0.2_1576479570145_0.7229938222854198"}, "_hasShrinkwrap": false}, "1.1.0": {"name": "object-is", "version": "1.1.0", "description": "ES2015-compliant shim for Object.is - differentiates between -0 and +0", "author": {"name": "<PERSON>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "main": "index.js", "scripts": {"prepublish": "safe-publish-latest", "pretest": "npm run lint", "tests-only": "node test", "test": "npm run tests-only", "posttest": "npx aud --production", "coverage": "covert test", "prelint": "es-shim-api", "lint": "eslint .", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/es-shims/object-is.git"}, "bugs": {"url": "https://github.com/es-shims/object-is/issues"}, "homepage": "https://github.com/es-shims/object-is", "keywords": ["is", "Object.is", "equality", "sameValueZero", "ES6", "ES2015", "shim", "polyfill", "es-shim API"], "dependencies": {}, "devDependencies": {"@es-shims/api": "^2.1.2", "@ljharb/eslint-config": "^16.0.0", "aud": "^1.1.0", "auto-changelog": "^2.0.0", "covert": "^1.1.1", "eslint": "^6.8.0", "has-symbols": "^1.0.1", "safe-publish-latest": "^1.1.4", "tape": "^5.0.0-next.5"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..12.0", "opera/15.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "gitHead": "2c559ab591dc6b43df491f8b9eac5db5d401681f", "_id": "object-is@1.1.0", "_nodeVersion": "13.12.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-wznR5+ya11MdnkLq+oYePGjW2ge4RY5DVSwa3iKuDCpvLsYGnp24Qy5EzVRgyMHEuEkKd+dX/1JpAT6QxZXq2g==", "shasum": "57e5e236f2831066c82948cbd735c24a9e54084d", "tarball": "https://registry.npmjs.org/object-is/-/object-is-1.1.0.tgz", "fileCount": 13, "unpackedSize": 18380, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeld/nCRA9TVsSAnZWagAAH1QP/R4suubDK/XkvmV4tKLp\ngmPRaaVtuSHa736FsJa8u0tp8SLhOiD0jPNF6AbOi/PIFQOGOZeS3epN9Y/K\nqNrq6UeQL4gM28xuF4OSACzAUTw+LeiUGMItu19x7Qwlm351yuzb1pg9dRDD\nnmJLxs3J8H25xN0SM7asC+o7zUtHpDVxITrgPrTubmPwR/D3ZVBvdtE7nUc8\n0Ai/++V7M/nj65iT2PV13By+E78cyL0H+PJEcJQAddlgI8z1STySWcCFY1eg\nHrjudeh8xmPnb9gX7HFgPS5jtaF+bhnI4ehTH1hdWBsSsQ4d3QJjkz1ql4vN\niQPOm92+z2xF/ajgq8PBEUZ3JDaK2PoXouGAPgtKW/uxxapqMIxm4j32TpAd\nz4ZTsZamK4uI0uA5rSFz2TN0YLycIA+gZ2SWEeSoHJyAYOF4+ER+k3SOywxo\n1stnhn1bTgPY1qUNweff0AxVb6tnS/vDzuaHbB8SKJBcgbi4DcGzBM1si2gy\nR/dzHX8HRH43BJIQxaLD0BLXgd1GX3dBtIRaqq1+omrOPIZPt/V20Bw9rlTt\nnW+qtOFbedNwZ5PxkTdmt5iARNzH9uWrzxCVkTx57jD1gMrXid74ra7Unvat\nqvXRdBE3GBg7lydyJnrk78n93JkqLBDGKT2HJqY77BOUD0vMbIRLj1xGvmjE\n19tO\r\n=ptum\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEw2iNNGceTXPN40N2dSUGTxf7a/DtsEU1KtzDNUSeHfAiA7r7pZpWQWMidH33ThWxcZYkkr/Z0xbHBLlzIt6vhItw=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "lj<PERSON>b"}, {"email": "<EMAIL>", "name": "substack"}], "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/object-is_1.1.0_1586880486475_0.3625097471806267"}, "_hasShrinkwrap": false}, "1.1.1": {"name": "object-is", "version": "1.1.1", "description": "ES2015-compliant shim for Object.is - differentiates between -0 and +0", "author": {"name": "<PERSON>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "main": "index.js", "scripts": {"prepublish": "safe-publish-latest", "pretest": "npm run lint", "tests-only": "node test", "test": "npm run tests-only", "posttest": "npx aud --production", "coverage": "covert test", "prelint": "es-shim-api", "lint": "eslint .", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/es-shims/object-is.git"}, "bugs": {"url": "https://github.com/es-shims/object-is/issues"}, "homepage": "https://github.com/es-shims/object-is", "keywords": ["is", "Object.is", "equality", "sameValueZero", "ES6", "ES2015", "shim", "polyfill", "es-shim API"], "dependencies": {"define-properties": "^1.1.3"}, "devDependencies": {"@es-shims/api": "^2.1.2", "@ljharb/eslint-config": "^16.0.0", "aud": "^1.1.0", "auto-changelog": "^2.0.0", "covert": "^1.1.1", "eslint": "^6.8.0", "has-symbols": "^1.0.1", "safe-publish-latest": "^1.1.4", "tape": "^5.0.0-next.5"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..12.0", "opera/15.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "gitHead": "2d6ac911b256eafb6b54dda4e993931070da230e", "_id": "object-is@1.1.1", "_nodeVersion": "13.13.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-9wFT2CEamBmkYN6TEqnXCOzexDKR/N3hyUOQwtxIxZCZOyvlTUhKUejftM6+nciflJX0x2VLh7wNFWvIpmKGNw==", "shasum": "9314b6dc7f649891a1fc7f499a6cd3b0a6c01543", "tarball": "https://registry.npmjs.org/object-is/-/object-is-1.1.1.tgz", "fileCount": 13, "unpackedSize": 25554, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJelhFcCRA9TVsSAnZWagAAnXMP/34zIHmloOiJ5GCE49aI\nVW3IijmBH61f+Tf45EbOFFQw6v3X9SDgdge6ZogwHvbWjPA97s5dU5DfjnIo\nP1khJdlU/IWdrTRVpAg6KZgX8S9dZ4GBNcgKi71d5IcnLflCBHPUpDliuDZK\nqkPIz0apz4IHSXkopyFyyrYFzpFwc0Gtj9hpXSnMZk1cpLpBGptSJWdHgPJl\nAWzPgeaNIAMMZVt0Y0L5m7Vet3dHGmnxiCqnUu4fbJbdIr1/DHZiRQf3WAgP\n8myP0F+HmPtb5jqAqzZmHqcMpPL9byRDnmASpvz1T2ZX0tPXfK4yuOPjozJA\n+oukV1AAT99WOLG+FOhfCeG5lKQX4PH0OhBk7k+NsuC6A27AcfPT8GGo5t7R\n030PGVQOLdxu7S9DIK7ekoBGyeUmuP36a3t4GFGLVTqIWsnsovm6v0qXNYte\nwh94oGgFfBFFnFSu75nmcp0AM0Sj+wE6RUv4vUcTlQH6/Sc60Aq1PmRBkA/d\nGURbHEjUPzWV81+gw/22EtMrpl9bUenoAzcdscR5aTEv6kBLXXHfoF9wV/et\n9qwYm+4tWNV63+AOVL1OCWZM8WQLAS9j5/8DHrrvpBBgF5kSfJ03O/7aAonR\nf5G+W+/O5YA5AWb/gHalJlW5FjwYdZuF8gabMPC6bH+DwY3NHMrogIH0034N\nAzoE\r\n=Ya0T\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDNP7XpgL7csgPuC75JqOIsKtL9xphlrykI49P/mgStyAIgTSegpV8mxeyUHS4imcflKJ99tY0fgFDbCLPen1CegAY="}]}, "maintainers": [{"email": "<EMAIL>", "name": "lj<PERSON>b"}, {"email": "<EMAIL>", "name": "substack"}], "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/object-is_1.1.1_1586893147687_0.7202577943507198"}, "_hasShrinkwrap": false}, "1.1.2": {"name": "object-is", "version": "1.1.2", "description": "ES2015-compliant shim for Object.is - differentiates between -0 and +0", "author": {"name": "<PERSON>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "main": "index.js", "scripts": {"prepublish": "safe-publish-latest", "pretest": "npm run lint", "tests-only": "node test", "test": "npm run tests-only", "posttest": "npx aud --production", "coverage": "covert test", "prelint": "es-shim-api --bound", "lint": "eslint .", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/es-shims/object-is.git"}, "bugs": {"url": "https://github.com/es-shims/object-is/issues"}, "homepage": "https://github.com/es-shims/object-is", "keywords": ["is", "Object.is", "equality", "sameValueZero", "ES6", "ES2015", "shim", "polyfill", "es-shim API"], "dependencies": {"define-properties": "^1.1.3", "es-abstract": "^1.17.5"}, "devDependencies": {"@es-shims/api": "^2.1.2", "@ljharb/eslint-config": "^16.0.0", "aud": "^1.1.0", "auto-changelog": "^2.0.0", "covert": "^1.1.1", "eslint": "^6.8.0", "has-symbols": "^1.0.1", "safe-publish-latest": "^1.1.4", "tape": "^5.0.0-next.5"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..12.0", "opera/15.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "gitHead": "f320ccf74845a3d4a7da7b03153c60c28955eb57", "_id": "object-is@1.1.2", "_nodeVersion": "13.13.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-5lHCz+0uufF6wZ7CRFWJN3hp8Jqblpgve06U5CMQ3f//6iDjPr2PEo9MWCjEssDsa+UZEL4PkFpr+BMop6aKzQ==", "shasum": "c5d2e87ff9e119f78b7a088441519e2eec1573b6", "tarball": "https://registry.npmjs.org/object-is/-/object-is-1.1.2.tgz", "fileCount": 13, "unpackedSize": 25937, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJelhQ5CRA9TVsSAnZWagAAexQP/1ltZexI1kiaTRK+P31+\niU5Fqfyh+OFkM7Cib8fYLTVv+Ed63TCKfxdxg9NTT+8pf3rklsYhlpIn02kZ\nHWJidKNaJQSjxLVkS51o42rXSlpxH4URWhbE/cPfUpJKusBGZNTvVbtBiUks\nhZfkrYwE33ggybAikJ2HeoLDTBZpUtv5mJNHgsdtT/MKAE4ckg0e8NtHyOSn\nFp2quuIRXFsdLTDUrKzHLEb6J4X3MicBNelq8vwaZAxuPf2ZedY8t7vT5GWc\nxn2PP+auXyjAz8ieemKwB4XfJeqErzj7j9uJ5nAwjbg8/LVmYybR273G/zuW\nGZ83XCOHX/gGvXLD/wrbH7jyIt5TTsYna6QMpPJK3HU85g/3h/+5bU1IGzhS\npDHc7qdvS4friP+/YrZ67kQPY4cRdbQX/TGUMTPZznRxVIHmd3ptCASa/6Hf\nS+OTXKV6qwF35PfXoW6XHUwsFMsyKy/NOL/MH8J3WQ03AavqcOIFRp8SKK6N\nuPHOSSWJGj6aJuCNcWVwi1O4q9A5MejVJFxT4TqPM0r6O1qGrfFKuGpa8I1N\ngYxlEDk8xQbrLK0zSYMsgNggqOZr0+iGh+r0MT8EeD9/CRw53cBmZxKibgES\nMvvSJCF1rCrCZH10mq0JXFqqk8/QzaJsce9ChXLnE4hPkPcgtI7KdjNNVNYa\nh0zJ\r\n=RjDk\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHcRrDyqq/DAVDmYUD8+XctxOF4ZtRcvG2lbP93pMHObAiEAh5Z8hPhwrZ6qVYWFeKaQBPKz+8kddT7pZAsPFDH04EI="}]}, "maintainers": [{"email": "<EMAIL>", "name": "lj<PERSON>b"}, {"email": "<EMAIL>", "name": "substack"}], "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/object-is_1.1.2_1586893880595_0.14621663101190285"}, "_hasShrinkwrap": false}, "1.1.3": {"name": "object-is", "version": "1.1.3", "description": "ES2015-compliant shim for Object.is - differentiates between -0 and +0", "author": {"name": "<PERSON>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "main": "index.js", "scripts": {"prepublish": "safe-publish-latest", "pretest": "npm run lint", "tests-only": "node test", "test": "npm run tests-only", "posttest": "npx aud --production", "coverage": "covert test", "prelint": "es-shim-api --bound", "lint": "eslint .", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/es-shims/object-is.git"}, "bugs": {"url": "https://github.com/es-shims/object-is/issues"}, "homepage": "https://github.com/es-shims/object-is", "keywords": ["is", "Object.is", "equality", "sameValueZero", "ES6", "ES2015", "shim", "polyfill", "es-shim API"], "dependencies": {"define-properties": "^1.1.3", "es-abstract": "^1.18.0-next.1"}, "devDependencies": {"@es-shims/api": "^2.1.2", "@ljharb/eslint-config": "^17.2.0", "aud": "^1.1.2", "auto-changelog": "^2.2.1", "covert": "^1.1.1", "eslint": "^7.10.0", "has-symbols": "^1.0.1", "safe-publish-latest": "^1.1.4", "tape": "^5.0.1"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..12.0", "opera/15.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "gitHead": "ba25251d13d0d0646b9896985eb48dd92706161a", "_id": "object-is@1.1.3", "_nodeVersion": "14.12.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-teyqLvFWzLkq5B9ki8FVWA902UER2qkxmdA4nLf+wjOLAWgxzCWZNCxpDq9MvE8MmhWNr+I8w3BN49Vx36Y6Xg==", "shasum": "2e3b9e65560137455ee3bd62aec4d90a2ea1cc81", "tarball": "https://registry.npmjs.org/object-is/-/object-is-1.1.3.tgz", "fileCount": 13, "unpackedSize": 19659, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfdPl8CRA9TVsSAnZWagAARM8P/26bP/SmvdymZv18pA6s\nb1Pc6DegdmeFZQuCyesCeyrRl+0huTNbNI2teo1dMU6xwgF7a4H1/YMZuf1L\nWve7FMHX2NHkVtmNOGpqoTJT4om7oapeSr+gCLjuc7FxI6dALJyb0CNMkNJp\nRHj3aSCCepNm8uSSix8HJzvpxFx/xZRXpeYMxlpmRuCdqhnDqwenW9+SpCJi\njNvTlV2Xp8F8ZR0SnGN8nBmOihNhjywrEC65r6zJJaoyuE3rCNLMBcndGqFt\nghZFqUF4uduLaWwcJ/mjn1GVNtDgEH6rP6pv4o6mdJ/RdyueHlVL/CZdED7b\n6DgK5AFg9qPKVflF36L8cNrWaEAdO50vIXxD08XSj1UFjCHcVmqK34kvRyMk\nqqv/UjQWLFCpVe1vFiVhjXjbc7RwRtZGYdYldN+OT1jpRSwBOBfOTpIdWoOI\nrEYGNXHAM4D4SlPdaKKdKrCHVlGMzd3Lw9V9Ma8dWCC9zHPjde+wyjmHKzKa\nsf8Me4HmnrnJ5waKLOLO5y8K1n74vf3yhxmv6zgiLXPqxO/R9+q37U9SgeSk\nYspmBT48FzNE0CRxeEfYyUO+vs0XQzOF7GZp1sZ/wa/SsX6jWpcBcE6VGUK0\nUroX0uno0TdCJsi9jcovue75ix+Mp5F+KvqzM5oReFVyYxvd0atNh3dxFwdD\ndWo5\r\n=Y2R/\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAFtPHoo4YCNiJmgujYOIIMBajvRqIynvPySCEV0kuGSAiEA6SIprHYRFxIT2px7jNOgZvHbb4AdmNOBoAfd2kMmDXw="}]}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/object-is_1.1.3_1601501563862_0.400024732395988"}, "_hasShrinkwrap": false}, "1.1.4": {"name": "object-is", "version": "1.1.4", "description": "ES2015-compliant shim for Object.is - differentiates between -0 and +0", "author": {"name": "<PERSON>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "main": "index.js", "scripts": {"prepublish": "safe-publish-latest", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "aud --production", "lint": "eslint .", "postlint": "es-shim-api --bound", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/es-shims/object-is.git"}, "bugs": {"url": "https://github.com/es-shims/object-is/issues"}, "homepage": "https://github.com/es-shims/object-is", "keywords": ["is", "Object.is", "equality", "sameValueZero", "ES6", "ES2015", "shim", "polyfill", "es-shim API"], "dependencies": {"call-bind": "^1.0.0", "define-properties": "^1.1.3"}, "devDependencies": {"@es-shims/api": "^2.1.2", "@ljharb/eslint-config": "^17.3.0", "aud": "^1.1.3", "auto-changelog": "^2.2.1", "eslint": "^7.14.0", "functions-have-names": "^1.2.1", "has-symbols": "^1.0.1", "nyc": "^10.3.2", "safe-publish-latest": "^1.1.4", "tape": "^5.0.1"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..12.0", "opera/15.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "gitHead": "4f0e99b3a9ddadf159ec31d6f99ac07a9cf6075e", "_id": "object-is@1.1.4", "_nodeVersion": "14.15.1", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-1ZvAZ4wlF7IyPVOcE1Omikt7UpaFlOQq0HlSti+ZvDH3UiD2brwGMwDbyV43jao2bKJ+4+WdPJHSd7kgzKYVqg==", "shasum": "63d6c83c00a43f4cbc9434eb9757c8a5b8565068", "tarball": "https://registry.npmjs.org/object-is/-/object-is-1.1.4.tgz", "fileCount": 22, "unpackedSize": 27454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfwKtFCRA9TVsSAnZWagAALZkP/13JZeRddeD/pYq541XR\nxPSOx5IMIwAcP+I1gYmWhF9T5P6s4jZ9BmKejfyco9Qsjo0hULoOhhzHkYS2\nzamSGRKjyy8xJ3gZy7sSF6yDFpsqkqI7Xta5SzuFfOA70EbXXbp4oHe9ZhgX\nVjKeZeLIty4HgYV+lwcST1l2e7lkQt2gpQvjYqb/z8M91FyWUh96UpZoMUj1\nDvGVDKHU+zbaTbpvDKIkcK+Nb33jBSa+0MrYrg4iqJXtNT6gSsDg9Wurpy22\nBs5aXbrqSTDsG7NRMgAdNxJqL2uwjp68SDKXtXOyK4/h//JhQdHEi90vzrTT\nJKpQ/LhmXYcX05lVFae5XVUYANvZKAFw8sPfjUmulVn5JypYDsH+gBJcxSmS\n2cYDjiv7y7Ou7Uk41aS+oFflkYAx0r8mrBvSSibgm9RupV7gL/ikCea2/PFm\njJZngWnaOXFDfxcH3K6DgshGL/WukS6z4V4B4bxV225k0UL6OWOyiZ+xeSuH\ndO5mIZPf/Zy5yIhRmhZye3OIV8tXZxIXpqppJHGlFC+DKuUmh9W2TQIFyWA8\nD0PiSvAO7iATI55Qmej3lsnFGD+7JHWkLG0g7GIzht7NGH+Tjgio0Ksogw9d\nDXfm+oDYsL5Y8zrTGQbk3boV91MYZqVpL0Cl7b0/v7W4yd30VOCuF5mcWS0k\ncb+n\r\n=AY7L\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHhTSx19uo81/hbYffTs7M3cVCnihaGtBmnfpmvIPf02AiBSw+la0xyhbAVlay+nOMjDlYv2Ej4hIzZ4ZpqW0yQJ7w=="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/object-is_1.1.4_1606462277287_0.7345627632347489"}, "_hasShrinkwrap": false}, "1.1.5": {"name": "object-is", "version": "1.1.5", "description": "ES2015-compliant shim for Object.is - differentiates between -0 and +0", "author": {"name": "<PERSON>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "main": "index.js", "scripts": {"prepublish": "safe-publish-latest", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "aud --production", "lint": "eslint .", "postlint": "es-shim-api --bound", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/es-shims/object-is.git"}, "bugs": {"url": "https://github.com/es-shims/object-is/issues"}, "homepage": "https://github.com/es-shims/object-is", "keywords": ["is", "Object.is", "equality", "sameValueZero", "ES6", "ES2015", "shim", "polyfill", "es-shim API"], "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.3"}, "devDependencies": {"@es-shims/api": "^2.1.2", "@ljharb/eslint-config": "^17.5.1", "aud": "^1.1.4", "auto-changelog": "^2.2.1", "eslint": "^7.20.0", "functions-have-names": "^1.2.2", "has-symbols": "^1.0.1", "nyc": "^10.3.2", "safe-publish-latest": "^1.1.4", "tape": "^5.2.0"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..12.0", "opera/15.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "gitHead": "4cbc7a46fc71aa0d31277209d2adfae08cd5c415", "_id": "object-is@1.1.5", "_nodeVersion": "14.15.5", "_npmVersion": "6.14.11", "dist": {"integrity": "sha512-3cyDsyHgtmi7I7DfSSI2LDp6SK2lwvtbg0p0R1e0RvTqF5ceGx+K2dfSjm1bKDMVCFEDAQvy+o8c6a7VujOddw==", "shasum": "b9deeaa5fc7f1846a0faecdceec138e5778f53ac", "tarball": "https://registry.npmjs.org/object-is/-/object-is-1.1.5.tgz", "fileCount": 16, "unpackedSize": 23335, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgMYLlCRA9TVsSAnZWagAAsBMP/0GP/qmVI6+ugveERek5\nl+An3k0KIzNigzMdjgZfkT23afOakWKyRHRcewEwsk1fG2rCYDJHlTCdfPNx\nYM+uHH7q9l6+NER0EUTmbqb7Bqn5baK1IJCBAZkxVolPO0RKR/1RBKcbteKi\nrdmI5ReNzBZox0ikKHcZi6lSaK2I9A5qgKNnsQdxFt8RGReNPi13hjsyA4v9\nB6na47gAe4CLSSFhlWgOpbr0TOf2Q+0vBk+0CBm/hAuek4CYn9if9Dj+5wnI\nmSBxBHRxONp95Xisu7usFWUZJZluiO94ZO3dr33dMOH8EWaxGf+yt0bCn7dn\nyaO5Cr1hllE56OKiqTxYgybA3ZGGD44GTGbbXoE9Xwku59oIvmATPWjpQ6AH\nLjGCCB5qPsRbzC0XACTjyE2e1QTQfKVoDAH2tQATOLtwDaZiHYTsRPNwQ8yW\n2Jo96DcLPL/v18wlVSxnxMp7Gjnm83pfSPtQnKI4x6qALJ88AhCpg1LKyged\n2dD4rbbDfmYlpF0oB5Uqg/ehLFIyIE1NNf5Tr3Jsbfy8xX7YfiZWUL6ljXRO\nqVZjCFTfJ3Qgf1eS3pwEcDVfrE8ci2qoXdvviA+U2/CAZ/NbTiBsRVGUPQaE\n1zMS8Q/noFdGxwaWRnwgxESCWQ6iZ1pf+KDXVSR4VM76hwUZBleTUgHpmDxO\n1eT9\r\n=3x9D\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFxd1IDLkmThFEBRpxMGKP44CrgQQ2onjhxKroN0/CgeAiEA7sY515IzHTBbQoKM6WFY7F9k09zmJ9L2fZG9bQ+LAIs="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/object-is_1.1.5_1613857508773_0.9591365354579005"}, "_hasShrinkwrap": false}, "1.1.6": {"name": "object-is", "version": "1.1.6", "description": "ES2015-compliant shim for Object.is - differentiates between -0 and +0", "author": {"name": "<PERSON>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "main": "index.js", "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "aud --production", "lint": "eslint --ext=js,mjs .", "postlint": "es-shim-api --bound", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/es-shims/object-is.git"}, "bugs": {"url": "https://github.com/es-shims/object-is/issues"}, "homepage": "https://github.com/es-shims/object-is", "keywords": ["is", "Object.is", "equality", "sameValueZero", "ES6", "ES2015", "shim", "polyfill", "es-shim API"], "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1"}, "devDependencies": {"@es-shims/api": "^2.4.2", "@ljharb/eslint-config": "^21.1.0", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "functions-have-names": "^1.2.3", "has-symbols": "^1.0.3", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.7.5"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..12.0", "opera/15.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "_id": "object-is@1.1.6", "gitHead": "a4d045ace233763a3715d90c3d3af5052a290dc4", "_nodeVersion": "21.6.2", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-F8cZ+KfGlSGi09lJT7/Nd6KJZ9ygtvYC0/UYYLI9nmQKLMnydpB9yvbv9K1uSkEu7FU9vYPmVwLg328tX+ot3Q==", "shasum": "1a6a53aed2dd8f7e6775ff870bea58545956ab07", "tarball": "https://registry.npmjs.org/object-is/-/object-is-1.1.6.tgz", "fileCount": 15, "unpackedSize": 27025, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCmPmT+UxK7fowBPE6m+u+04KbkRdggRTaKDBhev/dnrwIgTBIptn5nVXPHZ5m1orREqxyqxk0nzSsP1pBJENmXO+E="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/object-is_1.1.6_1709078589491_0.9866208447188409"}, "_hasShrinkwrap": false}}, "readme": "# object-is <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\nES2015-compliant shim for Object.is - differentiates between -0 and +0, and can compare to NaN.\n\nEssentially, Object.is returns the same value as === - but true for NaN, and false for -0 and +0.\n\nThis package implements the [es-shim API](https://github.com/es-shims/api) interface. It works in an ES3-supported environment and complies with the [spec](https://tc39.es/ecma262).\n\n## Example\n\n```js\nObject.is = require('object-is');\nvar assert = require('assert');\n\nassert.ok(Object.is());\nassert.ok(Object.is(undefined));\nassert.ok(Object.is(undefined, undefined));\nassert.ok(Object.is(null, null));\nassert.ok(Object.is(true, true));\nassert.ok(Object.is(false, false));\nassert.ok(Object.is('foo', 'foo'));\n\nvar arr = [1, 2];\nassert.ok(Object.is(arr, arr));\nassert.equal(Object.is(arr, [1, 2]), false);\n\nassert.ok(Object.is(0, 0));\nassert.ok(Object.is(-0, -0));\nassert.equal(Object.is(0, -0), false);\n\nassert.ok(Object.is(NaN, NaN));\nassert.ok(Object.is(Infinity, Infinity));\nassert.ok(Object.is(-Infinity, -Infinity));\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n[package-url]: https://npmjs.com/package/object-is\n[npm-version-svg]: https://versionbadg.es/es-shims/object-is.svg\n[deps-svg]: https://david-dm.org/es-shims/object-is.svg\n[deps-url]: https://david-dm.org/es-shims/object-is\n[dev-deps-svg]: https://david-dm.org/es-shims/object-is/dev-status.svg\n[dev-deps-url]: https://david-dm.org/es-shims/object-is#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/object-is.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/object-is.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/object-is.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=object-is\n[codecov-image]: https://codecov.io/gh/es-shims/object-is/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/es-shims/object-is/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/es-shims/object-is\n[actions-url]: https://github.com/es-shims/object-is/actions\n", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "time": {"modified": "2024-02-28T00:03:10.099Z", "created": "2014-02-18T06:25:04.343Z", "0.0.0": "2014-02-18T06:25:04.343Z", "1.0.0": "2014-08-01T07:19:38.410Z", "1.0.1": "2014-08-28T09:21:04.558Z", "1.0.2": "2019-12-16T06:59:30.404Z", "1.1.0": "2020-04-14T16:08:06.579Z", "1.1.1": "2020-04-14T19:39:07.796Z", "1.1.2": "2020-04-14T19:51:20.748Z", "1.1.3": "2020-09-30T21:32:44.018Z", "1.1.4": "2020-11-27T07:31:17.453Z", "1.1.5": "2021-02-20T21:45:08.953Z", "1.1.6": "2024-02-28T00:03:09.672Z"}, "readmeFilename": "README.md", "homepage": "https://github.com/es-shims/object-is", "keywords": ["is", "Object.is", "equality", "sameValueZero", "ES6", "ES2015", "shim", "polyfill", "es-shim API"], "repository": {"type": "git", "url": "git://github.com/es-shims/object-is.git"}, "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/es-shims/object-is/issues"}, "license": "MIT", "users": {"emiljohansson": true, "flumpus-dev": true}}