{"_id": "<PERSON><PERSON><PERSON>", "_rev": "512-66c8c43105e208e065761441884d9820", "name": "<PERSON><PERSON><PERSON>", "dist-tags": {"latest": "6.0.1", "v5-legacy": "5.0.10"}, "versions": {"1.0.0": {"name": "<PERSON><PERSON><PERSON>", "version": "1.0.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "rimraf@1.0.0", "dist": {"shasum": "5797e55b587c77fc3e5b61051e429d1b00310840", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-1.0.0.tgz", "integrity": "sha512-s4qolHU38K7qfbmw1yYBUudi9iuUvpvDMf1EZeibz3dnnK1+32VfUBEJGacPyno1HHJBoA+UcpHjfmIum4NWhQ==", "signatures": [{"sig": "MEUCIQDo1CL5PxfIjiDZJaYrP+jLzK3rzwB31naEpKGvsAo8aAIgTpSc0uG6AMMKolXEo+QvWKRC/xv+r1X4eQepUvww9gw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./rimraf", "files": [""], "engines": {"node": "*"}, "scripts": {"test": "cd ./test && bash run.sh"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": "git://github.com/isaacs/rimraf.git", "_npmVersion": "0.3.0", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "v0.4.0-pre", "_defaultsLoaded": true, "_engineSupported": true}, "1.0.1": {"name": "<PERSON><PERSON><PERSON>", "version": "1.0.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "rimraf@1.0.1", "contributors": [{"url": "http://github.com/wvl", "name": "<PERSON>"}], "dist": {"shasum": "8ff61e034ccd6f5e687b3d4f4da9247c4da7dd46", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-1.0.1.tgz", "integrity": "sha512-lbtrvlGSGyxJuSAgxfzNAjRasud588KYJPupMozE+Suq9pGOg/Z1LaQ4I0G0Q427zA6W/+Lqupe+9lGdjbvpkw==", "signatures": [{"sig": "MEUCIQCIRGQDOOStw3nwJBRqbEuOAW9Fzy+GokBDF3glonK6ewIgZOtDARBeHfV9zeY3Au2Rms1+xe1VDrTIHUPqU7jcPGk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "engines": {"node": "*"}, "scripts": {"test": "cd test && bash run.sh"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "1.0.1rc7", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "v0.5.0-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "1.0.2": {"name": "<PERSON><PERSON><PERSON>", "version": "1.0.2", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/rimraf/raw/master/LICENSE", "type": "MIT"}, "_id": "rimraf@1.0.2", "contributors": [{"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/wvl", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "ritch", "email": "<EMAIL>"}], "dist": {"shasum": "4cc292a756559123ee9e4995cffb783e769b50a3", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-1.0.2.tgz", "integrity": "sha512-OoZnEcUdoIOfUwOqmQbI5X+sFzZVpJVxLNt9aA2vXal/1ghCVMCEI0IV5ZCsjsZPTTiZx24RwVMWtkD+1bH7ng==", "signatures": [{"sig": "MEQCIA+gIvW00NwC3MeNoBqgyCoecqOp4siZxboXIGlkNIxLAiAf+1SxSapN7fNFQPcUbKnyW1RgNmFYtBu/R3VIwbFsAg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "engines": {"node": "*"}, "scripts": {"test": "cd test && bash run.sh"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "1.0.6", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "v0.4.8-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "1.0.4": {"name": "<PERSON><PERSON><PERSON>", "version": "1.0.4", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/rimraf/raw/master/LICENSE", "type": "MIT"}, "_id": "rimraf@1.0.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "contributors": [{"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/wvl", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "ritch", "email": "<EMAIL>"}], "dist": {"shasum": "2137e3d9a45c547b8df9f1309b8fbca29ea20822", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-1.0.4.tgz", "integrity": "sha512-kF9OiB7SeXHX+EX5LvBudPH/92kiaNd3y7a0ZeTZo/AnHadnkV8AU+uT956pjFTt/Rs1GFfHQxhrX0EYkPIvkA==", "signatures": [{"sig": "MEUCIGTN7YQ4ynTwDUH6wVX8l0RJRsgdpmCYds7EckuKzsYJAiEAusLpm27yeBUu0H5di2dIjYirJdW8+5Uj+28Gu4e76MQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "engines": {"node": "*"}, "scripts": {"test": "cd test && bash run.sh"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "1.0.22", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "v0.4.11-pre", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/rimraf/1.0.4/package/package.json", "wscript": false, "serverjs": false, "contributors": ["<PERSON> <<EMAIL>> (http://blog.izs.me)", "<PERSON> <<EMAIL>> (http://github.com/wvl)", "ritch <<EMAIL>>"]}, "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "1.0.5": {"name": "<PERSON><PERSON><PERSON>", "version": "1.0.5", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/rimraf/raw/master/LICENSE", "type": "MIT"}, "_id": "rimraf@1.0.5", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "contributors": [{"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/wvl", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "ritch", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "2c988219578bc569e461b9202bc22f5dbfa5b3e3", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-1.0.5.tgz", "integrity": "sha512-y5A+HvFwFx8LlUnjCHeD6ciPwj7OcAmZiOsxq6mcQNmVjm5HCCb9W7oG2qGkY+/ThqAULRRMhnrbYnl84y84oA==", "signatures": [{"sig": "MEUCIA5XtGI5rc8gW17SesgOjP440Q17NyjGqqCXRB7si5kEAiEAvmOAE6pc3aDZzzlv5ZJMMwBdRrbkb1fTiRMPLwUamfA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "engines": {"node": "*"}, "scripts": {"test": "cd test && bash run.sh"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "1.0.27", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "v0.4.11", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/rimraf/1.0.5/package/package.json", "wscript": false, "serverjs": false, "contributors": ["<PERSON> <<EMAIL>> (http://blog.izs.me)", "<PERSON> <<EMAIL>> (http://github.com/wvl)", "ritch <<EMAIL>>", "<PERSON>"]}, "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "1.0.6": {"name": "<PERSON><PERSON><PERSON>", "version": "1.0.6", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/rimraf/raw/master/LICENSE", "type": "MIT"}, "_id": "rimraf@1.0.6", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "contributors": [{"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/wvl", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "ritch", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "8e404afc4edc5ac544dce5441a148e52657e5860", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-1.0.6.tgz", "integrity": "sha512-CweTP29lrb7vcTH49HhMXOnyOnhskv6E43Mw/MJvkdp5G4qAmZxnH+hzxFUHG8OE94mQb3BjKuVKDMxzC7hFCA==", "signatures": [{"sig": "MEQCIGXRIhs6AtEdf5yP7tJT2GEZELX0KuJrMW/SiSkfR7AZAiBDQ1W26TdFdWChS0w+9TVmgJnIgoxBPWK2tZYkxm7jmQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "engines": {"node": "*"}, "scripts": {"test": "cd test && bash run.sh"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "1.0.27", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "v0.4.11", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/rimraf/1.0.6/package/package.json", "wscript": false, "serverjs": false, "contributors": ["<PERSON> <<EMAIL>> (http://blog.izs.me)", "<PERSON> <<EMAIL>> (http://github.com/wvl)", "ritch <<EMAIL>>", "<PERSON>"]}, "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "1.0.7": {"name": "<PERSON><PERSON><PERSON>", "version": "1.0.7", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/rimraf/raw/master/LICENSE", "type": "MIT"}, "_id": "rimraf@1.0.7", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "contributors": [{"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/wvl", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "ritch", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "9b664339fb366bf669d79672718336eba8d6adb4", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-1.0.7.tgz", "integrity": "sha512-DwXzKPjqt2nrjSv259EYbnAArs4fxuRI6fO4hHRdG4pU7riq/I+nyxWoiNxXxCI07ri424ja1hrmPxYREWHCgQ==", "signatures": [{"sig": "MEUCIQDl/mMdT+KJzDHt2yKbXVnbE8LemcL/xEha1tPVl562GQIgCzbFoNQ/gmZ55aML2DwvJiziixqB8xtcGLfKfas3CfE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "engines": {"node": "*"}, "scripts": {"test": "cd test && bash run.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "1.0.30", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "v0.5.8-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "1.0.8": {"name": "<PERSON><PERSON><PERSON>", "version": "1.0.8", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/rimraf/raw/master/LICENSE", "type": "MIT"}, "_id": "rimraf@1.0.8", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "contributors": [{"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/wvl", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "ritch", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "d8808068156c5135b16842348304b92c86f23bd1", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-1.0.8.tgz", "integrity": "sha512-HSJWc4z6h8JH1V8pIpfPYA++fhc3nIs23jVHumnQ4D080E73YeLaJRTe/bMuH8/ONCimYGfmgHD+ehKGn/InCQ==", "signatures": [{"sig": "MEQCIDrN8ibSbt5Vue2PHmiC1j67MK2m48mMw8UqZBhG0z9vAiBLxRVTWrEv958m2F2chhBwXK1zwDXlrp2oA+KGUwN0yg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "engines": {"node": "*"}, "scripts": {"test": "cd test && bash run.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "1.0.94", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "1.0.9": {"name": "<PERSON><PERSON><PERSON>", "version": "1.0.9", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/rimraf/raw/master/LICENSE", "type": "MIT"}, "_id": "rimraf@1.0.9", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "contributors": [{"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/wvl", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "ritch", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dist": {"shasum": "be4801ff76c2ba6f1c50c78e9700eb1d21f239f1", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-1.0.9.tgz", "integrity": "sha512-vMboELGrBSgcKZ1fVCCfLEvRdhIeOhDAK4XhjuKXFFs/TOkvWxjT9RRHw2kDhM139zfU3wUQbJkRanMXLl7zFA==", "signatures": [{"sig": "MEUCIEPL7B9HzvpW2X4fVPi5h23xPaijuc5eUzmYaaA7FjO3AiEAvKc0hLi/kS5phUsAZfviERThIWyI4BRRFX6D6ix5src=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "engines": {"node": "*"}, "scripts": {"test": "cd test && bash run.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "1.1.0-alpha-6", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "v0.6.4", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "2.0.0": {"name": "<PERSON><PERSON><PERSON>", "version": "2.0.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/rimraf/raw/master/LICENSE", "type": "MIT"}, "_id": "rimraf@2.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "contributors": [{"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/wvl", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "ritch", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "61bafdc9d6b385e8de0101b4b47c3d01b856c1ee", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.0.0.tgz", "integrity": "sha512-ZBMvl3zW9cEVtEwA3Tqy7ALi0vL6+L8tr1asd3/b5qAZpaFdzgsB8U77itPuzF21f2QS2Ke3ChFo3ecdiMTnTg==", "signatures": [{"sig": "MEYCIQCyHQjWD0JEHSw5nNHf1KfP8dC7h+r+uPSceLDcnenCnAIhANQat/guHx73qUWeqTldXpdHJ7JAxIyXCOlLM0OLCE+4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "engines": {"node": "*"}, "scripts": {"test": "cd test && bash run.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "1.1.0-3", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "v0.6.9-pre", "dependencies": {"graceful-fs": "~1.1"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {"graceful-fs": "~1.1"}}, "2.0.1": {"name": "<PERSON><PERSON><PERSON>", "version": "2.0.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/rimraf/raw/master/LICENSE", "type": "MIT"}, "_id": "rimraf@2.0.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "contributors": [{"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/wvl", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "ritch", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "6e58b6e7d3980bd620618d0703d502f872078fee", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.0.1.tgz", "integrity": "sha512-nd0X/lwiFfQklOUOZYtdHoZrKmJ8Ad5o0LtxSnBu9/ygv3n/y7qCH2hQdk9Rlby2+BRgTq200jtFDj04ul4y0g==", "signatures": [{"sig": "MEQCICxYXIrY3RX3owvYPnuw2+0DjIEZc0pKkASbMfBELluPAiAxxKOy1ef9A7EaLL923fvIKH61L3LF8qdp6JX3cYHhGg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "engines": {"node": "*"}, "scripts": {"test": "cd test && bash run.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "1.1.0-3", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "v0.6.9-pre", "dependencies": {"graceful-fs": "~1.1"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {"graceful-fs": "~1.1"}}, "2.0.2": {"name": "<PERSON><PERSON><PERSON>", "version": "2.0.2", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/rimraf/raw/master/LICENSE", "type": "MIT"}, "_id": "rimraf@2.0.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "contributors": [{"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/wvl", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "ritch", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "2a860f3f74bd7975002f73cb0b003b218ec351e4", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.0.2.tgz", "integrity": "sha512-STJIDawfEaUr9d9q5owCIv8wG3zHx9Xt9UpC/V2gkN88j0UKRm+XhmOQh1mG5P3O2g5WnqyUHvVzRADzGJoCKg==", "signatures": [{"sig": "MEUCIHUooPnpIHIP2upKhTrDNtp2n9YRTRDVzHVlasjM+jVtAiEAyHjoZLaPBEbekuOdT7qzMixMNtTrXA8io/78xjcUi28=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "engines": {"node": "*"}, "scripts": {"test": "cd test && bash run.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "1.1.24", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "v0.7.10-pre", "dependencies": {"graceful-fs": "~1.1"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {"graceful-fs": "~1.1"}}, "2.0.3": {"name": "<PERSON><PERSON><PERSON>", "version": "2.0.3", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/rimraf/raw/master/LICENSE", "type": "MIT"}, "_id": "rimraf@2.0.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "contributors": [{"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/wvl", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "ritch", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "f50a2965e7144e9afd998982f15df706730f56a9", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.0.3.tgz", "integrity": "sha512-uR09PSoW2+1hW0hquRqxb+Ae2h6R5ls3OAy2oNekQFtqbSJkltkhKRa+OhZKoxWsN9195Gp1vg7sELDRoJ8a3w==", "signatures": [{"sig": "MEYCIQCbqh+PZnlKsxNt49BK5qdYsiAlAEB+wZEpU/kENAzGogIhAORbvUC2aumyqKm32b/IuWBKQ0vgJnyUW6qgX61BYsjc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "scripts": {"test": "cd test && bash run.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "1.1.69", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "dependencies": {"graceful-fs": "~1.1"}, "optionalDependencies": {"graceful-fs": "~1.1"}}, "2.1.0": {"name": "<PERSON><PERSON><PERSON>", "version": "2.1.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/rimraf/raw/master/LICENSE", "type": "MIT"}, "_id": "rimraf@2.1.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "contributors": [{"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/wvl", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "ritch", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "6003214d56590b613791b457b4df6fb3170dc73b", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.1.0.tgz", "integrity": "sha512-tw7TH5FKBfDBkm8A9vXyJ8s95d6cgVVoRtmWn5engS8Bv1nIzNIBazBL1pXrkdVjVomS+sH6mIzB6xDYeD+IFg==", "signatures": [{"sig": "MEUCIQCkkr6iMNhR8p/Clw2DIin1+RJzbC3HXAhVxCf+3p3qfwIgLQt2k/TA8sysmuvodu+3El1GwDs3okvV8/hLZsxG19s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "scripts": {"test": "cd test && bash run.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "1.1.70", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "dependencies": {"graceful-fs": "~1.1"}, "optionalDependencies": {"graceful-fs": "~1.1"}}, "2.1.1": {"name": "<PERSON><PERSON><PERSON>", "version": "2.1.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/rimraf/raw/master/LICENSE", "type": "MIT"}, "_id": "rimraf@2.1.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "contributors": [{"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/wvl", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "ritch", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "ee9cec7e2d796ef59ceaa5f3a3024c225e630c61", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.1.1.tgz", "integrity": "sha512-npW97zhKZozX+RF4usKfVf7+Qs/kyEwiEm8M7aonNMse7+x8TCQkemTBen6Qgkb+K42JZyPYocYcvqkoBqVoUQ==", "signatures": [{"sig": "MEYCIQDjhWPf76K+hteLOYtM3TFqrzPMJAVBv3DOko9WmUn4owIhALiVXbprTELggkKDwaMVTTS85fae9FDlwjbHOvmY5Yx5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "scripts": {"test": "cd test && bash run.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "1.1.70", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "dependencies": {"graceful-fs": "~1.1"}, "optionalDependencies": {"graceful-fs": "~1.1"}}, "2.1.2": {"name": "<PERSON><PERSON><PERSON>", "version": "2.1.2", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/rimraf/raw/master/LICENSE", "type": "MIT"}, "_id": "rimraf@2.1.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "contributors": [{"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/wvl", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "ritch", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "82b9bedb9f88c39c43a20b01f209551b34a2ab76", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.1.2.tgz", "integrity": "sha512-3VtCju6CD6DCp7GxaTAkJtw6BRwT/azvZmjyug2Zx1r5ZVAiFD+1XjVtWrEQtrqLRvhZFYTx3Bu6Gx7NCNfLSQ==", "signatures": [{"sig": "MEUCIHsos4W/LvdZVdiMkLXkxAboXBdphhzex/CIAyHbGteXAiEAuMOBj5MetznMvn0jh61dCAWY0rv/f/qowE1JRCeCKkY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "_from": ".", "scripts": {"test": "cd test && bash run.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "1.2.2", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "dependencies": {"graceful-fs": "~1.1"}, "optionalDependencies": {"graceful-fs": "~1.1"}}, "2.1.3": {"name": "<PERSON><PERSON><PERSON>", "version": "2.1.3", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/rimraf/raw/master/LICENSE", "type": "MIT"}, "_id": "rim<PERSON>f@2.1.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "contributors": [{"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/wvl", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "ritch", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "23f9915e1935e2548fee061ae982316006f66322", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.1.3.tgz", "integrity": "sha512-tAbkfld7gejflgub7ilpsAJXC5jxrA2ljLSE7gF34s23MjCtrhbyJaefJLhZGBM0uT/9IWEwyVC9z7s2W5iS3w==", "signatures": [{"sig": "MEUCICafoeMD4TMujjuOIeGLy6xuKhbtsMZ6sTvNKyolDcKBAiEAoNKPAyE1OaFokpzrndVys0rjJ9gnaQc3bZHpC/fpiC4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "_from": ".", "scripts": {"test": "cd test && bash run.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "1.2.4", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "dependencies": {"graceful-fs": "~1"}, "optionalDependencies": {"graceful-fs": "~1"}}, "2.1.4": {"name": "<PERSON><PERSON><PERSON>", "version": "2.1.4", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/rimraf/raw/master/LICENSE", "type": "MIT"}, "_id": "rimraf@2.1.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "contributors": [{"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/wvl", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "ritch", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "5a6eb62eeda068f51ede50f29b3e5cd22f3d9bb2", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.1.4.tgz", "integrity": "sha512-tzwmX16YQhcFu0T/m0gHBcFKx6yQAg77Z6WWaQSJsUekXYa6yaAmHhrDdmFicgauX/er7GsdN+vRao3mBhA4kQ==", "signatures": [{"sig": "MEUCIQDaMfSjl0LusbMUgftOC2P3uMN/TXy1GliHJVyDO1nG3gIgcP7Iinrg+MFDxJjAmw2cWck2+X+0WApmhdP/8bcBaFI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "_from": ".", "scripts": {"test": "cd test && bash run.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "1.2.7", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "dependencies": {"graceful-fs": "~1"}, "optionalDependencies": {"graceful-fs": "~1"}}, "2.2.0": {"name": "<PERSON><PERSON><PERSON>", "version": "2.2.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/rimraf/raw/master/LICENSE", "type": "MIT"}, "_id": "rimraf@2.2.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "contributors": [{"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/wvl", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "ritch", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "./bin.js"}, "dist": {"shasum": "70797542e81a935849443d7b69bec612ca74c96e", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.2.0.tgz", "integrity": "sha512-5EkbSwHW19vurr0ChzriACz6Nf8f/uQhTLoUZb+mcILmGhrzjjA48ls9txwJPfDiUKGROpA8jLwJR6BczYIPZQ==", "signatures": [{"sig": "MEYCIQCV21OFa0Ndo+qWz/AH7D3l4r0+MmcHJBdEG/xOzy17SgIhAJk6zjTkLtMNYLNWHCSDcFLGnyc8b4yAD9iggtP2KRLL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "_from": ".", "scripts": {"test": "cd test && bash run.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "1.3.0", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "dependencies": {"graceful-fs": "~1"}, "optionalDependencies": {"graceful-fs": "~1"}}, "2.2.1": {"name": "<PERSON><PERSON><PERSON>", "version": "2.2.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/rimraf/raw/master/LICENSE", "type": "MIT"}, "_id": "rimraf@2.2.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "contributors": [{"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/wvl", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "ritch", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "./bin.js"}, "dist": {"shasum": "8f49c57874ce166b667fbfecac2cdf7f26d3d8a5", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.2.1.tgz", "integrity": "sha512-0l9USMpITPF5FWxhuKb+caJ2IuvxEFj+99QMZSwaxJDpbTFixI6UPJIxdl7x6clTQ3EgmIPAdZMBoaWkW+kIQg==", "signatures": [{"sig": "MEUCIFdXoWhhFdrmdwtxHxldf+wm07bh+noH6euNVqZ8K+LtAiEAv05tkd6nD/5VcadlTAGiBgdbdXrHwUrNOcpL3+tSIhw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "_from": ".", "scripts": {"test": "cd test && bash run.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "1.3.4", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "dependencies": {"graceful-fs": "~1"}, "optionalDependencies": {"graceful-fs": "~1"}}, "2.2.2": {"name": "<PERSON><PERSON><PERSON>", "version": "2.2.2", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/rimraf/raw/master/LICENSE", "type": "MIT"}, "_id": "rimraf@2.2.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "contributors": [{"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/wvl", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "ritch", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "./bin.js"}, "dist": {"shasum": "d99ec41dc646e55bf7a7a44a255c28bef33a8abf", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.2.2.tgz", "integrity": "sha512-HCQfBUbhPWPJjX9P1ZbibfMlWpu0mrnneK6Dl71b74xC7Kq/C0vsX+5Ntn8s8bsR1eSIWI4+qgx5/kqlLq/ZGg==", "signatures": [{"sig": "MEYCIQDfb7qJwtS83UVZfiGcgtGf8IU2x60JNWXPzjmzRMiBsQIhALEstSBQWz77loMdhZRxzrG0kOpCWAcrtpVZW5k44EA5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "_from": ".", "scripts": {"test": "cd test && bash run.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "1.3.4", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "dependencies": {"graceful-fs": "~2"}, "optionalDependencies": {"graceful-fs": "~2"}}, "2.2.3": {"name": "<PERSON><PERSON><PERSON>", "version": "2.2.3", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/rimraf/raw/master/LICENSE", "type": "MIT"}, "_id": "rim<PERSON>f@2.2.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "contributors": [{"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/wvl", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "ritch", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "./bin.js"}, "dist": {"shasum": "ed60a43d480a1d576d0715aadf36181ffe3186c6", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.2.3.tgz", "integrity": "sha512-SodhmjP0tOH3g4jglhI3a51mhST3QpDKtrGTwWo2wdhhuTkdsY+ctIS6KyqhvN0BxQWv819Vj3Ys9lgRP7eV0A==", "signatures": [{"sig": "MEYCIQDbSUVO3rqHoBLWT5H7PjOS32W6WyoSDwCa7hMb8bY11QIhAIuDF2GJHeQA5iHQ/JZvk3nkBqjgjJZ734y/vzVzetcg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "_from": ".", "scripts": {"test": "cd test && bash run.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "1.3.15", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "dependencies": {"graceful-fs": "~2"}, "optionalDependencies": {"graceful-fs": "~2"}}, "2.2.4": {"name": "<PERSON><PERSON><PERSON>", "version": "2.2.4", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/rimraf/raw/master/LICENSE", "type": "MIT"}, "_id": "rimraf@2.2.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "contributors": [{"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/wvl", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "ritch", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "./bin.js"}, "dist": {"shasum": "9f0b530f829f7b655a6e4d2fd8cf4c042336ef58", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.2.4.tgz", "integrity": "sha512-bDNQykgzqDZhTqGtn7NIAh4cNlALgr/4byGFCkLHnfeS5nes77mo0ofB/PAXBbzif7hSvmNKw3Wn9PiQW8S8BQ==", "signatures": [{"sig": "MEYCIQDrlxve/LGIVUSZNCZFcoN4d14WgkU5JHChdpUubp8+pwIhAMbPKTd1Fsd03dxhGogokxV3ZkFBDX8KiGGQMyg3xcLW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "_from": ".", "scripts": {"test": "cd test && bash run.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "1.3.15", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "dependencies": {"graceful-fs": "~2"}, "optionalDependencies": {"graceful-fs": "~2"}}, "2.2.5": {"name": "<PERSON><PERSON><PERSON>", "version": "2.2.5", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/rimraf/raw/master/LICENSE", "type": "MIT"}, "_id": "rim<PERSON>f@2.2.5", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "contributors": [{"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/wvl", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "ritch", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "./bin.js"}, "dist": {"shasum": "4e5c4f667b121afa806d0c5b58920996f9478aa0", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.2.5.tgz", "integrity": "sha512-+21guJY/c6DCpgGmoLeNesNQGNpSPx+rpaaoTPqsNESxQjAkekmqICQYRl4Kp1UJnIQIYFJeoSWB/JvPaRS77w==", "signatures": [{"sig": "MEQCIErT77ZqZNepR9AQCNi4qFMoe64bAvCE6G4wk4q/BWc0AiB+anRfSfaNSXF8qQo4RMag4pbhCsBMQt+t7Mlovjx2/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "_from": ".", "scripts": {"test": "cd test && bash run.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "1.3.15", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}}, "2.2.6": {"name": "<PERSON><PERSON><PERSON>", "version": "2.2.6", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/rimraf/raw/master/LICENSE", "type": "MIT"}, "_id": "rim<PERSON>f@2.2.6", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "contributors": [{"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/wvl", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "ritch", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "./bin.js"}, "dist": {"shasum": "c59597569b14d956ad29cacc42bdddf5f0ea4f4c", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.2.6.tgz", "integrity": "sha512-33Fa/MIw/3F9KcDE/uJ2OuYUyxY+fkmw1c20DFnyhP7dfo2+BexeE1thjluPiJaG8sW6CcaqnTffwpRd4NAiTg==", "signatures": [{"sig": "MEUCIAQXC5DchnPcKBpHfLZjvhz8GS1d1/K5u86rwj6ZiNnvAiEAtwOUOrCWx8TDLmMkfuZ+fX/XpKVa7bVNP1AxI2gWM/E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "_from": ".", "scripts": {"test": "cd test && bash run.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "1.3.23", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}}, "2.2.8": {"name": "<PERSON><PERSON><PERSON>", "version": "2.2.8", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/rimraf/raw/master/LICENSE", "type": "MIT"}, "_id": "rimraf@2.2.8", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "contributors": [{"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/wvl", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "ritch", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "./bin.js"}, "dist": {"shasum": "e439be2aaee327321952730f99a8929e4fc50582", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.2.8.tgz", "integrity": "sha512-R5KMKHnPAQaZMqLOsyuyUmcIjSeDm+73eoqQpaXA7AZ22BL+6C+1mcUscgOsNd8WVlJuvlgAPsegcx7pjlV0Dg==", "signatures": [{"sig": "MEUCIQCZn+d6b/v65Tet2juQy/F6GBqkspblzmBVQllkaiCiZQIgcuamkK3MEg3DYshlOHQ3epJfKnpAUOmBx3ETRfdr6DI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "_from": ".", "_shasum": "e439be2aaee327321952730f99a8929e4fc50582", "scripts": {"test": "cd test && bash run.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "1.4.10", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}}, "2.3.0": {"name": "<PERSON><PERSON><PERSON>", "version": "2.3.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/rimraf/raw/master/LICENSE", "type": "MIT"}, "_id": "rimraf@2.3.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "./bin.js"}, "dist": {"shasum": "a5f0f3c702e39939be3ecbd1f7098f99750b5ed8", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.3.0.tgz", "integrity": "sha512-/qdtvZmKablIMX+Qm0EImTqIpVmKJWFZ2Ry6HUq+V0p/eqYLEBuW/tU+oW33uRITCQQKl7tsHTiRa+wcRuqWQg==", "signatures": [{"sig": "MEUCIC/GV/2P1UZo+a4i4iYDv93CUz9WY3rjqcJaBNbWm3l6AiEA/sVQKuyctR14240JsdnU0pwkYOvKAcgNj0C1eZAMrD8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "_from": ".", "files": ["bin.js", "rimraf.js", "LICENSE", "README.md"], "_shasum": "a5f0f3c702e39939be3ecbd1f7098f99750b5ed8", "gitHead": "e4e9dcbf64d55b8ced01a914e08827715089190a", "scripts": {"test": "cd test && bash run.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "2.6.0", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "1.4.2", "dependencies": {"glob": "^4.4.1"}}, "2.3.1": {"name": "<PERSON><PERSON><PERSON>", "version": "2.3.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/rimraf/raw/master/LICENSE", "type": "MIT"}, "_id": "rimraf@2.3.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "./bin.js"}, "dist": {"shasum": "f83df78c168d5daf9f021e8e092e7a165898ee75", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.3.1.tgz", "integrity": "sha512-h92xWC4XD6ZtmVZt4dNmwAs6kLYKlrq0hncVXIqB8bA4XIZZoxhgZMihFplELQWnp7jDezD42T+QcnCKRGpAdA==", "signatures": [{"sig": "MEUCIBXphf9pIh5r+neyEqBFKqldtYNVmTu7FGSZt8IXFFWTAiEAlm4dHYv10zThzcPoCJIPxfOx1uegaVSVq445MbFx3hg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "_from": ".", "files": ["bin.js", "rimraf.js", "LICENSE", "README.md"], "_shasum": "f83df78c168d5daf9f021e8e092e7a165898ee75", "gitHead": "aa707db2fb5b11c35fc614a1472775373dc9d46c", "scripts": {"test": "cd test && bash run.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "2.6.0", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "1.4.2", "dependencies": {"glob": "^4.4.2"}}, "2.3.2": {"name": "<PERSON><PERSON><PERSON>", "version": "2.3.2", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/rimraf/raw/master/LICENSE", "type": "MIT"}, "_id": "rim<PERSON>f@2.3.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "./bin.js"}, "dist": {"shasum": "7304bd9275c401b89103b106b3531c1ef0c02fe9", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.3.2.tgz", "integrity": "sha512-oAWTkrnur7uVIEJvBXgAdGptlx3Sq+KgPwU1HOjEQuzO+k53SuQH61j93nf0xrvXr6umbBMMn+ghJBrPekw/dg==", "signatures": [{"sig": "MEUCIGBMf7MQZ2mwVCuhfW+TA0UT9uiuKkfRgWDFN9aShZeiAiEA+2JOnvzzP6ek3rzZwmVSbxrypgVxTx/oE4R8qeM4eX0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "_from": ".", "files": ["bin.js", "rimraf.js", "LICENSE", "README.md"], "_shasum": "7304bd9275c401b89103b106b3531c1ef0c02fe9", "gitHead": "9d5ab4a8b6986ec909af04f6d91315e98f5893e8", "scripts": {"test": "cd test && bash run.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "2.7.0", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "1.4.2", "dependencies": {"glob": "^4.4.2"}}, "2.3.3": {"name": "<PERSON><PERSON><PERSON>", "version": "2.3.3", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": {"url": "https://github.com/isaacs/rimraf/raw/master/LICENSE", "type": "MIT"}, "_id": "rim<PERSON>f@2.3.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "./bin.js"}, "dist": {"shasum": "d0073d8b3010611e8f3ad377b08e9a3c18b98f06", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.3.3.tgz", "integrity": "sha512-+xmgLLwwh8ic63qKREyuoS69Mk/KERGdOrCpFoymsSObMIjPi1wmfWooeyparCRS7qCk1cezuFgZmHSmP8C3SA==", "signatures": [{"sig": "MEQCIEL4X1rLm08Uw0qKS6e3y3yOdS6/7TcB3FU47KFn7vmrAiBa6K+8ZYNEiQp4NClwVm2RvHnLk0s8b8xKkzuVT7DeMw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "_from": ".", "files": ["bin.js", "rimraf.js", "LICENSE", "README.md"], "_shasum": "d0073d8b3010611e8f3ad377b08e9a3c18b98f06", "gitHead": "ad4efe8102a72c77bf2b13165ecc2229a9a68955", "scripts": {"test": "cd test && bash run.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "2.7.6", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "1.7.1", "dependencies": {"glob": "^4.4.2"}}, "2.3.4": {"name": "<PERSON><PERSON><PERSON>", "version": "2.3.4", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rim<PERSON>f@2.3.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "./bin.js"}, "dist": {"shasum": "82d9bc1b2fcf31e205ac7b28138a025d08e9159a", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.3.4.tgz", "integrity": "sha512-+LXZSA3Jh1rwsClNO72krtN5NHhgPH0JaB6MxFHyImAI2QdfpquuO/vx/FAZNs8fcql1d5F+j9rQr4larQ871w==", "signatures": [{"sig": "MEUCIQD4TU1r8pp/nqOl5D8G1sBsKThyKVCiqLdsjiCPy6VqMwIgUDyHgFije8walLu+yEB00zIwmppl/lSAuPo0z0SmgfA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "_from": ".", "files": ["bin.js", "rimraf.js", "LICENSE", "README.md"], "_shasum": "82d9bc1b2fcf31e205ac7b28138a025d08e9159a", "gitHead": "2a6cc45bbbdd8da5fd703d8af62843565f7dea57", "scripts": {"test": "cd test && bash run.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "2.10.0", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "2.0.1", "dependencies": {"glob": "^4.4.2"}}, "2.4.0": {"name": "<PERSON><PERSON><PERSON>", "version": "2.4.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@2.4.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "./bin.js"}, "dist": {"shasum": "40ba0416037d8511ecb50f6b07cf8d18e658a864", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.4.0.tgz", "integrity": "sha512-h6OsUVOxvngnU7AKA+aEeu3Vy6VolozHNzkUSzx7lI5eYmtVi674fEScu5CkfPyLoaIct93SDHqKX3C/uRoQ1A==", "signatures": [{"sig": "MEQCIAtXEbjkTBb1izvTQVjmJeIHevC74HQH12E77MRv+NIbAiBdT4dU7XZl3YXW/ot3rewhNZWaL9oqgLhhOk23pWDjUA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "_from": ".", "files": ["bin.js", "rimraf.js", "LICENSE", "README.md"], "_shasum": "40ba0416037d8511ecb50f6b07cf8d18e658a864", "gitHead": "2128f0b9ade05a81394dd21a383fa309dd9eca94", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "2.0.1", "dependencies": {"glob": "^4.4.2"}, "devDependencies": {"tap": "^1.2.0", "mkdirp": "^0.5.1"}}, "2.4.1": {"name": "<PERSON><PERSON><PERSON>", "version": "2.4.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@2.4.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "./bin.js"}, "dist": {"shasum": "99ff3cc1d61d687b67489b9f97cfa9d3db3b9e48", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.4.1.tgz", "integrity": "sha512-19l55XrKoDsdQPZiJIQ4PHnFfhp0++x+fLAkL+uOkyinYqJDsiK1wmxUKLJuRNjKnKYl+AnKZcHmZJlB7dDoQQ==", "signatures": [{"sig": "MEUCIGS2x3WOWHXh+ILQZZPmlTaozAZyL7i/owTbxxogwkhsAiEAvMWSG5nwUpHNX59kkvS2X9yhtjEfpkVQ6rNVdhj4XFs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "_from": ".", "files": ["bin.js", "rimraf.js", "LICENSE", "README.md"], "_shasum": "99ff3cc1d61d687b67489b9f97cfa9d3db3b9e48", "gitHead": "9b97ac62e1b459d84dbe18f20757bfe4374e65ab", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "3.0.0", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "2.2.1", "dependencies": {"glob": "^4.4.2"}, "devDependencies": {"tap": "^1.2.0", "mkdirp": "^0.5.1"}}, "2.4.2": {"name": "<PERSON><PERSON><PERSON>", "version": "2.4.2", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@2.4.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "./bin.js"}, "dist": {"shasum": "ab4f39b08b72eae07c3d9fe9f4831aebfc9f431d", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.4.2.tgz", "integrity": "sha512-9207HF6Pb1ZrJfikR13IGbEUzxoN+ulMmTSSa/xQ9aLkB+L3ARZpKajjca/bmaVXkj+Q8dM0QEPoa1ZquNeteA==", "signatures": [{"sig": "MEUCIQCkMgNYpZ/XksaR5zSWb8gT57rh7v6RsLQkCfUJ1S8BCAIgUIwm4jPxqHlKn3D5iE/1Z6yte2iI3gDQyuiB06JHHZU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "_from": ".", "files": ["LICENSE", "README.md", "bin.js", "rimraf.js"], "_shasum": "ab4f39b08b72eae07c3d9fe9f4831aebfc9f431d", "gitHead": "4359e9d3b3c0f26e6abe3139a00b93337f1689d7", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "3.1.0", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "2.2.1", "dependencies": {"glob": "^5.0.14"}, "devDependencies": {"tap": "^1.3.1", "mkdirp": "^0.5.1"}}, "2.4.3": {"name": "<PERSON><PERSON><PERSON>", "version": "2.4.3", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rim<PERSON>f@2.4.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "./bin.js"}, "dist": {"shasum": "e5b51c9437a4c582adb955e9f28cf8d945e272af", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.4.3.tgz", "integrity": "sha512-ktqRS+MK3Jh3BWfGoUK0fVGmfi5CV7M0y2FbzafR46hk00ZCC9OzQBJAeT+oExA3CUCOyGZ39LXKIBa3a75UeA==", "signatures": [{"sig": "MEQCIF5Fjg+j54ArdONTRQV8QTdph3aAGyraZbMUq5BIyTUdAiBWVgcSOIbfBr4Ca/AvOT35FYnH5APQiQFHQFOrNuTY3A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "_from": ".", "files": ["LICENSE", "README.md", "bin.js", "rimraf.js"], "_shasum": "e5b51c9437a4c582adb955e9f28cf8d945e272af", "gitHead": "ec7050f8ca14c931b847414f18466e601ca7c02e", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "3.2.2", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "2.2.1", "dependencies": {"glob": "^5.0.14"}, "devDependencies": {"tap": "^1.3.1", "mkdirp": "^0.5.1"}}, "2.4.4": {"name": "<PERSON><PERSON><PERSON>", "version": "2.4.4", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@2.4.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "./bin.js"}, "dist": {"shasum": "b528ce2ebe0e6d89fb03b265de11d61da0dbcf82", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.4.4.tgz", "integrity": "sha512-vM55sE6DOvNEHSEIQ7uCej02DKQYcGdnl1ToNfGy9t2jNrfNYcP5OwSkdeDXEpMtDqHEEFOORhvP0jA3/kBk1w==", "signatures": [{"sig": "MEYCIQCDG/cflGOykxX1vTtDLte0stbgvHIcEjHKCxlTc9WbqAIhAM5nzhxcmKKse1LCUue9v1aBAcTRNfOeow1a2buTyu14", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "_from": ".", "files": ["LICENSE", "README.md", "bin.js", "rimraf.js"], "_shasum": "b528ce2ebe0e6d89fb03b265de11d61da0dbcf82", "gitHead": "62ae8a4037e7190691eeab35265aed1c768d23e3", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "3.3.2", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "4.0.0", "dependencies": {"glob": "^5.0.14"}, "devDependencies": {"tap": "^1.3.1", "mkdirp": "^0.5.1"}}, "2.4.5": {"name": "<PERSON><PERSON><PERSON>", "version": "2.4.5", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@2.4.5", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "./bin.js"}, "dist": {"shasum": "ee710ce5d93a8fdb856fb5ea8ff0e2d75934b2da", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.4.5.tgz", "integrity": "sha512-J5xnxTyqaiw06JjMftq7L9ouA448dw/E7dKghkP9WpKNuwmARNNg+Gk8/u5ryb9N/Yo2+z3MCwuqFK/+qPOPfQ==", "signatures": [{"sig": "MEUCIDxQ+olKHptFkOZURp/DVR9vuI9K+x8kjwN8+t08U8ZiAiEAz7soueVAVbM8G9yuvFr3q4FLNySlZlU+S5bJICXb/5I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "_from": ".", "files": ["LICENSE", "README.md", "bin.js", "rimraf.js"], "_shasum": "ee710ce5d93a8fdb856fb5ea8ff0e2d75934b2da", "gitHead": "02c4ecd2a7a1895ada0a244ea04e6f0996f6ba61", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "3.3.2", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "4.0.0", "dependencies": {"glob": "^6.0.1"}, "devDependencies": {"tap": "^2.3.4", "mkdirp": "^0.5.1"}}, "2.5.0": {"name": "<PERSON><PERSON><PERSON>", "version": "2.5.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@2.5.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "./bin.js"}, "dist": {"shasum": "30c096cdf772e26bf3e1d2cff84c2196541a9bb6", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.5.0.tgz", "integrity": "sha512-6vb5xVCu/fcP+IyejDFivO086PiiEj9xOL1ejOIfz3ga1aE8pQGLihutPgaAX+wcGEC21RLwDUDjkZJvBQBQWQ==", "signatures": [{"sig": "MEQCICNYrtI3AVqAWlKj4u1sagHQIK0Geum9rPTRrWzFghqHAiAM0IhcXGHhswcxgSW3Phti5Y1yqsCHTLDoPROnUknIXw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "_from": ".", "files": ["LICENSE", "README.md", "bin.js", "rimraf.js"], "_shasum": "30c096cdf772e26bf3e1d2cff84c2196541a9bb6", "gitHead": "c42412ca7a4c9fcf1a219bdfef2283bd7dcbc1d2", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "3.3.2", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "4.0.0", "dependencies": {"glob": "^6.0.1"}, "devDependencies": {"tap": "^2.3.4", "mkdirp": "^0.5.1"}}, "2.5.1": {"name": "<PERSON><PERSON><PERSON>", "version": "2.5.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@2.5.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "./bin.js"}, "dist": {"shasum": "52e1e946f3f9b9b0d5d8988ba3191aaf2a2dbd43", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.5.1.tgz", "integrity": "sha512-CNymZDrSR9PfkqZnBWaIki7Wlba4c7GzSkSKsHHvjXswXmJA1hM8ZHFrNWIt4L/WcR9kOwvsJZpbxV4fygtXag==", "signatures": [{"sig": "MEUCIQDHG8PEw5P5pM95wAU1B4naFU5hRjBMX+GeaLKvWSF+WwIgKkadNxMEycruAvo7ZjpNaNuNTJyAt3r49xm/Uuwi6Ds=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "_from": ".", "files": ["LICENSE", "README.md", "bin.js", "rimraf.js"], "_shasum": "52e1e946f3f9b9b0d5d8988ba3191aaf2a2dbd43", "gitHead": "979f7a3aa061d2262f9e1b41c6e4afd66c71d1d5", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "2.14.15", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "4.0.0", "dependencies": {"glob": "^6.0.1"}, "devDependencies": {"tap": "^5.1.1", "mkdirp": "^0.5.1"}}, "2.5.2": {"name": "<PERSON><PERSON><PERSON>", "version": "2.5.2", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@2.5.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "./bin.js"}, "dist": {"shasum": "62ba947fa4c0b4363839aefecd4f0fbad6059726", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.5.2.tgz", "integrity": "sha512-IlPt1Uu2VoNSXQ2VvrVSdanffKsqS2ozRsJNEDSBFnS3Kt5hpOcHoLFHyOEPS43+ZCwH3BbmW/JvGPWGD3CS8g==", "signatures": [{"sig": "MEUCIHaY/A2hA7eGLYf3bjYeSYOItC80aXv0/WwmyQ+bJtwzAiEAoESF3yqUXWUrIQl7pllBIPm0HdMvYMurqUCAAzIvCF8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "_from": ".", "files": ["LICENSE", "README.md", "bin.js", "rimraf.js"], "_shasum": "62ba947fa4c0b4363839aefecd4f0fbad6059726", "gitHead": "f414f87021f88d004ac487eebc8d07ce6a152721", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "3.7.0", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "5.6.0", "dependencies": {"glob": "^7.0.0"}, "devDependencies": {"tap": "^5.1.1", "mkdirp": "^0.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf-2.5.2.tgz_1455346499772_0.9326622514054179", "host": "packages-6-west.internal.npmjs.com"}}, "2.5.3": {"name": "<PERSON><PERSON><PERSON>", "version": "2.5.3", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rim<PERSON>f@2.5.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "./bin.js"}, "dist": {"shasum": "6e5efdda4aa2f03417f6b2a574aec29f4b652705", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.5.3.tgz", "integrity": "sha512-h7dDyZRKbfOkCgSI8V5Q3+DvrWvOvr3uESyOWdRhHD6rpHdaBiX54n4tlxeSvvKP5IMUHN4j8641s1mZq1T5Vw==", "signatures": [{"sig": "MEUCIAvPAb+nd/degz2uhopN9URw+5v/YXDZEYzGV1sa/ijaAiEA901uoi00Eycbx3QdvLtm6HWIW6jSGOos07TphU6qilk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "_from": ".", "files": ["LICENSE", "README.md", "bin.js", "rimraf.js"], "_shasum": "6e5efdda4aa2f03417f6b2a574aec29f4b652705", "gitHead": "7263a784e8f08d94dd6caf6ee934fceb525a6f3d", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "3.10.2", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "4.4.4", "dependencies": {"glob": "^7.0.5"}, "devDependencies": {"tap": "^6.1.1", "mkdirp": "^0.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf-2.5.3.tgz_1467582915019_0.6380921453237534", "host": "packages-16-east.internal.npmjs.com"}}, "2.5.4": {"name": "<PERSON><PERSON><PERSON>", "version": "2.5.4", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@2.5.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "./bin.js"}, "dist": {"shasum": "96800093cbf1a0c86bd95b4625467535c29dfa04", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.5.4.tgz", "integrity": "sha512-Lw7SHMjssciQb/rRz7JyPIy9+bbUshEucPoLRvWqy09vC5zQixl8Uet+Zl+SROBB/JMWHJRdCk1qdxNWHNMvlQ==", "signatures": [{"sig": "MEYCIQCrC/KO+GudCoLIfejxWmr4pIX36ww+U0DTSdajJMJA+wIhAIX+WIrC3p1AUyTNIFkl1DnvNx44uVJym5stDhJAYAsV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "_from": ".", "files": ["LICENSE", "README.md", "bin.js", "rimraf.js"], "_shasum": "96800093cbf1a0c86bd95b4625467535c29dfa04", "gitHead": "2af08bbbd0a03549b278414309dc5d8097699443", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "3.10.6", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "4.4.4", "dependencies": {"glob": "^7.0.5"}, "devDependencies": {"tap": "^6.1.1", "mkdirp": "^0.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf-2.5.4.tgz_1469206941888_0.8645927573088557", "host": "packages-16-east.internal.npmjs.com"}}, "2.6.0": {"name": "<PERSON><PERSON><PERSON>", "version": "2.6.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@2.6.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "./bin.js"}, "dist": {"shasum": "89b8a0fe432b9ff9ec9a925a00b6cdb3a91bbada", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.6.0.tgz", "integrity": "sha512-Z6lThA8xiguOWVCdiS/uxmDIMXIuxAwBiCMWMF/A/hdPgzvNABydFs/6k8U86nDN9ttRNi9+6LxWILp4XBO+iw==", "signatures": [{"sig": "MEUCIQDrz/c8ViSK9VzR5tEq1F71EauigBp3mic+KG7blG4ipgIgNUkwlcXjOAUr0ACsUwbzQgaEAtpNIuIN4q9eEG5HVWE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "_from": ".", "files": ["LICENSE", "README.md", "bin.js", "rimraf.js"], "_shasum": "89b8a0fe432b9ff9ec9a925a00b6cdb3a91bbada", "gitHead": "5b661e45ff409d42251890cb1b993c9315803bbf", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "4.3.0", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "8.0.0-pre", "dependencies": {"glob": "^7.0.5"}, "devDependencies": {"tap": "^10.1.2", "mkdirp": "^0.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf-2.6.0.tgz_1487455299128_0.33092148951254785", "host": "packages-12-west.internal.npmjs.com"}}, "2.6.1": {"name": "<PERSON><PERSON><PERSON>", "version": "2.6.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@2.6.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "./bin.js"}, "dist": {"shasum": "c2338ec643df7a1b7fe5c54fa86f57428a55f33d", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.6.1.tgz", "integrity": "sha512-5QIcndZ8am2WyseL6lln/utl51SwRBQs/at+zi1UnhsnPyZcAID+g0PZrKdb+kJn2fo/CwgyJweR8sP36Jer5g==", "signatures": [{"sig": "MEUCIAk1f4kzSOFXXWOf37VxxGcG4Qy/KUJSWlNJAkHocRcMAiEA+D+XB7+q2RvVmp9mBpxPZvjGNW9H6CsdymaVz/vvNvg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "_from": ".", "files": ["LICENSE", "README.md", "bin.js", "rimraf.js"], "_shasum": "c2338ec643df7a1b7fe5c54fa86f57428a55f33d", "gitHead": "d84fe2cc6646d30a401baadcee22ae105a2d4909", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "4.3.0", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "8.0.0-pre", "dependencies": {"glob": "^7.0.5"}, "devDependencies": {"tap": "^10.1.2", "mkdirp": "^0.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf-2.6.1.tgz_1487908074285_0.8205490333493799", "host": "packages-18-east.internal.npmjs.com"}}, "2.6.2": {"name": "<PERSON><PERSON><PERSON>", "version": "2.6.2", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@2.6.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "./bin.js"}, "dist": {"shasum": "2ed8150d24a16ea8651e6d6ef0f47c4158ce7a36", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.6.2.tgz", "integrity": "sha512-lreewLK/BlghmxtfH36YYVg1i8IAce4TI7oao75I1g245+6BctqTVQiBP3YUJ9C6DQOXJmkYR9X9fCLtCOJc5w==", "signatures": [{"sig": "MEUCIHnF0n4D5QJys8NmiM8mxxrzR1Wn3ixYV/iENeayCVNdAiEA+UD8dAD0Pl9rrbPNcLFJuy2IK3bh2AyBhiPo5xRNT5A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "rimraf.js", "files": ["LICENSE", "README.md", "bin.js", "rimraf.js"], "gitHead": "79b933fb362b2c51bedfa448be848e1d7ed32d7e", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "5.4.1", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"glob": "^7.0.5"}, "devDependencies": {"tap": "^10.1.2", "mkdirp": "^0.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf-2.6.2.tgz_1505148366963_0.392012212658301", "host": "s3://npm-registry-packages"}}, "2.6.3": {"name": "<PERSON><PERSON><PERSON>", "version": "2.6.3", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rim<PERSON>f@2.6.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "./bin.js"}, "dist": {"shasum": "b2d104fe0d8fb27cf9e0a1cda8262dd3833c6cab", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.6.3.tgz", "fileCount": 5, "integrity": "sha512-mwqeW5XsA2qAejG46gYdENaxXjx9onRNCfn7L0duuP4hCuTIi/QO7PDK07KJfp1d+izWPrzEJDcSqBa0OZQriA==", "signatures": [{"sig": "MEUCIAJNBT9gdCJel/+mqlcUR9J/ne7MMrqr3YXek+U4ZYuGAiEAs8IYEBQs5QwueiVZ3SKKVxwN84QPERQVbw77wHFEL24=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15232, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcLRBVCRA9TVsSAnZWagAA8CgP/iE8LDrquxhzGuOAze8l\nPuqdFv3hpb1PxuX+BC5kcdQyWIvIsgwZC9VpXCyZ9Bvz6dDsCmLcUO+FiJ4Z\naR+ibLu4C2jbG3palWIoilOvK84KpG++2PJuVSXhuaeY4MUfRxerBY4kES4C\nkHtm4o8H1u+crgS5SXeUeRrYfytHjzhJAfpKgemGdnqjExHUC22B7G1sXs5+\nVtK4sYqQ5ep7bu62L/rvPPhX9eNLLWsti/UpoJis/a+NZ5G0/YrUP36dLR8X\n9Q0QChiEwcl7lHrzYPy9SDeHCSPssTZUBpQXjYncRFqJRqZK5vB+QaHE0FEP\nRLQQ7lkAd6Css5YekJc5kcCbIdCnO6Alc82V2Ib6JKZt5JB/rLGHu92CM0bZ\nNVNXBOk/pqYWIvBiOzS+eRW9Yq/APRnJWC8EJ4wN4oJ91+V/oSXNybSLCghI\ngT2qI2jHNihkRrzzNICDIHqUmONZpCkfvBgEvunuUwTLdNhRUGBykXkl0Tyr\nD9bbkXUXxLT96bo2MN1z1MvzLW9Kfz6Cu0gNjKk5nepCa4FB1vRDimIM3oZf\n2Vb8YOMdiLy8hYRy721s4Q+pr1B0faB2EeEMtYmiO8rIfW6HD4uKvOg05EHd\nstDk23HbrKcN3DWHkNH6dkdJMQCeqsFLeXVeQ3QH8mZOGTwLnsRzj0hGVcBG\nQCao\r\n=7Otg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "rimraf.js", "gitHead": "9442819908e52f2c32620e8fa609d7a5d472cc2c", "scripts": {"test": "tap test/*.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "10.12.0", "dependencies": {"glob": "^7.1.3"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.1.1", "mkdirp": "^0.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf_2.6.3_1546457172729_0.1407152545041752", "host": "s3://npm-registry-packages"}}, "2.7.0": {"name": "<PERSON><PERSON><PERSON>", "version": "2.7.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@2.7.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "./bin.js"}, "dist": {"shasum": "eb43198c5e2fb83b9323abee63bd87836f9a7c85", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.7.0.tgz", "fileCount": 5, "integrity": "sha512-4Liqw7ccABzsWV5BzeZeGRSq7KWIgQYzOcmRDEwSX4WAawlQpcAFXZ1Kid72XYrjSnK5yxOS6Gez/iGusYE/Pw==", "signatures": [{"sig": "MEUCID1m5NiggjYXCY2S/TK1J0ypsKBc0Q6t1DKfWvBZ7HAJAiEAqXFNrOogk2ElR7TUhiTi7P+xhMSjKsCZ8t+mLWfFvDM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15463, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdU0/3CRA9TVsSAnZWagAA2LwP/301H0EhIUW6AzV1LOaX\n4M+XnMFDd62/heTxmQX6KH5/Kyk8gztyP+RfTsoqwAVE5yKnvWmzgVPuat9r\npK6YvpiCROz1NoUYb4VamSlnGgB9F0zMsorjQF5FkOG9cKGdspVqceYelGND\nWJbfEVMULEuaS1PzKohKahmdGzh5AYExvkWU6xhlbdTIOncQzX+/okxs9NtA\nTbHVphIQBy/IEqaumi1w896NrIWslRpMq8KLH97Lc2axKHvZ/lS+wcC17LAn\ncniujf/QDbPLZURqzbkQR/UzGMlg8WMUg2BQKYMWd6PyrPRWaD8Jk5Zs/7n4\n3W2NdfpbJcyhje5W1V6oSWpmjfg4+ok4dmpG+RaxMz1meUl0uigful6BvBrF\ncwJoATekyyQgRzGv8Sd9o5A5LFPaLPaQ1gyjw1FaNppvOES9bIvJNSn9zFrf\nWCjKGfepTwtiCYRqravqUlwSPcfTkOnkBf03vUn/fcwvkn1l9n8LFK9gngP+\n2Oyt5g6t6jYxNZJiPxc51RaS3CmZ821VpkJo933A3SNxLuCzdb4k7uUBBZ/N\n9y9ssNYTSbzKVcqivrdgeX1fyJquBvm5hzKe45tzOKdgHI4+AKuOmuH+p2VL\nUZdPhCOOeDg4YSaPvnFt+a7oLXXIcjEma5nTFalq/HhjVZXu/PEGG1Alk1NR\n7USI\r\n=04vV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "rimraf.js", "gitHead": "250ee15838242554a238aa59b3c084234fba4691", "scripts": {"test": "tap test/*.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "6.10.3", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "12.6.0", "dependencies": {"glob": "^7.1.3"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.1.1", "mkdirp": "^0.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf_2.7.0_1565741046749_0.4705353236263339", "host": "s3://npm-registry-packages"}}, "2.7.1": {"name": "<PERSON><PERSON><PERSON>", "version": "2.7.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rim<PERSON>f@2.7.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "./bin.js"}, "dist": {"shasum": "35797f13a7fdadc566142c29d4f07ccad483e3ec", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-2.7.1.tgz", "fileCount": 5, "integrity": "sha512-uWjbaKIK3T1OSVptzX7Nl6PvQ3qAGtKEtVRjRuazjfL3Bx5eI409VZSqgND+4UNnmzLVdPj9FqFJNPqBZFve4w==", "signatures": [{"sig": "MEUCIQD3BXRIXk7Os11FXCZml/VX4SUsEzwY/abNabdxB6Aa4gIgVN4PQ4PLCkMLUT1YRqNZec10WTxK81jKP2d78BIKBQo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15463, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVDyNCRA9TVsSAnZWagAA8W0QAIT29Q9jl+LJNy8n64zS\nFsUTl5kKdzwXBsldns0NuhuISz+tShXRH2VvBkD9QibzKDb1g58ePtXiPX2M\nz1QiRXsCddl22zf7emSv8xPrUMm3T4gR911FhdAiwRxT7XV8JUsEgeDoYJdB\nzAQn1hhyRcSNQDn0mGs3JHjrMCJ6UaS9xpiHb1uXN57SNVsauI2ui/JyvaNH\nwfL/cgRA2Ehpg6//f3bA7o/2OCs0i9GlUF24C1wfKb+ppFyCnlJbRuyn4Kaw\nievpjqNGRrYCb9BzYQ9Cg2X1ZKUW/ISt7fWr5dGobcZl6+WQVRLvK2rCgWnR\nsL9nB3xXSab2Iz/1HrS34a+eUJhZPqPyb+SHO2Zpx+Y/EXNXoyORj3xUdHk1\n/uYRcFQXIV/L0U0tVNsD7iPnS3Qhn9Qd2S4ywwwbVOqq8yRTcmqdU08SGQIK\nwuMCEPGu52wZWtvcFn83oDralVr7zmFc+3Fx7dUkhZKb4ruzQzdzGxWAt7Qw\nX1Hj2uI6espQ17HgUURKukWKxTOO2l1YpJ4bE7b6caKQq4hiyuMBtHk2+ryr\nqSZQkSrg2/yGDW/BdTq/14hios74XmYihjcFUUM3nomYusuxAL3+jHhmOD/D\nk+HVcl5FY0laW+EMfcsUj+Vame6Nn8SFvAmVOJgnMl+JAudpoXZcaD0u6q1+\nzv1E\r\n=DYaq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "rimraf.js", "gitHead": "bb572746b8791d886f25913dd57d2859b4246ef3", "scripts": {"test": "tap test/*.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "6.10.3", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "12.6.0", "dependencies": {"glob": "^7.1.3"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.1.1", "mkdirp": "^0.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf_2.7.1_1565801612611_0.04079291615185654", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "<PERSON><PERSON><PERSON>", "version": "3.0.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@3.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "./bin.js"}, "dist": {"shasum": "614176d4b3010b75e5c390eb0ee96f6dc0cebb9b", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-3.0.0.tgz", "fileCount": 5, "integrity": "sha512-NDGVxTsjqfunkds7CqsOiEnxln4Bo7Nddl3XhS4pXg5OzwkLqJ971ZVAAnB+DDLnF76N+VnDEiBHaVV8I06SUg==", "signatures": [{"sig": "MEUCIB7JUVspn7ffZkdDMRuF6bFST7P6mCBlDqHAsnXgZL3FAiEA5CTX9sO99Xutoeb2wCORWAG86S1FNbWJKT+KckWNPdQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15970, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVE8bCRA9TVsSAnZWagAAZNIP/0NrU0yQqJNOLOEGgbUG\nkM05pe5WP4EF0er+OLh0LmGidZxDC7onmobxMGK8sSjdyQEG2oNrjnx+xO50\nSFTa8lwrOAgACGz06cIISUFppk7l7UOOTobTixh6sE9IlDkuV71+jBypBudJ\npQMxrnQlbPSKaGXK4x386sN8OkVNiyRKR6cnthwmzigtWGeOfdUXehay5A37\nlxzhgW+1j510N3uCEu7CFQRaPYB+nHV31bAdb983ghSfKpyiuylImZpcqUlr\nszJIgO1j75cYehBsDjkTUgrIDOoJg04LzcmN8QVyD4DNkh9UYubYiHp/IjI8\n/AXXQXsjYb7egBx279JdPi62Thn1CXsMsGqbpzqNp6J68lYrO1OdhVyHh22e\nG1oHs8lC7fY/CzmHeIUfMe1XXKT4w6Kja9jexwraRlPfXgNMTW+eteNs0Twl\nYg7+gHceKviuyeyMq8Z9uCsdNWPeguYX593oAOuSALSCfJmHZT4EqYjasWIz\n8GNKCc9Cp7frpACu+IyfADjcZaMz7pI0KJQc45Y0uX1rtLkXQYxe1x+Q5XQ4\nstOUlZF/FJE6LuCOWdU6kgQ9mu7m13yI4ZT7KqHnsooIsbbtcQmqchrwRvVo\nHx9r5Z1smeVT+KU1TK4CeNP51Ggy5/oqHlXLOIufTiv8TvGaH4sc1OYu+8sc\nYiMu\r\n=/syx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "rimraf.js", "gitHead": "191ae6979d93330b5afeb6886e70047a98bb5253", "scripts": {"test": "tap test/*.js", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "6.10.3", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "12.6.0", "dependencies": {"glob": "^7.1.3"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.1.1", "mkdirp": "^0.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf_3.0.0_1565806362422_0.6548862349674989", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "<PERSON><PERSON><PERSON>", "version": "3.0.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@3.0.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "bin.js"}, "dist": {"shasum": "48d3d4cb46c80d388ab26cd61b1b466ae9ae225a", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-3.0.1.tgz", "fileCount": 6, "integrity": "sha512-IQ4ikL8SjBiEDZfk+DFVwqRK8md24RWMEJkdSlgNLkyyAImcjf8SWvU1qFMDOb4igBClbTQ/ugPqXcRwdFTxZw==", "signatures": [{"sig": "MEUCIGWfCB5yYdKN5pj+sIfJSVeEX1ywuzkH3PMhkiQSigC5AiEAiupBlAwGmi/jeuQhmMO8D6L891nz+A2JtZhw594NJc0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17527, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeL64jCRA9TVsSAnZWagAAbzEP/14CYXfraD2Zq1n70g4h\nQOO5u0zdQCx8tuDylNUf47IYn9l9WMMR9Rc8JRxIFbhYPqga9ZjD1vJ3SnqY\nRaHpqqsB3BXBlQRFRccGnmAzX8oY/abuyQA68CfB4oefC2X0nNEPT8tZ6j6K\nTzLpzStviDAqzRbD9C2OaPBqrGhFLrYfJDoJ+Hh4WQU+PZ0bMiYTWA5gJ4mU\n+rug+gZb3m0L41llvd7b2MjFaFy6Ao7XJHrIkmOElpqbnLJllRwM4vNJv/e9\nDxRqYvUHmHs/Ok/XOmwrE639jOFzvvyaTOTReZEwA8JXuS5F9qw+jVSOAve/\n16tzWrC2Q9Bd1LYT5sDF9WYj/iznuqZ+VNLwLYM0yJ3C8PLvMtY+dgoX2pSE\nnFEOTijPBkRJ69hx+YKFt22ey7FnyqWiQbgmJeEc/Fj62z7XeBDWPy2bqfmj\n1MqUXWc3QYwMEOmH0jDv/ANru8SUnBNj+xEVD8K7m/HN0ETc6zbI+T3VCsIo\nEOem3rbW4yt1MxDO7QQ115namGEq1a9O+39X7bsydPBZE2YJApJGLJDIn6Ue\nCeyxynPuN6Rb+ZZBbhsB+Qmv4f53AwKtb7xzly0YMvTZGE4Jr4FOzI/pd9Ko\nVbsNJzMXkr5vf8stOsJiRdw+DfejJh6p1o/VA/r6BvoyP8KPq8XmBC79J4gJ\nND9y\r\n=wIbg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "rimraf.js", "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "d709272f6122b008de66650616fda34c8ae6f954", "scripts": {"test": "tap test/*.js", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"glob": "^7.1.3"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.1.1", "mkdirp": "^0.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf_3.0.1_1580183074937_0.7895967350443918", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "<PERSON><PERSON><PERSON>", "version": "3.0.2", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@3.0.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "bin.js"}, "dist": {"shasum": "f1a5402ba6220ad52cc1282bac1ae3aa49fd061a", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz", "fileCount": 6, "integrity": "sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==", "signatures": [{"sig": "MEUCIQDGihngc27ghlDnKDNXT9RhfIWmyFcEFMM0O04hwSZ89QIge3JjJbbQVY6PT91mp6XLHrqbfuyA/zGaQRWTl7Taeuk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17327, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh253cCRA9TVsSAnZWagAAnsIP/1OTKaCS4Fr/j5P3cDy+\nMd2H6nbfCLNnHvzz3Ma/vDpgCuEVxG0fsNYL5q2ZQ+qB8Dcmf9+gAfKiaiJO\nNa8ITCub2CCxYULMd+nLxv4XRpq4l2pat60i9yaZhINhqN4ZwyOCaMOLmS/X\nEtStBJEnuqs9vHWX6K7J6a0H+dRPOcUTpzL4ZnVTwysM7ZuN9uX8a8ifZ0QY\nPuF3dWFmCbFrrjZV5Ku7lwoHHE92xQkKqmt+FleGAHiFBRhw1d0XB83+DYko\n09NKvk9HFs2fwylNmUDyl3hwYzZb3pLaq4S6YwNxKwUGcYMRsorx4qF4QyQJ\nL4OcJuNBp5mUCT0qk7bvT7v5qjTHedld8LMDmhQti8viDXnbwJWajaWympMR\n2xgK3jRH1Qnct+hywLqzN3u2ilItWIIA84nCruFmu8wE0ZEPJt3IEuDgNpJa\ntxG97b2Ev2lEDNe8kG+2VcazKR1tjHpG4v5iE5W8417os9Nll0uVsvXSWayS\nj+8OEh1KJw2D2h/JN+S5rMS/KrwGgBkUjyhRMfnK2PP68fFwnpK//F1V6I4T\nGGWW0DJfISLF9U4pjEHlEtP+kERUrkKMA+v0vTNIN5tzeR/WFuQyVLCCnD1S\nghq9PsIKCj6UlkrfPBfPoognBzsNKYVsZ7NDDCP3pK1ImmaTKEA2yuXB7pnh\nbp8H\r\n=k9E2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "rimraf.js", "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "8c10fb8d685d5cc35708e0ffc4dac9ec5dd5b444", "scripts": {"test": "tap test/*.js", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "Rimraf versions prior to v4 are no longer supported", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"glob": "^7.1.3"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.1.1", "mkdirp": "^0.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf_3.0.2_1581229117362_0.43146474897774256", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "<PERSON><PERSON><PERSON>", "version": "4.0.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@4.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "dist/cjs/src/bin.js"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "libtap-settings": "libtap-settings.js"}, "dist": {"shasum": "216e6bfbf077610f9fef976e60be50d634992666", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-4.0.0.tgz", "fileCount": 107, "integrity": "sha512-1jUYEffrxKEAYJLJvqYLvKQW5oExH8QhBVYKUO1YXPrKTpobYuZ89KbgmpGbeTj+6kUXqGrIT1w+3BI4T6hR1w==", "signatures": [{"sig": "MEYCIQDT6fdsLTGSoc+COeLdYDexO5S+m41z36PUZN9QIe1q+gIhALv3mqYtIolzgPujKuvXoQTSq1C1+wbgdIvrayEFbTpo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 148365, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjwKX1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrdzw//Qi71HX5omesajScN+M3n4H8TxCZcNNcMPP9xv9YSPoe3xX/E\r\nD/fA4DlHSgU6N6FFlF+qeMzgBy+AXuV8UUO6A3oTUU+AZ1kcihW/AhDl+UbF\r\nnZXSfHF+but8jtIco7ZVeif704Ah4baaoLpolNqI+jZd9lV0viG5auU+D+9v\r\nvZyuiX6ZsEMrayeuZXt1Lk5j0yCCsW5EhG9VVYMoxku2twSMDUGx2KQ4S/mu\r\nC89gm0eGCluda2Eas0X/WcV9K/BjhbYLksEW7G1jbfzzdF7H/mR6sVKjmRhK\r\n4kw/++2OQO+qjee5MzogE7BitIYWlabTa85+aa4fbhcSkuEaAQLAkVB63VP+\r\nvv86y0KfGggO2s8zpOZNdsl+2bA8kZPLYukY7vSG/sYnORd+3hVDfl/MXQhl\r\nTr6cL+RoUazGU8XPrdaDesuguy5j10kPjAozdOfQ/wY+ouEHJc1EiBzhBhlS\r\nRNrPw5XoQXJm3CEozfCz9XtIbzd2dNcTMpmvVkv4gfCyEVY7Hhj9sK0AU8iV\r\ngGz59MttZTmXlnFcu51zLH1lNrbgOYeV91BEsjsKIlJgukwENaj13SEop7ME\r\nRfH1ehlqSl+8kTMKXt9lm4R1MY657IBikm0OJ9Q/EiTSv67lbDhP6rQjz/Bf\r\nbepDMcpSCcrZ00oe6lUKfgr6Qjhs+c0Nvhs=\r\n=14Ra\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/src/index.js", "types": "./dist/cjs/src/index.d.ts", "module": "./dist/mjs/src/index.js", "engines": {"node": ">=14"}, "exports": {".": {"import": "./dist/mjs/src/index.js", "require": "./dist/cjs/src/index.js"}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "3b6b098c16a535295dbaada20d5b237fd4f7469c", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig-cjs.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "deprecated": "Please upgrade to 4.3.1 or higher to fix a potentially damaging issue regarding symbolic link following. See https://github.com/isaacs/rimraf/issues/259 for details.", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "19.4.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf_4.0.0_1673569781585_0.25937024865223823", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "<PERSON><PERSON><PERSON>", "version": "4.0.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@4.0.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "dist/cjs/src/bin.js"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "libtap-settings": "libtap-settings.js"}, "dist": {"shasum": "e8170d52ab336054026ca38eabe045b1274e3ab4", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-4.0.1.tgz", "fileCount": 107, "integrity": "sha512-qwvb/Wzf8XJxdv8InnDtmijfbIk6gk576swPeogHbUh7AT4ezF42lvhXUdbIzMYrO4HbaXi1rYGhKTDyPF1AJw==", "signatures": [{"sig": "MEQCIACMPvB7dOL+QlN71mXYUSC9vq+L0BxavQZXJMmo3mfQAiAXnjFGqNUJHO/xossY+VE5KSavzMU1Yjq/Odg4fJdecA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 149261, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjwKutACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqvEQ//T7Z49WKMHDLGZHFhN5FHG4+k+AZveV4gAqQRA5SQNhQHgfP/\r\nOrt+0PbFwQRJuF/rGkAdPnW/nzuDkOQpqooeDwn63DlD7tSE46wxz03AapOe\r\nqS66H3bB+e342F6Ih7jrmiBGeMcvVOamNK7nXGmIM7WcjJqxqCpHkfUm08LO\r\ny7UydbVqHZAEze+I8jUS8MDTTehIUOcU9LzDFYkkQ+w0CVdhp00nOSFyCFMO\r\n5CUxW1TAEo4jBUlnwf2Wu0+CqSN7NMrXrpCsQW06KHFjw/2Ss1b1li8/WEFv\r\nbH3tfrt4fXm53mECZ0ikt2miNBFblZjs5i+coVcgJz39YS3T8yixpB0WpMwi\r\nlwUNWRZSBzRtDr9a/1SpPRdpuaU6ouMiKRoAum6FIRLy9Ovdi93G2HuyEehH\r\n6BxsP4wS1gZm5+Y+vzccCNlQlzLBJtIbAXG6XbbR17ysgwqgClZSkCfVZ6rv\r\nJ6WE+vLDT29i/lWTTTD4pSe1bGlIavWv6w1XFrXDPoTzjwEVn2bwgy8kYAf8\r\na4fggmsnRWLlqrT0TzcHZVUpEsuB456QvON3Lb07WneyoD5NdrsD6VsOeZJv\r\nvlIisT59DOvH60OKea4rCbEOZId02kESXwXU1EKfc4YhNKqMQGT9Poofiaxr\r\nTkydGlhACjp7zEcIN2XYjMgi41mfbOWrK70=\r\n=Ewiy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/src/index.js", "types": "./dist/cjs/src/index.d.ts", "module": "./dist/mjs/src/index.js", "engines": {"node": ">=14"}, "exports": {".": {"import": "./dist/mjs/src/index.js", "require": "./dist/cjs/src/index.js"}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "5d5e87c9ae0f5676376e45ef7e6993709e890a7a", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig-cjs.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "deprecated": "Please upgrade to 4.3.1 or higher to fix a potentially damaging issue regarding symbolic link following. See https://github.com/isaacs/rimraf/issues/259 for details.", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "19.4.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf_4.0.1_1673571244968_0.7555858909607547", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "<PERSON><PERSON><PERSON>", "version": "4.0.2", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@4.0.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "dist/cjs/src/bin.js"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "libtap-settings": "libtap-settings.js"}, "dist": {"shasum": "3a91ece236033a83d7a1332a7d929559ee6a3791", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-4.0.2.tgz", "fileCount": 107, "integrity": "sha512-WrOGeOIPqDLQZw+fBlciqu/yajcfNYeI1tERmCNMJ14VLBiKq1Ud3xSfPD+nqA8kYRcEr39HZeBuxHYEqvoRyw==", "signatures": [{"sig": "MEYCIQD3SC5V1CiAONWAT92CxwXn8nmqMFjJM0jU4OmeqA3GWgIhAJv6FhXTCRqYx9OXnKyVy1xOBvUF0OXXNv8se71qIK29", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 149635, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjwLSWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpz7A/9HmFAwuXmWijhLLfFtThPKNcGgBUcR6GEXz1Y+evxeAsS39YB\r\nne+6XDeV6Ozpyj/ia3+hBt9Ze08l0QabDLUyPj/W3ZICq5wkcxbwXNFTQjBW\r\nkpR7Oph0YzdsavUMFX8o3X3eisf2zUuj1whuBZkOMs6rhsJdvuE8/XMsc7F6\r\nwjsH+XRwLW7+4UNTTbrYyEtxBGjOSgVMDL7P4ggcncMWbFaMUpjgTKbc9C2l\r\n0VcPW7OA8bTHPXjiTCB7THBdX/t2d/IKmKypUCYwiw/YvCbyPZ2MV6GJyAc0\r\nkaPQ/3ag80d8nHJuXL+MFhSLJRMj1vkSxgv2uOOZhTV74QAntqgXdMPIUjUT\r\njoROxQiRMERqOtADIifFjHtguNH8jTe0Fuki7bCCqpR1imtoHaLWCa4HmCvV\r\nMKad6KZUziQDPYNgP0bWBwnm780dkjclUCYnHZCBQD7f0RHBOpmLFcXI06bq\r\nnAZMBUmEDeXCw+zuoMb97P7nTDeZQdahAY/AWuDIWI1YtdXugX40KFFNNO+P\r\nWGxYubinqOF8Fh1V6kJwNOzgyWAzQ50WBYdnpRr5TSgO+W2wusDR3OtOXegV\r\nUiTxFLcP8j9dnz4ShIJU0BdXdVf6IQ0yk5gQgP4Yj7sZgUMx+GZ9O5DsaHyv\r\nhWMzsM1UZony6c+KQElJSpGKs5GMVW0LDF4=\r\n=QNv+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/src/index.js", "types": "./dist/cjs/src/index.d.ts", "module": "./dist/mjs/src/index.js", "engines": {"node": ">=14"}, "exports": {".": {"import": "./dist/mjs/src/index.js", "require": "./dist/cjs/src/index.js"}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "bad1ecfbc17cf1bae488835e86b1eb0a27b667b0", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig-cjs.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "deprecated": "Please upgrade to 4.3.1 or higher to fix a potentially damaging issue regarding symbolic link following. See https://github.com/isaacs/rimraf/issues/259 for details.", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "19.4.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf_4.0.2_1673573526022_0.1205509908110225", "host": "s3://npm-registry-packages"}}, "4.0.3": {"name": "<PERSON><PERSON><PERSON>", "version": "4.0.3", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rim<PERSON>f@4.0.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "dist/cjs/src/bin.js"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "libtap-settings": "libtap-settings.js"}, "dist": {"shasum": "5e22101e127e869465eb812445ffb77eca8cab54", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-4.0.3.tgz", "fileCount": 107, "integrity": "sha512-C8djGToHJHno7oMSP32Jg3EpzyUXtCE9Clo4LnBJu5XIoVb4rtzK3eMyR6iqJ3KfR/m90/ekfDt3Qp111HcD3Q==", "signatures": [{"sig": "MEUCIQDyFlXRNU4obD3SuKoe8JTePBVUefOhO2CT6UJQiU/tyAIgBM+mGYKz3JTVqNAvgmO6D/WabUTTM9DflFQIY8hYCiw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 150240, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjwLXKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqGJA/7BVe7B8NFgwubYTLVofmElqNIiCEBwxhxaqei/BzpBiwJInRz\r\njqVn1wHfEnnXLomQx0u34gdlrLyv4mqrEoz3WyDeE/cdHlTMqyoh/NiHPm5T\r\nRNYRYMSBuderWQRUhdXOu3Z+5xpUAY4O4oTCx68Pgya/us0h7TPH5Knjaeyl\r\nKUhWINyuyEWKzT7x8JbPbIZqFcT/1WSpXz0+fk0fz04a+6Ox3e7l5vL5TQq0\r\ny8TABEr9EYbGsr5Kz992YL7Z3sPylei0TUV5J2PFxTvv5VM2W/NCpf5b/LEf\r\nb0w6BDFuQcm5+2OJGnRo/Xz0zEIg6jbgz6rstZlP8PVkgjiZ51s8/v//QQOt\r\nDn1UKqlnyK9DiOY1+lR/cdV1Iwen+rt0OoeWD843igJreymm2TN9yraBS8Qr\r\npZw79RDjCINSSROy/lGDp5PkC4jlq40IlROjccdRmi27URM5hREqJVfDxVoy\r\nlvdBalr6Zv67vp37lKmbPS2Jk3hjS2E5m+AJBsszD7RAUPTu+GUlch5z3VII\r\nDH2524zMKekdVpdqTDmYi/x4/2KKyrL3iRZuoRuM0mueGEGKhZA4EC2UPlo7\r\nP51eXEBzF5y4OpjzEIrWb6ziZ7GHlHF7SWQxHF1vLc1pv7G4YfEIlfi0Y5uz\r\n2P2x0gh0YPK/LmeEJ9OeBpEkiBGZ3+iwwJg=\r\n=SHDe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/src/index.js", "types": "./dist/cjs/src/index.d.ts", "module": "./dist/mjs/src/index.js", "engines": {"node": ">=14"}, "exports": {".": {"import": "./dist/mjs/src/index.js", "require": "./dist/cjs/src/index.js"}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "9ba1a2e54e57a3841486e36bb6f431b905cba46f", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig-cjs.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "deprecated": "Please upgrade to 4.3.1 or higher to fix a potentially damaging issue regarding symbolic link following. See https://github.com/isaacs/rimraf/issues/259 for details.", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "19.4.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf_4.0.3_1673573834574_0.01549903700777766", "host": "s3://npm-registry-packages"}}, "4.0.4": {"name": "<PERSON><PERSON><PERSON>", "version": "4.0.4", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@4.0.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "dist/cjs/src/bin.js"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "libtap-settings": "libtap-settings.js"}, "dist": {"shasum": "8d0a91709fdc294d40f59c773f63c44e8dc878f1", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-4.0.4.tgz", "fileCount": 109, "integrity": "sha512-R0hoVr9xTwemarQjoWlNt/nb5dEGVTBhVdkRmEX2zEkT8T6onH0XKiGjuaC7rNNj/gYzY2p4NVRJ3sjO1ascHQ==", "signatures": [{"sig": "MEYCIQDlVbtK1a23Fs9SJh6rfsoXcWHqguylgd00VcfZK3iwVAIhAKSDWnnOTwC6FrZ1OkRLSSojgYQkjNn+Sj+nK/5SKab6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 150020, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjwLa1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoh/RAAkwJxwLOGz6Bh/IBVnIt9heSb9HqWIwcGGiPk5HHHjEUkTzzM\r\nnuJywGJ7NBnSFRMzGa+tIawl8EOKhBBGQDIWppL3unLoLGOPgwPIhHqIjYeC\r\nDUmiLFtWJMB1w6TErq5ZoCDBCmgwO9MEWzhtzsPBY6lhkqtWz2oBjxTlWgM4\r\nEBtrUtsAtfN2UuZMoV3bSIoEs3IjoPWJGwc+T1/bSyMmzK+EzNviow0fTpp7\r\niERxgsotLpWhST0sM149UwFOdWEwb32Hdm09sviXHuoEv8cLTWHMb3+JyBfF\r\ncrr+l50kYZlhKkS+vfjKzhuR4PNF4fqz3DK0myG2gnr2Ea38cmsOf2wtSyB7\r\ncwFm3XJ7Y7u7WuQhkZcmtBsr8/FQRZUkMO13g5y/1quD4sj85SeM3+7qowXk\r\nPGyJgLUDGG3IGrZCxYEPWwZzWc86MS76hGXFfnbvIUHadaFjGqPjKHK87w+0\r\nIQWEdPSp7KmRot4umsxZOlQ6THiLnBG8Ab6t+6L8EcF6R+mF9T+W5X2c0KUk\r\nGlhUFgT8GrfDSvaWwutiApnNfEVrT2DyXtNJFyF7QHZIyL8xgyBqtQBC878t\r\nQkN7PUOOuLJGlSNmeuZwpjCXrUI/tdx9y9oJir1Ri6AP3lYSq8t6qXB7sUVv\r\nDiCF39WKWaU6aD/7tbrDAmQFlqkpjXCHYq4=\r\n=bcHk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/src/index.js", "types": "./dist/cjs/src/index.d.ts", "module": "./dist/mjs/src/index.js", "engines": {"node": ">=14"}, "exports": {".": {"import": "./dist/mjs/src/index.js", "require": "./dist/cjs/src/index.js"}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "a02bb88bd3897d4259a6a2c9cd53cde1b8b3dda4", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig-cjs.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "deprecated": "Please upgrade to 4.3.1 or higher to fix a potentially damaging issue regarding symbolic link following. See https://github.com/isaacs/rimraf/issues/259 for details.", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "19.4.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf_4.0.4_1673574069497_0.6114625231253124", "host": "s3://npm-registry-packages"}}, "4.0.5": {"name": "<PERSON><PERSON><PERSON>", "version": "4.0.5", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@4.0.5", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "dist/cjs/src/bin.js"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "libtap-settings": "libtap-settings.js"}, "dist": {"shasum": "c93184b2cc87e78b205f912226320c8515df9cf9", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-4.0.5.tgz", "fileCount": 109, "integrity": "sha512-3gf/WdsBM+ZkuPb76MpXSxNKt00QQ3PdXqDCCBOsHgcoBs8DAhXbqtrGQUAkhxHgXYWzXxuaOsN4s21+at5fBw==", "signatures": [{"sig": "MEQCIFBHiifus9GV8Kya4VCEOUM/O1btAEdlHwMZVLp4nccRAiAKg/lLGgkVgBW2dIl7EFJ0/zE66WFsa3dLglg2BeWerg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 150291, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjwvQUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqA+A//Sxv2apnm4t8sIaO83mRTBziDaMhdpOT6MJ4vPcfIhnZypCll\r\nd7rDwbB0aggZVWtpmjhYhHGwiIRHNPKQTohEb3Pri5Hqt4fLypr2yuv2Ennv\r\nK4KTk6qWfRHuHyci1NCifR4BuNxuRVgffhFoBp3wS5dtXDFMl4d2tMjGQDnZ\r\nDy5YfxIyupjSI3Ut3+LSqB+83BtWlGFGH/Tajdop/dSn4lD815jY4sD2q/16\r\n7Vf9hVNPo5Tcxos7WBx4ZwoGbC47vzhy4Q+nc1fwXcNSHSFAKDCsolN4lidm\r\nFTSfFGZfuVojwUyuMnP/ltxp8/0EnxD433kmcuNuIC8xGHWGnK/WIi+pAL1W\r\npVCN3JQXXEduCYJZQKzMR/wUxXiXuMHL1hsAbqaNDEA/hHqoYX4WoBCT0MC/\r\nz5oRIsvg2UzU/21G5UGfxDP5nQOD+yI/KSYspfdgs9EjCeiT/cit+lSofTPB\r\nT/4hTrNjYUhWRRkffcPMIN+spdVHFi3tdWuCghLrFqd1+6AEQGdZwPTd66xQ\r\nIAgYAgGTROIT+EWp2mEEmSdrkZ+nWnG23vjTULQEXBKtiG8VQ0Lk4CiDG7k4\r\naitSLDTzMIW/DlJAcWeePwQ5xsNjaQZ6foHEYU4f02Dw1KyWVhCbM7tq0iUO\r\nRYkngZIwZ4UCYCWDLpCyHqNziERreByfNeU=\r\n=BWqO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/src/index.js", "types": "./dist/cjs/src/index.d.ts", "module": "./dist/mjs/src/index.js", "engines": {"node": ">=14"}, "exports": {".": {"types": "./dist/cjs/src/index.d.ts", "import": "./dist/mjs/src/index.js", "require": "./dist/cjs/src/index.js"}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "db19dd21d33092c82eb23c924b885935651fd7c6", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig-cjs.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "deprecated": "Please upgrade to 4.3.1 or higher to fix a potentially damaging issue regarding symbolic link following. See https://github.com/isaacs/rimraf/issues/259 for details.", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "19.4.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf_4.0.5_1673720852704_0.08436743650170753", "host": "s3://npm-registry-packages"}}, "4.0.6": {"name": "<PERSON><PERSON><PERSON>", "version": "4.0.6", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@4.0.6", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "dist/cjs/src/bin.js"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "libtap-settings": "libtap-settings.js"}, "dist": {"shasum": "cfaf33accc45fa393faaf77a1b3fd68de7a0a7fa", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-4.0.6.tgz", "fileCount": 109, "integrity": "sha512-O6zMt2OF2Vuxtm6V4wdBd9t21WPqJeXrHQiFBL4BFHnPMk0MBYaUhcJ4/u64Cp+PpM9UgVajhtidh8nD6fz1FA==", "signatures": [{"sig": "MEQCIGyUgC0aDkyo+m1XMU/LeiohWfdSkEjUbtSBIGg2t776AiA557J0IhVYAXxVzswmUKXx2Toe2TP8/d5Hh21aJBqsaw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 150313, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxDsxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoeTw/6A4h7qdfysVzS4OjJVzv3hpSPiL2s9JHd/F8bdxzCToMkAsHX\r\np3bp6UfaNEBfDL9xh/Ln/bNgXCuQxLg32wr0WVlxndHR0rQY9I7jh/m56bUx\r\nTuvWq5S6PsQTMe4CuhjYG/OEDkOsxBu1uX2bLMu8OMK+gbz4UqEZybdE+xcR\r\nHShq3Y+Vz56Yb1whcapUu6SFkXfN28FDxvgelxY7gn03xu01BvQnGXR2avB/\r\n5Ionl+Rx7nZ1nm+u+e7oqamiiqxfGKpS0H+MpnD8gTTdI4Jn2QAGjznwEXBL\r\ns4Iszr5VPHEFEjMsKK7frAW3ymeYCqhLawNbREOcQrRVOkrHamQD4qeWtNIs\r\n+JlJ4SUK7LG8rkOR4xnsM8liklfKpa6lYG6/2ZFpaYAsBQ6RIKtXP/jEoOU+\r\ng4fy/Jeri56G6Izt6NPsYucI3BGqNaMA3r5pCbj9xm/EMKQfi3FOQWpTpRSV\r\n0N9IgDf90xxqPV5QwjwcSXq7Od9DoQKzQ8rtVK67O4gXSHwpMvoMbfcwnl1s\r\ntd0IkKDQ6sPHknJJGwMFhWmd7BW15NZP2u1+zJ/Te5+aS63Js+UkpqXg0nGH\r\ncpLQUpQYJc4cOSNiKG7BDXwr+vnfz1+Ojtdojg1gT5IMQT6eQbpULEslWO5t\r\nXC2sAgmONx4KDMe93rMTOzbFBf5VO8enVQk=\r\n=sG3M\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/src/index.js", "types": "./dist/cjs/src/index.d.ts", "module": "./dist/mjs/src/index.js", "engines": {"node": ">=14"}, "exports": {".": {"types": "./dist/cjs/src/index.d.ts", "import": "./dist/mjs/src/index.js", "require": "./dist/cjs/src/index.js"}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "20a9dcbb003789f8179a95370af5146628de6a11", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig-cjs.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "deprecated": "Please upgrade to 4.3.1 or higher to fix a potentially damaging issue regarding symbolic link following. See https://github.com/isaacs/rimraf/issues/259 for details.", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "19.4.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf_4.0.6_1673804592821_0.18156619034717636", "host": "s3://npm-registry-packages"}}, "4.0.7": {"name": "<PERSON><PERSON><PERSON>", "version": "4.0.7", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rim<PERSON>f@4.0.7", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "dist/cjs/src/bin.js"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "libtap-settings": "libtap-settings.js"}, "dist": {"shasum": "f438c7d6a2d5e5cca1d81e3904a48ac7b053a542", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-4.0.7.tgz", "fileCount": 109, "integrity": "sha512-CUEDDrZvc0swDgVdXGiv3FcYYQMpJxjvSGt85Amj6yU+MCVWurrLCeLiJDdJPHCzNJnwuebBEdcO//eP11Xa7w==", "signatures": [{"sig": "MEYCIQCCZRwcNjTpooqtNsdmdLZGhI3Q31t5lEJyvYtAQgTHOQIhALy/2FNplJCgzDLMa01KnXBJlEswA1r8rR9Pchg4uT0n", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 150583, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxG+bACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrQAhAAn1HxLmEaoHdfuIxIbzXFUXw/2g+ARyd/SWd8CRE7zckfl3TC\r\nbdFPbCn+NPfyiFBTaGP3RHlgg5Q9whV7ObNSLtz5Igsmu325KTamwWsWOlrg\r\noM7JWwYvpo2UsrcAvEi1qAkDVEpOAu9eO6bWT/ViYL2k9IvIVeYnHgK8Hru9\r\nlK7fe3NgXBEYoCIGn/ZR3A9HoxIm+NQBU7ZPPU+L7DS4G4zNNtelWCL82Ij6\r\nz+R9l7rzznNY4Sge0PpaaODEjAZvv3cKycWj0d9uD09WLD1GwpgNB6NRu9vU\r\nfI/bEb6h/PSmr676WSJ58JZ8TQASY0xSZNcPk6VfsWlfeNHfQOVR/0A/K1Ep\r\nuXCmBL/VDmj+9JH93ifLE84mot1y9aI+iBEBY9k2rpZWA9pCSBZvMfrpREbs\r\ntoqE0iQwj1TQv7eYXW66fpSTM6a/c/UYkTpTX5uTFSz8YQvwPjmH7svrY8Di\r\ndpBHSlshLNBK9eMmP/bqeBL1y94OXeyd4gvIO3VlxQrn1tEGAelH5ixmNcY/\r\nIEdNPPmYrOPbPW90OzAuraOrJaRZWafKD3/4XjDjW+iGUVjohbUGEm1bEhdt\r\nKh//cGpV7npUy15+kTUoImw/CLC0uwwihn6CRBeUSQQZUUrlH5zwKZLbgz7j\r\nnv5uBEexJRc4vNgDYPKqACXQ9w4+LwYxKZc=\r\n=2dfN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/src/index.js", "types": "./dist/cjs/src/index.d.ts", "module": "./dist/mjs/src/index.js", "engines": {"node": ">=14"}, "exports": {".": {"import": {"types": "./dist/mjs/src/index.d.ts", "default": "./dist/mjs/src/index.js"}, "require": {"types": "./dist/cjs/src/index.d.ts", "default": "./dist/cjs/src/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "b563d8b0761525196dc2230e4e27df94cee8322d", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig-cjs.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "deprecated": "Please upgrade to 4.3.1 or higher to fix a potentially damaging issue regarding symbolic link following. See https://github.com/isaacs/rimraf/issues/259 for details.", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "19.4.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf_4.0.7_1673818011194_0.0006246918969587956", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "<PERSON><PERSON><PERSON>", "version": "4.1.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@4.1.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "dist/cjs/src/bin.js"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "libtap-settings": "libtap-settings.js"}, "dist": {"shasum": "c503e3b75b0da4e276846b45cace9a93d16a4903", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-4.1.0.tgz", "fileCount": 107, "integrity": "sha512-ZJdkUR5/dRrRJHdcSKJLrpRn6tVjl9ALxd4yrTCxFsMpjQNzwHF6GLqMsuEzEYIXxHlFPDSk21Vr8VPuFBEz0g==", "signatures": [{"sig": "MEUCIBu41/uRzjODefpA2Eho0P/ciKsW7YxKfTEvEuYKI+u4AiEAqmeYG+IPzAJlhTNMq5n3rRc429nNoNmrUhi7c3zYLf4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 145044, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxe3hACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo0Uw/+MeXSyk9XUB3zFSUAoW1IDceJThPtT9COy9WySLOrwzCYEzOL\r\n3T3dmmrVxlgRMmeIIxyHIjeiK8B2WCEVmU8Q3PXsGyCUkkkY9brpGTT2iSQ1\r\nZO6C4i4UDreN95lNmj7w7qPU3NHXEhhawaRkjNO1bhIa9KwUERXWoEKifN1P\r\nmCPoxSJF6BrNPYezpDMast5sd+ZICWtGkyR5ywXSa5YtB0xN7k31px/qTUNH\r\nQrY9nnZV2BofySxp1cX3qkd93+bbwi2/raJEe8wj1ibt5VjTb9S9b5D1vPwi\r\n3iGWSKEKpAaZ72h/ILNSFQspSdcve5soB7cNZd9TNdOEErfvmrnEddXH4F5t\r\nnmHrLJn7TO258UcoOw4yvu+49wKD53cjmSp7BZEUB5zLam0YPT0CgYDsUjVI\r\nJtvMJzavJ9cF7XkcGN4rayvs4Q6GO7MQeMWnuKVF5tGfoRWK41xBYG9BM4Zg\r\nekGfJUZdAAOZSeNm10u6RUu5gpn+XluWHr6qryfNdbfQd9IMDBe6ZGDwhR86\r\nHiGQwJxJ+Uneo1deAvX6av02EQiCg7xmZXBuseNT/mvEQNPWAOTJGobengFV\r\nOGKdP+sVngyPtAbIa2FhYvrSdSfso0304ewQPtPZNsFpWZ37vMGZrYi3dd65\r\ncgwb9lfssas+m8absY2w15E8sH8IZMI0OiY=\r\n=0VQ3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/src/index-cjs.js", "types": "./dist/cjs/src/index-cjs.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/src/index-cjs.d.ts", "default": "./dist/cjs/src/index-cjs.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "dac1929c23a21a84336882a1c4b2d899358ef9bf", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "deprecated": "Please upgrade to 4.3.1 or higher to fix a potentially damaging issue regarding symbolic link following. See https://github.com/isaacs/rimraf/issues/259 for details.", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "9.3.0", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "19.4.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf_4.1.0_1673915873195_0.15121953919418485", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "<PERSON><PERSON><PERSON>", "version": "4.1.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@4.1.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "dist/cjs/src/bin.js"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "libtap-settings": "libtap-settings.js"}, "dist": {"shasum": "ec29817863e5d82d22bca82f9dc4325be2f1e72b", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-4.1.1.tgz", "fileCount": 107, "integrity": "sha512-Z4Y81w8atcvaJuJuBB88VpADRH66okZAuEm+Jtaufa+s7rZmIz+Hik2G53kGaNytE7lsfXyWktTmfVz0H9xuDg==", "signatures": [{"sig": "MEQCIHaJvdr+fDg36a2kBpJsWsdZv4FgWK+7F/sq0XzjSc0RAiBvLLqxaxsPog3CBYouJQBPjdP2Z3fmIQDHudzg9jHXOQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 145044, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxx7gACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoJWBAAgPMHNmDhubIK4kWkQJF9tFxehgNAkbFpxTmgqfaaTSo+/cs+\r\neOigmiUwXyYuveOiWwsHlK9MCEeV9yAMmwEaSOfnwggZB6Z85QiHz9eyM9qF\r\nuK+3Iq7UnbP1rTEgSJ0t1yEbugDKz/lEH1jHJs/ehcArRsAaeQe7eZavgvs8\r\ntZhPc/nkGh1WiNaf993lu0EEDAaBWdWqCj/tdVqSIytq7vGpxoSXC/kpkXNP\r\nBfTm0FYl0iXXmKrtHDwPu14w9AYAlRZkbFiVaeRNvdXIzGATpvCMuYZGdf+P\r\n0hb14xMWR9R4aXxGMvRFJV98eJgjC9HBV2spk3Vr3ehcXXFiwBCCeFEfKLPA\r\n1cLXqoWHCA1O3Uuw1eAxwDUE8nwx6ofe7bCBc82mbDi0U1cUaBE1fYnXgLym\r\n52341W1PFTqSmpoM+t3OS5TIapgQe98uwFFcw/NMIrLb38gxAmPrPqLEuObn\r\nwbuevwW4sYYe+xvhV/AhzRQ3PFjegN8s9Q+rWs+I7+mxwQxKKB4LgkqOzG8M\r\n89N2zZqJngRMBx3ylrXPc5KN5hPxVsVW/LNkER5noE6KkpLJIvHdDnXCwyhW\r\n/c8nkoJFjPV0n4W1D0IupCAFAIjjUgbEq7VCgWkIa56G3KmvEppTtacjvxGR\r\n3dqXM90qWZltjv0AagJUB5t0r8/AKwXjw4s=\r\n=7aIZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/src/index-cjs.js", "types": "./dist/cjs/src/index-cjs.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/src/index-cjs.d.ts", "default": "./dist/cjs/src/index-cjs.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "1b95def53c00fcbb3e2db05cf1c3ce34d1ee2e51", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "deprecated": "Please upgrade to 4.3.1 or higher to fix a potentially damaging issue regarding symbolic link following. See https://github.com/isaacs/rimraf/issues/259 for details.", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "9.3.0", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "19.4.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf_4.1.1_1673993951991_0.3014326545678079", "host": "s3://npm-registry-packages"}}, "4.1.2": {"name": "<PERSON><PERSON><PERSON>", "version": "4.1.2", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@4.1.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "dist/cjs/src/bin.js"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "libtap-settings": "libtap-settings.js"}, "dist": {"shasum": "20dfbc98083bdfaa28b01183162885ef213dbf7c", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-4.1.2.tgz", "fileCount": 107, "integrity": "sha512-BlIbgFryTbw3Dz6hyoWFhKk+unCcHMSkZGrTFVAx2WmttdBSonsdtRlwiuTbDqTKr+UlXIUqJVS4QT5tUzGENQ==", "signatures": [{"sig": "MEUCIFavYg4XfGoRM/8KwCidvIeIVGdXKZwv73trj1Gl8RGQAiEA5fGopTe5hyq5UwrZXcAP12g1+pf3ExhNsO1H0a8WOqA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 144986, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjz3NwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp29Q//fVwLD+elaE6tDsgkjBGz1Q901mJl+dzbkCZmmH4lYJpnDYTG\r\nVbPzVF0MbioXZXuXl3E9tZ1XhgTfM6OqWqz46eaL5iAMJQEhVCuble2xqGmZ\r\nPYmB3UvcxBLY9jrbdOWZ1EAsJxkKyg1zdPllawmJUc2BE0XsKI3ON6B21zEY\r\nRE9ipvfPyFxL51VEbQpW38ig/Hf9AG7ypQE3CuGrzeGPdFP+qiW90n81uHGl\r\neRxzPi8MJRtuUoW6vzE+4luoeOcDpdixMPNl4vwtHcuCEWKvs9DHUbu3RlsL\r\nW2OGHw4RYtEyeqC5aLoxKuatAgE7KoS9lmdQZrl4qitM6Y9La5CeoZ9Cwnpe\r\nWRNf7exFTpmEibnqEZ/4K2ALAWfYd4+7iRB0+3kfg4Q47q3jJLuv30usUxxG\r\nQ08x6KR1saUn6kEAO+eJmPfIq8CW2VhKRZh/2nhvu/Q3+cSJg7sB16HzD90O\r\nF56HnhPvV5zTQXR7kcXpC1X/PL6trelrpKXacuF5WXZ9QwIv3dfcJRLLtH2I\r\nPIeVnUsdtwd+EdozZQh2piCZ1uvmu6AYewBEqGYvVr0AgxQyyu+0rlExtUXa\r\n1BeG4lhj707XGyk6kXeZR1N7OZZCJL/VDPKT4r8NeYCqVuhjiz/H3Etezcg4\r\ntHWePPR4S8jTZsdIOlFUiSVwfJdY4g8wo9k=\r\n=9SPP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/src/index-cjs.js", "types": "./dist/cjs/src/index-cjs.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/src/index-cjs.d.ts", "default": "./dist/cjs/src/index-cjs.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "df3d08568c74ad3ec7ce953752a916511f7ff6cf", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "deprecated": "Please upgrade to 4.3.1 or higher to fix a potentially damaging issue regarding symbolic link following. See https://github.com/isaacs/rimraf/issues/259 for details.", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "9.3.0", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "19.4.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf_4.1.2_1674539887933_0.07515900509023155", "host": "s3://npm-registry-packages"}}, "4.1.3": {"name": "<PERSON><PERSON><PERSON>", "version": "4.1.3", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rim<PERSON>f@4.1.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "dist/cjs/src/bin.js"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "libtap-settings": "libtap-settings.js"}, "dist": {"shasum": "e8ace19d5f009b4fa6108deeaffe39ef68bba194", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-4.1.3.tgz", "fileCount": 141, "integrity": "sha512-iyzalDLo3l5FZxxaIGUY7xI4Bf90Xt7pCipc1Mr7RsdU7H3538z+M0tlsUDrz0aHeGS9uNqiKHUJyTewwRP91Q==", "signatures": [{"sig": "MEUCIAZ38PorVQNK1B2GC/m9aFVo7JVqITmMnvQh+2b8cOiQAiEAsK9GPD6xXVKUIJeDoItw0jeF4OxX+bmvZRNlTmAnom0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160649, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj/7IoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr2HxAAlgIejTroDm37yVQ1BDhngPOfblanBLkogvp8o1PXioGi20e0\r\nq5Dp1JByvOHhLba2FYyw1kFPtgkKVrCbSFAyRz/jFoBKslrg0lVjb6sCOETL\r\nvo3MIFucZKhohZUS0QLBZ/O+Mxn0PZRWhv7j3jNapJdlStIJyiVCadPfXO52\r\nY8JlHthfec9SYdtnjNbhL21mEQoCkuLLb2WaWuTALPHiuzr9kJkiVhItNI+a\r\nqQE3BChj1hrciIJCMx2rToEWz8sV4AHD47M774nVYb5NLe6xbosaLep5GmAQ\r\nBiTzPw/kZ2gvOCDkOdneD0jDYYI56qil1SKrTneQrFDTFxCQbd9BAVf42tyb\r\nWrfLr5rY+0TtndBlykqYW2VizOPPZ/XZqKuKe3Di3Q3t+Zfq+IsKbW6p5TNG\r\nOJgF1vQwW9OT8cLLI4yt1PSpTmcic/a50xMf/TI+laQk11sWcIDAi4YcSJhJ\r\neiia0t5aFkwiGvMLZz6Et2LnHMBc1/UiW4hdMjkw7mf97cjCCxRnINObL+Gg\r\n4uv5QTwqDOjg1Y+DoPbHmDy1/2npCOdbV+Fo2DKv1QM3tNbiU+bfUI+yHrYt\r\n34Xcu0NiI5iyRkm3m/Nhqke8r8PQAG+IrQoTOU42tZPVR+i3QvTcbjwHfvSv\r\n2dXq3W8mQhz+FikJBTJ2XnFQVvEoGBhxNOc=\r\n=VKA4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/src/index-cjs.js", "types": "./dist/cjs/src/index-cjs.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/src/index-cjs.d.ts", "default": "./dist/cjs/src/index-cjs.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "450e3d2a61d293792ded04c0d53a03afb14927ab", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "deprecated": "Please upgrade to 4.3.1 or higher to fix a potentially damaging issue regarding symbolic link following. See https://github.com/isaacs/rimraf/issues/259 for details.", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "18.14.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf_4.1.3_1677701672022_0.6353076340964654", "host": "s3://npm-registry-packages"}}, "4.1.4": {"name": "<PERSON><PERSON><PERSON>", "version": "4.1.4", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@4.1.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "dist/cjs/src/bin.js"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "libtap-settings": "libtap-settings.js"}, "dist": {"shasum": "f1af3c1278944c549c77157b011ba5e4c083efe4", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-4.1.4.tgz", "fileCount": 141, "integrity": "sha512-CoUTSckPOFgu0m8PzkXWlp/e0IHxxv3fUxOivMspXfxcm1rPaNtj6lkkbi5NnFVoGDFj6kX7jPKVccafEf9HoA==", "signatures": [{"sig": "MEUCIQCS3eZFTOpbUfHIAs5Zmi+l92WWjbA7Ts7Mi01qZNLzEgIgCfQ//FOZjE+dM+W9MnA8xqp7SsslhU0g1xNioYSt6zs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160350, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkAS2EACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoyvQ//VC9hSfo/R4KfgaLGI2YcDz1scHOEOcqNQ+bQ88WAGA2ZZHKJ\r\nvxiG6qRxvKW5aynWc1gNCjHkvS/xmhdoDbA7HQl5anMM1tdGW3elD1ZSbBEt\r\nOchhJOfQG7Fyrklr4Gex83HGGgRylJgYmV7jJ/miUWiAVwMKSKtuStZAuvYa\r\nUYT+f7MA/7kSz6yIoaoXU6RQSdZ23jC7ul0VyiME51vq27csjQsdqujlDlNe\r\nilHjQw+VWUDErXZSmhunZc5hgeTs3GYCdbvIoP/s/hnI5IjMvGdnpm+AqrDe\r\ns5fvVVcx53ogiuMqym1cTPiSJgyK/md6Vj7kv3usiozAuE3S7wt6kp4Vs9dB\r\nQf1d6xjAVOihWQaR10rRacT8ZKJ3XBeGj/ljmtT58O95La0hCfRrJDc7g1y/\r\n7FB3yRKlpVGv+4nfKDs6Msj7ldVFaeH8+DWRTZ2cabRM2jxxSCaVcQeZMqMe\r\ndn5QQeUX5ErBv4CFKM/t5WJQ2HgmJ57Ua83HIS/6hUcg7RR96oOug1BNgs6k\r\nDU/URJ3O2oDejS6DJY4GqhDV9PnCDhIuqdzAw/ypgpquNvpc7XIlUhd8pcYV\r\nmNif6CyfwmUfPfIHgno39q6BYVERqcaPvgYKOhkldoOyES2JIgPTP7MlB/ys\r\nZHu8W14O0vjTKa36QGkToC6nbqJbh3Wtq78=\r\n=NE6J\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/src/index-cjs.js", "types": "./dist/mjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/src/index.d.ts", "default": "./dist/cjs/src/index-cjs.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "417cdc7184b8f80bf1414aca599f543a6e8ffb76", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "deprecated": "Please upgrade to 4.3.1 or higher to fix a potentially damaging issue regarding symbolic link following. See https://github.com/isaacs/rimraf/issues/259 for details.", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "18.14.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf_4.1.4_1677798787739_0.15216131421821322", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "<PERSON><PERSON><PERSON>", "version": "4.2.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@4.2.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "dist/cjs/src/bin.js"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "libtap-settings": "libtap-settings.js"}, "dist": {"shasum": "f9c2a2b121f5133095c5d578983aec534c65f282", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-4.2.0.tgz", "fileCount": 141, "integrity": "sha512-tPt+gLORNVqRCk0NwuJ5SlMEcOGvt4CCU8sUPqgCFtCbnoNCTd9Q6vq7JlBbxQlACiH14OR28y7piA2Bak9Sxw==", "signatures": [{"sig": "MEQCIGvk1qh9KQR4wWqM5K3ewcEm9IVYVVD+eA77ONhVjIENAiAnOCF/OCDn1p3f+qoVVQXacIIPlAosnQvtPQkyrvGkNw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 168547, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkAUAxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoyBQ//SmSmAsBC0EB/4D4pp+F2LeluGCJ/sPnW19e0WEAV95rPs0w7\r\naWGC0oztQ8LE2Z7EvabFe2fJGyCrAiln92bxsC+RrDPjmTgRwAjmW0oVcQ8p\r\nM6iL/xIB5buwFuHSN1iumeJDWUguRlPzmDBgU+89Luv5Eb1vIfibKzXs1BU7\r\nWUzDuB2+/JRDa9jiCwOgFlRWsj8goCWD46o8I5N3i9R6flQOV5Q56QwouX0r\r\nbxwz7PdJxmfaqjlwlPK/MmMNXb8qKBAdKNAEwrG5F9YtqVKRkVKP5IloBPFK\r\nQCsVd9f1tAElpbEFw1aB+OZ5N4LvzMftdHnHDhH+GBZF7/LrnY9J42Bkd0WX\r\nAWR3SatHqLxNAX5uJd2Vg22zvfAY37pFURmvbysfjWnXaR8JBNIPy1CDivPW\r\nCfaJJQCi8Z8b8sYz2WOyKKpuMFBbDaKcNsAZDAq5GPYjOuVd/IiPpzfM8QX9\r\n4LtIdi/vKqj8FQZXg7/Ktj/Gbiynv/5nav5DHZPZycAvNneUQleDWqJDh+y0\r\nc/HfXBCeVU46ap9j1bnW/qxFojK7TcpaUVdq+8WBNLHKeWrLzht2oPPloxVf\r\n/E52Idk6u4Udnr+RS0Q4a6vsDNtfJZ/L3tK0y1yQPOnChroH+sf6k1LKuVhO\r\nm08aNyddZ6BVaVnTSTa6LhNSW+xXXeh235c=\r\n=lU2W\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/src/index-cjs.js", "types": "./dist/mjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/src/index.d.ts", "default": "./dist/cjs/src/index-cjs.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "80aef8b8a1f49a68c7fc8db3cb8d281ecf2bdfaf", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "deprecated": "Please upgrade to 4.3.1 or higher to fix a potentially damaging issue regarding symbolic link following. See https://github.com/isaacs/rimraf/issues/259 for details.", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"glob": "^9.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.3", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf_4.2.0_1677803569228_0.18738758572451308", "host": "s3://npm-registry-packages"}}, "4.3.0": {"name": "<PERSON><PERSON><PERSON>", "version": "4.3.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@4.3.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "dist/cjs/src/bin.js"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "libtap-settings": "libtap-settings.js"}, "dist": {"shasum": "cd4d9a918c1735197e0ae8217e5eeb4657d42154", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-4.3.0.tgz", "fileCount": 141, "integrity": "sha512-5qVDXPbByA1qSJEWMv1qAwKsoS22vVpsL2QyxCKBw4gf6XiFo1K3uRLY6uSOOBFDwnqAZtnbILqWKKlzh8bkGg==", "signatures": [{"sig": "MEYCIQDqS8O5zEDmuXA4QnjBDBCoXpcr5zyewTm+WQt++73IxAIhAId3zqNwRC6ix43z3SJ3ZiqY2p+buLPwINZuoO/FW3jn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191746, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkAp9JACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrD6xAAjmAVbEdiKCTTdbc/+/+ttVEmELgN4ijHFCh2VHsdtIhadOSe\r\nNS9eE6yIOtyh3NdzvXTr733ODBhyVVjOgH36HyFtxyDywFRydti3PHUyiFCp\r\n05SXFd75HNJmLP6D71Y+U5iOBCQ3M2jpL4vB9dzIlJMZkQNd1CW7+Td3DCQ4\r\nWLb0db7tusf/vBXHMNxnsOiP/OYprlwHQgmM+I1tJuhiWE/kf/DdL8DEO6O5\r\nYnAcrmBI2AOQ06eKh/A6yq9QDPHv4P+vtUHk7TnCwKw2SH5c68Vl9qOlIpnU\r\nRl8atibPLFKWh5Cng4lzJwQlXCVO1GLcXvKYBEM7I1B/UTBbNqSLrGqkg3fT\r\nEulIHeZvCWPgATkGf1ThnqHXMqpJKfKgODF49B54DFGx+pUWkAG+Ns8WRyiY\r\nVe1+0vbUu0JwuFAWaNkXa4kNvpbc8TvVQMnExsbk0ejai7tpQuBb2IBxWHbM\r\nnjHISvCw+Z3rjxG2hOPzyiCVTxjrHO/XYzOsmrR6DBRAkroOKNjsR0tbkqxf\r\nNY4QQk7m+7lSOKCpcSlPftGQhkV4snQOeKbvriZ6BRASB82gRe187NlrzzOr\r\n0hbF9YAzrDwynZOH8OU24v9nuzAPny6gXRXrs8i2EvDrjjpToZGsRNsdcofs\r\nA03CSwcTTkoezXwKc6O7aMi7P2GBgPt2wDg=\r\n=+LpR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/src/index-cjs.js", "types": "./dist/mjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/src/index.d.ts", "default": "./dist/cjs/src/index-cjs.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "f923bb054cad6a63e7816eca8247a50b13d10d07", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "deprecated": "Please upgrade to 4.3.1 or higher to fix a potentially damaging issue regarding symbolic link following. See https://github.com/isaacs/rimraf/issues/259 for details.", "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"glob": "^9.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.4", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf_4.3.0_1677893449500_0.5159947364352786", "host": "s3://npm-registry-packages"}}, "4.3.1": {"name": "<PERSON><PERSON><PERSON>", "version": "4.3.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rim<PERSON>f@4.3.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "dist/cjs/src/bin.js"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "libtap-settings": "libtap-settings.js"}, "dist": {"shasum": "ccb3525e39100478acb334fae6d23029b87912ea", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-4.3.1.tgz", "fileCount": 141, "integrity": "sha512-GfHJHBzFQra23IxDzIdBqhOWfbtdgS1/dCHrDy+yvhpoJY5TdwdT28oWaHWfRpKFDLd3GZnGTx6Mlt4+anbsxQ==", "signatures": [{"sig": "MEYCIQCX38SpZHQGyxxBWDKjXN7dnZJJiR7D+SsOyguelKsB6AIhAOsil8FlMB4UyxQhhL2KvIycUY71Uf3+9k0gDnz9yAub", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 206285, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBhcEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqacxAAleTdCn4AuddzqKpZ2/CoBQzwp7Ldutlq1xIT3d+8mNzz5aWf\r\nkHAoHwiHJxX0oD3w5W9dnL7Wi0E/Z5RmphCEOe4GtLGMpNB1r1+Jsdwj0GBp\r\nhJXwB7UcwYva3fYU+QScewS26DzwiD7ExHvIZ6iBWi5EiEessknHDl+VAzJ3\r\nbu8YXb0o6yY3ZsD77U+E+Z46LZ4EtpJuRp4t5gJ3QwQv2VGh9Vdm/NXW82Qf\r\n+NyAQ7MdpjA2EIpkgttF5JvHSsxuTe4Dt/84ZbloxMprioKdPZN1lqXymV3P\r\n17+psbpEKRCJGEcZb6ab3Xp67LWqzDLUMpGuT7W158zf7zJAuuw82aJgdg4p\r\niVIx9hAPNwvCDOu7LN3/S9akEHlsT7GmPkKgs6XfrHZOnAl34XauST/PbBc1\r\na5FwzUAPSLNZXEnmdUf8sn67ihQyvm5c6Yw4VQ1Oqarf6hPXgZQLiWWtw9AZ\r\nyyjYHNaLsdYb1Z+/I42FmRIt0MKaz+3H0XJ6jv2FdrWs3OvP4A6tYfZ5iolf\r\ntedz0jNmvjRepZaTbiA71KNnFZq4ZZlm3OmUYDywThqCtjCP7d7Iw6hSeMaX\r\nCbbRR7vTZ+W3qOMQ7MBdIrf2ECYBL0EfSrcvtzXnfeF+JTO1NODKsKq/vCsc\r\nqxvV0+Q4x/VDLi1LSPoNPfTQcsH9bpGoq+8=\r\n=HJe8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/src/index-cjs.js", "types": "./dist/mjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/src/index.d.ts", "default": "./dist/cjs/src/index-cjs.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "a1268c9829844561c57424ff40e7e2d46e6676e2", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"glob": "^9.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.4", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf_4.3.1_1678120708650_0.6762480976439182", "host": "s3://npm-registry-packages"}}, "4.4.0": {"name": "<PERSON><PERSON><PERSON>", "version": "4.4.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@4.4.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "dist/cjs/src/bin.js"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "libtap-settings": "libtap-settings.js"}, "dist": {"shasum": "c7a9f45bb2ec058d2e60ef9aca5167974313d605", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-4.4.0.tgz", "fileCount": 141, "integrity": "sha512-X36S+qpCUR0HjXlkDe4NAOhS//aHH0Z+h8Ckf2auGJk3PTnx5rLmrHkwNdbVQuCSUhOyFrlRvFEllZOYE+yZGQ==", "signatures": [{"sig": "MEUCIGA0wcOhg6oTZRmf7YzoaoBwMvIZ9SxrgyEYZBgdIJbSAiEA/P00nlCEwgFZJ5DS2f0dbcBXKXi9K/ke5aR5xW2NHL8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 207415, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkCR9rACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqiyQ//bAmb0bghURN4LtZCg3BhGHWSgaJxv8/YRZlmC5TiWUYNZpbN\r\nKWeyQHzQS7B2uhaLWStJogaFi498sQQ9W62fTydnz/9BvLBajZ9LrrCVFAVO\r\ncfjPKYYpduwAxldCWHE63S+qGLebXRep2ZjN9k61vRdKTGHFS4bdVRMlBZtU\r\nD3xKVv9D1GTAoOltbO5mya+LpRSG+UJBIC/HyoXtZkbecSItbs3yHGuV3B9W\r\nKuL9dmrXpb5tVaxlA9MpIZF/TZ4KSemfv+5XbeQXdka8PHKSu6q/HrbMgO3u\r\nAy3QPWJh/Sk2FZLKL/Y5VTcgeRrmWl/x9lpBd/TikBPb1m+PfmskgBYGuBjR\r\nI3gmUJ0002X1oqRFgf6riiPmoYvXfF81/zDajyXRAshNRyhfxVHeFbk8fGys\r\nFyeRIarLomIrbQq7Dh39sXXFmZzKj4NPPQfBqkQH8Z62q/jtM1vZFrhM93Fq\r\n05RvPn2xsPwFa81jJ2jLF48hpqj96+xyWFoKqxTK2ieuK91VarT90SM9uqvq\r\nIBQ5ftGgNewym7NwRSiL16LRTXpNZmVTnqKcolebhsumtW8v3tLHeYf0Au1V\r\ncVv/9IFJn9Pfx93v0y5GTu3shfTct9d9fRb6c4Arkn+pJY78iJ81UjvoYUlk\r\na2CjVFhgAcz5ImS3idg8d5PJUqU8GjHrSlQ=\r\n=Xnkq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/src/index-cjs.js", "types": "./dist/mjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/src/index.d.ts", "default": "./dist/cjs/src/index-cjs.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "cd0b6f485b6334fb7ee45d3af716be7105293296", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"glob": "^9.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.4", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf_4.4.0_1678319467225_0.34879194538838165", "host": "s3://npm-registry-packages"}}, "4.4.1": {"name": "<PERSON><PERSON><PERSON>", "version": "4.4.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@4.4.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "dist/cjs/src/bin.js"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "libtap-settings": "libtap-settings.js"}, "dist": {"shasum": "bd33364f67021c5b79e93d7f4fa0568c7c21b755", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-4.4.1.tgz", "fileCount": 141, "integrity": "sha512-Gk8NlF062+T9CqNGn6h4tls3k6T1+/nXdOcSZVikNVtlRdYpA7wRJJMoXmuvOnLW844rPjdQ7JgXCYM6PPC/og==", "signatures": [{"sig": "MEUCIQDzZclUOxOGbLvr6IQZgG4jXQbu7KaxmahAQZ7P2KE6tQIgCGNs5+cNS+8l6Ze45vULhmMMFSsudtRQ8cOgEvKvcX4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 284076, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkG04dACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoI2BAAhncIGMliNw2iAAMiZYiYwVXGyu77btj1s+4A92b9UmFFXyly\r\n0gMZ/zdd0NEDmT2ODzeqhN9qY8NRrtyzkWF6qw3Uo3GXbezQ4fN5Jz9WRJBW\r\na2Z5+VsLtznC7AXN0efOWbnpMkw8wqZjWS8eegMR91zFyLkmtIfQmz84wOzy\r\nIW/ZGJYG4ZdmwR/y7R7+L7aPgKXrU0cv96AwAmEHQK4F1i3xHzE7e+JCMOPt\r\nZfySsrWuG2oIr21Tf4CoeLc3MOjduoY8ACOPB+cFazo27LjX+Fcq4GNnB2jb\r\nn2F6KXSU1H/Up2x48sNnsz/pD8LQL4Gx/JfDD2F2o8AEOfI11RgI8zn78UkZ\r\noV30LVS4DZhvWJeJ6VOP2AWoSCgymjs4MtSB/lgIiarLNk6hwNxdFmTyMWHa\r\n5RnyJhI1llRvS/L/E7baRgFUV/9QcGdEAtRq6D3ASiQ1j8vwYMd4KPkH7cGt\r\nSD5+gHdNVGZJWevb+KQ92tPt6WtAJ+xeHW6zUQF0Tx8GyRUJpzHzsogDMnFi\r\n/KE4a/Xflt8a5Oub/t72fq4dTq8GW1dVWHz1yLUcK7F9vqae7HKjey2gxJjL\r\nhHRpI5Ogtdnln7FigJcN54n/1k3EPFqONE+KaOvA2s/eoPjtltomYX/hg4Uq\r\nimSupiPm0+sNWiJwjSofFQCyzm3SmbTaGJ0=\r\n=Bdkt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/src/index-cjs.js", "types": "./dist/mjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/src/index.d.ts", "default": "./dist/cjs/src/index-cjs.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "04bcedfd7bb8d39e657d513cc6542758db6dc130", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"glob": "^9.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.4", "mkdirp": "1", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^4.9.3", "@types/node": "^18.11.9", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf_4.4.1_1679511068946_0.7184476861795324", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "<PERSON><PERSON><PERSON>", "version": "5.0.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@5.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "dist/cjs/src/bin.js"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "libtap-settings": "libtap-settings.js"}, "dist": {"shasum": "5bda14e410d7e4dd522154891395802ce032c2cb", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-5.0.0.tgz", "fileCount": 137, "integrity": "sha512-Jf9llaP+RvaEVS5nPShYFhtXIrb3LRKP281ib3So0KkeZKo2wIKyq0Re7TOSwanasA423PSr6CCIL4bP6T040g==", "signatures": [{"sig": "MEQCIACkfwHgsgJNbA8Hm+azHrDBl9TM3zgkJzFuhtJlNzlxAiBi2mzXuz205mjQ55OnATBjsnh0aVoJcY7fymCZ5pXe0Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 277335, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkMz8NACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqzIg//S368eRuh9f9mrWyF5D7ZRz7xV1J9s+weCsAeBiSJa4fZqy1O\r\nfoktjJNY1Cr2hOHHnlonepaEefC57ZGkY8VrGzqhcECDWdp1UVzrn4vVe3Mt\r\nX6juPLlHKXKVT0oR6jMmqo08pBZ/1g8Y3Ro9FhOYLmK4sgMkkdSaXYHe+pnB\r\nXOD3mYz4t+UbaysE7B69Yb9mYhjW6+I9DUOWmHH6GJ9p9UgNZEGJ1Wt/v1fq\r\ns3rcyQExl0ET6FGwxQz1EfZM3QptBGZkpdcGC0ojZKGMZSxEYelcgjqf22We\r\njZ4FtEhu4uTDyKjRt/vORx6M66AKUDf+SjbmVawKKSwzRqcoG2AlIyFUuAaC\r\n/v9+M5ndxJ5ZrP/zP5ztfVNhAThYld42NNwPsKn5j5QlHAUeRj1RMiDEUrBy\r\nwBzpWRH2VRchquj1Kj8mgWfwji2vQ+RAfBnLDtgXSMw4LRH+XIOMbxB6ZFhj\r\nj/X2ScZRQ62H5/ie9qFUbaiEwzYLnAZglIt45h0teMKNprdqyctkI5567l4e\r\nq3qKSG4DE/l5Y8i55tvv6X4JMuNzcPbUHsLq521S2aO41t5o46gFn1L612VD\r\nzTpOnFua/iOi8M5tGdks4G9GmEfUY0nEEG9szchlbRmEIlAigBmkrX8M1dJK\r\neuH0QUO0C7gM5gSr5AinfYQMrMXd6a+l4YM=\r\n=vpq3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/src/index.js", "types": "./dist/mjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/src/index.d.ts", "default": "./dist/cjs/src/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "98ee1e9e5784f776b71df27b73058afb026613f4", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "9.6.4", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"glob": "^10.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.4", "mkdirp": "^3.0.0", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^5.0.4", "@types/node": "^18.11.9", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf_5.0.0_1681080077211_0.6502161478520951", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "<PERSON><PERSON><PERSON>", "version": "5.0.1", "keywords": ["rm", "rm -rf", "rm -fr", "remove", "directory", "cli", "rmdir", "recursive"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@5.0.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "dist/cjs/src/bin.js"}, "tap": {"ts": false, "coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "libtap-settings": "libtap-settings.js"}, "dist": {"shasum": "0881323ab94ad45fec7c0221f27ea1a142f3f0d0", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-5.0.1.tgz", "fileCount": 137, "integrity": "sha512-OfFZdwtd3lZ+XZzYP/6gTACubwFcHdLRqS9UX3UwpU2dnGQYkPFISRwvM3w9IiB2w7bW5qGo/uAwE4SmXXSKvg==", "signatures": [{"sig": "MEUCIQCBED0MBdcLD//RcpvYMQ9KD0PHfHlwiFgUpQ0o2TccPgIgS5bheT0okQ6enzNetBkgdJkYX73Ofnoh3lKSeAFtagE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 277931}, "main": "./dist/cjs/src/index.js", "types": "./dist/mjs/index.d.ts", "module": "./dist/mjs/index.js", "engines": {"node": ">=14"}, "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/src/index.d.ts", "default": "./dist/cjs/src/index.js"}}}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "bfda548deddff84ca2b7ced40675b1b2648ee9bb", "scripts": {"snap": "c8 tap", "test": "c8 tap", "format": "prettier --write . --loglevel warn", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preprepare": "rm -rf dist", "preversion": "npm test", "postprepare": "bash fixup.sh", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "9.6.5", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"glob": "^10.2.5"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "tap": "^16.3.4", "mkdirp": "^3.0.0", "ts-node": "^10.9.1", "typedoc": "^0.23.21", "prettier": "^2.8.2", "@types/tap": "^15.0.7", "typescript": "^5.0.4", "@types/node": "^18.11.9", "eslint-config-prettier": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf_5.0.1_1684361005480_0.10550444339663678", "host": "s3://npm-registry-packages"}}, "5.0.2": {"name": "<PERSON><PERSON><PERSON>", "version": "5.0.2", "keywords": ["rm", "rm -rf", "rm -fr", "remove", "directory", "cli", "rmdir", "recursive"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@5.0.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "dist/esm/bin.mjs"}, "dist": {"shasum": "4c3f2937763b9cb4361a68e026a618969c9d102a", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-5.0.2.tgz", "fileCount": 137, "integrity": "sha512-SeHT0IRkQNIhWL7O5qrCt8MfJagJ2ZOemGMIx2NXS7MP1GldYpWSw3mCLbnjA0Ac6eadZMcDFZjynCWGJmwO6A==", "signatures": [{"sig": "MEUCIQD8gCDsWCYVDRiCM5iVUf7/UC/4O1mfFmP31XjoTarj3gIgIfUgXQhoKyJF8geFufFZCfoJIqYRqsSfV9kfed/clac=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 277423}, "tshy": {"exports": {".": "./src/index.ts", "./package.json": "./package.json"}}, "type": "module", "engines": {"node": ">=14"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "6297992df2ca8ecf07d2b6b4863e2bff73e2f10c", "scripts": {"snap": "tap", "test": "tap", "format": "prettier --write . --loglevel warn", "prepare": "tshy", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "20.7.0", "dependencies": {"glob": "^10.3.7"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^18.1.4", "tshy": "^1.1.1", "mkdirp": "^3.0.1", "typedoc": "^0.25.1", "prettier": "^2.8.2", "typescript": "^5.2", "@types/node": "^20.6.5"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf_5.0.2_1695674209094_0.6172596907515298", "host": "s3://npm-registry-packages"}}, "5.0.3": {"name": "<PERSON><PERSON><PERSON>", "version": "5.0.3", "keywords": ["rm", "rm -rf", "rm -fr", "remove", "directory", "cli", "rmdir", "recursive"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@5.0.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "dist/esm/bin.mjs"}, "dist": {"shasum": "e0a31c3f818de7c3592067e0ffb53dfc221454fa", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-5.0.3.tgz", "fileCount": 137, "integrity": "sha512-8BvsxqiTk8MEW6egd3oshF0e31BpiON1m/drw6nnn5AsRTAXDapBMENd2pf4ehjLJBlf9saQ+uMeBUfrRIHxvw==", "signatures": [{"sig": "MEYCIQCPnqKfPpk1189+JZLh/G6uYczUx3ohXgm94Z47WwA6OQIhAIWPoggOKIMx55Lt+ACe4mk60xXxi6UeBQ/JcyYFzYdg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 277665}, "tshy": {"exports": {".": "./src/index.ts", "./package.json": "./package.json"}}, "type": "module", "engines": {"node": ">=14"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "15656aa0fae2b4899680624a5d962a66b19b4743", "scripts": {"snap": "tap", "test": "tap", "format": "prettier --write . --loglevel warn", "prepare": "tshy", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "20.7.0", "dependencies": {"glob": "^10.3.7"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^18.1.4", "tshy": "^1.1.1", "mkdirp": "^3.0.1", "typedoc": "^0.25.1", "prettier": "^2.8.2", "typescript": "^5.2", "@types/node": "^20.6.5"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf_5.0.3_1695680055721_0.30909367667190035", "host": "s3://npm-registry-packages"}}, "5.0.4": {"name": "<PERSON><PERSON><PERSON>", "version": "5.0.4", "keywords": ["rm", "rm -rf", "rm -fr", "remove", "directory", "cli", "rmdir", "recursive"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@5.0.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "dist/esm/bin.mjs"}, "dist": {"shasum": "2a9b79e2fa5218c1d286095b4e0405cfc5f1064e", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-5.0.4.tgz", "fileCount": 137, "integrity": "sha512-rizQI/o/YAMM1us0Zyax0uRfIK39XR52EAjjOi0fzMolpGp0onj6CWzBAXuOx6+6Xi9Rgi0d9tUZojhJerLUmQ==", "signatures": [{"sig": "MEQCIDRYvFdcc8OqIbeXIHxn0DJ5uk0b0Q5e7bpFyJXHYn34AiBCNPujBoir2I303b/UFfsi2JRlBVp1uEr+OOabHEs9RQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 277268}, "tshy": {"exports": {".": "./src/index.ts", "./package.json": "./package.json"}}, "type": "module", "engines": {"node": ">=14"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "ffc44d3a0b9bd6be386f08bed6ad559c0f0f75ee", "scripts": {"snap": "tap", "test": "tap", "format": "prettier --write . --loglevel warn", "prepare": "tshy", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "20.7.0", "dependencies": {"glob": "^10.3.7"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^18.1.4", "tshy": "^1.1.1", "mkdirp": "^3.0.1", "typedoc": "^0.25.1", "prettier": "^2.8.2", "typescript": "^5.2", "@types/node": "^20.6.5"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf_5.0.4_1695680666405_0.020557058964562502", "host": "s3://npm-registry-packages"}}, "5.0.5": {"name": "<PERSON><PERSON><PERSON>", "version": "5.0.5", "keywords": ["rm", "rm -rf", "rm -fr", "remove", "directory", "cli", "rmdir", "recursive"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@5.0.5", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "dist/esm/bin.mjs"}, "dist": {"shasum": "9be65d2d6e683447d2e9013da2bf451139a61ccf", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-5.0.5.tgz", "fileCount": 137, "integrity": "sha512-CqDakW+hMe/Bz202FPEymy68P+G50RfMQK+Qo5YUqc9SPipvbGjCGKd0RSKEelbsfQuw3g5NZDSrlZZAJurH1A==", "signatures": [{"sig": "MEQCIAPt58j9hwFf4hWqRZ0DhUVu36P44mcXJYCFn67XQWlcAiBZVkZhfBMjorPpMWgdZqOTWzPuSZPc5v5krcCRmrKAVA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 277365}, "main": "./dist/commonjs/index.js", "tshy": {"main": true, "exports": {".": "./src/index.ts", "./package.json": "./package.json"}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "engines": {"node": ">=14"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "3cc9222c646393713f671b807731f2d70f554850", "scripts": {"snap": "tap", "test": "tap", "format": "prettier --write . --loglevel warn", "prepare": "tshy", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "20.7.0", "dependencies": {"glob": "^10.3.7"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^18.1.4", "tshy": "^1.2.2", "mkdirp": "^3.0.1", "typedoc": "^0.25.1", "prettier": "^2.8.2", "typescript": "^5.2", "@types/node": "^20.6.5"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf_5.0.5_1695794647472_0.6790867633008759", "host": "s3://npm-registry-packages"}}, "5.0.6": {"name": "<PERSON><PERSON><PERSON>", "version": "5.0.6", "keywords": ["rm", "rm -rf", "rm -fr", "remove", "directory", "cli", "rmdir", "recursive"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@5.0.6", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "dist/esm/bin.mjs"}, "dist": {"shasum": "f13b90af1794a2e8e1ecd052263bcf688742af88", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-5.0.6.tgz", "fileCount": 137, "integrity": "sha512-X72SgyOf+1lFnGM6gYcmZ4+jMOwuT4E4SajKQzUIlI7EoR5eFHMhS/wf8Ll0mN+w2bxcIVldrJQ6xT7HFQywjg==", "signatures": [{"sig": "MEQCIGwhQUbuKkdnmuIgFPPfxy1mcVr7J4vyam2eEDpH0LQHAiAUzj/zoibXagGC3xgCcpcmJ9C/+SK5m2wrrKBRtc7lhA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 281213}, "main": "./dist/commonjs/index.js", "tshy": {"main": true, "exports": {".": "./src/index.ts", "./package.json": "./package.json"}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "engines": {"node": ">=14"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "e729f762d66a30c63bf4d3f63bcf905e1faa543b", "scripts": {"snap": "tap", "test": "tap", "format": "prettier --write . --log-level warn", "prepare": "tshy", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "experimentalTernaries": true}, "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "20.11.0", "dependencies": {"glob": "^10.3.7"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^18.5.4", "tshy": "^1.14.0", "mkdirp": "^3.0.1", "typedoc": "^0.25.13", "prettier": "^3.2.5", "typescript": "^5.4.5", "@types/node": "^20.12.11"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf_5.0.6_1715361201579_0.14350051746857106", "host": "s3://npm-registry-packages"}}, "5.0.7": {"name": "<PERSON><PERSON><PERSON>", "version": "5.0.7", "keywords": ["rm", "rm -rf", "rm -fr", "remove", "directory", "cli", "rmdir", "recursive"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@5.0.7", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "dist/esm/bin.mjs"}, "dist": {"shasum": "27bddf202e7d89cb2e0381656380d1734a854a74", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-5.0.7.tgz", "fileCount": 137, "integrity": "sha512-nV6YcJo5wbLW77m+8KjH8aB/7/rxQy9SZ0HY5shnwULfS+9nmTtVXAJET5NdZmCzA4fPI/Hm1wo/Po/4mopOdg==", "signatures": [{"sig": "MEYCIQDL9nDrIcf4CO/5CvlBi+57q9yk+yartU0X+WoCdg6B4QIhAKf5h6n40V62YU1fUss73vrp3bo4Bk+oUMtZ95OopC9X", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 281216}, "main": "./dist/commonjs/index.js", "tshy": {"main": true, "exports": {".": "./src/index.ts", "./package.json": "./package.json"}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "engines": {"node": ">=14.18"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "fb974adeed36bfc4d41a601f60a751aa509dbe2a", "scripts": {"snap": "tap", "test": "tap", "format": "prettier --write . --log-level warn", "prepare": "tshy", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "experimentalTernaries": true}, "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "20.11.0", "dependencies": {"glob": "^10.3.7"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^18.5.4", "tshy": "^1.14.0", "mkdirp": "^3.0.1", "typedoc": "^0.25.13", "prettier": "^3.2.5", "typescript": "^5.4.5", "@types/node": "^20.12.11"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf_5.0.7_1715482104380_0.10721769419799787", "host": "s3://npm-registry-packages"}}, "5.0.8": {"name": "<PERSON><PERSON><PERSON>", "version": "5.0.8", "keywords": ["rm", "rm -rf", "rm -fr", "remove", "directory", "cli", "rmdir", "recursive"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@5.0.8", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "dist/esm/bin.mjs"}, "dist": {"shasum": "9d4d0ef5106817102b14fdbbf01cf29545e99a6c", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-5.0.8.tgz", "fileCount": 137, "integrity": "sha512-XSh0V2/yNhDEi8HwdIefD8MLgs4LQXPag/nEJWs3YUc3Upn+UHa1GyIkEg9xSSNt7HnkO5FjTvmcRzgf+8UZuw==", "signatures": [{"sig": "MEUCIEFd9vxDEAyhMHthJ7yYQYb6FMIGRXj2789+FPDgfl8uAiEAvLqGbxsGoerlQO6NDAq/wiR7haAEiteepSdmAekP2fw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 281205}, "main": "./dist/commonjs/index.js", "tshy": {"main": true, "exports": {".": "./src/index.ts", "./package.json": "./package.json"}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "0d83c12b15f2617f10deb6665d63e50a4512b967", "scripts": {"snap": "tap", "test": "tap", "format": "prettier --write . --log-level warn", "prepare": "tshy", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "experimentalTernaries": true}, "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "20.13.1", "dependencies": {"glob": "^10.3.7"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^19.0.1", "tshy": "^1.14.0", "mkdirp": "^3.0.1", "typedoc": "^0.25.13", "prettier": "^3.2.5", "typescript": "^5.4.5", "@types/node": "^20.12.11"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf_5.0.8_1720239378090_0.7776440121657209", "host": "s3://npm-registry-packages"}}, "5.0.9": {"name": "<PERSON><PERSON><PERSON>", "version": "5.0.9", "keywords": ["rm", "rm -rf", "rm -fr", "remove", "directory", "cli", "rmdir", "recursive"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@5.0.9", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "dist/esm/bin.mjs"}, "dist": {"shasum": "c3baa1b886eadc2ec7981a06a593c3d01134ffe9", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-5.0.9.tgz", "fileCount": 137, "integrity": "sha512-3i7b8OcswU6CpU8Ej89quJD4O98id7TtVM5U4Mybh84zQXdrFmDLouWBEEaD/QfO3gDDfH+AGFCGsR7kngzQnA==", "signatures": [{"sig": "MEUCIBmIwMXtfLe6DWF/O6NwBBI84/OnzNhzRUUPv7tkJd71AiEA206lUzHDfRM+p4dUTyjE4c5/JVBGpwa+EDUpL4eAgp8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 281233}, "main": "./dist/commonjs/index.js", "tshy": {"main": true, "exports": {".": "./src/index.ts", "./package.json": "./package.json"}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "engines": {"node": "14 >=14.20 || 16 >=16.20 || >=18"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "546c9ec50c0e94ac08af7188b02fc1dddfc5134e", "scripts": {"snap": "tap", "test": "tap", "format": "prettier --write . --log-level warn", "prepare": "tshy", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "experimentalTernaries": true}, "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "20.13.1", "dependencies": {"glob": "^10.3.7"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^19.0.1", "tshy": "^1.14.0", "mkdirp": "^3.0.1", "typedoc": "^0.25.13", "prettier": "^3.2.5", "typescript": "^5.4.5", "@types/node": "^20.12.11"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf_5.0.9_1720477138853_0.6562174729503563", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "<PERSON><PERSON><PERSON>", "version": "6.0.0", "keywords": ["rm", "rm -rf", "rm -fr", "remove", "directory", "cli", "rmdir", "recursive"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@6.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "dist/esm/bin.mjs"}, "dist": {"shasum": "503bb3d9283272384c121792d40e7ee3ab763cde", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-6.0.0.tgz", "fileCount": 137, "integrity": "sha512-u+yqhM92LW+89cxUQK0SRyvXYQmyuKHx0jkx4W7KfwLGLqJnQM5031Uv1trE4gB9XEXBM/s6MxKlfW95IidqaA==", "signatures": [{"sig": "MEYCIQDzPQEgn6cuEsHI/VHqSiiDSt4TilCw799jYxywX0t0xAIhAOk/xh/Tad0QkZhCu1U99OnIQ1UX7UZFrVE0EvkC98fx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 281308}, "main": "./dist/commonjs/index.js", "tshy": {"main": true, "exports": {".": "./src/index.ts", "./package.json": "./package.json"}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": "20 || >=22"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "90d1feb00fde6050fd04855540af3eb557257bf5", "scripts": {"snap": "tap", "test": "tap", "format": "prettier --write . --log-level warn", "prepare": "tshy", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "experimentalTernaries": true}, "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "20.13.1", "dependencies": {"glob": "^11.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^20.0.3", "tshy": "^2.0.1", "mkdirp": "^3.0.1", "typedoc": "^0.26.3", "prettier": "^3.3.2", "@types/node": "^20.14.10"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf_6.0.0_1720477277730_0.5897714892326156", "host": "s3://npm-registry-packages"}}, "6.0.1": {"name": "<PERSON><PERSON><PERSON>", "version": "6.0.1", "keywords": ["rm", "rm -rf", "rm -fr", "remove", "directory", "cli", "rmdir", "recursive"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@6.0.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "dist/esm/bin.mjs"}, "dist": {"shasum": "ffb8ad8844dd60332ab15f52bc104bc3ed71ea4e", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-6.0.1.tgz", "fileCount": 137, "integrity": "sha512-9dkvaxAsk/xNXSJzMgFqqMCuFgt2+KsOFek3TMLfo8NCPfWpBmqwyNn5Y+NX56QUYfCtsyhF3ayiboEoUmJk/A==", "signatures": [{"sig": "MEUCIQDzBq0/3uZSpXexk+KcneXvM+raPRWu8b2r/KXyPYPy5AIgdyAlu/6xnZkjQslWqDOdv7A6JbWE4lay1G9rmvHhX68=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 281212}, "main": "./dist/commonjs/index.js", "tshy": {"main": true, "exports": {".": "./src/index.ts", "./package.json": "./package.json"}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": "20 || >=22"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "21560c7cdb46c039fccfd3f5fb4218946489881a", "scripts": {"snap": "tap", "test": "tap", "format": "prettier --write . --log-level warn", "prepare": "tshy", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "experimentalTernaries": true}, "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "20.13.1", "dependencies": {"glob": "^11.0.0", "package-json-from-dist": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^20.0.3", "tshy": "^2.0.1", "mkdirp": "^3.0.1", "typedoc": "^0.26.3", "prettier": "^3.3.2", "@types/node": "^20.14.10"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf_6.0.1_1720631574422_0.3530426636852977", "host": "s3://npm-registry-packages"}}, "5.0.10": {"name": "<PERSON><PERSON><PERSON>", "version": "5.0.10", "keywords": ["rm", "rm -rf", "rm -fr", "remove", "directory", "cli", "rmdir", "recursive"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "rimraf@5.0.10", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/rimraf#readme", "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "bin": {"rimraf": "dist/esm/bin.mjs"}, "dist": {"shasum": "23b9843d3dc92db71f96e1a2ce92e39fd2a8221c", "tarball": "https://registry.npmjs.org/rimraf/-/rimraf-5.0.10.tgz", "fileCount": 137, "integrity": "sha512-l0OE8wL34P4nJH/H2ffoaniAokM2qSmrtXHmlpvYr5AVVX8msAyW0l8NVJFDxlSK4u3Uh/f41cQheDVdnYijwQ==", "signatures": [{"sig": "MEYCIQCXe67RZfcIGc3biaiAmNKVlAi/0jXV1sve5ozElbq/YwIhAKqFdsXDk4aueo6raQJAQTxlZHwUOMJSuSUzLyAT/xyP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 281343}, "main": "./dist/commonjs/index.js", "tshy": {"main": true, "exports": {".": "./src/index.ts", "./package.json": "./package.json"}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "module": "./dist/esm/index.js", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "gitHead": "982faa77dc2398f5e4e1daa9a8f35d3bb82168c0", "scripts": {"snap": "tap", "test": "tap", "format": "prettier --write . --log-level warn", "prepare": "tshy", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts", "benchmark": "node benchmark/index.js", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 80, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "experimentalTernaries": true}, "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A deep deletion module for node (like `rm -rf`)", "directories": {}, "_nodeVersion": "20.13.1", "dependencies": {"glob": "^10.3.7"}, "publishConfig": {"tag": "v5-legacy"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^19.0.1", "tshy": "^1.14.0", "mkdirp": "^3.0.1", "typedoc": "^0.25.13", "prettier": "^3.2.5", "typescript": "^5.4.5", "@types/node": "^20.12.11"}, "_npmOperationalInternal": {"tmp": "tmp/rimraf_5.0.10_1722453953304_0.5738010333698311", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2011-02-08T08:55:37.756Z", "modified": "2024-10-22T17:26:12.673Z", "1.0.0": "2011-02-08T08:55:39.437Z", "1.0.1": "2011-04-13T18:06:00.422Z", "1.0.2": "2011-05-30T18:33:02.093Z", "1.0.4": "2011-08-07T03:59:00.634Z", "1.0.5": "2011-09-03T00:20:05.595Z", "1.0.6": "2011-09-03T00:30:43.110Z", "1.0.7": "2011-09-25T00:26:39.319Z", "1.0.8": "2011-10-07T18:25:44.535Z", "1.0.9": "2011-12-03T16:52:51.833Z", "2.0.0": "2012-01-30T02:20:45.661Z", "2.0.1": "2012-01-30T17:07:51.384Z", "2.0.2": "2012-06-08T21:19:19.066Z", "2.0.3": "2012-12-15T22:59:57.912Z", "2.1.0": "2012-12-20T23:23:50.937Z", "2.1.1": "2012-12-21T02:23:38.182Z", "2.1.2": "2013-01-22T20:50:45.534Z", "2.1.3": "2013-02-06T00:24:04.069Z", "2.1.4": "2013-02-06T14:58:43.510Z", "2.2.0": "2013-06-21T14:52:14.555Z", "2.2.1": "2013-07-14T18:48:37.747Z", "2.2.2": "2013-07-22T16:20:26.910Z", "2.2.3": "2013-11-29T16:55:07.432Z", "2.2.4": "2013-11-29T16:59:28.538Z", "2.2.5": "2013-12-09T17:05:49.993Z", "2.2.6": "2014-01-15T23:58:31.660Z", "2.2.7": "2014-05-05T22:17:46.141Z", "2.2.8": "2014-05-06T16:00:05.895Z", "2.3.0": "2015-03-03T23:25:07.298Z", "2.3.1": "2015-03-05T02:37:34.817Z", "2.3.2": "2015-03-10T01:13:34.964Z", "2.3.3": "2015-04-30T14:18:05.546Z", "2.3.4": "2015-05-19T01:37:39.894Z", "2.4.0": "2015-06-07T17:20:09.158Z", "2.4.1": "2015-06-30T18:02:24.393Z", "2.4.2": "2015-07-19T21:44:13.360Z", "2.4.3": "2015-08-26T22:30:40.696Z", "2.4.4": "2015-11-20T00:41:43.301Z", "2.4.5": "2015-12-22T19:25:39.476Z", "2.5.0": "2015-12-23T18:42:32.610Z", "2.5.1": "2016-01-22T23:48:00.013Z", "2.5.2": "2016-02-13T06:55:01.448Z", "2.5.3": "2016-07-03T21:55:18.448Z", "2.5.4": "2016-07-22T17:02:24.805Z", "2.6.0": "2017-02-18T22:01:39.377Z", "2.6.1": "2017-02-24T03:47:56.218Z", "2.6.2": "2017-09-11T16:46:07.098Z", "2.6.3": "2019-01-02T19:26:12.834Z", "2.7.0": "2019-08-14T00:04:06.905Z", "2.7.1": "2019-08-14T16:53:32.844Z", "3.0.0": "2019-08-14T18:12:42.601Z", "3.0.1": "2020-01-28T03:44:35.076Z", "3.0.2": "2020-02-09T06:18:37.504Z", "4.0.0": "2023-01-13T00:29:41.802Z", "4.0.1": "2023-01-13T00:54:05.169Z", "4.0.2": "2023-01-13T01:32:06.253Z", "4.0.3": "2023-01-13T01:37:14.720Z", "4.0.4": "2023-01-13T01:41:09.677Z", "4.0.5": "2023-01-14T18:27:32.889Z", "4.0.6": "2023-01-15T17:43:12.968Z", "4.0.7": "2023-01-15T21:26:51.362Z", "4.1.0": "2023-01-17T00:37:53.415Z", "4.1.1": "2023-01-17T22:19:12.155Z", "4.1.2": "2023-01-24T05:58:08.125Z", "4.1.3": "2023-03-01T20:14:32.250Z", "4.1.4": "2023-03-02T23:13:07.965Z", "4.2.0": "2023-03-03T00:32:49.423Z", "4.3.0": "2023-03-04T01:30:49.691Z", "4.3.1": "2023-03-06T16:38:28.918Z", "4.4.0": "2023-03-08T23:51:07.468Z", "4.4.1": "2023-03-22T18:51:09.123Z", "5.0.0": "2023-04-09T22:41:17.381Z", "5.0.1": "2023-05-17T22:03:25.693Z", "5.0.2": "2023-09-25T20:36:49.428Z", "5.0.3": "2023-09-25T22:14:15.900Z", "5.0.4": "2023-09-25T22:24:26.693Z", "5.0.5": "2023-09-27T06:04:07.772Z", "5.0.6": "2024-05-10T17:13:21.898Z", "5.0.7": "2024-05-12T02:48:24.568Z", "5.0.8": "2024-07-06T04:16:18.294Z", "5.0.9": "2024-07-08T22:18:59.056Z", "6.0.0": "2024-07-08T22:21:17.952Z", "6.0.1": "2024-07-10T17:12:54.614Z", "5.0.10": "2024-07-31T19:25:53.666Z"}, "bugs": {"url": "https://github.com/isaacs/rimraf/issues"}, "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "homepage": "https://github.com/isaacs/rimraf#readme", "keywords": ["rm", "rm -rf", "rm -fr", "remove", "directory", "cli", "rmdir", "recursive"], "repository": {"url": "git://github.com/isaacs/rimraf.git", "type": "git"}, "description": "A deep deletion module for node (like `rm -rf`)", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "readme": "", "readmeFilename": "", "users": {"285858315": true, "ash": true, "dm7": true, "nfd": true, "pid": true, "a1ip": true, "bcoe": true, "btoo": true, "dwqs": true, "fill": true, "jits": true, "kmck": true, "lmhs": true, "tztz": true, "usex": true, "xlsu": true, "z1c0": true, "akiva": true, "bengi": true, "ehrig": true, "etsit": true, "jream": true, "jruif": true, "junos": true, "laomu": true, "molid": true, "octoo": true, "sam16": true, "samar": true, "thotk": true, "weerd": true, "yikuo": true, "youcp": true, "ackhub": true, "agplan": true, "aicest": true, "ajduke": true, "attl8d": true, "barzim": true, "bojand": true, "bpatel": true, "broofa": true, "caesor": true, "chicho": true, "d-band": true, "daizch": true, "dankle": true, "finico": true, "gdbtek": true, "glebec": true, "hsiang": true, "iotale": true, "isyara": true, "jaeger": true, "jcmark": true, "jimnox": true, "johnym": true, "jslite": true, "kudakv": true, "landen": true, "lianer": true, "lomocc": true, "lore-w": true, "manten": true, "mattms": true, "mikkoh": true, "monjer": true, "mrbgit": true, "mrdain": true, "narven": true, "netweb": true, "noyobo": true, "nuwaio": true, "pstoev": true, "rwhogg": true, "sensui": true, "smtnkc": true, "tedyhy": true, "uipoet": true, "yeming": true, "yoking": true, "yong_a": true, "acbde45": true, "alectic": true, "asaupup": true, "cellule": true, "chaoliu": true, "dhampik": true, "diegohb": true, "dnp1204": true, "drewigg": true, "dyakovk": true, "ferrari": true, "fr-esco": true, "holyzfy": true, "itonyyo": true, "jpoehls": true, "kahboom": true, "kirusha": true, "kkho595": true, "kontrax": true, "kxbrand": true, "laoshaw": true, "malitov": true, "mkamayd": true, "morewry": true, "morifen": true, "nilz3ro": true, "phenome": true, "preco21": true, "restuta": true, "rhaynel": true, "tdreitz": true, "testabc": true, "timwzou": true, "tsxuehu": true, "warmhug": true, "xfloops": true, "xtx1130": true, "yanghcc": true, "yehudag": true, "abhisekp": true, "akmishra": true, "amartelr": true, "ampcpmgp": true, "andrej-k": true, "anhulife": true, "bcowgi11": true, "diversen": true, "dzhou777": true, "edloidas": true, "esundahl": true, "fakefarm": true, "gejiawen": true, "gurunate": true, "ifeature": true, "jmsherry": true, "josudoey": true, "kilkelly": true, "kulyk404": true, "leodutra": true, "lshearer": true, "manishrc": true, "maxblock": true, "maxzhang": true, "npmlincq": true, "npmrud5g": true, "pablopap": true, "panos277": true, "qddegtya": true, "rochejul": true, "sallyone": true, "shiva127": true, "staraple": true, "superjoe": true, "tdmalone": true, "thephilv": true, "tmurngon": true, "vishwasc": true, "waiwaiku": true, "wangfeia": true, "wkaifang": true, "wuuashen": true, "xiaobing": true, "xiaochao": true, "xueboren": true, "xujinnet": true, "ycjcl868": true, "youngluo": true, "zuojiang": true, "abuelwafa": true, "affiction": true, "alantsuis": true, "alert1983": true, "alimaster": true, "allen_lyu": true, "anddoutoi": true, "azertypow": true, "bigslycat": true, "coding327": true, "czj686001": true, "davequick": true, "debashish": true, "diwushi33": true, "fgribreau": true, "hellotoby": true, "hongz1125": true, "jazzhuang": true, "jiwaddell": true, "joreyaesh": true, "landy2014": true, "ldq-first": true, "madsummer": true, "magicxiao": true, "max_devjs": true, "mojaray2k": true, "mvolkmann": true, "nbuchanan": true, "nice_body": true, "nickgogan": true, "philligan": true, "snowdream": true, "stephan-v": true, "stone-jin": true, "sunnylost": true, "twinraven": true, "wukaidong": true, "adammacias": true, "aitorllj93": true, "axelrindle": true, "cfleschhut": true, "coderaiser": true, "colin.wang": true, "coolhector": true, "daniellink": true, "darkfriend": true, "earthbound": true, "huangdawei": true, "ignatovmsu": true, "incendiary": true, "jswartwood": true, "kasiriveni": true, "lababygirl": true, "land-melon": true, "langri-sha": true, "leonardorb": true, "liuchangyu": true, "manikantag": true, "mysticatea": true, "octo-utils": true, "oddjobsman": true, "pmbenjamin": true, "qqqppp9998": true, "quocnguyen": true, "rain-again": true, "rossmartin": true, "ruffle1986": true, "seangenabe": true, "shuoshubao": true, "simplyianm": true, "sunny_anna": true, "tunderdomb": true, "vincentlau": true, "zbussinger": true, "adrianvlupu": true, "alxe.master": true, "balazserdos": true, "blakecscott": true, "cbetancourt": true, "ciro-maciel": true, "coolhanddev": true, "davidnyhuis": true, "flumpus-dev": true, "glektarssza": true, "iandstanley": true, "illuminator": true, "lucifier129": true, "michaelnisi": true, "micromax720": true, "monsterkodi": true, "neaker15668": true, "phoenix-xsy": true, "pines-cheng": true, "russiansoon": true, "sadmansamee": true, "scytalezero": true, "silentcloud": true, "sinlovgmppt": true, "skullmasher": true, "thangakumar": true, "tunnckocore": true, "wangnan0610": true, "abhijitkalta": true, "aixiaocheng_": true, "bianlongting": true, "cameronnokes": true, "cherenkevich": true, "fanchangyong": true, "iori20091101": true, "jamescostian": true, "jamesmgreene": true, "johnstonbl01": true, "killparadise": true, "nickeltobias": true, "rajivmehtajs": true, "saadbinsaeed": true, "santhoshbabu": true, "shaomingquan": true, "tobiasnickel": true, "wengqianshan": true, "yourhoneysky": true, "zhenguo.zhao": true, "deadcoder0904": true, "filipecarmona": true, "hoanganh25991": true, "jasonwang1888": true, "jian263994241": true, "markthethomas": true, "schnittstabil": true, "serge-nikitin": true, "shanerobinson": true, "stone_breaker": true, "webtobesocial": true, "astraloverflow": true, "baraunaluciano": true, "blade254353074": true, "enjoyharddrink": true, "fabrianibrahim": true, "forbeslindesay": true, "herreraemanuel": true, "arcticicestudio": true, "icodeforcookies": true, "joaquin.briceno": true, "jonnymaceachern": true, "pensierinmusica": true, "thierrymarianne": true, "valeriistefanyk": true, "sammy_winchester": true, "stefano_magrassi": true, "joris-van-der-wel": true, "ricardogobbosouza": true}}