{"_id": "calculator", "_rev": "49-8cb13036c3004f54cc31b276c8137097", "name": "calculator", "description": "simple cli calculator", "dist-tags": {"latest": "0.1.12"}, "versions": {"0.0.1": {"name": "calculator", "version": "0.0.1", "description": "simple cli calculator", "main": "cliCalc.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node cliCalc.js"}, "repository": "", "keywords": ["calc", "calculator", "calculus"], "author": {"name": "saam barati", "email": "<EMAIL>"}, "license": "MIT", "_id": "calculator@0.0.1", "dist": {"shasum": "cc331a40d4b8ef601de00560685b521920c2568b", "tarball": "https://registry.npmjs.org/calculator/-/calculator-0.0.1.tgz", "integrity": "sha512-aDXPXdiLlYJ9TwcVJzsXWsXAX6Xfiqy+KlCoCEmjlMmPcTYGZ+b7ohl+Rd4UUFRziB9ROpbpy2lZ7R8TaERA+g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCsJHwChELMg7Y9nVK02a2qFCz7snnjPZNh9rjeDLIQwQIhALVsNczWCE40jWIKR+e1VgjVn2Y9X9EVKQdqeb89WEtY"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}]}, "0.0.2": {"name": "calculator", "version": "0.0.2", "description": "simple cli calculator", "main": "cliCalc.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node cliCalc.js"}, "repository": {"type": "git", "url": "https://github.com/saambarati/node-calculator"}, "keywords": ["calc", "calculator", "calculus"], "author": {"name": "saam barati", "email": "<EMAIL>"}, "license": "MIT", "_id": "calculator@0.0.2", "dist": {"shasum": "bf8c719bc93c294d97308216d2d11839bda93b96", "tarball": "https://registry.npmjs.org/calculator/-/calculator-0.0.2.tgz", "integrity": "sha512-ZxfTC4jiVeCDulFxjs38BfPdKRD1S8VWMbhye6qvUAJ58oKPDZG5k2/4OtxG+k+l1CUmUtpDYsrTe5p8dFk/3g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDaKo/VD6PSgSL0dG4SJtce7otozNQbKG1CSHWx2OT+NQIgKAOgvOERryqFT+v+PXzasmTidgez2Nb9AijiFVJBbeE="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}]}, "0.0.3": {"name": "calculator", "version": "0.0.3", "description": "simple cli calculator", "main": "cliCalc.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node cliCalc.js"}, "bin": {"node-calc": "node ./cliCalc.js"}, "repository": {"type": "git", "url": "https://github.com/saambarati/node-calculator"}, "keywords": ["calc", "calculator", "calculus"], "author": {"name": "saam barati", "email": "<EMAIL>"}, "license": "MIT", "_id": "calculator@0.0.3", "dist": {"shasum": "00b80b3bf18f18421dc3eaa6a7d6fb4a578b8ecb", "tarball": "https://registry.npmjs.org/calculator/-/calculator-0.0.3.tgz", "integrity": "sha512-PXW+lQ0NmUFuR7N07EY1RmCrw6tG5Yn1Kso0ZnJbd+qDhJ4UYbcBsPAFefHYpSGOPr7O+7uVIAnoLs2n3Y1x2A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDou2zUTrOkOYGuWkqSPD0D+hrqaIITIHy/YW8wA5hSFwIgf7W7IVcfUlcImV81B2QMqSMwdIbbRh627L0KWUMhCWw="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}]}, "0.0.4": {"name": "calculator", "version": "0.0.4", "description": "simple cli calculator", "main": "cliCalc.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node cliCalc.js"}, "bin": {"node-calc": "./cliCalc.js"}, "repository": {"type": "git", "url": "https://github.com/saambarati/node-calculator"}, "keywords": ["calc", "calculator", "calculus"], "author": {"name": "saam barati", "email": "<EMAIL>"}, "license": "MIT", "_id": "calculator@0.0.4", "dist": {"shasum": "70c4d4b804da6bd1fb6a24590538e24e72acee22", "tarball": "https://registry.npmjs.org/calculator/-/calculator-0.0.4.tgz", "integrity": "sha512-aYrPTVcRO1tJvrVvFY+XU6c0QaltIIVzlYeF9+05l4vZ4FLjd90y8y7qDd6pRIQuZR3I5NwNMVSvDaRqw7qeDw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCtAoE3rwfQ4cpYON4BLTE3yElGidPS3BzitiGdM8Q/vgIhAO14PampkKzj8dYKn6TxoDeaEnq3AGUEXmFdBMOwNCnD"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}]}, "0.0.5": {"name": "calculator", "version": "0.0.5", "description": "simple cli calculator", "main": "cliCalc.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node cliCalc.js"}, "bin": {"node-calc": "./cliCalc.js"}, "repository": {"type": "git", "url": "https://github.com/saambarati/node-calculator"}, "keywords": ["calc", "calculator", "calculus"], "author": {"name": "saam barati", "email": "<EMAIL>"}, "license": "MIT", "_id": "calculator@0.0.5", "dist": {"shasum": "45746e26231cdb755636dabbc7205aa104fd62a4", "tarball": "https://registry.npmjs.org/calculator/-/calculator-0.0.5.tgz", "integrity": "sha512-HB9KU6tmNuNIAJsdTaZXcYOkADdZoqz9EWIjUBmfwooP8GuMydxX6qtqAyFEBhmduZeDPDSjPega2QzMw2unXA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDyUxifVcnMfuHpzdj5wrujGxsBXFY+fNqsG7Ke48KAqAiAMXY1aBpPCYYo7EYMe4Hj+gelknZ3CIWsE2F16R+Fyhg=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}]}, "0.0.6": {"name": "calculator", "version": "0.0.6", "description": "simple cli calculator", "main": "cliCalc.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node cliCalc.js"}, "bin": {"node-calc": "./cliCalc.js"}, "repository": {"type": "git", "url": "https://github.com/saambarati/node-calculator"}, "keywords": ["calc", "calculator", "calculus"], "author": {"name": "saam barati", "email": "<EMAIL>"}, "license": "MIT", "_id": "calculator@0.0.6", "dist": {"shasum": "0d4465ae46e8f3fb009b07b87b532ab8cc1a3f25", "tarball": "https://registry.npmjs.org/calculator/-/calculator-0.0.6.tgz", "integrity": "sha512-zeIFPmjDA8QBWYhCAt0Oxz0h5staeZ/aF/01BKyAW202ncPeahNNF4p4i5Fm3Nd8lKeYp3Wmwl57p7Hytr5QXA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD1sU20v8Z4qdd8AajYE41xAOP/EqrdsO2LzV54vNiEnwIhAPA35krZzvST2P9vPqXIictXhXRum3YhopBDYrC8CoH7"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}]}, "0.0.7": {"name": "calculator", "version": "0.0.7", "description": "simple cli calculator", "main": "cliCalc.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node cliCalc.js"}, "bin": {"node-calc": "./cliCalc.js"}, "repository": {"type": "git", "url": "https://github.com/saambarati/node-calculator"}, "keywords": ["calc", "calculator", "calculus"], "author": {"name": "saam barati", "email": "<EMAIL>"}, "license": "MIT", "_id": "calculator@0.0.7", "dist": {"shasum": "478cfb4613159b755350764fe2342082e52a0cd0", "tarball": "https://registry.npmjs.org/calculator/-/calculator-0.0.7.tgz", "integrity": "sha512-6Bs4EVZWd0Wf6ghOmy0zqxS3JExZb5erjT6AgvJJVyVYBJZr65FT5LAEYNSrSZ8VVesP1ld3huKWmcHcHleVxQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFzTkJtERtcQZG9nYfzsUTjjBiZJ58pr1K8Ztp+ytv+UAiATs2t5hZNA9pMLFEIiaYv6TDxU1mFOi979ohARAUh5CA=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}]}, "0.0.8": {"name": "calculator", "version": "0.0.8", "description": "simple cli calculator", "main": "cliCalc.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node cliCalc.js"}, "bin": {"node-calc": "./cliCalc.js"}, "repository": {"type": "git", "url": "https://github.com/saambarati/node-calculator"}, "keywords": ["calc", "calculator", "calculus"], "author": {"name": "saam barati", "email": "<EMAIL>"}, "license": "MIT", "_id": "calculator@0.0.8", "dist": {"shasum": "1489a10c08628973705b830524e00d790624ef2a", "tarball": "https://registry.npmjs.org/calculator/-/calculator-0.0.8.tgz", "integrity": "sha512-4VEY84yBjQIXqO6f6+oRTbsEstKLxAdI+hkNVqIL6vmWAISl9/odvtbSiJ623SJbyLxz6DdH09JqpJGLMnwZCA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDGIitguT+D23yx5AaT2DW0DfO4DmJCHO4nwsYsl86GVAiB3kybA26XqXdgzecq4uYU9fmJDwRXrusjt7G33aE+gwg=="}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}]}, "0.0.9": {"name": "calculator", "version": "0.0.9", "description": "simple cli calculator", "main": "cliCalc.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node cliCalc.js"}, "bin": {"node-calc": "./cliCalc.js"}, "repository": {"type": "git", "url": "https://github.com/saambarati/node-calculator"}, "keywords": ["calc", "calculator", "calculus"], "author": {"name": "saam barati", "email": "<EMAIL>"}, "license": "MIT", "_id": "calculator@0.0.9", "dist": {"shasum": "1d10659a613880b45fb019f11cb47fe5688f0cfc", "tarball": "https://registry.npmjs.org/calculator/-/calculator-0.0.9.tgz", "integrity": "sha512-RFgFVW469hBNW46Ro7V3cSVfZ3VHlASUSCM5DdH3rdaxzLpCZIEIFotmzg5qS48NCkEw7NPn/8FsP1s3wAyzAg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGumKdYEmCDtMEZ1ae/kBd+Dqy90lgi14ifFXYimrfcuAiBvHmrY+WZhO/QcnHr+y6KLfLX1AqjxzslFw4gSQ+u3eQ=="}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}]}, "0.1.0": {"name": "calculator", "version": "0.1.0", "description": "simple cli calculator", "main": "cliCalc.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node cliCalc.js"}, "bin": {"node-calc": "./cliCalc.js"}, "repository": {"type": "git", "url": "https://github.com/saambarati/node-calculator"}, "keywords": ["calc", "calculator", "calculus"], "dependencies": {"optimist": "0.3.x", "prompt": "0.2.x", "colors": "0.6.x"}, "author": {"name": "saam barati", "email": "<EMAIL>"}, "license": "MIT", "_id": "calculator@0.1.0", "dist": {"shasum": "16304012b4d47bd5f507d036973984bedeb557d2", "tarball": "https://registry.npmjs.org/calculator/-/calculator-0.1.0.tgz", "integrity": "sha512-kzp7jgFUjXvniCL5AUx0sGPXATK2D3PET6Qw9oVA1PFduzAYrhty1kRqokPqS9Hz4y0Cqgza8e5+508NTMe+8Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIADL1LYzIIccowVlLyqxQ32uIFZVsBP9S8v3Nk172rkfAiEArH6Ftkg/OPMCnJmjeeEiTWBaLIw0BAgv8gTi/AmvuWE="}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}]}, "0.1.1": {"name": "calculator", "version": "0.1.1", "description": "simple cli calculator", "main": "cliCalc.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node cli.js"}, "bin": {"node-calc": "./cli.js"}, "repository": {"type": "git", "url": "https://github.com/saambarati/node-calculator"}, "keywords": ["calc", "calculator", "calculus"], "dependencies": {"optimist": "0.3.x", "prompt": "0.2.x", "colors": "0.6.x"}, "author": {"name": "saam barati", "email": "<EMAIL>"}, "license": "MIT", "_id": "calculator@0.1.1", "dist": {"shasum": "6a4c7aef1baf9ccde0aeba01569bec4e3ab4d8b3", "tarball": "https://registry.npmjs.org/calculator/-/calculator-0.1.1.tgz", "integrity": "sha512-uxSsnrRag8HMNd+4Hadb7DW08nVodgnbGEgSH1kZg6Eja98njzKAHnmlBU/zC3s2q7N5B5MMEvjLZF9T1//Dmw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAy1ENHfUePwirGx+TN7cTUC4nW0wlptnqfnW0KO3dTDAiAY+VwUy58ftc87LqrQU8oZVJSL3qNs0OiZVrhhiSDIUg=="}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}]}, "0.1.2": {"name": "calculator", "version": "0.1.2", "description": "simple cli calculator", "main": "cliCalc.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node cli.js"}, "bin": {"node-calc": "./cli.js"}, "repository": {"type": "git", "url": "https://github.com/saambarati/node-calculator"}, "keywords": ["calc", "calculator", "calculus"], "dependencies": {"optimist": "0.3.x", "prompt": "0.2.x", "colors": "0.6.x"}, "author": {"name": "saam barati", "email": "<EMAIL>"}, "license": "MIT", "_id": "calculator@0.1.2", "dist": {"shasum": "66d081c0aa4178cb440d888d16099df73ec67a30", "tarball": "https://registry.npmjs.org/calculator/-/calculator-0.1.2.tgz", "integrity": "sha512-KREWJIpkrQGSzh2pRx6mkIg55/jafVPS+s5iokkQVM2HdecSiblQRzvVFIyGK8wXXtFcTrY+WPBgdbfZkaz63Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDB/UzcDFt7M8Nvs/j+ecHwAPOExi73fRZt1nE7yGpd9AIgTqzv6NryNxTq/iJX4mNCcBlUSJe1F2U3w+Ozg/AiP8E="}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}]}, "0.1.3": {"name": "calculator", "version": "0.1.3", "description": "simple cli calculator", "main": "calculator.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node cli.js", "prepublish": "./build.sh"}, "bin": {"node-calc": "./cli.js"}, "repository": {"type": "git", "url": "https://github.com/saambarati/node-calculator"}, "keywords": ["calc", "calculator", "calculus"], "dependencies": {"optimist": "0.3.x", "prompt": "0.2.x", "colors": "0.6.x"}, "author": {"name": "saam barati", "email": "<EMAIL>"}, "license": "MIT", "_id": "calculator@0.1.3", "dist": {"shasum": "25e44079f835e8299ff30acb33d916d08237fc90", "tarball": "https://registry.npmjs.org/calculator/-/calculator-0.1.3.tgz", "integrity": "sha512-FBfWPaUd6GqaX++C+tfsc5YAOGVxsAnsW76aFRitTSdMlJXFfbg3gIqiaujq/8G15MeDndOVWwvhmUGra8HsoA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC5SEIE9IPp5/h/tkbi/q4jJ+Mur2L9oLQRgfR+QfR74AiEArFhCTLnYJpi93OhRUPSIPV+R+xrukWoUiBQFQDn7xwg="}]}, "_npmVersion": "1.1.61", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}]}, "0.1.4": {"name": "calculator", "version": "0.1.4", "description": "simple cli calculator", "main": "calculator.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node cli.js", "postinstall": "./postinstall.sh", "prepublish": "./build.sh"}, "bin": {"node-calc": "./cli.js"}, "repository": {"type": "git", "url": "https://github.com/saambarati/node-calculator"}, "keywords": ["calc", "calculator", "calculus"], "dependencies": {"optimist": "0.3.x", "prompt": "0.2.x", "colors": "0.6.x"}, "author": {"name": "saam barati", "email": "<EMAIL>"}, "license": "MIT", "_id": "calculator@0.1.4", "dist": {"shasum": "f098b4ba842f8b79c1c6b3d9963d1b33727499e5", "tarball": "https://registry.npmjs.org/calculator/-/calculator-0.1.4.tgz", "integrity": "sha512-R5vqtYULy5t/cXkQnPhxe3/9dhr0HpldWz8KfjucH9aKRpWl49KgXhLkolLPJpe0jZKdbUcvR1/sTMYcqXSlDA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEUtuU6zC7rOyHkbwDB9z85CfayhuUhNg5YJrNM4LWQCAiEAutsrY4M7fFj3NDUO2TRL5ONDgwzKg7tnE9Ejl5D91u0="}]}, "_npmVersion": "1.1.61", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}]}, "0.1.5": {"name": "calculator", "version": "0.1.5", "description": "simple cli calculator", "main": "calculator.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node cli.js", "postinstall": "./postinstall.sh", "prepublish": "./build.sh"}, "bin": {"node-calc": "./cli.js"}, "repository": {"type": "git", "url": "https://github.com/saambarati/node-calculator"}, "keywords": ["calc", "calculator", "calculus"], "dependencies": {"optimist": "0.3.x", "prompt": "0.2.x", "colors": "0.6.x"}, "author": {"name": "saam barati", "email": "<EMAIL>"}, "license": "MIT", "_id": "calculator@0.1.5", "dist": {"shasum": "3164317613eafd97f47df4b52520af02f7dc6f0b", "tarball": "https://registry.npmjs.org/calculator/-/calculator-0.1.5.tgz", "integrity": "sha512-aG1IRUrpuroArLmswAhUaVFXqNWFx00xNbm119HOKpiMwm0VEWDiUFPj4L48NhJKcm5bHL7l+aG8nHPshKkZhw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDc/UZbIfw6hQl93sIf6djXnAXg+Q5nNYDRySs7wdDXSgIhAPWnRe3QrftO2fwYQRNlRIHm408kEzfrLyGZGj3F7Gqz"}]}, "_npmVersion": "1.1.61", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}]}, "0.1.6": {"name": "calculator", "version": "0.1.6", "description": "simple cli calculator", "main": "calculator.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node cli.js", "postinstall": "./postinstall.sh", "prepublish": "./build.sh"}, "bin": {"node-calc": "./cli.js"}, "repository": {"type": "git", "url": "https://github.com/saambarati/node-calculator"}, "keywords": ["calc", "calculator", "calculus"], "dependencies": {"optimist": "0.3.x", "prompt": "0.2.x", "colors": "0.6.x"}, "author": {"name": "saam barati", "email": "<EMAIL>"}, "license": "MIT", "_id": "calculator@0.1.6", "dist": {"shasum": "e2280039d127b00f31bd15faec86efce95d20ba2", "tarball": "https://registry.npmjs.org/calculator/-/calculator-0.1.6.tgz", "integrity": "sha512-FW2ALsI034MKcwWBRRbZ7EsL8U2nWZrw0kmM1NAM2XqqT+dcx7ytzIino0/kct6yFbIb0AqkMd6z4nUyBy0Lgw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD4yzx2VUTSjZ6sqZkOpcU6cJA3zC7qkI7ncwsnmtXlegIhANuOdV1z54bONGFjwiOvmjuQgxFoZRTgFP4YekQwgEt/"}]}, "_npmVersion": "1.1.61", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}]}, "0.1.7": {"name": "calculator", "version": "0.1.7", "description": "simple cli calculator", "main": "calculator.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node cli.js", "preinstall": "./preinstall.sh", "postinstall": "./postinstall.sh", "prepublish": "./build.sh"}, "bin": {"node-calc": "./cli.js"}, "repository": {"type": "git", "url": "https://github.com/saambarati/node-calculator"}, "keywords": ["calc", "calculator", "calculus"], "dependencies": {"optimist": "0.3.x", "prompt": "0.2.x", "colors": "0.6.x"}, "author": {"name": "saam barati", "email": "<EMAIL>"}, "license": "MIT", "_id": "calculator@0.1.7", "dist": {"shasum": "fb49d9d80603ad7619c8d4f25921673ddcc9ee9b", "tarball": "https://registry.npmjs.org/calculator/-/calculator-0.1.7.tgz", "integrity": "sha512-V//+2DU5QDllByZvNVC8NaJtKEwbOdv7JR74sc5wXEAJUu6Kt8Hf40w7QBg9UpXf+f0lwXHLI1RkjFg9VN5g4A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC0yFffVO/ZwM/UBScRqxxT4O+Xaku0jUe/YzUh92WU6AIhANLsvgd4OpRkUxenlelgoEXZa90/fF/6iV9zSuDzvH4A"}]}, "_npmVersion": "1.1.61", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}]}, "0.1.8": {"name": "calculator", "version": "0.1.8", "description": "simple cli calculator", "main": "calculator.js", "scripts": {"test": "node ./test.js", "start": "node cli.js -c", "postuninstall": "node ./postuninstall.js"}, "bin": {"node-calc": "./cli.js"}, "repository": {"type": "git", "url": "https://github.com/saambarati/node-calculator"}, "keywords": ["calc", "calculator", "calculus"], "dependencies": {"optimist": "0.3.x", "prompt": "0.2.x", "colors": "0.6.x"}, "author": {"name": "saam barati", "email": "<EMAIL>"}, "license": "MIT", "_id": "calculator@0.1.8", "dist": {"shasum": "f7727307a0828ff683a0bca32a17d7d250ed5e4f", "tarball": "https://registry.npmjs.org/calculator/-/calculator-0.1.8.tgz", "integrity": "sha512-Nw0+gX+WwEIdHjoEQ/IHgyzfuZOiF02ZvpgobYjmMVaTwMtaxrPiiVhVOHIoWv2wJ/zvCxP1i96869mvS570KQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDaUGaoSFP80nFIoH7pCzLdTmr48zRzalBOzAni1PkhXwIhALZeixM1kMAYvoOpylWOgHFrTXtbMdxqgf7qStXj8HUW"}]}, "_npmVersion": "1.1.61", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}]}, "0.1.9": {"name": "calculator", "version": "0.1.9", "description": "simple cli calculator", "main": "calculator.js", "scripts": {"test": "node ./test.js", "start": "node ./cli.js -c", "postuninstall": "node ./postuninstall.js"}, "bin": {"node-calc": "./cli.js"}, "repository": {"type": "git", "url": "https://github.com/saambarati/node-calculator"}, "keywords": ["calc", "calculator", "calculus"], "dependencies": {"optimist": "0.3.x", "prompt": "0.2.x", "colors": "0.6.x"}, "author": {"name": "saam barati", "email": "<EMAIL>"}, "license": "MIT", "_id": "calculator@0.1.9", "dist": {"shasum": "356d9303e926fc5072f996d5487a799343e25e77", "tarball": "https://registry.npmjs.org/calculator/-/calculator-0.1.9.tgz", "integrity": "sha512-tDI+SSRR3c/YjoPdzKP/3kIMOyShAgQdsxZgSGQkf5BvU98tijncp9wKnRIw3nEUfP23AWLN7y600PechXgG+A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDNbC149qi3qwie/s/KGQCwPPC1Ye2LQHGg1LMrfAdttAiBSwTF/Cype9ajFPG5dZ+PH/lexBm3bkmvJWtBDgLWr1Q=="}]}, "_npmVersion": "1.1.61", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}]}, "0.1.10": {"name": "calculator", "version": "0.1.10", "description": "simple cli calculator", "main": "calculator.js", "scripts": {"test": "node ./test.js", "start": "node ./cli.js -c", "postuninstall": "node ./postuninstall.js"}, "bin": {"node-calc": "./cli.js"}, "repository": {"type": "git", "url": "https://github.com/saambarati/node-calculator"}, "keywords": ["calc", "calculator", "calculus"], "dependencies": {"optimist": "0.3.x", "prompt": "0.2.x", "colors": "0.6.x"}, "author": {"name": "saam barati", "email": "<EMAIL>"}, "license": "MIT", "_id": "calculator@0.1.10", "dist": {"shasum": "79559492cba51f5571fd7e40220e444fb696e015", "tarball": "https://registry.npmjs.org/calculator/-/calculator-0.1.10.tgz", "integrity": "sha512-/NjPnropZTOoeSiYGt3OEJZd+WO0TAB+KhMbGHlfjCQLgypJ2NrZ+Btz+1MvkBjTdoQuvGnRvfiyXN1nqW1uXA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFq3UBxdi3et88IyHSK5M9VJBUvtEmw/N9IM6lE0bc/YAiEAvA+20VkEIS5gl8VuOI/nKao4fC7uE4d/9D4nqVi+vQ4="}]}, "_npmVersion": "1.1.61", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}]}, "0.1.11": {"name": "calculator", "version": "0.1.11", "description": "simple cli calculator", "main": "calculator.js", "scripts": {"test": "node ./test.js", "start": "node ./cli.js -c"}, "bin": {"node-calc": "./cli.js"}, "repository": {"type": "git", "url": "https://github.com/saambarati/node-calculator"}, "keywords": ["calc", "calculator", "calculus"], "dependencies": {"optimist": "0.3.x", "prompt": "0.2.x", "colors": "0.6.x"}, "author": {"name": "saam barati", "email": "<EMAIL>"}, "license": "MIT", "_id": "calculator@0.1.11", "dist": {"shasum": "6907ca29b1e039ee1dae14c41c0abe7e495432e3", "tarball": "https://registry.npmjs.org/calculator/-/calculator-0.1.11.tgz", "integrity": "sha512-CWaHX0qEKL3y1W/QKlofSym/W+u+ZvBE4Ns6wL4uNmOkaQ85W6iAvK5v/9oj4Lz//1zGxh6pQgAYvRy0DtIb1Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE3zgZr7htSJUElLGHkC3x1quI/N+ekaBmQAHY+JNYK1AiARViCMyCeVMfwO5JNYSnntEvlUAega+blw/GkpXhn3nw=="}]}, "_npmVersion": "1.1.61", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}]}, "0.1.12": {"name": "calculator", "version": "0.1.12", "description": "simple cli calculator", "main": "calculator.js", "scripts": {"test": "node ./test.js", "start": "node ./cli.js -c"}, "bin": {"node-calc": "./cli.js", "calculator": "./cli.js"}, "repository": {"type": "git", "url": "https://github.com/saambarati/node-calculator"}, "keywords": ["calc", "calculator", "calculus"], "dependencies": {"optimist": "0.3.x", "prompt": "0.2.x", "colors": "0.6.x"}, "author": {"name": "saam barati", "email": "<EMAIL>"}, "license": "MIT", "_id": "calculator@0.1.12", "dist": {"shasum": "e0627746f0d540e0e1814cd4b10f9f6b6c4255d4", "tarball": "https://registry.npmjs.org/calculator/-/calculator-0.1.12.tgz", "integrity": "sha512-e6S9VKwZZ9f2ODols+45EY+lU8vbhUaf9JVqMZ1qKkzh0OZ4QJ7JOcUCdcIxNoJHRhIrATbQ6gIDBax2jrHzHg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEcidoQby/jg1t5uO+DXrI1cTejEJZevilFCzaecvvDCAiEArPhaHwHemHVvxm60E3ihwX/jx8tWbYx2o/MPOpluIpo="}]}, "_npmVersion": "1.1.61", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}]}}, "readme": "# calculator\n\n### Install\n     npm install calculator\n     cd calculator\n     npm start\n\nor\n\n    npm install -g calculator\n    node-calc\n\n### API\n\nWhen starting calculator, it starts a node repl with all of the following properties set as properties of the `GLOBAL` object.\nAll Math properties are replaced with `Math.(prop)` so you can write `var f = func('f(x) = sin(x)')` instead of `var f = func('f(x) = Math.sin(x)')`\n\n#### func\n    var f = func('f(x) = x*10 - 20')\n    f(3) //returns 10\n\n    f = func('f(x, t) = Math.pow(x, 2) + t + 1')\n    f(2, 4) // returns 9\n\n#### derive\nparams `([string|function], point to evaluate function)`\n    `derive('f(x) = x*x', 2) //evaluate derivative @ x = 2;  returns 4`\n\n#### integrate\nparams `([string|function], lower limit, upper limit)`\n`derive('f(x) = x*x', 0, 1) //evaluate definite integral from x = 0 to x = 1;  returns .333 = 1/3`\n\n#### accuracy\nSet this property to determine how accurate the definite integral will be. Essentialy the \"dx\" in f(x)dx.\nDefault is `1/1000000`\n\n#### roundAccuracy\nSet this property to determine the distance between an estimated answer and a whole number that you wish cli calc to return the rounded number.\nThis is useful when you know the answer is a whole number and you don't want something like `1.99999999998384` returned instead of `2`\n\n", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2022-06-13T05:25:25.779Z", "created": "2012-09-03T08:05:39.533Z", "0.0.1": "2012-09-03T08:05:40.805Z", "0.0.2": "2012-09-03T08:09:27.008Z", "0.0.3": "2012-09-03T08:21:15.973Z", "0.0.4": "2012-09-03T08:22:06.416Z", "0.0.5": "2012-09-03T08:23:58.554Z", "0.0.6": "2012-09-03T08:29:34.474Z", "0.0.7": "2012-09-03T08:48:56.257Z", "0.0.8": "2012-09-11T01:03:09.690Z", "0.0.9": "2012-09-16T04:47:31.336Z", "0.1.0": "2012-09-23T03:23:00.873Z", "0.1.1": "2012-09-23T03:27:49.306Z", "0.1.2": "2012-09-23T03:35:20.043Z", "0.1.3": "2012-09-23T03:59:36.101Z", "0.1.4": "2012-09-23T04:29:57.236Z", "0.1.5": "2012-09-23T04:31:52.644Z", "0.1.6": "2012-09-23T04:34:06.685Z", "0.1.7": "2012-09-23T04:58:14.899Z", "0.1.8": "2012-09-24T04:01:39.127Z", "0.1.9": "2012-09-24T04:08:56.536Z", "0.1.10": "2012-09-24T04:18:26.728Z", "0.1.11": "2012-09-24T04:20:09.953Z", "0.1.12": "2012-09-24T04:21:57.312Z"}, "author": {"name": "saam barati", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/saambarati/node-calculator"}, "keywords": ["calc", "calculator", "calculus"], "license": "MIT", "readmeFilename": "", "users": {"codefoster": true}}