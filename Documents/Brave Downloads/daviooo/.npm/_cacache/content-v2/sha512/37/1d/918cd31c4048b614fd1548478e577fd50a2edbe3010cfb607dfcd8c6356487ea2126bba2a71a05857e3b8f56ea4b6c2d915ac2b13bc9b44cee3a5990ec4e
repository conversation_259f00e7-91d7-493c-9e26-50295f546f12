{"_id": "set-function-name", "_rev": "5-e051893281b456fccb3721d44cec4fd1", "name": "set-function-name", "description": "Set a function's name property", "dist-tags": {"latest": "2.0.2"}, "versions": {"1.0.0": {"name": "set-function-name", "version": "1.0.0", "description": "Set a function's name property", "main": "index.js", "directories": {"test": "test"}, "scripts": {"test": "node ./test/index.js"}, "repository": {"type": "git", "url": "git+https://github.com/jfsiii/set-function-name.git"}, "keywords": ["set", "assign", "function", "name", "function.name"], "author": {"name": "JFSIII"}, "license": "ISC", "bugs": {"url": "https://github.com/jfsiii/set-function-name/issues"}, "homepage": "https://github.com/jfsiii/set-function-name#readme", "gitHead": "9cac96fd9e98f25883be6e727ac32356522a5d69", "_id": "set-function-name@1.0.0", "_npmVersion": "5.4.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "jfsiii", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-ynkzpWi8LCkr4QhYNJSMStcm4qjVlo8AzGNSajO/EUlt5dre+wacYw4AcpMAaS4LwAFaYGTYC6zzFmmRbudfuw==", "shasum": "3028b7f19120b871caf27a27911b087ae1d9b240", "tarball": "https://registry.npmjs.org/set-function-name/-/set-function-name-1.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD8n5B9P4nzbP0Sct8uZOjhzEuhKc6mlf3ne1Ys4z2L3wIhANRSe1Ix4b9psXTqy1B4jfkKT86UCBYOcYH3DJu3L1uk"}]}, "maintainers": [{"name": "jfsiii", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/set-function-name-1.0.0.tgz_1505426424510_0.538303472334519"}}, "2.0.0": {"name": "set-function-name", "version": "2.0.0", "description": "Set a function's name property", "main": "index.js", "directories": {"test": "test"}, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "lint": "eslint --ext=js,mjs .", "postlint": "evalmd README.md", "pretest": "npm run lint", "tests-only": "tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/set-function-name.git"}, "keywords": ["set", "assign", "function", "name", "function.name"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/set-function-name/issues"}, "homepage": "https://github.com/ljharb/set-function-name#readme", "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "aud": "^2.0.3", "auto-changelog": "^2.4.0", "es-value-fixtures": "^1.4.2", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "function.prototype.name": "^1.1.6", "functions-have-names": "^1.2.3", "in-publish": "^2.0.1", "make-arrow-function": "^1.2.0", "make-async-function": "^1.0.0", "make-async-generator-function": "^1.0.0", "make-generator-function": "^2.0.0", "npmignore": "^0.3.0", "object-inspect": "^1.12.3", "safe-publish-latest": "^2.0.0", "tape": "^5.6.6"}, "dependencies": {"define-data-property": "^1.0.1", "has-property-descriptors": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows", "test"]}, "_id": "set-function-name@2.0.0", "gitHead": "80cb448906423417a8e4db7d67fc8f8f71967340", "_nodeVersion": "20.5.1", "_npmVersion": "9.8.0", "dist": {"integrity": "sha512-WmS8UHojv5s1eSoRSmzO5zzgDq8PE1/X/ij0k+9fMYmINCc6+j+SF3Om8YyucKn2yjnK4ItNZOoQycNnHsZJTw==", "shasum": "e59e53e7fa23f9c2da1c0999fa202d5ed09c4756", "tarball": "https://registry.npmjs.org/set-function-name/-/set-function-name-2.0.0.tgz", "fileCount": 7, "unpackedSize": 9882, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD/ypj1lupHpqGGc6sa4g4VltEqIfEctMQW0gXk6C8wigIhAMpr1Nrmeh1zlOH4/MDd1GzSo7KtDjyWjC4DcUF2bzTs"}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "jfsiii", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/set-function-name_2.0.0_1694579611283_0.889174187745311"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "set-function-name", "version": "2.0.1", "description": "Set a function's name property", "main": "index.js", "directories": {"test": "test"}, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "lint": "eslint --ext=js,mjs .", "postlint": "evalmd README.md", "pretest": "npm run lint", "tests-only": "tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/set-function-name.git"}, "keywords": ["set", "assign", "function", "name", "function.name"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/set-function-name/issues"}, "homepage": "https://github.com/ljharb/set-function-name#readme", "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "aud": "^2.0.3", "auto-changelog": "^2.4.0", "es-value-fixtures": "^1.4.2", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "function.prototype.name": "^1.1.6", "in-publish": "^2.0.1", "make-arrow-function": "^1.2.0", "make-async-function": "^1.0.0", "make-async-generator-function": "^1.0.0", "make-generator-function": "^2.0.0", "npmignore": "^0.3.0", "object-inspect": "^1.12.3", "safe-publish-latest": "^2.0.0", "tape": "^5.6.6"}, "dependencies": {"define-data-property": "^1.0.1", "functions-have-names": "^1.2.3", "has-property-descriptors": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows", "test"]}, "_id": "set-function-name@2.0.1", "gitHead": "eebd109730ffb1dff9e22efd3bcff163e87edb00", "_nodeVersion": "20.6.0", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-tMNCiqYVkXIZgc2Hnoy2IvC/f8ezc5koaRFkCjrpWzGpCd3qbZXPzVy9MAZzK1ch/X0jvSkojys3oqJN0qCmdA==", "shasum": "12ce38b7954310b9f61faa12701620a0c882793a", "tarball": "https://registry.npmjs.org/set-function-name/-/set-function-name-2.0.1.tgz", "fileCount": 7, "unpackedSize": 10148, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEVn6QFVUdEfYHeZPyz2iu5pfB2gUsok0oVAaYZi8PWGAiEAgNopWgb/ZBRp+nXRQmKN3m0SpPBkesFmhHBInK2UzN4="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "jfsiii", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/set-function-name_2.0.1_1694614181690_0.8215519378100382"}, "_hasShrinkwrap": false}, "2.0.2": {"name": "set-function-name", "version": "2.0.2", "description": "Set a function's name property", "main": "index.js", "types": "./index.d.ts", "directories": {"test": "test"}, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "prelint": "evalmd README.md", "lint": "eslint --ext=js,mjs .", "postlint": "tsc -p .", "pretest": "npm run lint", "tests-only": "tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/set-function-name.git"}, "keywords": ["set", "assign", "function", "name", "function.name"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/set-function-name/issues"}, "homepage": "https://github.com/ljharb/set-function-name#readme", "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "@types/call-bind": "^1.0.5", "@types/define-properties": "^1.1.5", "@types/es-value-fixtures": "^1.4.4", "@types/for-each": "^0.3.3", "@types/function.prototype.name": "^1.1.3", "@types/functions-have-names": "^1.2.2", "@types/has-property-descriptors": "^1.0.3", "@types/make-arrow-function": "^1.2.2", "@types/make-async-function": "^1.0.2", "@types/make-async-generator-function": "^1.0.3", "@types/make-generator-function": "^2.0.3", "@types/object-inspect": "^1.8.4", "@types/tape": "^5.6.4", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "es-value-fixtures": "^1.4.2", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "function.prototype.name": "^1.1.6", "in-publish": "^2.0.1", "make-arrow-function": "^1.2.0", "make-async-function": "^1.0.0", "make-async-generator-function": "^1.0.0", "make-generator-function": "^2.0.0", "npmignore": "^0.3.1", "object-inspect": "^1.13.1", "safe-publish-latest": "^2.0.0", "tape": "^5.7.5", "typescript": "next"}, "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "functions-have-names": "^1.2.3", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows", "test", "!*.d.ts", "!*.d.ts.map"]}, "_id": "set-function-name@2.0.2", "gitHead": "2ff8b304c8795e8d76ce992622eb046ff88d1c48", "_nodeVersion": "21.6.2", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-7PGFlmtwsEADb0WYyvCMa1t+yke6daIG4Wirafur5kcf+MhUnPms1UeR0CKQdTZD81yESwMHbtn+TR+dMviakQ==", "shasum": "16a705c5a0dc2f5e638ca96d8a8cd4e1c2b90985", "tarball": "https://registry.npmjs.org/set-function-name/-/set-function-name-2.0.2.tgz", "fileCount": 9, "unpackedSize": 16720, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAGvnYsURjWqAxBZ1fwFEJaWyUeq/QfWMCoS9JWm5RBVAiBkF71oL6yi5qVTIm2dYghWivYN6NYNREh2uGLF/kbDcQ=="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "jfsiii", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/set-function-name_2.0.2_1708407265153_0.6351991321661548"}, "_hasShrinkwrap": false}}, "readme": "# set-function-name <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\nSet a function’s name.\n\nArguments:\n - `fn`: the function\n - `name`: the new name\n - `loose`: Optional. If true, and the name fails to be set, do not throw. Default false.\n\nReturns `fn`.\n\n## Usage\n\n```javascript\nvar setFunctionName = require('set-function-name');\nvar assert = require('assert');\n\nconst obj = {\n    concise() {},\n    arrow: () => {},\n    named: function named() {},\n    anon: function () {},\n};\nassert.equal(obj.concise.name, 'concise');\nassert.equal(obj.arrow.name, 'arrow');\nassert.equal(obj.named.name, 'named');\nassert.equal(obj.anon.name, 'anon');\n\nassert.equal(setFunctionName(obj.concise, 'brief'), obj.concise);\nassert.equal(setFunctionName(obj.arrow, 'pointy'), obj.arrow);\nassert.equal(setFunctionName(obj.named, ''), obj.named);\nassert.equal(setFunctionName(obj.anon, 'anonymous'), obj.anon);\n\nassert.equal(obj.concise.name, 'brief');\nassert.equal(obj.arrow.name, 'pointy');\nassert.equal(obj.named.name, '');\nassert.equal(obj.anon.name, 'anonymous');\n```\n\n[package-url]: https://npmjs.org/package/set-function-name\n[npm-version-svg]: https://versionbadg.es/ljharb/set-function-name.svg\n[deps-svg]: https://david-dm.org/ljharb/set-function-name.svg\n[deps-url]: https://david-dm.org/ljharb/set-function-name\n[dev-deps-svg]: https://david-dm.org/ljharb/set-function-name/dev-status.svg\n[dev-deps-url]: https://david-dm.org/ljharb/set-function-name#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/set-function-name.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/set-function-name.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/set-function-name.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=set-function-name\n[codecov-image]: https://codecov.io/gh/ljharb/set-function-name/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/ljharb/set-function-name/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/ljharb/set-function-name\n[actions-url]: https://github.com/ljharb/set-function-name/actions\n", "maintainers": [{"name": "jfsiii", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "time": {"modified": "2024-02-20T05:34:26.139Z", "created": "2017-09-14T22:00:24.576Z", "1.0.0": "2017-09-14T22:00:24.576Z", "2.0.0": "2023-09-13T04:33:31.446Z", "2.0.1": "2023-09-13T14:09:41.850Z", "2.0.2": "2024-02-20T05:34:25.309Z"}, "homepage": "https://github.com/ljharb/set-function-name#readme", "keywords": ["set", "assign", "function", "name", "function.name"], "repository": {"type": "git", "url": "git+https://github.com/ljharb/set-function-name.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/ljharb/set-function-name/issues"}, "license": "MIT", "readmeFilename": "README.md"}