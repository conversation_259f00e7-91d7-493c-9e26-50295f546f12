{"_id": "@types/cacheable-request", "_rev": "339-fd87cc5dfbca6e0fc635ca35c5e05558", "name": "@types/cacheable-request", "dist-tags": {"ts2.4": "6.0.1", "ts3.5": "6.0.1", "ts3.3": "6.0.1", "ts3.4": "6.0.1", "ts3.2": "6.0.1", "ts3.1": "6.0.1", "ts3.0": "6.0.1", "ts2.9": "6.0.1", "ts2.8": "6.0.1", "ts2.7": "6.0.1", "ts2.6": "6.0.1", "ts2.3": "6.0.1", "ts2.5": "6.0.1", "ts3.7": "6.0.2", "ts4.0": "6.0.2", "ts3.6": "6.0.2", "ts3.9": "6.0.2", "ts3.8": "6.0.2", "ts4.3": "6.0.3", "ts4.9": "6.0.3", "ts4.2": "6.0.3", "ts5.0": "6.0.3", "ts4.8": "6.0.3", "ts4.7": "6.0.3", "ts4.6": "6.0.3", "ts4.5": "6.0.3", "ts4.4": "6.0.3", "ts4.1": "6.0.3", "latest": "8.3.6"}, "versions": {"6.0.0": {"name": "@types/cacheable-request", "version": "6.0.0", "license": "MIT", "_id": "@types/cacheable-request@6.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "86e0f4f3b432cc3098acda97e3b0af8bcc99d657", "tarball": "https://registry.npmjs.org/@types/cacheable-request/-/cacheable-request-6.0.0.tgz", "fileCount": 4, "integrity": "sha512-<PERSON><PERSON><PERSON>Kp18UBsx100v7DGpcYl4JkN6X2X9BaEv2d4A6vygQgkRy9jgSxtwPwVJZ/1ES4ckAaBtgEuI652+wP4YZPQ==", "signatures": [{"sig": "MEUCICOtwFmkxbs5zZXhJ62cFWl12ERU02ebcs+o9ZwcRfyNAiEA2je+c9GToEHWje+b2H4nw48VrsaIxnQ7DPictW2vAh8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8564, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcU9mNCRA9TVsSAnZWagAAWaAP/1XVrVXzXGI0/BJ6X8Db\nBtl9b1ViObtR9EW4OS8OJHJ6ao4QN0dYU79yulkYWVfVBkUnfKDqZm0FL09k\nwjkx1bVeMtEwx8xdgF0vJNuEqOzJ69SzTmBIalV3bGIxHKfpxqj+OlvlDx91\n1CZnXoF+uGmka5A9mAIS7bq885zDElnmupG0ohyXgeAb2waFba43XNRbqzLv\nWUsbSn5pUOQStndv6IylIlfQO55d4FrbnYeIkIBFlKMU9g0QTXpk/dGFNC8s\nAGtfwHswcvB+o/yx1lZdqlWnM6B4OR1GFCoGY4eZdokJciP/m8+bEeoCcPfR\nWVIQghhepckQj5FYC5gU3XVGZf9SPDivGS3r29vdvt0eorybyu7F1XCuWHRl\nOutIFfmSEhHT+rLg9qsO13FdlH08RYLAzgtUCVv8CMhGYu9o0FJ8rPZuAQRP\nTOdHsyvG/neLQrxBg5hjhjf2cyTHWB5rriDpc5M3dFcCJrFiX6NgMzZIX/sJ\n93t1j7AyFE9kolHBb5q+z+wrIAHz6w7KK2mFTyGcHWHpw/RbyorUMGyyvyBG\nAziRvtfVPR2+7430wA+mvaTi9ub0APCTuOW8jsYK5eEr8rzhtaO0yBOti8Aw\n9bG2G7JqvsPwowYGWVOK1kNwPenzf4OCxtxMAexSsobAgWjAPpNpnCmhrGud\nlb7S\r\n=u50l\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for cacheable-request", "directories": {}, "dependencies": {"@types/keyv": "*", "@types/node": "*", "@types/responselike": "*", "@types/http-cache-semantics": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.3", "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_6.0.0_1548999053214_0.921964204240669", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "3acbd7a10d417704ad77b296254534c32d5f2f60927a70ae808d75d0269d8dc3"}, "6.0.1": {"name": "@types/cacheable-request", "version": "6.0.1", "license": "MIT", "_id": "@types/cacheable-request@6.0.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/paulmelnikow", "name": "<PERSON>", "githubUsername": "paulmelnikow"}], "dist": {"shasum": "5d22f3dded1fd3a84c0bbeb5039a7419c2c91976", "tarball": "https://registry.npmjs.org/@types/cacheable-request/-/cacheable-request-6.0.1.tgz", "fileCount": 4, "integrity": "sha512-ykFq2zmBGOCbpIXtoVbz4SKY5QriWPh3AjyU4G74RYbtt5yOc5OfaY75ftjg7mikMOla1CTGpX3lLbuJh8DTrQ==", "signatures": [{"sig": "MEQCIGqqYBT0EN25eIeb2FRXXtPyKvygJx076dpwXEI3Zj24AiBcdSU5lyZC9OK4DXWpvvxPsMeEekrBMKS3sDCyEiIqtg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8945, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcmQKyCRA9TVsSAnZWagAAB1sQAJKb81y8whagTsjGKuVx\nZpkbAlQXDAuUxfk6VYHRoP0kJLqyvb1zSHFcaCIhsqBOn2Wu0DZJWtaEqXTe\n31BUCpTENeX2gzHOz42FyD3a/wkgJzKYbPlOO9G00SnDQ4ReorR8tfgKJDq7\nax/3yNfaSp/9Zlr93BugVBSzj8zRaaI7Pn+7b8xYYv4elvGUAoHoVcBxnxG/\nx7i4ADEbnvhhMcuBj9+Zta7fFEEurMe8QIWUx/+FnB5yvu/vU/47gt1GZTwc\noqFM6bqAD6zv51blYA55Zexf8dWClFiX66tpG7+pKu3EgrAWu0lbTWMSBIlU\nv0pkaG8t3qYfea1McRwblEMUdvUOeqZnaqNDN7eFIsZbHeZkJN0uriXOSfy1\nrbfUqQcWWDcUKE04QwWp6PQ7yfQvKwwTYa/T+qvkRKpOi+B4q3vE3Edz2syI\nMrISSw1RdDvR/1CxU9SGS1zE07FAkmCDBSEG4LOjU7AjuFDp7vOJBQFDBrNy\n7seG6T5O976iWKyPbUKhSmDoP5zkYHctMtrVNUvSOvl/02t3PXdjClAH9GLJ\nzspCY3D2o2QGh3/knUarahXV7nmmYgmdMK6CHYFQRYG3sHqsayum/kZEGaux\n21WGgPMrE4OJeh8uR9EG1b/mYJ4raYKV6IlAi9z6EJAIUTIh8CEGFigcmXLb\nZr/5\r\n=C7ui\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/cacheable-request"}, "description": "TypeScript definitions for cacheable-request", "directories": {}, "dependencies": {"@types/keyv": "*", "@types/node": "*", "@types/responselike": "*", "@types/http-cache-semantics": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.3", "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_6.0.1_1553531569503_0.24259323389549525", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "c6afce5b9ca6ee2952549da29e2d4af2bd367dbf44a175a005063192d46b7814"}, "6.0.2": {"name": "@types/cacheable-request", "version": "6.0.2", "license": "MIT", "_id": "@types/cacheable-request@6.0.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/paulmelnikow", "name": "<PERSON>", "githubUsername": "paulmelnikow"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/cacheable-request", "dist": {"shasum": "c324da0197de0a98a2312156536ae262429ff6b9", "tarball": "https://registry.npmjs.org/@types/cacheable-request/-/cacheable-request-6.0.2.tgz", "fileCount": 4, "integrity": "sha512-B3xVo+dlKM6nnKTcmm5ZtY/OL8bOAOd2Olee9M1zft65ox50OzjEHW91sDiU9j6cvW8Ejg1/Qkf4xd2kugApUA==", "signatures": [{"sig": "MEUCIQCTjqeIBb/7kyyvrbQsW3f4PHyzNZtIq8a7N51WsLETEAIgAKqG6hRQ6rNrFqQgdlTFGsztt8vJDB3NOh2gWB0qSGw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9271, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5J+OCRA9TVsSAnZWagAAfLUQAJETrku6MCePPEthskFO\nGqr8ru4clqHrH3iXALfO8jQlbFhKsHDoeI3T4g4yf77qjWDPyomnlvOUqeIo\n6bNGTLrhB5KNl4G9FFxC3E8jp3WBrlfdmoMXhEnEB3GrzhMa55LptQPa+OIn\nZ4UQLZ63bYrBzoaU33rQXti9v2vtL7fYOLtMmtKOh+6dZ11Pn1C7eYcURNT6\n+/f7QK6DS4iqJiG9kCJKWlZvPVDyIpKHAWSCzdz4xznEuKZXP+36row+SgJ0\nZW29suFL55bx4gI9tx59ux5bvF6j9P0sUq26Lwa70234TIv/wLstKViXwuQi\nv+AZaV837mTpolstKLit6D/hadfv0GBWcshSPe2R10j0wUfrIG7pXM4cwCiJ\n0Qvn4XM4G13cO3d/iKJnMaEE0Ie7K9PXgxf+SFY2yAwawrQvmXXLeAowcJ4p\nK4Z3RFzEzD1NjSp2Zdsvx7j1/h/hTueYJv1Q/M8IJBN/KExcZO6ZiYigNng7\nrpA0DUNyFAU1FSvaTgmmQG2IXZ/4wy8bHFn11d/lq4sycLF+j/kWOMcXOcnA\nxWrZhUV7oWyqf/U/wxfA3jgScyIz/FLpsJgdI5WYCmwymIFHO37lTmuxH39P\nsDxKRULK1nFlRKGhxDj9jz+7qcpYD6fJKt+5Evk1CShjKo1Xx1iKFQ2b5f6N\nW8aJ\r\n=nU3G\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/cacheable-request"}, "description": "TypeScript definitions for cacheable-request", "directories": {}, "dependencies": {"@types/keyv": "*", "@types/node": "*", "@types/responselike": "*", "@types/http-cache-semantics": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_6.0.2_1625595789757_0.6196358477455672", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "5bc07db78df7c21a4d6250dbb806ad088df376f7ed46c63b60bb0e08488dcdc4"}, "8.3.1": {"name": "@types/cacheable-request", "version": "8.3.1", "license": "MIT", "_id": "@types/cacheable-request@8.3.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "58df53b106285774c102c8dbdca0cccadf0ad674", "tarball": "https://registry.npmjs.org/@types/cacheable-request/-/cacheable-request-8.3.1.tgz", "fileCount": 4, "integrity": "sha512-ZPI1I9bg9ZpKgVP28Iom1Tev64JkIVgEdM7GnQeXgYNthjgWkhA6lEEhkl68MJ1WFC3oEy6ly6BRmytxguDVlg==", "signatures": [{"sig": "MEQCIDcWHb7L8OliC5zIwJj+nfE0R44LiB4/jxLkFQRfYYU4AiBBlUnkyIDOVrOnTzbyoyXjO2WTUrLE7B0rkeZCwzOaIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1813, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFIXPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo73g//at0GAN1rfyuQJlNdy9FMrwmczT2t6mZRRMy2LdeKgQn3SpAC\r\nrLdc92P+pbSaS7wR4qk5RiMjENbsbILnxXg0IY84p59FyAhuX0PaPmQc1d3S\r\nxFApA5mj83nlK8r5voBPMUgR9vHFOUXqpIwk34we86Rd0/CWjxPjAw3eA71q\r\noRvbEYyXTK7o+kQQds/VGKOj+mek6vK+sJ71xc2e+NZF2wPozrkZZXFbrgNp\r\n2VbjqSYYMnUDvW9577bL7lZmXbQJzxa9J1rcG2uErijAjyLcAAwKk2ehJQOd\r\nujpjGvghfRX1kil3dokWrG9Ds66PQRD806LeECI/hUH2rFrtEjgqj87E9Wz/\r\ngoDSbetDHQw1DrTnnge7DKHDSWap23a8O1+DPoKH2E5jqVn4A13XwBGyd3YY\r\n6KUNDvbV2jqtIkt/xP96o+tc3C2G2FMH6424QeAFMgrChWneYS7GXhiMPAwB\r\n8K6NmN8eveIYb8kFPEbo7u0hQPX7uMYG2Qy52M7Dr8I3e4Ut16igKWmIcd3G\r\nhmqBMAXcMPevvytLwA2+qxyGhZCkt0u3rLGvUjsxvidnMFMPbr5TFxDUhGsU\r\n3Iqqs1VcRTfiAICP2WqWmY6aKAj4V4c3XYrs2q9l3BBb9gn64Jzn4i1dt0XG\r\nZXvEjGB7juO/fsMb65JJZNsdG1pQmBFUHOE=\r\n=PjB/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "deprecated": "This is a stub types definition. cacheable-request provides its own type definitions, so you do not need this installed.", "description": "Stub TypeScript definitions entry for cacheable-request, which provides its own types definitions", "directories": {}, "dependencies": {"cacheable-request": "*"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_8.3.1_1662289359005_0.3079225339973488", "host": "s3://npm-registry-packages"}}, "6.0.3": {"name": "@types/cacheable-request", "version": "6.0.3", "license": "MIT", "_id": "@types/cacheable-request@6.0.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/paulmelnikow", "name": "<PERSON>", "githubUsername": "paulmelnikow"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/cacheable-request", "dist": {"shasum": "a430b3260466ca7b5ca5bfd735693b36e7a9d183", "tarball": "https://registry.npmjs.org/@types/cacheable-request/-/cacheable-request-6.0.3.tgz", "fileCount": 5, "integrity": "sha512-IQ3EbTzGxIigb1I3qPZc1rWJnH0BmSKv5QYTalEwweFvyBDLSAe24zP0le/hyi7ecGfZVlIVAg4BZqb8WBwKqw==", "signatures": [{"sig": "MEYCIQCThafO2E26TF3VJIVX/oIQ3597Eg8+D/q7M7zXdCfqlAIhAM3058Ndcj6OjurZ66C6c2Pb7vx21aj9NCZfJoSz1xOr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9281, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJja9Y3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpDgw//Xm6yGQvzQtdJ0YOMXBbRuYoJmv75JRFB/tYIfCShcU5IC+h8\r\nGruQZxu3059rZHoqPzE4fqBEho6ikwQh0iQ0wKtWcZ68bXqpeRXkIfVxOB+l\r\nC/gqYk35OA4vaKXy4TQFxCLoaQvh2dV3R1kPQr70xoVmyv3g5Z38t9gT+/+O\r\nGSx4XELP2zDnQ+pqnDBGjlSdqa8cUasRmUSeoknozplssdLpxdxC0HpFzYV+\r\nbkkFm4kdlIYddC7Ah7X1UjegMIHY9hsCpalLN0TcBORz9xqP5ohKypUbuqYR\r\n+E/XNPyVkASg9ae+FTJq+2AX59WBGHI1GV9anTBtq8jLxt3SNFy4wvUKvNix\r\njwjyOT4Fj4acc1oCXpWbPxPAlJOZ6/lrCkWncmY/32HWQwXyu82fh04ciaHS\r\nORutZ460FdnVL1/xuS/MXYjDkGhFb/yx6YJJoZ/aY7CVbMKmhmjgybTNZ3v2\r\nIi+nQbJigNULd/upe6DPCJnEtB8JS+q1nWOz4zTeRwo2C2nl3Lr7d7QR2A4V\r\nHP68oZgrKW24cFj9igkMb7edAKyJHa6sYy/Qe3hVH5Gop9p8tk6w4mMzFtKq\r\n7LO8Fvo6pwTfv3VuWOyjv2BA8TW6P6WE7z0tvZ6UTzv4VfBXmhHFQfeS/C1d\r\nM0Bqs+pkQZO2DZ6fxLXa7iK2DPqtyRQKrL4=\r\n=TPq8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/cacheable-request"}, "description": "TypeScript definitions for cacheable-request", "directories": {}, "dependencies": {"@types/keyv": "^3.1.4", "@types/node": "*", "@types/responselike": "^1.0.0", "@types/http-cache-semantics": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.1", "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_6.0.3_1668011574865_0.8141265611548882", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "9345f1216c9d26f9046880c34f6329b2874405d68cf13d1f1f771fbb4d96549f"}, "8.3.2": {"name": "@types/cacheable-request", "version": "8.3.2", "license": "MIT", "_id": "@types/cacheable-request@8.3.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "8e0fe22accd57dcf9e66e02513c1173ed80af495", "tarball": "https://registry.npmjs.org/@types/cacheable-request/-/cacheable-request-8.3.2.tgz", "fileCount": 4, "integrity": "sha512-iA77e7Y/vMmEXTqhRFMLLu2h9j6Mww/U1nl56aNTgJ6r7Is/IPnJGG86Xb15wo5nG2u7D8Hruft8zeergKVNog==", "signatures": [{"sig": "MEUCIQDbgz+/dWgXmEejF3qLh+JBiD1w4LH/rjUwV5bHLFRMdwIgeq35wQHZGt/dQtd8FWp/IvlbvkMFB23TakzdG41EmaY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1813, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjfmQ5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqjJxAAlHXNEsTpPOk9XZGvIW/rt2j8NSxqFp2BfwjCGxyL4UJItw4i\r\nnlAyyk08uoNJn2OwjuPr7XEAD7Ydh+oDqbazfcdjYwAq1BTSS6EiOzEtNs/F\r\npZd68AlQAW5D1yyjE0Nmc/rfF6X0vyFOUK+Xq+wc8U8/+09dSpd3w7D2rch3\r\n9s7pDh93xvvF7DQnI1S7Dsdd4aYkbTVIXWP1b61xiiEZNMQUzQpftfpjHjUV\r\nEYOUwMoCahx0Zst1hfavF9VDPCAZbomups94SQpQI2UUI6qiqz2AtJzkM1W8\r\nyaUsnkJKq4kCkOzK42AW5Ki4lksocE53fuEQ90s/MGKn4veJPJYPfwiJPL7n\r\nqxs0svhbG24P+pAJWRnR/lNeEXCurAn05bQ9WmcKlQupTF2hDiX4DU4Ya0cx\r\nvj3FgHetA3iYsbBJi38mgk1nymNIXI4qQKp3ddCVYeWj1R08JTSAvlSUdGyz\r\nqMuZHVBd7xOrLO32ZtK5y1Y7K5O6OATHqp2HMEXbSh14hvG6hvk8mHik1CKI\r\nAHrjtIbEiNOpEnNNtdprLIrQEOmnCbODQ4l4nG3xYolS8y95kE3kBq3Nfh96\r\n1FETNz6DBlpJOK2w+oBFh19jT4HzKHaFCKqOX/ySn8MGAh50cfEs2/LZtpHi\r\nvbY7Q21ZW+VgFWcI/Vz6Uz5i+9SOM3CE+uw=\r\n=21wn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "deprecated": "This is a stub types definition. cacheable-request provides its own type definitions, so you do not need this installed.", "description": "Stub TypeScript definitions entry for cacheable-request, which provides its own types definitions", "directories": {}, "dependencies": {"cacheable-request": "*"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_8.3.2_1669227577119_0.32355858319097064", "host": "s3://npm-registry-packages"}}, "8.3.3": {"name": "@types/cacheable-request", "version": "8.3.3", "license": "MIT", "_id": "@types/cacheable-request@8.3.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "a0d114da0341d1bc833c31070e4f501b9aa80b2d", "tarball": "https://registry.npmjs.org/@types/cacheable-request/-/cacheable-request-8.3.3.tgz", "fileCount": 4, "integrity": "sha512-1AUDuqvFVWM2l0IsjAnISq8PlTnqEfelKERmp7+GzhRBt8v66A0G8+w2jCT+pNWDtHxWV5Ty25Dkz8Ie6/Jc3g==", "signatures": [{"sig": "MEQCIAm+uyQJB07VScol6H4vvGDEA1YsPTUAZTLbYIlRPjQiAiATPuaq+2xUyUx5sl37dJDUggtdhVuBgoxCiK2ZMfg43Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1805}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "deprecated": "This is a stub types definition. cacheable-request provides its own type definitions, so you do not need this installed.", "description": "Stub TypeScript definitions entry for cacheable-request, which provides its own types definitions", "directories": {}, "dependencies": {"cacheable-request": "*"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_8.3.3_1729644122993_0.9812735331953168", "host": "s3://npm-registry-packages"}}, "8.3.4": {"name": "@types/cacheable-request", "version": "8.3.4", "license": "MIT", "_id": "@types/cacheable-request@8.3.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "dd22741ad94e6a9266f87b6f7e591d3046798bc4", "tarball": "https://registry.npmjs.org/@types/cacheable-request/-/cacheable-request-8.3.4.tgz", "fileCount": 4, "integrity": "sha512-lX1jtECNx91f81O1BXu6sEbxs4ECfZCHbF8q5bA4iOSMmZCDqhqRS/qt48egKMdjnz7PBm89YYR/628hWf8wxg==", "signatures": [{"sig": "MEYCIQDtL26AxwBOo0OPctM3d6LuOhJY+NgaHVwsuYox6q7NgQIhAILYuOhZTIYuzx8zN1HYW781hLjGnL4z/aUXvle7Mr8z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1805}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "deprecated": "This is a stub types definition. cacheable-request provides its own type definitions, so you do not need this installed.", "description": "Stub TypeScript definitions entry for cacheable-request, which provides its own types definitions", "directories": {}, "dependencies": {"cacheable-request": "*"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_8.3.4_1729645621648_0.48167549418156397", "host": "s3://npm-registry-packages"}}, "8.3.5": {"name": "@types/cacheable-request", "version": "8.3.5", "license": "MIT", "_id": "@types/cacheable-request@8.3.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "7d1f21fc40b084191f586715932da4e81920ed86", "tarball": "https://registry.npmjs.org/@types/cacheable-request/-/cacheable-request-8.3.5.tgz", "fileCount": 4, "integrity": "sha512-817ra8NC3FsIJ4ILPeRFwIELHp/QDqfBnyRVvVcZn110bmJd/V5enSuMuCMS+EkSO6kFNsrw95cmUir1eDGOZg==", "signatures": [{"sig": "MEQCIA1+HntX12186AYorCEogg8sv8J7/xoJCcoDwQ6ZeGKMAiBWsTmqahd2HpWT6HmZ654SxZyS39gEpjxelJPu0MpVDw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1805}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "deprecated": "This is a stub types definition. cacheable-request provides its own type definitions, so you do not need this installed.", "description": "Stub TypeScript definitions entry for cacheable-request, which provides its own types definitions", "directories": {}, "dependencies": {"cacheable-request": "*"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/cacheable-request_8.3.5_1729647867053_0.2715796484904165", "host": "s3://npm-registry-packages"}}, "8.3.6": {"name": "@types/cacheable-request", "version": "8.3.6", "description": "Stub TypeScript definitions entry for cacheable-request, which provides its own types definitions", "main": "", "scripts": {}, "license": "MIT", "dependencies": {"cacheable-request": "*"}, "deprecated": "This is a stub types definition. cacheable-request provides its own type definitions, so you do not need this installed.", "_id": "@types/cacheable-request@8.3.6", "dist": {"integrity": "sha512-490ZO711a+yBqLd7O2PIr4XKdeFftV/eIyragGl7/ki2piOhBt4rmDxTe/yDzxgMcxy+Kis3QtV2w8C9X38ZZg==", "shasum": "e0547cf6eb659a9669f5be421e543971664c1db2", "tarball": "https://registry.npmjs.org/@types/cacheable-request/-/cacheable-request-8.3.6.tgz", "fileCount": 4, "unpackedSize": 1805, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDKvFGmWsPy5qkexd8K1KpyRPYt1bAKXYaiSblDSSonbQIgL47zIZYAEPDAtytZqWsLqX7qlBeeYZ9vi1JWY2ECv6Y="}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cacheable-request_8.3.6_1729651216923_0.2246302391964341"}, "_hasShrinkwrap": false}}, "time": {"created": "2019-02-01T05:30:53.035Z", "modified": "2024-10-23T02:40:17.245Z", "6.0.0": "2019-02-01T05:30:53.335Z", "6.0.1": "2019-03-25T16:32:49.636Z", "6.0.2": "2021-07-06T18:23:09.886Z", "8.3.1": "2022-09-04T11:02:39.242Z", "6.0.3": "2022-11-09T16:32:55.083Z", "8.3.2": "2022-11-23T18:19:37.343Z", "8.3.3": "2024-10-23T00:42:03.182Z", "8.3.4": "2024-10-23T01:07:01.838Z", "8.3.5": "2024-10-23T01:44:27.264Z", "8.3.6": "2024-10-23T02:40:17.094Z"}, "license": "MIT", "description": "Stub TypeScript definitions entry for cacheable-request, which provides its own types definitions", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "readme": "[object Object]", "readmeFilename": ""}