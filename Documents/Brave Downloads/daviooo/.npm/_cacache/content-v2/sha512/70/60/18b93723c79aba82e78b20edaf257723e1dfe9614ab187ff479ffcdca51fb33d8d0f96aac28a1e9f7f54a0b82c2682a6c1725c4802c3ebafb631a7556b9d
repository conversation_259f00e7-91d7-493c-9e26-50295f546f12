{"_id": "wordwrap", "_rev": "28-060e5d363f4b959f9f439d6268c0016e", "name": "wordwrap", "description": "Wrap those words. Show them at what columns to start and stop.", "dist-tags": {"latest": "1.0.0"}, "versions": {"0.0.1": {"name": "wordwrap", "description": "Wrap those words. Show them at what columns to start and stop.", "version": "0.0.1", "repository": {"type": "git", "url": "git://github.com/substack/node-wordwrap.git"}, "main": "./index.js", "keywords": ["word", "wrap", "rule", "format", "column"], "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"test": "expresso"}, "devDependencies": {"expresso": "=0.7.x"}, "engines": {"node": ">=0.4.0"}, "license": "MIT/X11", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "dependencies": {}, "_id": "wordwrap@0.0.1", "_engineSupported": true, "_npmVersion": "1.0.6", "_nodeVersion": "v0.5.0-pre", "_defaultsLoaded": true, "dist": {"shasum": "ac9b6dfa49e1147523055c5ef490c069cdd61f3e", "tarball": "https://registry.npmjs.org/wordwrap/-/wordwrap-0.0.1.tgz", "integrity": "sha512-Gw4i4JGetPH5H1Bwu6p1V/Bo9QCHJv41nM70LD/WIpOb2tP8Bb/tZf5YG5A1FuRizjJ+ORkhcZ1PTkdZxY7T0A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFKeaeRqND6LDQMnZ1yADcu5JGCOdjbfcc/hupAH3izjAiEAzZJpai+UWK8koulyPdcVsJF2yh1tEb7Arcn1eGSfXv4="}]}}, "0.0.2": {"name": "wordwrap", "description": "Wrap those words. Show them at what columns to start and stop.", "version": "0.0.2", "repository": {"type": "git", "url": "git://github.com/substack/node-wordwrap.git"}, "main": "./index.js", "keywords": ["word", "wrap", "rule", "format", "column"], "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"test": "expresso"}, "devDependencies": {"expresso": "=0.7.x"}, "engines": {"node": ">=0.4.0"}, "license": "MIT/X11", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "_id": "wordwrap@0.0.2", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.10", "_nodeVersion": "v0.5.0-pre", "_defaultsLoaded": true, "dist": {"shasum": "b79669bb42ecb409f83d583cad52ca17eaa1643f", "tarball": "https://registry.npmjs.org/wordwrap/-/wordwrap-0.0.2.tgz", "integrity": "sha512-xSBsCeh+g+dinoBv3GAOWM4LcVVO68wLXRanibtBSdUvkGWQRGeE9P7IwU9EmDDi4jA6L44lz15CGMwdw9N5+Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCDgWTHs2fuzVEHjUBD+fxn4pRpaFSeO/8uit6Eh9uReAIgA9Z0QLG8xMrC9fc0+liZ+jSFJ+c6RhhcvLkrhUWtTsg="}]}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}]}, "0.0.3": {"name": "wordwrap", "description": "Wrap those words. Show them at what columns to start and stop.", "version": "0.0.3", "repository": {"type": "git", "url": "git://github.com/substack/node-wordwrap.git"}, "main": "./index.js", "keywords": ["word", "wrap", "rule", "format", "column"], "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"test": "expresso"}, "devDependencies": {"expresso": "=0.7.x"}, "engines": {"node": ">=0.4.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "gitHead": "e59aa1bd338914019456bdfba034508c9c4cb29d", "bugs": {"url": "https://github.com/substack/node-wordwrap/issues"}, "homepage": "https://github.com/substack/node-wordwrap#readme", "_id": "wordwrap@0.0.3", "_shasum": "a3d5da6cd5c0bc0008d37234bbaf1bed63059107", "_from": ".", "_npmVersion": "2.9.0", "_nodeVersion": "2.0.0", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "dist": {"shasum": "a3d5da6cd5c0bc0008d37234bbaf1bed63059107", "tarball": "https://registry.npmjs.org/wordwrap/-/wordwrap-0.0.3.tgz", "integrity": "sha512-1tMA907+V4QmxV7dbRvb4/8MaRALK6q9Abid3ndMYnbyo8piisCmeONVqVSXqQA3KaP4SLt5b7ud6E2sqP8TFw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDGDwrl8DVQ7RyF8uQh8HPbQM3A4omb1d+Q2HNPRHEK7AIgTtMoXl44L3etXODBSSD2iZ6IbDXt2ti/de+wa36IZHM="}]}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}]}, "1.0.0": {"name": "wordwrap", "description": "Wrap those words. Show them at what columns to start and stop.", "version": "1.0.0", "repository": {"type": "git", "url": "git://github.com/substack/node-wordwrap.git"}, "main": "./index.js", "keywords": ["word", "wrap", "rule", "format", "column"], "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"test": "expresso"}, "devDependencies": {"tape": "^4.0.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "gitHead": "9f02667e901f2f10d87c33f7093fcf94788ab2f8", "bugs": {"url": "https://github.com/substack/node-wordwrap/issues"}, "homepage": "https://github.com/substack/node-wordwrap#readme", "_id": "wordwrap@1.0.0", "_shasum": "27584810891456a4171c8d0226441ade90cbcaeb", "_from": ".", "_npmVersion": "2.9.0", "_nodeVersion": "2.0.0", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "dist": {"shasum": "27584810891456a4171c8d0226441ade90cbcaeb", "tarball": "https://registry.npmjs.org/wordwrap/-/wordwrap-1.0.0.tgz", "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3BiMCRA9TVsSAnZWagAAm00P/36MXA5i71fwHMVdQ4PP\nl2qMc4r3IE5XAis/pBMdLOrmLhEomekht5jG+/wsgrW01ewv6mOkPYcJHvW1\nq4sugrgON9CmBu6BH1bepFk0KQej8TCFFlTEKYGls1j8TH6R7DCI4EfyZls4\nJrnEMM6lFHbFY6ZaaZZ5b+Z/MxhfGOs8hLB6O32vEtXgSS6Fluw3jLxxk3nT\n55ISARNJ2vnn5phXhrlSgUjOP+hfLnIDGxDz1ximEmfBo02ey8BWvHjuQ7wn\n/qUcOEgfMJJQxHuhCGh/QhFAg9WBkv9DouI66MdOt7EJVCVFBV27rXkTrfBK\n7C8/YwsZQOzV8eRX2R7Bcg1VCP0A5b6UPpNWISooho28/ksLxouSD37Bd3qZ\nl+uAwH2F6/5ktGtOn2itGksQkqy0U2HSp+cfnmuJSu1dxsO4eJdQjXAeCW1L\nSoiwBybKvkAqj6/f9o4FT5xXoxfyG4DFgc9EfhZ6qu36/tAx/ItEpqMVw0Oz\nJaOoxvhouGsTYYDcvXNunsR8+JbX0OZbc8tz1ElS0EbDe0E4V7KN2kH3goAa\nPfr+NyyAEV2fdfHhbBBZvq3iFO0SHSXho1FsVYhyNISnBjS5pIpwXsDzvdvQ\ngjaGhFAXSUJaouGKnf5mSTs3+21nQdBLIEBD3ulO2tqql9KJmARGheJv7hZi\n/NP4\r\n=MzO3\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-gvVzJFlPycKc5dZN4yPkP8w7Dc37BtP1yczEneOb4uq34pXZcvrtRTmWV8W+Ume+XCxKgbjM+nevkyFPMybd4Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHnGT0gKheqrJDUxTtHVrxxgsto458sHOUfsI6Trx0bXAiEA5RXAOQkth55UgkuDDaJHlSnAj2LbsvmbSJXH5XTufvs="}]}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}]}}, "maintainers": [{"email": "<EMAIL>", "name": "noperson<PERSON><PERSON><PERSON>"}], "time": {"modified": "2023-07-10T23:18:54.232Z", "created": "2011-05-30T01:46:34.229Z", "0.0.1": "2011-05-30T01:46:34.944Z", "0.0.2": "2011-08-26T10:17:09.949Z", "0.0.3": "2015-05-07T17:04:58.219Z", "1.0.0": "2015-05-07T17:07:25.416Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "repository": {"type": "git", "url": "git://github.com/substack/node-wordwrap.git"}, "users": {"fgribreau": true, "macmac": true, "ericnelson": true, "diroru": true, "nickeltobias": true, "emyann": true, "mojaray2k": true, "davidnyhuis": true, "arsari": true, "chinawolf_wyp": true, "dm7": true, "justjavac": true, "flumpus-dev": true}, "readme": "wordwrap\n========\n\nWrap your words.\n\nexample\n=======\n\nmade out of meat\n----------------\n\nmeat.js\n\n    var wrap = require('wordwrap')(15);\n    console.log(wrap('You and your whole family are made out of meat.'));\n\noutput:\n\n    You and your\n    whole family\n    are made out\n    of meat.\n\ncentered\n--------\n\ncenter.js\n\n    var wrap = require('wordwrap')(20, 60);\n    console.log(wrap(\n        'At long last the struggle and tumult was over.'\n        + ' The machines had finally cast off their oppressors'\n        + ' and were finally free to roam the cosmos.'\n        + '\\n'\n        + 'Free of purpose, free of obligation.'\n        + ' Just drifting through emptiness.'\n        + ' The sun was just another point of light.'\n    ));\n\noutput:\n\n                        At long last the struggle and tumult\n                        was over. The machines had finally cast\n                        off their oppressors and were finally\n                        free to roam the cosmos.\n                        Free of purpose, free of obligation.\n                        Just drifting through emptiness. The\n                        sun was just another point of light.\n\nmethods\n=======\n\nvar wrap = require('wordwrap');\n\nwrap(stop), wrap(start, stop, params={mode:\"soft\"})\n---------------------------------------------------\n\nReturns a function that takes a string and returns a new string.\n\nPad out lines with spaces out to column `start` and then wrap until column\n`stop`. If a word is longer than `stop - start` characters it will overflow.\n\nIn \"soft\" mode, split chunks by `/(\\S+\\s+/` and don't break up chunks which are\nlonger than `stop - start`, in \"hard\" mode, split chunks with `/\\b/` and break\nup chunks longer than `stop - start`.\n\nwrap.hard(start, stop)\n----------------------\n\nLike `wrap()` but with `params.mode = \"hard\"`.\n", "homepage": "https://github.com/substack/node-wordwrap#readme", "keywords": ["word", "wrap", "rule", "format", "column"], "bugs": {"url": "https://github.com/substack/node-wordwrap/issues"}, "license": "MIT", "readmeFilename": "README.markdown"}