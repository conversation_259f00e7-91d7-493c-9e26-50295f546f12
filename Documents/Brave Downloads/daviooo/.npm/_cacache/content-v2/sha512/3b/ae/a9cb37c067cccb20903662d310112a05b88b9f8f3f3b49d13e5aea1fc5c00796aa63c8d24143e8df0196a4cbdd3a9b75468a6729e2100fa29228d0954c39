{"_id": "simple-concat", "_rev": "6-f47c83b63d9557a7075a787a7b0059be", "name": "simple-concat", "description": "Super-minimalist version of `concat-stream`. Less than 15 lines!", "dist-tags": {"latest": "1.0.1"}, "versions": {"1.0.0": {"name": "simple-concat", "description": "Super-minimalist version of `concat-stream`. Less than 15 lines!", "version": "1.0.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org/"}, "bugs": {"url": "https://github.com/feross/simple-concat/issues"}, "dependencies": {}, "devDependencies": {"standard": "^6.0.8", "tape": "^4.0.0"}, "homepage": "https://github.com/feross/simple-concat", "keywords": ["concat", "concat-stream", "concat stream"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/simple-concat.git"}, "scripts": {"test": "standard && tape test/*.js"}, "gitHead": "7ea5b5aecfbbbf554c66474dd43a2271373640ed", "_id": "simple-concat@1.0.0", "_shasum": "7344cbb8b6e26fb27d66b2fc86f9f6d5997521c6", "_from": ".", "_npmVersion": "2.14.20", "_nodeVersion": "4.4.1", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "7344cbb8b6e26fb27d66b2fc86f9f6d5997521c6", "tarball": "https://registry.npmjs.org/simple-concat/-/simple-concat-1.0.0.tgz", "integrity": "sha512-pgxq9iGMSS24atefsqEznXW1Te610qB4pwMdrEg6mxczHh7sPtPyiixkP/VaQic8JjZofnIvT7CDeKlHqfbPBg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICvefrH6+kDiJtf/pfa5yZoSNmKaBR2YaZYgv12dC+YXAiEA93+3uegFJmIpDCbXFk905mrZeXYFNqqOFIo44TeN5MI="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/simple-concat-1.0.0.tgz_1461229969789_0.8977642937097698"}, "directories": {}}, "1.0.1": {"name": "simple-concat", "description": "Super-minimalist version of `concat-stream`. Less than 15 lines!", "version": "1.0.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://feross.org"}, "bugs": {"url": "https://github.com/feross/simple-concat/issues"}, "dependencies": {}, "devDependencies": {"standard": "*", "tape": "^5.0.1"}, "homepage": "https://github.com/feross/simple-concat", "keywords": ["concat", "concat-stream", "concat stream"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/simple-concat.git"}, "scripts": {"test": "standard && tape test/*.js"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "gitHead": "44134bf16667b6006a254135d5c8c76ea96823d4", "_id": "simple-concat@1.0.1", "_nodeVersion": "12.18.2", "_npmVersion": "6.14.6", "dist": {"integrity": "sha512-cSFtAPtRhljv69IK0hTVZQ+OfE9nePi/rtJmw5UjHeVyVroEqJXP1sFztKUy1qU+xvz3u/sfYJLa947b7nAN2Q==", "shasum": "f46976082ba35c2263f1c8ab5edfe26c41c9552f", "tarball": "https://registry.npmjs.org/simple-concat/-/simple-concat-1.0.1.tgz", "fileCount": 6, "unpackedSize": 4624, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfESrICRA9TVsSAnZWagAAj24P/1vvSpzsf82E7+dCCRkS\nhcoysvOFrBkEfOKPMW9CpEXtZr3sPcblmcSunP4Arrr2APn5f2eol7DI2nyy\nci4qIabjYWCSivAr6MayxO/L93GM+iR/LB9bciMlw2mwV7TNyF0V6VXwk0v8\nEKM7N9Tcx3NXLLaF7wXdWlzDW7uRIGDUUQmxGnSwrAaG0RQG01/MMGE/zzyu\naVuC7td9Hscxt2kvq72ZvrUre1JybJt/48doSkR//fORXGWrUdE3kqyRM/b6\nwjAuYKHqLbzMwK1SrgUJQgdhOQDVgQ4H/6BvpVOKHO3faDe0yRxLC9rXziiC\npkZ9ZMLkWB0yp0f3dHy4y9+jYJnxOOeKgQ2UJ6Tsm+GZC77xg9XDUb6ofueZ\nUr2ROIg3BMWCWewADR2ukgo7VKc5C7+tkI0VgbF9TEyPmhGxos/oTf1URJyp\nCxn0ch1RT/GpaJRN7c8bN0qGahqDikHhHMGyNsKBKt/zBPtHWS9okgBO9wMn\nt9LxhCLYKSLThLhqCeG7dATctzEcvqipffHQbL+448ZWRendhVhC5L/d0XCn\nt2F0MTFdkRISuFnmLjrQipLPCd3y/ixZmvdRN6qGGLcOUucLW1FXKxABuiYW\n3ZL1fC14KpycLfEhP7AfyqmJf58W1tya2JotzagsyQYYTygSdg2fV0WRxuXa\netBF\r\n=7XYd\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIECOSMdfsaO2NGCAfUq8R8FG4mciDnt3QOIkOKQMOIPOAiEAxGjpeIwdfJM5jkUZtCwSDnv5f8khLi+Y8+BB8MntZww="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/simple-concat_1.0.1_1594960584449_0.8623043576224503"}, "_hasShrinkwrap": false}}, "readme": "# simple-concat [![travis][travis-image]][travis-url] [![npm][npm-image]][npm-url] [![downloads][downloads-image]][downloads-url] [![javascript style guide][standard-image]][standard-url]\n\n[travis-image]: https://img.shields.io/travis/feross/simple-concat/master.svg\n[travis-url]: https://travis-ci.org/feross/simple-concat\n[npm-image]: https://img.shields.io/npm/v/simple-concat.svg\n[npm-url]: https://npmjs.org/package/simple-concat\n[downloads-image]: https://img.shields.io/npm/dm/simple-concat.svg\n[downloads-url]: https://npmjs.org/package/simple-concat\n[standard-image]: https://img.shields.io/badge/code_style-standard-brightgreen.svg\n[standard-url]: https://standardjs.com\n\n### Super-minimalist version of [`concat-stream`](https://github.com/maxogden/concat-stream). Less than 15 lines!\n\n## install\n\n```\nnpm install simple-concat\n```\n\n## usage\n\nThis example is longer than the implementation.\n\n```js\nvar s = new stream.PassThrough()\nconcat(s, function (err, buf) {\n  if (err) throw err\n  console.error(buf)\n})\ns.write('abc')\nsetTimeout(function () {\n  s.write('123')\n}, 10)\nsetTimeout(function () {\n  s.write('456')\n}, 20)\nsetTimeout(function () {\n  s.end('789')\n}, 30)\n```\n\n## license\n\nMIT. Copyright (c) [Feross Aboukhadijeh](http://feross.org).\n", "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "time": {"modified": "2022-06-26T19:33:45.106Z", "created": "2016-04-21T09:12:52.546Z", "1.0.0": "2016-04-21T09:12:52.546Z", "1.0.1": "2020-07-17T04:36:24.584Z"}, "homepage": "https://github.com/feross/simple-concat", "keywords": ["concat", "concat-stream", "concat stream"], "repository": {"type": "git", "url": "git://github.com/feross/simple-concat.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://feross.org"}, "bugs": {"url": "https://github.com/feross/simple-concat/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"monjer": true}}