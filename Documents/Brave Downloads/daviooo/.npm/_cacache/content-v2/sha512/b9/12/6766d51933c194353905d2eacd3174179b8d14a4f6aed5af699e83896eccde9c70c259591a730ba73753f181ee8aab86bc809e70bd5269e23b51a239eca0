{"name": "sigstore", "dist-tags": {"latest": "3.1.0"}, "versions": {"0.0.0": {"name": "sigstore", "version": "0.0.0", "dist": {"shasum": "de9d47c51c7fb6a07a9f979e6c4ad7ae824510b7", "tarball": "https://registry.npmjs.org/sigstore/-/sigstore-0.0.0.tgz", "fileCount": 3, "integrity": "sha512-LofDh0K3DWkkPM39pJODrj5yqLABFBFXfKSeJlON+pgV2i6DGU1/GzorpWdc3jpo6dy3AhoLXaZTqOD3y81BFQ==", "signatures": [{"sig": "MEQCIEpxpB1vmdm4Oqga0Q5843LpfGj72xJ9AEheVdg6eh0/AiA5I3vfMVpilJIb39Ib1SvoNTWu/Hf0vLCcWr9EN5N8vQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3133, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi8XUmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqPyg/+IOim3ETyt1pm3nJ9YxukzK+Nmnj54BPG3UZ4xGqoRLUraGi9\r\nwhn2odke8DJGw0UY8dyDlrvRuP+U9oljCGgT+xZJtz03iOrvKbs+RDHsUZLv\r\nr9sYTB8wGcp0DR9BPYhFkh1k3Crx+V9pozsGx9RU/+5E6UWemX2oEkaoHfoI\r\ncryRpEzl3YPig0URcWNKwpEFRcBlgfepMxiq4qdVaGhnLdh6GxcHn9UR7XPJ\r\nz+ngXzhKpWsXiz0xECiMGp7OS8Kl8EVkiG/dGd7UBYyvjHlr+kPNUtCsRTgk\r\neNJU2XYfLj0h4LdAwXhGROMt4besyH0sms+GaKppOhQdYH4zsFCE+6v5hxhT\r\n/pO9dhX5URzyHPacRisdrApHgOcuDV02inXOm9k3xqcPzv/ZeGmsPLvr0S2h\r\nfW9rMsUD9YKoic8CCq743cTJDTc7DGfG+Vn0V4iizKtkviXHUVo6iCsWi0N5\r\ntxDepmo3fr12fgPEu1FPx23HlpnxCyId+sj0CfA3WhiqH7XzF6l20DEMpYGM\r\nFNZ9Uf43ZkAUqbza5GC0u/IV2CN1jM93woJ09MBiag7e2eWxF+9nxtfzfqdj\r\nwTVyS3uk4u3HbaQumfDOXhKusRkV0HSn9aRRApv9R3ifMhcoqQUJdnWKw1hH\r\nzg03tCzj7E5D2KISPqNlLdHSEk/v/3Nm05c=\r\n=q341\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.1-alpha.1": {"name": "sigstore", "version": "0.0.1-alpha.1", "dependencies": {"make-fetch-happen": "^10.1.5"}, "devDependencies": {"jest": "^27.5.1", "nock": "^13.2.4", "eslint": "^8.16.0", "ts-jest": "^27.1.5", "prettier": "^2.6.2", "typescript": "^4.7.2", "@types/jest": "^27.5.1", "@types/node": "^18.6.5", "@tsconfig/node12": "^1.0.9", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "@types/make-fetch-happen": "^10.0.0", "@typescript-eslint/parser": "^5.26.0", "@typescript-eslint/eslint-plugin": "^5.26.0"}, "bin": {"sigstore": "bin/sigstore.js"}, "dist": {"shasum": "b3403bbdba49b909166592af3ef5441519fcdec8", "tarball": "https://registry.npmjs.org/sigstore/-/sigstore-0.0.1-alpha.1.tgz", "fileCount": 69, "integrity": "sha512-+O3Vn4IAG8fqVxmIPuN37MafBfGkcKCMfmY6vxq2+uctxtK4a8PaehOxIE/LPl0irVBlKM8NWN1giOp1dr189w==", "signatures": [{"sig": "MEUCIAx4mRIV3Y7Iqu11kyTtEtRO5fWpWCqeLtMlooasM0HZAiEAkq5lCJTZ/lfiDi/Q3E/ns6Aubkcv8ALkLCJIt1luBvY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 113538, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjGhwdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpaqw/9F4x6TNMmIS2qTbKUsakjjtuivDCZvYxc8tLDh7sCtDrtA1nk\r\nHRsT4RIV6/xqgJi9kgHQHZOqNGrUzfD+hUtoFktLzJOd4B5CBKyGDyplGTrl\r\nrpuL6YDVQpKy4tM390S/FXHaCflxnUUgnFxSM+OHrDt39clvtfiyJn4Cxuav\r\n7fM1dK4pATCcta6Vziz/G3BcfvbU1GbryJTsxV1vy5JAxDUlPY26cu1mko28\r\nonHrn46auagdn0Mqk9fUy+et/ckhFOBpCJ//YXmc+abjszqb5WzudUQhOWVQ\r\nQE+NzPalRaKkHit3NIiAcRQMwcHnafJLz2fJaNVIjRO0sqz5ginBk7boYL+D\r\n6V/rym8MyTDQGGW6N6vtuis/q52kbTNIcEAO1lBgZ8NcT/t08NBaxeFeTtFs\r\nffaoPdBnKbNtVCoCTyKYf3ZvR25gtzmwinJBMn5B0BElTfZ3+zl/EWq6yj+W\r\nFpCVIWxZD2md6PPT7bmB/yHsmJe/N4FVhDJjl7F/fqHsc2Cj3pdF/Jj2aF51\r\nrxlLvwDIg/5gn5XiSY8Hl+esXcfGP3vWR/qjTuaPcGWHoM9/D+SIIeLz9YnK\r\nd/OavqzdpY+tsNCjGdL2SXvs1HOzrhjWb25bEJbR8zTQDWj8Iqv+JEw/Pou5\r\nyjRcGF5/QOjlenI81AhW84Ehiqb8+y1Hsac=\r\n=pIV3\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.1-alpha.2": {"name": "sigstore", "version": "0.0.1-alpha.2", "dependencies": {"make-fetch-happen": "^11.0.1"}, "devDependencies": {"jest": "^27.5.1", "nock": "^13.2.4", "eslint": "^8.16.0", "ts-jest": "^27.1.5", "prettier": "^2.6.2", "typescript": "^4.7.2", "@types/jest": "^27.5.1", "@types/node": "^18.6.5", "@tsconfig/node12": "^1.0.9", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "@types/make-fetch-happen": "^10.0.0", "@typescript-eslint/parser": "^5.26.0", "json-schema-to-typescript": "^11.0.2", "@typescript-eslint/eslint-plugin": "^5.26.0"}, "bin": {"sigstore": "bin/sigstore.js"}, "dist": {"shasum": "cd539c297a1d00199f5bcbb7b94fbd7d7b80252f", "tarball": "https://registry.npmjs.org/sigstore/-/sigstore-0.0.1-alpha.2.tgz", "fileCount": 109, "integrity": "sha512-+a4YoL8dTEgckQxrEx+t9yzGbLq0YADzrbHp6qoEovlODm+SP7tYftdFIVrGCkKQhRV+2WB1UgnFny0nxt3Smg==", "signatures": [{"sig": "MEYCIQCmRfe/pLhLiYTKGAXOxO8wBKbPBGC6xFpDAn/lFQmw+AIhALEczq+lpyUVofaPSQIQD/jsZKH0x8N4pM9MLYH2Tuhy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160002, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjVswuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpQ3BAAjWdlEWh19czflyVnbDt86PkdKc0QuSZWB+0f6Y/T8utADI9w\r\ncuj7B++PCcgWcIo3wsFCH2gRLJ4UPXcjljWxxAmPpSAqxdA8SSnu4DuRJqSR\r\nT0MqAVfs3+w/tlxXrr+xZpCJVIbOXJObf8hOIvBpiMnnywTtsGKUBGUoqKeZ\r\npnz2ffKAZFvpz07/m3tpKzBjtyyCSesJ3dxbtoxgbbywZ/mls4wwaEX6ufwc\r\n9RrAFoxK2DkBcTeeVjrB+AQXBaQ1zgahy8EsY7/YDugyEKBTnygDeykFCoqN\r\nopGXGTbstNsvd3PARGHdc+vP/u1YFMf3PcUEvVRYhE+HImNcGmqKMMAp9+ZV\r\n5qjB9EAfXkAA73wwk28ysNQOTNcjAhuyoB42wEG5WH5Py9qkAUsAbBU98URQ\r\ntHMRE8X2HS8w0lHY6205tjxfJvlIU/+etg4NyLoYMEvs6UOAY36dnc30oNIv\r\nsmdDCDZ9bNRCPWY+SiHef5z/BX94vHPhOw4IRDSoxErsRHHXBuPX8pSDgf0R\r\nxUychFTGc0pyvdapf1aUFyrUDsa3mi1/DvAjKMEFC9nuCcIJs45GRF+NtR5V\r\nWu0iKgrLWji1ljBmumfwlT/KDq05f2mn+qwxO3RKfX0VEB6beVjZAGoLIno2\r\n6ssd+wP5RwJAcF3LvwVSoO8Zqst5p4JEKXY=\r\n=tKo4\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.1-alpha.3": {"name": "sigstore", "version": "0.0.1-alpha.3", "dependencies": {"make-fetch-happen": "^11.0.1"}, "devDependencies": {"jest": "^27.5.1", "nock": "^13.2.4", "eslint": "^8.16.0", "ts-jest": "^27.1.5", "prettier": "^2.6.2", "typescript": "^4.7.2", "@types/jest": "^27.5.1", "@types/node": "^18.6.5", "@tsconfig/node12": "^1.0.9", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "@types/make-fetch-happen": "^10.0.0", "@typescript-eslint/parser": "^5.26.0", "json-schema-to-typescript": "^11.0.2", "@typescript-eslint/eslint-plugin": "^5.26.0"}, "bin": {"sigstore": "bin/sigstore.js"}, "dist": {"shasum": "7d37096fa7c30c0bdb953f148f0e36b01c7c4906", "tarball": "https://registry.npmjs.org/sigstore/-/sigstore-0.0.1-alpha.3.tgz", "fileCount": 89, "integrity": "sha512-EUZ1nBaKYc0rCyIieTDBV+LUpraAuPNZ8PywCrReljxV9xkSy1EuaU1dT0VPHoq3sroLHS5jNJWSvxQ29xydjg==", "signatures": [{"sig": "MEUCIC/7WvXBvbyqc/QdU7eLzdt1eA64VCGPq3edYOfdAEM0AiEAoIODjZvBNR14Z1RNKIFjm1+MmeG18GW2A4HoCs2/wXs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160763, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjYCD5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpPlQ//edFk+cDANZIkKORvRs5CwUbJftGJd1MvT8ajK8gvOe1v6+0h\r\nxPQ8iBb9yRfRwc58Zjdhlc9VS2of37Ib8h17ZXGeXtKCG/R1B2kwxTiy3YF4\r\nBBujmFTMkS5/lWdwokqQkW1R0wbPR+HsZI5ISfAADU1kDz6QwQMlj6lTyTP5\r\nql/qKUMJ0BTCEdsHOmd3MowW64DNiYZ+Mxs/eULuh2dvUJUbctfkObtqHCeG\r\nhdoRpC3vkOXQUsn2awCkDYr35o4mvFs2+qV+WIjtPsSNWRb9Yu22YhdYx+qq\r\nojweUaPK1NasrDJUk7bExI4owttZ7gcIs39M67yrv/dRGxDwcEuMkaZRKyBW\r\nq14Lmf9QzUjmib4JvoYwXH7x4VXcpkHPZ1oGem87PDqaEys009GQCNgQWoF3\r\nh12qajX69Db18bREcLXb18j/TypFRujs2QzjbcXGygD6mJXbLAqvb3HQItuh\r\nQ/ZyL51/zNs3mRI/fNZFn5ICOrwnlGszX7a2sxogqIhrnY/cJuJzHlu0TykG\r\nUDNpDIfxwvwYXxgKQF1QvCVyZD0RX+JQTtGy0vy3EhrqyCCRkMTnQSQgCGEB\r\nltXg7P7YPdeVXU501WLMLTPkXIFPnGGqMwNvwL+dfxGbq/i9NRw4Iq2XfTa9\r\nu/dLVf1Zc2V7o2h17VCrSsycoU5rAFBHJLA=\r\n=m8YO\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.1-alpha.4": {"name": "sigstore", "version": "0.0.1-alpha.4", "dependencies": {"make-fetch-happen": "^11.0.1"}, "devDependencies": {"jest": "^27.5.1", "nock": "^13.2.4", "eslint": "^8.16.0", "ts-jest": "^27.1.5", "prettier": "^2.6.2", "typescript": "^4.7.2", "@types/jest": "^27.5.1", "@types/node": "^18.6.5", "@tsconfig/node12": "^1.0.9", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "@types/make-fetch-happen": "^10.0.0", "@typescript-eslint/parser": "^5.26.0", "json-schema-to-typescript": "^11.0.2", "@typescript-eslint/eslint-plugin": "^5.26.0"}, "bin": {"sigstore": "bin/sigstore.js"}, "dist": {"shasum": "0ede24e31ddc3ad6e5f869c045075780c701d68d", "tarball": "https://registry.npmjs.org/sigstore/-/sigstore-0.0.1-alpha.4.tgz", "fileCount": 89, "integrity": "sha512-+Bb82LvBfXcDzwCHcKfI4eur602x4+6UnZvzmtPaWFumj6kVGVTXB9BVF11ru5LRGrHzDkk1ldXu2ScHOweqAw==", "signatures": [{"sig": "MEUCIAVGchPQ8G94bqV0NMBR1w77lJhyz5ny0glF0JYJa6OSAiEAkSkXmDkG/vEHeEs7oxyp8jNPmA3IWv7AmV+/m2Ah+Og=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160853, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjYUpvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqKKg//SzBjhz+4MfVyU7rKIY1jA4QPZ8LJWS7nZxDYKqabqLGYrRyR\r\nPGlA3+sy2zfLyMDZ4HH1jewOIEfALf5hKjGGQlNIL1XgQd3viczlv4x8qPPZ\r\nz2HOJe/KRWXQsM5wjG9z36gygJUtCOT0uktbBBAdjTa9T3OV/6h6AYn6l4HY\r\n7Ft42OOKMOI4F0DR40SEOXPKfvPx/N57JdtF/D9ADpOZocs3YHlwy7oSFcj7\r\nVQQZJerwLv/ziijPgabtMC5/WRoFIH68010GSkjx650in+hSjwIUzVTD+ROU\r\nKgxQumlH6pa1t87JEPE8RraZI/F8JtLKMGjYGQCaBgPV00whzaQLC5OK+SXE\r\nFKbxhUT7uD1ncZ1vzGnnWbTXv8s3a7zrQXEKz3LkOE3WbuSUNl+g4ZAUedv/\r\nc2ar7Kl1p9xhqx6oVY+xCkkyJcjPzZoDdnoDHAp7x87iMQwcjK8M/p5o0/K0\r\nB09TqfOvAoLpq8o5f44+/bu2zKywOTZtlcCHFM/2i3xEt6Zm7Pi9GkceosHa\r\nvgb/5N/IBusFaRCwzgLkPTFxtU8fT+lPu6Wrfmn+aLjzL/jCs84gHJ5g6NEe\r\nZQePOLUFxpaWIXNtf4NNorIj0oUT6f/ybC6jSgIwcdbxU1MxlVBjxjEi2f86\r\nAae2G107mFXwve/2Sj5sBNU1+w6TviInjAM=\r\n=EeOT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.1-alpha.5": {"name": "sigstore", "version": "0.0.1-alpha.5", "dependencies": {"make-fetch-happen": "^11.0.1"}, "devDependencies": {"jest": "^27.5.1", "nock": "^13.2.4", "eslint": "^8.16.0", "ts-jest": "^27.1.5", "prettier": "^2.6.2", "typescript": "^4.7.2", "@types/jest": "^27.5.1", "@types/node": "^18.6.5", "@tsconfig/node12": "^1.0.9", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "@types/make-fetch-happen": "^10.0.0", "@typescript-eslint/parser": "^5.26.0", "json-schema-to-typescript": "^11.0.2", "@typescript-eslint/eslint-plugin": "^5.26.0"}, "bin": {"sigstore": "bin/sigstore.js"}, "dist": {"shasum": "6c498a40ac85b439ce8bfd8db32cd4a3cc613fca", "tarball": "https://registry.npmjs.org/sigstore/-/sigstore-0.0.1-alpha.5.tgz", "fileCount": 97, "integrity": "sha512-vl67fD2vDOkJcrNOU9wtwfNTnFq4dLJ8i7dM08QME06/xN4O8nr4bUDXLgnTqwJrr131DyFV5EXg4PKtHR+Qow==", "signatures": [{"sig": "MEUCIFgx1pkob35wLQjqT1kc9PoCs+sEgDoXlyDqq6aHKCVdAiEAxFNcBku13DTH5LuGd+p7wHAbPUVzmqjwDALbCWpCBs4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 322158, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjc9dKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrTRA//Tpfri4G0rXC7/wBKoZNY6a7x6yH8sLnMCfDp6EhTSlgFcLUz\r\nMknmYb/+bKryGOEOyiHqPjXNNKMy908H963JG00eDMw2Slb1C6s9VCb2+tzA\r\ntHjIhbeaGryyMcZyByRhdL5LhWr6h3lWB9I+PLes8Y7Zfe8D1U2+4SCtu9ko\r\nE5y3mlEFD8+9PchrLFsvcYxCZOzDa235ZqnDc2l3gq+co/5byv9QgGPRpLTZ\r\ntEo4t8T+f8o+sr6Y6neZATSbNcvgLKcwt7KDd4N0ns2DLv0yO5zLuUwOj0Xh\r\ngEnCPH25/UCUG4GFchQ+NK5Vb+Il8isEvwUN28U5CCSFWPhK1FCN7rGXSnbS\r\nX1vG1Ozo7eerWsPj8+8IBnR3P23Tta940+KJovUxwAZCcOGw5LWqIaLrPmKc\r\nELS6iscw1hOsXBPi5/3+DkrfsMa2kJu27+TN772kAKsXGfcffQX6GzV9ucPD\r\njE6URiVw3WmQmPKnGl4vWUYxaq46OLMi6RFkz5WrSgru9n6TYhXH4KVlL3NK\r\nr9ZQXZjVmVI4cUDrE7VOXFBqc8Ay32vcB/PIVrlbpFc7aTD1MvCBYpciXfXU\r\n2QUN39+TwY1llEaQpwANM4vZglgJ1kWln1/tjrmbpQjviRSbryAzuDpHGSr6\r\n11dq1ulRI21MND9M9P0DKC7/kXNR46YSoVo=\r\n=+Zuy\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "0.1.0": {"name": "sigstore", "version": "0.1.0", "dependencies": {"make-fetch-happen": "^11.0.1"}, "devDependencies": {"jest": "^27.5.1", "nock": "^13.2.4", "eslint": "^8.16.0", "ts-jest": "^27.1.5", "prettier": "^2.6.2", "typescript": "^4.7.2", "@types/jest": "^27.5.1", "@types/node": "^18.6.5", "@tsconfig/node12": "^1.0.9", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "@types/make-fetch-happen": "^10.0.0", "@typescript-eslint/parser": "^5.26.0", "json-schema-to-typescript": "^11.0.2", "@typescript-eslint/eslint-plugin": "^5.26.0"}, "bin": {"sigstore": "bin/sigstore.js"}, "dist": {"shasum": "7069093c47658051c21c77c3827baf273a1ea442", "tarball": "https://registry.npmjs.org/sigstore/-/sigstore-0.1.0.tgz", "fileCount": 105, "integrity": "sha512-AAbBkDrryxtG4hAnsKsuN7wYnZcmkbdlfsiLyTmQOGqCX31oHMjo7qdcPUjFjU2rwxYXuLVtR4U9+Waz4+0HbA==", "signatures": [{"sig": "MEUCIEPLqzyOnuy9eCc0yzJ8l/JAUqdwH2g06T8H4FU539U+AiEA89gc1X4vGZzHvQZri+mCbDS2391R1P7dcwWzPPZlEOE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 189416, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjh6OGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpDHg//ZZ8Mf/dnLzM6vK8yB8Ii1tagBYB9dEZBqasXylmCdHCjlzDm\r\nAwvuSt9BcyN5Hg8e+bBLjtLmTNZO3BKWGTJOQDFhyIujywTEZXjCJa7/aaKl\r\n3Ub9W4EyiUHM6LB2pQTA7MLq0Q4N7kVY9oPjm1k6Jpi7U1B/P9tKs80AUbV+\r\nYSmR+SbRjyejoVkSu0GybKOz4k+f3m4eOIGlSW5ZnpqcJi3CfWAfYDLwOVNE\r\nb4GdVwXuiy7AKyC/z8Zc5t+gnFxY8P7FulAqURBKsvZdgNuh1ACq4azMim+G\r\n3x5H2jpqiDoGE1EP5L+dgS0SSbZZcqZu38Mqt1kpGERU5yoJ6pC8GyKCd2wf\r\npPl0YTbJ4T8noQo0zilbZYc/pBswOzsQOy3Bxm25neyIGD/Zm9aGRvmnBQQe\r\n0lEAcTXf9zCk3xJ0iODrd6hUY7FVG1T/IoZm6rUgIkth/z+7MnjSMpi7T8pz\r\nM+CAYcBj8usF3jiKAa3KRPCohEBFkCro5mHCcblGjoQVw7IIdwbGWLtgCnQW\r\nqW3ngd+BwCaXaAgh46Q4h07a72jxD6KqtoWgwFbCMWBh0uY9ER0JpGTxPvo7\r\npAhsQtCbCcnTHPV8q/KgUKk2uzAeiHFc8s7kYbKMOgjEqxCeOSgG1mmvH8R4\r\nQFapL8nZeVphoyvOuWzy+xX14XtPXgK6WtI=\r\n=iOOA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "0.1.1": {"name": "sigstore", "version": "0.1.1", "dependencies": {"make-fetch-happen": "^11.0.1"}, "devDependencies": {"jest": "^27.5.1", "nock": "^13.2.4", "eslint": "^8.16.0", "ts-jest": "^27.1.5", "prettier": "^2.6.2", "typescript": "^4.7.2", "@types/jest": "^27.5.1", "@types/node": "^18.6.5", "@tsconfig/node12": "^1.0.9", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "@types/make-fetch-happen": "^10.0.0", "@typescript-eslint/parser": "^5.26.0", "json-schema-to-typescript": "^11.0.2", "@typescript-eslint/eslint-plugin": "^5.26.0"}, "bin": {"sigstore": "bin/sigstore.js"}, "dist": {"shasum": "4c1181d676395bfa478a54d0d51f1650b094e23f", "tarball": "https://registry.npmjs.org/sigstore/-/sigstore-0.1.1.tgz", "fileCount": 109, "integrity": "sha512-H33B4+E6DQKYdst5MscF6zm/vYvhq2OV1raloghvU+SpJQQ6TW3y9uQWjK1rjouOu0Y8vVrHa6iGuSufRG1Zng==", "signatures": [{"sig": "MEUCIFKxB2dvh0aQX1DPXE2qkVe4InAC49tO+s2qIcVdZKd2AiEAkI7sRAwhoHNKjDEy+FIVbj5fF2PHEhpXqnQ2gt+jTzk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 195167, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjiOEqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrsbw/+K4ZCjvosNMz80HZR4a8W1FxhV/ZR1ObweTTLn9rKQvc1ooU8\r\n8OfNoX8gFkzzldjJ7+NUOgzYiMbyBIfZtlE1PaNCrlmp7+0mBV8402twhLLq\r\n7C1CwBC8rQZGqnyDwYLqpzNKTFrYuQuVNToHjgc3wOG+kSI5XYsbbORlgB90\r\nnpiaAggVvB7xMHNJFtwTGB5Y9hhQAVCvINI3yfhmlgQ/2vd+VykFhCXP0E63\r\ntGcdNDvjDUvRFTgu/MCgRuKN8DimPgiqRsnGjR9FlyOu+f6zbihNxYKo1mEU\r\nOlOQ2YTXWLT5S8le5ZTrf7IA69FWCgrPIDvpqfPjZTZMK9jtIqQmXRsR3ZBx\r\nV0NRlfVZNqUHHHEBr7gX7nJU3C9cKBdfCBPUsf0q3slBf5OxHEw+/EeizFsV\r\nQQn3KcihLoo5m+ipVTddx1x8/DO5/xAtLdz4YtWYdOG0mhYwQ6kGKhvk/mXO\r\noQvp118V0JC8FBPHA1QYTsl/nYBjZwrFZx9WXK9u2U1k4+L56AsNDVGd3rEx\r\nQipYI7SAO0CyNU0fQSJRde4J6xnD8Fiq7McWrQYaQRLHcTgK7WpKjwdOBxXY\r\nk5P4s51csXNTEj4v6STzz/qv1dqSmypaUcoDCaMmny7iT6NrvqgVcA4s4XOT\r\n7uujuugZga9jvEmqviAWJp5hs/7YP3hI5Jk=\r\n=48jR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "0.2.0": {"name": "sigstore", "version": "0.2.0", "dependencies": {"make-fetch-happen": "^11.0.1"}, "devDependencies": {"jest": "^27.5.1", "nock": "^13.2.4", "eslint": "^8.16.0", "ts-jest": "^27.1.5", "prettier": "^2.6.2", "typescript": "^4.7.2", "@types/jest": "^27.5.1", "@types/node": "^18.6.5", "@tsconfig/node14": "^1.0.3", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "@types/make-fetch-happen": "^10.0.0", "@typescript-eslint/parser": "^5.26.0", "json-schema-to-typescript": "^11.0.2", "@typescript-eslint/eslint-plugin": "^5.26.0"}, "bin": {"sigstore": "bin/sigstore.js"}, "dist": {"shasum": "264d4b3af27fbf06d74c8458782a9ff823543202", "tarball": "https://registry.npmjs.org/sigstore/-/sigstore-0.2.0.tgz", "fileCount": 113, "integrity": "sha512-KHE8uPbGM0sWEEjrOX8crujKS4fyQt7dIRx/L8t6zL6WhCwb5NSw+7+6hdXUASL9P2lxTPtjHFGuoFcz9biqXQ==", "signatures": [{"sig": "MEUCIQDm5FodMuZGZVx6gxXibbhz5VIJqO5YEZTiIo0iyhYRwwIgH9kbrvJT8rq4lGmX1bGcF4v9uq6IhMsmF0/aZs0ySJo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/sigstore@0.2.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 202761, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkg5hACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoV1g//TxukNM47GLB6YJi0eNCjGN9erEJlDvOqD2kbew1UAjCNS1AX\r\n1x5dxlRtMdUkXtRWTVXcj2kNjKddhe6fwtrWxyxQNWagHpUzCxxU6bbdarnP\r\nr7zKBcHoItSJUtCNjXrhDKPVrSrJ/ZoWupJIXx6FXZqx8LtAJNLG/X3ebBVe\r\na9TaFQqUAyNw0hQh64mJpn+XEkDLcAP4rsrXlSSSis/LgrR89aIQSL89Q5p1\r\nvo11jOgvKAfwt0krDqnCt/JAsa3NPrN2319RkYauQu/xPuBdVv2WYlNacChc\r\nJOJnfUW74Y3E8+C1D5Go+Hnq23n5sLR92Q84iB/pxNNBQ0x+jsma/q5HwmmW\r\nH2dvcbCgZYpi+RPrsW2F89siAIsR6bTVWNQ6Rq0kHhOX5qDceGqBTweZlZqf\r\nqO9pkiY0TWg7gkFeUUXTevZxpu828FyTGhkJeR2NFkiL9n653qtvwOtzgkF5\r\nfm34CuNdDFMZCBlvFalaAGUZ51fTdtu8swKsXjleaik72kFi4x/1fNxgc4NE\r\nCRT6HWeX20F0xfgXaRd9CvJ9SN61yfirZoTphLKDPTNv8gisXGqU5LbdHInY\r\n2Eli0yHM+vi1PjgQK/G45WXcPP9XvcYfGBu3X9hpp1ThJwKYyPxMOaSbgD3o\r\nPM0+tB3gqmPmK9guf5cGrT9dm5StrTy0GX4=\r\n=gIuw\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "0.3.0": {"name": "sigstore", "version": "0.3.0", "dependencies": {"make-fetch-happen": "^11.0.1"}, "devDependencies": {"jest": "^27.5.1", "nock": "^13.2.4", "eslint": "^8.16.0", "ts-jest": "^27.1.5", "prettier": "^2.6.2", "typescript": "^4.7.2", "@types/jest": "^27.5.1", "@types/node": "^18.6.5", "@tsconfig/node14": "^1.0.3", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "@types/make-fetch-happen": "^10.0.0", "@typescript-eslint/parser": "^5.26.0", "json-schema-to-typescript": "^11.0.2", "@typescript-eslint/eslint-plugin": "^5.26.0"}, "bin": {"sigstore": "bin/sigstore.js"}, "dist": {"shasum": "c223a53e61261f49b7ea612e60f3bef35a89aedf", "tarball": "https://registry.npmjs.org/sigstore/-/sigstore-0.3.0.tgz", "fileCount": 137, "integrity": "sha512-/BbXMWsd1B9+wzWbaWPZpgs0AQRuLmbOI8drAEWIIxOy83exVnmNUAvW0Z8uCevsrcBA4wMrVO7RmOd0qeDGTw==", "signatures": [{"sig": "MEYCIQCWPPUQE25xG5T4Ognme7jO0xLJG8ageXrr83Ot7Fdh4AIhAOHDa5vzGz+0JAwvsOjGjJtYRIYVBTqUei/NVCjWq1R6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/sigstore@0.3.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 411243, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtyOqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpR/Q//YyOxFYOVxy1HEJ8EBayuohdhnsvnbbLEINsHaAtkNu8+m/9w\r\nf3KscfWZiXmHrCaQS51xu1Y8n5S51IS5lQQfy9R6r2X3RLOyYQ6M7hz8N3gZ\r\nqc75vJgsU/drKlY1rFZGoa/1Rpi0hSpmIaKuUc9Ylu/ATq43DN6QhvUq0UBS\r\nXKvkQVzmfwFxJnYYa3uZBXmkKAZBX0MzT82OsO4p6UCDh6Yskn1AzKcR9SXi\r\n56jXuoRNVY2iNeIVPUSuCw5VTqa9y+wlzNBAPRoKJQZWNNfNtmTNmjCKVBW2\r\nO1gGgaIpp+x15XC+9g+KYMDyTbDZStpGCNRrrvhdJHYM3QLsP7Lp7bht9ft0\r\nQfM3SiGoM5mbk2Y1l2N9yMWDWXM1bdnxy4agco9W4jPzOkFthrHeQX9O5oAU\r\nDiPKmR77jFfJoIIz+x9vN+RtCDOlpEEpOsIPI9U9eiQnXMLEdtasdmT2+sdb\r\njeKFile10DR2ocNNfem+eVOUnH2s22ikJiDLkmL0/LjLT1M0Ik0xASqgVvda\r\njeULpNsw9y/H64JFZXJAVNgx4cv9+sYy0AtL9+8owBh0MHCNKBVBLEfS1XLu\r\n98CJFb+OB43W4Jymqcvv2JQE1gCF6uV08b+uPQzLJcDxB+M4hYc4iYDQUvks\r\nRxvajQOmDXU8Z+Wki2I6wmXd7oR4nr4JDAc=\r\n=SjyN\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "0.4.0": {"name": "sigstore", "version": "0.4.0", "dependencies": {"make-fetch-happen": "^11.0.1"}, "devDependencies": {"jest": "^27.5.1", "nock": "^13.2.4", "eslint": "^8.16.0", "ts-jest": "^27.1.5", "prettier": "^2.6.2", "typescript": "^4.7.2", "@types/jest": "^27.5.1", "@types/node": "^18.6.5", "@tsconfig/node14": "^1.0.3", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "@types/make-fetch-happen": "^10.0.0", "@typescript-eslint/parser": "^5.26.0", "json-schema-to-typescript": "^11.0.2", "@typescript-eslint/eslint-plugin": "^5.26.0"}, "bin": {"sigstore": "bin/sigstore.js"}, "dist": {"shasum": "a5e5386bd2ad86d7d1f20c3b2083d345c0cced23", "tarball": "https://registry.npmjs.org/sigstore/-/sigstore-0.4.0.tgz", "fileCount": 148, "integrity": "sha512-KCwMX6k20mQyFkNYG2XT3lwK9u1P36wS9YURFd85zCXPrwrSLZCEh7/vMBFNYcJXRiBtGDS+T4/RZZF493zABA==", "signatures": [{"sig": "MEUCIEG1SOvLEEFRiCNCUvpEIIiRjzxYiYRBORYyuSKx/9USAiEAklMrCPF74R3I0q9LXXLXt5M/q+bMNoupAMnkdUX4vZM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/sigstore@0.4.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 546038, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjvvKJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqg0hAAl8H/syO67Mi/F4UmisjgSNfjtirtSDLrPHLh2qDMmKBgxJDu\r\njMAnflKWnwcCsCSuTy0SAlUEFC86AZeJjqH0d3sutd2hCKNI+Q4Qlt4ev7p3\r\nQMh7yDrQbx09k9IPnxYT4nY/rCY4mNAf7EvSDlZsxJIyjMlZJ/UOpoXT6sSB\r\nQyAN4lAFNMj1BvhU8vF4AunyeuD9qiRlX8hF0ahHqZcgFepf0NoLFkmY7aZb\r\nD0/FTh3BqXX29sJvfdLvx9INszBOalxJZXfbClBs/YvW1Qs7JYxcjQ9NFftj\r\n5Wi9pqtwn1SHDHktxpfjoFeWb0VU2+cwDc0op2to2vbZ7jGA2CWCMoY2Bkwm\r\nJHBDLzmBYTzJAmi3Wah+7uWdKNKHHqRg6X8Xl2GHykXPbm+dpDmBW0X9pAhw\r\nhfQKnYzqJadH9c/9omAk8SvLGiTdbS657LkY3ymzfHfWQKVmYPNxAmYj8EyW\r\nxENHWQ64BzYzEUHySuRLetDZWFFbz1psIFOsmc/Ml87ioZ1+D6MnPl6+IzYw\r\nvLp0vfTo2LmO8VDxPVJLyywvD3Yi+b2S2rPy9DslyQ9uw76EvYiFviZUeu5N\r\nZla14ZLTXnDmlZHT6SUzq1YSwcHH+MpkhA4RPfRZF+kLT9lkwcpbNKzZpfy9\r\nGB+8XTKBnD+09viqz6JwYswegY1kjcuhGdw=\r\n=GsHr\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "1.0.0-beta.1": {"name": "sigstore", "version": "1.0.0-beta.1", "dependencies": {"tuf-js": "1.0.0-beta.1", "make-fetch-happen": "^11.0.1"}, "devDependencies": {"jest": "^29.4.1", "nock": "^13.2.4", "eslint": "^8.16.0", "ts-jest": "^29.0.5", "prettier": "^2.6.2", "typescript": "^4.7.2", "@types/jest": "^29.4.0", "@types/node": "^18.6.5", "@tsconfig/node14": "^1.0.3", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "@types/make-fetch-happen": "^10.0.0", "@typescript-eslint/parser": "^5.26.0", "json-schema-to-typescript": "^11.0.2", "@typescript-eslint/eslint-plugin": "^5.26.0"}, "bin": {"sigstore": "bin/sigstore.js"}, "dist": {"shasum": "c4ced7a56a1293e4b1f01a57021dc2d718b69a98", "tarball": "https://registry.npmjs.org/sigstore/-/sigstore-1.0.0-beta.1.tgz", "fileCount": 144, "integrity": "sha512-f1QDca0rKpIX9aryoqHEWIQWFh3H0KIsVgZoZlqjZbNSketri/xsPY/roC8WsRBDGafAkee7Ngpjl8+yFrW94Q==", "signatures": [{"sig": "MEUCIDAfjERj+JSOeEmpHcVoRyX42XtdaEA7bRwg5BERQYp3AiEAiNmV6uCHJv8U15iKaoUfKZ6/XJ+tCaIrEIjT+b6vqLU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/sigstore@1.0.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 419034, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj49TTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqAjA/8CrHjCYRKM6IdGHbRtEk2WOF4gwYGt0SyDrUcHnr0XLyGLadj\r\noUATkReba/W5Xd4lgRfTjymr0SdLuvzHLeHqFSsyt59tYZfiMbGuTOGlcdwB\r\nuwe4nIzPltIEKukdQdAipSANFTwXrrIHi0aiqv73aMgKdj+BbeSdPFevX4e7\r\nHpQ3Cm1xYWb8ck0bUNhdQc4CU6cDQHmOIpdPhE25ZsYtRhkCtkoP4CnxjyEj\r\n7c2ziHKUhaqfIZKzrcG2/XmbqcF7OA/+vn9/edm5K0vBwMoZ6NoyTEyiX846\r\nklpcBcM+kR9Tbdlc9oME33wSEho7r9JvkzGJhl8qGWv1JMQwIhLC2tqgfilk\r\nIflt1r3+JI+wqgVM7P0mc3R3q07YUsumJXpUr72IbYyrm2rcBebcLlaJPNL6\r\nqIyNyzgOjg9B2BaqUY4sxcxIUCICgF0VwwtjkvSEdZLMA9UwAh3vp7TX6HO6\r\nE0kRXUEWFAZ5PbeRC2aUTwv3iuKn3cggvxJKL1G3nJ3vhY8i6U5WkJBLP9Bl\r\nIZ2W/Vr3gee8LKYE+6wz6wMm35y/yBu4LMrfMgp0X8YTpsZYWvkTFKewLBtZ\r\nNj1+IyokCEuvLDe7l4+Q2KTI9UAyti8ci5nX74LWP0lbX8ct/FY7XEQxk/0K\r\nqu+zfdsB5SVDdIGigZC6DKNVAdjnZqNJMq8=\r\n=PoNP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "1.0.0": {"name": "sigstore", "version": "1.0.0", "dependencies": {"tuf-js": "^1.0.0", "make-fetch-happen": "^11.0.1"}, "devDependencies": {"jest": "^29.4.1", "nock": "^13.2.4", "eslint": "^8.16.0", "ts-jest": "^29.0.5", "prettier": "^2.6.2", "typescript": "^4.7.2", "@types/jest": "^29.4.0", "@types/node": "^18.6.5", "@tsconfig/node14": "^1.0.3", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "@types/make-fetch-happen": "^10.0.0", "@typescript-eslint/parser": "^5.26.0", "json-schema-to-typescript": "^11.0.2", "@typescript-eslint/eslint-plugin": "^5.26.0"}, "bin": {"sigstore": "bin/sigstore.js"}, "dist": {"shasum": "3c7a8bbacf99d0f978276bd29bd94911006b72c7", "tarball": "https://registry.npmjs.org/sigstore/-/sigstore-1.0.0.tgz", "fileCount": 144, "integrity": "sha512-e+qfbn/zf1+rCza/BhIA//Awmf0v1pa5HQS8Xk8iXrn9bgytytVLqYD0P7NSqZ6IELTgq+tcDvLPkQjNHyWLNg==", "signatures": [{"sig": "MEQCIBlpcHT68iWOpx8pJr3WUzD1EqQ7tb0CmY36ebbceR6IAiAVGRaxrFoyh0/5B7H1o4VFhfsHw9F8G+AxOZQq87q+lg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/sigstore@1.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 419021, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5TOxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqGrxAAi98tZadrTkKj3oYsVSg7tpXOEoRrqTSQ61XmWsihmB1vYvF1\r\nqC5nqVhgh/OGOEY/hRs9PkIMcexCJxlDc6MkkbHGEcmOb3WWSFxRGX72fWfh\r\neZOOfYgPL/2AXDIhzf3H0Ok9QVkGNbJQY+5fCSwzOYVn7bfFjXEPLx3J7rkK\r\n1Y2Yjpk2KM41q7/YW07fFf3RBya3jzNS3p0SSC5aCnkzziwq55FLVQnIlYlq\r\nBaIgs3/2TXEVCgFB6wesr8uTDlP76QrEnwwyC+cGVCQ97j8pZHrjOBDTxRl1\r\nhNGSM8ypHzWt5QTYTyi+8NESHiq7M5E7Yi4bgCEMc4687rVLQwucGDb1pVsh\r\n8tmpI73amK1DlDH9I+cSKuPgQLiwPZZjrQoOWyGvQZObgvRLKMu/C6pBTy9q\r\nya9NF8yoOc5OgiIxF+PyqANruM2vdRXuOX6/hNT5ErD8f+UVYDN5mXiSYivM\r\nhjAxNuw5y3NFWAXyJdX8inGrBGeAMGANDy4XQe8J4flXOAEfQ354PvlNVqm2\r\nRw2rfc5xYwz8Ckvs6j6LbXi7AfevKPL2tchoBmho+ty3xhbyF8RHQfXAhh16\r\nSt+8owBTYPfPR0AD0thoWMsAOCZzxytRgjmMOTL1Eh27AfrepaiTjdaFX2NF\r\nXzhGBnbjp9vhEt1ui0C1AyOwkXJptFUzcQg=\r\n=aIjs\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "1.1.0": {"name": "sigstore", "version": "1.1.0", "dependencies": {"tuf-js": "^1.0.0", "make-fetch-happen": "^11.0.1", "@sigstore/protobuf-specs": "^0.1.0"}, "devDependencies": {"jest": "^29.4.1", "nock": "^13.2.4", "eslint": "^8.16.0", "ts-jest": "^29.0.5", "prettier": "^2.6.2", "typescript": "^4.7.2", "@types/jest": "^29.4.0", "@types/node": "^18.6.5", "@changesets/cli": "^2.26.0", "@tsconfig/node14": "^1.0.3", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "@types/make-fetch-happen": "^10.0.0", "@typescript-eslint/parser": "^5.26.0", "json-schema-to-typescript": "^12.0.0", "@typescript-eslint/eslint-plugin": "^5.26.0"}, "bundleDependencies": ["@sigstore/protobuf-specs"], "bin": {"sigstore": "bin/sigstore.js"}, "dist": {"shasum": "55bd590e1b49a64e1b13f3ee27b129b7e2fd8e31", "tarball": "https://registry.npmjs.org/sigstore/-/sigstore-1.1.0.tgz", "fileCount": 150, "integrity": "sha512-GyWJT8NTWo+Uxcb9DSowse9fokJLYvapEuvXmHwKSBSIoymMN/uhUke8Lg0t1ld1W+vSKXiD6O1bh0Bxd/Kihw==", "signatures": [{"sig": "MEUCIQDaSLlQso1HH/DIen6K/ceJdpbpcN/slo5htzThM7qnSQIgWwFulo4pCCx+/xDrhLeFr+nS+VWHMvi5L9HmDy441KA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/sigstore@1.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 429865, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkEKJ4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoXng//drfjpnDTT5bstnDxWl8BwPfePaPJHteVMemrMwxiBu5/CzJ3\r\nUljWUIZRpiIkjxnGD1oXoycbWXub/2totZeM19EPzjldNhcD9/gC13kS2O0w\r\nKqhSQx1kxA7O8MXN0Sc9snJHkMtFMmx4WuF2KBC/gFeOeWHhfrXhFq3lh9wu\r\nJQUDAYo50T6W0Tbq1cQZU5m4PI8P2a2dP1YNbVv4X6Hwtl2WELKt1kxtPKa5\r\nG66tVGIhu5c6ppawL91sniiYZSkCPbQ1JtUd6bmqlV9b9fASKvThmq/dUOmk\r\nSYXVm+6tF7oMMue0+Lfb7WarEybJh5rQbrYJ92mW7odPCm1kbrMPRB2+lw7s\r\nCsoF90JTMxy+KObRMAGLKUKDKWVByAeV0XQdHg9nald9x9Faul6jXQlSjXer\r\nk+Er7YtB+eJohY/Bh561WZc6x8tTN/0SNw0xCkcr/IiXQpHJanMbU51ZkO78\r\ni5IvAwXfAliTsbgZa2ARR0JQ95mhz23IyYL6GuZClZNs+UIOd5IUyo+TACje\r\nxXxOCJU62gndf5m2FNmmCUzSE56tmHTEWbyLp9k28j7H2efjajsP7I7vihBu\r\n3CyzJdIVctyDj+i6LV9Aik1n2alOqR4tw+xsXX14sOK9LYiOEyl28Q9nzWJc\r\nXaAd93VIDiFMRUrLrwL79BUus+qbjL7443w=\r\n=jxSk\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "1.1.1": {"name": "sigstore", "version": "1.1.1", "dependencies": {"tuf-js": "^1.0.0", "make-fetch-happen": "^11.0.1", "@sigstore/protobuf-specs": "^0.1.0"}, "devDependencies": {"jest": "^29.4.1", "nock": "^13.2.4", "eslint": "^8.16.0", "ts-jest": "^29.0.5", "prettier": "^2.6.2", "typescript": "^4.7.2", "@types/jest": "^29.4.0", "@types/node": "^18.6.5", "@changesets/cli": "^2.26.0", "@tsconfig/node14": "^1.0.3", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "@types/make-fetch-happen": "^10.0.0", "@typescript-eslint/parser": "^5.26.0", "json-schema-to-typescript": "^12.0.0", "@typescript-eslint/eslint-plugin": "^5.26.0"}, "bin": {"sigstore": "bin/sigstore.js"}, "dist": {"shasum": "751a709a7467ca54dcb2e81e36d67cfdcf5ca546", "tarball": "https://registry.npmjs.org/sigstore/-/sigstore-1.1.1.tgz", "fileCount": 127, "integrity": "sha512-4hR3tPP1y59YWlaoAgAWFVZ7srTjNWOrrpkQXWu05qP0BvwFYyt3K3l848+IHo+mKhkOzGcNDf7ktASXLEPC+A==", "signatures": [{"sig": "MEYCIQCOZQzz+udvDeDTJ33fi8KMe8wNDv3xwhVKOnP9ct1g2QIhAIvZGySF9TeuS+5QkhKdqpxNHBvYpD7o2ZokkL8rKdJD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/sigstore@1.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 232110, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkEMy/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr1cw//QGjnyz8jmWzM8NQP/6CFsCFWq0pYnS4fvZUeHY5g/Fyvwyms\r\nSssJ/oQDImCmxBZjRAU4Z5dXgWNzyadsuAuXYL+gu76E9fEyHnPWNwtVsX0v\r\n82ChnJS3txQdQN9ynHc7gnvXSJbNsWXvPNbMQ9Z2qN3VKaLdGaiSuFSQBkQ2\r\nuXoXulWea67XIMSiJ4q6UVE+oc23gAvHLBEGbs460mrIvc0Qlm+kGEb2xRTV\r\n3/P5X5k67ljj94bk1NM3nqHL/d/Zftx9p8QU/eE+/HZWkCB9G1zTv+lFexqz\r\nLlM4pwAQrXihFteABR9lTsZRHXlyCUHM1Vj2KTzy3g3SmPLEJ1TMmCALbrUC\r\ntB8EfBULRw9vZzHmDS5q3civ4vHjtVMbw2Z/6eBbOYaYXZdyZDojwxNPS/Fr\r\n412yB3M7pC/0CcY2S1zCUeQure/jGQrEBxOpWq0Ou+Y8cw5JKESyVLKuDwQO\r\nupLT0GevxDx0+lE+cQVOKjXIVBi27uE/aMjN9JetyKbbTilceFvpTizzyyXY\r\n8eMaj1Ip9CWk3k3XnID4V/B8GeCr9HgZ7TqZ9DLK4CMwUdf070vQqrWTR7LK\r\nkwJs+vSY15Vt+HZf3tzWsVzYqFCHhRdEbJUD3yjn2ic+pTrniu1KOuosVKUs\r\n+j+IyU2bKr2EWFE5tVxgGR2WPoj4S4kOCCI=\r\n=0BY2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "1.2.0": {"name": "sigstore", "version": "1.2.0", "dependencies": {"tuf-js": "^1.0.0", "make-fetch-happen": "^11.0.1", "@sigstore/protobuf-specs": "^0.1.0"}, "devDependencies": {"jest": "^29.4.1", "nock": "^13.2.4", "eslint": "^8.16.0", "ts-jest": "^29.0.5", "prettier": "^2.6.2", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^18.6.5", "@changesets/cli": "^2.26.0", "@tsconfig/node14": "^1.0.3", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "@types/make-fetch-happen": "^10.0.0", "@typescript-eslint/parser": "^5.26.0", "json-schema-to-typescript": "^12.0.0", "@typescript-eslint/eslint-plugin": "^5.26.0"}, "bin": {"sigstore": "bin/sigstore.js"}, "dist": {"shasum": "ae5b31dac75c2d31e7873897e2862f0d0b205bce", "tarball": "https://registry.npmjs.org/sigstore/-/sigstore-1.2.0.tgz", "fileCount": 127, "integrity": "sha512-Fr9+W1nkBSIZCkJQR7jDn/zI0UXNsVpp+7mDQkCnZOIxG9p6yNXBx9xntHsfUyYHE55XDkkVV3+rYbrkzAeesA==", "signatures": [{"sig": "MEUCIQDMXpvv99G3NLOATNIdUPRul/Hdz8plstXkPn/KK0w6cwIgFXhSOwuh0dzRGVjRGMe5XR07EdVjk+cCM0ZVvyoXnfs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/sigstore@1.2.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 231816, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkHI9PACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqoWRAAk24gZN+L4jIWgKGXnVSBV4BGRe9PodVTphy8dhQJ+pHgQlBb\r\nCmA6L5vtq8i0n/l8yKcK6mcw2ctt6VM/pMQ6+bQRci1pTBqjzsHjWILru5Q+\r\nnB9GrhDU1D/vbaMG7bO/FkmvX/yiqz+bPjL6Uv4m7ixvQeyUUfYPE9qWmqoU\r\nkYv/s5xXRBiyspSoSQN8I7BAqqMX+ajteBmTQejtWjFX4GZna4N5sT5aq2xQ\r\nDerDI1rZoXCa38aRfK8SXqJDv8r1jeUEiYqwpY6pbM0hWojh2//IrlkT76ee\r\nxmov+Gqqh6G+HkUHIjZbgPx6GZ0VQr8lxsypgPAL7QG/4QCiAHhY7WLhbGYP\r\nA+FD17+M+16ZxnxCBCQG5MozBW5NFBBXbioPDnK3cP4u9+kBGrrwJCp78tv8\r\n0CPwU3hy4aNo+NMWxGRVlGZDbMSng4c3xxi6twGtqUmD9K2i5FpRhW52YFkx\r\ntfjO49uA7PhtT6ckY4Asjj6Mtxd+RWPkJPU7hVikTZ73piNGCceJlW+ojkub\r\nQN5nqAa5hi2OsE4kf55Vxm5qYZFY4K6bcjIVGxXL1CPmj3qAINwX2FpJnhZU\r\n7FTEdJv2HfhifT4qcQL/49yEYAmpKBDD9UOzwJ4e/eqjxnJlYcyDBS8o+0NC\r\nslaHlC0xiSZX/uHpbPdWCEAr0UTiAX2w3kE=\r\n=raNX\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "1.3.0": {"name": "sigstore", "version": "1.3.0", "dependencies": {"tuf-js": "^1.1.3", "make-fetch-happen": "^11.0.1", "@sigstore/protobuf-specs": "^0.1.0"}, "devDependencies": {"jest": "^29.4.1", "nock": "^13.2.4", "eslint": "^8.16.0", "ts-jest": "^29.0.5", "prettier": "^2.6.2", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^18.6.5", "@changesets/cli": "^2.26.0", "@tsconfig/node14": "^1.0.3", "@tufjs/repo-mock": "^1.1.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "@types/make-fetch-happen": "^10.0.0", "@typescript-eslint/parser": "^5.26.0", "json-schema-to-typescript": "^12.0.0", "@typescript-eslint/eslint-plugin": "^5.26.0"}, "bin": {"sigstore": "bin/sigstore.js"}, "dist": {"shasum": "cfcb636ff5ef3df1dafb34552ef1ee5e72be1a83", "tarball": "https://registry.npmjs.org/sigstore/-/sigstore-1.3.0.tgz", "fileCount": 129, "integrity": "sha512-dhdv+jOAi1RgLHw13lxumk3rlgZtumUz9QrCNPTx9MazUnUV3BfAb74oYAMPQQ7uaeogB5vTosbz3POzKbEHUQ==", "signatures": [{"sig": "MEYCIQDrhadLEynyqqRcG+d3fclnXhwW76PB8DYRFIQwK6WT1wIhAJ9HnyE2EPFk8li9RwmiJIG5pC6LeYghRGs1JqaSdhk2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/sigstore@1.3.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 239371, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPtcpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqKQw/9H6yCnnDOKeD5Qr1N0WSLJ2e4+6IBHVjKlfKbwcRXmPOnd/bJ\r\n/w4OMPn8DkksMEKtt0qt1+EsnzZWxligOczfh5vZT3ESGsGKRcFIsAz6eK0T\r\nOvHC00QBmsQ+bALppwG0IfK5VRVDNQQjji8YEKsn9Dm43f5teV0R7WRmVBUV\r\nMe9alx2EDsJ1N3LdmRNITvyg3V2k+MjHZAkQjnDWeQEj4W1BPqQDK/nFsH5K\r\nbm8NrKCxREbSfpM6RHtRtdrNbw+bD/kh+DG1Byghz2lMQKPvm0tCIU5aSz/B\r\ntIxA3Un0//KI8ArQ7qH3blTJ1TGAoCt7jRjh5iBzu9peGoyQPRJJDzF2VgJs\r\n6BBoUSpzztYy3XK75B/ezgmK3HDodtTVKu9WwpsRbkUn32V7ivLyRAt+GLlh\r\n3Q/uYsWIfQZGOnRHmc1e1+JtMIqrduh2SPKZYKMrEDU8tx1B0Y7+JwiC5BqE\r\nAfpBG7LaYULmfF2h/yr1OJHjoXOvaGICpf04NrkZ9BqWKC52IdxrgmAxAKd5\r\nBWictefyNA2S6A69ntah2Zx44K49821tBPRAAwDbsSSXQfAqzBDEuGjjlAcr\r\nr6eypehqYofCVtO9cWPPll8bKSQH+Xy4pcCu82FT3Rxu2gzm7xAewNTQlIQv\r\nPWPrNBxeXPLMzthrEgMc+lxpbXT7VH7GGtk=\r\n=bB5o\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "1.3.1": {"name": "sigstore", "version": "1.3.1", "dependencies": {"tuf-js": "^1.1.3", "make-fetch-happen": "^11.0.1", "@sigstore/protobuf-specs": "^0.1.0"}, "devDependencies": {"jest": "^29.4.1", "nock": "^13.2.4", "eslint": "^8.16.0", "ts-jest": "^29.0.5", "prettier": "^2.6.2", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^18.6.5", "@changesets/cli": "^2.26.0", "@tsconfig/node14": "^1.0.3", "@tufjs/repo-mock": "^1.1.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "@types/make-fetch-happen": "^10.0.0", "@typescript-eslint/parser": "^5.26.0", "json-schema-to-typescript": "^12.0.0", "@typescript-eslint/eslint-plugin": "^5.26.0"}, "bin": {"sigstore": "bin/sigstore.js"}, "dist": {"shasum": "355aea461e704d22fcbc1e300516be9d05827836", "tarball": "https://registry.npmjs.org/sigstore/-/sigstore-1.3.1.tgz", "fileCount": 129, "integrity": "sha512-C3HBrbJJ9KCg+etufAqeI8Ut4DxgKh/I8VBUmW+wgQJbk3GW56zhSRlVADmBwnIKXJOcAwCXPKLTVayIWTQJHA==", "signatures": [{"sig": "MEYCIQCPFXJ9AC1/j0pqcwG1OeRA44U5Gf7pXLI0NVBLQM2l1wIhAKvBvAQRng8+n5eC6/SjsN4AkDYz4V+ABaxasl7oM4ri", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/sigstore@1.3.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 239521, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkQPkIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoD+BAAguf71TfMASpJ042dx8UroV9T0iAP2V5bqIq2kuULKLifTYCx\r\nXUiZBBNPybhT70RAvdOYEQ+jUqh+1Og4/9NhpUslKN5tgPg1Nk7USwQ8ZcZD\r\nM42GHGSGbvGj2s+gBqUwmMHfuIxopNOgSgu6N51JyDImFe8gwnaqUJBcfT2i\r\nYvFRaYT3nIXCx3R1iel35S2tlm2Fou1bG7ARiqfMaLzEttj/69QnSvUCqC2S\r\nPffdIzmNnzL7AmjGZleuV7c0kEQgPrvgLbknkcXtmR9KuYJUUaZUatQBgcHJ\r\n+Ov1h64ln9AlxcTXsaLvvkNk/17SP6MyqGK6kdaQz9l7Evx+vRRRbqTzzR/z\r\nTwprfjyPMB+6jK+fIG1/UdIhb3v9D3GxAUKVkovhDYwBuMGEu4QljbrOPpeD\r\nfGZG4vPjQuKEumRjQRopjpGcEGTWOwTB7cXRT4u0v01rxuFFH1zXlDnli0T6\r\nMHnDfGamwV68GyqPsyAPNBKUdKrrcRrQ/5JAUWHDMkYYi6S65zDcJ4CQAQq1\r\npsLtwT3D/RMf73rnzbBfGvhzu6vz/FbuKpQpOmU6FlsI0c9DTe6I8Pr0xaJG\r\n6cp8HrRuG1/EDlq//ve5YE7VuW2nrqBB1VnsBJIjv3Z2wiHMCLUYvJCtfPKx\r\n130vs0EzWvuW7bCbePCKKZw5DU6qCegTQiE=\r\n=NfDj\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "1.3.2": {"name": "sigstore", "version": "1.3.2", "dependencies": {"tuf-js": "^1.1.3", "make-fetch-happen": "^11.0.1", "@sigstore/protobuf-specs": "^0.1.0"}, "devDependencies": {"jest": "^29.4.1", "nock": "^13.2.4", "eslint": "^8.16.0", "ts-jest": "^29.0.5", "prettier": "^2.6.2", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^18.6.5", "@changesets/cli": "^2.26.0", "@tsconfig/node14": "^1.0.3", "@tufjs/repo-mock": "^1.1.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "@types/make-fetch-happen": "^10.0.0", "@typescript-eslint/parser": "^5.26.0", "json-schema-to-typescript": "^12.0.0", "@typescript-eslint/eslint-plugin": "^5.26.0"}, "bin": {"sigstore": "bin/sigstore.js"}, "dist": {"shasum": "2f70ec3e1192911484d9d148ecd9c98345cd172d", "tarball": "https://registry.npmjs.org/sigstore/-/sigstore-1.3.2.tgz", "fileCount": 129, "integrity": "sha512-0KT1DjpVB11FK15ep7BIsZQV6j1jBm4SnXIInbBCRvql6II39IKONOMO+j036sGsArU/+2xqa1NDJwJkic0neA==", "signatures": [{"sig": "MEYCIQDJdUH803Y6rbK1R3WzOvFB2yiKWKC5p4N9DVnGfoeTYwIhAOKSlDwC1QiTXmuH83O6NB40pIPSaOvxCcKgKVqpk7C/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/sigstore@1.3.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 239682, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkQTHzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrDbw//cWBgnuUdwkfq/QZ2XjL5IdLphZjmzNOkvxngFjJMHYF2iRDU\r\npXildfa1RaZOhsoQSZgLLpmSaFlNYGVRyrgR89cBADHs9fvPMBgkRmBwOa69\r\nY1pQ0nQ1rjxA8rclkzbNb1RJFD9nxULuM1N56ryD8pDwlJlvzaFwNhUAaCKz\r\nNVqBVGX82jWVnfgixYqHWqENkmq0F7vcXm3MZQvz16vKADMOSYlE5HFtaCs9\r\nAP0C/6k9bhod3HLyI6FwOZkQBjMv9TUbhmDKT9dMlZt+qrj9g79CeaIy1vU4\r\nwYFDADKxi0d8uSWn/emcqSb5kFoSJHYMcNCKSks76RwMLU8KBpLlSdHM7i4r\r\ng2C9JCvDwX8TuVWa4RmwAxMwKx21U50lUX+EgJA3/ujmgN+/FsxeFRtDrhyS\r\nChk2kCUEM3T6lQW7sRHJR5kMpgbBgaBxk1JIRao+FyFdKtlZWTBqzViZKnf0\r\n0F3FM7oMLjDmkGkNMFzgNeKCNkFaCNjaW5xzxDBTqncetp0/oO7CEisQqgMQ\r\ntSibd2ZfIzw4EsirovxXovEbCqWrZ5IX9ota/lfzptraFAWb/KaAAAHDmJw+\r\ncdlKdRQfgTEVJ7x0xQsXIZ8u3CqWF5r+Q638hjivCxm4Boo+zJrhHBGbUl8p\r\nNU6p5y89CjTq+eeauE9ueeND7EL8X1T0o4c=\r\n=tVzY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "1.4.0": {"name": "sigstore", "version": "1.4.0", "dependencies": {"tuf-js": "^1.1.3", "make-fetch-happen": "^11.0.1", "@sigstore/protobuf-specs": "^0.1.0"}, "devDependencies": {"jest": "^29.4.1", "nock": "^13.2.4", "eslint": "^8.16.0", "ts-jest": "^29.0.5", "prettier": "^2.6.2", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^18.6.5", "@changesets/cli": "^2.26.0", "@tsconfig/node14": "^1.0.3", "@tufjs/repo-mock": "^1.1.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "@types/make-fetch-happen": "^10.0.0", "@typescript-eslint/parser": "^5.26.0", "json-schema-to-typescript": "^12.0.0", "@total-typescript/shoehorn": "^0.1.0", "@typescript-eslint/eslint-plugin": "^5.26.0"}, "bin": {"sigstore": "bin/sigstore.js"}, "dist": {"shasum": "2e3a28c08b1b8246744c27cfb179c525c3f164d8", "tarball": "https://registry.npmjs.org/sigstore/-/sigstore-1.4.0.tgz", "fileCount": 129, "integrity": "sha512-N7TRpSbFjY/TrFDg6yGAQSYBrQ5s6qmPiq4pD6fkv1LoyfMsLG0NwZWG2s5q+uttLHgyVyTa0Rogx2P78rN8kQ==", "signatures": [{"sig": "MEQCIElvyyG7uCaLxrnfIyjoHx1pG58XC4HrHkY/PevKPqPDAiAl3xAvx1SmeA1U1QuiaWS+7VGrfzZ3oN7qY2Log4rNuA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/sigstore@1.4.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 241782, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkRp+gACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoUUQ//Wj+3mVCcsByTSYzwl/VvDHzLvm6hG8cWJRYku5KSClgGY8Uv\r\n1k/lyrECo01FmSOAcs1rwDOB0gtjgrgTo/hhqYTv/8n+M8MHytIgvbe2uR/H\r\nqszvuIcs4+lBGlk/DGjdmVD1FEr9sQtdrMknxQMnbaw7Iq0QMoeEcl1yhRqi\r\nEF/Hvb/8GN6405IGvln5zKQ2/8YvesOZkiUkBy2MLTb7ZwaXsnKEICl0nmHm\r\nFKnUOA/MYlXZgEcjeCXRM2v1RL3uU1TBQrYzbJDeZrCA/s8W1pkgZIE7GtaB\r\n3Enxzv+7vBGVOy+6NYwDY9o0JtBjzAdCl7433+PQCbRMi38+13wCe+wATacx\r\nhoSwOEg8rnW4oP8wCtsgp+GyCdwOQYvf3W/2tZkICFbPlNFyD6IsOcHAk8Tq\r\nFvp8EquYMHZMYc9SnNJ8Wpkzm7UX9p6pJxhPIg6EGbTt7O/uT3pWuCAvRVsq\r\n89DLUOLnJaoNN0tksTCgHj91vmZ0FZ+ZISmWMFMvErk1XfSt+KMV9j4J5Nh+\r\nczv2iTKJbxLgBvGHLiHu7LQzO8p3lQVsbeu55QBw/th32BatqyFNwC2vKu79\r\n/4y28MVcUTThNLzIGmbTTrcoPELOOmy915WGYN/hJlh/HUJTjs0kjUtKq9f5\r\nbph8Y0oNrazkdPnG/dWVWrno3ZWDAOXAuKQ=\r\n=K/ex\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "1.5.0": {"name": "sigstore", "version": "1.5.0", "dependencies": {"tuf-js": "^1.1.3", "make-fetch-happen": "^11.0.1", "@sigstore/protobuf-specs": "^0.1.0"}, "devDependencies": {"jest": "^29.4.1", "nock": "^13.2.4", "eslint": "^8.16.0", "ts-jest": "^29.0.5", "prettier": "^2.6.2", "typescript": "^5.0.2", "@types/jest": "^29.4.0", "@types/node": "^20.0.0", "@changesets/cli": "^2.26.0", "@tsconfig/node14": "^1.0.3", "@tufjs/repo-mock": "^1.1.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "@types/make-fetch-happen": "^10.0.0", "@typescript-eslint/parser": "^5.26.0", "json-schema-to-typescript": "^13.0.0", "@total-typescript/shoehorn": "^0.1.0", "@typescript-eslint/eslint-plugin": "^5.26.0"}, "bin": {"sigstore": "bin/sigstore.js"}, "dist": {"shasum": "795e44b8e9ab0089daa90eff792a831ba87ffe9c", "tarball": "https://registry.npmjs.org/sigstore/-/sigstore-1.5.0.tgz", "fileCount": 135, "integrity": "sha512-i3nhvdobiPj8XrXNIggjeur6+A5iAQ4f+r1bR5SGitFJBbthy/6c7Fz0h+kY70Wua1FSMdDr/UEhXSVRXNpynw==", "signatures": [{"sig": "MEQCIB9cX3fQNyT5CF0nK5q18XLEBC8j1i3CXe51CliaSfT0AiAcKU6cQR05wNs5M7LPUc3i11OMPoPJYsGq/y3QmPx/yA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/sigstore@1.5.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 253580}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "1.5.1": {"name": "sigstore", "version": "1.5.1", "dependencies": {"tuf-js": "^1.1.3", "make-fetch-happen": "^11.0.1", "@sigstore/protobuf-specs": "^0.1.0"}, "devDependencies": {"nock": "^13.2.4", "typescript": "^5.0.2", "@types/node": "^20.0.0", "@tufjs/repo-mock": "^1.1.0", "@types/make-fetch-happen": "^10.0.0", "json-schema-to-typescript": "^13.0.0", "@total-typescript/shoehorn": "^0.1.0"}, "bin": {"sigstore": "bin/sigstore.js"}, "dist": {"shasum": "92b9e371a37d1f53d3baa991a2c63b89bbe3cee5", "tarball": "https://registry.npmjs.org/sigstore/-/sigstore-1.5.1.tgz", "fileCount": 135, "integrity": "sha512-FIPThk7S1oeFXn8O8yh7gpyiQb6lYXzMIlOBzXhId/f81VvU587xNCHc4jd2lZ9724UkKUYYTuKSYcjhDSRD/Q==", "signatures": [{"sig": "MEYCIQDkNxRBykaUWwzSD2eO+75AIW1wZsPPRBvUVwLRo3RlSAIhAIoC5iOTN4Knb5hZMx9lm+aUhgOmUt+GqH0ySsF7Zkah", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/sigstore@1.5.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 251310}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "1.5.2": {"name": "sigstore", "version": "1.5.2", "dependencies": {"tuf-js": "^1.1.3", "make-fetch-happen": "^11.0.1", "@sigstore/protobuf-specs": "^0.1.0"}, "devDependencies": {"nock": "^13.2.4", "typescript": "^5.0.2", "@types/node": "^20.0.0", "@tufjs/repo-mock": "^1.1.0", "@types/make-fetch-happen": "^10.0.0", "json-schema-to-typescript": "^13.0.0", "@total-typescript/shoehorn": "^0.1.0"}, "bin": {"sigstore": "bin/sigstore.js"}, "dist": {"shasum": "8d4c2a549341211cb08c687999843edc48c1a94c", "tarball": "https://registry.npmjs.org/sigstore/-/sigstore-1.5.2.tgz", "fileCount": 135, "integrity": "sha512-X95v6xAAooVpn7PaB94TDmFeSO5SBfCtB1R23fvzr36WTfjtkiiyOeei979nbTjc8nzh6FSLeltQZuODsm1EjQ==", "signatures": [{"sig": "MEUCIQCRRZ5kBLq8vaq4T4UAAlHZsF09PhCt0NRnL+KedEQdfQIgXBg5dn5bzlMdKg5hcWlYPVNoFbBYo8kFRgqSvmAW/Qs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/sigstore@1.5.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 251833}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "1.6.0": {"name": "sigstore", "version": "1.6.0", "dependencies": {"tuf-js": "^1.1.3", "@sigstore/tuf": "^1.0.0", "make-fetch-happen": "^11.0.1", "@sigstore/protobuf-specs": "^0.1.0"}, "devDependencies": {"shx": "^0.3.3", "nock": "^13.2.4", "typescript": "^5.1.3", "@types/node": "^20.2.5", "@tufjs/repo-mock": "^1.1.0", "@sigstore/rekor-types": "^1.0.0", "@types/make-fetch-happen": "^10.0.0", "json-schema-to-typescript": "^13.0.0", "@total-typescript/shoehorn": "^0.1.0", "@types/sigstore-jest-extended": "^0.0.0"}, "bin": {"sigstore": "bin/sigstore.js"}, "dist": {"shasum": "887a4007c6ee83f3ef3fd844be1a0840e849c301", "tarball": "https://registry.npmjs.org/sigstore/-/sigstore-1.6.0.tgz", "fileCount": 125, "integrity": "sha512-QODKff/qW/TXOZI6V/Clqu74xnInAS6it05mufj4/fSewexLtfEntgLZZcBtUK44CDQyUE5TUXYy1ARYzlfG9g==", "signatures": [{"sig": "MEQCIGwqHq6iPaPD5VlVxQij0mtHi1UPukCUAD3iRfEwbWbZAiAXiph6N3BPZhnLDIdauia1a2DR/483lS1Q+p5UthI7TA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/sigstore@1.6.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 238904}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "1.7.0": {"name": "sigstore", "version": "1.7.0", "dependencies": {"@sigstore/tuf": "^1.0.1", "make-fetch-happen": "^11.0.1", "@sigstore/protobuf-specs": "^0.1.0"}, "devDependencies": {"@sigstore/jest": "^0.0.0", "@tufjs/repo-mock": "^1.1.0", "@sigstore/rekor-types": "^1.0.0", "@types/make-fetch-happen": "^10.0.0"}, "bin": {"sigstore": "bin/sigstore.js"}, "dist": {"shasum": "9186e6c8ce1ab0cba5d97b414212d40f0a01564e", "tarball": "https://registry.npmjs.org/sigstore/-/sigstore-1.7.0.tgz", "fileCount": 120, "integrity": "sha512-KP7QULhWdlu3hlp+jw2EvgWKlOGOY9McLj/jrchLjHNlNPK0KWIwF919cbmOp6QiKXLmPijR2qH/5KYWlbtG9Q==", "signatures": [{"sig": "MEQCIChc5xfCtIXZQWbDmDtrj9p8c0of4tJ03ZrA5iAqeHa+AiAoXpgSVN7SXjI/5JzrZKab650TyMxnN+a9PYG4Ul5Qig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/sigstore@1.7.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 234372}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "1.8.0": {"name": "sigstore", "version": "1.8.0", "dependencies": {"@sigstore/tuf": "^1.0.3", "@sigstore/bundle": "^1.0.0", "make-fetch-happen": "^11.0.1", "@sigstore/protobuf-specs": "^0.2.0"}, "devDependencies": {"@sigstore/jest": "^0.0.0", "@tufjs/repo-mock": "^1.1.0", "@sigstore/rekor-types": "^1.0.0", "@types/make-fetch-happen": "^10.0.0"}, "bin": {"sigstore": "bin/sigstore.js"}, "dist": {"shasum": "f790120697fa7c89f4418598ce59e638ff680aa5", "tarball": "https://registry.npmjs.org/sigstore/-/sigstore-1.8.0.tgz", "fileCount": 118, "integrity": "sha512-ogU8qtQ3VFBawRJ8wjsBEX/vIFeHuGs1fm4jZtjWQwjo8pfAt7T/rh+udlAN4+QUe0IzA8qRSc/YZ7dHP6kh+w==", "signatures": [{"sig": "MEUCIQDiwtSkB2eTprSqtkgm2R/ohKJbbzwWnWgZ2AZ4e36ewAIgO8HrOitRmNn+h+gTDw74Y2ohzswywRuFfGdPiBsyUjY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/sigstore@1.8.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 234998}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "1.9.0": {"name": "sigstore", "version": "1.9.0", "dependencies": {"@sigstore/tuf": "^1.0.3", "@sigstore/sign": "^1.0.0", "@sigstore/bundle": "^1.1.0", "make-fetch-happen": "^11.0.1", "@sigstore/protobuf-specs": "^0.2.0"}, "devDependencies": {"@sigstore/jest": "^0.0.0", "@sigstore/mock": "^0.2.0", "@tufjs/repo-mock": "^1.1.0", "@sigstore/rekor-types": "^1.0.0", "@types/make-fetch-happen": "^10.0.0"}, "bin": {"sigstore": "bin/sigstore.js"}, "dist": {"shasum": "1e7ad8933aa99b75c6898ddd0eeebc3eb0d59875", "tarball": "https://registry.npmjs.org/sigstore/-/sigstore-1.9.0.tgz", "fileCount": 86, "integrity": "sha512-0Zjz0oe37d08VeOtBIuB6cRriqXse2e8w+7yIy2XSXjshRKxbc2KkhXjL229jXSxEm7UbcjS76wcJDGQddVI9A==", "signatures": [{"sig": "MEYCIQC3fYqLjhxouDDWw6JjpA7ngFstq0Z/+FPYkbVZZ1TmIgIhAMmYS3IGqNO68AdR9eTi25b621d3gmb2d7f3/cKqsjOM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/sigstore@1.9.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 189183}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "2.0.0": {"name": "sigstore", "version": "2.0.0", "dependencies": {"@sigstore/tuf": "^2.0.0", "@sigstore/sign": "^2.0.0", "@sigstore/bundle": "^2.0.0", "@sigstore/protobuf-specs": "^0.2.1"}, "devDependencies": {"@sigstore/jest": "^0.0.0", "@sigstore/mock": "^0.3.0", "@tufjs/repo-mock": "^2.0.0", "@sigstore/rekor-types": "^2.0.0", "@types/make-fetch-happen": "^10.0.0"}, "dist": {"shasum": "dbef27b7040fa8d0351fa9aacc48e63adbf030b2", "tarball": "https://registry.npmjs.org/sigstore/-/sigstore-2.0.0.tgz", "fileCount": 73, "integrity": "sha512-RtTi90xIdzFmQAAKb9+Ki1nx4IR2Z5c+mFn3dN0xuPHgk3gTt3f7ZqKsZ9UFQP40ZAlm7un8LMyjhwgrTIXNPA==", "signatures": [{"sig": "MEQCIFNuNXfOSnm5Xxy9Ljn9KG/j+OYlbgPY4ifPFAjNsvS4AiBA9iHySkBCL36k1Pe1/Maq78XqQMCGO+hW0pCYUjhcxw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/sigstore@2.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 160999}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "2.1.0": {"name": "sigstore", "version": "2.1.0", "dependencies": {"@sigstore/tuf": "^2.1.0", "@sigstore/sign": "^2.1.0", "@sigstore/bundle": "^2.1.0", "@sigstore/protobuf-specs": "^0.2.1"}, "devDependencies": {"@sigstore/jest": "^0.0.0", "@sigstore/mock": "^0.4.0", "@tufjs/repo-mock": "^2.0.0", "@sigstore/rekor-types": "^2.0.0", "@types/make-fetch-happen": "^10.0.0"}, "dist": {"shasum": "c577b596642b3f360dc4135d476466e6edeb2364", "tarball": "https://registry.npmjs.org/sigstore/-/sigstore-2.1.0.tgz", "fileCount": 73, "integrity": "sha512-kPIj+ZLkyI3QaM0qX8V/nSsweYND3W448pwkDgS6CQ74MfhEkIR8ToK5Iyx46KJYRjseVcD3Rp9zAmUAj6ZjPw==", "signatures": [{"sig": "MEUCIHxnGcNtJmOs68L+fANGzs1oZzI5B3HT/pEBnU1fWjYEAiEA1C/CIft7s/7Uc79sS6Oh7ducrhp5Jz2+G0ojh+kVgng=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/sigstore@2.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 160555}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "2.2.0": {"name": "sigstore", "version": "2.2.0", "dependencies": {"@sigstore/tuf": "^2.3.0", "@sigstore/core": "^0.2.0", "@sigstore/sign": "^2.2.1", "@sigstore/bundle": "^2.1.1", "@sigstore/verify": "^0.1.0", "@sigstore/protobuf-specs": "^0.2.1"}, "devDependencies": {"@sigstore/jest": "^0.0.0", "@sigstore/mock": "^0.6.3", "@tufjs/repo-mock": "^2.0.0", "@sigstore/rekor-types": "^2.0.0", "@types/make-fetch-happen": "^10.0.4"}, "dist": {"shasum": "acba5f73ca2158d2b0507bc52d3592149c3ed20e", "tarball": "https://registry.npmjs.org/sigstore/-/sigstore-2.2.0.tgz", "fileCount": 9, "integrity": "sha512-fcU9clHwEss2/M/11FFM8Jwc4PjBgbhXoNskoK5guoK0qGQBSeUbQZRJ+B2fDFIvhyf0gqCaPrel9mszbhAxug==", "signatures": [{"sig": "MEYCIQDw6pR4Uyfsbdn9bh6WIJ+lDuFDT8B4p3x4c862YDmRTwIhAMC36SGZ5eo1E3iQfvIo27QM0ie76jof4YEk2zGkg3/P", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/sigstore@2.2.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 36278}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "2.2.1": {"name": "sigstore", "version": "2.2.1", "dependencies": {"@sigstore/tuf": "^2.3.0", "@sigstore/core": "^1.0.0", "@sigstore/sign": "^2.2.2", "@sigstore/bundle": "^2.1.1", "@sigstore/verify": "^1.0.0", "@sigstore/protobuf-specs": "^0.2.1"}, "devDependencies": {"@sigstore/jest": "^0.0.0", "@sigstore/mock": "^0.6.4", "@tufjs/repo-mock": "^2.0.0", "@sigstore/rekor-types": "^2.0.0", "@types/make-fetch-happen": "^10.0.4"}, "dist": {"shasum": "a0f9f6b7f39aef5d9b3d659cd14b99a502fb44b0", "tarball": "https://registry.npmjs.org/sigstore/-/sigstore-2.2.1.tgz", "fileCount": 9, "integrity": "sha512-OBBSKvmjr4DCyUb+IC2p7wooOCsCNwaqvCilTJVNPo0y8lJl+LsCrfz4LtMwnw3Gn+8frt816wi1+DWZTUCpBQ==", "signatures": [{"sig": "MEUCID4yWaFk9cmYrtPDpuHX1qAEACUu2hUPMh4YXTE+PYVkAiEArfeUfTGojjBLYdluV4DVdVe4zcU7HudvTXPazY6/7gE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/sigstore@2.2.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 36278}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "2.2.2": {"name": "sigstore", "version": "2.2.2", "dependencies": {"@sigstore/tuf": "^2.3.1", "@sigstore/core": "^1.0.0", "@sigstore/sign": "^2.2.3", "@sigstore/bundle": "^2.2.0", "@sigstore/verify": "^1.1.0", "@sigstore/protobuf-specs": "^0.3.0"}, "devDependencies": {"@sigstore/jest": "^0.0.0", "@sigstore/mock": "^0.6.5", "@tufjs/repo-mock": "^2.0.0", "@sigstore/rekor-types": "^2.0.0", "@types/make-fetch-happen": "^10.0.4"}, "dist": {"shasum": "5e4ff39febeae9e0679bafa22180cb0f445a7e35", "tarball": "https://registry.npmjs.org/sigstore/-/sigstore-2.2.2.tgz", "fileCount": 9, "integrity": "sha512-2A3WvXkQurhuMgORgT60r6pOWiCOO5LlEqY2ADxGBDGVYLSo5HN0uLtb68YpVpuL/Vi8mLTe7+0Dx2Fq8lLqEg==", "signatures": [{"sig": "MEYCIQCus1vJs2S24JqW/kb/MlbSBa9g6q8vQ64JPIveVm6//wIhAKe3jBst1UAAHzIuxMl7pZgHGawxQDeHTU7nsp7yG6Ki", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/sigstore@2.2.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 36278}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "2.3.0": {"name": "sigstore", "version": "2.3.0", "dependencies": {"@sigstore/tuf": "^2.3.1", "@sigstore/core": "^1.0.0", "@sigstore/sign": "^2.3.0", "@sigstore/bundle": "^2.3.1", "@sigstore/verify": "^1.2.0", "@sigstore/protobuf-specs": "^0.3.1"}, "devDependencies": {"@sigstore/jest": "^0.0.0", "@sigstore/mock": "^0.7.0", "@tufjs/repo-mock": "^2.0.0", "@sigstore/rekor-types": "^2.0.0", "@types/make-fetch-happen": "^10.0.4"}, "dist": {"shasum": "c56b32818d4dc989f6ea3c0897f4d9bff5d14bed", "tarball": "https://registry.npmjs.org/sigstore/-/sigstore-2.3.0.tgz", "fileCount": 9, "integrity": "sha512-q+o8L2ebiWD1AxD17eglf1pFrl9jtW7FHa0ygqY6EKvibK8JHyq9Z26v9MZXeDiw+RbfOJ9j2v70M10Hd6E06A==", "signatures": [{"sig": "MEUCIBZlF7eXgKSVSnf2cNkrkneihdC2Pj6RxjikqjPwlLJUAiEAhMG5Ejxy4eA5ot/2HRIFQQKrLc4IVkbg/WRmFNfLE98=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/sigstore@2.3.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 36278}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "2.3.1": {"name": "sigstore", "version": "2.3.1", "dependencies": {"@sigstore/tuf": "^2.3.4", "@sigstore/core": "^1.0.0", "@sigstore/sign": "^2.3.2", "@sigstore/bundle": "^2.3.2", "@sigstore/verify": "^1.2.1", "@sigstore/protobuf-specs": "^0.3.2"}, "devDependencies": {"@sigstore/jest": "^0.0.0", "@sigstore/mock": "^0.7.4", "@tufjs/repo-mock": "^2.0.1", "@sigstore/rekor-types": "^2.0.0", "@types/make-fetch-happen": "^10.0.4"}, "dist": {"shasum": "0755dd2cc4820f2e922506da54d3d628e13bfa39", "tarball": "https://registry.npmjs.org/sigstore/-/sigstore-2.3.1.tgz", "fileCount": 9, "integrity": "sha512-8G+/XDU8wNsJOQS5ysDVO0Etg9/2uA5gR9l4ZwijjlwxBcrU6RPfwi2+jJmbP+Ap1Hlp/nVAaEO4Fj22/SL2gQ==", "signatures": [{"sig": "MEUCICNMO+KaH2tlza9tIHQMG7ur7yOktbqMc3+PLP7D5OzjAiEA5UX16gk+WG5Er+9ZFT4udqQtDKYkqAcfEkSTtCXJYP8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/sigstore@2.3.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 36278}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "3.0.0": {"name": "sigstore", "version": "3.0.0", "dependencies": {"@sigstore/tuf": "^3.0.0", "@sigstore/core": "^2.0.0", "@sigstore/sign": "^3.0.0", "@sigstore/bundle": "^3.0.0", "@sigstore/verify": "^2.0.0", "@sigstore/protobuf-specs": "^0.3.2"}, "devDependencies": {"@sigstore/jest": "^0.0.0", "@sigstore/mock": "^0.8.0", "@tufjs/repo-mock": "^3.0.1", "@sigstore/rekor-types": "^3.0.0", "@types/make-fetch-happen": "^10.0.4"}, "dist": {"shasum": "d6eadcc6590185a7f1c16184078ce8a9ef6db937", "tarball": "https://registry.npmjs.org/sigstore/-/sigstore-3.0.0.tgz", "fileCount": 9, "integrity": "sha512-PHMifhh3EN4loMcHCz6l3v/luzgT3za+9f8subGgeMNjbJjzH4Ij/YoX3Gvu+kaouJRIlVdTHHCREADYf+ZteA==", "signatures": [{"sig": "MEUCIFFWJhn8uYavdTFhFBe51TdFVm2BWZTpNU8XXDyKqjHEAiEA6ueK9t6wUnn5DqL9LA+25JRseNOfzRPsCWLULd2m3Fs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/sigstore@3.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 36880}, "engines": {"node": "^18.17.0 || >=20.5.0"}}, "3.1.0": {"name": "sigstore", "version": "3.1.0", "dependencies": {"@sigstore/bundle": "^3.1.0", "@sigstore/core": "^2.0.0", "@sigstore/protobuf-specs": "^0.4.0", "@sigstore/sign": "^3.1.0", "@sigstore/tuf": "^3.1.0", "@sigstore/verify": "^2.1.0"}, "devDependencies": {"@sigstore/rekor-types": "^3.0.0", "@sigstore/jest": "^0.0.0", "@sigstore/mock": "^0.10.0", "@tufjs/repo-mock": "^3.0.1", "@types/make-fetch-happen": "^10.0.4"}, "dist": {"integrity": "sha512-ZpzWAFHIFqyFE56dXqgX/DkDRZdz+rRcjoIk/RQU4IX0wiCv1l8S7ZrXDHcCc+uaf+6o7w3h2l3g6GYG5TKN9Q==", "shasum": "08dc6c0c425263e9fdab85ffdb6477550e2c511d", "tarball": "https://registry.npmjs.org/sigstore/-/sigstore-3.1.0.tgz", "fileCount": 9, "unpackedSize": 37213, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/sigstore@3.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCCs7f3ZfBv/GAQbS2bwYKkP7qA+YhEXPVcTlRrP88JHAIgaWJxIIvEupPr1wB3i15BqqC768dquUTCaFcnxxMfK+I="}]}, "engines": {"node": "^18.17.0 || >=20.5.0"}}}, "modified": "2025-02-04T20:35:49.251Z"}