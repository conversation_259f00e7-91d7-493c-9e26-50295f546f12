{"_id": "@types/keyv", "_rev": "519-cd844754ea191b85a7f70de09e87596f", "name": "@types/keyv", "dist-tags": {"latest": "4.2.0", "ts2.1": "3.0.1", "ts2.2": "3.0.1", "ts2.3": "3.1.0", "ts2.4": "3.1.0", "ts2.5": "3.1.0", "ts2.6": "3.1.0", "ts2.7": "3.1.0", "ts2.8": "3.1.1", "ts2.9": "3.1.1", "ts3.0": "3.1.1", "ts3.1": "3.1.1", "ts3.2": "3.1.1", "ts3.3": "3.1.1", "ts3.4": "3.1.1", "ts3.5": "3.1.1", "ts3.6": "3.1.2", "ts3.7": "3.1.3", "ts3.8": "3.1.3", "ts3.9": "3.1.4", "ts4.0": "3.1.4", "ts4.1": "3.1.4", "ts4.2": "3.1.4", "ts4.3": "3.1.4", "ts4.4": "3.1.4", "ts4.5": "3.1.4", "ts4.6": "3.1.4", "ts4.7": "3.1.4", "ts4.8": "3.1.4", "ts4.9": "3.1.4"}, "versions": {"3.0.0": {"name": "@types/keyv", "version": "3.0.0", "description": "TypeScript definitions for keyv", "license": "MIT", "contributors": [{"name": "AryloYeung", "url": "https://github.com/Arylo", "githubUsername": "Arylo"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "234dc10e833a6e1254a18417d9330fb2e671fe86df1009f137b8c8d7395c370f", "typeScriptVersion": "2.1", "_id": "@types/keyv@3.0.0", "dist": {"integrity": "sha512-WC/MXa64qaA+H2NHO+11zbGiPbqgGNliOxSkyntZL8+JGHiePkqHC31s5K1zJGLz5VlSv4M3KEUiAVP9Wyie3g==", "shasum": "b557704a74a05e4357425362b55bdf7e3ec87ed7", "tarball": "https://registry.npmjs.org/@types/keyv/-/keyv-3.0.0.tgz", "fileCount": 4, "unpackedSize": 4195, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCZzSoe6ekeluQA+0GoGX2YMOMOb5pBaMDF8wSEqR46CAIgTq8jcb0+5KWVTAfkYku/SY1SiNjq0E6qd3UzhMT61dA="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/keyv_3.0.0_1521845525199_0.5360199603689222"}, "_hasShrinkwrap": false}, "3.0.1": {"name": "@types/keyv", "version": "3.0.1", "description": "TypeScript definitions for keyv", "license": "MIT", "contributors": [{"name": "AryloYeung", "url": "https://github.com/Arylo", "githubUsername": "Arylo"}], "main": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "996c6fc84323d87aa0d6e2201f2c9af9d0099d8eb5ddf636806f15c78bf1e106", "typeScriptVersion": "2.1", "_id": "@types/keyv@3.0.1", "dist": {"integrity": "sha512-Nn9TebKwLyY17j7arUL1yKYS5Mx+I1h45bejs/C9g8LW1Km7CrJbMHmm96cSsxslOAk1CnQj614gF9ekMnH1Lg==", "shasum": "360353aba0fcc8db2c857685d3b31b42d4602b5c", "tarball": "https://registry.npmjs.org/@types/keyv/-/keyv-3.0.1.tgz", "fileCount": 4, "unpackedSize": 4290, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa7POdCRA9TVsSAnZWagAA/LwP/1So6WI+cUKiG7QiSF9m\n+lrpWs/v+lB6TQHQdMYj2niQBCVofxm94p403hJzfij+4SjnV1b25KQlvyIE\nKUdtSibWZ6jZ49fy1D00jCISr+Vm6fSmIg4EXGtGlagqhMxGagd9gNs4rnFq\nl4UXSE6VYdxuPDdbbSrG0jRZouY1+6tkVAFYpz39ehewwRkOErdkh5JKmazo\nAd65u/hD5NSU5Af9QClyP/u8dadh5XLZTB2qACHk4wLtfHSiAI+Ck7EGXBoJ\nMshCEfy9QtUVsjFBMo98ykucD+SgjO8lS5Vcy/9jy/OMheJeFsGDRJMiyERj\nC/nJ0tYUJNAzkVCoky7kQSWkvBDbpdyZmVj63eWEbsXwe5uBPcSicf0iWnFB\namWujVZEbWnlmfYqcsu/2m+dzKOkcXWYj4279dC42jaxgY3FXy+eI3jBOXVc\nOnFpP0YZh2B0F//LbnYXdbL41kg9p1W6lqClKYMTkEZTdrl85BR6VrkQ4F07\nSJ/CP8QzFfYvNvqJ3LceSo4x/zgwkmoL2HwqruHVdzDkf4oLWR8CMCZdHogZ\nduA5loDu7ra7HR0e4RJeiigP+TOaDI4cnBnmH0PKPDdqIqdOjNzXzWf0ckbO\n+fZ1ZRiZldC4exb2jRpn7wamK7f41BaM8nCKOrMAoI3Axxkt2nzAQJ9lqLkM\nsKO6\r\n=fUBX\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCyQHG1TiPEE+YMVs4kLEIqzYIYP8T5DWfR2nvss0/ksgIhAK9GLB3o+lL2Km9Vg0oTPq9r9XQ24L5n38dWgET660by"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/keyv_3.0.1_1525478300766_0.8962022937531067"}, "_hasShrinkwrap": false}, "3.1.0": {"name": "@types/keyv", "version": "3.1.0", "description": "TypeScript definitions for keyv", "license": "MIT", "contributors": [{"name": "AryloYeung", "url": "https://github.com/Arylo", "githubUsername": "Arylo"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "9dde299bb997332ace0a8477b702424b478ec4c45368418265336b4f8899ed7c", "typeScriptVersion": "2.3", "_id": "@types/keyv@3.1.0", "dist": {"integrity": "sha512-OxT2IEeRdwvoUyp8n1v1hTIFzATb3NQYN8OHv/XbXRHiF2DXwKyzoI4UUaQgwZkRflLaSgyttat+RfWgsKIMIQ==", "shasum": "1961f73b3bf1084c044f79a070b45a5bfa6578b9", "tarball": "https://registry.npmjs.org/@types/keyv/-/keyv-3.1.0.tgz", "fileCount": 4, "unpackedSize": 5043, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcKl+MCRA9TVsSAnZWagAA12EQAIsinYHf1MpQ4SCRcIsB\nsQ8giAynpK0sV0QszcwQQ/b7iZceLTXJRFCLaTCjUPhPXsp/CjQdToEbfHFQ\nYCTR7b9pm+8xVft3c8ebL2Mp2ilSasJHZWWjfUN6PlRUg2QE2FNBKZBO2q6v\nD6eKlxzHjmCIq0qaWMpos3DF/mOhw/DIAPwXYRmatpMVZrt4dhBocd4K3Wji\nDTn0/eOeiYbOY4PbZwYSkRlFfK5LDefhVRqGLQiaeLVp8ihitvGApjAWds+f\neuVATwA5mlkpVHupAOdZhqtbJnwL3B+EKyxCyp3HVTmWbGsd6jbJzErO5HCJ\nWpapjS20IJ0Tzyh7tXtflorM3xJ9mlKmQQ1MR80TFfSrPNSE/pKRnWNIUJy3\n5aSr2ihjteT3+ndylFiKasrVrQGm62QRurp7XXbkmhAtBLSi/UixFCDURHgF\nJ5i3tng84OUNqhREAKCJLapUmtiRJRDLjjBk1/F5BB8fY+niZYt/Ihf8M5kb\nwnlQU8PJwXNUkKkQSGCSXALsmvvgQRIiJtZGo4m4AwfNBlWPXD06anK2GEM0\n0DB7ll0+r7j2hhlKTdbxvalHDbNqsUbwb9FqlV68p5LCdYEfgwkYIVcGcVtV\nG3b98VuDJVOYf+ViyIhahB5muDmHOxEYU3+OqVUL9pGHCMCjutDsmMZbHjee\nploB\r\n=CSDh\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBRSwj0ymGqxOakZjsWER00b2Z5Z9azgauiV0F9eSgrUAiBWBdX46E/srrUbsvDRcg2b054ZCbdhJIWxDbgDkgdjew=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/keyv_3.1.0_1546280843858_0.4978870057819742"}, "_hasShrinkwrap": false}, "3.1.1": {"name": "@types/keyv", "version": "3.1.1", "description": "TypeScript definitions for keyv", "license": "MIT", "contributors": [{"name": "AryloYeung", "url": "https://github.com/Arylo", "githubUsername": "Arylo"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/keyv"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "883f9e92997b7991324e37284aa16e71d6f43731110ec0e7f62ccca68960aec3", "typeScriptVersion": "2.8", "_id": "@types/keyv@3.1.1", "dist": {"integrity": "sha512-MPtoySlAZQ37VoLaPcTHCu1RWJ4llDkULYZIzOYxlhxBqYPB0RsRlmMU0R6tahtFe27mIdkHV+551ZWV4PLmVw==", "shasum": "e45a45324fca9dab716ab1230ee249c9fb52cfa7", "tarball": "https://registry.npmjs.org/@types/keyv/-/keyv-3.1.1.tgz", "fileCount": 4, "unpackedSize": 5165, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeAO7bCRA9TVsSAnZWagAAzyoQAJBzt6SQHa3SqYEtqahA\n+X6bUUhlZ34hyytRHpLCg5uV6fRxK45kd8gdEfeIa4rpZJ0fLQpT++AmPHQu\nmcyXOY4y5BaJcH74DZRn7kMS0JoPim+Jb3q1eAqDT5AZu3l2HWAfqhVSEh6A\nukeLL+aMDwJkhpOIlUrhRkJkcqUVHz3X4ijcz8XUi5tzpk2pcoDsGXGo6mCH\n0Gtc/ZZj5bawSwqAN5tOjkZyKYbuHYSPkqg23aYI6ggG41Woi7yLPwkjQJ7T\nCXXFYt/Q4hX5LmS6psDsRztuPzOyFc22P+BZ5WSZM7libcyCVBQMWTi0gdXJ\nzjh821ps8Q+4b691Fqbr6pahWYB12B7/tDeGbGXuWAS37YDU9WLmUaEsbs26\nGHh6e/wgacnIbBaYorX0H/zAae+8fVUHoFvUhfzivx57CKsSN5PF9oqUgAoW\nPNCIl3+tml7zSmEgpr0LRuNGnVOSrqGLH0XiBz7a3FoPX+ArpYiq+z9ygiHc\n129X46yEJ7ghMoiYc47G7FLPOq3y+kTHSv25kJyEFHyFuP2zXwWbRy9grEMz\n6OuVMeNgPGt8dX4U0yrdoqWNcGgtBhJ11tYF3t4suLTNRoxhGd6VoQ3zwYD8\nTZTIF6e/QPqzYfoUGdJWMGWXQ7Es+nIPSuDzmDHDJRTIlPcghsKWnnWN/TqD\nRlzO\r\n=C+84\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCJtW5nmyoT8bsF8boAk1K9/t6ZjZnexnTw4MAujZ1GDQIhALt7ZGMk7Yd0qzDja0T1Bo3arcAV1K5/IhaHrLN+MKUI"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/keyv_3.1.1_1577119450648_0.26656431432322925"}, "_hasShrinkwrap": false}, "3.1.2": {"name": "@types/keyv", "version": "3.1.2", "description": "TypeScript definitions for keyv", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/keyv", "license": "MIT", "contributors": [{"name": "AryloYeung", "url": "https://github.com/Arylo", "githubUsername": "Arylo"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/keyv"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "2b153ef5b8e08f978aea261701c155564f0e696b711bf90a0914388f9598aa95", "typeScriptVersion": "3.6", "_id": "@types/keyv@3.1.2", "dist": {"integrity": "sha512-/FvAK2p4jQOaJ6CGDHJTqZcUtbZe820qIeTg7o0Shg7drB4JHeL+V/dhSaly7NXx6u8eSee+r7coT+yuJEvDLg==", "shasum": "5d97bb65526c20b6e0845f6b0d2ade4f28604ee5", "tarball": "https://registry.npmjs.org/@types/keyv/-/keyv-3.1.2.tgz", "fileCount": 4, "unpackedSize": 5306, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5NOWCRA9TVsSAnZWagAAVv4P/3IvLoTxVPDZ8dD/vWTN\n5U5jJHpFIDuwehPCKnHdOwB00RWPAOzMwXXGYgqG3+3yG8Y8RuOrbXkafX/D\nrLywCoFLvdLvu4WXWbWlLSMu8mwm1YXuEaMTl4MaXaY3c8wyNV1UYE1sVI3U\n+rgLb/hV5b0C2ErvuVqDX+P9LBmpyZx0oZ/i3GP6bLxuOqliQnBX8H43B0uc\nCcP1lenv0zITnh0V5zNQnQbKutOAq8+HrKPOwxH2TrkrFDfe9Ewx6u7gJBnk\nnUq7Zo2l7rFlCnfx8z+BSJtZNHJHcV5CkSFDdrqXDEtiSzLDaQ9lzdjRkNtz\ny6IsH3+hjoWRJIuO1I3Re3H/w/7xRYGDPzCT0l0XVbIyq2O1Wm7q7rHfPFME\n5IHh4JeHfTu26ucTtNwKcb/HO3Aej33JSCvPmNM0efU1xZjlsrEUQiEW51l3\nLZTvZ6Z2X5bwaWOLKFDrZuDr/PsiZgu8/hvPnUFlSoTzqV8b9c/BwCUrd1qn\nLgSkTLobGREFPWhyAZ16hXWLoOJ8usVY1AIB+bc9EYJUDMpM8WlWtRBwyJtT\ndiFcn6ZzZAprtFP/VGR6QOTUi4P41OUY1dpLGhkXnHNQslc5lDgF7tTGb1OC\npF5pKQjr+cfl0NNkOCKS5Tq4T/NGviDPxjJXJ19Un2SWjQRvhuI4uuqaAQxs\nSPcl\r\n=2ccA\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCogzpp5+XgIgXpXqceSvKuOMqfQwyugoyCwLAH3yVf1gIgKFIBAbjWdOmlrAVe16xdmp/M29nl2q3w5+MjACN35n4="}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/keyv_3.1.2_1625609109991_0.791950110656559"}, "_hasShrinkwrap": false}, "3.1.3": {"name": "@types/keyv", "version": "3.1.3", "description": "TypeScript definitions for keyv", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/keyv", "license": "MIT", "contributors": [{"name": "AryloYeung", "url": "https://github.com/Arylo", "githubUsername": "Arylo"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/keyv"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "f8d3aab62b87448dca0f22f85932aed615f961053f7cc2604aad6c374e62c692", "typeScriptVersion": "3.7", "_id": "@types/keyv@3.1.3", "dist": {"integrity": "sha512-FXCJgyyN3ivVgRoml4h94G/p3kY+u/B86La+QptcqJaWtBWtmc6TtkNfS40n9bIvyLteHh7zXOtgbobORKPbDg==", "shasum": "1c9aae32872ec1f20dcdaee89a9f3ba88f465e41", "tarball": "https://registry.npmjs.org/@types/keyv/-/keyv-3.1.3.tgz", "fileCount": 4, "unpackedSize": 5583, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNzGBCRA9TVsSAnZWagAAec8QAJMhvbMI6vVA6mkIrQ0x\nNdYdZrfFu6Za1hBNlmhXPnv0WYWxmidUXkadMI6LoSup39NylBRjg/wu7Fbf\nR+Tti+wOe0dXMtJ/Kv6pUle0XE5PPViyJyuvycRoSOtgANzIiwhYPqyPYBdg\nZVdKyIyT8eAJ0czzTeEs5zpahflIjl1MGk6WH/eDAX7S+0nDioYvQgBanoAF\nx/4MGlEhxlvHkDFNwTRpoqvYjoTkh5jFq2nijxSiQshO+uOUOmauk0g+Sp0z\nZXuCZ7D7pTk0FIIMslUli30wRzljp2KP4CIODPp92qgowjB/hQuN+o8QiS3y\ngv4oJoQ5KdBEREg90/1HkrmsPvVWEe4YAzUSnls0IkxvTzs4g2MoF3Q2pfN/\nRpArnYlB02nMIn5dxGUGhWj+hK83CGiBATVpZ8zmWn6x9yYaZOwMgyagI6cE\nF0h8FUn9RA20l+64PMzK9OpDe20vtSTcSUvxAxFsTIqnESuNb0IXIAIomoM/\njkAulFOhJucJZgUSD9WyFEyHOcWPLw3mLevxGiem1nUxsxdDrr0lQeR14u83\n9Lz7IH11uKx9Rw2YZpTDv7lrHKFskIqlMGBPuB70fvpDsAOIi7u45HsNPYuS\nXEF5eINVb/zxFAwMEfFdJHcT1UiR1a/um8hurKL/1RIYjS2vwVO6B9UkCfqf\npFcT\r\n=+N5p\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFZEtfUBZK3MswMlN/jpQRxv+8R8ToYh8frYO5Z550MWAiEAqFFKQ8GWcNt3lWMwvPf/5B4+TzUxYyR5MpIk81RwEW8="}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/keyv_3.1.3_1631007104875_0.7248541047195243"}, "_hasShrinkwrap": false}, "3.1.4": {"name": "@types/keyv", "version": "3.1.4", "description": "TypeScript definitions for keyv", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/keyv", "license": "MIT", "contributors": [{"name": "AryloYeung", "url": "https://github.com/Arylo", "githubUsername": "Arylo"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/keyv"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "e83393e0860475d12e960cede22532e18e129cf659f31f2a0298a88cb5d02d36", "typeScriptVersion": "3.9", "_id": "@types/keyv@3.1.4", "dist": {"integrity": "sha512-BQ5aZNSCpj7D6K2ksrRCTmKRLEpnPvWDiLPfoGyhZ++8YtiK9d/3DBKPJgry359X/P1PfruyYwvnvwFjuEiEIg==", "shasum": "3ccdb1c6751b0c7e52300bcdacd5bcbf8faa75b6", "tarball": "https://registry.npmjs.org/@types/keyv/-/keyv-3.1.4.tgz", "fileCount": 4, "unpackedSize": 6123, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiMsfEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrH5A/9EAxA+dHhUeGyRaMtvZhvQ6tvW0/WprF7E7C0X02G4TeHTiXR\r\nlEdPP/ktdcaJh39q5ylZ/42Njr5vpbzNNUH4RzTEV6y79LKdzGjYzr3vzMTo\r\no9LA/V/VmdK2oED0pojbvysnnoKg5DRjzuxf9yTRFwG1hpGVhoj9GCA4vRmQ\r\nbvVaj0pLIb6oIfKdbwrsSR/A4b9I2mkVCXWL8VnOO4V2BnnQAuiUhs5tiRLo\r\n6A/BZhS0i+HVLqUwZN7WrUMz6Lqae82ZpwQmW/biQ2UKQMgXnbQUwr/VY2Ep\r\n/a+2xOYc+UldqqdtuzCBpOzEmg+GHM8o4iwgmeIR3YuqcHGc2ZlQymasaUIN\r\nkGc78RaKcadnOuP4BlP74edZUahvoD967UFdRxz4XBCgn/O1zYQoIAorey6b\r\nuDwp4unAYrvqEnublx7T1i11BTxAzDJZ2rPoziZrHLbMz3oPxmBr8a2MwL4z\r\nwR/lw+Nn6lDFM7wvUKJcxjS1u8q4goDJrXVJwI+lVhbCMvBk8U/PRyedChuT\r\n837B8q5vx55TwJ3lJHs47hEAUwT53UDBNmdL96tjUhARFyKvAbh0btmSwvgS\r\nObHkAHHNkYoF/HeszAcMTU+BbVULA02W8UDMNV9N9RQgLyms6cbz7sUoyIJM\r\nzur9icud3GMo/BNvHmPZvX3bVph/y2rl2Ng=\r\n=XNwo\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGDg3NwZMui4Nt1s5dw9W271crP71GSBKAgaWzgFDPENAiBQRQ11IfdT9R1BBf5bzLjGz+1e8cWNvrP2DCy0PvsAfg=="}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/keyv_3.1.4_1647495108781_0.15438612873042645"}, "_hasShrinkwrap": false}, "4.2.0": {"name": "@types/keyv", "version": "4.2.0", "description": "Stub TypeScript definitions entry for keyv, which provides its own types definitions", "main": "", "scripts": {}, "license": "MIT", "dependencies": {"keyv": "*"}, "deprecated": "This is a stub types definition. keyv provides its own type definitions, so you do not need this installed.", "_id": "@types/keyv@4.2.0", "dist": {"integrity": "sha512-xoBtGl5R9jeKUhc8ZqeYaRDx04qqJ10yhhXYGmJ4Jr8qKpvMsDQQrNUvF/wUJ4klOtmJeJM+p2Xo3zp9uaC3tw==", "shasum": "65b97868ab757906f2dbb653590d7167ad023fa0", "tarball": "https://registry.npmjs.org/@types/keyv/-/keyv-4.2.0.tgz", "fileCount": 4, "unpackedSize": 1702, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDqcEZv93TdwAR3uJCOyUMbV/Z8KLHlqM/G20usNoBL5QIhAKVWU6ELhPYJCip5QPeHLNBzBnyjQMFkHX1FqmNf269P"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTxTiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo8/Q/+NfJS88DRAPsn5l7ypKaHV9W07/Ws+3vzKyVdCj32GI1k1IOd\r\nSlW9Me/pgxFULCCvDfK0qKuqJKEoM94Q2NmTWoE5ykmqe7mFlWHj4Uu1Rjr6\r\nWF/CPJoHeMr8ba91uqfuzl2BvIf22/4WqOp1vkd2ss5LFW429lrqangmi54a\r\nJpEQU/pXh7i8yX0UisuVu4obT5behDu972FDgPzbUO8fSXcdaRJ28J86+hJv\r\niWN7bSKZBQVypcBheB7gDtDp6aPvUQHl7E4iq0rsidBgI0llx1bvCXl9Bkxt\r\nulYJwdstqU4gAc8nJ1b9pqrefo0vuaveYxMnaOhqULDhkCQsnlc/1Y8zp0kf\r\nNsNulGn1Gk30f1zKb+vSdTcRBrN4aTG0pLb9PODq3E+Ljpp4vYVm/37dI4gw\r\ntYpXLYSJKZABdrVKjZyUydJNswSlNaec4kC9rXK3FYC/Wf4xlnxEP6bQUbrY\r\n/G7iXgSV/N3pJ9mpdbLqZL1RI9GoXaitfvBb3sVoNLCqEtsJKDbfyAt/ClaT\r\npV23DNAfj2dlShdVEjPGZy3owoB+3STPk2G7r6t8J2DNp0CBgilfeyXiB8wP\r\nBqdogtIYPp+DkZW3gOr2eOZLdlWctMCN9a89RWEL8HdoIcOwpclwAkuO4b8Q\r\nmvFKFLhHJ37bMKVRRyjgFRgsU0NDtadJnI8=\r\n=SEqZ\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/keyv_4.2.0_1666127074429_0.7322157267222644"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-03-23T22:52:05.125Z", "3.0.0": "2018-03-23T22:52:05.316Z", "modified": "2022-10-18T21:04:34.655Z", "3.0.1": "2018-05-04T23:58:20.909Z", "3.1.0": "2018-12-31T18:27:23.997Z", "3.1.1": "2019-12-23T16:44:10.770Z", "3.1.2": "2021-07-06T22:05:10.109Z", "3.1.3": "2021-09-07T09:31:45.051Z", "3.1.4": "2022-03-17T05:31:48.941Z", "4.2.0": "2022-10-18T21:04:34.585Z"}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "description": "Stub TypeScript definitions entry for keyv, which provides its own types definitions", "license": "MIT", "readme": "[object Object]", "readmeFilename": ""}