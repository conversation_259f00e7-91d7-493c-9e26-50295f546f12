{"_id": "pkginfo", "_rev": "44-34d406ae4e71641ba73c4ab5ea1d22d8", "name": "pkginfo", "description": "An easy way to expose properties on a module from a package.json", "dist-tags": {"latest": "0.4.1"}, "versions": {"0.2.1": {"name": "pkginfo", "version": "0.2.1", "description": "An easy way to expose properties on a module from a package.json", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/indexzero/pkginfo.git"}, "keywords": ["info", "tools", "package.json"], "devDependencies": {"vows": "0.5.x"}, "main": "./lib/pkginfo", "scripts": {"test": "vows test/*-test.js --spec"}, "engines": {"node": ">= 0.4.0"}, "_id": "pkginfo@0.2.1", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.10", "_nodeVersion": "v0.4.8", "_defaultsLoaded": true, "dist": {"shasum": "40fd285c83b52dbd549fbd633c154607fe38fc89", "tarball": "https://registry.npmjs.org/pkginfo/-/pkginfo-0.2.1.tgz", "integrity": "sha512-624KhiKnvrIcjf+p5M0UNJn3SEgXWa/EZ2I2YKW90x226ue0zgrRYMLiQ3Dq7rxQzjzoVCTrtUTP3ExtGu6j+g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICSxyG2rSeh4SQ9jrWvSkQLBQUOpGtz7M7q/0tO5KtAPAiEA3Q/j1STEbYbmsHIGeqmEnzJ6sUrHFmi7YsAj05ETSsw="}]}, "directories": {}}, "0.2.2": {"name": "pkginfo", "version": "0.2.2", "description": "An easy way to expose properties on a module from a package.json", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/indexzero/pkginfo.git"}, "keywords": ["info", "tools", "package.json"], "devDependencies": {"vows": "0.5.x"}, "main": "./lib/pkginfo", "scripts": {"test": "vows test/*-test.js --spec"}, "engines": {"node": ">= 0.4.0"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/pkginfo/0.2.2/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "pkginfo@0.2.2", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.15", "_nodeVersion": "v0.4.9", "_defaultsLoaded": true, "dist": {"shasum": "97e1100dbbb275ff6fab583a256a7eea85120c8e", "tarball": "https://registry.npmjs.org/pkginfo/-/pkginfo-0.2.2.tgz", "integrity": "sha512-rGw3wS84FpWM+l7hzzHDBl6sTkRi4WkAvBntj1+bhPalJIzGa2rTUVGlCyVjBUrJD1wscbfEqypRt/VWKgOCbQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDGsozwk3WHDGwFpLgYciFEnpfivQF/3R8qoFIYgNJjpQIgRytUgbxyk7k48F6KkUFtZEWAOt9Xx458B5TCjxx2WmQ="}]}, "directories": {}}, "0.2.3": {"name": "pkginfo", "version": "0.2.3", "description": "An easy way to expose properties on a module from a package.json", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/indexzero/node-pkginfo.git"}, "keywords": ["info", "tools", "package.json"], "devDependencies": {"vows": "0.6.x"}, "main": "./lib/pkginfo", "scripts": {"test": "vows test/*-test.js --spec"}, "engines": {"node": ">= 0.4.0"}, "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "_id": "pkginfo@0.2.3", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"shasum": "7239c42a5ef6c30b8f328439d9b9ff71042490f8", "tarball": "https://registry.npmjs.org/pkginfo/-/pkginfo-0.2.3.tgz", "integrity": "sha512-7W7wTrE/NsY8xv/DTGjwNIyNah81EQH0MWcTzrHL6pOpMocOGZc0Mbdz9aXxSrp+U0mSmkU8jrNCDCfUs3sOBg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBv0UIYfupV/utY+BtLr+OuZRKfvrYwXr175xtqtdZ5SAiEA1IjI/4Zm7b+3lzKZ3mXEXq0yr5gVlhOEinLwAiXnTec="}]}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "directories": {}}, "0.3.0": {"name": "pkginfo", "version": "0.3.0", "description": "An easy way to expose properties on a module from a package.json", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "http://github.com/indexzero/node-pkginfo.git"}, "keywords": ["info", "tools", "package.json"], "devDependencies": {"vows": "0.7.x"}, "main": "./lib/pkginfo", "scripts": {"test": "vows test/*-test.js --spec"}, "engines": {"node": ">= 0.4.0"}, "_id": "pkginfo@0.3.0", "dist": {"shasum": "726411401039fe9b009eea86614295d5f3a54276", "tarball": "https://registry.npmjs.org/pkginfo/-/pkginfo-0.3.0.tgz", "integrity": "sha512-Q4uZFfasmQ7GagbOAcVXGKlcL2Lt01A7Mt+qBd1Geo4hiqo8k+SG+NEiEbTR2R1UjbHQUOIpB7FtJXc36PL4yw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCimB7E9ToCffI2zQH6iU8Px0ZdRxEozFcnLhT2uQMjtgIhAM/ik9oq0/vqNGX5DOz7Q6c52zsEfPbuPyvcZf6cP+1G"}]}, "_npmVersion": "1.1.66", "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "directories": {}}, "0.3.1": {"name": "pkginfo", "version": "0.3.1", "license": "MIT", "description": "An easy way to expose properties on a module from a package.json", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/indexzero/node-pkginfo.git"}, "bugs": {"url": "https://github.com/indexzero/node-pkginfo/issues"}, "keywords": ["info", "tools", "package.json"], "devDependencies": {"vows": "0.7.x"}, "main": "./lib/pkginfo.js", "scripts": {"test": "vows test/*-test.js --spec"}, "engines": {"node": ">= 0.4.0"}, "gitHead": "630fcf486543ee48b4c16afc575c0421fe039f26", "homepage": "https://github.com/indexzero/node-pkginfo#readme", "_id": "pkginfo@0.3.1", "_shasum": "5b29f6a81f70717142e09e765bbeab97b4f81e21", "_from": ".", "_npmVersion": "2.14.1", "_nodeVersion": "0.10.38", "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "dist": {"shasum": "5b29f6a81f70717142e09e765bbeab97b4f81e21", "tarball": "https://registry.npmjs.org/pkginfo/-/pkginfo-0.3.1.tgz", "integrity": "sha512-yO5feByMzAp96LtP58wvPKSbaKAi/1C4kV9XpTctr6EepnP6F33RBNOiVrdz9BrPA98U2BMFsTNHo44TWcbQ2A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDhrCihw0JIVkulDEGwipVr9U+utnavBhYI52xISVw+SgIgXt3znbvJFXjRzVMoQXxljtSOP1Opb4Jk0yLfBfoonYU="}]}, "directories": {}}, "0.4.0": {"name": "pkginfo", "version": "0.4.0", "license": "MIT", "description": "An easy way to expose properties on a module from a package.json", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/indexzero/node-pkginfo.git"}, "bugs": {"url": "https://github.com/indexzero/node-pkginfo/issues"}, "keywords": ["info", "tools", "package.json"], "devDependencies": {"vows": "0.8.0"}, "main": "./lib/pkginfo.js", "scripts": {"test": "vows test/*-test.js --spec"}, "engines": {"node": ">= 0.4.0"}, "gitHead": "f0cc3fa9d45413bdabd2d160bb0dbe03e2d04870", "homepage": "https://github.com/indexzero/node-pkginfo#readme", "_id": "pkginfo@0.4.0", "_shasum": "349dbb7ffd38081fcadc0853df687f0c7744cd65", "_from": ".", "_npmVersion": "3.7.3", "_nodeVersion": "4.2.2", "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "dist": {"shasum": "349dbb7ffd38081fcadc0853df687f0c7744cd65", "tarball": "https://registry.npmjs.org/pkginfo/-/pkginfo-0.4.0.tgz", "integrity": "sha512-PvRaVdb+mc4R87WFh2Xc7t41brwIgRFSNoDmRyG0cAov6IfnFARp0GHxU8wP5Uh4IWduQSJsRPSwaKDjgMremg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDL0ixSEqeZuIp2IYOM3U4APEd34tsAEOu+cTQCjQaH/AiB67GCcn9cGvGt3MF/ScoZEAcmscUDW2MNgvjx5QBcKaw=="}]}, "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/pkginfo-0.4.0.tgz_1456888821214_0.3420205994043499"}, "directories": {}}, "0.4.1": {"name": "pkginfo", "version": "0.4.1", "license": "MIT", "description": "An easy way to expose properties on a module from a package.json", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/indexzero/node-pkginfo.git"}, "bugs": {"url": "https://github.com/indexzero/node-pkginfo/issues"}, "keywords": ["info", "tools", "package.json"], "devDependencies": {"vows": "0.8.0"}, "main": "./lib/pkginfo.js", "scripts": {"test": "vows test/*-test.js --spec"}, "engines": {"node": ">= 0.4.0"}, "gitHead": "f4e90ac77034ee9eb3dd7aafc56657d7376795a8", "homepage": "https://github.com/indexzero/node-pkginfo#readme", "_id": "pkginfo@0.4.1", "_shasum": "b5418ef0439de5425fc4995042dced14fb2a84ff", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.10.0", "_npmUser": {"name": "indexzero", "email": "<EMAIL>"}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "dist": {"shasum": "b5418ef0439de5425fc4995042dced14fb2a84ff", "tarball": "https://registry.npmjs.org/pkginfo/-/pkginfo-0.4.1.tgz", "integrity": "sha512-8xCNE/aT/EXKenuMDZ+xTVwkT8gsoHN2z/Q29l80u0ppGEXVvsKRzNMbtKhg8LS8k1tJLAHHylf6p4VFmP6XUQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDzEr2m2L3/PsMOJ0Pj1HmJJUrC5DO3+FUzhzon0mGCwwIgBkMXUBKfo3pXmpOF+kr5LJ8KvzYGaZtHyEtpiom5GEA="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/pkginfo-0.4.1.tgz_1503460521335_0.48146630520932376"}, "directories": {}}}, "maintainers": [{"name": "indexzero", "email": "<EMAIL>"}], "time": {"modified": "2023-06-24T22:12:50.654Z", "created": "2011-06-04T03:19:54.635Z", "0.1.0": "2011-06-04T03:19:55.001Z", "0.2.0": "2011-06-08T03:48:48.652Z", "0.2.1": "2011-06-08T09:38:59.614Z", "0.2.2": "2011-07-13T21:42:39.570Z", "0.2.3": "2011-12-08T23:50:52.279Z", "0.3.0": "2013-01-18T06:59:12.201Z", "0.3.1": "2015-10-20T14:54:28.048Z", "0.4.0": "2016-03-02T03:20:23.448Z", "0.4.1": "2017-08-23T03:55:22.290Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/indexzero/node-pkginfo.git"}, "users": {"luuse": true, "fgribreau": true, "stdarg": true, "stoneren": true, "pstoev": true, "iamveen": true, "mrmartineau": true, "loselovegirl": true, "recursion_excursion": true, "demiurgosoft": true, "roccomuso": true, "aquiandres": true, "yabasha": true, "joaquin.briceno": true, "dm7": true, "cbetancourt": true, "l8niteowl": true, "xiaobing": true}, "readme": "# node-pkginfo\n\nAn easy way to expose properties on a module from a package.json\n\n### Installing pkginfo\n```\n  npm install pkginfo\n```\n\n## Motivation\nHow often when writing node.js modules have you written the following line(s) of code?\n\n* Hard code your version string into your code\n\n``` js\n  exports.version = '0.1.0';\n```\n\n* Programmatically expose the version from the package.json\n\n``` js\n  exports.version = require('/path/to/package.json').version;\n```\n\nIn other words, how often have you wanted to expose basic information from your package.json onto your module programmatically? **WELL NOW YOU CAN!**\n\n## Usage\n\nUsing `pkginfo` is idiot-proof, just require and invoke it.\n\n``` js\n  var pkginfo = require('pkginfo')(module);\n\n  console.dir(module.exports);\n```\n\nBy invoking the `pkginfo` module all of the properties in your `package.json` file will be automatically exposed on the callee module (i.e. the parent module of `pkginfo`).\n\nHere's a sample of the output:\n\n```\n  { name: 'simple-app',\n    description: 'A test fixture for pkginfo',\n    version: '0.1.0',\n    author: '<PERSON> <<EMAIL>>',\n    keywords: [ 'test', 'fixture' ],\n    main: './index.js',\n    scripts: { test: 'vows test/*-test.js --spec' },\n    engines: { node: '>= 0.4.0' } }\n```\n\n### Expose specific properties\nIf you don't want to expose **all** properties on from your `package.json` on your module then simple pass those properties to the `pkginfo` function:\n\n``` js\n  var pkginfo = require('pkginfo')(module, 'version', 'author');\n\n  console.dir(module.exports);\n```\n\n```\n  { version: '0.1.0',\n    author: 'Charlie Robbins <<EMAIL>>' }\n```\n\nIf you're looking for further usage see the [examples][0] included in this repository.\n\n## Run Tests\nTests are written in [vows][1] and give complete coverage of all APIs.\n\n```\n  npm install\n  npm test\n```\n\n[0]: https://github.com/indexzero/node-pkginfo/tree/master/examples\n[1]: http://vowsjs.org\n\n#### Author: [Charlie Robbins](http://github.com/indexzero)\n#### License: MIT\n", "homepage": "https://github.com/indexzero/node-pkginfo#readme", "keywords": ["info", "tools", "package.json"], "bugs": {"url": "https://github.com/indexzero/node-pkginfo/issues"}, "license": "MIT", "readmeFilename": "README.md"}