{"_id": "p-cancelable", "_rev": "20-ac48f2c7a0fa9e6c8f19707af90bf2b4", "name": "p-cancelable", "description": "Create a promise that can be canceled", "dist-tags": {"latest": "4.0.1"}, "versions": {"0.1.0": {"name": "p-cancelable", "version": "0.1.0", "description": "Create a promise that can be canceled", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/p-cancelable.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["promise", "cancelable", "cancel", "canceled", "canceling", "cancellable", "cancellation", "abort", "abortable", "aborting", "cleanup", "task", "token", "async", "function", "await", "promises", "bluebird"], "devDependencies": {"ava": "*", "delay": "^1.3.1", "xo": "*"}, "xo": {"esnext": true}, "gitHead": "5e1f5b4d5b8b0f7549c87dbae0417c4afe9c6d8f", "bugs": {"url": "https://github.com/sindresorhus/p-cancelable/issues"}, "homepage": "https://github.com/sindresorhus/p-cancelable#readme", "_id": "p-cancelable@0.1.0", "_shasum": "7a551d2b87cd825e927683ab996b13abdbd53ed2", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.6.2", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "7a551d2b87cd825e927683ab996b13abdbd53ed2", "tarball": "https://registry.npmjs.org/p-cancelable/-/p-cancelable-0.1.0.tgz", "integrity": "sha512-/UzcxtKyX5zOy0VUMxpLDcW5lvwrTtXBw8aFCjwFx2cFXMkmFcmgiz9X5LF2s9hmb3+9IMi5G4Zp97JoSeVTfw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDYoM1hFn9X0eqXZKmtvMGOL/v+qaEcsS7YMySLvf6eogIhAJ3Kt557uJ5afUwpbKTuA8XA0p1mRxqFzTB7wSNYyokW"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/p-cancelable-0.1.0.tgz_1480330568731_0.36330019892193377"}, "directories": {}}, "0.2.0": {"name": "p-cancelable", "version": "0.2.0", "description": "Create a promise that can be canceled", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/p-cancelable.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["promise", "cancelable", "cancel", "canceled", "canceling", "cancellable", "cancellation", "abort", "abortable", "aborting", "cleanup", "task", "token", "async", "function", "await", "promises", "bluebird"], "devDependencies": {"ava": "*", "delay": "^2.0.0", "xo": "*"}, "gitHead": "3da2d57d79c0ba9c68db62822cae124dcc5a0d9f", "bugs": {"url": "https://github.com/sindresorhus/p-cancelable/issues"}, "homepage": "https://github.com/sindresorhus/p-cancelable#readme", "_id": "p-cancelable@0.2.0", "_shasum": "3152f4f30be7606b60ebfe8bb93b3fdf69085e46", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "3152f4f30be7606b60ebfe8bb93b3fdf69085e46", "tarball": "https://registry.npmjs.org/p-cancelable/-/p-cancelable-0.2.0.tgz", "integrity": "sha512-DWvnD5odHJZ1aENQgtuNcnW2Jeim9NmS8MneDHDH2PrHN/dJ/mtVxohkdNsIncVDntHPPUagyObHPrMZxz1+sA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAC4pFfqhaoejeBEGOcVTrgayDhYC840udkFl6CMBoGTAiEApp9JQKE+ouceseMo8Kvw/l2rp+wm9PC6VAkV9I5uyNU="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/p-cancelable-0.2.0.tgz_1493962754416_0.057322570122778416"}, "directories": {}}, "0.3.0": {"name": "p-cancelable", "version": "0.3.0", "description": "Create a promise that can be canceled", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/p-cancelable.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["promise", "cancelable", "cancel", "canceled", "canceling", "cancellable", "cancellation", "abort", "abortable", "aborting", "cleanup", "task", "token", "async", "function", "await", "promises", "bluebird"], "devDependencies": {"ava": "*", "delay": "^2.0.0", "xo": "*"}, "gitHead": "0ea492a3a02e5b8bb984b7a7f1db60a31f66da0b", "bugs": {"url": "https://github.com/sindresorhus/p-cancelable/issues"}, "homepage": "https://github.com/sindresorhus/p-cancelable#readme", "_id": "p-cancelable@0.3.0", "_npmVersion": "5.0.0", "_nodeVersion": "8.0.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-RVbZPLso8+jFeq1MfNvgXtCRED2raz/dKpacfTNxsx6pLEpEomM7gah6VeHSYV3+vo0OAi4MkArtQcWWXuQoyw==", "shasum": "b9e123800bcebb7ac13a479be195b507b98d30fa", "tarball": "https://registry.npmjs.org/p-cancelable/-/p-cancelable-0.3.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCuBI0/qX9JZpC8VvVqvTJ7+JH/cX/dk97cuozwKUDdRgIhAOJKp70g0NhJB7XorYZ7uPqXl1FKb+eXA5pfGFG9DN8P"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/p-cancelable-0.3.0.tgz_1498837734166_0.038153667002916336"}, "directories": {}}, "0.4.0": {"name": "p-cancelable", "version": "0.4.0", "description": "Create a promise that can be canceled", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/p-cancelable.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["promise", "cancelable", "cancel", "canceled", "canceling", "cancellable", "cancellation", "abort", "abortable", "aborting", "cleanup", "task", "token", "async", "function", "await", "promises", "bluebird"], "devDependencies": {"ava": "*", "delay": "^2.0.0", "xo": "*"}, "gitHead": "172141757dff0acaf3d6a93473c1ba3c2e78a7aa", "bugs": {"url": "https://github.com/sindresorhus/p-cancelable/issues"}, "homepage": "https://github.com/sindresorhus/p-cancelable#readme", "_id": "p-cancelable@0.4.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.4", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-/AodqPe1y/GYbhSlnMjxukLGQfQIgsmjSy2CXCNB96kg4ozKvmlovuHEKICToOO/yS3LLWgrWI1dFtFfrePS1g==", "shasum": "bcb41d35bf6097fc4367a065b6eb84b9b124eff0", "tarball": "https://registry.npmjs.org/p-cancelable/-/p-cancelable-0.4.0.tgz", "fileCount": 4, "unpackedSize": 6866, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDCwmSNJTw1qtbBvufC+3YdNoGECp64zkkXd5X45INHJQIhALw/FEl2RpyCf4tbGnxB5azk3Nv/92m2l1v/pG1cy2Cu"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/p-cancelable_0.4.0_1520348511879_0.719711445299539"}, "_hasShrinkwrap": false}, "0.4.1": {"name": "p-cancelable", "version": "0.4.1", "description": "Create a promise that can be canceled", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/p-cancelable.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["promise", "cancelable", "cancel", "canceled", "canceling", "cancellable", "cancellation", "abort", "abortable", "aborting", "cleanup", "task", "token", "async", "function", "await", "promises", "bluebird"], "devDependencies": {"ava": "*", "delay": "^2.0.0", "promise.prototype.finally": "^3.1.0", "xo": "*"}, "gitHead": "50d66ce7228c23511a754a03d52aebf55e0229b1", "bugs": {"url": "https://github.com/sindresorhus/p-cancelable/issues"}, "homepage": "https://github.com/sindresorhus/p-cancelable#readme", "_id": "p-cancelable@0.4.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-HNa1A8LvB1kie7cERyy21VNeHb2CWJJYqyyC2o3klWFfMGlFmWv2Z7sFgZH8ZiaYL95ydToKTFVXgMV/Os0bBQ==", "shasum": "35f363d67d52081c8d9585e37bcceb7e0bbcb2a0", "tarball": "https://registry.npmjs.org/p-cancelable/-/p-cancelable-0.4.1.tgz", "fileCount": 4, "unpackedSize": 6976, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDdfYWxDYSx8B+HxbvjmDTiQyF+tkK5s2MfksXLvcHu3gIgJniKbpDVqOPWhlJgTtUdnJa5JTx2Sl69KFWl3ixIujA="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/p-cancelable_0.4.1_1522581779099_0.05425666370241489"}, "_hasShrinkwrap": false}, "0.5.0": {"name": "p-cancelable", "version": "0.5.0", "description": "Create a promise that can be canceled", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/p-cancelable.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["promise", "cancelable", "cancel", "canceled", "canceling", "cancellable", "cancellation", "abort", "abortable", "aborting", "cleanup", "task", "token", "async", "function", "await", "promises", "bluebird"], "devDependencies": {"ava": "*", "delay": "^3.0.0", "promise.prototype.finally": "^3.1.0", "xo": "*"}, "gitHead": "54b12bba45d3b38180f7a490bc2bc24e384612ac", "bugs": {"url": "https://github.com/sindresorhus/p-cancelable/issues"}, "homepage": "https://github.com/sindresorhus/p-cancelable#readme", "_id": "p-cancelable@0.5.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.2", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-UTykUaG4vhPAMNi1/GZKGdCNDj4qGnq6taAjnOG0KCx5Fva3b1vtskXtagAePmgIhUrdcoDGEvAQIHaSPXEZWg==", "shasum": "65b8dfbcacf27983421ee4c044ebf5e35816c7c8", "tarball": "https://registry.npmjs.org/p-cancelable/-/p-cancelable-0.5.0.tgz", "fileCount": 4, "unpackedSize": 7032, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbPGdvCRA9TVsSAnZWagAAjrUP/2r3TGaqEBzVojRiVLac\nke7HOPnYjAFac5zeApVwTCnWBAGzW17+KystDtptBD2FoowgA/ECX4Eo2ict\nycONqhizohEnBj82WfyCB8c/sImheni/VO7vuek1gUgzIBF3q9v5qSruoB6d\ny+wDwFuhNWwXrMaFA1MAmm9XjyVVS6z9oNzzMvcsnfvBWMZwEkbYymT/S6ml\n/U0z9v38EUPdgqCuG6GJCAzyBrqFk9msZzm9zqmBId+WCWVfDnOyFxkQuujn\ny9n+17DYRiyriSodo1VCCl7VjbP+xJFnzAoINFa7P3es8s72WTXMi2gXJatH\nWd3rNWKkEqPs4nOqReOyocZAZi6p/Z3pqxTL9naYVhRG15tC5toAW8pMawSR\n5Liw5JalMRNdJR7LcmC91s2t4YtGwIgGdzcUQ0bWEYKi8h2c/UpDuap9WFoS\nQQ6u+s59j8dBoNnqlaDB/F/Br6aGyKb1xAHWKjmKGd63/FeR1CBgz2lNa5rP\n7RN5Ew7znnb0yVmyF7nEnnKVmCQ8Jp2h9GkkZhnFsmfp+ZOmzWCiLyEjmem6\nzdl+UccLhgjfr2HbC1T9sF5oMd+dqQa1Gd9DfEyhVGrklaCpxddHhfscAexP\nWByU0G+iGZn6VRxLQm10R2nzwM0uoaqpL1KxAMPwQToDAw+LMNRA1S3b5o+L\n4V7V\r\n=0GnV\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHKJil3ZapVIVVTfOVnnJTYKMBGLBpyn3Mr12WzJswwsAiEAh5Os/6adk6Xbnk18MP5wW61zZxI2pPhFZJ4wYnTJAmo="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/p-cancelable_0.5.0_1530685295652_0.4673471413509742"}, "_hasShrinkwrap": false}, "0.5.1": {"name": "p-cancelable", "version": "0.5.1", "description": "Create a promise that can be canceled", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/p-cancelable.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava"}, "keywords": ["promise", "cancelable", "cancel", "canceled", "canceling", "cancellable", "cancellation", "abort", "abortable", "aborting", "cleanup", "task", "token", "async", "function", "await", "promises", "bluebird"], "devDependencies": {"ava": "*", "delay": "^3.0.0", "promise.prototype.finally": "^3.1.0", "xo": "^0.23.0"}, "gitHead": "b7e75aa16c846286e7426b31d659dd1a6da09fb5", "bugs": {"url": "https://github.com/sindresorhus/p-cancelable/issues"}, "homepage": "https://github.com/sindresorhus/p-cancelable#readme", "_id": "p-cancelable@0.5.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.4", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-vkOBXQgQb03QTOoMeeB5/uS2W3iafXzQLaIh7ChHjEb8DDT06sWJizhdOACL1Sittl5dFqsyumJ4rD1WUF8Isw==", "shasum": "b797a33c43c645cd70d5a838b1d25352b9e29e75", "tarball": "https://registry.npmjs.org/p-cancelable/-/p-cancelable-0.5.1.tgz", "fileCount": 4, "unpackedSize": 7724, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJblpyOCRA9TVsSAnZWagAAmAoP/j2qfNZtIo31F3s2LOlM\npcy5ZwceNtPlth0py0LHXkOnMwBguQ3U74qoqrogLOwnEaf+Uo4n4NMwJ9A0\nHyvjK4FTp/vawWvyOAYdhfUnspYHGfUVN3wVGR+BOliV+i6axeZuVgK2W9WX\nlxHVdfm8lhcaxOO8b42iamCWTjcASlFTqvNUzQbW5jT8LLw/Fvj4o+F9dKmE\nM4RmIzfXmvY/wNJrDgOVuy1mM8Tg6enOBIaK1yLCVcBnWZtbm7C24CbUkmxO\n0aOBiO4ATJ3ZtzbTks0egtWBYCt72wCIyyCVrYn7NeOKqeSUgRbHRxlcZshJ\nKJ0PJHfCezHssGBh6TIgIAL/lD77RK3i8tqriBLaIxxy01as6PqT+FjHfrhE\nEHyplFkSBXOCGiB53WkX4h2ghBZg8uI2znEMoz5RIqAtaupK8YDz7bRQUK7Z\ndyrpdfIVLAlL2egLOrU0PDFAbzGYbyjIHObd7R01z0aSS+UgwByU2Hu5p2wu\n8HNx6uMMM6xM9yACrpsZjkMybGVzej5MaeLpg2m0m6i6Mh/Gry4L03JN5kbw\nrUlx6Rnr0MGuuzulMc3sft/6zs8zL3KEYPRQWySlkvt5E171QM0NdNHywN4v\nTrajXow63H/Lw6gjGtxgpZlhFwbuOUUijhN192r1LU/F+sD7PkiXCjS87NZZ\ncL7a\r\n=DMF0\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDmD2Rgwazp7lvyMSHl5reipDegQq5hzjcygCdBXcN/dgIgIJF4Kkzt6c5Om/oC04GmX8xWJasKlmGViWo8FmPZZF8="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/p-cancelable_0.5.1_1536597133529_0.9791231164444081"}, "_hasShrinkwrap": false}, "1.0.0": {"name": "p-cancelable", "version": "1.0.0", "description": "Create a promise that can be canceled", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/p-cancelable.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava"}, "keywords": ["promise", "cancelable", "cancel", "canceled", "canceling", "cancellable", "cancellation", "abort", "abortable", "aborting", "cleanup", "task", "token", "async", "function", "await", "promises", "bluebird"], "devDependencies": {"ava": "^0.25.0", "delay": "^4.1.0", "promise.prototype.finally": "^3.1.0", "xo": "^0.23.0"}, "gitHead": "354c4000c3b7602faa601e7e060a05d581b89419", "bugs": {"url": "https://github.com/sindresorhus/p-cancelable/issues"}, "homepage": "https://github.com/sindresorhus/p-cancelable#readme", "_id": "p-cancelable@1.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "8.12.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-USgPoaC6tkTGlS831CxsVdmZmyb8tR1D+hStI84MyckLOzfJlYQUweomrwE3D8T7u5u5GVuW064LT501wHTYYA==", "shasum": "07e9c6d22c31f9c6784cb4f1e1454a79b6d9e2d6", "tarball": "https://registry.npmjs.org/p-cancelable/-/p-cancelable-1.0.0.tgz", "fileCount": 4, "unpackedSize": 7859, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbzXxwCRA9TVsSAnZWagAA+pcP/j/rFF2dyPsgk5AoA0Iv\nlemfjn4JasBh9DSXOzLG5PRKwwQhq1ABbIOgScGQQzIStQPRccVOguNYvNnI\nYxqBY+SOd9gUEoB7bDBLLMn+Ein7fa9yfzR4+skCD+8FdYrq+upXT6OueRtu\n3VoymSyDf6lfl2DQ+zvii/ZzP4gVPFEwtjBAMlxUciQflNn0PkfxHLEgSgzd\naosWznFKQXDSTYWq1K6U/6rX+kL0eGfFbCD6usiVBrnV4nKAqfoJNF2l8C90\nOPKDFDpIC8pq7mo8K8uIoV2+/vggq+7QzNzEzaVvSkqt1Jvebp6NWT6h+rVA\nygZfxul30f60Nm8UYrF5ph6Ve/6agdmj5CUiRZe62RYlYexzoJX/vkNfRHNA\nigRgmQePSRbfFiosv7wrlWlKlJPzEGn2FussbaHL96ygM27G3bArbCtA0Rxt\n2poS21agi3L1LONuuBG4/8WpcJEcZg5UkIvdh1B/92XSSbx7dyl9wSgf5/Jt\nT9WV6x3NmNjZc0RzmPtVlx7+xJ19ffNaCgUkKwJSHtzk7/vUlyEem4Avv3Pv\nOHdHj1pxSee+XKlqmJWpggPdjxMtkl4WQrZRGYdOheeZ9zK7+esniT//9P+d\nlbY15NCLPm6poc1M8rQhG85lnE8EFDvh4B8WTU6eSCQF/8dyK7N2o43vbV7X\n0wK+\r\n=v59/\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDaK2WZ88arIo27u2RtyRd0c0gEFMb6PRx25j1TvT+NoQIgLMR3/RwMgoCp4L4opvmX+32cyYQRWZDKRt4VsLNHHPY="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/p-cancelable_1.0.0_1540193391787_0.31877812620970536"}, "_hasShrinkwrap": false}, "1.1.0": {"name": "p-cancelable", "version": "1.1.0", "description": "Create a promise that can be canceled", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/p-cancelable.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd-check"}, "keywords": ["promise", "cancelable", "cancel", "canceled", "canceling", "cancellable", "cancellation", "abort", "abortable", "aborting", "cleanup", "task", "token", "async", "function", "await", "promises", "bluebird"], "devDependencies": {"ava": "^1.3.1", "delay": "^4.1.0", "promise.prototype.finally": "^3.1.0", "tsd-check": "^0.3.0", "xo": "^0.24.0"}, "gitHead": "8746e2a93a925e4e28a7848b6e08cad799f33ddd", "bugs": {"url": "https://github.com/sindresorhus/p-cancelable/issues"}, "homepage": "https://github.com/sindresorhus/p-cancelable#readme", "_id": "p-cancelable@1.1.0", "_nodeVersion": "8.15.0", "_npmVersion": "6.8.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-s73XxOZ4zpt1edZYZzvhqFa6uvQc1vwUa0K0BdtIZgQMAJj9IbebH+JkgKZc9h+B05PKHLOTl4ajG1BmNrVZlw==", "shasum": "d078d15a3af409220c886f1d9a0ca2e441ab26cc", "tarball": "https://registry.npmjs.org/p-cancelable/-/p-cancelable-1.1.0.tgz", "fileCount": 5, "unpackedSize": 12515, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcgiscCRA9TVsSAnZWagAAlT4P/RRYuvZxdtzqjQ1Qne9s\nN9IfUkgrXB8HDImsX3t6rVchQIsLB1FRr0K2IT8ORf2WFTNUDQ3h9GxgMRNv\nMP5jQpVwTsN2ZR+hBp74PG+600x0eTTyVZO6L6yUo4YtazFssaLT0bMvSYZx\nAIP48RG2D2CaS7xo3fd2dQjXC2EtgmIGprUTUQVHByd/mIeyr4UV5cMywpJF\nTrVSX5hD/ZG+r1q4dNJ31piAyAK925gkMOOPlpBn9pljN57FB7uOukS/e2FX\nzmMzYhFU1AkTZmhbiQgon7kb7XGHC1R3KPTuE1/boB0rLZkQGBrEy8taKxXg\ncJ8q5Lgn8XhFl+Ou1t2aC1CelBXn0yWA+nBSsE1UyqS+cblw2HoPJOB0vt9N\noSWgcxAB2Tpkk0xsuOIcP/Y1Dan+Py6uO3IfC7PEydt7EFap9JJAL0R0Wnxw\nf+Ht0OoteCovHg2Yumdqui39yNuHwX8IxmyJWizCFT0Qhr5pUhdIusOoWx45\nVAqe5XGsIIosoXpW760u9iPGnlMGx3qul2zwLysDk+kw7czBZvmzNHGe4F03\n+0GF5x+xi5YRVqauLPmJc+2BesAoszit0FJ0fCbc6+krMlC0sD4bxxeTOJ7o\nP8Vqan2Z9DrV38av6En+ys8Zs+NRSESZRng6wAP3KGqZH13KfueDIB5TfLU/\nsPQG\r\n=uzFX\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCjo+UaLGvG0xsjbGyRFgYFzsDGnTU3orNOw+KSWfLI9wIhAP+f2KEiPfY/ixX7Nx4YZKRRGVyeW04vKiVjCuMqhidJ"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/p-cancelable_1.1.0_1552034587600_0.8347780998024625"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "p-cancelable", "version": "2.0.0", "description": "Create a promise that can be canceled", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/p-cancelable.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["promise", "cancelable", "cancel", "canceled", "canceling", "cancellable", "cancellation", "abort", "abortable", "aborting", "cleanup", "task", "token", "async", "function", "await", "promises", "bluebird"], "devDependencies": {"ava": "^1.4.1", "delay": "^4.1.0", "promise.prototype.finally": "^3.1.0", "tsd": "^0.7.1", "xo": "^0.24.0"}, "gitHead": "5bcb7bcf966fc416755b50c24c4d7eb2fbf44b0e", "bugs": {"url": "https://github.com/sindresorhus/p-cancelable/issues"}, "homepage": "https://github.com/sindresorhus/p-cancelable#readme", "_id": "p-cancelable@2.0.0", "_nodeVersion": "8.15.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-wvPXDmbMmu2ksjkB4Z3nZWTSkJEb9lqVdMaCKpZUGJG9TMiNp9XcbG3fn9fPKjem04fJMJnXoyFPk2FmgiaiNg==", "shasum": "4a3740f5bdaf5ed5d7c3e34882c6fb5d6b266a6e", "tarball": "https://registry.npmjs.org/p-cancelable/-/p-cancelable-2.0.0.tgz", "fileCount": 5, "unpackedSize": 12795, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcoRy4CRA9TVsSAnZWagAA1KYP/iZN1N2lQelTHAi8CMUK\np4nnbIq/6g/2rJddpmptDgiPRdV24nqonvc+6NRC7oFRzFfdyWqwh6PYyHDo\nTckiL/+1mVtwh6g61dyX6X3ddOIwfUSjxMdCAsdw4lFUknH9oANotcwuYEhH\ni/h+jiNQvXA2FjpeFy7uNAbx5nMCn5RAGVxXuZlnh+5zA/0RoAzKD2A1UbPm\nFHYM0eVyjhggG0dWO12doO2WjrIVAZHJ3Q/pYr6s5e02/ZUXeihaBlzHyWNJ\ngqQpyiDRwF7FpHLx6h6Hn1zZu45Udq9FyAC+Mzx0syd5iLTH/bdQrSJwmnkz\ntY9ygsgWZ9WhsO0CPe7A6Nb71ZT33BeqMsRswxw8lThB98yM7bqTxWEqEBq0\nUtnBK9DxTDW5a1mBdyMwzpe2mxcaLwjsSFZOKLIDOQqDWMN94yH/JgU0o2Nb\n9wYeBFqPYkCu2Xm3uhNdaaC3EVGy5USc5XbyXjNDfkeueYfyNxpaudWITcQM\ng6c6iRLtkbtWSVDA0hTGxGMwnLEaORa1EL7wmyg4tZwcafOyHF6HY2yaloI4\nPPTCJagqbqPPiX8Ze5frad0F+s+wQRFX32YrWWl80G83QGphwBBwF9sCMrKw\nQGymS1nBIo23PtJkKX3gtLetw390gBBtiicJ6AAZvnMG/EAeFMgluuxiCa6o\noOC3\r\n=Gugh\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD39IjlSz1NUrEVC6iSwLBHvjmAOQIX/MJUqSt/LU422AIgNI0qKxq2AF+GknVyJVjCDFD1g1QCHRWOyfA2strUSw4="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/p-cancelable_2.0.0_1554062519912_0.7909646907529038"}, "_hasShrinkwrap": false}, "2.1.0": {"name": "p-cancelable", "version": "2.1.0", "description": "Create a promise that can be canceled", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/p-cancelable.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["promise", "cancelable", "cancel", "canceled", "canceling", "cancellable", "cancellation", "abort", "abortable", "aborting", "cleanup", "task", "token", "async", "function", "await", "promises", "bluebird"], "devDependencies": {"ava": "^1.4.1", "delay": "^4.1.0", "promise.prototype.finally": "^3.1.0", "tsd": "^0.7.1", "xo": "^0.24.0"}, "gitHead": "78639762f7c3ea26efb182ad0b65c4a8eacb5822", "bugs": {"url": "https://github.com/sindresorhus/p-cancelable/issues"}, "homepage": "https://github.com/sindresorhus/p-cancelable#readme", "_id": "p-cancelable@2.1.0", "_nodeVersion": "14.16.0", "_npmVersion": "6.14.10", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-HAZyB3ZodPo+BDpb4/Iu7Jv4P6cSazBz9ZM0ChhEXp70scx834aWCEjQRwgt41UzzejUAPdbqqONfRWTPYrPAQ==", "shasum": "4d51c3b91f483d02a0d300765321fca393d758dd", "tarball": "https://registry.npmjs.org/p-cancelable/-/p-cancelable-2.1.0.tgz", "fileCount": 5, "unpackedSize": 13538, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgSG3LCRA9TVsSAnZWagAAOeIP+wXAF3sNP9Wi83u0alkM\niPXc2j8hH3I64aWNBixebRS3Sok7RTPbOoThf9DEl1YmwjvtmvloGNskGkVV\nXnOE+BSsIIyXhYRIeutmLRMMyACZOncbRDNqFmmNGE1xnVeKxqoTfBa/W2HD\nBk13HChNzNJ7k+MespskUbMeCibTrUjU3+rocJUQkkdzkVSTDXvPqXQh+X5o\nHz3yDTwTKbyel/rZyoENVYSz1ammllzdSqQ1SXuf43hUS2S51sBxDL4asyIw\nZLtzfb2TZZ4f0/9XXrjvhkhz+VjNurN3BaXIXgZR5r7mWyoBFGXpJLY0XNOv\nIVDD81XK8fSWLjzPn8MGaqXOL73A/FrRH3Sg6PxP7wZa18D6FWx9csC678dO\nmJ+FWb6f8ni4tDehTrDUxOc9IcHqgNHDylWBaTLDe/BwPgvWu6L2mEGqKO3r\nKbFE4ybP2ZoEGIChnYNMN62Q7dzfUXNVRb/5ubJmqhzlqg7+5Po+NXJL42ju\n8LqtJRbpfu5MhvqT+f3NvK5DYeEv1Bd40gpRxLZ+K2NAFrBUxqNznDZ+ZhFG\n66B98NcuSs2B5GpqJLGk12vyZl3s+u3haT7VioCnv4qrnlvZuvqY1D7URF4s\nsVtkaS58uHz0XHLru6ReMbuG9mOwV9ouaMv7WwSCsQQlaP/+DL8nV7aAa//Z\n4BcS\r\n=bLeM\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEaBPXQqyOn78k3M4HfoVRAjvfKOl/I/tYiRtoFYoXZNAiEAoR7mkSqHbbrFhwL5Q8noPwZisLC161NIWxehupMK3uQ="}]}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/p-cancelable_2.1.0_1615359434883_0.2511422226617046"}, "_hasShrinkwrap": false}, "2.1.1": {"name": "p-cancelable", "version": "2.1.1", "description": "Create a promise that can be canceled", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/p-cancelable.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["promise", "cancelable", "cancel", "canceled", "canceling", "cancellable", "cancellation", "abort", "abortable", "aborting", "cleanup", "task", "token", "async", "function", "await", "promises", "bluebird"], "devDependencies": {"ava": "^1.4.1", "delay": "^4.1.0", "promise.prototype.finally": "^3.1.0", "tsd": "^0.7.1", "xo": "^0.24.0"}, "gitHead": "ab7fb34ab7569eb5822548ac6766e2469a48b98d", "bugs": {"url": "https://github.com/sindresorhus/p-cancelable/issues"}, "homepage": "https://github.com/sindresorhus/p-cancelable#readme", "_id": "p-cancelable@2.1.1", "_nodeVersion": "16.0.0", "_npmVersion": "7.10.0", "dist": {"integrity": "sha512-BZOr3nRQHOntUjTrH8+Lh54smKHoHyur8We1V8DSMVrl5A2malOOwuJRnKRDjSnkoeBh4at6BwEnb5I7Jl31wg==", "shasum": "aab7fbd416582fa32a3db49859c122487c5ed2cf", "tarball": "https://registry.npmjs.org/p-cancelable/-/p-cancelable-2.1.1.tgz", "fileCount": 5, "unpackedSize": 13542, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgjn0YCRA9TVsSAnZWagAAtSYP/RuvZr8CfcH1OzUNJajz\n/JN69Czw2MZyOPK5VtVqwcjHoos58d9C6S43aA42jUNH6qWFtLeggPi/VOIx\n8ryms6hYpHujuNYeQs++HqBLH0X79RlteIDMMbXhNy2oxuDYqsiUga77m6fc\nzRmw5SQhtbr2uRafJ4uKBr4n+B1d9WBsqQjs4IG1WxAXOlTwJ21J3kN2V3Bx\nEwgm67xORR9VeHd/1pgo3k9nVKkwQxrftku8YB4Bu9BGqXyWLldR0mi9y703\n/sg9KSGr4voAeycXlaNJ2vfNMkIz/fmSCQTrYp14aZ5srnrYWptlFrnFjM8x\niseYdrj6VliCvdrmxDb5SujOnuNvCsurD6YVluyL3LqGmCR9tOeLZEZCNn+L\nW6Dg2e0O7JlUe2QJXSU137mNUpzM6JIIE37U8T9/zKUe1wLPI0a8Egeoktzi\nvDfqtw3Xgq2G15KN8m2BwX/pJcfPSYvWPoUZMdt6b4UXWLEwk7rrJkRFV8yQ\nwIZtOodu+KoYLpESyUNfWWuRgaro60Ksw7AcQtTtNfS0O4LKtxIVq12W+57R\nUm7eQPbhkMOiVoX+cLKCcxXI4iRq5IvOqQCGqW98k82WyORo+atjc0Ek3ozj\nvMm49eztVR3i7W0ucQC4NKwlYRFZ+TxG2a++BTFjDlFQSp7HS+jTRx6YkliF\nHSJi\r\n=4Xvh\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCUvUzMSd0NVTP8QmUBQI1wjyHu1+U2jWDyMu00grFTXQIgVtFx/e9PQkm+qy3vHMycSb7/WJH2RDPYi+jG0T8DFbw="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/p-cancelable_2.1.1_1619950872444_0.7737593099583226"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "p-cancelable", "version": "3.0.0", "description": "Create a promise that can be canceled", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/p-cancelable.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12.20"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["promise", "cancelable", "cancel", "canceled", "canceling", "cancellable", "cancellation", "abort", "abortable", "aborting", "cleanup", "task", "token", "async", "function", "await", "promises", "bluebird"], "devDependencies": {"ava": "^3.15.0", "delay": "^5.0.0", "tsd": "^0.16.0", "xo": "^0.40.1"}, "gitHead": "30edb363b20c380067364a9f4ebc43e3129c07d9", "bugs": {"url": "https://github.com/sindresorhus/p-cancelable/issues"}, "homepage": "https://github.com/sindresorhus/p-cancelable#readme", "_id": "p-cancelable@3.0.0", "_nodeVersion": "12.22.1", "_npmVersion": "7.10.0", "dist": {"integrity": "sha512-mlVgR3PGuzlo0MmTdk4cXqXWlwQDLnONTAg6sm62XkMJEiRxN3GL3SffkYvqwonbkJBcrI7Uvv5Zh9yjvn2iUw==", "shasum": "63826694b54d61ca1c20ebcb6d3ecf5e14cd8050", "tarball": "https://registry.npmjs.org/p-cancelable/-/p-cancelable-3.0.0.tgz", "fileCount": 5, "unpackedSize": 13367, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgtOR4CRA9TVsSAnZWagAAkZgP+weNqHxdHQJQcj0Vhdi6\n/6I1E6XSsq8y5qpSJ2YB6r+E819lBPChfa4SEVfMGQGl4C7+rrURNj/Q9QtO\nk0Uz0R+7ZXkkgSzRRRjH/1r+HEmoXaPTX0C74aqkprFmmB+SzDPwQwxFRFZ4\nTu1GwpJ4itKF8B3bD00Vl54VBQkLxzyyE5BqFxSAevrs25GMQEJcPpEWJ+YF\ni7zek72Z+Y09phMwr9gBrvCt3XHW8J322daQvG+64xCOUhz8YL4q4N2V4LkP\nFeNUrS8FHPUhEUQHFTyYbuE9z5APjzNOgRSHk+D68cZ3EtKLxwy6PJQ55+lz\n2DJNJTVY4Kv/xnz3yHLUhZSEkYyerm22LBnSiuLQPXa2jOVG71XD6aFgs0Fk\n01wuRbHy1TN6bzulfWZHNXvKm++TivXnZyXqa7uU+tzY+FYRt38z0leTtTKm\nn1eKw+RbKbi+nywe7xA01dXGpiVS1eERdhgkxmNdbF4FoIbBO62PUdrNqv8e\nWYh5SBL8uy7H/Z837svXY2vxTnt4HD/rSOCxgkkcLx0REoMTqAamHUOsn1OS\n664TM7rfiDeKt0E3qFDHqjhmEW3ZO93r3giYzFwHxz1hNHRHwrdaqS2CIwF9\nBUGNwLwcC//40FcGq/NGB6F8W3AtYnGnWPxYxw6pS+L6j8qDE2HUw1L82zPr\nZRY3\r\n=Qpn1\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBy69M/OtLIrYtS6uXTckoHbVquVCVan5Yj8q9Zl0BKrAiAdRoiUzPCUp0JZVAmOt0/67qtPpigFXuPwJRGKRfP3ww=="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/p-cancelable_3.0.0_1622467703540_0.5798264943193931"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "p-cancelable", "version": "4.0.0", "description": "Create a promise that can be canceled", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/p-cancelable.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=14.16"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["promise", "cancelable", "cancel", "canceled", "canceling", "cancellable", "cancellation", "abort", "abortable", "aborting", "cleanup", "task", "token", "async", "function", "await", "promises", "bluebird"], "devDependencies": {"ava": "^4.2.0", "delay": "^5.0.0", "tsd": "^0.20.0", "xo": "^0.48.0"}, "types": "./index.d.ts", "gitHead": "f1adca0772aacc5626a82e2c5bce20536248bf23", "bugs": {"url": "https://github.com/sindresorhus/p-cancelable/issues"}, "homepage": "https://github.com/sindresorhus/p-cancelable#readme", "_id": "p-cancelable@4.0.0", "_nodeVersion": "14.19.1", "_npmVersion": "8.3.2", "dist": {"integrity": "sha512-Zdd8zN5qpMjMuKIHdiMTXWAyCtR5TQ3CrdHDqj2gm+u5OP1WxwFAqiYDgnaeBX8PgQlLxECYPjRFvnKFJHk5KA==", "shasum": "2bf05af7052dbf1c64d5f763a8cedc54b5095b9c", "tarball": "https://registry.npmjs.org/p-cancelable/-/p-cancelable-4.0.0.tgz", "fileCount": 5, "unpackedSize": 13520, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD11niqk2NaGHSo1CkBAjr5zrx/IUkV3avM/xDwllzdOAIhAMp5nG/O+umt12hAgvtmTSgaYUcfHfPeAXFHTB1ypXSe"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZWb4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqzNQ/+Na6apDXzCNUCdjXaQqdq9m9SJMVoAcGqwlIzrKvyWkUrqR3d\r\nWoLmaItE82tSYqRCdPk3hOShez/lUllDxfxKMbFmLoK1l1zTz+M5bFQ1UJOX\r\n9Rw0AYc+k1TU0wKNYfJTiCrY5NsHigT6Rh9uzzVyity6MoSl097ybqyYKsFM\r\nPXUfSJAC8e5vObUIzpGbEuLYglGJBAmrrN2+vjPLJipHubNbWi8Szg6TYpSv\r\nmZQxVCWPpNfyxadq3rQBn96JT2KtA36xamTTD8X171RjTyVRdsAiHuYrFlUv\r\nNHFl7UJUC7yOA5vmhv/wbXz673QrUpiXykGII1YYmAMuGF9tkeyKcpfTTDYb\r\nwbqkdSP8O4LfBrgqw2YJXACOpBdq4fL9OHn7EYywd7IwCdcGWx6uYfM6SytM\r\nwPAXbqYvRad5Ak+PoGoZXT6Xj0YKTCemvcpi7JZDp2btOW1Cwh0c0AW8bwCR\r\n42SYrAmVd6URPPX8vMnacZ/10QCyDlxih7iZuGXdKiJ89S1ZXcBBceByIRKz\r\nmHvFkVPbMClQVy6azPRCz/DrO35/wSflWI0v7VoHeCAYg1h0jdiRtEzFRLRE\r\nGcqa8LKgU+yy3uGOyEZj07mh4OogEoyyq9hDekM42QppNxi1UZBKKkLpfWI8\r\n7GyUNsg26dQvDG5m3m/V+ybD3AuGO2Y+4Sk=\r\n=nPj3\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/p-cancelable_4.0.0_1650812664110_0.33194743760523004"}, "_hasShrinkwrap": false}, "4.0.1": {"name": "p-cancelable", "version": "4.0.1", "description": "Create a promise that can be canceled", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/p-cancelable.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=14.16"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["promise", "cancelable", "cancel", "canceled", "canceling", "cancellable", "cancellation", "abort", "abortable", "aborting", "cleanup", "task", "token", "async", "function", "await", "promises", "bluebird"], "devDependencies": {"ava": "^4.2.0", "delay": "^5.0.0", "tsd": "^0.20.0", "xo": "^0.48.0"}, "types": "./index.d.ts", "gitHead": "5db52128b57b5d1e11b4df860b5d4257475d4071", "bugs": {"url": "https://github.com/sindresorhus/p-cancelable/issues"}, "homepage": "https://github.com/sindresorhus/p-cancelable#readme", "_id": "p-cancelable@4.0.1", "_nodeVersion": "16.15.0", "_npmVersion": "8.3.2", "dist": {"integrity": "sha512-wBowNApzd45EIKdO1LaU+LrMBwAcjfPaYtVzV3lmfM3gf8Z4CHZsiIqlM8TZZ8okYvh5A1cP6gTfCRQtwUpaUg==", "shasum": "2d1edf1ab8616b72c73db41c4bc9ecdd10af640e", "tarball": "https://registry.npmjs.org/p-cancelable/-/p-cancelable-4.0.1.tgz", "fileCount": 5, "unpackedSize": 13624, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDj0LIOq+KTwKzJYfrp+Bkne8vtn3hu4frpIBwWy+j30AiEA4X7+GF2whNjyCliSvkXfo+Kcm+7TsPS7cusr5WOfcgg="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1rm1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpiUw//T6hSH5gNc/ZwA9ko4hSz+YddQHx4u152+QJNGR5nVeCyD4CG\r\nboi/oOwyBX273/xwJR7R+2LIQlFExC45SbO1T01VvWLTn8KURWiuw4VSbTxF\r\nsxPKgY19iXNSxxZ7DeVRTfVw0Pr5ntpWxJnJoBqT8fVEHJxpnmCsFefVnEWR\r\ndy3b/366sZmc3YqsvADvhfIb4pdQltlkU89IBQB7Uf5C6fUqPVB0CaP5g+cE\r\nYgcXNlEdiY4pRDIFLso+B6wLFuQmfiYuAwoXDz2z8AYeNgEy7SRN1YVaalI5\r\nN14cqgRbe8nA5AGFzM6d+GhrGUsw95XQQwY0pIM3crKNCGKRgInZM1o/Jlv4\r\ncQ+Jyy6ihtjrCh8XTz6cC3KztH085/n2fvRjd888DzolpH7epkFDX6p0AHCQ\r\nt6tYQ06tD+EzMLWFfTP4Yc5uTnqK0PyODy7fIVMXlUrEPK/QSyIXVbIvSFl9\r\nRv0546uHncGKMooeJvG3PUcwr51rRsI64F34OcoxH6fZjuCH5T/7Mcgl89Il\r\n9OG69sz4anKmTYpxSt1y/UOrA574hbVil90KdKh1r50VNS6C0N6AaWhI7/h0\r\nQPkd+n7C/XdTQmBdL8MZxCUCztDnQA0+T39j8S52rCY5VIwaBEZRBGXakg6I\r\nbekiTsYD4MEid9MeccOHN2dSoiOmTEx7ndY=\r\n=mw3H\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/p-cancelable_4.0.1_1658239413718_0.37320410047508945"}, "_hasShrinkwrap": false}}, "readme": "# p-cancelable\n\n> Create a promise that can be canceled\n\nUseful for animation, loading resources, long-running async computations, async iteration, etc.\n\n*If you target [Node.js 16](https://medium.com/@nodejs/node-js-v15-0-0-is-here-deb00750f278) or later, this package is [less useful](https://github.com/sindresorhus/p-cancelable/issues/27) and you should probably use [`AbortController`](https://developer.mozilla.org/en-US/docs/Web/API/AbortController) instead.*\n\n## Install\n\n```sh\nnpm install p-cancelable\n```\n\n## Usage\n\n```js\nimport PCancelable from 'p-cancelable';\n\nconst cancelablePromise = new PCancelable((resolve, reject, onCancel) => {\n\tconst worker = new SomeLongRunningOperation();\n\n\tonCancel(() => {\n\t\tworker.close();\n\t});\n\n\tworker.on('finish', resolve);\n\tworker.on('error', reject);\n});\n\n// Cancel the operation after 10 seconds\nsetTimeout(() => {\n\tcancelablePromise.cancel('Unicorn has changed its color');\n}, 10000);\n\ntry {\n\tconsole.log('Operation finished successfully:', await cancelablePromise);\n} catch (error) {\n\tif (cancelablePromise.isCanceled) {\n\t\t// Handle the cancelation here\n\t\tconsole.log('Operation was canceled');\n\t\treturn;\n\t}\n\n\tthrow error;\n}\n```\n\n## API\n\n### new PCancelable(executor)\n\nSame as the [`Promise` constructor](https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Global_Objects/Promise), but with an appended `onCancel` parameter in `executor`.\n\nCancelling will reject the promise with `CancelError`. To avoid that, set `onCancel.shouldReject` to `false`.\n\n```js\nimport PCancelable from 'p-cancelable';\n\nconst cancelablePromise = new PCancelable((resolve, reject, onCancel) => {\n\tconst job = new Job();\n\n\tonCancel.shouldReject = false;\n\tonCancel(() => {\n\t\tjob.stop();\n\t});\n\n\tjob.on('finish', resolve);\n});\n\ncancelablePromise.cancel(); // Doesn't throw an error\n```\n\n`PCancelable` is a subclass of `Promise`.\n\n#### onCanceled(fn)\n\nType: `Function`\n\nAccepts a function that is called when the promise is canceled.\n\nYou're not required to call this function. You can call this function multiple times to add multiple cancel handlers.\n\n### PCancelable#cancel(reason?)\n\nType: `Function`\n\nCancel the promise and optionally provide a reason.\n\nThe cancellation is synchronous. Calling it after the promise has settled or multiple times does nothing.\n\n### PCancelable#isCanceled\n\nType: `boolean`\n\nWhether the promise is canceled.\n\n### PCancelable.fn(fn)\n\nConvenience method to make your promise-returning or async function cancelable.\n\nThe function you specify will have `onCancel` appended to its parameters.\n\n```js\nimport PCancelable from 'p-cancelable';\n\nconst fn = PCancelable.fn((input, onCancel) => {\n\tconst job = new Job();\n\n\tonCancel(() => {\n\t\tjob.cleanup();\n\t});\n\n\treturn job.start(); //=> Promise\n});\n\nconst cancelablePromise = fn('input'); //=> PCancelable\n\n// …\n\ncancelablePromise.cancel();\n```\n\n### CancelError\n\nType: `Error`\n\nRejection reason when `.cancel()` is called.\n\nIt includes a `.isCanceled` property for convenience.\n\n## FAQ\n\n### Cancelable vs. Cancellable\n\n[In American English, the verb cancel is usually inflected canceled and canceling—with one l.](http://grammarist.com/spelling/cancel/) Both a [browser API](https://developer.mozilla.org/en-US/docs/Web/API/Event/cancelable) and the [Cancelable Promises proposal](https://github.com/tc39/proposal-cancelable-promises) use this spelling.\n\n### What about the official [Cancelable Promises proposal](https://github.com/tc39/proposal-cancelable-promises)?\n\n~~It's still an early draft and I don't really like its current direction. It complicates everything and will require deep changes in the ecosystem to adapt to it. And the way you have to use cancel tokens is verbose and convoluted. I much prefer the more pragmatic and less invasive approach in this module.~~ The proposal was withdrawn.\n\n## p-cancelable for enterprise\n\nAvailable as part of the Tidelift Subscription.\n\nThe maintainers of p-cancelable and thousands of other packages are working with Tidelift to deliver commercial support and maintenance for the open source dependencies you use to build your applications. Save time, reduce risk, and improve code health, while paying the maintainers of the exact dependencies you use. [Learn more.](https://tidelift.com/subscription/pkg/npm-p-cancelable?utm_source=npm-p-cancelable&utm_medium=referral&utm_campaign=enterprise&utm_term=repo)\n\n## Related\n\n- [p-progress](https://github.com/sindresorhus/p-progress) - Create a promise that reports progress\n- [p-lazy](https://github.com/sindresorhus/p-lazy) - Create a lazy promise that defers execution until `.then()` or `.catch()` is called\n- [More…](https://github.com/sindresorhus/promise-fun)\n", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2022-07-19T14:03:33.995Z", "created": "2016-11-28T10:56:08.946Z", "0.1.0": "2016-11-28T10:56:08.946Z", "0.2.0": "2017-05-05T05:39:17.492Z", "0.3.0": "2017-06-30T15:48:55.134Z", "0.4.0": "2018-03-06T15:01:51.923Z", "0.4.1": "2018-04-01T11:22:59.190Z", "0.5.0": "2018-07-04T06:21:35.707Z", "0.5.1": "2018-09-10T16:32:13.688Z", "1.0.0": "2018-10-22T07:29:51.952Z", "1.1.0": "2019-03-08T08:43:07.840Z", "2.0.0": "2019-03-31T20:02:00.172Z", "2.1.0": "2021-03-10T06:57:15.048Z", "2.1.1": "2021-05-02T10:21:12.564Z", "3.0.0": "2021-05-31T13:28:23.690Z", "4.0.0": "2022-04-24T15:04:24.293Z", "4.0.1": "2022-07-19T14:03:33.922Z"}, "homepage": "https://github.com/sindresorhus/p-cancelable#readme", "keywords": ["promise", "cancelable", "cancel", "canceled", "canceling", "cancellable", "cancellation", "abort", "abortable", "aborting", "cleanup", "task", "token", "async", "function", "await", "promises", "bluebird"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/p-cancelable.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/p-cancelable/issues"}, "license": "MIT", "readmeFilename": "readme.md", "users": {"rocket0191": true, "drewigg": true}}