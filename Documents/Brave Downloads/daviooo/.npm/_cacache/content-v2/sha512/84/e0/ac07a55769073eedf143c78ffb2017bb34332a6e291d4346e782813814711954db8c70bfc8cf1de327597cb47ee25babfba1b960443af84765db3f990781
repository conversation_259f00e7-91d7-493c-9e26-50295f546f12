{"_id": "tr46", "_rev": "22-bc8a3dcc8074e4f386e3070d3593e015", "name": "tr46", "dist-tags": {"latest": "5.1.0"}, "versions": {"0.0.1": {"name": "tr46", "version": "0.0.1", "keywords": ["unicode", "tr46", "url", "whatwg"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tr46@0.0.1", "maintainers": [{"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/Sebmaster/tr46.js#readme", "bugs": {"url": "https://github.com/Sebmaster/tr46.js/issues"}, "dist": {"shasum": "b09da8c4b972bc43ba0cd750292358167a2b1dae", "tarball": "https://registry.npmjs.org/tr46/-/tr46-0.0.1.tgz", "integrity": "sha512-Jxc8qgb0Ulw7BZWke/eoMIAhRpo5dSfEM/DvDU1CWcr6jkwOWJY+eU7qrLmjGpmTSBGY2e0N+Al4rfwVynoAtg==", "signatures": [{"sig": "MEUCIGOnDcrir6U1p6HhAZhMM/acDa1q4QJS5OABd4iuf4jsAiEA4NDtW3BAe6vqip2anhr+5n90WiJOGxSnI1IAGcbkRLY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "b09da8c4b972bc43ba0cd750292358167a2b1dae", "gitHead": "b22c3e63e038c82b9e96aa1c274162cdec52e002", "scripts": {"test": "mocha", "prepublish": "node scripts/generate_mapping_table.js"}, "_npmUser": {"name": "sebmaster", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Sebmaster/tr46.js.git", "type": "git"}, "_npmVersion": "2.11.0", "description": "An implementation of the Unicode TR46 spec", "directories": {}, "_nodeVersion": "2.2.1", "devDependencies": {"mocha": "^2.2.5", "request": "^2.57.0"}}, "0.0.2": {"name": "tr46", "version": "0.0.2", "keywords": ["unicode", "tr46", "url", "whatwg"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tr46@0.0.2", "maintainers": [{"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/Sebmaster/tr46.js#readme", "bugs": {"url": "https://github.com/Sebmaster/tr46.js/issues"}, "dist": {"shasum": "847ca51535dcb1c9bd5df07a1e0ee7c3ed3f1479", "tarball": "https://registry.npmjs.org/tr46/-/tr46-0.0.2.tgz", "integrity": "sha512-Hifn/q4SuHkr7CP28KLnSXCEVQN27uGvP+jI4ZbKy2W4Hnx0sZJMmghzf3nADGoziqKRxqhdZHfTti0PtdeIQQ==", "signatures": [{"sig": "MEUCIGhgbUwaBbAIDCf9+f4TFmDb4K0iN03Q0ee9oA0hSu+0AiEAy42uptmMmCqwrSn9/1Dc5RT59lqXBIH6WeX1LZ1hvN8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "847ca51535dcb1c9bd5df07a1e0ee7c3ed3f1479", "gitHead": "fe029c592abe09154512f0d0ddb90d8d0e24dd87", "scripts": {"test": "mocha", "pretest": "node scripts/getLatestUnicodeTests.js", "prepublish": "node scripts/generateMappingTable.js"}, "_npmUser": {"name": "sebmaster", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Sebmaster/tr46.js.git", "type": "git"}, "_npmVersion": "2.11.0", "description": "An implementation of the Unicode TR46 spec", "directories": {}, "_nodeVersion": "2.2.1", "devDependencies": {"mocha": "^2.2.5", "request": "^2.57.0"}}, "0.0.3": {"name": "tr46", "version": "0.0.3", "keywords": ["unicode", "tr46", "url", "whatwg"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tr46@0.0.3", "maintainers": [{"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/Sebmaster/tr46.js#readme", "bugs": {"url": "https://github.com/Sebmaster/tr46.js/issues"}, "dist": {"shasum": "8184fd347dac9cdc185992f3a6622e14b9d9ab6a", "tarball": "https://registry.npmjs.org/tr46/-/tr46-0.0.3.tgz", "integrity": "sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==", "signatures": [{"sig": "MEYCIQCUrUj2itfC5h6uLF7nT69DcaiOyeOICexYW6ev9JGRQAIhAN/aPMT9PK0tD0xWc3TobFmgoP+XPw2e49OIWLB8ZjHB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "8184fd347dac9cdc185992f3a6622e14b9d9ab6a", "gitHead": "a8009f9ce80ff5dbe71dd71e203afe4e4c878d28", "scripts": {"test": "mocha", "pretest": "node scripts/getLatestUnicodeTests.js", "prepublish": "node scripts/generateMappingTable.js"}, "_npmUser": {"name": "sebmaster", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/Sebmaster/tr46.js.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "An implementation of the Unicode TR46 spec", "directories": {}, "_nodeVersion": "5.4.1", "devDependencies": {"mocha": "^2.2.5", "request": "^2.57.0"}}, "1.0.0": {"name": "tr46", "version": "1.0.0", "keywords": ["unicode", "tr46", "url", "whatwg"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tr46@1.0.0", "maintainers": [{"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/sebmaster/tr46.js#readme", "bugs": {"url": "https://github.com/sebmaster/tr46.js/issues"}, "dist": {"shasum": "78aa45b58a8f60382fcc66598d1499e38ac1b953", "tarball": "https://registry.npmjs.org/tr46/-/tr46-1.0.0.tgz", "integrity": "sha512-Xb<PERSON>uqZaeYPi6jb3dRTRLU1P1Y2MYcQauF/ZOYRsq5g7X6v4/CRVRpGuiOMULaFsVe6AdFDnENCQ5x/Mzo103rQ==", "signatures": [{"sig": "MEYCIQDRIZM6QItlwYKD7bbTlEscMS2r8bQ5XzovEFb+k5IaDAIhAOOuiD509dvtgSV4+FR14v1lHGBBh8SpXEpJcVhj1mXF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib/mappingTable.json", "lib/regexes.js"], "_shasum": "78aa45b58a8f60382fcc66598d1499e38ac1b953", "gitHead": "fe7e40f3464ff14b9da11c1d64e1470b60fed043", "scripts": {"lint": "eslint .", "test": "mocha", "pretest": "node scripts/getLatestUnicodeTests.js", "prepublish": "node scripts/generateMappingTable.js && node scripts/generateRegexes.js"}, "_npmUser": {"name": "sebmaster", "email": "<EMAIL>"}, "deprecated": "1.0.0 has some bugs with host name validation, mainly wrt BiDi rules. Please wait for v1.0.1.", "repository": {"url": "git+https://github.com/sebmaster/tr46.js.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "An implementation of the Unicode TR46 spec", "directories": {}, "_nodeVersion": "7.9.0", "dependencies": {"punycode": "^2.1.0"}, "devDependencies": {"mocha": "^3.2.0", "eslint": "^3.13.0", "request": "^2.79.0", "regenerate": "^1.3.2", "unicode-10.0.0": "^0.7.4"}, "_npmOperationalInternal": {"tmp": "tmp/tr46-1.0.0.tgz_1501195911430_0.7034000705461949", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "tr46", "version": "1.0.1", "keywords": ["unicode", "tr46", "url", "whatwg"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tr46@1.0.1", "maintainers": [{"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/sebmaster/tr46.js#readme", "bugs": {"url": "https://github.com/sebmaster/tr46.js/issues"}, "dist": {"shasum": "a8b13fd6bfd2489519674ccde55ba3693b706d09", "tarball": "https://registry.npmjs.org/tr46/-/tr46-1.0.1.tgz", "integrity": "sha512-dTpowEjclQ7Kgx5SdBkqRzVhERQXov8/l9Ft9dVM9fmg0W0KQSVaXX9T4i6twCPNtYiZM53lpSSUAwJbFPOHxA==", "signatures": [{"sig": "MEUCIGiAK7YkrBBNtYN2842NTEUAUP0mFpfQw9rubF1YK9pTAiEA5scC+rEItjpZwbmPX1urrnHKdgJkB7GfaSWBcu1uft0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib/mappingTable.json", "lib/regexes.js"], "_shasum": "a8b13fd6bfd2489519674ccde55ba3693b706d09", "gitHead": "89825520a434d1d750103e05b6fa19b1a75b7ab4", "scripts": {"lint": "eslint .", "test": "mocha", "pretest": "node scripts/getLatestTests.js", "prepublish": "node scripts/generateMappingTable.js && node scripts/generateRegexes.js"}, "_npmUser": {"name": "sebmaster", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sebmaster/tr46.js.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "An implementation of the Unicode TR46 spec", "directories": {}, "_nodeVersion": "7.7.2", "dependencies": {"punycode": "^2.1.0"}, "unicodeVersion": "10.0.0", "devDependencies": {"mocha": "^3.2.0", "eslint": "^3.13.0", "request": "^2.79.0", "regenerate": "^1.3.2", "unicode-10.0.0": "^0.7.4"}, "_npmOperationalInternal": {"tmp": "tmp/tr46-1.0.1.tgz_1504404535111_0.9779330701567233", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "tr46", "version": "2.0.0", "keywords": ["unicode", "tr46", "url", "whatwg"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tr46@2.0.0", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "a85da3f8511231357b347caa686abb3dfb150634", "tarball": "https://registry.npmjs.org/tr46/-/tr46-2.0.0.tgz", "fileCount": 8, "integrity": "sha512-LrErSqfhdUw73AC/eXV2fEmNkvgSYxfm5lvxnLvuVgoVDknvD28Pa5FeDGc8RuVouDxUD3GnHHFv7xnBp7As5w==", "signatures": [{"sig": "MEUCIQDGJHDN7SehtT7DOltf2SgjFE7jGOJlN5hi/IPxIup7pAIgMyGRCwNe1ie+i4OiB/ecvFF6F2DQAr+NFzeUMmxBbbY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 333050, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeDYgpCRA9TVsSAnZWagAAk1AQAJyk4IuStRGlBcKq1ilC\n0457ifrvItyGB8FxO3uksJKSIyknPlw14UBlJxXCuk0prfRHgSHnq0/am8Pw\nUHX2AypMTXyCivv52vxllSoAZtbHbntAG7K7QZiaVigA4/vWxtLfLlGFo5oj\nrSG3CBHE8pMRq4CZBXc1QCrNtAKaeK7MEI+EKCAb1Dt6hcRV3S458ILEyGje\n1gEsgmo1XM0HB5G77Gt+5eplyJBm1hz9PL/Dt9NYk7Wy8Dnd6p111ceo1An9\nPfn4uIQLceFzoZI6CZfn//4/bWsfsIGZ8NxtiDlY+r5zk/8Y1qplcGEV7q7H\n9gsLn4QCwMAaoXprdI/KjJ/1KfFwnuwgpN5MCl492OYqGt3xz7kBG/+mVUFU\n/QNrCb7ZDrDsGVSoQ1W21k51wKdFyGsPxTWNgIb7Q8W6KlGi5EJOOiQk+MMb\naPVJqjvYtZ1hjvYCQD77Uko2s9ReEBPaR8ML52QB1Jk/uObDeIpqREo4P0Of\nTVp09wPKQf/m8zDzuHuYmVunu3hUVJNjeVDBxO07CkueMM7LYjYvRyYxDhJk\nsBIJbSi8DD7eWTG8deLM2g+mryzEYbeeSqC1lf6rpwFPe5jeRf5JJMTFDKkK\nRi+0gw+xPLX5mfIDnip09la8q/avY+pac1cMjvbNXiI7ZK9ZEokwkzSreh35\nLVZo\r\n=Uin4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=8"}, "scripts": {"lint": "eslint .", "test": "mocha", "pretest": "node scripts/getLatestTests.js", "prepublish": "node scripts/generateMappingTable.js && node scripts/generateRegexes.js"}, "_npmUser": {"name": "timothy<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jsdom/tr46.js", "type": "git"}, "description": "An implementation of the Unicode TR46 spec", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2016 Sebastian Mayr\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"punycode": "^2.1.1"}, "_hasShrinkwrap": false, "unicodeVersion": "12.1.0", "devDependencies": {"mocha": "^6.2.2", "eslint": "^6.8.0", "request": "^2.88.0", "regenerate": "^1.4.0", "unicode-12.1.0": "^0.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/tr46_2.0.0_1577945129387_0.8387283270153352", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "tr46", "version": "2.0.1", "keywords": ["unicode", "tr46", "uts46", "punycode", "url", "whatwg"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tr46@2.0.1", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "ffc413a775a745866bc1c493ad3536c2b406c573", "tarball": "https://registry.npmjs.org/tr46/-/tr46-2.0.1.tgz", "fileCount": 8, "integrity": "sha512-WE2Oku6mSbwUP8601U7r5dW7KOJ4+WPpRufzsZPkkBNNwU004V6opMem0X7R0lSpdpAy0vjRPwPTEcFpgtmc+A==", "signatures": [{"sig": "MEQCIHUo2vzjgd0R74aP7O54FsQW/0w8hbA5FfoEOyIA9O3wAiAoXjoVsB1a/mWJUcYVjmCAICFj3EvoPy71Sxr7qPBXAQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 207369, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeTgtwCRA9TVsSAnZWagAAGeQP/30KJP/D3klDLD4XfhDL\n1YKhLMw4zroChMVKwTo9p3Aob1c3iKnsvUhSKgu9GHeq54kjpYnrKSNNRTUc\nguA6F1qUjmUDGyclbZvAax4eu98NqloklOy51G+dgwB+HvQJlYiCsIcyuiHh\nOWVUYoxFOODMDcORJg4rov4f/zOZREKSypjyWH3yoyisMKlSLh3nmZ6arAvt\nU/I5wDc0AA5xX64hwStRg7fbFH+VUCv8Z0SXqrcv4D3WF/WLjqVNaN9Y1CyJ\nFMVTwW333uMegT5MCE0EkC1YqKHdI7sB3/ElSSAgzSjOrRuBuGjOtlZdVmMu\nDz/LQwO4Qx2Y9zA9uSd7FpxgebLhAvacFrmnv6Z9zwkBWi2MmYcl+EAdOyuK\nsa+9KFIcV83YtfZJZRVuDAlcNUOZQoZfpikOjVk4RRFtZZG/a/4Vu2ZipZAT\n7Mf9LVwPsbW8rPVrpc0EafrukkrLVvTu9XtU5BmQSrEzVnYeweWgpM6DWfSe\nHPpANrw7uPkB5ZY4Z9O8KDHWYaHSuzkBKBddT28qxrCxQLJvMJQuL5yJIa17\nLntn7offPMJGNnMoIet46xY8J5R/0n1Etv7Qy3Ue3bFbtggo7qSGH4Bkac2u\nuDEbsrxHAvn4ued9T5pEEJ2jFCHPomf+ZmTmAEkYCmaemfM4gvlCg8LBnL0r\nVahg\r\n=Ompi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=8"}, "scripts": {"lint": "eslint .", "test": "mocha", "pretest": "node scripts/getLatestTests.js", "prepublish": "node scripts/generateMappingTable.js && node scripts/generateRegexes.js"}, "_npmUser": {"name": "timothy<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jsdom/tr46", "type": "git"}, "description": "An implementation of the Unicode UTS #46: Unicode IDNA Compatibility Processing", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2016 Sebastian Mayr\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"punycode": "^2.1.1"}, "_hasShrinkwrap": false, "unicodeVersion": "12.1.0", "devDependencies": {"pump": "^3.0.0", "mocha": "^6.2.2", "eslint": "^6.8.0", "node-fetch": "^2.6.0", "regenerate": "^1.4.0", "unicode-12.1.0": "^0.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/tr46_2.0.1_1582173039771_0.48082004913141674", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "tr46", "version": "2.0.2", "keywords": ["unicode", "tr46", "uts46", "punycode", "url", "whatwg"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tr46@2.0.2", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "03273586def1595ae08fedb38d7733cee91d2479", "tarball": "https://registry.npmjs.org/tr46/-/tr46-2.0.2.tgz", "fileCount": 9, "integrity": "sha512-3n1qG+/5kg+jrbTzwAykB5yRYtQCTqOGKq5U5PE3b0a1/mzo6snDhjGS0zJVJunO0NrT3Dg1MLy5TjWP/UJppg==", "signatures": [{"sig": "MEUCIQCUwkjwCL61XAc9fjM9SNTRnGQK2v86HtPrqwu6yy2TxgIgHUDKJ2W1ZV+4w/rg9CLT9XW7qBs2h20cq4WWFOq7SZY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 207643, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeTgvyCRA9TVsSAnZWagAALWYP/RBi2+8FUbXDlxO2Z0pP\nS+eC83fXUz6aR2dDvLxBfzHkv0R9MaQml/FI27SVzfc/fZSeDfM1nlirzyE6\nPyxHOuuEh5sx1BEXVKasa9OlQECU8YM79eTPSpo02pc3HtcFhcvN0Or+MKde\nsBqdaPIHORi2MJwmiU4pV5+faAGdR7837GQHvSEGQp7bk7NJbTU9iGuLxPdW\n2hXLjQc8aue9143krA5hy0n/uwOtBtrG4pRkPzrojTyc+XkCVjJymOKr3MFA\nhT32Qxgfz7uT4DDwvBX0liGjZK9Wyc6Irb2POzGh4CJ7Ldst1gsPgPjycSIR\nQeUp3tIhS3coHHLAD/0tDBKdmkFOqiFUbCcD34ckKFtegIUiEzvEuB6A1n8q\nUPuiFNHefk2Tf2w6Z8pRcDyi6fkqWZiq3ISTPEPbKHzTFFBUWq9FGB/Saagy\nijWM2HC+bpALUUr6fRwAw4MQ000xeACwx0MkCZ8aiEX0eeY0Mcqe3mMJKsTg\nzyQJp9pRoFJGTHs4XKK2Q5bt/8133eSH8nEMq3pdKYVWcYzWd+mDt00CJ8YT\nztRBnRZshE47uj3Q2Vu55GDP47mSuQ5ldtwzyMsWC5mO5LGCYFMqPQIP7hx1\nwn4z6WM6wQtDkogEJx+4NzgAPSCseezcXumcQKqhXmP1/3n4rhD3Ez4odDDu\nVFFh\r\n=/nKz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=8"}, "scripts": {"lint": "eslint .", "test": "mocha", "pretest": "node scripts/getLatestTests.js", "prepublish": "node scripts/generateMappingTable.js && node scripts/generateRegexes.js"}, "_npmUser": {"name": "timothy<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jsdom/tr46", "type": "git"}, "description": "An implementation of the Unicode UTS #46: Unicode IDNA Compatibility Processing", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2016 Sebastian Mayr\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"punycode": "^2.1.1"}, "_hasShrinkwrap": false, "unicodeVersion": "12.1.0", "devDependencies": {"pump": "^3.0.0", "mocha": "^6.2.2", "eslint": "^6.8.0", "node-fetch": "^2.6.0", "regenerate": "^1.4.0", "unicode-12.1.0": "^0.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/tr46_2.0.2_1582173169710_0.6400932710120866", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "tr46", "version": "2.1.0", "keywords": ["unicode", "tr46", "uts46", "punycode", "url", "whatwg"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tr46@2.1.0", "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "fa87aa81ca5d5941da8cbf1f9b749dc969a4e240", "tarball": "https://registry.npmjs.org/tr46/-/tr46-2.1.0.tgz", "fileCount": 9, "integrity": "sha512-15Ih7phfcdP5YxqiB+iDtLoaTz4Nd35+IiAv0kQ5FNKHzXgdWqPoTIqEDDJmXceQt4JZk6lVPT8lnDlPpGDppw==", "signatures": [{"sig": "MEUCIFJhTMI1HKWfsHe7GgWacwk1JrZoPCRibhxQ0T3VF+GfAiEApEFrRKSGzD6MZhM2nUPpOiZYnN6corlvByOQXmJQjog=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 210389, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgqNfMCRA9TVsSAnZWagAA7PYP/i9Fzqz64jDUtqpTNy7o\nrxTmgyhzf/os0mPd4zzGvTC+3zaB/7evULZ05sfFK+79L+V2E3GLY4uItByT\nZTUt2s6JLuRfz5SvpQhrBZwKeUeCGRdNihQvSqELBfEvv/TVEjyKOHx+w/O4\nZ47pJVZN/k5/PANeVVGEVjbOsPLPNTEa07zDoaP1adECE9MnhgaKj1u43bRW\nFYT1gYqb8ul627FZaio2CHrDHkAyfu2EspivgznqiIOwMfEO3hUoJvNS6JKk\nBj+u5574gC8BwkiYgliiIGjz2NdCENDwbRyQu+BEMjL+wFMaJx4/MvaCOtwX\ndKEkKP655yMoc26V6nQW965PeuEWBnwOcB+cPQ1Zm5C79A6aQU7qL9yH5CeT\nZTkBuiYK2pMRMAxxGDaUWb+HgfPDSY+LreX1GX0AhjZKu90ZOn4/1hsCpn5G\n36Bp6D/HjG2x/7JnVv/9ryVj2cvAWA0NZGNTN+3y25z1NCt8w636PDO0+wx2\nh1qu48sXQ/wFq6N20sbRgRxnQHo4693TdP7wtmC5l9xKXDbsLo6WSf2jO4nK\n4MWOFEhAPGjz9mS2W7tlsgbKy2PcGMfbzqXsbNAuHGJeHP+1aH1cTmBIXIWW\n5JzwueZ8rZZd5eeyRPYnjegd6CjY9ZOxOZEc+SkF6qTBNFAIHaQaT75TXabu\nvo1A\r\n=saI4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=8"}, "scripts": {"lint": "eslint .", "test": "mocha", "pretest": "node scripts/getLatestTests.js", "prepublish": "node scripts/generateMappingTable.js && node scripts/generateRegexes.js"}, "_npmUser": {"name": "timothy<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jsdom/tr46", "type": "git"}, "description": "An implementation of the Unicode UTS #46: Unicode IDNA Compatibility Processing", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2016 Sebastian Mayr\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"punycode": "^2.1.1"}, "_hasShrinkwrap": false, "unicodeVersion": "13.0.0", "devDependencies": {"mocha": "^8.4.0", "eslint": "^7.27.0", "node-fetch": "^2.6.0", "regenerate": "^1.4.2", "unicode-13.0.0": "^0.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/tr46_2.1.0_1621678028324_0.2069129795651361", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "tr46", "version": "3.0.0", "keywords": ["unicode", "tr46", "uts46", "punycode", "url", "whatwg"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tr46@3.0.0", "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/tr46#readme", "bugs": {"url": "https://github.com/jsdom/tr46/issues"}, "dist": {"shasum": "555c4e297a950617e8eeddef633c87d4d9d6cbf9", "tarball": "https://registry.npmjs.org/tr46/-/tr46-3.0.0.tgz", "fileCount": 7, "integrity": "sha512-l7FvfAHlcmulp8kr+flpQZmVwtu7nfRV7NZujtN0OqES8EL4O4e0qqzL0DC5gAvx/ZC/9lk6rhcUwYvkBnBnYA==", "signatures": [{"sig": "MEQCIFRK32xp1FNn2Ge2Obbww786y3Y4GjqKpINwuFGsAw0sAiAlWUu2ocpO90EKon+ggWcp6XzSoLNkiQT4hoEmYbbiuQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 215265, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2+J8CRA9TVsSAnZWagAAFVAP/2+xiOh7cHM6drQTLXIc\nRoTxu/AezimzcDI56UGVW/+T5HaHf/Aku787Mn657F+8mzPSLyyIqxsdRoWc\nHN+FOjtRNB27xPhicrPHDreLSKw1ABElLFjssR0tAtnsKLTJklpBoa7gBPn4\nbF8u60l1+Yvhs5hw/HVf0xI1qdiIqQOI9v+0ebc9en8mw3uiINhjxgf+G6Ty\nNaH4zs+A9zDkUU48cCaCJgKlU/P6pQc7O3LYIi1oPc2RyZUQcfzBtYvN1UO5\nkVVeZlDqCOLlcWFMCOk9EUVjl7KkGK2zV9Si0vTnbbhxjdVoYSUY8BszeW7i\nsHILvka7+9gjg+zI99TTI+va6TEX+EXmjFe3zwwieC4w5omzY8pxDjcgsd6Z\nqpEu48U/PjJOwHUhHhc2AeePQTI1RtxGVA9jBRahReBADn2C7Ekc1qdofnRM\n6Ogn0kuv1UPlJoVoArgtKZPACZ5q5qgdmonFzJe6/hiTPy6/AXXHC7L3mcQt\nz+jecXDn1ZRqyn3tcAcYxY26Iro/B/TNutBUv++zTAES4hjkjkaHE+Sv3i1/\nYwOsMDXd+naVKfU3+POVrY7ylPAyIX4eb+vy+9W8TEVCXeuKoH+WHUjjVQOx\ngmYxBM+VO9aBl/cs+z6cDWA+Vgg9+BHXXkgr9181YPWjoDQj9SBxaLT/juKr\neff0\r\n=DKln\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "e937be8d9c04b7938707fc3701e50118b7c023a5", "scripts": {"lint": "eslint .", "test": "mocha", "pretest": "node scripts/getLatestTests.js", "prepublish": "node scripts/generateMappingTable.js && node scripts/generateRegexes.js"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/tr46.git", "type": "git"}, "_npmVersion": "7.21.1", "description": "An implementation of the Unicode UTS #46: Unicode IDNA Compatibility Processing", "directories": {}, "_nodeVersion": "16.9.1", "dependencies": {"punycode": "^2.1.1"}, "_hasShrinkwrap": false, "unicodeVersion": "14.0.0", "devDependencies": {"mocha": "^9.1.1", "eslint": "^7.32.0", "regenerate": "^1.4.2", "minipass-fetch": "^1.4.1", "@domenic/eslint-config": "^1.4.0", "@unicode/unicode-14.0.0": "^1.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/tr46_3.0.0_1633302184390_0.5771107328600107", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "tr46", "version": "4.0.0", "keywords": ["unicode", "tr46", "uts46", "punycode", "url", "whatwg"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tr46@4.0.0", "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/tr46#readme", "bugs": {"url": "https://github.com/jsdom/tr46/issues"}, "dist": {"shasum": "0a89d333bafbd1d854d82fdf89e34f38d04d0918", "tarball": "https://registry.npmjs.org/tr46/-/tr46-4.0.0.tgz", "fileCount": 7, "integrity": "sha512-ypD+VaTN1WUKb+rvVGVCbhnFxAjlQWTHZXz3Q+sUOBXTuxWFv2ZF0guJav45fLdvMLFq5Le4Li9o0Bs56CNLcQ==", "signatures": [{"sig": "MEYCIQC1WIi7j5sgu1F+Zc7R58vl1lUN/aW7JKM/GTGMj/iIDwIhAJFKHA3/tYRZcGJzIvnFGPt3ajHLuAVamBhHIOjsB6y6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 218159, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjzfvYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo/GQ/9EL2CtWhoX4DCBmhWRxFTvTKCmT5/00bI/90ff6C55BEHtNTQ\r\n2dFtX4tYZNivQWNpMaPLqEyZnyLq8oHBHlYIHbe3oe1pB6Ss0SeHUliU1ljj\r\n4SFuQ41hBtW3ZBEJ9lAMtvHv+a8pXq9H41LwYi2MaiU19bTbWYumgJm3gojZ\r\n6POVshJSgvCndt0/DMZbj5Oq+VEueE5WLwfnqOcUrsWArWRTJXOb5pgFcedv\r\nnOtjSf1H+u10mlGVvtheDZjgxHHzwQ5m0SxkI4NBgDAq0C0dfNnGoeP4F9/L\r\n+gtNnXsiPLJ8BrTP9WiobWg5Inqc6L1bTVaGpSgGhyvBgBHNTfZ5mE1Ud/qi\r\ntEPgIu6E9td8Z9oaqqOFdpru4uGWc5L6g527i8Ch0iiqPMkIliUaZsl/8kUr\r\nzhkUEVBzY8UyRMe/CER+C18oHO2ttInFsA9WrZI76MrUyrSuE0cYjJeQZ0ng\r\nLcQ7IpInvcH8EtsQqduy18mHaEoYKrxmyreUVHS4NlQ6caoGk7sNQf5WFg8F\r\nZt+JovZLxKHF6ggftcokTHD/q83K4NN1C44lVeIUvPYTSx1d8uqmn9vt94OJ\r\nolKzIQlyc1epUh3AK0ulZQxuOUhEOq+4vC9LAlSosdBGuboOot+LtnLZL1QQ\r\nFoWaB7h3TfvoeLVAhnHvmiVEpF/uQhFnGaA=\r\n=ZqXb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=14"}, "gitHead": "cba76a80a52d775129001110ca8c3d22ed3e6245", "scripts": {"lint": "eslint .", "test": "mocha", "pretest": "node scripts/getLatestTests.js", "prepublish": "node scripts/generateMappingTable.js && node scripts/generateRegexes.js"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/tr46.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "An implementation of the Unicode UTS #46: Unicode IDNA Compatibility Processing", "directories": {}, "_nodeVersion": "19.2.0", "dependencies": {"punycode": "^2.3.0"}, "_hasShrinkwrap": false, "unicodeVersion": "15.0.0", "devDependencies": {"mocha": "^10.2.0", "eslint": "^8.32.0", "regenerate": "^1.4.2", "minipass-fetch": "^3.0.1", "@domenic/eslint-config": "^3.0.0", "@unicode/unicode-15.0.0": "^1.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/tr46_4.0.0_1674443736590_0.4993116147482981", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "tr46", "version": "4.1.0", "keywords": ["unicode", "tr46", "uts46", "punycode", "url", "whatwg"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tr46@4.1.0", "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/tr46#readme", "bugs": {"url": "https://github.com/jsdom/tr46/issues"}, "dist": {"shasum": "c8f9dd5275ebb87c9a55d91324543dcb413473e7", "tarball": "https://registry.npmjs.org/tr46/-/tr46-4.1.0.tgz", "fileCount": 7, "integrity": "sha512-CbwCbLo1wAnGIJpVX97P+d1Bggf4E4zQ7GdcyxmTeHNES2LV+eOI3xe+apCHq0UVrlnI652guuycPjMEhRdNnA==", "signatures": [{"sig": "MEUCIFHodWBOktLm231qbDU7ozCUG6VulKOLvY/PMIJuubWYAiEAtoCvU/bPFvoNpZBQtZKXLz88JXTZGYeBcUN8IqOXTb8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 218159, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0JHtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoHXw//SxgqkXsDTFxlK1SNg6mSah1w05neuifrNJlnmf1YlS2sJPUN\r\nHg6JkSgTj88tdYYH8G+iwkxP+uXS8M3apx5CL1XqIHWsPQCWYkB2DOHpR0HF\r\n7Xo5BEsEvSK+19AHSZ+CfNeyRE2vcapQsBi4ln6cHIYGlnmds8bqQKKnGKN/\r\njtnAcuHh3REgV6ZuMw+G11su3TjE2MwxVCM6G7lVTP2FiZDy8S7UobSvzB0y\r\nxXZsCgJT7cNVAsRP9MR1WO67Qb4cKtmZZ2lqjVJUppJRKBDr3+H0j/bh7k+c\r\nWgykieXKSC9toXeMw64WZ1VZSoXURLCP2xIus1Zv96GBuqvDBuxMxA/vAKXb\r\nK2HIm8zlSL2g6CSnrEOYnQFat3S8l6lZe/zyIFfHDYPcgWh9KthKmy0Ud5Ch\r\neN0XW/kQsS6FbCiJx2+PgBRxdVVQ+Ta23lpejrZUQU1IQALhAPqGQLcJB/Qe\r\nw4jUcgmox8D2vYbWL9USR+p2GdOc1OpnSPHbG6w26s2apqNbTmOIVA5/GBKE\r\ng8bDFcp44DlgQYdwJlMJM5812Wz2q36hSPcp3VFkqwVMmlrKddtHzg+PwTYq\r\nf+bRQkWEdtsqs0+GlfjEujJE4GS7FZQlf+CwgRBhXJUEjpnZ/lZnnXJcWEZk\r\nKqPSTUVh7OckBQF3prau+Lov6z4QCN7qEpU=\r\n=E8Fh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=14"}, "gitHead": "82234230c8be3b3a015a298b8d142e8a2d4ce984", "scripts": {"lint": "eslint .", "test": "mocha", "pretest": "node scripts/getLatestTests.js", "prepublish": "node scripts/generateMappingTable.js && node scripts/generateRegexes.js"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/tr46.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "An implementation of the Unicode UTS #46: Unicode IDNA Compatibility Processing", "directories": {}, "_nodeVersion": "19.2.0", "dependencies": {"punycode": "^2.3.0"}, "_hasShrinkwrap": false, "unicodeVersion": "15.0.0", "devDependencies": {"mocha": "^10.2.0", "eslint": "^8.32.0", "regenerate": "^1.4.2", "minipass-fetch": "^3.0.1", "@domenic/eslint-config": "^3.0.0", "@unicode/unicode-15.0.0": "^1.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/tr46_4.1.0_1674613229699_0.6581178546683062", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "tr46", "version": "4.1.1", "keywords": ["unicode", "tr46", "uts46", "punycode", "url", "whatwg"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tr46@4.1.1", "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/tr46#readme", "bugs": {"url": "https://github.com/jsdom/tr46/issues"}, "dist": {"shasum": "281a758dcc82aeb4fe38c7dfe4d11a395aac8469", "tarball": "https://registry.npmjs.org/tr46/-/tr46-4.1.1.tgz", "fileCount": 7, "integrity": "sha512-2lv/66T7e5yNyhAAC4NaKe5nVavzuGJQVVtRYLyQ2OI8tsJ61PMLlelehb0wi2Hx6+hT/OJUWZcw8MjlSRnxvw==", "signatures": [{"sig": "MEQCIAQhvxkazhon325eADBrUhCtEdIAJLn1j/rTpay0jvJkAiBa7GTKUWyR2HtIaSu7/KAEPCdrwWBbMPGT1BgB5GUVYQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 218128, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkCDGqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrc1hAAgYPNXhCkmiTuD548rnkvbLoA1aUVSrJ3+Bw5/B76Jkp6bYnM\r\nXQRg7Kbb5cdWbLUE4y8aJBdqaPG5AqT/Gr45I3OK3kkSnIvSFGJP6/P/KdVo\r\nRhatBhRvSK9qYUALIpfx+IFCjkaaN2VvpM6lBteSoNEXG6Z3butfkLrzbleD\r\nYYmxrlQmFnEussHqaF0oblCq1QZehgl2xe6F89zQv00vUYGvvXClJBcTzpl2\r\n+/cjrk1m7skBGrcSCPaMlxSrxM8TiYr/Otz3j2IwTssPodSH2/ALRbV/oC7B\r\nktdcdVu3+TPoOUG3tCBFeMRSxmQvnf3LCCB7TOv1Y3sQS76qdZs0YhwBTXfA\r\ntfr6igGx3xJnWRsfP9k/Q0atS16brMGqho8Fc2Qq4Isl+pc4NmodqU2ZkE3n\r\ntxxn6cQkasC/PyWeodLZkHnCVhdUlBdMVzUGlIeo38Ov92H6a+0jwNAS1E+D\r\nyYJVBUez42DEsHduk2l6LBqZ1+mtzs4pbc/R83CnSkRqaDhojBDwGfkTOtPb\r\ntvgX2+XS9R4LjbwDN7UfyhmgjqWSlXAucMTGxxrsBR6463mpyjDgY9m07eE7\r\n1O9fQGh2VdSjbAYCSKWBaSI9cI2b/CgjI/9e1Au72GWqPlASdE0sGMOlJgRb\r\nwgum5+nyw0TGE5UMRq9cwa7lft/srWNdzLU=\r\n=WHqa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=14"}, "gitHead": "b5302aff326ed603602935b3944e5be2fc5b6a0f", "scripts": {"lint": "eslint .", "test": "mocha", "pretest": "node scripts/getLatestTests.js", "prepublish": "node scripts/generateMappingTable.js && node scripts/generateRegexes.js"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/tr46.git", "type": "git"}, "_npmVersion": "9.4.0", "description": "An implementation of the Unicode UTS #46: Unicode IDNA Compatibility Processing", "directories": {}, "_nodeVersion": "19.6.0", "dependencies": {"punycode": "^2.3.0"}, "_hasShrinkwrap": false, "unicodeVersion": "15.0.0", "devDependencies": {"mocha": "^10.2.0", "eslint": "^8.32.0", "regenerate": "^1.4.2", "minipass-fetch": "^3.0.1", "@domenic/eslint-config": "^3.0.0", "@unicode/unicode-15.0.0": "^1.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/tr46_4.1.1_1678258602013_0.23444372439696548", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "tr46", "version": "5.0.0", "keywords": ["unicode", "tr46", "uts46", "punycode", "url", "whatwg"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "tr46@5.0.0", "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/tr46#readme", "bugs": {"url": "https://github.com/jsdom/tr46/issues"}, "dist": {"shasum": "3b46d583613ec7283020d79019f1335723801cec", "tarball": "https://registry.npmjs.org/tr46/-/tr46-5.0.0.tgz", "fileCount": 7, "integrity": "sha512-tk2G5R2KRwBd+ZN0zaEXpmzdKyOYksXwywulIX95MBODjSzMIuQnQ3m8JxgbhnL1LeVo7lqQKsYa1O3Htl7K5g==", "signatures": [{"sig": "MEYCIQCnQ0KiAombfYeg0HPkl0t4Om5KWeCVwTCs8RU7UON/mgIhAI27PbxLZLonzxSJE85Rb80kTT/AaV8nMifWc5+Z3KCC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 220527}, "main": "index.js", "engines": {"node": ">=18"}, "gitHead": "d6cd9a73bb9eb4b3defb50c348db2a60704d1367", "scripts": {"lint": "eslint .", "test": "node --test", "pretest": "node scripts/getLatestTests.js", "prepublish": "node scripts/generateMappingTable.js && node scripts/generateRegexes.js"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/tr46.git", "type": "git"}, "_npmVersion": "10.2.0", "description": "An implementation of the Unicode UTS #46: Unicode IDNA Compatibility Processing", "directories": {}, "_nodeVersion": "21.1.0", "dependencies": {"punycode": "^2.3.1"}, "_hasShrinkwrap": false, "unicodeVersion": "15.1.0", "devDependencies": {"eslint": "^8.53.0", "regenerate": "^1.4.2", "@domenic/eslint-config": "^3.0.0", "@unicode/unicode-15.1.0": "^1.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/tr46_5.0.0_1699160109646_0.3188651946817458", "host": "s3://npm-registry-packages"}}, "5.1.0": {"name": "tr46", "version": "5.1.0", "engines": {"node": ">=18"}, "description": "An implementation of the Unicode UTS #46: Unicode IDNA Compatibility Processing", "main": "index.js", "scripts": {"test": "node --test", "lint": "eslint", "pretest": "node scripts/getLatestTests.js", "prepublish": "node scripts/generateMappingTable.js && node scripts/generateRegexes.js"}, "repository": {"type": "git", "url": "git+https://github.com/jsdom/tr46.git"}, "keywords": ["unicode", "tr46", "uts46", "punycode", "url", "whatwg"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"punycode": "^2.3.1"}, "devDependencies": {"@domenic/eslint-config": "^4.0.1", "@unicode/unicode-16.0.0": "^1.6.5", "eslint": "^9.22.0", "globals": "^16.0.0", "regenerate": "^1.4.2"}, "unicodeVersion": "16.0.0", "_id": "tr46@5.1.0", "gitHead": "a034468f6f484d092da6ec24bc82cac4d2d4b596", "bugs": {"url": "https://github.com/jsdom/tr46/issues"}, "homepage": "https://github.com/jsdom/tr46#readme", "_nodeVersion": "23.7.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-IUWnUK7ADYR5Sl1fZlO1INDUhVhatWl7BtJWsIhwJ0UAK7ilzzIa8uIqOO/aYVWHZPJkKbEL+362wrzoeRF7bw==", "shasum": "4a077922360ae807e172075ce5beb79b36e4a101", "tarball": "https://registry.npmjs.org/tr46/-/tr46-5.1.0.tgz", "fileCount": 7, "unpackedSize": 225896, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIF4OxWwdRtlyxr1z+P780l3Yo2G+yWfywexXvonCffyEAiEAxYQp0cgm3UZlKtmm8w531w1u6g4qamCBz/LqG8OTGyw="}]}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/tr46_5.1.0_1742021399096_0.8984161523183427"}, "_hasShrinkwrap": false}}, "time": {"created": "2015-06-06T07:11:07.303Z", "modified": "2025-03-15T06:49:59.480Z", "0.0.1": "2015-06-06T07:11:07.303Z", "0.0.2": "2015-06-06T15:17:30.114Z", "0.0.3": "2016-01-20T02:08:54.875Z", "1.0.0": "2017-07-27T22:51:52.630Z", "1.0.1": "2017-09-03T02:08:56.379Z", "2.0.0": "2020-01-02T06:05:29.532Z", "2.0.1": "2020-02-20T04:30:39.918Z", "2.0.2": "2020-02-20T04:32:49.819Z", "2.1.0": "2021-05-22T10:07:08.518Z", "3.0.0": "2021-10-03T23:03:04.584Z", "4.0.0": "2023-01-23T03:15:36.813Z", "4.1.0": "2023-01-25T02:20:29.915Z", "4.1.1": "2023-03-08T06:56:42.196Z", "5.0.0": "2023-11-05T04:55:09.811Z", "5.1.0": "2025-03-15T06:49:59.284Z"}, "bugs": {"url": "https://github.com/jsdom/tr46/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/jsdom/tr46#readme", "keywords": ["unicode", "tr46", "uts46", "punycode", "url", "whatwg"], "repository": {"type": "git", "url": "git+https://github.com/jsdom/tr46.git"}, "description": "An implementation of the Unicode UTS #46: Unicode IDNA Compatibility Processing", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "readme": "# tr46\n\nAn JavaScript implementation of [Unicode Technical Standard #46: Unicode IDNA Compatibility Processing](https://unicode.org/reports/tr46/).\n\n## API\n\n### `toASCII(domainName[, options])`\n\nConverts a string of Unicode symbols to a case-folded Punycode string of ASCII symbols.\n\nAvailable options:\n\n* [`checkBidi`](#checkbidi)\n* [`checkHyphens`](#checkhyphens)\n* [`checkJoiners`](#checkjoiners)\n* [`ignoreInvalidPunycode`](#ignoreinvalidpunycode)\n* [`transitionalProcessing`](#transitionalprocessing)\n* [`useSTD3ASCIIRules`](#usestd3asciirules)\n* [`verifyDNSLength`](#verifydnslength)\n\n### `toUnicode(domainName[, options])`\n\nConverts a case-folded Punycode string of ASCII symbols to a string of Unicode symbols.\n\nAvailable options:\n\n* [`checkBidi`](#checkbidi)\n* [`checkHyphens`](#checkhyphens)\n* [`checkJoiners`](#checkjoiners)\n* [`ignoreInvalidPunycode`](#ignoreinvalidpunycode)\n* [`transitionalProcessing`](#transitionalprocessing)\n* [`useSTD3ASCIIRules`](#usestd3asciirules)\n\n## Options\n\n### `checkBidi`\n\nType: `boolean`\nDefault value: `false`\nWhen set to `true`, any bi-directional text within the input will be checked for validation.\n\n### `checkHyphens`\n\nType: `boolean`\nDefault value: `false`\nWhen set to `true`, the positions of any hyphen characters within the input will be checked for validation.\n\n### `checkJoiners`\n\nType: `boolean`\nDefault value: `false`\nWhen set to `true`, any word joiner characters within the input will be checked for validation.\n\n### `ignoreInvalidPunycode`\n\nType: `boolean`\nDefault value: `false`\nWhen set to `true`, invalid Punycode strings within the input will be allowed.\n\n### `transitionalProcessing`\n\nType: `boolean`\nDefault value: `false`\nWhen set to `true`, uses [transitional (compatibility) processing](https://unicode.org/reports/tr46/#Compatibility_Processing) of the deviation characters.\n\n### `useSTD3ASCIIRules`\n\nType: `boolean`\nDefault value: `false`\nWhen set to `true`, input will be validated according to [STD3 Rules](http://unicode.org/reports/tr46/#STD3_Rules).\n\n### `verifyDNSLength`\n\nType: `boolean`\nDefault value: `false`\nWhen set to `true`, the length of each DNS label within the input will be checked for validation.\n", "readmeFilename": "README.md"}