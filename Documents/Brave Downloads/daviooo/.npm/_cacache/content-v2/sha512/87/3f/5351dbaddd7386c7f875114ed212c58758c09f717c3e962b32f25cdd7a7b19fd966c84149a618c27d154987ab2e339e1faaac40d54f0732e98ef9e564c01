{"name": "@tufjs/models", "dist-tags": {"latest": "3.0.1"}, "versions": {"1.0.0-alpha.1": {"name": "@tufjs/models", "version": "1.0.0-alpha.1", "devDependencies": {"jest": "^29.4.3", "eslint": "^8.34.0", "ts-jest": "^29.0.5", "prettier": "^2.8.4", "typescript": "^4.9.5", "@types/jest": "^29.4.0", "@types/node": "^18.14.1", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "@typescript-eslint/parser": "^5.53.0", "@typescript-eslint/eslint-plugin": "^5.53.0"}, "dist": {"shasum": "4557ac9fd153cd20df42fdfe7e0a87cb06ade951", "tarball": "https://registry.npmjs.org/@tufjs/models/-/models-1.0.0-alpha.1.tgz", "fileCount": 42, "integrity": "sha512-rUk6gB1KKNsxxV5QGN5FAlkqx75klxNenwXk7zu7l31AC17OfI3ONIXYWJ7kW0vbcDFYanCezGn/gB09QeZZlA==", "signatures": [{"sig": "MEYCIQD1yGyoZHunCtbmBr2N+r45nId6Dmfn2N1hETaAw69m4AIhAIFVhQ8aI/oQOy/lDuxER0H85YB+6kvgQ0PjoD4cbrFI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80659, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+RcUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpzIw//comKyjq2ozBAm/I6HBCdP7wBMk29m4fB19Cn3ZiJyaIMQvKB\r\ndZPtzuIPoFMiiy3ssL9AxiJjT0VtZ7DNx9wsXOifoSfDu2r4OyNB0PvOf8Bi\r\nFe/y/T2ohNHcnRpLg/POy1wjphYgbDfyxFSaug/nJlGkRB2u+clxLCKmE8rC\r\nSnr5eOWk39fBx7hHnx3ApFkCImCmiW3VbeL3gb9wX0b7YtZmEGdm10wUEhEM\r\nBv+qpBkJNlTlHj9n/vxVLwgH8zm9DkZUoIZyNiTAnhUCzTdImieNO5wQ2cuh\r\nDApGWLtQwHkil/ZlLBzSPa4ACaardAbhwpLeomgCPcN1U49V1lwrsdK6sTfE\r\nAiWSnbOwWbHgUZ6ELDWQ5vWwOFt32o59aMwwEc5+OiBBVFszBe4YURi2F6dg\r\n0+TFBWqbVZPpT2yaO/u9dqiO5upfJsJHmKbAakQKwxuIrCXZu0HGZj4qD8JQ\r\nRr0q8fV0/Q5bHxxOzDi5Jeg8KUP8fFZEtZEm62n/QRw0OghpmzibITPjrJDW\r\nvAxHYPsQiSF74eg67jAhulK68T267aN7ayEpPUdPbw2l7D3LaFWitp6daDVm\r\nqlqv/D11btBeoOOzueE59hBecsOXDKaO2HR1svQwF5pF5TgiDqDMIRupKK6H\r\nhpFFMOniIQ8/Y4tLj7V+asyEYBZRd8YL6XU=\r\n=L1UN\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "@tufjs/models", "version": "1.0.0", "dependencies": {"minimatch": "^6.1.0"}, "devDependencies": {"typescript": "^4.9.5", "@types/node": "^18.14.1", "@types/minimatch": "^5.1.2"}, "dist": {"shasum": "5a5784e8770b7d014b5f87bff35af1f71fdebf1f", "tarball": "https://registry.npmjs.org/@tufjs/models/-/models-1.0.0.tgz", "fileCount": 43, "integrity": "sha512-RRMu4uMxWnZlxaIBxahSb2IssFZiu188sndesZflWOe1cA/qUqtemSIoBWbuVKPvvdktapImWNnKpBcc+VrCQw==", "signatures": [{"sig": "MEUCIQCzQ55zckBxcAO6/VR6AhxQV1t271UmeT/4okfrPHk+XQIgZV6D5zg14vNaY+G3o06NEL+P9d5jwhV6LR/MRGvPfpY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tufjs%2fmodels@1.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 80040, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkARszACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpnBQ/5Ab1xOszYx50Ax/QMU3NF7WRlOIXVtrW5JIGF5w7QZGfx9Bv2\r\n9fF9Fh06xaY5Krgk68n5kTEgw7CpdJqWlM7PVZHSo4oODO+vetw8xUU4C79X\r\nxxNzfH+ag2GjCTPej/WUXAcm5keom0CrCzYQ7sFcEC5vW8rILstv2fE1oXZo\r\nuLKugeYuS0O3uSR3N2ktyvCAbOMv8nSHExKRV+oGXpk5jlPa8xwUK+NmRAeL\r\nG7SaAjrixx8kPkSdROkj88yUHyayF6MQgj0hCU109ozx5QynUBntoUOFYofd\r\nnNuKsz22mw4Q+mvWy5dezChVbLAyF1H9T9tDBRpMR7xPkGX/lVQjB9lN+oHC\r\ns3WcxM3fQg7h0I4pAgeUWEa8j6kkpDjKBYP5i6uMhAo17IZJgihaQdc+Dbds\r\nAtxglS5Y8CgwTDzuSkIqCDIJ84EjbgG7YFeIrAlF26P5FpbE2pYZG27hAT6n\r\no03atPkUaxeYGJwQw49ASa2cos4rVffD8ikHvNE66t36Q0MW2444nbru8mM8\r\nDQCN3W66tSAxVo+fmYCjU+R+YSNcuVp4ZPNZx/Uks/62mI+T043ig/mvfemQ\r\ntuDpP4dNZ1w+LPykINe7hwvbKA0HEgyvF3CR1nkH5rXpp+ni9a2FFa9YszgV\r\nPjOR/x7cqW8n1SYxLSxv8I0VWRV8n/cmlBE=\r\n=94W+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "1.0.1": {"name": "@tufjs/models", "version": "1.0.1", "dependencies": {"minimatch": "^7.4.2"}, "devDependencies": {"typescript": "^4.9.5", "@types/node": "^18.15.3", "@types/minimatch": "^5.1.2"}, "dist": {"shasum": "cc8bb0478188b4b6e58d5528668212724aac87ac", "tarball": "https://registry.npmjs.org/@tufjs/models/-/models-1.0.1.tgz", "fileCount": 43, "integrity": "sha512-AY0VoG/AXdlSOocuREfPoEW4SNhOPp/7fw6mpAxfVIny1uZ+0fEtMoCi7NhELSlqQIRLMu7RgfKhkxT+AJ+EXg==", "signatures": [{"sig": "MEUCIC+VCbyujutyh14mEwAId8QltznAmD9FFc0xsJdoaX0rAiEAjtttpuPhq0gv4XGs1UWuENuFGHHcCxByt0/yT1ueZcY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tufjs%2fmodels@1.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 80050, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkGhf0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqH6Q/9G2gPMaxlqhsLhtphaforAJ4dLkL4qXogoH+JAVtQH3RYouGX\r\nXmO0v+7lop77km1p1DA70ESxx/dP029DI+STWuX7Ujhvjs5aRuue5XNBfE6f\r\nz8QZuc7WlGQAke2JkGhPdGiwKYOmi9fdSBbJPuBHQ9kQOcNcZH0ibeKSkdhg\r\ny02HeyOe4hoo1mDt36nWwJQjKJqg7HNT32r6EdigBnWlYZxeFo2+xmpfwACk\r\nLXTnqi1tmCHQGOGDxMNawIsu+9WCQsCf/7sNOolr7CaENsRohhIwUhnmhhM5\r\nTIopcGuszETMAGVYArOLGnMFe8GeO1owy3SMIMiRs6dTYNEpX1lHCogNSVZl\r\nolgEPgPOk2uR2pSR/0s4IEtQxX5N5Usf2eEQUH3fEsjt8w/SO7OlAkpjeZp/\r\nrequPL4kcCqsJkLqlp2cE/nX2UXJTUGGR9axIDwV4FsO50WvaQzwtiUsvBge\r\nwjFuKdkLshlooEU3yb2Bo9WGuBjPCdbLQB+7b/UV8x8/zA/rdBRCYWHHDcPk\r\nsouzbh+7qUqhT0jT2t//XlK4G5V/dfDkdBkYGJoM220o/hGnRNi9ewRczKxu\r\n+T7N6yASTHxt+69AHTMs+qvziQukSfTMjv62+0FUaHLlpIIFsDEB9NZZFXFk\r\nZAGWieImpIB9phWVUbnQEYkTVynETfkhNIg=\r\n=2bie\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "1.0.2": {"name": "@tufjs/models", "version": "1.0.2", "dependencies": {"minimatch": "^8.0.3", "@tufjs/canonical-json": "1.0.0"}, "devDependencies": {"typescript": "^5.0.4", "@types/node": "^18.15.11", "@types/minimatch": "^5.1.2"}, "dist": {"shasum": "6c1e99210ada62c057b1742b5528d85acbfe0a47", "tarball": "https://registry.npmjs.org/@tufjs/models/-/models-1.0.2.tgz", "fileCount": 41, "integrity": "sha512-uxarDtxTIK3f8hJS4yFhW/lvTa3tsiQU5iDCRut+NCnOXvNtEul0Ct58NIIcIx9Rkt7OFEK31Ndpqsd663nsew==", "signatures": [{"sig": "MEUCICO/vbtRd95c0B5L53FdtSfMTf8XfK9arqsJJRpg+d6cAiEAjWYc6MeJX73OjGphKcTPVSfabZ9J8Gzay5xGUsWCirg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tufjs%2fmodels@1.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 77827, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkNbwKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpHvRAAl+byxTgbqjpmXmuHUSh64N9x7gCUeAXMyD38jgYRe8eAu0rP\r\nudMXSRD2upDYNtoHQRB3mxYqyx/vqmiEunmuOsI4SXogb3Azsq1y3brM+2xD\r\njt/tIXjbH4kCigNqw7pLI9rvauHIIKdEVF0/XWyuDQ45TyqHNdsuEXrXoSTz\r\nIUyzf55UzaSTWvlKXAKOr+I6nJjJ9U8TauYrTcO1MbilzGCzxSwJ9Nl9LWZI\r\nlSVzqd9gM3luvvNbZHj4Udy+FZ4LRp8B1DFbq9GUR3o3kIMyJYszYJgmTWGy\r\nkpRgUNELgdzCnbueIJNWKIpH21TczCZlqPzToSp8J4D4dkkMj5CUJ75idHfN\r\n9t4Mi1PWbCM43R4ljHfyvIYtJyFJwhDiChcMrwqfOehhT8lt7rUqo05YEJNp\r\ns4pDHPbFezVWddCCt1jFPbbe9BaA9hHjt32TnwvnTm3vLkDrqj19ic7PI4Op\r\n2yIkYlzrMpxj4LDe/edQPXD8XlmXboew2PaxDjbvaZxV3re5Hqi4b4Cf9DR4\r\n1zbXPQsiQBoM7lz4Y++KLGklWKHrniO1RzzrTK3HewNLxH7rXBpki5S1kVRg\r\nBC/eCNT5tGk6YW3r0UKmSQGET/0SH12mKi0IgSDS8B7IVuCjS8gI4pVVvicO\r\nqE7ewRaE1WvAlbRnbZQKAwAyt1bsDi1hrio=\r\n=9Jp8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "1.0.3": {"name": "@tufjs/models", "version": "1.0.3", "dependencies": {"minimatch": "^7.4.6", "@tufjs/canonical-json": "1.0.0"}, "devDependencies": {"typescript": "^5.0.4", "@types/node": "^18.15.11", "@types/minimatch": "^5.1.2"}, "dist": {"shasum": "e6cb8a86834da7459a7c836cd892dee56b4bab44", "tarball": "https://registry.npmjs.org/@tufjs/models/-/models-1.0.3.tgz", "fileCount": 41, "integrity": "sha512-mkFEqqRisi13DmR5pX4x+Zk97EiU8djTtpNW1GeuX410y/raAsq/T3ZCjwoRIZ8/cIBfW0olK/sywlAiWevDVw==", "signatures": [{"sig": "MEUCIQC5TsctiOqYOC6njpGuDs50knDLvUXitVBnr8h5UVdO2wIgeEkOzV8/fzmsY7doOqmnNnSct/aSwv7bDF9zt3cMsps=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tufjs%2fmodels@1.0.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 77827, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPuhNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqSEA/9EFl2XBr6RSGFW8tqzUrRpQfXCccApU83eeZkr8NAWPN1lj66\r\n7CHZ/w+DF7EbZ9Jed+ANKbNxa9ZzasGTO0kVAlCA82T7cWIDvsASRFD9DjTl\r\nUEwXjFEdIUMIVNt4ry4GjZu+XkaRHKfCfLou6kvy6vx6br1u8Egpbd/f00/g\r\nHOkOIU7kd6LQqiI6nBnjXYkXDK80AE70kq9Zc/f0WU5sPbINZ/I6bvHgBOtb\r\nA4oBo3BJyel1ISfGCfIgUi2AboKi2xN6fhEFBhjv6PrNJeAXRURwsY7i36o8\r\nqpNw7OCzBI6iIPy4prj8J4019L+zlZ7cRCSe3cHzX0Id9iZDEzqVAT3cmI/+\r\nn9tsGJXIZkpDlOZPVk7zPxlROTWtzKpTogz/fMVhFlDxesi6ZMaYFFsvnGFd\r\nsN+xT5cm70IRC8gsgAPyu7j+1f4BCVD37klrNQTz580Up+0FRI5Mhr9nsXes\r\nasPqpBLg+ChUJdPzJRM2tJMj4MpqUiTmPMs1OpPIz1/rsUHzlJYp85RMBD+/\r\nEoUlDahGeSgOTQxcECx8UM8O5/UwJHZf3ngQkMCFsca6MQUPZYbwfcqGZGyq\r\nPd0w62v9plshM7Eh28XFGDmpvx6FseWTef90jA+xQ5fqnxqfTy2BJw55yFba\r\n7k4ep9nvVdvplNy+Rz2VRi6EEtGNW6XYiMw=\r\n=Bya5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "1.0.4": {"name": "@tufjs/models", "version": "1.0.4", "dependencies": {"minimatch": "^9.0.0", "@tufjs/canonical-json": "1.0.0"}, "devDependencies": {"typescript": "^5.0.4", "@types/node": "^18.16.3"}, "dist": {"shasum": "5a689630f6b9dbda338d4b208019336562f176ef", "tarball": "https://registry.npmjs.org/@tufjs/models/-/models-1.0.4.tgz", "fileCount": 41, "integrity": "sha512-qaGV9ltJP0EO25YfFUPhxRVK0evXFIAGicsVXuRim4Ed9cjPxYhNnNJ49SFmbeLgtxpslIkX317IgpfcHPVj/A==", "signatures": [{"sig": "MEUCIBXzvBBF2yXw4Be2+CzFXl+KpHwGBZKU/CMD1pjb+rbLAiEArcnq0QjQ71OHSS4q9WVeveHscZWQ5oikNro23GJCAPs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tufjs%2fmodels@1.0.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 77777, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkUp3/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpEKhAAnDECrdR2nN5jFp9AczfmH6oV0LHKezjww4+4Ye7VbxkGasFg\r\nlP3c4jMjLm76RMbKFrVJhRSBO8kVJ1ZQ8k3CxXXDkvZezYcugX9c6cAccMs/\r\nDvp7vl/Z0qE4BpVrzlTx3HAlwMs+VlT0ajxLxSVH9vuXgOId4se6VJrLNM6j\r\n2iDhrKJWX8hBWiPt5VmR8+EeHwKYDY1A6IH9C1DCS6v9CzrSi5hZRigYu/T8\r\nHBrD0s0h3/Rdfheyd4w7rDeLKEIie1UxOOhkkAXp5RQgDutt0BNKlI8N1+nv\r\nB/YuAt+LsktUJohe5tTP0THX+vch8OGuTvPyWbhYRfzqIX95COOq6yQqmtVc\r\nvajYAPgR57p2C1aJGGUITnl8l3Z6HQjeonTSCOepyf9qxZAFZHZd1WZveq55\r\n3DbjcIMb6af0AGSS1+xO7EF0wgl54h7K9VH2Jb1Buq12zRCia3skwRUewK8t\r\ngkygXZBRPAicpYoHw9jrI0P+90eZV3zWV7CAKbCy4kTXMeWtNR1znBQoI/eV\r\ng5usWo52iFYDKO7OQPCtPJnuBv/6SBCjKua8L+jb14kdM9iZ7MXtAYhQI4Yn\r\nfPaJ3t4HKTlwAybNbrmNr+qgKXfBdrrWXqkOuwInEjES5CD/hm/Xs9aEh0xH\r\nWms+bX/orwt+RSS0wc3QlU6qHYjl3ctuIUY=\r\n=qu/9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "2.0.0": {"name": "@tufjs/models", "version": "2.0.0", "dependencies": {"minimatch": "^9.0.3", "@tufjs/canonical-json": "2.0.0"}, "dist": {"shasum": "c7ab241cf11dd29deb213d6817dabb8c99ce0863", "tarball": "https://registry.npmjs.org/@tufjs/models/-/models-2.0.0.tgz", "fileCount": 41, "integrity": "sha512-c8nj8BaOExmZKO2DXhDfegyhSGcG9E/mPN3U13L+/PsoWm1uaGiHHjxqSHQiasDBQwDA3aHuw9+9spYAP1qvvg==", "signatures": [{"sig": "MEYCIQCuSx2xk7S7biC+ljqIdSNCahjfG0vI5GFCHn5uKsF/xAIhANnFvVcUI272Np4mKGKXJka6qCDUD4MnR4/xIT/HWvGD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tufjs%2fmodels@2.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 77671}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "2.0.1": {"name": "@tufjs/models", "version": "2.0.1", "dependencies": {"minimatch": "^9.0.4", "@tufjs/canonical-json": "2.0.0"}, "dist": {"shasum": "e429714e753b6c2469af3212e7f320a6973c2812", "tarball": "https://registry.npmjs.org/@tufjs/models/-/models-2.0.1.tgz", "fileCount": 41, "integrity": "sha512-92F7/SFyufn4DXsha9+QfKnN03JGqtMFMXgSHbZOo8JG59WkTni7UzAouNQDf7AuP9OAMxVOPQcqG3sB7w+kkg==", "signatures": [{"sig": "MEYCIQDYxugz8vRGW4j8GU1pAvgJTHozGsmJ+0lD7KtRFu2BcAIhAJYmmwRXfJY4uNHlB7krW2Q1xWBpzIkkkhA7zPDFXlWv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tufjs%2fmodels@2.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 77702}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "3.0.0": {"name": "@tufjs/models", "version": "3.0.0", "dependencies": {"minimatch": "^9.0.5", "@tufjs/canonical-json": "2.0.0"}, "dist": {"shasum": "2ad4159c1269864d85c5b272740b5b114c8c0077", "tarball": "https://registry.npmjs.org/@tufjs/models/-/models-3.0.0.tgz", "fileCount": 41, "integrity": "sha512-aB09vGUD/8G4wgWaCtm+7pGynktEOQSRsV8kvUeLu65Ztgj6+Tj6vUpw/l2bkMzI+ACQWalXFA/YpEKE54SwyQ==", "signatures": [{"sig": "MEUCIBRwZ+yvf2d+TbOFJzYK5Il/XqdYvXV5OpjVjceMGCrYAiEA93kQFtCdsSKn750CPMquHzokm8bAi4i/wSszL/VixZw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tufjs%2fmodels@3.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 77281}, "engines": {"node": "^18.17.0 || >=20.5.0"}}, "3.0.1": {"name": "@tufjs/models", "version": "3.0.1", "dependencies": {"@tufjs/canonical-json": "2.0.0", "minimatch": "^9.0.5"}, "dist": {"integrity": "sha512-UUYHISyhCU3ZgN8yaear3cGATHb3SMuKHsQ/nVbHXcmnBf+LzQ/cQfhNG+rfaSHgqGKNEm2cOCLVLELStUQ1JA==", "shasum": "5aebb782ebb9e06f071ae7831c1f35b462b0319c", "tarball": "https://registry.npmjs.org/@tufjs/models/-/models-3.0.1.tgz", "fileCount": 41, "unpackedSize": 77662, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tufjs%2fmodels@3.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDzH2ccVIECPiUQIVhxJHmUWnc8TtUMPE8V+ZoUp6Gk8AIhAK5ov+wg8U0OTaMHUI/t5e6bkqiBG70luBJo7gLnp32+"}]}, "engines": {"node": "^18.17.0 || >=20.5.0"}}}, "modified": "2024-10-14T15:37:35.896Z"}