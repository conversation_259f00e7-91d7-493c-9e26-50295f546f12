{"_id": "wide-align", "_rev": "13-48fe08429a42d94fb03d4f83d4c5544a", "name": "wide-align", "description": "A wide-character aware text alignment function for use on the console or with fixed width fonts.", "dist-tags": {"latest": "1.1.5"}, "versions": {"1.0.0": {"name": "wide-align", "version": "1.0.0", "description": "A wide-character aware text alignment function for use on the console or with fixed width fonts.", "main": "align.js", "scripts": {"test": "tap --coverage test/*.js"}, "keywords": ["wide", "double", "unicode", "cjkv", "pad", "align"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org/"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/iarna/wide-align.git"}, "dependencies": {"string-width": "^1.0.1"}, "devDependencies": {"tap": "^2.3.2"}, "gitHead": "195beaf9ffddacc074a179ddb7750382914152d5", "bugs": {"url": "https://github.com/iarna/wide-align/issues"}, "homepage": "https://github.com/iarna/wide-align#readme", "_id": "wide-align@1.0.0", "_shasum": "1f106589d579da0c79ea3d76f3ee5c399116d0b0", "_from": ".", "_npmVersion": "3.5.2", "_nodeVersion": "4.2.2", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"shasum": "1f106589d579da0c79ea3d76f3ee5c399116d0b0", "tarball": "https://registry.npmjs.org/wide-align/-/wide-align-1.0.0.tgz", "integrity": "sha512-tXiTonk4+7Gm79R/Ft+IX7P3DleFfsL2YxdPzfNGY8nC5nGBAOi08rX8wWLOoz/UKOo/Hwio5dBtxyfeBHu1iw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEwIWxT4pEFnBAGcMtOHuDAPW+y+V4t3EMPSCKB9c49cAiAtb9RhvdsVe1sMh39m0DiVxF1gI/rTVmo+fp0gfB0xCA=="}]}, "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "directories": {}}, "1.0.1": {"name": "wide-align", "version": "1.0.1", "description": "A wide-character aware text alignment function for use on the console or with fixed width fonts.", "main": "align.js", "scripts": {"test": "tap --coverage test/*.js"}, "keywords": ["wide", "double", "unicode", "cjkv", "pad", "align"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org/"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/iarna/wide-align.git"}, "dependencies": {"string-width": "^1.0.1"}, "devDependencies": {"tap": "^2.3.2"}, "gitHead": "7f81882d88ef050f9cf728beb89a6d2347030b9f", "bugs": {"url": "https://github.com/iarna/wide-align/issues"}, "homepage": "https://github.com/iarna/wide-align#readme", "_id": "wide-align@1.0.1", "_shasum": "6cbb9c0bb42e020592877d61fc093358644998fd", "_from": ".", "_npmVersion": "3.5.2", "_nodeVersion": "4.2.2", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"shasum": "6cbb9c0bb42e020592877d61fc093358644998fd", "tarball": "https://registry.npmjs.org/wide-align/-/wide-align-1.0.1.tgz", "integrity": "sha512-J<PERSON>rhnKZmiwTmkNoEwtam/YjMYAdrBtC/xMOiRbborrKjyzYZgXJOvf9N0r9RWmabL1Dy4D+HVQCCphJMaK/pfQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDD9OwoK4oHO+SY89TJrb0Pjk/6ScqELyfel1sQtVWoHAiEAzdOfr5ahL3aEL6WFzWdrbI5I7WR+nOndoENBiTr3V8c="}]}, "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "directories": {}}, "1.0.2": {"name": "wide-align", "version": "1.0.2", "description": "A wide-character aware text alignment function for use on the console or with fixed width fonts.", "main": "align.js", "scripts": {"test": "tap --coverage test/*.js"}, "keywords": ["wide", "double", "unicode", "cjkv", "pad", "align"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org/"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/iarna/wide-align.git"}, "dependencies": {"string-width": "^1.0.1"}, "devDependencies": {"tap": "^2.3.2"}, "gitHead": "a38742ce5826d101938535d140d5d6ffb123f676", "bugs": {"url": "https://github.com/iarna/wide-align/issues"}, "homepage": "https://github.com/iarna/wide-align#readme", "_id": "wide-align@1.0.2", "_shasum": "3473d4cf2528ea002fedc8d6866bc1afad5108c6", "_from": ".", "_npmVersion": "3.5.2", "_nodeVersion": "4.2.2", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"shasum": "3473d4cf2528ea002fedc8d6866bc1afad5108c6", "tarball": "https://registry.npmjs.org/wide-align/-/wide-align-1.0.2.tgz", "integrity": "sha512-jPtcXc1kqTPwML5mBRBzBYEMQw9jVTNUICnwPtCeMoEcQwBOC+j6qqnVKxS6tJZU+k5JHBSshG3wk6aiCZESag==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEDxAaeD8UXxgJaRFQsh4J0Xqvt9mqeeTug3kTeSLXNaAiALE81hoxfVtjG5z998uzdnHChaYUiMwYrIkbSnu+quDg=="}]}, "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "directories": {}}, "1.1.0": {"name": "wide-align", "version": "1.1.0", "description": "A wide-character aware text alignment function for use on the console or with fixed width fonts.", "main": "align.js", "scripts": {"test": "tap --coverage test/*.js"}, "keywords": ["wide", "double", "unicode", "cjkv", "pad", "align"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org/"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/iarna/wide-align.git"}, "dependencies": {"string-width": "^1.0.1"}, "devDependencies": {"tap": "^2.3.2"}, "gitHead": "fe3f7f210650913d5bee702d7e19938f6977bc8a", "bugs": {"url": "https://github.com/iarna/wide-align/issues"}, "homepage": "https://github.com/iarna/wide-align#readme", "_id": "wide-align@1.1.0", "_shasum": "40edde802a71fea1f070da3e62dcda2e7add96ad", "_from": ".", "_npmVersion": "3.5.3", "_nodeVersion": "4.2.2", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"shasum": "40edde802a71fea1f070da3e62dcda2e7add96ad", "tarball": "https://registry.npmjs.org/wide-align/-/wide-align-1.1.0.tgz", "integrity": "sha512-rlD2ELOb94L+t1gMHCObqlC3QvrHVF1V5ipWEGm4vb9OK2C6ZTvluPg1Mfp2O9POtGb/3aZNxBpKOmx0JNVHSQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD/CWnA9074+NdNuz9cF+hpi3ZWbJzlATnTb1nVaSZi3gIhAKXAKOB7CnFzTiMkSJinPg980tlHdKycvJzgg0Y5pJJM"}]}, "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "directories": {}}, "1.1.1": {"name": "wide-align", "version": "1.1.1", "description": "A wide-character aware text alignment function for use on the console or with fixed width fonts.", "main": "align.js", "scripts": {"test": "tap --coverage test/*.js"}, "keywords": ["wide", "double", "unicode", "cjkv", "pad", "align"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org/"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/iarna/wide-align.git"}, "dependencies": {"string-width": "^2.0.0"}, "devDependencies": {"tap": "^10.3.2"}, "files": ["align.js"], "gitHead": "9bfc4e482cf69e8abff2a2d9b19f105729a77a4b", "bugs": {"url": "https://github.com/iarna/wide-align/issues"}, "homepage": "https://github.com/iarna/wide-align#readme", "_id": "wide-align@1.1.1", "_npmVersion": "4.6.1", "_nodeVersion": "4.6.1", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-zD7wsq/y2fs6BGv0rdS/PpnGuqCoUmWdbVwitpObGByptZD4BKlAa9W6h6w2I+qsdaYoCUAvagV/IMiT2Z9kpg==", "shasum": "d2ea8aa2db2e66467e8b60cc3e897de3bc4429e6", "tarball": "https://registry.npmjs.org/wide-align/-/wide-align-1.1.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCB450qRPeqEFRTdkGxdWMoL6yRUqID0KgH5Yv8xGq0DQIgeuG6QSfkF+3cS4pAhY9NOZZm2Plhll8dfRApblMx6Y4="}]}, "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/wide-align-1.1.1.tgz_1494458462732_0.9675460546277463"}, "directories": {}}, "1.1.2": {"name": "wide-align", "version": "1.1.2", "description": "A wide-character aware text alignment function for use on the console or with fixed width fonts.", "main": "align.js", "scripts": {"test": "tap --coverage test/*.js", "version": "perl -pi -e 's/^(  \"version\": $ENV{npm_config_node_version}\").*?\",/$1abc\",/' package-lock.json ; git add package-lock.json"}, "keywords": ["wide", "double", "unicode", "cjkv", "pad", "align"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org/"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/iarna/wide-align.git"}, "dependencies": {"string-width": "^1.0.2"}, "devDependencies": {"tap": "^10.3.2"}, "files": ["align.js"], "gitHead": "f904295d03f73ddce10c2c1b0808513ebb5525c6", "bugs": {"url": "https://github.com/iarna/wide-align/issues"}, "homepage": "https://github.com/iarna/wide-align#readme", "_id": "wide-align@1.1.2", "_npmVersion": "4.6.1", "_nodeVersion": "7.7.4", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-ijDLlyQ7s6x1JgCLur53osjm/UXUYD9+0PbYKrBsYisYXzCxN+HC3mYDNy/dWdmf3AwqwU3CXwDCvsNgGK1S0w==", "shasum": "571e0f1b0604636ebc0dfc21b0339bbe31341710", "tarball": "https://registry.npmjs.org/wide-align/-/wide-align-1.1.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICuDMbFV6leirk/UbS3FX0IzFcll7cKWXbukgjW0BZ+5AiEAzA/n1phaLU8nXdgbISCEXKrDbLw3fWYSZ+idDUheBC8="}]}, "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/wide-align-1.1.2.tgz_1494527486997_0.9166653461288661"}, "directories": {}}, "1.1.3": {"name": "wide-align", "version": "1.1.3", "description": "A wide-character aware text alignment function for use on the console or with fixed width fonts.", "main": "align.js", "scripts": {"test": "tap --coverage test/*.js", "version": "perl -pi -e 's/^(  \"version\": $ENV{npm_config_node_version}\").*?\",/$1abc\",/' package-lock.json ; git add package-lock.json"}, "keywords": ["wide", "double", "unicode", "cjkv", "pad", "align"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org/"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/iarna/wide-align.git"}, "dependencies": {"string-width": "^1.0.2 || 2"}, "devDependencies": {"tap": "10 || 11 || 12"}, "files": ["align.js"], "gitHead": "6b766c9874a1e5157eda2ac75b90ccc01b313620", "bugs": {"url": "https://github.com/iarna/wide-align/issues"}, "homepage": "https://github.com/iarna/wide-align#readme", "_id": "wide-align@1.1.3", "_npmVersion": "6.1.0", "_nodeVersion": "9.3.0", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-QGkOQc8XL6Bt5PwnsExKBPuMKBxnGxWWW3fU55Xt4feHozMUhdUMaBCk290qpm/wG5u/RSKzwdAC4i51YigihA==", "shasum": "ae074e6bdc0c14a431e804e624549c633b000457", "tarball": "https://registry.npmjs.org/wide-align/-/wide-align-1.1.3.tgz", "fileCount": 4, "unpackedSize": 4551, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBzBzCRA9TVsSAnZWagAAxeYP/1L3tYrqfB/llIclGJJM\ns3DfFOyKiZgjsOMKrRXJcqEryc1YPQWJ3BqMj0viqaYNXOoOpMihGVFqGP6b\nb+PlmX9INOi7hU7q6Wb/ICXnLDVOn83Ye8uQN/aKlCJ/s1cIz27A6dL7u2d2\nmP5a/Eaxok9JjgFipBwxm5d93ZiZ1rOoILaiXCTlvzSCNpbdsZVDbOuEC9Bz\nkLRRXuG3dd8t8g9xi24VT5VFEiztVlvTgTGfcain+dZccQpNUtgDdFxX0Zco\niAjwt0n1L1lip3n40GHEz8lzPYULz3qAvt07bBFevSWNfPDGF4+n9eN91TAQ\nTqpY1kIgqhwz+QsTVC/w13yL4RKbuRqmWNshIAz7xqM2gvyzU/Zawead3MsC\naP3tdu6PA5KZxosBzozwzw1KRBmkg4EFQZTj+3yu6tL4En5jVS2VzznyLlrM\n1/J7NWeCub+yoKCUTNNnr1pGTMMEOIzsfU8NhvjHb3TxVcFkabdITveOVPf+\n5cElAiJ3yvxY/I1V/0hXl0EAgQEsghdRuDcXN3E1PsE0TwAyreOl4QJWyHU5\nILJhOb2wk9IL0aPKlun6tacPehAqu2EWV5TiGISM9HNgaArY9D3mU0iHmnTy\nADsKuTPcnY00KHtxuZT6a3QgCvrvtmxHL6zm1521mru3V1+7oGkrk5c6Hlzk\nYbx2\r\n=w9c6\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDDMMAJeDEY70dvw4ypj2qtQpUgDqRJWPhFs4VPVvg5+wIhAKk1t8/a6JJNi+xiY2PgLLs8Nbw2iDrF4unE9j4eoL/9"}]}, "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/wide-align_1.1.3_1527197810643_0.006292446790612027"}, "_hasShrinkwrap": false}, "1.1.5": {"name": "wide-align", "version": "1.1.5", "description": "A wide-character aware text alignment function for use on the console or with fixed width fonts.", "main": "align.js", "scripts": {"test": "tap --coverage test/*.js"}, "keywords": ["wide", "double", "unicode", "cjkv", "pad", "align"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org/"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/iarna/wide-align.git"}, "//": "But not version 5 of string-width, as that's ESM only", "dependencies": {"string-width": "^1.0.2 || 2 || 3 || 4"}, "devDependencies": {"tap": "*"}, "gitHead": "c1bf09df8a2c549d68a7a0e65315db89d0eff457", "bugs": {"url": "https://github.com/iarna/wide-align/issues"}, "homepage": "https://github.com/iarna/wide-align#readme", "_id": "wide-align@1.1.5", "_nodeVersion": "13.13.0", "_npmVersion": "6.14.4", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-eDMORYaPNZ4sQIuuYPDHdQvf4gyCF9rEEV/yPxGfwPkRodwEgiMUUXTx/dex+Me0wxx53S+NgUHaP7y3MGlDmg==", "shasum": "df1d4c206854369ecf3c9a4898f1b23fbd9d15d3", "tarball": "https://registry.npmjs.org/wide-align/-/wide-align-1.1.5.tgz", "fileCount": 4, "unpackedSize": 4467, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3BStCRA9TVsSAnZWagAAtlgP+wXq/HbssDME6VfLps3z\nLsr8BtjzwLwhvT29EH8nzEeZTfo8SkzPWQPKPiH5Z77Ywz/cPouhZOaWcjZb\nFfdjMeKwdVc5TJaDVEs6sR/sHYKr1G5yfJ6JSlOlMI9lqR2eEiRaQz06OClm\nW52UxK1YJ0RWYwylU2r0ft3pKATI1DVEpp7FEWr2Z/KcqAvbeF9v9anhifYz\nCCbdPKSmWmh2tr3dscwEQGOkgaKYQCvrnTs7VIRNGgio7Xoolx/M9xIm1xsJ\nQF5faTIQ8g0FL/6MJyif1Kk+hUhGe5hC4x6VOEB76B1bOTxJ9gQt8BAKpAGd\nMz3RA/49HDfcINV43cGORs6gP7qzxOOQbWMqGYYGgtwpDWe679Ck9/iYjz9C\nkPoEFCfxmIWG7ncsRlbOWp5AZvFBBld/r0rLuw9Xuw99iJRdiaL01LZquNvz\nozOYStLLbAGq4reD7kxobhvsb1B37vtnuWyEVE5d39LjnWfeC1ktiTx5jFfS\noYdL4fhr1g8oqsWUgbmnHCbdJ9Bba680t1oPQy+T69MYUcTUqldFUxNx+3UA\nXGO/O6G91+eYfdHyKMqRyy+L5qSozp6JQN0VApz27PXJGQh1WoKPm7VYb/pc\nEkwFysnmjnZHkROtpbgXgcEhl3pbRm7JuX0k7oKu1fsQ0f17bLTFCv/lUI6l\nLDL8\r\n=85+G\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCKUNaH5KfPRtAHPgB4UXf2OuZcCo1iAhITnOe3+nbgYgIhAOlyjLp5dKbvG1/OpdXEQ7WxKSuoPEbVRwmHkpu8G3Pr"}]}, "directories": {}, "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/wide-align_1.1.5_1634307421358_0.6415138028065521"}, "_hasShrinkwrap": false}}, "readme": "wide-align\n----------\n\nA wide-character aware text alignment function for use in terminals / on the\nconsole.\n\n### Usage\n\n```\nvar align = require('wide-align')\n\n// Note that if you view this on a unicode console, all of the slashes are\n// aligned. This is because on a console, all narrow characters are\n// an en wide and all wide characters are an em. In browsers, this isn't\n// held to and wide characters like \"古\" can be less than two narrow\n// characters even with a fixed width font.\n\nconsole.log(align.center('abc', 10))     // '   abc    '\nconsole.log(align.center('古古古', 10))  // '  古古古  '\nconsole.log(align.left('abc', 10))       // 'abc       '\nconsole.log(align.left('古古古', 10))    // '古古古    '\nconsole.log(align.right('abc', 10))      // '       abc'\nconsole.log(align.right('古古古', 10))   // '    古古古'\n```\n\n### Functions\n\n#### `align.center(str, length)` → `str`\n\nReturns *str* with spaces added to both sides such that that it is *length*\nchars long and centered in the spaces.\n\n#### `align.left(str, length)` → `str`\n\nReturns *str* with spaces to the right such that it is *length* chars long.\n\n### `align.right(str, length)` → `str`\n\nReturns *str* with spaces to the left such that it is *length* chars long.\n\n### Origins\n\nThese functions were originally taken from \n[cliui](https://npmjs.com/package/cliui). Changes include switching to the\nMUCH faster pad generation function from\n[lodash](https://npmjs.com/package/lodash), making center alignment pad\nboth sides and adding left alignment.\n", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "time": {"modified": "2022-06-29T01:41:37.367Z", "created": "2015-12-16T05:58:03.765Z", "1.0.0": "2015-12-16T05:58:03.765Z", "1.0.1": "2015-12-16T19:55:49.801Z", "1.0.2": "2015-12-31T07:07:16.373Z", "1.1.0": "2016-01-08T21:59:38.349Z", "1.1.1": "2017-05-10T23:21:02.976Z", "1.1.2": "2017-05-11T18:31:29.686Z", "1.1.3": "2018-05-24T21:36:50.718Z", "1.1.5": "2021-10-15T14:17:01.529Z"}, "homepage": "https://github.com/iarna/wide-align#readme", "keywords": ["wide", "double", "unicode", "cjkv", "pad", "align"], "repository": {"type": "git", "url": "git+https://github.com/iarna/wide-align.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org/"}, "bugs": {"url": "https://github.com/iarna/wide-align/issues"}, "license": "ISC", "readmeFilename": "README.md", "users": {"iarna": true}}