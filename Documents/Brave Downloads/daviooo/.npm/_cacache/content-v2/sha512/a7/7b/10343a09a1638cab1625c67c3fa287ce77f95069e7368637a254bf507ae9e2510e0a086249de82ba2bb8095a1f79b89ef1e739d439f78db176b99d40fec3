{"_id": "whatwg-url", "_rev": "85-af6347d91758b2f2a85754c2ba7576e2", "name": "whatwg-url", "dist-tags": {"latest": "14.2.0"}, "versions": {"0.0.1": {"name": "whatwg-url", "version": "0.0.1", "author": {"name": "<PERSON>", "email": "<PERSON> <<EMAIL>"}, "_id": "whatwg-url@0.0.1", "maintainers": [{"name": "sebmaster", "email": "<EMAIL>"}], "dist": {"shasum": "7c9f44c05a635f63387b030bce0e71238da9e06d", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-0.0.1.tgz", "integrity": "sha512-K8pvgp3xogxqDhfjE2PgkIkZBpPQz2Fn/amzuLHlGtaF8wJoRyHG0QjNHufdu3valu1QpzOfQFxZVt27g7cLcQ==", "signatures": [{"sig": "MEYCIQD+3+RZrYsay/JQsOqTtvvEH++mnTSbU9xLOl4QFE0b0AIhAPfGs+7gq6byGHFwsitZhfc9tTcy06o7er13n8pMBl67", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/url.js", "_from": ".", "_shasum": "7c9f44c05a635f63387b030bce0e71238da9e06d", "gitHead": "d0a5301f866909b3be599eb8aef790f6d1cde6eb", "scripts": {"lint": "jscs lib/ test/ scripts/ && jshint lib/ test/ scripts/", "test": "npm run lint && mocha", "pretest": "iojs scripts/get-latest-platform-tests.js"}, "_npmUser": {"name": "sebmaster", "email": "<EMAIL>"}, "_npmVersion": "2.11.0", "description": "An implementation of the WHATWG URL algorithm", "directories": {}, "_nodeVersion": "2.2.1", "devDependencies": {"jscs": "^1.13.0", "mocha": "^2.2.4", "jshint": "^2.7.0", "request": "^2.55.0"}}, "0.1.0": {"name": "whatwg-url", "version": "0.1.0", "author": {"name": "<PERSON>", "email": "<PERSON> <<EMAIL>"}, "_id": "whatwg-url@0.1.0", "maintainers": [{"name": "sebmaster", "email": "<EMAIL>"}], "dist": {"shasum": "b5d7fc37ae3f65a987f9367b8662624999da39f3", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-0.1.0.tgz", "integrity": "sha512-sAYOAL+h3O0uZC6CUrj7HtpNciaTV+8tPenTvkjrXTCkK3+umoQpN91tjZnWDlUOtd+jIO3TUB/OCDEEL3pA2A==", "signatures": [{"sig": "MEQCIHXHhLJr5V4pvOOELTEEQ6tudFTywSm3nGfasAj2rwNQAiA/WmJLFnRpHALHktZup6mrpkf2boYQi7zKu8jVNQLq7g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/url.js", "_from": ".", "_shasum": "b5d7fc37ae3f65a987f9367b8662624999da39f3", "gitHead": "d0a5301f866909b3be599eb8aef790f6d1cde6eb", "scripts": {"lint": "jscs lib/ test/ scripts/ && jshint lib/ test/ scripts/", "test": "npm run lint && mocha", "pretest": "iojs scripts/get-latest-platform-tests.js"}, "_npmUser": {"name": "sebmaster", "email": "<EMAIL>"}, "_npmVersion": "2.11.0", "description": "An implementation of the WHATWG URL algorithm", "directories": {}, "_nodeVersion": "2.2.1", "devDependencies": {"jscs": "^1.13.0", "mocha": "^2.2.4", "jshint": "^2.7.0", "request": "^2.55.0"}}, "0.2.0": {"name": "whatwg-url", "version": "0.2.0", "author": {"name": "<PERSON>", "email": "<PERSON> <<EMAIL>"}, "_id": "whatwg-url@0.2.0", "maintainers": [{"name": "sebmaster", "email": "<EMAIL>"}], "dist": {"shasum": "8e0c163bfbbba16f437532bc4dd1451f8da366c3", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-0.2.0.tgz", "integrity": "sha512-Zi4sXJX8v2uXjPUOv3jX8cHe7CCS4DFRiFzEnl2wwm3nx7U3wywdQCd4svhNqbKFbgQHkehumjIWBZa8yXZs3A==", "signatures": [{"sig": "MEQCHy0r8BhyJ3cqC2NOyzVo8MNhHgJJHELFEj+u3g1IENUCIQCfnEYWrQwdoee5A398YHMvqstvJ73/nYCsr0vmTxTCMA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/url.js", "_from": ".", "_shasum": "8e0c163bfbbba16f437532bc4dd1451f8da366c3", "gitHead": "7c5ef8bf8c8b0bafc58b5ecb919eb89382325a14", "scripts": {"lint": "jscs lib/ test/ scripts/ && jshint lib/ test/ scripts/", "test": "npm run lint && mocha", "pretest": "iojs scripts/get-latest-platform-tests.js", "coverage": "istanbul cover node_modules/mocha/bin/_mocha"}, "_npmUser": {"name": "sebmaster", "email": "<EMAIL>"}, "_npmVersion": "2.11.0", "description": "An implementation of the WHATWG URL algorithm", "directories": {}, "_nodeVersion": "2.2.1", "dependencies": {"guid": "0.0.12", "tr46": "~0.0.1"}, "devDependencies": {"jscs": "^1.13.0", "mocha": "^2.2.4", "jshint": "^2.7.0", "request": "^2.55.0", "istanbul": "~0.3.14"}}, "0.2.1": {"name": "whatwg-url", "version": "0.2.1", "author": {"name": "<PERSON>", "email": "<PERSON> <<EMAIL>"}, "_id": "whatwg-url@0.2.1", "maintainers": [{"name": "sebmaster", "email": "<EMAIL>"}], "dist": {"shasum": "1a871c1f0564ae7292a7f7ba876af39e782f504e", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-0.2.1.tgz", "integrity": "sha512-pvz/NLtVrOgKt9NF7sw/7aXQiUJ34Dv7TYIPzQsiVYDnzySgfod3FzmSJROmpfrFjFRYpsgBopOcP/Tzpcm+0A==", "signatures": [{"sig": "MEUCIQDkm+cq0J9NqMsDmeAhSEB7oVnaU96N0ecVzwMUMqqlPAIgaiLQbjkKYgo+Kx4zEjwsf6S1MJfavlwV4NLqV/0PRCo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/url.js", "_from": ".", "_shasum": "1a871c1f0564ae7292a7f7ba876af39e782f504e", "gitHead": "dd73f062bc913df1be52a8f7e8a0d394e1650f27", "scripts": {"lint": "jscs lib/ test/ scripts/ && jshint lib/ test/ scripts/", "test": "npm run lint && mocha", "pretest": "iojs scripts/get-latest-platform-tests.js", "coverage": "istanbul cover node_modules/mocha/bin/_mocha"}, "_npmUser": {"name": "sebmaster", "email": "<EMAIL>"}, "_npmVersion": "2.11.0", "description": "An implementation of the WHATWG URL algorithm", "directories": {}, "_nodeVersion": "2.2.1", "dependencies": {"tr46": "~0.0.1"}, "devDependencies": {"jscs": "^1.13.0", "mocha": "^2.2.4", "jshint": "^2.7.0", "request": "^2.55.0", "istanbul": "~0.3.14"}}, "0.3.0": {"name": "whatwg-url", "version": "0.3.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@0.3.0", "maintainers": [{"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "6e6e0b3d73b7c225966dd1658cf214e45d3ea958", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-0.3.0.tgz", "integrity": "sha512-0azAGuoIb2grLhGbaySQ/qY6aXM7Askd5JWxwGKkwet5a45u7lqO0BrpgUZXloG5DVuVjciCK1/pS0Ye81HMcQ==", "signatures": [{"sig": "MEUCIQDoSoPZDxcwS6bbymO6vz6QD2SSL4GeCbhTtOlebeTlNwIgYiTN4odSZPcvcn52DXc3evSh7eIx0Y997Cpy5fnKQvY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/url.js", "_from": ".", "_shasum": "6e6e0b3d73b7c225966dd1658cf214e45d3ea958", "gitHead": "acc107353ccb97a0f78798766111e781b0b30c4e", "scripts": {"lint": "jscs lib/ test/ scripts/ && jshint lib/ test/ scripts/", "test": "npm run lint && mocha", "pretest": "iojs scripts/get-latest-platform-tests.js", "coverage": "istanbul cover node_modules/mocha/bin/_mocha"}, "_npmUser": {"name": "sebmaster", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "2.12.1", "description": "An implementation of the WHATWG URL algorithm", "directories": {}, "_nodeVersion": "2.3.4", "dependencies": {"tr46": "~0.0.1"}, "devDependencies": {"jscs": "^1.13.0", "mocha": "^2.2.4", "jshint": "^2.7.0", "request": "^2.55.0", "istanbul": "~0.3.14"}}, "0.3.1": {"name": "whatwg-url", "version": "0.3.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@0.3.1", "maintainers": [{"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "912234fb70244cb3f519b2ce6f0b66e2c7a76031", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-0.3.1.tgz", "integrity": "sha512-bZ1U477rPFqsPyWmtZTfXoZX28xiO/6FCVEOwuBTicOai11b2s7kNTao6Vx3RINraYRynAmGKfl5ICVb3YmzAQ==", "signatures": [{"sig": "MEYCIQCHjwW/utwMIhLTLwriUMoIWzUem+nGxFcPncQnAgVhYwIhAKmViRf235PNvHQeC8/k08AdmF2qUQjypz3VFkiVoGH2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/url.js", "_from": ".", "_shasum": "912234fb70244cb3f519b2ce6f0b66e2c7a76031", "gitHead": "20fce5f42c3310140647ac63db8114f6b6fe1423", "scripts": {"lint": "jscs lib/ test/ scripts/ && jshint lib/ test/ scripts/", "test": "npm run lint && mocha", "pretest": "iojs scripts/get-latest-platform-tests.js", "coverage": "istanbul cover node_modules/mocha/bin/_mocha"}, "_npmUser": {"name": "sebmaster", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "2.12.1", "description": "An implementation of the WHATWG URL algorithm", "directories": {}, "_nodeVersion": "2.3.4", "dependencies": {"tr46": "~0.0.1"}, "devDependencies": {"jscs": "^1.13.0", "mocha": "^2.2.4", "jshint": "^2.7.0", "request": "^2.55.0", "istanbul": "~0.3.14"}}, "0.4.0": {"name": "whatwg-url", "version": "0.4.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@0.4.0", "maintainers": [{"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "cf0b6b216c42a81affbc6eac64264e075ca6b68c", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-0.4.0.tgz", "integrity": "sha512-s0ZOR5kgoPnNmpwuUBAYm6bUyUFE8eKcMUYuEJEeJyMBwCeIqdtgs1UnkR873ASoxo/hkxupIdeRtsdXSkHO7Q==", "signatures": [{"sig": "MEUCIQD245CCWkRtcjiDjjYJzHCvBaa6AdK+oJoH7X0pv4EOyAIgP8EpS9LU+fKPlSE1HrpDUw9Qs2wqVAdCVFRVyup4z4I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/url.js", "_from": ".", "_shasum": "cf0b6b216c42a81affbc6eac64264e075ca6b68c", "gitHead": "49253dc0341cdd9e9a4bf037ef22a86d170c50b7", "scripts": {"lint": "jscs lib/ test/ scripts/ && jshint lib/ test/ scripts/", "test": "npm run lint && mocha", "pretest": "iojs scripts/get-latest-platform-tests.js", "coverage": "istanbul cover node_modules/mocha/bin/_mocha"}, "_npmUser": {"name": "sebmaster", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "2.12.1", "description": "An implementation of the WHATWG URL algorithm", "directories": {}, "_nodeVersion": "2.3.4", "dependencies": {"tr46": "~0.0.1"}, "devDependencies": {"jscs": "^1.13.0", "mocha": "^2.2.4", "jshint": "^2.7.0", "request": "^2.55.0", "istanbul": "~0.3.14"}}, "0.4.1": {"name": "whatwg-url", "version": "0.4.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@0.4.1", "maintainers": [{"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "ab9358c434094a20ee6ebf7faf514d6a43caccf4", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-0.4.1.tgz", "integrity": "sha512-qrsS0diyUn3s5mTkJtx7sRkuVrnCthYxd5Hq04VH/Bsh1K4Q3bsujUkNkyYzl/VHMoxk2d0pJ4obOQ3KvImj0A==", "signatures": [{"sig": "MEYCIQCsLbw4QS3xGgjQkO3uTFM+lOfdl0Fh6hONBjNUu12GWAIhAIg4fBmIYJ+sHEIf+frinBp/RZig4MgebvQLoSJWkgUo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/url.js", "_from": ".", "_shasum": "ab9358c434094a20ee6ebf7faf514d6a43caccf4", "gitHead": "13049590acb88a8b230094e5934da45a312a96c2", "scripts": {"lint": "jscs lib/ test/ scripts/ && jshint lib/ test/ scripts/", "test": "npm run lint && mocha", "pretest": "iojs scripts/get-latest-platform-tests.js", "coverage": "istanbul cover node_modules/mocha/bin/_mocha"}, "_npmUser": {"name": "sebmaster", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "2.12.1", "description": "An implementation of the WHATWG URL algorithm", "directories": {}, "_nodeVersion": "2.3.4", "dependencies": {"tr46": "~0.0.1"}, "devDependencies": {"jscs": "^1.13.0", "mocha": "^2.2.4", "jshint": "^2.7.0", "request": "^2.55.0", "istanbul": "~0.3.14"}}, "0.4.2": {"name": "whatwg-url", "version": "0.4.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@0.4.2", "maintainers": [{"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "8f18660325c58c94d5916d1ac1b8ccaba315659b", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-0.4.2.tgz", "integrity": "sha512-B22dJZYoDMihLAXA/QJnmJ62Klpli5zGc5zz15HAKv1DFDt2MLyKDN/e0pobdPMEFpevjDw1rH77lwqwKX/hxg==", "signatures": [{"sig": "MEQCIEbSZDkdXGesbigouhhaOzIVdvLSs8ehL5QGVFnbEnOZAiAm6YmoIAxQA5NEBvVcF0iQDdH5GIaL/NLSJYeS568c+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/url.js", "_from": ".", "_shasum": "8f18660325c58c94d5916d1ac1b8ccaba315659b", "gitHead": "e54f2332c1a9b738cdcdc87a59601a860e045621", "scripts": {"lint": "jscs lib/ test/ scripts/ && jshint lib/ test/ scripts/", "test": "npm run lint && mocha", "pretest": "iojs scripts/get-latest-platform-tests.js", "coverage": "istanbul cover node_modules/mocha/bin/_mocha"}, "_npmUser": {"name": "sebmaster", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "2.13.2", "description": "An implementation of the WHATWG URL algorithm", "directories": {}, "_nodeVersion": "2.5.0", "dependencies": {"tr46": "~0.0.1"}, "devDependencies": {"jscs": "^1.13.0", "mocha": "^2.2.4", "jshint": "^2.7.0", "request": "^2.55.0", "istanbul": "~0.3.14"}}, "0.5.0": {"name": "whatwg-url", "version": "0.5.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@0.5.0", "maintainers": [{"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "0758fa133a953ff4653772b408f039e0306e3cf4", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-0.5.0.tgz", "integrity": "sha512-e8hD4LplLv4igoFaDUdFlpNFRku75MBza77tXO6I2BcCdUCG0+EFPuzqY7Yc7dPfrwG7sbqtXgSWJ02xYMmtZg==", "signatures": [{"sig": "MEUCICwO40NYzYY6NByCYMIX+KEKMjKAhu1eAqDICTJx5qIAAiEA7HbCUdfXOdrPXeQCMgJKliMMp+yDqhahsz3UAwzas3s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/url.js", "_from": ".", "_shasum": "0758fa133a953ff4653772b408f039e0306e3cf4", "gitHead": "57fba4f5204e6f686b4a225d1497a69f79c51066", "scripts": {"lint": "jscs lib/ test/ scripts/ && jshint lib/ test/ scripts/", "test": "npm run lint && mocha", "pretest": "iojs scripts/get-latest-platform-tests.js", "coverage": "istanbul cover node_modules/mocha/bin/_mocha"}, "_npmUser": {"name": "sebmaster", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "2.13.2", "description": "An implementation of the WHATWG URL algorithm", "directories": {}, "_nodeVersion": "2.5.0", "dependencies": {"tr46": "~0.0.1"}, "devDependencies": {"jscs": "^1.13.0", "mocha": "^2.2.4", "jshint": "^2.7.0", "request": "^2.55.0", "istanbul": "~0.3.14"}}, "0.6.0": {"name": "whatwg-url", "version": "0.6.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@0.6.0", "maintainers": [{"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "9dd9b57e9623b7bff264d21a2c7e4783ccbf36ea", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-0.6.0.tgz", "integrity": "sha512-HqT1/VrIGotonXrfQZOrmLgE4JMLMZOBnFksMEgoCUWcXQ6qFiQ5ymXc34/pXC8sS8MWM5R2vSZSJ5nPk7LFlw==", "signatures": [{"sig": "MEYCIQDHQgPwGsHzbUCufDgspVkFHH2CnxH49Au2YtHdkVJriQIhAPx1aGBolrMIz5EgM0t7toRYmmhsdlnLyM90A5D6ezMm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/url.js", "_from": ".", "_shasum": "9dd9b57e9623b7bff264d21a2c7e4783ccbf36ea", "gitHead": "14129c5759b67c0f9e2c6e4323fc2271878e0e14", "scripts": {"lint": "jscs lib/ test/ scripts/ && jshint lib/ test/ scripts/", "test": "npm run lint && mocha", "pretest": "iojs scripts/get-latest-platform-tests.js", "coverage": "istanbul cover node_modules/mocha/bin/_mocha"}, "_npmUser": {"name": "sebmaster", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "2.13.2", "description": "An implementation of the WHATWG URL algorithm", "directories": {}, "_nodeVersion": "2.5.0", "dependencies": {"tr46": "~0.0.1"}, "devDependencies": {"jscs": "^1.13.0", "mocha": "^2.2.4", "jshint": "^2.7.0", "request": "^2.55.0", "istanbul": "~0.3.14"}}, "0.6.1": {"name": "whatwg-url", "version": "0.6.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@0.6.1", "maintainers": [{"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "454168d5efb8f53b6760f84a23d50e50ea10adcf", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-0.6.1.tgz", "integrity": "sha512-O+524FwopgAK8HoNBamGeGGNG1nsJJG6DHjBYxmi2HtzEa5NZGkN84P2tDBSOIquAK6KA69pe2lmdSYLQLSuyw==", "signatures": [{"sig": "MEUCIC1ia9ya01IwfdtwVSSR/Z8E58E6bHaOdDBDqAkCmme9AiEA5Xm1Wx9PwR/JtUKWQUCfKAL20pvv1GhfPXsyBawK8vM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/url.js", "_from": ".", "_shasum": "454168d5efb8f53b6760f84a23d50e50ea10adcf", "gitHead": "83dcf28cd02c96ef48ccb4015f921a6b97c1533e", "scripts": {"lint": "jscs lib/ test/ scripts/ && jshint lib/ test/ scripts/", "test": "npm run lint && mocha", "pretest": "iojs scripts/get-latest-platform-tests.js", "coverage": "istanbul cover node_modules/mocha/bin/_mocha"}, "_npmUser": {"name": "sebmaster", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "2.13.2", "description": "An implementation of the WHATWG URL algorithm", "directories": {}, "_nodeVersion": "2.5.0", "dependencies": {"tr46": "~0.0.1"}, "devDependencies": {"jscs": "^1.13.0", "mocha": "^2.2.4", "jshint": "^2.7.0", "request": "^2.55.0", "istanbul": "~0.3.14"}}, "0.6.2": {"name": "whatwg-url", "version": "0.6.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@0.6.2", "maintainers": [{"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "758c0596a2b0b93cec96667800359946f022af5f", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-0.6.2.tgz", "integrity": "sha512-IZx0+al5gXMZutxl+RYRH0gQY8rbKFs9hADw55ZGAHGAo9C9JTSdnX04kBkp0sZqadKTTIgezTVJ8drn+giKOQ==", "signatures": [{"sig": "MEUCIG+EQkSkrJkoIujunGG2VjVlaOahcc/AzB9ExV7J2SjbAiEA8TBvg3FiNOlUh1/tIDYxC+5X+HgG4ZLSn5ePD9HY4zY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/url.js", "_from": ".", "_shasum": "758c0596a2b0b93cec96667800359946f022af5f", "gitHead": "660e4e8c9365c51958726b3e504584bc75dbff8f", "scripts": {"lint": "jscs lib/ test/ scripts/ && jshint lib/ test/ scripts/", "test": "npm run lint && mocha", "pretest": "iojs scripts/get-latest-platform-tests.js", "coverage": "istanbul cover node_modules/mocha/bin/_mocha"}, "_npmUser": {"name": "sebmaster", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "2.13.2", "description": "An implementation of the WHATWG URL algorithm", "directories": {}, "_nodeVersion": "2.5.0", "dependencies": {"tr46": "~0.0.1"}, "devDependencies": {"jscs": "^1.13.0", "mocha": "^2.2.4", "jshint": "^2.7.0", "request": "^2.55.0", "istanbul": "~0.3.14"}}, "0.6.4": {"name": "whatwg-url", "version": "0.6.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@0.6.4", "maintainers": [{"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "6a60829d03104b36645045726b2e7fcb557f674c", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-0.6.4.tgz", "integrity": "sha512-ZlCDJ4DXQIqWA8dyyX+ENRT1WrXyLnFm39iKBwY2ioWzUnTG1xeOM3G2JdHaPiyYFdG1xkRdmr8f8iSG0gfEoA==", "signatures": [{"sig": "MEYCIQCHqX9hQ/2fT7ASXpTHePAIWpE3KAQJo8q9z/jhCpAP5QIhAM5RnMZNILA4o+kvgfLpo3PA81htIj3S/YREqI1Bzb3r", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/url.js", "_from": ".", "_shasum": "6a60829d03104b36645045726b2e7fcb557f674c", "gitHead": "0b26d11e44891cd82ae45f8fb04f5fc4c71ee7c8", "scripts": {"lint": "jscs src/ test/ scripts/ && jshint src/ test/ scripts/", "test": "npm run lint && mocha", "build": "iojs bin/transform.js", "pretest": "iojs scripts/get-latest-platform-tests.js && npm run build", "coverage": "istanbul cover node_modules/mocha/bin/_mocha", "prepublish": "npm run build"}, "_npmUser": {"name": "sebmaster", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "2.13.5", "description": "An implementation of the WHATWG URL algorithm", "directories": {}, "_nodeVersion": "2.5.0", "dependencies": {"tr46": "~0.0.1"}, "devDependencies": {"jscs": "^1.13.0", "mocha": "^2.2.4", "jshint": "^2.7.0", "recast": "~0.10.29", "request": "^2.55.0", "istanbul": "~0.3.14"}}, "0.6.5": {"name": "whatwg-url", "version": "0.6.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@0.6.5", "maintainers": [{"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "a6f8f1a98fd4a3bf6d85af6744745c16c4a9bf79", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-0.6.5.tgz", "integrity": "sha512-T15Ity4AgFUah8lbBUGXmvr546hkcx4oOdJc6srA/ciVJq3TMgqMA/Qm7uc0aJ+qogP5unz4Vf7BcpU2GWvCCA==", "signatures": [{"sig": "MEUCICz1SlFl3quWuec5HwgpjxgmcFBHmSKBfKPDyIFqGC5wAiEAgnpwp78jXwKNg6EJMk43GZGOf5FoFQMIwL4rMBi0oi4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/url.js", "_from": ".", "_shasum": "a6f8f1a98fd4a3bf6d85af6744745c16c4a9bf79", "gitHead": "765cb383fe05b04d444ca59dd166e4f7b199ecbe", "scripts": {"lint": "jscs lib/ test/ scripts/ && jshint lib/ test/ scripts/", "test": "npm run lint && mocha", "pretest": "iojs scripts/get-latest-platform-tests.js", "coverage": "istanbul cover node_modules/mocha/bin/_mocha"}, "_npmUser": {"name": "sebmaster", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "2.13.5", "description": "An implementation of the WHATWG URL algorithm", "directories": {}, "_nodeVersion": "2.5.0", "dependencies": {"tr46": "~0.0.1"}, "devDependencies": {"jscs": "^1.13.0", "mocha": "^2.2.4", "jshint": "^2.7.0", "request": "^2.55.0", "istanbul": "~0.3.14"}}, "1.0.0": {"name": "whatwg-url", "version": "1.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@1.0.0", "maintainers": [{"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "8cde8928f56c0e830fed076818fb54e18a25bbb0", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-1.0.0.tgz", "integrity": "sha512-HwA5TbH+yrS2ilN6PgP7ZuqG260dUvGXGXtVSLr0/bI3EkohisO7v5K0K3jgOBZAcG8GGX8cfEcCdXB5tg12oQ==", "signatures": [{"sig": "MEQCIARhl+IZ3jgBeNoDgzXGTSaQOj0MpO9mAwTTcmXvMBPYAiBdpDIOdCcC5M0LXaMnz/mS1p+g4EqTCyg0GVSeazViuw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/public-api.js", "_from": ".", "_shasum": "8cde8928f56c0e830fed076818fb54e18a25bbb0", "gitHead": "143e69a83770ca33ad3145beff722b3b0abd398d", "scripts": {"lint": "jscs src/ test/ scripts/ && jshint src/ test/ scripts/", "test": "mocha", "build": "node scripts/transform.js && node scripts/convert-idl.js", "pretest": "node scripts/get-latest-platform-tests.js && npm run build", "coverage": "istanbul cover node_modules/mocha/bin/_mocha", "prepublish": "npm run build"}, "_npmUser": {"name": "sebmaster", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "An implementation of the WHATWG URL algorithm", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}, "devDependencies": {"jscs": "^1.13.0", "mocha": "^2.2.4", "jshint": "^2.7.0", "recast": "~0.10.29", "request": "^2.55.0", "istanbul": "~0.3.14", "webidl2js": "^3.0.2"}}, "1.0.1": {"name": "whatwg-url", "version": "1.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@1.0.1", "maintainers": [{"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "2ad742fc419d671026376f84c80fe4f69f55ccc7", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-1.0.1.tgz", "integrity": "sha512-TZ62zU14IZiYVfMxWZSa4ZHAIiJjwzhlBO6VLYexjC3uKBRJIOIDbew/tfU/ZTK413REcR2/vsHmEouew4bBRQ==", "signatures": [{"sig": "MEUCIQDGnfd2wCnIGSnM5Qb9D2hoqNUvVLnKtOEZZETf9Bjw/gIgapiSrTrtnSjKDMUcfSDlw/tDdMuqYsqnukRouajL31A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/public-api.js", "_from": ".", "_shasum": "2ad742fc419d671026376f84c80fe4f69f55ccc7", "gitHead": "51a93d1497dfa3666b5afe1ad3dc5e69ea3c8052", "scripts": {"lint": "jscs src/ test/ scripts/ && jshint src/ test/ scripts/", "test": "mocha", "build": "node scripts/transform.js && node scripts/convert-idl.js", "pretest": "node scripts/get-latest-platform-tests.js && npm run build", "coverage": "istanbul cover node_modules/mocha/bin/_mocha", "prepublish": "npm run build"}, "_npmUser": {"name": "sebmaster", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "An implementation of the WHATWG URL algorithm", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}, "devDependencies": {"jscs": "^1.13.0", "mocha": "^2.2.4", "jshint": "^2.7.0", "recast": "~0.10.29", "request": "^2.55.0", "istanbul": "~0.3.14", "webidl2js": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url-1.0.1.tgz_1455127633394_0.16841055802069604", "host": "packages-5-east.internal.npmjs.com"}}, "2.0.0": {"name": "whatwg-url", "version": "2.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@2.0.0", "maintainers": [{"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "5980883353c93607113cbe8c1ab5a58a656070e2", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-2.0.0.tgz", "integrity": "sha512-SaKBIa6nyugRsA51xTDOG0UpmRBLijNPM1Gbg69bV1pfhMnSsBooj2KsKCG3bYgFGbK0BgNfrhR2w+4xv9XKKw==", "signatures": [{"sig": "MEYCIQChpT9Ti5m9YIt1+qxhnNTgz+d8HwYuVi9WCcPkTB1lhAIhALJOOr9sPRYY5+Zd8h9K1VK+FkeG63OLhci1/q7QiYcO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/public-api.js", "_from": ".", "_shasum": "5980883353c93607113cbe8c1ab5a58a656070e2", "gitHead": "93b140f24699397d3f39cdb49b6d7ece021d6375", "scripts": {"lint": "eslint .", "test": "mocha", "build": "node scripts/transform.js && node scripts/convert-idl.js", "pretest": "node scripts/get-latest-platform-tests.js && npm run build", "coverage": "istanbul cover node_modules/mocha/bin/_mocha", "prepublish": "npm run build"}, "_npmUser": {"name": "sebmaster", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "3.8.3", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "5.10.0", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}, "devDependencies": {"mocha": "^2.2.4", "eslint": "^2.6.0", "recast": "~0.10.29", "request": "^2.55.0", "istanbul": "~0.3.14", "webidl2js": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url-2.0.0.tgz_1459979359048_0.7188771087676287", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.1": {"name": "whatwg-url", "version": "2.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@2.0.1", "maintainers": [{"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "5396b2043f020ee6f704d9c45ea8519e724de659", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-2.0.1.tgz", "integrity": "sha512-sX+FT4N6iR0ZiqGqyDEKklyfMGR99zvxZD+LQ8IGae5uVGswQ7DOeLPB5KgJY8FzkwSzwqOXLQeVQvtOTSQU9Q==", "signatures": [{"sig": "MEUCIG0w5ueYmA/XKVNHXiybdrFjQFlFqlOpwUcMF3ziN+q4AiEA+XyUDnHRSUlia7GFL3K7fj2pnpTQl389HF6hQGJHo3U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/public-api.js", "_from": ".", "_shasum": "5396b2043f020ee6f704d9c45ea8519e724de659", "gitHead": "8a94299bb044e46c553e8e0d90ecf338102c2296", "scripts": {"lint": "eslint .", "test": "mocha", "build": "node scripts/transform.js && node scripts/convert-idl.js", "pretest": "node scripts/get-latest-platform-tests.js && npm run build", "coverage": "istanbul cover node_modules/mocha/bin/_mocha", "prepublish": "npm run build"}, "_npmUser": {"name": "sebmaster", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "3.8.3", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "5.10.1", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}, "devDependencies": {"mocha": "^2.2.4", "eslint": "^2.6.0", "recast": "~0.10.29", "request": "^2.55.0", "istanbul": "~0.3.14", "webidl2js": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url-2.0.1.tgz_1460758356720_0.45845040353015065", "host": "packages-16-east.internal.npmjs.com"}}, "3.0.0": {"name": "whatwg-url", "version": "3.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@3.0.0", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "b9033c50c7ce763e91d78777ce825a6d7f56dac5", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-3.0.0.tgz", "integrity": "sha512-pv67jGw+Guwm0bIzf3WVs26CYxoj53QAS60W0v+5d5WnPt1tSe1YP6pmATa8mYfzIj2kC0HXKKJd5SSJ55hHRw==", "signatures": [{"sig": "MEUCIQDOdqBTvK2KU1Zdc9pF+lUUaVOziN14l2yp8mkNXZkJWgIgdzbfeXOr8Bwa9aEgD4cX0eh9ONugOI9lwdHicYRH838=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/public-api.js", "_from": ".", "_shasum": "b9033c50c7ce763e91d78777ce825a6d7f56dac5", "gitHead": "15547ead082b2434331a73d003a71e6923f5783e", "scripts": {"lint": "eslint .", "test": "mocha", "build": "node scripts/transform.js && node scripts/convert-idl.js", "pretest": "node scripts/get-latest-platform-tests.js && npm run build", "coverage": "istanbul cover node_modules/mocha/bin/_mocha", "prepublish": "npm run build"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "6.0.0", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}, "devDependencies": {"mocha": "^2.2.4", "eslint": "^2.6.0", "recast": "~0.10.29", "request": "^2.55.0", "istanbul": "~0.4.3", "webidl2js": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url-3.0.0.tgz_1464463943614_0.6469055290799588", "host": "packages-12-west.internal.npmjs.com"}}, "3.1.0": {"name": "whatwg-url", "version": "3.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@3.1.0", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "7bdcae490f921aef6451fb6739ec6bbd8e907bf6", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-3.1.0.tgz", "integrity": "sha512-eXfeQgVJ6TM0+Fa3Kw36p70QP6lpxAVuleQi7Y9dtHYGvshVB5dfbYx1lzDE+iCujjK0f/neAi3jtMhMg9jlVA==", "signatures": [{"sig": "MEUCIDVd3E3+FTBXS19f8zt9kajMeA5uvmT04P4wYP+v/vHLAiEAuqGSSMaUfLW97RT+eVYD5Padst1pOsKIHkdizwA9SPE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/public-api.js", "_from": ".", "_shasum": "7bdcae490f921aef6451fb6739ec6bbd8e907bf6", "gitHead": "96da223b1b36c9028af67dea5911f993420968ae", "scripts": {"lint": "eslint .", "test": "mocha", "build": "node scripts/transform.js && node scripts/convert-idl.js", "pretest": "node scripts/get-latest-platform-tests.js && npm run build", "coverage": "istanbul cover node_modules/mocha/bin/_mocha", "prepublish": "npm run build"}, "_npmUser": {"name": "sebmaster", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "7.1.0", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}, "devDependencies": {"mocha": "^2.2.4", "eslint": "^2.6.0", "recast": "~0.10.29", "request": "^2.55.0", "istanbul": "~0.4.3", "webidl2js": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url-3.1.0.tgz_1479612162307_0.29943900532089174", "host": "packages-18-east.internal.npmjs.com"}}, "4.0.0": {"name": "whatwg-url", "version": "4.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@4.0.0", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "5be362f0b6e2f8760f7260df6e0e1df536f5479c", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-4.0.0.tgz", "integrity": "sha512-aug4iRR91rrvi4iq2E/oz8Qw/DpejG0WQk8bDiEXZvl6dNojrb3GRMauJ4St/oQiYu0zDwJx4F21/orDUFaCgg==", "signatures": [{"sig": "MEQCIEAC+EMabXo/NEQ7p/iWAgRak2jJ8D0irv54lU3d/3oLAiBvAr7tCexXTaTzqL1NzGkWp/C6T4TPTJ90EDktv2fQ7w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/public-api.js", "_from": ".", "_shasum": "5be362f0b6e2f8760f7260df6e0e1df536f5479c", "gitHead": "ab443c4b4b581fce6661e883f3844bbbafedf451", "scripts": {"lint": "eslint .", "test": "mocha", "build": "node scripts/transform.js && node scripts/convert-idl.js", "pretest": "node scripts/get-latest-platform-tests.js && npm run build", "coverage": "istanbul cover node_modules/mocha/bin/_mocha", "prepublish": "npm run build"}, "_npmUser": {"name": "sebmaster", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "7.1.0", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}, "devDependencies": {"mocha": "^2.2.4", "eslint": "^2.6.0", "recast": "~0.10.29", "request": "^2.55.0", "istanbul": "~0.4.3", "webidl2js": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url-4.0.0.tgz_1479612610671_0.005820423131808639", "host": "packages-12-west.internal.npmjs.com"}}, "4.1.0": {"name": "whatwg-url", "version": "4.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@4.1.0", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "7a2863c15805020b37d0c17ed4091f7ce9d1c634", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-4.1.0.tgz", "integrity": "sha512-u7mGmpsNAO+WvjNywWGoAg5YGeDwStGjFiiVVPULWjjyth4f57zNqZfOhV2CW77wvO8OA/zkfdM7Uw1aH1V88Q==", "signatures": [{"sig": "MEYCIQD4tuCzf8nTxmxXguRtnvF/+GMBu2TXwgRjrtF5Kjln0QIhANWWDpS6LjyBTv4XU3fcbslqcAVtHT5sYQkWinvwjlWS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/public-api.js", "_from": ".", "_shasum": "7a2863c15805020b37d0c17ed4091f7ce9d1c634", "gitHead": "bc1fe214fac5da6b418ab7d46cae7ca879d6d251", "scripts": {"lint": "eslint .", "test": "mocha", "build": "node scripts/transform.js && node scripts/convert-idl.js", "pretest": "node scripts/get-latest-platform-tests.js && npm run build", "coverage": "istanbul cover node_modules/mocha/bin/_mocha", "prepublish": "npm run build"}, "_npmUser": {"name": "sebmaster", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "6.9.2", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}, "devDependencies": {"mocha": "^2.2.4", "eslint": "^2.6.0", "recast": "~0.10.29", "request": "^2.55.0", "istanbul": "~0.4.3", "webidl2js": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url-4.1.0.tgz_1481305588580_0.8640300696715713", "host": "packages-12-west.internal.npmjs.com"}}, "4.1.1": {"name": "whatwg-url", "version": "4.1.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@4.1.1", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "567074923352de781e3500d64a86aa92a971b4a4", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-4.1.1.tgz", "integrity": "sha512-ZfgzNjnWCIp2mlqGlZrCOIDunJfPikANOfIa/CeTwdL7pgxKVbY94urL9+pIxe9Jt836KYvRKOC6XygC9cki4Q==", "signatures": [{"sig": "MEQCICiR12sfj8RVUZACSJDQF0ahsWJzOp3+RndJdt7DcgFDAiAvcwxWW0QVWXM5mbQOZoBHOBHK0Qy65+hOaW/T2H+sLg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/public-api.js", "_from": ".", "_shasum": "567074923352de781e3500d64a86aa92a971b4a4", "gitHead": "43e343837b713353d7bde690c407f571b7c670cb", "scripts": {"lint": "eslint .", "test": "mocha", "build": "node scripts/transform.js && node scripts/convert-idl.js", "pretest": "node scripts/get-latest-platform-tests.js && npm run build", "coverage": "istanbul cover node_modules/mocha/bin/_mocha", "prepublish": "npm run build"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "7.2.1", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}, "devDependencies": {"mocha": "^2.2.4", "eslint": "^2.6.0", "recast": "~0.10.29", "request": "^2.55.0", "istanbul": "~0.4.3", "webidl2js": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url-4.1.1.tgz_1482106170404_0.012135676108300686", "host": "packages-12-west.internal.npmjs.com"}}, "4.2.0": {"name": "whatwg-url", "version": "4.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@4.2.0", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "abf1a3f5ff4bc2005b3f0c2119382631789d8e44", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-4.2.0.tgz", "integrity": "sha512-YtaURl3J+k2cqFC881a7Toszc5YNqF+PxRisqYvayMqQ9g+R8tuLmTB1py+8kWVIaCmn0gTCkeImHf441rYL1Q==", "signatures": [{"sig": "MEQCIC8x1/cTPslsSrLilMw04HuQ5pElVd2rNGkwdnsiU525AiBT1+XVHGkr+3tbIf3VwFW69ipEHX3oQSFZ6A8yeJ+3nQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/public-api.js", "_from": ".", "_shasum": "abf1a3f5ff4bc2005b3f0c2119382631789d8e44", "gitHead": "2556e463883639ea74e61d518bf53fb5816b1099", "scripts": {"lint": "eslint .", "test": "mocha", "build": "node scripts/transform.js && node scripts/convert-idl.js", "pretest": "node scripts/get-latest-platform-tests.js && npm run build", "coverage": "istanbul cover node_modules/mocha/bin/_mocha", "prepublish": "npm run build"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "7.2.1", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}, "devDependencies": {"mocha": "^2.2.4", "eslint": "^2.6.0", "recast": "~0.10.29", "request": "^2.55.0", "istanbul": "~0.4.3", "webidl2js": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url-4.2.0.tgz_1483481566169_0.0977134327404201", "host": "packages-18-east.internal.npmjs.com"}}, "4.3.0": {"name": "whatwg-url", "version": "4.3.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@4.3.0", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "92aaee21f4f2a642074357d70ef8500a7cbb171a", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-4.3.0.tgz", "integrity": "sha512-//VBXpZ8ZHPIkXbtwAuzz4arWsGvGQpWQRRSkGsMuJtkW9UuASFbyXn4CluHEAHXbJ6Px53yLYHf6LNepDIPbA==", "signatures": [{"sig": "MEQCIE1HheCIrAsp6vxIjFpiCK2uQCkq4o1DbDHUW91XEOK8AiBJEaW2Movh5Bj9ChAOKLUkG2uZcYvJYt59+Z+EHvFQEA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/public-api.js", "_from": ".", "_shasum": "92aaee21f4f2a642074357d70ef8500a7cbb171a", "gitHead": "0f228168cb3aa31366c182f4114ec4d821fb351d", "scripts": {"lint": "eslint .", "test": "mocha", "build": "node scripts/transform.js && node scripts/convert-idl.js", "pretest": "node scripts/get-latest-platform-tests.js && npm run build", "coverage": "istanbul cover node_modules/mocha/bin/_mocha", "prepublish": "npm run build"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "7.0.0", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}, "devDependencies": {"mocha": "^2.2.4", "eslint": "^2.6.0", "recast": "~0.10.29", "request": "^2.55.0", "istanbul": "~0.4.3", "webidl2js": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url-4.3.0.tgz_1484181629024_0.38307241164147854", "host": "packages-12-west.internal.npmjs.com"}}, "4.4.0": {"name": "whatwg-url", "version": "4.4.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@4.4.0", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "594f95781545c13934a62db40897c818cafa2e04", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-4.4.0.tgz", "integrity": "sha512-d9l8wkozQjACGpW+4fKdiKpYbbAAZ/9ywiDh/6hZMXgxHjiSbnli6ZBrTXe7nlm82yx3NIuOJR82whGdUCFTBg==", "signatures": [{"sig": "MEYCIQCOjqq/CI/miP+ZRpULl2COjnM3jDMidWgiPowMd3vw8gIhAO0il98t8Xh58S/Zrj6T1VE2CAxVa+hRsWSxmApPJvos", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/public-api.js", "_from": ".", "_shasum": "594f95781545c13934a62db40897c818cafa2e04", "gitHead": "8d2df0a2dfe24cbabf72bb4dbd3088481f01236f", "scripts": {"lint": "eslint .", "test": "mocha", "build": "node scripts/transform.js && node scripts/convert-idl.js", "pretest": "node scripts/get-latest-platform-tests.js && npm run build", "coverage": "istanbul cover node_modules/mocha/bin/_mocha", "prepublish": "npm run build"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "7.0.0", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}, "devDependencies": {"mocha": "^2.2.4", "eslint": "^2.6.0", "recast": "~0.10.29", "request": "^2.55.0", "istanbul": "~0.4.3", "webidl2js": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url-4.4.0.tgz_1487277003155_0.2883491162210703", "host": "packages-12-west.internal.npmjs.com"}}, "4.5.0": {"name": "whatwg-url", "version": "4.5.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@4.5.0", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "79bb6f0e370a4dda1cbc8f3062a490cf8bbb09ea", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-4.5.0.tgz", "integrity": "sha512-62+1Y/3MfBoWowCheMuVuMqaAn/lcJKQ9KzHaol9lmi9M5XVuEzkMJBKRDmV0wYIy7uN4G0Mh7wbTQSEoUajYQ==", "signatures": [{"sig": "MEUCIQDyPYYa7N4cXRvVnZ1uEKPMr4l4JKtOog4JphjncWElwAIgQojPgzoVrR90JmhPVRWyGSeusPdoLZO8fhKBAB5DMPE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/public-api.js", "_from": ".", "_shasum": "79bb6f0e370a4dda1cbc8f3062a490cf8bbb09ea", "gitHead": "f280010cdc26b016d9c8f785d0ac61fea41e1f39", "scripts": {"lint": "eslint .", "test": "mocha", "build": "node scripts/transform.js && node scripts/convert-idl.js", "pretest": "node scripts/get-latest-platform-tests.js && npm run build", "coverage": "istanbul cover node_modules/mocha/bin/_mocha", "prepublish": "npm run build"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "7.0.0", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}, "devDependencies": {"mocha": "^2.2.4", "eslint": "^2.6.0", "recast": "~0.10.29", "request": "^2.55.0", "istanbul": "~0.4.3", "webidl2js": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url-4.5.0.tgz_1487785438418_0.1314921067096293", "host": "packages-18-east.internal.npmjs.com"}}, "4.5.1": {"name": "whatwg-url", "version": "4.5.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@4.5.1", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "ba634f630ff0778212c52ea9055d2d061380b1bb", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-4.5.1.tgz", "integrity": "sha512-B8mgGfhn/3JIucBVRUfjlQj/3YLVjIOGzck3KNqzhekg1wybOIW3ZflFaLc9SqNR/JE4S/dXbYju7f12cyYHnw==", "signatures": [{"sig": "MEUCIEEDQh3ydYADTJ9ia7ONeFKUPgP7FNFZrX9OQOQbg6gRAiEA/PXtb6ZEAtecPtt5+IHJqso4rjZ8AgnmSTYhJnYxQpQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/public-api.js", "_from": ".", "files": ["lib/"], "_shasum": "ba634f630ff0778212c52ea9055d2d061380b1bb", "gitHead": "0a1403bcc0a66ec6dc3efedf3c3947ac690a5c24", "scripts": {"lint": "eslint .", "test": "mocha", "build": "node scripts/transform.js && node scripts/convert-idl.js", "pretest": "node scripts/get-latest-platform-tests.js && npm run build", "coverage": "istanbul cover node_modules/mocha/bin/_mocha", "prepublish": "npm run build"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "7.5.0", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}, "devDependencies": {"mocha": "^2.2.4", "eslint": "^2.6.0", "recast": "~0.10.29", "request": "^2.55.0", "istanbul": "~0.4.3", "webidl2js": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url-4.5.1.tgz_1489346471258_0.9136115643195808", "host": "packages-18-east.internal.npmjs.com"}}, "4.6.0": {"name": "whatwg-url", "version": "4.6.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@4.6.0", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "ef98da442273be04cf9632e176f257d2395a1ae4", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-4.6.0.tgz", "integrity": "sha512-G6gmP0dV3JEmFD7OpSMkxDirRHGHTv/U9ZfqpOS38AJXFsnU0s+PKOiTnOHBsK1w4VPXQNZBeIgInQm0zzoz8A==", "signatures": [{"sig": "MEQCIEv+iMZDVvIfTWj5QarZhPEm9NRscOCdqWzuNsUsPhDPAiAI5qG5nNt1pMjka7fMRyEWoXMTY3PvP/7aKilYZg5YaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/public-api.js", "_from": ".", "files": ["lib/"], "_shasum": "ef98da442273be04cf9632e176f257d2395a1ae4", "gitHead": "084105d1b524c58d816a57602004c9d1403e99c5", "scripts": {"lint": "eslint .", "test": "mocha", "build": "node scripts/transform.js && node scripts/convert-idl.js", "pretest": "node scripts/get-latest-platform-tests.js && npm run build", "coverage": "istanbul cover node_modules/mocha/bin/_mocha", "prepublish": "npm run build"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "7.5.0", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}, "devDependencies": {"mocha": "^2.2.4", "eslint": "^2.6.0", "recast": "~0.10.29", "request": "^2.55.0", "istanbul": "~0.4.3", "webidl2js": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url-4.6.0.tgz_1489608567954_0.9198021700140089", "host": "packages-12-west.internal.npmjs.com"}}, "4.7.0": {"name": "whatwg-url", "version": "4.7.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@4.7.0", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "202035ac1955b087cdd20fa8b58ded3ab1cd2af5", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-4.7.0.tgz", "integrity": "sha512-qXGcxwrigpFbkkngFH6EdO6Qt2gb/4zymaHIRqDJTGyRLeRd3KtDH0aCMkvRLXJMXENquxDt0Ld9bylg88Ekrw==", "signatures": [{"sig": "MEQCIGV6wTxO6yYV12MRMi186pO4MbrZE6wrLw78blhkaMCeAiBFlN5WefhvR8Pz2QdXUyOMmqcoJLppyg//ZjDlpc8LMQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/public-api.js", "_from": ".", "files": ["lib/"], "_shasum": "202035ac1955b087cdd20fa8b58ded3ab1cd2af5", "gitHead": "6e0f00c6c020a81c9b918e64c757ba6a727743f6", "scripts": {"lint": "eslint .", "test": "mocha", "build": "node scripts/transform.js && node scripts/convert-idl.js", "pretest": "node scripts/get-latest-platform-tests.js && npm run build", "coverage": "istanbul cover node_modules/mocha/bin/_mocha", "prepublish": "npm run build"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "4.5.0", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "7.2.0", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}, "devDependencies": {"mocha": "^2.2.4", "eslint": "^2.6.0", "recast": "~0.10.29", "request": "^2.55.0", "istanbul": "~0.4.3", "webidl2js": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url-4.7.0.tgz_1491196642007_0.7352425667922944", "host": "packages-18-east.internal.npmjs.com"}}, "4.7.1": {"name": "whatwg-url", "version": "4.7.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@4.7.1", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "df4dc2e3f25a63b1fa5b32ed6d6c139577d690de", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-4.7.1.tgz", "integrity": "sha512-7rwLuNiZQbujtIu7Ibp7mq9X/Swqq90X0+zOWESoViRYcIOoQWtThlRX9K2YQHZLwGZv4CBOdTc4N3/SzAdb6w==", "signatures": [{"sig": "MEYCIQDhY9YHHMxcKTejM+rosKAhcd5/wwnFl+qT5l4DNdDRoQIhAPhLvjBi6WCzKLdYUcRz7+8smkcYrPGNsqix6YC9hPOn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/public-api.js", "_from": ".", "files": ["lib/"], "_shasum": "df4dc2e3f25a63b1fa5b32ed6d6c139577d690de", "gitHead": "08f7c219053825e78aabeb622e9eefeab499a802", "scripts": {"lint": "eslint .", "test": "mocha", "build": "node scripts/transform.js && node scripts/convert-idl.js", "pretest": "node scripts/get-latest-platform-tests.js && npm run build", "coverage": "istanbul cover node_modules/mocha/bin/_mocha", "prepublish": "npm run build"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "7.0.0", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}, "devDependencies": {"mocha": "^2.2.4", "eslint": "^2.6.0", "recast": "~0.10.29", "request": "^2.55.0", "istanbul": "~0.4.3", "webidl2js": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url-4.7.1.tgz_1493066682277_0.8912100386805832", "host": "packages-12-west.internal.npmjs.com"}}, "4.8.0": {"name": "whatwg-url", "version": "4.8.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@4.8.0", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "d2981aa9148c1e00a41c5a6131166ab4683bbcc0", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-4.8.0.tgz", "integrity": "sha512-nUvUPuenPFtPfy/X+dAYh/TfRbTBlnXTM5iIfLseJFkkQewmpG9pGR6i87E9qL+lZaJzv+99kkQWoGOtLfkZQQ==", "signatures": [{"sig": "MEQCIHqX3Dpk8Cw96DQs+UspQSQojDvUQRPtI6wpomEwWyIzAiBl6UN7HJHReRGlt3oTciPERVfMw1ocl2p7uEV3vK/hFQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/public-api.js", "_from": ".", "files": ["lib/"], "_shasum": "d2981aa9148c1e00a41c5a6131166ab4683bbcc0", "gitHead": "0a7f2fbd14aa99a1bcbebe5449e3ecc839af1281", "scripts": {"lint": "eslint .", "test": "mocha", "build": "node scripts/transform.js && node scripts/convert-idl.js", "pretest": "node scripts/get-latest-platform-tests.js && npm run build", "coverage": "istanbul cover node_modules/mocha/bin/_mocha", "prepublish": "npm run build"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "7.0.0", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}, "devDependencies": {"mocha": "^2.2.4", "eslint": "^2.6.0", "recast": "~0.10.29", "request": "^2.55.0", "istanbul": "~0.4.3", "webidl2js": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url-4.8.0.tgz_1494442601895_0.890925264917314", "host": "packages-18-east.internal.npmjs.com"}}, "5.0.0": {"name": "whatwg-url", "version": "5.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@5.0.0", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "966454e8765462e37644d3626f6742ce8b70965d", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-5.0.0.tgz", "integrity": "sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==", "signatures": [{"sig": "MEUCIQDR7ZFHw4LM3lzfaljmB/si3Id3kHynTIIeegmgHzKCWgIgTeFerrEkdamDyZ3rv4uMlngS4bKIAMTNDs0tbfHvD/M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/public-api.js", "_from": ".", "files": ["lib/"], "_shasum": "966454e8765462e37644d3626f6742ce8b70965d", "gitHead": "d34854a7af6ed1204f55d7da7761497bad350a7b", "scripts": {"lint": "eslint .", "test": "mocha", "build": "node scripts/transform.js && node scripts/convert-idl.js", "pretest": "node scripts/get-latest-platform-tests.js && npm run build", "coverage": "istanbul cover node_modules/mocha/bin/_mocha", "prepublish": "npm run build"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "7.5.0", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}, "devDependencies": {"mocha": "^2.2.4", "eslint": "^2.6.0", "recast": "~0.10.29", "request": "^2.55.0", "istanbul": "~0.4.3", "webidl2js": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url-5.0.0.tgz_1495832200994_0.4803313452284783", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "whatwg-url", "version": "6.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@6.0.0", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "b4fabb090b68685e2cdc7f6bae330162814e7173", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-6.0.0.tgz", "integrity": "sha512-qkcSNAzyC8cvp1NHmJUmhzyFV+6wBa9zkVSwulGYEO01cofdRnSvKQdrSbr4Z5uSRfdJQSpMlqXoGOOpzCemSQ==", "signatures": [{"sig": "MEUCIBw6kEuBon+Njyb9USc9JdAxwBJFACMKQ37sjlpYDUU2AiEAvdn4YOog6OQidjP+6copL7e4p4plpuuFtOjJaE3C2v0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/public-api.js", "files": ["lib/"], "gitHead": "4d52fa4f6bb5d07a3aa34335f59873096b70a3bb", "scripts": {"lint": "eslint .", "test": "mocha", "build": "node scripts/transform.js && node scripts/convert-idl.js", "pretest": "node scripts/get-latest-platform-tests.js && npm run build", "coverage": "istanbul cover node_modules/mocha/bin/_mocha", "prepublish": "npm run build"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "5.0.4", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "8.0.0", "dependencies": {"tr46": "~0.0.3", "stable": "^0.1.6", "lodash.sortby": "^4.7.0", "webidl-conversions": "^4.0.1"}, "devDependencies": {"jsdom": "^11.0.0", "mocha": "^3.4.2", "eslint": "^4.1.1", "recast": "~0.12.6", "request": "^2.55.0", "istanbul": "~0.4.3", "webidl2js": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url-6.0.0.tgz_1498704193079_0.08849321026355028", "host": "s3://npm-registry-packages"}}, "6.0.1": {"name": "whatwg-url", "version": "6.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@6.0.1", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "e8c88d2c028c4a463d4a0f78e6bb421e4082b323", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-6.0.1.tgz", "integrity": "sha512-zC0Q3WsTebgwcOd9dNgeXJ/j2ZuUuAXE5i3ugG7GdpT6RTKR5U4iJdNwMxyXyHQ1sTkdOKYeNAp1Yr+9yFNDIw==", "signatures": [{"sig": "MEQCIA+NoG166skoHpnwiWCJHCrFgAfLG04bwMsTeIRE8eEpAiBX2tEgeSUYeZELJShSAYr80A/GeharYNvu+7B6aUK3Vg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/public-api.js", "files": ["lib/"], "gitHead": "5738b3effd2307d12683bd7fd5dc5a6391ce1147", "scripts": {"lint": "eslint .", "test": "mocha", "build": "node scripts/transform.js && node scripts/convert-idl.js", "pretest": "node scripts/get-latest-platform-tests.js && npm run build", "coverage": "istanbul cover node_modules/mocha/bin/_mocha", "prepublish": "npm run build"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "5.0.4", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "8.0.0", "dependencies": {"tr46": "~0.0.3", "lodash.sortby": "^4.7.0", "webidl-conversions": "^4.0.1"}, "devDependencies": {"jsdom": "^11.0.0", "mocha": "^3.4.2", "eslint": "^4.1.1", "recast": "~0.12.6", "request": "^2.55.0", "istanbul": "~0.4.3", "webidl2js": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url-6.0.1.tgz_1498707863242_0.6493291836231947", "host": "s3://npm-registry-packages"}}, "6.1.0": {"name": "whatwg-url", "version": "6.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@6.1.0", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "5fc8279b93d75483b9ced8b26239854847a18578", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-6.1.0.tgz", "integrity": "sha512-fEPI5W0waHZCiBgaz4LxvJbDgaid3ULlHGBNMgqYkwJqbAHRXARn4PNNAhmJYLM10B5mE7vCF7G71kGsVASGuA==", "signatures": [{"sig": "MEUCIQDsfO+r712rMmD+HczvMdKn/ZjE9oxQsBLPaoypxfL0uwIgDzmYSVn2rsVf1rusxh1gcuRzM3FO4eFS14Fy9kLF8W8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/public-api.js", "_from": ".", "files": ["lib/"], "_shasum": "5fc8279b93d75483b9ced8b26239854847a18578", "gitHead": "ebc0b39f5d24ad4ac22cd10a8e25840cff852f8c", "scripts": {"lint": "eslint .", "test": "mocha", "build": "node scripts/transform.js && node scripts/convert-idl.js", "pretest": "node scripts/get-latest-platform-tests.js && npm run build", "coverage": "istanbul cover node_modules/mocha/bin/_mocha", "prepublish": "npm run build"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "7.0.0", "dependencies": {"tr46": "~0.0.3", "lodash.sortby": "^4.7.0", "webidl-conversions": "^4.0.1"}, "devDependencies": {"jsdom": "^11.0.0", "mocha": "^3.4.2", "eslint": "^4.1.1", "recast": "~0.12.6", "request": "^2.55.0", "istanbul": "~0.4.3", "webidl2js": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url-6.1.0.tgz_1498781749852_0.31180791836231947", "host": "s3://npm-registry-packages"}}, "6.2.0": {"name": "whatwg-url", "version": "6.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@6.2.0", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "36a5fee54c32b9f926000031e5526c72ad22ede6", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-6.2.0.tgz", "integrity": "sha512-9sOJZPQEVV7P1HVLQqJQnqO79KoH1hr3z5vvc0Uxq78f4Hn5Y2ddnjeEKfokJCeeDCC1+wxJh4KtYHNXzzf00Q==", "signatures": [{"sig": "MEYCIQDQUoWeOVYxU5WQo3jUFQMrkgTYs+wrONrGJ2D5J6wzvAIhALTOeQadyVu+/1ynLNu0xAAJTLhC1iFHhuCexTDJrb9U", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"testMatch": ["<rootDir>/test/**/*.js"], "testEnvironment": "node", "coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "collectCoverageFrom": ["lib/**/*.js", "!lib/utils.js"], "testPathIgnorePatterns": ["^<rootDir>/test/testharness.js$", "^<rootDir>/test/web-platform-tests/"]}, "main": "lib/public-api.js", "files": ["lib/"], "gitHead": "8cd6fe99047b0435d97123adfcc6b500de6e6cc0", "scripts": {"lint": "eslint .", "test": "jest", "build": "node scripts/transform.js && node scripts/convert-idl.js", "pretest": "node scripts/get-latest-platform-tests.js && node scripts/transform.js && node scripts/convert-idl.js", "coverage": "jest --coverage", "prepublish": "node scripts/transform.js && node scripts/convert-idl.js"}, "_npmUser": {"name": "sebmaster", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"tr46": "^1.0.0", "lodash.sortby": "^4.7.0", "webidl-conversions": "^4.0.1"}, "devDependencies": {"jest": "^21.0.2", "jsdom": "^11.0.0", "eslint": "^4.1.1", "recast": "~0.12.6", "request": "^2.55.0", "istanbul": "~0.4.3", "webidl2js": "^7.1.0", "domexception": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url-6.2.0.tgz_1505175805052_0.049473063088953495", "host": "s3://npm-registry-packages"}}, "6.2.1": {"name": "whatwg-url", "version": "6.2.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@6.2.1", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "db8fb96d7f02661af266e3cefc18425923900a00", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-6.2.1.tgz", "integrity": "sha512-+WH1K7dyXmt9a3U1Nt1GcsQsmnnHWfl/D8VTSK30ltddZukygZh691yNmydWcyzn0aDwc9+OFpDHYwzRgBEZTQ==", "signatures": [{"sig": "MEUCIQDwkAaP948PiZnOllH9t8eAvt9GbR/tNcFMiuEUlazH5wIgdrUDZdEYNXvQEUY+Nl8UE+4pj8J0coWkpMjvdzByoII=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"testMatch": ["<rootDir>/test/**/*.js"], "testEnvironment": "node", "coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "collectCoverageFrom": ["lib/**/*.js", "!lib/utils.js"], "testPathIgnorePatterns": ["^<rootDir>/test/testharness.js$", "^<rootDir>/test/web-platform-tests/"]}, "main": "lib/public-api.js", "files": ["lib/"], "gitHead": "be9038b00a2a1da7a5b4717cee13e4c734e10d8a", "scripts": {"lint": "eslint .", "test": "jest", "build": "node scripts/transform.js && node scripts/convert-idl.js", "pretest": "node scripts/get-latest-platform-tests.js && node scripts/transform.js && node scripts/convert-idl.js", "coverage": "jest --coverage", "prepublish": "node scripts/transform.js && node scripts/convert-idl.js"}, "_npmUser": {"name": "sebmaster", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"tr46": "^1.0.0", "lodash.sortby": "^4.7.0", "webidl-conversions": "^4.0.1"}, "devDependencies": {"jest": "^21.0.2", "jsdom": "^11.0.0", "eslint": "^4.1.1", "recast": "~0.12.6", "request": "^2.55.0", "istanbul": "~0.4.3", "webidl2js": "^7.1.0", "domexception": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url-6.2.1.tgz_1505176449662_0.3377517997287214", "host": "s3://npm-registry-packages"}}, "6.3.0": {"name": "whatwg-url", "version": "6.3.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@6.3.0", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "597ee5488371abe7922c843397ddec1ae94c048d", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-6.3.0.tgz", "integrity": "sha512-rM+hE5iYKGPAOu05mIdJR47pYSR2vDzfrTEFRc/S8D3L60yW8BuXmUJ7Kog7x/DrokFN7JNaHKadpzjouKRRAw==", "signatures": [{"sig": "MEYCIQDnccfJPzR/eUinOD0QCP0bXpIDm9YT+ycDVg2Hb8F2pAIhAKK3C2m2iRM6oboOzfDTqszeB7MCCh22ChYgzvlw26Ja", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"testMatch": ["<rootDir>/test/**/*.js"], "testEnvironment": "node", "coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "collectCoverageFrom": ["lib/**/*.js", "!lib/utils.js"], "testPathIgnorePatterns": ["^<rootDir>/test/testharness.js$", "^<rootDir>/test/web-platform-tests/"]}, "main": "lib/public-api.js", "files": ["lib/"], "gitHead": "18d92e92de5c052b90694bae9be0a89002f2412f", "scripts": {"lint": "eslint .", "test": "jest", "build": "node scripts/transform.js && node scripts/convert-idl.js", "pretest": "node scripts/get-latest-platform-tests.js && node scripts/transform.js && node scripts/convert-idl.js", "coverage": "jest --coverage", "prepublish": "node scripts/transform.js && node scripts/convert-idl.js"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "8.5.0", "dependencies": {"tr46": "^1.0.0", "lodash.sortby": "^4.7.0", "webidl-conversions": "^4.0.1"}, "devDependencies": {"jest": "^21.0.2", "jsdom": "^11.0.0", "eslint": "^4.1.1", "recast": "~0.12.6", "request": "^2.55.0", "istanbul": "~0.4.3", "webidl2js": "^7.1.0", "domexception": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url-6.3.0.tgz_1506300938920_0.32468617195263505", "host": "s3://npm-registry-packages"}}, "6.4.0": {"name": "whatwg-url", "version": "6.4.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@6.4.0", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "08fdf2b9e872783a7a1f6216260a1d66cc722e08", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-6.4.0.tgz", "integrity": "sha512-Z0CVh/YE217Foyb488eo+iBv+r7eAQ0wSTyApi9n06jhcA3z6Nidg/EGvl0UFkg7kMdKxfBzzr+o9JF+cevgMg==", "signatures": [{"sig": "MEUCIQCB+3fk7kZ7TsO/hJ0fMSKwcunFl+czazT5yzIXs4p3kwIgUfCauLVnYPCXL97Aqc9Dg/fwA42uBO1Nf1DlOxYR10Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"testMatch": ["<rootDir>/test/**/*.js"], "testEnvironment": "node", "coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "collectCoverageFrom": ["lib/**/*.js", "!lib/utils.js"], "testPathIgnorePatterns": ["^<rootDir>/test/testharness.js$", "^<rootDir>/test/web-platform-tests/"]}, "main": "lib/public-api.js", "files": ["lib/"], "gitHead": "6bb04c76f48ff5d6eee9776edbc66c648d840179", "scripts": {"lint": "eslint .", "test": "jest", "build": "node scripts/transform.js && node scripts/convert-idl.js", "pretest": "node scripts/get-latest-platform-tests.js && node scripts/transform.js && node scripts/convert-idl.js", "coverage": "jest --coverage", "prepublish": "node scripts/transform.js && node scripts/convert-idl.js"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {"tr46": "^1.0.0", "lodash.sortby": "^4.7.0", "webidl-conversions": "^4.0.1"}, "devDependencies": {"jest": "^21.0.2", "jsdom": "^11.0.0", "eslint": "^4.1.1", "recast": "~0.12.6", "request": "^2.55.0", "istanbul": "~0.4.3", "webidl2js": "^7.1.0", "domexception": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url-6.4.0.tgz_1512513561005_0.5477508250623941", "host": "s3://npm-registry-packages"}}, "6.4.1": {"name": "whatwg-url", "version": "6.4.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@6.4.1", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "fdb94b440fd4ad836202c16e9737d511f012fd67", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-6.4.1.tgz", "fileCount": 12, "integrity": "sha512-FwygsxsXx27x6XXuExA/ox3Ktwcbf+OAvrKmLulotDAiO1Q6ixchPFaHYsis2zZBZSJTR0+dR+JVtf7MlbqZjw==", "signatures": [{"sig": "MEUCIHCQd1Q9EvL8dKbEg6u4iY/mzi2qors3njrdetqMvPj0AiEA+rEZUUd9Jl47IdNHw92TUEVOPfE3n31qMLuLJIGUDUk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76827, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3N9TCRA9TVsSAnZWagAA9A0P/3MAd7hVynyzMgGRqu3S\n8c16+RiQNp374FkedWFrum4R23UoMf9bHd6UFNxk1hXIPO50W8oEu4Pojpvh\nvAxYAkY2kUW1/50U1Bsrxa+3xepLhsuRwKQBrXUngJpzllBqUEhqgk1BjlER\nmXvOflrTKMF18unJUQg5PVH1ccevsvrPYjcr2ODqVJZ7IRhMWe1bPyxR5ACJ\nxmMgANDq/G3p5IvV/sjunRFyTe6lZ+3RNbbHGDc0UANTvraEctrAMHzJGoLp\nAwrKDvTlsPxujGUc8Ew9K5Z6SHYy7tRAZRgfuyWC9IWL2m8M4CU95bviey7v\n+G3Udg/6uXPELhDAGrAaF3itOLazijg8nER0wr9TDj6HsawKmJPsdyhQNRt2\nayS/AV3t1lnDXXjKLJ014fTLAxLPU2g5SYXuLSfZ9HVF0kha5stxiOL9Gp7H\nZsmG6Av2XwAn1xuUKCuUVb1oeuwI/tntitDj25Zvy6FEUIxBZm0jBS/JsDLP\n7ZpCIRNHIxjaCfsftIZqd3GTSj8ph/UgeVcmeM15Nw/LI87pjkyi9Ns9UwZL\n3dUjjxALqyTYj07XGXiWSwHAJE1brIR7Ptb+fkMxZ5UsBN443ZutKnlzJ+ev\na7GUzFq1awF7cdKto7DWepeZtqy3/1ZiYfGqP0lC4IMJ44PfYJhD9ktyebyq\n1zoK\r\n=ctEg\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testMatch": ["<rootDir>/test/**/*.js"], "testEnvironment": "node", "coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "collectCoverageFrom": ["lib/**/*.js", "!lib/utils.js"], "testPathIgnorePatterns": ["^<rootDir>/test/testharness.js$", "^<rootDir>/test/web-platform-tests/"]}, "main": "lib/public-api.js", "files": ["lib/"], "gitHead": "cb9f0dd5f01cedcbc81df3f8faeaffae714e9fd2", "scripts": {"lint": "eslint .", "test": "jest", "build": "node scripts/transform.js && node scripts/convert-idl.js", "pretest": "node scripts/get-latest-platform-tests.js && node scripts/transform.js && node scripts/convert-idl.js", "coverage": "jest --coverage", "prepublish": "node scripts/transform.js && node scripts/convert-idl.js"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "8.9.4", "dependencies": {"tr46": "^1.0.1", "lodash.sortby": "^4.7.0", "webidl-conversions": "^4.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^22.4.3", "jsdom": "^11.8.0", "eslint": "^4.19.1", "recast": "~0.14.7", "request": "^2.85.0", "istanbul": "~0.4.5", "webidl2js": "^7.4.0", "domexception": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url_6.4.1_1524424529474_0.5994462621079473", "host": "s3://npm-registry-packages"}}, "6.5.0": {"name": "whatwg-url", "version": "6.5.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@6.5.0", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "f2df02bff176fd65070df74ad5ccbb5a199965a8", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-6.5.0.tgz", "fileCount": 12, "integrity": "sha512-rhRZRqx/TLJQWUpQ6bmrt2UV4f0HCQ463yQuONJqC6fO2VoEb1pTYddbe59SkYq87aoM5A3bdhMZiUiVws+fzQ==", "signatures": [{"sig": "MEYCIQDdsydCWe4S+zxSdHMqwzb1DCuTs9d6ccaGvQHbw1wJ2AIhAJhxz0D8MZRfNY7RgnpzsiRmnPwYCmZ/c/U4+/qZRQ5L", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77124, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbGukMCRA9TVsSAnZWagAAuxgQAJcuS32R2OwcRgbBamBf\nsXbTFoyiLkTEvHUOxf5NXgYRemXmc10+0wyF6tNrprwaZwdxLpvVTOVY+dfy\nUT19eFW+Svt78Qa5J+n+zKHQyPWmVIa96E3zvLuHRcC81/wZ+Hx89EilLboJ\n1uSsv8EjyrjR32aNSf1Z7hdNdiY+LpvZXugjILRMqVCXyQJb6YBLCV3StxWu\n6AQjps6nRtkPMDq1B1+JF7q/27W3Tw+R0NDFYWkfH9eVeMnGNTWcXwAwgG9w\nbX7sUGAGddTnatkoceW1xfZSD8ObQl01gtG4jvFK5b7Hr1zMfSzOmCauinZ0\n/uW/tarU4IWuodAS7/2PZIH3korOlMtRif58aD5jAZcCXuN0b16bCbuD0oz7\nzmsSoIduJJiGImzoLpaRC2Uw5N3lFSLdIYkW7MQXYnJf3vHOIYjBcUq29pwV\n6pzp6yezlwoaI+bEsvX+KUROZb68hHqsHQpNrDAFMVEMRiz9MwmYrCTAZlSh\nMN+Rb+fLo2XVn+qJlUc8T1HDaag03poepkelo8tI9ELCdyiB0m/MRBzxK1cc\nolucD4MB/MEGSeUDQpoJdOLO4OtrwpTs8JO4Gd/QN9qS2aQdHU5dCYybxRQr\nPW9sioc8rH6YcjYiaaPZAf0cdo06hdnUJy29w2r2+Xloh4TNXDYEzNWtVCsq\nGMN1\r\n=RyzT\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testMatch": ["<rootDir>/test/**/*.js"], "testEnvironment": "node", "coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "collectCoverageFrom": ["lib/**/*.js", "!lib/utils.js"], "testPathIgnorePatterns": ["^<rootDir>/test/testharness.js$", "^<rootDir>/test/web-platform-tests/"]}, "main": "lib/public-api.js", "files": ["lib/"], "gitHead": "4a073d8641307c377ef736d320616018c17c25e4", "scripts": {"lint": "eslint .", "test": "jest", "build": "node scripts/transform.js && node scripts/convert-idl.js", "pretest": "node scripts/get-latest-platform-tests.js && node scripts/transform.js && node scripts/convert-idl.js", "coverage": "jest --coverage", "prepublish": "node scripts/transform.js && node scripts/convert-idl.js", "build-live-viewer": "browserify lib/public-api.js --standalone whatwgURL > live-viewer/whatwg-url.js"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "10.1.0", "dependencies": {"tr46": "^1.0.1", "lodash.sortby": "^4.7.0", "webidl-conversions": "^4.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^22.4.3", "jsdom": "^11.8.0", "eslint": "^4.19.1", "recast": "~0.14.7", "request": "^2.85.0", "istanbul": "~0.4.5", "webidl2js": "^7.4.0", "browserify": "^16.2.2", "domexception": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url_6.5.0_1528490251889_0.8751875236451687", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "whatwg-url", "version": "7.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@7.0.0", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "fde926fa54a599f3adf82dff25a9f7be02dc6edd", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-7.0.0.tgz", "fileCount": 12, "integrity": "sha512-37GeVSIJ3kn1JgKyjiYNmSLP1yzbpb29jdmwBSgkD9h40/hyrR/OifpVUndji3tmwGgD8qpw7iQu3RSbCrBpsQ==", "signatures": [{"sig": "MEQCIHOJHIwX2WCLPV5Ex3vVsQZA8xSYEie3mK/monL8Etp3AiB1q8/A0IwEm3B/J0FfZ8673ZYF8xmfyGjFnQHgtVFLsg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77922, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbeHTPCRA9TVsSAnZWagAAuVAP/1jFnEAG6pv1D9ieOgVP\nnXfT8Y/cxmm6Ztlvrl0vPEoifDnbAGKvI4IwHLRbgu54n8Iz1x0hg+6I6RKY\nyi12yVbJZd8S98cT0OjffKL2L1jPNLyQeEAvC8to/NgIiHfpjPPpeA6i9X5c\nvIkbYYCA6d7a/bpqIgpNf5rpiTsOQDtrejdbsvrNP1kox17yAoY1b96NzDtJ\n66UQRlFCuS6MgkZeDyzqST1q/gF+UwxpaKvDNnXS3JRdlfl5IjgC+0bNZDuo\nBLCnk2OLAoFzPgkUI3cZVsH4QAUFwA86YnLcG2FPO8FzanAi4nrCOj7Xd2/V\nF9xjzRUcu6kIwmKtGloyE1cDlzJJvEWtrsesA5vy4Chxx0BL8quIxHneFXrD\nOWPeCkuh/jAZOfdit9ORUaPY2DUxfF2LCnTMKletAm7ANppGUgS8uomF/ktg\nFpr7sE/tXwfOccTCLN0VOouLdoGuavtZThfWj5s9VjjAOASxjqnTH/7SJYIT\nHJi/gjIGVGaqu4CDsk9YYGb1i5nZcnv3hRKGT+wSwYl4zCryEq7WQAnGxGYJ\nlJNvOeN/kFHRERs/4yVH6szybwFBKEepwii6IXFU+qNeO5b3c4/91CiWcPhi\nSQoCLv48nmt2+7pg+7hJf1I1+imIDT2PWTYfQk4yPz6dZLTqcg43vnOrOWjQ\n3eFw\r\n=468P\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testMatch": ["<rootDir>/test/**/*.js"], "testEnvironment": "node", "coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "collectCoverageFrom": ["lib/**/*.js", "!lib/utils.js"], "testPathIgnorePatterns": ["^<rootDir>/test/testharness.js$", "^<rootDir>/test/web-platform-tests/"]}, "main": "lib/public-api.js", "files": ["lib/"], "gitHead": "a7ca3cad23d5c4cb263c751492f4a1f867dfafca", "scripts": {"lint": "eslint .", "test": "jest", "build": "node scripts/transform.js && node scripts/convert-idl.js", "pretest": "node scripts/get-latest-platform-tests.js && node scripts/transform.js && node scripts/convert-idl.js", "coverage": "jest --coverage", "prepublish": "node scripts/transform.js && node scripts/convert-idl.js", "build-live-viewer": "browserify lib/public-api.js --standalone whatwgURL > live-viewer/whatwg-url.js"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"tr46": "^1.0.1", "lodash.sortby": "^4.7.0", "webidl-conversions": "^4.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^23.5.0", "jsdom": "^11.12.0", "eslint": "^5.4.0", "recast": "^0.15.3", "request": "^2.88.0", "webidl2js": "^9.0.1", "browserify": "^16.2.2", "domexception": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url_7.0.0_1534620878844_0.5401327223354646", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "whatwg-url", "version": "7.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@7.1.0", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "c2c492f1eca612988efd3d2266be1b9fc6170d06", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-7.1.0.tgz", "fileCount": 12, "integrity": "sha512-WUu7Rg1DroM7oQvGWfOiAK21n74Gg+T4elXEQYkOhtyLeWiJFoOGLXPKI/9gzIie9CtwVLm8wtw6YJdKyxSjeg==", "signatures": [{"sig": "MEUCIFE6qprY3Fw2aBvyOM/V4OTiGeB5Z1S9wLI04SybOsi4AiEAs+CygR2PTNrF0hLg6OgmIjkyEKKYEsFXN5Nzd39QfPo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78428, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdrdcOCRA9TVsSAnZWagAAf5cP/jEQ5+b+XCaQCswxck5d\npGWmqPPj1jWO74AjH+lWDpUjJTRKSKft/n+t1/Pj6WRU12Mi9CQPxKmIzoO7\nf9vetmggkYHNU9FlH835F4U2hG2yOtU0mtfOjrEr5dw/aUwrhmBtfq1M89IL\nIPaatHdNiZpz/AeIKiewG8vUHjXNVaR9QWmj3TmXWPvi+snSi3X9gBH/CbCt\nno6Z+QMxLW7UVwUY2rYZ2NiVo9s7Pdo96buou/0WoGb83VxZ13IZo1gR99QE\nAfb/y9UxfY4klDAZYhcJL6S1P7k08kADn8tVJommDXHIT/UR246o038MX7MF\nKpiB6af3XXzP1tG1R7YOyX54m095cRS+MISJsY+8EUVGltzwCDG3IGOSkYiU\nnbDz4u0j5nESpFozwHpyJWfmRvXeE7+/3h9pdGcnu35SIGYoOXVVG2LZWxDu\njrz7QJB3MARhvJKNWGAAXm+81sShzzzVRbV0ngVNNATdX0HCQ1CdpsYE93YR\nASl/buzfNU5wPQ+dz7JjfrjHtakVg5Ebhnqn6sE0mCGLVBzwpRd5LyW2vtMR\nLAvRy/ORWAv74BIJGJV4jFAD0divLlFZRLqtPgwvD2GiyidwFN3o8LnXlLcL\nb2EA0GNyxFbG4u+Wu/ACEAo8uGDMTfKy3A9ZgjI0w4R5GftPVVwd12eFmUFG\nWAar\r\n=RS9J\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testMatch": ["<rootDir>/test/**/*.js"], "testEnvironment": "node", "coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "collectCoverageFrom": ["lib/**/*.js", "!lib/utils.js"], "testPathIgnorePatterns": ["^<rootDir>/test/testharness.js$", "^<rootDir>/test/web-platform-tests/"]}, "main": "lib/public-api.js", "gitHead": "d1c8d31465924aaf654a55254c9c63526b73c9dd", "scripts": {"lint": "eslint .", "test": "jest", "build": "node scripts/transform.js && node scripts/convert-idl.js", "pretest": "node scripts/get-latest-platform-tests.js && node scripts/transform.js && node scripts/convert-idl.js", "coverage": "jest --coverage", "prepublish": "node scripts/transform.js && node scripts/convert-idl.js", "build-live-viewer": "browserify lib/public-api.js --standalone whatwgURL > live-viewer/whatwg-url.js"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "6.10.2", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "12.9.1", "dependencies": {"tr46": "^1.0.1", "lodash.sortby": "^4.7.0", "webidl-conversions": "^4.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^9.2.2", "jest": "^23.5.0", "eslint": "^5.4.0", "recast": "^0.15.3", "webidl2js": "^9.0.1", "browserify": "^16.2.2", "domexception": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url_7.1.0_1571673869527_0.6964578719490482", "host": "s3://npm-registry-packages"}}, "8.0.0": {"name": "whatwg-url", "version": "8.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@8.0.0", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "37f256cb746398e19b107bd6ef820b4ae2d15871", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-8.0.0.tgz", "fileCount": 13, "integrity": "sha512-41ou2Dugpij8/LPO5Pq64K5q++MnRCBpEHvQr26/mArEKTkCV5aoXIqyhuYtE0pkqScXwhf2JP57rkRTYM29lQ==", "signatures": [{"sig": "MEYCIQDKU8DLG3O+3V15n/Y76e1WoEFyKZm+M4wThWjwhCQvZAIhAK4NJrrXR6tY79YOiZzRj0/A/iFyXlC4X8FUYu6Qo5o+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82030, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeDk5NCRA9TVsSAnZWagAATPYP/j00BF9HOThvkARQw0aZ\nEiwRicOZzXYwx/l5KPkMAOU7suDPjzfl6CKO+JqFsrwxUlm7XjIuJ+J9eQn8\nO7RAd5X/ZReptq3CnLXmw/5EO5mjTDu7ya7WUT9PF6iGdpmxawOV4A8FAu1T\n7Ebc+u/jCK3u+cgeJIITiR6W3MccMWmlmQ8mTGHPaFwYNEFnnv+W3uNIgrHD\nqCnxnZEMIqRcBsOqWOsJz9TzbynrxlBNpdZvp49/eP9Hu7SIf9PCzF0vHu6A\nTAqeBqA+sztvCQA9QEo1fD1xLoEpYVnYkTwtSzufA1UU96FzUv+Jc+yungQu\nzXoSspVeBHr5J7dDoRSqGDUajAUvekOyIr8V8GUiM1lpUT6DQh+B/tPTmP4l\nD1XtqvLmI6i9QeUxZof2Qo+k9hgcR0pNeMO9iKX5Esh3sEymLVvXLpFcN/L7\nzooutEOR8FE2A2o9wWuk55eRy0Eqyn3KzmSgphHjE0Rdlhb1RiSl3r4H54Ez\nH/dsLmXYTmNv590d96lvnwDg/NlqB0XJENHuVeCtJ+QC2XjsskdcZ6kfzOj+\nw1gFSb6h3GdNxueR8SMqVGlxgJO9NORNvYGvykDiGe/AOvcbXGw+sIZ0oAI2\nobnQ4QQ3KPTqb382AkuX+ebj0tZ8upY6FA/kLMHIGqhGhCkRSSa6h/b6IlSK\nR4zb\r\n=YXxG\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testMatch": ["<rootDir>/test/**/*.js"], "testEnvironment": "node", "coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "collectCoverageFrom": ["lib/**/*.js", "!lib/utils.js"], "testPathIgnorePatterns": ["^<rootDir>/test/testharness.js$", "^<rootDir>/test/web-platform-tests/"]}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "2a73056229253b42b6833aff93331097f5b51674", "scripts": {"lint": "eslint .", "test": "jest", "build": "node scripts/transform.js && node scripts/convert-idl.js", "pretest": "node scripts/get-latest-platform-tests.js && node scripts/transform.js && node scripts/convert-idl.js", "coverage": "jest --coverage", "prepublish": "node scripts/transform.js && node scripts/convert-idl.js", "build-live-viewer": "browserify index.js --standalone whatwgURL > live-viewer/whatwg-url.js"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "12.1.0", "dependencies": {"tr46": "^2.0.0", "lodash.sortby": "^4.7.0", "webidl-conversions": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^10.2.1", "jest": "^24.9.0", "eslint": "^6.8.0", "recast": "^0.18.5", "webidl2js": "^12.0.0", "browserify": "^16.5.0", "domexception": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url_8.0.0_1577995852651_0.6281129849289138", "host": "s3://npm-registry-packages"}}, "8.1.0": {"name": "whatwg-url", "version": "8.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@8.1.0", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "c628acdcf45b82274ce7281ee31dd3c839791771", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-8.1.0.tgz", "fileCount": 13, "integrity": "sha512-vEIkwNi9Hqt4TV9RdnaBPNt+E2Sgmo3gePebCRgZ1R7g6d23+53zCTnuB0amKI4AXq6VM8jj2DUAa0S1vjJxkw==", "signatures": [{"sig": "MEUCIQC8l6x0D1HaDoVFmcylr3wlolBs88bRZCwPweEIFX258wIgMXQn5xkgdUaPOrBRAXu7J58XrOfyGGFvgbNvLZMVi6E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80806, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeta3UCRA9TVsSAnZWagAAsuQP/3/FbcenqPFEQM4tDzll\nsmCEmekrg+LvaK9vlH2+VfbR49WZb39H09AtNTngm8LQrBJjhLgCJ8dbRYdB\nMD2JTOuR+Iq//JOJsuHbCA2E7U+0kbmFtyRhZcVDE5ug9O0TwPHKpx0KzBOX\ngHZd8CebA0RhEWRrciqLpG7DOgy3GzI5hEzyNmQrvAPxOOjyhJZX8s0Eq2p6\nZxMPrJ29BeZSH0o7xH6j+zu/Fb5p0qdyAhvhvCmlFRspDYyvcAo5jV0DXPUE\ncMijSuxBcz7W5FqstvrPqIHg5wzfX1Lqeb7UYZMHAS/NhPdFqba4FI3N8M3G\n72ix/6yb2qxbXXhXJZ2E0fltwwpl4nlackpuDvZN/Ea3HfT+sJzYub8iOZtK\nNBgTkOP5RitTsYJ0xeNPGKopZ22MHdOAA8Vr4Dk+AGJHS55tHS814pNnOMka\nz2PY21wOIy+ox8VxlBN9CYG5IXK1gD2Bn8lx/OiXcWm/0BPOcWkIR+Bt9eok\n1QA46c3xgRxWAhufNeabvEzCwzJCRAdWzkXf5qi1Ib8YXVRWf7XW4N0YVu1r\neQqsnI5cOIrUvsx506y6jBxlffR+iZ/ZFR1bQBs6lnoCy0PgIdAAv3a64E9T\nWUC12BjtMW2HicyfIe5DjJQCobNQhOVgWwq/kIaq87uCl4WMOHXAzt5gB2lj\nhFpj\r\n=idDv\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testMatch": ["<rootDir>/test/**/*.js"], "testEnvironment": "node", "coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "collectCoverageFrom": ["lib/**/*.js", "!lib/utils.js"], "testPathIgnorePatterns": ["^<rootDir>/test/testharness.js$", "^<rootDir>/test/web-platform-tests/"]}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "1e9fe4924f9760499ed9f722bade9afed4c14265", "scripts": {"lint": "eslint .", "test": "jest", "build": "node scripts/transform.js && node scripts/convert-idl.js", "prepare": "node scripts/transform.js && node scripts/convert-idl.js", "pretest": "node scripts/get-latest-platform-tests.js && node scripts/transform.js && node scripts/convert-idl.js", "coverage": "jest --coverage", "build-live-viewer": "browserify index.js --standalone whatwgURL > live-viewer/whatwg-url.js"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "14.0.0", "dependencies": {"tr46": "^2.0.2", "lodash.sortby": "^4.7.0", "webidl-conversions": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^10.6.0", "jest": "^25.1.0", "eslint": "^6.8.0", "recast": "^0.18.7", "webidl2js": "^14.0.0", "browserify": "^16.5.0", "domexception": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url_8.1.0_1588964820310_0.7405488527743682", "host": "s3://npm-registry-packages"}}, "8.2.0": {"name": "whatwg-url", "version": "8.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@8.2.0", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "89383f80ea3888734d1cf29526c135d52e60166d", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-8.2.0.tgz", "fileCount": 17, "integrity": "sha512-Sl4svq71j4kzaFD13uxkVl2AIsbj/xwp8NTM1VMhFRyNT1ZMTWaV6+Pva0fQs7y8+cAEPrDGfCAFLvJejhT79g==", "signatures": [{"sig": "MEQCIHTVudt7KdPjb3CCRU9xycG9aL8Kb1j+Tw0yqNEj9yJiAiAa5/8MXOw0cbAbGLAbWO6yjk0acOyx0ZCH2P+ujZb3Vg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90733, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfQ+M9CRA9TVsSAnZWagAAP2MP/je/P+nKLNgi506molBV\ny1F8AgVKeJz9x5XrJGckBorqvpZf/TVR2thnPykRGtTaLBIsntPPAygGuBFw\noZ6beM76uQ7llhagHssUjRMbTqFyRtoInMdcq5lD7+yTHquZcijT4wgz5TFl\nl67QLtWa9YBM8KuzjA54wPOdUkg9aqdX9JuwXkNfResIHvycQzeld43+89Ah\nvz45EgvXob1TFOS4Jo4X/FiZEwhTyI0eiX/1K71+DQK9yB2vnMB67q9qd/Pm\nYR0s6kIeCp3voDPK3Iygf+p8t4zUQ+oltHbrpMBBSVCx+nRH61hOGidGnsC5\nI0y0Sul+sT+ZtE4onk3xRMRjONMGm6208WrhrW/hQfUkJVGHkb4aqpX5WdDf\ntLToobZaV2RJ3aHHxo+O9vxyHcxGSzUv9yKVeBHHkrqeXFSbchZL5zce3f8N\nnxh+8uVFdQkIvWZYWUCW0LFvOeUkDzu5GnU+BUOhtmiZ1RIp6yFBRoETtsUD\ni2vazbDmMoZ7og23oqVKM7ktbOlJoUAEMBwZYPjXZVM6mRq08/ldZrRX/Ofy\nlZr49hMgXN374BOGX+iv9Ddud9vHjc2t8kPo7VgsewtuaPbF0TS6J6rpCkA9\nNITzlo7KS4FLMWKdO0wFnc4d/Hk8uP/J8EqR2zRo5F0ZpwtS1GJXCx4bfH+x\nctj3\r\n=coIu\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testMatch": ["<rootDir>/test/**/*.js"], "testEnvironment": "node", "coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "collectCoverageFrom": ["lib/**/*.js", "!lib/utils.js"], "testPathIgnorePatterns": ["^<rootDir>/test/testharness.js$", "^<rootDir>/test/web-platform-tests/"]}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "56e557231cf14ea3104e44d45da6c9ca2a1216e5", "scripts": {"lint": "eslint .", "test": "jest", "prepare": "node scripts/transform.js", "pretest": "node scripts/get-latest-platform-tests.js && node scripts/transform.js", "coverage": "jest --coverage", "build-live-viewer": "browserify index.js --standalone whatwgURL > live-viewer/whatwg-url.js"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "14.3.0", "dependencies": {"tr46": "^2.0.2", "lodash.sortby": "^4.7.0", "webidl-conversions": "^6.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.5.2", "glob": "^7.1.6", "jest": "^26.4.2", "eslint": "^7.7.0", "recast": "^0.20.2", "webidl2js": "^16.2.0", "browserify": "^16.5.2", "domexception": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url_8.2.0_1598284604545_0.07155127697778196", "host": "s3://npm-registry-packages"}}, "8.2.1": {"name": "whatwg-url", "version": "8.2.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@8.2.1", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "ed73417230784b281fb2a32c3c501738b46167c3", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-8.2.1.tgz", "fileCount": 17, "integrity": "sha512-ZmVCr6nfBeaMxEHALLEGy0LszYjpJqf6PVNQUQ1qd9Et+q7Jpygd4rGGDXgHjD8e99yLFseD69msHDM4YwPZ4A==", "signatures": [{"sig": "MEYCIQDOdTYvDhlev5yg3Js/TJ33VSJhsNEgXb7I6Wo9bA1C0wIhAO61Vyl2mcu8OEdzTW9SrVdcBVZhOwgIQmRKAHPoAjWa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90754, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfRTPYCRA9TVsSAnZWagAATGkP/jzg6j8CJ+QLs3LV60oD\n8JwsmTNilUE8aVjz9VyiTttk7RB6Ci2rTFuwwhgUygTiKkbRAQW2UeRU7Iz5\nFcmKs6h3skf3v+Wfz7rYvf52Ru3Uof+IfIBM7YRej2OJj4ZXOOe3OyQ2KQm3\nvmsVxxTnmFRuw7c0BmbcH1UjguMi6xP++tQs4qAgUap3aFRRoXv8AkJPsZja\n2fZxE3LTzt61I2dLDFkLQnXKrovLiKKXOphOavYGeN3XWG1HB1wApd/g/AQk\ne+Do2PIIFunRoJex0GIHe3Nqg3wtX+HkBiT5IXWjiVRDrYeDCv9FMMRrXlB3\nq9uMbN21mTxnbu0iHcc6FtyhFXVzLoo92dIyqGoGWltbk9VvbmQjT09PndjY\n+3CGShrz2mJyrMWiTah77I2BtwZN3J3qGnxYDedMhnWofhHLejlf3LnmjXHs\nUPi1BbRKXymq/3uY85B9LI/XVrwO/02yB/12+hO/ug+RbP5VBNn+ft8v5sd6\nHGN943rha6iIy3tQb0Yb4BTYesST77p8dwYVEdM2d3EkAA9gxMzQrfsChTkr\noCs5fKr6X+m9AY/Y+mSPLOfPV3VsxDKLclj70G1stGYZ9ea1/1GaTWn4rIY1\ntayh8d9IubKAuqCGY6uSyO7bO2Ta2Rdsgfx1WM1L2+VgWxDhQdECjXB1yAJC\ngWT/\r\n=t4IS\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testMatch": ["<rootDir>/test/**/*.js"], "testEnvironment": "node", "coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "collectCoverageFrom": ["lib/**/*.js", "!lib/utils.js"], "testPathIgnorePatterns": ["^<rootDir>/test/testharness.js$", "^<rootDir>/test/web-platform-tests/"]}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "3052b3b6141c2f9d7e6e28dd7729eb44fb2f61c5", "scripts": {"lint": "eslint .", "test": "jest", "prepare": "node scripts/transform.js", "pretest": "node scripts/get-latest-platform-tests.js && node scripts/transform.js", "coverage": "jest --coverage", "build-live-viewer": "browserify index.js --standalone whatwgURL > live-viewer/whatwg-url.js"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "14.3.0", "dependencies": {"tr46": "^2.0.2", "lodash.sortby": "^4.7.0", "webidl-conversions": "^6.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.5.2", "glob": "^7.1.6", "jest": "^26.4.2", "eslint": "^7.7.0", "recast": "^0.20.2", "webidl2js": "^16.2.0", "browserify": "^16.5.2", "domexception": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url_8.2.1_1598370775719_0.98344739277726", "host": "s3://npm-registry-packages"}}, "8.2.2": {"name": "whatwg-url", "version": "8.2.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@8.2.2", "maintainers": [{"name": "domenic", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "85e7f9795108b53d554cec640b2e8aee2a0d4bfd", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-8.2.2.tgz", "fileCount": 17, "integrity": "sha512-PcVnO6NiewhkmzV0qn7A+UZ9Xx4maNTI+O+TShmfE4pqjoCMwUMjkvoNhNHPTvgR7QH9Xt3R13iHuWy2sToFxQ==", "signatures": [{"sig": "MEUCIQC49VEei0Ka8jO1v92XS9oamYpK0Dw2WW1ko/Z5ZQZGIAIgCHNTmv9AUXqv3vceEFdn3AxcltDRpNlOEbzbwsrwEto=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90997, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfVTiUCRA9TVsSAnZWagAAfyQP/0BVZwlHmJ3S4UAjbU9y\nlYc5xB+udJqpen+NZyBP7ARkU4OyuqsrJxuEruQHGElPQnuU0d3rodb/Y9rF\n107+6l0WkFmVHdUtQOTYtOGJsBRK2JiXk+3pBIG3Fg6iIrglIDWOh5t6bpS5\nu4hfnzVjI0pDy+GANQQ5olt/MJkQ6/WKFHKwNSkUI1jLlkKLXA8SRFxw8R3D\na5ISefWJQ2e5hpe5X3xcj0w0kZ74448Eo/yE5tNjrfvKqQ5N8E+Tr+PdcXz2\nDPl+n+oZ6Ml6Pp2Tz1zNgDobAUo1FyQsfa3NA/USMfeH8r/b2/tRJ0cTdGpK\nswHD1MVSsNvJhl7FnSNn8/PMbBGIo7O3ELQOjK2UKVOG5piqDnZ3wpZmrmyt\n/PIf+QI5Feyt8KfUtF1XPjEaKld6Rcrp16Va8WvYsZH9DjM1mCHnD8SjXlsq\nqFALCl5UFxJD8oV3DnipP+PVrKHIWHqyhXEH/ozjqZxB9gkmQQi63USjHhKd\nhU6X78RXmCEECe//MV/TMyqTfrnkPW8Ybu/T+SVrsZlh1ilR0GOTQT3qwcql\n0RPqAxza7gURtiTHyBJmWX5EesAu/NFijpt4dkNeKVhCxyq6pOi0AXbnBQQR\nDv0BAcL1BSV/pcHNx3RiOnRRM5klJD14kG8VRWB6ojL2915ni6mjHQxQh+Ft\n4DG6\r\n=vxoj\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testMatch": ["<rootDir>/test/**/*.js"], "testEnvironment": "node", "coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "collectCoverageFrom": ["lib/**/*.js", "!lib/utils.js"], "testPathIgnorePatterns": ["^<rootDir>/test/testharness.js$", "^<rootDir>/test/web-platform-tests/"]}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "4282f6ca86571d8b25fd57f42dcd49908f643e76", "scripts": {"lint": "eslint .", "test": "jest", "prepare": "node scripts/transform.js", "pretest": "node scripts/get-latest-platform-tests.js && node scripts/transform.js", "coverage": "jest --coverage", "build-live-viewer": "browserify index.js --standalone whatwgURL > live-viewer/whatwg-url.js"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "14.3.0", "dependencies": {"tr46": "^2.0.2", "lodash.sortby": "^4.7.0", "webidl-conversions": "^6.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.5.2", "glob": "^7.1.6", "jest": "^26.4.2", "eslint": "^7.7.0", "recast": "^0.20.2", "webidl2js": "^16.2.0", "browserify": "^16.5.2", "domexception": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url_8.2.2_1599420563876_0.22111744195295624", "host": "s3://npm-registry-packages"}}, "8.3.0": {"name": "whatwg-url", "version": "8.3.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@8.3.0", "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "d1e11e565334486cdb280d3101b9c3fd1c867582", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-8.3.0.tgz", "fileCount": 17, "integrity": "sha512-BQRf/ej5Rp3+n7k0grQXZj9a1cHtsp4lqj01p59xBWFKdezR8sO37XnpafwNqiFac/v2Il12EIMjX/Y4VZtT8Q==", "signatures": [{"sig": "MEYCIQCei+pHWWvUH8DhYYC3X7iilQbKIQrM0+kxIpqSI6OSUgIhAMusaATLGr9nVrxtgbVS14q+WM9WSiXKN890JuJuZqfB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90588, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfdMJKCRA9TVsSAnZWagAAvDgQAKF5n4NJ6y7rc8LzA5qi\n4HJTmWGF7RnL19YXy1zCLLxfYFkpEojb4OJQf0P6IOIJT5qhCSBL3sPMSDVl\nv896i6ZgmnseGYl6Wyd3/n7yBl0nz6eWOk/ibPhxQQKW/bDBXI8fGlPkxDBx\nzaZJaewN8uFtRzHM1pa4Btv6/sITBP5VON09Wgkmf+593F6y5GFsRttn+O9X\n01nt6hImVt5PMD0+PwAlcWFvZQ43uWsgyCRSYzohhZTEAyOPphQ46wWsoR2l\n71o51X765URshfA9G1kzgBf0bQq6X0xVTLvqfXnyC8+BTQo0dBtKkf/TzD3p\nl4JX3AGsHqkpHFg8xGcF5BKG3+3zvLnY2M3qwG/x7ZbyKOkga5yGXm46ix3z\nO32GvecTY/XhPLhjxB1tZyjIM7lOI4rp+jSD5qI8Zw8A9L2hWGvNkeJWQYhE\nqnkvVRWU9LgBir5+4/XOBR0nBPslvW59V0ASaXZx01SBhQ4m59zPLWly8Tel\nH4icXz3C3gD0mQSpA7u/KvdR4VHgLT376UVXKSPR6C2fLMC3ZxyuJ7qZZ6jU\nZXCU1CJeVvLqs/r7NiL5YRwmumR7rxyWPKiSa7v4Dq06ytM4AE8XfPpdcEnB\no5KSAK2iyARj3IvWoZ71E43sarj4Ej2LAmmxETPqEjn+54y5HRC90HUHUvtf\npR7Y\r\n=xhtu\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testMatch": ["<rootDir>/test/**/*.js"], "testEnvironment": "node", "coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "collectCoverageFrom": ["lib/**/*.js", "!lib/utils.js"], "testPathIgnorePatterns": ["^<rootDir>/test/testharness.js$", "^<rootDir>/test/web-platform-tests/"]}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "497f05202c4b4261884e036aa315ed8254536833", "scripts": {"lint": "eslint .", "test": "jest", "prepare": "node scripts/transform.js", "pretest": "node scripts/get-latest-platform-tests.js && node scripts/transform.js", "coverage": "jest --coverage", "build-live-viewer": "browserify index.js --standalone whatwgURL > live-viewer/whatwg-url.js"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "14.3.0", "dependencies": {"tr46": "^2.0.2", "lodash.sortby": "^4.7.0", "webidl-conversions": "^6.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.5.2", "glob": "^7.1.6", "jest": "^26.4.2", "eslint": "^7.7.0", "recast": "^0.20.2", "webidl2js": "^16.2.0", "browserify": "^16.5.2", "domexception": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url_8.3.0_1601487434300_0.3239265349004674", "host": "s3://npm-registry-packages"}}, "8.4.0": {"name": "whatwg-url", "version": "8.4.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@8.4.0", "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "50fb9615b05469591d2b2bd6dfaed2942ed72837", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-8.4.0.tgz", "fileCount": 17, "integrity": "sha512-vwTUFf6V4zhcPkWp/4CQPr1TW9Ml6SF4lVyaIMBdJw5i6qUUJ1QWM4Z6YYVkfka0OUIzVo/0aNtGVGk256IKWw==", "signatures": [{"sig": "MEUCIBR8SecHR9t2xYrKRDiemA1Ou1Jdy41RCGs52suBDsbgAiEA2VIqUlkKjMWAiwFi2dQHZ5M2Ucpn5+Pm3QKlVgr8n7Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90503, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJffzuvCRA9TVsSAnZWagAAhTwP/iNrBeE0UHTAg+sV6qGc\n1sTpKPLQ7XnSFnaTZCTRoEpFNai3B+f8HPjbmwdeVx1xrf7KnZ8fZGDbxojS\nOiypxk5ovvMJKrW0dYwQRZrJ2T4z6FvSeiKP63x3q9hwCoGrxf3gqdLRt/sH\nJqVVbUuLAtv5TnWImo8/aRrIE/PIdMqtXKisPD+XV6a76UBu/O5n7kGeUUWS\nPCYaj5ZNz4cB7//MVkEmEU5DupUxSKFfVkyh+LmPO7YObUd/Tl+14O3UXix5\n/iOLaokty2FewjkGJWAXTlhfaZn9uxqfFIpmBcaEpMpBe+Vgvp4ul7y6aiaK\nXNygBFck1/c5faeh6hveT9fXtbMlMmJRHABUP8bNLFFBq1NeBDvY++VfijrX\nG7YZCiu3pcCkHbZw8BQwO5VLP/a68KlC//UieyBvmC/PyiLrM2V8tVrloiAq\nlry2psTxRlHH/UhTGkuJvo1NR87BrJRsXyvD2nFOuX7qS8uaZI25ZoR+9rX3\n99T3s+6b9kbQrz/Avn8axafUvTAPDSktF7A3fC7MhKqMcH7wGcU+1ncKMd32\nJ1Y1eZKQ0/xv7sveT3lcl1fnQmxd5pdaEf0RkCetsIrCODS22CDLR4Ik89uv\nnUZ1Oyp/AVMflnMRF0+hLDR3pbzoKtWdaruS72KbF1+pA91BDkfBef3hD+Jx\nUiED\r\n=6Fu3\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testMatch": ["<rootDir>/test/**/*.js"], "testEnvironment": "node", "coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "collectCoverageFrom": ["lib/**/*.js", "!lib/utils.js"], "testPathIgnorePatterns": ["^<rootDir>/test/testharness.js$", "^<rootDir>/test/web-platform-tests/"]}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "6c337df639d238758b48f3a85dd46ba6017a2363", "scripts": {"lint": "eslint .", "test": "jest", "prepare": "node scripts/transform.js", "pretest": "node scripts/get-latest-platform-tests.js && node scripts/transform.js", "coverage": "jest --coverage", "build-live-viewer": "browserify index.js --standalone whatwgURL > live-viewer/whatwg-url.js"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "14.3.0", "dependencies": {"tr46": "^2.0.2", "lodash.sortby": "^4.7.0", "webidl-conversions": "^6.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.5.2", "glob": "^7.1.6", "jest": "^26.4.2", "eslint": "^7.7.0", "recast": "^0.20.2", "webidl2js": "^16.2.0", "browserify": "^16.5.2", "domexception": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url_8.4.0_1602173871489_0.6987996414748254", "host": "s3://npm-registry-packages"}}, "8.5.0": {"name": "whatwg-url", "version": "8.5.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@8.5.0", "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "7752b8464fc0903fec89aa9846fc9efe07351fd3", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-8.5.0.tgz", "fileCount": 17, "integrity": "sha512-fy+R77xWv0AiqfLl4nuGUlQ3/6b5uNfQ4WAbGQVMYshCTCCPK9psC1nWh3XHuxGVCtlcDDQPQW1csmmIQo+fwg==", "signatures": [{"sig": "MEUCIDUIPOe++aCJcjVVbhdAUMdMA1dmIInU2HBS0OG7my+uAiEA4WsV9rZRzq+pPanEIq8+MKyOHfjXV7+eOq2POEYBLBk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90633, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW6DeCRA9TVsSAnZWagAAFKgP/0JxqkUMHZMsAC6JGl/+\n9GDs7urPCYbnarelIIXcchDV41INFBN13RKNzKZ62e9KXvMv5rj6EbREgV4f\n+e8ej2PUNlkR6DAn+grgoRM4sY/pesyd70pMUF3EeGqPDmILmfdKSJ48iaSo\nB51V4K2LIz4U4kY+e9wYnS/j/JtOlHDmA/t6wEsUQhnx2C00zno7BaxR3MAN\nG4ZGsf71jlU7hqQJH84+H8F9b69l+SxwD5B+rTHrb+WfAqlrKNUnObvf6ubI\nIlBjIEtGqfjLW2YMY7UNzEflsEpDDbRcvTGfVInXPZspSksImZq4R6Tf1bsO\ng3xKxU5YUSA3UPbqWmvy6gi+hqf6m+dXtm8Kqk41EJtiPCXNZtjwrJgFurCu\nTgVkatoQQfohQTiedcqgWzpWx/YglEFa3VEyMgwZYVk4q3LY6mPs9T8C9lhh\nE8uMcb+Xnphz8IAwPk/j2v2fHxTtUyvgtRj3oJPE+DQ17Wn66ez7Ed4HaN9M\nJTwus9cTeaPRvAHfkJvKzWvpCkg/Tp9IFfYXTYrzYW3d6DczsRre6z0HbAa0\nCRql227o0tcxQVq/FvO4C+FBAq8l6Lv7HENbL4IIl5Bw9PN9o6195+dZ+Ga1\n8rOb+sPKMCok2A2n0D+ypMbh75Vuh4iPnQYmKTSzFbL6W5O+oJTNC5f8oWOe\nTCzs\r\n=Zp3B\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testMatch": ["<rootDir>/test/**/*.js"], "testEnvironment": "node", "coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "collectCoverageFrom": ["lib/**/*.js", "!lib/utils.js"], "testPathIgnorePatterns": ["^<rootDir>/test/testharness.js$", "^<rootDir>/test/web-platform-tests/"]}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "058fa4fba3b1d183e0ef9f753434386c9667f19e", "scripts": {"lint": "eslint .", "test": "jest", "prepare": "node scripts/transform.js", "pretest": "node scripts/get-latest-platform-tests.js && node scripts/transform.js", "coverage": "jest --coverage", "build-live-viewer": "browserify index.js --standalone whatwgURL > live-viewer/whatwg-url.js"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "7.6.1", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "15.11.0", "dependencies": {"tr46": "^2.0.2", "lodash": "^4.7.0", "webidl-conversions": "^6.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.1", "glob": "^7.1.6", "jest": "^26.6.3", "eslint": "^7.20.0", "recast": "^0.20.4", "webidl2js": "^16.2.0", "browserify": "^17.0.0", "domexception": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url_8.5.0_1616617693964_0.9614243640934454", "host": "s3://npm-registry-packages"}}, "8.6.0": {"name": "whatwg-url", "version": "8.6.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@8.6.0", "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "27c0205a4902084b872aecb97cf0f2a7a3011f4c", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-8.6.0.tgz", "fileCount": 17, "integrity": "sha512-os0KkeeqUOl7ccdDT1qqUcS4KH4tcBTSKK5Nl5WKb2lyxInIZ/CpjkqKa1Ss12mjfdcRX9mHmPPs7/SxG1Hbdw==", "signatures": [{"sig": "MEUCIQDMppAi/UMPAxySYmNgsqOZIfIH324wvc9q2fhSrsRtTgIgD4Ay/c9Xwk41zs67LIx8T/W7+NJAgXpfO5+2Nq9QrKY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90634, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgv6BzCRA9TVsSAnZWagAA0kUP/1mUF0VXEHhGRW4Henya\nrM+globilO/XcqTX1T3Nq0J/5w5hWJn+r4lcKZdTQIChaZ3LVg6R2Xr/XmaR\nvuUY5pWAVK+cnv50zj4rAFu5IUwDKs5mpSrzYUIN+KSHJNZ4z/mE0kwj2+fa\nCYcagk+nBynlugQg6HmWXMm9ksbpe1XO5otbb/9Mn/vQhNulOEg14eBZ52eV\nR+8sNDmwNOuS1b2ouyqCac2BnrXaUj5wleOiJkGUAztJYTm0xozJnva0JRzg\nG1ouKDbTrF9p414xglYum2Dw5ntS9AO+CqSiFqw6LNAQVjA9QI5RKWT0sDO0\nASuYwYBVMEWkUYnhtMwjGjrt52PM4+lu3klyxc+MPKysRPtQFT2913VhQ5bD\nulM2kz4UaIJBBJCQqxpW5SBKdGVLTHFun3CG1w2tIWCvd4Bg15Y6+8/rcGcN\n1Kj5hfZ47EGfIOoHtUKwf58kR/1SssIqqoOiHfTBhuA/lUc1mrmPm/yGlHmr\npYEEdnKrVXe3LG/f0Ku6jTw3efUP50ujUg67uajMPY7Ke/nBG/2VU8TxM8n6\n8l5yThMOEEV50L62Y0LNiR3Sg1Xg7GfpK7shC6lyIlKXv9qTARsNdTMQza0E\nCLOFkrTRYXQZyxfOQAh9oWM5aFsw4K7GG/bcncoOE9YvB2UXDLv3fTD8EwVj\nUSy5\r\n=O3nk\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testMatch": ["<rootDir>/test/**/*.js"], "testEnvironment": "node", "coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "collectCoverageFrom": ["lib/**/*.js", "!lib/utils.js"], "testPathIgnorePatterns": ["^<rootDir>/test/testharness.js$", "^<rootDir>/test/web-platform-tests/"]}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "b5f41c62fb1c49175f690455db4e51e62ea749e3", "scripts": {"lint": "eslint .", "test": "jest", "prepare": "node scripts/transform.js", "pretest": "node scripts/get-latest-platform-tests.js && node scripts/transform.js", "coverage": "jest --coverage", "build-live-viewer": "browserify index.js --standalone whatwgURL > live-viewer/whatwg-url.js"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "15.13.0", "dependencies": {"tr46": "^2.1.0", "lodash": "^4.7.0", "webidl-conversions": "^6.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "glob": "^7.1.7", "jest": "^27.0.4", "eslint": "^7.28.0", "recast": "^0.20.4", "webidl2js": "^16.2.0", "browserify": "^17.0.0", "domexception": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url_8.6.0_1623171186924_0.46817678593978296", "host": "s3://npm-registry-packages"}}, "8.7.0": {"name": "whatwg-url", "version": "8.7.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@8.7.0", "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "656a78e510ff8f3937bc0bcbe9f5c0ac35941b77", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-8.7.0.tgz", "fileCount": 17, "integrity": "sha512-gAojqb/m9Q8a5IV96E3fHJM70AzCkgt4uXYX2O7EmuyOnLrViCQlsEBmF9UQIu3/aeAIp2U17rtbpZWNntQqdg==", "signatures": [{"sig": "MEUCIEHIjYbWYAEauqQc7uj+5JAeL+mbeC5ga4YnsnH/m7rvAiEAkKpMg0+WhJgApGgE8lWilZ73e2kpXZq+eEE1uD0jIoM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90738, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg1kQTCRA9TVsSAnZWagAATTIQAJVf5t4cAfyo1InhyhuC\nnpM7zhSQqLVOWjzQAznDZAaE8WpIdFqj+71KnofyyYZOjNF3AJxr77jSOWri\nw2Fv7zFkVINTChuRH88sXs9d4/kB6P8icWuanYenb2S4bQL4ZSb7Um7smZ2O\nNYQvBa1KeGEVG8fcTAkYFR8Df38W4C+GxMQU3n3pc7jCQXeM/1ypC1UW5/wO\nHgFq8PEcUM3TUm8CX3VG+UKhpmc3h2ndvp5Ag1Seo3Y6mcj0YqKgVmzvykq9\n2WMb0POfD5XlNj5pwTbr0bQh1SGTBX1dkkY4wmHmmMddb4DLnaq8O97jq9YY\nJkHjJZ0kKtuLIQb54x2qpnASSxY9X6SVhGrF2wSSukuMm+RqNCUThDHUKWLr\nT0I1BtZvbBORP4+1+AkDtAtskmwgcjq5RFOtqClvymhouNOXoysPO53dTTBJ\nxFtKSTHz0LSu5T513JFf0I09qpfATTp+ZEBEbCdrPe60B4g6j245JAwk4rEV\nj/Qk/l47QIQZqadWAR06/HBpHhD6pZrzavdc9/14JrLKGeZpwGvQscn7FgVY\nE4UE92c0cc73d3vVLU30cqVoA+QLSAi1QobQCuA9uGBuCF1VU2uvmi0UyfbE\nY7K30TYBoxU9MTdL2Okobn9NsK07AP+MiG+KKanFWgzoJ7g4mkJKP+FEYyPr\nXPQC\r\n=dbag\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testMatch": ["<rootDir>/test/**/*.js"], "testEnvironment": "node", "coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "collectCoverageFrom": ["lib/**/*.js", "!lib/utils.js"], "testPathIgnorePatterns": ["^<rootDir>/test/testharness.js$", "^<rootDir>/test/web-platform-tests/"]}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "c41e9e7b3b80c25d831e404bc8576f990c844180", "scripts": {"lint": "eslint .", "test": "jest", "prepare": "node scripts/transform.js", "pretest": "node scripts/get-latest-platform-tests.js && node scripts/transform.js", "coverage": "jest --coverage", "build-live-viewer": "browserify index.js --standalone whatwgURL > live-viewer/whatwg-url.js"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "16.4.0", "dependencies": {"tr46": "^2.1.0", "lodash": "^4.7.0", "webidl-conversions": "^6.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "glob": "^7.1.7", "jest": "^27.0.5", "eslint": "^7.29.0", "recast": "^0.20.4", "webidl2js": "^16.2.0", "browserify": "^17.0.0", "domexception": "^2.0.1", "@domenic/eslint-config": "^1.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url_8.7.0_1624654866887_0.7115969270687144", "host": "s3://npm-registry-packages"}}, "9.0.0": {"name": "whatwg-url", "version": "9.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@9.0.0", "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "7de99809eaaba2bff7e0a995a516b26aa789fb89", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-9.0.0.tgz", "fileCount": 17, "integrity": "sha512-KGnRJgaFqeUnHQH/3KbcBh8VB6nKCodfzmBih0lizk+FlETQ+IYti4WbV2GL5D5A0++79nizlMwNaQCtxmwL7w==", "signatures": [{"sig": "MEUCIQDp9lIEUZ9k+I6VN9HF5mBABKGH0TdhCgarsM0JPFSQIwIgLWuOtAF58PjC8/AXLGeHP0W23jUtUHT5nPOrCJqIAvg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90418, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg15n5CRA9TVsSAnZWagAADpwP/0md6oAD3oHm6ryEKVbJ\n3ZWIZ1hIjDe/Uqb8YRCUDFTbgUWqgL+/QF1mt3ZKgY8IW1tWLideXEtOFSi4\nTCKw6/BaMVq+mVIKSKpEZ0JAfnubhLsy+70fxg2kzH30JYJa9f2/WTwCI4mT\ndZv36Rch8oeKlpTBuMnWPdzAkULFRv/Q+CHzUrUToVYpL7fRMd+GL9jBcXVc\nGPe+UheN6MRCv5mH8hnifSAL9KCYsNRQud1TKQoL59GBqrW2b20VoIXUxMU+\nt2h3MB2Xj+bLSUeO81g8bhhv9jzH3P9Zb0Lg/hnCLU+MLVkG1eHy2HSk2hcg\nDb0FTainz5ghpva23W/mdDQSEKweY/YRtvJGLrD+30u9cPqeH6jXKEILkpDG\nT8wbuubPSyMZfwWZ45OkEILqMqkX+a8mEKOMS64AGsGEAEbs4pqB/S4Dz62N\nDjvHDsQkNz4nwQeotcv/kaUsHCLLvX3yXTgk73nFrUPHWiAJ7sAj+mEZBshO\nO+nMPQl/sILDD32w4vWVECKNlbUd/k2pm7muRuyzNNFPk7JKmMJnd1CGaRKa\n9S0DwgFMcp5NhFcbPqNMsmdvFj04NZN0wzcaheOfEzl04Q7hq6A+cneDLoju\nuc5F+ZQVz7du5RKi0b6EAmgVh7SGr05S6oAGX5S7dzY4fuyr2r3ykBBOnBWa\nDyeh\r\n=d0vd\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testMatch": ["<rootDir>/test/**/*.js"], "testEnvironment": "node", "coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "collectCoverageFrom": ["lib/**/*.js", "!lib/utils.js"], "testPathIgnorePatterns": ["^<rootDir>/test/testharness.js$", "^<rootDir>/test/web-platform-tests/"]}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "a96b4f93f2338aad01b21160d9ceb6eea628942b", "scripts": {"lint": "eslint .", "test": "jest", "prepare": "node scripts/transform.js", "pretest": "node scripts/get-latest-platform-tests.js && node scripts/transform.js", "coverage": "jest --coverage", "build-live-viewer": "browserify index.js --standalone whatwgURL > live-viewer/whatwg-url.js"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "16.4.0", "dependencies": {"tr46": "^2.1.0", "webidl-conversions": "^6.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "glob": "^7.1.7", "jest": "^27.0.5", "eslint": "^7.29.0", "recast": "^0.20.4", "webidl2js": "^16.2.0", "browserify": "^17.0.0", "domexception": "^2.0.1", "@domenic/eslint-config": "^1.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url_9.0.0_1624742392535_0.004081947534856578", "host": "s3://npm-registry-packages"}}, "9.1.0": {"name": "whatwg-url", "version": "9.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@9.1.0", "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "1b112cf237d72cd64fa7882b9c3f6234a1c3050d", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-9.1.0.tgz", "fileCount": 17, "integrity": "sha512-CQ0UcrPHyomtlOCot1TL77WyMIm/bCwrJ2D6AOKGwEczU9EpyoqAokfqrf/MioU9kHcMsmJZcg1egXix2KYEsA==", "signatures": [{"sig": "MEYCIQCXtvCnGLzughWbZomXPbNZ0rATkZrnGAslMjZzYgFc5QIhANkaYRq40+7Bh+HXuIphFqGnH2vPiIbqmCVp1MGinaWj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90727, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhDAnlCRA9TVsSAnZWagAAoxEP/jPy1Q4lA0+JkPJ4FLXz\nOtsGeFhIAZms9SAydI6RpDmS2nbri02empw1UA1gyGHIwuqAJq33oVRzig4D\n4BLL9cZHYZKQyg8nUkFvsugHum0gSBjcnejE00hYx+s/uOCtvM7oMbbV/Ptf\nyqaJPuFbTGmJs5yjtQ7K16LO/NCs5PljMTwnlOKS3xMzD9WpOyOPu+R8qMrJ\nHx53Yu3NOAYHzXIOoqhm3q29s5rxYLFHDPjykhHRACezG3dtmR7dhxvEcz1L\ni2t6Ua6Piie8McOc4RyiF+aeSlmIxHaYa4eT7oBFlTO/QOZ6AZSwP24EflGK\niotTaTGum5fAwxvIdm74NDcE4tUF7cl6XG7OhAbGbeU06vGEQYluCjYzggRq\npB8fIDXNiyKDyFI1WVY2yo50Qdz6F7xYfvNfJ+1TY5Osl4X3Sj3iuLijjov/\nFLhUwVwTekj8lA9lqtKSv2oL5OUQnaXMBp8ZRcoNkbsBficDYyuDTY/zIdQ5\n2lXSJ8EhNPurD4jGOuC8+8/tQJ0QXqallH1D3Nn5eE5iVs4Zdm+uYaNXWNd1\nM+nOPobWMIRmi/KVy8Z6NESBPPKH1HtPVKMWb0ofXKJ1nvrVND1asi1IahYq\nxA/soz/nNgVjGImCQzjoEEBwPzs1Vz94HTIBE4pUHyplhWdevdczWdvgmwbX\nOdVu\r\n=PKlZ\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testMatch": ["<rootDir>/test/**/*.js"], "testEnvironment": "node", "coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "collectCoverageFrom": ["lib/**/*.js", "!lib/utils.js"], "testPathIgnorePatterns": ["^<rootDir>/test/testharness.js$", "^<rootDir>/test/web-platform-tests/"]}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "210a7a694a661fd1163ff623a2e074ca72981c42", "scripts": {"lint": "eslint .", "test": "jest", "prepare": "node scripts/transform.js", "pretest": "node scripts/get-latest-platform-tests.js && node scripts/transform.js", "coverage": "jest --coverage", "build-live-viewer": "browserify index.js --standalone whatwgURL > live-viewer/whatwg-url.js"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "7.19.1", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "16.6.0", "dependencies": {"tr46": "^2.1.0", "webidl-conversions": "^6.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "glob": "^7.1.7", "jest": "^27.0.5", "eslint": "^7.29.0", "recast": "^0.20.4", "webidl2js": "^16.2.0", "browserify": "^17.0.0", "domexception": "^2.0.1", "@domenic/eslint-config": "^1.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url_9.1.0_1628178917140_0.627897498213732", "host": "s3://npm-registry-packages"}}, "10.0.0": {"name": "whatwg-url", "version": "10.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@10.0.0", "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "37264f720b575b4a311bd4094ed8c760caaa05da", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-10.0.0.tgz", "fileCount": 17, "integrity": "sha512-CLxxCmdUby142H5FZzn4D8ikO1cmypvXVQktsgosNy4a4BHrDHeciBBGZhb0bNoR5/MltoCatso+vFjjGx8t0w==", "signatures": [{"sig": "MEUCIQCQl2hZ9hDVCb/fklWMe2UVpzkUv/WUI76oIwTlnletjgIgNWS+OlS/WLUff3cEQgQ2gIjtBc0ijAyqNEJMDQGq17Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 94484}, "jest": {"testMatch": ["<rootDir>/test/**/*.js"], "testEnvironment": "node", "coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "collectCoverageFrom": ["lib/**/*.js", "!lib/utils.js"], "testPathIgnorePatterns": ["^<rootDir>/test/testharness.js$", "^<rootDir>/test/web-platform-tests/"]}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "e2d79901a707851f43c78c3a34e2d3bf83e572e1", "scripts": {"lint": "eslint .", "test": "jest", "prepare": "node scripts/transform.js", "pretest": "node scripts/get-latest-platform-tests.js && node scripts/transform.js", "coverage": "jest --coverage", "build-live-viewer": "browserify index.js --standalone whatwgURL > live-viewer/whatwg-url.js"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "7.21.1", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "16.9.1", "dependencies": {"tr46": "^3.0.0", "webidl-conversions": "^7.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "jest": "^27.2.4", "eslint": "^7.32.0", "benchmark": "^2.1.4", "webidl2js": "^17.0.0", "browserify": "^17.0.0", "domexception": "^4.0.0", "@domenic/eslint-config": "^1.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url_10.0.0_1633539039867_0.20033015988023606", "host": "s3://npm-registry-packages"}}, "11.0.0": {"name": "whatwg-url", "version": "11.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@11.0.0", "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "0a849eebb5faf2119b901bb76fd795c2848d4018", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-11.0.0.tgz", "fileCount": 17, "integrity": "sha512-RKT8HExMpoYx4igMiVMY83lN6UeITKJlBQ+vR/8ZJ8OCdSiN3RwCq+9gH0+Xzj0+5IrM6i4j/6LuvzbZIQgEcQ==", "signatures": [{"sig": "MEQCIE8Fiv0ttqUr/0x8eqss8Ei5Kk2N83QBbnj7FqK+c5x1AiAhajxOYEsjSyMNOh+tHfs0rcwiDcvnzDgAWN8BIpI1Ww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 94695, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3BQFCRA9TVsSAnZWagAAX7wP/0qEO0DMedb3TWPrPgnz\nGXKi4hMxdVzx7ABUYR2P3vIxzZcOgwm1oGkgMz2yHjKlItCnZFNA9xHJdF8n\nJ5bhu+XoTd47jNXdS7nPYV2PtGHS05jxsCj+y620sr0xbrcgZqFFmZTY1hV4\nI4Xy7AAaBfXLEL5dYpQO9q6kye3jo6OOBpLdG5lODKiAQTVyP2XTWFo5wBfh\n34bMvab7CN1vbFtuajGSRrJpUK0byBN7B4c9+cxH/jmPNZzCld4Jzc8P9Tcz\np5OceXYdHf3f14xg9r0qjUY8tYq3RBgr0K0y2hHXYQmJqPrG6nQmJ/2NqHqc\nHXGgO4qNucwhhNAIYHLuvV5UtijEO25pNHMQAkbs+k7Dbj2IBTtur3AIc3Z6\nm2LB2f+fqkw/8Chd/S8PumM/xs7zmz4YbfLQUv5AQq8No17NeatbT5YAYd84\nOLmRnFrwwFfid+sQYnxObB5eg7JTwUkea0jXoyQk20quBMYz6Pv3Gcq76Xtw\n7r99gxnMsy6yAQ8dcGxPl8fRvuq8tl8Cwb/lKzitEFM4m3ShIsf/crb/7qo4\n/IkfJXMAgySM6SLdq7OJvXlwCbsZIojtxtoXzg6t1g3JM7lo+SHC2+svbzHZ\nDf52AcBZ002YXRSJhJY4Y8/nEdCgeY6ff7b2YJWc6poWb9Ui76H1rmBNRpGS\njpBc\r\n=8fP+\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testMatch": ["<rootDir>/test/**/*.js"], "testEnvironment": "node", "coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "collectCoverageFrom": ["lib/**/*.js", "!lib/utils.js"], "testPathIgnorePatterns": ["^<rootDir>/test/testharness.js$", "^<rootDir>/test/web-platform-tests/"]}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "a21a69a589a2ac4433d2d0c1ab8064f2ee243fbd", "scripts": {"lint": "eslint .", "test": "jest", "prepare": "node scripts/transform.js", "pretest": "node scripts/get-latest-platform-tests.js && node scripts/transform.js", "coverage": "jest --coverage", "build-live-viewer": "browserify index.js --standalone whatwgURL > live-viewer/whatwg-url.js"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "8.0.0", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "16.11.1", "dependencies": {"tr46": "^3.0.0", "webidl-conversions": "^7.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "jest": "^27.2.4", "eslint": "^7.32.0", "benchmark": "^2.1.4", "webidl2js": "^17.0.0", "browserify": "^17.0.0", "domexception": "^4.0.0", "@domenic/eslint-config": "^1.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url_11.0.0_1634673614883_0.34639570455135504", "host": "s3://npm-registry-packages"}}, "12.0.0": {"name": "whatwg-url", "version": "12.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@12.0.0", "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "cf5798db0f8036de3fffabbcf92180d313811710", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-12.0.0.tgz", "fileCount": 17, "integrity": "sha512-N2SCrbcSPw0gDyNqE+y5qH1gqiOe+HagWfgRJy2SmDO3C23mISmJhQ3zvZljBv2DWfBdLXypAtecWYZ+mg1krQ==", "signatures": [{"sig": "MEUCIQDvj6ZYBlpSXyyEKwER96zrQoU+mlLaX5qEyMXC4E0UnQIgbUL9s3B9+Ib+phkYVpNVeCA9stacM0rgHRpwCPchPKQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 95250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjs433ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq4iBAAhgM+sb2f7yXe4/kPYZ7aHuaKw+EBIzzf22CUUH4ImyLqu5J5\r\nmjvbevnaO77Xe/LYuLw3bw0TtJMSwSoOxJMeAVrdiLnYQPOZyK+3cLJNTUTp\r\nicmrAcp2AZXBaO4YYmRDO7aGGiLD0EvoYQa3yJvDiCBB0Q48fY9u43yZCgD/\r\nFjtTUoXzdG/f+xJXGlK/V6/PGmwh81kcWsv0g+FRiU5ni8ZKYdSsG2cpWlv2\r\nC2lGpH3qz+TSK/MqxYMILvoxUgW7sFEmqFQdRhQTu1YpFBKwwldSMCWi3oVE\r\nzT9IapAduCZ0Bo5MqvcW7EZowLLPE/DxP0YrmVFFqPcGUI7wOrH7IGjs0Ncw\r\nL/slAeWVUKwXcPjDPCfEzymnobXEyouw5rzqKaQXnVy8K+w5OK6zyiazH03D\r\nuM2WXemNEMPRlBlcY5e6G7baPwRUOWo8Mk+xoMAqZCcWh84Pg6H4Zyb4jlC5\r\n2itISWcMnPE4YYm0sWDWIv6jbnYH51GqPUjR+aYzmm2oY3ItUwQGc+1yIOd9\r\nUO/s43kUPX1+BxwiIZtiRfDP62e4hEHdpmwhYrBHRLAeke9WvN492RcWIu6K\r\nfKEoxIjzMoG2sqJGUGWYRQshz5zc7+l7eSNPSDFUUWZmF8WPqiBSmOZf7DTo\r\nd2nGujpuVhwJHjh3QbN08skdkkTal34ZxvE=\r\n=/Blt\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testMatch": ["<rootDir>/test/**/*.js"], "testEnvironment": "node", "coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "collectCoverageFrom": ["lib/**/*.js", "!lib/utils.js"], "testPathIgnorePatterns": ["^<rootDir>/test/testharness.js$", "^<rootDir>/test/web-platform-tests/"]}, "main": "index.js", "engines": {"node": ">=14"}, "gitHead": "c61d5b4fa602fa99d6b24f563d6f12a03f718b21", "scripts": {"lint": "eslint .", "test": "jest", "prepare": "node scripts/transform.js", "pretest": "node scripts/get-latest-platform-tests.js && node scripts/transform.js", "coverage": "jest --coverage", "build-live-viewer": "esbuild --bundle --format=esm --sourcemap --outfile=live-viewer/whatwg-url.mjs index.js"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "19.1.0", "dependencies": {"tr46": "^3.0.0", "webidl-conversions": "^7.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.3.1", "eslint": "^8.30.0", "esbuild": "^0.16.12", "benchmark": "^2.1.4", "webidl2js": "^17.1.0", "domexception": "^4.0.0", "minipass-fetch": "^3.0.1", "@domenic/eslint-config": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url_12.0.0_1672711671606_0.6466384149945548", "host": "s3://npm-registry-packages"}}, "12.0.1": {"name": "whatwg-url", "version": "12.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@12.0.1", "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "fd7bcc71192e7c3a2a97b9a8d6b094853ed8773c", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-12.0.1.tgz", "fileCount": 17, "integrity": "sha512-Ed/LrqB8EPlGxjS+TrsXcpUond1mhccS3pchLhzSgPCnTimUCKj3IZE75pAs5m6heB2U2TMerKFUXheyHY+VDQ==", "signatures": [{"sig": "MEYCIQCJIOSWwUUfbICFJFD3mvFpdClzDRE/HpDRGyxFHTqdmwIhALq7hVrisotMfauF+/BO/pQmB0VKtgvyA7pnQp5kSJq1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 95250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkCDbnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmof6RAAlQZIqeRSmRRzQBFReXDzRkvvDZgsMDst8+VTGWWb5v/VwIYT\r\nHv2ap0x74Sb+g1yl/SibwMxm2Q7qEgiWMKljj8xZ8/4K4SmMSJ9FzUiFNrix\r\nWq4810bsI/1tP4NA6lBuEBkgAGmjEcWJED/umpusc6SNNlv7p6wTM8hyKd5U\r\nBsLwIA8IQrXO2opiBDVsAsvJP9zunuqNeTo+ibGvhDyFnaw26c6P/9Mb+Dm6\r\nDihaKj54xGsBoz1L2VEZmqYYvUP2SbE0zUJOpSCJB3lx0Xsc1sDSzUYAplwG\r\noqs0rtF7Fv5fFZDvKUfQfYw41VRNyYTZfIogNQNT7lINZwdiZQYH+G10qWK4\r\ntohllFQy/F2nmQNvGNZLGs0Ug+vE02skbi1nC3HAJcMyswhVQqyxdUJiLAlD\r\nRxQqVc79VFcV29HgBhv/7XPYBNml8M6m9A1H92mbwJ3Re8TPQ5uGDCb6Tqll\r\n/zIcOkyKCy7AHdxW/vO+xZrOYacoUZ9NLeJ8tRqhKR5kkO/c5Ht1JdSIJNOy\r\neujaziBN+MEWm24UMwRTH6d/VaQRc9YVi8f/EGYqUOKsMMhRa69Gn0CvDCbL\r\nFO0hKoqiKAfqWM/LWrr3flUthtK21J+n0N+JRuNrT+fXpoCxoIQ2fiR2QAs4\r\nH4BSsTFRi9lz4BRPc0/5o5989GA26CAkErc=\r\n=M+wz\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testMatch": ["<rootDir>/test/**/*.js"], "testEnvironment": "node", "coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "collectCoverageFrom": ["lib/**/*.js", "!lib/utils.js"], "testPathIgnorePatterns": ["^<rootDir>/test/testharness.js$", "^<rootDir>/test/web-platform-tests/"]}, "main": "index.js", "engines": {"node": ">=14"}, "gitHead": "423b9fad5c8f260d06bbd807bda7eb558fce3260", "scripts": {"lint": "eslint .", "test": "jest", "prepare": "node scripts/transform.js", "pretest": "node scripts/get-latest-platform-tests.js && node scripts/transform.js", "coverage": "jest --coverage", "build-live-viewer": "esbuild --bundle --format=esm --sourcemap --outfile=live-viewer/whatwg-url.mjs index.js"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "19.7.0", "dependencies": {"tr46": "^4.1.1", "webidl-conversions": "^7.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.5.0", "eslint": "^8.35.0", "esbuild": "^0.17.11", "benchmark": "^2.1.4", "webidl2js": "^17.1.0", "domexception": "^4.0.0", "minipass-fetch": "^3.0.1", "@domenic/eslint-config": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url_12.0.1_1678259943165_0.8303601655605488", "host": "s3://npm-registry-packages"}}, "13.0.0": {"name": "whatwg-url", "version": "13.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@13.0.0", "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "dist": {"shasum": "b7b536aca48306394a34e44bda8e99f332410f8f", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-13.0.0.tgz", "fileCount": 17, "integrity": "sha512-9WWbymnqj57+XEuqADHrCJ2eSXzn8WXIW/YSGaZtb2WKAInQ6CHfaUUcTyyver0p8BDg5StLQq8h1vtZuwmOig==", "signatures": [{"sig": "MEYCIQCrL9bkQfVluLFQdzVHyqLNwgkbedGmrw3YcnmIi9vadAIhAJiyNq8nUnZ7iZKQ4XaU7XuRf5sOx/U8TThZl0Xh8gGQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97976}, "jest": {"testMatch": ["<rootDir>/test/**/*.js"], "testEnvironment": "node", "coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "collectCoverageFrom": ["lib/**/*.js", "!lib/utils.js"], "testPathIgnorePatterns": ["^<rootDir>/test/testharness.js$", "^<rootDir>/test/web-platform-tests/"]}, "main": "index.js", "engines": {"node": ">=16"}, "gitHead": "64edabb353e9f69af4385ac6332b5ab624684747", "scripts": {"lint": "eslint .", "test": "jest", "prepare": "node scripts/transform.js", "pretest": "node scripts/get-latest-platform-tests.js && node scripts/transform.js", "coverage": "jest --coverage", "build-live-viewer": "esbuild --bundle --format=esm --sourcemap --outfile=live-viewer/whatwg-url.mjs index.js"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"tr46": "^4.1.1", "webidl-conversions": "^7.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.5.0", "eslint": "^8.41.0", "esbuild": "^0.17.19", "benchmark": "^2.1.4", "webidl2js": "^17.1.0", "domexception": "^4.0.0", "minipass-fetch": "^3.0.3", "@domenic/eslint-config": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url_13.0.0_1685262853808_0.11358340651737486", "host": "s3://npm-registry-packages"}}, "14.0.0": {"name": "whatwg-url", "version": "14.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@14.0.0", "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "c8": {"exclude": ["lib/Function.js", "lib/URL.js", "lib/URLSearchParams.js", "lib/utils.js", "scripts/", "test/"], "reporter": ["text", "html"]}, "dist": {"shasum": "00baaa7fd198744910c4b1ef68378f2200e4ceb6", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-14.0.0.tgz", "fileCount": 17, "integrity": "sha512-1lfMEm2IEr7RIV+f4lUNPOqfFL+pO+Xw3fJSqmjX9AbXcXcYOkCe1P6+9VBZB6n94af16NfZf+sSk0JCBZC9aw==", "signatures": [{"sig": "MEYCIQDqY8Orgym9YDdlv8H3yexTTsn0e+ho3dScI9VMnkKj0QIhAN1LjIU/SsXmcyrdIvQljE1b9HtLHqz5RokgcNVMpiMA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97803}, "main": "index.js", "engines": {"node": ">=18"}, "gitHead": "6c6fb1771f9d12d7174d586ebbfc8f2db401557a", "scripts": {"lint": "eslint .", "test": "node --test test/*.js", "prepare": "node scripts/transform.js", "pretest": "node scripts/get-latest-platform-tests.js && node scripts/transform.js", "coverage": "c8 node --test --experimental-test-coverage test/*.js", "build-live-viewer": "esbuild --bundle --format=esm --sourcemap --outfile=live-viewer/whatwg-url.mjs index.js"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "10.2.0", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "21.1.0", "dependencies": {"tr46": "^5.0.0", "webidl-conversions": "^7.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^8.0.1", "eslint": "^8.53.0", "esbuild": "^0.19.5", "benchmark": "^2.1.4", "webidl2js": "^17.1.0", "domexception": "^4.0.0", "@domenic/eslint-config": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url_14.0.0_1699193937969_0.5104030376808613", "host": "s3://npm-registry-packages"}}, "14.1.0": {"name": "whatwg-url", "version": "14.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@14.1.0", "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "c8": {"exclude": ["lib/Function.js", "lib/URL.js", "lib/URLSearchParams.js", "lib/utils.js", "scripts/", "test/"], "reporter": ["text", "html"]}, "dist": {"shasum": "fffebec86cc8e6c2a657e50dc606207b870f0ab3", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-14.1.0.tgz", "fileCount": 17, "integrity": "sha512-jlf/foYIKywAt3x/XWKZ/3rz8OSJPiWktjmk891alJUEjiVxKX9LEO92qH3hv4aJ0mN3MWPvGMCy8jQi95xK4w==", "signatures": [{"sig": "MEUCIQDl9FNLSWA7FLwc5JpPJ0uLYHd7CkonpetAYpWWD8OaswIgSSAhmx9ktP25eDyyapKFY78c/sasLcx+FGLTNNqG4qY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98895}, "main": "index.js", "engines": {"node": ">=18"}, "gitHead": "807353d966f73cdf62f852230b699bbf5c9cefca", "scripts": {"lint": "eslint", "test": "node --test test/*.js", "prepare": "node scripts/transform.js", "pretest": "node scripts/get-latest-platform-tests.js && node scripts/transform.js", "coverage": "c8 node --test --experimental-test-coverage test/*.js", "build-live-viewer": "esbuild --bundle --format=esm --sourcemap --outfile=live-viewer/whatwg-url.mjs index.js"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "22.11.0", "dependencies": {"tr46": "^5.0.0", "webidl-conversions": "^7.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^10.1.2", "eslint": "^9.16.0", "esbuild": "^0.24.0", "globals": "^15.13.0", "benchmark": "^2.1.4", "webidl2js": "^18.0.0", "@domenic/eslint-config": "^4.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url_14.1.0_1733277434728_0.9361355166842509", "host": "s3://npm-registry-packages"}}, "14.1.1": {"name": "whatwg-url", "version": "14.1.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "whatwg-url@14.1.1", "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "homepage": "https://github.com/jsdom/whatwg-url#readme", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "c8": {"exclude": ["lib/Function.js", "lib/URL.js", "lib/URLSearchParams.js", "lib/utils.js", "scripts/", "test/"], "reporter": ["text", "html"]}, "dist": {"shasum": "ce71e240c61541315833b5cdafd139a479e47058", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-14.1.1.tgz", "fileCount": 17, "integrity": "sha512-mDGf9diDad/giZ/Sm9Xi2YcyzaFpbdLpJPr+E9fSkyQ7KpQD4SdFcugkRQYzhmfI4KeV4Qpnn2sKPdo+kmsgRQ==", "signatures": [{"sig": "MEYCIQDQgfvCLliH/qSWiAgXQ1GqmTn0BsNXZ+N40RIHJRrD3QIhAJ+ujPKnf0Tkf7DPqEa8C2yMYgRW6BZ7an3J3L0q0Nsy", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 99235}, "main": "index.js", "engines": {"node": ">=18"}, "gitHead": "0dfbc79cc48342a906ccaba66cc7b1ee5c5cb507", "scripts": {"lint": "eslint", "test": "node --test test/*.js", "bench": "node scripts/benchmark.js", "prepare": "node scripts/transform.js", "pretest": "node scripts/get-latest-platform-tests.js && node scripts/transform.js", "coverage": "c8 node --test --experimental-test-coverage test/*.js", "build-live-viewer": "esbuild --bundle --format=esm --sourcemap --outfile=live-viewer/whatwg-url.mjs index.js"}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jsdom/whatwg-url.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "directories": {}, "_nodeVersion": "23.7.0", "dependencies": {"tr46": "^5.0.0", "webidl-conversions": "^7.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^10.1.2", "eslint": "^9.16.0", "esbuild": "^0.24.0", "globals": "^15.13.0", "benchmark": "^2.1.4", "webidl2js": "^18.0.0", "@domenic/eslint-config": "^4.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/whatwg-url_14.1.1_1739263973016_0.9176991696890151", "host": "s3://npm-registry-packages-npm-production"}}, "14.2.0": {"name": "whatwg-url", "version": "14.2.0", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "main": "index.js", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jsdom/whatwg-url.git"}, "dependencies": {"tr46": "^5.1.0", "webidl-conversions": "^7.0.0"}, "devDependencies": {"@domenic/eslint-config": "^4.0.1", "benchmark": "^2.1.4", "c8": "^10.1.3", "esbuild": "^0.25.1", "eslint": "^9.22.0", "globals": "^16.0.0", "webidl2js": "^18.0.0"}, "engines": {"node": ">=18"}, "scripts": {"coverage": "c8 node --test --experimental-test-coverage test/*.js", "lint": "eslint", "prepare": "node scripts/transform.js", "pretest": "node scripts/get-latest-platform-tests.js && node scripts/transform.js", "build-live-viewer": "esbuild --bundle --format=esm --sourcemap --outfile=live-viewer/whatwg-url.mjs index.js", "test": "node --test test/*.js", "bench": "node scripts/benchmark.js"}, "c8": {"reporter": ["text", "html"], "exclude": ["lib/Function.js", "lib/URL.js", "lib/URLSearchParams.js", "lib/utils.js", "scripts/", "test/"]}, "_id": "whatwg-url@14.2.0", "gitHead": "414f17a3459b0872baee7a2b77e23953b8a5ccd9", "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "homepage": "https://github.com/jsdom/whatwg-url#readme", "_nodeVersion": "23.7.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-De72GdQZzNTUBBChsXueQUnPKDkg/5A5zp7pFDuQAj5UFoENpiACU0wlCvzpAGnTkj++ihpKwKyYewn/XNUbKw==", "shasum": "4ee02d5d725155dae004f6ae95c73e7ef5d95663", "tarball": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-14.2.0.tgz", "fileCount": 17, "unpackedSize": 98558, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDDwtNT4nXObRhvdP9yEeuTkn825rEZ8BV//O1ec/UMtwIgCcoxF6hi4Ff8T7v4XF3A4yKYKDYrm6vAx5gQXH69Wng="}]}, "_npmUser": {"name": "domenic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/whatwg-url_14.2.0_1742022761716_0.5025897377834339"}, "_hasShrinkwrap": false}}, "time": {"created": "2015-06-04T13:09:58.924Z", "modified": "2025-03-15T07:12:42.131Z", "0.0.1": "2015-06-04T13:09:58.924Z", "0.1.0": "2015-06-04T13:49:20.087Z", "0.2.0": "2015-06-07T13:27:26.272Z", "0.2.1": "2015-06-08T03:00:29.433Z", "0.3.0": "2015-07-21T08:32:04.766Z", "0.3.1": "2015-07-21T11:07:58.642Z", "0.4.0": "2015-07-23T09:45:41.021Z", "0.4.1": "2015-07-23T16:20:48.729Z", "0.4.2": "2015-07-31T20:07:08.825Z", "0.5.0": "2015-08-01T19:13:35.551Z", "0.6.0": "2015-08-01T21:44:09.517Z", "0.6.1": "2015-08-02T23:10:21.150Z", "0.6.2": "2015-08-13T01:35:41.324Z", "0.6.3": "2015-08-29T02:44:57.335Z", "0.6.4": "2015-08-29T02:47:13.953Z", "0.6.5": "2015-08-30T13:58:56.358Z", "1.0.0": "2016-01-27T22:03:42.087Z", "1.0.1": "2016-02-10T18:07:15.148Z", "2.0.0": "2016-04-06T21:49:21.279Z", "2.0.1": "2016-04-15T22:12:37.778Z", "3.0.0": "2016-05-28T19:32:27.252Z", "3.1.0": "2016-11-20T03:22:42.852Z", "4.0.0": "2016-11-20T03:30:12.579Z", "4.1.0": "2016-12-09T17:46:30.354Z", "4.1.1": "2016-12-19T00:09:32.418Z", "4.2.0": "2017-01-03T22:12:46.910Z", "4.3.0": "2017-01-12T00:40:31.291Z", "4.4.0": "2017-02-16T20:30:05.197Z", "4.5.0": "2017-02-22T17:43:59.019Z", "4.5.1": "2017-03-12T19:21:11.865Z", "4.6.0": "2017-03-15T20:09:29.895Z", "4.7.0": "2017-04-03T05:17:23.819Z", "4.7.1": "2017-04-24T20:44:44.321Z", "4.8.0": "2017-05-10T18:56:43.181Z", "5.0.0": "2017-05-26T20:56:41.955Z", "6.0.0": "2017-06-29T02:43:14.188Z", "6.0.1": "2017-06-29T03:44:24.331Z", "6.1.0": "2017-06-30T00:15:50.961Z", "6.2.0": "2017-09-12T00:23:26.069Z", "6.2.1": "2017-09-12T00:34:10.664Z", "6.3.0": "2017-09-25T00:55:39.030Z", "6.4.0": "2017-12-05T22:39:22.006Z", "6.4.1": "2018-04-22T19:15:29.579Z", "6.5.0": "2018-06-08T20:37:32.015Z", "7.0.0": "2018-08-18T19:34:38.926Z", "7.1.0": "2019-10-21T16:04:29.957Z", "8.0.0": "2020-01-02T20:10:52.787Z", "8.1.0": "2020-05-08T19:07:00.483Z", "8.2.0": "2020-08-24T15:56:44.690Z", "8.2.1": "2020-08-25T15:52:55.895Z", "8.2.2": "2020-09-06T19:29:24.032Z", "8.3.0": "2020-09-30T17:37:14.436Z", "8.4.0": "2020-10-08T16:17:51.606Z", "8.5.0": "2021-03-24T20:28:14.163Z", "8.6.0": "2021-06-08T16:53:07.119Z", "8.7.0": "2021-06-25T21:01:07.287Z", "9.0.0": "2021-06-26T21:19:52.676Z", "9.1.0": "2021-08-05T15:55:17.332Z", "10.0.0": "2021-10-06T16:50:40.037Z", "11.0.0": "2021-10-19T20:00:15.053Z", "12.0.0": "2023-01-03T02:07:51.777Z", "12.0.1": "2023-03-08T07:19:03.350Z", "13.0.0": "2023-05-28T08:34:14.013Z", "14.0.0": "2023-11-05T14:18:58.221Z", "14.1.0": "2024-12-04T01:57:14.913Z", "14.1.1": "2025-02-11T08:52:53.202Z", "14.2.0": "2025-03-15T07:12:41.904Z"}, "bugs": {"url": "https://github.com/jsdom/whatwg-url/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/jsdom/whatwg-url#readme", "repository": {"type": "git", "url": "git+https://github.com/jsdom/whatwg-url.git"}, "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "maintainers": [{"name": "timothy<PERSON>", "email": "<EMAIL>"}, {"name": "domenic", "email": "<EMAIL>"}, {"name": "sebmaster", "email": "<EMAIL>"}, {"name": "zirro", "email": "<EMAIL>"}, {"name": "tmpvar", "email": "<EMAIL>"}, {"name": "j<PERSON>-<PERSON><PERSON><PERSON>-wel", "email": "<EMAIL>"}], "readme": "﻿# whatwg-url\n\nwhatwg-url is a full implementation of the WHATWG [URL Standard](https://url.spec.whatwg.org/). It can be used standalone, but it also exposes a lot of the internal algorithms that are useful for integrating a URL parser into a project like [jsdom](https://github.com/jsdom/jsdom).\n\n## Specification conformance\n\nwhatwg-url is currently up to date with the URL spec up to commit [6c78200](https://github.com/whatwg/url/commit/6c782003a2d53b1feecd072d1006eb8f1d65fb2d).\n\nFor `file:` URLs, whose [origin is left unspecified](https://url.spec.whatwg.org/#concept-url-origin), whatwg-url chooses to use a new opaque origin (which serializes to `\"null\"`).\n\nwhatwg-url does not yet implement any encoding handling beyond UTF-8. That is, the _encoding override_ parameter does not exist in our API.\n\n## API\n\n### The `URL` and `URLSearchParams` classes\n\nThe main API is provided by the [`URL`](https://url.spec.whatwg.org/#url-class) and [`URLSearchParams`](https://url.spec.whatwg.org/#interface-urlsearchparams) exports, which follows the spec's behavior in all ways (including e.g. `USVString` conversion). Most consumers of this library will want to use these.\n\n### Low-level URL Standard API\n\nThe following methods are exported for use by places like jsdom that need to implement things like [`HTMLHyperlinkElementUtils`](https://html.spec.whatwg.org/#htmlhyperlinkelementutils). They mostly operate on or return an \"internal URL\" or [\"URL record\"](https://url.spec.whatwg.org/#concept-url) type.\n\n- [URL parser](https://url.spec.whatwg.org/#concept-url-parser): `parseURL(input, { baseURL })`\n- [Basic URL parser](https://url.spec.whatwg.org/#concept-basic-url-parser): `basicURLParse(input, { baseURL, url, stateOverride })`\n- [URL serializer](https://url.spec.whatwg.org/#concept-url-serializer): `serializeURL(urlRecord, excludeFragment)`\n- [Host serializer](https://url.spec.whatwg.org/#concept-host-serializer): `serializeHost(hostFromURLRecord)`\n- [URL path serializer](https://url.spec.whatwg.org/#url-path-serializer): `serializePath(urlRecord)`\n- [Serialize an integer](https://url.spec.whatwg.org/#serialize-an-integer): `serializeInteger(number)`\n- [Origin](https://url.spec.whatwg.org/#concept-url-origin) [serializer](https://html.spec.whatwg.org/multipage/origin.html#ascii-serialisation-of-an-origin): `serializeURLOrigin(urlRecord)`\n- [Set the username](https://url.spec.whatwg.org/#set-the-username): `setTheUsername(urlRecord, usernameString)`\n- [Set the password](https://url.spec.whatwg.org/#set-the-password): `setThePassword(urlRecord, passwordString)`\n- [Has an opaque path](https://url.spec.whatwg.org/#url-opaque-path): `hasAnOpaquePath(urlRecord)`\n- [Cannot have a username/password/port](https://url.spec.whatwg.org/#cannot-have-a-username-password-port): `cannotHaveAUsernamePasswordPort(urlRecord)`\n- [Percent decode bytes](https://url.spec.whatwg.org/#percent-decode): `percentDecodeBytes(uint8Array)`\n- [Percent decode a string](https://url.spec.whatwg.org/#string-percent-decode): `percentDecodeString(string)`\n\nThe `stateOverride` parameter is one of the following strings:\n\n- [`\"scheme start\"`](https://url.spec.whatwg.org/#scheme-start-state)\n- [`\"scheme\"`](https://url.spec.whatwg.org/#scheme-state)\n- [`\"no scheme\"`](https://url.spec.whatwg.org/#no-scheme-state)\n- [`\"special relative or authority\"`](https://url.spec.whatwg.org/#special-relative-or-authority-state)\n- [`\"path or authority\"`](https://url.spec.whatwg.org/#path-or-authority-state)\n- [`\"relative\"`](https://url.spec.whatwg.org/#relative-state)\n- [`\"relative slash\"`](https://url.spec.whatwg.org/#relative-slash-state)\n- [`\"special authority slashes\"`](https://url.spec.whatwg.org/#special-authority-slashes-state)\n- [`\"special authority ignore slashes\"`](https://url.spec.whatwg.org/#special-authority-ignore-slashes-state)\n- [`\"authority\"`](https://url.spec.whatwg.org/#authority-state)\n- [`\"host\"`](https://url.spec.whatwg.org/#host-state)\n- [`\"hostname\"`](https://url.spec.whatwg.org/#hostname-state)\n- [`\"port\"`](https://url.spec.whatwg.org/#port-state)\n- [`\"file\"`](https://url.spec.whatwg.org/#file-state)\n- [`\"file slash\"`](https://url.spec.whatwg.org/#file-slash-state)\n- [`\"file host\"`](https://url.spec.whatwg.org/#file-host-state)\n- [`\"path start\"`](https://url.spec.whatwg.org/#path-start-state)\n- [`\"path\"`](https://url.spec.whatwg.org/#path-state)\n- [`\"opaque path\"`](https://url.spec.whatwg.org/#cannot-be-a-base-url-path-state)\n- [`\"query\"`](https://url.spec.whatwg.org/#query-state)\n- [`\"fragment\"`](https://url.spec.whatwg.org/#fragment-state)\n\nThe URL record type has the following API:\n\n- [`scheme`](https://url.spec.whatwg.org/#concept-url-scheme)\n- [`username`](https://url.spec.whatwg.org/#concept-url-username)\n- [`password`](https://url.spec.whatwg.org/#concept-url-password)\n- [`host`](https://url.spec.whatwg.org/#concept-url-host)\n- [`port`](https://url.spec.whatwg.org/#concept-url-port)\n- [`path`](https://url.spec.whatwg.org/#concept-url-path) (as an array of strings, or a string)\n- [`query`](https://url.spec.whatwg.org/#concept-url-query)\n- [`fragment`](https://url.spec.whatwg.org/#concept-url-fragment)\n\nThese properties should be treated with care, as in general changing them will cause the URL record to be in an inconsistent state until the appropriate invocation of `basicURLParse` is used to fix it up. You can see examples of this in the URL Standard, where there are many step sequences like \"4. Set context object’s url’s fragment to the empty string. 5. Basic URL parse _input_ with context object’s url as _url_ and fragment state as _state override_.\" In between those two steps, a URL record is in an unusable state.\n\nThe return value of \"failure\" in the spec is represented by `null`. That is, functions like `parseURL` and `basicURLParse` can return _either_ a URL record _or_ `null`.\n\n### `whatwg-url/webidl2js-wrapper` module\n\nThis module exports the `URL` and `URLSearchParams` [interface wrappers API](https://github.com/jsdom/webidl2js#for-interfaces) generated by [webidl2js](https://github.com/jsdom/webidl2js).\n\n## Development instructions\n\nFirst, install [Node.js](https://nodejs.org/). Then, fetch the dependencies of whatwg-url, by running from this directory:\n\n    npm install\n\nTo run tests:\n\n    npm test\n\nTo generate a coverage report:\n\n    npm run coverage\n\nTo build and run the live viewer:\n\n    npm run prepare\n    npm run build-live-viewer\n\nServe the contents of the `live-viewer` directory using any web server.\n\n## Supporting whatwg-url\n\nThe jsdom project (including whatwg-url) is a community-driven project maintained by a team of [volunteers](https://github.com/orgs/jsdom/people). You could support us by:\n\n- [Getting professional support for whatwg-url](https://tidelift.com/subscription/pkg/npm-whatwg-url?utm_source=npm-whatwg-url&utm_medium=referral&utm_campaign=readme) as part of a Tidelift subscription. Tidelift helps making open source sustainable for us while giving teams assurances for maintenance, licensing, and security.\n- Contributing directly to the project.\n", "readmeFilename": "README.md", "users": {"lestad": true, "teneff": true, "zewish": true, "moimikey": true, "sternelee": true, "darrentorpey": true, "stevenvachon": true, "max-developer": true}}