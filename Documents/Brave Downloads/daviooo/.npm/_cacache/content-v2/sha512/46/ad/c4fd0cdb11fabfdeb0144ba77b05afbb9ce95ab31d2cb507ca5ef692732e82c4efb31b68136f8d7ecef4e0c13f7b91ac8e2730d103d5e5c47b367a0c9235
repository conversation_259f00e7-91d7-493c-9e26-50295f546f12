{"_id": "which-collection", "_rev": "5-d49f9c364583a56ea2b8ab007ac12366", "name": "which-collection", "dist-tags": {"latest": "1.0.2"}, "versions": {"1.0.0": {"name": "which-collection", "version": "1.0.0", "description": "Which kind of Collection (Map, Set, WeakMap, WeakSet) is this JavaScript value? Works cross-realm, without `instanceof`, and despite Symbol.toStringTag.", "main": "index.js", "scripts": {"prepublish": "safe-publish-latest", "lint": "eslint .", "pretest": "npm run lint", "tests-only": "node test", "test": "npm run tests-only", "posttest": "npx aud", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/which-collection.git"}, "keywords": ["map", "set", "weakmap", "weakset", "collection.es6", "es2015"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/which-collection/issues"}, "homepage": "https://github.com/inspect-js/which-collection#readme", "devDependencies": {"@ljharb/eslint-config": "^15.0.1", "auto-changelog": "^1.16.2", "eslint": "^6.6.0", "for-each": "^0.3.3", "object-inspect": "^1.7.0", "safe-publish-latest": "^1.1.4", "tape": "^4.11.0"}, "dependencies": {"is-map": "^2.0.0", "is-set": "^2.0.0", "is-weakmap": "^2.0.0", "is-weakset": "^2.0.0"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "gitHead": "abf79aef38fcd7d699f0cb00a8f1850960822973", "_id": "which-collection@1.0.0", "_nodeVersion": "13.1.0", "_npmVersion": "6.12.1", "dist": {"integrity": "sha512-mG4RtFHE+17N2AxRNvBQ488oBjrhaOaI/G+soUaRJwdyDbu5zmqoAKPYBlY7Zd+QTwpfvInRLKo40feo2si1yA==", "shasum": "303d38022473f4b7048b529b45f6c842d8814269", "tarball": "https://registry.npmjs.org/which-collection/-/which-collection-1.0.0.tgz", "fileCount": 9, "unpackedSize": 9637, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdzGDyCRA9TVsSAnZWagAARAkP/R5pBRm34CKkle2kKybr\nuIT+57kRBpsCroNg7yALwFO2gvWIDh8JuO0ldAljj/C9F8j4hpUBfVnHuqKk\n9jjhHe3uci/2f/LzOJZIPCgaQnEUbYPYR88a/DORXDUsDFNdAKphrccWG/WG\nCRobihnexdhrqh2FNbuJfvsCwQk5+3Vf4hcwXjUzJNUB5SpioEj5zvqTi8IQ\n6Js3wm2re0fq8vWzuUAO6pi5TA6nATDzs2fpOOSc5xYcFq62nc8Lm3ajAsct\noH8kma2hKVIU5joZSl0/Pw/TNNkRas5C7sPPtb6oSG0yUVpm/4ihtkxMBf2C\nmJyaDKrz6OUYyCP8RNsjEeS4nywJcnyV/I5fOJQ2YIS2m/nSnpG6b1qAZNlR\nLuz8mtiklIi6FD7So2lNvA7e/0IKyj/Z9Qgd/JhHZOJmNkq27HDCcI8Bzmcv\n6xaBzRjjJziI+1Wsop7hqx1hy4IN8KOHZhxXXNJjpRfV2aDQiFFfMFY9dVwU\nt72kdEBJJKEi8WXOWz+nDvlBWmR2qfB/p3R0uOjYZxxF4r+h0L1gZ5UJZfor\nS2nSCC8llcNL+WLmngCPTem8Lu9D/pZIKE+S6ryt8HSsqBfsuRAKIA5l9onD\nZfFzwNukwVsoCRCBnMESNuTp52balCQ8B1koNjHMApo9AFOvI80NfnTKLD8V\nLBhd\r\n=EXdB\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDQDKEkeoE4L4xLEgdzKetln+iypcmmhgxBXJ0guRbaywIhAOBaf9a9PVfg2gJ1n0kGlMRJtnRWZfjaGF+l+Oq+tyak"}]}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/which-collection_1.0.0_1573675249568_0.7044027117797167"}, "_hasShrinkwrap": false}, "1.0.1": {"name": "which-collection", "version": "1.0.1", "description": "Which kind of Collection (Map, Set, WeakMap, WeakSet) is this JavaScript value? Works cross-realm, without `instanceof`, and despite Symbol.toStringTag.", "main": "index.js", "type": "commonjs", "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package.json": "./package.json"}, "scripts": {"prepublish": "safe-publish-latest", "lint": "eslint .", "pretest": "npm run lint", "tests-only": "node test", "test": "npm run tests-only", "posttest": "npx aud", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/which-collection.git"}, "keywords": ["map", "set", "weakmap", "weakset", "collection.es6", "es2015"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/which-collection/issues"}, "homepage": "https://github.com/inspect-js/which-collection#readme", "devDependencies": {"@ljharb/eslint-config": "^16.0.0", "auto-changelog": "^1.16.2", "eslint": "^6.8.0", "for-each": "^0.3.3", "object-inspect": "^1.7.0", "safe-publish-latest": "^1.1.4", "tape": "^5.0.0-next.4"}, "dependencies": {"is-map": "^2.0.1", "is-set": "^2.0.1", "is-weakmap": "^2.0.1", "is-weakset": "^2.0.1"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "gitHead": "088b8ceb3b645f51fdc962618717eb45562bbce5", "_id": "which-collection@1.0.1", "_nodeVersion": "13.7.0", "_npmVersion": "6.13.6", "dist": {"integrity": "sha512-W8xeTUwaln8i3K/cY1nGXzdnVZlidBcagyNFtBdD5kxnb4TvGKR7FfSIS3mYpwWS1QUCutfKz8IY8RjftB0+1A==", "shasum": "70eab71ebbbd2aefaf32f917082fc62cdcb70906", "tarball": "https://registry.npmjs.org/which-collection/-/which-collection-1.0.1.tgz", "fileCount": 10, "unpackedSize": 10942, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeLgC9CRA9TVsSAnZWagAAupMP/0/jquCHaklZXi4SKpns\nrmPFWrMcEmJJFBlcss95V5xjU+2oI1qVz8zjWe0NRTXd0Mab8d5jPWxiWr5D\nSQVK20QIc80ZxJrHR+rG805W5peQcHpJGYCmhPUvbqJzP1dSdFhUvAPWYyfm\nX+L8p7xtZIkUZwGhSxsq2MgpmYD/6lJu1ADH97WmyuIgysfFjw1So82hvUF7\nOZdrdWhCrTF+dq93d7UdZ6QdkzTQfRra+7TApcBTLnRPkZyh6EJ9D2jZsHUU\nu8TdLHyG1vZmrqMJKPqmdjX4mVyZV36ldyEhTyqx2jBH18ZUxuf48ShKuEiG\nXuK3JAU76IyMBGHcUQTtI9V2Udab6tYGAY6Dm2DneSe2E5aZCHpLslx2QEsp\nLg0crH6GsN5rsV+dXrxwRn7mHx3OwRHeSp4cmZM9oZHT1LW86ZMJbbiKOKh5\no0UUI/17rKtktaTZEtGppvJ22aHKrXkZaJYxglWGBfzB29rxUrjX4i4xWi0S\nZ/XQpLOpkLVVdfXqAWvPsyd53vaYMg+NM6Vg0o/aZl6keg7u3hJ721MFIEON\np65DBnSxhzV/CXHM5nKSzNAVzKh3s5zSKwdrSfA0suZfDKwleoFi9dS0yEcK\nqaZRrIfIdhxyPKl/VFCQFZjFnximcKkXwfSitYZDKCwx5AT2lUVDvItCTrc2\nymXE\r\n=6CLz\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAE/pu2DKPHUQSYJEf7JkPLKk5Six/VG5VEQ64O4S0nzAiEAkgC8FAwSDIMwBk4SOvfwigRBgEcAcJ09bO4X7+71FGo="}]}, "maintainers": [{"email": "<EMAIL>", "name": "lj<PERSON>b"}], "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/which-collection_1.0.1_1580073148958_0.2885548563363387"}, "_hasShrinkwrap": false}, "1.0.2": {"name": "which-collection", "version": "1.0.2", "description": "Which kind of Collection (Map, Set, WeakMap, WeakSet) is this JavaScript value? Works cross-realm, without `instanceof`, and despite Symbol.toStringTag.", "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "sideEffects": false, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "lint": "eslint --ext=js,mjs .", "pretest": "npm run lint", "postlint": "tsc -p . && attw -P", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "npx aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/which-collection.git"}, "keywords": ["map", "set", "weakmap", "weakset", "collection.es6", "es2015"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/which-collection/issues"}, "homepage": "https://github.com/inspect-js/which-collection#readme", "devDependencies": {"@arethetypeswrong/cli": "^0.15.0", "@ljharb/eslint-config": "^21.1.0", "@types/for-each": "^0.3.3", "@types/object-inspect": "^1.8.4", "@types/tape": "^5.6.4", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "for-each": "^0.3.3", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.1", "safe-publish-latest": "^2.0.0", "tape": "^5.7.5", "typescript": "next"}, "dependencies": {"is-map": "^2.0.3", "is-set": "^2.0.3", "is-weakmap": "^2.0.2", "is-weakset": "^2.0.3"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "engines": {"node": ">= 0.4"}, "_id": "which-collection@1.0.2", "gitHead": "c0320b4bd454be60d24a3e7bdd76b28df7484399", "types": "./index.d.ts", "_nodeVersion": "21.7.0", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-K4jVyjnBdgvc86Y6BkaLZEN933SwYOuBFkdmBu9ZfkcAbdVbpITnDmjvZ/aQjRXQrv5EPkTnD1s39GiiqbngCw==", "shasum": "627ef76243920a107e7ce8e96191debe4b16c2a0", "tarball": "https://registry.npmjs.org/which-collection/-/which-collection-1.0.2.tgz", "fileCount": 11, "unpackedSize": 20064, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCSCstvKnyPanO9sHo/xY1DFUghIvMC9bNHHJ4uOy10PwIhAORFOWJL92b4DaQmuaSfocYcIcHfQNrYQM5d9v2qndYP"}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/which-collection_1.0.2_1709938384355_0.05037225417160052"}, "_hasShrinkwrap": false}}, "time": {"created": "2019-11-13T20:00:49.567Z", "1.0.0": "2019-11-13T20:00:49.653Z", "modified": "2024-03-08T22:53:04.970Z", "1.0.1": "2020-01-26T21:12:29.195Z", "1.0.2": "2024-03-08T22:53:04.536Z"}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "description": "Which kind of Collection (Map, Set, WeakMap, WeakSet) is this JavaScript value? Works cross-realm, without `instanceof`, and despite Symbol.toStringTag.", "homepage": "https://github.com/inspect-js/which-collection#readme", "keywords": ["map", "set", "weakmap", "weakset", "collection.es6", "es2015"], "repository": {"type": "git", "url": "git+https://github.com/inspect-js/which-collection.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/inspect-js/which-collection/issues"}, "license": "MIT", "readme": "# which-collection <sup>[![Version Badge][2]][1]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![dependency status][5]][6]\n[![dev dependency status][7]][8]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][11]][1]\n\nWhich kind of Collection (Map, Set, WeakMap, WeakSet) is this JavaScript value? Works cross-realm, without `instanceof`, and despite Symbol.toStringTag.\n\n## Example\n\n```js\nvar whichCollection = require('which-collection');\nvar assert = require('assert');\n\nassert.equal(false, whichCollection(undefined));\nassert.equal(false, whichCollection(null));\nassert.equal(false, whichCollection(false));\nassert.equal(false, whichCollection(true));\nassert.equal(false, whichCollection([]));\nassert.equal(false, whichCollection({}));\nassert.equal(false, whichCollection(/a/g));\nassert.equal(false, whichCollection(new RegExp('a', 'g')));\nassert.equal(false, whichCollection(new Date()));\nassert.equal(false, whichCollection(42));\nassert.equal(false, whichCollection(NaN));\nassert.equal(false, whichCollection(Infinity));\nassert.equal(false, whichCollection(new Number(42)));\nassert.equal(false, whichCollection(42n));\nassert.equal(false, whichCollection(Object(42n)));\nassert.equal(false, whichCollection('foo'));\nassert.equal(false, whichCollection(Object('foo')));\nassert.equal(false, whichCollection(function () {}));\nassert.equal(false, whichCollection(function* () {}));\nassert.equal(false, whichCollection(x => x * x));\nassert.equal(false, whichCollection([]));\n\nassert.equal('Map', whichCollection(new Map()));\nassert.equal('Set', whichCollection(new Set()));\nassert.equal('WeakMap', whichCollection(new WeakMap()));\nassert.equal('WeakSet', whichCollection(new WeakSet()));\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n[1]: https://npmjs.org/package/which-collection\n[2]: https://versionbadg.es/inspect-js/which-collection.svg\n[5]: https://david-dm.org/inspect-js/which-collection.svg\n[6]: https://david-dm.org/inspect-js/which-collection\n[7]: https://david-dm.org/inspect-js/which-collection/dev-status.svg\n[8]: https://david-dm.org/inspect-js/which-collection#info=devDependencies\n[11]: https://nodei.co/npm/which-collection.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/which-collection.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/which-collection.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=which-collection\n[codecov-image]: https://codecov.io/gh/inspect-js/which-collection/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/inspect-js/which-collection/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/inspect-js/which-collection\n[actions-url]: https://github.com/inspect-js/which-collection/actions\n", "readmeFilename": "README.md"}