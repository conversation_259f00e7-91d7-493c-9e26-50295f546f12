{"_id": "is-utf8", "_rev": "11-2780e44247c718753704bd92ecf6abb1", "name": "is-utf8", "description": "Detect if a buffer is utf8 encoded.", "dist-tags": {"latest": "0.2.1"}, "versions": {"0.1.0": {"name": "is-utf8", "version": "0.1.0", "description": "detect if a buffer is utf8 encoded.", "main": "is-utf8.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/wayfind/is-utf8.git"}, "keywords": ["utf8", "charset"], "author": {"name": "wayfind"}, "license": "BSD", "_id": "is-utf8@0.1.0", "dist": {"shasum": "813d72a6f0219952b3814f38cd277876802d9ebb", "tarball": "https://registry.npmjs.org/is-utf8/-/is-utf8-0.1.0.tgz", "integrity": "sha512-xdSchxXkPNEgPNNrzJ+wS/xpeS2odOX9t08BDvx8gp5WIOo55W2vO/rP/c6x8SJpibd7QYqQO+CXql2JuZUVlA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCgkPTaMSC2qx3ScfYoMmJ6XLsWzPGsB3cWBO7uUVRZBAIhAMU+TlDgPX41LmIdL64XSljblCh8dJq7xh99ELX8imSE"}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "wayfind", "email": "<EMAIL>"}, "maintainers": [{"name": "wayfind", "email": "<EMAIL>"}], "directories": {}}, "0.2.0": {"name": "is-utf8", "version": "0.2.0", "description": "detect if a buffer is utf8 encoded.", "main": "is-utf8.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "https://github.com/wayfind/is-utf8.git"}, "keywords": ["utf8", "charset"], "author": {"name": "wayfind"}, "license": "BSD", "_id": "is-utf8@0.2.0", "dist": {"shasum": "b8aa54125ae626bfe4e3beb965f16a89c58a1137", "tarball": "https://registry.npmjs.org/is-utf8/-/is-utf8-0.2.0.tgz", "integrity": "sha512-L7zvMCxfKofnKFkjLjTQYJGLP6Zmpv8jc/hErI5go6WS+rRiYKd7lUXdHW75arCw0Sjy+h9sGS0spd4y9q2l+w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCjqRdQ6qScgJ7bED5nPXEr/5tS2ZFwkzpKaBkPe2hpSwIgBlmh/RHNLjJylUD6BwWmcGJlfgiXRLa31NE+Pe7H+3g="}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "wayfind", "email": "<EMAIL>"}, "maintainers": [{"name": "wayfind", "email": "<EMAIL>"}], "directories": {}}, "0.2.1": {"name": "is-utf8", "version": "0.2.1", "description": "Detect if a buffer is utf8 encoded.", "main": "is-utf8.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git+https://github.com/wayfind/is-utf8.git"}, "keywords": ["utf8", "charset"], "files": ["is-utf8.js"], "author": {"name": "wayfind"}, "license": "MIT", "gitHead": "709df7202f9c3f93cdc2463b352dd80d8de9ce0b", "bugs": {"url": "https://github.com/wayfind/is-utf8/issues"}, "homepage": "https://github.com/wayfind/is-utf8#readme", "_id": "is-utf8@0.2.1", "_shasum": "4b0da1442104d1b336340e80797e865cf39f7d72", "_from": ".", "_npmVersion": "2.12.1", "_nodeVersion": "2.3.4", "_npmUser": {"name": "wayfind", "email": "<EMAIL>"}, "maintainers": [{"name": "wayfind", "email": "<EMAIL>"}], "dist": {"shasum": "4b0da1442104d1b336340e80797e865cf39f7d72", "tarball": "https://registry.npmjs.org/is-utf8/-/is-utf8-0.2.1.tgz", "integrity": "sha512-rMYPYvCzsXywIsldgLaSoPlw5PfoB/ssr7hY4pLfcodrA5M/eArza1a9VmTiNIBNMjOGr1Ow9mTyU2o69U6U9Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIByfXl0EI85+i9ZR0qe8XNhf7NWN8pjmklSoU84nhzAtAiEAuOOxMllogMjSg04VhAZm4USfx0yQUMOAFp6hZcInWv0="}]}, "directories": {}}}, "readme": "#utf8 detector\n\nDetect if a Buffer is utf8 encoded. \nIt need The minimum amount of bytes is 4.\n\n\n```javascript\n    var fs = require('fs');\n    var isUtf8 = require('is-utf8');\n    var ansi = fs.readFileSync('ansi.txt');\n    var utf8 = fs.readFileSync('utf8.txt');\n    \n    console.log('ansi.txt is utf8: '+isUtf8(ansi)); //false\n    console.log('utf8.txt is utf8: '+isUtf8(utf8)); //true\n```\n    \n", "maintainers": [{"name": "wayfind", "email": "<EMAIL>"}], "time": {"modified": "2023-07-12T19:12:53.413Z", "created": "2012-09-12T09:23:46.194Z", "0.1.0": "2012-09-12T09:23:51.635Z", "0.2.0": "2012-09-12T09:47:48.160Z", "0.2.1": "2015-12-19T04:04:22.462Z"}, "author": {"name": "wayfind"}, "repository": {"type": "git", "url": "git+https://github.com/wayfind/is-utf8.git"}, "users": {"alanshaw": true, "robermac": true, "flumpus-dev": true}, "homepage": "https://github.com/wayfind/is-utf8#readme", "keywords": ["utf8", "charset"], "bugs": {"url": "https://github.com/wayfind/is-utf8/issues"}, "license": "MIT", "readmeFilename": "README.md"}