{"_id": "@iamtraction/google-translate", "_rev": "3-a35a6e1045fdc2594ba35b80f067634e", "name": "@iamtraction/google-translate", "dist-tags": {"latest": "2.0.1"}, "versions": {"1.1.2": {"name": "@iamtraction/google-translate", "version": "1.1.2", "description": "A Node.JS library to consume Google Translate API for free.", "main": "src/index.js", "typings": "typings/index.d.ts", "scripts": {"test:lint": "eslint .", "test": "npm run test:lint", "start": "node ."}, "repository": {"type": "git", "url": "git+https://github.com/iamtraction/google-translate.git"}, "keywords": ["google translate api", "google translate", "google api", "translate api", "google", "translate", "api"], "author": {"name": "<PERSON><PERSON><PERSON>", "url": "iamtraction"}, "license": "MIT", "engines": {"node": ">=8.0.0"}, "dependencies": {"got": "^11.8.1"}, "devDependencies": {"eslint": "^7.18.0"}, "gitHead": "46e06baf64d8425f148f1da3cf427813eeb179ac", "bugs": {"url": "https://github.com/iamtraction/google-translate/issues"}, "homepage": "https://github.com/iamtraction/google-translate#readme", "_id": "@iamtraction/google-translate@1.1.2", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-z30o1rz2b7JTTqmy/MSQjwfz59++qrBpTXGHWQrqxPW1mTW5pLEMM2FymxCyJcg6alogKgSLT2RVrjuvuNCxVA==", "shasum": "59ed4f57fd0068f0c2486a04d1a55d241a30194f", "tarball": "https://registry.npmjs.org/@iamtraction/google-translate/-/google-translate-1.1.2.tgz", "fileCount": 12, "unpackedSize": 21826, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgBS8+CRA9TVsSAnZWagAA3x0P/AyoszkVESrnymNUXZwY\n1HpSfaPObLyOqifP0C2O2r5NSj5fduhz6Vq2cJDcFPEFb2eEcNnKe6x5Nlzf\nq5aWJjwqN0L1yaHZ+4EYNNWPs15TXBPSi6XP9j2AWlErWjqCeVOpn9D7EHXN\nie2nrM1sC5ukRvw4RAoIjZLbL2uco5VNm1Jz13HRz+PQLZZklYWDERdGdYFi\n3BfgXTfFufOEb9xhfgD79N7qeMC+ZDdxZXHJNCI2HZWvRaimddS0BcUGcM0G\noqf8tfGP0FwuX4ViitRKUrocygnzLqPwD6b9UgxoP8YuA58d4Xt7v3cGwpue\nSlRWb+HztU3WwlftbYNMbWxF3R5nxyRdAlQ0kvxceFfqSaetMB5x6smzXW8E\nYnCru7JMIMTZ1w63LkAdommSbwo1RNIpZ3GdBiyc96ymsr35OHHNGHk4VBuC\nZhqaeOQMyLVgnSWDKMW0VoKRRjhiC4VX3nii2ryOZlNnuYOnTYMRikEPt31b\na8xnFsM/bj1J3iYuQly2F7anvpmPdUgpVtluFzwt9t9cLPoAXuOFzOlqaeD8\nLuhEVdmEUWGaBROZQ/lryiydZRVb7QESYdJRvJMTAETdViB9tGEDWdeEcu1n\nN6RzGOKpFRjyROqFXJ3gYCviEGYvIFmbsjmcUmW7WXeFH6sy0NWrsVPSauP9\nhMQu\r\n=dz8h\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBS7HUJmV5KVHw7J5W4mIh/VIT60YVmeO62YiKBy0GaNAiB1AQ8cAtA/HDeSos0YSLbdY4fPd3BjFju0dH+OH9WFEg=="}]}, "_npmUser": {"name": "iamtraction", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "iamtraction", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/google-translate_1.1.2_1610952509898_0.2561249052699941"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "@iamtraction/google-translate", "version": "2.0.0", "description": "A Node.JS library to consume Google Translate API for free.", "main": "src/index.js", "typings": "typings/index.d.ts", "scripts": {"test:lint": "eslint .", "test": "npm run test:lint", "start": "node ."}, "repository": {"type": "git", "url": "git+https://github.com/iamtraction/google-translate.git"}, "keywords": ["google translate api", "google translate", "google api", "translate api", "google", "translate", "api"], "author": {"name": "<PERSON><PERSON><PERSON>", "url": "iamtraction"}, "license": "MIT", "engines": {"node": ">=16.0.0"}, "dependencies": {"undici": "^5.11.0"}, "devDependencies": {"eslint": "^8.25.0"}, "gitHead": "3de9ce69d4aacd9c53624219d4463ad26a64d437", "bugs": {"url": "https://github.com/iamtraction/google-translate/issues"}, "homepage": "https://github.com/iamtraction/google-translate#readme", "_id": "@iamtraction/google-translate@2.0.0", "_nodeVersion": "16.17.1", "_npmVersion": "8.15.0", "dist": {"integrity": "sha512-NdGO7FOVZR5X8mhhBmpDWurAGbPSc5RTTszPC7cBElu7pDafz4dzQA6Me3TaPcOLb2YW2YNk+vuZRV04XGG8Iw==", "shasum": "3b806b37f41822821669fe67a44fca44947a0b8d", "tarball": "https://registry.npmjs.org/@iamtraction/google-translate/-/google-translate-2.0.0.tgz", "fileCount": 12, "unpackedSize": 20803, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEngCSOQwATArkjU09iGslycUHvvtF1nTy5fZfxvXBmaAiEAijlndT2aTMhbbLJECoMkttEbRdj9V5aDoOSzwjP0qCg="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSc6gACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqlPBAAgUe4+TckzT3rErxyPDSK+KZCik7cUV1Ze+IphNWSNbSg2FiQ\r\nYO1HQC0T1ROR5hApeZ113yAyteMMBEORj+2YgqYWxakY1InGPZ0i0vA5sXzT\r\nVdOkwxbrd56CKQVDgE9JeDB11XkpD2jlTK/P3OZMC4lB6EXdyNL9q/rZlueY\r\nuVZsLgj/qgDbcmUnA4CGm+bkHP4QRi6/Y9iUvN6v5uoyAuMajgnxf3EhtxV7\r\nryighJgJ6kIM1wE32GOYKZV/ICfHPDNH2dM8Hr5UaL7Ep0CiVdVVCGLHF/wz\r\n3RpaHaEXeIchnHNy+Bgw73FkAv0XbzVFXj5sJOPRmUiXFQtAJueWcx52L2q7\r\n8lyPDz8iq9bZJtHwPZ/jahcdXNq/vybRxbocvRGgPmZOq93pxipdU6hEnAI4\r\nfHUTxYfZgswLarBaCaApko0K1BoBlgtyO2TO1lVs5rz9Ky09qdfLhwkdC/Ms\r\nhoCk7P4id1zvVO7ZSACNJN/c2uZtna5YrN43FzpDPHiewh6uC3qW/myT7W/z\r\nzVCW/+5OAiS3dKNksUyKWZ/83Bzgi46yzsGrWSpwPkewXY+xw8L6R29y5nXG\r\nnwd6QmQE3MSgHYMVlEogju18wbSG4w1+QNGrIRqHMrKOvFaVwxgGRJddFfnB\r\nnQwacA8NlVEWkW4l8sAQPwlFOq+uZslN4wc=\r\n=t5pv\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "iamtraction", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "iamtraction", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/google-translate_2.0.0_1665781408639_0.3188671054100958"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "@iamtraction/google-translate", "version": "2.0.1", "description": "A Node.JS library to consume Google Translate API for free.", "main": "src/index.js", "typings": "typings/index.d.ts", "scripts": {"test:lint": "eslint .", "test": "npm run test:lint", "start": "node ."}, "repository": {"type": "git", "url": "git+https://github.com/iamtraction/google-translate.git"}, "keywords": ["google translate api", "google translate", "google api", "translate api", "google", "translate", "api"], "author": {"name": "<PERSON><PERSON><PERSON>", "url": "iamtraction"}, "license": "MIT", "engines": {"node": ">=16.0.0"}, "dependencies": {"undici": "^5.12.0"}, "devDependencies": {"eslint": "^8.27.0"}, "gitHead": "3b0bbd13d105786cf28deac945f36b0cf7dd1369", "bugs": {"url": "https://github.com/iamtraction/google-translate/issues"}, "homepage": "https://github.com/iamtraction/google-translate#readme", "_id": "@iamtraction/google-translate@2.0.1", "_nodeVersion": "16.18.0", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-kOTkt23zflxpgxLIe9tLvDU8IvNscXYbgMSXydNkZGWDtJ75YJiU3v7EBz+t7h/gSiY7sT5r4dlFkMrHCl+N6A==", "shasum": "f6c0aa7f15030fc2014c13db8eb282f5ab85ed10", "tarball": "https://registry.npmjs.org/@iamtraction/google-translate/-/google-translate-2.0.1.tgz", "fileCount": 12, "unpackedSize": 20933, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDHsgB4uN88t7fvaUw8+7tlyNDGpKHXzbTC35LSstCSUQIhAMv6pOOc2Hz3oreGKN2DcLEYaiGMpKwe7rRaxQ2ZOywF"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjcowPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrLmw/9Hb+Q6dJzYElWCkv1aL+5AyOZbzg7tqRi0oYnB/4rptyLl9l/\r\nDA9a3ACOLhNWfGSap8Q25d0YxONdId9+tm5GGWvyU4U/oPUgddSSfUQX3HtI\r\nlkymhMp+CTHsWHh7aBIr0gQvaBnLkbwU4CqFwnudOXQLodVjIbewMZE3wyb7\r\nSISABtWcSez7Kww0XtEJK0/rllitZ1Cwf4AXozHb5sKEJWB1rDZfNTGeloZi\r\n5JONzXpqVJdj3nxAsJWAOy0lJEApbVjx9bl83sodeFdwmUbWEf6K21kwWjzV\r\nAQNfwmUQGfepMlubSMMGjqSuy3eYo3YqG4jaCPjGc0SlOdHMV9nkRnO2tAk1\r\nVbvd97tv25+YBxgaUuOIOqiwhvMG5LSwrvy5GuOMjfJHwxa/mo2DybygEkmA\r\nc6s80nepqWPotgCFOK71HsDTk+WMsXjPLRQ8hEsGGb6AS2+GVcqpFfJcT/xs\r\nZDUxeQlKN9FbA7SZeL/UjiS6kP8LLB0XksMmp0FGBS/PoaPTV4OOqE8eNAAh\r\nfjs9dOY1XsNoUQFrvnZnfomudUYz8jGJKz2ciKhuZoDSD7/zBUyTGxFYSaVH\r\n1boLCc0mfO3raLBv2K2C9eEWDiY6dnfd/zJmjH5DbLCoiwc1jc0xsmdZuXsA\r\nbwgwDGC/MNAWH2nn1fm2rvytrrheoRF4e2o=\r\n=5YPn\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "iamtraction", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "iamtraction", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/google-translate_2.0.1_1668451343510_0.22538730000529705"}, "_hasShrinkwrap": false}}, "time": {"created": "2021-01-18T06:48:29.843Z", "1.1.2": "2021-01-18T06:48:30.043Z", "modified": "2022-11-14T18:42:23.813Z", "2.0.0": "2022-10-14T21:03:28.803Z", "2.0.1": "2022-11-14T18:42:23.742Z"}, "maintainers": [{"name": "iamtraction", "email": "<EMAIL>"}], "description": "A Node.JS library to consume Google Translate API for free.", "homepage": "https://github.com/iamtraction/google-translate#readme", "keywords": ["google translate api", "google translate", "google api", "translate api", "google", "translate", "api"], "repository": {"type": "git", "url": "git+https://github.com/iamtraction/google-translate.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "url": "iamtraction"}, "bugs": {"url": "https://github.com/iamtraction/google-translate/issues"}, "license": "MIT", "readme": "# Google Translate API\nA [Node.JS](https://nodejs.org) library to consume Google Translate for free.\n\n[![GitHub release](https://img.shields.io/github/release/iamtraction/google-translate.svg?style=flat)](https://github.com/iamtraction/google-translate/releases)\n[![Dependencies](https://david-dm.org/iamtraction/google-translate.svg)](https://david-dm.org/iamtraction/google-translate)\n[![Known Vulnerabilities](https://snyk.io/test/github/iamtraction/google-translate/badge.svg?targetFile=package.json)](https://snyk.io/test/github/iamtraction/google-translate?targetFile=package.json)\n[![license](https://img.shields.io/github/license/iamtraction/google-translate.svg)](LICENSE)\n\n### Feature Highlights\n* Automatically detect source language\n* Automatic spelling corrections\n* Automatic language correction\n* Fast and reliable\n\n## Table of Contents\n* [Installation](#installation)\n* [Usage](#usage)\n* [Examples](#examples)\n* [Credits, etc](#extras)\n\n## Installation\n```bash\n# Stable version, from npm repository\nnpm install --save @iamtraction/google-translate\n\n# Latest version, from GitHub repository\nnpm install --save iamtraction/google-translate\n```\n\n## Usage\n```js\n// If you've installed from npm, do:\nconst translate = require('@iamtraction/google-translate');\n\n// If you've installed from GitHub, do:\nconst translate = require('google-translate');\n```\n\n#### Method: `translate(text, options)`\n```js\ntranslate(text, options).then(console.log).catch(console.error);\n```\n| Parameter | Type | Optional | Default | Description |\n|-|-|-|-|-|\n| `text` | `String` | No | - | The text you want to translate. |\n| `options` | `Object` | - | - | The options for translating. |\n| `options.from` | `String` | Yes | `'auto'` | The language name/ISO 639-1 code to translate from. If none is given, it will auto detect the source language. |\n| `options.to` | `String` | Yes | `'en'` | The language name/ISO 639-1 code to translate to. If none is given, it will translate to English. |\n| `options.raw` | `Boolean` | Yes | `false` | If `true`, it will return the raw output that was received from Google Translate. |\n\n#### Returns: `Promise<Object>`\n**Response Object:**\n\n| Key | Type | Description |\n|-|-|-|\n| `text` | `String` | The translated text. |\n| `from` | `Object` | - |\n| `from.language` | `Object` | - |\n| `from.language.didYouMean` | `Boolean` | Whether or not the API suggest a correction in the source language. |\n| `from.language.iso` | `String` | The ISO 639-1 code of the language that the API has recognized in the text. |\n| `from.text` | `Object` | - |\n| `from.text.autoCorrected` | `Boolean` | Whether or not the API has auto corrected the original text. |\n| `from.text.value` | `String` | The auto corrected text or the text with suggested corrections. Only returned if `from.text.autoCorrected` or `from.text.didYouMean` is `true`. |\n| `from.text.didYouMean` | `Boolean` | Wherether or not the API has suggested corrections to the text |\n| `raw` | `String` | The raw response from Google Translate servers. Only returned if `options.raw` is `true` in the request options. |\n\n\n## Examples\n#### From automatic language detection to English:\n```js\ntranslate('Tu es incroyable!', { to: 'en' }).then(res => {\n  console.log(res.text); // OUTPUT: You are amazing!\n}).catch(err => {\n  console.error(err);\n});\n```\n\n#### From English to French, with a typo:\n```js\ntranslate('Thank you', { from: 'en', to: 'fr' }).then(res => {\n  console.log(res.text); // OUTPUT: Je vous remercie\n  console.log(res.from.autoCorrected); // OUTPUT: true\n  console.log(res.from.text.value); // OUTPUT: [Thank] you\n  console.log(res.from.text.didYouMean); // OUTPUT: false\n}).catch(err => {\n  console.error(err);\n});\n```\n\n#### Sometimes Google Translate won't auto correct:\n```js\ntranslate('Thank you', { from: 'en', to: 'fr' }).then(res => {\n  console.log(res.text); // OUTPUT: ''\n  console.log(res.from.autoCorrected); // OUTPUT: false\n  console.log(res.from.text.value); // OUTPUT: [Thank] you\n  console.log(res.from.text.didYouMean); // OUTPUT: true\n}).catch(err => {\n  console.error(err);\n});\n```\n\n## Extras\nIf you liked this project, please give it a ⭐ in [**GitHub**](https://github.com/iamtraction/google-translate).\n\n> Credits to [matheuss](https://github.com/matheuss) for writing the original version of this library. I rewrote this, with improvements and without using many external libraries, as his library was not actively developed and had vulnerabilities.\n", "readmeFilename": "README.md"}