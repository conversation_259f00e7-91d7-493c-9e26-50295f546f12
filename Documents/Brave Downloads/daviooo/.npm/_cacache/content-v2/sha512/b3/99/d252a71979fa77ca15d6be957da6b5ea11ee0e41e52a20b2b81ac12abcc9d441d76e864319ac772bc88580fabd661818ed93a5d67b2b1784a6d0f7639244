{"_id": "has-unicode", "_rev": "11-3824f301328964a34ddbf63717584196", "name": "has-unicode", "description": "Try to guess if your terminal supports unicode", "dist-tags": {"latest": "2.0.1"}, "versions": {"1.0.0": {"name": "has-unicode", "version": "1.0.0", "description": "Try to guess if your terminal supports unicode", "main": "index.js", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "https://github.com/iarna/has-unicode"}, "keywords": ["unicode", "terminal"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/iarna/has-unicode/issues"}, "homepage": "https://github.com/iarna/has-unicode", "devDependencies": {"require-inject": "^1.1.1", "tap": "^0.4.13"}, "gitHead": "a8c3dcf3be5f0c8f8e26a3e7ffea7da24344a006", "_id": "has-unicode@1.0.0", "_shasum": "bac5c44e064c2ffc3b8fcbd8c71afe08f9afc8cc", "_from": ".", "_npmVersion": "2.1.11", "_nodeVersion": "0.10.33", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "dist": {"shasum": "bac5c44e064c2ffc3b8fcbd8c71afe08f9afc8cc", "tarball": "https://registry.npmjs.org/has-unicode/-/has-unicode-1.0.0.tgz", "integrity": "sha512-JwgOrHApSgA0NsNfYfUt4DUJgCNL54MpbrsdzDH9PMJPcxCfizecciWkOGQtuggG7oBT6ZGXiH5vsKOrCxvD/w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAVXll3ZMQvYoD3fGuYwJD1r5VbwLHj7tmlUTZ2SrJsgAiAWGAZ1MRZ19Z+dg8z1sHoxA4dSjWa/kMgRsSRywIJ1kg=="}]}, "directories": {}}, "1.0.1": {"name": "has-unicode", "version": "1.0.1", "description": "Try to guess if your terminal supports unicode", "main": "index.js", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/iarna/has-unicode.git"}, "keywords": ["unicode", "terminal"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/iarna/has-unicode/issues"}, "homepage": "https://github.com/iarna/has-unicode", "devDependencies": {"require-inject": "^1.1.1", "tap": "^0.4.13"}, "gitHead": "d4ad300c67b25c197582e42e936ea928f7935d01", "_id": "has-unicode@1.0.1", "_shasum": "c46fceea053eb8ec789bffbba25fca52dfdcf38e", "_from": ".", "_npmVersion": "3.3.6", "_nodeVersion": "4.1.1", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"shasum": "c46fceea053eb8ec789bffbba25fca52dfdcf38e", "tarball": "https://registry.npmjs.org/has-unicode/-/has-unicode-1.0.1.tgz", "integrity": "sha512-TnWnzjql9ZsMsXHXcIHxoUZNhd1pvHXI/8iO70KKArH5ykACBQSjdO3QzXpR7IYNbuRxeAwl3X2w1gJ4hsIxPg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD5zkRTFzPWV3nmKfJO9WDVE9iyxCWpnnYWRC+lD+OF2QIgYwRq3ZW06cycIBTfSQGx6vA9qQJAUlCS3cdwDRHHpXU="}]}, "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "directories": {}}, "2.0.0": {"name": "has-unicode", "version": "2.0.0", "description": "Try to guess if your terminal supports unicode", "main": "index.js", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/iarna/has-unicode.git"}, "keywords": ["unicode", "terminal"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/iarna/has-unicode/issues"}, "homepage": "https://github.com/iarna/has-unicode", "devDependencies": {"require-inject": "^1.3.0", "tap": "^2.3.1"}, "gitHead": "fdd5de141a5564bdb5bc991d951209da40f6a598", "_id": "has-unicode@2.0.0", "_shasum": "a3cd96c307ba41d559c5a2ee408c12a11c4c2ec3", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.2", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"shasum": "a3cd96c307ba41d559c5a2ee408c12a11c4c2ec3", "tarball": "https://registry.npmjs.org/has-unicode/-/has-unicode-2.0.0.tgz", "integrity": "sha512-O9wqHeXNkfa6ktLXg+WYg6oRqdpl5AH8FQdZanjILpQEQ8lqiN0fD6AoGqtMGn2xhRJ4UBKMP2Z17PZxQKoo5Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA6MV3gVBzu5ovcaXw2Ln8q0DvT0+Q0AF8VIwZk1fq4QAiEAsaSOR4C4Z4ZefKNTpmqBIFX5S4H5Lao+lVQDDl96kKE="}]}, "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "directories": {}}, "2.0.1": {"name": "has-unicode", "version": "2.0.1", "description": "Try to guess if your terminal supports unicode", "main": "index.js", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/iarna/has-unicode.git"}, "keywords": ["unicode", "terminal"], "files": ["index.js"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/iarna/has-unicode/issues"}, "homepage": "https://github.com/iarna/has-unicode", "devDependencies": {"require-inject": "^1.3.0", "tap": "^2.3.1"}, "gitHead": "0a05df154e8d89a7fb9798da60b68c78c2df6646", "_id": "has-unicode@2.0.1", "_shasum": "e0e6fe6a28cf51138855e086d1691e771de2a8b9", "_from": ".", "_npmVersion": "3.10.2", "_nodeVersion": "4.4.0", "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "dist": {"shasum": "e0e6fe6a28cf51138855e086d1691e771de2a8b9", "tarball": "https://registry.npmjs.org/has-unicode/-/has-unicode-2.0.1.tgz", "integrity": "sha512-8Rf9Y83NBReMnx0gFzA8JImQACstCYWUplepDa9xprwwtmgEZUF0h/i5xSA625zB/I37EtrswSST6OXxwaaIJQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCHHvNVIlXUWZTLZNP7zxy8CkLTFwfeQuHWIsNanb+/XwIgde9jCC8RAydT16dlJfDoa8KNyRNEaf72Q3zCWP+eYVY="}]}, "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/has-unicode-2.0.1.tgz_1466719828333_0.48896647873334587"}, "directories": {}}}, "readme": "has-unicode\n===========\n\nTry to guess if your terminal supports unicode\n\n```javascript\nvar hasUnicode = require(\"has-unicode\")\n\nif (hasUnicode()) {\n  // the terminal probably has unicode support\n}\n```\n```javascript\nvar hasUnicode = require(\"has-unicode\").tryHarder\nhasUnicode(function(unicodeSupported) {\n  if (unicodeSupported) {\n    // the terminal probably has unicode support\n  }\n})\n```\n\n## Detecting Unicode\n\nWhat we actually detect is UTF-8 support, as that's what Node itself supports.\nIf you have a UTF-16 locale then you won't be detected as unicode capable.\n\n### Windows\n\nSince at least Windows 7, `cmd` and `powershell` have been unicode capable,\nbut unfortunately even then it's not guaranteed. In many localizations it\nstill uses legacy code pages and there's no facility short of running\nprograms or linking C++ that will let us detect this. As such, we\nreport any Windows installation as NOT unicode capable, and recommend\nthat you encourage your users to override this via config.\n\n### Unix Like Operating Systems\n\nWe look at the environment variables `LC_ALL`, `LC_CTYPE`, and `LANG` in\nthat order.  For `LC_ALL` and `LANG`, it looks for `.UTF-8` in the value. \nFor `LC_CTYPE` it looks to see if the value is `UTF-8`.  This is sufficient\nfor most POSIX systems.  While locale data can be put in `/etc/locale.conf`\nas well, AFAIK it's always copied into the environment.\n\n", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "time": {"modified": "2023-04-12T02:45:53.797Z", "created": "2014-12-30T17:33:11.432Z", "1.0.0": "2014-12-30T17:33:11.432Z", "1.0.1": "2015-10-08T00:33:47.037Z", "2.0.0": "2015-11-26T00:59:19.737Z", "2.0.1": "2016-06-23T22:10:28.872Z"}, "homepage": "https://github.com/iarna/has-unicode", "keywords": ["unicode", "terminal"], "repository": {"type": "git", "url": "git+https://github.com/iarna/has-unicode.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/iarna/has-unicode/issues"}, "license": "ISC", "readmeFilename": "README.md", "users": {"iarna": true, "flumpus-dev": true}}