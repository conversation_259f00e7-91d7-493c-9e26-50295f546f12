{"_id": "regexp.prototype.flags", "_rev": "24-6e5b07cd7f156f259de09dcd80e18395", "name": "regexp.prototype.flags", "dist-tags": {"latest": "1.5.4"}, "versions": {"1.0.0": {"name": "regexp.prototype.flags", "version": "1.0.0", "keywords": ["RegExp.prototype.flags", "regex", "ES6", "shim", "flags", "regexp", "RegExp#flags", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "regexp.prototype.flags@1.0.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/es-shims/RegExp.prototype.flags", "bugs": {"url": "https://github.com/es-shims/RegExp.prototype.flags/issues"}, "dist": {"shasum": "088904e2a540aa77b55e5344561d5e24d7145594", "tarball": "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.0.0.tgz", "integrity": "sha512-gP/7VRqc9RRsJCt7m51lmKg4wboWkg5UIn6jKkdLDnIEtHH2xaRZURLD4zhDtglt4YYQOPSjb/WjaMhNYdZ05g==", "signatures": [{"sig": "MEYCIQDcYjQxofr0aORkADlKKVi5aPmIRlk6TEFHmbhCj7WL1QIhALd8DxP495VMdr+vi/m2lv3ng1S6ThuO6MqWDyCfE5gG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "088904e2a540aa77b55e5344561d5e24d7145594", "engines": {"node": ">= 0.4"}, "gitHead": "0e13fc12df09f3a7ac30116ef13bba820c220730", "scripts": {"jscs": "jscs test/*.js *.js", "lint": "npm run jscs", "test": "npm run lint && node test/index.js && npm run security", "eslint": "eslint --env=node test/*.js *.js", "eccheck": "editorconfig-tools check *.js **/*.js > /dev/null", "coverage": "covert test/*.js", "security": "nsp package", "coverage-quiet": "covert test/*.js --quiet"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/9.0..latest", "firefox/4.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/11.6..latest", "opera/next", "safari/5.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/es-shims/RegExp.prototype.flags.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "ES6 spec-compliant RegExp.prototype.flags shim.", "directories": {}, "dependencies": {}, "devDependencies": {"nsp": "~0.5.2", "jscs": "~1.8.1", "tape": "~3.0.3", "covert": "1.0.0", "eslint": "~0.10.1", "editorconfig-tools": "~0.0.1"}}, "1.0.1": {"name": "regexp.prototype.flags", "version": "1.0.1", "keywords": ["RegExp.prototype.flags", "regex", "ES6", "shim", "flags", "regexp", "RegExp#flags", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "regexp.prototype.flags@1.0.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/es-shims/RegExp.prototype.flags", "bugs": {"url": "https://github.com/es-shims/RegExp.prototype.flags/issues"}, "dist": {"shasum": "54a06afd02cf5892de05ec73f5b7ad4b71c2682f", "tarball": "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.0.1.tgz", "integrity": "sha512-oi7VF8qviteIaruBliW4x3nf3AG2eWx1IQQkbxYDcpdTxx66ijf68VG9XYRgQGpFtFbASI6qtawvECCHW3bT/w==", "signatures": [{"sig": "MEYCIQC25MDRShpF9nP4iJmiVL6vQzm/M9xFLu/5JOU18iKEmAIhAJvzZYQpqxCS9Idob3dxKzIuMWXA7bqrmZPM3upRiL7X", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "54a06afd02cf5892de05ec73f5b7ad4b71c2682f", "engines": {"node": ">= 0.4"}, "gitHead": "aa57c95172cd6059fc412b90f25d9cfc52180a40", "scripts": {"jscs": "jscs test/*.js *.js", "lint": "npm run jscs", "test": "npm run lint && node test/index.js && npm run security", "eslint": "eslint --env=node test/*.js *.js", "eccheck": "editorconfig-tools check *.js **/*.js > /dev/null", "coverage": "covert test/*.js", "security": "nsp package", "coverage-quiet": "covert test/*.js --quiet"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/9.0..latest", "firefox/4.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/11.6..latest", "opera/next", "safari/5.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/es-shims/RegExp.prototype.flags.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "ES6 spec-compliant RegExp.prototype.flags shim.", "directories": {}, "dependencies": {}, "devDependencies": {"nsp": "~0.5.2", "jscs": "~1.8.1", "tape": "~3.0.3", "covert": "1.0.0", "eslint": "~0.10.1", "editorconfig-tools": "~0.0.1"}}, "1.1.0": {"name": "regexp.prototype.flags", "version": "1.1.0", "keywords": ["RegExp.prototype.flags", "regex", "ES6", "shim", "flags", "regexp", "RegExp#flags", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "regexp.prototype.flags@1.1.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/es-shims/RegExp.prototype.flags#readme", "bugs": {"url": "https://github.com/es-shims/RegExp.prototype.flags/issues"}, "dist": {"shasum": "79e7b15c70505b295d20711a7cad580a43d97d7f", "tarball": "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.1.0.tgz", "integrity": "sha512-ave6lx0uUygr7lEPGu+gA/MoP1DGB7/88Hy79u1AXb2Z/ohF+KE/qffNBEuoU2gWnw4jH9K+LUbNt7eU5kZrCg==", "signatures": [{"sig": "MEUCIQDBTw98ipAojp9xvJlJFa7681lcBC1Y7G7O/qYNJETUfAIgbidmu4o9w2hlkGs2jN3g631BDxgXquRHu2XiUiEGAUU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "79e7b15c70505b295d20711a7cad580a43d97d7f", "engines": {"node": ">= 0.4"}, "gitHead": "cf59c1814d6a9e2039d09f046d24d1549bf613aa", "scripts": {"jscs": "jscs test/*.js *.js", "lint": "npm run jscs && npm run eslint", "test": "npm run lint && es-shim-api --bound && node test/index.js && npm run security", "eslint": "eslint test/*.js *.js", "eccheck": "editorconfig-tools check *.js **/*.js > /dev/null", "coverage": "covert test/*.js", "security": "nsp package", "coverage-quiet": "covert test/*.js --quiet"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/9.0..latest", "firefox/4.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/11.6..latest", "opera/next", "safari/5.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/es-shims/RegExp.prototype.flags.git", "type": "git"}, "_npmVersion": "2.14.0", "description": "ES6 spec-compliant RegExp.prototype.flags shim.", "directories": {}, "_nodeVersion": "3.0.0", "dependencies": {"define-properties": "^1.1.1"}, "devDependencies": {"nsp": "^1.0.3", "jscs": "^2.1.0", "tape": "^4.2.0", "covert": "^1.1.0", "eslint": "^1.1.0", "@es-shims/api": "^1.0.0", "editorconfig-tools": "^0.1.1", "@ljharb/eslint-config": "^1.0.4"}}, "1.1.1": {"name": "regexp.prototype.flags", "version": "1.1.1", "keywords": ["RegExp.prototype.flags", "regex", "regular expression", "ES6", "shim", "flag", "flags", "regexp", "RegExp#flags", "polyfill", "es-shim API"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "regexp.prototype.flags@1.1.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/es-shims/RegExp.prototype.flags#readme", "bugs": {"url": "https://github.com/es-shims/RegExp.prototype.flags/issues"}, "dist": {"shasum": "4b45162251d73bbd1a73555ad2f109be1f4c2f4a", "tarball": "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.1.1.tgz", "integrity": "sha512-+Vj34msbzURlTpzYnrcawDEJc67XV6IBUEztdn7CRCnZrWU+rYBx3D5Qy8TM03QTRoEs9fOPPKln3IPFNbzOgQ==", "signatures": [{"sig": "MEQCIARiDuov9HYA4WXc6UHc23Q2cw5jmnuzPGZf1IRFugy+AiBTFEoMZ5A2E7v0mlOmkNdHk95EtiWaxIqSBjPspdqvGQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "4b45162251d73bbd1a73555ad2f109be1f4c2f4a", "engines": {"node": ">= 0.4"}, "gitHead": "de150bb4e689785a11d6898eefefcc1875bd0b4e", "scripts": {"jscs": "jscs test/*.js *.js", "lint": "npm run jscs && npm run eslint", "test": "npm run lint && es-shim-api --bound && node test/index.js && npm run security", "eslint": "eslint test/*.js *.js", "eccheck": "editorconfig-tools check *.js **/*.js > /dev/null", "coverage": "covert test/*.js", "security": "nsp package", "coverage-quiet": "covert test/*.js --quiet"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/9.0..latest", "firefox/4.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/11.6..latest", "opera/next", "safari/5.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/es-shims/RegExp.prototype.flags.git", "type": "git"}, "_npmVersion": "2.13.3", "description": "ES6 spec-compliant RegExp.prototype.flags shim.", "directories": {}, "_nodeVersion": "3.0.0", "dependencies": {"define-properties": "^1.1.1"}, "devDependencies": {"nsp": "^1.0.3", "jscs": "^2.1.0", "tape": "^4.2.0", "covert": "^1.1.0", "eslint": "^1.1.0", "@es-shims/api": "^1.0.0", "editorconfig-tools": "^0.1.1", "@ljharb/eslint-config": "^1.0.4"}}, "1.2.0": {"name": "regexp.prototype.flags", "version": "1.2.0", "keywords": ["RegExp.prototype.flags", "regex", "regular expression", "ES6", "shim", "flag", "flags", "regexp", "RegExp#flags", "polyfill", "es-shim API"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "regexp.prototype.flags@1.2.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/es-shims/RegExp.prototype.flags#readme", "bugs": {"url": "https://github.com/es-shims/RegExp.prototype.flags/issues"}, "dist": {"shasum": "6b30724e306a27833eeb171b66ac8890ba37e41c", "tarball": "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.2.0.tgz", "integrity": "sha512-ztaw4M1VqgMwl9HlPpOuiYgItcHlunW0He2fE6eNfT6E/CF2FtYi9ofOYe4mKntstYk0Fyh/rDRBdS3AnxjlrA==", "signatures": [{"sig": "MEQCIFmnLXY2yjq4Q4sQwykUs9GzxLtiY3K3eEp1qqeLLd0+AiBKxpBGInfaQzNdR9F+k0hEzg+bt3k3w7AfhZDh2zzklA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "engines": {"node": ">= 0.4"}, "gitHead": "e629ce3d469060bdfff837eb556bfa94bc80c092", "scripts": {"jscs": "jscs test/*.js *.js", "lint": "npm run --silent jscs && npm run --silent eslint", "test": "npm run --silent tests-only", "eslint": "eslint test/*.js *.js", "eccheck": "editorconfig-tools check *.js **/*.js > /dev/null", "pretest": "npm run --silent lint", "coverage": "covert test/*.js", "posttest": "npm run --silent security", "security": "nsp check", "tests-only": "es-shim-api --bound && node --harmony --es-staging test/index.js", "coverage-quiet": "covert test/*.js --quiet"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/9.0..latest", "firefox/4.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/11.6..latest", "opera/next", "safari/5.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/es-shims/RegExp.prototype.flags.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "ES6 spec-compliant RegExp.prototype.flags shim.", "directories": {}, "_nodeVersion": "8.8.0", "dependencies": {"define-properties": "^1.1.2"}, "devDependencies": {"has": "^1.0.1", "nsp": "^2.8.1", "jscs": "^3.0.7", "tape": "^4.8.0", "covert": "^1.1.0", "eslint": "^4.9.0", "@es-shims/api": "^1.3.0", "editorconfig-tools": "^0.1.1", "@ljharb/eslint-config": "^12.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/regexp.prototype.flags-1.2.0.tgz_1508908451848_0.3062057360075414", "host": "s3://npm-registry-packages"}}, "1.3.0": {"name": "regexp.prototype.flags", "version": "1.3.0", "keywords": ["RegExp.prototype.flags", "regex", "regular expression", "ES6", "shim", "flag", "flags", "regexp", "RegExp#flags", "polyfill", "es-shim API"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "regexp.prototype.flags@1.3.0", "maintainers": [{"name": "es-shims-owner", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/es-shims/RegExp.prototype.flags#readme", "bugs": {"url": "https://github.com/es-shims/RegExp.prototype.flags/issues"}, "dist": {"shasum": "7aba89b3c13a64509dabcf3ca8d9fbb9bdf5cb75", "tarball": "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.3.0.tgz", "fileCount": 14, "integrity": "sha512-2+Q0C5g951OlYlJz6yu5/M33IcsESLlLfsyIaLJaG4FA2r4yP8MvVMJUUP/fVBkSpbbbZlS5gynbEWLipiiXiQ==", "signatures": [{"sig": "MEUCIQCjWPL5PqTJSWD0qoKwaSHgnWbCq5wkoHnDsz2YIWhKKwIgXkStGk5d5HaJwRtvYUVkPK2HoTSHTLuDMSyGRrsiEWU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15793, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd9b05CRA9TVsSAnZWagAAf9oP/RFyX+2qmpcZqZh9TyDt\nsOUwPKgHagv6we2crsWxkALCZbsktWC5JTxPsDsDxeYt09oBRG1vY8t0mADd\nMCPXVYmsmjCaZcG2pUP/KhP50LupAb6gqfAqsilmziGPD+D5zTDGjvfRwmk2\nSYTdntJ7JHmV+FOs7EogR9CxoDNmF6MxR10md0fXrr8gqcX4WCXTjAkBbH8O\nyqeBtlhl51ttWY1DmAKn6lDx7nSXnkvYiiCzISMJZ5Fxsd23q+N500g6rYDJ\njlrYvlCUD1on9bfOIs7C8e4pehxWr8V7QVzQ8sK3KZxO1Cm/pgJ/F+2ehG4u\ngbtZX68sZH6iM/IbeIvt/2VgPB7YXoTJLhHCbdI8ec0q/AGtPwZNtEhTDgSM\nNO9xbASsYduK6imuCJzxqQuhJL09IpTPfD7Q0+e9n9VCvs9uj9kFvTXxqvnL\n5aPUoA4NhQ8hnIBIre9GUVC9c7Y298+ddgq1waGT1wEIJhVyzhHrzVEnpHOB\nDnxdhEUSd0CKnKpjO81/OBy4IVFn53rN1u9NOhLHYzokuakOgG1xgf/4tTdZ\n8vh7k9AU+hNhnWRdfjYub4IxJu/Tw1Q95U+cg+hnZw4ZrF9IwsecdLIxRiuJ\nVpNgE8WAK1gY2fRc1lUR/i+b9M8NYktB9T15kyQhSCostL97Hso1rp+Rcg14\n3wgd\r\n=AS4C\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "f1b1ec0c2e036840204766ecbc8a3a1ff095f163", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "eccheck": "eclint check *.js **/*.js > /dev/null", "pretest": "npm run lint", "coverage": "covert test/*.js", "posttest": "npx aud", "tests-only": "es-shim-api --bound && node --harmony --es-staging test/index.js", "coverage-quiet": "covert test/*.js --quiet"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/9.0..latest", "firefox/4.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/11.6..latest", "opera/next", "safari/5.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/es-shims/RegExp.prototype.flags.git", "type": "git"}, "_npmVersion": "6.13.1", "description": "ES6 spec-compliant RegExp.prototype.flags shim.", "directories": {}, "_nodeVersion": "13.3.0", "dependencies": {"es-abstract": "^1.17.0-next.1", "define-properties": "^1.1.3"}, "_hasShrinkwrap": false, "devDependencies": {"has": "^1.0.3", "tape": "^4.11.0", "covert": "^1.1.1", "eclint": "^2.8.1", "eslint": "^6.7.2", "@es-shims/api": "^2.1.2", "@ljharb/eslint-config": "^15.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/regexp.prototype.flags_1.3.0_1576385849027_0.23217087361572486", "host": "s3://npm-registry-packages"}}, "1.3.1": {"name": "regexp.prototype.flags", "version": "1.3.1", "keywords": ["RegExp.prototype.flags", "regex", "regular expression", "ES6", "shim", "flag", "flags", "regexp", "RegExp#flags", "polyfill", "es-shim API"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "regexp.prototype.flags@1.3.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/es-shims/RegExp.prototype.flags#readme", "bugs": {"url": "https://github.com/es-shims/RegExp.prototype.flags/issues"}, "dist": {"shasum": "7ef352ae8d159e758c0eadca6f8fcb4eef07be26", "tarball": "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.3.1.tgz", "fileCount": 17, "integrity": "sha512-JiBdRBq91WlY7uRJ0ds7R+dU02i6LKi8r3BuQhNXn+kmeLN+EfHhfjqMRis1zJxnlu88hq/4dx0P2OP3APRTOA==", "signatures": [{"sig": "MEQCIHJaHfToMal8SJepok0t4+H8DpFmErnMIzMGr/up+pw6AiBwEdFZzls45wOKz4Da57Hc6R8MZ7RG0dEd7jhy4yBYZQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30842, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgAcCCCRA9TVsSAnZWagAA2iYP/186oMWC1Hd0pGjSZz1c\nndiCeQk5NdkFgVTJQsBpVsr0RNHX4pTzxvJfKE5P2c2wDAwookQikAOWE8X9\nRbPGqecNSrFvQkFNjtwrIo2rk6Ym4xVU6Zsx+WHu+wuEEqGUrGHCZKNxwh9g\nrHKOOfNGv0W+MiilwhqK5C4v9vpClnQYi73unuaRPzZLiXVfwDfw/hOeLIkW\nXHZ12OgkNJ3qEVhI9CDsmNvfm+TwUo3TIi8wIyuRSeQB2JReLTgG8n//lkV6\nMxwya3paKSyU+93uEWha4wxae+jiIizBPuFT3b40jNJUyOlWBQPrTWnPgoiB\n5vjr57IFri6kkb7GtuPNlQl7Whay+li+uk2zNCjf2tms0BBeEEeiGYsjUT+u\nBJT90AXvzBwmYekMReSsXB+RbMwoFT1CHOcM2LWC8PLQEkbQTbRJ47bBWsbO\ngSYS7JjJ4mBx9nwVh05m5mV91TydlLR8r88dMtZKw9JTyItS98FZhwbENdsp\ndE5htmReEg9XM6WiwzI66qIxbRCgIfqG5CTcVy5R0rDpaA/2MgRQLU6AoU5A\nRHwTFFxAKD8yOHhvdCaBk90loUSP1VHU1hWlrDH7v3Pztzy+k6pGjd+ybP3U\nqj4gJIIzeqGaCWh4gx7OWSy4TgEWLXP/7it+6tCHaaog23OviAvyyg/le+do\n8t7E\r\n=nSmk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "e4ba14753c062536f445fb6fa9c462bcb1306319", "scripts": {"lint": "eslint .", "test": "npm run tests-only", "eccheck": "eclint check *.js **/*.js > /dev/null", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "es-shim-api --bound", "posttest": "aud --production", "prepublish": "safe-publish-latest", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/9.0..latest", "firefox/4.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/11.6..latest", "opera/next", "safari/5.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/es-shims/RegExp.prototype.flags.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "ES6 spec-compliant RegExp.prototype.flags shim.", "directories": {}, "_nodeVersion": "14.15.4", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.3"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^1.1.3", "has": "^1.0.3", "nyc": "^10.3.2", "tape": "^5.1.1", "covert": "^1.1.1", "eclint": "^2.8.1", "eslint": "^7.17.0", "@es-shims/api": "^2.1.2", "auto-changelog": "^2.2.1", "object-inspect": "^1.9.0", "has-strict-mode": "^1.0.1", "safe-publish-latest": "^1.1.4", "functions-have-names": "^1.2.2", "@ljharb/eslint-config": "^17.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/regexp.prototype.flags_1.3.1_1610727553905_0.****************", "host": "s3://npm-registry-packages"}}, "1.3.2": {"name": "regexp.prototype.flags", "version": "1.3.2", "keywords": ["RegExp.prototype.flags", "regex", "regular expression", "ES6", "shim", "flag", "flags", "regexp", "RegExp#flags", "polyfill", "es-shim API"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "regexp.prototype.flags@1.3.2", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/es-shims/RegExp.prototype.flags#readme", "bugs": {"url": "https://github.com/es-shims/RegExp.prototype.flags/issues"}, "dist": {"shasum": "ea664a0cf7fecf6bf811a84a06ba88c52b38a5a6", "tarball": "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.3.2.tgz", "fileCount": 16, "integrity": "sha512-uaro52GSI5be7+ssxjxxnLlleDBN3VHIWQHvBhfeeSXRQkuV/0Jo/hBU+omYH6NUkM+LYpTHnRRf2W/v+x7LzQ==", "signatures": [{"sig": "MEUCIE1Xh1yoG5qe4Bv5sw4ndnr3grp660sfD/V1i2aQ8FBdAiEAj+D93NsppXwhgGEsU2Gx3idnXwySCwra/E2JVeg7Gt4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33235, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4I6TCRA9TVsSAnZWagAAWlgP/Ra/GRlq7rev6CdDfQ+G\np35EilsMY3ZPf1Gh4bRPOk6FhRGmoXvTHQLa/m+R4euKVn0J44b5ZmiR58eW\n2xM5LlwkDDys1SKpyITmptHJqEkbFVJ4gfMxUNXmKOLAenHErj/+hPES/Fur\nTLyNtAJKxIcYUSgzogJo6aZdlhAepXpzCFXDkJ9ZEHlL3OqbxSmZAisy4qAK\nVN662LPzz5NbcFlbsLV+2IXDd11ofxaG/Hh3fDHZaHTZ5yHp4aRbCUQW2hDF\niciwaTFaxHqXuT3vHeDbGolJu9Sj8Wnj5+8JwPo4L/fTlH+iRXoMi3zAW9AN\neTqsrN4En+XDoBq/sruajjACILJF1PqqeM/T7Q36m8T6NBGGpAX9Xd1OqJXa\nh4r+Xh9uOv5ANUZ+Xrg1RRjBMszuzt9F/HbRZ/T2XW/RfcXCEHwxcEnr3noP\ne7UoMDn32/lVRyGJ1hahepdlBrVtsA6n9CXsp6pDSH92LDA8+ua1jHrcVk85\n0Ke5IMnDpf/wJhE8FyH2QZFTrZRn9vi0+IgxBspN3HECVg255kcP/BCCAWuc\nfK6ejRkq6IJ3xGgCSH49gx5g6ujvRxlKsoif5fZEce9NpMCxUN4BTFBIPAsx\nbW9hy2qGv0/ExxQGPeeqk9BViOJVH4mY8oRyInWVQqbNGIj4jlbgUBCljRBY\nrvTv\r\n=9iac\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "45b78d54fbaf80d2ff0649fffffcdcc452adc6fc", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "es-shim-api --bound", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/9.0..latest", "firefox/4.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/11.6..latest", "opera/next", "safari/5.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/es-shims/RegExp.prototype.flags.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "ES6 spec-compliant RegExp.prototype.flags shim.", "directories": {}, "_nodeVersion": "17.3.1", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.3"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.0", "has": "^1.0.3", "nyc": "^10.3.2", "tape": "^5.4.0", "covert": "^1.1.1", "eclint": "^2.8.1", "eslint": "^8.6.0", "@es-shims/api": "^2.2.3", "auto-changelog": "^2.3.0", "object-inspect": "^1.12.0", "has-strict-mode": "^1.0.1", "safe-publish-latest": "^2.0.0", "functions-have-names": "^1.2.2", "@ljharb/eslint-config": "^20.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/regexp.prototype.flags_1.3.2_1642106515274_0.****************", "host": "s3://npm-registry-packages"}}, "1.4.0": {"name": "regexp.prototype.flags", "version": "1.4.0", "keywords": ["RegExp.prototype.flags", "regex", "regular expression", "ES6", "shim", "flag", "flags", "regexp", "RegExp#flags", "polyfill", "es-shim API"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "regexp.prototype.flags@1.4.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/es-shims/RegExp.prototype.flags#readme", "bugs": {"url": "https://github.com/es-shims/RegExp.prototype.flags/issues"}, "dist": {"shasum": "320e3ccb61ebb74daebffd1c09e484ea3c67070b", "tarball": "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.4.0.tgz", "fileCount": 16, "integrity": "sha512-OE85RadmCYZJzYgIcWd2Qum/wJ20WwY5Z6Bfv5FeBPU46uPD01s3pe2LNoi0etmr83ibsFidC0ZiKXmPY5UpmQ==", "signatures": [{"sig": "MEYCIQD+bZuVE/JpHYremJ3Z1GeR4AdmZcicyOPGxLXKQHSYEAIhALBiChYvQdjDp5NXmj951kNoOErlgthoAhZ6bJA4WrLK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34000, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4QH9CRA9TVsSAnZWagAArn4P/Al1SXobp8hPI8HgZQzj\nev3rFj+lh3ppXEOwms36dj/LI/A1XRcqrNgFwZrJx8QbtLvTg3l8IAndnaQ0\njEgRUOH5cNOfm3efBFt2Frg35ZGI48WTmCCMItkwtG6b9A7QN8ibC7pACgbf\n5+9Sa5W/38J/R3DHC0rO5HwcADtZBr7kaWw6VM/EN9tUfO1aWOIK22az+BZB\n9y7SU4LKZEfhKDdF3HqKVNpAW0Z5ELinQpErqis/8zfqiYCNt69qvDmagFdJ\nXs6x3AYnmpuJSQIueHXIyhIVnp9tffxCLq9jTsnJ7mb4eIXzuIDS6HuWXsxr\nj6+7BTutj0X/Y7fvmjFaqdkEhJ4h8Z6pz9mtW+z7cDxFFXL4Es/6WWv+wg1N\nymgN8QVWq8MAlQBCAFRkLQl9NSiymBJi72MpGXb6qFSjnOuogDhLDy91EDFv\nBBX9SA9SAOiqMBCxCZ+7vfKzpZU+bXLxnuDP8cMlUY9tb0D4nzSXukDbmwLm\nEHF7L1V1+T01vXtQP/Dw+nI4gtwPo4WbU779fJzDeQBQZN2KfMTlBR3n7ALF\njRlsiKaYci25Dw+ud3F7hbUiN7cMwm/QMlyRe2w1G+0BdgUIkslvelUwxDFA\njj87Cz+bsE0vTPGox1jtAHGQmir0URp7X+8RY8CsyJgHq5uPlBB11N7F7DST\nyX+1\r\n=1z8w\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "3ce4dd6a05a6bdf58374652ff79a4fcc637da1e9", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "es-shim-api --bound", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/9.0..latest", "firefox/4.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/11.6..latest", "opera/next", "safari/5.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/es-shims/RegExp.prototype.flags.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "ES6 spec-compliant RegExp.prototype.flags shim.", "directories": {}, "_nodeVersion": "17.3.1", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.3"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.0", "has": "^1.0.3", "nyc": "^10.3.2", "tape": "^5.4.0", "covert": "^1.1.1", "eclint": "^2.8.1", "eslint": "^8.6.0", "foreach": "^2.0.5", "@es-shims/api": "^2.2.3", "auto-changelog": "^2.3.0", "object-inspect": "^1.12.0", "has-strict-mode": "^1.0.1", "safe-publish-latest": "^2.0.0", "functions-have-names": "^1.2.2", "@ljharb/eslint-config": "^20.2.0", "available-regexp-flags": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/regexp.prototype.flags_1.4.0_1642136060918_0.8199974239403245", "host": "s3://npm-registry-packages"}}, "1.4.1": {"name": "regexp.prototype.flags", "version": "1.4.1", "keywords": ["RegExp.prototype.flags", "regex", "regular expression", "ES6", "shim", "flag", "flags", "regexp", "RegExp#flags", "polyfill", "es-shim API"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "regexp.prototype.flags@1.4.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/es-shims/RegExp.prototype.flags#readme", "bugs": {"url": "https://github.com/es-shims/RegExp.prototype.flags/issues"}, "dist": {"shasum": "b3f4c0059af9e47eca9f3f660e51d81307e72307", "tarball": "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.4.1.tgz", "fileCount": 16, "integrity": "sha512-pMR7hBVUUGI7PMA37m2ofIdQCsomVnas+Jn5UPGAHQ+/LlwKm/aTLJHdasmHRzlfeZwHiAOaRSo2rbBDm3nNUQ==", "signatures": [{"sig": "MEYCIQCxNlckUYcovtXJHIJdx7yPlDTjWMhX5KGzdsYDMi0i9QIhAOV8MvPx+AKu9fZjdf+CTc3szY3Q6iIwzXNvZJUZ1u7X", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34139, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4SXHCRA9TVsSAnZWagAACaAP/i03nJfLbXpBm3Uwe0uA\nNydngtSX0tXrx3b7KozrdFk+oVIOQxQC5ld2bxb0nM1BTRHfLx1/QGU7yI3x\nemDUwkqf1+iLwEdcMYLkL52sPN9+EhfJ0Qw6IE6Q+XnpUm34xNI7m6fHpQDm\ni1UbGfAx4Sgi25xkJ4obxcHAB2Qa7OwapPvmNX6acMRUlBl3OR9ljjPXHg/y\n0s1ws4mLqgobHEeYBK4yQdvxtpHRKLdG9dPJbiqNXdOD5TtlVMgR+Y8yKUfe\nj0TFlr/RWhRh7v/tPh+pqnC8kDBoXJpRkwyaj4jcyWbAr8lDQpx75Yben69M\n2K6uckbIwu6kNgXXqyOj3Se1wWGbS+YJVMaAGiCkGfCXG5PSfTbOThQlpIgK\nUMoM8m1Yrc+nG2Y7SIGgoyB5WiRAKEuq6oX7wIgXJ5jjWTIpV3Q1FBmRg0n4\nuG7qikIU6HruWNh72WjjlkUo+4esHrrvj1okbIH6UN2kdNcHP6Zddta50q0t\nhMkzT6Miy4/cUbfb4KVpB0D9ZpfX4P8LpgnFWDMdzgZ1sa+iozCNYT4X524e\nH+xx6gLjnIx+TZQ3wKXYyM6dN4UyvtRYxuIHD7DHyqpD3D+cGJo7klvrDp+W\n5dFk+A7twxwSlvjZrZSJqJ+jCpyzzhb+27pBC67AyA4VAB80D6Ez7DFgFCUF\nUUR7\r\n=Y3r9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "81d3adc11ae344cc2b85d643b8df07de5fefba29", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "es-shim-api --bound", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/9.0..latest", "firefox/4.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/11.6..latest", "opera/next", "safari/5.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/es-shims/RegExp.prototype.flags.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "ES6 spec-compliant RegExp.prototype.flags shim.", "directories": {}, "_nodeVersion": "17.3.1", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.3"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.0", "has": "^1.0.3", "nyc": "^10.3.2", "tape": "^5.4.0", "covert": "^1.1.1", "eclint": "^2.8.1", "eslint": "^8.6.0", "foreach": "^2.0.5", "@es-shims/api": "^2.2.3", "auto-changelog": "^2.3.0", "object-inspect": "^1.12.0", "has-strict-mode": "^1.0.1", "safe-publish-latest": "^2.0.0", "functions-have-names": "^1.2.2", "@ljharb/eslint-config": "^20.2.0", "available-regexp-flags": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/regexp.prototype.flags_1.4.1_1642145222930_0.049692340395019574", "host": "s3://npm-registry-packages"}}, "1.4.2": {"name": "regexp.prototype.flags", "version": "1.4.2", "keywords": ["RegExp.prototype.flags", "regex", "regular expression", "ES6", "shim", "flag", "flags", "regexp", "RegExp#flags", "polyfill", "es-shim API"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "regexp.prototype.flags@1.4.2", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/es-shims/RegExp.prototype.flags#readme", "bugs": {"url": "https://github.com/es-shims/RegExp.prototype.flags/issues"}, "dist": {"shasum": "bf635117a2f4b755595ebb0c0ee2d2a49b2084db", "tarball": "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.4.2.tgz", "fileCount": 16, "integrity": "sha512-Ynz8fTQW5/1elh+jWU2EDDzeoNbD0OQ0R+D1VJU5ATOkUaro4A9YEkdN2ODQl/8UQFPPpZNw91fOcLFamM7Pww==", "signatures": [{"sig": "MEYCIQD+WdWbySTnTrsDZbyVrmUcfHuGLRwPsdD8y7L1Qbf9LAIhAOTfdeVupeaLeO4lvlrivLz3TIvb5r1Irp/WzbcMT+UL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35968, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVlfdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrGHA/9GrBKrNh9Ahxbpo0485IENj5SqVjWXGcLhDG7j05DqgrOye4o\r\ni7kvQHwPHKNYg3PJDm41wBGrV+DsRJ2DOkTIH59xFcn3VJk73WzinOkRaXwA\r\nn96nHzclB9vKw3nLHfGhYXcaxvb3thSXld8wX+FwvqOSyUaHkfai5LMCF4Ku\r\nRIlUe1UrwHwAz6RV0A34HNPPearvSsG/NpUrp7/X7lVP9t+BEVONuZ1WhNSn\r\ns/FSQuUIrq/dj+/7ymq7pk8pZWYm9Dyn7LFO8WnaIjDnJiDwt05UxBz1twkt\r\nLK+vlgsHzho86zBuY9AZA9riosgSnVUljilUir5+68SHborrKIScxsN5v16W\r\nOTkfVnPF5vFryspUaNzDWYcAitxhxVGUrEnTsQUSQrnpfojKGLqGiC00H53g\r\nEpMZTSH8nwTnbRsMZR1iwiLyoRdxHqm4KfZDDTQGPDBLGWROlOa1Z3x+Qxav\r\nwPVWMirbUwW3H3wznzZlYeSo/sHJiyq4eDfPHnhLIUOtoqhhfsDQ/SnKrlT4\r\nbVPndAio4wfUrLYwUocffVdM4ucdG2ocZ51pvYuKKvnY9IQSmL1OxO2ZGxlK\r\nlD5zvphuY9sV6Q1Z8TLrJF5hEBByu1JqkZZTxqNlXINs4NRzJC6kZPH8lZBE\r\n2xh5/fK5hQzXk9isYoii6vh7JXNMVJkefjs=\r\n=euVf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "cc38017efaf0ac9ebe857e25bc08499c9916fc7a", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "es-shim-api --bound", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/9.0..latest", "firefox/4.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/11.6..latest", "opera/next", "safari/5.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/es-shims/RegExp.prototype.flags.git", "type": "git"}, "_npmVersion": "8.3.1", "description": "ES6 spec-compliant RegExp.prototype.flags shim.", "directories": {}, "_nodeVersion": "17.9.0", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.3"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.0", "has": "^1.0.3", "nyc": "^10.3.2", "tape": "^5.5.3", "covert": "^1.1.1", "eclint": "^2.8.1", "eslint": "=8.8.0", "foreach": "^2.0.5", "@es-shims/api": "^2.2.3", "auto-changelog": "^2.4.0", "object-inspect": "^1.12.0", "has-strict-mode": "^1.0.1", "safe-publish-latest": "^2.0.0", "functions-have-names": "^1.2.2", "@ljharb/eslint-config": "^21.0.0", "available-regexp-flags": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/regexp.prototype.flags_1.4.2_1649825757541_0.****************", "host": "s3://npm-registry-packages"}}, "1.4.3": {"name": "regexp.prototype.flags", "version": "1.4.3", "keywords": ["RegExp.prototype.flags", "regex", "regular expression", "ES6", "shim", "flag", "flags", "regexp", "RegExp#flags", "polyfill", "es-shim API"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "regexp.prototype.flags@1.4.3", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/es-shims/RegExp.prototype.flags#readme", "bugs": {"url": "https://github.com/es-shims/RegExp.prototype.flags/issues"}, "dist": {"shasum": "87cab30f80f66660181a3bb7bf5981a872b367ac", "tarball": "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.4.3.tgz", "fileCount": 16, "integrity": "sha512-fjggEOO3slI6Wvgjwflkc4NFRCTZAu5CnNfBd5qOMYhWdn67nJBBu34/TkD++eeFmd8C9r9jfXJ27+nSiRkSUA==", "signatures": [{"sig": "MEQCIDZ9uI6Yr8FIbQCsU/uVf+8X/se7P6NeedHyzzoQyiQ6AiB7Vss0thvWJp08+XgxCHYHe//tCLHhsWCxF2jzabo/KQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36699, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWOeGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpexRAAogpKB+7pwv9KnT7g1nlLivQzGQeTgTHkC+RT82Fed0MhBPLC\r\ndsPyd0TP5tTW0IvT6gIq4VnpOrjCrULMDy91RfFN/oCqjEsdHtNr/2sGmbeD\r\nDbdhW+WjcEpTY1yfqxSqa+KX6QlLFt9NeezmVH5dp07TjTtWpQLKVDi4/ZQJ\r\noGGXTHtLayqn6HpK+S8KgtgTnmAg0WCtEGC5hl6RC2TgXKqbnon1Ml2j6K4S\r\n3+aEG4v6pB8+JaRG/MZ/L0GrzEyt9OMBsOshX3zq7l0rka6RjODjDVXJ6CDm\r\nfawxCN8SV7T7kpwiDKvBlVcCopTs4Tb5ZxcMCvnj4YIOWx92OsPabPeGnSxK\r\nNBbIj3WzqT205qM6/EFf2BN37UVEjAcCpu97ZLSVYSuBfiDOAZmTIZmq53ll\r\nte22K8U87E48ZGEOrY6XhOnrSy4AJjhpljtnq0XKjHZzNjf8R6F+tGJFYqWO\r\nT3vuDwbvDfZFAstNorlxyxnzFAJNNxSGwfH2PAT6UgM1bmwoK5hBjXa4ltC/\r\neIvtuK1o5E+FtkWXUO640x8lwpBhDUIcXV/xA3gxcu4twObeBKMeZxa57iYz\r\nN3ruPGbYsqZhH3t9df03A2ztVtGMPphdqGUUOWOiCEkl0iwecHmDD+pOFzKo\r\nxu9A/mxPrLXGAXJBf2DWmB6Vprs3Whg6bA8=\r\n=40QP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "0ed48d003353a0157f14ca8f2b5e3034830dd179", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "es-shim-api --bound", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/9.0..latest", "firefox/4.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/11.6..latest", "opera/next", "safari/5.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/es-shims/RegExp.prototype.flags.git", "type": "git"}, "_npmVersion": "8.3.1", "description": "ES6 spec-compliant RegExp.prototype.flags shim.", "directories": {}, "_nodeVersion": "17.9.0", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.3", "functions-have-names": "^1.2.2"}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.0", "has": "^1.0.3", "nyc": "^10.3.2", "tape": "^5.5.3", "covert": "^1.1.1", "eclint": "^2.8.1", "eslint": "=8.8.0", "foreach": "^2.0.5", "@es-shims/api": "^2.2.3", "auto-changelog": "^2.4.0", "object-inspect": "^1.12.0", "has-strict-mode": "^1.0.1", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.0.0", "available-regexp-flags": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/regexp.prototype.flags_1.4.3_1649993605741_0.33023018068904175", "host": "s3://npm-registry-packages"}}, "1.5.0": {"name": "regexp.prototype.flags", "version": "1.5.0", "keywords": ["RegExp.prototype.flags", "regex", "regular expression", "ES6", "shim", "flag", "flags", "regexp", "RegExp#flags", "polyfill", "es-shim API"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "regexp.prototype.flags@1.5.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/es-shims/RegExp.prototype.flags#readme", "bugs": {"url": "https://github.com/es-shims/RegExp.prototype.flags/issues"}, "dist": {"shasum": "fe7ce25e7e4cca8db37b6634c8a2c7009199b9cb", "tarball": "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.5.0.tgz", "fileCount": 16, "integrity": "sha512-0SutC3pNudRKgquxGoRGIz946MZVHqbNfPjBdxeOhBrdgDKlRoXmYLQN9xRbrR09ZXWeGAdPuif7egofn6v5LA==", "signatures": [{"sig": "MEQCIF8nFBMksqgFgUDpY3N9M/xVcQByYGrDUK99r+qsRB/nAiA7o6OvlRpJ4/YhG+HpT3s3+l45xY8+EqGTF/cNvpSK8g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38482, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkP5DxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr0Og//bDr0eMYt5/92CT13+C3ZsOHK/gJTWP6pD5bPr48PqGeYLCXM\r\nAm/Mfni6hWuKKhjHdKZWKvjB2qsCGON2iJUwzq4Me+89sHRRF3E3z7E7v6/U\r\nPVk/nR2Vj4IK/xjePjYc4kzupa0nArEHkdxMBRKIMet4tc2msrImbDMYTAla\r\nZ21cwdmhj5trNWMIg2bLfqu7l47HWtFqefA6rbwZv8z9lPOmFjNeod/vi++q\r\nVrBCpVN+DwvbgeqenqIF/DWPyL1pVrc1QKeb1W0hDaRJ0YwoeL2RUKBj8LXD\r\nj49iCL2NOte46fA3Jh88SHhfxECzuDcQU+lXJmGNaZ3oYyr+B0mqDdM7AMAE\r\n8+Q5GVi61XUjRFQ+pOHYOQN2da7T63EOkOpzS21jo1H1colmfpdQydb73Onm\r\nUVTjYYnMzYs+cOwtLLMNeTx+RaL7e5N04dtV4YITHWqAUbpDA5KKf+w8Jx8i\r\nQmX4ROIgYpKtHxU0+YOfZxl2rQGwwH3TVu1dr3yf1JGqRmaglUpz5k8qdyJ8\r\nTSnlfmPhFLwZfsVBBgYitKrMllSHSvIfkseq5hT4Zn251ABWac45+xcir1eV\r\n1bd6ASW8LcO0Jj6GvasrOeln/Fbdg5lim52qykGmA2WrQb4MqFYkKiHnQ64R\r\nVS5clwjo7tmXh6adQCBkR0kmw3XxTTO+9rk=\r\n=q0AB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "0edc251db52a5e7889ff4b44c7ea5273687ce410", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "es-shim-api --bound", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/9.0..latest", "firefox/4.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/11.6..latest", "opera/next", "safari/5.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/es-shims/RegExp.prototype.flags.git", "type": "git"}, "_npmVersion": "9.6.4", "description": "ES6 spec-compliant RegExp.prototype.flags shim.", "directories": {}, "_nodeVersion": "20.0.0", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.2.0", "functions-have-names": "^1.2.3"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.2", "has": "^1.0.3", "nyc": "^10.3.2", "tape": "^5.6.3", "covert": "^1.1.1", "eclint": "^2.8.1", "eslint": "=8.8.0", "for-each": "^0.3.3", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "@es-shims/api": "^2.3.1", "auto-changelog": "^2.4.0", "object-inspect": "^1.12.3", "has-strict-mode": "^1.0.1", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.0.1", "available-regexp-flags": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/regexp.prototype.flags_1.5.0_1681887473099_0.9588566361064341", "host": "s3://npm-registry-packages"}}, "1.5.1": {"name": "regexp.prototype.flags", "version": "1.5.1", "keywords": ["RegExp.prototype.flags", "regex", "regular expression", "ES6", "shim", "flag", "flags", "regexp", "RegExp#flags", "polyfill", "es-shim API"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "regexp.prototype.flags@1.5.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/es-shims/RegExp.prototype.flags#readme", "bugs": {"url": "https://github.com/es-shims/RegExp.prototype.flags/issues"}, "dist": {"shasum": "90ce989138db209f81492edd734183ce99f9677e", "tarball": "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.5.1.tgz", "fileCount": 16, "integrity": "sha512-sy6TXMN+hnP/wMy+ISxg3krXx7BAtWVO4UouuCN/ziM9UEne0euamVNafDfvC83bRNr95y0V5iijeDQFUNpvrg==", "signatures": [{"sig": "MEYCIQCUtZpsFw5Q5PL4/PMrEr1dpgKN0NBop7CyT3X+9mqRmQIhAKNErENmMNP1SdBR3dHxt3ZoRNuhvGFcq4romVPRlLq6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39023}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "ffb75fa85d009ebb5f581a4bf9bbd34458ccd8e4", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "es-shim-api --bound", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/9.0..latest", "firefox/4.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/11.6..latest", "opera/next", "safari/5.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/es-shims/RegExp.prototype.flags.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "ES6 spec-compliant RegExp.prototype.flags shim.", "directories": {}, "_nodeVersion": "20.6.0", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.2.0", "set-function-name": "^2.0.0"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.3", "has": "^1.0.3", "nyc": "^10.3.2", "tape": "^5.6.6", "covert": "^1.1.1", "eclint": "^2.8.1", "eslint": "=8.8.0", "for-each": "^0.3.3", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "@es-shims/api": "^2.4.2", "auto-changelog": "^2.4.0", "object-inspect": "^1.12.3", "has-strict-mode": "^1.0.1", "safe-publish-latest": "^2.0.0", "functions-have-names": "^1.2.3", "@ljharb/eslint-config": "^21.1.0", "available-regexp-flags": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/regexp.prototype.flags_1.5.1_1694582947788_0.****************", "host": "s3://npm-registry-packages"}}, "1.5.2": {"name": "regexp.prototype.flags", "version": "1.5.2", "keywords": ["RegExp.prototype.flags", "regex", "regular expression", "ES6", "shim", "flag", "flags", "regexp", "RegExp#flags", "polyfill", "es-shim API"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "regexp.prototype.flags@1.5.2", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/es-shims/RegExp.prototype.flags#readme", "bugs": {"url": "https://github.com/es-shims/RegExp.prototype.flags/issues"}, "dist": {"shasum": "138f644a3350f981a858c44f6bb1a61ff59be334", "tarball": "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.5.2.tgz", "fileCount": 16, "integrity": "sha512-NcDiDkTLuPR+++OCKB0nWafEmhg/Da8aUPLPMQbK+bxKKCm1/S5he+AqYa4PlMCVBalb4/yxIRub6qkEx5yJbw==", "signatures": [{"sig": "MEUCIHGmJmkJcXA1Ds1JCitREokUeJ6rVvE0G+/leJioGqa2AiEA7jtjC+EO/dZQ5pkSaWuQ6TqyqvT6nA2iLaKAVyWa3fA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40353}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "b1d53b17a24bab677103b9e1bff83d97caae8e9a", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "es-shim-api --bound", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/9.0..latest", "firefox/4.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/11.6..latest", "opera/next", "safari/5.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/es-shims/RegExp.prototype.flags.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "ES6 spec-compliant RegExp.prototype.flags shim.", "directories": {}, "_nodeVersion": "21.6.0", "dependencies": {"call-bind": "^1.0.6", "es-errors": "^1.3.0", "define-properties": "^1.2.1", "set-function-name": "^2.0.1"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.4", "nyc": "^10.3.2", "tape": "^5.7.4", "eclint": "^2.8.1", "eslint": "=8.8.0", "hasown": "^2.0.1", "for-each": "^0.3.3", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "@es-shims/api": "^2.4.2", "auto-changelog": "^2.4.0", "object-inspect": "^1.13.1", "has-strict-mode": "^1.0.1", "es-value-fixtures": "^1.4.2", "safe-publish-latest": "^2.0.0", "functions-have-names": "^1.2.3", "@ljharb/eslint-config": "^21.1.0", "available-regexp-flags": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/regexp.prototype.flags_1.5.2_1707673877306_0.09844798967529989", "host": "s3://npm-registry-packages"}}, "1.5.3": {"name": "regexp.prototype.flags", "version": "1.5.3", "keywords": ["RegExp.prototype.flags", "regex", "regular expression", "ES6", "shim", "flag", "flags", "regexp", "RegExp#flags", "polyfill", "es-shim API"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "regexp.prototype.flags@1.5.3", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/es-shims/RegExp.prototype.flags#readme", "bugs": {"url": "https://github.com/es-shims/RegExp.prototype.flags/issues"}, "dist": {"shasum": "b3ae40b1d2499b8350ab2c3fe6ef3845d3a96f42", "tarball": "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.5.3.tgz", "fileCount": 17, "integrity": "sha512-vqlC04+RQoFalODCbCumG2xIOvapzVMHwsyIGM/SIE8fRhFFsXeH8/QQ+s0T0kDAhKc4k30s73/0ydkHQz6HlQ==", "signatures": [{"sig": "MEUCIB2eHSQ7Vg8TqkYGS/VYKO3lOqz74DeCYstiCyPR7dttAiEAqJLcy1dc3d2pybM/WRvw7CSwYdtUlKX4EKO0FFb/iWQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43703}, "main": "index.js", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "1ab57aad4a58f9f86bef3f2e6af671bb3c80a4af", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "evalmd README.md && es-shim-api --bound", "posttest": "npx npm@'>=10.2' audit --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/9.0..latest", "firefox/4.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/11.6..latest", "opera/next", "safari/5.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "repository": {"url": "git://github.com/es-shims/RegExp.prototype.flags.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "ES6 spec-compliant RegExp.prototype.flags shim.", "directories": {}, "_nodeVersion": "22.9.0", "dependencies": {"call-bind": "^1.0.7", "es-errors": "^1.3.0", "define-properties": "^1.2.1", "set-function-name": "^2.0.2"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"nyc": "^10.3.2", "tape": "^5.9.0", "eclint": "^2.8.1", "eslint": "=8.8.0", "evalmd": "^0.0.19", "hasown": "^2.0.2", "encoding": "^0.1.13", "for-each": "^0.3.3", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "@es-shims/api": "^2.5.1", "auto-changelog": "^2.5.0", "object-inspect": "^1.13.2", "has-strict-mode": "^1.0.1", "es-value-fixtures": "^1.5.0", "safe-publish-latest": "^2.0.0", "functions-have-names": "^1.2.3", "@ljharb/eslint-config": "^21.1.1", "available-regexp-flags": "^1.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/regexp.prototype.flags_1.5.3_1727927983749_0.7084273013748432", "host": "s3://npm-registry-packages"}}, "1.5.4": {"name": "regexp.prototype.flags", "version": "1.5.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "description": "ES6 spec-compliant RegExp.prototype.flags shim.", "license": "MIT", "main": "index.js", "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "posttest": "npx npm@'>=10.2' audit --production", "tests-only": "nyc tape 'test/**/*.js'", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "lint": "eslint --ext=js,mjs .", "postlint": "evalmd README.md && es-shim-api --bound", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/es-shims/RegExp.prototype.flags.git"}, "keywords": ["RegExp.prototype.flags", "regex", "regular expression", "ES6", "shim", "flag", "flags", "regexp", "RegExp#flags", "polyfill", "es-shim API"], "dependencies": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-errors": "^1.3.0", "get-proto": "^1.0.1", "gopd": "^1.2.0", "set-function-name": "^2.0.2"}, "devDependencies": {"@es-shims/api": "^2.5.1", "@ljharb/eslint-config": "^21.1.1", "auto-changelog": "^2.5.0", "available-regexp-flags": "^1.0.4", "eclint": "^2.8.1", "encoding": "^0.1.13", "es-value-fixtures": "^1.5.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "functions-have-names": "^1.2.3", "has-strict-mode": "^1.0.1", "hasown": "^2.0.2", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.3", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/9.0..latest", "firefox/4.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/11.6..latest", "opera/next", "safari/5.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "_id": "regexp.prototype.flags@1.5.4", "gitHead": "36fcf221a351d8451d02bcdf91e63cf3ee83b1db", "bugs": {"url": "https://github.com/es-shims/RegExp.prototype.flags/issues"}, "homepage": "https://github.com/es-shims/RegExp.prototype.flags#readme", "_nodeVersion": "23.5.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-dYqgNSZbDwkaJ2ceRd9ojCGjBq+mOm9LmtXnAnEGyHhN/5R7iDW2TRw3h+o/jCFxus3P2LfWIIiwowAjANm7IA==", "shasum": "1ad6c62d44a259007e55b3970e00f746efbcaa19", "tarball": "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.5.4.tgz", "fileCount": 17, "unpackedSize": 44310, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBohNMANim5vaGmigomBCSnRsjxYE8gWDbjJEp5mHIfsAiEA94XmKdn75sWf4VSBaHZ6z3/pocEIucvK8Sh4minI6Tg="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/regexp.prototype.flags_1.5.4_1735864832885_0.6431442628571149"}, "_hasShrinkwrap": false}}, "time": {"created": "2014-12-10T10:13:36.850Z", "modified": "2025-01-03T00:40:33.352Z", "1.0.0": "2014-12-10T10:13:36.850Z", "1.0.1": "2014-12-13T09:55:25.846Z", "1.1.0": "2015-08-16T09:08:44.729Z", "1.1.1": "2015-08-16T19:55:02.345Z", "1.2.0": "2017-10-25T05:14:12.819Z", "1.3.0": "2019-12-15T04:57:29.158Z", "1.3.1": "2021-01-15T16:19:14.219Z", "1.3.2": "2022-01-13T20:41:55.475Z", "1.4.0": "2022-01-14T04:54:21.063Z", "1.4.1": "2022-01-14T07:27:03.135Z", "1.4.2": "2022-04-13T04:55:57.705Z", "1.4.3": "2022-04-15T03:33:25.998Z", "1.5.0": "2023-04-19T06:57:53.284Z", "1.5.1": "2023-09-13T05:29:08.020Z", "1.5.2": "2024-02-11T17:51:17.530Z", "1.5.3": "2024-10-03T03:59:43.895Z", "1.5.4": "2025-01-03T00:40:33.153Z"}, "bugs": {"url": "https://github.com/es-shims/RegExp.prototype.flags/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/es-shims/RegExp.prototype.flags#readme", "keywords": ["RegExp.prototype.flags", "regex", "regular expression", "ES6", "shim", "flag", "flags", "regexp", "RegExp#flags", "polyfill", "es-shim API"], "repository": {"type": "git", "url": "git://github.com/es-shims/RegExp.prototype.flags.git"}, "description": "ES6 spec-compliant RegExp.prototype.flags shim.", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "readme": "RegExp.prototype.flags <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![Build Status][travis-svg]][travis-url]\n[![dependency status][deps-svg]][deps-url]\n[![dev dependency status][dev-deps-svg]][dev-deps-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\n[![browser support][testling-svg]][testling-url]\n\nAn ES6 spec-compliant `RegExp.prototype.flags` shim. Invoke its \"shim\" method to shim RegExp.prototype.flags if it is unavailable.\n*Note*: `RegExp#flags` requires a true ES5 environment - specifically, one with ES5 getters.\n\nThis package implements the [es-shim API](https://github.com/es-shims/api) interface. It works in an ES5-supported environment and complies with the [spec](http://www.ecma-international.org/ecma-262/6.0/#sec-get-regexp.prototype.flags).\n\nMost common usage:\n```js\nvar flags = require('regexp.prototype.flags');\nvar assert = require('assert');\n\nassert(flags(/a/) === '');\nassert(flags(new RegExp('a')) === '');\nassert(flags(/a/mig) === 'gim');\nassert(flags(new RegExp('a', 'mig')) === 'gim');\n\nif (!RegExp.prototype.flags) {\n\tflags.shim();\n}\n\nassert(flags(/a/) === /a/.flags);\nassert(flags(new RegExp('a')) === new RegExp('a').flags);\nassert(flags(/a/mig) === /a/mig.flags);\nassert(flags(new RegExp('a', 'mig')) === new RegExp('a', 'mig').flags);\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n[package-url]: https://npmjs.com/package/regexp.prototype.flags\n[npm-version-svg]: http://versionbadg.es/es-shims/RegExp.prototype.flags.svg\n[travis-svg]: https://travis-ci.org/es-shims/RegExp.prototype.flags.svg\n[travis-url]: https://travis-ci.org/es-shims/RegExp.prototype.flags\n[deps-svg]: https://david-dm.org/es-shims/RegExp.prototype.flags.svg\n[deps-url]: https://david-dm.org/es-shims/RegExp.prototype.flags\n[dev-deps-svg]: https://david-dm.org/es-shims/RegExp.prototype.flags/dev-status.svg\n[dev-deps-url]: https://david-dm.org/es-shims/RegExp.prototype.flags#info=devDependencies\n[testling-svg]: https://ci.testling.com/es-shims/RegExp.prototype.flags.png\n[testling-url]: https://ci.testling.com/es-shims/RegExp.prototype.flags\n[npm-badge-png]: https://nodei.co/npm/regexp.prototype.flags.png?downloads=true&stars=true\n[license-image]: http://img.shields.io/npm/l/regexp.prototype.flags.svg\n[license-url]: LICENSE\n[downloads-image]: http://img.shields.io/npm/dm/regexp.prototype.flags.svg\n[downloads-url]: http://npm-stat.com/charts.html?package=regexp.prototype.flags\n", "readmeFilename": "README.md"}