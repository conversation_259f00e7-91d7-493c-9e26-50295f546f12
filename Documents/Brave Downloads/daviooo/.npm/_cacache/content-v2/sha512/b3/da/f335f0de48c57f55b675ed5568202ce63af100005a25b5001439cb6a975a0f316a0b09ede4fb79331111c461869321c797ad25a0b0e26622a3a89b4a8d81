{"_id": "decompress-response", "_rev": "17-bd0d43fe9661b1307b3a0bef13038ae6", "name": "decompress-response", "description": "Decompress a HTTP response if needed", "dist-tags": {"latest": "9.0.0"}, "versions": {"3.1.0": {"name": "decompress-response", "version": "3.1.0", "description": "Decompress a HTTP response if needed", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/decompress-response.git"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/floatdrop"}], "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["decompress", "response", "http", "https", "zlib", "gzip", "zip", "deflate", "unzip", "ungzip", "incoming", "message", "stream", "compressed"], "devDependencies": {"ava": "*", "get-stream": "^3.0.0", "pify": "^2.3.0", "rfpify": "^1.0.0", "xo": "*"}, "gitHead": "5d3ef9a5bc384bb0362e860dc553e435a93e5cda", "bugs": {"url": "https://github.com/sindresorhus/decompress-response/issues"}, "homepage": "https://github.com/sindresorhus/decompress-response#readme", "_id": "decompress-response@3.1.0", "_shasum": "10402ecc6b3b3ebd5888bc07a66227fb662df78d", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "10402ecc6b3b3ebd5888bc07a66227fb662df78d", "tarball": "https://registry.npmjs.org/decompress-response/-/decompress-response-3.1.0.tgz", "integrity": "sha512-YgGQHDo70exone81OPvqyxonYUQ4D84BmlsQ1NzItjHB2J4Vuc5rB5qO+WO7sp6F01Jqlnt3MGfDEh41L46gUg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC2yUTtCvOjnn8lCiDgexJo1KVGyQGsrIaIycNWy/AAvgIgBS0SjNkbZuv3TCFij5KndkYafT20jR+S2+t7t9yezm0="}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/decompress-response-3.1.0.tgz_1494065631111_0.5124727436341345"}, "directories": {}}, "3.2.0": {"name": "decompress-response", "version": "3.2.0", "description": "Decompress a HTTP response if needed", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/decompress-response.git"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["decompress", "response", "http", "https", "zlib", "gzip", "zip", "deflate", "unzip", "ungzip", "incoming", "message", "stream", "compressed"], "devDependencies": {"ava": "*", "get-stream": "^3.0.0", "pify": "^2.3.0", "rfpify": "^1.0.0", "xo": "*"}, "gitHead": "c5ca457170c190ca2baefa591aee3e38a5a8a0f8", "bugs": {"url": "https://github.com/sindresorhus/decompress-response/issues"}, "homepage": "https://github.com/sindresorhus/decompress-response#readme", "_id": "decompress-response@3.2.0", "_shasum": "d443b8c65cd7434ac7ae15579ccbdd402a61c252", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "d443b8c65cd7434ac7ae15579ccbdd402a61c252", "tarball": "https://registry.npmjs.org/decompress-response/-/decompress-response-3.2.0.tgz", "integrity": "sha512-KOiRbNIqjhjCAx5qS7apqpuOazTCTPqU4XP8sE5g3g192OP69pIDZovaZdVsCQwLEwhDe5Y5ubRAtx+cASUkng==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFLUiQjeewgB3SAp7qmNZ71AgLa5YVob8pSWxrwgXXo0AiAUt7GoT+hJaokjxPKrlfDFoqXgpt8vHsH4OpZu51Ubtw=="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/decompress-response-3.2.0.tgz_1494151814028_0.4629032590892166"}, "directories": {}}, "3.3.0": {"name": "decompress-response", "version": "3.3.0", "description": "Decompress a HTTP response if needed", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/decompress-response.git"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["decompress", "response", "http", "https", "zlib", "gzip", "zip", "deflate", "unzip", "ungzip", "incoming", "message", "stream", "compressed"], "dependencies": {"mimic-response": "^1.0.0"}, "devDependencies": {"ava": "*", "get-stream": "^3.0.0", "pify": "^3.0.0", "xo": "*"}, "gitHead": "dcd62471a9758f970cdcc9a33c104e8d37f366ee", "bugs": {"url": "https://github.com/sindresorhus/decompress-response/issues"}, "homepage": "https://github.com/sindresorhus/decompress-response#readme", "_id": "decompress-response@3.3.0", "_shasum": "80a4dd323748384bfa248083622aedec982adff3", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.8.3", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "80a4dd323748384bfa248083622aedec982adff3", "tarball": "https://registry.npmjs.org/decompress-response/-/decompress-response-3.3.0.tgz", "integrity": "sha512-BzRPQuY1ip+qDonAOz42gRm/pg9F768C+npV/4JOsxRC2sq+Rlk+Q4ZCAsOhnIaMrgarILY+RMUIvMmmX1qAEA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDXBL9Ch+P9wpgzGaeBigDKt41ZL+k7lIBR/WcueZ/0xQIgHaDcOmT+oZJK5y0BqzSseDLDslDQWHyDxKgvy+51kBU="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/decompress-response-3.3.0.tgz_1496432627972_0.16955837607383728"}, "directories": {}}, "4.0.0": {"name": "decompress-response", "version": "4.0.0", "description": "Decompress a HTTP response if needed", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/decompress-response.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava"}, "keywords": ["decompress", "response", "http", "https", "zlib", "gzip", "zip", "deflate", "unzip", "ungzip", "incoming", "message", "stream", "compressed", "brotli"], "dependencies": {"mimic-response": "^2.0.0"}, "devDependencies": {"ava": "^1.1.0", "get-stream": "^4.1.0", "pify": "^4.0.1", "xo": "^0.24.0"}, "gitHead": "ae6ddbd9aa7bd29efc5bdaf86213b14df1a15810", "bugs": {"url": "https://github.com/sindresorhus/decompress-response/issues"}, "homepage": "https://github.com/sindresorhus/decompress-response#readme", "_id": "decompress-response@4.0.0", "_nodeVersion": "8.15.0", "_npmVersion": "6.6.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-4d74bEpqTQaEgl/nsDmbS3ocXOsRFiGD/tYUOAHl2BSualStrIMU/cmd4I8uFsY0LXDIuGXOLBBB0KRkqeG5hg==", "shasum": "f84a8b5dc0eb859c4d77a139f5787b7cff170e5e", "tarball": "https://registry.npmjs.org/decompress-response/-/decompress-response-4.0.0.tgz", "fileCount": 4, "unpackedSize": 3567, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcRFK3CRA9TVsSAnZWagAA1p8P/0luESo2pty20vQMjWqy\n4Z127VmiKhG7cbnXRZ03222z3IpoFaXdwO+jDHtWNlFLkQfE6htUtM94kV8K\nR1uUMlh7Qjek9FW6Vy5LdJNxKpKky6UgjHUOIGAtRSZueDHeA50rKyI7M0zs\n0pbJvircmTM7gRlvhTRw+UfCjHo9wF5Xzb2h3CxMb+oR0ufP6fynMQqCr/ve\naBF9CrLiyabSSWeIBKoV7FgRmLthIXjYg/lSnRzhRA3zQ4rovalVl/5LuFws\nvDNGtV25GRUcikFDnddmlHqLKdxf0Xwvyqp0K8mFO36SJcewcO+1JmYI4Bhg\nWqaheXMFK1Z1pX+chstP/wPWVzqr+AleJFnITJAMSPDyulxyIhlC3c4kb4q8\nSwYexPOsmLUqwOavm/gnfKHnYtUuNa9a2wuMK0GUsqBF7Lh5B9QVycPr/l88\nzDjia6rNWexR1tmXX/DaqwaFufCJBTpdJmtG9bWwOrPF+hXgtEyDFYMubTcn\nPzUIT4dOew6eu0FVPT764cG9q0yfylSKwf2I5sv10nzbeaH9gA1MuroXqGTl\nDDNxiy6MkwLM7KgQ+kNdp3wYmw2pjYLKgNMZIFtLWq+m12u4TRa54/NifCFQ\nryUmqOipYvp47eEm86kJzTRwz+aW0YzfRAWnE+9nnKmJlne+TDMEQf1PJm86\nkIBp\r\n=F5C9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDYfG/tqVlH3DEPD4wwwwmGt8WvB2woMuLSA9HY6BwQGwIgcON6/RfTTWeFrqfOmyynzItkBG6HD+dKi9yq5vQk+UI="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/decompress-response_4.0.0_1547981495233_0.9058451655304223"}, "_hasShrinkwrap": false}, "4.1.0": {"name": "decompress-response", "version": "4.1.0", "description": "Decompress a HTTP response if needed", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/decompress-response.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd-check"}, "keywords": ["decompress", "response", "http", "https", "zlib", "gzip", "zip", "deflate", "unzip", "ungzip", "incoming", "message", "stream", "compressed", "brotli"], "dependencies": {"mimic-response": "^2.0.0"}, "devDependencies": {"@types/node": "^11.10.5", "ava": "^1.3.1", "get-stream": "^5.0.0", "pify": "^4.0.1", "tsd-check": "^0.3.0", "xo": "^0.24.0"}, "gitHead": "a12b4c81e75a674ca368658276ee58a183b069dc", "bugs": {"url": "https://github.com/sindresorhus/decompress-response/issues"}, "homepage": "https://github.com/sindresorhus/decompress-response#readme", "_id": "decompress-response@4.1.0", "_nodeVersion": "8.15.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-6HDRhiJlClQyXGK45905TplMr6ZII1gHZuztMyYBV000nPhMEmbmY8imLUBnr80ei6uSuoX069brW4LTh+HuoA==", "shasum": "0d912451665523e5b34f04baceeac1cbad078b2f", "tarball": "https://registry.npmjs.org/decompress-response/-/decompress-response-4.1.0.tgz", "fileCount": 5, "unpackedSize": 4348, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJchgZYCRA9TVsSAnZWagAAQC0P/jd/NIXZpXqPlKHcm8zv\n42gGuItmPBZMTsMjuqgn46kLlBHB8KFpnc8Ej1WRSc9E+tzsFhH2nV3kLyh+\noUh2iRfKbR0tdpyxSPwwlE1gkySJUuolX71qeJWJxB0fd/bGWJDGfauSNnWk\nWd/MBPjypm2Ke02CrowGoapOxnsoX0iloRUT1q0WjkotnmlxpCYBRoJLEXeI\nAZ3QyJZhW+K/SyvE9ieEGVZDHZeyyT3TgCY/HucD4/htCrx9GyT6reeaqaT0\nUFp7LSozxs4NLTVNjth0rK6KxmuY8hHJIu+eVbIqxQGzTIEeIDDAHBlOCLAA\n8WoVTQaWGXkzfRc17CPzUusEVaFG2G8iCI1Zut7XKaGeu9+EsU2HdmenSosZ\nGtOM8VCFCiuYW9IiS4XTXeNod41yGOIBQPCLEvgzFHP1oL8gcCjjUJM4FM/L\nq+q+uLpNEf2qisqH6BH1qpZOH0tXlpZVIUnhq+dtR98KZ0/6tzF6g+crKci8\nkHNRXrXlcqA8F+aD6ks9jW3x025aP1sJDYOjN9ZeIKNCGZxeri7woRtubhNT\nwX1Fky/YMdMWR9x9SCh/f3aAEW6O6vOWCXEBwh+xuz++mncwh1BO9z65re9E\nngTzSW0Uv9w6v930df7WwQw72j8uBscZpD5Ctg/PXAlXmsPpkGrlAFx6RLBv\nTHQh\r\n=z/mD\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBgC5qDWGKFeTiX3Yehlud/CbyP7vcrnj+g8k34Om/vEAiBWt2mDYPP69FYzPPb+bpBpi8rzP0WkgoiGmdG5bg8cUg=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/decompress-response_4.1.0_1552287319747_0.36787183703384096"}, "_hasShrinkwrap": false}, "4.2.0": {"name": "decompress-response", "version": "4.2.0", "description": "Decompress a HTTP response if needed", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/decompress-response.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["decompress", "response", "http", "https", "zlib", "gzip", "zip", "deflate", "unzip", "ungzip", "incoming", "message", "stream", "compressed", "brotli"], "dependencies": {"mimic-response": "^2.0.0"}, "devDependencies": {"@types/node": "^11.12.2", "ava": "^1.4.1", "get-stream": "^5.0.0", "pify": "^4.0.1", "tsd": "^0.7.1", "xo": "^0.24.0"}, "gitHead": "2f7a9953359234bbb50075cf89c1b1adc73fa6f4", "bugs": {"url": "https://github.com/sindresorhus/decompress-response/issues"}, "homepage": "https://github.com/sindresorhus/decompress-response#readme", "_id": "decompress-response@4.2.0", "_nodeVersion": "8.15.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-MHebOkORCgLW1ramLri5vzfR4r7HgXXrVkVr/eaPVRCtYWFUp9hNAuqsBxhpABbpqd7zY2IrjxXfTuaVrW0Z2A==", "shasum": "805ca9d1d3cdf17a03951475ad6cdc93115cec3f", "tarball": "https://registry.npmjs.org/decompress-response/-/decompress-response-4.2.0.tgz", "fileCount": 5, "unpackedSize": 4854, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcoKjXCRA9TVsSAnZWagAAuGYP/2fVuN06C95AAuxKaaet\ntrVzYiFJjErVrM6X9sJFiT1UVL5lzQFsiaGKTlaNciqJpS9WFiscCK9QUnAp\nzyt5KCsdJQdw2GrWPok7Bfbsj/n9CEbTJ1Eub8sYBAtpkm46lqxnNDNIhfgm\nlxo28JoPqvYyGugJpBy/l/dlyz1+vjEjEK6qIDiInwvSfPmzkOAvXYHpKNU6\nP6MB/WYXAF0Oq17hrDCi+X0uttl/SsRD5cPLbCuD0WHs5Y4vfbTttlBqDLnD\nQ7CUqNPDWJCirLE9q73LPlwikzCHaI9pIqg3d1WfH1dUFwyAPaXFqD+2KknQ\nVXpl0Vw1z0X00gGgN3YaCqHfBmOWL4waAD7CC+bTZeYfmvYn7tbB7FKXG9gP\nD7vFKLAyljitqlhrcY+FC/0asp1tBpVuHQxsWOI3Qlp58mWp6NzixakFsRhy\n/jLKV6qkcErDWcfMhmzPB91yXGrmVbSmZLebY0bZLT/c6VeABQSagE79o/u2\n0jw8B+5KOFtJk0WmOLicQIgbQwiRoocJKEca2M9ypdXlpksUgvTwW486dPyj\nDQbtDGYcr5iUalhO2zqc7CKbBT35VT0sxw+6jDkr6EGgiefWUkEI60Xru+0Q\nXgWzwMz/f50lUXQYyQy/KlTjNQ5tgujtRHHeu6airzumfEmYaYLA111xFSIm\noKJ1\r\n=8Tx7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC6iHDZgi651iAPVbVazMu54Oq6H0mXRmbCsJjg6sCobAiA0a7dbGVYNp6ncLcjetzw1MOIheac5sJbbI7SXlZIZyA=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/decompress-response_4.2.0_1554032854345_0.012761173080178434"}, "_hasShrinkwrap": false}, "4.2.1": {"name": "decompress-response", "version": "4.2.1", "description": "Decompress a HTTP response if needed", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/decompress-response.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["decompress", "response", "http", "https", "zlib", "gzip", "zip", "deflate", "unzip", "ungzip", "incoming", "message", "stream", "compressed", "brotli"], "dependencies": {"mimic-response": "^2.0.0"}, "devDependencies": {"@types/node": "^12.7.1", "ava": "^2.2.0", "get-stream": "^5.0.0", "pify": "^4.0.1", "tsd": "^0.7.1", "xo": "^0.24.0"}, "gitHead": "c6cfc99d36fbb52d61bc252f3e766af51d21ed76", "bugs": {"url": "https://github.com/sindresorhus/decompress-response/issues"}, "homepage": "https://github.com/sindresorhus/decompress-response#readme", "_id": "decompress-response@4.2.1", "_nodeVersion": "10.16.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-jOSne2qbyE+/r8G1VU+G/82LBs2Fs4LAsTiLSHOCOMZQl2OKZ6i8i4IyHemTe+/yIXOtTcRQMzPcgyhoFlqPkw==", "shasum": "414023cc7a302da25ce2ec82d0d5238ccafd8986", "tarball": "https://registry.npmjs.org/decompress-response/-/decompress-response-4.2.1.tgz", "fileCount": 5, "unpackedSize": 5259, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdUd2kCRA9TVsSAnZWagAA4gQP/AmmlwU2x5j/7jcxblOj\nZHhBfW745jeURUiiAedjWB/3XzxlDbMBZPefL0fEeLz6SeDyQuPaYTvHM9D+\nimf40ckSM2mzJDxJrzIy/BTPr53lxtiz+g9xPzQxJWVQKVSzVp1wsKpxG06J\nhGRLCKvtkecijSHi0FJ99B3kEyDJ3twmP5c8c6pWu05wc4Oftf8JFq8atSpM\nzEB96pjrJnfSusDzviH/6RHsZSGKUJoohL8KN7njW3qg1wiKRrwg6nHzfdPN\nYrwpGQIvN/2Iou3gMjFiR4ZCwstYlczH+K0gLNnCZhTSsCQOzyf6WpGgkJ6+\n0cZMPgxgmeHjKYPWNB/humCJe+akhII1fx2smRNrH2dgRUobqnnhI+UxQ4R2\nakMNMrF+N2XZWklagOwV9Qx4skSNXB3FL4Z5UspWdp3hpJbBwBX9bLX/8NFS\nqsnKTJaDorGfHqR2rCY3g6yDbJncVht1eE8sdgCilJhCneqIBSL1lFWUjZlg\nj+IHw26yK/+0pAHKO8Xm1RpB28HxqT88LI6Cda4eZct5CFD923FKWnPn252M\ncOyldT18eXkkXiXOM12Mlf6EHesUw4l0CcAPU23frctbHCqjMwRuk0CgOqEO\n/Jp6MhGcQqzzTgngcBFl+2d1qfGaQZRJ81Q5Sz+S3YJeH40LZnZbawz20T/1\nbjkK\r\n=/wjt\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDZF15fGcBCHkQo8VGeIY9Q5Cljp0/muLmeyHhdMCarKQIgUgJqNTf4ajeBTW6VJBs7cnXSzlbjv3iM0JcgcmtXMdw="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/decompress-response_4.2.1_1565646243258_0.8844781198189944"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "decompress-response", "version": "5.0.0", "description": "Decompress a HTTP response if needed", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/decompress-response.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["decompress", "response", "http", "https", "zlib", "gzip", "zip", "deflate", "unzip", "ungzip", "incoming", "message", "stream", "compressed", "brotli"], "dependencies": {"mimic-response": "^2.0.0"}, "devDependencies": {"@types/node": "^12.7.1", "ava": "^2.2.0", "get-stream": "^5.0.0", "pify": "^4.0.1", "tsd": "^0.10.0", "xo": "^0.25.3"}, "gitHead": "a31bde7a6bfe23345e41531ffa1a22cedbd4eadd", "bugs": {"url": "https://github.com/sindresorhus/decompress-response/issues"}, "homepage": "https://github.com/sindresorhus/decompress-response#readme", "_id": "decompress-response@5.0.0", "_nodeVersion": "12.13.0", "_npmVersion": "6.12.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-TLZWWybuxWgoW7Lykv+gq9xvzOsUjQ9tF09Tj6NSTYGMTCHNXzrPnD6Hi+TgZq19PyTAGH4Ll/NIM/eTGglnMw==", "shasum": "7849396e80e3d1eba8cb2f75ef4930f76461cb0f", "tarball": "https://registry.npmjs.org/decompress-response/-/decompress-response-5.0.0.tgz", "fileCount": 5, "unpackedSize": 5047, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdvDjkCRA9TVsSAnZWagAAW3AP/0pZaAOLTKBKdCUcvftc\nvTchYIYap5xb0SYUpWaJh57KZoVSekz5iPKPFb0UvFLhKLHvGBEkla+AwY1g\nxo5X01e9lT0epjPM4RbooLrHOioPRlrpdoj68VB9FCARIrM/E6C/MEb7hmdh\nAlWdtMvOTIxWaU8B7Y9phMIJflxgHNKiWPJTKGp5Ngwf5e2j3Q4ckUjKRRby\n5rlQM00qGKG+iarv62qSgLNqDT1yisZWRdWGL/Ih3hSI16CGOAgvYZawd/DT\nraGCA+PHDYfWLOCL4F7RhjS3wZ8bN+Ew9dK+GkgMDISyecfwYV4nc+4BtKzp\nsV9KX2IjGDyDcTGy8XP3xzsJyZM0vRDJQW8IDj2pXecdlfm7QPwSDoIJ4bUo\nymbXXbTLY/QcaFxbFdycBeBotCmJM6Jq8yd4p8tzSADfvQ1QElswu3Fwg4xf\nLazSCVd+MbohcDBW1QtCLQHOjWRaYXfg4EOirWsJxU/WlxHzUNaq61O2oZTG\n3TIdNQkpSKjeg/RnUOImx2yjpaxvPND4T52QU3XMuZ6lqqkKav/QAw4ua0U9\n8/pYiCYl0brXVHNjiDeSGbExUvcZ6CvmLnghFjEajPDaLa5TSVo6yqtvJFdj\nQ6moHe4LeA2p4D+kAcC8n/ZB1keDVG5kbrgPkq58SUXreRZMv92MuS0cD4ZL\n3kMm\r\n=zmSK\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD27QQeJ37cle+E16oObP4vylr10z6vqnp+Vsg0Vx24/QIgK4uMmt2T4eLrMo78JpMLyqm1qVaFZnjHlX99w2YubQU="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/decompress-response_5.0.0_1572616419647_0.7744270447294967"}, "_hasShrinkwrap": false}, "6.0.0": {"name": "decompress-response", "version": "6.0.0", "description": "Decompress a HTTP response if needed", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/decompress-response.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["decompress", "response", "http", "https", "zlib", "gzip", "zip", "deflate", "unzip", "ungzip", "incoming", "message", "stream", "compressed", "brotli"], "dependencies": {"mimic-response": "^3.1.0"}, "devDependencies": {"@types/node": "^14.0.1", "ava": "^2.2.0", "get-stream": "^5.0.0", "pify": "^5.0.0", "tsd": "^0.11.0", "xo": "^0.30.0"}, "xo": {"rules": {"@typescript-eslint/prefer-readonly-parameter-types": "off"}}, "gitHead": "279d53e59fae44eec709e1891d9cf8c2d863f6f3", "bugs": {"url": "https://github.com/sindresorhus/decompress-response/issues"}, "homepage": "https://github.com/sindresorhus/decompress-response#readme", "_id": "decompress-response@6.0.0", "_nodeVersion": "10.20.1", "_npmVersion": "6.14.4", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-aW35yZM6Bb/4oJlZncMH2LCoZtJXTRxES17vE3hoRiowU2kWHaJKFkSBDnDR+cm9J+9QhXmREyIfv0pji9ejCQ==", "shasum": "ca387612ddb7e104bd16d85aab00d5ecf09c66fc", "tarball": "https://registry.npmjs.org/decompress-response/-/decompress-response-6.0.0.tgz", "fileCount": 5, "unpackedSize": 5472, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJevgetCRA9TVsSAnZWagAAJX4P/0DzKboud5MjqnMCALrC\n2Xaqk/9+ET9Un+MOqWlA/EZn4sf8TMTHQ+81V0UgkN6ZEbpddzSH4ArTZe+j\nUyidbgtyFQ3oXRz7sQW9RzQp5uJ2iFX5U/A+621uz/MaRFWMvT+nLSeBWy/f\nR5x1csDQawVyfzA7T4pGXiZxgpcvxu259gWsOCjdxzLBlya8XpJjHL3OnXlc\nPgN3n7xlLMAsVn/IwpraQ31a69ZBQM5yXf9Mm8+01iJhDiYU7is1G254jVIi\nO+CUWSGAccmfcoMdmjwNmKLzR4n2jP+l6Zgd3TLaLCmDFUCGY/wLDQ7hMzSl\n9/D2LqPDwOOSUSbzjWbIESoSRZa86cwCex2LjBcqGouxS1YabMqQwFyoywin\nLGPYgToOGoxWL9Blm3Xwtd9XVFh2rx4ag3ahBRTRfkMLI88duwVOviBNGm5N\n3MWu/cBAmmOloAwPlv4y0NHfz9AbPcfqPKliKCggyKmozNcPOCSg/HHjGiJR\nKLq0EvzqFD4mFCTKbTN3qlajimqwB4Qukk3kdHuuJm1YGU46yBnMrulM3KB/\nSFTPs3LzWMT9B6M0PHVY9WERYop3/jXn9Nl/hSQ9P47ogQLLtqBVKvD/zc5f\n4ITdU0jTR1Qzx7wnIxY1Q+E/Zf63321uH21BIqLvqI9hssuApHiFKjGWN2Ag\nbK6q\r\n=tAjI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCl9xsOLG5LjiH27Ugy2iJ+C9F3H/9OZTz0wzOJodP0EQIgcBD7Ps/IHsL/2WV47VkM+aO6VFOSlYGSQV65Wg+4F3M="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/decompress-response_6.0.0_1589512108663_0.20171740850344988"}, "_hasShrinkwrap": false}, "7.0.0": {"name": "decompress-response", "version": "7.0.0", "description": "Decompress a HTTP response if needed", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/decompress-response.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["decompress", "response", "http", "https", "zlib", "gzip", "zip", "deflate", "unzip", "ungzip", "incoming", "message", "stream", "compressed", "brotli"], "dependencies": {"mimic-response": "^3.1.0"}, "devDependencies": {"@types/node": "^14.0.1", "ava": "^2.2.0", "get-stream": "^5.0.0", "pify": "^5.0.0", "tsd": "^0.11.0", "xo": "^0.30.0"}, "xo": {"rules": {"@typescript-eslint/prefer-readonly-parameter-types": "off"}}, "gitHead": "81d9895b918936d000e92891165d1c51c66b04e6", "bugs": {"url": "https://github.com/sindresorhus/decompress-response/issues"}, "homepage": "https://github.com/sindresorhus/decompress-response#readme", "_id": "decompress-response@7.0.0", "_nodeVersion": "10.22.1", "_npmVersion": "6.14.10", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-6IvPrADQyyPGLpMnUh6kfKiqy7SrbXbjoUuZ90WMBJKErzv2pCiwlGEXjRX9/54OnTq+XFVnkOnOMzclLI5aEA==", "shasum": "dc42107cc29a258aa8983fddc81c92351810f6fb", "tarball": "https://registry.npmjs.org/decompress-response/-/decompress-response-7.0.0.tgz", "fileCount": 5, "unpackedSize": 5683, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgJlZ0CRA9TVsSAnZWagAAag8P/3kbFMi9R0okh669lhpz\nW+Aayc3QlFRHpzUwX9NkJ9SSFQQjASJw97JDLaWxRbYzi+q3TcZUIJ7Qlq14\nT/El7SYyFaQ5SDvAYFkZjBoCBM2X/YeMPxoWCvGc0bV/YjaHvg2Rd9AGNKyi\nH5akwGxvw36Eu5f3URR86QvhhDRSRMA5Plseiyf1D4gW0k8x/3/e3SEKegw3\nEjdE2xWox5SRJTU4IYjeoZN/N5SnOJGuChHvuthasOq7Ngb+GDDqLG7mV3WN\nnYURHW6dfmCPeOlyJywJHi+lLmNCKm9Qp+kHzBM+Fy4Z8cX4oGFw2E1qBCRM\nGD2kh0oFhpn/0hPEZ9U01PJFBqm2degDeis7jStAEAGJenMUbCkig7SU+9wl\nodVxXbTJwTWaloApsxuDfeLU5z6xvcprKvFj5cHoux4Az3VnB8VxEs73OE81\nxdfBmsMizOciR0m6s4ZbjhwzCy6hTaEzjD9J1yPHNOFW8RwI7P117PX6rKp2\nRxGWeBx+/C5/JzLZWlvcos/G7wQk0niQ6G5UttQXl2zf0Zqn0u1okcbwt/y6\nVawWo7KW1ra2r5yszv7YC2R5q9ZptF1rexK1uSedkeyPcdk9KX5E2klQarMD\nZjWUR3bBOO5rhuWXN6Rxg3EgA7rSZQHoRrSTcYv3KOWOth+lJqmuB0kVBgjv\nA0eg\r\n=+VKM\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDIAkkq75zCaCjxi6T702vXV6Eui6+pWNppNmM4KtvIhwIhAPr0R0rTGqohmFEwGHLh4SgAebg59kkPqfS8g699hlUl"}]}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/decompress-response_7.0.0_1613125236324_0.8425377886190142"}, "_hasShrinkwrap": false}, "8.0.0": {"name": "decompress-response", "version": "8.0.0", "description": "Decompress a HTTP response if needed", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/decompress-response.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["decompress", "response", "http", "https", "zlib", "gzip", "zip", "deflate", "unzip", "ungzip", "incoming", "message", "stream", "compressed", "brotli"], "dependencies": {"mimic-response": "^4.0.0"}, "devDependencies": {"@types/node": "^16.11.6", "ava": "^3.15.0", "get-stream": "^6.0.1", "pify": "^5.0.0", "tsd": "^0.18.0", "typescript": "^4.4.4", "xo": "^0.45.0"}, "types": "./index.d.ts", "gitHead": "c717c55f958ab492d13338cc999eb7268d82e1fb", "bugs": {"url": "https://github.com/sindresorhus/decompress-response/issues"}, "homepage": "https://github.com/sindresorhus/decompress-response#readme", "_id": "decompress-response@8.0.0", "_nodeVersion": "17.0.1", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-osK7RWZFivlvC7K8BVM/uMyIE6K24twOtJdvXHRknFwvlidyWNUMyaKx1/i4etWDqK/UMHGB5Ywr0InttmrIiQ==", "shasum": "bdce51511730bfe9cfac8e90443b0ae791f030e3", "tarball": "https://registry.npmjs.org/decompress-response/-/decompress-response-8.0.0.tgz", "fileCount": 5, "unpackedSize": 5398, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2n97CRA9TVsSAnZWagAALB8P/i8x3snGDF10KUqqLm/A\nytnMuE/tlyLknwGtEJz7bz/iCN5rDzxD9+57XfSImTku/3Q1YIM59S8hNqIt\nXFO04fhksMrew+BdW4nzi3g74HUmPQpE31Qb9BQDwx2QX2gAhyn3YqzbWNIK\naDmVVJ5lvli4SyVrcu3lIeZXET0O8m2jGSdJHNA3D5f4DDSWWXy+zukHm4dg\n6ce82ZoZf0ufnzeyayM6MUhXyGViGYhnr4dkE17DEXkFK1Rwe2rWqHg23NiJ\nG6KK9Lvh7zl5KAOu3N7+QKzu+xoymVUGWqe1hthRTRF3XVMeqRik/m3rloPG\n5SMjH3hBRUgf3uJ9zCDyv/pWOATz8vQfBNU1DzR7SxVgyrMj/KUfBfNjVMNA\nOwnRCrbZNzRKkS+53wplvPLTy56P2y3vaoWR+fN+2k7lj2xAPFw+ssn3E7Tg\nhvHZVuIiNHJ0M3saWjK/XFx1l/vfjHSmE3Y1lX7QZcoo8XFxyvs0kFMg80Yu\nLFJfoMzF2h+bQ37pb1n62HOn47eEWBqEWWoDF4VIdgxmhEneWdbQ8nKAiq6l\nL3Pl7rAuWOoGld3fzfzxNV0PUTf1XQ6N3l3nTiBiEotresKfVN29jne1ANty\nigRqriVQtNF8isOIHtWbU0C4CCD3YdyHky/tzFvtPRsbf6ZPwvC1kl+eP58c\nqyQQ\r\n=fifV\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFITWaSWlVq1Qgu+ZC6wvP9zz0n9zKfQxEQ2L3fWFiPrAiEAt54bU0nJQ4NdY0cUcx35eazph4a93Ob+q4irfPaUEGk="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/decompress-response_8.0.0_1635332080100_0.6514187464212695"}, "_hasShrinkwrap": false}, "8.1.0": {"name": "decompress-response", "version": "8.1.0", "description": "Decompress a HTTP response if needed", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/decompress-response.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["decompress", "response", "http", "https", "zlib", "gzip", "zip", "deflate", "unzip", "ungzip", "incoming", "message", "stream", "compressed", "brotli"], "dependencies": {"mimic-response": "^4.0.0"}, "devDependencies": {"@types/node": "^16.11.6", "ava": "^3.15.0", "get-stream": "^6.0.1", "pify": "^5.0.0", "tsd": "^0.18.0", "typescript": "^4.4.4", "xo": "^0.45.0"}, "types": "./index.d.ts", "gitHead": "d935d94728eef4bdeacf41531f3a1cb923418afb", "bugs": {"url": "https://github.com/sindresorhus/decompress-response/issues"}, "homepage": "https://github.com/sindresorhus/decompress-response#readme", "_id": "decompress-response@8.1.0", "_nodeVersion": "16.16.0", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-0W/lM+moRwab90sk5EhLp4EDZrWlaxVAnyD9iGwOxfV1TkbDJ88LDKLSnT5LQyGHtqWSbNioJXt4F1uEIkrN6A==", "shasum": "1240582deb1406c17ffdec19833bcd29c96aed12", "tarball": "https://registry.npmjs.org/decompress-response/-/decompress-response-8.1.0.tgz", "fileCount": 5, "unpackedSize": 5721, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEPucyrQyF5RqxfvtzqPrJ7u8SLZ0epr9pPzoZVSEUqsAiEAzWaJiGNFsoKsGfImfTJJm+23jGKgvSRkPSi9mKq4f/s="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj95ngACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmonAQ/5AXpWoWZMmUFpuWOEAicyFHHJXl5HOCoZOHWBSbMxa0qXVLt8\r\nITL+CXVoA6Sin7fw8AcailSzN3yJBFvTbBgzqi4kiDP+P7hz6ISY5zLm5Tx/\r\nirApsq8TAUyWlACUPKmkrFpGymDnaRjzxY9L/UrzzEPsCvnpw3y1ymSfv3ho\r\ny1RSfWSegIVRDzdYo+t7hclB0ACQsiUWtbWZ4nXkaObOESwxV2AzTHpnLIqx\r\nUXqyisvj9Dh3UTMMVgy1xUwd5boKztAF1gjMB2+epLY0WbtJnX75jMg7ySV7\r\nKMWVOg7uYs6USSM4hUvYiNUJorNsG/R/l2iJxlopHePUcxy9gNs6zPL0U4oZ\r\np4r4Yndzzl5PP1eMbaD6L4jhw7q3slubsnMzOvLdYTFNhrpQwplrfjIk+9xY\r\nLtVizOENVU7euLP24RWPC2o4mtVNHyJ2SPWTiRDcb46vd6pbFByosPtRP66C\r\nJCFwnsHDyn+zaRZ+bN0aMpdbBdQsiwBNbibglms5MEA9cTGNYWISKMtWdLNK\r\noLmS6syrFIVUDt2gqaROzqAj2vhfuWIUPN3YlM1Pn4dwMSYB05lvIeFnNfOS\r\ny5oICNOf4h3hctMKYASxu17OzLGvZo/mac4AWZF4HIWzCEvYtlHa7RsaqwEw\r\nVks5rj4/GW8zi3CVrw0MlRCoiaOZSYtaSCE=\r\n=FABn\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/decompress-response_8.1.0_1677171168520_0.8370834099680775"}, "_hasShrinkwrap": false}, "9.0.0": {"name": "decompress-response", "version": "9.0.0", "description": "Decompress a HTTP response if needed", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/decompress-response.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["decompress", "response", "http", "https", "zlib", "gzip", "zip", "deflate", "unzip", "ungzip", "incoming", "message", "stream", "compressed", "brotli"], "dependencies": {"mimic-response": "^4.0.0"}, "devDependencies": {"@types/node": "^20.12.12", "ava": "^6.1.3", "get-stream": "^8.0.1", "pify": "^6.1.0", "tsd": "^0.31.0", "xo": "^0.58.0"}, "_id": "decompress-response@9.0.0", "gitHead": "d6600ec6e89db748d136ffd2f54820f734e9676f", "types": "./index.d.ts", "bugs": {"url": "https://github.com/sindresorhus/decompress-response/issues"}, "homepage": "https://github.com/sindresorhus/decompress-response#readme", "_nodeVersion": "18.20.2", "_npmVersion": "10.6.0", "dist": {"integrity": "sha512-6I7smNZulDaes/5onVuVXJ5WleFu62mcjW5A8YJkPxe3FyJ17NpADIXRB41hYXlU4VDMsqhke2K1SnXIxc8ozw==", "shasum": "867d498d03a4629c0c781ceb8f8e368c9322f8e4", "tarball": "https://registry.npmjs.org/decompress-response/-/decompress-response-9.0.0.tgz", "fileCount": 5, "unpackedSize": 5477, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAdGgYwAHHpUCq9lgS8voi72u5vlUbIR066WQetg6ZEsAiEA7YlHfMzOBj8sW2HMPdy//ih0JcTEFQFbPpZ4ETZJvBk="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/decompress-response_9.0.0_1716932588996_0.22016702808713373"}, "_hasShrinkwrap": false}}, "readme": "# decompress-response\n\n> Decompress a HTTP response if needed\n\nDecompresses the [response](https://nodejs.org/api/http.html#http_class_http_incomingmessage) from [`http.request`](https://nodejs.org/api/http.html#http_http_request_options_callback) if it's gzipped, deflated or compressed with Brotli, otherwise just passes it through.\n\nUsed by [`got`](https://github.com/sindresorhus/got).\n\n## Install\n\n```sh\nnpm install decompress-response\n```\n\n## Usage\n\n```js\nimport http from 'node:http';\nimport decompressResponse from 'decompress-response';\n\nhttp.get('https://sindresorhus.com', response => {\n\tresponse = decompressResponse(response);\n});\n```\n\n## API\n\n### decompressResponse(response)\n\nReturns the decompressed HTTP response stream.\n\n#### response\n\nType: [`http.IncomingMessage`](https://nodejs.org/api/http.html#http_class_http_incomingmessage)\n\nThe HTTP incoming stream with compressed data.\n", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2024-05-28T21:43:09.415Z", "created": "2017-05-06T10:13:52.908Z", "3.1.0": "2017-05-06T10:13:52.908Z", "3.2.0": "2017-05-07T10:10:14.249Z", "3.3.0": "2017-06-02T19:43:48.054Z", "4.0.0": "2019-01-20T10:51:35.377Z", "4.1.0": "2019-03-11T06:55:19.918Z", "4.2.0": "2019-03-31T11:47:34.531Z", "4.2.1": "2019-08-12T21:44:03.388Z", "5.0.0": "2019-11-01T13:53:39.809Z", "6.0.0": "2020-05-15T03:08:28.784Z", "7.0.0": "2021-02-12T10:20:36.454Z", "8.0.0": "2021-10-27T10:54:40.261Z", "8.1.0": "2023-02-23T16:52:48.681Z", "9.0.0": "2024-05-28T21:43:09.245Z"}, "homepage": "https://github.com/sindresorhus/decompress-response#readme", "keywords": ["decompress", "response", "http", "https", "zlib", "gzip", "zip", "deflate", "unzip", "ungzip", "incoming", "message", "stream", "compressed", "brotli"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/decompress-response.git"}, "bugs": {"url": "https://github.com/sindresorhus/decompress-response/issues"}, "license": "MIT", "readmeFilename": "readme.md", "users": {"wimas": true}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}}