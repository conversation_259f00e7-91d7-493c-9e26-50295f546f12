{"_id": "optimist", "_rev": "333-4aa4fa6ee25d304dc0ec3f955a517609", "name": "optimist", "dist-tags": {"latest": "0.6.1"}, "versions": {"0.0.1": {"name": "optimist", "version": "0.0.1", "descrption": "Light-weight option parsing", "modules": {"index": "./optimist.js"}, "repository": {"type": "git", "url": "http://github.com/substack/node-optimist.git"}, "engine": ["node >=0.1.100"], "_id": "optimist@0.0.1", "engines": {"node": "*"}, "_nodeSupported": true, "_npmVersion": "0.2.7-2", "_nodeVersion": "v0.3.1-pre", "dist": {"tarball": "https://registry.npmjs.org/optimist/-/optimist-0.0.1.tgz", "shasum": "be66175d8781290f1672ae96858814c88274c41c", "integrity": "sha512-EJ8b+3INOiRHJsGZBzPrXhWd978UDLcMelMNovXNxK3pvJg1qDIRAiylAREN+RBpkKf3ga9FzOp5mQcAFgzmhQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCB7i4xm0AYozRw53UvtHOdRG/UBllIkg/8MxTfekTNoQIhAP2SBo6FxfVsAua928rFI1KCLJnerA9q9E/X3PQJ/7fV"}]}, "directories": {}}, "0.0.2": {"name": "optimist", "version": "0.0.2", "description": "Light-weight option parsing", "modules": {"index": "./optimist.js"}, "repository": {"type": "git", "url": "http://github.com/substack/node-optimist.git"}, "engine": ["node >=0.1.100"], "_id": "optimist@0.0.2", "engines": {"node": "*"}, "_nodeSupported": true, "_npmVersion": "0.2.7-2", "_nodeVersion": "v0.3.1-pre", "dist": {"tarball": "https://registry.npmjs.org/optimist/-/optimist-0.0.2.tgz", "shasum": "594ac6711315fa0d06887e3833567ff3fc7de4e1", "integrity": "sha512-YPjsaEGXzOspiG8gkrT1lLS+42iAm/lLW8JS+lV6JNYoW4KG4ob3p9tXOYIOwsw90Bdpl6SY/06COy3NZCK3xg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCwC4/hY4BmeVxAPZ6i2GFJ6TOprtejTkGR681lnZh/0AIgTQV4b1shDTehC52GF39ycUQp/QHKXn191R2Tz4P1OSg="}]}, "directories": {}}, "0.0.4": {"name": "optimist", "version": "0.0.4", "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "modules": {"index": "./lib/optimist.js"}, "repository": {"type": "git", "url": "http://github.com/substack/node-optimist.git"}, "license": "MIT/X11", "engine": ["node >=0.1.100"], "_id": "optimist@0.0.4", "engines": {"node": "*"}, "_nodeSupported": true, "_npmVersion": "0.2.7-2", "_nodeVersion": "v0.3.1-pre", "dist": {"tarball": "https://registry.npmjs.org/optimist/-/optimist-0.0.4.tgz", "shasum": "6d5a2516e5b9a4808a8477cd62086ef829f07f4e", "integrity": "sha512-NxEtHn61vJffoM0c/vxb0HHr5ULcZMHUpVU0TWekV9rBxp5dC28hxT5w/bKKW4mxUZ5LHat+F5rT39PkY829MA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHykMaHxlLG+1eTeFMgeeInoEyrC8D0gdHR1snTa8i5tAiEAv+8zoccJf2htAf0zcZMXl7h8aniW/2xhJdyXbP0qYZs="}]}, "directories": {}}, "0.0.5": {"name": "optimist", "version": "0.0.5", "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "modules": {"index": "./lib/optimist.js"}, "repository": {"type": "git", "url": "http://github.com/substack/node-optimist.git"}, "license": "MIT/X11", "engine": ["node >=0.1.100"], "_id": "optimist@0.0.5", "engines": {"node": "*"}, "_nodeSupported": true, "_npmVersion": "0.2.7-2", "_nodeVersion": "v0.3.1-pre", "dist": {"tarball": "https://registry.npmjs.org/optimist/-/optimist-0.0.5.tgz", "shasum": "79f3bd7caf7c9002ace2a0600df80761fa459ea7", "integrity": "sha512-eH1FXMuRhFLTgYMJ1n/ck/bQugyChWbKNDfvStMuMpVTsk1Rgtj7qLBcOWllhGsHf4+YxYET7e/V33m4M9BMIg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA9JJlm/09BL79RVBqCvx748h6AlqDCZ3Tey7wGAddtZAiEA7NfC2AsWhykWvJCToewAfjuDyQrFL45O24xcEaziHOQ="}]}, "directories": {}}, "0.0.6": {"name": "optimist", "version": "0.0.6", "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "modules": {"index": "./lib/optimist.js"}, "repository": {"type": "git", "url": "http://github.com/substack/node-optimist.git"}, "license": "MIT/X11", "engine": ["node >=0.1.100"], "_id": "optimist@0.0.6", "engines": {"node": "*"}, "_nodeSupported": true, "_npmVersion": "0.2.7-2", "_nodeVersion": "v0.3.1-pre", "dist": {"tarball": "https://registry.npmjs.org/optimist/-/optimist-0.0.6.tgz", "shasum": "1e00197a8f5c010ed02c9bed629af28422c51bdf", "integrity": "sha512-5CLPzz6kPrV/FffpxrTwJNYKik3dj0iexm9IWynj2G0uG4rEXq220V21fUCMDkWLJlVHKWIiovHaCvrBrEwwEQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDfJ9ON7T+sNmmmwdmtmmI6kL2IpgXm4VMZRcQpKDrAdQIgEd1kd9JfRtopYyOHFWAhuqFE/VpjojrlmZOvAQSzziY="}]}, "directories": {}}, "0.0.7": {"name": "optimist", "version": "0.0.7", "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "modules": {"index": "./lib/optimist.js"}, "repository": {"type": "git", "url": "http://github.com/substack/node-optimist.git"}, "license": "MIT/X11", "engine": ["node >=0.1.100"], "_id": "optimist@0.0.7", "engines": {"node": "*"}, "_nodeSupported": true, "_npmVersion": "0.2.10", "_nodeVersion": "v0.2.5", "dist": {"shasum": "5ffc1dce7ddfdfe57a61fabb2644d7bda57722b2", "tarball": "https://registry.npmjs.org/optimist/-/optimist-0.0.7.tgz", "integrity": "sha512-v4bxP7RwxOYVjvSlwwKiD+NwHoZm1KNc5BOoO6tmAEKdnj9J6iusHj/nVya0wE2pePvsWUOo/ED+u8RzJ5+jnQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCshC7S/ld1E2MTAubH0gRoznStEG4e0fnvtAb1ql2GgAIhAO668rkcOXrE9gJ/TRq9N7ZMWDfaSxm5mPlxXMd4JRwT"}]}, "directories": {}}, "0.1.0": {"name": "optimist", "version": "0.1.0", "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "modules": {"index": "./lib/optimist.js"}, "repository": {"type": "git", "url": "http://github.com/substack/node-optimist.git"}, "license": "MIT/X11", "engine": ["node >=0.1.100"], "_id": "optimist@0.1.0", "engines": {"node": "*"}, "_nodeSupported": true, "_npmVersion": "0.2.10", "_nodeVersion": "v0.2.5", "dist": {"shasum": "b523820a36a51c35bf6098d2dc4b5aa001e0f541", "tarball": "https://registry.npmjs.org/optimist/-/optimist-0.1.0.tgz", "integrity": "sha512-TjidEZ/VhQuNMK7yLW0b/W3yzGQh5eOtICHDG5Jkd30QFxMNhCr6uU5oB4t1z87VXxic/7IzGW83O7ELTJpsfw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG38WMw9v2QFCV5SDq4YgnEANaIG/6fiDkGr/2kUis7pAiEAo5qKWwYExHtRculL53tuSCJXpYsH/sIvYzEXZtfXaN0="}]}, "directories": {}}, "0.1.1": {"name": "optimist", "version": "0.1.1", "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "modules": {"index": "./lib/optimist.js"}, "repository": {"type": "git", "url": "http://github.com/substack/node-optimist.git"}, "license": "MIT/X11", "engine": ["node >=0.1.100"], "_id": "optimist@0.1.1", "engines": {"node": "*"}, "_nodeSupported": true, "_npmVersion": "0.2.10", "_nodeVersion": "v0.2.5", "dist": {"shasum": "ed43041fe2196e9f36b9c0f75e301526ab751baa", "tarball": "https://registry.npmjs.org/optimist/-/optimist-0.1.1.tgz", "integrity": "sha512-MNqGYjGixHls02Ouc1MrJxYj6qp7wph/Nq+/UzpItqzr3Uevuy0XsDDZR7Ib1SDmGrcUHn7wHcuZXzcsSo6RCg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDt9+9uhjzFbpPKFimMdcXpN/E0bj7jI5FBSwK19wQ8pAIgdRIIKvZldrXjL01cI1l4pHnOse0y3+CgFW0PFyHtnUc="}]}, "directories": {}}, "0.1.2": {"name": "optimist", "version": "0.1.2", "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "modules": {"index": "./lib/optimist.js"}, "repository": {"type": "git", "url": "http://github.com/substack/node-optimist.git"}, "keywords": ["argument", "option", "parser", "parsing"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT/X11", "engine": ["node >=0.1.100"], "_id": "optimist@0.1.2", "engines": {"node": "*"}, "_nodeSupported": true, "_npmVersion": "0.2.12-1", "_nodeVersion": "v0.2.5", "dist": {"shasum": "489780fb5350e8429e99a9e6e1305124eb3bbc8e", "tarball": "https://registry.npmjs.org/optimist/-/optimist-0.1.2.tgz", "integrity": "sha512-VjFXQtUu8BSV/Dikp5BPMdot7bRZ7eZyCozU1TrPWzq4j15yUJru9TsHLwnEZ3V0Tugm5vbtxtHAwO6Nc4H09w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAkTq5sFAdEEvtJjnDe2PXYt/0pgmDlOLoKPI8hrPQeLAiAIErIVXMjt4YxAyVr3TGDJKnbxbOjm7KW8r8G7U4LNGQ=="}]}, "directories": {}}, "0.1.3": {"name": "optimist", "version": "0.1.3", "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "modules": {"index": "./lib/optimist.js"}, "repository": {"type": "git", "url": "http://github.com/substack/node-optimist.git"}, "keywords": ["argument", "option", "parser", "parsing"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT/X11", "engine": ["node >=0.1.100"], "_id": "optimist@0.1.3", "engines": {"node": "*"}, "_nodeSupported": true, "_npmVersion": "0.2.12-1", "_nodeVersion": "v0.2.5", "dist": {"shasum": "90389a7e6807b5798b41c4b4112403a9691b98ff", "tarball": "https://registry.npmjs.org/optimist/-/optimist-0.1.3.tgz", "integrity": "sha512-mJ2CDtgbFwRC8bckN5kEWn42tvHp2T+cTxMRAqsPBB/TRYnXJvjJiaagZ9Nc5+qynK2kNxgYCzf1OYjIapj1RQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDecL9W/HNSWiPD9ACZxOrWG9qQZXL/e2FsRNN89p/xHgIgcSGTglGxsDV5ZJq/1gTuIHCksAbOqH4B4sBPe75gWlU="}]}, "directories": {}}, "0.1.4": {"name": "optimist", "version": "0.1.4", "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "main": "./index", "repository": {"type": "git", "url": "http://github.com/substack/node-optimist.git"}, "keywords": ["argument", "option", "parser", "parsing"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT/X11", "engine": ["node >=0.1.100"], "_id": "optimist@0.1.4", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "0.2.16", "_nodeVersion": "v0.3.8-pre", "directories": {}, "files": [""], "_defaultsLoaded": true, "dist": {"shasum": "92496e1e378b46a24b6c027a612637cfc5fb543e", "tarball": "https://registry.npmjs.org/optimist/-/optimist-0.1.4.tgz", "integrity": "sha512-wtddXzz98CE6YUiEVjLD2wEDe8P0DUIboZh97ZdEUcmHFAVFPWkm80wXmXBBxb6jDpEQq1fOI4ZqmcKAAoKMYA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAehlrDR/53BHmipjMTrD5Dwg/wgE+wjVZwVne2SKIjZAiEA1xMpQRUFn+SQp135oq98N+WFsMHOclGKw6KOEjlBZLM="}]}}, "0.1.5": {"name": "optimist", "version": "0.1.5", "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "main": "./index", "repository": {"type": "git", "url": "http://github.com/substack/node-optimist.git"}, "keywords": ["argument", "option", "parser", "parsing"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT/X11", "engine": ["node >=0.1.100"], "_id": "optimist@0.1.5", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "0.2.16", "_nodeVersion": "v0.3.8-pre", "directories": {}, "files": [""], "_defaultsLoaded": true, "dist": {"shasum": "f5b85dd7ba7928224db268f668419ffb1e7d2cec", "tarball": "https://registry.npmjs.org/optimist/-/optimist-0.1.5.tgz", "integrity": "sha512-+28O5CC6sYNtNxHneBiepbJB2fBpT6yvvUdeHNqg0SRJT+1AKxnNNtU7hs5HRJFf7B72jHECB7pbtRGHIoGXYQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBhJ5nbwZSt4RJr0ivcpbjr03Ml57dpE0ubAvLwtpG8XAiAdVK8j7dIHuUhWTO5Op56vqR6uGgPIXheJ16i9JZ+jxQ=="}]}}, "0.1.6": {"name": "optimist", "version": "0.1.6", "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "main": "./index", "repository": {"type": "git", "url": "http://github.com/substack/node-optimist.git"}, "keywords": ["argument", "option", "parser", "parsing"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT/X11", "engine": ["node >=0.1.100"], "_id": "optimist@0.1.6", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "0.2.16", "_nodeVersion": "v0.3.8-pre", "directories": {}, "files": [""], "_defaultsLoaded": true, "dist": {"shasum": "0f2f671dfec3365509dc335f098158aa90c80100", "tarball": "https://registry.npmjs.org/optimist/-/optimist-0.1.6.tgz", "integrity": "sha512-xtUQ4IBUgg0hrtcHlzUfPs2u0sr8AuGFa0SNlDMmmWBRpYtSzLtkn5QrcB7gWnO04CAFGe8+cxQHlYWsENIUUA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFx841VOZADlGdiUah8ngruCUYTMcXFd3BpyIg2GxMRjAiEAjg4aG82OBf4mQ2WwX9Q7d4v5H/5FrGfCMlkTFmw7Ph0="}]}}, "0.1.7": {"name": "optimist", "version": "0.1.7", "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "main": "./index.js", "repository": {"type": "git", "url": "git://github.com/substack/node-optimist.git"}, "keywords": ["argument", "args", "option", "parser", "parsing", "cli", "command"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT/X11", "engine": ["node >=0.1.100"], "_id": "optimist@0.1.7", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "0.3.15", "_nodeVersion": "v0.4.2", "directories": {}, "files": [""], "_defaultsLoaded": true, "dist": {"shasum": "f83a9644634d446bf3934518257d55dd6d08e183", "tarball": "https://registry.npmjs.org/optimist/-/optimist-0.1.7.tgz", "integrity": "sha512-EATRGYAtGNMwvv3d7jX3Rm95c5tBQTs5ZpKWXnjGqCVPDd44wwf70WHGn0AmpSgOz7D8ex+ABTibov7qKkuvCA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDKE6xzah2BAWw8ZyIPc+cTm0zF1MnEkTjJdfEuq1RidQIgHMYFKV5sNI6xLKVrIFLHAGeS7bQ4AhMD4pgz1lTFw30="}]}}, "0.1.8": {"name": "optimist", "version": "0.1.8", "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "main": "./index.js", "repository": {"type": "git", "url": "git://github.com/substack/node-optimist.git"}, "keywords": ["argument", "args", "option", "parser", "parsing", "cli", "command"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT/X11", "engine": ["node >=0.1.100"], "_id": "optimist@0.1.8", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "0.3.15", "_nodeVersion": "v0.4.2", "directories": {}, "files": [""], "_defaultsLoaded": true, "dist": {"shasum": "58d0adde9d61db67dfbe2c7467da8abf9c86bc94", "tarball": "https://registry.npmjs.org/optimist/-/optimist-0.1.8.tgz", "integrity": "sha512-qMznCVjZfJON6pSrWOsITGoR0Xi08dtqYks/dC8ib/sOmH4MU20cgyX65MgeTdU1/yq8ptU81IAMXQWc2cOklQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDLlClUTEMMe7KpgRBGnBRgcHgCbR7JbypVgIQQk+AcJAiAWanqKqenHOo+7mI9qjCjzuQdtVkaeflMnPntkSWGdTQ=="}]}}, "0.1.9": {"name": "optimist", "version": "0.1.9", "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "main": "./index.js", "repository": {"type": "git", "url": "git://github.com/substack/node-optimist.git"}, "keywords": ["argument", "args", "option", "parser", "parsing", "cli", "command"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT/X11", "engine": ["node >=0.1.100"], "dependencies": {}, "devDependencies": {}, "_id": "optimist@0.1.9", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.1rc8", "_nodeVersion": "v0.4.2", "_defaultsLoaded": true, "dist": {"shasum": "d88fd79743a88960a418f5754b3b2157252447cc", "tarball": "https://registry.npmjs.org/optimist/-/optimist-0.1.9.tgz", "integrity": "sha512-LyfdGHaYpzSyaVxMzEqiLyhFc3I8olOgd/vYLpBkoF6zLwYQQXWGe4sWbqmIfcjhJYKEmuCVwO8gA63x1uTeUg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDHdrVC/QcvneOgWkdhdoljaUoKzQ7x3OCXQPu/zu6+RAIhAMJZGpt+/uH8JrXVAznCHtGLvzHQM2WGpz54f48IqRn0"}]}, "directories": {}}, "0.0.3": {"name": "optimist", "version": "0.0.3", "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "main": "./optimist.js", "repository": {"type": "git", "url": "git://github.com/substack/node-optimist.git"}, "license": "MIT/X11", "engine": ["node >=0.1.100"], "dependencies": {}, "devDependencies": {}, "_id": "optimist@0.0.3", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.6", "_nodeVersion": "v0.4.8-pre", "_defaultsLoaded": true, "dist": {"shasum": "323a5c625b708e0197b72c106aef6444ada0c515", "tarball": "https://registry.npmjs.org/optimist/-/optimist-0.0.3.tgz", "integrity": "sha512-sgFIbHEEzRXEXrI/Ls1wsMsB2ATsL7N9jg/mBOKIafIKj5hAuimUUtE3E2EYjsiXVEZ+JpDXIjwpEHbTIlZCzQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDeQ+K8q1X4Z5R5mEgQhb1RDf5ZdkmDZyUUcsZsEKwc0AiEAkc7P9tw8VJmEHion/b5MbaMci14JwoIctt/IKnK/n28="}]}, "scripts": {}, "directories": {}}, "0.2.0": {"name": "optimist", "version": "0.2.0", "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "main": "./index.js", "repository": {"type": "git", "url": "git://github.com/substack/node-optimist.git"}, "keywords": ["argument", "args", "option", "parser", "parsing", "cli", "command"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT/X11", "engine": {"node": ">=0.4"}, "dependencies": {}, "devDependencies": {}, "_id": "optimist@0.2.0", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.3", "_nodeVersion": "v0.5.0-pre", "_defaultsLoaded": true, "dist": {"shasum": "1cb5b0e727009370f324765e2a5245ac0d806bfd", "tarball": "https://registry.npmjs.org/optimist/-/optimist-0.2.0.tgz", "integrity": "sha512-93xHWODbG8gEhK4GzLIqshMlklUykuBOabkWLBgzK7oMCaKtcv0g4D2Al18njyIzfqg5ovzJKsm3UvN+wQRstw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH7+H0vr6O/UmnCl/PGVGm84HJbZOnu6Q1J+Ji7D+juSAiEAxgY7nw/40jsfWQ0rXW9AhxHr8T3t9GVtjuMW7h1SeKA="}]}, "scripts": {}, "directories": {}}, "0.2.1": {"name": "optimist", "version": "0.2.1", "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "main": "./index.js", "repository": {"type": "git", "url": "git://github.com/substack/node-optimist.git"}, "keywords": ["argument", "args", "option", "parser", "parsing", "cli", "command"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT/X11", "engine": {"node": ">=0.4"}, "dependencies": {}, "devDependencies": {}, "_id": "optimist@0.2.1", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.3", "_nodeVersion": "v0.5.0-pre", "_defaultsLoaded": true, "dist": {"shasum": "80a2d75b660d467f673599dcbc69c113f289554a", "tarball": "https://registry.npmjs.org/optimist/-/optimist-0.2.1.tgz", "integrity": "sha512-YuwZAwj2UXrKJlvIS6TfpGmxziqhyzpbrogi/fVNUqgG38UtlxBpWWifXk5oxyCm8SKgDubnfQ3V/sbkwHB83w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDdTiEHi98Oe1c/WJODrrfy0JCa7CgOj6hT0iQ6dkc11gIhAPaKW9zKB0XZpoprJn2BqWH/dvKfV3xjDkeiWDEcY9jK"}]}, "scripts": {}, "directories": {}}, "0.2.2": {"name": "optimist", "version": "0.2.2", "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "main": "./index.js", "directories": {"lib": ".", "test": "test", "example": "examples"}, "dependencies": {"wordwrap": ">=0.0.1 <0.1"}, "devDependencies": {"expresso": "=0.7.x"}, "repository": {"type": "git", "url": "git://github.com/substack/node-optimist.git"}, "keywords": ["argument", "args", "option", "parser", "parsing", "cli", "command"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT/X11", "engine": {"node": ">=0.4"}, "_id": "optimist@0.2.2", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.3", "_nodeVersion": "v0.5.0-pre", "_defaultsLoaded": true, "dist": {"shasum": "a6bb06ff1f8229a12ee9abcb8160eee35e629ef8", "tarball": "https://registry.npmjs.org/optimist/-/optimist-0.2.2.tgz", "integrity": "sha512-VONCZSp31N9B+3Hdch40+9/XRcHbV4BT8fZKI1GTk5RbUIJPzjixC8a/p14KREiWHhugeFMiOZDeVKpI9inSTQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCDW8swr++1DXVpxiuWtVXufgeamYAOL8tsoVg5rEsfTwIhAKmkBPSbVuQqmx57WY89oEVfj0H4vRo7ZZ7SlaGw0sv9"}]}, "scripts": {}}, "0.2.3": {"name": "optimist", "version": "0.2.3", "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "main": "./index.js", "directories": {"lib": ".", "test": "test", "example": "examples"}, "dependencies": {"wordwrap": ">=0.0.1 <0.1"}, "devDependencies": {"expresso": "=0.7.x"}, "repository": {"type": "git", "url": "git://github.com/substack/node-optimist.git"}, "keywords": ["argument", "args", "option", "parser", "parsing", "cli", "command"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT/X11", "engine": {"node": ">=0.4"}, "_id": "optimist@0.2.3", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.3", "_nodeVersion": "v0.5.0-pre", "_defaultsLoaded": true, "dist": {"shasum": "dc259cc0e5d73e1f3fcc2dea3526e52f19ed740c", "tarball": "https://registry.npmjs.org/optimist/-/optimist-0.2.3.tgz", "integrity": "sha512-h7dyAU8vAJF2utqPKF/IoTFLf6aoISBhIrn6n9Qvrwrk8XkssZxOMO3OB5im+Td2Py1gK84yHcnXx9JV2YvKsw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA82TMJRf0V8o6Gn7DNZGKKU6M8VlbLkBuCK29nyAX4bAiBonHOystEKnh7jFEb5jAUg+EaoZo8geEWIyFwc6NzetQ=="}]}, "scripts": {}}, "0.2.4": {"name": "optimist", "version": "0.2.4", "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "main": "./index.js", "directories": {"lib": ".", "test": "test", "example": "examples"}, "dependencies": {"wordwrap": ">=0.0.1 <0.1"}, "devDependencies": {"hashish": "0.0.x", "expresso": "=0.7.x"}, "repository": {"type": "git", "url": "git://github.com/substack/node-optimist.git"}, "keywords": ["argument", "args", "option", "parser", "parsing", "cli", "command"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT/X11", "engine": {"node": ">=0.4"}, "_id": "optimist@0.2.4", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.10", "_nodeVersion": "v0.5.0-pre", "_defaultsLoaded": true, "dist": {"shasum": "9d543b3444fe127e8c01891c11a38d20b886317b", "tarball": "https://registry.npmjs.org/optimist/-/optimist-0.2.4.tgz", "integrity": "sha512-jYLRjyBGf1hYQV4S/s6K61/IhXfSwzSF0aNnnP3yzeZ41/m80Zcz57gvg9FtO2Dvyg9HV3438pCpURnHCsBy7Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDHM/qjM7zC5xHUHQ8f5iF4pONX4sP/wUkd6/j7y9fLDwIhAOmsbWYZ5XsJ5ExhgG10z3oZb8INvo9X+Fjfaj/0iEMs"}]}, "scripts": {}}, "0.2.5": {"name": "optimist", "version": "0.2.5", "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "main": "./index.js", "directories": {"lib": ".", "test": "test", "example": "examples"}, "dependencies": {"wordwrap": ">=0.0.1 <0.1"}, "devDependencies": {"hashish": "0.0.x", "expresso": "=0.7.x"}, "repository": {"type": "git", "url": "git://github.com/substack/node-optimist.git"}, "keywords": ["argument", "args", "option", "parser", "parsing", "cli", "command"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT/X11", "engine": {"node": ">=0.4"}, "_id": "optimist@0.2.5", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.10", "_nodeVersion": "v0.5.0-pre", "_defaultsLoaded": true, "dist": {"shasum": "50e0127b8443da18f4fdb756aaca446f1c65d136", "tarball": "https://registry.npmjs.org/optimist/-/optimist-0.2.5.tgz", "integrity": "sha512-UJoUbp6ljoeRX6dE8j92Vsf7g05YXONpG+nGeQXoZNrhKsNZp7SZr8rcnjHEO/+mypJAwZgO1IRodIIe0HheNA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGhimzLzqqmmFEdZ9mXRY+Ggb6nGnqA/Zs3XqF93xNdzAiEAkaBvg1dqVZliWHJ5QiKUGGXEowBeAKyOeDUFNkMuEXk="}]}, "scripts": {}}, "0.2.6": {"name": "optimist", "version": "0.2.6", "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "main": "./index.js", "directories": {"lib": ".", "test": "test", "example": "examples"}, "dependencies": {"wordwrap": ">=0.0.1 <0.1"}, "devDependencies": {"hashish": "0.0.x", "expresso": "=0.7.x"}, "repository": {"type": "git", "url": "git://github.com/substack/node-optimist.git"}, "keywords": ["argument", "args", "option", "parser", "parsing", "cli", "command"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT/X11", "engine": {"node": ">=0.4"}, "_id": "optimist@0.2.6", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.10", "_nodeVersion": "v0.5.0-pre", "_defaultsLoaded": true, "dist": {"shasum": "c15b750c98274ea175d241b745edf4ddc88f177b", "tarball": "https://registry.npmjs.org/optimist/-/optimist-0.2.6.tgz", "integrity": "sha512-aQ9Q7wiy7KP1caj9kERXs1w3UKIpG8JXyJIdVFhUirP9oUP29PIq5og7buvqSbKPAIQNd1O2380DhzponCPrSA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCb8mVhwwdB5Zvpl+l12FlyavA8Jd0t/fRygyJ9xKQ2LgIhAOQ5u37LIOhyDS6oAU6Yv2nH3W/hqbEkqb9r7jhs5pE+"}]}, "scripts": {}}, "0.2.7": {"name": "optimist", "version": "0.2.7", "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "main": "./index.js", "directories": {"lib": ".", "test": "test", "example": "examples"}, "dependencies": {"wordwrap": ">=0.0.1 <0.1.0"}, "devDependencies": {"hashish": "0.0.x", "expresso": "0.7.x"}, "repository": {"type": "git", "url": "git://github.com/substack/node-optimist.git"}, "keywords": ["argument", "args", "option", "parser", "parsing", "cli", "command"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT/X11", "engine": {"node": ">=0.4"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "_id": "optimist@0.2.7", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.99", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"shasum": "62945bcc760643d918a5c7649ade86e662144024", "tarball": "https://registry.npmjs.org/optimist/-/optimist-0.2.7.tgz", "integrity": "sha512-xlVTW3js+CHTwQ9M851LU82ew0uxPvXeuwIQfsN9T8JiS4YUnAF3fN0arl1UaX31hWDQou/4JlG7rKSoRcM+Zg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDrU5DAU5KiM+64K3oK2i5JBJRJx+k44fUkZp1U/MZ52AIgPrPKlUjMDSlEsJdCJGvgf+oCX6Bb4Act4fdzgc86mGg="}]}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}]}, "0.2.8": {"name": "optimist", "version": "0.2.8", "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "main": "./index.js", "directories": {"lib": ".", "test": "test", "example": "examples"}, "dependencies": {"wordwrap": ">=0.0.1 <0.1.0"}, "devDependencies": {"hashish": "0.0.x", "expresso": "0.7.x"}, "scripts": {"test": "expresso"}, "repository": {"type": "git", "url": "git://github.com/substack/node-optimist.git"}, "keywords": ["argument", "args", "option", "parser", "parsing", "cli", "command"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT/X11", "engine": {"node": ">=0.4"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "_id": "optimist@0.2.8", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.99", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"shasum": "e981ab7e268b457948593b55674c099a815cac31", "tarball": "https://registry.npmjs.org/optimist/-/optimist-0.2.8.tgz", "integrity": "sha512-Wy7E3cQDpqsTIFyW7m22hSevyTLxw850ahYv7FWsw4G6MIKVTZ8NSA95KBrQ95a4SMsMr1UGUUnwEFKhVaSzIg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICBbdmKoYarFxfXsIpDwG+wQUGyKIu+LUCIyyJgRf+smAiAanIVVbyYQR8B+PfZqAgfoF5e3tIC9BZYwq4TS2pz6Pg=="}]}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}]}, "0.3.0": {"name": "optimist", "version": "0.3.0", "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "main": "./index.js", "directories": {"lib": ".", "test": "test", "example": "examples"}, "dependencies": {"wordwrap": ">=0.0.1 <0.1.0"}, "devDependencies": {"hashish": "0.0.x", "expresso": "0.7.x"}, "scripts": {"test": "expresso"}, "repository": {"type": "git", "url": "git://github.com/substack/node-optimist.git"}, "keywords": ["argument", "args", "option", "parser", "parsing", "cli", "command"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT/X11", "engine": {"node": ">=0.4"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "_id": "optimist@0.3.0", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"shasum": "4458c1f02acf1e5c9ece36ce2fd4d338e56ee0f6", "tarball": "https://registry.npmjs.org/optimist/-/optimist-0.3.0.tgz", "integrity": "sha512-EuNw7KTQ51UbGhC20ivSiYafQQHd11qJLCIWKeS+i9vZbVCueaOZPaKWN4LiiFh8/K3l9UdbifI0QZoQ2QOBZQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBzPlsFbKTazxlkbvE+FjelWJRGfWgeok2mAhRktHfvmAiEAxws3ijO5oOyu5PshYL4SlTAbh6vy9wjigwR4G+v8zFI="}]}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}]}, "0.3.1": {"name": "optimist", "version": "0.3.1", "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "main": "./index.js", "directories": {"lib": ".", "test": "test", "example": "examples"}, "dependencies": {"wordwrap": ">=0.0.1 <0.1.0"}, "devDependencies": {"hashish": "0.0.x", "expresso": "0.7.x"}, "scripts": {"test": "expresso"}, "repository": {"type": "git", "url": "git://github.com/substack/node-optimist.git"}, "keywords": ["argument", "args", "option", "parser", "parsing", "cli", "command"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT/X11", "engine": {"node": ">=0.4"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "_id": "optimist@0.3.1", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"shasum": "6680d30560193af5a55eb64394883ed7bcb98f2e", "tarball": "https://registry.npmjs.org/optimist/-/optimist-0.3.1.tgz", "integrity": "sha512-SlvDwWll6UznuRvu5kxmlskndLf8GPbTFr75XYWkbmDdDL3HZWnsUqUbniXCpWlJnsIxJ5ZKKy0DOfL0i2RCRA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDu9QhWsodHPCGT2KzxDQw/84Sx2ht1lPXWNxBAtZo9CwIhAJeMIJP78ocWSy+nTAoCCw8XskRraOy2KtNYN7QJkLRo"}]}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}]}, "0.3.3": {"name": "optimist", "version": "0.3.3", "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "main": "./index.js", "directories": {"lib": ".", "test": "test", "example": "example"}, "dependencies": {"wordwrap": "~0.0.2"}, "devDependencies": {"hashish": "~0.0.4", "tap": "~0.2.4"}, "scripts": {"test": "tap ./test/*.js"}, "repository": {"type": "git", "url": "git://github.com/substack/node-optimist.git"}, "keywords": ["argument", "args", "option", "parser", "parsing", "cli", "command"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT/X11", "engine": {"node": ">=0.4"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "_id": "optimist@0.3.3", "optionalDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.1.1", "_nodeVersion": "v0.6.11", "_defaultsLoaded": true, "dist": {"shasum": "d75701d48f37fe0e6f06f88e7b0cf0882a3ce394", "tarball": "https://registry.npmjs.org/optimist/-/optimist-0.3.3.tgz", "integrity": "sha512-xbUfNrcEeE3Hss5NMx7EGjdak1dx5V+hX6xvidQOefWDAZCoDgNDYxHN5CymG/nWKhurrdjYuGlB+UCeqQT2jA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQChKZv6rJM2oMy2d7XMbQvUXxZOyzYxRAqTI0Z28QjXSAIhAOs0u6R3bfi3SLyA0I6W4l1oTmEN3Tr0mTPBXSN8fYP3"}]}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}]}, "0.3.4": {"name": "optimist", "version": "0.3.4", "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "main": "./index.js", "directories": {"lib": ".", "test": "test", "example": "example"}, "dependencies": {"wordwrap": "~0.0.2"}, "devDependencies": {"hashish": "~0.0.4", "tap": "~0.2.4"}, "scripts": {"test": "tap ./test/*.js"}, "repository": {"type": "git", "url": "git://github.com/substack/node-optimist.git"}, "keywords": ["argument", "args", "option", "parser", "parsing", "cli", "command"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT/X11", "engine": {"node": ">=0.4"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "_id": "optimist@0.3.4", "optionalDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.1.1", "_nodeVersion": "v0.6.11", "_defaultsLoaded": true, "dist": {"shasum": "4d6d0bd71ffad0da4ba4f6d876d5eeb04e07480b", "tarball": "https://registry.npmjs.org/optimist/-/optimist-0.3.4.tgz", "integrity": "sha512-CM668OmXLjrLKlVPze/bc0BIOZ0qoM5gWwEVurkjXPv6cgkybJ2TSt86nmaud+niwPfWHLOcgbbcRtA5zJgOIQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD84Y7szEppXoo+d0Se5SBShrCNalDvo8DQoZk6P7mJ8gIgO1+jwS5ireOGBKE64qvLbtNSBx/IaDoSl64cZ40gO6s="}]}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}]}, "0.3.5": {"name": "optimist", "version": "0.3.5", "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "main": "./index.js", "directories": {"lib": ".", "test": "test", "example": "example"}, "dependencies": {"wordwrap": "~0.0.2"}, "devDependencies": {"hashish": "~0.0.4", "tap": "~0.2.4"}, "scripts": {"test": "tap ./test/*.js"}, "repository": {"type": "git", "url": "http://github.com/substack/node-optimist.git"}, "keywords": ["argument", "args", "option", "parser", "parsing", "cli", "command"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT/X11", "engine": {"node": ">=0.4"}, "_id": "optimist@0.3.5", "dist": {"shasum": "03654b52417030312d109f39b159825b60309304", "tarball": "https://registry.npmjs.org/optimist/-/optimist-0.3.5.tgz", "integrity": "sha512-yZCNofwlj5aaED9y/eZbIx4t+ZRy3eN4Muv/+hKvOWcuOOxIb1WqPgEilLr3/vrQ7adbxrA6v43fHNg9WevvpQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICknt3WUwrXHmfetFJJllF0FOLeVhnL+oYLL48CuUoyPAiEA/jT9SARUeomnCxYtWZvPJmlYP3tqPVRY2KxhN+/PP8E="}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}]}, "0.3.6": {"name": "optimist", "version": "0.3.6", "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "main": "./index.js", "dependencies": {"wordwrap": "~0.0.2"}, "devDependencies": {"hashish": "~0.0.4", "tap": "~0.4.0"}, "scripts": {"test": "tap ./test/*.js"}, "repository": {"type": "git", "url": "http://github.com/substack/node-optimist.git"}, "keywords": ["argument", "args", "option", "parser", "parsing", "cli", "command"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT/X11", "engine": {"node": ">=0.4"}, "_id": "optimist@0.3.6", "dist": {"shasum": "816e0039f848fccf9db70cada5535ed9dd55f496", "tarball": "https://registry.npmjs.org/optimist/-/optimist-0.3.6.tgz", "integrity": "sha512-ffttxIVK/x658w7MUCT6LttyYMKqWKLQUeBU1AeocwAS82c+kLOJI//SatCjljkQT/LBRZMJRRz00NexsuOLWQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCzVU5RTNZOdolVJjyax5VyzqjDHoMTeThdcUGWS5bvWAIgOAtt25j0+BLO3o81YvMWjjk2Ey1eNwhGV2smJx0+wk0="}]}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "0.3.7": {"name": "optimist", "version": "0.3.7", "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "main": "./index.js", "dependencies": {"wordwrap": "~0.0.2"}, "devDependencies": {"hashish": "~0.0.4", "tap": "~0.4.0"}, "scripts": {"test": "tap ./test/*.js"}, "repository": {"type": "git", "url": "http://github.com/substack/node-optimist.git"}, "keywords": ["argument", "args", "option", "parser", "parsing", "cli", "command"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT/X11", "engine": {"node": ">=0.4"}, "_id": "optimist@0.3.7", "dist": {"shasum": "c90941ad59e4273328923074d2cf2e7cbc6ec0d9", "tarball": "https://registry.npmjs.org/optimist/-/optimist-0.3.7.tgz", "integrity": "sha512-TCx0dXQzVtSCg2OgY/bO9hjM9cV4XYx09TVK+s3+FhkjT6LovsLe+pPMzpWf+6yXK/hUizs2gUoTw3jHM0VaTQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAj+2mLiaRNTNo4azYcc9F4mcj6WN0D5W9BV9FGBQt1/AiAKMcSsE4lEkTdW32ozxXdeAp0tRpZgty6U4/w+oB9a9g=="}]}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "0.4.0": {"name": "optimist", "version": "0.4.0", "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "main": "./index.js", "dependencies": {"wordwrap": "~0.0.2"}, "devDependencies": {"hashish": "~0.0.4", "tap": "~0.4.0"}, "scripts": {"test": "tap ./test/*.js"}, "repository": {"type": "git", "url": "http://github.com/substack/node-optimist.git"}, "keywords": ["argument", "args", "option", "parser", "parsing", "cli", "command"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT/X11", "engine": {"node": ">=0.4"}, "_id": "optimist@0.4.0", "dist": {"shasum": "cb8ec37f2fe3aa9864cb67a275250e7e19620a25", "tarball": "https://registry.npmjs.org/optimist/-/optimist-0.4.0.tgz", "integrity": "sha512-v/ueFqighwEQeAnw8ZJofuEsHDvAiUGyfojtJdKurxd4KUanznxvK9hJocaxWpn+nAAbxTA4qfh7orYHgNZIWA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCW2T0x0mtyitLPrXPV+uu01+t3mTgFja9cca3Ggb+NBwIhAJuzTRIUjCiF1XlN1p320ljMkHT4aBq1zSCuv4ZKrsnN"}]}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "0.5.0": {"name": "optimist", "version": "0.5.0", "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "main": "./index.js", "dependencies": {"wordwrap": "~0.0.2"}, "devDependencies": {"hashish": "~0.0.4", "tap": "~0.4.0"}, "scripts": {"test": "tap ./test/*.js"}, "repository": {"type": "git", "url": "http://github.com/substack/node-optimist.git"}, "keywords": ["argument", "args", "option", "parser", "parsing", "cli", "command"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT/X11", "engine": {"node": ">=0.4"}, "_id": "optimist@0.5.0", "dist": {"shasum": "d9c60da4c34811418d183390623f8046f134a2d4", "tarball": "https://registry.npmjs.org/optimist/-/optimist-0.5.0.tgz", "integrity": "sha512-LqS69GyG1JN9XI0NLfTVL+UwJbQZqZmEMDl3FET2yToOyu1o1G1aSp/JjIwi1MaoBJfqSHjPq6ZbqNj1d5D/xQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDYBO2uKc7ZQaiAXMtFZaJJniYU6BQFVA3SSZ6QqJoQfAIhAI/UTuoW15HpkvazLBvf8wY2cmAmu3SAHabo76Kr2IEh"}]}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "0.5.1": {"name": "optimist", "version": "0.5.1", "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "main": "./index.js", "dependencies": {"wordwrap": "~0.0.2"}, "devDependencies": {"hashish": "~0.0.4", "tap": "~0.4.0"}, "scripts": {"test": "tap ./test/*.js"}, "repository": {"type": "git", "url": "http://github.com/substack/node-optimist.git"}, "keywords": ["argument", "args", "option", "parser", "parsing", "cli", "command"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT/X11", "engine": {"node": ">=0.4"}, "_id": "optimist@0.5.1", "dist": {"shasum": "9f6a34014ca8344a60a5d39734436f49d2bbe4f5", "tarball": "https://registry.npmjs.org/optimist/-/optimist-0.5.1.tgz", "integrity": "sha512-/5Ej6We31JpUthc9Y7x/bK1iuyUX1KToceiddm88Nwxf37/siMJk0v0JirUH1tRmB1alkqWSMhR28nRJ1r2/cQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCEo7sltPD4YCAPnLqU3YajZn6CN6CX9+hyLLBp8zc3cwIgQNxhgRfuJWnXbMbFkivD/lzsTWjwHEQu5VVaPbR6eo0="}]}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "0.5.2": {"name": "optimist", "version": "0.5.2", "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "main": "./index.js", "dependencies": {"wordwrap": "~0.0.2"}, "devDependencies": {"hashish": "~0.0.4", "tap": "~0.4.0"}, "scripts": {"test": "tap ./test/*.js"}, "repository": {"type": "git", "url": "http://github.com/substack/node-optimist.git"}, "keywords": ["argument", "args", "option", "parser", "parsing", "cli", "command"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT/X11", "engine": {"node": ">=0.4"}, "_id": "optimist@0.5.2", "dist": {"shasum": "85c8c1454b3315e4a78947e857b1df033450bfbc", "tarball": "https://registry.npmjs.org/optimist/-/optimist-0.5.2.tgz", "integrity": "sha512-r9M8ZpnM9SXV5Wii7TCqienfcaY3tAiJe9Jchof87icbmbruKgK0xKXngmrnowTDnEawmmI1Qbha59JEoBkBGA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAKz6nsUsi96UIRr7q3zoJmCNcmp4r1CN0KoKr2IW6U5AiBn4o9Xak4o/KiM9eqli82j99+uz+4jtsEwSHECLysMAg=="}]}, "_from": ".", "_npmVersion": "1.2.2", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "0.6.0": {"name": "optimist", "version": "0.6.0", "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "main": "./index.js", "dependencies": {"wordwrap": "~0.0.2", "minimist": "~0.0.1"}, "devDependencies": {"hashish": "~0.0.4", "tap": "~0.4.0"}, "scripts": {"test": "tap ./test/*.js"}, "repository": {"type": "git", "url": "http://github.com/substack/node-optimist.git"}, "keywords": ["argument", "args", "option", "parser", "parsing", "cli", "command"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT/X11", "engine": {"node": ">=0.4"}, "bugs": {"url": "https://github.com/substack/node-optimist/issues"}, "_id": "optimist@0.6.0", "dist": {"shasum": "69424826f3405f79f142e6fc3d9ae58d4dbb9200", "tarball": "https://registry.npmjs.org/optimist/-/optimist-0.6.0.tgz", "integrity": "sha512-ubrZPyOU0AHpXkmwqfWolap+eHMwQ484AKivkf0ZGyysd6fUJZl7ow9iu5UNV1vCZv46HQ7EM83IC3NGJ820hg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC4eojL2IoudT6YdyejQ9KPNucJZPty55e48hkZpJ+fqgIgBGgjSgtgg9TwA7ZJsaqR3QneMiYyZgSYLOMysYc8sas="}]}, "_from": ".", "_npmVersion": "1.3.0", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "0.6.1": {"name": "optimist", "version": "0.6.1", "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "main": "./index.js", "dependencies": {"wordwrap": "~0.0.2", "minimist": "~0.0.1"}, "devDependencies": {"hashish": "~0.0.4", "tap": "~0.4.0"}, "scripts": {"test": "tap ./test/*.js"}, "repository": {"type": "git", "url": "http://github.com/substack/node-optimist.git"}, "keywords": ["argument", "args", "option", "parser", "parsing", "cli", "command"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT/X11", "engine": {"node": ">=0.4"}, "bugs": {"url": "https://github.com/substack/node-optimist/issues"}, "homepage": "https://github.com/substack/node-optimist", "_id": "optimist@0.6.1", "dist": {"shasum": "da3ea74686fa21a19a111c326e90eb15a0196686", "tarball": "https://registry.npmjs.org/optimist/-/optimist-0.6.1.tgz", "integrity": "sha512-snN4O4TkigujZphWLN0E//nQmm7790RYaE53DdL7ZYwee2D8DDo9/EyYiKUfN3rneWUjhJnueija3G9I2i0h3g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDU6DtY6zY6wqchCXaJqabtpLnHvnW5gK9Rtwl47e+vsgIhANQrTxW4ILcdi9N4e1A/WBf+pCs6ht1Mt8a3ctcn0ikE"}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}}, "maintainers": [{"email": "<EMAIL>", "name": "chevex"}, {"email": "<EMAIL>", "name": "bcoe"}], "repository": {"type": "git", "url": "http://github.com/substack/node-optimist.git"}, "description": "Light-weight option parsing with an argv hash. No optstrings attached.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "time": {"modified": "2023-04-12T04:02:15.820Z", "created": "2010-12-21T14:33:53.354Z", "0.0.1": "2010-12-21T14:33:53.354Z", "0.0.2": "2010-12-21T14:33:53.354Z", "0.0.3": "2010-12-21T14:33:53.354Z", "0.0.4": "2010-12-21T14:33:53.354Z", "0.0.5": "2010-12-21T14:33:53.354Z", "0.0.6": "2010-12-21T14:33:53.354Z", "0.0.7": "2010-12-21T14:33:53.354Z", "0.1.0": "2010-12-21T14:33:53.354Z", "0.1.1": "2010-12-21T14:33:53.354Z", "0.1.2": "2010-12-21T14:33:53.354Z", "0.1.3": "2010-12-21T14:33:53.354Z", "0.1.4": "2011-01-30T07:04:28.963Z", "0.1.5": "2011-02-01T08:01:38.160Z", "0.1.6": "2011-02-13T23:35:31.427Z", "0.1.7": "2011-03-28T05:44:30.304Z", "0.1.8": "2011-03-28T21:03:46.234Z", "0.1.9": "2011-04-14T03:33:37.811Z", "0.2.0": "2011-05-08T03:32:40.650Z", "0.2.1": "2011-05-16T07:14:37.232Z", "0.2.2": "2011-05-16T09:20:48.490Z", "0.2.3": "2011-05-16T19:03:41.732Z", "0.2.4": "2011-06-13T04:00:46.046Z", "0.2.5": "2011-06-25T22:24:50.361Z", "0.2.6": "2011-07-14T21:41:44.257Z", "0.2.7": "2011-10-20T02:25:41.335Z", "0.2.8": "2011-10-20T03:47:03.659Z", "0.3.0": "2011-12-09T08:22:35.261Z", "0.3.1": "2011-12-31T08:45:18.568Z", "0.3.3": "2012-04-30T06:46:32.091Z", "0.3.4": "2012-04-30T06:59:33.018Z", "0.3.5": "2012-10-10T11:12:31.230Z", "0.3.6": "2013-04-04T04:06:39.393Z", "0.3.7": "2013-04-04T04:09:40.361Z", "0.4.0": "2013-04-13T19:05:38.560Z", "0.5.0": "2013-05-18T22:00:22.299Z", "0.5.1": "2013-05-30T07:17:29.830Z", "0.5.2": "2013-05-31T03:46:50.271Z", "0.6.0": "2013-06-25T08:49:19.511Z", "0.6.1": "2014-02-06T05:40:56.954Z"}, "users": {"avianflu": true, "mvolkmann": true, "naholyr": true, "vtsvang": true, "linus": true, "pvorb": true, "matthiasg": true, "dshaw": true, "thlorenz": true, "MattiSG": true, "fgribreau": true, "hughsk": true, "pid": true, "gillesruppert": true, "jswartwood": true, "tokuhirom": true, "kennethjor": true, "tivac": true, "konklone": true, "hij1nx": true, "luk": true, "booyaa": true, "megadrive": true, "nrn": true, "kastor": true, "joshthegeek": true, "charmander": true, "zaphod1984": true, "everywhere.js": true, "florianwendelborn": true, "spekkionu": true, "conradz": true, "pana": true, "darosh": true, "oliversalzburg": true, "ettalea": true, "jacques": true, "cilindrox": true, "apache2046": true, "alpigc": true, "cocopas": true, "tanel": true, "patrickpietens": true, "zheref": true, "jimnox": true, "beth_rogers465": true, "davidrlee": true, "janez89": true, "twlk28": true, "huangjia86": true, "tchey": true, "hrmoller": true, "bluejeansandrain": true, "contolini": true, "jits": true, "yasinaydin": true, "mtscout6": true, "riaanpelser": true, "sandalsoft": true, "kevbaker": true, "forlan": true, "yourhoneysky": true, "l0n9h02n": true, "intellinote": true, "micate": true, "noyobo": true, "zhanglin": true, "liliguodong": true, "alekseyleshko": true, "allthingssmitty": true, "thorsson": true, "kaiquewdev": true, "alxe.master": true, "tomas-sereikis": true, "chzhewl": true, "jtianling": true, "haeck": true, "aguz": true, "ab": true, "iksnae": true, "gchudnov": true, "joeyblue": true, "skyinlayer": true, "subso": true, "dennismadsen": true, "lewisbrown": true, "dexteryy": true, "xiechao06": true, "helsner": true, "rivy": true, "vbv": true, "javascript": true, "guananddu": true, "monolithed": true, "kontrax": true, "prestorondo": true, "zombinary": true, "pruettti": true, "slurm": true, "joypeterson": true, "boto": true, "evanyeung": true, "theaklair": true, "nuer": true, "nickeltobias": true, "xyyjk": true, "antanst": true, "scottfreecode": true, "dofy": true, "ahvonenj": true, "alphatr": true, "abdul": true, "dzhou777": true, "rylan_yan": true, "xueboren": true, "codelegant": true, "noncreature0714": true, "yujiikebata": true, "shuoshubao": true, "leizongmin": true, "langri-sha": true, "chinawolf_wyp": true, "asaupup": true, "alexxnica": true, "suhaib": true, "jonasjancarik": true, "usex": true, "wayn": true, "jruif": true, "mateussampsouza": true, "dm7": true, "icoon.li": true, "gurunate": true, "nuwaio": true, "justjavac": true, "poryoung": true, "kodekracker": true, "monjer": true, "zuojiang": true, "jeremy_yang": true, "isayme": true, "xiaobing": true, "flumpus-dev": true}, "readme": "# DEPRECATION NOTICE\n\nI don't want to maintain this module anymore since I just use\n[minimist](https://npmjs.org/package/minimist), the argument parsing engine,\ndirectly instead nowadays.\n\nSee [yargs](https://github.com/chevex/yargs) for the modern, pirate-themed\nsuccessor to optimist.\n\n[![yarrrrrrrgs!](http://i.imgur.com/4WFGVJ9.png)](https://github.com/chevex/yargs)\n\nYou should also consider [nomnom](https://github.com/harthur/nomnom).\n\noptimist\n========\n\nOptimist is a node.js library for option parsing for people who hate option\nparsing. More specifically, this module is for people who like all the --bells\nand -whistlz of program usage but think optstrings are a waste of time.\n\nWith optimist, option parsing doesn't have to suck (as much).\n\n[![build status](https://secure.travis-ci.org/substack/node-optimist.png)](http://travis-ci.org/substack/node-optimist)\n\nexamples\n========\n\nWith Optimist, the options are just a hash! No optstrings attached.\n-------------------------------------------------------------------\n\nxup.js:\n\n````javascript\n#!/usr/bin/env node\nvar argv = require('optimist').argv;\n\nif (argv.rif - 5 * argv.xup > 7.138) {\n    console.log('Buy more riffiwobbles');\n}\nelse {\n    console.log('Sell the xupptumblers');\n}\n````\n\n***\n\n    $ ./xup.js --rif=55 --xup=9.52\n    Buy more riffiwobbles\n    \n    $ ./xup.js --rif 12 --xup 8.1\n    Sell the xupptumblers\n\n![This one's optimistic.](http://substack.net/images/optimistic.png)\n\nBut wait! There's more! You can do short options:\n-------------------------------------------------\n \nshort.js:\n\n````javascript\n#!/usr/bin/env node\nvar argv = require('optimist').argv;\nconsole.log('(%d,%d)', argv.x, argv.y);\n````\n\n***\n\n    $ ./short.js -x 10 -y 21\n    (10,21)\n\nAnd booleans, both long and short (and grouped):\n----------------------------------\n\nbool.js:\n\n````javascript\n#!/usr/bin/env node\nvar util = require('util');\nvar argv = require('optimist').argv;\n\nif (argv.s) {\n    util.print(argv.fr ? 'Le chat dit: ' : 'The cat says: ');\n}\nconsole.log(\n    (argv.fr ? 'miaou' : 'meow') + (argv.p ? '.' : '')\n);\n````\n\n***\n\n    $ ./bool.js -s\n    The cat says: meow\n    \n    $ ./bool.js -sp\n    The cat says: meow.\n\n    $ ./bool.js -sp --fr\n    Le chat dit: miaou.\n\nAnd non-hypenated options too! Just use `argv._`!\n-------------------------------------------------\n \nnonopt.js:\n\n````javascript\n#!/usr/bin/env node\nvar argv = require('optimist').argv;\nconsole.log('(%d,%d)', argv.x, argv.y);\nconsole.log(argv._);\n````\n\n***\n\n    $ ./nonopt.js -x 6.82 -y 3.35 moo\n    (6.82,3.35)\n    [ 'moo' ]\n    \n    $ ./nonopt.js foo -x 0.54 bar -y 1.12 baz\n    (0.54,1.12)\n    [ 'foo', 'bar', 'baz' ]\n\nPlus, Optimist comes with .usage() and .demand()!\n-------------------------------------------------\n\ndivide.js:\n\n````javascript\n#!/usr/bin/env node\nvar argv = require('optimist')\n    .usage('Usage: $0 -x [num] -y [num]')\n    .demand(['x','y'])\n    .argv;\n\nconsole.log(argv.x / argv.y);\n````\n\n***\n \n    $ ./divide.js -x 55 -y 11\n    5\n    \n    $ node ./divide.js -x 4.91 -z 2.51\n    Usage: node ./divide.js -x [num] -y [num]\n\n    Options:\n      -x  [required]\n      -y  [required]\n\n    Missing required arguments: y\n\nEVEN MORE HOLY COW\n------------------\n\ndefault_singles.js:\n\n````javascript\n#!/usr/bin/env node\nvar argv = require('optimist')\n    .default('x', 10)\n    .default('y', 10)\n    .argv\n;\nconsole.log(argv.x + argv.y);\n````\n\n***\n\n    $ ./default_singles.js -x 5\n    15\n\ndefault_hash.js:\n\n````javascript\n#!/usr/bin/env node\nvar argv = require('optimist')\n    .default({ x : 10, y : 10 })\n    .argv\n;\nconsole.log(argv.x + argv.y);\n````\n\n***\n\n    $ ./default_hash.js -y 7\n    17\n\nAnd if you really want to get all descriptive about it...\n---------------------------------------------------------\n\nboolean_single.js\n\n````javascript\n#!/usr/bin/env node\nvar argv = require('optimist')\n    .boolean('v')\n    .argv\n;\nconsole.dir(argv);\n````\n\n***\n\n    $ ./boolean_single.js -v foo bar baz\n    true\n    [ 'bar', 'baz', 'foo' ]\n\nboolean_double.js\n\n````javascript\n#!/usr/bin/env node\nvar argv = require('optimist')\n    .boolean(['x','y','z'])\n    .argv\n;\nconsole.dir([ argv.x, argv.y, argv.z ]);\nconsole.dir(argv._);\n````\n\n***\n\n    $ ./boolean_double.js -x -z one two three\n    [ true, false, true ]\n    [ 'one', 'two', 'three' ]\n\nOptimist is here to help...\n---------------------------\n\nYou can describe parameters for help messages and set aliases. Optimist figures\nout how to format a handy help string automatically.\n\nline_count.js\n\n````javascript\n#!/usr/bin/env node\nvar argv = require('optimist')\n    .usage('Count the lines in a file.\\nUsage: $0')\n    .demand('f')\n    .alias('f', 'file')\n    .describe('f', 'Load a file')\n    .argv\n;\n\nvar fs = require('fs');\nvar s = fs.createReadStream(argv.file);\n\nvar lines = 0;\ns.on('data', function (buf) {\n    lines += buf.toString().match(/\\n/g).length;\n});\n\ns.on('end', function () {\n    console.log(lines);\n});\n````\n\n***\n\n    $ node line_count.js\n    Count the lines in a file.\n    Usage: node ./line_count.js\n\n    Options:\n      -f, --file  Load a file  [required]\n\n    Missing required arguments: f\n\n    $ node line_count.js --file line_count.js \n    20\n    \n    $ node line_count.js -f line_count.js \n    20\n\nmethods\n=======\n\nBy itself,\n\n````javascript\nrequire('optimist').argv\n`````\n\nwill use `process.argv` array to construct the `argv` object.\n\nYou can pass in the `process.argv` yourself:\n\n````javascript\nrequire('optimist')([ '-x', '1', '-y', '2' ]).argv\n````\n\nor use .parse() to do the same thing:\n\n````javascript\nrequire('optimist').parse([ '-x', '1', '-y', '2' ])\n````\n\nThe rest of these methods below come in just before the terminating `.argv`.\n\n.alias(key, alias)\n------------------\n\nSet key names as equivalent such that updates to a key will propagate to aliases\nand vice-versa.\n\nOptionally `.alias()` can take an object that maps keys to aliases.\n\n.default(key, value)\n--------------------\n\nSet `argv[key]` to `value` if no option was specified on `process.argv`.\n\nOptionally `.default()` can take an object that maps keys to default values.\n\n.demand(key)\n------------\n\nIf `key` is a string, show the usage information and exit if `key` wasn't\nspecified in `process.argv`.\n\nIf `key` is a number, demand at least as many non-option arguments, which show\nup in `argv._`.\n\nIf `key` is an Array, demand each element.\n\n.describe(key, desc)\n--------------------\n\nDescribe a `key` for the generated usage information.\n\nOptionally `.describe()` can take an object that maps keys to descriptions.\n\n.options(key, opt)\n------------------\n\nInstead of chaining together `.alias().demand().default()`, you can specify\nkeys in `opt` for each of the chainable methods.\n\nFor example:\n\n````javascript\nvar argv = require('optimist')\n    .options('f', {\n        alias : 'file',\n        default : '/etc/passwd',\n    })\n    .argv\n;\n````\n\nis the same as\n\n````javascript\nvar argv = require('optimist')\n    .alias('f', 'file')\n    .default('f', '/etc/passwd')\n    .argv\n;\n````\n\nOptionally `.options()` can take an object that maps keys to `opt` parameters.\n\n.usage(message)\n---------------\n\nSet a usage message to show which commands to use. Inside `message`, the string\n`$0` will get interpolated to the current script name or node command for the\npresent script similar to how `$0` works in bash or perl.\n\n.check(fn)\n----------\n\nCheck that certain conditions are met in the provided arguments.\n\nIf `fn` throws or returns `false`, show the thrown error, usage information, and\nexit.\n\n.boolean(key)\n-------------\n\nInterpret `key` as a boolean. If a non-flag option follows `key` in\n`process.argv`, that string won't get set as the value of `key`.\n\nIf `key` never shows up as a flag in `process.arguments`, `argv[key]` will be\n`false`.\n\nIf `key` is an Array, interpret all the elements as booleans.\n\n.string(key)\n------------\n\nTell the parser logic not to interpret `key` as a number or boolean.\nThis can be useful if you need to preserve leading zeros in an input.\n\nIf `key` is an Array, interpret all the elements as strings.\n\n.wrap(columns)\n--------------\n\nFormat usage output to wrap at `columns` many columns.\n\n.help()\n-------\n\nReturn the generated usage string.\n\n.showHelp(fn=console.error)\n---------------------------\n\nPrint the usage data using `fn` for printing.\n\n.parse(args)\n------------\n\nParse `args` instead of `process.argv`. Returns the `argv` object.\n\n.argv\n-----\n\nGet the arguments as a plain old object.\n\nArguments without a corresponding flag show up in the `argv._` array.\n\nThe script name or node command is available at `argv.$0` similarly to how `$0`\nworks in bash or perl.\n\nparsing tricks\n==============\n\nstop parsing\n------------\n\nUse `--` to stop parsing flags and stuff the remainder into `argv._`.\n\n    $ node examples/reflect.js -a 1 -b 2 -- -c 3 -d 4\n    { _: [ '-c', '3', '-d', '4' ],\n      '$0': 'node ./examples/reflect.js',\n      a: 1,\n      b: 2 }\n\nnegate fields\n-------------\n\nIf you want to explicity set a field to false instead of just leaving it\nundefined or to override a default you can do `--no-key`.\n\n    $ node examples/reflect.js -a --no-b\n    { _: [],\n      '$0': 'node ./examples/reflect.js',\n      a: true,\n      b: false }\n\nnumbers\n-------\n\nEvery argument that looks like a number (`!isNaN(Number(arg))`) is converted to\none. This way you can just `net.createConnection(argv.port)` and you can add\nnumbers out of `argv` with `+` without having that mean concatenation,\nwhich is super frustrating.\n\nduplicates\n----------\n\nIf you specify a flag multiple times it will get turned into an array containing\nall the values in order.\n\n    $ node examples/reflect.js -x 5 -x 8 -x 0\n    { _: [],\n      '$0': 'node ./examples/reflect.js',\n        x: [ 5, 8, 0 ] }\n\ndot notation\n------------\n\nWhen you use dots (`.`s) in argument names, an implicit object path is assumed.\nThis lets you organize arguments into nested objects.\n\n     $ node examples/reflect.js --foo.bar.baz=33 --foo.quux=5\n     { _: [],\n       '$0': 'node ./examples/reflect.js',\n         foo: { bar: { baz: 33 }, quux: 5 } }\n\nshort numbers\n-------------\n\nShort numeric `head -n5` style argument work too:\n\n    $ node reflect.js -n123 -m456\n    { '3': true,\n      '6': true,\n      _: [],\n      '$0': 'node ./reflect.js',\n      n: 123,\n      m: 456 }\n\ninstallation\n============\n\nWith [npm](http://github.com/isaacs/npm), just do:\n    npm install optimist\n \nor clone this project on github:\n\n    git clone http://github.com/substack/node-optimist.git\n\nTo run the tests with [expresso](http://github.com/visionmedia/expresso),\njust do:\n    \n    expresso\n\ninspired By\n===========\n\nThis module is loosely inspired by Perl's\n[Getopt::Casual](http://search.cpan.org/~photo/Getopt-Casual-0.13.1/Casual.pm).\n", "readmeFilename": "readme.markdown", "homepage": "https://github.com/substack/node-optimist", "keywords": ["argument", "args", "option", "parser", "parsing", "cli", "command"], "bugs": {"url": "https://github.com/substack/node-optimist/issues"}, "license": "MIT/X11"}