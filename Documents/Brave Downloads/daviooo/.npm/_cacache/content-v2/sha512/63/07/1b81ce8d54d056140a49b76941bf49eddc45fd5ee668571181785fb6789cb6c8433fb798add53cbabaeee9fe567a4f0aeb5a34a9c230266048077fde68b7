{"_id": "cycle", "_rev": "12-0ac2bb90bc7336f0083778f110174eed", "name": "cycle", "description": "decycle your json", "dist-tags": {"latest": "1.0.3"}, "versions": {"1.0.0": {"name": "cycle", "description": "decycle your json", "author": {"name": "<PERSON><PERSON><PERSON> crockford", "email": "<EMAIL>", "url": "http://www.CROCKFORD.com/"}, "version": "1.0.0", "main": "./cycle.js", "homepage": "https://github.com/douglascrockford/JSON-js", "repository": {"type": "git", "url": "git://github.com/douglascrockford/JSON-js.git"}, "bugs": {"name": "http://github.com/douglascrockford/JSON-js/issues"}, "keywords": ["json", "cycle", "stringify", "parse"], "engines": {"node": ">=0.4.0"}, "_npmUser": {"name": "dscape", "email": "<EMAIL>"}, "_id": "cycle@1.0.0", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.0-2", "_nodeVersion": "v0.6.7", "_defaultsLoaded": true, "dist": {"shasum": "7d2bfce4be6c81cc76d1fc12bcbaec0b89c1bc55", "tarball": "https://registry.npmjs.org/cycle/-/cycle-1.0.0.tgz", "integrity": "sha512-Z1rO5Y0zIks4L/YWltBsSMG7vxLlrbOjQxiyQagFt+QThNofNInP5gaExhIlNLLRC6a+yoXC3RaIrroHqgcDYg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBYH5n2ByBH7zeIvL7/Qq2nOb5uFoxcDLRyD3hLoVcp7AiEAsCLkI5FkN15ZSg0jECWliEFng7sn6mhcPCGpmoXX4E0="}]}, "readme": "", "maintainers": [{"name": "dscape", "email": "<EMAIL>"}]}, "1.0.1": {"name": "cycle", "description": "decycle your json", "author": {"name": "<PERSON><PERSON><PERSON> crockford", "email": "<EMAIL>", "url": "http://www.CROCKFORD.com/"}, "version": "1.0.1", "main": "./cycle.js", "homepage": "https://github.com/douglascrockford/JSON-js", "repository": {"type": "git", "url": "http://github.com/douglascrockford/JSON-js.git"}, "bugs": "http://github.com/douglascrockford/JSON-js/issues", "keywords": ["json", "cycle", "stringify", "parse"], "engines": {"node": ">=0.4.0"}, "_id": "cycle@1.0.1", "readme": "ERROR: No README.md file found!", "_from": "cycle", "dist": {"shasum": "f3215c57574389d2a5aab6868173e74a4efac256", "tarball": "https://registry.npmjs.org/cycle/-/cycle-1.0.1.tgz", "integrity": "sha512-4EUnqN4uFGe4hbe5poYdWXHJBa8v+A+r1Q8NBRvxRqFnW9qXsAEiGPi3xFb50AnGLbjGT1uE2CsHt+wGpjYyIA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICWfITWnMl1dUAj9yK9M0whtddt+nkj72snaPO41/9jqAiEA4DVHEB9kBDe3jT51EeYJCnBJo1HZD8euGsk+VCyyCYs="}]}, "_npmVersion": "1.1.63", "_npmUser": {"name": "dscape", "email": "<EMAIL>"}, "maintainers": [{"name": "dscape", "email": "<EMAIL>"}]}, "1.0.2": {"name": "cycle", "description": "decycle your json", "author": "", "version": "1.0.2", "main": "./cycle.js", "homepage": "https://github.com/douglascrockford/JSON-js", "repository": {"type": "git", "url": "http://github.com/dscape/cycle.git"}, "bugs": "http://github.com/douglascrockford/JSON-js/issues", "keywords": ["json", "cycle", "stringify", "parse"], "engines": {"node": ">=0.4.0"}, "readme": "Fork of https://github.com/douglascrockford/JSON-js, maintained in npm as `cycle`.\n\nOriginal readme follows\n\n# JSON in JavaScript\n\nDouglas Crockford\n<EMAIL>\n\n2010-11-18\n\n\nJSON is a light-weight, language independent, data interchange format.\nSee http://www.JSON.org/\n\nThe files in this collection implement JSON encoders/decoders in JavaScript.\n\nJSON became a built-in feature of JavaScript when the ECMAScript Programming\nLanguage Standard - Fifth Edition was adopted by the ECMA General Assembly\nin December 2009. Most of the files in this collection are for applications\nthat are expected to run in obsolete web browsers. For most purposes, json2.js\nis the best choice.\n\n\njson2.js: This file creates a JSON property in the global object, if there\nisn't already one, setting its value to an object containing a stringify\nmethod and a parse method. The parse method uses the eval method to do the\nparsing, guarding it with several regular expressions to defend against\naccidental code execution hazards. On current browsers, this file does nothing,\nprefering the built-in JSON object.\n\njson.js: This file does everything that json2.js does. It also adds a\ntoJSONString method and a parseJSON method to Object.prototype. Use of this\nfile is not recommended.\n\njson_parse.js: This file contains an alternative JSON parse function that\nuses recursive descent instead of eval.\n\njson_parse_state.js: This files contains an alternative JSON parse function that\nuses a state machine instead of eval.\n\ncycle.js: This file contains two functions, JSON.decycle and JSON.retrocycle,\nwhich make it possible to encode cyclical structures and dags in JSON, and to\nthen recover them. JSONPath is used to represent the links.\nhttp://GOESSNER.net/articles/JsonPath/\n", "_id": "cycle@1.0.2", "dist": {"shasum": "269aca6f1b8d2baeefc8ccbc888b459f322c4e60", "tarball": "https://registry.npmjs.org/cycle/-/cycle-1.0.2.tgz", "integrity": "sha512-byD5qiF53JUs62MoaB0TWthBIapY4kKlTXF187KggBtIia9O0iitwsUMcLKrI8uybx+lQFqLp+6SKMp/rVsRVA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGs/TxbKbLE1va/TOjobhh3HglvNL1k6R192V/+AQZizAiBZ7k4doO3dngOr7xFbX5znyE5KRlGaM/Bal1bYh19Bdw=="}]}, "_npmVersion": "1.1.63", "_npmUser": {"name": "dscape", "email": "<EMAIL>"}, "maintainers": [{"name": "dscape", "email": "<EMAIL>"}]}, "1.0.3": {"name": "cycle", "description": "decycle your json", "author": "", "version": "1.0.3", "main": "./cycle.js", "homepage": "https://github.com/douglascrockford/JSON-js", "repository": {"type": "git", "url": "http://github.com/dscape/cycle.git"}, "bugs": {"url": "http://github.com/douglascrockford/JSON-js/issues"}, "keywords": ["json", "cycle", "stringify", "parse"], "engines": {"node": ">=0.4.0"}, "readme": "Fork of https://github.com/douglascrockford/JSON-js, maintained in npm as `cycle`.\n\n# Contributors\n\n* <PERSON>ford\n* <PERSON>uno Job\n* <PERSON>\n\n# JSON in JavaScript\n\nDouglas Crockford\n<EMAIL>\n\n2010-11-18\n\n\nJSON is a light-weight, language independent, data interchange format.\nSee http://www.JSON.org/\n\nThe files in this collection implement JSON encoders/decoders in JavaScript.\n\nJSON became a built-in feature of JavaScript when the ECMAScript Programming\nLanguage Standard - Fifth Edition was adopted by the ECMA General Assembly\nin December 2009. Most of the files in this collection are for applications\nthat are expected to run in obsolete web browsers. For most purposes, json2.js\nis the best choice.\n\n\njson2.js: This file creates a JSON property in the global object, if there\nisn't already one, setting its value to an object containing a stringify\nmethod and a parse method. The parse method uses the eval method to do the\nparsing, guarding it with several regular expressions to defend against\naccidental code execution hazards. On current browsers, this file does nothing,\nprefering the built-in JSON object.\n\njson.js: This file does everything that json2.js does. It also adds a\ntoJSONString method and a parseJSON method to Object.prototype. Use of this\nfile is not recommended.\n\njson_parse.js: This file contains an alternative JSON parse function that\nuses recursive descent instead of eval.\n\njson_parse_state.js: This files contains an alternative JSON parse function that\nuses a state machine instead of eval.\n\ncycle.js: This file contains two functions, JSON.decycle and JSON.retrocycle,\nwhich make it possible to encode cyclical structures and dags in JSON, and to\nthen recover them. JSONPath is used to represent the links.\nhttp://GOESSNER.net/articles/JsonPath/\n", "readmeFilename": "README.md", "_id": "cycle@1.0.3", "dist": {"shasum": "21e80b2be8580f98b468f379430662b046c34ad2", "tarball": "https://registry.npmjs.org/cycle/-/cycle-1.0.3.tgz", "integrity": "sha512-TVF6svNzeQCOpjCqsy0/CSy8VgObG3wXusJ73xW2GbG5rGx7lC8zxDSURicsXI2UsGdi2L0QNRCi745/wUDvsA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCVMW7QBLa+Pp6pHcOC/his6DoJ39U1nt7P3ahg6uHHfQIgGBMFve4sb82ooCy63/5R8ZJsKUJqkfm+1SetHb8z3nw="}]}, "_from": ".", "_npmVersion": "1.2.32", "_npmUser": {"name": "dscape", "email": "<EMAIL>"}, "maintainers": [{"name": "dscape", "email": "<EMAIL>"}]}}, "readme": "", "maintainers": [{"name": "dscape", "email": "<EMAIL>"}], "time": {"modified": "2022-06-14T06:50:43.260Z", "created": "2012-03-17T16:52:46.599Z", "1.0.0": "2012-03-17T16:52:48.650Z", "1.0.1": "2012-11-25T17:12:00.857Z", "1.0.2": "2013-02-14T18:13:43.868Z", "1.0.3": "2013-12-16T18:21:25.845Z"}, "author": {"name": "<PERSON><PERSON><PERSON> crockford", "email": "<EMAIL>", "url": "http://www.CROCKFORD.com/"}, "repository": {"type": "git", "url": "http://github.com/dscape/cycle.git"}, "users": {"daviddias": true}}