{"_id": "quick-lru", "_rev": "18-7b4c6c167329a41a92510d940b4028f9", "name": "quick-lru", "description": "Simple “Least Recently Used” (LRU) cache", "dist-tags": {"latest": "7.0.0"}, "versions": {"1.0.0": {"name": "quick-lru", "version": "1.0.0", "description": "Simple \"Least Recently Used\" (LRU) cache", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/quick-lru.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && nyc ava"}, "files": ["index.js"], "keywords": ["lru", "quick", "cache", "caching", "least", "recently", "used", "fast", "map", "hash", "buffer"], "devDependencies": {"ava": "*", "coveralls": "^2.12.0", "nyc": "^10.2.0", "xo": "*"}, "gitHead": "2ea4c25ec7b5d4eacd37872c9313bdfae0f31d33", "bugs": {"url": "https://github.com/sindresorhus/quick-lru/issues"}, "homepage": "https://github.com/sindresorhus/quick-lru#readme", "_id": "quick-lru@1.0.0", "_shasum": "7fa80304ab72c1f81cef738739cd47d7cc0c8bff", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.8.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "7fa80304ab72c1f81cef738739cd47d7cc0c8bff", "tarball": "https://registry.npmjs.org/quick-lru/-/quick-lru-1.0.0.tgz", "integrity": "sha512-MzlsvOuRTMen9CoYiDOajyevAChw+VGqjNIDHVRINKWz7iBAZC8F7ZXt6TlHRZ8/mbjysmUYFmWYcsHzKWdhBA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHtijk9zS1IwBDpvc88QFdETEYCv1Ag+VYj3JJk+ev9vAiATsY6BZzUOI8tjz+OcY7AajkKBPS6X0VKhA8qhFnKM0Q=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/quick-lru-1.0.0.tgz_1490864340991_0.10071378503926098"}, "directories": {}}, "1.1.0": {"name": "quick-lru", "version": "1.1.0", "description": "Simple \"Least Recently Used\" (LRU) cache", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/quick-lru.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && nyc ava"}, "files": ["index.js"], "keywords": ["lru", "quick", "cache", "caching", "least", "recently", "used", "fast", "map", "hash", "buffer"], "devDependencies": {"ava": "*", "coveralls": "^2.12.0", "nyc": "^11.0.3", "xo": "*"}, "gitHead": "618c5cdcc591289e58fc02c93830b2dc6162cdfa", "bugs": {"url": "https://github.com/sindresorhus/quick-lru/issues"}, "homepage": "https://github.com/sindresorhus/quick-lru#readme", "_id": "quick-lru@1.1.0", "_shasum": "4360b17c61136ad38078397ff11416e186dcfbb8", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.8.3", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "4360b17c61136ad38078397ff11416e186dcfbb8", "tarball": "https://registry.npmjs.org/quick-lru/-/quick-lru-1.1.0.tgz", "integrity": "sha512-tRS7sTgyxMXtLum8L65daJnHUhfDUgboRdcWW2bR9vBfrj2+O5HSMbQOJfJJjIVSPFqbBCF37FpwWXGitDc5tA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCJVYg22hQVbsDTVA5mMVNYkyxInWDfmDHDbpLFUSbs0wIgd4OlEFh5DwxYvOb8os2jLTEojQwpfhlBQ92n15T301Q="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/quick-lru-1.1.0.tgz_1501163138777_0.7843833630904555"}, "directories": {}}, "2.0.0": {"name": "quick-lru", "version": "2.0.0", "description": "Simple \"Least Recently Used\" (LRU) cache", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/quick-lru.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && nyc ava"}, "keywords": ["lru", "quick", "cache", "caching", "least", "recently", "used", "fast", "map", "hash", "buffer"], "devDependencies": {"ava": "^0.25.0", "coveralls": "^3.0.1", "nyc": "^13.1.0", "xo": "^0.23.0"}, "gitHead": "01f18dde7ffba7991906c96adaff3bdf1dc76e5a", "bugs": {"url": "https://github.com/sindresorhus/quick-lru/issues"}, "homepage": "https://github.com/sindresorhus/quick-lru#readme", "_id": "quick-lru@2.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "8.12.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-DqOtZziv7lDjEyuqyVQacRciAwMCEjTNrLYCHYEIIgjcE/tLEpBF82hiDIwCjRnEL9/hY2GJxA0T8ZvYvVVSSA==", "shasum": "32b017b28d1784631c8ab0a1ed2978e094dbe181", "tarball": "https://registry.npmjs.org/quick-lru/-/quick-lru-2.0.0.tgz", "fileCount": 4, "unpackedSize": 5636, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb6GUKCRA9TVsSAnZWagAAsusQAIOK7Ryr7oNHzwTOeRkc\nRTEEMBFyzfwOISbXkvJ9JZr0P9gchIu+j7RWHGEj/88iaW16rA80Prbt1GYP\nOnX8Y7TIyxqxlWhfvln/j6eLMv4O8EzDsRjblbIFWfz1xfCBA/KD95ZwPNvj\n3lXeFXuU1pyIjFWFghLPE2KRj2IdLkqnNQojOGAqJpyB6tDf0Ik2iHIZC/3+\nSmWU9jPy7LDPuwhvJ9f0sQklsD0nSEoxxWff+88AmZX0MLJB0zK2BgNeUB8x\nWv1udh09r8bbsyINKy0pApIM7Q079hG4NWJuWLVrjpMDiQ4X++RvCsDJ5YbB\nWnte4tsFDmIZcWsyqHxAy/r48YR6KaIn2G5O1MbQcbCgd8bLQtfI7jXXorlD\noAuHmGAf0VaD2f2I3NCSaiOdC+3PBQZpcuSBc6wF9AAglUuOjLoUzKiVtSuG\nrHuZPxJgRhjbeYrQmboChQjVWOcUEWcWzeJwm/AMtWD/QxP1dnCCBNhyMaZq\n+p4eow77qNoyLIFyZyKGgkHm/Xh/bV3iyK1okxPfZa5rVNuPy8iJwpaL5TlW\ncRWkFRT3UVwoQ5Rbz6VTF3LEmb/def3CpbRrjueLrEFqro3V1CMnO3va6AUW\nGeDSnIjRxzAWXh4Oq85NsHO+IdKNeAhz+7Of7YSuJGTh96qzQpAGvmpAMoEJ\nkiUz\r\n=muuJ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICXu+ri15q1EltnMNjMXXGKMMOgw3XId1Dq3ZflWKWwnAiAlawmgA2kXkr/7TbY7QCB3ADXRnaPbo2LLK4DO7LnKoQ=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/quick-lru_2.0.0_1541956873962_0.3470425557929264"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "quick-lru", "version": "3.0.0", "description": "Simple \"Least Recently Used\" (LRU) cache", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/quick-lru.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && nyc ava && tsd-check"}, "keywords": ["lru", "quick", "cache", "caching", "least", "recently", "used", "fast", "map", "hash", "buffer"], "devDependencies": {"ava": "^1.3.1", "coveralls": "^3.0.1", "nyc": "^13.1.0", "tsd-check": "^0.3.0", "xo": "^0.24.0"}, "gitHead": "c206b5c8e9d6eddba8188a3e741c3a7eb521eea5", "bugs": {"url": "https://github.com/sindresorhus/quick-lru/issues"}, "homepage": "https://github.com/sindresorhus/quick-lru#readme", "_id": "quick-lru@3.0.0", "_nodeVersion": "10.15.1", "_npmVersion": "6.8.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-WLzHLB/msD8Q/5jNh4LviurG896aIEJRS1M3gHz91nSmaXn8q+/q4yOQvNMIETl9f5pAu3igqexB62ARWOUoOg==", "shasum": "2f96145481567e5bc442c6c4a66e86158405c696", "tarball": "https://registry.npmjs.org/quick-lru/-/quick-lru-3.0.0.tgz", "fileCount": 5, "unpackedSize": 7395, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJchRQDCRA9TVsSAnZWagAAnFIP/3Q3Rcnm6jDi4HGP47lg\nHFleI2YSyoL5FbI6oSyv6cEBaWZf5mzsNac0AuftIvKoPBmu7iQHRLVh44mK\nltsP4TgHbtR/ewYP52/a8QrSZEBNe+MYTJ/85znEM2Av3YFh0dkTIbViXOxy\neyqregn8NXdEjsLaaISq5PY4Xf0z0aCBzvW+9Kqf4oRMJ7Tg8hkHAszBHVJb\nYY+au4zeXMav/2OkZ/DwijoMopcN9osXAaE3vrJWJUAZfxoIo18ehyr/UStU\nH41JzfcjwJNcrRqSs+hJsyyLNt/4NZA8U3XxIUuNbxfzeQWRN0/IUvLVCE7l\nJz1TyMY/4J2ZSgYetYe8uJv5tGkDi/T8FnCHwqLphgLho8GM09e5wWs3Fh6N\nnaymmzIFRLQqOTtKmzPQX1lQXQ+7vQn2ipryUYR+hU12zKCuMdV3y0m872jT\nefZUwBNdwMW9bLRk73rZjkqEDHQh4WNckIAK89mb44vmBY7+5/9NOT+tLvoX\naAe2oqWRFf18KrzIveDRLlsNiYoAoY0VMzEg4pDTB60lcXVdXpMGrPjJLvE5\nNs0ndnRJ2dLZS3PT7v/c6sTWSchYqJRVZeJfNzn7c83bggf7Xn+9mJz65NoO\nbKAIRudlQ/iz9t0gL1jAQoExsUE9yMUchh4UlZb18g+nVSydkxfYS3G7ocpX\nEFjh\r\n=ESNF\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCN9rOuGFSE7/6io2FsOQevGrwbxzUSEgcjjJY49DVJqQIgQHzhGG+dSxdzxMTO7K1GxF19SwIl0P86JSCb3bNoS5A="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/quick-lru_3.0.0_1552225282235_0.7626934676744992"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "quick-lru", "version": "4.0.0", "description": "Simple \"Least Recently Used\" (LRU) cache", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/quick-lru.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && nyc ava && tsd"}, "keywords": ["lru", "quick", "cache", "caching", "least", "recently", "used", "fast", "map", "hash", "buffer"], "devDependencies": {"ava": "^1.4.1", "coveralls": "^3.0.3", "nyc": "^13.3.0", "tsd": "^0.7.2", "xo": "^0.24.0"}, "gitHead": "4e484ef5202ffc07977cd4ae771ceb6fa253ff61", "bugs": {"url": "https://github.com/sindresorhus/quick-lru/issues"}, "homepage": "https://github.com/sindresorhus/quick-lru#readme", "_id": "quick-lru@4.0.0", "_nodeVersion": "8.15.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-5cS39FEMrySKt/8c66v10HrmoexP2iYOsJBhtbVrlAr6Cbuc6khFMN8CHJG87c+QsdxBABivfVscgk20I/rPDw==", "shasum": "a44d44010a776d787af65b1226566dd1ae7b9649", "tarball": "https://registry.npmjs.org/quick-lru/-/quick-lru-4.0.0.tgz", "fileCount": 5, "unpackedSize": 7503, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpwDsCRA9TVsSAnZWagAA5WMP/2doQ1zTz99qHRYcwXDX\nPmg6tFIwaRpb04EpQ31evlOKAEwr4f/3ETQY6D0xD2bCei5IUkXSkHPXN+AZ\nl4P+SMS3KH/Bl5z6ksAivF0dwe/jGy2JrNqbnJ0gwz+to+NBLnfzyT77Xjml\nGjEvHQu8rmwuZDatCUruN8p4cbnev5N9g+rnFkpRD2/Li6uuKaD/1GzEfmFf\nVU5YD3HUBgmQrtCmt9xh9dH5yhCw+KCvSi+xqDtPHpttp/HgMcvJzDmEQo7E\n0zF+0xRHYXG55umZETtgIeDccaJF+xtcBHnL7M+WRDJag74Q3ovsrv/qsC6S\nZDStDXvbd7+8hT+21uc46dRwgoLF/8MLUJZkNdJ/wgp0D+SAquwP1nSnWzNS\ncBWr9z1GN4iBi6khiBz03hM/Ix+KOsP4Jzc0DV+tKogXrH2YdzxksXzrg16o\nBN5dOaJ1O+KaNYnRNgewXjDFDvoq6/E4YSUlGDk9kRTdPyU5KjhqlIid87eA\nYO1QFiDj9R6X8O3vG8J0cp57hQG//oXkMOqW57ngEviRy/JTDwqzdUGldh6T\ntaoO36lXzHFPtF7M+zXbe1gadEOnZ27qs8CVScOa/kMsl0l/Z7zlLwRHVd8w\n0OOcfZFB9Gc1d90d3ByimePm/sM0qSu/wLDofWxQ2mUsAIpd+4IpeBLzhSfd\n621s\r\n=0kTf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICVcHdX0mCxC5s+f06B5SOv66V93HBwDP0931F/rVxMfAiAoS5q0cI0/o9ulnn1Jzat5KAiR8xUv+50lw9xp0pP7iA=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/quick-lru_4.0.0_1554448619367_0.9515006929966581"}, "_hasShrinkwrap": false}, "4.0.1": {"name": "quick-lru", "version": "4.0.1", "description": "Simple \"Least Recently Used\" (LRU) cache", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/quick-lru.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && nyc ava && tsd"}, "keywords": ["lru", "quick", "cache", "caching", "least", "recently", "used", "fast", "map", "hash", "buffer"], "devDependencies": {"ava": "^1.4.1", "coveralls": "^3.0.3", "nyc": "^14.1.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "gitHead": "64c21e6e8898d2f2751357c422ae53c63ac66aff", "bugs": {"url": "https://github.com/sindresorhus/quick-lru/issues"}, "homepage": "https://github.com/sindresorhus/quick-lru#readme", "_id": "quick-lru@4.0.1", "_nodeVersion": "8.16.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-ARhCpm70fzdcvNQfPoy49IaanKkTlRWF2JMzqhcJbhSFRZv7nPTvZJdcY7301IPmvW+/p0RgIWnQDLJxifsQ7g==", "shasum": "5b8878f113a58217848c6482026c73e1ba57727f", "tarball": "https://registry.npmjs.org/quick-lru/-/quick-lru-4.0.1.tgz", "fileCount": 5, "unpackedSize": 7469, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc7r+bCRA9TVsSAnZWagAAQx4P/2Nu8oQyT6aWSMNbQXQs\nU8IThgCSLVFHOX3tIqlVhlLc8Zr4+jtY/xXjXSdlAHtPpRzTJNCsZMNU5T8V\n1YS5EMLs+zJbiJLdcbZ+yBI/3eNKxcT5bsWkAS1yLPgsrx6O0ypv4+vxKgV4\nvcST7nniuqi2mhE5aR/g3XP1+vJOR5b8uu4BCQGlL1wrO0oZHSXNfeY5uNsD\n/Tzyr8e+nWPJbo50fTMEm1xbGju4vqf4sgMhSYkIxymfWRuTPdxmnNH/JWU0\nYMH97BXghmhJ5ej3kTev3YkQSnVnAdz7c8bNhE+erbclez/WP65QAQyRLmvR\nJEMlvNd0O6ZOlb2aehzdsjl8nqsH+bL2N//g0ZFPEFGGUe4L+G+rIrv7O6U9\nLo+GpXi0gI/FECODJzsq/05otrIG0lbkVIZY1OH3DM8gQMlnGn1/hf8Hm9Ms\nj863/ckwYYrOza0BZB/hVAz9bMyQV5TMQ6mXINOCFVD+mzBVD3VEA7m58SmG\nFA5hjJ3Zb46nypJtihcqSqyGkSaoQYdXrVjMg01ocavM7dOefnJN2KbGUlwD\nT/rVI9N/ZU+fILQIwi1MxpiY7Eyryr6y6zPvBfbzb4jsYfAkaX8XxBwLEKP6\nl1FDTzMQMxnMzomtF6YxlSUgohoioaV9xWSa0pElsEMBcijlI3SqZfu2NTQq\nTFDL\r\n=Sy0n\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDow97hIbBBcQZqBqEWiPJaxL7Jfb3DerA17k7b412eEAiAXSPKHMRh0ZNJwa/ALZRD/DxRQ+kzPCvRPte0uoEzJ4g=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/quick-lru_4.0.1_1559150490405_0.9876888152612853"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "quick-lru", "version": "5.0.0", "description": "Simple “Least Recently Used” (LRU) cache", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/quick-lru.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && nyc ava && tsd"}, "keywords": ["lru", "quick", "cache", "caching", "least", "recently", "used", "fast", "map", "hash", "buffer"], "devDependencies": {"ava": "^2.0.0", "coveralls": "^3.0.3", "nyc": "^15.0.0", "tsd": "^0.11.0", "xo": "^0.26.0"}, "gitHead": "b0feed4ec77456836cc2c6934801fe29f69d2b7f", "bugs": {"url": "https://github.com/sindresorhus/quick-lru/issues"}, "homepage": "https://github.com/sindresorhus/quick-lru#readme", "_id": "quick-lru@5.0.0", "_nodeVersion": "10.18.1", "_npmVersion": "6.13.4", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-iNjBLSxM3byM7z8a1MB/Oa+AAeopX4NqKEj/ykgKRi1R4AFXNINR4DSjyjDzXzjqjN9+OBQGfK5w5CAnCtV7jg==", "shasum": "a2b007dd2335408a2348735bfc3aa42af605d37e", "tarball": "https://registry.npmjs.org/quick-lru/-/quick-lru-5.0.0.tgz", "fileCount": 5, "unpackedSize": 7963, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeRUxyCRA9TVsSAnZWagAAA58QAKAT6K91Hy5FC0vIZxEU\nBx14C5OBH5Quf/pYONIGWCDQiNT7De2uQmjbhlBs9uO8+f+5o3aBq5+1ecKS\nExnuvvbeKYzC5tAjinEP2ajFG28BgsirL1ebNjrn5qOdPOaxb5K9ZSOkv8Cj\nQuxBbfQjnc/Wl5qt+xPWuUaP6AZiHXjOhNt88FDIrf+EhzOd2qO//snvSoN+\nK6c0l1Cn8Z/UaFtq5Af+PUM8iCMD/aer0EWaUKBNtOkdQN//PHF4bIiyxgU8\ng/WnfdjTrllxygDK24OUPw12kOio7Qtea9d8ZUby8RQWrJm/0Pw4MQIpuQti\nLAb8VC81/EfbeWdmmxuiZ7l7LPVu2PDfWcr7HZIPCyJeZgTRvjScB5PwuW2W\nfuyGFwtwmSBlgABPxos+zSYn/Y6fy6I/B0fFrZMuClDsyPDEFfCPcHY89MYi\nWW/A0kp0nHMIuCooJjx+96BAGjdhqCSQQ31p0dFvUXprw7Rd/VpgeA2glEnZ\n6/wWOaohgTovkfRFhG/5+rjrcbvwz7vTD1rvfs40DomNu+35TeOe5ZTgrYcs\n7Q30o0NnxxNDnxOqvb2+2wHtGtlRQFW2QepKsGrIfAUjZiUva5Ed8OokArxF\nPeIBM4uuBwX20TX4dvyj5+96NSJoiL9C0LogecdZy2TtqlpTg0Gi0l1652zZ\nEHbz\r\n=G0Xr\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAb0G6RdNPP3nmnnBEZm558pq8naQ33ZXS3/C916Q1PLAiAWJIc6PsQb3quse1aZkv+OThSKMcA6E52wFeaH33eJew=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/quick-lru_5.0.0_1581599857888_0.43905769021974583"}, "_hasShrinkwrap": false}, "5.1.0": {"name": "quick-lru", "version": "5.1.0", "description": "Simple “Least Recently Used” (LRU) cache", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/quick-lru.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && nyc ava && tsd"}, "keywords": ["lru", "quick", "cache", "caching", "least", "recently", "used", "fast", "map", "hash", "buffer"], "devDependencies": {"ava": "^2.0.0", "coveralls": "^3.0.3", "nyc": "^15.0.0", "tsd": "^0.11.0", "xo": "^0.26.0"}, "gitHead": "c205876c128de9b34be24ef5189c3194bcbcee23", "bugs": {"url": "https://github.com/sindresorhus/quick-lru/issues"}, "homepage": "https://github.com/sindresorhus/quick-lru#readme", "_id": "quick-lru@5.1.0", "_nodeVersion": "10.19.0", "_npmVersion": "6.13.4", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-WjAKQ9ORzvqjLijJXiXWqc3Gcs1ivoxCj6KJmEjoWBE6OtHwuaDLSAUqGHALUiid7A1KqGqsSHZs8prxF5xxAQ==", "shasum": "1602f339bde554c4dace47880227ec9c2869f2e8", "tarball": "https://registry.npmjs.org/quick-lru/-/quick-lru-5.1.0.tgz", "fileCount": 5, "unpackedSize": 8625, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeiuDWCRA9TVsSAnZWagAAnSAP/R9/dvrW0C4UIlSIMomV\n1P5FwJcPecxSwouX8YCqUu75uucoCXwSIhMkUyeMRF8g3tXl7D3TYrPRVfhe\nHYa2WhrSgr6ABLmizJ8UQYe6aBVyPt7OKotcmwkViIuraetB2olJEQEHAGXH\n5++rRn0WvvLpjngMyOqfDqo8EkULRZSPGM1i9wjYcFbN2B78O5UzUwcv6/aK\nOaipu0idmFwH2ua8ilmCPE6DgNVa/PI8UkuuUSmV1jTheyBcwSGqSHlzgdnz\nJF1C0Jb0MWZh1xj/MZ74w6Wlu6SP/m6O84OuM6EvNZ3+1Nf5ftAaMNXdK+Fh\nMCyeECYXW48P1WL36lH6mCDBdVGdRmvk3hYf0d/tCoQpHu3ib1L347YSgUBA\nUe2gIzncxXP2fv9pX5gLWlZcXIdLtoBAC0bhdBsljpWUJJKtNlYiUjxJ0Rob\nTPTzioyWyqfv4qG18MYjQjxZjIa7+EoO/UiGG++go+ch6G9tIq0kaE7JWlBQ\nDnMn2fUDtByZ82KUeW0LcKvFt35LNSk744Xjls8GVNIkAnYNAhXC6aUrj742\nSAMlN+6TlZaoqkTKrirsU97eZN463jC+684DoDjs/zJ+V8FbKb061ZnZWkB2\nLTa2T5GHpKgrCm6BG63EsrQ8c0mbWvAOC3ZyFi6CH7T+RKyJY6l4t+kDQJ61\nojDe\r\n=3QYT\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDBmS42jujvyMPnseZlBKX2D00xWdRojQ2VV3+Ajp70kAIhAJsIgazYBNMtRXCbu5wuDSaInyL1alR+gNpVquwUfwtB"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/quick-lru_5.1.0_1586159830047_0.5906710401908553"}, "_hasShrinkwrap": false}, "5.1.1": {"name": "quick-lru", "version": "5.1.1", "description": "Simple “Least Recently Used” (LRU) cache", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/quick-lru.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && nyc ava && tsd"}, "keywords": ["lru", "quick", "cache", "caching", "least", "recently", "used", "fast", "map", "hash", "buffer"], "devDependencies": {"ava": "^2.0.0", "coveralls": "^3.0.3", "nyc": "^15.0.0", "tsd": "^0.11.0", "xo": "^0.26.0"}, "gitHead": "64915f7a4ce7c0cf7aec1cc6320c050f1c4bddd1", "bugs": {"url": "https://github.com/sindresorhus/quick-lru/issues"}, "homepage": "https://github.com/sindresorhus/quick-lru#readme", "_id": "quick-lru@5.1.1", "_nodeVersion": "10.20.1", "_npmVersion": "6.14.5", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-WuyALRjWPDGtt/wzJiadO5AXY+8hZ80hVpe6MyivgraREW751X3SbhRvG3eLKOYN+8VEvqLcf3wdnt44Z4S4SA==", "shasum": "366493e6b3e42a3a6885e2e99d18f80fb7a8c932", "tarball": "https://registry.npmjs.org/quick-lru/-/quick-lru-5.1.1.tgz", "fileCount": 5, "unpackedSize": 8645, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe1P6CCRA9TVsSAnZWagAA80oP/A8WECPR5ihBf9mMDo5O\nBQLFLT8kIYxT7KaKzh3eju70yQ/dW3aq4qqFmTfExIJFW1iKyCtlOs5kNGmN\nKaZ5MI53nRogiTFHn3JVa6pJtL4cWjN4jYEG4VllykhuEsseb6ejcbFRwGMP\no0FnWpEr4fGW8zkDuaycBmWIgXgsdpPqSC0VuSxW+WIt/AfJbxbBEoWIlmgC\niZtYtdkbq1Ouz8pG+3GuR7/Wtou65IjSOrs4d8VB5nhDgjdhVyue1pO9TeEu\ndiIqj8tpbgPvU25odaHZIjm6uSmUqzwzQUPNBO1OEEbiSSnBYi0b/ABSBHsc\nt4bYiTS1ivnldxcekc6T9kfqb66QKYstHKED+uS9Q/cg6z8Qdc9AtA0Gw7BX\nfxwPdwRLbJrUsU5KCNTpvG9tskz5CtUHy4LdteNJGYMKrUx1EZ/EdKQyEbWi\njK9DntuqVrIAXKWvUDpYLnryGj7GFBSwgNShOw5RTfZMojlpdsZBTwFGv1R6\n7D4WotZv8pW/WrE11jEk3FeHQx+v4PMwyR/KogWwnL1ywU1M9TytOgIxKlvv\nZMaqyHwfNvNkHG5vwsFWpm99ThV9YVQNISQkt3tWrxYN0NxfqR+Ez3jF5pgP\nswrks+7RQD33dqpE0kIlVJGnr9cZO+yUWlS6PB1a0v9v+pg9hQ0nLzdcdB6k\n4vjv\r\n=MepF\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID80crA+KHwceeL++pmCGswkxuX2IDV6Y2R8zGnJZffJAiEA82n+jJ/uV/T1Kh2RhLhkYbFDl5CSYmLretNIldTE2BM="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/quick-lru_5.1.1_1591017089902_0.4589325163276523"}, "_hasShrinkwrap": false}, "6.0.0": {"name": "quick-lru", "version": "6.0.0", "description": "Simple “Least Recently Used” (LRU) cache", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/quick-lru.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"//test": "xo && nyc ava && tsd", "test": "xo && ava && tsd"}, "keywords": ["lru", "quick", "cache", "caching", "least", "recently", "used", "fast", "map", "hash", "buffer"], "devDependencies": {"ava": "^3.15.0", "nyc": "^15.1.0", "tsd": "^0.14.0", "xo": "^0.37.1"}, "nyc": {"reporter": ["text", "lcov"]}, "gitHead": "1a51a3eb9e32f14357cef3d3ccbf38d08cda02d2", "bugs": {"url": "https://github.com/sindresorhus/quick-lru/issues"}, "homepage": "https://github.com/sindresorhus/quick-lru#readme", "_id": "quick-lru@6.0.0", "_nodeVersion": "14.15.1", "_npmVersion": "6.14.10", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-kQ3ACYYIALMi7x8xvMU+qSF6N6baMZms8/q3dn2XFiORhFrLbpR6vTRNGR80HwlakWSoY4RnGoCJWUgnyH6zZQ==", "shasum": "c5a245a773c702694870828e5f09b28f7af087db", "tarball": "https://registry.npmjs.org/quick-lru/-/quick-lru-6.0.0.tgz", "fileCount": 5, "unpackedSize": 14276, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf//4KCRA9TVsSAnZWagAAODUP/iB/qX/YR5jl89Y3I50x\ntMUuERP0jxJmwSPZRAK5aWHdj4srDMzTJd0zkJ8XANCZUoxsj51SMKjVH37A\nANRfuhsTIxKKPOzX+G1Ni5NnA1MrcATsmSPU8Qlqh6p3WCyilqW/xACxciLV\nTK/GDRYMoD0hSYlxPEWY0Wo3IsfjvqR6LpEkmzAYlujFDYLIFIdsEHxSLRLi\nY9bfK+mJYzhR6PQK7AizgkZmGuFxC1bFJbW+t/ixZwsdZgcXaPenM6XfXHRS\nemrhlZUv+4/gOtHw5JzKsEerKDWpAgZS/NROmctD8ki+oCRhaZzaSlCSa3V0\nTDICT4uuIM2Mqw0KfJ2bnMiS6wxZnDLhc3TrhE2kQRwSJ6gMYtBYkPwEWSvT\n/9KISvW8xwpJZI1fV5Hh06ywKoOQMYPjgq0qV72ZfRwI+G2izBYRBngVNiLD\ndsybHpsmNxzRD25NYcEx3CQuNKKVJbwRSvKv7+7RvmP9ph9F8vgyr+qFQMqp\nzfikoS7OPfyH/FACwdS0qYOHTP82USIjIjukstdtVFjxTJ4KPwtlAxHdo/Ye\nfSPGbL5NMAt8ui0LdVN8lx0ZcH2OFnhJfV51VNw4t0IsBvP5/eF8gG5DEeMn\ngi0TAUwn9Lk6VKLYHPS+RRHQ6aDD1LcGhTIt0mJwgs+OD8xmI6X/vCEPTI0Q\nznAy\r\n=8+M+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGbkqp56cckJNE/dwG8v3FjJdor52dODhzHvVR6rDG5fAiEAtRArTlBrWYkwfw/ZCLS0N1w6Tw9cx9wt5hpBeiQPmCs="}]}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/quick-lru_6.0.0_1610612233916_0.5484875996721288"}, "_hasShrinkwrap": false}, "6.0.1": {"name": "quick-lru", "version": "6.0.1", "description": "Simple “Least Recently Used” (LRU) cache", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/quick-lru.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"//test": "xo && nyc ava && tsd", "test": "xo && ava && tsd"}, "keywords": ["lru", "quick", "cache", "caching", "least", "recently", "used", "fast", "map", "hash", "buffer"], "devDependencies": {"ava": "^3.15.0", "nyc": "^15.1.0", "tsd": "^0.14.0", "xo": "^0.37.1"}, "nyc": {"reporter": ["text", "lcov"]}, "gitHead": "6ee4b6e9a7391f4536324ea1df909839d5d2734d", "bugs": {"url": "https://github.com/sindresorhus/quick-lru/issues"}, "homepage": "https://github.com/sindresorhus/quick-lru#readme", "_id": "quick-lru@6.0.1", "_nodeVersion": "12.22.1", "_npmVersion": "7.10.0", "dist": {"integrity": "sha512-IE0DNgU7LWaVHB3KqPwWwZ9QiBK9HqZ1Au1uOcLkoosOzehlhOq/v1LA3TUUSzjA98CCOfhug74rWb0L7sGwdw==", "shasum": "3a3fabc74960d663222d04957634766d3447c562", "tarball": "https://registry.npmjs.org/quick-lru/-/quick-lru-6.0.1.tgz", "fileCount": 5, "unpackedSize": 14280, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhEWCaCRA9TVsSAnZWagAA1CUP/RbSE5/1qWBhZ4bjCABy\nlQJbdOLZvcGTFqS5/uzuJ3A6EW61TIHRuZ2Eh0CY77s7qaGj9daEehdZ5xaB\npFDvvXui70cRylldT2nSvM6PIx56Zrr2MMm6hHQNKzz3TQMY5in5vIE1PSpx\nkHTNcAPA2QW4aEV25q6VoUq17A/ModKiFonRvorzrzRsGKfmZtrkGou30uL7\nzkrYL7X/xABNsUkRmW4eIZmBQ94fzoBdSG5qN8vsiwu8KeLOUdXQciPYcZKg\nSD+5qpCRgbNNaKRsbX1JnOQ5I7wtN5kekgcv0JeaPbgiEdNZ8crXcNxhPVnz\nWTETtz4aLaiaOX1suTtEB5LFOvLmkOOWjm0LGOPrE+qpXTN2EI381tvPkV39\nfaIGBs0HrJdq+tvkeS3o9+v2m2Nq875H+MxiogtUgpO2CRPRe5kzE5z6aPHk\nqdoJTbcsLisluMPaX9EzU0h5otxFTCWFS5bGx2LvmvOF4iHwvGgodfPXj+aB\n3rrZEAheHR8GYWFNxUrLzIhBs1XAyhXKnvQ7Bh6B5IRp4eMbm7ZxmJ6K2Ude\nROmWX7P5QtO5IC/+xDjXNnwQLh7d3VyLV+SDM6vO9sCXuYuejvrXJhpeBqmE\nQVZ3HFrr4ssCyyldwwN9juYo3IBzIAMIaXyxhgrK+ns6Dr2BKZEwOmGyZvhh\nA5Bs\r\n=q+fe\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDZohUdu8UYJgmSloLo13b5w6TtKsyTWhfi/5VnsG09VAIhAIgJJuwa5xD4Npl/uq0RNZD4mZXPBrEl+q6rd5f708iK"}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/quick-lru_6.0.1_1628528794361_0.06180127485580211"}, "_hasShrinkwrap": false}, "6.0.2": {"name": "quick-lru", "version": "6.0.2", "description": "Simple “Least Recently Used” (LRU) cache", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/quick-lru.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"//test": "xo && nyc ava && tsd", "test": "xo && ava && tsd"}, "keywords": ["lru", "quick", "cache", "caching", "least", "recently", "used", "fast", "map", "hash", "buffer"], "devDependencies": {"ava": "^3.15.0", "nyc": "^15.1.0", "tsd": "^0.14.0", "xo": "^0.37.1"}, "nyc": {"reporter": ["text", "lcov"]}, "types": "./index.d.ts", "gitHead": "cf1bbcf9a92cb972b5b4684a67b516c0c1a668fc", "bugs": {"url": "https://github.com/sindresorhus/quick-lru/issues"}, "homepage": "https://github.com/sindresorhus/quick-lru#readme", "_id": "quick-lru@6.0.2", "_nodeVersion": "17.0.1", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-H5ZVbbMzZEl+wEwiMP3v/b7ji+GrM3MRiy62LJbpI+F5+9RNcSKrjz7T0jIJVrlMfMxr7ZG0EGswJiABt75LDg==", "shasum": "3282ffba92717532e8c9c6aea9537a7f0859164b", "tarball": "https://registry.npmjs.org/quick-lru/-/quick-lru-6.0.2.tgz", "fileCount": 5, "unpackedSize": 14325, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhmfCXCRA9TVsSAnZWagAAF1IP/ivNC4pdEkJk2V0SGN6I\nIg3o1cPKWNPzrnmGlzkrIwPXOCo364SuwcP/AeTtmYVIbpV90LB0a9xQ6Bm/\nrHAcpIepD7M546uqIvH2PzaDrjXNWLHUvHwfW9wPiqbaxGCrtaDk7OVK1RIu\nmWrC8OVFSvy7mgjJ82zlIkDZJ5jcjU1WaoH5LOhEJtnwLzf/2xYYrXVxTvx4\nwwbRgX99ZWYnO3PVIfPfGhaBNvi1Khm3VfTtlhxCCrwIF75k8NR3mHb4bVqd\ndoEtvqaXX5Db9BaL9hNwmK7zUsQvxBlzEeo2UERkxgt1+xUC+hxrLKOqfE0I\nyd4lF51lImlE4vUP7Ht/BST+2QyXhH3EgYI8/G29v7ofyp0gjrv6lXka6BZE\n5euiClMI13LC5ehhvnV5wbTX66SAa/g/CZuFbMq0KKjEvOLLcXN+PJabvsy9\n0udMcGaCExOUYuWj4y2urO4cp/ImUCoNzzuBLVhVHHYaR+wMILY1CvvbAoXz\nYaomriMDR8K73i17G4UVXRnOvM0cqeO2F588GlVrPjXFtf8i9W0XI50sD341\nqKcnFrT0DQrrX5yX1IrqOSLsj8V16St5PRhOBSFyM1GB+8Q/VyBR9xXfKFWh\nsHLYv+EHcC8jS1ApjJB5z2G8rlxwQQHyFMjs7OXuefsmXdq7Zo7PPKJJEYou\nXdhN\r\n=hFYt\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCMCdd3DdTIbWnGTye8STae7bhw1Z3++MBO61NSxKx1WQIgDm47PGWz5dHYVps+5Sv/9oG02Cu3DtClRvcFkhF+jYs="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/quick-lru_6.0.2_1637478551246_0.10431457352718732"}, "_hasShrinkwrap": false}, "6.1.0": {"name": "quick-lru", "version": "6.1.0", "description": "Simple “Least Recently Used” (LRU) cache", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/quick-lru.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"//test": "xo && nyc ava && tsd", "test": "xo && ava && tsd"}, "keywords": ["lru", "quick", "cache", "caching", "least", "recently", "used", "fast", "map", "hash", "buffer"], "devDependencies": {"ava": "^3.15.0", "nyc": "^15.1.0", "tsd": "^0.14.0", "xo": "^0.37.1"}, "nyc": {"reporter": ["text", "lcov"]}, "types": "./index.d.ts", "gitHead": "b93929f39c93598d8832f37eddcfc11ea0d66c8b", "bugs": {"url": "https://github.com/sindresorhus/quick-lru/issues"}, "homepage": "https://github.com/sindresorhus/quick-lru#readme", "_id": "quick-lru@6.1.0", "_nodeVersion": "17.5.0", "_npmVersion": "8.3.2", "dist": {"integrity": "sha512-8HdyR8c0jNVWbYrhUWs9Tg/qAAHgjuJoOIX+mP3eIhgqPO9ytMRURCEFTkOxaHLLsEXo0Cm+bXO5ULuGez+45g==", "shasum": "2a431d0cf85f55482d314647e4e9d93b21cbc8a9", "tarball": "https://registry.npmjs.org/quick-lru/-/quick-lru-6.1.0.tgz", "fileCount": 5, "unpackedSize": 15222, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiD7vNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpfVA//bIUp5sUuvXK4L6g6kT2glAKYUPuO1GV/F/pMWlfJ325W24UJ\r\ncaXiipSAEVOl6p++SMBXl7IL2dJhDBLJOtTOMIwmkZDzqWXwsnN/aJS0Jv9z\r\n6zBHCUiMP8v3rCY/mVBAIIzS34jfP+2QUuG3dgMD4rSw38lw8cbQGQJSNTca\r\nr0ERI1vZq4gemVI9I10E3PGzPQQLqZxpR+u1xX/1Sflq7zzI/N5XboXT71CX\r\nxgRXC319NGl1E+yJI4Y0HjQ8buRe5wtzpwGTX5M+ykhRZ/wpJV8zXrC6lT7p\r\nPi2EfLp2gFmn7lg2J2QhE0Qfc7ddmFAJsnXBQCMDQ1JZ0nUDg+qSNfld93dI\r\nzCG9PbQWlNLu23wGUo2D7IXWUx/wKLcup5G7lU9jlbwvzbjGiYozzdZ2WSrQ\r\nK6gBhK74PYmcg8njPxj0u7sB/w0NhuSVEfPG8rrUDy90zrYVbQlLw4dCdm8V\r\nD3hkmgZoPK4IpzcG1UwyIjvWW5gcPyn7+e2EQVNueMYtDxKeot0tlM1TxEfR\r\ndkY4MGZ/FiVzVYDTu8t+oc8qpvKV2Ry27OBlaR3qioEv2jfGSEaKgMswP5UR\r\n4yIkz04sL0Qkj8LeirQm/iE1DpQ6YlKNiBoxqIo9NggmqUVxGVo+yVlRfchK\r\nCv8VbExXO9QJPEROryg4JbOpXvKPhwBmP4s=\r\n=lcKT\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFlEBtIHaSgVSfJU6tLn1tNkxawlN65FIUIgdPBbfGGYAiEAnYSpKdnEZYDBEqgSHtNmXuub5Qm0S/r64QTypp9HouE="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/quick-lru_6.1.0_1645198284884_0.8799832205764686"}, "_hasShrinkwrap": false}, "6.1.1": {"name": "quick-lru", "version": "6.1.1", "description": "Simple “Least Recently Used” (LRU) cache", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/quick-lru.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"//test": "xo && nyc ava && tsd", "test": "xo && ava && tsd"}, "keywords": ["lru", "quick", "cache", "caching", "least", "recently", "used", "fast", "map", "hash", "buffer"], "devDependencies": {"ava": "^3.15.0", "nyc": "^15.1.0", "tsd": "^0.14.0", "xo": "^0.37.1"}, "nyc": {"reporter": ["text", "lcov"]}, "types": "./index.d.ts", "gitHead": "771392878fc0e2325b1172d04260e87afe94c8f7", "bugs": {"url": "https://github.com/sindresorhus/quick-lru/issues"}, "homepage": "https://github.com/sindresorhus/quick-lru#readme", "_id": "quick-lru@6.1.1", "_nodeVersion": "16.14.0", "_npmVersion": "8.3.2", "dist": {"integrity": "sha512-S27GBT+F0NTRiehtbrgaSE1idUAJ5bX8dPAQTdylEyNlrdcH5X4Lz7Edz3DYzecbsCluD5zO8ZNEe04z3D3u6Q==", "shasum": "f8e5bf9010376c126c80c1a62827a526c0e60adf", "tarball": "https://registry.npmjs.org/quick-lru/-/quick-lru-6.1.1.tgz", "fileCount": 5, "unpackedSize": 15226, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiPy7rACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoojg//dT1oijG0PTgFFfjNFa+FgrOkaldeU1DD2xkWBZas2SwwKcQ4\r\nX7dsLXDunwf689oM5uliqcD12g9PRi5L/kJ/yqayh18UHUH3FriT1F7sSZb/\r\nQi8ByWW3wBMBFDRuT1vVmfxFq4zNG99T00CdAULJvclNdCxPxQeK2W2bQoJS\r\n8yshGW97YXesSKFYizFLDbRx5V50IQxBpfkUvcZIsb9XotHIos8o+GgdsEZh\r\nN55D9UsAbKzWMtHD8xxdXkBU/I8mIa4Kc982AELiE1A68kWbAhuBBtvYxk+J\r\n7J+gijdERqk1MdrmQ8J42SHlPulRmNKxglKV09Rbvdk17lJK5WLl0ZRVhjdc\r\noE0qmwVs5JCPKKfW9F8dT3buv2pnaOiW7kEBwETB1D4POJYxUwqmjhmgV2nT\r\n8ZUzf37M3VnnXGOBiaGuWBlUBGGlmo3uC+NS9a6ihOKcIZ5KbC053x6be2Lx\r\nzs6jbmNddysDPIZJpjok2KJAAh7/OoEg9uu++QTMcheYUN/0JqcF38B+LyS4\r\ndUkPNbeHFnenrKbZ4I0doigt052lH7R8Uzho0sDXGbSpHixXB2OW6wt8Fo1e\r\nL71xXtPwANKIVG9awiOFhe2MfdcnNjZfOUsoSZFIXTW2iy8C9UGVc1qLbx5W\r\n7G2HyF6kYGd28XVvjWLw4zBJ/rKw6ddBWPE=\r\n=9lL3\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEqND0ZDUsL95nVUJbQqLuELrX2LWAqpQM4lnYhCdNVtAiEAzLrR+0b7Emx0xvg1amSrY9AAMKZZCuCpU0TccAyOnkg="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/quick-lru_6.1.1_1648307946784_0.9757007166465537"}, "_hasShrinkwrap": false}, "6.1.2": {"name": "quick-lru", "version": "6.1.2", "description": "Simple “Least Recently Used” (LRU) cache", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/quick-lru.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"//test": "xo && nyc ava && tsd", "test": "xo && ava"}, "keywords": ["lru", "quick", "cache", "caching", "least", "recently", "used", "fast", "map", "hash", "buffer"], "devDependencies": {"ava": "^3.15.0", "nyc": "^15.1.0", "tsd": "^0.14.0", "xo": "^0.37.1"}, "nyc": {"reporter": ["text", "lcov"]}, "types": "./index.d.ts", "gitHead": "b552634391f442540ccec95646d26df03529eb05", "bugs": {"url": "https://github.com/sindresorhus/quick-lru/issues"}, "homepage": "https://github.com/sindresorhus/quick-lru#readme", "_id": "quick-lru@6.1.2", "_nodeVersion": "16.20.0", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-AAFUA5O1d83pIHEhJwWCq/RQcRukCkn/NSm2QsTEMle5f2hP0ChI2+3Xb051PZCkLryI/Ir1MVKviT2FIloaTQ==", "shasum": "e9a90524108629be35287d0b864e7ad6ceb3659e", "tarball": "https://registry.npmjs.org/quick-lru/-/quick-lru-6.1.2.tgz", "fileCount": 5, "unpackedSize": 15235, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFsgCYBeOw6KZxFAZte2I9kIbA8TOD6KOJFhDqcQZmYaAiEAxce8lgswHm+HmVzXj7uOKHvuFcnBnJqrm50+XdPoIss="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/quick-lru_6.1.2_1693171406730_0.2538378381410218"}, "_hasShrinkwrap": false}, "7.0.0": {"name": "quick-lru", "version": "7.0.0", "description": "Simple “Least Recently Used” (LRU) cache", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/quick-lru.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "engines": {"node": ">=18"}, "scripts": {"test": "xo && nyc ava && tsd"}, "keywords": ["lru", "quick", "cache", "caching", "least", "recently", "used", "fast", "map", "hash", "buffer"], "devDependencies": {"ava": "^5.3.1", "nyc": "^15.1.0", "tsd": "^0.29.0", "xo": "^0.56.0"}, "nyc": {"reporter": ["text", "lcov"]}, "types": "./index.d.ts", "gitHead": "6862e8ecab576618c44fe0cb26e9fa921c85a884", "bugs": {"url": "https://github.com/sindresorhus/quick-lru/issues"}, "homepage": "https://github.com/sindresorhus/quick-lru#readme", "_id": "quick-lru@7.0.0", "_nodeVersion": "18.16.1", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-MX8gB7cVYTrYcFfAnfLlhRd0+Toyl8yX8uBx1MrX7K0jegiz9TumwOK27ldXrgDlHRdVi+MqU9Ssw6dr4BNreg==", "shasum": "447f6925b33ae4d2d637e211967d74bae4b99c3f", "tarball": "https://registry.npmjs.org/quick-lru/-/quick-lru-7.0.0.tgz", "fileCount": 5, "unpackedSize": 15059, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGdBu8mPah/zebYvJqS7BZg0gnHS9idgz+bJ6CpzfbbYAiBuoNr2rtgby+pb4EsV9Y/BBzAdxz2yFgZeyi8lN5Ypbw=="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/quick-lru_7.0.0_1694423602313_0.4835452465221286"}, "_hasShrinkwrap": false}}, "readme": "# quick-lru [![Coverage Status](https://codecov.io/gh/sindresorhus/quick-lru/branch/main/graph/badge.svg)](https://codecov.io/gh/sindresorhus/quick-lru/branch/main)\n\n> Simple [“Least Recently Used” (LRU) cache](https://en.m.wikipedia.org/wiki/Cache_replacement_policies#Least_Recently_Used_.28LRU.29)\n\nUseful when you need to cache something and limit memory usage.\n\nInspired by the [`hashlru` algorithm](https://github.com/dominictarr/hashlru#algorithm), but instead uses [`Map`](https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Global_Objects/Map) to support keys of any type, not just strings, and values can be `undefined`.\n\n## Install\n\n```sh\nnpm install quick-lru\n```\n\n## Usage\n\n```js\nimport QuickLRU from 'quick-lru';\n\nconst lru = new QuickLRU({maxSize: 1000});\n\nlru.set('🦄', '🌈');\n\nlru.has('🦄');\n//=> true\n\nlru.get('🦄');\n//=> '🌈'\n```\n\n## API\n\n### new QuickLRU(options?)\n\nReturns a new instance.\n\nIt's a [`Map`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Map) subclass.\n\n### options\n\nType: `object`\n\n#### maxSize\n\n*Required*\\\nType: `number`\n\nThe maximum number of items before evicting the least recently used items.\n\n#### maxAge\n\nType: `number`\\\nDefault: `Infinity`\n\nThe maximum number of milliseconds an item should remain in cache.\nBy default maxAge will be Infinity, which means that items will never expire.\n\nLazy expiration happens upon the next `write` or `read` call.\n\nIndividual expiration of an item can be specified by the `set(key, value, options)` method.\n\n#### onEviction\n\n*Optional*\\\nType: `(key, value) => void`\n\nCalled right before an item is evicted from the cache.\n\nUseful for side effects or for items like object URLs that need explicit cleanup (`revokeObjectURL`).\n\n### Instance\n\nThe instance is an [`Iterable`](https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Iteration_protocols) of `[key, value]` pairs so you can use it directly in a [`for…of`](https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Statements/for...of) loop.\n\nBoth `key` and `value` can be of any type.\n\n#### .set(key, value, options?)\n\nSet an item. Returns the instance.\n\nIndividual expiration of an item can be specified with the `maxAge` option. If not specified, the global `maxAge` value will be used in case it is specified on the constructor, otherwise the item will never expire.\n\n#### .get(key)\n\nGet an item.\n\n#### .has(key)\n\nCheck if an item exists.\n\n#### .peek(key)\n\nGet an item without marking it as recently used.\n\n#### .delete(key)\n\nDelete an item.\n\nReturns `true` if the item is removed or `false` if the item doesn't exist.\n\n#### .clear()\n\nDelete all items.\n\n#### .resize(maxSize)\n\nUpdate the `maxSize`, discarding items as necessary. Insertion order is mostly preserved, though this is not a strong guarantee.\n\nUseful for on-the-fly tuning of cache sizes in live systems.\n\n#### .keys()\n\nIterable for all the keys.\n\n#### .values()\n\nIterable for all the values.\n\n#### .entriesAscending()\n\nIterable for all entries, starting with the oldest (ascending in recency).\n\n#### .entriesDescending()\n\nIterable for all entries, starting with the newest (descending in recency).\n\n#### .entries()\n\nIterable for all entries, starting with the newest (ascending in recency).\n\n**This method exists for `Map` compatibility. Prefer [.entriesAscending()](#entriesascending) instead.**\n\n#### .forEach(callbackFunction, thisArgument)\n\nLoop over entries calling the `callbackFunction` for each entry (ascending in recency).\n\n**This method exists for `Map` compatibility. Prefer [.entriesAscending()](#entriesascending) instead.**\n\n#### .size *(getter)*\n\nThe stored item count.\n\n#### .maxSize *(getter)*\n\nThe set max size.\n\n## Related\n\n- [yocto-queue](https://github.com/sindresorhus/yocto-queue) - Tiny queue data structure\n", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-09-11T09:13:22.653Z", "created": "2017-03-30T08:59:02.872Z", "1.0.0": "2017-03-30T08:59:02.872Z", "1.1.0": "2017-07-27T13:45:39.693Z", "2.0.0": "2018-11-11T17:21:14.144Z", "3.0.0": "2019-03-10T13:41:22.484Z", "4.0.0": "2019-04-05T07:16:59.556Z", "4.0.1": "2019-05-29T17:21:30.565Z", "5.0.0": "2020-02-13T13:17:38.014Z", "5.1.0": "2020-04-06T07:57:10.191Z", "5.1.1": "2020-06-01T13:11:30.055Z", "6.0.0": "2021-01-14T08:17:14.266Z", "6.0.1": "2021-08-09T17:06:34.520Z", "6.0.2": "2021-11-21T07:09:11.437Z", "6.1.0": "2022-02-18T15:31:25.026Z", "6.1.1": "2022-03-26T15:19:07.150Z", "6.1.2": "2023-08-27T21:23:26.991Z", "7.0.0": "2023-09-11T09:13:22.504Z"}, "homepage": "https://github.com/sindresorhus/quick-lru#readme", "keywords": ["lru", "quick", "cache", "caching", "least", "recently", "used", "fast", "map", "hash", "buffer"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/quick-lru.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/quick-lru/issues"}, "license": "MIT", "readmeFilename": "readme.md"}