{"_id": "ansi-regex", "_rev": "58-e66a0cf7adb5f3ebd261a27c6f66662a", "name": "ansi-regex", "dist-tags": {"latest": "6.1.0"}, "versions": {"0.1.0": {"name": "ansi-regex", "version": "0.1.0", "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ansi-regex@0.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ansi-regex", "bugs": {"url": "https://github.com/sindresorhus/ansi-regex/issues"}, "dist": {"shasum": "55ca60db6900857c423ae9297980026f941ed903", "tarball": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-0.1.0.tgz", "integrity": "sha512-sKASJ0JEeHkkUN5A7GBQ1JH5EmBGi8dhl9KChCXV40kGmykzW4uLkVB8XbjV7598iK+I/HQOlqJGLCFA+1ZmzQ==", "signatures": [{"sig": "MEUCICFNrKksSFLI1nUU89uuh0FXYyTjllUj7rEaVgEb+kf4AiEAgpaAhOocM9F+Nwk8ZX9bpQElv1w2cGdA8DQelu8ZJbs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "55ca60db6900857c423ae9297980026f941ed903", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/sindresorhus/ansi-regex", "type": "git"}, "_npmVersion": "1.4.9", "description": "Regular expression for matching ANSI escape codes", "directories": {}, "devDependencies": {"mocha": "*"}}, "0.2.0": {"name": "ansi-regex", "version": "0.2.0", "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ansi-regex@0.2.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ansi-regex", "bugs": {"url": "https://github.com/sindresorhus/ansi-regex/issues"}, "dist": {"shasum": "3c48015ac52bcae430b08b822b87522644eb0de7", "tarball": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-0.2.0.tgz", "integrity": "sha512-Dl8j95am19SdSL9f3jc7rJN9qykOF10PwqRcJX9iXZfxXDHaXHuiYrZDsRWJCEGdsJwyama5HBZONykdFmvf4w==", "signatures": [{"sig": "MEQCIGANhX70jFuwQ3p9TQLXhhUTQBkEYkT71exvXCm7kHPLAiBpDIwsCnR3LMhUEu0Cv2ydghhLfD7HtwG3FsNByKDApw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "3c48015ac52bcae430b08b822b87522644eb0de7", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/sindresorhus/ansi-regex", "type": "git"}, "_npmVersion": "1.4.9", "description": "Regular expression for matching ANSI escape codes", "directories": {}, "devDependencies": {"mocha": "*"}}, "0.2.1": {"name": "ansi-regex", "version": "0.2.1", "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ansi-regex@0.2.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ansi-regex", "bugs": {"url": "https://github.com/sindresorhus/ansi-regex/issues"}, "dist": {"shasum": "0d8e946967a3d8143f93e24e298525fc1b2235f9", "tarball": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-0.2.1.tgz", "integrity": "sha512-sGwIGMjhYdW26/IhwK2gkWWI8DRCVO6uj3hYgHT+zD+QL1pa37tM3ujhyfcJIYSbsxp7Gxhy7zrRW/1AHm4BmA==", "signatures": [{"sig": "MEUCICfo8s4bZKG0g9UNFY/QkHrogtKK5S8PTGqDWDzAdraPAiEA4o3UAqG58AwN4vyEoAi1ryDbK8we8xeaFmZsvBsqQn8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "0d8e946967a3d8143f93e24e298525fc1b2235f9", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/sindresorhus/ansi-regex", "type": "git"}, "_npmVersion": "1.4.9", "description": "Regular expression for matching ANSI escape codes", "directories": {}, "devDependencies": {"mocha": "*"}}, "1.0.0": {"name": "ansi-regex", "version": "1.0.0", "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ansi-regex@1.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ansi-regex", "bugs": {"url": "https://github.com/sindresorhus/ansi-regex/issues"}, "dist": {"shasum": "54c7ce13af71e436348666484c44516ab9bc144e", "tarball": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-1.0.0.tgz", "integrity": "sha512-wzNmkzoFDDHQR1zwctGAX0KOZjX8rwjXG+5j4EeLqr1dHyGwcvH7/AIXk5yYB2Abuh2Ge3H44Fn83csDLLUqQA==", "signatures": [{"sig": "MEUCIQDuY826SxKNnmojTxOZlQLMmJX09dRgHGMzES5BC4q/hwIgWv8f0RQDZe5BTPbFF2VpNfZ9AdZ6So2p1OgJEDnofx0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "54c7ce13af71e436348666484c44516ab9bc144e", "engines": {"node": ">=0.10.0"}, "gitHead": "4210f11027ddd7937f9e25a9a1570aee6d0594f5", "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/sindresorhus/ansi-regex", "type": "git"}, "_npmVersion": "1.4.14", "description": "Regular expression for matching ANSI escape codes", "directories": {}, "devDependencies": {"mocha": "*"}}, "1.1.0": {"name": "ansi-regex", "version": "1.1.0", "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ansi-regex@1.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ansi-regex", "bugs": {"url": "https://github.com/sindresorhus/ansi-regex/issues"}, "dist": {"shasum": "67792c5d6ad05c792d6cd6057ac8f5e69ebf4357", "tarball": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-1.1.0.tgz", "integrity": "sha512-+VAkk48zFJGuS3ru8ycFCrX1wif67Tbn+yJHAo5xxFY5pFVp8Fy9WbZqOKYxGCP9dEr4ny8aGbjMMpcTGPQzMA==", "signatures": [{"sig": "MEUCIHfNx4mPZnwGi6aQi5TpXIAqzudRmeeTfVQn2xU+sLm9AiEAzm02QbEZY+N0h4N8tSuFE8xOlmhWv6Clv0Osnyf0814=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "67792c5d6ad05c792d6cd6057ac8f5e69ebf4357", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha test/test.js", "view-supported": "node test/viewCodes.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/sindresorhus/ansi-regex", "type": "git"}, "_npmVersion": "1.4.9", "description": "Regular expression for matching ANSI escape codes", "directories": {}, "devDependencies": {"mocha": "*"}}, "1.1.1": {"name": "ansi-regex", "version": "1.1.1", "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ansi-regex@1.1.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ansi-regex", "bugs": {"url": "https://github.com/sindresorhus/ansi-regex/issues"}, "dist": {"shasum": "41c847194646375e6a1a5d10c3ca054ef9fc980d", "tarball": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-1.1.1.tgz", "integrity": "sha512-q5i8bFLg2wDfsuR56c1NzlJFPzVD+9mxhDrhqOGigEFa87OZHlF+9dWeGWzVTP/0ECiA/JUGzfzRr2t3eYORRw==", "signatures": [{"sig": "MEUCICU6NOln1A9kSVM+kcZ0RQ7Foaj7ZZrTVD3kUtQWXiPgAiEAkK2BBFckDS1wSyOm3gW6oFGWCccNojUqGbs0ehRYcY0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "41c847194646375e6a1a5d10c3ca054ef9fc980d", "engines": {"node": ">=0.10.0"}, "gitHead": "47fb974630af70998157b30fad6eb5e5bd7c7cd6", "scripts": {"test": "mocha test/test.js", "view-supported": "node test/viewCodes.js"}, "_npmUser": {"name": "jbnicolai", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/ansi-regex", "type": "git"}, "_npmVersion": "2.1.16", "description": "Regular expression for matching ANSI escape codes", "directories": {}, "_nodeVersion": "0.10.35", "devDependencies": {"mocha": "*"}}, "2.0.0": {"name": "ansi-regex", "version": "2.0.0", "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ansi-regex@2.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/ansi-regex", "bugs": {"url": "https://github.com/sindresorhus/ansi-regex/issues"}, "dist": {"shasum": "c5061b6e0ef8a81775e50f5d66151bf6bf371107", "tarball": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-2.0.0.tgz", "integrity": "sha512-jCcLjwL2jOaTcRIaJkoRteMwNXg8nfJvwT/9K91kwZhH7bf4lsprqZ2+Qa7tSp8BYtejobOCBkDreC07q0KmZw==", "signatures": [{"sig": "MEYCIQCdHxAVrdkIa+f4ShCg9LmFGGXaydAjrFi2EKdL7dSQsAIhAKHKSEqbLoyws6ix+AeKJHAHTX7IXclIhkYwIH9vP8a1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "c5061b6e0ef8a81775e50f5d66151bf6bf371107", "engines": {"node": ">=0.10.0"}, "gitHead": "57c3f2941a73079fa8b081e02a522e3d29913e2f", "scripts": {"test": "mocha test/test.js", "view-supported": "node test/viewCodes.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/ansi-regex", "type": "git"}, "_npmVersion": "2.11.2", "description": "Regular expression for matching ANSI escape codes", "directories": {}, "_nodeVersion": "0.12.5", "devDependencies": {"mocha": "*"}}, "2.1.1": {"name": "ansi-regex", "version": "2.1.1", "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ansi-regex@2.1.1", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/ansi-regex#readme", "bugs": {"url": "https://github.com/chalk/ansi-regex/issues"}, "xo": {"rules": {"guard-for-in": 0, "no-loop-func": 0}}, "dist": {"shasum": "c3b33ab5ee360d86e0e628f0468ae7ef27d654df", "tarball": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-2.1.1.tgz", "integrity": "sha512-TIGnTpdo+E3+pCyAluZvtED5p5wCqLdezCyhPZzKPcxvFplEt4i+W7OONCKgeZFT3+y5NZZfOOS/Bdcanm1MYA==", "signatures": [{"sig": "MEQCIAR2sccS4/yhooGKsXIc/4rbjLYgEI/ZRh6zjX4sAOioAiAMaqaR1nse5wZf6Gl1qpfxCxMLaXyb6MKNhG4G5Cjc+w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "c3b33ab5ee360d86e0e628f0468ae7ef27d654df", "engines": {"node": ">=0.10.0"}, "gitHead": "7c908e7b4eb6cd82bfe1295e33fdf6d166c7ed85", "scripts": {"test": "xo && ava --verbose", "view-supported": "node fixtures/view-codes.js"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/ansi-regex.git", "type": "git"}, "_npmVersion": "2.14.2", "description": "Regular expression for matching ANSI escape codes", "directories": {}, "_nodeVersion": "0.10.32", "devDependencies": {"xo": "0.16.0", "ava": "0.17.0"}, "_npmOperationalInternal": {"tmp": "tmp/ansi-regex-2.1.1.tgz_1484363378013_0.4482989883981645", "host": "packages-18-east.internal.npmjs.com"}}, "3.0.0": {"name": "ansi-regex", "version": "3.0.0", "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ansi-regex@3.0.0", "maintainers": [{"name": "dthree", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/ansi-regex#readme", "bugs": {"url": "https://github.com/chalk/ansi-regex/issues"}, "dist": {"shasum": "ed0317c322064f79466c02966bddb605ab37d998", "tarball": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-3.0.0.tgz", "integrity": "sha512-wFUFA5bg5dviipbQQ32yOQhl6gcJaJXiHE7dvR8VYPG97+J/GNC5FKGepKdEDUFeXRzDxPF1X/Btc8L+v7oqIQ==", "signatures": [{"sig": "MEUCIGZIxnZvDlDd2wlGhadhaYGHYJUyNwYOgCW3R98PdgWbAiEA2wN0X8oDjEhS1vHmuDx3Rmof6hJLkC4TfVvx6jBu6eE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "ed0317c322064f79466c02966bddb605ab37d998", "engines": {"node": ">=4"}, "gitHead": "0a8cc19946c03c38520fe8c086b8adb66f9cce0b", "scripts": {"test": "xo && ava", "view-supported": "node fixtures/view-codes.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/ansi-regex.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Regular expression for matching ANSI escape codes", "directories": {}, "_nodeVersion": "4.8.3", "devDependencies": {"xo": "*", "ava": "*"}, "_npmOperationalInternal": {"tmp": "tmp/ansi-regex-3.0.0.tgz_1497985412590_0.5700640194118023", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "ansi-regex", "version": "4.0.0", "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ansi-regex@4.0.0", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/ansi-regex#readme", "bugs": {"url": "https://github.com/chalk/ansi-regex/issues"}, "dist": {"shasum": "70de791edf021404c3fd615aa89118ae0432e5a9", "tarball": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-4.0.0.tgz", "fileCount": 4, "integrity": "sha512-iB5Dda8t/UqpPI/IjsejXu5jOGDrzn41wJyljwPH65VCIbk6+1BzFIMJGFwTNrYXT1CrD+B4l19U7awiQ8rk7w==", "signatures": [{"sig": "MEUCIAso5oveCqpFlBxrb53yO+PrFu0XbxlabKuVmjtXneRlAiEA9s3wMBc2PkUfz2kFfjMfBAsauEdzEr5cV3K1YMPSUO8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4364, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJboLTyCRA9TVsSAnZWagAAGnQP/ikFm/pU0nz/k+kwLXQF\nQaOqkrWmoNlb6fiz7NElGRxe+a7K5dkovim/Plf66V+kcT1u47O/IsNn+1AU\nUYztxJ2Syn8QWdLgCKZlVy4jgN8qrq3ACLHYV6XFo+fV7JVoRqZnVLZlJtCg\nxaWT3K+ckyo0SbFrSKHchZWSha20mtgBjUzeVnzsx0zUELhL3I9soTTMaXOp\n8tYKs2rxhYduYewTu5hDfi/aAYlWhUuS0KkxZ+lR8zjrRJV3OMMcexJ+6qzZ\n5YozFB5L72r+aXJ5b9epDbRMTUh9D+J6OIRED4COBTbi5VaxjE5Qef7Y4wuy\nB5t+S8shSu/yPbMGKdHW+lBIv8tskZgCislmedHL7N3dWqKRqaelIKJHp8ea\n5NlvT4Bq6bIgggoPSG4ccVPvnwhPzZso3yBhx1Xzxdysb3nRor0ZrkDr9NSS\nPHhQwxlIZr3Ba4R7BWeyNjvOw4kAB8EicYP9REruWDoMxee/mWYd3xo1rOTD\nGocidueWQwHJPbyxGm4boKcBsToDN7boOoZAJONR0DBFgB5fCFB7FbzLRgil\nh3sOpoe7K+8/tCC/GlbpnhPEtNtFAWmtVEr5fuG6pwCxwO9ZOUNsFKCccs/Q\nSkXo9JmCGUgLzW04Z0Cms6BMXuS/VKERq+QyJKq5EQXJ93KwEBWj7gSmuYar\nmiIW\r\n=6Av/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "a1d92466388fc8766b63bfab27ddc1e1df897dda", "scripts": {"test": "xo && ava", "view-supported": "node fixtures/view-codes.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/ansi-regex.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Regular expression for matching ANSI escape codes", "directories": {}, "_nodeVersion": "8.11.4", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.23.0", "ava": "^0.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/ansi-regex_4.0.0_1537258737952_0.3982474354635459", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "ansi-regex", "version": "4.1.0", "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ansi-regex@4.1.0", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/ansi-regex#readme", "bugs": {"url": "https://github.com/chalk/ansi-regex/issues"}, "dist": {"shasum": "8b9f8f08cf1acb843756a839ca8c7e3168c51997", "tarball": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-4.1.0.tgz", "fileCount": 4, "integrity": "sha512-1apePfXM1UOSqw0o9IiFAovVz9M5S1Dg+4TrDwfMewQ6p/rmMueb7tWZjQ1rx4Loy1ArBggoqGpfqqdI4rondg==", "signatures": [{"sig": "MEUCIBW8s+I9AGd0oTXWxFXE5bPmDufETac5wywy5sMer6BBAiEAtP72v3hFf5e/0XRtGiTmNsZ2Pg1GRmrWpJA6TJUYr5o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5139, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcgghQCRA9TVsSAnZWagAAkicP/A0YuAuK50ptS+EZeV8x\nNdxf9AYCNd3FSMq/0MeHHbSFb/ofI8CKKGuru3zVPFV/KfVhddUsnqv45Z/y\nNRxrXDkUd0LpUtCZW8QcPOQReuUshJ1n4XvSngt6753R95YbGzqVbDkORvvH\nfOjl/eIiTd3J2amW4LcOsiqTRkNCahD1AA+dEHiYVJ3dpymihWH5vTWWPmll\nGVzb6oUpIb2YtusNlma5ALRPFXYv4kxMVuTV5LXrdxeZyOyhytShYDJX6/DK\n7eaHDjB3TJT3lGq8emq4xI5xLn1pXceZOhd4iusPCvToG3uwu5DMKSuoLYF0\nEzKPyi1D78oUPgZ+mzDSgU3KobiEtMckHatyUSMS2LwBW1Xdz+LEX4mrt6Jn\ni6hSmJQjhlEeYgFlWKpxYgx/B95G+ckCEqDcSlhrLqwcw6VQhEWd8X7RWfOv\nyLJ9rwja7ILbMHIgQHl1NYGqW6+n3Qe/5MtZdwC5OmZaVwbiSpesDOcJXsEM\nD1JqCLZxQTrKwVzlUbj2FJbKjGls5V0RupMKqr4hSftxUIa4PZZ5Aaekt7XX\nVt0EkP6NZ4hRkFYzutsvNOK37slo948zb6Xp8e0ymeMjTIhISCz+k0R0KgmM\nP4o4nK1zyz1VPqzVvKOp0n6VjYABlRe1d1A2ui42LWy5E7I7F081QqpCUkCJ\nVJP2\r\n=TkVA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "a079ab2d30cfb752a3f247dcf358d0a591c288c5", "scripts": {"test": "xo && ava", "view-supported": "node fixtures/view-codes.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/ansi-regex.git", "type": "git"}, "_npmVersion": "6.8.0", "description": "Regular expression for matching ANSI escape codes", "directories": {}, "_nodeVersion": "8.15.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.23.0", "ava": "^0.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/ansi-regex_4.1.0_1552025680031_0.26097113418100015", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "ansi-regex", "version": "5.0.0", "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ansi-regex@5.0.0", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/ansi-regex#readme", "bugs": {"url": "https://github.com/chalk/ansi-regex/issues"}, "dist": {"shasum": "388539f55179bf39339c81af30a654d69f87cb75", "tarball": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.0.tgz", "fileCount": 5, "integrity": "sha512-bY6fj56OUQ0hU1KjFNDQuJFezqKdrAyFdIevADiqrWHwSlbmBNMHp5ak2f40Pm8JTFyM2mqxkG6ngkHO11f/lg==", "signatures": [{"sig": "MEUCIQDHZmRpyQjhEtkiaP76KYQKYK38C6gxln7LWeBNELA7DAIgOpfLZg09XrrsW5VwbbWEy+7RDNpdR62FEt94u1lJCpc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5692, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdly0KCRA9TVsSAnZWagAAinUP/0Ck5MNcNm9ueLhK5Ga/\nwggCVoaJ77iq1Kgi8QoOPd0E4PKrMXOnXfoHMjJfvGmzh+2GieptLu+2zzUo\nXQjbFLCvUXzMVJEh7sS5q1AI3JM5dQxEi4Ab2IDWPVn41gvqvdfRWOv1KXtC\nmYxnnhoTUpU3rYKHMbKI+eskQDsZjM/ZZj4Vf2Z8OlTzPv583uuBAAiP/SGk\n6mMwPbdEuVq5hN2EEOlAKzxkxTKCAX3+7Re2H9PVAMmtCXWj09qwaxwRxtVc\nCHi7FGRefk3a3FJxdLPKHdmOR7fQJy5TRoOhutjhQPFZKGqFhReCtsZwHGuJ\nKNqagC1KBLFQRLLwtJGhdQGpLUYZgcurWC1OX+wbMBJB8ZrC0HBIuUAlLCOJ\noKhRI81dJ89lT75+xe6B2X8TZsWzQNuohd4V5ymKBlztU3wOPk1r+SEt8tFu\nH+iesokrKb/tSHL0cr5XVKq5yJC3oWFpvdfM7xToVS4ehxfh50Ib8OuhFJXr\nxws6yKp+PA4RTy533CHyfME1K2lM8M8YQPnFs6uHeaWMQhJHxTsYWIccZP+t\n17qqhl64fW6UhO0Nxio2Jpbwrdo1pfmdsN04AVow6nsEfRfGrZ/9Qzq23c0G\nawgI4nagjuIuQeknNymHC8IbhpYEa5eRY7+uvElE1bWZwmJshf9ZItStOjYI\n1B0E\r\n=DWyA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "2b56fb0c7a07108e5b54241e8faec160d393aedb", "scripts": {"test": "xo && ava && tsd", "view-supported": "node fixtures/view-codes.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/ansi-regex.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Regular expression for matching ANSI escape codes", "directories": {}, "_nodeVersion": "10.16.3", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.3", "ava": "^2.4.0", "tsd": "^0.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/ansi-regex_5.0.0_1570188553674_0.9267473348647308", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "ansi-regex", "version": "6.0.0", "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ansi-regex@6.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/ansi-regex#readme", "bugs": {"url": "https://github.com/chalk/ansi-regex/issues"}, "dist": {"shasum": "ecc7f5933cbe5ac7b33e209a5ff409ab1669c6b2", "tarball": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-6.0.0.tgz", "fileCount": 5, "integrity": "sha512-tAaOSrWCHF+1Ear1Z4wnJCXA9GGox4K6Ic85a5qalES2aeEwQGr7UC93mwef49536PkCYjzkp0zIxfFvexJ6zQ==", "signatures": [{"sig": "MEYCIQDLdXfGwD/nurKg7vItN259S8UAUAS62nccUonevxA/mgIhAKy4ITBu326c+CBVMt1HLqssMHFhIj8COqAV7/kigOXR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5631, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgeShnCRA9TVsSAnZWagAA5skP/RJzYexBxMl+ln5Waq+j\nJcMY3Gjz1UL+qodDm2QbAMK41c92SEVh5f26fdyRUfhSgLo3Jjn8PBcMlStr\njAH/FBhyrQsFGyxVsgOhOfkSxDjQtWRX9JBCwnYbCp8zfpOcCqIGgwl6Za8X\njQkODSEkwpBdIAI0I8E8bTLcfO/RK7pPfbEWptrdNJgsv80Y3ye8fLBiFERm\nHzgt1FCR5m0LyVlfq941MpmLyzd0f0dewP3WiQ9yDyD60Sune4s6jw4n97pU\nDG3H/MboqIaGkcQPyVWTtM92UTRKt6dBcIfnCAXgf6qD7Iu2/hWUQQg6NpVX\nAD22nWt5e6GCJXOzDDQSY5xO5dnR4DcYpcQBiszYqxRopGTSWy5E6pK6x+5N\na3mbFq4zFi33TtjM8MRdwGRyIqnGwsYuSTCw/ie6EHZNBlBMLhcMfffYYwtD\n8Yc04q1n3jsloKT4Z3TGDkTDPALEQk7LcOklArsOHkqVDjtUyPMdm3y8ew73\n4k9b7G/x4AnP9yQ6zhIASoToZ8YLnQCPZxFZ93HnMAhPAvwe9n6Ni6LqnKzU\n5myBJYckIV6qcOp1nPwxyBTwdv0uXE5h4FLCbE2B3JVe+kV5cbu3OC2rGwN0\nncdZvUwFGo5rNKkHm+jPcI7/BrZGR1+4r/Z8GFrPYGpY20gFJsL8055NsGvu\n+l/+\r\n=ys2B\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "engines": {"node": ">=12"}, "exports": "./index.js", "funding": "https://github.com/chalk/ansi-regex?sponsor=1", "gitHead": "c1b5e45f7c65a332ffb03ac8e5804ad37c579cdc", "scripts": {"test": "xo && ava && tsd", "view-supported": "node fixtures/view-codes.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/ansi-regex.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Regular expression for matching ANSI escape codes", "directories": {}, "_nodeVersion": "14.16.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.38.2", "ava": "^3.15.0", "tsd": "^0.14.0"}, "_npmOperationalInternal": {"tmp": "tmp/ansi-regex_6.0.0_1618552935087_0.14381997678067826", "host": "s3://npm-registry-packages"}}, "6.0.1": {"name": "ansi-regex", "version": "6.0.1", "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ansi-regex@6.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/ansi-regex#readme", "bugs": {"url": "https://github.com/chalk/ansi-regex/issues"}, "dist": {"shasum": "3183e38fae9a65d7cb5e53945cd5897d0260a06a", "tarball": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-6.0.1.tgz", "fileCount": 5, "integrity": "sha512-n5M855fKb2SsfMIiFFoVrABHJC8QtHwVx+mHWP3QcEqBHYienj5dHSgjbxtC0WEZXYt4wcD6zrQElDPhFuZgfA==", "signatures": [{"sig": "MEUCIQCWW/ZEXmQGK500jBFw7uRYczrrcLTzOEnm3uDiNPs6rAIgcKX8SLAOUpBu/tWg+7vPsMA4l7siraEVYPpTETOVqX0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5667, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhO78hCRA9TVsSAnZWagAA+F4P/0Xt4D0GoXduv+o147iO\nj3b1oPSNypnmThhmCAUInwB1Xd9SXFEjzuNDNGX/8xeWUQrJssJ+SBEzb5te\nr34P6wPHUeuTa2p1j3ABN9ABP/NPCPkaEDgjx86ImkT++3VI8yNEuSSOsghI\n5Ssvhm6bLJBWzCt/cweIG7x4ZJQPeUbyoWIJmBobCDyNuVgabmk9B2B1RyO0\nf2aounmh5w1fBWsx65MjTi7fu4e/GOP+xscf9Rn1AdRP6rcxJakPjaGTF3HE\naz6UNP9UZCuZ4rb6xOFEfLmiqfNbK31NKNLPrABDXF6NkbJepsUhNmju32cg\nUuo4lSkxfFaVyFXFB1h62RmDdemxZWl9j24fVt+99K4BH6xOiwPmOHHHPJnL\nXNuZ6eCwdcLjPe8Af/0GEsFqdDu591Hwi2Nl8UNAD5hCzcDTITOTL95Dopaw\nGzySTClnDsh17Ud73FWM51FQzVDORa2vOmVzQts3aHlF93ufk9RHtlN9YxAV\nUvM3IxMHx2O8Rc2gSc6t/pJAdFACBLkLDGtcQVhxaFcTpGXrKUr+KN2gFC4D\nXZA/MvK0YXBmrwec20k2wTAdmi7XO5k8HciCPT7MZDJONTBlt9cC/LL41JNc\nvxj0A0loP9R44eVoAJOSbxhpnteeIjw4rwkOySZOqqEsUw5MDFp/peiojlcv\nCOQk\r\n=VRgZ\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=12"}, "exports": "./index.js", "funding": "https://github.com/chalk/ansi-regex?sponsor=1", "gitHead": "d908492e0070f26552fad1b25e339aff9011ae8b", "scripts": {"test": "xo && ava && tsd", "view-supported": "node fixtures/view-codes.js"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/ansi-regex.git", "type": "git"}, "_npmVersion": "7.23.0", "description": "Regular expression for matching ANSI escape codes", "directories": {}, "_nodeVersion": "16.0.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.38.2", "ava": "^3.15.0", "tsd": "^0.14.0"}, "_npmOperationalInternal": {"tmp": "tmp/ansi-regex_6.0.1_1631305505393_0.35989069175371236", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "ansi-regex", "version": "5.0.1", "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ansi-regex@5.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/ansi-regex#readme", "bugs": {"url": "https://github.com/chalk/ansi-regex/issues"}, "dist": {"shasum": "082cb2c89c9fe8659a311a53bd6a4dc5301db304", "tarball": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz", "fileCount": 5, "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==", "signatures": [{"sig": "MEUCIQCS7XsMq0d29jMXjedcRcP044S7ZelyMVBxKjkUpCShHAIgGyiTNkLoCCh/rpMnXAisLtOsV3SGe2ftVRwhvZTAPyw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5609, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQMXnCRA9TVsSAnZWagAADYYP/iz1/gbU0N9EjGpKc+Gm\nnX7qe5zf8TvcOpxBMDSFiCD5ntg/EH4YguqeUhRgW7X/nx58basGjNU2Payd\n6gIBVMEriQUWhS6MllSbiIbxY3ALKRcNhIphqER4kneujLwTqgAMOSjjnOk+\nvhPheetSO9ydRVZvb1UHC45D/4Bl0BJjPuAgF6YJlAae+bon50yImBeBUus4\nSmcbaBKSoCKT+ml4pfNidfUIJn+Zo8u0u8WI/6hfJ5oD9/eerQaSK/Fjt1hZ\nj67NmXWMsMugedkgpKbK3lZX+LucgTrb7L9QJC++LQj6FSRDQBguiODgJ912\nVxqjJN0SSqcpNRVk0dTHhI2IoOn4Mu3HVZolsrVFGXsQDMlNHWxehHUtzZ/h\ne592hb0tqcyIeMFLUMcRhcnaQZrNKTYv7XSMpfPWh73slOk5AaZFOyY/yZ2k\nIqfm7sKVsijHzRgyveZ+n1QyV6CWFMzQbxnj34t7NpnX429VY/F/ZL/E1Yfu\noJhSbp/K0+k8Gp9g9YNDANE7s5jM8pJzlSOo3XUWyaFaujRHMsAtxgtYikZB\n3uzZaYQxryjCkvuvZJFwUBMeLWJP2JKt7bpK3mv7DHvEWTxicBHSPaePhA01\nOfiQ7YzPY3pNI/Fki/jloyJgSazLpvlWv7XWAF/OhvB5G3KQbfDe/hbQg0Yq\nsUn3\r\n=I35d\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./index.d.ts", "engines": {"node": ">=8"}, "gitHead": "a9babce885cf19c363cf1d1c645f834592c3f7a4", "scripts": {"test": "xo && ava && tsd", "view-supported": "node fixtures/view-codes.js"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/ansi-regex.git", "type": "git"}, "_npmVersion": "7.23.0", "description": "Regular expression for matching ANSI escape codes", "directories": {}, "_nodeVersion": "16.0.0", "_hasShrinkwrap": false, "readmeFilename": "readme.md", "devDependencies": {"xo": "^0.25.3", "ava": "^2.4.0", "tsd": "^0.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/ansi-regex_5.0.1_1631634919356_0.29996150981120184", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "ansi-regex", "version": "4.1.1", "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ansi-regex@4.1.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/ansi-regex#readme", "bugs": {"url": "https://github.com/chalk/ansi-regex/issues"}, "dist": {"shasum": "164daac87ab2d6f6db3a29875e2d1766582dabed", "tarball": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-4.1.1.tgz", "fileCount": 4, "integrity": "sha512-ILlv4k/3f6vfQ4OoP2AGvirOktlQ98ZEL1k9FaQjxa3L1abBgbuTDAdPOpvbGncC0BTVQrl+OM8xZGK6tWXt7g==", "signatures": [{"sig": "MEUCIHTU2SCcqrOjx+c6SBKSTrP6U6QykHkMd5WKISOMq7dAAiEAj9BPT1vJW/ZV85WYFAKWbTTGMksmIw4Sv9s9V9U55ko=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5172, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiLA7KACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq3gQ//a+k/IMT0gBGfY6uA0Cgu+o/GhIow5TQbqkJER9T27TO8Po7h\r\n/07GR3KJTtxgFn+wkRlNLgXxdCRRjXe5df4eHzbMM52Ag49iqrYecm3xC28i\r\nCJArVdmZ0djATnvThwWImD/dHN5cj76MHQzJjWEdWPXXim/cRHHHcHXkWEsv\r\nN64mWjz06J8NqidAS0ARvYGc6QmUeWENzuhUO7Pa2rymBrkccTQkYNJUoTCf\r\nJ+DAtQZpUm9jFHOKoKsNtyBkb3BlPLZVjnfF6TNv0Ya2KiW5N3FIQEeS8h54\r\nM9l82ED8sopqO7Z6Vsps2W7xdCTRvKPht+cZJhet4hmcxMQE/lNtH6R4C1hR\r\n0MzhnSYwvvsDfqDbjNaerMe3/yq/EkUj8DTDqnNBPeODWlLgG3WF3tTlA7aQ\r\nIqGk8bVfd8Fxr6EJ4JKjn4bUCbmTSzqK7lAiDX/pDXMOONiDvRwQaDJbT5y5\r\nycep5aL9mlYGkSOf2dPRp7B71KHKQAmGTl3OupYMrWxSVNAoIk3T4r5j6IX4\r\n+8LlAtusnCJ4mGWo2M4rXhQ1o+5gWyLgyf470hdCkgH24Yp/8348NEMRZD+z\r\nGv3sPclHhjVZzS0GhEIvHUYYGUuSRxM6Vd6g1lgv8e9LI3/yqvf830l/FHQo\r\nJwEpTS3Q8YrM9t2YpodilybqqpuTbu8uoB4=\r\n=Hgo9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "64735d25eb839b55bc9fae3877edb702b4c92ca2", "scripts": {"test": "xo && ava", "view-supported": "node fixtures/view-codes.js"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/ansi-regex.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "Regular expression for matching ANSI escape codes", "directories": {}, "_nodeVersion": "17.3.1", "_hasShrinkwrap": false, "readmeFilename": "readme.md", "devDependencies": {"xo": "^0.23.0", "ava": "^0.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/ansi-regex_4.1.1_1647054537685_0.02257829120511201", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "ansi-regex", "version": "3.0.1", "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ansi-regex@3.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "homepage": "https://github.com/chalk/ansi-regex#readme", "bugs": {"url": "https://github.com/chalk/ansi-regex/issues"}, "dist": {"shasum": "123d6479e92ad45ad897d4054e3c7ca7db4944e1", "tarball": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-3.0.1.tgz", "fileCount": 4, "integrity": "sha512-+O9Jct8wf++lXxxFc4hc8LsjaSq0HFzzL7cVsw8pRDIPdjKD2mT4ytDZlLuSBZ4cLKZFXIrMGO7DbQCtMJJMKw==", "signatures": [{"sig": "MEUCIGxf4gJU3Ry4fyipshNrDGCYFK/glbif7vsA/O9sywmCAiEAgFdMiI1ykrpU2QunGRKSzrIksQtwFrDpgjgh2uUQKh8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4054, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiQGbIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqy/Q//Vh9oKpgDxgXcW+UYZ9DZKYScVldnKzuT5VNYLrGT8mMMPxAC\r\nws4wVRyeWo8WX4jwQqpZaDxr5WlH+X2tbxM+PmwncwkOYTtbTGutmr+cwc16\r\nXaKNwQZt4F5fA32N39Jxyu05sLqje517n3rRFvN6HqzOw21DYIL6N7UpUXeD\r\nFiwNMFOi2S3RMUJgf6EDVNB59MNX7X6W85cvJrjRK8xfUJB50ODx1WfUNG1X\r\nc96t6swskGBdq0riRxtmUTyJgxkgaL4eHnYMpLSad1Xx5rVyHckL9CFajYPo\r\np64kmi2+UHAswdfzDYuQTuIt2vKzpKCGGP74FSZ5trTsQNuljvjhemFePlAw\r\nXOoq2sf/yTGiNu9W+ved2NPpR/nSQO9UnBp7P/pKcfY92rPOwRc3ypBx4JDC\r\nQHANx8/fM7TtRrVUyVJoyEYzMnDxDQnzeGRrYfSneg1oI7oci62HXmgrfxvh\r\n8TPDgsKALJixmkDc6FQgENjmMrblrmU7ZuK2H1meidi+62MJEoTj5lwXKp9d\r\nhay1RKGB4qKHEO5PVIBrYdvkoZIaUyIrN4KgcqHHjrOKWhcWAZDu3A9wwyLD\r\nAOMLQbPqJccQj2FrSMOGrc1ego70swRFGX21xLW0xqr8p65PTLGBKp0+OgC9\r\nlcz8F/xfg1PkqVRZw4TGuEcBE3EHcDFSrr0=\r\n=xF18\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}, "gitHead": "f545bdb80048f527889eddb9ac1a851c6f2a2241", "scripts": {"test": "xo && ava", "view-supported": "node fixtures/view-codes.js"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/chalk/ansi-regex.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "Regular expression for matching ANSI escape codes", "directories": {}, "_nodeVersion": "17.3.1", "_hasShrinkwrap": false, "readmeFilename": "readme.md", "devDependencies": {"xo": "*", "ava": "*"}, "_npmOperationalInternal": {"tmp": "tmp/ansi-regex_3.0.1_1648387784373_0.8659992768445368", "host": "s3://npm-registry-packages"}}, "6.1.0": {"name": "ansi-regex", "version": "6.1.0", "description": "Regular expression for matching ANSI escape codes", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-regex.git"}, "funding": "https://github.com/chalk/ansi-regex?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "types": "./index.d.ts", "sideEffects": false, "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd", "view-supported": "node fixtures/view-codes.js"}, "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "devDependencies": {"ansi-escapes": "^5.0.0", "ava": "^3.15.0", "tsd": "^0.21.0", "xo": "^0.54.2"}, "_id": "ansi-regex@6.1.0", "gitHead": "f338e1814144efb950276aac84135ff86b72dc8e", "bugs": {"url": "https://github.com/chalk/ansi-regex/issues"}, "homepage": "https://github.com/chalk/ansi-regex#readme", "_nodeVersion": "18.20.4", "_npmVersion": "10.6.0", "dist": {"integrity": "sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==", "shasum": "95ec409c69619d6cb1b8b34f14b660ef28ebd654", "tarball": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-6.1.0.tgz", "fileCount": 5, "unpackedSize": 5412, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDGQvwQxWIDhiEnJTo1Tb35X3bxVWxMjBoPaj+qTxlfnAiA3pztqy3OFgYbMqWZC5GdWT1yG2frnePf72yHSrhynBw=="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ansi-regex_6.1.0_1725890276733_0.3140624451173344"}, "_hasShrinkwrap": false}}, "time": {"created": "2014-06-03T16:59:22.332Z", "modified": "2024-09-09T13:57:57.475Z", "0.1.0": "2014-06-03T16:59:22.332Z", "0.2.0": "2014-06-14T01:12:53.550Z", "0.2.1": "2014-06-20T16:44:03.241Z", "1.0.0": "2014-08-13T13:29:14.050Z", "1.1.0": "2014-08-30T12:38:30.166Z", "1.1.1": "2015-02-22T09:24:51.185Z", "2.0.0": "2015-06-30T16:07:19.279Z", "2.1.1": "2017-01-14T03:09:39.887Z", "3.0.0": "2017-06-20T19:03:33.464Z", "4.0.0": "2018-09-18T08:18:58.129Z", "4.1.0": "2019-03-08T06:14:40.169Z", "5.0.0": "2019-10-04T11:29:13.830Z", "6.0.0": "2021-04-16T06:02:15.282Z", "6.0.1": "2021-09-10T20:25:05.514Z", "5.0.1": "2021-09-14T15:55:19.540Z", "4.1.1": "2022-03-12T03:08:58.035Z", "3.0.1": "2022-03-27T13:29:44.595Z", "6.1.0": "2024-09-09T13:57:56.873Z"}, "bugs": {"url": "https://github.com/chalk/ansi-regex/issues"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "license": "MIT", "homepage": "https://github.com/chalk/ansi-regex#readme", "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-regex.git"}, "description": "Regular expression for matching ANSI escape codes", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}], "readme": "# ansi-regex\n\n> Regular expression for matching [ANSI escape codes](https://en.wikipedia.org/wiki/ANSI_escape_code)\n\n## Install\n\n```sh\nnpm install ansi-regex\n```\n\n## Usage\n\n```js\nimport ansiRegex from 'ansi-regex';\n\nansiRegex().test('\\u001B[4mcake\\u001B[0m');\n//=> true\n\nansiRegex().test('cake');\n//=> false\n\n'\\u001B[4mcake\\u001B[0m'.match(ansiRegex());\n//=> ['\\u001B[4m', '\\u001B[0m']\n\n'\\u001B[4mcake\\u001B[0m'.match(ansiRegex({onlyFirst: true}));\n//=> ['\\u001B[4m']\n\n'\\u001B]8;;https://github.com\\u0007click\\u001B]8;;\\u0007'.match(ansiRegex());\n//=> ['\\u001B]8;;https://github.com\\u0007', '\\u001B]8;;\\u0007']\n```\n\n## API\n\n### ansiRegex(options?)\n\nReturns a regex for matching ANSI escape codes.\n\n#### options\n\nType: `object`\n\n##### onlyFirst\n\nType: `boolean`\\\nDefault: `false` *(Matches any ANSI escape codes in a string)*\n\nMatch only the first ANSI escape.\n\n## FAQ\n\n### Why do you test for codes not in the ECMA 48 standard?\n\nSome of the codes we run as a test are codes that we acquired finding various lists of non-standard or manufacturer specific codes. We test for both standard and non-standard codes, as most of them follow the same or similar format and can be safely matched in strings without the risk of removing actual string content. There are a few non-standard control codes that do not follow the traditional format (i.e. they end in numbers) thus forcing us to exclude them from the test because we cannot reliably match them.\n\nOn the historical side, those ECMA standards were established in the early 90's whereas the VT100, for example, was designed in the mid/late 70's. At that point in time, control codes were still pretty ungoverned and engineers used them for a multitude of things, namely to activate hardware ports that may have been proprietary. Somewhere else you see a similar 'anarchy' of codes is in the x86 architecture for processors; there are a ton of \"interrupts\" that can mean different things on certain brands of processors, most of which have been phased out.\n\n## Maintainers\n\n- [Sindre Sorhus](https://github.com/sindresorhus)\n- [Josh Junon](https://github.com/qix-)\n", "readmeFilename": "readme.md", "users": {"nfd": true, "usex": true, "hualei": true, "qddegtya": true, "edwardxyt": true, "justjavac": true, "flumpus-dev": true, "scottfreecode": true, "kailash.yogeshwar": true}}