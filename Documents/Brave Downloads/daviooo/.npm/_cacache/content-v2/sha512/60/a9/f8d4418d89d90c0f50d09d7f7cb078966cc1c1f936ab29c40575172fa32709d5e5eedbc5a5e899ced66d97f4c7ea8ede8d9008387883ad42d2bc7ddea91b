{"_id": "is-array-buffer", "_rev": "16-5ae79ef2a7edf6ce09c32219d4a6870f", "name": "is-array-buffer", "dist-tags": {"latest": "3.0.5"}, "versions": {"0.0.1": {"name": "is-array-buffer", "version": "0.0.1", "keywords": ["type", "object", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "javascript"], "author": {"url": "chenfengyuan.com", "name": "<PERSON><PERSON> Chen"}, "license": "MIT", "_id": "is-array-buffer@0.0.1", "maintainers": [{"name": "feng<PERSON>chen", "email": "<EMAIL>"}], "homepage": "https://github.com/fengyuanchen/is-array-buffer#readme", "bugs": {"url": "https://github.com/fengyuanchen/is-array-buffer/issues"}, "dist": {"shasum": "f60e5ca254e2724071dd6f537d1960d1b9621580", "tarball": "https://registry.npmjs.org/is-array-buffer/-/is-array-buffer-0.0.1.tgz", "integrity": "sha512-Fd9fkI98afDOriXjoP5zL9Tz10hQ5X74AueZfuEfsg54zSly3Oln8d21VdPcbFipUI6ivgkN6snr8+0o5Dn2Pg==", "signatures": [{"sig": "MEYCIQCpf7Fx7kwevoHDU2hxhmNZoHq7avTatWsM182gyzjRpQIhAJhNLzVNSVBHfWXCexBurwKF2CixX/cMXHu+1qCVpQX5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "f60e5ca254e2724071dd6f537d1960d1b9621580", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "feng<PERSON>chen", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/fengyuanchen/is-array-buffer.git", "type": "git"}, "_npmVersion": "3.3.6", "description": "Check if the given value is an ArrayBuffer.", "directories": {}, "_nodeVersion": "5.0.0", "devDependencies": {"mocha": "^2.3.3"}}, "0.1.0": {"name": "is-array-buffer", "version": "0.1.0", "keywords": ["type", "object", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "javascript"], "author": {"url": "chenfengyuan.com", "name": "<PERSON><PERSON> Chen"}, "license": "MIT", "_id": "is-array-buffer@0.1.0", "maintainers": [{"name": "feng<PERSON>chen", "email": "<EMAIL>"}], "homepage": "https://github.com/fengyuanchen/is-array-buffer#readme", "bugs": {"url": "https://github.com/fengyuanchen/is-array-buffer/issues"}, "dist": {"shasum": "9f236b19cb41c749f231696c612c418afebf4705", "tarball": "https://registry.npmjs.org/is-array-buffer/-/is-array-buffer-0.1.0.tgz", "integrity": "sha512-m5n4ynv56iFCjxCq/dEqgk8F4Hof94BEgXdaWCfFUyfcYwfqJllTeR0dT8DZ5rWJlPBMviABPmKLiIq3tPe/lg==", "signatures": [{"sig": "MEUCIH0q/iYwontzcEUmPcTSMVVkq2/CDABoVkeA0S5blNDnAiEA3yn2mPw2KzZUYpkBo6Xg3z+QnHUqCJQYYWy+Emu90hg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "9f236b19cb41c749f231696c612c418afebf4705", "engines": {"node": ">=0.10.0"}, "gitHead": "daa6bc0ee70160af83b113c7bf886fea08b36778", "scripts": {"test": "mocha"}, "_npmUser": {"name": "feng<PERSON>chen", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/fengyuanchen/is-array-buffer.git", "type": "git"}, "_npmVersion": "3.5.0", "description": "Check if the given value is an ArrayBuffer.", "directories": {}, "_nodeVersion": "5.1.0", "devDependencies": {"mocha": "^2.3.3"}}, "1.0.0": {"name": "is-array-buffer", "version": "1.0.0", "keywords": ["is", "array", "buffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "author": {"url": "http://chenfengyuan.com", "name": "<PERSON>"}, "license": "MIT", "_id": "is-array-buffer@1.0.0", "maintainers": [{"name": "feng<PERSON>chen", "email": "<EMAIL>"}], "homepage": "https://github.com/fengyuanchen/is-array-buffer", "bugs": {"url": "https://github.com/fengyuanchen/is-array-buffer/issues"}, "dist": {"shasum": "f32497a0509d109423f472003f98bab6a8ea34cb", "tarball": "https://registry.npmjs.org/is-array-buffer/-/is-array-buffer-1.0.0.tgz", "integrity": "sha512-KtzJzWuC1kZQ377GJbEsoBh0LuQh1uaZnQg8oL2LcDkY/Ny8rpAzu21Ls3oph3SEKXbnrLHt3rAUVm28iuEPfw==", "signatures": [{"sig": "MEUCICKEn9y+kdlZQyKfBnb4HThZiMDl/tXp6bOUuTJbg3rAAiEA+/MtVu80TEwVO32wmrr0T3/M2m0HD++8AIHqspwaBYw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/is-array-buffer.common.js", "module": "dist/is-array-buffer.esm.js", "browser": "dist/is-array-buffer.js", "gitHead": "524f02b7b643183f20eee8a9f05e813defd4f5b5", "scripts": {"lint": "eslint src", "test": "mocha", "build": "rollup -c", "compress": "uglifyjs dist/is-array-buffer.js -o dist/is-array-buffer.min.js -c -m --comments /^!/", "prebuild": "npm run lint", "postbuild": "npm run test && npm run compress"}, "_npmUser": {"name": "feng<PERSON>chen", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/fengyuanchen/is-array-buffer.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Check if the given value is an ArrayBuffer.", "directories": {}, "_nodeVersion": "8.1.4", "devDependencies": {"mocha": "^3.4.2", "eslint": "^4.3.0", "rollup": "^0.45.2", "uglify-js": "^3.0.26", "babel-preset-env": "^1.6.0", "rollup-plugin-babel": "^2.7.1", "eslint-plugin-import": "^2.7.0", "eslint-config-airbnb-base": "^11.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/is-array-buffer-1.0.0.tgz_1501067235237_0.20454894984140992", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "is-array-buffer", "version": "1.0.1", "keywords": ["is", "array", "buffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "author": {"url": "http://chenfengyuan.com", "name": "<PERSON>"}, "license": "MIT", "_id": "is-array-buffer@1.0.1", "maintainers": [{"name": "chenfengyuan", "email": "<EMAIL>"}], "homepage": "https://github.com/fengyuanchen/is-array-buffer", "bugs": {"url": "https://github.com/fengyuanchen/is-array-buffer/issues"}, "dist": {"shasum": "a4fac0cf325c1fad3f713e2b109eb241a907cf27", "tarball": "https://registry.npmjs.org/is-array-buffer/-/is-array-buffer-1.0.1.tgz", "fileCount": 9, "integrity": "sha512-lj035IqdAwsodoRGs9/8+Kn3HPoz9CTuZbcw63afugWonxigvUVeHY5d6Ve1O+s1N3RCk7txo2TIWQLbU0SuNA==", "signatures": [{"sig": "MEUCIQDGhjFGjYlQolSDDFwdHVybayRckvhASuMU5z7aCz0S4wIgOqK+1HS2DmT+ozNoJcbr8SzWCv4MFxmIr2vM4bKlcLc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7491}, "main": "dist/is-array-buffer.common.js", "files": ["src", "dist"], "unpkg": "dist/is-array-buffer.js", "module": "dist/is-array-buffer.esm.js", "gitHead": "db7892b4312b897b9f071c41f906eac9757c1bd1", "scripts": {"lint": "eslint src *.js --fix", "test": "nyc mocha", "build": "rollup -c", "release": "npm run lint && npm run build && npm run compress && npm run test", "compress": "uglifyjs dist/is-array-buffer.js -o dist/is-array-buffer.min.js -c -m --comments /^!/", "coverage": "nyc report --reporter=text-lcov > coverage.lcov && codecov"}, "_npmUser": {"name": "chenfengyuan", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/fengyuanchen/is-array-buffer.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Check if the given value is an ArrayBuffer.", "directories": {}, "_nodeVersion": "9.8.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.6.0", "mocha": "^5.0.5", "eslint": "^4.19.1", "rollup": "^0.57.1", "codecov": "^3.0.0", "uglify-js": "^3.3.17", "babel-core": "^6.26.0", "babel-preset-env": "^1.6.1", "rollup-plugin-babel": "^3.0.3", "eslint-plugin-import": "^2.10.0", "eslint-config-airbnb-base": "^12.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-array-buffer_1.0.1_1522567879803_0.23770064839453564", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "is-array-buffer", "version": "2.0.0", "keywords": ["is", "array", "buffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "author": {"url": "https://chenfengyuan.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "is-array-buffer@2.0.0", "maintainers": [{"name": "chenfengyuan", "email": "<EMAIL>"}], "homepage": "https://github.com/fengyuanchen/is-array-buffer/#readme", "bugs": {"url": "https://github.com/fengyuanchen/is-array-buffer/issues"}, "dist": {"shasum": "4fd60658707917c2d60ffac40550f56a02408377", "tarball": "https://registry.npmjs.org/is-array-buffer/-/is-array-buffer-2.0.0.tgz", "fileCount": 7, "integrity": "sha512-O7i/ai40oYpKKdGc4svMkDTeZpYeYuX3QhqD4nPMnwJ1XSEeYn84FI96hnp87YUst6V1fLSWXh5qMosIRLLDQw==", "signatures": [{"sig": "MEUCIBfUZe6AwBWwFrYLuvK5a1LrG6c+9cQfjAYOSZAPz10PAiEArTbbcXleLZorXZU491qYJddk+1GEOWmzEz1dHzJMvhg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5988, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgJkXcCRA9TVsSAnZWagAAsR0P/j20kBnZ6Ig0QRyRRqgN\nKDEHUcY8sJsQDziSez+3MUjWc4kUSfhpej31fJ0hWxZbHLyjkmWj/ax6g44w\nOxOJycN6qu/GkMtqhND8igFtmdICjmc6ST4WNFPl0Su5pkFW0FvVc5hxI1Yc\n9Fc+igLlVJdOZ9R7YIAo9d+0ckq4i992A4u8Cm0piK8tvF844XpDWP26kK24\nVa9wP1AU0daOoo5ngZyUKhp8OpsbgMBCdsfXhxmat02muJzasCes+MlhqBcb\nROL493Luf7q715fUas3mYByehdBDfpLNj63vkpfuxcz3RnCFVlaMDOJ7TsuB\nDqQKLs1vv9e6kmVIh1Nzg7IXBBHVZU4blyYfC6woYACwaUGr6vsp5fwyWwUR\nwhD7Mecbz5En0v75SACadJTXcdrcoQaeFhThI9DaC474jQdxtCaOoeLnluHY\nAiW8L66fQe4vHX5tCeqrpXAOgbmrfgGUpPIw46eOpwxcuuG2GI3W34BTQ/Lj\nV59usq0sMQTb4PbVK7xpwS0qNSNBz/xB8DT7JNJ3eZILhkx61ZEdcRCDjJnH\n/UUCThXQOLeKDuq6jy0Nbw80DnoEjqyaKj30TdAEF8a6/rvxppasxh09Ynl2\n3HOOra0Z/fZdrGRykeTyZkcXZPDioeWaXBWVaDcNhO3a4Bf1yCh4Dj+ecQzB\nbBCp\r\n=MMGD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "index.d.ts", "module": "index.esm.js", "gitHead": "ee3b16f5c10e46a54f1bdf80721f276faa43578c", "scripts": {"lint": "eslint . --ext .js,.ts --fix", "test": "nyc mocha", "build": "rollup -c", "clean": "del-cli index.js index.esm.js index.d.ts", "release": "npm run clean && npm run lint && npm run build && npm run build:dts && npm test", "build:dts": "tsc --declaration --emitDeclarationOnly", "test:coverage": "nyc report --reporter=text-lcov > coverage.lcov && codecov"}, "_npmUser": {"name": "chenfengyuan", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/fengyuanchen/is-array-buffer.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Check if the given value is an ArrayBuffer.", "directories": {}, "_nodeVersion": "12.18.2", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "mocha": "^8.3.0", "eslint": "^7.19.0", "rollup": "^2.38.5", "codecov": "^3.8.1", "del-cli": "^3.0.1", "typescript": "^4.1.5", "eslint-plugin-import": "^2.22.1", "@rollup/plugin-typescript": "^8.1.1", "@typescript-eslint/parser": "^4.15.0", "eslint-config-airbnb-typescript": "^12.3.1", "@typescript-eslint/eslint-plugin": "^4.15.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-array-buffer_2.0.0_1613120988321_0.9845847387300155", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "is-array-buffer", "version": "3.0.0", "keywords": ["javascript", "ecmascript", "is", "arraybuffer", "array", "buffer"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-array-buffer@3.0.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "chenfengyuan", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-array-buffer#readme", "bugs": {"url": "https://github.com/inspect-js/is-array-buffer/issues"}, "dist": {"shasum": "d93aaf24dade5e5391e2992e884c378482f078c1", "tarball": "https://registry.npmjs.org/is-array-buffer/-/is-array-buffer-3.0.0.tgz", "fileCount": 9, "integrity": "sha512-TI2hnvT6dPUnn/jARFCJBKL1eeabAfLnKZ2lmW5Uh317s1Ii2IMroL1yMciEk/G+OETykVzlsH6x/L4q/avhgw==", "signatures": [{"sig": "MEQCICmX3T0QVUT/ViXvn4PT/3EkExm4TRs3eUWU0NhOgRMUAiACHgWVE4x1f1dx0Imx9kT9e/tv5+m4yNep5TZykNWnJg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10033, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtbvAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoDng//cxp9tVOu6PIST8bgJ7H2j9VqgbrJtaVv5+FOc2chpOP/leNH\r\nnQBUEpotKMzP3Ns//EuVRXRXzPGojTbIeJlvZf9jJ11/X7qND8lNcPgm4E+X\r\n1DxkPeZtufZYJUMYoaHOhUshj27BC4zkW3nHUBQ3WHSJNxo3mSmfxmrucanS\r\nTdgdE9t/kYVv9gY+4c3LU3tzZ29TRKNuVbYIaTztqdeGsvsVCOw5LehkL+zN\r\nzlA0nWJm2Z0Cq3/7l9fsUo0RMU33f+n3AXMUDtkABZEvdS3ZGosuRkzsyUEp\r\nDUeoCShH7xAAJJ/5QQUhGSQ7fVg9fmfEoEpI+QN7TDZ7KRA7XLOGPLKHgJGw\r\nm01DafkKVWzJJGgAbnVIL4ncw2L7/s6cO1Gl2OYNUOBOGFwU8oQcklxDYzbG\r\noIqW+N8Jj3TO+mB6WwFc1OFBKfd/iL+Voadzdi3fAC1wtVUAEeTExRzBRLqR\r\nXCPCeCxoqepadpjcWbe/lxhL831B+sklY/rSCWzbR8kvOqylBNKhv2q6mZW9\r\n/uQHnjRaWgXgcYfQ02DGTK8LexQntIJcLB/tRMo6tf8AnR2QwmC29K6Ql9IU\r\nQG8h7Y/Y9zos9EoGvvTVivGISB1DUCXIibNokoAJDHiJxMcsmylPy+6mo+NN\r\n69OLqix6WxldahJMWxAzQakBDZodCcL7/74=\r\n=rvF3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "04f2eb4ac37f1399df77e4cdb05c2c04ff199494", "scripts": {"lint": "eslint --ext=.js,.mjs .", "test": "npm run tests-only --", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspect-js/is-array-buffer.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Is this value a JS ArrayBuffer?", "directories": {}, "sideEffects": false, "_nodeVersion": "19.3.0", "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.1.3"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false, "startingVersion": "2.0.1"}, "devDependencies": {"aud": "^2.0.1", "nyc": "^10.3.2", "tape": "^5.6.1", "eslint": "=8.8.0", "for-each": "^0.3.3", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "auto-changelog": "^2.4.0", "object-inspect": "^1.12.2", "es-value-fixtures": "^1.4.2", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/is-array-buffer_3.0.0_1672854464452_0.36842059998929955", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "is-array-buffer", "version": "3.0.1", "keywords": ["javascript", "ecmascript", "is", "arraybuffer", "array", "buffer"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-array-buffer@3.0.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "chenfengyuan", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-array-buffer#readme", "bugs": {"url": "https://github.com/inspect-js/is-array-buffer/issues"}, "dist": {"shasum": "deb1db4fcae48308d54ef2442706c0393997052a", "tarball": "https://registry.npmjs.org/is-array-buffer/-/is-array-buffer-3.0.1.tgz", "fileCount": 9, "integrity": "sha512-ASfLknmY8Xa2XtB4wmbz13Wu202baeA18cJBCeCy0wXUHZF0IPyVEXqKEcd+t2fNSLLL1vC6k7lxZEojNbISXQ==", "signatures": [{"sig": "MEQCIFpnBgxEmwtTo6Gz/x4Iq/QediPJG2vDsGsUoOMUOYvLAiBtrvIaoywtr0phOnG6h4MkG5zHbB5gYRa6BLSV0ng2tA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11085, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjt0z1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq/qw/+Mn1SPdU/mneQZ3+ft7mQ/sajFqMKVAfBU/aHK6X8G95/87Ws\r\noQ0rcvFf6Q/+OdozmvyILudIZl4QkmCozkGfhh4NZUqV5aYqcWnWvBVZzwYZ\r\nehRqczSNdc0cHksLnZfu2pUZh6ekEZSoLakg7Ts8AePdVzeqQSMjL6jsslTU\r\nVAgZkZnqGKtp/geVTFkLaoWt6XZdHdRoNTeKLm9RNvu/HJ8jt87ZRMymkHPm\r\nunyr/qJPV042YVDjsfM86IGIZeqHGnKC4GPoU/TOFvvXaCLoZDs5P6ScAUIs\r\n9ksE4SM6QptT1a5qXQB7i/RxmoDcBZpnFkp8pENLDBG9OqEqa98RwHC6CkQY\r\nHzTpJAwddjLqFgo4i4bkvdfnSRM4nDGfc4nUGzd1uESdNoX6xNM4EtMdmywO\r\n5Ds18RefS1ZwzWj42Bo5TGtG2uGrEZVDD1RNzg+R0q6vdz9QpUGdxDZuF3Mz\r\n/Y2tT2PJ4gwzuIg7wGyFjtCx1TfiXFWXS1Egf7xvDMKy9GNhJEPg+8nh/QXw\r\nsZXsxZkxqNSU+oEjPsOiWS1E3zBbYoBMHvki2A//Ef4NX/LGkrxbd/msZnkm\r\nz0v1gVOxTj4IMlhHi4hnhSOuYmpu4fqNgXcDHGrjt/Eim9YElnwJ5c1mbJn4\r\nLNPkbZt79ynvbFVjIDHxHL7RwqUPNKQhJuY=\r\n=N+UI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "1ac0ffe4f48f86e9cb1777bdb972d1bc47bf3225", "scripts": {"lint": "eslint --ext=.js,.mjs .", "test": "npm run tests-only --", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspect-js/is-array-buffer.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Is this value a JS ArrayBuffer?", "directories": {}, "sideEffects": false, "_nodeVersion": "19.3.0", "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.1.3", "is-typed-array": "^1.1.10"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false, "startingVersion": "2.0.1"}, "devDependencies": {"aud": "^2.0.2", "nyc": "^10.3.2", "tape": "^5.6.1", "eslint": "=8.8.0", "for-each": "^0.3.3", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "auto-changelog": "^2.4.0", "object-inspect": "^1.12.2", "es-value-fixtures": "^1.4.2", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.0.1", "available-typed-arrays": "^1.0.5"}, "_npmOperationalInternal": {"tmp": "tmp/is-array-buffer_3.0.1_1672957173035_0.9324456047698708", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "is-array-buffer", "version": "3.0.2", "keywords": ["javascript", "ecmascript", "is", "arraybuffer", "array", "buffer"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-array-buffer@3.0.2", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "chenfengyuan", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-array-buffer#readme", "bugs": {"url": "https://github.com/inspect-js/is-array-buffer/issues"}, "dist": {"shasum": "f2653ced8412081638ecb0ebbd0c41c6e0aecbbe", "tarball": "https://registry.npmjs.org/is-array-buffer/-/is-array-buffer-3.0.2.tgz", "fileCount": 9, "integrity": "sha512-y+FyyR/w8vfIRq4eQcM1EYgSTnmHXPqaF+IgzgraytCFq5Xh8lllDVmAZolPJiZttZLeFSINPYMaEJ7/vWUa1w==", "signatures": [{"sig": "MEUCIC7+hT73tI4k34aMjmraLNWXoQgg+/IR6pKA/BDn8uY1AiEApztM87pytdVWCB1q8hA8zIXhIEUwb4kEEMF1ryAHeHA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11882, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkADs8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq/FxAAnn9Ti45OCD5rczrS/sc7+3Bv7XZsrqoURqkyTp8gc58ktnsq\r\ndTFYg4kieoiTZ9dq4YjLoiXZv6vQvceEURc6x+ipHCR2gXzHVN9n/G6sRWPf\r\nPX4JShrMKamvcXXY6vpZiBUSN+71hDuHZaIz1zNe6jLEj1bNk8eeuL+qhKNI\r\ntpjRlOzLA+2PWi4ffKNIDlRF8+O2DvZVG3ogFha8mlU5cazxv6N/5oOsHc/Y\r\nd/lny0Fho1Ta7l8HtEYu6M7STu3FH7lvmq5l5jjH4wVfXUd0m519f9my2XTT\r\nE/4TXVHxITrIyCXoUa25oSALdFwCsjRX/lcbSXpwamBot3GgJ4iLOdo1h/JV\r\nmEfHX8H3Vp6sYrP20gSDaYhOdFqlaQCy9BQfbPLY67UscHvftPsU/E85n8Qw\r\ns3H5Tox8pil9ZYcFYb7kXpbgTPuycRmhRhbVUFqIffEMqdm7lFdRbI1VeI7Y\r\nunDt4aiVIRLLgtip8F34vmNBd16f5e9IMFm+AvPxH10QTJlcl3DN9YEgSPDE\r\nGVp2F/hnEurE5gv0DQ76hrEUG850Wiyz7ykdUXwWgoOGWA+dWl/wo8ds7uI0\r\nk5Lf6ymk/ML8IpVaOiH6GqHktxXeU/ZArpAj9wUnqGjhLmvVXYyUE410SyCf\r\nLDw5qYm/7I3okq4F/7V5+8lXo0nQ/NVJIcU=\r\n=bq2o\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "87a13652c4361a9d1ae8cbcde27fc836e3de6d32", "scripts": {"lint": "eslint --ext=.js,.mjs .", "test": "npm run tests-only --", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspect-js/is-array-buffer.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "Is this value a JS ArrayBuffer?", "directories": {}, "sideEffects": false, "_nodeVersion": "19.7.0", "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.2.0", "is-typed-array": "^1.1.10"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false, "startingVersion": "2.0.1"}, "devDependencies": {"aud": "^2.0.2", "nyc": "^10.3.2", "tape": "^5.6.3", "eslint": "=8.8.0", "for-each": "^0.3.3", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "auto-changelog": "^2.4.0", "object-inspect": "^1.12.3", "es-value-fixtures": "^1.4.2", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.0.1", "available-typed-arrays": "^1.0.5"}, "_npmOperationalInternal": {"tmp": "tmp/is-array-buffer_3.0.2_1677736764324_0.13093851564276804", "host": "s3://npm-registry-packages"}}, "3.0.3": {"name": "is-array-buffer", "version": "3.0.3", "keywords": ["javascript", "ecmascript", "is", "arraybuffer", "array", "buffer"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-array-buffer@3.0.3", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "chenfengyuan", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-array-buffer#readme", "bugs": {"url": "https://github.com/inspect-js/is-array-buffer/issues"}, "dist": {"shasum": "96e256e36f2861d87464ecb2b0875dddec7d4fdf", "tarball": "https://registry.npmjs.org/is-array-buffer/-/is-array-buffer-3.0.3.tgz", "fileCount": 9, "integrity": "sha512-4uU8qe5vJcW6tiP6HfaXsXPDN9pkcrp8DBCrvCkHQS08xeDEdLNbshLfIydPf3x7VDtGYKad/GkX3ujNfgxPaw==", "signatures": [{"sig": "MEYCIQC1L4NZmj2K8m5ebGe68B8YD5L/SBXE8RB+2ckMMf1QFAIhAPMGe4baKWsjp+wYvR0rUubSjFj/mpHjYV0a6FCmWVDf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13021}, "main": "index.js", "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "3d0ceea8fa4deba5cc57c00e0d8680d569ee0505", "scripts": {"lint": "eslint --ext=.js,.mjs .", "test": "npm run tests-only --", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspect-js/is-array-buffer.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Is this value a JS ArrayBuffer?", "directories": {}, "sideEffects": false, "_nodeVersion": "21.6.0", "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.2.1"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false, "startingVersion": "2.0.1"}, "devDependencies": {"aud": "^2.0.4", "nyc": "^10.3.2", "tape": "^5.7.4", "eslint": "=8.8.0", "for-each": "^0.3.3", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "auto-changelog": "^2.4.0", "object-inspect": "^1.13.1", "es-value-fixtures": "^1.4.2", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.1.0", "available-typed-arrays": "^1.0.6"}, "_npmOperationalInternal": {"tmp": "tmp/is-array-buffer_3.0.3_1706937873593_0.1575085415209403", "host": "s3://npm-registry-packages"}}, "3.0.4": {"name": "is-array-buffer", "version": "3.0.4", "keywords": ["javascript", "ecmascript", "is", "arraybuffer", "array", "buffer"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "is-array-buffer@3.0.4", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "chenfengyuan", "email": "<EMAIL>"}], "homepage": "https://github.com/inspect-js/is-array-buffer#readme", "bugs": {"url": "https://github.com/inspect-js/is-array-buffer/issues"}, "dist": {"shasum": "7a1f92b3d61edd2bc65d24f130530ea93d7fae98", "tarball": "https://registry.npmjs.org/is-array-buffer/-/is-array-buffer-3.0.4.tgz", "fileCount": 11, "integrity": "sha512-wcjaerHw0ydZwfhiKbXJWLDY8A7yV7KhjQOpb83hGgGfId/aQa4TOvwyzn2PuswW2gPCYEL/nEAiSVpdOj1lXw==", "signatures": [{"sig": "MEYCIQD3rQFChGVzW9jL9iin07qTXWlpI+9cRVVG6uuX+bBk8gIhALi8fKPj1dJvD9L+9XCopd9jYw2SYzEt9pagv85bk3r/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17639}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "9d1576d22a154370cb4e82118cedb32e63bf10a3", "scripts": {"lint": "eslint --ext=.js,.mjs .", "test": "npm run tests-only --", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "tsc -p .", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspect-js/is-array-buffer.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Is this value a JS ArrayBuffer?", "directories": {}, "sideEffects": false, "_nodeVersion": "21.6.0", "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.2.1"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false, "startingVersion": "2.0.1"}, "devDependencies": {"aud": "^2.0.4", "nyc": "^10.3.2", "tape": "^5.7.4", "eslint": "=8.8.0", "for-each": "^0.3.3", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "next", "@types/tape": "^5.6.4", "auto-changelog": "^2.4.0", "object-inspect": "^1.13.1", "@types/for-each": "^0.3.3", "@types/call-bind": "^1.0.5", "es-value-fixtures": "^1.4.2", "safe-publish-latest": "^2.0.0", "@types/get-intrinsic": "^1.2.2", "@ljharb/eslint-config": "^21.1.0", "@types/object-inspect": "^1.8.4", "available-typed-arrays": "^1.0.6", "@types/es-value-fixtures": "^1.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/is-array-buffer_3.0.4_1706941599160_0.256392653165191", "host": "s3://npm-registry-packages"}}, "3.0.5": {"name": "is-array-buffer", "version": "3.0.5", "description": "Is this value a JS ArrayBuffer?", "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "types": "./index.d.ts", "sideEffects": false, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "lint": "eslint --ext=.js,.mjs .", "postlint": "tsc && attw -P", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only --", "posttest": "npx npm@'>= 10.2' audit --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/is-array-buffer.git"}, "keywords": ["javascript", "ecmascript", "is", "arraybuffer", "array", "buffer"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/is-array-buffer/issues"}, "homepage": "https://github.com/inspect-js/is-array-buffer#readme", "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/call-bind": "^1.0.5", "@types/for-each": "^0.3.3", "@types/get-intrinsic": "^1.2.3", "@types/object-inspect": "^1.13.0", "@types/tape": "^5.7.0", "auto-changelog": "^2.5.0", "available-typed-arrays": "^1.0.7", "encoding": "^0.1.13", "es-value-fixtures": "^1.5.0", "eslint": "=8.8.0", "for-each": "^0.3.3", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.3", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true, "startingVersion": "2.0.1"}, "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "get-intrinsic": "^1.2.6"}, "publishConfig": {"ignore": [".github/workflows"]}, "engines": {"node": ">= 0.4"}, "_id": "is-array-buffer@3.0.5", "gitHead": "415b6eb1aae305496f376f008e4cf77da0a4d778", "_nodeVersion": "23.4.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-DDfANUiiG2wC1qawP66qlTugJeL5HyzMpfr8lLK+jMQirGzNod0B12cFB/9q838Ru27sBwfw78/rdoU7RERz6A==", "shasum": "65742e1e687bd2cc666253068fd8707fe4d44280", "tarball": "https://registry.npmjs.org/is-array-buffer/-/is-array-buffer-3.0.5.tgz", "fileCount": 11, "unpackedSize": 16118, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD9gMWNDQQ1g1bjxEeiF1lJWMkR+O+I1xFJEGnMLcM7zQIgPrTOigRyzOhVMMXS1sEygpUOb2N60P6/SHv3R8hJyJw="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "chenfengyuan", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/is-array-buffer_3.0.5_1734367606972_0.35052660016588355"}, "_hasShrinkwrap": false}}, "time": {"created": "2015-11-11T11:09:28.558Z", "modified": "2024-12-16T16:46:47.342Z", "0.0.1": "2015-11-11T11:09:28.558Z", "0.1.0": "2015-11-28T12:32:00.596Z", "1.0.0": "2017-07-26T11:07:15.316Z", "1.0.1": "2018-04-01T07:31:19.876Z", "2.0.0": "2021-02-12T09:09:48.440Z", "3.0.0": "2023-01-04T17:47:44.615Z", "3.0.1": "2023-01-05T22:19:33.205Z", "3.0.2": "2023-03-02T05:59:24.498Z", "3.0.3": "2024-02-03T05:24:33.743Z", "3.0.4": "2024-02-03T06:26:39.320Z", "3.0.5": "2024-12-16T16:46:47.152Z"}, "bugs": {"url": "https://github.com/inspect-js/is-array-buffer/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/inspect-js/is-array-buffer#readme", "keywords": ["javascript", "ecmascript", "is", "arraybuffer", "array", "buffer"], "repository": {"type": "git", "url": "git+https://github.com/inspect-js/is-array-buffer.git"}, "description": "Is this value a JS ArrayBuffer?", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "chenfengyuan", "email": "<EMAIL>"}], "readme": "# is-array-buffer <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\nIs this value a JS ArrayBuffer? This module works cross-realm/iframe, does not depend on `instanceof` or mutable properties, and despite ES6 Symbol.toStringTag.\n\n## Example\n\n```js\nvar assert = require('assert');\nvar isArrayBuffer = require('is-array-buffer');\n\nassert(!isArrayBuffer(function () {}));\nassert(!isArrayBuffer(null));\nassert(!isArrayBuffer(function* () { yield 42; return Infinity; });\nassert(!isArrayBuffer(Symbol('foo')));\nassert(!isArrayBuffer(1n));\nassert(!isArrayBuffer(Object(1n)));\n\nassert(!isArrayBuffer(new Set()));\nassert(!isArrayBuffer(new WeakSet()));\nassert(!isArrayBuffer(new Map()));\nassert(!isArrayBuffer(new WeakMap()));\nassert(!isArrayBuffer(new WeakRef({})));\nassert(!isArrayBuffer(new FinalizationRegistry(() => {})));\nassert(!isArrayBuffer(new SharedArrayBuffer()));\n\nassert(isArrayBuffer(new ArrayBuffer()));\n\nclass MyArrayBuffer extends ArrayBuffer {}\nassert(isArrayBuffer(new MyArrayBuffer()));\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n[package-url]: https://npmjs.org/package/is-array-buffer\n[npm-version-svg]: https://versionbadg.es/inspect-js/is-array-buffer.svg\n[deps-svg]: https://david-dm.org/inspect-js/is-array-buffer.svg\n[deps-url]: https://david-dm.org/inspect-js/is-array-buffer\n[dev-deps-svg]: https://david-dm.org/inspect-js/is-array-buffer/dev-status.svg\n[dev-deps-url]: https://david-dm.org/inspect-js/is-array-buffer#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/is-array-buffer.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/is-array-buffer.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/is-array-buffer.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=is-array-buffer\n[codecov-image]: https://codecov.io/gh/inspect-js/is-array-buffer/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/inspect-js/is-array-buffer/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/inspect-js/is-array-buffer\n[actions-url]: https://github.com/inspect-js/is-array-buffer/actions\n", "readmeFilename": "README.md"}