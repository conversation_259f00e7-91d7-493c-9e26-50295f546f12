{"name": "openai", "dist-tags": {"next": "4.0.0-beta.12", "alpha": "5.0.0-alpha.0", "latest": "4.78.1"}, "versions": {"0.0.0": {"name": "openai", "version": "0.0.0", "dist": {"shasum": "8de6a226b7f7ac71ae4e826e9e3d51cf9adf2dc6", "tarball": "https://registry.npmjs.org/openai/-/openai-0.0.0.tgz", "fileCount": 2, "integrity": "sha512-zMfA6bKY9mYD5+49ozQd3eKmVwIAlPmUNsn+ShgWaMzIy1DlTXnEgKQt/iD5YZr38qwQN/JDHdieV6l1nEB9jg==", "signatures": [{"sig": "MEUCIQCsa3ephGkXZaY+2h7S6rpmkvJay92N4iSHAUmeJ3QyoAIgXhh+x3+jmroqHH+SGjJ/g7tvmcOOi2PjGr+3GLNfxiQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfBxw9CRA9TVsSAnZWagAAucEP/ia8NxNyN+238V5MYKeY\njII0ii7mp89qtNci/T3/HGVZe9VByGqCPiHPRFGBywzggaCpnwSDZEjTHmL1\nR8ZynNVd5j+MrRdZo8CAlIf6QzqCXz6qyO3U6rkUschQ1c3Fopv5tVWOV/yU\nwArwG5mwlrRAA4tfgPvTiZyBaGUxgL+TNrCjLZb6D3vvp3pWgcC7OsvrIv+V\nn/v6d3JJHBjr28nqAKaxT/MnUTHayZsyDsX6gi2jKH2LCwd0KupLsAj3r5es\nADPilFE02mjOJ2aezBDhbuBTYLj5pjB0nCG+i7FPUNMmuNeUCyIkadsNPgZN\nRF0JGBD0bUnXPiWuZNzKYDSwz94hBzzsOngMM1Y4juFpfImMUUQJFRdWZMO5\nzg9n5zV9WkbU+W801t/u/2tLXMzIGZ9zBRS3A5wqtHRevZ9rWXTlv7cTRgPu\nypSqiWnUU+Xr1x42NHhbVGYrCaguRRSSsuRouqh0dL1tL5NrsMhzlH4knQV4\nG7vgToBJzZJDNWeZZxQ4+plJpTjkVP3xZLGcrZWufY+nOTSGqWhqYKJTFeLh\nHoXPTLfFV9ZJhQvPGNzgro5Ijf+L8YebGafnR3iiplMqXK8kW5IQHGHpKZTm\nLkVlddnu4jh44mKui7AQp9udA02JiCxDNCpvyf8U0qXpPzjQoJ0vntC9c/fu\nncfu\r\n=/5IF\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.1": {"name": "openai", "version": "0.0.1", "dependencies": {"node-fetch": "^2.6.1"}, "devDependencies": {"tslib": "2.3.0", "eslint": "7.31.0", "prettier": "2.3.2", "typescript": "4.3.5", "@types/node-fetch": "^2.5.12", "eslint-config-prettier": "8.3.0", "eslint-plugin-prettier": "3.4.0", "@typescript-eslint/parser": "4.28.5", "@typescript-eslint/eslint-plugin": "4.28.5", "eslint-plugin-sort-imports-es6-autofix": "0.6.0"}, "dist": {"shasum": "a5b0138ba5db5e0733c5e2e8d9e166dff0717b12", "tarball": "https://registry.npmjs.org/openai/-/openai-0.0.1.tgz", "fileCount": 11, "integrity": "sha512-Fe/vlHeERjhIlsLuEIOVcTpxZ/08GmOXZgM0Yq0OGauegD/5PcriNKtYzJvf6V3Wg7ZEjxmYirSC1F0TK04PAQ==", "signatures": [{"sig": "MEYCIQDHV8LKTB4yI4R5XUpvhSqnM+grVgiXJy02/QjfcPL7iQIhAN/Sa75faqY/aHd8ZRUknE7qlliKK3KYXHKVEvv8s+hz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10666, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhAF9oCRA9TVsSAnZWagAAj8YP/R12IQKcvS68exzm6xE/\nONfydf6NfTZ1ic4PfPZjWik0i+MCHrD6A3V2SkLu7SlHrVzHL2tu02jts4IL\nChumjOs3psKn5i8uEtSMB5Sy8w0I0j6lhXYDPXG5CYiWsKTyRE1fxlPEu9CH\n7waZ76A7ZpMamoIq7dOtG/01E9Fxla+5hEro/s5lbwnSG1cwMLY5Z47D3Cdw\n2i2dQWHpwHkt5vd9CAQP+VYJOW2HA/iAd0lqyz7iLtnyHHxVY1zLT+lCcCeT\nbTPEgPSsVLfv5Yn1olrjYvYEtPMMqIUIJ7Bub9Lzev287kxLmPzQB2CuiCGx\n3Yl7Krba1UsN+zG3bI8+Wng7RladtRBy2ytOyF/nVrAuJVFG8dhcNMaO/MdM\nlckv0VvLQnkYHFGpFx3ZD9Io/Aaa3xOqImisb6cNYNkYRAQat8HhIg2t4dRu\nXeQcsIqwmMg2oVYHVctOT91WP9Hjl92NS5K5U4t2+BconvWeXyN2c8Dv317O\nXUKkpwGsZnab/GnNK/7e6rf7lFOodr6Q1rXp+sFjpuzpoJvGM/VgXmS4knIT\niUJjABxaWrbipODUT8G3F17OGrWGwiV0y6JCTAF3FWwgjy1a9OKuzyARlq5+\nKepxGiCgoSGloyLGgN4RpmuO84TTJTB3APi5kBCQVK+EJUY3L6cWOIyO22oZ\nrIll\r\n=tkF/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.2": {"name": "openai", "version": "0.0.2", "dependencies": {"tslib": "2.3.0", "form-data": "4.0.0", "node-fetch": "2.6.1"}, "devDependencies": {"eslint": "7.32.0", "prettier": "2.3.2", "typescript": "4.3.5", "@types/node-fetch": "2.5.12", "eslint-config-prettier": "8.3.0", "eslint-plugin-prettier": "3.4.0", "@typescript-eslint/parser": "4.29.0", "@typescript-eslint/eslint-plugin": "4.29.0", "eslint-plugin-sort-imports-es6-autofix": "0.6.0"}, "dist": {"shasum": "234b9643d92e953c00c137c5f46798c29a8f6d5b", "tarball": "https://registry.npmjs.org/openai/-/openai-0.0.2.tgz", "fileCount": 11, "integrity": "sha512-KXgQ50jLPCvdWtI1SPxYmkiW6slYzHMkTqd5vOjpmxR7og/G89dk/0dGcgNTk8zt0E7MPKR+hQPToVntwvUKNQ==", "signatures": [{"sig": "MEUCIHhCmHFu89iun6AR56QDU5beyZtF5C8QHT8p3bo/qNx+AiEAgV7SB2N2VtJFjZfCz354Om8DiWBYOGcTAU7/8si90YQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18618, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhDVorCRA9TVsSAnZWagAAHSIQAJD659ASuidZqXcgAFb9\nvSlpACSWuc7sQnGD5Ya/GaLXtIh7WgQZ3cI2TiUnuKnfs9Ed8YsoaItQ7icq\n6YCvi36nfIh++owTaAowqJ+Ne/3h74OE/cRDThqnas4ZjkiDyp8ZECysN5Hf\n8F5UmBjoVE0J7UA2jiWgljW/TGyAj5ejaDVOuggWk72up5jDV3OmSLVPaI+9\nFNDF47N4DwU80JVHLJVRlXqziEOmn9ZhGXLjzyZysARMsdODcNYWOTG5H+6c\nsVmyIiTUzbNDhH7i6HWqlAeCElQjseaGpEVL6z5Q6jyuD2Ljp3P/R1WjH/bc\nOPYKzrXtKi9KMWJIL/EUXu08HmQLVL+NfYXBos7B/Or78045j5Y/rmyySQZo\nki8HrEYyI2LWda5TJyQtzTPkRQMq8KC64MIFgvIxBzND7dt0icGzchJz2WEz\n7EWugQNIfIKby9WetieVX7SUS8oGOSDCoG3CW9MKgxlTfiFz+Vy4g3cBkjJ/\nAICd0kccErDUacgBupq90Mib559rB/EeYFiWrxJe9LsgT5IqZ7QTmXSZtxkm\ncSKbQkf6UhPjilDg8nE09FJefrqFm9Jt8hm+w+AvJA/XdupFEc/YxOUCW6Ze\nfClQL/eaVINOorjBopbq4W35A70KGqxMgtLFs7byDzWA6AKgLpTWvS+Gjdkg\naAFN\r\n=6yNj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.3": {"name": "openai", "version": "0.0.3", "dependencies": {"tslib": "2.3.0", "form-data": "4.0.0", "node-fetch": "2.6.1"}, "devDependencies": {"eslint": "7.32.0", "prettier": "2.3.2", "typescript": "4.3.5", "@types/node-fetch": "2.5.12", "eslint-config-prettier": "8.3.0", "eslint-plugin-prettier": "3.4.0", "@typescript-eslint/parser": "4.29.0", "@typescript-eslint/eslint-plugin": "4.29.0", "eslint-plugin-sort-imports-es6-autofix": "0.6.0"}, "dist": {"shasum": "462e3f19112e88069e397e3b3b389a950e43772e", "tarball": "https://registry.npmjs.org/openai/-/openai-0.0.3.tgz", "fileCount": 8, "integrity": "sha512-6NUvO9Ha0xO5EKCWOHZ68aOaIWB+gMv/PwdEKJzBtl4SBloZ9VeYrc1RbxAy3Q9q2XGxVOSuNXg5/BQKGbpZlQ==", "signatures": [{"sig": "MEUCIQDJK1OMuQgyCCLNoOQb9/loc8WRWgWFTYd9IcXxP/lAJwIgT1er2TTIIh+p25YH36h6CFgtr3n2O6oJqFOGuIOcFMk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12730, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhDV1DCRA9TVsSAnZWagAAAvQP/1dERXhElEul4NbEtoh5\n5bgu0slag03lj9z+yTsLVLbmBX+5F9w6wZrIzoDBgfv9jViXLwcKKnQIECgC\ntrIDuDL5IbxCXVuPS1IAudMgZnQS0iJ6HNzwwNU+psZ/61POTiKGjRjEJbmM\nEUe5tX8Xyjz9K8+SSjMfPju4A9+Roi6TCkx6oHuAUcoZQawnRe5Hkrn0e6gg\nK9997M21eaB66SxZFZjGcbuwlZGWZUDOPeghFZcwF+Sf+64BIQvPthJGiKim\nHPAWO<PERSON>+vm2i71cdbpcJnj93h5etlS5R8idHQiKKMbpW1UHq06KLbaqmJBRhW\nS2BfR9XMcSf3l1S7V54EmL2xI6h3DDTa1W4/Qh+CslE8WAOWeIzAY22CLcsb\nX1SpwbvAWUqUKRt1nOeRn5T3KwD8dkO3lDZqtxm89a3k5oPptoPOpECZ4cXA\nBQkrv7PnULOdUJBhH3xfIj7FfrIldhcIvmydKT9QhT+yFFfKAyzZqeyc/Dfq\n+Ne/LRO14jAyVaQ3wV8Ly1jrEG6+FifVsy928bxK35683BxprMTqZOVoqFVT\nvRU19DJv0ZXcckQ3zUNeZyzvG1YlR/0SSjFjnepHfK4JPE1aZRoIJemPEXjU\nRBUs+1G3HKHOR9ox8iiUTXkUTUYeZ2FpmjdISBaJcD4PchNxxAsMWSWWsT5v\n7wey\r\n=dLyv\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.4": {"name": "openai", "version": "0.0.4", "dependencies": {"tslib": "2.3.0", "form-data": "4.0.0", "node-fetch": "2.6.1"}, "devDependencies": {"eslint": "7.32.0", "prettier": "2.3.2", "typescript": "4.3.5", "@types/node-fetch": "2.5.12", "eslint-config-prettier": "8.3.0", "eslint-plugin-prettier": "3.4.0", "@typescript-eslint/parser": "4.29.0", "@typescript-eslint/eslint-plugin": "4.29.0", "eslint-plugin-sort-imports-es6-autofix": "0.6.0"}, "dist": {"shasum": "f937c4b06fc97044036407d11d532b7c123b6380", "tarball": "https://registry.npmjs.org/openai/-/openai-0.0.4.tgz", "fileCount": 7, "integrity": "sha512-0i0YKVSVUAC0S3ppLQoxicY5+xAr3sk9CrGcU2f2Ee1NB+77ikBxVY+h5W4etAI0MLy/t70OINkramX5/zITOQ==", "signatures": [{"sig": "MEUCIELkgEbCDmYlt/xYizB4GZgEDdEPksh0Nzt7k1mg3C/CAiEAw0NskPD2CEwHTztPIwffRNlUOIx2bg83YgnBxFF60vY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12565, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhDWrACRA9TVsSAnZWagAAjF8QAJ20FuWMRvjye2TtLmsz\n7o/9JyzjE+KfWcdP2AfY+ar44qhMQ0hdnCUmxH9HRHdnweGYE08z0LbSQ0MN\nEJWS9bkUg1ST6jKi2CeQVgCZhOBbWzNQYnq9mzlfr5g+EayRZFj1EvaR+1Gf\n/55o030Ag4SmxzEOfYgZMkTj0IgV8NWBzUiE6lFWRslYtAUP39dJrKY2Buyv\nis7FU8wxmAQQRlzbW9ikqKQd12nh3qR1djoNQ7ubBkz3KWZpNnGJnQ/g/pke\noEdfGtK8sq+2O4ctg0MXxEXqj2LnHPgD66WVyeHEK6gZ94RHGpBetEKqT2m1\nN+iCDjIte0SscCJo8/0o2b2d7Spx8J4pBqTcewv7T3DNfe9iQVqSc2Bfyf9V\nMpbIjaQTZGCKhB+FHeypqyVZUFUICRUj8udR7yBpuOJHQh4bHNTVJMpQGV77\nFO4ymTPqqru9H1sHFFpws+Pe8YiuF1OKEEVpGI1LGKcd7o06qzWSP2brm0j1\niPjK3FzwcoqfL3/bAXEcs/s+ICz9t1ob+pzOfaaQeij15ykZFVX3PthG3mrv\nvbUVRlUeKgj2/iOINtZvtiBmD5GVhhi/1pPyoIG0VpyWiyryMCY91W6m1yjS\n4V+nyVwCWmkidbalo1I2vYfnouUzPq9r3D1YRR3bZlc9pl++7dbxy2ajhdWb\nC4Fg\r\n=eX6G\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.5": {"name": "openai", "version": "0.0.5", "dependencies": {"tslib": "2.3.0", "form-data": "4.0.0", "node-fetch": "2.6.1"}, "devDependencies": {"eslint": "7.32.0", "prettier": "2.3.2", "typescript": "4.3.5", "@types/node-fetch": "2.5.12", "eslint-config-prettier": "8.3.0", "eslint-plugin-prettier": "3.4.0", "@typescript-eslint/parser": "4.29.0", "@typescript-eslint/eslint-plugin": "4.29.0", "eslint-plugin-sort-imports-es6-autofix": "0.6.0"}, "dist": {"shasum": "0e86ebfaa277069b164db7a67b565ca543c803c9", "tarball": "https://registry.npmjs.org/openai/-/openai-0.0.5.tgz", "fileCount": 7, "integrity": "sha512-0XkjNkBfwkT0ymdaWPvHX7DekJvSNhZjYaZaqD/O3CrGWb07TLZgVy4hF6sNFC9ZYC0ElnlgFN2Cfehw5UIKMw==", "signatures": [{"sig": "MEUCIFuxiHWBec2Xe1UoIJb134wjEu+gN0lHzu4SrNREwTKiAiEAu4cZa9x+pvQuq4FFzmqp77nDf5PRqR4WyWPVSseyKF4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15668, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhFW6dCRA9TVsSAnZWagAACZ8QAJKA5yjY4O9FI65TplI9\nVnXqbppym7g+qeHaOxGBFuir27zktIDIxv4pXx+FdKOI3drYfRPwHN94a2ia\nZFPQohNN0tX/UQpm7pV5zBap0BisugqPfMF9JGs4f6mkdvEDCL4EKN0DMvkg\n/fM2R0WWSOrdGt8YDO44zjxsR24nSm8YbzJ3LrjN+SyY2AOVxSHM4tQuF9/j\nmLEvaG0R9YhIKZYK01a43HT79+wimQkYpOzkBMyFgbr8QSzMvB6QYvYEgj+2\nHoVoGod0wBwz7ue7G0PqTji/NU0DcaVOIKQv8OaVeC+6nZfhKAUst2UoJZAc\n4Z0QBbYTJPsnjm8HIK7ZBSDOJV4Yoot8inEC4W4PXAGu6I0HJ9ydmgM8XPB0\nPRWpOyeCTWmAQgwtF1EqUA/A8eF8vQSlY4H8Lny3mrHbvlRXURI8kEcelnnq\nMF3C5Oqn9CMJnclM2jMhzzogDn/yEYtaDCdljMzGbZP20q9sXeyFU9l50GK5\n7u04VbKEkPW+wHxdCK1wlJ6kPvBt9mCByWMFSzTtuhxNwXqnxUh+zNxqAW/g\nj5uccMnja95hOoVUlVkstr7+kP385HxjKG/VhC48dZG7yGDhdlVHJjre2Wzu\nKowreuCbBV/N+7LrrgkaQh4+g4MsClY2haMEei2Tdzfjm2Ny0DhQnCCIfRPu\n2yS0\r\n=RKys\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.6": {"name": "openai", "version": "0.0.6", "dependencies": {"tslib": "2.3.1", "form-data": "4.0.0", "node-fetch": "2.6.1"}, "devDependencies": {"eslint": "7.32.0", "prettier": "2.3.2", "typescript": "4.3.5", "@types/node": "16.7.0", "@types/node-fetch": "2.5.12", "eslint-config-prettier": "8.3.0", "eslint-plugin-prettier": "3.4.0", "@typescript-eslint/parser": "4.29.2", "@typescript-eslint/eslint-plugin": "4.29.2", "eslint-plugin-sort-imports-es6-autofix": "0.6.0"}, "dist": {"shasum": "e94a872ffdd1fcca7953ce00fe2bacf379aec520", "tarball": "https://registry.npmjs.org/openai/-/openai-0.0.6.tgz", "fileCount": 7, "integrity": "sha512-61jCJ/OqpQpOSgKjrfPC6CSmqoV5+Fg1Qwmqy3caZW+J33S/rjR7cZhKjFQcDtCtFahJ/aZqb/o+WCSUbG4T3Q==", "signatures": [{"sig": "MEUCIQCVfL2DHBnYbPtkCSgiddjtO9nuHTZHB19sWfPBBfp/vwIgI851aJXTji4Kz8mFHpVaSiTF1d1WhfrKvhPUa7SDPHo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17388, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhJP46CRA9TVsSAnZWagAANCUP/1Hg+fDKUi3Ix2KQSjGK\nKkEFIdYdw6vw9mgNSFKUsb4sAJnTXN5jMyHO8TQDkRgbMR5q+x7Z+hacQNSm\nbnEDj6Iz+nCPpTVuA2zhz5nVj8dLnrHG2TFF3RAaZkfwxFpSX4IXigjBOYaN\naXRyoy/ZvDruW1nCbrFkwkac9b9qjT/nQfnS8aveAuSL8x5NZ35kPoLLHw0W\n+unWNLqacxmbowzqcVAVoOjTcmdECb6gyqRAJzaTlP6Xtv92Ftt3xRbusi74\neec7iVhM4J8+S/gxMDG9gEHejitCSehnfk9YvE+RF2gKSLe6ngFuVYlMqgDS\n0GBknw1SgzfblH09OFaWHiwOL4H/pcZKxhRofxv/oOSC28y6WVC5Rwt9BXgh\nKJh88QlVNU2G0/l2WRy4BSFUuhQLa1yyhirm+7OIeVuWSE5fSvj31wX9No3C\ncOsrX8SJoWXU/Fq9uKGBo4ejctZi8w8Q148RJKEoKCZKjz6NdyymZljx636f\n2bl0+751TIropTRrXkpYBgTXZSRDDKqxIFWIucrRVvoXfjrqsEoQG27DM25I\nPlfSM18laRq9wqAe9wDygLeNMFu6cpJKuCZdA3awWqyhUg6sAaXRBc1q/zGb\nKCM/DdWUafwv9X+ngEnr/T1NbM1RyCoQSYWwpPzQuf0sntuc5qMOs6rO3I8K\nDKo9\r\n=JM7D\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.7": {"name": "openai", "version": "0.0.7", "dependencies": {"tslib": "2.3.1", "form-data": "4.0.0", "node-fetch": "2.6.1"}, "devDependencies": {"eslint": "7.32.0", "prettier": "2.3.2", "typescript": "4.3.5", "@types/node": "16.7.0", "@types/node-fetch": "2.5.12", "eslint-config-prettier": "8.3.0", "eslint-plugin-prettier": "3.4.0", "@typescript-eslint/parser": "4.29.2", "@typescript-eslint/eslint-plugin": "4.29.2", "eslint-plugin-sort-imports-es6-autofix": "0.6.0"}, "dist": {"shasum": "90b75d8d1d9ec07f52f6362d13bb8fc5f16779b7", "tarball": "https://registry.npmjs.org/openai/-/openai-0.0.7.tgz", "fileCount": 7, "integrity": "sha512-suOBlZ7ICO/IXZ+d2+RJ3HibHlLjvnXA19Ob0w7fS38SS5o+XUEoJyZ8vIPMik4bRzVZ0i8wTeo0oeOxb+PdMg==", "signatures": [{"sig": "MEYCIQC3t3/uB5PVEyyeem35HvAbHPZiq0X8bPbn/eCClb/cNQIhAJMLwZmYYoL6OA8wKWJGtBopd8zeDLZr7Yu2cHYb5aWb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17391, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhJP6gCRA9TVsSAnZWagAAfVQP/315hrR/hLAA+A+jSm5r\nQfMoQ/73wI/Wa4idjyZKI4Ta3PYxXc2vG+BwYc2ThvbWbDhZ8imq4ytAiNbL\n+TkoeKSUp3f2hSD6H6g+EKKotc2HDUJ6illHJK5ogPo2ZCUO2VE196io9YMu\nbRnHdwFd6Am4/58WnNVpxR9aABHJFN7QAIEb1E+GoEOE0Hus/tWShHWlloVI\nba/KAy82zEiN/uv6HEG1XGh+ZY/vPVuhFUA4Eb5K6C5B56stF42ur7X0n77d\n9Fso2+IK1y7V/xI6chieZylPkPtRGwVXExa3lcdeJ9D5YcckB80zRb0dYnMo\nNnTgnXo6Ijjg8VtbNo9axNb6/WTMlRr3dTLpRGZcQqMtn7mxrd6R/wW9a8D4\ngC0kVChze/I94LvyhObEUNuAePTB0BZ+y6M9e1Tie2hajb9Gx2/pkNr/PqgR\n6uOS64y9VTTUuZijV5/L7kr+H3RaGxujlgm2Ly1VmbfX6iKXjjT73fP2MPNU\nhacei9XkGJcvmRFAByMXSCBu+E4RGxrsh5hZisymRGptTrinBtMvWZnMY+fI\nz7nomSCs/aVzm7Z8mb6Smo4L6dDOiua5GXR53ApAwA8UF2gXH+ie3g+yXLJJ\nfgimHjKpcMmdNSbPUUDQMIWjDBB/vaSeBCGyF1dcGxEVdTmPonRYRipwlQZv\n+no1\r\n=84NM\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.8": {"name": "openai", "version": "0.0.8", "dependencies": {"tslib": "2.3.1", "form-data": "4.0.0", "node-fetch": "2.6.1"}, "devDependencies": {"eslint": "7.32.0", "prettier": "2.3.2", "typescript": "4.3.5", "@types/node": "16.7.0", "@types/node-fetch": "2.5.12", "eslint-config-prettier": "8.3.0", "eslint-plugin-prettier": "3.4.0", "@typescript-eslint/parser": "4.29.2", "@typescript-eslint/eslint-plugin": "4.29.2", "eslint-plugin-sort-imports-es6-autofix": "0.6.0"}, "dist": {"shasum": "9ede0e537ef769f3f6f31be15163010cb22cd704", "tarball": "https://registry.npmjs.org/openai/-/openai-0.0.8.tgz", "fileCount": 7, "integrity": "sha512-CNDxWPqQYRNo+rqIadYPZUothkJ50e6/tYscMrboa1lVlHOkp2L8P7fOZ6ogYvRyIN3YNP++EAFKKQM/zDFYiQ==", "signatures": [{"sig": "MEQCIALodC/+1/C4TT7Ltd61lIYvDGZhC9See3bgG7V1RhSbAiBvJbL7g9tB+KUCFfB+etlm4EICEQhNCwJIy8cb8a/7bg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19005, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhKSp7CRA9TVsSAnZWagAA7tQP/19S7mrPopPDwCCQbRSf\ndNBcCAr7y6Yv/5Q+GyogxINhSnhRJKT2hB5P449pqAwl/97ofCWx7dpXZhdF\n+/RXfTE+mDTjo77CG1e2AT3Kha/ANagPV9LyY+xOf2RazEWj2Ae2RqczO5Zs\nPAXtejwoi8NzEzEJ80/+5rJtjs7LgFmNC2HLyUh7DQ5/RbAzUoIASvXF8d7j\nKU7jnqBI6+G7bgWRquOQS7uITxzd/rWhXmmnY8KFBbL+UioJpWGY7foYoMju\n+L5Ht2DoyugZ033B91iy3oMU1RS1sMuNgVWlL9GB1gz50coTAaw2j/9OCuL3\nIEvDujsb6kLhT8zwchIAy4CBNBD3SFY2A6zdwsK45AMha7qU9kkABssseVBc\niKFX7FZwZOoWtSnKuqSZqh0Q3egPYENNz4yn90VWT67fWcn46wfjDS0gI+xL\njiutxnJ/LIQmrbUi1Orrk+vW9EYnbIYoIe2Z5PTvlR1S3A1U2CpkGfoDzBzr\nRBQGFF6AQ+YFtYYcdvkSSxEitulXS6QY585OcFotXTgNKcUT19PQBaWMt9cd\nnxNk59J3wVisSrGQ7VRCXph4u6M8BDIOt6E42Jduq6/th0pGruwxwtvjY9pK\nEUldfDa5/bTrm8A4JODMXbMSsLkEJyqqBuLkhF7uEHZlhGA8nWvJlUV+PsP9\nzGzP\r\n=Hum8\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.9": {"name": "openai", "version": "0.0.9", "dependencies": {"tslib": "2.3.1", "form-data": "4.0.0", "node-fetch": "2.6.5"}, "devDependencies": {"eslint": "8.0.0", "prettier": "2.4.1", "typescript": "4.4.4", "@types/node": "16.10.5", "@types/node-fetch": "2.5.12", "eslint-config-prettier": "8.3.0", "eslint-plugin-prettier": "4.0.0", "@typescript-eslint/parser": "5.0.0", "@typescript-eslint/eslint-plugin": "5.0.0", "eslint-plugin-sort-imports-es6-autofix": "0.6.0"}, "dist": {"shasum": "977c06c37e4bbdbb98bedd0f9ee68f7c61f502b8", "tarball": "https://registry.npmjs.org/openai/-/openai-0.0.9.tgz", "fileCount": 7, "integrity": "sha512-3kfw6QgzCptHlrSsDrmPyQRQFgzgEJOTX7qBVhVRsFanLKKnJYdWeHC109BGsGibn9kWWM7RDQfec3c2xX72DA==", "signatures": [{"sig": "MEQCIDG0eB6NWXuk/5vV88XhXYwhJY2m78PAB3J6J50pfqmyAiB/EFpnh87sUBD9Xx3pb1bDpfX+7W6a46uojCnnThC+ag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19548}}, "1.0.0": {"name": "openai", "version": "1.0.0", "dependencies": {"tslib": "2.3.1", "form-data": "4.0.0", "node-fetch": "2.6.5"}, "devDependencies": {"eslint": "8.0.0", "prettier": "2.4.1", "typescript": "4.4.4", "@types/node": "16.10.5", "@types/node-fetch": "2.5.12", "eslint-config-prettier": "8.3.0", "eslint-plugin-prettier": "4.0.0", "@typescript-eslint/parser": "5.0.0", "@typescript-eslint/eslint-plugin": "5.0.0", "eslint-plugin-sort-imports-es6-autofix": "0.6.0"}, "dist": {"shasum": "68c18b7dfab33ecd8526800462d2e24e56f282c5", "tarball": "https://registry.npmjs.org/openai/-/openai-1.0.0.tgz", "fileCount": 7, "integrity": "sha512-<PERSON><PERSON>yrW4CTIqmP85ub1aAERot9cXUJrN6vEV5K/gKF3QOK2wrgMJ4t6RzhAxRHznYoEs29Zx025mFGjQqql1tukg==", "signatures": [{"sig": "MEYCIQDvAB1AB6WtN1aoZqC3lDC100FyRCVbTU6yYM+Wr0UfXAIhAN3P0+lk6UMp80A3iPyUEF7uRHvKsRA2jbjXMFsVU0GP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20410}}, "1.0.0-next.1": {"name": "openai", "version": "1.0.0-next.1", "dependencies": {"form-data": "4.0.0", "node-fetch": "3.0.0"}, "devDependencies": {"eslint": "8.0.1", "prettier": "2.4.1", "typescript": "4.4.4", "@types/node": "16.10.9", "eslint-config-prettier": "8.3.0", "eslint-plugin-prettier": "4.0.0", "@typescript-eslint/parser": "5.0.0", "@typescript-eslint/eslint-plugin": "5.0.0", "eslint-plugin-sort-imports-es6-autofix": "0.6.0"}, "dist": {"shasum": "ba8ce7dc9c580c368704996e62d773ba010e4966", "tarball": "https://registry.npmjs.org/openai/-/openai-1.0.0-next.1.tgz", "fileCount": 9, "integrity": "sha512-oW5lCEMWCKdEokfHn6dRDRAkzQK0FW9q8RAQKBUxbeyVXgyu5lBWgXkFuKcHjuTNl+Fhed+LhfdVUOhZIwE7fA==", "signatures": [{"sig": "MEYCIQCLR5grib/tmQt0XyB12L/0BbkxfWxOJYz3fkhKy0fi6AIhAMd24bNje43ZCCHHM9NfFhMK7aw+FCDHTZeApDqxRS+v", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19208}}, "1.1.0": {"name": "openai", "version": "1.1.0", "dependencies": {"form-data": "4.0.0", "node-fetch": "3.1.0"}, "devDependencies": {"eslint": "8.3.0", "prettier": "2.5.0", "typescript": "4.5.2", "@types/node": "16.11.11", "eslint-config-prettier": "8.3.0", "eslint-plugin-prettier": "4.0.0", "@typescript-eslint/parser": "5.5.0", "@typescript-eslint/eslint-plugin": "5.5.0", "eslint-plugin-sort-imports-es6-autofix": "0.6.0"}, "dist": {"shasum": "937e80f6710790261cf76d539c2d441f324a942c", "tarball": "https://registry.npmjs.org/openai/-/openai-1.1.0.tgz", "fileCount": 9, "integrity": "sha512-QWk24vUKmT+JP/85fV2Rayh1OWeoZkofK0/yP5kpAwr5t5XuhsR6B0eNogQNXVdu0BZkbhgcNE0mGi1iyZdpVA==", "signatures": [{"sig": "MEQCIGXN+6fFiloURuxEbllrsDzfEClhtDJJAsb9GH5OyJYoAiAdhF+VRGHe0P4iQeV68kuWC5OVl0WHblrFQcqNybthug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19641, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqk4zCRA9TVsSAnZWagAA29cP/1tl46SfPmzlJjovlHxI\nyeMIUvg8UU0zvJbbQc/LdHhTm3LmH6KtcOq6jeuJliaM99mJW394U7G8uhJx\nuQtZXtz4OnGm+upK60u0XA/sL3rncpruk+Pa7PfYBkWe7T1BrKSlzU+W8MIz\nfmAjF2oC9+lcH3QXqswFAykB6WwfjiooGUm9Li5+hCM0GXpOsCD555ki3vzk\nGpvpvR58hFBau3gLZE4VgfnUDQpPQise+eaUFE6ZbcAJnFGL32S4EsXGdgsF\nNMmGQFdbyPPpAN8G268H3jKJ1FkTN724eBzhqV4CnBwcgJnw8WQUeA+SAO2q\nq15HOwZ7Ols55HknO1GOmO6P1kgcxhLHvL/5mcvy+bWhOb06tr+xlL48ycxD\nFOrSVWgtIQKfU1/VGbVLeU+zfLujm15tZsB/0oukSQoUJiDwuU1TT4MkqOoy\nLRITEPl3x2V6U/wf2yX5GN+p5feYDVSnfuq2442F4pVcDCKl4FD9X8a8t8eW\nzWjWqxgexpLdjjcS/6tO1TuyFZN25Y3hd9d1jF3imNdogl9shcEaLX7eTbdL\n9qLAkXLBSGxL0yVdf4Ki+GJR4b+eRpfagMIfu4Nce/OfifGPKxfLc5KftDiF\nYuipvRb8C4CLGKml9hrH0Z9rs9cLflDzJUjV2981FnEaSlJQIc4syOwItcG3\nMl8P\r\n=OWBk\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.1": {"name": "openai", "version": "1.1.1", "dependencies": {"form-data": "4.0.0", "node-fetch": "3.1.0"}, "devDependencies": {"eslint": "8.3.0", "prettier": "2.5.0", "typescript": "4.5.2", "@types/node": "16.11.11", "eslint-config-prettier": "8.3.0", "eslint-plugin-prettier": "4.0.0", "@typescript-eslint/parser": "5.5.0", "@typescript-eslint/eslint-plugin": "5.5.0", "eslint-plugin-sort-imports-es6-autofix": "0.6.0"}, "dist": {"shasum": "907d908235538d17f575b60fa143fbc8e45dd59c", "tarball": "https://registry.npmjs.org/openai/-/openai-1.1.1.tgz", "fileCount": 9, "integrity": "sha512-l6D<PERSON>il6zjyYvIOFghHvMRWWQBO0469q34AR39QvqMr0KTvJsf5wEBcpc/ToLN7K9tC37p0m+VxW73+BYBPT4g==", "signatures": [{"sig": "MEYCIQCcDC0JeBGOX+r5JkzD6ypC65aeNoAZkPuUDzMSszdoqAIhAK65O7sjA7Md07AyRrRLCTPjjXcbeXLttuYCncNAbXzK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19721, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhrWUvCRA9TVsSAnZWagAAEEEP/RafODiFcmvmcbBxXOFG\nu84siuyEtxN/AiyQbGV6hfp6y9M+prsYp3miMq6gAMA2r8E1CBB+SeL5yfCt\n599pbpil3/9PCXwRFi6Ahe5camYW3Tn38y8Kxsx71v0N/dKQ/S4CoQWjMDKs\ny4DoyhbaeDXFim8cWYFknO8m9H43enM8KbK/U8JsQzQyrrBj0jg5MxYOJkR8\nZAX+rhXo/FZFxUH56GSgBzXQPRCXzoywUYpgIuBpG5M5ahQMhMZeUvWOtQZs\njjwxcviF0AtTT6LSphIROeeredV4k6K8tVhzKB5/4qQ9ndZiwv7yF9L39BqV\nm9L0+zLpZm+o3FTOa+jFiWB7K48RvFSkwlVZlCsLqoec1U8ruYnUr7P+GqNa\nudF7alMqPLgUnByMlLgyNt7LXE5C220atZqTkAVEUt7qcqg47CjyAy3dTPxH\nCBeIm9mcinPN+wyuZseZH4wYtSFue4x1ncp95R0VXChGdXOe1fyxRCaRU3Eq\nnE+xxT8E/4aGHdf2YF2pnA2lPuRwxaK2Xd/H/ZONQF2TKlNyEA5j10N3JPla\nKrdGBC1KALRXYUHV8qmu6aT1i1UaXVoElhUwc/Nije2o8ElQThPFrIkX8v6d\nxZ5Hdu8otRyGD67f3SdfcgJgCutEmXQtUQ2Amp3/C75IxPDWT5eL7W17rzYH\nOmiX\r\n=NGht\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.3": {"name": "openai", "version": "1.1.3", "dependencies": {"openai": "^1.1.2", "form-data": "4.0.0", "node-fetch": "3.1.0"}, "devDependencies": {"eslint": "8.3.0", "prettier": "2.5.0", "typescript": "4.5.2", "@types/node": "16.11.11", "eslint-config-prettier": "8.3.0", "eslint-plugin-prettier": "4.0.0", "@typescript-eslint/parser": "5.5.0", "@typescript-eslint/eslint-plugin": "5.5.0", "eslint-plugin-sort-imports-es6-autofix": "0.6.0"}, "dist": {"shasum": "9713d499a6b8c05a2e495464ef5fa365523de23b", "tarball": "https://registry.npmjs.org/openai/-/openai-1.1.3.tgz", "fileCount": 10, "integrity": "sha512-G+VounGq9lSo15Swc5wyT2tgtI+9bCw9N51Vryy/UNwSpYCjtF3lFDQOjak6ELqRmqHBpMltzVV1R0svrk3QDQ==", "signatures": [{"sig": "MEUCIQCXFts/XPAik6exVT/+i6m7jU+IqOLVL58aDCk+hfUG8AIgd5lYE1WwWqen13x/CkXqhT/rzY+BC+qfEc8YDKYeMhM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16949, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzMtnCRA9TVsSAnZWagAAlWcP/3rHx6bNeOzyJPYSjPR4\nKsDVhzMWi6cSechL1nsM0Uu8lllAYN1q3HLRPKxK1kf96xy3ztleuIkg1Ioc\nCFg9FZmXlA8e02A2SRpkWy5okqsbKiYYl7m5J3r1VxNqI5+LpfC2DoJUAvLc\ndNer9W10eUjSmv08Ks7XVOwFwLeJXKzMm48Uuku0X/GfTYkJuF6dQJyNuGcV\n+7/hGjNxdKmAhB0M6qfqAT7exPfmcD32bO/GA/YJ2XtkaJHHneS7qjK91tRJ\nJQLWcXkCQrwgWX28FMXcGgC3Zw/6oNrIKQziXgIhP78SsvzO9MHyv1dKoX+8\n/vL4/abMzzvuU3Cetl23Q00A6BiAG/tze24fmzaPm15G2xvE0svuXSKpYJAp\nvZskVuQb0y+yaEoK9VWRKoDF4jcwa/CNeQ34oSvxMGitkDdOLFMAoHpAXRoa\nJ+RuTTZVpoMC5isOj3wBx6UUUWDP6L3+avH3xccIxgNlMwZzoXzoB+eHbZIY\ndWkOhBE5D0Lz4OPWytEW2POGwXbvLiIIqXxeDJ+KdturABCKr6SqACZhQqsu\nb9Yj3ppMcVdOXRiPuElxSpVWtd4VfG49SL32EF3sLcHXfjOwVOFEhUwT9asE\ndjK7f8/8DfT4K/s9o9IzACS2BkXngiIFRKel4tAYnsXiMln4krX7UJvmz8WR\nRwqT\r\n=cEqS\r\n-----END PGP SIGNATURE-----\r\n"}, "hasInstallScript": true}, "2.0.0": {"name": "openai", "version": "2.0.0", "dependencies": {"axios": "^0.25.0", "form-data": "^4.0.0"}, "devDependencies": {"typescript": "^3.6.4", "@types/node": "^12.11.5"}, "dist": {"shasum": "de6fe8aa2a276a9b6b8cb785d4c97befdc4f8236", "tarball": "https://registry.npmjs.org/openai/-/openai-2.0.0.tgz", "fileCount": 22, "integrity": "sha512-5O3Z70NCWaDhLMhCNG2oCpqYCzlZh5Vilc5bogdA2N08kIXYJfPKgi8StdRB2s17A1Wv8ia5/naMymH/pngLvA==", "signatures": [{"sig": "MEUCICJZwURFhY/VOkpI8J6JE5rs3x60yKYICCq843CJp4FZAiEA5QePyLNOLKSGAsJBMn/iK4LGtUll/gCMwIiz4A0ZJYs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 294145, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6GIoCRA9TVsSAnZWagAARvIQAIoDVymkq7y/+0HwP08y\nJ+uVq2VvZxqCCDfUwcIdTGh5J38VkS7p1ONVp4Xi1ur8RDNAxbSyrYznNPka\n2JwfWs1Ub/S+ptzHGiScEusq1NwKF93Pfib1FPLhiGIgh0baw2pvzg71X1x4\nqviCQ2DDgOn0BqHS0Pf6S8p8PF6W7i0hH1n7K3Jif6y2FyMcp4K9r2MaWhk0\nnLh8tpkkZvE0BV4VM6kFGhjb1jtMlD5fXGIKOinh94/Hv2hrN392GZZq8oPZ\nfZVQNr2ootLFZsV8ghxvfd1nNJ3eO16nVKihJlP1gjtNyOjYGtdi/h+HtgNK\ndCkHrdv2XTjb8FjPBJ0XLToF8gY79MeLb0r/hg7sTUggHielI/b5b6sS6HH9\n8Xm7CkRTQFQswtT+RtctoAs3FA6aIDwJ87WVmXKYPf35tr5K6+K+9QTqethv\nCvFkrbQ9tpb+MDlNUKdrWlzMsiNm3HKwLgd14+1YslmX6tRUPe/5cBkk38ti\n5JHoyq6J/Ci7LGnH32qljDT+kn5m9sM4QXjwHBrKPB3b/Pi9bTFxUHPKhBR2\nbQG2FkkTK5FDQisEvS5EJW3FGOj11AbalpLhFoKPYEBrqCvG77KeD/gufRqn\nu1oMvLu3/L2WigvHf8UYJ89Ux4s+Ss+RjAvhbNkfHHzLKOOHmJeJm3qZn2be\nKeqH\r\n=aujj\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.1": {"name": "openai", "version": "2.0.1", "dependencies": {"axios": "^0.25.0", "form-data": "^4.0.0"}, "devDependencies": {"typescript": "^3.6.4", "@types/node": "^12.11.5"}, "dist": {"shasum": "396322f7767a12ef4b997be7b2d4d6f57e20fc59", "tarball": "https://registry.npmjs.org/openai/-/openai-2.0.1.tgz", "fileCount": 22, "integrity": "sha512-HWqAX7otfjbaO3/+msjAnfSCgO3RWWrXizuY87LUyQM6Oif4DPD+4zwClul4iXcpuFftI0o0ZXf+xnV3UsXMDg==", "signatures": [{"sig": "MEUCIAEJo5HIMvsBAd3eRyUCwOxMAees92brk8u+gt8m5C1qAiEAtnpAPFMzeJH4EFWNJk6G9BvMdmHBa/f3/DYkjbAYbYU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 294910, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6dbSCRA9TVsSAnZWagAAKBgQAIQWIfqFFZ5WoYx1rmsI\n5H3IjYBjy6oNoeUL+K5pTApXEeQUiMBxRZ5gIplFMhACMrExZubeY/XV4eTR\nKSSap+K8sM8eThaeCzKZxioyznPIsFYN3V1nr69nd9Y4B4S7CHBL83i2Nu1A\nirqcUcRm/92s7bwYR2Njf63k01LZrRbzKwofuke80RxTPdOZF1qHGT6lJKqX\nKNCucJ6Snm7XCj2TfG+Bv41izvssgn9ezjbWbWs41twVrhVy9H7xk2B3ibh+\nQCko2wx1JeoXJrdmqEPkjznpspu9/k4NNulFz8v/fWJCnjNuuhtVNmakoH0m\nm5YdzqJF85g0heVO3QyWydLEcRUnQN0XE6tUF/C9RhEhtZBZeOu2bP4Ywc1I\npN4tltB7B396klwlpbCELkP1rMtiTf6/ZgSrU35LUP+p873v8SuWdqkNaMm1\neV5KgoHPb/xVRU9xkMtxwEhKVl4m3olOUJ8QWNnZ2Pjw27qJMSaHg8UoiGSA\nSz0kUVhMsfSS+AH9QTDX/OGghbZ71S1Nb6uUaPhVaFspOOb2IFINF6VFt198\nvF87fDQZaP0ua9xYp/CXIc56+p4O3agIavhqOND2zItDTObCLALNZ6Sj04m1\nv/UykDQ+tMM6fUGb+IfEtb/vOTKol3PjN86U1iyoXQ0uqa2dGrsW77IiNNLM\nBIci\r\n=aF2k\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.2": {"name": "openai", "version": "2.0.2", "dependencies": {"axios": "^0.25.0", "form-data": "^4.0.0"}, "devDependencies": {"typescript": "^3.6.4", "@types/node": "^12.11.5"}, "dist": {"shasum": "c0508552e06c269ffcb503a2e204a924b17d2e9d", "tarball": "https://registry.npmjs.org/openai/-/openai-2.0.2.tgz", "fileCount": 22, "integrity": "sha512-JyvCwi3/yj8OTm7Qbj3RGj8KpCzOQ+Vq6Sn3MqaqFmhGQxTW0LS98YPLwDqK6MRPVGdbx3++cjXDzQ+kqaB6uA==", "signatures": [{"sig": "MEYCIQDMPqO17LyMu3HDbqrkRZl+hfGVXgsRtK4TgztYBmiYKQIhAM4e/mhlgS680qcpB7PqUr5vzzwXBw2e4/IRxeu+HuvK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 319131, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8IymCRA9TVsSAnZWagAAwaQP/2t2VvNSQb8+hw850IMG\nptWD7reJ/poTAa5sgWYcvmikcyda7nn0JEmQA1/QV3qvapQpOZbCSHIqrsRw\nmP0TdgZZjuqxKmXHL7yYxorNmdG2wDQe/pEe1f2tycT8aBvLS+tE1K6NG7Cs\nb6Ma6Qx/efIgrM45Z7JK1evVr4alG4m7oTUdMHYaNx7uSYi/S20Xj3D0hhKX\nFOBNRApSd22PFhJEVyY9VzWuEI8BgywleN8MwDf+A2Ta3HjrlVlqpM1JPgn9\nYyGnnfmglVGuZShAuKIhyVg4f1a4iTm9NEe98OGpwfVzM/Db//NNWoBSE26U\nzvnZUa84V1q65FKpQkmhi6Bgr7RlbhFyXQMQTbQ278K3RO9WlWcpVgGL/w14\nNmu3jSWiQUHnJY5a6AQPEmSxHc67OLRnac7aBmLQos3c8KpGNX/mOzXb+Mgo\nhjBszWsLH4BSKsc9Na0ZCyJPcKA8zm5h5+C495q+Z9RA9fx8grvXg2waMb5U\n+bje6BKFAAV9109MX6TMp/CLiJZ/pppV65cEaWDmf4LlQTGTX7A/gohNxgHX\nYTgDq+toSx+W55kfdmgAoheCUnT4vPayi1+4B+lUVh0FJo+Wrc7+1W1+pCea\nZC6Wp6uIddaaeGUM3SaLKGGeL2OpHrsnIevdtXNDIVhp45JvPIB0SDcaiLf9\nJc+Y\r\n=MdJr\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.4": {"name": "openai", "version": "2.0.4", "dependencies": {"axios": "^0.26.0", "form-data": "^4.0.0"}, "devDependencies": {"typescript": "^3.6.4", "@types/node": "^12.11.5"}, "dist": {"shasum": "fa3c2747db913a9fab70d9304799657dbaf13971", "tarball": "https://registry.npmjs.org/openai/-/openai-2.0.4.tgz", "fileCount": 22, "integrity": "sha512-S7i9BSbdQZwnW1sJRQ0ZRkZf/14UTYwqvyMbJmAIMbI/pFv9kgn5T7+ajml3MC2g3e1CAE7Y0LEt+DBOnDSWnA==", "signatures": [{"sig": "MEUCIQCi62B6WW88Oyw+mAbEAG8s69mdx/GXeZkIWjDsjeHN/wIgajHfrAONakc5VgoPj21d2Jc/Th0vC7e2574qW/4SD/M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 321675, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDUozCRA9TVsSAnZWagAAYBEP/2upiW1JB2yvWIRjkzFt\nur8EDSd2DZkuQOfWxVQEmrmW0IINXyckFOitZzXVCwLHBq1zUNLWqahlvs28\nLiGFkSz4kpjHGyuS5zeDrOJ2iTJTpMWP7ZkdM9RAfPL80j33fKrtrfNDNa7j\nPAGqH6RDTqmlC7io1In3s3Nr8OvMy38yG7+ShO4cnAV4Bgy3roob8lKbPU/e\nuenzuoQKdaMgAUIPF5DK4WVZ9xRBhh8fK3nT+u1PnDw1RogE4SoV5nQRWkvB\nOHhBq9S42tNdmTSOV2hakzDCP2zoSLETBiTdp9SuPNSwfmRuTNB+uYur0Gtn\n54nDeCm75d/dUKx2m4ar43Ddt+KXA6VqxzS1HJ8XnY5VPIGgSVkPGT4EOmdU\n9Rmxe2kq6n6f1AumWSbiOHq32rlSQUa8zfwOeR+RqH6FfTDUgZ8C40IKMg2B\nxgoutNAaHTx6bzC8ukOX7ttMbSZwj/mVMuSGe7b9CS/BljVh2JzaGwniPCL6\nY1iqYPKP25YTgM8VJWWXStU44mcNuvGMgFOM/Oc5d6vo8b/md0e5Igemw777\nHo8XvH9OFKI7kSlk9lb/Oq4JxZkhcsOh62IT7qKjwAueFFj+1sBIKTShkUc4\n7PqB8topzVIwbfzeNFkwgxJwOaBQ7+JPnu+d39xp02TAQHYqCNI+Chdz8pfT\nBgDJ\r\n=nWCM\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.5": {"name": "openai", "version": "2.0.5", "dependencies": {"axios": "^0.26.0", "form-data": "^4.0.0"}, "devDependencies": {"typescript": "^3.6.4", "@types/node": "^12.11.5"}, "dist": {"shasum": "dc1e0b0588ce9464e62af87f86a18195de2baa11", "tarball": "https://registry.npmjs.org/openai/-/openai-2.0.5.tgz", "fileCount": 22, "integrity": "sha512-M/drXV2QYdRhmB5EhAINpeA4oGzj0M6mSVfi8o8Rex3Re+yEnSBMUExtGZSlbVRAKpIvTLbq0oC7bgHBaSnjLw==", "signatures": [{"sig": "MEQCIHAJlKLa1w5DEH0LHO9PyPKzQ+mROrkj+7bJHOCyJUHXAiAZjUwM063zk0uT4QYksXt3HIQnpMktAo1SzZFaq5POvA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 346249, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiMOGfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpMrQ/+PTcue/6CDfrJ0s/A05fJNpHUIc4bEX8Cj+VAnf2iMlVi6wc7\r\nBWny6DWfQjtNUKNTDYC2AnZPbFDKermF8wCz6DjPaxWb0iUh0VVDoRvdB4pL\r\nLU5vRW6q/awpwyn5hDsvjb3SpBXZU7SxX6tl3XNc5wZCKR7xaBg9+YCX/Tsz\r\nQlb1s2UgfFLQiYqydD96PYMKLTFU1//3C0FHbs6JFJ/1dpidTP3DbCUApCKJ\r\naOxP5W02lakyMlsqcmTB0IwpK94AP3Qk7vVbDpryDUlmTYn+u7AafkBKYSNF\r\nfInj9QUpYFsx0goaomxJDoueXtYcnxDvRrb7VLsjFmgZ3RKEaejXt3yegaoJ\r\n6CXcRjkq91dkUGGc+KNMHGGBm9JOkZroGMD/z+B3HlbfJOW5GsaQSovvonDP\r\nfC/n7ZniH08Nos3o1uklR3I5L2uqvazI3c4r/JHT57K51gLtz2v7fb1+p9MI\r\nq0dqVi11blxHEAkljDHWZHTWpyw82wFl2PEdatmyh9SOOKHa4Hi+lQSmWk90\r\nDuQpOrzXvGyM7dYgIqK/3L5BGOVg9LySzWssOePzGrwwmQ+B0n6x+WWOxDNv\r\nSP32TrdJckGflxd4Cr4v8uV0mTxTpqBQ+HSit+/agMkfxWy99yNpBQ9FFoEO\r\n0f9U2zGxU5dDTqJjezDQjQSPFrlmx7mWCsA=\r\n=R+X1\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.0.0": {"name": "openai", "version": "3.0.0", "dependencies": {"axios": "^0.26.0", "form-data": "^4.0.0"}, "devDependencies": {"typescript": "^3.6.4", "@types/node": "^12.11.5"}, "dist": {"shasum": "0816f1d72ee37820c9ff14d93d597431489fec37", "tarball": "https://registry.npmjs.org/openai/-/openai-3.0.0.tgz", "fileCount": 22, "integrity": "sha512-YNAPZKzBfE6MnR5Ro/z3uKbg7T3F3W1FoTCtYheKRdEjZeheMX49QYFeL990gBFAhuGziEZCUdhnNT+eIrxX/Q==", "signatures": [{"sig": "MEUCICzAXhXPGw5CaFHZhJEmSzjav6i8ggaIkNgWFi+DFvRmAiEAyqK1NLkHGX0oiLFO3qZPvAbBTxUyNeic86tm7oW0rZY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 328628, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJin7UCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpAYw/9GNl+bl7qWXTjtsgfmaq5zrRwHJGu6AN/lYX/MtPbWKvhrzpl\r\n0E2FOT9XKQbX4alT2NJ2uAuomqD4M4sIDNpLCNn3Utl0Bk6vL9ALPwH1RCv3\r\nESIJPSAfBzYX4WI7inGBrLWromeba8GsHgfIOxxaWGdbx9XZgyAg83kBGyxv\r\nJDHs8eHRtZxv1OLqyK3hXduJ5CjDizVX/GAhzsvNfa2HLRBh+c/ctZTxE+kJ\r\ny/UM5R4RtfqK/EhXsxpQyJeK9EZ4dbMOxv77DLsYC/QLientLJ0yeHul/tZF\r\nZzp78pYeAtrgzVODRyx7rMLnDhNEfdJ7t81AeOV+cakgrZ5lLnDTsuEZNgg1\r\n53bldjfa2topLfqSBrS0LgmmckKmFwAvm1Rq7V0gKi51qffcDpHkw9Uzy9aX\r\nSbOkEoqFSlAWSp59J4fjjzX79ZVQ6bZQfW4f+oTfycUW+klqK/MGDq63NxKf\r\nsSwK5nVS9XCxCKUeNEXKTzByMw0L6w/yYygYJQVQRfu0VOI0USvSisJlU6+L\r\n4o8ZfBA/qfOBFUtnc2OWNp2WRM5THxJdP0yQOx7XPSAmj8FMltZmlbsWUzsu\r\nOzTn8mjx2L4odJ8lmvh1o9B2B5xb7+0BQHA/hxx7D7I2m0qQq7x12pTafCRj\r\nmbyhII95VLm/jjN6sMKwLeeqT5wpTkrC8Gk=\r\n=YRcw\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.0.1": {"name": "openai", "version": "3.0.1", "dependencies": {"axios": "^0.26.0", "form-data": "^4.0.0"}, "devDependencies": {"typescript": "^3.6.4", "@types/node": "^12.11.5"}, "dist": {"shasum": "4a6021e5bf32dfaa56e346b7a4830a80a479dd9c", "tarball": "https://registry.npmjs.org/openai/-/openai-3.0.1.tgz", "fileCount": 22, "integrity": "sha512-5O9t7dBcUOIhzK5764nS/8WtkX812l8Uz0uoIHT8mczN2IUUUe7FsGbIxYGEkGBKA4GY1blT8QaWJVNZLG2cwA==", "signatures": [{"sig": "MEUCIHSrTxpXxOcQm5obj5tGM63vkOPS1NEfSelxBcZ/kpR3AiEAx4l4fU5eU5/oX+N7aNLQMLJKOzp93XuKyhxQe1/h57M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 349720, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjUKtqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpMgQ/7B0lvEUBgpm6B9UGpRrKFiVmIuGmLbNU3EoNZiWx38D2RtW0W\r\nxdNiqaoO9uYs6MDICv8s69dIcK/IF+KokDZJWBy8S0G/QR6PKwb8qewUuGMr\r\n/vqPyWRoBo7yqtCOk3QZqhb9bjcZdoUciIvGi91g2Ggw+8gnHouRxsGWw0MI\r\nlb4EoF6p4fwCQ/FflFMfCi3jLHy2aRI0UHQ/S8++sfs4eXl1vI5Odnewnzb6\r\n9P0ej7K8GAXP765z6IDmR6sNrs9DR7xq02q0X7YRlPpI1WZLinryycL1YeT2\r\n6VSb9bJHW8GaDvWJG2NVLrQS/9HtsA+yJf0q+NkbBTHAoyaoY+t4SlMozCd/\r\nzrPdv2CBGGfbdqINMcHs0gD8/hRaMBTAGR8+5hjcAyDTqBdrd/55fcyTagPc\r\nVfePZrUUleWgvxWdlewdSV8r1Z0OfphWzK2oIXI/dXcbbMQ5Zzyukvy6vlpH\r\nPbHxLCYFkRlFGl/lJglY+xoY9ftFHTzOmBpUrNSRnkY7Ja/hbKVee1y0DaJb\r\nS+eOThjSvDMCvH1iWI83etfFEiTq/I73jGqAqIR847atUL2kXUZAmPbqC6mC\r\nmeqWvgbSmwjJiKeLx6I1kNcT/56EN5Su/bpYGZ+MBi/G9L2R3BAHfidqE5PP\r\nMM8oo5ucWoIOJd4+S9Yj+Cl8gaQSlfWs8GM=\r\n=Xjvx\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.1.0": {"name": "openai", "version": "3.1.0", "dependencies": {"axios": "^0.26.0", "form-data": "^4.0.0"}, "devDependencies": {"typescript": "^3.6.4", "@types/node": "^12.11.5"}, "dist": {"shasum": "13bfb228cf777155b882c2deb3a03bc5094cb7b3", "tarball": "https://registry.npmjs.org/openai/-/openai-3.1.0.tgz", "fileCount": 25, "integrity": "sha512-v5kKFH5o+8ld+t0arudj833Mgm3GcgBnbyN9946bj6u7bvel4Yg6YFz2A4HLIYDzmMjIo0s6vSG9x73kOwvdCg==", "signatures": [{"sig": "MEUCIDesNy971ubHnTyktBLEZcSxtD/obJqRvXNj4QctF61bAiEAolCwL3RINZX0tcvR1YWcvXMqxfWF2WMtA/CabGsyM+8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 407219, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjY/FbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqG+xAAo9HFOAKW2Tzir4N+5kM/asRfsAy4lwTFb6GJG0i0E6H0auc4\r\nWNinoNa6S5wJ9FCRWaUYJ1hlpOojrvkk1HdzG5dKv4umtQi4cdm9PU0OoXLM\r\nfczCYMXhFpaNboOeK+EoMWxD6xo1s6+6XDauJT7OzeoTTmDhLkFgHTeWanW2\r\nyUt2GpmFYSzHDbKQ8hBbqpBL4BYdIztxvUKEgRC83V39LO51dtcHlB+M/oBk\r\nl/9RYj3ZAALHNxKV1qHE6k7uJZLkzE8CGUUMV8bOANZzWqVg7zhYZJzP6593\r\nY2y2BUSmIbJYSAR+J9vkZ8Zo9Y49Vg+EnrB8CZlDUacfHyXFLx33rMX07IX+\r\nF2jIL2vPXs9XV5ADhuAJ3OrTbsxr2evgGn7BAZwuIhjrvVi+Dt4aE8CcVGWP\r\nbDTxiXdSek+ofAreofgU52raVbtFAfIYHzh3Lyya1hWA2j/4mYVCz9o6nf38\r\n3CmZA57tbM9HKwYbfu0enQq8Vrv7u46vFoCtDyvcq1oTmJ9/J2cnS/q4aUAE\r\n5O3do5NJtVGRZvDRFIuXtrG+QLodaxuoFJs+VgvZ6ISlge4dtUTesY7bjegz\r\nj+9q7fEIbu5SOW1I/Cq18+7hNjEnBEzRdT32IFaLsEeAMxbJHcchypDm/vIY\r\nRJ3BTZAQhWM2fbVLuV9Ot6JumzDsmPp4QhU=\r\n=i2BC\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.2.0": {"name": "openai", "version": "3.2.0", "dependencies": {"axios": "^0.26.0", "form-data": "^4.0.0"}, "devDependencies": {"typescript": "^3.6.4", "@types/node": "^12.11.5"}, "dist": {"shasum": "8328e46e7fe9373813713cdcc64144bbb43c9a0e", "tarball": "https://registry.npmjs.org/openai/-/openai-3.2.0.tgz", "fileCount": 26, "integrity": "sha512-feeX3sSKyCVpDt1CBB++FxlaPxzc1dpwmx5ZlXAvuauv+Yk+R26x04TFIm9mLBJnDQ69MoCcJ5YSMASPJidSNA==", "signatures": [{"sig": "MEYCIQDQpGgmMZ0g9SBrHhZGCje8drdfYkEAQIhHvfmRj/pJIAIhANfkIR8LGxWqxriAVve4m4mlQvrZReC76qFo7armFKPG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 475939, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj/5WxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoVCw/8DBram7tV6zoRCOLeAedTZNrzps1Pr06T/Z/OydUP9LZX4bD1\r\nMy+gH3zX8IKrC6A+pJoZVeHRtqGecPruoQb7t0v5I9C6+/TDpTl2t8m78un0\r\nOiUQIkyahwM3RzJsnRRFEg1x1LcDW2UGqQcxE4gu3t56SNJrjlOKt6GAm+KR\r\ntUbX5zO30xWhUCG3zg+QJz/d9IleWSRALpXxm+k8XBho9M62Y4cV1DFF7gE2\r\nxOdVcVUM9mj4R0UVoFVcds+2SFQm3LbQSk1R14A6uEDTxgYZvDkkWI4dDMG4\r\nhM95dBndx3Zlc+72Lhn6k6emEN+jaxBr/pK80NN8GIyttNdrdgL3E6PRvZJX\r\n5xnQrfb0k68xVSuHo90qOpxS4O7ARWBJbpaYs2yqMFrO9vsbMXY7o4m2V0sM\r\n1/AlpjsFVTz4fV7NuBvYgeo/TtAJmXFHNw0XkthnMsbOV6H0AOPtfLXFQxgr\r\nS9hYUjguRQseq+3UMsUu2UCujOTdL6qDDnA32EaStTn94AIX+3yoyFc7l99z\r\n6Ycw0sJlKupt9Fpda3UGExbaTtgzz3pr3RBJXCnb6ebBBQWuAzcMsBAspR/C\r\n5o9ns2aUfP8ehD1nT+asgg5hZRyIXg+gAGV2mpK/s/Z4Fyu1aMwhOV/CI3n5\r\nLuhGk99gvyf38I3KngnDh5wRBgQmeBBvKNQ=\r\n=7DtH\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.2.1": {"name": "openai", "version": "3.2.1", "dependencies": {"axios": "^0.26.0", "form-data": "^4.0.0"}, "devDependencies": {"typescript": "^3.6.4", "@types/node": "^12.11.5"}, "dist": {"shasum": "1fa35bdf979cbde8453b43f2dd3a7d401ee40866", "tarball": "https://registry.npmjs.org/openai/-/openai-3.2.1.tgz", "fileCount": 26, "integrity": "sha512-762C9BNlJPbjjlWZi4WYK9iM2tAVAv0uUp1UmI34vb0CN5T2mjB/qM6RYBmNKMh/dN9fC+bxqPwWJZUTWW052A==", "signatures": [{"sig": "MEUCIQCjuqqWEAlUeEOUGSsQn2iOE1OyZO8O/rEPSa5+G+lOngIgfdNKf8AQ1f2jbXf1nZ2EUFNbtQEcYwv3JGbN1SQB1Js=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 479503, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj/8uUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrOgg//apmrlg0OFc64UNDyLTKnt8V5SboET6tz0HspRlMlpvcn1d/L\r\nEwld0vsHbsZ6bHpwbz7KU3QO0Lhk1ogLssEaZ06AtkthqqAx6tRT6PyhCCYk\r\nQEKyAY96PFT6Y2+eLhhzZNwAW4qqMKCLeTKS20YctoM+StG4EWN4gNpVUdtD\r\nDNm+mQWUp9o3PoQ+wQ0EVyLVXts5Jx1taYuXfq4vlMkG6/NwAuDRkGl+HIZR\r\noJJxBMIcgqkT+LmAKUcKJjnR4/RFGAW7XhfkpnpO3Uzc6CRr7YOLts31tAiZ\r\nPISIYoWeocPmDhfJwi4wGhuRHPZBlDea9aobvjmYxO9jUHYDrYqKe6wQgGyv\r\nVCH2T239KTqRTGfmyFDaRlbnxeSKjRd5RXqtoiotu9qvPuHcEook8vu4jUKA\r\nQVWC+XeR0iqcA4biEILDljUxs8jw7MzyN4pI2mSH+y7c2cMj41Lf7HWEMkTL\r\nuxW02R07FkrCg13vYrReLZUIfchT4lsh+kbcbEK38DsZs4Df2y7xzaW60QNi\r\n9EQHMBb8XqUoOOk3NZ3FPr/XyAztNp6/yOEBCwlz69E7C/KTvwrIDujvA4iS\r\nInTq0I9VcYn885xbn2ONs4P+B1ibTcEzX0p6hs5PQZYXFOzK7TwwAV0IRLBt\r\nMCxAx+o589SMcdxnQdBsb9anHF36HI4UFu0=\r\n=oF6y\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.3.0": {"name": "openai", "version": "3.3.0", "dependencies": {"axios": "^0.26.0", "form-data": "^4.0.0"}, "devDependencies": {"typescript": "^3.6.4", "@types/node": "^12.11.5"}, "dist": {"shasum": "a6408016ad0945738e1febf43f2fccca83a3f532", "tarball": "https://registry.npmjs.org/openai/-/openai-3.3.0.tgz", "fileCount": 26, "integrity": "sha512-uqxI/Au+aPRnsaQRe8CojU0eCR7I0mBiKjD3sNMzY6DaC1ZVrc85u98mtJW6voDug8fgGN+DIZmTDxTthxb7dQ==", "signatures": [{"sig": "MEUCIFwMh0SuiWM6AREKI79LTlQgPrnaZGBUsRjmjvAurb+pAiEAq14pSO7YalIonot7NPPTZsT3qCOomlTYXdu49jkcz+o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 490056}}, "4.0.0-beta.0": {"name": "openai", "version": "4.0.0-beta.0", "dependencies": {"qs": "^6.10.3", "@types/qs": "^6.9.7", "node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.1", "form-data-encoder": "1.7.2"}, "devDependencies": {"jest": "^28.1.0", "eslint": "^8.22.0", "openai": "link:.", "ts-jest": "^28.0.2", "ts-node": "^10.5.0", "prettier": "rattrayalex/prettier#postfix-ternaries", "tsc-alias": "^1.6.9", "typescript": "^4.8.2", "@types/jest": "^27.5.0", "tsconfig-paths": "^3.12.0", "@typescript-eslint/parser": "^5.33.0", "eslint-plugin-unused-imports": "^2.0.0", "@typescript-eslint/eslint-plugin": "^5.33.0"}, "dist": {"shasum": "bce168347a754d1d480705de364bda983805d62d", "tarball": "https://registry.npmjs.org/openai/-/openai-4.0.0-beta.0.tgz", "fileCount": 244, "integrity": "sha512-Sf2p/sX43P3UsuRCpcAdHLqaB+Yql65vgLlwfRh7cexNR/XrVSgaTayTQvJwz/Q37vm2BxEm3MuCcBdRVj7zcw==", "signatures": [{"sig": "MEYCIQDO7HsRX5ySng1nDRMAaav4qKGoR9p0udRFcyFg5Ic+bQIhAPlOX4UrS+jAc+4szUh0yChe0OocLe4oHBXSLxTdUnd6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 492070}}, "4.0.0-beta.1": {"name": "openai", "version": "4.0.0-beta.1", "dependencies": {"qs": "^6.10.3", "@types/qs": "^6.9.7", "node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "devDependencies": {"jest": "^29.4.0", "eslint": "^8.22.0", "openai": "link:.", "ts-jest": "^29.1.0", "ts-node": "^10.5.0", "prettier": "rattrayalex/prettier#postfix-ternaries", "tsc-alias": "^1.6.9", "typescript": "^4.8.2", "@types/jest": "^29.4.0", "tsconfig-paths": "^3.12.0", "@typescript-eslint/parser": "^5.33.0", "eslint-plugin-unused-imports": "^2.0.0", "@typescript-eslint/eslint-plugin": "^5.33.0"}, "dist": {"shasum": "2095a2b28c5e4f884613702c1aed4ab616021103", "tarball": "https://registry.npmjs.org/openai/-/openai-4.0.0-beta.1.tgz", "fileCount": 382, "integrity": "sha512-Qwq8IpMimUDhGQDcUMoPuBCXKzoFUe6UsoFE3nG8ISONr6sMoT8Ps3NVAIKxjls+h13N4Uo+vnPUSZ5rXtEKbA==", "signatures": [{"sig": "MEUCIBJL6an/9QMAyXv3tvVDGXfSNDOADa8f0TG6sy+M1UblAiEAigKcYHofD+Vnhc9MOWhhQndDDmhLd3+UJ+xtQvCNuWg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 724976}}, "4.0.0-beta.2": {"name": "openai", "version": "4.0.0-beta.2", "dependencies": {"qs": "^6.10.3", "@types/qs": "^6.9.7", "node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "dist": {"shasum": "5044e28ccc9a73d1ca868053d4fb74d4383963ec", "tarball": "https://registry.npmjs.org/openai/-/openai-4.0.0-beta.2.tgz", "fileCount": 250, "integrity": "sha512-zTuAxBFe5nSO7LngbV+/O0udtgHWfXb2lFei8/sDY4GB5cOdnrRoSOtiyUfV65ANdvlI4F75oYZX7w067cxj3w==", "signatures": [{"sig": "MEUCIGnj3CFGFsjZPJSNQ7N+NsOxwRHg30yvSlXre/3l98UEAiEAwWm16KobGqRmKg0Hm/YZBmCZW+f46bQbSjYWF4nHp50=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 499760}}, "4.0.0-beta.3": {"name": "openai", "version": "4.0.0-beta.3", "dependencies": {"qs": "^6.10.3", "@types/qs": "^6.9.7", "node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "dist": {"shasum": "44ff2e2ae06f0b6cacbec284d6a05e7b4d7ea887", "tarball": "https://registry.npmjs.org/openai/-/openai-4.0.0-beta.3.tgz", "fileCount": 252, "integrity": "sha512-dSEWpsoS4g90PRgs6vRka7UD52zih04fYQe+w3YCZfrBBOtBqxG7Uv7JaRX4f79YfCHcORNE1ggRocbroKhsag==", "signatures": [{"sig": "MEUCIQDmtCJ/SM6kSNoHL00ZLRzfEWzCC+VU17Hj6c/jfr9xhAIgDPqq+4LCbLmHR/34KapXDIr41o0jWqoIcKbBm5jVFqk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 533941}}, "4.0.0-beta.4": {"name": "openai", "version": "4.0.0-beta.4", "dependencies": {"qs": "^6.10.3", "@types/qs": "^6.9.7", "node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "dist": {"shasum": "f7fdcf8831fee21eaea47e854ccdb646a34436e5", "tarball": "https://registry.npmjs.org/openai/-/openai-4.0.0-beta.4.tgz", "fileCount": 252, "integrity": "sha512-8U1E4IHxemQE/VePwOpd4kaq0/vRTK9GJpHWrF6KQ3GEshWkUN///gCA+PtVFyf+irZrDGqjBAb3C02zn0UZBg==", "signatures": [{"sig": "MEQCIE4U7/TS6T1FVDwe4BNFSKcgRig2LSSaO+LUEpCRFh7eAiAwj9ivjmQ5KF4vrvlnUehD+wUuYHx5EZxgFfDnAngBog==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 533825}}, "4.0.0-beta.5": {"name": "openai", "version": "4.0.0-beta.5", "dependencies": {"qs": "^6.10.3", "@types/qs": "^6.9.7", "node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "dist": {"shasum": "7c10d5513e0f8c26da5f937fa558652e87567fc7", "tarball": "https://registry.npmjs.org/openai/-/openai-4.0.0-beta.5.tgz", "fileCount": 252, "integrity": "sha512-Ksu4w12Q0dTfTY1tR9YPemCLCyx/zk0Ryb6HxWtkFQm8jzGN7yETEJ7RvHWJqa7rHX6WSODHiAWfKzH1GEPilA==", "signatures": [{"sig": "MEQCIESR7nNwTpcAiRAU/JPevgGlFIs9QPUsnUMN/TuugoQyAiBH7WCCD0YWRz/GRuTuAr53vxAFZ23MFjsFxL/kl7J3rw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 544417}}, "4.0.0-beta.6": {"name": "openai", "version": "4.0.0-beta.6", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "dist": {"shasum": "faf2841f0328d4e57e82eac745383632bc2fa8ca", "tarball": "https://registry.npmjs.org/openai/-/openai-4.0.0-beta.6.tgz", "fileCount": 252, "integrity": "sha512-sZscRgs6nhBgIt0qcK8XB2PGga6V0Qy9rQn/vKesox/laQDs9tMaOi6rsDhHq15aXQJPROfEB0K9SZoCuyWbSw==", "signatures": [{"sig": "MEYCIQDMNX/lFE+6lZXmF1JVjqfEI1BDgcRFxFLdBS0heTaU2AIhAPhmzBaIbZOgEAsT+w72YP2ZL7IaT3Spvmrz5oyeznjL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 545353}}, "4.0.0-beta.7": {"name": "openai", "version": "4.0.0-beta.7", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "dist": {"shasum": "4b05af6122fa142e59644e7ffa3389700f1ac192", "tarball": "https://registry.npmjs.org/openai/-/openai-4.0.0-beta.7.tgz", "fileCount": 252, "integrity": "sha512-jHjwvpMuGkNxiQ3erwLZsOvPEhcVrMtwtfNeYmGCjhbdB+oStVw/7pIhIPkualu8rlhLwgMR7awknIaN3IQcOA==", "signatures": [{"sig": "MEQCIE1/1mG1IHbYuM7fbN7tOvBBw96aqgQihEMCD/c6rPkOAiBwYXMDAKW8AxqiYxn8wOee0oRMr3aIja/c9eXkUb5cdw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 549488}}, "4.0.0-beta.8": {"name": "openai", "version": "4.0.0-beta.8", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "dist": {"shasum": "1a871c3ebd54b481bdf3285bbf6d31cb862e4c6e", "tarball": "https://registry.npmjs.org/openai/-/openai-4.0.0-beta.8.tgz", "fileCount": 267, "integrity": "sha512-bXBxUK2B288GlLckjnpT+UgyyddP+f4JIT5/df95C6eeqQhf5UkuN41Z3uf/6unPvR8Tthm4jJje1fA0OgsDEw==", "signatures": [{"sig": "MEUCIQDRRpf8a8DyRmeqZV8ZHlpqr/WYPsD9+I2zyhzuP76+qAIgAYickqV2nFYtuWYXXDjyx38GRXNYg0UxoGNW5VbLXbQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 555648}}, "4.0.0-beta.9": {"name": "openai", "version": "4.0.0-beta.9", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "de41a74f7cd6fbbba17c99815457cb64c48b08fd", "tarball": "https://registry.npmjs.org/openai/-/openai-4.0.0-beta.9.tgz", "fileCount": 268, "integrity": "sha512-0sCeOpKiMDRkvPUDg2zvEVT/d1yhugnQIVdPunID2UrIHMNQGPuYBjvhwRrkAyravPX8jQy56sRKmFQYM3hEvw==", "signatures": [{"sig": "MEUCIQCEAV+nE6glweCf+IRXWkcLewdPv/EtLJDvBcZ6twwyHAIgMpc416LiNU3sPhcBEG6DQYet0w6PmW8Qmr5U3HjAIlA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 567141}}, "4.0.0-beta.10": {"name": "openai", "version": "4.0.0-beta.10", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "f907146c87d44312eb040ead02348dac738dc5fe", "tarball": "https://registry.npmjs.org/openai/-/openai-4.0.0-beta.10.tgz", "fileCount": 268, "integrity": "sha512-uEBByprSmEho3q3OduNBBdSSeigoh/r70hbmPIatokBgwvyhcv+Y6lKOis2fED5sZkR3ZZoP9ao3eY+mslpgpg==", "signatures": [{"sig": "MEUCIQDfCzdy0Z3yPMribXWddFq9ZagiIIzaTxqtU8N0wcom+wIgIbclp65bJhRSoHCMTgLsYHhcbgjGP8zIW0SKzAeM6i8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 562497}}, "4.0.0-beta.11": {"name": "openai", "version": "4.0.0-beta.11", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "a761509bc64135e84d9dc8440d697c6302d2153e", "tarball": "https://registry.npmjs.org/openai/-/openai-4.0.0-beta.11.tgz", "fileCount": 268, "integrity": "sha512-uIvizM2fwO/3ZsiN5fXZt9V6lG5i/3Zh8XXyAGPMCJmwP2ixPh67nc72GpZsozTaRVLSd/ZLgZKfjgEKTHnUjQ==", "signatures": [{"sig": "MEYCIQDgy18/YbQGu7Z+nF/sBvBrTEM2f1qh5OJljrlVXZ+wegIhAKi01MIqqZcfiyJxkkJExmUCXWl2/WUHvQ65D+ldMQlt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 584113}}, "4.0.0-beta.12": {"name": "openai", "version": "4.0.0-beta.12", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "c8f8100a554f61b00f58211e62d23edd47d61bdb", "tarball": "https://registry.npmjs.org/openai/-/openai-4.0.0-beta.12.tgz", "fileCount": 268, "integrity": "sha512-K7PrIyF+Rv2kI8/FKcb1ZiM4L9ORR5J8DnoSgOT+mHrR9d3eBkAdxqVJ4uCXLFTGh7rDzU4pbuD+DpfdgzL1IQ==", "signatures": [{"sig": "MEUCIQCLNPCo+2JZHXzd1SjMcU3h7D6bDiOzf0Y+jzC/MpPQRgIgVWxShQKMVnyKH2PfAz2zC2krB06CPiA3GfWBgM2gM30=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 584559}}, "4.0.0": {"name": "openai", "version": "4.0.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "d3fc2288ffe97ddf3ae9f89bdaa1ec75657e4fd5", "tarball": "https://registry.npmjs.org/openai/-/openai-4.0.0.tgz", "fileCount": 268, "integrity": "sha512-UHv70gIw20pxu9tiUueE9iS+4U4eTGiTgQr+zlJ5aX4oj6LUUp+7mBn0xAqilawftwUB/biohPth2vcZFmoNYw==", "signatures": [{"sig": "MEYCIQDex8ZfAzbfZlBzEKgl7eE4dx470etEdlTmGGFHYW6AhAIhALgY2E+KrvFspPRFCeBk+lE99Bp0Vxcn48Eui/wH8y9F", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 590297}}, "4.0.1": {"name": "openai", "version": "4.0.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "bcf1b4a53ee23f2df0f60e96eadc908c95eed1e2", "tarball": "https://registry.npmjs.org/openai/-/openai-4.0.1.tgz", "fileCount": 268, "integrity": "sha512-UanMv/kCD/ylgOdBrkkRgkoFXdfENrXYWNvYdvPttXZaEJmRVY9MZSknlkWYH7iPMX6tTcz6szbI2d3O04UFag==", "signatures": [{"sig": "MEYCIQD+8xD2bUlFS53FNHiTboGJAW0e7Payc89NWYK166ylSQIhAOAIuerB82EMxsTGhZBbHQziUjToqiO+4lIe59ex9EIJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 596758}}, "4.1.0": {"name": "openai", "version": "4.1.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "06522099ae3e366804d4b7b3bb13e2a16c463b9f", "tarball": "https://registry.npmjs.org/openai/-/openai-4.1.0.tgz", "fileCount": 289, "integrity": "sha512-EaCMqi7JI0xw68PUE+DJkAdykgCZEl203QXWmYWm5wRVUmEC8FAeliLkpeWWEafxnC43UtWNdnTMRImOS3C3ZA==", "signatures": [{"sig": "MEYCIQCMSCuMeObPf+B/iCLw0zjACtphCb/wpBAobQZmXu81OgIhALf9ZVbJtKbZsC66A+Xc1xBdi65Ty4LQS15CZCSjyQ0F", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 632521}}, "4.2.0": {"name": "openai", "version": "4.2.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "ccaef83bccad50041dd3e733e231b0c38f889b99", "tarball": "https://registry.npmjs.org/openai/-/openai-4.2.0.tgz", "fileCount": 303, "integrity": "sha512-zfvpO2eITIxIjTG8T6Cek7NB2dMvP/LW0TRUJ4P9E8+qbBNKw00DrtfF64b+fAV2+wUYCVyynT6iSycJ//TtbA==", "signatures": [{"sig": "MEUCIFu+cmoQFFsPPj5t9Vw0qV6BF+PUfSBVryQf8hPrHMYwAiEAr5CR5bkmzJiznMsKJbEMJ9wps26eSS2taV5A1wv9e/w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 633547}}, "4.3.0": {"name": "openai", "version": "4.3.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "e12320015be7b86469304318ccf9a34e3d81a4df", "tarball": "https://registry.npmjs.org/openai/-/openai-4.3.0.tgz", "fileCount": 303, "integrity": "sha512-ObF5jxvZoQbCNAI6FiiNkzFDinBRbu4KPm73/PKCy9UvjI24kPpnN9kK56rtPDVYxfk78C0A2SK0bJAE633BuQ==", "signatures": [{"sig": "MEUCIQDKL/WlwsWPC0yzTnPiTbI1DOPyeFoXKOcjirCA7V3y2QIgDST44Oz/AZs2UPS116X8WRiGmSRG5mvMDjIsJkZt3+M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 644757}}, "4.3.1": {"name": "openai", "version": "4.3.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "800dd4bfb7dc764a956042197896725f39b6a08d", "tarball": "https://registry.npmjs.org/openai/-/openai-4.3.1.tgz", "fileCount": 303, "integrity": "sha512-64iI2LbJLk0Ss4Nv5IrdGFe6ALNnKlMuXoGuH525bJYxdupJfDCAtra/Jigex1z8it0U82M87tR2TMGU+HYeFQ==", "signatures": [{"sig": "MEQCIETxaaiJ9bhZBTz4aFj0zoqO0aJhe6yFcS8BnMfDWQ5JAiA6I4ir8vXcQrqzq9TO0dYWzMx9ugCyDtr+JOOeUiDgkA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 645289}}, "4.4.0": {"name": "openai", "version": "4.4.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "dbaab326eb044ddec479951b245850c482678031", "tarball": "https://registry.npmjs.org/openai/-/openai-4.4.0.tgz", "fileCount": 303, "integrity": "sha512-JN0t628Kh95T0IrXl0HdBqnlJg+4Vq0Bnh55tio+dfCnyzHvMLiWyCM9m726MAJD2YkDU4/8RQB6rNbEq9ct2w==", "signatures": [{"sig": "MEUCIB5m2ZY/i7I/cM47BCUtzV6ilWaOQ7sWrQBYwglVsbihAiEApWJr6eaFyjqcX7cnvnuWuPjY4g2sUkROCn0726lAPiA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 650294}}, "4.5.0": {"name": "openai", "version": "4.5.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "ef968fe1a86af26f4184fb1e016fd6aaf96d49e3", "tarball": "https://registry.npmjs.org/openai/-/openai-4.5.0.tgz", "fileCount": 302, "integrity": "sha512-9cA5i6KpEAeLV8nZrVgOXqRmUWUIu4FV2oRKvSIwrA7Z5aMd4sXVOaqWAuRDhFFewirxhys8D6R5B2Drj1pKIQ==", "signatures": [{"sig": "MEYCIQCnI76vSW2R87c4nb/HzhHie5c+x2/YBoGizQHGXeeTYQIhAN4sAwskz3RK0mKFobL5zprf1GO/O7pZ1l9X3yTrskoA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 657359}}, "4.6.0": {"name": "openai", "version": "4.6.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "0335111cf71c608b68dbdca19f8ccbd9cfb0cd97", "tarball": "https://registry.npmjs.org/openai/-/openai-4.6.0.tgz", "fileCount": 302, "integrity": "sha512-LuONkTgoe4D172raQCv+eEK5OdLGnY/M4JrUz/pxRGevZwqDqy3xhBbCeWX8QLCbFcnITYsu/VBJXZJ0rDAMpA==", "signatures": [{"sig": "MEUCIHfggTKqRRD9/P5T+199TZQg85VMUrmIAl8MjNL4Pt/dAiEArtx5Ib2V56k5HG3A+gNz7uEWoAbdEcOAcDfRYQ6X5cg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 657911}}, "4.7.0": {"name": "openai", "version": "4.7.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "df0e0dfd5f03e9f60bcf8551054d9ac9032d50ac", "tarball": "https://registry.npmjs.org/openai/-/openai-4.7.0.tgz", "fileCount": 302, "integrity": "sha512-URaLIqEyh60i46h0yM296TA5HRTK4NY+mB718METphyQZo74Vd2s0thwaHZMWqYc/3LHFOokpZLpW2HNiP54sw==", "signatures": [{"sig": "MEQCIFVRQJSeiMrTGb9lrPzX9qsoyM8yOqf9QI1POLHd0iRMAiB5mOxi6w2FK+zEBlv5RkXeYqX9HmUe+H1ssjK/qDHedw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 661325}}, "4.7.1": {"name": "openai", "version": "4.7.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "dbc13f5e830365f8e927026acc5cf68255e57909", "tarball": "https://registry.npmjs.org/openai/-/openai-4.7.1.tgz", "fileCount": 302, "integrity": "sha512-4Um9A4aLGzZxyENyway0zVgi69BOxaqXmjOCKp3PUteOvSn9TeVf6IjkaNY8k/LXYG5l2e7PpacOl2sxsrTc/w==", "signatures": [{"sig": "MEUCIQCyGcQLVYQELrwkrkIdS3Qu/BSa8y8ZKyy4RvnxmAwkywIgDovOsrscWy8cxI4kySt53ELpKu71KsJHHUeooxZ3zXw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 662875}}, "4.8.0": {"name": "openai", "version": "4.8.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "42e818e484d5c831e438439ed202b08d67416b1c", "tarball": "https://registry.npmjs.org/openai/-/openai-4.8.0.tgz", "fileCount": 302, "integrity": "sha512-CnLZvHi2x4pIoGAWCaj3jHi1a6NA4oFBL6mJDSXkIR5A/wv6lven7uL2gxMevjGBLA7OqYqis3Z2PMluiGauVw==", "signatures": [{"sig": "MEYCIQDUk3FFUztDYBw47pkSWTyB1sDPEr8qWKDyECTlohCBtQIhALY0hWPGm3UTEZ8XtrwpqB4kjFMkVamqLOKezpVLBiZX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 663429}}, "4.9.0": {"name": "openai", "version": "4.9.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "ade7ffb61cee7ff23b693c1bed0c892f1a764a5a", "tarball": "https://registry.npmjs.org/openai/-/openai-4.9.0.tgz", "fileCount": 322, "integrity": "sha512-ZJqPZiMk0zdlTjGFZIfR52yyCVdkJjw+VyjHtgmCBcldhDO3nyOXxLRzlr6OZ2i1IWb/2KM/ccYKIHQz3BKJoQ==", "signatures": [{"sig": "MEQCIH+WCJeGPtqr8V16R4cDVbULBvSg6Em/iEx7CFgPwEVAAiAe5oPL1NMvNlmK2d1x9iD2yJ552/hzDg1vcX+aIcRieA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 711860}}, "4.9.1": {"name": "openai", "version": "4.9.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "fe5c11a61f298d2d70cfd6163e146307581c71bc", "tarball": "https://registry.npmjs.org/openai/-/openai-4.9.1.tgz", "fileCount": 322, "integrity": "sha512-R+ahYXaBYcDvyBrFpor2WZcNqh/0/+3rtCLy0/m2nriBuB9UecpKxllk6kfrwNkkLryCi1cs8tWjvCX2Ny27Gw==", "signatures": [{"sig": "MEQCIFaLcby6A9u9IUC4ZhC/VbsqnljdvIYS5Kw0M7TjPGyfAiAQ6bRH3DQhOAvxGiU6LuwHL5Dla3U1vwSf2X7m4sWnEw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 712267}}, "4.10.0": {"name": "openai", "version": "4.10.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "60d8f2a469ac7109a34115c75b6d66edbac1d914", "tarball": "https://registry.npmjs.org/openai/-/openai-4.10.0.tgz", "fileCount": 322, "integrity": "sha512-II4b5/7qzwYkqA9MSjgqdofCc798EW+dtF2h6qNaVLet+qO7FShAJTWnoyzb50J4ZH1rPxRFAsmDLIhY3PT6DQ==", "signatures": [{"sig": "MEUCIQD5mQNKgs4yPf6Uge9msFg8PluVas3v5riCP4fYO3UdpAIgKtbJotWW+/MjZsquCYWVri8EbE5ns0Y+M29egMQrwks=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 716419}}, "4.11.0": {"name": "openai", "version": "4.11.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "8adc03e6f74e71cc501d798d81209aa06e4ca14b", "tarball": "https://registry.npmjs.org/openai/-/openai-4.11.0.tgz", "fileCount": 322, "integrity": "sha512-zU/MJxZTijL0Ym6CKoQPbnmHDsGZlH9g5zorPszdc41OyLxlhnlrorBcGzmGS9qpnjGGNncJR1hfg/mXq0OONw==", "signatures": [{"sig": "MEYCIQDh7bW1VGnsnXTDvpfOtSruEHzIAYkkqS42ZgGFN+ROxQIhAKN0fIjdkjTpp+Q3VVVm4X/PBAoYTZkWbufyV+Dt0vXA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 720987}}, "4.11.1": {"name": "openai", "version": "4.11.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "c2ed9b5e58c3c626b8d43e4eae0cfdbcd44cc844", "tarball": "https://registry.npmjs.org/openai/-/openai-4.11.1.tgz", "fileCount": 322, "integrity": "sha512-GU0HQWbejXuVAQlDjxIE8pohqnjptFDIm32aPlNT1H9ucMz1VJJD0DaTJRQsagNaJ97awWjjVLEG7zCM6sm4SA==", "signatures": [{"sig": "MEYCIQD0idiOnvqZNJvJ7iBVSAXmL2FLlL/ORVEO7SGjS14j5gIhAKRNxQSjLSbHtR092Ok6FH0sr6YrGLqlgJV2Mxm0jcRC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 721113}}, "4.12.0": {"name": "openai", "version": "4.12.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "0de844eb8ff148b30ebfe7aec6083fa02b67eab3", "tarball": "https://registry.npmjs.org/openai/-/openai-4.12.0.tgz", "fileCount": 322, "integrity": "sha512-vuF8625uHqhaTLMBwCXbLiUBz4Do9LCzY86M3z1/8vnlxKSuFzp/9/1AQQEaqJ95KxHmZbnITBlHhqqoPWwuww==", "signatures": [{"sig": "MEQCIBBKZQVXULnjNdv+r1qr2rf4anm4lRitQFesi4w9Pb2BAiB08HRXIY1ELDQ2HzxIqppUTGES1mVuRpEWwAp4JB1uqQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 723279}}, "4.12.1": {"name": "openai", "version": "4.12.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "f1ef4283197cf2ef932abc55afeae8a2182d8fe6", "tarball": "https://registry.npmjs.org/openai/-/openai-4.12.1.tgz", "fileCount": 322, "integrity": "sha512-EAoUwm4dtiWvFwBhOCK/VfF8sj1ZU8+aAIJnfT4NyeTfrt1DM/6Gdd6fOZWTjBYryTAqu9Vpb5+9Wu6JMtm/gA==", "signatures": [{"sig": "MEUCIQCRifIF55G2CgWr1nGyadMv2UCjE/AASed/8u3Yg9PYTwIgBS+4Q908SCOK3nsqJWRZDT+yKy7vYXjLJU35b3Jyiiw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 727989}}, "4.12.2": {"name": "openai", "version": "4.12.2", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "084e2c539837bb389ebb1467f5a7c8a573da2257", "tarball": "https://registry.npmjs.org/openai/-/openai-4.12.2.tgz", "fileCount": 322, "integrity": "sha512-3ijtNbjN3zIt9Ltpu0QJcftjqzbvosaoaj17C1dNtqdslZKFuwDXpTL0g0ovIAIBIlyI/FNLTJGm31Mph7d/XA==", "signatures": [{"sig": "MEYCIQD26IOBoUIt8hna1cz7wMXPXeaTAFPpscOFFAjTWKY0lQIhALym7S0Lo+KpVLAtfej6pK3AeLkY3PN0nqr4brlCcm+n", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 756385}}, "4.12.3": {"name": "openai", "version": "4.12.3", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "4a0e624812b672e5e3aeeb1232d682af5ab4623c", "tarball": "https://registry.npmjs.org/openai/-/openai-4.12.3.tgz", "fileCount": 322, "integrity": "sha512-Kw8M8qioKg+iw2tCpdJ1G772uV0tk4SRF1MtySYSPr82YH+Csl61kYv2KzKAOqINcOVn5eSN26t0ImIsdJexfw==", "signatures": [{"sig": "MEUCICpShc6MogmkOYwgmGfmucMgGw1hgYf5O92tGafP0Nw7AiEA9UsSjYM5UAU35M56ldmUYldEZO+PzSD0PRXKkU7ApeQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 756742}}, "4.12.4": {"name": "openai", "version": "4.12.4", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "663f75611de766904942422cec7fbbfc0a906c77", "tarball": "https://registry.npmjs.org/openai/-/openai-4.12.4.tgz", "fileCount": 322, "integrity": "sha512-oPNVJkpgxDUKF6WGGdHEZh5m/kjmYxS2Y1q7YVFCkvKUGthb8OGYRGCFBRPq5CQJezifzABTZRlVYnXLd6L4vQ==", "signatures": [{"sig": "MEUCIQDV5IMVELoQuYHEmrrCUCpSiuf/yH1x4aQHvSUYXKSwzwIgd4oDQtoa75InKfiF+C+1RXouWtN4MgaH1FEwv56doSE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 757337}}, "4.13.0": {"name": "openai", "version": "4.13.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "9645754564eefcb81b5d731b2b2cb039d5ee55bf", "tarball": "https://registry.npmjs.org/openai/-/openai-4.13.0.tgz", "fileCount": 322, "integrity": "sha512-EPqHcB0got9cXDZmQae1KytgA4YWtTnUc7tV8hlahZtcO70DMa4kiaXoxnutj9lwmeKQO7ntG+6pmXtrCMejuQ==", "signatures": [{"sig": "MEUCIBzPgF4yPJrxEDvRJkX3HVV5+J0EIJTZ1ma99ffoD9JDAiEAxgkPksvbOpABCjrss8enLKImOx3Xnamc2C9r6gUO6aY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 759018}}, "4.14.0": {"name": "openai", "version": "4.14.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "5177437683e73bceea1803ea7605905a6e95a0a3", "tarball": "https://registry.npmjs.org/openai/-/openai-4.14.0.tgz", "fileCount": 322, "integrity": "sha512-pWQYkFWdeudR1yLyS/rFfIv/MTXqFEtlN4EKVm0F6KKKGjhuthznPOCC4hxfAFmjlgbpJJXhQQ/oBeB9bxFmUw==", "signatures": [{"sig": "MEQCIEu7XeZ1lY9hR2b4aCJ/2cw8Y45geck+nsYuYlXPpdj8AiA4/+FHrFGT7jj+6iT8tEcBvOvhEQZ9li0WUQjZdNQ04A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 759419}}, "4.14.1": {"name": "openai", "version": "4.14.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "26ffa8e86a57da6595b46350355a754d934e61a1", "tarball": "https://registry.npmjs.org/openai/-/openai-4.14.1.tgz", "fileCount": 322, "integrity": "sha512-aBb7DVdzSnEUBFHTbnVoitauefvjRuUHS5pa7lm1m5JmHifD+1Hff1RzxYC12ogugVcCmWT99NZNfzyD6n/0IQ==", "signatures": [{"sig": "MEYCIQCXwG3YcHalNrZ2b+hCztzjJOy5jVwwa9TxhMWDeR02YQIhAM3/nxBKeK6f7vosXnB4wCoaFvP6NYnS5lStZ7sPsjgJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 760113}}, "4.14.2": {"name": "openai", "version": "4.14.2", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "edc09e986a922da22d14b96b547c29f435e724ae", "tarball": "https://registry.npmjs.org/openai/-/openai-4.14.2.tgz", "fileCount": 322, "integrity": "sha512-JGlm7mMC7J+cyQZnQMOH7daD9cBqqWqLtlBsejElEkgoehPrYfdyxSxIGICz5xk4YimbwI5FlLATSVojLtCKXQ==", "signatures": [{"sig": "MEUCIF8CY4L+vtvqOagI9rLZaD2ESZltevdcOUIhoblvj0VNAiEApRmGMQ0zQpj+nmJJ/PidvP0q6y8GpxoexZ0rDd+ka2g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 760439}}, "4.15.0": {"name": "openai", "version": "4.15.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "96a64fb829a56fe8a22231fffd790b6a63d5273d", "tarball": "https://registry.npmjs.org/openai/-/openai-4.15.0.tgz", "fileCount": 412, "integrity": "sha512-LPNgtG9gxRphH+7AUM5Q/dS/iWuM0UOKf+723e2kvyENNVMjYFrnQjCdcb2nNJY7+ao+J2jP/PfsXBLN8Si7iA==", "signatures": [{"sig": "MEQCIFPunKcj+LvNLTCY74gEaoWDhh5Aqtn1iitknOi9F2sXAiB8Gl4TlP6VJDeeJfWdtsVxB6AmhPcQmtSbQrtTsytaqA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1330914}}, "4.15.1": {"name": "openai", "version": "4.15.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "e8cbf14fbf5f3221a7d8d4b292254327b16e76e5", "tarball": "https://registry.npmjs.org/openai/-/openai-4.15.1.tgz", "fileCount": 412, "integrity": "sha512-cpvjjLppNgR54Hr9/T+qgBhq2uceqRrf3vtx7LH8sdJ+8XjSE/WZ+/MvOho2Rm2l1ZBN96WYpgOpAqCtlBiIeA==", "signatures": [{"sig": "MEUCIQD3iLAS8x5QjwkRFmoM+KY1wW/nujXlUxOPfMroftgBVQIgcWSvzEpgUxvb+Cxr5NnofEZiKb1oteQpSk/rjgAHWuQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1332949}}, "4.15.2": {"name": "openai", "version": "4.15.2", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "3a85a7e8970531c6a5b189c42f4762ac2b06688d", "tarball": "https://registry.npmjs.org/openai/-/openai-4.15.2.tgz", "fileCount": 412, "integrity": "sha512-tlBCXRG7aFaDJG9HDRIaGU7wGXUG2Vood84aejjnPT8UFD9+y5qF/lwOWjCv6gaCV6lVEiwY5GxAPTeqr76iIQ==", "signatures": [{"sig": "MEYCIQDxt0afS7i+0EVcN9WSNEnN3Djn/witturJau1Zmt0UZAIhAJpIDio7XFYiNlQdFJ7rvsN8cKt5hBzTs9gIw3pvumnU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1333158}}, "4.15.3": {"name": "openai", "version": "4.15.3", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "d281e9a20259a13976c01c8c30bc0a5f50afc818", "tarball": "https://registry.npmjs.org/openai/-/openai-4.15.3.tgz", "fileCount": 412, "integrity": "sha512-j2XSxxiOhF7lCiHUXmuDOGOgtKa36ia9pOQ2m9YCOMA2Ee4QTI+MzdHRoHlp6ewOsvW5NXkoT+xustSZljiGnA==", "signatures": [{"sig": "MEUCIQDuKCu82MsPH4LdzmA9QwmHCbVkfbfrT9IODJLMupBVDAIgedfkuoKf4CWlopgJCDQrJ69B0Co9/JvI6Jf1gVYoYJw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1333482}}, "4.15.4": {"name": "openai", "version": "4.15.4", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "7c6acdb5b02806a663fdb142cc35cecc3792aab2", "tarball": "https://registry.npmjs.org/openai/-/openai-4.15.4.tgz", "fileCount": 412, "integrity": "sha512-EnlSl1p8n7Q/HnBf4+VOEcYloBKKe23sKOFfH/WJcw+XVyWav4lwDK4wCmsUY1wS4RFOdbA2EwBUB2p5WEPmoQ==", "signatures": [{"sig": "MEUCIQDI6lsEWS7Trbv2Zb6LPed9/Su4JgE9m2cXQ30hbCr8IgIgMLrmmbgI/UsI0puhLqio4RT8dTfVKHB/gmsyR1SjEKQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1333779}}, "4.16.0": {"name": "openai", "version": "4.16.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "de494b6602065564203dfc8ed5a12894f89901d1", "tarball": "https://registry.npmjs.org/openai/-/openai-4.16.0.tgz", "fileCount": 511, "integrity": "sha512-P7IWqvaP0EuD3cQvzmU24KprnS0tHavrTuMiChEOa7pugqrKUfcmTnHcr+w01sJHny3vYoTvydCZ77cQolYj+g==", "signatures": [{"sig": "MEUCIGGgE3ZphskZLjolHrC4MGHH1v+db9yR8UGEqVJeH87HAiEA8Uwz+QpEkWGITSf5JwXjB9mELI03Hm+5d3qdfRmAvME=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1668103}}, "4.16.1": {"name": "openai", "version": "4.16.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "6377682ad2af805affd1b401958fb6eb92a87d61", "tarball": "https://registry.npmjs.org/openai/-/openai-4.16.1.tgz", "fileCount": 511, "integrity": "sha512-Gr+uqUN1ICSk6VhrX64E+zL7skjI1TgPr/XUN+ZQuNLLOvx15+XZulx/lSW4wFEAQzgjBDlMBbBeikguGIjiMg==", "signatures": [{"sig": "MEUCICyAYP+FcRSEXU4Gf1yc+XlIe5yeBgMUvm1dQ+F5TP4QAiEArLd02nfAXhKE6qFaV4Oz2BunzfVM9AaUDMRxEQLg0j8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1668315}}, "4.16.2": {"name": "openai", "version": "4.16.2", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "c386ada1d091194fb45212bbc69a97116e2f68f3", "tarball": "https://registry.npmjs.org/openai/-/openai-4.16.2.tgz", "fileCount": 511, "integrity": "sha512-jsrv008Z3SkeiJ2USdJTrD/tkwBHK6YYv7cwNxkfhgAu1GUBKEDiuYKLLXtvVxdrU9gK6LXnIb03ugwoBgEimQ==", "signatures": [{"sig": "MEYCIQDoZZIetDr93OR19Lwn7lV0FgWR4w3HxhbWYSPY9TVb9gIhAIrF0rb9Zt77U7HC8VMqGu4Ok44tVkfU8MpN/F0hbPIz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1671706}}, "4.17.0": {"name": "openai", "version": "4.17.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "92e2397032e95c39bb1f25caece44060eb5218f3", "tarball": "https://registry.npmjs.org/openai/-/openai-4.17.0.tgz", "fileCount": 518, "integrity": "sha512-fJUdfOzKE49L2oitQ+L8oCYcTDI2oLOYmQvnPjklFrmH7M+8w65/8Ag9120ky1A3Xr3lCivALs2oBmNaZIvTgg==", "signatures": [{"sig": "MEUCIQCsGYTFtT4g8xAKjDo9v+wPcVPg3+9jGXq1MOobOEuJKgIgRWIKz1d+gYO1JS1cIRgqZG9FeMOPvRKzJb7vu7IfaYo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1661370}}, "4.17.1": {"name": "openai", "version": "4.17.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "160fa9f0cdc778eda2c0a28bc72e64aba8ef462b", "tarball": "https://registry.npmjs.org/openai/-/openai-4.17.1.tgz", "fileCount": 518, "integrity": "sha512-8IrjeT9B63/6rKA2NI/0LFnjRA37G+NvNwbRvcEH9+AjAahReSMtT8xs4J+ClhzdUL9d91GJymgbo9RJt+HQuQ==", "signatures": [{"sig": "MEUCIQD7kThfSObo9JwZ833kW/LDYDoqU4cvjxuVtgVWGfHb3wIgfSr3+5FU6rZLafVqHe8lGLZCU8p+8gtrOACQHa+WH4E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1663400}}, "4.17.2": {"name": "openai", "version": "4.17.2", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "f7afb9617705aa080a10d03e14372bf9bbb51fe0", "tarball": "https://registry.npmjs.org/openai/-/openai-4.17.2.tgz", "fileCount": 518, "integrity": "sha512-7wh6Vyz3Fuay4aGLQF7T9bMUfj7pJHjg36bZJeWzkrZwTrB4HnKcODFPDkponE9obP+DmIbuL3Q68s9uUB6K3Q==", "signatures": [{"sig": "MEYCIQCk13RVZRL+nP+5xl6rXSMV+4WN6KpoVK+fAzUJD9NOvwIhAOph0d+jV0B5ykmedVWESrFciqDZO93YWL0cW4H/AjUq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1663738}}, "4.17.3": {"name": "openai", "version": "4.17.3", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "c07948a4ffda2883643f31f769cabc77c26ec15f", "tarball": "https://registry.npmjs.org/openai/-/openai-4.17.3.tgz", "fileCount": 518, "integrity": "sha512-Gx9wzl9HWX5pjagkgXVu6U2BTFEPkQFdkppNnAX2n2Rpjtn2zt152wXh7NnZ5eJuVxUGYzRe66JmayAEGjzqAg==", "signatures": [{"sig": "MEUCIAY+4WSaPJC5+l2A6V6mRD9Gbm9kFPZlN9m03jXFDVQuAiEA02EqgoZllyLUav8PsebPmj/I1AUJQgQTugHBjoYOYuw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1663864}}, "4.17.4": {"name": "openai", "version": "4.17.4", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "984cf61fb2d4c3dae168d7295259cda7e677ad4d", "tarball": "https://registry.npmjs.org/openai/-/openai-4.17.4.tgz", "fileCount": 518, "integrity": "sha512-ThRFkl6snLbcAKS58St7N3CaKuI5WdYUvIjPvf4s+8SdymgNtOfzmZcZXVcCefx04oKFnvZJvIcTh3eAFUUhAQ==", "signatures": [{"sig": "MEQCIAHBKGOUBzcpeGsfcfRla6txQKJgytauZtpZBfnW8gh6AiANyzpqll6reRHmV+IwZYbq+mewepmK40Kgw/gb+aJzWA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1664453}}, "4.17.5": {"name": "openai", "version": "4.17.5", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "096655741965656ec969731e97d4bce880112d66", "tarball": "https://registry.npmjs.org/openai/-/openai-4.17.5.tgz", "fileCount": 518, "integrity": "sha512-SDgA933/QOjISCgWRc/JQhY1HweYZ6FOie3bWrCpj09FA5xIlaomldbyzICHNjtkh7SWEmGYFjRHIDtuwr+eTw==", "signatures": [{"sig": "MEUCIQDNDRZ3cXuc8QTL/koKK0ljUiN7O3FjvyJ66UlDI/EvqAIgUdowlEqPqpphX/O0vb0MHMq5go+2Oj5/C5PHUw1MjX0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1666304}}, "4.18.0": {"name": "openai", "version": "4.18.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "445f2001c2261ca3b4372039103fa5fc072d70bb", "tarball": "https://registry.npmjs.org/openai/-/openai-4.18.0.tgz", "fileCount": 518, "integrity": "sha512-0C+waXoU7mvp/1QZzwnmPCkq4t02VJkjL1EXn0Pd7pFuTA0Y4y1LwzSZSiF/tpxw+Pr7diHVJjbfJ8QbdZnEcA==", "signatures": [{"sig": "MEUCIHvrbYm+agkz21KHQp5EKU5ARxA73Nrk0TbMajRoC7PLAiEA0jMfkJwYSbVU1CCPLZyk503BpSd4m87/CA9rM/2vjr0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1666700}}, "4.19.0": {"name": "openai", "version": "4.19.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "201bf971d2296b028f852fe9dabf61c4e5b96e71", "tarball": "https://registry.npmjs.org/openai/-/openai-4.19.0.tgz", "fileCount": 518, "integrity": "sha512-cJbl0noZyAaXVKBTMMq6X5BAvP1pm2rWYDBnZes99NL+Zh5/4NmlAwyuhTZEru5SqGGZIoiYKeMPXy4bm9DI0w==", "signatures": [{"sig": "MEQCIA9iKSgu93BLmf/kN2BByi3ZYpuOauyi4XW6aGYJJyWZAiBpmN5w0sLuZfyYF2CP4aklPi7ZhFM6CJliY2uN7P5kMQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1663957}}, "4.19.1": {"name": "openai", "version": "4.19.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "229d6e994248966f255f6a5b849dcec28e3b3439", "tarball": "https://registry.npmjs.org/openai/-/openai-4.19.1.tgz", "fileCount": 518, "integrity": "sha512-9TddzuZBn2xxhghGGTHLZ4EeNBGTLs3xVzh266NiSJvtUsCsZQ5yVV6H5NhnhyAkKK8uUiZOUUlUAk3HdV+4xg==", "signatures": [{"sig": "MEYCIQC/QuXYNV1t/E4pa/rOm74kKL7XoQHj6OE9yElnLfk9EAIhAJzlOcEfSLdbNd4tZf5dOH7SRBo8phFbwbvkHlwKtENi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1664083}}, "4.20.0": {"name": "openai", "version": "4.20.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "d171b5afb38e0c5ba07f9dd7b15b5c4bbc1f1bda", "tarball": "https://registry.npmjs.org/openai/-/openai-4.20.0.tgz", "fileCount": 518, "integrity": "sha512-VbAYerNZFfIIeESS+OL9vgDkK8Mnri55n+jN0UN/HZeuM0ghGh6nDN6UGRZxslNgyJ7XmY/Ca9DO4YYyvrszGA==", "signatures": [{"sig": "MEUCIQD4UXuXWlLgVUB+1WNWF35869lR9P9z03mPpBptAKYVSwIgV9ExFGGQ7ROj21LUa4ZA7EegYZ9+vDDjqOS4FFYkj3o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1664704}}, "4.20.1": {"name": "openai", "version": "4.20.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "afa0d496d125b5a0f6cebcb4b9aeabf71e00214e", "tarball": "https://registry.npmjs.org/openai/-/openai-4.20.1.tgz", "fileCount": 518, "integrity": "sha512-Dd3q8EvINfganZFtg6V36HjrMaihqRgIcKiHua4Nq9aw/PxOP48dhbsk8x5klrxajt5Lpnc1KTOG5i1S6BKAJA==", "signatures": [{"sig": "MEYCIQChTRO8nF4D4rp1/ESi93moQrLKRoalX7fMHtR7H795iAIhAPGIOG16UupvjMR7V76OIc1rpTw+CUvCZP0vxHs5E/XV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1665355}}, "4.21.0": {"name": "openai", "version": "4.21.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "8560ad4541b55af52c7c1c68857e94be1fed6827", "tarball": "https://registry.npmjs.org/openai/-/openai-4.21.0.tgz", "fileCount": 518, "integrity": "sha512-HT0Jm2iGI5+Maq7Da/DPTeIAxNvpa5pamkhlNnJJAqJgVjaFDvMUBjGhFJoVohkYWwZjM9oSyfSC0eoVNPioaQ==", "signatures": [{"sig": "MEUCIEvKKFlw7Xb84KDC4afOs96PsDG6dzoEfx47Bgur7mMQAiEAz42NgRqkd7L4zzn/CqkpSYB7eMiynw5SmRINJzMkq9g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1701301}}, "4.22.0": {"name": "openai", "version": "4.22.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "ae472627d7a27b7a112d3152e9581679f4772884", "tarball": "https://registry.npmjs.org/openai/-/openai-4.22.0.tgz", "fileCount": 518, "integrity": "sha512-HrRx5pvkeG5H8ihaI8GmG1ls+H2pkLaFewIFmvh9Dnwz9GNcGQS6CSh5PjgalnYxE5Vu9jvz2ikRgw8xhOFweg==", "signatures": [{"sig": "MEQCIHql+GtJHYr9PuYwCAR4iZYm5cIryu1d9eWwTfnikEQSAiA7AHDKu/WHnI4e+zKHHK9x3LzQqzyJbf6HrXFXvqVFPA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1703683}}, "4.22.1": {"name": "openai", "version": "4.22.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "ef228d0da42d9a5ae150cc4117a762ba1618b437", "tarball": "https://registry.npmjs.org/openai/-/openai-4.22.1.tgz", "fileCount": 518, "integrity": "sha512-Igk2FixXiEDQKkS3VJR0tTpO27O48mJqH4ztayATHTvcAmKqrIrYOjUBc7DrJcq+cKcQS5lTQalGZD05ySydHA==", "signatures": [{"sig": "MEUCIQCy6wbDrQo69hQSt4e1FgEkh0LrQP+JSW2g4HsO+nG/EAIgehFDYV1sa85vEl/L16dPn55ez32TWcAUZBe/oJ8CRMo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1704529}}, "4.23.0": {"name": "openai", "version": "4.23.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "17b0e8493094e658896b683c6bf1824246d4d47b", "tarball": "https://registry.npmjs.org/openai/-/openai-4.23.0.tgz", "fileCount": 518, "integrity": "sha512-ey2CXh1OTcTUa0AWZWuTpgA9t5GuAG3DVU1MofCRUI7fQJij8XJ3Sr0VtgxoAE69C9wbHBMCux8Z/IQZfSwHiA==", "signatures": [{"sig": "MEYCIQCpMNs1ue1dAkrDc+HejGcmlJCeAvDc3YHqZx8cXfOtqgIhALpFuvDISbCS0VtgKajk+IX33Q+Y2muu4Qb6xxXwu5Mf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1718072}}, "4.24.0": {"name": "openai", "version": "4.24.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "0870accfd7a4a95375d5fe2caacb46b809da5611", "tarball": "https://registry.npmjs.org/openai/-/openai-4.24.0.tgz", "fileCount": 518, "integrity": "sha512-h8fsas4lu0ul18oDzFIZpEQryh2SJEUJWanyfEwjME/klcKWZM/blDz2cKiT+aFBqPJzcYdCUj/8E7n20f0jEw==", "signatures": [{"sig": "MEYCIQDqd/SCkIvj5H9MKJgkahieGc22nrrehYFqDv8MXDQmvgIhANTa8u2Vw/rwbgiuOgVo6BrOZbQQgsdhm5cBUVSHqH3t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1720348}}, "4.24.1": {"name": "openai", "version": "4.24.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "3759001eca835228289fcf18c1bd8d35dae538ba", "tarball": "https://registry.npmjs.org/openai/-/openai-4.24.1.tgz", "fileCount": 518, "integrity": "sha512-ezm/O3eiZMnyBqirUnWm9N6INJU1WhNtz+nK/Zj/2oyKvRz9pgpViDxa5wYOtyGYXPn1sIKBV0I/S4BDhtydqw==", "signatures": [{"sig": "MEUCIQDhqyiLT9zWlDxa6dkG+sOMAlxNJCwsU5NpxWPIFsPY+QIgNxOJipTytm9535EBjPV3bFUDO9PG2czUHGSXbM86WWk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1721403}}, "4.24.2": {"name": "openai", "version": "4.24.2", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "7b58236e28e8b94277c12c4fb6ead8351af9c7c1", "tarball": "https://registry.npmjs.org/openai/-/openai-4.24.2.tgz", "fileCount": 519, "integrity": "sha512-Np5zFPAYsBRmsfgPS8cMM25r1wVO5NCTdmeoYDNEUhJn/rw6jNXU0CFSFwhbWsjIkYhdv31pPIzQ0xChHCvltQ==", "signatures": [{"sig": "MEQCIQCvDVrd/NUWHVxHmk0FNlr+/L1LzEY/BD9r/YcKZWpntAIfVKfQBaNpTKahvArBnna+dhkWe04SFC6o8YuBJ8oFRw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1726194}}, "4.24.3": {"name": "openai", "version": "4.24.3", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "bcb41763891f2819470f48de8d734f78c2985932", "tarball": "https://registry.npmjs.org/openai/-/openai-4.24.3.tgz", "fileCount": 519, "integrity": "sha512-trXt1hwYmJTwQCzYZiKijOYslqvterDwohTncadZOTXXJh+XV4Jf9KcLP9dDRlXh0mAxGXaVzbpiNgAXa2khNQ==", "signatures": [{"sig": "MEUCIQCpof46cPw7cyy5EvYCDC2YZp0EsYG+pMXw979G+kAqKwIge/6MLOkXNMmqXCqFQOgQ6trNThu3CMMUWRLEe6w3MOg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1726848}}, "4.24.4": {"name": "openai", "version": "4.24.4", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "d455465d1ebda2306508b0e3c43de22c0e5c39a5", "tarball": "https://registry.npmjs.org/openai/-/openai-4.24.4.tgz", "fileCount": 519, "integrity": "sha512-y58aEgq4HFCkfwiVCAvdxTkjGqHUWYFx4vJP6FWmH5udJn5SooZNXn+0N6uAe0ShmUT6846aZy3boJ0iN/KWvw==", "signatures": [{"sig": "MEUCIGTIXDh0If06DrCp/k7MG+HjUr+nn9214caudINVgiQjAiEA/Xmj+3bXLtUz0QGwZD3FKiAHRVnLQEjp02TX5Mu0MH8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1727516}}, "4.24.5": {"name": "openai", "version": "4.24.5", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "c6a8939a5653cdb23cdc66afe38fd1249a1a66a4", "tarball": "https://registry.npmjs.org/openai/-/openai-4.24.5.tgz", "fileCount": 505, "integrity": "sha512-wkNX6ZhWJH9i4guB2GTsloiky+a8I4/RoZzgMdpJUsIZomETOoIcmzaesBZKOgdBPEWd6pjqC28s3q6M970JfA==", "signatures": [{"sig": "MEUCIQC7gA3Hen+brALNgSFdiBgVf3EI5k/vJTNJ7VJTJqC6HwIgBpH72uNxaRDtqEWfYTu/1sWgCCd61dSYX9r3lgpfbGs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1672116}}, "4.24.6": {"name": "openai", "version": "4.24.6", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "256f7d6c774d07a2d29f1df4b9621567a5cef280", "tarball": "https://registry.npmjs.org/openai/-/openai-4.24.6.tgz", "fileCount": 505, "integrity": "sha512-ekNHYtPHnxGlwu6SQClRxIPPi34Gy/6WqpUIT0wrX+tgCXZZEmmgnWeep2aYIGPceMj9cIIY5sS70NBJ1FHRcQ==", "signatures": [{"sig": "MEYCIQCg8EWY0YyCZGYoeX3rFP/CgKzwigxs4Djx93s2sWs7TwIhANuMKz9+5g3U442Af/NFqnYY0+7dsP4SZ2XKMC1CfWmB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1672707}}, "4.24.7": {"name": "openai", "version": "4.24.7", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "65b206bcf15c11202f758b5e1e945ffa13fd6b7f", "tarball": "https://registry.npmjs.org/openai/-/openai-4.24.7.tgz", "fileCount": 505, "integrity": "sha512-JUesECWPtsDHO0TlZGb6q73hnAmXUdzj9NrwgZeL4lqlRt/kR1sWrXoy8LocxN/6uOtitywvcJqe0O1PLkG45g==", "signatures": [{"sig": "MEQCIERh81sDJ3zKj8+kT4UN8R+KUyqDwCXUO4NcSILAY9XeAiB/Qg+Fr5p8TUqeXcVKxrGae9S5mGrrvYqxnyDXkiHVgA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1673287}}, "4.25.0": {"name": "openai", "version": "4.25.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "b40099d625cccb19cbf1cab88915ac1699ece0ed", "tarball": "https://registry.npmjs.org/openai/-/openai-4.25.0.tgz", "fileCount": 505, "integrity": "sha512-qLMFOizjxKuDfQkBrczZPYo6XVL4bdcuz9MR11Q+M91kGcs8dQw+O90nRcC+qWuhaGphQkfXQJMn4cd7Yew3Kg==", "signatures": [{"sig": "MEUCIQCSw9z7Bs34xj+YxgvAlG/3f167nPWhkOVjz3NHARX58AIgSefqd7fqPKFY7i4u5ZqtaTssWqtIIsH1+jLRC4bH2p8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1685094}}, "4.26.0": {"name": "openai", "version": "4.26.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "0363b9d390727705b89cd1b1ea8e38af2afd53b6", "tarball": "https://registry.npmjs.org/openai/-/openai-4.26.0.tgz", "fileCount": 505, "integrity": "sha512-HPC7tgYdeP38F3uHA5WgnoXZyGbAp9jgcIo23p6It+q/07u4C+NZ8xHKlMShsPbDDmFRpPsa3vdbXYpbhJH3eg==", "signatures": [{"sig": "MEUCIQC2h62vQL88aZwJHIFK3/hhokHXqS/OnNXwAkaOxWabxQIgHwJTQGrRdLoWRtgaokcrcTG5PnGlMDOmJnd7C3yGBm0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1691258}}, "4.26.1": {"name": "openai", "version": "4.26.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "7b7c0225c09922445f68f3c4cdbd5775ed31108c", "tarball": "https://registry.npmjs.org/openai/-/openai-4.26.1.tgz", "fileCount": 505, "integrity": "sha512-DvWbjhWbappsFRatOWmu4Dp1/Q4RG9oOz6CfOSjy0/Drb8G+5iAiqWAO4PfpGIkhOOKtvvNfQri2SItl+U7LhQ==", "signatures": [{"sig": "MEUCIQCKKwIAmFyqwq8RFM0wm21TMMKNjoOY3yDieurjYS1zYgIgMPBJcsqgM0FzoKJlpNkq6QoNP0xLXd+bMoCNcwGClIw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1692473}}, "4.27.0": {"name": "openai", "version": "4.27.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "4ada66a19c369b202e8a939b8bd287e4dca036f4", "tarball": "https://registry.npmjs.org/openai/-/openai-4.27.0.tgz", "fileCount": 505, "integrity": "sha512-j1ZEx9NiBpm31rxWqQTjQt1QvH/8001xHsc/pRoPjkRDYWONCb+qkR6L9C7Wl6ar72Mz1ybtn1bv6fqAoTPlKw==", "signatures": [{"sig": "MEYCIQDXANJf2f47Gs0Y5YWnL58uZHBLL76mNebKAZGWfd8ptQIhAPj+kvCvfJ6B2T7uBI8fFB6rJaEf6lL42M8WC6WKnSx1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1694572}}, "4.27.1": {"name": "openai", "version": "4.27.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "62dcb5951ca83ec8dc71aa2bb0a46cfbe3c2e52d", "tarball": "https://registry.npmjs.org/openai/-/openai-4.27.1.tgz", "fileCount": 505, "integrity": "sha512-dwBw0geNdi0sutIVeROsQu0qCdKlpKlyM2zrX5+1R2X9KS3/CfssfiZGjMUhrIU0A+1BwzA1kI1KCkJDQDX5WA==", "signatures": [{"sig": "MEQCIGhemMqWkxyioxPrcYkSz9aKmrkyXEUpSh9OPTLlNmH5AiB9qMnE8A/dLGovHZGyYHobox3QXTJyfdMT+kz6JW8zkw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1694698}}, "4.28.0": {"name": "openai", "version": "4.28.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "ded00e3d98c25758b5406c9675ec27a957e00930", "tarball": "https://registry.npmjs.org/openai/-/openai-4.28.0.tgz", "fileCount": 505, "integrity": "sha512-JM8fhcpmpGN0vrUwGquYIzdcEQHtFuom6sRCbbCM6CfzZXNuRk33G7KfeRAIfnaCxSpzrP5iHtwJzIm6biUZ2Q==", "signatures": [{"sig": "MEYCIQDq/HEqnQG6eOdznZdZxZG2GvrzvV89aZUa3qGjsCqgPQIhALnDmpqgudOY08B/jGjQmviNtqj6BjUFtfcUmGAQyvDh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1695462}}, "4.28.4": {"name": "openai", "version": "4.28.4", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "d4bf1f53a89ef151bf066ef284489e12e7dd1657", "tarball": "https://registry.npmjs.org/openai/-/openai-4.28.4.tgz", "fileCount": 505, "integrity": "sha512-RNIwx4MT/F0zyizGcwS+bXKLzJ8QE9IOyigDG/ttnwB220d58bYjYFp0qjvGwEFBO6+pvFVIDABZPGDl46RFsg==", "signatures": [{"sig": "MEYCIQCqkfUVBc4wRsWrbw1NWoTrh2S+6a/Eq8/1ArbwAs7HAAIhAOfSjzQzE/XfFlfxlDR5MX1nKVRXARhQG2gaaw1f4CBU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1699308}}, "4.28.5": {"name": "openai", "version": "4.28.5", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "67dab8525b18230dc2d8be14767f44262d299dfb", "tarball": "https://registry.npmjs.org/openai/-/openai-4.28.5.tgz", "fileCount": 505, "integrity": "sha512-T1caBTrKWs3CfM6h1YRmsUenrE2ehc6ckfj3TiVLc9U8337U2/KZ0kYqD6T0c4eT3xENL4py8YF7RKT9J6qWRg==", "signatures": [{"sig": "MEYCIQDt8D4SKJwTJrXrj0IBZvM6geq+LpESoodZGQyNiP1MzQIhALR7E2fWZFoPTymZJQbX6LNb13wQu2DW/lD5nLD7vk+t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1716511}}, "4.29.0": {"name": "openai", "version": "4.29.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "4e69e6be5fc623d6783c1758de93126a3b27b315", "tarball": "https://registry.npmjs.org/openai/-/openai-4.29.0.tgz", "fileCount": 519, "integrity": "sha512-ic6C681bSow1XQdKhADthM/OOKqNL05M1gCFLx1mRqLJ+yH49v6qnvaWQ76kwqI/IieCuVTXfRfTk3sz4cB45w==", "signatures": [{"sig": "MEUCIEUlS7DiuMEdb4oSyHTvbvH4FTXaYhIdzMDcMC1/0Rj7AiEAltkT4wHXcdTVrGnunTVDP1cQ9lA1JZVQkE1mxYP3yqU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2012984}}, "4.29.1": {"name": "openai", "version": "4.29.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "89d981f8ced9d1d0db2e09ca1b16b0d7775dcf36", "tarball": "https://registry.npmjs.org/openai/-/openai-4.29.1.tgz", "fileCount": 519, "integrity": "sha512-vvKRIgB4/7w48PGVbeR8OceH/PT6fRo4sTIjRC7+y7WoK7by1R0cXs2SZRx4KsEh0ZB8J0eqdVIdRgs8XzeoEg==", "signatures": [{"sig": "MEQCIFEyedvz5wsrD+D3EbBA5f7NW82iGlrUFyVt8J3hvX/nAiBHJnZKutqcSzecxxwrJOjhWFw/OyEzgJ3ca5CXc7Y/4g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2013322}}, "4.29.2": {"name": "openai", "version": "4.29.2", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "45e83cb49dbd052626637b267c749785a24f411c", "tarball": "https://registry.npmjs.org/openai/-/openai-4.29.2.tgz", "fileCount": 519, "integrity": "sha512-cPkT6zjEcE4qU5OW/SoDDuXEsdOLrXlAORhzmaguj5xZSPlgKvLhi27sFWhLKj07Y6WKNWxcwIbzm512FzTBNQ==", "signatures": [{"sig": "MEQCIHDVYV1g6WvDNXDeNCgWgeMvzDx7vfPPl+mAkN8pjoCxAiBAToZUO2VK7KMzQlK3Fz8Oo0A95F3cRHbm2Oylfz+n3g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2019044}}, "4.30.0": {"name": "openai", "version": "4.30.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "fafa30cc8d2a8570f258f0a8609ec4a3080022fa", "tarball": "https://registry.npmjs.org/openai/-/openai-4.30.0.tgz", "fileCount": 519, "integrity": "sha512-jOaT4u7DpWzvuOuSxw5skuBbSuagw91Vd4IU/zP9qdgu6C19AAq4wzx+24e59wdEmsAG58enOUNf6t5V2WwN9g==", "signatures": [{"sig": "MEYCIQDUr+3RxyWiRo8IrLmhHyuEGHPiI0iULT2DvNynMX+fuAIhAIxl8JwXm7tsmgimHoGfCbWr/jl7kdO240N4sL6I18QP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2028529}}, "4.31.0": {"name": "openai", "version": "4.31.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "digest-fetch": "^1.3.0", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "5d96045c4eb244fa21f0fff0981043a2c9f09e8c", "tarball": "https://registry.npmjs.org/openai/-/openai-4.31.0.tgz", "fileCount": 519, "integrity": "sha512-JebkRnRGEGLnJt3+bJ5B7au8nBeZvJjs9baVxDmUZ5+BgafAdy6KDxJGSuyaw/IA+ErqY3jmOH5cDC2mCDJF2w==", "signatures": [{"sig": "MEUCIQCfJKNa3VwCHfdKllBIJFezsYTdaYFtAqvoo5qGcKXobwIgMJhiHgDysGLXk5ZqNZiG6Mx8jnl1AhN5KKynOd18D0I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2034359}}, "4.32.0": {"name": "openai", "version": "4.32.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "6feaf9fca691979857594517801c4025b4dcb74d", "tarball": "https://registry.npmjs.org/openai/-/openai-4.32.0.tgz", "fileCount": 519, "integrity": "sha512-TXmBSej7RoQ4GWgKw1Waopx6QRroWlwaLH+PkxCTN45nKNEPFcGUV7VSWQCB/DwWMQVRLSNUAH4BqFYnSCEgkQ==", "signatures": [{"sig": "MEUCIQCUTndONxWggXGmH51nY6daTbk9ktFajh9rS4NQaEokVQIgMHhJEZqF+ZH+O5E98Iwpx95ZeFI9SkVVfGzS8T1thI8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2074509}}, "4.32.1": {"name": "openai", "version": "4.32.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "9e375fdbc727330c5ea5d287beb325db3e6f9ad7", "tarball": "https://registry.npmjs.org/openai/-/openai-4.32.1.tgz", "fileCount": 519, "integrity": "sha512-3e9QyCY47tgOkxBe2CSVKlXOE2lLkMa24Y0s3LYZR40yYjiBU9dtVze+C3mu1TwWDGiRX52STpQAEJZvRNuIrA==", "signatures": [{"sig": "MEUCIFuNuKTbZgZ+1w9ryOR334nIZc8YittAX+hs76AUS9zOAiEAp2Ytv09ihQiKzecuwF2t8JPgZ8ZlwxxAFO6ZOTT+u2A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2074840}}, "4.32.2": {"name": "openai", "version": "4.32.2", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "0ac847cd372048d425e70fddd8b63adfbf3e65bf", "tarball": "https://registry.npmjs.org/openai/-/openai-4.32.2.tgz", "fileCount": 519, "integrity": "sha512-J<PERSON><PERSON><PERSON>ttiL4liZJ2GRYvyqd/m9zA64bI8e6B5BtyKlbkcv5DAd5wiDgd0cQ1CIIdsEH90k5p3TdK6MyjnUhvOe7w==", "signatures": [{"sig": "MEUCIGPW9MiqAKNwO5P/fu6FujVQJ6Eadqp0U3zE0KS6UlkJAiEAqJwYix4N6LKTT7qJkyHeHqfsBO1itdrw6+GPxHcqkv0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2084316}}, "4.33.0": {"name": "openai", "version": "4.33.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "8c33da687d4a7f3dd7576179318341615394c79d", "tarball": "https://registry.npmjs.org/openai/-/openai-4.33.0.tgz", "fileCount": 519, "integrity": "sha512-Sh4KvplkvkAREuhb8yZpohqsOo08cBBu6LNWLD8YyMxe8yCxbE+ouJYUs1X2oDPrzQGANj0rFNQYiwW9gWLBOg==", "signatures": [{"sig": "MEQCIGgAyt+lfnqwx2xq6RYVTFC4eICnVvdrCmxQ1JFnu6+zAiBc73/cdc4ZdNfBOhOvoGTv/UfqN+1kEPsGcfshPMGVuw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2097689}}, "4.33.1": {"name": "openai", "version": "4.33.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "7f14c28ea0994ff6eacc2dbca2131c61e7692cd4", "tarball": "https://registry.npmjs.org/openai/-/openai-4.33.1.tgz", "fileCount": 519, "integrity": "sha512-0DH572aSxGTT1JPOXgJQ9mjiuSPg/7scPot8hLc5I1mfQxPxLXTZWJpWerKaIWOuPkR2nrB0SamGDEehH8RuWA==", "signatures": [{"sig": "MEYCIQDC+uu2TqU43Yzb6/e+XVeHTORLuXU/+g8uA/1ls5gEDQIhAIQngPEQWnTA3JmrCQH6AcwJ7bJOl8ZQvvBJ575rtwpy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2098211}}, "4.34.0": {"name": "openai", "version": "4.34.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "02d979e9f09c5ae488a709fb739168fa86daead4", "tarball": "https://registry.npmjs.org/openai/-/openai-4.34.0.tgz", "fileCount": 542, "integrity": "sha512-ma6sJ1lHUaJRPQj9GvfXzLXDL4K9DoUeesgAnw/Lwm9r2jm/xqGGIW6LaIScL4ZLfQyzxHs7aTBZGyiKMDWgIA==", "signatures": [{"sig": "MEYCIQDiiO3HdcVmZvj4JVqLV1/WggW8L80SO25Qwsf2TdDr5QIhAKH9078qCOpMVm4V8kjo732PXyQz3lgIIsdtFRx0qtGe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2213603}}, "4.35.0": {"name": "openai", "version": "4.35.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "3a9fa95e87d18df73ef3ff03ed40bd7b4e7770e9", "tarball": "https://registry.npmjs.org/openai/-/openai-4.35.0.tgz", "fileCount": 542, "integrity": "sha512-K8GY7k4h+dT2GSmn3JGSjkwElpo8cPa4eQ6p++uK1HSrrIBp6NalD3A3DIamvX1AnIyrKqe8M//HoQWgjntUiA==", "signatures": [{"sig": "MEUCIDVlvSRhKyfx8K/d4cYGFHpgoH0Vu0S8XAWcM7oKzkS6AiEAhqFHpqrY3U7+39ezgwOvRfUhxaFB3Z1ab47R/ecj1EM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2214366}}, "4.36.0": {"name": "openai", "version": "4.36.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "a41404df80e60334b0b649c949d05adc63f48fd3", "tarball": "https://registry.npmjs.org/openai/-/openai-4.36.0.tgz", "fileCount": 542, "integrity": "sha512-AtYrhhWY64LhB9P6f3H0nV8nTSaQJ89mWPnfNU5CnYg81zlYaV8nkyO+aTNfprdqP/9xv10woNNUgefXINT4Dg==", "signatures": [{"sig": "MEQCIALG5ghSiCJMLdX13KI9KrHDNUn8BjyC76i9F6a6nC6XAiBsPSdGPfEdDamCI4UOV93tBjhI1SO0DEPYWo7vn4/FsQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2217559}}, "4.37.0": {"name": "openai", "version": "4.37.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "7a43850626da4670e9a6723c5b29f89845c3c514", "tarball": "https://registry.npmjs.org/openai/-/openai-4.37.0.tgz", "fileCount": 547, "integrity": "sha512-dB0XXg6k0TPIC/QgmsDfcbpxxPCNRrG4sB13Ut/6RER0t5eifBRqC/5U6KQS4D6DRxXzQhtiizDxDwy1o67xfQ==", "signatures": [{"sig": "MEYCIQD+cS2Z+b7Juh9cEOL3w653AGb8f1SMi/jt4gc+VraOkwIhAITOsGYKbVsIxDl3zgI6akiulh4VUEuKHqNCcjTfGa54", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2357892}}, "4.37.1": {"name": "openai", "version": "4.37.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "b3d64d61a972d43da2399d86c4b0cc7b07e9c2bc", "tarball": "https://registry.npmjs.org/openai/-/openai-4.37.1.tgz", "fileCount": 547, "integrity": "sha512-Y<PERSON>uhylpDeTNCWgsfhZe38+c4dDWZuW9VgzNY/sdYiNt6K9pvijroyYENp8YGEUHnuIAKtsLneZX9Qb/iB5XHkw==", "signatures": [{"sig": "MEYCIQCKgLEmaR1BJC4RcBaOEFY4AbumpaE+CnrjoxNjCLzerQIhAI5cJHQFc2Bwhq+7tVLodqbaMfGnScxwDxrNLoOh8vS9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2367571}}, "4.38.0": {"name": "openai", "version": "4.38.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "d97accc7c368670a40c2f668650b624cb941dc8b", "tarball": "https://registry.npmjs.org/openai/-/openai-4.38.0.tgz", "fileCount": 547, "integrity": "sha512-q1w04cRm+7CgUAGDXqt+OMa89zXBffHrEK0FcVDRhD+zL1S1aAatu4iYO5sIxR2QFEP//i8CM3QaxGVTNajxuw==", "signatures": [{"sig": "MEYCIQDtPmdU7bh29ip98FOAPWTA2FtfLvlvd5ta2YFvjDpp8QIhANt9F/xHdEhlZgFceKyTOCIL18rl90ZZVrjcfXiJP8LR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2373498}}, "4.38.1": {"name": "openai", "version": "4.38.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "375ba1e2ff9fcd6434114bfeffa7a0ef35d74b78", "tarball": "https://registry.npmjs.org/openai/-/openai-4.38.1.tgz", "fileCount": 547, "integrity": "sha512-nmSKE9O2piuoh9+AgDqwGHojIFSxToQ2jJqwaxjbzz2ebdD5LYY9s+bMe25b18t4QEgvtgW70JfK8BU3xf5dRw==", "signatures": [{"sig": "MEUCIQCO+CRbFH7QO2mpzpzAEEwWTidBqMdoF+BOrfy+CLHldwIge7KTz/lQOL+NFwpaikBCPFRVgckBVG8WQAOKc8jjWjg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2373819}}, "4.38.2": {"name": "openai", "version": "4.38.2", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "4c5335260b819a0f89fd8f922fac6aa942f540e7", "tarball": "https://registry.npmjs.org/openai/-/openai-4.38.2.tgz", "fileCount": 547, "integrity": "sha512-M16ehj0D84Gjq5cjvBzXRb5X+UvtWlxPDRAWAWMC0EN+6nHqnULIn5fWWeiexDPup25FeSZYv/ldp8KefcZVJQ==", "signatures": [{"sig": "MEUCIHjqgyilW4IvttH6yFf/f+G8awAtU6ddjKPAH4AKa+9uAiEAlEAWu0XOrGjq6hG9hymzbw4XobSWp9fdd25LZhkAY0M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2376640}}, "4.38.3": {"name": "openai", "version": "4.38.3", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "0e560541d9c31b0d4faaa6f660ade60dd5ee8052", "tarball": "https://registry.npmjs.org/openai/-/openai-4.38.3.tgz", "fileCount": 547, "integrity": "sha512-mIL9WtrFNOanpx98mJ+X/wkoepcxdqqu0noWFoNQHl/yODQ47YM7NEYda7qp8JfjqpLFVxY9mQhshoS/Fqac0A==", "signatures": [{"sig": "MEQCIGStqt4OTRiHkIJk28Sei8GIkm7z2ZHRyDKYi+mS+68PAiBYL5x9ZEJ3ER/Kvyjl0KUNue5iO2DVA6PK0h3whjNYJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2376820}}, "4.38.4": {"name": "openai", "version": "4.38.4", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "10b008e7f2bcde70857487652130440c0b3828b2", "tarball": "https://registry.npmjs.org/openai/-/openai-4.38.4.tgz", "fileCount": 547, "integrity": "sha512-ef5d2YaXDULXkoK+JB/GqmXgOZF5dsPxYwB27QGh5Cv8ykaYugl6MFyKipiwvlhubmNrMtlnISibx3G6BOpR9g==", "signatures": [{"sig": "MEUCIQD5DSG93o6Z45MYqRpXiz96APgIr+Of+Kl2HG9r5PhI3wIgCk3K112G3McSTGhtM0qI4ZbJrtD7CULnLV5YcUF1E54=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2377917}}, "4.38.5": {"name": "openai", "version": "4.38.5", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "87de78eed9f7e63331fb6b1307d8c9dd986b39d0", "tarball": "https://registry.npmjs.org/openai/-/openai-4.38.5.tgz", "fileCount": 547, "integrity": "sha512-Ym5GJL98ZhLJJ7enBx53jjG3vwN/fsB+Ozh46nnRZZS9W1NiYqbwkJ+sXd3dkCIiWIgcyyOPL2Zr8SQAzbpj3g==", "signatures": [{"sig": "MEUCIQD8MJiB5SVoLkIzwVwqc1waL6PcAydcUCJhZFIwINVeIwIgBUnTKXoz1/24mg1yaeHC/mN5g1HCjhzAKz5utxterYI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2378267}}, "4.39.0": {"name": "openai", "version": "4.39.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "e7cb3a2f0283e4a07afff228860cca8a19f62b0c", "tarball": "https://registry.npmjs.org/openai/-/openai-4.39.0.tgz", "fileCount": 547, "integrity": "sha512-8RcdppE8RKC17VnacZxru1OQL76rhI1107i32nE2RsSsvrd2yyDLAKp//2zzJxLY3iz8Sw1ywcwF5kEcreim6Q==", "signatures": [{"sig": "MEYCIQC2HuF8UumEWsC5ykUTR1yrAL5XMjrCt0IznGUO4uuAsQIhAOcXxvabKJznGHA0G8vDPk6HYd/4OEhlKngHZ3XnUPTR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2380897}}, "4.39.1": {"name": "openai", "version": "4.39.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "619687717e42094927a1977362dd03261a4c4ff3", "tarball": "https://registry.npmjs.org/openai/-/openai-4.39.1.tgz", "fileCount": 547, "integrity": "sha512-Qe3HXHrCMjwAHXWPywUwzI4PHf9r14uyiRRuMmiJjsfv1tREYMHtgIHXNlb54ZxtYJaIj19RHYMq6UQ2hgrIJQ==", "signatures": [{"sig": "MEUCIGdF1shymZfrLZQpPXYqT/4zg35Z/gzoQ5tKvzh8xuOwAiEAgoe00s22btUHdVLRafaj29CqbqKzHZERMjuRvAMISyc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2381625}}, "4.40.0": {"name": "openai", "version": "4.40.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "6cf11702ca009ead10b61061d9e0a1635c6ac307", "tarball": "https://registry.npmjs.org/openai/-/openai-4.40.0.tgz", "fileCount": 547, "integrity": "sha512-ofh9qMxRPDSZTWYvifScusMfnyIwEQL3w+fv3ucQGn3cIn0W6Zw4vXSUod8DwYfcX/hkAx9/ZvWrdkFYnVXlmQ==", "signatures": [{"sig": "MEQCICN6kiAGq1KBFIJ/tXFfcucJHN04ezlG5V8X1l38ACl6AiB4kH0fRPG9M91yGbbQuS2TzNDgFjqI7ro1LIQWVm9hHQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2384157}}, "4.40.1": {"name": "openai", "version": "4.40.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "095a8b7c2af7b05617ab83c825aaa944195d7e7d", "tarball": "https://registry.npmjs.org/openai/-/openai-4.40.1.tgz", "fileCount": 554, "integrity": "sha512-mS7LerF4fY1/we0aKGGwIWtosTJFLKuNbBWMBR/G1TAZUHoktAdod0dqIrlQvSD39uS6jNEEbT7jRsXmzfEPBw==", "signatures": [{"sig": "MEUCIE+ncxrTFLY2BQvd4m4o/JQ15/j7hN9hGlf3yfS3q44FAiEA3I5vH85GkJ5ZGFh37fMd1XYfX9OgcvSFIkonwkiFHXk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2377525}}, "4.40.2": {"name": "openai", "version": "4.40.2", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "5ed3f9adbed46060c6f091aa344bef4acf988f42", "tarball": "https://registry.npmjs.org/openai/-/openai-4.40.2.tgz", "fileCount": 547, "integrity": "sha512-r9AIaYQNHw8HGJpnny6Rcu/0moGUVqvpv0wTJfP0hKlk8ja5DVUMUCdPWEVfg7lxQMC+wIh+Qjp3onDIhVBemA==", "signatures": [{"sig": "MEUCIHmwUiakpup34Aq8Rl4iBWGNYoAmEI0Fz2VKfgQVxrmsAiEAsnZK/Q83MdYCEMHFQ4dvmq8PCdT30I6RtEQPQi9IEyg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2385057}}, "4.41.0": {"name": "openai", "version": "4.41.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "85f9880fdca6a4d761d948f84df3f4f3558ff079", "tarball": "https://registry.npmjs.org/openai/-/openai-4.41.0.tgz", "fileCount": 547, "integrity": "sha512-fiV+RvUGRW+PXxycqeDYuOwsL3TxNqT/LcM6vlqyLz9ACmfSUGg1qviQrHuuNKL7gFOvfzgEJRVVFdqmv/sjxg==", "signatures": [{"sig": "MEQCICHylWoG7BJY02nkWdvtQQKTSzrCKQjXbHdDo2aF+1ICAiAicF3A0tYFjZ40yfgk98u3b0Z418yGbZTjFgoyhC0D8w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2417283}}, "4.41.1": {"name": "openai", "version": "4.41.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "61d22d3f9338841be9f1ab3242418193e4df620f", "tarball": "https://registry.npmjs.org/openai/-/openai-4.41.1.tgz", "fileCount": 547, "integrity": "sha512-QY8pkCOTjwXWS1AsTxRLomkX6B9fBQvggqRu4uvFd7Xjg1NDcPChGyki+sFiYzstpUeoApim/DNGy7fBKwdokg==", "signatures": [{"sig": "MEQCIBmd1jSaLgt6ZqNRt/5YCC9oPFqmMsaMYCWEIfDh5ySyAiA+Z9jmEh+bPYDOn2MOmJAymMquni0BYDd6e7H6p9214A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2419161}}, "4.42.0": {"name": "openai", "version": "4.42.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "8e6517839dcfbde848b981965989ccd2a4d70b1c", "tarball": "https://registry.npmjs.org/openai/-/openai-4.42.0.tgz", "fileCount": 547, "integrity": "sha512-xbiQQ2YNqdkE6cHqeWKa7lsAvdYfgp84XiNFOVkAMa6+9KpmOL4hCWCRR6e6I/clpaens/T9XeLVtyC5StXoRw==", "signatures": [{"sig": "MEQCIA00NETFiQ/VpLkc19DuysOtMK6o5ZeJm4iR8It05PpPAiAjUZc0XLHCbjTXblXWFeYHeaee4+a25VoaBlgVjf2RgA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2423512}}, "4.43.0": {"name": "openai", "version": "4.43.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "df450acca72e44f49eec891d4a95868ec3cb8b70", "tarball": "https://registry.npmjs.org/openai/-/openai-4.43.0.tgz", "fileCount": 547, "integrity": "sha512-4SMUB/XiqnO5IrEcdzEGGTcHoeXq7D/k82v36zoqSitrMUjenZXGH5JysIH7aF7Wr+gjvq0dT2mV6wLVKA7Seg==", "signatures": [{"sig": "MEUCIEr/w+uoBxRi24jyrk7DQCQ8UOy4yz6DVdcMkH+18UsUAiEArIm4etF+KEZmzPaxR4hqTkgwpETeJU1jpYIFTa5tCzo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2423969}}, "4.44.0": {"name": "openai", "version": "4.44.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "7234d98eb459ea913b93e46263a932b233838219", "tarball": "https://registry.npmjs.org/openai/-/openai-4.44.0.tgz", "fileCount": 547, "integrity": "sha512-jVpDIJsBAR83rVbIHPuWRr9UkFc5DaH9ev2kt2IQAhKCs73DBRoFOa5SwtqfN7/CcBdIGBdygpmpc0gsFaV+Ow==", "signatures": [{"sig": "MEQCIHORIwnbEGOMjblY2bmhGZzofbYh1pOxfcPPOTpnjv6HAiBGVmuWNhRrgizGf5XS+GJkoxus3etZvpYKrPVSmUr4hA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2433743}}, "4.45.0": {"name": "openai", "version": "4.45.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "cfec91b228b94c29b833666972692a44d2f64b79", "tarball": "https://registry.npmjs.org/openai/-/openai-4.45.0.tgz", "fileCount": 547, "integrity": "sha512-uszUQrl9eQPCA9a7Zml+Eizb3mG0JDd8zUl528OM6Ccn039dqbOmUivL5s8zUM6iJMRMvNGRMXS9yuuR1Bv2sw==", "signatures": [{"sig": "MEYCIQCp5XVrLKPS1zFFrqQgOQPHrKv3R+4+Ac/tx0Q0boRlpwIhAL3N2as/5lDZwAx6sdiNpIVKRP67/35HLyT1SeHozZcO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2434338}}, "4.46.0": {"name": "openai", "version": "4.46.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "60087751627ed7c40e33f46c0762f214b83d9884", "tarball": "https://registry.npmjs.org/openai/-/openai-4.46.0.tgz", "fileCount": 547, "integrity": "sha512-l0Betzsx3WIjdagqQiH14hWmwYouUzUCcB1ENvKyfG5XOqh6YC2XT7OukzEBTnweutMC91pW2ToddWn8uyD4SA==", "signatures": [{"sig": "MEUCIQDV/HaGFegXVVwNoi+lyiqoDUq7TE3iXr17vAU5hb3C4wIgC2K1sGe7m+1oFc5UEMVKxqYmRQCB9tMp4LW8NE31V0o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2435534}}, "4.46.1": {"name": "openai", "version": "4.46.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "3394fc8e80455ee6cb1b72dbcdb1ddb7c09b75c7", "tarball": "https://registry.npmjs.org/openai/-/openai-4.46.1.tgz", "fileCount": 547, "integrity": "sha512-YILOwSwyg6h4FqDCehzZ/vu0hI6eQWXv7BQ/nNNyRHbprerZmkiRYBzq2h4iiJKUOrIw66APE46ZW/iP8y8Fiw==", "signatures": [{"sig": "MEUCIFcDhsRNpPBvK7dnWmD+LwvieCK5sn7wwUEJn1o9eAmoAiEAqJJqJx1RxOrgFTRLu6x0jQrVhBA1dB5LtIr5t9fq97o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2433055}}, "4.47.0": {"name": "openai", "version": "4.47.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "0cf42eaad7b3f9f33d3dda40b707033b9755b433", "tarball": "https://registry.npmjs.org/openai/-/openai-4.47.0.tgz", "fileCount": 547, "integrity": "sha512-C2/gfAL3omKtOJJ56Gb2PM0yYMIz63/A9FHEL4X/hMJlw8xkZHFVXXz6cOjP8U+tHRCNDYZV7s2BAdnku5hVCw==", "signatures": [{"sig": "MEYCIQC/+g/XTsI4Ee5nphD+nm77Hwn8BCf2kfUEyUpLg7anZAIhAJ8kd9f8FgmsCMSmHfBFvFwBvP7t+/aQiTArrEwh+JUe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2436239}}, "4.47.1": {"name": "openai", "version": "4.47.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "1d23c7a8eb3d7bcdc69709cd905f4c9af0181dba", "tarball": "https://registry.npmjs.org/openai/-/openai-4.47.1.tgz", "fileCount": 547, "integrity": "sha512-WWSxhC/69ZhYWxH/OBsLEirIjUcfpQ5+ihkXKp06hmeYXgBBIUCa9IptMzYx6NdkiOCsSGYCnTIsxaic3AjRCQ==", "signatures": [{"sig": "MEQCIFLX76JmrA4fmsG1jyZYgreTedkK6DXMEWDLm+OlMdphAiAfrqblhvrD4e3LelOakxqI1QM40X8tQPFPejPfknkFTg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2436591}}, "4.47.2": {"name": "openai", "version": "4.47.2", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "075a0f9fa8e96422804ddd017428ce0a3862e1d0", "tarball": "https://registry.npmjs.org/openai/-/openai-4.47.2.tgz", "fileCount": 547, "integrity": "sha512-E3Wq9mYdDSLajmcJm9RO/lCegTKrQ7ilAkMbhob4UgGhTjHwIHI+mXNDNPl5+sGIUp2iVUkpoi772FjYa7JlqA==", "signatures": [{"sig": "MEQCIBSwFEtJGU5l9D0gQ7l2mM8SGPBuH0grbu6q1GMoRrDkAiBydnUcBpjLRo33OTN6OhVRKa3PMWzYWR4mJUjcCJ6VHA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2437001}}, "4.47.3": {"name": "openai", "version": "4.47.3", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "5d01f4a5a06fc390ba67477848278ffc9b3b2b2c", "tarball": "https://registry.npmjs.org/openai/-/openai-4.47.3.tgz", "fileCount": 547, "integrity": "sha512-470d4ibH5kizXflCzgur22GpM4nOjrg7WQ9jTOa3dNKEn248oBy4+pjOyfcFR4V4YUn/YlDNjp6h83PbviCCKQ==", "signatures": [{"sig": "MEUCIHG2OpKD6HFHk8lTrfJRV86TLfGEAzZMgl+mZ2QZQ7/FAiEAycRy6zKHvl3RI51HR2027z5DODOMvvrnqECtLZ3ca2c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2437836}}, "4.48.1": {"name": "openai", "version": "4.48.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "689f7f5daad3c811344a2f4731297c7558ed6849", "tarball": "https://registry.npmjs.org/openai/-/openai-4.48.1.tgz", "fileCount": 547, "integrity": "sha512-nlEzUAzDG1GsTlBVAFFtB0WZB8BFY+XU7o4oslzC7YMZ9PlgDixnbM49hXRWzv5OztevSn64hVKqptvzHq5/6Q==", "signatures": [{"sig": "MEUCIFMXjgv8rgIAG3GYIXnXNHwr/RyPJerVXungbAjMFCtvAiEA2fnO/fOgQfFcOYuzPL1ydUnBPMCg6egEnYA5EpG+LcM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2467500}}, "4.48.2": {"name": "openai", "version": "4.48.2", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "f06cba9c3d90628d625f04a70901f2d5c4dd974c", "tarball": "https://registry.npmjs.org/openai/-/openai-4.48.2.tgz", "fileCount": 547, "integrity": "sha512-3z4JEipIzwFtLvDC3KigZEWH4ALWDHPr6XCq/xYi8O1AQ5lAtu6Ib6FO8KeQ8RdjwfIqHWOWk/zopW5g780s/w==", "signatures": [{"sig": "MEUCIQCkavN41O37avTVDo7szKKaZACDd6zVWHD6pipmAafVjQIgesu6FN/A8Anc2ph4uRj69OX4hSTWZL54WUVluyip/uQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2467835}}, "4.48.3": {"name": "openai", "version": "4.48.3", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "3390e137035b5e6d6f97ff517273c71686e0d574", "tarball": "https://registry.npmjs.org/openai/-/openai-4.48.3.tgz", "fileCount": 547, "integrity": "sha512-EmyvU39gEUWlSMVaL5l351DIEq2iMmtyQHpJHOdJnPIULiIbRtFlgyZh8w+svs5Dh/Z2J5m0Svzpk174YWUHpQ==", "signatures": [{"sig": "MEYCIQDwtKz6HALArUF0SB3iQfqW2KnhYsAhbqlGBmp1+9pk4QIhAPkUNZBa/n+Rm/OGjxiSP31iTiX3aWHAhbhdi1Qjo7wG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2468172}}, "4.49.0": {"name": "openai", "version": "4.49.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "d1c0c6c78625862a67170ca864aa2f5d655dfc6e", "tarball": "https://registry.npmjs.org/openai/-/openai-4.49.0.tgz", "fileCount": 547, "integrity": "sha512-/UkrBSej5ejZ4vnOFoeFefX7erjp4k3+xoLKkswjLEvgBU9QtCnOUgsfpvHkzTzgDXpqPMCzyQHQXWsgiq2xPw==", "signatures": [{"sig": "MEYCIQCkUkaq2ecDaew4lHCltJEGplazCs+m+WoqwDBbGmcXhQIhAPZzQnZVAcXhduXbcog+LoB/NpO8pu4rcHQQjW9mGvUX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2470167}}, "4.49.1": {"name": "openai", "version": "4.49.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "81fc7d3bc97a1905a1a340065158275b5a893765", "tarball": "https://registry.npmjs.org/openai/-/openai-4.49.1.tgz", "fileCount": 547, "integrity": "sha512-bsFSNhhTNon+g6r4UYPKGLi+PlfP1G9TJGSkZS5nZx+PTwW2YUTlfxXxpOKrPab5auIXJdlYpC/g/wkHGR1xug==", "signatures": [{"sig": "MEUCIEwSDHUuz0WxlO8paKq2cY+mtWcsvkU/7GXdM1RZYpvrAiEA2FIriDdJninAZb3pSRMaX7hvMrWft15LJN1OD/lJOsY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2473267}}, "4.50.0": {"name": "openai", "version": "4.50.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "afb4a0c6269f15cb09f0c39b825757b5d89d862c", "tarball": "https://registry.npmjs.org/openai/-/openai-4.50.0.tgz", "fileCount": 547, "integrity": "sha512-2ADkNIU6Q589oYHr5pn9k7SbUcrBTK9X0rIXrYqwMVSoqOj1yK9/1OO0ExaWsqOOpD7o58UmRjeKlx9gKAcuKQ==", "signatures": [{"sig": "MEQCIGYw78E9s2+md/ufH60MgY+pykGmWDMAROlqVA9+7WN/AiA9edEt8M2CvQB9iAqOzP3aY7DRdNBgtKBW9ONwd9UbbQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2477601}}, "4.51.0": {"name": "openai", "version": "4.51.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "8ab08bba2441375e8e4ce6161f9ac987d2b2c157", "tarball": "https://registry.npmjs.org/openai/-/openai-4.51.0.tgz", "fileCount": 547, "integrity": "sha512-UKuWc3/qQyklqhHM8CbdXCv0Z0obap6T0ECdcO5oATQxAbKE5Ky3YCXFQY207z+eGG6ez4U9wvAcuMygxhmStg==", "signatures": [{"sig": "MEUCICKNLYRGuN4pzkN2k8MwQmEPXr1vQ3wkFLPlP1xgPtk1AiEA2/+X/zjaFPxCB8/WLJwiyh3B5Q0wZYgpw0p2zZJnFQM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2477718}}, "4.52.0": {"name": "openai", "version": "4.52.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "5f93bdbef05ca3407d92f7a68717234ac0ffd09e", "tarball": "https://registry.npmjs.org/openai/-/openai-4.52.0.tgz", "fileCount": 547, "integrity": "sha512-xmiNcdA9QJ5wffHpZDpIsge6AsPTETJ6h5iqDNuFQ7qGSNtonHn8Qe0VHy4UwLE8rBWiSqh4j+iSvuYZSeKkPg==", "signatures": [{"sig": "MEQCIEhFAC0ZKP5OGhjLL2ZEeTmF4poR63Svy143yxaj0ReeAiBiO7jH5XEojb3BTpx9r2bdFPpotMSTP4sVRJZ0TXTB+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2480136}}, "4.52.1": {"name": "openai", "version": "4.52.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "44acc362a844fa2927b0cfa1fb70fb51e388af65", "tarball": "https://registry.npmjs.org/openai/-/openai-4.52.1.tgz", "fileCount": 547, "integrity": "sha512-kv2hevAWZZ3I/vd2t8znGO2rd8wkowncsfcYpo8i+wU9ML+JEcdqiViANXXjWWGjIhajFNixE6gOY1fEgqILAg==", "signatures": [{"sig": "MEYCIQCFhF2rMD6k0ovgJSvU80zk7TO59UB0wQfSRYKmVHMwvQIhAL9inhiUHRyc/PB4pPwXVec0p8m5SAdtNAnpq4taNaMA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2483496}}, "4.52.2": {"name": "openai", "version": "4.52.2", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "5d67271f3df84c0b54676b08990eaa9402151759", "tarball": "https://registry.npmjs.org/openai/-/openai-4.52.2.tgz", "fileCount": 547, "integrity": "sha512-mMc0XgFuVSkcm0lRIi8zaw++otC82ZlfkCur1qguXYWPETr/+ZwL9A/vvp3YahX+shpaT6j03dwsmUyLAfmEfg==", "signatures": [{"sig": "MEQCICBZge6JRhGlD8f+DuoGWAZxHrTBfGg8h34uNQxpo3+VAiAX1/UF5cUtJW5MN6f3Ky/IZMmLBkY4JnNOQzBC1brJDA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2483822}}, "4.52.3": {"name": "openai", "version": "4.52.3", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "3c6459dfd65e2bf9671729f25879a5b4224dca70", "tarball": "https://registry.npmjs.org/openai/-/openai-4.52.3.tgz", "fileCount": 547, "integrity": "sha512-IyQLYKGYoEEkUCEm2frPzwHDJ3Ym663KtivnY6pWCzuoi6/HgSIMMxpcuTRS81GH6tiULPYGmTxIvzXdmPIWOw==", "signatures": [{"sig": "MEUCIQDMUagKpbpVrJ9W4jRNbhOur1iQ9bH+VPrd7gujuA/BRwIgGp2LuAuJmncLWqQhdPikoIJy8ziAVFJBH2jaV+cqJHY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2484143}}, "4.52.4": {"name": "openai", "version": "4.52.4", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "03c573dec87920905ce384d3967a810ed39ee724", "tarball": "https://registry.npmjs.org/openai/-/openai-4.52.4.tgz", "fileCount": 547, "integrity": "sha512-3CkV7e8epJBnTe5ptn4i3ivfm1d8cvkbvBOzhGmGYEarpDpcCgwOMV1aBPvZ/HoveUtREWUE9Fqcy7BcPNtMJg==", "signatures": [{"sig": "MEUCIQCk5V1IjrgeFJJzbDSuZz2k3cOvhe9juC1KuIBuGa7dcAIgG0SPlfoffeOTaDBf9Bqi74lNZEpCk+7EGfitITCC+cg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2484509}}, "4.52.5": {"name": "openai", "version": "4.52.5", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "d202c712a63b115e445a3e98a539fa81002cfbb3", "tarball": "https://registry.npmjs.org/openai/-/openai-4.52.5.tgz", "fileCount": 547, "integrity": "sha512-qqH8GsyPE3z06took/2uWOGqRcrZNlRoPAsihpg4jsl0+2Dfelnw6HDDMep0EI2Cfzw75nn3vHRZehep/IZzxg==", "signatures": [{"sig": "MEUCIDog/dwym8alIopCG31nA+3Wzv+Cd8VqVbzGRdsnwEePAiEA7yycEdJxzqc+CHYO5ubnVlsZyyxY3Co6rBOR5DZTpFE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2485153}}, "4.52.6": {"name": "openai", "version": "4.52.6", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "738d8a9c90670a247b61059e144a612688c14c70", "tarball": "https://registry.npmjs.org/openai/-/openai-4.52.6.tgz", "fileCount": 547, "integrity": "sha512-mT4SblnPXkzgiGY/cByU57sDDCqNUt3GQV8mzt4rL/xP6PHIQyTqwJ/WxwGhHRQ9okxgsDNgKQ6asdq8Dynw+g==", "signatures": [{"sig": "MEYCIQCqOu+84uTje7wuDXzBsVjSs7vxvY/Jfxdsr4GrLbQ7pgIhAMom4MI6hQbZRdV1NsYCmj+yAJTo2MJudnT9ZeciV++M", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2485504}}, "4.52.7": {"name": "openai", "version": "4.52.7", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "e32b000142287a9e8eda8512ba28df33d11ec1f1", "tarball": "https://registry.npmjs.org/openai/-/openai-4.52.7.tgz", "fileCount": 547, "integrity": "sha512-dgxA6UZHary6NXUHEDj5TWt8ogv0+ibH+b4pT5RrWMjiRZVylNwLcw/2ubDrX5n0oUmHX/ZgudMJeemxzOvz7A==", "signatures": [{"sig": "MEQCIAyxNtVFEv2p1D7q/l/+Tv3OAr/ZFzY+kQGnpcyDrP7cAiAYQJwrNgrDIk3FOY3g/aRQWFSJq1AgTxQRJWoAD73aog==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2485846}}, "4.53.0": {"name": "openai", "version": "4.53.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2", "web-streams-polyfill": "^3.2.1"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "5ac6fc2ba1bba239a31c910bd57d793814bea61d", "tarball": "https://registry.npmjs.org/openai/-/openai-4.53.0.tgz", "fileCount": 570, "integrity": "sha512-XoMaJsSLuedW5eoMEMmZbdNoXgML3ujcU5KfwRnC6rnbmZkHE2Q4J/SArwhqCxQRqJwHnQUj1LpiROmKPExZJA==", "signatures": [{"sig": "MEUCIQDZvlanFb1ivRxXXN5UMxdJFliUE8VEUyGGIkQ2Dat6rAIgVGmuOflAAJyM4Xe+SiLXy3oArN8IHBJpNf9X4pqgaHk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2521427}}, "4.53.1": {"name": "openai", "version": "4.53.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "5ea38c175a70b685d329fafd6669dc4e789592b2", "tarball": "https://registry.npmjs.org/openai/-/openai-4.53.1.tgz", "fileCount": 570, "integrity": "sha512-BFj9e0jfzqd2GAGRY9hj6PU7VrGyl3LPhUdji7QvZCVxlqusoLR5qBzH5wjrJZ4d1BBDic/t5yvTdk023fM7+w==", "signatures": [{"sig": "MEUCIQC8gI3y0j5I70F0jvXweuv7HS63URCJiHXNltn81E8awQIgaVMVRzUVnaIampClWjiwBtSY8ZoFCu7zhuOS5VMEusY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2521678}}, "4.53.2": {"name": "openai", "version": "4.53.2", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "86f54a38091a87db36f651cf28c9e5ee7c98d56a", "tarball": "https://registry.npmjs.org/openai/-/openai-4.53.2.tgz", "fileCount": 570, "integrity": "sha512-ohYEv6OV3jsFGqNrgolDDWN6Ssx1nFg6JDJQuaBFo4SL2i+MBoOQ16n2Pq1iBF5lH1PKnfCIOfqAGkmzPvdB9g==", "signatures": [{"sig": "MEQCIHvz1Pr15/R6FekdF6EYFCAdeJI3CTgBS/l1yHqI52N/AiBsF0ex3ad5lT6HF6h9Zg6GxVAt95rn/l1UlZ5wDp8RMw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2522018}}, "4.54.0": {"name": "openai", "version": "4.54.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "eeb209c6892b997e524181b6ddb7e27bf4d09389", "tarball": "https://registry.npmjs.org/openai/-/openai-4.54.0.tgz", "fileCount": 570, "integrity": "sha512-e/12BdtTtj+tXs7iHm+Dm7H7WjEWnw7O52B2wSfCQ6lD5F6cvjzo7cANXy5TJ1Q3/qc8YRPT5wBTTFtP5sBp1g==", "signatures": [{"sig": "MEUCIEGq/THsKzbOoPiMdoAhWAwQKVgw0rHNjc5/lK+WAzhhAiEA8oCeE8XU1bCDT0At55SR5gjg9+Ow8/BfkKF+Nno8xxU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2466145}}, "4.55.0": {"name": "openai", "version": "4.55.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "d28b5a0b97214de4a2f018efd4665468e6c1fd94", "tarball": "https://registry.npmjs.org/openai/-/openai-4.55.0.tgz", "fileCount": 847, "integrity": "sha512-BR3TUybzdqNeBMgEFvxgBrFks9FY2NoP2jyTf7LT4UxPv8chevRKSxKezsINVSeQ/QLA12CALR1oco6KVdVpVA==", "signatures": [{"sig": "MEUCIQD6B7wAdnWVO7lFlfqoXZrRjDGMLbj3/3V47r2s3mbtUgIgCzVvJkH2D6eQkfIi3jrlzcWB1wQlkfgV2dXhiQotHHE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2519097}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.55.1": {"name": "openai", "version": "4.55.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "59fe4565ab117cf869137588f6da15ef330e405f", "tarball": "https://registry.npmjs.org/openai/-/openai-4.55.1.tgz", "fileCount": 847, "integrity": "sha512-FziYJcWl+SAGbt5AcRIzVzNcnKohpEMQdtzVOmHFbBp/if7x2+ACqgxF2XUbyi2PcKONPcVpmtG5h9qoDAEXwQ==", "signatures": [{"sig": "MEUCIESt4yWvyZCO2Y0fzQiu5WX/o2nIwe0rJ7hT64HS60NjAiEAk6DVCQ1U2DLs7XDUOlVqQpwHRMXeFbSjg7ddYsCe+RQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2524226}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.55.2": {"name": "openai", "version": "4.55.2", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "5dae9ff3416e338303391cec665ac338fb30c0a2", "tarball": "https://registry.npmjs.org/openai/-/openai-4.55.2.tgz", "fileCount": 855, "integrity": "sha512-Z81nbAPvlLl5eDLNldkixAayhUzbbCbOef/dqqW3uuN0Fkgj5GZn2kgUDTqGPOp2RHtcOYMfZltXNPoC6kEkTQ==", "signatures": [{"sig": "MEUCIQC5/DST0RfWixohSY3Achmiuv/WbM86i6PM8Z8uYilhyAIgBPee/B2HfPmMh2cQgNzSplmixcpHEzS1+eyFtzTwQmE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2532611}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.55.3": {"name": "openai", "version": "4.55.3", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "9b29c6173078465b85e581157502ff7bec1b50da", "tarball": "https://registry.npmjs.org/openai/-/openai-4.55.3.tgz", "fileCount": 855, "integrity": "sha512-/IUDdK5w3aB1Kd88Ml7w5F+EkVM5aXlrY+lSpWXdIPL18CmGkC7lV9HFJ7beR0OUSFLFT0qmWvMynqtbMF06/Q==", "signatures": [{"sig": "MEUCIBULoGXu7bpgMb/gZMFpE68qiUt320Rla1K4/x1oon9lAiEAvMgV1rY60EkkZK/THpXVaegc30ccXJM/bC7BX8K1RRo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2533505}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.55.4": {"name": "openai", "version": "4.55.4", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "2f188e718e668f478f40c03012d92394e0c3d1b1", "tarball": "https://registry.npmjs.org/openai/-/openai-4.55.4.tgz", "fileCount": 855, "integrity": "sha512-TEC75Y6U/OKIJp9fHao3zkTYfKLYGqXdD2TI+xN2Zd5W8KNKvv6E4/OBTOW7jg7fySfrBrhy5fYzBbyBcdHEtQ==", "signatures": [{"sig": "MEUCIQCAZLWqwlmQsoJhwHfGUI8y1qvIPcTE01mXcyxoXq932gIgVENWR6G7bdcjpNrz1kmnhGbxvkbCtwtYtgUmNa7q/AI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2534469}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.55.5": {"name": "openai", "version": "4.55.5", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "dccd7fd42ee76a4135498494d5161dc9f9a44aa4", "tarball": "https://registry.npmjs.org/openai/-/openai-4.55.5.tgz", "fileCount": 855, "integrity": "sha512-9OkMAMljFv1LxUFf5HLm/pw7zFd4yMgW+lKOYF80RBwuGWU+ZKF5BQGll+TEGAHu23YMeT8t6VSxI27c/DRAOA==", "signatures": [{"sig": "MEQCIEs8A8vQ0feh8hNNKCcAm1LllhFVH77YbqSDHYeddG1xAiBstqWr854hTg62ujFlh8p2cteonxEdRMHz7G4vazQfLw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2534984}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.55.6": {"name": "openai", "version": "4.55.6", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "88fc3b462e71a750431a9faa5228dc0bdf29847c", "tarball": "https://registry.npmjs.org/openai/-/openai-4.55.6.tgz", "fileCount": 856, "integrity": "sha512-CXLciLu0Hihv7bVATzbGTfOTPXe9CFszT2YVJfX//Czkb5+IAOALtORL/r7RkYb9OJhN+LSaittfHW+ZodRinQ==", "signatures": [{"sig": "MEUCIQDDgMlxOUrKb8F4bLYlFijyMBN6xR5oarcov3S1jsuvxAIgJTcZOuz8omX11CMIbcrUQAjb6Qt+83eWcGBdnJECZ0o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2536074}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.55.7": {"name": "openai", "version": "4.55.7", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "2bba4ae9224ad205c0d087d1412fe95421397dff", "tarball": "https://registry.npmjs.org/openai/-/openai-4.55.7.tgz", "fileCount": 856, "integrity": "sha512-I2dpHTINt0Zk+Wlns6KzkKu77MmNW3VfIIQf5qYziEUI6t7WciG1zTobfKqdPzBmZi3TTM+3DtjPumxQdcvzwA==", "signatures": [{"sig": "MEYCIQDtDZzAUuExpIwD/MUjhk3ZLwTkzkv2u5PMkS82Gd5B8gIhAMeuGN7u5OEwHzlQ0AeEMP47BIzFQCOW348EcXCImGG/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2539807}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.55.8": {"name": "openai", "version": "4.55.8", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "609ed386f169e2cb380d50a6d9423a8239e316cd", "tarball": "https://registry.npmjs.org/openai/-/openai-4.55.8.tgz", "fileCount": 856, "integrity": "sha512-k40u3QCrP10bL6c5b6b+lffJzG4QBSAqr6QDxXPThTuWaQmlKztaeW+NmSkJTsB7Ho1Cwq+IkdZZs1ZIlScXMA==", "signatures": [{"sig": "MEUCIQDJmFohoBOgwgMWXNaWMpNhewGYg7pqAr2isoOJa+7o2QIgDZvmYJEz2X0+ghJ6FaCI0nNjauNS/JKCf8B6Y88/TVA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2541493}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.55.9": {"name": "openai", "version": "4.55.9", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "ed11cbaacdc96a645e7740e0bdb8a1a28f8f6887", "tarball": "https://registry.npmjs.org/openai/-/openai-4.55.9.tgz", "fileCount": 856, "integrity": "sha512-gO2BVMuGirwoftSXw6OnC6YvLK+1ZuUrAlDfWAyDbWUuXsJ+RGDAjA7ZCTocgAwSzDZNUYUr3SJ7siFaF5zquQ==", "signatures": [{"sig": "MEUCIC2ivdo2qvGB5RL7s3gtaFGLso3sZ9cWmpB2CQTqigWtAiEA7nDreD8olmGK2W4yioD4CAM+8uc81b1mbNFdlPljOdY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2541626}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.56.0": {"name": "openai", "version": "4.56.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "07d3982544cabd5781127288a8dfcceb7319a4cf", "tarball": "https://registry.npmjs.org/openai/-/openai-4.56.0.tgz", "fileCount": 856, "integrity": "sha512-zcag97+3bG890MNNa0DQD9dGmmTWL8unJdNkulZzWRXrl+QeD+YkBI4H58rJcwErxqGK6a0jVPZ4ReJjhDGcmw==", "signatures": [{"sig": "MEQCIARbBlRGdlxw4SlxuEYMUhyr6YdugeLQH/RNoyJv7Dp7AiA1VO+tkzbk4RTuEN4WoWGaH9HYVXL0keb//rF7dZ0YtQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2541929}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.56.1": {"name": "openai", "version": "4.56.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "fc2160964d4c88a45b8f14dd6003ab7b09398eb3", "tarball": "https://registry.npmjs.org/openai/-/openai-4.56.1.tgz", "fileCount": 856, "integrity": "sha512-XMsxdjrWBYgbP6EsDIwbhkQEgeyL2C41te/QrJm8kdfho22exhTUJ/cFJSmCTToam/RSOC1BlOylHvD6i/bmsA==", "signatures": [{"sig": "MEYCIQCr/gtRL9Y5wlm++W8xzg7hD46UKiV1fIGk5JK3TWX9ywIhALfEeQocCriXimLWYrHDy+veMAimuqEZvemsYN20+PBv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2543029}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.56.2": {"name": "openai", "version": "4.56.2", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "51e3caab4b368eba4050f37597b0f111e24b815f", "tarball": "https://registry.npmjs.org/openai/-/openai-4.56.2.tgz", "fileCount": 856, "integrity": "sha512-5VIG9ktyKm3WhJ24Qmp362DYQswZl7PLRF5CeJS95vCWp1Lm78AdlKUKOajCqNtAhXP+2XPRGmbcF4XAslWXDg==", "signatures": [{"sig": "MEUCIQC/n5kC63BwmsnavDkvrFNSALDpw9u68/WbNKY/tLgC2QIgSkaW6l1htSU/tdx3JSp/EyBAVi6rkwe5ogbqUtpj4yU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2543393}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.57.0": {"name": "openai", "version": "4.57.0", "dependencies": {"qs": "^6.10.3", "@types/qs": "^6.9.7", "node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "c1c735e9bbccf1fc42fdcfc9676643cad56258fe", "tarball": "https://registry.npmjs.org/openai/-/openai-4.57.0.tgz", "fileCount": 856, "integrity": "sha512-JnwBSIYqiZ3jYjB5f2in8hQ0PRA092c6m+/6dYB0MzK0BEbn+0dioxZsPLBm5idJbg9xzLNOiGVm2OSuhZ+BdQ==", "signatures": [{"sig": "MEQCIHomKf2Ew3E5tZUnhGnKONB9+jMsO3MCkbpPL65L8PtYAiBrlnrXIXCZgcEdbVQBCoHrC7lVAXV2VRSLnnPXOYaHSw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2557995}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.57.1": {"name": "openai", "version": "4.57.1", "dependencies": {"qs": "^6.10.3", "@types/qs": "^6.9.7", "node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "33999ed3e2d92a57a04d83d785a151c3501b4eb6", "tarball": "https://registry.npmjs.org/openai/-/openai-4.57.1.tgz", "fileCount": 856, "integrity": "sha512-7q+4U9A/klaAT40bqL6sPFhIKb4jsUJ8udddCzaf8mdwICYeBG7grps/zDcrOUfkwCxCzR6fxfDDah3WqHoVUA==", "signatures": [{"sig": "MEQCICNcancQSHOaZpmBHgig6hpuUFdVvuaFLjMPTLT7xTRZAiBGt0MfqfpojNejWxxOwKU7gdyZE3p+vaOzm4MltutBUw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2564726}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.57.2": {"name": "openai", "version": "4.57.2", "dependencies": {"qs": "^6.10.3", "@types/qs": "^6.9.7", "node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "e0b17abc3fca6803563745e962637263b500915b", "tarball": "https://registry.npmjs.org/openai/-/openai-4.57.2.tgz", "fileCount": 856, "integrity": "sha512-IgIxNjo9tfgnfx6gmwNMg3tdF9giK/2lbwG5DY7zs4TP9Gz+h6h2hBOMoalLPFUVOO5HLOgMI/PFV5VDAUvvMg==", "signatures": [{"sig": "MEUCIF8xEqRzSTUDZnubCro0JP8gAAxIaKtX2V/Dk6WdghDsAiEAyIlK8iEUBzzcUTrVrDHakzDFkXnIdTpZR2C0yreqvwY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2565060}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.57.3": {"name": "openai", "version": "4.57.3", "dependencies": {"qs": "^6.10.3", "@types/qs": "^6.9.15", "node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "e8dc0973c112a348a7cb29a92d59111831db9787", "tarball": "https://registry.npmjs.org/openai/-/openai-4.57.3.tgz", "fileCount": 856, "integrity": "sha512-mTz5/SmulkkeSpqbSr6WNLRU6krkyhnbfRUC8XfaXbj1T6xUorKEELjZvbRSzI714JLOk1MeFkqYS9H4WHhqDQ==", "signatures": [{"sig": "MEUCIQCRuV3DiP2Qsu/BzN9ugcuh59G4Fabj+1CtG2fzGaaAVAIgfoXSskBdGJsxA9FPoIrU5BAhY2U2pH0Z2y5u12fvKU0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2565680}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.58.0": {"name": "openai", "version": "4.58.0", "dependencies": {"qs": "^6.10.3", "@types/qs": "^6.9.15", "node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "ccad3be9fda55aa4a345ecd400fc7725e05972fb", "tarball": "https://registry.npmjs.org/openai/-/openai-4.58.0.tgz", "fileCount": 856, "integrity": "sha512-iEjwfZ+3ZOyf/Rjfca0vrDA2y4Lt7W3auf2ZJGt6+puyvzGYwp22da4mrNcYJkcek7SngOqiQI3h/44XCSCO6A==", "signatures": [{"sig": "MEUCIQCsuX2GnxxeI+UUuwtsbzU6sZUt3S+dfZplLTS2LlGPXwIgRRWjGjsc/NBqODe1klhzrlwIsSUOf3+th0EarrF2VgY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2556854}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.58.1": {"name": "openai", "version": "4.58.1", "dependencies": {"qs": "^6.10.3", "@types/qs": "^6.9.15", "node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "be3720720e730cd6e14726b346d0bbe8d7983017", "tarball": "https://registry.npmjs.org/openai/-/openai-4.58.1.tgz", "fileCount": 856, "integrity": "sha512-n9fN4RIjbj4PbZU6IN/FOBBbxHbHEcW18rDZ4nW2cDNfZP2+upm/FM20UCmRNMQTvhOvw/2Tw4vgioQyQb5nlA==", "signatures": [{"sig": "MEQCIBv6EO2pyYJNF5T/tjG3160Zp4EaTFKpSJhMq+WrxKxyAiBhD/Togd6Z3Y27CZOTyStaWVqHTo+/Pvuc1687rnU00Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2557210}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.58.2": {"name": "openai", "version": "4.58.2", "dependencies": {"qs": "^6.10.3", "@types/qs": "^6.9.15", "node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "e45a41fe91eb626b8f07afb522c27016f58aab58", "tarball": "https://registry.npmjs.org/openai/-/openai-4.58.2.tgz", "fileCount": 856, "integrity": "sha512-hIalypYELt7/PcryFpz4Gi1z/8ZDzukWyOhr+jKM6L/GVE+t4NseaENXKt+OxnkkIm/1R2EkdGxgnHrZ0kB5bQ==", "signatures": [{"sig": "MEQCICB9V3ZyGU2/2fZxsChq6g9hHeP96av4mJST5TDj6OqaAiAv0xjWD7VgZP00Fd6hxNX/v6BtM0nQEGSD+OtzBxjekg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2558615}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.59.0": {"name": "openai", "version": "4.59.0", "dependencies": {"qs": "^6.10.3", "@types/qs": "^6.9.15", "node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "3961d11a9afb5920e1bd475948a87969e244fc08", "tarball": "https://registry.npmjs.org/openai/-/openai-4.59.0.tgz", "fileCount": 856, "integrity": "sha512-3bn7FypMt2R1ZDuO0+GcXgBEnVFhIzrpUkb47pQRoYvyfdZ2fQXcuP14aOc4C8F9FvCtZ/ElzJmVzVqnP4nHNg==", "signatures": [{"sig": "MEQCIFgvpcF70HNC48engpuRDHODwkuZMtbJYTWqk7KmOfLJAiAJQ9LsXBDXJiveY44o2UM58/Lney1W9XzjXo7e+DaEgA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2559766}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.60.0": {"name": "openai", "version": "4.60.0", "dependencies": {"qs": "^6.10.3", "@types/qs": "^6.9.15", "node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "4970354b7dfebd92b72aefd205212d3611eb7248", "tarball": "https://registry.npmjs.org/openai/-/openai-4.60.0.tgz", "fileCount": 856, "integrity": "sha512-U/wNmrUPdfsvU1GrKRP5mY5YHR3ev6vtdfNID6Sauz+oquWD8r+cXPL1xiUlYniosPKajy33muVHhGS/9/t6KA==", "signatures": [{"sig": "MEYCIQC2X/sul0I2aADGI4fHWVJkGqqo9V7mkzqs77v+UCNx5AIhAIYFMbURJHyMQwebPxHfpRxyIo0BkuBl8yXgMFXJLHo6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2562780}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.60.1": {"name": "openai", "version": "4.60.1", "dependencies": {"qs": "^6.10.3", "@types/qs": "^6.9.15", "node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "8729496d4abc8901bebf1fbc50179a92970d9231", "tarball": "https://registry.npmjs.org/openai/-/openai-4.60.1.tgz", "fileCount": 856, "integrity": "sha512-j7aZOpgN0MtflPgNaBlSnOyok4jwto0GVOxVC5g0jCfa0lvywYx+LhlFim/dXzPtyi8MjKozyLshSj93617vNQ==", "signatures": [{"sig": "MEQCIAOwbq1IAHWK0VeBfpTPOi6oUJAbefMDDo8TVfZpPJCeAiAE1eLNOrj6jBfvP1TKWvKjjuL+qtM1N+A3Xz1uMBIsug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2563600}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.61.0": {"name": "openai", "version": "4.61.0", "dependencies": {"qs": "^6.10.3", "@types/qs": "^6.9.15", "node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "fa21f2a636595d656e97c4aae6b5d9a8be9f05e2", "tarball": "https://registry.npmjs.org/openai/-/openai-4.61.0.tgz", "fileCount": 856, "integrity": "sha512-xkygRBRLIUumxzKGb1ug05pWmJROQsHkGuj/N6Jiw2dj0dI19JvbFpErSZKmJ/DA+0IvpcugZqCAyk8iLpyM6Q==", "signatures": [{"sig": "MEYCIQDUgD7ECKSASbxjeBxpZYV3BM9n5vFuWmHjKWhKzDfWowIhALZTqyE4ddwVxXOpvR4UvXk/LC6Hej4AFe4AGUangVZY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2573568}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.61.1": {"name": "openai", "version": "4.61.1", "dependencies": {"qs": "^6.10.3", "@types/qs": "^6.9.15", "node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "1fe2fa231b6de54fad32785528d7628dbbf68ab4", "tarball": "https://registry.npmjs.org/openai/-/openai-4.61.1.tgz", "fileCount": 856, "integrity": "sha512-jZ2WRn+f4QWZkYnrUS+xzEUIBllsGN75dUCaXmMIHcv2W9yn7O8amaReTbGHCNEYkL43vuDOcxPUWfNPUmoD3Q==", "signatures": [{"sig": "MEUCIBtuzn5wMOWz2A1MmMIas9SkOsfWa4sqQOnOWMTIM/hFAiEApIBzb5FCFA2n731kPL57QVe9MM4BisAoPe/Y+U9sMqY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2574268}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.62.0": {"name": "openai", "version": "4.62.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "41266a62277b1ca90f2a1511e52af5b2ffaeadf6", "tarball": "https://registry.npmjs.org/openai/-/openai-4.62.0.tgz", "fileCount": 897, "integrity": "sha512-cPSsarEXoJENNwYMx/Xh/wuvnyYf8lPSR4zDVSnRvbcMHmKkDIzXhUVvPPfuI4M4T83x25gVnlW7huWEGKG+SA==", "signatures": [{"sig": "MEQCIB4kCTqJdbfllN/ieUBwsl3hEKqEvZVU2vPwuNIQenYmAiBBe0pH9x/suwnec5P7o2AmWIeMLZdhUm4AznWPTn/n+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2689485}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.62.1": {"name": "openai", "version": "4.62.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "ebf9ae0a0c367463162e7b822a76e16efef6139d", "tarball": "https://registry.npmjs.org/openai/-/openai-4.62.1.tgz", "fileCount": 897, "integrity": "sha512-Aa6i4oBR1tV8E2d2p3MvXg57X98i8gZtHq4bQNX274qLKZVX7PXXq5P1FMonTXOrX3zwvkqN1iNccn3XK3CwVg==", "signatures": [{"sig": "MEUCIQCKkqOWvcyRQwXVGIJPpODx53rhTEE6sUyG1sWHbCQ+wgIgPFDyM8+hG3lcXxlrU7kbcIdeF3St22OEv+htSybPHrU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2689853}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.63.0": {"name": "openai", "version": "4.63.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "cabe7223788157c96c818317cc361386807157f7", "tarball": "https://registry.npmjs.org/openai/-/openai-4.63.0.tgz", "fileCount": 897, "integrity": "sha512-Y9V4KODbmrOpqiOmCDVnPfMxMqKLOx8Hwcdn/r8mePq4yv7FSXGnxCs8/jZKO7zCB/IVPWihpJXwJNAIOEiZ2g==", "signatures": [{"sig": "MEYCIQCNy+Exago16gMO36r161X5swi0gsZ3BfIbSoAm0SsfiAIhANXiZ+wkjW3Uh28Siak0Q07B9ABs7XVPIHrt0IprwmdZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2692143}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.64.0": {"name": "openai", "version": "4.64.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "28ac7907e3c00b4ccf40cc7773e810cbb26bed5b", "tarball": "https://registry.npmjs.org/openai/-/openai-4.64.0.tgz", "fileCount": 897, "integrity": "sha512-+o4vDKn2xzNMeR71rFMCPLr2penpwoALgRoJyIboIBOlkKjw+SoRiBOlO9ss1diXM4Elv01L/iliyr2oqY/l+A==", "signatures": [{"sig": "MEUCIQDKnIaps9seLZTxwyGuRltEkphRn9hEbLwjjCpOtr4hRAIgBXHkjkaNtL89R70f8+OCWqV1VPFljLuK7a1W15E3stU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2696518}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.65.0": {"name": "openai", "version": "4.65.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "97878efc00013b13b946b1be15a586bb4b125c62", "tarball": "https://registry.npmjs.org/openai/-/openai-4.65.0.tgz", "fileCount": 897, "integrity": "sha512-LfA4KUBpH/8rA3vjCQ74LZtdK/8wx9W6Qxq8MHqEdImPsN1XPQ2ompIuJWkKS6kXt5Cs5i8Eb65IIo4M7U+yeQ==", "signatures": [{"sig": "MEUCIQDsPgmSkTkhY/bSqfXhx7zDUdxG7E+17PIWvhFSinZ25wIgGMj9JhmSQFEJGQIChCc30wxR11e7blQGyOTdW6xZmS0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2707837}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.66.1": {"name": "openai", "version": "4.66.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "07d228cb52d58471e512bad8669e7d001e97791d", "tarball": "https://registry.npmjs.org/openai/-/openai-4.66.1.tgz", "fileCount": 897, "integrity": "sha512-+sSyV6VtGHerPb6Kfi76hrEOVt+wayvuw7GX/ky7rAR11kN6JVs4dgbtoaxzDXvAxc5dcQLuXeco54mBfOMoQQ==", "signatures": [{"sig": "MEUCIAg09ek/iDHXApBdzSwtv53jzdnn/Lt7D4uxklGZDB9nAiEAwV4uGNX60iVlRjerwuq08qojvQW6CUpBa/yMNSweq/w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2723752}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.67.0": {"name": "openai", "version": "4.67.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "94f46fc2e98445fab8b70d62fc684a6b6680402e", "tarball": "https://registry.npmjs.org/openai/-/openai-4.67.0.tgz", "fileCount": 897, "integrity": "sha512-jdsPSEdZbUNVtvEFE/eeL4FjKavyVMJJEdGMZk9vExglqUrblEcFxi3LK2WhskhrYKAU1MgJAI+dK9pDcA5z5w==", "signatures": [{"sig": "MEUCIDqMZ+SqUP+0VG/lPw1//qytnAz3R7gykKaM+tB+0KulAiEAsCxJwhhVDRPHwSsLefTNxpQ3MeuTJDPZD0JDhWaircM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2726917}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.67.1": {"name": "openai", "version": "4.67.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "ed07c40bf24c5f4108dfb662de1471f0c75e890d", "tarball": "https://registry.npmjs.org/openai/-/openai-4.67.1.tgz", "fileCount": 897, "integrity": "sha512-2YbRFy6qaYRJabK2zLMn4txrB2xBy0KP5g/eoqeSPTT31mIJMnkT75toagvfE555IKa2RdrzJrZwdDsUipsAMw==", "signatures": [{"sig": "MEUCIDSKIERCmqwW/wVvxVQ8Y2i/Fx+g54pqfd31W7GadlT6AiEArUr7X577pKYDSu84kBVSwh4pKfFfMFrMdrTBV9pzFQg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2727349}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.67.2": {"name": "openai", "version": "4.67.2", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "91f894fede549df26ad4ae3a228bd6d301d16756", "tarball": "https://registry.npmjs.org/openai/-/openai-4.67.2.tgz", "fileCount": 906, "integrity": "sha512-u4FJFGXgqEHrCYcD5jAD4nHj6JCiicH+/dskQY7qka9R6hOw29R0kOz7GwcA9k2JKcLf86lzAWPtPagPbO8KnQ==", "signatures": [{"sig": "MEUCIQDTGSb7v4Pq9RqSmE6dlY3primHPaX5i/x0LlrOzYsRYgIgcmQ/KwAZPDPri4YhVSsmKrjE7FLMtVvXlpNlbmbvgVA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2729823}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.67.3": {"name": "openai", "version": "4.67.3", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "a7c1b59cb9eca064f3ff3d439b7bf51487d88a49", "tarball": "https://registry.npmjs.org/openai/-/openai-4.67.3.tgz", "fileCount": 906, "integrity": "sha512-HT2tZgjLgRqbLQNKmYtjdF/4TQuiBvg1oGvTDhwpSEQzxo6/oM1us8VQ53vBK2BiKvCxFuq6gKGG70qfwrNhKg==", "signatures": [{"sig": "MEUCIHRDgj04jmOwedDlxt42Tk/eOyK8K7ZBVdD3/phhLT1MAiEAjt5XAfp92Birstkg7g3HGucarfcD3oxnRHKPmbJMOXs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2730286}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.68.0": {"name": "openai", "version": "4.68.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "6390d35fbcb2b8dc15f93b1a8c87a65ebeac8636", "tarball": "https://registry.npmjs.org/openai/-/openai-4.68.0.tgz", "fileCount": 906, "integrity": "sha512-cVH0WMKd4cColyorwqo+Gn08lN8LQ8uKLMfWXFfvnedrLq3lCH6lRd0Rd0XJRunyfgNve/L9E7uZLAii39NBkw==", "signatures": [{"sig": "MEYCIQCWcbkFc7k0o/MKzTSEM8kTp39/owBXuAyhb85mAUee3wIhAPsfFChln++2uMh/34vQPU9MMy3Oz8IyUQ3pLXZAlx0i", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2743975}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.68.1": {"name": "openai", "version": "4.68.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "a2bd0df5d6c1c0f53b296b690acce88b44c56624", "tarball": "https://registry.npmjs.org/openai/-/openai-4.68.1.tgz", "fileCount": 906, "integrity": "sha512-C9XmYRHgra1U1G4GGFNqRHQEjxhoOWbQYR85IibfJ0jpHUhOm4/lARiKaC/h3zThvikwH9Dx/XOKWPNVygIS3g==", "signatures": [{"sig": "MEUCIDISCqNllWUmrN3BYA+l8cTo+36AxWrZzSuiMQmR0HVpAiEAw3RXQoKFqpDQpgCfzQsDas7I+GOIZPnUeg879dWHsl4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2744905}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.68.2": {"name": "openai", "version": "4.68.2", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "2443d23127c61dcc9a9356ff8ea5b21acf0ba60d", "tarball": "https://registry.npmjs.org/openai/-/openai-4.68.2.tgz", "fileCount": 906, "integrity": "sha512-Ys3Jl9vkBUFtrFj4pgrF7rMte4JNekZoMgI6dWkkpOIwNUKGkc4I8jTqv86LB+TcoqkTPzV6DS269dPR9ILWsQ==", "signatures": [{"sig": "MEQCIB7W+c7AApk/xHrqFVQyebFznrAXkVLXEhHFNdRe6NDZAiBX55hHIXoBookuk8JIoub9DO+gYmSu/j0cwsvqFM6Qlw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2745240}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.68.3": {"name": "openai", "version": "4.68.3", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "104eeb3538f3d69de86fe45b660728142b3c15ce", "tarball": "https://registry.npmjs.org/openai/-/openai-4.68.3.tgz", "fileCount": 906, "integrity": "sha512-KfnhZ7mR9rK/f0O1vJGRnB3aYuDGgVkNIegJFxGviV0SGDVGlTha7FR8UV9P0NbO6l/podml0E/rk2R1egY94w==", "signatures": [{"sig": "MEMCIBb6wpi/+SqO/23ayLKrBI3Ap3JMnlQnvzHy1YUefe0UAh8DWBf6UApDBLAZyHZPMnTNlre7bEZYh3fhMuQzK2pj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2745593}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.68.4": {"name": "openai", "version": "4.68.4", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "f8d684c1f2408d362164ad71916e961941aeedd1", "tarball": "https://registry.npmjs.org/openai/-/openai-4.68.4.tgz", "fileCount": 906, "integrity": "sha512-LRinV8iU9VQplkr25oZlyrsYGPGasIwYN8KFMAAFTHHLHjHhejtJ5BALuLFrkGzY4wfbKhOhuT+7lcHZ+F3iEA==", "signatures": [{"sig": "MEQCIEdHLpLMR3LCqUfzA2vnclmVjt/LQA68qB6HKI00NwD2AiAEasahOzFya1W9HqcmoLd3cPPOWkuoCrYCmGZ2wC2vOg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2745928}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.69.0": {"name": "openai", "version": "4.69.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "ac2463719280987e506e4bd62dd477337e2406a1", "tarball": "https://registry.npmjs.org/openai/-/openai-4.69.0.tgz", "fileCount": 906, "integrity": "sha512-S3hOHSkk609KqwgH+7dwFrSvO3Gm3Nk0YWGyPHNscoMH/Y2tH1qunMi7gtZnLbUv4/N1elqCp6bDior2401kCQ==", "signatures": [{"sig": "MEYCIQCGmO3OK7Wce2tSkayhaiOIFrMDjj3QKz4P93aJSL1VXwIhALw2gVvakQhh0sGsa2jfU/guLne1RSq8gbJ+T3To7lwS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2746885}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.70.0": {"name": "openai", "version": "4.70.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "65662d779eb1bf67a2dd79d975a65652aac40dff", "tarball": "https://registry.npmjs.org/openai/-/openai-4.70.0.tgz", "fileCount": 906, "integrity": "sha512-Hz8lRAH2oXBG9ct0mssSag/iJz9vrH+k6hSl3enwwtnJz5x9sCaBmWNcYYTsPmkz3GmgUUD102GRrWWBbufqFQ==", "signatures": [{"sig": "MEYCIQDBOIAiHvZ0DVWhwbGnAUnaVxvP9gJIcyd+ZYz++AcAOgIhAN4t/ILsdtmbE7qVFdOyHh2izoWNwqQzb2yx6Jk9ALck", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2727803}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.70.1": {"name": "openai", "version": "4.70.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "a47d6c924c112a403570de3235c43b709a1cc637", "tarball": "https://registry.npmjs.org/openai/-/openai-4.70.1.tgz", "fileCount": 906, "integrity": "sha512-9tQ8ay+GF2qhnydPL2ytI03ilScfps5oc7p765aUm3uUH6llwmEUgjmztGIF2Nvdw2RtMI4qEd2uAKkPMO/rPA==", "signatures": [{"sig": "MEUCICvPfb+NUazTj3Pi619LZCL4EQXWXY3d66w6uxwN4t5JAiEA3yiTKyoVuYCPrOYS69M4nV/9OO13TR6Lnn8ypy5nsQM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2728131}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.70.2": {"name": "openai", "version": "4.70.2", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "dfe54b2b13996b76d93f2aaac07203d2660d6e65", "tarball": "https://registry.npmjs.org/openai/-/openai-4.70.2.tgz", "fileCount": 906, "integrity": "sha512-Q2ymi/KPUYv+LJ9rFxeYxpkVAhcrZFTVvnJbdF1pUHg9eMC6lY8PU4TO1XOK5UZzOZuuVicouRwVMi1iDrT4qw==", "signatures": [{"sig": "MEUCIQCXh6PsAn/KqfnCZGeH0mRVTA04nomncHDncz+8+kGHqgIgOtui/Bp2n8N/+R/f/a3f2iQqB2ASDpPsX5YDxQm1QU4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2728535}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.70.3": {"name": "openai", "version": "4.70.3", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "47e6acafd28f30a216a6721ee301a5744fb4b9df", "tarball": "https://registry.npmjs.org/openai/-/openai-4.70.3.tgz", "fileCount": 906, "integrity": "sha512-N2XOWjuT5yKIdLgjZkQt9i5+cAXI7qKM7E5PpIsmVfnTi/Y812omr3rozgKwxXJC6aga8nl2BWos4HRdlZllFA==", "signatures": [{"sig": "MEQCIC+y5M1kCMbWjZ83+d7Yt9C/6TQwLvDhzeVs5u02SkN2AiA5AY5ns8FEuZYfmPmNgx7Y7bzh38WoVYJw1cvWdAKdvw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2728774}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.71.0": {"name": "openai", "version": "4.71.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "77f344c0229f52786b833e6988634a882b06c76f", "tarball": "https://registry.npmjs.org/openai/-/openai-4.71.0.tgz", "fileCount": 906, "integrity": "sha512-jeJ7+6cZvj+ZbIsbX/Ag8+pug2+vjKbrD/v3Hwp6uv3KZyWjSkZa5MdUshzpNC3jsFzakfbUhEEFQXsKWNgm/g==", "signatures": [{"sig": "MEYCIQCaS1xxaKrhr2BdxVVb5qNfkNiDCOoeEqv9hQpngwVpgAIhAIQTxH9Wcp0wcFKhSLeryKG8VMBzPcK/fKrahNuMtWmd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2732892}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.71.1": {"name": "openai", "version": "4.71.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "f26bf5db00d75703676d80bf0ae7cb7674e41eac", "tarball": "https://registry.npmjs.org/openai/-/openai-4.71.1.tgz", "fileCount": 906, "integrity": "sha512-C6JNMaQ1eijM0lrjiRUL3MgThVP5RdwNAghpbJFdW0t11LzmyqON8Eh8MuUuEZ+CeD6bgYl2Fkn2BoptVxv9Ug==", "signatures": [{"sig": "MEQCIH1N3TP6W9KKvTyiY+dNvF40VuWIObtXzpfrSa5msVFkAiATJQlIni711LugL0prwoaBMwvl/2ItwKt/IqUzPfGokg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2733245}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.72.0": {"name": "openai", "version": "4.72.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "61630553157a0c9bb1c304b1dd4d2f90e9ec4cf7", "tarball": "https://registry.npmjs.org/openai/-/openai-4.72.0.tgz", "fileCount": 906, "integrity": "sha512-hFqG9BWCs7L7ifrhJXw7mJXmUBr7d9N6If3J9563o0jfwVA4wFANFDDaOIWFdgDdwgCXg5emf0Q+LoLCGszQYA==", "signatures": [{"sig": "MEUCIQDJ03QSzlLw163hUUX/VJPdCtOnSemsKZs1Vc5oyopr6QIgLHvWVEvFd0e6sfO+GlGQ+1CIjzik8JdVckqfKx91WaY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2733756}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.73.0": {"name": "openai", "version": "4.73.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "b8f8a4793d4db91e7eeab7235446d2cfe3aa0e9c", "tarball": "https://registry.npmjs.org/openai/-/openai-4.73.0.tgz", "fileCount": 906, "integrity": "sha512-NZstV77w3CEol9KQTRBRQ15+Sw6nxVTicAULSjYO4wn9E5gw72Mtp3fAVaBFXyyVPws4241YmFG6ya4L8v03tA==", "signatures": [{"sig": "MEYCIQDrhJW7cKlI7lnyt8QBpvLj1Mtpkdk34mVgb4q4PJ1a4AIhAJLyvQVkV9KLxM2d0mwuly3BdYRXkkcgjGrClzomlwg7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2736157}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.73.1": {"name": "openai", "version": "4.73.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "532bd000d5f1a558e4fff1119da6749992ac41e4", "tarball": "https://registry.npmjs.org/openai/-/openai-4.73.1.tgz", "fileCount": 906, "integrity": "sha512-nWImDJBcUsqrhy7yJScXB4+iqjzbUEgzfA3un/6UnHFdwWhjX24oztj69Ped/njABfOdLcO/F7CeWTI5dt8Xmg==", "signatures": [{"sig": "MEYCIQCqsUGniwBy3YkGlnqEwQ65jK98cv6h2YzMUuHDPG9FvwIhAMd4qU4AQGO4pNq2G0w1ACT/JBqTrbg4dMAkc9yMMTLs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2736817}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.74.0": {"name": "openai", "version": "4.74.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "db9fc1442e20cb92e9d7afc40b0dc0f71c04cba4", "tarball": "https://registry.npmjs.org/openai/-/openai-4.74.0.tgz", "fileCount": 906, "integrity": "sha512-pQ8t1jchUymw5WB5jZPchuBtWvxul7RyVxa+9RWfiCQyzvzUyI2sKvUYfpEDI/ouaRLcik3K6psj15ByCefeNA==", "signatures": [{"sig": "MEUCIQCyJLI3B1qXoKkfKa2zPVCDtFACU+fDxTSXt/Rxp9lo1wIgWPxRpKY4F2dzoKHNmyTN1K0oqY7RwmWwz56n9fqH3i8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2737176}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.75.0": {"name": "openai", "version": "4.75.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "8bd44885aba5f8f35fe9dea17a4d6859bca5d199", "tarball": "https://registry.npmjs.org/openai/-/openai-4.75.0.tgz", "fileCount": 906, "integrity": "sha512-8cWaK3td0qLspaflKWD6AvpQnl0gynWFbHg7sMAgiu//F20I4GJlCCpllDrECO6WFSuY8HXJj8gji3urw2BGGg==", "signatures": [{"sig": "MEUCIHPti49xVeputxsvF6GAmD67MGEriYFf11ud8aqQRR23AiEAlcFDD7ueInM8hSNyHMcmwxjN+EwlVVuF8WT9X+Y6/xk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2737510}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.76.0": {"name": "openai", "version": "4.76.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "0ba135ff6452eadbce980c604f98799dfa239325", "tarball": "https://registry.npmjs.org/openai/-/openai-4.76.0.tgz", "fileCount": 906, "integrity": "sha512-QBGIetjX1C9xDp5XGa/3mPnfKI9BgAe2xHQX6PmO98wuW9qQaurBaumcYptQWc9LHZZq7cH/Y1Rjnsr6uUDdVw==", "signatures": [{"sig": "MEQCIEEuAcZAfM5HCgIyf9Q8AIrFAZvAfFFXYtRVxGWAOJSBAiApM99ubQylPmlz8yalAUBNYX7zGDaKLBWUWksC9BVWpQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2738023}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.76.1": {"name": "openai", "version": "4.76.1", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "3e844cef2f2d55814ff7c6e134700eacb7c9897b", "tarball": "https://registry.npmjs.org/openai/-/openai-4.76.1.tgz", "fileCount": 906, "integrity": "sha512-ci63/WFEMd6QjjEVeH0pV7hnFS6CCqhgJydSti4Aak/8uo2SpgzKjteUDaY+OkwziVj11mi6j+0mRUIiGKUzWw==", "signatures": [{"sig": "MEUCIE5tJZyfO87okuKdWsmbm4pu1NFQqjL4JXbiPYrNgie8AiEA7ys+61xX3M1BSzIB9D6Vp8d82Fz7U8kcqstf29j2UEc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2737971}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.76.2": {"name": "openai", "version": "4.76.2", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "aeadbe2d51277579e0825fe97b9494aacb2c974f", "tarball": "https://registry.npmjs.org/openai/-/openai-4.76.2.tgz", "fileCount": 906, "integrity": "sha512-T9<PERSON>yxAFwLNZz3onC+SFvSR0POF18egIsY8lLze9e2YBe1wzQNf8IHcIgFPWizGPpoCGv/9i3IdTAx3EnLmTL4A==", "signatures": [{"sig": "MEQCIDYaAb3EwkrKnmosJo+fxHz8/8O0/Zj0K5FUgod54b6rAiBPMnWm0r5TYBYJbUdPkLu5zDCmTCYy7IBLQpXRn7xMXA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2736965}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.76.3": {"name": "openai", "version": "4.76.3", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "67ea81b9ad330997593ba8bd0910d2f4a992740a", "tarball": "https://registry.npmjs.org/openai/-/openai-4.76.3.tgz", "fileCount": 906, "integrity": "sha512-BISkI90m8zT7BAMljK0j00TzOoLvmc7AulPxv6EARa++3+hhIK5G6z4xkITurEaA9bvDhQ09kSNKA3DL+rDMwA==", "signatures": [{"sig": "MEUCIBn3DfoBDSatZirqJSMBpmMiQXZP20vGlOPytVosSEBvAiEA1vfrKjAllF/lhU/XTvqVGIWKHcoDns7L2IUwMdf8KNY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2737481}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.77.0": {"name": "openai", "version": "4.77.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "228f2d43ffa79ae9d8b5d4155e965da82e5ac330", "tarball": "https://registry.npmjs.org/openai/-/openai-4.77.0.tgz", "fileCount": 906, "integrity": "sha512-WWacavtns/7pCUkOWvQIjyOfcdr9X+9n9Vvb0zFeKVDAqwCMDHB+iSr24SVaBAhplvSG6JrRXFpcNM9gWhOGIw==", "signatures": [{"sig": "MEUCIEIweXArLHYjHmgRPam3q/w9dZ/YugeheiCSlDqD1LqwAiEAkH4DpFcjdPrloXngdwcC3DLBk4vyFOpRGKCJSL7egA4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760441}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "5.0.0-alpha.0": {"name": "openai", "version": "5.0.0-alpha.0", "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "f6e95300ad728458b8e537260864300e32728c47", "tarball": "https://registry.npmjs.org/openai/-/openai-5.0.0-alpha.0.tgz", "fileCount": 1172, "integrity": "sha512-sfxLpUQePGt3FRRAphDW22A5fdw/HYGD/o6HlGgnGI9DiGp8OxgWJ+Wkze9rgQgyNebC3quF3p8Y8gSZAtxxSw==", "signatures": [{"sig": "MEYCIQCFEXajMCTydOyr6mPQMmHYEJMECK7lk0S4mkTPTD8O/AIhAOn2VNoFPRpxEL4TSYESXqo2Q1N73+z9v4e8rG3vPyiP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3371037}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.77.3": {"name": "openai", "version": "4.77.3", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "10f6906f2f737a98b656b745a6b710e595ba2e4d", "tarball": "https://registry.npmjs.org/openai/-/openai-4.77.3.tgz", "fileCount": 906, "integrity": "sha512-wLDy4+KWHz31HRFMW2+9KQuVuT2QWhs0z94w1Gm1h2Ut9vIHr9/rHZggbykZEfyiaJRVgw8ZS9K6AylDWzvPYw==", "signatures": [{"sig": "MEUCIQCT7dX/AATiRjnIFe0Gp0/hwKJ/+6NUlAARBCIOGhYwigIgOLt2oafW4e4Lig/Rijkn+hHwkSCschBc2GEen0+BLQE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2763457}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.77.4": {"name": "openai", "version": "4.77.4", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "1093d165efb3e13e763faf42fa62e34313e293e9", "tarball": "https://registry.npmjs.org/openai/-/openai-4.77.4.tgz", "fileCount": 906, "integrity": "sha512-rShjKsZ/HXm1cSxXt6iFeZxiCohrVShawt0aRRQmbb+z/EXcH4OouyQZP1ShyZMb63LJajpl8aGw3DzEi8Wh9Q==", "signatures": [{"sig": "MEUCICimP8yxE6GQorbpZ4dgSS1a6vZyVFrAM0xmMlWfDQ90AiEA2kRmY9s7piXQIoNuWFxAP4UsIovWf9O5jgaPo+LSAkA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2763798}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.78.0": {"name": "openai", "version": "4.78.0", "dependencies": {"node-fetch": "^2.6.7", "@types/node": "^18.11.18", "formdata-node": "^4.3.2", "agentkeepalive": "^4.2.1", "abort-controller": "^3.0.0", "@types/node-fetch": "^2.6.4", "form-data-encoder": "1.7.2"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "79a021de5247944f63c250bdc78e9aa74c8b2236", "tarball": "https://registry.npmjs.org/openai/-/openai-4.78.0.tgz", "fileCount": 929, "integrity": "sha512-4rRsKkx++5m1zayxkryVH+K/z91cv1sRbaNJAhSQjZiSCQOR7eaM8KpfIssXrS9Hlpta7+VcuO/fi57pW8xGjA==", "signatures": [{"sig": "MEYCIQDliFsrBtM/yom5zVrusQZIkvb+3xL6wnw0RIwQRUpdKgIhAMAUky76L2vi2T7N6NbA3fr8Frq/41436NsKLuYcI+0i", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2930792}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "4.78.1": {"name": "openai", "version": "4.78.1", "dependencies": {"@types/node": "^18.11.18", "@types/node-fetch": "^2.6.4", "abort-controller": "^3.0.0", "agentkeepalive": "^4.2.1", "form-data-encoder": "1.7.2", "formdata-node": "^4.3.2", "node-fetch": "^2.6.7"}, "peerDependencies": {"zod": "^3.23.8"}, "bin": {"openai": "bin/cli"}, "dist": {"shasum": "44c3b195d239891be9c9c53722539ad8a1fcc5f2", "integrity": "sha512-drt0lHZBd2lMyORckOXFPQTmnGLWSLt8VK0W9BhOKWpMFBEoHMoz5gxMPmVq5icp+sOrsbMnsmZTVHUlKvD1Ow==", "tarball": "https://registry.npmjs.org/openai/-/openai-4.78.1.tgz", "fileCount": 929, "unpackedSize": 2931623, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDd1fUQl5jTvN+n5diIYZtYQgH72pCkbInpHDTGbIR7VAiEA2dzLNPHyazkNgSnN8O3VTQvtbMlZyIjQNtltOCaZfSM="}]}, "peerDependenciesMeta": {"zod": {"optional": true}}}}, "modified": "2025-01-10T17:41:01.752Z"}