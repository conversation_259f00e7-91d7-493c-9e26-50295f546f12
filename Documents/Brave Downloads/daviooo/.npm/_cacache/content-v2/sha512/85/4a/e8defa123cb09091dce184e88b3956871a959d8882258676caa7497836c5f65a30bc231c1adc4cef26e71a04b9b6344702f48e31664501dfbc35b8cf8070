{"_id": "@types/ws", "_rev": "1083-ab72a17d733fed843a031bedc2c522a1", "name": "@types/ws", "dist-tags": {"ts2.7": "6.0.3", "ts2.6": "6.0.3", "ts2.5": "6.0.3", "ts2.4": "6.0.3", "ts2.3": "6.0.3", "ts2.2": "6.0.3", "ts2.1": "6.0.3", "ts2.0": "6.0.3", "ts2.8": "7.2.4", "ts2.9": "7.2.4", "ts3.0": "7.2.6", "ts3.1": "7.2.6", "ts3.2": "7.4.0", "ts3.3": "7.4.0", "ts3.4": "7.4.0", "ts3.5": "7.4.4", "ts3.6": "7.4.7", "ts3.7": "8.2.0", "ts3.8": "8.5.1", "ts4.0": "8.5.3", "ts3.9": "8.5.3", "ts4.1": "8.5.3", "ts4.2": "8.5.4", "ts4.3": "8.5.5", "ts4.4": "8.5.5", "ts4.7": "8.5.10", "ts4.6": "8.5.10", "ts4.5": "8.5.10", "ts5.5": "8.5.13", "ts5.4": "8.5.13", "ts5.3": "8.5.13", "ts5.2": "8.5.13", "ts5.1": "8.5.13", "ts5.0": "8.5.13", "ts4.9": "8.5.13", "ts4.8": "8.5.13", "latest": "8.5.13", "ts5.6": "8.5.13", "ts5.7": "8.5.13", "ts5.8": "8.5.13"}, "versions": {"0.0.14-alpha": {"name": "@types/ws", "version": "0.0.14-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/loyd"}, "license": "MIT", "_id": "@types/ws@0.0.14-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "d7c46e446f9b42f325db4ee35941740fc1649462", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-0.0.14-alpha.tgz", "integrity": "sha512-F3RTtQ6eHsyLMFR/5oZhVv+Pw8p8EDu0UOqtr1/NA2RhSTpPDAh6HreWlo0g2KOd7O0n8/irUQy+5OK9T9p3oQ==", "signatures": [{"sig": "MEUCIQDomPTWV/p3DH2x/tCyBqj/MkJECmbwuQQHWGcYxTzndAIgf+HhLulW8FyjUR6Z2NTvrwzQAqmBZMXqgs3bBDAhQqQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\ws", "_shasum": "d7c46e446f9b42f325db4ee35941740fc1649462", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\ws", "_npmVersion": "3.8.2", "description": "Type definitions for ws from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"net": "*", "http": "*", "events": "*", "@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/ws-0.0.14-alpha.tgz_1463512642753_0.8953879200853407", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.15-alpha": {"name": "@types/ws", "version": "0.0.15-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/loyd"}, "license": "MIT", "_id": "@types/ws@0.0.15-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "b1cfe2f60f93a0b190f89d5128e807fcc117a078", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-0.0.15-alpha.tgz", "integrity": "sha512-ti3rqN9VFVXwRuAArGEpaFeYLyPlkIAtGLsO+yZ5iEx5cmk4r4T9ZR+mZOYIWujiI5JLFPt6p8o6yyGWasyaLA==", "signatures": [{"sig": "MEQCIBNiUuyt7VgBgA1rKJ8qUXQCVMhEeRvlbBWUr7RnomuLAiADKgusjOug7uhYqAmXTyU9VhHfOzjdD3loyQdFC4QzMQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\ws", "_shasum": "b1cfe2f60f93a0b190f89d5128e807fcc117a078", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\ws", "_npmVersion": "3.8.2", "description": "Type definitions for ws from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"net": "*", "http": "*", "events": "*", "@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/ws-0.0.15-alpha.tgz_1463697931527_0.7051708763465285", "host": "packages-12-west.internal.npmjs.com"}}, "0.0.20-alpha": {"name": "@types/ws", "version": "0.0.20-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/loyd"}, "license": "MIT", "_id": "@types/ws@0.0.20-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "7761ba596a44de5185840ee9da8a9a5e4c146566", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-0.0.20-alpha.tgz", "integrity": "sha512-pfjySUlzWtIFNyBKCNlTy6l/Cqy+X7NGmu69+EUCX+Y7nbYv/krq9miWj89UoGZoaRPMrAS+0BjVy/aabqr4Aw==", "signatures": [{"sig": "MEUCIBFkcetis0esf8HXPaYzmuQxb7OImViRUqh6Lzdt6g33AiEAtJ7sHRulTJunndDT0WdTpGiaRbjcictdQeHC2yvWJjM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\ws", "_shasum": "7761ba596a44de5185840ee9da8a9a5e4c146566", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\ws", "_npmVersion": "3.8.2", "description": "TypeScript definitions for ws", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/ws-0.0.20-alpha.tgz_1463777809823_0.13087232480756938", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.21-alpha": {"name": "@types/ws", "version": "0.0.21-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/loyd"}, "license": "MIT", "_id": "@types/ws@0.0.21-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "20cbc196974f29d4b6be808f3a536912d0a3e27a", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-0.0.21-alpha.tgz", "integrity": "sha512-yOGjnO678qaw4QfeAuxA4GrrBCltMFTGvgWsf2QWU/GoYbnoReg6LMXE7jB3xTdIBoTso2cDg+Vvo/b7ib4VUQ==", "signatures": [{"sig": "MEUCIDBgoHufKoZF0A4fnirPmAf9I+uchbfesImM9BKdP5wgAiEAgiJza/x3mxG9vt60Awwm3IyVs8fB7Dqbvcp4ooGijVA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\ws", "_shasum": "20cbc196974f29d4b6be808f3a536912d0a3e27a", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\ws", "_npmVersion": "3.8.2", "description": "TypeScript definitions for ws", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/ws-0.0.21-alpha.tgz_1464156699449_0.9051715950481594", "host": "packages-12-west.internal.npmjs.com"}}, "0.0.22-alpha": {"name": "@types/ws", "version": "0.0.22-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/loyd"}, "license": "MIT", "_id": "@types/ws@0.0.22-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "770def889e152509895afb857e01fcbb240b4ca3", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-0.0.22-alpha.tgz", "integrity": "sha512-4bJJVnFMPjbaP5vxutWADLiHQoX+6aAaX4kaKCmUWQSH/hiNJLqrxf+xY/C6MPRz0oR25S+Kkue25uIL99E/FA==", "signatures": [{"sig": "MEYCIQCiErE7G1tnT386S8URbHFMMYPSpwYf5uDqyOpgqydhMgIhAOApPP2k91QA9l2yAN2UswDIzFC7OvAYkx0qgaXxXekK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\ws", "_shasum": "770def889e152509895afb857e01fcbb240b4ca3", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\ws", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.9.5", "description": "TypeScript definitions for ws", "directories": {}, "_nodeVersion": "6.2.2", "dependencies": {"@types/node": "4.0.*"}, "_npmOperationalInternal": {"tmp": "tmp/ws-0.0.22-alpha.tgz_1467406526539_0.9852784110698849", "host": "packages-12-west.internal.npmjs.com"}}, "0.0.23-alpha": {"name": "@types/ws", "version": "0.0.23-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/loyd"}, "license": "MIT", "_id": "@types/ws@0.0.23-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "381aa83808719ccdd3fcff528e58403ff18983fd", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-0.0.23-alpha.tgz", "integrity": "sha512-lv8Esg7eAQOa0iQUKKrX5Ie0XpfR1Y17OtvKgoWo8UvGX4jTHCEcBwbY5dQaB1SLMxevWl70iyRZ+ho7VtMYNQ==", "signatures": [{"sig": "MEYCIQDGAANCvygnMKkp0wC+tJNmq6N1/2QdUijTvs6BNq7jaQIhAOHpA+L6RZqQzwo/uABG3U6mIqIQRoeknfmji6bengcz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\ws", "_shasum": "381aa83808719ccdd3fcff528e58403ff18983fd", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\ws", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for ws", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"@types/node": "4.0.24-alpha"}, "_npmOperationalInternal": {"tmp": "tmp/ws-0.0.23-alpha.tgz_1467418641177_0.11155138979665935", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.24-alpha": {"name": "@types/ws", "version": "0.0.24-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/loyd"}, "license": "MIT", "_id": "@types/ws@0.0.24-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "541499600ea54984d61887ab186cdde88d802cdc", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-0.0.24-alpha.tgz", "integrity": "sha512-Ob<PERSON>xaLUzcZB8RhAI/1LrbkWRfv1K3Cc8vip948Nns1/HvL+dqkWbETtfg3CrJ4g4W99OIh3RAWSZIngTU2C9kw==", "signatures": [{"sig": "MEQCIH3wHD1j2UaTvqTp7iISUY7v7pASqRpezggif+7nt/2lAiAHB3/3bXuSXtLVHz/yX6VCigwrmX5Yswuk5vVfIzU5RQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\ws", "_shasum": "541499600ea54984d61887ab186cdde88d802cdc", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\ws", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for ws", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"@types/node": "4.0.25-alpha"}, "_npmOperationalInternal": {"tmp": "tmp/ws-0.0.24-alpha.tgz_1467430833808_0.7455715835094452", "host": "packages-12-west.internal.npmjs.com"}}, "0.0.25-alpha": {"name": "@types/ws", "version": "0.0.25-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/loyd"}, "license": "MIT", "_id": "@types/ws@0.0.25-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "752f2fa29a17c3d1ec9fe7a98869aa540b116a97", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-0.0.25-alpha.tgz", "integrity": "sha512-3Y1ZP01XCAliQNH+gTsfIULtxaLawHjNNb0ku8l1M7LfmRzVFG6cX9n7b+edzB38KoNFBvEcqLAf1DPM6x5Xig==", "signatures": [{"sig": "MEQCIAYQ02uH9kxLTlCqJ2b4PxJhLQafVL4ZXGNo9ZlsliHfAiAHonRUIXZI67UrK1vkOwJkRs+XyjTlyJ5AwUCX+vdl2A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\ws", "_shasum": "752f2fa29a17c3d1ec9fe7a98869aa540b116a97", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\ws", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for ws", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"@types/node": "4.0.26-alpha"}, "_npmOperationalInternal": {"tmp": "tmp/ws-0.0.25-alpha.tgz_1467596010633_0.9261964941397309", "host": "packages-12-west.internal.npmjs.com"}}, "0.0.26-alpha": {"name": "@types/ws", "version": "0.0.26-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/loyd"}, "license": "MIT", "_id": "@types/ws@0.0.26-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "749f405cdab8c02e6f65a5770a066554875122f6", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-0.0.26-alpha.tgz", "integrity": "sha512-obf3eq/epTgH5WLRL1zv9NceP/4gItL8tEsrCKixdmABdaqN84KlLwUNWXscSWqeINSo76c7KPz+UMgMTU53vA==", "signatures": [{"sig": "MEYCIQDcEwwjdPza1BAbtgO2bGgToqD+CqDF2GBuXAqTi7B6VAIhAOJqYwfnV04w8A+jlqPS/BdbwYJCppTp1B0RXdTY3gcw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\ws", "_shasum": "749f405cdab8c02e6f65a5770a066554875122f6", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\ws", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "TypeScript definitions for ws", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"@types/node": "4.0.27-alpha"}, "_npmOperationalInternal": {"tmp": "tmp/ws-0.0.26-alpha.tgz_1468014436030_0.9117195669095963", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.27": {"name": "@types/ws", "version": "0.0.27", "author": {"name": "<PERSON>", "email": "https://github.com/loyd"}, "license": "MIT", "_id": "@types/ws@0.0.27", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "8afebaad2663ec180b321e79d12734f19f595133", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-0.0.27.tgz", "integrity": "sha512-jEFG3y9U2WeJmmlv0ucCGUpOo65WBScfc+mMWPNKwdoL7tNWkxBYl/3m3iqDhVjJJhGlhd2tYfsuXMyHbzMq+Q==", "signatures": [{"sig": "MEYCIQC00iXztxzr/R3aFMJu/jbNssEbR4qau8vQRoJ+5ZPwzgIhAJibPCY3waJqAgCTXpegL8nYYC2oGUNJM+EBuA0UQ5p4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\ws", "_shasum": "8afebaad2663ec180b321e79d12734f19f595133", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\ws", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "TypeScript definitions for ws", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"@types/node": "4.0.*"}, "_npmOperationalInternal": {"tmp": "tmp/ws-0.0.27.tgz_1468513509307_0.8419363514985889", "host": "packages-12-west.internal.npmjs.com"}}, "0.0.28": {"name": "@types/ws", "version": "0.0.28", "author": "<PERSON> <https://github.com/loyd>", "license": "MIT", "_id": "@types/ws@0.0.28", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "3585e81b26f4cd8f8a9a702378edb603e905eb2c", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-0.0.28.tgz", "integrity": "sha512-VmLVSeLYEQnMzOjJOR4syaDmEiuVrHtlCRUt3Zd91XJpgX35oYgfUkV1MlGKjG31kIqn0YoKvBUETZGfsH2o2w==", "signatures": [{"sig": "MEUCIHpb8UE48hLsuDLjRyxPPCzR/d+lCT+jJypmClXgf3KdAiEA65LJ2VC+ajuWbCLcxOQIMo/Dt3oiAh1tMRjZnkvoO0o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "6.0.*"}, "_npmOperationalInternal": {"tmp": "tmp/ws-0.0.28.tgz_1470154541593_0.7799404663965106", "host": "packages-12-west.internal.npmjs.com"}}, "0.0.29": {"name": "@types/ws", "version": "0.0.29", "author": "<PERSON> <https://github.com/loyd>", "license": "MIT", "_id": "@types/ws@0.0.29", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "e9ddefea65770b0dde1a46ed2a3e15ae09dbef32", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-0.0.29.tgz", "integrity": "sha512-XLpKao2nfIvhmoiffZ40iUgC2Q5ixkY+cisb8nbD3DnpWqNDO9vvrs33z7sTCqDe3dKkfGoDrgYQ13WkSnHPyg==", "signatures": [{"sig": "MEQCICLUoC0AsEb83ieDWQjJWgsUTBONqvx2rg16P/OLxQFLAiASX++9wDKyArJfYmVQ/Yx5v0VOrQI+XipqqUO7kqQqfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "6.0.*"}, "_npmOperationalInternal": {"tmp": "tmp/ws-0.0.29.tgz_1471621598801_0.8646421735174954", "host": "packages-12-west.internal.npmjs.com"}}, "0.0.30": {"name": "@types/ws", "version": "0.0.30", "author": "<PERSON> <https://github.com/loyd>", "license": "MIT", "_id": "@types/ws@0.0.30", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "55eb54ec2826d6558500029f7cf9b6f4f71364ba", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-0.0.30.tgz", "integrity": "sha512-5wVRkZezyOEfSRUxd1be/HIG6Iu+B/bpLa0eNPsNCPiBxgRyCj16ZMZ5VE1q8TSfejqFExgBO6j/yx9L4HTVGw==", "signatures": [{"sig": "MEUCIAoqfqiDN8Lpd0EDpmdrwSLyduzt3/IcpTItpbqveULoAiEAlrPqGmMRQQznNVhtlBdHLB7wOIrEF0U4YekoynWswRs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "6.0.*"}, "_npmOperationalInternal": {"tmp": "tmp/ws-0.0.30.tgz_1472151881771_0.7122400735970587", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.31": {"name": "@types/ws", "version": "0.0.31", "author": "<PERSON> <https://github.com/loyd>", "license": "MIT", "_id": "@types/ws@0.0.31", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "809c52696b48ef8b7372b39a788cc2ea5412f0bf", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-0.0.31.tgz", "integrity": "sha512-7+TgfrbDnvc0whzeuwqLNeSZLyWqB9xY049w5OTeU4rAGBNvN/u+6ydnqDUpFEIRTBzEGtPG6nfYLM7RDhVC6w==", "signatures": [{"sig": "MEQCIFjqm6TdKXbPlJLZo+2cy/dK5A6vbjZ1HL8qjCfNhLzJAiBX7xEezvsBx78zEej/RCF5gR7Laij4qAUCIqZnAN3QPw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/ws-0.0.31.tgz_1474309214893_0.05747388768941164", "host": "packages-16-east.internal.npmjs.com"}, "typesPublisherContentHash": "6eba6d7e75e3de5cc8dbb180e711934f995b6f46ae3691e473fd37a674f8a381"}, "0.0.32": {"name": "@types/ws", "version": "0.0.32", "author": "<PERSON> <https://github.com/loyd>", "license": "MIT", "_id": "@types/ws@0.0.32", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "f2d6418c12584ca853838567531c8666fea0ff45", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-0.0.32.tgz", "integrity": "sha512-1i8FnQ/HEyXl9XP8o7dTlWNahpKnS95lY0WxbKQy0fCshd559SkasvszpQUFAodVapbQebzrK0RQIOwkpMiAJQ==", "signatures": [{"sig": "MEYCIQD7I+wQiTpYBJZ86UVYISyRDeWvaerTTtHd8qsKSEZgKAIhAOL1d+FM5f8AY7bCC8LwtKShvDd32a3JVj2u+OpOKyTX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/ws-0.0.32.tgz_1474489745984_0.7198145885486156", "host": "packages-12-west.internal.npmjs.com"}, "typesPublisherContentHash": "86a0a46a928e0b303a9f785e21e661a6d40f91f4c7f0431dcb7e94c674f72eb0"}, "0.0.33": {"name": "@types/ws", "version": "0.0.33", "author": "<PERSON> <https://github.com/loyd>", "license": "MIT", "_id": "@types/ws@0.0.33", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "ed28095edcc147b3eba90364a983db5d247fbb8e", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-0.0.33.tgz", "integrity": "sha512-/kRrbgq+YGszFSo4a4OlQWFcy79COxG7SeF2HP6/0SDfZcrVLhtQWDLMwChXkjnSDQtQgoqiazWA3UNPSNJBYg==", "signatures": [{"sig": "MEQCIFsQoRRoxEJPt11nvMre4mSDI6wARc5UiUjUGwd2FUfNAiAm5753eJElAnuhqZkkEK9nrbaSHZyOeLkVY5DhqE4wEw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/ws-0.0.33.tgz_1474653609035_0.37866590335033834", "host": "packages-12-west.internal.npmjs.com"}, "typesPublisherContentHash": "614262839f2e003b201893b64f8c4856e1ab5baeafde03d27486e8a0d60ae161"}, "0.0.34": {"name": "@types/ws", "version": "0.0.34", "author": "<PERSON> <https://github.com/loyd>", "license": "MIT", "_id": "@types/ws@0.0.34", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "72b4157c41fa5696eacd3f726d84d33808d7d3e5", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-0.0.34.tgz", "integrity": "sha512-yRh8gX5fEHCU4Bgv3POuLyBiyl3wXSeZEW5y279jCN5/FqHzzskL7rL+ijMHLaOzN8mEIdC/aH/qoFAQhXeK6Q==", "signatures": [{"sig": "MEUCIQCyMWs2ZvHZWJ2LCLs8ux98i4w7sH63rePjhBPXjVXETAIgFOiJflE3asbZF2yXcljEamOWXTr+s+XTZ+ea/x7xWbs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/ws-0.0.34.tgz_1475701476225_0.5620560047682375", "host": "packages-16-east.internal.npmjs.com"}, "typesPublisherContentHash": "de55ad1d9790a98236a0d7be0ffb333f60c84ce1da29eaf25da4ccf85b2a66fe"}, "0.0.35": {"name": "@types/ws", "version": "0.0.35", "author": "<PERSON> <https://github.com/loyd>", "license": "MIT", "_id": "@types/ws@0.0.35", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "2a4ae611d95a481cadf023a5775c72fd4dd6d856", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-0.0.35.tgz", "integrity": "sha512-p3oITedsxgR8mfudLQJR4WEJw+/JqEkxnzG8GXGKr5rc2P2LY02n+dYsErbG8J3emOTj6DfO/SAE2mVFLccdJA==", "signatures": [{"sig": "MEUCIQCOzqWju7qBJfrgTp2b061vQ2T1HMOtTnbZ1VzLHweB3wIgbtF9dl5frTXOgVkdFWWJ0oiTYN5fKoOfQoZ5FXtazuw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/ws-0.0.35.tgz_1477510164081_0.6128990761935711", "host": "packages-18-east.internal.npmjs.com"}, "typesPublisherContentHash": "9287c4fe77b0e7747169dff1f0adb33649c5c87ef873f4d8b63e29c6e44ddb66"}, "0.0.36": {"name": "@types/ws", "version": "0.0.36", "author": "<PERSON> <https://github.com/loyd>", "license": "MIT", "_id": "@types/ws@0.0.36", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "a6901fc771d32e2bf4a15d79a4757984cf34e0df", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-0.0.36.tgz", "integrity": "sha512-tD0rOhaXKz1vsUt7QE++/aXhMj4GfpaCjI5IWiN84yoOPNmJSNk87o0ULPXC4ueqjBMfAlPcYCjhu0OS/dTUHg==", "signatures": [{"sig": "MEUCIBrjEyvhmCyJpsMwKd35z01yq1t9QQQkvvKWHrpPyx85AiEAnb+3AfjJc938IvoIXRRltc7qiyc7lnMJL1WvN7VpAis=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/ws-0.0.36.tgz_1479152264862_0.9148024627938867", "host": "packages-18-east.internal.npmjs.com"}, "typesPublisherContentHash": "ef71886f5ab2258b2240ce27bca81d07b37a20e1d377759f35350c4b149a9d56"}, "0.0.37": {"name": "@types/ws", "version": "0.0.37", "author": "<PERSON> <https://github.com/loyd>", "license": "MIT", "_id": "@types/ws@0.0.37", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "71d26d7bdc40e75e7d325cf9511648c518ad6fd5", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-0.0.37.tgz", "integrity": "sha512-p//H81zeaJu0ma9edCWTnidx5Rr3bi1OgMX9I3leKTv/YDTj9/GmTQWNwJRfLUxjvxk612Y/vE83lb9bL20jrA==", "signatures": [{"sig": "MEUCIHAbNJ7JD+bSFZz1IOYDs7GxwKE2jA9Z+IyTb77XGM6EAiEAxHQWA+Yyi4Sb/iTTRJz3Sf2qTsRtMRYK5CnPUO/2ARU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/ws-0.0.37.tgz_1479848498767_0.0793333814945072", "host": "packages-12-west.internal.npmjs.com"}, "typesPublisherContentHash": "15c97e8020268968e8c18ffd0d4213fc7158b689ad2e191ce49f267493fcf19a"}, "0.0.38": {"name": "@types/ws", "version": "0.0.38", "author": "<PERSON> <https://github.com/loyd>", "license": "MIT", "_id": "@types/ws@0.0.38", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "42106fff4b422ca956734e29f0d73a6d893194d3", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-0.0.38.tgz", "integrity": "sha512-Wxvrt9EKuayTAw7WSgoPN0GP4Bav2qATvacT7UvTGEaeRanEN5DivfiUvqAKT25afrlaHwkagn/faroAdNHOFw==", "signatures": [{"sig": "MEUCIQDiwgpz6JUI3sKZsH0HtbDeDOZSXF9YVuC5LOQvQDSjegIgeP7TvlAGrX+dz8dj4EvIxj2imT5UObznVsxf6JjhH2Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/ws-0.0.38.tgz_1485138783802_0.5998397797811776", "host": "packages-18-east.internal.npmjs.com"}, "typesPublisherContentHash": "925c01d4816bdf43529c0313361d3093629c25b467665de3617383fdb0578d92"}, "0.0.39": {"name": "@types/ws", "version": "0.0.39", "license": "MIT", "_id": "@types/ws@0.0.39", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>"}], "dist": {"shasum": "d2386c3475eb64e561137a245a4d1d13b1f69fd1", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-0.0.39.tgz", "integrity": "sha512-Eyhoc7KszPWWx9ObCdlxHIG5dd2vAp7tCYZ4LshTHTUY/3i/pHsY5uwajb2FWgoq48J12u07OsUH0KbK2aeVEw==", "signatures": [{"sig": "MEYCIQChS4IfU++qrwlRT636IM6kNe3htuTBkCpwTTEbODrewgIhAODJXQPYzTJuWbrmLXD2jCXbAqWoxu0j6IU54KUBahGG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/ws-0.0.39.tgz_1489004151832_0.8508057778235525", "host": "packages-12-west.internal.npmjs.com"}, "typesPublisherContentHash": "2cc683c0beefddc41341baffe4314fbfb8d239e631a63f1808b5dbf53e347c8a"}, "0.0.40": {"name": "@types/ws", "version": "0.0.40", "license": "MIT", "_id": "@types/ws@0.0.40", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>"}], "dist": {"shasum": "c10c705ab30b51531b76f99a4581544edf09756d", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-0.0.40.tgz", "integrity": "sha512-ZPsy6tOiIktkbB1WP2zszfZLcWRAd5lZPXDMfP1dFNWZFvX91EGoiClKdmko+QBjrRfdPn4YgXaOaXuSZt6RDg==", "signatures": [{"sig": "MEUCIQC20sY6Btj/1FfHKO3qeha1lHJAHD7gVbP77y4ZEJ9KyAIgXZQ4BAHQvK5JYI0a2Drwq+FLDhWVI5I6Sf9RK/HlYyg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/ws-0.0.40.tgz_1492451921097_0.06966845877468586", "host": "packages-12-west.internal.npmjs.com"}, "typesPublisherContentHash": "96d30929d71f4f105c5f703214202049a650e57ec96e19c0596cfb95501bddc5"}, "0.0.41": {"name": "@types/ws", "version": "0.0.41", "license": "MIT", "_id": "@types/ws@0.0.41", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>"}], "dist": {"shasum": "88a7e0cd1605bd6ea773110954671394c690db1a", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-0.0.41.tgz", "integrity": "sha512-Ge7/1PrYn7x9024CjsMAkNckjoP5chk93YWRSEUyz5SvQ0NWDJWbcYI3cKMWBjVlvG1E+9LpkVcOrZYle4Zjsw==", "signatures": [{"sig": "MEUCIQCrw8NHM7tgqUxK3wQ/S1t9TL6MCMDwU8hrxU1gnKI7XQIgREVzUJwufpuf3ysE3cna8Q5MhxiIhstlRKROog2ACwg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/ws-0.0.41.tgz_1493917565386_0.22462696000002325", "host": "packages-12-west.internal.npmjs.com"}, "typesPublisherContentHash": "07dbebd51602b8c14ca336c47f435b4f117bba6853a6d91a8223855b95a1a652"}, "0.0.42": {"name": "@types/ws", "version": "0.0.42", "license": "MIT", "_id": "@types/ws@0.0.42", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>"}], "dist": {"shasum": "2f46a7c736296140fa4b732ffffeba44599e2344", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-0.0.42.tgz", "integrity": "sha512-+30f9gcx24GZRD9EqqiQM+I5pRf/MJiJoEqp2X62QRwfEjdqyn9mPmjxZAEXBUVunWotE5qkadIPqf2MMcDYNw==", "signatures": [{"sig": "MEYCIQCvcN8UyEUymjnkVOslFPdaq8BzI50OO+pAvtwqQvq24AIhAL6fCgCaDlSjMuT/DXOEs+ew8EPh+kqzbWMflXoQi89J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/ws-0.0.42.tgz_1496411800239_0.4697836877312511", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "c0b0a7d7df2c9cd7185c48650668d5a18b4d7f11b090e0918aa4ee7a164665ac"}, "3.0.0": {"name": "@types/ws", "version": "3.0.0", "license": "MIT", "_id": "@types/ws@3.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>"}, {"url": "https://github.com/elithrar", "name": "<PERSON>"}], "dist": {"shasum": "4682a04d385484e73f7d8275cabc8b672d66143c", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-3.0.0.tgz", "integrity": "sha512-izHR5cQvRE6nG828CkNd3pOCIjwZ/poOhxeeLyBWyRoXBDZ2qSA7iEHwp7XFnq7seJk9APpm8iJBau4fAhDY0Q==", "signatures": [{"sig": "MEQCIEccieBRiZIoV2zO+wPVYUOqK4fNGU02YW5NPWM4ZNMUAiAwJcKdQcOGG11uelyoioFOjzYNuaekTaduHl0uJ6AyuQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/ws-3.0.0.tgz_1496418034178_0.36788008897565305", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "1f3fe367af89f04a00da4f9e526be6ef456cf69c76b389547f43bb7dd62bf03b"}, "3.0.1": {"name": "@types/ws", "version": "3.0.1", "license": "MIT", "_id": "@types/ws@3.0.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>"}, {"url": "https://github.com/elithrar", "name": "<PERSON>"}], "dist": {"shasum": "64fbca93525768645a219eb424da59254788306a", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-3.0.1.tgz", "integrity": "sha512-cfBmtvCOil/OIeNU+qB0cfKaVcVqnnJQDSy1jwBltV76mWIxtieITsvKEyIC1bYn4rx/M8q0Pn9qa52sMbtRGg==", "signatures": [{"sig": "MEUCIQCLLL/V2wGcnkL3F0ntIPLDq00mbU4KCfabfAzr8q1o/wIgdobblWetPYmwNbtDODhZA1pZ0nALvBIcibz54pSmRJQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/ws-3.0.1.tgz_1499349935861_0.7713993627112359", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "8b097cc9bd92c921da3ce93bdc63879e313678895b5e6473bb86414194d7a463"}, "3.0.2": {"name": "@types/ws", "version": "3.0.2", "license": "MIT", "_id": "@types/ws@3.0.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>"}, {"url": "https://github.com/elithrar", "name": "<PERSON>"}], "dist": {"shasum": "b538b6a16daee454ac04054991271f3da38772de", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-3.0.2.tgz", "integrity": "sha512-jW<PERSON>+P8pn6lBdKoi+8cxBdLbRwwiiBdp8Jqgr/9e6A33fRNunM/C3ACGU/9Ksr3rHV6C2KzhncNGwgCFwGTgX/g==", "signatures": [{"sig": "MEUCIGSyaIPvKcJxJEjfpGx+p315KnVd9SZKX260bZLb/t7+AiEA4n3GEzjH8HLtdzvGxQeZJc8RfaiRUxVFzHkALupQo9s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/ws-3.0.2.tgz_1499716053538_0.6266499448101968", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "f999096b92a8e9ec7212a0aabfd3223b87125210631e16fe3bd608095b5d227c"}, "3.2.0": {"name": "@types/ws", "version": "3.2.0", "license": "MIT", "_id": "@types/ws@3.2.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/elithrar", "name": "<PERSON>", "githubUsername": "elith<PERSON>"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}], "dist": {"shasum": "988ff690e6ed10068a86aa0e9f842d0a03c09e21", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-3.2.0.tgz", "integrity": "sha512-XehU2SdII5wu7EUV1bAwCoTDZYZCCU7Es7gbHtJjGXq6Bs2AI4HuJ//wvPrVuuYwkkZseQzDUxsZF8Urnb3I1A==", "signatures": [{"sig": "MEUCIErA7FsRCGTxggvc5Ug0aYU91DHmTG2jFqL9QmnxT9anAiEAlAyTJclhD1k/nRnq/T3TxAJk7A/1rgi5SH6CKU73ipo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/ws-3.2.0.tgz_1507055315848_0.4845333220437169", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "aa8e2aaf4bb29a408e0eee5edf2ae7d5d74b4b3ec94aaa735f6db4905eb8a760"}, "3.2.1": {"name": "@types/ws", "version": "3.2.1", "license": "MIT", "_id": "@types/ws@3.2.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/elithrar", "name": "<PERSON>", "githubUsername": "elith<PERSON>"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}], "dist": {"shasum": "b0c1579e58e686f83ce0a97bb9463d29705827fb", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-3.2.1.tgz", "integrity": "sha512-t5n0/iHoavnX1MqeYmKJgWc1W6yX4BXsNxQg7M5862RWrfN9S5k8yaWbDMGJSTCzbH7+q5QS8chjymd+ND9gMw==", "signatures": [{"sig": "MEUCIHvpAdgqoH1N8AfW+04g8FIcaSLAr2PTu4sSZMjbmXqZAiEA6bE5DXEVAvSttrq/LOqn/4KSAItEPj3s5PremJsLsD8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/ws-3.2.1.tgz_1512078395605_0.5844513804186136", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "29ef5d867a8940f9bf875a1ed9ed40b96c307c2b9497f6febed4398ca40287b8"}, "4.0.0": {"name": "@types/ws", "version": "4.0.0", "license": "MIT", "_id": "@types/ws@4.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/elithrar", "name": "<PERSON>", "githubUsername": "elith<PERSON>"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}], "dist": {"shasum": "4b1c170b00f7d880c8e3267ceaf147189234a9ed", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-4.0.0.tgz", "integrity": "sha512-c+D/EJQ7yFGjEdY9/1nPTpeMjtm9P27a2nKeGF8YpYegvLH68AjxSYnI74CfQaz8dSiDdw8owL3j2w7RjEpZnQ==", "signatures": [{"sig": "MEUCICEwgOEdc48ic2DSkQ6EMQjq9pfrhuW7Dj7PahHdWnY7AiEAnUUW/gXXWVBsdVAWPqrmnbgaoBHPz8azD0vjz5mjK+w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*", "@types/events": "*"}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/ws-4.0.0.tgz_1515783172621_0.2353502786718309", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "dcf842dc3ab2a896b721a1f5e6eaff590564a9fd8845d036498c2911ea0d4a6b"}, "4.0.1": {"name": "@types/ws", "version": "4.0.1", "license": "MIT", "_id": "@types/ws@4.0.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/elithrar", "name": "<PERSON>", "githubUsername": "elith<PERSON>"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}], "dist": {"shasum": "3309d4d02a1ea9cf617d638b9239a2e1e28ef21e", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-4.0.1.tgz", "fileCount": 4, "integrity": "sha512-J56Wn8j7ovzmlrkUSPXnVRH+YXUCGoVokiB49QIjz+yq0234guOrBvF/HHrqrJjnY4p5oq+q6xAxT/7An6SeWQ==", "signatures": [{"sig": "MEUCIQCuxoeSwVhKQMpklMzmG2Zngx+ZSJIBGmX2Hog86G/w1wIgOxuLE1S/11ALyrKoTIVmy6TLC1VI2voH0spm5BaiI/k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11835}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*", "@types/events": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/ws_4.0.1_1518490480012_0.28919895009602326", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "6570a37fea4c6f9f4d0592253926e8be0b4e436e647f8eb7cb9f3fd14bed9bb0"}, "4.0.2": {"name": "@types/ws", "version": "4.0.2", "license": "MIT", "_id": "@types/ws@4.0.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/elithrar", "name": "<PERSON>", "githubUsername": "elith<PERSON>"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/orblazer", "name": "Orblazer", "githubUsername": "orblazer"}], "dist": {"shasum": "b29037627dd7ba31ec49a4f1584840422efb856f", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-4.0.2.tgz", "fileCount": 4, "integrity": "sha512-tlDVFHCcJdNqYgjGNDPDCo4tNqhFMymIAdJCcykFbdhYr4X6vD7IlMxY0t3/k6Pfup68YNkMTpRfLKTRuKDmnQ==", "signatures": [{"sig": "MEUCIDQTPFSEd7mPIsfWS1AGLLw15RtXtISK+jWJga+BaHzGAiEA2Mp8ohU1ts783l1lbuZnyBUp6uO78vqkReBOmlc0Jj4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12514}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*", "@types/events": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/ws_4.0.2_1522259223141_0.7465296858275186", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "b35a345f45e2615d5973b9fe5717b79e0c973a6500c66455b16dce72838bea90"}, "5.1.0": {"name": "@types/ws", "version": "5.1.0", "license": "MIT", "_id": "@types/ws@5.1.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/elithrar", "name": "<PERSON>", "githubUsername": "elith<PERSON>"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/orblazer", "name": "Orblazer", "githubUsername": "orblazer"}], "dist": {"shasum": "a6382c1ad2dc3a68d0650fbb4ce214ca78f2e5bf", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-5.1.0.tgz", "fileCount": 4, "integrity": "sha512-iT73Caj1QRK48+jOicM/aN+tNAFRWSeLZKkdbchctxKQU5ozzx141BnzVm1E6aIA9abBa1yWYvfdZdnC/smdPQ==", "signatures": [{"sig": "MEQCIB9wuxbDAU4G8h55NNpTHle3bZ2KPNB4AALjz6LjDBeXAiACt88NWmAvN9CRYkRF6e6rvfBYZBYA+/oZYF2siMLYFw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12586, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa6yxuCRA9TVsSAnZWagAAumsP+QDNXqVw/IOd+7gI6ULN\nG+iJXr6FM72fwMC65glOWgyXgrM8d3pcS9F/v8tHHmI1iWla+xa620i8fMHb\ngcqM/YMtkaZNyFmnJV7OJXXCuOfD4OILFfYduHDk+TngmOkz81R54QaR8OWN\nGmClk9VNG14Vi5xWnu68hQppFiRwmSxWcCTrcBtg4Dqa91uS/Phljz4ag3lF\n9Dck5hYJFzitsN2Lv57uIXy3z4rakuY+IkrGdXf6u16uyHby3SA3uO84u+cM\nkp9MZdP6l2EdOoUDFZ9ymge/kwYd1PoNoMlFPsb8iBT3qHJufN4gIGhCVKzB\nERQLVo63gHGjW665C68hqtCm6xySgKXioVJ8NK0CZZSd3iPTrLk9wegHOIkU\nnw2fBpNW08P3QQ2aLvRcRNM1t6dOJQuIWTEjx9r+MiiO///Ebe7xY7TviEsW\nGbKkPw1ALBLqspvOljdkBt5FtoQ7r0ImeGrSHAxGSO2eIAtzl42e2ZeOUSvk\nF3cHzkx8CBiQTzRET+9gET2dhRAtZbgr0tUQRvGUAlHkN7CuAJT4OIqKlZC4\ngF3gSHVJ4wi5iDdxy5PS9R0CD7gakb2xMkRHoeeK8v4zcibCHrg5vGnvltKD\nMBV9d2PwyrtOKlNrsabpwaINDdRG1oc/W7/Zyjmiqa2dW3QPl2ULHtwIlvPw\n5qfs\r\n=xnBR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*", "@types/events": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/ws_5.1.0_1525361773566_0.3047908816192697", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ddf4c88f12d9f4ace375a2ae3baf95b848230237e0dab5e79a2a2e53290b28b8"}, "5.1.1": {"name": "@types/ws", "version": "5.1.1", "license": "MIT", "_id": "@types/ws@5.1.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/elithrar", "name": "<PERSON>", "githubUsername": "elith<PERSON>"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/orblazer", "name": "Orblazer", "githubUsername": "orblazer"}], "dist": {"shasum": "4adc1f1a5e92e7e0e95658fb35c7eab1bc52f4ac", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-5.1.1.tgz", "fileCount": 4, "integrity": "sha512-zf12eIggrJKYkOXood4EbQOsMFaVR8e5W8azkesRH1yFg4GhocS7j4sqE+FesLbvf1FLws4HenWUG6YZQ/LTJQ==", "signatures": [{"sig": "MEYCIQCPpBsl1XzHBucWnR3sCEL2lDpKxa5zCc4GOA5hrwzd1AIhAITKlBlanPfddehWPrEagg+f7OXFL/AR55Bs7kcA3Tf1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12339, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8iIBCRA9TVsSAnZWagAA3AkQAIPP8Z6fgKAShFWHzPaj\nwTSa3x0+hJl/QG4lbo/DJm4X+fribGE6kwlkkcnO5sqN4dwwa6aYUG4QMFF6\nXRrsFfAHF3oVO+hGn3gwXmyhwJoYEMhVYMrCvPL93SV2/m80W699bEFgvgnr\naQAz2ioxPBT6LJtZg6wIB28qvnW+lkJc1EDh5eWnmZuX+mIC9NwrPbXOSYwJ\nZi4Q+Jcdm/PIbUethtgQwhmkO0pJOaDgjfRQU1DcY8H0dSZc3FbtJpeINJAp\nFLF7PuEBCP/9avrO5Xv9Gt6qk01RwaPkz+B6w0FQKM+LGN7utqANc1nTlOB0\nLZDFvPgI12ZN89cOjLTyOd0rESoJUB58ZP/7zyLQCelXnr03qwqwmvyNj6Hw\nhsaTTgOnwRBBnk2GLD4ulQ8e5sVcZuQwnpQUtVs3h0qFjgxG3+Ka8Jb3HT1L\nHUocLmpXJSZwTP3U6mID74+MLKdFmbZPJkqaNgR31+12qqLRnItZ0sM0uqEB\nkwGibLfpYbBh8fTQa3L9iurs56O3GB7LugR8NiKwGtI+tbEHOboKUpdjkpZk\nnD1Vkr6PX63jsuxZ+BUWc9D9QcoS8ei7ZR5GJLLaZ9Yv6MPXlq/5CVnyWp6k\nBv59yWDIkGxjEe13+4ORwNpd1pLNu1cmVWd3xaEuG/BLYvJWIwSJrCWIj2hl\noBo2\r\n=qaMy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git.git", "type": "git"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*", "@types/events": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/ws_5.1.1_1525817856790_0.5951836025395159", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "f4b5cf19b760d7f7ca8d7f554461d9cc93c7feac8665e698958d759ccd4867da"}, "5.1.2": {"name": "@types/ws", "version": "5.1.2", "license": "MIT", "_id": "@types/ws@5.1.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/elithrar", "name": "<PERSON>", "githubUsername": "elith<PERSON>"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/orblazer", "name": "Orblazer", "githubUsername": "orblazer"}], "dist": {"shasum": "f02d3b1cd46db7686734f3ce83bdf46c49decd64", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-5.1.2.tgz", "fileCount": 4, "integrity": "sha512-NkTXUKTYdXdnPE2aUUbGOXE1XfMK527SCvU/9bj86kyFF6kZ9ZnOQ3mK5jADn98Y2vEUD/7wKDgZa7Qst2wYOg==", "signatures": [{"sig": "MEYCIQDBpEcWc6SSOUXnLxMS042EeOgBSIHULgKZ8uYSxXY+PgIhANBBOr/PKCE635ujxzqTR6X0RIFCm37KCYwGveSY1zeW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12433, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbGZwpCRA9TVsSAnZWagAAnU8P/1pvFaIbSJlHbQQihpKr\nwvoVXN9MEj5Qj84IW7VuZqwF/7Lve/LG+geLcsQPcEMuXjjkHb+8vqwQN2dO\nvkF4Wjh2jlM1KGLAAHVpZTjuAGTu6pcwSkGfPi6g8byIWqjGve/LGEmN3xR8\nr2/JamqcBUaLM8l2+xIK9J3kG1AS71Isozffr2QNjn7niRFfZftOx0wS1Jg4\ndqy/M1oXZB9coX2OKF4DXxqVF59jFuok6Cfj7DFzz3U+Z6MfZLoB77nLODK1\nqbL52c3rDjNf0s9E8Ld3bD3kHKRyrz6y1QNedHa/WkmEIjHyVlc57c5DTBO2\ngfr1WkvxlF21r+hIBQVD7V+hW+4EwskIPcJdDJNIgK0dGxPSJl6fVhDc6oGf\n4WKU5VyYQVUaDNRm4385hyBw9gKlr8LGV0ZpZDWFi1Ii5er5lEZddUTZjh8Q\noqGS2p1i2k1BI2SZosGBOPeCD691nbXw6/THXViaUAzW+wSYIqUmSHTW2Ufh\nZLV2UU5EXUN9GRll5ZvwbjicbBCiMBKOgbpJ7a5ZU9Rcm6ocQjggtxv00+RE\npux0DnqjREuUqWcb5YsFyH4+RlGIpuLJmSJ1kKc2woNH9pnn3y/EPUxSCUXg\nHxC6vACXyEsYJ9uvDW/zIzznspstnwBwjZSzTBTfTHXzZn7VRBpXSs9PaVux\nSODh\r\n=p0cl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*", "@types/events": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/ws_5.1.2_1528405032480_0.46646837100605865", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "8b5bb212f4589181238778c47f30acac73104ec397b8ff04f03b571834fa673b"}, "6.0.0": {"name": "@types/ws", "version": "6.0.0", "license": "MIT", "_id": "@types/ws@6.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/elithrar", "name": "<PERSON>", "githubUsername": "elith<PERSON>"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/orblazer", "name": "Orblazer", "githubUsername": "orblazer"}], "dist": {"shasum": "4787c4194c29cc6361208dcf4e580e208794e012", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-6.0.0.tgz", "fileCount": 4, "integrity": "sha512-i/dVaSjmTM92EFFFhmGL6AmHzvJ70XpAXmMLvNKh3JrRTGOiXvejfxe5+OSxcJK0paGOYHDaRLS8nXW6/FxSxg==", "signatures": [{"sig": "MEQCIG6/BzMjMtmmYt1ktTGqnPQgb96VtBGL8bgGLuR2dIV4AiAEReZ51DogvAoJMB3grbeXYq99Jgo4ynxDTKGp67trTw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12789, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbciiYCRA9TVsSAnZWagAAfxYP/it9TB6fOFS8p5flpsHz\nSu5eqpJEm6uwEumUdblicpeNKc5cPLnFH/9uXXG/TmzBwH6zDG4erIVK2Qar\nVBpozc5GFNHndzMT1qVmXvYR4xXpaOphQ96BFiAqRqJuwKuqdCuzF0gnwPEI\ndNuind+TAltQDHFE2KwaOzjDeq61WkXFvuxW364D8TK0M0gs3UvlWLyhfQRA\nynQq6CPbTRMcDx+JGOFczIJ1ljNZBkdF3F2bRz36BwGgrPuuVZTPLxfUdgLI\n5zvfh2VztXoRB1swpbfCdXbdksZsykvfT8Rxt1A9Gz/qKnGvUpev5/tw98p+\nq0+lht8b3YNlCz4IHWEpNhSMjrXMU74A5+9rga/2u+RlT6LrCu02gS0dbMsG\nPtJ5/sBZI8lu+UaUBUKyxX6heoRRcfCd5zyc5ua2LDqcImPnPNrHyUiSJaP4\nZVyOmk8INZXyXqLe/G/DLf2ImnuIHphWfPdILe++R8JIjAPcw/Tz9fJsyfjh\nMOFLM5btbsSk+ubjFjJr98wKyuROWymhSlA1mXNNG/eAy8mLyEvpOGKkzlgK\nOBZtSZVzcxgUVuHqTvIbDiPiXQtJCXA1Fi18NjRioP+3iyjrcwhbfX8mpNji\ndF5mNZnt5hoBd9LVP4kikFptBgrA96OiD5+T8CbKFXoCsMxejec+GZVKo/rr\nzjN0\r\n=d36z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*", "@types/events": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/ws_6.0.0_1534208152055_0.15652091372279076", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "485cc819a3e474f23677a0b60a530981b5ac1c9abcabfe606a81dfa97d5713e6"}, "6.0.1": {"name": "@types/ws", "version": "6.0.1", "license": "MIT", "_id": "@types/ws@6.0.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/elithrar", "name": "<PERSON>", "githubUsername": "elith<PERSON>"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/orblazer", "name": "Orblazer", "githubUsername": "orblazer"}], "dist": {"shasum": "ca7a3f3756aa12f62a0a62145ed14c6db25d5a28", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-6.0.1.tgz", "fileCount": 4, "integrity": "sha512-EzH8k1gyZ4xih/MaZTXwT2xOkPiIMSrhQ9b8wrlX88L0T02eYsddatQlwVFlEPyEqV0ChpdpNnE51QPH6NVT4Q==", "signatures": [{"sig": "MEUCIBktL5ebpjXhDP3k1eW/vfudTJqMgfe1phnANssWmQAiAiEAlixhRa5kFC+kO2f5OsHFIoSOu0g8I2/mN3b3YdhDo3s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12791, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbkHHFCRA9TVsSAnZWagAASK0P+QEndh0XuuXvM8qP0nFA\nCO+Rz/ER4GBlZx7jEjuE/RJ5xW0F51vJYqZcjXFm1/xmyN7AHcJct4zBYBhf\nJFY0uRAqyuKr3UNhAq7uJING3o9FxL8SZXC04hqiRH8VzQqWJcT36GGgpOyx\nBGUoKIS0KIvNbe0f1KCr7UENVsBeKb/fF4VxfRhp+xn0N+gfIgFmqoPx/rYy\nu3KPoepmq1qrYNjBY1vpSA6RNa4n2aPc2Mab1qU62kmDdxHh7afhJt+E0s0n\nzs4ZOLVfnqlnmVEnlQJn6NYn7Mu2hhsr7GG/7gzWAJPOeqJe8TBpawRXlkr0\n6B9RPJOsb024W+tGCfGlhpC/jg3YCeKFLGQ4Net5mJEIT/IYXcI09sbnm0bz\n+105SC+hLzFUWcljzH5EddHlUw9aFmyKYhy5Tq140uBBK67nhhM9U5N6IYR/\njPbvuZO6eFZvtK1bh7QLlfwYiiUNuBOhpb61Q7SRmFwAmyqP0qx+a7WoeFKQ\nrTLBSv4m74GLbozZpDADhitwoS9mZutQmkiBTvOnZJkUrW0ZHSV1VmcbqVNF\novzCvK3p+xdyuJ8mIY1IjUrYV6G8L0txiH1G8avlYh87ZspoR7GXUnYyGu/Q\nLMGdqqgABgWhYnFVLrkJeFT4C7Sr645NEnKh7Zem2diG7/U1zEeSh390QqJm\nIm0k\r\n=ztjB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*", "@types/events": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/ws_6.0.1_1536192965134_0.30047290375612223", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "4ecfceef5e69e2fe7cddd05fa2ab63b16c0e5f742ac0d97a6682417ecaa46675"}, "6.0.2": {"name": "@types/ws", "version": "6.0.2", "license": "MIT", "_id": "@types/ws@6.0.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/elithrar", "name": "<PERSON>", "githubUsername": "elith<PERSON>"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/orblazer", "name": "Orblazer", "githubUsername": "orblazer"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}], "dist": {"shasum": "f3340f7e3d7a07104a5dbcaa8ada4e8d2d45eecb", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-6.0.2.tgz", "fileCount": 4, "integrity": "sha512-22XiR1ox9LftTaAtn/c5JCninwc7moaqbkJfaDUb7PkaUitcf5vbTZHdq9dxSMviCm9C3W85rzB8e6yNR70apQ==", "signatures": [{"sig": "MEUCIHg3/jsO+3vRsWNJPm5yEU7461Ujx50qKb0diGnx77KMAiEAztVo3Uc7L1TbWerswLTMx25Po2+0LxNb59JhZwKSfrI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13368, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdSG50CRA9TVsSAnZWagAAC2kP/ixce+bqnwcypDLMoh7U\nvolhAlf0Z+iiVdwja0OgoS8vPp2G4xHhat3fGeK65Q3Xbnf0N8XZQ8q1wfGM\nNQko/ZCPJeVLXtuq1fZtUzAIhri78NtPfJyWOYViSM4mAomCvo+59cwmP6Ak\n2nPXrXAz7vn7+dbWh083gRU6SEhvC7xehHLrYmTRKijDlKdqY+L6gxhUSeAv\nzk5EHejLv3TG3BUQx6zkMzlf8UFVZFbomIz1/7b+pGYHJ+Msf9gmiBORTpzN\nt6rm8CUW7OqJttjHlNG0zJh/I7SEIyhuFNnxUAFjZR9Ko6ZWcRyMrWHYqIHG\nJGW0TBSGxoa6mDpoeDo7GKWWVOfnIloy1eAaDmh9dlWREY6+gupHkDNpVV8P\nWtRoR5MrCRHNd7T4+S7MEXy2io6/6Oo1P6tmYODTbehhiL2eMCySkOCnoldz\n2kvS+t10U1dQi97RwtDHFML+5sUHaWAvapkEujujtM4eMKxAcMhtKeibleKv\ndqv8j74aUHHcIQGXlS1QwS2WGbte5zD+3Njb/RRHM3Z/GYJ7Lq7dIeE2f5kw\nKrrwpIKIolH2l4mJ3l3cgbwz/jfWeHx2eYhrkUu6fmn7C4UFa8y0rk1XUslg\nVX/H8CTL7zIskPpXcLBh/UbQ1isfxLMjAFir7tt813pErUQAejW1tgjIQ0IO\nFdc7\r\n=7JEI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/ws_6.0.2_1565027955210_0.4977243599107455", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "45dc4f6c8721640c2265b102200cf89ac49f19dc527b12f76e399ae07b053f34"}, "6.0.3": {"name": "@types/ws", "version": "6.0.3", "license": "MIT", "_id": "@types/ws@6.0.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/elithrar", "name": "<PERSON>", "githubUsername": "elith<PERSON>"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/orblazer", "name": "Orblazer", "githubUsername": "orblazer"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}], "dist": {"shasum": "b772375ba59d79066561c8d87500144d674ba6b3", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-6.0.3.tgz", "fileCount": 4, "integrity": "sha512-yBTM0P05Tx9iXGq00BbJPo37ox68R5vaGTXivs6RGh/BQ6QP5zqZDGWdAO6JbRE/iR1l80xeGAwCQS2nMV9S/w==", "signatures": [{"sig": "MEUCIQCHwT805F/Q2i5fWBCw88osX6paRDzdFbcC0xnJunc8rgIgOQ2S853SAkxNKI7xArRE9JIa8eMRy3GB6zzYrIWJTZg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13445, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdYCBaCRA9TVsSAnZWagAAkHIP/3lsY6Mvd7ICUfRqjlIW\nKpgSygVcRH9eKlQvteXY3VVZQsZG3ncyIz9Lm75pH0nlwByHNu7UuqYWWFIE\nlwjVq9XXSwanMsNmNWDC1IwYzqehMDceY61N+y9ZVTPAOEI1XjinwKybD4Sd\nbnEO+OqyBeBa5rXEHbpsPeWrZJ6r8cc29HGiN4QfRrG7DuKxC8EZYFKvDeCI\nm0SDcUXG6g/Pp/9YSrQyp/INwXR1qCPq5m1xTHzAGrwvnqqc2uQt0UK37snF\nSTVSSh6IXDVhbmM8jZCFpnOtV6173I0FAu6jNZjWe8+jCI+/eo2eUir6hRrU\nKpN4JR/3Tg4jD3JgUtcuInUP6QnLCD9B8HwBjsdv4K9U8AXI+2XEgUqrg4ZO\nXE9qQooIoiP0CfsJtkgLcsE+ElEwZQpyuB8vzmhdQKrQoL/crRgoPumS//Z4\nLnWpcL62AskMlqLdvvoNcsrg91yG3NKibFwGvdXgHrFEoH0Ahf6x6SRqlswZ\nNKWuyGvcD7Xx6a1Ene1j9udV+ZBGyzCXbWUJjI6QrtBSGADyoslu+6Bk81qd\nrrcHj0m9fyZ8hTlS85mN7JGMAlaCJvPFtSEV1pvIsMeJBYu4KwR1q2P2zZ2O\nIL85fIBl6BdPRc+yHiXt/bSCwHo59bRHdGP81Sd6FVnKlwb19TWTaGgLRtN/\nyIeQ\r\n=s7Kg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/ws_6.0.3_1566580825989_0.1870543787170844", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "14ae4ed25682765570a159d21524779bb978a8a51bc61ffd23a68b175a0cd396"}, "6.0.4": {"name": "@types/ws", "version": "6.0.4", "license": "MIT", "_id": "@types/ws@6.0.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/elithrar", "name": "<PERSON>", "githubUsername": "elith<PERSON>"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/orblazer", "name": "Orblazer", "githubUsername": "orblazer"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}], "dist": {"shasum": "7797707c8acce8f76d8c34b370d4645b70421ff1", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-6.0.4.tgz", "fileCount": 4, "integrity": "sha512-PpPrX7SZW9re6+Ha8ojZG4Se8AZXgf0GK6zmfqEuCsY49LFDNXO3SByp44X3dFEqtB73lkCDAdUazhAjVPiNwg==", "signatures": [{"sig": "MEYCIQDv0jxBrOoLivXlCPLEZ9CTxdq3F0yJnIJ+ERP0jHkQuQIhAIjrglL0kfp19c3m7Bp7vYnYk2f1Wlf4U7OZVdC76rly", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13544, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd3FVtCRA9TVsSAnZWagAA6ooP/0M3grXAQ7hmb38tnzIe\nVPV5uUhijbPIFgUynTohi0mBjo/LDc8NzM1S1510plknGbVcZwjg7zifFC5o\nEVyDnu1hmdtmkJcM98uhN917FfpcwUkFxQeEsJbHRviXEfAxHVH7AC9oK54y\nbvYmmJiXAVU3GFcEG0nyZikbN16fG0U3A5TarY1WlhkE+jNsOopADY/7gwGv\nVNVsdguHfSkhsAfBrNR94ixVqb5KXNX0Ak8mG/ZVhwpKd98+ZLQyW1oKT7OF\nRJ2w4QLyuZIz2YxPSscmQ0HJwwbni3Ol/mQC/TEbXUltrfUuLi22aowpP4Ko\nl9haYmRyJnvnItibxKfwVBNP9nv3DGbFfacgRc+5fwG/fOdJTOWiOH3vWbc9\n9UKuHDxXky49H0ubmGUFd0qA08hd44Dyk/W5hIV8N2B/CoBmfp7IP12NwHo0\nl5M4TXm0PFhr6GJR72oXtIAFEaJjDGYUfYf51RTfHmDn9zwpUojxx8a7AidD\no44ihiMFcfGLWNUBPH06BHgcqOiIeK3Ekj8CyeTQwA3BZtW0Y2YhvrIfL2BG\nf3NtbqhQhLUsjuxzz18zOyVPS4dxsdszcJvNmZAK/veL8WCjovdPcBI5AbXj\nzpzGnp77dkpld99qtvui64viPFgMpcOdo85OFG9fby0kgD2qyApmmmSrPt+2\nnVih\r\n=N1rH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/ws_6.0.4_1574720877231_0.5194367692521757", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "fe96747b9bb4f09959c055797757b71397d998345c0c940d8e5e67a5b1d23c68"}, "7.2.0": {"name": "@types/ws", "version": "7.2.0", "license": "MIT", "_id": "@types/ws@7.2.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/elithrar", "name": "<PERSON>", "githubUsername": "elith<PERSON>"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/orblazer", "name": "Orblazer", "githubUsername": "orblazer"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}, {"url": "https://github.com/teidesu", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "ed94695be01a77efd590244fc17c3b730e75d88a", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-7.2.0.tgz", "fileCount": 4, "integrity": "sha512-HnqczxiZ828df9FUh9OyY7vSOelpQNaj+SLEnDvU74rYijp61ggV7dhmDlMky0oYXKLdVuIG4KvExk8DEqzJgQ==", "signatures": [{"sig": "MEUCIFKmuF1tWIS6vSUzdY+ad8NO1RO0OSSeQwI+GrTpSgbpAiEAsCzGOl5LD3IxVrru4kFuCtYaLRTWd+umjr9mhP6e1lA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14008, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHtX2CRA9TVsSAnZWagAA5gwP/3jSw4OBRbDAZlEAWbNs\nrkkyiJqlCv1FAJG4OkP1aGxdMNuMLOO5MW+mYI32B27rhXFr4YvGTqU1RchQ\nZt1gLhpioNGGHrkMIvlx69VCQKoi/G2fmHWvN6eGEA92LKn0sSRqqtExoMsh\nfoH/GdpwnF+kRufcdP7sVJ9mllZaEOdITMaCh0c7aSZ0VUZchClr1TOR9rTJ\nbVzCaXRgLnHbh782ieicKQN0cSHn2yDjgFTn1RGhzBDoEAfsSqeKfaMQ5UmZ\nrMmSlY8e7TeOn+46ToEhF26/5TlDViDDOvJfYL4h8zCciIzz4LI8y3tlb2km\nowhBxeXcCt/qXGGc3EfMo7DhAN/mKRH2P1/g5RJzDEEKACEiqPpnfgOkDHC3\nH440pFKbA7fHeFsbBxA8yrX+dZ0LO1MXkBZbkQhTpI0YKxD8EDsaL5OjsszC\nORNXBR8/7b8GsQ/+CiM5ivireXM6uWtecr1aAUNCOpT/sCHI+epP3bt055yN\n7WRcPjuVa5cpjxvC5kIRwG7T5dnU7PQ3/UJ+99VA4pVBdipvVqwnERmahFVo\nep7NqqGtt8VpcKzwGVLTYyvbuM+y2Bj3tC5aFFQ4MKSsjT2jGSKkVRAi3+5W\nW7npMG2OjtJRpDa4YGxYruWSbEABxYhT3JDG2+rAhGf80HdZc3yYXR9AU0Q8\ndPsw\r\n=m5iF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/ws_7.2.0_1579079157559_0.9529762269496311", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "8d3a85349cc6b626d4b6c2133ccebdd76a7ee17112f2b6c115190dbfd00bfc7a"}, "7.2.1": {"name": "@types/ws", "version": "7.2.1", "license": "MIT", "_id": "@types/ws@7.2.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/elithrar", "name": "<PERSON>", "githubUsername": "elith<PERSON>"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/orblazer", "name": "Orblazer", "githubUsername": "orblazer"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}, {"url": "https://github.com/teidesu", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "b800f2b8aee694e2b581113643e20d79dd3b8556", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-7.2.1.tgz", "fileCount": 4, "integrity": "sha512-UEmRNbXFGvfs/sLncf01GuVv6U1mZP3Df0iXWx4kUlikJxbFyFADp95mDn1XDTE2mXpzzoHcKlfFcbytLq4vaA==", "signatures": [{"sig": "MEUCIQD7k/4Hqus5WxXWcXTZmAgnXChFeZgYexaogw1ZG4Ek7QIgM1Eq8YBSF5EQ621zt7uSrJWFZe8Nga4PeLqba/lQIJQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14013, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeMgH/CRA9TVsSAnZWagAA2awQAISoS5YE2x9Ciq1GBP4R\na3bF3zlu1f3+cLUXIWt6HlbmjsbkYcH+QJwTysMrcqC2NUZNl+pQp1LinaPG\nmgo3Baf7X6r0D8q0uCJ0UEI+uLup/d/JUt2HDcdOLe+OrErENI+c15udKKqn\nF7pxVmBXr2Zok3u8/0K8g/iRe5rQwH76Ju0ei2sUF7rWh/DljF+cHB/kbXOu\nZ9qi4hnVW7OaGrHxNPTf0mO/u1gRxFyQ6VhSOky+Fo6R42eAX1zdngok3qjh\nh0FcxjQF0Q/nNVCytQqEpSZfpb2dE7GN8NKpN9M3zFtaKBEcz5QVCbshq9J4\nlopCFJNngX5Tor117VksJknDSzcr75gR5LFW7k3LgKDZtfNRBcQsbjgbC7nA\nFopOVRrpIbyRYxVDe83iaJEBN3oe6widHsIw+ARpe3m8c+OaL3rGV1hcBgHS\nnpPVMs7WG6v96m307GSVf+wMxBnmgUdA/+l2K1LN3V1jp3KEp6kXtgB+JzQ+\nnxh6DkTzHynHuEonPy7T5l7KKdzpRblM+BfzzuthM7Djqvwb2W548aINU1ff\n39lzPSKcAtENjJ3JG0gpNKha4E03Wv6D5DjGs8g7pc8W+/SpcHU/ciBDjXi6\n2I5uTct0dj+wDJxunMVxbngeIi8N1nfvxE3LfdvUxBcs8aLgZNhbaej+5AQM\nymKP\r\n=ymrE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/ws_7.2.1_1580335614750_0.7825567292768061", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "e435141b407cf4f42311dfebad78215a97fe4cd33eb6df8abf26f9c6cbdfb63e"}, "7.2.2": {"name": "@types/ws", "version": "7.2.2", "license": "MIT", "_id": "@types/ws@7.2.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/elithrar", "name": "<PERSON>", "githubUsername": "elith<PERSON>"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/orblazer", "name": "Orblazer", "githubUsername": "orblazer"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}, {"url": "https://github.com/teidesu", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/wojtkowiak", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "1bd2038bc80aea60f8a20b2dcf08602a72e65063", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-7.2.2.tgz", "fileCount": 4, "integrity": "sha512-oqnI3DbGCVI9zJ/WHdFo3CUE8jQ8CVQDUIKaDtlTcNeT4zs6UCg9Gvk5QrFx2QPkRszpM6yc8o0p4aGjCsTi+w==", "signatures": [{"sig": "MEUCIAQzcL8wSXjXLFfphx1B6eCAblHiuT80xl3mqKYKE0g1AiEA0W0f7Ehjz+/NXPdYGnrkZfKfBpJk4Ssvl3AeU60/Ldk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14351, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeVXJZCRA9TVsSAnZWagAANcQP/3c0hPsNPsa1TLBeVe1I\nlZdGxBSRi8ItV7kBgiLgIUt5mBlIKh4OnhxWRUf+j1CaTxqh23+a7K7+vD4w\nIwEFcqYvVgoN96Bphza7hyqH4ZqUAS1Ix+og2qUxQb54jKCZfwC01VOHLhqr\nj2QYfJcaV7anuRyBfJTemS1NK5/WZCDugKL1m3Gr/IMqgKmOSMxH20QvFub4\nlxJEjnO+FzgZ0ejT54NMfEntwgt1jlT/XUqu47/ig7bDYAyJUqPc6BBTrP0m\nYkbtfSTbxC3L5qw4LGNFibb9uFBTilrGt2Eek+9jv/n5GBQKJfArmnIrYao2\nKGsSkblP23dB7JySPJ9jQAhG4j5WMg3BZLr2c2h70QGKkcKcyyq3qFcRmcJq\n9c+hA2VMeNUb0SAN0W1/ZlG2XGvrAprHV0nQTSFbAysoASSGYwpX8iqV/hnA\nD2Wg6ntqGycoK2Vh2C5qvLWGRylchGZLiQrqQXYBRPgRiBFnE9TZqGfJQqPy\nwufSdBAJRw94MbUqQJmMxQWkj+6zuE33GGpizhQNJ22LAOEUyNqSbE9ANnMx\nGNzlSQPjtndSpSKAF19YT34S7kKbYja+I2GZXkMoGGjb7+K3ipaxnVl2+eKp\nhrRexFrP2ohcU80fPwJPpDJEtSK979ht7bzklqKo0AlU7e31hRVtCMlJe290\nL+3d\r\n=9jYD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/ws_7.2.2_1582658137068_0.5410410668045136", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "4956aec51f26437cff00ee1bb021a50b0102529001cea202e50e1caeac6a4799"}, "7.2.3": {"name": "@types/ws", "version": "7.2.3", "license": "MIT", "_id": "@types/ws@7.2.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/elithrar", "name": "<PERSON>", "githubUsername": "elith<PERSON>"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/orblazer", "name": "Orblazer", "githubUsername": "orblazer"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}, {"url": "https://github.com/teidesu", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/wojtkowiak", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "a3add56077ac6cc9396b9502c7252a1635922032", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-7.2.3.tgz", "fileCount": 4, "integrity": "sha512-VT/GK7nvDA7lfHy40G3LKM+ICqmdIsBLBHGXcWD97MtqQEjNMX+7Gudo8YGpaSlYdTX7IFThhCE8Jx09HegymQ==", "signatures": [{"sig": "MEYCIQCmw5YdYl0n4r7woDjE4fxLCwwD+5Hh4BFji2hj7MNA2QIhALUl6K9y7wyunUdoH/ngBQj/nSypXoFzMuEMkTey/UgJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15739, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJecoQRCRA9TVsSAnZWagAARhcP/0OQ8NgvrV6cYnSY3Ygp\nWwPnh/HGvdpbAbjlXwA5okwrGHgOu+s1dZUcAktUtJGEvuAktbasVqQBWzj0\nUij6yXh8suc5yfV+Pw/zSOfmfp+1Xk5tjt+KFO0ZrsuUgNSwcltyJO4ssp0u\ndvwHPABRNmnRMjyEwUqr3ASPOU/IDwJ6HEVXz3SLcjioAxMC7RalMvHgwD2C\nfn/xYBDU7KmgyS4TOBljeLspHfIlLx2q8tEmgPyGw4DGVgr1cCEId3f5yK6u\nfwEG/SuqN1MguhJG67CmGnOa+isK5qVFJWUczvMAeteGeiI6O9xeldDvMf9N\ntXPl73ozj024umQDlUge5RdOQMISNYc+DOC3/aG/vp5Mib0k8XutI8OAZ/Ho\neCXvG9SiGM5eTrO70rJ9nHf9mCdXk2iGufjg09q2blBDrOmvDEgfm3cwZjnn\nJlrcHiveQ5s3dC126/xLxw9gPDHaE9gymQIni+FSmyEzapYTl6Q+2TJXkuEP\nd3j9dy+44+8Dbb+t+MNbY5KRryVl5UDTQBbRUjPpWjyi9CwUe8RE+15uCDr1\nCwvr/fcCw26D3Wmn8Ww+pQlovTm5+61M5FvAmBP45Q+ulRXH/WCt8AV44zth\noo6f5JXvSLuQj5SwERB3v5TvNMzFHCZPj+wGT2Inxkc3EIBVTAyiG7uDRroO\neNq+\r\n=Iae3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/ws_7.2.3_1584563217499_0.7920111527896532", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "df63355903af9b0bb6cdeb8cf353d951a114d70d50aa5a8057758f32d5ed46e6"}, "7.2.4": {"name": "@types/ws", "version": "7.2.4", "license": "MIT", "_id": "@types/ws@7.2.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/elithrar", "name": "<PERSON>", "githubUsername": "elith<PERSON>"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/orblazer", "name": "Orblazer", "githubUsername": "orblazer"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}, {"url": "https://github.com/teidesu", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/wojtkowiak", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "b3859f7b9c243b220efac9716ec42c716a72969d", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-7.2.4.tgz", "fileCount": 4, "integrity": "sha512-9S6Ask71vujkVyeEXKxjBSUV8ZUB0mjL5la4IncBoheu04bDaYyUKErh1BQcY9+WzOUOiKqz/OnpJHYckbMfNg==", "signatures": [{"sig": "MEUCIGs0dy7y6vwzFrPhoVfk00oOYaaeNOThCwqYzawXgpnRAiEAhn42WO5VqnVSTQVaUoSPZTtsq8LyjBmBEnE3rD/GAUQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15698, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJelLTNCRA9TVsSAnZWagAAdT0QAJdx0FiCldf8ZOFDIy1X\nKeFckDPqsf/+Onz9dEaOZIotsVJb2OI5g8LiV3KED4CEYP+EnUZWhopTcw8r\n/JLNJgHDdVDGUNxFpZhZpPVVqkuXEaPilkmlsIY0RGwkUCZnIDz+oiejDr5m\nxL7gXa/KbCfzgHa259dxPR/a/gkt+NrrdIXxPApx78wkwxQ6TGbkcc0+ELvc\nDfE8CqnCCxylNTUoCX4qfgNYqsQj3nyuqLxiI/xhFXtyacA4KKY1zED+AGqM\nynpbZJtUKSbIK4hhJ9tpxpazsvg1GNqQ6W1IrBTXrNQV3ocOvbVQKHZh4UVg\nS+1XEfbvJkdBYQflUzrbqw/3il/cwVcTgRfKg2ND+nq+m2sgaaNMcuKl9AeQ\njfR5poQcHepLVk03Bvkn5MQ2SYGL9vlaMVbmjbKfK5waV3d/BNiRKFkRuCZQ\npEpOmOAAEaTYtRwnDMSaactC0ovHX8U1cus5iwdeX+RcFnb8/9JZqSL9T67Y\nfKe+GOIyZwBCebRPMODdiqbeJAtY76wh3qzZ4hIiCrhJ0XPNPethLuKEeDCc\n7gJ1lc53nSJtJpeW/gJ7f53n2ygdDdku2Rb8IKAC6F8jrAoxWszng11aargI\nCRRzSHXPweUpbAMlWRHDaCWIT/xkjdikHEesoL96ttDQ2jEM9BRPsKOVtSMD\nIyNC\r\n=hn9Y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/ws_7.2.4_1586803917072_0.630686642894805", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "844e4610d8ca55540b62dcae1394b2b144e52f2d99c1cee7aa368d75b250c1c2"}, "7.2.5": {"name": "@types/ws", "version": "7.2.5", "license": "MIT", "_id": "@types/ws@7.2.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/elithrar", "name": "<PERSON>", "githubUsername": "elith<PERSON>"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/orblazer", "name": "Orblazer", "githubUsername": "orblazer"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}, {"url": "https://github.com/teidesu", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/wojtkowiak", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "513f28b04a1ea1aa9dc2cad3f26e8e37c88aae49", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-7.2.5.tgz", "fileCount": 4, "integrity": "sha512-4UEih9BI1nBKii385G9id1oFrSkLcClbwtDfcYj8HJLQqZVAtb/42vXVrYvRWCcufNF/a+rZD3MxNwghA7UmCg==", "signatures": [{"sig": "MEUCIHZ048PJmMtAf3FjRuE6jdVBZ4qt2mCe5tHBJ7D5cDngAiEAvq1/7bgJaqCIuTITUr4J7m80tUxmVcsLNaZSb4wq1Do=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15613, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe2EnGCRA9TVsSAnZWagAAuHEP/0bQago5x+HpPoQNFmLu\nkBogUFmGkRfNUWL1pqAcImPongWWZPOMgmmWWdpyHFcwvk1XSgA9EARqOUso\nt4GetzrjZTmfDO3FxiiW0qOoQxrGVZHz/HORLQl9jJePaAF4zOGl7BMeTezr\nra5Pd2p8LVpAAXy5rNAdO+lMMJufwXtpJGtckfLfkoUopZkuMQuOsHkkUizL\nEe+VJziwZksku3T48GG5Xwz2ipqFCPpYV/KNuGPRihOYFbUdOfkLdGFeTxrw\nul9dAlKaMRGXksvLzqrSt3Li6w1L4ypJb1PXQXWMyD2DMWA9wLh+zwq9joq4\nluvT051abZBstPm+ReKNVtKKxBkppY2OtGOGetdrBCkJk/lqdmK7jhBx/lFk\n/yRoyq41q3GozTFjUKz7kLJFqZ4/3R65dX/EOoUY4TaocDkrYFjD+EhEzAlf\nhZll7gi9dC1HO4rdkYJ5cFQMxOgM48w113fMz9kbMh8BjUmwC1iHPOYGVhd0\nK5cuYDA7/Ese3SkTVwcZC1DhsbWbvofesVkH2X47z6hNzSymkjOAovo18kO3\naQ84WUvQ3ds/XIp7C2/FhrlsqH1WC4dAEEE2Heqfmu5zxTm0d+WbH29sxlkr\nvC4ExAUFhFZYJ/Mc4VHtFvQSbZ6BLnxLg3h1u++sUrWdpnOW08rek04uZdeG\nodI/\r\n=oyuU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/ws_7.2.5_1591232965737_0.10876797855088105", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "992946b32dbc9f7b6c88e3ee5032d21ad5af1d1669ca1f51ed21a70815c03d53"}, "7.2.6": {"name": "@types/ws", "version": "7.2.6", "license": "MIT", "_id": "@types/ws@7.2.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/elithrar", "name": "<PERSON>", "githubUsername": "elith<PERSON>"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/orblazer", "name": "Orblazer", "githubUsername": "orblazer"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}, {"url": "https://github.com/teidesu", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/wojtkowiak", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "516cbfb818310f87b43940460e065eb912a4178d", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-7.2.6.tgz", "fileCount": 4, "integrity": "sha512-Q07IrQUSNpr+cXU4E4LtkSIBPie5GLZyyMC1QtQYRLWz701+XcoVygGUZgvLqElq1nU4ICldMYPnexlBsg3dqQ==", "signatures": [{"sig": "MEUCIExtHpElAViGeyf/Y71k7JodkL6m5vDO8N07RXTUOP1HAiEAqLdK/Fge+is3oT8CwSXZBo6rHiJ/8AqzMq3B2x3H0C4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15632, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+h1mCRA9TVsSAnZWagAAwjQP/3Jd9G6mDtwhq0LC6DxX\nhjkjoyxuxUqh5GT7b3Wv6mtfNpTHnTRxF3u53v2mlzSC9utLz7q4ntYkPHh+\nnQlV/hlwMOLoNNy6mlvNa3ApWEKHA9HRQsKjx1ahlukXQTq+E3+U4QzZsytd\nEXw4kreUUZUznzppiBlfq+tXVDjR9XGHYkUrnHzLTuazszpKpazLD9VBQgdV\nLcEjOsyYUirZWWQ8i8GIjMLSmpns4wF6gW6GpOkvr0SAB/o7LnSWGfhkXjai\nGQsVQ0hz6c3kcCq4c4RRv1tUoUK/s/C7ajkkxwEZJgzkAn2X5IJKZ+1wnQlL\ntysrDsHSA1pZ1iNls9033UUJ+UT9ZEKzcX8RMWI1cqROFGRzZfCyL4SHEHQF\nD6Knrxz336MLEOoaA23RhcvmYotbSXkyVM0rqjIHfTohfMtlPmVgv/lmcyf8\nD1C8k/zc2VUF8FOldRHjEJdQMGRYNfxfheEiUjO4voovGKdLo0m2dPqY+S3G\nab3UfmjGeofIDQoEDa1INInyBqZzhrtk9o8zN/IVjUFsDtEkZLe+16Gho/Iq\n794TDmVpr7xzzrvhFYaMVkkUTtyaUTgxlkwcqm0fBRHm9Q1/g+GiPjlH+eQv\n3e+cVxe9FmMdU2Pxb+vhA3Ny0hS1X2+k8f/pkhdYKX9nAmoYV3cRTjXGDmtG\nz+wi\r\n=Ga9d\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/ws_7.2.6_1593449829742_0.8290806144870051", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "e201f77e7884fe181a817b50462656319d0bceef645f0c474c6811f9db33b319"}, "7.2.7": {"name": "@types/ws", "version": "7.2.7", "license": "MIT", "_id": "@types/ws@7.2.7", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/elithrar", "name": "<PERSON>", "githubUsername": "elith<PERSON>"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/orblazer", "name": "Orblazer", "githubUsername": "orblazer"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}, {"url": "https://github.com/teidesu", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/wojtkowiak", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "362ad1a1d62721bdb725e72c8cccf357078cf5a3", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-7.2.7.tgz", "fileCount": 4, "integrity": "sha512-UUFC/xxqFLP17hTva8/lVT0SybLUrfSD9c+iapKb0fEiC8uoDbA+xuZ3pAN603eW+bY8ebSMLm9jXdIPnD0ZgA==", "signatures": [{"sig": "MEQCIH0Z5ondFKFtDy56oRZmSJpjzc2b9l9h85YVX8aqEe4+AiB1mgFKje/Z4r2ACeuB0knRrCyL5jPPoqQTVfilvDBlzQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15970, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfcmkTCRA9TVsSAnZWagAAwroP/0uZiGzm7eVes+LvWvAW\nwwAFV8vYO0HMyLzf8U9evZ45RbIhRtLzUiMrl43YsOLcRl1n7apY0DSxP2mm\nBbT9mDnrYHVsAVqAR0Fp/cQGkYkrm81/0P5NhnCIZZa+vpK/LhXV7GqUIQMg\nda9eys05f/mrdj4IWl8UNx8aLIadCcps1v5luTewl18OLr3bcQ/ae3cZ7PHl\nwug5XBLfJyAZ/g/S1ko/UpdmIPa6PaUdLq4FwcLJTDn/R6RPEF4LDWEGhSg2\nAtqqnwOxpZHt/KWbr/iYs2dVfas3S4oZLIn34kflfsLASW0cE7Hm8phHkSr0\nNaaIDQAwax2ztmihwibGyo7hmpwGRhBFp8J6OZRETCh1kIFrMSdEL6SRGY19\nbBvomQ/1VpSpx7fUBo9zkWB7YPTyEPy/jmP+reFbIGM1PrVacFSPK4VPAt8Y\n9A2+BwbH+f6nsCcqfnrTPYhQ0X4/bcCIYrI10U/YJz/wcNyU/g/NrngfbmKe\nJ1U5vDLktXwSLssBUVl8s+pOkz7zEGY8GlcSd1OqiEzT+0O53nnNujao8Ksp\nEgNOnxTuBAToe26FuKIo1IZELv6SGc2DathlRG6qDdIjQQ3OBaedbScXs4h2\nFqxCLi/mESwnXFZcrooSLzw3nW13+0qO7LvNavVGW2e4IBi4hN+6ZQLS8Gid\nKinI\r\n=0ceh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.2", "_npmOperationalInternal": {"tmp": "tmp/ws_7.2.7_1601333523187_0.45598716887709556", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "8cd215aa17ea5ce0fbdcf5d7ef57c68e70efef180780e2aa366c3bfd1d48e387"}, "7.2.8": {"name": "@types/ws", "version": "7.2.8", "license": "MIT", "_id": "@types/ws@7.2.8", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/elithrar", "name": "<PERSON>", "githubUsername": "elith<PERSON>"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}, {"url": "https://github.com/teidesu", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/wojtkowiak", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "47f72f835a531c1860e1ecaed6e4f55fc8d44177", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-7.2.8.tgz", "fileCount": 4, "integrity": "sha512-LGtjDQxcMk4uU7ET85qJWYLwCdsSxLRxqOums/SDDWJw/BCCgFrOvqcvly6rGNkB/OkOiUaRzzhz8pchuxXr7w==", "signatures": [{"sig": "MEYCIQC/yYxYR2W3IW3Xzb6uUk+7/ONjr+Y/tTjATt5KlKVyTQIhAJ3ZkcL2+5RjinIkI9HAFMKN9suLh1m3WOmfze1mDKFJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15727, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfl0RTCRA9TVsSAnZWagAAE+wP+QH7sf0fqYy/ydlU2UpA\nBMO1z6hnDBRDkgIbynkeoZ3hOH/PsZ9TZpcMsbHvOQJGguIZ5ujNBMVmOSeq\nfk809vlc4QqvewKQGYS3tx9Inl+nHi2SUpsEFIcuOmDTqgwHz7euRFneGmOQ\n4iFOC9HlR1j2ZewkpT81pbEjYNOQhsCqxZSieZ2mVbGoQOTZgGF3jzcHRr/y\ngOWf+yMPoCV0k8mmW9qU4RjtyZ4p4U/5rkpfH9PWzIO5OLCK0LVN8/rpirTk\nPfQmWru4q94Jtrk/xkDxR1kRjHK3rr4qnHvHvZoxEWeAwLavYphMwqXZYSpL\nNPH9RxJaaOeB3J75ST1Y5ix/+7TousXwUwxazwv0fH8NNGHNplwQNKLB8DDt\nVYXg3JJughnXl6c6HY2WRI0flbrOlkW2CKRwzJs/2dasXnoaJipn96Z2sh6K\n3yZ6ZJczFbBKoEbxBI2PzQ7fw9yqNQhC2J5rEsyzYXyxfqX4yF4psoNql4Mg\nWe/LNyo9YjEYRrAdHPIm8FGzjl07nemaCJYKjyc9U5aV5SCOYv688Njd0C6R\nlKmwihpvtmkdB3AvzXZeQkoyAO+7GuZehjk6Z1sLHBLBLwGLzk/xXqkrjtxv\nh7PsYHLuaSA+l63Rsl/K2a05B7WYxU280QIJ+dkP7Ad1VbQUttzdJ0mcyQTl\nUvPK\r\n=FWOq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.2", "_npmOperationalInternal": {"tmp": "tmp/ws_7.2.8_1603748947071_0.1989502165932595", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "7a71fd6c7865b5339c7cc06bd48cd092d12d44d59a4c62abb0ad6e0d4d904354"}, "7.2.9": {"name": "@types/ws", "version": "7.2.9", "license": "MIT", "_id": "@types/ws@7.2.9", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/elithrar", "name": "<PERSON>", "githubUsername": "elith<PERSON>"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}, {"url": "https://github.com/teidesu", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/wojtkowiak", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "cadfac473acfab192678a487c3ecbb13a503547f", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-7.2.9.tgz", "fileCount": 4, "integrity": "sha512-gmXYAXr7G4BrRMnkGQGkGonc3ArVro9VZd//C1uns/qqsJyl2dxaJdlPMhZbcq5MTxFFC+ttFWtHSfVW5+hlRA==", "signatures": [{"sig": "MEYCIQDKKhCak/Al0bw1P9qShoRWZuuWyGWqo/MpZJbr2rKuIQIhAIFsznmTgIJVn357YWTozQqfXtr8Z+J3UoQROnZprJEq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15727, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfmaS1CRA9TVsSAnZWagAA0dAP+wZtmH8nKlIAeEwuLWon\nACS117O4WmWJe48MaPAtbdBwDv7KWFhOBFWyoFUSECFXGC5WI6aVVAfSwxKK\noMn8n9sRpomugad+AKwaGyBbceFlGFpBStYtn33Mhvws5y6+7m4weCeaeMJB\nL/MGBtcIsG9FhXzRTps3EmGVFlvfxdY0z0P6BVuLECMmqXl+Cyz4+Cl0VyDk\nN+c7ltmWSVVD3HUtWT8exvDCDjHWnopo0l2oSk77CRVz8JsnEkAAYsOsO85S\nqJY4z0tvr91XeNIMtLJ2sMCFm5mUZKOOE7351t2wBXIAZ8dKn3GgqomXJJB3\nGGTKQg1gc9WybrFBx9ns+/D/l+6GSlusno43SfeahOhpCL4UWQbJG87uWg6J\nOdSGS8WDN6he27lfpCBrehpnFnVq8yi22zvjtWDVlptlAgji0+EdGRZaMfgJ\ndGkRzYLjB0DLbpbE5fg20kkI5nXnO6v+qRmmw9g9cfdiew3ZRqOVCTyuU9AZ\nlVwbBVve+RsAXQIgY24crRrCNug1NVjN7zNOJvZOZE9TYs6tqkRHhgWSwJ3p\nc03YrRpJ8m4jwiexRpldR8NFujEYMKocy1OL0BLobN5Z1T1muXIYn5N+O3tj\nWvN8OnrTml+6/00VKBINMwhhvSmQFXgjnqDjaW9yfqg+20u73KMJ+ZlFQ6ZW\nTRag\r\n=MDtp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.2", "_npmOperationalInternal": {"tmp": "tmp/ws_7.2.9_1603904693365_0.7902471474996102", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "9e4ff321979a3045e8c05bb54ba43e32c493eb97b27cb6c15610126ff392de6a"}, "7.4.0": {"name": "@types/ws", "version": "7.4.0", "license": "MIT", "_id": "@types/ws@7.4.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/elithrar", "name": "<PERSON>", "githubUsername": "elith<PERSON>"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}, {"url": "https://github.com/teidesu", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/wojtkowiak", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "499690ea08736e05a8186113dac37769ab251a0e", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-7.4.0.tgz", "fileCount": 4, "integrity": "sha512-Y29uQ3Uy+58bZrFLhX36hcI3Np37nqWE7ky5tjiDoy1GDZnIwVxS0CgF+s+1bXMzjKBFy+fqaRfb708iNzdinw==", "signatures": [{"sig": "MEUCIQD9LWF5kHeQde//khIhGcE0lMcKyhlS5lsID2evaw7m1gIgRYKEIVvP5hcZRnVrKimYs6WrIz2CJ2DADOPbZamb0lo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15758, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfqdgUCRA9TVsSAnZWagAA/nsP/R7Ntk09JzM5bsczQ6Zp\nuv1wLTvkvTypPgZjFYQMPYpRDehkmIkMOV/+kyilvkjYkslnMOT+4uPU+nMh\n81DDS6rr20fDiH68qXgjmbyi7YRLxcFUGl1DcO9ARNZWBOPL36wqxFJeXqdM\ncUK8nL/YVpkxOjO6y7IJYw9KKrYusBGpZUTGK9GTcvRpc8YnyKVGSYAEeLEa\n52gQZCkp8rp/082I1eztIc9iJjDErsYbaa0ZUR1WSU3XNzIFiM4u34ZgQfgc\nn1O8mFNMvU1DH5f6O3HdiuCizz86CFhh81FCpA5U7C1TRD99dFLNIw0jRccX\niUoHUCA/HRgowW/3e85JHJp7P9LbzbGg/Pg9Hhtd+ypK6+sAQTwrDghNpju2\nzmvzEKkJaKq6l4qkEjKGjkAHTHPJ4kdmsFMzGvcykEqeAcOGhulgoYsNtIcF\nE1+OND18wE5gl5H4stIOooyiR85TByaKLRfJkN4vSU2SVscmldFn2gpv77KT\nhZfgdIX0qi88rMXY/do9CBaM63qrQH/y/hLmes5e4R9+B//y6EPOQR9liviO\nGwoSyEAlmyX3CRFK3+80pbsuPDrZutUKP7AB/W+5JK/W1gKbUUcyXkZfjnmv\nrYsOwxS/GpGsV3QCId7DUnGq5087aSTgK0V13FYquZDsq9QQfdn7utzctKxe\nPWn6\r\n=DvP4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.2", "_npmOperationalInternal": {"tmp": "tmp/ws_7.4.0_1604966419596_0.6038786488147039", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "77b3e10cf373ea323f596b45785b79dd2b23f671bdbc08a581245f57a083fd1b"}, "7.4.1": {"name": "@types/ws", "version": "7.4.1", "license": "MIT", "_id": "@types/ws@7.4.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/elithrar", "name": "<PERSON>", "githubUsername": "elith<PERSON>"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}, {"url": "https://github.com/teidesu", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/wojtkowiak", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/k-yle", "name": "<PERSON>", "githubUsername": "k-yle"}], "dist": {"shasum": "49eacb15a0534663d53a36fbf5b4d98f5ae9a73a", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-7.4.1.tgz", "fileCount": 4, "integrity": "sha512-ISCK1iFnR+jYv7+jLNX0wDqesZ/5RAeY3wUx6QaphmocphU61h+b+PHjS18TF4WIPTu/MMzxIq2PHr32o2TS5Q==", "signatures": [{"sig": "MEYCIQD+D5tJmazeIJDUVIPzt/NJ5JpgMtUmxZZnB3lVrDeyKgIhAMADj2N3C7tMbG1E2lfo6myc9anEHxWQUlKz4X5ccSh9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16529, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZmXnCRA9TVsSAnZWagAA/40P/28EJfedwOSwD3t6caKj\nYj8aujFwM8j/7tDv36q+OZpFfXXHeRPzOeXDItuvDn7vveB1qzU3XyTVmRrl\n5E+QMhWBdlZ2WpT8e8bBn0A4NthwYyYMpzYDdh+6rTXH9ihE7KdYJCvOGMs1\nAM07KegFk2v3J1tdO750X7xB1IkoI/IDqLCmh96j4EInyJmOEMUB6s4jt87d\nQfE0OUZdFVeMGt/fR6s/JAnZXAfFBlGJLo6xHb2mHVU/vGBl47ctlJOu4JvQ\nRA/CCjC9lDV6MVwJbuX0m9Mog2108TuW/+Bag6wDqRkvZ3smSHX7naMwBJ8/\n0C6BNnRDjOlXWrCT/c+w3X6o02HIsTRb39SnCnYT1tTgePwSiA0Xuiy9z+O9\nYMUbM+3beXHpRo6xTTeLZ6TvbNQY7Q1y8v5M0uazzie641Q/Vd0KahfXesN5\nHWad+72mKS4MlB+70kzRojk37ZooVi+FEERRoPAdDSEmCN/qpLvqx+XzCMSa\ngcTYKF/jQ+UdT/hw4+wMWaqrmI7KD7Qssn8GXxvLUUSbGzuSPR+OkcEwMpWN\n9JdfJahCpvZQEr9olwTMgcypfQdacq9bHQqUATlAt0bvi9JgGKENVg+AOI4L\nOyxySyI4G3j9WyQtI4ohS8GULA58XefmvSw3OzmTibumMY4gZnm4XdvlOmk6\nM+Dz\r\n=bAdG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.5", "_npmOperationalInternal": {"tmp": "tmp/ws_7.4.1_1617323495374_0.592478424717777", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "88eb5948b3010711a63042f8323d899704eefcd9b7233dd9b0820e183549a154"}, "7.4.2": {"name": "@types/ws", "version": "7.4.2", "license": "MIT", "_id": "@types/ws@7.4.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/elithrar", "name": "<PERSON>", "githubUsername": "elith<PERSON>"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}, {"url": "https://github.com/teidesu", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/wojtkowiak", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/k-yle", "name": "<PERSON>", "githubUsername": "k-yle"}], "dist": {"shasum": "bfe739b5f8b3a39742605fbe415ae7e88ee614c8", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-7.4.2.tgz", "fileCount": 4, "integrity": "sha512-PbeN0Eydl7LQl4OIav29YmkO2LxbVuz3nZD/kb19lOS+wLgIkRbWMNmU/QQR7ABpOJ7D7xDOU8co7iohObewrw==", "signatures": [{"sig": "MEUCICkhgB81nSKWPG+cdcfj6HM5B3ofnERrIceOeHz3Qob0AiEA2px0yDbOt5an98fsx2aCA6Dr1BXXaGyVeLGUdfkDG2s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18067, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgh6jwCRA9TVsSAnZWagAAGT4P/jPpBqxYMXGtRPJgVDy+\ngnLSPwjnKBwKsji+qY1EfL8Tl5MylSHEi2Tf3v6CVG45hAWDsM6YK/mwroRt\nMCkoBghcL/7GQu7t+kcfPwXoSjdxIEGeLOB3qVynZ1frwqdK6ouhee7HxVvl\n/iBkOKFSERohLDz7E8m3fQjzQf1rZN4xepPznhaGvm4OJLJNMRwnm2fO30yU\nUmYb/dxEa+6xOKvoJENjaIAdc07J1z96h4h7wxD9Qw4zHeOz1jB7yGpCuqPi\n40NqW6SymMmzVbyjhNXy8Kq/dHHin9LUVIJyPBN9jdQHLLjZ2/0DLrCPg+jg\nViOyl6/X1ymBrkDXXujYN2FS9F+8p+xdPslJznXXKuHiUTvxAsQr5vu56fhF\nMB7abh9kI8E2N+2Ud7QiLeUY/Kmz66hSskv6Jm/7NurlQyeS29K9buLJCvx9\nEijr8JSNm+OyrgCzUESKgMjYK9XOE6meoCmQo5iIPC8gm+9MVZ/9z1it/Nvw\n++qVc67IztvUa+u586zqcPSFSYLBRCh4RhB9Klxc5Invm7hhdob/EUkXMhd9\nRxehYrIPLZ23kQFMypePvABzTtdaLBuZKWiqgA+RoZ+mRVCeypGORng98MVS\niFbucE6f2z2SLFxiYyX1LfBSGLx0RpN9XlolWsvBhVSFS3RblC4beud/jexa\njOd1\r\n=vfJs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.5", "_npmOperationalInternal": {"tmp": "tmp/ws_7.4.2_1619503344043_0.7400747889280415", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "9fbd148321366b984c199352950bf805182888f4e2b42d9d2f46853bf41f2b89"}, "7.4.3": {"name": "@types/ws", "version": "7.4.3", "license": "MIT", "_id": "@types/ws@7.4.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}, {"url": "https://github.com/teidesu", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/wojtkowiak", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/k-yle", "name": "<PERSON>", "githubUsername": "k-yle"}], "dist": {"shasum": "eb68ffe017489566164f859cd88513907f50a18a", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-7.4.3.tgz", "fileCount": 4, "integrity": "sha512-LiMEM1yWxVHbFLGn00ADIg/EKjZsX7B29pioAr8Qe4yZtqSEl+aRcuGukxqA7I/7aZqOfrYdJpFEPNKB0cmhbg==", "signatures": [{"sig": "MEUCIGmQPVKLeAaE0qe+Jpxbwh2qTiUn7cV4TkZFtOif2St/AiEA5+NcjcUh7Tq2GmSAI9jEH+BP+lRl+6Lgd2JAKKtvE00=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17803, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgmcmoCRA9TVsSAnZWagAA01UP/RIucTRH/F7mNytaMwCZ\n1jXqfPQ6gnbTfkQn3QnQvWXnfBU9iRyuDTlf5CmPCx5/7Vu+9vj+0s+jRBKJ\nqnnwm0+X+c5NJxs/cYg+JXG1cAWc3TuR74AFmleCMbYoLMsSncq72OEv2A1S\nheKDOC2DqZak8yAtquwADRupiWe/t+UuC5/KlvLGhUZ4uJMX2zjVC9/Z6qFy\ntSNk2pPwF6vfCod+9KJA59ilY1GPafnfEQCLWRUvBegMFXAc/vhKSh1mKfrZ\nPZ+GAy1gbmnqW0AdNW1bx+9MGcdtILpLEMG67L7J+x/RmZxK0mhFySpse5lD\nPQ3N1PW4VZuIAX9riLSI6oFlOFQWiWgSRFXNVnaT/FolTPpOpVOfwww/ZFdd\njTreSgYrpD5QU/qvTiHvmua7Dutv6nbULvBuV2rf1LmAbaJZJV2MRC80SPCa\n1a9S/vImfKAdQXbQ+kbThsip5Le+8W490jqkOsd+NGVqAyuQzAsrfCGwNwAj\nR1z90kMinQYPrroxQwnh9wzn/UxiWcsWfj2Z5DhuXYRoNuwg0sGjfytAQPrJ\n9h1axYpGDZwwOQBxac/Hrw8KBu+oGmvD3ElnvXzchebEkVz+kYFSATOaUSD1\nom8WoA9HVXmdxFmngJ3MVUwtWzFkJoRool0Qz291VQhd/RT4sW213tEQsVWW\nbgzu\r\n=SdF7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.5", "_npmOperationalInternal": {"tmp": "tmp/ws_7.4.3_1620691367567_0.451386815350745", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "d439208f4c055f95f144c1f69e8ea13f33aefd2860ba7bd676689b491eb0310a"}, "7.4.4": {"name": "@types/ws", "version": "7.4.4", "license": "MIT", "_id": "@types/ws@7.4.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}, {"url": "https://github.com/teidesu", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/wojtkowiak", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/k-yle", "name": "<PERSON>", "githubUsername": "k-yle"}], "dist": {"shasum": "93e1e00824c1de2608c30e6de4303ab3b4c0c9bc", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-7.4.4.tgz", "fileCount": 4, "integrity": "sha512-d/7W23JAXPodQNbOZNXvl2K+bqAQrCMwlh/nuQsPSQk6Fq0opHoPrUw43aHsvSbIiQPr8Of2hkFbnz1XBFVyZQ==", "signatures": [{"sig": "MEUCIQDj+a2p1wAzCISYEKWiP4Tnweka/rk8E30Xp5Qfdx0v+AIgZs41+ADs4mseJ9KKvjgusBY9djyB8nlgIgf/GAl9m+g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17834, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgmdeTCRA9TVsSAnZWagAADL0QAKEZWGrUWgg6XWHaUA/P\nkn4oHo3aRTBV4wvJFhCpy+ELXgMy9r1iNwI5GQ2+0mTccRommYeOJi8VQmla\nWYzocJnCZHMmv1S+RUSzMrAfnFxiwoFUJTy2iLqKtbCRpuvdGg8Xlois+JfF\n7eslwrsnC9NerFQUa34qPHEwEnY9Fgnv4TIfc4blxGvrkkcttxAIjTDvrGNP\nhGBXKQjZzFswD5p9aB67yHrihEB9QIJXHPbI7Ru+NaOgpypU07lHOxJTuTJv\ndact9Sv3oygPESc3YC31SyVMInGj039skg6WESsO0jTcfzX1asxQ8pVPc1wN\nt9AteHJ3YQbifjFfCsGIFyX86nuqQYddfYpWVAEcSHoUngNxu0kA3DrrsokA\nPUtByQUhtd6mHWrlR6BidIf90yf97i71WIQ7zItz0uwTeqerK6UoUmHGE+mx\nzB3Xxez/Aa8tWYTD0RXz7nubeGoItD/3NIXVPMe/N3se8O3ra9D1nYe3sPFN\neB6Huf74fQdlHo4RyNpym6Roo+OIhh3tY4+kNtQNrTY+vzqj/F5I8KROI5uw\ny1a17bgkO49lohQka/l52hZtDyAXll4jTteGAsJ1L/FD4Wb7A30Zc/jxbEFC\nHDU1rfRGwinE6NXmlB8r2Eh9Ds2pww7oRXOhHYlviqr2QmNDQ74b+MMPcQLo\ny4+L\r\n=PLWI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.5", "_npmOperationalInternal": {"tmp": "tmp/ws_7.4.4_1620694930675_0.301298586850389", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "0f06c4f2be6fc0cd19d4bc725964e3dd0e74843083aadf1716985b5be42bbb4d"}, "7.4.5": {"name": "@types/ws", "version": "7.4.5", "license": "MIT", "_id": "@types/ws@7.4.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}, {"url": "https://github.com/teidesu", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/wojtkowiak", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/k-yle", "name": "<PERSON>", "githubUsername": "k-yle"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/ws", "dist": {"shasum": "8ff0f7efcd8fea19f51f9dd66cb8b498d172a752", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-7.4.5.tgz", "fileCount": 4, "integrity": "sha512-8mbDgtc8xpxDDem5Gwj76stBDJX35KQ3YBoayxlqUQcL5BZUthiqP/VQ4PQnLHqM4PmlbyO74t98eJpURO+gPA==", "signatures": [{"sig": "MEQCICvOKkfvRiWIx/N02VJmz9cppH9tHR6i6keurW7wFvbKAiA9j8kUxR46VsPfZKQKmNAZZ9w/ncnP37e1xESo5Z2eKw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18326, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgyxZ4CRA9TVsSAnZWagAAAn4P/0PHTogpAFKNdeZi/8D6\nJuuZ3uaZl5pxMlArD8qtSwMGILy9YNY2CnGluH5zXsbPwZUWr5adnlVvRB/U\nPxLsF+/vP+vWf62UWy2fkDOy0Lk7bBGLats4vQf6gflPZjE755FwcVUs/g/R\nM1uCqm6AEiPWvfopmU0HXRVhio40NbPGyNmYP261GREvcWUazZx4/kgP4Vhn\nT8DG66AsFpZSegwqOt0aIcxHL85UZqyR2StQ8h8qm63214NoGDRloSymMl9W\n0gy93kuz2BVPlRwYG6SuT6WMisE5Z0Ws0aI0bW1ulE2D1Y0haXsY4nv631xK\nTcTkGGNWUYjZV4YH2WGcp+GS5573hrJ5GQwMKI+yhTuv3q5StDHZVf60Fscl\nrrlKSg/LCxc8ULFcB7b7Bpwj8/02BsdklWC83ipGRHC3yQI0I1CiowA6CDDh\nBMisHDzw55rP+iaPFuOscUrg9Tcq5jWG9EUitu0bq8InBSolzag3c87GGDcC\nXk5eKMBCB1Vpqr/iAXYzqLNL10MgzQ0obLstXIDrIw86T5hxSzrGnSKmXyR7\nr4qM7lH14mWhNhBKlwpVAJACnNpjfJj0jeNDZ9NYXtV98t5OLvAmkfoh+Fzi\n58rdjGVnschiisH56d9T+bFU4bgJFoAZRIEdx7xqhZlzsnzTl9uFncB68uiI\nT4b7\r\n=4yHP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/ws_7.4.5_1623922295659_0.4783111663609134", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "9f28541650b7333d7fc31658780f4f9ad35b01395809ef964089854b8846b2e8"}, "7.4.6": {"name": "@types/ws", "version": "7.4.6", "license": "MIT", "_id": "@types/ws@7.4.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}, {"url": "https://github.com/teidesu", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/wojtkowiak", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/k-yle", "name": "<PERSON>", "githubUsername": "k-yle"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/ws", "dist": {"shasum": "c4320845e43d45a7129bb32905e28781c71c1fff", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-7.4.6.tgz", "fileCount": 4, "integrity": "sha512-ijZ1vzRawI7QoWnTNL8KpHixd2b2XVb9I9HAqI3triPsh1EC0xH0Eg6w2O3TKbDCgiNNlJqfrof6j4T2I+l9vw==", "signatures": [{"sig": "MEYCIQCLdfrC/5PR3DJKIMslM/EfO365xVZuLCgUpL1FdJHqOQIhALYHTfywJ0kcX8BPxtdOQ45FaGrhtof3wEixs5fmogFP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18878, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg32QxCRA9TVsSAnZWagAAzlYP/0RZzcfO3hAPe1+tEIub\nNuGMkYAhYg+qrmNGzC/dr9p3kphfZlS3QfU/J1Lsk/MNawkQK/zrTHkxF/Ok\nU2At0E45cNDiKsd/65rEDDfRrYyalHCCK8OgWTxTLOHyS11sXmnB3pGWkzuL\nsULEJsK/qv1Ida+eZyWnof6tOXiQr+kdwTmrP7DzQ6zZyCcvUw5O55LYTGAJ\nhaLZDJCzN/zyF9A/RJoqqCuHqr+u87XGMXFpMPsoDQmrQ2bXI81qndM+xyIb\naIr6gNtrTxj96Wd/jpdQfcpuLjsKK7tIiw9JeWRVb3tJyWIb2tli4WC/yEMN\n+4jikt1L/oiTiGh0G4EHEWU9TE18jaj4Nt3xFbEsZXSkIB32/GXLJzBWwRPr\nxQvo5cc/oNO0vbGJDon+jStgYOkT02hTBg8m/6w6ZEOvbbMOi2bB0VWU6BmI\nUUObjo7Lw2TZlaa85UuijzJksujXY4ldaNyiTvHurd4inM5zS4fGYbAW3v76\nZyDVlX9ykHb0uOAaVuSKz4PYDdMx4HaFKDjzWarZmDuPteaASLRHF2ENt2fn\nPfE3iljvMcoKONeM0jHIsbmWW9fJ2XJkLFMWsldrN/5GYGL0nx3mLNtxFCNm\nNg8To4QIk6pldLg6UBR9GiFEJdg+eMK+N0kh9El+TiSQj7tCe7f6S+/OpDvM\nIKzq\r\n=1uBU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/ws_7.4.6_1625252913310_0.24820165786341164", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "a6368572e0a06fac2a7153bac855964115083e179af3c7b6930e06f891b57884"}, "7.4.7": {"name": "@types/ws", "version": "7.4.7", "license": "MIT", "_id": "@types/ws@7.4.7", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}, {"url": "https://github.com/teidesu", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/wojtkowiak", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/k-yle", "name": "<PERSON>", "githubUsername": "k-yle"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/ws", "dist": {"shasum": "f7c390a36f7a0679aa69de2d501319f4f8d9b702", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-7.4.7.tgz", "fileCount": 4, "integrity": "sha512-JQbbmxZTZehdc2iszGKs5oC3NFnjeay7mtAWrdt7qNtAVK0g19muApzAy4bm9byz79xa2ZnO/BOBC2R8RC5Lww==", "signatures": [{"sig": "MEYCIQD3JO6JIJkffCLOE7PaoJ2/OwCHoyUh0utF/YKd4OdSTQIhAOlB+a3otp51cfGKcvQCCRaWechqlPkTJImyB8JDC8On", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18922, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg9gS9CRA9TVsSAnZWagAANEcP/2/fcmIDKS22dAsoQrwo\ncyit6s3KglKm0XdEWUpqELXKiMmc+29PHTpJhks1Zvlr9GLkJtKiCI6Nk0cC\ngCjP9siIZn4bECwJmYI5jPpCKdqx7kaYCCcB0720aC63b869EUOZ72HY3n8q\nB+c7+ExmgWV81f92pq7x+vzyYSbrKnMO3GhSUap9KT4DaTh9zLySt6hCqocT\nnqxgLT1Y4dSb3V5oW1mPAbJtiJxTIDTW30QdNIb8C/3SRNIrIszIPYjAp3H7\ngQfP5+GTVYCpHJPsKq8WKD9GchEFLOtiWvcYYpBu7a+DXWxi3HmdsRkiIzJa\nY0SSm0rsyYfDf2xtSz51qfxKIJD/mtaCwkYaPKlUYfscXkxziR6mnHEeIB1j\n9bDEtKhH0fU8V3nrkPb+kgA002LyF+lXfKJqKuY986WvyrMRFO34/3qYDQ4V\nksZVuqlGFbxVGP/y5A6r+Vdq33BJCro9lRS4v3yXLDajIV3aCAAPiMlQt39U\nNNinkKMPnaAWHbyW3kGxwCnm5eqqjD+vVacGOLQzoWSYcHaQsD5B9b3oYCSd\nNR22ovmjWnDX06NgaYKXEWnypRbAjhIfMnS9hjBvp8YrgtGyNHVO6XJlRd52\n05tg75eXpVo1TOnETLoibLkIDBtOkOpRHJp4vtQzSweYUR3z07JoxBc8JLjC\nP02U\r\n=rViy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/ws_7.4.7_1626735805738_0.03330144091596576", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "bfa5f3d19c5c1f1c415aec2e218c5c83c4c88b441bb05b2c022b6cfee2c36dfd"}, "8.2.0": {"name": "@types/ws", "version": "8.2.0", "license": "MIT", "_id": "@types/ws@8.2.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}, {"url": "https://github.com/teidesu", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/wojtkowiak", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/k-yle", "name": "<PERSON>", "githubUsername": "k-yle"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/ws", "dist": {"shasum": "75faefbe2328f3b833cb8dc640658328990d04f3", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-8.2.0.tgz", "fileCount": 4, "integrity": "sha512-cyeefcUCgJlEk+hk2h3N+MqKKsPViQgF5boi9TTHSK+PoR9KWBb/C5ccPcDyAqgsbAYHTwulch725DV84+pSpg==", "signatures": [{"sig": "MEUCIHhKUar41In9BkbIbIhGZ4T3++CNYJL921SW9t92TimJAiEAmycJ+j2bJO/dy0uBa7/mbZhJ9c3HLqVi3D0KVJXzl08=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19062}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.7", "_npmOperationalInternal": {"tmp": "tmp/ws_8.2.0_1632571309545_0.37238097812124216", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "a70b4686f870b5b806e91546b00f3001eb3dbce7104b3a2d2250addef08af760"}, "8.2.1": {"name": "@types/ws", "version": "8.2.1", "license": "MIT", "_id": "@types/ws@8.2.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}, {"url": "https://github.com/teidesu", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/wojtkowiak", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/k-yle", "name": "<PERSON>", "githubUsername": "k-yle"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/ws", "dist": {"shasum": "7bdf6b12869726c9f3cc3c48485efea5d4505274", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-8.2.1.tgz", "fileCount": 5, "integrity": "sha512-SqQ+LhVZaJi7c7sYVkjWALDigi/Wy7h7Iu72gkQp8Y8OWw/DddEVBrTSKu86pQftV2+Gm8lYM61hadPKqyaIeg==", "signatures": [{"sig": "MEYCIQC61TH3Fb/ughwG6EwmHt2BNzzyMQ5QveOvZuBDXPfi4QIhAKN+kXnR46+wkQVqj2gNKIOQSoz2InAqKNGpdr7HKdHI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19594, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpRxzCRA9TVsSAnZWagAABqUP/0p4nu5lRLtAqWAvaZ2h\nY7s9DE1IsYLVA0/WYKyfcR/KiAdMMvzu5LHK16VOQfb3vOCGEIzG1/CeY0AY\nSuCse1o3cGO0vzbMwJB768Ke6l4+z2KW9WPiYHzqbBdYIMtYYPFZKG/qWKiT\nNT79pilksyCrF7DnGrfNzn+kUzOI0tS/aAQM26xlfdKOrYwpzVhBLGIabfnT\npCb4HMzwJU+lM2adp29SClEik9xr0MrkRekKFue4HXKF9JKXv2iYXJl0/bo4\n2yZF3GZRSqT7Hp2jxMISbSNN9ZOSl2rmgZRPQd5jAjs2Qg3xh/7pC5xc9SEo\nu/IUht0k4dG6ejAdRBn+Wb9HecenH7RKMpVHUTndX5mg5UTv/ciEeF8xNutO\noliWk+fwSlUZ5mRDG2BaiEqccxC+ZU00+zZXarYVKylf0Onm+4yIHUcqRfCl\nYs5A+TEhUkOSwnUotlyCvV/9TVobjlYRh4cj8v5pZPLCFy6o74tUURanVfUD\n2aULJzdn3Ndom01Q7OzvRmMjxkAYaBl9qybIECxBWvHfkAgsTlOa7tBaqaz5\nd5sfWDE9eHN2zeKtRdTwzTdkg+e9eizjOa4p1Gn6UXm3UaKPjNXW8qfDtjlE\njvnjO7WlWs3vt8VKUqrhvkudNC1A0NnkQJ4iM0VxI3zUFLS+ZVe44hFSrslH\n8bRb\r\n=5ZNH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.8", "_npmOperationalInternal": {"tmp": "tmp/ws_8.2.1_1638210675294_0.05968458263494414", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "05160e50296b9b9348043779aa7617810b921d0f76d253c7fad8b28f88150d0f"}, "8.2.2": {"name": "@types/ws", "version": "8.2.2", "license": "MIT", "_id": "@types/ws@8.2.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}, {"url": "https://github.com/teidesu", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/wojtkowiak", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/k-yle", "name": "<PERSON>", "githubUsername": "k-yle"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/ws", "dist": {"shasum": "7c5be4decb19500ae6b3d563043cd407bf366c21", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-8.2.2.tgz", "fileCount": 5, "integrity": "sha512-NOn5eIcgWLOo6qW8AcuLZ7G8PycXu0xTxxkS6Q18VWFxgPUSOwV0pBj2a/4viNZVu25i7RIB7GttdkAIUUXOOg==", "signatures": [{"sig": "MEQCIDwngzi8EWrWJgKeAUk57Dv4FOagre7wrsj2YP3l1IUIAiBOfGa9+9u26/BIDpae7eFLT3EeH/r5IFLBH+L/1/+BRw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19608, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhruqqCRA9TVsSAnZWagAAdMEP/jiP+YK+0hBYgLC52gaE\nXUHdT8F39H6QaxYzRZ6l0sjFawg5y80VbiZrGBWQRntmXhI096nccCDCT1ix\nnsohXefizDKtUUgQ7LTphXfdgRwRpJsC9gH9CJnwGOnjPP56bXSmwobVI0IL\n9t3eqJCLvesYGpQUyyL8LMHJ+nAdKP43AJYwM7v2LXlOGdj1beZQAQoWhzVy\nSBgEmr6VhEXWms6chCg4/IKzY6obzZYl/eNoFEbXk4VCAADXTdJgAJ14MHVk\nrOH2TJto1MOHgMrpvCG7lafqE25xk8wAG8uAAfa69LQ8c9hIMyPySOG7Kh8V\nVbj61+lUB74jL0OPOMuh7dfkhgPNDQwFV5HQm8lTCDecBlshuy+mDyNNcVxz\npPjnLGCYLdZCiIU3t1Syka3opgHSlw1tJunJLhVJ8ELldQzVwfFan7y0ykrn\nOwuaN7FSrGfSRGf6HbdX2wRHFOkcPhZkIITcOTQfG1JsCViRvIcyeRqSAL6P\n3E03pnURCNGerBiP90VA1jBC94CoIABTDTekozd2Z3FhR4rW7I7qEwlpjkSS\nI5hx3fKrP+/eiGK9FakisbSgNBw49wM/H29DjCLMz87f8tfsyYNo5Kk+hbWd\nC43NsQ6zzJ23bNY0COj2nl5uD2XQb063ugVyCxT0w4nPuRfTzu94yhogtCx/\ndU3Z\r\n=5Vsa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.8", "_npmOperationalInternal": {"tmp": "tmp/ws_8.2.2_1638853290594_0.7691091523565721", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "5582235834d539b8c43a9cd869295839d1d449374992b4df4fcd4ee8d310bf4f"}, "8.2.3": {"name": "@types/ws", "version": "8.2.3", "license": "MIT", "_id": "@types/ws@8.2.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}, {"url": "https://github.com/teidesu", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/wojtkowiak", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/k-yle", "name": "<PERSON>", "githubUsername": "k-yle"}, {"url": "https://github.com/cwadrupldijjit", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/ws", "dist": {"shasum": "0bca6b03ba2f41e0fab782d4a573fe284aa907ae", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-8.2.3.tgz", "fileCount": 5, "integrity": "sha512-ahRJZquUYCdOZf/rCsWg88S0/+cb9wazUBHv6HZEe3XdYaBe2zr/slM8J28X07Hn88Pnm4ezo7N8/ofnOgrPVQ==", "signatures": [{"sig": "MEMCIHZBGcUJWW7baIFFg0fNrqyDFLpZJ+VjAfjVXTagg199Ah9jD9ITPOkS4OeShkSCXrBrpqYhFD8qjnJyHeTxMKi8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiEF2WACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpheRAAkfzJYsUjeb5mA8dst+UrnwxcmXi3aWQVUWB7dwqzdersWzuz\r\nfbVrpYQFOcZE00+3AyykfIvljb2R1sqolub0yIHEA5dzJak2RP7DDeMHrr8D\r\nG+5LmErreNirPGt0LS3+DSNC/7/ktI+BmCtbbhfbrm/fNTMXeLOXiOuRqso5\r\nmLLOhhXVViIwaYq2Ow401IMk72QJPpVBdZBmFv03FR/BUrI7Ic7Eqq6W5ABf\r\nVssTHL1kuWN+0K3qUm2HdQUKkfNrdleqERmMu+Ka1DFzqJXI8Zi3fOLr97SH\r\nKuLYql0HDJwxL2SmWin+Q+oQ92reDzFz67y37/m55J0JXlaJOvMnX1+QJM7l\r\nmh34NM4QdsLcb6X4CWF2oGopejIvJVk8Tykmv+4gFyIuwdUygJErBJvVwckP\r\nJqdrMsR2RNNPVhFFqk8rdqwajOqJqjlQQtZ7h87Tc8Kf1AO9cS1Zd7EmI06U\r\ni6pVKGI164aLyKwZZx9UsVSmtz0rZbERze8GXwe/VJS4lrher7PEcEAzlGEa\r\nsNauOZFHYnav7xhVJuGhMewDXiAvlOM0tDiASTebqK5u3pAmHvySVgdrtM7V\r\ntyMSD67AoUCCwAQ3iROtET0m7cPbsbckU9Ag7PrJsebP1qnZI6+oD54Fn8j/\r\n+rgg6PgXfJ5TDVffbGPzMWL3cBx4vaX2Zo0=\r\n=NZwD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.8", "_npmOperationalInternal": {"tmp": "tmp/ws_8.2.3_1645239702489_0.3479672960472304", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "68c1818ce7fc02704926ce86da9d426220e9873edb83bd108f1c6b039c276f2d"}, "8.5.0": {"name": "@types/ws", "version": "8.5.0", "license": "MIT", "_id": "@types/ws@8.5.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}, {"url": "https://github.com/teidesu", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/wojtkowiak", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/k-yle", "name": "<PERSON>", "githubUsername": "k-yle"}, {"url": "https://github.com/cwadrupldijjit", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/ws", "dist": {"shasum": "805ceb3f68eaebbed1a3004a66f16e35b7f3bf14", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-8.5.0.tgz", "fileCount": 5, "integrity": "sha512-mTClfhq5cuGyW4jthaFuig6Q8OVfB3IRyZfN/9SCyJtiM5H0SubwM89cHoT9UngO6HyUFic88HvT1zSNLNyxWA==", "signatures": [{"sig": "MEUCIQD651ul9PNciIcPKoA4fT2SQkPCdlT9sNYy5SnYj1v3YQIgJVDg6DRURjo4hE+IfqeCQkEp3I7QkgPWSkAtp5xY5bI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20145, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFpKvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrcRg//bLpSTBlWzieHbH6XlT/izHCXIXcHn419BRIS66jTEgeX96qO\r\ngh24teyGMB2Ppm/qjdgmEn0fbIYE0UiLM0fnIfkuD/ZafGF2GHvTnQvgA1MX\r\nMxRIA0F9uPreGS6w90yEvs/8EfuajzNsezSsCLmfVMFdxgdAodZ0nvUa7PDy\r\nfGD76MB/q3Pq1Vh7xH7u4FJMBRLOt4ke+cDrW0556xDxmReqUVE/tc8kll47\r\nv+nJ1epQlLj3FYYYgvMsji2HjbdVlKDgkDUtfVwcS9sCYcKBcYCTmv+L4/4M\r\nD7a4ftmS+apUxH4hoj9AZcDYYZAUVslikGrGWXEyd9rZaBNXvyL/fOe/zSD9\r\np10M2wHXnUwgYLf683YcP4JR94jWTz2kQkCrfnmsk5B4HpWXaFOWj9vr3f/F\r\n9rJ8U7KDVqloe+UtPOzy5E/oyL7DM1RxeIIbfHfu700naB+KbpCbXJdlaE+3\r\nTnQIZ+KNJ6RwCtK5PQckPP5KRBv1rh1egQiIjYo5NnlhXhtyxKEVnCWY+2ss\r\nQGJPkkHB6hbwr4wyy71MvErHMW1sVrPORWXXWlSK+BAiQSqPsvrdlPMvZsM5\r\nc3iJYHrNHhifaNOHVFIuVN8vTC02BiXFKdojNoummXI05lKI/f0SRon4DfsW\r\n0MlzWLKmGcziDPuV7EKWWGZnZUvglK2rg2w=\r\n=XjAL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.8", "_npmOperationalInternal": {"tmp": "tmp/ws_8.5.0_1645646511023_0.6468566597707923", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "5aa556a794e24717053cb3820ed284b515582a924b486950c7cea74f18267af8"}, "8.5.1": {"name": "@types/ws", "version": "8.5.1", "license": "MIT", "_id": "@types/ws@8.5.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}, {"url": "https://github.com/teidesu", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/wojtkowiak", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/k-yle", "name": "<PERSON>", "githubUsername": "k-yle"}, {"url": "https://github.com/cwadrupldijjit", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/ws", "dist": {"shasum": "79136958b48bc73d5165f286707ceb9f04471599", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-8.5.1.tgz", "fileCount": 5, "integrity": "sha512-UxlLOfkuQnT2YSBCNq0x86SGOUxas6gAySFeDe2DcnEnA8655UIPoCDorWZCugcvKIL8IUI4oueUfJ1hhZSE2A==", "signatures": [{"sig": "MEYCIQDHLrdTvfWmgChmHiDHkavGytQfqL+noR5Qy5AIZL15MwIhAJeHBhmaiPl+VcE9YJvA1KGR0cFJ6pL2DpT0cLeOUeGY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20273, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiGABQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqR2A/9GHMGKDGt4g+NP1T4pl65ftn5FfcOyDby1VIBPOuUpO7sZe4i\r\noWb5LEedFtMJjJEWZ534Q1M/HdpyRbNrS60EOVPpKsoM6CUG75VP/FNPeA3l\r\notXOsyEaz867RBHV88560mDXO8Eu35vX8jPUOoJom6RqM+Bom16pqEUDyqbU\r\nPtBP8SxrZR3qWKLJY1HrGKkVHgZV73Wp8sjdIFUpwBsqBw2uF8GcdcJXHzay\r\nr1fKIKSFN/GlFkwTiopFfN47RcFFm2xBVD0LcBBdNZ/qVcoX+YIuXVrW7Wxn\r\n3a31jSsl+0V8B+mN6L8wvSxYdFIazGX1Usvvsb5lP2O5GTR1E8pgmyVya3es\r\nqz1ggiBO9HGMwyiB21JaaKuMl7ICP/oMRpBPyNVhyxyWeetaCZzlI+vqI1Lp\r\n9xMHTDY3WkjoHIFPbjmPDKYtgarcp5bnTCDisYsx+t9fLvnJQwpDjZnH3rGV\r\ncjRnGahndWKpqwAc1npCcuxrhRQF+f0X89IHniW2o0La8CKGUN1Am761vxyW\r\n1pCE06PnJ0v1Cs1834tsyHfwjRvrJ1ex1DLrrt5OFljP/L7t8++07x60pXMw\r\nezmW+Zlcu0WSnC5iuymbCWi+fX8pejHFl4UP3WfOFS8NYOmEmbVSJqQP58Jy\r\nmEn25RLcj9ITmvk6Y67ZB2UcKQJTOaxNEqc=\r\n=avax\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.8", "_npmOperationalInternal": {"tmp": "tmp/ws_8.5.1_1645740111950_0.5894807067244552", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "44cfe58679c6fe88514fa1a812d2e19ae226568e93a3aec698193a9d4f8cbc18"}, "8.5.2": {"name": "@types/ws", "version": "8.5.2", "license": "MIT", "_id": "@types/ws@8.5.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}, {"url": "https://github.com/teidesu", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/wojtkowiak", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/k-yle", "name": "<PERSON>", "githubUsername": "k-yle"}, {"url": "https://github.com/cwadrupldijjit", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/ws", "dist": {"shasum": "77e0c2e360e9579da930ffcfa53c5975ea3bdd26", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-8.5.2.tgz", "fileCount": 5, "integrity": "sha512-VXI82ykONr5tacHEojnErTQk+KQSoYbW1NB6iz6wUwrNd+BqfkfggQNoNdCqhJSzbNumShPERbM+Pc5zpfhlbw==", "signatures": [{"sig": "MEYCIQCabgGEIbe6Gx0ppifihe7tAS9Gi55KD6CknU/d4o1sigIhANPjs4yFmp6/s7H2t8uu0Ccso22h870stul/afxo7Iqb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20798, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiHooeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrJVA//bDfMWIib9kWKL6F9D1Ws09z13cWGBtsirLNKg/7996tJCUYF\r\nBuNJno+rAVGUdTNb+Nu7jRT29RCjDSn9u4INlDwOB3Z+VlGG6dJkKJ90vTml\r\ncANqlanBPpzIg0H9nLWaDwmqdmni1/Xx8+TesFLrElc/ZojRemY43XFAb3tu\r\nLoGM6J8zvVQEebWQfZ51ZFJ5k3rmg7Bgt/GqLk6IYg3swgxY7J1UAIDU387/\r\nfAoAFglX+6qx2YRGeIkdshi00GyncFMjh7BuICSEBeSnKP1X4KDvcT67Htyh\r\n/2ZHkzWAxXtV4nCXUBDlx8viMVct0j56OcNJixAvGmzGXrfbrnXnZuVaHn1O\r\nKtdlWwZxZkesibBux24ufsdSl4qFqOzJtkuqoC62EPQN1qa/zCV2BqsSSytY\r\n0kjWjsQChHOgD1VcgLo6j/O/I8L8RZt9w+5akWIif3E722TPr1BKGV0UMt37\r\nq2q7QQr5TS35UzEJ27/kaqXJyJZ1eO7JeKkk3+E3HghCazp98fCcFZTFahHR\r\nLDo9UML/UZVLNafinKtlcNFC90HmHJgTLQ9rKltzLzZXo7nad270tJmEotwX\r\n5a+L7Z6nVaKy3Wh8Yq14lbADXDgTn01YKFoI48X7/GUfxTvvDYA2+zTt20wC\r\nbFPhK9T4rEYq35mzgEqHV7jyZ4ETUi0t38Q=\r\n=o5qk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.9", "_npmOperationalInternal": {"tmp": "tmp/ws_8.5.2_1646168606216_0.48803051895648664", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "60bc396d4e227e7caaf1c042796b37f092ff47f0cc499a6994a0f38590aa0645"}, "8.5.3": {"name": "@types/ws", "version": "8.5.3", "license": "MIT", "_id": "@types/ws@8.5.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}, {"url": "https://github.com/teidesu", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/wojtkowiak", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/k-yle", "name": "<PERSON>", "githubUsername": "k-yle"}, {"url": "https://github.com/cwadrupldijjit", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/ws", "dist": {"shasum": "7d25a1ffbecd3c4f2d35068d0b283c037003274d", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-8.5.3.tgz", "fileCount": 5, "integrity": "sha512-6YOoWjruKj1uLf3INHH7D3qTXwFfEsg1kf3c0uDdSBJwfa/llkwIjrAGV7j7mVgGNbzTQ3HiHKKDXl6bJPD97w==", "signatures": [{"sig": "MEYCIQDIzJ6XfKptjXVAe2eljjooOOwXFaCj7QXlD5a0BqtivQIhAJLiSSUrda4v3saFZlJiUs3iuONtSm68dLukykIU2w4u", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20834, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiK9vrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo+hg//R73jgWdrAGszae6Rapb8xnNZAwJDecAEPxlKv07jM5maIhO5\r\nFEfN+a/u+/6XSdKdG2mBeulmU1unv8DsJjSGKahN9UhjpxQGqWEkKQAx1upd\r\nMAr9IxZOGm7ndXyXHHvGIYIGS9lftDtSBFLSBqbG1i4UOTbI05Tn+PtUpkkK\r\nhkHyqEpZbP9N1ZYICtvaHgwtTmpjUYS5eFBVp+FMEcCv16OhIXYUlw+qgYWw\r\nA+JllYCBRwBWQmJduO1Io16vUT6lucKeC3kKEmlI7bZyqAYfm2NNjbsXOGqD\r\neaJuihhuh4AaK1T5Z/K2OJ1Xm0/aobLPndLbhq/Y8lAn+B544GnaJNTQxpQx\r\n1iYsRUqLTatMAXAPLEhcERSllk3w8SeCGkN7+23G/XZIP4710NwKYddUKyqf\r\nDbZvZGIXti8EdL3QOhvUdU1ythOSWBqnFKcts8ZS9/6FX63JK9UtWQ8iZkAe\r\nX8f3UMWprVZhDgR8/5KpTcJ4Jl2vr/0AodQmP2co/KafhxVBPJCbk4xPjYuM\r\nCfG8wLawiWcW9lr0o7mn/4yhWKH5YWIuqB4k69GSfnlwPkQ57+waTU1+YmRc\r\nuQvtsBhzKL06p5E6cnuNn40mPGXhCfLSPTJp/nq1487Oh4Rr+qsAnOOiH/q5\r\n5uVVWDr47mlttkPfJ/ARVZc5BCEq/0R9AuY=\r\n=7/Lm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.9", "_npmOperationalInternal": {"tmp": "tmp/ws_8.5.3_1647041515472_0.1380669543378834", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "736677af1e79719b2a3c152433fa403a596bc2e7ff92dfa676dcb9800e971164"}, "8.5.4": {"name": "@types/ws", "version": "8.5.4", "license": "MIT", "_id": "@types/ws@8.5.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}, {"url": "https://github.com/teidesu", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/wojtkowiak", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/k-yle", "name": "<PERSON>", "githubUsername": "k-yle"}, {"url": "https://github.com/cwadrupldijjit", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/ws", "dist": {"shasum": "bb10e36116d6e570dd943735f86c933c1587b8a5", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-8.5.4.tgz", "fileCount": 6, "integrity": "sha512-zdQDHKUgcX/zBc4GrwsE/7dVdAD8JR4EuiAXiiUhhfyIJXXb2+PrGshFyeXWQPMmmZ2XxgaqclgpIC7eTXc1mg==", "signatures": [{"sig": "MEYCIQCAALHIBX/umDvFR25O8TOexotkMbBICfYzC2UfZH9upwIhANfih4dbjlJ9bnm8yRxyzVXeplObJ6EQOTfo8YyQzfAp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21517, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjrjGrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmou9g//YQpyLea7XVm/RO8ChrNUuX9LNZSBmMmfThiSQH2jOsP0MMo1\r\neJ5p8a1VNiNfWVuadh9V3iPDT0jlsZhJSc2jAWih5wfvaJ/Yo4clKwGTbhZ8\r\neZN0fdVovuovyP5QHkSccuGAMHll606tSIGA6Zr0+AZI6HVNU1wXurRGNWKz\r\nQHt2GECOpR0yq8n/tiTkBbL0OzZMTIV/N+9cBDEwaTnC+de6keLADxD0x3MJ\r\nfPeJ2X4GtFt+BfVZk3Vroe4cb5cZahL76skupNA5iAQ/OjNIE6EBZPez+43s\r\na0B2sgkuYIaCxtKGeRcM9i1s5BQBihF/Nd5vr1+Xh5dKSJGODty6+uriicuR\r\ngVgPAkOHabL7D6B8JxSZeNmVGQtVmOFdoa6JFTwEq3NCjgfpxJ2jJg8wxQAE\r\nVqqdX1/Iyw+JaLxZPuJ5yHcyU1LES/M9TmnM81BgBr9cHFzH2KpSYcd3Lght\r\nHfgP7tSmjkqSYr+MI4ZUPToHD1g5eLaLAZ+Ov+UVWnsodwf3KfPnqPzi1akw\r\n0tzXl0rNPHaz3lxm2bHmXLIOOEI13qqm/jS4m5vgZ9ruadPHnqb2a3XK6Al8\r\n0wtVGgtuSawHCdUh59gDcuSdF/IuXs7vvika3ZUj4S+HTKJuKTzCCESZ9QfE\r\nqFqbm+B4DcJek2eR+Eg24jPe8oWSirArjqM=\r\n=O3XF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.2", "_npmOperationalInternal": {"tmp": "tmp/ws_8.5.4_1672360363007_0.9318492534858211", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "d78ccf27d7710f245ab25f9d4957067c8fc0f7aadc035919f7a890427d7360ca"}, "8.5.5": {"name": "@types/ws", "version": "8.5.5", "license": "MIT", "_id": "@types/ws@8.5.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}, {"url": "https://github.com/teidesu", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/wojtkowiak", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/k-yle", "name": "<PERSON>", "githubUsername": "k-yle"}, {"url": "https://github.com/cwadrupldijjit", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/ws", "dist": {"shasum": "af587964aa06682702ee6dcbc7be41a80e4b28eb", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-8.5.5.tgz", "fileCount": 6, "integrity": "sha512-lwhs8hktwxSjf9UaZ9tG5M03PGogvFaH8gUgLNbN9HKIg0dvv6q+gkSuJ8HN4/VbyxkuLzCjlN7GquQ0gUJfIg==", "signatures": [{"sig": "MEQCIC9mqnZuElKL7TAJPvhfVQj45TZHZrBOYOXjGvFMmYyyAiA69mWr3BM5xj/iL2Wk/EI9L1Z6ZhkcdmNJTZI3j1yJSA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22086}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.3", "_npmOperationalInternal": {"tmp": "tmp/ws_8.5.5_1686254624382_0.18449567350989815", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "6eea0ac9982d9cb29e842a5be3f7d3e0103c444a7b96a324c0ce4a86980da93f"}, "8.5.6": {"name": "@types/ws", "version": "8.5.6", "license": "MIT", "_id": "@types/ws@8.5.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}, {"url": "https://github.com/teidesu", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/wojtkowiak", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/k-yle", "name": "<PERSON>", "githubUsername": "k-yle"}, {"url": "https://github.com/cwadrupldijjit", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/ws", "dist": {"shasum": "e9ad51f0ab79b9110c50916c9fcbddc36d373065", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-8.5.6.tgz", "fileCount": 6, "integrity": "sha512-8B5EO9jLVCy+B58PLHvLDuOD8DRVMgQzq8d55SjLCOn9kqGyqOvy27exVaTio1q1nX5zLu8/6N0n2ThSxOM6tg==", "signatures": [{"sig": "MEYCIQD5hQg1mhTaXHRxqIIk47ycytZ1YfwNSKO27L4UpVaX/AIhAORyHWHkXf50Tc3ogIQ6Aj84eWb9g6ovSQMvwUPKry7P", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22283}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/ws_8.5.6_1695660836876_0.7919789835573667", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "c15a88686bd570f066b625badf535436d4e6d630f522caec313fba6c68e66255"}, "8.5.7": {"name": "@types/ws", "version": "8.5.7", "license": "MIT", "_id": "@types/ws@8.5.7", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}, {"url": "https://github.com/teidesu", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/wojtkowiak", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/k-yle", "name": "<PERSON>", "githubUsername": "k-yle"}, {"url": "https://github.com/cwadrupldijjit", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/ws", "dist": {"shasum": "1ca585074fe5d2c81dec7a3d451f244a2a6d83cb", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-8.5.7.tgz", "fileCount": 6, "integrity": "sha512-6UrLjiDUvn40CMrAubXuIVtj2PEfKDffJS7ychvnPU44j+KVeXmdHHTgqcM/dxLUTHxlXHiFM8Skmb8ozGdTnQ==", "signatures": [{"sig": "MEQCIEsb/kGJtL1dxA6CVlNgFeNROKe0qoW4t54VLEUHI3HkAiBoLlwqRIzZf4QLAat2UfsffvT8yA4HR4gP0hY3QuIf/w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22283}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/ws_8.5.7_1696966919077_0.5250280270681051", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "2891341a9460712ef58c4a537acce14360e743ed7e16540f6779baf27a1af0c0"}, "8.5.8": {"name": "@types/ws", "version": "8.5.8", "license": "MIT", "_id": "@types/ws@8.5.8", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}, {"url": "https://github.com/teidesu", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/wojtkowiak", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/k-yle", "name": "<PERSON>", "githubUsername": "k-yle"}, {"url": "https://github.com/cwadrupldijjit", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/ws", "dist": {"shasum": "13efec7bd439d0bdf2af93030804a94f163b1430", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-8.5.8.tgz", "fileCount": 6, "integrity": "sha512-flUksGIQCnJd6sZ1l5dqCEG/ksaoAg/eUwiLAGTJQcfgvZJKF++Ta4bJA6A5aPSJmsr+xlseHn4KLgVlNnvPTg==", "signatures": [{"sig": "MEQCID1XfjElMeAFf906nLBADJyCsADhl3XZpE3BUPlQVhTZAiBhp7UlIAUPQh1zljgeb7EWArIl7lRfj36xhbj+dKvQXw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21624}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/ws_8.5.8_1697656318443_0.9412942770190866", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "7317485dddefdfd6f7ae3f14524f0d39ecf95aac6fa26f9c5a8ed0eade07e93c"}, "8.5.9": {"name": "@types/ws", "version": "8.5.9", "license": "MIT", "_id": "@types/ws@8.5.9", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}, {"url": "https://github.com/teidesu", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/wojtkowiak", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/k-yle", "name": "<PERSON>", "githubUsername": "k-yle"}, {"url": "https://github.com/cwadrupldijjit", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/ws", "dist": {"shasum": "384c489f99c83225a53f01ebc3eddf3b8e202a8c", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-8.5.9.tgz", "fileCount": 6, "integrity": "sha512-jbdrY0a8lxfdTp/+r7Z4CkycbOFN8WX+IOchLJr3juT/xzbJ8URyTVSJ/hvNdadTgM1mnedb47n+Y31GsFnQlg==", "signatures": [{"sig": "MEQCIGBFAt+nw2nF2a8lEePBaEaY3NmVFxOqlFy7IlFjIEBWAiAnYte8jUxRHGE0v1z+oR6LQGysvykCeM/XIyWjbAxF4A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21624}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/ws_8.5.9_1699385227537_0.8816487482228408", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "8a75ac241cffea6a14b11065cb42b26293acd2aacc62c1f1aedb3f900ba9cb95"}, "8.5.10": {"name": "@types/ws", "version": "8.5.10", "license": "MIT", "_id": "@types/ws@8.5.10", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}, {"url": "https://github.com/teidesu", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/wojtkowiak", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/k-yle", "name": "<PERSON>", "githubUsername": "k-yle"}, {"url": "https://github.com/cwadrupldijjit", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/ws", "dist": {"shasum": "4acfb517970853fa6574a3a6886791d04a396787", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-8.5.10.tgz", "fileCount": 6, "integrity": "sha512-vmQSUcfalpIq0R9q7uTo2lXs6eGIpt9wtnLdMv9LVpIjCA/+ufZRozlVoVelIYixx1ugCBKDhn89vnsEGOCx9A==", "signatures": [{"sig": "MEUCIQCz6rFhwdKd/p1QJE0vlFGCqU3tEBpwUbOcvnYxJ/ftTQIgSUaX6qHiLzSvCDJwCEMj7fdi2PA2bzG5smx3iBiRtDU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21670}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/ws_8.5.10_1700529838882_0.9987802622649458", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "eccd9f863e6a5af0b53c429a6feeed5375c23d20c06a73d226f4b59f34407820"}, "8.5.11": {"name": "@types/ws", "version": "8.5.11", "license": "MIT", "_id": "@types/ws@8.5.11", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}, {"url": "https://github.com/teidesu", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/wojtkowiak", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/k-yle", "name": "<PERSON>", "githubUsername": "k-yle"}, {"url": "https://github.com/cwadrupldijjit", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/ws", "dist": {"shasum": "90ad17b3df7719ce3e6bc32f83ff954d38656508", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-8.5.11.tgz", "fileCount": 6, "integrity": "sha512-4+q7P5h3SpJxaBft0Dzpbr6lmMaqh0Jr2tbhJZ/luAwvD7ohSCniYkwz/pLxuT2h0EOa6QADgJj1Ko+TzRfZ+w==", "signatures": [{"sig": "MEUCIDC3OFYB9shmlLnH4bCAUtgBzzQqa00b95OBFloyeVBbAiEAxOQImN3v2iyNvNjLQEdagT7IMp/xblKKZHtXnMh7TBg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21193}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.8", "_npmOperationalInternal": {"tmp": "tmp/ws_8.5.11_1720838524682_0.6051729353740636", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "f8abfbf3acbcdca0f5c6b03740fc61b384c4507cc71eb700893ed0bb4f7ae35b"}, "8.5.12": {"name": "@types/ws", "version": "8.5.12", "license": "MIT", "_id": "@types/ws@8.5.12", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}, {"url": "https://github.com/teidesu", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/wojtkowiak", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/k-yle", "name": "<PERSON>", "githubUsername": "k-yle"}, {"url": "https://github.com/cwadrupldijjit", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/ws", "dist": {"shasum": "619475fe98f35ccca2a2f6c137702d85ec247b7e", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-8.5.12.tgz", "fileCount": 6, "integrity": "sha512-3tPRkv1EtkDpzlgyKyI8pGsGZAGPEaXeu0DOj5DI25Ja91bdAYddYHbADRYVrZMRbfW+1l5YwXVDKohDJNQxkQ==", "signatures": [{"sig": "MEUCIQCvYmZvnNtEJD+IohOOP1EgT59Qzs6WrZb6dGcHZFoC2AIgekVki+oLKHG9Naf8Vi6xIgYbiwzDec8IBPJ/4a2AAhs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21200}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.8", "_npmOperationalInternal": {"tmp": "tmp/ws_8.5.12_1722242284259_0.09754657648071441", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "32ddd11d89328bee2bba51767ec4f352327efde9e14bd1107492d0c8676d2cf6"}, "8.5.13": {"name": "@types/ws", "version": "8.5.13", "license": "MIT", "_id": "@types/ws@8.5.13", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}, {"url": "https://github.com/teidesu", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/wojtkowiak", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/k-yle", "name": "<PERSON>", "githubUsername": "k-yle"}, {"url": "https://github.com/cwadrupldijjit", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/ws", "dist": {"shasum": "6414c280875e2691d0d1e080b05addbf5cb91e20", "tarball": "https://registry.npmjs.org/@types/ws/-/ws-8.5.13.tgz", "fileCount": 6, "integrity": "sha512-osM/gWBTPKgHV8XkTunnegTRIsvF6owmf5w+JtAfOw472dptdm0dlGv4xCt6GwQRcC2XVOvvRE/0bAoQcL2QkA==", "signatures": [{"sig": "MEQCIBVsDghe3MrWyBYd3AElO7EJbFPMH+7K8ut1+l5qtkYdAiAFoyJYEZl7MlSjrx7XERaFurPKSwfdKtx2ipnB+3SCNw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21644}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "4.8", "_npmOperationalInternal": {"tmp": "tmp/ws_8.5.13_1730538157541_0.03938172534656781", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ad7aaa6f68e4c033bdd7b82a5e3fe2183fa0268f34658739aed4d7b22d547699"}}, "time": {"created": "2016-05-17T19:17:25.362Z", "modified": "2024-11-11T10:03:48.752Z", "0.0.14-alpha": "2016-05-17T19:17:25.362Z", "0.0.15-alpha": "2016-05-19T22:45:32.195Z", "0.0.20-alpha": "2016-05-20T20:56:52.134Z", "0.0.21-alpha": "2016-05-25T06:11:39.930Z", "0.0.22-alpha": "2016-07-01T20:55:27.190Z", "0.0.23-alpha": "2016-07-02T00:17:23.493Z", "0.0.24-alpha": "2016-07-02T03:40:34.343Z", "0.0.25-alpha": "2016-07-04T01:33:31.264Z", "0.0.26-alpha": "2016-07-08T21:47:17.411Z", "0.0.27": "2016-07-14T16:25:11.676Z", "0.0.28": "2016-08-02T16:15:43.517Z", "0.0.29": "2016-08-19T15:46:40.498Z", "0.0.30": "2016-08-25T19:04:43.486Z", "0.0.31": "2016-09-19T18:20:18.045Z", "0.0.32": "2016-09-21T20:29:06.205Z", "0.0.33": "2016-09-23T18:00:09.257Z", "0.0.34": "2016-10-05T21:04:39.256Z", "0.0.35": "2016-10-26T19:29:27.939Z", "0.0.36": "2016-11-14T19:37:46.640Z", "0.0.37": "2016-11-22T21:01:38.987Z", "0.0.38": "2017-01-23T02:33:05.620Z", "0.0.39": "2017-03-08T20:15:52.095Z", "0.0.40": "2017-04-17T17:58:41.344Z", "0.0.41": "2017-05-04T17:06:05.612Z", "0.0.42": "2017-06-02T13:56:40.315Z", "3.0.0": "2017-06-02T15:40:34.259Z", "3.0.1": "2017-07-06T14:05:35.971Z", "3.0.2": "2017-07-10T19:47:33.658Z", "3.2.0": "2017-10-03T18:28:35.906Z", "3.2.1": "2017-11-30T21:46:35.675Z", "4.0.0": "2018-01-12T18:52:52.684Z", "4.0.1": "2018-02-13T02:54:40.061Z", "4.0.2": "2018-03-28T17:47:03.214Z", "5.1.0": "2018-05-03T15:36:13.621Z", "5.1.1": "2018-05-08T22:17:36.868Z", "5.1.2": "2018-06-07T20:57:12.533Z", "6.0.0": "2018-08-14T00:55:52.154Z", "6.0.1": "2018-09-06T00:16:05.262Z", "6.0.2": "2019-08-05T17:59:15.343Z", "6.0.3": "2019-08-23T17:20:26.094Z", "6.0.4": "2019-11-25T22:27:57.347Z", "7.2.0": "2020-01-15T09:05:57.741Z", "7.2.1": "2020-01-29T22:06:54.842Z", "7.2.2": "2020-02-25T19:15:37.209Z", "7.2.3": "2020-03-18T20:26:57.613Z", "7.2.4": "2020-04-13T18:51:57.224Z", "7.2.5": "2020-06-04T01:09:25.880Z", "7.2.6": "2020-06-29T16:57:09.901Z", "7.2.7": "2020-09-28T22:52:03.323Z", "7.2.8": "2020-10-26T21:49:07.214Z", "7.2.9": "2020-10-28T17:04:53.553Z", "7.4.0": "2020-11-10T00:00:19.794Z", "7.4.1": "2021-04-02T00:31:35.517Z", "7.4.2": "2021-04-27T06:02:24.162Z", "7.4.3": "2021-05-11T00:02:47.731Z", "7.4.4": "2021-05-11T01:02:10.828Z", "7.4.5": "2021-06-17T09:31:35.801Z", "7.4.6": "2021-07-02T19:08:33.437Z", "7.4.7": "2021-07-19T23:03:25.901Z", "8.2.0": "2021-09-25T12:01:49.682Z", "8.2.1": "2021-11-29T18:31:15.459Z", "8.2.2": "2021-12-07T05:01:30.754Z", "8.2.3": "2022-02-19T03:01:42.647Z", "8.5.0": "2022-02-23T20:01:51.201Z", "8.5.1": "2022-02-24T22:01:52.116Z", "8.5.2": "2022-03-01T21:03:26.363Z", "8.5.3": "2022-03-11T23:31:55.667Z", "8.5.4": "2022-12-30T00:32:43.160Z", "8.5.5": "2023-06-08T20:03:44.573Z", "8.5.6": "2023-09-25T16:53:57.062Z", "8.5.7": "2023-10-10T19:41:59.250Z", "8.5.8": "2023-10-18T19:11:58.650Z", "8.5.9": "2023-11-07T19:27:07.725Z", "8.5.10": "2023-11-21T01:23:59.251Z", "8.5.11": "2024-07-13T02:42:04.825Z", "8.5.12": "2024-07-29T08:38:04.391Z", "8.5.13": "2024-11-02T09:02:37.820Z"}, "license": "MIT", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/ws", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/ws"}, "description": "TypeScript definitions for ws", "contributors": [{"url": "https://github.com/loyd", "name": "<PERSON>", "githubUsername": "loyd"}, {"url": "https://github.com/mlamp", "name": "<PERSON><PERSON>", "githubUsername": "mlamp"}, {"url": "https://github.com/TitaneBoy", "name": "<PERSON>", "githubUsername": "TitaneBoy"}, {"url": "https://github.com/reduckted", "name": "reduckted", "githubUsername": "reduckted"}, {"url": "https://github.com/teidesu", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/wojtkowiak", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/k-yle", "name": "<PERSON>", "githubUsername": "k-yle"}, {"url": "https://github.com/cwadrupldijjit", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "maintainers": [{"name": "types", "email": "<EMAIL>"}], "readme": "[object Object]", "readmeFilename": "", "users": {"hongbo-miao": true}}