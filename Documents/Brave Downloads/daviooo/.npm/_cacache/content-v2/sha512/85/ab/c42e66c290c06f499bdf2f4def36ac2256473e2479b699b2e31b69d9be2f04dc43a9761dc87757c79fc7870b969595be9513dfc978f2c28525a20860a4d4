{"_id": "delegates", "_rev": "45-4bc7f61a9fb1e6e18d290044b2c002a1", "name": "delegates", "description": "delegate methods and accessors to another property", "dist-tags": {"latest": "1.0.0"}, "versions": {"0.0.1": {"name": "delegates", "version": "0.0.1", "repository": {"type": "git", "url": "git://github.com/visionmedia/node-delegates"}, "description": "delegate methods and accessors to another property", "keywords": ["delegate", "delegation"], "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}, "license": "MIT", "bugs": {"url": "https://github.com/visionmedia/node-delegates/issues"}, "homepage": "https://github.com/visionmedia/node-delegates", "_id": "delegates@0.0.1", "dist": {"shasum": "****************************************", "tarball": "https://registry.npmjs.org/delegates/-/delegates-0.0.1.tgz", "integrity": "sha512-bqF7Us/lLPTOifV4ALEqlNJkEbuetXuU5wR7f6a/xM3cHBtAS/Owbh2HpQJxK4VGr85L36w/ZJ73b0xrizKuWQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC67Xk41Ry6LXDapbFvoN/6fK9RoyVuOax19d1/49YW3AiA+SDXcXUGQ8NJCumtKcUj5R6VGqKcXoaWz/k92seX7Aw=="}]}, "_from": ".", "_npmVersion": "1.3.15", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.0.2": {"name": "delegates", "version": "0.0.2", "repository": {"type": "git", "url": "git://github.com/visionmedia/node-delegates"}, "description": "delegate methods and accessors to another property", "keywords": ["delegate", "delegation"], "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}, "license": "MIT", "bugs": {"url": "https://github.com/visionmedia/node-delegates/issues"}, "homepage": "https://github.com/visionmedia/node-delegates", "_id": "delegates@0.0.2", "dist": {"shasum": "41c8b770dcfff0a65c32192cac022dc037076d77", "tarball": "https://registry.npmjs.org/delegates/-/delegates-0.0.2.tgz", "integrity": "sha512-T2p1KIVjCH+ZUwlygGJr5/CL6p76DvotXlAUPbT8BLaPwHV1n3biQUljHWnNsi5N/BwgxSfEZ0hsF0U/QEFgvg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEhs65PfKEu0W4ETi+rgFQwQtFWF1pHZtkCYTZHNIAcdAiAv0pYOVrlJQSxl7pCXuP3j0hhyjYqqChVNNC511HEEAw=="}]}, "_from": ".", "_npmVersion": "1.3.15", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.0.3": {"name": "delegates", "version": "0.0.3", "repository": {"type": "git", "url": "git://github.com/visionmedia/node-delegates"}, "description": "delegate methods and accessors to another property", "keywords": ["delegate", "delegation"], "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}, "license": "MIT", "bugs": {"url": "https://github.com/visionmedia/node-delegates/issues"}, "homepage": "https://github.com/visionmedia/node-delegates", "_id": "delegates@0.0.3", "dist": {"shasum": "4f25cbf8e1c061967f834e003f3bd18ded4baeea", "tarball": "https://registry.npmjs.org/delegates/-/delegates-0.0.3.tgz", "integrity": "sha512-9xrtyihBf7ybA34THs6W4Gwu1xNoVGUNzsegM2Q0XoalOqitE6qw7eGT0tadysvMK1u3xAswHmPcwUCtjsEKKg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCAviYWmBe0yjbXJE43nb4Zd5eY1FrxkPc4zFngIgoksAIgDh/OxnwCpNOG2CAOUh6Jv3q7RUFwRFZlqI4XnXftdlI="}]}, "_from": ".", "_npmVersion": "1.3.15", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.1.0": {"name": "delegates", "version": "0.1.0", "repository": {"type": "git", "url": "git://github.com/visionmedia/node-delegates"}, "description": "delegate methods and accessors to another property", "keywords": ["delegate", "delegation"], "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}, "license": "MIT", "bugs": {"url": "https://github.com/visionmedia/node-delegates/issues"}, "homepage": "https://github.com/visionmedia/node-delegates", "_id": "delegates@0.1.0", "_shasum": "b4b57be11a1653517a04b27f0949bdc327dfe390", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "b4b57be11a1653517a04b27f0949bdc327dfe390", "tarball": "https://registry.npmjs.org/delegates/-/delegates-0.1.0.tgz", "integrity": "sha512-tPYr58xmVlUWcL8zPk6ZAxP6XqiYx5IIn395dkeER12JmMy8P6ipGKnUvgD++g8+uCaALfs/CRERixvKBu1pow==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG+rcE/wB+PupmR77XtYwh344bN3aZCAyH8rGfbJysfMAiEAtqY2jx4JsGAJ8BvZdMWqoAQycAoZX3Jti48Tts/fiLY="}]}, "directories": {}}, "1.0.0": {"name": "delegates", "version": "1.0.0", "repository": {"type": "git", "url": "git+https://github.com/visionmedia/node-delegates.git"}, "description": "delegate methods and accessors to another property", "keywords": ["delegate", "delegation"], "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}, "license": "MIT", "gitHead": "c4dc07ef1ed51c2b2a63f3585e5ef949ee577a49", "bugs": {"url": "https://github.com/visionmedia/node-delegates/issues"}, "homepage": "https://github.com/visionmedia/node-delegates#readme", "_id": "delegates@1.0.0", "scripts": {}, "_shasum": "84c6e159b81904fdca59a0ef44cd870d31250f9a", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "4.2.1", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "84c6e159b81904fdca59a0ef44cd870d31250f9a", "tarball": "https://registry.npmjs.org/delegates/-/delegates-1.0.0.tgz", "integrity": "sha512-bd2L678uiWATM6m5Z1VzNCErI3jiGzt6HGY8OVICs40JQq/HALfbyNJmp0UDakEY4pMMaN0Ly5om/B1VI/+xfQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICgg6SAK0q7HFgtOvtLTSqXZq1uy/rZFuXwVkFuVZqG3AiBh7M+ZEnHzGg3ou+HnGnQJHVlpLYRR3Mnoqu8fuQUnYg=="}]}, "directories": {}}}, "readme": "\n# delegates\n\n  Node method and accessor delegation utilty.\n\n## Installation\n\n```\n$ npm install delegates\n```\n\n## Example\n\n```js\nvar delegate = require('delegates');\n\n...\n\ndelegate(proto, 'request')\n  .method('acceptsLanguages')\n  .method('acceptsEncodings')\n  .method('acceptsCharsets')\n  .method('accepts')\n  .method('is')\n  .access('querystring')\n  .access('idempotent')\n  .access('socket')\n  .access('length')\n  .access('query')\n  .access('search')\n  .access('status')\n  .access('method')\n  .access('path')\n  .access('body')\n  .access('host')\n  .access('url')\n  .getter('subdomains')\n  .getter('protocol')\n  .getter('header')\n  .getter('stale')\n  .getter('fresh')\n  .getter('secure')\n  .getter('ips')\n  .getter('ip')\n```\n\n# API\n\n## Delegate(proto, prop)\n\nCreates a delegator instance used to configure using the `prop` on the given\n`proto` object. (which is usually a prototype)\n\n## Delegate#method(name)\n\nAllows the given method `name` to be accessed on the host.\n\n## Delegate#getter(name)\n\nCreates a \"getter\" for the property with the given `name` on the delegated\nobject.\n\n## Delegate#setter(name)\n\nCreates a \"setter\" for the property with the given `name` on the delegated\nobject.\n\n## Delegate#access(name)\n\nCreates an \"accessor\" (ie: both getter *and* setter) for the property with the\ngiven `name` on the delegated object.\n\n## Delegate#fluent(name)\n\nA unique type of \"accessor\" that works for a \"fluent\" API. When called as a\ngetter, the method returns the expected value. However, if the method is called\nwith a value, it will return itself so it can be chained. For example:\n\n```js\ndelegate(proto, 'request')\n  .fluent('query')\n\n// getter\nvar q = request.query();\n\n// setter (chainable)\nrequest\n  .query({ a: 1 })\n  .query({ b: 2 });\n```\n\n# License\n\n  MIT\n", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2022-06-14T23:51:05.101Z", "created": "2014-01-13T14:22:03.294Z", "0.0.1": "2014-01-13T14:22:04.168Z", "0.0.2": "2014-01-13T14:31:47.744Z", "0.0.3": "2014-01-13T14:34:41.995Z", "0.1.0": "2014-10-17T22:35:53.914Z", "1.0.0": "2015-12-14T19:56:09.247Z"}, "repository": {"type": "git", "url": "git+https://github.com/visionmedia/node-delegates.git"}, "users": {"jksdua": true, "goodseller": true, "highflying": true, "xieranmaya": true, "surfacew": true, "attomos": true, "progmer": true, "corenova": true, "yokubee": true, "kodekracker": true, "leonzhao": true, "suddi": true, "wangnan0610": true, "programmer.severson": true, "danielpavelic": true, "laomu": true, "xtx1130": true, "shuoshubao": true, "tiancheng9": true, "sballan": true, "x-cold": true, "tedyhy": true, "coolhector": true, "bhaskarmelkani": true, "pasturn": true, "jream": true}, "homepage": "https://github.com/visionmedia/node-delegates#readme", "keywords": ["delegate", "delegation"], "bugs": {"url": "https://github.com/visionmedia/node-delegates/issues"}, "license": "MIT", "readmeFilename": "Readme.md"}